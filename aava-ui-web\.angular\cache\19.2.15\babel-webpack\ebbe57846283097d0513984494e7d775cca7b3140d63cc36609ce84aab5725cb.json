{"ast": null, "code": "/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i3 from '@angular/common';\nimport { LOCATION_INITIALIZED, HashLocationStrategy, LocationStrategy, ViewportScroller, Location, PathLocationStrategy } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { ɵRuntimeError as _RuntimeError, ɵɵsanitizeUrlOrResourceUrl as __sanitizeUrlOrResourceUrl, booleanAttribute, HostListener, Input, HostBinding, Attribute, Directive, EventEmitter, Output, ContentChildren, Optional, createEnvironmentInjector, Injectable, InjectionToken, ɵperformanceMarkFeature as _performanceMarkFeature, makeEnvironmentProviders, APP_BOOTSTRAP_LISTENER, ENVIRONMENT_INITIALIZER, provideAppInitializer, inject, Injector, ApplicationRef, InjectFlags, NgZone, SkipSelf, NgModule } from '@angular/core';\nimport { NavigationEnd, isUrlTree, Router, ActivatedRoute, RouterConfigLoader, NavigationStart, NavigationSkipped, NavigationSkippedCode, Scroll, UrlSerializer, NavigationTransitions, ROUTES, afterNextNavigation, ROUTER_CONFIGURATION, NAVIGATION_ERROR_HANDLER, RoutedComponentInputBinder, INPUT_BINDER, createViewTransition, CREATE_VIEW_TRANSITION, VIEW_TRANSITION_OPTIONS, stringifyEvent, DefaultUrlSerializer, ChildrenOutletContexts, RouterOutlet, ɵEmptyOutletComponent as _EmptyOutletComponent } from './router-Dwfin5Au.mjs';\nimport { Subject, of, from } from 'rxjs';\nimport { mergeAll, catchError, filter, concatMap, mergeMap } from 'rxjs/operators';\n\n/**\n * @description\n *\n * When applied to an element in a template, makes that element a link\n * that initiates navigation to a route. Navigation opens one or more routed components\n * in one or more `<router-outlet>` locations on the page.\n *\n * Given a route configuration `[{ path: 'user/:name', component: UserCmp }]`,\n * the following creates a static link to the route:\n * `<a routerLink=\"/user/bob\">link to user component</a>`\n *\n * You can use dynamic values to generate the link.\n * For a dynamic link, pass an array of path segments,\n * followed by the params for each segment.\n * For example, `['/team', teamId, 'user', userName, {details: true}]`\n * generates a link to `/team/11/user/bob;details=true`.\n *\n * Multiple static segments can be merged into one term and combined with dynamic segments.\n * For example, `['/team/11/user', userName, {details: true}]`\n *\n * The input that you provide to the link is treated as a delta to the current URL.\n * For instance, suppose the current URL is `/user/(box//aux:team)`.\n * The link `<a [routerLink]=\"['/user/jim']\">Jim</a>` creates the URL\n * `/user/(jim//aux:team)`.\n * See {@link Router#createUrlTree} for more information.\n *\n * @usageNotes\n *\n * You can use absolute or relative paths in a link, set query parameters,\n * control how parameters are handled, and keep a history of navigation states.\n *\n * ### Relative link paths\n *\n * The first segment name can be prepended with `/`, `./`, or `../`.\n * * If the first segment begins with `/`, the router looks up the route from the root of the\n *   app.\n * * If the first segment begins with `./`, or doesn't begin with a slash, the router\n *   looks in the children of the current activated route.\n * * If the first segment begins with `../`, the router goes up one level in the route tree.\n *\n * ### Setting and handling query params and fragments\n *\n * The following link adds a query parameter and a fragment to the generated URL:\n *\n * ```html\n * <a [routerLink]=\"['/user/bob']\" [queryParams]=\"{debug: true}\" fragment=\"education\">\n *   link to user component\n * </a>\n * ```\n * By default, the directive constructs the new URL using the given query parameters.\n * The example generates the link: `/user/bob?debug=true#education`.\n *\n * You can instruct the directive to handle query parameters differently\n * by specifying the `queryParamsHandling` option in the link.\n * Allowed values are:\n *\n *  - `'merge'`: Merge the given `queryParams` into the current query params.\n *  - `'preserve'`: Preserve the current query params.\n *\n * For example:\n *\n * ```html\n * <a [routerLink]=\"['/user/bob']\" [queryParams]=\"{debug: true}\" queryParamsHandling=\"merge\">\n *   link to user component\n * </a>\n * ```\n *\n * `queryParams`, `fragment`, `queryParamsHandling`, `preserveFragment`, and `relativeTo`\n * cannot be used when the `routerLink` input is a `UrlTree`.\n *\n * See {@link UrlCreationOptions#queryParamsHandling}.\n *\n * ### Preserving navigation history\n *\n * You can provide a `state` value to be persisted to the browser's\n * [`History.state` property](https://developer.mozilla.org/en-US/docs/Web/API/History#Properties).\n * For example:\n *\n * ```html\n * <a [routerLink]=\"['/user/bob']\" [state]=\"{tracingId: 123}\">\n *   link to user component\n * </a>\n * ```\n *\n * Use {@link Router#getCurrentNavigation} to retrieve a saved\n * navigation-state value. For example, to capture the `tracingId` during the `NavigationStart`\n * event:\n *\n * ```ts\n * // Get NavigationStart events\n * router.events.pipe(filter(e => e instanceof NavigationStart)).subscribe(e => {\n *   const navigation = router.getCurrentNavigation();\n *   tracingService.trace({id: navigation.extras.state.tracingId});\n * });\n * ```\n *\n * @ngModule RouterModule\n *\n * @publicApi\n */\nlet RouterLink = /*#__PURE__*/(() => {\n  class RouterLink {\n    router;\n    route;\n    tabIndexAttribute;\n    renderer;\n    el;\n    locationStrategy;\n    /**\n     * Represents an `href` attribute value applied to a host element,\n     * when a host element is `<a>`. For other tags, the value is `null`.\n     */\n    href = null;\n    /**\n     * Represents the `target` attribute on a host element.\n     * This is only used when the host element is an `<a>` tag.\n     */\n    target;\n    /**\n     * Passed to {@link Router#createUrlTree} as part of the\n     * `UrlCreationOptions`.\n     * @see {@link UrlCreationOptions#queryParams}\n     * @see {@link Router#createUrlTree}\n     */\n    queryParams;\n    /**\n     * Passed to {@link Router#createUrlTree} as part of the\n     * `UrlCreationOptions`.\n     * @see {@link UrlCreationOptions#fragment}\n     * @see {@link Router#createUrlTree}\n     */\n    fragment;\n    /**\n     * Passed to {@link Router#createUrlTree} as part of the\n     * `UrlCreationOptions`.\n     * @see {@link UrlCreationOptions#queryParamsHandling}\n     * @see {@link Router#createUrlTree}\n     */\n    queryParamsHandling;\n    /**\n     * Passed to {@link Router#navigateByUrl} as part of the\n     * `NavigationBehaviorOptions`.\n     * @see {@link NavigationBehaviorOptions#state}\n     * @see {@link Router#navigateByUrl}\n     */\n    state;\n    /**\n     * Passed to {@link Router#navigateByUrl} as part of the\n     * `NavigationBehaviorOptions`.\n     * @see {@link NavigationBehaviorOptions#info}\n     * @see {@link Router#navigateByUrl}\n     */\n    info;\n    /**\n     * Passed to {@link Router#createUrlTree} as part of the\n     * `UrlCreationOptions`.\n     * Specify a value here when you do not want to use the default value\n     * for `routerLink`, which is the current activated route.\n     * Note that a value of `undefined` here will use the `routerLink` default.\n     * @see {@link UrlCreationOptions#relativeTo}\n     * @see {@link Router#createUrlTree}\n     */\n    relativeTo;\n    /** Whether a host element is an `<a>` tag. */\n    isAnchorElement;\n    subscription;\n    /** @internal */\n    onChanges = new Subject();\n    constructor(router, route, tabIndexAttribute, renderer, el, locationStrategy) {\n      this.router = router;\n      this.route = route;\n      this.tabIndexAttribute = tabIndexAttribute;\n      this.renderer = renderer;\n      this.el = el;\n      this.locationStrategy = locationStrategy;\n      const tagName = el.nativeElement.tagName?.toLowerCase();\n      this.isAnchorElement = tagName === 'a' || tagName === 'area';\n      if (this.isAnchorElement) {\n        this.subscription = router.events.subscribe(s => {\n          if (s instanceof NavigationEnd) {\n            this.updateHref();\n          }\n        });\n      } else {\n        this.setTabIndexIfNotOnNativeEl('0');\n      }\n    }\n    /**\n     * Passed to {@link Router#createUrlTree} as part of the\n     * `UrlCreationOptions`.\n     * @see {@link UrlCreationOptions#preserveFragment}\n     * @see {@link Router#createUrlTree}\n     */\n    preserveFragment = false;\n    /**\n     * Passed to {@link Router#navigateByUrl} as part of the\n     * `NavigationBehaviorOptions`.\n     * @see {@link NavigationBehaviorOptions#skipLocationChange}\n     * @see {@link Router#navigateByUrl}\n     */\n    skipLocationChange = false;\n    /**\n     * Passed to {@link Router#navigateByUrl} as part of the\n     * `NavigationBehaviorOptions`.\n     * @see {@link NavigationBehaviorOptions#replaceUrl}\n     * @see {@link Router#navigateByUrl}\n     */\n    replaceUrl = false;\n    /**\n     * Modifies the tab index if there was not a tabindex attribute on the element during\n     * instantiation.\n     */\n    setTabIndexIfNotOnNativeEl(newTabIndex) {\n      if (this.tabIndexAttribute != null /* both `null` and `undefined` */ || this.isAnchorElement) {\n        return;\n      }\n      this.applyAttributeValue('tabindex', newTabIndex);\n    }\n    /** @docs-private */\n    // TODO(atscott): Remove changes parameter in major version as a breaking change.\n    ngOnChanges(changes) {\n      if (ngDevMode && isUrlTree(this.routerLinkInput) && (this.fragment !== undefined || this.queryParams || this.queryParamsHandling || this.preserveFragment || this.relativeTo)) {\n        throw new _RuntimeError(4016 /* RuntimeErrorCode.INVALID_ROUTER_LINK_INPUTS */, 'Cannot configure queryParams or fragment when using a UrlTree as the routerLink input value.');\n      }\n      if (this.isAnchorElement) {\n        this.updateHref();\n      }\n      // This is subscribed to by `RouterLinkActive` so that it knows to update when there are changes\n      // to the RouterLinks it's tracking.\n      this.onChanges.next(this);\n    }\n    routerLinkInput = null;\n    /**\n     * Commands to pass to {@link Router#createUrlTree} or a `UrlTree`.\n     *   - **array**: commands to pass to {@link Router#createUrlTree}.\n     *   - **string**: shorthand for array of commands with just the string, i.e. `['/route']`\n     *   - **UrlTree**: a `UrlTree` for this link rather than creating one from the commands\n     *     and other inputs that correspond to properties of `UrlCreationOptions`.\n     *   - **null|undefined**: effectively disables the `routerLink`\n     * @see {@link Router#createUrlTree}\n     */\n    set routerLink(commandsOrUrlTree) {\n      if (commandsOrUrlTree == null) {\n        this.routerLinkInput = null;\n        this.setTabIndexIfNotOnNativeEl(null);\n      } else {\n        if (isUrlTree(commandsOrUrlTree)) {\n          this.routerLinkInput = commandsOrUrlTree;\n        } else {\n          this.routerLinkInput = Array.isArray(commandsOrUrlTree) ? commandsOrUrlTree : [commandsOrUrlTree];\n        }\n        this.setTabIndexIfNotOnNativeEl('0');\n      }\n    }\n    /** @docs-private */\n    onClick(button, ctrlKey, shiftKey, altKey, metaKey) {\n      const urlTree = this.urlTree;\n      if (urlTree === null) {\n        return true;\n      }\n      if (this.isAnchorElement) {\n        if (button !== 0 || ctrlKey || shiftKey || altKey || metaKey) {\n          return true;\n        }\n        if (typeof this.target === 'string' && this.target != '_self') {\n          return true;\n        }\n      }\n      const extras = {\n        skipLocationChange: this.skipLocationChange,\n        replaceUrl: this.replaceUrl,\n        state: this.state,\n        info: this.info\n      };\n      this.router.navigateByUrl(urlTree, extras);\n      // Return `false` for `<a>` elements to prevent default action\n      // and cancel the native behavior, since the navigation is handled\n      // by the Router.\n      return !this.isAnchorElement;\n    }\n    /** @docs-private */\n    ngOnDestroy() {\n      this.subscription?.unsubscribe();\n    }\n    updateHref() {\n      const urlTree = this.urlTree;\n      this.href = urlTree !== null && this.locationStrategy ? this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(urlTree)) : null;\n      const sanitizedValue = this.href === null ? null :\n      // This class represents a directive that can be added to both `<a>` elements,\n      // as well as other elements. As a result, we can't define security context at\n      // compile time. So the security context is deferred to runtime.\n      // The `ɵɵsanitizeUrlOrResourceUrl` selects the necessary sanitizer function\n      // based on the tag and property names. The logic mimics the one from\n      // `packages/compiler/src/schema/dom_security_schema.ts`, which is used at compile time.\n      //\n      // Note: we should investigate whether we can switch to using `@HostBinding('attr.href')`\n      // instead of applying a value via a renderer, after a final merge of the\n      // `RouterLinkWithHref` directive.\n      __sanitizeUrlOrResourceUrl(this.href, this.el.nativeElement.tagName.toLowerCase(), 'href');\n      this.applyAttributeValue('href', sanitizedValue);\n    }\n    applyAttributeValue(attrName, attrValue) {\n      const renderer = this.renderer;\n      const nativeElement = this.el.nativeElement;\n      if (attrValue !== null) {\n        renderer.setAttribute(nativeElement, attrName, attrValue);\n      } else {\n        renderer.removeAttribute(nativeElement, attrName);\n      }\n    }\n    get urlTree() {\n      if (this.routerLinkInput === null) {\n        return null;\n      } else if (isUrlTree(this.routerLinkInput)) {\n        return this.routerLinkInput;\n      }\n      return this.router.createUrlTree(this.routerLinkInput, {\n        // If the `relativeTo` input is not defined, we want to use `this.route` by default.\n        // Otherwise, we should use the value provided by the user in the input.\n        relativeTo: this.relativeTo !== undefined ? this.relativeTo : this.route,\n        queryParams: this.queryParams,\n        fragment: this.fragment,\n        queryParamsHandling: this.queryParamsHandling,\n        preserveFragment: this.preserveFragment\n      });\n    }\n    static ɵfac = function RouterLink_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RouterLink)(i0.ɵɵdirectiveInject(Router), i0.ɵɵdirectiveInject(ActivatedRoute), i0.ɵɵinjectAttribute('tabindex'), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i3.LocationStrategy));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: RouterLink,\n      selectors: [[\"\", \"routerLink\", \"\"]],\n      hostVars: 1,\n      hostBindings: function RouterLink_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function RouterLink_click_HostBindingHandler($event) {\n            return ctx.onClick($event.button, $event.ctrlKey, $event.shiftKey, $event.altKey, $event.metaKey);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"target\", ctx.target);\n        }\n      },\n      inputs: {\n        target: \"target\",\n        queryParams: \"queryParams\",\n        fragment: \"fragment\",\n        queryParamsHandling: \"queryParamsHandling\",\n        state: \"state\",\n        info: \"info\",\n        relativeTo: \"relativeTo\",\n        preserveFragment: [2, \"preserveFragment\", \"preserveFragment\", booleanAttribute],\n        skipLocationChange: [2, \"skipLocationChange\", \"skipLocationChange\", booleanAttribute],\n        replaceUrl: [2, \"replaceUrl\", \"replaceUrl\", booleanAttribute],\n        routerLink: \"routerLink\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n  return RouterLink;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n *\n * @description\n *\n * Tracks whether the linked route of an element is currently active, and allows you\n * to specify one or more CSS classes to add to the element when the linked route\n * is active.\n *\n * Use this directive to create a visual distinction for elements associated with an active route.\n * For example, the following code highlights the word \"Bob\" when the router\n * activates the associated route:\n *\n * ```html\n * <a routerLink=\"/user/bob\" routerLinkActive=\"active-link\">Bob</a>\n * ```\n *\n * Whenever the URL is either '/user' or '/user/bob', the \"active-link\" class is\n * added to the anchor tag. If the URL changes, the class is removed.\n *\n * You can set more than one class using a space-separated string or an array.\n * For example:\n *\n * ```html\n * <a routerLink=\"/user/bob\" routerLinkActive=\"class1 class2\">Bob</a>\n * <a routerLink=\"/user/bob\" [routerLinkActive]=\"['class1', 'class2']\">Bob</a>\n * ```\n *\n * To add the classes only when the URL matches the link exactly, add the option `exact: true`:\n *\n * ```html\n * <a routerLink=\"/user/bob\" routerLinkActive=\"active-link\" [routerLinkActiveOptions]=\"{exact:\n * true}\">Bob</a>\n * ```\n *\n * To directly check the `isActive` status of the link, assign the `RouterLinkActive`\n * instance to a template variable.\n * For example, the following checks the status without assigning any CSS classes:\n *\n * ```html\n * <a routerLink=\"/user/bob\" routerLinkActive #rla=\"routerLinkActive\">\n *   Bob {{ rla.isActive ? '(already open)' : ''}}\n * </a>\n * ```\n *\n * You can apply the `RouterLinkActive` directive to an ancestor of linked elements.\n * For example, the following sets the active-link class on the `<div>`  parent tag\n * when the URL is either '/user/jim' or '/user/bob'.\n *\n * ```html\n * <div routerLinkActive=\"active-link\" [routerLinkActiveOptions]=\"{exact: true}\">\n *   <a routerLink=\"/user/jim\">Jim</a>\n *   <a routerLink=\"/user/bob\">Bob</a>\n * </div>\n * ```\n *\n * The `RouterLinkActive` directive can also be used to set the aria-current attribute\n * to provide an alternative distinction for active elements to visually impaired users.\n *\n * For example, the following code adds the 'active' class to the Home Page link when it is\n * indeed active and in such case also sets its aria-current attribute to 'page':\n *\n * ```html\n * <a routerLink=\"/\" routerLinkActive=\"active\" ariaCurrentWhenActive=\"page\">Home Page</a>\n * ```\n *\n * @ngModule RouterModule\n *\n * @publicApi\n */\nlet RouterLinkActive = /*#__PURE__*/(() => {\n  class RouterLinkActive {\n    router;\n    element;\n    renderer;\n    cdr;\n    link;\n    links;\n    classes = [];\n    routerEventsSubscription;\n    linkInputChangesSubscription;\n    _isActive = false;\n    get isActive() {\n      return this._isActive;\n    }\n    /**\n     * Options to configure how to determine if the router link is active.\n     *\n     * These options are passed to the `Router.isActive()` function.\n     *\n     * @see {@link Router#isActive}\n     */\n    routerLinkActiveOptions = {\n      exact: false\n    };\n    /**\n     * Aria-current attribute to apply when the router link is active.\n     *\n     * Possible values: `'page'` | `'step'` | `'location'` | `'date'` | `'time'` | `true` | `false`.\n     *\n     * @see {@link https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Attributes/aria-current}\n     */\n    ariaCurrentWhenActive;\n    /**\n     *\n     * You can use the output `isActiveChange` to get notified each time the link becomes\n     * active or inactive.\n     *\n     * Emits:\n     * true  -> Route is active\n     * false -> Route is inactive\n     *\n     * ```html\n     * <a\n     *  routerLink=\"/user/bob\"\n     *  routerLinkActive=\"active-link\"\n     *  (isActiveChange)=\"this.onRouterLinkActive($event)\">Bob</a>\n     * ```\n     */\n    isActiveChange = new EventEmitter();\n    constructor(router, element, renderer, cdr, link) {\n      this.router = router;\n      this.element = element;\n      this.renderer = renderer;\n      this.cdr = cdr;\n      this.link = link;\n      this.routerEventsSubscription = router.events.subscribe(s => {\n        if (s instanceof NavigationEnd) {\n          this.update();\n        }\n      });\n    }\n    /** @docs-private */\n    ngAfterContentInit() {\n      // `of(null)` is used to force subscribe body to execute once immediately (like `startWith`).\n      of(this.links.changes, of(null)).pipe(mergeAll()).subscribe(_ => {\n        this.update();\n        this.subscribeToEachLinkOnChanges();\n      });\n    }\n    subscribeToEachLinkOnChanges() {\n      this.linkInputChangesSubscription?.unsubscribe();\n      const allLinkChanges = [...this.links.toArray(), this.link].filter(link => !!link).map(link => link.onChanges);\n      this.linkInputChangesSubscription = from(allLinkChanges).pipe(mergeAll()).subscribe(link => {\n        if (this._isActive !== this.isLinkActive(this.router)(link)) {\n          this.update();\n        }\n      });\n    }\n    set routerLinkActive(data) {\n      const classes = Array.isArray(data) ? data : data.split(' ');\n      this.classes = classes.filter(c => !!c);\n    }\n    /** @docs-private */\n    ngOnChanges(changes) {\n      this.update();\n    }\n    /** @docs-private */\n    ngOnDestroy() {\n      this.routerEventsSubscription.unsubscribe();\n      this.linkInputChangesSubscription?.unsubscribe();\n    }\n    update() {\n      if (!this.links || !this.router.navigated) return;\n      queueMicrotask(() => {\n        const hasActiveLinks = this.hasActiveLinks();\n        this.classes.forEach(c => {\n          if (hasActiveLinks) {\n            this.renderer.addClass(this.element.nativeElement, c);\n          } else {\n            this.renderer.removeClass(this.element.nativeElement, c);\n          }\n        });\n        if (hasActiveLinks && this.ariaCurrentWhenActive !== undefined) {\n          this.renderer.setAttribute(this.element.nativeElement, 'aria-current', this.ariaCurrentWhenActive.toString());\n        } else {\n          this.renderer.removeAttribute(this.element.nativeElement, 'aria-current');\n        }\n        // Only emit change if the active state changed.\n        if (this._isActive !== hasActiveLinks) {\n          this._isActive = hasActiveLinks;\n          this.cdr.markForCheck();\n          // Emit on isActiveChange after classes are updated\n          this.isActiveChange.emit(hasActiveLinks);\n        }\n      });\n    }\n    isLinkActive(router) {\n      const options = isActiveMatchOptions(this.routerLinkActiveOptions) ? this.routerLinkActiveOptions :\n      // While the types should disallow `undefined` here, it's possible without strict inputs\n      this.routerLinkActiveOptions.exact || false;\n      return link => {\n        const urlTree = link.urlTree;\n        return urlTree ? router.isActive(urlTree, options) : false;\n      };\n    }\n    hasActiveLinks() {\n      const isActiveCheckFn = this.isLinkActive(this.router);\n      return this.link && isActiveCheckFn(this.link) || this.links.some(isActiveCheckFn);\n    }\n    static ɵfac = function RouterLinkActive_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RouterLinkActive)(i0.ɵɵdirectiveInject(Router), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(RouterLink, 8));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: RouterLinkActive,\n      selectors: [[\"\", \"routerLinkActive\", \"\"]],\n      contentQueries: function RouterLinkActive_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, RouterLink, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.links = _t);\n        }\n      },\n      inputs: {\n        routerLinkActiveOptions: \"routerLinkActiveOptions\",\n        ariaCurrentWhenActive: \"ariaCurrentWhenActive\",\n        routerLinkActive: \"routerLinkActive\"\n      },\n      outputs: {\n        isActiveChange: \"isActiveChange\"\n      },\n      exportAs: [\"routerLinkActive\"],\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n  return RouterLinkActive;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Use instead of `'paths' in options` to be compatible with property renaming\n */\nfunction isActiveMatchOptions(options) {\n  return !!options.paths;\n}\n\n/**\n * @description\n *\n * Provides a preloading strategy.\n *\n * @publicApi\n */\nclass PreloadingStrategy {}\n/**\n * @description\n *\n * Provides a preloading strategy that preloads all modules as quickly as possible.\n *\n * ```ts\n * RouterModule.forRoot(ROUTES, {preloadingStrategy: PreloadAllModules})\n * ```\n *\n * @publicApi\n */\nlet PreloadAllModules = /*#__PURE__*/(() => {\n  class PreloadAllModules {\n    preload(route, fn) {\n      return fn().pipe(catchError(() => of(null)));\n    }\n    static ɵfac = function PreloadAllModules_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PreloadAllModules)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: PreloadAllModules,\n      factory: PreloadAllModules.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return PreloadAllModules;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @description\n *\n * Provides a preloading strategy that does not preload any modules.\n *\n * This strategy is enabled by default.\n *\n * @publicApi\n */\nlet NoPreloading = /*#__PURE__*/(() => {\n  class NoPreloading {\n    preload(route, fn) {\n      return of(null);\n    }\n    static ɵfac = function NoPreloading_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NoPreloading)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NoPreloading,\n      factory: NoPreloading.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return NoPreloading;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * The preloader optimistically loads all router configurations to\n * make navigations into lazily-loaded sections of the application faster.\n *\n * The preloader runs in the background. When the router bootstraps, the preloader\n * starts listening to all navigation events. After every such event, the preloader\n * will check if any configurations can be loaded lazily.\n *\n * If a route is protected by `canLoad` guards, the preloaded will not load it.\n *\n * @publicApi\n */\nlet RouterPreloader = /*#__PURE__*/(() => {\n  class RouterPreloader {\n    router;\n    injector;\n    preloadingStrategy;\n    loader;\n    subscription;\n    constructor(router, injector, preloadingStrategy, loader) {\n      this.router = router;\n      this.injector = injector;\n      this.preloadingStrategy = preloadingStrategy;\n      this.loader = loader;\n    }\n    setUpPreloading() {\n      this.subscription = this.router.events.pipe(filter(e => e instanceof NavigationEnd), concatMap(() => this.preload())).subscribe(() => {});\n    }\n    preload() {\n      return this.processRoutes(this.injector, this.router.config);\n    }\n    /** @docs-private */\n    ngOnDestroy() {\n      if (this.subscription) {\n        this.subscription.unsubscribe();\n      }\n    }\n    processRoutes(injector, routes) {\n      const res = [];\n      for (const route of routes) {\n        if (route.providers && !route._injector) {\n          route._injector = createEnvironmentInjector(route.providers, injector, `Route: ${route.path}`);\n        }\n        const injectorForCurrentRoute = route._injector ?? injector;\n        const injectorForChildren = route._loadedInjector ?? injectorForCurrentRoute;\n        // Note that `canLoad` is only checked as a condition that prevents `loadChildren` and not\n        // `loadComponent`. `canLoad` guards only block loading of child routes by design. This\n        // happens as a consequence of needing to descend into children for route matching immediately\n        // while component loading is deferred until route activation. Because `canLoad` guards can\n        // have side effects, we cannot execute them here so we instead skip preloading altogether\n        // when present. Lastly, it remains to be decided whether `canLoad` should behave this way\n        // at all. Code splitting and lazy loading is separate from client-side authorization checks\n        // and should not be used as a security measure to prevent loading of code.\n        if (route.loadChildren && !route._loadedRoutes && route.canLoad === undefined || route.loadComponent && !route._loadedComponent) {\n          res.push(this.preloadConfig(injectorForCurrentRoute, route));\n        }\n        if (route.children || route._loadedRoutes) {\n          res.push(this.processRoutes(injectorForChildren, route.children ?? route._loadedRoutes));\n        }\n      }\n      return from(res).pipe(mergeAll());\n    }\n    preloadConfig(injector, route) {\n      return this.preloadingStrategy.preload(route, () => {\n        let loadedChildren$;\n        if (route.loadChildren && route.canLoad === undefined) {\n          loadedChildren$ = this.loader.loadChildren(injector, route);\n        } else {\n          loadedChildren$ = of(null);\n        }\n        const recursiveLoadChildren$ = loadedChildren$.pipe(mergeMap(config => {\n          if (config === null) {\n            return of(void 0);\n          }\n          route._loadedRoutes = config.routes;\n          route._loadedInjector = config.injector;\n          // If the loaded config was a module, use that as the module/module injector going\n          // forward. Otherwise, continue using the current module/module injector.\n          return this.processRoutes(config.injector ?? injector, config.routes);\n        }));\n        if (route.loadComponent && !route._loadedComponent) {\n          const loadComponent$ = this.loader.loadComponent(route);\n          return from([recursiveLoadChildren$, loadComponent$]).pipe(mergeAll());\n        } else {\n          return recursiveLoadChildren$;\n        }\n      });\n    }\n    static ɵfac = function RouterPreloader_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RouterPreloader)(i0.ɵɵinject(Router), i0.ɵɵinject(i0.EnvironmentInjector), i0.ɵɵinject(PreloadingStrategy), i0.ɵɵinject(RouterConfigLoader));\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: RouterPreloader,\n      factory: RouterPreloader.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return RouterPreloader;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst ROUTER_SCROLLER = /*#__PURE__*/new InjectionToken('');\nlet RouterScroller = /*#__PURE__*/(() => {\n  class RouterScroller {\n    urlSerializer;\n    transitions;\n    viewportScroller;\n    zone;\n    options;\n    routerEventsSubscription;\n    scrollEventsSubscription;\n    lastId = 0;\n    lastSource = 'imperative';\n    restoredId = 0;\n    store = {};\n    /** @docs-private */\n    constructor(urlSerializer, transitions, viewportScroller, zone, options = {}) {\n      this.urlSerializer = urlSerializer;\n      this.transitions = transitions;\n      this.viewportScroller = viewportScroller;\n      this.zone = zone;\n      this.options = options;\n      // Default both options to 'disabled'\n      options.scrollPositionRestoration ||= 'disabled';\n      options.anchorScrolling ||= 'disabled';\n    }\n    init() {\n      // we want to disable the automatic scrolling because having two places\n      // responsible for scrolling results race conditions, especially given\n      // that browser don't implement this behavior consistently\n      if (this.options.scrollPositionRestoration !== 'disabled') {\n        this.viewportScroller.setHistoryScrollRestoration('manual');\n      }\n      this.routerEventsSubscription = this.createScrollEvents();\n      this.scrollEventsSubscription = this.consumeScrollEvents();\n    }\n    createScrollEvents() {\n      return this.transitions.events.subscribe(e => {\n        if (e instanceof NavigationStart) {\n          // store the scroll position of the current stable navigations.\n          this.store[this.lastId] = this.viewportScroller.getScrollPosition();\n          this.lastSource = e.navigationTrigger;\n          this.restoredId = e.restoredState ? e.restoredState.navigationId : 0;\n        } else if (e instanceof NavigationEnd) {\n          this.lastId = e.id;\n          this.scheduleScrollEvent(e, this.urlSerializer.parse(e.urlAfterRedirects).fragment);\n        } else if (e instanceof NavigationSkipped && e.code === NavigationSkippedCode.IgnoredSameUrlNavigation) {\n          this.lastSource = undefined;\n          this.restoredId = 0;\n          this.scheduleScrollEvent(e, this.urlSerializer.parse(e.url).fragment);\n        }\n      });\n    }\n    consumeScrollEvents() {\n      return this.transitions.events.subscribe(e => {\n        if (!(e instanceof Scroll)) return;\n        // a popstate event. The pop state event will always ignore anchor scrolling.\n        if (e.position) {\n          if (this.options.scrollPositionRestoration === 'top') {\n            this.viewportScroller.scrollToPosition([0, 0]);\n          } else if (this.options.scrollPositionRestoration === 'enabled') {\n            this.viewportScroller.scrollToPosition(e.position);\n          }\n          // imperative navigation \"forward\"\n        } else {\n          if (e.anchor && this.options.anchorScrolling === 'enabled') {\n            this.viewportScroller.scrollToAnchor(e.anchor);\n          } else if (this.options.scrollPositionRestoration !== 'disabled') {\n            this.viewportScroller.scrollToPosition([0, 0]);\n          }\n        }\n      });\n    }\n    scheduleScrollEvent(routerEvent, anchor) {\n      this.zone.runOutsideAngular(() => {\n        // The scroll event needs to be delayed until after change detection. Otherwise, we may\n        // attempt to restore the scroll position before the router outlet has fully rendered the\n        // component by executing its update block of the template function.\n        setTimeout(() => {\n          this.zone.run(() => {\n            this.transitions.events.next(new Scroll(routerEvent, this.lastSource === 'popstate' ? this.store[this.restoredId] : null, anchor));\n          });\n        }, 0);\n      });\n    }\n    /** @docs-private */\n    ngOnDestroy() {\n      this.routerEventsSubscription?.unsubscribe();\n      this.scrollEventsSubscription?.unsubscribe();\n    }\n    static ɵfac = function RouterScroller_Factory(__ngFactoryType__) {\n      i0.ɵɵinvalidFactory();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: RouterScroller,\n      factory: RouterScroller.ɵfac\n    });\n  }\n  return RouterScroller;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Sets up providers necessary to enable `Router` functionality for the application.\n * Allows to configure a set of routes as well as extra features that should be enabled.\n *\n * @usageNotes\n *\n * Basic example of how you can add a Router to your application:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent, {\n *   providers: [provideRouter(appRoutes)]\n * });\n * ```\n *\n * You can also enable optional features in the Router by adding functions from the `RouterFeatures`\n * type:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes,\n *         withDebugTracing(),\n *         withRouterConfig({paramsInheritanceStrategy: 'always'}))\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link RouterFeatures}\n *\n * @publicApi\n * @param routes A set of `Route`s to use for the application routing table.\n * @param features Optional features to configure additional router behaviors.\n * @returns A set of providers to setup a Router.\n */\nfunction provideRouter(routes, ...features) {\n  return makeEnvironmentProviders([{\n    provide: ROUTES,\n    multi: true,\n    useValue: routes\n  }, typeof ngDevMode === 'undefined' || ngDevMode ? {\n    provide: ROUTER_IS_PROVIDED,\n    useValue: true\n  } : [], {\n    provide: ActivatedRoute,\n    useFactory: rootRoute,\n    deps: [Router]\n  }, {\n    provide: APP_BOOTSTRAP_LISTENER,\n    multi: true,\n    useFactory: getBootstrapListener\n  }, features.map(feature => feature.ɵproviders)]);\n}\nfunction rootRoute(router) {\n  return router.routerState.root;\n}\n/**\n * Helper function to create an object that represents a Router feature.\n */\nfunction routerFeature(kind, providers) {\n  return {\n    ɵkind: kind,\n    ɵproviders: providers\n  };\n}\n/**\n * An Injection token used to indicate whether `provideRouter` or `RouterModule.forRoot` was ever\n * called.\n */\nconst ROUTER_IS_PROVIDED = /*#__PURE__*/new InjectionToken('', {\n  providedIn: 'root',\n  factory: () => false\n});\nconst routerIsProvidedDevModeCheck = {\n  provide: ENVIRONMENT_INITIALIZER,\n  multi: true,\n  useFactory() {\n    return () => {\n      if (!inject(ROUTER_IS_PROVIDED)) {\n        console.warn('`provideRoutes` was called without `provideRouter` or `RouterModule.forRoot`. ' + 'This is likely a mistake.');\n      }\n    };\n  }\n};\n/**\n * Registers a DI provider for a set of routes.\n * @param routes The route configuration to provide.\n *\n * @usageNotes\n *\n * ```ts\n * @NgModule({\n *   providers: [provideRoutes(ROUTES)]\n * })\n * class LazyLoadedChildModule {}\n * ```\n *\n * @deprecated If necessary, provide routes using the `ROUTES` `InjectionToken`.\n * @see {@link ROUTES}\n * @publicApi\n */\nfunction provideRoutes(routes) {\n  return [{\n    provide: ROUTES,\n    multi: true,\n    useValue: routes\n  }, typeof ngDevMode === 'undefined' || ngDevMode ? routerIsProvidedDevModeCheck : []];\n}\n/**\n * Enables customizable scrolling behavior for router navigations.\n *\n * @usageNotes\n *\n * Basic example of how you can enable scrolling feature:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withInMemoryScrolling())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n * @see {@link ViewportScroller}\n *\n * @publicApi\n * @param options Set of configuration parameters to customize scrolling behavior, see\n *     `InMemoryScrollingOptions` for additional information.\n * @returns A set of providers for use with `provideRouter`.\n */\nfunction withInMemoryScrolling(options = {}) {\n  const providers = [{\n    provide: ROUTER_SCROLLER,\n    useFactory: () => {\n      const viewportScroller = inject(ViewportScroller);\n      const zone = inject(NgZone);\n      const transitions = inject(NavigationTransitions);\n      const urlSerializer = inject(UrlSerializer);\n      return new RouterScroller(urlSerializer, transitions, viewportScroller, zone, options);\n    }\n  }];\n  return routerFeature(4 /* RouterFeatureKind.InMemoryScrollingFeature */, providers);\n}\nfunction getBootstrapListener() {\n  const injector = inject(Injector);\n  return bootstrappedComponentRef => {\n    const ref = injector.get(ApplicationRef);\n    if (bootstrappedComponentRef !== ref.components[0]) {\n      return;\n    }\n    const router = injector.get(Router);\n    const bootstrapDone = injector.get(BOOTSTRAP_DONE);\n    if (injector.get(INITIAL_NAVIGATION) === 1 /* InitialNavigation.EnabledNonBlocking */) {\n      router.initialNavigation();\n    }\n    injector.get(ROUTER_PRELOADER, null, InjectFlags.Optional)?.setUpPreloading();\n    injector.get(ROUTER_SCROLLER, null, InjectFlags.Optional)?.init();\n    router.resetRootComponentType(ref.componentTypes[0]);\n    if (!bootstrapDone.closed) {\n      bootstrapDone.next();\n      bootstrapDone.complete();\n      bootstrapDone.unsubscribe();\n    }\n  };\n}\n/**\n * A subject used to indicate that the bootstrapping phase is done. When initial navigation is\n * `enabledBlocking`, the first navigation waits until bootstrapping is finished before continuing\n * to the activation phase.\n */\nconst BOOTSTRAP_DONE = /*#__PURE__*/new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'bootstrap done indicator' : '', {\n  factory: () => {\n    return new Subject();\n  }\n});\nconst INITIAL_NAVIGATION = /*#__PURE__*/new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'initial navigation' : '', {\n  providedIn: 'root',\n  factory: () => 1 /* InitialNavigation.EnabledNonBlocking */\n});\n/**\n * Configures initial navigation to start before the root component is created.\n *\n * The bootstrap is blocked until the initial navigation is complete. This should be set in case\n * you use [server-side rendering](guide/ssr), but do not enable [hydration](guide/hydration) for\n * your application.\n *\n * @usageNotes\n *\n * Basic example of how you can enable this navigation behavior:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withEnabledBlockingInitialNavigation())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @publicApi\n * @returns A set of providers for use with `provideRouter`.\n */\nfunction withEnabledBlockingInitialNavigation() {\n  const providers = [{\n    provide: INITIAL_NAVIGATION,\n    useValue: 0 /* InitialNavigation.EnabledBlocking */\n  }, provideAppInitializer(() => {\n    const injector = inject(Injector);\n    const locationInitialized = injector.get(LOCATION_INITIALIZED, Promise.resolve());\n    return locationInitialized.then(() => {\n      return new Promise(resolve => {\n        const router = injector.get(Router);\n        const bootstrapDone = injector.get(BOOTSTRAP_DONE);\n        afterNextNavigation(router, () => {\n          // Unblock APP_INITIALIZER in case the initial navigation was canceled or errored\n          // without a redirect.\n          resolve(true);\n        });\n        injector.get(NavigationTransitions).afterPreactivation = () => {\n          // Unblock APP_INITIALIZER once we get to `afterPreactivation`. At this point, we\n          // assume activation will complete successfully (even though this is not\n          // guaranteed).\n          resolve(true);\n          return bootstrapDone.closed ? of(void 0) : bootstrapDone;\n        };\n        router.initialNavigation();\n      });\n    });\n  })];\n  return routerFeature(2 /* RouterFeatureKind.EnabledBlockingInitialNavigationFeature */, providers);\n}\n/**\n * Disables initial navigation.\n *\n * Use if there is a reason to have more control over when the router starts its initial navigation\n * due to some complex initialization logic.\n *\n * @usageNotes\n *\n * Basic example of how you can disable initial navigation:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withDisabledInitialNavigation())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withDisabledInitialNavigation() {\n  const providers = [provideAppInitializer(() => {\n    inject(Router).setUpLocationChangeListener();\n  }), {\n    provide: INITIAL_NAVIGATION,\n    useValue: 2 /* InitialNavigation.Disabled */\n  }];\n  return routerFeature(3 /* RouterFeatureKind.DisabledInitialNavigationFeature */, providers);\n}\n/**\n * Enables logging of all internal navigation events to the console.\n * Extra logging might be useful for debugging purposes to inspect Router event sequence.\n *\n * @usageNotes\n *\n * Basic example of how you can enable debug tracing:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withDebugTracing())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withDebugTracing() {\n  let providers = [];\n  if (typeof ngDevMode === 'undefined' || ngDevMode) {\n    providers = [{\n      provide: ENVIRONMENT_INITIALIZER,\n      multi: true,\n      useFactory: () => {\n        const router = inject(Router);\n        return () => router.events.subscribe(e => {\n          // tslint:disable:no-console\n          console.group?.(`Router Event: ${e.constructor.name}`);\n          console.log(stringifyEvent(e));\n          console.log(e);\n          console.groupEnd?.();\n          // tslint:enable:no-console\n        });\n      }\n    }];\n  } else {\n    providers = [];\n  }\n  return routerFeature(1 /* RouterFeatureKind.DebugTracingFeature */, providers);\n}\nconst ROUTER_PRELOADER = /*#__PURE__*/new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'router preloader' : '');\n/**\n * Allows to configure a preloading strategy to use. The strategy is configured by providing a\n * reference to a class that implements a `PreloadingStrategy`.\n *\n * @usageNotes\n *\n * Basic example of how you can configure preloading:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withPreloading(PreloadAllModules))\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @param preloadingStrategy A reference to a class that implements a `PreloadingStrategy` that\n *     should be used.\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withPreloading(preloadingStrategy) {\n  const providers = [{\n    provide: ROUTER_PRELOADER,\n    useExisting: RouterPreloader\n  }, {\n    provide: PreloadingStrategy,\n    useExisting: preloadingStrategy\n  }];\n  return routerFeature(0 /* RouterFeatureKind.PreloadingFeature */, providers);\n}\n/**\n * Allows to provide extra parameters to configure Router.\n *\n * @usageNotes\n *\n * Basic example of how you can provide extra configuration options:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withRouterConfig({\n *          onSameUrlNavigation: 'reload'\n *       }))\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @param options A set of parameters to configure Router, see `RouterConfigOptions` for\n *     additional information.\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withRouterConfig(options) {\n  const providers = [{\n    provide: ROUTER_CONFIGURATION,\n    useValue: options\n  }];\n  return routerFeature(5 /* RouterFeatureKind.RouterConfigurationFeature */, providers);\n}\n/**\n * Provides the location strategy that uses the URL fragment instead of the history API.\n *\n * @usageNotes\n *\n * Basic example of how you can use the hash location option:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withHashLocation())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n * @see {@link /api/common/HashLocationStrategy HashLocationStrategy}\n *\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withHashLocation() {\n  const providers = [{\n    provide: LocationStrategy,\n    useClass: HashLocationStrategy\n  }];\n  return routerFeature(6 /* RouterFeatureKind.RouterHashLocationFeature */, providers);\n}\n/**\n * Provides a function which is called when a navigation error occurs.\n *\n * This function is run inside application's [injection context](guide/di/dependency-injection-context)\n * so you can use the [`inject`](api/core/inject) function.\n *\n * This function can return a `RedirectCommand` to convert the error to a redirect, similar to returning\n * a `UrlTree` or `RedirectCommand` from a guard. This will also prevent the `Router` from emitting\n * `NavigationError`; it will instead emit `NavigationCancel` with code NavigationCancellationCode.Redirect.\n * Return values other than `RedirectCommand` are ignored and do not change any behavior with respect to\n * how the `Router` handles the error.\n *\n * @usageNotes\n *\n * Basic example of how you can use the error handler option:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withNavigationErrorHandler((e: NavigationError) =>\n * inject(MyErrorTracker).trackError(e)))\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link NavigationError}\n * @see {@link /api/core/inject inject}\n * @see {@link runInInjectionContext}\n *\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withNavigationErrorHandler(handler) {\n  const providers = [{\n    provide: NAVIGATION_ERROR_HANDLER,\n    useValue: handler\n  }];\n  return routerFeature(7 /* RouterFeatureKind.NavigationErrorHandlerFeature */, providers);\n}\n/**\n * Enables binding information from the `Router` state directly to the inputs of the component in\n * `Route` configurations.\n *\n * @usageNotes\n *\n * Basic example of how you can enable the feature:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withComponentInputBinding())\n *     ]\n *   }\n * );\n * ```\n *\n * The router bindings information from any of the following sources:\n *\n *  - query parameters\n *  - path and matrix parameters\n *  - static route data\n *  - data from resolvers\n *\n * Duplicate keys are resolved in the same order from above, from least to greatest,\n * meaning that resolvers have the highest precedence and override any of the other information\n * from the route.\n *\n * Importantly, when an input does not have an item in the route data with a matching key, this\n * input is set to `undefined`. This prevents previous information from being\n * retained if the data got removed from the route (i.e. if a query parameter is removed).\n * Default values can be provided with a resolver on the route to ensure the value is always present\n * or an input and use an input transform in the component.\n *\n * @see {@link /guide/components/inputs#input-transforms Input Transforms}\n * @returns A set of providers for use with `provideRouter`.\n */\nfunction withComponentInputBinding() {\n  const providers = [RoutedComponentInputBinder, {\n    provide: INPUT_BINDER,\n    useExisting: RoutedComponentInputBinder\n  }];\n  return routerFeature(8 /* RouterFeatureKind.ComponentInputBindingFeature */, providers);\n}\n/**\n * Enables view transitions in the Router by running the route activation and deactivation inside of\n * `document.startViewTransition`.\n *\n * Note: The View Transitions API is not available in all browsers. If the browser does not support\n * view transitions, the Router will not attempt to start a view transition and continue processing\n * the navigation as usual.\n *\n * @usageNotes\n *\n * Basic example of how you can enable the feature:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withViewTransitions())\n *     ]\n *   }\n * );\n * ```\n *\n * @returns A set of providers for use with `provideRouter`.\n * @see https://developer.chrome.com/docs/web-platform/view-transitions/\n * @see https://developer.mozilla.org/en-US/docs/Web/API/View_Transitions_API\n * @developerPreview\n */\nfunction withViewTransitions(options) {\n  _performanceMarkFeature('NgRouterViewTransitions');\n  const providers = [{\n    provide: CREATE_VIEW_TRANSITION,\n    useValue: createViewTransition\n  }, {\n    provide: VIEW_TRANSITION_OPTIONS,\n    useValue: {\n      skipNextTransition: !!options?.skipInitialTransition,\n      ...options\n    }\n  }];\n  return routerFeature(9 /* RouterFeatureKind.ViewTransitionsFeature */, providers);\n}\n\n/**\n * The directives defined in the `RouterModule`.\n */\nconst ROUTER_DIRECTIVES = [RouterOutlet, RouterLink, RouterLinkActive, _EmptyOutletComponent];\n/**\n * @docsNotRequired\n */\nconst ROUTER_FORROOT_GUARD = /*#__PURE__*/new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'router duplicate forRoot guard' : '');\n// TODO(atscott): All of these except `ActivatedRoute` are `providedIn: 'root'`. They are only kept\n// here to avoid a breaking change whereby the provider order matters based on where the\n// `RouterModule`/`RouterTestingModule` is imported. These can/should be removed as a \"breaking\"\n// change in a major version.\nconst ROUTER_PROVIDERS = [Location, {\n  provide: UrlSerializer,\n  useClass: DefaultUrlSerializer\n}, Router, ChildrenOutletContexts, {\n  provide: ActivatedRoute,\n  useFactory: rootRoute,\n  deps: [Router]\n}, RouterConfigLoader,\n// Only used to warn when `provideRoutes` is used without `RouterModule` or `provideRouter`. Can\n// be removed when `provideRoutes` is removed.\ntypeof ngDevMode === 'undefined' || ngDevMode ? {\n  provide: ROUTER_IS_PROVIDED,\n  useValue: true\n} : []];\n/**\n * @description\n *\n * Adds directives and providers for in-app navigation among views defined in an application.\n * Use the Angular `Router` service to declaratively specify application states and manage state\n * transitions.\n *\n * You can import this NgModule multiple times, once for each lazy-loaded bundle.\n * However, only one `Router` service can be active.\n * To ensure this, there are two ways to register routes when importing this module:\n *\n * * The `forRoot()` method creates an `NgModule` that contains all the directives, the given\n * routes, and the `Router` service itself.\n * * The `forChild()` method creates an `NgModule` that contains all the directives and the given\n * routes, but does not include the `Router` service.\n *\n * @see [Routing and Navigation guide](guide/routing/common-router-tasks) for an\n * overview of how the `Router` service should be used.\n *\n * @publicApi\n */\nlet RouterModule = /*#__PURE__*/(() => {\n  class RouterModule {\n    constructor() {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        inject(ROUTER_FORROOT_GUARD, {\n          optional: true\n        });\n      }\n    }\n    /**\n     * Creates and configures a module with all the router providers and directives.\n     * Optionally sets up an application listener to perform an initial navigation.\n     *\n     * When registering the NgModule at the root, import as follows:\n     *\n     * ```ts\n     * @NgModule({\n     *   imports: [RouterModule.forRoot(ROUTES)]\n     * })\n     * class MyNgModule {}\n     * ```\n     *\n     * @param routes An array of `Route` objects that define the navigation paths for the application.\n     * @param config An `ExtraOptions` configuration object that controls how navigation is performed.\n     * @return The new `NgModule`.\n     *\n     */\n    static forRoot(routes, config) {\n      return {\n        ngModule: RouterModule,\n        providers: [ROUTER_PROVIDERS, typeof ngDevMode === 'undefined' || ngDevMode ? config?.enableTracing ? withDebugTracing().ɵproviders : [] : [], {\n          provide: ROUTES,\n          multi: true,\n          useValue: routes\n        }, typeof ngDevMode === 'undefined' || ngDevMode ? {\n          provide: ROUTER_FORROOT_GUARD,\n          useFactory: provideForRootGuard,\n          deps: [[Router, new Optional(), new SkipSelf()]]\n        } : [], config?.errorHandler ? {\n          provide: NAVIGATION_ERROR_HANDLER,\n          useValue: config.errorHandler\n        } : [], {\n          provide: ROUTER_CONFIGURATION,\n          useValue: config ? config : {}\n        }, config?.useHash ? provideHashLocationStrategy() : providePathLocationStrategy(), provideRouterScroller(), config?.preloadingStrategy ? withPreloading(config.preloadingStrategy).ɵproviders : [], config?.initialNavigation ? provideInitialNavigation(config) : [], config?.bindToComponentInputs ? withComponentInputBinding().ɵproviders : [], config?.enableViewTransitions ? withViewTransitions().ɵproviders : [], provideRouterInitializer()]\n      };\n    }\n    /**\n     * Creates a module with all the router directives and a provider registering routes,\n     * without creating a new Router service.\n     * When registering for submodules and lazy-loaded submodules, create the NgModule as follows:\n     *\n     * ```ts\n     * @NgModule({\n     *   imports: [RouterModule.forChild(ROUTES)]\n     * })\n     * class MyNgModule {}\n     * ```\n     *\n     * @param routes An array of `Route` objects that define the navigation paths for the submodule.\n     * @return The new NgModule.\n     *\n     */\n    static forChild(routes) {\n      return {\n        ngModule: RouterModule,\n        providers: [{\n          provide: ROUTES,\n          multi: true,\n          useValue: routes\n        }]\n      };\n    }\n    static ɵfac = function RouterModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RouterModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: RouterModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n  return RouterModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * For internal use by `RouterModule` only. Note that this differs from `withInMemoryRouterScroller`\n * because it reads from the `ExtraOptions` which should not be used in the standalone world.\n */\nfunction provideRouterScroller() {\n  return {\n    provide: ROUTER_SCROLLER,\n    useFactory: () => {\n      const viewportScroller = inject(ViewportScroller);\n      const zone = inject(NgZone);\n      const config = inject(ROUTER_CONFIGURATION);\n      const transitions = inject(NavigationTransitions);\n      const urlSerializer = inject(UrlSerializer);\n      if (config.scrollOffset) {\n        viewportScroller.setOffset(config.scrollOffset);\n      }\n      return new RouterScroller(urlSerializer, transitions, viewportScroller, zone, config);\n    }\n  };\n}\n// Note: For internal use only with `RouterModule`. Standalone setup via `provideRouter` should\n// provide hash location directly via `{provide: LocationStrategy, useClass: HashLocationStrategy}`.\nfunction provideHashLocationStrategy() {\n  return {\n    provide: LocationStrategy,\n    useClass: HashLocationStrategy\n  };\n}\n// Note: For internal use only with `RouterModule`. Standalone setup via `provideRouter` does not\n// need this at all because `PathLocationStrategy` is the default factory for `LocationStrategy`.\nfunction providePathLocationStrategy() {\n  return {\n    provide: LocationStrategy,\n    useClass: PathLocationStrategy\n  };\n}\nfunction provideForRootGuard(router) {\n  if (router) {\n    throw new _RuntimeError(4007 /* RuntimeErrorCode.FOR_ROOT_CALLED_TWICE */, `The Router was provided more than once. This can happen if 'forRoot' is used outside of the root injector.` + ` Lazy loaded modules should use RouterModule.forChild() instead.`);\n  }\n  return 'guarded';\n}\n// Note: For internal use only with `RouterModule`. Standalone router setup with `provideRouter`\n// users call `withXInitialNavigation` directly.\nfunction provideInitialNavigation(config) {\n  return [config.initialNavigation === 'disabled' ? withDisabledInitialNavigation().ɵproviders : [], config.initialNavigation === 'enabledBlocking' ? withEnabledBlockingInitialNavigation().ɵproviders : []];\n}\n// TODO(atscott): This should not be in the public API\n/**\n * A DI token for the router initializer that\n * is called after the app is bootstrapped.\n *\n * @publicApi\n */\nconst ROUTER_INITIALIZER = /*#__PURE__*/new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'Router Initializer' : '');\nfunction provideRouterInitializer() {\n  return [\n  // ROUTER_INITIALIZER token should be removed. It's public API but shouldn't be. We can just\n  // have `getBootstrapListener` directly attached to APP_BOOTSTRAP_LISTENER.\n  {\n    provide: ROUTER_INITIALIZER,\n    useFactory: getBootstrapListener\n  }, {\n    provide: APP_BOOTSTRAP_LISTENER,\n    multi: true,\n    useExisting: ROUTER_INITIALIZER\n  }];\n}\nexport { NoPreloading, PreloadAllModules, PreloadingStrategy, ROUTER_INITIALIZER, ROUTER_PROVIDERS, RouterLink, RouterLinkActive, RouterModule, RouterPreloader, provideRouter, provideRoutes, withComponentInputBinding, withDebugTracing, withDisabledInitialNavigation, withEnabledBlockingInitialNavigation, withHashLocation, withInMemoryScrolling, withNavigationErrorHandler, withPreloading, withRouterConfig, withViewTransitions };", "map": {"version": 3, "names": ["i3", "LOCATION_INITIALIZED", "HashLocationStrategy", "LocationStrategy", "ViewportScroller", "Location", "PathLocationStrategy", "i0", "ɵRuntimeError", "_RuntimeError", "ɵɵsanitizeUrlOrResourceUrl", "__sanitizeUrlOrResourceUrl", "booleanAttribute", "HostListener", "Input", "HostBinding", "Attribute", "Directive", "EventEmitter", "Output", "ContentChildren", "Optional", "createEnvironmentInjector", "Injectable", "InjectionToken", "ɵperformanceMarkFeature", "_performanceMarkFeature", "makeEnvironmentProviders", "APP_BOOTSTRAP_LISTENER", "ENVIRONMENT_INITIALIZER", "provideAppInitializer", "inject", "Injector", "ApplicationRef", "InjectFlags", "NgZone", "SkipSelf", "NgModule", "NavigationEnd", "isUrlTree", "Router", "ActivatedRoute", "RouterConfigLoader", "NavigationStart", "NavigationSkipped", "NavigationSkippedCode", "<PERSON><PERSON>", "UrlSerializer", "NavigationTransitions", "ROUTES", "afterNextNavigation", "ROUTER_CONFIGURATION", "NAVIGATION_ERROR_HANDLER", "RoutedComponentInputBinder", "INPUT_BINDER", "createViewTransition", "CREATE_VIEW_TRANSITION", "VIEW_TRANSITION_OPTIONS", "stringifyEvent", "DefaultUrlSerializer", "ChildrenOutletContexts", "RouterOutlet", "ɵEmptyOutletComponent", "_EmptyOutletComponent", "Subject", "of", "from", "mergeAll", "catchError", "filter", "concatMap", "mergeMap", "RouterLink", "router", "route", "tabIndexAttribute", "renderer", "el", "locationStrategy", "href", "target", "queryParams", "fragment", "queryParamsHandling", "state", "info", "relativeTo", "isAnchorElement", "subscription", "onChanges", "constructor", "tagName", "nativeElement", "toLowerCase", "events", "subscribe", "s", "updateHref", "setTabIndexIfNotOnNativeEl", "preserveFragment", "skipLocationChange", "replaceUrl", "newTabIndex", "applyAttributeValue", "ngOnChanges", "changes", "ngDevMode", "routerLinkInput", "undefined", "next", "routerLink", "commandsOrUrlTree", "Array", "isArray", "onClick", "button", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "metaKey", "urlTree", "extras", "navigateByUrl", "ngOnDestroy", "unsubscribe", "prepareExternalUrl", "serializeUrl", "sanitizedValue", "attrName", "attrValue", "setAttribute", "removeAttribute", "createUrlTree", "ɵfac", "RouterLink_Factory", "__ngFactoryType__", "ɵɵdirectiveInject", "ɵɵinjectAttribute", "Renderer2", "ElementRef", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostVars", "hostBindings", "RouterLink_HostBindings", "rf", "ctx", "ɵɵlistener", "RouterLink_click_HostBindingHandler", "$event", "ɵɵattribute", "inputs", "features", "ɵɵNgOnChangesFeature", "RouterLinkActive", "element", "cdr", "link", "links", "classes", "routerEventsSubscription", "linkInputChangesSubscription", "_isActive", "isActive", "routerLinkActiveOptions", "exact", "ariaCurrentWhenActive", "isActiveChange", "update", "ngAfterContentInit", "pipe", "_", "subscribeToEachLinkOnChanges", "allLinkChanges", "toArray", "map", "isLinkActive", "routerLinkActive", "data", "split", "c", "navigated", "queueMicrotask", "hasActiveLinks", "for<PERSON>ach", "addClass", "removeClass", "toString", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emit", "options", "isActiveMatchOptions", "isActiveCheckFn", "some", "RouterLinkActive_Factory", "ChangeDetectorRef", "contentQueries", "RouterLinkActive_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "outputs", "exportAs", "paths", "PreloadingStrategy", "PreloadAllModules", "preload", "fn", "PreloadAllModules_Factory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "NoPreloading", "NoPreloading_Factory", "<PERSON>r<PERSON><PERSON><PERSON><PERSON>", "injector", "preloadingStrategy", "loader", "setUpPreloading", "e", "processRoutes", "config", "routes", "res", "providers", "_injector", "path", "injectorForCurrentRoute", "injectorForChildren", "_loadedInjector", "loadChildren", "_loadedRoutes", "canLoad", "loadComponent", "_loadedComponent", "push", "preloadConfig", "children", "loadedChildren$", "recursiveLoadChildren$", "loadComponent$", "RouterPreloader_Factory", "ɵɵinject", "EnvironmentInjector", "ROUTER_SCROLLER", "RouterS<PERSON>roller", "urlSerializer", "transitions", "viewportScroller", "zone", "scrollEventsSubscription", "lastId", "lastSource", "restoredId", "store", "scrollPositionRestoration", "anchorScrolling", "init", "setHistoryScrollRestoration", "createScrollEvents", "consumeScrollEvents", "getScrollPosition", "navigationTrigger", "restoredState", "navigationId", "id", "scheduleScrollEvent", "parse", "urlAfterRedirects", "code", "IgnoredSameUrlNavigation", "url", "position", "scrollToPosition", "anchor", "scrollToAnchor", "routerEvent", "runOutsideAngular", "setTimeout", "run", "RouterScroller_Factory", "ɵɵinvalidFactory", "provideRouter", "provide", "multi", "useValue", "ROUTER_IS_PROVIDED", "useFactory", "rootRoute", "deps", "getBootstrapListener", "feature", "ɵproviders", "routerState", "root", "routerFeature", "kind", "ɵkind", "routerIsProvidedDevModeCheck", "console", "warn", "provideRoutes", "withInMemoryScrolling", "bootstrappedComponentRef", "ref", "get", "components", "bootstrapDone", "BOOTSTRAP_DONE", "INITIAL_NAVIGATION", "initialNavigation", "ROUTER_PRELOADER", "resetRootComponentType", "componentTypes", "closed", "complete", "withEnabledBlockingInitialNavigation", "locationInitialized", "Promise", "resolve", "then", "afterPreactivation", "withDisabledInitialNavigation", "setUpLocationChangeListener", "withDebugTracing", "group", "name", "log", "groupEnd", "withPreloading", "useExisting", "withRouterConfig", "withHashLocation", "useClass", "withNavigationErrorHandler", "handler", "withComponentInputBinding", "withViewTransitions", "skipNextTransition", "skipInitialTransition", "ROUTER_DIRECTIVES", "ROUTER_FORROOT_GUARD", "ROUTER_PROVIDERS", "RouterModule", "optional", "forRoot", "ngModule", "enableTracing", "provideForRootGuard", "<PERSON><PERSON><PERSON><PERSON>", "useHash", "provideHashLocationStrategy", "providePathLocationStrategy", "provideRouterScroller", "provideInitialNavigation", "bindToComponentInputs", "enableViewTransitions", "provideRouterInitializer", "<PERSON><PERSON><PERSON><PERSON>", "RouterModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "scrollOffset", "setOffset", "ROUTER_INITIALIZER"], "sources": ["C:/console/aava-ui-web/node_modules/@angular/router/fesm2022/router_module-DTJgGWLd.mjs"], "sourcesContent": ["/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i3 from '@angular/common';\nimport { LOCATION_INITIALIZED, HashLocationStrategy, LocationStrategy, ViewportScroller, Location, PathLocationStrategy } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { ɵRuntimeError as _RuntimeError, ɵɵsanitizeUrlOrResourceUrl as __sanitizeUrlOrResourceUrl, booleanAttribute, HostListener, Input, HostBinding, Attribute, Directive, EventEmitter, Output, ContentChildren, Optional, createEnvironmentInjector, Injectable, InjectionToken, ɵperformanceMarkFeature as _performanceMarkFeature, makeEnvironmentProviders, APP_BOOTSTRAP_LISTENER, ENVIRONMENT_INITIALIZER, provideAppInitializer, inject, Injector, ApplicationRef, InjectFlags, NgZone, SkipSelf, NgModule } from '@angular/core';\nimport { NavigationEnd, isUrlTree, Router, ActivatedRoute, RouterConfigLoader, NavigationStart, NavigationSkipped, NavigationSkippedCode, Scroll, UrlSerializer, NavigationTransitions, ROUTES, afterNextNavigation, ROUTER_CONFIGURATION, NAVIGATION_ERROR_HANDLER, RoutedComponentInputBinder, INPUT_BINDER, createViewTransition, CREATE_VIEW_TRANSITION, VIEW_TRANSITION_OPTIONS, stringifyEvent, DefaultUrlSerializer, ChildrenOutletContexts, RouterOutlet, ɵEmptyOutletComponent as _EmptyOutletComponent } from './router-Dwfin5Au.mjs';\nimport { Subject, of, from } from 'rxjs';\nimport { mergeAll, catchError, filter, concatMap, mergeMap } from 'rxjs/operators';\n\n/**\n * @description\n *\n * When applied to an element in a template, makes that element a link\n * that initiates navigation to a route. Navigation opens one or more routed components\n * in one or more `<router-outlet>` locations on the page.\n *\n * Given a route configuration `[{ path: 'user/:name', component: UserCmp }]`,\n * the following creates a static link to the route:\n * `<a routerLink=\"/user/bob\">link to user component</a>`\n *\n * You can use dynamic values to generate the link.\n * For a dynamic link, pass an array of path segments,\n * followed by the params for each segment.\n * For example, `['/team', teamId, 'user', userName, {details: true}]`\n * generates a link to `/team/11/user/bob;details=true`.\n *\n * Multiple static segments can be merged into one term and combined with dynamic segments.\n * For example, `['/team/11/user', userName, {details: true}]`\n *\n * The input that you provide to the link is treated as a delta to the current URL.\n * For instance, suppose the current URL is `/user/(box//aux:team)`.\n * The link `<a [routerLink]=\"['/user/jim']\">Jim</a>` creates the URL\n * `/user/(jim//aux:team)`.\n * See {@link Router#createUrlTree} for more information.\n *\n * @usageNotes\n *\n * You can use absolute or relative paths in a link, set query parameters,\n * control how parameters are handled, and keep a history of navigation states.\n *\n * ### Relative link paths\n *\n * The first segment name can be prepended with `/`, `./`, or `../`.\n * * If the first segment begins with `/`, the router looks up the route from the root of the\n *   app.\n * * If the first segment begins with `./`, or doesn't begin with a slash, the router\n *   looks in the children of the current activated route.\n * * If the first segment begins with `../`, the router goes up one level in the route tree.\n *\n * ### Setting and handling query params and fragments\n *\n * The following link adds a query parameter and a fragment to the generated URL:\n *\n * ```html\n * <a [routerLink]=\"['/user/bob']\" [queryParams]=\"{debug: true}\" fragment=\"education\">\n *   link to user component\n * </a>\n * ```\n * By default, the directive constructs the new URL using the given query parameters.\n * The example generates the link: `/user/bob?debug=true#education`.\n *\n * You can instruct the directive to handle query parameters differently\n * by specifying the `queryParamsHandling` option in the link.\n * Allowed values are:\n *\n *  - `'merge'`: Merge the given `queryParams` into the current query params.\n *  - `'preserve'`: Preserve the current query params.\n *\n * For example:\n *\n * ```html\n * <a [routerLink]=\"['/user/bob']\" [queryParams]=\"{debug: true}\" queryParamsHandling=\"merge\">\n *   link to user component\n * </a>\n * ```\n *\n * `queryParams`, `fragment`, `queryParamsHandling`, `preserveFragment`, and `relativeTo`\n * cannot be used when the `routerLink` input is a `UrlTree`.\n *\n * See {@link UrlCreationOptions#queryParamsHandling}.\n *\n * ### Preserving navigation history\n *\n * You can provide a `state` value to be persisted to the browser's\n * [`History.state` property](https://developer.mozilla.org/en-US/docs/Web/API/History#Properties).\n * For example:\n *\n * ```html\n * <a [routerLink]=\"['/user/bob']\" [state]=\"{tracingId: 123}\">\n *   link to user component\n * </a>\n * ```\n *\n * Use {@link Router#getCurrentNavigation} to retrieve a saved\n * navigation-state value. For example, to capture the `tracingId` during the `NavigationStart`\n * event:\n *\n * ```ts\n * // Get NavigationStart events\n * router.events.pipe(filter(e => e instanceof NavigationStart)).subscribe(e => {\n *   const navigation = router.getCurrentNavigation();\n *   tracingService.trace({id: navigation.extras.state.tracingId});\n * });\n * ```\n *\n * @ngModule RouterModule\n *\n * @publicApi\n */\nclass RouterLink {\n    router;\n    route;\n    tabIndexAttribute;\n    renderer;\n    el;\n    locationStrategy;\n    /**\n     * Represents an `href` attribute value applied to a host element,\n     * when a host element is `<a>`. For other tags, the value is `null`.\n     */\n    href = null;\n    /**\n     * Represents the `target` attribute on a host element.\n     * This is only used when the host element is an `<a>` tag.\n     */\n    target;\n    /**\n     * Passed to {@link Router#createUrlTree} as part of the\n     * `UrlCreationOptions`.\n     * @see {@link UrlCreationOptions#queryParams}\n     * @see {@link Router#createUrlTree}\n     */\n    queryParams;\n    /**\n     * Passed to {@link Router#createUrlTree} as part of the\n     * `UrlCreationOptions`.\n     * @see {@link UrlCreationOptions#fragment}\n     * @see {@link Router#createUrlTree}\n     */\n    fragment;\n    /**\n     * Passed to {@link Router#createUrlTree} as part of the\n     * `UrlCreationOptions`.\n     * @see {@link UrlCreationOptions#queryParamsHandling}\n     * @see {@link Router#createUrlTree}\n     */\n    queryParamsHandling;\n    /**\n     * Passed to {@link Router#navigateByUrl} as part of the\n     * `NavigationBehaviorOptions`.\n     * @see {@link NavigationBehaviorOptions#state}\n     * @see {@link Router#navigateByUrl}\n     */\n    state;\n    /**\n     * Passed to {@link Router#navigateByUrl} as part of the\n     * `NavigationBehaviorOptions`.\n     * @see {@link NavigationBehaviorOptions#info}\n     * @see {@link Router#navigateByUrl}\n     */\n    info;\n    /**\n     * Passed to {@link Router#createUrlTree} as part of the\n     * `UrlCreationOptions`.\n     * Specify a value here when you do not want to use the default value\n     * for `routerLink`, which is the current activated route.\n     * Note that a value of `undefined` here will use the `routerLink` default.\n     * @see {@link UrlCreationOptions#relativeTo}\n     * @see {@link Router#createUrlTree}\n     */\n    relativeTo;\n    /** Whether a host element is an `<a>` tag. */\n    isAnchorElement;\n    subscription;\n    /** @internal */\n    onChanges = new Subject();\n    constructor(router, route, tabIndexAttribute, renderer, el, locationStrategy) {\n        this.router = router;\n        this.route = route;\n        this.tabIndexAttribute = tabIndexAttribute;\n        this.renderer = renderer;\n        this.el = el;\n        this.locationStrategy = locationStrategy;\n        const tagName = el.nativeElement.tagName?.toLowerCase();\n        this.isAnchorElement = tagName === 'a' || tagName === 'area';\n        if (this.isAnchorElement) {\n            this.subscription = router.events.subscribe((s) => {\n                if (s instanceof NavigationEnd) {\n                    this.updateHref();\n                }\n            });\n        }\n        else {\n            this.setTabIndexIfNotOnNativeEl('0');\n        }\n    }\n    /**\n     * Passed to {@link Router#createUrlTree} as part of the\n     * `UrlCreationOptions`.\n     * @see {@link UrlCreationOptions#preserveFragment}\n     * @see {@link Router#createUrlTree}\n     */\n    preserveFragment = false;\n    /**\n     * Passed to {@link Router#navigateByUrl} as part of the\n     * `NavigationBehaviorOptions`.\n     * @see {@link NavigationBehaviorOptions#skipLocationChange}\n     * @see {@link Router#navigateByUrl}\n     */\n    skipLocationChange = false;\n    /**\n     * Passed to {@link Router#navigateByUrl} as part of the\n     * `NavigationBehaviorOptions`.\n     * @see {@link NavigationBehaviorOptions#replaceUrl}\n     * @see {@link Router#navigateByUrl}\n     */\n    replaceUrl = false;\n    /**\n     * Modifies the tab index if there was not a tabindex attribute on the element during\n     * instantiation.\n     */\n    setTabIndexIfNotOnNativeEl(newTabIndex) {\n        if (this.tabIndexAttribute != null /* both `null` and `undefined` */ || this.isAnchorElement) {\n            return;\n        }\n        this.applyAttributeValue('tabindex', newTabIndex);\n    }\n    /** @docs-private */\n    // TODO(atscott): Remove changes parameter in major version as a breaking change.\n    ngOnChanges(changes) {\n        if (ngDevMode &&\n            isUrlTree(this.routerLinkInput) &&\n            (this.fragment !== undefined ||\n                this.queryParams ||\n                this.queryParamsHandling ||\n                this.preserveFragment ||\n                this.relativeTo)) {\n            throw new _RuntimeError(4016 /* RuntimeErrorCode.INVALID_ROUTER_LINK_INPUTS */, 'Cannot configure queryParams or fragment when using a UrlTree as the routerLink input value.');\n        }\n        if (this.isAnchorElement) {\n            this.updateHref();\n        }\n        // This is subscribed to by `RouterLinkActive` so that it knows to update when there are changes\n        // to the RouterLinks it's tracking.\n        this.onChanges.next(this);\n    }\n    routerLinkInput = null;\n    /**\n     * Commands to pass to {@link Router#createUrlTree} or a `UrlTree`.\n     *   - **array**: commands to pass to {@link Router#createUrlTree}.\n     *   - **string**: shorthand for array of commands with just the string, i.e. `['/route']`\n     *   - **UrlTree**: a `UrlTree` for this link rather than creating one from the commands\n     *     and other inputs that correspond to properties of `UrlCreationOptions`.\n     *   - **null|undefined**: effectively disables the `routerLink`\n     * @see {@link Router#createUrlTree}\n     */\n    set routerLink(commandsOrUrlTree) {\n        if (commandsOrUrlTree == null) {\n            this.routerLinkInput = null;\n            this.setTabIndexIfNotOnNativeEl(null);\n        }\n        else {\n            if (isUrlTree(commandsOrUrlTree)) {\n                this.routerLinkInput = commandsOrUrlTree;\n            }\n            else {\n                this.routerLinkInput = Array.isArray(commandsOrUrlTree)\n                    ? commandsOrUrlTree\n                    : [commandsOrUrlTree];\n            }\n            this.setTabIndexIfNotOnNativeEl('0');\n        }\n    }\n    /** @docs-private */\n    onClick(button, ctrlKey, shiftKey, altKey, metaKey) {\n        const urlTree = this.urlTree;\n        if (urlTree === null) {\n            return true;\n        }\n        if (this.isAnchorElement) {\n            if (button !== 0 || ctrlKey || shiftKey || altKey || metaKey) {\n                return true;\n            }\n            if (typeof this.target === 'string' && this.target != '_self') {\n                return true;\n            }\n        }\n        const extras = {\n            skipLocationChange: this.skipLocationChange,\n            replaceUrl: this.replaceUrl,\n            state: this.state,\n            info: this.info,\n        };\n        this.router.navigateByUrl(urlTree, extras);\n        // Return `false` for `<a>` elements to prevent default action\n        // and cancel the native behavior, since the navigation is handled\n        // by the Router.\n        return !this.isAnchorElement;\n    }\n    /** @docs-private */\n    ngOnDestroy() {\n        this.subscription?.unsubscribe();\n    }\n    updateHref() {\n        const urlTree = this.urlTree;\n        this.href =\n            urlTree !== null && this.locationStrategy\n                ? this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(urlTree))\n                : null;\n        const sanitizedValue = this.href === null\n            ? null\n            : // This class represents a directive that can be added to both `<a>` elements,\n                // as well as other elements. As a result, we can't define security context at\n                // compile time. So the security context is deferred to runtime.\n                // The `ɵɵsanitizeUrlOrResourceUrl` selects the necessary sanitizer function\n                // based on the tag and property names. The logic mimics the one from\n                // `packages/compiler/src/schema/dom_security_schema.ts`, which is used at compile time.\n                //\n                // Note: we should investigate whether we can switch to using `@HostBinding('attr.href')`\n                // instead of applying a value via a renderer, after a final merge of the\n                // `RouterLinkWithHref` directive.\n                __sanitizeUrlOrResourceUrl(this.href, this.el.nativeElement.tagName.toLowerCase(), 'href');\n        this.applyAttributeValue('href', sanitizedValue);\n    }\n    applyAttributeValue(attrName, attrValue) {\n        const renderer = this.renderer;\n        const nativeElement = this.el.nativeElement;\n        if (attrValue !== null) {\n            renderer.setAttribute(nativeElement, attrName, attrValue);\n        }\n        else {\n            renderer.removeAttribute(nativeElement, attrName);\n        }\n    }\n    get urlTree() {\n        if (this.routerLinkInput === null) {\n            return null;\n        }\n        else if (isUrlTree(this.routerLinkInput)) {\n            return this.routerLinkInput;\n        }\n        return this.router.createUrlTree(this.routerLinkInput, {\n            // If the `relativeTo` input is not defined, we want to use `this.route` by default.\n            // Otherwise, we should use the value provided by the user in the input.\n            relativeTo: this.relativeTo !== undefined ? this.relativeTo : this.route,\n            queryParams: this.queryParams,\n            fragment: this.fragment,\n            queryParamsHandling: this.queryParamsHandling,\n            preserveFragment: this.preserveFragment,\n        });\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: RouterLink, deps: [{ token: Router }, { token: ActivatedRoute }, { token: 'tabindex', attribute: true }, { token: i0.Renderer2 }, { token: i0.ElementRef }, { token: i3.LocationStrategy }], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"19.2.14\", type: RouterLink, isStandalone: true, selector: \"[routerLink]\", inputs: { target: \"target\", queryParams: \"queryParams\", fragment: \"fragment\", queryParamsHandling: \"queryParamsHandling\", state: \"state\", info: \"info\", relativeTo: \"relativeTo\", preserveFragment: [\"preserveFragment\", \"preserveFragment\", booleanAttribute], skipLocationChange: [\"skipLocationChange\", \"skipLocationChange\", booleanAttribute], replaceUrl: [\"replaceUrl\", \"replaceUrl\", booleanAttribute], routerLink: \"routerLink\" }, host: { listeners: { \"click\": \"onClick($event.button,$event.ctrlKey,$event.shiftKey,$event.altKey,$event.metaKey)\" }, properties: { \"attr.target\": \"this.target\" } }, usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: RouterLink, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[routerLink]',\n                }]\n        }], ctorParameters: () => [{ type: Router }, { type: ActivatedRoute }, { type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['tabindex']\n                }] }, { type: i0.Renderer2 }, { type: i0.ElementRef }, { type: i3.LocationStrategy }], propDecorators: { target: [{\n                type: HostBinding,\n                args: ['attr.target']\n            }, {\n                type: Input\n            }], queryParams: [{\n                type: Input\n            }], fragment: [{\n                type: Input\n            }], queryParamsHandling: [{\n                type: Input\n            }], state: [{\n                type: Input\n            }], info: [{\n                type: Input\n            }], relativeTo: [{\n                type: Input\n            }], preserveFragment: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], skipLocationChange: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], replaceUrl: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], routerLink: [{\n                type: Input\n            }], onClick: [{\n                type: HostListener,\n                args: ['click', [\n                        '$event.button',\n                        '$event.ctrlKey',\n                        '$event.shiftKey',\n                        '$event.altKey',\n                        '$event.metaKey',\n                    ]]\n            }] } });\n\n/**\n *\n * @description\n *\n * Tracks whether the linked route of an element is currently active, and allows you\n * to specify one or more CSS classes to add to the element when the linked route\n * is active.\n *\n * Use this directive to create a visual distinction for elements associated with an active route.\n * For example, the following code highlights the word \"Bob\" when the router\n * activates the associated route:\n *\n * ```html\n * <a routerLink=\"/user/bob\" routerLinkActive=\"active-link\">Bob</a>\n * ```\n *\n * Whenever the URL is either '/user' or '/user/bob', the \"active-link\" class is\n * added to the anchor tag. If the URL changes, the class is removed.\n *\n * You can set more than one class using a space-separated string or an array.\n * For example:\n *\n * ```html\n * <a routerLink=\"/user/bob\" routerLinkActive=\"class1 class2\">Bob</a>\n * <a routerLink=\"/user/bob\" [routerLinkActive]=\"['class1', 'class2']\">Bob</a>\n * ```\n *\n * To add the classes only when the URL matches the link exactly, add the option `exact: true`:\n *\n * ```html\n * <a routerLink=\"/user/bob\" routerLinkActive=\"active-link\" [routerLinkActiveOptions]=\"{exact:\n * true}\">Bob</a>\n * ```\n *\n * To directly check the `isActive` status of the link, assign the `RouterLinkActive`\n * instance to a template variable.\n * For example, the following checks the status without assigning any CSS classes:\n *\n * ```html\n * <a routerLink=\"/user/bob\" routerLinkActive #rla=\"routerLinkActive\">\n *   Bob {{ rla.isActive ? '(already open)' : ''}}\n * </a>\n * ```\n *\n * You can apply the `RouterLinkActive` directive to an ancestor of linked elements.\n * For example, the following sets the active-link class on the `<div>`  parent tag\n * when the URL is either '/user/jim' or '/user/bob'.\n *\n * ```html\n * <div routerLinkActive=\"active-link\" [routerLinkActiveOptions]=\"{exact: true}\">\n *   <a routerLink=\"/user/jim\">Jim</a>\n *   <a routerLink=\"/user/bob\">Bob</a>\n * </div>\n * ```\n *\n * The `RouterLinkActive` directive can also be used to set the aria-current attribute\n * to provide an alternative distinction for active elements to visually impaired users.\n *\n * For example, the following code adds the 'active' class to the Home Page link when it is\n * indeed active and in such case also sets its aria-current attribute to 'page':\n *\n * ```html\n * <a routerLink=\"/\" routerLinkActive=\"active\" ariaCurrentWhenActive=\"page\">Home Page</a>\n * ```\n *\n * @ngModule RouterModule\n *\n * @publicApi\n */\nclass RouterLinkActive {\n    router;\n    element;\n    renderer;\n    cdr;\n    link;\n    links;\n    classes = [];\n    routerEventsSubscription;\n    linkInputChangesSubscription;\n    _isActive = false;\n    get isActive() {\n        return this._isActive;\n    }\n    /**\n     * Options to configure how to determine if the router link is active.\n     *\n     * These options are passed to the `Router.isActive()` function.\n     *\n     * @see {@link Router#isActive}\n     */\n    routerLinkActiveOptions = { exact: false };\n    /**\n     * Aria-current attribute to apply when the router link is active.\n     *\n     * Possible values: `'page'` | `'step'` | `'location'` | `'date'` | `'time'` | `true` | `false`.\n     *\n     * @see {@link https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Attributes/aria-current}\n     */\n    ariaCurrentWhenActive;\n    /**\n     *\n     * You can use the output `isActiveChange` to get notified each time the link becomes\n     * active or inactive.\n     *\n     * Emits:\n     * true  -> Route is active\n     * false -> Route is inactive\n     *\n     * ```html\n     * <a\n     *  routerLink=\"/user/bob\"\n     *  routerLinkActive=\"active-link\"\n     *  (isActiveChange)=\"this.onRouterLinkActive($event)\">Bob</a>\n     * ```\n     */\n    isActiveChange = new EventEmitter();\n    constructor(router, element, renderer, cdr, link) {\n        this.router = router;\n        this.element = element;\n        this.renderer = renderer;\n        this.cdr = cdr;\n        this.link = link;\n        this.routerEventsSubscription = router.events.subscribe((s) => {\n            if (s instanceof NavigationEnd) {\n                this.update();\n            }\n        });\n    }\n    /** @docs-private */\n    ngAfterContentInit() {\n        // `of(null)` is used to force subscribe body to execute once immediately (like `startWith`).\n        of(this.links.changes, of(null))\n            .pipe(mergeAll())\n            .subscribe((_) => {\n            this.update();\n            this.subscribeToEachLinkOnChanges();\n        });\n    }\n    subscribeToEachLinkOnChanges() {\n        this.linkInputChangesSubscription?.unsubscribe();\n        const allLinkChanges = [...this.links.toArray(), this.link]\n            .filter((link) => !!link)\n            .map((link) => link.onChanges);\n        this.linkInputChangesSubscription = from(allLinkChanges)\n            .pipe(mergeAll())\n            .subscribe((link) => {\n            if (this._isActive !== this.isLinkActive(this.router)(link)) {\n                this.update();\n            }\n        });\n    }\n    set routerLinkActive(data) {\n        const classes = Array.isArray(data) ? data : data.split(' ');\n        this.classes = classes.filter((c) => !!c);\n    }\n    /** @docs-private */\n    ngOnChanges(changes) {\n        this.update();\n    }\n    /** @docs-private */\n    ngOnDestroy() {\n        this.routerEventsSubscription.unsubscribe();\n        this.linkInputChangesSubscription?.unsubscribe();\n    }\n    update() {\n        if (!this.links || !this.router.navigated)\n            return;\n        queueMicrotask(() => {\n            const hasActiveLinks = this.hasActiveLinks();\n            this.classes.forEach((c) => {\n                if (hasActiveLinks) {\n                    this.renderer.addClass(this.element.nativeElement, c);\n                }\n                else {\n                    this.renderer.removeClass(this.element.nativeElement, c);\n                }\n            });\n            if (hasActiveLinks && this.ariaCurrentWhenActive !== undefined) {\n                this.renderer.setAttribute(this.element.nativeElement, 'aria-current', this.ariaCurrentWhenActive.toString());\n            }\n            else {\n                this.renderer.removeAttribute(this.element.nativeElement, 'aria-current');\n            }\n            // Only emit change if the active state changed.\n            if (this._isActive !== hasActiveLinks) {\n                this._isActive = hasActiveLinks;\n                this.cdr.markForCheck();\n                // Emit on isActiveChange after classes are updated\n                this.isActiveChange.emit(hasActiveLinks);\n            }\n        });\n    }\n    isLinkActive(router) {\n        const options = isActiveMatchOptions(this.routerLinkActiveOptions)\n            ? this.routerLinkActiveOptions\n            : // While the types should disallow `undefined` here, it's possible without strict inputs\n                this.routerLinkActiveOptions.exact || false;\n        return (link) => {\n            const urlTree = link.urlTree;\n            return urlTree ? router.isActive(urlTree, options) : false;\n        };\n    }\n    hasActiveLinks() {\n        const isActiveCheckFn = this.isLinkActive(this.router);\n        return (this.link && isActiveCheckFn(this.link)) || this.links.some(isActiveCheckFn);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: RouterLinkActive, deps: [{ token: Router }, { token: i0.ElementRef }, { token: i0.Renderer2 }, { token: i0.ChangeDetectorRef }, { token: RouterLink, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.14\", type: RouterLinkActive, isStandalone: true, selector: \"[routerLinkActive]\", inputs: { routerLinkActiveOptions: \"routerLinkActiveOptions\", ariaCurrentWhenActive: \"ariaCurrentWhenActive\", routerLinkActive: \"routerLinkActive\" }, outputs: { isActiveChange: \"isActiveChange\" }, queries: [{ propertyName: \"links\", predicate: RouterLink, descendants: true }], exportAs: [\"routerLinkActive\"], usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: RouterLinkActive, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[routerLinkActive]',\n                    exportAs: 'routerLinkActive',\n                }]\n        }], ctorParameters: () => [{ type: Router }, { type: i0.ElementRef }, { type: i0.Renderer2 }, { type: i0.ChangeDetectorRef }, { type: RouterLink, decorators: [{\n                    type: Optional\n                }] }], propDecorators: { links: [{\n                type: ContentChildren,\n                args: [RouterLink, { descendants: true }]\n            }], routerLinkActiveOptions: [{\n                type: Input\n            }], ariaCurrentWhenActive: [{\n                type: Input\n            }], isActiveChange: [{\n                type: Output\n            }], routerLinkActive: [{\n                type: Input\n            }] } });\n/**\n * Use instead of `'paths' in options` to be compatible with property renaming\n */\nfunction isActiveMatchOptions(options) {\n    return !!options.paths;\n}\n\n/**\n * @description\n *\n * Provides a preloading strategy.\n *\n * @publicApi\n */\nclass PreloadingStrategy {\n}\n/**\n * @description\n *\n * Provides a preloading strategy that preloads all modules as quickly as possible.\n *\n * ```ts\n * RouterModule.forRoot(ROUTES, {preloadingStrategy: PreloadAllModules})\n * ```\n *\n * @publicApi\n */\nclass PreloadAllModules {\n    preload(route, fn) {\n        return fn().pipe(catchError(() => of(null)));\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: PreloadAllModules, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: PreloadAllModules, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: PreloadAllModules, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n/**\n * @description\n *\n * Provides a preloading strategy that does not preload any modules.\n *\n * This strategy is enabled by default.\n *\n * @publicApi\n */\nclass NoPreloading {\n    preload(route, fn) {\n        return of(null);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: NoPreloading, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: NoPreloading, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: NoPreloading, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n/**\n * The preloader optimistically loads all router configurations to\n * make navigations into lazily-loaded sections of the application faster.\n *\n * The preloader runs in the background. When the router bootstraps, the preloader\n * starts listening to all navigation events. After every such event, the preloader\n * will check if any configurations can be loaded lazily.\n *\n * If a route is protected by `canLoad` guards, the preloaded will not load it.\n *\n * @publicApi\n */\nclass RouterPreloader {\n    router;\n    injector;\n    preloadingStrategy;\n    loader;\n    subscription;\n    constructor(router, injector, preloadingStrategy, loader) {\n        this.router = router;\n        this.injector = injector;\n        this.preloadingStrategy = preloadingStrategy;\n        this.loader = loader;\n    }\n    setUpPreloading() {\n        this.subscription = this.router.events\n            .pipe(filter((e) => e instanceof NavigationEnd), concatMap(() => this.preload()))\n            .subscribe(() => { });\n    }\n    preload() {\n        return this.processRoutes(this.injector, this.router.config);\n    }\n    /** @docs-private */\n    ngOnDestroy() {\n        if (this.subscription) {\n            this.subscription.unsubscribe();\n        }\n    }\n    processRoutes(injector, routes) {\n        const res = [];\n        for (const route of routes) {\n            if (route.providers && !route._injector) {\n                route._injector = createEnvironmentInjector(route.providers, injector, `Route: ${route.path}`);\n            }\n            const injectorForCurrentRoute = route._injector ?? injector;\n            const injectorForChildren = route._loadedInjector ?? injectorForCurrentRoute;\n            // Note that `canLoad` is only checked as a condition that prevents `loadChildren` and not\n            // `loadComponent`. `canLoad` guards only block loading of child routes by design. This\n            // happens as a consequence of needing to descend into children for route matching immediately\n            // while component loading is deferred until route activation. Because `canLoad` guards can\n            // have side effects, we cannot execute them here so we instead skip preloading altogether\n            // when present. Lastly, it remains to be decided whether `canLoad` should behave this way\n            // at all. Code splitting and lazy loading is separate from client-side authorization checks\n            // and should not be used as a security measure to prevent loading of code.\n            if ((route.loadChildren && !route._loadedRoutes && route.canLoad === undefined) ||\n                (route.loadComponent && !route._loadedComponent)) {\n                res.push(this.preloadConfig(injectorForCurrentRoute, route));\n            }\n            if (route.children || route._loadedRoutes) {\n                res.push(this.processRoutes(injectorForChildren, (route.children ?? route._loadedRoutes)));\n            }\n        }\n        return from(res).pipe(mergeAll());\n    }\n    preloadConfig(injector, route) {\n        return this.preloadingStrategy.preload(route, () => {\n            let loadedChildren$;\n            if (route.loadChildren && route.canLoad === undefined) {\n                loadedChildren$ = this.loader.loadChildren(injector, route);\n            }\n            else {\n                loadedChildren$ = of(null);\n            }\n            const recursiveLoadChildren$ = loadedChildren$.pipe(mergeMap((config) => {\n                if (config === null) {\n                    return of(void 0);\n                }\n                route._loadedRoutes = config.routes;\n                route._loadedInjector = config.injector;\n                // If the loaded config was a module, use that as the module/module injector going\n                // forward. Otherwise, continue using the current module/module injector.\n                return this.processRoutes(config.injector ?? injector, config.routes);\n            }));\n            if (route.loadComponent && !route._loadedComponent) {\n                const loadComponent$ = this.loader.loadComponent(route);\n                return from([recursiveLoadChildren$, loadComponent$]).pipe(mergeAll());\n            }\n            else {\n                return recursiveLoadChildren$;\n            }\n        });\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: RouterPreloader, deps: [{ token: Router }, { token: i0.EnvironmentInjector }, { token: PreloadingStrategy }, { token: RouterConfigLoader }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: RouterPreloader, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: RouterPreloader, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: Router }, { type: i0.EnvironmentInjector }, { type: PreloadingStrategy }, { type: RouterConfigLoader }] });\n\nconst ROUTER_SCROLLER = new InjectionToken('');\nclass RouterScroller {\n    urlSerializer;\n    transitions;\n    viewportScroller;\n    zone;\n    options;\n    routerEventsSubscription;\n    scrollEventsSubscription;\n    lastId = 0;\n    lastSource = 'imperative';\n    restoredId = 0;\n    store = {};\n    /** @docs-private */\n    constructor(urlSerializer, transitions, viewportScroller, zone, options = {}) {\n        this.urlSerializer = urlSerializer;\n        this.transitions = transitions;\n        this.viewportScroller = viewportScroller;\n        this.zone = zone;\n        this.options = options;\n        // Default both options to 'disabled'\n        options.scrollPositionRestoration ||= 'disabled';\n        options.anchorScrolling ||= 'disabled';\n    }\n    init() {\n        // we want to disable the automatic scrolling because having two places\n        // responsible for scrolling results race conditions, especially given\n        // that browser don't implement this behavior consistently\n        if (this.options.scrollPositionRestoration !== 'disabled') {\n            this.viewportScroller.setHistoryScrollRestoration('manual');\n        }\n        this.routerEventsSubscription = this.createScrollEvents();\n        this.scrollEventsSubscription = this.consumeScrollEvents();\n    }\n    createScrollEvents() {\n        return this.transitions.events.subscribe((e) => {\n            if (e instanceof NavigationStart) {\n                // store the scroll position of the current stable navigations.\n                this.store[this.lastId] = this.viewportScroller.getScrollPosition();\n                this.lastSource = e.navigationTrigger;\n                this.restoredId = e.restoredState ? e.restoredState.navigationId : 0;\n            }\n            else if (e instanceof NavigationEnd) {\n                this.lastId = e.id;\n                this.scheduleScrollEvent(e, this.urlSerializer.parse(e.urlAfterRedirects).fragment);\n            }\n            else if (e instanceof NavigationSkipped &&\n                e.code === NavigationSkippedCode.IgnoredSameUrlNavigation) {\n                this.lastSource = undefined;\n                this.restoredId = 0;\n                this.scheduleScrollEvent(e, this.urlSerializer.parse(e.url).fragment);\n            }\n        });\n    }\n    consumeScrollEvents() {\n        return this.transitions.events.subscribe((e) => {\n            if (!(e instanceof Scroll))\n                return;\n            // a popstate event. The pop state event will always ignore anchor scrolling.\n            if (e.position) {\n                if (this.options.scrollPositionRestoration === 'top') {\n                    this.viewportScroller.scrollToPosition([0, 0]);\n                }\n                else if (this.options.scrollPositionRestoration === 'enabled') {\n                    this.viewportScroller.scrollToPosition(e.position);\n                }\n                // imperative navigation \"forward\"\n            }\n            else {\n                if (e.anchor && this.options.anchorScrolling === 'enabled') {\n                    this.viewportScroller.scrollToAnchor(e.anchor);\n                }\n                else if (this.options.scrollPositionRestoration !== 'disabled') {\n                    this.viewportScroller.scrollToPosition([0, 0]);\n                }\n            }\n        });\n    }\n    scheduleScrollEvent(routerEvent, anchor) {\n        this.zone.runOutsideAngular(() => {\n            // The scroll event needs to be delayed until after change detection. Otherwise, we may\n            // attempt to restore the scroll position before the router outlet has fully rendered the\n            // component by executing its update block of the template function.\n            setTimeout(() => {\n                this.zone.run(() => {\n                    this.transitions.events.next(new Scroll(routerEvent, this.lastSource === 'popstate' ? this.store[this.restoredId] : null, anchor));\n                });\n            }, 0);\n        });\n    }\n    /** @docs-private */\n    ngOnDestroy() {\n        this.routerEventsSubscription?.unsubscribe();\n        this.scrollEventsSubscription?.unsubscribe();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: RouterScroller, deps: \"invalid\", target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: RouterScroller });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: RouterScroller, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: UrlSerializer }, { type: NavigationTransitions }, { type: i3.ViewportScroller }, { type: i0.NgZone }, { type: undefined }] });\n\n/**\n * Sets up providers necessary to enable `Router` functionality for the application.\n * Allows to configure a set of routes as well as extra features that should be enabled.\n *\n * @usageNotes\n *\n * Basic example of how you can add a Router to your application:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent, {\n *   providers: [provideRouter(appRoutes)]\n * });\n * ```\n *\n * You can also enable optional features in the Router by adding functions from the `RouterFeatures`\n * type:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes,\n *         withDebugTracing(),\n *         withRouterConfig({paramsInheritanceStrategy: 'always'}))\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link RouterFeatures}\n *\n * @publicApi\n * @param routes A set of `Route`s to use for the application routing table.\n * @param features Optional features to configure additional router behaviors.\n * @returns A set of providers to setup a Router.\n */\nfunction provideRouter(routes, ...features) {\n    return makeEnvironmentProviders([\n        { provide: ROUTES, multi: true, useValue: routes },\n        typeof ngDevMode === 'undefined' || ngDevMode\n            ? { provide: ROUTER_IS_PROVIDED, useValue: true }\n            : [],\n        { provide: ActivatedRoute, useFactory: rootRoute, deps: [Router] },\n        { provide: APP_BOOTSTRAP_LISTENER, multi: true, useFactory: getBootstrapListener },\n        features.map((feature) => feature.ɵproviders),\n    ]);\n}\nfunction rootRoute(router) {\n    return router.routerState.root;\n}\n/**\n * Helper function to create an object that represents a Router feature.\n */\nfunction routerFeature(kind, providers) {\n    return { ɵkind: kind, ɵproviders: providers };\n}\n/**\n * An Injection token used to indicate whether `provideRouter` or `RouterModule.forRoot` was ever\n * called.\n */\nconst ROUTER_IS_PROVIDED = new InjectionToken('', {\n    providedIn: 'root',\n    factory: () => false,\n});\nconst routerIsProvidedDevModeCheck = {\n    provide: ENVIRONMENT_INITIALIZER,\n    multi: true,\n    useFactory() {\n        return () => {\n            if (!inject(ROUTER_IS_PROVIDED)) {\n                console.warn('`provideRoutes` was called without `provideRouter` or `RouterModule.forRoot`. ' +\n                    'This is likely a mistake.');\n            }\n        };\n    },\n};\n/**\n * Registers a DI provider for a set of routes.\n * @param routes The route configuration to provide.\n *\n * @usageNotes\n *\n * ```ts\n * @NgModule({\n *   providers: [provideRoutes(ROUTES)]\n * })\n * class LazyLoadedChildModule {}\n * ```\n *\n * @deprecated If necessary, provide routes using the `ROUTES` `InjectionToken`.\n * @see {@link ROUTES}\n * @publicApi\n */\nfunction provideRoutes(routes) {\n    return [\n        { provide: ROUTES, multi: true, useValue: routes },\n        typeof ngDevMode === 'undefined' || ngDevMode ? routerIsProvidedDevModeCheck : [],\n    ];\n}\n/**\n * Enables customizable scrolling behavior for router navigations.\n *\n * @usageNotes\n *\n * Basic example of how you can enable scrolling feature:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withInMemoryScrolling())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n * @see {@link ViewportScroller}\n *\n * @publicApi\n * @param options Set of configuration parameters to customize scrolling behavior, see\n *     `InMemoryScrollingOptions` for additional information.\n * @returns A set of providers for use with `provideRouter`.\n */\nfunction withInMemoryScrolling(options = {}) {\n    const providers = [\n        {\n            provide: ROUTER_SCROLLER,\n            useFactory: () => {\n                const viewportScroller = inject(ViewportScroller);\n                const zone = inject(NgZone);\n                const transitions = inject(NavigationTransitions);\n                const urlSerializer = inject(UrlSerializer);\n                return new RouterScroller(urlSerializer, transitions, viewportScroller, zone, options);\n            },\n        },\n    ];\n    return routerFeature(4 /* RouterFeatureKind.InMemoryScrollingFeature */, providers);\n}\nfunction getBootstrapListener() {\n    const injector = inject(Injector);\n    return (bootstrappedComponentRef) => {\n        const ref = injector.get(ApplicationRef);\n        if (bootstrappedComponentRef !== ref.components[0]) {\n            return;\n        }\n        const router = injector.get(Router);\n        const bootstrapDone = injector.get(BOOTSTRAP_DONE);\n        if (injector.get(INITIAL_NAVIGATION) === 1 /* InitialNavigation.EnabledNonBlocking */) {\n            router.initialNavigation();\n        }\n        injector.get(ROUTER_PRELOADER, null, InjectFlags.Optional)?.setUpPreloading();\n        injector.get(ROUTER_SCROLLER, null, InjectFlags.Optional)?.init();\n        router.resetRootComponentType(ref.componentTypes[0]);\n        if (!bootstrapDone.closed) {\n            bootstrapDone.next();\n            bootstrapDone.complete();\n            bootstrapDone.unsubscribe();\n        }\n    };\n}\n/**\n * A subject used to indicate that the bootstrapping phase is done. When initial navigation is\n * `enabledBlocking`, the first navigation waits until bootstrapping is finished before continuing\n * to the activation phase.\n */\nconst BOOTSTRAP_DONE = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'bootstrap done indicator' : '', {\n    factory: () => {\n        return new Subject();\n    },\n});\nconst INITIAL_NAVIGATION = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'initial navigation' : '', { providedIn: 'root', factory: () => 1 /* InitialNavigation.EnabledNonBlocking */ });\n/**\n * Configures initial navigation to start before the root component is created.\n *\n * The bootstrap is blocked until the initial navigation is complete. This should be set in case\n * you use [server-side rendering](guide/ssr), but do not enable [hydration](guide/hydration) for\n * your application.\n *\n * @usageNotes\n *\n * Basic example of how you can enable this navigation behavior:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withEnabledBlockingInitialNavigation())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @publicApi\n * @returns A set of providers for use with `provideRouter`.\n */\nfunction withEnabledBlockingInitialNavigation() {\n    const providers = [\n        { provide: INITIAL_NAVIGATION, useValue: 0 /* InitialNavigation.EnabledBlocking */ },\n        provideAppInitializer(() => {\n            const injector = inject(Injector);\n            const locationInitialized = injector.get(LOCATION_INITIALIZED, Promise.resolve());\n            return locationInitialized.then(() => {\n                return new Promise((resolve) => {\n                    const router = injector.get(Router);\n                    const bootstrapDone = injector.get(BOOTSTRAP_DONE);\n                    afterNextNavigation(router, () => {\n                        // Unblock APP_INITIALIZER in case the initial navigation was canceled or errored\n                        // without a redirect.\n                        resolve(true);\n                    });\n                    injector.get(NavigationTransitions).afterPreactivation = () => {\n                        // Unblock APP_INITIALIZER once we get to `afterPreactivation`. At this point, we\n                        // assume activation will complete successfully (even though this is not\n                        // guaranteed).\n                        resolve(true);\n                        return bootstrapDone.closed ? of(void 0) : bootstrapDone;\n                    };\n                    router.initialNavigation();\n                });\n            });\n        }),\n    ];\n    return routerFeature(2 /* RouterFeatureKind.EnabledBlockingInitialNavigationFeature */, providers);\n}\n/**\n * Disables initial navigation.\n *\n * Use if there is a reason to have more control over when the router starts its initial navigation\n * due to some complex initialization logic.\n *\n * @usageNotes\n *\n * Basic example of how you can disable initial navigation:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withDisabledInitialNavigation())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withDisabledInitialNavigation() {\n    const providers = [\n        provideAppInitializer(() => {\n            inject(Router).setUpLocationChangeListener();\n        }),\n        { provide: INITIAL_NAVIGATION, useValue: 2 /* InitialNavigation.Disabled */ },\n    ];\n    return routerFeature(3 /* RouterFeatureKind.DisabledInitialNavigationFeature */, providers);\n}\n/**\n * Enables logging of all internal navigation events to the console.\n * Extra logging might be useful for debugging purposes to inspect Router event sequence.\n *\n * @usageNotes\n *\n * Basic example of how you can enable debug tracing:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withDebugTracing())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withDebugTracing() {\n    let providers = [];\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        providers = [\n            {\n                provide: ENVIRONMENT_INITIALIZER,\n                multi: true,\n                useFactory: () => {\n                    const router = inject(Router);\n                    return () => router.events.subscribe((e) => {\n                        // tslint:disable:no-console\n                        console.group?.(`Router Event: ${e.constructor.name}`);\n                        console.log(stringifyEvent(e));\n                        console.log(e);\n                        console.groupEnd?.();\n                        // tslint:enable:no-console\n                    });\n                },\n            },\n        ];\n    }\n    else {\n        providers = [];\n    }\n    return routerFeature(1 /* RouterFeatureKind.DebugTracingFeature */, providers);\n}\nconst ROUTER_PRELOADER = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'router preloader' : '');\n/**\n * Allows to configure a preloading strategy to use. The strategy is configured by providing a\n * reference to a class that implements a `PreloadingStrategy`.\n *\n * @usageNotes\n *\n * Basic example of how you can configure preloading:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withPreloading(PreloadAllModules))\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @param preloadingStrategy A reference to a class that implements a `PreloadingStrategy` that\n *     should be used.\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withPreloading(preloadingStrategy) {\n    const providers = [\n        { provide: ROUTER_PRELOADER, useExisting: RouterPreloader },\n        { provide: PreloadingStrategy, useExisting: preloadingStrategy },\n    ];\n    return routerFeature(0 /* RouterFeatureKind.PreloadingFeature */, providers);\n}\n/**\n * Allows to provide extra parameters to configure Router.\n *\n * @usageNotes\n *\n * Basic example of how you can provide extra configuration options:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withRouterConfig({\n *          onSameUrlNavigation: 'reload'\n *       }))\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n *\n * @param options A set of parameters to configure Router, see `RouterConfigOptions` for\n *     additional information.\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withRouterConfig(options) {\n    const providers = [{ provide: ROUTER_CONFIGURATION, useValue: options }];\n    return routerFeature(5 /* RouterFeatureKind.RouterConfigurationFeature */, providers);\n}\n/**\n * Provides the location strategy that uses the URL fragment instead of the history API.\n *\n * @usageNotes\n *\n * Basic example of how you can use the hash location option:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withHashLocation())\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link provideRouter}\n * @see {@link /api/common/HashLocationStrategy HashLocationStrategy}\n *\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withHashLocation() {\n    const providers = [{ provide: LocationStrategy, useClass: HashLocationStrategy }];\n    return routerFeature(6 /* RouterFeatureKind.RouterHashLocationFeature */, providers);\n}\n/**\n * Provides a function which is called when a navigation error occurs.\n *\n * This function is run inside application's [injection context](guide/di/dependency-injection-context)\n * so you can use the [`inject`](api/core/inject) function.\n *\n * This function can return a `RedirectCommand` to convert the error to a redirect, similar to returning\n * a `UrlTree` or `RedirectCommand` from a guard. This will also prevent the `Router` from emitting\n * `NavigationError`; it will instead emit `NavigationCancel` with code NavigationCancellationCode.Redirect.\n * Return values other than `RedirectCommand` are ignored and do not change any behavior with respect to\n * how the `Router` handles the error.\n *\n * @usageNotes\n *\n * Basic example of how you can use the error handler option:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withNavigationErrorHandler((e: NavigationError) =>\n * inject(MyErrorTracker).trackError(e)))\n *     ]\n *   }\n * );\n * ```\n *\n * @see {@link NavigationError}\n * @see {@link /api/core/inject inject}\n * @see {@link runInInjectionContext}\n *\n * @returns A set of providers for use with `provideRouter`.\n *\n * @publicApi\n */\nfunction withNavigationErrorHandler(handler) {\n    const providers = [\n        {\n            provide: NAVIGATION_ERROR_HANDLER,\n            useValue: handler,\n        },\n    ];\n    return routerFeature(7 /* RouterFeatureKind.NavigationErrorHandlerFeature */, providers);\n}\n/**\n * Enables binding information from the `Router` state directly to the inputs of the component in\n * `Route` configurations.\n *\n * @usageNotes\n *\n * Basic example of how you can enable the feature:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withComponentInputBinding())\n *     ]\n *   }\n * );\n * ```\n *\n * The router bindings information from any of the following sources:\n *\n *  - query parameters\n *  - path and matrix parameters\n *  - static route data\n *  - data from resolvers\n *\n * Duplicate keys are resolved in the same order from above, from least to greatest,\n * meaning that resolvers have the highest precedence and override any of the other information\n * from the route.\n *\n * Importantly, when an input does not have an item in the route data with a matching key, this\n * input is set to `undefined`. This prevents previous information from being\n * retained if the data got removed from the route (i.e. if a query parameter is removed).\n * Default values can be provided with a resolver on the route to ensure the value is always present\n * or an input and use an input transform in the component.\n *\n * @see {@link /guide/components/inputs#input-transforms Input Transforms}\n * @returns A set of providers for use with `provideRouter`.\n */\nfunction withComponentInputBinding() {\n    const providers = [\n        RoutedComponentInputBinder,\n        { provide: INPUT_BINDER, useExisting: RoutedComponentInputBinder },\n    ];\n    return routerFeature(8 /* RouterFeatureKind.ComponentInputBindingFeature */, providers);\n}\n/**\n * Enables view transitions in the Router by running the route activation and deactivation inside of\n * `document.startViewTransition`.\n *\n * Note: The View Transitions API is not available in all browsers. If the browser does not support\n * view transitions, the Router will not attempt to start a view transition and continue processing\n * the navigation as usual.\n *\n * @usageNotes\n *\n * Basic example of how you can enable the feature:\n * ```ts\n * const appRoutes: Routes = [];\n * bootstrapApplication(AppComponent,\n *   {\n *     providers: [\n *       provideRouter(appRoutes, withViewTransitions())\n *     ]\n *   }\n * );\n * ```\n *\n * @returns A set of providers for use with `provideRouter`.\n * @see https://developer.chrome.com/docs/web-platform/view-transitions/\n * @see https://developer.mozilla.org/en-US/docs/Web/API/View_Transitions_API\n * @developerPreview\n */\nfunction withViewTransitions(options) {\n    _performanceMarkFeature('NgRouterViewTransitions');\n    const providers = [\n        { provide: CREATE_VIEW_TRANSITION, useValue: createViewTransition },\n        {\n            provide: VIEW_TRANSITION_OPTIONS,\n            useValue: { skipNextTransition: !!options?.skipInitialTransition, ...options },\n        },\n    ];\n    return routerFeature(9 /* RouterFeatureKind.ViewTransitionsFeature */, providers);\n}\n\n/**\n * The directives defined in the `RouterModule`.\n */\nconst ROUTER_DIRECTIVES = [RouterOutlet, RouterLink, RouterLinkActive, _EmptyOutletComponent];\n/**\n * @docsNotRequired\n */\nconst ROUTER_FORROOT_GUARD = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'router duplicate forRoot guard' : '');\n// TODO(atscott): All of these except `ActivatedRoute` are `providedIn: 'root'`. They are only kept\n// here to avoid a breaking change whereby the provider order matters based on where the\n// `RouterModule`/`RouterTestingModule` is imported. These can/should be removed as a \"breaking\"\n// change in a major version.\nconst ROUTER_PROVIDERS = [\n    Location,\n    { provide: UrlSerializer, useClass: DefaultUrlSerializer },\n    Router,\n    ChildrenOutletContexts,\n    { provide: ActivatedRoute, useFactory: rootRoute, deps: [Router] },\n    RouterConfigLoader,\n    // Only used to warn when `provideRoutes` is used without `RouterModule` or `provideRouter`. Can\n    // be removed when `provideRoutes` is removed.\n    typeof ngDevMode === 'undefined' || ngDevMode\n        ? { provide: ROUTER_IS_PROVIDED, useValue: true }\n        : [],\n];\n/**\n * @description\n *\n * Adds directives and providers for in-app navigation among views defined in an application.\n * Use the Angular `Router` service to declaratively specify application states and manage state\n * transitions.\n *\n * You can import this NgModule multiple times, once for each lazy-loaded bundle.\n * However, only one `Router` service can be active.\n * To ensure this, there are two ways to register routes when importing this module:\n *\n * * The `forRoot()` method creates an `NgModule` that contains all the directives, the given\n * routes, and the `Router` service itself.\n * * The `forChild()` method creates an `NgModule` that contains all the directives and the given\n * routes, but does not include the `Router` service.\n *\n * @see [Routing and Navigation guide](guide/routing/common-router-tasks) for an\n * overview of how the `Router` service should be used.\n *\n * @publicApi\n */\nclass RouterModule {\n    constructor() {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            inject(ROUTER_FORROOT_GUARD, { optional: true });\n        }\n    }\n    /**\n     * Creates and configures a module with all the router providers and directives.\n     * Optionally sets up an application listener to perform an initial navigation.\n     *\n     * When registering the NgModule at the root, import as follows:\n     *\n     * ```ts\n     * @NgModule({\n     *   imports: [RouterModule.forRoot(ROUTES)]\n     * })\n     * class MyNgModule {}\n     * ```\n     *\n     * @param routes An array of `Route` objects that define the navigation paths for the application.\n     * @param config An `ExtraOptions` configuration object that controls how navigation is performed.\n     * @return The new `NgModule`.\n     *\n     */\n    static forRoot(routes, config) {\n        return {\n            ngModule: RouterModule,\n            providers: [\n                ROUTER_PROVIDERS,\n                typeof ngDevMode === 'undefined' || ngDevMode\n                    ? config?.enableTracing\n                        ? withDebugTracing().ɵproviders\n                        : []\n                    : [],\n                { provide: ROUTES, multi: true, useValue: routes },\n                typeof ngDevMode === 'undefined' || ngDevMode\n                    ? {\n                        provide: ROUTER_FORROOT_GUARD,\n                        useFactory: provideForRootGuard,\n                        deps: [[Router, new Optional(), new SkipSelf()]],\n                    }\n                    : [],\n                config?.errorHandler\n                    ? {\n                        provide: NAVIGATION_ERROR_HANDLER,\n                        useValue: config.errorHandler,\n                    }\n                    : [],\n                { provide: ROUTER_CONFIGURATION, useValue: config ? config : {} },\n                config?.useHash ? provideHashLocationStrategy() : providePathLocationStrategy(),\n                provideRouterScroller(),\n                config?.preloadingStrategy ? withPreloading(config.preloadingStrategy).ɵproviders : [],\n                config?.initialNavigation ? provideInitialNavigation(config) : [],\n                config?.bindToComponentInputs ? withComponentInputBinding().ɵproviders : [],\n                config?.enableViewTransitions ? withViewTransitions().ɵproviders : [],\n                provideRouterInitializer(),\n            ],\n        };\n    }\n    /**\n     * Creates a module with all the router directives and a provider registering routes,\n     * without creating a new Router service.\n     * When registering for submodules and lazy-loaded submodules, create the NgModule as follows:\n     *\n     * ```ts\n     * @NgModule({\n     *   imports: [RouterModule.forChild(ROUTES)]\n     * })\n     * class MyNgModule {}\n     * ```\n     *\n     * @param routes An array of `Route` objects that define the navigation paths for the submodule.\n     * @return The new NgModule.\n     *\n     */\n    static forChild(routes) {\n        return {\n            ngModule: RouterModule,\n            providers: [{ provide: ROUTES, multi: true, useValue: routes }],\n        };\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: RouterModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.14\", ngImport: i0, type: RouterModule, imports: [RouterOutlet, RouterLink, RouterLinkActive, _EmptyOutletComponent], exports: [RouterOutlet, RouterLink, RouterLinkActive, _EmptyOutletComponent] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: RouterModule });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: RouterModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: ROUTER_DIRECTIVES,\n                    exports: ROUTER_DIRECTIVES,\n                }]\n        }], ctorParameters: () => [] });\n/**\n * For internal use by `RouterModule` only. Note that this differs from `withInMemoryRouterScroller`\n * because it reads from the `ExtraOptions` which should not be used in the standalone world.\n */\nfunction provideRouterScroller() {\n    return {\n        provide: ROUTER_SCROLLER,\n        useFactory: () => {\n            const viewportScroller = inject(ViewportScroller);\n            const zone = inject(NgZone);\n            const config = inject(ROUTER_CONFIGURATION);\n            const transitions = inject(NavigationTransitions);\n            const urlSerializer = inject(UrlSerializer);\n            if (config.scrollOffset) {\n                viewportScroller.setOffset(config.scrollOffset);\n            }\n            return new RouterScroller(urlSerializer, transitions, viewportScroller, zone, config);\n        },\n    };\n}\n// Note: For internal use only with `RouterModule`. Standalone setup via `provideRouter` should\n// provide hash location directly via `{provide: LocationStrategy, useClass: HashLocationStrategy}`.\nfunction provideHashLocationStrategy() {\n    return { provide: LocationStrategy, useClass: HashLocationStrategy };\n}\n// Note: For internal use only with `RouterModule`. Standalone setup via `provideRouter` does not\n// need this at all because `PathLocationStrategy` is the default factory for `LocationStrategy`.\nfunction providePathLocationStrategy() {\n    return { provide: LocationStrategy, useClass: PathLocationStrategy };\n}\nfunction provideForRootGuard(router) {\n    if (router) {\n        throw new _RuntimeError(4007 /* RuntimeErrorCode.FOR_ROOT_CALLED_TWICE */, `The Router was provided more than once. This can happen if 'forRoot' is used outside of the root injector.` +\n            ` Lazy loaded modules should use RouterModule.forChild() instead.`);\n    }\n    return 'guarded';\n}\n// Note: For internal use only with `RouterModule`. Standalone router setup with `provideRouter`\n// users call `withXInitialNavigation` directly.\nfunction provideInitialNavigation(config) {\n    return [\n        config.initialNavigation === 'disabled' ? withDisabledInitialNavigation().ɵproviders : [],\n        config.initialNavigation === 'enabledBlocking'\n            ? withEnabledBlockingInitialNavigation().ɵproviders\n            : [],\n    ];\n}\n// TODO(atscott): This should not be in the public API\n/**\n * A DI token for the router initializer that\n * is called after the app is bootstrapped.\n *\n * @publicApi\n */\nconst ROUTER_INITIALIZER = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'Router Initializer' : '');\nfunction provideRouterInitializer() {\n    return [\n        // ROUTER_INITIALIZER token should be removed. It's public API but shouldn't be. We can just\n        // have `getBootstrapListener` directly attached to APP_BOOTSTRAP_LISTENER.\n        { provide: ROUTER_INITIALIZER, useFactory: getBootstrapListener },\n        { provide: APP_BOOTSTRAP_LISTENER, multi: true, useExisting: ROUTER_INITIALIZER },\n    ];\n}\n\nexport { NoPreloading, PreloadAllModules, PreloadingStrategy, ROUTER_INITIALIZER, ROUTER_PROVIDERS, RouterLink, RouterLinkActive, RouterModule, RouterPreloader, provideRouter, provideRoutes, withComponentInputBinding, withDebugTracing, withDisabledInitialNavigation, withEnabledBlockingInitialNavigation, withHashLocation, withInMemoryScrolling, withNavigationErrorHandler, withPreloading, withRouterConfig, withViewTransitions };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAO,KAAKA,EAAE,MAAM,iBAAiB;AACrC,SAASC,oBAAoB,EAAEC,oBAAoB,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEC,oBAAoB,QAAQ,iBAAiB;AAChJ,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,aAAa,IAAIC,aAAa,EAAEC,0BAA0B,IAAIC,0BAA0B,EAAEC,gBAAgB,EAAEC,YAAY,EAAEC,KAAK,EAAEC,WAAW,EAAEC,SAAS,EAAEC,SAAS,EAAEC,YAAY,EAAEC,MAAM,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,yBAAyB,EAAEC,UAAU,EAAEC,cAAc,EAAEC,uBAAuB,IAAIC,uBAAuB,EAAEC,wBAAwB,EAAEC,sBAAsB,EAAEC,uBAAuB,EAAEC,qBAAqB,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,WAAW,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,eAAe;AAC3gB,SAASC,aAAa,EAAEC,SAAS,EAAEC,MAAM,EAAEC,cAAc,EAAEC,kBAAkB,EAAEC,eAAe,EAAEC,iBAAiB,EAAEC,qBAAqB,EAAEC,MAAM,EAAEC,aAAa,EAAEC,qBAAqB,EAAEC,MAAM,EAAEC,mBAAmB,EAAEC,oBAAoB,EAAEC,wBAAwB,EAAEC,0BAA0B,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,sBAAsB,EAAEC,uBAAuB,EAAEC,cAAc,EAAEC,oBAAoB,EAAEC,sBAAsB,EAAEC,YAAY,EAAEC,qBAAqB,IAAIC,qBAAqB,QAAQ,uBAAuB;AAC/gB,SAASC,OAAO,EAAEC,EAAE,EAAEC,IAAI,QAAQ,MAAM;AACxC,SAASC,QAAQ,EAAEC,UAAU,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,gBAAgB;;AAElF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAnGA,IAoGMC,UAAU;EAAhB,MAAMA,UAAU,CAAC;IACbC,MAAM;IACNC,KAAK;IACLC,iBAAiB;IACjBC,QAAQ;IACRC,EAAE;IACFC,gBAAgB;IAChB;AACJ;AACA;AACA;IACIC,IAAI,GAAG,IAAI;IACX;AACJ;AACA;AACA;IACIC,MAAM;IACN;AACJ;AACA;AACA;AACA;AACA;IACIC,WAAW;IACX;AACJ;AACA;AACA;AACA;AACA;IACIC,QAAQ;IACR;AACJ;AACA;AACA;AACA;AACA;IACIC,mBAAmB;IACnB;AACJ;AACA;AACA;AACA;AACA;IACIC,KAAK;IACL;AACJ;AACA;AACA;AACA;AACA;IACIC,IAAI;IACJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACIC,UAAU;IACV;IACAC,eAAe;IACfC,YAAY;IACZ;IACAC,SAAS,GAAG,IAAIzB,OAAO,CAAC,CAAC;IACzB0B,WAAWA,CAACjB,MAAM,EAAEC,KAAK,EAAEC,iBAAiB,EAAEC,QAAQ,EAAEC,EAAE,EAAEC,gBAAgB,EAAE;MAC1E,IAAI,CAACL,MAAM,GAAGA,MAAM;MACpB,IAAI,CAACC,KAAK,GAAGA,KAAK;MAClB,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;MAC1C,IAAI,CAACC,QAAQ,GAAGA,QAAQ;MACxB,IAAI,CAACC,EAAE,GAAGA,EAAE;MACZ,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;MACxC,MAAMa,OAAO,GAAGd,EAAE,CAACe,aAAa,CAACD,OAAO,EAAEE,WAAW,CAAC,CAAC;MACvD,IAAI,CAACN,eAAe,GAAGI,OAAO,KAAK,GAAG,IAAIA,OAAO,KAAK,MAAM;MAC5D,IAAI,IAAI,CAACJ,eAAe,EAAE;QACtB,IAAI,CAACC,YAAY,GAAGf,MAAM,CAACqB,MAAM,CAACC,SAAS,CAAEC,CAAC,IAAK;UAC/C,IAAIA,CAAC,YAAY1D,aAAa,EAAE;YAC5B,IAAI,CAAC2D,UAAU,CAAC,CAAC;UACrB;QACJ,CAAC,CAAC;MACN,CAAC,MACI;QACD,IAAI,CAACC,0BAA0B,CAAC,GAAG,CAAC;MACxC;IACJ;IACA;AACJ;AACA;AACA;AACA;AACA;IACIC,gBAAgB,GAAG,KAAK;IACxB;AACJ;AACA;AACA;AACA;AACA;IACIC,kBAAkB,GAAG,KAAK;IAC1B;AACJ;AACA;AACA;AACA;AACA;IACIC,UAAU,GAAG,KAAK;IAClB;AACJ;AACA;AACA;IACIH,0BAA0BA,CAACI,WAAW,EAAE;MACpC,IAAI,IAAI,CAAC3B,iBAAiB,IAAI,IAAI,CAAC,qCAAqC,IAAI,CAACY,eAAe,EAAE;QAC1F;MACJ;MACA,IAAI,CAACgB,mBAAmB,CAAC,UAAU,EAAED,WAAW,CAAC;IACrD;IACA;IACA;IACAE,WAAWA,CAACC,OAAO,EAAE;MACjB,IAAIC,SAAS,IACTnE,SAAS,CAAC,IAAI,CAACoE,eAAe,CAAC,KAC9B,IAAI,CAACzB,QAAQ,KAAK0B,SAAS,IACxB,IAAI,CAAC3B,WAAW,IAChB,IAAI,CAACE,mBAAmB,IACxB,IAAI,CAACgB,gBAAgB,IACrB,IAAI,CAACb,UAAU,CAAC,EAAE;QACtB,MAAM,IAAI7E,aAAa,CAAC,IAAI,CAAC,mDAAmD,8FAA8F,CAAC;MACnL;MACA,IAAI,IAAI,CAAC8E,eAAe,EAAE;QACtB,IAAI,CAACU,UAAU,CAAC,CAAC;MACrB;MACA;MACA;MACA,IAAI,CAACR,SAAS,CAACoB,IAAI,CAAC,IAAI,CAAC;IAC7B;IACAF,eAAe,GAAG,IAAI;IACtB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAIG,UAAUA,CAACC,iBAAiB,EAAE;MAC9B,IAAIA,iBAAiB,IAAI,IAAI,EAAE;QAC3B,IAAI,CAACJ,eAAe,GAAG,IAAI;QAC3B,IAAI,CAACT,0BAA0B,CAAC,IAAI,CAAC;MACzC,CAAC,MACI;QACD,IAAI3D,SAAS,CAACwE,iBAAiB,CAAC,EAAE;UAC9B,IAAI,CAACJ,eAAe,GAAGI,iBAAiB;QAC5C,CAAC,MACI;UACD,IAAI,CAACJ,eAAe,GAAGK,KAAK,CAACC,OAAO,CAACF,iBAAiB,CAAC,GACjDA,iBAAiB,GACjB,CAACA,iBAAiB,CAAC;QAC7B;QACA,IAAI,CAACb,0BAA0B,CAAC,GAAG,CAAC;MACxC;IACJ;IACA;IACAgB,OAAOA,CAACC,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAE;MAChD,MAAMC,OAAO,GAAG,IAAI,CAACA,OAAO;MAC5B,IAAIA,OAAO,KAAK,IAAI,EAAE;QAClB,OAAO,IAAI;MACf;MACA,IAAI,IAAI,CAACjC,eAAe,EAAE;QACtB,IAAI4B,MAAM,KAAK,CAAC,IAAIC,OAAO,IAAIC,QAAQ,IAAIC,MAAM,IAAIC,OAAO,EAAE;UAC1D,OAAO,IAAI;QACf;QACA,IAAI,OAAO,IAAI,CAACvC,MAAM,KAAK,QAAQ,IAAI,IAAI,CAACA,MAAM,IAAI,OAAO,EAAE;UAC3D,OAAO,IAAI;QACf;MACJ;MACA,MAAMyC,MAAM,GAAG;QACXrB,kBAAkB,EAAE,IAAI,CAACA,kBAAkB;QAC3CC,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3BjB,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBC,IAAI,EAAE,IAAI,CAACA;MACf,CAAC;MACD,IAAI,CAACZ,MAAM,CAACiD,aAAa,CAACF,OAAO,EAAEC,MAAM,CAAC;MAC1C;MACA;MACA;MACA,OAAO,CAAC,IAAI,CAAClC,eAAe;IAChC;IACA;IACAoC,WAAWA,CAAA,EAAG;MACV,IAAI,CAACnC,YAAY,EAAEoC,WAAW,CAAC,CAAC;IACpC;IACA3B,UAAUA,CAAA,EAAG;MACT,MAAMuB,OAAO,GAAG,IAAI,CAACA,OAAO;MAC5B,IAAI,CAACzC,IAAI,GACLyC,OAAO,KAAK,IAAI,IAAI,IAAI,CAAC1C,gBAAgB,GACnC,IAAI,CAACA,gBAAgB,EAAE+C,kBAAkB,CAAC,IAAI,CAACpD,MAAM,CAACqD,YAAY,CAACN,OAAO,CAAC,CAAC,GAC5E,IAAI;MACd,MAAMO,cAAc,GAAG,IAAI,CAAChD,IAAI,KAAK,IAAI,GACnC,IAAI;MACJ;MACE;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACApE,0BAA0B,CAAC,IAAI,CAACoE,IAAI,EAAE,IAAI,CAACF,EAAE,CAACe,aAAa,CAACD,OAAO,CAACE,WAAW,CAAC,CAAC,EAAE,MAAM,CAAC;MAClG,IAAI,CAACU,mBAAmB,CAAC,MAAM,EAAEwB,cAAc,CAAC;IACpD;IACAxB,mBAAmBA,CAACyB,QAAQ,EAAEC,SAAS,EAAE;MACrC,MAAMrD,QAAQ,GAAG,IAAI,CAACA,QAAQ;MAC9B,MAAMgB,aAAa,GAAG,IAAI,CAACf,EAAE,CAACe,aAAa;MAC3C,IAAIqC,SAAS,KAAK,IAAI,EAAE;QACpBrD,QAAQ,CAACsD,YAAY,CAACtC,aAAa,EAAEoC,QAAQ,EAAEC,SAAS,CAAC;MAC7D,CAAC,MACI;QACDrD,QAAQ,CAACuD,eAAe,CAACvC,aAAa,EAAEoC,QAAQ,CAAC;MACrD;IACJ;IACA,IAAIR,OAAOA,CAAA,EAAG;MACV,IAAI,IAAI,CAACb,eAAe,KAAK,IAAI,EAAE;QAC/B,OAAO,IAAI;MACf,CAAC,MACI,IAAIpE,SAAS,CAAC,IAAI,CAACoE,eAAe,CAAC,EAAE;QACtC,OAAO,IAAI,CAACA,eAAe;MAC/B;MACA,OAAO,IAAI,CAAClC,MAAM,CAAC2D,aAAa,CAAC,IAAI,CAACzB,eAAe,EAAE;QACnD;QACA;QACArB,UAAU,EAAE,IAAI,CAACA,UAAU,KAAKsB,SAAS,GAAG,IAAI,CAACtB,UAAU,GAAG,IAAI,CAACZ,KAAK;QACxEO,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7BC,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBC,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;QAC7CgB,gBAAgB,EAAE,IAAI,CAACA;MAC3B,CAAC,CAAC;IACN;IACA,OAAOkC,IAAI,YAAAC,mBAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAyF/D,UAAU,EAApBjE,EAAE,CAAAiI,iBAAA,CAAoChG,MAAM,GAA5CjC,EAAE,CAAAiI,iBAAA,CAAuD/F,cAAc,GAAvElC,EAAE,CAAAkI,iBAAA,CAAkF,UAAU,GAA9FlI,EAAE,CAAAiI,iBAAA,CAA0HjI,EAAE,CAACmI,SAAS,GAAxInI,EAAE,CAAAiI,iBAAA,CAAmJjI,EAAE,CAACoI,UAAU,GAAlKpI,EAAE,CAAAiI,iBAAA,CAA6KxI,EAAE,CAACG,gBAAgB;IAAA;IAC5R,OAAOyI,IAAI,kBAD+ErI,EAAE,CAAAsI,iBAAA;MAAAC,IAAA,EACJtE,UAAU;MAAAuE,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADR5I,EAAE,CAAA8I,UAAA,mBAAAC,oCAAAC,MAAA;YAAA,OACJH,GAAA,CAAAlC,OAAA,CAAAqC,MAAA,CAAApC,MAAA,EAAAoC,MAAA,CAAAnC,OAAA,EAAAmC,MAAA,CAAAlC,QAAA,EAAAkC,MAAA,CAAAjC,MAAA,EAAAiC,MAAA,CAAAhC,OAAiF,CAAC;UAAA,CAAzE,CAAC;QAAA;QAAA,IAAA4B,EAAA;UADR5I,EAAE,CAAAiJ,WAAA,WAAAJ,GAAA,CAAApE,MAAA;QAAA;MAAA;MAAAyE,MAAA;QAAAzE,MAAA;QAAAC,WAAA;QAAAC,QAAA;QAAAC,mBAAA;QAAAC,KAAA;QAAAC,IAAA;QAAAC,UAAA;QAAAa,gBAAA,8CACmSvF,gBAAgB;QAAAwF,kBAAA,kDAAoExF,gBAAgB;QAAAyF,UAAA,kCAA4CzF,gBAAgB;QAAAkG,UAAA;MAAA;MAAA4C,QAAA,GADrcnJ,EAAE,CAAAoJ,oBAAA;IAAA;EAEhG;EAAC,OApPKnF,UAAU;AAAA;AAqPhB;EAAA,QAAAkC,SAAA,oBAAAA,SAAA;AAAA;;AA+CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AApEA,IAqEMkD,gBAAgB;EAAtB,MAAMA,gBAAgB,CAAC;IACnBnF,MAAM;IACNoF,OAAO;IACPjF,QAAQ;IACRkF,GAAG;IACHC,IAAI;IACJC,KAAK;IACLC,OAAO,GAAG,EAAE;IACZC,wBAAwB;IACxBC,4BAA4B;IAC5BC,SAAS,GAAG,KAAK;IACjB,IAAIC,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAACD,SAAS;IACzB;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;IACIE,uBAAuB,GAAG;MAAEC,KAAK,EAAE;IAAM,CAAC;IAC1C;AACJ;AACA;AACA;AACA;AACA;AACA;IACIC,qBAAqB;IACrB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACIC,cAAc,GAAG,IAAIvJ,YAAY,CAAC,CAAC;IACnCwE,WAAWA,CAACjB,MAAM,EAAEoF,OAAO,EAAEjF,QAAQ,EAAEkF,GAAG,EAAEC,IAAI,EAAE;MAC9C,IAAI,CAACtF,MAAM,GAAGA,MAAM;MACpB,IAAI,CAACoF,OAAO,GAAGA,OAAO;MACtB,IAAI,CAACjF,QAAQ,GAAGA,QAAQ;MACxB,IAAI,CAACkF,GAAG,GAAGA,GAAG;MACd,IAAI,CAACC,IAAI,GAAGA,IAAI;MAChB,IAAI,CAACG,wBAAwB,GAAGzF,MAAM,CAACqB,MAAM,CAACC,SAAS,CAAEC,CAAC,IAAK;QAC3D,IAAIA,CAAC,YAAY1D,aAAa,EAAE;UAC5B,IAAI,CAACoI,MAAM,CAAC,CAAC;QACjB;MACJ,CAAC,CAAC;IACN;IACA;IACAC,kBAAkBA,CAAA,EAAG;MACjB;MACA1G,EAAE,CAAC,IAAI,CAAC+F,KAAK,CAACvD,OAAO,EAAExC,EAAE,CAAC,IAAI,CAAC,CAAC,CAC3B2G,IAAI,CAACzG,QAAQ,CAAC,CAAC,CAAC,CAChB4B,SAAS,CAAE8E,CAAC,IAAK;QAClB,IAAI,CAACH,MAAM,CAAC,CAAC;QACb,IAAI,CAACI,4BAA4B,CAAC,CAAC;MACvC,CAAC,CAAC;IACN;IACAA,4BAA4BA,CAAA,EAAG;MAC3B,IAAI,CAACX,4BAA4B,EAAEvC,WAAW,CAAC,CAAC;MAChD,MAAMmD,cAAc,GAAG,CAAC,GAAG,IAAI,CAACf,KAAK,CAACgB,OAAO,CAAC,CAAC,EAAE,IAAI,CAACjB,IAAI,CAAC,CACtD1F,MAAM,CAAE0F,IAAI,IAAK,CAAC,CAACA,IAAI,CAAC,CACxBkB,GAAG,CAAElB,IAAI,IAAKA,IAAI,CAACtE,SAAS,CAAC;MAClC,IAAI,CAAC0E,4BAA4B,GAAGjG,IAAI,CAAC6G,cAAc,CAAC,CACnDH,IAAI,CAACzG,QAAQ,CAAC,CAAC,CAAC,CAChB4B,SAAS,CAAEgE,IAAI,IAAK;QACrB,IAAI,IAAI,CAACK,SAAS,KAAK,IAAI,CAACc,YAAY,CAAC,IAAI,CAACzG,MAAM,CAAC,CAACsF,IAAI,CAAC,EAAE;UACzD,IAAI,CAACW,MAAM,CAAC,CAAC;QACjB;MACJ,CAAC,CAAC;IACN;IACA,IAAIS,gBAAgBA,CAACC,IAAI,EAAE;MACvB,MAAMnB,OAAO,GAAGjD,KAAK,CAACC,OAAO,CAACmE,IAAI,CAAC,GAAGA,IAAI,GAAGA,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC;MAC5D,IAAI,CAACpB,OAAO,GAAGA,OAAO,CAAC5F,MAAM,CAAEiH,CAAC,IAAK,CAAC,CAACA,CAAC,CAAC;IAC7C;IACA;IACA9E,WAAWA,CAACC,OAAO,EAAE;MACjB,IAAI,CAACiE,MAAM,CAAC,CAAC;IACjB;IACA;IACA/C,WAAWA,CAAA,EAAG;MACV,IAAI,CAACuC,wBAAwB,CAACtC,WAAW,CAAC,CAAC;MAC3C,IAAI,CAACuC,4BAA4B,EAAEvC,WAAW,CAAC,CAAC;IACpD;IACA8C,MAAMA,CAAA,EAAG;MACL,IAAI,CAAC,IAAI,CAACV,KAAK,IAAI,CAAC,IAAI,CAACvF,MAAM,CAAC8G,SAAS,EACrC;MACJC,cAAc,CAAC,MAAM;QACjB,MAAMC,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC,CAAC;QAC5C,IAAI,CAACxB,OAAO,CAACyB,OAAO,CAAEJ,CAAC,IAAK;UACxB,IAAIG,cAAc,EAAE;YAChB,IAAI,CAAC7G,QAAQ,CAAC+G,QAAQ,CAAC,IAAI,CAAC9B,OAAO,CAACjE,aAAa,EAAE0F,CAAC,CAAC;UACzD,CAAC,MACI;YACD,IAAI,CAAC1G,QAAQ,CAACgH,WAAW,CAAC,IAAI,CAAC/B,OAAO,CAACjE,aAAa,EAAE0F,CAAC,CAAC;UAC5D;QACJ,CAAC,CAAC;QACF,IAAIG,cAAc,IAAI,IAAI,CAACjB,qBAAqB,KAAK5D,SAAS,EAAE;UAC5D,IAAI,CAAChC,QAAQ,CAACsD,YAAY,CAAC,IAAI,CAAC2B,OAAO,CAACjE,aAAa,EAAE,cAAc,EAAE,IAAI,CAAC4E,qBAAqB,CAACqB,QAAQ,CAAC,CAAC,CAAC;QACjH,CAAC,MACI;UACD,IAAI,CAACjH,QAAQ,CAACuD,eAAe,CAAC,IAAI,CAAC0B,OAAO,CAACjE,aAAa,EAAE,cAAc,CAAC;QAC7E;QACA;QACA,IAAI,IAAI,CAACwE,SAAS,KAAKqB,cAAc,EAAE;UACnC,IAAI,CAACrB,SAAS,GAAGqB,cAAc;UAC/B,IAAI,CAAC3B,GAAG,CAACgC,YAAY,CAAC,CAAC;UACvB;UACA,IAAI,CAACrB,cAAc,CAACsB,IAAI,CAACN,cAAc,CAAC;QAC5C;MACJ,CAAC,CAAC;IACN;IACAP,YAAYA,CAACzG,MAAM,EAAE;MACjB,MAAMuH,OAAO,GAAGC,oBAAoB,CAAC,IAAI,CAAC3B,uBAAuB,CAAC,GAC5D,IAAI,CAACA,uBAAuB;MAC5B;MACE,IAAI,CAACA,uBAAuB,CAACC,KAAK,IAAI,KAAK;MACnD,OAAQR,IAAI,IAAK;QACb,MAAMvC,OAAO,GAAGuC,IAAI,CAACvC,OAAO;QAC5B,OAAOA,OAAO,GAAG/C,MAAM,CAAC4F,QAAQ,CAAC7C,OAAO,EAAEwE,OAAO,CAAC,GAAG,KAAK;MAC9D,CAAC;IACL;IACAP,cAAcA,CAAA,EAAG;MACb,MAAMS,eAAe,GAAG,IAAI,CAAChB,YAAY,CAAC,IAAI,CAACzG,MAAM,CAAC;MACtD,OAAQ,IAAI,CAACsF,IAAI,IAAImC,eAAe,CAAC,IAAI,CAACnC,IAAI,CAAC,IAAK,IAAI,CAACC,KAAK,CAACmC,IAAI,CAACD,eAAe,CAAC;IACxF;IACA,OAAO7D,IAAI,YAAA+D,yBAAA7D,iBAAA;MAAA,YAAAA,iBAAA,IAAyFqB,gBAAgB,EAhQ1BrJ,EAAE,CAAAiI,iBAAA,CAgQ0ChG,MAAM,GAhQlDjC,EAAE,CAAAiI,iBAAA,CAgQ6DjI,EAAE,CAACoI,UAAU,GAhQ5EpI,EAAE,CAAAiI,iBAAA,CAgQuFjI,EAAE,CAACmI,SAAS,GAhQrGnI,EAAE,CAAAiI,iBAAA,CAgQgHjI,EAAE,CAAC8L,iBAAiB,GAhQtI9L,EAAE,CAAAiI,iBAAA,CAgQiJhE,UAAU;IAAA;IACvP,OAAOoE,IAAI,kBAjQ+ErI,EAAE,CAAAsI,iBAAA;MAAAC,IAAA,EAiQJc,gBAAgB;MAAAb,SAAA;MAAAuD,cAAA,WAAAC,gCAAApD,EAAA,EAAAC,GAAA,EAAAoD,QAAA;QAAA,IAAArD,EAAA;UAjQd5I,EAAE,CAAAkM,cAAA,CAAAD,QAAA,EAiQqThI,UAAU;QAAA;QAAA,IAAA2E,EAAA;UAAA,IAAAuD,EAAA;UAjQjUnM,EAAE,CAAAoM,cAAA,CAAAD,EAAA,GAAFnM,EAAE,CAAAqM,WAAA,QAAAxD,GAAA,CAAAY,KAAA,GAAA0C,EAAA;QAAA;MAAA;MAAAjD,MAAA;QAAAa,uBAAA;QAAAE,qBAAA;QAAAW,gBAAA;MAAA;MAAA0B,OAAA;QAAApC,cAAA;MAAA;MAAAqC,QAAA;MAAApD,QAAA,GAAFnJ,EAAE,CAAAoJ,oBAAA;IAAA;EAkQhG;EAAC,OA3IKC,gBAAgB;AAAA;AA4ItB;EAAA,QAAAlD,SAAA,oBAAAA,SAAA;AAAA;AAoBA;AACA;AACA;AACA,SAASuF,oBAAoBA,CAACD,OAAO,EAAE;EACnC,OAAO,CAAC,CAACA,OAAO,CAACe,KAAK;AAC1B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,CAAC;AAEzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA,IAWMC,iBAAiB;EAAvB,MAAMA,iBAAiB,CAAC;IACpBC,OAAOA,CAACxI,KAAK,EAAEyI,EAAE,EAAE;MACf,OAAOA,EAAE,CAAC,CAAC,CAACvC,IAAI,CAACxG,UAAU,CAAC,MAAMH,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;IAChD;IACA,OAAOoE,IAAI,YAAA+E,0BAAA7E,iBAAA;MAAA,YAAAA,iBAAA,IAAyF0E,iBAAiB;IAAA;IACrH,OAAOI,KAAK,kBAvT8E9M,EAAE,CAAA+M,kBAAA;MAAAC,KAAA,EAuTYN,iBAAiB;MAAAO,OAAA,EAAjBP,iBAAiB,CAAA5E,IAAA;MAAAoF,UAAA,EAAc;IAAM;EACjJ;EAAC,OANKR,iBAAiB;AAAA;AAOvB;EAAA,QAAAvG,SAAA,oBAAAA,SAAA;AAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,IASMgH,YAAY;EAAlB,MAAMA,YAAY,CAAC;IACfR,OAAOA,CAACxI,KAAK,EAAEyI,EAAE,EAAE;MACf,OAAOlJ,EAAE,CAAC,IAAI,CAAC;IACnB;IACA,OAAOoE,IAAI,YAAAsF,qBAAApF,iBAAA;MAAA,YAAAA,iBAAA,IAAyFmF,YAAY;IAAA;IAChH,OAAOL,KAAK,kBA3U8E9M,EAAE,CAAA+M,kBAAA;MAAAC,KAAA,EA2UYG,YAAY;MAAAF,OAAA,EAAZE,YAAY,CAAArF,IAAA;MAAAoF,UAAA,EAAc;IAAM;EAC5I;EAAC,OANKC,YAAY;AAAA;AAOlB;EAAA,QAAAhH,SAAA,oBAAAA,SAAA;AAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA,IAYMkH,eAAe;EAArB,MAAMA,eAAe,CAAC;IAClBnJ,MAAM;IACNoJ,QAAQ;IACRC,kBAAkB;IAClBC,MAAM;IACNvI,YAAY;IACZE,WAAWA,CAACjB,MAAM,EAAEoJ,QAAQ,EAAEC,kBAAkB,EAAEC,MAAM,EAAE;MACtD,IAAI,CAACtJ,MAAM,GAAGA,MAAM;MACpB,IAAI,CAACoJ,QAAQ,GAAGA,QAAQ;MACxB,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;MAC5C,IAAI,CAACC,MAAM,GAAGA,MAAM;IACxB;IACAC,eAAeA,CAAA,EAAG;MACd,IAAI,CAACxI,YAAY,GAAG,IAAI,CAACf,MAAM,CAACqB,MAAM,CACjC8E,IAAI,CAACvG,MAAM,CAAE4J,CAAC,IAAKA,CAAC,YAAY3L,aAAa,CAAC,EAAEgC,SAAS,CAAC,MAAM,IAAI,CAAC4I,OAAO,CAAC,CAAC,CAAC,CAAC,CAChFnH,SAAS,CAAC,MAAM,CAAE,CAAC,CAAC;IAC7B;IACAmH,OAAOA,CAAA,EAAG;MACN,OAAO,IAAI,CAACgB,aAAa,CAAC,IAAI,CAACL,QAAQ,EAAE,IAAI,CAACpJ,MAAM,CAAC0J,MAAM,CAAC;IAChE;IACA;IACAxG,WAAWA,CAAA,EAAG;MACV,IAAI,IAAI,CAACnC,YAAY,EAAE;QACnB,IAAI,CAACA,YAAY,CAACoC,WAAW,CAAC,CAAC;MACnC;IACJ;IACAsG,aAAaA,CAACL,QAAQ,EAAEO,MAAM,EAAE;MAC5B,MAAMC,GAAG,GAAG,EAAE;MACd,KAAK,MAAM3J,KAAK,IAAI0J,MAAM,EAAE;QACxB,IAAI1J,KAAK,CAAC4J,SAAS,IAAI,CAAC5J,KAAK,CAAC6J,SAAS,EAAE;UACrC7J,KAAK,CAAC6J,SAAS,GAAGjN,yBAAyB,CAACoD,KAAK,CAAC4J,SAAS,EAAET,QAAQ,EAAE,UAAUnJ,KAAK,CAAC8J,IAAI,EAAE,CAAC;QAClG;QACA,MAAMC,uBAAuB,GAAG/J,KAAK,CAAC6J,SAAS,IAAIV,QAAQ;QAC3D,MAAMa,mBAAmB,GAAGhK,KAAK,CAACiK,eAAe,IAAIF,uBAAuB;QAC5E;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAK/J,KAAK,CAACkK,YAAY,IAAI,CAAClK,KAAK,CAACmK,aAAa,IAAInK,KAAK,CAACoK,OAAO,KAAKlI,SAAS,IACzElC,KAAK,CAACqK,aAAa,IAAI,CAACrK,KAAK,CAACsK,gBAAiB,EAAE;UAClDX,GAAG,CAACY,IAAI,CAAC,IAAI,CAACC,aAAa,CAACT,uBAAuB,EAAE/J,KAAK,CAAC,CAAC;QAChE;QACA,IAAIA,KAAK,CAACyK,QAAQ,IAAIzK,KAAK,CAACmK,aAAa,EAAE;UACvCR,GAAG,CAACY,IAAI,CAAC,IAAI,CAACf,aAAa,CAACQ,mBAAmB,EAAGhK,KAAK,CAACyK,QAAQ,IAAIzK,KAAK,CAACmK,aAAc,CAAC,CAAC;QAC9F;MACJ;MACA,OAAO3K,IAAI,CAACmK,GAAG,CAAC,CAACzD,IAAI,CAACzG,QAAQ,CAAC,CAAC,CAAC;IACrC;IACA+K,aAAaA,CAACrB,QAAQ,EAAEnJ,KAAK,EAAE;MAC3B,OAAO,IAAI,CAACoJ,kBAAkB,CAACZ,OAAO,CAACxI,KAAK,EAAE,MAAM;QAChD,IAAI0K,eAAe;QACnB,IAAI1K,KAAK,CAACkK,YAAY,IAAIlK,KAAK,CAACoK,OAAO,KAAKlI,SAAS,EAAE;UACnDwI,eAAe,GAAG,IAAI,CAACrB,MAAM,CAACa,YAAY,CAACf,QAAQ,EAAEnJ,KAAK,CAAC;QAC/D,CAAC,MACI;UACD0K,eAAe,GAAGnL,EAAE,CAAC,IAAI,CAAC;QAC9B;QACA,MAAMoL,sBAAsB,GAAGD,eAAe,CAACxE,IAAI,CAACrG,QAAQ,CAAE4J,MAAM,IAAK;UACrE,IAAIA,MAAM,KAAK,IAAI,EAAE;YACjB,OAAOlK,EAAE,CAAC,KAAK,CAAC,CAAC;UACrB;UACAS,KAAK,CAACmK,aAAa,GAAGV,MAAM,CAACC,MAAM;UACnC1J,KAAK,CAACiK,eAAe,GAAGR,MAAM,CAACN,QAAQ;UACvC;UACA;UACA,OAAO,IAAI,CAACK,aAAa,CAACC,MAAM,CAACN,QAAQ,IAAIA,QAAQ,EAAEM,MAAM,CAACC,MAAM,CAAC;QACzE,CAAC,CAAC,CAAC;QACH,IAAI1J,KAAK,CAACqK,aAAa,IAAI,CAACrK,KAAK,CAACsK,gBAAgB,EAAE;UAChD,MAAMM,cAAc,GAAG,IAAI,CAACvB,MAAM,CAACgB,aAAa,CAACrK,KAAK,CAAC;UACvD,OAAOR,IAAI,CAAC,CAACmL,sBAAsB,EAAEC,cAAc,CAAC,CAAC,CAAC1E,IAAI,CAACzG,QAAQ,CAAC,CAAC,CAAC;QAC1E,CAAC,MACI;UACD,OAAOkL,sBAAsB;QACjC;MACJ,CAAC,CAAC;IACN;IACA,OAAOhH,IAAI,YAAAkH,wBAAAhH,iBAAA;MAAA,YAAAA,iBAAA,IAAyFqF,eAAe,EA7azBrN,EAAE,CAAAiP,QAAA,CA6ayChN,MAAM,GA7ajDjC,EAAE,CAAAiP,QAAA,CA6a4DjP,EAAE,CAACkP,mBAAmB,GA7apFlP,EAAE,CAAAiP,QAAA,CA6a+FxC,kBAAkB,GA7anHzM,EAAE,CAAAiP,QAAA,CA6a8H9M,kBAAkB;IAAA;IAC5O,OAAO2K,KAAK,kBA9a8E9M,EAAE,CAAA+M,kBAAA;MAAAC,KAAA,EA8aYK,eAAe;MAAAJ,OAAA,EAAfI,eAAe,CAAAvF,IAAA;MAAAoF,UAAA,EAAc;IAAM;EAC/I;EAAC,OAlFKG,eAAe;AAAA;AAmFrB;EAAA,QAAAlH,SAAA,oBAAAA,SAAA;AAAA;AAKA,MAAMgJ,eAAe,gBAAG,IAAIlO,cAAc,CAAC,EAAE,CAAC;AAAC,IACzCmO,cAAc;EAApB,MAAMA,cAAc,CAAC;IACjBC,aAAa;IACbC,WAAW;IACXC,gBAAgB;IAChBC,IAAI;IACJ/D,OAAO;IACP9B,wBAAwB;IACxB8F,wBAAwB;IACxBC,MAAM,GAAG,CAAC;IACVC,UAAU,GAAG,YAAY;IACzBC,UAAU,GAAG,CAAC;IACdC,KAAK,GAAG,CAAC,CAAC;IACV;IACA1K,WAAWA,CAACkK,aAAa,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,IAAI,EAAE/D,OAAO,GAAG,CAAC,CAAC,EAAE;MAC1E,IAAI,CAAC4D,aAAa,GAAGA,aAAa;MAClC,IAAI,CAACC,WAAW,GAAGA,WAAW;MAC9B,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;MACxC,IAAI,CAACC,IAAI,GAAGA,IAAI;MAChB,IAAI,CAAC/D,OAAO,GAAGA,OAAO;MACtB;MACAA,OAAO,CAACqE,yBAAyB,KAAK,UAAU;MAChDrE,OAAO,CAACsE,eAAe,KAAK,UAAU;IAC1C;IACAC,IAAIA,CAAA,EAAG;MACH;MACA;MACA;MACA,IAAI,IAAI,CAACvE,OAAO,CAACqE,yBAAyB,KAAK,UAAU,EAAE;QACvD,IAAI,CAACP,gBAAgB,CAACU,2BAA2B,CAAC,QAAQ,CAAC;MAC/D;MACA,IAAI,CAACtG,wBAAwB,GAAG,IAAI,CAACuG,kBAAkB,CAAC,CAAC;MACzD,IAAI,CAACT,wBAAwB,GAAG,IAAI,CAACU,mBAAmB,CAAC,CAAC;IAC9D;IACAD,kBAAkBA,CAAA,EAAG;MACjB,OAAO,IAAI,CAACZ,WAAW,CAAC/J,MAAM,CAACC,SAAS,CAAEkI,CAAC,IAAK;QAC5C,IAAIA,CAAC,YAAYtL,eAAe,EAAE;UAC9B;UACA,IAAI,CAACyN,KAAK,CAAC,IAAI,CAACH,MAAM,CAAC,GAAG,IAAI,CAACH,gBAAgB,CAACa,iBAAiB,CAAC,CAAC;UACnE,IAAI,CAACT,UAAU,GAAGjC,CAAC,CAAC2C,iBAAiB;UACrC,IAAI,CAACT,UAAU,GAAGlC,CAAC,CAAC4C,aAAa,GAAG5C,CAAC,CAAC4C,aAAa,CAACC,YAAY,GAAG,CAAC;QACxE,CAAC,MACI,IAAI7C,CAAC,YAAY3L,aAAa,EAAE;UACjC,IAAI,CAAC2N,MAAM,GAAGhC,CAAC,CAAC8C,EAAE;UAClB,IAAI,CAACC,mBAAmB,CAAC/C,CAAC,EAAE,IAAI,CAAC2B,aAAa,CAACqB,KAAK,CAAChD,CAAC,CAACiD,iBAAiB,CAAC,CAAChM,QAAQ,CAAC;QACvF,CAAC,MACI,IAAI+I,CAAC,YAAYrL,iBAAiB,IACnCqL,CAAC,CAACkD,IAAI,KAAKtO,qBAAqB,CAACuO,wBAAwB,EAAE;UAC3D,IAAI,CAAClB,UAAU,GAAGtJ,SAAS;UAC3B,IAAI,CAACuJ,UAAU,GAAG,CAAC;UACnB,IAAI,CAACa,mBAAmB,CAAC/C,CAAC,EAAE,IAAI,CAAC2B,aAAa,CAACqB,KAAK,CAAChD,CAAC,CAACoD,GAAG,CAAC,CAACnM,QAAQ,CAAC;QACzE;MACJ,CAAC,CAAC;IACN;IACAwL,mBAAmBA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACb,WAAW,CAAC/J,MAAM,CAACC,SAAS,CAAEkI,CAAC,IAAK;QAC5C,IAAI,EAAEA,CAAC,YAAYnL,MAAM,CAAC,EACtB;QACJ;QACA,IAAImL,CAAC,CAACqD,QAAQ,EAAE;UACZ,IAAI,IAAI,CAACtF,OAAO,CAACqE,yBAAyB,KAAK,KAAK,EAAE;YAClD,IAAI,CAACP,gBAAgB,CAACyB,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAClD,CAAC,MACI,IAAI,IAAI,CAACvF,OAAO,CAACqE,yBAAyB,KAAK,SAAS,EAAE;YAC3D,IAAI,CAACP,gBAAgB,CAACyB,gBAAgB,CAACtD,CAAC,CAACqD,QAAQ,CAAC;UACtD;UACA;QACJ,CAAC,MACI;UACD,IAAIrD,CAAC,CAACuD,MAAM,IAAI,IAAI,CAACxF,OAAO,CAACsE,eAAe,KAAK,SAAS,EAAE;YACxD,IAAI,CAACR,gBAAgB,CAAC2B,cAAc,CAACxD,CAAC,CAACuD,MAAM,CAAC;UAClD,CAAC,MACI,IAAI,IAAI,CAACxF,OAAO,CAACqE,yBAAyB,KAAK,UAAU,EAAE;YAC5D,IAAI,CAACP,gBAAgB,CAACyB,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAClD;QACJ;MACJ,CAAC,CAAC;IACN;IACAP,mBAAmBA,CAACU,WAAW,EAAEF,MAAM,EAAE;MACrC,IAAI,CAACzB,IAAI,CAAC4B,iBAAiB,CAAC,MAAM;QAC9B;QACA;QACA;QACAC,UAAU,CAAC,MAAM;UACb,IAAI,CAAC7B,IAAI,CAAC8B,GAAG,CAAC,MAAM;YAChB,IAAI,CAAChC,WAAW,CAAC/J,MAAM,CAACe,IAAI,CAAC,IAAI/D,MAAM,CAAC4O,WAAW,EAAE,IAAI,CAACxB,UAAU,KAAK,UAAU,GAAG,IAAI,CAACE,KAAK,CAAC,IAAI,CAACD,UAAU,CAAC,GAAG,IAAI,EAAEqB,MAAM,CAAC,CAAC;UACtI,CAAC,CAAC;QACN,CAAC,EAAE,CAAC,CAAC;MACT,CAAC,CAAC;IACN;IACA;IACA7J,WAAWA,CAAA,EAAG;MACV,IAAI,CAACuC,wBAAwB,EAAEtC,WAAW,CAAC,CAAC;MAC5C,IAAI,CAACoI,wBAAwB,EAAEpI,WAAW,CAAC,CAAC;IAChD;IACA,OAAOS,IAAI,YAAAyJ,uBAAAvJ,iBAAA;MAphB+EhI,EAAE,CAAAwR,gBAAA;IAAA;IAqhB5F,OAAO1E,KAAK,kBArhB8E9M,EAAE,CAAA+M,kBAAA;MAAAC,KAAA,EAqhBYoC,cAAc;MAAAnC,OAAA,EAAdmC,cAAc,CAAAtH;IAAA;EAC1H;EAAC,OAhGKsH,cAAc;AAAA;AAiGpB;EAAA,QAAAjJ,SAAA,oBAAAA,SAAA;AAAA;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASsL,aAAaA,CAAC5D,MAAM,EAAE,GAAG1E,QAAQ,EAAE;EACxC,OAAO/H,wBAAwB,CAAC,CAC5B;IAAEsQ,OAAO,EAAEhP,MAAM;IAAEiP,KAAK,EAAE,IAAI;IAAEC,QAAQ,EAAE/D;EAAO,CAAC,EAClD,OAAO1H,SAAS,KAAK,WAAW,IAAIA,SAAS,GACvC;IAAEuL,OAAO,EAAEG,kBAAkB;IAAED,QAAQ,EAAE;EAAK,CAAC,GAC/C,EAAE,EACR;IAAEF,OAAO,EAAExP,cAAc;IAAE4P,UAAU,EAAEC,SAAS;IAAEC,IAAI,EAAE,CAAC/P,MAAM;EAAE,CAAC,EAClE;IAAEyP,OAAO,EAAErQ,sBAAsB;IAAEsQ,KAAK,EAAE,IAAI;IAAEG,UAAU,EAAEG;EAAqB,CAAC,EAClF9I,QAAQ,CAACuB,GAAG,CAAEwH,OAAO,IAAKA,OAAO,CAACC,UAAU,CAAC,CAChD,CAAC;AACN;AACA,SAASJ,SAASA,CAAC7N,MAAM,EAAE;EACvB,OAAOA,MAAM,CAACkO,WAAW,CAACC,IAAI;AAClC;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,IAAI,EAAExE,SAAS,EAAE;EACpC,OAAO;IAAEyE,KAAK,EAAED,IAAI;IAAEJ,UAAU,EAAEpE;EAAU,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA,MAAM8D,kBAAkB,gBAAG,IAAI5Q,cAAc,CAAC,EAAE,EAAE;EAC9CiM,UAAU,EAAE,MAAM;EAClBD,OAAO,EAAEA,CAAA,KAAM;AACnB,CAAC,CAAC;AACF,MAAMwF,4BAA4B,GAAG;EACjCf,OAAO,EAAEpQ,uBAAuB;EAChCqQ,KAAK,EAAE,IAAI;EACXG,UAAUA,CAAA,EAAG;IACT,OAAO,MAAM;MACT,IAAI,CAACtQ,MAAM,CAACqQ,kBAAkB,CAAC,EAAE;QAC7Ba,OAAO,CAACC,IAAI,CAAC,gFAAgF,GACzF,2BAA2B,CAAC;MACpC;IACJ,CAAC;EACL;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAAC/E,MAAM,EAAE;EAC3B,OAAO,CACH;IAAE6D,OAAO,EAAEhP,MAAM;IAAEiP,KAAK,EAAE,IAAI;IAAEC,QAAQ,EAAE/D;EAAO,CAAC,EAClD,OAAO1H,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAGsM,4BAA4B,GAAG,EAAE,CACpF;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,qBAAqBA,CAACpH,OAAO,GAAG,CAAC,CAAC,EAAE;EACzC,MAAMsC,SAAS,GAAG,CACd;IACI2D,OAAO,EAAEvC,eAAe;IACxB2C,UAAU,EAAEA,CAAA,KAAM;MACd,MAAMvC,gBAAgB,GAAG/N,MAAM,CAAC3B,gBAAgB,CAAC;MACjD,MAAM2P,IAAI,GAAGhO,MAAM,CAACI,MAAM,CAAC;MAC3B,MAAM0N,WAAW,GAAG9N,MAAM,CAACiB,qBAAqB,CAAC;MACjD,MAAM4M,aAAa,GAAG7N,MAAM,CAACgB,aAAa,CAAC;MAC3C,OAAO,IAAI4M,cAAc,CAACC,aAAa,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,IAAI,EAAE/D,OAAO,CAAC;IAC1F;EACJ,CAAC,CACJ;EACD,OAAO6G,aAAa,CAAC,CAAC,CAAC,kDAAkDvE,SAAS,CAAC;AACvF;AACA,SAASkE,oBAAoBA,CAAA,EAAG;EAC5B,MAAM3E,QAAQ,GAAG9L,MAAM,CAACC,QAAQ,CAAC;EACjC,OAAQqR,wBAAwB,IAAK;IACjC,MAAMC,GAAG,GAAGzF,QAAQ,CAAC0F,GAAG,CAACtR,cAAc,CAAC;IACxC,IAAIoR,wBAAwB,KAAKC,GAAG,CAACE,UAAU,CAAC,CAAC,CAAC,EAAE;MAChD;IACJ;IACA,MAAM/O,MAAM,GAAGoJ,QAAQ,CAAC0F,GAAG,CAAC/Q,MAAM,CAAC;IACnC,MAAMiR,aAAa,GAAG5F,QAAQ,CAAC0F,GAAG,CAACG,cAAc,CAAC;IAClD,IAAI7F,QAAQ,CAAC0F,GAAG,CAACI,kBAAkB,CAAC,KAAK,CAAC,CAAC,4CAA4C;MACnFlP,MAAM,CAACmP,iBAAiB,CAAC,CAAC;IAC9B;IACA/F,QAAQ,CAAC0F,GAAG,CAACM,gBAAgB,EAAE,IAAI,EAAE3R,WAAW,CAACb,QAAQ,CAAC,EAAE2M,eAAe,CAAC,CAAC;IAC7EH,QAAQ,CAAC0F,GAAG,CAAC7D,eAAe,EAAE,IAAI,EAAExN,WAAW,CAACb,QAAQ,CAAC,EAAEkP,IAAI,CAAC,CAAC;IACjE9L,MAAM,CAACqP,sBAAsB,CAACR,GAAG,CAACS,cAAc,CAAC,CAAC,CAAC,CAAC;IACpD,IAAI,CAACN,aAAa,CAACO,MAAM,EAAE;MACvBP,aAAa,CAAC5M,IAAI,CAAC,CAAC;MACpB4M,aAAa,CAACQ,QAAQ,CAAC,CAAC;MACxBR,aAAa,CAAC7L,WAAW,CAAC,CAAC;IAC/B;EACJ,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8L,cAAc,gBAAG,IAAIlS,cAAc,CAAC,OAAOkF,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAG,0BAA0B,GAAG,EAAE,EAAE;EACvH8G,OAAO,EAAEA,CAAA,KAAM;IACX,OAAO,IAAIxJ,OAAO,CAAC,CAAC;EACxB;AACJ,CAAC,CAAC;AACF,MAAM2P,kBAAkB,gBAAG,IAAInS,cAAc,CAAC,OAAOkF,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAG,oBAAoB,GAAG,EAAE,EAAE;EAAE+G,UAAU,EAAE,MAAM;EAAED,OAAO,EAAEA,CAAA,KAAM,CAAC,CAAC;AAA2C,CAAC,CAAC;AAC7M;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0G,oCAAoCA,CAAA,EAAG;EAC5C,MAAM5F,SAAS,GAAG,CACd;IAAE2D,OAAO,EAAE0B,kBAAkB;IAAExB,QAAQ,EAAE,CAAC,CAAC;EAAwC,CAAC,EACpFrQ,qBAAqB,CAAC,MAAM;IACxB,MAAM+L,QAAQ,GAAG9L,MAAM,CAACC,QAAQ,CAAC;IACjC,MAAMmS,mBAAmB,GAAGtG,QAAQ,CAAC0F,GAAG,CAACtT,oBAAoB,EAAEmU,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC;IACjF,OAAOF,mBAAmB,CAACG,IAAI,CAAC,MAAM;MAClC,OAAO,IAAIF,OAAO,CAAEC,OAAO,IAAK;QAC5B,MAAM5P,MAAM,GAAGoJ,QAAQ,CAAC0F,GAAG,CAAC/Q,MAAM,CAAC;QACnC,MAAMiR,aAAa,GAAG5F,QAAQ,CAAC0F,GAAG,CAACG,cAAc,CAAC;QAClDxQ,mBAAmB,CAACuB,MAAM,EAAE,MAAM;UAC9B;UACA;UACA4P,OAAO,CAAC,IAAI,CAAC;QACjB,CAAC,CAAC;QACFxG,QAAQ,CAAC0F,GAAG,CAACvQ,qBAAqB,CAAC,CAACuR,kBAAkB,GAAG,MAAM;UAC3D;UACA;UACA;UACAF,OAAO,CAAC,IAAI,CAAC;UACb,OAAOZ,aAAa,CAACO,MAAM,GAAG/P,EAAE,CAAC,KAAK,CAAC,CAAC,GAAGwP,aAAa;QAC5D,CAAC;QACDhP,MAAM,CAACmP,iBAAiB,CAAC,CAAC;MAC9B,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC,CAAC,CACL;EACD,OAAOf,aAAa,CAAC,CAAC,CAAC,iEAAiEvE,SAAS,CAAC;AACtG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkG,6BAA6BA,CAAA,EAAG;EACrC,MAAMlG,SAAS,GAAG,CACdxM,qBAAqB,CAAC,MAAM;IACxBC,MAAM,CAACS,MAAM,CAAC,CAACiS,2BAA2B,CAAC,CAAC;EAChD,CAAC,CAAC,EACF;IAAExC,OAAO,EAAE0B,kBAAkB;IAAExB,QAAQ,EAAE,CAAC,CAAC;EAAiC,CAAC,CAChF;EACD,OAAOU,aAAa,CAAC,CAAC,CAAC,0DAA0DvE,SAAS,CAAC;AAC/F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASoG,gBAAgBA,CAAA,EAAG;EACxB,IAAIpG,SAAS,GAAG,EAAE;EAClB,IAAI,OAAO5H,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;IAC/C4H,SAAS,GAAG,CACR;MACI2D,OAAO,EAAEpQ,uBAAuB;MAChCqQ,KAAK,EAAE,IAAI;MACXG,UAAU,EAAEA,CAAA,KAAM;QACd,MAAM5N,MAAM,GAAG1C,MAAM,CAACS,MAAM,CAAC;QAC7B,OAAO,MAAMiC,MAAM,CAACqB,MAAM,CAACC,SAAS,CAAEkI,CAAC,IAAK;UACxC;UACAgF,OAAO,CAAC0B,KAAK,GAAG,iBAAiB1G,CAAC,CAACvI,WAAW,CAACkP,IAAI,EAAE,CAAC;UACtD3B,OAAO,CAAC4B,GAAG,CAACnR,cAAc,CAACuK,CAAC,CAAC,CAAC;UAC9BgF,OAAO,CAAC4B,GAAG,CAAC5G,CAAC,CAAC;UACdgF,OAAO,CAAC6B,QAAQ,GAAG,CAAC;UACpB;QACJ,CAAC,CAAC;MACN;IACJ,CAAC,CACJ;EACL,CAAC,MACI;IACDxG,SAAS,GAAG,EAAE;EAClB;EACA,OAAOuE,aAAa,CAAC,CAAC,CAAC,6CAA6CvE,SAAS,CAAC;AAClF;AACA,MAAMuF,gBAAgB,gBAAG,IAAIrS,cAAc,CAAC,OAAOkF,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAG,kBAAkB,GAAG,EAAE,CAAC;AACpH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASqO,cAAcA,CAACjH,kBAAkB,EAAE;EACxC,MAAMQ,SAAS,GAAG,CACd;IAAE2D,OAAO,EAAE4B,gBAAgB;IAAEmB,WAAW,EAAEpH;EAAgB,CAAC,EAC3D;IAAEqE,OAAO,EAAEjF,kBAAkB;IAAEgI,WAAW,EAAElH;EAAmB,CAAC,CACnE;EACD,OAAO+E,aAAa,CAAC,CAAC,CAAC,2CAA2CvE,SAAS,CAAC;AAChF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS2G,gBAAgBA,CAACjJ,OAAO,EAAE;EAC/B,MAAMsC,SAAS,GAAG,CAAC;IAAE2D,OAAO,EAAE9O,oBAAoB;IAAEgP,QAAQ,EAAEnG;EAAQ,CAAC,CAAC;EACxE,OAAO6G,aAAa,CAAC,CAAC,CAAC,oDAAoDvE,SAAS,CAAC;AACzF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS4G,gBAAgBA,CAAA,EAAG;EACxB,MAAM5G,SAAS,GAAG,CAAC;IAAE2D,OAAO,EAAE9R,gBAAgB;IAAEgV,QAAQ,EAAEjV;EAAqB,CAAC,CAAC;EACjF,OAAO2S,aAAa,CAAC,CAAC,CAAC,mDAAmDvE,SAAS,CAAC;AACxF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS8G,0BAA0BA,CAACC,OAAO,EAAE;EACzC,MAAM/G,SAAS,GAAG,CACd;IACI2D,OAAO,EAAE7O,wBAAwB;IACjC+O,QAAQ,EAAEkD;EACd,CAAC,CACJ;EACD,OAAOxC,aAAa,CAAC,CAAC,CAAC,uDAAuDvE,SAAS,CAAC;AAC5F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgH,yBAAyBA,CAAA,EAAG;EACjC,MAAMhH,SAAS,GAAG,CACdjL,0BAA0B,EAC1B;IAAE4O,OAAO,EAAE3O,YAAY;IAAE0R,WAAW,EAAE3R;EAA2B,CAAC,CACrE;EACD,OAAOwP,aAAa,CAAC,CAAC,CAAC,sDAAsDvE,SAAS,CAAC;AAC3F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASiH,mBAAmBA,CAACvJ,OAAO,EAAE;EAClCtK,uBAAuB,CAAC,yBAAyB,CAAC;EAClD,MAAM4M,SAAS,GAAG,CACd;IAAE2D,OAAO,EAAEzO,sBAAsB;IAAE2O,QAAQ,EAAE5O;EAAqB,CAAC,EACnE;IACI0O,OAAO,EAAExO,uBAAuB;IAChC0O,QAAQ,EAAE;MAAEqD,kBAAkB,EAAE,CAAC,CAACxJ,OAAO,EAAEyJ,qBAAqB;MAAE,GAAGzJ;IAAQ;EACjF,CAAC,CACJ;EACD,OAAO6G,aAAa,CAAC,CAAC,CAAC,gDAAgDvE,SAAS,CAAC;AACrF;;AAEA;AACA;AACA;AACA,MAAMoH,iBAAiB,GAAG,CAAC7R,YAAY,EAAEW,UAAU,EAAEoF,gBAAgB,EAAE7F,qBAAqB,CAAC;AAC7F;AACA;AACA;AACA,MAAM4R,oBAAoB,gBAAG,IAAInU,cAAc,CAAC,OAAOkF,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAG,gCAAgC,GAAG,EAAE,CAAC;AACtI;AACA;AACA;AACA;AACA,MAAMkP,gBAAgB,GAAG,CACrBvV,QAAQ,EACR;EAAE4R,OAAO,EAAElP,aAAa;EAAEoS,QAAQ,EAAExR;AAAqB,CAAC,EAC1DnB,MAAM,EACNoB,sBAAsB,EACtB;EAAEqO,OAAO,EAAExP,cAAc;EAAE4P,UAAU,EAAEC,SAAS;EAAEC,IAAI,EAAE,CAAC/P,MAAM;AAAE,CAAC,EAClEE,kBAAkB;AAClB;AACA;AACA,OAAOgE,SAAS,KAAK,WAAW,IAAIA,SAAS,GACvC;EAAEuL,OAAO,EAAEG,kBAAkB;EAAED,QAAQ,EAAE;AAAK,CAAC,GAC/C,EAAE,CACX;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AApBA,IAqBM0D,YAAY;EAAlB,MAAMA,YAAY,CAAC;IACfnQ,WAAWA,CAAA,EAAG;MACV,IAAI,OAAOgB,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;QAC/C3E,MAAM,CAAC4T,oBAAoB,EAAE;UAAEG,QAAQ,EAAE;QAAK,CAAC,CAAC;MACpD;IACJ;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,OAAOC,OAAOA,CAAC3H,MAAM,EAAED,MAAM,EAAE;MAC3B,OAAO;QACH6H,QAAQ,EAAEH,YAAY;QACtBvH,SAAS,EAAE,CACPsH,gBAAgB,EAChB,OAAOlP,SAAS,KAAK,WAAW,IAAIA,SAAS,GACvCyH,MAAM,EAAE8H,aAAa,GACjBvB,gBAAgB,CAAC,CAAC,CAAChC,UAAU,GAC7B,EAAE,GACN,EAAE,EACR;UAAET,OAAO,EAAEhP,MAAM;UAAEiP,KAAK,EAAE,IAAI;UAAEC,QAAQ,EAAE/D;QAAO,CAAC,EAClD,OAAO1H,SAAS,KAAK,WAAW,IAAIA,SAAS,GACvC;UACEuL,OAAO,EAAE0D,oBAAoB;UAC7BtD,UAAU,EAAE6D,mBAAmB;UAC/B3D,IAAI,EAAE,CAAC,CAAC/P,MAAM,EAAE,IAAInB,QAAQ,CAAC,CAAC,EAAE,IAAIe,QAAQ,CAAC,CAAC,CAAC;QACnD,CAAC,GACC,EAAE,EACR+L,MAAM,EAAEgI,YAAY,GACd;UACElE,OAAO,EAAE7O,wBAAwB;UACjC+O,QAAQ,EAAEhE,MAAM,CAACgI;QACrB,CAAC,GACC,EAAE,EACR;UAAElE,OAAO,EAAE9O,oBAAoB;UAAEgP,QAAQ,EAAEhE,MAAM,GAAGA,MAAM,GAAG,CAAC;QAAE,CAAC,EACjEA,MAAM,EAAEiI,OAAO,GAAGC,2BAA2B,CAAC,CAAC,GAAGC,2BAA2B,CAAC,CAAC,EAC/EC,qBAAqB,CAAC,CAAC,EACvBpI,MAAM,EAAEL,kBAAkB,GAAGiH,cAAc,CAAC5G,MAAM,CAACL,kBAAkB,CAAC,CAAC4E,UAAU,GAAG,EAAE,EACtFvE,MAAM,EAAEyF,iBAAiB,GAAG4C,wBAAwB,CAACrI,MAAM,CAAC,GAAG,EAAE,EACjEA,MAAM,EAAEsI,qBAAqB,GAAGnB,yBAAyB,CAAC,CAAC,CAAC5C,UAAU,GAAG,EAAE,EAC3EvE,MAAM,EAAEuI,qBAAqB,GAAGnB,mBAAmB,CAAC,CAAC,CAAC7C,UAAU,GAAG,EAAE,EACrEiE,wBAAwB,CAAC,CAAC;MAElC,CAAC;IACL;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,OAAOC,QAAQA,CAACxI,MAAM,EAAE;MACpB,OAAO;QACH4H,QAAQ,EAAEH,YAAY;QACtBvH,SAAS,EAAE,CAAC;UAAE2D,OAAO,EAAEhP,MAAM;UAAEiP,KAAK,EAAE,IAAI;UAAEC,QAAQ,EAAE/D;QAAO,CAAC;MAClE,CAAC;IACL;IACA,OAAO/F,IAAI,YAAAwO,qBAAAtO,iBAAA;MAAA,YAAAA,iBAAA,IAAyFsN,YAAY;IAAA;IAChH,OAAOiB,IAAI,kBAhrC+EvW,EAAE,CAAAwW,gBAAA;MAAAjO,IAAA,EAgrCS+M;IAAY;IACjH,OAAOmB,IAAI,kBAjrC+EzW,EAAE,CAAA0W,gBAAA;EAkrChG;EAAC,OApFKpB,YAAY;AAAA;AAqFlB;EAAA,QAAAnP,SAAA,oBAAAA,SAAA;AAAA;AAOA;AACA;AACA;AACA;AACA,SAAS6P,qBAAqBA,CAAA,EAAG;EAC7B,OAAO;IACHtE,OAAO,EAAEvC,eAAe;IACxB2C,UAAU,EAAEA,CAAA,KAAM;MACd,MAAMvC,gBAAgB,GAAG/N,MAAM,CAAC3B,gBAAgB,CAAC;MACjD,MAAM2P,IAAI,GAAGhO,MAAM,CAACI,MAAM,CAAC;MAC3B,MAAMgM,MAAM,GAAGpM,MAAM,CAACoB,oBAAoB,CAAC;MAC3C,MAAM0M,WAAW,GAAG9N,MAAM,CAACiB,qBAAqB,CAAC;MACjD,MAAM4M,aAAa,GAAG7N,MAAM,CAACgB,aAAa,CAAC;MAC3C,IAAIoL,MAAM,CAAC+I,YAAY,EAAE;QACrBpH,gBAAgB,CAACqH,SAAS,CAAChJ,MAAM,CAAC+I,YAAY,CAAC;MACnD;MACA,OAAO,IAAIvH,cAAc,CAACC,aAAa,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,IAAI,EAAE5B,MAAM,CAAC;IACzF;EACJ,CAAC;AACL;AACA;AACA;AACA,SAASkI,2BAA2BA,CAAA,EAAG;EACnC,OAAO;IAAEpE,OAAO,EAAE9R,gBAAgB;IAAEgV,QAAQ,EAAEjV;EAAqB,CAAC;AACxE;AACA;AACA;AACA,SAASoW,2BAA2BA,CAAA,EAAG;EACnC,OAAO;IAAErE,OAAO,EAAE9R,gBAAgB;IAAEgV,QAAQ,EAAE7U;EAAqB,CAAC;AACxE;AACA,SAAS4V,mBAAmBA,CAACzR,MAAM,EAAE;EACjC,IAAIA,MAAM,EAAE;IACR,MAAM,IAAIhE,aAAa,CAAC,IAAI,CAAC,8CAA8C,4GAA4G,GACnL,kEAAkE,CAAC;EAC3E;EACA,OAAO,SAAS;AACpB;AACA;AACA;AACA,SAAS+V,wBAAwBA,CAACrI,MAAM,EAAE;EACtC,OAAO,CACHA,MAAM,CAACyF,iBAAiB,KAAK,UAAU,GAAGY,6BAA6B,CAAC,CAAC,CAAC9B,UAAU,GAAG,EAAE,EACzFvE,MAAM,CAACyF,iBAAiB,KAAK,iBAAiB,GACxCM,oCAAoC,CAAC,CAAC,CAACxB,UAAU,GACjD,EAAE,CACX;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM0E,kBAAkB,gBAAG,IAAI5V,cAAc,CAAC,OAAOkF,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAG,oBAAoB,GAAG,EAAE,CAAC;AACxH,SAASiQ,wBAAwBA,CAAA,EAAG;EAChC,OAAO;EACH;EACA;EACA;IAAE1E,OAAO,EAAEmF,kBAAkB;IAAE/E,UAAU,EAAEG;EAAqB,CAAC,EACjE;IAAEP,OAAO,EAAErQ,sBAAsB;IAAEsQ,KAAK,EAAE,IAAI;IAAE8C,WAAW,EAAEoC;EAAmB,CAAC,CACpF;AACL;AAEA,SAAS1J,YAAY,EAAET,iBAAiB,EAAED,kBAAkB,EAAEoK,kBAAkB,EAAExB,gBAAgB,EAAEpR,UAAU,EAAEoF,gBAAgB,EAAEiM,YAAY,EAAEjI,eAAe,EAAEoE,aAAa,EAAEmB,aAAa,EAAEmC,yBAAyB,EAAEZ,gBAAgB,EAAEF,6BAA6B,EAAEN,oCAAoC,EAAEgB,gBAAgB,EAAE9B,qBAAqB,EAAEgC,0BAA0B,EAAEL,cAAc,EAAEE,gBAAgB,EAAEM,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}