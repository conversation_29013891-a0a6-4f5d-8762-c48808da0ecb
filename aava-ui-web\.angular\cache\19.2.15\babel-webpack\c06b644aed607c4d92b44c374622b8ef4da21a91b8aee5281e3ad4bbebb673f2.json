{"ast": null, "code": "import identity from \"../identity.js\";\nimport stream from \"../stream.js\";\nimport pathArea from \"./area.js\";\nimport pathBounds from \"./bounds.js\";\nimport pathCentroid from \"./centroid.js\";\nimport PathContext from \"./context.js\";\nimport pathMeasure from \"./measure.js\";\nimport PathString from \"./string.js\";\nexport default function (projection, context) {\n  let digits = 3,\n    pointRadius = 4.5,\n    projectionStream,\n    contextStream;\n  function path(object) {\n    if (object) {\n      if (typeof pointRadius === \"function\") contextStream.pointRadius(+pointRadius.apply(this, arguments));\n      stream(object, projectionStream(contextStream));\n    }\n    return contextStream.result();\n  }\n  path.area = function (object) {\n    stream(object, projectionStream(pathArea));\n    return pathArea.result();\n  };\n  path.measure = function (object) {\n    stream(object, projectionStream(pathMeasure));\n    return pathMeasure.result();\n  };\n  path.bounds = function (object) {\n    stream(object, projectionStream(pathBounds));\n    return pathBounds.result();\n  };\n  path.centroid = function (object) {\n    stream(object, projectionStream(pathCentroid));\n    return pathCentroid.result();\n  };\n  path.projection = function (_) {\n    if (!arguments.length) return projection;\n    projectionStream = _ == null ? (projection = null, identity) : (projection = _).stream;\n    return path;\n  };\n  path.context = function (_) {\n    if (!arguments.length) return context;\n    contextStream = _ == null ? (context = null, new PathString(digits)) : new PathContext(context = _);\n    if (typeof pointRadius !== \"function\") contextStream.pointRadius(pointRadius);\n    return path;\n  };\n  path.pointRadius = function (_) {\n    if (!arguments.length) return pointRadius;\n    pointRadius = typeof _ === \"function\" ? _ : (contextStream.pointRadius(+_), +_);\n    return path;\n  };\n  path.digits = function (_) {\n    if (!arguments.length) return digits;\n    if (_ == null) digits = null;else {\n      const d = Math.floor(_);\n      if (!(d >= 0)) throw new RangeError(`invalid digits: ${_}`);\n      digits = d;\n    }\n    if (context === null) contextStream = new PathString(digits);\n    return path;\n  };\n  return path.projection(projection).digits(digits).context(context);\n}", "map": {"version": 3, "names": ["identity", "stream", "pathArea", "pathBounds", "pathCentroid", "PathContext", "pathMeasure", "PathString", "projection", "context", "digits", "pointRadius", "projectionStream", "contextStream", "path", "object", "apply", "arguments", "result", "area", "measure", "bounds", "centroid", "_", "length", "d", "Math", "floor", "RangeError"], "sources": ["C:/console/aava-ui-web/node_modules/d3-geo/src/path/index.js"], "sourcesContent": ["import identity from \"../identity.js\";\nimport stream from \"../stream.js\";\nimport pathArea from \"./area.js\";\nimport pathBounds from \"./bounds.js\";\nimport pathCentroid from \"./centroid.js\";\nimport PathContext from \"./context.js\";\nimport pathMeasure from \"./measure.js\";\nimport PathString from \"./string.js\";\n\nexport default function(projection, context) {\n  let digits = 3,\n      pointRadius = 4.5,\n      projectionStream,\n      contextStream;\n\n  function path(object) {\n    if (object) {\n      if (typeof pointRadius === \"function\") contextStream.pointRadius(+pointRadius.apply(this, arguments));\n      stream(object, projectionStream(contextStream));\n    }\n    return contextStream.result();\n  }\n\n  path.area = function(object) {\n    stream(object, projectionStream(pathArea));\n    return pathArea.result();\n  };\n\n  path.measure = function(object) {\n    stream(object, projectionStream(pathMeasure));\n    return pathMeasure.result();\n  };\n\n  path.bounds = function(object) {\n    stream(object, projectionStream(pathBounds));\n    return pathBounds.result();\n  };\n\n  path.centroid = function(object) {\n    stream(object, projectionStream(pathCentroid));\n    return pathCentroid.result();\n  };\n\n  path.projection = function(_) {\n    if (!arguments.length) return projection;\n    projectionStream = _ == null ? (projection = null, identity) : (projection = _).stream;\n    return path;\n  };\n\n  path.context = function(_) {\n    if (!arguments.length) return context;\n    contextStream = _ == null ? (context = null, new PathString(digits)) : new PathContext(context = _);\n    if (typeof pointRadius !== \"function\") contextStream.pointRadius(pointRadius);\n    return path;\n  };\n\n  path.pointRadius = function(_) {\n    if (!arguments.length) return pointRadius;\n    pointRadius = typeof _ === \"function\" ? _ : (contextStream.pointRadius(+_), +_);\n    return path;\n  };\n\n  path.digits = function(_) {\n    if (!arguments.length) return digits;\n    if (_ == null) digits = null;\n    else {\n      const d = Math.floor(_);\n      if (!(d >= 0)) throw new RangeError(`invalid digits: ${_}`);\n      digits = d;\n    }\n    if (context === null) contextStream = new PathString(digits);\n    return path;\n  };\n\n  return path.projection(projection).digits(digits).context(context);\n}\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,QAAQ,MAAM,WAAW;AAChC,OAAOC,UAAU,MAAM,aAAa;AACpC,OAAOC,YAAY,MAAM,eAAe;AACxC,OAAOC,WAAW,MAAM,cAAc;AACtC,OAAOC,WAAW,MAAM,cAAc;AACtC,OAAOC,UAAU,MAAM,aAAa;AAEpC,eAAe,UAASC,UAAU,EAAEC,OAAO,EAAE;EAC3C,IAAIC,MAAM,GAAG,CAAC;IACVC,WAAW,GAAG,GAAG;IACjBC,gBAAgB;IAChBC,aAAa;EAEjB,SAASC,IAAIA,CAACC,MAAM,EAAE;IACpB,IAAIA,MAAM,EAAE;MACV,IAAI,OAAOJ,WAAW,KAAK,UAAU,EAAEE,aAAa,CAACF,WAAW,CAAC,CAACA,WAAW,CAACK,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,CAAC;MACrGhB,MAAM,CAACc,MAAM,EAAEH,gBAAgB,CAACC,aAAa,CAAC,CAAC;IACjD;IACA,OAAOA,aAAa,CAACK,MAAM,CAAC,CAAC;EAC/B;EAEAJ,IAAI,CAACK,IAAI,GAAG,UAASJ,MAAM,EAAE;IAC3Bd,MAAM,CAACc,MAAM,EAAEH,gBAAgB,CAACV,QAAQ,CAAC,CAAC;IAC1C,OAAOA,QAAQ,CAACgB,MAAM,CAAC,CAAC;EAC1B,CAAC;EAEDJ,IAAI,CAACM,OAAO,GAAG,UAASL,MAAM,EAAE;IAC9Bd,MAAM,CAACc,MAAM,EAAEH,gBAAgB,CAACN,WAAW,CAAC,CAAC;IAC7C,OAAOA,WAAW,CAACY,MAAM,CAAC,CAAC;EAC7B,CAAC;EAEDJ,IAAI,CAACO,MAAM,GAAG,UAASN,MAAM,EAAE;IAC7Bd,MAAM,CAACc,MAAM,EAAEH,gBAAgB,CAACT,UAAU,CAAC,CAAC;IAC5C,OAAOA,UAAU,CAACe,MAAM,CAAC,CAAC;EAC5B,CAAC;EAEDJ,IAAI,CAACQ,QAAQ,GAAG,UAASP,MAAM,EAAE;IAC/Bd,MAAM,CAACc,MAAM,EAAEH,gBAAgB,CAACR,YAAY,CAAC,CAAC;IAC9C,OAAOA,YAAY,CAACc,MAAM,CAAC,CAAC;EAC9B,CAAC;EAEDJ,IAAI,CAACN,UAAU,GAAG,UAASe,CAAC,EAAE;IAC5B,IAAI,CAACN,SAAS,CAACO,MAAM,EAAE,OAAOhB,UAAU;IACxCI,gBAAgB,GAAGW,CAAC,IAAI,IAAI,IAAIf,UAAU,GAAG,IAAI,EAAER,QAAQ,IAAI,CAACQ,UAAU,GAAGe,CAAC,EAAEtB,MAAM;IACtF,OAAOa,IAAI;EACb,CAAC;EAEDA,IAAI,CAACL,OAAO,GAAG,UAASc,CAAC,EAAE;IACzB,IAAI,CAACN,SAAS,CAACO,MAAM,EAAE,OAAOf,OAAO;IACrCI,aAAa,GAAGU,CAAC,IAAI,IAAI,IAAId,OAAO,GAAG,IAAI,EAAE,IAAIF,UAAU,CAACG,MAAM,CAAC,IAAI,IAAIL,WAAW,CAACI,OAAO,GAAGc,CAAC,CAAC;IACnG,IAAI,OAAOZ,WAAW,KAAK,UAAU,EAAEE,aAAa,CAACF,WAAW,CAACA,WAAW,CAAC;IAC7E,OAAOG,IAAI;EACb,CAAC;EAEDA,IAAI,CAACH,WAAW,GAAG,UAASY,CAAC,EAAE;IAC7B,IAAI,CAACN,SAAS,CAACO,MAAM,EAAE,OAAOb,WAAW;IACzCA,WAAW,GAAG,OAAOY,CAAC,KAAK,UAAU,GAAGA,CAAC,IAAIV,aAAa,CAACF,WAAW,CAAC,CAACY,CAAC,CAAC,EAAE,CAACA,CAAC,CAAC;IAC/E,OAAOT,IAAI;EACb,CAAC;EAEDA,IAAI,CAACJ,MAAM,GAAG,UAASa,CAAC,EAAE;IACxB,IAAI,CAACN,SAAS,CAACO,MAAM,EAAE,OAAOd,MAAM;IACpC,IAAIa,CAAC,IAAI,IAAI,EAAEb,MAAM,GAAG,IAAI,CAAC,KACxB;MACH,MAAMe,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACJ,CAAC,CAAC;MACvB,IAAI,EAAEE,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,IAAIG,UAAU,CAAC,mBAAmBL,CAAC,EAAE,CAAC;MAC3Db,MAAM,GAAGe,CAAC;IACZ;IACA,IAAIhB,OAAO,KAAK,IAAI,EAAEI,aAAa,GAAG,IAAIN,UAAU,CAACG,MAAM,CAAC;IAC5D,OAAOI,IAAI;EACb,CAAC;EAED,OAAOA,IAAI,CAACN,UAAU,CAACA,UAAU,CAAC,CAACE,MAAM,CAACA,MAAM,CAAC,CAACD,OAAO,CAACA,OAAO,CAAC;AACpE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}