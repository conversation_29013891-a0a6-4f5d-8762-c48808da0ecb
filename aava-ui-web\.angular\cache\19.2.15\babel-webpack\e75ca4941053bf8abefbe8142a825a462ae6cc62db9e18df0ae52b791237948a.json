{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, Input, Output, EventEmitter, ViewChild, ContentChild } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormControl, ReactiveFormsModule } from '@angular/forms';\nimport { ButtonComponent, AvaTextboxComponent, AvaTextareaComponent, IconComponent } from '@ava/play-comp-library';\nlet CanvasBoardComponent = class CanvasBoardComponent {\n  cdr;\n  canvasContainer;\n  nodeTemplate;\n  // Inputs\n  nodes = [];\n  edges = [];\n  showGrid = true;\n  enablePan = false; // Disabled by default\n  enableZoom = false; // Disabled by default\n  enableConnections = true;\n  minZoom = 0.1;\n  maxZoom = 2;\n  connectionColor = '#9ca3af'; // Grey color for connections\n  fallbackMessage = 'Drop items here to get started';\n  navigationHints = ['Select toolbar options to enable canvas interactions', 'Alt + Drag to pan canvas (when enabled)', 'Mouse wheel to zoom (when enabled)', 'Space to reset view'];\n  showToolbar = true;\n  primaryButtonText = 'Execute';\n  primaryButtonIcon = '';\n  enableUndo = true;\n  enableRedo = true;\n  enableReset = true;\n  showCanvasTools = true;\n  enableGridToggle = true;\n  enablePanMode = true;\n  enableSelectionMode = true;\n  enableZoomControls = true;\n  isExecuteMode = false;\n  showLeftActions = true;\n  mouseInteractionsEnabled = true;\n  // Built-in input fields configuration\n  showHeaderInputs = false;\n  inputFieldsConfig = {};\n  // Input properties for initial values\n  initialAgentName = '';\n  initialAgentDetails = '';\n  initialMetadata = {\n    org: '',\n    domain: '',\n    project: '',\n    team: ''\n  };\n  // Built-in metadata dropdown state\n  isMetadataDropdownOpen = false;\n  metadataStatus = 'Metadata Information not saved';\n  // Agent details dropdown state\n  isAgentDetailsDropdownOpen = false;\n  // Form controls for built-in inputs\n  agentNameControl = new FormControl('');\n  agentTypeControl = new FormControl('individual');\n  agentTypeDisplayControl = new FormControl('individual');\n  // Agent details form controls\n  agentDetailNameControl = new FormControl('');\n  agentDetailControl = new FormControl('');\n  // Metadata form controls\n  orgControl = new FormControl('');\n  domainControl = new FormControl('');\n  projectControl = new FormControl('');\n  teamControl = new FormControl('');\n  // Dropdown options\n  dropdownValues = {\n    org: [{\n      value: 'ascendion',\n      label: 'Ascendion'\n    }, {\n      value: 'company2',\n      label: 'Company 2'\n    }, {\n      value: 'company3',\n      label: 'Company 3'\n    }],\n    domain: [],\n    project: [],\n    team: []\n  };\n  // Outputs\n  nodeAdded = new EventEmitter();\n  nodeRemoved = new EventEmitter();\n  nodeMoved = new EventEmitter();\n  nodeSelected = new EventEmitter();\n  nodeDoubleClicked = new EventEmitter();\n  connectionStarted = new EventEmitter();\n  connectionCreated = new EventEmitter();\n  canvasDropped = new EventEmitter();\n  viewportChanged = new EventEmitter();\n  undoAction = new EventEmitter();\n  redoAction = new EventEmitter();\n  resetAction = new EventEmitter();\n  primaryButtonClicked = new EventEmitter();\n  stateChanged = new EventEmitter();\n  agentNameChanged = new EventEmitter();\n  agentTypeChanged = new EventEmitter();\n  metadataChanged = new EventEmitter();\n  agentDetailsChanged = new EventEmitter();\n  // Internal state\n  selectedNodeId = null;\n  tempConnection = {\n    isActive: false\n  };\n  nodeConnectionPoints = {};\n  viewport = {\n    zoom: 1,\n    x: 0,\n    y: 0,\n    isDragging: false,\n    lastMouseX: 0,\n    lastMouseY: 0\n  };\n  // Performance optimization for connection updates\n  updateConnectionPointsFrame = null;\n  // Internal history management\n  history = [];\n  historyIndex = -1;\n  maxHistorySize = 50;\n  isRestoringState = false;\n  // Canvas tool states\n  canvasMode = 'select'; // Start with select mode\n  showGridDots = true;\n  // Mouse function controls\n  // mouseInteractionsEnabled: boolean = true; // Enable mouse interactions by default\n  // Toolbar is now fixed at bottom center - no drag state needed\n  constructor(cdr) {\n    this.cdr = cdr;\n  }\n  ngOnInit() {\n    // Set initial values\n    this.setInitialValues();\n    // Subscribe to agent name changes\n    this.agentNameControl.valueChanges.subscribe(value => {\n      if (value !== null && value !== undefined) {\n        this.agentNameChanged.emit(value);\n      }\n    });\n    // Subscribe to agent type changes\n    this.agentTypeControl.valueChanges.subscribe(value => {\n      if (value !== null && value !== undefined) {\n        this.agentTypeChanged.emit(value);\n      }\n    });\n  }\n  setInitialValues() {\n    // Set agent name\n    if (this.initialAgentName) {\n      this.agentNameControl.setValue(this.initialAgentName, {\n        emitEvent: false\n      });\n    }\n    // Set agent details\n    if (this.initialAgentName) {\n      this.agentDetailNameControl.setValue(this.initialAgentName, {\n        emitEvent: false\n      });\n    }\n    if (this.initialAgentDetails) {\n      this.agentDetailControl.setValue(this.initialAgentDetails, {\n        emitEvent: false\n      });\n    }\n    // Set metadata\n    if (this.initialMetadata) {\n      this.orgControl.setValue(this.initialMetadata.org, {\n        emitEvent: false\n      });\n      this.domainControl.setValue(this.initialMetadata.domain, {\n        emitEvent: false\n      });\n      this.projectControl.setValue(this.initialMetadata.project, {\n        emitEvent: false\n      });\n      this.teamControl.setValue(this.initialMetadata.team, {\n        emitEvent: false\n      });\n    }\n  }\n  // TrackBy function for nodes to maintain DOM element identity\n  trackByNodeId(index, node) {\n    return node.id;\n  }\n  ngOnChanges(changes) {\n    // Update initial values when inputs change\n    if (changes['initialAgentName'] || changes['initialAgentDetails'] || changes['initialMetadata']) {\n      this.setInitialValues();\n    }\n    // Update internal state when inputs change (but not during state restoration)\n    if (!this.isRestoringState) {\n      if (changes.nodes && changes.nodes.currentValue) {\n        console.log('🔗 ngOnChanges - Received new nodes:', changes.nodes.currentValue.map(n => ({\n          id: n.id,\n          position: n.position\n        })));\n        this.nodes = [...changes.nodes.currentValue];\n      }\n      if (changes.edges && changes.edges.currentValue) {\n        console.log('🔗 ngOnChanges - Received new edges:', changes.edges.currentValue.map(e => ({\n          id: e.id,\n          source: e.source,\n          target: e.target\n        })));\n        this.edges = [...changes.edges.currentValue];\n      }\n      // Update connection points when nodes change - IMMEDIATE\n      if (changes.nodes) {\n        console.log('🔗 ngOnChanges - Updating for node changes');\n        this.updateNodeConnectionPoints();\n        this.updateEdgePaths();\n        // Force additional update after DOM settles\n        setTimeout(() => this.updateNodeConnectionPoints(), 0);\n      }\n      if (changes.edges) {\n        console.log('🔗 ngOnChanges - Updating for edge changes');\n        this.updateEdgePaths();\n      }\n    }\n    // Update connection points when execute mode changes\n    if (changes['isExecuteMode']) {\n      // Toolbar is now fixed at bottom center via CSS\n      this.updateNodeConnectionPoints();\n    }\n    if (changes['mouseInteractionsEnabled']) {\n      this.mouseInteractionsEnabled = changes['mouseInteractionsEnabled'].currentValue;\n    }\n  }\n  ngAfterViewInit() {\n    this.setupCanvasNavigation();\n    // Use requestAnimationFrame for smooth initialization\n    requestAnimationFrame(() => {\n      this.updateNodeConnectionPoints();\n      // Initialize history with current state\n      this.saveToHistory();\n      // Toolbar is now fixed at bottom center via CSS\n      // Set initial cursor to default arrow\n      this.setSelectMode();\n    });\n  }\n  ngOnDestroy() {\n    // Cleanup event listeners\n    document.removeEventListener('keydown', this.handleKeyDown);\n    // Cancel any pending animation frame\n    if (this.updateConnectionPointsFrame) {\n      cancelAnimationFrame(this.updateConnectionPointsFrame);\n      this.updateConnectionPointsFrame = null;\n    }\n  }\n  setupCanvasNavigation() {\n    const element = this.canvasContainer?.nativeElement;\n    if (element) {\n      document.addEventListener('keydown', this.handleKeyDown.bind(this));\n      element.addEventListener('mouseup', () => {\n        if (this.viewport.isDragging) {\n          this.updateNodeConnectionPoints();\n        }\n      });\n    }\n  }\n  handleKeyDown = event => {\n    if (event.key === ' ') {\n      // Only reset viewport if the user is not typing in an input field\n      const target = event.target;\n      const isInputField = target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.contentEditable === 'true' || target.closest('ava-textbox') || target.closest('ava-textarea') || target.closest('.form-field') || target.closest('.input-container') || target.closest('.chat-input') || target.closest('textarea') || target.closest('input');\n      if (!isInputField) {\n        this.resetViewport();\n        event.preventDefault();\n      }\n    }\n  };\n  resetViewport() {\n    this.viewport = {\n      zoom: 1,\n      x: 0,\n      y: 0,\n      isDragging: false,\n      lastMouseX: 0,\n      lastMouseY: 0\n    };\n    this.updateNodeConnectionPoints();\n    this.viewportChanged.emit(this.viewport);\n    this.cdr.detectChanges();\n  }\n  onDragOver(event) {\n    if (!this.enableConnections || !this.mouseInteractionsEnabled) return;\n    event.preventDefault();\n    if (event.dataTransfer) {\n      event.dataTransfer.dropEffect = 'move';\n    }\n  }\n  onDrop(event) {\n    if (!this.mouseInteractionsEnabled) return;\n    event.preventDefault();\n    // Save history before adding new node\n    this.saveHistoryBeforeAction();\n    const canvasBounds = this.canvasContainer.nativeElement.getBoundingClientRect();\n    const position = {\n      x: (event.clientX - canvasBounds.left - this.viewport.x) / this.viewport.zoom,\n      y: (event.clientY - canvasBounds.top - this.viewport.y) / this.viewport.zoom\n    };\n    const safePosition = {\n      x: Math.max(0, position.x),\n      y: Math.max(0, position.y)\n    };\n    this.canvasDropped.emit({\n      event,\n      position: safePosition\n    });\n  }\n  onNodeSelected(nodeId) {\n    this.selectedNodeId = nodeId;\n    this.nodeSelected.emit(nodeId);\n  }\n  onNodeDoubleClicked(nodeId) {\n    this.nodeDoubleClicked.emit(nodeId);\n  }\n  onNodeMoved(data) {\n    // Save history before moving node\n    this.saveHistoryBeforeAction();\n    // Update node position in internal state\n    const nodeIndex = this.nodes.findIndex(node => node.id === data.nodeId);\n    if (nodeIndex !== -1) {\n      this.nodes[nodeIndex] = {\n        ...this.nodes[nodeIndex],\n        position: data.position\n      };\n    }\n    // CRITICAL: Update edge paths immediately when node moves\n    console.log('🔗 Node moved - updating edge paths for:', data.nodeId);\n    this.updateEdgePaths();\n    this.nodeMoved.emit(data);\n    this.updateNodeConnectionPoints();\n    // Force immediate change detection to update connections\n    this.cdr.detectChanges();\n  }\n  onStartConnection(data) {\n    if (!this.enableConnections) return;\n    const node = this.nodes.find(n => n.id === data.nodeId);\n    if (!node) return;\n    // Enhanced connection starting - use mouse position on the node as starting point\n    const canvasRect = this.canvasContainer.nativeElement.getBoundingClientRect();\n    // Calculate the exact mouse position in canvas coordinates\n    const mouseX = (data.event.clientX - canvasRect.left - this.viewport.x) / this.viewport.zoom;\n    const mouseY = (data.event.clientY - canvasRect.top - this.viewport.y) / this.viewport.zoom;\n    // Use the mouse position as the starting point for more natural connections\n    let handleX = mouseX;\n    let handleY = mouseY;\n    // Get the node's connection points for fallback\n    const connectionPoints = this.nodeConnectionPoints[data.nodeId];\n    if (connectionPoints) {\n      // If we have calculated connection points, use the nearest edge point\n      const nodeElement = document.querySelector(`[data-node-id=\"${data.nodeId}\"]`);\n      if (nodeElement) {\n        const rect = nodeElement.getBoundingClientRect();\n        const nodeX = (rect.left - canvasRect.left - this.viewport.x) / this.viewport.zoom;\n        const nodeY = (rect.top - canvasRect.top - this.viewport.y) / this.viewport.zoom;\n        const nodeWidth = rect.width / this.viewport.zoom;\n        const nodeHeight = rect.height / this.viewport.zoom;\n        // Determine which edge of the node is closest to the mouse\n        const relativeX = mouseX - nodeX;\n        const relativeY = mouseY - nodeY;\n        // Clamp to node edges for clean connection start points\n        if (relativeX <= 0) {\n          // Left edge\n          handleX = nodeX;\n          handleY = Math.max(nodeY, Math.min(nodeY + nodeHeight, mouseY));\n        } else if (relativeX >= nodeWidth) {\n          // Right edge\n          handleX = nodeX + nodeWidth;\n          handleY = Math.max(nodeY, Math.min(nodeY + nodeHeight, mouseY));\n        } else if (relativeY <= 0) {\n          // Top edge\n          handleX = Math.max(nodeX, Math.min(nodeX + nodeWidth, mouseX));\n          handleY = nodeY;\n        } else if (relativeY >= nodeHeight) {\n          // Bottom edge\n          handleX = Math.max(nodeX, Math.min(nodeX + nodeWidth, mouseX));\n          handleY = nodeY + nodeHeight;\n        }\n      }\n    }\n    this.tempConnection = {\n      isActive: true,\n      sourceNodeId: data.nodeId,\n      sourceHandleType: data.handleType,\n      sourceX: handleX,\n      sourceY: handleY,\n      targetX: mouseX,\n      targetY: mouseY\n    };\n    this.connectionStarted.emit(data);\n  }\n  onDeleteNode(nodeId) {\n    // Save history before deleting node\n    this.saveHistoryBeforeAction();\n    // Remove node from internal state\n    this.nodes = this.nodes.filter(node => node.id !== nodeId);\n    // Remove any edges connected to this node\n    this.edges = this.edges.filter(edge => edge.source !== nodeId && edge.target !== nodeId);\n    this.nodeRemoved.emit(nodeId);\n    if (this.selectedNodeId === nodeId) {\n      this.selectedNodeId = null;\n    }\n    this.updateNodeConnectionPoints();\n  }\n  onCanvasMouseMove(event) {\n    // Handle canvas panning\n    if (this.viewport.isDragging && this.enablePan) {\n      const deltaX = event.clientX - this.viewport.lastMouseX;\n      const deltaY = event.clientY - this.viewport.lastMouseY;\n      this.viewport.x += deltaX;\n      this.viewport.y += deltaY;\n      this.viewport.lastMouseX = event.clientX;\n      this.viewport.lastMouseY = event.clientY;\n      this.cdr.detectChanges();\n      return;\n    }\n    if (!this.tempConnection.isActive) return;\n    // Update the temporary connection endpoint\n    const canvasRect = this.canvasContainer.nativeElement.getBoundingClientRect();\n    const targetX = (event.clientX - canvasRect.left - this.viewport.x) / this.viewport.zoom;\n    const targetY = (event.clientY - canvasRect.top - this.viewport.y) / this.viewport.zoom;\n    this.tempConnection = {\n      ...this.tempConnection,\n      targetX,\n      targetY\n    };\n    this.cdr.detectChanges();\n  }\n  onCanvasWheel(event) {\n    if (!this.enableZoom || !this.mouseInteractionsEnabled) return;\n    event.preventDefault();\n    const delta = -event.deltaY;\n    const zoomSpeed = 0.001;\n    const newZoom = Math.max(this.minZoom, Math.min(this.maxZoom, this.viewport.zoom + delta * zoomSpeed));\n    if (newZoom !== this.viewport.zoom) {\n      const rect = this.canvasContainer.nativeElement.getBoundingClientRect();\n      const mouseX = event.clientX - rect.left;\n      const mouseY = event.clientY - rect.top;\n      const zoomRatio = newZoom / this.viewport.zoom;\n      const newX = mouseX - (mouseX - this.viewport.x) * zoomRatio;\n      const newY = mouseY - (mouseY - this.viewport.y) * zoomRatio;\n      this.viewport.zoom = newZoom;\n      this.viewport.x = newX;\n      this.viewport.y = newY;\n      this.updateNodeConnectionPoints();\n      this.viewportChanged.emit(this.viewport);\n      this.cdr.detectChanges();\n    }\n  }\n  onCanvasMouseDown(event) {\n    if (!this.enablePan || !this.mouseInteractionsEnabled) return;\n    // Start canvas dragging when:\n    // 1. Middle mouse button is pressed\n    // 2. Alt+left click\n    // 3. Pan mode is active and left click\n    if (event.button === 1 || event.button === 0 && event.altKey || event.button === 0 && this.canvasMode === 'pan') {\n      this.viewport.isDragging = true;\n      this.viewport.lastMouseX = event.clientX;\n      this.viewport.lastMouseY = event.clientY;\n      // Update cursor during drag\n      if (this.canvasContainer) {\n        this.canvasContainer.nativeElement.style.cursor = 'grabbing';\n      }\n      event.preventDefault();\n    }\n  }\n  onCanvasMouseUp(event) {\n    this.viewport.isDragging = false;\n    // Restore cursor based on current mode\n    if (this.canvasContainer) {\n      this.canvasContainer.nativeElement.style.cursor = this.canvasMode === 'pan' ? 'grab' : 'default';\n    }\n    if (!this.tempConnection.isActive || !this.enableConnections) return;\n    const target = event.target;\n    // Enhanced connection logic - allow connections to any part of a node\n    const nodeElement = target.closest('[data-node-id]');\n    if (nodeElement && this.tempConnection.sourceNodeId) {\n      const sourceNodeId = this.tempConnection.sourceNodeId;\n      const targetNodeId = nodeElement.getAttribute('data-node-id');\n      // Allow connections between different nodes\n      if (targetNodeId && sourceNodeId !== targetNodeId) {\n        // Check if connection already exists to prevent duplicates\n        const existingConnection = this.edges.find(edge => edge.source === sourceNodeId && edge.target === targetNodeId || edge.source === targetNodeId && edge.target === sourceNodeId);\n        if (!existingConnection) {\n          // Save history before creating connection\n          this.saveHistoryBeforeAction();\n          const newEdge = {\n            id: `${sourceNodeId}-${targetNodeId}`,\n            source: sourceNodeId,\n            target: targetNodeId,\n            animated: false // Beautiful static connections\n          };\n          // Add edge to internal state\n          this.edges.push(newEdge);\n          this.connectionCreated.emit(newEdge);\n        }\n      }\n    }\n    this.tempConnection = {\n      isActive: false\n    };\n  }\n  updateNodeConnectionPoints() {\n    if (this.updateConnectionPointsFrame) {\n      cancelAnimationFrame(this.updateConnectionPointsFrame);\n    }\n    // SIMPLIFIED: Immediate update for consistent connection rendering\n    this.updateConnectionPointsFrame = requestAnimationFrame(() => {\n      this.nodeConnectionPoints = {};\n      for (const node of this.nodes) {\n        const nodeElement = document.querySelector(`[data-node-id=\"${node.id}\"]`);\n        // Calculate connection points even for nodes outside viewport\n        let nodeX, nodeY, nodeWidth, nodeHeight;\n        if (nodeElement) {\n          // Node is in DOM - use actual measurements\n          const rect = nodeElement.getBoundingClientRect();\n          const canvasRect = this.canvasContainer?.nativeElement.getBoundingClientRect();\n          if (!canvasRect) continue;\n          // In execute mode, always use the actual DOM position since CSS transforms the layout\n          if (this.isExecuteMode) {\n            // Use actual DOM position without viewport adjustments for execute mode\n            nodeX = rect.left - canvasRect.left;\n            nodeY = rect.top - canvasRect.top;\n            nodeWidth = rect.width;\n            nodeHeight = rect.height;\n          } else {\n            // Build mode - use viewport-adjusted coordinates\n            nodeX = (rect.left - canvasRect.left - this.viewport.x) / this.viewport.zoom;\n            nodeY = (rect.top - canvasRect.top - this.viewport.y) / this.viewport.zoom;\n            nodeWidth = rect.width / this.viewport.zoom;\n            nodeHeight = rect.height / this.viewport.zoom;\n          }\n        } else {\n          // Node is outside viewport - use node position data\n          nodeX = node.position.x;\n          nodeY = node.position.y;\n          // Use default dimensions based on mode\n          nodeWidth = this.isExecuteMode ? 55 : 90; // Execute mode nodes are 55px (circular with border)\n          nodeHeight = this.isExecuteMode ? 55 : 48; // Execute mode nodes are 55px (circular with border)\n        }\n        if (this.isExecuteMode) {\n          // Execute mode - icon center to icon center connections\n          const centerX = nodeX + nodeWidth / 2;\n          const centerY = nodeY + nodeHeight / 2;\n          // All connection points are at the center of the circular icon for icon-to-icon connections\n          this.nodeConnectionPoints[node.id] = {\n            top: {\n              x: centerX,\n              y: centerY\n            },\n            right: {\n              x: centerX,\n              y: centerY\n            },\n            bottom: {\n              x: centerX,\n              y: centerY\n            },\n            left: {\n              x: centerX,\n              y: centerY\n            }\n          };\n          console.log(`Execute mode node ${node.id}: center(${centerX}, ${centerY})`);\n        } else {\n          // Build mode - account for node structure and padding\n          // The node has 10px padding and the icon is 40px with center at 30px from left edge\n          const iconCenterX = nodeX + 30; // 10px padding + 20px to icon center\n          const centerY = nodeY + nodeHeight / 2; // Vertical center\n          // Create connection points optimized for the node structure\n          this.nodeConnectionPoints[node.id] = {\n            top: {\n              x: iconCenterX,\n              y: nodeY\n            },\n            right: {\n              x: nodeX + nodeWidth,\n              y: centerY\n            },\n            bottom: {\n              x: iconCenterX,\n              y: nodeY + nodeHeight\n            },\n            left: {\n              x: nodeX,\n              y: centerY\n            }\n          };\n        }\n      }\n      // Trigger change detection immediately for consistent rendering\n      this.cdr.detectChanges();\n      this.updateConnectionPointsFrame = null;\n    });\n  }\n  // REMOVED getEdgePath getter to prevent infinite loops\n  // The template now uses edge.pathData directly\n  calculateEdgePath(sourceNode, targetNode) {\n    // Calculate connection points based on mode\n    let sourceX, sourceY, targetX, targetY;\n    if (this.isExecuteMode) {\n      // Execute mode - center to center\n      sourceX = sourceNode.position.x + 27.5; // Center of 55px node\n      sourceY = sourceNode.position.y + 27.5;\n      targetX = targetNode.position.x + 27.5;\n      targetY = targetNode.position.y + 27.5;\n    } else {\n      // Build mode - icon center to icon center for consistency\n      sourceX = sourceNode.position.x + 30; // Icon center (10px padding + 20px to center)\n      sourceY = sourceNode.position.y + 30; // Icon center\n      targetX = targetNode.position.x + 30;\n      targetY = targetNode.position.y + 30;\n    }\n    // Create simple, reliable connection path\n    return this.createConnectionPath({\n      x: sourceX,\n      y: sourceY\n    }, {\n      x: targetX,\n      y: targetY\n    });\n  }\n  // Pre-calculate all edge paths when nodes or edges change\n  updateEdgePaths() {\n    console.log('🔗 updateEdgePaths - Current nodes:', this.nodes.map(n => ({\n      id: n.id,\n      position: n.position\n    })));\n    console.log('🔗 updateEdgePaths - Current edges:', this.edges.map(e => ({\n      id: e.id,\n      source: e.source,\n      target: e.target\n    })));\n    this.edges = this.edges.map(edge => {\n      const sourceNode = this.nodes.find(n => n.id === edge.source);\n      const targetNode = this.nodes.find(n => n.id === edge.target);\n      if (sourceNode && targetNode) {\n        const pathData = this.calculateEdgePath(sourceNode, targetNode);\n        console.log(`🔗 SIMPLE: ${edge.id} -> ${pathData}`);\n        console.log(`🔗 SOURCE: ${sourceNode.id} at (${sourceNode.position.x}, ${sourceNode.position.y})`);\n        console.log(`🔗 TARGET: ${targetNode.id} at (${targetNode.position.x}, ${targetNode.position.y})`);\n        return {\n          ...edge,\n          pathData: pathData\n        };\n      } else {\n        // Fallback for missing nodes\n        console.log(`🔗 WARNING: Missing nodes for edge ${edge.id}, using empty path`);\n        return {\n          ...edge,\n          pathData: ''\n        };\n      }\n    });\n    console.log('🔗 updateEdgePaths completed - All edges now have pathData');\n    this.cdr.detectChanges();\n  }\n  findNearestConnectionPoints(sourcePoints, targetPoints) {\n    if (this.isExecuteMode) {\n      // Execute mode - icon center to icon center connections\n      // Since all connection points are at the center, use the center point directly\n      return {\n        sourcePoint: sourcePoints.top,\n        // All points are the same (center), so any will work\n        targetPoint: targetPoints.top // All points are the same (center), so any will work\n      };\n    } else {\n      // Build mode - find optimal connection points for any node placement\n      // Calculate all possible connection combinations\n      const sourcePointOptions = [{\n        point: sourcePoints.right,\n        name: 'right'\n      }, {\n        point: sourcePoints.bottom,\n        name: 'bottom'\n      }, {\n        point: sourcePoints.left,\n        name: 'left'\n      }, {\n        point: sourcePoints.top,\n        name: 'top'\n      }];\n      const targetPointOptions = [{\n        point: targetPoints.left,\n        name: 'left'\n      }, {\n        point: targetPoints.top,\n        name: 'top'\n      }, {\n        point: targetPoints.right,\n        name: 'right'\n      }, {\n        point: targetPoints.bottom,\n        name: 'bottom'\n      }];\n      let bestDistance = Infinity;\n      let bestSourcePoint = sourcePoints.right;\n      let bestTargetPoint = targetPoints.left;\n      // Find the shortest distance combination\n      for (const sourceOption of sourcePointOptions) {\n        for (const targetOption of targetPointOptions) {\n          const distance = Math.sqrt(Math.pow(targetOption.point.x - sourceOption.point.x, 2) + Math.pow(targetOption.point.y - sourceOption.point.y, 2));\n          if (distance < bestDistance) {\n            bestDistance = distance;\n            bestSourcePoint = sourceOption.point;\n            bestTargetPoint = targetOption.point;\n          }\n        }\n      }\n      return {\n        sourcePoint: bestSourcePoint,\n        targetPoint: bestTargetPoint\n      };\n    }\n  }\n  getConnectionDirection(sourcePoint, targetPoint) {\n    const dx = targetPoint.x - sourcePoint.x;\n    const dy = targetPoint.y - sourcePoint.y;\n    if (Math.abs(dx) > Math.abs(dy)) {\n      return dx > 0 ? 'right' : 'left';\n    } else {\n      return dy > 0 ? 'bottom' : 'top';\n    }\n  }\n  adjustTargetForArrow(targetPoint, direction, offset) {\n    switch (direction) {\n      case 'right':\n        return {\n          x: targetPoint.x - offset,\n          y: targetPoint.y\n        };\n      case 'left':\n        return {\n          x: targetPoint.x + offset,\n          y: targetPoint.y\n        };\n      case 'bottom':\n        return {\n          x: targetPoint.x,\n          y: targetPoint.y - offset\n        };\n      case 'top':\n        return {\n          x: targetPoint.x,\n          y: targetPoint.y + offset\n        };\n      default:\n        return targetPoint;\n    }\n  }\n  createConnectionPath(sourcePoint, targetPoint) {\n    // SIMPLIFIED: Create straight lines for consistent vertical stacking\n    const sourceX = sourcePoint.x;\n    const sourceY = sourcePoint.y;\n    const targetX = targetPoint.x;\n    const targetY = targetPoint.y;\n    if (this.isExecuteMode) {\n      // Execute mode - direct line between centers with arrow spacing\n      const dx = targetX - sourceX;\n      const dy = targetY - sourceY;\n      const length = Math.sqrt(dx * dx + dy * dy);\n      if (length < 20) {\n        return `M ${sourceX} ${sourceY} L ${targetX} ${targetY}`;\n      }\n      const unitX = dx / length;\n      const unitY = dy / length;\n      const sourceRadius = 27;\n      const targetRadius = 20;\n      const arrowSpace = 8;\n      const adjustedSourceX = sourceX + unitX * sourceRadius;\n      const adjustedSourceY = sourceY + unitY * sourceRadius;\n      const adjustedTargetX = targetX - unitX * (targetRadius + arrowSpace);\n      const adjustedTargetY = targetY - unitY * (targetRadius + arrowSpace);\n      return `M ${adjustedSourceX} ${adjustedSourceY} L ${adjustedTargetX} ${adjustedTargetY}`;\n    } else {\n      // Build mode - ALWAYS STRAIGHT LINES regardless of node positioning\n      const dx = targetX - sourceX;\n      const dy = targetY - sourceY;\n      const distance = Math.sqrt(dx * dx + dy * dy);\n      if (distance < 20) {\n        // Very close nodes - direct line\n        return `M ${sourceX} ${sourceY} L ${targetX} ${targetY}`;\n      }\n      // Calculate arrow spacing to ensure arrows are visible\n      const unitX = dx / distance;\n      const unitY = dy / distance;\n      // Adjust source to start from node edge\n      const sourceRadius = 30; // Start from edge of source node\n      const adjustedSourceX = sourceX + unitX * sourceRadius;\n      const adjustedSourceY = sourceY + unitY * sourceRadius;\n      // Adjust target to end before the node (leave space for arrow)\n      const targetNodeRadius = 30; // Target node radius\n      const arrowLength = 15; // Space for the arrow itself\n      const adjustedTargetX = targetX - unitX * (targetNodeRadius + arrowLength);\n      const adjustedTargetY = targetY - unitY * (targetNodeRadius + arrowLength);\n      // ALWAYS use straight lines - no curves\n      return `M ${adjustedSourceX} ${adjustedSourceY} L ${adjustedTargetX} ${adjustedTargetY}`;\n    }\n  }\n  getTempConnectionPath() {\n    if (!this.tempConnection.isActive || this.tempConnection.sourceX === undefined || this.tempConnection.sourceY === undefined || this.tempConnection.targetX === undefined || this.tempConnection.targetY === undefined) {\n      return '';\n    }\n    const sourceX = this.tempConnection.sourceX;\n    const sourceY = this.tempConnection.sourceY;\n    const targetX = this.tempConnection.targetX;\n    const targetY = this.tempConnection.targetY;\n    // Create beautiful Bézier curves exactly like workflow editor\n    const dx = targetX - sourceX;\n    const dy = targetY - sourceY;\n    const distance = Math.sqrt(dx * dx + dy * dy);\n    // Use the exact same offset calculation as your workflow editor\n    const baseOffset = Math.min(100, distance * 0.5);\n    const offset = Math.max(50, baseOffset);\n    // Create horizontal Bézier curve with proper control points\n    const controlPointX1 = sourceX + offset;\n    const controlPointX2 = targetX - offset;\n    return `M ${sourceX} ${sourceY} C ${controlPointX1} ${sourceY}, ${controlPointX2} ${targetY}, ${targetX} ${targetY}`;\n  }\n  // Toolbar actions\n  onUndo() {\n    if (this.historyIndex > 0) {\n      this.historyIndex--;\n      const state = this.history[this.historyIndex];\n      this.restoreState(state);\n    }\n    this.undoAction.emit();\n  }\n  onRedo() {\n    if (this.historyIndex < this.history.length - 1) {\n      this.historyIndex++;\n      const state = this.history[this.historyIndex];\n      this.restoreState(state);\n    }\n    this.redoAction.emit();\n  }\n  onReset() {\n    // Save current state before reset\n    this.saveToHistory();\n    // Clear all nodes and edges\n    this.nodes = [];\n    this.edges = [];\n    this.selectedNodeId = null;\n    this.tempConnection = {\n      isActive: false\n    };\n    // Reset viewport\n    this.resetViewport();\n    // Emit state change to parent component\n    this.stateChanged.emit({\n      nodes: [],\n      edges: []\n    });\n    // Emit events\n    this.nodeRemoved.emit('all'); // Special case for clearing all\n    this.resetAction.emit();\n  }\n  onPrimaryButtonClick() {\n    this.primaryButtonClicked.emit();\n  }\n  // History management methods\n  saveToHistory() {\n    // Don't save history during state restoration\n    if (this.isRestoringState) return;\n    // Remove any history after current index (when we're not at the end)\n    this.history = this.history.slice(0, this.historyIndex + 1);\n    // Add current state to history\n    const currentState = {\n      nodes: JSON.parse(JSON.stringify(this.nodes)),\n      edges: JSON.parse(JSON.stringify(this.edges))\n    };\n    this.history.push(currentState);\n    this.historyIndex = this.history.length - 1;\n    // Limit history size\n    if (this.history.length > this.maxHistorySize) {\n      this.history.shift();\n      this.historyIndex--;\n    }\n  }\n  restoreState(state) {\n    this.isRestoringState = true;\n    // Update the internal state\n    this.nodes = [...state.nodes];\n    this.edges = [...state.edges];\n    // Clear selection and temp connections\n    this.selectedNodeId = null;\n    this.tempConnection = {\n      isActive: false\n    };\n    // Update connection points\n    this.updateNodeConnectionPoints();\n    // Emit state change to parent component\n    this.stateChanged.emit({\n      nodes: [...this.nodes],\n      edges: [...this.edges]\n    });\n    // Reset flag after a short delay\n    setTimeout(() => {\n      this.isRestoringState = false;\n    }, 100);\n  }\n  // Save history before actions\n  saveHistoryBeforeAction() {\n    this.saveToHistory();\n  }\n  // Public methods for external components to add/remove nodes and edges\n  addNode(node) {\n    this.saveHistoryBeforeAction();\n    this.nodes.push(node);\n    this.updateNodeConnectionPoints();\n    this.nodeAdded.emit(node);\n  }\n  addEdge(edge) {\n    this.saveHistoryBeforeAction();\n    this.edges.push(edge);\n  }\n  removeNode(nodeId) {\n    this.onDeleteNode(nodeId);\n  }\n  removeEdge(edgeId) {\n    this.saveHistoryBeforeAction();\n    this.edges = this.edges.filter(edge => edge.id !== edgeId);\n  }\n  // Canvas tool methods\n  toggleGrid() {\n    this.showGridDots = !this.showGridDots;\n    this.showGrid = this.showGridDots;\n  }\n  setPanMode() {\n    this.canvasMode = 'pan';\n    this.mouseInteractionsEnabled = true;\n    if (this.canvasContainer) {\n      this.canvasContainer.nativeElement.style.cursor = 'grab';\n    }\n  }\n  setSelectMode() {\n    this.canvasMode = 'select';\n    this.mouseInteractionsEnabled = true;\n    if (this.canvasContainer) {\n      this.canvasContainer.nativeElement.style.cursor = 'default';\n    }\n  }\n  setZoomMode() {\n    this.canvasMode = 'zoom';\n    this.mouseInteractionsEnabled = true;\n    if (this.canvasContainer) {\n      this.canvasContainer.nativeElement.style.cursor = 'zoom-in';\n    }\n  }\n  disableMouseInteractions() {\n    this.canvasMode = 'disabled';\n    this.mouseInteractionsEnabled = false;\n    if (this.canvasContainer) {\n      this.canvasContainer.nativeElement.style.cursor = 'not-allowed';\n    }\n  }\n  zoomIn() {\n    const newZoom = Math.min(this.maxZoom, this.viewport.zoom * 1.2);\n    this.setZoom(newZoom);\n  }\n  zoomOut() {\n    const newZoom = Math.max(this.minZoom, this.viewport.zoom / 1.2);\n    this.setZoom(newZoom);\n  }\n  setZoom(newZoom) {\n    if (newZoom !== this.viewport.zoom) {\n      // Get canvas center for zoom\n      const rect = this.canvasContainer.nativeElement.getBoundingClientRect();\n      const centerX = rect.width / 2;\n      const centerY = rect.height / 2;\n      // Calculate new position to zoom towards center\n      const zoomRatio = newZoom / this.viewport.zoom;\n      const newX = centerX - (centerX - this.viewport.x) * zoomRatio;\n      const newY = centerY - (centerY - this.viewport.y) * zoomRatio;\n      // Update viewport\n      this.viewport.zoom = newZoom;\n      this.viewport.x = newX;\n      this.viewport.y = newY;\n      // Update connection points\n      this.updateNodeConnectionPoints();\n      // Emit viewport change\n      this.viewportChanged.emit(this.viewport);\n    }\n  }\n  // Built-in input field methods\n  onAgentNameChange(value) {\n    this.agentNameControl.setValue(value);\n    this.agentNameChanged.emit(value);\n  }\n  onAgentTypeChange(value) {\n    const finalValue = Array.isArray(value) ? value[0] : value;\n    this.agentTypeControl.setValue(finalValue);\n    this.agentTypeChanged.emit(finalValue);\n  }\n  // Built-in metadata dropdown methods\n  toggleMetadataDropdown() {\n    this.isMetadataDropdownOpen = !this.isMetadataDropdownOpen;\n  }\n  closeMetadataDropdown() {\n    this.isMetadataDropdownOpen = false;\n  }\n  onDropdownSelect(selectedValue, level, currentType, nextType) {\n    const value = Array.isArray(selectedValue) ? selectedValue[0] : selectedValue;\n    if (!value) return;\n    // Update the current dropdown value\n    switch (currentType) {\n      case 'org':\n        this.orgControl.setValue(value);\n        // Reset dependent dropdowns\n        this.domainControl.setValue('');\n        this.projectControl.setValue('');\n        this.teamControl.setValue('');\n        // Load domain options\n        this.loadDomainOptions(value);\n        break;\n      case 'domain':\n        this.domainControl.setValue(value);\n        // Reset dependent dropdowns\n        this.projectControl.setValue('');\n        this.teamControl.setValue('');\n        // Load project options\n        this.loadProjectOptions(this.orgControl.value || '', value);\n        break;\n      case 'project':\n        this.projectControl.setValue(value);\n        // Reset dependent dropdown\n        this.teamControl.setValue('');\n        // Load team options\n        this.loadTeamOptions(this.orgControl.value || '', this.domainControl.value || '', value);\n        break;\n      case 'team':\n        this.teamControl.setValue(value);\n        break;\n    }\n  }\n  applyMetadata() {\n    const hasValues = this.orgControl.value || this.domainControl.value || this.projectControl.value || this.teamControl.value;\n    if (hasValues) {\n      this.metadataStatus = this.inputFieldsConfig.metadata?.statusText?.saved || 'Metadata Information saved';\n    } else {\n      this.metadataStatus = this.inputFieldsConfig.metadata?.statusText?.notSaved || 'Metadata Information not saved';\n    }\n    // Emit metadata change event\n    this.metadataChanged.emit({\n      org: this.orgControl.value || '',\n      domain: this.domainControl.value || '',\n      project: this.projectControl.value || '',\n      team: this.teamControl.value || ''\n    });\n    this.closeMetadataDropdown();\n  }\n  cancelMetadata() {\n    this.closeMetadataDropdown();\n  }\n  // Load dropdown options (same API pattern as nav-item)\n  loadDomainOptions(org) {\n    const domainOptionsMap = {\n      ascendion: [{\n        value: 'engineering',\n        label: 'Engineering'\n      }, {\n        value: 'marketing',\n        label: 'Marketing'\n      }, {\n        value: 'sales',\n        label: 'Sales'\n      }],\n      company2: [{\n        value: 'tech',\n        label: 'Technology'\n      }, {\n        value: 'operations',\n        label: 'Operations'\n      }],\n      company3: [{\n        value: 'research',\n        label: 'Research'\n      }, {\n        value: 'development',\n        label: 'Development'\n      }]\n    };\n    this.dropdownValues['domain'] = domainOptionsMap[org] || [];\n  }\n  loadProjectOptions(org, domain) {\n    const projectOptions = [{\n      value: 'project1',\n      label: 'Project Alpha'\n    }, {\n      value: 'project2',\n      label: 'Project Beta'\n    }, {\n      value: 'project3',\n      label: 'Project Gamma'\n    }];\n    this.dropdownValues['project'] = projectOptions;\n  }\n  loadTeamOptions(org, domain, project) {\n    const teamOptions = [{\n      value: 'team1',\n      label: 'Team Alpha'\n    }, {\n      value: 'team2',\n      label: 'Team Beta'\n    }, {\n      value: 'team3',\n      label: 'Team Gamma'\n    }];\n    this.dropdownValues['team'] = teamOptions;\n  }\n  // Toolbar is now fixed at bottom center - no drag methods needed\n  // Toolbar is now fixed at bottom center via CSS - no positioning method needed\n  // Agent details dropdown methods\n  toggleAgentDetailsDropdown() {\n    this.isAgentDetailsDropdownOpen = !this.isAgentDetailsDropdownOpen;\n  }\n  closeAgentDetailsDropdown() {\n    this.isAgentDetailsDropdownOpen = false;\n  }\n  applyAgentDetails() {\n    const name = this.agentDetailNameControl.value || '';\n    const useCaseDetails = this.agentDetailControl.value || '';\n    // Emit agent details change event\n    this.agentDetailsChanged.emit({\n      name: name,\n      useCaseDetails: useCaseDetails\n    });\n    this.closeAgentDetailsDropdown();\n  }\n  cancelAgentDetails() {\n    this.closeAgentDetailsDropdown();\n  }\n};\n__decorate([ViewChild('canvasContainer')], CanvasBoardComponent.prototype, \"canvasContainer\", void 0);\n__decorate([ContentChild('nodeTemplate')], CanvasBoardComponent.prototype, \"nodeTemplate\", void 0);\n__decorate([Input()], CanvasBoardComponent.prototype, \"nodes\", void 0);\n__decorate([Input()], CanvasBoardComponent.prototype, \"edges\", void 0);\n__decorate([Input()], CanvasBoardComponent.prototype, \"showGrid\", void 0);\n__decorate([Input()], CanvasBoardComponent.prototype, \"enablePan\", void 0);\n__decorate([Input()], CanvasBoardComponent.prototype, \"enableZoom\", void 0);\n__decorate([Input()], CanvasBoardComponent.prototype, \"enableConnections\", void 0);\n__decorate([Input()], CanvasBoardComponent.prototype, \"minZoom\", void 0);\n__decorate([Input()], CanvasBoardComponent.prototype, \"maxZoom\", void 0);\n__decorate([Input()], CanvasBoardComponent.prototype, \"connectionColor\", void 0);\n__decorate([Input()], CanvasBoardComponent.prototype, \"fallbackMessage\", void 0);\n__decorate([Input()], CanvasBoardComponent.prototype, \"navigationHints\", void 0);\n__decorate([Input()], CanvasBoardComponent.prototype, \"showToolbar\", void 0);\n__decorate([Input()], CanvasBoardComponent.prototype, \"primaryButtonText\", void 0);\n__decorate([Input()], CanvasBoardComponent.prototype, \"primaryButtonIcon\", void 0);\n__decorate([Input()], CanvasBoardComponent.prototype, \"enableUndo\", void 0);\n__decorate([Input()], CanvasBoardComponent.prototype, \"enableRedo\", void 0);\n__decorate([Input()], CanvasBoardComponent.prototype, \"enableReset\", void 0);\n__decorate([Input()], CanvasBoardComponent.prototype, \"showCanvasTools\", void 0);\n__decorate([Input()], CanvasBoardComponent.prototype, \"enableGridToggle\", void 0);\n__decorate([Input()], CanvasBoardComponent.prototype, \"enablePanMode\", void 0);\n__decorate([Input()], CanvasBoardComponent.prototype, \"enableSelectionMode\", void 0);\n__decorate([Input()], CanvasBoardComponent.prototype, \"enableZoomControls\", void 0);\n__decorate([Input()], CanvasBoardComponent.prototype, \"isExecuteMode\", void 0);\n__decorate([Input()], CanvasBoardComponent.prototype, \"showLeftActions\", void 0);\n__decorate([Input()], CanvasBoardComponent.prototype, \"mouseInteractionsEnabled\", void 0);\n__decorate([Input()], CanvasBoardComponent.prototype, \"showHeaderInputs\", void 0);\n__decorate([Input()], CanvasBoardComponent.prototype, \"inputFieldsConfig\", void 0);\n__decorate([Input()], CanvasBoardComponent.prototype, \"initialAgentName\", void 0);\n__decorate([Input()], CanvasBoardComponent.prototype, \"initialAgentDetails\", void 0);\n__decorate([Input()], CanvasBoardComponent.prototype, \"initialMetadata\", void 0);\n__decorate([Input()], CanvasBoardComponent.prototype, \"agentNameControl\", void 0);\n__decorate([Input()], CanvasBoardComponent.prototype, \"agentDetailNameControl\", void 0);\n__decorate([Input()], CanvasBoardComponent.prototype, \"agentDetailControl\", void 0);\n__decorate([Output()], CanvasBoardComponent.prototype, \"nodeAdded\", void 0);\n__decorate([Output()], CanvasBoardComponent.prototype, \"nodeRemoved\", void 0);\n__decorate([Output()], CanvasBoardComponent.prototype, \"nodeMoved\", void 0);\n__decorate([Output()], CanvasBoardComponent.prototype, \"nodeSelected\", void 0);\n__decorate([Output()], CanvasBoardComponent.prototype, \"nodeDoubleClicked\", void 0);\n__decorate([Output()], CanvasBoardComponent.prototype, \"connectionStarted\", void 0);\n__decorate([Output()], CanvasBoardComponent.prototype, \"connectionCreated\", void 0);\n__decorate([Output()], CanvasBoardComponent.prototype, \"canvasDropped\", void 0);\n__decorate([Output()], CanvasBoardComponent.prototype, \"viewportChanged\", void 0);\n__decorate([Output()], CanvasBoardComponent.prototype, \"undoAction\", void 0);\n__decorate([Output()], CanvasBoardComponent.prototype, \"redoAction\", void 0);\n__decorate([Output()], CanvasBoardComponent.prototype, \"resetAction\", void 0);\n__decorate([Output()], CanvasBoardComponent.prototype, \"primaryButtonClicked\", void 0);\n__decorate([Output()], CanvasBoardComponent.prototype, \"stateChanged\", void 0);\n__decorate([Output()], CanvasBoardComponent.prototype, \"agentNameChanged\", void 0);\n__decorate([Output()], CanvasBoardComponent.prototype, \"agentTypeChanged\", void 0);\n__decorate([Output()], CanvasBoardComponent.prototype, \"metadataChanged\", void 0);\n__decorate([Output()], CanvasBoardComponent.prototype, \"agentDetailsChanged\", void 0);\nCanvasBoardComponent = __decorate([Component({\n  selector: 'app-canvas-board',\n  standalone: true,\n  imports: [CommonModule, ReactiveFormsModule, ButtonComponent, AvaTextboxComponent, AvaTextareaComponent, IconComponent],\n  templateUrl: './canvas-board.component.html',\n  styleUrls: ['./canvas-board.component.scss']\n})], CanvasBoardComponent);\nexport { CanvasBoardComponent };", "map": {"version": 3, "names": ["Component", "Input", "Output", "EventEmitter", "ViewChild", "ContentChild", "CommonModule", "FormControl", "ReactiveFormsModule", "ButtonComponent", "AvaTextboxComponent", "AvaTextareaComponent", "IconComponent", "CanvasBoardComponent", "cdr", "canvasContainer", "nodeTemplate", "nodes", "edges", "showGrid", "enablePan", "enableZoom", "enableConnections", "minZoom", "max<PERSON><PERSON>", "connectionColor", "fallbackMessage", "navigationHints", "showToolbar", "primaryButtonText", "primaryButtonIcon", "enableUndo", "enableRedo", "enableReset", "showCanvasTools", "enableGridToggle", "enablePanMode", "enableSelectionMode", "enableZoomControls", "isExecuteMode", "showLeftActions", "mouseInteractionsEnabled", "showHeaderInputs", "inputFieldsConfig", "initialAgentName", "initialAgentDetails", "initialMetadata", "org", "domain", "project", "team", "isMetadataDropdownOpen", "metadataStatus", "isAgentDetailsDropdownOpen", "agentNameControl", "agentTypeControl", "agentTypeDisplayControl", "agentDetailNameControl", "agentDetailControl", "orgControl", "domainControl", "projectControl", "teamControl", "dropdownValues", "value", "label", "nodeAdded", "nodeRemoved", "nodeMoved", "nodeSelected", "nodeDoubleClicked", "connectionStarted", "connectionCreated", "canvasDropped", "viewportChanged", "undoAction", "redoAction", "resetAction", "primaryButtonClicked", "stateChanged", "agentNameChanged", "agentTypeChanged", "metadataChanged", "agentDetailsChanged", "selectedNodeId", "tempConnection", "isActive", "nodeConnectionPoints", "viewport", "zoom", "x", "y", "isDragging", "lastMouseX", "lastMouseY", "updateConnectionPointsFrame", "history", "historyIndex", "maxHistorySize", "isRestoringState", "canvasMode", "showGridDots", "constructor", "ngOnInit", "setInitialValues", "valueChanges", "subscribe", "undefined", "emit", "setValue", "emitEvent", "trackByNodeId", "index", "node", "id", "ngOnChanges", "changes", "currentValue", "console", "log", "map", "n", "position", "e", "source", "target", "updateNodeConnectionPoints", "updateEdgePaths", "setTimeout", "ngAfterViewInit", "setupCanvasNavigation", "requestAnimationFrame", "saveToHistory", "setSelectMode", "ngOnDestroy", "document", "removeEventListener", "handleKeyDown", "cancelAnimationFrame", "element", "nativeElement", "addEventListener", "bind", "event", "key", "isInputField", "tagName", "contentEditable", "closest", "resetViewport", "preventDefault", "detectChanges", "onDragOver", "dataTransfer", "dropEffect", "onDrop", "saveHistoryBeforeAction", "canvasBounds", "getBoundingClientRect", "clientX", "left", "clientY", "top", "safePosition", "Math", "max", "onNodeSelected", "nodeId", "onNodeDoubleClicked", "onNodeMoved", "data", "nodeIndex", "findIndex", "onStartConnection", "find", "canvasRect", "mouseX", "mouseY", "handleX", "handleY", "connectionPoints", "nodeElement", "querySelector", "rect", "nodeX", "nodeY", "nodeWidth", "width", "nodeHeight", "height", "relativeX", "relativeY", "min", "sourceNodeId", "sourceHandleType", "handleType", "sourceX", "sourceY", "targetX", "targetY", "onDeleteNode", "filter", "edge", "onCanvasMouseMove", "deltaX", "deltaY", "onCanvasWheel", "delta", "zoomSpeed", "newZoom", "zoomRatio", "newX", "newY", "onCanvasMouseDown", "button", "altKey", "style", "cursor", "onCanvasMouseUp", "targetNodeId", "getAttribute", "existingConnection", "newEdge", "animated", "push", "centerX", "centerY", "right", "bottom", "iconCenterX", "calculateEdgePath", "sourceNode", "targetNode", "createConnectionPath", "pathData", "findNearestConnectionPoints", "sourcePoints", "targetPoints", "sourcePoint", "targetPoint", "sourcePointOptions", "point", "name", "targetPointOptions", "bestDistance", "Infinity", "bestSourcePoint", "bestTargetPoint", "sourceOption", "targetOption", "distance", "sqrt", "pow", "getConnectionDirection", "dx", "dy", "abs", "adjustTargetForArrow", "direction", "offset", "length", "unitX", "unitY", "sourceRadius", "targetRadius", "arrowSpace", "adjustedSourceX", "adjustedSourceY", "adjustedTargetX", "adjustedTargetY", "targetNodeRadius", "<PERSON><PERSON><PERSON><PERSON>", "getTempConnectionPath", "baseOffset", "controlPointX1", "controlPointX2", "onUndo", "state", "restoreState", "onRedo", "onReset", "onPrimaryButtonClick", "slice", "currentState", "JSON", "parse", "stringify", "shift", "addNode", "addEdge", "removeNode", "removeEdge", "edgeId", "to<PERSON><PERSON><PERSON>", "setPanMode", "setZoomMode", "disableMouseInteractions", "zoomIn", "setZoom", "zoomOut", "onAgentNameChange", "onAgentTypeChange", "finalValue", "Array", "isArray", "toggleMetadataDropdown", "closeMetadataDropdown", "onDropdownSelect", "selected<PERSON><PERSON><PERSON>", "level", "currentType", "nextType", "loadDomainOptions", "loadProjectOptions", "loadTeamOptions", "applyMetadata", "<PERSON><PERSON><PERSON><PERSON>", "metadata", "statusText", "saved", "notSaved", "cancelMetadata", "domainOptionsMap", "ascendion", "company2", "company3", "projectOptions", "teamOptions", "toggleAgentDetailsDropdown", "closeAgentDetailsDropdown", "applyAgentDetails", "useCaseDetails", "cancelAgentDetails", "__decorate", "selector", "standalone", "imports", "templateUrl", "styleUrls"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\components\\canvas-board\\canvas-board.component.ts"], "sourcesContent": ["import {\r\n  Component,\r\n  Input,\r\n  Output,\r\n  EventEmitter,\r\n  ViewChild,\r\n  ElementRef,\r\n  AfterViewInit,\r\n  OnDestroy,\r\n  OnChanges,\r\n  OnInit,\r\n  ChangeDetectorRef,\r\n  ContentChild,\r\n  TemplateRef,\r\n} from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormControl, ReactiveFormsModule } from '@angular/forms';\r\nimport {\r\n  ButtonComponent,\r\n  AvaTextboxComponent,\r\n  AvaTextareaComponent,\r\n  IconComponent,\r\n} from '@ava/play-comp-library';\r\n\r\nexport interface CanvasNode {\r\n  id: string;\r\n  type: string;\r\n  data: any;\r\n  position: { x: number; y: number };\r\n}\r\n\r\nexport interface CanvasEdge {\r\n  id: string;\r\n  source: string;\r\n  target: string;\r\n  animated?: boolean;\r\n  pathData?: string; // Pre-calculated SVG path data\r\n}\r\n\r\nexport interface SelectOption {\r\n  value: string;\r\n  label: string;\r\n}\r\n\r\nexport interface CustomInputField {\r\n  id: string;\r\n  label: string;\r\n  type: 'text' | 'textarea' | 'select' | 'number' | 'email';\r\n  placeholder?: string;\r\n  required?: boolean;\r\n  disabled?: boolean;\r\n  options?: SelectOption[]; // For select type\r\n  control?: FormControl;\r\n  value?: any;\r\n  width?: string;\r\n  height?: string;\r\n}\r\n\r\nexport interface MetadataConfig {\r\n  enabled: boolean;\r\n  orgOptions: SelectOption[];\r\n  domainOptions: SelectOption[];\r\n  projectOptions: SelectOption[];\r\n  teamOptions: SelectOption[];\r\n  orgControl: FormControl;\r\n  domainControl: FormControl;\r\n  projectControl: FormControl;\r\n  teamControl: FormControl;\r\n}\r\n\r\ninterface TempConnection {\r\n  isActive: boolean;\r\n  sourceNodeId?: string;\r\n  targetNodeId?: string;\r\n  sourceHandleType?: 'source' | 'target';\r\n  sourceX?: number;\r\n  sourceY?: number;\r\n  targetX?: number;\r\n  targetY?: number;\r\n}\r\n\r\ntype CanvasToolMode = 'select' | 'pan' | 'zoom' | 'disabled';\r\n\r\ninterface CanvasViewport {\r\n  zoom: number;\r\n  x: number;\r\n  y: number;\r\n  isDragging: boolean;\r\n  lastMouseX: number;\r\n  lastMouseY: number;\r\n}\r\n\r\ninterface NodeConnectionPoints {\r\n  [nodeId: string]: {\r\n    top: { x: number; y: number };\r\n    right: { x: number; y: number };\r\n    bottom: { x: number; y: number };\r\n    left: { x: number; y: number };\r\n  };\r\n}\r\n\r\n@Component({\r\n  selector: 'app-canvas-board',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    ReactiveFormsModule,\r\n    ButtonComponent,\r\n    AvaTextboxComponent,\r\n    AvaTextareaComponent,\r\n    IconComponent,\r\n  ],\r\n  templateUrl: './canvas-board.component.html',\r\n  styleUrls: ['./canvas-board.component.scss'],\r\n})\r\nexport class CanvasBoardComponent\r\n  implements OnInit, AfterViewInit, OnDestroy, OnChanges\r\n{\r\n  @ViewChild('canvasContainer') canvasContainer!: ElementRef;\r\n  @ContentChild('nodeTemplate') nodeTemplate!: TemplateRef<any>;\r\n\r\n  // Inputs\r\n  @Input() nodes: CanvasNode[] = [];\r\n  @Input() edges: CanvasEdge[] = [];\r\n  @Input() showGrid: boolean = true;\r\n  @Input() enablePan: boolean = false; // Disabled by default\r\n  @Input() enableZoom: boolean = false; // Disabled by default\r\n  @Input() enableConnections: boolean = true;\r\n  @Input() minZoom: number = 0.1;\r\n  @Input() maxZoom: number = 2;\r\n  @Input() connectionColor: string = '#9ca3af'; // Grey color for connections\r\n  @Input() fallbackMessage: string = 'Drop items here to get started';\r\n  @Input() navigationHints: string[] = [\r\n    'Select toolbar options to enable canvas interactions',\r\n    'Alt + Drag to pan canvas (when enabled)',\r\n    'Mouse wheel to zoom (when enabled)',\r\n    'Space to reset view',\r\n  ];\r\n  @Input() showToolbar: boolean = true;\r\n  @Input() primaryButtonText: string = 'Execute';\r\n  @Input() primaryButtonIcon: string = '';\r\n  @Input() enableUndo: boolean = true;\r\n  @Input() enableRedo: boolean = true;\r\n  @Input() enableReset: boolean = true;\r\n  @Input() showCanvasTools: boolean = true;\r\n  @Input() enableGridToggle: boolean = true;\r\n  @Input() enablePanMode: boolean = true;\r\n  @Input() enableSelectionMode: boolean = true;\r\n  @Input() enableZoomControls: boolean = true;\r\n  @Input() isExecuteMode: boolean = false;\r\n  @Input() showLeftActions: boolean = true;\r\n  @Input() mouseInteractionsEnabled: boolean = true;\r\n\r\n  // Built-in input fields configuration\r\n  @Input() showHeaderInputs: boolean = false;\r\n  @Input() inputFieldsConfig: {\r\n    agentName?: {\r\n      enabled: boolean;\r\n      placeholder?: string;\r\n      required?: boolean;\r\n      style?: Record<string, any>;\r\n    };\r\n    agentType?: {\r\n      enabled: boolean;\r\n      options?: SelectOption[];\r\n      defaultValue?: string;\r\n    };\r\n    agentTypeTag?: {\r\n      enabled: boolean;\r\n      value?: string;\r\n      showInAgentsBuilderOnly?: boolean;\r\n    };\r\n    metadata?: {\r\n      enabled: boolean;\r\n      label?: string;\r\n      statusText?: { saved: string; notSaved: string };\r\n    };\r\n    agentDetails?: {\r\n      enabled: boolean;\r\n      label?: string;\r\n      namePlaceholder?: string;\r\n      detailPlaceholder?: string;\r\n      detailLabel?: string;\r\n    };\r\n  } = {};\r\n\r\n  // Input properties for initial values\r\n  @Input() initialAgentName: string = '';\r\n  @Input() initialAgentDetails: string = '';\r\n  @Input() initialMetadata: {\r\n    org: string;\r\n    domain: string;\r\n    project: string;\r\n    team: string;\r\n  } = {\r\n    org: '',\r\n    domain: '',\r\n    project: '',\r\n    team: '',\r\n  };\r\n\r\n  // Built-in metadata dropdown state\r\n  isMetadataDropdownOpen = false;\r\n  metadataStatus = 'Metadata Information not saved';\r\n\r\n  // Agent details dropdown state\r\n  isAgentDetailsDropdownOpen = false;\r\n\r\n  // Form controls for built-in inputs\r\n  @Input() agentNameControl = new FormControl('');\r\n  agentTypeControl = new FormControl('individual');\r\n  agentTypeDisplayControl = new FormControl('individual');\r\n\r\n  // Agent details form controls\r\n  @Input() agentDetailNameControl = new FormControl('');\r\n  @Input() agentDetailControl = new FormControl('');\r\n\r\n  // Metadata form controls\r\n  orgControl = new FormControl('');\r\n  domainControl = new FormControl('');\r\n  projectControl = new FormControl('');\r\n  teamControl = new FormControl('');\r\n\r\n  // Dropdown options\r\n  dropdownValues: { [key: string]: SelectOption[] } = {\r\n    org: [\r\n      { value: 'ascendion', label: 'Ascendion' },\r\n      { value: 'company2', label: 'Company 2' },\r\n      { value: 'company3', label: 'Company 3' },\r\n    ],\r\n    domain: [],\r\n    project: [],\r\n    team: [],\r\n  };\r\n\r\n  // Outputs\r\n  @Output() nodeAdded = new EventEmitter<CanvasNode>();\r\n  @Output() nodeRemoved = new EventEmitter<string>();\r\n  @Output() nodeMoved = new EventEmitter<{\r\n    nodeId: string;\r\n    position: { x: number; y: number };\r\n  }>();\r\n  @Output() nodeSelected = new EventEmitter<string>();\r\n  @Output() nodeDoubleClicked = new EventEmitter<string>();\r\n\r\n  @Output() connectionStarted = new EventEmitter<{\r\n    nodeId: string;\r\n    handleType: 'source' | 'target';\r\n    event: MouseEvent;\r\n  }>();\r\n  @Output() connectionCreated = new EventEmitter<CanvasEdge>();\r\n  @Output() canvasDropped = new EventEmitter<{\r\n    event: DragEvent;\r\n    position: { x: number; y: number };\r\n  }>();\r\n  @Output() viewportChanged = new EventEmitter<CanvasViewport>();\r\n  @Output() undoAction = new EventEmitter<void>();\r\n  @Output() redoAction = new EventEmitter<void>();\r\n  @Output() resetAction = new EventEmitter<void>();\r\n  @Output() primaryButtonClicked = new EventEmitter<void>();\r\n  @Output() stateChanged = new EventEmitter<{\r\n    nodes: CanvasNode[];\r\n    edges: CanvasEdge[];\r\n  }>();\r\n  @Output() agentNameChanged = new EventEmitter<string>();\r\n  @Output() agentTypeChanged = new EventEmitter<string>();\r\n  @Output() metadataChanged = new EventEmitter<{\r\n    org: string;\r\n    domain: string;\r\n    project: string;\r\n    team: string;\r\n  }>();\r\n  @Output() agentDetailsChanged = new EventEmitter<{\r\n    name: string;\r\n    useCaseDetails: string;\r\n  }>();\r\n\r\n  // Internal state\r\n  selectedNodeId: string | null = null;\r\n  tempConnection: TempConnection = { isActive: false };\r\n  nodeConnectionPoints: NodeConnectionPoints = {};\r\n  viewport: CanvasViewport = {\r\n    zoom: 1,\r\n    x: 0,\r\n    y: 0,\r\n    isDragging: false,\r\n    lastMouseX: 0,\r\n    lastMouseY: 0,\r\n  };\r\n\r\n  // Performance optimization for connection updates\r\n  private updateConnectionPointsFrame: number | null = null;\r\n\r\n  // Internal history management\r\n  private history: { nodes: CanvasNode[]; edges: CanvasEdge[] }[] = [];\r\n  private historyIndex: number = -1;\r\n  private maxHistorySize: number = 50;\r\n  private isRestoringState: boolean = false;\r\n\r\n  // Canvas tool states\r\n  canvasMode: CanvasToolMode = 'select'; // Start with select mode\r\n  showGridDots: boolean = true;\r\n\r\n  // Mouse function controls\r\n  // mouseInteractionsEnabled: boolean = true; // Enable mouse interactions by default\r\n\r\n  // Toolbar is now fixed at bottom center - no drag state needed\r\n\r\n  constructor(private cdr: ChangeDetectorRef) {}\r\n\r\n  ngOnInit(): void {\r\n    // Set initial values\r\n    this.setInitialValues();\r\n\r\n    // Subscribe to agent name changes\r\n    this.agentNameControl.valueChanges.subscribe((value) => {\r\n      if (value !== null && value !== undefined) {\r\n        this.agentNameChanged.emit(value);\r\n      }\r\n    });\r\n\r\n    // Subscribe to agent type changes\r\n    this.agentTypeControl.valueChanges.subscribe((value) => {\r\n      if (value !== null && value !== undefined) {\r\n        this.agentTypeChanged.emit(value);\r\n      }\r\n    });\r\n  }\r\n\r\n  private setInitialValues(): void {\r\n    // Set agent name\r\n    if (this.initialAgentName) {\r\n      this.agentNameControl.setValue(this.initialAgentName, {\r\n        emitEvent: false,\r\n      });\r\n    }\r\n\r\n    // Set agent details\r\n    if (this.initialAgentName) {\r\n      this.agentDetailNameControl.setValue(this.initialAgentName, {\r\n        emitEvent: false,\r\n      });\r\n    }\r\n    if (this.initialAgentDetails) {\r\n      this.agentDetailControl.setValue(this.initialAgentDetails, {\r\n        emitEvent: false,\r\n      });\r\n    }\r\n\r\n    // Set metadata\r\n    if (this.initialMetadata) {\r\n      this.orgControl.setValue(this.initialMetadata.org, { emitEvent: false });\r\n      this.domainControl.setValue(this.initialMetadata.domain, {\r\n        emitEvent: false,\r\n      });\r\n      this.projectControl.setValue(this.initialMetadata.project, {\r\n        emitEvent: false,\r\n      });\r\n      this.teamControl.setValue(this.initialMetadata.team, {\r\n        emitEvent: false,\r\n      });\r\n    }\r\n  }\r\n\r\n  // TrackBy function for nodes to maintain DOM element identity\r\n  trackByNodeId(index: number, node: CanvasNode): string {\r\n    return node.id;\r\n  }\r\n\r\n  ngOnChanges(changes: any): void {\r\n    // Update initial values when inputs change\r\n    if (\r\n      changes['initialAgentName'] ||\r\n      changes['initialAgentDetails'] ||\r\n      changes['initialMetadata']\r\n    ) {\r\n      this.setInitialValues();\r\n    }\r\n\r\n    // Update internal state when inputs change (but not during state restoration)\r\n    if (!this.isRestoringState) {\r\n      if (changes.nodes && changes.nodes.currentValue) {\r\n        console.log(\r\n          '🔗 ngOnChanges - Received new nodes:',\r\n          changes.nodes.currentValue.map((n: any) => ({\r\n            id: n.id,\r\n            position: n.position,\r\n          })),\r\n        );\r\n        this.nodes = [...changes.nodes.currentValue];\r\n      }\r\n      if (changes.edges && changes.edges.currentValue) {\r\n        console.log(\r\n          '🔗 ngOnChanges - Received new edges:',\r\n          changes.edges.currentValue.map((e: any) => ({\r\n            id: e.id,\r\n            source: e.source,\r\n            target: e.target,\r\n          })),\r\n        );\r\n        this.edges = [...changes.edges.currentValue];\r\n      }\r\n\r\n      // Update connection points when nodes change - IMMEDIATE\r\n      if (changes.nodes) {\r\n        console.log('🔗 ngOnChanges - Updating for node changes');\r\n        this.updateNodeConnectionPoints();\r\n        this.updateEdgePaths();\r\n        // Force additional update after DOM settles\r\n        setTimeout(() => this.updateNodeConnectionPoints(), 0);\r\n      }\r\n\r\n      if (changes.edges) {\r\n        console.log('🔗 ngOnChanges - Updating for edge changes');\r\n        this.updateEdgePaths();\r\n      }\r\n    }\r\n\r\n    // Update connection points when execute mode changes\r\n    if (changes['isExecuteMode']) {\r\n      // Toolbar is now fixed at bottom center via CSS\r\n      this.updateNodeConnectionPoints();\r\n    }\r\n\r\n    if (changes['mouseInteractionsEnabled']) {\r\n      this.mouseInteractionsEnabled =\r\n        changes['mouseInteractionsEnabled'].currentValue;\r\n    }\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    this.setupCanvasNavigation();\r\n    // Use requestAnimationFrame for smooth initialization\r\n    requestAnimationFrame(() => {\r\n      this.updateNodeConnectionPoints();\r\n      // Initialize history with current state\r\n      this.saveToHistory();\r\n      // Toolbar is now fixed at bottom center via CSS\r\n      // Set initial cursor to default arrow\r\n      this.setSelectMode();\r\n    });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // Cleanup event listeners\r\n    document.removeEventListener('keydown', this.handleKeyDown);\r\n\r\n    // Cancel any pending animation frame\r\n    if (this.updateConnectionPointsFrame) {\r\n      cancelAnimationFrame(this.updateConnectionPointsFrame);\r\n      this.updateConnectionPointsFrame = null;\r\n    }\r\n  }\r\n\r\n  private setupCanvasNavigation(): void {\r\n    const element = this.canvasContainer?.nativeElement;\r\n    if (element) {\r\n      document.addEventListener('keydown', this.handleKeyDown.bind(this));\r\n\r\n      element.addEventListener('mouseup', () => {\r\n        if (this.viewport.isDragging) {\r\n          this.updateNodeConnectionPoints();\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  private handleKeyDown = (event: KeyboardEvent): void => {\r\n    if (event.key === ' ') {\r\n      // Only reset viewport if the user is not typing in an input field\r\n      const target = event.target as HTMLElement;\r\n      const isInputField =\r\n        target.tagName === 'INPUT' ||\r\n        target.tagName === 'TEXTAREA' ||\r\n        target.contentEditable === 'true' ||\r\n        target.closest('ava-textbox') ||\r\n        target.closest('ava-textarea') ||\r\n        target.closest('.form-field') ||\r\n        target.closest('.input-container') ||\r\n        target.closest('.chat-input') ||\r\n        target.closest('textarea') ||\r\n        target.closest('input');\r\n\r\n      if (!isInputField) {\r\n        this.resetViewport();\r\n        event.preventDefault();\r\n      }\r\n    }\r\n  };\r\n\r\n  resetViewport(): void {\r\n    this.viewport = {\r\n      zoom: 1,\r\n      x: 0,\r\n      y: 0,\r\n      isDragging: false,\r\n      lastMouseX: 0,\r\n      lastMouseY: 0,\r\n    };\r\n\r\n    this.updateNodeConnectionPoints();\r\n    this.viewportChanged.emit(this.viewport);\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  onDragOver(event: DragEvent): void {\r\n    if (!this.enableConnections || !this.mouseInteractionsEnabled) return;\r\n    event.preventDefault();\r\n    if (event.dataTransfer) {\r\n      event.dataTransfer.dropEffect = 'move';\r\n    }\r\n  }\r\n\r\n  onDrop(event: DragEvent): void {\r\n    if (!this.mouseInteractionsEnabled) return;\r\n    event.preventDefault();\r\n\r\n    // Save history before adding new node\r\n    this.saveHistoryBeforeAction();\r\n\r\n    const canvasBounds =\r\n      this.canvasContainer.nativeElement.getBoundingClientRect();\r\n    const position = {\r\n      x:\r\n        (event.clientX - canvasBounds.left - this.viewport.x) /\r\n        this.viewport.zoom,\r\n      y:\r\n        (event.clientY - canvasBounds.top - this.viewport.y) /\r\n        this.viewport.zoom,\r\n    };\r\n\r\n    const safePosition = {\r\n      x: Math.max(0, position.x),\r\n      y: Math.max(0, position.y),\r\n    };\r\n\r\n    this.canvasDropped.emit({ event, position: safePosition });\r\n  }\r\n\r\n  onNodeSelected(nodeId: string): void {\r\n    this.selectedNodeId = nodeId;\r\n    this.nodeSelected.emit(nodeId);\r\n  }\r\n\r\n  onNodeDoubleClicked(nodeId: string): void {\r\n    this.nodeDoubleClicked.emit(nodeId);\r\n  }\r\n\r\n  onNodeMoved(data: {\r\n    nodeId: string;\r\n    position: { x: number; y: number };\r\n  }): void {\r\n    // Save history before moving node\r\n    this.saveHistoryBeforeAction();\r\n\r\n    // Update node position in internal state\r\n    const nodeIndex = this.nodes.findIndex((node) => node.id === data.nodeId);\r\n    if (nodeIndex !== -1) {\r\n      this.nodes[nodeIndex] = {\r\n        ...this.nodes[nodeIndex],\r\n        position: data.position,\r\n      };\r\n    }\r\n\r\n    // CRITICAL: Update edge paths immediately when node moves\r\n    console.log('🔗 Node moved - updating edge paths for:', data.nodeId);\r\n    this.updateEdgePaths();\r\n\r\n    this.nodeMoved.emit(data);\r\n    this.updateNodeConnectionPoints();\r\n\r\n    // Force immediate change detection to update connections\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  onStartConnection(data: {\r\n    nodeId: string;\r\n    handleType: 'source' | 'target';\r\n    event: MouseEvent;\r\n  }): void {\r\n    if (!this.enableConnections) return;\r\n\r\n    const node = this.nodes.find((n) => n.id === data.nodeId);\r\n    if (!node) return;\r\n\r\n    // Enhanced connection starting - use mouse position on the node as starting point\r\n    const canvasRect =\r\n      this.canvasContainer.nativeElement.getBoundingClientRect();\r\n\r\n    // Calculate the exact mouse position in canvas coordinates\r\n    const mouseX =\r\n      (data.event.clientX - canvasRect.left - this.viewport.x) /\r\n      this.viewport.zoom;\r\n    const mouseY =\r\n      (data.event.clientY - canvasRect.top - this.viewport.y) /\r\n      this.viewport.zoom;\r\n\r\n    // Use the mouse position as the starting point for more natural connections\r\n    let handleX = mouseX;\r\n    let handleY = mouseY;\r\n\r\n    // Get the node's connection points for fallback\r\n    const connectionPoints = this.nodeConnectionPoints[data.nodeId];\r\n    if (connectionPoints) {\r\n      // If we have calculated connection points, use the nearest edge point\r\n      const nodeElement = document.querySelector(\r\n        `[data-node-id=\"${data.nodeId}\"]`,\r\n      ) as HTMLElement;\r\n      if (nodeElement) {\r\n        const rect = nodeElement.getBoundingClientRect();\r\n        const nodeX =\r\n          (rect.left - canvasRect.left - this.viewport.x) / this.viewport.zoom;\r\n        const nodeY =\r\n          (rect.top - canvasRect.top - this.viewport.y) / this.viewport.zoom;\r\n        const nodeWidth = rect.width / this.viewport.zoom;\r\n        const nodeHeight = rect.height / this.viewport.zoom;\r\n\r\n        // Determine which edge of the node is closest to the mouse\r\n        const relativeX = mouseX - nodeX;\r\n        const relativeY = mouseY - nodeY;\r\n\r\n        // Clamp to node edges for clean connection start points\r\n        if (relativeX <= 0) {\r\n          // Left edge\r\n          handleX = nodeX;\r\n          handleY = Math.max(nodeY, Math.min(nodeY + nodeHeight, mouseY));\r\n        } else if (relativeX >= nodeWidth) {\r\n          // Right edge\r\n          handleX = nodeX + nodeWidth;\r\n          handleY = Math.max(nodeY, Math.min(nodeY + nodeHeight, mouseY));\r\n        } else if (relativeY <= 0) {\r\n          // Top edge\r\n          handleX = Math.max(nodeX, Math.min(nodeX + nodeWidth, mouseX));\r\n          handleY = nodeY;\r\n        } else if (relativeY >= nodeHeight) {\r\n          // Bottom edge\r\n          handleX = Math.max(nodeX, Math.min(nodeX + nodeWidth, mouseX));\r\n          handleY = nodeY + nodeHeight;\r\n        }\r\n      }\r\n    }\r\n\r\n    this.tempConnection = {\r\n      isActive: true,\r\n      sourceNodeId: data.nodeId,\r\n      sourceHandleType: data.handleType,\r\n      sourceX: handleX,\r\n      sourceY: handleY,\r\n      targetX: mouseX,\r\n      targetY: mouseY,\r\n    };\r\n\r\n    this.connectionStarted.emit(data);\r\n  }\r\n\r\n  onDeleteNode(nodeId: string): void {\r\n    // Save history before deleting node\r\n    this.saveHistoryBeforeAction();\r\n\r\n    // Remove node from internal state\r\n    this.nodes = this.nodes.filter((node) => node.id !== nodeId);\r\n\r\n    // Remove any edges connected to this node\r\n    this.edges = this.edges.filter(\r\n      (edge) => edge.source !== nodeId && edge.target !== nodeId,\r\n    );\r\n\r\n    this.nodeRemoved.emit(nodeId);\r\n    if (this.selectedNodeId === nodeId) {\r\n      this.selectedNodeId = null;\r\n    }\r\n\r\n    this.updateNodeConnectionPoints();\r\n  }\r\n\r\n  onCanvasMouseMove(event: MouseEvent): void {\r\n    // Handle canvas panning\r\n    if (this.viewport.isDragging && this.enablePan) {\r\n      const deltaX = event.clientX - this.viewport.lastMouseX;\r\n      const deltaY = event.clientY - this.viewport.lastMouseY;\r\n\r\n      this.viewport.x += deltaX;\r\n      this.viewport.y += deltaY;\r\n\r\n      this.viewport.lastMouseX = event.clientX;\r\n      this.viewport.lastMouseY = event.clientY;\r\n\r\n      this.cdr.detectChanges();\r\n      return;\r\n    }\r\n\r\n    if (!this.tempConnection.isActive) return;\r\n\r\n    // Update the temporary connection endpoint\r\n    const canvasRect =\r\n      this.canvasContainer.nativeElement.getBoundingClientRect();\r\n    const targetX =\r\n      (event.clientX - canvasRect.left - this.viewport.x) / this.viewport.zoom;\r\n    const targetY =\r\n      (event.clientY - canvasRect.top - this.viewport.y) / this.viewport.zoom;\r\n\r\n    this.tempConnection = {\r\n      ...this.tempConnection,\r\n      targetX,\r\n      targetY,\r\n    };\r\n\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  onCanvasWheel(event: WheelEvent): void {\r\n    if (!this.enableZoom || !this.mouseInteractionsEnabled) return;\r\n    event.preventDefault();\r\n\r\n    const delta = -event.deltaY;\r\n    const zoomSpeed = 0.001;\r\n    const newZoom = Math.max(\r\n      this.minZoom,\r\n      Math.min(this.maxZoom, this.viewport.zoom + delta * zoomSpeed),\r\n    );\r\n\r\n    if (newZoom !== this.viewport.zoom) {\r\n      const rect = this.canvasContainer.nativeElement.getBoundingClientRect();\r\n      const mouseX = event.clientX - rect.left;\r\n      const mouseY = event.clientY - rect.top;\r\n\r\n      const zoomRatio = newZoom / this.viewport.zoom;\r\n      const newX = mouseX - (mouseX - this.viewport.x) * zoomRatio;\r\n      const newY = mouseY - (mouseY - this.viewport.y) * zoomRatio;\r\n\r\n      this.viewport.zoom = newZoom;\r\n      this.viewport.x = newX;\r\n      this.viewport.y = newY;\r\n\r\n      this.updateNodeConnectionPoints();\r\n      this.viewportChanged.emit(this.viewport);\r\n      this.cdr.detectChanges();\r\n    }\r\n  }\r\n\r\n  onCanvasMouseDown(event: MouseEvent): void {\r\n    if (!this.enablePan || !this.mouseInteractionsEnabled) return;\r\n\r\n    // Start canvas dragging when:\r\n    // 1. Middle mouse button is pressed\r\n    // 2. Alt+left click\r\n    // 3. Pan mode is active and left click\r\n    if (\r\n      event.button === 1 ||\r\n      (event.button === 0 && event.altKey) ||\r\n      (event.button === 0 && this.canvasMode === 'pan')\r\n    ) {\r\n      this.viewport.isDragging = true;\r\n      this.viewport.lastMouseX = event.clientX;\r\n      this.viewport.lastMouseY = event.clientY;\r\n\r\n      // Update cursor during drag\r\n      if (this.canvasContainer) {\r\n        this.canvasContainer.nativeElement.style.cursor = 'grabbing';\r\n      }\r\n\r\n      event.preventDefault();\r\n    }\r\n  }\r\n\r\n  onCanvasMouseUp(event: MouseEvent): void {\r\n    this.viewport.isDragging = false;\r\n\r\n    // Restore cursor based on current mode\r\n    if (this.canvasContainer) {\r\n      this.canvasContainer.nativeElement.style.cursor =\r\n        this.canvasMode === 'pan' ? 'grab' : 'default';\r\n    }\r\n\r\n    if (!this.tempConnection.isActive || !this.enableConnections) return;\r\n\r\n    const target = event.target as HTMLElement;\r\n\r\n    // Enhanced connection logic - allow connections to any part of a node\r\n    const nodeElement = target.closest('[data-node-id]');\r\n\r\n    if (nodeElement && this.tempConnection.sourceNodeId) {\r\n      const sourceNodeId = this.tempConnection.sourceNodeId;\r\n      const targetNodeId = nodeElement.getAttribute('data-node-id');\r\n\r\n      // Allow connections between different nodes\r\n      if (targetNodeId && sourceNodeId !== targetNodeId) {\r\n        // Check if connection already exists to prevent duplicates\r\n        const existingConnection = this.edges.find(\r\n          (edge) =>\r\n            (edge.source === sourceNodeId && edge.target === targetNodeId) ||\r\n            (edge.source === targetNodeId && edge.target === sourceNodeId),\r\n        );\r\n\r\n        if (!existingConnection) {\r\n          // Save history before creating connection\r\n          this.saveHistoryBeforeAction();\r\n\r\n          const newEdge: CanvasEdge = {\r\n            id: `${sourceNodeId}-${targetNodeId}`,\r\n            source: sourceNodeId,\r\n            target: targetNodeId,\r\n            animated: false, // Beautiful static connections\r\n          };\r\n\r\n          // Add edge to internal state\r\n          this.edges.push(newEdge);\r\n\r\n          this.connectionCreated.emit(newEdge);\r\n        }\r\n      }\r\n    }\r\n\r\n    this.tempConnection = { isActive: false };\r\n  }\r\n\r\n  updateNodeConnectionPoints(): void {\r\n    if (this.updateConnectionPointsFrame) {\r\n      cancelAnimationFrame(this.updateConnectionPointsFrame);\r\n    }\r\n\r\n    // SIMPLIFIED: Immediate update for consistent connection rendering\r\n    this.updateConnectionPointsFrame = requestAnimationFrame(() => {\r\n      this.nodeConnectionPoints = {};\r\n\r\n      for (const node of this.nodes) {\r\n        const nodeElement = document.querySelector(\r\n          `[data-node-id=\"${node.id}\"]`,\r\n        ) as HTMLElement;\r\n\r\n        // Calculate connection points even for nodes outside viewport\r\n        let nodeX: number, nodeY: number, nodeWidth: number, nodeHeight: number;\r\n\r\n        if (nodeElement) {\r\n          // Node is in DOM - use actual measurements\r\n          const rect = nodeElement.getBoundingClientRect();\r\n          const canvasRect =\r\n            this.canvasContainer?.nativeElement.getBoundingClientRect();\r\n          if (!canvasRect) continue;\r\n\r\n          // In execute mode, always use the actual DOM position since CSS transforms the layout\r\n          if (this.isExecuteMode) {\r\n            // Use actual DOM position without viewport adjustments for execute mode\r\n            nodeX = rect.left - canvasRect.left;\r\n            nodeY = rect.top - canvasRect.top;\r\n            nodeWidth = rect.width;\r\n            nodeHeight = rect.height;\r\n          } else {\r\n            // Build mode - use viewport-adjusted coordinates\r\n            nodeX =\r\n              (rect.left - canvasRect.left - this.viewport.x) /\r\n              this.viewport.zoom;\r\n            nodeY =\r\n              (rect.top - canvasRect.top - this.viewport.y) /\r\n              this.viewport.zoom;\r\n            nodeWidth = rect.width / this.viewport.zoom;\r\n            nodeHeight = rect.height / this.viewport.zoom;\r\n          }\r\n        } else {\r\n          // Node is outside viewport - use node position data\r\n          nodeX = node.position.x;\r\n          nodeY = node.position.y;\r\n          // Use default dimensions based on mode\r\n          nodeWidth = this.isExecuteMode ? 55 : 90; // Execute mode nodes are 55px (circular with border)\r\n          nodeHeight = this.isExecuteMode ? 55 : 48; // Execute mode nodes are 55px (circular with border)\r\n        }\r\n\r\n        if (this.isExecuteMode) {\r\n          // Execute mode - icon center to icon center connections\r\n          const centerX = nodeX + nodeWidth / 2;\r\n          const centerY = nodeY + nodeHeight / 2;\r\n\r\n          // All connection points are at the center of the circular icon for icon-to-icon connections\r\n          this.nodeConnectionPoints[node.id] = {\r\n            top: { x: centerX, y: centerY },\r\n            right: { x: centerX, y: centerY },\r\n            bottom: { x: centerX, y: centerY },\r\n            left: { x: centerX, y: centerY },\r\n          };\r\n\r\n          console.log(\r\n            `Execute mode node ${node.id}: center(${centerX}, ${centerY})`,\r\n          );\r\n        } else {\r\n          // Build mode - account for node structure and padding\r\n          // The node has 10px padding and the icon is 40px with center at 30px from left edge\r\n          const iconCenterX = nodeX + 30; // 10px padding + 20px to icon center\r\n          const centerY = nodeY + nodeHeight / 2; // Vertical center\r\n\r\n          // Create connection points optimized for the node structure\r\n          this.nodeConnectionPoints[node.id] = {\r\n            top: { x: iconCenterX, y: nodeY },\r\n            right: { x: nodeX + nodeWidth, y: centerY },\r\n            bottom: { x: iconCenterX, y: nodeY + nodeHeight },\r\n            left: { x: nodeX, y: centerY },\r\n          };\r\n        }\r\n      }\r\n\r\n      // Trigger change detection immediately for consistent rendering\r\n      this.cdr.detectChanges();\r\n      this.updateConnectionPointsFrame = null;\r\n    });\r\n  }\r\n\r\n  // REMOVED getEdgePath getter to prevent infinite loops\r\n  // The template now uses edge.pathData directly\r\n\r\n  private calculateEdgePath(\r\n    sourceNode: CanvasNode,\r\n    targetNode: CanvasNode,\r\n  ): string {\r\n    // Calculate connection points based on mode\r\n    let sourceX: number, sourceY: number, targetX: number, targetY: number;\r\n\r\n    if (this.isExecuteMode) {\r\n      // Execute mode - center to center\r\n      sourceX = sourceNode.position.x + 27.5; // Center of 55px node\r\n      sourceY = sourceNode.position.y + 27.5;\r\n      targetX = targetNode.position.x + 27.5;\r\n      targetY = targetNode.position.y + 27.5;\r\n    } else {\r\n      // Build mode - icon center to icon center for consistency\r\n      sourceX = sourceNode.position.x + 30; // Icon center (10px padding + 20px to center)\r\n      sourceY = sourceNode.position.y + 30; // Icon center\r\n      targetX = targetNode.position.x + 30;\r\n      targetY = targetNode.position.y + 30;\r\n    }\r\n\r\n    // Create simple, reliable connection path\r\n    return this.createConnectionPath(\r\n      { x: sourceX, y: sourceY },\r\n      { x: targetX, y: targetY },\r\n    );\r\n  }\r\n\r\n  // Pre-calculate all edge paths when nodes or edges change\r\n  public updateEdgePaths(): void {\r\n    console.log(\r\n      '🔗 updateEdgePaths - Current nodes:',\r\n      this.nodes.map((n) => ({ id: n.id, position: n.position })),\r\n    );\r\n    console.log(\r\n      '🔗 updateEdgePaths - Current edges:',\r\n      this.edges.map((e) => ({ id: e.id, source: e.source, target: e.target })),\r\n    );\r\n\r\n    this.edges = this.edges.map((edge) => {\r\n      const sourceNode = this.nodes.find((n) => n.id === edge.source);\r\n      const targetNode = this.nodes.find((n) => n.id === edge.target);\r\n\r\n      if (sourceNode && targetNode) {\r\n        const pathData = this.calculateEdgePath(sourceNode, targetNode);\r\n        console.log(`🔗 SIMPLE: ${edge.id} -> ${pathData}`);\r\n        console.log(\r\n          `🔗 SOURCE: ${sourceNode.id} at (${sourceNode.position.x}, ${sourceNode.position.y})`,\r\n        );\r\n        console.log(\r\n          `🔗 TARGET: ${targetNode.id} at (${targetNode.position.x}, ${targetNode.position.y})`,\r\n        );\r\n        return {\r\n          ...edge,\r\n          pathData: pathData,\r\n        } as any;\r\n      } else {\r\n        // Fallback for missing nodes\r\n        console.log(\r\n          `🔗 WARNING: Missing nodes for edge ${edge.id}, using empty path`,\r\n        );\r\n        return {\r\n          ...edge,\r\n          pathData: '',\r\n        } as any;\r\n      }\r\n    });\r\n\r\n    console.log('🔗 updateEdgePaths completed - All edges now have pathData');\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  private findNearestConnectionPoints(\r\n    sourcePoints: any,\r\n    targetPoints: any,\r\n  ): {\r\n    sourcePoint: { x: number; y: number };\r\n    targetPoint: { x: number; y: number };\r\n  } {\r\n    if (this.isExecuteMode) {\r\n      // Execute mode - icon center to icon center connections\r\n      // Since all connection points are at the center, use the center point directly\r\n      return {\r\n        sourcePoint: sourcePoints.top, // All points are the same (center), so any will work\r\n        targetPoint: targetPoints.top, // All points are the same (center), so any will work\r\n      };\r\n    } else {\r\n      // Build mode - find optimal connection points for any node placement\r\n      // Calculate all possible connection combinations\r\n      const sourcePointOptions = [\r\n        { point: sourcePoints.right, name: 'right' },\r\n        { point: sourcePoints.bottom, name: 'bottom' },\r\n        { point: sourcePoints.left, name: 'left' },\r\n        { point: sourcePoints.top, name: 'top' },\r\n      ];\r\n\r\n      const targetPointOptions = [\r\n        { point: targetPoints.left, name: 'left' },\r\n        { point: targetPoints.top, name: 'top' },\r\n        { point: targetPoints.right, name: 'right' },\r\n        { point: targetPoints.bottom, name: 'bottom' },\r\n      ];\r\n\r\n      let bestDistance = Infinity;\r\n      let bestSourcePoint = sourcePoints.right;\r\n      let bestTargetPoint = targetPoints.left;\r\n\r\n      // Find the shortest distance combination\r\n      for (const sourceOption of sourcePointOptions) {\r\n        for (const targetOption of targetPointOptions) {\r\n          const distance = Math.sqrt(\r\n            Math.pow(targetOption.point.x - sourceOption.point.x, 2) +\r\n              Math.pow(targetOption.point.y - sourceOption.point.y, 2),\r\n          );\r\n\r\n          if (distance < bestDistance) {\r\n            bestDistance = distance;\r\n            bestSourcePoint = sourceOption.point;\r\n            bestTargetPoint = targetOption.point;\r\n          }\r\n        }\r\n      }\r\n\r\n      return {\r\n        sourcePoint: bestSourcePoint,\r\n        targetPoint: bestTargetPoint,\r\n      };\r\n    }\r\n  }\r\n\r\n  private getConnectionDirection(\r\n    sourcePoint: { x: number; y: number },\r\n    targetPoint: { x: number; y: number },\r\n  ): string {\r\n    const dx = targetPoint.x - sourcePoint.x;\r\n    const dy = targetPoint.y - sourcePoint.y;\r\n\r\n    if (Math.abs(dx) > Math.abs(dy)) {\r\n      return dx > 0 ? 'right' : 'left';\r\n    } else {\r\n      return dy > 0 ? 'bottom' : 'top';\r\n    }\r\n  }\r\n\r\n  private adjustTargetForArrow(\r\n    targetPoint: { x: number; y: number },\r\n    direction: string,\r\n    offset: number,\r\n  ): { x: number; y: number } {\r\n    switch (direction) {\r\n      case 'right':\r\n        return { x: targetPoint.x - offset, y: targetPoint.y };\r\n      case 'left':\r\n        return { x: targetPoint.x + offset, y: targetPoint.y };\r\n      case 'bottom':\r\n        return { x: targetPoint.x, y: targetPoint.y - offset };\r\n      case 'top':\r\n        return { x: targetPoint.x, y: targetPoint.y + offset };\r\n      default:\r\n        return targetPoint;\r\n    }\r\n  }\r\n\r\n  private createConnectionPath(\r\n    sourcePoint: { x: number; y: number },\r\n    targetPoint: { x: number; y: number },\r\n  ): string {\r\n    // SIMPLIFIED: Create straight lines for consistent vertical stacking\r\n    const sourceX = sourcePoint.x;\r\n    const sourceY = sourcePoint.y;\r\n    const targetX = targetPoint.x;\r\n    const targetY = targetPoint.y;\r\n\r\n    if (this.isExecuteMode) {\r\n      // Execute mode - direct line between centers with arrow spacing\r\n      const dx = targetX - sourceX;\r\n      const dy = targetY - sourceY;\r\n      const length = Math.sqrt(dx * dx + dy * dy);\r\n\r\n      if (length < 20) {\r\n        return `M ${sourceX} ${sourceY} L ${targetX} ${targetY}`;\r\n      }\r\n\r\n      const unitX = dx / length;\r\n      const unitY = dy / length;\r\n      const sourceRadius = 27;\r\n      const targetRadius = 20;\r\n      const arrowSpace = 8;\r\n\r\n      const adjustedSourceX = sourceX + unitX * sourceRadius;\r\n      const adjustedSourceY = sourceY + unitY * sourceRadius;\r\n      const adjustedTargetX = targetX - unitX * (targetRadius + arrowSpace);\r\n      const adjustedTargetY = targetY - unitY * (targetRadius + arrowSpace);\r\n\r\n      return `M ${adjustedSourceX} ${adjustedSourceY} L ${adjustedTargetX} ${adjustedTargetY}`;\r\n    } else {\r\n      // Build mode - ALWAYS STRAIGHT LINES regardless of node positioning\r\n      const dx = targetX - sourceX;\r\n      const dy = targetY - sourceY;\r\n      const distance = Math.sqrt(dx * dx + dy * dy);\r\n\r\n      if (distance < 20) {\r\n        // Very close nodes - direct line\r\n        return `M ${sourceX} ${sourceY} L ${targetX} ${targetY}`;\r\n      }\r\n\r\n      // Calculate arrow spacing to ensure arrows are visible\r\n      const unitX = dx / distance;\r\n      const unitY = dy / distance;\r\n\r\n      // Adjust source to start from node edge\r\n      const sourceRadius = 30; // Start from edge of source node\r\n      const adjustedSourceX = sourceX + unitX * sourceRadius;\r\n      const adjustedSourceY = sourceY + unitY * sourceRadius;\r\n\r\n      // Adjust target to end before the node (leave space for arrow)\r\n      const targetNodeRadius = 30; // Target node radius\r\n      const arrowLength = 15; // Space for the arrow itself\r\n      const adjustedTargetX =\r\n        targetX - unitX * (targetNodeRadius + arrowLength);\r\n      const adjustedTargetY =\r\n        targetY - unitY * (targetNodeRadius + arrowLength);\r\n\r\n      // ALWAYS use straight lines - no curves\r\n      return `M ${adjustedSourceX} ${adjustedSourceY} L ${adjustedTargetX} ${adjustedTargetY}`;\r\n    }\r\n  }\r\n\r\n  getTempConnectionPath(): string {\r\n    if (\r\n      !this.tempConnection.isActive ||\r\n      this.tempConnection.sourceX === undefined ||\r\n      this.tempConnection.sourceY === undefined ||\r\n      this.tempConnection.targetX === undefined ||\r\n      this.tempConnection.targetY === undefined\r\n    ) {\r\n      return '';\r\n    }\r\n\r\n    const sourceX = this.tempConnection.sourceX;\r\n    const sourceY = this.tempConnection.sourceY;\r\n    const targetX = this.tempConnection.targetX;\r\n    const targetY = this.tempConnection.targetY;\r\n\r\n    // Create beautiful Bézier curves exactly like workflow editor\r\n    const dx = targetX - sourceX;\r\n    const dy = targetY - sourceY;\r\n    const distance = Math.sqrt(dx * dx + dy * dy);\r\n\r\n    // Use the exact same offset calculation as your workflow editor\r\n    const baseOffset = Math.min(100, distance * 0.5);\r\n    const offset = Math.max(50, baseOffset);\r\n\r\n    // Create horizontal Bézier curve with proper control points\r\n    const controlPointX1 = sourceX + offset;\r\n    const controlPointX2 = targetX - offset;\r\n\r\n    return `M ${sourceX} ${sourceY} C ${controlPointX1} ${sourceY}, ${controlPointX2} ${targetY}, ${targetX} ${targetY}`;\r\n  }\r\n\r\n  // Toolbar actions\r\n  onUndo(): void {\r\n    if (this.historyIndex > 0) {\r\n      this.historyIndex--;\r\n      const state = this.history[this.historyIndex];\r\n      this.restoreState(state);\r\n    }\r\n    this.undoAction.emit();\r\n  }\r\n\r\n  onRedo(): void {\r\n    if (this.historyIndex < this.history.length - 1) {\r\n      this.historyIndex++;\r\n      const state = this.history[this.historyIndex];\r\n      this.restoreState(state);\r\n    }\r\n    this.redoAction.emit();\r\n  }\r\n\r\n  onReset(): void {\r\n    // Save current state before reset\r\n    this.saveToHistory();\r\n\r\n    // Clear all nodes and edges\r\n    this.nodes = [];\r\n    this.edges = [];\r\n    this.selectedNodeId = null;\r\n    this.tempConnection = { isActive: false };\r\n\r\n    // Reset viewport\r\n    this.resetViewport();\r\n\r\n    // Emit state change to parent component\r\n    this.stateChanged.emit({\r\n      nodes: [],\r\n      edges: [],\r\n    });\r\n\r\n    // Emit events\r\n    this.nodeRemoved.emit('all'); // Special case for clearing all\r\n    this.resetAction.emit();\r\n  }\r\n\r\n  onPrimaryButtonClick(): void {\r\n    this.primaryButtonClicked.emit();\r\n  }\r\n\r\n  // History management methods\r\n  private saveToHistory(): void {\r\n    // Don't save history during state restoration\r\n    if (this.isRestoringState) return;\r\n\r\n    // Remove any history after current index (when we're not at the end)\r\n    this.history = this.history.slice(0, this.historyIndex + 1);\r\n\r\n    // Add current state to history\r\n    const currentState = {\r\n      nodes: JSON.parse(JSON.stringify(this.nodes)),\r\n      edges: JSON.parse(JSON.stringify(this.edges)),\r\n    };\r\n\r\n    this.history.push(currentState);\r\n    this.historyIndex = this.history.length - 1;\r\n\r\n    // Limit history size\r\n    if (this.history.length > this.maxHistorySize) {\r\n      this.history.shift();\r\n      this.historyIndex--;\r\n    }\r\n  }\r\n\r\n  private restoreState(state: {\r\n    nodes: CanvasNode[];\r\n    edges: CanvasEdge[];\r\n  }): void {\r\n    this.isRestoringState = true;\r\n\r\n    // Update the internal state\r\n    this.nodes = [...state.nodes];\r\n    this.edges = [...state.edges];\r\n\r\n    // Clear selection and temp connections\r\n    this.selectedNodeId = null;\r\n    this.tempConnection = { isActive: false };\r\n\r\n    // Update connection points\r\n    this.updateNodeConnectionPoints();\r\n\r\n    // Emit state change to parent component\r\n    this.stateChanged.emit({\r\n      nodes: [...this.nodes],\r\n      edges: [...this.edges],\r\n    });\r\n\r\n    // Reset flag after a short delay\r\n    setTimeout(() => {\r\n      this.isRestoringState = false;\r\n    }, 100);\r\n  }\r\n\r\n  // Save history before actions\r\n  private saveHistoryBeforeAction(): void {\r\n    this.saveToHistory();\r\n  }\r\n\r\n  // Public methods for external components to add/remove nodes and edges\r\n  addNode(node: CanvasNode): void {\r\n    this.saveHistoryBeforeAction();\r\n    this.nodes.push(node);\r\n    this.updateNodeConnectionPoints();\r\n    this.nodeAdded.emit(node);\r\n  }\r\n\r\n  addEdge(edge: CanvasEdge): void {\r\n    this.saveHistoryBeforeAction();\r\n    this.edges.push(edge);\r\n  }\r\n\r\n  removeNode(nodeId: string): void {\r\n    this.onDeleteNode(nodeId);\r\n  }\r\n\r\n  removeEdge(edgeId: string): void {\r\n    this.saveHistoryBeforeAction();\r\n    this.edges = this.edges.filter((edge) => edge.id !== edgeId);\r\n  }\r\n\r\n  // Canvas tool methods\r\n  toggleGrid(): void {\r\n    this.showGridDots = !this.showGridDots;\r\n    this.showGrid = this.showGridDots;\r\n  }\r\n\r\n  setPanMode(): void {\r\n    this.canvasMode = 'pan';\r\n    this.mouseInteractionsEnabled = true;\r\n    if (this.canvasContainer) {\r\n      this.canvasContainer.nativeElement.style.cursor = 'grab';\r\n    }\r\n  }\r\n\r\n  setSelectMode(): void {\r\n    this.canvasMode = 'select';\r\n    this.mouseInteractionsEnabled = true;\r\n    if (this.canvasContainer) {\r\n      this.canvasContainer.nativeElement.style.cursor = 'default';\r\n    }\r\n  }\r\n\r\n  setZoomMode(): void {\r\n    this.canvasMode = 'zoom';\r\n    this.mouseInteractionsEnabled = true;\r\n    if (this.canvasContainer) {\r\n      this.canvasContainer.nativeElement.style.cursor = 'zoom-in';\r\n    }\r\n  }\r\n\r\n  disableMouseInteractions(): void {\r\n    this.canvasMode = 'disabled';\r\n    this.mouseInteractionsEnabled = false;\r\n    if (this.canvasContainer) {\r\n      this.canvasContainer.nativeElement.style.cursor = 'not-allowed';\r\n    }\r\n  }\r\n\r\n  zoomIn(): void {\r\n    const newZoom = Math.min(this.maxZoom, this.viewport.zoom * 1.2);\r\n    this.setZoom(newZoom);\r\n  }\r\n\r\n  zoomOut(): void {\r\n    const newZoom = Math.max(this.minZoom, this.viewport.zoom / 1.2);\r\n    this.setZoom(newZoom);\r\n  }\r\n\r\n  private setZoom(newZoom: number): void {\r\n    if (newZoom !== this.viewport.zoom) {\r\n      // Get canvas center for zoom\r\n      const rect = this.canvasContainer.nativeElement.getBoundingClientRect();\r\n      const centerX = rect.width / 2;\r\n      const centerY = rect.height / 2;\r\n\r\n      // Calculate new position to zoom towards center\r\n      const zoomRatio = newZoom / this.viewport.zoom;\r\n      const newX = centerX - (centerX - this.viewport.x) * zoomRatio;\r\n      const newY = centerY - (centerY - this.viewport.y) * zoomRatio;\r\n\r\n      // Update viewport\r\n      this.viewport.zoom = newZoom;\r\n      this.viewport.x = newX;\r\n      this.viewport.y = newY;\r\n\r\n      // Update connection points\r\n      this.updateNodeConnectionPoints();\r\n\r\n      // Emit viewport change\r\n      this.viewportChanged.emit(this.viewport);\r\n    }\r\n  }\r\n\r\n  // Built-in input field methods\r\n  onAgentNameChange(value: string): void {\r\n    this.agentNameControl.setValue(value);\r\n    this.agentNameChanged.emit(value);\r\n  }\r\n\r\n  onAgentTypeChange(value: string | string[]): void {\r\n    const finalValue = Array.isArray(value) ? value[0] : value;\r\n    this.agentTypeControl.setValue(finalValue);\r\n    this.agentTypeChanged.emit(finalValue);\r\n  }\r\n\r\n  // Built-in metadata dropdown methods\r\n  toggleMetadataDropdown(): void {\r\n    this.isMetadataDropdownOpen = !this.isMetadataDropdownOpen;\r\n  }\r\n\r\n  closeMetadataDropdown(): void {\r\n    this.isMetadataDropdownOpen = false;\r\n  }\r\n\r\n  onDropdownSelect(\r\n    selectedValue: string | string[],\r\n    level: number,\r\n    currentType: string,\r\n    nextType: string,\r\n  ): void {\r\n    const value = Array.isArray(selectedValue)\r\n      ? selectedValue[0]\r\n      : selectedValue;\r\n    if (!value) return;\r\n\r\n    // Update the current dropdown value\r\n    switch (currentType) {\r\n      case 'org':\r\n        this.orgControl.setValue(value);\r\n        // Reset dependent dropdowns\r\n        this.domainControl.setValue('');\r\n        this.projectControl.setValue('');\r\n        this.teamControl.setValue('');\r\n        // Load domain options\r\n        this.loadDomainOptions(value);\r\n        break;\r\n      case 'domain':\r\n        this.domainControl.setValue(value);\r\n        // Reset dependent dropdowns\r\n        this.projectControl.setValue('');\r\n        this.teamControl.setValue('');\r\n        // Load project options\r\n        this.loadProjectOptions(this.orgControl.value || '', value);\r\n        break;\r\n      case 'project':\r\n        this.projectControl.setValue(value);\r\n        // Reset dependent dropdown\r\n        this.teamControl.setValue('');\r\n        // Load team options\r\n        this.loadTeamOptions(\r\n          this.orgControl.value || '',\r\n          this.domainControl.value || '',\r\n          value,\r\n        );\r\n        break;\r\n      case 'team':\r\n        this.teamControl.setValue(value);\r\n        break;\r\n    }\r\n  }\r\n\r\n  applyMetadata(): void {\r\n    const hasValues =\r\n      this.orgControl.value ||\r\n      this.domainControl.value ||\r\n      this.projectControl.value ||\r\n      this.teamControl.value;\r\n\r\n    if (hasValues) {\r\n      this.metadataStatus =\r\n        this.inputFieldsConfig.metadata?.statusText?.saved ||\r\n        'Metadata Information saved';\r\n    } else {\r\n      this.metadataStatus =\r\n        this.inputFieldsConfig.metadata?.statusText?.notSaved ||\r\n        'Metadata Information not saved';\r\n    }\r\n\r\n    // Emit metadata change event\r\n    this.metadataChanged.emit({\r\n      org: this.orgControl.value || '',\r\n      domain: this.domainControl.value || '',\r\n      project: this.projectControl.value || '',\r\n      team: this.teamControl.value || '',\r\n    });\r\n\r\n    this.closeMetadataDropdown();\r\n  }\r\n\r\n  cancelMetadata(): void {\r\n    this.closeMetadataDropdown();\r\n  }\r\n\r\n  // Load dropdown options (same API pattern as nav-item)\r\n  private loadDomainOptions(org: string): void {\r\n    const domainOptionsMap: { [key: string]: SelectOption[] } = {\r\n      ascendion: [\r\n        { value: 'engineering', label: 'Engineering' },\r\n        { value: 'marketing', label: 'Marketing' },\r\n        { value: 'sales', label: 'Sales' },\r\n      ],\r\n      company2: [\r\n        { value: 'tech', label: 'Technology' },\r\n        { value: 'operations', label: 'Operations' },\r\n      ],\r\n      company3: [\r\n        { value: 'research', label: 'Research' },\r\n        { value: 'development', label: 'Development' },\r\n      ],\r\n    };\r\n\r\n    this.dropdownValues['domain'] = domainOptionsMap[org] || [];\r\n  }\r\n\r\n  private loadProjectOptions(org: string, domain: string): void {\r\n    const projectOptions: SelectOption[] = [\r\n      { value: 'project1', label: 'Project Alpha' },\r\n      { value: 'project2', label: 'Project Beta' },\r\n      { value: 'project3', label: 'Project Gamma' },\r\n    ];\r\n\r\n    this.dropdownValues['project'] = projectOptions;\r\n  }\r\n\r\n  private loadTeamOptions(org: string, domain: string, project: string): void {\r\n    const teamOptions: SelectOption[] = [\r\n      { value: 'team1', label: 'Team Alpha' },\r\n      { value: 'team2', label: 'Team Beta' },\r\n      { value: 'team3', label: 'Team Gamma' },\r\n    ];\r\n\r\n    this.dropdownValues['team'] = teamOptions;\r\n  }\r\n\r\n  // Toolbar is now fixed at bottom center - no drag methods needed\r\n\r\n  // Toolbar is now fixed at bottom center via CSS - no positioning method needed\r\n\r\n  // Agent details dropdown methods\r\n  toggleAgentDetailsDropdown(): void {\r\n    this.isAgentDetailsDropdownOpen = !this.isAgentDetailsDropdownOpen;\r\n  }\r\n\r\n  closeAgentDetailsDropdown(): void {\r\n    this.isAgentDetailsDropdownOpen = false;\r\n  }\r\n\r\n  applyAgentDetails(): void {\r\n    const name = this.agentDetailNameControl.value || '';\r\n    const useCaseDetails = this.agentDetailControl.value || '';\r\n\r\n    // Emit agent details change event\r\n    this.agentDetailsChanged.emit({\r\n      name: name,\r\n      useCaseDetails: useCaseDetails,\r\n    });\r\n\r\n    this.closeAgentDetailsDropdown();\r\n  }\r\n\r\n  cancelAgentDetails(): void {\r\n    this.closeAgentDetailsDropdown();\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SACEA,SAAS,EACTC,KAAK,EACLC,MAAM,EACNC,YAAY,EACZC,SAAS,EAOTC,YAAY,QAEP,eAAe;AACtB,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SACEC,eAAe,EACfC,mBAAmB,EACnBC,oBAAoB,EACpBC,aAAa,QACR,wBAAwB;AA6FxB,IAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EAiMXC,GAAA;EA9LUC,eAAe;EACfC,YAAY;EAE1C;EACSC,KAAK,GAAiB,EAAE;EACxBC,KAAK,GAAiB,EAAE;EACxBC,QAAQ,GAAY,IAAI;EACxBC,SAAS,GAAY,KAAK,CAAC,CAAC;EAC5BC,UAAU,GAAY,KAAK,CAAC,CAAC;EAC7BC,iBAAiB,GAAY,IAAI;EACjCC,OAAO,GAAW,GAAG;EACrBC,OAAO,GAAW,CAAC;EACnBC,eAAe,GAAW,SAAS,CAAC,CAAC;EACrCC,eAAe,GAAW,gCAAgC;EAC1DC,eAAe,GAAa,CACnC,sDAAsD,EACtD,yCAAyC,EACzC,oCAAoC,EACpC,qBAAqB,CACtB;EACQC,WAAW,GAAY,IAAI;EAC3BC,iBAAiB,GAAW,SAAS;EACrCC,iBAAiB,GAAW,EAAE;EAC9BC,UAAU,GAAY,IAAI;EAC1BC,UAAU,GAAY,IAAI;EAC1BC,WAAW,GAAY,IAAI;EAC3BC,eAAe,GAAY,IAAI;EAC/BC,gBAAgB,GAAY,IAAI;EAChCC,aAAa,GAAY,IAAI;EAC7BC,mBAAmB,GAAY,IAAI;EACnCC,kBAAkB,GAAY,IAAI;EAClCC,aAAa,GAAY,KAAK;EAC9BC,eAAe,GAAY,IAAI;EAC/BC,wBAAwB,GAAY,IAAI;EAEjD;EACSC,gBAAgB,GAAY,KAAK;EACjCC,iBAAiB,GA6BtB,EAAE;EAEN;EACSC,gBAAgB,GAAW,EAAE;EAC7BC,mBAAmB,GAAW,EAAE;EAChCC,eAAe,GAKpB;IACFC,GAAG,EAAE,EAAE;IACPC,MAAM,EAAE,EAAE;IACVC,OAAO,EAAE,EAAE;IACXC,IAAI,EAAE;GACP;EAED;EACAC,sBAAsB,GAAG,KAAK;EAC9BC,cAAc,GAAG,gCAAgC;EAEjD;EACAC,0BAA0B,GAAG,KAAK;EAElC;EACSC,gBAAgB,GAAG,IAAI/C,WAAW,CAAC,EAAE,CAAC;EAC/CgD,gBAAgB,GAAG,IAAIhD,WAAW,CAAC,YAAY,CAAC;EAChDiD,uBAAuB,GAAG,IAAIjD,WAAW,CAAC,YAAY,CAAC;EAEvD;EACSkD,sBAAsB,GAAG,IAAIlD,WAAW,CAAC,EAAE,CAAC;EAC5CmD,kBAAkB,GAAG,IAAInD,WAAW,CAAC,EAAE,CAAC;EAEjD;EACAoD,UAAU,GAAG,IAAIpD,WAAW,CAAC,EAAE,CAAC;EAChCqD,aAAa,GAAG,IAAIrD,WAAW,CAAC,EAAE,CAAC;EACnCsD,cAAc,GAAG,IAAItD,WAAW,CAAC,EAAE,CAAC;EACpCuD,WAAW,GAAG,IAAIvD,WAAW,CAAC,EAAE,CAAC;EAEjC;EACAwD,cAAc,GAAsC;IAClDhB,GAAG,EAAE,CACH;MAAEiB,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAED,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAW,CAAE,EACzC;MAAED,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAW,CAAE,CAC1C;IACDjB,MAAM,EAAE,EAAE;IACVC,OAAO,EAAE,EAAE;IACXC,IAAI,EAAE;GACP;EAED;EACUgB,SAAS,GAAG,IAAI/D,YAAY,EAAc;EAC1CgE,WAAW,GAAG,IAAIhE,YAAY,EAAU;EACxCiE,SAAS,GAAG,IAAIjE,YAAY,EAGlC;EACMkE,YAAY,GAAG,IAAIlE,YAAY,EAAU;EACzCmE,iBAAiB,GAAG,IAAInE,YAAY,EAAU;EAE9CoE,iBAAiB,GAAG,IAAIpE,YAAY,EAI1C;EACMqE,iBAAiB,GAAG,IAAIrE,YAAY,EAAc;EAClDsE,aAAa,GAAG,IAAItE,YAAY,EAGtC;EACMuE,eAAe,GAAG,IAAIvE,YAAY,EAAkB;EACpDwE,UAAU,GAAG,IAAIxE,YAAY,EAAQ;EACrCyE,UAAU,GAAG,IAAIzE,YAAY,EAAQ;EACrC0E,WAAW,GAAG,IAAI1E,YAAY,EAAQ;EACtC2E,oBAAoB,GAAG,IAAI3E,YAAY,EAAQ;EAC/C4E,YAAY,GAAG,IAAI5E,YAAY,EAGrC;EACM6E,gBAAgB,GAAG,IAAI7E,YAAY,EAAU;EAC7C8E,gBAAgB,GAAG,IAAI9E,YAAY,EAAU;EAC7C+E,eAAe,GAAG,IAAI/E,YAAY,EAKxC;EACMgF,mBAAmB,GAAG,IAAIhF,YAAY,EAG5C;EAEJ;EACAiF,cAAc,GAAkB,IAAI;EACpCC,cAAc,GAAmB;IAAEC,QAAQ,EAAE;EAAK,CAAE;EACpDC,oBAAoB,GAAyB,EAAE;EAC/CC,QAAQ,GAAmB;IACzBC,IAAI,EAAE,CAAC;IACPC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJC,UAAU,EAAE,KAAK;IACjBC,UAAU,EAAE,CAAC;IACbC,UAAU,EAAE;GACb;EAED;EACQC,2BAA2B,GAAkB,IAAI;EAEzD;EACQC,OAAO,GAAmD,EAAE;EAC5DC,YAAY,GAAW,CAAC,CAAC;EACzBC,cAAc,GAAW,EAAE;EAC3BC,gBAAgB,GAAY,KAAK;EAEzC;EACAC,UAAU,GAAmB,QAAQ,CAAC,CAAC;EACvCC,YAAY,GAAY,IAAI;EAE5B;EACA;EAEA;EAEAC,YAAoBxF,GAAsB;IAAtB,KAAAA,GAAG,GAAHA,GAAG;EAAsB;EAE7CyF,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,gBAAgB,EAAE;IAEvB;IACA,IAAI,CAAClD,gBAAgB,CAACmD,YAAY,CAACC,SAAS,CAAE1C,KAAK,IAAI;MACrD,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK2C,SAAS,EAAE;QACzC,IAAI,CAAC3B,gBAAgB,CAAC4B,IAAI,CAAC5C,KAAK,CAAC;MACnC;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAACT,gBAAgB,CAACkD,YAAY,CAACC,SAAS,CAAE1C,KAAK,IAAI;MACrD,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK2C,SAAS,EAAE;QACzC,IAAI,CAAC1B,gBAAgB,CAAC2B,IAAI,CAAC5C,KAAK,CAAC;MACnC;IACF,CAAC,CAAC;EACJ;EAEQwC,gBAAgBA,CAAA;IACtB;IACA,IAAI,IAAI,CAAC5D,gBAAgB,EAAE;MACzB,IAAI,CAACU,gBAAgB,CAACuD,QAAQ,CAAC,IAAI,CAACjE,gBAAgB,EAAE;QACpDkE,SAAS,EAAE;OACZ,CAAC;IACJ;IAEA;IACA,IAAI,IAAI,CAAClE,gBAAgB,EAAE;MACzB,IAAI,CAACa,sBAAsB,CAACoD,QAAQ,CAAC,IAAI,CAACjE,gBAAgB,EAAE;QAC1DkE,SAAS,EAAE;OACZ,CAAC;IACJ;IACA,IAAI,IAAI,CAACjE,mBAAmB,EAAE;MAC5B,IAAI,CAACa,kBAAkB,CAACmD,QAAQ,CAAC,IAAI,CAAChE,mBAAmB,EAAE;QACzDiE,SAAS,EAAE;OACZ,CAAC;IACJ;IAEA;IACA,IAAI,IAAI,CAAChE,eAAe,EAAE;MACxB,IAAI,CAACa,UAAU,CAACkD,QAAQ,CAAC,IAAI,CAAC/D,eAAe,CAACC,GAAG,EAAE;QAAE+D,SAAS,EAAE;MAAK,CAAE,CAAC;MACxE,IAAI,CAAClD,aAAa,CAACiD,QAAQ,CAAC,IAAI,CAAC/D,eAAe,CAACE,MAAM,EAAE;QACvD8D,SAAS,EAAE;OACZ,CAAC;MACF,IAAI,CAACjD,cAAc,CAACgD,QAAQ,CAAC,IAAI,CAAC/D,eAAe,CAACG,OAAO,EAAE;QACzD6D,SAAS,EAAE;OACZ,CAAC;MACF,IAAI,CAAChD,WAAW,CAAC+C,QAAQ,CAAC,IAAI,CAAC/D,eAAe,CAACI,IAAI,EAAE;QACnD4D,SAAS,EAAE;OACZ,CAAC;IACJ;EACF;EAEA;EACAC,aAAaA,CAACC,KAAa,EAAEC,IAAgB;IAC3C,OAAOA,IAAI,CAACC,EAAE;EAChB;EAEAC,WAAWA,CAACC,OAAY;IACtB;IACA,IACEA,OAAO,CAAC,kBAAkB,CAAC,IAC3BA,OAAO,CAAC,qBAAqB,CAAC,IAC9BA,OAAO,CAAC,iBAAiB,CAAC,EAC1B;MACA,IAAI,CAACZ,gBAAgB,EAAE;IACzB;IAEA;IACA,IAAI,CAAC,IAAI,CAACL,gBAAgB,EAAE;MAC1B,IAAIiB,OAAO,CAACnG,KAAK,IAAImG,OAAO,CAACnG,KAAK,CAACoG,YAAY,EAAE;QAC/CC,OAAO,CAACC,GAAG,CACT,sCAAsC,EACtCH,OAAO,CAACnG,KAAK,CAACoG,YAAY,CAACG,GAAG,CAAEC,CAAM,KAAM;UAC1CP,EAAE,EAAEO,CAAC,CAACP,EAAE;UACRQ,QAAQ,EAAED,CAAC,CAACC;SACb,CAAC,CAAC,CACJ;QACD,IAAI,CAACzG,KAAK,GAAG,CAAC,GAAGmG,OAAO,CAACnG,KAAK,CAACoG,YAAY,CAAC;MAC9C;MACA,IAAID,OAAO,CAAClG,KAAK,IAAIkG,OAAO,CAAClG,KAAK,CAACmG,YAAY,EAAE;QAC/CC,OAAO,CAACC,GAAG,CACT,sCAAsC,EACtCH,OAAO,CAAClG,KAAK,CAACmG,YAAY,CAACG,GAAG,CAAEG,CAAM,KAAM;UAC1CT,EAAE,EAAES,CAAC,CAACT,EAAE;UACRU,MAAM,EAAED,CAAC,CAACC,MAAM;UAChBC,MAAM,EAAEF,CAAC,CAACE;SACX,CAAC,CAAC,CACJ;QACD,IAAI,CAAC3G,KAAK,GAAG,CAAC,GAAGkG,OAAO,CAAClG,KAAK,CAACmG,YAAY,CAAC;MAC9C;MAEA;MACA,IAAID,OAAO,CAACnG,KAAK,EAAE;QACjBqG,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;QACzD,IAAI,CAACO,0BAA0B,EAAE;QACjC,IAAI,CAACC,eAAe,EAAE;QACtB;QACAC,UAAU,CAAC,MAAM,IAAI,CAACF,0BAA0B,EAAE,EAAE,CAAC,CAAC;MACxD;MAEA,IAAIV,OAAO,CAAClG,KAAK,EAAE;QACjBoG,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;QACzD,IAAI,CAACQ,eAAe,EAAE;MACxB;IACF;IAEA;IACA,IAAIX,OAAO,CAAC,eAAe,CAAC,EAAE;MAC5B;MACA,IAAI,CAACU,0BAA0B,EAAE;IACnC;IAEA,IAAIV,OAAO,CAAC,0BAA0B,CAAC,EAAE;MACvC,IAAI,CAAC3E,wBAAwB,GAC3B2E,OAAO,CAAC,0BAA0B,CAAC,CAACC,YAAY;IACpD;EACF;EAEAY,eAAeA,CAAA;IACb,IAAI,CAACC,qBAAqB,EAAE;IAC5B;IACAC,qBAAqB,CAAC,MAAK;MACzB,IAAI,CAACL,0BAA0B,EAAE;MACjC;MACA,IAAI,CAACM,aAAa,EAAE;MACpB;MACA;MACA,IAAI,CAACC,aAAa,EAAE;IACtB,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT;IACAC,QAAQ,CAACC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACC,aAAa,CAAC;IAE3D;IACA,IAAI,IAAI,CAAC1C,2BAA2B,EAAE;MACpC2C,oBAAoB,CAAC,IAAI,CAAC3C,2BAA2B,CAAC;MACtD,IAAI,CAACA,2BAA2B,GAAG,IAAI;IACzC;EACF;EAEQmC,qBAAqBA,CAAA;IAC3B,MAAMS,OAAO,GAAG,IAAI,CAAC5H,eAAe,EAAE6H,aAAa;IACnD,IAAID,OAAO,EAAE;MACXJ,QAAQ,CAACM,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACJ,aAAa,CAACK,IAAI,CAAC,IAAI,CAAC,CAAC;MAEnEH,OAAO,CAACE,gBAAgB,CAAC,SAAS,EAAE,MAAK;QACvC,IAAI,IAAI,CAACrD,QAAQ,CAACI,UAAU,EAAE;UAC5B,IAAI,CAACkC,0BAA0B,EAAE;QACnC;MACF,CAAC,CAAC;IACJ;EACF;EAEQW,aAAa,GAAIM,KAAoB,IAAU;IACrD,IAAIA,KAAK,CAACC,GAAG,KAAK,GAAG,EAAE;MACrB;MACA,MAAMnB,MAAM,GAAGkB,KAAK,CAAClB,MAAqB;MAC1C,MAAMoB,YAAY,GAChBpB,MAAM,CAACqB,OAAO,KAAK,OAAO,IAC1BrB,MAAM,CAACqB,OAAO,KAAK,UAAU,IAC7BrB,MAAM,CAACsB,eAAe,KAAK,MAAM,IACjCtB,MAAM,CAACuB,OAAO,CAAC,aAAa,CAAC,IAC7BvB,MAAM,CAACuB,OAAO,CAAC,cAAc,CAAC,IAC9BvB,MAAM,CAACuB,OAAO,CAAC,aAAa,CAAC,IAC7BvB,MAAM,CAACuB,OAAO,CAAC,kBAAkB,CAAC,IAClCvB,MAAM,CAACuB,OAAO,CAAC,aAAa,CAAC,IAC7BvB,MAAM,CAACuB,OAAO,CAAC,UAAU,CAAC,IAC1BvB,MAAM,CAACuB,OAAO,CAAC,OAAO,CAAC;MAEzB,IAAI,CAACH,YAAY,EAAE;QACjB,IAAI,CAACI,aAAa,EAAE;QACpBN,KAAK,CAACO,cAAc,EAAE;MACxB;IACF;EACF,CAAC;EAEDD,aAAaA,CAAA;IACX,IAAI,CAAC7D,QAAQ,GAAG;MACdC,IAAI,EAAE,CAAC;MACPC,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJC,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE,CAAC;MACbC,UAAU,EAAE;KACb;IAED,IAAI,CAACgC,0BAA0B,EAAE;IACjC,IAAI,CAACpD,eAAe,CAACkC,IAAI,CAAC,IAAI,CAACpB,QAAQ,CAAC;IACxC,IAAI,CAAC1E,GAAG,CAACyI,aAAa,EAAE;EAC1B;EAEAC,UAAUA,CAACT,KAAgB;IACzB,IAAI,CAAC,IAAI,CAACzH,iBAAiB,IAAI,CAAC,IAAI,CAACmB,wBAAwB,EAAE;IAC/DsG,KAAK,CAACO,cAAc,EAAE;IACtB,IAAIP,KAAK,CAACU,YAAY,EAAE;MACtBV,KAAK,CAACU,YAAY,CAACC,UAAU,GAAG,MAAM;IACxC;EACF;EAEAC,MAAMA,CAACZ,KAAgB;IACrB,IAAI,CAAC,IAAI,CAACtG,wBAAwB,EAAE;IACpCsG,KAAK,CAACO,cAAc,EAAE;IAEtB;IACA,IAAI,CAACM,uBAAuB,EAAE;IAE9B,MAAMC,YAAY,GAChB,IAAI,CAAC9I,eAAe,CAAC6H,aAAa,CAACkB,qBAAqB,EAAE;IAC5D,MAAMpC,QAAQ,GAAG;MACfhC,CAAC,EACC,CAACqD,KAAK,CAACgB,OAAO,GAAGF,YAAY,CAACG,IAAI,GAAG,IAAI,CAACxE,QAAQ,CAACE,CAAC,IACpD,IAAI,CAACF,QAAQ,CAACC,IAAI;MACpBE,CAAC,EACC,CAACoD,KAAK,CAACkB,OAAO,GAAGJ,YAAY,CAACK,GAAG,GAAG,IAAI,CAAC1E,QAAQ,CAACG,CAAC,IACnD,IAAI,CAACH,QAAQ,CAACC;KACjB;IAED,MAAM0E,YAAY,GAAG;MACnBzE,CAAC,EAAE0E,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE3C,QAAQ,CAAChC,CAAC,CAAC;MAC1BC,CAAC,EAAEyE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE3C,QAAQ,CAAC/B,CAAC;KAC1B;IAED,IAAI,CAAClB,aAAa,CAACmC,IAAI,CAAC;MAAEmC,KAAK;MAAErB,QAAQ,EAAEyC;IAAY,CAAE,CAAC;EAC5D;EAEAG,cAAcA,CAACC,MAAc;IAC3B,IAAI,CAACnF,cAAc,GAAGmF,MAAM;IAC5B,IAAI,CAAClG,YAAY,CAACuC,IAAI,CAAC2D,MAAM,CAAC;EAChC;EAEAC,mBAAmBA,CAACD,MAAc;IAChC,IAAI,CAACjG,iBAAiB,CAACsC,IAAI,CAAC2D,MAAM,CAAC;EACrC;EAEAE,WAAWA,CAACC,IAGX;IACC;IACA,IAAI,CAACd,uBAAuB,EAAE;IAE9B;IACA,MAAMe,SAAS,GAAG,IAAI,CAAC1J,KAAK,CAAC2J,SAAS,CAAE3D,IAAI,IAAKA,IAAI,CAACC,EAAE,KAAKwD,IAAI,CAACH,MAAM,CAAC;IACzE,IAAII,SAAS,KAAK,CAAC,CAAC,EAAE;MACpB,IAAI,CAAC1J,KAAK,CAAC0J,SAAS,CAAC,GAAG;QACtB,GAAG,IAAI,CAAC1J,KAAK,CAAC0J,SAAS,CAAC;QACxBjD,QAAQ,EAAEgD,IAAI,CAAChD;OAChB;IACH;IAEA;IACAJ,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEmD,IAAI,CAACH,MAAM,CAAC;IACpE,IAAI,CAACxC,eAAe,EAAE;IAEtB,IAAI,CAAC3D,SAAS,CAACwC,IAAI,CAAC8D,IAAI,CAAC;IACzB,IAAI,CAAC5C,0BAA0B,EAAE;IAEjC;IACA,IAAI,CAAChH,GAAG,CAACyI,aAAa,EAAE;EAC1B;EAEAsB,iBAAiBA,CAACH,IAIjB;IACC,IAAI,CAAC,IAAI,CAACpJ,iBAAiB,EAAE;IAE7B,MAAM2F,IAAI,GAAG,IAAI,CAAChG,KAAK,CAAC6J,IAAI,CAAErD,CAAC,IAAKA,CAAC,CAACP,EAAE,KAAKwD,IAAI,CAACH,MAAM,CAAC;IACzD,IAAI,CAACtD,IAAI,EAAE;IAEX;IACA,MAAM8D,UAAU,GACd,IAAI,CAAChK,eAAe,CAAC6H,aAAa,CAACkB,qBAAqB,EAAE;IAE5D;IACA,MAAMkB,MAAM,GACV,CAACN,IAAI,CAAC3B,KAAK,CAACgB,OAAO,GAAGgB,UAAU,CAACf,IAAI,GAAG,IAAI,CAACxE,QAAQ,CAACE,CAAC,IACvD,IAAI,CAACF,QAAQ,CAACC,IAAI;IACpB,MAAMwF,MAAM,GACV,CAACP,IAAI,CAAC3B,KAAK,CAACkB,OAAO,GAAGc,UAAU,CAACb,GAAG,GAAG,IAAI,CAAC1E,QAAQ,CAACG,CAAC,IACtD,IAAI,CAACH,QAAQ,CAACC,IAAI;IAEpB;IACA,IAAIyF,OAAO,GAAGF,MAAM;IACpB,IAAIG,OAAO,GAAGF,MAAM;IAEpB;IACA,MAAMG,gBAAgB,GAAG,IAAI,CAAC7F,oBAAoB,CAACmF,IAAI,CAACH,MAAM,CAAC;IAC/D,IAAIa,gBAAgB,EAAE;MACpB;MACA,MAAMC,WAAW,GAAG9C,QAAQ,CAAC+C,aAAa,CACxC,kBAAkBZ,IAAI,CAACH,MAAM,IAAI,CACnB;MAChB,IAAIc,WAAW,EAAE;QACf,MAAME,IAAI,GAAGF,WAAW,CAACvB,qBAAqB,EAAE;QAChD,MAAM0B,KAAK,GACT,CAACD,IAAI,CAACvB,IAAI,GAAGe,UAAU,CAACf,IAAI,GAAG,IAAI,CAACxE,QAAQ,CAACE,CAAC,IAAI,IAAI,CAACF,QAAQ,CAACC,IAAI;QACtE,MAAMgG,KAAK,GACT,CAACF,IAAI,CAACrB,GAAG,GAAGa,UAAU,CAACb,GAAG,GAAG,IAAI,CAAC1E,QAAQ,CAACG,CAAC,IAAI,IAAI,CAACH,QAAQ,CAACC,IAAI;QACpE,MAAMiG,SAAS,GAAGH,IAAI,CAACI,KAAK,GAAG,IAAI,CAACnG,QAAQ,CAACC,IAAI;QACjD,MAAMmG,UAAU,GAAGL,IAAI,CAACM,MAAM,GAAG,IAAI,CAACrG,QAAQ,CAACC,IAAI;QAEnD;QACA,MAAMqG,SAAS,GAAGd,MAAM,GAAGQ,KAAK;QAChC,MAAMO,SAAS,GAAGd,MAAM,GAAGQ,KAAK;QAEhC;QACA,IAAIK,SAAS,IAAI,CAAC,EAAE;UAClB;UACAZ,OAAO,GAAGM,KAAK;UACfL,OAAO,GAAGf,IAAI,CAACC,GAAG,CAACoB,KAAK,EAAErB,IAAI,CAAC4B,GAAG,CAACP,KAAK,GAAGG,UAAU,EAAEX,MAAM,CAAC,CAAC;QACjE,CAAC,MAAM,IAAIa,SAAS,IAAIJ,SAAS,EAAE;UACjC;UACAR,OAAO,GAAGM,KAAK,GAAGE,SAAS;UAC3BP,OAAO,GAAGf,IAAI,CAACC,GAAG,CAACoB,KAAK,EAAErB,IAAI,CAAC4B,GAAG,CAACP,KAAK,GAAGG,UAAU,EAAEX,MAAM,CAAC,CAAC;QACjE,CAAC,MAAM,IAAIc,SAAS,IAAI,CAAC,EAAE;UACzB;UACAb,OAAO,GAAGd,IAAI,CAACC,GAAG,CAACmB,KAAK,EAAEpB,IAAI,CAAC4B,GAAG,CAACR,KAAK,GAAGE,SAAS,EAAEV,MAAM,CAAC,CAAC;UAC9DG,OAAO,GAAGM,KAAK;QACjB,CAAC,MAAM,IAAIM,SAAS,IAAIH,UAAU,EAAE;UAClC;UACAV,OAAO,GAAGd,IAAI,CAACC,GAAG,CAACmB,KAAK,EAAEpB,IAAI,CAAC4B,GAAG,CAACR,KAAK,GAAGE,SAAS,EAAEV,MAAM,CAAC,CAAC;UAC9DG,OAAO,GAAGM,KAAK,GAAGG,UAAU;QAC9B;MACF;IACF;IAEA,IAAI,CAACvG,cAAc,GAAG;MACpBC,QAAQ,EAAE,IAAI;MACd2G,YAAY,EAAEvB,IAAI,CAACH,MAAM;MACzB2B,gBAAgB,EAAExB,IAAI,CAACyB,UAAU;MACjCC,OAAO,EAAElB,OAAO;MAChBmB,OAAO,EAAElB,OAAO;MAChBmB,OAAO,EAAEtB,MAAM;MACfuB,OAAO,EAAEtB;KACV;IAED,IAAI,CAAC1G,iBAAiB,CAACqC,IAAI,CAAC8D,IAAI,CAAC;EACnC;EAEA8B,YAAYA,CAACjC,MAAc;IACzB;IACA,IAAI,CAACX,uBAAuB,EAAE;IAE9B;IACA,IAAI,CAAC3I,KAAK,GAAG,IAAI,CAACA,KAAK,CAACwL,MAAM,CAAExF,IAAI,IAAKA,IAAI,CAACC,EAAE,KAAKqD,MAAM,CAAC;IAE5D;IACA,IAAI,CAACrJ,KAAK,GAAG,IAAI,CAACA,KAAK,CAACuL,MAAM,CAC3BC,IAAI,IAAKA,IAAI,CAAC9E,MAAM,KAAK2C,MAAM,IAAImC,IAAI,CAAC7E,MAAM,KAAK0C,MAAM,CAC3D;IAED,IAAI,CAACpG,WAAW,CAACyC,IAAI,CAAC2D,MAAM,CAAC;IAC7B,IAAI,IAAI,CAACnF,cAAc,KAAKmF,MAAM,EAAE;MAClC,IAAI,CAACnF,cAAc,GAAG,IAAI;IAC5B;IAEA,IAAI,CAAC0C,0BAA0B,EAAE;EACnC;EAEA6E,iBAAiBA,CAAC5D,KAAiB;IACjC;IACA,IAAI,IAAI,CAACvD,QAAQ,CAACI,UAAU,IAAI,IAAI,CAACxE,SAAS,EAAE;MAC9C,MAAMwL,MAAM,GAAG7D,KAAK,CAACgB,OAAO,GAAG,IAAI,CAACvE,QAAQ,CAACK,UAAU;MACvD,MAAMgH,MAAM,GAAG9D,KAAK,CAACkB,OAAO,GAAG,IAAI,CAACzE,QAAQ,CAACM,UAAU;MAEvD,IAAI,CAACN,QAAQ,CAACE,CAAC,IAAIkH,MAAM;MACzB,IAAI,CAACpH,QAAQ,CAACG,CAAC,IAAIkH,MAAM;MAEzB,IAAI,CAACrH,QAAQ,CAACK,UAAU,GAAGkD,KAAK,CAACgB,OAAO;MACxC,IAAI,CAACvE,QAAQ,CAACM,UAAU,GAAGiD,KAAK,CAACkB,OAAO;MAExC,IAAI,CAACnJ,GAAG,CAACyI,aAAa,EAAE;MACxB;IACF;IAEA,IAAI,CAAC,IAAI,CAAClE,cAAc,CAACC,QAAQ,EAAE;IAEnC;IACA,MAAMyF,UAAU,GACd,IAAI,CAAChK,eAAe,CAAC6H,aAAa,CAACkB,qBAAqB,EAAE;IAC5D,MAAMwC,OAAO,GACX,CAACvD,KAAK,CAACgB,OAAO,GAAGgB,UAAU,CAACf,IAAI,GAAG,IAAI,CAACxE,QAAQ,CAACE,CAAC,IAAI,IAAI,CAACF,QAAQ,CAACC,IAAI;IAC1E,MAAM8G,OAAO,GACX,CAACxD,KAAK,CAACkB,OAAO,GAAGc,UAAU,CAACb,GAAG,GAAG,IAAI,CAAC1E,QAAQ,CAACG,CAAC,IAAI,IAAI,CAACH,QAAQ,CAACC,IAAI;IAEzE,IAAI,CAACJ,cAAc,GAAG;MACpB,GAAG,IAAI,CAACA,cAAc;MACtBiH,OAAO;MACPC;KACD;IAED,IAAI,CAACzL,GAAG,CAACyI,aAAa,EAAE;EAC1B;EAEAuD,aAAaA,CAAC/D,KAAiB;IAC7B,IAAI,CAAC,IAAI,CAAC1H,UAAU,IAAI,CAAC,IAAI,CAACoB,wBAAwB,EAAE;IACxDsG,KAAK,CAACO,cAAc,EAAE;IAEtB,MAAMyD,KAAK,GAAG,CAAChE,KAAK,CAAC8D,MAAM;IAC3B,MAAMG,SAAS,GAAG,KAAK;IACvB,MAAMC,OAAO,GAAG7C,IAAI,CAACC,GAAG,CACtB,IAAI,CAAC9I,OAAO,EACZ6I,IAAI,CAAC4B,GAAG,CAAC,IAAI,CAACxK,OAAO,EAAE,IAAI,CAACgE,QAAQ,CAACC,IAAI,GAAGsH,KAAK,GAAGC,SAAS,CAAC,CAC/D;IAED,IAAIC,OAAO,KAAK,IAAI,CAACzH,QAAQ,CAACC,IAAI,EAAE;MAClC,MAAM8F,IAAI,GAAG,IAAI,CAACxK,eAAe,CAAC6H,aAAa,CAACkB,qBAAqB,EAAE;MACvE,MAAMkB,MAAM,GAAGjC,KAAK,CAACgB,OAAO,GAAGwB,IAAI,CAACvB,IAAI;MACxC,MAAMiB,MAAM,GAAGlC,KAAK,CAACkB,OAAO,GAAGsB,IAAI,CAACrB,GAAG;MAEvC,MAAMgD,SAAS,GAAGD,OAAO,GAAG,IAAI,CAACzH,QAAQ,CAACC,IAAI;MAC9C,MAAM0H,IAAI,GAAGnC,MAAM,GAAG,CAACA,MAAM,GAAG,IAAI,CAACxF,QAAQ,CAACE,CAAC,IAAIwH,SAAS;MAC5D,MAAME,IAAI,GAAGnC,MAAM,GAAG,CAACA,MAAM,GAAG,IAAI,CAACzF,QAAQ,CAACG,CAAC,IAAIuH,SAAS;MAE5D,IAAI,CAAC1H,QAAQ,CAACC,IAAI,GAAGwH,OAAO;MAC5B,IAAI,CAACzH,QAAQ,CAACE,CAAC,GAAGyH,IAAI;MACtB,IAAI,CAAC3H,QAAQ,CAACG,CAAC,GAAGyH,IAAI;MAEtB,IAAI,CAACtF,0BAA0B,EAAE;MACjC,IAAI,CAACpD,eAAe,CAACkC,IAAI,CAAC,IAAI,CAACpB,QAAQ,CAAC;MACxC,IAAI,CAAC1E,GAAG,CAACyI,aAAa,EAAE;IAC1B;EACF;EAEA8D,iBAAiBA,CAACtE,KAAiB;IACjC,IAAI,CAAC,IAAI,CAAC3H,SAAS,IAAI,CAAC,IAAI,CAACqB,wBAAwB,EAAE;IAEvD;IACA;IACA;IACA;IACA,IACEsG,KAAK,CAACuE,MAAM,KAAK,CAAC,IACjBvE,KAAK,CAACuE,MAAM,KAAK,CAAC,IAAIvE,KAAK,CAACwE,MAAO,IACnCxE,KAAK,CAACuE,MAAM,KAAK,CAAC,IAAI,IAAI,CAAClH,UAAU,KAAK,KAAM,EACjD;MACA,IAAI,CAACZ,QAAQ,CAACI,UAAU,GAAG,IAAI;MAC/B,IAAI,CAACJ,QAAQ,CAACK,UAAU,GAAGkD,KAAK,CAACgB,OAAO;MACxC,IAAI,CAACvE,QAAQ,CAACM,UAAU,GAAGiD,KAAK,CAACkB,OAAO;MAExC;MACA,IAAI,IAAI,CAAClJ,eAAe,EAAE;QACxB,IAAI,CAACA,eAAe,CAAC6H,aAAa,CAAC4E,KAAK,CAACC,MAAM,GAAG,UAAU;MAC9D;MAEA1E,KAAK,CAACO,cAAc,EAAE;IACxB;EACF;EAEAoE,eAAeA,CAAC3E,KAAiB;IAC/B,IAAI,CAACvD,QAAQ,CAACI,UAAU,GAAG,KAAK;IAEhC;IACA,IAAI,IAAI,CAAC7E,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAAC6H,aAAa,CAAC4E,KAAK,CAACC,MAAM,GAC7C,IAAI,CAACrH,UAAU,KAAK,KAAK,GAAG,MAAM,GAAG,SAAS;IAClD;IAEA,IAAI,CAAC,IAAI,CAACf,cAAc,CAACC,QAAQ,IAAI,CAAC,IAAI,CAAChE,iBAAiB,EAAE;IAE9D,MAAMuG,MAAM,GAAGkB,KAAK,CAAClB,MAAqB;IAE1C;IACA,MAAMwD,WAAW,GAAGxD,MAAM,CAACuB,OAAO,CAAC,gBAAgB,CAAC;IAEpD,IAAIiC,WAAW,IAAI,IAAI,CAAChG,cAAc,CAAC4G,YAAY,EAAE;MACnD,MAAMA,YAAY,GAAG,IAAI,CAAC5G,cAAc,CAAC4G,YAAY;MACrD,MAAM0B,YAAY,GAAGtC,WAAW,CAACuC,YAAY,CAAC,cAAc,CAAC;MAE7D;MACA,IAAID,YAAY,IAAI1B,YAAY,KAAK0B,YAAY,EAAE;QACjD;QACA,MAAME,kBAAkB,GAAG,IAAI,CAAC3M,KAAK,CAAC4J,IAAI,CACvC4B,IAAI,IACFA,IAAI,CAAC9E,MAAM,KAAKqE,YAAY,IAAIS,IAAI,CAAC7E,MAAM,KAAK8F,YAAY,IAC5DjB,IAAI,CAAC9E,MAAM,KAAK+F,YAAY,IAAIjB,IAAI,CAAC7E,MAAM,KAAKoE,YAAa,CACjE;QAED,IAAI,CAAC4B,kBAAkB,EAAE;UACvB;UACA,IAAI,CAACjE,uBAAuB,EAAE;UAE9B,MAAMkE,OAAO,GAAe;YAC1B5G,EAAE,EAAE,GAAG+E,YAAY,IAAI0B,YAAY,EAAE;YACrC/F,MAAM,EAAEqE,YAAY;YACpBpE,MAAM,EAAE8F,YAAY;YACpBI,QAAQ,EAAE,KAAK,CAAE;WAClB;UAED;UACA,IAAI,CAAC7M,KAAK,CAAC8M,IAAI,CAACF,OAAO,CAAC;UAExB,IAAI,CAACtJ,iBAAiB,CAACoC,IAAI,CAACkH,OAAO,CAAC;QACtC;MACF;IACF;IAEA,IAAI,CAACzI,cAAc,GAAG;MAAEC,QAAQ,EAAE;IAAK,CAAE;EAC3C;EAEAwC,0BAA0BA,CAAA;IACxB,IAAI,IAAI,CAAC/B,2BAA2B,EAAE;MACpC2C,oBAAoB,CAAC,IAAI,CAAC3C,2BAA2B,CAAC;IACxD;IAEA;IACA,IAAI,CAACA,2BAA2B,GAAGoC,qBAAqB,CAAC,MAAK;MAC5D,IAAI,CAAC5C,oBAAoB,GAAG,EAAE;MAE9B,KAAK,MAAM0B,IAAI,IAAI,IAAI,CAAChG,KAAK,EAAE;QAC7B,MAAMoK,WAAW,GAAG9C,QAAQ,CAAC+C,aAAa,CACxC,kBAAkBrE,IAAI,CAACC,EAAE,IAAI,CACf;QAEhB;QACA,IAAIsE,KAAa,EAAEC,KAAa,EAAEC,SAAiB,EAAEE,UAAkB;QAEvE,IAAIP,WAAW,EAAE;UACf;UACA,MAAME,IAAI,GAAGF,WAAW,CAACvB,qBAAqB,EAAE;UAChD,MAAMiB,UAAU,GACd,IAAI,CAAChK,eAAe,EAAE6H,aAAa,CAACkB,qBAAqB,EAAE;UAC7D,IAAI,CAACiB,UAAU,EAAE;UAEjB;UACA,IAAI,IAAI,CAACxI,aAAa,EAAE;YACtB;YACAiJ,KAAK,GAAGD,IAAI,CAACvB,IAAI,GAAGe,UAAU,CAACf,IAAI;YACnCyB,KAAK,GAAGF,IAAI,CAACrB,GAAG,GAAGa,UAAU,CAACb,GAAG;YACjCwB,SAAS,GAAGH,IAAI,CAACI,KAAK;YACtBC,UAAU,GAAGL,IAAI,CAACM,MAAM;UAC1B,CAAC,MAAM;YACL;YACAL,KAAK,GACH,CAACD,IAAI,CAACvB,IAAI,GAAGe,UAAU,CAACf,IAAI,GAAG,IAAI,CAACxE,QAAQ,CAACE,CAAC,IAC9C,IAAI,CAACF,QAAQ,CAACC,IAAI;YACpBgG,KAAK,GACH,CAACF,IAAI,CAACrB,GAAG,GAAGa,UAAU,CAACb,GAAG,GAAG,IAAI,CAAC1E,QAAQ,CAACG,CAAC,IAC5C,IAAI,CAACH,QAAQ,CAACC,IAAI;YACpBiG,SAAS,GAAGH,IAAI,CAACI,KAAK,GAAG,IAAI,CAACnG,QAAQ,CAACC,IAAI;YAC3CmG,UAAU,GAAGL,IAAI,CAACM,MAAM,GAAG,IAAI,CAACrG,QAAQ,CAACC,IAAI;UAC/C;QACF,CAAC,MAAM;UACL;UACA+F,KAAK,GAAGvE,IAAI,CAACS,QAAQ,CAAChC,CAAC;UACvB+F,KAAK,GAAGxE,IAAI,CAACS,QAAQ,CAAC/B,CAAC;UACvB;UACA+F,SAAS,GAAG,IAAI,CAACnJ,aAAa,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;UAC1CqJ,UAAU,GAAG,IAAI,CAACrJ,aAAa,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QAC7C;QAEA,IAAI,IAAI,CAACA,aAAa,EAAE;UACtB;UACA,MAAM0L,OAAO,GAAGzC,KAAK,GAAGE,SAAS,GAAG,CAAC;UACrC,MAAMwC,OAAO,GAAGzC,KAAK,GAAGG,UAAU,GAAG,CAAC;UAEtC;UACA,IAAI,CAACrG,oBAAoB,CAAC0B,IAAI,CAACC,EAAE,CAAC,GAAG;YACnCgD,GAAG,EAAE;cAAExE,CAAC,EAAEuI,OAAO;cAAEtI,CAAC,EAAEuI;YAAO,CAAE;YAC/BC,KAAK,EAAE;cAAEzI,CAAC,EAAEuI,OAAO;cAAEtI,CAAC,EAAEuI;YAAO,CAAE;YACjCE,MAAM,EAAE;cAAE1I,CAAC,EAAEuI,OAAO;cAAEtI,CAAC,EAAEuI;YAAO,CAAE;YAClClE,IAAI,EAAE;cAAEtE,CAAC,EAAEuI,OAAO;cAAEtI,CAAC,EAAEuI;YAAO;WAC/B;UAED5G,OAAO,CAACC,GAAG,CACT,qBAAqBN,IAAI,CAACC,EAAE,YAAY+G,OAAO,KAAKC,OAAO,GAAG,CAC/D;QACH,CAAC,MAAM;UACL;UACA;UACA,MAAMG,WAAW,GAAG7C,KAAK,GAAG,EAAE,CAAC,CAAC;UAChC,MAAM0C,OAAO,GAAGzC,KAAK,GAAGG,UAAU,GAAG,CAAC,CAAC,CAAC;UAExC;UACA,IAAI,CAACrG,oBAAoB,CAAC0B,IAAI,CAACC,EAAE,CAAC,GAAG;YACnCgD,GAAG,EAAE;cAAExE,CAAC,EAAE2I,WAAW;cAAE1I,CAAC,EAAE8F;YAAK,CAAE;YACjC0C,KAAK,EAAE;cAAEzI,CAAC,EAAE8F,KAAK,GAAGE,SAAS;cAAE/F,CAAC,EAAEuI;YAAO,CAAE;YAC3CE,MAAM,EAAE;cAAE1I,CAAC,EAAE2I,WAAW;cAAE1I,CAAC,EAAE8F,KAAK,GAAGG;YAAU,CAAE;YACjD5B,IAAI,EAAE;cAAEtE,CAAC,EAAE8F,KAAK;cAAE7F,CAAC,EAAEuI;YAAO;WAC7B;QACH;MACF;MAEA;MACA,IAAI,CAACpN,GAAG,CAACyI,aAAa,EAAE;MACxB,IAAI,CAACxD,2BAA2B,GAAG,IAAI;IACzC,CAAC,CAAC;EACJ;EAEA;EACA;EAEQuI,iBAAiBA,CACvBC,UAAsB,EACtBC,UAAsB;IAEtB;IACA,IAAIpC,OAAe,EAAEC,OAAe,EAAEC,OAAe,EAAEC,OAAe;IAEtE,IAAI,IAAI,CAAChK,aAAa,EAAE;MACtB;MACA6J,OAAO,GAAGmC,UAAU,CAAC7G,QAAQ,CAAChC,CAAC,GAAG,IAAI,CAAC,CAAC;MACxC2G,OAAO,GAAGkC,UAAU,CAAC7G,QAAQ,CAAC/B,CAAC,GAAG,IAAI;MACtC2G,OAAO,GAAGkC,UAAU,CAAC9G,QAAQ,CAAChC,CAAC,GAAG,IAAI;MACtC6G,OAAO,GAAGiC,UAAU,CAAC9G,QAAQ,CAAC/B,CAAC,GAAG,IAAI;IACxC,CAAC,MAAM;MACL;MACAyG,OAAO,GAAGmC,UAAU,CAAC7G,QAAQ,CAAChC,CAAC,GAAG,EAAE,CAAC,CAAC;MACtC2G,OAAO,GAAGkC,UAAU,CAAC7G,QAAQ,CAAC/B,CAAC,GAAG,EAAE,CAAC,CAAC;MACtC2G,OAAO,GAAGkC,UAAU,CAAC9G,QAAQ,CAAChC,CAAC,GAAG,EAAE;MACpC6G,OAAO,GAAGiC,UAAU,CAAC9G,QAAQ,CAAC/B,CAAC,GAAG,EAAE;IACtC;IAEA;IACA,OAAO,IAAI,CAAC8I,oBAAoB,CAC9B;MAAE/I,CAAC,EAAE0G,OAAO;MAAEzG,CAAC,EAAE0G;IAAO,CAAE,EAC1B;MAAE3G,CAAC,EAAE4G,OAAO;MAAE3G,CAAC,EAAE4G;IAAO,CAAE,CAC3B;EACH;EAEA;EACOxE,eAAeA,CAAA;IACpBT,OAAO,CAACC,GAAG,CACT,qCAAqC,EACrC,IAAI,CAACtG,KAAK,CAACuG,GAAG,CAAEC,CAAC,KAAM;MAAEP,EAAE,EAAEO,CAAC,CAACP,EAAE;MAAEQ,QAAQ,EAAED,CAAC,CAACC;IAAQ,CAAE,CAAC,CAAC,CAC5D;IACDJ,OAAO,CAACC,GAAG,CACT,qCAAqC,EACrC,IAAI,CAACrG,KAAK,CAACsG,GAAG,CAAEG,CAAC,KAAM;MAAET,EAAE,EAAES,CAAC,CAACT,EAAE;MAAEU,MAAM,EAAED,CAAC,CAACC,MAAM;MAAEC,MAAM,EAAEF,CAAC,CAACE;IAAM,CAAE,CAAC,CAAC,CAC1E;IAED,IAAI,CAAC3G,KAAK,GAAG,IAAI,CAACA,KAAK,CAACsG,GAAG,CAAEkF,IAAI,IAAI;MACnC,MAAM6B,UAAU,GAAG,IAAI,CAACtN,KAAK,CAAC6J,IAAI,CAAErD,CAAC,IAAKA,CAAC,CAACP,EAAE,KAAKwF,IAAI,CAAC9E,MAAM,CAAC;MAC/D,MAAM4G,UAAU,GAAG,IAAI,CAACvN,KAAK,CAAC6J,IAAI,CAAErD,CAAC,IAAKA,CAAC,CAACP,EAAE,KAAKwF,IAAI,CAAC7E,MAAM,CAAC;MAE/D,IAAI0G,UAAU,IAAIC,UAAU,EAAE;QAC5B,MAAME,QAAQ,GAAG,IAAI,CAACJ,iBAAiB,CAACC,UAAU,EAAEC,UAAU,CAAC;QAC/DlH,OAAO,CAACC,GAAG,CAAC,cAAcmF,IAAI,CAACxF,EAAE,OAAOwH,QAAQ,EAAE,CAAC;QACnDpH,OAAO,CAACC,GAAG,CACT,cAAcgH,UAAU,CAACrH,EAAE,QAAQqH,UAAU,CAAC7G,QAAQ,CAAChC,CAAC,KAAK6I,UAAU,CAAC7G,QAAQ,CAAC/B,CAAC,GAAG,CACtF;QACD2B,OAAO,CAACC,GAAG,CACT,cAAciH,UAAU,CAACtH,EAAE,QAAQsH,UAAU,CAAC9G,QAAQ,CAAChC,CAAC,KAAK8I,UAAU,CAAC9G,QAAQ,CAAC/B,CAAC,GAAG,CACtF;QACD,OAAO;UACL,GAAG+G,IAAI;UACPgC,QAAQ,EAAEA;SACJ;MACV,CAAC,MAAM;QACL;QACApH,OAAO,CAACC,GAAG,CACT,sCAAsCmF,IAAI,CAACxF,EAAE,oBAAoB,CAClE;QACD,OAAO;UACL,GAAGwF,IAAI;UACPgC,QAAQ,EAAE;SACJ;MACV;IACF,CAAC,CAAC;IAEFpH,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;IACzE,IAAI,CAACzG,GAAG,CAACyI,aAAa,EAAE;EAC1B;EAEQoF,2BAA2BA,CACjCC,YAAiB,EACjBC,YAAiB;IAKjB,IAAI,IAAI,CAACtM,aAAa,EAAE;MACtB;MACA;MACA,OAAO;QACLuM,WAAW,EAAEF,YAAY,CAAC1E,GAAG;QAAE;QAC/B6E,WAAW,EAAEF,YAAY,CAAC3E,GAAG,CAAE;OAChC;IACH,CAAC,MAAM;MACL;MACA;MACA,MAAM8E,kBAAkB,GAAG,CACzB;QAAEC,KAAK,EAAEL,YAAY,CAACT,KAAK;QAAEe,IAAI,EAAE;MAAO,CAAE,EAC5C;QAAED,KAAK,EAAEL,YAAY,CAACR,MAAM;QAAEc,IAAI,EAAE;MAAQ,CAAE,EAC9C;QAAED,KAAK,EAAEL,YAAY,CAAC5E,IAAI;QAAEkF,IAAI,EAAE;MAAM,CAAE,EAC1C;QAAED,KAAK,EAAEL,YAAY,CAAC1E,GAAG;QAAEgF,IAAI,EAAE;MAAK,CAAE,CACzC;MAED,MAAMC,kBAAkB,GAAG,CACzB;QAAEF,KAAK,EAAEJ,YAAY,CAAC7E,IAAI;QAAEkF,IAAI,EAAE;MAAM,CAAE,EAC1C;QAAED,KAAK,EAAEJ,YAAY,CAAC3E,GAAG;QAAEgF,IAAI,EAAE;MAAK,CAAE,EACxC;QAAED,KAAK,EAAEJ,YAAY,CAACV,KAAK;QAAEe,IAAI,EAAE;MAAO,CAAE,EAC5C;QAAED,KAAK,EAAEJ,YAAY,CAACT,MAAM;QAAEc,IAAI,EAAE;MAAQ,CAAE,CAC/C;MAED,IAAIE,YAAY,GAAGC,QAAQ;MAC3B,IAAIC,eAAe,GAAGV,YAAY,CAACT,KAAK;MACxC,IAAIoB,eAAe,GAAGV,YAAY,CAAC7E,IAAI;MAEvC;MACA,KAAK,MAAMwF,YAAY,IAAIR,kBAAkB,EAAE;QAC7C,KAAK,MAAMS,YAAY,IAAIN,kBAAkB,EAAE;UAC7C,MAAMO,QAAQ,GAAGtF,IAAI,CAACuF,IAAI,CACxBvF,IAAI,CAACwF,GAAG,CAACH,YAAY,CAACR,KAAK,CAACvJ,CAAC,GAAG8J,YAAY,CAACP,KAAK,CAACvJ,CAAC,EAAE,CAAC,CAAC,GACtD0E,IAAI,CAACwF,GAAG,CAACH,YAAY,CAACR,KAAK,CAACtJ,CAAC,GAAG6J,YAAY,CAACP,KAAK,CAACtJ,CAAC,EAAE,CAAC,CAAC,CAC3D;UAED,IAAI+J,QAAQ,GAAGN,YAAY,EAAE;YAC3BA,YAAY,GAAGM,QAAQ;YACvBJ,eAAe,GAAGE,YAAY,CAACP,KAAK;YACpCM,eAAe,GAAGE,YAAY,CAACR,KAAK;UACtC;QACF;MACF;MAEA,OAAO;QACLH,WAAW,EAAEQ,eAAe;QAC5BP,WAAW,EAAEQ;OACd;IACH;EACF;EAEQM,sBAAsBA,CAC5Bf,WAAqC,EACrCC,WAAqC;IAErC,MAAMe,EAAE,GAAGf,WAAW,CAACrJ,CAAC,GAAGoJ,WAAW,CAACpJ,CAAC;IACxC,MAAMqK,EAAE,GAAGhB,WAAW,CAACpJ,CAAC,GAAGmJ,WAAW,CAACnJ,CAAC;IAExC,IAAIyE,IAAI,CAAC4F,GAAG,CAACF,EAAE,CAAC,GAAG1F,IAAI,CAAC4F,GAAG,CAACD,EAAE,CAAC,EAAE;MAC/B,OAAOD,EAAE,GAAG,CAAC,GAAG,OAAO,GAAG,MAAM;IAClC,CAAC,MAAM;MACL,OAAOC,EAAE,GAAG,CAAC,GAAG,QAAQ,GAAG,KAAK;IAClC;EACF;EAEQE,oBAAoBA,CAC1BlB,WAAqC,EACrCmB,SAAiB,EACjBC,MAAc;IAEd,QAAQD,SAAS;MACf,KAAK,OAAO;QACV,OAAO;UAAExK,CAAC,EAAEqJ,WAAW,CAACrJ,CAAC,GAAGyK,MAAM;UAAExK,CAAC,EAAEoJ,WAAW,CAACpJ;QAAC,CAAE;MACxD,KAAK,MAAM;QACT,OAAO;UAAED,CAAC,EAAEqJ,WAAW,CAACrJ,CAAC,GAAGyK,MAAM;UAAExK,CAAC,EAAEoJ,WAAW,CAACpJ;QAAC,CAAE;MACxD,KAAK,QAAQ;QACX,OAAO;UAAED,CAAC,EAAEqJ,WAAW,CAACrJ,CAAC;UAAEC,CAAC,EAAEoJ,WAAW,CAACpJ,CAAC,GAAGwK;QAAM,CAAE;MACxD,KAAK,KAAK;QACR,OAAO;UAAEzK,CAAC,EAAEqJ,WAAW,CAACrJ,CAAC;UAAEC,CAAC,EAAEoJ,WAAW,CAACpJ,CAAC,GAAGwK;QAAM,CAAE;MACxD;QACE,OAAOpB,WAAW;IACtB;EACF;EAEQN,oBAAoBA,CAC1BK,WAAqC,EACrCC,WAAqC;IAErC;IACA,MAAM3C,OAAO,GAAG0C,WAAW,CAACpJ,CAAC;IAC7B,MAAM2G,OAAO,GAAGyC,WAAW,CAACnJ,CAAC;IAC7B,MAAM2G,OAAO,GAAGyC,WAAW,CAACrJ,CAAC;IAC7B,MAAM6G,OAAO,GAAGwC,WAAW,CAACpJ,CAAC;IAE7B,IAAI,IAAI,CAACpD,aAAa,EAAE;MACtB;MACA,MAAMuN,EAAE,GAAGxD,OAAO,GAAGF,OAAO;MAC5B,MAAM2D,EAAE,GAAGxD,OAAO,GAAGF,OAAO;MAC5B,MAAM+D,MAAM,GAAGhG,IAAI,CAACuF,IAAI,CAACG,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;MAE3C,IAAIK,MAAM,GAAG,EAAE,EAAE;QACf,OAAO,KAAKhE,OAAO,IAAIC,OAAO,MAAMC,OAAO,IAAIC,OAAO,EAAE;MAC1D;MAEA,MAAM8D,KAAK,GAAGP,EAAE,GAAGM,MAAM;MACzB,MAAME,KAAK,GAAGP,EAAE,GAAGK,MAAM;MACzB,MAAMG,YAAY,GAAG,EAAE;MACvB,MAAMC,YAAY,GAAG,EAAE;MACvB,MAAMC,UAAU,GAAG,CAAC;MAEpB,MAAMC,eAAe,GAAGtE,OAAO,GAAGiE,KAAK,GAAGE,YAAY;MACtD,MAAMI,eAAe,GAAGtE,OAAO,GAAGiE,KAAK,GAAGC,YAAY;MACtD,MAAMK,eAAe,GAAGtE,OAAO,GAAG+D,KAAK,IAAIG,YAAY,GAAGC,UAAU,CAAC;MACrE,MAAMI,eAAe,GAAGtE,OAAO,GAAG+D,KAAK,IAAIE,YAAY,GAAGC,UAAU,CAAC;MAErE,OAAO,KAAKC,eAAe,IAAIC,eAAe,MAAMC,eAAe,IAAIC,eAAe,EAAE;IAC1F,CAAC,MAAM;MACL;MACA,MAAMf,EAAE,GAAGxD,OAAO,GAAGF,OAAO;MAC5B,MAAM2D,EAAE,GAAGxD,OAAO,GAAGF,OAAO;MAC5B,MAAMqD,QAAQ,GAAGtF,IAAI,CAACuF,IAAI,CAACG,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;MAE7C,IAAIL,QAAQ,GAAG,EAAE,EAAE;QACjB;QACA,OAAO,KAAKtD,OAAO,IAAIC,OAAO,MAAMC,OAAO,IAAIC,OAAO,EAAE;MAC1D;MAEA;MACA,MAAM8D,KAAK,GAAGP,EAAE,GAAGJ,QAAQ;MAC3B,MAAMY,KAAK,GAAGP,EAAE,GAAGL,QAAQ;MAE3B;MACA,MAAMa,YAAY,GAAG,EAAE,CAAC,CAAC;MACzB,MAAMG,eAAe,GAAGtE,OAAO,GAAGiE,KAAK,GAAGE,YAAY;MACtD,MAAMI,eAAe,GAAGtE,OAAO,GAAGiE,KAAK,GAAGC,YAAY;MAEtD;MACA,MAAMO,gBAAgB,GAAG,EAAE,CAAC,CAAC;MAC7B,MAAMC,WAAW,GAAG,EAAE,CAAC,CAAC;MACxB,MAAMH,eAAe,GACnBtE,OAAO,GAAG+D,KAAK,IAAIS,gBAAgB,GAAGC,WAAW,CAAC;MACpD,MAAMF,eAAe,GACnBtE,OAAO,GAAG+D,KAAK,IAAIQ,gBAAgB,GAAGC,WAAW,CAAC;MAEpD;MACA,OAAO,KAAKL,eAAe,IAAIC,eAAe,MAAMC,eAAe,IAAIC,eAAe,EAAE;IAC1F;EACF;EAEAG,qBAAqBA,CAAA;IACnB,IACE,CAAC,IAAI,CAAC3L,cAAc,CAACC,QAAQ,IAC7B,IAAI,CAACD,cAAc,CAAC+G,OAAO,KAAKzF,SAAS,IACzC,IAAI,CAACtB,cAAc,CAACgH,OAAO,KAAK1F,SAAS,IACzC,IAAI,CAACtB,cAAc,CAACiH,OAAO,KAAK3F,SAAS,IACzC,IAAI,CAACtB,cAAc,CAACkH,OAAO,KAAK5F,SAAS,EACzC;MACA,OAAO,EAAE;IACX;IAEA,MAAMyF,OAAO,GAAG,IAAI,CAAC/G,cAAc,CAAC+G,OAAO;IAC3C,MAAMC,OAAO,GAAG,IAAI,CAAChH,cAAc,CAACgH,OAAO;IAC3C,MAAMC,OAAO,GAAG,IAAI,CAACjH,cAAc,CAACiH,OAAO;IAC3C,MAAMC,OAAO,GAAG,IAAI,CAAClH,cAAc,CAACkH,OAAO;IAE3C;IACA,MAAMuD,EAAE,GAAGxD,OAAO,GAAGF,OAAO;IAC5B,MAAM2D,EAAE,GAAGxD,OAAO,GAAGF,OAAO;IAC5B,MAAMqD,QAAQ,GAAGtF,IAAI,CAACuF,IAAI,CAACG,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;IAE7C;IACA,MAAMkB,UAAU,GAAG7G,IAAI,CAAC4B,GAAG,CAAC,GAAG,EAAE0D,QAAQ,GAAG,GAAG,CAAC;IAChD,MAAMS,MAAM,GAAG/F,IAAI,CAACC,GAAG,CAAC,EAAE,EAAE4G,UAAU,CAAC;IAEvC;IACA,MAAMC,cAAc,GAAG9E,OAAO,GAAG+D,MAAM;IACvC,MAAMgB,cAAc,GAAG7E,OAAO,GAAG6D,MAAM;IAEvC,OAAO,KAAK/D,OAAO,IAAIC,OAAO,MAAM6E,cAAc,IAAI7E,OAAO,KAAK8E,cAAc,IAAI5E,OAAO,KAAKD,OAAO,IAAIC,OAAO,EAAE;EACtH;EAEA;EACA6E,MAAMA,CAAA;IACJ,IAAI,IAAI,CAACnL,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACA,YAAY,EAAE;MACnB,MAAMoL,KAAK,GAAG,IAAI,CAACrL,OAAO,CAAC,IAAI,CAACC,YAAY,CAAC;MAC7C,IAAI,CAACqL,YAAY,CAACD,KAAK,CAAC;IAC1B;IACA,IAAI,CAAC1M,UAAU,CAACiC,IAAI,EAAE;EACxB;EAEA2K,MAAMA,CAAA;IACJ,IAAI,IAAI,CAACtL,YAAY,GAAG,IAAI,CAACD,OAAO,CAACoK,MAAM,GAAG,CAAC,EAAE;MAC/C,IAAI,CAACnK,YAAY,EAAE;MACnB,MAAMoL,KAAK,GAAG,IAAI,CAACrL,OAAO,CAAC,IAAI,CAACC,YAAY,CAAC;MAC7C,IAAI,CAACqL,YAAY,CAACD,KAAK,CAAC;IAC1B;IACA,IAAI,CAACzM,UAAU,CAACgC,IAAI,EAAE;EACxB;EAEA4K,OAAOA,CAAA;IACL;IACA,IAAI,CAACpJ,aAAa,EAAE;IAEpB;IACA,IAAI,CAACnH,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACkE,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,cAAc,GAAG;MAAEC,QAAQ,EAAE;IAAK,CAAE;IAEzC;IACA,IAAI,CAAC+D,aAAa,EAAE;IAEpB;IACA,IAAI,CAACtE,YAAY,CAAC6B,IAAI,CAAC;MACrB3F,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE;KACR,CAAC;IAEF;IACA,IAAI,CAACiD,WAAW,CAACyC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAI,CAAC/B,WAAW,CAAC+B,IAAI,EAAE;EACzB;EAEA6K,oBAAoBA,CAAA;IAClB,IAAI,CAAC3M,oBAAoB,CAAC8B,IAAI,EAAE;EAClC;EAEA;EACQwB,aAAaA,CAAA;IACnB;IACA,IAAI,IAAI,CAACjC,gBAAgB,EAAE;IAE3B;IACA,IAAI,CAACH,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC0L,KAAK,CAAC,CAAC,EAAE,IAAI,CAACzL,YAAY,GAAG,CAAC,CAAC;IAE3D;IACA,MAAM0L,YAAY,GAAG;MACnB1Q,KAAK,EAAE2Q,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAAC,IAAI,CAAC7Q,KAAK,CAAC,CAAC;MAC7CC,KAAK,EAAE0Q,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAAC,IAAI,CAAC5Q,KAAK,CAAC;KAC7C;IAED,IAAI,CAAC8E,OAAO,CAACgI,IAAI,CAAC2D,YAAY,CAAC;IAC/B,IAAI,CAAC1L,YAAY,GAAG,IAAI,CAACD,OAAO,CAACoK,MAAM,GAAG,CAAC;IAE3C;IACA,IAAI,IAAI,CAACpK,OAAO,CAACoK,MAAM,GAAG,IAAI,CAAClK,cAAc,EAAE;MAC7C,IAAI,CAACF,OAAO,CAAC+L,KAAK,EAAE;MACpB,IAAI,CAAC9L,YAAY,EAAE;IACrB;EACF;EAEQqL,YAAYA,CAACD,KAGpB;IACC,IAAI,CAAClL,gBAAgB,GAAG,IAAI;IAE5B;IACA,IAAI,CAAClF,KAAK,GAAG,CAAC,GAAGoQ,KAAK,CAACpQ,KAAK,CAAC;IAC7B,IAAI,CAACC,KAAK,GAAG,CAAC,GAAGmQ,KAAK,CAACnQ,KAAK,CAAC;IAE7B;IACA,IAAI,CAACkE,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,cAAc,GAAG;MAAEC,QAAQ,EAAE;IAAK,CAAE;IAEzC;IACA,IAAI,CAACwC,0BAA0B,EAAE;IAEjC;IACA,IAAI,CAAC/C,YAAY,CAAC6B,IAAI,CAAC;MACrB3F,KAAK,EAAE,CAAC,GAAG,IAAI,CAACA,KAAK,CAAC;MACtBC,KAAK,EAAE,CAAC,GAAG,IAAI,CAACA,KAAK;KACtB,CAAC;IAEF;IACA8G,UAAU,CAAC,MAAK;MACd,IAAI,CAAC7B,gBAAgB,GAAG,KAAK;IAC/B,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACQyD,uBAAuBA,CAAA;IAC7B,IAAI,CAACxB,aAAa,EAAE;EACtB;EAEA;EACA4J,OAAOA,CAAC/K,IAAgB;IACtB,IAAI,CAAC2C,uBAAuB,EAAE;IAC9B,IAAI,CAAC3I,KAAK,CAAC+M,IAAI,CAAC/G,IAAI,CAAC;IACrB,IAAI,CAACa,0BAA0B,EAAE;IACjC,IAAI,CAAC5D,SAAS,CAAC0C,IAAI,CAACK,IAAI,CAAC;EAC3B;EAEAgL,OAAOA,CAACvF,IAAgB;IACtB,IAAI,CAAC9C,uBAAuB,EAAE;IAC9B,IAAI,CAAC1I,KAAK,CAAC8M,IAAI,CAACtB,IAAI,CAAC;EACvB;EAEAwF,UAAUA,CAAC3H,MAAc;IACvB,IAAI,CAACiC,YAAY,CAACjC,MAAM,CAAC;EAC3B;EAEA4H,UAAUA,CAACC,MAAc;IACvB,IAAI,CAACxI,uBAAuB,EAAE;IAC9B,IAAI,CAAC1I,KAAK,GAAG,IAAI,CAACA,KAAK,CAACuL,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACxF,EAAE,KAAKkL,MAAM,CAAC;EAC9D;EAEA;EACAC,UAAUA,CAAA;IACR,IAAI,CAAChM,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;IACtC,IAAI,CAAClF,QAAQ,GAAG,IAAI,CAACkF,YAAY;EACnC;EAEAiM,UAAUA,CAAA;IACR,IAAI,CAAClM,UAAU,GAAG,KAAK;IACvB,IAAI,CAAC3D,wBAAwB,GAAG,IAAI;IACpC,IAAI,IAAI,CAAC1B,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAAC6H,aAAa,CAAC4E,KAAK,CAACC,MAAM,GAAG,MAAM;IAC1D;EACF;EAEApF,aAAaA,CAAA;IACX,IAAI,CAACjC,UAAU,GAAG,QAAQ;IAC1B,IAAI,CAAC3D,wBAAwB,GAAG,IAAI;IACpC,IAAI,IAAI,CAAC1B,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAAC6H,aAAa,CAAC4E,KAAK,CAACC,MAAM,GAAG,SAAS;IAC7D;EACF;EAEA8E,WAAWA,CAAA;IACT,IAAI,CAACnM,UAAU,GAAG,MAAM;IACxB,IAAI,CAAC3D,wBAAwB,GAAG,IAAI;IACpC,IAAI,IAAI,CAAC1B,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAAC6H,aAAa,CAAC4E,KAAK,CAACC,MAAM,GAAG,SAAS;IAC7D;EACF;EAEA+E,wBAAwBA,CAAA;IACtB,IAAI,CAACpM,UAAU,GAAG,UAAU;IAC5B,IAAI,CAAC3D,wBAAwB,GAAG,KAAK;IACrC,IAAI,IAAI,CAAC1B,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAAC6H,aAAa,CAAC4E,KAAK,CAACC,MAAM,GAAG,aAAa;IACjE;EACF;EAEAgF,MAAMA,CAAA;IACJ,MAAMxF,OAAO,GAAG7C,IAAI,CAAC4B,GAAG,CAAC,IAAI,CAACxK,OAAO,EAAE,IAAI,CAACgE,QAAQ,CAACC,IAAI,GAAG,GAAG,CAAC;IAChE,IAAI,CAACiN,OAAO,CAACzF,OAAO,CAAC;EACvB;EAEA0F,OAAOA,CAAA;IACL,MAAM1F,OAAO,GAAG7C,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC9I,OAAO,EAAE,IAAI,CAACiE,QAAQ,CAACC,IAAI,GAAG,GAAG,CAAC;IAChE,IAAI,CAACiN,OAAO,CAACzF,OAAO,CAAC;EACvB;EAEQyF,OAAOA,CAACzF,OAAe;IAC7B,IAAIA,OAAO,KAAK,IAAI,CAACzH,QAAQ,CAACC,IAAI,EAAE;MAClC;MACA,MAAM8F,IAAI,GAAG,IAAI,CAACxK,eAAe,CAAC6H,aAAa,CAACkB,qBAAqB,EAAE;MACvE,MAAMmE,OAAO,GAAG1C,IAAI,CAACI,KAAK,GAAG,CAAC;MAC9B,MAAMuC,OAAO,GAAG3C,IAAI,CAACM,MAAM,GAAG,CAAC;MAE/B;MACA,MAAMqB,SAAS,GAAGD,OAAO,GAAG,IAAI,CAACzH,QAAQ,CAACC,IAAI;MAC9C,MAAM0H,IAAI,GAAGc,OAAO,GAAG,CAACA,OAAO,GAAG,IAAI,CAACzI,QAAQ,CAACE,CAAC,IAAIwH,SAAS;MAC9D,MAAME,IAAI,GAAGc,OAAO,GAAG,CAACA,OAAO,GAAG,IAAI,CAAC1I,QAAQ,CAACG,CAAC,IAAIuH,SAAS;MAE9D;MACA,IAAI,CAAC1H,QAAQ,CAACC,IAAI,GAAGwH,OAAO;MAC5B,IAAI,CAACzH,QAAQ,CAACE,CAAC,GAAGyH,IAAI;MACtB,IAAI,CAAC3H,QAAQ,CAACG,CAAC,GAAGyH,IAAI;MAEtB;MACA,IAAI,CAACtF,0BAA0B,EAAE;MAEjC;MACA,IAAI,CAACpD,eAAe,CAACkC,IAAI,CAAC,IAAI,CAACpB,QAAQ,CAAC;IAC1C;EACF;EAEA;EACAoN,iBAAiBA,CAAC5O,KAAa;IAC7B,IAAI,CAACV,gBAAgB,CAACuD,QAAQ,CAAC7C,KAAK,CAAC;IACrC,IAAI,CAACgB,gBAAgB,CAAC4B,IAAI,CAAC5C,KAAK,CAAC;EACnC;EAEA6O,iBAAiBA,CAAC7O,KAAwB;IACxC,MAAM8O,UAAU,GAAGC,KAAK,CAACC,OAAO,CAAChP,KAAK,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK;IAC1D,IAAI,CAACT,gBAAgB,CAACsD,QAAQ,CAACiM,UAAU,CAAC;IAC1C,IAAI,CAAC7N,gBAAgB,CAAC2B,IAAI,CAACkM,UAAU,CAAC;EACxC;EAEA;EACAG,sBAAsBA,CAAA;IACpB,IAAI,CAAC9P,sBAAsB,GAAG,CAAC,IAAI,CAACA,sBAAsB;EAC5D;EAEA+P,qBAAqBA,CAAA;IACnB,IAAI,CAAC/P,sBAAsB,GAAG,KAAK;EACrC;EAEAgQ,gBAAgBA,CACdC,aAAgC,EAChCC,KAAa,EACbC,WAAmB,EACnBC,QAAgB;IAEhB,MAAMvP,KAAK,GAAG+O,KAAK,CAACC,OAAO,CAACI,aAAa,CAAC,GACtCA,aAAa,CAAC,CAAC,CAAC,GAChBA,aAAa;IACjB,IAAI,CAACpP,KAAK,EAAE;IAEZ;IACA,QAAQsP,WAAW;MACjB,KAAK,KAAK;QACR,IAAI,CAAC3P,UAAU,CAACkD,QAAQ,CAAC7C,KAAK,CAAC;QAC/B;QACA,IAAI,CAACJ,aAAa,CAACiD,QAAQ,CAAC,EAAE,CAAC;QAC/B,IAAI,CAAChD,cAAc,CAACgD,QAAQ,CAAC,EAAE,CAAC;QAChC,IAAI,CAAC/C,WAAW,CAAC+C,QAAQ,CAAC,EAAE,CAAC;QAC7B;QACA,IAAI,CAAC2M,iBAAiB,CAACxP,KAAK,CAAC;QAC7B;MACF,KAAK,QAAQ;QACX,IAAI,CAACJ,aAAa,CAACiD,QAAQ,CAAC7C,KAAK,CAAC;QAClC;QACA,IAAI,CAACH,cAAc,CAACgD,QAAQ,CAAC,EAAE,CAAC;QAChC,IAAI,CAAC/C,WAAW,CAAC+C,QAAQ,CAAC,EAAE,CAAC;QAC7B;QACA,IAAI,CAAC4M,kBAAkB,CAAC,IAAI,CAAC9P,UAAU,CAACK,KAAK,IAAI,EAAE,EAAEA,KAAK,CAAC;QAC3D;MACF,KAAK,SAAS;QACZ,IAAI,CAACH,cAAc,CAACgD,QAAQ,CAAC7C,KAAK,CAAC;QACnC;QACA,IAAI,CAACF,WAAW,CAAC+C,QAAQ,CAAC,EAAE,CAAC;QAC7B;QACA,IAAI,CAAC6M,eAAe,CAClB,IAAI,CAAC/P,UAAU,CAACK,KAAK,IAAI,EAAE,EAC3B,IAAI,CAACJ,aAAa,CAACI,KAAK,IAAI,EAAE,EAC9BA,KAAK,CACN;QACD;MACF,KAAK,MAAM;QACT,IAAI,CAACF,WAAW,CAAC+C,QAAQ,CAAC7C,KAAK,CAAC;QAChC;IACJ;EACF;EAEA2P,aAAaA,CAAA;IACX,MAAMC,SAAS,GACb,IAAI,CAACjQ,UAAU,CAACK,KAAK,IACrB,IAAI,CAACJ,aAAa,CAACI,KAAK,IACxB,IAAI,CAACH,cAAc,CAACG,KAAK,IACzB,IAAI,CAACF,WAAW,CAACE,KAAK;IAExB,IAAI4P,SAAS,EAAE;MACb,IAAI,CAACxQ,cAAc,GACjB,IAAI,CAACT,iBAAiB,CAACkR,QAAQ,EAAEC,UAAU,EAAEC,KAAK,IAClD,4BAA4B;IAChC,CAAC,MAAM;MACL,IAAI,CAAC3Q,cAAc,GACjB,IAAI,CAACT,iBAAiB,CAACkR,QAAQ,EAAEC,UAAU,EAAEE,QAAQ,IACrD,gCAAgC;IACpC;IAEA;IACA,IAAI,CAAC9O,eAAe,CAAC0B,IAAI,CAAC;MACxB7D,GAAG,EAAE,IAAI,CAACY,UAAU,CAACK,KAAK,IAAI,EAAE;MAChChB,MAAM,EAAE,IAAI,CAACY,aAAa,CAACI,KAAK,IAAI,EAAE;MACtCf,OAAO,EAAE,IAAI,CAACY,cAAc,CAACG,KAAK,IAAI,EAAE;MACxCd,IAAI,EAAE,IAAI,CAACY,WAAW,CAACE,KAAK,IAAI;KACjC,CAAC;IAEF,IAAI,CAACkP,qBAAqB,EAAE;EAC9B;EAEAe,cAAcA,CAAA;IACZ,IAAI,CAACf,qBAAqB,EAAE;EAC9B;EAEA;EACQM,iBAAiBA,CAACzQ,GAAW;IACnC,MAAMmR,gBAAgB,GAAsC;MAC1DC,SAAS,EAAE,CACT;QAAEnQ,KAAK,EAAE,aAAa;QAAEC,KAAK,EAAE;MAAa,CAAE,EAC9C;QAAED,KAAK,EAAE,WAAW;QAAEC,KAAK,EAAE;MAAW,CAAE,EAC1C;QAAED,KAAK,EAAE,OAAO;QAAEC,KAAK,EAAE;MAAO,CAAE,CACnC;MACDmQ,QAAQ,EAAE,CACR;QAAEpQ,KAAK,EAAE,MAAM;QAAEC,KAAK,EAAE;MAAY,CAAE,EACtC;QAAED,KAAK,EAAE,YAAY;QAAEC,KAAK,EAAE;MAAY,CAAE,CAC7C;MACDoQ,QAAQ,EAAE,CACR;QAAErQ,KAAK,EAAE,UAAU;QAAEC,KAAK,EAAE;MAAU,CAAE,EACxC;QAAED,KAAK,EAAE,aAAa;QAAEC,KAAK,EAAE;MAAa,CAAE;KAEjD;IAED,IAAI,CAACF,cAAc,CAAC,QAAQ,CAAC,GAAGmQ,gBAAgB,CAACnR,GAAG,CAAC,IAAI,EAAE;EAC7D;EAEQ0Q,kBAAkBA,CAAC1Q,GAAW,EAAEC,MAAc;IACpD,MAAMsR,cAAc,GAAmB,CACrC;MAAEtQ,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAe,CAAE,EAC7C;MAAED,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAc,CAAE,EAC5C;MAAED,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAe,CAAE,CAC9C;IAED,IAAI,CAACF,cAAc,CAAC,SAAS,CAAC,GAAGuQ,cAAc;EACjD;EAEQZ,eAAeA,CAAC3Q,GAAW,EAAEC,MAAc,EAAEC,OAAe;IAClE,MAAMsR,WAAW,GAAmB,CAClC;MAAEvQ,KAAK,EAAE,OAAO;MAAEC,KAAK,EAAE;IAAY,CAAE,EACvC;MAAED,KAAK,EAAE,OAAO;MAAEC,KAAK,EAAE;IAAW,CAAE,EACtC;MAAED,KAAK,EAAE,OAAO;MAAEC,KAAK,EAAE;IAAY,CAAE,CACxC;IAED,IAAI,CAACF,cAAc,CAAC,MAAM,CAAC,GAAGwQ,WAAW;EAC3C;EAEA;EAEA;EAEA;EACAC,0BAA0BA,CAAA;IACxB,IAAI,CAACnR,0BAA0B,GAAG,CAAC,IAAI,CAACA,0BAA0B;EACpE;EAEAoR,yBAAyBA,CAAA;IACvB,IAAI,CAACpR,0BAA0B,GAAG,KAAK;EACzC;EAEAqR,iBAAiBA,CAAA;IACf,MAAMxF,IAAI,GAAG,IAAI,CAACzL,sBAAsB,CAACO,KAAK,IAAI,EAAE;IACpD,MAAM2Q,cAAc,GAAG,IAAI,CAACjR,kBAAkB,CAACM,KAAK,IAAI,EAAE;IAE1D;IACA,IAAI,CAACmB,mBAAmB,CAACyB,IAAI,CAAC;MAC5BsI,IAAI,EAAEA,IAAI;MACVyF,cAAc,EAAEA;KACjB,CAAC;IAEF,IAAI,CAACF,yBAAyB,EAAE;EAClC;EAEAG,kBAAkBA,CAAA;IAChB,IAAI,CAACH,yBAAyB,EAAE;EAClC;CACD;AA54C+BI,UAAA,EAA7BzU,SAAS,CAAC,iBAAiB,CAAC,C,4DAA8B;AAC7ByU,UAAA,EAA7BxU,YAAY,CAAC,cAAc,CAAC,C,yDAAiC;AAGrDwU,UAAA,EAAR5U,KAAK,EAAE,C,kDAA0B;AACzB4U,UAAA,EAAR5U,KAAK,EAAE,C,kDAA0B;AACzB4U,UAAA,EAAR5U,KAAK,EAAE,C,qDAA0B;AACzB4U,UAAA,EAAR5U,KAAK,EAAE,C,sDAA4B;AAC3B4U,UAAA,EAAR5U,KAAK,EAAE,C,uDAA6B;AAC5B4U,UAAA,EAAR5U,KAAK,EAAE,C,8DAAmC;AAClC4U,UAAA,EAAR5U,KAAK,EAAE,C,oDAAuB;AACtB4U,UAAA,EAAR5U,KAAK,EAAE,C,oDAAqB;AACpB4U,UAAA,EAAR5U,KAAK,EAAE,C,4DAAqC;AACpC4U,UAAA,EAAR5U,KAAK,EAAE,C,4DAA4D;AAC3D4U,UAAA,EAAR5U,KAAK,EAAE,C,4DAKN;AACO4U,UAAA,EAAR5U,KAAK,EAAE,C,wDAA6B;AAC5B4U,UAAA,EAAR5U,KAAK,EAAE,C,8DAAuC;AACtC4U,UAAA,EAAR5U,KAAK,EAAE,C,8DAAgC;AAC/B4U,UAAA,EAAR5U,KAAK,EAAE,C,uDAA4B;AAC3B4U,UAAA,EAAR5U,KAAK,EAAE,C,uDAA4B;AAC3B4U,UAAA,EAAR5U,KAAK,EAAE,C,wDAA6B;AAC5B4U,UAAA,EAAR5U,KAAK,EAAE,C,4DAAiC;AAChC4U,UAAA,EAAR5U,KAAK,EAAE,C,6DAAkC;AACjC4U,UAAA,EAAR5U,KAAK,EAAE,C,0DAA+B;AAC9B4U,UAAA,EAAR5U,KAAK,EAAE,C,gEAAqC;AACpC4U,UAAA,EAAR5U,KAAK,EAAE,C,+DAAoC;AACnC4U,UAAA,EAAR5U,KAAK,EAAE,C,0DAAgC;AAC/B4U,UAAA,EAAR5U,KAAK,EAAE,C,4DAAiC;AAChC4U,UAAA,EAAR5U,KAAK,EAAE,C,qEAA0C;AAGzC4U,UAAA,EAAR5U,KAAK,EAAE,C,6DAAmC;AAClC4U,UAAA,EAAR5U,KAAK,EAAE,C,8DA6BD;AAGE4U,UAAA,EAAR5U,KAAK,EAAE,C,6DAA+B;AAC9B4U,UAAA,EAAR5U,KAAK,EAAE,C,gEAAkC;AACjC4U,UAAA,EAAR5U,KAAK,EAAE,C,4DAUN;AAUO4U,UAAA,EAAR5U,KAAK,EAAE,C,6DAAwC;AAKvC4U,UAAA,EAAR5U,KAAK,EAAE,C,mEAA8C;AAC7C4U,UAAA,EAAR5U,KAAK,EAAE,C,+DAA0C;AAqBxC4U,UAAA,EAAT3U,MAAM,EAAE,C,sDAA4C;AAC3C2U,UAAA,EAAT3U,MAAM,EAAE,C,wDAA0C;AACzC2U,UAAA,EAAT3U,MAAM,EAAE,C,sDAGJ;AACK2U,UAAA,EAAT3U,MAAM,EAAE,C,yDAA2C;AAC1C2U,UAAA,EAAT3U,MAAM,EAAE,C,8DAAgD;AAE/C2U,UAAA,EAAT3U,MAAM,EAAE,C,8DAIJ;AACK2U,UAAA,EAAT3U,MAAM,EAAE,C,8DAAoD;AACnD2U,UAAA,EAAT3U,MAAM,EAAE,C,0DAGJ;AACK2U,UAAA,EAAT3U,MAAM,EAAE,C,4DAAsD;AACrD2U,UAAA,EAAT3U,MAAM,EAAE,C,uDAAuC;AACtC2U,UAAA,EAAT3U,MAAM,EAAE,C,uDAAuC;AACtC2U,UAAA,EAAT3U,MAAM,EAAE,C,wDAAwC;AACvC2U,UAAA,EAAT3U,MAAM,EAAE,C,iEAAiD;AAChD2U,UAAA,EAAT3U,MAAM,EAAE,C,yDAGJ;AACK2U,UAAA,EAAT3U,MAAM,EAAE,C,6DAA+C;AAC9C2U,UAAA,EAAT3U,MAAM,EAAE,C,6DAA+C;AAC9C2U,UAAA,EAAT3U,MAAM,EAAE,C,4DAKJ;AACK2U,UAAA,EAAT3U,MAAM,EAAE,C,gEAGJ;AAhKMW,oBAAoB,GAAAgU,UAAA,EAdhC7U,SAAS,CAAC;EACT8U,QAAQ,EAAE,kBAAkB;EAC5BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACP1U,YAAY,EACZE,mBAAmB,EACnBC,eAAe,EACfC,mBAAmB,EACnBC,oBAAoB,EACpBC,aAAa,CACd;EACDqU,WAAW,EAAE,+BAA+B;EAC5CC,SAAS,EAAE,CAAC,+BAA+B;CAC5C,CAAC,C,EACWrU,oBAAoB,CA+4ChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}