{"ast": null, "code": "import { defer } from './defer';\nexport function iif(condition, trueResult, falseResult) {\n  return defer(() => condition() ? trueResult : falseResult);\n}", "map": {"version": 3, "names": ["defer", "iif", "condition", "trueResult", "falseResult"], "sources": ["C:/console/aava-ui-web/node_modules/rxjs/dist/esm/internal/observable/iif.js"], "sourcesContent": ["import { defer } from './defer';\nexport function iif(condition, trueResult, falseResult) {\n    return defer(() => (condition() ? trueResult : falseResult));\n}\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,SAAS;AAC/B,OAAO,SAASC,GAAGA,CAACC,SAAS,EAAEC,UAAU,EAAEC,WAAW,EAAE;EACpD,OAAOJ,KAAK,CAAC,MAAOE,SAAS,CAAC,CAAC,GAAGC,UAAU,GAAGC,WAAY,CAAC;AAChE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}