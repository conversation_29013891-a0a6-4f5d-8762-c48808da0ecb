{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ButtonComponent } from '@ava/play-comp-library';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nconst _c0 = [\"editorContainer\"];\nconst _c1 = () => ({\n  background: \"linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)\",\n  \"--button-effect-color\": \"33, 90, 214\"\n});\nconst _c2 = () => ({\n  \"border\": \"2px solid transparent\",\n  \"background-image\": \"linear-gradient(#ffffff, #ffffff), linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)\",\n  \"background-origin\": \"border-box\",\n  \"background-clip\": \"padding-box, border-box\",\n  \"--button-effect-color\": \"33, 90, 214\"\n});\nfunction CodeEditorComponent_div_1_h3_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 15);\n    i0.ɵɵtext(1, \"*\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CodeEditorComponent_div_1_h3_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h3\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, CodeEditorComponent_div_1_h3_2_span_2_Template, 2, 0, \"span\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.title, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.titleRequired);\n  }\n}\nfunction CodeEditorComponent_div_1_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 16)(1, \"ava-button\", 17);\n    i0.ɵɵlistener(\"userClick\", function CodeEditorComponent_div_1_span_3_Template_ava_button_userClick_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onPrimaryButtonClick());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"customStyles\", i0.ɵɵpureFunction0(2, _c1))(\"disabled\", ctx_r0.state.loading || ctx_r0.isPrimaryButtonDisabled);\n  }\n}\nfunction CodeEditorComponent_div_1_div_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 20)(2, \"ava-button\", 21);\n    i0.ɵɵlistener(\"userClick\", function CodeEditorComponent_div_1_div_4_ng_container_1_Template_ava_button_userClick_2_listener() {\n      const i_r4 = i0.ɵɵrestoreView(_r3).index;\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.onActionButtonClick(i_r4));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const btn_r5 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", btn_r5.customClass);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", btn_r5.label)(\"iconName\", btn_r5.icon || \"\")(\"customStyles\", i0.ɵɵpureFunction0(6, _c2))(\"pill\", true)(\"disabled\", ctx_r0.state.loading);\n  }\n}\nfunction CodeEditorComponent_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtemplate(1, CodeEditorComponent_div_1_div_4_ng_container_1_Template, 3, 7, \"ng-container\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.actionButtons);\n  }\n}\nfunction CodeEditorComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9);\n    i0.ɵɵtemplate(2, CodeEditorComponent_div_1_h3_2_Template, 3, 2, \"h3\", 10)(3, CodeEditorComponent_div_1_span_3_Template, 2, 3, \"span\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, CodeEditorComponent_div_1_div_4_Template, 2, 1, \"div\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.title);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.primaryButtonSelected.observed);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.actionButtons.length);\n  }\n}\nfunction CodeEditorComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵelement(1, \"div\", 23);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading Code Editor...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CodeEditorComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25);\n    i0.ɵɵtext(2, \"\\u26A0\\uFE0F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4, \"Failed to Load Editor\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"ava-button\", 26);\n    i0.ɵɵlistener(\"userClick\", function CodeEditorComponent_div_3_Template_ava_button_userClick_7_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.initializeEditor());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r0.state.errorMessage);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pill\", true)(\"customStyles\", i0.ɵɵpureFunction0(3, _c2));\n  }\n}\nfunction CodeEditorComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵelement(1, \"div\", 28);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CodeEditorComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"p\", 30)(2, \"strong\");\n    i0.ɵɵtext(3, \"Note:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.footerText, \" \");\n  }\n}\n/**\n * Monaco Code Editor Component\n *\n * @Input title - Editor title displayed in header\n * @Input value - Initial code value\n * @Input language - Programming language (python, javascript, etc.)\n * @Input theme - Editor theme (light/dark)\n * @Input height - Editor height (default: 400px)\n * @Input readonly - Make editor read-only\n * @Input Control - Angular FormControl for form integration\n * @Input actionButtons - Array of custom action buttons\n * @Input footerText - Footer note text\n *\n * @Output valueChange - Emits when code changes\n * @Output primaryButtonSelected - Emits when Run button clicked\n * @Output actionButtonClicked - Emits when action button clicked\n * @Output editorReady - Emits when editor is initialized\n *\n * Usage:\n * <app-code-editor\n *   title=\"Code Editor\"\n *   language=\"python\"\n *   [Control]=\"formControl\"\n *   (primaryButtonSelected)=\"runCode()\">\n * </app-code-editor>\n */\nexport let CodeEditorComponent = /*#__PURE__*/(() => {\n  class CodeEditorComponent {\n    cdr;\n    editorContainer;\n    title = '';\n    value = '';\n    language = 'python';\n    theme = 'light';\n    height = '400px';\n    readonly = false;\n    customCssClass = '';\n    placeholder = '';\n    Control = null;\n    actionButtons = [];\n    footerText = '';\n    isPrimaryButtonDisabled = false;\n    valueChange = new EventEmitter();\n    primaryButtonSelected = new EventEmitter();\n    actionButtonClicked = new EventEmitter();\n    editorReady = new EventEmitter();\n    titleRequired = false;\n    state = {\n      loading: true,\n      error: false,\n      errorMessage: null,\n      processing: false\n    };\n    editor = null;\n    valueChangeTimeout = null;\n    monacoModule;\n    constructor(cdr) {\n      this.cdr = cdr;\n    }\n    ngOnChanges(changes) {\n      if (this.editor) {\n        if (changes['value'] && changes['value'].currentValue !== this.editor.getValue()) {\n          this.editor.setValue(changes['value'].currentValue || '');\n        }\n        if (changes['language'] && changes['language'].currentValue) {\n          this.monacoModule.editor.setModelLanguage(this.editor.getModel(), changes['language'].currentValue);\n        }\n        if (changes['theme'] && changes['theme'].currentValue) {\n          this.applyTheme();\n        }\n      }\n    }\n    ngOnInit() {\n      this.configureMonacoEnvironment();\n      if (this.Control?.value && !this.value) {\n        this.value = this.Control.value;\n      }\n    }\n    ngAfterViewInit() {\n      setTimeout(() => this.initializeEditor(), 100);\n    }\n    ngOnDestroy() {\n      this.editor?.dispose();\n    }\n    configureMonacoEnvironment() {\n      if (typeof window !== 'undefined' && !window.MonacoEnvironment) {\n        window.MonacoEnvironment = {\n          getWorkerUrl: () => './editor.worker.js'\n        };\n      }\n    }\n    initializeEditor() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        try {\n          if (_this.editor) return;\n          _this.state.loading = true;\n          _this.state.error = false;\n          _this.state.errorMessage = null;\n          _this.cdr.markForCheck();\n          // Temporarily disabled Monaco Editor for Docker build\n          const monacoModule = yield import('monaco-editor');\n          _this.monacoModule = monacoModule;\n          if (!_this.editorContainer?.nativeElement) {\n            throw new Error('Editor container not found');\n          }\n          _this.editorContainer.nativeElement.style.height = _this.height;\n          // Temporarily disabled Monaco Editor for Docker build\n          _this.defineCustomThemes(monacoModule);\n          // Temporarily disabled Monaco Editor initialization for Docker build\n          _this.editor = monacoModule.editor.create(_this.editorContainer.nativeElement, {\n            value: _this.value,\n            language: _this.language,\n            theme: _this.theme === 'dark' ? 'code-editor-dark' : 'code-editor-light',\n            automaticLayout: true,\n            minimap: {\n              enabled: false\n            },\n            scrollBeyondLastLine: false,\n            readOnly: _this.readonly,\n            placeholder: _this.placeholder,\n            fontSize: 14,\n            fontFamily: \"'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', 'Consolas', 'Courier New', monospace\",\n            tabSize: 4,\n            lineHeight: 22,\n            wordWrap: 'on',\n            lineNumbers: 'on',\n            folding: true,\n            autoIndent: 'full',\n            formatOnPaste: true,\n            formatOnType: true,\n            autoClosingBrackets: 'always',\n            autoClosingQuotes: 'always',\n            bracketPairColorization: {\n              enabled: true\n            },\n            guides: {\n              bracketPairs: true,\n              indentation: true\n            }\n          });\n          _this.setupEventListeners();\n          _this.state.loading = false;\n          _this.editorReady.emit(_this.editor);\n          _this.cdr.markForCheck();\n        } catch (error) {\n          _this.state.loading = false;\n          _this.state.error = true;\n          _this.state.errorMessage = `Failed to initialize code editor: ${error}`;\n          _this.cdr.markForCheck();\n        }\n      })();\n    }\n    defineCustomThemes(monaco) {\n      monaco.editor.defineTheme('code-editor-light', {\n        base: 'vs',\n        inherit: true,\n        rules: [{\n          token: 'comment',\n          foreground: '6c757d',\n          fontStyle: 'italic'\n        }, {\n          token: 'keyword',\n          foreground: '0066cc'\n        }, {\n          token: 'string',\n          foreground: '28a745'\n        }, {\n          token: 'number',\n          foreground: 'e83e8c'\n        }, {\n          token: 'function',\n          foreground: 'fd7e14'\n        }],\n        colors: {\n          'editor.background': '#ffffff',\n          'editor.foreground': '#2c3e50',\n          'editorLineNumber.foreground': '#6c757d',\n          'editor.selectionBackground': '#007bff26',\n          'editor.lineHighlightBackground': '#f8f9fa',\n          'editorCursor.foreground': '#007bff'\n        }\n      });\n      monaco.editor.defineTheme('code-editor-dark', {\n        base: 'vs-dark',\n        inherit: true,\n        rules: [{\n          token: 'comment',\n          foreground: '6c757d',\n          fontStyle: 'italic'\n        }, {\n          token: 'keyword',\n          foreground: '66d9ef'\n        }, {\n          token: 'string',\n          foreground: 'a6e22e'\n        }, {\n          token: 'number',\n          foreground: 'fd971f'\n        }, {\n          token: 'function',\n          foreground: 'f92672'\n        }],\n        colors: {\n          'editor.background': '#1e1e1e',\n          'editor.foreground': '#e9ecef',\n          'editorLineNumber.foreground': '#6c757d',\n          'editor.selectionBackground': '#66d9ef26',\n          'editor.lineHighlightBackground': '#2c2c2c',\n          'editorCursor.foreground': '#66d9ef'\n        }\n      });\n    }\n    applyTheme() {\n      if (this.editor) {\n        this.editor.updateOptions({\n          theme: this.theme === 'dark' ? 'code-editor-dark' : 'code-editor-light'\n        });\n      }\n    }\n    setupEventListeners() {\n      if (!this.editor) return;\n      this.editor.onDidChangeModelContent(() => {\n        const currentValue = this.editor.getValue();\n        if (this.valueChangeTimeout) clearTimeout(this.valueChangeTimeout);\n        this.valueChangeTimeout = setTimeout(() => {\n          this.valueChange.emit(currentValue);\n          if (this.Control) {\n            this.Control.setValue(currentValue);\n          }\n        }, 200);\n      });\n    }\n    selectAll() {\n      if (this.editor) {\n        this.editor.setSelection(this.editor.getModel().getFullModelRange());\n        this.editor.focus();\n      }\n    }\n    clear() {\n      if (this.editor) {\n        this.editor.setValue('');\n        this.editor.focus();\n      }\n    }\n    showProcessingLoader() {\n      this.state.processing = true;\n      this.cdr.markForCheck();\n    }\n    hideProcessingLoader() {\n      this.state.processing = false;\n      this.cdr.markForCheck();\n    }\n    onPrimaryButtonClick() {\n      this.showProcessingLoader();\n      this.primaryButtonSelected.emit();\n    }\n    getValue() {\n      return this.editor?.getValue() ?? '';\n    }\n    setValue(newValue) {\n      this.editor?.setValue(newValue);\n    }\n    setTheme(newTheme) {\n      this.theme = newTheme;\n      this.applyTheme();\n    }\n    focus() {\n      this.editor?.focus();\n    }\n    get isReady() {\n      return !!this.editor && !this.state.loading;\n    }\n    onActionButtonClick(idx) {\n      this.actionButtonClicked.emit(idx);\n    }\n    static ɵfac = function CodeEditorComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CodeEditorComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CodeEditorComponent,\n      selectors: [[\"app-code-editor\"]],\n      viewQuery: function CodeEditorComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.editorContainer = _t.first);\n        }\n      },\n      inputs: {\n        title: \"title\",\n        value: \"value\",\n        language: \"language\",\n        theme: \"theme\",\n        height: \"height\",\n        readonly: \"readonly\",\n        customCssClass: \"customCssClass\",\n        placeholder: \"placeholder\",\n        Control: \"Control\",\n        actionButtons: \"actionButtons\",\n        footerText: \"footerText\",\n        isPrimaryButtonDisabled: \"isPrimaryButtonDisabled\",\n        titleRequired: \"titleRequired\"\n      },\n      outputs: {\n        valueChange: \"valueChange\",\n        primaryButtonSelected: \"primaryButtonSelected\",\n        actionButtonClicked: \"actionButtonClicked\",\n        editorReady: \"editorReady\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 8,\n      vars: 8,\n      consts: [[\"editorContainer\", \"\"], [1, \"code-editor-container\", 3, \"ngClass\"], [\"class\", \"editor-header\", 4, \"ngIf\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [1, \"monaco-editor-container\"], [\"class\", \"editor-loader-overlay\", 4, \"ngIf\"], [\"class\", \"editor-footer\", 4, \"ngIf\"], [1, \"editor-header\"], [1, \"title-action-row\"], [\"class\", \"editor-title\", 4, \"ngIf\"], [\"class\", \"run-btn-wrapper\", 4, \"ngIf\"], [\"class\", \"editor-actions\", 4, \"ngIf\"], [1, \"editor-title\"], [\"class\", \"required-asterisk\", 4, \"ngIf\"], [1, \"required-asterisk\"], [1, \"run-btn-wrapper\"], [\"label\", \"Test\", \"variant\", \"primary\", \"size\", \"small\", 1, \"run-black-btn\", 3, \"userClick\", \"customStyles\", \"disabled\"], [1, \"editor-actions\"], [4, \"ngFor\", \"ngForOf\"], [3, \"ngClass\"], [\"variant\", \"secondary\", \"size\", \"small\", 1, \"action-btn\", 3, \"userClick\", \"label\", \"iconName\", \"customStyles\", \"pill\", \"disabled\"], [1, \"loading-container\"], [1, \"loading-spinner\"], [1, \"error-container\"], [1, \"error-icon\"], [\"label\", \"Retry\", \"variant\", \"secondary\", \"size\", \"small\", 3, \"userClick\", \"pill\", \"customStyles\"], [1, \"editor-loader-overlay\"], [1, \"editor-loader-spinner\"], [1, \"editor-footer\"], [1, \"footer-note\"]],\n      template: function CodeEditorComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1);\n          i0.ɵɵtemplate(1, CodeEditorComponent_div_1_Template, 5, 3, \"div\", 2)(2, CodeEditorComponent_div_2_Template, 4, 0, \"div\", 3)(3, CodeEditorComponent_div_3_Template, 8, 4, \"div\", 4);\n          i0.ɵɵelement(4, \"div\", 5, 0);\n          i0.ɵɵtemplate(6, CodeEditorComponent_div_6_Template, 2, 0, \"div\", 6)(7, CodeEditorComponent_div_7_Template, 5, 1, \"div\", 7);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngClass\", ctx.customCssClass);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.title || ctx.primaryButtonSelected.observed);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.state.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.state.error);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"hidden\", ctx.state.loading || ctx.state.error);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.state.processing);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.footerText);\n        }\n      },\n      dependencies: [CommonModule, i1.NgClass, i1.NgForOf, i1.NgIf, ButtonComponent],\n      styles: [\".code-editor-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n  background: #ffffff;\\n  font-family: -apple-system, BlinkMacSystemFont, \\\"Segoe UI\\\", Roboto, sans-serif;\\n  position: relative;\\n}\\n\\n.editor-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n  padding: 16px 20px;\\n  background: #ffffff;\\n}\\n\\n.title-action-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.editor-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 18px;\\n  font-weight: 700;\\n  color: #000000;\\n}\\n\\n.editor-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.run-btn-wrapper[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n  .run-black-btn .btn {\\n  background: #000 !important;\\n  color: #fff !important;\\n  border: none !important;\\n  border-radius: 6px;\\n  font-size: 14px;\\n  height: 32px;\\n  padding: 6px 12px;\\n}\\n  .run-black-btn .btn:hover:not(:disabled) {\\n  background: #222 !important;\\n}\\n\\n.loading-container[_ngcontent-%COMP%], \\n.error-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 20px;\\n  text-align: center;\\n  min-height: 200px;\\n  background: #ffffff;\\n}\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  border: 2px solid #f3f3f3;\\n  border-top: 2px solid #0969da;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n  margin-bottom: 12px;\\n}\\n\\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #656d76;\\n  font-size: 14px;\\n}\\n\\n.error-container[_ngcontent-%COMP%] {\\n  color: #cf222e;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  margin-bottom: 8px;\\n}\\n.error-container[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 16px;\\n  color: #cf222e;\\n}\\n.error-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  font-size: 14px;\\n  color: #656d76;\\n}\\n\\n.monaco-editor-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-height: 300px;\\n  position: relative;\\n  background: #ffffff;\\n}\\n.monaco-editor-container.hidden[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n\\n.editor-footer[_ngcontent-%COMP%] {\\n  padding: 12px 20px;\\n  background: #ffffff;\\n}\\n.editor-footer[_ngcontent-%COMP%]   .footer-note[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 12px;\\n  color: #656d76;\\n  line-height: 1.4;\\n}\\n.editor-footer[_ngcontent-%COMP%]   .footer-note[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #24292f;\\n  font-weight: 600;\\n}\\n\\n.editor-loader-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: rgba(255, 255, 255, 0.5);\\n  -webkit-backdrop-filter: blur(4px);\\n          backdrop-filter: blur(4px);\\n  z-index: 10;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.editor-loader-overlay[_ngcontent-%COMP%]   .editor-loader-spinner[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border: 5px solid rgba(0, 0, 0, 0.1);\\n  border-top: 5px solid #222;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .editor-header[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n  }\\n  .title-action-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 8px;\\n  }\\n  .monaco-editor-container[_ngcontent-%COMP%] {\\n    min-height: 250px;\\n  }\\n  .editor-title[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n}\\n.required-asterisk[_ngcontent-%COMP%] {\\n  color: red;\\n  margin-left: 4px;\\n  font-size: 0.9rem;\\n  font-weight: normal;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n  return CodeEditorComponent;\n})();", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "ButtonComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "CodeEditorComponent_div_1_h3_2_span_2_Template", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "title", "ɵɵproperty", "titleRequired", "ɵɵlistener", "CodeEditorComponent_div_1_span_3_Template_ava_button_userClick_1_listener", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "onPrimaryButtonClick", "ɵɵpureFunction0", "_c1", "state", "loading", "isPrimaryButtonDisabled", "ɵɵelementContainerStart", "CodeEditorComponent_div_1_div_4_ng_container_1_Template_ava_button_userClick_2_listener", "i_r4", "_r3", "index", "onActionButtonClick", "btn_r5", "customClass", "label", "icon", "_c2", "CodeEditorComponent_div_1_div_4_ng_container_1_Template", "actionButtons", "CodeEditorComponent_div_1_h3_2_Template", "CodeEditorComponent_div_1_span_3_Template", "CodeEditorComponent_div_1_div_4_Template", "primaryButtonSelected", "observed", "length", "ɵɵelement", "CodeEditorComponent_div_3_Template_ava_button_userClick_7_listener", "_r6", "initializeEditor", "ɵɵtextInterpolate", "errorMessage", "footerText", "CodeEditorComponent", "cdr", "<PERSON><PERSON><PERSON><PERSON>", "value", "language", "theme", "height", "readonly", "customCssClass", "placeholder", "Control", "valueChange", "actionButtonClicked", "<PERSON><PERSON><PERSON><PERSON>", "error", "processing", "editor", "valueChangeTimeout", "monacoModule", "constructor", "ngOnChanges", "changes", "currentValue", "getValue", "setValue", "setModelLanguage", "getModel", "applyTheme", "ngOnInit", "configureMonacoEnvironment", "ngAfterViewInit", "setTimeout", "ngOnDestroy", "dispose", "window", "MonacoEnvironment", "getWorkerUrl", "_this", "_asyncToGenerator", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nativeElement", "Error", "style", "defineCustomThemes", "create", "automaticLayout", "minimap", "enabled", "scrollBeyondLastLine", "readOnly", "fontSize", "fontFamily", "tabSize", "lineHeight", "wordWrap", "lineNumbers", "folding", "autoIndent", "formatOnPaste", "formatOnType", "autoClosingBrackets", "autoClosingQuotes", "bracketPairColorization", "guides", "bracketPairs", "indentation", "setupEventListeners", "emit", "monaco", "defineTheme", "base", "inherit", "rules", "token", "foreground", "fontStyle", "colors", "updateOptions", "onDidChangeModelContent", "clearTimeout", "selectAll", "setSelection", "getFullModelRange", "focus", "clear", "showProcessingLoader", "hideProcessingLoader", "newValue", "setTheme", "newTheme", "isReady", "idx", "ɵɵdirectiveInject", "ChangeDetectorRef", "selectors", "viewQuery", "CodeEditorComponent_Query", "rf", "ctx", "CodeEditorComponent_div_1_Template", "CodeEditorComponent_div_2_Template", "CodeEditorComponent_div_3_Template", "CodeEditorComponent_div_6_Template", "CodeEditorComponent_div_7_Template", "ɵɵclassProp", "i1", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles", "changeDetection"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\components\\code-editor\\code-editor.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\components\\code-editor\\code-editor.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  ElementRef,\r\n  ViewChild,\r\n  Input,\r\n  Output,\r\n  EventEmitter,\r\n  OnInit,\r\n  OnDestroy,\r\n  AfterViewInit,\r\n  ChangeDetectionStrategy,\r\n  ChangeDetectorRef,\r\n  OnChanges,\r\n  SimpleChanges\r\n} from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormControl } from '@angular/forms';\r\nimport { ButtonComponent } from '@ava/play-comp-library';\r\n\r\ndeclare const monaco: any;\r\n\r\nexport type CodeLanguage = 'python' | 'javascript' | 'typescript' | 'json' | 'sql' | 'html' | 'css' | 'markdown' | 'yaml' | 'xml' | 'plaintext';\r\nexport type CodeEditorTheme = 'light' | 'dark';\r\n\r\nexport interface EditorActionButton {\r\n \r\n  label: string;\r\n  style?: 'primary' | 'secondary' | 'outline' | 'text' | 'danger';\r\n  customClass?: string;\r\n  icon?: string;\r\n}\r\n\r\n/**\r\n * Monaco Code Editor Component\r\n *\r\n * @Input title - Editor title displayed in header\r\n * @Input value - Initial code value\r\n * @Input language - Programming language (python, javascript, etc.)\r\n * @Input theme - Editor theme (light/dark)\r\n * @Input height - Editor height (default: 400px)\r\n * @Input readonly - Make editor read-only\r\n * @Input Control - Angular FormControl for form integration\r\n * @Input actionButtons - Array of custom action buttons\r\n * @Input footerText - Footer note text\r\n *\r\n * @Output valueChange - Emits when code changes\r\n * @Output primaryButtonSelected - Emits when Run button clicked\r\n * @Output actionButtonClicked - Emits when action button clicked\r\n * @Output editorReady - Emits when editor is initialized\r\n *\r\n * Usage:\r\n * <app-code-editor\r\n *   title=\"Code Editor\"\r\n *   language=\"python\"\r\n *   [Control]=\"formControl\"\r\n *   (primaryButtonSelected)=\"runCode()\">\r\n * </app-code-editor>\r\n */\r\n@Component({\r\n  selector: 'app-code-editor',\r\n  standalone: true,\r\n  imports: [CommonModule, ButtonComponent],\r\n  changeDetection: ChangeDetectionStrategy.OnPush,\r\n  templateUrl: './code-editor.component.html',\r\n  styleUrls: ['./code-editor.component.scss']\r\n})\r\nexport class CodeEditorComponent implements OnInit, AfterViewInit, OnDestroy, OnChanges {\r\n  @ViewChild('editorContainer', { static: true }) private readonly editorContainer!: ElementRef;\r\n\r\n  @Input() title = '';\r\n  @Input() value = '';\r\n  @Input() language: CodeLanguage = 'python';\r\n  @Input() theme: CodeEditorTheme = 'light';\r\n  @Input() height = '400px';\r\n  @Input() readonly = false;\r\n  @Input() customCssClass = '';\r\n  @Input() placeholder = '';\r\n  @Input() Control: FormControl | null = null;\r\n  @Input() actionButtons: EditorActionButton[] = [];\r\n  @Input() footerText = '';\r\n  @Input() isPrimaryButtonDisabled = false;\r\n\r\n\r\n  @Output() readonly valueChange = new EventEmitter<string>();\r\n  @Output() readonly primaryButtonSelected = new EventEmitter<void>();\r\n  @Output() readonly actionButtonClicked = new EventEmitter<number>();\r\n  @Output() readonly editorReady = new EventEmitter<any>();\r\n  @Input() titleRequired = false;\r\n\r\n  readonly state = {\r\n    loading: true,\r\n    error: false,\r\n    errorMessage: null as string | null,\r\n    processing: false,\r\n  };\r\n\r\n  private editor: any = null;\r\n  private valueChangeTimeout: any = null;\r\n  private monacoModule:any\r\n\r\n  constructor(private readonly cdr: ChangeDetectorRef) {}\r\n\r\n  ngOnChanges(changes: SimpleChanges): void {\r\n    if (this.editor) {\r\n      if (changes['value'] && changes['value'].currentValue !== this.editor.getValue()) {\r\n        this.editor.setValue(changes['value'].currentValue || '');\r\n      }\r\n      if (changes['language'] && changes['language'].currentValue) {\r\n        this.monacoModule.editor.setModelLanguage(this.editor.getModel(), changes['language'].currentValue);\r\n      }\r\n      if (changes['theme'] && changes['theme'].currentValue) {\r\n        this.applyTheme();\r\n      }\r\n    }\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.configureMonacoEnvironment();\r\n    if (this.Control?.value && !this.value) {\r\n      this.value = this.Control.value;\r\n    }\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    setTimeout(() => this.initializeEditor(), 100);\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.editor?.dispose();\r\n  }\r\n\r\n  private configureMonacoEnvironment(): void {\r\n    if (typeof window !== 'undefined' && !(window as any).MonacoEnvironment) {\r\n      (window as any).MonacoEnvironment = {\r\n        getWorkerUrl: () => './editor.worker.js'\r\n      };\r\n    }\r\n  }\r\n\r\n  async initializeEditor(): Promise<void> {\r\n    try {\r\n      if (this.editor) return;\r\n      this.state.loading = true;\r\n      this.state.error = false;\r\n      this.state.errorMessage = null;\r\n      this.cdr.markForCheck();\r\n\r\n      // Temporarily disabled Monaco Editor for Docker build\r\n    const monacoModule = await import('monaco-editor');\r\n\r\n    this.monacoModule = monacoModule;\r\n\r\n      if (!this.editorContainer?.nativeElement) {\r\n        throw new Error('Editor container not found');\r\n      }\r\n\r\n      this.editorContainer.nativeElement.style.height = this.height;\r\n      // Temporarily disabled Monaco Editor for Docker build\r\n      this.defineCustomThemes(monacoModule);\r\n\r\n      // Temporarily disabled Monaco Editor initialization for Docker build\r\n      \r\n      this.editor = monacoModule.editor.create(this.editorContainer.nativeElement, {\r\n        value: this.value,\r\n        language: this.language,\r\n        theme: this.theme === 'dark' ? 'code-editor-dark' : 'code-editor-light',\r\n        automaticLayout: true,\r\n        minimap: { enabled: false },\r\n        scrollBeyondLastLine: false,\r\n        readOnly: this.readonly,\r\n        placeholder: this.placeholder,\r\n        fontSize: 14,\r\n        fontFamily: \"'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', 'Consolas', 'Courier New', monospace\",\r\n        tabSize: 4,\r\n        lineHeight: 22,\r\n        wordWrap: 'on',\r\n        lineNumbers: 'on',\r\n        folding: true,\r\n        autoIndent: 'full',\r\n        formatOnPaste: true,\r\n        formatOnType: true,\r\n        autoClosingBrackets: 'always',\r\n        autoClosingQuotes: 'always',\r\n        bracketPairColorization: { enabled: true },\r\n        guides: { bracketPairs: true, indentation: true }\r\n      });\r\n\r\n      this.setupEventListeners();\r\n      \r\n      this.state.loading = false;\r\n      this.editorReady.emit(this.editor);\r\n      this.cdr.markForCheck();\r\n\r\n    } catch (error) {\r\n      this.state.loading = false;\r\n      this.state.error = true;\r\n      this.state.errorMessage = `Failed to initialize code editor: ${error}`;\r\n      this.cdr.markForCheck();\r\n    }\r\n  }\r\n\r\n  private defineCustomThemes(monaco: any): void {\r\n    monaco.editor.defineTheme('code-editor-light', {\r\n      base: 'vs',\r\n      inherit: true,\r\n      rules: [\r\n        { token: 'comment', foreground: '6c757d', fontStyle: 'italic' },\r\n        { token: 'keyword', foreground: '0066cc' },\r\n        { token: 'string', foreground: '28a745' },\r\n        { token: 'number', foreground: 'e83e8c' },\r\n        { token: 'function', foreground: 'fd7e14' }\r\n      ],\r\n      colors: {\r\n        'editor.background': '#ffffff',\r\n        'editor.foreground': '#2c3e50',\r\n        'editorLineNumber.foreground': '#6c757d',\r\n        'editor.selectionBackground': '#007bff26',\r\n        'editor.lineHighlightBackground': '#f8f9fa',\r\n        'editorCursor.foreground': '#007bff'\r\n      }\r\n    });\r\n\r\n    monaco.editor.defineTheme('code-editor-dark', {\r\n      base: 'vs-dark',\r\n      inherit: true,\r\n      rules: [\r\n        { token: 'comment', foreground: '6c757d', fontStyle: 'italic' },\r\n        { token: 'keyword', foreground: '66d9ef' },\r\n        { token: 'string', foreground: 'a6e22e' },\r\n        { token: 'number', foreground: 'fd971f' },\r\n        { token: 'function', foreground: 'f92672' }\r\n      ],\r\n      colors: {\r\n        'editor.background': '#1e1e1e',\r\n        'editor.foreground': '#e9ecef',\r\n        'editorLineNumber.foreground': '#6c757d',\r\n        'editor.selectionBackground': '#66d9ef26',\r\n        'editor.lineHighlightBackground': '#2c2c2c',\r\n        'editorCursor.foreground': '#66d9ef'\r\n      }\r\n    });\r\n  }\r\n\r\n  private applyTheme(): void {\r\n    if (this.editor) {\r\n      this.editor.updateOptions({\r\n        theme: this.theme === 'dark' ? 'code-editor-dark' : 'code-editor-light'\r\n      });\r\n    }\r\n  }\r\n\r\n  private setupEventListeners(): void {\r\n    if (!this.editor) return;\r\n\r\n    this.editor.onDidChangeModelContent(() => {\r\n      const currentValue = this.editor.getValue();\r\n      if (this.valueChangeTimeout) clearTimeout(this.valueChangeTimeout);\r\n      this.valueChangeTimeout = setTimeout(() => {\r\n        this.valueChange.emit(currentValue);\r\n        if (this.Control) {\r\n          this.Control.setValue(currentValue);\r\n        }\r\n      }, 200);\r\n    });\r\n  }\r\n\r\n  selectAll(): void {\r\n    if (this.editor) {\r\n      this.editor.setSelection(this.editor.getModel()!.getFullModelRange());\r\n      this.editor.focus();\r\n    }\r\n  }\r\n\r\n  clear(): void {\r\n    if (this.editor) {\r\n      this.editor.setValue('');\r\n      this.editor.focus();\r\n    }\r\n  }\r\n\r\n  showProcessingLoader(): void {\r\n    this.state.processing = true;\r\n    this.cdr.markForCheck();\r\n  }\r\n\r\n  hideProcessingLoader(): void {\r\n    this.state.processing = false;\r\n    this.cdr.markForCheck();\r\n  }\r\n\r\n  onPrimaryButtonClick(): void {\r\n    this.showProcessingLoader();\r\n    this.primaryButtonSelected.emit();\r\n  }\r\n\r\n  getValue(): string {\r\n    return this.editor?.getValue() ?? '';\r\n  }\r\n\r\n  setValue(newValue: string): void {\r\n    this.editor?.setValue(newValue);\r\n  }\r\n\r\n  setTheme(newTheme: CodeEditorTheme): void {\r\n    this.theme = newTheme;\r\n    this.applyTheme();\r\n  }\r\n\r\n  focus(): void {\r\n    this.editor?.focus();\r\n  }\r\n\r\n  get isReady(): boolean {\r\n    return !!this.editor && !this.state.loading;\r\n  }\r\n\r\n  onActionButtonClick(idx: number): void {\r\n    this.actionButtonClicked.emit(idx);\r\n  }\r\n}\r\n", "<div class=\"code-editor-container\" [ngClass]=\"customCssClass\">\r\n  <div class=\"editor-header\" *ngIf=\"title || primaryButtonSelected.observed\">\r\n    <div class=\"title-action-row\">\r\n      <h3 class=\"editor-title\" *ngIf=\"title\">{{ title }}\r\n         <span *ngIf=\"titleRequired\" class=\"required-asterisk\">*</span>\r\n      </h3>\r\n      <span class=\"run-btn-wrapper\" *ngIf=\"primaryButtonSelected.observed\">\r\n        <ava-button\r\n          label=\"Test\"\r\n          variant=\"primary\"\r\n          size=\"small\"\r\n          [customStyles]=\"{\r\n            background:\r\n              'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n            '--button-effect-color': '33, 90, 214',\r\n          }\"\r\n          [disabled]=\"state.loading || isPrimaryButtonDisabled\"\r\n          (userClick)=\"onPrimaryButtonClick()\"\r\n          class=\"run-black-btn\">\r\n        </ava-button>\r\n      </span>\r\n    </div>\r\n\r\n    <div class=\"editor-actions\" *ngIf=\"actionButtons.length\">\r\n      <ng-container *ngFor=\"let btn of actionButtons; let i = index\">\r\n        <span [ngClass]=\"btn.customClass\">\r\n          <ava-button\r\n            class=\"action-btn\"\r\n            [label]=\"btn.label\"\r\n            variant=\"secondary\"\r\n            [iconName]=\"btn.icon || ''\"\r\n            size=\"small\"\r\n            [customStyles]=\"{\r\n              'border': '2px solid transparent',\r\n              'background-image': 'linear-gradient(#ffffff, #ffffff), linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n              'background-origin': 'border-box',\r\n              'background-clip': 'padding-box, border-box',\r\n              '--button-effect-color': '33, 90, 214'\r\n            }\"\r\n            [pill]=\"true\"\r\n            [disabled]=\"state.loading\"\r\n            (userClick)=\"onActionButtonClick(i)\">\r\n          </ava-button>\r\n        </span>\r\n      </ng-container>\r\n    </div>\r\n  </div>\r\n\r\n  <div *ngIf=\"state.loading\" class=\"loading-container\">\r\n    <div class=\"loading-spinner\"></div>\r\n    <p>Loading Code Editor...</p>\r\n  </div>\r\n\r\n  <div *ngIf=\"state.error\" class=\"error-container\">\r\n    <div class=\"error-icon\">⚠️</div>\r\n    <h4>Failed to Load Editor</h4>\r\n    <p>{{ state.errorMessage }}</p>\r\n    <ava-button\r\n      label=\"Retry\"\r\n      variant=\"secondary\"\r\n      size=\"small\"\r\n      [pill]=\"true\"\r\n      [customStyles]=\"{\r\n        'border': '2px solid transparent',\r\n        'background-image': 'linear-gradient(#ffffff, #ffffff), linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n        'background-origin': 'border-box',\r\n        'background-clip': 'padding-box, border-box',\r\n        '--button-effect-color': '33, 90, 214'\r\n      }\"\r\n      (userClick)=\"initializeEditor()\">\r\n    </ava-button>\r\n  </div>\r\n\r\n  <div\r\n    #editorContainer\r\n    class=\"monaco-editor-container\"\r\n    [class.hidden]=\"state.loading || state.error\">\r\n  </div>\r\n\r\n  <div *ngIf=\"state.processing\" class=\"editor-loader-overlay\">\r\n    <div class=\"editor-loader-spinner\"></div>\r\n  </div>\r\n\r\n  <div class=\"editor-footer\" *ngIf=\"footerText\">\r\n    <p class=\"footer-note\">\r\n      <strong>Note:</strong> {{ footerText }}\r\n    </p>\r\n  </div>\r\n</div>\r\n"], "mappings": ";AAAA,SAMEA,YAAY,QAQP,eAAe;AACtB,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,eAAe,QAAQ,wBAAwB;;;;;;;;;;;;;;;;;ICb/CC,EAAA,CAAAC,cAAA,eAAsD;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IADjEH,EAAA,CAAAC,cAAA,aAAuC;IAAAD,EAAA,CAAAE,MAAA,GACpC;IAAAF,EAAA,CAAAI,UAAA,IAAAC,8CAAA,mBAAsD;IACzDL,EAAA,CAAAG,YAAA,EAAK;;;;IAFkCH,EAAA,CAAAM,SAAA,EACpC;IADoCN,EAAA,CAAAO,kBAAA,KAAAC,MAAA,CAAAC,KAAA,MACpC;IAAOT,EAAA,CAAAM,SAAA,EAAmB;IAAnBN,EAAA,CAAAU,UAAA,SAAAF,MAAA,CAAAG,aAAA,CAAmB;;;;;;IAG3BX,EADF,CAAAC,cAAA,eAAqE,qBAY3C;IADtBD,EAAA,CAAAY,UAAA,uBAAAC,0EAAA;MAAAb,EAAA,CAAAc,aAAA,CAAAC,GAAA;MAAA,MAAAP,MAAA,GAAAR,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAaT,MAAA,CAAAU,oBAAA,EAAsB;IAAA,EAAC;IAGxClB,EADE,CAAAG,YAAA,EAAa,EACR;;;;IATHH,EAAA,CAAAM,SAAA,EAIE;IACFN,EALA,CAAAU,UAAA,iBAAAV,EAAA,CAAAmB,eAAA,IAAAC,GAAA,EAIE,aAAAZ,MAAA,CAAAa,KAAA,CAAAC,OAAA,IAAAd,MAAA,CAAAe,uBAAA,CACmD;;;;;;IAQzDvB,EAAA,CAAAwB,uBAAA,GAA+D;IAE3DxB,EADF,CAAAC,cAAA,eAAkC,qBAgBO;IAArCD,EAAA,CAAAY,UAAA,uBAAAa,wFAAA;MAAA,MAAAC,IAAA,GAAA1B,EAAA,CAAAc,aAAA,CAAAa,GAAA,EAAAC,KAAA;MAAA,MAAApB,MAAA,GAAAR,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAaT,MAAA,CAAAqB,mBAAA,CAAAH,IAAA,CAAsB;IAAA,EAAC;IAExC1B,EADE,CAAAG,YAAA,EAAa,EACR;;;;;;IAlBDH,EAAA,CAAAM,SAAA,EAA2B;IAA3BN,EAAA,CAAAU,UAAA,YAAAoB,MAAA,CAAAC,WAAA,CAA2B;IAG7B/B,EAAA,CAAAM,SAAA,EAAmB;IAYnBN,EAZA,CAAAU,UAAA,UAAAoB,MAAA,CAAAE,KAAA,CAAmB,aAAAF,MAAA,CAAAG,IAAA,OAEQ,iBAAAjC,EAAA,CAAAmB,eAAA,IAAAe,GAAA,EAQzB,cACW,aAAA1B,MAAA,CAAAa,KAAA,CAAAC,OAAA,CACa;;;;;IAjBlCtB,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAI,UAAA,IAAA+B,uDAAA,2BAA+D;IAqBjEnC,EAAA,CAAAG,YAAA,EAAM;;;;IArB0BH,EAAA,CAAAM,SAAA,EAAkB;IAAlBN,EAAA,CAAAU,UAAA,YAAAF,MAAA,CAAA4B,aAAA,CAAkB;;;;;IAtBlDpC,EADF,CAAAC,cAAA,aAA2E,aAC3C;IAI5BD,EAHA,CAAAI,UAAA,IAAAiC,uCAAA,iBAAuC,IAAAC,yCAAA,mBAG8B;IAevEtC,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAI,UAAA,IAAAmC,wCAAA,kBAAyD;IAuB3DvC,EAAA,CAAAG,YAAA,EAAM;;;;IA3CwBH,EAAA,CAAAM,SAAA,GAAW;IAAXN,EAAA,CAAAU,UAAA,SAAAF,MAAA,CAAAC,KAAA,CAAW;IAGNT,EAAA,CAAAM,SAAA,EAAoC;IAApCN,EAAA,CAAAU,UAAA,SAAAF,MAAA,CAAAgC,qBAAA,CAAAC,QAAA,CAAoC;IAiBxCzC,EAAA,CAAAM,SAAA,EAA0B;IAA1BN,EAAA,CAAAU,UAAA,SAAAF,MAAA,CAAA4B,aAAA,CAAAM,MAAA,CAA0B;;;;;IAyBzD1C,EAAA,CAAAC,cAAA,cAAqD;IACnDD,EAAA,CAAA2C,SAAA,cAAmC;IACnC3C,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAC3BF,EAD2B,CAAAG,YAAA,EAAI,EACzB;;;;;;IAGJH,EADF,CAAAC,cAAA,cAAiD,cACvB;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAChCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC/BH,EAAA,CAAAC,cAAA,qBAYmC;IAAjCD,EAAA,CAAAY,UAAA,uBAAAgC,mEAAA;MAAA5C,EAAA,CAAAc,aAAA,CAAA+B,GAAA;MAAA,MAAArC,MAAA,GAAAR,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAaT,MAAA,CAAAsC,gBAAA,EAAkB;IAAA,EAAC;IAEpC9C,EADE,CAAAG,YAAA,EAAa,EACT;;;;IAfDH,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAA+C,iBAAA,CAAAvC,MAAA,CAAAa,KAAA,CAAA2B,YAAA,CAAwB;IAKzBhD,EAAA,CAAAM,SAAA,EAAa;IACbN,EADA,CAAAU,UAAA,cAAa,iBAAAV,EAAA,CAAAmB,eAAA,IAAAe,GAAA,EAOX;;;;;IAWNlC,EAAA,CAAAC,cAAA,cAA4D;IAC1DD,EAAA,CAAA2C,SAAA,cAAyC;IAC3C3C,EAAA,CAAAG,YAAA,EAAM;;;;;IAIFH,EAFJ,CAAAC,cAAA,cAA8C,YACrB,aACb;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,GACzB;IACFF,EADE,CAAAG,YAAA,EAAI,EACA;;;;IAFqBH,EAAA,CAAAM,SAAA,GACzB;IADyBN,EAAA,CAAAO,kBAAA,MAAAC,MAAA,CAAAyC,UAAA,MACzB;;;ADtDJ;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA,WAAaC,mBAAmB;EAA1B,MAAOA,mBAAmB;IAkCDC,GAAA;IAjCoCC,eAAe;IAEvE3C,KAAK,GAAG,EAAE;IACV4C,KAAK,GAAG,EAAE;IACVC,QAAQ,GAAiB,QAAQ;IACjCC,KAAK,GAAoB,OAAO;IAChCC,MAAM,GAAG,OAAO;IAChBC,QAAQ,GAAG,KAAK;IAChBC,cAAc,GAAG,EAAE;IACnBC,WAAW,GAAG,EAAE;IAChBC,OAAO,GAAuB,IAAI;IAClCxB,aAAa,GAAyB,EAAE;IACxCa,UAAU,GAAG,EAAE;IACf1B,uBAAuB,GAAG,KAAK;IAGrBsC,WAAW,GAAG,IAAIhE,YAAY,EAAU;IACxC2C,qBAAqB,GAAG,IAAI3C,YAAY,EAAQ;IAChDiE,mBAAmB,GAAG,IAAIjE,YAAY,EAAU;IAChDkE,WAAW,GAAG,IAAIlE,YAAY,EAAO;IAC/Cc,aAAa,GAAG,KAAK;IAErBU,KAAK,GAAG;MACfC,OAAO,EAAE,IAAI;MACb0C,KAAK,EAAE,KAAK;MACZhB,YAAY,EAAE,IAAqB;MACnCiB,UAAU,EAAE;KACb;IAEOC,MAAM,GAAQ,IAAI;IAClBC,kBAAkB,GAAQ,IAAI;IAC9BC,YAAY;IAEpBC,YAA6BlB,GAAsB;MAAtB,KAAAA,GAAG,GAAHA,GAAG;IAAsB;IAEtDmB,WAAWA,CAACC,OAAsB;MAChC,IAAI,IAAI,CAACL,MAAM,EAAE;QACf,IAAIK,OAAO,CAAC,OAAO,CAAC,IAAIA,OAAO,CAAC,OAAO,CAAC,CAACC,YAAY,KAAK,IAAI,CAACN,MAAM,CAACO,QAAQ,EAAE,EAAE;UAChF,IAAI,CAACP,MAAM,CAACQ,QAAQ,CAACH,OAAO,CAAC,OAAO,CAAC,CAACC,YAAY,IAAI,EAAE,CAAC;QAC3D;QACA,IAAID,OAAO,CAAC,UAAU,CAAC,IAAIA,OAAO,CAAC,UAAU,CAAC,CAACC,YAAY,EAAE;UAC3D,IAAI,CAACJ,YAAY,CAACF,MAAM,CAACS,gBAAgB,CAAC,IAAI,CAACT,MAAM,CAACU,QAAQ,EAAE,EAAEL,OAAO,CAAC,UAAU,CAAC,CAACC,YAAY,CAAC;QACrG;QACA,IAAID,OAAO,CAAC,OAAO,CAAC,IAAIA,OAAO,CAAC,OAAO,CAAC,CAACC,YAAY,EAAE;UACrD,IAAI,CAACK,UAAU,EAAE;QACnB;MACF;IACF;IAEAC,QAAQA,CAAA;MACN,IAAI,CAACC,0BAA0B,EAAE;MACjC,IAAI,IAAI,CAACnB,OAAO,EAAEP,KAAK,IAAI,CAAC,IAAI,CAACA,KAAK,EAAE;QACtC,IAAI,CAACA,KAAK,GAAG,IAAI,CAACO,OAAO,CAACP,KAAK;MACjC;IACF;IAEA2B,eAAeA,CAAA;MACbC,UAAU,CAAC,MAAM,IAAI,CAACnC,gBAAgB,EAAE,EAAE,GAAG,CAAC;IAChD;IAEAoC,WAAWA,CAAA;MACT,IAAI,CAAChB,MAAM,EAAEiB,OAAO,EAAE;IACxB;IAEQJ,0BAA0BA,CAAA;MAChC,IAAI,OAAOK,MAAM,KAAK,WAAW,IAAI,CAAEA,MAAc,CAACC,iBAAiB,EAAE;QACtED,MAAc,CAACC,iBAAiB,GAAG;UAClCC,YAAY,EAAEA,CAAA,KAAM;SACrB;MACH;IACF;IAEMxC,gBAAgBA,CAAA;MAAA,IAAAyC,KAAA;MAAA,OAAAC,iBAAA;QACpB,IAAI;UACF,IAAID,KAAI,CAACrB,MAAM,EAAE;UACjBqB,KAAI,CAAClE,KAAK,CAACC,OAAO,GAAG,IAAI;UACzBiE,KAAI,CAAClE,KAAK,CAAC2C,KAAK,GAAG,KAAK;UACxBuB,KAAI,CAAClE,KAAK,CAAC2B,YAAY,GAAG,IAAI;UAC9BuC,KAAI,CAACpC,GAAG,CAACsC,YAAY,EAAE;UAEvB;UACF,MAAMrB,YAAY,SAAS,MAAM,CAAC,eAAe,CAAC;UAElDmB,KAAI,CAACnB,YAAY,GAAGA,YAAY;UAE9B,IAAI,CAACmB,KAAI,CAACnC,eAAe,EAAEsC,aAAa,EAAE;YACxC,MAAM,IAAIC,KAAK,CAAC,4BAA4B,CAAC;UAC/C;UAEAJ,KAAI,CAACnC,eAAe,CAACsC,aAAa,CAACE,KAAK,CAACpC,MAAM,GAAG+B,KAAI,CAAC/B,MAAM;UAC7D;UACA+B,KAAI,CAACM,kBAAkB,CAACzB,YAAY,CAAC;UAErC;UAEAmB,KAAI,CAACrB,MAAM,GAAGE,YAAY,CAACF,MAAM,CAAC4B,MAAM,CAACP,KAAI,CAACnC,eAAe,CAACsC,aAAa,EAAE;YAC3ErC,KAAK,EAAEkC,KAAI,CAAClC,KAAK;YACjBC,QAAQ,EAAEiC,KAAI,CAACjC,QAAQ;YACvBC,KAAK,EAAEgC,KAAI,CAAChC,KAAK,KAAK,MAAM,GAAG,kBAAkB,GAAG,mBAAmB;YACvEwC,eAAe,EAAE,IAAI;YACrBC,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAK,CAAE;YAC3BC,oBAAoB,EAAE,KAAK;YAC3BC,QAAQ,EAAEZ,KAAI,CAAC9B,QAAQ;YACvBE,WAAW,EAAE4B,KAAI,CAAC5B,WAAW;YAC7ByC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAE,uFAAuF;YACnGC,OAAO,EAAE,CAAC;YACVC,UAAU,EAAE,EAAE;YACdC,QAAQ,EAAE,IAAI;YACdC,WAAW,EAAE,IAAI;YACjBC,OAAO,EAAE,IAAI;YACbC,UAAU,EAAE,MAAM;YAClBC,aAAa,EAAE,IAAI;YACnBC,YAAY,EAAE,IAAI;YAClBC,mBAAmB,EAAE,QAAQ;YAC7BC,iBAAiB,EAAE,QAAQ;YAC3BC,uBAAuB,EAAE;cAAEf,OAAO,EAAE;YAAI,CAAE;YAC1CgB,MAAM,EAAE;cAAEC,YAAY,EAAE,IAAI;cAAEC,WAAW,EAAE;YAAI;WAChD,CAAC;UAEF5B,KAAI,CAAC6B,mBAAmB,EAAE;UAE1B7B,KAAI,CAAClE,KAAK,CAACC,OAAO,GAAG,KAAK;UAC1BiE,KAAI,CAACxB,WAAW,CAACsD,IAAI,CAAC9B,KAAI,CAACrB,MAAM,CAAC;UAClCqB,KAAI,CAACpC,GAAG,CAACsC,YAAY,EAAE;QAEzB,CAAC,CAAC,OAAOzB,KAAK,EAAE;UACduB,KAAI,CAAClE,KAAK,CAACC,OAAO,GAAG,KAAK;UAC1BiE,KAAI,CAAClE,KAAK,CAAC2C,KAAK,GAAG,IAAI;UACvBuB,KAAI,CAAClE,KAAK,CAAC2B,YAAY,GAAG,qCAAqCgB,KAAK,EAAE;UACtEuB,KAAI,CAACpC,GAAG,CAACsC,YAAY,EAAE;QACzB;MAAC;IACH;IAEQI,kBAAkBA,CAACyB,MAAW;MACpCA,MAAM,CAACpD,MAAM,CAACqD,WAAW,CAAC,mBAAmB,EAAE;QAC7CC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,IAAI;QACbC,KAAK,EAAE,CACL;UAAEC,KAAK,EAAE,SAAS;UAAEC,UAAU,EAAE,QAAQ;UAAEC,SAAS,EAAE;QAAQ,CAAE,EAC/D;UAAEF,KAAK,EAAE,SAAS;UAAEC,UAAU,EAAE;QAAQ,CAAE,EAC1C;UAAED,KAAK,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAQ,CAAE,EACzC;UAAED,KAAK,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAQ,CAAE,EACzC;UAAED,KAAK,EAAE,UAAU;UAAEC,UAAU,EAAE;QAAQ,CAAE,CAC5C;QACDE,MAAM,EAAE;UACN,mBAAmB,EAAE,SAAS;UAC9B,mBAAmB,EAAE,SAAS;UAC9B,6BAA6B,EAAE,SAAS;UACxC,4BAA4B,EAAE,WAAW;UACzC,gCAAgC,EAAE,SAAS;UAC3C,yBAAyB,EAAE;;OAE9B,CAAC;MAEFR,MAAM,CAACpD,MAAM,CAACqD,WAAW,CAAC,kBAAkB,EAAE;QAC5CC,IAAI,EAAE,SAAS;QACfC,OAAO,EAAE,IAAI;QACbC,KAAK,EAAE,CACL;UAAEC,KAAK,EAAE,SAAS;UAAEC,UAAU,EAAE,QAAQ;UAAEC,SAAS,EAAE;QAAQ,CAAE,EAC/D;UAAEF,KAAK,EAAE,SAAS;UAAEC,UAAU,EAAE;QAAQ,CAAE,EAC1C;UAAED,KAAK,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAQ,CAAE,EACzC;UAAED,KAAK,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAQ,CAAE,EACzC;UAAED,KAAK,EAAE,UAAU;UAAEC,UAAU,EAAE;QAAQ,CAAE,CAC5C;QACDE,MAAM,EAAE;UACN,mBAAmB,EAAE,SAAS;UAC9B,mBAAmB,EAAE,SAAS;UAC9B,6BAA6B,EAAE,SAAS;UACxC,4BAA4B,EAAE,WAAW;UACzC,gCAAgC,EAAE,SAAS;UAC3C,yBAAyB,EAAE;;OAE9B,CAAC;IACJ;IAEQjD,UAAUA,CAAA;MAChB,IAAI,IAAI,CAACX,MAAM,EAAE;QACf,IAAI,CAACA,MAAM,CAAC6D,aAAa,CAAC;UACxBxE,KAAK,EAAE,IAAI,CAACA,KAAK,KAAK,MAAM,GAAG,kBAAkB,GAAG;SACrD,CAAC;MACJ;IACF;IAEQ6D,mBAAmBA,CAAA;MACzB,IAAI,CAAC,IAAI,CAAClD,MAAM,EAAE;MAElB,IAAI,CAACA,MAAM,CAAC8D,uBAAuB,CAAC,MAAK;QACvC,MAAMxD,YAAY,GAAG,IAAI,CAACN,MAAM,CAACO,QAAQ,EAAE;QAC3C,IAAI,IAAI,CAACN,kBAAkB,EAAE8D,YAAY,CAAC,IAAI,CAAC9D,kBAAkB,CAAC;QAClE,IAAI,CAACA,kBAAkB,GAAGc,UAAU,CAAC,MAAK;UACxC,IAAI,CAACpB,WAAW,CAACwD,IAAI,CAAC7C,YAAY,CAAC;UACnC,IAAI,IAAI,CAACZ,OAAO,EAAE;YAChB,IAAI,CAACA,OAAO,CAACc,QAAQ,CAACF,YAAY,CAAC;UACrC;QACF,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,CAAC;IACJ;IAEA0D,SAASA,CAAA;MACP,IAAI,IAAI,CAAChE,MAAM,EAAE;QACf,IAAI,CAACA,MAAM,CAACiE,YAAY,CAAC,IAAI,CAACjE,MAAM,CAACU,QAAQ,EAAG,CAACwD,iBAAiB,EAAE,CAAC;QACrE,IAAI,CAAClE,MAAM,CAACmE,KAAK,EAAE;MACrB;IACF;IAEAC,KAAKA,CAAA;MACH,IAAI,IAAI,CAACpE,MAAM,EAAE;QACf,IAAI,CAACA,MAAM,CAACQ,QAAQ,CAAC,EAAE,CAAC;QACxB,IAAI,CAACR,MAAM,CAACmE,KAAK,EAAE;MACrB;IACF;IAEAE,oBAAoBA,CAAA;MAClB,IAAI,CAAClH,KAAK,CAAC4C,UAAU,GAAG,IAAI;MAC5B,IAAI,CAACd,GAAG,CAACsC,YAAY,EAAE;IACzB;IAEA+C,oBAAoBA,CAAA;MAClB,IAAI,CAACnH,KAAK,CAAC4C,UAAU,GAAG,KAAK;MAC7B,IAAI,CAACd,GAAG,CAACsC,YAAY,EAAE;IACzB;IAEAvE,oBAAoBA,CAAA;MAClB,IAAI,CAACqH,oBAAoB,EAAE;MAC3B,IAAI,CAAC/F,qBAAqB,CAAC6E,IAAI,EAAE;IACnC;IAEA5C,QAAQA,CAAA;MACN,OAAO,IAAI,CAACP,MAAM,EAAEO,QAAQ,EAAE,IAAI,EAAE;IACtC;IAEAC,QAAQA,CAAC+D,QAAgB;MACvB,IAAI,CAACvE,MAAM,EAAEQ,QAAQ,CAAC+D,QAAQ,CAAC;IACjC;IAEAC,QAAQA,CAACC,QAAyB;MAChC,IAAI,CAACpF,KAAK,GAAGoF,QAAQ;MACrB,IAAI,CAAC9D,UAAU,EAAE;IACnB;IAEAwD,KAAKA,CAAA;MACH,IAAI,CAACnE,MAAM,EAAEmE,KAAK,EAAE;IACtB;IAEA,IAAIO,OAAOA,CAAA;MACT,OAAO,CAAC,CAAC,IAAI,CAAC1E,MAAM,IAAI,CAAC,IAAI,CAAC7C,KAAK,CAACC,OAAO;IAC7C;IAEAO,mBAAmBA,CAACgH,GAAW;MAC7B,IAAI,CAAC/E,mBAAmB,CAACuD,IAAI,CAACwB,GAAG,CAAC;IACpC;;uCA5PW3F,mBAAmB,EAAAlD,EAAA,CAAA8I,iBAAA,CAAA9I,EAAA,CAAA+I,iBAAA;IAAA;;YAAnB7F,mBAAmB;MAAA8F,SAAA;MAAAC,SAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UClEhCnJ,EAAA,CAAAC,cAAA,aAA8D;UAqD5DD,EApDA,CAAAI,UAAA,IAAAiJ,kCAAA,iBAA2E,IAAAC,kCAAA,iBA+CtB,IAAAC,kCAAA,iBAKJ;UAoBjDvJ,EAAA,CAAA2C,SAAA,gBAIM;UAMN3C,EAJA,CAAAI,UAAA,IAAAoJ,kCAAA,iBAA4D,IAAAC,kCAAA,iBAId;UAKhDzJ,EAAA,CAAAG,YAAA,EAAM;;;UAxF6BH,EAAA,CAAAU,UAAA,YAAA0I,GAAA,CAAA1F,cAAA,CAA0B;UAC/B1D,EAAA,CAAAM,SAAA,EAA6C;UAA7CN,EAAA,CAAAU,UAAA,SAAA0I,GAAA,CAAA3I,KAAA,IAAA2I,GAAA,CAAA5G,qBAAA,CAAAC,QAAA,CAA6C;UA+CnEzC,EAAA,CAAAM,SAAA,EAAmB;UAAnBN,EAAA,CAAAU,UAAA,SAAA0I,GAAA,CAAA/H,KAAA,CAAAC,OAAA,CAAmB;UAKnBtB,EAAA,CAAAM,SAAA,EAAiB;UAAjBN,EAAA,CAAAU,UAAA,SAAA0I,GAAA,CAAA/H,KAAA,CAAA2C,KAAA,CAAiB;UAuBrBhE,EAAA,CAAAM,SAAA,EAA6C;UAA7CN,EAAA,CAAA0J,WAAA,WAAAN,GAAA,CAAA/H,KAAA,CAAAC,OAAA,IAAA8H,GAAA,CAAA/H,KAAA,CAAA2C,KAAA,CAA6C;UAGzChE,EAAA,CAAAM,SAAA,GAAsB;UAAtBN,EAAA,CAAAU,UAAA,SAAA0I,GAAA,CAAA/H,KAAA,CAAA4C,UAAA,CAAsB;UAIAjE,EAAA,CAAAM,SAAA,EAAgB;UAAhBN,EAAA,CAAAU,UAAA,SAAA0I,GAAA,CAAAnG,UAAA,CAAgB;;;qBDtBlCnD,YAAY,EAAA6J,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAE/J,eAAe;MAAAgK,MAAA;MAAAC,eAAA;IAAA;;SAK5B9G,mBAAmB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}