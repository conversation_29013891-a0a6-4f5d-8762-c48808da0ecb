{"ast": null, "code": "import { HttpClient } from '@angular/common/http';\nimport { inject } from '@angular/core';\nimport { environment } from 'projects/console/src/environments/environment';\nimport * as i0 from \"@angular/core\";\nexport let UserManagementService = /*#__PURE__*/(() => {\n  class UserManagementService {\n    apiAuthUrl = environment.consoleApiAuthUrl;\n    http = inject(HttpClient);\n    getAllUsers(page, records) {\n      const url = `${this.apiAuthUrl}/user/mgmt?page=${page}&records=${records}`;\n      return this.http.get(url);\n    }\n    getUserDetails(id) {\n      const url = `${this.apiAuthUrl}/user?userId=${id}`;\n      return this.http.get(url);\n    }\n    getAllRoles() {\n      const url = `${this.apiAuthUrl}/roles`;\n      return this.http.get(url);\n    }\n    getAllPages() {\n      const url = `${this.apiAuthUrl}/pages`;\n      return this.http.get(url);\n    }\n    getAllActions() {\n      const url = `${this.apiAuthUrl}/actions`;\n      return this.http.get(url);\n    }\n    getAllRealms() {\n      const url = `${this.apiAuthUrl}/realms`;\n      return this.http.get(url);\n    }\n    getExistingAccessControl(roleId) {\n      const url = `${this.apiAuthUrl}/access/permissions?roleId=${roleId}`;\n      return this.http.get(url);\n    }\n    addNewUser(payload) {\n      const url = `${this.apiAuthUrl}/user`;\n      return this.http.post(url, payload);\n    }\n    updateUser(payload, userId) {\n      const url = `${this.apiAuthUrl}/user?userId=${userId}`;\n      return this.http.put(url, payload);\n    }\n    createRealm(name, teamId) {\n      const url = `${this.apiAuthUrl}/realm?realmName=${name}&teamId=${teamId}`;\n      return this.http.post(url, null);\n    }\n    createRole(payload) {\n      const url = `${this.apiAuthUrl}/role`;\n      return this.http.post(url, payload);\n    }\n    removeUser(id) {\n      const url = `${this.apiAuthUrl}/user/${id}`;\n      return this.http.delete(url);\n    }\n    static ɵfac = function UserManagementService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || UserManagementService)();\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: UserManagementService,\n      factory: UserManagementService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return UserManagementService;\n})();", "map": {"version": 3, "names": ["HttpClient", "inject", "environment", "UserManagementService", "apiAuthUrl", "consoleApiAuthUrl", "http", "getAllUsers", "page", "records", "url", "get", "getUserDetails", "id", "getAllRoles", "getAllPages", "getAllActions", "getAllRealms", "getExistingAccessControl", "roleId", "addNewUser", "payload", "post", "updateUser", "userId", "put", "createRealm", "name", "teamId", "createRole", "removeUser", "delete", "factory", "ɵfac", "providedIn"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\manage\\admin-management\\services\\user-management.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { inject, Injectable } from '@angular/core';\r\nimport { environment } from 'projects/console/src/environments/environment';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class UserManagementService {\r\n  private apiAuthUrl = environment.consoleApiAuthUrl;\r\n\r\n  private http = inject(HttpClient); \r\n\r\n  getAllUsers(page: number, records: number) {\r\n    const url = `${this.apiAuthUrl}/user/mgmt?page=${page}&records=${records}`;\r\n    return this.http.get(url);\r\n  }\r\n\r\n  getUserDetails(id: number) {\r\n    const url = `${this.apiAuthUrl}/user?userId=${id}`;\r\n    return this.http.get(url);\r\n  }\r\n\r\n  getAllRoles() {\r\n    const url = `${this.apiAuthUrl}/roles`;\r\n    return this.http.get(url);\r\n  }\r\n\r\n  getAllPages() {\r\n    const url = `${this.apiAuthUrl}/pages`;\r\n    return this.http.get(url);\r\n  }\r\n\r\n  getAllActions() {\r\n    const url = `${this.apiAuthUrl}/actions`;\r\n    return this.http.get(url);\r\n  } \r\n\r\n  getAllRealms() {\r\n    const url = `${this.apiAuthUrl}/realms`;\r\n    return this.http.get(url);\r\n  }\r\n  \r\n  getExistingAccessControl(roleId: string) {\r\n    const url = `${this.apiAuthUrl}/access/permissions?roleId=${roleId}`;\r\n    return this.http.get(url);\r\n  }\r\n\r\n  addNewUser(payload: any) {\r\n    const url = `${this.apiAuthUrl}/user`;\r\n    return this.http.post(url, payload);\r\n  }\r\n\r\n  updateUser(payload: any, userId: number) {\r\n    const url = `${this.apiAuthUrl}/user?userId=${userId}`;\r\n    return this.http.put(url, payload);\r\n  }\r\n\r\n  createRealm(name: string, teamId: string) {\r\n    const url = `${this.apiAuthUrl}/realm?realmName=${name}&teamId=${teamId}`;\r\n    return this.http.post(url, null);\r\n  }\r\n\r\n  createRole(payload: any) {\r\n    const url = `${this.apiAuthUrl}/role`;\r\n    return this.http.post(url, payload);\r\n  }\r\n\r\n  removeUser(id: string) {\r\n    const url = `${this.apiAuthUrl}/user/${id}`;\r\n    return this.http.delete(url);\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,sBAAsB;AACjD,SAASC,MAAM,QAAoB,eAAe;AAClD,SAASC,WAAW,QAAQ,+CAA+C;;AAK3E,WAAaC,qBAAqB;EAA5B,MAAOA,qBAAqB;IACxBC,UAAU,GAAGF,WAAW,CAACG,iBAAiB;IAE1CC,IAAI,GAAGL,MAAM,CAACD,UAAU,CAAC;IAEjCO,WAAWA,CAACC,IAAY,EAAEC,OAAe;MACvC,MAAMC,GAAG,GAAG,GAAG,IAAI,CAACN,UAAU,mBAAmBI,IAAI,YAAYC,OAAO,EAAE;MAC1E,OAAO,IAAI,CAACH,IAAI,CAACK,GAAG,CAACD,GAAG,CAAC;IAC3B;IAEAE,cAAcA,CAACC,EAAU;MACvB,MAAMH,GAAG,GAAG,GAAG,IAAI,CAACN,UAAU,gBAAgBS,EAAE,EAAE;MAClD,OAAO,IAAI,CAACP,IAAI,CAACK,GAAG,CAACD,GAAG,CAAC;IAC3B;IAEAI,WAAWA,CAAA;MACT,MAAMJ,GAAG,GAAG,GAAG,IAAI,CAACN,UAAU,QAAQ;MACtC,OAAO,IAAI,CAACE,IAAI,CAACK,GAAG,CAACD,GAAG,CAAC;IAC3B;IAEAK,WAAWA,CAAA;MACT,MAAML,GAAG,GAAG,GAAG,IAAI,CAACN,UAAU,QAAQ;MACtC,OAAO,IAAI,CAACE,IAAI,CAACK,GAAG,CAACD,GAAG,CAAC;IAC3B;IAEAM,aAAaA,CAAA;MACX,MAAMN,GAAG,GAAG,GAAG,IAAI,CAACN,UAAU,UAAU;MACxC,OAAO,IAAI,CAACE,IAAI,CAACK,GAAG,CAACD,GAAG,CAAC;IAC3B;IAEAO,YAAYA,CAAA;MACV,MAAMP,GAAG,GAAG,GAAG,IAAI,CAACN,UAAU,SAAS;MACvC,OAAO,IAAI,CAACE,IAAI,CAACK,GAAG,CAACD,GAAG,CAAC;IAC3B;IAEAQ,wBAAwBA,CAACC,MAAc;MACrC,MAAMT,GAAG,GAAG,GAAG,IAAI,CAACN,UAAU,8BAA8Be,MAAM,EAAE;MACpE,OAAO,IAAI,CAACb,IAAI,CAACK,GAAG,CAACD,GAAG,CAAC;IAC3B;IAEAU,UAAUA,CAACC,OAAY;MACrB,MAAMX,GAAG,GAAG,GAAG,IAAI,CAACN,UAAU,OAAO;MACrC,OAAO,IAAI,CAACE,IAAI,CAACgB,IAAI,CAACZ,GAAG,EAAEW,OAAO,CAAC;IACrC;IAEAE,UAAUA,CAACF,OAAY,EAAEG,MAAc;MACrC,MAAMd,GAAG,GAAG,GAAG,IAAI,CAACN,UAAU,gBAAgBoB,MAAM,EAAE;MACtD,OAAO,IAAI,CAAClB,IAAI,CAACmB,GAAG,CAACf,GAAG,EAAEW,OAAO,CAAC;IACpC;IAEAK,WAAWA,CAACC,IAAY,EAAEC,MAAc;MACtC,MAAMlB,GAAG,GAAG,GAAG,IAAI,CAACN,UAAU,oBAAoBuB,IAAI,WAAWC,MAAM,EAAE;MACzE,OAAO,IAAI,CAACtB,IAAI,CAACgB,IAAI,CAACZ,GAAG,EAAE,IAAI,CAAC;IAClC;IAEAmB,UAAUA,CAACR,OAAY;MACrB,MAAMX,GAAG,GAAG,GAAG,IAAI,CAACN,UAAU,OAAO;MACrC,OAAO,IAAI,CAACE,IAAI,CAACgB,IAAI,CAACZ,GAAG,EAAEW,OAAO,CAAC;IACrC;IAEAS,UAAUA,CAACjB,EAAU;MACnB,MAAMH,GAAG,GAAG,GAAG,IAAI,CAACN,UAAU,SAASS,EAAE,EAAE;MAC3C,OAAO,IAAI,CAACP,IAAI,CAACyB,MAAM,CAACrB,GAAG,CAAC;IAC9B;;uCA/DWP,qBAAqB;IAAA;;aAArBA,qBAAqB;MAAA6B,OAAA,EAArB7B,qBAAqB,CAAA8B,IAAA;MAAAC,UAAA,EAFpB;IAAM;;SAEP/B,qBAAqB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}