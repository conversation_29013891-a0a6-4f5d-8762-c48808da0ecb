{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\models\\tab.model.ts"], "sourcesContent": ["export interface AvaTab {\r\n  id: string;\r\n  title?: string;\r\n  label?: string;\r\n  content?: any;\r\n  isActive?: boolean;\r\n  isDisabled?: boolean;\r\n  iconName?: string;\r\n  [key: string]: any;\r\n} "], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}