{"ast": null, "code": "import { InjectionToken } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nexport const ENVIRONMENT_CONFIG = new InjectionToken('ENVIRONMENT_CONFIG');\nexport let EnvironmentService = /*#__PURE__*/(() => {\n  class EnvironmentService {\n    config;\n    constructor(config) {\n      this.config = config;\n    }\n    get consoleApi() {\n      return this.config.consoleApi || this.config.baseUrl || this.config.apiUrl || '';\n    }\n    get consoleApiV2() {\n      return this.config.consoleApiV2 || this.config.baseUrl || this.config.apiUrl || '';\n    }\n    get consoleEmbeddingApi() {\n      return this.config.consoleEmbeddingApi || this.config.baseUrl || this.config.apiUrl || '';\n    }\n    get consoleInstructionApi() {\n      return this.config.consoleInstructionApi || this.config.baseUrl || this.config.apiUrl || '';\n    }\n    get baseUrl() {\n      return this.config.baseUrl || this.config.apiUrl || '';\n    }\n    getConfig() {\n      return this.config;\n    }\n    static ɵfac = function EnvironmentService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || EnvironmentService)(i0.ɵɵinject(ENVIRONMENT_CONFIG));\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: EnvironmentService,\n      factory: EnvironmentService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return EnvironmentService;\n})();", "map": {"version": 3, "names": ["InjectionToken", "ENVIRONMENT_CONFIG", "EnvironmentService", "config", "constructor", "consoleApi", "baseUrl", "apiUrl", "consoleApiV2", "consoleEmbeddingApi", "consoleInstructionApi", "getConfig", "i0", "ɵɵinject", "factory", "ɵfac", "providedIn"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\services\\environment.service.ts"], "sourcesContent": ["import { Injectable, Inject, InjectionToken } from '@angular/core';\r\n\r\nexport interface EnvironmentConfig {\r\n  consoleApi?: string;\r\n  consoleApiV2?: string;\r\n  consoleEmbeddingApi?: string;\r\n  consoleInstructionApi?: string;\r\n  baseUrl?: string;\r\n  apiUrl?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nexport const ENVIRONMENT_CONFIG = new InjectionToken<EnvironmentConfig>('ENVIRONMENT_CONFIG');\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class EnvironmentService {\r\n  constructor(@Inject(ENVIRONMENT_CONFIG) private config: EnvironmentConfig) {}\r\n\r\n  get consoleApi(): string {\r\n    return this.config.consoleApi || this.config.baseUrl || this.config.apiUrl || '';\r\n  }\r\n\r\n  get consoleApiV2(): string {\r\n    return this.config.consoleApiV2 || this.config.baseUrl || this.config.apiUrl || '';\r\n  }\r\n\r\n  get consoleEmbeddingApi(): string {\r\n    return this.config.consoleEmbeddingApi || this.config.baseUrl || this.config.apiUrl || '';\r\n  }\r\n\r\n  get consoleInstructionApi(): string {\r\n    return this.config.consoleInstructionApi || this.config.baseUrl || this.config.apiUrl || '';\r\n  }\r\n\r\n  get baseUrl(): string {\r\n    return this.config.baseUrl || this.config.apiUrl || '';\r\n  }\r\n\r\n  getConfig(): EnvironmentConfig {\r\n    return this.config;\r\n  }\r\n} "], "mappings": "AAAA,SAA6BA,cAAc,QAAQ,eAAe;;AAYlE,OAAO,MAAMC,kBAAkB,GAAG,IAAID,cAAc,CAAoB,oBAAoB,CAAC;AAK7F,WAAaE,kBAAkB;EAAzB,MAAOA,kBAAkB;IACmBC,MAAA;IAAhDC,YAAgDD,MAAyB;MAAzB,KAAAA,MAAM,GAANA,MAAM;IAAsB;IAE5E,IAAIE,UAAUA,CAAA;MACZ,OAAO,IAAI,CAACF,MAAM,CAACE,UAAU,IAAI,IAAI,CAACF,MAAM,CAACG,OAAO,IAAI,IAAI,CAACH,MAAM,CAACI,MAAM,IAAI,EAAE;IAClF;IAEA,IAAIC,YAAYA,CAAA;MACd,OAAO,IAAI,CAACL,MAAM,CAACK,YAAY,IAAI,IAAI,CAACL,MAAM,CAACG,OAAO,IAAI,IAAI,CAACH,MAAM,CAACI,MAAM,IAAI,EAAE;IACpF;IAEA,IAAIE,mBAAmBA,CAAA;MACrB,OAAO,IAAI,CAACN,MAAM,CAACM,mBAAmB,IAAI,IAAI,CAACN,MAAM,CAACG,OAAO,IAAI,IAAI,CAACH,MAAM,CAACI,MAAM,IAAI,EAAE;IAC3F;IAEA,IAAIG,qBAAqBA,CAAA;MACvB,OAAO,IAAI,CAACP,MAAM,CAACO,qBAAqB,IAAI,IAAI,CAACP,MAAM,CAACG,OAAO,IAAI,IAAI,CAACH,MAAM,CAACI,MAAM,IAAI,EAAE;IAC7F;IAEA,IAAID,OAAOA,CAAA;MACT,OAAO,IAAI,CAACH,MAAM,CAACG,OAAO,IAAI,IAAI,CAACH,MAAM,CAACI,MAAM,IAAI,EAAE;IACxD;IAEAI,SAASA,CAAA;MACP,OAAO,IAAI,CAACR,MAAM;IACpB;;uCAzBWD,kBAAkB,EAAAU,EAAA,CAAAC,QAAA,CACTZ,kBAAkB;IAAA;;aAD3BC,kBAAkB;MAAAY,OAAA,EAAlBZ,kBAAkB,CAAAa,IAAA;MAAAC,UAAA,EAFjB;IAAM;;SAEPd,kBAAkB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}