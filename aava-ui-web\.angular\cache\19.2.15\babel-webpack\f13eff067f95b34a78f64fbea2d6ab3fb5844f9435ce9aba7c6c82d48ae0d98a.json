{"ast": null, "code": "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\nexport var scheme = new Array(3).concat(\"ece2f0a6bddb1c9099\", \"f6eff7bdc9e167a9cf02818a\", \"f6eff7bdc9e167a9cf1c9099016c59\", \"f6eff7d0d1e6a6bddb67a9cf1c9099016c59\", \"f6eff7d0d1e6a6bddb67a9cf3690c002818a016450\", \"fff7fbece2f0d0d1e6a6bddb67a9cf3690c002818a016450\", \"fff7fbece2f0d0d1e6a6bddb67a9cf3690c002818a016c59014636\").map(colors);\nexport default ramp(scheme);", "map": {"version": 3, "names": ["colors", "ramp", "scheme", "Array", "concat", "map"], "sources": ["C:/console/aava-ui-web/node_modules/d3-scale-chromatic/src/sequential-multi/PuBuGn.js"], "sourcesContent": ["import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"ece2f0a6bddb1c9099\",\n  \"f6eff7bdc9e167a9cf02818a\",\n  \"f6eff7bdc9e167a9cf1c9099016c59\",\n  \"f6eff7d0d1e6a6bddb67a9cf1c9099016c59\",\n  \"f6eff7d0d1e6a6bddb67a9cf3690c002818a016450\",\n  \"fff7fbece2f0d0d1e6a6bddb67a9cf3690c002818a016450\",\n  \"fff7fbece2f0d0d1e6a6bddb67a9cf3690c002818a016c59014636\"\n).map(colors);\n\nexport default ramp(scheme);\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,cAAc;AACjC,OAAOC,IAAI,MAAM,YAAY;AAE7B,OAAO,IAAIC,MAAM,GAAG,IAAIC,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,CACrC,oBAAoB,EACpB,0BAA0B,EAC1B,gCAAgC,EAChC,sCAAsC,EACtC,4CAA4C,EAC5C,kDAAkD,EAClD,wDACF,CAAC,CAACC,GAAG,CAACL,MAAM,CAAC;AAEb,eAAeC,IAAI,CAACC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}