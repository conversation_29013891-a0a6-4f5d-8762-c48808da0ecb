{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport SearchBar from '../search-bar/search-bar.component';\nimport { IconComponent } from \"@ava/play-comp-library\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../service/global-store.service\";\nimport * as i2 from \"@shared/index\";\nimport * as i3 from \"@angular/common\";\nfunction Hero_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\", \", ctx_r0.userName, \"\");\n  }\n}\nconst HERO_TEXT_LOOKUP = {\n  Sales: {\n    greeting: 'Heya',\n    message: \"I see you have a demo with Dover CPQ in 5 minutes. I've kept your space organized and ready for you!\"\n  },\n  'Project Team': {\n    greeting: 'Welcome back',\n    message: 'It’s been a busy week, hasn’t it? I’ve organized your space with the tasks you use most often, so you don’t have to keep searching!'\n  },\n  'Marketplace': {\n    greeting: 'Just for You and Your Team',\n    message: 'A curated list for you - browse or add more Agents & Flows to maximize value!'\n  }\n};\nlet Hero = /*#__PURE__*/(() => {\n  class Hero {\n    globalStoreService;\n    tokenStorageService;\n    heroType; // Allow parent to override hero type\n    selectedUser;\n    heroContent = HERO_TEXT_LOOKUP['Sales'];\n    constructor(globalStoreService, tokenStorageService) {\n      this.globalStoreService = globalStoreService;\n      this.tokenStorageService = tokenStorageService;\n    }\n    ngOnInit() {\n      // If heroType is provided as input, use it directly\n      if (this.heroType) {\n        this.heroContent = HERO_TEXT_LOOKUP[this.heroType] || this.heroContent;\n      } else {\n        // Otherwise, use the user type from global store\n        this.globalStoreService.selectedUser.subscribe(user => {\n          this.selectedUser = user;\n          if (user && user.type) {\n            this.heroContent = HERO_TEXT_LOOKUP[user.type] || this.heroContent;\n          }\n        });\n      }\n    }\n    get userName() {\n      const userName = this.tokenStorageService.getDaName() || 'User';\n      return userName.split(' ')[0];\n    }\n    static ɵfac = function Hero_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || Hero)(i0.ɵɵdirectiveInject(i1.GlobalStoreService), i0.ɵɵdirectiveInject(i2.TokenStorageService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: Hero,\n      selectors: [[\"app-hero\"]],\n      inputs: {\n        heroType: \"heroType\"\n      },\n      decls: 13,\n      vars: 6,\n      consts: [[1, \"hero-container\"], [1, \"hero-content\"], [1, \"greeting-section\"], [1, \"greeting-header\"], [\"iconName\", \"Sparkles\", \"iconSize\", \"26px\", 1, \"stars-icon\", 3, \"iconColor\"], [1, \"greeting-title\"], [\"class\", \"name\", 4, \"ngIf\"], [1, \"greeting-message\"], [1, \"search-bar-background\"], [1, \"search-bar-container\", \"transparent-bg\"], [1, \"name\"]],\n      template: function Hero_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelement(4, \"ava-icon\", 4);\n          i0.ɵɵelementStart(5, \"h1\", 5);\n          i0.ɵɵtext(6);\n          i0.ɵɵtemplate(7, Hero_span_7_Template, 2, 1, \"span\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"p\", 7);\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 8)(11, \"div\", 9);\n          i0.ɵɵelement(12, \"app-search-bar\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"marketplace-hero\", ctx.heroType === \"Marketplace\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"iconColor\", ctx.heroType === \"Marketplace\" ? \"#E91E63\" : \"#FFD700\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.heroContent.greeting, \"\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.heroType);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.heroContent.message, \" \");\n        }\n      },\n      dependencies: [CommonModule, i3.NgIf, SearchBar, IconComponent],\n      styles: [\".hero-container {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  width: 100%;\\n  margin-top: 2rem;\\n  margin-bottom: 62px;\\n}\\n\\n.hero-content {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  text-align: center;\\n}\\n\\n.greeting-section {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 24px;\\n  margin: 0;\\n  margin-bottom: 24px;\\n}\\n\\n.greeting-header {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  white-space: nowrap;\\n}\\n\\n.greeting-title {\\n  background: linear-gradient(90deg, #6f1c7d 0%, #f06896 100%);\\n  background-clip: text;\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  text-align: center;\\n  font-family: Mulish;\\n  font-size: 48px;\\n  font-style: normal;\\n  font-weight: 700;\\n  line-height: normal;\\n  letter-spacing: -0.912px;\\n  height: auto;\\n  width: auto;\\n  margin: 0;\\n}\\n\\n.name {\\n  background: linear-gradient(90deg, #6f1c7d 0%, #f06896 100%);\\n  background-clip: text;\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  font-family: Mulish;\\n  font-size: 48px;\\n  font-style: normal;\\n  font-weight: 700;\\n  line-height: normal;\\n  letter-spacing: -0.912px;\\n  white-space: nowrap;\\n}\\n\\n.greeting-message {\\n  width: 913px;\\n  color: #616874;\\n  text-align: center;\\n  font-family: Mulish;\\n  font-size: 20px;\\n  font-style: normal;\\n  font-weight: 600;\\n  line-height: 24px; /* 120% */\\n  margin: 0;\\n}\\n\\np {\\n  color: var(--Text-Body, #33364d);\\n  text-align: center;\\n  font-family: Mulish;\\n  font-size: 24px;\\n  font-style: normal;\\n  font-weight: 500;\\n  line-height: 150%;\\n  letter-spacing: -0.456px;\\n}\\n\\n.search-bar-background {\\n  position: relative;\\n  width: 100%;\\n  height: 60px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.transparent-bg {\\n  width: 100%;\\n  background: transparent !important;\\n  box-shadow: none !important;\\n}\\n\\n.stars-icon {\\n  height: 35px;\\n  width: 27px;\\n  margin-right: 16px;\\n  animation: blink 3s ease-in-out infinite;\\n  vertical-align: middle;\\n  margin-top: 18px;\\n}\\n\\n.marketplace-hero .greeting-title {\\n  background: none !important;\\n  -webkit-background-clip: initial !important;\\n  -webkit-text-fill-color: initial !important;\\n  background-clip: initial !important;\\n  color: #000 !important;\\n}\\n\\n/* Animations */\\n@keyframes blink {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n@keyframes blink {\\n  0%, 100% {\\n    opacity: 0;\\n  }\\n  50% {\\n    opacity: 0.8;\\n    /* Slightly reduce opacity for smoother blink */\\n  }\\n}\\n/* Responsive adjustments */\\n@media (max-width: 768px) {\\n  .greeting-title,\\n  .name {\\n    font-size: 28px;\\n  }\\n  .greeting-message {\\n    font-size: 16px;\\n  }\\n  .search-ball-left,\\n  .search-ball-right {\\n    width: 60px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      encapsulation: 2\n    });\n  }\n  return Hero;\n})();\nexport { Hero as default };", "map": {"version": 3, "names": ["CommonModule", "SearchBar", "IconComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "userName", "HERO_TEXT_LOOKUP", "Sales", "greeting", "message", "Hero", "globalStoreService", "tokenStorageService", "heroType", "selected<PERSON>ser", "hero<PERSON><PERSON>nt", "constructor", "ngOnInit", "subscribe", "user", "type", "getDaName", "split", "ɵɵdirectiveInject", "i1", "GlobalStoreService", "i2", "TokenStorageService", "selectors", "inputs", "decls", "vars", "consts", "template", "Hero_Template", "rf", "ctx", "ɵɵelement", "ɵɵtemplate", "Hero_span_7_Template", "ɵɵclassProp", "ɵɵproperty", "i3", "NgIf", "styles", "encapsulation", "default"], "sources": ["C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\shared\\components\\hero\\hero.component.ts", "C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\shared\\components\\hero\\hero.component.html"], "sourcesContent": ["import { Component, ViewEncapsulation, Input, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport SearchBar from '../search-bar/search-bar.component';\r\nimport { GlobalStoreService } from '../../service/global-store.service';\r\nimport { IconComponent } from \"@ava/play-comp-library\";\r\nimport { TokenStorageService } from '@shared/index';\r\n\r\ninterface HeroContent {\r\n  greeting: string;\r\n  message: string;\r\n}\r\n\r\nconst HERO_TEXT_LOOKUP: Record<string, HeroContent> = {\r\n  Sales: {\r\n    greeting: 'Heya',\r\n    message:\r\n      \"I see you have a demo with Dover CPQ in 5 minutes. I've kept your space organized and ready for you!\",\r\n  },\r\n  'Project Team': {\r\n    greeting: 'Welcome back',\r\n    message:\r\n      'It’s been a busy week, hasn’t it? I’ve organized your space with the tasks you use most often, so you don’t have to keep searching!',\r\n  },\r\n  'Marketplace': {\r\n    greeting: 'Just for You and Your Team',\r\n    message:\r\n      'A curated list for you - browse or add more Agents & Flows to maximize value!',\r\n  },\r\n};\r\n\r\n@Component({\r\n  selector: 'app-hero',\r\n  templateUrl: './hero.component.html',\r\n  styleUrls: ['./hero.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SearchBar, IconComponent],\r\n  encapsulation: ViewEncapsulation.None\r\n})\r\nexport default class Hero implements OnInit {\r\n  @Input() heroType?: string; // Allow parent to override hero type\r\n  selectedUser: any;\r\n  heroContent: HeroContent = HERO_TEXT_LOOKUP['Sales'];\r\n\r\n  constructor(private readonly globalStoreService: GlobalStoreService,\r\n    private tokenStorageService: TokenStorageService,\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    // If heroType is provided as input, use it directly\r\n    if (this.heroType) {\r\n      this.heroContent = HERO_TEXT_LOOKUP[this.heroType] || this.heroContent;\r\n    } else {\r\n      // Otherwise, use the user type from global store\r\n      this.globalStoreService.selectedUser.subscribe((user) => {\r\n        this.selectedUser = user;\r\n        if (user && user.type) {\r\n          this.heroContent = HERO_TEXT_LOOKUP[user.type] || this.heroContent;\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  get userName(): string {\r\n    const userName = this.tokenStorageService.getDaName() || 'User';\r\n    return userName.split(' ')[0];\r\n\r\n  }\r\n}\r\n", "<div class=\"hero-container\" [class.marketplace-hero]=\"heroType === 'Marketplace'\">\r\n  <div class=\"hero-content\">\r\n    <div class=\"greeting-section\">\r\n      <div class=\"greeting-header\">\r\n        <ava-icon iconName=\"Sparkles\" iconSize=\"26px\" [iconColor]=\"heroType === 'Marketplace' ? '#E91E63' : '#FFD700'\" class=\"stars-icon\"></ava-icon>\r\n        <h1 class=\"greeting-title\">\r\n          {{ heroContent.greeting }}<span class=\"name\" *ngIf=\"!heroType\">, {{ userName }}</span>\r\n        </h1>\r\n      </div>\r\n      <p class=\"greeting-message\">\r\n        {{ heroContent.message }}\r\n      </p>\r\n    </div>\r\n    <div class=\"search-bar-background\">\r\n      <div class=\"search-bar-container transparent-bg\">\r\n        <app-search-bar></app-search-bar>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,OAAOC,SAAS,MAAM,oCAAoC;AAE1D,SAASC,aAAa,QAAQ,wBAAwB;;;;;;;ICElBC,EAAA,CAAAC,cAAA,eAAqC;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAvBH,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAK,kBAAA,OAAAC,MAAA,CAAAC,QAAA,KAAgB;;;ADMzF,MAAMC,gBAAgB,GAAgC;EACpDC,KAAK,EAAE;IACLC,QAAQ,EAAE,MAAM;IAChBC,OAAO,EACL;GACH;EACD,cAAc,EAAE;IACdD,QAAQ,EAAE,cAAc;IACxBC,OAAO,EACL;GACH;EACD,aAAa,EAAE;IACbD,QAAQ,EAAE,4BAA4B;IACtCC,OAAO,EACL;;CAEL;AAAC,IAUmBC,IAAI;EAAX,MAAOA,IAAI;IAKMC,kBAAA;IACnBC,mBAAA;IALDC,QAAQ,CAAU,CAAC;IAC5BC,YAAY;IACZC,WAAW,GAAgBT,gBAAgB,CAAC,OAAO,CAAC;IAEpDU,YAA6BL,kBAAsC,EACzDC,mBAAwC;MADrB,KAAAD,kBAAkB,GAAlBA,kBAAkB;MACrC,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACzB;IAEJK,QAAQA,CAAA;MACN;MACA,IAAI,IAAI,CAACJ,QAAQ,EAAE;QACjB,IAAI,CAACE,WAAW,GAAGT,gBAAgB,CAAC,IAAI,CAACO,QAAQ,CAAC,IAAI,IAAI,CAACE,WAAW;MACxE,CAAC,MAAM;QACL;QACA,IAAI,CAACJ,kBAAkB,CAACG,YAAY,CAACI,SAAS,CAAEC,IAAI,IAAI;UACtD,IAAI,CAACL,YAAY,GAAGK,IAAI;UACxB,IAAIA,IAAI,IAAIA,IAAI,CAACC,IAAI,EAAE;YACrB,IAAI,CAACL,WAAW,GAAGT,gBAAgB,CAACa,IAAI,CAACC,IAAI,CAAC,IAAI,IAAI,CAACL,WAAW;UACpE;QACF,CAAC,CAAC;MACJ;IACF;IAEA,IAAIV,QAAQA,CAAA;MACV,MAAMA,QAAQ,GAAG,IAAI,CAACO,mBAAmB,CAACS,SAAS,EAAE,IAAI,MAAM;MAC/D,OAAOhB,QAAQ,CAACiB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAE/B;;uCA5BmBZ,IAAI,EAAAZ,EAAA,CAAAyB,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAA3B,EAAA,CAAAyB,iBAAA,CAAAG,EAAA,CAAAC,mBAAA;IAAA;;YAAJjB,IAAI;MAAAkB,SAAA;MAAAC,MAAA;QAAAhB,QAAA;MAAA;MAAAiB,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,cAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCnCnBrC,EAHN,CAAAC,cAAA,aAAkF,aACtD,aACM,aACC;UAC3BD,EAAA,CAAAuC,SAAA,kBAA6I;UAC7IvC,EAAA,CAAAC,cAAA,YAA2B;UACzBD,EAAA,CAAAE,MAAA,GAA0B;UAAAF,EAAA,CAAAwC,UAAA,IAAAC,oBAAA,kBAAqC;UAEnEzC,EADE,CAAAG,YAAA,EAAK,EACD;UACNH,EAAA,CAAAC,cAAA,WAA4B;UAC1BD,EAAA,CAAAE,MAAA,GACF;UACFF,EADE,CAAAG,YAAA,EAAI,EACA;UAEJH,EADF,CAAAC,cAAA,cAAmC,cACgB;UAC/CD,EAAA,CAAAuC,SAAA,sBAAiC;UAIzCvC,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACF;;;UAnBsBH,EAAA,CAAA0C,WAAA,qBAAAJ,GAAA,CAAAvB,QAAA,mBAAqD;UAI3Bf,EAAA,CAAAI,SAAA,GAAgE;UAAhEJ,EAAA,CAAA2C,UAAA,cAAAL,GAAA,CAAAvB,QAAA,2CAAgE;UAE5Gf,EAAA,CAAAI,SAAA,GAA0B;UAA1BJ,EAAA,CAAAK,kBAAA,MAAAiC,GAAA,CAAArB,WAAA,CAAAP,QAAA,KAA0B;UAAoBV,EAAA,CAAAI,SAAA,EAAe;UAAfJ,EAAA,CAAA2C,UAAA,UAAAL,GAAA,CAAAvB,QAAA,CAAe;UAI/Df,EAAA,CAAAI,SAAA,GACF;UADEJ,EAAA,CAAAK,kBAAA,MAAAiC,GAAA,CAAArB,WAAA,CAAAN,OAAA,MACF;;;qBDwBMd,YAAY,EAAA+C,EAAA,CAAAC,IAAA,EAAE/C,SAAS,EAAEC,aAAa;MAAA+C,MAAA;MAAAC,aAAA;IAAA;;SAG7BnC,IAAI;AAAA;AAAA,SAAJA,IAAI,IAAAoC,OAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}