{"ast": null, "code": "export var WorkflowModes = /*#__PURE__*/function (WorkflowModes) {\n  WorkflowModes[\"duplicate\"] = \"duplicate\";\n  return WorkflowModes;\n}(WorkflowModes || {});", "map": {"version": 3, "names": ["WorkflowModes"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\workflows\\constants\\workflow.constants.ts"], "sourcesContent": ["export enum WorkflowModes{\r\n    duplicate='duplicate'\r\n}"], "mappings": "AAAA,WAAYA,aAEX,gBAFD,UAAYA,aAAa;EACrBA,aAAA,2BAAqB;EAAA,OADbA,aAAa;AAEzB,CAAC,CAFWA,aAAa,OAExB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}