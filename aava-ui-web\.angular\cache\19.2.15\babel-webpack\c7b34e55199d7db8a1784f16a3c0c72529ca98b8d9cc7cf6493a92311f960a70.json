{"ast": null, "code": "import { constructNow } from \"./constructNow.js\";\nimport { isSameSecond } from \"./isSameSecond.js\";\n\n/**\n * @name isThisSecond\n * @category Second Helpers\n * @summary Is the given date in the same second as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same second as the current date?\n *\n * @param date - The date to check\n *\n * @returns The date is in this second\n *\n * @example\n * // If now is 25 September 2014 18:30:15.500,\n * // is 25 September 2014 18:30:15.000 in this second?\n * const result = isThisSecond(new Date(2014, 8, 25, 18, 30, 15))\n * //=> true\n */\nexport function isThisSecond(date) {\n  return isSameSecond(date, constructNow(date));\n}\n\n// Fallback for modularized imports:\nexport default isThisSecond;", "map": {"version": 3, "names": ["constructNow", "isSameSecond", "isThisSecond", "date"], "sources": ["C:/console/aava-ui-web/node_modules/date-fns/isThisSecond.js"], "sourcesContent": ["import { constructNow } from \"./constructNow.js\";\nimport { isSameSecond } from \"./isSameSecond.js\";\n\n/**\n * @name isThisSecond\n * @category Second Helpers\n * @summary Is the given date in the same second as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same second as the current date?\n *\n * @param date - The date to check\n *\n * @returns The date is in this second\n *\n * @example\n * // If now is 25 September 2014 18:30:15.500,\n * // is 25 September 2014 18:30:15.000 in this second?\n * const result = isThisSecond(new Date(2014, 8, 25, 18, 30, 15))\n * //=> true\n */\nexport function isThisSecond(date) {\n  return isSameSecond(date, constructNow(date));\n}\n\n// Fallback for modularized imports:\nexport default isThisSecond;\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,mBAAmB;AAChD,SAASC,YAAY,QAAQ,mBAAmB;;AAEhD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAACC,IAAI,EAAE;EACjC,OAAOF,YAAY,CAACE,IAAI,EAAEH,YAAY,CAACG,IAAI,CAAC,CAAC;AAC/C;;AAEA;AACA,eAAeD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}