{"ast": null, "code": "import { CommonModule, DatePipe } from '@angular/common';\nimport { PageFooterComponent } from '@shared/components/page-footer/page-footer.component';\nimport { IconComponent, DropdownComponent, AvaTextboxComponent, DialogService, TextCardComponent } from '@ava/play-comp-library';\nimport { LucideAngularModule } from 'lucide-angular';\nimport { ConsoleCardComponent } from '@shared/components/console-card/console-card.component';\nimport { TimeAgoPipe } from '@shared/pipes/time-ago.pipe';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { startWith, debounceTime, distinctUntilChanged } from 'rxjs';\nimport { WorkflowModes } from './constants/workflow.constants';\nimport workflowConstants from './constants/workflows.json';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@shared/services/pagination.service\";\nimport * as i2 from \"../../services/debounced-search.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@shared/services/workflow.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"@shared/index\";\nimport * as i8 from \"@ava/play-comp-library\";\nfunction WorkflowsComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14)(2, \"h5\", 15);\n    i0.ɵɵtext(3, \"No workflow found matching your criteria\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction WorkflowsComponent_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"ava-console-card\", 16);\n    i0.ɵɵpipe(2, \"timeAgo\");\n    i0.ɵɵlistener(\"actionClick\", function WorkflowsComponent_ng_container_11_Template_ava_console_card_actionClick_1_listener($event) {\n      const workflow_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onActionClick($event, workflow_r2.id));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const workflow_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"title\", workflow_r2 == null ? null : workflow_r2.title)(\"description\", workflow_r2 == null ? null : workflow_r2.description)(\"author\", (workflow_r2 == null ? null : workflow_r2.owner) || \"AAVA\")(\"date\", i0.ɵɵpipeBind1(2, 6, workflow_r2 == null ? null : workflow_r2.createdDate))(\"actions\", ctx_r2.defaultActions)(\"skeleton\", ctx_r2.isLoading);\n  }\n}\nfunction WorkflowsComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 18)(2, \"app-page-footer\", 19);\n    i0.ɵɵlistener(\"pageChange\", function WorkflowsComponent_div_12_Template_app_page_footer_pageChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onPageChange($event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"totalItems\", ctx_r2.totalRecords)(\"currentPage\", ctx_r2.currentPage)(\"itemsPerPage\", ctx_r2.itemsPerPage);\n  }\n}\nexport let WorkflowsComponent = /*#__PURE__*/(() => {\n  class WorkflowsComponent {\n    paginationService;\n    debounceService;\n    router;\n    workflowService;\n    datePipe;\n    fb;\n    tokenStorage;\n    dialogService;\n    defaultActions = [{\n      id: 'duplicate',\n      icon: 'copy',\n      label: 'Duplicate',\n      tooltip: 'Duplicate'\n    }, {\n      id: 'edit',\n      icon: 'edit',\n      label: 'Edit item',\n      tooltip: 'Edit'\n    }, {\n      id: 'delete',\n      icon: 'trash',\n      label: 'Delete item',\n      tooltip: 'Delete'\n    }, {\n      id: 'run',\n      icon: 'play',\n      label: 'Run application',\n      tooltip: 'Run',\n      isPrimary: true\n    }];\n    allWorkflows = [];\n    filteredWorkflows = [];\n    displayedWorkflows = [];\n    isLoading = false;\n    error = null;\n    currentPage = 1;\n    itemsPerPage = 11;\n    totalPages = 1;\n    workflowsOptions = [{\n      name: 'All',\n      value: 'all'\n    }, {\n      name: 'Type A',\n      value: 'typeA'\n    }, {\n      name: 'Type B',\n      value: 'typeB'\n    }];\n    selectedData = null;\n    searchForm;\n    cardSkeletonPlaceholders = Array(11);\n    totalRecords = 25;\n    showDeleteWorkflowPopup = false;\n    showInfoPopup = false;\n    infoMessage = '';\n    showErrorPopup = false;\n    labels = workflowConstants.labels;\n    workflowId = '';\n    constructor(paginationService, debounceService, router, workflowService, datePipe, fb, tokenStorage, dialogService) {\n      this.paginationService = paginationService;\n      this.debounceService = debounceService;\n      this.router = router;\n      this.workflowService = workflowService;\n      this.datePipe = datePipe;\n      this.fb = fb;\n      this.tokenStorage = tokenStorage;\n      this.dialogService = dialogService;\n      this.searchForm = this.fb.group({\n        search: ['']\n      });\n    }\n    ngOnInit() {\n      this.searchList();\n      this.fetchAllWorkflows();\n    }\n    searchList() {\n      this.searchForm.get('search').valueChanges.pipe(startWith(''), debounceTime(300), distinctUntilChanged()).subscribe(searchText => {\n        const trimmed = searchText.trim();\n        if (trimmed) {\n          // API-based search\n          this.isLoading = true;\n          this.debounceService.triggerSearch(trimmed, 'workflows', 'default');\n        } else {\n          // Fallback to default list\n          this.fetchAllWorkflows();\n        }\n      });\n      this.debounceService.searchResults$.subscribe({\n        next: results => {\n          this.isLoading = false;\n          // Extract `workflowDetails` safely\n          const workflows = Array.isArray(results?.workflowDetails) ? results.workflowDetails : [];\n          this.allWorkflows = workflows.map(item => ({\n            ...item,\n            id: item.id,\n            title: item.name,\n            name: item.name,\n            description: item.description || 'No description',\n            createdDate: this.datePipe.transform(item.createdAt, 'MM/dd/yyyy') || ''\n          }));\n          this.filteredWorkflows = [...this.allWorkflows];\n          this.updateDisplayedWorkflows(); // Ensure visible in UI\n        },\n        error: () => {\n          this.error = 'Search failed.';\n          this.isLoading = false;\n        }\n      });\n    }\n    fetchAllWorkflows() {\n      this.isLoading = true;\n      this.workflowService.fetchAllV2Workflows(this.currentPage, this.itemsPerPage, false).subscribe({\n        next: response => {\n          this.totalRecords = response.totalNoOfRecords;\n          const pipeLines = response.workflowDetails || response;\n          this.allWorkflows = pipeLines.map(item => ({\n            ...item,\n            id: item.id,\n            title: item.name,\n            name: item.name,\n            description: item.description || 'No description',\n            createdDate: this.datePipe.transform(item.createdAt, 'MM/dd/yyyy') || ''\n          }));\n          this.filteredWorkflows = [...this.allWorkflows];\n          this.displayedWorkflows = [...this.allWorkflows];\n          this.isLoading = false;\n        },\n        error: error => {\n          this.error = error.message || 'Failed to load workflows';\n          this.isLoading = false;\n        }\n      });\n    }\n    updateDisplayedWorkflows() {\n      if (this.currentPage === 1) {\n        this.itemsPerPage = 11;\n      } else {\n        this.itemsPerPage = 12;\n      }\n      const paginationResult = this.paginationService.getPaginatedItems(this.filteredWorkflows, this.currentPage, this.itemsPerPage);\n      this.displayedWorkflows = paginationResult.displayedItems;\n      this.totalPages = paginationResult.totalPages;\n    }\n    onCreateWorkflow() {\n      this.router.navigate(['/build/workflows/create']);\n    }\n    editWorkflow(workflowId) {\n      this.router.navigate(['/build/workflows/edit', workflowId]);\n    }\n    onActionClick(event, workflowId) {\n      switch (event.actionId) {\n        case 'edit':\n          this.editWorkflow(workflowId);\n          break;\n        case 'delete':\n          this.handleDeleteWorkflow(workflowId);\n          break;\n        case 'duplicate':\n          this.duplicateWorkflow(workflowId);\n          break;\n        case 'run':\n          this.executePrompt(workflowId);\n          break;\n        default:\n          break;\n      }\n    }\n    executePrompt(workflowId) {\n      this.router.navigate(['/build/workflows/execute', workflowId]);\n    }\n    handleDeleteWorkflow(workflowId) {\n      const workflow = this.filteredWorkflows.find(w => w.id === workflowId);\n      if (!workflow) return;\n      this.dialogService.confirmation({\n        title: 'Delete Workflow',\n        message: `Are you sure you want to delete \"${workflow.name}\"? This action cannot be undone.`,\n        confirmButtonText: 'Delete',\n        cancelButtonText: 'Cancel'\n      }).then(result => {\n        if (result.confirmed) {\n          this.deleteWorkflow(workflowId);\n        }\n      });\n    }\n    deleteWorkflow(workflowId) {\n      // Store the loading dialog reference\n      const loadingDialog = this.dialogService.loading({\n        title: 'Deleting Workflow',\n        message: 'Please wait while we delete the workflow...',\n        showProgress: true\n      });\n      const modifiedBy = this.tokenStorage.getDaUsername() || '';\n      this.workflowService.deleteWorkflow(workflowId, modifiedBy).subscribe({\n        next: response => {\n          // Close the loading dialog\n          this.dialogService.close();\n          // Show success message\n          this.dialogService.sucess({\n            title: 'Workflow Deleted',\n            message: response?.message || 'Workflow deleted successfully',\n            confirmButtonText: 'OK'\n          }).then(() => {\n            this.fetchAllWorkflows();\n          });\n        },\n        error: error => {\n          // Close the loading dialog\n          this.dialogService.close();\n          // Show error message\n          this.dialogService.error({\n            title: 'Error',\n            message: error?.message || 'Failed to delete workflow',\n            retryButtonText: 'OK'\n          });\n        }\n      });\n    }\n    duplicateWorkflow(workflowId) {\n      this.router.navigate(['/build/workflows/create'], {\n        queryParams: {\n          id: workflowId,\n          mode: WorkflowModes.duplicate\n        }\n      });\n    }\n    onSelectionChange(data) {\n      this.selectedData = data;\n    }\n    onPageChange(page) {\n      this.currentPage = page;\n      this.fetchAllWorkflows();\n    }\n    static ɵfac = function WorkflowsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || WorkflowsComponent)(i0.ɵɵdirectiveInject(i1.PaginationService), i0.ɵɵdirectiveInject(i2.DebouncedSearchService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.WorkflowService), i0.ɵɵdirectiveInject(i5.DatePipe), i0.ɵɵdirectiveInject(i6.FormBuilder), i0.ɵɵdirectiveInject(i7.TokenStorageService), i0.ɵɵdirectiveInject(i8.DialogService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: WorkflowsComponent,\n      selectors: [[\"app-workflows\"]],\n      features: [i0.ɵɵProvidersFeature([DatePipe, DialogService])],\n      decls: 13,\n      vars: 9,\n      consts: [[\"id\", \"workflows-container\", 1, \"container-fluid\"], [\"id\", \"search-filter-container\", 1, \"row\", \"g-3\"], [1, \"col-12\", \"col-md-8\", \"col-lg-9\", \"col-xl-10\", \"search-section\"], [3, \"formGroup\"], [\"placeholder\", \"Search \\\"Workflows\\\"\", \"hoverEffect\", \"glow\", \"pressedEffect\", \"solid\", \"formControlName\", \"search\"], [\"slot\", \"icon-start\", \"iconName\", \"search\", \"iconColor\", \"var(--color-brand-primary)\", 3, \"iconSize\"], [1, \"col-12\", \"col-md-4\", \"col-lg-3\", \"col-xl-2\", \"action-buttons\"], [\"dropdownTitle\", \"choose workflow\", 3, \"selectionChange\", \"options\"], [\"id\", \"prompts-card-container\", 1, \"row\", \"g-3\"], [\"iconColor\", \"#144692\", \"title\", \"Create Workflow\", 1, \"col-12\", \"col-sm-6\", \"col-md-4\", \"col-lg-3\", \"col-xl-3\", \"col-xxl-2\", \"mt-5\", 3, \"cardClick\", \"type\", \"iconName\", \"isLoading\"], [\"class\", \"col-12 d-flex justify-content-center align-items-center py-5\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"row\", 4, \"ngIf\"], [1, \"col-12\", \"d-flex\", \"justify-content-center\", \"align-items-center\", \"py-5\"], [1, \"text-center\"], [1, \"text-muted\"], [\"categoryIcon\", \"bot\", \"categoryTitle\", \"Workflows\", \"categoryValue\", \"42\", 1, \"col-12\", \"col-sm-6\", \"col-md-4\", \"col-lg-3\", \"col-xl-3\", \"col-xxl-2\", \"mt-5\", 3, \"actionClick\", \"title\", \"description\", \"author\", \"date\", \"actions\", \"skeleton\"], [1, \"row\"], [1, \"col-12\", \"d-flex\", \"justify-content-center\", \"mt-4\"], [3, \"pageChange\", \"totalItems\", \"currentPage\", \"itemsPerPage\"]],\n      template: function WorkflowsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"form\", 3)(4, \"ava-textbox\", 4);\n          i0.ɵɵelement(5, \"ava-icon\", 5);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"ava-dropdown\", 7);\n          i0.ɵɵlistener(\"selectionChange\", function WorkflowsComponent_Template_ava_dropdown_selectionChange_7_listener($event) {\n            return ctx.onSelectionChange($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(8, \"div\", 8)(9, \"ava-text-card\", 9);\n          i0.ɵɵlistener(\"cardClick\", function WorkflowsComponent_Template_ava_text_card_cardClick_9_listener() {\n            return ctx.onCreateWorkflow();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(10, WorkflowsComponent_div_10_Template, 4, 0, \"div\", 10)(11, WorkflowsComponent_ng_container_11_Template, 3, 8, \"ng-container\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(12, WorkflowsComponent_div_12_Template, 3, 3, \"div\", 12);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"formGroup\", ctx.searchForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"iconSize\", 16);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"options\", ctx.workflowsOptions);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"type\", \"create\")(\"iconName\", \"plus\")(\"isLoading\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.displayedWorkflows.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.isLoading && ctx.displayedWorkflows.length === 0 ? ctx.cardSkeletonPlaceholders : ctx.displayedWorkflows);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.filteredWorkflows.length > 0);\n        }\n      },\n      dependencies: [CommonModule, i5.NgForOf, i5.NgIf, PageFooterComponent, TextCardComponent, AvaTextboxComponent, DropdownComponent, LucideAngularModule, IconComponent, ReactiveFormsModule, i6.ɵNgNoValidate, i6.NgControlStatus, i6.NgControlStatusGroup, i6.FormGroupDirective, i6.FormControlName, ConsoleCardComponent, TimeAgoPipe],\n      styles: [\".ava-dropdown {\\n  width: 100% !important;\\n}\\n\\n.mt-5[_ngcontent-%COMP%] {\\n  margin-top: 2rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3Byb2plY3RzL3NoYXJlZC9wYWdlcy93b3JrZmxvd3Mvd29ya2Zsb3dzLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0Usc0JBQUE7QUFDRjs7QUFFQTtFQUNFLGdCQUFBO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyI6Om5nLWRlZXAgLmF2YS1kcm9wZG93biB7XHJcbiAgd2lkdGg6IDEwMCUgIWltcG9ydGFudDtcclxufVxyXG5cclxuLm10LTUge1xyXG4gIG1hcmdpbi10b3A6IDJyZW07XHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n  return WorkflowsComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "DatePipe", "PageFooterComponent", "IconComponent", "DropdownComponent", "AvaTextboxComponent", "DialogService", "TextCardComponent", "LucideAngularModule", "ConsoleCardComponent", "TimeAgoPipe", "ReactiveFormsModule", "startWith", "debounceTime", "distinctUntilChanged", "WorkflowModes", "workflowConstants", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelementContainerStart", "ɵɵlistener", "WorkflowsComponent_ng_container_11_Template_ava_console_card_actionClick_1_listener", "$event", "workflow_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "onActionClick", "id", "ɵɵadvance", "ɵɵproperty", "title", "description", "owner", "ɵɵpipeBind1", "createdDate", "defaultActions", "isLoading", "WorkflowsComponent_div_12_Template_app_page_footer_pageChange_2_listener", "_r4", "onPageChange", "totalRecords", "currentPage", "itemsPerPage", "WorkflowsComponent", "paginationService", "debounceService", "router", "workflowService", "datePipe", "fb", "tokenStorage", "dialogService", "icon", "label", "tooltip", "isPrimary", "allWorkflows", "filteredWorkflows", "displayedWorkflows", "error", "totalPages", "workflowsOptions", "name", "value", "selectedData", "searchForm", "cardSkeletonPlaceholders", "Array", "showDeleteWorkflowPopup", "showInfoPopup", "infoMessage", "showErrorPopup", "labels", "workflowId", "constructor", "group", "search", "ngOnInit", "searchList", "fetchAllWorkflows", "get", "valueChanges", "pipe", "subscribe", "searchText", "trimmed", "trim", "triggerSearch", "searchResults$", "next", "results", "workflows", "isArray", "workflowDetails", "map", "item", "transform", "createdAt", "updateDisplayedWorkflows", "fetchAllV2Workflows", "response", "totalNoOfRecords", "pipeLines", "message", "paginationResult", "getPaginatedItems", "displayedItems", "onCreateWorkflow", "navigate", "editWorkflow", "event", "actionId", "handleDeleteWorkflow", "duplicateWorkflow", "executePrompt", "workflow", "find", "w", "confirmation", "confirmButtonText", "cancelButtonText", "then", "result", "confirmed", "deleteWorkflow", "loadingDialog", "loading", "showProgress", "modifiedBy", "getDaUsername", "close", "sucess", "retryButtonText", "queryParams", "mode", "duplicate", "onSelectionChange", "data", "page", "ɵɵdirectiveInject", "i1", "PaginationService", "i2", "DebouncedSearchService", "i3", "Router", "i4", "WorkflowService", "i5", "i6", "FormBuilder", "i7", "TokenStorageService", "i8", "selectors", "features", "ɵɵProvidersFeature", "decls", "vars", "consts", "template", "WorkflowsComponent_Template", "rf", "ctx", "ɵɵelement", "WorkflowsComponent_Template_ava_dropdown_selectionChange_7_listener", "WorkflowsComponent_Template_ava_text_card_cardClick_9_listener", "ɵɵtemplate", "WorkflowsComponent_div_10_Template", "WorkflowsComponent_ng_container_11_Template", "WorkflowsComponent_div_12_Template", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "ɵNgNoValidate", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\workflows\\workflows.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\pages\\workflows\\workflows.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { CommonModule, DatePipe } from '@angular/common';\r\nimport { PageFooterComponent } from '@shared/components/page-footer/page-footer.component';\r\nimport { PaginationService } from '@shared/services/pagination.service';\r\nimport { WorkflowService } from '@shared/services/workflow.service';\r\nimport { TokenStorageService } from '@shared/index';\r\n\r\nimport {\r\n  ButtonComponent,\r\n  IconComponent,\r\n  DropdownComponent,\r\n  DropdownOption,\r\n  AvaTextboxComponent,\r\n  DialogService,\r\n  TextCardComponent,\r\n} from '@ava/play-comp-library';\r\nimport { LucideAngularModule } from 'lucide-angular';\r\nimport {\r\n  ConsoleCardComponent,\r\n  ConsoleCardAction,\r\n} from '@shared/components/console-card/console-card.component';\r\nimport { TimeAgoPipe } from '@shared/pipes/time-ago.pipe';\r\nimport { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';\r\nimport { startWith, debounceTime, distinctUntilChanged, map } from 'rxjs';\r\nimport { WorkflowModes } from './constants/workflow.constants';\r\nimport workflowConstants from './constants/workflows.json';\r\nimport { DebouncedSearchService } from '../../services/debounced-search.service';\r\n\r\n@Component({\r\n  selector: 'app-workflows',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    PageFooterComponent,\r\n    TextCardComponent,\r\n    AvaTextboxComponent,\r\n    DropdownComponent,\r\n    LucideAngularModule,\r\n    IconComponent,\r\n    ReactiveFormsModule,\r\n    ConsoleCardComponent,\r\n    TimeAgoPipe\r\n  ],\r\n  providers: [DatePipe, DialogService],\r\n  templateUrl: './workflows.component.html',\r\n  styleUrl: './workflows.component.scss',\r\n})\r\nexport class WorkflowsComponent implements OnInit {\r\n  defaultActions: ConsoleCardAction[] = [\r\n    {\r\n      id: 'duplicate',\r\n      icon: 'copy',\r\n      label: 'Duplicate',\r\n      tooltip: 'Duplicate',\r\n    },\r\n    {\r\n      id: 'edit',\r\n      icon: 'edit',\r\n      label: 'Edit item',\r\n      tooltip: 'Edit',\r\n    },\r\n    {\r\n      id: 'delete',\r\n      icon: 'trash',\r\n      label: 'Delete item',\r\n      tooltip: 'Delete',\r\n    },\r\n    {\r\n      id: 'run',\r\n      icon: 'play',\r\n      label: 'Run application',\r\n      tooltip: 'Run',\r\n      isPrimary: true,\r\n    },\r\n  ];\r\n  allWorkflows: any[] = [];\r\n  filteredWorkflows: any[] = [];\r\n  displayedWorkflows: any[] = [];\r\n  isLoading: boolean = false;\r\n  error: string | null = null;\r\n  currentPage: number = 1;\r\n  itemsPerPage: number = 11;\r\n  totalPages: number = 1;\r\n  workflowsOptions: DropdownOption[] = [\r\n    { name: 'All', value: 'all' },\r\n    { name: 'Type A', value: 'typeA' },\r\n    { name: 'Type B', value: 'typeB' },\r\n  ];\r\n  selectedData: any = null;\r\n  searchForm!: FormGroup;\r\n  cardSkeletonPlaceholders = Array(11);\r\n  public totalRecords: number = 25;\r\n  public showDeleteWorkflowPopup: boolean = false;\r\n  public showInfoPopup: boolean = false;\r\n  public infoMessage: string = '';\r\n  public showErrorPopup: boolean = false;\r\n  public labels = workflowConstants.labels;\r\n  public workflowId: string = '';\r\n\r\n  constructor(\r\n    private paginationService: PaginationService,\r\n    private debounceService: DebouncedSearchService,\r\n    private router: Router,\r\n    private workflowService: WorkflowService,\r\n    private datePipe: DatePipe,\r\n    private fb: FormBuilder,\r\n    private tokenStorage: TokenStorageService,\r\n    private dialogService: DialogService\r\n\r\n  ) {\r\n    this.searchForm = this.fb.group({\r\n      search: [''],\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.searchList();\r\n    this.fetchAllWorkflows();\r\n  }\r\n\r\n  private searchList() {\r\n    this.searchForm\r\n      .get('search')!\r\n      .valueChanges\r\n      .pipe(\r\n        startWith(''),\r\n        debounceTime(300),\r\n        distinctUntilChanged()\r\n      )\r\n      .subscribe((searchText: string) => {\r\n        const trimmed = searchText.trim();\r\n        if (trimmed) {\r\n          // API-based search\r\n          this.isLoading = true;\r\n          this.debounceService.triggerSearch(trimmed, 'workflows', 'default');\r\n        } else {\r\n          // Fallback to default list\r\n          this.fetchAllWorkflows();\r\n        }\r\n      });\r\n    this.debounceService.searchResults$.subscribe({\r\n      next: (results: any) => {\r\n        this.isLoading = false;\r\n\r\n        // Extract `workflowDetails` safely\r\n        const workflows = Array.isArray(results?.workflowDetails)\r\n          ? results.workflowDetails\r\n          : [];\r\n\r\n        this.allWorkflows = workflows.map((item: any) => ({\r\n          ...item,\r\n          id: item.id,\r\n          title: item.name,\r\n          name: item.name,\r\n          description: item.description || 'No description',\r\n          createdDate: this.datePipe.transform(item.createdAt, 'MM/dd/yyyy') || '',\r\n        }));\r\n\r\n        this.filteredWorkflows = [...this.allWorkflows];\r\n        this.updateDisplayedWorkflows(); // Ensure visible in UI\r\n      },\r\n      error: () => {\r\n        this.error = 'Search failed.';\r\n        this.isLoading = false;\r\n      },\r\n    });\r\n  }\r\n\r\n  private fetchAllWorkflows() {\r\n    this.isLoading = true;\r\n    this.workflowService\r\n      .fetchAllV2Workflows(this.currentPage, this.itemsPerPage, false)\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.totalRecords = response.totalNoOfRecords;\r\n          const pipeLines = response.workflowDetails || response;\r\n          this.allWorkflows = pipeLines.map((item: any) => ({\r\n            ...item,\r\n            id: item.id,\r\n            title: item.name,\r\n            name: item.name,\r\n            description: item.description || 'No description',\r\n            createdDate:\r\n              this.datePipe.transform(item.createdAt, 'MM/dd/yyyy') || '',\r\n          }));\r\n          this.filteredWorkflows = [...this.allWorkflows];\r\n          this.displayedWorkflows = [...this.allWorkflows];\r\n          this.isLoading = false;\r\n        },\r\n        error: (error) => {\r\n          this.error = error.message || 'Failed to load workflows';\r\n          this.isLoading = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  private updateDisplayedWorkflows(): void {\r\n    if (this.currentPage === 1) {\r\n      this.itemsPerPage = 11;\r\n    } else {\r\n      this.itemsPerPage = 12;\r\n    }\r\n    const paginationResult = this.paginationService.getPaginatedItems(\r\n      this.filteredWorkflows,\r\n      this.currentPage,\r\n      this.itemsPerPage,\r\n    );\r\n    this.displayedWorkflows = paginationResult.displayedItems;\r\n    this.totalPages = paginationResult.totalPages;\r\n  }\r\n\r\n  public onCreateWorkflow(): void {\r\n    this.router.navigate(['/build/workflows/create']);\r\n  }\r\n\r\n  private editWorkflow(workflowId: string): void {\r\n    this.router.navigate(['/build/workflows/edit', workflowId]);\r\n  }\r\n\r\n  public onActionClick(\r\n    event: { actionId: string; action: ConsoleCardAction },\r\n    workflowId: string,\r\n  ): void {\r\n    switch (event.actionId) {\r\n      case 'edit':\r\n        this.editWorkflow(workflowId);\r\n        break;\r\n      case 'delete':\r\n        this.handleDeleteWorkflow(workflowId);\r\n        break;\r\n      case 'duplicate':\r\n        this.duplicateWorkflow(workflowId);\r\n        break;\r\n      case 'run':\r\n        this.executePrompt(workflowId);\r\n        break;\r\n      default:\r\n        break;\r\n    }\r\n  }\r\n\r\n  executePrompt(workflowId: string): void {\r\n    this.router.navigate(['/build/workflows/execute', workflowId]);\r\n  }\r\n\r\n  public handleDeleteWorkflow(workflowId: string): void {\r\n    const workflow = this.filteredWorkflows.find(w => w.id === workflowId);\r\n    if (!workflow) return;\r\n\r\n    this.dialogService.confirmation({\r\n      title: 'Delete Workflow',\r\n      message: `Are you sure you want to delete \"${workflow.name}\"? This action cannot be undone.`,\r\n      confirmButtonText: 'Delete',\r\n      cancelButtonText: 'Cancel',\r\n    }).then(result => {\r\n      if (result.confirmed) {\r\n        this.deleteWorkflow(workflowId);\r\n      }\r\n    });\r\n  }\r\n\r\n  private deleteWorkflow(workflowId: string): void {\r\n    // Store the loading dialog reference\r\n    const loadingDialog = this.dialogService.loading({\r\n      title: 'Deleting Workflow',\r\n      message: 'Please wait while we delete the workflow...',\r\n      showProgress: true\r\n    });\r\n\r\n    const modifiedBy = this.tokenStorage.getDaUsername() || '';\r\n    this.workflowService.deleteWorkflow(workflowId, modifiedBy).subscribe({\r\n      next: (response: any) => {\r\n        // Close the loading dialog\r\n        this.dialogService.close();\r\n        \r\n        // Show success message\r\n        this.dialogService.sucess({\r\n          title: 'Workflow Deleted',\r\n          message: response?.message || 'Workflow deleted successfully',\r\n          confirmButtonText: 'OK'\r\n        }).then(() => {\r\n          this.fetchAllWorkflows();\r\n        });\r\n      },\r\n      error: (error: any) => {\r\n        // Close the loading dialog\r\n        this.dialogService.close();\r\n        \r\n        // Show error message\r\n        this.dialogService.error({\r\n          title: 'Error',\r\n          message: error?.message || 'Failed to delete workflow',\r\n          retryButtonText: 'OK'\r\n        });\r\n      },\r\n    });\r\n  }\r\n\r\n\r\n\r\n  private duplicateWorkflow(workflowId: string): void {\r\n    this.router.navigate(['/build/workflows/create'], {\r\n      queryParams: {\r\n        id: workflowId,\r\n        mode: WorkflowModes.duplicate,\r\n      },\r\n    });\r\n  }\r\n\r\n  onSelectionChange(data: any) {\r\n    this.selectedData = data;\r\n  }\r\n\r\n  public onPageChange(page: number): void {\r\n    this.currentPage = page;\r\n    this.fetchAllWorkflows();\r\n  }\r\n}\r\n", "<div id=\"workflows-container\" class=\"container-fluid\">\r\n  <div id=\"search-filter-container\" class=\"row g-3\">\r\n    <div class=\"col-12 col-md-8 col-lg-9 col-xl-10 search-section\">\r\n      <form [formGroup]=\"searchForm\">\r\n        <ava-textbox\r\n          placeholder='Search \"Workflows\"'\r\n          hoverEffect=\"glow\"\r\n          pressedEffect=\"solid\"\r\n          formControlName=\"search\"\r\n        >\r\n          <ava-icon\r\n            slot=\"icon-start\"\r\n            iconName=\"search\"\r\n            [iconSize]=\"16\"\r\n            iconColor=\"var(--color-brand-primary)\"\r\n          >\r\n          </ava-icon>\r\n        </ava-textbox>\r\n      </form>\r\n    </div>\r\n    <div class=\"col-12 col-md-4 col-lg-3 col-xl-2 action-buttons\">\r\n      <ava-dropdown\r\n        dropdownTitle=\"choose workflow\"\r\n        [options]=\"workflowsOptions\"\r\n        (selectionChange)=\"onSelectionChange($event)\"\r\n      >\r\n      </ava-dropdown>\r\n    </div>\r\n  </div>\r\n\r\n  <div id=\"prompts-card-container\" class=\"row g-3\">\r\n    <ava-text-card\r\n      class=\"col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3 col-xxl-2 mt-5\"\r\n      [type]=\"'create'\"\r\n      [iconName]=\"'plus'\"\r\n      iconColor=\"#144692\"\r\n      title=\"Create Workflow\"\r\n      (cardClick)=\"onCreateWorkflow()\"\r\n      [isLoading]=\"isLoading\"\r\n    >\r\n    </ava-text-card>\r\n\r\n    <!-- No Results Message -->\r\n    <div\r\n      class=\"col-12 d-flex justify-content-center align-items-center py-5\"\r\n      *ngIf=\"!isLoading && displayedWorkflows.length === 0\"\r\n    >\r\n      <div class=\"text-center\">\r\n        <h5 class=\"text-muted\">No workflow found matching your criteria</h5>\r\n      </div>\r\n    </div>\r\n\r\n    <ng-container\r\n      *ngFor=\"\r\n        let workflow of isLoading && displayedWorkflows.length === 0\r\n          ? cardSkeletonPlaceholders\r\n          : displayedWorkflows\r\n      \"\r\n    >\r\n      <ava-console-card\r\n        class=\"col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3 col-xxl-2 mt-5\"\r\n        [title]=\"workflow?.title\"\r\n        [description]=\"workflow?.description\"\r\n        categoryIcon=\"bot\"\r\n        categoryTitle=\"Workflows\"\r\n        categoryValue=\"42\"\r\n        [author]=\"workflow?.owner || 'AAVA'\"\r\n        [date]=\"workflow?.createdDate | timeAgo\"\r\n        [actions]=\"defaultActions\"\r\n        (actionClick)=\"onActionClick($event, workflow.id)\"\r\n        [skeleton]=\"isLoading\"\r\n      >\r\n      </ava-console-card>\r\n    </ng-container>\r\n  </div>\r\n\r\n  <!-- Pagination Footer -->\r\n  <div class=\"row\" *ngIf=\"filteredWorkflows.length > 0\">\r\n    <div class=\"col-12 d-flex justify-content-center mt-4\">\r\n      <app-page-footer\r\n        [totalItems]=\"totalRecords\"\r\n        [currentPage]=\"currentPage\"\r\n        [itemsPerPage]=\"itemsPerPage\"\r\n        (pageChange)=\"onPageChange($event)\"\r\n      ></app-page-footer>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAEA,SAASA,YAAY,EAAEC,QAAQ,QAAQ,iBAAiB;AACxD,SAASC,mBAAmB,QAAQ,sDAAsD;AAK1F,SAEEC,aAAa,EACbC,iBAAiB,EAEjBC,mBAAmB,EACnBC,aAAa,EACbC,iBAAiB,QACZ,wBAAwB;AAC/B,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SACEC,oBAAoB,QAEf,wDAAwD;AAC/D,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAAiCC,mBAAmB,QAAQ,gBAAgB;AAC5E,SAASC,SAAS,EAAEC,YAAY,EAAEC,oBAAoB,QAAa,MAAM;AACzE,SAASC,aAAa,QAAQ,gCAAgC;AAC9D,OAAOC,iBAAiB,MAAM,4BAA4B;;;;;;;;;;;;ICsBlDC,EALJ,CAAAC,cAAA,cAGC,cAC0B,aACA;IAAAD,EAAA,CAAAE,MAAA,+CAAwC;IAEnEF,EAFmE,CAAAG,YAAA,EAAK,EAChE,EACF;;;;;;IAENH,EAAA,CAAAI,uBAAA,GAMC;IACCJ,EAAA,CAAAC,cAAA,2BAYC;;IAFCD,EAAA,CAAAK,UAAA,yBAAAC,oFAAAC,MAAA;MAAA,MAAAC,WAAA,GAAAR,EAAA,CAAAS,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAZ,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAAeF,MAAA,CAAAG,aAAA,CAAAR,MAAA,EAAAC,WAAA,CAAAQ,EAAA,CAAkC;IAAA,EAAC;IAGpDhB,EAAA,CAAAG,YAAA,EAAmB;;;;;;IAXjBH,EAAA,CAAAiB,SAAA,EAAyB;IASzBjB,EATA,CAAAkB,UAAA,UAAAV,WAAA,kBAAAA,WAAA,CAAAW,KAAA,CAAyB,gBAAAX,WAAA,kBAAAA,WAAA,CAAAY,WAAA,CACY,YAAAZ,WAAA,kBAAAA,WAAA,CAAAa,KAAA,YAID,SAAArB,EAAA,CAAAsB,WAAA,OAAAd,WAAA,kBAAAA,WAAA,CAAAe,WAAA,EACI,YAAAX,MAAA,CAAAY,cAAA,CACd,aAAAZ,MAAA,CAAAa,SAAA,CAEJ;;;;;;IASxBzB,EAFJ,CAAAC,cAAA,cAAsD,cACG,0BAMpD;IADCD,EAAA,CAAAK,UAAA,wBAAAqB,yEAAAnB,MAAA;MAAAP,EAAA,CAAAS,aAAA,CAAAkB,GAAA;MAAA,MAAAf,MAAA,GAAAZ,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAAcF,MAAA,CAAAgB,YAAA,CAAArB,MAAA,CAAoB;IAAA,EAAC;IAGzCP,EAFK,CAAAG,YAAA,EAAkB,EACf,EACF;;;;IANAH,EAAA,CAAAiB,SAAA,GAA2B;IAE3BjB,EAFA,CAAAkB,UAAA,eAAAN,MAAA,CAAAiB,YAAA,CAA2B,gBAAAjB,MAAA,CAAAkB,WAAA,CACA,iBAAAlB,MAAA,CAAAmB,YAAA,CACE;;;ADlCrC,WAAaC,kBAAkB;EAAzB,MAAOA,kBAAkB;IAqDnBC,iBAAA;IACAC,eAAA;IACAC,MAAA;IACAC,eAAA;IACAC,QAAA;IACAC,EAAA;IACAC,YAAA;IACAC,aAAA;IA3DVhB,cAAc,GAAwB,CACpC;MACER,EAAE,EAAE,WAAW;MACfyB,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,WAAW;MAClBC,OAAO,EAAE;KACV,EACD;MACE3B,EAAE,EAAE,MAAM;MACVyB,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,WAAW;MAClBC,OAAO,EAAE;KACV,EACD;MACE3B,EAAE,EAAE,QAAQ;MACZyB,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,aAAa;MACpBC,OAAO,EAAE;KACV,EACD;MACE3B,EAAE,EAAE,KAAK;MACTyB,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE;KACZ,CACF;IACDC,YAAY,GAAU,EAAE;IACxBC,iBAAiB,GAAU,EAAE;IAC7BC,kBAAkB,GAAU,EAAE;IAC9BtB,SAAS,GAAY,KAAK;IAC1BuB,KAAK,GAAkB,IAAI;IAC3BlB,WAAW,GAAW,CAAC;IACvBC,YAAY,GAAW,EAAE;IACzBkB,UAAU,GAAW,CAAC;IACtBC,gBAAgB,GAAqB,CACnC;MAAEC,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAK,CAAE,EAC7B;MAAED,IAAI,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAO,CAAE,EAClC;MAAED,IAAI,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAO,CAAE,CACnC;IACDC,YAAY,GAAQ,IAAI;IACxBC,UAAU;IACVC,wBAAwB,GAAGC,KAAK,CAAC,EAAE,CAAC;IAC7B3B,YAAY,GAAW,EAAE;IACzB4B,uBAAuB,GAAY,KAAK;IACxCC,aAAa,GAAY,KAAK;IAC9BC,WAAW,GAAW,EAAE;IACxBC,cAAc,GAAY,KAAK;IAC/BC,MAAM,GAAG9D,iBAAiB,CAAC8D,MAAM;IACjCC,UAAU,GAAW,EAAE;IAE9BC,YACU9B,iBAAoC,EACpCC,eAAuC,EACvCC,MAAc,EACdC,eAAgC,EAChCC,QAAkB,EAClBC,EAAe,EACfC,YAAiC,EACjCC,aAA4B;MAP5B,KAAAP,iBAAiB,GAAjBA,iBAAiB;MACjB,KAAAC,eAAe,GAAfA,eAAe;MACf,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,eAAe,GAAfA,eAAe;MACf,KAAAC,QAAQ,GAARA,QAAQ;MACR,KAAAC,EAAE,GAAFA,EAAE;MACF,KAAAC,YAAY,GAAZA,YAAY;MACZ,KAAAC,aAAa,GAAbA,aAAa;MAGrB,IAAI,CAACc,UAAU,GAAG,IAAI,CAAChB,EAAE,CAAC0B,KAAK,CAAC;QAC9BC,MAAM,EAAE,CAAC,EAAE;OACZ,CAAC;IACJ;IAEAC,QAAQA,CAAA;MACN,IAAI,CAACC,UAAU,EAAE;MACjB,IAAI,CAACC,iBAAiB,EAAE;IAC1B;IAEQD,UAAUA,CAAA;MAChB,IAAI,CAACb,UAAU,CACZe,GAAG,CAAC,QAAQ,CAAE,CACdC,YAAY,CACZC,IAAI,CACH5E,SAAS,CAAC,EAAE,CAAC,EACbC,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,CACvB,CACA2E,SAAS,CAAEC,UAAkB,IAAI;QAChC,MAAMC,OAAO,GAAGD,UAAU,CAACE,IAAI,EAAE;QACjC,IAAID,OAAO,EAAE;UACX;UACA,IAAI,CAACjD,SAAS,GAAG,IAAI;UACrB,IAAI,CAACS,eAAe,CAAC0C,aAAa,CAACF,OAAO,EAAE,WAAW,EAAE,SAAS,CAAC;QACrE,CAAC,MAAM;UACL;UACA,IAAI,CAACN,iBAAiB,EAAE;QAC1B;MACF,CAAC,CAAC;MACJ,IAAI,CAAClC,eAAe,CAAC2C,cAAc,CAACL,SAAS,CAAC;QAC5CM,IAAI,EAAGC,OAAY,IAAI;UACrB,IAAI,CAACtD,SAAS,GAAG,KAAK;UAEtB;UACA,MAAMuD,SAAS,GAAGxB,KAAK,CAACyB,OAAO,CAACF,OAAO,EAAEG,eAAe,CAAC,GACrDH,OAAO,CAACG,eAAe,GACvB,EAAE;UAEN,IAAI,CAACrC,YAAY,GAAGmC,SAAS,CAACG,GAAG,CAAEC,IAAS,KAAM;YAChD,GAAGA,IAAI;YACPpE,EAAE,EAAEoE,IAAI,CAACpE,EAAE;YACXG,KAAK,EAAEiE,IAAI,CAACjC,IAAI;YAChBA,IAAI,EAAEiC,IAAI,CAACjC,IAAI;YACf/B,WAAW,EAAEgE,IAAI,CAAChE,WAAW,IAAI,gBAAgB;YACjDG,WAAW,EAAE,IAAI,CAACc,QAAQ,CAACgD,SAAS,CAACD,IAAI,CAACE,SAAS,EAAE,YAAY,CAAC,IAAI;WACvE,CAAC,CAAC;UAEH,IAAI,CAACxC,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACD,YAAY,CAAC;UAC/C,IAAI,CAAC0C,wBAAwB,EAAE,CAAC,CAAC;QACnC,CAAC;QACDvC,KAAK,EAAEA,CAAA,KAAK;UACV,IAAI,CAACA,KAAK,GAAG,gBAAgB;UAC7B,IAAI,CAACvB,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;IACJ;IAEQ2C,iBAAiBA,CAAA;MACvB,IAAI,CAAC3C,SAAS,GAAG,IAAI;MACrB,IAAI,CAACW,eAAe,CACjBoD,mBAAmB,CAAC,IAAI,CAAC1D,WAAW,EAAE,IAAI,CAACC,YAAY,EAAE,KAAK,CAAC,CAC/DyC,SAAS,CAAC;QACTM,IAAI,EAAGW,QAAa,IAAI;UACtB,IAAI,CAAC5D,YAAY,GAAG4D,QAAQ,CAACC,gBAAgB;UAC7C,MAAMC,SAAS,GAAGF,QAAQ,CAACP,eAAe,IAAIO,QAAQ;UACtD,IAAI,CAAC5C,YAAY,GAAG8C,SAAS,CAACR,GAAG,CAAEC,IAAS,KAAM;YAChD,GAAGA,IAAI;YACPpE,EAAE,EAAEoE,IAAI,CAACpE,EAAE;YACXG,KAAK,EAAEiE,IAAI,CAACjC,IAAI;YAChBA,IAAI,EAAEiC,IAAI,CAACjC,IAAI;YACf/B,WAAW,EAAEgE,IAAI,CAAChE,WAAW,IAAI,gBAAgB;YACjDG,WAAW,EACT,IAAI,CAACc,QAAQ,CAACgD,SAAS,CAACD,IAAI,CAACE,SAAS,EAAE,YAAY,CAAC,IAAI;WAC5D,CAAC,CAAC;UACH,IAAI,CAACxC,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACD,YAAY,CAAC;UAC/C,IAAI,CAACE,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACF,YAAY,CAAC;UAChD,IAAI,CAACpB,SAAS,GAAG,KAAK;QACxB,CAAC;QACDuB,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACA,KAAK,GAAGA,KAAK,CAAC4C,OAAO,IAAI,0BAA0B;UACxD,IAAI,CAACnE,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;IACN;IAEQ8D,wBAAwBA,CAAA;MAC9B,IAAI,IAAI,CAACzD,WAAW,KAAK,CAAC,EAAE;QAC1B,IAAI,CAACC,YAAY,GAAG,EAAE;MACxB,CAAC,MAAM;QACL,IAAI,CAACA,YAAY,GAAG,EAAE;MACxB;MACA,MAAM8D,gBAAgB,GAAG,IAAI,CAAC5D,iBAAiB,CAAC6D,iBAAiB,CAC/D,IAAI,CAAChD,iBAAiB,EACtB,IAAI,CAAChB,WAAW,EAChB,IAAI,CAACC,YAAY,CAClB;MACD,IAAI,CAACgB,kBAAkB,GAAG8C,gBAAgB,CAACE,cAAc;MACzD,IAAI,CAAC9C,UAAU,GAAG4C,gBAAgB,CAAC5C,UAAU;IAC/C;IAEO+C,gBAAgBA,CAAA;MACrB,IAAI,CAAC7D,MAAM,CAAC8D,QAAQ,CAAC,CAAC,yBAAyB,CAAC,CAAC;IACnD;IAEQC,YAAYA,CAACpC,UAAkB;MACrC,IAAI,CAAC3B,MAAM,CAAC8D,QAAQ,CAAC,CAAC,uBAAuB,EAAEnC,UAAU,CAAC,CAAC;IAC7D;IAEO/C,aAAaA,CAClBoF,KAAsD,EACtDrC,UAAkB;MAElB,QAAQqC,KAAK,CAACC,QAAQ;QACpB,KAAK,MAAM;UACT,IAAI,CAACF,YAAY,CAACpC,UAAU,CAAC;UAC7B;QACF,KAAK,QAAQ;UACX,IAAI,CAACuC,oBAAoB,CAACvC,UAAU,CAAC;UACrC;QACF,KAAK,WAAW;UACd,IAAI,CAACwC,iBAAiB,CAACxC,UAAU,CAAC;UAClC;QACF,KAAK,KAAK;UACR,IAAI,CAACyC,aAAa,CAACzC,UAAU,CAAC;UAC9B;QACF;UACE;MACJ;IACF;IAEAyC,aAAaA,CAACzC,UAAkB;MAC9B,IAAI,CAAC3B,MAAM,CAAC8D,QAAQ,CAAC,CAAC,0BAA0B,EAAEnC,UAAU,CAAC,CAAC;IAChE;IAEOuC,oBAAoBA,CAACvC,UAAkB;MAC5C,MAAM0C,QAAQ,GAAG,IAAI,CAAC1D,iBAAiB,CAAC2D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC1F,EAAE,KAAK8C,UAAU,CAAC;MACtE,IAAI,CAAC0C,QAAQ,EAAE;MAEf,IAAI,CAAChE,aAAa,CAACmE,YAAY,CAAC;QAC9BxF,KAAK,EAAE,iBAAiB;QACxByE,OAAO,EAAE,oCAAoCY,QAAQ,CAACrD,IAAI,kCAAkC;QAC5FyD,iBAAiB,EAAE,QAAQ;QAC3BC,gBAAgB,EAAE;OACnB,CAAC,CAACC,IAAI,CAACC,MAAM,IAAG;QACf,IAAIA,MAAM,CAACC,SAAS,EAAE;UACpB,IAAI,CAACC,cAAc,CAACnD,UAAU,CAAC;QACjC;MACF,CAAC,CAAC;IACJ;IAEQmD,cAAcA,CAACnD,UAAkB;MACvC;MACA,MAAMoD,aAAa,GAAG,IAAI,CAAC1E,aAAa,CAAC2E,OAAO,CAAC;QAC/ChG,KAAK,EAAE,mBAAmB;QAC1ByE,OAAO,EAAE,6CAA6C;QACtDwB,YAAY,EAAE;OACf,CAAC;MAEF,MAAMC,UAAU,GAAG,IAAI,CAAC9E,YAAY,CAAC+E,aAAa,EAAE,IAAI,EAAE;MAC1D,IAAI,CAAClF,eAAe,CAAC6E,cAAc,CAACnD,UAAU,EAAEuD,UAAU,CAAC,CAAC7C,SAAS,CAAC;QACpEM,IAAI,EAAGW,QAAa,IAAI;UACtB;UACA,IAAI,CAACjD,aAAa,CAAC+E,KAAK,EAAE;UAE1B;UACA,IAAI,CAAC/E,aAAa,CAACgF,MAAM,CAAC;YACxBrG,KAAK,EAAE,kBAAkB;YACzByE,OAAO,EAAEH,QAAQ,EAAEG,OAAO,IAAI,+BAA+B;YAC7DgB,iBAAiB,EAAE;WACpB,CAAC,CAACE,IAAI,CAAC,MAAK;YACX,IAAI,CAAC1C,iBAAiB,EAAE;UAC1B,CAAC,CAAC;QACJ,CAAC;QACDpB,KAAK,EAAGA,KAAU,IAAI;UACpB;UACA,IAAI,CAACR,aAAa,CAAC+E,KAAK,EAAE;UAE1B;UACA,IAAI,CAAC/E,aAAa,CAACQ,KAAK,CAAC;YACvB7B,KAAK,EAAE,OAAO;YACdyE,OAAO,EAAE5C,KAAK,EAAE4C,OAAO,IAAI,2BAA2B;YACtD6B,eAAe,EAAE;WAClB,CAAC;QACJ;OACD,CAAC;IACJ;IAIQnB,iBAAiBA,CAACxC,UAAkB;MAC1C,IAAI,CAAC3B,MAAM,CAAC8D,QAAQ,CAAC,CAAC,yBAAyB,CAAC,EAAE;QAChDyB,WAAW,EAAE;UACX1G,EAAE,EAAE8C,UAAU;UACd6D,IAAI,EAAE7H,aAAa,CAAC8H;;OAEvB,CAAC;IACJ;IAEAC,iBAAiBA,CAACC,IAAS;MACzB,IAAI,CAACzE,YAAY,GAAGyE,IAAI;IAC1B;IAEOlG,YAAYA,CAACmG,IAAY;MAC9B,IAAI,CAACjG,WAAW,GAAGiG,IAAI;MACvB,IAAI,CAAC3D,iBAAiB,EAAE;IAC1B;;uCA7QWpC,kBAAkB,EAAAhC,EAAA,CAAAgI,iBAAA,CAAAC,EAAA,CAAAC,iBAAA,GAAAlI,EAAA,CAAAgI,iBAAA,CAAAG,EAAA,CAAAC,sBAAA,GAAApI,EAAA,CAAAgI,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAtI,EAAA,CAAAgI,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAAxI,EAAA,CAAAgI,iBAAA,CAAAS,EAAA,CAAAzJ,QAAA,GAAAgB,EAAA,CAAAgI,iBAAA,CAAAU,EAAA,CAAAC,WAAA,GAAA3I,EAAA,CAAAgI,iBAAA,CAAAY,EAAA,CAAAC,mBAAA,GAAA7I,EAAA,CAAAgI,iBAAA,CAAAc,EAAA,CAAAzJ,aAAA;IAAA;;YAAlB2C,kBAAkB;MAAA+G,SAAA;MAAAC,QAAA,GAAAhJ,EAAA,CAAAiJ,kBAAA,CAJlB,CAACjK,QAAQ,EAAEK,aAAa,CAAC;MAAA6J,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCxC9BvJ,EAJR,CAAAC,cAAA,aAAsD,aACF,aACe,cAC9B,qBAM5B;UACCD,EAAA,CAAAyJ,SAAA,kBAMW;UAGjBzJ,EAFI,CAAAG,YAAA,EAAc,EACT,EACH;UAEJH,EADF,CAAAC,cAAA,aAA8D,sBAK3D;UADCD,EAAA,CAAAK,UAAA,6BAAAqJ,oEAAAnJ,MAAA;YAAA,OAAmBiJ,GAAA,CAAA3B,iBAAA,CAAAtH,MAAA,CAAyB;UAAA,EAAC;UAInDP,EAFI,CAAAG,YAAA,EAAe,EACX,EACF;UAGJH,EADF,CAAAC,cAAA,aAAiD,uBAS9C;UAFCD,EAAA,CAAAK,UAAA,uBAAAsJ,+DAAA;YAAA,OAAaH,GAAA,CAAAxD,gBAAA,EAAkB;UAAA,EAAC;UAGlChG,EAAA,CAAAG,YAAA,EAAgB;UAYhBH,EATA,CAAA4J,UAAA,KAAAC,kCAAA,kBAGC,KAAAC,2CAAA,2BAYA;UAgBH9J,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAA4J,UAAA,KAAAG,kCAAA,kBAAsD;UAUxD/J,EAAA,CAAAG,YAAA,EAAM;;;UApFMH,EAAA,CAAAiB,SAAA,GAAwB;UAAxBjB,EAAA,CAAAkB,UAAA,cAAAsI,GAAA,CAAAlG,UAAA,CAAwB;UAUxBtD,EAAA,CAAAiB,SAAA,GAAe;UAAfjB,EAAA,CAAAkB,UAAA,gBAAe;UAUnBlB,EAAA,CAAAiB,SAAA,GAA4B;UAA5BjB,EAAA,CAAAkB,UAAA,YAAAsI,GAAA,CAAAtG,gBAAA,CAA4B;UAU9BlD,EAAA,CAAAiB,SAAA,GAAiB;UAKjBjB,EALA,CAAAkB,UAAA,kBAAiB,oBACE,cAAAsI,GAAA,CAAA/H,SAAA,CAII;UAOtBzB,EAAA,CAAAiB,SAAA,EAAmD;UAAnDjB,EAAA,CAAAkB,UAAA,UAAAsI,GAAA,CAAA/H,SAAA,IAAA+H,GAAA,CAAAzG,kBAAA,CAAAiH,MAAA,OAAmD;UAS5BhK,EAAA,CAAAiB,SAAA,EAGzB;UAHyBjB,EAAA,CAAAkB,UAAA,YAAAsI,GAAA,CAAA/H,SAAA,IAAA+H,GAAA,CAAAzG,kBAAA,CAAAiH,MAAA,SAAAR,GAAA,CAAAjG,wBAAA,GAAAiG,GAAA,CAAAzG,kBAAA,CAGzB;UAoBe/C,EAAA,CAAAiB,SAAA,EAAkC;UAAlCjB,EAAA,CAAAkB,UAAA,SAAAsI,GAAA,CAAA1G,iBAAA,CAAAkH,MAAA,KAAkC;;;qBD5ClDjL,YAAY,EAAA0J,EAAA,CAAAwB,OAAA,EAAAxB,EAAA,CAAAyB,IAAA,EACZjL,mBAAmB,EACnBK,iBAAiB,EACjBF,mBAAmB,EACnBD,iBAAiB,EACjBI,mBAAmB,EACnBL,aAAa,EACbQ,mBAAmB,EAAAgJ,EAAA,CAAAyB,aAAA,EAAAzB,EAAA,CAAA0B,eAAA,EAAA1B,EAAA,CAAA2B,oBAAA,EAAA3B,EAAA,CAAA4B,kBAAA,EAAA5B,EAAA,CAAA6B,eAAA,EACnB/K,oBAAoB,EACpBC,WAAW;MAAA+K,MAAA;IAAA;;SAMFxI,kBAAkB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}