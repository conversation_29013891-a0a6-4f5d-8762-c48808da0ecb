{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport let WorkflowGraphService = /*#__PURE__*/(() => {\n  class WorkflowGraphService {\n    nodesSubject = new BehaviorSubject([]);\n    edgesSubject = new BehaviorSubject([]);\n    nodes$ = this.nodesSubject.asObservable();\n    edges$ = this.edgesSubject.asObservable();\n    constructor() {}\n    getAllNodes() {\n      return this.nodesSubject.value;\n    }\n    // Add a new node to the workflow\n    addNode(node) {\n      const currentNodes = this.nodesSubject.getValue();\n      this.nodesSubject.next([...currentNodes, node]);\n    }\n    // Remove a node from the workflow\n    removeNode(nodeId) {\n      const currentNodes = this.nodesSubject.getValue();\n      const currentEdges = this.edgesSubject.getValue();\n      // Remove the node\n      const updatedNodes = currentNodes.filter(node => node.id !== nodeId);\n      // Remove any edges connected to this node\n      const updatedEdges = currentEdges.filter(edge => edge.source !== nodeId && edge.target !== nodeId);\n      this.nodesSubject.next(updatedNodes);\n      this.edgesSubject.next(updatedEdges);\n    }\n    // Add a new edge connecting two nodes\n    addEdge(edge) {\n      const currentEdges = this.edgesSubject.getValue();\n      this.edgesSubject.next([...currentEdges, edge]);\n    }\n    // Remove an edge from the workflow\n    removeEdge(edgeId) {\n      const currentEdges = this.edgesSubject.getValue();\n      const updatedEdges = currentEdges.filter(edge => edge.id !== edgeId);\n      this.edgesSubject.next(updatedEdges);\n    }\n    // Update node positions after drag\n    updateNodePositions(updatedNodes) {\n      this.nodesSubject.next(updatedNodes);\n    }\n    // Clear the entire workflow\n    clearWorkflow() {\n      this.nodesSubject.next([]);\n      this.edgesSubject.next([]);\n    }\n    // Load a saved workflow\n    loadWorkflow(nodes, edges) {\n      this.nodesSubject.next(nodes);\n      this.edgesSubject.next(edges);\n    }\n    // Get the current state of the workflow\n    getWorkflowState() {\n      return {\n        nodes: this.nodesSubject.getValue(),\n        edges: this.edgesSubject.getValue()\n      };\n    }\n    // Generate a unique node ID\n    generateNodeId() {\n      return `node_${Date.now()}_${Math.floor(Math.random() * 1000)}`;\n    }\n    // Generate a unique edge ID\n    generateEdgeId(source, target) {\n      return `edge_${source}_${target}_${Math.floor(Math.random() * 1000)}`;\n    }\n    // Set nodes (for undo/redo functionality)\n    setNodes(nodes) {\n      this.nodesSubject.next([...nodes]);\n    }\n    // Set edges (for undo/redo functionality)\n    setEdges(edges) {\n      this.edgesSubject.next([...edges]);\n    }\n    getAllEdges() {\n      return this.edgesSubject.value;\n    }\n    static ɵfac = function WorkflowGraphService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || WorkflowGraphService)();\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: WorkflowGraphService,\n      factory: WorkflowGraphService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return WorkflowGraphService;\n})();", "map": {"version": 3, "names": ["BehaviorSubject", "WorkflowGraphService", "nodesSubject", "edgesSubject", "nodes$", "asObservable", "edges$", "constructor", "getAllNodes", "value", "addNode", "node", "currentNodes", "getValue", "next", "removeNode", "nodeId", "currentEdges", "updatedNodes", "filter", "id", "updatedEdges", "edge", "source", "target", "addEdge", "removeEdge", "edgeId", "updateNodePositions", "clearWorkflow", "loadWorkflow", "nodes", "edges", "getWorkflowState", "generateNodeId", "Date", "now", "Math", "floor", "random", "generateEdgeId", "setNodes", "set<PERSON><PERSON>", "getAllEdges", "factory", "ɵfac", "providedIn"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\workflows\\workflow-editor\\services\\workflow-graph.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\n\r\n// Node and Edge interfaces for the workflow graph\r\nexport interface WorkflowNode {\r\n  id: string;\r\n  type: string;\r\n  data: {\r\n    label: string;\r\n    agentId?: string;\r\n    agentName?: string;\r\n    agentDescription?: string;\r\n    width?: number;\r\n    [key: string]: any;\r\n  };\r\n  position: {\r\n    x: number;\r\n    y: number;\r\n  };\r\n}\r\n\r\nexport interface WorkflowEdge {\r\n  id: string;\r\n  source: string;\r\n  target: string;\r\n  type?: string;\r\n  animated?: boolean;\r\n  label?: string;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class WorkflowGraphService {\r\n  private nodesSubject = new BehaviorSubject<WorkflowNode[]>([]);\r\n  private edgesSubject = new BehaviorSubject<WorkflowEdge[]>([]);\r\n\r\n  nodes$: Observable<WorkflowNode[]> = this.nodesSubject.asObservable();\r\n  edges$: Observable<WorkflowEdge[]> = this.edgesSubject.asObservable();\r\n\r\n  constructor() { }\r\n\r\n  getAllNodes(){\r\n    return this.nodesSubject.value\r\n  }\r\n\r\n  // Add a new node to the workflow\r\n  addNode(node: WorkflowNode): void {\r\n    const currentNodes = this.nodesSubject.getValue();\r\n    this.nodesSubject.next([...currentNodes, node]);\r\n  }\r\n\r\n  // Remove a node from the workflow\r\n  removeNode(nodeId: string): void {\r\n    const currentNodes = this.nodesSubject.getValue();\r\n    const currentEdges = this.edgesSubject.getValue();\r\n\r\n    // Remove the node\r\n    const updatedNodes = currentNodes.filter(node => node.id !== nodeId);\r\n\r\n    // Remove any edges connected to this node\r\n    const updatedEdges = currentEdges.filter(\r\n      edge => edge.source !== nodeId && edge.target !== nodeId\r\n    );\r\n\r\n    this.nodesSubject.next(updatedNodes);\r\n    this.edgesSubject.next(updatedEdges);\r\n  }\r\n\r\n  // Add a new edge connecting two nodes\r\n  addEdge(edge: WorkflowEdge): void {\r\n    const currentEdges = this.edgesSubject.getValue();\r\n    this.edgesSubject.next([...currentEdges, edge]);\r\n  }\r\n\r\n  // Remove an edge from the workflow\r\n  removeEdge(edgeId: string): void {\r\n    const currentEdges = this.edgesSubject.getValue();\r\n    const updatedEdges = currentEdges.filter(edge => edge.id !== edgeId);\r\n    this.edgesSubject.next(updatedEdges);\r\n  }\r\n\r\n  // Update node positions after drag\r\n  updateNodePositions(updatedNodes: WorkflowNode[]): void {\r\n    this.nodesSubject.next(updatedNodes);\r\n  }\r\n\r\n  // Clear the entire workflow\r\n  clearWorkflow(): void {\r\n    this.nodesSubject.next([]);\r\n    this.edgesSubject.next([]);\r\n  }\r\n\r\n  // Load a saved workflow\r\n  loadWorkflow(nodes: WorkflowNode[], edges: WorkflowEdge[]): void {\r\n    this.nodesSubject.next(nodes);\r\n    this.edgesSubject.next(edges);\r\n  }\r\n\r\n  // Get the current state of the workflow\r\n  getWorkflowState(): { nodes: WorkflowNode[], edges: WorkflowEdge[] } {\r\n    return {\r\n      nodes: this.nodesSubject.getValue(),\r\n      edges: this.edgesSubject.getValue()\r\n    };\r\n  }\r\n\r\n  // Generate a unique node ID\r\n  generateNodeId(): string {\r\n    return `node_${Date.now()}_${Math.floor(Math.random() * 1000)}`;\r\n  }\r\n\r\n  // Generate a unique edge ID\r\n  generateEdgeId(source: string, target: string): string {\r\n    return `edge_${source}_${target}_${Math.floor(Math.random() * 1000)}`;\r\n  }\r\n\r\n  // Set nodes (for undo/redo functionality)\r\n  setNodes(nodes: WorkflowNode[]): void {\r\n    this.nodesSubject.next([...nodes]);\r\n  }\r\n\r\n  // Set edges (for undo/redo functionality)\r\n  setEdges(edges: WorkflowEdge[]): void {\r\n    this.edgesSubject.next([...edges]);\r\n  }\r\n\r\n  getAllEdges(){\r\n    return this.edgesSubject.value\r\n  }\r\n  \r\n}\r\n"], "mappings": "AACA,SAASA,eAAe,QAAoB,MAAM;;AAgClD,WAAaC,oBAAoB;EAA3B,MAAOA,oBAAoB;IACvBC,YAAY,GAAG,IAAIF,eAAe,CAAiB,EAAE,CAAC;IACtDG,YAAY,GAAG,IAAIH,eAAe,CAAiB,EAAE,CAAC;IAE9DI,MAAM,GAA+B,IAAI,CAACF,YAAY,CAACG,YAAY,EAAE;IACrEC,MAAM,GAA+B,IAAI,CAACH,YAAY,CAACE,YAAY,EAAE;IAErEE,YAAA,GAAgB;IAEhBC,WAAWA,CAAA;MACT,OAAO,IAAI,CAACN,YAAY,CAACO,KAAK;IAChC;IAEA;IACAC,OAAOA,CAACC,IAAkB;MACxB,MAAMC,YAAY,GAAG,IAAI,CAACV,YAAY,CAACW,QAAQ,EAAE;MACjD,IAAI,CAACX,YAAY,CAACY,IAAI,CAAC,CAAC,GAAGF,YAAY,EAAED,IAAI,CAAC,CAAC;IACjD;IAEA;IACAI,UAAUA,CAACC,MAAc;MACvB,MAAMJ,YAAY,GAAG,IAAI,CAACV,YAAY,CAACW,QAAQ,EAAE;MACjD,MAAMI,YAAY,GAAG,IAAI,CAACd,YAAY,CAACU,QAAQ,EAAE;MAEjD;MACA,MAAMK,YAAY,GAAGN,YAAY,CAACO,MAAM,CAACR,IAAI,IAAIA,IAAI,CAACS,EAAE,KAAKJ,MAAM,CAAC;MAEpE;MACA,MAAMK,YAAY,GAAGJ,YAAY,CAACE,MAAM,CACtCG,IAAI,IAAIA,IAAI,CAACC,MAAM,KAAKP,MAAM,IAAIM,IAAI,CAACE,MAAM,KAAKR,MAAM,CACzD;MAED,IAAI,CAACd,YAAY,CAACY,IAAI,CAACI,YAAY,CAAC;MACpC,IAAI,CAACf,YAAY,CAACW,IAAI,CAACO,YAAY,CAAC;IACtC;IAEA;IACAI,OAAOA,CAACH,IAAkB;MACxB,MAAML,YAAY,GAAG,IAAI,CAACd,YAAY,CAACU,QAAQ,EAAE;MACjD,IAAI,CAACV,YAAY,CAACW,IAAI,CAAC,CAAC,GAAGG,YAAY,EAAEK,IAAI,CAAC,CAAC;IACjD;IAEA;IACAI,UAAUA,CAACC,MAAc;MACvB,MAAMV,YAAY,GAAG,IAAI,CAACd,YAAY,CAACU,QAAQ,EAAE;MACjD,MAAMQ,YAAY,GAAGJ,YAAY,CAACE,MAAM,CAACG,IAAI,IAAIA,IAAI,CAACF,EAAE,KAAKO,MAAM,CAAC;MACpE,IAAI,CAACxB,YAAY,CAACW,IAAI,CAACO,YAAY,CAAC;IACtC;IAEA;IACAO,mBAAmBA,CAACV,YAA4B;MAC9C,IAAI,CAAChB,YAAY,CAACY,IAAI,CAACI,YAAY,CAAC;IACtC;IAEA;IACAW,aAAaA,CAAA;MACX,IAAI,CAAC3B,YAAY,CAACY,IAAI,CAAC,EAAE,CAAC;MAC1B,IAAI,CAACX,YAAY,CAACW,IAAI,CAAC,EAAE,CAAC;IAC5B;IAEA;IACAgB,YAAYA,CAACC,KAAqB,EAAEC,KAAqB;MACvD,IAAI,CAAC9B,YAAY,CAACY,IAAI,CAACiB,KAAK,CAAC;MAC7B,IAAI,CAAC5B,YAAY,CAACW,IAAI,CAACkB,KAAK,CAAC;IAC/B;IAEA;IACAC,gBAAgBA,CAAA;MACd,OAAO;QACLF,KAAK,EAAE,IAAI,CAAC7B,YAAY,CAACW,QAAQ,EAAE;QACnCmB,KAAK,EAAE,IAAI,CAAC7B,YAAY,CAACU,QAAQ;OAClC;IACH;IAEA;IACAqB,cAAcA,CAAA;MACZ,OAAO,QAAQC,IAAI,CAACC,GAAG,EAAE,IAAIC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE;IACjE;IAEA;IACAC,cAAcA,CAACjB,MAAc,EAAEC,MAAc;MAC3C,OAAO,QAAQD,MAAM,IAAIC,MAAM,IAAIa,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE;IACvE;IAEA;IACAE,QAAQA,CAACV,KAAqB;MAC5B,IAAI,CAAC7B,YAAY,CAACY,IAAI,CAAC,CAAC,GAAGiB,KAAK,CAAC,CAAC;IACpC;IAEA;IACAW,QAAQA,CAACV,KAAqB;MAC5B,IAAI,CAAC7B,YAAY,CAACW,IAAI,CAAC,CAAC,GAAGkB,KAAK,CAAC,CAAC;IACpC;IAEAW,WAAWA,CAAA;MACT,OAAO,IAAI,CAACxC,YAAY,CAACM,KAAK;IAChC;;uCAhGWR,oBAAoB;IAAA;;aAApBA,oBAAoB;MAAA2C,OAAA,EAApB3C,oBAAoB,CAAA4C,IAAA;MAAAC,UAAA,EAFnB;IAAM;;SAEP7C,oBAAoB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}