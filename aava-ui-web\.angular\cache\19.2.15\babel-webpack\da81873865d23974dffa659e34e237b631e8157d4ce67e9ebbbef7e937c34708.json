{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { IconComponent } from '@ava/play-comp-library';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nfunction SidebarComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_div_5_Template_div_click_0_listener() {\n      const item_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onItemClick(item_r2));\n    });\n    i0.ɵɵelementStart(1, \"ava-icon\", 11);\n    i0.ɵɵlistener(\"userClick\", function SidebarComponent_div_5_Template_ava_icon_userClick_1_listener() {\n      const item_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onItemClick(item_r2));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    i0.ɵɵclassProp(\"active\", item_r2.isActive);\n    i0.ɵɵattribute(\"aria-label\", item_r2.label);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"iconName\", item_r2.iconName)(\"iconSize\", 20)(\"iconColor\", \"#666D99\")(\"cursor\", true);\n  }\n}\nexport let SidebarComponent = /*#__PURE__*/(() => {\n  class SidebarComponent {\n    router;\n    constructor(router) {\n      this.router = router;\n    }\n    sidebarItems = [{\n      iconName: 'bot',\n      label: 'Dashboard',\n      isActive: false,\n      route: '/dashboard'\n    }, {\n      iconName: 'wrench',\n      label: 'Tools',\n      isActive: false,\n      route: '/tools'\n    }, {\n      iconName: 'workflow',\n      label: 'Analytics',\n      isActive: false,\n      route: '/analytics'\n    }, {\n      iconName: 'shield-check',\n      label: 'Team',\n      isActive: false,\n      route: '/team'\n    }, {\n      iconName: 'book-text',\n      label: 'Documents',\n      isActive: false,\n      route: '/documents'\n    }];\n    onItemClick(item) {\n      // Reset all items to inactive\n      this.sidebarItems.forEach(sidebarItem => sidebarItem.isActive = false);\n      // Set clicked item as active\n      item.isActive = true;\n      // Add navigation logic here\n      console.log(`Navigating to: ${item.label} - Route: ${item.route}`);\n      // You can add router navigation here:\n      // this.router.navigate([item.route]);\n    }\n    onAccountClick() {\n      console.log('Account icon clicked - navigating to my-agent-home');\n      this.router.navigate(['/my-agent-home']);\n    }\n    onLogoClick() {\n      console.log('Logo clicked - navigate to home');\n    }\n    static ɵfac = function SidebarComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SidebarComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SidebarComponent,\n      selectors: [[\"app-sidebar\"]],\n      decls: 10,\n      vars: 3,\n      consts: [[1, \"sidebar-container\"], [1, \"sidebar-top\"], [1, \"logo-container\", 3, \"click\"], [\"src\", \"assets/icons/Aava.svg\", \"alt\", \"Aava Logo\", 1, \"aava-logo\"], [1, \"sidebar-nav\"], [\"class\", \"nav-item\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"sidebar-divider\"], [1, \"sidebar-bottom\"], [1, \"account-icon\", 3, \"click\"], [\"iconName\", \"user\", \"iconColor\", \"#FFFFFF\", 3, \"userClick\", \"iconSize\", \"cursor\"], [1, \"nav-item\", 3, \"click\"], [3, \"userClick\", \"iconName\", \"iconSize\", \"iconColor\", \"cursor\"]],\n      template: function SidebarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵlistener(\"click\", function SidebarComponent_Template_div_click_2_listener() {\n            return ctx.onLogoClick();\n          });\n          i0.ɵɵelement(3, \"img\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4);\n          i0.ɵɵtemplate(5, SidebarComponent_div_5_Template, 2, 7, \"div\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(6, \"div\", 6);\n          i0.ɵɵelementStart(7, \"div\", 7)(8, \"div\", 8);\n          i0.ɵɵlistener(\"click\", function SidebarComponent_Template_div_click_8_listener() {\n            return ctx.onAccountClick();\n          });\n          i0.ɵɵelementStart(9, \"ava-icon\", 9);\n          i0.ɵɵlistener(\"userClick\", function SidebarComponent_Template_ava_icon_userClick_9_listener() {\n            return ctx.onAccountClick();\n          });\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.sidebarItems);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"iconSize\", 20)(\"cursor\", true);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, IconComponent],\n      styles: [\".sidebar-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  width: 80px;\\n  height: 947px;\\n  padding: 32px 10px 32px 10px;\\n  flex-direction: column;\\n  justify-content: flex-start;\\n  align-items: center;\\n  flex-shrink: 0;\\n  border-radius: 24px;\\n  border: 2px solid #FFF;\\n  background: linear-gradient(0deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.3) 100%), linear-gradient(114deg, rgba(240, 235, 248, 0.5) 1.5%, rgba(255, 255, 255, 0.5) 45.85%, rgba(245, 233, 247, 0.5) 98.86%);\\n  box-shadow: 0px 2px 2px -3px #F0F1F2, 0px 0px 6px -2px #D1D3D8;\\n  -webkit-backdrop-filter: blur(48px);\\n          backdrop-filter: blur(48px);\\n}\\n\\n.sidebar-top[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 48px;\\n}\\n\\n.logo-container[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  cursor: pointer;\\n}\\n\\n.aava-logo[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  object-fit: contain;\\n}\\n\\n.sidebar-nav[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 24px;\\n  align-items: center;\\n}\\n\\n.nav-item[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  border-radius: 12px;\\n  cursor: pointer;\\n  position: relative;\\n}\\n.nav-item[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(167, 123, 243, 0.1);\\n}\\n.nav-item.active[_ngcontent-%COMP%] {\\n  background-color: rgba(167, 123, 243, 0.2);\\n}\\n.nav-item[_ngcontent-%COMP%]   ava-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n.nav-item[_ngcontent-%COMP%]   ava-icon[_ngcontent-%COMP%]     .ava-icon-container {\\n  background: transparent;\\n  border: none;\\n  padding: 0;\\n  width: 20px;\\n  height: 20px;\\n}\\n.nav-item[_ngcontent-%COMP%]   ava-icon[_ngcontent-%COMP%]     .ava-icon-container lucide-icon {\\n  width: 20px !important;\\n  height: 20px !important;\\n}\\n\\n.sidebar-divider[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 1px;\\n  background-color: rgba(167, 123, 243, 0.2);\\n  margin: auto 0;\\n}\\n\\n.sidebar-bottom[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  margin-top: auto;\\n}\\n\\n.account-icon[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  cursor: pointer;\\n  background-color: #A77BF3;\\n}\\n.account-icon[_ngcontent-%COMP%]:hover {\\n  background-color: #31195a;\\n}\\n.account-icon[_ngcontent-%COMP%]   ava-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n.account-icon[_ngcontent-%COMP%]   ava-icon[_ngcontent-%COMP%]     .ava-icon-container {\\n  background: transparent;\\n  border: none;\\n  padding: 0;\\n  width: 20px;\\n  height: 20px;\\n}\\n.account-icon[_ngcontent-%COMP%]   ava-icon[_ngcontent-%COMP%]     .ava-icon-container lucide-icon {\\n  width: 20px !important;\\n  height: 20px !important;\\n}\\n\\n@media (max-width: 768px) {\\n  .sidebar-container[_ngcontent-%COMP%] {\\n    width: 60px;\\n    height: auto;\\n    min-height: 400px;\\n    padding: 20px 8px;\\n    gap: 32px;\\n    left: 10px;\\n  }\\n  .logo-container[_ngcontent-%COMP%] {\\n    width: 36px;\\n    height: 36px;\\n  }\\n  .aava-logo[_ngcontent-%COMP%] {\\n    width: 36px;\\n    height: 36px;\\n  }\\n  .nav-item[_ngcontent-%COMP%] {\\n    width: 36px;\\n    height: 36px;\\n  }\\n  .nav-item[_ngcontent-%COMP%]   ava-icon[_ngcontent-%COMP%]     .ava-icon-container lucide-icon {\\n    width: 18px !important;\\n    height: 18px !important;\\n  }\\n  .account-icon[_ngcontent-%COMP%] {\\n    width: 36px;\\n    height: 36px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .sidebar-container[_ngcontent-%COMP%] {\\n    width: 50px;\\n    padding: 16px 6px;\\n    gap: 24px;\\n    left: 5px;\\n  }\\n  .logo-container[_ngcontent-%COMP%] {\\n    width: 32px;\\n    height: 32px;\\n  }\\n  .aava-logo[_ngcontent-%COMP%] {\\n    width: 32px;\\n    height: 32px;\\n  }\\n  .nav-item[_ngcontent-%COMP%] {\\n    width: 32px;\\n    height: 32px;\\n  }\\n  .nav-item[_ngcontent-%COMP%]   ava-icon[_ngcontent-%COMP%]     .ava-icon-container lucide-icon {\\n    width: 16px !important;\\n    height: 16px !important;\\n  }\\n  .account-icon[_ngcontent-%COMP%] {\\n    width: 32px;\\n    height: 32px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3Byb2plY3RzL2VsZGVyLXdhbmQvc3JjL2FwcC9zaGFyZWQvY29tcG9uZW50cy9zaWRlYmFyL3NpZGViYXIuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFFRSxhQUFBO0VBQ0EsV0FBQTtFQUNBLGFBQUE7RUFDQSw0QkFBQTtFQUNBLHNCQUFBO0VBQ0EsMkJBQUE7RUFDQSxtQkFBQTtFQUNBLGNBQUE7RUFHQSxtQkFBQTtFQUNBLHNCQUFBO0VBQ0EsdU5BQUE7RUFJQSw4REFBQTtFQUVBLG1DQUFBO1VBQUEsMkJBQUE7QUFORjs7QUFTQTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQUFBO0VBQ0EsU0FBQTtBQU5GOztBQVNBO0VBRUUsV0FBQTtFQUNBLFlBQUE7RUFDQSxhQUFBO0VBQ0EsdUJBQUE7RUFDQSxtQkFBQTtFQUNBLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSxlQUFBO0FBUEY7O0FBVUE7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLG1CQUFBO0FBUEY7O0FBVUE7RUFDRSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxTQUFBO0VBQ0EsbUJBQUE7QUFQRjs7QUFVQTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0EsYUFBQTtFQUNBLHVCQUFBO0VBQ0EsbUJBQUE7RUFDQSxtQkFBQTtFQUNBLGVBQUE7RUFDQSxrQkFBQTtBQVBGO0FBVUU7RUFDRSwwQ0FBQTtBQVJKO0FBWUU7RUFDRSwwQ0FBQTtBQVZKO0FBY0U7RUFDRSxhQUFBO0VBQ0EsdUJBQUE7RUFDQSxtQkFBQTtBQVpKO0FBY0k7RUFDRSx1QkFBQTtFQUNBLFlBQUE7RUFDQSxVQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7QUFaTjtBQWNNO0VBQ0Usc0JBQUE7RUFDQSx1QkFBQTtBQVpSOztBQW1CQTtFQUNFLFdBQUE7RUFDQSxXQUFBO0VBQ0EsMENBQUE7RUFDQSxjQUFBO0FBaEJGOztBQW1CQTtFQUNFLGFBQUE7RUFDQSx1QkFBQTtFQUNBLG1CQUFBO0VBQ0EsZ0JBQUE7QUFoQkY7O0FBbUJBO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtFQUNBLGFBQUE7RUFDQSx1QkFBQTtFQUNBLG1CQUFBO0VBQ0EsZUFBQTtFQUNBLHlCQUFBO0FBaEJGO0FBbUJFO0VBQ0UseUJBQUE7QUFqQko7QUFvQkU7RUFDRSxhQUFBO0VBQ0EsdUJBQUE7RUFDQSxtQkFBQTtBQWxCSjtBQW9CSTtFQUNFLHVCQUFBO0VBQ0EsWUFBQTtFQUNBLFVBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtBQWxCTjtBQW9CTTtFQUNFLHNCQUFBO0VBQ0EsdUJBQUE7QUFsQlI7O0FBMkJBO0VBQ0U7SUFDRSxXQUFBO0lBQ0EsWUFBQTtJQUNBLGlCQUFBO0lBQ0EsaUJBQUE7SUFDQSxTQUFBO0lBQ0EsVUFBQTtFQXhCRjtFQTJCQTtJQUNFLFdBQUE7SUFDQSxZQUFBO0VBekJGO0VBNEJBO0lBQ0UsV0FBQTtJQUNBLFlBQUE7RUExQkY7RUE2QkE7SUFDRSxXQUFBO0lBQ0EsWUFBQTtFQTNCRjtFQTZCRTtJQUNFLHNCQUFBO0lBQ0EsdUJBQUE7RUEzQko7RUErQkE7SUFDRSxXQUFBO0lBQ0EsWUFBQTtFQTdCRjtBQUNGO0FBZ0NBO0VBQ0U7SUFDRSxXQUFBO0lBQ0EsaUJBQUE7SUFDQSxTQUFBO0lBQ0EsU0FBQTtFQTlCRjtFQWlDQTtJQUNFLFdBQUE7SUFDQSxZQUFBO0VBL0JGO0VBa0NBO0lBQ0UsV0FBQTtJQUNBLFlBQUE7RUFoQ0Y7RUFtQ0E7SUFDRSxXQUFBO0lBQ0EsWUFBQTtFQWpDRjtFQW1DRTtJQUNFLHNCQUFBO0lBQ0EsdUJBQUE7RUFqQ0o7RUFxQ0E7SUFDRSxXQUFBO0lBQ0EsWUFBQTtFQW5DRjtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiLnNpZGViYXItY29udGFpbmVyIHtcclxuICAvLyBFeGFjdCBsYXlvdXQgZnJvbSByZXF1aXJlbWVudHNcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIHdpZHRoOiA4MHB4O1xyXG4gIGhlaWdodDogOTQ3cHg7XHJcbiAgcGFkZGluZzogMzJweCAxMHB4IDMycHggMTBweDsgLy8gTW9yZSBwYWRkaW5nIGF0IGJvdHRvbVxyXG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAganVzdGlmeS1jb250ZW50OiBmbGV4LXN0YXJ0OyAvLyBDaGFuZ2VkIHRvIGZsZXgtc3RhcnQgZm9yIG1hbnVhbCBzcGFjaW5nXHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBmbGV4LXNocmluazogMDtcclxuXHJcbiAgLy8gRXhhY3Qgc3R5bGluZyBmcm9tIHJlcXVpcmVtZW50c1xyXG4gIGJvcmRlci1yYWRpdXM6IDI0cHg7XHJcbiAgYm9yZGVyOiAycHggc29saWQgI0ZGRjtcclxuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMGRlZywgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjMwKSAwJSwgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjMwKSAxMDAlKSxcclxuICAgIGxpbmVhci1ncmFkaWVudCgxMTRkZWcsIHJnYmEoMjQwLCAyMzUsIDI0OCwgMC41MCkgMS41JSxcclxuICAgICAgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjUwKSA0NS44NSUsXHJcbiAgICAgIHJnYmEoMjQ1LCAyMzMsIDI0NywgMC41MCkgOTguODYlKTtcclxuICBib3gtc2hhZG93OiAwcHggMnB4IDJweCAtM3B4ICNGMEYxRjIsXHJcbiAgICAwcHggMHB4IDZweCAtMnB4ICNEMUQzRDg7XHJcbiAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDQ4cHgpO1xyXG59XHJcblxyXG4uc2lkZWJhci10b3Age1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIGdhcDogNDhweDsgLy8gR2FwIGJldHdlZW4gbG9nbyBhbmQgbmF2aWdhdGlvbiBpY29uc1xyXG59XHJcblxyXG4ubG9nby1jb250YWluZXIge1xyXG4gIC8vIEZpcnN0IGltYWdlIGZyb20gYXNzZXRzIHdpdGggNDhweCBkaW1lbnNpb25zIGFzIHBlciByZXF1aXJlbWVudHNcclxuICB3aWR0aDogNDhweDtcclxuICBoZWlnaHQ6IDQ4cHg7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIGJvcmRlci1yYWRpdXM6IDEycHg7XHJcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuICBjdXJzb3I6IHBvaW50ZXI7XHJcbn1cclxuXHJcbi5hYXZhLWxvZ28ge1xyXG4gIHdpZHRoOiA0OHB4O1xyXG4gIGhlaWdodDogNDhweDtcclxuICBvYmplY3QtZml0OiBjb250YWluO1xyXG59XHJcblxyXG4uc2lkZWJhci1uYXYge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICBnYXA6IDI0cHg7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxufVxyXG5cclxuLm5hdi1pdGVtIHtcclxuICB3aWR0aDogNDBweDtcclxuICBoZWlnaHQ6IDQwcHg7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIGJvcmRlci1yYWRpdXM6IDEycHg7XHJcbiAgY3Vyc29yOiBwb2ludGVyO1xyXG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuXHJcbiAgLy8gU2ltcGxlIGhvdmVyIGVmZmVjdCAtIGxpZ2h0IHB1cnBsZSBiYWNrZ3JvdW5kXHJcbiAgJjpob3ZlciB7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDE2NywgMTIzLCAyNDMsIDAuMSk7XHJcbiAgfVxyXG5cclxuICAvLyBBY3RpdmUgc3RhdGUgd2l0aCBwdXJwbGUgYmFja2dyb3VuZFxyXG4gICYuYWN0aXZlIHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMTY3LCAxMjMsIDI0MywgMC4yKTtcclxuICB9XHJcblxyXG4gIC8vIEljb24gc2l6ZSAyMHB4IGFzIHBlciByZXF1aXJlbWVudHNcclxuICBhdmEtaWNvbiB7XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG5cclxuICAgIDo6bmctZGVlcCAuYXZhLWljb24tY29udGFpbmVyIHtcclxuICAgICAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7XHJcbiAgICAgIGJvcmRlcjogbm9uZTtcclxuICAgICAgcGFkZGluZzogMDtcclxuICAgICAgd2lkdGg6IDIwcHg7XHJcbiAgICAgIGhlaWdodDogMjBweDtcclxuXHJcbiAgICAgIGx1Y2lkZS1pY29uIHtcclxuICAgICAgICB3aWR0aDogMjBweCAhaW1wb3J0YW50O1xyXG4gICAgICAgIGhlaWdodDogMjBweCAhaW1wb3J0YW50O1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4vLyBEaXZpZGVyIGJldHdlZW4gbmF2aWdhdGlvbiBhbmQgYWNjb3VudCBpY29uXHJcbi5zaWRlYmFyLWRpdmlkZXIge1xyXG4gIHdpZHRoOiA0MHB4O1xyXG4gIGhlaWdodDogMXB4O1xyXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMTY3LCAxMjMsIDI0MywgMC4yKTtcclxuICBtYXJnaW46IGF1dG8gMDsgLy8gVGhpcyB3aWxsIHB1c2ggdGhlIGFjY291bnQgaWNvbiB0byB0aGUgYm90dG9tXHJcbn1cclxuXHJcbi5zaWRlYmFyLWJvdHRvbSB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIG1hcmdpbi10b3A6IGF1dG87IC8vIFB1c2ggdG8gYm90dG9tXHJcbn1cclxuXHJcbi5hY2NvdW50LWljb24ge1xyXG4gIHdpZHRoOiA0MHB4O1xyXG4gIGhlaWdodDogNDBweDtcclxuICBib3JkZXItcmFkaXVzOiA1MCU7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIGN1cnNvcjogcG9pbnRlcjtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjQTc3QkYzOyAvLyBQdXJwbGUgYmFja2dyb3VuZCBmb3IgYWNjb3VudCBpY29uXHJcblxyXG4gIC8vIEJsYWNrIGJhY2tncm91bmQgb24gaG92ZXIgZm9yIGFjY291bnQgaWNvblxyXG4gICY6aG92ZXIge1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogIzMxMTk1YTtcclxuICB9XHJcblxyXG4gIGF2YS1pY29uIHtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcblxyXG4gICAgOjpuZy1kZWVwIC5hdmEtaWNvbi1jb250YWluZXIge1xyXG4gICAgICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDtcclxuICAgICAgYm9yZGVyOiBub25lO1xyXG4gICAgICBwYWRkaW5nOiAwO1xyXG4gICAgICB3aWR0aDogMjBweDtcclxuICAgICAgaGVpZ2h0OiAyMHB4O1xyXG5cclxuICAgICAgbHVjaWRlLWljb24ge1xyXG4gICAgICAgIHdpZHRoOiAyMHB4ICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgaGVpZ2h0OiAyMHB4ICFpbXBvcnRhbnQ7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcblxyXG5cclxuLy8gUmVzcG9uc2l2ZSBhZGp1c3RtZW50c1xyXG5AbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcclxuICAuc2lkZWJhci1jb250YWluZXIge1xyXG4gICAgd2lkdGg6IDYwcHg7XHJcbiAgICBoZWlnaHQ6IGF1dG87XHJcbiAgICBtaW4taGVpZ2h0OiA0MDBweDtcclxuICAgIHBhZGRpbmc6IDIwcHggOHB4O1xyXG4gICAgZ2FwOiAzMnB4O1xyXG4gICAgbGVmdDogMTBweDtcclxuICB9XHJcblxyXG4gIC5sb2dvLWNvbnRhaW5lciB7XHJcbiAgICB3aWR0aDogMzZweDtcclxuICAgIGhlaWdodDogMzZweDtcclxuICB9XHJcblxyXG4gIC5hYXZhLWxvZ28ge1xyXG4gICAgd2lkdGg6IDM2cHg7XHJcbiAgICBoZWlnaHQ6IDM2cHg7XHJcbiAgfVxyXG5cclxuICAubmF2LWl0ZW0ge1xyXG4gICAgd2lkdGg6IDM2cHg7XHJcbiAgICBoZWlnaHQ6IDM2cHg7XHJcblxyXG4gICAgYXZhLWljb24gOjpuZy1kZWVwIC5hdmEtaWNvbi1jb250YWluZXIgbHVjaWRlLWljb24ge1xyXG4gICAgICB3aWR0aDogMThweCAhaW1wb3J0YW50O1xyXG4gICAgICBoZWlnaHQ6IDE4cHggIWltcG9ydGFudDtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC5hY2NvdW50LWljb24ge1xyXG4gICAgd2lkdGg6IDM2cHg7XHJcbiAgICBoZWlnaHQ6IDM2cHg7XHJcbiAgfVxyXG59XHJcblxyXG5AbWVkaWEgKG1heC13aWR0aDogNDgwcHgpIHtcclxuICAuc2lkZWJhci1jb250YWluZXIge1xyXG4gICAgd2lkdGg6IDUwcHg7XHJcbiAgICBwYWRkaW5nOiAxNnB4IDZweDtcclxuICAgIGdhcDogMjRweDtcclxuICAgIGxlZnQ6IDVweDtcclxuICB9XHJcblxyXG4gIC5sb2dvLWNvbnRhaW5lciB7XHJcbiAgICB3aWR0aDogMzJweDtcclxuICAgIGhlaWdodDogMzJweDtcclxuICB9XHJcblxyXG4gIC5hYXZhLWxvZ28ge1xyXG4gICAgd2lkdGg6IDMycHg7XHJcbiAgICBoZWlnaHQ6IDMycHg7XHJcbiAgfVxyXG5cclxuICAubmF2LWl0ZW0ge1xyXG4gICAgd2lkdGg6IDMycHg7XHJcbiAgICBoZWlnaHQ6IDMycHg7XHJcblxyXG4gICAgYXZhLWljb24gOjpuZy1kZWVwIC5hdmEtaWNvbi1jb250YWluZXIgbHVjaWRlLWljb24ge1xyXG4gICAgICB3aWR0aDogMTZweCAhaW1wb3J0YW50O1xyXG4gICAgICBoZWlnaHQ6IDE2cHggIWltcG9ydGFudDtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC5hY2NvdW50LWljb24ge1xyXG4gICAgd2lkdGg6IDMycHg7XHJcbiAgICBoZWlnaHQ6IDMycHg7XHJcbiAgfVxyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n  return SidebarComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "IconComponent", "i0", "ɵɵelementStart", "ɵɵlistener", "SidebarComponent_div_5_Template_div_click_0_listener", "item_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "onItemClick", "SidebarComponent_div_5_Template_ava_icon_userClick_1_listener", "ɵɵelementEnd", "ɵɵclassProp", "isActive", "ɵɵadvance", "ɵɵproperty", "iconName", "SidebarComponent", "router", "constructor", "sidebarItems", "label", "route", "item", "for<PERSON>ach", "sidebarItem", "console", "log", "onAccountClick", "navigate", "onLogoClick", "ɵɵdirectiveInject", "i1", "Router", "selectors", "decls", "vars", "consts", "template", "SidebarComponent_Template", "rf", "ctx", "SidebarComponent_Template_div_click_2_listener", "ɵɵelement", "ɵɵtemplate", "SidebarComponent_div_5_Template", "SidebarComponent_Template_div_click_8_listener", "SidebarComponent_Template_ava_icon_userClick_9_listener", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\shared\\components\\sidebar\\sidebar.component.ts", "C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\shared\\components\\sidebar\\sidebar.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Router } from '@angular/router';\r\nimport { IconComponent } from '@ava/play-comp-library';\r\n\r\n@Component({\r\n  selector: 'app-sidebar',\r\n  standalone: true,\r\n  imports: [CommonModule, IconComponent],\r\n  templateUrl: './sidebar.component.html',\r\n  styleUrl: './sidebar.component.scss'\r\n})\r\nexport class SidebarComponent {\r\n\r\n  constructor(private router: Router) {}\r\n  \r\n  sidebarItems = [\r\n    {\r\n      iconName: 'bot',\r\n      label: 'Dashboard',\r\n      isActive: false,\r\n      route: '/dashboard'\r\n    },\r\n    {\r\n      iconName: 'wrench',\r\n      label: 'Tools',\r\n      isActive: false,\r\n      route: '/tools'\r\n    },\r\n    {\r\n      iconName: 'workflow',\r\n      label: 'Analytics',\r\n      isActive: false,\r\n      route: '/analytics'\r\n    },\r\n    {\r\n      iconName: 'shield-check',\r\n      label: 'Team',\r\n      isActive: false,\r\n      route: '/team'\r\n    },\r\n    {\r\n      iconName: 'book-text',\r\n      label: 'Documents',\r\n      isActive: false,\r\n      route: '/documents'\r\n    }\r\n  ];\r\n\r\n  onItemClick(item: any) {\r\n    // Reset all items to inactive\r\n    this.sidebarItems.forEach(sidebarItem => sidebarItem.isActive = false);\r\n    // Set clicked item as active\r\n    item.isActive = true;\r\n\r\n    // Add navigation logic here\r\n    console.log(`Navigating to: ${item.label} - Route: ${item.route}`);\r\n\r\n    // You can add router navigation here:\r\n    // this.router.navigate([item.route]);\r\n  }\r\n\r\n  onAccountClick() {\r\n    console.log('Account icon clicked - navigating to my-agent-home');\r\n    this.router.navigate(['/my-agent-home']);\r\n  }\r\n\r\n  onLogoClick() {\r\n    console.log('Logo clicked - navigate to home');\r\n  }\r\n}\r\n", "<div class=\"sidebar-container\">\r\n  <!-- Top section with Aava logo -->\r\n  <div class=\"sidebar-top\">\r\n    <div class=\"logo-container\" (click)=\"onLogoClick()\">\r\n      <img src=\"assets/icons/Aava.svg\" alt=\"Aava Logo\" class=\"aava-logo\" />\r\n    </div>\r\n\r\n    <!-- Navigation icons right after logo -->\r\n    <div class=\"sidebar-nav\">\r\n      <div\r\n        *ngFor=\"let item of sidebarItems\"\r\n        class=\"nav-item\"\r\n        [class.active]=\"item.isActive\"\r\n        (click)=\"onItemClick(item)\"\r\n        [attr.aria-label]=\"item.label\"\r\n      >\r\n        <ava-icon\r\n          [iconName]=\"item.iconName\"\r\n          [iconSize]=\"20\"\r\n          [iconColor]=\"'#666D99'\"\r\n          [cursor]=\"true\"\r\n          (userClick)=\"onItemClick(item)\"\r\n        ></ava-icon>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Divider line -->\r\n  <div class=\"sidebar-divider\"></div>\r\n\r\n  <!-- Bottom section with account icon -->\r\n  <div class=\"sidebar-bottom\">\r\n    <div class=\"account-icon\" (click)=\"onAccountClick()\">\r\n      <ava-icon\r\n        iconName=\"user\"\r\n        [iconSize]=\"20\"\r\n        iconColor=\"#FFFFFF\"\r\n        [cursor]=\"true\"\r\n        (userClick)=\"onAccountClick()\"\r\n      ></ava-icon>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,aAAa,QAAQ,wBAAwB;;;;;;;ICMhDC,EAAA,CAAAC,cAAA,cAMC;IAFCD,EAAA,CAAAE,UAAA,mBAAAC,qDAAA;MAAA,MAAAC,OAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,WAAA,CAAAP,OAAA,CAAiB;IAAA,EAAC;IAG3BJ,EAAA,CAAAC,cAAA,mBAMC;IADCD,EAAA,CAAAE,UAAA,uBAAAU,8DAAA;MAAA,MAAAR,OAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAaF,MAAA,CAAAG,WAAA,CAAAP,OAAA,CAAiB;IAAA,EAAC;IAEnCJ,EADG,CAAAa,YAAA,EAAW,EACR;;;;IAXJb,EAAA,CAAAc,WAAA,WAAAV,OAAA,CAAAW,QAAA,CAA8B;;IAK5Bf,EAAA,CAAAgB,SAAA,EAA0B;IAG1BhB,EAHA,CAAAiB,UAAA,aAAAb,OAAA,CAAAc,QAAA,CAA0B,gBACX,wBACQ,gBACR;;;ADRzB,WAAaC,gBAAgB;EAAvB,MAAOA,gBAAgB;IAEPC,MAAA;IAApBC,YAAoBD,MAAc;MAAd,KAAAA,MAAM,GAANA,MAAM;IAAW;IAErCE,YAAY,GAAG,CACb;MACEJ,QAAQ,EAAE,KAAK;MACfK,KAAK,EAAE,WAAW;MAClBR,QAAQ,EAAE,KAAK;MACfS,KAAK,EAAE;KACR,EACD;MACEN,QAAQ,EAAE,QAAQ;MAClBK,KAAK,EAAE,OAAO;MACdR,QAAQ,EAAE,KAAK;MACfS,KAAK,EAAE;KACR,EACD;MACEN,QAAQ,EAAE,UAAU;MACpBK,KAAK,EAAE,WAAW;MAClBR,QAAQ,EAAE,KAAK;MACfS,KAAK,EAAE;KACR,EACD;MACEN,QAAQ,EAAE,cAAc;MACxBK,KAAK,EAAE,MAAM;MACbR,QAAQ,EAAE,KAAK;MACfS,KAAK,EAAE;KACR,EACD;MACEN,QAAQ,EAAE,WAAW;MACrBK,KAAK,EAAE,WAAW;MAClBR,QAAQ,EAAE,KAAK;MACfS,KAAK,EAAE;KACR,CACF;IAEDb,WAAWA,CAACc,IAAS;MACnB;MACA,IAAI,CAACH,YAAY,CAACI,OAAO,CAACC,WAAW,IAAIA,WAAW,CAACZ,QAAQ,GAAG,KAAK,CAAC;MACtE;MACAU,IAAI,CAACV,QAAQ,GAAG,IAAI;MAEpB;MACAa,OAAO,CAACC,GAAG,CAAC,kBAAkBJ,IAAI,CAACF,KAAK,aAAaE,IAAI,CAACD,KAAK,EAAE,CAAC;MAElE;MACA;IACF;IAEAM,cAAcA,CAAA;MACZF,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;MACjE,IAAI,CAACT,MAAM,CAACW,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;IAC1C;IAEAC,WAAWA,CAAA;MACTJ,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAChD;;uCAzDWV,gBAAgB,EAAAnB,EAAA,CAAAiC,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;;YAAhBhB,gBAAgB;MAAAiB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCTzB1C,EAHJ,CAAAC,cAAA,aAA+B,aAEJ,aAC6B;UAAxBD,EAAA,CAAAE,UAAA,mBAAA0C,+CAAA;YAAA,OAASD,GAAA,CAAAX,WAAA,EAAa;UAAA,EAAC;UACjDhC,EAAA,CAAA6C,SAAA,aAAqE;UACvE7C,EAAA,CAAAa,YAAA,EAAM;UAGNb,EAAA,CAAAC,cAAA,aAAyB;UACvBD,EAAA,CAAA8C,UAAA,IAAAC,+BAAA,iBAMC;UAUL/C,EADE,CAAAa,YAAA,EAAM,EACF;UAGNb,EAAA,CAAA6C,SAAA,aAAmC;UAIjC7C,EADF,CAAAC,cAAA,aAA4B,aAC2B;UAA3BD,EAAA,CAAAE,UAAA,mBAAA8C,+CAAA;YAAA,OAASL,GAAA,CAAAb,cAAA,EAAgB;UAAA,EAAC;UAClD9B,EAAA,CAAAC,cAAA,kBAMC;UADCD,EAAA,CAAAE,UAAA,uBAAA+C,wDAAA;YAAA,OAAaN,GAAA,CAAAb,cAAA,EAAgB;UAAA,EAAC;UAItC9B,EAHO,CAAAa,YAAA,EAAW,EACR,EACF,EACF;;;UAhCmBb,EAAA,CAAAgB,SAAA,GAAe;UAAfhB,EAAA,CAAAiB,UAAA,YAAA0B,GAAA,CAAArB,YAAA,CAAe;UAyBhCtB,EAAA,CAAAgB,SAAA,GAAe;UAEfhB,EAFA,CAAAiB,UAAA,gBAAe,gBAEA;;;qBD7BXnB,YAAY,EAAAoD,EAAA,CAAAC,OAAA,EAAEpD,aAAa;MAAAqD,MAAA;IAAA;;SAI1BjC,gBAAgB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}