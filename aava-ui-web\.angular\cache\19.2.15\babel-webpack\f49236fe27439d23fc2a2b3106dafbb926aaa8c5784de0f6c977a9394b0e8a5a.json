{"ast": null, "code": "// Helper function to safely get environment variables from window.env\n// It will throw an error if a key is not found, ensuring all required envs are present.\nconst getRequiredEnv = key => {\n  const envWindow = window;\n  const value = envWindow.env?.[key];\n  if (value === undefined || value === null) {\n    throw new Error(`Environment variable '${key}' is not defined in window.env.`);\n  }\n  return String(value); // Ensure the value is returned as a string\n};\n// Helper function to safely get environment variables with fallback\nconst getEnvWithFallback = (key, fallback) => {\n  try {\n    return getRequiredEnv(key);\n  } catch {\n    return fallback;\n  }\n};\n// Detect current project context\nconst detectProjectContext = () => {\n  try {\n    const currentPath = window.location.pathname;\n    const currentPort = window.location.port;\n    // Check for elder-wand specific indicators\n    if (currentPath.includes('/launchpad/') || currentPort === '52906') {\n      return 'elder-wand';\n    }\n    // Check for console specific indicators\n    if (currentPath.includes('/console/') || currentPort === '4203') {\n      return 'console';\n    }\n    // Default context detection based on available environment variables\n    try {\n      getRequiredEnv('elderWandUrl');\n      return 'elder-wand';\n    } catch {\n      return 'console';\n    }\n  } catch {\n    return 'unknown';\n  }\n};\n// ---\n// Shared Environment configuration\n// This environment file can be used by both Console and Elder Wand applications\n// It dynamically detects the project context and provides appropriate defaults\nexport const environment = {\n  production: false,\n  // This often remains a static build-time flag\n  // Application URLs (constructed dynamically)\n  elderWandUrl: getEnvWithFallback('elderWandUrl', 'http://localhost:52906/launchpad'),\n  experienceStudioUrl: getEnvWithFallback('experienceStudioUrl', 'http://localhost:4201/experience'),\n  productStudioUrl: getEnvWithFallback('productStudioUrl', 'http://localhost:4202/product'),\n  consoleRedirectUrl: getEnvWithFallback('consoleRedirectUrl', 'http://localhost:4203/console'),\n  consoleUrl: getEnvWithFallback('consoleUrl', 'http://localhost:4203/console'),\n  consoleRedirectUri: getEnvWithFallback('consoleRedirectUri', 'http://localhost:4203/console'),\n  // API Configuration (constructed dynamically or directly from window.env)\n  apiVersion: getEnvWithFallback('apiVersion', 'v1'),\n  baseUrl: getEnvWithFallback('baseUrl', 'http://localhost:3000'),\n  consoleApi: getEnvWithFallback('consoleApi', 'http://localhost:3000/api'),\n  consoleApiV2: getEnvWithFallback('consoleApiV2', 'http://localhost:3000/api/v2'),\n  consoleApiAuthUrl: getEnvWithFallback('consoleApiAuthUrl', 'http://localhost:3000/auth'),\n  consoleEmbeddingApi: getEnvWithFallback('consoleEmbeddingApi', 'http://localhost:3000/embedding'),\n  consoleInstructionApi: getEnvWithFallback('consoleInstructionApi', 'http://localhost:3000/instruction'),\n  consoleLangfuseUrl: getEnvWithFallback('consoleLangfuseUrl', 'http://localhost:3000/langfuse'),\n  consoleTruelensUrl: getEnvWithFallback('consoleTruelensUrl', 'http://localhost:3000/truelens'),\n  consolePipelineApi: getEnvWithFallback('consolePipelineApi', 'http://localhost:3000/pipeline'),\n  experienceApiUrl: getEnvWithFallback('experienceApiUrl', 'http://localhost:3000/experience'),\n  productApiUrl: getEnvWithFallback('productApiUrl', 'http://localhost:3000/product'),\n  // Legacy properties for backward compatibility\n  apiBaseUrl: getEnvWithFallback('baseUrl', 'http://localhost:3000'),\n  apiUrl: getEnvWithFallback('baseUrl', 'http://localhost:3000'),\n  elderWandApiAuthUrl: getEnvWithFallback('consoleApiAuthUrl', 'http://localhost:3000/auth'),\n  elderWandRedirectUrl: getEnvWithFallback('elderWandUrl', 'http://localhost:52906/launchpad'),\n  // Logging and App Specific\n  enableLogStreaming: getEnvWithFallback('enableLogStreaming', 'all'),\n  logStreamingApiUrl: getEnvWithFallback('logStreamingApiUrl', 'http://localhost:3000/logs'),\n  appVersion: getEnvWithFallback('appVersion', '1.0.0'),\n  workflowExecutionMode: getEnvWithFallback('workflowExecutionMode', 'local'),\n  useBasicLogin: getEnvWithFallback('useBasicLogin', 'false'),\n  // Project context detection\n  projectContext: detectProjectContext(),\n  // Utility function for constructing API URLs\n  getApiUrl: endpoint => {\n    const baseUrl = getEnvWithFallback('baseUrl', 'http://localhost:3000');\n    return `${baseUrl}${endpoint}`;\n  }\n};\n// Log the environment configuration for debugging purposes\nconsole.log('Shared Environment configuration loaded:', {\n  projectContext: environment.projectContext,\n  enableLogStreaming: environment.enableLogStreaming,\n  baseUrl: environment.baseUrl\n});", "map": {"version": 3, "names": ["getRequiredEnv", "key", "envWindow", "window", "value", "env", "undefined", "Error", "String", "getEnvWithFallback", "fallback", "detectProjectContext", "currentPath", "location", "pathname", "currentPort", "port", "includes", "environment", "production", "elderWandUrl", "experienceStudioUrl", "productStudioUrl", "consoleRedirectUrl", "consoleUrl", "consoleRedirectUri", "apiVersion", "baseUrl", "consoleApi", "consoleApiV2", "consoleApiAuthUrl", "consoleEmbeddingApi", "consoleInstructionApi", "consoleLangfuseUrl", "consoleTruelensUrl", "consolePipelineApi", "experienceApiUrl", "productApiUrl", "apiBaseUrl", "apiUrl", "elderWandApiAuthUrl", "elderWandRedirectUrl", "enableLogStreaming", "logStreamingApiUrl", "appVersion", "workflowExecutionMode", "useBasicLogin", "projectContext", "getApiUrl", "endpoint", "console", "log"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\environments\\environment.ts"], "sourcesContent": ["// Helper function to safely get environment variables from window.env\r\n// It will throw an error if a key is not found, ensuring all required envs are present.\r\nconst getRequiredEnv = (key: string): string => {\r\n  // Extend the Window interface to include 'env'\r\n  interface EnvWindow extends Window {\r\n    env?: Record<string, string>;\r\n  }\r\n  const envWindow = window as EnvWindow;\r\n  const value = envWindow.env?.[key];\r\n  if (value === undefined || value === null) {\r\n    throw new Error(\r\n      `Environment variable '${key}' is not defined in window.env.`,\r\n    );\r\n  }\r\n  return String(value); // Ensure the value is returned as a string\r\n};\r\n\r\n// Helper function to safely get environment variables with fallback\r\nconst getEnvWithFallback = (key: string, fallback: string): string => {\r\n  try {\r\n    return getRequiredEnv(key);\r\n  } catch {\r\n    return fallback;\r\n  }\r\n};\r\n\r\n// Detect current project context\r\nconst detectProjectContext = (): 'console' | 'elder-wand' | 'unknown' => {\r\n  try {\r\n    const currentPath = window.location.pathname;\r\n    const currentPort = window.location.port;\r\n\r\n    // Check for elder-wand specific indicators\r\n    if (currentPath.includes('/launchpad/') || currentPort === '52906') {\r\n      return 'elder-wand';\r\n    }\r\n\r\n    // Check for console specific indicators\r\n    if (currentPath.includes('/console/') || currentPort === '4203') {\r\n      return 'console';\r\n    }\r\n\r\n    // Default context detection based on available environment variables\r\n    try {\r\n      getRequiredEnv('elderWandUrl');\r\n      return 'elder-wand';\r\n    } catch {\r\n      return 'console';\r\n    }\r\n  } catch {\r\n    return 'unknown';\r\n  }\r\n};\r\n\r\n// ---\r\n\r\n// Shared Environment configuration\r\n// This environment file can be used by both Console and Elder Wand applications\r\n// It dynamically detects the project context and provides appropriate defaults\r\nexport const environment = {\r\n  production: false, // This often remains a static build-time flag\r\n\r\n  // Application URLs (constructed dynamically)\r\n  elderWandUrl: getEnvWithFallback(\r\n    'elderWandUrl',\r\n    'http://localhost:52906/launchpad',\r\n  ),\r\n  experienceStudioUrl: getEnvWithFallback(\r\n    'experienceStudioUrl',\r\n    'http://localhost:4201/experience',\r\n  ),\r\n  productStudioUrl: getEnvWithFallback(\r\n    'productStudioUrl',\r\n    'http://localhost:4202/product',\r\n  ),\r\n  consoleRedirectUrl: getEnvWithFallback(\r\n    'consoleRedirectUrl',\r\n    'http://localhost:4203/console',\r\n  ),\r\n  consoleUrl: getEnvWithFallback('consoleUrl', 'http://localhost:4203/console'),\r\n  consoleRedirectUri: getEnvWithFallback(\r\n    'consoleRedirectUri',\r\n    'http://localhost:4203/console',\r\n  ),\r\n\r\n  // API Configuration (constructed dynamically or directly from window.env)\r\n  apiVersion: getEnvWithFallback('apiVersion', 'v1'),\r\n  baseUrl: getEnvWithFallback('baseUrl', 'http://localhost:3000'),\r\n  consoleApi: getEnvWithFallback('consoleApi', 'http://localhost:3000/api'),\r\n  consoleApiV2: getEnvWithFallback(\r\n    'consoleApiV2',\r\n    'http://localhost:3000/api/v2',\r\n  ),\r\n  consoleApiAuthUrl: getEnvWithFallback(\r\n    'consoleApiAuthUrl',\r\n    'http://localhost:3000/auth',\r\n  ),\r\n  consoleEmbeddingApi: getEnvWithFallback(\r\n    'consoleEmbeddingApi',\r\n    'http://localhost:3000/embedding',\r\n  ),\r\n  consoleInstructionApi: getEnvWithFallback(\r\n    'consoleInstructionApi',\r\n    'http://localhost:3000/instruction',\r\n  ),\r\n  consoleLangfuseUrl: getEnvWithFallback(\r\n    'consoleLangfuseUrl',\r\n    'http://localhost:3000/langfuse',\r\n  ),\r\n  consoleTruelensUrl: getEnvWithFallback(\r\n    'consoleTruelensUrl',\r\n    'http://localhost:3000/truelens',\r\n  ),\r\n  consolePipelineApi: getEnvWithFallback(\r\n    'consolePipelineApi',\r\n    'http://localhost:3000/pipeline',\r\n  ),\r\n  experienceApiUrl: getEnvWithFallback(\r\n    'experienceApiUrl',\r\n    'http://localhost:3000/experience',\r\n  ),\r\n  productApiUrl: getEnvWithFallback(\r\n    'productApiUrl',\r\n    'http://localhost:3000/product',\r\n  ),\r\n\r\n  // Legacy properties for backward compatibility\r\n  apiBaseUrl: getEnvWithFallback('baseUrl', 'http://localhost:3000'),\r\n  apiUrl: getEnvWithFallback('baseUrl', 'http://localhost:3000'),\r\n  elderWandApiAuthUrl: getEnvWithFallback(\r\n    'consoleApiAuthUrl',\r\n    'http://localhost:3000/auth',\r\n  ),\r\n  elderWandRedirectUrl: getEnvWithFallback(\r\n    'elderWandUrl',\r\n    'http://localhost:52906/launchpad',\r\n  ),\r\n\r\n  // Logging and App Specific\r\n  enableLogStreaming: getEnvWithFallback('enableLogStreaming', 'all'),\r\n  logStreamingApiUrl: getEnvWithFallback(\r\n    'logStreamingApiUrl',\r\n    'http://localhost:3000/logs',\r\n  ),\r\n  appVersion: getEnvWithFallback('appVersion', '1.0.0'),\r\n  workflowExecutionMode: getEnvWithFallback('workflowExecutionMode', 'local'),\r\n  useBasicLogin: getEnvWithFallback('useBasicLogin', 'false'),\r\n\r\n  // Project context detection\r\n  projectContext: detectProjectContext(),\r\n\r\n  // Utility function for constructing API URLs\r\n  getApiUrl: (endpoint: string) => {\r\n    const baseUrl = getEnvWithFallback('baseUrl', 'http://localhost:3000');\r\n    return `${baseUrl}${endpoint}`;\r\n  },\r\n};\r\n\r\n// Log the environment configuration for debugging purposes\r\nconsole.log('Shared Environment configuration loaded:', {\r\n  projectContext: environment.projectContext,\r\n  enableLogStreaming: environment.enableLogStreaming,\r\n  baseUrl: environment.baseUrl,\r\n});\r\n"], "mappings": "AAAA;AACA;AACA,MAAMA,cAAc,GAAIC,GAAW,IAAY;EAK7C,MAAMC,SAAS,GAAGC,MAAmB;EACrC,MAAMC,KAAK,GAAGF,SAAS,CAACG,GAAG,GAAGJ,GAAG,CAAC;EAClC,IAAIG,KAAK,KAAKE,SAAS,IAAIF,KAAK,KAAK,IAAI,EAAE;IACzC,MAAM,IAAIG,KAAK,CACb,yBAAyBN,GAAG,iCAAiC,CAC9D;EACH;EACA,OAAOO,MAAM,CAACJ,KAAK,CAAC,CAAC,CAAC;AACxB,CAAC;AAED;AACA,MAAMK,kBAAkB,GAAGA,CAACR,GAAW,EAAES,QAAgB,KAAY;EACnE,IAAI;IACF,OAAOV,cAAc,CAACC,GAAG,CAAC;EAC5B,CAAC,CAAC,MAAM;IACN,OAAOS,QAAQ;EACjB;AACF,CAAC;AAED;AACA,MAAMC,oBAAoB,GAAGA,CAAA,KAA2C;EACtE,IAAI;IACF,MAAMC,WAAW,GAAGT,MAAM,CAACU,QAAQ,CAACC,QAAQ;IAC5C,MAAMC,WAAW,GAAGZ,MAAM,CAACU,QAAQ,CAACG,IAAI;IAExC;IACA,IAAIJ,WAAW,CAACK,QAAQ,CAAC,aAAa,CAAC,IAAIF,WAAW,KAAK,OAAO,EAAE;MAClE,OAAO,YAAY;IACrB;IAEA;IACA,IAAIH,WAAW,CAACK,QAAQ,CAAC,WAAW,CAAC,IAAIF,WAAW,KAAK,MAAM,EAAE;MAC/D,OAAO,SAAS;IAClB;IAEA;IACA,IAAI;MACFf,cAAc,CAAC,cAAc,CAAC;MAC9B,OAAO,YAAY;IACrB,CAAC,CAAC,MAAM;MACN,OAAO,SAAS;IAClB;EACF,CAAC,CAAC,MAAM;IACN,OAAO,SAAS;EAClB;AACF,CAAC;AAED;AAEA;AACA;AACA;AACA,OAAO,MAAMkB,WAAW,GAAG;EACzBC,UAAU,EAAE,KAAK;EAAE;EAEnB;EACAC,YAAY,EAAEX,kBAAkB,CAC9B,cAAc,EACd,kCAAkC,CACnC;EACDY,mBAAmB,EAAEZ,kBAAkB,CACrC,qBAAqB,EACrB,kCAAkC,CACnC;EACDa,gBAAgB,EAAEb,kBAAkB,CAClC,kBAAkB,EAClB,+BAA+B,CAChC;EACDc,kBAAkB,EAAEd,kBAAkB,CACpC,oBAAoB,EACpB,+BAA+B,CAChC;EACDe,UAAU,EAAEf,kBAAkB,CAAC,YAAY,EAAE,+BAA+B,CAAC;EAC7EgB,kBAAkB,EAAEhB,kBAAkB,CACpC,oBAAoB,EACpB,+BAA+B,CAChC;EAED;EACAiB,UAAU,EAAEjB,kBAAkB,CAAC,YAAY,EAAE,IAAI,CAAC;EAClDkB,OAAO,EAAElB,kBAAkB,CAAC,SAAS,EAAE,uBAAuB,CAAC;EAC/DmB,UAAU,EAAEnB,kBAAkB,CAAC,YAAY,EAAE,2BAA2B,CAAC;EACzEoB,YAAY,EAAEpB,kBAAkB,CAC9B,cAAc,EACd,8BAA8B,CAC/B;EACDqB,iBAAiB,EAAErB,kBAAkB,CACnC,mBAAmB,EACnB,4BAA4B,CAC7B;EACDsB,mBAAmB,EAAEtB,kBAAkB,CACrC,qBAAqB,EACrB,iCAAiC,CAClC;EACDuB,qBAAqB,EAAEvB,kBAAkB,CACvC,uBAAuB,EACvB,mCAAmC,CACpC;EACDwB,kBAAkB,EAAExB,kBAAkB,CACpC,oBAAoB,EACpB,gCAAgC,CACjC;EACDyB,kBAAkB,EAAEzB,kBAAkB,CACpC,oBAAoB,EACpB,gCAAgC,CACjC;EACD0B,kBAAkB,EAAE1B,kBAAkB,CACpC,oBAAoB,EACpB,gCAAgC,CACjC;EACD2B,gBAAgB,EAAE3B,kBAAkB,CAClC,kBAAkB,EAClB,kCAAkC,CACnC;EACD4B,aAAa,EAAE5B,kBAAkB,CAC/B,eAAe,EACf,+BAA+B,CAChC;EAED;EACA6B,UAAU,EAAE7B,kBAAkB,CAAC,SAAS,EAAE,uBAAuB,CAAC;EAClE8B,MAAM,EAAE9B,kBAAkB,CAAC,SAAS,EAAE,uBAAuB,CAAC;EAC9D+B,mBAAmB,EAAE/B,kBAAkB,CACrC,mBAAmB,EACnB,4BAA4B,CAC7B;EACDgC,oBAAoB,EAAEhC,kBAAkB,CACtC,cAAc,EACd,kCAAkC,CACnC;EAED;EACAiC,kBAAkB,EAAEjC,kBAAkB,CAAC,oBAAoB,EAAE,KAAK,CAAC;EACnEkC,kBAAkB,EAAElC,kBAAkB,CACpC,oBAAoB,EACpB,4BAA4B,CAC7B;EACDmC,UAAU,EAAEnC,kBAAkB,CAAC,YAAY,EAAE,OAAO,CAAC;EACrDoC,qBAAqB,EAAEpC,kBAAkB,CAAC,uBAAuB,EAAE,OAAO,CAAC;EAC3EqC,aAAa,EAAErC,kBAAkB,CAAC,eAAe,EAAE,OAAO,CAAC;EAE3D;EACAsC,cAAc,EAAEpC,oBAAoB,EAAE;EAEtC;EACAqC,SAAS,EAAGC,QAAgB,IAAI;IAC9B,MAAMtB,OAAO,GAAGlB,kBAAkB,CAAC,SAAS,EAAE,uBAAuB,CAAC;IACtE,OAAO,GAAGkB,OAAO,GAAGsB,QAAQ,EAAE;EAChC;CACD;AAED;AACAC,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE;EACtDJ,cAAc,EAAE7B,WAAW,CAAC6B,cAAc;EAC1CL,kBAAkB,EAAExB,WAAW,CAACwB,kBAAkB;EAClDf,OAAO,EAAET,WAAW,CAACS;CACtB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}