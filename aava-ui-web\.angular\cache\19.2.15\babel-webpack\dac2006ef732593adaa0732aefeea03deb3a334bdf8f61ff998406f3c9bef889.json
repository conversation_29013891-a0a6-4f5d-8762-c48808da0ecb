{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { EventEmitter, Component, Input, Output, NgModule } from '@angular/core';\nlet HighchartsChartComponent = /*#__PURE__*/(() => {\n  class HighchartsChartComponent {\n    constructor(el, _zone // #75\n    ) {\n      this.el = el;\n      this._zone = _zone;\n      this.updateChange = new EventEmitter(true);\n      this.chartInstance = new EventEmitter(); // #26\n    }\n    ngOnChanges(changes) {\n      const update = changes.update?.currentValue;\n      if (changes.options || update) {\n        this.wrappedUpdateOrCreateChart();\n        if (update) {\n          this.updateChange.emit(false); // clear the flag after update\n        }\n      }\n    }\n    wrappedUpdateOrCreateChart() {\n      if (this.runOutsideAngular) {\n        this._zone.runOutsideAngular(() => {\n          this.updateOrCreateChart();\n        });\n      } else {\n        this.updateOrCreateChart();\n      }\n    }\n    updateOrCreateChart() {\n      if (this.chart?.update) {\n        this.chart.update(this.options, true, this.oneToOne || false);\n      } else {\n        this.chart = this.Highcharts[this.constructorType || 'chart'](this.el.nativeElement, this.options, this.callbackFunction || null);\n        // emit chart instance on init\n        this.chartInstance.emit(this.chart);\n      }\n    }\n    ngOnDestroy() {\n      if (this.chart) {\n        // #56\n        this.chart.destroy();\n        this.chart = null;\n        // emit chart instance on destroy\n        this.chartInstance.emit(this.chart);\n      }\n    }\n    static {\n      this.ɵfac = function HighchartsChartComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || HighchartsChartComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: HighchartsChartComponent,\n        selectors: [[\"highcharts-chart\"]],\n        inputs: {\n          Highcharts: \"Highcharts\",\n          constructorType: \"constructorType\",\n          callbackFunction: \"callbackFunction\",\n          oneToOne: \"oneToOne\",\n          runOutsideAngular: \"runOutsideAngular\",\n          options: \"options\",\n          update: \"update\"\n        },\n        outputs: {\n          updateChange: \"updateChange\",\n          chartInstance: \"chartInstance\"\n        },\n        standalone: false,\n        features: [i0.ɵɵNgOnChangesFeature],\n        decls: 0,\n        vars: 0,\n        template: function HighchartsChartComponent_Template(rf, ctx) {},\n        encapsulation: 2\n      });\n    }\n  }\n  return HighchartsChartComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet HighchartsChartModule = /*#__PURE__*/(() => {\n  class HighchartsChartModule {\n    static {\n      this.ɵfac = function HighchartsChartModule_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || HighchartsChartModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: HighchartsChartModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n    }\n  }\n  return HighchartsChartModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/*\n * Public API Surface of highcharts-angular\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { HighchartsChartComponent, HighchartsChartModule };", "map": {"version": 3, "names": ["i0", "EventEmitter", "Component", "Input", "Output", "NgModule", "HighchartsChartComponent", "constructor", "el", "_zone", "updateChange", "chartInstance", "ngOnChanges", "changes", "update", "currentValue", "options", "wrappedUpdateOrCreateChart", "emit", "runOutsideAngular", "updateOrCreateChart", "chart", "oneToOne", "Highcharts", "constructorType", "nativeElement", "callbackFunction", "ngOnDestroy", "destroy", "ɵfac", "HighchartsChartComponent_Factory", "__ngFactoryType__", "ɵɵdirectiveInject", "ElementRef", "NgZone", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵNgOnChangesFeature", "decls", "vars", "template", "HighchartsChartComponent_Template", "rf", "ctx", "encapsulation", "ngDevMode", "HighchartsChartModule", "HighchartsChartModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector"], "sources": ["C:/console/aava-ui-web/node_modules/highcharts-angular/fesm2022/highcharts-angular.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, Component, Input, Output, NgModule } from '@angular/core';\n\nclass HighchartsChartComponent {\n    constructor(el, _zone // #75\n    ) {\n        this.el = el;\n        this._zone = _zone;\n        this.updateChange = new EventEmitter(true);\n        this.chartInstance = new EventEmitter(); // #26\n    }\n    ngOnChanges(changes) {\n        const update = changes.update?.currentValue;\n        if (changes.options || update) {\n            this.wrappedUpdateOrCreateChart();\n            if (update) {\n                this.updateChange.emit(false); // clear the flag after update\n            }\n        }\n    }\n    wrappedUpdateOrCreateChart() {\n        if (this.runOutsideAngular) {\n            this._zone.runOutsideAngular(() => {\n                this.updateOrCreateChart();\n            });\n        }\n        else {\n            this.updateOrCreateChart();\n        }\n    }\n    updateOrCreateChart() {\n        if (this.chart?.update) {\n            this.chart.update(this.options, true, this.oneToOne || false);\n        }\n        else {\n            this.chart = this.Highcharts[this.constructorType || 'chart'](this.el.nativeElement, this.options, this.callbackFunction || null);\n            // emit chart instance on init\n            this.chartInstance.emit(this.chart);\n        }\n    }\n    ngOnDestroy() {\n        if (this.chart) { // #56\n            this.chart.destroy();\n            this.chart = null;\n            // emit chart instance on destroy\n            this.chartInstance.emit(this.chart);\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: HighchartsChartComponent, deps: [{ token: i0.ElementRef }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: HighchartsChartComponent, selector: \"highcharts-chart\", inputs: { Highcharts: \"Highcharts\", constructorType: \"constructorType\", callbackFunction: \"callbackFunction\", oneToOne: \"oneToOne\", runOutsideAngular: \"runOutsideAngular\", options: \"options\", update: \"update\" }, outputs: { updateChange: \"updateChange\", chartInstance: \"chartInstance\" }, usesOnChanges: true, ngImport: i0, template: '', isInline: true }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: HighchartsChartComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'highcharts-chart',\n                    template: ''\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.NgZone }]; }, propDecorators: { Highcharts: [{\n                type: Input\n            }], constructorType: [{\n                type: Input\n            }], callbackFunction: [{\n                type: Input\n            }], oneToOne: [{\n                type: Input\n            }], runOutsideAngular: [{\n                type: Input\n            }], options: [{\n                type: Input\n            }], update: [{\n                type: Input\n            }], updateChange: [{\n                type: Output\n            }], chartInstance: [{\n                type: Output\n            }] } });\n\nclass HighchartsChartModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: HighchartsChartModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.2.12\", ngImport: i0, type: HighchartsChartModule, declarations: [HighchartsChartComponent], exports: [HighchartsChartComponent] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: HighchartsChartModule }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: HighchartsChartModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    declarations: [HighchartsChartComponent],\n                    exports: [HighchartsChartComponent]\n                }]\n        }] });\n\n/*\n * Public API Surface of highcharts-angular\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { HighchartsChartComponent, HighchartsChartModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AAAC,IAE3EC,wBAAwB;EAA9B,MAAMA,wBAAwB,CAAC;IAC3BC,WAAWA,CAACC,EAAE,EAAEC,KAAK,CAAC;IAAA,EACpB;MACE,IAAI,CAACD,EAAE,GAAGA,EAAE;MACZ,IAAI,CAACC,KAAK,GAAGA,KAAK;MAClB,IAAI,CAACC,YAAY,GAAG,IAAIT,YAAY,CAAC,IAAI,CAAC;MAC1C,IAAI,CAACU,aAAa,GAAG,IAAIV,YAAY,CAAC,CAAC,CAAC,CAAC;IAC7C;IACAW,WAAWA,CAACC,OAAO,EAAE;MACjB,MAAMC,MAAM,GAAGD,OAAO,CAACC,MAAM,EAAEC,YAAY;MAC3C,IAAIF,OAAO,CAACG,OAAO,IAAIF,MAAM,EAAE;QAC3B,IAAI,CAACG,0BAA0B,CAAC,CAAC;QACjC,IAAIH,MAAM,EAAE;UACR,IAAI,CAACJ,YAAY,CAACQ,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;QACnC;MACJ;IACJ;IACAD,0BAA0BA,CAAA,EAAG;MACzB,IAAI,IAAI,CAACE,iBAAiB,EAAE;QACxB,IAAI,CAACV,KAAK,CAACU,iBAAiB,CAAC,MAAM;UAC/B,IAAI,CAACC,mBAAmB,CAAC,CAAC;QAC9B,CAAC,CAAC;MACN,CAAC,MACI;QACD,IAAI,CAACA,mBAAmB,CAAC,CAAC;MAC9B;IACJ;IACAA,mBAAmBA,CAAA,EAAG;MAClB,IAAI,IAAI,CAACC,KAAK,EAAEP,MAAM,EAAE;QACpB,IAAI,CAACO,KAAK,CAACP,MAAM,CAAC,IAAI,CAACE,OAAO,EAAE,IAAI,EAAE,IAAI,CAACM,QAAQ,IAAI,KAAK,CAAC;MACjE,CAAC,MACI;QACD,IAAI,CAACD,KAAK,GAAG,IAAI,CAACE,UAAU,CAAC,IAAI,CAACC,eAAe,IAAI,OAAO,CAAC,CAAC,IAAI,CAAChB,EAAE,CAACiB,aAAa,EAAE,IAAI,CAACT,OAAO,EAAE,IAAI,CAACU,gBAAgB,IAAI,IAAI,CAAC;QACjI;QACA,IAAI,CAACf,aAAa,CAACO,IAAI,CAAC,IAAI,CAACG,KAAK,CAAC;MACvC;IACJ;IACAM,WAAWA,CAAA,EAAG;MACV,IAAI,IAAI,CAACN,KAAK,EAAE;QAAE;QACd,IAAI,CAACA,KAAK,CAACO,OAAO,CAAC,CAAC;QACpB,IAAI,CAACP,KAAK,GAAG,IAAI;QACjB;QACA,IAAI,CAACV,aAAa,CAACO,IAAI,CAAC,IAAI,CAACG,KAAK,CAAC;MACvC;IACJ;IACA;MAAS,IAAI,CAACQ,IAAI,YAAAC,iCAAAC,iBAAA;QAAA,YAAAA,iBAAA,IAAyFzB,wBAAwB,EAAlCN,EAAE,CAAAgC,iBAAA,CAAkDhC,EAAE,CAACiC,UAAU,GAAjEjC,EAAE,CAAAgC,iBAAA,CAA4EhC,EAAE,CAACkC,MAAM;MAAA,CAA4C;IAAE;IACtO;MAAS,IAAI,CAACC,IAAI,kBAD+EnC,EAAE,CAAAoC,iBAAA;QAAAC,IAAA,EACJ/B,wBAAwB;QAAAgC,SAAA;QAAAC,MAAA;UAAAhB,UAAA;UAAAC,eAAA;UAAAE,gBAAA;UAAAJ,QAAA;UAAAH,iBAAA;UAAAH,OAAA;UAAAF,MAAA;QAAA;QAAA0B,OAAA;UAAA9B,YAAA;UAAAC,aAAA;QAAA;QAAA8B,UAAA;QAAAC,QAAA,GADtB1C,EAAE,CAAA2C,oBAAA;QAAAC,KAAA;QAAAC,IAAA;QAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAAC,aAAA;MAAA,EACqZ;IAAE;EAC9f;EAAC,OA/CK5C,wBAAwB;AAAA;AAgD9B;EAAA,QAAA6C,SAAA,oBAAAA,SAAA;AAAA;AAwBoB,IAEdC,qBAAqB;EAA3B,MAAMA,qBAAqB,CAAC;IACxB;MAAS,IAAI,CAACvB,IAAI,YAAAwB,8BAAAtB,iBAAA;QAAA,YAAAA,iBAAA,IAAyFqB,qBAAqB;MAAA,CAAkD;IAAE;IACpL;MAAS,IAAI,CAACE,IAAI,kBA/B+EtD,EAAE,CAAAuD,gBAAA;QAAAlB,IAAA,EA+BSe;MAAqB,EAAkF;IAAE;IACrN;MAAS,IAAI,CAACI,IAAI,kBAhC+ExD,EAAE,CAAAyD,gBAAA,IAgCiC;IAAE;EAC1I;EAAC,OAJKL,qBAAqB;AAAA;AAK3B;EAAA,QAAAD,SAAA,oBAAAA,SAAA;AAAA;;AAQA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAAS7C,wBAAwB,EAAE8C,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}