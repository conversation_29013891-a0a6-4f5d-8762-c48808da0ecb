{"ast": null, "code": "import { signal, inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, Validators } from '@angular/forms';\nimport logintext from './login.json';\nimport { AvaTextboxComponent, IconComponent, ButtonComponent } from '@ava/play-comp-library';\nimport { AuthService } from '@shared/auth/services/auth.service';\nimport { TokenStorageService } from '../../../auth/services/token-storage.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nexport let LoginComponent = /*#__PURE__*/(() => {\n  class LoginComponent {\n    fb;\n    router;\n    loginMode = signal('sso');\n    isLoading = signal(false);\n    showPassword = signal(false);\n    loginForm;\n    errorMessage = signal(null);\n    labels = logintext;\n    authService = inject(AuthService);\n    tokenStorage = inject(TokenStorageService);\n    constructor(fb, router) {\n      this.fb = fb;\n      this.router = router;\n      this.loginForm = this.fb.group({\n        username: ['', [Validators.required]],\n        password: ['', Validators.required],\n        keepSignedIn: [false]\n      });\n    }\n    ngOnInit() {\n      const storedLoginType = this.tokenStorage.getLoginType();\n      if (storedLoginType === 'sso' || storedLoginType === 'basic') {\n        this.loginMode.set(storedLoginType === 'basic' ? 'form' : 'sso');\n      } else {\n        this.loginMode.set('sso');\n      }\n    }\n    getControl(name) {\n      return this.loginForm.get(name);\n    }\n    onBasicLogin() {\n      if (this.loginForm.valid) {\n        this.isLoading.set(true);\n        this.errorMessage.set(null);\n        const {\n          username,\n          password\n        } = this.loginForm.value;\n        this.authService.basicLoginWithCredentials(username, password).subscribe({\n          next: () => {\n            this.tokenStorage.storeLoginType('basic');\n            const redirectUrl = this.authService.getPostLoginRedirectUrl();\n            this.router.navigate([redirectUrl]);\n          },\n          error: error => {\n            this.isLoading.set(false);\n            this.errorMessage.set('Invalid username or password');\n            console.error('Login failed:', error);\n          }\n        });\n      } else {\n        this.markFormGroupTouched();\n      }\n    }\n    onCompanyLogin() {\n      this.isLoading.set(true);\n      this.errorMessage.set(null);\n      this.authService.loginSSO().subscribe({\n        next: () => {\n          this.isLoading.set(false);\n        },\n        error: error => {\n          console.error('Login failed:', error);\n          this.errorMessage.set('Failed to initiate login with company account.');\n          this.isLoading.set(false);\n        }\n      });\n    }\n    togglePasswordVisibility() {\n      this.showPassword.set(!this.showPassword());\n    }\n    clearInput(fieldName) {\n      this.loginForm.get(fieldName)?.setValue('');\n      this.loginForm.get(fieldName)?.markAsTouched();\n    }\n    clearUsername() {\n      this.clearInput('username');\n    }\n    markFormGroupTouched() {\n      Object.keys(this.loginForm.controls).forEach(key => {\n        const control = this.loginForm.get(key);\n        control?.markAsTouched();\n      });\n    }\n    getFieldError(fieldName) {\n      const field = this.loginForm.get(fieldName);\n      if (field?.touched && field?.errors) {\n        if (field.errors['required']) {\n          return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`;\n        }\n        if (field.errors['email']) {\n          return 'Please enter a valid email address';\n        }\n        if (field.errors['minlength']) {\n          return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} must be at least ${field.errors['minlength'].requiredLength} characters`;\n        }\n      }\n      return '';\n    }\n    onForgotPassword() {\n      this.router.navigate(['/forgot-password']);\n    }\n    onTroubleSigningIn() {\n      this.router.navigate(['/help']);\n    }\n    static ɵfac = function LoginComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 24,\n      vars: 29,\n      consts: [[\"id\", \"login-container\", 1, \"row\"], [1, \"col-5\", \"p-0\"], [\"src\", \"login.png\", \"alt\", \"\", 1, \"login-image\"], [1, \"col-7\", \"p-0\", \"login-section\"], [1, \"sign-in-container\"], [1, \"heading\"], [1, \"mb-2\", \"main-heading\"], [1, \"sub-heading\"], [1, \"new-login-form\", 3, \"formGroup\"], [1, \"form-field-wrapper\"], [\"formControlName\", \"username\", 3, \"label\", \"placeholder\", \"required\", \"error\", \"iconSeparator\", \"iconSpacing\"], [\"slot\", \"icon-end\", \"iconName\", \"x\", 3, \"click\", \"iconSize\", \"cursor\", \"disabled\"], [1, \"form-field-wrapper\", \"mt-4\", \"mb-4\"], [\"formControlName\", \"password\", 3, \"label\", \"type\", \"placeholder\", \"required\", \"error\", \"iconSeparator\", \"iconSpacing\"], [\"slot\", \"icon-end\", 3, \"click\", \"iconName\", \"iconSize\", \"cursor\", \"disabled\"], [1, \"new-buttons-container\"], [1, \"sign-in-button\", \"mb-5\"], [\"variant\", \"primary\", \"size\", \"large\", 1, \"mb-4\", 3, \"userClick\", \"label\", \"processing\", \"width\"], [1, \"new-separator\"], [1, \"login-with-company\", \"mt-5\"], [\"variant\", \"secondary\", \"size\", \"large\", 3, \"userClick\", \"label\", \"width\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"img\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"h3\", 6);\n          i0.ɵɵtext(7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"p\", 7);\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"form\", 8)(11, \"div\", 9)(12, \"ava-textbox\", 10)(13, \"ava-icon\", 11);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_ava_icon_click_13_listener() {\n            return ctx.clearUsername();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(14, \"div\", 12)(15, \"ava-textbox\", 13)(16, \"ava-icon\", 14);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_ava_icon_click_16_listener() {\n            return ctx.togglePasswordVisibility();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 15)(18, \"div\", 16)(19, \"ava-button\", 17);\n          i0.ɵɵlistener(\"userClick\", function LoginComponent_Template_ava_button_userClick_19_listener() {\n            return ctx.onBasicLogin();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 18);\n          i0.ɵɵtext(21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"div\", 19)(23, \"ava-button\", 20);\n          i0.ɵɵlistener(\"userClick\", function LoginComponent_Template_ava_button_userClick_23_listener() {\n            return ctx.onCompanyLogin();\n          });\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate(ctx.labels.labels.main_heading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.labels.labels.sub_heading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"label\", ctx.labels.labels.username)(\"placeholder\", ctx.labels.placeholders.username)(\"required\", true)(\"error\", ctx.getFieldError(\"username\"))(\"iconSeparator\", false)(\"iconSpacing\", \"normal\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"iconSize\", 16)(\"cursor\", true)(\"disabled\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"label\", ctx.labels.labels.password)(\"type\", ctx.showPassword() ? \"text\" : \"password\")(\"placeholder\", ctx.labels.placeholders.password)(\"required\", true)(\"error\", ctx.getFieldError(\"password\"))(\"iconSeparator\", false)(\"iconSpacing\", \"normal\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"iconName\", ctx.showPassword() ? \"eye-off\" : \"eye\")(\"iconSize\", 18)(\"cursor\", true)(\"disabled\", false);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"label\", ctx.labels.labels.sign_in + \" \" + ctx.labels.labels.arrow)(\"processing\", ctx.isLoading())(\"width\", \"100%\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.labels.labels.seperator, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"label\", ctx.labels.labels.login_with_company)(\"width\", \"100%\");\n        }\n      },\n      dependencies: [CommonModule, ReactiveFormsModule, i1.ɵNgNoValidate, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.FormGroupDirective, i1.FormControlName, AvaTextboxComponent, IconComponent, ButtonComponent],\n      styles: [\"#login-container[_ngcontent-%COMP%]   .login-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100vh;\\n}\\n#login-container[_ngcontent-%COMP%]   .login-section[_ngcontent-%COMP%] {\\n  background-color: white;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n#login-container[_ngcontent-%COMP%]   .login-section[_ngcontent-%COMP%]   .sign-in-container[_ngcontent-%COMP%] {\\n  min-width: 510px;\\n  width: 50%;\\n  margin: 0 auto;\\n  padding: 24px;\\n  background: var(--Brand-Neutral-n-50, #f0f1f2);\\n  border: 1px solid var(--Brand-Neutral-n-50, #f0f1f2);\\n  border-radius: 16px;\\n}\\n\\n.p-0[_ngcontent-%COMP%] {\\n  padding: 0;\\n}\\n\\n.mb-2[_ngcontent-%COMP%] {\\n  margin-bottom: 0.5rem;\\n}\\n\\n.mt-4[_ngcontent-%COMP%] {\\n  margin-top: 1.5rem;\\n}\\n\\n.mb-4[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n\\n.mb-5[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n}\\n\\n.mt-5[_ngcontent-%COMP%] {\\n  margin-top: 2rem;\\n}\\n\\n.heading[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 64px;\\n}\\n\\n.main-heading[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  font-weight: 700;\\n  color: #1a1a1a;\\n  margin-bottom: 8px;\\n}\\n\\n.sub-heading[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #666;\\n  font-weight: 400;\\n}\\n\\n.new-separator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  text-align: center;\\n  color: #898e99;\\n  font-size: 18px;\\n  font-weight: 600;\\n}\\n.new-separator[_ngcontent-%COMP%]::before, .new-separator[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  flex: 1;\\n  height: 1px;\\n  background: #898e99;\\n}\\n.new-separator[_ngcontent-%COMP%]::before {\\n  margin-right: 16px;\\n}\\n.new-separator[_ngcontent-%COMP%]::after {\\n  margin-left: 16px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return LoginComponent;\n})();", "map": {"version": 3, "names": ["signal", "inject", "CommonModule", "ReactiveFormsModule", "Validators", "logintext", "AvaTextboxComponent", "IconComponent", "ButtonComponent", "AuthService", "TokenStorageService", "LoginComponent", "fb", "router", "loginMode", "isLoading", "showPassword", "loginForm", "errorMessage", "labels", "authService", "tokenStorage", "constructor", "group", "username", "required", "password", "keepSignedIn", "ngOnInit", "storedLoginType", "getLoginType", "set", "getControl", "name", "get", "onBasicLogin", "valid", "value", "basicLoginWithCredentials", "subscribe", "next", "storeLoginType", "redirectUrl", "getPostLoginRedirectUrl", "navigate", "error", "console", "markFormGroupTouched", "onCompanyLogin", "loginSSO", "togglePasswordVisibility", "clearInput", "fieldName", "setValue", "<PERSON><PERSON><PERSON><PERSON>ched", "clearUsername", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "getFieldError", "field", "touched", "errors", "char<PERSON>t", "toUpperCase", "slice", "<PERSON><PERSON><PERSON><PERSON>", "onForgotPassword", "onTroubleSigningIn", "i0", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "LoginComponent_Template_ava_icon_click_13_listener", "LoginComponent_Template_ava_icon_click_16_listener", "LoginComponent_Template_ava_button_userClick_19_listener", "LoginComponent_Template_ava_button_userClick_23_listener", "ɵɵadvance", "ɵɵtextInterpolate", "main_heading", "sub_heading", "ɵɵproperty", "placeholders", "sign_in", "arrow", "ɵɵtextInterpolate1", "seperator", "login_with_company", "ɵNgNoValidate", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "FormGroupDirective", "FormControlName", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\auth\\components\\login\\login.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\auth\\components\\login\\login.component.html"], "sourcesContent": ["import { Component, OnInit, signal, inject } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport {\r\n  ReactiveFormsModule,\r\n  FormBuilder,\r\n  FormGroup,\r\n  Validators,\r\n  FormControl,\r\n} from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\n\r\nimport { HttpErrorResponse } from '@angular/common/http';\r\nimport logintext from './login.json';\r\nimport {\r\n  AvaTextboxComponent,\r\n  IconComponent,\r\n  ButtonComponent,\r\n} from '@ava/play-comp-library';\r\nimport { AuthService } from '@shared/auth/services/auth.service';\r\nimport { TokenStorageService } from '../../../auth/services/token-storage.service';\r\n\r\nexport interface SavedAccount {\r\n  email: string;\r\n  profilePic?: string;\r\n  isSelected?: boolean;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-login',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    ReactiveFormsModule,\r\n    AvaTextboxComponent,\r\n    IconComponent,\r\n    ButtonComponent,\r\n  ],\r\n  templateUrl: './login.component.html',\r\n  styleUrls: ['./login.component.scss'],\r\n})\r\nexport class LoginComponent implements OnInit {\r\n  loginMode = signal<'sso' | 'form'>('sso');\r\n  isLoading = signal(false);\r\n  showPassword = signal(false);\r\n  loginForm: FormGroup;\r\n  errorMessage = signal<string | null>(null);\r\n  public labels: any = logintext;\r\n\r\n  private authService = inject(AuthService);\r\n  private tokenStorage = inject(TokenStorageService);\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private router: Router,\r\n  ) {\r\n    this.loginForm = this.fb.group({\r\n      username: ['', [Validators.required]],\r\n      password: ['', Validators.required],\r\n      keepSignedIn: [false],\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    const storedLoginType = this.tokenStorage.getLoginType();\r\n    if (storedLoginType === 'sso' || storedLoginType === 'basic') {\r\n      this.loginMode.set(storedLoginType === 'basic' ? 'form' : 'sso');\r\n    } else {\r\n      this.loginMode.set('sso');\r\n    }\r\n  }\r\n\r\n  getControl(name: string): FormControl {\r\n    return this.loginForm.get(name) as FormControl;\r\n  }\r\n\r\n  onBasicLogin(): void {\r\n    if (this.loginForm.valid) {\r\n      this.isLoading.set(true);\r\n      this.errorMessage.set(null);\r\n\r\n      const { username, password } = this.loginForm.value;\r\n      this.authService.basicLoginWithCredentials(username, password).subscribe({\r\n        next: () => {\r\n          this.tokenStorage.storeLoginType('basic');\r\n          const redirectUrl = this.authService.getPostLoginRedirectUrl();\r\n          this.router.navigate([redirectUrl]);\r\n        },\r\n        error: (error: HttpErrorResponse) => {\r\n          this.isLoading.set(false);\r\n          this.errorMessage.set('Invalid username or password');\r\n          console.error('Login failed:', error);\r\n        },\r\n      });\r\n    } else {\r\n      this.markFormGroupTouched();\r\n    }\r\n  }\r\n\r\n  onCompanyLogin(): void {\r\n    this.isLoading.set(true);\r\n    this.errorMessage.set(null);\r\n\r\n    this.authService.loginSSO().subscribe({\r\n      next: () => {\r\n        this.isLoading.set(false);\r\n      },\r\n      error: (error) => {\r\n        console.error('Login failed:', error);\r\n        this.errorMessage.set('Failed to initiate login with company account.');\r\n        this.isLoading.set(false);\r\n      },\r\n    });\r\n  }\r\n\r\n  togglePasswordVisibility(): void {\r\n    this.showPassword.set(!this.showPassword());\r\n  }\r\n\r\n  clearInput(fieldName: string): void {\r\n    this.loginForm.get(fieldName)?.setValue('');\r\n    this.loginForm.get(fieldName)?.markAsTouched();\r\n  }\r\n\r\n  clearUsername(): void {\r\n    this.clearInput('username');\r\n  }\r\n\r\n  private markFormGroupTouched(): void {\r\n    Object.keys(this.loginForm.controls).forEach((key) => {\r\n      const control = this.loginForm.get(key);\r\n      control?.markAsTouched();\r\n    });\r\n  }\r\n\r\n  getFieldError(fieldName: string): string {\r\n    const field = this.loginForm.get(fieldName);\r\n    if (field?.touched && field?.errors) {\r\n      if (field.errors['required']) {\r\n        return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`;\r\n      }\r\n      if (field.errors['email']) {\r\n        return 'Please enter a valid email address';\r\n      }\r\n      if (field.errors['minlength']) {\r\n        return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} must be at least ${field.errors['minlength'].requiredLength} characters`;\r\n      }\r\n    }\r\n    return '';\r\n  }\r\n\r\n  onForgotPassword(): void {\r\n    this.router.navigate(['/forgot-password']);\r\n  }\r\n\r\n  onTroubleSigningIn(): void {\r\n    this.router.navigate(['/help']);\r\n  }\r\n}\r\n", "<div id=\"login-container\" class=\"row\">\r\n  <div class=\"col-5 p-0\">\r\n    <img class=\"login-image\" src=\"login.png\" alt=\"\" />\r\n  </div>\r\n  <div class=\"col-7 p-0 login-section\">\r\n    <div class=\"sign-in-container\">\r\n      <div class=\"heading\">\r\n        <h3 class=\"mb-2 main-heading\">{{ labels.labels.main_heading }}</h3>\r\n        <p class=\"sub-heading\">{{ labels.labels.sub_heading }}</p>\r\n      </div>\r\n      <form [formGroup]=\"loginForm\" class=\"new-login-form\">\r\n        <div class=\"form-field-wrapper\">\r\n          <ava-textbox\r\n            [label]=\"labels.labels.username\"\r\n            [placeholder]=\"labels.placeholders.username\"\r\n            [required]=\"true\"\r\n            formControlName=\"username\"\r\n            [error]=\"getFieldError('username')\"\r\n            [iconSeparator]=\"false\"\r\n            [iconSpacing]=\"'normal'\"\r\n          >\r\n            <ava-icon\r\n              slot=\"icon-end\"\r\n              iconName=\"x\"\r\n              [iconSize]=\"16\"\r\n              [cursor]=\"true\"\r\n              (click)=\"clearUsername()\"\r\n              [disabled]=\"false\"\r\n            >\r\n            </ava-icon>\r\n          </ava-textbox>\r\n        </div>\r\n        <div class=\"form-field-wrapper mt-4 mb-4\">\r\n          <ava-textbox\r\n            [label]=\"labels.labels.password\"\r\n            [type]=\"showPassword() ? 'text' : 'password'\"\r\n            [placeholder]=\"labels.placeholders.password\"\r\n            [required]=\"true\"\r\n            formControlName=\"password\"\r\n            [error]=\"getFieldError('password')\"\r\n            [iconSeparator]=\"false\"\r\n            [iconSpacing]=\"'normal'\"\r\n          >\r\n            <ava-icon\r\n              slot=\"icon-end\"\r\n              [iconName]=\"showPassword() ? 'eye-off' : 'eye'\"\r\n              [iconSize]=\"18\"\r\n              [cursor]=\"true\"\r\n              (click)=\"togglePasswordVisibility()\"\r\n              [disabled]=\"false\"\r\n            >\r\n            </ava-icon>\r\n          </ava-textbox>\r\n        </div>\r\n        <div class=\"new-buttons-container\">\r\n          <div class=\"sign-in-button mb-5\">\r\n            <ava-button\r\n              class=\"mb-4\"\r\n              [label]=\"labels.labels.sign_in + ' ' + labels.labels.arrow\"\r\n              variant=\"primary\"\r\n              size=\"large\"\r\n              [processing]=\"isLoading()\"\r\n              [width]=\"'100%'\"\r\n              (userClick)=\"onBasicLogin()\"\r\n            >\r\n            </ava-button>\r\n          </div>\r\n\r\n          <div class=\"new-separator\">\r\n            {{ labels.labels.seperator }}\r\n          </div>\r\n          <div class=\"login-with-company mt-5\">\r\n            <ava-button\r\n              [label]=\"labels.labels.login_with_company\"\r\n              variant=\"secondary\"\r\n              size=\"large\"\r\n              [width]=\"'100%'\"\r\n              (userClick)=\"onCompanyLogin()\"\r\n            >\r\n            </ava-button>\r\n          </div>\r\n        </div>\r\n      </form>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAAA,SAA4BA,MAAM,EAAEC,MAAM,QAAQ,eAAe;AACjE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SACEC,mBAAmB,EAGnBC,UAAU,QAEL,gBAAgB;AAIvB,OAAOC,SAAS,MAAM,cAAc;AACpC,SACEC,mBAAmB,EACnBC,aAAa,EACbC,eAAe,QACV,wBAAwB;AAC/B,SAASC,WAAW,QAAQ,oCAAoC;AAChE,SAASC,mBAAmB,QAAQ,8CAA8C;;;;AAqBlF,WAAaC,cAAc;EAArB,MAAOA,cAAc;IAYfC,EAAA;IACAC,MAAA;IAZVC,SAAS,GAAGd,MAAM,CAAiB,KAAK,CAAC;IACzCe,SAAS,GAAGf,MAAM,CAAC,KAAK,CAAC;IACzBgB,YAAY,GAAGhB,MAAM,CAAC,KAAK,CAAC;IAC5BiB,SAAS;IACTC,YAAY,GAAGlB,MAAM,CAAgB,IAAI,CAAC;IACnCmB,MAAM,GAAQd,SAAS;IAEtBe,WAAW,GAAGnB,MAAM,CAACQ,WAAW,CAAC;IACjCY,YAAY,GAAGpB,MAAM,CAACS,mBAAmB,CAAC;IAElDY,YACUV,EAAe,EACfC,MAAc;MADd,KAAAD,EAAE,GAAFA,EAAE;MACF,KAAAC,MAAM,GAANA,MAAM;MAEd,IAAI,CAACI,SAAS,GAAG,IAAI,CAACL,EAAE,CAACW,KAAK,CAAC;QAC7BC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACpB,UAAU,CAACqB,QAAQ,CAAC,CAAC;QACrCC,QAAQ,EAAE,CAAC,EAAE,EAAEtB,UAAU,CAACqB,QAAQ,CAAC;QACnCE,YAAY,EAAE,CAAC,KAAK;OACrB,CAAC;IACJ;IAEAC,QAAQA,CAAA;MACN,MAAMC,eAAe,GAAG,IAAI,CAACR,YAAY,CAACS,YAAY,EAAE;MACxD,IAAID,eAAe,KAAK,KAAK,IAAIA,eAAe,KAAK,OAAO,EAAE;QAC5D,IAAI,CAACf,SAAS,CAACiB,GAAG,CAACF,eAAe,KAAK,OAAO,GAAG,MAAM,GAAG,KAAK,CAAC;MAClE,CAAC,MAAM;QACL,IAAI,CAACf,SAAS,CAACiB,GAAG,CAAC,KAAK,CAAC;MAC3B;IACF;IAEAC,UAAUA,CAACC,IAAY;MACrB,OAAO,IAAI,CAAChB,SAAS,CAACiB,GAAG,CAACD,IAAI,CAAgB;IAChD;IAEAE,YAAYA,CAAA;MACV,IAAI,IAAI,CAAClB,SAAS,CAACmB,KAAK,EAAE;QACxB,IAAI,CAACrB,SAAS,CAACgB,GAAG,CAAC,IAAI,CAAC;QACxB,IAAI,CAACb,YAAY,CAACa,GAAG,CAAC,IAAI,CAAC;QAE3B,MAAM;UAAEP,QAAQ;UAAEE;QAAQ,CAAE,GAAG,IAAI,CAACT,SAAS,CAACoB,KAAK;QACnD,IAAI,CAACjB,WAAW,CAACkB,yBAAyB,CAACd,QAAQ,EAAEE,QAAQ,CAAC,CAACa,SAAS,CAAC;UACvEC,IAAI,EAAEA,CAAA,KAAK;YACT,IAAI,CAACnB,YAAY,CAACoB,cAAc,CAAC,OAAO,CAAC;YACzC,MAAMC,WAAW,GAAG,IAAI,CAACtB,WAAW,CAACuB,uBAAuB,EAAE;YAC9D,IAAI,CAAC9B,MAAM,CAAC+B,QAAQ,CAAC,CAACF,WAAW,CAAC,CAAC;UACrC,CAAC;UACDG,KAAK,EAAGA,KAAwB,IAAI;YAClC,IAAI,CAAC9B,SAAS,CAACgB,GAAG,CAAC,KAAK,CAAC;YACzB,IAAI,CAACb,YAAY,CAACa,GAAG,CAAC,8BAA8B,CAAC;YACrDe,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;UACvC;SACD,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAACE,oBAAoB,EAAE;MAC7B;IACF;IAEAC,cAAcA,CAAA;MACZ,IAAI,CAACjC,SAAS,CAACgB,GAAG,CAAC,IAAI,CAAC;MACxB,IAAI,CAACb,YAAY,CAACa,GAAG,CAAC,IAAI,CAAC;MAE3B,IAAI,CAACX,WAAW,CAAC6B,QAAQ,EAAE,CAACV,SAAS,CAAC;QACpCC,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACzB,SAAS,CAACgB,GAAG,CAAC,KAAK,CAAC;QAC3B,CAAC;QACDc,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;UACrC,IAAI,CAAC3B,YAAY,CAACa,GAAG,CAAC,gDAAgD,CAAC;UACvE,IAAI,CAAChB,SAAS,CAACgB,GAAG,CAAC,KAAK,CAAC;QAC3B;OACD,CAAC;IACJ;IAEAmB,wBAAwBA,CAAA;MACtB,IAAI,CAAClC,YAAY,CAACe,GAAG,CAAC,CAAC,IAAI,CAACf,YAAY,EAAE,CAAC;IAC7C;IAEAmC,UAAUA,CAACC,SAAiB;MAC1B,IAAI,CAACnC,SAAS,CAACiB,GAAG,CAACkB,SAAS,CAAC,EAAEC,QAAQ,CAAC,EAAE,CAAC;MAC3C,IAAI,CAACpC,SAAS,CAACiB,GAAG,CAACkB,SAAS,CAAC,EAAEE,aAAa,EAAE;IAChD;IAEAC,aAAaA,CAAA;MACX,IAAI,CAACJ,UAAU,CAAC,UAAU,CAAC;IAC7B;IAEQJ,oBAAoBA,CAAA;MAC1BS,MAAM,CAACC,IAAI,CAAC,IAAI,CAACxC,SAAS,CAACyC,QAAQ,CAAC,CAACC,OAAO,CAAEC,GAAG,IAAI;QACnD,MAAMC,OAAO,GAAG,IAAI,CAAC5C,SAAS,CAACiB,GAAG,CAAC0B,GAAG,CAAC;QACvCC,OAAO,EAAEP,aAAa,EAAE;MAC1B,CAAC,CAAC;IACJ;IAEAQ,aAAaA,CAACV,SAAiB;MAC7B,MAAMW,KAAK,GAAG,IAAI,CAAC9C,SAAS,CAACiB,GAAG,CAACkB,SAAS,CAAC;MAC3C,IAAIW,KAAK,EAAEC,OAAO,IAAID,KAAK,EAAEE,MAAM,EAAE;QACnC,IAAIF,KAAK,CAACE,MAAM,CAAC,UAAU,CAAC,EAAE;UAC5B,OAAO,GAAGb,SAAS,CAACc,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGf,SAAS,CAACgB,KAAK,CAAC,CAAC,CAAC,cAAc;QAChF;QACA,IAAIL,KAAK,CAACE,MAAM,CAAC,OAAO,CAAC,EAAE;UACzB,OAAO,oCAAoC;QAC7C;QACA,IAAIF,KAAK,CAACE,MAAM,CAAC,WAAW,CAAC,EAAE;UAC7B,OAAO,GAAGb,SAAS,CAACc,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGf,SAAS,CAACgB,KAAK,CAAC,CAAC,CAAC,qBAAqBL,KAAK,CAACE,MAAM,CAAC,WAAW,CAAC,CAACI,cAAc,aAAa;QAC5I;MACF;MACA,OAAO,EAAE;IACX;IAEAC,gBAAgBA,CAAA;MACd,IAAI,CAACzD,MAAM,CAAC+B,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;IAC5C;IAEA2B,kBAAkBA,CAAA;MAChB,IAAI,CAAC1D,MAAM,CAAC+B,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;IACjC;;uCApHWjC,cAAc,EAAA6D,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;;YAAdlE,cAAc;MAAAmE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCvCzBZ,EADF,CAAAc,cAAA,aAAsC,aACb;UACrBd,EAAA,CAAAe,SAAA,aAAkD;UACpDf,EAAA,CAAAgB,YAAA,EAAM;UAIAhB,EAHN,CAAAc,cAAA,aAAqC,aACJ,aACR,YACW;UAAAd,EAAA,CAAAiB,MAAA,GAAgC;UAAAjB,EAAA,CAAAgB,YAAA,EAAK;UACnEhB,EAAA,CAAAc,cAAA,WAAuB;UAAAd,EAAA,CAAAiB,MAAA,GAA+B;UACxDjB,EADwD,CAAAgB,YAAA,EAAI,EACtD;UAYAhB,EAXN,CAAAc,cAAA,eAAqD,cACnB,uBAS7B,oBAQE;UAFCd,EAAA,CAAAkB,UAAA,mBAAAC,mDAAA;YAAA,OAASN,GAAA,CAAA9B,aAAA,EAAe;UAAA,EAAC;UAK/BiB,EAFI,CAAAgB,YAAA,EAAW,EACC,EACV;UAYFhB,EAXJ,CAAAc,cAAA,eAA0C,uBAUvC,oBAQE;UAFCd,EAAA,CAAAkB,UAAA,mBAAAE,mDAAA;YAAA,OAASP,GAAA,CAAAnC,wBAAA,EAA0B;UAAA,EAAC;UAK1CsB,EAFI,CAAAgB,YAAA,EAAW,EACC,EACV;UAGFhB,EAFJ,CAAAc,cAAA,eAAmC,eACA,sBAS9B;UADCd,EAAA,CAAAkB,UAAA,uBAAAG,yDAAA;YAAA,OAAaR,GAAA,CAAAlD,YAAA,EAAc;UAAA,EAAC;UAGhCqC,EADE,CAAAgB,YAAA,EAAa,EACT;UAENhB,EAAA,CAAAc,cAAA,eAA2B;UACzBd,EAAA,CAAAiB,MAAA,IACF;UAAAjB,EAAA,CAAAgB,YAAA,EAAM;UAEJhB,EADF,CAAAc,cAAA,eAAqC,sBAOlC;UADCd,EAAA,CAAAkB,UAAA,uBAAAI,yDAAA;YAAA,OAAaT,GAAA,CAAArC,cAAA,EAAgB;UAAA,EAAC;UAQ5CwB,EANY,CAAAgB,YAAA,EAAa,EACT,EACF,EACD,EACH,EACF,EACF;;;UA9EgChB,EAAA,CAAAuB,SAAA,GAAgC;UAAhCvB,EAAA,CAAAwB,iBAAA,CAAAX,GAAA,CAAAlE,MAAA,CAAAA,MAAA,CAAA8E,YAAA,CAAgC;UACvCzB,EAAA,CAAAuB,SAAA,GAA+B;UAA/BvB,EAAA,CAAAwB,iBAAA,CAAAX,GAAA,CAAAlE,MAAA,CAAAA,MAAA,CAAA+E,WAAA,CAA+B;UAElD1B,EAAA,CAAAuB,SAAA,EAAuB;UAAvBvB,EAAA,CAAA2B,UAAA,cAAAd,GAAA,CAAApE,SAAA,CAAuB;UAGvBuD,EAAA,CAAAuB,SAAA,GAAgC;UAMhCvB,EANA,CAAA2B,UAAA,UAAAd,GAAA,CAAAlE,MAAA,CAAAA,MAAA,CAAAK,QAAA,CAAgC,gBAAA6D,GAAA,CAAAlE,MAAA,CAAAiF,YAAA,CAAA5E,QAAA,CACY,kBAC3B,UAAA6D,GAAA,CAAAvB,aAAA,aAEkB,wBACZ,yBACC;UAKtBU,EAAA,CAAAuB,SAAA,EAAe;UAGfvB,EAHA,CAAA2B,UAAA,gBAAe,gBACA,mBAEG;UAOpB3B,EAAA,CAAAuB,SAAA,GAAgC;UAOhCvB,EAPA,CAAA2B,UAAA,UAAAd,GAAA,CAAAlE,MAAA,CAAAA,MAAA,CAAAO,QAAA,CAAgC,SAAA2D,GAAA,CAAArE,YAAA,yBACa,gBAAAqE,GAAA,CAAAlE,MAAA,CAAAiF,YAAA,CAAA1E,QAAA,CACD,kBAC3B,UAAA2D,GAAA,CAAAvB,aAAA,aAEkB,wBACZ,yBACC;UAItBU,EAAA,CAAAuB,SAAA,EAA+C;UAI/CvB,EAJA,CAAA2B,UAAA,aAAAd,GAAA,CAAArE,YAAA,uBAA+C,gBAChC,gBACA,mBAEG;UASlBwD,EAAA,CAAAuB,SAAA,GAA2D;UAI3DvB,EAJA,CAAA2B,UAAA,UAAAd,GAAA,CAAAlE,MAAA,CAAAA,MAAA,CAAAkF,OAAA,SAAAhB,GAAA,CAAAlE,MAAA,CAAAA,MAAA,CAAAmF,KAAA,CAA2D,eAAAjB,GAAA,CAAAtE,SAAA,GAGjC,iBACV;UAOlByD,EAAA,CAAAuB,SAAA,GACF;UADEvB,EAAA,CAAA+B,kBAAA,MAAAlB,GAAA,CAAAlE,MAAA,CAAAA,MAAA,CAAAqF,SAAA,MACF;UAGIhC,EAAA,CAAAuB,SAAA,GAA0C;UAG1CvB,EAHA,CAAA2B,UAAA,UAAAd,GAAA,CAAAlE,MAAA,CAAAA,MAAA,CAAAsF,kBAAA,CAA0C,iBAG1B;;;qBD7C1BvG,YAAY,EACZC,mBAAmB,EAAAuE,EAAA,CAAAgC,aAAA,EAAAhC,EAAA,CAAAiC,eAAA,EAAAjC,EAAA,CAAAkC,oBAAA,EAAAlC,EAAA,CAAAmC,iBAAA,EAAAnC,EAAA,CAAAoC,kBAAA,EAAApC,EAAA,CAAAqC,eAAA,EACnBzG,mBAAmB,EACnBC,aAAa,EACbC,eAAe;MAAAwG,MAAA;IAAA;;SAKNrG,cAAc;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}