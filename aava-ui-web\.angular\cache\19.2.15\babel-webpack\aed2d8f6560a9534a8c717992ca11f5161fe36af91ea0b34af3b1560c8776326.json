{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { map } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"../../../auth/services/token-storage.service\";\n// Injection token for environment\nexport const AGENT_ENVIRONMENT_CONFIG = 'AGENT_ENVIRONMENT_CONFIG';\nexport let AgentServiceService = /*#__PURE__*/(() => {\n  class AgentServiceService {\n    http;\n    tokenStorage;\n    environment;\n    baseUrl;\n    v2BaseUrl;\n    headers = {\n      headers: new HttpHeaders({\n        'Content-Type': 'application/json'\n      })\n    };\n    constructor(http, tokenStorage, environment) {\n      this.http = http;\n      this.tokenStorage = tokenStorage;\n      this.environment = environment;\n      this.baseUrl = this.environment.consoleApi;\n      this.v2BaseUrl = this.environment.consoleApiV2;\n    }\n    /**\n     * Get user signature from token storage\n     * @returns User signature or default email\n     */\n    getUserSignature() {\n      return this.tokenStorage.getDaUsername() || '<EMAIL>';\n    }\n    getAllAgentList() {\n      const url = `${this.baseUrl}/ava/force/agents`;\n      return this.http.get(url, this.headers).pipe(map(response => {\n        return response;\n      }));\n    }\n    /**\n     * Get all individual agents\n     * @returns Observable with individual agents list\n     */\n    getAllIndividualAgents() {\n      const url = `${this.baseUrl}/ava/force/individualAgents`;\n      return this.http.get(url, this.headers).pipe(map(response => {\n        return response;\n      }));\n    }\n    /**\n     * Get collaborative agents with pagination (for workflow editor compatibility)\n     * @param page - Page number\n     * @param records - Number of records per page\n     * @param isDeleted - Whether to include deleted agents\n     * @returns Observable with paginated collaborative agents list\n     */\n    getAllCollaborativeAgentsPagination(page, records, isDeleted) {\n      const url = `${this.v2BaseUrl}/ava/force/da/agent/approved`;\n      return this.http.get(url);\n    }\n    /**\n     * Get collaborative agents with pagination\n     * @param page - Page number (default: 1)\n     * @param records - Number of records per page (default: 10)\n     * @returns Observable with paginated collaborative agents list\n     */\n    getCollaborativeAgentsPaginated(page = 1, records = 10) {\n      const url = `${this.v2BaseUrl}/ava/force/da/agent`;\n      const params = {\n        page: page.toString(),\n        records: records.toString(),\n        isDeleted: 'false'\n      };\n      return this.http.get(url, {\n        ...this.headers,\n        params\n      }).pipe(map(response => {\n        return response;\n      }));\n    }\n    /**\n     * Get collaborative agent details by ID\n     * @param agentId - The ID of the collaborative agent to fetch\n     * @returns Observable with the agent details\n     */\n    getCollaborativeAgentDetailsById(agentId) {\n      const url = `${this.v2BaseUrl}/ava/force/da/agent`;\n      const params = {\n        agentId: agentId\n      };\n      return this.http.get(url, {\n        ...this.headers,\n        params\n      }).pipe(map(response => {\n        return response;\n      }));\n    }\n    getLabels() {\n      const url = `${this.baseUrl}/ava/force/label`;\n      return this.http.get(url, this.headers).pipe(map(response => {\n        return response;\n      }));\n    }\n    /**\n     * Parse label values from string format to array of options\n     * @param labelValues - String in format \"id=name;id=name;\"\n     * @returns Array of {value, name} objects\n     */\n    parseLabelValues(labelValues) {\n      if (!labelValues) return [];\n      const labelPairs = labelValues.split(\";\");\n      return labelPairs.filter(pair => pair.trim()).map(pair => {\n        const [value, name] = pair.split(\"=\");\n        return {\n          value: value?.trim() || '',\n          name: name?.trim() || ''\n        };\n      });\n    }\n    /**\n     * Get models from labels API\n     * @returns Observable with parsed model options\n     */\n    getModelsFromLabels() {\n      return this.getLabels().pipe(map(response => {\n        const categoryLabels = response.categoryLabels || [];\n        const modelCategory = categoryLabels.find(category => category.categoryId === 1);\n        if (modelCategory) {\n          const modelLabel = modelCategory.labels.find(label => label.labelCode === 'MODEL');\n          if (modelLabel && modelLabel.labelValues) {\n            return this.parseLabelValues(modelLabel.labelValues).map(option => ({\n              id: option.value,\n              name: option.name,\n              type: 'model',\n              icon: 'assets/images/model.png',\n              description: `Model: ${option.name}`\n            }));\n          }\n        }\n        return [];\n      }));\n    }\n    /**\n     * Get knowledge bases from labels API\n     * @returns Observable with parsed knowledge base options\n     */\n    getKnowledgeBasesFromLabels() {\n      return this.getLabels().pipe(map(response => {\n        const categoryLabels = response.categoryLabels || [];\n        const iclCategory = categoryLabels.find(category => category.categoryId === 2);\n        if (iclCategory) {\n          const knowledgeLabel = iclCategory.labels.find(label => label.labelCode === 'RAG_KNOWLEDGEBASE_NAME');\n          if (knowledgeLabel && knowledgeLabel.labelValues) {\n            return this.parseLabelValues(knowledgeLabel.labelValues).map(option => ({\n              id: option.value,\n              name: option.name,\n              type: 'knowledge',\n              icon: 'assets/images/knowledge.png',\n              description: `Knowledge Base: ${option.name}`\n            }));\n          }\n        }\n        return [];\n      }));\n    }\n    /**\n     * Get guardrails from labels API\n     * @returns Observable with parsed guardrail options\n     */\n    getGuardrailsFromLabels() {\n      return this.getLabels().pipe(map(response => {\n        const categoryLabels = response.categoryLabels || [];\n        const otherCategory = categoryLabels.find(category => category.categoryId === 3);\n        if (otherCategory) {\n          // Include ALL labels from Other category, including \"Enable Guardrails\"\n          return otherCategory.labels.filter(label => label.labelType === 'Toggle') // Include all toggle types\n          .map(label => ({\n            id: label.labelId.toString(),\n            name: label.labelName,\n            code: label.labelCode,\n            type: 'guardrail',\n            icon: 'assets/images/guardrail.png',\n            description: label.labelInfo || `Guardrail: ${label.labelName}`\n          }));\n        }\n        return [];\n      }));\n    }\n    /**\n     * Get agent details by ID\n     * @param agentId - The ID of the agent to fetch\n     * @returns Observable with the agent details\n     */\n    getAgentById(agentId) {\n      const url = `${this.baseUrl}/ava/force/individualAgent?individualAgentId=${agentId}`;\n      return this.http.get(url, this.headers).pipe(map(response => {\n        return response;\n      }));\n    }\n    /**\n     * Save individual agent with the provided payload\n     * @param payload - The individual agent data to save\n     * @returns Observable with the API response\n     */\n    individualAgentSave(payload) {\n      const url = `${this.baseUrl}/ava/force/individualAgent`;\n      return this.http.post(url, payload, this.headers).pipe(map(response => {\n        return response;\n      }));\n    }\n    /**\n     * Create new collaborative agent with v2 API\n     * @param payload - The collaborative agent data to create\n     * @returns Observable with the API response\n     */\n    createCollaborativeAgentV2(payload) {\n      const url = `${this.v2BaseUrl}/ava/force/da/agent`;\n      return this.http.post(url, payload, this.headers).pipe(map(response => {\n        return response;\n      }));\n    }\n    /**\n     * Edit individual agent data\n     * @param payload - The individual agent data to update\n     * @returns Observable with the API response\n     */\n    individualAgentEdit(payload) {\n      const url = `${this.baseUrl}/ava/force/individualAgent`;\n      return this.http.put(url, payload, this.headers).pipe(map(response => {\n        return response;\n      }));\n    }\n    /**\n     * Update collaborative agent with v2 API\n     * @param payload - The collaborative agent data to update\n     * @returns Observable with the API response\n     */\n    updateCollaborativeAgentV2(payload) {\n      const url = `${this.v2BaseUrl}/ava/force/da/agent`;\n      return this.http.put(url, payload, this.headers).pipe(map(response => {\n        return response;\n      }));\n    }\n    /**\n     * Submit collaborative agent change request\n     * @param payload - The collaborative agent change request data\n     * @returns Observable with the API response\n     */\n    submitCollaborativeAgentChangeRequest(payload) {\n      const url = `${this.v2BaseUrl}/ava/force/da/agent/change_request`;\n      return this.http.put(url, payload, this.headers).pipe(map(response => {\n        return response;\n      }));\n    }\n    /**\n     * Get models for collaborative agents using the direct API\n     * @returns Observable with the models array\n     */\n    getCollaborativeModels() {\n      const url = `${this.baseUrl}/ava/force/model?modelType=Generative`;\n      return this.http.get(url, this.headers).pipe(map(response => {\n        return response;\n      }));\n    }\n    /**\n     * Delete individual agent by ID (Legacy - kept for backward compatibility)\n     * @param agentId - The ID of the agent to delete\n     * @returns Observable with the API response\n     */\n    deleteAgent(agentId) {\n      const url = `${this.baseUrl}/ava/force/individualAgent?individualAgentId=${agentId}`;\n      return this.http.delete(url, this.headers).pipe(map(response => {\n        return response;\n      }));\n    }\n    /**\n     * Delete collaborative agent by ID using v2 API\n     * @param agentId - The ID of the collaborative agent to delete\n     * @returns Observable with the API response\n     */\n    deleteCollaborativeAgent(agentId) {\n      const userSignature = this.getUserSignature();\n      const url = `${this.v2BaseUrl}/ava/force/da/agent/change_request?agentId=${agentId}&modifiedBy=${userSignature}`;\n      return this.http.delete(url, this.headers).pipe(map(response => {\n        return response;\n      }));\n    }\n    /**\n     * Get individual agent minimal details for playground dropdown\n     * @returns Observable with individual agents minimal details\n     */\n    getIndividualAgentMinDetails() {\n      const url = `${this.baseUrl}/ava/force/individualAgentMinDetails`;\n      return this.http.get(url, this.headers).pipe(map(response => {\n        return response;\n      }));\n    }\n    static ɵfac = function AgentServiceService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AgentServiceService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.TokenStorageService), i0.ɵɵinject(AGENT_ENVIRONMENT_CONFIG));\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AgentServiceService,\n      factory: AgentServiceService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return AgentServiceService;\n})();", "map": {"version": 3, "names": ["HttpHeaders", "map", "AGENT_ENVIRONMENT_CONFIG", "AgentServiceService", "http", "tokenStorage", "environment", "baseUrl", "v2BaseUrl", "headers", "constructor", "consoleApi", "consoleApiV2", "getUserSignature", "getDaUsername", "getAllAgentList", "url", "get", "pipe", "response", "getAllIndividualAgents", "getAllCollaborativeAgentsPagination", "page", "records", "isDeleted", "getCollaborativeAgentsPaginated", "params", "toString", "getCollaborativeAgentDetailsById", "agentId", "<PERSON><PERSON><PERSON><PERSON>", "parseLabel<PERSON><PERSON><PERSON>", "labelValues", "labelPairs", "split", "filter", "pair", "trim", "value", "name", "getModelsFromLabels", "categoryLabels", "modelCategory", "find", "category", "categoryId", "modelLabel", "labels", "label", "labelCode", "option", "id", "type", "icon", "description", "getKnowledgeBasesFromLabels", "iclCategory", "knowledgeLabel", "getGuardrailsFromLabels", "otherCategory", "labelType", "labelId", "labelName", "code", "labelInfo", "getAgentById", "individualAgentSave", "payload", "post", "createCollaborativeAgentV2", "individualAgentEdit", "put", "updateCollaborativeAgentV2", "submitCollaborativeAgentChangeRequest", "getCollaborativeModels", "deleteAgent", "delete", "deleteCollaborativeAgent", "userSignature", "getIndividualAgentMinDetails", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "TokenStorageService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\agents\\services\\agent-service.service.ts"], "sourcesContent": ["import { HttpHeaders, HttpClient } from '@angular/common/http';\r\nimport { Injectable, Inject } from '@angular/core';\r\nimport { map, Observable } from 'rxjs';\r\nimport { TokenStorageService } from '../../../auth/services/token-storage.service';\r\n\r\n// Interface for environment configuration\r\nexport interface AgentEnvironmentConfig {\r\n  consoleApi: string;\r\n  consoleApiV2: string;\r\n}\r\n\r\n// Injection token for environment\r\nexport const AGENT_ENVIRONMENT_CONFIG = 'AGENT_ENVIRONMENT_CONFIG';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class AgentServiceService {\r\n  private baseUrl: string;\r\n  private v2BaseUrl: string;\r\n  private headers = {\r\n    headers: new HttpHeaders({\r\n      'Content-Type': 'application/json',\r\n    }),\r\n  }\r\n\r\n  constructor(\r\n    private http: HttpClient,\r\n    private tokenStorage: TokenStorageService,\r\n    @Inject(AGENT_ENVIRONMENT_CONFIG) private environment: AgentEnvironmentConfig\r\n  ) {\r\n    this.baseUrl = this.environment.consoleApi;\r\n    this.v2BaseUrl = this.environment.consoleApiV2;\r\n  }\r\n\r\n  /**\r\n   * Get user signature from token storage\r\n   * @returns User signature or default email\r\n   */\r\n  private getUserSignature(): string {\r\n    return this.tokenStorage.getDaUsername() || '<EMAIL>';\r\n  }\r\n\r\n  public getAllAgentList() {\r\n    const url = `${this.baseUrl}/ava/force/agents`;\r\n    return this.http.get(url, this.headers).pipe(\r\n      map((response: any) => {\r\n          return response;\r\n      })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get all individual agents\r\n   * @returns Observable with individual agents list\r\n   */\r\n  public getAllIndividualAgents() {\r\n    const url = `${this.baseUrl}/ava/force/individualAgents`;\r\n    return this.http.get(url, this.headers).pipe(\r\n      map((response: any) => {\r\n        return response;\r\n      })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get collaborative agents with pagination (for workflow editor compatibility)\r\n   * @param page - Page number\r\n   * @param records - Number of records per page\r\n   * @param isDeleted - Whether to include deleted agents\r\n   * @returns Observable with paginated collaborative agents list\r\n   */\r\n  public getAllCollaborativeAgentsPagination(page: number, records: number, isDeleted: boolean): Observable<any> {\r\n    const url = `${this.v2BaseUrl}/ava/force/da/agent/approved`;\r\n    return this.http.get<any>(url);\r\n  }\r\n\r\n  /**\r\n   * Get collaborative agents with pagination\r\n   * @param page - Page number (default: 1)\r\n   * @param records - Number of records per page (default: 10)\r\n   * @returns Observable with paginated collaborative agents list\r\n   */\r\n  public getCollaborativeAgentsPaginated(page: number = 1, records: number = 10) {\r\n    const url = `${this.v2BaseUrl}/ava/force/da/agent`;\r\n    const params = {\r\n      page: page.toString(),\r\n      records: records.toString(),\r\n      isDeleted: 'false'\r\n    };\r\n\r\n    return this.http.get(url, { ...this.headers, params }).pipe(\r\n      map((response: any) => {\r\n        return response;\r\n      })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get collaborative agent details by ID\r\n   * @param agentId - The ID of the collaborative agent to fetch\r\n   * @returns Observable with the agent details\r\n   */\r\n  public getCollaborativeAgentDetailsById(agentId: string): Observable<any> {\r\n    const url = `${this.v2BaseUrl}/ava/force/da/agent`;\r\n    const params = {\r\n      agentId: agentId\r\n    };\r\n\r\n    return this.http.get(url, { ...this.headers, params }).pipe(\r\n      map((response: any) => {\r\n        return response;\r\n      })\r\n    );\r\n  }\r\n\r\n  public getLabels() {\r\n    const url = `${this.baseUrl}/ava/force/label`;\r\n    return this.http.get(url, this.headers).pipe(\r\n      map((response: any) => {\r\n          return response;\r\n      })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Parse label values from string format to array of options\r\n   * @param labelValues - String in format \"id=name;id=name;\"\r\n   * @returns Array of {value, name} objects\r\n   */\r\n  public parseLabelValues(labelValues: string): {value: string, name: string}[] {\r\n    if (!labelValues) return [];\r\n\r\n    const labelPairs = labelValues.split(\";\");\r\n    return labelPairs\r\n      .filter((pair: string) => pair.trim())\r\n      .map((pair: string) => {\r\n        const [value, name] = pair.split(\"=\");\r\n        return { value: value?.trim() || '', name: name?.trim() || '' };\r\n      });\r\n  }\r\n\r\n  /**\r\n   * Get models from labels API\r\n   * @returns Observable with parsed model options\r\n   */\r\n  public getModelsFromLabels(): Observable<any[]> {\r\n    return this.getLabels().pipe(\r\n      map((response: any) => {\r\n        const categoryLabels = response.categoryLabels || [];\r\n        const modelCategory = categoryLabels.find((category: any) => category.categoryId === 1);\r\n\r\n        if (modelCategory) {\r\n          const modelLabel = modelCategory.labels.find((label: any) => label.labelCode === 'MODEL');\r\n          if (modelLabel && modelLabel.labelValues) {\r\n            return this.parseLabelValues(modelLabel.labelValues).map(option => ({\r\n              id: option.value,\r\n              name: option.name,\r\n              type: 'model',\r\n              icon: 'assets/images/model.png',\r\n              description: `Model: ${option.name}`\r\n            }));\r\n          }\r\n        }\r\n        return [];\r\n      })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get knowledge bases from labels API\r\n   * @returns Observable with parsed knowledge base options\r\n   */\r\n  public getKnowledgeBasesFromLabels(): Observable<any[]> {\r\n    return this.getLabels().pipe(\r\n      map((response: any) => {\r\n        const categoryLabels = response.categoryLabels || [];\r\n        const iclCategory = categoryLabels.find((category: any) => category.categoryId === 2);\r\n\r\n        if (iclCategory) {\r\n          const knowledgeLabel = iclCategory.labels.find((label: any) => label.labelCode === 'RAG_KNOWLEDGEBASE_NAME');\r\n          if (knowledgeLabel && knowledgeLabel.labelValues) {\r\n            return this.parseLabelValues(knowledgeLabel.labelValues).map(option => ({\r\n              id: option.value,\r\n              name: option.name,\r\n              type: 'knowledge',\r\n              icon: 'assets/images/knowledge.png',\r\n              description: `Knowledge Base: ${option.name}`\r\n            }));\r\n          }\r\n        }\r\n        return [];\r\n      })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get guardrails from labels API\r\n   * @returns Observable with parsed guardrail options\r\n   */\r\n  public getGuardrailsFromLabels(): Observable<any[]> {\r\n    return this.getLabels().pipe(\r\n      map((response: any) => {\r\n        const categoryLabels = response.categoryLabels || [];\r\n        const otherCategory = categoryLabels.find((category: any) => category.categoryId === 3);\r\n\r\n        if (otherCategory) {\r\n          // Include ALL labels from Other category, including \"Enable Guardrails\"\r\n          return otherCategory.labels\r\n            .filter((label: any) => label.labelType === 'Toggle') // Include all toggle types\r\n            .map((label: any) => ({\r\n              id: label.labelId.toString(),\r\n              name: label.labelName,\r\n              code: label.labelCode,\r\n              type: 'guardrail',\r\n              icon: 'assets/images/guardrail.png',\r\n              description: label.labelInfo || `Guardrail: ${label.labelName}`\r\n            }));\r\n        }\r\n        return [];\r\n      })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get agent details by ID\r\n   * @param agentId - The ID of the agent to fetch\r\n   * @returns Observable with the agent details\r\n   */\r\n  public getAgentById(agentId: string): Observable<any> {\r\n    const url = `${this.baseUrl}/ava/force/individualAgent?individualAgentId=${agentId}`;\r\n    return this.http.get(url, this.headers).pipe(\r\n      map((response: any) => {\r\n        return response;\r\n      })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Save individual agent with the provided payload\r\n   * @param payload - The individual agent data to save\r\n   * @returns Observable with the API response\r\n   */\r\n  public individualAgentSave(payload: any): Observable<any> {\r\n    const url = `${this.baseUrl}/ava/force/individualAgent`;\r\n    return this.http.post(url, payload, this.headers).pipe(\r\n      map((response: any) => {\r\n        return response;\r\n      })\r\n    );\r\n  }\r\n\r\n\r\n\r\n  /**\r\n   * Create new collaborative agent with v2 API\r\n   * @param payload - The collaborative agent data to create\r\n   * @returns Observable with the API response\r\n   */\r\n  public createCollaborativeAgentV2(payload: any): Observable<any> {\r\n    const url = `${this.v2BaseUrl}/ava/force/da/agent`;\r\n    return this.http.post(url, payload, this.headers).pipe(\r\n      map((response: any) => {\r\n        return response;\r\n      })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Edit individual agent data\r\n   * @param payload - The individual agent data to update\r\n   * @returns Observable with the API response\r\n   */\r\n  public individualAgentEdit(payload: any): Observable<any> {\r\n    const url = `${this.baseUrl}/ava/force/individualAgent`;\r\n    return this.http.put(url, payload, this.headers).pipe(\r\n      map((response: any) => {\r\n        return response;\r\n      })\r\n    );\r\n  }\r\n\r\n\r\n\r\n  /**\r\n   * Update collaborative agent with v2 API\r\n   * @param payload - The collaborative agent data to update\r\n   * @returns Observable with the API response\r\n   */\r\n  public updateCollaborativeAgentV2(payload: any): Observable<any> {\r\n    const url = `${this.v2BaseUrl}/ava/force/da/agent`;\r\n    return this.http.put(url, payload, this.headers).pipe(\r\n      map((response: any) => {\r\n        return response;\r\n      })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Submit collaborative agent change request\r\n   * @param payload - The collaborative agent change request data\r\n   * @returns Observable with the API response\r\n   */\r\n  public submitCollaborativeAgentChangeRequest(payload: any): Observable<any> {\r\n    const url = `${this.v2BaseUrl}/ava/force/da/agent/change_request`;\r\n    return this.http.put(url, payload, this.headers).pipe(\r\n      map((response: any) => {\r\n        return response;\r\n      })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get models for collaborative agents using the direct API\r\n   * @returns Observable with the models array\r\n   */\r\n  public getCollaborativeModels(): Observable<any> {\r\n    const url = `${this.baseUrl}/ava/force/model?modelType=Generative`;\r\n    return this.http.get(url, this.headers).pipe(\r\n      map((response: any) => {\r\n        return response;\r\n      })\r\n    );\r\n  }\r\n\r\n\r\n  /**\r\n   * Delete individual agent by ID (Legacy - kept for backward compatibility)\r\n   * @param agentId - The ID of the agent to delete\r\n   * @returns Observable with the API response\r\n   */\r\n  public deleteAgent(agentId: string): Observable<any> {\r\n    const url = `${this.baseUrl}/ava/force/individualAgent?individualAgentId=${agentId}`;\r\n    return this.http.delete(url, this.headers).pipe(\r\n      map((response: any) => {\r\n        return response;\r\n      })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Delete collaborative agent by ID using v2 API\r\n   * @param agentId - The ID of the collaborative agent to delete\r\n   * @returns Observable with the API response\r\n   */\r\n  public deleteCollaborativeAgent(agentId: string): Observable<any> {\r\n    const userSignature = this.getUserSignature();\r\n    const url = `${this.v2BaseUrl}/ava/force/da/agent/change_request?agentId=${agentId}&modifiedBy=${userSignature}`;\r\n    return this.http.delete(url, this.headers).pipe(\r\n      map((response: any) => {\r\n        return response;\r\n      })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get individual agent minimal details for playground dropdown\r\n   * @returns Observable with individual agents minimal details\r\n   */\r\n  public getIndividualAgentMinDetails(): Observable<any> {\r\n    const url = `${this.baseUrl}/ava/force/individualAgentMinDetails`;\r\n    return this.http.get(url, this.headers).pipe(\r\n      map((response: any) => {\r\n        return response;\r\n      })\r\n    );\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAASA,WAAW,QAAoB,sBAAsB;AAE9D,SAASC,GAAG,QAAoB,MAAM;;;;AAStC;AACA,OAAO,MAAMC,wBAAwB,GAAG,0BAA0B;AAKlE,WAAaC,mBAAmB;EAA1B,MAAOA,mBAAmB;IAUpBC,IAAA;IACAC,YAAA;IACkCC,WAAA;IAXpCC,OAAO;IACPC,SAAS;IACTC,OAAO,GAAG;MAChBA,OAAO,EAAE,IAAIT,WAAW,CAAC;QACvB,cAAc,EAAE;OACjB;KACF;IAEDU,YACUN,IAAgB,EAChBC,YAAiC,EACCC,WAAmC;MAFrE,KAAAF,IAAI,GAAJA,IAAI;MACJ,KAAAC,YAAY,GAAZA,YAAY;MACsB,KAAAC,WAAW,GAAXA,WAAW;MAErD,IAAI,CAACC,OAAO,GAAG,IAAI,CAACD,WAAW,CAACK,UAAU;MAC1C,IAAI,CAACH,SAAS,GAAG,IAAI,CAACF,WAAW,CAACM,YAAY;IAChD;IAEA;;;;IAIQC,gBAAgBA,CAAA;MACtB,OAAO,IAAI,CAACR,YAAY,CAACS,aAAa,EAAE,IAAI,uBAAuB;IACrE;IAEOC,eAAeA,CAAA;MACpB,MAAMC,GAAG,GAAG,GAAG,IAAI,CAACT,OAAO,mBAAmB;MAC9C,OAAO,IAAI,CAACH,IAAI,CAACa,GAAG,CAACD,GAAG,EAAE,IAAI,CAACP,OAAO,CAAC,CAACS,IAAI,CAC1CjB,GAAG,CAAEkB,QAAa,IAAI;QAClB,OAAOA,QAAQ;MACnB,CAAC,CAAC,CACH;IACH;IAEA;;;;IAIOC,sBAAsBA,CAAA;MAC3B,MAAMJ,GAAG,GAAG,GAAG,IAAI,CAACT,OAAO,6BAA6B;MACxD,OAAO,IAAI,CAACH,IAAI,CAACa,GAAG,CAACD,GAAG,EAAE,IAAI,CAACP,OAAO,CAAC,CAACS,IAAI,CAC1CjB,GAAG,CAAEkB,QAAa,IAAI;QACpB,OAAOA,QAAQ;MACjB,CAAC,CAAC,CACH;IACH;IAEA;;;;;;;IAOOE,mCAAmCA,CAACC,IAAY,EAAEC,OAAe,EAAEC,SAAkB;MAC1F,MAAMR,GAAG,GAAG,GAAG,IAAI,CAACR,SAAS,8BAA8B;MAC3D,OAAO,IAAI,CAACJ,IAAI,CAACa,GAAG,CAAMD,GAAG,CAAC;IAChC;IAEA;;;;;;IAMOS,+BAA+BA,CAACH,IAAA,GAAe,CAAC,EAAEC,OAAA,GAAkB,EAAE;MAC3E,MAAMP,GAAG,GAAG,GAAG,IAAI,CAACR,SAAS,qBAAqB;MAClD,MAAMkB,MAAM,GAAG;QACbJ,IAAI,EAAEA,IAAI,CAACK,QAAQ,EAAE;QACrBJ,OAAO,EAAEA,OAAO,CAACI,QAAQ,EAAE;QAC3BH,SAAS,EAAE;OACZ;MAED,OAAO,IAAI,CAACpB,IAAI,CAACa,GAAG,CAACD,GAAG,EAAE;QAAE,GAAG,IAAI,CAACP,OAAO;QAAEiB;MAAM,CAAE,CAAC,CAACR,IAAI,CACzDjB,GAAG,CAAEkB,QAAa,IAAI;QACpB,OAAOA,QAAQ;MACjB,CAAC,CAAC,CACH;IACH;IAEA;;;;;IAKOS,gCAAgCA,CAACC,OAAe;MACrD,MAAMb,GAAG,GAAG,GAAG,IAAI,CAACR,SAAS,qBAAqB;MAClD,MAAMkB,MAAM,GAAG;QACbG,OAAO,EAAEA;OACV;MAED,OAAO,IAAI,CAACzB,IAAI,CAACa,GAAG,CAACD,GAAG,EAAE;QAAE,GAAG,IAAI,CAACP,OAAO;QAAEiB;MAAM,CAAE,CAAC,CAACR,IAAI,CACzDjB,GAAG,CAAEkB,QAAa,IAAI;QACpB,OAAOA,QAAQ;MACjB,CAAC,CAAC,CACH;IACH;IAEOW,SAASA,CAAA;MACd,MAAMd,GAAG,GAAG,GAAG,IAAI,CAACT,OAAO,kBAAkB;MAC7C,OAAO,IAAI,CAACH,IAAI,CAACa,GAAG,CAACD,GAAG,EAAE,IAAI,CAACP,OAAO,CAAC,CAACS,IAAI,CAC1CjB,GAAG,CAAEkB,QAAa,IAAI;QAClB,OAAOA,QAAQ;MACnB,CAAC,CAAC,CACH;IACH;IAEA;;;;;IAKOY,gBAAgBA,CAACC,WAAmB;MACzC,IAAI,CAACA,WAAW,EAAE,OAAO,EAAE;MAE3B,MAAMC,UAAU,GAAGD,WAAW,CAACE,KAAK,CAAC,GAAG,CAAC;MACzC,OAAOD,UAAU,CACdE,MAAM,CAAEC,IAAY,IAAKA,IAAI,CAACC,IAAI,EAAE,CAAC,CACrCpC,GAAG,CAAEmC,IAAY,IAAI;QACpB,MAAM,CAACE,KAAK,EAAEC,IAAI,CAAC,GAAGH,IAAI,CAACF,KAAK,CAAC,GAAG,CAAC;QACrC,OAAO;UAAEI,KAAK,EAAEA,KAAK,EAAED,IAAI,EAAE,IAAI,EAAE;UAAEE,IAAI,EAAEA,IAAI,EAAEF,IAAI,EAAE,IAAI;QAAE,CAAE;MACjE,CAAC,CAAC;IACN;IAEA;;;;IAIOG,mBAAmBA,CAAA;MACxB,OAAO,IAAI,CAACV,SAAS,EAAE,CAACZ,IAAI,CAC1BjB,GAAG,CAAEkB,QAAa,IAAI;QACpB,MAAMsB,cAAc,GAAGtB,QAAQ,CAACsB,cAAc,IAAI,EAAE;QACpD,MAAMC,aAAa,GAAGD,cAAc,CAACE,IAAI,CAAEC,QAAa,IAAKA,QAAQ,CAACC,UAAU,KAAK,CAAC,CAAC;QAEvF,IAAIH,aAAa,EAAE;UACjB,MAAMI,UAAU,GAAGJ,aAAa,CAACK,MAAM,CAACJ,IAAI,CAAEK,KAAU,IAAKA,KAAK,CAACC,SAAS,KAAK,OAAO,CAAC;UACzF,IAAIH,UAAU,IAAIA,UAAU,CAACd,WAAW,EAAE;YACxC,OAAO,IAAI,CAACD,gBAAgB,CAACe,UAAU,CAACd,WAAW,CAAC,CAAC/B,GAAG,CAACiD,MAAM,KAAK;cAClEC,EAAE,EAAED,MAAM,CAACZ,KAAK;cAChBC,IAAI,EAAEW,MAAM,CAACX,IAAI;cACjBa,IAAI,EAAE,OAAO;cACbC,IAAI,EAAE,yBAAyB;cAC/BC,WAAW,EAAE,UAAUJ,MAAM,CAACX,IAAI;aACnC,CAAC,CAAC;UACL;QACF;QACA,OAAO,EAAE;MACX,CAAC,CAAC,CACH;IACH;IAEA;;;;IAIOgB,2BAA2BA,CAAA;MAChC,OAAO,IAAI,CAACzB,SAAS,EAAE,CAACZ,IAAI,CAC1BjB,GAAG,CAAEkB,QAAa,IAAI;QACpB,MAAMsB,cAAc,GAAGtB,QAAQ,CAACsB,cAAc,IAAI,EAAE;QACpD,MAAMe,WAAW,GAAGf,cAAc,CAACE,IAAI,CAAEC,QAAa,IAAKA,QAAQ,CAACC,UAAU,KAAK,CAAC,CAAC;QAErF,IAAIW,WAAW,EAAE;UACf,MAAMC,cAAc,GAAGD,WAAW,CAACT,MAAM,CAACJ,IAAI,CAAEK,KAAU,IAAKA,KAAK,CAACC,SAAS,KAAK,wBAAwB,CAAC;UAC5G,IAAIQ,cAAc,IAAIA,cAAc,CAACzB,WAAW,EAAE;YAChD,OAAO,IAAI,CAACD,gBAAgB,CAAC0B,cAAc,CAACzB,WAAW,CAAC,CAAC/B,GAAG,CAACiD,MAAM,KAAK;cACtEC,EAAE,EAAED,MAAM,CAACZ,KAAK;cAChBC,IAAI,EAAEW,MAAM,CAACX,IAAI;cACjBa,IAAI,EAAE,WAAW;cACjBC,IAAI,EAAE,6BAA6B;cACnCC,WAAW,EAAE,mBAAmBJ,MAAM,CAACX,IAAI;aAC5C,CAAC,CAAC;UACL;QACF;QACA,OAAO,EAAE;MACX,CAAC,CAAC,CACH;IACH;IAEA;;;;IAIOmB,uBAAuBA,CAAA;MAC5B,OAAO,IAAI,CAAC5B,SAAS,EAAE,CAACZ,IAAI,CAC1BjB,GAAG,CAAEkB,QAAa,IAAI;QACpB,MAAMsB,cAAc,GAAGtB,QAAQ,CAACsB,cAAc,IAAI,EAAE;QACpD,MAAMkB,aAAa,GAAGlB,cAAc,CAACE,IAAI,CAAEC,QAAa,IAAKA,QAAQ,CAACC,UAAU,KAAK,CAAC,CAAC;QAEvF,IAAIc,aAAa,EAAE;UACjB;UACA,OAAOA,aAAa,CAACZ,MAAM,CACxBZ,MAAM,CAAEa,KAAU,IAAKA,KAAK,CAACY,SAAS,KAAK,QAAQ,CAAC,CAAC;UAAA,CACrD3D,GAAG,CAAE+C,KAAU,KAAM;YACpBG,EAAE,EAAEH,KAAK,CAACa,OAAO,CAAClC,QAAQ,EAAE;YAC5BY,IAAI,EAAES,KAAK,CAACc,SAAS;YACrBC,IAAI,EAAEf,KAAK,CAACC,SAAS;YACrBG,IAAI,EAAE,WAAW;YACjBC,IAAI,EAAE,6BAA6B;YACnCC,WAAW,EAAEN,KAAK,CAACgB,SAAS,IAAI,cAAchB,KAAK,CAACc,SAAS;WAC9D,CAAC,CAAC;QACP;QACA,OAAO,EAAE;MACX,CAAC,CAAC,CACH;IACH;IAEA;;;;;IAKOG,YAAYA,CAACpC,OAAe;MACjC,MAAMb,GAAG,GAAG,GAAG,IAAI,CAACT,OAAO,gDAAgDsB,OAAO,EAAE;MACpF,OAAO,IAAI,CAACzB,IAAI,CAACa,GAAG,CAACD,GAAG,EAAE,IAAI,CAACP,OAAO,CAAC,CAACS,IAAI,CAC1CjB,GAAG,CAAEkB,QAAa,IAAI;QACpB,OAAOA,QAAQ;MACjB,CAAC,CAAC,CACH;IACH;IAEA;;;;;IAKO+C,mBAAmBA,CAACC,OAAY;MACrC,MAAMnD,GAAG,GAAG,GAAG,IAAI,CAACT,OAAO,4BAA4B;MACvD,OAAO,IAAI,CAACH,IAAI,CAACgE,IAAI,CAACpD,GAAG,EAAEmD,OAAO,EAAE,IAAI,CAAC1D,OAAO,CAAC,CAACS,IAAI,CACpDjB,GAAG,CAAEkB,QAAa,IAAI;QACpB,OAAOA,QAAQ;MACjB,CAAC,CAAC,CACH;IACH;IAIA;;;;;IAKOkD,0BAA0BA,CAACF,OAAY;MAC5C,MAAMnD,GAAG,GAAG,GAAG,IAAI,CAACR,SAAS,qBAAqB;MAClD,OAAO,IAAI,CAACJ,IAAI,CAACgE,IAAI,CAACpD,GAAG,EAAEmD,OAAO,EAAE,IAAI,CAAC1D,OAAO,CAAC,CAACS,IAAI,CACpDjB,GAAG,CAAEkB,QAAa,IAAI;QACpB,OAAOA,QAAQ;MACjB,CAAC,CAAC,CACH;IACH;IAEA;;;;;IAKOmD,mBAAmBA,CAACH,OAAY;MACrC,MAAMnD,GAAG,GAAG,GAAG,IAAI,CAACT,OAAO,4BAA4B;MACvD,OAAO,IAAI,CAACH,IAAI,CAACmE,GAAG,CAACvD,GAAG,EAAEmD,OAAO,EAAE,IAAI,CAAC1D,OAAO,CAAC,CAACS,IAAI,CACnDjB,GAAG,CAAEkB,QAAa,IAAI;QACpB,OAAOA,QAAQ;MACjB,CAAC,CAAC,CACH;IACH;IAIA;;;;;IAKOqD,0BAA0BA,CAACL,OAAY;MAC5C,MAAMnD,GAAG,GAAG,GAAG,IAAI,CAACR,SAAS,qBAAqB;MAClD,OAAO,IAAI,CAACJ,IAAI,CAACmE,GAAG,CAACvD,GAAG,EAAEmD,OAAO,EAAE,IAAI,CAAC1D,OAAO,CAAC,CAACS,IAAI,CACnDjB,GAAG,CAAEkB,QAAa,IAAI;QACpB,OAAOA,QAAQ;MACjB,CAAC,CAAC,CACH;IACH;IAEA;;;;;IAKOsD,qCAAqCA,CAACN,OAAY;MACvD,MAAMnD,GAAG,GAAG,GAAG,IAAI,CAACR,SAAS,oCAAoC;MACjE,OAAO,IAAI,CAACJ,IAAI,CAACmE,GAAG,CAACvD,GAAG,EAAEmD,OAAO,EAAE,IAAI,CAAC1D,OAAO,CAAC,CAACS,IAAI,CACnDjB,GAAG,CAAEkB,QAAa,IAAI;QACpB,OAAOA,QAAQ;MACjB,CAAC,CAAC,CACH;IACH;IAEA;;;;IAIOuD,sBAAsBA,CAAA;MAC3B,MAAM1D,GAAG,GAAG,GAAG,IAAI,CAACT,OAAO,uCAAuC;MAClE,OAAO,IAAI,CAACH,IAAI,CAACa,GAAG,CAACD,GAAG,EAAE,IAAI,CAACP,OAAO,CAAC,CAACS,IAAI,CAC1CjB,GAAG,CAAEkB,QAAa,IAAI;QACpB,OAAOA,QAAQ;MACjB,CAAC,CAAC,CACH;IACH;IAGA;;;;;IAKOwD,WAAWA,CAAC9C,OAAe;MAChC,MAAMb,GAAG,GAAG,GAAG,IAAI,CAACT,OAAO,gDAAgDsB,OAAO,EAAE;MACpF,OAAO,IAAI,CAACzB,IAAI,CAACwE,MAAM,CAAC5D,GAAG,EAAE,IAAI,CAACP,OAAO,CAAC,CAACS,IAAI,CAC7CjB,GAAG,CAAEkB,QAAa,IAAI;QACpB,OAAOA,QAAQ;MACjB,CAAC,CAAC,CACH;IACH;IAEA;;;;;IAKO0D,wBAAwBA,CAAChD,OAAe;MAC7C,MAAMiD,aAAa,GAAG,IAAI,CAACjE,gBAAgB,EAAE;MAC7C,MAAMG,GAAG,GAAG,GAAG,IAAI,CAACR,SAAS,8CAA8CqB,OAAO,eAAeiD,aAAa,EAAE;MAChH,OAAO,IAAI,CAAC1E,IAAI,CAACwE,MAAM,CAAC5D,GAAG,EAAE,IAAI,CAACP,OAAO,CAAC,CAACS,IAAI,CAC7CjB,GAAG,CAAEkB,QAAa,IAAI;QACpB,OAAOA,QAAQ;MACjB,CAAC,CAAC,CACH;IACH;IAEA;;;;IAIO4D,4BAA4BA,CAAA;MACjC,MAAM/D,GAAG,GAAG,GAAG,IAAI,CAACT,OAAO,sCAAsC;MACjE,OAAO,IAAI,CAACH,IAAI,CAACa,GAAG,CAACD,GAAG,EAAE,IAAI,CAACP,OAAO,CAAC,CAACS,IAAI,CAC1CjB,GAAG,CAAEkB,QAAa,IAAI;QACpB,OAAOA,QAAQ;MACjB,CAAC,CAAC,CACH;IACH;;uCA7VWhB,mBAAmB,EAAA6E,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,mBAAA,GAAAL,EAAA,CAAAC,QAAA,CAYpB/E,wBAAwB;IAAA;;aAZvBC,mBAAmB;MAAAmF,OAAA,EAAnBnF,mBAAmB,CAAAoF,IAAA;MAAAC,UAAA,EAFlB;IAAM;;SAEPrF,mBAAmB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}