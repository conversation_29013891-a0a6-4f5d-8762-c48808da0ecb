{"ast": null, "code": "import { CommonModule, formatDate } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { ApprovalCardComponent, IconComponent, AvaTextboxComponent, TextCardComponent, PopupComponent, ConfirmationPopupComponent, AvaTagComponent, ButtonComponent } from '@ava/play-comp-library';\nimport approvalText from '../constants/approval.json';\nimport { debounceTime, distinctUntilChanged, map, startWith } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../shared/services/shared-api-service.service\";\nimport * as i3 from \"../../../shared/services/approval.service\";\nimport * as i4 from \"@angular/forms\";\nconst _c0 = () => ({\n  background: \"linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)\",\n  \"--button-effect-color\": \"33, 90, 214\"\n});\nconst _c1 = () => ({\n  \"border\": \"2px solid transparent\",\n  \"background-image\": \"linear-gradient(#ffffff, #ffffff), linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)\",\n  \"background-origin\": \"border-box\",\n  \"background-clip\": \"padding-box, border-box\",\n  \"--button-effect-color\": \"33, 90, 214\"\n});\nfunction ApprovalToolsComponent_Conditional_14_For_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"ava-approval-card\", 18)(2, \"div\", 19);\n    i0.ɵɵelement(3, \"ava-icon\", 20);\n    i0.ɵɵelementStart(4, \"div\", 21)(5, \"h2\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"ava-tag\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 23)(9, \"div\", 24);\n    i0.ɵɵelement(10, \"ava-tag\", 25)(11, \"ava-tag\", 26)(12, \"ava-tag\", 27)(13, \"ava-tag\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 29)(15, \"div\", 30);\n    i0.ɵɵelement(16, \"ava-icon\", 31);\n    i0.ɵɵelementStart(17, \"span\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 32);\n    i0.ɵɵelement(20, \"ava-icon\", 33);\n    i0.ɵɵelementStart(21, \"span\");\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(23, \"div\", 34)(24, \"div\", 35)(25, \"div\", 36)(26, \"span\", 37);\n    i0.ɵɵtext(27, \"Execution Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\");\n    i0.ɵɵelement(29, \"ava-icon\", 38);\n    i0.ɵɵelementStart(30, \"span\");\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(32, \"div\", 39)(33, \"ava-button\", 40);\n    i0.ɵɵlistener(\"userClick\", function ApprovalToolsComponent_Conditional_14_For_3_Template_ava_button_userClick_33_listener() {\n      const $index_r2 = i0.ɵɵrestoreView(_r1).$index;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.handleTesting($index_r2));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"ava-button\", 41);\n    i0.ɵɵlistener(\"userClick\", function ApprovalToolsComponent_Conditional_14_For_3_Template_ava_button_userClick_34_listener() {\n      const $index_r2 = i0.ɵɵrestoreView(_r1).$index;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.rejectApproval($index_r2));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"ava-button\", 42);\n    i0.ɵɵlistener(\"userClick\", function ApprovalToolsComponent_Conditional_14_For_3_Template_ava_button_userClick_35_listener() {\n      const $index_r2 = i0.ɵɵrestoreView(_r1).$index;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.approveApproval($index_r2));\n    });\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(item_r4.session1.title);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r2.currentTab);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(item_r4.session3[0].label);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(item_r4.session3[1].label);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(item_r4 == null ? null : item_r4.session4.status);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", ctx_r2.labels.test)(\"customStyles\", i0.ɵɵpureFunction0(11, _c1));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", ctx_r2.labels.sendback)(\"customStyles\", i0.ɵɵpureFunction0(12, _c1));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", ctx_r2.labels.approve)(\"customStyles\", i0.ɵɵpureFunction0(13, _c0));\n  }\n}\nfunction ApprovalToolsComponent_Conditional_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵrepeaterCreate(2, ApprovalToolsComponent_Conditional_14_For_3_Template, 36, 14, \"div\", 17, i0.ɵɵrepeaterTrackByIndex);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" All - \", ctx_r2.totalRecords, \" \", ctx_r2.currentTab, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(ctx_r2.consoleApproval.contents);\n  }\n}\nfunction ApprovalToolsComponent_Conditional_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" All \", ctx_r2.currentTab, \" have been successfully approved. No pending actions. \");\n  }\n}\nexport let ApprovalToolsComponent = /*#__PURE__*/(() => {\n  class ApprovalToolsComponent {\n    router;\n    apiService;\n    approvalService;\n    fb;\n    appLabels = approvalText.labels;\n    totalApprovedApprovals = 20;\n    totalPendingApprovals = 15;\n    totalApprovals = 60;\n    isBasicCollapsed = false;\n    quickActionsExpanded = true;\n    consoleApproval = {};\n    options = [];\n    basicSidebarItems = [];\n    quickActions = [];\n    toolReviews = [];\n    filteredToolReviews = [];\n    workflowReviews = [];\n    agentsReviews = [];\n    currentToolsPage = 1;\n    currentAgentsPage = 1;\n    currentWorkflowsPage = 1;\n    pageSize = 50;\n    totalRecords = 0;\n    isDeleted = false;\n    currentTab = 'Tools';\n    showToolApprovalPopup = false;\n    showInfoPopup = false;\n    showErrorPopup = false;\n    infoMessage = '';\n    selectedIndex = 0;\n    showFeedbackPopup = false;\n    searchForm;\n    labels = approvalText.labels;\n    approvedAgentId = null;\n    constructor(router, apiService, approvalService, fb) {\n      this.router = router;\n      this.apiService = apiService;\n      this.approvalService = approvalService;\n      this.fb = fb;\n      this.labels = approvalText.labels;\n      this.options = [{\n        name: this.labels.electronics,\n        value: 'electronics'\n      }, {\n        name: this.labels.clothing,\n        value: 'clothing'\n      }, {\n        name: this.labels.books,\n        value: 'books'\n      }];\n      this.basicSidebarItems = [{\n        id: '1',\n        icon: 'hammer',\n        text: this.labels.agents,\n        route: '',\n        active: true\n      }, {\n        id: '2',\n        icon: 'circle-check',\n        text: this.labels.workflows,\n        route: ''\n      }, {\n        id: '3',\n        icon: 'bot',\n        text: this.labels.tools,\n        route: ''\n      }];\n      this.quickActions = [{\n        icon: 'awe_agents',\n        label: this.labels.agents,\n        route: ''\n      }, {\n        icon: 'awe_workflows',\n        label: this.labels.workflows,\n        route: ''\n      }, {\n        icon: 'awe_tools',\n        label: this.labels.tools,\n        route: ''\n      }];\n      this.searchForm = this.fb.group({\n        search: ['']\n      });\n    }\n    ngOnInit() {\n      this.searchList();\n      this.totalApprovals = 60;\n      this.loadToolReviews();\n    }\n    searchList() {\n      console.log(this.searchForm.get('search')?.value);\n      this.searchForm.get('search').valueChanges.pipe(startWith(''), debounceTime(300), distinctUntilChanged(), map(value => value?.toLowerCase() ?? '')).subscribe(searchText => {\n        this.applyFilter(searchText);\n      });\n    }\n    applyFilter(text) {\n      const lower = text;\n      if (!text) {\n        this.updateConsoleApproval(this.toolReviews, 'tool');\n        return;\n      }\n      this.filteredToolReviews = this.toolReviews.filter(item => item.toolName?.toLowerCase().includes(lower));\n      this.updateConsoleApproval(this.filteredToolReviews, 'tool');\n    }\n    onSelectionChange(data) {\n      console.log('Selection changed:', data);\n    }\n    uClick(i) {\n      console.log('log' + i);\n    }\n    toggleQuickActions() {\n      this.quickActionsExpanded = !this.quickActionsExpanded;\n    }\n    onBasicCollapseToggle(isCollapsed) {\n      this.isBasicCollapsed = isCollapsed;\n      console.log('Basic sidebar collapsed:', isCollapsed);\n    }\n    onBasicItemClick(item) {\n      this.basicSidebarItems.forEach(i => i.active = false);\n      item.active = true;\n      console.log(item);\n    }\n    toRequestStatus(value) {\n      return value === 'approved' || value === 'rejected' || value === 'review' ? value : 'review';\n    }\n    loadToolReviews() {\n      this.approvalService.getAllReviewTools(this.currentToolsPage, this.pageSize, this.isDeleted).subscribe(response => {\n        if (this.currentToolsPage > 1) {\n          this.toolReviews = [...this.toolReviews, ...response.userToolReviewDetails];\n        } else {\n          this.toolReviews = response?.userToolReviewDetails;\n        }\n        this.toolReviews = this.toolReviews.filter(r => r.status !== 'approved');\n        this.filteredToolReviews = this.toolReviews;\n        // console.log('tool reviews ', this.toolReviews);\n        this.totalRecords = this.toolReviews.length;\n        this.updateConsoleApproval(this.toolReviews, 'tool');\n      });\n    }\n    loadMoreTools(page) {\n      this.currentToolsPage = page;\n      this.loadToolReviews();\n    }\n    loadReviews(name) {\n      this.currentTab = name;\n      this.loadToolReviews();\n    }\n    rejectApproval(idx) {\n      console.log(idx);\n      // console.log(this.filteredToolReviews);\n      this.selectedIndex = idx;\n      this.showFeedbackPopup = true;\n    }\n    approveApproval(idx) {\n      console.log(idx);\n      // console.log(this.filteredToolReviews);\n      this.selectedIndex = idx;\n      this.showToolApprovalPopup = true;\n    }\n    handleApproval() {\n      this.handleToolApproval();\n    }\n    handleRejection(feedback) {\n      this.handleToolRejection(feedback);\n    }\n    handleToolApproval() {\n      const toolDetails = this.filteredToolReviews[this.selectedIndex];\n      const id = toolDetails.id;\n      const toolId = toolDetails.toolId;\n      const status = 'approved';\n      const reviewedBy = toolDetails.reviewedBy;\n      this.approvalService.approveTool(id, toolId, status, reviewedBy).subscribe({\n        next: response => {\n          this.infoMessage = response?.message || this.labels.toolSuccessApproveMessage;\n          this.showInfoPopup = true;\n        },\n        error: error => {\n          this.showErrorPopup = true;\n          this.infoMessage = error?.error?.message || this.labels.defaultErrorMessage;\n          console.error('Error:', error);\n        }\n      });\n    }\n    handleToolRejection(feedback) {\n      const toolDetails = this.filteredToolReviews[this.selectedIndex];\n      const id = toolDetails.id;\n      const toolId = toolDetails.toolId;\n      const status = 'rejected';\n      const reviewedBy = toolDetails.reviewedBy;\n      const message = feedback;\n      this.approvalService.rejectTool(id, toolId, status, reviewedBy, message).subscribe({\n        next: response => {\n          this.infoMessage = response?.message || this.labels.toolSuccessRejectMessage;\n          this.showInfoPopup = true;\n        },\n        error: error => {\n          this.showErrorPopup = true;\n          this.infoMessage = error?.error?.message || this.labels.defaultErrorMessage;\n          console.error('Error:', error);\n        }\n      });\n    }\n    handleInfoPopup() {\n      this.showInfoPopup = false;\n      this.loadToolReviews();\n    }\n    handleTesting(index) {\n      console.log(index);\n      const toolId = this.filteredToolReviews[index].toolId;\n      this.redirectToToolPlayground(toolId);\n    }\n    redirectToToolPlayground(id) {\n      this.router.navigate(['/libraries/tools/execute', id]);\n    }\n    updateConsoleApproval(data, type) {\n      this.consoleApproval = {\n        contents: data?.map(req => {\n          const statusIcons = {\n            approved: 'circle-check-big',\n            rejected: 'circle-x',\n            review: 'clock'\n          };\n          const statusTexts = {\n            approved: this.labels.approved,\n            rejected: this.labels.rejected,\n            review: this.labels.review\n          };\n          const statusKey = this.toRequestStatus(req?.status);\n          const specificId = req.toolId;\n          const title = req.toolName;\n          return {\n            id: req.id,\n            refId: specificId,\n            type: type,\n            session1: {\n              title: title,\n              labels: [{\n                name: type,\n                color: 'success',\n                background: 'red',\n                type: 'normal'\n              }, {\n                name: req.changeRequestType,\n                color: req.changeRequestType === 'update' ? 'error' : 'info',\n                background: 'red',\n                type: 'pill'\n              }]\n            },\n            session2: [{\n              name: type,\n              color: 'default',\n              background: 'red',\n              type: 'normal'\n            }, {\n              name: req.status,\n              color: 'default',\n              background: 'red',\n              type: 'normal'\n            }],\n            session3: [{\n              iconName: 'user',\n              label: req.requestedBy\n            }, {\n              iconName: 'calendar-days',\n              label: formatDate(req?.requestedAt, 'dd MMM yyyy', 'en-IN')\n            }],\n            session4: {\n              status: statusTexts[statusKey],\n              iconName: statusIcons[statusKey]\n            }\n          };\n        }),\n        footer: {}\n      };\n    }\n    static ɵfac = function ApprovalToolsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ApprovalToolsComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.SharedApiServiceService), i0.ɵɵdirectiveInject(i3.ApprovalService), i0.ɵɵdirectiveInject(i4.FormBuilder));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ApprovalToolsComponent,\n      selectors: [[\"app-approval-tools\"]],\n      decls: 20,\n      vars: 41,\n      consts: [[1, \"approval-right-screen\"], [1, \"approval-title-filter\"], [3, \"type\", \"iconName\", \"title\", \"value\", \"description\"], [1, \"filter-section\"], [1, \"search-bars\"], [1, \"textbox\", \"section\"], [3, \"formGroup\"], [\"formControlName\", \"search\", 3, \"placeholder\"], [\"slot\", \"icon-start\", \"iconName\", \"search\", \"iconColor\", \"var(--color-brand-primary)\", 3, \"iconSize\"], [\"label\", \"Primary\", \"variant\", \"primary\", \"iconName\", \"calendar-days\", \"iconPosition\", \"only\", 3, \"userClick\", \"customStyles\"], [1, \"approval-card-section\"], [1, \"no-pending-message\"], [3, \"confirm\", \"cancel\", \"closed\", \"show\", \"title\", \"message\", \"showClose\", \"showCancel\", \"showConfirm\", \"confirmButtonLabel\", \"confirmButtonVariant\", \"confirmButtonBackground\"], [\"messageAlignment\", \"center\", \"title\", \"SUCCESS!\", \"headerIconName\", \"circle-check\", \"iconColor\", \"green\", 3, \"closed\", \"show\", \"message\", \"showHeaderIcon\", \"showClose\"], [\"title\", \"Confirm Send Back\", \"confirmationLabel\", \"Send Back\", 3, \"closed\", \"confirm\", \"show\", \"message\"], [\"messageAlignment\", \"center\", \"title\", \"FAILED!\", \"headerIconName\", \"circle-x\", \"iconColor\", \"red\", 3, \"closed\", \"show\", \"message\", \"showHeaderIcon\", \"showClose\"], [1, \"approval-card-header\"], [1, \"approval-card-wrapper\"], [\"height\", \"300\"], [\"header\", \"\"], [\"iconSize\", \"20\", \"iconName\", \"ellipsis-vertical\"], [1, \"header\"], [\"color\", \"info\", \"size\", \"sm\", 3, \"label\"], [\"content\", \"\", 1, \"a-content\"], [1, \"box\", \"tag-wrapper\"], [\"label\", \"Individual\", \"size\", \"sm\"], [\"label\", \"Ascendion\", \"size\", \"sm\"], [\"label\", \"Digital Ascender\", \"size\", \"sm\"], [\"label\", \"Platform Engineering\", \"size\", \"sm\"], [1, \"box\", \"info-wrapper\"], [1, \"f\"], [\"iconSize\", \"13\", \"iconName\", \"user\"], [1, \"ml-auto\", \"s\"], [\"iconSize\", \"20\", \"iconName\", \"calendar-days\"], [\"footer\", \"\"], [1, \"footer-content\"], [1, \"footer-left\"], [1, \"ex\"], [\"iconSize\", \"20\", \"iconName\", \"circle-check-big\"], [1, \"footer-right\"], [\"variant\", \"secondary\", \"size\", \"small\", \"state\", \"default\", \"iconName\", \"play\", \"iconPosition\", \"left\", 3, \"userClick\", \"label\", \"customStyles\"], [\"variant\", \"secondary\", \"size\", \"small\", \"state\", \"default\", \"iconName\", \"move-left\", \"iconPosition\", \"left\", 3, \"userClick\", \"label\", \"customStyles\"], [\"variant\", \"primary\", \"size\", \"small\", \"state\", \"default\", \"iconName\", \"Check\", \"iconPosition\", \"left\", 3, \"userClick\", \"label\", \"customStyles\"]],\n      template: function ApprovalToolsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"ava-text-card\", 2)(3, \"ava-text-card\", 2)(4, \"ava-text-card\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 3);\n          i0.ɵɵelement(6, \"div\", 4);\n          i0.ɵɵelementStart(7, \"div\", 5)(8, \"div\")(9, \"form\", 6)(10, \"ava-textbox\", 7);\n          i0.ɵɵelement(11, \"ava-icon\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"ava-button\", 9);\n          i0.ɵɵlistener(\"userClick\", function ApprovalToolsComponent_Template_ava_button_userClick_12_listener($event) {\n            return ctx.uClick($event);\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(13, \"div\", 10);\n          i0.ɵɵtemplate(14, ApprovalToolsComponent_Conditional_14_Template, 4, 2)(15, ApprovalToolsComponent_Conditional_15_Template, 2, 1, \"div\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"ava-popup\", 12);\n          i0.ɵɵlistener(\"confirm\", function ApprovalToolsComponent_Template_ava_popup_confirm_16_listener() {\n            return ctx.handleApproval();\n          })(\"cancel\", function ApprovalToolsComponent_Template_ava_popup_cancel_16_listener() {\n            return ctx.showToolApprovalPopup = false;\n          })(\"closed\", function ApprovalToolsComponent_Template_ava_popup_closed_16_listener() {\n            return ctx.showToolApprovalPopup = false;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"ava-popup\", 13);\n          i0.ɵɵlistener(\"closed\", function ApprovalToolsComponent_Template_ava_popup_closed_17_listener() {\n            return ctx.handleInfoPopup();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"ava-confirmation-popup\", 14);\n          i0.ɵɵlistener(\"closed\", function ApprovalToolsComponent_Template_ava_confirmation_popup_closed_18_listener() {\n            return ctx.showFeedbackPopup = false;\n          })(\"confirm\", function ApprovalToolsComponent_Template_ava_confirmation_popup_confirm_18_listener($event) {\n            return ctx.handleRejection($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"ava-popup\", 15);\n          i0.ɵɵlistener(\"closed\", function ApprovalToolsComponent_Template_ava_popup_closed_19_listener() {\n            return ctx.showErrorPopup = false;\n          });\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"type\", \"default\")(\"iconName\", \"hourglass\")(\"title\", ctx.labels.totalApprovals)(\"value\", ctx.totalApprovals)(\"description\", ctx.currentTab + \" \" + ctx.labels.whichAreRequestedForApproval);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"type\", \"default\")(\"iconName\", \"shield-alert\")(\"title\", ctx.labels.totalApprovedApprovals)(\"value\", ctx.totalApprovedApprovals)(\"description\", ctx.currentTab + \" \" + ctx.labels.whichAreApproved);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"type\", \"default\")(\"iconName\", \"hourglass\")(\"title\", ctx.labels.totalPendingApprovals)(\"value\", ctx.totalPendingApprovals)(\"description\", ctx.labels.all + \" \" + ctx.currentTab + \" \" + ctx.labels.awaitingApproval);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"formGroup\", ctx.searchForm);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"placeholder\", ctx.labels.searchPlaceholder);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"iconSize\", 16);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"customStyles\", i0.ɵɵpureFunction0(40, _c0));\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(ctx.totalRecords > 0 ? 14 : 15);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"show\", ctx.showToolApprovalPopup)(\"title\", ctx.labels.confirmApproval)(\"message\", ctx.labels.youAreAboutToApproveThis + \" \" + ctx.currentTab + \". \" + ctx.labels.itWillBeActiveAndAvailableIn + \" \" + ctx.currentTab + \" \" + ctx.labels.catalogueForUsersToExecute)(\"showClose\", true)(\"showCancel\", true)(\"showConfirm\", true)(\"confirmButtonLabel\", ctx.labels.approve)(\"confirmButtonVariant\", \"primary\")(\"confirmButtonBackground\", \"#dc3545\");\n          i0.ɵɵadvance();\n          i0.ɵɵpropertyInterpolate(\"message\", ctx.infoMessage);\n          i0.ɵɵproperty(\"show\", ctx.showInfoPopup)(\"showHeaderIcon\", true)(\"showClose\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵpropertyInterpolate1(\"message\", \"This \", ctx.currentTab, \" will be send back for corrections and modification. Kindly comment what needs to be done.\");\n          i0.ɵɵproperty(\"show\", ctx.showFeedbackPopup);\n          i0.ɵɵadvance();\n          i0.ɵɵpropertyInterpolate(\"message\", ctx.infoMessage);\n          i0.ɵɵproperty(\"show\", ctx.showErrorPopup)(\"showHeaderIcon\", true)(\"showClose\", true);\n        }\n      },\n      dependencies: [CommonModule, RouterModule, ApprovalCardComponent, IconComponent, AvaTextboxComponent, TextCardComponent, ReactiveFormsModule, i4.ɵNgNoValidate, i4.NgControlStatus, i4.NgControlStatusGroup, i4.FormGroupDirective, i4.FormControlName, AvaTagComponent, ButtonComponent, PopupComponent, ConfirmationPopupComponent],\n      styles: [\".approval[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  width: 100%;\\n  height: 100%;\\n}\\n\\n.approval-left-screen[_ngcontent-%COMP%] {\\n  flex: 0 0 70px; \\n\\n  width: 70px;\\n  transition: all var(--transition-speed) ease;\\n  height: 120vh; \\n\\n  overflow: hidden;\\n}\\n.approval-left-screen.quick-actions-expanded[_ngcontent-%COMP%] {\\n  flex: 0 0 250px;\\n  margin-right: 15px;\\n}\\n\\n\\n\\n.approval-right-screen[_ngcontent-%COMP%] {\\n  flex: 1; \\n\\n  padding: 1rem; \\n\\n  overflow-y: auto; \\n\\n  background-color: #ffffff; \\n\\n}\\n\\n\\n\\n.approval-title-filter[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  margin-bottom: 1rem; \\n\\n}\\n\\n\\n\\n.approvals-title[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n  font-size: 1.2rem;\\n  margin-bottom: 0.5rem;\\n}\\n\\n\\n\\n.filter-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 0 1rem; \\n\\n  margin-bottom: 1rem; \\n\\n}\\n\\n\\n\\n.search-bars[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem; \\n\\n}\\n\\n\\n\\n.textbox.section[_ngcontent-%COMP%] {\\n  margin-left: 1rem;\\n}\\n\\n.textbox.section[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center; \\n\\n  gap: 8px; \\n\\n}\\n\\n.approval-card-header[_ngcontent-%COMP%] {\\n  font-size: 1.25rem; \\n\\n  font-weight: 600; \\n\\n  color: #333; \\n\\n  padding: 0.75rem 1rem; \\n\\n  margin-bottom: 1rem; \\n\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between; \\n\\n}\\n\\n.approval-title-filter[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  justify-content: space-between;\\n  align-items: center;\\n  gap: 1rem;\\n  flex-wrap: nowrap;\\n  width: 100%;\\n  padding: 1rem 0;\\n}\\n\\n.approval-title-filter[_ngcontent-%COMP%]    > ava-text-card[_ngcontent-%COMP%] {\\n  flex: 1 1 22%;\\n  min-width: 200px;\\n}\\n\\n.quick-actions-wrapper[_ngcontent-%COMP%] {\\n  grid-area: quick-actions;\\n  background-color: var(--dashboard-card-bg);\\n  border-radius: var(--border-radius-standard);\\n  display: flex;\\n  flex-direction: column;\\n  width: 55px;\\n  height: 250vh;\\n  transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);\\n  overflow: hidden;\\n  box-shadow: var(--shadow-medium);\\n  border: var(--border-thin);\\n  position: relative;\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n}\\n.quick-actions-wrapper[_ngcontent-%COMP%]:hover {\\n  box-shadow: var(--shadow-hover);\\n}\\n.quick-actions-wrapper.expanded[_ngcontent-%COMP%] {\\n  width: 100%; \\n\\n}\\n@media (min-width: 1900px) and (max-width: 1930px) and (max-height: 1100px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%] {\\n    height: 595px;\\n  }\\n}\\n@media (min-width: 1200px) and (max-width: 1400px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%] {\\n    height: 580px !important;\\n    max-height: 580px !important;\\n  }\\n}\\n@media (max-width: 1200px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%] {\\n    height: 320px;\\n  }\\n}\\n@media (max-width: 992px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    width: 100%;\\n    height: 48px;\\n  }\\n  .quick-actions-wrapper.expanded[_ngcontent-%COMP%] {\\n    height: auto;\\n    max-height: 320px;\\n    width: 100%;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%] {\\n    height: 280px;\\n  }\\n}\\n@media (min-width: 1200px) and (max-width: 1366px) and (max-height: 800px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%] {\\n    height: 100%;\\n  }\\n  .quick-actions-wrapper[_ngcontent-%COMP%]     .card-container {\\n    height: 100%;\\n  }\\n}\\n.quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card {\\n  height: 100% !important;\\n  width: 100% !important;\\n  \\n\\n  \\n\\n}\\n.quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card .card-container {\\n  height: 100% !important;\\n  width: 100% !important;\\n  padding: 0 !important;\\n  overflow: hidden !important;\\n  display: flex !important;\\n  flex-direction: column !important;\\n}\\n.quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card .card-container .card-body {\\n  padding: 0 !important;\\n  height: 100% !important;\\n  display: flex !important;\\n  flex-direction: column !important;\\n}\\n.quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card .card-content {\\n  height: 100% !important;\\n  display: flex !important;\\n  flex-direction: column !important;\\n  padding: 0 !important;\\n}\\n@media (max-width: 992px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card .card-content {\\n    flex-direction: row !important;\\n  }\\n}\\n@media (max-width: 992px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card .card-container {\\n    height: 48px !important;\\n    width: 100% !important;\\n    flex-direction: row !important;\\n  }\\n  .quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card .card-container.expanded {\\n    height: auto !important;\\n  }\\n}\\n@media (min-width: 1200px) and (max-width: 1366px) and (max-height: 800px) {\\n  .quick-actions-wrapper[_ngcontent-%COMP%]     .quick-actions-card .card-container {\\n    width: 100% !important;\\n  }\\n}\\n\\n.quick-actions-content[_ngcontent-%COMP%] {\\n  padding: 20px 16px;\\n  overflow-y: auto;\\n  flex-grow: 1;\\n}\\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px; \\n\\n}\\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-start;\\n  gap: 16px; \\n\\n  padding: 16px 20px; \\n\\n  border-radius: 12px; \\n\\n  border: none;\\n  border: 2px solid transparent;\\n  background: linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%);\\n  background-origin: border-box;\\n  background-clip: padding-box, border-box;\\n  --button-effect-color: 33, 90, 214;\\n  cursor: pointer;\\n  transition: all var(--transition-speed) ease;\\n  width: 100%;\\n  text-align: left;\\n  color: #fff;\\n}\\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]   .action-icon[_ngcontent-%COMP%], \\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]   .action-label[_ngcontent-%COMP%] {\\n  color: #fff;\\n}\\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]:hover {\\n  opacity: 0.9;\\n  background: linear-gradient(103.35deg, #03BDD4 31.33%, #215AD6 100%);\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px var(--dashboard-shadow-hover);\\n}\\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]   .action-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 24px;\\n  height: 24px;\\n}\\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]   .action-icon[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  filter: brightness(0) invert(1); \\n\\n}\\n.quick-actions-content[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]   .action-label[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n  color: linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%); \\n\\n}\\n\\n.action-button.active-action[_ngcontent-%COMP%] {\\n  background: linear-gradient(103.35deg, #03BDD4 31.33%, #215AD6 100%) !important;\\n}\\n\\n.quick-actions-toggle[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  padding: 20px 16px;\\n  padding-bottom: 0px;\\n  cursor: pointer;\\n  transition: background-color 0.35s cubic-bezier(0.4, 0, 0.2, 1);\\n  flex-shrink: 0;\\n  \\n\\n  \\n\\n}\\n.quick-actions-wrapper[_ngcontent-%COMP%]:not(.expanded)   .quick-actions-toggle[_ngcontent-%COMP%] {\\n  padding: 20px 0;\\n  justify-content: center;\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   .toggle-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 8px;\\n  background-color: transparent;\\n  position: relative;\\n  \\n\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   .toggle-button[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  inset: 0;\\n  border-radius: 8px;\\n  padding: 1px;\\n  background: var(--dashboard-gradient);\\n  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);\\n  mask-composite: exclude;\\n  transition: opacity 0.35s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   .toggle-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  transition: transform 0.35s cubic-bezier(0.4, 0, 0.2, 1);\\n  width: 16px;\\n  height: 16px;\\n  stroke: var(--dashboard-toggle-stroke);\\n  z-index: 1;\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   .toggle-button[_ngcontent-%COMP%]   svg.rotate[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   .quick-actions-wrapper[_ngcontent-%COMP%]:not(.expanded)   .toggle-button[_ngcontent-%COMP%] {\\n  background: var(--dashboard-gradient);\\n  transition: background 0.35s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   .quick-actions-wrapper[_ngcontent-%COMP%]:not(.expanded)   .toggle-button[_ngcontent-%COMP%]::before {\\n  display: none;\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   .quick-actions-wrapper[_ngcontent-%COMP%]:not(.expanded)   .toggle-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  stroke: var(--dashboard-toggle-stroke-collapsed);\\n  transition: stroke 0.35s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.quick-actions-toggle[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 580;\\n  font-size: 16px;\\n  color: var(--dashboard-text-primary);\\n  opacity: 1;\\n  transition: opacity 0.35s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.quick-actions-icons[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 16px; \\n\\n  padding: 20px 0; \\n\\n  height: 150vh;\\n}\\n@media (max-width: 992px) {\\n  .quick-actions-icons[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    justify-content: center;\\n    flex-wrap: wrap;\\n    padding: 8px;\\n  }\\n}\\n.quick-actions-icons[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 36px; \\n\\n  height: 36px; \\n\\n  border-radius: 8px; \\n\\n  border: none;\\n  background: linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%);\\n  cursor: pointer;\\n  transition: all var(--transition-speed) ease;\\n}\\n.quick-actions-icons[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(103.35deg, #03BDD4 31.33%, #215AD6 100%);\\n  opacity: 0.9;\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px var(--dashboard-shadow-hover);\\n}\\n.quick-actions-icons[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.quick-actions-icons[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  filter: brightness(0) invert(1); \\n\\n}\\n\\n.icon-button.active-action[_ngcontent-%COMP%] {\\n  background: linear-gradient(103.35deg, #03BDD4 31.33%, #215AD6 100%) !important;\\n}\\n\\n.approval-card-section[_ngcontent-%COMP%] {\\n  border-radius: 24px;\\n  border: 1px solid #DCDCDC;\\n  background: #F8F8F8;\\n  height: 780px;\\n  overflow-y: auto;\\n}\\n\\n.approval-card-wrapper[_ngcontent-%COMP%] {\\n  margin: 10px;\\n}\\n\\n.no-pending-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-top: 20%;\\n  font-size: 1.2rem;\\n  color: #000000;\\n  font-weight: 500;\\n  text-align: center;\\n  background: #f8f8f8;\\n  border-radius: 16px;\\n  min-height: 100px;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .filter-section[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n  }\\n  .search-bars[_ngcontent-%COMP%], \\n   .textbox.section[_ngcontent-%COMP%] {\\n    width: 100%;\\n    margin: 0 0 0.5rem 0;\\n    justify-content: center;\\n  }\\n  .search-bars[_ngcontent-%COMP%] {\\n    flex-wrap: wrap;\\n    gap: 0.5rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return ApprovalToolsComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "formatDate", "ReactiveFormsModule", "RouterModule", "ApprovalCardComponent", "IconComponent", "AvaTextboxComponent", "TextCardComponent", "PopupComponent", "ConfirmationPopupComponent", "AvaTagComponent", "ButtonComponent", "approvalText", "debounceTime", "distinctUntilChanged", "map", "startWith", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ApprovalToolsComponent_Conditional_14_For_3_Template_ava_button_userClick_33_listener", "$index_r2", "ɵɵrestoreView", "_r1", "$index", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "handleTesting", "ApprovalToolsComponent_Conditional_14_For_3_Template_ava_button_userClick_34_listener", "rejectApproval", "ApprovalToolsComponent_Conditional_14_For_3_Template_ava_button_userClick_35_listener", "approveApproval", "ɵɵadvance", "ɵɵtextInterpolate", "item_r4", "session1", "title", "ɵɵpropertyInterpolate", "currentTab", "session3", "label", "session4", "status", "ɵɵproperty", "labels", "test", "ɵɵpureFunction0", "_c1", "sendback", "approve", "_c0", "ɵɵrepeaterCreate", "ApprovalToolsComponent_Conditional_14_For_3_Template", "ɵɵrepeaterTrackByIndex", "ɵɵtextInterpolate2", "totalRecords", "ɵɵrepeater", "consoleApproval", "contents", "ɵɵtextInterpolate1", "ApprovalToolsComponent", "router", "apiService", "approvalService", "fb", "appLabels", "totalApprovedApprovals", "totalPendingApprovals", "totalApprovals", "isBasicCollapsed", "quickActionsExpanded", "options", "basicSidebarItems", "quickActions", "toolReviews", "filteredToolReviews", "workflowReviews", "agentsReviews", "currentToolsPage", "currentAgentsPage", "currentWorkflowsPage", "pageSize", "isDeleted", "showToolApprovalPopup", "showInfoPopup", "showErrorPopup", "infoMessage", "selectedIndex", "showFeedbackPopup", "searchForm", "approvedAgentId", "constructor", "name", "electronics", "value", "clothing", "books", "id", "icon", "text", "agents", "route", "active", "workflows", "tools", "group", "search", "ngOnInit", "searchList", "loadToolReviews", "console", "log", "get", "valueChanges", "pipe", "toLowerCase", "subscribe", "searchText", "applyFilter", "lower", "updateConsoleApproval", "filter", "item", "toolName", "includes", "onSelectionChange", "data", "uClick", "i", "toggleQuickActions", "onBasicCollapseToggle", "isCollapsed", "onBasicItemClick", "for<PERSON>ach", "toRequestStatus", "getAllReviewTools", "response", "userToolReviewDetails", "r", "length", "loadMoreTools", "page", "loadReviews", "idx", "handleApproval", "handleToolApproval", "handleRejection", "feedback", "handleToolRejection", "toolDetails", "toolId", "reviewedBy", "approveTool", "next", "message", "toolSuccessApproveMessage", "error", "defaultErrorMessage", "rejectTool", "toolSuccessRejectMessage", "handleInfoPopup", "index", "redirectToToolPlayground", "navigate", "type", "req", "statusIcons", "approved", "rejected", "review", "statusTexts", "statusKey", "specificId", "refId", "color", "background", "changeRequestType", "session2", "iconName", "requestedBy", "requestedAt", "footer", "ɵɵdirectiveInject", "i1", "Router", "i2", "SharedApiServiceService", "i3", "ApprovalService", "i4", "FormBuilder", "selectors", "decls", "vars", "consts", "template", "ApprovalToolsComponent_Template", "rf", "ctx", "ApprovalToolsComponent_Template_ava_button_userClick_12_listener", "$event", "ɵɵtemplate", "ApprovalToolsComponent_Conditional_14_Template", "ApprovalToolsComponent_Conditional_15_Template", "ApprovalToolsComponent_Template_ava_popup_confirm_16_listener", "ApprovalToolsComponent_Template_ava_popup_cancel_16_listener", "ApprovalToolsComponent_Template_ava_popup_closed_16_listener", "ApprovalToolsComponent_Template_ava_popup_closed_17_listener", "ApprovalToolsComponent_Template_ava_confirmation_popup_closed_18_listener", "ApprovalToolsComponent_Template_ava_confirmation_popup_confirm_18_listener", "ApprovalToolsComponent_Template_ava_popup_closed_19_listener", "whichAreRequestedForApproval", "whichAreApproved", "all", "awaitingApproval", "searchPlaceholder", "ɵɵconditional", "confirmApproval", "youAreAboutToApproveThis", "itWillBeActiveAndAvailableIn", "catalogueForUsersToExecute", "ɵɵpropertyInterpolate1", "ɵNgNoValidate", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\approval\\approval-tools\\approval-tools.component.ts", "C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\approval\\approval-tools\\approval-tools.component.html"], "sourcesContent": ["import { CommonModule, formatDate } from '@angular/common';\r\nimport { Component, CUSTOM_ELEMENTS_SCHEMA, OnInit } from '@angular/core';\r\nimport { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';\r\nimport { Router, RouterModule } from '@angular/router';\r\nimport { ApprovalCardComponent, IconComponent, AvaTextboxComponent, TextCardComponent, PopupComponent, ConfirmationPopupComponent, AvaTagComponent, DropdownOption, ButtonComponent } from '@ava/play-comp-library';\r\nimport approvalText  from '../constants/approval.json';\r\nimport { SharedApiServiceService } from '../../../shared/services/shared-api-service.service';\r\nimport { ApprovalService } from '../../../shared/services/approval.service';\r\nimport { debounceTime, distinctUntilChanged, map, startWith } from 'rxjs';\r\n\r\ntype RequestStatus = 'approved' | 'rejected' | 'review';\r\n\r\n@Component({\r\n  selector: 'app-approval-tools',\r\n  imports: [\r\n    CommonModule,\r\n    RouterModule,\r\n    ApprovalCardComponent,\r\n    IconComponent,\r\n    AvaTextboxComponent,\r\n    TextCardComponent,\r\n    ReactiveFormsModule,\r\n    AvaTagComponent,\r\n    ButtonComponent,\r\n    PopupComponent,\r\n    ConfirmationPopupComponent\r\n  ],\r\n  schemas: [CUSTOM_ELEMENTS_SCHEMA],\r\n  templateUrl: './approval-tools.component.html',\r\n  styleUrls: ['./approval-tools.component.scss']\r\n})\r\n\r\nexport class ApprovalToolsComponent implements OnInit {\r\n    appLabels = approvalText.labels;\r\n\r\n    public totalApprovedApprovals: number = 20;\r\n    public totalPendingApprovals: number = 15;\r\n    public totalApprovals: number = 60;\r\n    public isBasicCollapsed: boolean = false;\r\n    public quickActionsExpanded: boolean = true;\r\n    public consoleApproval: any = {};\r\n    public options: DropdownOption[] = [];\r\n    public basicSidebarItems: any[] = [];\r\n    public quickActions: any[] = [];\r\n    public toolReviews: any[] = [];\r\n    public filteredToolReviews: any[] = [];\r\n    public workflowReviews: any[] = [];\r\n    public agentsReviews: any[] = [];\r\n    public currentToolsPage = 1;\r\n    public currentAgentsPage = 1;\r\n    public currentWorkflowsPage = 1;\r\n    public pageSize = 50;\r\n    public totalRecords = 0;\r\n    public isDeleted = false;\r\n    public currentTab = 'Tools';\r\n    public showToolApprovalPopup = false;\r\n    public showInfoPopup = false;\r\n    public showErrorPopup = false;\r\n    public infoMessage = '';\r\n    public selectedIndex = 0;\r\n    public showFeedbackPopup = false;\r\n    public searchForm!: FormGroup;\r\n    public labels: any = approvalText.labels;\r\n    public approvedAgentId: number | null = null;\r\n  \r\n    constructor(\r\n      private router: Router,\r\n      private apiService: SharedApiServiceService,\r\n      private approvalService: ApprovalService,\r\n      private fb: FormBuilder,\r\n    ) {\r\n      this.labels = approvalText.labels;\r\n      this.options = [\r\n        { name: this.labels.electronics, value: 'electronics' },\r\n        { name: this.labels.clothing, value: 'clothing' },\r\n        { name: this.labels.books, value: 'books' },\r\n      ];\r\n      this.basicSidebarItems = [\r\n        { id: '1', icon: 'hammer', text: this.labels.agents, route: '', active: true },\r\n        { id: '2', icon: 'circle-check', text: this.labels.workflows, route: '' },\r\n        { id: '3', icon: 'bot', text: this.labels.tools, route: '' },\r\n      ];\r\n      this.quickActions = [\r\n        {\r\n          icon: 'awe_agents',\r\n          label: this.labels.agents,\r\n          route: '',\r\n        },\r\n        {\r\n          icon: 'awe_workflows',\r\n          label: this.labels.workflows,\r\n          route: '',\r\n        },\r\n        {\r\n          icon: 'awe_tools',\r\n          label: this.labels.tools,\r\n          route: '',\r\n        },\r\n      ];\r\n      this.searchForm = this.fb.group({\r\n        search: [''],\r\n      });\r\n    }\r\n  \r\n    ngOnInit(): void {\r\n      this.searchList();\r\n      this.totalApprovals = 60;\r\n      this.loadToolReviews();\r\n    }\r\n  \r\n    public searchList() {\r\n      console.log(this.searchForm.get('search')?.value);\r\n      this.searchForm\r\n        .get('search')!\r\n        .valueChanges.pipe(\r\n          startWith(''),\r\n          debounceTime(300),\r\n          distinctUntilChanged(),\r\n          map((value) => value?.toLowerCase() ?? ''),\r\n        )\r\n        .subscribe((searchText) => {\r\n          this.applyFilter(searchText);\r\n        });\r\n    }\r\n  \r\n    public applyFilter(text: string) {\r\n      const lower = text;\r\n  \r\n      if(!text){\r\n        this.updateConsoleApproval(this.toolReviews, 'tool');\r\n        return;\r\n      }\r\n  \r\n      \r\n      this.filteredToolReviews = this.toolReviews.filter((item) =>\r\n          item.toolName?.toLowerCase().includes(lower),\r\n      );\r\n\r\n      this.updateConsoleApproval(this.filteredToolReviews, 'tool');\r\n    }\r\n  \r\n    public onSelectionChange(data: any) {\r\n      console.log('Selection changed:', data);\r\n    }\r\n  \r\n    public uClick(i: any) {\r\n      console.log('log' + i);\r\n    }\r\n  \r\n    public toggleQuickActions(): void {\r\n      this.quickActionsExpanded = !this.quickActionsExpanded;\r\n    }\r\n  \r\n    public onBasicCollapseToggle(isCollapsed: boolean): void {\r\n      this.isBasicCollapsed = isCollapsed;\r\n      console.log('Basic sidebar collapsed:', isCollapsed);\r\n    }\r\n  \r\n    public onBasicItemClick(item: any): void {\r\n      this.basicSidebarItems.forEach((i) => (i.active = false));\r\n      item.active = true;\r\n      console.log(item);\r\n    }\r\n  \r\n    public toRequestStatus(value: string | null | undefined): RequestStatus {\r\n      return value === 'approved' || value === 'rejected' || value === 'review'\r\n        ? value\r\n        : 'review';\r\n    }\r\n  \r\n    public loadToolReviews() {\r\n      this.approvalService\r\n        .getAllReviewTools(this.currentToolsPage, this.pageSize, this.isDeleted)\r\n        .subscribe((response) => {\r\n          if (this.currentToolsPage > 1) {\r\n            this.toolReviews = [\r\n              ...this.toolReviews,\r\n              ...response.userToolReviewDetails,\r\n            ];\r\n          } else {\r\n            this.toolReviews = response?.userToolReviewDetails;\r\n          }\r\n          this.toolReviews = this.toolReviews.filter(\r\n            (r) => r.status !== 'approved',\r\n          );\r\n          this.filteredToolReviews = this.toolReviews;\r\n          // console.log('tool reviews ', this.toolReviews);\r\n          this.totalRecords = this.toolReviews.length;\r\n          this.updateConsoleApproval(this.toolReviews, 'tool');\r\n        });\r\n    }\r\n  \r\n    public loadMoreTools(page: number) {\r\n      this.currentToolsPage = page;\r\n      this.loadToolReviews();\r\n    }\r\n  \r\n  \r\n    public loadReviews(name: string) {\r\n      this.currentTab = name;\r\n      this.loadToolReviews();\r\n    }\r\n  \r\n    public rejectApproval(idx: any) {\r\n      console.log(idx);\r\n      // console.log(this.filteredToolReviews);\r\n      this.selectedIndex = idx;\r\n      this.showFeedbackPopup = true;\r\n    }\r\n  \r\n    public approveApproval(idx: any) {\r\n      console.log(idx);\r\n      // console.log(this.filteredToolReviews);\r\n      this.selectedIndex = idx;\r\n      this.showToolApprovalPopup = true;\r\n    }\r\n  \r\n    public handleApproval() {\r\n      this.handleToolApproval();\r\n    }\r\n  \r\n    public handleRejection(feedback: any) {\r\n      this.handleToolRejection(feedback);\r\n    }\r\n  \r\n    public handleToolApproval() {\r\n      const toolDetails = this.filteredToolReviews[this.selectedIndex];\r\n      const id = toolDetails.id;\r\n      const toolId = toolDetails.toolId;\r\n      const status = 'approved';\r\n      const reviewedBy = toolDetails.reviewedBy;\r\n  \r\n      this.approvalService.approveTool(id, toolId, status, reviewedBy).subscribe({\r\n        next: (response: any) => {\r\n          this.infoMessage =\r\n            response?.message || this.labels.toolSuccessApproveMessage;\r\n          this.showInfoPopup = true;\r\n        },\r\n        error: (error) => {\r\n          this.showErrorPopup = true;\r\n          this.infoMessage = error?.error?.message || this.labels.defaultErrorMessage;\r\n          console.error('Error:', error);\r\n        },\r\n      });\r\n    }\r\n  \r\n    public handleToolRejection(feedback: any) {\r\n      const toolDetails = this.filteredToolReviews[this.selectedIndex];\r\n      const id = toolDetails.id;\r\n      const toolId = toolDetails.toolId;\r\n      const status = 'rejected';\r\n      const reviewedBy = toolDetails.reviewedBy;\r\n      const message = feedback;\r\n      \r\n      this.approvalService\r\n        .rejectTool(id, toolId, status, reviewedBy, message)\r\n        .subscribe({\r\n          next: (response: any) => {\r\n            this.infoMessage = response?.message || this.labels.toolSuccessRejectMessage;\r\n            this.showInfoPopup = true;\r\n          },\r\n          error: (error) => {\r\n            this.showErrorPopup = true;\r\n            this.infoMessage = error?.error?.message || this.labels.defaultErrorMessage;\r\n            console.error('Error:', error);\r\n          },\r\n        });\r\n    }\r\n  \r\n    public handleInfoPopup() {\r\n      this.showInfoPopup = false;\r\n      this.loadToolReviews();\r\n    }\r\n\r\n    public handleTesting(index : any){\r\n      console.log(index);\r\n      const toolId = this.filteredToolReviews[index].toolId;\r\n      this.redirectToToolPlayground(toolId);\r\n    }\r\n  \r\n    public redirectToToolPlayground(id: number): void {\r\n      this.router.navigate(['/libraries/tools/execute', id]);\r\n    }\r\n  \r\n    public updateConsoleApproval(data: any[], type: string) {\r\n      this.consoleApproval = {\r\n        contents: data?.map((req: any) => {\r\n          const statusIcons: Record<RequestStatus, string> = {\r\n            approved: 'circle-check-big',\r\n            rejected: 'circle-x',\r\n            review: 'clock',\r\n          };\r\n          const statusTexts: Record<RequestStatus, string> = {\r\n            approved: this.labels.approved,\r\n            rejected: this.labels.rejected,\r\n            review: this.labels.review,\r\n          };\r\n          const statusKey = this.toRequestStatus(req?.status);\r\n          const specificId = req.toolId;\r\n          const title = req.toolName;\r\n  \r\n          return {\r\n            id: req.id,\r\n            refId: specificId,\r\n            type: type,\r\n            session1: {\r\n              title: title,\r\n              labels: [\r\n                {\r\n                  name: type,\r\n                  color: 'success',\r\n                  background: 'red',\r\n                  type: 'normal',\r\n                },\r\n                {\r\n                  name: req.changeRequestType,\r\n                  color: req.changeRequestType === 'update' ? 'error' : 'info',\r\n                  background: 'red',\r\n                  type: 'pill',\r\n                },\r\n              ],\r\n            },\r\n            session2: [\r\n              {\r\n                name: type,\r\n                color: 'default',\r\n                background: 'red',\r\n                type: 'normal',\r\n              },\r\n              {\r\n                name: req.status,\r\n                color: 'default',\r\n                background: 'red',\r\n                type: 'normal',\r\n              },\r\n            ],\r\n            session3: [\r\n              {\r\n                iconName: 'user',\r\n                label: req.requestedBy,\r\n              },\r\n              {\r\n                iconName: 'calendar-days',\r\n                label: formatDate(req?.requestedAt, 'dd MMM yyyy', 'en-IN'), \r\n              },\r\n            ],\r\n            session4: {\r\n              status: statusTexts[statusKey],\r\n              iconName: statusIcons[statusKey],\r\n            },\r\n          };\r\n        }),\r\n        footer: {},\r\n      };\r\n    }\r\n}\r\n", "<div class=\"approval-right-screen\">\r\n    <div class=\"approval-title-filter\">\r\n        <ava-text-card [type]=\"'default'\" [iconName]=\"'hourglass'\" [title]=\"labels.totalApprovals\" [value]=\"totalApprovals\"\r\n            [description]=\"currentTab + ' ' + labels.whichAreRequestedForApproval\">\r\n        </ava-text-card>\r\n        <ava-text-card [type]=\"'default'\" [iconName]=\"'shield-alert'\" [title]=\"labels.totalApprovedApprovals\" [value]=\"totalApprovedApprovals\"\r\n            [description]=\"currentTab + ' ' + labels.whichAreApproved\">\r\n        </ava-text-card>\r\n        <ava-text-card [type]=\"'default'\" [iconName]=\"'hourglass'\" [title]=\"labels.totalPendingApprovals\" [value]=\"totalPendingApprovals\"\r\n            [description]=\"labels.all + ' ' + currentTab + ' ' + labels.awaitingApproval\">\r\n        </ava-text-card>\r\n    </div>\r\n    \r\n<div class=\"filter-section\">\r\n    <div class=\"search-bars\">\r\n        <!-- <ava-dropdown [dropdownTitle]=\"labels.allStatus\" [options]=\"options\" [search]=\"true\"\r\n            (selectionChange)=\"onSelectionChange($event)\">\r\n        </ava-dropdown>\r\n        <ava-dropdown [dropdownTitle]=\"labels.allPriority\" [options]=\"options\" [search]=\"true\"\r\n            (selectionChange)=\"onSelectionChange($event)\">\r\n        </ava-dropdown> -->\r\n        <!-- <ava-button label=\"Bulk Approve\" (userClick)=\"uClick(1)\" variant=\"primary\" size=\"large\" state=\"default\"\r\n            iconPosition=\"left\"></ava-button> -->\r\n    </div>\r\n    <div class=\"textbox section\">\r\n        <div>\r\n            <form [formGroup]=\"searchForm\">\r\n                <ava-textbox [placeholder]=\"labels.searchPlaceholder\" formControlName=\"search\">\r\n                    <ava-icon slot=\"icon-start\" iconName=\"search\" [iconSize]=\"16\" iconColor=\"var(--color-brand-primary)\"></ava-icon>\r\n                </ava-textbox>\r\n            </form>\r\n            <ava-button label=\"Primary\" variant=\"primary\" iconName=\"calendar-days\" iconPosition=\"only\"\r\n            [customStyles]=\"{\r\n                background:\r\n                  'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n                '--button-effect-color': '33, 90, 214',\r\n              }\" (userClick)=\"uClick($event)\"></ava-button>\r\n        </div>\r\n    </div>\r\n</div>\r\n    \r\n    <div class=\"approval-card-section\">\r\n        @if(totalRecords > 0){        \r\n            <div class=\"approval-card-header\">\r\n                All - {{totalRecords}} {{currentTab}}\r\n            </div>\r\n            @for (item of consoleApproval.contents; track $index){\r\n            <div class=\"approval-card-wrapper\">\r\n                <ava-approval-card height=\"300\">\r\n                    <div header>\r\n                        <ava-icon iconSize=\"20\" iconName=\"ellipsis-vertical\"></ava-icon>\r\n                        <div class=\"header\">\r\n                            <h2>{{item.session1.title}}</h2>\r\n                            <ava-tag label=\"{{currentTab}}\" color=\"info\" size=\"sm\"></ava-tag>\r\n                        </div>\r\n                    </div>\r\n                    <div content class=\"a-content\">\r\n                        <div class=\"box tag-wrapper\">\r\n                            <ava-tag label=\"Individual\" size=\"sm\"></ava-tag>\r\n                            <ava-tag label=\"Ascendion\" size=\"sm\"></ava-tag>\r\n                            <ava-tag label=\"Digital Ascender\" size=\"sm\"></ava-tag>\r\n                            <ava-tag label=\"Platform Engineering\" size=\"sm\"></ava-tag>\r\n                        </div>\r\n            \r\n                        <div class=\"box info-wrapper\">\r\n                            <div class=\"f\">\r\n                                <ava-icon iconSize=\"13\" iconName=\"user\"></ava-icon>\r\n                                <span>{{item.session3[0].label}}</span>\r\n                            </div>\r\n                            <div class=\"ml-auto s\">\r\n                                <ava-icon iconSize=\"20\" iconName=\"calendar-days\"></ava-icon>\r\n                                <span>{{item.session3[1].label}}</span>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div footer>\r\n                        <div class=\"footer-content\">\r\n                            <div class=\"footer-left\">\r\n                                <span class=\"ex\">Execution Status</span>\r\n                                <div>\r\n                                    <ava-icon iconSize=\"20\" iconName=\"circle-check-big\"></ava-icon>\r\n                                    <span>{{item?.session4.status}}</span>\r\n                                </div>\r\n            \r\n                            </div>\r\n                            <div class=\"footer-right\">\r\n                                <ava-button [label]=\"labels.test\" (userClick)=\"handleTesting($index)\" variant=\"secondary\" size=\"small\" [customStyles]=\"{\r\n                                    'border': '2px solid transparent',\r\n                                    'background-image': 'linear-gradient(#ffffff, #ffffff), linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n                                    'background-origin': 'border-box',\r\n                                    'background-clip': 'padding-box, border-box',\r\n                                    '--button-effect-color': '33, 90, 214'\r\n                                  }\"\r\n                                    state=\"default\" iconName=\"play\" iconPosition=\"left\"></ava-button>\r\n                                <ava-button [label]=\"labels.sendback\" (userClick)=\"rejectApproval($index)\" variant=\"secondary\" size=\"small\" [customStyles]=\"{\r\n                                    'border': '2px solid transparent',\r\n                                    'background-image': 'linear-gradient(#ffffff, #ffffff), linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n                                    'background-origin': 'border-box',\r\n                                    'background-clip': 'padding-box, border-box',\r\n                                    '--button-effect-color': '33, 90, 214'\r\n                                  }\"\r\n                                    state=\"default\" iconName=\"move-left\" iconPosition=\"left\"></ava-button>\r\n                                <ava-button [label]=\"labels.approve\" (userClick)=\"approveApproval($index)\" variant=\"primary\" size=\"small\" [customStyles]=\"{\r\n                                    background:\r\n                                      'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n                                    '--button-effect-color': '33, 90, 214',\r\n                                  }\"\r\n                                    state=\"default\" iconName=\"Check\" iconPosition=\"left\"></ava-button>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </ava-approval-card>\r\n            </div>\r\n            }\r\n        }\r\n        @else{\r\n            <div class=\"no-pending-message\">\r\n                All {{currentTab}} have been successfully approved. No pending actions.\r\n            </div>\r\n        }\r\n    </div>\r\n</div>\r\n\r\n<ava-popup\r\n  [show]=\"showToolApprovalPopup\"\r\n  [title]=\"labels.confirmApproval\"\r\n  [message]=\"labels.youAreAboutToApproveThis + ' ' + currentTab + '. ' + labels.itWillBeActiveAndAvailableIn + ' ' + currentTab + ' ' + labels.catalogueForUsersToExecute\"\r\n  [showClose]=\"true\"\r\n  [showCancel]=\"true\"\r\n  [showConfirm]=\"true\"\r\n  [confirmButtonLabel]=\"labels.approve\"\r\n  [confirmButtonVariant]=\"'primary'\"\r\n  [confirmButtonBackground]=\"'#dc3545'\"\r\n  (confirm)=\"handleApproval()\"\r\n  (cancel)=\"showToolApprovalPopup=false\"\r\n  (closed)=\"showToolApprovalPopup=false\"\r\n>\r\n</ava-popup>\r\n\r\n<ava-popup messageAlignment=\"center\" [show]=\"showInfoPopup\"\r\n    title=\"SUCCESS!\" message={{infoMessage}} [showHeaderIcon]=\"true\"\r\n    headerIconName=\"circle-check\" iconColor=\"green\" [showClose]=\"true\" (closed)=\"handleInfoPopup()\">\r\n</ava-popup>\r\n\r\n<ava-confirmation-popup [show]=\"showFeedbackPopup\" title=\"Confirm Send Back\"\r\n    message=\"This {{currentTab}} will be send back for corrections and modification. Kindly comment what needs to be done.\"\r\n    confirmationLabel=\"Send Back\" (closed)=\"showFeedbackPopup = false\" (confirm)=\"handleRejection($event)\">\r\n</ava-confirmation-popup>\r\n\r\n<ava-popup messageAlignment=\"center\" [show]=\"showErrorPopup\"\r\n    title=\"FAILED!\" message={{infoMessage}} [showHeaderIcon]=\"true\"\r\n    headerIconName=\"circle-x\" iconColor=\"red\" [showClose]=\"true\" (closed)=\"showErrorPopup = false\" >\r\n</ava-popup>"], "mappings": "AAAA,SAASA,YAAY,EAAEC,UAAU,QAAQ,iBAAiB;AAE1D,SAAiCC,mBAAmB,QAAQ,gBAAgB;AAC5E,SAAiBC,YAAY,QAAQ,iBAAiB;AACtD,SAASC,qBAAqB,EAAEC,aAAa,EAAEC,mBAAmB,EAAEC,iBAAiB,EAAEC,cAAc,EAAEC,0BAA0B,EAAEC,eAAe,EAAkBC,eAAe,QAAQ,wBAAwB;AACnN,OAAOC,YAAY,MAAO,4BAA4B;AAGtD,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,GAAG,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;ICyCrDC,EAFR,CAAAC,cAAA,cAAmC,4BACC,cAChB;IACRD,EAAA,CAAAE,SAAA,mBAAgE;IAE5DF,EADJ,CAAAC,cAAA,cAAoB,SACZ;IAAAD,EAAA,CAAAG,MAAA,GAAuB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAChCJ,EAAA,CAAAE,SAAA,kBAAiE;IAEzEF,EADI,CAAAI,YAAA,EAAM,EACJ;IAEFJ,EADJ,CAAAC,cAAA,cAA+B,cACE;IAIzBD,EAHA,CAAAE,SAAA,mBAAgD,mBACD,mBACO,mBACI;IAC9DF,EAAA,CAAAI,YAAA,EAAM;IAGFJ,EADJ,CAAAC,cAAA,eAA8B,eACX;IACXD,EAAA,CAAAE,SAAA,oBAAmD;IACnDF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,IAA0B;IACpCH,EADoC,CAAAI,YAAA,EAAO,EACrC;IACNJ,EAAA,CAAAC,cAAA,eAAuB;IACnBD,EAAA,CAAAE,SAAA,oBAA4D;IAC5DF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,IAA0B;IAG5CH,EAH4C,CAAAI,YAAA,EAAO,EACrC,EACJ,EACJ;IAIMJ,EAHZ,CAAAC,cAAA,eAAY,eACoB,eACC,gBACJ;IAAAD,EAAA,CAAAG,MAAA,wBAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACxCJ,EAAA,CAAAC,cAAA,WAAK;IACDD,EAAA,CAAAE,SAAA,oBAA+D;IAC/DF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,IAAyB;IAGvCH,EAHuC,CAAAI,YAAA,EAAO,EACpC,EAEJ;IAEFJ,EADJ,CAAAC,cAAA,eAA0B,sBAQkC;IAPtBD,EAAA,CAAAK,UAAA,uBAAAC,sFAAA;MAAA,MAAAC,SAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,MAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAAaF,MAAA,CAAAG,aAAA,CAAAP,SAAA,CAAqB;IAAA,EAAC;IAObP,EAAA,CAAAI,YAAA,EAAa;IACrEJ,EAAA,CAAAC,cAAA,sBAO6D;IAPvBD,EAAA,CAAAK,UAAA,uBAAAU,sFAAA;MAAA,MAAAR,SAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,MAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAAaF,MAAA,CAAAK,cAAA,CAAAT,SAAA,CAAsB;IAAA,EAAC;IAObP,EAAA,CAAAI,YAAA,EAAa;IAC1EJ,EAAA,CAAAC,cAAA,sBAKyD;IALpBD,EAAA,CAAAK,UAAA,uBAAAY,sFAAA;MAAA,MAAAV,SAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,MAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAAaF,MAAA,CAAAO,eAAA,CAAAX,SAAA,CAAuB;IAAA,EAAC;IAU9FP,EAL6E,CAAAI,YAAA,EAAa,EACpE,EACJ,EACJ,EACU,EAClB;;;;;IA5DcJ,EAAA,CAAAmB,SAAA,GAAuB;IAAvBnB,EAAA,CAAAoB,iBAAA,CAAAC,OAAA,CAAAC,QAAA,CAAAC,KAAA,CAAuB;IAClBvB,EAAA,CAAAmB,SAAA,EAAsB;IAAtBnB,EAAA,CAAAwB,qBAAA,UAAAb,MAAA,CAAAc,UAAA,CAAsB;IAcrBzB,EAAA,CAAAmB,SAAA,IAA0B;IAA1BnB,EAAA,CAAAoB,iBAAA,CAAAC,OAAA,CAAAK,QAAA,IAAAC,KAAA,CAA0B;IAI1B3B,EAAA,CAAAmB,SAAA,GAA0B;IAA1BnB,EAAA,CAAAoB,iBAAA,CAAAC,OAAA,CAAAK,QAAA,IAAAC,KAAA,CAA0B;IAUtB3B,EAAA,CAAAmB,SAAA,GAAyB;IAAzBnB,EAAA,CAAAoB,iBAAA,CAAAC,OAAA,kBAAAA,OAAA,CAAAO,QAAA,CAAAC,MAAA,CAAyB;IAKvB7B,EAAA,CAAAmB,SAAA,GAAqB;IAAsEnB,EAA3F,CAAA8B,UAAA,UAAAnB,MAAA,CAAAoB,MAAA,CAAAC,IAAA,CAAqB,iBAAAhC,EAAA,CAAAiC,eAAA,KAAAC,GAAA,EAM7B;IAEQlC,EAAA,CAAAmB,SAAA,EAAyB;IAAuEnB,EAAhG,CAAA8B,UAAA,UAAAnB,MAAA,CAAAoB,MAAA,CAAAI,QAAA,CAAyB,iBAAAnC,EAAA,CAAAiC,eAAA,KAAAC,GAAA,EAMjC;IAEQlC,EAAA,CAAAmB,SAAA,EAAwB;IAAsEnB,EAA9F,CAAA8B,UAAA,UAAAnB,MAAA,CAAAoB,MAAA,CAAAK,OAAA,CAAwB,iBAAApC,EAAA,CAAAiC,eAAA,KAAAI,GAAA,EAIhC;;;;;IA/DxBrC,EAAA,CAAAC,cAAA,cAAkC;IAC9BD,EAAA,CAAAG,MAAA,GACJ;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAsC,gBAAA,IAAAC,oDAAA,qBAAAvC,EAAA,CAAAwC,sBAAA,CAmEC;;;;IArEGxC,EAAA,CAAAmB,SAAA,EACJ;IADInB,EAAA,CAAAyC,kBAAA,YAAA9B,MAAA,CAAA+B,YAAA,OAAA/B,MAAA,CAAAc,UAAA,MACJ;IACAzB,EAAA,CAAAmB,SAAA,EAmEC;IAnEDnB,EAAA,CAAA2C,UAAA,CAAAhC,MAAA,CAAAiC,eAAA,CAAAC,QAAA,CAmEC;;;;;IAGD7C,EAAA,CAAAC,cAAA,cAAgC;IAC5BD,EAAA,CAAAG,MAAA,GACJ;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADFJ,EAAA,CAAAmB,SAAA,EACJ;IADInB,EAAA,CAAA8C,kBAAA,UAAAnC,MAAA,CAAAc,UAAA,2DACJ;;;ADtFZ,WAAasB,sBAAsB;EAA7B,MAAOA,sBAAsB;IAkCrBC,MAAA;IACAC,UAAA;IACAC,eAAA;IACAC,EAAA;IApCVC,SAAS,GAAGzD,YAAY,CAACoC,MAAM;IAExBsB,sBAAsB,GAAW,EAAE;IACnCC,qBAAqB,GAAW,EAAE;IAClCC,cAAc,GAAW,EAAE;IAC3BC,gBAAgB,GAAY,KAAK;IACjCC,oBAAoB,GAAY,IAAI;IACpCb,eAAe,GAAQ,EAAE;IACzBc,OAAO,GAAqB,EAAE;IAC9BC,iBAAiB,GAAU,EAAE;IAC7BC,YAAY,GAAU,EAAE;IACxBC,WAAW,GAAU,EAAE;IACvBC,mBAAmB,GAAU,EAAE;IAC/BC,eAAe,GAAU,EAAE;IAC3BC,aAAa,GAAU,EAAE;IACzBC,gBAAgB,GAAG,CAAC;IACpBC,iBAAiB,GAAG,CAAC;IACrBC,oBAAoB,GAAG,CAAC;IACxBC,QAAQ,GAAG,EAAE;IACb1B,YAAY,GAAG,CAAC;IAChB2B,SAAS,GAAG,KAAK;IACjB5C,UAAU,GAAG,OAAO;IACpB6C,qBAAqB,GAAG,KAAK;IAC7BC,aAAa,GAAG,KAAK;IACrBC,cAAc,GAAG,KAAK;IACtBC,WAAW,GAAG,EAAE;IAChBC,aAAa,GAAG,CAAC;IACjBC,iBAAiB,GAAG,KAAK;IACzBC,UAAU;IACV7C,MAAM,GAAQpC,YAAY,CAACoC,MAAM;IACjC8C,eAAe,GAAkB,IAAI;IAE5CC,YACU9B,MAAc,EACdC,UAAmC,EACnCC,eAAgC,EAChCC,EAAe;MAHf,KAAAH,MAAM,GAANA,MAAM;MACN,KAAAC,UAAU,GAAVA,UAAU;MACV,KAAAC,eAAe,GAAfA,eAAe;MACf,KAAAC,EAAE,GAAFA,EAAE;MAEV,IAAI,CAACpB,MAAM,GAAGpC,YAAY,CAACoC,MAAM;MACjC,IAAI,CAAC2B,OAAO,GAAG,CACb;QAAEqB,IAAI,EAAE,IAAI,CAAChD,MAAM,CAACiD,WAAW;QAAEC,KAAK,EAAE;MAAa,CAAE,EACvD;QAAEF,IAAI,EAAE,IAAI,CAAChD,MAAM,CAACmD,QAAQ;QAAED,KAAK,EAAE;MAAU,CAAE,EACjD;QAAEF,IAAI,EAAE,IAAI,CAAChD,MAAM,CAACoD,KAAK;QAAEF,KAAK,EAAE;MAAO,CAAE,CAC5C;MACD,IAAI,CAACtB,iBAAiB,GAAG,CACvB;QAAEyB,EAAE,EAAE,GAAG;QAAEC,IAAI,EAAE,QAAQ;QAAEC,IAAI,EAAE,IAAI,CAACvD,MAAM,CAACwD,MAAM;QAAEC,KAAK,EAAE,EAAE;QAAEC,MAAM,EAAE;MAAI,CAAE,EAC9E;QAAEL,EAAE,EAAE,GAAG;QAAEC,IAAI,EAAE,cAAc;QAAEC,IAAI,EAAE,IAAI,CAACvD,MAAM,CAAC2D,SAAS;QAAEF,KAAK,EAAE;MAAE,CAAE,EACzE;QAAEJ,EAAE,EAAE,GAAG;QAAEC,IAAI,EAAE,KAAK;QAAEC,IAAI,EAAE,IAAI,CAACvD,MAAM,CAAC4D,KAAK;QAAEH,KAAK,EAAE;MAAE,CAAE,CAC7D;MACD,IAAI,CAAC5B,YAAY,GAAG,CAClB;QACEyB,IAAI,EAAE,YAAY;QAClB1D,KAAK,EAAE,IAAI,CAACI,MAAM,CAACwD,MAAM;QACzBC,KAAK,EAAE;OACR,EACD;QACEH,IAAI,EAAE,eAAe;QACrB1D,KAAK,EAAE,IAAI,CAACI,MAAM,CAAC2D,SAAS;QAC5BF,KAAK,EAAE;OACR,EACD;QACEH,IAAI,EAAE,WAAW;QACjB1D,KAAK,EAAE,IAAI,CAACI,MAAM,CAAC4D,KAAK;QACxBH,KAAK,EAAE;OACR,CACF;MACD,IAAI,CAACZ,UAAU,GAAG,IAAI,CAACzB,EAAE,CAACyC,KAAK,CAAC;QAC9BC,MAAM,EAAE,CAAC,EAAE;OACZ,CAAC;IACJ;IAEAC,QAAQA,CAAA;MACN,IAAI,CAACC,UAAU,EAAE;MACjB,IAAI,CAACxC,cAAc,GAAG,EAAE;MACxB,IAAI,CAACyC,eAAe,EAAE;IACxB;IAEOD,UAAUA,CAAA;MACfE,OAAO,CAACC,GAAG,CAAC,IAAI,CAACtB,UAAU,CAACuB,GAAG,CAAC,QAAQ,CAAC,EAAElB,KAAK,CAAC;MACjD,IAAI,CAACL,UAAU,CACZuB,GAAG,CAAC,QAAQ,CAAE,CACdC,YAAY,CAACC,IAAI,CAChBtG,SAAS,CAAC,EAAE,CAAC,EACbH,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,GAAG,CAAEmF,KAAK,IAAKA,KAAK,EAAEqB,WAAW,EAAE,IAAI,EAAE,CAAC,CAC3C,CACAC,SAAS,CAAEC,UAAU,IAAI;QACxB,IAAI,CAACC,WAAW,CAACD,UAAU,CAAC;MAC9B,CAAC,CAAC;IACN;IAEOC,WAAWA,CAACnB,IAAY;MAC7B,MAAMoB,KAAK,GAAGpB,IAAI;MAElB,IAAG,CAACA,IAAI,EAAC;QACP,IAAI,CAACqB,qBAAqB,CAAC,IAAI,CAAC9C,WAAW,EAAE,MAAM,CAAC;QACpD;MACF;MAGA,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAACD,WAAW,CAAC+C,MAAM,CAAEC,IAAI,IACpDA,IAAI,CAACC,QAAQ,EAAER,WAAW,EAAE,CAACS,QAAQ,CAACL,KAAK,CAAC,CAC/C;MAED,IAAI,CAACC,qBAAqB,CAAC,IAAI,CAAC7C,mBAAmB,EAAE,MAAM,CAAC;IAC9D;IAEOkD,iBAAiBA,CAACC,IAAS;MAChChB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEe,IAAI,CAAC;IACzC;IAEOC,MAAMA,CAACC,CAAM;MAClBlB,OAAO,CAACC,GAAG,CAAC,KAAK,GAAGiB,CAAC,CAAC;IACxB;IAEOC,kBAAkBA,CAAA;MACvB,IAAI,CAAC3D,oBAAoB,GAAG,CAAC,IAAI,CAACA,oBAAoB;IACxD;IAEO4D,qBAAqBA,CAACC,WAAoB;MAC/C,IAAI,CAAC9D,gBAAgB,GAAG8D,WAAW;MACnCrB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEoB,WAAW,CAAC;IACtD;IAEOC,gBAAgBA,CAACV,IAAS;MAC/B,IAAI,CAAClD,iBAAiB,CAAC6D,OAAO,CAAEL,CAAC,IAAMA,CAAC,CAAC1B,MAAM,GAAG,KAAM,CAAC;MACzDoB,IAAI,CAACpB,MAAM,GAAG,IAAI;MAClBQ,OAAO,CAACC,GAAG,CAACW,IAAI,CAAC;IACnB;IAEOY,eAAeA,CAACxC,KAAgC;MACrD,OAAOA,KAAK,KAAK,UAAU,IAAIA,KAAK,KAAK,UAAU,IAAIA,KAAK,KAAK,QAAQ,GACrEA,KAAK,GACL,QAAQ;IACd;IAEOe,eAAeA,CAAA;MACpB,IAAI,CAAC9C,eAAe,CACjBwE,iBAAiB,CAAC,IAAI,CAACzD,gBAAgB,EAAE,IAAI,CAACG,QAAQ,EAAE,IAAI,CAACC,SAAS,CAAC,CACvEkC,SAAS,CAAEoB,QAAQ,IAAI;QACtB,IAAI,IAAI,CAAC1D,gBAAgB,GAAG,CAAC,EAAE;UAC7B,IAAI,CAACJ,WAAW,GAAG,CACjB,GAAG,IAAI,CAACA,WAAW,EACnB,GAAG8D,QAAQ,CAACC,qBAAqB,CAClC;QACH,CAAC,MAAM;UACL,IAAI,CAAC/D,WAAW,GAAG8D,QAAQ,EAAEC,qBAAqB;QACpD;QACA,IAAI,CAAC/D,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC+C,MAAM,CACvCiB,CAAC,IAAKA,CAAC,CAAChG,MAAM,KAAK,UAAU,CAC/B;QACD,IAAI,CAACiC,mBAAmB,GAAG,IAAI,CAACD,WAAW;QAC3C;QACA,IAAI,CAACnB,YAAY,GAAG,IAAI,CAACmB,WAAW,CAACiE,MAAM;QAC3C,IAAI,CAACnB,qBAAqB,CAAC,IAAI,CAAC9C,WAAW,EAAE,MAAM,CAAC;MACtD,CAAC,CAAC;IACN;IAEOkE,aAAaA,CAACC,IAAY;MAC/B,IAAI,CAAC/D,gBAAgB,GAAG+D,IAAI;MAC5B,IAAI,CAAChC,eAAe,EAAE;IACxB;IAGOiC,WAAWA,CAAClD,IAAY;MAC7B,IAAI,CAACtD,UAAU,GAAGsD,IAAI;MACtB,IAAI,CAACiB,eAAe,EAAE;IACxB;IAEOhF,cAAcA,CAACkH,GAAQ;MAC5BjC,OAAO,CAACC,GAAG,CAACgC,GAAG,CAAC;MAChB;MACA,IAAI,CAACxD,aAAa,GAAGwD,GAAG;MACxB,IAAI,CAACvD,iBAAiB,GAAG,IAAI;IAC/B;IAEOzD,eAAeA,CAACgH,GAAQ;MAC7BjC,OAAO,CAACC,GAAG,CAACgC,GAAG,CAAC;MAChB;MACA,IAAI,CAACxD,aAAa,GAAGwD,GAAG;MACxB,IAAI,CAAC5D,qBAAqB,GAAG,IAAI;IACnC;IAEO6D,cAAcA,CAAA;MACnB,IAAI,CAACC,kBAAkB,EAAE;IAC3B;IAEOC,eAAeA,CAACC,QAAa;MAClC,IAAI,CAACC,mBAAmB,CAACD,QAAQ,CAAC;IACpC;IAEOF,kBAAkBA,CAAA;MACvB,MAAMI,WAAW,GAAG,IAAI,CAAC1E,mBAAmB,CAAC,IAAI,CAACY,aAAa,CAAC;MAChE,MAAMU,EAAE,GAAGoD,WAAW,CAACpD,EAAE;MACzB,MAAMqD,MAAM,GAAGD,WAAW,CAACC,MAAM;MACjC,MAAM5G,MAAM,GAAG,UAAU;MACzB,MAAM6G,UAAU,GAAGF,WAAW,CAACE,UAAU;MAEzC,IAAI,CAACxF,eAAe,CAACyF,WAAW,CAACvD,EAAE,EAAEqD,MAAM,EAAE5G,MAAM,EAAE6G,UAAU,CAAC,CAACnC,SAAS,CAAC;QACzEqC,IAAI,EAAGjB,QAAa,IAAI;UACtB,IAAI,CAAClD,WAAW,GACdkD,QAAQ,EAAEkB,OAAO,IAAI,IAAI,CAAC9G,MAAM,CAAC+G,yBAAyB;UAC5D,IAAI,CAACvE,aAAa,GAAG,IAAI;QAC3B,CAAC;QACDwE,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACvE,cAAc,GAAG,IAAI;UAC1B,IAAI,CAACC,WAAW,GAAGsE,KAAK,EAAEA,KAAK,EAAEF,OAAO,IAAI,IAAI,CAAC9G,MAAM,CAACiH,mBAAmB;UAC3E/C,OAAO,CAAC8C,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;QAChC;OACD,CAAC;IACJ;IAEOR,mBAAmBA,CAACD,QAAa;MACtC,MAAME,WAAW,GAAG,IAAI,CAAC1E,mBAAmB,CAAC,IAAI,CAACY,aAAa,CAAC;MAChE,MAAMU,EAAE,GAAGoD,WAAW,CAACpD,EAAE;MACzB,MAAMqD,MAAM,GAAGD,WAAW,CAACC,MAAM;MACjC,MAAM5G,MAAM,GAAG,UAAU;MACzB,MAAM6G,UAAU,GAAGF,WAAW,CAACE,UAAU;MACzC,MAAMG,OAAO,GAAGP,QAAQ;MAExB,IAAI,CAACpF,eAAe,CACjB+F,UAAU,CAAC7D,EAAE,EAAEqD,MAAM,EAAE5G,MAAM,EAAE6G,UAAU,EAAEG,OAAO,CAAC,CACnDtC,SAAS,CAAC;QACTqC,IAAI,EAAGjB,QAAa,IAAI;UACtB,IAAI,CAAClD,WAAW,GAAGkD,QAAQ,EAAEkB,OAAO,IAAI,IAAI,CAAC9G,MAAM,CAACmH,wBAAwB;UAC5E,IAAI,CAAC3E,aAAa,GAAG,IAAI;QAC3B,CAAC;QACDwE,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACvE,cAAc,GAAG,IAAI;UAC1B,IAAI,CAACC,WAAW,GAAGsE,KAAK,EAAEA,KAAK,EAAEF,OAAO,IAAI,IAAI,CAAC9G,MAAM,CAACiH,mBAAmB;UAC3E/C,OAAO,CAAC8C,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;QAChC;OACD,CAAC;IACN;IAEOI,eAAeA,CAAA;MACpB,IAAI,CAAC5E,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACyB,eAAe,EAAE;IACxB;IAEOlF,aAAaA,CAACsI,KAAW;MAC9BnD,OAAO,CAACC,GAAG,CAACkD,KAAK,CAAC;MAClB,MAAMX,MAAM,GAAG,IAAI,CAAC3E,mBAAmB,CAACsF,KAAK,CAAC,CAACX,MAAM;MACrD,IAAI,CAACY,wBAAwB,CAACZ,MAAM,CAAC;IACvC;IAEOY,wBAAwBA,CAACjE,EAAU;MACxC,IAAI,CAACpC,MAAM,CAACsG,QAAQ,CAAC,CAAC,0BAA0B,EAAElE,EAAE,CAAC,CAAC;IACxD;IAEOuB,qBAAqBA,CAACM,IAAW,EAAEsC,IAAY;MACpD,IAAI,CAAC3G,eAAe,GAAG;QACrBC,QAAQ,EAAEoE,IAAI,EAAEnH,GAAG,CAAE0J,GAAQ,IAAI;UAC/B,MAAMC,WAAW,GAAkC;YACjDC,QAAQ,EAAE,kBAAkB;YAC5BC,QAAQ,EAAE,UAAU;YACpBC,MAAM,EAAE;WACT;UACD,MAAMC,WAAW,GAAkC;YACjDH,QAAQ,EAAE,IAAI,CAAC3H,MAAM,CAAC2H,QAAQ;YAC9BC,QAAQ,EAAE,IAAI,CAAC5H,MAAM,CAAC4H,QAAQ;YAC9BC,MAAM,EAAE,IAAI,CAAC7H,MAAM,CAAC6H;WACrB;UACD,MAAME,SAAS,GAAG,IAAI,CAACrC,eAAe,CAAC+B,GAAG,EAAE3H,MAAM,CAAC;UACnD,MAAMkI,UAAU,GAAGP,GAAG,CAACf,MAAM;UAC7B,MAAMlH,KAAK,GAAGiI,GAAG,CAAC1C,QAAQ;UAE1B,OAAO;YACL1B,EAAE,EAAEoE,GAAG,CAACpE,EAAE;YACV4E,KAAK,EAAED,UAAU;YACjBR,IAAI,EAAEA,IAAI;YACVjI,QAAQ,EAAE;cACRC,KAAK,EAAEA,KAAK;cACZQ,MAAM,EAAE,CACN;gBACEgD,IAAI,EAAEwE,IAAI;gBACVU,KAAK,EAAE,SAAS;gBAChBC,UAAU,EAAE,KAAK;gBACjBX,IAAI,EAAE;eACP,EACD;gBACExE,IAAI,EAAEyE,GAAG,CAACW,iBAAiB;gBAC3BF,KAAK,EAAET,GAAG,CAACW,iBAAiB,KAAK,QAAQ,GAAG,OAAO,GAAG,MAAM;gBAC5DD,UAAU,EAAE,KAAK;gBACjBX,IAAI,EAAE;eACP;aAEJ;YACDa,QAAQ,EAAE,CACR;cACErF,IAAI,EAAEwE,IAAI;cACVU,KAAK,EAAE,SAAS;cAChBC,UAAU,EAAE,KAAK;cACjBX,IAAI,EAAE;aACP,EACD;cACExE,IAAI,EAAEyE,GAAG,CAAC3H,MAAM;cAChBoI,KAAK,EAAE,SAAS;cAChBC,UAAU,EAAE,KAAK;cACjBX,IAAI,EAAE;aACP,CACF;YACD7H,QAAQ,EAAE,CACR;cACE2I,QAAQ,EAAE,MAAM;cAChB1I,KAAK,EAAE6H,GAAG,CAACc;aACZ,EACD;cACED,QAAQ,EAAE,eAAe;cACzB1I,KAAK,EAAE3C,UAAU,CAACwK,GAAG,EAAEe,WAAW,EAAE,aAAa,EAAE,OAAO;aAC3D,CACF;YACD3I,QAAQ,EAAE;cACRC,MAAM,EAAEgI,WAAW,CAACC,SAAS,CAAC;cAC9BO,QAAQ,EAAEZ,WAAW,CAACK,SAAS;;WAElC;QACH,CAAC,CAAC;QACFU,MAAM,EAAE;OACT;IACH;;uCAlUSzH,sBAAsB,EAAA/C,EAAA,CAAAyK,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAA3K,EAAA,CAAAyK,iBAAA,CAAAG,EAAA,CAAAC,uBAAA,GAAA7K,EAAA,CAAAyK,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAA/K,EAAA,CAAAyK,iBAAA,CAAAO,EAAA,CAAAC,WAAA;IAAA;;YAAtBlI,sBAAsB;MAAAmI,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC/B/BxL,EADJ,CAAAC,cAAA,aAAmC,aACI;UAO/BD,EANA,CAAAE,SAAA,uBAEgB,uBAGA,uBAGA;UACpBF,EAAA,CAAAI,YAAA,EAAM;UAEVJ,EAAA,CAAAC,cAAA,aAA4B;UACxBD,EAAA,CAAAE,SAAA,aASM;UAIMF,EAHZ,CAAAC,cAAA,aAA6B,UACpB,cAC8B,sBACoD;UAC3ED,EAAA,CAAAE,SAAA,mBAAgH;UAExHF,EADI,CAAAI,YAAA,EAAc,EACX;UACPJ,EAAA,CAAAC,cAAA,qBAKkC;UAA7BD,EAAA,CAAAK,UAAA,uBAAAqL,iEAAAC,MAAA;YAAA,OAAaF,GAAA,CAAAvE,MAAA,CAAAyE,MAAA,CAAc;UAAA,EAAC;UAG7C3L,EAH8C,CAAAI,YAAA,EAAa,EAC7C,EACJ,EACJ;UAEFJ,EAAA,CAAAC,cAAA,eAAmC;UA0E/BD,EAzEA,CAAA4L,UAAA,KAAAC,8CAAA,OAAsB,KAAAC,8CAAA,kBAyEhB;UAMd9L,EADI,CAAAI,YAAA,EAAM,EACJ;UAENJ,EAAA,CAAAC,cAAA,qBAaC;UADCD,EAFA,CAAAK,UAAA,qBAAA0L,8DAAA;YAAA,OAAWN,GAAA,CAAAtD,cAAA,EAAgB;UAAA,EAAC,oBAAA6D,6DAAA;YAAA,OAAAP,GAAA,CAAAnH,qBAAA,GACI,KAAK;UAAA,EAAC,oBAAA2H,6DAAA;YAAA,OAAAR,GAAA,CAAAnH,qBAAA,GACN,KAAK;UAAA,EAAC;UAExCtE,EAAA,CAAAI,YAAA,EAAY;UAEZJ,EAAA,CAAAC,cAAA,qBAEoG;UAA7BD,EAAA,CAAAK,UAAA,oBAAA6L,6DAAA;YAAA,OAAUT,GAAA,CAAAtC,eAAA,EAAiB;UAAA,EAAC;UACnGnJ,EAAA,CAAAI,YAAA,EAAY;UAEZJ,EAAA,CAAAC,cAAA,kCAE2G;UAApCD,EAArC,CAAAK,UAAA,oBAAA8L,0EAAA;YAAA,OAAAV,GAAA,CAAA9G,iBAAA,GAA8B,KAAK;UAAA,EAAC,qBAAAyH,2EAAAT,MAAA;YAAA,OAAYF,GAAA,CAAApD,eAAA,CAAAsD,MAAA,CAAuB;UAAA,EAAC;UAC1G3L,EAAA,CAAAI,YAAA,EAAyB;UAEzBJ,EAAA,CAAAC,cAAA,qBAEoG;UAAnCD,EAAA,CAAAK,UAAA,oBAAAgM,6DAAA;YAAA,OAAAZ,GAAA,CAAAjH,cAAA,GAA2B,KAAK;UAAA,EAAC;UAClGxE,EAAA,CAAAI,YAAA,EAAY;;;UAtJWJ,EAAA,CAAAmB,SAAA,GAAkB;UAC7BnB,EADW,CAAA8B,UAAA,mBAAkB,yBAAyB,UAAA2J,GAAA,CAAA1J,MAAA,CAAAwB,cAAA,CAAgC,UAAAkI,GAAA,CAAAlI,cAAA,CAAyB,gBAAAkI,GAAA,CAAAhK,UAAA,SAAAgK,GAAA,CAAA1J,MAAA,CAAAuK,4BAAA,CACzC;UAE3DtM,EAAA,CAAAmB,SAAA,EAAkB;UAC7BnB,EADW,CAAA8B,UAAA,mBAAkB,4BAA4B,UAAA2J,GAAA,CAAA1J,MAAA,CAAAsB,sBAAA,CAAwC,UAAAoI,GAAA,CAAApI,sBAAA,CAAiC,gBAAAoI,GAAA,CAAAhK,UAAA,SAAAgK,GAAA,CAAA1J,MAAA,CAAAwK,gBAAA,CACxE;UAE/CvM,EAAA,CAAAmB,SAAA,EAAkB;UAC7BnB,EADW,CAAA8B,UAAA,mBAAkB,yBAAyB,UAAA2J,GAAA,CAAA1J,MAAA,CAAAuB,qBAAA,CAAuC,UAAAmI,GAAA,CAAAnI,qBAAA,CAAgC,gBAAAmI,GAAA,CAAA1J,MAAA,CAAAyK,GAAA,SAAAf,GAAA,CAAAhK,UAAA,SAAAgK,GAAA,CAAA1J,MAAA,CAAA0K,gBAAA,CAChD;UAiBvEzM,EAAA,CAAAmB,SAAA,GAAwB;UAAxBnB,EAAA,CAAA8B,UAAA,cAAA2J,GAAA,CAAA7G,UAAA,CAAwB;UACb5E,EAAA,CAAAmB,SAAA,EAAwC;UAAxCnB,EAAA,CAAA8B,UAAA,gBAAA2J,GAAA,CAAA1J,MAAA,CAAA2K,iBAAA,CAAwC;UACH1M,EAAA,CAAAmB,SAAA,EAAe;UAAfnB,EAAA,CAAA8B,UAAA,gBAAe;UAIrE9B,EAAA,CAAAmB,SAAA,EAII;UAJJnB,EAAA,CAAA8B,UAAA,iBAAA9B,EAAA,CAAAiC,eAAA,KAAAI,GAAA,EAII;UAMRrC,EAAA,CAAAmB,SAAA,GA6EC;UA7EDnB,EAAA,CAAA2M,aAAA,CAAAlB,GAAA,CAAA/I,YAAA,eA6EC;UAKP1C,EAAA,CAAAmB,SAAA,GAA8B;UAQ9BnB,EARA,CAAA8B,UAAA,SAAA2J,GAAA,CAAAnH,qBAAA,CAA8B,UAAAmH,GAAA,CAAA1J,MAAA,CAAA6K,eAAA,CACE,YAAAnB,GAAA,CAAA1J,MAAA,CAAA8K,wBAAA,SAAApB,GAAA,CAAAhK,UAAA,UAAAgK,GAAA,CAAA1J,MAAA,CAAA+K,4BAAA,SAAArB,GAAA,CAAAhK,UAAA,SAAAgK,GAAA,CAAA1J,MAAA,CAAAgL,0BAAA,CACwI,mBACtJ,oBACC,qBACC,uBAAAtB,GAAA,CAAA1J,MAAA,CAAAK,OAAA,CACiB,mCACH,sCACG;UAQlBpC,EAAA,CAAAmB,SAAA,EAAuB;UAAvBnB,EAAA,CAAAwB,qBAAA,YAAAiK,GAAA,CAAAhH,WAAA,CAAuB;UACQzE,EAFf,CAAA8B,UAAA,SAAA2J,GAAA,CAAAlH,aAAA,CAAsB,wBACS,mBACE;UAIlEvE,EAAA,CAAAmB,SAAA,EAAuH;UAAvHnB,EAAA,CAAAgN,sBAAA,qBAAAvB,GAAA,CAAAhK,UAAA,+FAAuH;UADnGzB,EAAA,CAAA8B,UAAA,SAAA2J,GAAA,CAAA9G,iBAAA,CAA0B;UAM9B3E,EAAA,CAAAmB,SAAA,EAAuB;UAAvBnB,EAAA,CAAAwB,qBAAA,YAAAiK,GAAA,CAAAhH,WAAA,CAAuB;UACGzE,EAFT,CAAA8B,UAAA,SAAA2J,GAAA,CAAAjH,cAAA,CAAuB,wBACO,mBACH;;;qBDxI5DzF,YAAY,EACZG,YAAY,EACZC,qBAAqB,EACrBC,aAAa,EACbC,mBAAmB,EACnBC,iBAAiB,EACjBL,mBAAmB,EAAA+L,EAAA,CAAAiC,aAAA,EAAAjC,EAAA,CAAAkC,eAAA,EAAAlC,EAAA,CAAAmC,oBAAA,EAAAnC,EAAA,CAAAoC,kBAAA,EAAApC,EAAA,CAAAqC,eAAA,EACnB5N,eAAe,EACfC,eAAe,EACfH,cAAc,EACdC,0BAA0B;MAAA8N,MAAA;IAAA;;SAOjBvK,sBAAsB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}