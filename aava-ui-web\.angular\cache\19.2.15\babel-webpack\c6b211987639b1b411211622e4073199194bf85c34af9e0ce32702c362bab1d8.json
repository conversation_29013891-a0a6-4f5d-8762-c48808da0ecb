{"ast": null, "code": "import { daysInWeek } from \"./constants.js\";\n\n/**\n * @name weeksToDays\n * @category Conversion Helpers\n * @summary Convert weeks to days.\n *\n * @description\n * Convert a number of weeks to a full number of days.\n *\n * @param weeks - The number of weeks to be converted\n *\n * @returns The number of weeks converted in days\n *\n * @example\n * // Convert 2 weeks into days\n * const result = weeksToDays(2)\n * //=> 14\n */\nexport function weeksToDays(weeks) {\n  return Math.trunc(weeks * daysInWeek);\n}\n\n// Fallback for modularized imports:\nexport default weeksToDays;", "map": {"version": 3, "names": ["daysInWeek", "weeksToDays", "weeks", "Math", "trunc"], "sources": ["C:/console/aava-ui-web/node_modules/date-fns/weeksToDays.js"], "sourcesContent": ["import { daysInWeek } from \"./constants.js\";\n\n/**\n * @name weeksToDays\n * @category Conversion Helpers\n * @summary Convert weeks to days.\n *\n * @description\n * Convert a number of weeks to a full number of days.\n *\n * @param weeks - The number of weeks to be converted\n *\n * @returns The number of weeks converted in days\n *\n * @example\n * // Convert 2 weeks into days\n * const result = weeksToDays(2)\n * //=> 14\n */\nexport function weeksToDays(weeks) {\n  return Math.trunc(weeks * daysInWeek);\n}\n\n// Fallback for modularized imports:\nexport default weeksToDays;\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,gBAAgB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,KAAK,EAAE;EACjC,OAAOC,IAAI,CAACC,KAAK,CAACF,KAAK,GAAGF,UAAU,CAAC;AACvC;;AAEA;AACA,eAAeC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}