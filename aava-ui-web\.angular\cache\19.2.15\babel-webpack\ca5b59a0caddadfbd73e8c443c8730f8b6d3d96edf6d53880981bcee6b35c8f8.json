{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { Subject, takeUntil, switchMap, finalize, catchError, of } from 'rxjs';\nimport { FormsModule } from '@angular/forms';\n// Import child components\nimport { AgentExecutionPlaygroundComponent } from './components/agent-execution-playground/agent-execution-playground.component';\nimport { IconComponent } from '@ava/play-comp-library';\nimport { environment } from 'projects/console/src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../services/agent-service.service\";\nimport * as i3 from \"../build-agents/services/agent-playground.service\";\nimport * as i4 from \"@shared/auth/services/token-storage.service\";\nimport * as i5 from \"../../../shared/services/loader/loader.service\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"../../../shared/services/tool-execution/tool-execution.service\";\nimport * as i8 from \"@angular/common\";\nfunction AgentExecutionComponent_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵtext(1, \" History \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"app-agent-execution-playground\", 22);\n    i0.ɵɵlistener(\"promptChange\", function AgentExecutionComponent_div_13_Template_app_agent_execution_playground_promptChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPromptChanged($event));\n    })(\"messageSent\", function AgentExecutionComponent_div_13_Template_app_agent_execution_playground_messageSent_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.handleChatMessage($event));\n    })(\"conversationalToggle\", function AgentExecutionComponent_div_13_Template_app_agent_execution_playground_conversationalToggle_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPlaygroundConversationalToggle($event));\n    })(\"templateToggle\", function AgentExecutionComponent_div_13_Template_app_agent_execution_playground_templateToggle_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPlaygroundTemplateToggle($event));\n    })(\"filesSelected\", function AgentExecutionComponent_div_13_Template_app_agent_execution_playground_filesSelected_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFilesSelected($event));\n    })(\"approvalRequested\", function AgentExecutionComponent_div_13_Template_app_agent_execution_playground_approvalRequested_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onApprovalRequested());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"messages\", ctx_r1.chatMessages)(\"isLoading\", ctx_r1.isProcessingChat)(\"agentType\", ctx_r1.agentType)(\"showChatInteractionToggles\", ctx_r1.agentType === \"individual\")(\"showAiPrincipleToggle\", true)(\"showApprovalButton\", false)(\"showDropdown\", false)(\"showAgentNameInput\", true)(\"showFileUploadButton\", true)(\"displayedAgentName\", ctx_r1.agentName)(\"agentNamePlaceholder\", \"Current Agent Name\")(\"acceptedFileType\", ctx_r1.fileType);\n  }\n}\nfunction AgentExecutionComponent_div_22_div_40_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78);\n    i0.ɵɵtext(1, \" No prompt configured \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_22_div_40_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 79)(1, \"span\", 80);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(node_r4.name || \"Prompt\");\n  }\n}\nfunction AgentExecutionComponent_div_22_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74);\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_22_div_40_div_1_Template, 2, 0, \"div\", 75);\n    i0.ɵɵelementStart(2, \"div\", 76);\n    i0.ɵɵtemplate(3, AgentExecutionComponent_div_22_div_40_div_3_Template, 3, 1, \"div\", 77);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.blueprintPromptNodes.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.blueprintPromptNodes);\n  }\n}\nfunction AgentExecutionComponent_div_22_div_61_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78);\n    i0.ɵɵtext(1, \" No knowledge base configured \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_22_div_61_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 79)(1, \"span\", 80);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(node_r5.name || \"Knowledge Base\");\n  }\n}\nfunction AgentExecutionComponent_div_22_div_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74);\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_22_div_61_div_1_Template, 2, 0, \"div\", 75);\n    i0.ɵɵelementStart(2, \"div\", 76);\n    i0.ɵɵtemplate(3, AgentExecutionComponent_div_22_div_61_div_3_Template, 3, 1, \"div\", 77);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.blueprintKnowledgeNodes.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.blueprintKnowledgeNodes);\n  }\n}\nfunction AgentExecutionComponent_div_22_div_79_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78);\n    i0.ɵɵtext(1, \" No model configured \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_22_div_79_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 79)(1, \"span\", 80);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(node_r6.name || \"Model\");\n  }\n}\nfunction AgentExecutionComponent_div_22_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74);\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_22_div_79_div_1_Template, 2, 0, \"div\", 75);\n    i0.ɵɵelementStart(2, \"div\", 76);\n    i0.ɵɵtemplate(3, AgentExecutionComponent_div_22_div_79_div_3_Template, 3, 1, \"div\", 77);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.blueprintModelNodes.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.blueprintModelNodes);\n  }\n}\nfunction AgentExecutionComponent_div_22_div_95_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78);\n    i0.ɵɵtext(1, \" No guardrails configured \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_22_div_95_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 79)(1, \"span\", 80);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(node_r7.name || \"Guardrail\");\n  }\n}\nfunction AgentExecutionComponent_div_22_div_95_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74);\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_22_div_95_div_1_Template, 2, 0, \"div\", 75);\n    i0.ɵɵelementStart(2, \"div\", 76);\n    i0.ɵɵtemplate(3, AgentExecutionComponent_div_22_div_95_div_3_Template, 3, 1, \"div\", 77);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.blueprintGuardrailNodes.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.blueprintGuardrailNodes);\n  }\n}\nfunction AgentExecutionComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 24)(2, \"h3\");\n    i0.ɵɵtext(3, \"Agent Blueprint\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 25);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(5, \"svg\", 26);\n    i0.ɵɵelement(6, \"line\", 27)(7, \"line\", 28)(8, \"line\", 29)(9, \"line\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(10, \"div\", 31)(11, \"div\", 32);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(12, \"svg\", 33)(13, \"defs\")(14, \"linearGradient\", 34);\n    i0.ɵɵelement(15, \"stop\", 35)(16, \"stop\", 36);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(17, \"circle\", 37)(18, \"circle\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(19, \"div\", 39)(20, \"div\", 40);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 41);\n    i0.ɵɵtext(23, \"Complete\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(24, \"div\", 42)(25, \"div\", 43)(26, \"div\", 44);\n    i0.ɵɵlistener(\"click\", function AgentExecutionComponent_div_22_Template_div_click_26_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleBlueprintZone(\"prompt\"));\n    });\n    i0.ɵɵelementStart(27, \"div\", 45)(28, \"div\", 46);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(29, \"svg\", 47);\n    i0.ɵɵelement(30, \"rect\", 48)(31, \"path\", 49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(32, \"h3\", 50);\n    i0.ɵɵtext(33, \"System Prompt\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 51)(35, \"span\", 52);\n    i0.ɵɵtext(36, \"Required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"button\", 53);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(38, \"svg\", 54);\n    i0.ɵɵelement(39, \"path\", 55);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(40, AgentExecutionComponent_div_22_div_40_Template, 4, 2, \"div\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(41, \"div\", 57)(42, \"div\", 44);\n    i0.ɵɵlistener(\"click\", function AgentExecutionComponent_div_22_Template_div_click_42_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleBlueprintZone(\"knowledge\"));\n    });\n    i0.ɵɵelementStart(43, \"div\", 45)(44, \"div\", 46);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(45, \"svg\", 47);\n    i0.ɵɵelement(46, \"rect\", 58)(47, \"path\", 59)(48, \"path\", 60)(49, \"path\", 61)(50, \"path\", 62)(51, \"path\", 63)(52, \"path\", 64);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(53, \"h3\", 50);\n    i0.ɵɵtext(54, \"Knowledgebase\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(55, \"div\", 51)(56, \"span\", 65);\n    i0.ɵɵtext(57, \"Optional\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"button\", 53);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(59, \"svg\", 54);\n    i0.ɵɵelement(60, \"path\", 55);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(61, AgentExecutionComponent_div_22_div_61_Template, 4, 2, \"div\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(62, \"div\", 66)(63, \"div\", 44);\n    i0.ɵɵlistener(\"click\", function AgentExecutionComponent_div_22_Template_div_click_63_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleBlueprintZone(\"model\"));\n    });\n    i0.ɵɵelementStart(64, \"div\", 45)(65, \"div\", 46);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(66, \"svg\", 47);\n    i0.ɵɵelement(67, \"rect\", 67)(68, \"path\", 68)(69, \"path\", 69)(70, \"path\", 70);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(71, \"h3\", 50);\n    i0.ɵɵtext(72, \"AI Model\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(73, \"div\", 51)(74, \"span\", 52);\n    i0.ɵɵtext(75, \"Required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(76, \"button\", 53);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(77, \"svg\", 54);\n    i0.ɵɵelement(78, \"path\", 55);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(79, AgentExecutionComponent_div_22_div_79_Template, 4, 2, \"div\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(80, \"div\", 71)(81, \"div\", 44);\n    i0.ɵɵlistener(\"click\", function AgentExecutionComponent_div_22_Template_div_click_81_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleBlueprintZone(\"guardrail\"));\n    });\n    i0.ɵɵelementStart(82, \"div\", 45)(83, \"div\", 46);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(84, \"svg\", 47);\n    i0.ɵɵelement(85, \"rect\", 72)(86, \"path\", 73);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(87, \"h3\", 50);\n    i0.ɵɵtext(88, \"Guardrails\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(89, \"div\", 51)(90, \"span\", 65);\n    i0.ɵɵtext(91, \"Optional\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(92, \"button\", 53);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(93, \"svg\", 54);\n    i0.ɵɵelement(94, \"path\", 55);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(95, AgentExecutionComponent_div_22_div_95_Template, 4, 2, \"div\", 56);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(18);\n    i0.ɵɵstyleProp(\"stroke-dasharray\", 314)(\"stroke-dashoffset\", 314 - 314 * ctx_r1.blueprintCompletionPercentage / 100);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.blueprintCompletionPercentage, \"% \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"has-nodes\", ctx_r1.blueprintPromptNodes.length > 0);\n    i0.ɵɵadvance(13);\n    i0.ɵɵstyleProp(\"transform\", ctx_r1.isBlueprintZoneExpanded(\"prompt\") ? \"rotate(180deg)\" : \"rotate(0deg)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isBlueprintZoneExpanded(\"prompt\"));\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"has-nodes\", ctx_r1.blueprintKnowledgeNodes.length > 0);\n    i0.ɵɵadvance(18);\n    i0.ɵɵstyleProp(\"transform\", ctx_r1.isBlueprintZoneExpanded(\"knowledge\") ? \"rotate(180deg)\" : \"rotate(0deg)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isBlueprintZoneExpanded(\"knowledge\"));\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"has-nodes\", ctx_r1.blueprintModelNodes.length > 0);\n    i0.ɵɵadvance(15);\n    i0.ɵɵstyleProp(\"transform\", ctx_r1.isBlueprintZoneExpanded(\"model\") ? \"rotate(180deg)\" : \"rotate(0deg)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isBlueprintZoneExpanded(\"model\"));\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"has-nodes\", ctx_r1.blueprintGuardrailNodes.length > 0);\n    i0.ɵɵadvance(13);\n    i0.ɵɵstyleProp(\"transform\", ctx_r1.isBlueprintZoneExpanded(\"guardrail\") ? \"rotate(180deg)\" : \"rotate(0deg)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isBlueprintZoneExpanded(\"guardrail\"));\n  }\n}\nfunction AgentExecutionComponent_div_23_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86)(1, \"div\", 87)(2, \"h4\");\n    i0.ɵɵtext(3, \"Response\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 88);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.latestAgentResponse.response.choices[0].text, \" \");\n  }\n}\nfunction AgentExecutionComponent_div_23_div_1_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"No agent output available yet. Send a message to see the response.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_23_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 84)(1, \"h3\");\n    i0.ɵɵtext(2, \"Agent Output\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, AgentExecutionComponent_div_23_div_1_div_3_Template, 6, 1, \"div\", 85)(4, AgentExecutionComponent_div_23_div_1_ng_template_4_Template, 2, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const noIndividualOutput_r8 = i0.ɵɵreference(5);\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.latestAgentResponse == null ? null : ctx_r1.latestAgentResponse.response == null ? null : ctx_r1.latestAgentResponse.response.choices == null ? null : ctx_r1.latestAgentResponse.response.choices.length) > 0)(\"ngIfElse\", noIndividualOutput_r8);\n  }\n}\nfunction AgentExecutionComponent_div_23_div_2_div_3_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 94)(1, \"strong\");\n    i0.ɵɵtext(2, \"Description:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const taskOutput_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", taskOutput_r9.description, \" \");\n  }\n}\nfunction AgentExecutionComponent_div_23_div_2_div_3_div_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 95)(1, \"strong\");\n    i0.ɵɵtext(2, \"Expected Output:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const taskOutput_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", taskOutput_r9.expected_output, \" \");\n  }\n}\nfunction AgentExecutionComponent_div_23_div_2_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86)(1, \"div\", 87)(2, \"h4\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, AgentExecutionComponent_div_23_div_2_div_3_div_1_div_4_Template, 4, 1, \"div\", 92)(5, AgentExecutionComponent_div_23_div_2_div_3_div_1_div_5_Template, 4, 1, \"div\", 93);\n    i0.ɵɵelementStart(6, \"div\", 88);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const taskOutput_r9 = ctx.$implicit;\n    const i_r10 = ctx.index;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(taskOutput_r9.summary || \"Task \" + (i_r10 + 1));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", taskOutput_r9.description);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", taskOutput_r9.expected_output);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", taskOutput_r9.raw, \" \");\n  }\n}\nfunction AgentExecutionComponent_div_23_div_2_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_23_div_2_div_3_div_1_Template, 8, 4, \"div\", 91);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.latestAgentResponse.agentResponse.agent.tasksOutputs);\n  }\n}\nfunction AgentExecutionComponent_div_23_div_2_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"No task outputs available yet. Send a message to see the results.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_23_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"h3\");\n    i0.ɵɵtext(2, \"Task Outputs\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, AgentExecutionComponent_div_23_div_2_div_3_Template, 2, 1, \"div\", 90)(4, AgentExecutionComponent_div_23_div_2_ng_template_4_Template, 2, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const noCollaborativeOutput_r11 = i0.ɵɵreference(5);\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.latestAgentResponse == null ? null : ctx_r1.latestAgentResponse.agentResponse == null ? null : ctx_r1.latestAgentResponse.agentResponse.agent == null ? null : ctx_r1.latestAgentResponse.agentResponse.agent.tasksOutputs == null ? null : ctx_r1.latestAgentResponse.agentResponse.agent.tasksOutputs.length) > 0)(\"ngIfElse\", noCollaborativeOutput_r11);\n  }\n}\nfunction AgentExecutionComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 81);\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_23_div_1_Template, 6, 2, \"div\", 82)(2, AgentExecutionComponent_div_23_div_2_Template, 6, 2, \"div\", 83);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.agentType === \"individual\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.agentType === \"collaborative\");\n  }\n}\nexport var ExecutionStatus = /*#__PURE__*/function (ExecutionStatus) {\n  ExecutionStatus[\"notStarted\"] = \"notStarted\";\n  ExecutionStatus[\"running\"] = \"running\";\n  ExecutionStatus[\"completed\"] = \"completed\";\n  return ExecutionStatus;\n}(ExecutionStatus || {});\nexport let AgentExecutionComponent = /*#__PURE__*/(() => {\n  class AgentExecutionComponent {\n    route;\n    router;\n    agentService;\n    agentPlaygroundService;\n    tokenStorage;\n    loaderService;\n    formBuilder;\n    toolExecutionService;\n    navigationTabs = [{\n      id: 'nav-home',\n      label: 'Agent Activity'\n    }, {\n      id: 'nav-products',\n      label: 'Agent Output'\n    }, {\n      id: 'nav-services',\n      label: 'Preview',\n      disabled: true\n    }];\n    // Agent details\n    agentId = null;\n    agentType = 'individual';\n    agentName = 'Agent';\n    agentDetail = '';\n    playgroundComp;\n    // Activity logs\n    activityLogs = [];\n    activityProgress = 0;\n    executionDetails;\n    isRunning = false;\n    status = ExecutionStatus.notStarted;\n    // Chat messages\n    chatMessages = [];\n    isProcessingChat = false;\n    inputText = '';\n    // Agent outputs\n    agentOutputs = [];\n    latestAgentResponse = null; // Store the latest agent response for display\n    agentForm;\n    fileType = '.pdf,.doc,.docx,.txt,.csv,.xlsx,.xls,.ppt,.pptx,.png,.jpg,.jpeg,.gif,.bmp,.svg';\n    // Execution state\n    executionStartTime = null;\n    executionCompleted = false;\n    executionId;\n    enableStreamingLog = environment.enableLogStreaming || 'all';\n    isExecutionComplete = false;\n    progressInterval;\n    destroy$ = new Subject();\n    selectedTab = 'Agent Activity';\n    demoTabs = [{\n      id: 'tab1',\n      label: 'History'\n    }, {\n      id: 'tab2',\n      label: 'Blueprint'\n    }, {\n      id: 'tab3',\n      label: 'Agent Output'\n    }];\n    errorMsg = false;\n    resMessage;\n    taskMessage = [];\n    isJsonValid = false;\n    disableChat = false;\n    selectedFiles = [];\n    agentNodes = [];\n    userInputList = [];\n    progress = 0;\n    isLoading = false;\n    loaderColor = '';\n    inputFieldOrder = [];\n    currentInputIndex = 0;\n    activeTabId = 'nav-home';\n    // Panel state properties\n    isLeftPanelCollapsed = false;\n    activeRightTab = 'blueprint';\n    // Agent-specific properties\n    currentAgentDetails = null;\n    buildAgentNodes = [];\n    canvasNodes = [];\n    canvasEdges = [];\n    selectedPrompt = '';\n    selectedAgentMode = '';\n    selectedUseCaseIdentifier = '';\n    agentFilesUploadedData = [];\n    agentAttachment = [];\n    isAgentPlaygroundLoading = false;\n    agentPlaygroundDestroy = new Subject();\n    agentChatPayload = [];\n    agentCode = '';\n    promptOptions = [];\n    // Custom Blueprint Display Properties\n    blueprintCompletionPercentage = 0;\n    blueprintPromptNodes = [];\n    blueprintModelNodes = [];\n    blueprintKnowledgeNodes = [];\n    blueprintGuardrailNodes = [];\n    blueprintToolNodes = [];\n    // Blueprint zone expansion state\n    blueprintZonesExpanded = {\n      prompt: true,\n      model: true,\n      knowledge: true,\n      guardrail: true,\n      tool: true\n    };\n    // Blueprint panel properties (using existing arrays above)\n    constructor(route, router, agentService, agentPlaygroundService, tokenStorage, loaderService, formBuilder, toolExecutionService) {\n      this.route = route;\n      this.router = router;\n      this.agentService = agentService;\n      this.agentPlaygroundService = agentPlaygroundService;\n      this.tokenStorage = tokenStorage;\n      this.loaderService = loaderService;\n      this.formBuilder = formBuilder;\n      this.toolExecutionService = toolExecutionService;\n      this.agentForm = this.formBuilder.group({\n        isConversational: [true],\n        isUseTemplate: [false]\n      });\n    }\n    ngOnInit() {\n      this.executionId = crypto.randomUUID();\n      this.route.params.subscribe(params => {\n        this.agentType = params['type'] || 'individual';\n      });\n      this.route.queryParams.subscribe(params => {\n        if (params['id']) {\n          this.agentId = params['id'];\n          this.loadAgentData(params['id']);\n        }\n      });\n      // Initialize chat messages\n      this.chatMessages = [{\n        from: 'ai',\n        text: `Hi there! I am ${this.agentName || 'your agent'}. How can I help you today?`\n      }];\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n      if (this.progressInterval) {\n        clearInterval(this.progressInterval);\n      }\n    }\n    onTabChange(event) {\n      this.activeTabId = event.id;\n      this.selectedTab = event.label;\n    }\n    loadAgentData(agentId) {\n      this.isLoading = true;\n      // Load agent data based on type\n      if (this.agentType === 'collaborative') {\n        this.agentService.getCollaborativeAgentDetailsById(agentId).subscribe({\n          next: response => {\n            this.handleAgentDataResponse(response);\n          },\n          error: error => {\n            console.error('Error loading collaborative agent:', error);\n            this.isLoading = false;\n          }\n        });\n      } else {\n        this.agentService.getAgentById(agentId).subscribe({\n          next: response => {\n            this.handleAgentDataResponse(response);\n          },\n          error: error => {\n            console.error('Error loading individual agent:', error);\n            this.isLoading = false;\n          }\n        });\n      }\n    }\n    handleAgentDataResponse(response) {\n      this.isLoading = false;\n      // Extract agent details\n      let agentData;\n      if (response.agentDetails && Array.isArray(response.agentDetails) && response.agentDetails.length > 0) {\n        agentData = response.agentDetails[0];\n      } else if (response.agentDetail) {\n        agentData = response.agentDetail;\n      } else if (response.data) {\n        agentData = response.data;\n      } else {\n        agentData = response;\n      }\n      if (agentData) {\n        this.currentAgentDetails = agentData;\n        this.agentName = agentData.name || agentData.agentName || 'Agent';\n        this.agentDetail = agentData.description || agentData.agentDetail || '';\n        // For individual agents, set up the required properties for playground functionality\n        if (this.agentType === 'individual') {\n          // Set selectedPrompt to agent name or useCaseName to bypass dropdown requirement\n          this.selectedPrompt = agentData.useCaseName || agentData.name || 'loaded-agent';\n          // Set selectedAgentMode for API calls - use useCaseCode if available\n          this.selectedAgentMode = agentData.useCaseCode || agentData.useCaseName || agentData.name || '';\n          // Set useCaseIdentifier - use organizationPath if available\n          if (agentData.organizationPath) {\n            this.selectedUseCaseIdentifier = agentData.organizationPath;\n          } else if (agentData.useCaseCode) {\n            this.selectedUseCaseIdentifier = agentData.useCaseCode;\n          } else if (agentData.useCaseName) {\n            this.selectedUseCaseIdentifier = agentData.useCaseName;\n          }\n        }\n        // Update chat message with agent name\n        if (this.chatMessages.length > 0) {\n          this.chatMessages[0].text = `Hi there! I am ${this.agentName}. How can I help you today?`;\n        }\n        // Load agent nodes and configuration\n        this.loadAgentNodes(agentData);\n      }\n    }\n    loadAgentNodes(agentData) {\n      // Map agent configuration to blueprint panel\n      this.mapAgentConfigurationToBlueprint(agentData);\n    }\n    handleChatMessage(message) {\n      if (this.agentType === 'individual') {\n        // For individual agents, use the loaded agent details instead of requiring dropdown selection\n        if (!this.currentAgentDetails && (!this.selectedPrompt || this.selectedPrompt === 'default')) {\n          this.showAgentError('Agent details are not loaded. Please try refreshing the page.');\n          return;\n        }\n        let displayMessage = message;\n        if (this.agentFilesUploadedData.length > 0) {\n          const fileNames = this.agentFilesUploadedData.map(file => file.documentName).join(', ');\n          displayMessage = `${message}\\n\\n📎 Attached files: ${fileNames}`;\n        }\n        this.chatMessages = [...this.chatMessages, {\n          from: 'user',\n          text: displayMessage\n        }];\n        this.isProcessingChat = true;\n        const isConversational = this.agentForm.get('isConversational')?.value || false;\n        const isUseTemplate = this.agentForm.get('isUseTemplate')?.value || false;\n        console.log('Chat message handling - isConversational:', isConversational, 'isUseTemplate:', isUseTemplate);\n        // Use agent details from the loaded agent data instead of dropdown selection\n        // Mode should be the useCaseCode, not useCaseName\n        const agentMode = this.agentCode || this.selectedAgentMode || this.currentAgentDetails?.useCaseCode || this.currentAgentDetails?.useCaseName || this.currentAgentDetails?.name || this.selectedPrompt;\n        let useCaseIdentifier = this.selectedUseCaseIdentifier;\n        if (!useCaseIdentifier) {\n          // Use organizationPath if available, otherwise build from agent details\n          if (this.currentAgentDetails?.organizationPath) {\n            useCaseIdentifier = this.currentAgentDetails.organizationPath;\n          } else {\n            const orgPath = this.buildOrganizationPath();\n            const agentIdentifier = this.currentAgentDetails?.useCaseCode || this.currentAgentDetails?.useCaseName || this.currentAgentDetails?.name || agentMode;\n            useCaseIdentifier = `${agentIdentifier}${orgPath}`;\n          }\n        }\n        if (this.agentFilesUploadedData.length > 0) {\n          this.processAgentFilesAndSendMessage(message, agentMode, useCaseIdentifier, isConversational, isUseTemplate);\n          return;\n        }\n        this.sendAgentMessageToAPI(message, agentMode, useCaseIdentifier, isConversational, isUseTemplate);\n      } else if (this.agentType === 'collaborative') {\n        this.isProcessingChat = true;\n        let payload = {\n          executionId: this.executionId,\n          agentId: Number(this.agentId),\n          user: this.tokenStorage.getDaUsername() || '<EMAIL>',\n          userInputs: {\n            question: message\n          }\n        };\n        if (this.agentFilesUploadedData.length > 0) {\n          const fileWrapper = this.agentFilesUploadedData[0];\n          let displayMessage;\n          if (this.agentFilesUploadedData.length > 0) {\n            const fileNames = this.agentFilesUploadedData.map(file => file.documentName).join(', ');\n            displayMessage = `📎 Attached files: ${fileNames}`;\n            this.chatMessages = [{\n              from: 'user',\n              text: displayMessage\n            }];\n          }\n          this.agentPlaygroundService.submitAgentExecuteWithFile(payload, fileWrapper).pipe(finalize(() => {\n            this.isProcessingChat = false;\n            this.isAgentPlaygroundLoading = false;\n          }), takeUntil(this.agentPlaygroundDestroy)).subscribe({\n            next: res => this.handleAgentExecuteResponse(res, message),\n            error: err => {\n              this.chatMessages = [...this.chatMessages, {\n                from: 'user',\n                text: message\n              }, {\n                from: 'ai',\n                text: err?.error?.message || err?.message || 'Something went wrong.'\n              }];\n            }\n          });\n        } else {\n          this.agentPlaygroundService.submitAgentExecute(payload).pipe(finalize(() => {\n            this.isProcessingChat = false;\n            this.isAgentPlaygroundLoading = false;\n          }), takeUntil(this.agentPlaygroundDestroy)).subscribe({\n            next: res => this.handleAgentExecuteResponse(res, message),\n            error: err => {\n              this.chatMessages = [...this.chatMessages, {\n                from: 'user',\n                text: message\n              }, {\n                from: 'ai',\n                text: err?.error?.message || err?.message || 'Something went wrong.'\n              }];\n            }\n          });\n        }\n      }\n    }\n    onPromptChanged(prompt) {\n      this.inputText = prompt.name || String(prompt.value) || '';\n    }\n    onPlaygroundConversationalToggle(value) {\n      // Update the form control\n      this.agentForm.get('isConversational')?.setValue(value);\n      // When conversational mode is turned off, clear the conversation history\n      // This ensures that the next message will be treated as a fresh start\n      if (!value) {\n        this.agentChatPayload = [];\n        console.log('Conversational mode disabled - cleared chat payload history');\n      } else {\n        console.log('Conversational mode enabled - will maintain chat history');\n      }\n    }\n    onPlaygroundTemplateToggle(value) {\n      // Update the form control\n      this.agentForm.get('isUseTemplate')?.setValue(value);\n      console.log('Template mode toggled:', value);\n    }\n    onFilesSelected(files) {\n      this.selectedFiles = files;\n      // Update agentFilesUploadedData for agent execution\n      this.agentFilesUploadedData = files;\n    }\n    onApprovalRequested() {\n      // Handle approval request\n    }\n    saveLogs() {\n      // Save execution logs\n    }\n    exportResults(section) {\n      // Export results\n    }\n    handleControlAction(action) {\n      // Handle execution control actions\n    }\n    navigateBack() {\n      this.router.navigate(['/build/agents', this.agentType], {\n        queryParams: {\n          id: this.agentId,\n          mode: 'view'\n        }\n      });\n    }\n    editAgent() {\n      this.router.navigate(['/build/agents', this.agentType], {\n        queryParams: {\n          id: this.agentId,\n          mode: 'edit'\n        }\n      });\n    }\n    navigateToAgentsList() {\n      this.router.navigate(['/build/agents']);\n    }\n    toggleLeftPanel() {\n      this.isLeftPanelCollapsed = !this.isLeftPanelCollapsed;\n    }\n    setActiveRightTab(tab) {\n      this.activeRightTab = tab;\n    }\n    // Blueprint zone management methods\n    toggleBlueprintZone(zoneType) {\n      this.blueprintZonesExpanded[zoneType] = !this.blueprintZonesExpanded[zoneType];\n    }\n    isBlueprintZoneExpanded(zoneType) {\n      return this.blueprintZonesExpanded[zoneType] || false;\n    }\n    // API and helper methods from build-agents component\n    showAgentError(message) {\n      this.chatMessages = [...this.chatMessages, {\n        from: 'ai',\n        text: message\n      }];\n    }\n    buildOrganizationPath() {\n      // Simple implementation - in real scenario this would be from navbar/metadata\n      return '';\n    }\n    getMetadataFromNavbar() {\n      // Simple implementation - in real scenario this would get org level mapping\n      return {};\n    }\n    handleAgentExecuteResponse(response, message) {\n      try {\n        // Store the latest response for display in the output panel\n        this.latestAgentResponse = response;\n        const outputRaw = response?.agentResponse?.agent?.output;\n        let formattedOutput = '';\n        if (outputRaw) {\n          // Directly replace escaped \\n with real newlines\n          formattedOutput = outputRaw.replace(/\\\\n/g, '\\n');\n        } else {\n          formattedOutput = response?.agentResponse?.detail;\n        }\n        this.chatMessages = [...this.chatMessages, {\n          from: 'user',\n          text: message\n        }, {\n          from: 'ai',\n          text: formattedOutput || 'No response from agent.'\n        }];\n      } catch (err) {\n        this.chatMessages = [...this.chatMessages, {\n          from: 'ai',\n          text: err?.message || 'Agent response could not be processed.'\n        }];\n      }\n    }\n    processAgentFilesAndSendMessage(message, mode, useCaseIdentifier, isConversational, isUseTemplate) {\n      const formData = new FormData();\n      this.agentFilesUploadedData.forEach(fileData => {\n        if (fileData.file) {\n          formData.append('files', fileData.file);\n        }\n      });\n      if (formData.has('files')) {\n        this.agentPlaygroundService.getFileToContent(formData).pipe(switchMap(fileResponse => {\n          const fileContent = fileResponse?.fileResponses?.map(response => response.fileContent)?.join('\\n') || '';\n          this.sendAgentMessageToAPIWithFiles(message, mode, useCaseIdentifier, isConversational, isUseTemplate, fileContent);\n          return of(null);\n        }), catchError(error => {\n          console.error('Error parsing files:', error);\n          this.sendAgentMessageToAPI(message, mode, useCaseIdentifier, isConversational, isUseTemplate);\n          return of(null);\n        })).subscribe();\n      } else {\n        this.sendAgentMessageToAPI(message, mode, useCaseIdentifier, isConversational, isUseTemplate);\n      }\n    }\n    sendAgentMessageToAPI(message, mode, useCaseIdentifier, isConversational, isUseTemplate) {\n      console.log('API Call Parameters:', {\n        message,\n        mode,\n        useCaseIdentifier,\n        isConversational,\n        isUseTemplate,\n        currentChatPayloadLength: this.agentChatPayload.length\n      });\n      if (isConversational) {\n        this.agentChatPayload.push({\n          content: message,\n          role: 'user'\n        });\n      }\n      const payload = isConversational ? this.agentChatPayload : message;\n      const {\n        levelId\n      } = this.getMetadataFromNavbar();\n      console.log('Final payload being sent:', payload);\n      this.agentPlaygroundService.generatePrompt(payload, mode, isConversational, isUseTemplate, this.agentAttachment, useCaseIdentifier, '', levelId).pipe(finalize(() => {\n        this.isProcessingChat = false;\n        this.isAgentPlaygroundLoading = false;\n      }), takeUntil(this.agentPlaygroundDestroy)).subscribe({\n        next: generatedResponse => {\n          // Store the latest response for display in the output panel\n          this.latestAgentResponse = generatedResponse;\n          if (generatedResponse?.response && generatedResponse?.response?.choices) {\n            const aiResponseText = generatedResponse.response.choices[0].text;\n            this.chatMessages = [...this.chatMessages, {\n              from: 'ai',\n              text: aiResponseText\n            }];\n            if (isConversational) {\n              this.agentChatPayload.push({\n                content: aiResponseText,\n                role: 'assistant'\n              });\n            }\n          } else {\n            console.warn('Unexpected API response format:', generatedResponse);\n            this.showAgentError('Received unexpected response format from API.');\n          }\n        },\n        error: error => {\n          console.error('API Error:', error);\n          const errorMessage = error?.error?.message || 'An error occurred while processing your request.';\n          this.showAgentError(errorMessage);\n          if (isConversational && this.agentChatPayload.length > 0) {\n            this.agentChatPayload.pop();\n          }\n        }\n      });\n    }\n    sendAgentMessageToAPIWithFiles(message, mode, useCaseIdentifier, isConversational, isUseTemplate, fileContents) {\n      if (isConversational) {\n        this.agentChatPayload.push({\n          content: message,\n          role: 'user'\n        });\n      }\n      const payload = isConversational ? this.agentChatPayload : message;\n      const {\n        levelId\n      } = this.getMetadataFromNavbar();\n      this.agentPlaygroundService.generatePrompt(payload, mode, isConversational, isUseTemplate, this.agentAttachment, useCaseIdentifier, fileContents, levelId).pipe(finalize(() => {\n        this.isProcessingChat = false;\n        this.isAgentPlaygroundLoading = false;\n      }), takeUntil(this.agentPlaygroundDestroy)).subscribe({\n        next: generatedResponse => {\n          if (generatedResponse?.response && generatedResponse?.response?.choices) {\n            const aiResponseText = generatedResponse.response.choices[0].text;\n            this.chatMessages = [...this.chatMessages, {\n              from: 'ai',\n              text: aiResponseText\n            }];\n            if (isConversational) {\n              this.agentChatPayload.push({\n                content: aiResponseText,\n                role: 'assistant'\n              });\n            }\n          } else {\n            console.warn('Unexpected API response format:', generatedResponse);\n            this.showAgentError('Received unexpected response format from API.');\n          }\n        },\n        error: error => {\n          console.error('API Error:', error);\n          const errorMessage = error?.error?.message || 'An error occurred while processing your request.';\n          this.showAgentError(errorMessage);\n          if (isConversational && this.agentChatPayload.length > 0) {\n            this.agentChatPayload.pop();\n          }\n        }\n      });\n    }\n    // Blueprint panel methods\n    mapAgentConfigurationToBlueprint(agentData) {\n      if (!agentData) {\n        console.warn('No agent data provided for blueprint');\n        return;\n      }\n      console.log('🔍 DEBUG: Full agent data received:', agentData);\n      console.log('🔍 DEBUG: Agent type:', this.agentType);\n      console.log('🔍 DEBUG: Agent data keys:', Object.keys(agentData));\n      // Clear existing nodes\n      this.buildAgentNodes = [];\n      this.canvasNodes = [];\n      let nodeCounter = 1;\n      // Map agent configuration to nodes based on agent type\n      if (this.agentType === 'individual') {\n        this.mapIndividualAgentToBlueprint(agentData, nodeCounter);\n      } else if (this.agentType === 'collaborative') {\n        this.mapCollaborativeAgentToBlueprint(agentData, nodeCounter);\n      }\n      console.log('🎯 Blueprint nodes mapped:', {\n        buildAgentNodes: this.buildAgentNodes,\n        canvasNodes: this.canvasNodes,\n        totalNodes: this.buildAgentNodes.length\n      });\n    }\n    mapIndividualAgentToBlueprint(agentData, nodeCounter) {\n      console.log('🔍 Individual agent mapping - checking fields:', {\n        config: agentData.config,\n        configLength: agentData.config?.length,\n        useCaseName: agentData.useCaseName,\n        promptTemplate: agentData.promptTemplate\n      });\n      // Clear existing blueprint nodes\n      this.blueprintPromptNodes = [];\n      this.blueprintModelNodes = [];\n      this.blueprintKnowledgeNodes = [];\n      this.blueprintGuardrailNodes = [];\n      // Add prompt node - individual agents always have a prompt\n      if (agentData.useCaseName || agentData.promptTemplate) {\n        this.blueprintPromptNodes.push({\n          id: `prompt-${nodeCounter++}`,\n          name: agentData.useCaseName || agentData.promptTemplate || 'System Prompt',\n          type: 'prompt'\n        });\n      }\n      // Process the config array to extract model, tools, knowledge bases, etc.\n      if (agentData.config && Array.isArray(agentData.config)) {\n        console.log('🔍 Processing config array with length:', agentData.config.length);\n        agentData.config.forEach((category, categoryIndex) => {\n          console.log(`🔍 Category ${categoryIndex}:`, category);\n          if (category.config && Array.isArray(category.config)) {\n            console.log(`🔍 Category ${categoryIndex} has ${category.config.length} config items`);\n            category.config.forEach((configItem, itemIndex) => {\n              console.log(`🔍 Config item ${categoryIndex}-${itemIndex}:`, {\n                configKey: configItem.configKey,\n                configValue: configItem.configValue,\n                type: typeof configItem.configValue\n              });\n              // Handle MODEL configuration\n              if (configItem.configKey === 'MODEL' && configItem.configValue) {\n                console.log('✅ Adding model node:', configItem.configValue);\n                this.blueprintModelNodes.push({\n                  id: `model-${nodeCounter++}`,\n                  name: `Model: ${configItem.configValue}`,\n                  type: 'model'\n                });\n              }\n              // Handle RAG_KNOWLEDGEBASE_NAME configuration\n              if (configItem.configKey === 'RAG_KNOWLEDGEBASE_NAME' && configItem.configValue) {\n                console.log('✅ Adding knowledge base node:', configItem.configValue);\n                const kbValue = configItem.configValue.toString();\n                const kbIds = kbValue.split(',').map(id => id.trim()).filter(id => id);\n                kbIds.forEach(kbId => {\n                  this.blueprintKnowledgeNodes.push({\n                    id: `knowledge-${nodeCounter++}`,\n                    name: `Knowledge Base: ${kbId}`,\n                    type: 'knowledge'\n                  });\n                });\n              }\n              // Handle ENABLE_GUARDRAILS configuration\n              if (configItem.configKey === 'ENABLE_GUARDRAILS' && (configItem.configValue === 'true' || configItem.configValue === true)) {\n                console.log('✅ Found ENABLE_GUARDRAILS = true, adding general guardrail node');\n                // Only add one general guardrail node if not already added\n                if (this.blueprintGuardrailNodes.length === 0) {\n                  this.blueprintGuardrailNodes.push({\n                    id: `guardrail-${nodeCounter++}`,\n                    name: 'Guardrails Enabled',\n                    type: 'guardrail'\n                  });\n                }\n              }\n              // Handle specific GUARDRAIL configurations\n              if (configItem.configKey && configItem.configKey.startsWith('GUARDRAIL_') && configItem.configKey !== 'ENABLE_GUARDRAILS' && (configItem.configValue === 'true' || configItem.configValue === true || configItem.configValue !== 'false')) {\n                console.log('✅ Found specific guardrail:', {\n                  key: configItem.configKey,\n                  value: configItem.configValue\n                });\n                let guardrailName = configItem.configKey;\n                if (guardrailName.startsWith('GUARDRAIL_')) {\n                  guardrailName = guardrailName.replace('GUARDRAIL_', '').replace(/_/g, ' ');\n                }\n                this.blueprintGuardrailNodes.push({\n                  id: `guardrail-${nodeCounter++}`,\n                  name: `Guardrail: ${guardrailName}`,\n                  type: 'guardrail'\n                });\n              }\n              // Handle generic GUARDRAIL configuration (fallback)\n              if (configItem.configKey === 'GUARDRAIL' && configItem.configValue && configItem.configValue !== 'false') {\n                console.log('✅ Found generic GUARDRAIL config:', configItem.configValue);\n                this.blueprintGuardrailNodes.push({\n                  id: `guardrail-${nodeCounter++}`,\n                  name: `Guardrail: ${configItem.configValue}`,\n                  type: 'guardrail'\n                });\n              }\n            });\n          }\n        });\n      }\n      console.log('🎯 Final blueprint nodes:', {\n        promptNodes: this.blueprintPromptNodes,\n        modelNodes: this.blueprintModelNodes,\n        knowledgeNodes: this.blueprintKnowledgeNodes,\n        guardrailNodes: this.blueprintGuardrailNodes\n      });\n      // Calculate completion percentage\n      const totalRequired = 2; // Prompt + Model are required\n      const currentRequired = this.blueprintPromptNodes.length + this.blueprintModelNodes.length;\n      this.blueprintCompletionPercentage = Math.round(currentRequired / totalRequired * 100);\n    }\n    mapCollaborativeAgentToBlueprint(agentData, nodeCounter) {\n      // Clear existing blueprint nodes\n      this.blueprintPromptNodes = [];\n      this.blueprintModelNodes = [];\n      this.blueprintKnowledgeNodes = [];\n      this.blueprintToolNodes = [];\n      // Add prompt node from workflow\n      if (agentData.workflow && agentData.workflow.length > 0) {\n        const workflowItem = agentData.workflow[0]; // Usually first item has the prompt\n        if (workflowItem.goal) {\n          this.blueprintPromptNodes.push({\n            id: `prompt-${nodeCounter++}`,\n            name: workflowItem.goal || 'Collaborative Goal',\n            type: 'prompt'\n          });\n        }\n      }\n      // Add model node\n      if (agentData.model || agentData.modelName) {\n        this.blueprintModelNodes.push({\n          id: `model-${nodeCounter++}`,\n          name: agentData.model || agentData.modelName || 'Default Model',\n          type: 'model'\n        });\n      }\n      // Add tool nodes from workflow\n      if (agentData.workflow && agentData.workflow.length > 0) {\n        agentData.workflow.forEach(workflowItem => {\n          if (workflowItem.tools && workflowItem.tools.length > 0) {\n            workflowItem.tools.forEach(tool => {\n              this.blueprintToolNodes.push({\n                id: `tool-${nodeCounter++}`,\n                name: tool.name || tool.toolName || 'Tool',\n                type: 'tool'\n              });\n            });\n          }\n        });\n      }\n      // Add knowledge base nodes\n      if (agentData.knowledgeBase && agentData.knowledgeBase.length > 0) {\n        agentData.knowledgeBase.forEach(kb => {\n          this.blueprintKnowledgeNodes.push({\n            id: `knowledge-${nodeCounter++}`,\n            name: kb.name || kb.documentName || 'Knowledge Base',\n            type: 'knowledge'\n          });\n        });\n      }\n      // Calculate completion percentage\n      const totalRequired = 2; // Prompt + Model are required\n      const currentRequired = this.blueprintPromptNodes.length + this.blueprintModelNodes.length;\n      this.blueprintCompletionPercentage = Math.round(currentRequired / totalRequired * 100);\n    }\n    static ɵfac = function AgentExecutionComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AgentExecutionComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AgentServiceService), i0.ɵɵdirectiveInject(i3.AgentPlaygroundService), i0.ɵɵdirectiveInject(i4.TokenStorageService), i0.ɵɵdirectiveInject(i5.LoaderService), i0.ɵɵdirectiveInject(i6.FormBuilder), i0.ɵɵdirectiveInject(i7.ToolExecutionService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AgentExecutionComponent,\n      selectors: [[\"app-agent-execution\"]],\n      viewQuery: function AgentExecutionComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(AgentExecutionPlaygroundComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.playgroundComp = _t.first);\n        }\n      },\n      decls: 24,\n      vars: 12,\n      consts: [[\"noIndividualOutput\", \"\"], [\"noCollaborativeOutput\", \"\"], [1, \"agent-execution-container\"], [1, \"top-nav-bar\"], [1, \"nav-left\"], [\"type\", \"button\", 1, \"back-button\", 3, \"click\"], [\"iconName\", \"ArrowLeft\", \"iconSize\", \"16\", \"iconColor\", \"#374151\"], [1, \"agent-name\"], [1, \"main-content\"], [1, \"left-panel\"], [1, \"panel-header\"], [\"type\", \"button\", 1, \"collapse-btn\", 3, \"click\"], [\"iconSize\", \"16\", \"iconColor\", \"#6B7280\", 3, \"iconName\"], [\"class\", \"history-btn\", \"type\", \"button\", \"disabled\", \"\", 4, \"ngIf\"], [\"class\", \"panel-content\", 4, \"ngIf\"], [1, \"right-panel\"], [1, \"tabs-container\"], [1, \"tab-btn\", 3, \"click\"], [1, \"panel-content\"], [\"class\", \"blueprint-content\", 4, \"ngIf\"], [\"class\", \"output-content\", 4, \"ngIf\"], [\"type\", \"button\", \"disabled\", \"\", 1, \"history-btn\"], [3, \"promptChange\", \"messageSent\", \"conversationalToggle\", \"templateToggle\", \"filesSelected\", \"approvalRequested\", \"messages\", \"isLoading\", \"agentType\", \"showChatInteractionToggles\", \"showAiPrincipleToggle\", \"showApprovalButton\", \"showDropdown\", \"showAgentNameInput\", \"showFileUploadButton\", \"displayedAgentName\", \"agentNamePlaceholder\", \"acceptedFileType\"], [1, \"blueprint-content\"], [1, \"blueprint-header\"], [1, \"custom-blueprint-container\"], [\"viewBox\", \"0 0 100 100\", \"preserveAspectRatio\", \"none\", 1, \"connection-lines\"], [\"x1\", \"25\", \"y1\", \"25\", \"x2\", \"50\", \"y2\", \"50\", \"stroke\", \"#BBBEC5\", \"stroke-width\", \"0.3\"], [\"x1\", \"75\", \"y1\", \"25\", \"x2\", \"50\", \"y2\", \"50\", \"stroke\", \"#BBBEC5\", \"stroke-width\", \"0.3\"], [\"x1\", \"25\", \"y1\", \"75\", \"x2\", \"50\", \"y2\", \"50\", \"stroke\", \"#BBBEC5\", \"stroke-width\", \"0.3\"], [\"x1\", \"75\", \"y1\", \"75\", \"x2\", \"50\", \"y2\", \"50\", \"stroke\", \"#BBBEC5\", \"stroke-width\", \"0.3\"], [1, \"central-progress\"], [1, \"progress-ring\"], [\"width\", \"120\", \"height\", \"120\", 1, \"progress-circle\"], [\"id\", \"progressGradient\", \"x1\", \"0%\", \"y1\", \"0%\", \"x2\", \"100%\", \"y2\", \"100%\"], [\"offset\", \"33.91%\", 2, \"stop-color\", \"#0084ff\"], [\"offset\", \"100%\", 2, \"stop-color\", \"#03bdd4\"], [\"cx\", \"60\", \"cy\", \"60\", \"r\", \"50\", \"fill\", \"none\", \"stroke\", \"#e5e7eb\", \"stroke-width\", \"8\", 1, \"progress-background\"], [\"cx\", \"60\", \"cy\", \"60\", \"r\", \"50\", \"fill\", \"none\", \"stroke\", \"url(#progressGradient)\", \"stroke-width\", \"8\", \"stroke-linecap\", \"round\", \"transform\", \"rotate(180 60 60)\", 1, \"progress-bar\"], [1, \"progress-content\"], [1, \"progress-percentage\"], [1, \"progress-label\"], [\"id\", \"parent-box\"], [1, \"blueprint-zone\", \"north-zone\", \"prompts-zone\"], [1, \"zone-header\", 3, \"click\"], [1, \"header-content\"], [1, \"header-icon\"], [\"width\", \"45\", \"height\", \"44\", \"viewBox\", \"0 0 45 44\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"x\", \"0.679688\", \"width\", \"44\", \"height\", \"44\", \"rx\", \"8\", \"fill\", \"#5082EF\"], [\"d\", \"M20.6166 25.5C20.5273 25.1539 20.347 24.8381 20.0942 24.5854C19.8415 24.3327 19.5257 24.1523 19.1796 24.063L13.0446 22.481C12.94 22.4513 12.8478 22.3883 12.7822 22.3014C12.7166 22.2146 12.6812 22.1088 12.6812 22C12.6812 21.8912 12.7166 21.7854 12.7822 21.6986C12.8478 21.6118 12.94 21.5487 13.0446 21.519L19.1796 19.936C19.5256 19.8468 19.8413 19.6666 20.094 19.414C20.3467 19.1615 20.5272 18.8459 20.6166 18.5L22.1986 12.365C22.228 12.2599 22.291 12.1673 22.3779 12.1014C22.4649 12.0355 22.571 11.9998 22.6801 11.9998C22.7892 11.9998 22.8954 12.0355 22.9823 12.1014C23.0692 12.1673 23.1322 12.2599 23.1616 12.365L24.7426 18.5C24.8319 18.8461 25.0123 19.1619 25.265 19.4146C25.5177 19.6673 25.8336 19.8477 26.1796 19.937L32.3146 21.518C32.4201 21.5471 32.5132 21.61 32.5795 21.6971C32.6458 21.7841 32.6817 21.8906 32.6817 22C32.6817 22.1094 32.6458 22.2159 32.5795 22.3029C32.5132 22.39 32.4201 22.4529 32.3146 22.482L26.1796 24.063C25.8336 24.1523 25.5177 24.3327 25.265 24.5854C25.0123 24.8381 24.8319 25.1539 24.7426 25.5L23.1606 31.635C23.1312 31.7401 23.0682 31.8327 22.9813 31.8986C22.8944 31.9646 22.7882 32.0003 22.6791 32.0003C22.57 32.0003 22.4639 31.9646 22.3769 31.8986C22.29 31.8327 22.227 31.7401 22.1976 31.635L20.6166 25.5Z\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"zone-title\"], [1, \"header-actions\"], [1, \"required-badge\"], [\"type\", \"button\", 1, \"accordion-toggle\"], [\"width\", \"16\", \"height\", \"16\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\"], [\"d\", \"M6 9L12 15L18 9\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"class\", \"zone-content\", 4, \"ngIf\"], [1, \"blueprint-zone\", \"west-zone\", \"knowledge-zone\"], [\"x\", \"0.679688\", \"width\", \"44\", \"height\", \"44\", \"rx\", \"8\", \"fill\", \"#308666\"], [\"d\", \"M22.6797 17V31\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M26.6797 22H28.6797\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M26.6797 18H28.6797\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M13.6797 28C13.4145 28 13.1601 27.8946 12.9726 27.7071C12.785 27.5196 12.6797 27.2652 12.6797 27V14C12.6797 13.7348 12.785 13.4804 12.9726 13.2929C13.1601 13.1054 13.4145 13 13.6797 13H18.6797C19.7406 13 20.758 13.4214 21.5081 14.1716C22.2583 14.9217 22.6797 15.9391 22.6797 17C22.6797 15.9391 23.1011 14.9217 23.8513 14.1716C24.6014 13.4214 25.6188 13 26.6797 13H31.6797C31.9449 13 32.1993 13.1054 32.3868 13.2929C32.5743 13.4804 32.6797 13.7348 32.6797 14V27C32.6797 27.2652 32.5743 27.5196 32.3868 27.7071C32.1993 27.8946 31.9449 28 31.6797 28H25.6797C24.884 28 24.121 28.3161 23.5584 28.8787C22.9958 29.4413 22.6797 30.2044 22.6797 31C22.6797 30.2044 22.3636 29.4413 21.801 28.8787C21.2384 28.3161 20.4753 28 19.6797 28H13.6797Z\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M16.6797 22H18.6797\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M16.6797 18H18.6797\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"optional-badge\"], [1, \"blueprint-zone\", \"east-zone\", \"models-zone\"], [\"x\", \"0.679688\", \"width\", \"44\", \"height\", \"44\", \"rx\", \"8\", \"fill\", \"#997BCF\"], [\"d\", \"M31.6797 18C31.6793 17.6493 31.5867 17.3048 31.4112 17.0012C31.2357 16.6975 30.9834 16.4454 30.6797 16.27L23.6797 12.27C23.3756 12.0945 23.0308 12.0021 22.6797 12.0021C22.3286 12.0021 21.9837 12.0945 21.6797 12.27L14.6797 16.27C14.376 16.4454 14.1237 16.6975 13.9481 17.0012C13.7726 17.3048 13.68 17.6493 13.6797 18V26C13.68 26.3508 13.7726 26.6952 13.9481 26.9989C14.1237 27.3025 14.376 27.5547 14.6797 27.73L21.6797 31.73C21.9837 31.9056 22.3286 31.998 22.6797 31.998C23.0308 31.998 23.3756 31.9056 23.6797 31.73L30.6797 27.73C30.9834 27.5547 31.2357 27.3025 31.4112 26.9989C31.5867 26.6952 31.6793 26.3508 31.6797 26V18Z\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M13.9795 17L22.6795 22L31.3795 17\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M22.6797 32V22\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"blueprint-zone\", \"south-zone\", \"guardrails-zone\"], [\"x\", \"0.679688\", \"width\", \"44\", \"height\", \"44\", \"rx\", \"8\", \"fill\", \"#DC2626\"], [\"d\", \"M30.6797 23C30.6797 28 27.1797 30.5 23.0197 31.95C22.8019 32.0238 22.5652 32.0202 22.3497 31.94C18.1797 30.5 14.6797 28 14.6797 23V16C14.6797 15.7347 14.785 15.4804 14.9726 15.2929C15.1601 15.1053 15.4145 15 15.6797 15C17.6797 15 20.1797 13.8 21.9197 12.28C22.1315 12.099 22.401 11.9995 22.6797 11.9995C22.9583 11.9995 23.2278 12.099 23.4397 12.28C25.1897 13.81 27.6797 15 29.6797 15C29.9449 15 30.1993 15.1053 30.3868 15.2929C30.5743 15.4804 30.6797 15.7347 30.6797 16V23Z\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"zone-content\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [1, \"nodes-list\"], [\"class\", \"kanban-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"empty-state\"], [1, \"kanban-card\"], [1, \"card-title\"], [1, \"output-content\"], [\"class\", \"individual-output\", 4, \"ngIf\"], [\"class\", \"collaborative-output\", 4, \"ngIf\"], [1, \"individual-output\"], [\"class\", \"output-box\", 4, \"ngIf\", \"ngIfElse\"], [1, \"output-box\"], [1, \"output-section\"], [1, \"output-text\"], [1, \"collaborative-output\"], [4, \"ngIf\", \"ngIfElse\"], [\"class\", \"output-box\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"task-description\", 4, \"ngIf\"], [\"class\", \"task-expected\", 4, \"ngIf\"], [1, \"task-description\"], [1, \"task-expected\"]],\n      template: function AgentExecutionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4)(3, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function AgentExecutionComponent_Template_button_click_3_listener() {\n            return ctx.navigateBack();\n          });\n          i0.ɵɵelement(4, \"ava-icon\", 6);\n          i0.ɵɵelementStart(5, \"span\", 7);\n          i0.ɵɵtext(6);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(7, \"div\", 8)(8, \"div\", 9)(9, \"div\", 10)(10, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function AgentExecutionComponent_Template_button_click_10_listener() {\n            return ctx.toggleLeftPanel();\n          });\n          i0.ɵɵelement(11, \"ava-icon\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(12, AgentExecutionComponent_button_12_Template, 2, 0, \"button\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(13, AgentExecutionComponent_div_13_Template, 2, 12, \"div\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"div\", 15)(15, \"div\", 10)(16, \"div\", 16)(17, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function AgentExecutionComponent_Template_button_click_17_listener() {\n            return ctx.setActiveRightTab(\"blueprint\");\n          });\n          i0.ɵɵtext(18, \" Blueprint \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function AgentExecutionComponent_Template_button_click_19_listener() {\n            return ctx.setActiveRightTab(\"output\");\n          });\n          i0.ɵɵtext(20, \" Agent Output \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"div\", 18);\n          i0.ɵɵtemplate(22, AgentExecutionComponent_div_22_Template, 96, 25, \"div\", 19)(23, AgentExecutionComponent_div_23_Template, 3, 2, \"div\", 20);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.agentName);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"collapsed\", ctx.isLeftPanelCollapsed);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"iconName\", ctx.isLeftPanelCollapsed ? \"ChevronRight\" : \"PanelLeft\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLeftPanelCollapsed);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLeftPanelCollapsed);\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"active\", ctx.activeRightTab === \"blueprint\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"active\", ctx.activeRightTab === \"output\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.activeRightTab === \"blueprint\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.activeRightTab === \"output\");\n        }\n      },\n      dependencies: [CommonModule, i8.NgForOf, i8.NgIf, FormsModule, AgentExecutionPlaygroundComponent, IconComponent],\n      styles: [\".agent-execution-container[_ngcontent-%COMP%] {\\n  height: calc(100vh - 84px);\\n  display: flex;\\n  flex-direction: column;\\n  color: var(--color-text-primary);\\n  overflow: hidden;\\n}\\n\\n.top-nav-bar[_ngcontent-%COMP%] {\\n  padding: 16px 24px;\\n  padding-bottom: 0px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  min-height: 64px;\\n}\\n\\n.nav-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.back-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 8px 12px;\\n  border-radius: 6px;\\n  transition: background-color 0.2s ease;\\n}\\n.back-button[_ngcontent-%COMP%]:hover {\\n  background: var(--color-background-tertiary);\\n}\\n.back-button[_ngcontent-%COMP%]   .agent-name[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #000000;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  gap: 16px;\\n  padding: 16px;\\n  padding-top: 0px;\\n  height: calc(100vh - 96px);\\n  overflow: hidden;\\n}\\n\\n.left-panel[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 400px;\\n  max-width: 600px;\\n  background: white;\\n  border-radius: 12px;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n  transition: all 0.3s ease;\\n}\\n.left-panel.collapsed[_ngcontent-%COMP%] {\\n  flex: 0 0 48px;\\n  min-width: 48px;\\n  max-width: 48px;\\n}\\n\\n.right-panel[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 400px;\\n  background: white;\\n  border-radius: 12px;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n\\n.panel-header[_ngcontent-%COMP%] {\\n  padding: 16px 20px;\\n  background: #e9effd;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  max-height: 45px;\\n  border-radius: 12px 12px 0 0;\\n}\\n\\n.collapse-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 6px;\\n  border-radius: 4px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: background-color 0.2s ease;\\n  color: #1a46a7;\\n}\\n.collapse-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--color-background-quaternary);\\n}\\n\\n.history-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 8px 12px;\\n  border-radius: 6px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #1a46a7;\\n  transition: all 0.2s ease;\\n}\\n.history-btn[_ngcontent-%COMP%]:disabled {\\n  cursor: not-allowed;\\n}\\n.history-btn[_ngcontent-%COMP%]:hover {\\n  color: var(--color-text-primary);\\n  background: var(--color-background-quaternary);\\n}\\n\\n.tabs-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  padding: 4px;\\n}\\n\\n.tab-btn[_ngcontent-%COMP%] {\\n  padding: 8px 20px;\\n  border: none;\\n  background: transparent;\\n  color: var(--text-secondary);\\n  border-radius: 10px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  white-space: nowrap;\\n  color: #1a46a7;\\n}\\n.tab-btn.active[_ngcontent-%COMP%] {\\n  background: white;\\n  color: var(--nav-pill-selected-color);\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n  font-weight: 600;\\n}\\n\\n.panel-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.mock-content[_ngcontent-%COMP%] {\\n  padding: 24px;\\n  height: 100%;\\n  overflow-y: auto;\\n}\\n.mock-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: var(--color-text-primary);\\n}\\n.mock-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 24px 0;\\n  color: var(--color-text-secondary);\\n  font-size: 14px;\\n  line-height: 1.5;\\n}\\n\\n.blueprint-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n}\\n\\n.blueprint-header[_ngcontent-%COMP%] {\\n  padding: 10px;\\n}\\n.blueprint-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: black;\\n  margin: 0 0 5px 0;\\n  font-size: 18px;\\n  font-weight: 600;\\n  text-align: center;\\n}\\n.blueprint-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--color-text-secondary);\\n  margin: 0;\\n  font-size: 14px;\\n}\\n\\n.blueprint-canvas-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow: auto;\\n  padding: 10px;\\n  background: var(--color-background-primary);\\n}\\n\\n.custom-blueprint-container[_ngcontent-%COMP%] {\\n  height: 100%;\\n  width: 100%;\\n  min-height: 500px;\\n  background-color: #f8f9fa;\\n  background-image: radial-gradient(circle, #d1d5db 1px, transparent 1px);\\n  background-size: 20px 20px;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  border: 1px solid #d1d3d8;\\n  position: relative;\\n  padding: 5%;\\n  border-radius: 10px;\\n}\\n\\n.central-progress[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  z-index: 10;\\n  background-color: white;\\n  border-radius: 50%;\\n  padding: 8px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-ring[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-circle[_ngcontent-%COMP%] {\\n  transform: rotate(-90deg);\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-background[_ngcontent-%COMP%] {\\n  opacity: 0.15;\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  transition: stroke-dashoffset 0.3s ease;\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-content[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  text-align: center;\\n  color: #374151;\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-percentage[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  line-height: 1;\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-label[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #6b7280;\\n  margin-top: 2px;\\n}\\n\\n#parent-box[_ngcontent-%COMP%] {\\n  height: 100%;\\n  width: 100%;\\n  gap: 2rem;\\n  position: relative;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.blueprint-zones-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 80%;\\n  height: 80%;\\n  max-width: 800px;\\n  max-height: 600px;\\n}\\n\\n#box1[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: space-between;\\n  height: 300px;\\n  position: relative;\\n  border: 1px solid #bbbec5;\\n  position: fixed;\\n  top: 32%;\\n  left: 40%;\\n  border-left: none;\\n}\\n\\n#box2[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: space-between;\\n  height: 300px;\\n  position: relative;\\n  border: 1px solid #bbbec5;\\n  position: fixed;\\n  top: 32%;\\n  left: 60%;\\n  border-right: none;\\n}\\n\\n.blueprint-zone[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  padding: 16px;\\n  transition: all 0.3s ease;\\n  position: absolute;\\n  width: 280px;\\n  z-index: 5;\\n}\\n.blueprint-zone.has-nodes[_ngcontent-%COMP%] {\\n  border-style: solid;\\n}\\n.blueprint-zone.collapsed[_ngcontent-%COMP%] {\\n  min-height: 40px;\\n}\\n.blueprint-zone.collapsed[_ngcontent-%COMP%]   .zone-header[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n\\n.north-zone[_ngcontent-%COMP%] {\\n  background-color: #ecf0fa;\\n  color: #005eb5;\\n  border: 2px solid #9ab7f6;\\n  top: 0;\\n  left: 0;\\n  overflow: hidden;\\n}\\n\\n.east-zone[_ngcontent-%COMP%] {\\n  background-color: #f2ebfd;\\n  border: 2px solid #d6c2f9;\\n  color: #d6c2f9;\\n  top: 0;\\n  right: 0;\\n  overflow: hidden;\\n}\\n\\n.south-zone[_ngcontent-%COMP%] {\\n  background-color: #fbf6f7;\\n  border: 2px solid #fecaca;\\n  color: #dc2626 !important;\\n  bottom: 0;\\n  right: 0;\\n  overflow: hidden;\\n}\\n.south-zone.has-nodes[_ngcontent-%COMP%] {\\n  height: auto;\\n  min-height: 100px;\\n  max-height: 200px;\\n}\\n.south-zone.has-nodes[_ngcontent-%COMP%]:hover {\\n  overflow-y: auto;\\n  scrollbar-width: none !important;\\n}\\n\\n.west-zone[_ngcontent-%COMP%] {\\n  background-color: #ecf8f4;\\n  border: 2px solid #a9e1cc;\\n  color: #25684f;\\n  bottom: 0;\\n  left: 0;\\n  overflow: hidden;\\n}\\n.west-zone.has-nodes[_ngcontent-%COMP%] {\\n  height: auto;\\n  min-height: 100px;\\n  max-height: 200px;\\n}\\n.west-zone.has-nodes[_ngcontent-%COMP%]:hover {\\n  overflow-y: auto;\\n  scrollbar-width: none !important;\\n}\\n\\n.connection-lines[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  pointer-events: none;\\n  z-index: 1;\\n}\\n.connection-lines[_ngcontent-%COMP%]   line[_ngcontent-%COMP%] {\\n  opacity: 0.7;\\n  transition: opacity 0.3s ease;\\n}\\n.connection-lines[_ngcontent-%COMP%]   line[_ngcontent-%COMP%]:hover {\\n  opacity: 1;\\n}\\n\\n.central-progress[_ngcontent-%COMP%] {\\n  z-index: 10;\\n}\\n\\n.blueprint-zone[_ngcontent-%COMP%] {\\n  z-index: 5;\\n}\\n\\n.zone-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin-bottom: 16px;\\n  padding-bottom: 12px;\\n  cursor: pointer;\\n  -webkit-user-select: none;\\n          user-select: none;\\n  transition: all 0.2s ease;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 8px;\\n  background-color: rgba(59, 130, 246, 0.1);\\n  color: #3b82f6;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .zone-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #374151;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .accordion-toggle[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 4px;\\n  border-radius: 4px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.2s ease;\\n  color: #6b7280;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .accordion-toggle[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 0, 0, 0.05);\\n  color: #374151;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .accordion-toggle[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  transition: transform 0.3s ease;\\n}\\n\\n.required-badge[_ngcontent-%COMP%] {\\n  background-color: var(--status-error);\\n  color: white;\\n  font-size: 10px;\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n  font-weight: 500;\\n}\\n\\n.optional-badge[_ngcontent-%COMP%] {\\n  background-color: var(--status-warning);\\n  color: white;\\n  font-size: 10px;\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n  font-weight: 500;\\n}\\n\\n.zone-content[_ngcontent-%COMP%] {\\n  min-height: 60px;\\n  flex: 1;\\n  transition: all 0.3s ease-in-out;\\n  opacity: 1;\\n  overflow: hidden;\\n}\\n\\n.empty-state[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: var(--text-secondary);\\n  font-size: 12px;\\n  padding: 20px 0;\\n  font-style: italic;\\n}\\n\\n.nodes-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n}\\n\\n.kanban-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  padding: 12px 16px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n  transition: all 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n}\\n.kanban-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\\n  transform: translateY(-1px);\\n}\\n.kanban-card[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #374151;\\n  flex: 1;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n.blueprint-placeholder[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n\\n.blueprint-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 16px;\\n  background: var(--color-background-primary);\\n  border: 1px solid var(--color-border-primary);\\n  border-radius: 8px;\\n  transition: all 0.2s ease;\\n}\\n.blueprint-item[_ngcontent-%COMP%]:hover {\\n  border-color: var(--color-border-secondary);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.blueprint-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  width: 32px;\\n  height: 32px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.blueprint-text[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: var(--color-text-primary);\\n}\\n\\n.output-placeholder[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 24px;\\n}\\n\\n.output-section[_ngcontent-%COMP%] {\\n  background: var(--color-background-primary);\\n  border: 1px solid var(--color-border-primary);\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n.output-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 16px 16px 8px 16px;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: var(--color-text-primary);\\n}\\n.output-section[_ngcontent-%COMP%]   .output-meta[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 0 16px 16px 16px;\\n  font-size: 12px;\\n  color: var(--color-text-tertiary);\\n}\\n\\n.output-preview[_ngcontent-%COMP%] {\\n  border-top: 1px solid var(--color-border-primary);\\n}\\n.output-preview[_ngcontent-%COMP%]   .code-block[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  background: var(--color-background-secondary);\\n  font-family: \\\"Monaco\\\", \\\"Menlo\\\", \\\"Ubuntu Mono\\\", monospace;\\n  font-size: 12px;\\n  line-height: 1.6;\\n  color: var(--color-text-primary);\\n  border-bottom: 1px solid var(--color-border-primary);\\n}\\n.output-preview[_ngcontent-%COMP%]   .preview-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 12px 16px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: var(--color-brand-primary);\\n  transition: all 0.2s ease;\\n  width: 100%;\\n  text-align: left;\\n}\\n.output-preview[_ngcontent-%COMP%]   .preview-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--color-background-tertiary);\\n}\\n\\n@media (max-width: 768px) {\\n  .execution-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .playground-column[_ngcontent-%COMP%], \\n   .output-column[_ngcontent-%COMP%] {\\n    flex: none;\\n    height: 50%;\\n  }\\n  .playground-column[_ngcontent-%COMP%] {\\n    border-right: none;\\n    border-bottom: 1px solid var(--color-border-primary);\\n  }\\n}\\n.row[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  margin: 0;\\n}\\n\\n.col-7[_ngcontent-%COMP%] {\\n  flex: 0 0 58.333333%;\\n  max-width: 58.333333%;\\n}\\n\\n.col-5[_ngcontent-%COMP%] {\\n  flex: 0 0 41.666667%;\\n  max-width: 41.666667%;\\n}\\n\\n*[_ngcontent-%COMP%] {\\n  box-sizing: border-box;\\n}\\n\\n[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n  height: 6px;\\n}\\n\\n[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: var(--color-background-secondary);\\n}\\n\\n[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: var(--color-border-primary);\\n  border-radius: 3px;\\n}\\n\\n[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: var(--color-border-secondary);\\n}\\n\\n.activity-placeholder[_ngcontent-%COMP%], \\n.output-placeholder[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n.activity-placeholder[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], \\n.output-placeholder[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: var(--color-text-primary);\\n}\\n.activity-placeholder[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], \\n.output-placeholder[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: var(--color-text-secondary);\\n  font-size: 14px;\\n}\\n\\n.activity-list[_ngcontent-%COMP%], \\n.output-list[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  border: 1px solid var(--color-border-primary);\\n  border-radius: 8px;\\n  padding: 16px;\\n  background: var(--color-background-secondary);\\n}\\n\\n.activity-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n  padding: 12px 0;\\n  border-bottom: 1px solid var(--color-border-primary);\\n}\\n.activity-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.activity-item[_ngcontent-%COMP%]   .timestamp[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: var(--color-text-tertiary);\\n  font-weight: 500;\\n}\\n.activity-item[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--color-text-primary);\\n}\\n\\n.output-item[_ngcontent-%COMP%] {\\n  padding: 12px 0;\\n  border-bottom: 1px solid var(--color-border-primary);\\n}\\n.output-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.output-item[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--color-text-primary);\\n  line-height: 1.5;\\n}\\n\\n.individual-output[_ngcontent-%COMP%], \\n.collaborative-output[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n.individual-output[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], \\n.collaborative-output[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: var(--color-text-primary);\\n}\\n.individual-output[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], \\n.collaborative-output[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--color-text-secondary);\\n  font-size: 14px;\\n  margin: 0;\\n}\\n\\n.output-box[_ngcontent-%COMP%] {\\n  background: var(--color-background-secondary);\\n  border: 1px solid var(--color-border-primary);\\n  border-radius: 8px;\\n  margin-bottom: 16px;\\n}\\n.output-box[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n\\n.output-section[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n.output-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 12px 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: var(--color-text-primary);\\n}\\n.output-section[_ngcontent-%COMP%]   .task-description[_ngcontent-%COMP%], \\n.output-section[_ngcontent-%COMP%]   .task-expected[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n  font-size: 14px;\\n  color: var(--color-text-secondary);\\n}\\n.output-section[_ngcontent-%COMP%]   .task-description[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%], \\n.output-section[_ngcontent-%COMP%]   .task-expected[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: var(--color-text-primary);\\n}\\n\\n.output-text[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  line-height: 1.6;\\n  color: var(--color-text-primary);\\n  white-space: pre-wrap;\\n  word-wrap: break-word;\\n  background: var(--color-background-primary);\\n  padding: 12px;\\n  border-radius: 6px;\\n  border: 1px solid var(--color-border-secondary);\\n  max-height: 400px;\\n  overflow-y: auto;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return AgentExecutionComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "Subject", "takeUntil", "switchMap", "finalize", "catchError", "of", "FormsModule", "AgentExecutionPlaygroundComponent", "IconComponent", "environment", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "AgentExecutionComponent_div_13_Template_app_agent_execution_playground_promptChange_1_listener", "$event", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onPromptChanged", "AgentExecutionComponent_div_13_Template_app_agent_execution_playground_messageSent_1_listener", "handleChatMessage", "AgentExecutionComponent_div_13_Template_app_agent_execution_playground_conversationalToggle_1_listener", "onPlaygroundConversationalToggle", "AgentExecutionComponent_div_13_Template_app_agent_execution_playground_templateToggle_1_listener", "onPlaygroundTemplateToggle", "AgentExecutionComponent_div_13_Template_app_agent_execution_playground_filesSelected_1_listener", "onFilesSelected", "AgentExecutionComponent_div_13_Template_app_agent_execution_playground_approvalRequested_1_listener", "onApprovalRequested", "ɵɵadvance", "ɵɵproperty", "chatMessages", "isProcessingChat", "agentType", "<PERSON><PERSON><PERSON>", "fileType", "ɵɵtextInterpolate", "node_r4", "name", "ɵɵtemplate", "AgentExecutionComponent_div_22_div_40_div_1_Template", "AgentExecutionComponent_div_22_div_40_div_3_Template", "blueprintPromptNodes", "length", "node_r5", "AgentExecutionComponent_div_22_div_61_div_1_Template", "AgentExecutionComponent_div_22_div_61_div_3_Template", "blueprintKnowledgeNodes", "node_r6", "AgentExecutionComponent_div_22_div_79_div_1_Template", "AgentExecutionComponent_div_22_div_79_div_3_Template", "blueprintModelNodes", "node_r7", "AgentExecutionComponent_div_22_div_95_div_1_Template", "AgentExecutionComponent_div_22_div_95_div_3_Template", "blueprintGuardrailNodes", "ɵɵelement", "AgentExecutionComponent_div_22_Template_div_click_26_listener", "_r3", "toggleBlueprintZone", "AgentExecutionComponent_div_22_div_40_Template", "AgentExecutionComponent_div_22_Template_div_click_42_listener", "AgentExecutionComponent_div_22_div_61_Template", "AgentExecutionComponent_div_22_Template_div_click_63_listener", "AgentExecutionComponent_div_22_div_79_Template", "AgentExecutionComponent_div_22_Template_div_click_81_listener", "AgentExecutionComponent_div_22_div_95_Template", "ɵɵstyleProp", "blueprintCompletionPercentage", "ɵɵtextInterpolate1", "ɵɵclassProp", "isBlueprintZoneExpanded", "latestAgentResponse", "response", "choices", "text", "AgentExecutionComponent_div_23_div_1_div_3_Template", "AgentExecutionComponent_div_23_div_1_ng_template_4_Template", "ɵɵtemplateRefExtractor", "noIndividualOutput_r8", "taskOutput_r9", "description", "expected_output", "AgentExecutionComponent_div_23_div_2_div_3_div_1_div_4_Template", "AgentExecutionComponent_div_23_div_2_div_3_div_1_div_5_Template", "summary", "i_r10", "raw", "AgentExecutionComponent_div_23_div_2_div_3_div_1_Template", "agentResponse", "agent", "tasksOutputs", "AgentExecutionComponent_div_23_div_2_div_3_Template", "AgentExecutionComponent_div_23_div_2_ng_template_4_Template", "noCollaborativeOutput_r11", "AgentExecutionComponent_div_23_div_1_Template", "AgentExecutionComponent_div_23_div_2_Template", "ExecutionStatus", "AgentExecutionComponent", "route", "router", "agentService", "agentPlaygroundService", "tokenStorage", "loaderService", "formBuilder", "toolExecutionService", "navigationTabs", "id", "label", "disabled", "agentId", "agentDetail", "playgroundComp", "activityLogs", "activityProgress", "executionDetails", "isRunning", "status", "notStarted", "inputText", "agentOutputs", "agentForm", "executionStartTime", "executionCompleted", "executionId", "enableStreamingLog", "enableLogStreaming", "isExecutionComplete", "progressInterval", "destroy$", "selectedTab", "demoTabs", "errorMsg", "resMessage", "taskMessage", "isJsonValid", "disable<PERSON>hat", "selectedFiles", "agentNodes", "userInputList", "progress", "isLoading", "loaderColor", "inputFieldOrder", "currentInputIndex", "activeTabId", "isLeftPanelCollapsed", "activeRightTab", "currentAgentDetails", "buildAgentNodes", "canvasNodes", "canvasEdges", "selected<PERSON><PERSON><PERSON>", "selectedAgentMode", "selectedUseCaseIdentifier", "agentFilesUploadedData", "agentAttachment", "isAgentPlaygroundLoading", "agentPlaygroundDestroy", "agentChatPayload", "agentCode", "promptOptions", "blueprintToolNodes", "blueprintZonesExpanded", "prompt", "model", "knowledge", "guardrail", "tool", "constructor", "group", "isConversational", "isUseTemplate", "ngOnInit", "crypto", "randomUUID", "params", "subscribe", "queryParams", "loadAgentData", "from", "ngOnDestroy", "next", "complete", "clearInterval", "onTabChange", "event", "getCollaborativeAgentDetailsById", "handleAgentDataResponse", "error", "console", "getAgentById", "agentData", "agentDetails", "Array", "isArray", "data", "useCaseName", "useCaseCode", "organizationPath", "loadAgentNodes", "mapAgentConfigurationToBlueprint", "message", "showAgentError", "displayMessage", "fileNames", "map", "file", "documentName", "join", "get", "value", "log", "agentMode", "useCaseIdentifier", "orgPath", "buildOrganizationPath", "agentIdentifier", "processAgentFilesAndSendMessage", "sendAgentMessageToAPI", "payload", "Number", "user", "getDaUsername", "userInputs", "question", "fileWrapper", "submitAgentExecuteWithFile", "pipe", "res", "handleAgentExecuteResponse", "err", "submitAgentExecute", "String", "setValue", "files", "saveLogs", "exportResults", "section", "handleControlAction", "action", "navigateBack", "navigate", "mode", "editAgent", "navigateToAgentsList", "toggleLeftPanel", "setActiveRightTab", "tab", "zoneType", "getMetadataFromNavbar", "outputRaw", "output", "formattedOutput", "replace", "detail", "formData", "FormData", "for<PERSON>ach", "fileData", "append", "has", "getFileToContent", "fileResponse", "fileContent", "fileResponses", "sendAgentMessageToAPIWithFiles", "currentChatPayloadLength", "push", "content", "role", "levelId", "generatePrompt", "generatedResponse", "aiResponseText", "warn", "errorMessage", "pop", "fileContents", "Object", "keys", "nodeCounter", "mapIndividualAgentToBlueprint", "mapCollaborativeAgentToBlueprint", "totalNodes", "config", "config<PERSON><PERSON><PERSON>", "promptTemplate", "type", "category", "categoryIndex", "configItem", "itemIndex", "config<PERSON><PERSON>", "config<PERSON><PERSON><PERSON>", "kbValue", "toString", "kbIds", "split", "trim", "filter", "kbId", "startsWith", "key", "guardrailName", "promptNodes", "modelNodes", "knowledgeNodes", "guardrailNodes", "totalRequired", "currentRequired", "Math", "round", "workflow", "workflowItem", "goal", "modelName", "tools", "toolName", "knowledgeBase", "kb", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "AgentServiceService", "i3", "AgentPlaygroundService", "i4", "TokenStorageService", "i5", "LoaderService", "i6", "FormBuilder", "i7", "ToolExecutionService", "selectors", "viewQuery", "AgentExecutionComponent_Query", "rf", "ctx", "AgentExecutionComponent_Template_button_click_3_listener", "AgentExecutionComponent_Template_button_click_10_listener", "AgentExecutionComponent_button_12_Template", "AgentExecutionComponent_div_13_Template", "AgentExecutionComponent_Template_button_click_17_listener", "AgentExecutionComponent_Template_button_click_19_listener", "AgentExecutionComponent_div_22_Template", "AgentExecutionComponent_div_23_Template", "i8", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\agents\\agent-execution\\agent-execution.component.ts", "C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\agents\\agent-execution\\agent-execution.component.html"], "sourcesContent": ["import { Component, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Subject, takeUntil, switchMap, finalize, catchError, of } from 'rxjs';\r\nimport {\r\n  FormBuilder,\r\n  FormGroup,\r\n  FormsModule,\r\n  Validators,\r\n} from '@angular/forms';\r\n \r\n// Import child components\r\nimport { AgentExecutionPlaygroundComponent } from './components/agent-execution-playground/agent-execution-playground.component';\r\nimport { ChatMessage } from '../../../shared/components/chat-window/chat-window.component';\r\nimport {\r\n  ButtonComponent,\r\n  IconComponent,\r\n  TabItem,\r\n  TabsComponent,\r\n  DropdownOption,\r\n} from '@ava/play-comp-library';\r\nimport { AgentServiceService } from '../services/agent-service.service';\r\nimport { AgentPlaygroundService } from '../build-agents/services/agent-playground.service';\r\nimport { environment } from 'projects/console/src/environments/environment';\r\nimport { TokenStorageService } from '@shared/auth/services/token-storage.service';\r\nimport { LoaderService } from '../../../shared/services/loader/loader.service';\r\nimport { ToolExecutionService } from '../../../shared/services/tool-execution/tool-execution.service';\r\n \r\nexport interface AvaTab {\r\n  id: string;\r\n  label: string;\r\n  content?: string;\r\n  iconName?: string;\r\n  subtitle?: string;\r\n  disabled?: boolean;\r\n  badge?: string | number;\r\n  closeable?: boolean;\r\n}\r\n \r\nexport enum ExecutionStatus {\r\n  notStarted = 'notStarted',\r\n  running = 'running',\r\n  completed = 'completed',\r\n}\r\n \r\nexport interface ActivityLog {\r\n  content: string;\r\n  timestamp: string;\r\n  message: string;\r\n  color: string;\r\n  type?: 'info' | 'success' | 'warning' | 'error';\r\n}\r\n \r\nexport interface ExecutionDetails {\r\n  startTime?: Date;\r\n  endTime?: Date;\r\n  duration?: string;\r\n  status?: ExecutionStatus;\r\n}\r\n \r\nexport interface OutputItem {\r\n  type: string;\r\n  content: any;\r\n  timestamp: string;\r\n}\r\n \r\n@Component({\r\n  selector: 'app-agent-execution',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    AgentExecutionPlaygroundComponent,\r\n    TabsComponent,\r\n    ButtonComponent,\r\n    IconComponent,\r\n  ],\r\n  templateUrl: './agent-execution.component.html',\r\n  styleUrls: ['./agent-execution.component.scss'],\r\n})\r\nexport class AgentExecutionComponent implements OnInit, OnDestroy {\r\n  navigationTabs: TabItem[] = [\r\n    { id: 'nav-home', label: 'Agent Activity' },\r\n    { id: 'nav-products', label: 'Agent Output' },\r\n    { id: 'nav-services', label: 'Preview', disabled: true },\r\n  ];\r\n \r\n  // Agent details\r\n  agentId: string | null = null;\r\n  agentType: string = 'individual';\r\n  agentName: string = 'Agent';\r\n  agentDetail: string = '';\r\n \r\n  @ViewChild(AgentExecutionPlaygroundComponent, { static: false })\r\n  playgroundComp!: AgentExecutionPlaygroundComponent;\r\n \r\n  // Activity logs\r\n  activityLogs: ActivityLog[] = [];\r\n  activityProgress: number = 0;\r\n  executionDetails?: ExecutionDetails;\r\n  isRunning: boolean = false;\r\n  status: ExecutionStatus = ExecutionStatus.notStarted;\r\n \r\n  // Chat messages\r\n  chatMessages: ChatMessage[] = [];\r\n  isProcessingChat: boolean = false;\r\n  inputText = '';\r\n \r\n  // Agent outputs\r\n  agentOutputs: OutputItem[] = [];\r\n  latestAgentResponse: any = null; // Store the latest agent response for display\r\n  public agentForm!: FormGroup;\r\n  public fileType: string =\r\n    '.pdf,.doc,.docx,.txt,.csv,.xlsx,.xls,.ppt,.pptx,.png,.jpg,.jpeg,.gif,.bmp,.svg';\r\n \r\n  // Execution state\r\n  executionStartTime: Date | null = null;\r\n  executionCompleted: boolean = false;\r\n  executionId!: string;\r\n \r\n  enableStreamingLog = environment.enableLogStreaming || 'all';\r\n \r\n  public isExecutionComplete: boolean = false;\r\n  progressInterval: any;\r\n \r\n  private destroy$ = new Subject<void>();\r\n  selectedTab: string = 'Agent Activity';\r\n  demoTabs: AvaTab[] = [\r\n    { id: 'tab1', label: 'History' },\r\n    { id: 'tab2', label: 'Blueprint' },\r\n    { id: 'tab3', label: 'Agent Output' },\r\n  ];\r\n \r\n  errorMsg = false;\r\n  resMessage: any;\r\n  taskMessage: any[] = [];\r\n  isJsonValid = false;\r\n  disableChat: boolean = false;\r\n  selectedFiles: File[] = [];\r\n  agentNodes: any[] = [];\r\n  userInputList: any[] = [];\r\n  progress = 0;\r\n  isLoading = false;\r\n  loaderColor: string = '';\r\n \r\n  inputFieldOrder: string[] = [];\r\n  currentInputIndex: number = 0;\r\n  activeTabId: string = 'nav-home';\r\n \r\n  // Panel state properties\r\n  isLeftPanelCollapsed: boolean = false;\r\n  activeRightTab: string = 'blueprint';\r\n \r\n  // Agent-specific properties\r\n  currentAgentDetails: any = null;\r\n  buildAgentNodes: any[] = [];\r\n  canvasNodes: any[] = [];\r\n  canvasEdges: any[] = [];\r\n  selectedPrompt: string = '';\r\n  selectedAgentMode: string = '';\r\n  selectedUseCaseIdentifier: string = '';\r\n  agentFilesUploadedData: any[] = [];\r\n  agentAttachment: string[] = [];\r\n  isAgentPlaygroundLoading = false;\r\n  agentPlaygroundDestroy = new Subject<boolean>();\r\n  agentChatPayload: any[] = [];\r\n  agentCode: string = '';\r\n  promptOptions: DropdownOption[] = [];\r\n \r\n  // Custom Blueprint Display Properties\r\n  blueprintCompletionPercentage: number = 0;\r\n  blueprintPromptNodes: any[] = [];\r\n  blueprintModelNodes: any[] = [];\r\n  blueprintKnowledgeNodes: any[] = [];\r\n  blueprintGuardrailNodes: any[] = [];\r\n  blueprintToolNodes: any[] = [];\r\n \r\n  // Blueprint zone expansion state\r\n  private blueprintZonesExpanded: { [key: string]: boolean } = {\r\n    prompt: true,\r\n    model: true,\r\n    knowledge: true,\r\n    guardrail: true,\r\n    tool: true,\r\n  };\r\n \r\n  // Blueprint panel properties (using existing arrays above)\r\n \r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private agentService: AgentServiceService,\r\n    private agentPlaygroundService: AgentPlaygroundService,\r\n    private tokenStorage: TokenStorageService,\r\n    private loaderService: LoaderService,\r\n    private formBuilder: FormBuilder,\r\n    private toolExecutionService: ToolExecutionService,\r\n  ) {\r\n    this.agentForm = this.formBuilder.group({\r\n      isConversational: [true],\r\n      isUseTemplate: [false],\r\n    });\r\n  }\r\n \r\n  ngOnInit(): void {\r\n    this.executionId = crypto.randomUUID();\r\n \r\n    this.route.params.subscribe((params) => {\r\n      this.agentType = params['type'] || 'individual';\r\n    });\r\n \r\n    this.route.queryParams.subscribe((params) => {\r\n      if (params['id']) {\r\n        this.agentId = params['id'];\r\n        this.loadAgentData(params['id']);\r\n      }\r\n    });\r\n \r\n    // Initialize chat messages\r\n    this.chatMessages = [\r\n      {\r\n        from: 'ai',\r\n        text: `Hi there! I am ${this.agentName || 'your agent'}. How can I help you today?`,\r\n      },\r\n    ];\r\n  }\r\n \r\n  ngOnDestroy(): void {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n    if (this.progressInterval) {\r\n      clearInterval(this.progressInterval);\r\n    }\r\n  }\r\n \r\n  onTabChange(event: { id: string; label: string }) {\r\n    this.activeTabId = event.id;\r\n    this.selectedTab = event.label;\r\n  }\r\n \r\n  loadAgentData(agentId: string): void {\r\n    this.isLoading = true;\r\n \r\n    // Load agent data based on type\r\n    if (this.agentType === 'collaborative') {\r\n      this.agentService.getCollaborativeAgentDetailsById(agentId).subscribe({\r\n        next: (response: any) => {\r\n          this.handleAgentDataResponse(response);\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error loading collaborative agent:', error);\r\n          this.isLoading = false;\r\n        },\r\n      });\r\n    } else {\r\n      this.agentService.getAgentById(agentId).subscribe({\r\n        next: (response: any) => {\r\n          this.handleAgentDataResponse(response);\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error loading individual agent:', error);\r\n          this.isLoading = false;\r\n        },\r\n      });\r\n    }\r\n  }\r\n \r\n  private handleAgentDataResponse(response: any): void {\r\n    this.isLoading = false;\r\n \r\n    // Extract agent details\r\n    let agentData;\r\n    if (\r\n      response.agentDetails &&\r\n      Array.isArray(response.agentDetails) &&\r\n      response.agentDetails.length > 0\r\n    ) {\r\n      agentData = response.agentDetails[0];\r\n    } else if (response.agentDetail) {\r\n      agentData = response.agentDetail;\r\n    } else if (response.data) {\r\n      agentData = response.data;\r\n    } else {\r\n      agentData = response;\r\n    }\r\n \r\n    if (agentData) {\r\n      this.currentAgentDetails = agentData;\r\n      this.agentName = agentData.name || agentData.agentName || 'Agent';\r\n      this.agentDetail = agentData.description || agentData.agentDetail || '';\r\n \r\n      // For individual agents, set up the required properties for playground functionality\r\n      if (this.agentType === 'individual') {\r\n        // Set selectedPrompt to agent name or useCaseName to bypass dropdown requirement\r\n        this.selectedPrompt = agentData.useCaseName || agentData.name || 'loaded-agent';\r\n \r\n        // Set selectedAgentMode for API calls - use useCaseCode if available\r\n        this.selectedAgentMode = agentData.useCaseCode || agentData.useCaseName || agentData.name || '';\r\n \r\n        // Set useCaseIdentifier - use organizationPath if available\r\n        if (agentData.organizationPath) {\r\n          this.selectedUseCaseIdentifier = agentData.organizationPath;\r\n        } else if (agentData.useCaseCode) {\r\n          this.selectedUseCaseIdentifier = agentData.useCaseCode;\r\n        } else if (agentData.useCaseName) {\r\n          this.selectedUseCaseIdentifier = agentData.useCaseName;\r\n        }\r\n      }\r\n \r\n      // Update chat message with agent name\r\n      if (this.chatMessages.length > 0) {\r\n        this.chatMessages[0].text = `Hi there! I am ${this.agentName}. How can I help you today?`;\r\n      }\r\n \r\n      // Load agent nodes and configuration\r\n      this.loadAgentNodes(agentData);\r\n    }\r\n  }\r\n \r\n  private loadAgentNodes(agentData: any): void {\r\n    // Map agent configuration to blueprint panel\r\n    this.mapAgentConfigurationToBlueprint(agentData);\r\n  }\r\n \r\n  handleChatMessage(message: string): void {\r\n    if (this.agentType === 'individual') {\r\n      // For individual agents, use the loaded agent details instead of requiring dropdown selection\r\n      if (!this.currentAgentDetails && (!this.selectedPrompt || this.selectedPrompt === 'default')) {\r\n        this.showAgentError(\r\n          'Agent details are not loaded. Please try refreshing the page.',\r\n        );\r\n        return;\r\n      }\r\n \r\n      let displayMessage = message;\r\n      if (this.agentFilesUploadedData.length > 0) {\r\n        const fileNames = this.agentFilesUploadedData\r\n          .map((file) => file.documentName)\r\n          .join(', ');\r\n        displayMessage = `${message}\\n\\n📎 Attached files: ${fileNames}`;\r\n      }\r\n \r\n      this.chatMessages = [\r\n        ...this.chatMessages,\r\n        { from: 'user', text: displayMessage },\r\n      ];\r\n      this.isProcessingChat = true;\r\n \r\n      const isConversational =\r\n        this.agentForm.get('isConversational')?.value || false;\r\n      const isUseTemplate = this.agentForm.get('isUseTemplate')?.value || false;\r\n \r\n      console.log('Chat message handling - isConversational:', isConversational, 'isUseTemplate:', isUseTemplate);\r\n \r\n      // Use agent details from the loaded agent data instead of dropdown selection\r\n      // Mode should be the useCaseCode, not useCaseName\r\n      const agentMode = this.agentCode ||\r\n                       this.selectedAgentMode ||\r\n                       this.currentAgentDetails?.useCaseCode ||\r\n                       this.currentAgentDetails?.useCaseName ||\r\n                       this.currentAgentDetails?.name ||\r\n                       this.selectedPrompt;\r\n \r\n      let useCaseIdentifier = this.selectedUseCaseIdentifier;\r\n      if (!useCaseIdentifier) {\r\n        // Use organizationPath if available, otherwise build from agent details\r\n        if (this.currentAgentDetails?.organizationPath) {\r\n          useCaseIdentifier = this.currentAgentDetails.organizationPath;\r\n        } else {\r\n          const orgPath = this.buildOrganizationPath();\r\n          const agentIdentifier = this.currentAgentDetails?.useCaseCode ||\r\n                                 this.currentAgentDetails?.useCaseName ||\r\n                                 this.currentAgentDetails?.name ||\r\n                                 agentMode;\r\n          useCaseIdentifier = `${agentIdentifier}${orgPath}`;\r\n        }\r\n      }\r\n \r\n      if (this.agentFilesUploadedData.length > 0) {\r\n        this.processAgentFilesAndSendMessage(\r\n          message,\r\n          agentMode,\r\n          useCaseIdentifier,\r\n          isConversational,\r\n          isUseTemplate,\r\n        );\r\n        return;\r\n      }\r\n \r\n      this.sendAgentMessageToAPI(\r\n        message,\r\n        agentMode,\r\n        useCaseIdentifier,\r\n        isConversational,\r\n        isUseTemplate,\r\n      );\r\n    } else if (this.agentType === 'collaborative') {\r\n      this.isProcessingChat = true;\r\n      let payload = {\r\n        executionId: this.executionId,\r\n        agentId: Number(this.agentId),\r\n        user: this.tokenStorage.getDaUsername() || '<EMAIL>',\r\n        userInputs: { question: message },\r\n      };\r\n \r\n      if (this.agentFilesUploadedData.length > 0) {\r\n        const fileWrapper = this.agentFilesUploadedData[0];\r\n        let displayMessage: string;\r\n        if (this.agentFilesUploadedData.length > 0) {\r\n          const fileNames = this.agentFilesUploadedData\r\n            .map((file) => file.documentName)\r\n            .join(', ');\r\n          displayMessage = `📎 Attached files: ${fileNames}`;\r\n \r\n          this.chatMessages = [{ from: 'user', text: displayMessage }];\r\n        }\r\n        this.agentPlaygroundService\r\n          .submitAgentExecuteWithFile(payload, fileWrapper)\r\n          .pipe(\r\n            finalize(() => {\r\n              this.isProcessingChat = false;\r\n              this.isAgentPlaygroundLoading = false;\r\n            }),\r\n            takeUntil(this.agentPlaygroundDestroy),\r\n          )\r\n          .subscribe({\r\n            next: (res) => this.handleAgentExecuteResponse(res, message),\r\n            error: (err: any) => {\r\n              this.chatMessages = [\r\n                ...this.chatMessages,\r\n                { from: 'user', text: message },\r\n                {\r\n                  from: 'ai',\r\n                  text:\r\n                    err?.error?.message ||\r\n                    err?.message ||\r\n                    'Something went wrong.',\r\n                },\r\n              ];\r\n            },\r\n          });\r\n      } else {\r\n        this.agentPlaygroundService\r\n          .submitAgentExecute(payload)\r\n          .pipe(\r\n            finalize(() => {\r\n              this.isProcessingChat = false;\r\n              this.isAgentPlaygroundLoading = false;\r\n            }),\r\n            takeUntil(this.agentPlaygroundDestroy),\r\n          )\r\n          .subscribe({\r\n            next: (res) => this.handleAgentExecuteResponse(res, message),\r\n            error: (err: any) => {\r\n              this.chatMessages = [\r\n                ...this.chatMessages,\r\n                { from: 'user', text: message },\r\n                {\r\n                  from: 'ai',\r\n                  text:\r\n                    err?.error?.message ||\r\n                    err?.message ||\r\n                    'Something went wrong.',\r\n                },\r\n              ];\r\n            },\r\n          });\r\n      }\r\n    }\r\n  }\r\n \r\n  onPromptChanged(prompt: DropdownOption): void {\r\n    this.inputText = prompt.name || String(prompt.value) || '';\r\n  }\r\n \r\n  onPlaygroundConversationalToggle(value: boolean): void {\r\n    // Update the form control\r\n    this.agentForm.get('isConversational')?.setValue(value);\r\n \r\n    // When conversational mode is turned off, clear the conversation history\r\n    // This ensures that the next message will be treated as a fresh start\r\n    if (!value) {\r\n      this.agentChatPayload = [];\r\n      console.log('Conversational mode disabled - cleared chat payload history');\r\n    } else {\r\n      console.log('Conversational mode enabled - will maintain chat history');\r\n    }\r\n  }\r\n \r\n  onPlaygroundTemplateToggle(value: boolean): void {\r\n    // Update the form control\r\n    this.agentForm.get('isUseTemplate')?.setValue(value);\r\n    console.log('Template mode toggled:', value);\r\n  }\r\n \r\n  onFilesSelected(files: any[]): void {\r\n    this.selectedFiles = files;\r\n    // Update agentFilesUploadedData for agent execution\r\n    this.agentFilesUploadedData = files;\r\n  }\r\n \r\n  onApprovalRequested(): void {\r\n    // Handle approval request\r\n  }\r\n \r\n  saveLogs(): void {\r\n    // Save execution logs\r\n  }\r\n \r\n  exportResults(section: 'activity' | 'output'): void {\r\n    // Export results\r\n  }\r\n \r\n  handleControlAction(action: 'play' | 'pause' | 'stop'): void {\r\n    // Handle execution control actions\r\n  }\r\n \r\n  navigateBack(): void {\r\n    this.router.navigate(['/build/agents', this.agentType], {\r\n      queryParams: { id: this.agentId, mode: 'view' },\r\n    });\r\n  }\r\n \r\n  editAgent(): void {\r\n    this.router.navigate(['/build/agents', this.agentType], {\r\n      queryParams: { id: this.agentId, mode: 'edit' },\r\n    });\r\n  }\r\n \r\n  navigateToAgentsList(): void {\r\n    this.router.navigate(['/build/agents']);\r\n  }\r\n \r\n  toggleLeftPanel(): void {\r\n    this.isLeftPanelCollapsed = !this.isLeftPanelCollapsed;\r\n  }\r\n \r\n  setActiveRightTab(tab: string): void {\r\n    this.activeRightTab = tab;\r\n  }\r\n \r\n  // Blueprint zone management methods\r\n  toggleBlueprintZone(zoneType: string): void {\r\n    this.blueprintZonesExpanded[zoneType] =\r\n      !this.blueprintZonesExpanded[zoneType];\r\n  }\r\n \r\n  isBlueprintZoneExpanded(zoneType: string): boolean {\r\n    return this.blueprintZonesExpanded[zoneType] || false;\r\n  }\r\n \r\n  // API and helper methods from build-agents component\r\n  private showAgentError(message: string): void {\r\n    this.chatMessages = [...this.chatMessages, { from: 'ai', text: message }];\r\n  }\r\n \r\n  private buildOrganizationPath(): string {\r\n    // Simple implementation - in real scenario this would be from navbar/metadata\r\n    return '';\r\n  }\r\n \r\n  private getMetadataFromNavbar(): { levelId?: number } {\r\n    // Simple implementation - in real scenario this would get org level mapping\r\n    return {};\r\n  }\r\n \r\n  handleAgentExecuteResponse(response: any, message: string): void {\r\n    try {\r\n      // Store the latest response for display in the output panel\r\n      this.latestAgentResponse = response;\r\n \r\n      const outputRaw = response?.agentResponse?.agent?.output;\r\n      let formattedOutput = '';\r\n \r\n      if (outputRaw) {\r\n        // Directly replace escaped \\n with real newlines\r\n        formattedOutput = outputRaw.replace(/\\\\n/g, '\\n');\r\n      } else {\r\n        formattedOutput = response?.agentResponse?.detail;\r\n      }\r\n \r\n      this.chatMessages = [\r\n        ...this.chatMessages,\r\n        { from: 'user', text: message },\r\n        { from: 'ai', text: formattedOutput || 'No response from agent.' },\r\n      ];\r\n    } catch (err: any) {\r\n      this.chatMessages = [\r\n        ...this.chatMessages,\r\n        {\r\n          from: 'ai',\r\n          text: err?.message || 'Agent response could not be processed.',\r\n        },\r\n      ];\r\n    }\r\n  }\r\n \r\n  private processAgentFilesAndSendMessage(\r\n    message: string,\r\n    mode: string,\r\n    useCaseIdentifier: string,\r\n    isConversational: boolean,\r\n    isUseTemplate: boolean,\r\n  ): void {\r\n    const formData = new FormData();\r\n    this.agentFilesUploadedData.forEach((fileData) => {\r\n      if (fileData.file) {\r\n        formData.append('files', fileData.file);\r\n      }\r\n    });\r\n \r\n    if (formData.has('files')) {\r\n      this.agentPlaygroundService\r\n        .getFileToContent(formData)\r\n        .pipe(\r\n          switchMap((fileResponse) => {\r\n            const fileContent =\r\n              fileResponse?.fileResponses\r\n                ?.map((response: any) => response.fileContent)\r\n                ?.join('\\n') || '';\r\n            this.sendAgentMessageToAPIWithFiles(\r\n              message,\r\n              mode,\r\n              useCaseIdentifier,\r\n              isConversational,\r\n              isUseTemplate,\r\n              fileContent,\r\n            );\r\n            return of(null);\r\n          }),\r\n          catchError((error) => {\r\n            console.error('Error parsing files:', error);\r\n            this.sendAgentMessageToAPI(\r\n              message,\r\n              mode,\r\n              useCaseIdentifier,\r\n              isConversational,\r\n              isUseTemplate,\r\n            );\r\n            return of(null);\r\n          }),\r\n        )\r\n        .subscribe();\r\n    } else {\r\n      this.sendAgentMessageToAPI(\r\n        message,\r\n        mode,\r\n        useCaseIdentifier,\r\n        isConversational,\r\n        isUseTemplate,\r\n      );\r\n    }\r\n  }\r\n \r\n  private sendAgentMessageToAPI(\r\n    message: string,\r\n    mode: string,\r\n    useCaseIdentifier: string,\r\n    isConversational: boolean,\r\n    isUseTemplate: boolean,\r\n  ): void {\r\n    console.log('API Call Parameters:', {\r\n      message,\r\n      mode,\r\n      useCaseIdentifier,\r\n      isConversational,\r\n      isUseTemplate,\r\n      currentChatPayloadLength: this.agentChatPayload.length\r\n    });\r\n \r\n    if (isConversational) {\r\n      this.agentChatPayload.push({ content: message, role: 'user' });\r\n    }\r\n \r\n    const payload = isConversational ? this.agentChatPayload : message;\r\n    const { levelId } = this.getMetadataFromNavbar();\r\n \r\n    console.log('Final payload being sent:', payload);\r\n \r\n    this.agentPlaygroundService\r\n      .generatePrompt(\r\n        payload,\r\n        mode,\r\n        isConversational,\r\n        isUseTemplate,\r\n        this.agentAttachment,\r\n        useCaseIdentifier,\r\n        '',\r\n        levelId,\r\n      )\r\n      .pipe(\r\n        finalize(() => {\r\n          this.isProcessingChat = false;\r\n          this.isAgentPlaygroundLoading = false;\r\n        }),\r\n        takeUntil(this.agentPlaygroundDestroy),\r\n      )\r\n      .subscribe({\r\n        next: (generatedResponse: any) => {\r\n          // Store the latest response for display in the output panel\r\n          this.latestAgentResponse = generatedResponse;\r\n \r\n          if (\r\n            generatedResponse?.response &&\r\n            generatedResponse?.response?.choices\r\n          ) {\r\n            const aiResponseText = generatedResponse.response.choices[0].text;\r\n            this.chatMessages = [\r\n              ...this.chatMessages,\r\n              { from: 'ai', text: aiResponseText },\r\n            ];\r\n            if (isConversational) {\r\n              this.agentChatPayload.push({\r\n                content: aiResponseText,\r\n                role: 'assistant',\r\n              });\r\n            }\r\n          } else {\r\n            console.warn('Unexpected API response format:', generatedResponse);\r\n            this.showAgentError(\r\n              'Received unexpected response format from API.',\r\n            );\r\n          }\r\n        },\r\n        error: (error: any) => {\r\n          console.error('API Error:', error);\r\n          const errorMessage =\r\n            error?.error?.message ||\r\n            'An error occurred while processing your request.';\r\n          this.showAgentError(errorMessage);\r\n          if (isConversational && this.agentChatPayload.length > 0) {\r\n            this.agentChatPayload.pop();\r\n          }\r\n        },\r\n      });\r\n  }\r\n \r\n  private sendAgentMessageToAPIWithFiles(\r\n    message: string,\r\n    mode: string,\r\n    useCaseIdentifier: string,\r\n    isConversational: boolean,\r\n    isUseTemplate: boolean,\r\n    fileContents: string,\r\n  ): void {\r\n    if (isConversational) {\r\n      this.agentChatPayload.push({ content: message, role: 'user' });\r\n    }\r\n    const payload = isConversational ? this.agentChatPayload : message;\r\n    const { levelId } = this.getMetadataFromNavbar();\r\n \r\n    this.agentPlaygroundService\r\n      .generatePrompt(\r\n        payload,\r\n        mode,\r\n        isConversational,\r\n        isUseTemplate,\r\n        this.agentAttachment,\r\n        useCaseIdentifier,\r\n        fileContents,\r\n        levelId,\r\n      )\r\n      .pipe(\r\n        finalize(() => {\r\n          this.isProcessingChat = false;\r\n          this.isAgentPlaygroundLoading = false;\r\n        }),\r\n        takeUntil(this.agentPlaygroundDestroy),\r\n      )\r\n      .subscribe({\r\n        next: (generatedResponse: any) => {\r\n          if (\r\n            generatedResponse?.response &&\r\n            generatedResponse?.response?.choices\r\n          ) {\r\n            const aiResponseText = generatedResponse.response.choices[0].text;\r\n            this.chatMessages = [\r\n              ...this.chatMessages,\r\n              { from: 'ai', text: aiResponseText },\r\n            ];\r\n            if (isConversational) {\r\n              this.agentChatPayload.push({\r\n                content: aiResponseText,\r\n                role: 'assistant',\r\n              });\r\n            }\r\n          } else {\r\n            console.warn('Unexpected API response format:', generatedResponse);\r\n            this.showAgentError(\r\n              'Received unexpected response format from API.',\r\n            );\r\n          }\r\n        },\r\n        error: (error: any) => {\r\n          console.error('API Error:', error);\r\n          const errorMessage =\r\n            error?.error?.message ||\r\n            'An error occurred while processing your request.';\r\n          this.showAgentError(errorMessage);\r\n          if (isConversational && this.agentChatPayload.length > 0) {\r\n            this.agentChatPayload.pop();\r\n          }\r\n        },\r\n      });\r\n  }\r\n \r\n  // Blueprint panel methods\r\n  private mapAgentConfigurationToBlueprint(agentData: any): void {\r\n    if (!agentData) {\r\n      console.warn('No agent data provided for blueprint');\r\n      return;\r\n    }\r\n \r\n    console.log('🔍 DEBUG: Full agent data received:', agentData);\r\n    console.log('🔍 DEBUG: Agent type:', this.agentType);\r\n    console.log('🔍 DEBUG: Agent data keys:', Object.keys(agentData));\r\n \r\n    // Clear existing nodes\r\n    this.buildAgentNodes = [];\r\n    this.canvasNodes = [];\r\n \r\n    let nodeCounter = 1;\r\n \r\n    // Map agent configuration to nodes based on agent type\r\n    if (this.agentType === 'individual') {\r\n      this.mapIndividualAgentToBlueprint(agentData, nodeCounter);\r\n    } else if (this.agentType === 'collaborative') {\r\n      this.mapCollaborativeAgentToBlueprint(agentData, nodeCounter);\r\n    }\r\n \r\n    console.log('🎯 Blueprint nodes mapped:', {\r\n      buildAgentNodes: this.buildAgentNodes,\r\n      canvasNodes: this.canvasNodes,\r\n      totalNodes: this.buildAgentNodes.length,\r\n    });\r\n  }\r\n \r\n  private mapIndividualAgentToBlueprint(\r\n    agentData: any,\r\n    nodeCounter: number,\r\n  ): void {\r\n    console.log('🔍 Individual agent mapping - checking fields:', {\r\n      config: agentData.config,\r\n      configLength: agentData.config?.length,\r\n      useCaseName: agentData.useCaseName,\r\n      promptTemplate: agentData.promptTemplate,\r\n    });\r\n \r\n    // Clear existing blueprint nodes\r\n    this.blueprintPromptNodes = [];\r\n    this.blueprintModelNodes = [];\r\n    this.blueprintKnowledgeNodes = [];\r\n    this.blueprintGuardrailNodes = [];\r\n \r\n    // Add prompt node - individual agents always have a prompt\r\n    if (agentData.useCaseName || agentData.promptTemplate) {\r\n      this.blueprintPromptNodes.push({\r\n        id: `prompt-${nodeCounter++}`,\r\n        name:\r\n          agentData.useCaseName || agentData.promptTemplate || 'System Prompt',\r\n        type: 'prompt',\r\n      });\r\n    }\r\n \r\n    // Process the config array to extract model, tools, knowledge bases, etc.\r\n    if (agentData.config && Array.isArray(agentData.config)) {\r\n      console.log(\r\n        '🔍 Processing config array with length:',\r\n        agentData.config.length,\r\n      );\r\n \r\n      agentData.config.forEach((category: any, categoryIndex: number) => {\r\n        console.log(`🔍 Category ${categoryIndex}:`, category);\r\n \r\n        if (category.config && Array.isArray(category.config)) {\r\n          console.log(\r\n            `🔍 Category ${categoryIndex} has ${category.config.length} config items`,\r\n          );\r\n \r\n          category.config.forEach((configItem: any, itemIndex: number) => {\r\n            console.log(`🔍 Config item ${categoryIndex}-${itemIndex}:`, {\r\n              configKey: configItem.configKey,\r\n              configValue: configItem.configValue,\r\n              type: typeof configItem.configValue,\r\n            });\r\n \r\n            // Handle MODEL configuration\r\n            if (configItem.configKey === 'MODEL' && configItem.configValue) {\r\n              console.log('✅ Adding model node:', configItem.configValue);\r\n              this.blueprintModelNodes.push({\r\n                id: `model-${nodeCounter++}`,\r\n                name: `Model: ${configItem.configValue}`,\r\n                type: 'model',\r\n              });\r\n            }\r\n \r\n            // Handle RAG_KNOWLEDGEBASE_NAME configuration\r\n            if (\r\n              configItem.configKey === 'RAG_KNOWLEDGEBASE_NAME' &&\r\n              configItem.configValue\r\n            ) {\r\n              console.log(\r\n                '✅ Adding knowledge base node:',\r\n                configItem.configValue,\r\n              );\r\n              const kbValue = configItem.configValue.toString();\r\n              const kbIds = kbValue\r\n                .split(',')\r\n                .map((id: string) => id.trim())\r\n                .filter((id: string) => id);\r\n \r\n              kbIds.forEach((kbId: string) => {\r\n                this.blueprintKnowledgeNodes.push({\r\n                  id: `knowledge-${nodeCounter++}`,\r\n                  name: `Knowledge Base: ${kbId}`,\r\n                  type: 'knowledge',\r\n                });\r\n              });\r\n            }\r\n \r\n            // Handle ENABLE_GUARDRAILS configuration\r\n            if (\r\n              configItem.configKey === 'ENABLE_GUARDRAILS' &&\r\n              (configItem.configValue === 'true' ||\r\n                configItem.configValue === true)\r\n            ) {\r\n              console.log(\r\n                '✅ Found ENABLE_GUARDRAILS = true, adding general guardrail node',\r\n              );\r\n              // Only add one general guardrail node if not already added\r\n              if (this.blueprintGuardrailNodes.length === 0) {\r\n                this.blueprintGuardrailNodes.push({\r\n                  id: `guardrail-${nodeCounter++}`,\r\n                  name: 'Guardrails Enabled',\r\n                  type: 'guardrail',\r\n                });\r\n              }\r\n            }\r\n \r\n            // Handle specific GUARDRAIL configurations\r\n            if (\r\n              configItem.configKey &&\r\n              configItem.configKey.startsWith('GUARDRAIL_') &&\r\n              configItem.configKey !== 'ENABLE_GUARDRAILS' &&\r\n              (configItem.configValue === 'true' ||\r\n                configItem.configValue === true ||\r\n                configItem.configValue !== 'false')\r\n            ) {\r\n              console.log('✅ Found specific guardrail:', {\r\n                key: configItem.configKey,\r\n                value: configItem.configValue,\r\n              });\r\n \r\n              let guardrailName = configItem.configKey;\r\n              if (guardrailName.startsWith('GUARDRAIL_')) {\r\n                guardrailName = guardrailName\r\n                  .replace('GUARDRAIL_', '')\r\n                  .replace(/_/g, ' ');\r\n              }\r\n \r\n              this.blueprintGuardrailNodes.push({\r\n                id: `guardrail-${nodeCounter++}`,\r\n                name: `Guardrail: ${guardrailName}`,\r\n                type: 'guardrail',\r\n              });\r\n            }\r\n \r\n            // Handle generic GUARDRAIL configuration (fallback)\r\n            if (\r\n              configItem.configKey === 'GUARDRAIL' &&\r\n              configItem.configValue &&\r\n              configItem.configValue !== 'false'\r\n            ) {\r\n              console.log(\r\n                '✅ Found generic GUARDRAIL config:',\r\n                configItem.configValue,\r\n              );\r\n              this.blueprintGuardrailNodes.push({\r\n                id: `guardrail-${nodeCounter++}`,\r\n                name: `Guardrail: ${configItem.configValue}`,\r\n                type: 'guardrail',\r\n              });\r\n            }\r\n          });\r\n        }\r\n      });\r\n    }\r\n \r\n    console.log('🎯 Final blueprint nodes:', {\r\n      promptNodes: this.blueprintPromptNodes,\r\n      modelNodes: this.blueprintModelNodes,\r\n      knowledgeNodes: this.blueprintKnowledgeNodes,\r\n      guardrailNodes: this.blueprintGuardrailNodes,\r\n    });\r\n \r\n    // Calculate completion percentage\r\n    const totalRequired = 2; // Prompt + Model are required\r\n    const currentRequired =\r\n      this.blueprintPromptNodes.length + this.blueprintModelNodes.length;\r\n    this.blueprintCompletionPercentage = Math.round(\r\n      (currentRequired / totalRequired) * 100,\r\n    );\r\n  }\r\n \r\n  private mapCollaborativeAgentToBlueprint(\r\n    agentData: any,\r\n    nodeCounter: number,\r\n  ): void {\r\n    // Clear existing blueprint nodes\r\n    this.blueprintPromptNodes = [];\r\n    this.blueprintModelNodes = [];\r\n    this.blueprintKnowledgeNodes = [];\r\n    this.blueprintToolNodes = [];\r\n \r\n    // Add prompt node from workflow\r\n    if (agentData.workflow && agentData.workflow.length > 0) {\r\n      const workflowItem = agentData.workflow[0]; // Usually first item has the prompt\r\n      if (workflowItem.goal) {\r\n        this.blueprintPromptNodes.push({\r\n          id: `prompt-${nodeCounter++}`,\r\n          name: workflowItem.goal || 'Collaborative Goal',\r\n          type: 'prompt',\r\n        });\r\n      }\r\n    }\r\n \r\n    // Add model node\r\n    if (agentData.model || agentData.modelName) {\r\n      this.blueprintModelNodes.push({\r\n        id: `model-${nodeCounter++}`,\r\n        name: agentData.model || agentData.modelName || 'Default Model',\r\n        type: 'model',\r\n      });\r\n    }\r\n \r\n    // Add tool nodes from workflow\r\n    if (agentData.workflow && agentData.workflow.length > 0) {\r\n      agentData.workflow.forEach((workflowItem: any) => {\r\n        if (workflowItem.tools && workflowItem.tools.length > 0) {\r\n          workflowItem.tools.forEach((tool: any) => {\r\n            this.blueprintToolNodes.push({\r\n              id: `tool-${nodeCounter++}`,\r\n              name: tool.name || tool.toolName || 'Tool',\r\n              type: 'tool',\r\n            });\r\n          });\r\n        }\r\n      });\r\n    }\r\n \r\n    // Add knowledge base nodes\r\n    if (agentData.knowledgeBase && agentData.knowledgeBase.length > 0) {\r\n      agentData.knowledgeBase.forEach((kb: any) => {\r\n        this.blueprintKnowledgeNodes.push({\r\n          id: `knowledge-${nodeCounter++}`,\r\n          name: kb.name || kb.documentName || 'Knowledge Base',\r\n          type: 'knowledge',\r\n        });\r\n      });\r\n    }\r\n \r\n    // Calculate completion percentage\r\n    const totalRequired = 2; // Prompt + Model are required\r\n    const currentRequired =\r\n      this.blueprintPromptNodes.length + this.blueprintModelNodes.length;\r\n    this.blueprintCompletionPercentage = Math.round(\r\n      (currentRequired / totalRequired) * 100,\r\n    );\r\n  }\r\n}", "<div class=\"agent-execution-container\">\r\n  <!-- Top Navigation Bar -->\r\n  <div class=\"top-nav-bar\">\r\n    <div class=\"nav-left\">\r\n      <button class=\"back-button\" (click)=\"navigateBack()\" type=\"button\">\r\n        <ava-icon\r\n          iconName=\"ArrowLeft\"\r\n          iconSize=\"16\"\r\n          iconColor=\"#374151\"\r\n        ></ava-icon>\r\n        <span class=\"agent-name\">{{ agentName }}</span>\r\n      </button>\r\n    </div>\r\n  </div>\r\n \r\n  <!-- Main Content with Two Panels -->\r\n  <div class=\"main-content\">\r\n    <!-- Left Panel: Playground -->\r\n    <div class=\"left-panel\" [class.collapsed]=\"isLeftPanelCollapsed\">\r\n      <div class=\"panel-header\">\r\n        <button class=\"collapse-btn\" (click)=\"toggleLeftPanel()\" type=\"button\">\r\n          <ava-icon\r\n            [iconName]=\"isLeftPanelCollapsed ? 'ChevronRight' : 'PanelLeft'\"\r\n            iconSize=\"16\"\r\n            iconColor=\"#6B7280\"\r\n          >\r\n          </ava-icon>\r\n        </button>\r\n        <button\r\n          class=\"history-btn\"\r\n          *ngIf=\"!isLeftPanelCollapsed\"\r\n          type=\"button\"\r\n          disabled\r\n        >\r\n          History\r\n        </button>\r\n      </div>\r\n \r\n      <div class=\"panel-content\" *ngIf=\"!isLeftPanelCollapsed\">\r\n        <app-agent-execution-playground\r\n          [messages]=\"chatMessages\"\r\n          [isLoading]=\"isProcessingChat\"\r\n          [agentType]=\"agentType\"\r\n          [showChatInteractionToggles]=\"agentType === 'individual'\"\r\n          [showAiPrincipleToggle]=\"true\"\r\n          [showApprovalButton]=\"false\"\r\n          [showDropdown]=\"false\"\r\n          [showAgentNameInput]=\"true\"\r\n          [showFileUploadButton]=\"true\"\r\n          [displayedAgentName]=\"agentName\"\r\n          [agentNamePlaceholder]=\"'Current Agent Name'\"\r\n          [acceptedFileType]=\"fileType\"\r\n          (promptChange)=\"onPromptChanged($event)\"\r\n          (messageSent)=\"handleChatMessage($event)\"\r\n          (conversationalToggle)=\"onPlaygroundConversationalToggle($event)\"\r\n          (templateToggle)=\"onPlaygroundTemplateToggle($event)\"\r\n          (filesSelected)=\"onFilesSelected($event)\"\r\n          (approvalRequested)=\"onApprovalRequested()\"\r\n        >\r\n        </app-agent-execution-playground>\r\n      </div>\r\n    </div>\r\n \r\n    <!-- Right Panel: Blueprint/Output -->\r\n    <div class=\"right-panel\">\r\n      <!-- Right Panel Header -->\r\n      <div class=\"panel-header\">\r\n        <div class=\"tabs-container\">\r\n          <button\r\n            class=\"tab-btn\"\r\n            [class.active]=\"activeRightTab === 'blueprint'\"\r\n            (click)=\"setActiveRightTab('blueprint')\"\r\n          >\r\n            Blueprint\r\n          </button>\r\n          <button\r\n            class=\"tab-btn\"\r\n            [class.active]=\"activeRightTab === 'output'\"\r\n            (click)=\"setActiveRightTab('output')\"\r\n          >\r\n            Agent Output\r\n          </button>\r\n        </div>\r\n      </div>\r\n \r\n      <div class=\"panel-content\">\r\n        <!-- Blueprint Content -->\r\n        <div *ngIf=\"activeRightTab === 'blueprint'\" class=\"blueprint-content\">\r\n          <div class=\"blueprint-header\">\r\n            <h3>Agent Blueprint</h3>\r\n          </div>\r\n \r\n          <!-- Custom Blueprint Display -->\r\n          <div class=\"custom-blueprint-container\">\r\n            <!-- SVG Connecting Lines -->\r\n            <svg\r\n              class=\"connection-lines\"\r\n              viewBox=\"0 0 100 100\"\r\n              preserveAspectRatio=\"none\"\r\n            >\r\n              <!-- Line from System Prompt (top-left) to center -->\r\n              <line\r\n                x1=\"25\"\r\n                y1=\"25\"\r\n                x2=\"50\"\r\n                y2=\"50\"\r\n                stroke=\"#BBBEC5\"\r\n                stroke-width=\"0.3\"\r\n              />\r\n \r\n              <!-- Line from AI Model (top-right) to center -->\r\n              <line\r\n                x1=\"75\"\r\n                y1=\"25\"\r\n                x2=\"50\"\r\n                y2=\"50\"\r\n                stroke=\"#BBBEC5\"\r\n                stroke-width=\"0.3\"\r\n              />\r\n \r\n              <!-- Line from Knowledge Base (bottom-left) to center -->\r\n              <line\r\n                x1=\"25\"\r\n                y1=\"75\"\r\n                x2=\"50\"\r\n                y2=\"50\"\r\n                stroke=\"#BBBEC5\"\r\n                stroke-width=\"0.3\"\r\n              />\r\n \r\n              <!-- Line from Guardrails (bottom-right) to center -->\r\n              <line\r\n                x1=\"75\"\r\n                y1=\"75\"\r\n                x2=\"50\"\r\n                y2=\"50\"\r\n                stroke=\"#BBBEC5\"\r\n                stroke-width=\"0.3\"\r\n              />\r\n            </svg>\r\n \r\n            <!-- Central Progress Bar -->\r\n            <div class=\"central-progress\">\r\n              <div class=\"progress-ring\">\r\n                <svg class=\"progress-circle\" width=\"120\" height=\"120\">\r\n                  <defs>\r\n                    <linearGradient\r\n                      id=\"progressGradient\"\r\n                      x1=\"0%\"\r\n                      y1=\"0%\"\r\n                      x2=\"100%\"\r\n                      y2=\"100%\"\r\n                    >\r\n                      <stop offset=\"33.91%\" style=\"stop-color: #0084ff\" />\r\n                      <stop offset=\"100%\" style=\"stop-color: #03bdd4\" />\r\n                    </linearGradient>\r\n                  </defs>\r\n                  <circle\r\n                    class=\"progress-background\"\r\n                    cx=\"60\"\r\n                    cy=\"60\"\r\n                    r=\"50\"\r\n                    fill=\"none\"\r\n                    stroke=\"#e5e7eb\"\r\n                    stroke-width=\"8\"\r\n                  />\r\n                  <circle\r\n                    class=\"progress-bar\"\r\n                    cx=\"60\"\r\n                    cy=\"60\"\r\n                    r=\"50\"\r\n                    fill=\"none\"\r\n                    stroke=\"url(#progressGradient)\"\r\n                    stroke-width=\"8\"\r\n                    stroke-linecap=\"round\"\r\n                    [style.stroke-dasharray]=\"314\"\r\n                    [style.stroke-dashoffset]=\"\r\n                      314 - (314 * blueprintCompletionPercentage) / 100\r\n                    \"\r\n                    transform=\"rotate(180 60 60)\"\r\n                  />\r\n                </svg>\r\n                <div class=\"progress-content\">\r\n                  <div class=\"progress-percentage\">\r\n                    {{ blueprintCompletionPercentage }}%\r\n                  </div>\r\n                  <div class=\"progress-label\">Complete</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n \r\n            <!-- Blueprint Zones Layout -->\r\n            <div id=\"parent-box\">\r\n              <!-- <div id=\"box1\"> -->\r\n              <!-- System Prompt Zone -->\r\n              <div\r\n                class=\"blueprint-zone north-zone prompts-zone\"\r\n                [class.has-nodes]=\"blueprintPromptNodes.length > 0\"\r\n              >\r\n                <div\r\n                  class=\"zone-header\"\r\n                  (click)=\"toggleBlueprintZone('prompt')\"\r\n                >\r\n                  <div class=\"header-content\">\r\n                    <div class=\"header-icon\">\r\n                      <svg\r\n                        width=\"45\"\r\n                        height=\"44\"\r\n                        viewBox=\"0 0 45 44\"\r\n                        fill=\"none\"\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                      >\r\n                        <rect\r\n                          x=\"0.679688\"\r\n                          width=\"44\"\r\n                          height=\"44\"\r\n                          rx=\"8\"\r\n                          fill=\"#5082EF\"\r\n                        />\r\n                        <path\r\n                          d=\"M20.6166 25.5C20.5273 25.1539 20.347 24.8381 20.0942 24.5854C19.8415 24.3327 19.5257 24.1523 19.1796 24.063L13.0446 22.481C12.94 22.4513 12.8478 22.3883 12.7822 22.3014C12.7166 22.2146 12.6812 22.1088 12.6812 22C12.6812 21.8912 12.7166 21.7854 12.7822 21.6986C12.8478 21.6118 12.94 21.5487 13.0446 21.519L19.1796 19.936C19.5256 19.8468 19.8413 19.6666 20.094 19.414C20.3467 19.1615 20.5272 18.8459 20.6166 18.5L22.1986 12.365C22.228 12.2599 22.291 12.1673 22.3779 12.1014C22.4649 12.0355 22.571 11.9998 22.6801 11.9998C22.7892 11.9998 22.8954 12.0355 22.9823 12.1014C23.0692 12.1673 23.1322 12.2599 23.1616 12.365L24.7426 18.5C24.8319 18.8461 25.0123 19.1619 25.265 19.4146C25.5177 19.6673 25.8336 19.8477 26.1796 19.937L32.3146 21.518C32.4201 21.5471 32.5132 21.61 32.5795 21.6971C32.6458 21.7841 32.6817 21.8906 32.6817 22C32.6817 22.1094 32.6458 22.2159 32.5795 22.3029C32.5132 22.39 32.4201 22.4529 32.3146 22.482L26.1796 24.063C25.8336 24.1523 25.5177 24.3327 25.265 24.5854C25.0123 24.8381 24.8319 25.1539 24.7426 25.5L23.1606 31.635C23.1312 31.7401 23.0682 31.8327 22.9813 31.8986C22.8944 31.9646 22.7882 32.0003 22.6791 32.0003C22.57 32.0003 22.4639 31.9646 22.3769 31.8986C22.29 31.8327 22.227 31.7401 22.1976 31.635L20.6166 25.5Z\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                      </svg>\r\n                    </div>\r\n                    <h3 class=\"zone-title\">System Prompt</h3>\r\n                  </div>\r\n                  <div class=\"header-actions\">\r\n                    <span class=\"required-badge\">Required</span>\r\n                    <button class=\"accordion-toggle\" type=\"button\">\r\n                      <svg\r\n                        width=\"16\"\r\n                        height=\"16\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        fill=\"none\"\r\n                        [style.transform]=\"\r\n                          isBlueprintZoneExpanded('prompt')\r\n                            ? 'rotate(180deg)'\r\n                            : 'rotate(0deg)'\r\n                        \"\r\n                      >\r\n                        <path\r\n                          d=\"M6 9L12 15L18 9\"\r\n                          stroke=\"currentColor\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                      </svg>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n                <div\r\n                  class=\"zone-content\"\r\n                  *ngIf=\"isBlueprintZoneExpanded('prompt')\"\r\n                >\r\n                  <div\r\n                    *ngIf=\"blueprintPromptNodes.length === 0\"\r\n                    class=\"empty-state\"\r\n                  >\r\n                    No prompt configured\r\n                  </div>\r\n                  <div class=\"nodes-list\">\r\n                    <div\r\n                      *ngFor=\"let node of blueprintPromptNodes\"\r\n                      class=\"kanban-card\"\r\n                    >\r\n                      <span class=\"card-title\">{{\r\n                        node.name || \"Prompt\"\r\n                      }}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n \r\n              <!-- Knowledge Base Zone -->\r\n              <div\r\n                class=\"blueprint-zone west-zone knowledge-zone\"\r\n                [class.has-nodes]=\"blueprintKnowledgeNodes.length > 0\"\r\n              >\r\n                <div\r\n                  class=\"zone-header\"\r\n                  (click)=\"toggleBlueprintZone('knowledge')\"\r\n                >\r\n                  <div class=\"header-content\">\r\n                    <div class=\"header-icon\">\r\n                      <svg\r\n                        width=\"45\"\r\n                        height=\"44\"\r\n                        viewBox=\"0 0 45 44\"\r\n                        fill=\"none\"\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                      >\r\n                        <rect\r\n                          x=\"0.679688\"\r\n                          width=\"44\"\r\n                          height=\"44\"\r\n                          rx=\"8\"\r\n                          fill=\"#308666\"\r\n                        />\r\n                        <path\r\n                          d=\"M22.6797 17V31\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                        <path\r\n                          d=\"M26.6797 22H28.6797\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                        <path\r\n                          d=\"M26.6797 18H28.6797\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                        <path\r\n                          d=\"M13.6797 28C13.4145 28 13.1601 27.8946 12.9726 27.7071C12.785 27.5196 12.6797 27.2652 12.6797 27V14C12.6797 13.7348 12.785 13.4804 12.9726 13.2929C13.1601 13.1054 13.4145 13 13.6797 13H18.6797C19.7406 13 20.758 13.4214 21.5081 14.1716C22.2583 14.9217 22.6797 15.9391 22.6797 17C22.6797 15.9391 23.1011 14.9217 23.8513 14.1716C24.6014 13.4214 25.6188 13 26.6797 13H31.6797C31.9449 13 32.1993 13.1054 32.3868 13.2929C32.5743 13.4804 32.6797 13.7348 32.6797 14V27C32.6797 27.2652 32.5743 27.5196 32.3868 27.7071C32.1993 27.8946 31.9449 28 31.6797 28H25.6797C24.884 28 24.121 28.3161 23.5584 28.8787C22.9958 29.4413 22.6797 30.2044 22.6797 31C22.6797 30.2044 22.3636 29.4413 21.801 28.8787C21.2384 28.3161 20.4753 28 19.6797 28H13.6797Z\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                        <path\r\n                          d=\"M16.6797 22H18.6797\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                        <path\r\n                          d=\"M16.6797 18H18.6797\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                      </svg>\r\n                    </div>\r\n                    <h3 class=\"zone-title\">Knowledgebase</h3>\r\n                  </div>\r\n                  <div class=\"header-actions\">\r\n                    <span class=\"optional-badge\">Optional</span>\r\n                    <button class=\"accordion-toggle\" type=\"button\">\r\n                      <svg\r\n                        width=\"16\"\r\n                        height=\"16\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        fill=\"none\"\r\n                        [style.transform]=\"\r\n                          isBlueprintZoneExpanded('knowledge')\r\n                            ? 'rotate(180deg)'\r\n                            : 'rotate(0deg)'\r\n                        \"\r\n                      >\r\n                        <path\r\n                          d=\"M6 9L12 15L18 9\"\r\n                          stroke=\"currentColor\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                      </svg>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n                <div\r\n                  class=\"zone-content\"\r\n                  *ngIf=\"isBlueprintZoneExpanded('knowledge')\"\r\n                >\r\n                  <div\r\n                    *ngIf=\"blueprintKnowledgeNodes.length === 0\"\r\n                    class=\"empty-state\"\r\n                  >\r\n                    No knowledge base configured\r\n                  </div>\r\n                  <div class=\"nodes-list\">\r\n                    <div\r\n                      *ngFor=\"let node of blueprintKnowledgeNodes\"\r\n                      class=\"kanban-card\"\r\n                    >\r\n                      <span class=\"card-title\">{{\r\n                        node.name || \"Knowledge Base\"\r\n                      }}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <!-- </div> -->\r\n \r\n              <!-- <div id=\"box2\"> -->\r\n              <!-- AI Model Zone -->\r\n              <div\r\n                class=\"blueprint-zone east-zone models-zone\"\r\n                [class.has-nodes]=\"blueprintModelNodes.length > 0\"\r\n              >\r\n                <div class=\"zone-header\" (click)=\"toggleBlueprintZone('model')\">\r\n                  <div class=\"header-content\">\r\n                    <div class=\"header-icon\">\r\n                      <svg\r\n                        width=\"45\"\r\n                        height=\"44\"\r\n                        viewBox=\"0 0 45 44\"\r\n                        fill=\"none\"\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                      >\r\n                        <rect\r\n                          x=\"0.679688\"\r\n                          width=\"44\"\r\n                          height=\"44\"\r\n                          rx=\"8\"\r\n                          fill=\"#997BCF\"\r\n                        />\r\n                        <path\r\n                          d=\"M31.6797 18C31.6793 17.6493 31.5867 17.3048 31.4112 17.0012C31.2357 16.6975 30.9834 16.4454 30.6797 16.27L23.6797 12.27C23.3756 12.0945 23.0308 12.0021 22.6797 12.0021C22.3286 12.0021 21.9837 12.0945 21.6797 12.27L14.6797 16.27C14.376 16.4454 14.1237 16.6975 13.9481 17.0012C13.7726 17.3048 13.68 17.6493 13.6797 18V26C13.68 26.3508 13.7726 26.6952 13.9481 26.9989C14.1237 27.3025 14.376 27.5547 14.6797 27.73L21.6797 31.73C21.9837 31.9056 22.3286 31.998 22.6797 31.998C23.0308 31.998 23.3756 31.9056 23.6797 31.73L30.6797 27.73C30.9834 27.5547 31.2357 27.3025 31.4112 26.9989C31.5867 26.6952 31.6793 26.3508 31.6797 26V18Z\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                        <path\r\n                          d=\"M13.9795 17L22.6795 22L31.3795 17\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                        <path\r\n                          d=\"M22.6797 32V22\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                      </svg>\r\n                    </div>\r\n                    <h3 class=\"zone-title\">AI Model</h3>\r\n                  </div>\r\n                  <div class=\"header-actions\">\r\n                    <span class=\"required-badge\">Required</span>\r\n                    <button class=\"accordion-toggle\" type=\"button\">\r\n                      <svg\r\n                        width=\"16\"\r\n                        height=\"16\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        fill=\"none\"\r\n                        [style.transform]=\"\r\n                          isBlueprintZoneExpanded('model')\r\n                            ? 'rotate(180deg)'\r\n                            : 'rotate(0deg)'\r\n                        \"\r\n                      >\r\n                        <path\r\n                          d=\"M6 9L12 15L18 9\"\r\n                          stroke=\"currentColor\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                      </svg>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n                <div\r\n                  class=\"zone-content\"\r\n                  *ngIf=\"isBlueprintZoneExpanded('model')\"\r\n                >\r\n                  <div\r\n                    *ngIf=\"blueprintModelNodes.length === 0\"\r\n                    class=\"empty-state\"\r\n                  >\r\n                    No model configured\r\n                  </div>\r\n                  <div class=\"nodes-list\">\r\n                    <div\r\n                      *ngFor=\"let node of blueprintModelNodes\"\r\n                      class=\"kanban-card\"\r\n                    >\r\n                      <span class=\"card-title\">{{ node.name || \"Model\" }}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n \r\n              <!-- Guardrails Zone -->\r\n              <div\r\n                class=\"blueprint-zone south-zone guardrails-zone\"\r\n                [class.has-nodes]=\"blueprintGuardrailNodes.length > 0\"\r\n              >\r\n                <div\r\n                  class=\"zone-header\"\r\n                  (click)=\"toggleBlueprintZone('guardrail')\"\r\n                >\r\n                  <div class=\"header-content\">\r\n                    <div class=\"header-icon\">\r\n                      <svg\r\n                        width=\"45\"\r\n                        height=\"44\"\r\n                        viewBox=\"0 0 45 44\"\r\n                        fill=\"none\"\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                      >\r\n                        <rect\r\n                          x=\"0.679688\"\r\n                          width=\"44\"\r\n                          height=\"44\"\r\n                          rx=\"8\"\r\n                          fill=\"#DC2626\"\r\n                        />\r\n                        <path\r\n                          d=\"M30.6797 23C30.6797 28 27.1797 30.5 23.0197 31.95C22.8019 32.0238 22.5652 32.0202 22.3497 31.94C18.1797 30.5 14.6797 28 14.6797 23V16C14.6797 15.7347 14.785 15.4804 14.9726 15.2929C15.1601 15.1053 15.4145 15 15.6797 15C17.6797 15 20.1797 13.8 21.9197 12.28C22.1315 12.099 22.401 11.9995 22.6797 11.9995C22.9583 11.9995 23.2278 12.099 23.4397 12.28C25.1897 13.81 27.6797 15 29.6797 15C29.9449 15 30.1993 15.1053 30.3868 15.2929C30.5743 15.4804 30.6797 15.7347 30.6797 16V23Z\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                      </svg>\r\n                    </div>\r\n                    <h3 class=\"zone-title\">Guardrails</h3>\r\n                  </div>\r\n                  <div class=\"header-actions\">\r\n                    <span class=\"optional-badge\">Optional</span>\r\n                    <button class=\"accordion-toggle\" type=\"button\">\r\n                      <svg\r\n                        width=\"16\"\r\n                        height=\"16\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        fill=\"none\"\r\n                        [style.transform]=\"\r\n                          isBlueprintZoneExpanded('guardrail')\r\n                            ? 'rotate(180deg)'\r\n                            : 'rotate(0deg)'\r\n                        \"\r\n                      >\r\n                        <path\r\n                          d=\"M6 9L12 15L18 9\"\r\n                          stroke=\"currentColor\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                      </svg>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n                <div\r\n                  class=\"zone-content\"\r\n                  *ngIf=\"isBlueprintZoneExpanded('guardrail')\"\r\n                >\r\n                  <div\r\n                    *ngIf=\"blueprintGuardrailNodes.length === 0\"\r\n                    class=\"empty-state\"\r\n                  >\r\n                    No guardrails configured\r\n                  </div>\r\n                  <div class=\"nodes-list\">\r\n                    <div\r\n                      *ngFor=\"let node of blueprintGuardrailNodes\"\r\n                      class=\"kanban-card\"\r\n                    >\r\n                      <span class=\"card-title\">{{\r\n                        node.name || \"Guardrail\"\r\n                      }}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <!-- </div> -->\r\n            </div>\r\n          </div>\r\n        </div>\r\n \r\n        <!-- Agent Output Content -->\r\n        <div *ngIf=\"activeRightTab === 'output'\" class=\"output-content\">\r\n          <!-- Individual Agent Output -->\r\n          <div *ngIf=\"agentType === 'individual'\" class=\"individual-output\">\r\n            <h3>Agent Output</h3>\r\n            <div *ngIf=\"latestAgentResponse?.response?.choices?.length > 0; else noIndividualOutput\" class=\"output-box\">\r\n              <div class=\"output-section\">\r\n                <h4>Response</h4>\r\n                <div class=\"output-text\">\r\n                  {{ latestAgentResponse.response.choices[0].text }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <ng-template #noIndividualOutput>\r\n              <p>No agent output available yet. Send a message to see the response.</p>\r\n            </ng-template>\r\n          </div>\r\n \r\n          <!-- Collaborative Agent Output -->\r\n          <div *ngIf=\"agentType === 'collaborative'\" class=\"collaborative-output\">\r\n            <h3>Task Outputs</h3>\r\n            <div *ngIf=\"latestAgentResponse?.agentResponse?.agent?.tasksOutputs?.length > 0; else noCollaborativeOutput\">\r\n              <div *ngFor=\"let taskOutput of latestAgentResponse.agentResponse.agent.tasksOutputs; let i = index\" class=\"output-box\">\r\n                <div class=\"output-section\">\r\n                  <h4>{{ taskOutput.summary || ('Task ' + (i + 1)) }}</h4>\r\n                  <div *ngIf=\"taskOutput.description\" class=\"task-description\">\r\n                    <strong>Description:</strong> {{ taskOutput.description }}\r\n                  </div>\r\n                  <div *ngIf=\"taskOutput.expected_output\" class=\"task-expected\">\r\n                    <strong>Expected Output:</strong> {{ taskOutput.expected_output }}\r\n                  </div>\r\n                  <div class=\"output-text\">\r\n                    {{ taskOutput.raw }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <ng-template #noCollaborativeOutput>\r\n              <p>No task outputs available yet. Send a message to see the results.</p>\r\n            </ng-template>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>"], "mappings": "AAEA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,OAAO,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,EAAE,QAAQ,MAAM;AAC9E,SAGEC,WAAW,QAEN,gBAAgB;AAEvB;AACA,SAASC,iCAAiC,QAAQ,8EAA8E;AAEhI,SAEEC,aAAa,QAIR,wBAAwB;AAG/B,SAASC,WAAW,QAAQ,+CAA+C;;;;;;;;;;;;ICKnEC,EAAA,CAAAC,cAAA,iBAKC;IACCD,EAAA,CAAAE,MAAA,gBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAITH,EADF,CAAAC,cAAA,cAAyD,yCAoBtD;IADCD,EALA,CAAAI,UAAA,0BAAAC,+FAAAC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAgBF,MAAA,CAAAG,eAAA,CAAAN,MAAA,CAAuB;IAAA,EAAC,yBAAAO,8FAAAP,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CACzBF,MAAA,CAAAK,iBAAA,CAAAR,MAAA,CAAyB;IAAA,EAAC,kCAAAS,uGAAAT,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CACjBF,MAAA,CAAAO,gCAAA,CAAAV,MAAA,CAAwC;IAAA,EAAC,4BAAAW,iGAAAX,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAC/CF,MAAA,CAAAS,0BAAA,CAAAZ,MAAA,CAAkC;IAAA,EAAC,2BAAAa,gGAAAb,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CACpCF,MAAA,CAAAW,eAAA,CAAAd,MAAA,CAAuB;IAAA,EAAC,+BAAAe,oGAAA;MAAArB,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CACpBF,MAAA,CAAAa,mBAAA,EAAqB;IAAA,EAAC;IAG/CtB,EADE,CAAAG,YAAA,EAAiC,EAC7B;;;;IApBFH,EAAA,CAAAuB,SAAA,EAAyB;IAWzBvB,EAXA,CAAAwB,UAAA,aAAAf,MAAA,CAAAgB,YAAA,CAAyB,cAAAhB,MAAA,CAAAiB,gBAAA,CACK,cAAAjB,MAAA,CAAAkB,SAAA,CACP,+BAAAlB,MAAA,CAAAkB,SAAA,kBACkC,+BAC3B,6BACF,uBACN,4BACK,8BACE,uBAAAlB,MAAA,CAAAmB,SAAA,CACG,8CACa,qBAAAnB,MAAA,CAAAoB,QAAA,CAChB;;;;;IAgNrB7B,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAMFH,EAJF,CAAAC,cAAA,cAGC,eAC0B;IAAAD,EAAA,CAAAE,MAAA,GAEvB;IACJF,EADI,CAAAG,YAAA,EAAO,EACL;;;;IAHqBH,EAAA,CAAAuB,SAAA,GAEvB;IAFuBvB,EAAA,CAAA8B,iBAAA,CAAAC,OAAA,CAAAC,IAAA,aAEvB;;;;;IAjBRhC,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAiC,UAAA,IAAAC,oDAAA,kBAGC;IAGDlC,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAiC,UAAA,IAAAE,oDAAA,kBAGC;IAMLnC,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAfDH,EAAA,CAAAuB,SAAA,EAAuC;IAAvCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAA2B,oBAAA,CAAAC,MAAA,OAAuC;IAOrBrC,EAAA,CAAAuB,SAAA,GAAuB;IAAvBvB,EAAA,CAAAwB,UAAA,YAAAf,MAAA,CAAA2B,oBAAA,CAAuB;;;;;IA+G5CpC,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,MAAA,qCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAMFH,EAJF,CAAAC,cAAA,cAGC,eAC0B;IAAAD,EAAA,CAAAE,MAAA,GAEvB;IACJF,EADI,CAAAG,YAAA,EAAO,EACL;;;;IAHqBH,EAAA,CAAAuB,SAAA,GAEvB;IAFuBvB,EAAA,CAAA8B,iBAAA,CAAAQ,OAAA,CAAAN,IAAA,qBAEvB;;;;;IAjBRhC,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAiC,UAAA,IAAAM,oDAAA,kBAGC;IAGDvC,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAiC,UAAA,IAAAO,oDAAA,kBAGC;IAMLxC,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAfDH,EAAA,CAAAuB,SAAA,EAA0C;IAA1CvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAgC,uBAAA,CAAAJ,MAAA,OAA0C;IAOxBrC,EAAA,CAAAuB,SAAA,GAA0B;IAA1BvB,EAAA,CAAAwB,UAAA,YAAAf,MAAA,CAAAgC,uBAAA,CAA0B;;;;;IAyF/CzC,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAMFH,EAJF,CAAAC,cAAA,cAGC,eAC0B;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IACrDF,EADqD,CAAAG,YAAA,EAAO,EACtD;;;;IADqBH,EAAA,CAAAuB,SAAA,GAA0B;IAA1BvB,EAAA,CAAA8B,iBAAA,CAAAY,OAAA,CAAAV,IAAA,YAA0B;;;;;IAfzDhC,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAiC,UAAA,IAAAU,oDAAA,kBAGC;IAGD3C,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAiC,UAAA,IAAAW,oDAAA,kBAGC;IAIL5C,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAbDH,EAAA,CAAAuB,SAAA,EAAsC;IAAtCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAoC,mBAAA,CAAAR,MAAA,OAAsC;IAOpBrC,EAAA,CAAAuB,SAAA,GAAsB;IAAtBvB,EAAA,CAAAwB,UAAA,YAAAf,MAAA,CAAAoC,mBAAA,CAAsB;;;;;IA0E3C7C,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAMFH,EAJF,CAAAC,cAAA,cAGC,eAC0B;IAAAD,EAAA,CAAAE,MAAA,GAEvB;IACJF,EADI,CAAAG,YAAA,EAAO,EACL;;;;IAHqBH,EAAA,CAAAuB,SAAA,GAEvB;IAFuBvB,EAAA,CAAA8B,iBAAA,CAAAgB,OAAA,CAAAd,IAAA,gBAEvB;;;;;IAjBRhC,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAiC,UAAA,IAAAc,oDAAA,kBAGC;IAGD/C,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAiC,UAAA,IAAAe,oDAAA,kBAGC;IAMLhD,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAfDH,EAAA,CAAAuB,SAAA,EAA0C;IAA1CvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAwC,uBAAA,CAAAZ,MAAA,OAA0C;IAOxBrC,EAAA,CAAAuB,SAAA,GAA0B;IAA1BvB,EAAA,CAAAwB,UAAA,YAAAf,MAAA,CAAAwC,uBAAA,CAA0B;;;;;;IA5drDjD,EAFJ,CAAAC,cAAA,cAAsE,cACtC,SACxB;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IACrBF,EADqB,CAAAG,YAAA,EAAK,EACpB;IAGNH,EAAA,CAAAC,cAAA,cAAwC;;IAEtCD,EAAA,CAAAC,cAAA,cAIC;IAgCCD,EA9BA,CAAAkD,SAAA,eAOE,eAUA,eAUA,eAUA;IACJlD,EAAA,CAAAG,YAAA,EAAM;;IAIJH,EADF,CAAAC,cAAA,eAA8B,eACD;;IAGrBD,EAFJ,CAAAC,cAAA,eAAsD,YAC9C,0BAOH;IAECD,EADA,CAAAkD,SAAA,gBAAoD,gBACF;IAEtDlD,EADE,CAAAG,YAAA,EAAiB,EACZ;IAUPH,EATA,CAAAkD,SAAA,kBAQE,kBAeA;IACJlD,EAAA,CAAAG,YAAA,EAAM;;IAEJH,EADF,CAAAC,cAAA,eAA8B,eACK;IAC/BD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAG1CF,EAH0C,CAAAG,YAAA,EAAM,EACtC,EACF,EACF;IAUFH,EAPJ,CAAAC,cAAA,eAAqB,eAMlB,eAIE;IADCD,EAAA,CAAAI,UAAA,mBAAA+C,8DAAA;MAAAnD,EAAA,CAAAO,aAAA,CAAA6C,GAAA;MAAA,MAAA3C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA4C,mBAAA,CAAoB,QAAQ,CAAC;IAAA,EAAC;IAGrCrD,EADF,CAAAC,cAAA,eAA4B,eACD;;IACvBD,EAAA,CAAAC,cAAA,eAMC;IAQCD,EAPA,CAAAkD,SAAA,gBAME,gBAOA;IAENlD,EADE,CAAAG,YAAA,EAAM,EACF;;IACNH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IACtCF,EADsC,CAAAG,YAAA,EAAK,EACrC;IAEJH,EADF,CAAAC,cAAA,eAA4B,gBACG;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,kBAA+C;;IAC7CD,EAAA,CAAAC,cAAA,eAUC;IACCD,EAAA,CAAAkD,SAAA,gBAME;IAIVlD,EAHM,CAAAG,YAAA,EAAM,EACC,EACL,EACF;IACNH,EAAA,CAAAiC,UAAA,KAAAqB,8CAAA,kBAGC;IAkBHtD,EAAA,CAAAG,YAAA,EAAM;;IAOJH,EAJF,CAAAC,cAAA,eAGC,eAIE;IADCD,EAAA,CAAAI,UAAA,mBAAAmD,8DAAA;MAAAvD,EAAA,CAAAO,aAAA,CAAA6C,GAAA;MAAA,MAAA3C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA4C,mBAAA,CAAoB,WAAW,CAAC;IAAA,EAAC;IAGxCrD,EADF,CAAAC,cAAA,eAA4B,eACD;;IACvBD,EAAA,CAAAC,cAAA,eAMC;IA2CCD,EA1CA,CAAAkD,SAAA,gBAME,gBAOA,gBAOA,gBAOA,gBAOA,gBAOA,gBAOA;IAENlD,EADE,CAAAG,YAAA,EAAM,EACF;;IACNH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IACtCF,EADsC,CAAAG,YAAA,EAAK,EACrC;IAEJH,EADF,CAAAC,cAAA,eAA4B,gBACG;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,kBAA+C;;IAC7CD,EAAA,CAAAC,cAAA,eAUC;IACCD,EAAA,CAAAkD,SAAA,gBAME;IAIVlD,EAHM,CAAAG,YAAA,EAAM,EACC,EACL,EACF;IACNH,EAAA,CAAAiC,UAAA,KAAAuB,8CAAA,kBAGC;IAkBHxD,EAAA,CAAAG,YAAA,EAAM;;IASJH,EAJF,CAAAC,cAAA,eAGC,eACiE;IAAvCD,EAAA,CAAAI,UAAA,mBAAAqD,8DAAA;MAAAzD,EAAA,CAAAO,aAAA,CAAA6C,GAAA;MAAA,MAAA3C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA4C,mBAAA,CAAoB,OAAO,CAAC;IAAA,EAAC;IAE3DrD,EADF,CAAAC,cAAA,eAA4B,eACD;;IACvBD,EAAA,CAAAC,cAAA,eAMC;IAsBCD,EArBA,CAAAkD,SAAA,gBAME,gBAOA,gBAOA,gBAOA;IAENlD,EADE,CAAAG,YAAA,EAAM,EACF;;IACNH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IACjCF,EADiC,CAAAG,YAAA,EAAK,EAChC;IAEJH,EADF,CAAAC,cAAA,eAA4B,gBACG;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,kBAA+C;;IAC7CD,EAAA,CAAAC,cAAA,eAUC;IACCD,EAAA,CAAAkD,SAAA,gBAME;IAIVlD,EAHM,CAAAG,YAAA,EAAM,EACC,EACL,EACF;IACNH,EAAA,CAAAiC,UAAA,KAAAyB,8CAAA,kBAGC;IAgBH1D,EAAA,CAAAG,YAAA,EAAM;;IAOJH,EAJF,CAAAC,cAAA,eAGC,eAIE;IADCD,EAAA,CAAAI,UAAA,mBAAAuD,8DAAA;MAAA3D,EAAA,CAAAO,aAAA,CAAA6C,GAAA;MAAA,MAAA3C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA4C,mBAAA,CAAoB,WAAW,CAAC;IAAA,EAAC;IAGxCrD,EADF,CAAAC,cAAA,eAA4B,eACD;;IACvBD,EAAA,CAAAC,cAAA,eAMC;IAQCD,EAPA,CAAAkD,SAAA,gBAME,gBAOA;IAENlD,EADE,CAAAG,YAAA,EAAM,EACF;;IACNH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IACnCF,EADmC,CAAAG,YAAA,EAAK,EAClC;IAEJH,EADF,CAAAC,cAAA,eAA4B,gBACG;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,kBAA+C;;IAC7CD,EAAA,CAAAC,cAAA,eAUC;IACCD,EAAA,CAAAkD,SAAA,gBAME;IAIVlD,EAHM,CAAAG,YAAA,EAAM,EACC,EACL,EACF;IACNH,EAAA,CAAAiC,UAAA,KAAA2B,8CAAA,kBAGC;IAsBT5D,EAJM,CAAAG,YAAA,EAAM,EAEF,EACF,EACF;;;;IAnZMH,EAAA,CAAAuB,SAAA,IAA8B;IAC9BvB,EADA,CAAA6D,WAAA,yBAA8B,kCAAApD,MAAA,CAAAqD,6BAAA,OAG7B;IAMD9D,EAAA,CAAAuB,SAAA,GACF;IADEvB,EAAA,CAAA+D,kBAAA,MAAAtD,MAAA,CAAAqD,6BAAA,OACF;IAYF9D,EAAA,CAAAuB,SAAA,GAAmD;IAAnDvB,EAAA,CAAAgE,WAAA,cAAAvD,MAAA,CAAA2B,oBAAA,CAAAC,MAAA,KAAmD;IAyC3CrC,EAAA,CAAAuB,SAAA,IAIC;IAJDvB,EAAA,CAAA6D,WAAA,cAAApD,MAAA,CAAAwD,uBAAA,+CAIC;IAeNjE,EAAA,CAAAuB,SAAA,GAAuC;IAAvCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAwD,uBAAA,WAAuC;IAwB1CjE,EAAA,CAAAuB,SAAA,EAAsD;IAAtDvB,EAAA,CAAAgE,WAAA,cAAAvD,MAAA,CAAAgC,uBAAA,CAAAJ,MAAA,KAAsD;IA4E9CrC,EAAA,CAAAuB,SAAA,IAIC;IAJDvB,EAAA,CAAA6D,WAAA,cAAApD,MAAA,CAAAwD,uBAAA,kDAIC;IAeNjE,EAAA,CAAAuB,SAAA,GAA0C;IAA1CvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAwD,uBAAA,cAA0C;IA0B7CjE,EAAA,CAAAuB,SAAA,EAAkD;IAAlDvB,EAAA,CAAAgE,WAAA,cAAAvD,MAAA,CAAAoC,mBAAA,CAAAR,MAAA,KAAkD;IAoD1CrC,EAAA,CAAAuB,SAAA,IAIC;IAJDvB,EAAA,CAAA6D,WAAA,cAAApD,MAAA,CAAAwD,uBAAA,8CAIC;IAeNjE,EAAA,CAAAuB,SAAA,GAAsC;IAAtCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAwD,uBAAA,UAAsC;IAsBzCjE,EAAA,CAAAuB,SAAA,EAAsD;IAAtDvB,EAAA,CAAAgE,WAAA,cAAAvD,MAAA,CAAAwC,uBAAA,CAAAZ,MAAA,KAAsD;IAyC9CrC,EAAA,CAAAuB,SAAA,IAIC;IAJDvB,EAAA,CAAA6D,WAAA,cAAApD,MAAA,CAAAwD,uBAAA,kDAIC;IAeNjE,EAAA,CAAAuB,SAAA,GAA0C;IAA1CvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAwD,uBAAA,cAA0C;;;;;IAgC7CjE,EAFJ,CAAAC,cAAA,cAA4G,cAC9E,SACtB;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAE,MAAA,GACF;IAEJF,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IAHAH,EAAA,CAAAuB,SAAA,GACF;IADEvB,EAAA,CAAA+D,kBAAA,MAAAtD,MAAA,CAAAyD,mBAAA,CAAAC,QAAA,CAAAC,OAAA,IAAAC,IAAA,MACF;;;;;IAIFrE,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,yEAAkE;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAV3EH,EADF,CAAAC,cAAA,cAAkE,SAC5D;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAK;IASrBH,EARA,CAAAiC,UAAA,IAAAqC,mDAAA,kBAA4G,IAAAC,2DAAA,gCAAAvE,EAAA,CAAAwE,sBAAA,CAQ3E;IAGnCxE,EAAA,CAAAG,YAAA,EAAM;;;;;IAXEH,EAAA,CAAAuB,SAAA,GAA0D;IAAAvB,EAA1D,CAAAwB,UAAA,UAAAf,MAAA,CAAAyD,mBAAA,kBAAAzD,MAAA,CAAAyD,mBAAA,CAAAC,QAAA,kBAAA1D,MAAA,CAAAyD,mBAAA,CAAAC,QAAA,CAAAC,OAAA,kBAAA3D,MAAA,CAAAyD,mBAAA,CAAAC,QAAA,CAAAC,OAAA,CAAA/B,MAAA,MAA0D,aAAAoC,qBAAA,CAAuB;;;;;IAqB/EzE,EADF,CAAAC,cAAA,cAA6D,aACnD;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,GAChC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAD0BH,EAAA,CAAAuB,SAAA,GAChC;IADgCvB,EAAA,CAAA+D,kBAAA,MAAAW,aAAA,CAAAC,WAAA,MAChC;;;;;IAEE3E,EADF,CAAAC,cAAA,cAA8D,aACpD;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,GACpC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAD8BH,EAAA,CAAAuB,SAAA,GACpC;IADoCvB,EAAA,CAAA+D,kBAAA,MAAAW,aAAA,CAAAE,eAAA,MACpC;;;;;IANA5E,EAFJ,CAAAC,cAAA,cAAuH,cACzF,SACtB;IAAAD,EAAA,CAAAE,MAAA,GAA+C;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAIxDH,EAHA,CAAAiC,UAAA,IAAA4C,+DAAA,kBAA6D,IAAAC,+DAAA,kBAGC;IAG9D9E,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAE,MAAA,GACF;IAEJF,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;;IAXEH,EAAA,CAAAuB,SAAA,GAA+C;IAA/CvB,EAAA,CAAA8B,iBAAA,CAAA4C,aAAA,CAAAK,OAAA,eAAAC,KAAA,MAA+C;IAC7ChF,EAAA,CAAAuB,SAAA,EAA4B;IAA5BvB,EAAA,CAAAwB,UAAA,SAAAkD,aAAA,CAAAC,WAAA,CAA4B;IAG5B3E,EAAA,CAAAuB,SAAA,EAAgC;IAAhCvB,EAAA,CAAAwB,UAAA,SAAAkD,aAAA,CAAAE,eAAA,CAAgC;IAIpC5E,EAAA,CAAAuB,SAAA,GACF;IADEvB,EAAA,CAAA+D,kBAAA,MAAAW,aAAA,CAAAO,GAAA,MACF;;;;;IAZNjF,EAAA,CAAAC,cAAA,UAA6G;IAC3GD,EAAA,CAAAiC,UAAA,IAAAiD,yDAAA,kBAAuH;IAczHlF,EAAA,CAAAG,YAAA,EAAM;;;;IAdwBH,EAAA,CAAAuB,SAAA,EAAyD;IAAzDvB,EAAA,CAAAwB,UAAA,YAAAf,MAAA,CAAAyD,mBAAA,CAAAiB,aAAA,CAAAC,KAAA,CAAAC,YAAA,CAAyD;;;;;IAgBrFrF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,wEAAiE;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAlB1EH,EADF,CAAAC,cAAA,cAAwE,SAClE;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAiBrBH,EAhBA,CAAAiC,UAAA,IAAAqD,mDAAA,kBAA6G,IAAAC,2DAAA,gCAAAvF,EAAA,CAAAwE,sBAAA,CAgBzE;IAGtCxE,EAAA,CAAAG,YAAA,EAAM;;;;;IAnBEH,EAAA,CAAAuB,SAAA,GAA2E;IAAAvB,EAA3E,CAAAwB,UAAA,UAAAf,MAAA,CAAAyD,mBAAA,kBAAAzD,MAAA,CAAAyD,mBAAA,CAAAiB,aAAA,kBAAA1E,MAAA,CAAAyD,mBAAA,CAAAiB,aAAA,CAAAC,KAAA,kBAAA3E,MAAA,CAAAyD,mBAAA,CAAAiB,aAAA,CAAAC,KAAA,CAAAC,YAAA,kBAAA5E,MAAA,CAAAyD,mBAAA,CAAAiB,aAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAhD,MAAA,MAA2E,aAAAmD,yBAAA,CAA0B;;;;;IApB/GxF,EAAA,CAAAC,cAAA,cAAgE;IAkB9DD,EAhBA,CAAAiC,UAAA,IAAAwD,6CAAA,kBAAkE,IAAAC,6CAAA,kBAgBM;IAsB1E1F,EAAA,CAAAG,YAAA,EAAM;;;;IAtCEH,EAAA,CAAAuB,SAAA,EAAgC;IAAhCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAkB,SAAA,kBAAgC;IAgBhC3B,EAAA,CAAAuB,SAAA,EAAmC;IAAnCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAkB,SAAA,qBAAmC;;;ADhjBnD,WAAYgE,eAIX,gBAJD,UAAYA,eAAe;EACzBA,eAAA,6BAAyB;EACzBA,eAAA,uBAAmB;EACnBA,eAAA,2BAAuB;EAAA,OAHbA,eAAe;AAI3B,CAAC,CAJWA,eAAe,OAI1B;AAqCD,WAAaC,uBAAuB;EAA9B,MAAOA,uBAAuB;IA6GxBC,KAAA;IACAC,MAAA;IACAC,YAAA;IACAC,sBAAA;IACAC,YAAA;IACAC,aAAA;IACAC,WAAA;IACAC,oBAAA;IAnHVC,cAAc,GAAc,CAC1B;MAAEC,EAAE,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAgB,CAAE,EAC3C;MAAED,EAAE,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAc,CAAE,EAC7C;MAAED,EAAE,EAAE,cAAc;MAAEC,KAAK,EAAE,SAAS;MAAEC,QAAQ,EAAE;IAAI,CAAE,CACzD;IAED;IACAC,OAAO,GAAkB,IAAI;IAC7B9E,SAAS,GAAW,YAAY;IAChCC,SAAS,GAAW,OAAO;IAC3B8E,WAAW,GAAW,EAAE;IAGxBC,cAAc;IAEd;IACAC,YAAY,GAAkB,EAAE;IAChCC,gBAAgB,GAAW,CAAC;IAC5BC,gBAAgB;IAChBC,SAAS,GAAY,KAAK;IAC1BC,MAAM,GAAoBrB,eAAe,CAACsB,UAAU;IAEpD;IACAxF,YAAY,GAAkB,EAAE;IAChCC,gBAAgB,GAAY,KAAK;IACjCwF,SAAS,GAAG,EAAE;IAEd;IACAC,YAAY,GAAiB,EAAE;IAC/BjD,mBAAmB,GAAQ,IAAI,CAAC,CAAC;IAC1BkD,SAAS;IACTvF,QAAQ,GACb,gFAAgF;IAElF;IACAwF,kBAAkB,GAAgB,IAAI;IACtCC,kBAAkB,GAAY,KAAK;IACnCC,WAAW;IAEXC,kBAAkB,GAAGzH,WAAW,CAAC0H,kBAAkB,IAAI,KAAK;IAErDC,mBAAmB,GAAY,KAAK;IAC3CC,gBAAgB;IAERC,QAAQ,GAAG,IAAItI,OAAO,EAAQ;IACtCuI,WAAW,GAAW,gBAAgB;IACtCC,QAAQ,GAAa,CACnB;MAAExB,EAAE,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAS,CAAE,EAChC;MAAED,EAAE,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAW,CAAE,EAClC;MAAED,EAAE,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAc,CAAE,CACtC;IAEDwB,QAAQ,GAAG,KAAK;IAChBC,UAAU;IACVC,WAAW,GAAU,EAAE;IACvBC,WAAW,GAAG,KAAK;IACnBC,WAAW,GAAY,KAAK;IAC5BC,aAAa,GAAW,EAAE;IAC1BC,UAAU,GAAU,EAAE;IACtBC,aAAa,GAAU,EAAE;IACzBC,QAAQ,GAAG,CAAC;IACZC,SAAS,GAAG,KAAK;IACjBC,WAAW,GAAW,EAAE;IAExBC,eAAe,GAAa,EAAE;IAC9BC,iBAAiB,GAAW,CAAC;IAC7BC,WAAW,GAAW,UAAU;IAEhC;IACAC,oBAAoB,GAAY,KAAK;IACrCC,cAAc,GAAW,WAAW;IAEpC;IACAC,mBAAmB,GAAQ,IAAI;IAC/BC,eAAe,GAAU,EAAE;IAC3BC,WAAW,GAAU,EAAE;IACvBC,WAAW,GAAU,EAAE;IACvBC,cAAc,GAAW,EAAE;IAC3BC,iBAAiB,GAAW,EAAE;IAC9BC,yBAAyB,GAAW,EAAE;IACtCC,sBAAsB,GAAU,EAAE;IAClCC,eAAe,GAAa,EAAE;IAC9BC,wBAAwB,GAAG,KAAK;IAChCC,sBAAsB,GAAG,IAAInK,OAAO,EAAW;IAC/CoK,gBAAgB,GAAU,EAAE;IAC5BC,SAAS,GAAW,EAAE;IACtBC,aAAa,GAAqB,EAAE;IAEpC;IACA9F,6BAA6B,GAAW,CAAC;IACzC1B,oBAAoB,GAAU,EAAE;IAChCS,mBAAmB,GAAU,EAAE;IAC/BJ,uBAAuB,GAAU,EAAE;IACnCQ,uBAAuB,GAAU,EAAE;IACnC4G,kBAAkB,GAAU,EAAE;IAE9B;IACQC,sBAAsB,GAA+B;MAC3DC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE,IAAI;MACXC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE,IAAI;MACfC,IAAI,EAAE;KACP;IAED;IAEAC,YACUvE,KAAqB,EACrBC,MAAc,EACdC,YAAiC,EACjCC,sBAA8C,EAC9CC,YAAiC,EACjCC,aAA4B,EAC5BC,WAAwB,EACxBC,oBAA0C;MAP1C,KAAAP,KAAK,GAALA,KAAK;MACL,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,YAAY,GAAZA,YAAY;MACZ,KAAAC,sBAAsB,GAAtBA,sBAAsB;MACtB,KAAAC,YAAY,GAAZA,YAAY;MACZ,KAAAC,aAAa,GAAbA,aAAa;MACb,KAAAC,WAAW,GAAXA,WAAW;MACX,KAAAC,oBAAoB,GAApBA,oBAAoB;MAE5B,IAAI,CAACgB,SAAS,GAAG,IAAI,CAACjB,WAAW,CAACkE,KAAK,CAAC;QACtCC,gBAAgB,EAAE,CAAC,IAAI,CAAC;QACxBC,aAAa,EAAE,CAAC,KAAK;OACtB,CAAC;IACJ;IAEAC,QAAQA,CAAA;MACN,IAAI,CAACjD,WAAW,GAAGkD,MAAM,CAACC,UAAU,EAAE;MAEtC,IAAI,CAAC7E,KAAK,CAAC8E,MAAM,CAACC,SAAS,CAAED,MAAM,IAAI;QACrC,IAAI,CAAChJ,SAAS,GAAGgJ,MAAM,CAAC,MAAM,CAAC,IAAI,YAAY;MACjD,CAAC,CAAC;MAEF,IAAI,CAAC9E,KAAK,CAACgF,WAAW,CAACD,SAAS,CAAED,MAAM,IAAI;QAC1C,IAAIA,MAAM,CAAC,IAAI,CAAC,EAAE;UAChB,IAAI,CAAClE,OAAO,GAAGkE,MAAM,CAAC,IAAI,CAAC;UAC3B,IAAI,CAACG,aAAa,CAACH,MAAM,CAAC,IAAI,CAAC,CAAC;QAClC;MACF,CAAC,CAAC;MAEF;MACA,IAAI,CAAClJ,YAAY,GAAG,CAClB;QACEsJ,IAAI,EAAE,IAAI;QACV1G,IAAI,EAAE,kBAAkB,IAAI,CAACzC,SAAS,IAAI,YAAY;OACvD,CACF;IACH;IAEAoJ,WAAWA,CAAA;MACT,IAAI,CAACpD,QAAQ,CAACqD,IAAI,EAAE;MACpB,IAAI,CAACrD,QAAQ,CAACsD,QAAQ,EAAE;MACxB,IAAI,IAAI,CAACvD,gBAAgB,EAAE;QACzBwD,aAAa,CAAC,IAAI,CAACxD,gBAAgB,CAAC;MACtC;IACF;IAEAyD,WAAWA,CAACC,KAAoC;MAC9C,IAAI,CAACzC,WAAW,GAAGyC,KAAK,CAAC/E,EAAE;MAC3B,IAAI,CAACuB,WAAW,GAAGwD,KAAK,CAAC9E,KAAK;IAChC;IAEAuE,aAAaA,CAACrE,OAAe;MAC3B,IAAI,CAAC+B,SAAS,GAAG,IAAI;MAErB;MACA,IAAI,IAAI,CAAC7G,SAAS,KAAK,eAAe,EAAE;QACtC,IAAI,CAACoE,YAAY,CAACuF,gCAAgC,CAAC7E,OAAO,CAAC,CAACmE,SAAS,CAAC;UACpEK,IAAI,EAAG9G,QAAa,IAAI;YACtB,IAAI,CAACoH,uBAAuB,CAACpH,QAAQ,CAAC;UACxC,CAAC;UACDqH,KAAK,EAAGA,KAAU,IAAI;YACpBC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;YAC1D,IAAI,CAAChD,SAAS,GAAG,KAAK;UACxB;SACD,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAACzC,YAAY,CAAC2F,YAAY,CAACjF,OAAO,CAAC,CAACmE,SAAS,CAAC;UAChDK,IAAI,EAAG9G,QAAa,IAAI;YACtB,IAAI,CAACoH,uBAAuB,CAACpH,QAAQ,CAAC;UACxC,CAAC;UACDqH,KAAK,EAAGA,KAAU,IAAI;YACpBC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;YACvD,IAAI,CAAChD,SAAS,GAAG,KAAK;UACxB;SACD,CAAC;MACJ;IACF;IAEQ+C,uBAAuBA,CAACpH,QAAa;MAC3C,IAAI,CAACqE,SAAS,GAAG,KAAK;MAEtB;MACA,IAAImD,SAAS;MACb,IACExH,QAAQ,CAACyH,YAAY,IACrBC,KAAK,CAACC,OAAO,CAAC3H,QAAQ,CAACyH,YAAY,CAAC,IACpCzH,QAAQ,CAACyH,YAAY,CAACvJ,MAAM,GAAG,CAAC,EAChC;QACAsJ,SAAS,GAAGxH,QAAQ,CAACyH,YAAY,CAAC,CAAC,CAAC;MACtC,CAAC,MAAM,IAAIzH,QAAQ,CAACuC,WAAW,EAAE;QAC/BiF,SAAS,GAAGxH,QAAQ,CAACuC,WAAW;MAClC,CAAC,MAAM,IAAIvC,QAAQ,CAAC4H,IAAI,EAAE;QACxBJ,SAAS,GAAGxH,QAAQ,CAAC4H,IAAI;MAC3B,CAAC,MAAM;QACLJ,SAAS,GAAGxH,QAAQ;MACtB;MAEA,IAAIwH,SAAS,EAAE;QACb,IAAI,CAAC5C,mBAAmB,GAAG4C,SAAS;QACpC,IAAI,CAAC/J,SAAS,GAAG+J,SAAS,CAAC3J,IAAI,IAAI2J,SAAS,CAAC/J,SAAS,IAAI,OAAO;QACjE,IAAI,CAAC8E,WAAW,GAAGiF,SAAS,CAAChH,WAAW,IAAIgH,SAAS,CAACjF,WAAW,IAAI,EAAE;QAEvE;QACA,IAAI,IAAI,CAAC/E,SAAS,KAAK,YAAY,EAAE;UACnC;UACA,IAAI,CAACwH,cAAc,GAAGwC,SAAS,CAACK,WAAW,IAAIL,SAAS,CAAC3J,IAAI,IAAI,cAAc;UAE/E;UACA,IAAI,CAACoH,iBAAiB,GAAGuC,SAAS,CAACM,WAAW,IAAIN,SAAS,CAACK,WAAW,IAAIL,SAAS,CAAC3J,IAAI,IAAI,EAAE;UAE/F;UACA,IAAI2J,SAAS,CAACO,gBAAgB,EAAE;YAC9B,IAAI,CAAC7C,yBAAyB,GAAGsC,SAAS,CAACO,gBAAgB;UAC7D,CAAC,MAAM,IAAIP,SAAS,CAACM,WAAW,EAAE;YAChC,IAAI,CAAC5C,yBAAyB,GAAGsC,SAAS,CAACM,WAAW;UACxD,CAAC,MAAM,IAAIN,SAAS,CAACK,WAAW,EAAE;YAChC,IAAI,CAAC3C,yBAAyB,GAAGsC,SAAS,CAACK,WAAW;UACxD;QACF;QAEA;QACA,IAAI,IAAI,CAACvK,YAAY,CAACY,MAAM,GAAG,CAAC,EAAE;UAChC,IAAI,CAACZ,YAAY,CAAC,CAAC,CAAC,CAAC4C,IAAI,GAAG,kBAAkB,IAAI,CAACzC,SAAS,6BAA6B;QAC3F;QAEA;QACA,IAAI,CAACuK,cAAc,CAACR,SAAS,CAAC;MAChC;IACF;IAEQQ,cAAcA,CAACR,SAAc;MACnC;MACA,IAAI,CAACS,gCAAgC,CAACT,SAAS,CAAC;IAClD;IAEA7K,iBAAiBA,CAACuL,OAAe;MAC/B,IAAI,IAAI,CAAC1K,SAAS,KAAK,YAAY,EAAE;QACnC;QACA,IAAI,CAAC,IAAI,CAACoH,mBAAmB,KAAK,CAAC,IAAI,CAACI,cAAc,IAAI,IAAI,CAACA,cAAc,KAAK,SAAS,CAAC,EAAE;UAC5F,IAAI,CAACmD,cAAc,CACjB,+DAA+D,CAChE;UACD;QACF;QAEA,IAAIC,cAAc,GAAGF,OAAO;QAC5B,IAAI,IAAI,CAAC/C,sBAAsB,CAACjH,MAAM,GAAG,CAAC,EAAE;UAC1C,MAAMmK,SAAS,GAAG,IAAI,CAAClD,sBAAsB,CAC1CmD,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACC,YAAY,CAAC,CAChCC,IAAI,CAAC,IAAI,CAAC;UACbL,cAAc,GAAG,GAAGF,OAAO,0BAA0BG,SAAS,EAAE;QAClE;QAEA,IAAI,CAAC/K,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;UAAEsJ,IAAI,EAAE,MAAM;UAAE1G,IAAI,EAAEkI;QAAc,CAAE,CACvC;QACD,IAAI,CAAC7K,gBAAgB,GAAG,IAAI;QAE5B,MAAM4I,gBAAgB,GACpB,IAAI,CAAClD,SAAS,CAACyF,GAAG,CAAC,kBAAkB,CAAC,EAAEC,KAAK,IAAI,KAAK;QACxD,MAAMvC,aAAa,GAAG,IAAI,CAACnD,SAAS,CAACyF,GAAG,CAAC,eAAe,CAAC,EAAEC,KAAK,IAAI,KAAK;QAEzErB,OAAO,CAACsB,GAAG,CAAC,2CAA2C,EAAEzC,gBAAgB,EAAE,gBAAgB,EAAEC,aAAa,CAAC;QAE3G;QACA;QACA,MAAMyC,SAAS,GAAG,IAAI,CAACrD,SAAS,IACf,IAAI,CAACP,iBAAiB,IACtB,IAAI,CAACL,mBAAmB,EAAEkD,WAAW,IACrC,IAAI,CAAClD,mBAAmB,EAAEiD,WAAW,IACrC,IAAI,CAACjD,mBAAmB,EAAE/G,IAAI,IAC9B,IAAI,CAACmH,cAAc;QAEpC,IAAI8D,iBAAiB,GAAG,IAAI,CAAC5D,yBAAyB;QACtD,IAAI,CAAC4D,iBAAiB,EAAE;UACtB;UACA,IAAI,IAAI,CAAClE,mBAAmB,EAAEmD,gBAAgB,EAAE;YAC9Ce,iBAAiB,GAAG,IAAI,CAAClE,mBAAmB,CAACmD,gBAAgB;UAC/D,CAAC,MAAM;YACL,MAAMgB,OAAO,GAAG,IAAI,CAACC,qBAAqB,EAAE;YAC5C,MAAMC,eAAe,GAAG,IAAI,CAACrE,mBAAmB,EAAEkD,WAAW,IACtC,IAAI,CAAClD,mBAAmB,EAAEiD,WAAW,IACrC,IAAI,CAACjD,mBAAmB,EAAE/G,IAAI,IAC9BgL,SAAS;YAChCC,iBAAiB,GAAG,GAAGG,eAAe,GAAGF,OAAO,EAAE;UACpD;QACF;QAEA,IAAI,IAAI,CAAC5D,sBAAsB,CAACjH,MAAM,GAAG,CAAC,EAAE;UAC1C,IAAI,CAACgL,+BAA+B,CAClChB,OAAO,EACPW,SAAS,EACTC,iBAAiB,EACjB3C,gBAAgB,EAChBC,aAAa,CACd;UACD;QACF;QAEA,IAAI,CAAC+C,qBAAqB,CACxBjB,OAAO,EACPW,SAAS,EACTC,iBAAiB,EACjB3C,gBAAgB,EAChBC,aAAa,CACd;MACH,CAAC,MAAM,IAAI,IAAI,CAAC5I,SAAS,KAAK,eAAe,EAAE;QAC7C,IAAI,CAACD,gBAAgB,GAAG,IAAI;QAC5B,IAAI6L,OAAO,GAAG;UACZhG,WAAW,EAAE,IAAI,CAACA,WAAW;UAC7Bd,OAAO,EAAE+G,MAAM,CAAC,IAAI,CAAC/G,OAAO,CAAC;UAC7BgH,IAAI,EAAE,IAAI,CAACxH,YAAY,CAACyH,aAAa,EAAE,IAAI,uBAAuB;UAClEC,UAAU,EAAE;YAAEC,QAAQ,EAAEvB;UAAO;SAChC;QAED,IAAI,IAAI,CAAC/C,sBAAsB,CAACjH,MAAM,GAAG,CAAC,EAAE;UAC1C,MAAMwL,WAAW,GAAG,IAAI,CAACvE,sBAAsB,CAAC,CAAC,CAAC;UAClD,IAAIiD,cAAsB;UAC1B,IAAI,IAAI,CAACjD,sBAAsB,CAACjH,MAAM,GAAG,CAAC,EAAE;YAC1C,MAAMmK,SAAS,GAAG,IAAI,CAAClD,sBAAsB,CAC1CmD,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACC,YAAY,CAAC,CAChCC,IAAI,CAAC,IAAI,CAAC;YACbL,cAAc,GAAG,sBAAsBC,SAAS,EAAE;YAElD,IAAI,CAAC/K,YAAY,GAAG,CAAC;cAAEsJ,IAAI,EAAE,MAAM;cAAE1G,IAAI,EAAEkI;YAAc,CAAE,CAAC;UAC9D;UACA,IAAI,CAACvG,sBAAsB,CACxB8H,0BAA0B,CAACP,OAAO,EAAEM,WAAW,CAAC,CAChDE,IAAI,CACHtO,QAAQ,CAAC,MAAK;YACZ,IAAI,CAACiC,gBAAgB,GAAG,KAAK;YAC7B,IAAI,CAAC8H,wBAAwB,GAAG,KAAK;UACvC,CAAC,CAAC,EACFjK,SAAS,CAAC,IAAI,CAACkK,sBAAsB,CAAC,CACvC,CACAmB,SAAS,CAAC;YACTK,IAAI,EAAG+C,GAAG,IAAK,IAAI,CAACC,0BAA0B,CAACD,GAAG,EAAE3B,OAAO,CAAC;YAC5Db,KAAK,EAAG0C,GAAQ,IAAI;cAClB,IAAI,CAACzM,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;gBAAEsJ,IAAI,EAAE,MAAM;gBAAE1G,IAAI,EAAEgI;cAAO,CAAE,EAC/B;gBACEtB,IAAI,EAAE,IAAI;gBACV1G,IAAI,EACF6J,GAAG,EAAE1C,KAAK,EAAEa,OAAO,IACnB6B,GAAG,EAAE7B,OAAO,IACZ;eACH,CACF;YACH;WACD,CAAC;QACN,CAAC,MAAM;UACL,IAAI,CAACrG,sBAAsB,CACxBmI,kBAAkB,CAACZ,OAAO,CAAC,CAC3BQ,IAAI,CACHtO,QAAQ,CAAC,MAAK;YACZ,IAAI,CAACiC,gBAAgB,GAAG,KAAK;YAC7B,IAAI,CAAC8H,wBAAwB,GAAG,KAAK;UACvC,CAAC,CAAC,EACFjK,SAAS,CAAC,IAAI,CAACkK,sBAAsB,CAAC,CACvC,CACAmB,SAAS,CAAC;YACTK,IAAI,EAAG+C,GAAG,IAAK,IAAI,CAACC,0BAA0B,CAACD,GAAG,EAAE3B,OAAO,CAAC;YAC5Db,KAAK,EAAG0C,GAAQ,IAAI;cAClB,IAAI,CAACzM,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;gBAAEsJ,IAAI,EAAE,MAAM;gBAAE1G,IAAI,EAAEgI;cAAO,CAAE,EAC/B;gBACEtB,IAAI,EAAE,IAAI;gBACV1G,IAAI,EACF6J,GAAG,EAAE1C,KAAK,EAAEa,OAAO,IACnB6B,GAAG,EAAE7B,OAAO,IACZ;eACH,CACF;YACH;WACD,CAAC;QACN;MACF;IACF;IAEAzL,eAAeA,CAACmJ,MAAsB;MACpC,IAAI,CAAC7C,SAAS,GAAG6C,MAAM,CAAC/H,IAAI,IAAIoM,MAAM,CAACrE,MAAM,CAAC+C,KAAK,CAAC,IAAI,EAAE;IAC5D;IAEA9L,gCAAgCA,CAAC8L,KAAc;MAC7C;MACA,IAAI,CAAC1F,SAAS,CAACyF,GAAG,CAAC,kBAAkB,CAAC,EAAEwB,QAAQ,CAACvB,KAAK,CAAC;MAEvD;MACA;MACA,IAAI,CAACA,KAAK,EAAE;QACV,IAAI,CAACpD,gBAAgB,GAAG,EAAE;QAC1B+B,OAAO,CAACsB,GAAG,CAAC,6DAA6D,CAAC;MAC5E,CAAC,MAAM;QACLtB,OAAO,CAACsB,GAAG,CAAC,0DAA0D,CAAC;MACzE;IACF;IAEA7L,0BAA0BA,CAAC4L,KAAc;MACvC;MACA,IAAI,CAAC1F,SAAS,CAACyF,GAAG,CAAC,eAAe,CAAC,EAAEwB,QAAQ,CAACvB,KAAK,CAAC;MACpDrB,OAAO,CAACsB,GAAG,CAAC,wBAAwB,EAAED,KAAK,CAAC;IAC9C;IAEA1L,eAAeA,CAACkN,KAAY;MAC1B,IAAI,CAAClG,aAAa,GAAGkG,KAAK;MAC1B;MACA,IAAI,CAAChF,sBAAsB,GAAGgF,KAAK;IACrC;IAEAhN,mBAAmBA,CAAA;MACjB;IAAA;IAGFiN,QAAQA,CAAA;MACN;IAAA;IAGFC,aAAaA,CAACC,OAA8B;MAC1C;IAAA;IAGFC,mBAAmBA,CAACC,MAAiC;MACnD;IAAA;IAGFC,YAAYA,CAAA;MACV,IAAI,CAAC9I,MAAM,CAAC+I,QAAQ,CAAC,CAAC,eAAe,EAAE,IAAI,CAAClN,SAAS,CAAC,EAAE;QACtDkJ,WAAW,EAAE;UAAEvE,EAAE,EAAE,IAAI,CAACG,OAAO;UAAEqI,IAAI,EAAE;QAAM;OAC9C,CAAC;IACJ;IAEAC,SAASA,CAAA;MACP,IAAI,CAACjJ,MAAM,CAAC+I,QAAQ,CAAC,CAAC,eAAe,EAAE,IAAI,CAAClN,SAAS,CAAC,EAAE;QACtDkJ,WAAW,EAAE;UAAEvE,EAAE,EAAE,IAAI,CAACG,OAAO;UAAEqI,IAAI,EAAE;QAAM;OAC9C,CAAC;IACJ;IAEAE,oBAAoBA,CAAA;MAClB,IAAI,CAAClJ,MAAM,CAAC+I,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;IACzC;IAEAI,eAAeA,CAAA;MACb,IAAI,CAACpG,oBAAoB,GAAG,CAAC,IAAI,CAACA,oBAAoB;IACxD;IAEAqG,iBAAiBA,CAACC,GAAW;MAC3B,IAAI,CAACrG,cAAc,GAAGqG,GAAG;IAC3B;IAEA;IACA9L,mBAAmBA,CAAC+L,QAAgB;MAClC,IAAI,CAACtF,sBAAsB,CAACsF,QAAQ,CAAC,GACnC,CAAC,IAAI,CAACtF,sBAAsB,CAACsF,QAAQ,CAAC;IAC1C;IAEAnL,uBAAuBA,CAACmL,QAAgB;MACtC,OAAO,IAAI,CAACtF,sBAAsB,CAACsF,QAAQ,CAAC,IAAI,KAAK;IACvD;IAEA;IACQ9C,cAAcA,CAACD,OAAe;MACpC,IAAI,CAAC5K,YAAY,GAAG,CAAC,GAAG,IAAI,CAACA,YAAY,EAAE;QAAEsJ,IAAI,EAAE,IAAI;QAAE1G,IAAI,EAAEgI;MAAO,CAAE,CAAC;IAC3E;IAEQc,qBAAqBA,CAAA;MAC3B;MACA,OAAO,EAAE;IACX;IAEQkC,qBAAqBA,CAAA;MAC3B;MACA,OAAO,EAAE;IACX;IAEApB,0BAA0BA,CAAC9J,QAAa,EAAEkI,OAAe;MACvD,IAAI;QACF;QACA,IAAI,CAACnI,mBAAmB,GAAGC,QAAQ;QAEnC,MAAMmL,SAAS,GAAGnL,QAAQ,EAAEgB,aAAa,EAAEC,KAAK,EAAEmK,MAAM;QACxD,IAAIC,eAAe,GAAG,EAAE;QAExB,IAAIF,SAAS,EAAE;UACb;UACAE,eAAe,GAAGF,SAAS,CAACG,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC;QACnD,CAAC,MAAM;UACLD,eAAe,GAAGrL,QAAQ,EAAEgB,aAAa,EAAEuK,MAAM;QACnD;QAEA,IAAI,CAACjO,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;UAAEsJ,IAAI,EAAE,MAAM;UAAE1G,IAAI,EAAEgI;QAAO,CAAE,EAC/B;UAAEtB,IAAI,EAAE,IAAI;UAAE1G,IAAI,EAAEmL,eAAe,IAAI;QAAyB,CAAE,CACnE;MACH,CAAC,CAAC,OAAOtB,GAAQ,EAAE;QACjB,IAAI,CAACzM,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;UACEsJ,IAAI,EAAE,IAAI;UACV1G,IAAI,EAAE6J,GAAG,EAAE7B,OAAO,IAAI;SACvB,CACF;MACH;IACF;IAEQgB,+BAA+BA,CACrChB,OAAe,EACfyC,IAAY,EACZ7B,iBAAyB,EACzB3C,gBAAyB,EACzBC,aAAsB;MAEtB,MAAMoF,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/B,IAAI,CAACtG,sBAAsB,CAACuG,OAAO,CAAEC,QAAQ,IAAI;QAC/C,IAAIA,QAAQ,CAACpD,IAAI,EAAE;UACjBiD,QAAQ,CAACI,MAAM,CAAC,OAAO,EAAED,QAAQ,CAACpD,IAAI,CAAC;QACzC;MACF,CAAC,CAAC;MAEF,IAAIiD,QAAQ,CAACK,GAAG,CAAC,OAAO,CAAC,EAAE;QACzB,IAAI,CAAChK,sBAAsB,CACxBiK,gBAAgB,CAACN,QAAQ,CAAC,CAC1B5B,IAAI,CACHvO,SAAS,CAAE0Q,YAAY,IAAI;UACzB,MAAMC,WAAW,GACfD,YAAY,EAAEE,aAAa,EACvB3D,GAAG,CAAEtI,QAAa,IAAKA,QAAQ,CAACgM,WAAW,CAAC,EAC5CvD,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;UACtB,IAAI,CAACyD,8BAA8B,CACjChE,OAAO,EACPyC,IAAI,EACJ7B,iBAAiB,EACjB3C,gBAAgB,EAChBC,aAAa,EACb4F,WAAW,CACZ;UACD,OAAOxQ,EAAE,CAAC,IAAI,CAAC;QACjB,CAAC,CAAC,EACFD,UAAU,CAAE8L,KAAK,IAAI;UACnBC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,IAAI,CAAC8B,qBAAqB,CACxBjB,OAAO,EACPyC,IAAI,EACJ7B,iBAAiB,EACjB3C,gBAAgB,EAChBC,aAAa,CACd;UACD,OAAO5K,EAAE,CAAC,IAAI,CAAC;QACjB,CAAC,CAAC,CACH,CACAiL,SAAS,EAAE;MAChB,CAAC,MAAM;QACL,IAAI,CAAC0C,qBAAqB,CACxBjB,OAAO,EACPyC,IAAI,EACJ7B,iBAAiB,EACjB3C,gBAAgB,EAChBC,aAAa,CACd;MACH;IACF;IAEQ+C,qBAAqBA,CAC3BjB,OAAe,EACfyC,IAAY,EACZ7B,iBAAyB,EACzB3C,gBAAyB,EACzBC,aAAsB;MAEtBkB,OAAO,CAACsB,GAAG,CAAC,sBAAsB,EAAE;QAClCV,OAAO;QACPyC,IAAI;QACJ7B,iBAAiB;QACjB3C,gBAAgB;QAChBC,aAAa;QACb+F,wBAAwB,EAAE,IAAI,CAAC5G,gBAAgB,CAACrH;OACjD,CAAC;MAEF,IAAIiI,gBAAgB,EAAE;QACpB,IAAI,CAACZ,gBAAgB,CAAC6G,IAAI,CAAC;UAAEC,OAAO,EAAEnE,OAAO;UAAEoE,IAAI,EAAE;QAAM,CAAE,CAAC;MAChE;MAEA,MAAMlD,OAAO,GAAGjD,gBAAgB,GAAG,IAAI,CAACZ,gBAAgB,GAAG2C,OAAO;MAClE,MAAM;QAAEqE;MAAO,CAAE,GAAG,IAAI,CAACrB,qBAAqB,EAAE;MAEhD5D,OAAO,CAACsB,GAAG,CAAC,2BAA2B,EAAEQ,OAAO,CAAC;MAEjD,IAAI,CAACvH,sBAAsB,CACxB2K,cAAc,CACbpD,OAAO,EACPuB,IAAI,EACJxE,gBAAgB,EAChBC,aAAa,EACb,IAAI,CAAChB,eAAe,EACpB0D,iBAAiB,EACjB,EAAE,EACFyD,OAAO,CACR,CACA3C,IAAI,CACHtO,QAAQ,CAAC,MAAK;QACZ,IAAI,CAACiC,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAAC8H,wBAAwB,GAAG,KAAK;MACvC,CAAC,CAAC,EACFjK,SAAS,CAAC,IAAI,CAACkK,sBAAsB,CAAC,CACvC,CACAmB,SAAS,CAAC;QACTK,IAAI,EAAG2F,iBAAsB,IAAI;UAC/B;UACA,IAAI,CAAC1M,mBAAmB,GAAG0M,iBAAiB;UAE5C,IACEA,iBAAiB,EAAEzM,QAAQ,IAC3ByM,iBAAiB,EAAEzM,QAAQ,EAAEC,OAAO,EACpC;YACA,MAAMyM,cAAc,GAAGD,iBAAiB,CAACzM,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,CAACC,IAAI;YACjE,IAAI,CAAC5C,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;cAAEsJ,IAAI,EAAE,IAAI;cAAE1G,IAAI,EAAEwM;YAAc,CAAE,CACrC;YACD,IAAIvG,gBAAgB,EAAE;cACpB,IAAI,CAACZ,gBAAgB,CAAC6G,IAAI,CAAC;gBACzBC,OAAO,EAAEK,cAAc;gBACvBJ,IAAI,EAAE;eACP,CAAC;YACJ;UACF,CAAC,MAAM;YACLhF,OAAO,CAACqF,IAAI,CAAC,iCAAiC,EAAEF,iBAAiB,CAAC;YAClE,IAAI,CAACtE,cAAc,CACjB,+CAA+C,CAChD;UACH;QACF,CAAC;QACDd,KAAK,EAAGA,KAAU,IAAI;UACpBC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;UAClC,MAAMuF,YAAY,GAChBvF,KAAK,EAAEA,KAAK,EAAEa,OAAO,IACrB,kDAAkD;UACpD,IAAI,CAACC,cAAc,CAACyE,YAAY,CAAC;UACjC,IAAIzG,gBAAgB,IAAI,IAAI,CAACZ,gBAAgB,CAACrH,MAAM,GAAG,CAAC,EAAE;YACxD,IAAI,CAACqH,gBAAgB,CAACsH,GAAG,EAAE;UAC7B;QACF;OACD,CAAC;IACN;IAEQX,8BAA8BA,CACpChE,OAAe,EACfyC,IAAY,EACZ7B,iBAAyB,EACzB3C,gBAAyB,EACzBC,aAAsB,EACtB0G,YAAoB;MAEpB,IAAI3G,gBAAgB,EAAE;QACpB,IAAI,CAACZ,gBAAgB,CAAC6G,IAAI,CAAC;UAAEC,OAAO,EAAEnE,OAAO;UAAEoE,IAAI,EAAE;QAAM,CAAE,CAAC;MAChE;MACA,MAAMlD,OAAO,GAAGjD,gBAAgB,GAAG,IAAI,CAACZ,gBAAgB,GAAG2C,OAAO;MAClE,MAAM;QAAEqE;MAAO,CAAE,GAAG,IAAI,CAACrB,qBAAqB,EAAE;MAEhD,IAAI,CAACrJ,sBAAsB,CACxB2K,cAAc,CACbpD,OAAO,EACPuB,IAAI,EACJxE,gBAAgB,EAChBC,aAAa,EACb,IAAI,CAAChB,eAAe,EACpB0D,iBAAiB,EACjBgE,YAAY,EACZP,OAAO,CACR,CACA3C,IAAI,CACHtO,QAAQ,CAAC,MAAK;QACZ,IAAI,CAACiC,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAAC8H,wBAAwB,GAAG,KAAK;MACvC,CAAC,CAAC,EACFjK,SAAS,CAAC,IAAI,CAACkK,sBAAsB,CAAC,CACvC,CACAmB,SAAS,CAAC;QACTK,IAAI,EAAG2F,iBAAsB,IAAI;UAC/B,IACEA,iBAAiB,EAAEzM,QAAQ,IAC3ByM,iBAAiB,EAAEzM,QAAQ,EAAEC,OAAO,EACpC;YACA,MAAMyM,cAAc,GAAGD,iBAAiB,CAACzM,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,CAACC,IAAI;YACjE,IAAI,CAAC5C,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;cAAEsJ,IAAI,EAAE,IAAI;cAAE1G,IAAI,EAAEwM;YAAc,CAAE,CACrC;YACD,IAAIvG,gBAAgB,EAAE;cACpB,IAAI,CAACZ,gBAAgB,CAAC6G,IAAI,CAAC;gBACzBC,OAAO,EAAEK,cAAc;gBACvBJ,IAAI,EAAE;eACP,CAAC;YACJ;UACF,CAAC,MAAM;YACLhF,OAAO,CAACqF,IAAI,CAAC,iCAAiC,EAAEF,iBAAiB,CAAC;YAClE,IAAI,CAACtE,cAAc,CACjB,+CAA+C,CAChD;UACH;QACF,CAAC;QACDd,KAAK,EAAGA,KAAU,IAAI;UACpBC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;UAClC,MAAMuF,YAAY,GAChBvF,KAAK,EAAEA,KAAK,EAAEa,OAAO,IACrB,kDAAkD;UACpD,IAAI,CAACC,cAAc,CAACyE,YAAY,CAAC;UACjC,IAAIzG,gBAAgB,IAAI,IAAI,CAACZ,gBAAgB,CAACrH,MAAM,GAAG,CAAC,EAAE;YACxD,IAAI,CAACqH,gBAAgB,CAACsH,GAAG,EAAE;UAC7B;QACF;OACD,CAAC;IACN;IAEA;IACQ5E,gCAAgCA,CAACT,SAAc;MACrD,IAAI,CAACA,SAAS,EAAE;QACdF,OAAO,CAACqF,IAAI,CAAC,sCAAsC,CAAC;QACpD;MACF;MAEArF,OAAO,CAACsB,GAAG,CAAC,qCAAqC,EAAEpB,SAAS,CAAC;MAC7DF,OAAO,CAACsB,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAACpL,SAAS,CAAC;MACpD8J,OAAO,CAACsB,GAAG,CAAC,4BAA4B,EAAEmE,MAAM,CAACC,IAAI,CAACxF,SAAS,CAAC,CAAC;MAEjE;MACA,IAAI,CAAC3C,eAAe,GAAG,EAAE;MACzB,IAAI,CAACC,WAAW,GAAG,EAAE;MAErB,IAAImI,WAAW,GAAG,CAAC;MAEnB;MACA,IAAI,IAAI,CAACzP,SAAS,KAAK,YAAY,EAAE;QACnC,IAAI,CAAC0P,6BAA6B,CAAC1F,SAAS,EAAEyF,WAAW,CAAC;MAC5D,CAAC,MAAM,IAAI,IAAI,CAACzP,SAAS,KAAK,eAAe,EAAE;QAC7C,IAAI,CAAC2P,gCAAgC,CAAC3F,SAAS,EAAEyF,WAAW,CAAC;MAC/D;MAEA3F,OAAO,CAACsB,GAAG,CAAC,4BAA4B,EAAE;QACxC/D,eAAe,EAAE,IAAI,CAACA,eAAe;QACrCC,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7BsI,UAAU,EAAE,IAAI,CAACvI,eAAe,CAAC3G;OAClC,CAAC;IACJ;IAEQgP,6BAA6BA,CACnC1F,SAAc,EACdyF,WAAmB;MAEnB3F,OAAO,CAACsB,GAAG,CAAC,gDAAgD,EAAE;QAC5DyE,MAAM,EAAE7F,SAAS,CAAC6F,MAAM;QACxBC,YAAY,EAAE9F,SAAS,CAAC6F,MAAM,EAAEnP,MAAM;QACtC2J,WAAW,EAAEL,SAAS,CAACK,WAAW;QAClC0F,cAAc,EAAE/F,SAAS,CAAC+F;OAC3B,CAAC;MAEF;MACA,IAAI,CAACtP,oBAAoB,GAAG,EAAE;MAC9B,IAAI,CAACS,mBAAmB,GAAG,EAAE;MAC7B,IAAI,CAACJ,uBAAuB,GAAG,EAAE;MACjC,IAAI,CAACQ,uBAAuB,GAAG,EAAE;MAEjC;MACA,IAAI0I,SAAS,CAACK,WAAW,IAAIL,SAAS,CAAC+F,cAAc,EAAE;QACrD,IAAI,CAACtP,oBAAoB,CAACmO,IAAI,CAAC;UAC7BjK,EAAE,EAAE,UAAU8K,WAAW,EAAE,EAAE;UAC7BpP,IAAI,EACF2J,SAAS,CAACK,WAAW,IAAIL,SAAS,CAAC+F,cAAc,IAAI,eAAe;UACtEC,IAAI,EAAE;SACP,CAAC;MACJ;MAEA;MACA,IAAIhG,SAAS,CAAC6F,MAAM,IAAI3F,KAAK,CAACC,OAAO,CAACH,SAAS,CAAC6F,MAAM,CAAC,EAAE;QACvD/F,OAAO,CAACsB,GAAG,CACT,yCAAyC,EACzCpB,SAAS,CAAC6F,MAAM,CAACnP,MAAM,CACxB;QAEDsJ,SAAS,CAAC6F,MAAM,CAAC3B,OAAO,CAAC,CAAC+B,QAAa,EAAEC,aAAqB,KAAI;UAChEpG,OAAO,CAACsB,GAAG,CAAC,eAAe8E,aAAa,GAAG,EAAED,QAAQ,CAAC;UAEtD,IAAIA,QAAQ,CAACJ,MAAM,IAAI3F,KAAK,CAACC,OAAO,CAAC8F,QAAQ,CAACJ,MAAM,CAAC,EAAE;YACrD/F,OAAO,CAACsB,GAAG,CACT,eAAe8E,aAAa,QAAQD,QAAQ,CAACJ,MAAM,CAACnP,MAAM,eAAe,CAC1E;YAEDuP,QAAQ,CAACJ,MAAM,CAAC3B,OAAO,CAAC,CAACiC,UAAe,EAAEC,SAAiB,KAAI;cAC7DtG,OAAO,CAACsB,GAAG,CAAC,kBAAkB8E,aAAa,IAAIE,SAAS,GAAG,EAAE;gBAC3DC,SAAS,EAAEF,UAAU,CAACE,SAAS;gBAC/BC,WAAW,EAAEH,UAAU,CAACG,WAAW;gBACnCN,IAAI,EAAE,OAAOG,UAAU,CAACG;eACzB,CAAC;cAEF;cACA,IAAIH,UAAU,CAACE,SAAS,KAAK,OAAO,IAAIF,UAAU,CAACG,WAAW,EAAE;gBAC9DxG,OAAO,CAACsB,GAAG,CAAC,sBAAsB,EAAE+E,UAAU,CAACG,WAAW,CAAC;gBAC3D,IAAI,CAACpP,mBAAmB,CAAC0N,IAAI,CAAC;kBAC5BjK,EAAE,EAAE,SAAS8K,WAAW,EAAE,EAAE;kBAC5BpP,IAAI,EAAE,UAAU8P,UAAU,CAACG,WAAW,EAAE;kBACxCN,IAAI,EAAE;iBACP,CAAC;cACJ;cAEA;cACA,IACEG,UAAU,CAACE,SAAS,KAAK,wBAAwB,IACjDF,UAAU,CAACG,WAAW,EACtB;gBACAxG,OAAO,CAACsB,GAAG,CACT,+BAA+B,EAC/B+E,UAAU,CAACG,WAAW,CACvB;gBACD,MAAMC,OAAO,GAAGJ,UAAU,CAACG,WAAW,CAACE,QAAQ,EAAE;gBACjD,MAAMC,KAAK,GAAGF,OAAO,CAClBG,KAAK,CAAC,GAAG,CAAC,CACV5F,GAAG,CAAEnG,EAAU,IAAKA,EAAE,CAACgM,IAAI,EAAE,CAAC,CAC9BC,MAAM,CAAEjM,EAAU,IAAKA,EAAE,CAAC;gBAE7B8L,KAAK,CAACvC,OAAO,CAAE2C,IAAY,IAAI;kBAC7B,IAAI,CAAC/P,uBAAuB,CAAC8N,IAAI,CAAC;oBAChCjK,EAAE,EAAE,aAAa8K,WAAW,EAAE,EAAE;oBAChCpP,IAAI,EAAE,mBAAmBwQ,IAAI,EAAE;oBAC/Bb,IAAI,EAAE;mBACP,CAAC;gBACJ,CAAC,CAAC;cACJ;cAEA;cACA,IACEG,UAAU,CAACE,SAAS,KAAK,mBAAmB,KAC3CF,UAAU,CAACG,WAAW,KAAK,MAAM,IAChCH,UAAU,CAACG,WAAW,KAAK,IAAI,CAAC,EAClC;gBACAxG,OAAO,CAACsB,GAAG,CACT,iEAAiE,CAClE;gBACD;gBACA,IAAI,IAAI,CAAC9J,uBAAuB,CAACZ,MAAM,KAAK,CAAC,EAAE;kBAC7C,IAAI,CAACY,uBAAuB,CAACsN,IAAI,CAAC;oBAChCjK,EAAE,EAAE,aAAa8K,WAAW,EAAE,EAAE;oBAChCpP,IAAI,EAAE,oBAAoB;oBAC1B2P,IAAI,EAAE;mBACP,CAAC;gBACJ;cACF;cAEA;cACA,IACEG,UAAU,CAACE,SAAS,IACpBF,UAAU,CAACE,SAAS,CAACS,UAAU,CAAC,YAAY,CAAC,IAC7CX,UAAU,CAACE,SAAS,KAAK,mBAAmB,KAC3CF,UAAU,CAACG,WAAW,KAAK,MAAM,IAChCH,UAAU,CAACG,WAAW,KAAK,IAAI,IAC/BH,UAAU,CAACG,WAAW,KAAK,OAAO,CAAC,EACrC;gBACAxG,OAAO,CAACsB,GAAG,CAAC,6BAA6B,EAAE;kBACzC2F,GAAG,EAAEZ,UAAU,CAACE,SAAS;kBACzBlF,KAAK,EAAEgF,UAAU,CAACG;iBACnB,CAAC;gBAEF,IAAIU,aAAa,GAAGb,UAAU,CAACE,SAAS;gBACxC,IAAIW,aAAa,CAACF,UAAU,CAAC,YAAY,CAAC,EAAE;kBAC1CE,aAAa,GAAGA,aAAa,CAC1BlD,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CACzBA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;gBACvB;gBAEA,IAAI,CAACxM,uBAAuB,CAACsN,IAAI,CAAC;kBAChCjK,EAAE,EAAE,aAAa8K,WAAW,EAAE,EAAE;kBAChCpP,IAAI,EAAE,cAAc2Q,aAAa,EAAE;kBACnChB,IAAI,EAAE;iBACP,CAAC;cACJ;cAEA;cACA,IACEG,UAAU,CAACE,SAAS,KAAK,WAAW,IACpCF,UAAU,CAACG,WAAW,IACtBH,UAAU,CAACG,WAAW,KAAK,OAAO,EAClC;gBACAxG,OAAO,CAACsB,GAAG,CACT,mCAAmC,EACnC+E,UAAU,CAACG,WAAW,CACvB;gBACD,IAAI,CAAChP,uBAAuB,CAACsN,IAAI,CAAC;kBAChCjK,EAAE,EAAE,aAAa8K,WAAW,EAAE,EAAE;kBAChCpP,IAAI,EAAE,cAAc8P,UAAU,CAACG,WAAW,EAAE;kBAC5CN,IAAI,EAAE;iBACP,CAAC;cACJ;YACF,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ;MAEAlG,OAAO,CAACsB,GAAG,CAAC,2BAA2B,EAAE;QACvC6F,WAAW,EAAE,IAAI,CAACxQ,oBAAoB;QACtCyQ,UAAU,EAAE,IAAI,CAAChQ,mBAAmB;QACpCiQ,cAAc,EAAE,IAAI,CAACrQ,uBAAuB;QAC5CsQ,cAAc,EAAE,IAAI,CAAC9P;OACtB,CAAC;MAEF;MACA,MAAM+P,aAAa,GAAG,CAAC,CAAC,CAAC;MACzB,MAAMC,eAAe,GACnB,IAAI,CAAC7Q,oBAAoB,CAACC,MAAM,GAAG,IAAI,CAACQ,mBAAmB,CAACR,MAAM;MACpE,IAAI,CAACyB,6BAA6B,GAAGoP,IAAI,CAACC,KAAK,CAC5CF,eAAe,GAAGD,aAAa,GAAI,GAAG,CACxC;IACH;IAEQ1B,gCAAgCA,CACtC3F,SAAc,EACdyF,WAAmB;MAEnB;MACA,IAAI,CAAChP,oBAAoB,GAAG,EAAE;MAC9B,IAAI,CAACS,mBAAmB,GAAG,EAAE;MAC7B,IAAI,CAACJ,uBAAuB,GAAG,EAAE;MACjC,IAAI,CAACoH,kBAAkB,GAAG,EAAE;MAE5B;MACA,IAAI8B,SAAS,CAACyH,QAAQ,IAAIzH,SAAS,CAACyH,QAAQ,CAAC/Q,MAAM,GAAG,CAAC,EAAE;QACvD,MAAMgR,YAAY,GAAG1H,SAAS,CAACyH,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,IAAIC,YAAY,CAACC,IAAI,EAAE;UACrB,IAAI,CAAClR,oBAAoB,CAACmO,IAAI,CAAC;YAC7BjK,EAAE,EAAE,UAAU8K,WAAW,EAAE,EAAE;YAC7BpP,IAAI,EAAEqR,YAAY,CAACC,IAAI,IAAI,oBAAoB;YAC/C3B,IAAI,EAAE;WACP,CAAC;QACJ;MACF;MAEA;MACA,IAAIhG,SAAS,CAAC3B,KAAK,IAAI2B,SAAS,CAAC4H,SAAS,EAAE;QAC1C,IAAI,CAAC1Q,mBAAmB,CAAC0N,IAAI,CAAC;UAC5BjK,EAAE,EAAE,SAAS8K,WAAW,EAAE,EAAE;UAC5BpP,IAAI,EAAE2J,SAAS,CAAC3B,KAAK,IAAI2B,SAAS,CAAC4H,SAAS,IAAI,eAAe;UAC/D5B,IAAI,EAAE;SACP,CAAC;MACJ;MAEA;MACA,IAAIhG,SAAS,CAACyH,QAAQ,IAAIzH,SAAS,CAACyH,QAAQ,CAAC/Q,MAAM,GAAG,CAAC,EAAE;QACvDsJ,SAAS,CAACyH,QAAQ,CAACvD,OAAO,CAAEwD,YAAiB,IAAI;UAC/C,IAAIA,YAAY,CAACG,KAAK,IAAIH,YAAY,CAACG,KAAK,CAACnR,MAAM,GAAG,CAAC,EAAE;YACvDgR,YAAY,CAACG,KAAK,CAAC3D,OAAO,CAAE1F,IAAS,IAAI;cACvC,IAAI,CAACN,kBAAkB,CAAC0G,IAAI,CAAC;gBAC3BjK,EAAE,EAAE,QAAQ8K,WAAW,EAAE,EAAE;gBAC3BpP,IAAI,EAAEmI,IAAI,CAACnI,IAAI,IAAImI,IAAI,CAACsJ,QAAQ,IAAI,MAAM;gBAC1C9B,IAAI,EAAE;eACP,CAAC;YACJ,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ;MAEA;MACA,IAAIhG,SAAS,CAAC+H,aAAa,IAAI/H,SAAS,CAAC+H,aAAa,CAACrR,MAAM,GAAG,CAAC,EAAE;QACjEsJ,SAAS,CAAC+H,aAAa,CAAC7D,OAAO,CAAE8D,EAAO,IAAI;UAC1C,IAAI,CAAClR,uBAAuB,CAAC8N,IAAI,CAAC;YAChCjK,EAAE,EAAE,aAAa8K,WAAW,EAAE,EAAE;YAChCpP,IAAI,EAAE2R,EAAE,CAAC3R,IAAI,IAAI2R,EAAE,CAAChH,YAAY,IAAI,gBAAgB;YACpDgF,IAAI,EAAE;WACP,CAAC;QACJ,CAAC,CAAC;MACJ;MAEA;MACA,MAAMqB,aAAa,GAAG,CAAC,CAAC,CAAC;MACzB,MAAMC,eAAe,GACnB,IAAI,CAAC7Q,oBAAoB,CAACC,MAAM,GAAG,IAAI,CAACQ,mBAAmB,CAACR,MAAM;MACpE,IAAI,CAACyB,6BAA6B,GAAGoP,IAAI,CAACC,KAAK,CAC5CF,eAAe,GAAGD,aAAa,GAAI,GAAG,CACxC;IACH;;uCA59BWpN,uBAAuB,EAAA5F,EAAA,CAAA4T,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA9T,EAAA,CAAA4T,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA/T,EAAA,CAAA4T,iBAAA,CAAAI,EAAA,CAAAC,mBAAA,GAAAjU,EAAA,CAAA4T,iBAAA,CAAAM,EAAA,CAAAC,sBAAA,GAAAnU,EAAA,CAAA4T,iBAAA,CAAAQ,EAAA,CAAAC,mBAAA,GAAArU,EAAA,CAAA4T,iBAAA,CAAAU,EAAA,CAAAC,aAAA,GAAAvU,EAAA,CAAA4T,iBAAA,CAAAY,EAAA,CAAAC,WAAA,GAAAzU,EAAA,CAAA4T,iBAAA,CAAAc,EAAA,CAAAC,oBAAA;IAAA;;YAAvB/O,uBAAuB;MAAAgP,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBAavBlV,iCAAiC;;;;;;;;;;;;UCzFxCG,EAJN,CAAAC,cAAA,aAAuC,aAEZ,aACD,gBAC+C;UAAvCD,EAAA,CAAAI,UAAA,mBAAA6U,yDAAA;YAAA,OAASD,GAAA,CAAApG,YAAA,EAAc;UAAA,EAAC;UAClD5O,EAAA,CAAAkD,SAAA,kBAIY;UACZlD,EAAA,CAAAC,cAAA,cAAyB;UAAAD,EAAA,CAAAE,MAAA,GAAe;UAG9CF,EAH8C,CAAAG,YAAA,EAAO,EACxC,EACL,EACF;UAOAH,EAJN,CAAAC,cAAA,aAA0B,aAEyC,cACrC,kBAC+C;UAA1CD,EAAA,CAAAI,UAAA,mBAAA8U,0DAAA;YAAA,OAASF,GAAA,CAAA/F,eAAA,EAAiB;UAAA,EAAC;UACtDjP,EAAA,CAAAkD,SAAA,oBAKW;UACblD,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAiC,UAAA,KAAAkT,0CAAA,qBAKC;UAGHnV,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAiC,UAAA,KAAAmT,uCAAA,mBAAyD;UAuB3DpV,EAAA,CAAAG,YAAA,EAAM;UAOAH,EAJN,CAAAC,cAAA,eAAyB,eAEG,eACI,kBAKzB;UADCD,EAAA,CAAAI,UAAA,mBAAAiV,0DAAA;YAAA,OAASL,GAAA,CAAA9F,iBAAA,CAAkB,WAAW,CAAC;UAAA,EAAC;UAExClP,EAAA,CAAAE,MAAA,mBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAIC;UADCD,EAAA,CAAAI,UAAA,mBAAAkV,0DAAA;YAAA,OAASN,GAAA,CAAA9F,iBAAA,CAAkB,QAAQ,CAAC;UAAA,EAAC;UAErClP,EAAA,CAAAE,MAAA,sBACF;UAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;UAENH,EAAA,CAAAC,cAAA,eAA2B;UAgfzBD,EA9eA,CAAAiC,UAAA,KAAAsT,uCAAA,oBAAsE,KAAAC,uCAAA,kBA8eN;UA4CxExV,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACF;;;UAvmB2BH,EAAA,CAAAuB,SAAA,GAAe;UAAfvB,EAAA,CAAA8B,iBAAA,CAAAkT,GAAA,CAAApT,SAAA,CAAe;UAQpB5B,EAAA,CAAAuB,SAAA,GAAwC;UAAxCvB,EAAA,CAAAgE,WAAA,cAAAgR,GAAA,CAAAnM,oBAAA,CAAwC;UAIxD7I,EAAA,CAAAuB,SAAA,GAAgE;UAAhEvB,EAAA,CAAAwB,UAAA,aAAAwT,GAAA,CAAAnM,oBAAA,gCAAgE;UAQjE7I,EAAA,CAAAuB,SAAA,EAA2B;UAA3BvB,EAAA,CAAAwB,UAAA,UAAAwT,GAAA,CAAAnM,oBAAA,CAA2B;UAQJ7I,EAAA,CAAAuB,SAAA,EAA2B;UAA3BvB,EAAA,CAAAwB,UAAA,UAAAwT,GAAA,CAAAnM,oBAAA,CAA2B;UAgCjD7I,EAAA,CAAAuB,SAAA,GAA+C;UAA/CvB,EAAA,CAAAgE,WAAA,WAAAgR,GAAA,CAAAlM,cAAA,iBAA+C;UAO/C9I,EAAA,CAAAuB,SAAA,GAA4C;UAA5CvB,EAAA,CAAAgE,WAAA,WAAAgR,GAAA,CAAAlM,cAAA,cAA4C;UAU1C9I,EAAA,CAAAuB,SAAA,GAAoC;UAApCvB,EAAA,CAAAwB,UAAA,SAAAwT,GAAA,CAAAlM,cAAA,iBAAoC;UA8epC9I,EAAA,CAAAuB,SAAA,EAAiC;UAAjCvB,EAAA,CAAAwB,UAAA,SAAAwT,GAAA,CAAAlM,cAAA,cAAiC;;;qBD/f3CzJ,YAAY,EAAAoW,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZ/V,WAAW,EACXC,iCAAiC,EAGjCC,aAAa;MAAA8V,MAAA;IAAA;;SAKJhQ,uBAAuB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}