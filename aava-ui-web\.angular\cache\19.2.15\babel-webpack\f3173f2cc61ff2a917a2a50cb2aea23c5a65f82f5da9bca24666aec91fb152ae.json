{"ast": null, "code": "import { bootstrapApplication } from '@angular/platform-browser';\nimport { appConfig } from './app/app.config';\nimport { AppComponent } from './app/app.component';\nbootstrapApplication(AppComponent, appConfig).catch(err => console.error(err));", "map": {"version": 3, "names": ["bootstrapApplication", "appConfig", "AppComponent", "catch", "err", "console", "error"], "sources": ["C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\bootstrap.ts"], "sourcesContent": ["import { bootstrapApplication } from '@angular/platform-browser';\r\nimport { appConfig } from './app/app.config';\r\nimport { AppComponent } from './app/app.component';\r\n\r\nbootstrapApplication(AppComponent, appConfig)\r\n  .catch((err) => console.error(err));\r\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,YAAY,QAAQ,qBAAqB;AAElDF,oBAAoB,CAACE,YAAY,EAAED,SAAS,CAAC,CAC1CE,KAAK,CAAEC,GAAG,IAAKC,OAAO,CAACC,KAAK,CAACF,GAAG,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}