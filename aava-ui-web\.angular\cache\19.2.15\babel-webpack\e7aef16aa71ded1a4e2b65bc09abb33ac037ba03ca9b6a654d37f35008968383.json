{"ast": null, "code": "import dice from \"./dice.js\";\nimport slice from \"./slice.js\";\nexport default function (parent, x0, y0, x1, y1) {\n  (parent.depth & 1 ? slice : dice)(parent, x0, y0, x1, y1);\n}", "map": {"version": 3, "names": ["dice", "slice", "parent", "x0", "y0", "x1", "y1", "depth"], "sources": ["C:/console/aava-ui-web/node_modules/d3-hierarchy/src/treemap/sliceDice.js"], "sourcesContent": ["import dice from \"./dice.js\";\nimport slice from \"./slice.js\";\n\nexport default function(parent, x0, y0, x1, y1) {\n  (parent.depth & 1 ? slice : dice)(parent, x0, y0, x1, y1);\n}\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,WAAW;AAC5B,OAAOC,KAAK,MAAM,YAAY;AAE9B,eAAe,UAASC,MAAM,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;EAC9C,CAACJ,MAAM,CAACK,KAAK,GAAG,CAAC,GAAGN,KAAK,GAAGD,IAAI,EAAEE,MAAM,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;AAC3D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}