{"ast": null, "code": "import { ReactiveFormsModule, Validators } from '@angular/forms';\nimport { AvaTagComponent, AvaTextboxComponent, ButtonComponent, DropdownComponent, IconComponent, PopupComponent, TextCardComponent } from '@ava/play-comp-library';\nimport { ConsoleCardComponent } from 'projects/console/src/app/shared/components/console-card/console-card.component';\nimport { Subject, takeUntil, map, startWith, debounceTime, distinctUntilChanged } from 'rxjs';\nimport { CommonModule } from '@angular/common';\nimport { LucideAngularModule } from 'lucide-angular';\nimport { PageFooterComponent } from 'projects/console/src/app/shared/components/page-footer/page-footer.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"projects/console/src/app/shared/services\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"projects/console/src/app/shared/services/realm.service\";\nimport * as i5 from \"../../../../org-config/services/org-config.service\";\nimport * as i6 from \"@angular/common\";\nconst _c0 = () => ({\n  background: \"#E6F3FF\",\n  color: \"#292C3D\"\n});\nfunction ViewRealmsComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 27)(2, \"h5\", 28);\n    i0.ɵɵtext(3, \"No realms found matching your criteria\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ViewRealmsComponent_ng_container_11_Conditional_2_For_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ava-tag\", 31);\n  }\n  if (rf & 2) {\n    const tag_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"label\", tag_r4)(\"customStyle\", i0.ɵɵpureFunction0(2, _c0));\n  }\n}\nfunction ViewRealmsComponent_ng_container_11_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵrepeaterCreate(1, ViewRealmsComponent_ng_container_11_Conditional_2_For_2_Template, 1, 3, \"ava-tag\", 31, i0.ɵɵrepeaterTrackByIdentity);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const realm_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(realm_r2.tags);\n  }\n}\nfunction ViewRealmsComponent_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"ava-console-card\", 29);\n    i0.ɵɵlistener(\"actionClick\", function ViewRealmsComponent_ng_container_11_Template_ava_console_card_actionClick_1_listener($event) {\n      const realm_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onActionClick($event, realm_r2.id));\n    });\n    i0.ɵɵtemplate(2, ViewRealmsComponent_ng_container_11_Conditional_2_Template, 3, 0, \"div\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const realm_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"title\", realm_r2 == null ? null : realm_r2.title)(\"author\", (realm_r2 == null ? null : realm_r2.owner) || \"AAVA\")(\"actions\", ctx_r2.defaultActions)(\"skeleton\", ctx_r2.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r2.displayedRealms.length ? 2 : -1);\n  }\n}\nfunction ViewRealmsComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"div\", 33)(2, \"app-page-footer\", 34);\n    i0.ɵɵlistener(\"pageChange\", function ViewRealmsComponent_div_12_Template_app_page_footer_pageChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onPageChange($event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"totalItems\", ctx_r2.filteredRealms.length + 1)(\"currentPage\", ctx_r2.currentPage)(\"itemsPerPage\", ctx_r2.itemsPerPage);\n  }\n}\nexport let ViewRealmsComponent = /*#__PURE__*/(() => {\n  class ViewRealmsComponent {\n    paginationService;\n    router;\n    route;\n    fb;\n    realmService;\n    orgConfigService;\n    // Popup state for success messages\n    showSuccessPopup = false;\n    popupMessage = '';\n    popupTitle = '';\n    iconName = 'info';\n    submissionSuccess = false;\n    // popup for delete confirmation\n    showDeletePopup = false;\n    realmToDelete = null;\n    simpleOptions = [{\n      name: 'Option 1',\n      value: '1'\n    }, {\n      name: 'Option 2',\n      value: '2'\n    }, {\n      name: 'Option 3',\n      value: '3'\n    }, {\n      name: 'Option 4',\n      value: '4'\n    }, {\n      name: 'Option 5',\n      value: '5'\n    }];\n    defaultActions = [{\n      id: 'edit',\n      icon: 'pencil',\n      label: 'Edit item',\n      tooltip: 'Edit'\n    }, {\n      id: 'delete',\n      icon: 'trash-2',\n      label: 'Delete item',\n      tooltip: 'Delete'\n    }];\n    allRealms = [];\n    filteredRealms = [];\n    displayedRealms = [];\n    searchForm;\n    isLoading = false;\n    currentPage = 1;\n    itemsPerPage = 11;\n    totalPages = 1;\n    destroy$ = new Subject();\n    selectedData = null;\n    cardSkeletonPlaceholders = Array(11);\n    showRealmPopup = false;\n    addRealmForm;\n    isRealmUpdateMode = false;\n    hierarchyData = [];\n    orgOptions = [];\n    domainOptions = [];\n    projectOptions = [];\n    teamOptions = [];\n    selectedOrgName = '';\n    selectedDomainName = '';\n    selectedProjectName = '';\n    selectedTeamName = '';\n    selectedOrg = '';\n    selectedDomain = '';\n    selectedProject = '';\n    selectedTeam = '';\n    constructor(paginationService, router, route, fb, realmService, orgConfigService) {\n      this.paginationService = paginationService;\n      this.router = router;\n      this.route = route;\n      this.fb = fb;\n      this.realmService = realmService;\n      this.orgConfigService = orgConfigService;\n      this.searchForm = this.fb.group({\n        search: ['']\n      });\n    }\n    ngOnInit() {\n      this.isLoading = true;\n      this.addRealmForm = this.getAddRealmForm();\n      this.initSearchListener();\n      this.getRealmList();\n      this.getOrgList();\n    }\n    getAddRealmForm() {\n      return this.fb.group({\n        realmId: [null],\n        realmName: [null, [Validators.required]],\n        orgId: [null, [Validators.required]],\n        domainId: [null, [Validators.required]],\n        projectId: [null, [Validators.required]],\n        teamId: [null, [Validators.required]]\n      });\n    }\n    getRealmList() {\n      this.realmService.getAllRealm().pipe(takeUntil(this.destroy$)).subscribe({\n        next: res => {\n          this.allRealms = this.transformResponseToCardData(res);\n          this.filteredRealms = [...this.allRealms];\n          this.updateDisplayedRealms();\n          this.setInitialPageFromQueryParam();\n        },\n        error: e => console.error(e),\n        complete: () => this.isLoading = false\n      });\n    }\n    onPageChange(page) {\n      this.currentPage = page;\n      this.updateDisplayedRealms();\n      this.router.navigate([], {\n        relativeTo: this.route,\n        queryParams: {\n          page: this.currentPage\n        },\n        queryParamsHandling: 'merge'\n      });\n    }\n    onActionClick(event, realmId) {\n      switch (event.actionId) {\n        case 'delete':\n          this.deleteRealm(realmId);\n          break;\n        case 'edit':\n          this.onUpdateRealmPopup(realmId);\n          break;\n        default:\n          break;\n      }\n    }\n    transformResponseToCardData(data) {\n      return data.map(item => {\n        const result = {\n          id: item.realmId || '',\n          title: item.realmName,\n          tags: [item.orgName, item.domainName, item.projectName, item.teamName],\n          orgId: item.orgId,\n          orgName: item.orgName,\n          domainId: item.domainId,\n          domainName: item.domainName,\n          projectId: item.projectId,\n          projectName: item.projectName,\n          teamId: item.teamId,\n          teamName: item.teamName\n        };\n        if (item.userSignature) {\n          result.owner = item.userSignature;\n        }\n        return result;\n      });\n    }\n    onUpdateRealmPopup(realmId) {\n      this.isRealmUpdateMode = true;\n      this.onUpdateRealmForm(realmId);\n    }\n    onUpdateRealmForm(realmId) {\n      const realm = this.allRealms.find(p => p.id === realmId);\n      if (!realm) return;\n      this.addRealmForm.patchValue({\n        realmId: realm.id,\n        realmName: realm.title,\n        orgId: realm.orgId,\n        domainId: realm.domainId,\n        projectId: realm.projectId,\n        teamId: realm.teamId\n      });\n      // Store the IDs for form and the names for dropdown pre-selection\n      this.selectedOrg = realm.orgId.toString();\n      this.selectedDomain = realm.domainId.toString();\n      this.selectedProject = realm.projectId.toString();\n      // Store the names for dropdown pre-selection\n      this.selectedOrgName = realm.orgName;\n      this.selectedDomainName = realm.domainName;\n      this.selectedProjectName = realm.projectName;\n      this.selectedTeamName = realm.teamName;\n      if (this.selectedOrg) {\n        this.loadDomains(this.selectedOrg);\n        if (this.selectedDomain) {\n          this.loadProjects(this.selectedDomain);\n          if (this.selectedProject) {\n            this.loadTeams(this.selectedProject);\n          }\n        }\n      }\n      this.showRealmPopup = true;\n    }\n    // Step 1: User clicks trash icon → open delete confirmation\n    deleteRealm(realmId) {\n      const realm = this.allRealms.find(p => p.id === realmId);\n      if (!realm) return;\n      this.realmToDelete = realm;\n      this.showDeletePopup = true;\n    }\n    // Step 2: User confirms delete in popup\n    onConfirmDelete() {\n      if (!this.realmToDelete?.id) return;\n      const realmId = this.realmToDelete.id;\n      this.realmService.deleteRealm(realmId).subscribe({\n        next: res => {\n          if (res) {\n            // Update local realms lists\n            this.allRealms = this.allRealms.filter(p => p.id !== realmId);\n            this.filteredRealms = this.filteredRealms.filter(p => p.id !== realmId);\n            this.updateDisplayedRealms();\n            // Show success popup\n            this.iconName = 'check-circle';\n            this.popupTitle = 'Success';\n            this.popupMessage = 'Realm deleted successfully.';\n            this.submissionSuccess = true;\n            this.showSuccessPopup = true;\n          }\n          this.closeDeletePopup();\n        },\n        error: err => {\n          console.error('Failed to delete realm:', err);\n          this.iconName = 'alert-circle';\n          this.popupTitle = 'Error';\n          this.popupMessage = 'An unexpected error occurred.';\n          this.submissionSuccess = false;\n          this.showSuccessPopup = true;\n          this.closeDeletePopup();\n        }\n      });\n    }\n    // Step 3: User cancels or closes delete popup\n    closeDeletePopup() {\n      this.showDeletePopup = false;\n      this.realmToDelete = null;\n    }\n    // Success popup confirm handler\n    onSuccessConfirm() {\n      this.closeSuccessPopup();\n    }\n    // Close success popup manually or when user clicks close icon\n    closeSuccessPopup() {\n      this.showSuccessPopup = false;\n      this.popupTitle = '';\n      this.popupMessage = '';\n      this.iconName = 'info';\n    }\n    setInitialPageFromQueryParam() {\n      const pageParam = this.route.snapshot.queryParamMap.get('page');\n      if (pageParam) {\n        const page = parseInt(pageParam, 10);\n        if (!isNaN(page)) this.currentPage = page;\n      }\n    }\n    initSearchListener() {\n      this.searchForm.get('search').valueChanges.pipe(startWith(''), debounceTime(300), distinctUntilChanged(), map(value => value?.toLowerCase() ?? ''), takeUntil(this.destroy$)).subscribe(searchText => {\n        this.filterRealms(searchText);\n      });\n    }\n    updateDisplayedRealms() {\n      this.itemsPerPage = this.currentPage === 1 ? 12 : 11;\n      const {\n        displayedItems,\n        totalPages\n      } = this.paginationService.getPaginatedItems(this.filteredRealms, this.currentPage, this.itemsPerPage);\n      this.displayedRealms = displayedItems;\n      this.totalPages = totalPages;\n    }\n    filterRealms(searchText) {\n      this.filteredRealms = this.allRealms.filter(realm => {\n        const titleMatch = realm.title?.toLowerCase().includes(searchText);\n        const descriptionMatch = realm.description?.toLowerCase().includes(searchText);\n        const tagMatch = realm.tags?.some(tag => tag.label?.toLowerCase().includes(searchText));\n        return titleMatch || descriptionMatch || tagMatch;\n      });\n      this.currentPage = 1;\n      this.updateDisplayedRealms();\n    }\n    onCreateRealmPopup() {\n      this.showRealmPopup = true;\n    }\n    getOrgList() {\n      if (this.hierarchyData.length === 0) {\n        this.orgConfigService.getOrganizationHierarchy().subscribe({\n          next: data => {\n            this.hierarchyData = data;\n            this.loadOrganizations();\n          },\n          error: e => console.error(e)\n        });\n      }\n    }\n    loadOrganizations() {\n      this.orgOptions = this.hierarchyData.map(org => ({\n        name: org.organizationName,\n        value: org.orgId.toString()\n      }));\n    }\n    onOrgSelect(event) {\n      const selectedOrgId = event.selectedOptions?.[0]?.value;\n      const selectedOrgName = event.selectedOptions?.[0]?.name;\n      if (selectedOrgId) {\n        this.selectedOrg = selectedOrgId;\n        this.selectedOrgName = selectedOrgName;\n        this.addRealmForm.patchValue({\n          orgId: selectedOrgId,\n          domainId: '',\n          projectId: '',\n          teamId: ''\n        });\n        this.loadDomains(selectedOrgId);\n        this.selectedDomain = '';\n        this.selectedProject = '';\n        this.selectedTeam = '';\n        this.selectedDomainName = '';\n        this.selectedProjectName = '';\n        this.selectedTeamName = '';\n        this.projectOptions = [];\n        this.teamOptions = [];\n      }\n    }\n    loadDomains(orgId) {\n      const org = this.hierarchyData.find(o => o.orgId.toString() === orgId);\n      if (org) {\n        this.domainOptions = org.domains.map(domain => ({\n          name: domain.domainName,\n          value: domain.domainId.toString()\n        }));\n      } else {\n        this.domainOptions = [];\n      }\n    }\n    onDomainSelect(event) {\n      const selectedDomainId = event.selectedOptions?.[0]?.value;\n      const selectedDomainName = event.selectedOptions?.[0]?.name;\n      if (selectedDomainId) {\n        this.selectedDomain = selectedDomainId;\n        this.selectedDomainName = selectedDomainName;\n        this.addRealmForm.patchValue({\n          domainId: selectedDomainId,\n          projectId: '',\n          teamId: ''\n        });\n        this.loadProjects(selectedDomainId);\n        this.selectedProject = '';\n        this.selectedTeam = '';\n        this.selectedProjectName = '';\n        this.selectedTeamName = '';\n        this.teamOptions = [];\n      }\n    }\n    loadProjects(domainId) {\n      const org = this.hierarchyData.find(o => o.domains.some(d => d.domainId.toString() === domainId));\n      if (org) {\n        const domain = org.domains.find(d => d.domainId.toString() === domainId);\n        if (domain) {\n          this.projectOptions = domain.projects.map(project => ({\n            name: project.projectName,\n            value: project.projectId.toString()\n          }));\n        } else {\n          this.projectOptions = [];\n        }\n      } else {\n        this.projectOptions = [];\n      }\n    }\n    onProjectSelect(event) {\n      const selectedProjectId = event.selectedOptions?.[0]?.value;\n      const selectedProjectName = event.selectedOptions?.[0]?.name;\n      if (selectedProjectId) {\n        this.selectedProject = selectedProjectId;\n        this.selectedProjectName = selectedProjectName;\n        this.addRealmForm.patchValue({\n          projectId: selectedProjectId,\n          team: ''\n        });\n        this.loadTeams(selectedProjectId);\n        this.selectedTeam = '';\n        this.selectedTeamName = '';\n      }\n    }\n    loadTeams(projectId) {\n      const org = this.hierarchyData.find(o => o.domains.some(d => d.projects.some(p => p.projectId.toString() === projectId)));\n      if (org) {\n        const domain = org.domains.find(d => d.projects.some(p => p.projectId.toString() === projectId));\n        if (domain) {\n          const project = domain.projects.find(p => p.projectId.toString() === projectId);\n          if (project) {\n            this.teamOptions = project.teams.map(team => ({\n              name: team.teamName,\n              value: team.teamId.toString()\n            }));\n          } else {\n            this.teamOptions = [];\n          }\n        } else {\n          this.teamOptions = [];\n        }\n      } else {\n        this.teamOptions = [];\n      }\n    }\n    onTeamSelect(event) {\n      const selectedTeamId = event.selectedOptions?.[0]?.value;\n      const selectedTeamName = event.selectedOptions?.[0]?.name;\n      if (selectedTeamId) {\n        this.selectedTeam = selectedTeamId;\n        this.selectedTeamName = selectedTeamName;\n        this.addRealmForm.patchValue({\n          teamId: selectedTeamId\n        });\n      }\n    }\n    createRealm() {\n      if (this.addRealmForm.valid) {\n        const {\n          realmId,\n          realmName,\n          teamId\n        } = this.addRealmForm.value;\n        if (this.isRealmUpdateMode) {\n          this.realmService.updateRealm(realmName, teamId, realmId).subscribe({\n            next: res => {\n              this.allRealms.forEach(r => {\n                if (r.id == res.realmId) {\n                  r.title = res.realmName;\n                  r.tags = [res.orgName, res.domainName, res.projectName, res.teamName];\n                  r.orgId = res.orgId;\n                  r.orgName = res.orgName;\n                  r.domainId = res.domainId;\n                  r.domainName = res.domainName;\n                  r.projectId = res.projectId;\n                  r.projectName = res.projectName;\n                  r.teamId = res.teamId;\n                  r.teamName = res.teamName;\n                }\n              });\n              this.filteredRealms = [...this.allRealms];\n              this.updateDisplayedRealms();\n              // Show success popup\n              this.iconName = 'check-circle';\n              this.popupTitle = 'Success';\n              this.popupMessage = 'Realm updated successfully.';\n              this.submissionSuccess = true;\n              this.showSuccessPopup = true;\n              this.showRealmPopup = false;\n            },\n            error: e => {\n              console.error(e);\n              this.iconName = 'alert-circle';\n              this.popupTitle = 'Error';\n              this.popupMessage = 'An unexpected error occurred.';\n              this.submissionSuccess = false;\n              this.showSuccessPopup = true;\n              this.showRealmPopup = false;\n            }\n          });\n        } else {\n          this.realmService.createRealm(realmName, teamId).subscribe({\n            next: res => {\n              this.allRealms.push({\n                id: res.realmId,\n                title: res.realmName,\n                tags: [res.orgName, res.domainName, res.projectName, res.teamName],\n                orgId: res.orgId,\n                orgName: res.orgName,\n                domainId: res.domainId,\n                domainName: res.domainName,\n                projectId: res.projectId,\n                projectName: res.projectName,\n                teamId: res.teamId,\n                teamName: res.teamName\n              });\n              this.filteredRealms = [...this.allRealms];\n              this.updateDisplayedRealms();\n              // Show success popup\n              this.iconName = 'check-circle';\n              this.popupTitle = 'Success';\n              this.popupMessage = 'Realm created successfully.';\n              this.submissionSuccess = true;\n              this.showSuccessPopup = true;\n              this.showRealmPopup = false;\n            },\n            error: e => {\n              console.error(e);\n              this.iconName = 'alert-circle';\n              this.popupTitle = 'Error';\n              this.popupMessage = 'An unexpected error occurred.';\n              this.submissionSuccess = false;\n              this.showSuccessPopup = true;\n              this.showRealmPopup = false;\n            }\n          });\n        }\n      }\n    }\n    closeRealmPopup() {\n      this.showRealmPopup = false;\n      this.addRealmForm.reset();\n      this.isRealmUpdateMode = false;\n    }\n    static ɵfac = function ViewRealmsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ViewRealmsComponent)(i0.ɵɵdirectiveInject(i1.PaginationService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.RealmService), i0.ɵɵdirectiveInject(i5.OrgConfigService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ViewRealmsComponent,\n      selectors: [[\"app-view-realms\"]],\n      decls: 45,\n      vars: 66,\n      consts: [[\"id\", \"realms-container\", 1, \"container-fluid\"], [\"id\", \"search-filter-container\", 1, \"row\", \"g-3\"], [1, \"col-12\", \"col-md-8\", \"col-lg-9\", \"col-xl-10\", \"search-section\"], [3, \"formGroup\"], [\"placeholder\", \"Search \\\"Realms\\\"\", \"hoverEffect\", \"glow\", \"pressedEffect\", \"solid\", \"formControlName\", \"search\"], [\"slot\", \"icon-start\", \"iconName\", \"search\", \"iconColor\", \"var(--color-brand-primary)\", 3, \"iconSize\"], [1, \"col-12\", \"col-md-4\", \"col-lg-3\", \"col-xl-2\", \"action-buttons\"], [\"label\", \"Create\", \"variant\", \"primary\", \"size\", \"large\", 1, \"action-button\", 3, \"userClick\", \"width\"], [\"id\", \"realms-card-container\", 1, \"row\", \"g-3\"], [\"iconColor\", \"#144692\", \"title\", \"Create Realm\", 1, \"col-12\", \"col-sm-6\", \"col-md-4\", \"col-lg-3\", \"col-xl-3\", \"col-xxl-2\", \"mt-5\", 3, \"cardClick\", \"type\", \"iconName\", \"isLoading\"], [\"class\", \"col-12 d-flex justify-content-center align-items-center py-5\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"row\", 4, \"ngIf\"], [\"iconColor\", \"#28a745\", 3, \"confirm\", \"closed\", \"show\", \"title\", \"message\", \"showHeaderIcon\", \"headerIconName\", \"showClose\", \"showCancel\", \"confirmButtonVariant\", \"confirmButtonBackground\"], [\"title\", \"Delete Realm?\", \"headerIconName\", \"trash\", \"iconColor\", \"#dc3545\", 3, \"confirm\", \"cancel\", \"closed\", \"show\", \"message\", \"showHeaderIcon\", \"showClose\", \"showCancel\", \"showConfirm\", \"confirmButtonLabel\", \"confirmButtonVariant\", \"confirmButtonBackground\"], [\"message\", \"\", 3, \"show\", \"showTitle\", \"showHeaderIcon\", \"showClose\", \"showInlineMessage\", \"showConfirm\", \"showCancel\", \"popupWidth\"], [1, \"add__realm--container\"], [1, \"realm__form\", 3, \"formGroup\"], [1, \"form-fields\"], [1, \"input__field--wrapper\"], [1, \"filter-label\", \"required\"], [\"formControlName\", \"realmName\", \"id\", \"realmName\", \"name\", \"realmName\", \"placeholder\", \"Enter Realm Name\", \"size\", \"md\", 1, \"input-field\", 3, \"required\", \"fullWidth\"], [3, \"selectionChange\", \"dropdownTitle\", \"options\", \"selectedValue\", \"disabled\", \"search\", \"enableSearch\"], [1, \"button__container\"], [\"label\", \"Cancel\", \"variant\", \"secondary\", \"size\", \"large\", 1, \"action-button\", 3, \"userClick\", \"width\"], [\"variant\", \"primary\", \"size\", \"large\", 1, \"action-button\", 3, \"userClick\", \"label\", \"width\", \"disabled\"], [1, \"col-12\", \"d-flex\", \"justify-content-center\", \"align-items-center\", \"py-5\"], [1, \"text-center\"], [1, \"text-muted\"], [\"categoryIcon\", \"plus\", \"categoryTitle\", \"Realm\", \"categoryValue\", \"2\", 1, \"col-12\", \"col-sm-6\", \"col-md-4\", \"col-lg-3\", \"col-xl-3\", \"col-xxl-2\", \"mt-5\", 3, \"actionClick\", \"title\", \"author\", \"actions\", \"skeleton\"], [1, \"tag--container\"], [\"size\", \"sm\", \"iconColor\", \"#000000\", 3, \"label\", \"customStyle\"], [1, \"row\"], [1, \"col-12\", \"d-flex\", \"justify-content-center\", \"mt-4\"], [3, \"pageChange\", \"totalItems\", \"currentPage\", \"itemsPerPage\"]],\n      template: function ViewRealmsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"form\", 3)(4, \"ava-textbox\", 4);\n          i0.ɵɵelement(5, \"ava-icon\", 5);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"ava-button\", 7);\n          i0.ɵɵlistener(\"userClick\", function ViewRealmsComponent_Template_ava_button_userClick_7_listener() {\n            return ctx.onCreateRealmPopup();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(8, \"div\", 8)(9, \"ava-text-card\", 9);\n          i0.ɵɵlistener(\"cardClick\", function ViewRealmsComponent_Template_ava_text_card_cardClick_9_listener() {\n            return ctx.onCreateRealmPopup();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(10, ViewRealmsComponent_div_10_Template, 4, 0, \"div\", 10)(11, ViewRealmsComponent_ng_container_11_Template, 3, 5, \"ng-container\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(12, ViewRealmsComponent_div_12_Template, 3, 3, \"div\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"ava-popup\", 13);\n          i0.ɵɵlistener(\"confirm\", function ViewRealmsComponent_Template_ava_popup_confirm_13_listener() {\n            return ctx.onSuccessConfirm();\n          })(\"closed\", function ViewRealmsComponent_Template_ava_popup_closed_13_listener() {\n            return ctx.closeSuccessPopup();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"ava-popup\", 14);\n          i0.ɵɵlistener(\"confirm\", function ViewRealmsComponent_Template_ava_popup_confirm_14_listener() {\n            return ctx.onConfirmDelete();\n          })(\"cancel\", function ViewRealmsComponent_Template_ava_popup_cancel_14_listener() {\n            return ctx.closeDeletePopup();\n          })(\"closed\", function ViewRealmsComponent_Template_ava_popup_closed_14_listener() {\n            return ctx.closeDeletePopup();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"ava-popup\", 15)(16, \"div\", 16)(17, \"form\", 17)(18, \"div\", 18)(19, \"div\", 19)(20, \"label\", 20);\n          i0.ɵɵtext(21, \"Name of the Realm\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(22, \"ava-textbox\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(23, \"div\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"div\", 18)(25, \"div\", 19)(26, \"label\", 20);\n          i0.ɵɵtext(27, \"Choose Organization\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"ava-dropdown\", 22);\n          i0.ɵɵlistener(\"selectionChange\", function ViewRealmsComponent_Template_ava_dropdown_selectionChange_28_listener($event) {\n            return ctx.onOrgSelect($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(29, \"div\", 19)(30, \"label\", 20);\n          i0.ɵɵtext(31, \"Choose Domain\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"ava-dropdown\", 22);\n          i0.ɵɵlistener(\"selectionChange\", function ViewRealmsComponent_Template_ava_dropdown_selectionChange_32_listener($event) {\n            return ctx.onDomainSelect($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(33, \"div\", 18)(34, \"div\", 19)(35, \"label\", 20);\n          i0.ɵɵtext(36, \"Choose Project\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"ava-dropdown\", 22);\n          i0.ɵɵlistener(\"selectionChange\", function ViewRealmsComponent_Template_ava_dropdown_selectionChange_37_listener($event) {\n            return ctx.onProjectSelect($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"div\", 19)(39, \"label\", 20);\n          i0.ɵɵtext(40, \"Choose Team\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"ava-dropdown\", 22);\n          i0.ɵɵlistener(\"selectionChange\", function ViewRealmsComponent_Template_ava_dropdown_selectionChange_41_listener($event) {\n            return ctx.onTeamSelect($event);\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(42, \"div\", 23)(43, \"ava-button\", 24);\n          i0.ɵɵlistener(\"userClick\", function ViewRealmsComponent_Template_ava_button_userClick_43_listener() {\n            return ctx.closeRealmPopup();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"ava-button\", 25);\n          i0.ɵɵlistener(\"userClick\", function ViewRealmsComponent_Template_ava_button_userClick_44_listener() {\n            return ctx.createRealm();\n          });\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"formGroup\", ctx.searchForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"iconSize\", 16);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"width\", \"100%\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"type\", \"create\")(\"iconName\", \"plus\")(\"isLoading\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.filteredRealms.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.isLoading && ctx.displayedRealms.length === 0 ? ctx.cardSkeletonPlaceholders : ctx.displayedRealms);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.filteredRealms.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵpropertyInterpolate(\"headerIconName\", ctx.iconName);\n          i0.ɵɵproperty(\"show\", ctx.showSuccessPopup)(\"title\", ctx.popupTitle)(\"message\", ctx.popupMessage)(\"showHeaderIcon\", true)(\"showClose\", true)(\"showCancel\", false)(\"confirmButtonVariant\", \"primary\")(\"confirmButtonBackground\", \"#28a745\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"show\", ctx.showDeletePopup)(\"message\", \"Are you sure you want to delete ?\")(\"showHeaderIcon\", true)(\"showClose\", true)(\"showCancel\", true)(\"showConfirm\", true)(\"confirmButtonLabel\", \"Delete\")(\"confirmButtonVariant\", \"primary\")(\"confirmButtonBackground\", \"#dc3545\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"show\", ctx.showRealmPopup)(\"showTitle\", false)(\"showHeaderIcon\", false)(\"showClose\", false)(\"showInlineMessage\", false)(\"showConfirm\", false)(\"showCancel\", false)(\"popupWidth\", \"744px\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.addRealmForm);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"required\", true)(\"fullWidth\", false);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"dropdownTitle\", \"Select Organization\")(\"options\", ctx.orgOptions)(\"selectedValue\", ctx.selectedOrgName)(\"disabled\", false)(\"search\", true)(\"enableSearch\", true);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"dropdownTitle\", \"Select Domain\")(\"options\", ctx.domainOptions)(\"selectedValue\", ctx.selectedDomainName)(\"disabled\", !ctx.selectedOrg)(\"search\", true)(\"enableSearch\", true);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"dropdownTitle\", \"Select Project\")(\"options\", ctx.projectOptions)(\"selectedValue\", ctx.selectedProjectName)(\"disabled\", !ctx.selectedDomain)(\"search\", true)(\"enableSearch\", true);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"dropdownTitle\", \"Select Team\")(\"options\", ctx.teamOptions)(\"selectedValue\", ctx.selectedTeamName)(\"disabled\", !ctx.selectedProject)(\"search\", true)(\"enableSearch\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"width\", \"100%\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"label\", ctx.isRealmUpdateMode ? \"Update\" : \"Create\")(\"width\", \"100%\")(\"disabled\", !ctx.addRealmForm.valid);\n        }\n      },\n      dependencies: [CommonModule, i6.NgForOf, i6.NgIf, PageFooterComponent, TextCardComponent, AvaTextboxComponent, LucideAngularModule, IconComponent, PopupComponent, ReactiveFormsModule, i3.ɵNgNoValidate, i3.NgControlStatus, i3.NgControlStatusGroup, i3.RequiredValidator, i3.FormGroupDirective, i3.FormControlName, ConsoleCardComponent, AvaTagComponent, ButtonComponent, DropdownComponent],\n      styles: [\"[_nghost-%COMP%] {\\n  padding: 2rem 1rem 0 1rem;\\n}\\n\\n#search-filter-container[_ngcontent-%COMP%] {\\n  align-items: center;\\n}\\n\\n.mt-5[_ngcontent-%COMP%] {\\n  margin-top: 2rem;\\n}\\n\\n.tag--container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  flex-wrap: wrap;\\n}\\n\\n.realm__form[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-flow: column;\\n  gap: 16px;\\n}\\n\\n.form-fields[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 24px;\\n}\\n\\n.input__field--wrapper[_ngcontent-%COMP%] {\\n  flex: 1;\\n  width: 360px;\\n}\\n\\n.filter-label[_ngcontent-%COMP%] {\\n  margin-bottom: 4px;\\n  display: block;\\n  font-weight: 500;\\n  text-align: left;\\n  color: #14161F;\\n}\\n\\n.required[_ngcontent-%COMP%]::after {\\n  content: \\\" *\\\";\\n  color: red;\\n  font-weight: bold;\\n}\\n\\n.button__container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  justify-content: center;\\n  margin: 16px 0 16px 0;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3Byb2plY3RzL2NvbnNvbGUvc3JjL2FwcC9wYWdlcy9tYW5hZ2UvcmVhbG0tbWFuYWdlbWVudC9jb21wb25lbnRzL3ZpZXctcmVhbG1zL3ZpZXctcmVhbG1zLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UseUJBQUE7QUFDRjs7QUFFQTtFQUNFLG1CQUFBO0FBQ0Y7O0FBRUE7RUFDRSxnQkFBQTtBQUNGOztBQUVBO0VBQ0UsYUFBQTtFQUNBLFFBQUE7RUFDQSxlQUFBO0FBQ0Y7O0FBRUE7RUFDRSxhQUFBO0VBQ0EsaUJBQUE7RUFDQSxTQUFBO0FBQ0Y7O0FBRUE7RUFDRSxhQUFBO0VBQ0EsZUFBQTtFQUNBLFNBQUE7QUFDRjs7QUFFQTtFQUNFLE9BQUE7RUFDQSxZQUFBO0FBQ0Y7O0FBRUE7RUFDRSxrQkFBQTtFQUNBLGNBQUE7RUFDQSxnQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtBQUNGOztBQUVBO0VBQ0UsYUFBQTtFQUNBLFVBQUE7RUFDQSxpQkFBQTtBQUNGOztBQUVBO0VBQ0UsYUFBQTtFQUNBLFNBQUE7RUFDQSx1QkFBQTtFQUNBLHFCQUFBO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyI6aG9zdCB7XHJcbiAgcGFkZGluZzogMnJlbSAxcmVtIDAgMXJlbTtcclxufVxyXG5cclxuI3NlYXJjaC1maWx0ZXItY29udGFpbmVyIHtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG59XHJcblxyXG4ubXQtNSB7XHJcbiAgbWFyZ2luLXRvcDogMnJlbTtcclxufVxyXG5cclxuLnRhZy0tY29udGFpbmVyIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGdhcDogOHB4O1xyXG4gIGZsZXgtd3JhcDogd3JhcDtcclxufVxyXG5cclxuLnJlYWxtX19mb3JtIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGZsZXgtZmxvdzogY29sdW1uO1xyXG4gIGdhcDogMTZweDsgICAgXHJcbn1cclxuXHJcbi5mb3JtLWZpZWxkcyB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBmbGV4LXdyYXA6IHdyYXA7XHJcbiAgZ2FwOiAyNHB4O1xyXG59XHJcblxyXG4uaW5wdXRfX2ZpZWxkLS13cmFwcGVyIHtcclxuICBmbGV4OiAxO1xyXG4gIHdpZHRoOiAzNjBweDtcclxufVxyXG5cclxuLmZpbHRlci1sYWJlbCB7XHJcbiAgbWFyZ2luLWJvdHRvbTogNHB4O1xyXG4gIGRpc3BsYXk6IGJsb2NrO1xyXG4gIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgdGV4dC1hbGlnbjogbGVmdDtcclxuICBjb2xvcjogIzE0MTYxRjtcclxufVxyXG5cclxuLnJlcXVpcmVkOjphZnRlciB7XHJcbiAgY29udGVudDogXCIgKlwiO1xyXG4gIGNvbG9yOiByZWQ7XHJcbiAgZm9udC13ZWlnaHQ6IGJvbGQ7XHJcbn1cclxuXHJcbi5idXR0b25fX2NvbnRhaW5lciB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBnYXA6IDE2cHg7XHJcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgbWFyZ2luOiAxNnB4IDAgMTZweCAwO1xyXG59XHJcbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n  return ViewRealmsComponent;\n})();", "map": {"version": 3, "names": ["ReactiveFormsModule", "Validators", "AvaTagComponent", "AvaTextboxComponent", "ButtonComponent", "DropdownComponent", "IconComponent", "PopupComponent", "TextCardComponent", "ConsoleCardComponent", "Subject", "takeUntil", "map", "startWith", "debounceTime", "distinctUntilChanged", "CommonModule", "LucideAngularModule", "PageFooterComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵproperty", "tag_r4", "ɵɵpureFunction0", "_c0", "ɵɵrepeaterCreate", "ViewRealmsComponent_ng_container_11_Conditional_2_For_2_Template", "ɵɵrepeaterTrackByIdentity", "ɵɵadvance", "ɵɵrepeater", "realm_r2", "tags", "ɵɵelementContainerStart", "ɵɵlistener", "ViewRealmsComponent_ng_container_11_Template_ava_console_card_actionClick_1_listener", "$event", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "onActionClick", "id", "ɵɵtemplate", "ViewRealmsComponent_ng_container_11_Conditional_2_Template", "title", "owner", "defaultActions", "isLoading", "ɵɵconditional", "displayedRealms", "length", "ViewRealmsComponent_div_12_Template_app_page_footer_pageChange_2_listener", "_r5", "onPageChange", "filteredRealms", "currentPage", "itemsPerPage", "ViewRealmsComponent", "paginationService", "router", "route", "fb", "realmService", "orgConfigService", "showSuccessPopup", "popupMessage", "popupTitle", "iconName", "submissionSuccess", "showDeletePopup", "realmToDelete", "simpleOptions", "name", "value", "icon", "label", "tooltip", "allRealms", "searchForm", "totalPages", "destroy$", "selectedData", "cardSkeletonPlaceholders", "Array", "showRealmPopup", "addRealmForm", "isRealmUpdateMode", "hierarchyData", "orgOptions", "domainOptions", "projectOptions", "teamOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selected<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectedProjectName", "selectedTeamName", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "selectedProject", "selectedTeam", "constructor", "group", "search", "ngOnInit", "getAddRealmForm", "initSearchListener", "getRealmList", "getOrgList", "realmId", "realmName", "required", "orgId", "domainId", "projectId", "teamId", "getAllRealm", "pipe", "subscribe", "next", "res", "transformResponseToCardData", "updateDisplayedRealms", "setInitialPageFromQueryParam", "error", "e", "console", "complete", "page", "navigate", "relativeTo", "queryParams", "queryParamsHandling", "event", "actionId", "deleteRealm", "onUpdateRealmPopup", "data", "item", "result", "orgName", "domainName", "projectName", "teamName", "userSignature", "onUpdateRealmForm", "realm", "find", "p", "patchValue", "toString", "loadDomains", "loadProjects", "loadTeams", "onConfirmDelete", "filter", "closeDeletePopup", "err", "onSuccessConfirm", "closeSuccessPopup", "pageParam", "snapshot", "queryParamMap", "get", "parseInt", "isNaN", "valueChanges", "toLowerCase", "searchText", "filterRealms", "displayedItems", "getPaginatedItems", "titleMatch", "includes", "descriptionMatch", "description", "tagMatch", "some", "tag", "onCreateRealmPopup", "getOrganizationHierarchy", "loadOrganizations", "org", "organizationName", "onOrgSelect", "selectedOrgId", "selectedOptions", "o", "domains", "domain", "onDomainSelect", "selectedDomainId", "d", "projects", "project", "onProjectSelect", "selectedProjectId", "team", "teams", "onTeamSelect", "selectedTeamId", "createRealm", "valid", "updateRealm", "for<PERSON>ach", "r", "push", "closeRealmPopup", "reset", "ɵɵdirectiveInject", "i1", "PaginationService", "i2", "Router", "ActivatedRoute", "i3", "FormBuilder", "i4", "RealmService", "i5", "OrgConfigService", "selectors", "decls", "vars", "consts", "template", "ViewRealmsComponent_Template", "rf", "ctx", "ViewRealmsComponent_Template_ava_button_userClick_7_listener", "ViewRealmsComponent_Template_ava_text_card_cardClick_9_listener", "ViewRealmsComponent_div_10_Template", "ViewRealmsComponent_ng_container_11_Template", "ViewRealmsComponent_div_12_Template", "ViewRealmsComponent_Template_ava_popup_confirm_13_listener", "ViewRealmsComponent_Template_ava_popup_closed_13_listener", "ViewRealmsComponent_Template_ava_popup_confirm_14_listener", "ViewRealmsComponent_Template_ava_popup_cancel_14_listener", "ViewRealmsComponent_Template_ava_popup_closed_14_listener", "ViewRealmsComponent_Template_ava_dropdown_selectionChange_28_listener", "ViewRealmsComponent_Template_ava_dropdown_selectionChange_32_listener", "ViewRealmsComponent_Template_ava_dropdown_selectionChange_37_listener", "ViewRealmsComponent_Template_ava_dropdown_selectionChange_41_listener", "ViewRealmsComponent_Template_ava_button_userClick_43_listener", "ViewRealmsComponent_Template_ava_button_userClick_44_listener", "ɵɵpropertyInterpolate", "i6", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "ɵNgNoValidate", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "FormGroupDirective", "FormControlName", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\manage\\realm-management\\components\\view-realms\\view-realms.component.ts", "C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\manage\\realm-management\\components\\view-realms\\view-realms.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { FormGroup, FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';\r\nimport { Router, ActivatedRoute } from '@angular/router';\r\nimport { AvaTagComponent, AvaTextboxComponent, ButtonComponent, DropdownComponent, DropdownOption, IconComponent, PopupComponent, TextCardComponent } from '@ava/play-comp-library';\r\nimport { ConsoleCardAction, ConsoleCardComponent } from 'projects/console/src/app/shared/components/console-card/console-card.component';\r\nimport { PaginationService } from 'projects/console/src/app/shared/services';\r\nimport { Subject, takeUntil, map, startWith, debounceTime, distinctUntilChanged } from 'rxjs';\r\nimport { CommonModule } from '@angular/common';\r\nimport { LucideAngularModule } from 'lucide-angular';\r\nimport { PageFooterComponent } from 'projects/console/src/app/shared/components/page-footer/page-footer.component';\r\nimport { RealmService } from 'projects/console/src/app/shared/services/realm.service';\r\nimport { RealmData } from '../../Models/realm-card.model';\r\nimport { OrgConfigService } from '../../../../org-config/services/org-config.service';\r\n\r\n\r\n@Component({\r\n  selector: 'app-view-realms',\r\n  imports: [\r\n    CommonModule,\r\n    PageFooterComponent,\r\n    TextCardComponent,\r\n    AvaTextboxComponent,\r\n    LucideAngularModule,\r\n    IconComponent,\r\n    PopupComponent,\r\n    ReactiveFormsModule,\r\n    ConsoleCardComponent,\r\n    AvaTagComponent,\r\n    ButtonComponent,\r\n    DropdownComponent\r\n  ],\r\n  templateUrl: './view-realms.component.html',\r\n  styleUrl: './view-realms.component.scss'\r\n})\r\nexport class ViewRealmsComponent {\r\n // Popup state for success messages\r\n  showSuccessPopup = false;\r\n  popupMessage = '';\r\n  popupTitle = '';\r\n  iconName = 'info';\r\n  submissionSuccess = false;\r\n  // popup for delete confirmation\r\n  showDeletePopup: boolean = false;\r\n  realmToDelete: RealmData | null = null;\r\n\r\n  simpleOptions: DropdownOption[] = [\r\n    { name: 'Option 1', value: '1' },\r\n    { name: 'Option 2', value: '2' },\r\n    { name: 'Option 3', value: '3' },\r\n    { name: 'Option 4', value: '4' },\r\n    { name: 'Option 5', value: '5' },\r\n  ];\r\n\r\n  defaultActions: ConsoleCardAction[] = [\r\n    {\r\n      id: 'edit',\r\n      icon: 'pencil',\r\n      label: 'Edit item',\r\n      tooltip: 'Edit',\r\n    },\r\n    {\r\n      id: 'delete',\r\n      icon: 'trash-2',\r\n      label: 'Delete item',\r\n      tooltip: 'Delete',\r\n    }    \r\n  ];\r\n  allRealms: RealmData[] = [];\r\n  filteredRealms: RealmData[] = [];\r\n  displayedRealms: any[] = [];\r\n  searchForm!: FormGroup;\r\n  isLoading = false;\r\n  currentPage: number = 1;\r\n  itemsPerPage: number = 11;\r\n  totalPages: number = 1;\r\n  protected destroy$ = new Subject<void>();\r\n  selectedData: any = null;\r\n  cardSkeletonPlaceholders = Array(11);\r\n  showRealmPopup: boolean = false;\r\n  addRealmForm!: FormGroup;\r\n\r\n  isRealmUpdateMode: boolean = false;\r\n  hierarchyData: any[] = [];\r\n  orgOptions: DropdownOption[] = [];\r\n  domainOptions: DropdownOption[] = [];\r\n  projectOptions: DropdownOption[] = [];\r\n  teamOptions: DropdownOption[] = [];\r\n\r\n  selectedOrgName: string = '';\r\n  selectedDomainName: string = '';\r\n  selectedProjectName: string = '';\r\n  selectedTeamName: string = '';\r\n\r\n  selectedOrg: string = '';\r\n  selectedDomain: string = '';\r\n  selectedProject: string = '';\r\n  selectedTeam: string = '';\r\n\r\n  constructor(\r\n    private paginationService: PaginationService,\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private fb: FormBuilder,\r\n    private realmService: RealmService,\r\n    private orgConfigService: OrgConfigService\r\n  ) {\r\n    this.searchForm = this.fb.group({\r\n      search: [''],\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.isLoading = true;\r\n    this.addRealmForm = this.getAddRealmForm();\r\n    this.initSearchListener();\r\n    this.getRealmList();\r\n    this.getOrgList();\r\n  }\r\n\r\n  getAddRealmForm() {\r\n    return this.fb.group({\r\n      realmId: [null],\r\n      realmName: [null, [Validators.required]],\r\n      orgId: [null, [Validators.required]],\r\n      domainId: [null, [Validators.required]],\r\n      projectId: [null, [Validators.required]],\r\n      teamId: [null, [Validators.required]],\r\n    });\r\n  }\r\n\r\n  getRealmList() {\r\n    this.realmService.getAllRealm().pipe(takeUntil(this.destroy$)).subscribe({\r\n      next: (res: any) => {\r\n        this.allRealms = this.transformResponseToCardData(res);\r\n        this.filteredRealms = [...this.allRealms];\r\n        this.updateDisplayedRealms();\r\n        this.setInitialPageFromQueryParam();\r\n      },\r\n      error: e => console.error(e),\r\n      complete: () => this.isLoading = false\r\n    })\r\n  }\r\n\r\n  onPageChange(page: number): void {\r\n    this.currentPage = page;\r\n    this.updateDisplayedRealms();\r\n    this.router.navigate([], {\r\n      relativeTo: this.route,\r\n      queryParams: { page: this.currentPage },\r\n      queryParamsHandling: 'merge',\r\n    });\r\n  }\r\n\r\n  onActionClick(\r\n    event: { actionId: string; action: ConsoleCardAction },\r\n    realmId: string,\r\n  ): void {\r\n    switch (event.actionId) {\r\n      case 'delete':\r\n        this.deleteRealm(realmId);\r\n        break;\r\n      case 'edit':\r\n        this.onUpdateRealmPopup(realmId);\r\n        break;\r\n      default:\r\n        break;\r\n    }\r\n  }\r\n\r\n\r\n  transformResponseToCardData(data: any): RealmData[] {\r\n    return data.map((item: any) => {\r\n      const result: RealmData = {\r\n        id: item.realmId || '',\r\n        title: item.realmName,\r\n        tags: [item.orgName, item.domainName, item.projectName, item.teamName],\r\n        orgId: item.orgId,\r\n        orgName: item.orgName,\r\n        domainId: item.domainId,\r\n        domainName: item.domainName,\r\n        projectId: item.projectId,\r\n        projectName: item.projectName,\r\n        teamId: item.teamId,\r\n        teamName: item.teamName,\r\n      };\r\n\r\n      if (item.userSignature) {\r\n        result.owner = item.userSignature;\r\n      }\r\n\r\n      return result;\r\n    });\r\n  }\r\n\r\n  onUpdateRealmPopup(realmId: string) {\r\n    this.isRealmUpdateMode = true;\r\n    this.onUpdateRealmForm(realmId);\r\n  }\r\n\r\n  onUpdateRealmForm(realmId: string) {\r\n    const realm = this.allRealms.find((p: any) => p.id === realmId);\r\n    if (!realm) return;\r\n    this.addRealmForm.patchValue({\r\n      realmId: realm.id,\r\n      realmName: realm.title,\r\n      orgId: realm.orgId,\r\n      domainId: realm.domainId,\r\n      projectId: realm.projectId,\r\n      teamId: realm.teamId\r\n    })\r\n\r\n    // Store the IDs for form and the names for dropdown pre-selection\r\n    this.selectedOrg = realm.orgId.toString();\r\n    this.selectedDomain = realm.domainId.toString();\r\n    this.selectedProject = realm.projectId.toString();\r\n\r\n    // Store the names for dropdown pre-selection\r\n    this.selectedOrgName = realm.orgName;\r\n    this.selectedDomainName = realm.domainName;\r\n    this.selectedProjectName = realm.projectName;\r\n    this.selectedTeamName = realm.teamName;\r\n\r\n    if (this.selectedOrg) {\r\n      this.loadDomains(this.selectedOrg);\r\n      if (this.selectedDomain) {\r\n        this.loadProjects(this.selectedDomain);\r\n        if (this.selectedProject) {\r\n          this.loadTeams(this.selectedProject);\r\n        }\r\n      }\r\n    }\r\n    this.showRealmPopup = true;\r\n  }\r\n\r\n  // Step 1: User clicks trash icon → open delete confirmation\r\n  private deleteRealm(realmId: string): void {\r\n    const realm = this.allRealms.find((p: any) => p.id === realmId);\r\n    if (!realm) return;\r\n    this.realmToDelete = realm;\r\n    this.showDeletePopup = true;\r\n  }\r\n\r\n  // Step 2: User confirms delete in popup\r\n  onConfirmDelete(): void {\r\n    if (!this.realmToDelete?.id) return;\r\n\r\n    const realmId = this.realmToDelete.id;\r\n\r\n    this.realmService.deleteRealm(realmId).subscribe({\r\n      next: (res) => {\r\n        if (res) {\r\n          // Update local realms lists\r\n          this.allRealms = this.allRealms.filter((p) => p.id !== realmId);\r\n          this.filteredRealms = this.filteredRealms.filter(\r\n            (p) => p.id !== realmId,\r\n          );\r\n          this.updateDisplayedRealms();\r\n\r\n          // Show success popup\r\n          this.iconName = 'check-circle';\r\n          this.popupTitle = 'Success';\r\n          this.popupMessage = 'Realm deleted successfully.';\r\n          this.submissionSuccess = true;\r\n          this.showSuccessPopup = true;\r\n        }\r\n\r\n        this.closeDeletePopup();\r\n      },\r\n      error: (err) => {\r\n        console.error('Failed to delete realm:', err);\r\n        this.iconName = 'alert-circle';\r\n        this.popupTitle = 'Error';\r\n        this.popupMessage = 'An unexpected error occurred.';\r\n        this.submissionSuccess = false;\r\n        this.showSuccessPopup = true;\r\n        this.closeDeletePopup();\r\n      },\r\n    });\r\n  }\r\n\r\n  // Step 3: User cancels or closes delete popup\r\n  closeDeletePopup(): void {\r\n    this.showDeletePopup = false;\r\n    this.realmToDelete = null;\r\n  }\r\n\r\n  // Success popup confirm handler\r\n  onSuccessConfirm(): void {\r\n    this.closeSuccessPopup();\r\n  }\r\n\r\n  // Close success popup manually or when user clicks close icon\r\n  closeSuccessPopup(): void {\r\n    this.showSuccessPopup = false;\r\n    this.popupTitle = '';\r\n    this.popupMessage = '';\r\n    this.iconName = 'info';\r\n  }\r\n\r\n  private setInitialPageFromQueryParam(): void {\r\n    const pageParam = this.route.snapshot.queryParamMap.get('page');\r\n    if (pageParam) {\r\n      const page = parseInt(pageParam, 10);\r\n      if (!isNaN(page)) this.currentPage = page;\r\n    }\r\n  }\r\n\r\n  private initSearchListener(): void {\r\n    this.searchForm\r\n      .get('search')!\r\n      .valueChanges.pipe(\r\n        startWith(''),\r\n        debounceTime(300),\r\n        distinctUntilChanged(),\r\n        map((value) => value?.toLowerCase() ?? ''),\r\n        takeUntil(this.destroy$),\r\n      )\r\n      .subscribe((searchText) => {\r\n        this.filterRealms(searchText);\r\n      });\r\n  }\r\n\r\n  private updateDisplayedRealms(): void {\r\n    this.itemsPerPage = this.currentPage === 1 ? 12 : 11;\r\n    const { displayedItems, totalPages } =\r\n      this.paginationService.getPaginatedItems(\r\n        this.filteredRealms,\r\n        this.currentPage,\r\n        this.itemsPerPage,\r\n      );\r\n    this.displayedRealms = displayedItems;\r\n    this.totalPages = totalPages;\r\n  }\r\n\r\n  private filterRealms(searchText: string): void {\r\n    this.filteredRealms = this.allRealms.filter((realm: any) => {\r\n      const titleMatch = realm.title?.toLowerCase().includes(searchText);\r\n      const descriptionMatch = realm.description\r\n        ?.toLowerCase()\r\n        .includes(searchText);\r\n      const tagMatch = realm.tags?.some((tag: any) =>\r\n        tag.label?.toLowerCase().includes(searchText),\r\n      );\r\n      return titleMatch || descriptionMatch || tagMatch;\r\n    });\r\n    this.currentPage = 1;\r\n    this.updateDisplayedRealms();\r\n  }\r\n\r\n  onCreateRealmPopup(): void {\r\n    this.showRealmPopup = true;\r\n  }\r\n\r\n  getOrgList() {\r\n    if(this.hierarchyData.length === 0) {\r\n      this.orgConfigService.getOrganizationHierarchy().subscribe({\r\n        next: (data: any) => {\r\n          this.hierarchyData = data;\r\n          this.loadOrganizations();\r\n        },\r\n        error: e => console.error(e)\r\n      })\r\n    }\r\n  }\r\n\r\n  loadOrganizations(): void {\r\n    this.orgOptions = this.hierarchyData.map((org) => ({\r\n      name: org.organizationName,\r\n      value: org.orgId.toString(),\r\n    }));\r\n  }\r\n\r\n  onOrgSelect(event: any) {\r\n    const selectedOrgId = event.selectedOptions?.[0]?.value;\r\n    const selectedOrgName = event.selectedOptions?.[0]?.name;\r\n    if (selectedOrgId) {\r\n      this.selectedOrg = selectedOrgId;\r\n      this.selectedOrgName = selectedOrgName;\r\n      this.addRealmForm.patchValue({ orgId: selectedOrgId, domainId: '', projectId: '', teamId: ''  });\r\n      this.loadDomains(selectedOrgId);\r\n      this.selectedDomain = '';\r\n      this.selectedProject = '';\r\n      this.selectedTeam = '';\r\n      this.selectedDomainName = '';\r\n      this.selectedProjectName = '';\r\n      this.selectedTeamName = '';\r\n      this.projectOptions = [];\r\n      this.teamOptions = [];\r\n    }\r\n  }\r\n\r\n  loadDomains(orgId: string): void {\r\n    const org = this.hierarchyData.find((o) => o.orgId.toString() === orgId);\r\n    if (org) {\r\n      this.domainOptions = org.domains.map((domain: any) => ({\r\n        name: domain.domainName,\r\n        value: domain.domainId.toString(),\r\n      }));\r\n    } else {\r\n      this.domainOptions = [];\r\n    }\r\n  }\r\n\r\n   onDomainSelect(event: any): void {\r\n    const selectedDomainId = event.selectedOptions?.[0]?.value;\r\n    const selectedDomainName = event.selectedOptions?.[0]?.name;\r\n    if (selectedDomainId) {\r\n      this.selectedDomain = selectedDomainId;\r\n      this.selectedDomainName = selectedDomainName;\r\n      this.addRealmForm.patchValue({ domainId: selectedDomainId, projectId: '', teamId: '' });\r\n      this.loadProjects(selectedDomainId);\r\n      this.selectedProject = '';\r\n      this.selectedTeam = '';\r\n      this.selectedProjectName = '';\r\n      this.selectedTeamName = '';\r\n      this.teamOptions = [];\r\n    }\r\n  }\r\n\r\n  loadProjects(domainId: string): void {\r\n    const org = this.hierarchyData.find((o) =>\r\n      o.domains.some((d: any) => d.domainId.toString() === domainId),\r\n    );\r\n    if (org) {\r\n      const domain = org.domains.find(\r\n        (d: any) => d.domainId.toString() === domainId,\r\n      );\r\n      if (domain) {\r\n        this.projectOptions = domain.projects.map((project: any) => ({\r\n          name: project.projectName,\r\n          value: project.projectId.toString(),\r\n        }));\r\n      } else {\r\n        this.projectOptions = [];\r\n      }\r\n    } else {\r\n      this.projectOptions = [];\r\n    }\r\n  }\r\n\r\n  onProjectSelect(event: any): void {\r\n    const selectedProjectId = event.selectedOptions?.[0]?.value;\r\n    const selectedProjectName = event.selectedOptions?.[0]?.name;\r\n    if (selectedProjectId) {\r\n      this.selectedProject = selectedProjectId;\r\n      this.selectedProjectName = selectedProjectName;\r\n      this.addRealmForm.patchValue({ projectId: selectedProjectId, team: '' });\r\n      this.loadTeams(selectedProjectId);\r\n      this.selectedTeam = '';\r\n      this.selectedTeamName = '';\r\n    }\r\n  }\r\n\r\n  loadTeams(projectId: string): void {\r\n    const org = this.hierarchyData.find((o) =>\r\n      o.domains.some((d: any) =>\r\n        d.projects.some((p: any) => p.projectId.toString() === projectId),\r\n      ),\r\n    );\r\n    if (org) {\r\n      const domain = org.domains.find((d: any) =>\r\n        d.projects.some((p: any) => p.projectId.toString() === projectId),\r\n      );\r\n      if (domain) {\r\n        const project = domain.projects.find(\r\n          (p: any) => p.projectId.toString() === projectId,\r\n        );\r\n        if (project) {\r\n          this.teamOptions = project.teams.map((team: any) => ({\r\n            name: team.teamName,\r\n            value: team.teamId.toString(),\r\n          }));\r\n        } else {\r\n          this.teamOptions = [];\r\n        }\r\n      } else {\r\n        this.teamOptions = [];\r\n      }\r\n    } else {\r\n      this.teamOptions = [];\r\n    }\r\n  }\r\n\r\n  onTeamSelect(event: any): void {\r\n    const selectedTeamId = event.selectedOptions?.[0]?.value;\r\n    const selectedTeamName = event.selectedOptions?.[0]?.name;\r\n    if (selectedTeamId) {\r\n      this.selectedTeam = selectedTeamId;\r\n      this.selectedTeamName = selectedTeamName;\r\n      this.addRealmForm.patchValue({ teamId: selectedTeamId });\r\n    }\r\n  }\r\n\r\n  createRealm() {\r\n    if(this.addRealmForm.valid) {\r\n      const {realmId, realmName, teamId} = this.addRealmForm.value;\r\n\r\n      if(this.isRealmUpdateMode) {\r\n        this.realmService.updateRealm(realmName, teamId, realmId).subscribe({\r\n          next: (res: any) => {\r\n            this.allRealms.forEach(r => {\r\n              if(r.id == res.realmId) {\r\n                r.title = res.realmName;\r\n                r.tags = [res.orgName, res.domainName, res.projectName, res.teamName];\r\n                r.orgId = res.orgId;\r\n                r.orgName = res.orgName;\r\n                r.domainId = res.domainId;\r\n                r.domainName = res.domainName;\r\n                r.projectId = res.projectId;\r\n                r.projectName = res.projectName;\r\n                r.teamId = res.teamId;\r\n                r.teamName = res.teamName;\r\n              }\r\n            })\r\n            this.filteredRealms = [...this.allRealms];\r\n            this.updateDisplayedRealms();\r\n  \r\n            // Show success popup\r\n            this.iconName = 'check-circle';\r\n            this.popupTitle = 'Success';\r\n            this.popupMessage = 'Realm updated successfully.';\r\n            this.submissionSuccess = true;\r\n            this.showSuccessPopup = true;\r\n            this.showRealmPopup = false;\r\n          },\r\n          error: e => {\r\n            console.error(e)\r\n            this.iconName = 'alert-circle';\r\n            this.popupTitle = 'Error';\r\n            this.popupMessage = 'An unexpected error occurred.';\r\n            this.submissionSuccess = false;\r\n            this.showSuccessPopup = true;\r\n            this.showRealmPopup = false;\r\n          }\r\n        })\r\n      } else {\r\n        this.realmService.createRealm(realmName, teamId).subscribe({\r\n          next: (res: any) => {\r\n            this.allRealms.push({\r\n              id: res.realmId,\r\n              title: res.realmName,\r\n              tags: [res.orgName, res.domainName, res.projectName, res.teamName],\r\n              orgId: res.orgId,\r\n              orgName: res.orgName,\r\n              domainId: res.domainId,\r\n              domainName: res.domainName,\r\n              projectId: res.projectId,\r\n              projectName: res.projectName,\r\n              teamId: res.teamId,\r\n              teamName: res.teamName,\r\n            })\r\n            this.filteredRealms = [...this.allRealms];\r\n            this.updateDisplayedRealms();\r\n  \r\n            // Show success popup\r\n            this.iconName = 'check-circle';\r\n            this.popupTitle = 'Success';\r\n            this.popupMessage = 'Realm created successfully.';\r\n            this.submissionSuccess = true;\r\n            this.showSuccessPopup = true;\r\n            this.showRealmPopup = false;\r\n          },\r\n          error: e => {\r\n            console.error(e)\r\n            this.iconName = 'alert-circle';\r\n            this.popupTitle = 'Error';\r\n            this.popupMessage = 'An unexpected error occurred.';\r\n            this.submissionSuccess = false;\r\n            this.showSuccessPopup = true;\r\n            this.showRealmPopup = false;\r\n          }\r\n        })\r\n      }\r\n    }\r\n  }\r\n\r\n  closeRealmPopup() {\r\n    this.showRealmPopup = false;\r\n    this.addRealmForm.reset();\r\n    this.isRealmUpdateMode = false;\r\n  }\r\n}\r\n", "<div id=\"realms-container\" class=\"container-fluid\">\r\n  <div id=\"search-filter-container\" class=\"row g-3\">\r\n    <div class=\"col-12 col-md-8 col-lg-9 col-xl-10 search-section\">\r\n      <form [formGroup]=\"searchForm\">\r\n        <ava-textbox\r\n          placeholder='Search \"Realms\"'\r\n          hoverEffect=\"glow\"\r\n          pressedEffect=\"solid\"\r\n          formControlName=\"search\"\r\n        >\r\n          <ava-icon\r\n            slot=\"icon-start\"\r\n            iconName=\"search\"\r\n            [iconSize]=\"16\"\r\n            iconColor=\"var(--color-brand-primary)\"\r\n          >\r\n          </ava-icon>\r\n        </ava-textbox>\r\n      </form>\r\n    </div>\r\n    <div class=\"col-12 col-md-4 col-lg-3 col-xl-2 action-buttons\">\r\n      <ava-button label=\"Create\" variant=\"primary\" size=\"large\" (userClick)=\"onCreateRealmPopup()\" [width]=\"'100%'\"\r\n        class=\"action-button\">\r\n      </ava-button>\r\n    </div>\r\n  </div>\r\n\r\n  <div id=\"realms-card-container\" class=\"row g-3\">\r\n    <ava-text-card\r\n      class=\"col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3 col-xxl-2 mt-5\"\r\n      [type]=\"'create'\"\r\n      [iconName]=\"'plus'\"\r\n      iconColor=\"#144692\"\r\n      title=\"Create Realm\"\r\n      (cardClick)=\"onCreateRealmPopup()\"\r\n      [isLoading]=\"isLoading\"\r\n    >\r\n    </ava-text-card>\r\n\r\n    <!-- No Results Message -->\r\n    <div\r\n      class=\"col-12 d-flex justify-content-center align-items-center py-5\"\r\n      *ngIf=\"!isLoading && filteredRealms.length === 0\"\r\n    >\r\n      <div class=\"text-center\">\r\n        <h5 class=\"text-muted\">No realms found matching your criteria</h5>\r\n      </div>\r\n    </div>\r\n\r\n    <ng-container\r\n      *ngFor=\"\r\n        let realm of isLoading && displayedRealms.length === 0\r\n          ? cardSkeletonPlaceholders\r\n          : displayedRealms\r\n      \"\r\n    >\r\n      <ava-console-card\r\n        class=\"col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3 col-xxl-2 mt-5\"\r\n        [title]=\"realm?.title\"\r\n        categoryIcon=\"plus\"\r\n        categoryTitle=\"Realm\"\r\n        categoryValue=\"2\"\r\n        [author]=\"realm?.owner || 'AAVA'\"\r\n        [actions]=\"defaultActions\"\r\n        (actionClick)=\"onActionClick($event, realm.id)\"\r\n        [skeleton]=\"isLoading\"\r\n      >\r\n        @if(displayedRealms.length) {\r\n          <div class=\"tag--container\">\r\n            @for(tag of realm.tags; track tag){\r\n              <ava-tag [label]=\"tag\" [customStyle]=\"{ background: '#E6F3FF', color: '#292C3D' }\"\r\n                 size=\"sm\" iconColor=\"#000000\"></ava-tag>\r\n              }\r\n          </div>\r\n        }\r\n      </ava-console-card>\r\n    </ng-container>\r\n  </div>\r\n\r\n  <!-- Pagination Footer -->\r\n  <div class=\"row\" *ngIf=\"filteredRealms.length > 0\">\r\n    <div class=\"col-12 d-flex justify-content-center mt-4\">\r\n      <app-page-footer\r\n        [totalItems]=\"filteredRealms.length + 1\"\r\n        [currentPage]=\"currentPage\"\r\n        [itemsPerPage]=\"itemsPerPage\"\r\n        (pageChange)=\"onPageChange($event)\"\r\n      ></app-page-footer>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<!-- Success Popup -->\r\n<ava-popup\r\n  [show]=\"showSuccessPopup\"\r\n  [title]=\"popupTitle\"\r\n  [message]=\"popupMessage\"\r\n  [showHeaderIcon]=\"true\"\r\n  headerIconName=\"{{ iconName }}\"\r\n  iconColor=\"#28a745\"\r\n  [showClose]=\"true\"\r\n  [showCancel]=\"false\"\r\n  [confirmButtonVariant]=\"'primary'\"\r\n  [confirmButtonBackground]=\"'#28a745'\"\r\n  (confirm)=\"onSuccessConfirm()\"\r\n  (closed)=\"closeSuccessPopup()\"\r\n>\r\n</ava-popup>\r\n\r\n<!-- Delete Confirmation Popup -->\r\n<ava-popup\r\n  [show]=\"showDeletePopup\"\r\n  title=\"Delete Realm?\"\r\n  [message]=\"\r\n    'Are you sure you want to delete ?'\r\n  \"\r\n  [showHeaderIcon]=\"true\"\r\n  headerIconName=\"trash\"\r\n  iconColor=\"#dc3545\"\r\n  [showClose]=\"true\"\r\n  [showCancel]=\"true\"\r\n  [showConfirm]=\"true\"\r\n  [confirmButtonLabel]=\"'Delete'\"\r\n  [confirmButtonVariant]=\"'primary'\"\r\n  [confirmButtonBackground]=\"'#dc3545'\"\r\n  (confirm)=\"onConfirmDelete()\"\r\n  (cancel)=\"closeDeletePopup()\"\r\n  (closed)=\"closeDeletePopup()\"\r\n>\r\n</ava-popup>\r\n\r\n<ava-popup [show]=\"showRealmPopup\" [showTitle]=\"false\" [showHeaderIcon]=\"false\" [showClose]=\"false\" [showInlineMessage]=\"false\"\r\n    [showConfirm]=\"false\" message=\"\" [showCancel]=\"false\" [popupWidth]=\"'744px'\">\r\n\r\n    <div class=\"add__realm--container\">\r\n        <form [formGroup]=\"addRealmForm\" class=\"realm__form\">\r\n            <div class=\"form-fields\">\r\n                <div class=\"input__field--wrapper\">\r\n                    <label class=\"filter-label required\">Name of the Realm</label>\r\n                    <ava-textbox class=\"input-field\" formControlName=\"realmName\" id=\"realmName\" name=\"realmName\"\r\n                        placeholder=\"Enter Realm Name\" [required]=\"true\"\r\n                        [fullWidth]=\"false\" size=\"md\">\r\n                    </ava-textbox>\r\n                </div>\r\n                <div class=\"input__field--wrapper\"></div>\r\n            </div>\r\n\r\n            <div class=\"form-fields\">\r\n                <div class=\"input__field--wrapper\">\r\n                    <label class=\"filter-label required\">Choose Organization</label>\r\n                    <ava-dropdown [dropdownTitle]=\"'Select Organization'\" [options]=\"orgOptions\"\r\n                        [selectedValue]=\"selectedOrgName\" [disabled]=\"false\" (selectionChange)=\"onOrgSelect($event)\"\r\n                        [search]=\"true\" [enableSearch]=\"true\"></ava-dropdown>\r\n                </div>\r\n                <div class=\"input__field--wrapper\">\r\n                    <label class=\"filter-label required\">Choose Domain</label>\r\n                    <ava-dropdown [dropdownTitle]=\"'Select Domain'\" [options]=\"domainOptions\"\r\n                        [selectedValue]=\"selectedDomainName\" [disabled]=\"!selectedOrg\"\r\n                        (selectionChange)=\"onDomainSelect($event)\" [search]=\"true\" [enableSearch]=\"true\"></ava-dropdown>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"form-fields\">\r\n                <div class=\"input__field--wrapper\">\r\n                    <label class=\"filter-label required\">Choose Project</label>\r\n                    <ava-dropdown [dropdownTitle]=\"'Select Project'\" [options]=\"projectOptions\"\r\n                        [selectedValue]=\"selectedProjectName\" [disabled]=\"!selectedDomain\"\r\n                        (selectionChange)=\"onProjectSelect($event)\" [search]=\"true\"\r\n                        [enableSearch]=\"true\"></ava-dropdown>\r\n                </div>\r\n                <div class=\"input__field--wrapper\">\r\n                    <label class=\"filter-label required\">Choose Team</label>\r\n                    <ava-dropdown [dropdownTitle]=\"'Select Team'\" [options]=\"teamOptions\"\r\n                        [selectedValue]=\"selectedTeamName\" [disabled]=\"!selectedProject\"\r\n                        (selectionChange)=\"onTeamSelect($event)\" [search]=\"true\" [enableSearch]=\"true\"></ava-dropdown>\r\n                </div>\r\n            </div>\r\n        </form>\r\n        <div class=\"button__container\">\r\n            <ava-button label=\"Cancel\" variant=\"secondary\" size=\"large\" (userClick)=\"closeRealmPopup()\" [width]=\"'100%'\"\r\n                class=\"action-button\">\r\n            </ava-button>\r\n            <ava-button [label]=\"isRealmUpdateMode ? 'Update' : 'Create'\" variant=\"primary\" size=\"large\" (userClick)=\"createRealm()\" [width]=\"'100%'\"\r\n                class=\"action-button\" [disabled]=\"!addRealmForm.valid\">\r\n            </ava-button>\r\n        </div>\r\n    </div>\r\n</ava-popup>\r\n"], "mappings": "AACA,SAAiCA,mBAAmB,EAAEC,UAAU,QAAQ,gBAAgB;AAExF,SAASC,eAAe,EAAEC,mBAAmB,EAAEC,eAAe,EAAEC,iBAAiB,EAAkBC,aAAa,EAAEC,cAAc,EAAEC,iBAAiB,QAAQ,wBAAwB;AACnL,SAA4BC,oBAAoB,QAAQ,gFAAgF;AAExI,SAASC,OAAO,EAAEC,SAAS,EAAEC,GAAG,EAAEC,SAAS,EAAEC,YAAY,EAAEC,oBAAoB,QAAQ,MAAM;AAC7F,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,mBAAmB,QAAQ,8EAA8E;;;;;;;;;;;;;;ICoC1GC,EALJ,CAAAC,cAAA,cAGC,cAC0B,aACA;IAAAD,EAAA,CAAAE,MAAA,6CAAsC;IAEjEF,EAFiE,CAAAG,YAAA,EAAK,EAC9D,EACF;;;;;IAuBIH,EAAA,CAAAI,SAAA,kBAC2C;;;;IADpBJ,EAAd,CAAAK,UAAA,UAAAC,MAAA,CAAa,gBAAAN,EAAA,CAAAO,eAAA,IAAAC,GAAA,EAA4D;;;;;IAFtFR,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAAS,gBAAA,IAAAC,gEAAA,uBAAAV,EAAA,CAAAW,yBAAA,CAGG;IACLX,EAAA,CAAAG,YAAA,EAAM;;;;IAJJH,EAAA,CAAAY,SAAA,EAGG;IAHHZ,EAAA,CAAAa,UAAA,CAAAC,QAAA,CAAAC,IAAA,CAGG;;;;;;IAvBXf,EAAA,CAAAgB,uBAAA,GAMC;IACChB,EAAA,CAAAC,cAAA,2BAUC;IAFCD,EAAA,CAAAiB,UAAA,yBAAAC,qFAAAC,MAAA;MAAA,MAAAL,QAAA,GAAAd,EAAA,CAAAoB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAvB,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAAeF,MAAA,CAAAG,aAAA,CAAAP,MAAA,EAAAL,QAAA,CAAAa,EAAA,CAA+B;IAAA,EAAC;IAG/C3B,EAAA,CAAA4B,UAAA,IAAAC,0DAAA,kBAA6B;IAQ/B7B,EAAA,CAAAG,YAAA,EAAmB;;;;;;IAjBjBH,EAAA,CAAAY,SAAA,EAAsB;IAOtBZ,EAPA,CAAAK,UAAA,UAAAS,QAAA,kBAAAA,QAAA,CAAAgB,KAAA,CAAsB,YAAAhB,QAAA,kBAAAA,QAAA,CAAAiB,KAAA,YAIW,YAAAR,MAAA,CAAAS,cAAA,CACP,aAAAT,MAAA,CAAAU,SAAA,CAEJ;IAEtBjC,EAAA,CAAAY,SAAA,EAOC;IAPDZ,EAAA,CAAAkC,aAAA,CAAAX,MAAA,CAAAY,eAAA,CAAAC,MAAA,UAOC;;;;;;IAQHpC,EAFJ,CAAAC,cAAA,cAAmD,cACM,0BAMpD;IADCD,EAAA,CAAAiB,UAAA,wBAAAoB,0EAAAlB,MAAA;MAAAnB,EAAA,CAAAoB,aAAA,CAAAkB,GAAA;MAAA,MAAAf,MAAA,GAAAvB,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAAcF,MAAA,CAAAgB,YAAA,CAAApB,MAAA,CAAoB;IAAA,EAAC;IAGzCnB,EAFK,CAAAG,YAAA,EAAkB,EACf,EACF;;;;IANAH,EAAA,CAAAY,SAAA,GAAwC;IAExCZ,EAFA,CAAAK,UAAA,eAAAkB,MAAA,CAAAiB,cAAA,CAAAJ,MAAA,KAAwC,gBAAAb,MAAA,CAAAkB,WAAA,CACb,iBAAAlB,MAAA,CAAAmB,YAAA,CACE;;;ADnDrC,WAAaC,mBAAmB;EAA1B,MAAOA,mBAAmB;IAiEpBC,iBAAA;IACAC,MAAA;IACAC,KAAA;IACAC,EAAA;IACAC,YAAA;IACAC,gBAAA;IArEX;IACCC,gBAAgB,GAAG,KAAK;IACxBC,YAAY,GAAG,EAAE;IACjBC,UAAU,GAAG,EAAE;IACfC,QAAQ,GAAG,MAAM;IACjBC,iBAAiB,GAAG,KAAK;IACzB;IACAC,eAAe,GAAY,KAAK;IAChCC,aAAa,GAAqB,IAAI;IAEtCC,aAAa,GAAqB,CAChC;MAAEC,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAG,CAAE,EAChC;MAAED,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAG,CAAE,EAChC;MAAED,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAG,CAAE,EAChC;MAAED,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAG,CAAE,EAChC;MAAED,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAG,CAAE,CACjC;IAED3B,cAAc,GAAwB,CACpC;MACEL,EAAE,EAAE,MAAM;MACViC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBC,OAAO,EAAE;KACV,EACD;MACEnC,EAAE,EAAE,QAAQ;MACZiC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,aAAa;MACpBC,OAAO,EAAE;KACV,CACF;IACDC,SAAS,GAAgB,EAAE;IAC3BvB,cAAc,GAAgB,EAAE;IAChCL,eAAe,GAAU,EAAE;IAC3B6B,UAAU;IACV/B,SAAS,GAAG,KAAK;IACjBQ,WAAW,GAAW,CAAC;IACvBC,YAAY,GAAW,EAAE;IACzBuB,UAAU,GAAW,CAAC;IACZC,QAAQ,GAAG,IAAI3E,OAAO,EAAQ;IACxC4E,YAAY,GAAQ,IAAI;IACxBC,wBAAwB,GAAGC,KAAK,CAAC,EAAE,CAAC;IACpCC,cAAc,GAAY,KAAK;IAC/BC,YAAY;IAEZC,iBAAiB,GAAY,KAAK;IAClCC,aAAa,GAAU,EAAE;IACzBC,UAAU,GAAqB,EAAE;IACjCC,aAAa,GAAqB,EAAE;IACpCC,cAAc,GAAqB,EAAE;IACrCC,WAAW,GAAqB,EAAE;IAElCC,eAAe,GAAW,EAAE;IAC5BC,kBAAkB,GAAW,EAAE;IAC/BC,mBAAmB,GAAW,EAAE;IAChCC,gBAAgB,GAAW,EAAE;IAE7BC,WAAW,GAAW,EAAE;IACxBC,cAAc,GAAW,EAAE;IAC3BC,eAAe,GAAW,EAAE;IAC5BC,YAAY,GAAW,EAAE;IAEzBC,YACU1C,iBAAoC,EACpCC,MAAc,EACdC,KAAqB,EACrBC,EAAe,EACfC,YAA0B,EAC1BC,gBAAkC;MALlC,KAAAL,iBAAiB,GAAjBA,iBAAiB;MACjB,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,KAAK,GAALA,KAAK;MACL,KAAAC,EAAE,GAAFA,EAAE;MACF,KAAAC,YAAY,GAAZA,YAAY;MACZ,KAAAC,gBAAgB,GAAhBA,gBAAgB;MAExB,IAAI,CAACe,UAAU,GAAG,IAAI,CAACjB,EAAE,CAACwC,KAAK,CAAC;QAC9BC,MAAM,EAAE,CAAC,EAAE;OACZ,CAAC;IACJ;IAEAC,QAAQA,CAAA;MACN,IAAI,CAACxD,SAAS,GAAG,IAAI;MACrB,IAAI,CAACsC,YAAY,GAAG,IAAI,CAACmB,eAAe,EAAE;MAC1C,IAAI,CAACC,kBAAkB,EAAE;MACzB,IAAI,CAACC,YAAY,EAAE;MACnB,IAAI,CAACC,UAAU,EAAE;IACnB;IAEAH,eAAeA,CAAA;MACb,OAAO,IAAI,CAAC3C,EAAE,CAACwC,KAAK,CAAC;QACnBO,OAAO,EAAE,CAAC,IAAI,CAAC;QACfC,SAAS,EAAE,CAAC,IAAI,EAAE,CAACjH,UAAU,CAACkH,QAAQ,CAAC,CAAC;QACxCC,KAAK,EAAE,CAAC,IAAI,EAAE,CAACnH,UAAU,CAACkH,QAAQ,CAAC,CAAC;QACpCE,QAAQ,EAAE,CAAC,IAAI,EAAE,CAACpH,UAAU,CAACkH,QAAQ,CAAC,CAAC;QACvCG,SAAS,EAAE,CAAC,IAAI,EAAE,CAACrH,UAAU,CAACkH,QAAQ,CAAC,CAAC;QACxCI,MAAM,EAAE,CAAC,IAAI,EAAE,CAACtH,UAAU,CAACkH,QAAQ,CAAC;OACrC,CAAC;IACJ;IAEAJ,YAAYA,CAAA;MACV,IAAI,CAAC5C,YAAY,CAACqD,WAAW,EAAE,CAACC,IAAI,CAAC9G,SAAS,CAAC,IAAI,CAAC0E,QAAQ,CAAC,CAAC,CAACqC,SAAS,CAAC;QACvEC,IAAI,EAAGC,GAAQ,IAAI;UACjB,IAAI,CAAC1C,SAAS,GAAG,IAAI,CAAC2C,2BAA2B,CAACD,GAAG,CAAC;UACtD,IAAI,CAACjE,cAAc,GAAG,CAAC,GAAG,IAAI,CAACuB,SAAS,CAAC;UACzC,IAAI,CAAC4C,qBAAqB,EAAE;UAC5B,IAAI,CAACC,4BAA4B,EAAE;QACrC,CAAC;QACDC,KAAK,EAAEC,CAAC,IAAIC,OAAO,CAACF,KAAK,CAACC,CAAC,CAAC;QAC5BE,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAAC/E,SAAS,GAAG;OAClC,CAAC;IACJ;IAEAM,YAAYA,CAAC0E,IAAY;MACvB,IAAI,CAACxE,WAAW,GAAGwE,IAAI;MACvB,IAAI,CAACN,qBAAqB,EAAE;MAC5B,IAAI,CAAC9D,MAAM,CAACqE,QAAQ,CAAC,EAAE,EAAE;QACvBC,UAAU,EAAE,IAAI,CAACrE,KAAK;QACtBsE,WAAW,EAAE;UAAEH,IAAI,EAAE,IAAI,CAACxE;QAAW,CAAE;QACvC4E,mBAAmB,EAAE;OACtB,CAAC;IACJ;IAEA3F,aAAaA,CACX4F,KAAsD,EACtDxB,OAAe;MAEf,QAAQwB,KAAK,CAACC,QAAQ;QACpB,KAAK,QAAQ;UACX,IAAI,CAACC,WAAW,CAAC1B,OAAO,CAAC;UACzB;QACF,KAAK,MAAM;UACT,IAAI,CAAC2B,kBAAkB,CAAC3B,OAAO,CAAC;UAChC;QACF;UACE;MACJ;IACF;IAGAY,2BAA2BA,CAACgB,IAAS;MACnC,OAAOA,IAAI,CAACjI,GAAG,CAAEkI,IAAS,IAAI;QAC5B,MAAMC,MAAM,GAAc;UACxBjG,EAAE,EAAEgG,IAAI,CAAC7B,OAAO,IAAI,EAAE;UACtBhE,KAAK,EAAE6F,IAAI,CAAC5B,SAAS;UACrBhF,IAAI,EAAE,CAAC4G,IAAI,CAACE,OAAO,EAAEF,IAAI,CAACG,UAAU,EAAEH,IAAI,CAACI,WAAW,EAAEJ,IAAI,CAACK,QAAQ,CAAC;UACtE/B,KAAK,EAAE0B,IAAI,CAAC1B,KAAK;UACjB4B,OAAO,EAAEF,IAAI,CAACE,OAAO;UACrB3B,QAAQ,EAAEyB,IAAI,CAACzB,QAAQ;UACvB4B,UAAU,EAAEH,IAAI,CAACG,UAAU;UAC3B3B,SAAS,EAAEwB,IAAI,CAACxB,SAAS;UACzB4B,WAAW,EAAEJ,IAAI,CAACI,WAAW;UAC7B3B,MAAM,EAAEuB,IAAI,CAACvB,MAAM;UACnB4B,QAAQ,EAAEL,IAAI,CAACK;SAChB;QAED,IAAIL,IAAI,CAACM,aAAa,EAAE;UACtBL,MAAM,CAAC7F,KAAK,GAAG4F,IAAI,CAACM,aAAa;QACnC;QAEA,OAAOL,MAAM;MACf,CAAC,CAAC;IACJ;IAEAH,kBAAkBA,CAAC3B,OAAe;MAChC,IAAI,CAACtB,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAAC0D,iBAAiB,CAACpC,OAAO,CAAC;IACjC;IAEAoC,iBAAiBA,CAACpC,OAAe;MAC/B,MAAMqC,KAAK,GAAG,IAAI,CAACpE,SAAS,CAACqE,IAAI,CAAEC,CAAM,IAAKA,CAAC,CAAC1G,EAAE,KAAKmE,OAAO,CAAC;MAC/D,IAAI,CAACqC,KAAK,EAAE;MACZ,IAAI,CAAC5D,YAAY,CAAC+D,UAAU,CAAC;QAC3BxC,OAAO,EAAEqC,KAAK,CAACxG,EAAE;QACjBoE,SAAS,EAAEoC,KAAK,CAACrG,KAAK;QACtBmE,KAAK,EAAEkC,KAAK,CAAClC,KAAK;QAClBC,QAAQ,EAAEiC,KAAK,CAACjC,QAAQ;QACxBC,SAAS,EAAEgC,KAAK,CAAChC,SAAS;QAC1BC,MAAM,EAAE+B,KAAK,CAAC/B;OACf,CAAC;MAEF;MACA,IAAI,CAAClB,WAAW,GAAGiD,KAAK,CAAClC,KAAK,CAACsC,QAAQ,EAAE;MACzC,IAAI,CAACpD,cAAc,GAAGgD,KAAK,CAACjC,QAAQ,CAACqC,QAAQ,EAAE;MAC/C,IAAI,CAACnD,eAAe,GAAG+C,KAAK,CAAChC,SAAS,CAACoC,QAAQ,EAAE;MAEjD;MACA,IAAI,CAACzD,eAAe,GAAGqD,KAAK,CAACN,OAAO;MACpC,IAAI,CAAC9C,kBAAkB,GAAGoD,KAAK,CAACL,UAAU;MAC1C,IAAI,CAAC9C,mBAAmB,GAAGmD,KAAK,CAACJ,WAAW;MAC5C,IAAI,CAAC9C,gBAAgB,GAAGkD,KAAK,CAACH,QAAQ;MAEtC,IAAI,IAAI,CAAC9C,WAAW,EAAE;QACpB,IAAI,CAACsD,WAAW,CAAC,IAAI,CAACtD,WAAW,CAAC;QAClC,IAAI,IAAI,CAACC,cAAc,EAAE;UACvB,IAAI,CAACsD,YAAY,CAAC,IAAI,CAACtD,cAAc,CAAC;UACtC,IAAI,IAAI,CAACC,eAAe,EAAE;YACxB,IAAI,CAACsD,SAAS,CAAC,IAAI,CAACtD,eAAe,CAAC;UACtC;QACF;MACF;MACA,IAAI,CAACd,cAAc,GAAG,IAAI;IAC5B;IAEA;IACQkD,WAAWA,CAAC1B,OAAe;MACjC,MAAMqC,KAAK,GAAG,IAAI,CAACpE,SAAS,CAACqE,IAAI,CAAEC,CAAM,IAAKA,CAAC,CAAC1G,EAAE,KAAKmE,OAAO,CAAC;MAC/D,IAAI,CAACqC,KAAK,EAAE;MACZ,IAAI,CAAC3E,aAAa,GAAG2E,KAAK;MAC1B,IAAI,CAAC5E,eAAe,GAAG,IAAI;IAC7B;IAEA;IACAoF,eAAeA,CAAA;MACb,IAAI,CAAC,IAAI,CAACnF,aAAa,EAAE7B,EAAE,EAAE;MAE7B,MAAMmE,OAAO,GAAG,IAAI,CAACtC,aAAa,CAAC7B,EAAE;MAErC,IAAI,CAACqB,YAAY,CAACwE,WAAW,CAAC1B,OAAO,CAAC,CAACS,SAAS,CAAC;QAC/CC,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAIA,GAAG,EAAE;YACP;YACA,IAAI,CAAC1C,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC6E,MAAM,CAAEP,CAAC,IAAKA,CAAC,CAAC1G,EAAE,KAAKmE,OAAO,CAAC;YAC/D,IAAI,CAACtD,cAAc,GAAG,IAAI,CAACA,cAAc,CAACoG,MAAM,CAC7CP,CAAC,IAAKA,CAAC,CAAC1G,EAAE,KAAKmE,OAAO,CACxB;YACD,IAAI,CAACa,qBAAqB,EAAE;YAE5B;YACA,IAAI,CAACtD,QAAQ,GAAG,cAAc;YAC9B,IAAI,CAACD,UAAU,GAAG,SAAS;YAC3B,IAAI,CAACD,YAAY,GAAG,6BAA6B;YACjD,IAAI,CAACG,iBAAiB,GAAG,IAAI;YAC7B,IAAI,CAACJ,gBAAgB,GAAG,IAAI;UAC9B;UAEA,IAAI,CAAC2F,gBAAgB,EAAE;QACzB,CAAC;QACDhC,KAAK,EAAGiC,GAAG,IAAI;UACb/B,OAAO,CAACF,KAAK,CAAC,yBAAyB,EAAEiC,GAAG,CAAC;UAC7C,IAAI,CAACzF,QAAQ,GAAG,cAAc;UAC9B,IAAI,CAACD,UAAU,GAAG,OAAO;UACzB,IAAI,CAACD,YAAY,GAAG,+BAA+B;UACnD,IAAI,CAACG,iBAAiB,GAAG,KAAK;UAC9B,IAAI,CAACJ,gBAAgB,GAAG,IAAI;UAC5B,IAAI,CAAC2F,gBAAgB,EAAE;QACzB;OACD,CAAC;IACJ;IAEA;IACAA,gBAAgBA,CAAA;MACd,IAAI,CAACtF,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACC,aAAa,GAAG,IAAI;IAC3B;IAEA;IACAuF,gBAAgBA,CAAA;MACd,IAAI,CAACC,iBAAiB,EAAE;IAC1B;IAEA;IACAA,iBAAiBA,CAAA;MACf,IAAI,CAAC9F,gBAAgB,GAAG,KAAK;MAC7B,IAAI,CAACE,UAAU,GAAG,EAAE;MACpB,IAAI,CAACD,YAAY,GAAG,EAAE;MACtB,IAAI,CAACE,QAAQ,GAAG,MAAM;IACxB;IAEQuD,4BAA4BA,CAAA;MAClC,MAAMqC,SAAS,GAAG,IAAI,CAACnG,KAAK,CAACoG,QAAQ,CAACC,aAAa,CAACC,GAAG,CAAC,MAAM,CAAC;MAC/D,IAAIH,SAAS,EAAE;QACb,MAAMhC,IAAI,GAAGoC,QAAQ,CAACJ,SAAS,EAAE,EAAE,CAAC;QACpC,IAAI,CAACK,KAAK,CAACrC,IAAI,CAAC,EAAE,IAAI,CAACxE,WAAW,GAAGwE,IAAI;MAC3C;IACF;IAEQtB,kBAAkBA,CAAA;MACxB,IAAI,CAAC3B,UAAU,CACZoF,GAAG,CAAC,QAAQ,CAAE,CACdG,YAAY,CAACjD,IAAI,CAChB5G,SAAS,CAAC,EAAE,CAAC,EACbC,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBH,GAAG,CAAEkE,KAAK,IAAKA,KAAK,EAAE6F,WAAW,EAAE,IAAI,EAAE,CAAC,EAC1ChK,SAAS,CAAC,IAAI,CAAC0E,QAAQ,CAAC,CACzB,CACAqC,SAAS,CAAEkD,UAAU,IAAI;QACxB,IAAI,CAACC,YAAY,CAACD,UAAU,CAAC;MAC/B,CAAC,CAAC;IACN;IAEQ9C,qBAAqBA,CAAA;MAC3B,IAAI,CAACjE,YAAY,GAAG,IAAI,CAACD,WAAW,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE;MACpD,MAAM;QAAEkH,cAAc;QAAE1F;MAAU,CAAE,GAClC,IAAI,CAACrB,iBAAiB,CAACgH,iBAAiB,CACtC,IAAI,CAACpH,cAAc,EACnB,IAAI,CAACC,WAAW,EAChB,IAAI,CAACC,YAAY,CAClB;MACH,IAAI,CAACP,eAAe,GAAGwH,cAAc;MACrC,IAAI,CAAC1F,UAAU,GAAGA,UAAU;IAC9B;IAEQyF,YAAYA,CAACD,UAAkB;MACrC,IAAI,CAACjH,cAAc,GAAG,IAAI,CAACuB,SAAS,CAAC6E,MAAM,CAAET,KAAU,IAAI;QACzD,MAAM0B,UAAU,GAAG1B,KAAK,CAACrG,KAAK,EAAE0H,WAAW,EAAE,CAACM,QAAQ,CAACL,UAAU,CAAC;QAClE,MAAMM,gBAAgB,GAAG5B,KAAK,CAAC6B,WAAW,EACtCR,WAAW,EAAE,CACdM,QAAQ,CAACL,UAAU,CAAC;QACvB,MAAMQ,QAAQ,GAAG9B,KAAK,CAACpH,IAAI,EAAEmJ,IAAI,CAAEC,GAAQ,IACzCA,GAAG,CAACtG,KAAK,EAAE2F,WAAW,EAAE,CAACM,QAAQ,CAACL,UAAU,CAAC,CAC9C;QACD,OAAOI,UAAU,IAAIE,gBAAgB,IAAIE,QAAQ;MACnD,CAAC,CAAC;MACF,IAAI,CAACxH,WAAW,GAAG,CAAC;MACpB,IAAI,CAACkE,qBAAqB,EAAE;IAC9B;IAEAyD,kBAAkBA,CAAA;MAChB,IAAI,CAAC9F,cAAc,GAAG,IAAI;IAC5B;IAEAuB,UAAUA,CAAA;MACR,IAAG,IAAI,CAACpB,aAAa,CAACrC,MAAM,KAAK,CAAC,EAAE;QAClC,IAAI,CAACa,gBAAgB,CAACoH,wBAAwB,EAAE,CAAC9D,SAAS,CAAC;UACzDC,IAAI,EAAGkB,IAAS,IAAI;YAClB,IAAI,CAACjD,aAAa,GAAGiD,IAAI;YACzB,IAAI,CAAC4C,iBAAiB,EAAE;UAC1B,CAAC;UACDzD,KAAK,EAAEC,CAAC,IAAIC,OAAO,CAACF,KAAK,CAACC,CAAC;SAC5B,CAAC;MACJ;IACF;IAEAwD,iBAAiBA,CAAA;MACf,IAAI,CAAC5F,UAAU,GAAG,IAAI,CAACD,aAAa,CAAChF,GAAG,CAAE8K,GAAG,KAAM;QACjD7G,IAAI,EAAE6G,GAAG,CAACC,gBAAgB;QAC1B7G,KAAK,EAAE4G,GAAG,CAACtE,KAAK,CAACsC,QAAQ;OAC1B,CAAC,CAAC;IACL;IAEAkC,WAAWA,CAACnD,KAAU;MACpB,MAAMoD,aAAa,GAAGpD,KAAK,CAACqD,eAAe,GAAG,CAAC,CAAC,EAAEhH,KAAK;MACvD,MAAMmB,eAAe,GAAGwC,KAAK,CAACqD,eAAe,GAAG,CAAC,CAAC,EAAEjH,IAAI;MACxD,IAAIgH,aAAa,EAAE;QACjB,IAAI,CAACxF,WAAW,GAAGwF,aAAa;QAChC,IAAI,CAAC5F,eAAe,GAAGA,eAAe;QACtC,IAAI,CAACP,YAAY,CAAC+D,UAAU,CAAC;UAAErC,KAAK,EAAEyE,aAAa;UAAExE,QAAQ,EAAE,EAAE;UAAEC,SAAS,EAAE,EAAE;UAAEC,MAAM,EAAE;QAAE,CAAG,CAAC;QAChG,IAAI,CAACoC,WAAW,CAACkC,aAAa,CAAC;QAC/B,IAAI,CAACvF,cAAc,GAAG,EAAE;QACxB,IAAI,CAACC,eAAe,GAAG,EAAE;QACzB,IAAI,CAACC,YAAY,GAAG,EAAE;QACtB,IAAI,CAACN,kBAAkB,GAAG,EAAE;QAC5B,IAAI,CAACC,mBAAmB,GAAG,EAAE;QAC7B,IAAI,CAACC,gBAAgB,GAAG,EAAE;QAC1B,IAAI,CAACL,cAAc,GAAG,EAAE;QACxB,IAAI,CAACC,WAAW,GAAG,EAAE;MACvB;IACF;IAEA2D,WAAWA,CAACvC,KAAa;MACvB,MAAMsE,GAAG,GAAG,IAAI,CAAC9F,aAAa,CAAC2D,IAAI,CAAEwC,CAAC,IAAKA,CAAC,CAAC3E,KAAK,CAACsC,QAAQ,EAAE,KAAKtC,KAAK,CAAC;MACxE,IAAIsE,GAAG,EAAE;QACP,IAAI,CAAC5F,aAAa,GAAG4F,GAAG,CAACM,OAAO,CAACpL,GAAG,CAAEqL,MAAW,KAAM;UACrDpH,IAAI,EAAEoH,MAAM,CAAChD,UAAU;UACvBnE,KAAK,EAAEmH,MAAM,CAAC5E,QAAQ,CAACqC,QAAQ;SAChC,CAAC,CAAC;MACL,CAAC,MAAM;QACL,IAAI,CAAC5D,aAAa,GAAG,EAAE;MACzB;IACF;IAECoG,cAAcA,CAACzD,KAAU;MACxB,MAAM0D,gBAAgB,GAAG1D,KAAK,CAACqD,eAAe,GAAG,CAAC,CAAC,EAAEhH,KAAK;MAC1D,MAAMoB,kBAAkB,GAAGuC,KAAK,CAACqD,eAAe,GAAG,CAAC,CAAC,EAAEjH,IAAI;MAC3D,IAAIsH,gBAAgB,EAAE;QACpB,IAAI,CAAC7F,cAAc,GAAG6F,gBAAgB;QACtC,IAAI,CAACjG,kBAAkB,GAAGA,kBAAkB;QAC5C,IAAI,CAACR,YAAY,CAAC+D,UAAU,CAAC;UAAEpC,QAAQ,EAAE8E,gBAAgB;UAAE7E,SAAS,EAAE,EAAE;UAAEC,MAAM,EAAE;QAAE,CAAE,CAAC;QACvF,IAAI,CAACqC,YAAY,CAACuC,gBAAgB,CAAC;QACnC,IAAI,CAAC5F,eAAe,GAAG,EAAE;QACzB,IAAI,CAACC,YAAY,GAAG,EAAE;QACtB,IAAI,CAACL,mBAAmB,GAAG,EAAE;QAC7B,IAAI,CAACC,gBAAgB,GAAG,EAAE;QAC1B,IAAI,CAACJ,WAAW,GAAG,EAAE;MACvB;IACF;IAEA4D,YAAYA,CAACvC,QAAgB;MAC3B,MAAMqE,GAAG,GAAG,IAAI,CAAC9F,aAAa,CAAC2D,IAAI,CAAEwC,CAAC,IACpCA,CAAC,CAACC,OAAO,CAACX,IAAI,CAAEe,CAAM,IAAKA,CAAC,CAAC/E,QAAQ,CAACqC,QAAQ,EAAE,KAAKrC,QAAQ,CAAC,CAC/D;MACD,IAAIqE,GAAG,EAAE;QACP,MAAMO,MAAM,GAAGP,GAAG,CAACM,OAAO,CAACzC,IAAI,CAC5B6C,CAAM,IAAKA,CAAC,CAAC/E,QAAQ,CAACqC,QAAQ,EAAE,KAAKrC,QAAQ,CAC/C;QACD,IAAI4E,MAAM,EAAE;UACV,IAAI,CAAClG,cAAc,GAAGkG,MAAM,CAACI,QAAQ,CAACzL,GAAG,CAAE0L,OAAY,KAAM;YAC3DzH,IAAI,EAAEyH,OAAO,CAACpD,WAAW;YACzBpE,KAAK,EAAEwH,OAAO,CAAChF,SAAS,CAACoC,QAAQ;WAClC,CAAC,CAAC;QACL,CAAC,MAAM;UACL,IAAI,CAAC3D,cAAc,GAAG,EAAE;QAC1B;MACF,CAAC,MAAM;QACL,IAAI,CAACA,cAAc,GAAG,EAAE;MAC1B;IACF;IAEAwG,eAAeA,CAAC9D,KAAU;MACxB,MAAM+D,iBAAiB,GAAG/D,KAAK,CAACqD,eAAe,GAAG,CAAC,CAAC,EAAEhH,KAAK;MAC3D,MAAMqB,mBAAmB,GAAGsC,KAAK,CAACqD,eAAe,GAAG,CAAC,CAAC,EAAEjH,IAAI;MAC5D,IAAI2H,iBAAiB,EAAE;QACrB,IAAI,CAACjG,eAAe,GAAGiG,iBAAiB;QACxC,IAAI,CAACrG,mBAAmB,GAAGA,mBAAmB;QAC9C,IAAI,CAACT,YAAY,CAAC+D,UAAU,CAAC;UAAEnC,SAAS,EAAEkF,iBAAiB;UAAEC,IAAI,EAAE;QAAE,CAAE,CAAC;QACxE,IAAI,CAAC5C,SAAS,CAAC2C,iBAAiB,CAAC;QACjC,IAAI,CAAChG,YAAY,GAAG,EAAE;QACtB,IAAI,CAACJ,gBAAgB,GAAG,EAAE;MAC5B;IACF;IAEAyD,SAASA,CAACvC,SAAiB;MACzB,MAAMoE,GAAG,GAAG,IAAI,CAAC9F,aAAa,CAAC2D,IAAI,CAAEwC,CAAC,IACpCA,CAAC,CAACC,OAAO,CAACX,IAAI,CAAEe,CAAM,IACpBA,CAAC,CAACC,QAAQ,CAAChB,IAAI,CAAE7B,CAAM,IAAKA,CAAC,CAAClC,SAAS,CAACoC,QAAQ,EAAE,KAAKpC,SAAS,CAAC,CAClE,CACF;MACD,IAAIoE,GAAG,EAAE;QACP,MAAMO,MAAM,GAAGP,GAAG,CAACM,OAAO,CAACzC,IAAI,CAAE6C,CAAM,IACrCA,CAAC,CAACC,QAAQ,CAAChB,IAAI,CAAE7B,CAAM,IAAKA,CAAC,CAAClC,SAAS,CAACoC,QAAQ,EAAE,KAAKpC,SAAS,CAAC,CAClE;QACD,IAAI2E,MAAM,EAAE;UACV,MAAMK,OAAO,GAAGL,MAAM,CAACI,QAAQ,CAAC9C,IAAI,CACjCC,CAAM,IAAKA,CAAC,CAAClC,SAAS,CAACoC,QAAQ,EAAE,KAAKpC,SAAS,CACjD;UACD,IAAIgF,OAAO,EAAE;YACX,IAAI,CAACtG,WAAW,GAAGsG,OAAO,CAACI,KAAK,CAAC9L,GAAG,CAAE6L,IAAS,KAAM;cACnD5H,IAAI,EAAE4H,IAAI,CAACtD,QAAQ;cACnBrE,KAAK,EAAE2H,IAAI,CAAClF,MAAM,CAACmC,QAAQ;aAC5B,CAAC,CAAC;UACL,CAAC,MAAM;YACL,IAAI,CAAC1D,WAAW,GAAG,EAAE;UACvB;QACF,CAAC,MAAM;UACL,IAAI,CAACA,WAAW,GAAG,EAAE;QACvB;MACF,CAAC,MAAM;QACL,IAAI,CAACA,WAAW,GAAG,EAAE;MACvB;IACF;IAEA2G,YAAYA,CAAClE,KAAU;MACrB,MAAMmE,cAAc,GAAGnE,KAAK,CAACqD,eAAe,GAAG,CAAC,CAAC,EAAEhH,KAAK;MACxD,MAAMsB,gBAAgB,GAAGqC,KAAK,CAACqD,eAAe,GAAG,CAAC,CAAC,EAAEjH,IAAI;MACzD,IAAI+H,cAAc,EAAE;QAClB,IAAI,CAACpG,YAAY,GAAGoG,cAAc;QAClC,IAAI,CAACxG,gBAAgB,GAAGA,gBAAgB;QACxC,IAAI,CAACV,YAAY,CAAC+D,UAAU,CAAC;UAAElC,MAAM,EAAEqF;QAAc,CAAE,CAAC;MAC1D;IACF;IAEAC,WAAWA,CAAA;MACT,IAAG,IAAI,CAACnH,YAAY,CAACoH,KAAK,EAAE;QAC1B,MAAM;UAAC7F,OAAO;UAAEC,SAAS;UAAEK;QAAM,CAAC,GAAG,IAAI,CAAC7B,YAAY,CAACZ,KAAK;QAE5D,IAAG,IAAI,CAACa,iBAAiB,EAAE;UACzB,IAAI,CAACxB,YAAY,CAAC4I,WAAW,CAAC7F,SAAS,EAAEK,MAAM,EAAEN,OAAO,CAAC,CAACS,SAAS,CAAC;YAClEC,IAAI,EAAGC,GAAQ,IAAI;cACjB,IAAI,CAAC1C,SAAS,CAAC8H,OAAO,CAACC,CAAC,IAAG;gBACzB,IAAGA,CAAC,CAACnK,EAAE,IAAI8E,GAAG,CAACX,OAAO,EAAE;kBACtBgG,CAAC,CAAChK,KAAK,GAAG2E,GAAG,CAACV,SAAS;kBACvB+F,CAAC,CAAC/K,IAAI,GAAG,CAAC0F,GAAG,CAACoB,OAAO,EAAEpB,GAAG,CAACqB,UAAU,EAAErB,GAAG,CAACsB,WAAW,EAAEtB,GAAG,CAACuB,QAAQ,CAAC;kBACrE8D,CAAC,CAAC7F,KAAK,GAAGQ,GAAG,CAACR,KAAK;kBACnB6F,CAAC,CAACjE,OAAO,GAAGpB,GAAG,CAACoB,OAAO;kBACvBiE,CAAC,CAAC5F,QAAQ,GAAGO,GAAG,CAACP,QAAQ;kBACzB4F,CAAC,CAAChE,UAAU,GAAGrB,GAAG,CAACqB,UAAU;kBAC7BgE,CAAC,CAAC3F,SAAS,GAAGM,GAAG,CAACN,SAAS;kBAC3B2F,CAAC,CAAC/D,WAAW,GAAGtB,GAAG,CAACsB,WAAW;kBAC/B+D,CAAC,CAAC1F,MAAM,GAAGK,GAAG,CAACL,MAAM;kBACrB0F,CAAC,CAAC9D,QAAQ,GAAGvB,GAAG,CAACuB,QAAQ;gBAC3B;cACF,CAAC,CAAC;cACF,IAAI,CAACxF,cAAc,GAAG,CAAC,GAAG,IAAI,CAACuB,SAAS,CAAC;cACzC,IAAI,CAAC4C,qBAAqB,EAAE;cAE5B;cACA,IAAI,CAACtD,QAAQ,GAAG,cAAc;cAC9B,IAAI,CAACD,UAAU,GAAG,SAAS;cAC3B,IAAI,CAACD,YAAY,GAAG,6BAA6B;cACjD,IAAI,CAACG,iBAAiB,GAAG,IAAI;cAC7B,IAAI,CAACJ,gBAAgB,GAAG,IAAI;cAC5B,IAAI,CAACoB,cAAc,GAAG,KAAK;YAC7B,CAAC;YACDuC,KAAK,EAAEC,CAAC,IAAG;cACTC,OAAO,CAACF,KAAK,CAACC,CAAC,CAAC;cAChB,IAAI,CAACzD,QAAQ,GAAG,cAAc;cAC9B,IAAI,CAACD,UAAU,GAAG,OAAO;cACzB,IAAI,CAACD,YAAY,GAAG,+BAA+B;cACnD,IAAI,CAACG,iBAAiB,GAAG,KAAK;cAC9B,IAAI,CAACJ,gBAAgB,GAAG,IAAI;cAC5B,IAAI,CAACoB,cAAc,GAAG,KAAK;YAC7B;WACD,CAAC;QACJ,CAAC,MAAM;UACL,IAAI,CAACtB,YAAY,CAAC0I,WAAW,CAAC3F,SAAS,EAAEK,MAAM,CAAC,CAACG,SAAS,CAAC;YACzDC,IAAI,EAAGC,GAAQ,IAAI;cACjB,IAAI,CAAC1C,SAAS,CAACgI,IAAI,CAAC;gBAClBpK,EAAE,EAAE8E,GAAG,CAACX,OAAO;gBACfhE,KAAK,EAAE2E,GAAG,CAACV,SAAS;gBACpBhF,IAAI,EAAE,CAAC0F,GAAG,CAACoB,OAAO,EAAEpB,GAAG,CAACqB,UAAU,EAAErB,GAAG,CAACsB,WAAW,EAAEtB,GAAG,CAACuB,QAAQ,CAAC;gBAClE/B,KAAK,EAAEQ,GAAG,CAACR,KAAK;gBAChB4B,OAAO,EAAEpB,GAAG,CAACoB,OAAO;gBACpB3B,QAAQ,EAAEO,GAAG,CAACP,QAAQ;gBACtB4B,UAAU,EAAErB,GAAG,CAACqB,UAAU;gBAC1B3B,SAAS,EAAEM,GAAG,CAACN,SAAS;gBACxB4B,WAAW,EAAEtB,GAAG,CAACsB,WAAW;gBAC5B3B,MAAM,EAAEK,GAAG,CAACL,MAAM;gBAClB4B,QAAQ,EAAEvB,GAAG,CAACuB;eACf,CAAC;cACF,IAAI,CAACxF,cAAc,GAAG,CAAC,GAAG,IAAI,CAACuB,SAAS,CAAC;cACzC,IAAI,CAAC4C,qBAAqB,EAAE;cAE5B;cACA,IAAI,CAACtD,QAAQ,GAAG,cAAc;cAC9B,IAAI,CAACD,UAAU,GAAG,SAAS;cAC3B,IAAI,CAACD,YAAY,GAAG,6BAA6B;cACjD,IAAI,CAACG,iBAAiB,GAAG,IAAI;cAC7B,IAAI,CAACJ,gBAAgB,GAAG,IAAI;cAC5B,IAAI,CAACoB,cAAc,GAAG,KAAK;YAC7B,CAAC;YACDuC,KAAK,EAAEC,CAAC,IAAG;cACTC,OAAO,CAACF,KAAK,CAACC,CAAC,CAAC;cAChB,IAAI,CAACzD,QAAQ,GAAG,cAAc;cAC9B,IAAI,CAACD,UAAU,GAAG,OAAO;cACzB,IAAI,CAACD,YAAY,GAAG,+BAA+B;cACnD,IAAI,CAACG,iBAAiB,GAAG,KAAK;cAC9B,IAAI,CAACJ,gBAAgB,GAAG,IAAI;cAC5B,IAAI,CAACoB,cAAc,GAAG,KAAK;YAC7B;WACD,CAAC;QACJ;MACF;IACF;IAEA0H,eAAeA,CAAA;MACb,IAAI,CAAC1H,cAAc,GAAG,KAAK;MAC3B,IAAI,CAACC,YAAY,CAAC0H,KAAK,EAAE;MACzB,IAAI,CAACzH,iBAAiB,GAAG,KAAK;IAChC;;uCAliBW7B,mBAAmB,EAAA3C,EAAA,CAAAkM,iBAAA,CAAAC,EAAA,CAAAC,iBAAA,GAAApM,EAAA,CAAAkM,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAtM,EAAA,CAAAkM,iBAAA,CAAAG,EAAA,CAAAE,cAAA,GAAAvM,EAAA,CAAAkM,iBAAA,CAAAM,EAAA,CAAAC,WAAA,GAAAzM,EAAA,CAAAkM,iBAAA,CAAAQ,EAAA,CAAAC,YAAA,GAAA3M,EAAA,CAAAkM,iBAAA,CAAAU,EAAA,CAAAC,gBAAA;IAAA;;YAAnBlK,mBAAmB;MAAAmK,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC9BxBpN,EAJR,CAAAC,cAAA,aAAmD,aACC,aACe,cAC9B,qBAM5B;UACCD,EAAA,CAAAI,SAAA,kBAMW;UAGjBJ,EAFI,CAAAG,YAAA,EAAc,EACT,EACH;UAEJH,EADF,CAAAC,cAAA,aAA8D,oBAEpC;UADkCD,EAAA,CAAAiB,UAAA,uBAAAqM,6DAAA;YAAA,OAAaD,GAAA,CAAAjD,kBAAA,EAAoB;UAAA,EAAC;UAIhGpK,EAFI,CAAAG,YAAA,EAAa,EACT,EACF;UAGJH,EADF,CAAAC,cAAA,aAAgD,uBAS7C;UAFCD,EAAA,CAAAiB,UAAA,uBAAAsM,gEAAA;YAAA,OAAaF,GAAA,CAAAjD,kBAAA,EAAoB;UAAA,EAAC;UAGpCpK,EAAA,CAAAG,YAAA,EAAgB;UAYhBH,EATA,CAAA4B,UAAA,KAAA4L,mCAAA,kBAGC,KAAAC,4CAAA,2BAYA;UAsBHzN,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAA4B,UAAA,KAAA8L,mCAAA,kBAAmD;UAUrD1N,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,qBAaC;UADCD,EADA,CAAAiB,UAAA,qBAAA0M,2DAAA;YAAA,OAAWN,GAAA,CAAAtE,gBAAA,EAAkB;UAAA,EAAC,oBAAA6E,0DAAA;YAAA,OACpBP,GAAA,CAAArE,iBAAA,EAAmB;UAAA,EAAC;UAEhChJ,EAAA,CAAAG,YAAA,EAAY;UAGZH,EAAA,CAAAC,cAAA,qBAkBC;UADCD,EAFA,CAAAiB,UAAA,qBAAA4M,2DAAA;YAAA,OAAWR,GAAA,CAAA1E,eAAA,EAAiB;UAAA,EAAC,oBAAAmF,0DAAA;YAAA,OACnBT,GAAA,CAAAxE,gBAAA,EAAkB;UAAA,EAAC,oBAAAkF,0DAAA;YAAA,OACnBV,GAAA,CAAAxE,gBAAA,EAAkB;UAAA,EAAC;UAE/B7I,EAAA,CAAAG,YAAA,EAAY;UASQH,EAPpB,CAAAC,cAAA,qBACiF,eAE1C,gBACsB,eACxB,eACc,iBACM;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC9DH,EAAA,CAAAI,SAAA,uBAGc;UAClBJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAI,SAAA,eAAyC;UAC7CJ,EAAA,CAAAG,YAAA,EAAM;UAIEH,EAFR,CAAAC,cAAA,eAAyB,eACc,iBACM;UAAAD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAChEH,EAAA,CAAAC,cAAA,wBAE0C;UADeD,EAAA,CAAAiB,UAAA,6BAAA+M,sEAAA7M,MAAA;YAAA,OAAmBkM,GAAA,CAAA5C,WAAA,CAAAtJ,MAAA,CAAmB;UAAA,EAAC;UAEpGnB,EAD8C,CAAAG,YAAA,EAAe,EACvD;UAEFH,EADJ,CAAAC,cAAA,eAAmC,iBACM;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC1DH,EAAA,CAAAC,cAAA,wBAEqF;UAAjFD,EAAA,CAAAiB,UAAA,6BAAAgN,sEAAA9M,MAAA;YAAA,OAAmBkM,GAAA,CAAAtC,cAAA,CAAA5J,MAAA,CAAsB;UAAA,EAAC;UAEtDnB,EAF6F,CAAAG,YAAA,EAAe,EAClG,EACJ;UAIEH,EAFR,CAAAC,cAAA,eAAyB,eACc,iBACM;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3DH,EAAA,CAAAC,cAAA,wBAG0B;UADtBD,EAAA,CAAAiB,UAAA,6BAAAiN,sEAAA/M,MAAA;YAAA,OAAmBkM,GAAA,CAAAjC,eAAA,CAAAjK,MAAA,CAAuB;UAAA,EAAC;UAEnDnB,EAD8B,CAAAG,YAAA,EAAe,EACvC;UAEFH,EADJ,CAAAC,cAAA,eAAmC,iBACM;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxDH,EAAA,CAAAC,cAAA,wBAEmF;UAA/ED,EAAA,CAAAiB,UAAA,6BAAAkN,sEAAAhN,MAAA;YAAA,OAAmBkM,GAAA,CAAA7B,YAAA,CAAArK,MAAA,CAAoB;UAAA,EAAC;UAGxDnB,EAH+F,CAAAG,YAAA,EAAe,EAChG,EACJ,EACH;UAEHH,EADJ,CAAAC,cAAA,eAA+B,sBAED;UADkCD,EAAA,CAAAiB,UAAA,uBAAAmN,8DAAA;YAAA,OAAaf,GAAA,CAAArB,eAAA,EAAiB;UAAA,EAAC;UAE3FhM,EAAA,CAAAG,YAAA,EAAa;UACbH,EAAA,CAAAC,cAAA,sBAC2D;UADkCD,EAAA,CAAAiB,UAAA,uBAAAoN,8DAAA;YAAA,OAAahB,GAAA,CAAA3B,WAAA,EAAa;UAAA,EAAC;UAKpI1L,EAHY,CAAAG,YAAA,EAAa,EACX,EACJ,EACE;;;UAxLAH,EAAA,CAAAY,SAAA,GAAwB;UAAxBZ,EAAA,CAAAK,UAAA,cAAAgN,GAAA,CAAArJ,UAAA,CAAwB;UAUxBhE,EAAA,CAAAY,SAAA,GAAe;UAAfZ,EAAA,CAAAK,UAAA,gBAAe;UAQwEL,EAAA,CAAAY,SAAA,GAAgB;UAAhBZ,EAAA,CAAAK,UAAA,iBAAgB;UAS7GL,EAAA,CAAAY,SAAA,GAAiB;UAKjBZ,EALA,CAAAK,UAAA,kBAAiB,oBACE,cAAAgN,GAAA,CAAApL,SAAA,CAII;UAOtBjC,EAAA,CAAAY,SAAA,EAA+C;UAA/CZ,EAAA,CAAAK,UAAA,UAAAgN,GAAA,CAAApL,SAAA,IAAAoL,GAAA,CAAA7K,cAAA,CAAAJ,MAAA,OAA+C;UAS3BpC,EAAA,CAAAY,SAAA,EAGtB;UAHsBZ,EAAA,CAAAK,UAAA,YAAAgN,GAAA,CAAApL,SAAA,IAAAoL,GAAA,CAAAlL,eAAA,CAAAC,MAAA,SAAAiL,GAAA,CAAAjJ,wBAAA,GAAAiJ,GAAA,CAAAlL,eAAA,CAGtB;UA0BenC,EAAA,CAAAY,SAAA,EAA+B;UAA/BZ,EAAA,CAAAK,UAAA,SAAAgN,GAAA,CAAA7K,cAAA,CAAAJ,MAAA,KAA+B;UAkBjDpC,EAAA,CAAAY,SAAA,EAA+B;UAA/BZ,EAAA,CAAAsO,qBAAA,mBAAAjB,GAAA,CAAAhK,QAAA,CAA+B;UAK/BrD,EATA,CAAAK,UAAA,SAAAgN,GAAA,CAAAnK,gBAAA,CAAyB,UAAAmK,GAAA,CAAAjK,UAAA,CACL,YAAAiK,GAAA,CAAAlK,YAAA,CACI,wBACD,mBAGL,qBACE,mCACc,sCACG;UAQrCnD,EAAA,CAAAY,SAAA,EAAwB;UAaxBZ,EAbA,CAAAK,UAAA,SAAAgN,GAAA,CAAA9J,eAAA,CAAwB,gDAIvB,wBACsB,mBAGL,oBACC,qBACC,gCACW,mCACG,sCACG;UAO5BvD,EAAA,CAAAY,SAAA,EAAuB;UACwBZ,EAD/C,CAAAK,UAAA,SAAAgN,GAAA,CAAA/I,cAAA,CAAuB,oBAAoB,yBAAyB,oBAAoB,4BAA4B,sBACtG,qBAAgC,uBAAuB;UAGlEtE,EAAA,CAAAY,SAAA,GAA0B;UAA1BZ,EAAA,CAAAK,UAAA,cAAAgN,GAAA,CAAA9I,YAAA,CAA0B;UAKevE,EAAA,CAAAY,SAAA,GAAiB;UAChDZ,EAD+B,CAAAK,UAAA,kBAAiB,oBAC7B;UASTL,EAAA,CAAAY,SAAA,GAAuC;UAEjCZ,EAFN,CAAAK,UAAA,wCAAuC,YAAAgN,GAAA,CAAA3I,UAAA,CAAuB,kBAAA2I,GAAA,CAAAvI,eAAA,CACvC,mBAAmB,gBACrC,sBAAsB;UAI3B9E,EAAA,CAAAY,SAAA,GAAiC;UAEgBZ,EAFjD,CAAAK,UAAA,kCAAiC,YAAAgN,GAAA,CAAA1I,aAAA,CAA0B,kBAAA0I,GAAA,CAAAtI,kBAAA,CACjC,cAAAsI,GAAA,CAAAnI,WAAA,CAA0B,gBACJ,sBAAsB;UAOtElF,EAAA,CAAAY,SAAA,GAAkC;UAG5CZ,EAHU,CAAAK,UAAA,mCAAkC,YAAAgN,GAAA,CAAAzI,cAAA,CAA2B,kBAAAyI,GAAA,CAAArI,mBAAA,CAClC,cAAAqI,GAAA,CAAAlI,cAAA,CAA6B,gBACP,sBACtC;UAIXnF,EAAA,CAAAY,SAAA,GAA+B;UAEgBZ,EAF/C,CAAAK,UAAA,gCAA+B,YAAAgN,GAAA,CAAAxI,WAAA,CAAwB,kBAAAwI,GAAA,CAAApI,gBAAA,CAC/B,cAAAoI,GAAA,CAAAjI,eAAA,CAA8B,gBACR,sBAAsB;UAKEpF,EAAA,CAAAY,SAAA,GAAgB;UAAhBZ,EAAA,CAAAK,UAAA,iBAAgB;UAGhGL,EAAA,CAAAY,SAAA,EAAiD;UACnCZ,EADd,CAAAK,UAAA,UAAAgN,GAAA,CAAA7I,iBAAA,uBAAiD,iBAA4E,cAAA6I,GAAA,CAAA9I,YAAA,CAAAoH,KAAA,CAC/E;;;qBDrKlE9L,YAAY,EAAA0O,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZ1O,mBAAmB,EACnBV,iBAAiB,EACjBL,mBAAmB,EACnBc,mBAAmB,EACnBX,aAAa,EACbC,cAAc,EACdP,mBAAmB,EAAA2N,EAAA,CAAAkC,aAAA,EAAAlC,EAAA,CAAAmC,eAAA,EAAAnC,EAAA,CAAAoC,oBAAA,EAAApC,EAAA,CAAAqC,iBAAA,EAAArC,EAAA,CAAAsC,kBAAA,EAAAtC,EAAA,CAAAuC,eAAA,EACnBzP,oBAAoB,EACpBP,eAAe,EACfE,eAAe,EACfC,iBAAiB;MAAA8P,MAAA;IAAA;;SAKRrM,mBAAmB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}