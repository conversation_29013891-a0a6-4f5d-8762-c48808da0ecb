{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { IconComponent } from '@ava/play-comp-library';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction MarketplaceAgentCardComponent_ava_icon_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ava-icon\", 14);\n  }\n  if (rf & 2) {\n    const filled_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"iconColor\", filled_r1 ? \"#fbbf24\" : \"#d1d5db\");\n  }\n}\nexport let MarketplaceAgentCardComponent = /*#__PURE__*/(() => {\n  class MarketplaceAgentCardComponent {\n    agent;\n    variant = 'default';\n    // Individual inputs for backward compatibility\n    title = '';\n    description = '';\n    rating = 0;\n    userCount = 0;\n    studioType = 'Experience Studio';\n    backgroundColor = '';\n    get displayAgent() {\n      // Use agent object if provided, otherwise use individual inputs\n      if (this.agent) {\n        return this.agent;\n      }\n      return {\n        title: this.title,\n        description: this.description,\n        rating: this.rating,\n        userCount: this.userCount,\n        studioType: this.studioType,\n        backgroundColor: this.backgroundColor\n      };\n    }\n    get studioConfig() {\n      const configs = {\n        'Experience Studio': {\n          color: '#ECF8F4',\n          // Cyan\n          backgroundColor: '#43BD90',\n          textColor: '#FFFFFF'\n        },\n        'Platform Studio': {\n          color: '#F2EBFD',\n          // Purple\n          backgroundColor: '#997BCF',\n          textColor: '#FFFFFF'\n        },\n        'Data Studio': {\n          color: '#FFF5E2',\n          // Yellow/Gold\n          backgroundColor: '#B4A02D',\n          textColor: '#FFFFFF'\n        },\n        'QE Studio': {\n          color: '#cceaf0ff',\n          // Cyan\n          backgroundColor: '#0891b2',\n          textColor: '#FFFFFF'\n        },\n        'Product Studio': {\n          color: '#FDE9EF',\n          // Rose/Pink\n          backgroundColor: '#F06896',\n          textColor: '#FFFFFF'\n        }\n      };\n      return configs[this.displayAgent.studioType] || configs['Experience Studio'];\n    }\n    get cardBackgroundColor() {\n      if (this.displayAgent.backgroundColor) {\n        return this.displayAgent.backgroundColor;\n      }\n      const backgroundColors = {\n        'Experience Studio': '#ECF8F4',\n        // Light pink\n        'Platform Studio': '#F2EBFD',\n        // Light purple\n        'Data Studio': '#FFF5E2',\n        // Light yellow\n        'QE Studio': '#cceaf0ff',\n        // Light blue\n        'Product Studio': '#FDE9EF' // Light pink\n      };\n      return backgroundColors[this.displayAgent.studioType] || '#ffffff';\n    }\n    onCardClick() {\n      if (this.displayAgent.onClick) {\n        this.displayAgent.onClick();\n      }\n    }\n    getRatingStars() {\n      // Show only one star as requested\n      return [true];\n    }\n    static ɵfac = function MarketplaceAgentCardComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MarketplaceAgentCardComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MarketplaceAgentCardComponent,\n      selectors: [[\"app-marketplace-agent-card\"]],\n      inputs: {\n        agent: \"agent\",\n        variant: \"variant\",\n        title: \"title\",\n        description: \"description\",\n        rating: \"rating\",\n        userCount: \"userCount\",\n        studioType: \"studioType\",\n        backgroundColor: \"backgroundColor\"\n      },\n      decls: 19,\n      vars: 17,\n      consts: [[1, \"marketplace-agent-card\", 3, \"click\", \"ngClass\"], [1, \"card-header\"], [1, \"card-title\"], [1, \"card-rating\"], [1, \"rating-stars\"], [\"iconName\", \"star\", \"iconSize\", \"14\", 3, \"iconColor\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-value\"], [1, \"card-description\"], [1, \"description-text\"], [1, \"card-footer\"], [1, \"user-count\"], [\"iconName\", \"user\", \"iconColor\", \"#6b7280\", \"iconSize\", \"16\"], [1, \"count-text\"], [1, \"studio-badge\"], [\"iconName\", \"star\", \"iconSize\", \"14\", 3, \"iconColor\"]],\n      template: function MarketplaceAgentCardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵlistener(\"click\", function MarketplaceAgentCardComponent_Template_div_click_0_listener() {\n            return ctx.onCardClick();\n          });\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"h3\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 3)(5, \"div\", 4);\n          i0.ɵɵtemplate(6, MarketplaceAgentCardComponent_ava_icon_6_Template, 1, 1, \"ava-icon\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"span\", 6);\n          i0.ɵɵtext(8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(9, \"div\", 7)(10, \"p\", 8);\n          i0.ɵɵtext(11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"div\", 9)(13, \"div\", 10);\n          i0.ɵɵelement(14, \"ava-icon\", 11);\n          i0.ɵɵelementStart(15, \"span\", 12);\n          i0.ɵɵtext(16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"div\", 13);\n          i0.ɵɵtext(18);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"background-color\", ctx.cardBackgroundColor);\n          i0.ɵɵclassProp(\"clickable\", ctx.displayAgent.onClick);\n          i0.ɵɵproperty(\"ngClass\", \"card-\" + ctx.variant);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.displayAgent.title);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.getRatingStars());\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.displayAgent.rating);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.displayAgent.description);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.displayAgent.userCount);\n          i0.ɵɵadvance();\n          i0.ɵɵstyleProp(\"background-color\", ctx.studioConfig.backgroundColor)(\"color\", ctx.studioConfig.textColor)(\"border-color\", ctx.studioConfig.color);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.displayAgent.studioType, \" \");\n        }\n      },\n      dependencies: [CommonModule, i1.NgClass, i1.NgForOf, IconComponent],\n      styles: [\".marketplace-agent-card[_ngcontent-%COMP%] {\\n  display: flex;\\n  width: 561px;\\n  padding: 24px;\\n  flex-direction: column;\\n  align-items: flex-start;\\n  border-radius: 16px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);\\n  border: 1px solid #e5e7eb;\\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\\n  cursor: default;\\n  height: 100%;\\n  min-height: 200px;\\n}\\n.marketplace-agent-card.clickable[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n.marketplace-agent-card.clickable[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);\\n}\\n.marketplace-agent-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);\\n}\\n\\n.card-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  margin-bottom: 16px;\\n  align-self: stretch;\\n}\\n.card-header[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\\n  flex: 1 0 0;\\n  color: var(--Global-colors-Neutral-n---700, #4C515B);\\n  font-family: Mulish;\\n  font-size: 20px;\\n  font-style: normal;\\n  font-weight: 700;\\n  line-height: 120%; \\n\\n  margin: 0;\\n  padding: 0;\\n}\\n.card-header[_ngcontent-%COMP%]   .card-rating[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  padding: 0 5px;\\n  flex-shrink: 0;\\n  background: white;\\n  margin-left: auto;\\n  border-radius: 5px;\\n}\\n.card-header[_ngcontent-%COMP%]   .card-rating[_ngcontent-%COMP%]   .rating-stars[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 2px;\\n}\\n.card-header[_ngcontent-%COMP%]   .card-rating[_ngcontent-%COMP%]   .rating-value[_ngcontent-%COMP%] {\\n  font-family: \\\"Mulish\\\", sans-serif;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #374151;\\n}\\n\\n.card-description[_ngcontent-%COMP%] {\\n  flex: 1;\\n  margin: 0;\\n  padding: 0;\\n}\\n.card-description[_ngcontent-%COMP%]   .description-text[_ngcontent-%COMP%] {\\n  color: var(--Global-colors-Neutral-n---700, #4C515B);\\n  font-family: Mulish;\\n  font-size: 16px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: 120%;\\n  margin: 0;\\n  display: -webkit-box;\\n  -webkit-line-clamp: 3;\\n  -webkit-box-orient: vertical;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  padding: 0;\\n}\\n\\n.card-footer[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  align-self: stretch;\\n  margin-top: auto;\\n}\\n.card-footer[_ngcontent-%COMP%]   .user-count[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n.card-footer[_ngcontent-%COMP%]   .user-count[_ngcontent-%COMP%]   .count-text[_ngcontent-%COMP%] {\\n  font-family: \\\"Mulish\\\", sans-serif;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #6b7280;\\n}\\n.card-footer[_ngcontent-%COMP%]   .studio-badge[_ngcontent-%COMP%] {\\n  font-family: \\\"Mulish\\\", sans-serif;\\n  font-size: 12px;\\n  font-weight: 600;\\n  padding: 6px 12px;\\n  border-radius: 12px;\\n  border: 1px solid;\\n  white-space: nowrap;\\n  text-transform: capitalize;\\n}\\n\\n@media (max-width: 768px) {\\n  .marketplace-agent-card[_ngcontent-%COMP%] {\\n    width: 400px;\\n    padding: 20px;\\n    min-height: 180px;\\n  }\\n  .marketplace-agent-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n    font-weight: 700;\\n    line-height: 120%;\\n  }\\n  .marketplace-agent-card[_ngcontent-%COMP%]   .card-description[_ngcontent-%COMP%]   .description-text[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n    font-weight: 400;\\n    line-height: 120%;\\n    -webkit-line-clamp: 2;\\n  }\\n  .marketplace-agent-card[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%]   .user-count[_ngcontent-%COMP%]   .count-text[_ngcontent-%COMP%] {\\n    font-size: 13px;\\n  }\\n  .marketplace-agent-card[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%]   .studio-badge[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n    padding: 4px 10px;\\n  }\\n  .card-featured[_ngcontent-%COMP%] {\\n    padding: 24px;\\n  }\\n  .card-featured[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n  .card-featured[_ngcontent-%COMP%]   .card-description[_ngcontent-%COMP%]   .description-text[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .marketplace-agent-card[_ngcontent-%COMP%] {\\n    width: 320px;\\n    padding: 16px;\\n    min-height: 160px;\\n  }\\n  .marketplace-agent-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%] {\\n    margin-bottom: 12px;\\n  }\\n  .marketplace-agent-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n    font-weight: 700;\\n    line-height: 120%;\\n  }\\n  .marketplace-agent-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-rating[_ngcontent-%COMP%]   .rating-value[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .marketplace-agent-card[_ngcontent-%COMP%]   .card-description[_ngcontent-%COMP%] {\\n    margin-bottom: 14px;\\n  }\\n  .marketplace-agent-card[_ngcontent-%COMP%]   .card-description[_ngcontent-%COMP%]   .description-text[_ngcontent-%COMP%] {\\n    font-size: 13px;\\n    font-weight: 400;\\n    line-height: 120%;\\n  }\\n  .marketplace-agent-card[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%]   .user-count[_ngcontent-%COMP%]   .count-text[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .marketplace-agent-card[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%]   .studio-badge[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n    padding: 3px 8px;\\n  }\\n}\\n@media (prefers-reduced-motion: reduce) {\\n  .marketplace-agent-card[_ngcontent-%COMP%] {\\n    transition: none;\\n  }\\n  .marketplace-agent-card.clickable[_ngcontent-%COMP%]:hover {\\n    transform: none;\\n  }\\n}\\n@media (prefers-contrast: high) {\\n  .marketplace-agent-card[_ngcontent-%COMP%] {\\n    border: 2px solid #000;\\n    box-shadow: none;\\n  }\\n  .marketplace-agent-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\\n    color: #000;\\n  }\\n  .marketplace-agent-card[_ngcontent-%COMP%]   .card-description[_ngcontent-%COMP%]   .description-text[_ngcontent-%COMP%] {\\n    color: #000;\\n  }\\n  .marketplace-agent-card[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%]   .user-count[_ngcontent-%COMP%]   .count-text[_ngcontent-%COMP%] {\\n    color: #000;\\n  }\\n  .marketplace-agent-card[_ngcontent-%COMP%]   .studio-badge[_ngcontent-%COMP%] {\\n    border: 2px solid !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return MarketplaceAgentCardComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "IconComponent", "i0", "ɵɵelement", "ɵɵproperty", "filled_r1", "MarketplaceAgentCardComponent", "agent", "variant", "title", "description", "rating", "userCount", "studioType", "backgroundColor", "displayAgent", "studioConfig", "configs", "color", "textColor", "cardBackgroundColor", "backgroundColors", "onCardClick", "onClick", "getRatingStars", "selectors", "inputs", "decls", "vars", "consts", "template", "MarketplaceAgentCardComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵlistener", "MarketplaceAgentCardComponent_Template_div_click_0_listener", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "MarketplaceAgentCardComponent_ava_icon_6_Template", "ɵɵstyleProp", "ɵɵclassProp", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵtextInterpolate1", "i1", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\shared\\components\\marketplace-agent-card\\marketplace-agent-card.component.ts", "C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\shared\\components\\marketplace-agent-card\\marketplace-agent-card.component.html"], "sourcesContent": ["import { Component, Input } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { IconComponent } from '@ava/play-comp-library';\r\n\r\nexport interface MarketplaceAgent {\r\n  id?: string | number;\r\n  title: string;\r\n  description: string;\r\n  rating: number;\r\n  userCount: number;\r\n  studioType: 'Experience Studio' | 'Platform Studio' | 'Data Studio' | 'QE Studio' | 'Product Studio';\r\n  backgroundColor?: string;\r\n  onClick?: () => void;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-marketplace-agent-card',\r\n  standalone: true,\r\n  imports: [CommonModule, IconComponent],\r\n  templateUrl: './marketplace-agent-card.component.html',\r\n  styleUrl: './marketplace-agent-card.component.scss',\r\n})\r\nexport class MarketplaceAgentCardComponent {\r\n  @Input() agent!: MarketplaceAgent;\r\n  @Input() variant: 'default' | 'compact' | 'featured' = 'default';\r\n\r\n  // Individual inputs for backward compatibility\r\n  @Input() title: string = '';\r\n  @Input() description: string = '';\r\n  @Input() rating: number = 0;\r\n  @Input() userCount: number = 0;\r\n  @Input() studioType: MarketplaceAgent['studioType'] = 'Experience Studio';\r\n  @Input() backgroundColor: string = '';\r\n\r\n  get displayAgent(): MarketplaceAgent {\r\n    // Use agent object if provided, otherwise use individual inputs\r\n    if (this.agent) {\r\n      return this.agent;\r\n    }\r\n    \r\n    return {\r\n      title: this.title,\r\n      description: this.description,\r\n      rating: this.rating,\r\n      userCount: this.userCount,\r\n      studioType: this.studioType,\r\n      backgroundColor: this.backgroundColor,\r\n    };\r\n  }\r\n\r\n  get studioConfig() {\r\n    const configs = {\r\n      'Experience Studio': {\r\n       color: '#ECF8F4', // Cyan\r\n        backgroundColor: '#43BD90',\r\n        textColor: '#FFFFFF'\r\n      },\r\n      'Platform Studio': {\r\n        color: '#F2EBFD', // Purple\r\n        backgroundColor: '#997BCF',\r\n        textColor: '#FFFFFF'\r\n      },\r\n      'Data Studio': {\r\n        color: '#FFF5E2', // Yellow/Gold\r\n        backgroundColor: '#B4A02D',\r\n        textColor: '#FFFFFF'\r\n      },\r\n      'QE Studio': {\r\n       color: '#cceaf0ff', // Cyan\r\n        backgroundColor: '#0891b2',\r\n        textColor: '#FFFFFF'\r\n      },\r\n      'Product Studio': {\r\n        color: '#FDE9EF', // Rose/Pink\r\n        backgroundColor: '#F06896',\r\n        textColor: '#FFFFFF'\r\n      }\r\n    };\r\n\r\n    return configs[this.displayAgent.studioType] || configs['Experience Studio'];\r\n  }\r\n\r\n  get cardBackgroundColor(): string {\r\n    if (this.displayAgent.backgroundColor) {\r\n      return this.displayAgent.backgroundColor;\r\n    }\r\n\r\n    const backgroundColors = {\r\n      'Experience Studio': '#ECF8F4', // Light pink\r\n      'Platform Studio': '#F2EBFD', // Light purple\r\n      'Data Studio': '#FFF5E2', // Light yellow\r\n      'QE Studio': '#cceaf0ff', // Light blue\r\n      'Product Studio': '#FDE9EF' // Light pink\r\n    };\r\n\r\n    return backgroundColors[this.displayAgent.studioType] || '#ffffff';\r\n  }\r\n  onCardClick(): void {\r\n    if (this.displayAgent.onClick) {\r\n      this.displayAgent.onClick();\r\n    }\r\n  }\r\n\r\n  getRatingStars(): boolean[] {\r\n    // Show only one star as requested\r\n    return [true];\r\n  }\r\n} ", "<div \r\n  class=\"marketplace-agent-card\" \r\n  [style.background-color]=\"cardBackgroundColor\"\r\n  [ngClass]=\"'card-' + variant\"\r\n  (click)=\"onCardClick()\"\r\n  [class.clickable]=\"displayAgent.onClick\"\r\n>\r\n  <!-- Card Header with Title and Rating -->\r\n  <div class=\"card-header\">\r\n    <h3 class=\"card-title\">{{ displayAgent.title }}</h3>\r\n    <div class=\"card-rating\">\r\n      <div class=\"rating-stars\">\r\n        <ava-icon\r\n          *ngFor=\"let filled of getRatingStars(); let i = index\"\r\n          iconName=\"star\"\r\n          [iconColor]=\"filled ? '#fbbf24' : '#d1d5db'\"\r\n          iconSize=\"14\"\r\n        ></ava-icon>\r\n      </div>\r\n      <span class=\"rating-value\">{{ displayAgent.rating }}</span>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Card Description -->\r\n  <div class=\"card-description\">\r\n    <p class=\"description-text\">{{ displayAgent.description }}</p>\r\n  </div>\r\n\r\n  <!-- Card Footer with User Count and Studio Badge -->\r\n  <div class=\"card-footer\">\r\n    <div class=\"user-count\">\r\n      <ava-icon \r\n        iconName=\"user\" \r\n        iconColor=\"#6b7280\" \r\n        iconSize=\"16\"\r\n      ></ava-icon>\r\n      <span class=\"count-text\">{{ displayAgent.userCount }}</span>\r\n    </div>\r\n    \r\n    <div class=\"studio-badge\" \r\n         [style.background-color]=\"studioConfig.backgroundColor\"\r\n         [style.color]=\"studioConfig.textColor\"\r\n         [style.border-color]=\"studioConfig.color\">\r\n      {{ displayAgent.studioType }}\r\n    </div>\r\n  </div>\r\n</div> "], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;;;;;ICU9CC,EAAA,CAAAC,SAAA,mBAKY;;;;IAFVD,EAAA,CAAAE,UAAA,cAAAC,SAAA,yBAA4C;;;ADOtD,WAAaC,6BAA6B;EAApC,MAAOA,6BAA6B;IAC/BC,KAAK;IACLC,OAAO,GAAuC,SAAS;IAEhE;IACSC,KAAK,GAAW,EAAE;IAClBC,WAAW,GAAW,EAAE;IACxBC,MAAM,GAAW,CAAC;IAClBC,SAAS,GAAW,CAAC;IACrBC,UAAU,GAAmC,mBAAmB;IAChEC,eAAe,GAAW,EAAE;IAErC,IAAIC,YAAYA,CAAA;MACd;MACA,IAAI,IAAI,CAACR,KAAK,EAAE;QACd,OAAO,IAAI,CAACA,KAAK;MACnB;MAEA,OAAO;QACLE,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBC,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7BC,MAAM,EAAE,IAAI,CAACA,MAAM;QACnBC,SAAS,EAAE,IAAI,CAACA,SAAS;QACzBC,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3BC,eAAe,EAAE,IAAI,CAACA;OACvB;IACH;IAEA,IAAIE,YAAYA,CAAA;MACd,MAAMC,OAAO,GAAG;QACd,mBAAmB,EAAE;UACpBC,KAAK,EAAE,SAAS;UAAE;UACjBJ,eAAe,EAAE,SAAS;UAC1BK,SAAS,EAAE;SACZ;QACD,iBAAiB,EAAE;UACjBD,KAAK,EAAE,SAAS;UAAE;UAClBJ,eAAe,EAAE,SAAS;UAC1BK,SAAS,EAAE;SACZ;QACD,aAAa,EAAE;UACbD,KAAK,EAAE,SAAS;UAAE;UAClBJ,eAAe,EAAE,SAAS;UAC1BK,SAAS,EAAE;SACZ;QACD,WAAW,EAAE;UACZD,KAAK,EAAE,WAAW;UAAE;UACnBJ,eAAe,EAAE,SAAS;UAC1BK,SAAS,EAAE;SACZ;QACD,gBAAgB,EAAE;UAChBD,KAAK,EAAE,SAAS;UAAE;UAClBJ,eAAe,EAAE,SAAS;UAC1BK,SAAS,EAAE;;OAEd;MAED,OAAOF,OAAO,CAAC,IAAI,CAACF,YAAY,CAACF,UAAU,CAAC,IAAII,OAAO,CAAC,mBAAmB,CAAC;IAC9E;IAEA,IAAIG,mBAAmBA,CAAA;MACrB,IAAI,IAAI,CAACL,YAAY,CAACD,eAAe,EAAE;QACrC,OAAO,IAAI,CAACC,YAAY,CAACD,eAAe;MAC1C;MAEA,MAAMO,gBAAgB,GAAG;QACvB,mBAAmB,EAAE,SAAS;QAAE;QAChC,iBAAiB,EAAE,SAAS;QAAE;QAC9B,aAAa,EAAE,SAAS;QAAE;QAC1B,WAAW,EAAE,WAAW;QAAE;QAC1B,gBAAgB,EAAE,SAAS,CAAC;OAC7B;MAED,OAAOA,gBAAgB,CAAC,IAAI,CAACN,YAAY,CAACF,UAAU,CAAC,IAAI,SAAS;IACpE;IACAS,WAAWA,CAAA;MACT,IAAI,IAAI,CAACP,YAAY,CAACQ,OAAO,EAAE;QAC7B,IAAI,CAACR,YAAY,CAACQ,OAAO,EAAE;MAC7B;IACF;IAEAC,cAAcA,CAAA;MACZ;MACA,OAAO,CAAC,IAAI,CAAC;IACf;;uCApFWlB,6BAA6B;IAAA;;YAA7BA,6BAA6B;MAAAmB,SAAA;MAAAC,MAAA;QAAAnB,KAAA;QAAAC,OAAA;QAAAC,KAAA;QAAAC,WAAA;QAAAC,MAAA;QAAAC,SAAA;QAAAC,UAAA;QAAAC,eAAA;MAAA;MAAAa,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCtB1C9B,EAAA,CAAAgC,cAAA,aAMC;UAFChC,EAAA,CAAAiC,UAAA,mBAAAC,4DAAA;YAAA,OAASH,GAAA,CAAAX,WAAA,EAAa;UAAA,EAAC;UAKrBpB,EADF,CAAAgC,cAAA,aAAyB,YACA;UAAAhC,EAAA,CAAAmC,MAAA,GAAwB;UAAAnC,EAAA,CAAAoC,YAAA,EAAK;UAElDpC,EADF,CAAAgC,cAAA,aAAyB,aACG;UACxBhC,EAAA,CAAAqC,UAAA,IAAAC,iDAAA,sBAKC;UACHtC,EAAA,CAAAoC,YAAA,EAAM;UACNpC,EAAA,CAAAgC,cAAA,cAA2B;UAAAhC,EAAA,CAAAmC,MAAA,GAAyB;UAExDnC,EAFwD,CAAAoC,YAAA,EAAO,EACvD,EACF;UAIJpC,EADF,CAAAgC,cAAA,aAA8B,YACA;UAAAhC,EAAA,CAAAmC,MAAA,IAA8B;UAC5DnC,EAD4D,CAAAoC,YAAA,EAAI,EAC1D;UAIJpC,EADF,CAAAgC,cAAA,cAAyB,eACC;UACtBhC,EAAA,CAAAC,SAAA,oBAIY;UACZD,EAAA,CAAAgC,cAAA,gBAAyB;UAAAhC,EAAA,CAAAmC,MAAA,IAA4B;UACvDnC,EADuD,CAAAoC,YAAA,EAAO,EACxD;UAENpC,EAAA,CAAAgC,cAAA,eAG+C;UAC7ChC,EAAA,CAAAmC,MAAA,IACF;UAEJnC,EAFI,CAAAoC,YAAA,EAAM,EACF,EACF;;;UA5CJpC,EAAA,CAAAuC,WAAA,qBAAAR,GAAA,CAAAb,mBAAA,CAA8C;UAG9ClB,EAAA,CAAAwC,WAAA,cAAAT,GAAA,CAAAlB,YAAA,CAAAQ,OAAA,CAAwC;UAFxCrB,EAAA,CAAAE,UAAA,sBAAA6B,GAAA,CAAAzB,OAAA,CAA6B;UAMJN,EAAA,CAAAyC,SAAA,GAAwB;UAAxBzC,EAAA,CAAA0C,iBAAA,CAAAX,GAAA,CAAAlB,YAAA,CAAAN,KAAA,CAAwB;UAItBP,EAAA,CAAAyC,SAAA,GAAqB;UAArBzC,EAAA,CAAAE,UAAA,YAAA6B,GAAA,CAAAT,cAAA,GAAqB;UAMjBtB,EAAA,CAAAyC,SAAA,GAAyB;UAAzBzC,EAAA,CAAA0C,iBAAA,CAAAX,GAAA,CAAAlB,YAAA,CAAAJ,MAAA,CAAyB;UAM1BT,EAAA,CAAAyC,SAAA,GAA8B;UAA9BzC,EAAA,CAAA0C,iBAAA,CAAAX,GAAA,CAAAlB,YAAA,CAAAL,WAAA,CAA8B;UAW/BR,EAAA,CAAAyC,SAAA,GAA4B;UAA5BzC,EAAA,CAAA0C,iBAAA,CAAAX,GAAA,CAAAlB,YAAA,CAAAH,SAAA,CAA4B;UAIlDV,EAAA,CAAAyC,SAAA,EAAuD;UAEvDzC,EAFA,CAAAuC,WAAA,qBAAAR,GAAA,CAAAjB,YAAA,CAAAF,eAAA,CAAuD,UAAAmB,GAAA,CAAAjB,YAAA,CAAAG,SAAA,CACjB,iBAAAc,GAAA,CAAAjB,YAAA,CAAAE,KAAA,CACG;UAC5ChB,EAAA,CAAAyC,SAAA,EACF;UADEzC,EAAA,CAAA2C,kBAAA,MAAAZ,GAAA,CAAAlB,YAAA,CAAAF,UAAA,MACF;;;qBD1BQb,YAAY,EAAA8C,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAE/C,aAAa;MAAAgD,MAAA;IAAA;;SAI1B3C,6BAA6B;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}