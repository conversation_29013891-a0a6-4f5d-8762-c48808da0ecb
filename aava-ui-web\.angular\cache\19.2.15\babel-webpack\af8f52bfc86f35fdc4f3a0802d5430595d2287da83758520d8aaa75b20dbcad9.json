{"ast": null, "code": "import { rgb } from \"d3-color\";\nvar c = rgb(),\n  pi_1_3 = Math.PI / 3,\n  pi_2_3 = Math.PI * 2 / 3;\nexport default function (t) {\n  var x;\n  t = (0.5 - t) * Math.PI;\n  c.r = 255 * (x = Math.sin(t)) * x;\n  c.g = 255 * (x = Math.sin(t + pi_1_3)) * x;\n  c.b = 255 * (x = Math.sin(t + pi_2_3)) * x;\n  return c + \"\";\n}", "map": {"version": 3, "names": ["rgb", "c", "pi_1_3", "Math", "PI", "pi_2_3", "t", "x", "r", "sin", "g", "b"], "sources": ["C:/console/aava-ui-web/node_modules/d3-scale-chromatic/src/sequential-multi/sinebow.js"], "sourcesContent": ["import {rgb} from \"d3-color\";\n\nvar c = rgb(),\n    pi_1_3 = Math.PI / 3,\n    pi_2_3 = Math.PI * 2 / 3;\n\nexport default function(t) {\n  var x;\n  t = (0.5 - t) * Math.PI;\n  c.r = 255 * (x = Math.sin(t)) * x;\n  c.g = 255 * (x = Math.sin(t + pi_1_3)) * x;\n  c.b = 255 * (x = Math.sin(t + pi_2_3)) * x;\n  return c + \"\";\n}\n"], "mappings": "AAAA,SAAQA,GAAG,QAAO,UAAU;AAE5B,IAAIC,CAAC,GAAGD,GAAG,CAAC,CAAC;EACTE,MAAM,GAAGC,IAAI,CAACC,EAAE,GAAG,CAAC;EACpBC,MAAM,GAAGF,IAAI,CAACC,EAAE,GAAG,CAAC,GAAG,CAAC;AAE5B,eAAe,UAASE,CAAC,EAAE;EACzB,IAAIC,CAAC;EACLD,CAAC,GAAG,CAAC,GAAG,GAAGA,CAAC,IAAIH,IAAI,CAACC,EAAE;EACvBH,CAAC,CAACO,CAAC,GAAG,GAAG,IAAID,CAAC,GAAGJ,IAAI,CAACM,GAAG,CAACH,CAAC,CAAC,CAAC,GAAGC,CAAC;EACjCN,CAAC,CAACS,CAAC,GAAG,GAAG,IAAIH,CAAC,GAAGJ,IAAI,CAACM,GAAG,CAACH,CAAC,GAAGJ,MAAM,CAAC,CAAC,GAAGK,CAAC;EAC1CN,CAAC,CAACU,CAAC,GAAG,GAAG,IAAIJ,CAAC,GAAGJ,IAAI,CAACM,GAAG,CAACH,CAAC,GAAGD,MAAM,CAAC,CAAC,GAAGE,CAAC;EAC1C,OAAON,CAAC,GAAG,EAAE;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}