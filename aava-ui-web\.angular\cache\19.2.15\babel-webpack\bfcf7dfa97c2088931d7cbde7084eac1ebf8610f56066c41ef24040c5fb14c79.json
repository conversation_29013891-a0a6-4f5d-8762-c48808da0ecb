{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { takeUntil, of, map, catchError, Subject, debounceTime, distinctUntilChanged, startWith } from 'rxjs';\nimport { PageFooterComponent } from '../../../shared/components/page-footer/page-footer.component';\nimport { formatToDisplayDate } from '../../../shared/utils/date-utils';\nimport promptsLabels from './constants/prompts.json';\nimport { PROMPTS_BASE_ACTIONS } from './prompts-actions';\nimport { AvaTextboxComponent, TextCardComponent, DropdownComponent, IconComponent, PopupComponent } from '@ava/play-comp-library';\nimport { LucideAngularModule } from 'lucide-angular';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { ConsoleCardComponent } from '../../../shared/components/console-card/console-card.component';\nimport { TimeAgoPipe } from '../../../shared/pipes/time-ago.pipe';\nlet PromptsComponent = class PromptsComponent {\n  paginationService;\n  promptsService;\n  router;\n  route;\n  fb;\n  // Popup state for success messages\n  showSuccessPopup = false;\n  popupMessage = '';\n  popupTitle = '';\n  iconName = 'info';\n  submissionSuccess = false;\n  // popup for delete confirmation\n  showDeletePopup = false;\n  promptToDelete = null;\n  simpleOptions = [{\n    name: 'Option 1',\n    value: '1'\n  }, {\n    name: 'Option 2',\n    value: '2'\n  }, {\n    name: 'Option 3',\n    value: '3'\n  }, {\n    name: 'Option 4',\n    value: '4'\n  }, {\n    name: 'Option 5',\n    value: '5'\n  }];\n  defaultActions = [{\n    id: 'delete',\n    icon: 'trash',\n    label: 'Delete item',\n    tooltip: 'Delete'\n  }, {\n    id: 'edit',\n    icon: 'edit',\n    label: 'Edit item',\n    tooltip: 'Edit'\n  }, {\n    id: 'view',\n    icon: 'eye',\n    label: 'View Prompts',\n    tooltip: 'View',\n    isPrimary: true\n  }];\n  promptLabels = promptsLabels.labels;\n  allPrompts = [];\n  filteredPrompts = [];\n  displayedPrompts = [];\n  searchForm;\n  isLoading = false;\n  currentPage = 1;\n  itemsPerPage = 11;\n  totalPages = 1;\n  destroy$ = new Subject();\n  selectedData = null;\n  cardSkeletonPlaceholders = Array(11);\n  constructor(paginationService, promptsService, router, route, fb) {\n    this.paginationService = paginationService;\n    this.promptsService = promptsService;\n    this.router = router;\n    this.route = route;\n    this.fb = fb;\n    this.searchForm = this.fb.group({\n      search: ['']\n    });\n  }\n  ngOnInit() {\n    this.isLoading = true;\n    this.initSearchListener();\n    this.promptsService.fetchAllPrompts().pipe(takeUntil(this.destroy$), map(this.transformResponseToCardData.bind(this)), catchError(error => {\n      console.error('Error fetching prompts:', error);\n      this.isLoading = false;\n      return of({\n        prompts: []\n      });\n    })).subscribe({\n      next: ({\n        prompts\n      }) => {\n        this.allPrompts = prompts;\n        this.filteredPrompts = [...prompts];\n        this.updateDisplayedPrompts();\n        this.setInitialPageFromQueryParam();\n      },\n      error: err => console.error('Subscription error:', err.message),\n      complete: () => {\n        this.isLoading = false;\n      }\n    });\n  }\n  onSelectionChange(data) {\n    this.selectedData = data;\n  }\n  onCreatePrompt() {\n    this.router.navigate(['/libraries/prompts/create']);\n  }\n  getHeaderIcons(prompt) {\n    return [{\n      iconName: 'NotebookText',\n      title: prompt.toolType || 'Prompt'\n    }, {\n      iconName: 'users',\n      title: `${prompt.userCount || 30}`\n    }];\n  }\n  getFooterIcons(prompt) {\n    return [{\n      iconName: 'user',\n      title: prompt.owner || 'AAVA'\n    }, {\n      iconName: 'calendar-days',\n      title: prompt.createdDate\n    }];\n  }\n  onPageChange(page) {\n    this.currentPage = page;\n    this.updateDisplayedPrompts();\n    this.router.navigate([], {\n      relativeTo: this.route,\n      queryParams: {\n        page: this.currentPage\n      },\n      queryParamsHandling: 'merge'\n    });\n  }\n  onActionClick(event, promptId) {\n    switch (event.actionId) {\n      case 'delete':\n        this.deletePrompt(promptId);\n        break;\n      case 'edit':\n        this.executePrompt(promptId);\n        break;\n      case 'view':\n        this.viewPrompt(promptId);\n        break;\n      default:\n        break;\n    }\n  }\n  viewPrompt(promptId) {\n    this.router.navigate(['/libraries/prompts/edit', promptId], {\n      queryParams: {\n        view: 'true',\n        returnPage: this.currentPage\n      }\n    });\n  }\n  transformResponseToCardData(response) {\n    const prompts = 'prompts' in response ? response.prompts : response;\n    return {\n      prompts: prompts.map(this.formatPromptCard.bind(this))\n    };\n  }\n  formatPromptCard(item) {\n    const {\n      name,\n      updatedAt,\n      categoryName,\n      domainName,\n      tags = [],\n      ...rest\n    } = item;\n    const customTags = this.getCustomTags(categoryName, domainName);\n    const allTags = [...tags, ...customTags];\n    const tagSummary = this.getTagSummary(allTags);\n    const createdDate = formatToDisplayDate(updatedAt);\n    return {\n      title: name,\n      createdDate,\n      tags: allTags,\n      tagSummary,\n      actions: PROMPTS_BASE_ACTIONS,\n      ...rest\n    };\n  }\n  // Step 1: User clicks trash icon → open delete confirmation\n  deletePrompt(promptId) {\n    const prompt = this.allPrompts.find(p => p.id === promptId);\n    if (!prompt) return;\n    this.promptToDelete = prompt;\n    this.showDeletePopup = true;\n  }\n  // Step 2: User confirms delete in popup\n  onConfirmDelete() {\n    if (!this.promptToDelete?.id) return;\n    const promptId = this.promptToDelete.id;\n    this.promptsService.deletePrompt(promptId).subscribe({\n      next: res => {\n        if (res && res.success !== false) {\n          // Update local prompt lists\n          this.allPrompts = this.allPrompts.filter(p => p.id !== promptId);\n          this.filteredPrompts = this.filteredPrompts.filter(p => p.id !== promptId);\n          this.updateDisplayedPrompts();\n          // Show success popup\n          this.iconName = 'check-circle';\n          this.popupTitle = 'Success';\n          this.popupMessage = 'Prompt deleted successfully.';\n          this.submissionSuccess = true;\n          this.showSuccessPopup = true;\n        } else {\n          this.iconName = 'alert-circle';\n          this.popupTitle = 'Error';\n          this.popupMessage = 'Failed to delete prompt.';\n          this.submissionSuccess = false;\n          this.showSuccessPopup = true;\n        }\n        this.closeDeletePopup();\n      },\n      error: err => {\n        console.error('Failed to delete prompt:', err);\n        this.iconName = 'alert-circle';\n        this.popupTitle = 'Error';\n        this.popupMessage = 'An unexpected error occurred.';\n        this.submissionSuccess = false;\n        this.showSuccessPopup = true;\n        this.closeDeletePopup();\n      }\n    });\n  }\n  // Step 3: User cancels or closes delete popup\n  closeDeletePopup() {\n    this.showDeletePopup = false;\n    this.promptToDelete = null;\n  }\n  // Success popup confirm handler\n  onSuccessConfirm() {\n    this.closeSuccessPopup();\n  }\n  // Close success popup manually or when user clicks close icon\n  closeSuccessPopup() {\n    this.showSuccessPopup = false;\n    this.popupTitle = '';\n    this.popupMessage = '';\n    this.iconName = 'info';\n  }\n  executePrompt(promptId) {\n    this.router.navigate(['/libraries/prompts/edit', promptId], {\n      queryParams: {\n        execute: 'true',\n        returnPage: this.currentPage\n      }\n    });\n  }\n  copyPrompt(promptId) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!promptId) return;\n      const prompt = _this.allPrompts.find(p => p.id === promptId);\n      if (prompt) {\n        try {\n          yield navigator.clipboard.writeText(JSON.stringify(prompt, null, 2));\n        } catch (err) {\n          // Optionally handle error silently\n        }\n      }\n    })();\n  }\n  getCustomTags(categoryName, domainName) {\n    const tags = [];\n    if (categoryName) tags.push({\n      label: categoryName,\n      type: 'primary'\n    });\n    if (domainName) tags.push({\n      label: domainName,\n      type: 'secondary'\n    });\n    return tags;\n  }\n  getTagSummary(tags) {\n    return tags.map(tag => tag.label).join(', ');\n  }\n  setInitialPageFromQueryParam() {\n    const pageParam = this.route.snapshot.queryParamMap.get('page');\n    if (pageParam) {\n      const page = parseInt(pageParam, 10);\n      if (!isNaN(page)) this.currentPage = page;\n    }\n  }\n  initSearchListener() {\n    this.searchForm.get('search').valueChanges.pipe(startWith(''), debounceTime(300), distinctUntilChanged(), map(value => value?.toLowerCase() ?? ''), takeUntil(this.destroy$)).subscribe(searchText => {\n      this.filterPrompts(searchText);\n    });\n  }\n  updateDisplayedPrompts() {\n    this.itemsPerPage = this.currentPage === 1 ? 12 : 11;\n    const {\n      displayedItems,\n      totalPages\n    } = this.paginationService.getPaginatedItems(this.filteredPrompts, this.currentPage, this.itemsPerPage);\n    this.displayedPrompts = displayedItems;\n    this.totalPages = totalPages;\n  }\n  filterPrompts(searchText) {\n    this.filteredPrompts = this.allPrompts.filter(prompt => {\n      const titleMatch = prompt.title?.toLowerCase().includes(searchText);\n      const descriptionMatch = prompt.description?.toLowerCase().includes(searchText);\n      const tagMatch = prompt.tags?.some(tag => tag.label?.toLowerCase().includes(searchText));\n      return titleMatch || descriptionMatch || tagMatch;\n    });\n    this.currentPage = 1;\n    this.updateDisplayedPrompts();\n  }\n};\nPromptsComponent = __decorate([Component({\n  selector: 'app-prompts',\n  standalone: true,\n  imports: [CommonModule, PageFooterComponent, TextCardComponent, AvaTextboxComponent, DropdownComponent, LucideAngularModule, IconComponent, PopupComponent, ReactiveFormsModule, ConsoleCardComponent, TimeAgoPipe],\n  templateUrl: './prompts.component.html',\n  styleUrl: './prompts.component.scss'\n})], PromptsComponent);\nexport { PromptsComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "takeUntil", "of", "map", "catchError", "Subject", "debounceTime", "distinctUntilChanged", "startWith", "PageFooterComponent", "formatToDisplayDate", "prompts<PERSON><PERSON><PERSON>", "PROMPTS_BASE_ACTIONS", "AvaTextboxComponent", "TextCardComponent", "DropdownComponent", "IconComponent", "PopupComponent", "LucideAngularModule", "ReactiveFormsModule", "ConsoleCardComponent", "TimeAgoPipe", "PromptsComponent", "paginationService", "promptsService", "router", "route", "fb", "showSuccessPopup", "popupMessage", "popupTitle", "iconName", "submissionSuccess", "showDeletePopup", "promptToDelete", "simpleOptions", "name", "value", "defaultActions", "id", "icon", "label", "tooltip", "isPrimary", "prompt<PERSON><PERSON><PERSON>", "labels", "allPrompts", "filteredPrompts", "displayedPrompts", "searchForm", "isLoading", "currentPage", "itemsPerPage", "totalPages", "destroy$", "selectedData", "cardSkeletonPlaceholders", "Array", "constructor", "group", "search", "ngOnInit", "initSearchListener", "fetchAllPrompts", "pipe", "transformResponseToCardData", "bind", "error", "console", "prompts", "subscribe", "next", "updateDisplayedPrompts", "setInitialPageFromQueryParam", "err", "message", "complete", "onSelectionChange", "data", "onCreatePrompt", "navigate", "getHeaderIcons", "prompt", "title", "toolType", "userCount", "getFooterIcons", "owner", "createdDate", "onPageChange", "page", "relativeTo", "queryParams", "queryParamsHandling", "onActionClick", "event", "promptId", "actionId", "deletePrompt", "executePrompt", "viewPrompt", "view", "returnPage", "response", "formatPromptCard", "item", "updatedAt", "categoryName", "domainName", "tags", "rest", "customTags", "getCustomTags", "allTags", "tagSummary", "getTagSummary", "actions", "find", "p", "onConfirmDelete", "res", "success", "filter", "closeDeletePopup", "onSuccessConfirm", "closeSuccessPopup", "execute", "copyPrompt", "_this", "_asyncToGenerator", "navigator", "clipboard", "writeText", "JSON", "stringify", "push", "type", "tag", "join", "pageParam", "snapshot", "queryParamMap", "get", "parseInt", "isNaN", "valueChanges", "toLowerCase", "searchText", "filterPrompts", "displayedItems", "getPaginatedItems", "titleMatch", "includes", "descriptionMatch", "description", "tagMatch", "some", "__decorate", "selector", "standalone", "imports", "templateUrl", "styleUrl"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\libraries\\prompts\\prompts.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Router, ActivatedRoute } from '@angular/router';\r\nimport {\r\n  takeUntil,\r\n  of,\r\n  map,\r\n  catchError,\r\n  Subject,\r\n  debounceTime,\r\n  distinctUntilChanged,\r\n  startWith,\r\n} from 'rxjs';\r\nimport { CardData, CardTag } from '../../../shared/models/card.model';\r\nimport { PageFooterComponent } from '../../../shared/components/page-footer/page-footer.component';\r\nimport { PaginationService } from '../../../shared/services/pagination.service';\r\nimport { PromptsService } from '../../../shared/services/prompts.service';\r\nimport { formatToDisplayDate } from '../../../shared/utils/date-utils';\r\nimport promptsLabels from './constants/prompts.json';\r\nimport { PROMPTS_BASE_ACTIONS } from './prompts-actions';\r\nimport {\r\n  AvaTextboxComponent,\r\n  TextCardComponent,\r\n  DropdownComponent,\r\n  DropdownOption,\r\n  IconComponent,\r\n  PopupComponent,\r\n} from '@ava/play-comp-library';\r\nimport { LucideAngularModule } from 'lucide-angular';\r\nimport { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';\r\nimport {\r\n  ConsoleCardAction,\r\n  ConsoleCardComponent,\r\n} from '../../../shared/components/console-card/console-card.component';\r\nimport { TimeAgoPipe } from '../../../shared/pipes/time-ago.pipe';\r\n\r\n@Component({\r\n  selector: 'app-prompts',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    PageFooterComponent,\r\n    TextCardComponent,\r\n    AvaTextboxComponent,\r\n    DropdownComponent,\r\n    LucideAngularModule,\r\n    IconComponent,\r\n    PopupComponent,\r\n    ReactiveFormsModule,\r\n    ConsoleCardComponent,\r\n    TimeAgoPipe,\r\n  ],\r\n  templateUrl: './prompts.component.html',\r\n  styleUrl: './prompts.component.scss',\r\n})\r\nexport class PromptsComponent implements OnInit {\r\n  // Popup state for success messages\r\n  showSuccessPopup = false;\r\n  popupMessage = '';\r\n  popupTitle = '';\r\n  iconName = 'info';\r\n  submissionSuccess = false;\r\n  // popup for delete confirmation\r\n  showDeletePopup: boolean = false;\r\n  promptToDelete: CardData | null = null;\r\n\r\n  simpleOptions: DropdownOption[] = [\r\n    { name: 'Option 1', value: '1' },\r\n    { name: 'Option 2', value: '2' },\r\n    { name: 'Option 3', value: '3' },\r\n    { name: 'Option 4', value: '4' },\r\n    { name: 'Option 5', value: '5' },\r\n  ];\r\n\r\n  defaultActions: ConsoleCardAction[] = [\r\n    {\r\n      id: 'delete',\r\n      icon: 'trash',\r\n      label: 'Delete item',\r\n      tooltip: 'Delete',\r\n    },\r\n    {\r\n      id: 'edit',\r\n      icon: 'edit',\r\n      label: 'Edit item',\r\n      tooltip: 'Edit',\r\n    },\r\n\r\n    {\r\n      id: 'view',\r\n      icon: 'eye',\r\n      label: 'View Prompts',\r\n      tooltip: 'View',\r\n      isPrimary: true,\r\n    },\r\n  ];\r\n  public promptLabels = promptsLabels.labels;\r\n  allPrompts: CardData[] = [];\r\n  filteredPrompts: CardData[] = [];\r\n  displayedPrompts: CardData[] = [];\r\n  searchForm!: FormGroup;\r\n  isLoading = false;\r\n  currentPage: number = 1;\r\n  itemsPerPage: number = 11;\r\n  totalPages: number = 1;\r\n  protected destroy$ = new Subject<void>();\r\n  selectedData: any = null;\r\n  cardSkeletonPlaceholders = Array(11);\r\n\r\n  constructor(\r\n    private paginationService: PaginationService,\r\n    private promptsService: PromptsService,\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private fb: FormBuilder,\r\n  ) {\r\n    this.searchForm = this.fb.group({\r\n      search: [''],\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.isLoading = true;\r\n    this.initSearchListener();\r\n    this.promptsService\r\n      .fetchAllPrompts()\r\n      .pipe(\r\n        takeUntil(this.destroy$),\r\n        map(this.transformResponseToCardData.bind(this)),\r\n        catchError((error) => {\r\n          console.error('Error fetching prompts:', error);\r\n          this.isLoading = false;\r\n          return of({ prompts: [] });\r\n        }),\r\n      )\r\n      .subscribe({\r\n        next: ({ prompts }) => {\r\n          this.allPrompts = prompts;\r\n          this.filteredPrompts = [...prompts];\r\n          this.updateDisplayedPrompts();\r\n          this.setInitialPageFromQueryParam();\r\n        },\r\n        error: (err) => console.error('Subscription error:', err.message),\r\n        complete: () => {\r\n          this.isLoading = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  onSelectionChange(data: any) {\r\n    this.selectedData = data;\r\n  }\r\n\r\n  onCreatePrompt(): void {\r\n    this.router.navigate(['/libraries/prompts/create']);\r\n  }\r\n\r\n  getHeaderIcons(prompt: any): { iconName: string; title: string }[] {\r\n    return [\r\n      { iconName: 'NotebookText', title: prompt.toolType || 'Prompt' },\r\n      { iconName: 'users', title: `${prompt.userCount || 30}` },\r\n    ];\r\n  }\r\n\r\n  getFooterIcons(prompt: any): { iconName: string; title: string }[] {\r\n    return [\r\n      { iconName: 'user', title: prompt.owner || 'AAVA' },\r\n      { iconName: 'calendar-days', title: prompt.createdDate },\r\n    ];\r\n  }\r\n\r\n  onPageChange(page: number): void {\r\n    this.currentPage = page;\r\n    this.updateDisplayedPrompts();\r\n    this.router.navigate([], {\r\n      relativeTo: this.route,\r\n      queryParams: { page: this.currentPage },\r\n      queryParamsHandling: 'merge',\r\n    });\r\n  }\r\n\r\n  onActionClick(\r\n    event: { actionId: string; action: ConsoleCardAction },\r\n    promptId: string,\r\n  ): void {\r\n    switch (event.actionId) {\r\n      case 'delete':\r\n        this.deletePrompt(promptId);\r\n        break;\r\n      case 'edit':\r\n        this.executePrompt(promptId);\r\n        break;\r\n      case 'view':\r\n        this.viewPrompt(promptId);\r\n        break;\r\n      default:\r\n        break;\r\n    }\r\n  }\r\n\r\n  private viewPrompt(promptId: string): void {\r\n    this.router.navigate(['/libraries/prompts/edit', promptId], {\r\n      queryParams: { view: 'true', returnPage: this.currentPage },\r\n    });\r\n  }\r\n\r\n  private transformResponseToCardData(\r\n    response: CardData[] | { prompts: CardData[] },\r\n  ): { prompts: (CardData & { tagSummary: string })[] } {\r\n    const prompts = 'prompts' in response ? response.prompts : response;\r\n    return {\r\n      prompts: prompts.map(this.formatPromptCard.bind(this)),\r\n    };\r\n  }\r\n\r\n  private formatPromptCard(item: any): CardData & { tagSummary: string } {\r\n    const {\r\n      name,\r\n      updatedAt,\r\n      categoryName,\r\n      domainName,\r\n      tags = [],\r\n      ...rest\r\n    } = item;\r\n    const customTags = this.getCustomTags(categoryName, domainName);\r\n    const allTags = [...tags, ...customTags];\r\n    const tagSummary = this.getTagSummary(allTags);\r\n    const createdDate = formatToDisplayDate(updatedAt);\r\n    return {\r\n      title: name,\r\n      createdDate,\r\n      tags: allTags,\r\n      tagSummary,\r\n      actions: PROMPTS_BASE_ACTIONS,\r\n      ...rest,\r\n    };\r\n  }\r\n\r\n  // Step 1: User clicks trash icon → open delete confirmation\r\n  private deletePrompt(promptId: string): void {\r\n    const prompt = this.allPrompts.find((p) => p.id === promptId);\r\n    if (!prompt) return;\r\n\r\n    this.promptToDelete = prompt;\r\n    this.showDeletePopup = true;\r\n  }\r\n\r\n  // Step 2: User confirms delete in popup\r\n  onConfirmDelete(): void {\r\n    if (!this.promptToDelete?.id) return;\r\n\r\n    const promptId = this.promptToDelete.id;\r\n\r\n    this.promptsService.deletePrompt(promptId).subscribe({\r\n      next: (res) => {\r\n        if (res && res.success !== false) {\r\n          // Update local prompt lists\r\n          this.allPrompts = this.allPrompts.filter((p) => p.id !== promptId);\r\n          this.filteredPrompts = this.filteredPrompts.filter(\r\n            (p) => p.id !== promptId,\r\n          );\r\n          this.updateDisplayedPrompts();\r\n\r\n          // Show success popup\r\n          this.iconName = 'check-circle';\r\n          this.popupTitle = 'Success';\r\n          this.popupMessage = 'Prompt deleted successfully.';\r\n          this.submissionSuccess = true;\r\n          this.showSuccessPopup = true;\r\n        } else {\r\n          this.iconName = 'alert-circle';\r\n          this.popupTitle = 'Error';\r\n          this.popupMessage = 'Failed to delete prompt.';\r\n          this.submissionSuccess = false;\r\n          this.showSuccessPopup = true;\r\n        }\r\n\r\n        this.closeDeletePopup();\r\n      },\r\n      error: (err) => {\r\n        console.error('Failed to delete prompt:', err);\r\n        this.iconName = 'alert-circle';\r\n        this.popupTitle = 'Error';\r\n        this.popupMessage = 'An unexpected error occurred.';\r\n        this.submissionSuccess = false;\r\n        this.showSuccessPopup = true;\r\n        this.closeDeletePopup();\r\n      },\r\n    });\r\n  }\r\n\r\n  // Step 3: User cancels or closes delete popup\r\n  closeDeletePopup(): void {\r\n    this.showDeletePopup = false;\r\n    this.promptToDelete = null;\r\n  }\r\n\r\n  // Success popup confirm handler\r\n  onSuccessConfirm(): void {\r\n    this.closeSuccessPopup();\r\n  }\r\n\r\n  // Close success popup manually or when user clicks close icon\r\n  closeSuccessPopup(): void {\r\n    this.showSuccessPopup = false;\r\n    this.popupTitle = '';\r\n    this.popupMessage = '';\r\n    this.iconName = 'info';\r\n  }\r\n\r\n  private executePrompt(promptId: string): void {\r\n    this.router.navigate(['/libraries/prompts/edit', promptId], {\r\n      queryParams: { execute: 'true', returnPage: this.currentPage },\r\n    });\r\n  }\r\n\r\n  private async copyPrompt(promptId: string): Promise<void> {\r\n    if (!promptId) return;\r\n    const prompt = this.allPrompts.find((p) => p.id === promptId);\r\n    if (prompt) {\r\n      try {\r\n        await navigator.clipboard.writeText(JSON.stringify(prompt, null, 2));\r\n      } catch (err) {\r\n        // Optionally handle error silently\r\n      }\r\n    }\r\n  }\r\n\r\n  private getCustomTags(categoryName?: string, domainName?: string): CardTag[] {\r\n    const tags: CardTag[] = [];\r\n    if (categoryName) tags.push({ label: categoryName, type: 'primary' });\r\n    if (domainName) tags.push({ label: domainName, type: 'secondary' });\r\n    return tags;\r\n  }\r\n\r\n  private getTagSummary(tags: CardTag[]): string {\r\n    return tags.map((tag) => tag.label).join(', ');\r\n  }\r\n\r\n  private setInitialPageFromQueryParam(): void {\r\n    const pageParam = this.route.snapshot.queryParamMap.get('page');\r\n    if (pageParam) {\r\n      const page = parseInt(pageParam, 10);\r\n      if (!isNaN(page)) this.currentPage = page;\r\n    }\r\n  }\r\n\r\n  private initSearchListener(): void {\r\n    this.searchForm\r\n      .get('search')!\r\n      .valueChanges.pipe(\r\n        startWith(''),\r\n        debounceTime(300),\r\n        distinctUntilChanged(),\r\n        map((value) => value?.toLowerCase() ?? ''),\r\n        takeUntil(this.destroy$),\r\n      )\r\n      .subscribe((searchText) => {\r\n        this.filterPrompts(searchText);\r\n      });\r\n  }\r\n\r\n  private updateDisplayedPrompts(): void {\r\n    this.itemsPerPage = this.currentPage === 1 ? 12 : 11;\r\n    const { displayedItems, totalPages } =\r\n      this.paginationService.getPaginatedItems(\r\n        this.filteredPrompts,\r\n        this.currentPage,\r\n        this.itemsPerPage,\r\n      );\r\n    this.displayedPrompts = displayedItems;\r\n    this.totalPages = totalPages;\r\n  }\r\n\r\n  private filterPrompts(searchText: string): void {\r\n    this.filteredPrompts = this.allPrompts.filter((prompt) => {\r\n      const titleMatch = prompt.title?.toLowerCase().includes(searchText);\r\n      const descriptionMatch = prompt.description\r\n        ?.toLowerCase()\r\n        .includes(searchText);\r\n      const tagMatch = prompt.tags?.some((tag) =>\r\n        tag.label?.toLowerCase().includes(searchText),\r\n      );\r\n      return titleMatch || descriptionMatch || tagMatch;\r\n    });\r\n    this.currentPage = 1;\r\n    this.updateDisplayedPrompts();\r\n  }\r\n}\r\n"], "mappings": ";;AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SACEC,SAAS,EACTC,EAAE,EACFC,GAAG,EACHC,UAAU,EACVC,OAAO,EACPC,YAAY,EACZC,oBAAoB,EACpBC,SAAS,QACJ,MAAM;AAEb,SAASC,mBAAmB,QAAQ,8DAA8D;AAGlG,SAASC,mBAAmB,QAAQ,kCAAkC;AACtE,OAAOC,aAAa,MAAM,0BAA0B;AACpD,SAASC,oBAAoB,QAAQ,mBAAmB;AACxD,SACEC,mBAAmB,EACnBC,iBAAiB,EACjBC,iBAAiB,EAEjBC,aAAa,EACbC,cAAc,QACT,wBAAwB;AAC/B,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAAiCC,mBAAmB,QAAQ,gBAAgB;AAC5E,SAEEC,oBAAoB,QACf,gEAAgE;AACvE,SAASC,WAAW,QAAQ,qCAAqC;AAqB1D,IAAMC,gBAAgB,GAAtB,MAAMA,gBAAgB;EAuDjBC,iBAAA;EACAC,cAAA;EACAC,MAAA;EACAC,KAAA;EACAC,EAAA;EA1DV;EACAC,gBAAgB,GAAG,KAAK;EACxBC,YAAY,GAAG,EAAE;EACjBC,UAAU,GAAG,EAAE;EACfC,QAAQ,GAAG,MAAM;EACjBC,iBAAiB,GAAG,KAAK;EACzB;EACAC,eAAe,GAAY,KAAK;EAChCC,cAAc,GAAoB,IAAI;EAEtCC,aAAa,GAAqB,CAChC;IAAEC,IAAI,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAG,CAAE,EAChC;IAAED,IAAI,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAG,CAAE,EAChC;IAAED,IAAI,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAG,CAAE,EAChC;IAAED,IAAI,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAG,CAAE,EAChC;IAAED,IAAI,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAG,CAAE,CACjC;EAEDC,cAAc,GAAwB,CACpC;IACEC,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,OAAO;IACbC,KAAK,EAAE,aAAa;IACpBC,OAAO,EAAE;GACV,EACD;IACEH,EAAE,EAAE,MAAM;IACVC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,WAAW;IAClBC,OAAO,EAAE;GACV,EAED;IACEH,EAAE,EAAE,MAAM;IACVC,IAAI,EAAE,KAAK;IACXC,KAAK,EAAE,cAAc;IACrBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE;GACZ,CACF;EACMC,YAAY,GAAGjC,aAAa,CAACkC,MAAM;EAC1CC,UAAU,GAAe,EAAE;EAC3BC,eAAe,GAAe,EAAE;EAChCC,gBAAgB,GAAe,EAAE;EACjCC,UAAU;EACVC,SAAS,GAAG,KAAK;EACjBC,WAAW,GAAW,CAAC;EACvBC,YAAY,GAAW,EAAE;EACzBC,UAAU,GAAW,CAAC;EACZC,QAAQ,GAAG,IAAIjD,OAAO,EAAQ;EACxCkD,YAAY,GAAQ,IAAI;EACxBC,wBAAwB,GAAGC,KAAK,CAAC,EAAE,CAAC;EAEpCC,YACUnC,iBAAoC,EACpCC,cAA8B,EAC9BC,MAAc,EACdC,KAAqB,EACrBC,EAAe;IAJf,KAAAJ,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,EAAE,GAAFA,EAAE;IAEV,IAAI,CAACsB,UAAU,GAAG,IAAI,CAACtB,EAAE,CAACgC,KAAK,CAAC;MAC9BC,MAAM,EAAE,CAAC,EAAE;KACZ,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACX,SAAS,GAAG,IAAI;IACrB,IAAI,CAACY,kBAAkB,EAAE;IACzB,IAAI,CAACtC,cAAc,CAChBuC,eAAe,EAAE,CACjBC,IAAI,CACH/D,SAAS,CAAC,IAAI,CAACqD,QAAQ,CAAC,EACxBnD,GAAG,CAAC,IAAI,CAAC8D,2BAA2B,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,EAChD9D,UAAU,CAAE+D,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,IAAI,CAACjB,SAAS,GAAG,KAAK;MACtB,OAAOhD,EAAE,CAAC;QAAEmE,OAAO,EAAE;MAAE,CAAE,CAAC;IAC5B,CAAC,CAAC,CACH,CACAC,SAAS,CAAC;MACTC,IAAI,EAAEA,CAAC;QAAEF;MAAO,CAAE,KAAI;QACpB,IAAI,CAACvB,UAAU,GAAGuB,OAAO;QACzB,IAAI,CAACtB,eAAe,GAAG,CAAC,GAAGsB,OAAO,CAAC;QACnC,IAAI,CAACG,sBAAsB,EAAE;QAC7B,IAAI,CAACC,4BAA4B,EAAE;MACrC,CAAC;MACDN,KAAK,EAAGO,GAAG,IAAKN,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEO,GAAG,CAACC,OAAO,CAAC;MACjEC,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAAC1B,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACN;EAEA2B,iBAAiBA,CAACC,IAAS;IACzB,IAAI,CAACvB,YAAY,GAAGuB,IAAI;EAC1B;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAACtD,MAAM,CAACuD,QAAQ,CAAC,CAAC,2BAA2B,CAAC,CAAC;EACrD;EAEAC,cAAcA,CAACC,MAAW;IACxB,OAAO,CACL;MAAEnD,QAAQ,EAAE,cAAc;MAAEoD,KAAK,EAAED,MAAM,CAACE,QAAQ,IAAI;IAAQ,CAAE,EAChE;MAAErD,QAAQ,EAAE,OAAO;MAAEoD,KAAK,EAAE,GAAGD,MAAM,CAACG,SAAS,IAAI,EAAE;IAAE,CAAE,CAC1D;EACH;EAEAC,cAAcA,CAACJ,MAAW;IACxB,OAAO,CACL;MAAEnD,QAAQ,EAAE,MAAM;MAAEoD,KAAK,EAAED,MAAM,CAACK,KAAK,IAAI;IAAM,CAAE,EACnD;MAAExD,QAAQ,EAAE,eAAe;MAAEoD,KAAK,EAAED,MAAM,CAACM;IAAW,CAAE,CACzD;EACH;EAEAC,YAAYA,CAACC,IAAY;IACvB,IAAI,CAACvC,WAAW,GAAGuC,IAAI;IACvB,IAAI,CAAClB,sBAAsB,EAAE;IAC7B,IAAI,CAAC/C,MAAM,CAACuD,QAAQ,CAAC,EAAE,EAAE;MACvBW,UAAU,EAAE,IAAI,CAACjE,KAAK;MACtBkE,WAAW,EAAE;QAAEF,IAAI,EAAE,IAAI,CAACvC;MAAW,CAAE;MACvC0C,mBAAmB,EAAE;KACtB,CAAC;EACJ;EAEAC,aAAaA,CACXC,KAAsD,EACtDC,QAAgB;IAEhB,QAAQD,KAAK,CAACE,QAAQ;MACpB,KAAK,QAAQ;QACX,IAAI,CAACC,YAAY,CAACF,QAAQ,CAAC;QAC3B;MACF,KAAK,MAAM;QACT,IAAI,CAACG,aAAa,CAACH,QAAQ,CAAC;QAC5B;MACF,KAAK,MAAM;QACT,IAAI,CAACI,UAAU,CAACJ,QAAQ,CAAC;QACzB;MACF;QACE;IACJ;EACF;EAEQI,UAAUA,CAACJ,QAAgB;IACjC,IAAI,CAACvE,MAAM,CAACuD,QAAQ,CAAC,CAAC,yBAAyB,EAAEgB,QAAQ,CAAC,EAAE;MAC1DJ,WAAW,EAAE;QAAES,IAAI,EAAE,MAAM;QAAEC,UAAU,EAAE,IAAI,CAACnD;MAAW;KAC1D,CAAC;EACJ;EAEQc,2BAA2BA,CACjCsC,QAA8C;IAE9C,MAAMlC,OAAO,GAAG,SAAS,IAAIkC,QAAQ,GAAGA,QAAQ,CAAClC,OAAO,GAAGkC,QAAQ;IACnE,OAAO;MACLlC,OAAO,EAAEA,OAAO,CAAClE,GAAG,CAAC,IAAI,CAACqG,gBAAgB,CAACtC,IAAI,CAAC,IAAI,CAAC;KACtD;EACH;EAEQsC,gBAAgBA,CAACC,IAAS;IAChC,MAAM;MACJrE,IAAI;MACJsE,SAAS;MACTC,YAAY;MACZC,UAAU;MACVC,IAAI,GAAG,EAAE;MACT,GAAGC;IAAI,CACR,GAAGL,IAAI;IACR,MAAMM,UAAU,GAAG,IAAI,CAACC,aAAa,CAACL,YAAY,EAAEC,UAAU,CAAC;IAC/D,MAAMK,OAAO,GAAG,CAAC,GAAGJ,IAAI,EAAE,GAAGE,UAAU,CAAC;IACxC,MAAMG,UAAU,GAAG,IAAI,CAACC,aAAa,CAACF,OAAO,CAAC;IAC9C,MAAMzB,WAAW,GAAG9E,mBAAmB,CAACgG,SAAS,CAAC;IAClD,OAAO;MACLvB,KAAK,EAAE/C,IAAI;MACXoD,WAAW;MACXqB,IAAI,EAAEI,OAAO;MACbC,UAAU;MACVE,OAAO,EAAExG,oBAAoB;MAC7B,GAAGkG;KACJ;EACH;EAEA;EACQZ,YAAYA,CAACF,QAAgB;IACnC,MAAMd,MAAM,GAAG,IAAI,CAACpC,UAAU,CAACuE,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAAC/E,EAAE,KAAKyD,QAAQ,CAAC;IAC7D,IAAI,CAACd,MAAM,EAAE;IAEb,IAAI,CAAChD,cAAc,GAAGgD,MAAM;IAC5B,IAAI,CAACjD,eAAe,GAAG,IAAI;EAC7B;EAEA;EACAsF,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAACrF,cAAc,EAAEK,EAAE,EAAE;IAE9B,MAAMyD,QAAQ,GAAG,IAAI,CAAC9D,cAAc,CAACK,EAAE;IAEvC,IAAI,CAACf,cAAc,CAAC0E,YAAY,CAACF,QAAQ,CAAC,CAAC1B,SAAS,CAAC;MACnDC,IAAI,EAAGiD,GAAG,IAAI;QACZ,IAAIA,GAAG,IAAIA,GAAG,CAACC,OAAO,KAAK,KAAK,EAAE;UAChC;UACA,IAAI,CAAC3E,UAAU,GAAG,IAAI,CAACA,UAAU,CAAC4E,MAAM,CAAEJ,CAAC,IAAKA,CAAC,CAAC/E,EAAE,KAAKyD,QAAQ,CAAC;UAClE,IAAI,CAACjD,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC2E,MAAM,CAC/CJ,CAAC,IAAKA,CAAC,CAAC/E,EAAE,KAAKyD,QAAQ,CACzB;UACD,IAAI,CAACxB,sBAAsB,EAAE;UAE7B;UACA,IAAI,CAACzC,QAAQ,GAAG,cAAc;UAC9B,IAAI,CAACD,UAAU,GAAG,SAAS;UAC3B,IAAI,CAACD,YAAY,GAAG,8BAA8B;UAClD,IAAI,CAACG,iBAAiB,GAAG,IAAI;UAC7B,IAAI,CAACJ,gBAAgB,GAAG,IAAI;QAC9B,CAAC,MAAM;UACL,IAAI,CAACG,QAAQ,GAAG,cAAc;UAC9B,IAAI,CAACD,UAAU,GAAG,OAAO;UACzB,IAAI,CAACD,YAAY,GAAG,0BAA0B;UAC9C,IAAI,CAACG,iBAAiB,GAAG,KAAK;UAC9B,IAAI,CAACJ,gBAAgB,GAAG,IAAI;QAC9B;QAEA,IAAI,CAAC+F,gBAAgB,EAAE;MACzB,CAAC;MACDxD,KAAK,EAAGO,GAAG,IAAI;QACbN,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEO,GAAG,CAAC;QAC9C,IAAI,CAAC3C,QAAQ,GAAG,cAAc;QAC9B,IAAI,CAACD,UAAU,GAAG,OAAO;QACzB,IAAI,CAACD,YAAY,GAAG,+BAA+B;QACnD,IAAI,CAACG,iBAAiB,GAAG,KAAK;QAC9B,IAAI,CAACJ,gBAAgB,GAAG,IAAI;QAC5B,IAAI,CAAC+F,gBAAgB,EAAE;MACzB;KACD,CAAC;EACJ;EAEA;EACAA,gBAAgBA,CAAA;IACd,IAAI,CAAC1F,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,cAAc,GAAG,IAAI;EAC5B;EAEA;EACA0F,gBAAgBA,CAAA;IACd,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEA;EACAA,iBAAiBA,CAAA;IACf,IAAI,CAACjG,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACE,UAAU,GAAG,EAAE;IACpB,IAAI,CAACD,YAAY,GAAG,EAAE;IACtB,IAAI,CAACE,QAAQ,GAAG,MAAM;EACxB;EAEQoE,aAAaA,CAACH,QAAgB;IACpC,IAAI,CAACvE,MAAM,CAACuD,QAAQ,CAAC,CAAC,yBAAyB,EAAEgB,QAAQ,CAAC,EAAE;MAC1DJ,WAAW,EAAE;QAAEkC,OAAO,EAAE,MAAM;QAAExB,UAAU,EAAE,IAAI,CAACnD;MAAW;KAC7D,CAAC;EACJ;EAEc4E,UAAUA,CAAC/B,QAAgB;IAAA,IAAAgC,KAAA;IAAA,OAAAC,iBAAA;MACvC,IAAI,CAACjC,QAAQ,EAAE;MACf,MAAMd,MAAM,GAAG8C,KAAI,CAAClF,UAAU,CAACuE,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAAC/E,EAAE,KAAKyD,QAAQ,CAAC;MAC7D,IAAId,MAAM,EAAE;QACV,IAAI;UACF,MAAMgD,SAAS,CAACC,SAAS,CAACC,SAAS,CAACC,IAAI,CAACC,SAAS,CAACpD,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QACtE,CAAC,CAAC,OAAOR,GAAG,EAAE;UACZ;QAAA;MAEJ;IAAC;EACH;EAEQsC,aAAaA,CAACL,YAAqB,EAAEC,UAAmB;IAC9D,MAAMC,IAAI,GAAc,EAAE;IAC1B,IAAIF,YAAY,EAAEE,IAAI,CAAC0B,IAAI,CAAC;MAAE9F,KAAK,EAAEkE,YAAY;MAAE6B,IAAI,EAAE;IAAS,CAAE,CAAC;IACrE,IAAI5B,UAAU,EAAEC,IAAI,CAAC0B,IAAI,CAAC;MAAE9F,KAAK,EAAEmE,UAAU;MAAE4B,IAAI,EAAE;IAAW,CAAE,CAAC;IACnE,OAAO3B,IAAI;EACb;EAEQM,aAAaA,CAACN,IAAe;IACnC,OAAOA,IAAI,CAAC1G,GAAG,CAAEsI,GAAG,IAAKA,GAAG,CAAChG,KAAK,CAAC,CAACiG,IAAI,CAAC,IAAI,CAAC;EAChD;EAEQjE,4BAA4BA,CAAA;IAClC,MAAMkE,SAAS,GAAG,IAAI,CAACjH,KAAK,CAACkH,QAAQ,CAACC,aAAa,CAACC,GAAG,CAAC,MAAM,CAAC;IAC/D,IAAIH,SAAS,EAAE;MACb,MAAMjD,IAAI,GAAGqD,QAAQ,CAACJ,SAAS,EAAE,EAAE,CAAC;MACpC,IAAI,CAACK,KAAK,CAACtD,IAAI,CAAC,EAAE,IAAI,CAACvC,WAAW,GAAGuC,IAAI;IAC3C;EACF;EAEQ5B,kBAAkBA,CAAA;IACxB,IAAI,CAACb,UAAU,CACZ6F,GAAG,CAAC,QAAQ,CAAE,CACdG,YAAY,CAACjF,IAAI,CAChBxD,SAAS,CAAC,EAAE,CAAC,EACbF,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBJ,GAAG,CAAEkC,KAAK,IAAKA,KAAK,EAAE6G,WAAW,EAAE,IAAI,EAAE,CAAC,EAC1CjJ,SAAS,CAAC,IAAI,CAACqD,QAAQ,CAAC,CACzB,CACAgB,SAAS,CAAE6E,UAAU,IAAI;MACxB,IAAI,CAACC,aAAa,CAACD,UAAU,CAAC;IAChC,CAAC,CAAC;EACN;EAEQ3E,sBAAsBA,CAAA;IAC5B,IAAI,CAACpB,YAAY,GAAG,IAAI,CAACD,WAAW,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE;IACpD,MAAM;MAAEkG,cAAc;MAAEhG;IAAU,CAAE,GAClC,IAAI,CAAC9B,iBAAiB,CAAC+H,iBAAiB,CACtC,IAAI,CAACvG,eAAe,EACpB,IAAI,CAACI,WAAW,EAChB,IAAI,CAACC,YAAY,CAClB;IACH,IAAI,CAACJ,gBAAgB,GAAGqG,cAAc;IACtC,IAAI,CAAChG,UAAU,GAAGA,UAAU;EAC9B;EAEQ+F,aAAaA,CAACD,UAAkB;IACtC,IAAI,CAACpG,eAAe,GAAG,IAAI,CAACD,UAAU,CAAC4E,MAAM,CAAExC,MAAM,IAAI;MACvD,MAAMqE,UAAU,GAAGrE,MAAM,CAACC,KAAK,EAAE+D,WAAW,EAAE,CAACM,QAAQ,CAACL,UAAU,CAAC;MACnE,MAAMM,gBAAgB,GAAGvE,MAAM,CAACwE,WAAW,EACvCR,WAAW,EAAE,CACdM,QAAQ,CAACL,UAAU,CAAC;MACvB,MAAMQ,QAAQ,GAAGzE,MAAM,CAAC2B,IAAI,EAAE+C,IAAI,CAAEnB,GAAG,IACrCA,GAAG,CAAChG,KAAK,EAAEyG,WAAW,EAAE,CAACM,QAAQ,CAACL,UAAU,CAAC,CAC9C;MACD,OAAOI,UAAU,IAAIE,gBAAgB,IAAIE,QAAQ;IACnD,CAAC,CAAC;IACF,IAAI,CAACxG,WAAW,GAAG,CAAC;IACpB,IAAI,CAACqB,sBAAsB,EAAE;EAC/B;CACD;AA7UYlD,gBAAgB,GAAAuI,UAAA,EAnB5B9J,SAAS,CAAC;EACT+J,QAAQ,EAAE,aAAa;EACvBC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPhK,YAAY,EACZS,mBAAmB,EACnBK,iBAAiB,EACjBD,mBAAmB,EACnBE,iBAAiB,EACjBG,mBAAmB,EACnBF,aAAa,EACbC,cAAc,EACdE,mBAAmB,EACnBC,oBAAoB,EACpBC,WAAW,CACZ;EACD4I,WAAW,EAAE,0BAA0B;EACvCC,QAAQ,EAAE;CACX,CAAC,C,EACW5I,gBAAgB,CA6U5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}