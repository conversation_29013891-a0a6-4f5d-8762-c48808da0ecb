{"ast": null, "code": "import { normalizeInterval } from \"./_lib/normalizeInterval.js\";\nimport { addQuarters } from \"./addQuarters.js\";\nimport { constructFrom } from \"./constructFrom.js\";\nimport { startOfQuarter } from \"./startOfQuarter.js\";\n\n/**\n * The {@link eachQuarterOfInterval} function options.\n */\n\n/**\n * The {@link eachQuarterOfInterval} function result type. It resolves the proper data type.\n * It uses the first argument date object type, starting from the date argument,\n * then the start interval date, and finally the end interval date. If\n * a context function is passed, it uses the context function return type.\n */\n\n/**\n * @name eachQuarterOfInterval\n * @category Interval Helpers\n * @summary Return the array of quarters within the specified time interval.\n *\n * @description\n * Return the array of quarters within the specified time interval.\n *\n * @typeParam IntervalType - Interval type.\n * @typeParam Options - Options type.\n *\n * @param interval - The interval\n * @param options - An object with options\n *\n * @returns The array with starts of quarters from the quarter of the interval start to the quarter of the interval end\n *\n * @example\n * // Each quarter within interval 6 February 2014 - 10 August 2014:\n * const result = eachQuarterOfInterval({\n *   start: new Date(2014, 1, 6),\n *   end: new Date(2014, 7, 10),\n * })\n * //=> [\n * //   Wed Jan 01 2014 00:00:00,\n * //   Tue Apr 01 2014 00:00:00,\n * //   Tue Jul 01 2014 00:00:00,\n * // ]\n */\nexport function eachQuarterOfInterval(interval, options) {\n  const {\n    start,\n    end\n  } = normalizeInterval(options?.in, interval);\n  let reversed = +start > +end;\n  const endTime = reversed ? +startOfQuarter(start) : +startOfQuarter(end);\n  let date = reversed ? startOfQuarter(end) : startOfQuarter(start);\n  let step = options?.step ?? 1;\n  if (!step) return [];\n  if (step < 0) {\n    step = -step;\n    reversed = !reversed;\n  }\n  const dates = [];\n  while (+date <= endTime) {\n    dates.push(constructFrom(start, date));\n    date = addQuarters(date, step);\n  }\n  return reversed ? dates.reverse() : dates;\n}\n\n// Fallback for modularized imports:\nexport default eachQuarterOfInterval;", "map": {"version": 3, "names": ["normalizeInterval", "addQuarters", "constructFrom", "startOfQuarter", "eachQuarterOfInterval", "interval", "options", "start", "end", "in", "reversed", "endTime", "date", "step", "dates", "push", "reverse"], "sources": ["C:/console/aava-ui-web/node_modules/date-fns/eachQuarterOfInterval.js"], "sourcesContent": ["import { normalizeInterval } from \"./_lib/normalizeInterval.js\";\nimport { addQuarters } from \"./addQuarters.js\";\nimport { constructFrom } from \"./constructFrom.js\";\nimport { startOfQuarter } from \"./startOfQuarter.js\";\n\n/**\n * The {@link eachQuarterOfInterval} function options.\n */\n\n/**\n * The {@link eachQuarterOfInterval} function result type. It resolves the proper data type.\n * It uses the first argument date object type, starting from the date argument,\n * then the start interval date, and finally the end interval date. If\n * a context function is passed, it uses the context function return type.\n */\n\n/**\n * @name eachQuarterOfInterval\n * @category Interval Helpers\n * @summary Return the array of quarters within the specified time interval.\n *\n * @description\n * Return the array of quarters within the specified time interval.\n *\n * @typeParam IntervalType - Interval type.\n * @typeParam Options - Options type.\n *\n * @param interval - The interval\n * @param options - An object with options\n *\n * @returns The array with starts of quarters from the quarter of the interval start to the quarter of the interval end\n *\n * @example\n * // Each quarter within interval 6 February 2014 - 10 August 2014:\n * const result = eachQuarterOfInterval({\n *   start: new Date(2014, 1, 6),\n *   end: new Date(2014, 7, 10),\n * })\n * //=> [\n * //   Wed Jan 01 2014 00:00:00,\n * //   Tue Apr 01 2014 00:00:00,\n * //   Tue Jul 01 2014 00:00:00,\n * // ]\n */\nexport function eachQuarterOfInterval(interval, options) {\n  const { start, end } = normalizeInterval(options?.in, interval);\n\n  let reversed = +start > +end;\n  const endTime = reversed ? +startOfQuarter(start) : +startOfQuarter(end);\n  let date = reversed ? startOfQuarter(end) : startOfQuarter(start);\n\n  let step = options?.step ?? 1;\n  if (!step) return [];\n  if (step < 0) {\n    step = -step;\n    reversed = !reversed;\n  }\n\n  const dates = [];\n\n  while (+date <= endTime) {\n    dates.push(constructFrom(start, date));\n    date = addQuarters(date, step);\n  }\n\n  return reversed ? dates.reverse() : dates;\n}\n\n// Fallback for modularized imports:\nexport default eachQuarterOfInterval;\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,cAAc,QAAQ,qBAAqB;;AAEpD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,qBAAqBA,CAACC,QAAQ,EAAEC,OAAO,EAAE;EACvD,MAAM;IAAEC,KAAK;IAAEC;EAAI,CAAC,GAAGR,iBAAiB,CAACM,OAAO,EAAEG,EAAE,EAAEJ,QAAQ,CAAC;EAE/D,IAAIK,QAAQ,GAAG,CAACH,KAAK,GAAG,CAACC,GAAG;EAC5B,MAAMG,OAAO,GAAGD,QAAQ,GAAG,CAACP,cAAc,CAACI,KAAK,CAAC,GAAG,CAACJ,cAAc,CAACK,GAAG,CAAC;EACxE,IAAII,IAAI,GAAGF,QAAQ,GAAGP,cAAc,CAACK,GAAG,CAAC,GAAGL,cAAc,CAACI,KAAK,CAAC;EAEjE,IAAIM,IAAI,GAAGP,OAAO,EAAEO,IAAI,IAAI,CAAC;EAC7B,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;EACpB,IAAIA,IAAI,GAAG,CAAC,EAAE;IACZA,IAAI,GAAG,CAACA,IAAI;IACZH,QAAQ,GAAG,CAACA,QAAQ;EACtB;EAEA,MAAMI,KAAK,GAAG,EAAE;EAEhB,OAAO,CAACF,IAAI,IAAID,OAAO,EAAE;IACvBG,KAAK,CAACC,IAAI,CAACb,aAAa,CAACK,KAAK,EAAEK,IAAI,CAAC,CAAC;IACtCA,IAAI,GAAGX,WAAW,CAACW,IAAI,EAAEC,IAAI,CAAC;EAChC;EAEA,OAAOH,QAAQ,GAAGI,KAAK,CAACE,OAAO,CAAC,CAAC,GAAGF,KAAK;AAC3C;;AAEA;AACA,eAAeV,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}