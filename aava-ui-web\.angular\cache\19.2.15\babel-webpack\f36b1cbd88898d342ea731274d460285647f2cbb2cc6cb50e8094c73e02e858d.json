{"ast": null, "code": "function responseText(response) {\n  if (!response.ok) throw new Error(response.status + \" \" + response.statusText);\n  return response.text();\n}\nexport default function (input, init) {\n  return fetch(input, init).then(responseText);\n}", "map": {"version": 3, "names": ["responseText", "response", "ok", "Error", "status", "statusText", "text", "input", "init", "fetch", "then"], "sources": ["C:/console/aava-ui-web/node_modules/d3-fetch/src/text.js"], "sourcesContent": ["function responseText(response) {\n  if (!response.ok) throw new Error(response.status + \" \" + response.statusText);\n  return response.text();\n}\n\nexport default function(input, init) {\n  return fetch(input, init).then(responseText);\n}\n"], "mappings": "AAAA,SAASA,YAAYA,CAACC,QAAQ,EAAE;EAC9B,IAAI,CAACA,QAAQ,CAACC,EAAE,EAAE,MAAM,IAAIC,KAAK,CAACF,QAAQ,CAACG,MAAM,GAAG,GAAG,GAAGH,QAAQ,CAACI,UAAU,CAAC;EAC9E,OAAOJ,QAAQ,CAACK,IAAI,CAAC,CAAC;AACxB;AAEA,eAAe,UAASC,KAAK,EAAEC,IAAI,EAAE;EACnC,OAAOC,KAAK,CAACF,KAAK,EAAEC,IAAI,CAAC,CAACE,IAAI,CAACV,YAAY,CAAC;AAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}