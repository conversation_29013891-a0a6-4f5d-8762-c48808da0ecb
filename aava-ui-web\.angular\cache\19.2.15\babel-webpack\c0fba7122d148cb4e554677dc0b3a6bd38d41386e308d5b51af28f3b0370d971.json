{"ast": null, "code": "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\nexport var scheme = new Array(3).concat(\"ef8a62f7f7f767a9cf\", \"ca0020f4a58292c5de0571b0\", \"ca0020f4a582f7f7f792c5de0571b0\", \"b2182bef8a62fddbc7d1e5f067a9cf2166ac\", \"b2182bef8a62fddbc7f7f7f7d1e5f067a9cf2166ac\", \"b2182bd6604df4a582fddbc7d1e5f092c5de4393c32166ac\", \"b2182bd6604df4a582fddbc7f7f7f7d1e5f092c5de4393c32166ac\", \"67001fb2182bd6604df4a582fddbc7d1e5f092c5de4393c32166ac053061\", \"67001fb2182bd6604df4a582fddbc7f7f7f7d1e5f092c5de4393c32166ac053061\").map(colors);\nexport default ramp(scheme);", "map": {"version": 3, "names": ["colors", "ramp", "scheme", "Array", "concat", "map"], "sources": ["C:/console/aava-ui-web/node_modules/d3-scale-chromatic/src/diverging/RdBu.js"], "sourcesContent": ["import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"ef8a62f7f7f767a9cf\",\n  \"ca0020f4a58292c5de0571b0\",\n  \"ca0020f4a582f7f7f792c5de0571b0\",\n  \"b2182bef8a62fddbc7d1e5f067a9cf2166ac\",\n  \"b2182bef8a62fddbc7f7f7f7d1e5f067a9cf2166ac\",\n  \"b2182bd6604df4a582fddbc7d1e5f092c5de4393c32166ac\",\n  \"b2182bd6604df4a582fddbc7f7f7f7d1e5f092c5de4393c32166ac\",\n  \"67001fb2182bd6604df4a582fddbc7d1e5f092c5de4393c32166ac053061\",\n  \"67001fb2182bd6604df4a582fddbc7f7f7f7d1e5f092c5de4393c32166ac053061\"\n).map(colors);\n\nexport default ramp(scheme);\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,cAAc;AACjC,OAAOC,IAAI,MAAM,YAAY;AAE7B,OAAO,IAAIC,MAAM,GAAG,IAAIC,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,CACrC,oBAAoB,EACpB,0BAA0B,EAC1B,gCAAgC,EAChC,sCAAsC,EACtC,4CAA4C,EAC5C,kDAAkD,EAClD,wDAAwD,EACxD,8DAA8D,EAC9D,oEACF,CAAC,CAACC,GAAG,CAACL,MAAM,CAAC;AAEb,eAAeC,IAAI,CAACC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}