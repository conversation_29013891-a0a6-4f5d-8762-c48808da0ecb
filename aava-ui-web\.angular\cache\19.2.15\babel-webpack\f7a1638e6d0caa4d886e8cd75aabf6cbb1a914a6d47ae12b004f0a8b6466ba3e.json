{"ast": null, "code": "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\nexport var scheme = new Array(3).concat(\"af8dc3f7f7f77fbf7b\", \"7b3294c2a5cfa6dba0008837\", \"7b3294c2a5cff7f7f7a6dba0008837\", \"762a83af8dc3e7d4e8d9f0d37fbf7b1b7837\", \"762a83af8dc3e7d4e8f7f7f7d9f0d37fbf7b1b7837\", \"762a839970abc2a5cfe7d4e8d9f0d3a6dba05aae611b7837\", \"762a839970abc2a5cfe7d4e8f7f7f7d9f0d3a6dba05aae611b7837\", \"40004b762a839970abc2a5cfe7d4e8d9f0d3a6dba05aae611b783700441b\", \"40004b762a839970abc2a5cfe7d4e8f7f7f7d9f0d3a6dba05aae611b783700441b\").map(colors);\nexport default ramp(scheme);", "map": {"version": 3, "names": ["colors", "ramp", "scheme", "Array", "concat", "map"], "sources": ["C:/console/aava-ui-web/node_modules/d3-scale-chromatic/src/diverging/PRGn.js"], "sourcesContent": ["import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"af8dc3f7f7f77fbf7b\",\n  \"7b3294c2a5cfa6dba0008837\",\n  \"7b3294c2a5cff7f7f7a6dba0008837\",\n  \"762a83af8dc3e7d4e8d9f0d37fbf7b1b7837\",\n  \"762a83af8dc3e7d4e8f7f7f7d9f0d37fbf7b1b7837\",\n  \"762a839970abc2a5cfe7d4e8d9f0d3a6dba05aae611b7837\",\n  \"762a839970abc2a5cfe7d4e8f7f7f7d9f0d3a6dba05aae611b7837\",\n  \"40004b762a839970abc2a5cfe7d4e8d9f0d3a6dba05aae611b783700441b\",\n  \"40004b762a839970abc2a5cfe7d4e8f7f7f7d9f0d3a6dba05aae611b783700441b\"\n).map(colors);\n\nexport default ramp(scheme);\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,cAAc;AACjC,OAAOC,IAAI,MAAM,YAAY;AAE7B,OAAO,IAAIC,MAAM,GAAG,IAAIC,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,CACrC,oBAAoB,EACpB,0BAA0B,EAC1B,gCAAgC,EAChC,sCAAsC,EACtC,4CAA4C,EAC5C,kDAAkD,EAClD,wDAAwD,EACxD,8DAA8D,EAC9D,oEACF,CAAC,CAACC,GAAG,CAACL,MAAM,CAAC;AAEb,eAAeC,IAAI,CAACC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}