{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction AnalyticsCardComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵelement(1, \"div\", 33);\n    i0.ɵɵelementStart(2, \"span\", 34);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background-color\", category_r1.color);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r1.name);\n  }\n}\nfunction AnalyticsCardComponent_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵelement(1, \"div\", 33);\n    i0.ɵɵelementStart(2, \"span\", 34);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background-color\", category_r2.color);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r2.name);\n  }\n}\nexport let AnalyticsCardComponent = /*#__PURE__*/(() => {\n  class AnalyticsCardComponent {\n    analyticsData = {\n      agentsUsedToday: {\n        count: 12,\n        percentage: '20%',\n        chartData: [{\n          date: new Date('2024-01-01'),\n          value: 8\n        }, {\n          date: new Date('2024-01-02'),\n          value: 12\n        }, {\n          date: new Date('2024-01-03'),\n          value: 6\n        }, {\n          date: new Date('2024-01-04'),\n          value: 15\n        }, {\n          date: new Date('2024-01-05'),\n          value: 10\n        }, {\n          date: new Date('2024-01-06'),\n          value: 18\n        }, {\n          date: new Date('2024-01-07'),\n          value: 12\n        }]\n      },\n      taskAchieved: {\n        percentage: 98,\n        categories: {\n          backend: 35,\n          frontend: 25,\n          creation: 25,\n          testing: 15\n        }\n      },\n      timeSaved: {\n        minutes: 200,\n        agentsUsed: 285,\n        chartData: [{\n          category: 'Mon',\n          value: 45\n        }, {\n          category: 'Tue',\n          value: 30\n        }, {\n          category: 'Wed',\n          value: 55\n        }, {\n          category: 'Thu',\n          value: 25\n        }, {\n          category: 'Fri',\n          value: 40\n        }, {\n          category: 'Sat',\n          value: 35\n        }, {\n          category: 'Sun',\n          value: 50\n        }]\n      }\n    };\n    ngOnInit() {\n      // Component initialization\n    }\n    getTaskAchievedCategories() {\n      return [{\n        name: 'Backend',\n        value: this.analyticsData.taskAchieved.categories.backend,\n        color: '#8B5CF6'\n      }, {\n        name: 'Frontend',\n        value: this.analyticsData.taskAchieved.categories.frontend,\n        color: '#EC4899'\n      }, {\n        name: 'Creation',\n        value: this.analyticsData.taskAchieved.categories.creation,\n        color: '#F97316'\n      }, {\n        name: 'Testing',\n        value: this.analyticsData.taskAchieved.categories.testing,\n        color: '#10B981'\n      }];\n    }\n    static ɵfac = function AnalyticsCardComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AnalyticsCardComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AnalyticsCardComponent,\n      selectors: [[\"app-analytics-card\"]],\n      decls: 60,\n      vars: 8,\n      consts: [[1, \"analytics\"], [1, \"analytics-section\", \"agents-section\"], [1, \"section-header\"], [1, \"section-content\"], [1, \"metric-display\"], [1, \"metric-value\"], [1, \"metric-badge\"], [1, \"chart-container\"], [1, \"line-chart\"], [1, \"chart-line\"], [\"viewBox\", \"0 0 280 80\", 1, \"line-svg\"], [\"d\", \"M 10 60 Q 50 40 90 50 Q 130 30 170 45 Q 210 25 250 35 Q 270 30 280 25\", \"stroke\", \"#8B5CF6\", \"stroke-width\", \"2\", \"fill\", \"none\", \"stroke-linecap\", \"round\"], [\"cx\", \"280\", \"cy\", \"25\", \"r\", \"3\", \"fill\", \"#8B5CF6\"], [1, \"analytics-section\", \"task-section\"], [1, \"task-content\"], [1, \"task-percentage\"], [1, \"percentage-circle\"], [1, \"percentage-text\"], [1, \"task-legend\"], [\"class\", \"legend-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"analytics-section\", \"time-section\"], [1, \"time-display\"], [1, \"time-value\"], [1, \"time-subtitle\"], [1, \"bar-chart\"], [1, \"bar-item\", 2, \"height\", \"45%\"], [1, \"bar-item\", 2, \"height\", \"30%\"], [1, \"bar-item\", 2, \"height\", \"55%\"], [1, \"bar-item\", 2, \"height\", \"25%\"], [1, \"bar-item\", 2, \"height\", \"40%\"], [1, \"bar-item\", 2, \"height\", \"35%\"], [1, \"bar-item\", 2, \"height\", \"50%\"], [1, \"legend-item\"], [1, \"legend-dot\"], [1, \"legend-label\"]],\n      template: function AnalyticsCardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h3\");\n          i0.ɵɵtext(4, \"Agents used today\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"div\", 3)(6, \"div\", 4)(7, \"div\", 5);\n          i0.ɵɵtext(8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 6);\n          i0.ɵɵtext(10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 7)(12, \"div\", 8)(13, \"div\", 9);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(14, \"svg\", 10);\n          i0.ɵɵelement(15, \"path\", 11)(16, \"circle\", 12);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(17, \"div\", 13)(18, \"div\", 2)(19, \"h3\");\n          i0.ɵɵtext(20, \"Task Achieved\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(21, \"div\", 3)(22, \"div\", 14)(23, \"div\", 15)(24, \"div\", 16)(25, \"div\", 17);\n          i0.ɵɵtext(26);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(27, \"div\", 18);\n          i0.ɵɵtemplate(28, AnalyticsCardComponent_div_28_Template, 4, 3, \"div\", 19);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(29, \"div\", 20)(30, \"div\", 2)(31, \"h3\");\n          i0.ɵɵtext(32, \"Time saved\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(33, \"div\", 3)(34, \"div\", 21)(35, \"div\", 22);\n          i0.ɵɵtext(36);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"div\", 23);\n          i0.ɵɵtext(38);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"div\", 7)(40, \"div\", 24);\n          i0.ɵɵelement(41, \"div\", 25)(42, \"div\", 26)(43, \"div\", 27)(44, \"div\", 28)(45, \"div\", 29)(46, \"div\", 30)(47, \"div\", 31);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(48, \"div\", 13)(49, \"div\", 2)(50, \"h3\");\n          i0.ɵɵtext(51, \"Task Achieved\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(52, \"div\", 3)(53, \"div\", 14)(54, \"div\", 15)(55, \"div\", 16)(56, \"div\", 17);\n          i0.ɵɵtext(57);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(58, \"div\", 18);\n          i0.ɵɵtemplate(59, AnalyticsCardComponent_div_59_Template, 4, 3, \"div\", 19);\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵtextInterpolate1(\"\", ctx.analyticsData.agentsUsedToday.count, \" Agents\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.analyticsData.agentsUsedToday.percentage);\n          i0.ɵɵadvance(16);\n          i0.ɵɵtextInterpolate1(\"\", ctx.analyticsData.taskAchieved.percentage, \"%\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.getTaskAchievedCategories());\n          i0.ɵɵadvance(8);\n          i0.ɵɵtextInterpolate1(\"\", ctx.analyticsData.timeSaved.minutes, \" Min\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\"\", ctx.analyticsData.timeSaved.agentsUsed, \" Agents used\");\n          i0.ɵɵadvance(19);\n          i0.ɵɵtextInterpolate1(\"\", ctx.analyticsData.taskAchieved.percentage, \"%\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.getTaskAchievedCategories());\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf],\n      styles: [\".analytics[_ngcontent-%COMP%] {\\n  gap: 48px;\\n}\\n\\n.analytics-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 600;\\n  color: #1f2937;\\n  margin: 0;\\n  letter-spacing: -0.025em;\\n}\\n\\n.analytics-section[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.8);\\n  border-radius: 16px;\\n  padding: 20px;\\n  border: 1px solid rgba(139, 92, 246, 0.1);\\n  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.05);\\n  -webkit-backdrop-filter: blur(8px);\\n          backdrop-filter: blur(8px);\\n  margin-bottom: 24px;\\n}\\n.analytics-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.analytics-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n  color: #6b7280;\\n  margin: 0;\\n}\\n\\n.agents-section[_ngcontent-%COMP%]   .metric-display[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  margin-bottom: 16px;\\n}\\n.agents-section[_ngcontent-%COMP%]   .metric-display[_ngcontent-%COMP%]   .metric-value[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: #1f2937;\\n}\\n.agents-section[_ngcontent-%COMP%]   .metric-display[_ngcontent-%COMP%]   .metric-badge[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #8B5CF6 0%, #EC4899 100%);\\n  color: white;\\n  padding: 4px 12px;\\n  border-radius: 12px;\\n  font-size: 12px;\\n  font-weight: 500;\\n}\\n.agents-section[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%] {\\n  height: 80px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.agents-section[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   .line-chart[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n}\\n.agents-section[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   .line-chart[_ngcontent-%COMP%]   .line-svg[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n}\\n\\n.task-section[_ngcontent-%COMP%]   .task-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 20px;\\n}\\n.task-section[_ngcontent-%COMP%]   .task-percentage[_ngcontent-%COMP%]   .percentage-circle[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  border-radius: 50%;\\n  background: conic-gradient(from 0deg, #8B5CF6 0deg 126deg, #EC4899 126deg 216deg, #F97316 216deg 306deg, #10B981 306deg 360deg);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: relative;\\n}\\n.task-section[_ngcontent-%COMP%]   .task-percentage[_ngcontent-%COMP%]   .percentage-circle[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  width: 50px;\\n  height: 50px;\\n  background: white;\\n  border-radius: 50%;\\n}\\n.task-section[_ngcontent-%COMP%]   .task-percentage[_ngcontent-%COMP%]   .percentage-circle[_ngcontent-%COMP%]   .percentage-text[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #1f2937;\\n  position: relative;\\n  z-index: 1;\\n}\\n.task-section[_ngcontent-%COMP%]   .task-legend[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n.task-section[_ngcontent-%COMP%]   .task-legend[_ngcontent-%COMP%]   .legend-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.task-section[_ngcontent-%COMP%]   .task-legend[_ngcontent-%COMP%]   .legend-item[_ngcontent-%COMP%]   .legend-dot[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 50%;\\n}\\n.task-section[_ngcontent-%COMP%]   .task-legend[_ngcontent-%COMP%]   .legend-item[_ngcontent-%COMP%]   .legend-label[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #6b7280;\\n  font-weight: 500;\\n}\\n\\n.time-section[_ngcontent-%COMP%]   .time-display[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.time-section[_ngcontent-%COMP%]   .time-display[_ngcontent-%COMP%]   .time-value[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  font-weight: 600;\\n  color: #1f2937;\\n  line-height: 1;\\n  margin-bottom: 4px;\\n}\\n.time-section[_ngcontent-%COMP%]   .time-display[_ngcontent-%COMP%]   .time-subtitle[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #6b7280;\\n  font-weight: 500;\\n}\\n.time-section[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%] {\\n  height: 60px;\\n  display: flex;\\n  align-items: end;\\n  justify-content: center;\\n}\\n.time-section[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   .bar-chart[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: end;\\n  gap: 4px;\\n  height: 100%;\\n  width: 100%;\\n  justify-content: center;\\n}\\n.time-section[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   .bar-chart[_ngcontent-%COMP%]   .bar-item[_ngcontent-%COMP%] {\\n  width: 8px;\\n  background: linear-gradient(180deg, #8B5CF6 0%, #EC4899 100%);\\n  border-radius: 2px 2px 0 0;\\n  min-height: 4px;\\n  transition: all 0.3s ease;\\n}\\n.time-section[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   .bar-chart[_ngcontent-%COMP%]   .bar-item[_ngcontent-%COMP%]:nth-child(2n) {\\n  background: linear-gradient(180deg, #EC4899 0%, #F97316 100%);\\n}\\n.time-section[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   .bar-chart[_ngcontent-%COMP%]   .bar-item[_ngcontent-%COMP%]:nth-child(3n) {\\n  background: linear-gradient(180deg, #F97316 0%, #10B981 100%);\\n}\\n.time-section[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   .bar-chart[_ngcontent-%COMP%]   .bar-item[_ngcontent-%COMP%]:nth-child(4n) {\\n  background: linear-gradient(180deg, #10B981 0%, #8B5CF6 100%);\\n}\\n\\n.dark-theme[_ngcontent-%COMP%]   .analytics-container[_ngcontent-%COMP%] {\\n  background: linear-gradient(102deg, rgba(30, 30, 35, 0.7) 1.07%, rgba(40, 40, 45, 0.7) 98.01%);\\n  border: 1px solid rgba(80, 80, 95, 0.2);\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), 0 4px 16px rgba(139, 92, 246, 0.1);\\n}\\n.dark-theme[_ngcontent-%COMP%]   .analytics-container[_ngcontent-%COMP%]::before {\\n  background: linear-gradient(135deg, rgba(139, 92, 246, 0.05) 0%, rgba(236, 72, 153, 0.05) 50%, rgba(30, 30, 35, 0.95) 100%);\\n}\\n.dark-theme[_ngcontent-%COMP%]   .analytics-container[_ngcontent-%COMP%]   .analytics-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  color: #e0e0e0;\\n}\\n.dark-theme[_ngcontent-%COMP%]   .analytics-container[_ngcontent-%COMP%]   .analytics-section[_ngcontent-%COMP%] {\\n  background: rgba(40, 40, 50, 0.6);\\n  border: 1px solid rgba(80, 80, 95, 0.2);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\\n}\\n.dark-theme[_ngcontent-%COMP%]   .analytics-container[_ngcontent-%COMP%]   .analytics-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #a0a0a0;\\n}\\n.dark-theme[_ngcontent-%COMP%]   .analytics-container[_ngcontent-%COMP%]   .agents-section[_ngcontent-%COMP%]   .metric-display[_ngcontent-%COMP%]   .metric-value[_ngcontent-%COMP%], \\n.dark-theme[_ngcontent-%COMP%]   .analytics-container[_ngcontent-%COMP%]   .task-section[_ngcontent-%COMP%]   .percentage-text[_ngcontent-%COMP%], \\n.dark-theme[_ngcontent-%COMP%]   .analytics-container[_ngcontent-%COMP%]   .time-section[_ngcontent-%COMP%]   .time-value[_ngcontent-%COMP%] {\\n  color: #e0e0e0;\\n}\\n.dark-theme[_ngcontent-%COMP%]   .analytics-container[_ngcontent-%COMP%]   .task-section[_ngcontent-%COMP%]   .task-legend[_ngcontent-%COMP%]   .legend-label[_ngcontent-%COMP%], \\n.dark-theme[_ngcontent-%COMP%]   .analytics-container[_ngcontent-%COMP%]   .time-section[_ngcontent-%COMP%]   .time-subtitle[_ngcontent-%COMP%] {\\n  color: #a0a0a0;\\n}\\n.dark-theme[_ngcontent-%COMP%]   .analytics-container[_ngcontent-%COMP%]   .task-section[_ngcontent-%COMP%]   .percentage-circle[_ngcontent-%COMP%]::before {\\n  background: rgba(40, 40, 50, 0.9);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return AnalyticsCardComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵstyleProp", "category_r1", "color", "ɵɵtextInterpolate", "name", "category_r2", "AnalyticsCardComponent", "analyticsData", "agentsUsedToday", "count", "percentage", "chartData", "date", "Date", "value", "taskAchieved", "categories", "backend", "frontend", "creation", "testing", "timeSaved", "minutes", "agentsUsed", "category", "ngOnInit", "getTaskAchievedCategories", "selectors", "decls", "vars", "consts", "template", "AnalyticsCardComponent_Template", "rf", "ctx", "ɵɵtemplate", "AnalyticsCardComponent_div_28_Template", "AnalyticsCardComponent_div_59_Template", "ɵɵtextInterpolate1", "ɵɵproperty", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\shared\\components\\analytics-card\\analytics-card.component.ts", "C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\shared\\components\\analytics-card\\analytics-card.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\ninterface AnalyticsData {\r\n  agentsUsedToday: {\r\n    count: number;\r\n    percentage: string;\r\n    chartData: any[];\r\n  };\r\n  taskAchieved: {\r\n    percentage: number;\r\n    categories: {\r\n      backend: number;\r\n      frontend: number;\r\n      creation: number;\r\n      testing: number;\r\n    };\r\n  };\r\n  timeSaved: {\r\n    minutes: number;\r\n    agentsUsed: number;\r\n    chartData: any[];\r\n  };\r\n}\r\n\r\n@Component({\r\n  selector: 'app-analytics-card',\r\n  standalone: true,\r\n  imports: [CommonModule],\r\n  templateUrl: './analytics-card.component.html',\r\n  styleUrls: ['./analytics-card.component.scss']\r\n})\r\nexport class AnalyticsCardComponent implements OnInit {\r\n  \r\n  analyticsData: AnalyticsData = {\r\n    agentsUsedToday: {\r\n      count: 12,\r\n      percentage: '20%',\r\n      chartData: [\r\n        { date: new Date('2024-01-01'), value: 8 },\r\n        { date: new Date('2024-01-02'), value: 12 },\r\n        { date: new Date('2024-01-03'), value: 6 },\r\n        { date: new Date('2024-01-04'), value: 15 },\r\n        { date: new Date('2024-01-05'), value: 10 },\r\n        { date: new Date('2024-01-06'), value: 18 },\r\n        { date: new Date('2024-01-07'), value: 12 }\r\n      ]\r\n    },\r\n    taskAchieved: {\r\n      percentage: 98,\r\n      categories: {\r\n        backend: 35,\r\n        frontend: 25,\r\n        creation: 25,\r\n        testing: 15\r\n      }\r\n    },\r\n    timeSaved: {\r\n      minutes: 200,\r\n      agentsUsed: 285,\r\n      chartData: [\r\n        { category: 'Mon', value: 45 },\r\n        { category: 'Tue', value: 30 },\r\n        { category: 'Wed', value: 55 },\r\n        { category: 'Thu', value: 25 },\r\n        { category: 'Fri', value: 40 },\r\n        { category: 'Sat', value: 35 },\r\n        { category: 'Sun', value: 50 }\r\n      ]\r\n    }\r\n  };\r\n\r\n  ngOnInit() {\r\n    // Component initialization\r\n  }\r\n\r\n  getTaskAchievedCategories() {\r\n    return [\r\n      { name: 'Backend', value: this.analyticsData.taskAchieved.categories.backend, color: '#8B5CF6' },\r\n      { name: 'Frontend', value: this.analyticsData.taskAchieved.categories.frontend, color: '#EC4899' },\r\n      { name: 'Creation', value: this.analyticsData.taskAchieved.categories.creation, color: '#F97316' },\r\n      { name: 'Testing', value: this.analyticsData.taskAchieved.categories.testing, color: '#10B981' }\r\n    ];\r\n  }\r\n}\r\n", "<div class=\"analytics\">\r\n\r\n  <!-- Agents Used Today Section -->\r\n  <div class=\"analytics-section agents-section\">\r\n    <div class=\"section-header\">\r\n      <h3>Agents used today</h3>\r\n    </div>\r\n    <div class=\"section-content\">\r\n      <div class=\"metric-display\">\r\n        <div class=\"metric-value\">{{ analyticsData.agentsUsedToday.count }} Agents</div>\r\n        <div class=\"metric-badge\">{{ analyticsData.agentsUsedToday.percentage }}</div>\r\n      </div>\r\n      <div class=\"chart-container\">\r\n        <div class=\"line-chart\">\r\n          <div class=\"chart-line\">\r\n            <svg viewBox=\"0 0 280 80\" class=\"line-svg\">\r\n              <path d=\"M 10 60 Q 50 40 90 50 Q 130 30 170 45 Q 210 25 250 35 Q 270 30 280 25\"\r\n                    stroke=\"#8B5CF6\"\r\n                    stroke-width=\"2\"\r\n                    fill=\"none\"\r\n                    stroke-linecap=\"round\"/>\r\n              <circle cx=\"280\" cy=\"25\" r=\"3\" fill=\"#8B5CF6\"/>\r\n            </svg>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Task Achieved Section -->\r\n  <div class=\"analytics-section task-section\">\r\n    <div class=\"section-header\">\r\n      <h3>Task Achieved</h3>\r\n    </div>\r\n    <div class=\"section-content\">\r\n      <div class=\"task-content\">\r\n        <div class=\"task-percentage\">\r\n          <div class=\"percentage-circle\">\r\n            <div class=\"percentage-text\">{{ analyticsData.taskAchieved.percentage }}%</div>\r\n          </div>\r\n        </div>\r\n        <div class=\"task-legend\">\r\n          <div class=\"legend-item\" *ngFor=\"let category of getTaskAchievedCategories()\">\r\n            <div class=\"legend-dot\" [style.background-color]=\"category.color\"></div>\r\n            <span class=\"legend-label\">{{ category.name }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Time Saved Section -->\r\n  <div class=\"analytics-section time-section\">\r\n    <div class=\"section-header\">\r\n      <h3>Time saved</h3>\r\n    </div>\r\n    <div class=\"section-content\">\r\n      <div class=\"time-display\">\r\n        <div class=\"time-value\">{{ analyticsData.timeSaved.minutes }} Min</div>\r\n        <div class=\"time-subtitle\">{{ analyticsData.timeSaved.agentsUsed }} Agents used</div>\r\n      </div>\r\n      <div class=\"chart-container\">\r\n        <div class=\"bar-chart\">\r\n          <div class=\"bar-item\" style=\"height: 45%\"></div>\r\n          <div class=\"bar-item\" style=\"height: 30%\"></div>\r\n          <div class=\"bar-item\" style=\"height: 55%\"></div>\r\n          <div class=\"bar-item\" style=\"height: 25%\"></div>\r\n          <div class=\"bar-item\" style=\"height: 40%\"></div>\r\n          <div class=\"bar-item\" style=\"height: 35%\"></div>\r\n          <div class=\"bar-item\" style=\"height: 50%\"></div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n    <div class=\"analytics-section task-section\">\r\n    <div class=\"section-header\">\r\n      <h3>Task Achieved</h3>\r\n    </div>\r\n    <div class=\"section-content\">\r\n      <div class=\"task-content\">\r\n        <div class=\"task-percentage\">\r\n          <div class=\"percentage-circle\">\r\n            <div class=\"percentage-text\">{{ analyticsData.taskAchieved.percentage }}%</div>\r\n          </div>\r\n        </div>\r\n        <div class=\"task-legend\">\r\n          <div class=\"legend-item\" *ngFor=\"let category of getTaskAchievedCategories()\">\r\n            <div class=\"legend-dot\" [style.background-color]=\"category.color\"></div>\r\n            <span class=\"legend-label\">{{ category.name }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;;;;;ICyCpCC,EAAA,CAAAC,cAAA,cAA8E;IAC5ED,EAAA,CAAAE,SAAA,cAAwE;IACxEF,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAChDH,EADgD,CAAAI,YAAA,EAAO,EACjD;;;;IAFoBJ,EAAA,CAAAK,SAAA,EAAyC;IAAzCL,EAAA,CAAAM,WAAA,qBAAAC,WAAA,CAAAC,KAAA,CAAyC;IACtCR,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAS,iBAAA,CAAAF,WAAA,CAAAG,IAAA,CAAmB;;;;;IA2ChDV,EAAA,CAAAC,cAAA,cAA8E;IAC5ED,EAAA,CAAAE,SAAA,cAAwE;IACxEF,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAChDH,EADgD,CAAAI,YAAA,EAAO,EACjD;;;;IAFoBJ,EAAA,CAAAK,SAAA,EAAyC;IAAzCL,EAAA,CAAAM,WAAA,qBAAAK,WAAA,CAAAH,KAAA,CAAyC;IACtCR,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAS,iBAAA,CAAAE,WAAA,CAAAD,IAAA,CAAmB;;;ADzD1D,WAAaE,sBAAsB;EAA7B,MAAOA,sBAAsB;IAEjCC,aAAa,GAAkB;MAC7BC,eAAe,EAAE;QACfC,KAAK,EAAE,EAAE;QACTC,UAAU,EAAE,KAAK;QACjBC,SAAS,EAAE,CACT;UAAEC,IAAI,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;UAAEC,KAAK,EAAE;QAAC,CAAE,EAC1C;UAAEF,IAAI,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;UAAEC,KAAK,EAAE;QAAE,CAAE,EAC3C;UAAEF,IAAI,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;UAAEC,KAAK,EAAE;QAAC,CAAE,EAC1C;UAAEF,IAAI,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;UAAEC,KAAK,EAAE;QAAE,CAAE,EAC3C;UAAEF,IAAI,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;UAAEC,KAAK,EAAE;QAAE,CAAE,EAC3C;UAAEF,IAAI,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;UAAEC,KAAK,EAAE;QAAE,CAAE,EAC3C;UAAEF,IAAI,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;UAAEC,KAAK,EAAE;QAAE,CAAE;OAE9C;MACDC,YAAY,EAAE;QACZL,UAAU,EAAE,EAAE;QACdM,UAAU,EAAE;UACVC,OAAO,EAAE,EAAE;UACXC,QAAQ,EAAE,EAAE;UACZC,QAAQ,EAAE,EAAE;UACZC,OAAO,EAAE;;OAEZ;MACDC,SAAS,EAAE;QACTC,OAAO,EAAE,GAAG;QACZC,UAAU,EAAE,GAAG;QACfZ,SAAS,EAAE,CACT;UAAEa,QAAQ,EAAE,KAAK;UAAEV,KAAK,EAAE;QAAE,CAAE,EAC9B;UAAEU,QAAQ,EAAE,KAAK;UAAEV,KAAK,EAAE;QAAE,CAAE,EAC9B;UAAEU,QAAQ,EAAE,KAAK;UAAEV,KAAK,EAAE;QAAE,CAAE,EAC9B;UAAEU,QAAQ,EAAE,KAAK;UAAEV,KAAK,EAAE;QAAE,CAAE,EAC9B;UAAEU,QAAQ,EAAE,KAAK;UAAEV,KAAK,EAAE;QAAE,CAAE,EAC9B;UAAEU,QAAQ,EAAE,KAAK;UAAEV,KAAK,EAAE;QAAE,CAAE,EAC9B;UAAEU,QAAQ,EAAE,KAAK;UAAEV,KAAK,EAAE;QAAE,CAAE;;KAGnC;IAEDW,QAAQA,CAAA;MACN;IAAA;IAGFC,yBAAyBA,CAAA;MACvB,OAAO,CACL;QAAEtB,IAAI,EAAE,SAAS;QAAEU,KAAK,EAAE,IAAI,CAACP,aAAa,CAACQ,YAAY,CAACC,UAAU,CAACC,OAAO;QAAEf,KAAK,EAAE;MAAS,CAAE,EAChG;QAAEE,IAAI,EAAE,UAAU;QAAEU,KAAK,EAAE,IAAI,CAACP,aAAa,CAACQ,YAAY,CAACC,UAAU,CAACE,QAAQ;QAAEhB,KAAK,EAAE;MAAS,CAAE,EAClG;QAAEE,IAAI,EAAE,UAAU;QAAEU,KAAK,EAAE,IAAI,CAACP,aAAa,CAACQ,YAAY,CAACC,UAAU,CAACG,QAAQ;QAAEjB,KAAK,EAAE;MAAS,CAAE,EAClG;QAAEE,IAAI,EAAE,SAAS;QAAEU,KAAK,EAAE,IAAI,CAACP,aAAa,CAACQ,YAAY,CAACC,UAAU,CAACI,OAAO;QAAElB,KAAK,EAAE;MAAS,CAAE,CACjG;IACH;;uCAnDWI,sBAAsB;IAAA;;YAAtBA,sBAAsB;MAAAqB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC3B7BvC,EALN,CAAAC,cAAA,aAAuB,aAGyB,aAChB,SACtB;UAAAD,EAAA,CAAAG,MAAA,wBAAiB;UACvBH,EADuB,CAAAI,YAAA,EAAK,EACtB;UAGFJ,EAFJ,CAAAC,cAAA,aAA6B,aACC,aACA;UAAAD,EAAA,CAAAG,MAAA,GAAgD;UAAAH,EAAA,CAAAI,YAAA,EAAM;UAChFJ,EAAA,CAAAC,cAAA,aAA0B;UAAAD,EAAA,CAAAG,MAAA,IAA8C;UAC1EH,EAD0E,CAAAI,YAAA,EAAM,EAC1E;UAGFJ,EAFJ,CAAAC,cAAA,cAA6B,cACH,cACE;;UACtBD,EAAA,CAAAC,cAAA,eAA2C;UAMzCD,EALA,CAAAE,SAAA,gBAI8B,kBACiB;UAM3DF,EALU,CAAAI,YAAA,EAAM,EACF,EACF,EACF,EACF,EACF;;UAKFJ,EAFJ,CAAAC,cAAA,eAA4C,cACd,UACtB;UAAAD,EAAA,CAAAG,MAAA,qBAAa;UACnBH,EADmB,CAAAI,YAAA,EAAK,EAClB;UAKEJ,EAJR,CAAAC,cAAA,cAA6B,eACD,eACK,eACI,eACA;UAAAD,EAAA,CAAAG,MAAA,IAA4C;UAE7EH,EAF6E,CAAAI,YAAA,EAAM,EAC3E,EACF;UACNJ,EAAA,CAAAC,cAAA,eAAyB;UACvBD,EAAA,CAAAyC,UAAA,KAAAC,sCAAA,kBAA8E;UAOtF1C,EAHM,CAAAI,YAAA,EAAM,EACF,EACF,EACF;UAKFJ,EAFJ,CAAAC,cAAA,eAA4C,cACd,UACtB;UAAAD,EAAA,CAAAG,MAAA,kBAAU;UAChBH,EADgB,CAAAI,YAAA,EAAK,EACf;UAGFJ,EAFJ,CAAAC,cAAA,cAA6B,eACD,eACA;UAAAD,EAAA,CAAAG,MAAA,IAAyC;UAAAH,EAAA,CAAAI,YAAA,EAAM;UACvEJ,EAAA,CAAAC,cAAA,eAA2B;UAAAD,EAAA,CAAAG,MAAA,IAAoD;UACjFH,EADiF,CAAAI,YAAA,EAAM,EACjF;UAEJJ,EADF,CAAAC,cAAA,cAA6B,eACJ;UAOrBD,EANA,CAAAE,SAAA,eAAgD,eACA,eACA,eACA,eACA,eACA,eACA;UAIxDF,EAHM,CAAAI,YAAA,EAAM,EACF,EACF,EACF;UAIFJ,EAFF,CAAAC,cAAA,eAA4C,cAChB,UACtB;UAAAD,EAAA,CAAAG,MAAA,qBAAa;UACnBH,EADmB,CAAAI,YAAA,EAAK,EAClB;UAKEJ,EAJR,CAAAC,cAAA,cAA6B,eACD,eACK,eACI,eACA;UAAAD,EAAA,CAAAG,MAAA,IAA4C;UAE7EH,EAF6E,CAAAI,YAAA,EAAM,EAC3E,EACF;UACNJ,EAAA,CAAAC,cAAA,eAAyB;UACvBD,EAAA,CAAAyC,UAAA,KAAAE,sCAAA,kBAA8E;UAOtF3C,EAHM,CAAAI,YAAA,EAAM,EACF,EACF,EACF,EA9Fe;;;UASWJ,EAAA,CAAAK,SAAA,GAAgD;UAAhDL,EAAA,CAAA4C,kBAAA,KAAAJ,GAAA,CAAA3B,aAAA,CAAAC,eAAA,CAAAC,KAAA,YAAgD;UAChDf,EAAA,CAAAK,SAAA,GAA8C;UAA9CL,EAAA,CAAAS,iBAAA,CAAA+B,GAAA,CAAA3B,aAAA,CAAAC,eAAA,CAAAE,UAAA,CAA8C;UA4BvChB,EAAA,CAAAK,SAAA,IAA4C;UAA5CL,EAAA,CAAA4C,kBAAA,KAAAJ,GAAA,CAAA3B,aAAA,CAAAQ,YAAA,CAAAL,UAAA,MAA4C;UAI7BhB,EAAA,CAAAK,SAAA,GAA8B;UAA9BL,EAAA,CAAA6C,UAAA,YAAAL,GAAA,CAAAR,yBAAA,GAA8B;UAgBtDhC,EAAA,CAAAK,SAAA,GAAyC;UAAzCL,EAAA,CAAA4C,kBAAA,KAAAJ,GAAA,CAAA3B,aAAA,CAAAc,SAAA,CAAAC,OAAA,SAAyC;UACtC5B,EAAA,CAAAK,SAAA,GAAoD;UAApDL,EAAA,CAAA4C,kBAAA,KAAAJ,GAAA,CAAA3B,aAAA,CAAAc,SAAA,CAAAE,UAAA,iBAAoD;UAwB9C7B,EAAA,CAAAK,SAAA,IAA4C;UAA5CL,EAAA,CAAA4C,kBAAA,KAAAJ,GAAA,CAAA3B,aAAA,CAAAQ,YAAA,CAAAL,UAAA,MAA4C;UAI7BhB,EAAA,CAAAK,SAAA,GAA8B;UAA9BL,EAAA,CAAA6C,UAAA,YAAAL,GAAA,CAAAR,yBAAA,GAA8B;;;qBD3D1EjC,YAAY,EAAA+C,EAAA,CAAAC,OAAA;MAAAC,MAAA;IAAA;;SAIXpC,sBAAsB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}