{"ast": null, "code": "var array = Array.prototype;\nexport var slice = array.slice;", "map": {"version": 3, "names": ["array", "Array", "prototype", "slice"], "sources": ["C:/console/aava-ui-web/node_modules/d3-contour/src/array.js"], "sourcesContent": ["var array = Array.prototype;\n\nexport var slice = array.slice;\n"], "mappings": "AAAA,IAAIA,KAAK,GAAGC,KAAK,CAACC,SAAS;AAE3B,OAAO,IAAIC,KAAK,GAAGH,KAAK,CAACG,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}