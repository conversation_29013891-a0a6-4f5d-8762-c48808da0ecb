{"ast": null, "code": "import constant from \"./constant.js\";\nfunction linear(a, d) {\n  return function (t) {\n    return a + t * d;\n  };\n}\nfunction exponential(a, b, y) {\n  return a = Math.pow(a, y), b = Math.pow(b, y) - a, y = 1 / y, function (t) {\n    return Math.pow(a + t * b, y);\n  };\n}\nexport function hue(a, b) {\n  var d = b - a;\n  return d ? linear(a, d > 180 || d < -180 ? d - 360 * Math.round(d / 360) : d) : constant(isNaN(a) ? b : a);\n}\nexport function gamma(y) {\n  return (y = +y) === 1 ? nogamma : function (a, b) {\n    return b - a ? exponential(a, b, y) : constant(isNaN(a) ? b : a);\n  };\n}\nexport default function nogamma(a, b) {\n  var d = b - a;\n  return d ? linear(a, d) : constant(isNaN(a) ? b : a);\n}", "map": {"version": 3, "names": ["constant", "linear", "a", "d", "t", "exponential", "b", "y", "Math", "pow", "hue", "round", "isNaN", "gamma", "nogamma"], "sources": ["C:/console/aava-ui-web/node_modules/d3-interpolate/src/color.js"], "sourcesContent": ["import constant from \"./constant.js\";\n\nfunction linear(a, d) {\n  return function(t) {\n    return a + t * d;\n  };\n}\n\nfunction exponential(a, b, y) {\n  return a = Math.pow(a, y), b = Math.pow(b, y) - a, y = 1 / y, function(t) {\n    return Math.pow(a + t * b, y);\n  };\n}\n\nexport function hue(a, b) {\n  var d = b - a;\n  return d ? linear(a, d > 180 || d < -180 ? d - 360 * Math.round(d / 360) : d) : constant(isNaN(a) ? b : a);\n}\n\nexport function gamma(y) {\n  return (y = +y) === 1 ? nogamma : function(a, b) {\n    return b - a ? exponential(a, b, y) : constant(isNaN(a) ? b : a);\n  };\n}\n\nexport default function nogamma(a, b) {\n  var d = b - a;\n  return d ? linear(a, d) : constant(isNaN(a) ? b : a);\n}\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;AAEpC,SAASC,MAAMA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACpB,OAAO,UAASC,CAAC,EAAE;IACjB,OAAOF,CAAC,GAAGE,CAAC,GAAGD,CAAC;EAClB,CAAC;AACH;AAEA,SAASE,WAAWA,CAACH,CAAC,EAAEI,CAAC,EAAEC,CAAC,EAAE;EAC5B,OAAOL,CAAC,GAAGM,IAAI,CAACC,GAAG,CAACP,CAAC,EAAEK,CAAC,CAAC,EAAED,CAAC,GAAGE,IAAI,CAACC,GAAG,CAACH,CAAC,EAAEC,CAAC,CAAC,GAAGL,CAAC,EAAEK,CAAC,GAAG,CAAC,GAAGA,CAAC,EAAE,UAASH,CAAC,EAAE;IACxE,OAAOI,IAAI,CAACC,GAAG,CAACP,CAAC,GAAGE,CAAC,GAAGE,CAAC,EAAEC,CAAC,CAAC;EAC/B,CAAC;AACH;AAEA,OAAO,SAASG,GAAGA,CAACR,CAAC,EAAEI,CAAC,EAAE;EACxB,IAAIH,CAAC,GAAGG,CAAC,GAAGJ,CAAC;EACb,OAAOC,CAAC,GAAGF,MAAM,CAACC,CAAC,EAAEC,CAAC,GAAG,GAAG,IAAIA,CAAC,GAAG,CAAC,GAAG,GAAGA,CAAC,GAAG,GAAG,GAAGK,IAAI,CAACG,KAAK,CAACR,CAAC,GAAG,GAAG,CAAC,GAAGA,CAAC,CAAC,GAAGH,QAAQ,CAACY,KAAK,CAACV,CAAC,CAAC,GAAGI,CAAC,GAAGJ,CAAC,CAAC;AAC5G;AAEA,OAAO,SAASW,KAAKA,CAACN,CAAC,EAAE;EACvB,OAAO,CAACA,CAAC,GAAG,CAACA,CAAC,MAAM,CAAC,GAAGO,OAAO,GAAG,UAASZ,CAAC,EAAEI,CAAC,EAAE;IAC/C,OAAOA,CAAC,GAAGJ,CAAC,GAAGG,WAAW,CAACH,CAAC,EAAEI,CAAC,EAAEC,CAAC,CAAC,GAAGP,QAAQ,CAACY,KAAK,CAACV,CAAC,CAAC,GAAGI,CAAC,GAAGJ,CAAC,CAAC;EAClE,CAAC;AACH;AAEA,eAAe,SAASY,OAAOA,CAACZ,CAAC,EAAEI,CAAC,EAAE;EACpC,IAAIH,CAAC,GAAGG,CAAC,GAAGJ,CAAC;EACb,OAAOC,CAAC,GAAGF,MAAM,CAACC,CAAC,EAAEC,CAAC,CAAC,GAAGH,QAAQ,CAACY,KAAK,CAACV,CAAC,CAAC,GAAGI,CAAC,GAAGJ,CAAC,CAAC;AACtD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}