{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { map } from 'rxjs';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let ToolsService = /*#__PURE__*/(() => {\n  class ToolsService {\n    http;\n    apiServiceUrl = environment.consoleApi;\n    apiServiceUrlV2 = environment.consoleApiV2;\n    apiInstructionsApi = environment.consoleInstructionApi;\n    RECORDS_PER_PAGE = 11;\n    headers = {\n      headers: new HttpHeaders({\n        'Content-Type': 'application/json'\n      })\n    };\n    constructor(http) {\n      this.http = http;\n    }\n    /* GET API to fetch the list of user-defined tools with pagination. */\n    getUserToolsList(page = 1, records = this.RECORDS_PER_PAGE) {\n      const url = `${this.apiServiceUrlV2}/ava/force/da/userTools?page=${page}&records=${records}&isDeleted=false`;\n      return this.http.get(url, this.headers).pipe(map(response => {\n        return response;\n      }));\n    }\n    /* GET API to fetch the list of built-in tools. */\n    getBuiltInToolsList() {\n      const url = `${this.apiServiceUrl}/ava/force/tools`;\n      return this.http.get(url, this.headers).pipe(map(response => {\n        return response;\n      }));\n    }\n    getUserToolDetails(id) {\n      const url = `${this.apiServiceUrlV2}/ava/force/da/userTools?userToolId=${id}`;\n      return this.http.get(url, this.headers).pipe(map(response => {\n        // Handle new API response structure\n        if (response && response.userToolDetail) {\n          const userTool = response.userToolDetail;\n          const toolConfigs = userTool.toolConfigs || {};\n          // Transform new structure to match expected format for backward compatibility\n          const transformedResponse = {\n            tools: [{\n              toolId: userTool.id,\n              toolName: userTool.name,\n              toolDescription: userTool.description,\n              toolClassName: toolConfigs.tool_class_name || '',\n              toolClassDef: toolConfigs.tool_class_def || '',\n              toolImage: toolConfigs.tool_image || '',\n              createdBy: userTool.createdBy,\n              createTimestamp: userTool.createdAt,\n              updateTimestamp: userTool.modifiedAt,\n              isApproved: !userTool.isDeleted\n            }]\n          };\n          return transformedResponse;\n        }\n        // Return original response if it's in the old format\n        return response;\n      }));\n    }\n    /* POST API to create a new tool. */\n    addNewUserTool(payload) {\n      const url = `${this.apiServiceUrlV2}/ava/force/da/userTools`;\n      console.log('Sending POST request to:', url);\n      console.log('Payload:', JSON.stringify(payload, null, 2));\n      return this.http.post(url, payload, this.headers).pipe(map(response => {\n        console.log('Create API response:', response);\n        return response;\n      }));\n    }\n    /* PUT API to update a tool. */\n    updateUserTool(payload) {\n      const url = `${this.apiServiceUrlV2}/ava/force/da/userTools/change_request`;\n      console.log('Sending PUT request to:', url);\n      console.log('Payload:', JSON.stringify(payload, null, 2));\n      return this.http.put(url, payload, this.headers).pipe(map(response => {\n        console.log('Update API response:', response);\n        return response;\n      }));\n    }\n    /* DELETE API to Delete a tool. */\n    deleteTool(id, modifiedBy) {\n      const url = `${this.apiServiceUrlV2}/ava/force/da/userTools/change_request?userToolId=${id}&modifiedBy=${modifiedBy}`;\n      console.log('Sending DELETE request to:', url);\n      console.log('Deleting tool ID:', id, 'by user:', modifiedBy);\n      return this.http.delete(url, this.headers).pipe(map(response => {\n        console.log('Delete API response:', response);\n        return response;\n      }));\n    }\n    /* POST API to test the tool with parameters. */\n    testTool(payload) {\n      const url = `${this.apiInstructionsApi}/ava/force/tools/executor`;\n      return this.http.post(url, payload, this.headers).pipe(map(response => {\n        return response;\n      }));\n    }\n    static ɵfac = function ToolsService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ToolsService)(i0.ɵɵinject(i1.HttpClient));\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ToolsService,\n      factory: ToolsService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return ToolsService;\n})();", "map": {"version": 3, "names": ["HttpHeaders", "map", "environment", "ToolsService", "http", "apiServiceUrl", "consoleApi", "apiServiceUrlV2", "consoleApiV2", "apiInstructionsApi", "consoleInstructionApi", "RECORDS_PER_PAGE", "headers", "constructor", "getUserToolsList", "page", "records", "url", "get", "pipe", "response", "getBuiltInToolsList", "getUserToolDetails", "id", "userToolDetail", "userTool", "toolConfigs", "transformedResponse", "tools", "toolId", "toolName", "name", "toolDescription", "description", "toolClassName", "tool_class_name", "toolClassDef", "tool_class_def", "toolImage", "tool_image", "created<PERSON>y", "createTimestamp", "createdAt", "updateTimestamp", "modifiedAt", "isApproved", "isDeleted", "addNewUserTool", "payload", "console", "log", "JSON", "stringify", "post", "updateUserTool", "put", "deleteTool", "modifiedBy", "delete", "testTool", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\shared\\services\\tools.service.ts"], "sourcesContent": ["import { HttpClient, HttpHeaders } from '@angular/common/http';\r\nimport { Injectable, signal } from '@angular/core';\r\nimport { map } from 'rxjs';\r\nimport { environment } from '../../../environments/environment';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ToolsService {\r\n  private apiServiceUrl = environment.consoleApi;\r\n  private apiServiceUrlV2 = environment.consoleApiV2;\r\n  private apiInstructionsApi = environment.consoleInstructionApi\r\n  private readonly RECORDS_PER_PAGE = 11;\r\n  private headers = { headers: new HttpHeaders({\r\n    'Content-Type': 'application/json',\r\n  })};\r\n\r\n  constructor(private http: HttpClient) { }\r\n\r\n  /* GET API to fetch the list of user-defined tools with pagination. */\r\n  public getUserToolsList(page: number = 1, records: number = this.RECORDS_PER_PAGE) {\r\n    const url = `${this.apiServiceUrlV2}/ava/force/da/userTools?page=${page}&records=${records}&isDeleted=false`;\r\n\r\n    return this.http.get(url, this.headers).pipe(\r\n      map((response: any) => {\r\n          return response;\r\n      })\r\n    );\r\n  }\r\n\r\n  /* GET API to fetch the list of built-in tools. */\r\n  public getBuiltInToolsList() {\r\n    const url = `${this.apiServiceUrl}/ava/force/tools`;\r\n\r\n    return this.http.get(url, this.headers).pipe(\r\n      map((response: any) => {\r\n          return response;\r\n      })\r\n    );\r\n  }\r\n\r\n  public getUserToolDetails(id: number) {\r\n    const url = `${this.apiServiceUrlV2}/ava/force/da/userTools?userToolId=${id}`;\r\n\r\n    return this.http.get(url, this.headers).pipe(\r\n      map((response: any) => {\r\n        // Handle new API response structure\r\n        if (response && response.userToolDetail) {\r\n          const userTool = response.userToolDetail;\r\n          const toolConfigs = userTool.toolConfigs || {};\r\n\r\n          // Transform new structure to match expected format for backward compatibility\r\n          const transformedResponse = {\r\n            tools: [{\r\n              toolId: userTool.id,\r\n              toolName: userTool.name,\r\n              toolDescription: userTool.description,\r\n              toolClassName: toolConfigs.tool_class_name || '',\r\n              toolClassDef: toolConfigs.tool_class_def || '',\r\n              toolImage: toolConfigs.tool_image || '',\r\n              createdBy: userTool.createdBy,\r\n              createTimestamp: userTool.createdAt,\r\n              updateTimestamp: userTool.modifiedAt,\r\n              isApproved: !userTool.isDeleted\r\n            }]\r\n          };\r\n\r\n          return transformedResponse;\r\n        }\r\n\r\n        // Return original response if it's in the old format\r\n        return response;\r\n      })\r\n    );\r\n  }\r\n\r\n  /* POST API to create a new tool. */\r\n  public addNewUserTool(payload: any) {\r\n    const url = `${this.apiServiceUrlV2}/ava/force/da/userTools`;\r\n\r\n    console.log('Sending POST request to:', url);\r\n    console.log('Payload:', JSON.stringify(payload, null, 2));\r\n\r\n    return this.http.post(url, payload, this.headers).pipe(\r\n      map((response: any) => {\r\n        console.log('Create API response:', response);\r\n        return response;\r\n      })\r\n    );\r\n  }\r\n\r\n  /* PUT API to update a tool. */\r\n  public updateUserTool(payload: any) {\r\n    const url = `${this.apiServiceUrlV2}/ava/force/da/userTools/change_request`;\r\n\r\n    console.log('Sending PUT request to:', url);\r\n    console.log('Payload:', JSON.stringify(payload, null, 2));\r\n\r\n    return this.http.put(url, payload, this.headers).pipe(\r\n      map((response: any) => {\r\n        console.log('Update API response:', response);\r\n        return response;\r\n      })\r\n    );\r\n  }\r\n\r\n  /* DELETE API to Delete a tool. */\r\n  public deleteTool(id: number, modifiedBy: string) {\r\n    const url = `${this.apiServiceUrlV2}/ava/force/da/userTools/change_request?userToolId=${id}&modifiedBy=${modifiedBy}`;\r\n\r\n    console.log('Sending DELETE request to:', url);\r\n    console.log('Deleting tool ID:', id, 'by user:', modifiedBy);\r\n\r\n    return this.http.delete(url, this.headers).pipe(\r\n      map((response: any) => {\r\n        console.log('Delete API response:', response);\r\n        return response;\r\n      })\r\n    );\r\n  }\r\n\r\n   /* POST API to test the tool with parameters. */\r\n   public testTool(payload: any) {\r\n    const url = `${this.apiInstructionsApi}/ava/force/tools/executor`;\r\n\r\n    return this.http.post(url, payload, this.headers).pipe(\r\n      map((response: any) => {\r\n        return response;\r\n      })\r\n    );\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,WAAW,QAAQ,sBAAsB;AAE9D,SAASC,GAAG,QAAQ,MAAM;AAC1B,SAASC,WAAW,QAAQ,mCAAmC;;;AAK/D,WAAaC,YAAY;EAAnB,MAAOA,YAAY;IASHC,IAAA;IARZC,aAAa,GAAGH,WAAW,CAACI,UAAU;IACtCC,eAAe,GAAGL,WAAW,CAACM,YAAY;IAC1CC,kBAAkB,GAAGP,WAAW,CAACQ,qBAAqB;IAC7CC,gBAAgB,GAAG,EAAE;IAC9BC,OAAO,GAAG;MAAEA,OAAO,EAAE,IAAIZ,WAAW,CAAC;QAC3C,cAAc,EAAE;OACjB;IAAC,CAAC;IAEHa,YAAoBT,IAAgB;MAAhB,KAAAA,IAAI,GAAJA,IAAI;IAAgB;IAExC;IACOU,gBAAgBA,CAACC,IAAA,GAAe,CAAC,EAAEC,OAAA,GAAkB,IAAI,CAACL,gBAAgB;MAC/E,MAAMM,GAAG,GAAG,GAAG,IAAI,CAACV,eAAe,gCAAgCQ,IAAI,YAAYC,OAAO,kBAAkB;MAE5G,OAAO,IAAI,CAACZ,IAAI,CAACc,GAAG,CAACD,GAAG,EAAE,IAAI,CAACL,OAAO,CAAC,CAACO,IAAI,CAC1ClB,GAAG,CAAEmB,QAAa,IAAI;QAClB,OAAOA,QAAQ;MACnB,CAAC,CAAC,CACH;IACH;IAEA;IACOC,mBAAmBA,CAAA;MACxB,MAAMJ,GAAG,GAAG,GAAG,IAAI,CAACZ,aAAa,kBAAkB;MAEnD,OAAO,IAAI,CAACD,IAAI,CAACc,GAAG,CAACD,GAAG,EAAE,IAAI,CAACL,OAAO,CAAC,CAACO,IAAI,CAC1ClB,GAAG,CAAEmB,QAAa,IAAI;QAClB,OAAOA,QAAQ;MACnB,CAAC,CAAC,CACH;IACH;IAEOE,kBAAkBA,CAACC,EAAU;MAClC,MAAMN,GAAG,GAAG,GAAG,IAAI,CAACV,eAAe,sCAAsCgB,EAAE,EAAE;MAE7E,OAAO,IAAI,CAACnB,IAAI,CAACc,GAAG,CAACD,GAAG,EAAE,IAAI,CAACL,OAAO,CAAC,CAACO,IAAI,CAC1ClB,GAAG,CAAEmB,QAAa,IAAI;QACpB;QACA,IAAIA,QAAQ,IAAIA,QAAQ,CAACI,cAAc,EAAE;UACvC,MAAMC,QAAQ,GAAGL,QAAQ,CAACI,cAAc;UACxC,MAAME,WAAW,GAAGD,QAAQ,CAACC,WAAW,IAAI,EAAE;UAE9C;UACA,MAAMC,mBAAmB,GAAG;YAC1BC,KAAK,EAAE,CAAC;cACNC,MAAM,EAAEJ,QAAQ,CAACF,EAAE;cACnBO,QAAQ,EAAEL,QAAQ,CAACM,IAAI;cACvBC,eAAe,EAAEP,QAAQ,CAACQ,WAAW;cACrCC,aAAa,EAAER,WAAW,CAACS,eAAe,IAAI,EAAE;cAChDC,YAAY,EAAEV,WAAW,CAACW,cAAc,IAAI,EAAE;cAC9CC,SAAS,EAAEZ,WAAW,CAACa,UAAU,IAAI,EAAE;cACvCC,SAAS,EAAEf,QAAQ,CAACe,SAAS;cAC7BC,eAAe,EAAEhB,QAAQ,CAACiB,SAAS;cACnCC,eAAe,EAAElB,QAAQ,CAACmB,UAAU;cACpCC,UAAU,EAAE,CAACpB,QAAQ,CAACqB;aACvB;WACF;UAED,OAAOnB,mBAAmB;QAC5B;QAEA;QACA,OAAOP,QAAQ;MACjB,CAAC,CAAC,CACH;IACH;IAEA;IACO2B,cAAcA,CAACC,OAAY;MAChC,MAAM/B,GAAG,GAAG,GAAG,IAAI,CAACV,eAAe,yBAAyB;MAE5D0C,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEjC,GAAG,CAAC;MAC5CgC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEC,IAAI,CAACC,SAAS,CAACJ,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;MAEzD,OAAO,IAAI,CAAC5C,IAAI,CAACiD,IAAI,CAACpC,GAAG,EAAE+B,OAAO,EAAE,IAAI,CAACpC,OAAO,CAAC,CAACO,IAAI,CACpDlB,GAAG,CAAEmB,QAAa,IAAI;QACpB6B,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE9B,QAAQ,CAAC;QAC7C,OAAOA,QAAQ;MACjB,CAAC,CAAC,CACH;IACH;IAEA;IACOkC,cAAcA,CAACN,OAAY;MAChC,MAAM/B,GAAG,GAAG,GAAG,IAAI,CAACV,eAAe,wCAAwC;MAE3E0C,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEjC,GAAG,CAAC;MAC3CgC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEC,IAAI,CAACC,SAAS,CAACJ,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;MAEzD,OAAO,IAAI,CAAC5C,IAAI,CAACmD,GAAG,CAACtC,GAAG,EAAE+B,OAAO,EAAE,IAAI,CAACpC,OAAO,CAAC,CAACO,IAAI,CACnDlB,GAAG,CAAEmB,QAAa,IAAI;QACpB6B,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE9B,QAAQ,CAAC;QAC7C,OAAOA,QAAQ;MACjB,CAAC,CAAC,CACH;IACH;IAEA;IACOoC,UAAUA,CAACjC,EAAU,EAAEkC,UAAkB;MAC9C,MAAMxC,GAAG,GAAG,GAAG,IAAI,CAACV,eAAe,qDAAqDgB,EAAE,eAAekC,UAAU,EAAE;MAErHR,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEjC,GAAG,CAAC;MAC9CgC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE3B,EAAE,EAAE,UAAU,EAAEkC,UAAU,CAAC;MAE5D,OAAO,IAAI,CAACrD,IAAI,CAACsD,MAAM,CAACzC,GAAG,EAAE,IAAI,CAACL,OAAO,CAAC,CAACO,IAAI,CAC7ClB,GAAG,CAAEmB,QAAa,IAAI;QACpB6B,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE9B,QAAQ,CAAC;QAC7C,OAAOA,QAAQ;MACjB,CAAC,CAAC,CACH;IACH;IAEC;IACOuC,QAAQA,CAACX,OAAY;MAC3B,MAAM/B,GAAG,GAAG,GAAG,IAAI,CAACR,kBAAkB,2BAA2B;MAEjE,OAAO,IAAI,CAACL,IAAI,CAACiD,IAAI,CAACpC,GAAG,EAAE+B,OAAO,EAAE,IAAI,CAACpC,OAAO,CAAC,CAACO,IAAI,CACpDlB,GAAG,CAAEmB,QAAa,IAAI;QACpB,OAAOA,QAAQ;MACjB,CAAC,CAAC,CACH;IACH;;uCA1HWjB,YAAY,EAAAyD,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;;aAAZ5D,YAAY;MAAA6D,OAAA,EAAZ7D,YAAY,CAAA8D,IAAA;MAAAC,UAAA,EAFX;IAAM;;SAEP/D,YAAY;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}