{"ast": null, "code": "import { Subject, AnonymousSubject } from '../../Subject';\nimport { Subscriber } from '../../Subscriber';\nimport { Observable } from '../../Observable';\nimport { Subscription } from '../../Subscription';\nimport { ReplaySubject } from '../../ReplaySubject';\nconst DEFAULT_WEBSOCKET_CONFIG = {\n  url: '',\n  deserializer: e => JSON.parse(e.data),\n  serializer: value => JSON.stringify(value)\n};\nconst WEBSOCKETSUBJECT_INVALID_ERROR_OBJECT = 'WebSocketSubject.error must be called with an object with an error code, and an optional reason: { code: number, reason: string }';\nexport class WebSocketSubject extends AnonymousSubject {\n  constructor(urlConfigOrSource, destination) {\n    super();\n    this._socket = null;\n    if (urlConfigOrSource instanceof Observable) {\n      this.destination = destination;\n      this.source = urlConfigOrSource;\n    } else {\n      const config = this._config = Object.assign({}, DEFAULT_WEBSOCKET_CONFIG);\n      this._output = new Subject();\n      if (typeof urlConfigOrSource === 'string') {\n        config.url = urlConfigOrSource;\n      } else {\n        for (const key in urlConfigOrSource) {\n          if (urlConfigOrSource.hasOwnProperty(key)) {\n            config[key] = urlConfigOrSource[key];\n          }\n        }\n      }\n      if (!config.WebSocketCtor && WebSocket) {\n        config.WebSocketCtor = WebSocket;\n      } else if (!config.WebSocketCtor) {\n        throw new Error('no WebSocket constructor can be found');\n      }\n      this.destination = new ReplaySubject();\n    }\n  }\n  lift(operator) {\n    const sock = new WebSocketSubject(this._config, this.destination);\n    sock.operator = operator;\n    sock.source = this;\n    return sock;\n  }\n  _resetState() {\n    this._socket = null;\n    if (!this.source) {\n      this.destination = new ReplaySubject();\n    }\n    this._output = new Subject();\n  }\n  multiplex(subMsg, unsubMsg, messageFilter) {\n    const self = this;\n    return new Observable(observer => {\n      try {\n        self.next(subMsg());\n      } catch (err) {\n        observer.error(err);\n      }\n      const subscription = self.subscribe({\n        next: x => {\n          try {\n            if (messageFilter(x)) {\n              observer.next(x);\n            }\n          } catch (err) {\n            observer.error(err);\n          }\n        },\n        error: err => observer.error(err),\n        complete: () => observer.complete()\n      });\n      return () => {\n        try {\n          self.next(unsubMsg());\n        } catch (err) {\n          observer.error(err);\n        }\n        subscription.unsubscribe();\n      };\n    });\n  }\n  _connectSocket() {\n    const {\n      WebSocketCtor,\n      protocol,\n      url,\n      binaryType\n    } = this._config;\n    const observer = this._output;\n    let socket = null;\n    try {\n      socket = protocol ? new WebSocketCtor(url, protocol) : new WebSocketCtor(url);\n      this._socket = socket;\n      if (binaryType) {\n        this._socket.binaryType = binaryType;\n      }\n    } catch (e) {\n      observer.error(e);\n      return;\n    }\n    const subscription = new Subscription(() => {\n      this._socket = null;\n      if (socket && socket.readyState === 1) {\n        socket.close();\n      }\n    });\n    socket.onopen = evt => {\n      const {\n        _socket\n      } = this;\n      if (!_socket) {\n        socket.close();\n        this._resetState();\n        return;\n      }\n      const {\n        openObserver\n      } = this._config;\n      if (openObserver) {\n        openObserver.next(evt);\n      }\n      const queue = this.destination;\n      this.destination = Subscriber.create(x => {\n        if (socket.readyState === 1) {\n          try {\n            const {\n              serializer\n            } = this._config;\n            socket.send(serializer(x));\n          } catch (e) {\n            this.destination.error(e);\n          }\n        }\n      }, err => {\n        const {\n          closingObserver\n        } = this._config;\n        if (closingObserver) {\n          closingObserver.next(undefined);\n        }\n        if (err && err.code) {\n          socket.close(err.code, err.reason);\n        } else {\n          observer.error(new TypeError(WEBSOCKETSUBJECT_INVALID_ERROR_OBJECT));\n        }\n        this._resetState();\n      }, () => {\n        const {\n          closingObserver\n        } = this._config;\n        if (closingObserver) {\n          closingObserver.next(undefined);\n        }\n        socket.close();\n        this._resetState();\n      });\n      if (queue && queue instanceof ReplaySubject) {\n        subscription.add(queue.subscribe(this.destination));\n      }\n    };\n    socket.onerror = e => {\n      this._resetState();\n      observer.error(e);\n    };\n    socket.onclose = e => {\n      if (socket === this._socket) {\n        this._resetState();\n      }\n      const {\n        closeObserver\n      } = this._config;\n      if (closeObserver) {\n        closeObserver.next(e);\n      }\n      if (e.wasClean) {\n        observer.complete();\n      } else {\n        observer.error(e);\n      }\n    };\n    socket.onmessage = e => {\n      try {\n        const {\n          deserializer\n        } = this._config;\n        observer.next(deserializer(e));\n      } catch (err) {\n        observer.error(err);\n      }\n    };\n  }\n  _subscribe(subscriber) {\n    const {\n      source\n    } = this;\n    if (source) {\n      return source.subscribe(subscriber);\n    }\n    if (!this._socket) {\n      this._connectSocket();\n    }\n    this._output.subscribe(subscriber);\n    subscriber.add(() => {\n      const {\n        _socket\n      } = this;\n      if (this._output.observers.length === 0) {\n        if (_socket && (_socket.readyState === 1 || _socket.readyState === 0)) {\n          _socket.close();\n        }\n        this._resetState();\n      }\n    });\n    return subscriber;\n  }\n  unsubscribe() {\n    const {\n      _socket\n    } = this;\n    if (_socket && (_socket.readyState === 1 || _socket.readyState === 0)) {\n      _socket.close();\n    }\n    this._resetState();\n    super.unsubscribe();\n  }\n}", "map": {"version": 3, "names": ["Subject", "AnonymousSubject", "Subscriber", "Observable", "Subscription", "ReplaySubject", "DEFAULT_WEBSOCKET_CONFIG", "url", "deserializer", "e", "JSON", "parse", "data", "serializer", "value", "stringify", "WEBSOCKETSUBJECT_INVALID_ERROR_OBJECT", "WebSocketSubject", "constructor", "urlConfigOrSource", "destination", "_socket", "source", "config", "_config", "Object", "assign", "_output", "key", "hasOwnProperty", "WebSocketCtor", "WebSocket", "Error", "lift", "operator", "sock", "_resetState", "multiplex", "subMsg", "unsubMsg", "messageFilter", "self", "observer", "next", "err", "error", "subscription", "subscribe", "x", "complete", "unsubscribe", "_connectSocket", "protocol", "binaryType", "socket", "readyState", "close", "onopen", "evt", "openObserver", "queue", "create", "send", "closingObserver", "undefined", "code", "reason", "TypeError", "add", "onerror", "onclose", "closeObserver", "<PERSON><PERSON><PERSON>", "onmessage", "_subscribe", "subscriber", "observers", "length"], "sources": ["C:/console/aava-ui-web/node_modules/rxjs/dist/esm/internal/observable/dom/WebSocketSubject.js"], "sourcesContent": ["import { Subject, AnonymousSubject } from '../../Subject';\nimport { Subscriber } from '../../Subscriber';\nimport { Observable } from '../../Observable';\nimport { Subscription } from '../../Subscription';\nimport { ReplaySubject } from '../../ReplaySubject';\nconst DEFAULT_WEBSOCKET_CONFIG = {\n    url: '',\n    deserializer: (e) => JSON.parse(e.data),\n    serializer: (value) => JSON.stringify(value),\n};\nconst WEBSOCKETSUBJECT_INVALID_ERROR_OBJECT = 'WebSocketSubject.error must be called with an object with an error code, and an optional reason: { code: number, reason: string }';\nexport class WebSocketSubject extends AnonymousSubject {\n    constructor(urlConfigOrSource, destination) {\n        super();\n        this._socket = null;\n        if (urlConfigOrSource instanceof Observable) {\n            this.destination = destination;\n            this.source = urlConfigOrSource;\n        }\n        else {\n            const config = (this._config = Object.assign({}, DEFAULT_WEBSOCKET_CONFIG));\n            this._output = new Subject();\n            if (typeof urlConfigOrSource === 'string') {\n                config.url = urlConfigOrSource;\n            }\n            else {\n                for (const key in urlConfigOrSource) {\n                    if (urlConfigOrSource.hasOwnProperty(key)) {\n                        config[key] = urlConfigOrSource[key];\n                    }\n                }\n            }\n            if (!config.WebSocketCtor && WebSocket) {\n                config.WebSocketCtor = WebSocket;\n            }\n            else if (!config.WebSocketCtor) {\n                throw new Error('no WebSocket constructor can be found');\n            }\n            this.destination = new ReplaySubject();\n        }\n    }\n    lift(operator) {\n        const sock = new WebSocketSubject(this._config, this.destination);\n        sock.operator = operator;\n        sock.source = this;\n        return sock;\n    }\n    _resetState() {\n        this._socket = null;\n        if (!this.source) {\n            this.destination = new ReplaySubject();\n        }\n        this._output = new Subject();\n    }\n    multiplex(subMsg, unsubMsg, messageFilter) {\n        const self = this;\n        return new Observable((observer) => {\n            try {\n                self.next(subMsg());\n            }\n            catch (err) {\n                observer.error(err);\n            }\n            const subscription = self.subscribe({\n                next: (x) => {\n                    try {\n                        if (messageFilter(x)) {\n                            observer.next(x);\n                        }\n                    }\n                    catch (err) {\n                        observer.error(err);\n                    }\n                },\n                error: (err) => observer.error(err),\n                complete: () => observer.complete(),\n            });\n            return () => {\n                try {\n                    self.next(unsubMsg());\n                }\n                catch (err) {\n                    observer.error(err);\n                }\n                subscription.unsubscribe();\n            };\n        });\n    }\n    _connectSocket() {\n        const { WebSocketCtor, protocol, url, binaryType } = this._config;\n        const observer = this._output;\n        let socket = null;\n        try {\n            socket = protocol ? new WebSocketCtor(url, protocol) : new WebSocketCtor(url);\n            this._socket = socket;\n            if (binaryType) {\n                this._socket.binaryType = binaryType;\n            }\n        }\n        catch (e) {\n            observer.error(e);\n            return;\n        }\n        const subscription = new Subscription(() => {\n            this._socket = null;\n            if (socket && socket.readyState === 1) {\n                socket.close();\n            }\n        });\n        socket.onopen = (evt) => {\n            const { _socket } = this;\n            if (!_socket) {\n                socket.close();\n                this._resetState();\n                return;\n            }\n            const { openObserver } = this._config;\n            if (openObserver) {\n                openObserver.next(evt);\n            }\n            const queue = this.destination;\n            this.destination = Subscriber.create((x) => {\n                if (socket.readyState === 1) {\n                    try {\n                        const { serializer } = this._config;\n                        socket.send(serializer(x));\n                    }\n                    catch (e) {\n                        this.destination.error(e);\n                    }\n                }\n            }, (err) => {\n                const { closingObserver } = this._config;\n                if (closingObserver) {\n                    closingObserver.next(undefined);\n                }\n                if (err && err.code) {\n                    socket.close(err.code, err.reason);\n                }\n                else {\n                    observer.error(new TypeError(WEBSOCKETSUBJECT_INVALID_ERROR_OBJECT));\n                }\n                this._resetState();\n            }, () => {\n                const { closingObserver } = this._config;\n                if (closingObserver) {\n                    closingObserver.next(undefined);\n                }\n                socket.close();\n                this._resetState();\n            });\n            if (queue && queue instanceof ReplaySubject) {\n                subscription.add(queue.subscribe(this.destination));\n            }\n        };\n        socket.onerror = (e) => {\n            this._resetState();\n            observer.error(e);\n        };\n        socket.onclose = (e) => {\n            if (socket === this._socket) {\n                this._resetState();\n            }\n            const { closeObserver } = this._config;\n            if (closeObserver) {\n                closeObserver.next(e);\n            }\n            if (e.wasClean) {\n                observer.complete();\n            }\n            else {\n                observer.error(e);\n            }\n        };\n        socket.onmessage = (e) => {\n            try {\n                const { deserializer } = this._config;\n                observer.next(deserializer(e));\n            }\n            catch (err) {\n                observer.error(err);\n            }\n        };\n    }\n    _subscribe(subscriber) {\n        const { source } = this;\n        if (source) {\n            return source.subscribe(subscriber);\n        }\n        if (!this._socket) {\n            this._connectSocket();\n        }\n        this._output.subscribe(subscriber);\n        subscriber.add(() => {\n            const { _socket } = this;\n            if (this._output.observers.length === 0) {\n                if (_socket && (_socket.readyState === 1 || _socket.readyState === 0)) {\n                    _socket.close();\n                }\n                this._resetState();\n            }\n        });\n        return subscriber;\n    }\n    unsubscribe() {\n        const { _socket } = this;\n        if (_socket && (_socket.readyState === 1 || _socket.readyState === 0)) {\n            _socket.close();\n        }\n        this._resetState();\n        super.unsubscribe();\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,gBAAgB,QAAQ,eAAe;AACzD,SAASC,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,aAAa,QAAQ,qBAAqB;AACnD,MAAMC,wBAAwB,GAAG;EAC7BC,GAAG,EAAE,EAAE;EACPC,YAAY,EAAGC,CAAC,IAAKC,IAAI,CAACC,KAAK,CAACF,CAAC,CAACG,IAAI,CAAC;EACvCC,UAAU,EAAGC,KAAK,IAAKJ,IAAI,CAACK,SAAS,CAACD,KAAK;AAC/C,CAAC;AACD,MAAME,qCAAqC,GAAG,mIAAmI;AACjL,OAAO,MAAMC,gBAAgB,SAAShB,gBAAgB,CAAC;EACnDiB,WAAWA,CAACC,iBAAiB,EAAEC,WAAW,EAAE;IACxC,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAIF,iBAAiB,YAAYhB,UAAU,EAAE;MACzC,IAAI,CAACiB,WAAW,GAAGA,WAAW;MAC9B,IAAI,CAACE,MAAM,GAAGH,iBAAiB;IACnC,CAAC,MACI;MACD,MAAMI,MAAM,GAAI,IAAI,CAACC,OAAO,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEpB,wBAAwB,CAAE;MAC3E,IAAI,CAACqB,OAAO,GAAG,IAAI3B,OAAO,CAAC,CAAC;MAC5B,IAAI,OAAOmB,iBAAiB,KAAK,QAAQ,EAAE;QACvCI,MAAM,CAAChB,GAAG,GAAGY,iBAAiB;MAClC,CAAC,MACI;QACD,KAAK,MAAMS,GAAG,IAAIT,iBAAiB,EAAE;UACjC,IAAIA,iBAAiB,CAACU,cAAc,CAACD,GAAG,CAAC,EAAE;YACvCL,MAAM,CAACK,GAAG,CAAC,GAAGT,iBAAiB,CAACS,GAAG,CAAC;UACxC;QACJ;MACJ;MACA,IAAI,CAACL,MAAM,CAACO,aAAa,IAAIC,SAAS,EAAE;QACpCR,MAAM,CAACO,aAAa,GAAGC,SAAS;MACpC,CAAC,MACI,IAAI,CAACR,MAAM,CAACO,aAAa,EAAE;QAC5B,MAAM,IAAIE,KAAK,CAAC,uCAAuC,CAAC;MAC5D;MACA,IAAI,CAACZ,WAAW,GAAG,IAAIf,aAAa,CAAC,CAAC;IAC1C;EACJ;EACA4B,IAAIA,CAACC,QAAQ,EAAE;IACX,MAAMC,IAAI,GAAG,IAAIlB,gBAAgB,CAAC,IAAI,CAACO,OAAO,EAAE,IAAI,CAACJ,WAAW,CAAC;IACjEe,IAAI,CAACD,QAAQ,GAAGA,QAAQ;IACxBC,IAAI,CAACb,MAAM,GAAG,IAAI;IAClB,OAAOa,IAAI;EACf;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACf,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC,IAAI,CAACC,MAAM,EAAE;MACd,IAAI,CAACF,WAAW,GAAG,IAAIf,aAAa,CAAC,CAAC;IAC1C;IACA,IAAI,CAACsB,OAAO,GAAG,IAAI3B,OAAO,CAAC,CAAC;EAChC;EACAqC,SAASA,CAACC,MAAM,EAAEC,QAAQ,EAAEC,aAAa,EAAE;IACvC,MAAMC,IAAI,GAAG,IAAI;IACjB,OAAO,IAAItC,UAAU,CAAEuC,QAAQ,IAAK;MAChC,IAAI;QACAD,IAAI,CAACE,IAAI,CAACL,MAAM,CAAC,CAAC,CAAC;MACvB,CAAC,CACD,OAAOM,GAAG,EAAE;QACRF,QAAQ,CAACG,KAAK,CAACD,GAAG,CAAC;MACvB;MACA,MAAME,YAAY,GAAGL,IAAI,CAACM,SAAS,CAAC;QAChCJ,IAAI,EAAGK,CAAC,IAAK;UACT,IAAI;YACA,IAAIR,aAAa,CAACQ,CAAC,CAAC,EAAE;cAClBN,QAAQ,CAACC,IAAI,CAACK,CAAC,CAAC;YACpB;UACJ,CAAC,CACD,OAAOJ,GAAG,EAAE;YACRF,QAAQ,CAACG,KAAK,CAACD,GAAG,CAAC;UACvB;QACJ,CAAC;QACDC,KAAK,EAAGD,GAAG,IAAKF,QAAQ,CAACG,KAAK,CAACD,GAAG,CAAC;QACnCK,QAAQ,EAAEA,CAAA,KAAMP,QAAQ,CAACO,QAAQ,CAAC;MACtC,CAAC,CAAC;MACF,OAAO,MAAM;QACT,IAAI;UACAR,IAAI,CAACE,IAAI,CAACJ,QAAQ,CAAC,CAAC,CAAC;QACzB,CAAC,CACD,OAAOK,GAAG,EAAE;UACRF,QAAQ,CAACG,KAAK,CAACD,GAAG,CAAC;QACvB;QACAE,YAAY,CAACI,WAAW,CAAC,CAAC;MAC9B,CAAC;IACL,CAAC,CAAC;EACN;EACAC,cAAcA,CAAA,EAAG;IACb,MAAM;MAAErB,aAAa;MAAEsB,QAAQ;MAAE7C,GAAG;MAAE8C;IAAW,CAAC,GAAG,IAAI,CAAC7B,OAAO;IACjE,MAAMkB,QAAQ,GAAG,IAAI,CAACf,OAAO;IAC7B,IAAI2B,MAAM,GAAG,IAAI;IACjB,IAAI;MACAA,MAAM,GAAGF,QAAQ,GAAG,IAAItB,aAAa,CAACvB,GAAG,EAAE6C,QAAQ,CAAC,GAAG,IAAItB,aAAa,CAACvB,GAAG,CAAC;MAC7E,IAAI,CAACc,OAAO,GAAGiC,MAAM;MACrB,IAAID,UAAU,EAAE;QACZ,IAAI,CAAChC,OAAO,CAACgC,UAAU,GAAGA,UAAU;MACxC;IACJ,CAAC,CACD,OAAO5C,CAAC,EAAE;MACNiC,QAAQ,CAACG,KAAK,CAACpC,CAAC,CAAC;MACjB;IACJ;IACA,MAAMqC,YAAY,GAAG,IAAI1C,YAAY,CAAC,MAAM;MACxC,IAAI,CAACiB,OAAO,GAAG,IAAI;MACnB,IAAIiC,MAAM,IAAIA,MAAM,CAACC,UAAU,KAAK,CAAC,EAAE;QACnCD,MAAM,CAACE,KAAK,CAAC,CAAC;MAClB;IACJ,CAAC,CAAC;IACFF,MAAM,CAACG,MAAM,GAAIC,GAAG,IAAK;MACrB,MAAM;QAAErC;MAAQ,CAAC,GAAG,IAAI;MACxB,IAAI,CAACA,OAAO,EAAE;QACViC,MAAM,CAACE,KAAK,CAAC,CAAC;QACd,IAAI,CAACpB,WAAW,CAAC,CAAC;QAClB;MACJ;MACA,MAAM;QAAEuB;MAAa,CAAC,GAAG,IAAI,CAACnC,OAAO;MACrC,IAAImC,YAAY,EAAE;QACdA,YAAY,CAAChB,IAAI,CAACe,GAAG,CAAC;MAC1B;MACA,MAAME,KAAK,GAAG,IAAI,CAACxC,WAAW;MAC9B,IAAI,CAACA,WAAW,GAAGlB,UAAU,CAAC2D,MAAM,CAAEb,CAAC,IAAK;QACxC,IAAIM,MAAM,CAACC,UAAU,KAAK,CAAC,EAAE;UACzB,IAAI;YACA,MAAM;cAAE1C;YAAW,CAAC,GAAG,IAAI,CAACW,OAAO;YACnC8B,MAAM,CAACQ,IAAI,CAACjD,UAAU,CAACmC,CAAC,CAAC,CAAC;UAC9B,CAAC,CACD,OAAOvC,CAAC,EAAE;YACN,IAAI,CAACW,WAAW,CAACyB,KAAK,CAACpC,CAAC,CAAC;UAC7B;QACJ;MACJ,CAAC,EAAGmC,GAAG,IAAK;QACR,MAAM;UAAEmB;QAAgB,CAAC,GAAG,IAAI,CAACvC,OAAO;QACxC,IAAIuC,eAAe,EAAE;UACjBA,eAAe,CAACpB,IAAI,CAACqB,SAAS,CAAC;QACnC;QACA,IAAIpB,GAAG,IAAIA,GAAG,CAACqB,IAAI,EAAE;UACjBX,MAAM,CAACE,KAAK,CAACZ,GAAG,CAACqB,IAAI,EAAErB,GAAG,CAACsB,MAAM,CAAC;QACtC,CAAC,MACI;UACDxB,QAAQ,CAACG,KAAK,CAAC,IAAIsB,SAAS,CAACnD,qCAAqC,CAAC,CAAC;QACxE;QACA,IAAI,CAACoB,WAAW,CAAC,CAAC;MACtB,CAAC,EAAE,MAAM;QACL,MAAM;UAAE2B;QAAgB,CAAC,GAAG,IAAI,CAACvC,OAAO;QACxC,IAAIuC,eAAe,EAAE;UACjBA,eAAe,CAACpB,IAAI,CAACqB,SAAS,CAAC;QACnC;QACAV,MAAM,CAACE,KAAK,CAAC,CAAC;QACd,IAAI,CAACpB,WAAW,CAAC,CAAC;MACtB,CAAC,CAAC;MACF,IAAIwB,KAAK,IAAIA,KAAK,YAAYvD,aAAa,EAAE;QACzCyC,YAAY,CAACsB,GAAG,CAACR,KAAK,CAACb,SAAS,CAAC,IAAI,CAAC3B,WAAW,CAAC,CAAC;MACvD;IACJ,CAAC;IACDkC,MAAM,CAACe,OAAO,GAAI5D,CAAC,IAAK;MACpB,IAAI,CAAC2B,WAAW,CAAC,CAAC;MAClBM,QAAQ,CAACG,KAAK,CAACpC,CAAC,CAAC;IACrB,CAAC;IACD6C,MAAM,CAACgB,OAAO,GAAI7D,CAAC,IAAK;MACpB,IAAI6C,MAAM,KAAK,IAAI,CAACjC,OAAO,EAAE;QACzB,IAAI,CAACe,WAAW,CAAC,CAAC;MACtB;MACA,MAAM;QAAEmC;MAAc,CAAC,GAAG,IAAI,CAAC/C,OAAO;MACtC,IAAI+C,aAAa,EAAE;QACfA,aAAa,CAAC5B,IAAI,CAAClC,CAAC,CAAC;MACzB;MACA,IAAIA,CAAC,CAAC+D,QAAQ,EAAE;QACZ9B,QAAQ,CAACO,QAAQ,CAAC,CAAC;MACvB,CAAC,MACI;QACDP,QAAQ,CAACG,KAAK,CAACpC,CAAC,CAAC;MACrB;IACJ,CAAC;IACD6C,MAAM,CAACmB,SAAS,GAAIhE,CAAC,IAAK;MACtB,IAAI;QACA,MAAM;UAAED;QAAa,CAAC,GAAG,IAAI,CAACgB,OAAO;QACrCkB,QAAQ,CAACC,IAAI,CAACnC,YAAY,CAACC,CAAC,CAAC,CAAC;MAClC,CAAC,CACD,OAAOmC,GAAG,EAAE;QACRF,QAAQ,CAACG,KAAK,CAACD,GAAG,CAAC;MACvB;IACJ,CAAC;EACL;EACA8B,UAAUA,CAACC,UAAU,EAAE;IACnB,MAAM;MAAErD;IAAO,CAAC,GAAG,IAAI;IACvB,IAAIA,MAAM,EAAE;MACR,OAAOA,MAAM,CAACyB,SAAS,CAAC4B,UAAU,CAAC;IACvC;IACA,IAAI,CAAC,IAAI,CAACtD,OAAO,EAAE;MACf,IAAI,CAAC8B,cAAc,CAAC,CAAC;IACzB;IACA,IAAI,CAACxB,OAAO,CAACoB,SAAS,CAAC4B,UAAU,CAAC;IAClCA,UAAU,CAACP,GAAG,CAAC,MAAM;MACjB,MAAM;QAAE/C;MAAQ,CAAC,GAAG,IAAI;MACxB,IAAI,IAAI,CAACM,OAAO,CAACiD,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE;QACrC,IAAIxD,OAAO,KAAKA,OAAO,CAACkC,UAAU,KAAK,CAAC,IAAIlC,OAAO,CAACkC,UAAU,KAAK,CAAC,CAAC,EAAE;UACnElC,OAAO,CAACmC,KAAK,CAAC,CAAC;QACnB;QACA,IAAI,CAACpB,WAAW,CAAC,CAAC;MACtB;IACJ,CAAC,CAAC;IACF,OAAOuC,UAAU;EACrB;EACAzB,WAAWA,CAAA,EAAG;IACV,MAAM;MAAE7B;IAAQ,CAAC,GAAG,IAAI;IACxB,IAAIA,OAAO,KAAKA,OAAO,CAACkC,UAAU,KAAK,CAAC,IAAIlC,OAAO,CAACkC,UAAU,KAAK,CAAC,CAAC,EAAE;MACnElC,OAAO,CAACmC,KAAK,CAAC,CAAC;IACnB;IACA,IAAI,CAACpB,WAAW,CAAC,CAAC;IAClB,KAAK,CAACc,WAAW,CAAC,CAAC;EACvB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}