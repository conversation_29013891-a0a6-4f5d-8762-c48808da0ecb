{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ButtonComponent } from '@ava/play-comp-library';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction AgentOutputComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵtext(1, \" No agent outputs available yet. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentOutputComponent_section_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 12)(1, \"div\", 13)(2, \"h3\");\n    i0.ɵɵtext(3, \"Expected Output \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 13)(7, \"h3\");\n    i0.ɵɵtext(8, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 13)(12, \"h3\");\n    i0.ɵɵtext(13, \"Summary \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"p\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 13)(17, \"h3\");\n    i0.ɵɵtext(18, \"Raw Output\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"p\");\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const output_r1 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(output_r1.expected_output);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(output_r1.description);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(output_r1.summary);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(output_r1.raw);\n  }\n}\nexport let AgentOutputComponent = /*#__PURE__*/(() => {\n  class AgentOutputComponent {\n    outputs = [];\n    export = new EventEmitter();\n    constructor() {}\n    getContentType(output) {\n      // Default to 'text' if not specified\n      if (!output.type) {\n        // Try to auto-detect\n        if (output.content.startsWith('<') && output.content.includes('</')) {\n          return 'code';\n        } else if (output.content.includes('```') || output.content.includes('#')) {\n          return 'markdown';\n        } else if (output.content.startsWith('{') || output.content.startsWith('[')) {\n          return 'json';\n        }\n        return 'text';\n      }\n      return output.type;\n    }\n    copyToClipboard(content) {\n      navigator.clipboard.writeText(content).then(() => {\n        // Could show a toast notification here\n        console.log('Content copied to clipboard');\n      }).catch(err => {\n        console.error('Could not copy text: ', err);\n      });\n    }\n    exportOutput() {\n      this.export.emit();\n    }\n    previewOutput(output) {\n      // In a real app, this would open a preview modal or navigate to a preview page\n      console.log('Preview output:', output);\n      window.open(`data:text/html;charset=utf-8,${encodeURIComponent(output.content)}`, '_blank');\n    }\n    ngOnInit() {\n      console.log(this.outputs);\n    }\n    static ɵfac = function AgentOutputComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AgentOutputComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AgentOutputComponent,\n      selectors: [[\"app-agent-output\"]],\n      inputs: {\n        outputs: \"outputs\"\n      },\n      outputs: {\n        export: \"export\"\n      },\n      decls: 13,\n      vars: 5,\n      consts: [[1, \"agent-output\"], [\"width\", \"0\", \"height\", \"0\", 2, \"position\", \"absolute\"], [\"id\", \"gradient\", \"x1\", \"0%\", \"y1\", \"0%\", \"x2\", \"100%\", \"y2\", \"0%\"], [\"offset\", \"0%\", \"stop-color\", \"#6566CD\"], [\"offset\", \"100%\", \"stop-color\", \"#F96CAB\"], [1, \"output-header\"], [\"id\", \"agent-output-title\"], [\"aria-label\", \"Export agent outputs\", \"title\", \"Export agent outputs\", \"variant\", \"primary\", \"size\", \"medium\", \"label\", \"Export\", 3, \"userClick\", \"iconName\", \"iconPosition\", \"iconColor\"], [\"aria-labelledby\", \"agent-output-title\", 1, \"output-content\"], [\"class\", \"no-outputs\", \"aria-live\", \"polite\", 4, \"ngIf\"], [\"class\", \"output-card\", 4, \"ngFor\", \"ngForOf\"], [\"aria-live\", \"polite\", 1, \"no-outputs\"], [1, \"output-card\"], [1, \"output-card-content\"]],\n      template: function AgentOutputComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(1, \"svg\", 1)(2, \"defs\")(3, \"linearGradient\", 2);\n          i0.ɵɵelement(4, \"stop\", 3)(5, \"stop\", 4);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(6, \"div\", 5)(7, \"h3\", 6);\n          i0.ɵɵtext(8, \"Agent Output\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"ava-button\", 7);\n          i0.ɵɵlistener(\"userClick\", function AgentOutputComponent_Template_ava_button_userClick_9_listener() {\n            return ctx.exportOutput();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 8);\n          i0.ɵɵtemplate(11, AgentOutputComponent_div_11_Template, 2, 0, \"div\", 9)(12, AgentOutputComponent_section_12_Template, 21, 4, \"section\", 10);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"iconName\", \"Download\")(\"iconPosition\", \"right\")(\"iconColor\", \"white\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.outputs.length);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.outputs);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, ButtonComponent],\n      styles: [\".agent-output[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  padding: 20px;\\n  background: var(--card-bg);\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 24px;\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: var(--text-color);\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-header[_ngcontent-%COMP%]   .export-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 10px 16px;\\n  background-color: var(--card-bg);\\n  position: relative;\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  color: transparent;\\n  background-image: var(--gradient-primary);\\n  background-clip: text;\\n  -webkit-background-clip: text;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-header[_ngcontent-%COMP%]   .export-button[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  border-radius: 8px;\\n  padding: 1px;\\n  background: var(--gradient-primary);\\n  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);\\n  mask-composite: exclude;\\n  pointer-events: none;\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-header[_ngcontent-%COMP%]   .export-button[_ngcontent-%COMP%]:hover {\\n  background-color: var(--dropdown-hover-bg);\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-header[_ngcontent-%COMP%]   .export-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n  stroke: url(#gradient);\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-header[_ngcontent-%COMP%]   .export-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]   path[_ngcontent-%COMP%] {\\n  stroke: url(#gradient);\\n  stroke-width: 2;\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n  overflow-y: auto;\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n  height: 6px;\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: var(--dashboard-scrollbar-track);\\n  border-radius: 3px;\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: var(--dashboard-scrollbar-thumb);\\n  border-radius: 3px;\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: var(--dashboard-scrollbar-thumb-hover);\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]   .no-outputs[_ngcontent-%COMP%] {\\n  padding: 40px 0;\\n  text-align: center;\\n  color: var(--text-secondary);\\n  font-size: 14px;\\n  font-style: italic;\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]   .output-card[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  border: 1px solid var(--dashboard-border-light);\\n  border-radius: 8px;\\n  overflow: hidden;\\n  background-color: var(--card-bg);\\n  box-shadow: 0 2px 4px var(--card-shadow);\\n  padding: 1rem;\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]   .output-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  font-weight: 700;\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]   .output-card[_ngcontent-%COMP%]   .output-card-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 12px 16px;\\n  background-color: var(--dashboard-bg-lighter);\\n  border-bottom: 1px solid var(--dashboard-border-light);\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]   .output-card[_ngcontent-%COMP%]   .output-card-header[_ngcontent-%COMP%]   .output-info[_ngcontent-%COMP%]   .output-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: var(--text-color);\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]   .output-card[_ngcontent-%COMP%]   .output-card-header[_ngcontent-%COMP%]   .output-info[_ngcontent-%COMP%]   .output-subtitle[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: var(--text-secondary);\\n  margin-top: 2px;\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]   .output-card[_ngcontent-%COMP%]   .output-card-header[_ngcontent-%COMP%]   .output-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]   .output-card[_ngcontent-%COMP%]   .output-card-header[_ngcontent-%COMP%]   .output-actions[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  padding: 6px 10px;\\n  background-color: var(--card-bg);\\n  border: 1px solid var(--dashboard-border-light);\\n  border-radius: 4px;\\n  font-size: 12px;\\n  color: var(--text-secondary);\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]   .output-card[_ngcontent-%COMP%]   .output-card-header[_ngcontent-%COMP%]   .output-actions[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]:hover {\\n  background-color: var(--dashboard-bg-lighter);\\n  color: var(--text-color);\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]   .output-card[_ngcontent-%COMP%]   .output-card-header[_ngcontent-%COMP%]   .output-actions[_ngcontent-%COMP%]   .action-button.copy-button[_ngcontent-%COMP%] {\\n  padding: 6px;\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]   .output-card[_ngcontent-%COMP%]   .output-card-header[_ngcontent-%COMP%]   .output-actions[_ngcontent-%COMP%]   .action-button.preview-button[_ngcontent-%COMP%] {\\n  background-color: var(--dashboard-bg-icon-button);\\n  border-color: var(--dashboard-border-accent);\\n  color: var(--dashboard-secondary);\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]   .output-card[_ngcontent-%COMP%]   .output-card-header[_ngcontent-%COMP%]   .output-actions[_ngcontent-%COMP%]   .action-button.preview-button[_ngcontent-%COMP%]:hover {\\n  background-color: var(--dropdown-hover-bg);\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]   .output-card[_ngcontent-%COMP%]   .output-card-content.code[_ngcontent-%COMP%] {\\n  background-color: var(--code-bg, var(--dashboard-bg-light));\\n  color: var(--code-color, var(--text-color));\\n  font-family: \\\"Fira Code\\\", monospace;\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]   .output-card[_ngcontent-%COMP%]   .output-card-content.code[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 16px;\\n  overflow-x: auto;\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]   .output-card[_ngcontent-%COMP%]   .output-card-content.code[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n  height: 6px;\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]   .output-card[_ngcontent-%COMP%]   .output-card-content.code[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: var(--dashboard-scrollbar-track);\\n  border-radius: 3px;\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]   .output-card[_ngcontent-%COMP%]   .output-card-content.code[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: var(--dashboard-scrollbar-thumb);\\n  border-radius: 3px;\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]   .output-card[_ngcontent-%COMP%]   .output-card-content.code[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: var(--dashboard-scrollbar-thumb-hover);\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]   .output-card[_ngcontent-%COMP%]   .output-card-content.code[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  line-height: 1.5;\\n  white-space: pre-wrap;\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]   .output-card[_ngcontent-%COMP%]   .output-card-content.text[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  color: var(--text-color);\\n  font-size: 14px;\\n  line-height: 1.5;\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]   .output-card[_ngcontent-%COMP%]   .output-card-content.text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  margin: 0;\\n  white-space: pre-wrap;\\n  word-break: break-word;\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]   .output-card[_ngcontent-%COMP%]   .output-card-content.json[_ngcontent-%COMP%] {\\n  background-color: var(--code-bg, var(--dashboard-bg-light));\\n  color: var(--code-color, var(--text-color));\\n  font-family: \\\"Fira Code\\\", monospace;\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]   .output-card[_ngcontent-%COMP%]   .output-card-content.json[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 16px;\\n  overflow-x: auto;\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]   .output-card[_ngcontent-%COMP%]   .output-card-content.json[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n  height: 6px;\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]   .output-card[_ngcontent-%COMP%]   .output-card-content.json[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: var(--dashboard-scrollbar-track);\\n  border-radius: 3px;\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]   .output-card[_ngcontent-%COMP%]   .output-card-content.json[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: var(--dashboard-scrollbar-thumb);\\n  border-radius: 3px;\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]   .output-card[_ngcontent-%COMP%]   .output-card-content.json[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: var(--dashboard-scrollbar-thumb-hover);\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]   .output-card[_ngcontent-%COMP%]   .output-card-content.json[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  line-height: 1.5;\\n  white-space: pre-wrap;\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]   .output-card[_ngcontent-%COMP%]   .output-card-content.markdown[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  color: var(--text-color);\\n  font-size: 14px;\\n  line-height: 1.5;\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]   .output-card[_ngcontent-%COMP%]   .output-card-content.markdown[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  margin: 0;\\n  white-space: pre-wrap;\\n  word-break: break-word;\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]   .output-card[_ngcontent-%COMP%]   .output-card-footer[_ngcontent-%COMP%] {\\n  padding: 8px 16px;\\n  background-color: var(--dashboard-bg-lighter);\\n  border-top: 1px solid var(--dashboard-border-light);\\n}\\n.agent-output[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%]   .output-card[_ngcontent-%COMP%]   .output-card-footer[_ngcontent-%COMP%]   .output-timestamp[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: var(--text-secondary);\\n}\\n\\n.card-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 32px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return AgentOutputComponent;\n})();", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "ButtonComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "output_r1", "expected_output", "description", "summary", "raw", "AgentOutputComponent", "outputs", "export", "constructor", "getContentType", "output", "type", "content", "startsWith", "includes", "copyToClipboard", "navigator", "clipboard", "writeText", "then", "console", "log", "catch", "err", "error", "exportOutput", "emit", "previewOutput", "window", "open", "encodeURIComponent", "ngOnInit", "selectors", "inputs", "decls", "vars", "consts", "template", "AgentOutputComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵlistener", "AgentOutputComponent_Template_ava_button_userClick_9_listener", "ɵɵtemplate", "AgentOutputComponent_div_11_Template", "AgentOutputComponent_section_12_Template", "ɵɵproperty", "length", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\workflows\\workflow-execution\\components\\agent-output\\agent-output.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\pages\\workflows\\workflow-execution\\components\\agent-output\\agent-output.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ButtonComponent } from '@ava/play-comp-library';\r\nexport interface AgentOutput {\r\n  id: string;\r\n  title: string;\r\n  content: string;\r\n  agentName: string;\r\n  timestamp: string;\r\n  type?: 'code' | 'text' | 'json' | 'markdown';\r\n  description: string;\r\n  expected_output: string;\r\n  summary: string;\r\n  raw: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-agent-output',\r\n  standalone: true,\r\n  imports: [CommonModule, ButtonComponent],\r\n  templateUrl: './agent-output.component.html',\r\n  styleUrls: ['./agent-output.component.scss'],\r\n})\r\nexport class AgentOutputComponent implements OnInit {\r\n  @Input() outputs: AgentOutput[] = [];\r\n  @Output() export = new EventEmitter<void>();\r\n\r\n  constructor() {}\r\n\r\n  getContentType(output: AgentOutput): string {\r\n    // Default to 'text' if not specified\r\n    if (!output.type) {\r\n      // Try to auto-detect\r\n      if (output.content.startsWith('<') && output.content.includes('</')) {\r\n        return 'code';\r\n      } else if (\r\n        output.content.includes('```') ||\r\n        output.content.includes('#')\r\n      ) {\r\n        return 'markdown';\r\n      } else if (\r\n        output.content.startsWith('{') ||\r\n        output.content.startsWith('[')\r\n      ) {\r\n        return 'json';\r\n      }\r\n      return 'text';\r\n    }\r\n    return output.type;\r\n  }\r\n\r\n  copyToClipboard(content: string): void {\r\n    navigator.clipboard\r\n      .writeText(content)\r\n      .then(() => {\r\n        // Could show a toast notification here\r\n        console.log('Content copied to clipboard');\r\n      })\r\n      .catch((err) => {\r\n        console.error('Could not copy text: ', err);\r\n      });\r\n  }\r\n\r\n  exportOutput(): void {\r\n    this.export.emit();\r\n  }\r\n\r\n  previewOutput(output: AgentOutput): void {\r\n    // In a real app, this would open a preview modal or navigate to a preview page\r\n    console.log('Preview output:', output);\r\n    window.open(\r\n      `data:text/html;charset=utf-8,${encodeURIComponent(output.content)}`,\r\n      '_blank',\r\n    );\r\n  }\r\n  ngOnInit(): void {\r\n    console.log(this.outputs);\r\n  }\r\n}\r\n", "<div class=\"agent-output\">\r\n  <!-- SVG Gradient Definitions for Icons -->\r\n  <svg width=\"0\" height=\"0\" style=\"position: absolute\">\r\n    <defs>\r\n      <linearGradient id=\"gradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\r\n        <stop offset=\"0%\" stop-color=\"#6566CD\" />\r\n        <stop offset=\"100%\" stop-color=\"#F96CAB\" />\r\n      </linearGradient>\r\n    </defs>\r\n  </svg>\r\n\r\n  <div class=\"output-header\">\r\n    <h3 id=\"agent-output-title\">Agent Output</h3>\r\n    <ava-button\r\n      (userClick)=\"exportOutput()\"\r\n      aria-label=\"Export agent outputs\"\r\n      title=\"Export agent outputs\"\r\n      variant=\"primary\"\r\n      size=\"medium\"\r\n      label=\"Export\"\r\n      [iconName]=\"'Download'\"\r\n      [iconPosition] = \"'right'\"\r\n      [iconColor]=\"'white'\"\r\n    >\r\n    </ava-button>\r\n  </div>\r\n\r\n  <div class=\"output-content\" aria-labelledby=\"agent-output-title\">\r\n    <!-- No outputs message -->\r\n    <div *ngIf=\"!outputs.length\" class=\"no-outputs\" aria-live=\"polite\">\r\n      No agent outputs available yet.\r\n    </div>\r\n\r\n\r\n    <!-- Output cards -->\r\n    <!-- <div *ngFor=\"let output of outputs; let i = index\" class=\"output-card\">\r\n      <div class=\"output-card-header\">\r\n        <div class=\"output-info\">\r\n          <h4 class=\"output-title\" [id]=\"'output-title-' + i\">\r\n            {{ output.title }}\r\n          </h4>\r\n          <div class=\"output-subtitle\">{{ output.agentName }} Output</div>\r\n        </div>\r\n        <div class=\"output-actions\">\r\n          <button\r\n            class=\"action-button copy-button\"\r\n            (click)=\"copyToClipboard(output.content)\"\r\n            aria-label=\"Copy to clipboard\"\r\n            title=\"Copy to clipboard\"\r\n          >\r\n            <svg\r\n              width=\"16\"\r\n              height=\"16\"\r\n              viewBox=\"0 0 24 24\"\r\n              fill=\"none\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              aria-hidden=\"true\"\r\n            >\r\n              <rect\r\n                x=\"9\"\r\n                y=\"9\"\r\n                width=\"13\"\r\n                height=\"13\"\r\n                rx=\"2\"\r\n                ry=\"2\"\r\n                stroke=\"url(#gradient)\"\r\n                stroke-width=\"2\"\r\n                stroke-linecap=\"round\"\r\n                stroke-linejoin=\"round\"\r\n              />\r\n              <path\r\n                d=\"M5 15H4a2 2 0 01-2-2V4a2 2 0 012-2h9a2 2 0 012 2v1\"\r\n                stroke=\"url(#gradient)\"\r\n                stroke-width=\"2\"\r\n                stroke-linecap=\"round\"\r\n                stroke-linejoin=\"round\"\r\n              />\r\n            </svg>\r\n          </button>\r\n          <button\r\n            class=\"action-button preview-button\"\r\n            (click)=\"previewOutput(output)\"\r\n            aria-label=\"Preview output\"\r\n            title=\"Preview output\"\r\n          >\r\n            Preview\r\n            <svg\r\n              width=\"16\"\r\n              height=\"16\"\r\n              viewBox=\"0 0 24 24\"\r\n              fill=\"none\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              aria-hidden=\"true\"\r\n            >\r\n              <path\r\n                d=\"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z\"\r\n                stroke=\"url(#gradient)\"\r\n                stroke-width=\"2\"\r\n                stroke-linecap=\"round\"\r\n                stroke-linejoin=\"round\"\r\n              />\r\n              <circle\r\n                cx=\"12\"\r\n                cy=\"12\"\r\n                r=\"3\"\r\n                stroke=\"url(#gradient)\"\r\n                stroke-width=\"2\"\r\n                stroke-linecap=\"round\"\r\n                stroke-linejoin=\"round\"\r\n              />\r\n            </svg>\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      // comment: Code or text content based on type\r\n      <div\r\n        class=\"output-card-content\"\r\n        [ngClass]=\"getContentType(output)\"\r\n        [attr.aria-labelledby]=\"'output-title-' + i\"\r\n      >\r\n        <pre><code>{{ output.content }}</code></pre>\r\n      </div>\r\n\r\n      Timestamp and metadata\r\n      <div class=\"output-card-footer\">\r\n        <div class=\"output-timestamp\">Generated: {{ output.timestamp }}</div>\r\n      </div>\r\n    </div> -->\r\n\r\n    <section *ngFor=\"let output of outputs\" class=\"output-card\">\r\n\r\n      <!-- <div class=\"card-details\"> -->\r\n      <div class=\"output-card-content \">\r\n        <h3>Expected Output\r\n        </h3>\r\n        <p>{{output.expected_output}}</p>\r\n      </div>\r\n\r\n      <div class=\"output-card-content \">\r\n        <h3>Description</h3>\r\n        <p>{{output.description}}</p>\r\n      </div>\r\n\r\n\r\n      <div class=\"output-card-content \">\r\n        <h3>Summary\r\n        </h3>\r\n        <p>{{output.summary}}</p>\r\n      </div>\r\n      <!-- </div> -->\r\n\r\n      <div class=\"output-card-content \">\r\n        <h3>Raw Output</h3>\r\n        <p>{{output.raw}}</p>\r\n      </div>\r\n    </section>\r\n  </div>\r\n</div>"], "mappings": "AAAA,SAAmCA,YAAY,QAAgB,eAAe;AAC9E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,wBAAwB;;;;;IC2BpDC,EAAA,CAAAC,cAAA,cAAmE;IACjED,EAAA,CAAAE,MAAA,wCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAuGFH,EAJJ,CAAAC,cAAA,kBAA4D,cAGxB,SAC5B;IAAAD,EAAA,CAAAE,MAAA,uBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAC/BF,EAD+B,CAAAG,YAAA,EAAI,EAC7B;IAGJH,EADF,CAAAC,cAAA,cAAkC,SAC5B;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAC3BF,EAD2B,CAAAG,YAAA,EAAI,EACzB;IAIJH,EADF,CAAAC,cAAA,eAAkC,UAC5B;IAAAD,EAAA,CAAAE,MAAA,gBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,IAAkB;IACvBF,EADuB,CAAAG,YAAA,EAAI,EACrB;IAIJH,EADF,CAAAC,cAAA,eAAkC,UAC5B;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnBH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,IAAc;IAErBF,EAFqB,CAAAG,YAAA,EAAI,EACjB,EACE;;;;IApBHH,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAK,iBAAA,CAAAC,SAAA,CAAAC,eAAA,CAA0B;IAK1BP,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,iBAAA,CAAAC,SAAA,CAAAE,WAAA,CAAsB;IAOtBR,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAAC,SAAA,CAAAG,OAAA,CAAkB;IAMlBT,EAAA,CAAAI,SAAA,GAAc;IAAdJ,EAAA,CAAAK,iBAAA,CAAAC,SAAA,CAAAI,GAAA,CAAc;;;ADnIzB,WAAaC,oBAAoB;EAA3B,MAAOA,oBAAoB;IACtBC,OAAO,GAAkB,EAAE;IAC1BC,MAAM,GAAG,IAAIhB,YAAY,EAAQ;IAE3CiB,YAAA,GAAe;IAEfC,cAAcA,CAACC,MAAmB;MAChC;MACA,IAAI,CAACA,MAAM,CAACC,IAAI,EAAE;QAChB;QACA,IAAID,MAAM,CAACE,OAAO,CAACC,UAAU,CAAC,GAAG,CAAC,IAAIH,MAAM,CAACE,OAAO,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;UACnE,OAAO,MAAM;QACf,CAAC,MAAM,IACLJ,MAAM,CAACE,OAAO,CAACE,QAAQ,CAAC,KAAK,CAAC,IAC9BJ,MAAM,CAACE,OAAO,CAACE,QAAQ,CAAC,GAAG,CAAC,EAC5B;UACA,OAAO,UAAU;QACnB,CAAC,MAAM,IACLJ,MAAM,CAACE,OAAO,CAACC,UAAU,CAAC,GAAG,CAAC,IAC9BH,MAAM,CAACE,OAAO,CAACC,UAAU,CAAC,GAAG,CAAC,EAC9B;UACA,OAAO,MAAM;QACf;QACA,OAAO,MAAM;MACf;MACA,OAAOH,MAAM,CAACC,IAAI;IACpB;IAEAI,eAAeA,CAACH,OAAe;MAC7BI,SAAS,CAACC,SAAS,CAChBC,SAAS,CAACN,OAAO,CAAC,CAClBO,IAAI,CAAC,MAAK;QACT;QACAC,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC5C,CAAC,CAAC,CACDC,KAAK,CAAEC,GAAG,IAAI;QACbH,OAAO,CAACI,KAAK,CAAC,uBAAuB,EAAED,GAAG,CAAC;MAC7C,CAAC,CAAC;IACN;IAEAE,YAAYA,CAAA;MACV,IAAI,CAAClB,MAAM,CAACmB,IAAI,EAAE;IACpB;IAEAC,aAAaA,CAACjB,MAAmB;MAC/B;MACAU,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEX,MAAM,CAAC;MACtCkB,MAAM,CAACC,IAAI,CACT,gCAAgCC,kBAAkB,CAACpB,MAAM,CAACE,OAAO,CAAC,EAAE,EACpE,QAAQ,CACT;IACH;IACAmB,QAAQA,CAAA;MACNX,OAAO,CAACC,GAAG,CAAC,IAAI,CAACf,OAAO,CAAC;IAC3B;;uCAtDWD,oBAAoB;IAAA;;YAApBA,oBAAoB;MAAA2B,SAAA;MAAAC,MAAA;QAAA3B,OAAA;MAAA;MAAAA,OAAA;QAAAC,MAAA;MAAA;MAAA2B,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCvBjC7C,EAAA,CAAAC,cAAA,aAA0B;;UAIpBD,EAFJ,CAAAC,cAAA,aAAqD,WAC7C,wBAC4D;UAE9DD,EADA,CAAA+C,SAAA,cAAyC,cACE;UAGjD/C,EAFI,CAAAG,YAAA,EAAiB,EACZ,EACH;;UAGJH,EADF,CAAAC,cAAA,aAA2B,YACG;UAAAD,EAAA,CAAAE,MAAA,mBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC7CH,EAAA,CAAAC,cAAA,oBAUC;UATCD,EAAA,CAAAgD,UAAA,uBAAAC,8DAAA;YAAA,OAAaH,GAAA,CAAAf,YAAA,EAAc;UAAA,EAAC;UAWhC/B,EADE,CAAAG,YAAA,EAAa,EACT;UAENH,EAAA,CAAAC,cAAA,cAAiE;UAuG/DD,EArGA,CAAAkD,UAAA,KAAAC,oCAAA,iBAAmE,KAAAC,wCAAA,uBAqGP;UA4BhEpD,EADE,CAAAG,YAAA,EAAM,EACF;;;UA1IAH,EAAA,CAAAI,SAAA,GAAuB;UAEvBJ,EAFA,CAAAqD,UAAA,wBAAuB,yBACG,sBACL;UAOjBrD,EAAA,CAAAI,SAAA,GAAqB;UAArBJ,EAAA,CAAAqD,UAAA,UAAAP,GAAA,CAAAlC,OAAA,CAAA0C,MAAA,CAAqB;UAqGCtD,EAAA,CAAAI,SAAA,EAAU;UAAVJ,EAAA,CAAAqD,UAAA,YAAAP,GAAA,CAAAlC,OAAA,CAAU;;;qBD/G9Bd,YAAY,EAAAyD,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE1D,eAAe;MAAA2D,MAAA;IAAA;;SAI5B/C,oBAAoB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}