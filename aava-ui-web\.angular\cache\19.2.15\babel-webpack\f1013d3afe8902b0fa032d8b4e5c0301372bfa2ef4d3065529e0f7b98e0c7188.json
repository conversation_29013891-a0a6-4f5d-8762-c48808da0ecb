{"ast": null, "code": "import { CommonModule, DatePipe } from '@angular/common';\nimport { AvaTextboxComponent, ButtonComponent, IconComponent, SliderComponent } from '@ava/play-comp-library';\nimport { PreviewPanelComponent } from 'projects/console/src/app/shared/components/preview-panel/preview-panel.component';\nimport approvalText from '../../constants/approval.json';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nconst _c0 = () => ({\n  \"border\": \"2px solid transparent\",\n  \"background-image\": \"linear-gradient(#ffffff, #ffffff), linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)\",\n  \"background-origin\": \"border-box\",\n  \"background-clip\": \"padding-box, border-box\",\n  \"--button-effect-color\": \"33, 90, 214\"\n});\nconst _c1 = () => ({\n  background: \"linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)\",\n  \"--button-effect-color\": \"33, 90, 214\"\n});\nfunction AgentsPreviewPanelComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵelement(1, \"div\", 17);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading details...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"label\");\n    i0.ɵɵtext(2, \"Model Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 35);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate((ctx_r1.previewData == null ? null : ctx_r1.previewData.title) || ctx_r1.previewData.data.name);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"label\");\n    i0.ɵɵtext(2, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 36);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.modelDescription || ctx_r1.previewData.data.description);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"label\");\n    i0.ɵɵtext(2, \"Added by\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 35);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.createdBy);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"label\");\n    i0.ɵɵtext(2, \"Added on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 35);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 1, ctx_r1.previewData.data.createdOn || ctx_r1.previewData.data.createdDate, \"MM/dd/yyyy\"));\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"label\");\n    i0.ɵɵtext(2, \"Select Model\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 39)(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.modelType);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"label\");\n    i0.ɵɵtext(2, \"Temperature\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"ava-slider\", 40);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"min\", 0)(\"max\", 1)(\"step\", 0.01)(\"value\", ctx_r1.previewData.data.temperature);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_15_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"label\");\n    i0.ɵɵtext(2, \"Max Token\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"ava-textbox\", 44);\n    i0.ɵɵelementStart(4, \"div\", 45);\n    i0.ɵɵtext(5, \"4096 Tokens used\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"value\", ctx_r1.previewData.data.maxTokens);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_15_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"label\");\n    i0.ɵɵtext(2, \"Top P\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"ava-textbox\", 46);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"value\", ctx_r1.previewData.data.topP);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵtemplate(1, AgentsPreviewPanelComponent_div_9_div_1_div_15_div_1_Template, 6, 1, \"div\", 42)(2, AgentsPreviewPanelComponent_div_9_div_1_div_15_div_2_Template, 4, 1, \"div\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.maxTokens);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.topP);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"label\");\n    i0.ɵɵtext(2, \"Max Iteration\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"ava-textbox\", 47);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"value\", ctx_r1.previewData.data.maxIteration);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"label\");\n    i0.ɵɵtext(2, \"AI Engine\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 39);\n    i0.ɵɵelement(4, \"span\", 48);\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.aiEngine);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"label\");\n    i0.ɵɵtext(2, \"Model Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 39)(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.modelType);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"label\");\n    i0.ɵɵtext(2, \"Base URL\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 49);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.baseurl || ctx_r1.previewData.data.baseUrl);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"label\");\n    i0.ɵɵtext(2, \"LLM Deployment Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 49);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.llmDeploymentName);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"label\");\n    i0.ɵɵtext(2, \"API Key Encoded\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 49);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.apiKey);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"label\");\n    i0.ɵɵtext(2, \"API Version\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 49);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.apiVersion);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 24)(2, \"h3\");\n    i0.ɵɵtext(3, \"Model Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, AgentsPreviewPanelComponent_div_9_div_1_div_4_Template, 5, 1, \"div\", 25)(5, AgentsPreviewPanelComponent_div_9_div_1_div_5_Template, 5, 1, \"div\", 25);\n    i0.ɵɵelementStart(6, \"div\", 26)(7, \"div\", 27);\n    i0.ɵɵtemplate(8, AgentsPreviewPanelComponent_div_9_div_1_div_8_Template, 5, 1, \"div\", 28)(9, AgentsPreviewPanelComponent_div_9_div_1_div_9_Template, 6, 4, \"div\", 28);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 24)(11, \"h3\");\n    i0.ɵɵtext(12, \"Model Configuration\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, AgentsPreviewPanelComponent_div_9_div_1_div_13_Template, 6, 1, \"div\", 29)(14, AgentsPreviewPanelComponent_div_9_div_1_div_14_Template, 4, 4, \"div\", 29)(15, AgentsPreviewPanelComponent_div_9_div_1_div_15_Template, 3, 2, \"div\", 30)(16, AgentsPreviewPanelComponent_div_9_div_1_div_16_Template, 4, 1, \"div\", 29);\n    i0.ɵɵelementStart(17, \"div\", 31)(18, \"span\", 32);\n    i0.ɵɵlistener(\"click\", function AgentsPreviewPanelComponent_div_9_div_1_Template_span_click_18_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleConfigDetails());\n    });\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 33);\n    i0.ɵɵtemplate(21, AgentsPreviewPanelComponent_div_9_div_1_div_21_Template, 7, 1, \"div\", 29)(22, AgentsPreviewPanelComponent_div_9_div_1_div_22_Template, 6, 1, \"div\", 29)(23, AgentsPreviewPanelComponent_div_9_div_1_div_23_Template, 5, 1, \"div\", 29)(24, AgentsPreviewPanelComponent_div_9_div_1_div_24_Template, 5, 1, \"div\", 29)(25, AgentsPreviewPanelComponent_div_9_div_1_div_25_Template, 5, 1, \"div\", 29)(26, AgentsPreviewPanelComponent_div_9_div_1_div_26_Template, 5, 1, \"div\", 29);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.previewData == null ? null : ctx_r1.previewData.title) || ctx_r1.previewData.data.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.modelDescription || ctx_r1.previewData.data.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.createdBy);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.createdOn || ctx_r1.previewData.data.createdDate);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.modelType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.temperature !== undefined && ctx_r1.previewData.data.temperature !== null);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.maxTokens || ctx_r1.previewData.data.topP);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.maxIteration);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" View More Configuration Details \", ctx_r1.showMoreConfig ? \"-\" : \"+\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"expanded\", ctx_r1.showMoreConfig);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.aiEngine);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.modelType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.baseurl || ctx_r1.previewData.data.baseUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.llmDeploymentName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.apiKey);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.apiVersion);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_2_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"label\");\n    i0.ɵɵtext(2, \"Tool Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 35);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate((ctx_r1.previewData == null ? null : ctx_r1.previewData.title) || ctx_r1.previewData.data.name);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_2_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"label\");\n    i0.ɵɵtext(2, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 36);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.description || ctx_r1.previewData.data.appDescription);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_2_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"label\");\n    i0.ɵɵtext(2, \"Area of Scope\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 36);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.areaOfScope);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_2_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"label\");\n    i0.ɵɵtext(2, \"Added by\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 35);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.createdBy);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_2_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"label\");\n    i0.ɵɵtext(2, \"Added on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 35);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 1, ctx_r1.previewData.data.createdOn || ctx_r1.previewData.data.createdDate, \"MM/dd/yyyy\"));\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_2_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"div\", 51);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.functionality || ctx_r1.previewData.data.content || ctx_r1.previewData.data.toolClassDef || \"No tool definition available\");\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"div\", 24)(2, \"h3\");\n    i0.ɵɵtext(3, \"Tool Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, AgentsPreviewPanelComponent_div_9_div_2_div_4_Template, 5, 1, \"div\", 25)(5, AgentsPreviewPanelComponent_div_9_div_2_div_5_Template, 5, 1, \"div\", 25)(6, AgentsPreviewPanelComponent_div_9_div_2_div_6_Template, 5, 1, \"div\", 25);\n    i0.ɵɵelementStart(7, \"div\", 26)(8, \"div\", 27);\n    i0.ɵɵtemplate(9, AgentsPreviewPanelComponent_div_9_div_2_div_9_Template, 5, 1, \"div\", 28)(10, AgentsPreviewPanelComponent_div_9_div_2_div_10_Template, 6, 4, \"div\", 28);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 24)(12, \"h3\");\n    i0.ɵɵtext(13, \"Tool Configuration\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, AgentsPreviewPanelComponent_div_9_div_2_div_14_Template, 3, 1, \"div\", 29);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.previewData == null ? null : ctx_r1.previewData.title) || ctx_r1.previewData.data.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.description || ctx_r1.previewData.data.appDescription);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.areaOfScope);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.createdBy);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.createdOn || ctx_r1.previewData.data.createdDate);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.functionality || ctx_r1.previewData.data.content || ctx_r1.previewData.data.toolClassDef);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_3_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"label\");\n    i0.ɵɵtext(2, \"Prompt Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 35);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate((ctx_r1.previewData == null ? null : ctx_r1.previewData.title) || ctx_r1.previewData.data.name);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_3_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"label\");\n    i0.ɵɵtext(2, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 36);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.description);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_3_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"label\");\n    i0.ɵɵtext(2, \"Role\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 35);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.role);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_3_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"label\");\n    i0.ɵɵtext(2, \"Goal\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 36);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.goal);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_3_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"label\");\n    i0.ɵɵtext(2, \"Backstory\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 36);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.backstory);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_3_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"label\");\n    i0.ɵɵtext(2, \"Added by\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 35);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.createdBy);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_3_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"label\");\n    i0.ɵɵtext(2, \"Added on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 35);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 1, ctx_r1.previewData.data.updatedAt || ctx_r1.previewData.data.createdAt, \"MM/dd/yyyy\"));\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_3_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"label\");\n    i0.ɵɵtext(2, \"Freeform Prompt \");\n    i0.ɵɵelementStart(3, \"span\", 53);\n    i0.ɵɵtext(4, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 54);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.promptTask || ctx_r1.previewData.data.template || ctx_r1.previewData.data.content);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_3_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"label\");\n    i0.ɵɵtext(2, \"Expected Output\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 54);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.expectedOutput);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"div\", 24)(2, \"h3\");\n    i0.ɵɵtext(3, \"Prompt Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, AgentsPreviewPanelComponent_div_9_div_3_div_4_Template, 5, 1, \"div\", 25)(5, AgentsPreviewPanelComponent_div_9_div_3_div_5_Template, 5, 1, \"div\", 25)(6, AgentsPreviewPanelComponent_div_9_div_3_div_6_Template, 5, 1, \"div\", 25)(7, AgentsPreviewPanelComponent_div_9_div_3_div_7_Template, 5, 1, \"div\", 25)(8, AgentsPreviewPanelComponent_div_9_div_3_div_8_Template, 5, 1, \"div\", 25);\n    i0.ɵɵelementStart(9, \"div\", 26)(10, \"div\", 27);\n    i0.ɵɵtemplate(11, AgentsPreviewPanelComponent_div_9_div_3_div_11_Template, 5, 1, \"div\", 28)(12, AgentsPreviewPanelComponent_div_9_div_3_div_12_Template, 6, 4, \"div\", 28);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"div\", 24)(14, \"h3\");\n    i0.ɵɵtext(15, \"Prompt Configuration\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, AgentsPreviewPanelComponent_div_9_div_3_div_16_Template, 7, 1, \"div\", 29)(17, AgentsPreviewPanelComponent_div_9_div_3_div_17_Template, 5, 1, \"div\", 29);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.previewData == null ? null : ctx_r1.previewData.title) || ctx_r1.previewData.data.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.description);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.role);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.goal);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.backstory);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.createdBy);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.updatedAt || ctx_r1.previewData.data.createdAt);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.promptTask || ctx_r1.previewData.data.template || ctx_r1.previewData.data.content);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.expectedOutput);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_4_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"label\");\n    i0.ɵɵtext(2, \"Knowledge Base Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 35);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.name || (ctx_r1.previewData == null ? null : ctx_r1.previewData.title));\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_4_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"label\");\n    i0.ɵɵtext(2, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 36);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.description);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_4_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"label\");\n    i0.ɵɵtext(2, \"Added by\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 35);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.createdBy);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_4_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"label\");\n    i0.ɵɵtext(2, \"Added on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 35);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 1, ctx_r1.previewData.data.createdDate || ctx_r1.previewData.data.createdOn, \"MM/dd/yyyy\"));\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_4_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"label\");\n    i0.ɵɵtext(2, \"Embedding Model\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 39)(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.embeddingModel);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_4_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"label\");\n    i0.ɵɵtext(2, \"Split Size\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"ava-slider\", 40);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"min\", 0)(\"max\", 1)(\"step\", 0.01)(\"value\", ctx_r1.previewData.data.splitSize);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_4_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"label\");\n    i0.ɵɵtext(2, \"Upload Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 49);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.uploadType);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_4_div_16_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵelement(1, \"ava-icon\", 59);\n    i0.ɵɵelementStart(2, \"span\", 60);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r3 = ctx.$implicit;\n    const i_r4 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"iconColor\", ctx_r1.getFileIconColor(i_r4))(\"iconSize\", 16);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(file_r3.fileName || file_r3.name || \"Knowledge Base Data\");\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_4_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"label\");\n    i0.ɵɵtext(2, \"Files Uploaded\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 56);\n    i0.ɵɵtemplate(4, AgentsPreviewPanelComponent_div_9_div_4_div_16_div_4_Template, 4, 3, \"div\", 57);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.previewData.data.files);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"div\", 24)(2, \"h3\");\n    i0.ɵɵtext(3, \"Knowledge Base Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, AgentsPreviewPanelComponent_div_9_div_4_div_4_Template, 5, 1, \"div\", 25)(5, AgentsPreviewPanelComponent_div_9_div_4_div_5_Template, 5, 1, \"div\", 25);\n    i0.ɵɵelementStart(6, \"div\", 26)(7, \"div\", 27);\n    i0.ɵɵtemplate(8, AgentsPreviewPanelComponent_div_9_div_4_div_8_Template, 5, 1, \"div\", 28)(9, AgentsPreviewPanelComponent_div_9_div_4_div_9_Template, 6, 4, \"div\", 28);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 24)(11, \"h3\");\n    i0.ɵɵtext(12, \"Knowledge Base Configuration\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, AgentsPreviewPanelComponent_div_9_div_4_div_13_Template, 6, 1, \"div\", 29)(14, AgentsPreviewPanelComponent_div_9_div_4_div_14_Template, 4, 4, \"div\", 29)(15, AgentsPreviewPanelComponent_div_9_div_4_div_15_Template, 5, 1, \"div\", 29)(16, AgentsPreviewPanelComponent_div_9_div_4_div_16_Template, 5, 1, \"div\", 29);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.name || (ctx_r1.previewData == null ? null : ctx_r1.previewData.title));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.createdBy);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.createdDate || ctx_r1.previewData.data.createdOn);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.embeddingModel);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.splitSize !== undefined && ctx_r1.previewData.data.splitSize !== null);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.uploadType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.files && ctx_r1.previewData.data.files.length > 0);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_5_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"label\");\n    i0.ɵɵtext(2, \"Guardrail Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 35);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate((ctx_r1.previewData == null ? null : ctx_r1.previewData.title) || ctx_r1.previewData.data.name);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_5_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"label\");\n    i0.ɵɵtext(2, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 36);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.description);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_5_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"label\");\n    i0.ɵɵtext(2, \"Added by\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 35);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.createdBy);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_5_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"label\");\n    i0.ɵɵtext(2, \"Added on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 35);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 1, ctx_r1.previewData.data.createdDate || ctx_r1.previewData.data.createdOn, \"MM/dd/yyyy\"));\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_5_div_10_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"label\");\n    i0.ɵɵtext(2, \"Guardrail in Colang\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 51);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.content);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_5_div_10_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"label\");\n    i0.ɵɵtext(2, \"Guardrail in Yml\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 51);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.yamlContent);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_5_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"h3\");\n    i0.ɵɵtext(2, \"Guardrail Configuration\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, AgentsPreviewPanelComponent_div_9_div_5_div_10_div_3_Template, 5, 1, \"div\", 29)(4, AgentsPreviewPanelComponent_div_9_div_5_div_10_div_4_Template, 5, 1, \"div\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.content);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.yamlContent);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"div\", 24)(2, \"h3\");\n    i0.ɵɵtext(3, \"Guardrail Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, AgentsPreviewPanelComponent_div_9_div_5_div_4_Template, 5, 1, \"div\", 25)(5, AgentsPreviewPanelComponent_div_9_div_5_div_5_Template, 5, 1, \"div\", 25);\n    i0.ɵɵelementStart(6, \"div\", 26)(7, \"div\", 27);\n    i0.ɵɵtemplate(8, AgentsPreviewPanelComponent_div_9_div_5_div_8_Template, 5, 1, \"div\", 28)(9, AgentsPreviewPanelComponent_div_9_div_5_div_9_Template, 6, 4, \"div\", 28);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(10, AgentsPreviewPanelComponent_div_9_div_5_div_10_Template, 5, 2, \"div\", 62);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.previewData == null ? null : ctx_r1.previewData.title) || ctx_r1.previewData.data.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.createdBy);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.createdDate || ctx_r1.previewData.data.createdOn);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.yamlContent || ctx_r1.previewData.data.content);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, AgentsPreviewPanelComponent_div_9_div_1_Template, 27, 17, \"div\", 18)(2, AgentsPreviewPanelComponent_div_9_div_2_Template, 15, 6, \"div\", 19)(3, AgentsPreviewPanelComponent_div_9_div_3_Template, 18, 9, \"div\", 20)(4, AgentsPreviewPanelComponent_div_9_div_4_Template, 17, 8, \"div\", 21)(5, AgentsPreviewPanelComponent_div_9_div_5_Template, 11, 5, \"div\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.previewData == null ? null : ctx_r1.previewData.type) === \"model\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.previewData == null ? null : ctx_r1.previewData.type) === \"tool\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.previewData == null ? null : ctx_r1.previewData.type) === \"prompt\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.previewData == null ? null : ctx_r1.previewData.type) === \"knowledge\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.previewData == null ? null : ctx_r1.previewData.type) === \"guardrail\");\n  }\n}\nfunction AgentsPreviewPanelComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"p\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.error);\n  }\n}\nexport let AgentsPreviewPanelComponent = /*#__PURE__*/(() => {\n  class AgentsPreviewPanelComponent {\n    previewData = null;\n    closePreview;\n    editTool;\n    rejectApproval;\n    approveApproval;\n    testApproval;\n    labels = approvalText.labels;\n    showMoreConfig = false;\n    toggleConfigDetails() {\n      this.showMoreConfig = !this.showMoreConfig;\n    }\n    onButtonClick(event) {\n      this.editTool();\n    }\n    handleTest() {\n      this.testApproval();\n    }\n    handleSendback() {\n      this.closePreview();\n      this.rejectApproval();\n    }\n    handleApprove() {\n      this.closePreview();\n      this.approveApproval();\n    }\n    getAdditionalFields(data) {\n      const excludeFields = ['id', 'name', 'description', 'labelCode', 'categoryName', 'categoryId'];\n      return Object.keys(data).filter(key => !excludeFields.includes(key) && data[key] != null).map(key => ({\n        key,\n        value: data[key]\n      }));\n    }\n    getFileIconColor(index) {\n      const colors = ['#dc2626', '#2563eb', '#16a34a', '#f59e0b', '#8b5cf6'];\n      return colors[index % colors.length];\n    }\n    getButtonLabel() {\n      return 'Edit';\n    }\n    static ɵfac = function AgentsPreviewPanelComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AgentsPreviewPanelComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AgentsPreviewPanelComponent,\n      selectors: [[\"app-agents-preview-panel\"]],\n      inputs: {\n        previewData: \"previewData\",\n        closePreview: \"closePreview\",\n        editTool: \"editTool\",\n        rejectApproval: \"rejectApproval\",\n        approveApproval: \"approveApproval\",\n        testApproval: \"testApproval\"\n      },\n      decls: 17,\n      vars: 14,\n      consts: [[1, \"preview-panel\"], [1, \"backdrop\", 3, \"click\"], [1, \"panel-container\", 3, \"click\", \"divider\"], [\"panel-header\", \"\", 1, \"preview-header\"], [1, \"panel-title\"], [\"iconName\", \"x\", \"iconColor\", \"black\", 1, \"close-btn\", 3, \"click\"], [\"panel-content\", \"\", 1, \"preview-content\"], [\"class\", \"preview-loading\", 4, \"ngIf\"], [4, \"ngIf\"], [\"class\", \"preview-error\", 4, \"ngIf\"], [\"panel-footer\", \"\"], [1, \"footer-buttons-row\"], [\"variant\", \"primary\", \"size\", \"small\", \"state\", \"default\", \"iconName\", \"edit\", \"iconPosition\", \"left\", 3, \"userClick\", \"label\"], [\"variant\", \"secondary\", \"size\", \"small\", \"state\", \"default\", \"iconName\", \"play\", \"iconPosition\", \"left\", 3, \"userClick\", \"label\", \"customStyles\"], [\"variant\", \"secondary\", \"size\", \"small\", \"state\", \"default\", \"iconName\", \"move-left\", \"iconPosition\", \"left\", 3, \"userClick\", \"label\", \"customStyles\"], [\"variant\", \"primary\", \"size\", \"small\", \"state\", \"default\", \"iconName\", \"Check\", \"iconPosition\", \"left\", 3, \"userClick\", \"label\", \"customStyles\"], [1, \"preview-loading\"], [1, \"loading-spinner\"], [\"class\", \"model-preview\", 4, \"ngIf\"], [\"class\", \"tool-preview\", 4, \"ngIf\"], [\"class\", \"prompt-preview\", 4, \"ngIf\"], [\"class\", \"knowledge-preview\", 4, \"ngIf\"], [\"class\", \"guardrail-preview\", 4, \"ngIf\"], [1, \"model-preview\"], [1, \"model-section\"], [\"class\", \"model-field\", 4, \"ngIf\"], [1, \"model-meta\"], [1, \"meta-row\"], [\"class\", \"meta-item\", 4, \"ngIf\"], [\"class\", \"config-field\", 4, \"ngIf\"], [\"class\", \"config-row\", 4, \"ngIf\"], [1, \"config-toggle\"], [1, \"toggle-text\", 3, \"click\"], [1, \"config-details\"], [1, \"model-field\"], [1, \"field-value\"], [1, \"field-value\", \"description-text\"], [1, \"meta-item\"], [1, \"config-field\"], [1, \"dropdown-display\"], [3, \"min\", \"max\", \"step\", \"value\"], [1, \"config-row\"], [\"class\", \"config-field half-width\", 4, \"ngIf\"], [1, \"config-field\", \"half-width\"], [\"placeholder\", \"4000\", \"type\", \"number\", 3, \"value\"], [1, \"field-hint\"], [\"placeholder\", \"0.95\", \"type\", \"number\", \"step\", \"0.01\", 3, \"value\"], [\"placeholder\", \"1\", \"type\", \"number\", 3, \"value\"], [1, \"engine-icon\"], [1, \"input-display\"], [1, \"tool-preview\"], [1, \"code-content\"], [1, \"prompt-preview\"], [1, \"required\"], [1, \"prompt-content\"], [1, \"knowledge-preview\"], [1, \"files-list\"], [\"class\", \"file-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"file-item\"], [\"iconName\", \"file-text\", 3, \"iconColor\", \"iconSize\"], [1, \"file-name\"], [1, \"guardrail-preview\"], [\"class\", \"model-section\", 4, \"ngIf\"], [1, \"preview-error\"]],\n      template: function AgentsPreviewPanelComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵlistener(\"click\", function AgentsPreviewPanelComponent_Template_div_click_1_listener() {\n            return ctx.closePreview();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"app-preview-panel\", 2);\n          i0.ɵɵlistener(\"click\", function AgentsPreviewPanelComponent_Template_app_preview_panel_click_2_listener($event) {\n            return $event.stopPropagation();\n          });\n          i0.ɵɵelementStart(3, \"div\", 3)(4, \"span\", 4);\n          i0.ɵɵtext(5, \"Metadata Information\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"ava-icon\", 5);\n          i0.ɵɵlistener(\"click\", function AgentsPreviewPanelComponent_Template_ava_icon_click_6_listener() {\n            return ctx.closePreview();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 6);\n          i0.ɵɵtemplate(8, AgentsPreviewPanelComponent_div_8_Template, 4, 0, \"div\", 7)(9, AgentsPreviewPanelComponent_div_9_Template, 6, 5, \"div\", 8)(10, AgentsPreviewPanelComponent_div_10_Template, 3, 1, \"div\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 10)(12, \"div\", 11)(13, \"ava-button\", 12);\n          i0.ɵɵlistener(\"userClick\", function AgentsPreviewPanelComponent_Template_ava_button_userClick_13_listener($event) {\n            return ctx.onButtonClick($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"ava-button\", 13);\n          i0.ɵɵlistener(\"userClick\", function AgentsPreviewPanelComponent_Template_ava_button_userClick_14_listener() {\n            return ctx.handleTest();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"ava-button\", 14);\n          i0.ɵɵlistener(\"userClick\", function AgentsPreviewPanelComponent_Template_ava_button_userClick_15_listener() {\n            return ctx.handleSendback();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"ava-button\", 15);\n          i0.ɵɵlistener(\"userClick\", function AgentsPreviewPanelComponent_Template_ava_button_userClick_16_listener() {\n            return ctx.handleApprove();\n          });\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"divider\", false);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.previewData == null ? null : ctx.previewData.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (ctx.previewData == null ? null : ctx.previewData.data) && !(ctx.previewData == null ? null : ctx.previewData.loading));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.previewData == null ? null : ctx.previewData.error);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"label\", ctx.getButtonLabel());\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"label\", ctx.labels.test)(\"customStyles\", i0.ɵɵpureFunction0(11, _c0));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"label\", ctx.labels.sendback)(\"customStyles\", i0.ɵɵpureFunction0(12, _c0));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"label\", ctx.labels.approve)(\"customStyles\", i0.ɵɵpureFunction0(13, _c1));\n        }\n      },\n      dependencies: [PreviewPanelComponent, IconComponent, ButtonComponent, DatePipe, CommonModule, i1.NgForOf, i1.NgIf, SliderComponent, AvaTextboxComponent],\n      styles: [\".preview-panel[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  z-index: 1000;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.footer-buttons-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  gap: 12px;\\n  align-items: center;\\n  justify-content: flex-end;\\n}\\n\\n.backdrop[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(0, 0, 0, 0.5);\\n  -webkit-backdrop-filter: blur(4px);\\n          backdrop-filter: blur(4px);\\n}\\n\\n.panel-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1000;\\n  width: 600px;\\n  max-height: 90vh;\\n  background: #fff;\\n  border-radius: 12px;\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);\\n  overflow: hidden;\\n  animation: slideIn 0.3s ease-out;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.preview-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  width: 100%;\\n}\\n\\n.panel-title[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin: 0;\\n}\\n\\n.close-btn[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  background: transparent;\\n  border: none;\\n  color: #6b7280;\\n  cursor: pointer;\\n  border-radius: 8px;\\n  transition: all 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.close-btn[_ngcontent-%COMP%]:hover {\\n  background: #f3f4f6;\\n  color: #374151;\\n}\\n\\n.preview-content[_ngcontent-%COMP%] {\\n  position: relative;\\n  flex: 1;\\n  overflow-y: auto;\\n  overflow-x: hidden; \\n\\n  max-height: calc(90vh - 140px);\\n  padding-top: 40px;\\n  border-top: 1px solid #e5e7eb;\\n}\\n.preview-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.preview-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: rgba(0, 0, 0, 0.1);\\n  border-radius: 3px;\\n}\\n.preview-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(0, 0, 0, 0.3);\\n  border-radius: 3px;\\n}\\n.preview-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: rgba(0, 0, 0, 0.5);\\n}\\n\\n.config-field[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.config-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin-bottom: 8px;\\n}\\n.config-field[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #1a1d29;\\n  line-height: 1.5;\\n}\\n.config-field[_ngcontent-%COMP%]   .field-hint[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #6b7280;\\n  margin-top: 4px;\\n}\\n\\n.code-content[_ngcontent-%COMP%], .prompt-content[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  background: #f8f9fa;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  color: #1a1d29;\\n  font-family: \\\"SF Mono\\\", Monaco, \\\"Cascadia Code\\\", monospace;\\n  font-size: 0.875rem;\\n  line-height: 1.6;\\n  min-height: 120px;\\n  white-space: pre-wrap;\\n  overflow-y: auto;\\n  overflow-x: hidden;\\n  scrollbar-width: none;\\n  -ms-overflow-style: none;\\n}\\n.code-content[_ngcontent-%COMP%]::-webkit-scrollbar, .prompt-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none;\\n}\\n.code-content[_ngcontent-%COMP%]:empty::before, .prompt-content[_ngcontent-%COMP%]:empty::before {\\n  content: \\\"No configuration available\\\";\\n  color: #9ca3af;\\n  font-style: italic;\\n}\\n\\n.model-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin: 0 0 24px 0;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  margin-bottom: 20px;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-row[_ngcontent-%COMP%]   .config-field.half-width[_ngcontent-%COMP%] {\\n  flex: 1;\\n  margin-bottom: 0;\\n  min-width: 0;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n  padding-bottom: 24px;\\n  border-bottom: 1px solid #e5e7eb;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10rem;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #1a1d29;\\n  margin-bottom: 4px;\\n  font-weight: 600;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #1a1d29;\\n  font-size: 0.875rem;\\n}\\n\\n.tool-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%], .prompt-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .prompt-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin: 0 0 24px 0;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%], .prompt-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%], .prompt-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin-bottom: 8px;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%], .prompt-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  line-height: 1.5;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value.description-text[_ngcontent-%COMP%], .prompt-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value.description-text[_ngcontent-%COMP%] {\\n  font-weight: 400;\\n  color: #6b7280;\\n  line-height: 1.6;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .input-display[_ngcontent-%COMP%], .prompt-preview[_ngcontent-%COMP%]   .input-display[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 12px 16px;\\n  background: #f8f9fa;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  color: #1a1d29;\\n  min-height: 48px;\\n  font-size: 0.875rem;\\n}\\n\\n  .preview-panel [panel-footer] {\\n  padding: 20px 24px 24px 24px;\\n  border-top: 1px solid #f0f1f2;\\n  background: #fafbfc;\\n}\\n  .preview-panel [panel-footer] ava-button {\\n  width: 100%;\\n}\\n  .preview-panel [panel-footer] ava-button button {\\n  width: 100%;\\n  height: 44px;\\n  font-weight: 600;\\n  border-radius: 8px;\\n  transition: all 0.2s ease;\\n}\\n  .preview-panel [panel-footer] ava-button button:hover {\\n  transform: translateY(-1px);\\n}\\n\\n.preview-loading[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 20px;\\n}\\n.preview-loading[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border: 3px solid #f3f4f6;\\n  border-top: 3px solid #3b82f6;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n  margin-bottom: 16px;\\n}\\n.preview-loading[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #6b7280;\\n  font-size: 0.875rem;\\n}\\n\\n.preview-error[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: #dc2626;\\n  padding: 40px 20px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .panel-container[_ngcontent-%COMP%] {\\n    width: 95vw;\\n    max-width: 400px;\\n  }\\n  .panel-title[_ngcontent-%COMP%] {\\n    font-size: 1.25rem;\\n  }\\n}\\n.model-preview[_ngcontent-%COMP%]   .preview-field[_ngcontent-%COMP%] {\\n  border-left-color: #10b981;\\n}\\n\\n.tool-preview[_ngcontent-%COMP%]   .preview-field[_ngcontent-%COMP%] {\\n  border-left-color: #f59e0b;\\n}\\n\\n.prompt-preview[_ngcontent-%COMP%]   .preview-field[_ngcontent-%COMP%] {\\n  border-left-color: #8b5cf6;\\n}\\n\\n.knowledge-preview[_ngcontent-%COMP%]   .preview-field[_ngcontent-%COMP%] {\\n  border-left-color: #06b6d4;\\n}\\n\\n.guardrail-preview[_ngcontent-%COMP%]   .preview-field[_ngcontent-%COMP%] {\\n  border-left-color: #ef4444;\\n}\\n\\n.model-preview[_ngcontent-%COMP%] {\\n  height: 100%;\\n  overflow-y: auto;\\n  overflow-x: hidden; \\n\\n  padding-bottom: 20px;\\n  width: 100%;\\n  box-sizing: border-box;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin: 0 0 24px 0;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  margin-bottom: 20px;\\n  width: 100%;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-row[_ngcontent-%COMP%]   .config-field.half-width[_ngcontent-%COMP%] {\\n  flex: 1;\\n  margin-bottom: 0;\\n  min-width: 0;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-row[_ngcontent-%COMP%]   .config-field.half-width[_ngcontent-%COMP%]     ava-textbox {\\n  width: 100%;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-row[_ngcontent-%COMP%]   .config-field.half-width[_ngcontent-%COMP%]     ava-textbox .textbox-container {\\n  width: 100%;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-row[_ngcontent-%COMP%]   .config-field.half-width[_ngcontent-%COMP%]     ava-textbox .textbox-container input {\\n  width: 100%;\\n  box-sizing: border-box;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  width: 100%;\\n  box-sizing: border-box;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin-bottom: 8px;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .field-hint[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #6b7280;\\n  margin-top: 4px;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]     ava-textbox {\\n  width: 100%;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]     ava-textbox .textbox-container {\\n  width: 100%;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]     ava-textbox .textbox-container input {\\n  width: 100%;\\n  box-sizing: border-box;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #374151;\\n  margin-bottom: 8px;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  line-height: 1.5;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value.description-text[_ngcontent-%COMP%] {\\n  font-weight: 400;\\n  color: #6b7280;\\n  line-height: 1.6;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n  padding-bottom: 24px;\\n  border-bottom: 1px solid #e5e7eb;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10rem;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  color: #6b7280;\\n  margin-bottom: 4px;\\n  font-weight: 400;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #1a1d29;\\n  font-size: 0.875rem;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #374151;\\n  margin-bottom: 8px;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .dropdown-display[_ngcontent-%COMP%], \\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .input-display[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 12px 16px;\\n  background: #f8f9fa;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  color: #1a1d29;\\n  min-height: 48px;\\n  box-sizing: border-box;\\n  font-size: 0.875rem;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .dropdown-display[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  width: 0;\\n  height: 0;\\n  border-left: 6px solid transparent;\\n  border-right: 6px solid transparent;\\n  border-top: 8px solid #6b7280;\\n  margin-left: 8px;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .dropdown-display[_ngcontent-%COMP%]   .engine-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 1rem;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]     ava-slider .slider-container {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]     ava-slider .slider-container .slider-track {\\n  flex: 1;\\n  height: 6px;\\n  background: #e5e7eb;\\n  border-radius: 3px;\\n  position: relative;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]     ava-slider .slider-container .slider-track .slider-fill {\\n  height: 100%;\\n  background: #3b82f6;\\n  border-radius: 3px;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]     ava-slider .slider-container .slider-thumb {\\n  width: 20px;\\n  height: 20px;\\n  background: #3b82f6;\\n  border-radius: 50%;\\n  cursor: pointer;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\\n  border: none;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]     ava-slider .slider-container .slider-value {\\n  min-width: 60px;\\n  padding: 8px 12px;\\n  background: #f8f9fa;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 6px;\\n  text-align: center;\\n  font-size: 0.875rem;\\n  color: #374151;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-toggle[_ngcontent-%COMP%] {\\n  margin: 16px 0;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-toggle[_ngcontent-%COMP%]   .toggle-text[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  cursor: pointer;\\n  font-size: 14px;\\n  color: #007bff;\\n  transition: color 0.2s ease;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-toggle[_ngcontent-%COMP%]   .toggle-text[_ngcontent-%COMP%]:hover {\\n  color: #0056b3;\\n}\\n\\n.config-toggle[_ngcontent-%COMP%] {\\n  margin: 16px 0;\\n}\\n.config-toggle[_ngcontent-%COMP%]   .toggle-text[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  cursor: pointer;\\n  font-size: 14px;\\n  color: #007bff;\\n  transition: color 0.2s ease;\\n}\\n.config-toggle[_ngcontent-%COMP%]   .toggle-text[_ngcontent-%COMP%]:hover {\\n  color: #0056b3;\\n}\\n\\n.config-details[_ngcontent-%COMP%] {\\n  max-height: 0;\\n  overflow: hidden;\\n  transition: max-height 0.3s ease;\\n}\\n.config-details.expanded[_ngcontent-%COMP%] {\\n  max-height: 500px;\\n}\\n\\n.prompt-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.prompt-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin: 0 0 24px 0;\\n}\\n.prompt-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.prompt-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #374151;\\n  margin-bottom: 8px;\\n}\\n.prompt-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  line-height: 1.5;\\n}\\n.prompt-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value.description-text[_ngcontent-%COMP%] {\\n  font-weight: 400;\\n  color: #6b7280;\\n  line-height: 1.6;\\n}\\n.prompt-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n  padding-bottom: 24px;\\n  border-bottom: 1px solid #e5e7eb;\\n}\\n.prompt-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10rem;\\n}\\n.prompt-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  color: #6b7280;\\n  margin-bottom: 4px;\\n  font-weight: 400;\\n}\\n.prompt-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #1a1d29;\\n  font-size: 0.875rem;\\n}\\n.prompt-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.prompt-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #374151;\\n  margin-bottom: 8px;\\n}\\n.prompt-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]   .required[_ngcontent-%COMP%] {\\n  color: #ef4444;\\n  margin-left: 2px;\\n}\\n.prompt-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .prompt-content[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  background: #f8f9fa;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  color: #1a1d29;\\n  font-size: 0.875rem;\\n  line-height: 1.6;\\n  min-height: 120px;\\n  white-space: pre-wrap;\\n}\\n\\n.guardrail-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.guardrail-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin: 0 0 24px 0;\\n}\\n.guardrail-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.guardrail-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin-bottom: 8px;\\n}\\n.guardrail-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 400;\\n  color: #1a1d29;\\n  line-height: 1.5;\\n}\\n.guardrail-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value.description-text[_ngcontent-%COMP%] {\\n  font-weight: 400;\\n  color: #6b7280;\\n  line-height: 1.6;\\n}\\n.guardrail-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n  padding-bottom: 24px;\\n  border-bottom: 1px solid #e5e7eb;\\n}\\n.guardrail-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10rem;\\n}\\n.guardrail-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  color: #1a1d29;\\n  margin-bottom: 4px;\\n  font-weight: 600;\\n}\\n.guardrail-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #1a1d29;\\n  font-size: 0.875rem;\\n}\\n.guardrail-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.guardrail-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin-bottom: 8px;\\n}\\n.guardrail-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .code-content[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  background: #f8f9fa;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  color: #1a1d29;\\n  font-family: \\\"SF Mono\\\", Monaco, \\\"Cascadia Code\\\", monospace;\\n  font-size: 0.875rem;\\n  line-height: 1.6;\\n  min-height: 200px;\\n  white-space: pre-wrap;\\n  overflow-y: auto;\\n  max-height: 400px;\\n}\\n\\n.knowledge-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin: 0 0 24px 0;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin-bottom: 8px;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  line-height: 1.5;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value.description-text[_ngcontent-%COMP%] {\\n  font-weight: 400;\\n  color: #6b7280;\\n  line-height: 1.6;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n  padding-bottom: 24px;\\n  border-bottom: 1px solid #e5e7eb;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10rem;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  color: #1a1d29;\\n  margin-bottom: 4px;\\n  font-weight: 600;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #1a1d29;\\n  font-size: 0.875rem;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin-bottom: 8px;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .dropdown-display[_ngcontent-%COMP%], \\n.knowledge-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .input-display[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 12px 16px;\\n  background: #f8f9fa;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  color: #1a1d29;\\n  min-height: 48px;\\n  box-sizing: border-box;\\n  font-size: 0.875rem;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .dropdown-display[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  width: 0;\\n  height: 0;\\n  border-left: 6px solid transparent;\\n  border-right: 6px solid transparent;\\n  border-top: 8px solid #6b7280;\\n  margin-left: 8px;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .slider-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .slider-container[_ngcontent-%COMP%]   .slider-track[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 6px;\\n  background: #e5e7eb;\\n  border-radius: 3px;\\n  position: relative;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .slider-container[_ngcontent-%COMP%]   .slider-track[_ngcontent-%COMP%]   .slider-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: #3b82f6;\\n  border-radius: 3px;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .slider-container[_ngcontent-%COMP%]   .slider-value[_ngcontent-%COMP%] {\\n  min-width: 60px;\\n  padding: 8px 12px;\\n  background: #f8f9fa;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 6px;\\n  text-align: center;\\n  font-size: 0.875rem;\\n  color: #374151;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .files-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 8px 12px;\\n  background: #f8f9fa;\\n  border-radius: 6px;\\n  margin-bottom: 8px;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .files-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-name[_ngcontent-%COMP%] {\\n  color: #3b82f6;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n}\\n\\n.tool-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin: 0 0 24px 0;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin-bottom: 8px;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  line-height: 1.5;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value.description-text[_ngcontent-%COMP%] {\\n  font-weight: 400;\\n  color: #6b7280;\\n  line-height: 1.6;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n  padding-bottom: 24px;\\n  border-bottom: 1px solid #e5e7eb;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10rem;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  color: #1a1d29;\\n  margin-bottom: 4px;\\n  font-weight: 600;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #1a1d29;\\n  font-size: 0.875rem;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin-bottom: 8px;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .input-display[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 12px 16px;\\n  background: #f8f9fa;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  color: #1a1d29;\\n  min-height: 48px;\\n  box-sizing: border-box;\\n  font-size: 0.875rem;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .code-content[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  background: #f8f9fa;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  color: #1a1d29;\\n  font-family: \\\"SF Mono\\\", Monaco, \\\"Cascadia Code\\\", monospace;\\n  font-size: 0.875rem;\\n  line-height: 1.6;\\n  min-height: 200px;\\n  white-space: pre-wrap;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .code-content[_ngcontent-%COMP%]:empty::before {\\n  content: \\\"No configuration available\\\";\\n  color: #9ca3af;\\n  font-style: italic;\\n}\\n\\n[panel-footer][_ngcontent-%COMP%] {\\n  position: sticky;\\n  bottom: 0;\\n  background: white;\\n  padding: 16px;\\n  border-top: 1px solid #f0f1f2;\\n  margin-top: auto;\\n}\\n\\n.code-content[_ngcontent-%COMP%] {\\n  overflow-y: auto;\\n  overflow-x: hidden;\\n  scrollbar-width: none; \\n\\n  -ms-overflow-style: none; \\n\\n}\\n.code-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none; \\n\\n}\\n\\n.config-field[_ngcontent-%COMP%]     ava-textbox .textbox-container {\\n  box-shadow: none !important;\\n}\\n.config-field[_ngcontent-%COMP%]     ava-textbox .textbox-container input {\\n  box-shadow: none !important;\\n}\\n\\n  app-preview-panel .preview-panel > .preview-header {\\n  padding: 24px !important;\\n}\\n\\n  app-preview-panel .preview-panel > .preview-content {\\n  padding: 0 24px !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return AgentsPreviewPanelComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "DatePipe", "AvaTextboxComponent", "ButtonComponent", "IconComponent", "SliderComponent", "PreviewPanelComponent", "approvalText", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "previewData", "title", "data", "name", "modelDescription", "description", "created<PERSON>y", "ɵɵpipeBind2", "createdOn", "createdDate", "modelType", "ɵɵproperty", "temperature", "maxTokens", "topP", "ɵɵtemplate", "AgentsPreviewPanelComponent_div_9_div_1_div_15_div_1_Template", "AgentsPreviewPanelComponent_div_9_div_1_div_15_div_2_Template", "maxIteration", "aiEngine", "baseurl", "baseUrl", "llmDeploymentName", "<PERSON><PERSON><PERSON><PERSON>", "apiVersion", "AgentsPreviewPanelComponent_div_9_div_1_div_4_Template", "AgentsPreviewPanelComponent_div_9_div_1_div_5_Template", "AgentsPreviewPanelComponent_div_9_div_1_div_8_Template", "AgentsPreviewPanelComponent_div_9_div_1_div_9_Template", "AgentsPreviewPanelComponent_div_9_div_1_div_13_Template", "AgentsPreviewPanelComponent_div_9_div_1_div_14_Template", "AgentsPreviewPanelComponent_div_9_div_1_div_15_Template", "AgentsPreviewPanelComponent_div_9_div_1_div_16_Template", "ɵɵlistener", "AgentsPreviewPanelComponent_div_9_div_1_Template_span_click_18_listener", "ɵɵrestoreView", "_r1", "ɵɵnextContext", "ɵɵresetView", "toggleConfigDetails", "AgentsPreviewPanelComponent_div_9_div_1_div_21_Template", "AgentsPreviewPanelComponent_div_9_div_1_div_22_Template", "AgentsPreviewPanelComponent_div_9_div_1_div_23_Template", "AgentsPreviewPanelComponent_div_9_div_1_div_24_Template", "AgentsPreviewPanelComponent_div_9_div_1_div_25_Template", "AgentsPreviewPanelComponent_div_9_div_1_div_26_Template", "undefined", "ɵɵtextInterpolate1", "showMoreConfig", "ɵɵclassProp", "appDescription", "areaOfScope", "functionality", "content", "toolClassDef", "AgentsPreviewPanelComponent_div_9_div_2_div_4_Template", "AgentsPreviewPanelComponent_div_9_div_2_div_5_Template", "AgentsPreviewPanelComponent_div_9_div_2_div_6_Template", "AgentsPreviewPanelComponent_div_9_div_2_div_9_Template", "AgentsPreviewPanelComponent_div_9_div_2_div_10_Template", "AgentsPreviewPanelComponent_div_9_div_2_div_14_Template", "role", "goal", "backstory", "updatedAt", "createdAt", "promptTask", "template", "expectedOutput", "AgentsPreviewPanelComponent_div_9_div_3_div_4_Template", "AgentsPreviewPanelComponent_div_9_div_3_div_5_Template", "AgentsPreviewPanelComponent_div_9_div_3_div_6_Template", "AgentsPreviewPanelComponent_div_9_div_3_div_7_Template", "AgentsPreviewPanelComponent_div_9_div_3_div_8_Template", "AgentsPreviewPanelComponent_div_9_div_3_div_11_Template", "AgentsPreviewPanelComponent_div_9_div_3_div_12_Template", "AgentsPreviewPanelComponent_div_9_div_3_div_16_Template", "AgentsPreviewPanelComponent_div_9_div_3_div_17_Template", "embeddingModel", "splitSize", "uploadType", "getFileIconColor", "i_r4", "file_r3", "fileName", "AgentsPreviewPanelComponent_div_9_div_4_div_16_div_4_Template", "files", "AgentsPreviewPanelComponent_div_9_div_4_div_4_Template", "AgentsPreviewPanelComponent_div_9_div_4_div_5_Template", "AgentsPreviewPanelComponent_div_9_div_4_div_8_Template", "AgentsPreviewPanelComponent_div_9_div_4_div_9_Template", "AgentsPreviewPanelComponent_div_9_div_4_div_13_Template", "AgentsPreviewPanelComponent_div_9_div_4_div_14_Template", "AgentsPreviewPanelComponent_div_9_div_4_div_15_Template", "AgentsPreviewPanelComponent_div_9_div_4_div_16_Template", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AgentsPreviewPanelComponent_div_9_div_5_div_10_div_3_Template", "AgentsPreviewPanelComponent_div_9_div_5_div_10_div_4_Template", "AgentsPreviewPanelComponent_div_9_div_5_div_4_Template", "AgentsPreviewPanelComponent_div_9_div_5_div_5_Template", "AgentsPreviewPanelComponent_div_9_div_5_div_8_Template", "AgentsPreviewPanelComponent_div_9_div_5_div_9_Template", "AgentsPreviewPanelComponent_div_9_div_5_div_10_Template", "AgentsPreviewPanelComponent_div_9_div_1_Template", "AgentsPreviewPanelComponent_div_9_div_2_Template", "AgentsPreviewPanelComponent_div_9_div_3_Template", "AgentsPreviewPanelComponent_div_9_div_4_Template", "AgentsPreviewPanelComponent_div_9_div_5_Template", "type", "error", "AgentsPreviewPanelComponent", "closePreview", "editTool", "rejectApproval", "approveApproval", "testApproval", "labels", "onButtonClick", "event", "handleTest", "handleSendback", "handleApprove", "getAdditionalFields", "excludeFields", "Object", "keys", "filter", "key", "includes", "map", "value", "index", "colors", "getButtonLabel", "selectors", "inputs", "decls", "vars", "consts", "AgentsPreviewPanelComponent_Template", "rf", "ctx", "AgentsPreviewPanelComponent_Template_div_click_1_listener", "AgentsPreviewPanelComponent_Template_app_preview_panel_click_2_listener", "$event", "stopPropagation", "AgentsPreviewPanelComponent_Template_ava_icon_click_6_listener", "AgentsPreviewPanelComponent_div_8_Template", "AgentsPreviewPanelComponent_div_9_Template", "AgentsPreviewPanelComponent_div_10_Template", "AgentsPreviewPanelComponent_Template_ava_button_userClick_13_listener", "AgentsPreviewPanelComponent_Template_ava_button_userClick_14_listener", "AgentsPreviewPanelComponent_Template_ava_button_userClick_15_listener", "AgentsPreviewPanelComponent_Template_ava_button_userClick_16_listener", "loading", "test", "ɵɵpureFunction0", "_c0", "sendback", "approve", "_c1", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\approval\\approval-tools\\agents-preview-panel\\agents-preview-panel.component.ts", "C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\approval\\approval-tools\\agents-preview-panel\\agents-preview-panel.component.html"], "sourcesContent": ["import { CommonModule, DatePipe } from '@angular/common';\r\nimport { Component, Input } from '@angular/core';\r\nimport { AvaTextboxComponent, ButtonComponent, IconComponent, SliderComponent } from '@ava/play-comp-library';\r\nimport { PreviewPanelComponent } from 'projects/console/src/app/shared/components/preview-panel/preview-panel.component';\r\nimport approvalText from '../../constants/approval.json'\r\n\r\n@Component({\r\n  selector: 'app-agents-preview-panel',\r\n  imports: [PreviewPanelComponent,IconComponent,ButtonComponent,DatePipe, CommonModule,SliderComponent,AvaTextboxComponent],\r\n  templateUrl: './agents-preview-panel.component.html',\r\n  styleUrl: './agents-preview-panel.component.scss'\r\n})\r\nexport class AgentsPreviewPanelComponent {\r\n  @Input() previewData: any = null;\r\n  @Input() closePreview!: () => void;\r\n  @Input() editTool!: () => void;\r\n  @Input() rejectApproval!: () => void;\r\n  @Input() approveApproval!: () => void;\r\n  @Input() testApproval!: () => void;\r\n  public labels: any = approvalText.labels;\r\n  \r\n  showMoreConfig = false;\r\n\r\n  toggleConfigDetails(): void {\r\n    this.showMoreConfig = !this.showMoreConfig;\r\n  }\r\n\r\n  onButtonClick(event: any): void {\r\n    this.editTool();\r\n  }\r\n\r\n  handleTest(): void {\r\n    this.testApproval();\r\n  }\r\n\r\n  handleSendback(): void {\r\n    this.closePreview();\r\n    this.rejectApproval();\r\n  }\r\n\r\n  handleApprove(): void {\r\n    this.closePreview();\r\n    this.approveApproval();\r\n  }\r\n\r\n  getAdditionalFields(data: any): { key: string; value: any }[] {\r\n    const excludeFields = ['id', 'name', 'description', 'labelCode', 'categoryName', 'categoryId'];\r\n    return Object.keys(data)\r\n      .filter(key => !excludeFields.includes(key) && data[key] != null)\r\n      .map(key => ({ key, value: data[key] }));\r\n  }\r\n\r\n  getFileIconColor(index: number): string {\r\n    const colors = ['#dc2626', '#2563eb', '#16a34a', '#f59e0b', '#8b5cf6'];\r\n    return colors[index % colors.length];\r\n  }\r\n\r\n  getButtonLabel(): string {\r\n    return 'Edit';\r\n  }\r\n}\r\n", "<div class=\"preview-panel\">\r\n  <div class=\"backdrop\" (click)=\"closePreview()\"></div>\r\n<app-preview-panel class=\"panel-container\" [divider]=\"false\" (click)=\"$event.stopPropagation()\">\r\n<div panel-header class=\"preview-header\">\r\n  <span class=\"panel-title\">Metadata Information</span>\r\n  <ava-icon iconName=\"x\" iconColor=\"black\" class=\"close-btn\" (click)=\"closePreview()\"></ava-icon>\r\n</div>\r\n<div panel-content class=\"preview-content\">\r\n  <!-- Loading State -->\r\n  <div *ngIf=\"previewData?.loading\" class=\"preview-loading\">\r\n    <div class=\"loading-spinner\"></div>\r\n    <p>Loading details...</p>\r\n  </div>\r\n\r\n  <!-- Content based on preview data -->\r\n  <div *ngIf=\"previewData?.data && !previewData?.loading\">\r\n    <!-- Model Preview -->\r\n    <div *ngIf=\"previewData?.type === 'model'\" class=\"model-preview\">\r\n      <div class=\"model-section\">\r\n        <h3>Model Details</h3>\r\n        \r\n        <div class=\"model-field\" *ngIf=\"previewData?.title || previewData.data.name\">\r\n          <label>Model Name</label>\r\n          <div class=\"field-value\">{{ previewData?.title || previewData.data.name }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-field\" *ngIf=\"previewData.data.modelDescription || previewData.data.description\">\r\n          <label>Description</label>\r\n          <div class=\"field-value description-text\">{{ previewData.data.modelDescription || previewData.data.description }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-meta\">\r\n          <div class=\"meta-row\">\r\n            <div class=\"meta-item\" *ngIf=\"previewData.data.createdBy\">\r\n              <label>Added by</label>\r\n              <div class=\"field-value\">{{ previewData.data.createdBy}}</div>\r\n            </div>\r\n            <div class=\"meta-item\" *ngIf=\"previewData.data.createdOn || previewData.data.createdDate\">\r\n              <label>Added on</label>\r\n              <div class=\"field-value\">{{ previewData.data.createdOn || previewData.data.createdDate | date:'MM/dd/yyyy' }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"model-section\">\r\n        <h3>Model Configuration</h3>\r\n        \r\n        <div class=\"config-field\" *ngIf=\"previewData.data.modelType\">\r\n          <label>Select Model</label>\r\n          <div class=\"dropdown-display\">\r\n            <span>{{ previewData.data.modelType}}</span>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Temperature -->\r\n        <div class=\"config-field\" *ngIf=\"previewData.data.temperature !== undefined && previewData.data.temperature !== null\">\r\n          <label>Temperature</label>\r\n          <ava-slider [min]=\"0\" [max]=\"1\" [step]=\"0.01\"\r\n              [value]=\"previewData.data.temperature\"></ava-slider>\r\n        </div>\r\n\r\n        <!-- Max Tokens and Top P in same row -->\r\n        <div class=\"config-row\" *ngIf=\"previewData.data.maxTokens || previewData.data.topP\">\r\n          <div class=\"config-field half-width\" *ngIf=\"previewData.data.maxTokens\">\r\n            <label>Max Token</label>\r\n            <ava-textbox \r\n              [value]=\"previewData.data.maxTokens\" \r\n              placeholder=\"4000\"\r\n              type=\"number\">\r\n            </ava-textbox>\r\n            <div class=\"field-hint\">4096 Tokens used</div>\r\n          </div>\r\n\r\n          <div class=\"config-field half-width\" *ngIf=\"previewData.data.topP\">\r\n            <label>Top P</label>\r\n            <ava-textbox \r\n              [value]=\"previewData.data.topP\" \r\n              placeholder=\"0.95\"\r\n              type=\"number\"\r\n              step=\"0.01\">\r\n            </ava-textbox>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Max Iteration -->\r\n        <div class=\"config-field\" *ngIf=\"previewData.data.maxIteration\">\r\n          <label>Max Iteration</label>\r\n          <ava-textbox \r\n            [value]=\"previewData.data.maxIteration\" \r\n            placeholder=\"1\"\r\n            type=\"number\">\r\n          </ava-textbox>\r\n        </div>\r\n\r\n        <div class=\"config-toggle\">\r\n          <span class=\"toggle-text\" (click)=\"toggleConfigDetails()\">\r\n            View More Configuration Details {{ showMoreConfig ? '-' : '+' }}\r\n          </span>\r\n        </div>\r\n\r\n        <!-- Collapsible Configuration Fields -->\r\n        <div class=\"config-details\" [class.expanded]=\"showMoreConfig\">\r\n          <div class=\"config-field\" *ngIf=\"previewData.data.aiEngine\">\r\n            <label>AI Engine</label>\r\n            <div class=\"dropdown-display\">\r\n              <span class=\"engine-icon\"></span>\r\n              <span>{{ previewData.data.aiEngine}}</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"config-field\" *ngIf=\"previewData.data.modelType\">\r\n            <label>Model Type</label>\r\n            <div class=\"dropdown-display\">\r\n              <span>{{ previewData.data.modelType}}</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"config-field\" *ngIf=\"previewData.data.baseurl || previewData.data.baseUrl\">\r\n            <label>Base URL</label>\r\n            <div class=\"input-display\">{{ previewData.data.baseurl || previewData.data.baseUrl }}</div>\r\n          </div>\r\n\r\n          <div class=\"config-field\" *ngIf=\"previewData.data.llmDeploymentName\">\r\n            <label>LLM Deployment Name</label>\r\n            <div class=\"input-display\">{{ previewData.data.llmDeploymentName }}</div>\r\n          </div>\r\n\r\n          <div class=\"config-field\" *ngIf=\"previewData.data.apiKey\">\r\n            <label>API Key Encoded</label>\r\n            <div class=\"input-display\">{{ previewData.data.apiKey}}</div>\r\n          </div>\r\n\r\n          <div class=\"config-field\" *ngIf=\"previewData.data.apiVersion\">\r\n            <label>API Version</label>\r\n            <div class=\"input-display\">{{ previewData.data.apiVersion }}</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Tool Preview -->\r\n    <div *ngIf=\"previewData?.type === 'tool'\" class=\"tool-preview\">\r\n      <div class=\"model-section\">\r\n        <h3>Tool Details</h3>\r\n        \r\n        <div class=\"model-field\" *ngIf=\"previewData?.title || previewData.data.name\">\r\n          <label>Tool Name</label>\r\n          <div class=\"field-value\">{{ previewData?.title || previewData.data.name }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-field\" *ngIf=\"previewData.data.description || previewData.data.appDescription\">\r\n          <label>Description</label>\r\n          <div class=\"field-value description-text\">{{ previewData.data.description || previewData.data.appDescription }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-field\" *ngIf=\"previewData.data.areaOfScope\">\r\n          <label>Area of Scope</label>\r\n          <div class=\"field-value description-text\">{{ previewData.data.areaOfScope }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-meta\">\r\n          <div class=\"meta-row\">\r\n            <div class=\"meta-item\" *ngIf=\"previewData.data.createdBy\">\r\n              <label>Added by</label>\r\n              <div class=\"field-value\">{{ previewData.data.createdBy }}</div>\r\n            </div>\r\n            <div class=\"meta-item\" *ngIf=\"previewData.data.createdOn || previewData.data.createdDate\">\r\n              <label>Added on</label>\r\n              <div class=\"field-value\">{{ previewData.data.createdOn || previewData.data.createdDate | date:'MM/dd/yyyy' }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"model-section\">\r\n        <h3>Tool Configuration</h3>\r\n        \r\n        <div class=\"config-field\" *ngIf=\"previewData.data.functionality || previewData.data.content || previewData.data.toolClassDef\">\r\n          <div class=\"code-content\">{{ previewData.data.functionality || previewData.data.content || previewData.data.toolClassDef || 'No tool definition available' }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Prompt Preview -->\r\n    <div *ngIf=\"previewData?.type === 'prompt'\" class=\"prompt-preview\">\r\n      <div class=\"model-section\">\r\n        <h3>Prompt Details</h3>\r\n        \r\n        <div class=\"model-field\" *ngIf=\"previewData?.title || previewData.data.name\">\r\n          <label>Prompt Name</label>\r\n          <div class=\"field-value\">{{ previewData?.title || previewData.data.name }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-field\" *ngIf=\"previewData.data.description\">\r\n          <label>Description</label>\r\n          <div class=\"field-value description-text\">{{ previewData.data.description }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-field\" *ngIf=\"previewData.data.role\">\r\n          <label>Role</label>\r\n          <div class=\"field-value\">{{ previewData.data.role }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-field\" *ngIf=\"previewData.data.goal\">\r\n          <label>Goal</label>\r\n          <div class=\"field-value description-text\">{{ previewData.data.goal }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-field\" *ngIf=\"previewData.data.backstory\">\r\n          <label>Backstory</label>\r\n          <div class=\"field-value description-text\">{{ previewData.data.backstory }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-meta\">\r\n          <div class=\"meta-row\">\r\n            <div class=\"meta-item\" *ngIf=\"previewData.data.createdBy\">\r\n              <label>Added by</label>\r\n              <div class=\"field-value\">{{ previewData.data.createdBy }}</div>\r\n            </div>\r\n            <div class=\"meta-item\" *ngIf=\"previewData.data.updatedAt || previewData.data.createdAt\">\r\n              <label>Added on</label>\r\n              <div class=\"field-value\">{{ previewData.data.updatedAt || previewData.data.createdAt | date:'MM/dd/yyyy' }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"model-section\">\r\n        <h3>Prompt Configuration</h3>\r\n        \r\n        <div class=\"config-field\" *ngIf=\"previewData.data.promptTask || previewData.data.template || previewData.data.content\">\r\n          <label>Freeform Prompt <span class=\"required\">*</span></label>\r\n          <div class=\"prompt-content\">{{ previewData.data.promptTask || previewData.data.template || previewData.data.content }}</div>\r\n        </div>\r\n\r\n        <div class=\"config-field\" *ngIf=\"previewData.data.expectedOutput\">\r\n          <label>Expected Output</label>\r\n          <div class=\"prompt-content\">{{ previewData.data.expectedOutput }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Knowledge Base Preview -->\r\n    <div *ngIf=\"previewData?.type === 'knowledge'\" class=\"knowledge-preview\">\r\n      <div class=\"model-section\">\r\n        <h3>Knowledge Base Details</h3>\r\n        \r\n        <div class=\"model-field\" *ngIf=\"previewData.data.name || previewData?.title\">\r\n          <label>Knowledge Base Name</label>\r\n          <div class=\"field-value\">{{ previewData.data.name || previewData?.title }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-field\" *ngIf=\"previewData.data.description\">\r\n          <label>Description</label>\r\n          <div class=\"field-value description-text\">{{ previewData.data.description }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-meta\">\r\n          <div class=\"meta-row\">\r\n            <div class=\"meta-item\" *ngIf=\"previewData.data.createdBy\">\r\n              <label>Added by</label>\r\n              <div class=\"field-value\">{{ previewData.data.createdBy}}</div>\r\n            </div>\r\n            <div class=\"meta-item\" *ngIf=\"previewData.data.createdDate || previewData.data.createdOn\">\r\n              <label>Added on</label>\r\n              <div class=\"field-value\">{{ previewData.data.createdDate || previewData.data.createdOn | date:'MM/dd/yyyy' }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"model-section\">\r\n        <h3>Knowledge Base Configuration</h3>\r\n        \r\n        <div class=\"config-field\" *ngIf=\"previewData.data.embeddingModel\">\r\n          <label>Embedding Model</label>\r\n          <div class=\"dropdown-display\">\r\n            <span>{{ previewData.data.embeddingModel }}</span>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"config-field\" *ngIf=\"previewData.data.splitSize !== undefined && previewData.data.splitSize !== null\">\r\n          <label>Split Size</label>\r\n          <ava-slider [min]=\"0\" [max]=\"1\" [step]=\"0.01\"\r\n              [value]=\"previewData.data.splitSize\"></ava-slider>\r\n        </div>\r\n\r\n        <div class=\"config-field\" *ngIf=\"previewData.data.uploadType\">\r\n          <label>Upload Type</label>\r\n          <div class=\"input-display\">{{ previewData.data.uploadType}}</div>\r\n        </div>\r\n\r\n        <div class=\"config-field\" *ngIf=\"previewData.data.files && previewData.data.files.length > 0\">\r\n          <label>Files Uploaded</label>\r\n          <div class=\"files-list\">\r\n            <div *ngFor=\"let file of previewData.data.files; let i = index\" class=\"file-item\">\r\n              <ava-icon iconName=\"file-text\" [iconColor]=\"getFileIconColor(i)\" [iconSize]=\"16\"></ava-icon>\r\n              <span class=\"file-name\">{{ file.fileName || file.name || \"Knowledge Base Data\" }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Guardrail Preview -->\r\n    <div *ngIf=\"previewData?.type === 'guardrail'\" class=\"guardrail-preview\">\r\n      <div class=\"model-section\">\r\n        <h3>Guardrail Details</h3>\r\n        \r\n        <div class=\"model-field\" *ngIf=\"previewData?.title || previewData.data.name\">\r\n          <label>Guardrail Name</label>\r\n          <div class=\"field-value\">{{ previewData?.title || previewData.data.name }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-field\" *ngIf=\"previewData.data.description\">\r\n          <label>Description</label>\r\n          <div class=\"field-value description-text\">{{ previewData.data.description }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-meta\">\r\n          <div class=\"meta-row\">\r\n            <div class=\"meta-item\" *ngIf=\"previewData.data.createdBy\">\r\n              <label>Added by</label>\r\n              <div class=\"field-value\">{{ previewData.data.createdBy}}</div>\r\n            </div>\r\n            <div class=\"meta-item\" *ngIf=\"previewData.data.createdDate || previewData.data.createdOn\">\r\n              <label>Added on</label>\r\n              <div class=\"field-value\">{{ previewData.data.createdDate || previewData.data.createdOn | date:'MM/dd/yyyy' }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"model-section\"  *ngIf=\"previewData.data.yamlContent || previewData.data.content\" >\r\n        <h3>Guardrail Configuration</h3>\r\n        \r\n        <div class=\"config-field\" *ngIf=\"previewData.data.content\">\r\n          <label>Guardrail in Colang</label>\r\n          <div class=\"code-content\">{{ previewData.data.content }}</div>\r\n        </div>\r\n         <div class=\"config-field\" *ngIf=\"previewData.data.yamlContent\">\r\n          <label>Guardrail in Yml</label>\r\n          <div class=\"code-content\">{{ previewData.data.yamlContent }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Error State -->\r\n  <div *ngIf=\"previewData?.error\" class=\"preview-error\">\r\n    <p>{{ previewData.error }}</p>\r\n  </div>\r\n</div>\r\n <div panel-footer>\r\n  <div class=\"footer-buttons-row\">\r\n    <ava-button [label]=\"getButtonLabel()\" variant=\"primary\" size=\"small\" (userClick)=\"onButtonClick($event)\"\r\n    state=\"default\" iconName=\"edit\" iconPosition=\"left\"\r\n    ></ava-button>\r\n    <ava-button [label]=\"labels.test\" (userClick)=\"handleTest()\" variant=\"secondary\" size=\"small\" [customStyles]=\"{\r\n          'border': '2px solid transparent',\r\n          'background-image': 'linear-gradient(#ffffff, #ffffff), linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n          'background-origin': 'border-box',\r\n          'background-clip': 'padding-box, border-box',\r\n          '--button-effect-color': '33, 90, 214'\r\n        }\" state=\"default\" iconName=\"play\" iconPosition=\"left\"></ava-button>\r\n    <ava-button [label]=\"labels.sendback\" (userClick)=\"handleSendback()\" variant=\"secondary\" size=\"small\"\r\n      [customStyles]=\"{\r\n          'border': '2px solid transparent',\r\n          'background-image': 'linear-gradient(#ffffff, #ffffff), linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n          'background-origin': 'border-box',\r\n          'background-clip': 'padding-box, border-box',\r\n          '--button-effect-color': '33, 90, 214'\r\n        }\" state=\"default\" iconName=\"move-left\" iconPosition=\"left\"></ava-button>\r\n    <ava-button [label]=\"labels.approve\" (userClick)=\"handleApprove()\" variant=\"primary\" size=\"small\"\r\n      [customStyles]=\"{\r\n          background:\r\n            'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n          '--button-effect-color': '33, 90, 214',\r\n        }\" state=\"default\" iconName=\"Check\" iconPosition=\"left\"></ava-button>\r\n  </div>\r\n</div>\r\n</app-preview-panel>\r\n</div>\r\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,QAAQ,QAAQ,iBAAiB;AAExD,SAASC,mBAAmB,EAAEC,eAAe,EAAEC,aAAa,EAAEC,eAAe,QAAQ,wBAAwB;AAC7G,SAASC,qBAAqB,QAAQ,kFAAkF;AACxH,OAAOC,YAAY,MAAM,+BAA+B;;;;;;;;;;;;;;;;ICKtDC,EAAA,CAAAC,cAAA,cAA0D;IACxDD,EAAA,CAAAE,SAAA,cAAmC;IACnCF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,yBAAkB;IACvBH,EADuB,CAAAI,YAAA,EAAI,EACrB;;;;;IAUEJ,EADF,CAAAC,cAAA,cAA6E,YACpE;IAAAD,EAAA,CAAAG,MAAA,iBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACzBJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAiD;IAC5EH,EAD4E,CAAAI,YAAA,EAAM,EAC5E;;;;IADqBJ,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAM,iBAAA,EAAAC,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,KAAA,KAAAF,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAC,IAAA,CAAiD;;;;;IAI1EX,EADF,CAAAC,cAAA,cAAmG,YAC1F;IAAAD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC1BJ,EAAA,CAAAC,cAAA,cAA0C;IAAAD,EAAA,CAAAG,MAAA,GAAuE;IACnHH,EADmH,CAAAI,YAAA,EAAM,EACnH;;;;IADsCJ,EAAA,CAAAK,SAAA,GAAuE;IAAvEL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAE,gBAAA,IAAAL,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAG,WAAA,CAAuE;;;;;IAM7Gb,EADF,CAAAC,cAAA,cAA0D,YACjD;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvBJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAA+B;IAC1DH,EAD0D,CAAAI,YAAA,EAAM,EAC1D;;;;IADqBJ,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAI,SAAA,CAA+B;;;;;IAGxDd,EADF,CAAAC,cAAA,cAA0F,YACjF;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvBJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAoF;;IAC/GH,EAD+G,CAAAI,YAAA,EAAM,EAC/G;;;;IADqBJ,EAAA,CAAAK,SAAA,GAAoF;IAApFL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAAe,WAAA,OAAAR,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAM,SAAA,IAAAT,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAO,WAAA,gBAAoF;;;;;IAUjHjB,EADF,CAAAC,cAAA,cAA6D,YACpD;IAAAD,EAAA,CAAAG,MAAA,mBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAEzBJ,EADF,CAAAC,cAAA,cAA8B,WACtB;IAAAD,EAAA,CAAAG,MAAA,GAA+B;IAEzCH,EAFyC,CAAAI,YAAA,EAAO,EACxC,EACF;;;;IAFIJ,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAQ,SAAA,CAA+B;;;;;IAMvClB,EADF,CAAAC,cAAA,cAAsH,YAC7G;IAAAD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC1BJ,EAAA,CAAAE,SAAA,qBACwD;IAC1DF,EAAA,CAAAI,YAAA,EAAM;;;;IAFQJ,EAAA,CAAAK,SAAA,GAAS;IACjBL,EADQ,CAAAmB,UAAA,UAAS,UAAU,cAAc,UAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAU,WAAA,CACH;;;;;IAMxCpB,EADF,CAAAC,cAAA,cAAwE,YAC/D;IAAAD,EAAA,CAAAG,MAAA,gBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACxBJ,EAAA,CAAAE,SAAA,sBAIc;IACdF,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAG,MAAA,uBAAgB;IAC1CH,EAD0C,CAAAI,YAAA,EAAM,EAC1C;;;;IALFJ,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAmB,UAAA,UAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAW,SAAA,CAAoC;;;;;IAQtCrB,EADF,CAAAC,cAAA,cAAmE,YAC1D;IAAAD,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACpBJ,EAAA,CAAAE,SAAA,sBAKc;IAChBF,EAAA,CAAAI,YAAA,EAAM;;;;IALFJ,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAmB,UAAA,UAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAY,IAAA,CAA+B;;;;;IAdrCtB,EAAA,CAAAC,cAAA,cAAoF;IAWlFD,EAVA,CAAAuB,UAAA,IAAAC,6DAAA,kBAAwE,IAAAC,6DAAA,kBAUL;IASrEzB,EAAA,CAAAI,YAAA,EAAM;;;;IAnBkCJ,EAAA,CAAAK,SAAA,EAAgC;IAAhCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAW,SAAA,CAAgC;IAUhCrB,EAAA,CAAAK,SAAA,EAA2B;IAA3BL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAY,IAAA,CAA2B;;;;;IAajEtB,EADF,CAAAC,cAAA,cAAgE,YACvD;IAAAD,EAAA,CAAAG,MAAA,oBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC5BJ,EAAA,CAAAE,SAAA,sBAIc;IAChBF,EAAA,CAAAI,YAAA,EAAM;;;;IAJFJ,EAAA,CAAAK,SAAA,GAAuC;IAAvCL,EAAA,CAAAmB,UAAA,UAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAgB,YAAA,CAAuC;;;;;IAevC1B,EADF,CAAAC,cAAA,cAA4D,YACnD;IAAAD,EAAA,CAAAG,MAAA,gBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACxBJ,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAE,SAAA,eAAiC;IACjCF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,GAA8B;IAExCH,EAFwC,CAAAI,YAAA,EAAO,EACvC,EACF;;;;IAFIJ,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAiB,QAAA,CAA8B;;;;;IAKtC3B,EADF,CAAAC,cAAA,cAA6D,YACpD;IAAAD,EAAA,CAAAG,MAAA,iBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAEvBJ,EADF,CAAAC,cAAA,cAA8B,WACtB;IAAAD,EAAA,CAAAG,MAAA,GAA+B;IAEzCH,EAFyC,CAAAI,YAAA,EAAO,EACxC,EACF;;;;IAFIJ,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAQ,SAAA,CAA+B;;;;;IAKvClB,EADF,CAAAC,cAAA,cAAuF,YAC9E;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvBJ,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAG,MAAA,GAA0D;IACvFH,EADuF,CAAAI,YAAA,EAAM,EACvF;;;;IADuBJ,EAAA,CAAAK,SAAA,GAA0D;IAA1DL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAkB,OAAA,IAAArB,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAmB,OAAA,CAA0D;;;;;IAIrF7B,EADF,CAAAC,cAAA,cAAqE,YAC5D;IAAAD,EAAA,CAAAG,MAAA,0BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAClCJ,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAG,MAAA,GAAwC;IACrEH,EADqE,CAAAI,YAAA,EAAM,EACrE;;;;IADuBJ,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAoB,iBAAA,CAAwC;;;;;IAInE9B,EADF,CAAAC,cAAA,cAA0D,YACjD;IAAAD,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC9BJ,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAG,MAAA,GAA4B;IACzDH,EADyD,CAAAI,YAAA,EAAM,EACzD;;;;IADuBJ,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAqB,MAAA,CAA4B;;;;;IAIvD/B,EADF,CAAAC,cAAA,cAA8D,YACrD;IAAAD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC1BJ,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAG,MAAA,GAAiC;IAC9DH,EAD8D,CAAAI,YAAA,EAAM,EAC9D;;;;IADuBJ,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAsB,UAAA,CAAiC;;;;;;IApHhEhC,EAFJ,CAAAC,cAAA,cAAiE,cACpC,SACrB;IAAAD,EAAA,CAAAG,MAAA,oBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAOtBJ,EALA,CAAAuB,UAAA,IAAAU,sDAAA,kBAA6E,IAAAC,sDAAA,kBAKsB;IAMjGlC,EADF,CAAAC,cAAA,cAAwB,cACA;IAKpBD,EAJA,CAAAuB,UAAA,IAAAY,sDAAA,kBAA0D,IAAAC,sDAAA,kBAIgC;IAMhGpC,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;IAGJJ,EADF,CAAAC,cAAA,eAA2B,UACrB;IAAAD,EAAA,CAAAG,MAAA,2BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAwC5BJ,EAtCA,CAAAuB,UAAA,KAAAc,uDAAA,kBAA6D,KAAAC,uDAAA,kBAQyD,KAAAC,uDAAA,kBAOlC,KAAAC,uDAAA,kBAuBpB;IAU9DxC,EADF,CAAAC,cAAA,eAA2B,gBACiC;IAAhCD,EAAA,CAAAyC,UAAA,mBAAAC,wEAAA;MAAA1C,EAAA,CAAA2C,aAAA,CAAAC,GAAA;MAAA,MAAArC,MAAA,GAAAP,EAAA,CAAA6C,aAAA;MAAA,OAAA7C,EAAA,CAAA8C,WAAA,CAASvC,MAAA,CAAAwC,mBAAA,EAAqB;IAAA,EAAC;IACvD/C,EAAA,CAAAG,MAAA,IACF;IACFH,EADE,CAAAI,YAAA,EAAO,EACH;IAGNJ,EAAA,CAAAC,cAAA,eAA8D;IA+B5DD,EA9BA,CAAAuB,UAAA,KAAAyB,uDAAA,kBAA4D,KAAAC,uDAAA,kBAQC,KAAAC,uDAAA,kBAO0B,KAAAC,uDAAA,kBAKlB,KAAAC,uDAAA,kBAKX,KAAAC,uDAAA,kBAKI;IAMpErD,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;;;;IAtHwBJ,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAmB,UAAA,UAAAZ,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,KAAA,KAAAF,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAC,IAAA,CAAiD;IAKjDX,EAAA,CAAAK,SAAA,EAAuE;IAAvEL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAE,gBAAA,IAAAL,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAG,WAAA,CAAuE;IAOrEb,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAI,SAAA,CAAgC;IAIhCd,EAAA,CAAAK,SAAA,EAAgE;IAAhEL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAM,SAAA,IAAAT,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAO,WAAA,CAAgE;IAWjEjB,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAQ,SAAA,CAAgC;IAQhClB,EAAA,CAAAK,SAAA,EAAyF;IAAzFL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAU,WAAA,KAAAkC,SAAA,IAAA/C,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAU,WAAA,UAAyF;IAO3FpB,EAAA,CAAAK,SAAA,EAAyD;IAAzDL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAW,SAAA,IAAAd,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAY,IAAA,CAAyD;IAuBvDtB,EAAA,CAAAK,SAAA,EAAmC;IAAnCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAgB,YAAA,CAAmC;IAW1D1B,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAuD,kBAAA,sCAAAhD,MAAA,CAAAiD,cAAA,kBACF;IAI0BxD,EAAA,CAAAK,SAAA,EAAiC;IAAjCL,EAAA,CAAAyD,WAAA,aAAAlD,MAAA,CAAAiD,cAAA,CAAiC;IAChCxD,EAAA,CAAAK,SAAA,EAA+B;IAA/BL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAiB,QAAA,CAA+B;IAQ/B3B,EAAA,CAAAK,SAAA,EAAgC;IAAhCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAQ,SAAA,CAAgC;IAOhClB,EAAA,CAAAK,SAAA,EAA0D;IAA1DL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAkB,OAAA,IAAArB,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAmB,OAAA,CAA0D;IAK1D7B,EAAA,CAAAK,SAAA,EAAwC;IAAxCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAoB,iBAAA,CAAwC;IAKxC9B,EAAA,CAAAK,SAAA,EAA6B;IAA7BL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAqB,MAAA,CAA6B;IAK7B/B,EAAA,CAAAK,SAAA,EAAiC;IAAjCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAsB,UAAA,CAAiC;;;;;IAc5DhC,EADF,CAAAC,cAAA,cAA6E,YACpE;IAAAD,EAAA,CAAAG,MAAA,gBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACxBJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAiD;IAC5EH,EAD4E,CAAAI,YAAA,EAAM,EAC5E;;;;IADqBJ,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAM,iBAAA,EAAAC,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,KAAA,KAAAF,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAC,IAAA,CAAiD;;;;;IAI1EX,EADF,CAAAC,cAAA,cAAiG,YACxF;IAAAD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC1BJ,EAAA,CAAAC,cAAA,cAA0C;IAAAD,EAAA,CAAAG,MAAA,GAAqE;IACjHH,EADiH,CAAAI,YAAA,EAAM,EACjH;;;;IADsCJ,EAAA,CAAAK,SAAA,GAAqE;IAArEL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAG,WAAA,IAAAN,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAgD,cAAA,CAAqE;;;;;IAI/G1D,EADF,CAAAC,cAAA,cAA8D,YACrD;IAAAD,EAAA,CAAAG,MAAA,oBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC5BJ,EAAA,CAAAC,cAAA,cAA0C;IAAAD,EAAA,CAAAG,MAAA,GAAkC;IAC9EH,EAD8E,CAAAI,YAAA,EAAM,EAC9E;;;;IADsCJ,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAiD,WAAA,CAAkC;;;;;IAMxE3D,EADF,CAAAC,cAAA,cAA0D,YACjD;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvBJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAgC;IAC3DH,EAD2D,CAAAI,YAAA,EAAM,EAC3D;;;;IADqBJ,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAI,SAAA,CAAgC;;;;;IAGzDd,EADF,CAAAC,cAAA,cAA0F,YACjF;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvBJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAoF;;IAC/GH,EAD+G,CAAAI,YAAA,EAAM,EAC/G;;;;IADqBJ,EAAA,CAAAK,SAAA,GAAoF;IAApFL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAAe,WAAA,OAAAR,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAM,SAAA,IAAAT,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAO,WAAA,gBAAoF;;;;;IAUjHjB,EADF,CAAAC,cAAA,cAA8H,cAClG;IAAAD,EAAA,CAAAG,MAAA,GAAmI;IAC/JH,EAD+J,CAAAI,YAAA,EAAM,EAC/J;;;;IADsBJ,EAAA,CAAAK,SAAA,GAAmI;IAAnIL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAkD,aAAA,IAAArD,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAmD,OAAA,IAAAtD,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAoD,YAAA,mCAAmI;;;;;IAnC/J9D,EAFJ,CAAAC,cAAA,cAA+D,cAClC,SACrB;IAAAD,EAAA,CAAAG,MAAA,mBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAYrBJ,EAVA,CAAAuB,UAAA,IAAAwC,sDAAA,kBAA6E,IAAAC,sDAAA,kBAKoB,IAAAC,sDAAA,kBAKnC;IAM5DjE,EADF,CAAAC,cAAA,cAAwB,cACA;IAKpBD,EAJA,CAAAuB,UAAA,IAAA2C,sDAAA,kBAA0D,KAAAC,uDAAA,kBAIgC;IAMhGnE,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;IAGJJ,EADF,CAAAC,cAAA,eAA2B,UACrB;IAAAD,EAAA,CAAAG,MAAA,0BAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAE3BJ,EAAA,CAAAuB,UAAA,KAAA6C,uDAAA,kBAA8H;IAIlIpE,EADE,CAAAI,YAAA,EAAM,EACF;;;;IApCwBJ,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAmB,UAAA,UAAAZ,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,KAAA,KAAAF,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAC,IAAA,CAAiD;IAKjDX,EAAA,CAAAK,SAAA,EAAqE;IAArEL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAG,WAAA,IAAAN,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAgD,cAAA,CAAqE;IAKrE1D,EAAA,CAAAK,SAAA,EAAkC;IAAlCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAiD,WAAA,CAAkC;IAOhC3D,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAI,SAAA,CAAgC;IAIhCd,EAAA,CAAAK,SAAA,EAAgE;IAAhEL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAM,SAAA,IAAAT,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAO,WAAA,CAAgE;IAWjEjB,EAAA,CAAAK,SAAA,GAAiG;IAAjGL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAkD,aAAA,IAAArD,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAmD,OAAA,IAAAtD,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAoD,YAAA,CAAiG;;;;;IAY1H9D,EADF,CAAAC,cAAA,cAA6E,YACpE;IAAAD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC1BJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAiD;IAC5EH,EAD4E,CAAAI,YAAA,EAAM,EAC5E;;;;IADqBJ,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAM,iBAAA,EAAAC,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,KAAA,KAAAF,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAC,IAAA,CAAiD;;;;;IAI1EX,EADF,CAAAC,cAAA,cAA8D,YACrD;IAAAD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC1BJ,EAAA,CAAAC,cAAA,cAA0C;IAAAD,EAAA,CAAAG,MAAA,GAAkC;IAC9EH,EAD8E,CAAAI,YAAA,EAAM,EAC9E;;;;IADsCJ,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAG,WAAA,CAAkC;;;;;IAI5Eb,EADF,CAAAC,cAAA,cAAuD,YAC9C;IAAAD,EAAA,CAAAG,MAAA,WAAI;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACnBJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAA2B;IACtDH,EADsD,CAAAI,YAAA,EAAM,EACtD;;;;IADqBJ,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA2D,IAAA,CAA2B;;;;;IAIpDrE,EADF,CAAAC,cAAA,cAAuD,YAC9C;IAAAD,EAAA,CAAAG,MAAA,WAAI;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACnBJ,EAAA,CAAAC,cAAA,cAA0C;IAAAD,EAAA,CAAAG,MAAA,GAA2B;IACvEH,EADuE,CAAAI,YAAA,EAAM,EACvE;;;;IADsCJ,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA4D,IAAA,CAA2B;;;;;IAIrEtE,EADF,CAAAC,cAAA,cAA4D,YACnD;IAAAD,EAAA,CAAAG,MAAA,gBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACxBJ,EAAA,CAAAC,cAAA,cAA0C;IAAAD,EAAA,CAAAG,MAAA,GAAgC;IAC5EH,EAD4E,CAAAI,YAAA,EAAM,EAC5E;;;;IADsCJ,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA6D,SAAA,CAAgC;;;;;IAMtEvE,EADF,CAAAC,cAAA,cAA0D,YACjD;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvBJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAgC;IAC3DH,EAD2D,CAAAI,YAAA,EAAM,EAC3D;;;;IADqBJ,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAI,SAAA,CAAgC;;;;;IAGzDd,EADF,CAAAC,cAAA,cAAwF,YAC/E;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvBJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAkF;;IAC7GH,EAD6G,CAAAI,YAAA,EAAM,EAC7G;;;;IADqBJ,EAAA,CAAAK,SAAA,GAAkF;IAAlFL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAAe,WAAA,OAAAR,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA8D,SAAA,IAAAjE,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA+D,SAAA,gBAAkF;;;;;IAU/GzE,EADF,CAAAC,cAAA,cAAuH,YAC9G;IAAAD,EAAA,CAAAG,MAAA,uBAAgB;IAAAH,EAAA,CAAAC,cAAA,eAAuB;IAAAD,EAAA,CAAAG,MAAA,QAAC;IAAOH,EAAP,CAAAI,YAAA,EAAO,EAAQ;IAC9DJ,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAG,MAAA,GAA0F;IACxHH,EADwH,CAAAI,YAAA,EAAM,EACxH;;;;IADwBJ,EAAA,CAAAK,SAAA,GAA0F;IAA1FL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAgE,UAAA,IAAAnE,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAiE,QAAA,IAAApE,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAmD,OAAA,CAA0F;;;;;IAItH7D,EADF,CAAAC,cAAA,cAAkE,YACzD;IAAAD,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC9BJ,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAG,MAAA,GAAqC;IACnEH,EADmE,CAAAI,YAAA,EAAM,EACnE;;;;IADwBJ,EAAA,CAAAK,SAAA,GAAqC;IAArCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAkE,cAAA,CAAqC;;;;;IAnDnE5E,EAFJ,CAAAC,cAAA,cAAmE,cACtC,SACrB;IAAAD,EAAA,CAAAG,MAAA,qBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAsBvBJ,EApBA,CAAAuB,UAAA,IAAAsD,sDAAA,kBAA6E,IAAAC,sDAAA,kBAKf,IAAAC,sDAAA,kBAKP,IAAAC,sDAAA,kBAKA,IAAAC,sDAAA,kBAKK;IAM1DjF,EADF,CAAAC,cAAA,cAAwB,eACA;IAKpBD,EAJA,CAAAuB,UAAA,KAAA2D,uDAAA,kBAA0D,KAAAC,uDAAA,kBAI8B;IAM9FnF,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;IAGJJ,EADF,CAAAC,cAAA,eAA2B,UACrB;IAAAD,EAAA,CAAAG,MAAA,4BAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAO7BJ,EALA,CAAAuB,UAAA,KAAA6D,uDAAA,kBAAuH,KAAAC,uDAAA,kBAKrD;IAKtErF,EADE,CAAAI,YAAA,EAAM,EACF;;;;IApDwBJ,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAmB,UAAA,UAAAZ,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,KAAA,KAAAF,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAC,IAAA,CAAiD;IAKjDX,EAAA,CAAAK,SAAA,EAAkC;IAAlCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAG,WAAA,CAAkC;IAKlCb,EAAA,CAAAK,SAAA,EAA2B;IAA3BL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA2D,IAAA,CAA2B;IAK3BrE,EAAA,CAAAK,SAAA,EAA2B;IAA3BL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA4D,IAAA,CAA2B;IAK3BtE,EAAA,CAAAK,SAAA,EAAgC;IAAhCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA6D,SAAA,CAAgC;IAO9BvE,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAI,SAAA,CAAgC;IAIhCd,EAAA,CAAAK,SAAA,EAA8D;IAA9DL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA8D,SAAA,IAAAjE,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA+D,SAAA,CAA8D;IAW/DzE,EAAA,CAAAK,SAAA,GAA0F;IAA1FL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAgE,UAAA,IAAAnE,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAiE,QAAA,IAAApE,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAmD,OAAA,CAA0F;IAK1F7D,EAAA,CAAAK,SAAA,EAAqC;IAArCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAkE,cAAA,CAAqC;;;;;IAa9D5E,EADF,CAAAC,cAAA,cAA6E,YACpE;IAAAD,EAAA,CAAAG,MAAA,0BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAClCJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAiD;IAC5EH,EAD4E,CAAAI,YAAA,EAAM,EAC5E;;;;IADqBJ,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAC,IAAA,KAAAJ,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,KAAA,EAAiD;;;;;IAI1ET,EADF,CAAAC,cAAA,cAA8D,YACrD;IAAAD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC1BJ,EAAA,CAAAC,cAAA,cAA0C;IAAAD,EAAA,CAAAG,MAAA,GAAkC;IAC9EH,EAD8E,CAAAI,YAAA,EAAM,EAC9E;;;;IADsCJ,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAG,WAAA,CAAkC;;;;;IAMxEb,EADF,CAAAC,cAAA,cAA0D,YACjD;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvBJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAA+B;IAC1DH,EAD0D,CAAAI,YAAA,EAAM,EAC1D;;;;IADqBJ,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAI,SAAA,CAA+B;;;;;IAGxDd,EADF,CAAAC,cAAA,cAA0F,YACjF;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvBJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAoF;;IAC/GH,EAD+G,CAAAI,YAAA,EAAM,EAC/G;;;;IADqBJ,EAAA,CAAAK,SAAA,GAAoF;IAApFL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAAe,WAAA,OAAAR,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAO,WAAA,IAAAV,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAM,SAAA,gBAAoF;;;;;IAUjHhB,EADF,CAAAC,cAAA,cAAkE,YACzD;IAAAD,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAE5BJ,EADF,CAAAC,cAAA,cAA8B,WACtB;IAAAD,EAAA,CAAAG,MAAA,GAAqC;IAE/CH,EAF+C,CAAAI,YAAA,EAAO,EAC9C,EACF;;;;IAFIJ,EAAA,CAAAK,SAAA,GAAqC;IAArCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA4E,cAAA,CAAqC;;;;;IAK7CtF,EADF,CAAAC,cAAA,cAAkH,YACzG;IAAAD,EAAA,CAAAG,MAAA,iBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACzBJ,EAAA,CAAAE,SAAA,qBACsD;IACxDF,EAAA,CAAAI,YAAA,EAAM;;;;IAFQJ,EAAA,CAAAK,SAAA,GAAS;IACjBL,EADQ,CAAAmB,UAAA,UAAS,UAAU,cAAc,UAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA6E,SAAA,CACL;;;;;IAIxCvF,EADF,CAAAC,cAAA,cAA8D,YACrD;IAAAD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC1BJ,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAG,MAAA,GAAgC;IAC7DH,EAD6D,CAAAI,YAAA,EAAM,EAC7D;;;;IADuBJ,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA8E,UAAA,CAAgC;;;;;IAMzDxF,EAAA,CAAAC,cAAA,cAAkF;IAChFD,EAAA,CAAAE,SAAA,mBAA4F;IAC5FF,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAG,MAAA,GAAyD;IACnFH,EADmF,CAAAI,YAAA,EAAO,EACpF;;;;;;IAF2BJ,EAAA,CAAAK,SAAA,EAAiC;IAACL,EAAlC,CAAAmB,UAAA,cAAAZ,MAAA,CAAAkF,gBAAA,CAAAC,IAAA,EAAiC,gBAAgB;IACxD1F,EAAA,CAAAK,SAAA,GAAyD;IAAzDL,EAAA,CAAAM,iBAAA,CAAAqF,OAAA,CAAAC,QAAA,IAAAD,OAAA,CAAAhF,IAAA,0BAAyD;;;;;IAJrFX,EADF,CAAAC,cAAA,cAA8F,YACrF;IAAAD,EAAA,CAAAG,MAAA,qBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC7BJ,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAuB,UAAA,IAAAsE,6DAAA,kBAAkF;IAKtF7F,EADE,CAAAI,YAAA,EAAM,EACF;;;;IALoBJ,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAAmB,UAAA,YAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAoF,KAAA,CAA2B;;;;;IAlDrD9F,EAFJ,CAAAC,cAAA,cAAyE,cAC5C,SACrB;IAAAD,EAAA,CAAAG,MAAA,6BAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAO/BJ,EALA,CAAAuB,UAAA,IAAAwE,sDAAA,kBAA6E,IAAAC,sDAAA,kBAKf;IAM5DhG,EADF,CAAAC,cAAA,cAAwB,cACA;IAKpBD,EAJA,CAAAuB,UAAA,IAAA0E,sDAAA,kBAA0D,IAAAC,sDAAA,kBAIgC;IAMhGlG,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;IAGJJ,EADF,CAAAC,cAAA,eAA2B,UACrB;IAAAD,EAAA,CAAAG,MAAA,oCAA4B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAoBrCJ,EAlBA,CAAAuB,UAAA,KAAA4E,uDAAA,kBAAkE,KAAAC,uDAAA,kBAOgD,KAAAC,uDAAA,kBAMpD,KAAAC,uDAAA,kBAKgC;IAUlGtG,EADE,CAAAI,YAAA,EAAM,EACF;;;;IAvDwBJ,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAC,IAAA,KAAAJ,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,KAAA,EAAiD;IAKjDT,EAAA,CAAAK,SAAA,EAAkC;IAAlCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAG,WAAA,CAAkC;IAOhCb,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAI,SAAA,CAAgC;IAIhCd,EAAA,CAAAK,SAAA,EAAgE;IAAhEL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAO,WAAA,IAAAV,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAM,SAAA,CAAgE;IAWjEhB,EAAA,CAAAK,SAAA,GAAqC;IAArCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA4E,cAAA,CAAqC;IAOrCtF,EAAA,CAAAK,SAAA,EAAqF;IAArFL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA6E,SAAA,KAAAjC,SAAA,IAAA/C,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA6E,SAAA,UAAqF;IAMrFvF,EAAA,CAAAK,SAAA,EAAiC;IAAjCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA8E,UAAA,CAAiC;IAKjCxF,EAAA,CAAAK,SAAA,EAAiE;IAAjEL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAoF,KAAA,IAAAvF,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAoF,KAAA,CAAAS,MAAA,KAAiE;;;;;IAkB1FvG,EADF,CAAAC,cAAA,cAA6E,YACpE;IAAAD,EAAA,CAAAG,MAAA,qBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC7BJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAiD;IAC5EH,EAD4E,CAAAI,YAAA,EAAM,EAC5E;;;;IADqBJ,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAM,iBAAA,EAAAC,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,KAAA,KAAAF,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAC,IAAA,CAAiD;;;;;IAI1EX,EADF,CAAAC,cAAA,cAA8D,YACrD;IAAAD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC1BJ,EAAA,CAAAC,cAAA,cAA0C;IAAAD,EAAA,CAAAG,MAAA,GAAkC;IAC9EH,EAD8E,CAAAI,YAAA,EAAM,EAC9E;;;;IADsCJ,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAG,WAAA,CAAkC;;;;;IAMxEb,EADF,CAAAC,cAAA,cAA0D,YACjD;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvBJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAA+B;IAC1DH,EAD0D,CAAAI,YAAA,EAAM,EAC1D;;;;IADqBJ,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAI,SAAA,CAA+B;;;;;IAGxDd,EADF,CAAAC,cAAA,cAA0F,YACjF;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvBJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAoF;;IAC/GH,EAD+G,CAAAI,YAAA,EAAM,EAC/G;;;;IADqBJ,EAAA,CAAAK,SAAA,GAAoF;IAApFL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAAe,WAAA,OAAAR,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAO,WAAA,IAAAV,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAM,SAAA,gBAAoF;;;;;IAUjHhB,EADF,CAAAC,cAAA,cAA2D,YAClD;IAAAD,EAAA,CAAAG,MAAA,0BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAClCJ,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAG,MAAA,GAA8B;IAC1DH,EAD0D,CAAAI,YAAA,EAAM,EAC1D;;;;IADsBJ,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAmD,OAAA,CAA8B;;;;;IAGxD7D,EADD,CAAAC,cAAA,cAA+D,YACvD;IAAAD,EAAA,CAAAG,MAAA,uBAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC/BJ,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAG,MAAA,GAAkC;IAC9DH,EAD8D,CAAAI,YAAA,EAAM,EAC9D;;;;IADsBJ,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA8F,WAAA,CAAkC;;;;;IAR9DxG,EADF,CAAAC,cAAA,cAA8F,SACxF;IAAAD,EAAA,CAAAG,MAAA,8BAAuB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAM/BJ,EAJD,CAAAuB,UAAA,IAAAkF,6DAAA,kBAA2D,IAAAC,6DAAA,kBAIK;IAIlE1G,EAAA,CAAAI,YAAA,EAAM;;;;IARuBJ,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAmD,OAAA,CAA8B;IAI7B7D,EAAA,CAAAK,SAAA,EAAkC;IAAlCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA8F,WAAA,CAAkC;;;;;IAjC9DxG,EAFJ,CAAAC,cAAA,cAAyE,cAC5C,SACrB;IAAAD,EAAA,CAAAG,MAAA,wBAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAO1BJ,EALA,CAAAuB,UAAA,IAAAoF,sDAAA,kBAA6E,IAAAC,sDAAA,kBAKf;IAM5D5G,EADF,CAAAC,cAAA,cAAwB,cACA;IAKpBD,EAJA,CAAAuB,UAAA,IAAAsF,sDAAA,kBAA0D,IAAAC,sDAAA,kBAIgC;IAMhG9G,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;IAENJ,EAAA,CAAAuB,UAAA,KAAAwF,uDAAA,kBAA8F;IAYhG/G,EAAA,CAAAI,YAAA,EAAM;;;;IApCwBJ,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAmB,UAAA,UAAAZ,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,KAAA,KAAAF,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAC,IAAA,CAAiD;IAKjDX,EAAA,CAAAK,SAAA,EAAkC;IAAlCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAG,WAAA,CAAkC;IAOhCb,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAI,SAAA,CAAgC;IAIhCd,EAAA,CAAAK,SAAA,EAAgE;IAAhEL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAO,WAAA,IAAAV,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAM,SAAA,CAAgE;IAQjEhB,EAAA,CAAAK,SAAA,EAA8D;IAA9DL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA8F,WAAA,IAAAjG,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAmD,OAAA,CAA8D;;;;;IA/T/F7D,EAAA,CAAAC,cAAA,UAAwD;IAmStDD,EAjSA,CAAAuB,UAAA,IAAAyF,gDAAA,oBAAiE,IAAAC,gDAAA,mBA6HF,IAAAC,gDAAA,mBA2CI,IAAAC,gDAAA,mBA2DM,IAAAC,gDAAA,mBA8DA;IAyC3EpH,EAAA,CAAAI,YAAA,EAAM;;;;IA1UEJ,EAAA,CAAAK,SAAA,EAAmC;IAAnCL,EAAA,CAAAmB,UAAA,UAAAZ,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAA6G,IAAA,cAAmC;IA6HnCrH,EAAA,CAAAK,SAAA,EAAkC;IAAlCL,EAAA,CAAAmB,UAAA,UAAAZ,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAA6G,IAAA,aAAkC;IA2ClCrH,EAAA,CAAAK,SAAA,EAAoC;IAApCL,EAAA,CAAAmB,UAAA,UAAAZ,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAA6G,IAAA,eAAoC;IA2DpCrH,EAAA,CAAAK,SAAA,EAAuC;IAAvCL,EAAA,CAAAmB,UAAA,UAAAZ,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAA6G,IAAA,kBAAuC;IA8DvCrH,EAAA,CAAAK,SAAA,EAAuC;IAAvCL,EAAA,CAAAmB,UAAA,UAAAZ,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAA6G,IAAA,kBAAuC;;;;;IA6C7CrH,EADF,CAAAC,cAAA,cAAsD,QACjD;IAAAD,EAAA,CAAAG,MAAA,GAAuB;IAC5BH,EAD4B,CAAAI,YAAA,EAAI,EAC1B;;;;IADDJ,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAA8G,KAAA,CAAuB;;;ADnV9B,WAAaC,2BAA2B;EAAlC,MAAOA,2BAA2B;IAC7B/G,WAAW,GAAQ,IAAI;IACvBgH,YAAY;IACZC,QAAQ;IACRC,cAAc;IACdC,eAAe;IACfC,YAAY;IACdC,MAAM,GAAQ9H,YAAY,CAAC8H,MAAM;IAExCrE,cAAc,GAAG,KAAK;IAEtBT,mBAAmBA,CAAA;MACjB,IAAI,CAACS,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC5C;IAEAsE,aAAaA,CAACC,KAAU;MACtB,IAAI,CAACN,QAAQ,EAAE;IACjB;IAEAO,UAAUA,CAAA;MACR,IAAI,CAACJ,YAAY,EAAE;IACrB;IAEAK,cAAcA,CAAA;MACZ,IAAI,CAACT,YAAY,EAAE;MACnB,IAAI,CAACE,cAAc,EAAE;IACvB;IAEAQ,aAAaA,CAAA;MACX,IAAI,CAACV,YAAY,EAAE;MACnB,IAAI,CAACG,eAAe,EAAE;IACxB;IAEAQ,mBAAmBA,CAACzH,IAAS;MAC3B,MAAM0H,aAAa,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,cAAc,EAAE,YAAY,CAAC;MAC9F,OAAOC,MAAM,CAACC,IAAI,CAAC5H,IAAI,CAAC,CACrB6H,MAAM,CAACC,GAAG,IAAI,CAACJ,aAAa,CAACK,QAAQ,CAACD,GAAG,CAAC,IAAI9H,IAAI,CAAC8H,GAAG,CAAC,IAAI,IAAI,CAAC,CAChEE,GAAG,CAACF,GAAG,KAAK;QAAEA,GAAG;QAAEG,KAAK,EAAEjI,IAAI,CAAC8H,GAAG;MAAC,CAAE,CAAC,CAAC;IAC5C;IAEA/C,gBAAgBA,CAACmD,KAAa;MAC5B,MAAMC,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MACtE,OAAOA,MAAM,CAACD,KAAK,GAAGC,MAAM,CAACtC,MAAM,CAAC;IACtC;IAEAuC,cAAcA,CAAA;MACZ,OAAO,MAAM;IACf;;uCA/CWvB,2BAA2B;IAAA;;YAA3BA,2BAA2B;MAAAwB,SAAA;MAAAC,MAAA;QAAAxI,WAAA;QAAAgH,YAAA;QAAAC,QAAA;QAAAC,cAAA;QAAAC,eAAA;QAAAC,YAAA;MAAA;MAAAqB,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAxE,QAAA,WAAAyE,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXtCrJ,EADF,CAAAC,cAAA,aAA2B,aACsB;UAAzBD,EAAA,CAAAyC,UAAA,mBAAA8G,0DAAA;YAAA,OAASD,GAAA,CAAA9B,YAAA,EAAc;UAAA,EAAC;UAACxH,EAAA,CAAAI,YAAA,EAAM;UACvDJ,EAAA,CAAAC,cAAA,2BAAgG;UAAnCD,EAAA,CAAAyC,UAAA,mBAAA+G,wEAAAC,MAAA;YAAA,OAASA,MAAA,CAAAC,eAAA,EAAwB;UAAA,EAAC;UAE7F1J,EADF,CAAAC,cAAA,aAAyC,cACb;UAAAD,EAAA,CAAAG,MAAA,2BAAoB;UAAAH,EAAA,CAAAI,YAAA,EAAO;UACrDJ,EAAA,CAAAC,cAAA,kBAAoF;UAAzBD,EAAA,CAAAyC,UAAA,mBAAAkH,+DAAA;YAAA,OAASL,GAAA,CAAA9B,YAAA,EAAc;UAAA,EAAC;UACrFxH,EADsF,CAAAI,YAAA,EAAW,EAC3F;UACNJ,EAAA,CAAAC,cAAA,aAA2C;UAuVzCD,EArVA,CAAAuB,UAAA,IAAAqI,0CAAA,iBAA0D,IAAAC,0CAAA,iBAMF,KAAAC,2CAAA,iBA+UF;UAGxD9J,EAAA,CAAAI,YAAA,EAAM;UAGFJ,EAFH,CAAAC,cAAA,eAAkB,eACe,sBAG7B;UAFqED,EAAA,CAAAyC,UAAA,uBAAAsH,sEAAAN,MAAA;YAAA,OAAaH,GAAA,CAAAxB,aAAA,CAAA2B,MAAA,CAAqB;UAAA,EAAC;UAExGzJ,EAAA,CAAAI,YAAA,EAAa;UACdJ,EAAA,CAAAC,cAAA,sBAM2D;UANzBD,EAAA,CAAAyC,UAAA,uBAAAuH,sEAAA;YAAA,OAAaV,GAAA,CAAAtB,UAAA,EAAY;UAAA,EAAC;UAMDhI,EAAA,CAAAI,YAAA,EAAa;UACxEJ,EAAA,CAAAC,cAAA,sBAOgE;UAP1BD,EAAA,CAAAyC,UAAA,uBAAAwH,sEAAA;YAAA,OAAaX,GAAA,CAAArB,cAAA,EAAgB;UAAA,EAAC;UAOJjI,EAAA,CAAAI,YAAA,EAAa;UAC7EJ,EAAA,CAAAC,cAAA,sBAK4D;UALvBD,EAAA,CAAAyC,UAAA,uBAAAyH,sEAAA;YAAA,OAAaZ,GAAA,CAAApB,aAAA,EAAe;UAAA,EAAC;UAStElI,EAJgE,CAAAI,YAAA,EAAa,EACrE,EACF,EACc,EACd;;;UA7XqCJ,EAAA,CAAAK,SAAA,GAAiB;UAAjBL,EAAA,CAAAmB,UAAA,kBAAiB;UAOpDnB,EAAA,CAAAK,SAAA,GAA0B;UAA1BL,EAAA,CAAAmB,UAAA,SAAAmI,GAAA,CAAA9I,WAAA,kBAAA8I,GAAA,CAAA9I,WAAA,CAAA2J,OAAA,CAA0B;UAM1BnK,EAAA,CAAAK,SAAA,EAAgD;UAAhDL,EAAA,CAAAmB,UAAA,UAAAmI,GAAA,CAAA9I,WAAA,kBAAA8I,GAAA,CAAA9I,WAAA,CAAAE,IAAA,OAAA4I,GAAA,CAAA9I,WAAA,kBAAA8I,GAAA,CAAA9I,WAAA,CAAA2J,OAAA,EAAgD;UA+UhDnK,EAAA,CAAAK,SAAA,EAAwB;UAAxBL,EAAA,CAAAmB,UAAA,SAAAmI,GAAA,CAAA9I,WAAA,kBAAA8I,GAAA,CAAA9I,WAAA,CAAA8G,KAAA,CAAwB;UAMhBtH,EAAA,CAAAK,SAAA,GAA0B;UAA1BL,EAAA,CAAAmB,UAAA,UAAAmI,GAAA,CAAAR,cAAA,GAA0B;UAG1B9I,EAAA,CAAAK,SAAA,EAAqB;UAA6DL,EAAlF,CAAAmB,UAAA,UAAAmI,GAAA,CAAAzB,MAAA,CAAAuC,IAAA,CAAqB,iBAAApK,EAAA,CAAAqK,eAAA,KAAAC,GAAA,EAM3B;UACMtK,EAAA,CAAAK,SAAA,EAAyB;UACnCL,EADU,CAAAmB,UAAA,UAAAmI,GAAA,CAAAzB,MAAA,CAAA0C,QAAA,CAAyB,iBAAAvK,EAAA,CAAAqK,eAAA,KAAAC,GAAA,EAO/B;UACMtK,EAAA,CAAAK,SAAA,EAAwB;UAClCL,EADU,CAAAmB,UAAA,UAAAmI,GAAA,CAAAzB,MAAA,CAAA2C,OAAA,CAAwB,iBAAAxK,EAAA,CAAAqK,eAAA,KAAAI,GAAA,EAK9B;;;qBDnXE3K,qBAAqB,EAACF,aAAa,EAACD,eAAe,EAACF,QAAQ,EAAED,YAAY,EAAAkL,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAC/K,eAAe,EAACH,mBAAmB;MAAAmL,MAAA;IAAA;;SAI7GtD,2BAA2B;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}