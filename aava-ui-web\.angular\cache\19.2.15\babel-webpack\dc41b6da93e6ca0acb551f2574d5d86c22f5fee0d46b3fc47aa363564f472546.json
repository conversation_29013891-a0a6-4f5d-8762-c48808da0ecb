{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nconst _c0 = [\"nodeTemplate\"];\nfunction DropZoneCanvasComponent_div_36_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵtext(1, \" Drop a prompt here \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DropZoneCanvasComponent_div_36_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"span\", 51);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function DropZoneCanvasComponent_div_36_div_3_Template_button_click_3_listener() {\n      const node_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onDeleteNode(node_r2.id));\n    });\n    i0.ɵɵtext(4, \" \\u00D7 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((node_r2.data == null ? null : node_r2.data.name) || \"Prompt\");\n  }\n}\nfunction DropZoneCanvasComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtemplate(1, DropZoneCanvasComponent_div_36_div_1_Template, 2, 0, \"div\", 46);\n    i0.ɵɵelementStart(2, \"div\", 47);\n    i0.ɵɵtemplate(3, DropZoneCanvasComponent_div_36_div_3_Template, 5, 1, \"div\", 48);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.promptNodes.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.promptNodes)(\"ngForTrackBy\", ctx_r2.trackByNodeId);\n  }\n}\nfunction DropZoneCanvasComponent_div_57_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵtext(1, \" Drop knowledge here \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DropZoneCanvasComponent_div_57_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"span\", 51);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function DropZoneCanvasComponent_div_57_div_3_Template_button_click_3_listener() {\n      const node_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onDeleteNode(node_r5.id));\n    });\n    i0.ɵɵtext(4, \" \\u00D7 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((node_r5.data == null ? null : node_r5.data.name) || \"Knowledge\");\n  }\n}\nfunction DropZoneCanvasComponent_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtemplate(1, DropZoneCanvasComponent_div_57_div_1_Template, 2, 0, \"div\", 46);\n    i0.ɵɵelementStart(2, \"div\", 47);\n    i0.ɵɵtemplate(3, DropZoneCanvasComponent_div_57_div_3_Template, 5, 1, \"div\", 48);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.knowledgeNodes.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.knowledgeNodes)(\"ngForTrackBy\", ctx_r2.trackByNodeId);\n  }\n}\nfunction DropZoneCanvasComponent__svg_svg_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 18);\n    i0.ɵɵelement(1, \"rect\", 53)(2, \"path\", 54)(3, \"path\", 55)(4, \"path\", 56);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DropZoneCanvasComponent_div_72_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵtext(1, \" Drop a model here \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DropZoneCanvasComponent_div_72_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"span\", 51);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function DropZoneCanvasComponent_div_72_div_3_Template_button_click_3_listener() {\n      const node_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onDeleteNode(node_r7.id));\n    });\n    i0.ɵɵtext(4, \" \\u00D7 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((node_r7.data == null ? null : node_r7.data.name) || \"Model\");\n  }\n}\nfunction DropZoneCanvasComponent_div_72_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtemplate(1, DropZoneCanvasComponent_div_72_div_1_Template, 2, 0, \"div\", 46);\n    i0.ɵɵelementStart(2, \"div\", 47);\n    i0.ɵɵtemplate(3, DropZoneCanvasComponent_div_72_div_3_Template, 5, 1, \"div\", 48);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.modelNodes.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.modelNodes)(\"ngForTrackBy\", ctx_r2.trackByNodeId);\n  }\n}\nfunction DropZoneCanvasComponent__svg_svg_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 18);\n    i0.ɵɵelement(1, \"rect\", 57)(2, \"path\", 58);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DropZoneCanvasComponent__svg_svg_78_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 18);\n    i0.ɵɵelement(1, \"rect\", 59)(2, \"path\", 60);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DropZoneCanvasComponent_div_87_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" Drop \", ctx_r2.config.agentType === \"individual\" ? \"guardrails\" : \"tools\", \" here \");\n  }\n}\nfunction DropZoneCanvasComponent_div_87_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"span\", 51);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function DropZoneCanvasComponent_div_87_div_3_Template_button_click_3_listener() {\n      const node_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onDeleteNode(node_r9.id));\n    });\n    i0.ɵɵtext(4, \" \\u00D7 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r9 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((node_r9.data == null ? null : node_r9.data.name) || (ctx_r2.config.agentType === \"individual\" ? \"Guardrail\" : \"Tool\"));\n  }\n}\nfunction DropZoneCanvasComponent_div_87_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtemplate(1, DropZoneCanvasComponent_div_87_div_1_Template, 2, 1, \"div\", 46);\n    i0.ɵɵelementStart(2, \"div\", 47);\n    i0.ɵɵtemplate(3, DropZoneCanvasComponent_div_87_div_3_Template, 5, 1, \"div\", 48);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.bottomNodes.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.bottomNodes)(\"ngForTrackBy\", ctx_r2.trackByNodeId);\n  }\n}\nexport let DropZoneCanvasComponent = /*#__PURE__*/(() => {\n  class DropZoneCanvasComponent {\n    nodeTemplate;\n    config;\n    nodes = [];\n    nodeDropped = new EventEmitter();\n    nodeDeleted = new EventEmitter();\n    // Accordion state management\n    zoneStates = {\n      prompt: true,\n      // expanded by default\n      model: true,\n      // expanded by default\n      knowledge: true,\n      // expanded by default\n      guardrail: true,\n      // expanded by default\n      tool: true // expanded by default\n    };\n    // Computed properties for categorized nodes\n    get promptNodes() {\n      return this.nodes.filter(node => node.data?.type === 'prompt');\n    }\n    get modelNodes() {\n      return this.nodes.filter(node => node.data?.type === 'model');\n    }\n    get knowledgeNodes() {\n      return this.nodes.filter(node => node.data?.type === 'knowledge');\n    }\n    get bottomNodes() {\n      if (this.config.agentType === 'individual') {\n        return this.nodes.filter(node => node.data?.type === 'guardrail');\n      } else {\n        return this.nodes.filter(node => node.data?.type === 'tool');\n      }\n    }\n    // Progress calculation\n    get completionPercentage() {\n      let completed = 0;\n      let total = 2; // Only 2 required: prompt and model\n      // Check required components only\n      if (this.promptNodes.length > 0) completed++;\n      if (this.modelNodes.length > 0) completed++;\n      // Knowledge, Guardrails, and Tools are now optional (don't count toward completion)\n      return Math.round(completed / total * 100);\n    }\n    onDragOver(event, zoneType) {\n      event.preventDefault();\n      // Try both possible drag data formats\n      const dragData = event.dataTransfer?.getData('application/reactflow') || event.dataTransfer?.getData('text/plain');\n      if (dragData) {\n        try {\n          const data = JSON.parse(dragData);\n          // Check if the dragged item is compatible with this zone\n          if (this.isCompatibleWithZone(data, zoneType)) {\n            event.dataTransfer.dropEffect = 'copy';\n          } else {\n            event.dataTransfer.dropEffect = 'none';\n          }\n        } catch (e) {\n          event.dataTransfer.dropEffect = 'none';\n        }\n      }\n    }\n    onDrop(event, zoneType) {\n      event.preventDefault();\n      // Try both possible drag data formats\n      const dragData = event.dataTransfer?.getData('application/reactflow') || event.dataTransfer?.getData('text/plain');\n      if (dragData) {\n        try {\n          const data = JSON.parse(dragData);\n          console.log('🎯 Drop zone received data:', {\n            data,\n            zoneType\n          });\n          // Check compatibility\n          if (!this.isCompatibleWithZone(data, zoneType)) {\n            console.log('Incompatible node type for this zone:', data.type, 'vs', zoneType);\n            return; // Bounce back\n          }\n          console.log('✅ Compatible drop detected');\n          // Check limits\n          if (!this.canAddToZone(zoneType)) {\n            console.log('Zone limit reached, replacing existing node');\n            // For single-node zones, we'll replace the existing node\n          }\n          // Calculate position within the zone\n          const rect = event.currentTarget.getBoundingClientRect();\n          const position = {\n            x: event.clientX - rect.left,\n            y: event.clientY - rect.top\n          };\n          console.log('✅ Emitting nodeDropped event');\n          this.nodeDropped.emit({\n            node: data,\n            zone: zoneType,\n            position\n          });\n        } catch (e) {\n          console.error('Invalid drag data:', e);\n        }\n      }\n    }\n    onDeleteNode(nodeId) {\n      this.nodeDeleted.emit(nodeId);\n    }\n    trackByNodeId(index, node) {\n      return node.id;\n    }\n    // Accordion toggle functionality\n    toggleZone(zoneType) {\n      if (zoneType === 'guardrail' || zoneType === 'tool') {\n        // Handle both guardrail and tool zones\n        const actualType = this.config.agentType === 'individual' ? 'guardrail' : 'tool';\n        this.zoneStates[actualType] = !this.zoneStates[actualType];\n      } else {\n        this.zoneStates[zoneType] = !this.zoneStates[zoneType];\n      }\n    }\n    // Get zone state for template\n    isZoneExpanded(zoneType) {\n      if (zoneType === 'guardrail' || zoneType === 'tool') {\n        const actualType = this.config.agentType === 'individual' ? 'guardrail' : 'tool';\n        return this.zoneStates[actualType];\n      }\n      return this.zoneStates[zoneType];\n    }\n    isCompatibleWithZone(dragData, zoneType) {\n      const nodeType = dragData.type || dragData.data?.type;\n      switch (zoneType) {\n        case 'prompt':\n          return nodeType === 'prompt';\n        case 'model':\n          return nodeType === 'model';\n        case 'knowledge':\n          return nodeType === 'knowledge';\n        case 'guardrail':\n          return nodeType === 'guardrail';\n        case 'tool':\n          return nodeType === 'tool';\n        default:\n          return false;\n      }\n    }\n    canAddToZone(zoneType) {\n      switch (zoneType) {\n        case 'prompt':\n          return this.promptNodes.length < this.config.allowedCategories.prompts.max;\n        case 'model':\n          return this.modelNodes.length < this.config.allowedCategories.models.max;\n        case 'knowledge':\n          return this.knowledgeNodes.length < this.config.allowedCategories.knowledge.max;\n        case 'guardrail':\n          return this.bottomNodes.length < this.config.allowedCategories.guardrails.max;\n        case 'tool':\n          return this.bottomNodes.length < this.config.allowedCategories.tools.max;\n        default:\n          return false;\n      }\n    }\n    static ɵfac = function DropZoneCanvasComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DropZoneCanvasComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DropZoneCanvasComponent,\n      selectors: [[\"app-drop-zone-canvas\"]],\n      contentQueries: function DropZoneCanvasComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, _c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.nodeTemplate = _t.first);\n        }\n      },\n      inputs: {\n        config: \"config\",\n        nodes: \"nodes\"\n      },\n      outputs: {\n        nodeDropped: \"nodeDropped\",\n        nodeDeleted: \"nodeDeleted\"\n      },\n      decls: 88,\n      vars: 41,\n      consts: [[1, \"drop-zone-canvas-container\"], [1, \"central-progress\"], [1, \"progress-ring\"], [\"width\", \"120\", \"height\", \"120\", 1, \"progress-circle\"], [\"id\", \"progressGradient\", \"x1\", \"0%\", \"y1\", \"0%\", \"x2\", \"100%\", \"y2\", \"100%\"], [\"offset\", \"33.91%\", 2, \"stop-color\", \"#0084ff\"], [\"offset\", \"100%\", 2, \"stop-color\", \"#03bdd4\"], [\"cx\", \"60\", \"cy\", \"60\", \"r\", \"50\", \"fill\", \"none\", \"stroke\", \"#e5e7eb\", \"stroke-width\", \"8\", 1, \"progress-background\"], [\"cx\", \"60\", \"cy\", \"60\", \"r\", \"50\", \"fill\", \"none\", \"stroke\", \"url(#progressGradient)\", \"stroke-width\", \"8\", \"stroke-linecap\", \"round\", \"transform\", \"rotate(180 60 60)\", 1, \"progress-bar\"], [1, \"progress-content\"], [1, \"progress-percentage\"], [1, \"progress-label\"], [\"id\", \"parent-box\"], [\"id\", \"box1\"], [1, \"drop-zone\", \"north-zone\", \"prompts-zone\", 3, \"dragover\", \"drop\"], [1, \"zone-header\", 3, \"click\"], [1, \"header-content\"], [1, \"header-icon\"], [\"width\", \"45\", \"height\", \"44\", \"viewBox\", \"0 0 45 44\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"x\", \"0.679688\", \"width\", \"44\", \"height\", \"44\", \"rx\", \"8\", \"fill\", \"#5082EF\"], [\"d\", \"M20.6166 25.5C20.5273 25.1539 20.347 24.8381 20.0942 24.5854C19.8415 24.3327 19.5257 24.1523 19.1796 24.063L13.0446 22.481C12.94 22.4513 12.8478 22.3883 12.7822 22.3014C12.7166 22.2146 12.6812 22.1088 12.6812 22C12.6812 21.8912 12.7166 21.7854 12.7822 21.6986C12.8478 21.6118 12.94 21.5487 13.0446 21.519L19.1796 19.936C19.5256 19.8468 19.8413 19.6666 20.094 19.414C20.3467 19.1615 20.5272 18.8459 20.6166 18.5L22.1986 12.365C22.228 12.2599 22.291 12.1673 22.3779 12.1014C22.4649 12.0355 22.571 11.9998 22.6801 11.9998C22.7892 11.9998 22.8954 12.0355 22.9823 12.1014C23.0692 12.1673 23.1322 12.2599 23.1616 12.365L24.7426 18.5C24.8319 18.8461 25.0123 19.1619 25.265 19.4146C25.5177 19.6673 25.8336 19.8477 26.1796 19.937L32.3146 21.518C32.4201 21.5471 32.5132 21.61 32.5795 21.6971C32.6458 21.7841 32.6817 21.8906 32.6817 22C32.6817 22.1094 32.6458 22.2159 32.5795 22.3029C32.5132 22.39 32.4201 22.4529 32.3146 22.482L26.1796 24.063C25.8336 24.1523 25.5177 24.3327 25.265 24.5854C25.0123 24.8381 24.8319 25.1539 24.7426 25.5L23.1606 31.635C23.1312 31.7401 23.0682 31.8327 22.9813 31.8986C22.8944 31.9646 22.7882 32.0003 22.6791 32.0003C22.57 32.0003 22.4639 31.9646 22.3769 31.8986C22.29 31.8327 22.227 31.7401 22.1976 31.635L20.6166 25.5Z\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M30.6797 13V17\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M32.6797 15H28.6797\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M14.6797 27V29\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M15.6797 28H13.6797\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"zone-title\"], [1, \"header-actions\"], [1, \"required-badge\"], [\"type\", \"button\", 1, \"accordion-toggle\", 3, \"click\"], [\"width\", \"16\", \"height\", \"16\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\"], [\"d\", \"M6 9L12 15L18 9\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"class\", \"zone-content\", 4, \"ngIf\"], [1, \"drop-zone\", \"west-zone\", \"knowledge-zone\", 3, \"dragover\", \"drop\"], [\"x\", \"0.679688\", \"width\", \"44\", \"height\", \"44\", \"rx\", \"8\", \"fill\", \"#308666\"], [\"d\", \"M22.6797 17V31\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M26.6797 22H28.6797\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M26.6797 18H28.6797\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M13.6797 28C13.4145 28 13.1601 27.8946 12.9726 27.7071C12.785 27.5196 12.6797 27.2652 12.6797 27V14C12.6797 13.7348 12.785 13.4804 12.9726 13.2929C13.1601 13.1054 13.4145 13 13.6797 13H18.6797C19.7406 13 20.758 13.4214 21.5081 14.1716C22.2583 14.9217 22.6797 15.9391 22.6797 17C22.6797 15.9391 23.1011 14.9217 23.8513 14.1716C24.6014 13.4214 25.6188 13 26.6797 13H31.6797C31.9449 13 32.1993 13.1054 32.3868 13.2929C32.5743 13.4804 32.6797 13.7348 32.6797 14V27C32.6797 27.2652 32.5743 27.5196 32.3868 27.7071C32.1993 27.8946 31.9449 28 31.6797 28H25.6797C24.884 28 24.121 28.3161 23.5584 28.8787C22.9958 29.4413 22.6797 30.2044 22.6797 31C22.6797 30.2044 22.3636 29.4413 21.801 28.8787C21.2384 28.3161 20.4753 28 19.6797 28H13.6797Z\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M16.6797 22H18.6797\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M16.6797 18H18.6797\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"optional-badge\"], [\"id\", \"box2\"], [1, \"drop-zone\", \"east-zone\", \"models-zone\", 3, \"dragover\", \"drop\"], [\"width\", \"45\", \"height\", \"44\", \"viewBox\", \"0 0 45 44\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\", 4, \"ngIf\"], [1, \"drop-zone\", \"south-zone\", 3, \"dragover\", \"drop\"], [1, \"zone-content\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [1, \"nodes-list\"], [\"class\", \"kanban-card\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"empty-state\"], [1, \"kanban-card\"], [1, \"card-title\"], [1, \"delete-btn\", 3, \"click\"], [\"x\", \"0.679688\", \"width\", \"44\", \"height\", \"44\", \"rx\", \"8\", \"fill\", \"#997BCF\"], [\"d\", \"M31.6797 18C31.6793 17.6493 31.5867 17.3048 31.4112 17.0012C31.2357 16.6975 30.9834 16.4454 30.6797 16.27L23.6797 12.27C23.3756 12.0945 23.0308 12.0021 22.6797 12.0021C22.3286 12.0021 21.9837 12.0945 21.6797 12.27L14.6797 16.27C14.376 16.4454 14.1237 16.6975 13.9481 17.0012C13.7726 17.3048 13.68 17.6493 13.6797 18V26C13.68 26.3508 13.7726 26.6952 13.9481 26.9989C14.1237 27.3025 14.376 27.5547 14.6797 27.73L21.6797 31.73C21.9837 31.9056 22.3286 31.998 22.6797 31.998C23.0308 31.998 23.3756 31.9056 23.6797 31.73L30.6797 27.73C30.9834 27.5547 31.2357 27.3025 31.4112 26.9989C31.5867 26.6952 31.6793 26.3508 31.6797 26V18Z\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M13.9795 17L22.6795 22L31.3795 17\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M22.6797 32V22\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"x\", \"0.679688\", \"width\", \"44\", \"height\", \"44\", \"rx\", \"8\", \"fill\", \"#DC2626\"], [\"d\", \"M30.6797 23C30.6797 28 27.1797 30.5 23.0197 31.95C22.8019 32.0238 22.5652 32.0202 22.3497 31.94C18.1797 30.5 14.6797 28 14.6797 23V16C14.6797 15.7347 14.785 15.4804 14.9726 15.2929C15.1601 15.1053 15.4145 15 15.6797 15C17.6797 15 20.1797 13.8 21.9197 12.28C22.1315 12.099 22.401 11.9995 22.6797 11.9995C22.9583 11.9995 23.2278 12.099 23.4397 12.28C25.1897 13.81 27.6797 15 29.6797 15C29.9449 15 30.1993 15.1053 30.3868 15.2929C30.5743 15.4804 30.6797 15.7347 30.6797 16V23Z\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"x\", \"0.679688\", \"width\", \"44\", \"height\", \"44\", \"rx\", \"8\", \"fill\", \"#D97706\"], [\"d\", \"M25.3795 16.3C25.1963 16.4869 25.0937 16.7382 25.0937 17C25.0937 17.2617 25.1963 17.5131 25.3795 17.7L26.9795 19.3C27.1665 19.4832 27.4178 19.5858 27.6795 19.5858C27.9413 19.5858 28.1926 19.4832 28.3795 19.3L32.1495 15.53C32.6524 16.6412 32.8046 17.8792 32.586 19.0791C32.3674 20.279 31.7883 21.3838 30.9258 22.2463C30.0634 23.1087 28.9586 23.6878 27.7587 23.9064C26.5588 24.1251 25.3207 23.9728 24.2095 23.47L17.2995 30.38C16.9017 30.7778 16.3622 31.0013 15.7995 31.0013C15.2369 31.0013 14.6974 30.7778 14.2995 30.38C13.9017 29.9822 13.6782 29.4426 13.6782 28.88C13.6782 28.3174 13.9017 27.7778 14.2995 27.38L21.2095 20.47C20.7067 19.3588 20.5545 18.1207 20.7731 16.9208C20.9917 15.7209 21.5708 14.6161 22.4333 13.7537C23.2957 12.8913 24.4005 12.3121 25.6004 12.0935C26.8003 11.8749 28.0384 12.0271 29.1495 12.53L25.3895 16.29L25.3795 16.3Z\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"]],\n      template: function DropZoneCanvasComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(3, \"svg\", 3)(4, \"defs\")(5, \"linearGradient\", 4);\n          i0.ɵɵelement(6, \"stop\", 5)(7, \"stop\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(8, \"circle\", 7)(9, \"circle\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(10, \"div\", 9)(11, \"div\", 10);\n          i0.ɵɵtext(12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 11);\n          i0.ɵɵtext(14, \"Complete\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(15, \"div\", 12)(16, \"div\", 13)(17, \"div\", 14);\n          i0.ɵɵlistener(\"dragover\", function DropZoneCanvasComponent_Template_div_dragover_17_listener($event) {\n            return ctx.onDragOver($event, \"prompt\");\n          })(\"drop\", function DropZoneCanvasComponent_Template_div_drop_17_listener($event) {\n            return ctx.onDrop($event, \"prompt\");\n          });\n          i0.ɵɵelementStart(18, \"div\", 15);\n          i0.ɵɵlistener(\"click\", function DropZoneCanvasComponent_Template_div_click_18_listener() {\n            return ctx.toggleZone(\"prompt\");\n          });\n          i0.ɵɵelementStart(19, \"div\", 16)(20, \"div\", 17);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(21, \"svg\", 18);\n          i0.ɵɵelement(22, \"rect\", 19)(23, \"path\", 20)(24, \"path\", 21)(25, \"path\", 22)(26, \"path\", 23)(27, \"path\", 24);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(28, \"h3\", 25);\n          i0.ɵɵtext(29, \"System Prompt\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"div\", 26)(31, \"span\", 27);\n          i0.ɵɵtext(32, \"Required\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"button\", 28);\n          i0.ɵɵlistener(\"click\", function DropZoneCanvasComponent_Template_button_click_33_listener($event) {\n            ctx.toggleZone(\"prompt\");\n            return $event.stopPropagation();\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(34, \"svg\", 29);\n          i0.ɵɵelement(35, \"path\", 30);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(36, DropZoneCanvasComponent_div_36_Template, 4, 3, \"div\", 31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(37, \"div\", 32);\n          i0.ɵɵlistener(\"dragover\", function DropZoneCanvasComponent_Template_div_dragover_37_listener($event) {\n            return ctx.onDragOver($event, \"knowledge\");\n          })(\"drop\", function DropZoneCanvasComponent_Template_div_drop_37_listener($event) {\n            return ctx.onDrop($event, \"knowledge\");\n          });\n          i0.ɵɵelementStart(38, \"div\", 15);\n          i0.ɵɵlistener(\"click\", function DropZoneCanvasComponent_Template_div_click_38_listener() {\n            return ctx.toggleZone(\"knowledge\");\n          });\n          i0.ɵɵelementStart(39, \"div\", 16)(40, \"div\", 17);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(41, \"svg\", 18);\n          i0.ɵɵelement(42, \"rect\", 33)(43, \"path\", 34)(44, \"path\", 35)(45, \"path\", 36)(46, \"path\", 37)(47, \"path\", 38)(48, \"path\", 39);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(49, \"h3\", 25);\n          i0.ɵɵtext(50, \"Knowledgebase\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"div\", 26)(52, \"span\", 40);\n          i0.ɵɵtext(53, \"Optional\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"button\", 28);\n          i0.ɵɵlistener(\"click\", function DropZoneCanvasComponent_Template_button_click_54_listener($event) {\n            ctx.toggleZone(\"knowledge\");\n            return $event.stopPropagation();\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(55, \"svg\", 29);\n          i0.ɵɵelement(56, \"path\", 30);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(57, DropZoneCanvasComponent_div_57_Template, 4, 3, \"div\", 31);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(58, \"div\", 41)(59, \"div\", 42);\n          i0.ɵɵlistener(\"dragover\", function DropZoneCanvasComponent_Template_div_dragover_59_listener($event) {\n            return ctx.onDragOver($event, \"model\");\n          })(\"drop\", function DropZoneCanvasComponent_Template_div_drop_59_listener($event) {\n            return ctx.onDrop($event, \"model\");\n          });\n          i0.ɵɵelementStart(60, \"div\", 15);\n          i0.ɵɵlistener(\"click\", function DropZoneCanvasComponent_Template_div_click_60_listener() {\n            return ctx.toggleZone(\"model\");\n          });\n          i0.ɵɵelementStart(61, \"div\", 16)(62, \"div\", 17);\n          i0.ɵɵtemplate(63, DropZoneCanvasComponent__svg_svg_63_Template, 5, 0, \"svg\", 43);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"h3\", 25);\n          i0.ɵɵtext(65, \"AI Model\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(66, \"div\", 26)(67, \"span\", 27);\n          i0.ɵɵtext(68, \"Required\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"button\", 28);\n          i0.ɵɵlistener(\"click\", function DropZoneCanvasComponent_Template_button_click_69_listener($event) {\n            ctx.toggleZone(\"model\");\n            return $event.stopPropagation();\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(70, \"svg\", 29);\n          i0.ɵɵelement(71, \"path\", 30);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(72, DropZoneCanvasComponent_div_72_Template, 4, 3, \"div\", 31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(73, \"div\", 44);\n          i0.ɵɵlistener(\"dragover\", function DropZoneCanvasComponent_Template_div_dragover_73_listener($event) {\n            return ctx.onDragOver($event, ctx.config.agentType === \"individual\" ? \"guardrail\" : \"tool\");\n          })(\"drop\", function DropZoneCanvasComponent_Template_div_drop_73_listener($event) {\n            return ctx.onDrop($event, ctx.config.agentType === \"individual\" ? \"guardrail\" : \"tool\");\n          });\n          i0.ɵɵelementStart(74, \"div\", 15);\n          i0.ɵɵlistener(\"click\", function DropZoneCanvasComponent_Template_div_click_74_listener() {\n            return ctx.toggleZone(ctx.config.agentType === \"individual\" ? \"guardrail\" : \"tool\");\n          });\n          i0.ɵɵelementStart(75, \"div\", 16)(76, \"div\", 17);\n          i0.ɵɵtemplate(77, DropZoneCanvasComponent__svg_svg_77_Template, 3, 0, \"svg\", 43)(78, DropZoneCanvasComponent__svg_svg_78_Template, 3, 0, \"svg\", 43);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"h3\", 25);\n          i0.ɵɵtext(80);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(81, \"div\", 26)(82, \"span\", 40);\n          i0.ɵɵtext(83, \"Optional\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(84, \"button\", 28);\n          i0.ɵɵlistener(\"click\", function DropZoneCanvasComponent_Template_button_click_84_listener($event) {\n            ctx.toggleZone(ctx.config.agentType === \"individual\" ? \"guardrail\" : \"tool\");\n            return $event.stopPropagation();\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(85, \"svg\", 29);\n          i0.ɵɵelement(86, \"path\", 30);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(87, DropZoneCanvasComponent_div_87_Template, 4, 3, \"div\", 31);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵstyleProp(\"stroke-dasharray\", 314)(\"stroke-dashoffset\", 314 - 314 * ctx.completionPercentage / 100);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\"\", ctx.completionPercentage, \"%\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"has-nodes\", ctx.promptNodes.length > 0)(\"collapsed\", !ctx.isZoneExpanded(\"prompt\"));\n          i0.ɵɵadvance(17);\n          i0.ɵɵstyleProp(\"transform\", ctx.isZoneExpanded(\"prompt\") ? \"rotate(180deg)\" : \"rotate(0deg)\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isZoneExpanded(\"prompt\"));\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"has-nodes\", ctx.knowledgeNodes.length > 0)(\"collapsed\", !ctx.isZoneExpanded(\"knowledge\"));\n          i0.ɵɵadvance(18);\n          i0.ɵɵstyleProp(\"transform\", ctx.isZoneExpanded(\"knowledge\") ? \"rotate(180deg)\" : \"rotate(0deg)\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isZoneExpanded(\"knowledge\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"has-nodes\", ctx.modelNodes.length > 0)(\"collapsed\", !ctx.isZoneExpanded(\"model\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.config.agentType === \"collaborative\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵstyleProp(\"transform\", ctx.isZoneExpanded(\"model\") ? \"rotate(180deg)\" : \"rotate(0deg)\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isZoneExpanded(\"model\"));\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"guardrails-zone\", ctx.config.agentType === \"individual\")(\"tools-zone\", ctx.config.agentType === \"collaborative\")(\"has-nodes\", ctx.bottomNodes.length > 0)(\"collapsed\", !ctx.isZoneExpanded(ctx.config.agentType === \"individual\" ? \"guardrail\" : \"tool\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.config.agentType === \"individual\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.config.agentType === \"collaborative\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.config.agentType === \"individual\" ? \"Guardrails\" : \"Tools\", \" \");\n          i0.ɵɵadvance(5);\n          i0.ɵɵstyleProp(\"transform\", ctx.isZoneExpanded(ctx.config.agentType === \"individual\" ? \"guardrail\" : \"tool\") ? \"rotate(180deg)\" : \"rotate(0deg)\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isZoneExpanded(ctx.config.agentType === \"individual\" ? \"guardrail\" : \"tool\"));\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf],\n      styles: [\".drop-zone-canvas-container[_ngcontent-%COMP%] {\\n  height: 100%;\\n  width: 100%;\\n  min-height: 500px;\\n  background-color: #f8f9fa;\\n  background-image: radial-gradient(circle, #d1d5db 1px, transparent 1px);\\n  background-size: 20px 20px;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  border: 1px solid #d1d3d8;\\n}\\n\\n.central-progress[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 58%;\\n  transform: translate(-50%, -50%);\\n  z-index: 10;\\n  background-color: white;\\n  border-radius: 50%;\\n  padding: 8px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n.progress-ring[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.progress-circle[_ngcontent-%COMP%] {\\n  transform: rotate(-90deg);\\n}\\n\\n.progress-background[_ngcontent-%COMP%] {\\n  opacity: 0.15;\\n}\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  transition: stroke-dashoffset 0.3s ease;\\n}\\n\\n.progress-content[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  text-align: center;\\n  color: #374151;\\n}\\n\\n.progress-percentage[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  line-height: 1;\\n}\\n\\n.progress-label[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #6b7280;\\n  margin-top: 2px;\\n}\\n\\n.drop-zone[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  padding: 20px;\\n  transition: all 0.3s ease;\\n}\\n.drop-zone.has-nodes[_ngcontent-%COMP%] {\\n  border-style: solid;\\n}\\n.drop-zone.collapsed[_ngcontent-%COMP%] {\\n  min-height: 40px;\\n}\\n.drop-zone.collapsed[_ngcontent-%COMP%]   .zone-header[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n\\n.north-zone[_ngcontent-%COMP%] {\\n  width: 300px;\\n  height: auto;\\n  background-color: #ecf0fa;\\n  color: #005eb5;\\n  border: 2px solid #9ab7f6;\\n  position: relative;\\n  top: -2.5rem;\\n  left: -30%;\\n}\\n\\n.east-zone[_ngcontent-%COMP%] {\\n  width: 300px;\\n  height: auto;\\n  background-color: #f2ebfd;\\n  border: 2px solid #d6c2f9;\\n  color: #d6c2f9;\\n  position: relative;\\n  top: -2.5rem;\\n  right: -30%;\\n}\\n\\n.south-zone[_ngcontent-%COMP%] {\\n  width: 300px;\\n  height: auto;\\n  background-color: #fbf6f7;\\n  border: 2px solid #fecaca;\\n  color: #dc2626 !important;\\n  position: absolute;\\n  bottom: -3.5rem;\\n  right: -30%;\\n}\\n.south-zone.has-nodes[_ngcontent-%COMP%] {\\n  height: auto;\\n  min-height: 100px;\\n  max-height: 200px;\\n  overflow-y: auto;\\n}\\n\\n.west-zone[_ngcontent-%COMP%] {\\n  width: 300px;\\n  height: auto;\\n  background-color: #ecf8f4;\\n  border: 2px solid #a9e1cc;\\n  color: #25684f;\\n  position: absolute;\\n  bottom: -3.5rem;\\n  left: -30%;\\n}\\n.west-zone.has-nodes[_ngcontent-%COMP%] {\\n  height: auto;\\n  min-height: 100px;\\n  max-height: 200px;\\n  overflow-y: auto;\\n}\\n\\n#parent-box[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  height: 100%;\\n  width: 100%;\\n  gap: 2rem;\\n}\\n\\n#box1[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: space-between;\\n  height: 300px;\\n  position: relative;\\n  border: 1px solid #bbbec5;\\n  position: fixed;\\n  top: 32%;\\n  left: 40%;\\n  border-left: none;\\n}\\n\\n#box2[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: space-between;\\n  height: 300px;\\n  position: relative;\\n  border: 1px solid #bbbec5;\\n  position: fixed;\\n  top: 32%;\\n  left: 60%;\\n  border-right: none;\\n}\\n\\n.zone-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin-bottom: 16px;\\n  padding-bottom: 12px;\\n  cursor: pointer;\\n  -webkit-user-select: none;\\n          user-select: none;\\n  transition: all 0.2s ease;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 8px;\\n  background-color: rgba(59, 130, 246, 0.1);\\n  color: #3b82f6;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .zone-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #374151;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .accordion-toggle[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 4px;\\n  border-radius: 4px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.2s ease;\\n  color: #6b7280;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .accordion-toggle[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 0, 0, 0.05);\\n  color: #374151;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .accordion-toggle[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  transition: transform 0.3s ease;\\n}\\n\\n.required-badge[_ngcontent-%COMP%] {\\n  background-color: var(--status-error);\\n  color: white;\\n  font-size: 10px;\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n  font-weight: 500;\\n}\\n\\n.optional-badge[_ngcontent-%COMP%] {\\n  background-color: var(--status-warning);\\n  color: white;\\n  font-size: 10px;\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n  font-weight: 500;\\n}\\n\\n.zone-content[_ngcontent-%COMP%] {\\n  min-height: 60px;\\n  flex: 1;\\n  transition: all 0.3s ease-in-out;\\n  opacity: 1;\\n  overflow: hidden;\\n}\\n\\n.west-zone[_ngcontent-%COMP%], \\n.south-zone[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n.west-zone[_ngcontent-%COMP%]   .zone-content[_ngcontent-%COMP%], \\n.south-zone[_ngcontent-%COMP%]   .zone-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  overflow-y: auto;\\n  max-height: 200px;\\n}\\n.west-zone[_ngcontent-%COMP%]   .nodes-list[_ngcontent-%COMP%], \\n.south-zone[_ngcontent-%COMP%]   .nodes-list[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n}\\n\\n.empty-state[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: var(--text-secondary);\\n  font-size: 12px;\\n  padding: 20px 0;\\n  font-style: italic;\\n}\\n\\n.nodes-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n}\\n\\n.kanban-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  padding: 12px 16px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n  transition: all 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n}\\n.kanban-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\\n  transform: translateY(-1px);\\n}\\n.kanban-card[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #374151;\\n  flex: 1;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n.kanban-card[_ngcontent-%COMP%]   .delete-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #9ca3af;\\n  font-size: 16px;\\n  cursor: pointer;\\n  padding: 0;\\n  width: 20px;\\n  height: 20px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 4px;\\n  margin-left: 8px;\\n  flex-shrink: 0;\\n}\\n.kanban-card[_ngcontent-%COMP%]   .delete-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #fef2f2;\\n  color: #ef4444;\\n}\\n\\n.prompts-zone.has-nodes[_ngcontent-%COMP%] {\\n  border-color: #3b82f6;\\n}\\n.prompts-zone[_ngcontent-%COMP%]   .zone-title[_ngcontent-%COMP%] {\\n  color: #005eb5 !important;\\n}\\n.prompts-zone[_ngcontent-%COMP%]   .required-badge[_ngcontent-%COMP%] {\\n  background-color: #3b82f6;\\n}\\n\\n.models-zone.has-nodes[_ngcontent-%COMP%] {\\n  border-color: #8b5cf6;\\n}\\n.models-zone[_ngcontent-%COMP%]   .zone-title[_ngcontent-%COMP%] {\\n  color: #673ab7 !important;\\n}\\n.models-zone[_ngcontent-%COMP%]   .required-badge[_ngcontent-%COMP%] {\\n  background-color: #8b5cf6;\\n}\\n\\n.knowledge-zone.has-nodes[_ngcontent-%COMP%] {\\n  border-color: #10b981;\\n}\\n.knowledge-zone[_ngcontent-%COMP%]   .zone-title[_ngcontent-%COMP%] {\\n  color: #25684f !important;\\n}\\n.knowledge-zone[_ngcontent-%COMP%]   .optional-badge[_ngcontent-%COMP%] {\\n  background-color: #10b981;\\n}\\n\\n.guardrails-zone.has-nodes[_ngcontent-%COMP%] {\\n  border-color: #ef4444;\\n}\\n.guardrails-zone[_ngcontent-%COMP%]   .zone-title[_ngcontent-%COMP%] {\\n  color: #dc2626 !important;\\n}\\n.guardrails-zone[_ngcontent-%COMP%]   .optional-badge[_ngcontent-%COMP%] {\\n  background-color: #ef4444;\\n}\\n\\n.tools-zone.has-nodes[_ngcontent-%COMP%] {\\n  border-color: #f59e0b;\\n}\\n.tools-zone[_ngcontent-%COMP%]   .zone-title[_ngcontent-%COMP%] {\\n  color: #d97706 !important;\\n}\\n.tools-zone[_ngcontent-%COMP%]   .optional-badge[_ngcontent-%COMP%] {\\n  background-color: #f59e0b;\\n}\\n\\n@media (max-width: 1600px) {\\n  #box1[_ngcontent-%COMP%] {\\n    top: 32%;\\n    left: 35%;\\n  }\\n  #box2[_ngcontent-%COMP%] {\\n    top: 32%;\\n    left: 60%;\\n  }\\n}\\n@media (max-width: 1400px) {\\n  #box1[_ngcontent-%COMP%] {\\n    top: 32%;\\n    left: 30%;\\n  }\\n  #box2[_ngcontent-%COMP%] {\\n    top: 32%;\\n    left: 60%;\\n  }\\n  .connection-system[_ngcontent-%COMP%]   .prompt-connection[_ngcontent-%COMP%]::before {\\n    width: 70px;\\n    left: 155px;\\n  }\\n  .connection-system[_ngcontent-%COMP%]   .prompt-connection[_ngcontent-%COMP%]::after {\\n    left: 223px;\\n  }\\n  .connection-system[_ngcontent-%COMP%]   .model-connection[_ngcontent-%COMP%]::before {\\n    width: 70px;\\n    right: 155px;\\n  }\\n  .connection-system[_ngcontent-%COMP%]   .model-connection[_ngcontent-%COMP%]::after {\\n    right: 223px;\\n  }\\n  .connection-system[_ngcontent-%COMP%]   .knowledge-connection[_ngcontent-%COMP%]::before {\\n    width: calc(26% - 50px);\\n  }\\n  .connection-system[_ngcontent-%COMP%]   .bottom-connection[_ngcontent-%COMP%]::before {\\n    width: calc(26% - 50px);\\n  }\\n}\\n@media (max-width: 1200px) {\\n  .connection-system[_ngcontent-%COMP%]   .prompt-connection[_ngcontent-%COMP%]::before {\\n    width: 60px;\\n    left: 160px;\\n  }\\n  .connection-system[_ngcontent-%COMP%]   .prompt-connection[_ngcontent-%COMP%]::after {\\n    left: 218px;\\n  }\\n  .connection-system[_ngcontent-%COMP%]   .model-connection[_ngcontent-%COMP%]::before {\\n    width: 60px;\\n    right: 160px;\\n  }\\n  .connection-system[_ngcontent-%COMP%]   .model-connection[_ngcontent-%COMP%]::after {\\n    right: 218px;\\n  }\\n  .connection-system[_ngcontent-%COMP%]   .knowledge-connection[_ngcontent-%COMP%]::before {\\n    width: calc(23% - 40px);\\n  }\\n  .connection-system[_ngcontent-%COMP%]   .bottom-connection[_ngcontent-%COMP%]::before {\\n    width: calc(23% - 40px);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .connection-system[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n.drop-zone.drag-over[_ngcontent-%COMP%] {\\n  border-color: var(--brand-primary);\\n  background-color: var(--brand-primary-10);\\n  transform: scale(1.02);\\n}\\n.drop-zone.drag-reject[_ngcontent-%COMP%] {\\n  border-color: var(--status-error);\\n  background-color: rgba(239, 68, 68, 0.1);\\n  animation: _ngcontent-%COMP%_shake 0.3s ease-in-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_shake {\\n  0%, 100% {\\n    transform: translateX(0);\\n  }\\n  25% {\\n    transform: translateX(-2px);\\n  }\\n  75% {\\n    transform: translateX(2px);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .drop-zone-canvas-container[_ngcontent-%COMP%] {\\n    height: 500px;\\n  }\\n  .drop-zone[_ngcontent-%COMP%] {\\n    width: 150px;\\n    height: 100px;\\n    padding: 12px;\\n  }\\n  .north-zone[_ngcontent-%COMP%], \\n   .south-zone[_ngcontent-%COMP%] {\\n    width: 150px;\\n  }\\n  .east-zone[_ngcontent-%COMP%], \\n   .west-zone[_ngcontent-%COMP%] {\\n    width: 150px;\\n    height: 100px;\\n  }\\n  .central-progress[_ngcontent-%COMP%]   .progress-circle[_ngcontent-%COMP%] {\\n    width: 100px;\\n    height: 100px;\\n  }\\n  .central-progress[_ngcontent-%COMP%]   .progress-percentage[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return DropZoneCanvasComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "EventEmitter", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "DropZoneCanvasComponent_div_36_div_3_Template_button_click_3_listener", "node_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "onDeleteNode", "id", "ɵɵadvance", "ɵɵtextInterpolate", "data", "name", "ɵɵtemplate", "DropZoneCanvasComponent_div_36_div_1_Template", "DropZoneCanvasComponent_div_36_div_3_Template", "ɵɵproperty", "promptNodes", "length", "trackByNodeId", "DropZoneCanvasComponent_div_57_div_3_Template_button_click_3_listener", "node_r5", "_r4", "DropZoneCanvasComponent_div_57_div_1_Template", "DropZoneCanvasComponent_div_57_div_3_Template", "knowledgeNodes", "ɵɵelement", "DropZoneCanvasComponent_div_72_div_3_Template_button_click_3_listener", "node_r7", "_r6", "DropZoneCanvasComponent_div_72_div_1_Template", "DropZoneCanvasComponent_div_72_div_3_Template", "modelNodes", "ɵɵtextInterpolate1", "config", "agentType", "DropZoneCanvasComponent_div_87_div_3_Template_button_click_3_listener", "node_r9", "_r8", "DropZoneCanvasComponent_div_87_div_1_Template", "DropZoneCanvasComponent_div_87_div_3_Template", "bottomNodes", "DropZoneCanvasComponent", "nodeTemplate", "nodes", "nodeDropped", "nodeDeleted", "zoneStates", "prompt", "model", "knowledge", "guardrail", "tool", "filter", "node", "type", "completionPercentage", "completed", "total", "Math", "round", "onDragOver", "event", "zoneType", "preventDefault", "dragData", "dataTransfer", "getData", "JSON", "parse", "isCompatibleWithZone", "dropEffect", "e", "onDrop", "console", "log", "canAddToZone", "rect", "currentTarget", "getBoundingClientRect", "position", "x", "clientX", "left", "y", "clientY", "top", "emit", "zone", "error", "nodeId", "index", "toggleZone", "actualType", "isZoneExpanded", "nodeType", "allowedCategories", "prompts", "max", "models", "guardrails", "tools", "selectors", "contentQueries", "DropZoneCanvasComponent_ContentQueries", "rf", "ctx", "dirIndex", "DropZoneCanvasComponent_Template_div_dragover_17_listener", "$event", "DropZoneCanvasComponent_Template_div_drop_17_listener", "DropZoneCanvasComponent_Template_div_click_18_listener", "DropZoneCanvasComponent_Template_button_click_33_listener", "stopPropagation", "DropZoneCanvasComponent_div_36_Template", "DropZoneCanvasComponent_Template_div_dragover_37_listener", "DropZoneCanvasComponent_Template_div_drop_37_listener", "DropZoneCanvasComponent_Template_div_click_38_listener", "DropZoneCanvasComponent_Template_button_click_54_listener", "DropZoneCanvasComponent_div_57_Template", "DropZoneCanvasComponent_Template_div_dragover_59_listener", "DropZoneCanvasComponent_Template_div_drop_59_listener", "DropZoneCanvasComponent_Template_div_click_60_listener", "DropZoneCanvasComponent__svg_svg_63_Template", "DropZoneCanvasComponent_Template_button_click_69_listener", "DropZoneCanvasComponent_div_72_Template", "DropZoneCanvasComponent_Template_div_dragover_73_listener", "DropZoneCanvasComponent_Template_div_drop_73_listener", "DropZoneCanvasComponent_Template_div_click_74_listener", "DropZoneCanvasComponent__svg_svg_77_Template", "DropZoneCanvasComponent__svg_svg_78_Template", "DropZoneCanvasComponent_Template_button_click_84_listener", "DropZoneCanvasComponent_div_87_Template", "ɵɵstyleProp", "ɵɵclassProp", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\shared\\components\\drop-zone-canvas\\drop-zone-canvas.component.ts", "C:\\console\\aava-ui-web\\projects\\console\\src\\app\\shared\\components\\drop-zone-canvas\\drop-zone-canvas.component.html"], "sourcesContent": ["import { CommonModule } from '@angular/common';\r\nimport {\r\n  Component,\r\n  Input,\r\n  Output,\r\n  EventEmitter,\r\n  TemplateRef,\r\n  ContentChild,\r\n} from '@angular/core';\r\nimport { CanvasNode } from '../canvas-board/canvas-board.component';\r\n\r\nexport interface DropZoneNode {\r\n  id: string;\r\n  type: 'prompt' | 'model' | 'knowledge' | 'guardrail' | 'tool';\r\n  data: any;\r\n}\r\n\r\nexport interface DropZoneConfig {\r\n  agentType: 'individual' | 'collaborative';\r\n  allowedCategories: {\r\n    prompts: { max: number; required: boolean };\r\n    models: { max: number; required: boolean };\r\n    knowledge: { max: number; required: boolean };\r\n    guardrails: { max: number; required: boolean };\r\n    tools: { max: number; required: boolean };\r\n  };\r\n}\r\n\r\n@Component({\r\n  selector: 'app-drop-zone-canvas',\r\n  standalone: true,\r\n  imports: [CommonModule],\r\n  templateUrl: './drop-zone-canvas.component.html',\r\n  styleUrls: ['./drop-zone-canvas.component.scss'],\r\n})\r\nexport class DropZoneCanvasComponent {\r\n  @ContentChild('nodeTemplate') nodeTemplate!: TemplateRef<any>;\r\n\r\n  @Input() config!: DropZoneConfig;\r\n  @Input() nodes: CanvasNode[] = [];\r\n\r\n  @Output() nodeDropped = new EventEmitter<{\r\n    node: any;\r\n    zone: string;\r\n    position: { x: number; y: number };\r\n  }>();\r\n  @Output() nodeDeleted = new EventEmitter<string>();\r\n\r\n  // Accordion state management\r\n  zoneStates = {\r\n    prompt: true, // expanded by default\r\n    model: true, // expanded by default\r\n    knowledge: true, // expanded by default\r\n    guardrail: true, // expanded by default\r\n    tool: true, // expanded by default\r\n  };\r\n\r\n  // Computed properties for categorized nodes\r\n  get promptNodes(): CanvasNode[] {\r\n    return this.nodes.filter((node) => node.data?.type === 'prompt');\r\n  }\r\n\r\n  get modelNodes(): CanvasNode[] {\r\n    return this.nodes.filter((node) => node.data?.type === 'model');\r\n  }\r\n\r\n  get knowledgeNodes(): CanvasNode[] {\r\n    return this.nodes.filter((node) => node.data?.type === 'knowledge');\r\n  }\r\n\r\n  get bottomNodes(): CanvasNode[] {\r\n    if (this.config.agentType === 'individual') {\r\n      return this.nodes.filter((node) => node.data?.type === 'guardrail');\r\n    } else {\r\n      return this.nodes.filter((node) => node.data?.type === 'tool');\r\n    }\r\n  }\r\n\r\n  // Progress calculation\r\n  get completionPercentage(): number {\r\n    let completed = 0;\r\n    let total = 2; // Only 2 required: prompt and model\r\n\r\n    // Check required components only\r\n    if (this.promptNodes.length > 0) completed++;\r\n    if (this.modelNodes.length > 0) completed++;\r\n\r\n    // Knowledge, Guardrails, and Tools are now optional (don't count toward completion)\r\n\r\n    return Math.round((completed / total) * 100);\r\n  }\r\n\r\n  onDragOver(event: DragEvent, zoneType: string): void {\r\n    event.preventDefault();\r\n    // Try both possible drag data formats\r\n    const dragData =\r\n      event.dataTransfer?.getData('application/reactflow') ||\r\n      event.dataTransfer?.getData('text/plain');\r\n    if (dragData) {\r\n      try {\r\n        const data = JSON.parse(dragData);\r\n        // Check if the dragged item is compatible with this zone\r\n        if (this.isCompatibleWithZone(data, zoneType)) {\r\n          event.dataTransfer!.dropEffect = 'copy';\r\n        } else {\r\n          event.dataTransfer!.dropEffect = 'none';\r\n        }\r\n      } catch (e) {\r\n        event.dataTransfer!.dropEffect = 'none';\r\n      }\r\n    }\r\n  }\r\n\r\n  onDrop(event: DragEvent, zoneType: string): void {\r\n    event.preventDefault();\r\n    // Try both possible drag data formats\r\n    const dragData =\r\n      event.dataTransfer?.getData('application/reactflow') ||\r\n      event.dataTransfer?.getData('text/plain');\r\n    if (dragData) {\r\n      try {\r\n        const data = JSON.parse(dragData);\r\n\r\n        console.log('🎯 Drop zone received data:', { data, zoneType });\r\n\r\n        // Check compatibility\r\n        if (!this.isCompatibleWithZone(data, zoneType)) {\r\n          console.log(\r\n            'Incompatible node type for this zone:',\r\n            data.type,\r\n            'vs',\r\n            zoneType,\r\n          );\r\n          return; // Bounce back\r\n        }\r\n\r\n        console.log('✅ Compatible drop detected');\r\n\r\n        // Check limits\r\n        if (!this.canAddToZone(zoneType)) {\r\n          console.log('Zone limit reached, replacing existing node');\r\n          // For single-node zones, we'll replace the existing node\r\n        }\r\n\r\n        // Calculate position within the zone\r\n        const rect = (event.currentTarget as Element).getBoundingClientRect();\r\n        const position = {\r\n          x: event.clientX - rect.left,\r\n          y: event.clientY - rect.top,\r\n        };\r\n\r\n        console.log('✅ Emitting nodeDropped event');\r\n        this.nodeDropped.emit({\r\n          node: data,\r\n          zone: zoneType,\r\n          position,\r\n        });\r\n      } catch (e) {\r\n        console.error('Invalid drag data:', e);\r\n      }\r\n    }\r\n  }\r\n\r\n  onDeleteNode(nodeId: string): void {\r\n    this.nodeDeleted.emit(nodeId);\r\n  }\r\n\r\n  trackByNodeId(index: number, node: any): string {\r\n    return node.id;\r\n  }\r\n\r\n  // Accordion toggle functionality\r\n  toggleZone(zoneType: string): void {\r\n    if (zoneType === 'guardrail' || zoneType === 'tool') {\r\n      // Handle both guardrail and tool zones\r\n      const actualType =\r\n        this.config.agentType === 'individual' ? 'guardrail' : 'tool';\r\n      this.zoneStates[actualType as keyof typeof this.zoneStates] =\r\n        !this.zoneStates[actualType as keyof typeof this.zoneStates];\r\n    } else {\r\n      this.zoneStates[zoneType as keyof typeof this.zoneStates] =\r\n        !this.zoneStates[zoneType as keyof typeof this.zoneStates];\r\n    }\r\n  }\r\n\r\n  // Get zone state for template\r\n  isZoneExpanded(zoneType: string): boolean {\r\n    if (zoneType === 'guardrail' || zoneType === 'tool') {\r\n      const actualType =\r\n        this.config.agentType === 'individual' ? 'guardrail' : 'tool';\r\n      return this.zoneStates[actualType as keyof typeof this.zoneStates];\r\n    }\r\n    return this.zoneStates[zoneType as keyof typeof this.zoneStates];\r\n  }\r\n\r\n  private isCompatibleWithZone(dragData: any, zoneType: string): boolean {\r\n    const nodeType = dragData.type || dragData.data?.type;\r\n\r\n    switch (zoneType) {\r\n      case 'prompt':\r\n        return nodeType === 'prompt';\r\n      case 'model':\r\n        return nodeType === 'model';\r\n      case 'knowledge':\r\n        return nodeType === 'knowledge';\r\n      case 'guardrail':\r\n        return nodeType === 'guardrail';\r\n      case 'tool':\r\n        return nodeType === 'tool';\r\n      default:\r\n        return false;\r\n    }\r\n  }\r\n\r\n  private canAddToZone(zoneType: string): boolean {\r\n    switch (zoneType) {\r\n      case 'prompt':\r\n        return (\r\n          this.promptNodes.length < this.config.allowedCategories.prompts.max\r\n        );\r\n      case 'model':\r\n        return (\r\n          this.modelNodes.length < this.config.allowedCategories.models.max\r\n        );\r\n      case 'knowledge':\r\n        return (\r\n          this.knowledgeNodes.length <\r\n          this.config.allowedCategories.knowledge.max\r\n        );\r\n      case 'guardrail':\r\n        return (\r\n          this.bottomNodes.length < this.config.allowedCategories.guardrails.max\r\n        );\r\n      case 'tool':\r\n        return (\r\n          this.bottomNodes.length < this.config.allowedCategories.tools.max\r\n        );\r\n      default:\r\n        return false;\r\n    }\r\n  }\r\n}\r\n", "<div class=\"drop-zone-canvas-container\">\r\n  <!-- Central Progress Bar -->\r\n  <div class=\"central-progress\">\r\n    <div class=\"progress-ring\">\r\n      <svg class=\"progress-circle\" width=\"120\" height=\"120\">\r\n        <defs>\r\n          <linearGradient\r\n            id=\"progressGradient\"\r\n            x1=\"0%\"\r\n            y1=\"0%\"\r\n            x2=\"100%\"\r\n            y2=\"100%\"\r\n          >\r\n            <stop offset=\"33.91%\" style=\"stop-color: #0084ff\" />\r\n            <stop offset=\"100%\" style=\"stop-color: #03bdd4\" />\r\n          </linearGradient>\r\n        </defs>\r\n        <circle\r\n          class=\"progress-background\"\r\n          cx=\"60\"\r\n          cy=\"60\"\r\n          r=\"50\"\r\n          fill=\"none\"\r\n          stroke=\"#e5e7eb\"\r\n          stroke-width=\"8\"\r\n        />\r\n        <circle\r\n          class=\"progress-bar\"\r\n          cx=\"60\"\r\n          cy=\"60\"\r\n          r=\"50\"\r\n          fill=\"none\"\r\n          stroke=\"url(#progressGradient)\"\r\n          stroke-width=\"8\"\r\n          stroke-linecap=\"round\"\r\n          [style.stroke-dasharray]=\"314\"\r\n          [style.stroke-dashoffset]=\"314 - (314 * completionPercentage) / 100\"\r\n          transform=\"rotate(180 60 60)\"\r\n        />\r\n      </svg>\r\n      <div class=\"progress-content\">\r\n        <div class=\"progress-percentage\">{{ completionPercentage }}%</div>\r\n        <div class=\"progress-label\">Complete</div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Drop Zones -->\r\n  <!-- <div class=\"drop-zones\"> -->\r\n  <!-- North: Prompts -->\r\n\r\n  <div id=\"parent-box\">\r\n    <div id=\"box1\">\r\n      <div\r\n        class=\"drop-zone north-zone prompts-zone\"\r\n        [class.has-nodes]=\"promptNodes.length > 0\"\r\n        [class.collapsed]=\"!isZoneExpanded('prompt')\"\r\n        (dragover)=\"onDragOver($event, 'prompt')\"\r\n        (drop)=\"onDrop($event, 'prompt')\"\r\n      >\r\n        <div class=\"zone-header\" (click)=\"toggleZone('prompt')\">\r\n          <div class=\"header-content\">\r\n            <div class=\"header-icon\">\r\n              <svg\r\n                width=\"45\"\r\n                height=\"44\"\r\n                viewBox=\"0 0 45 44\"\r\n                fill=\"none\"\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n              >\r\n                <rect\r\n                  x=\"0.679688\"\r\n                  width=\"44\"\r\n                  height=\"44\"\r\n                  rx=\"8\"\r\n                  fill=\"#5082EF\"\r\n                />\r\n                <path\r\n                  d=\"M20.6166 25.5C20.5273 25.1539 20.347 24.8381 20.0942 24.5854C19.8415 24.3327 19.5257 24.1523 19.1796 24.063L13.0446 22.481C12.94 22.4513 12.8478 22.3883 12.7822 22.3014C12.7166 22.2146 12.6812 22.1088 12.6812 22C12.6812 21.8912 12.7166 21.7854 12.7822 21.6986C12.8478 21.6118 12.94 21.5487 13.0446 21.519L19.1796 19.936C19.5256 19.8468 19.8413 19.6666 20.094 19.414C20.3467 19.1615 20.5272 18.8459 20.6166 18.5L22.1986 12.365C22.228 12.2599 22.291 12.1673 22.3779 12.1014C22.4649 12.0355 22.571 11.9998 22.6801 11.9998C22.7892 11.9998 22.8954 12.0355 22.9823 12.1014C23.0692 12.1673 23.1322 12.2599 23.1616 12.365L24.7426 18.5C24.8319 18.8461 25.0123 19.1619 25.265 19.4146C25.5177 19.6673 25.8336 19.8477 26.1796 19.937L32.3146 21.518C32.4201 21.5471 32.5132 21.61 32.5795 21.6971C32.6458 21.7841 32.6817 21.8906 32.6817 22C32.6817 22.1094 32.6458 22.2159 32.5795 22.3029C32.5132 22.39 32.4201 22.4529 32.3146 22.482L26.1796 24.063C25.8336 24.1523 25.5177 24.3327 25.265 24.5854C25.0123 24.8381 24.8319 25.1539 24.7426 25.5L23.1606 31.635C23.1312 31.7401 23.0682 31.8327 22.9813 31.8986C22.8944 31.9646 22.7882 32.0003 22.6791 32.0003C22.57 32.0003 22.4639 31.9646 22.3769 31.8986C22.29 31.8327 22.227 31.7401 22.1976 31.635L20.6166 25.5Z\"\r\n                  stroke=\"white\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n                <path\r\n                  d=\"M30.6797 13V17\"\r\n                  stroke=\"white\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n                <path\r\n                  d=\"M32.6797 15H28.6797\"\r\n                  stroke=\"white\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n                <path\r\n                  d=\"M14.6797 27V29\"\r\n                  stroke=\"white\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n                <path\r\n                  d=\"M15.6797 28H13.6797\"\r\n                  stroke=\"white\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n              </svg>\r\n            </div>\r\n            <h3 class=\"zone-title\">System Prompt</h3>\r\n          </div>\r\n          <div class=\"header-actions\">\r\n            <span class=\"required-badge\">Required</span>\r\n            <button\r\n              class=\"accordion-toggle\"\r\n              type=\"button\"\r\n              (click)=\"toggleZone('prompt'); $event.stopPropagation()\"\r\n            >\r\n              <svg\r\n                width=\"16\"\r\n                height=\"16\"\r\n                viewBox=\"0 0 24 24\"\r\n                fill=\"none\"\r\n                [style.transform]=\"\r\n                  isZoneExpanded('prompt') ? 'rotate(180deg)' : 'rotate(0deg)'\r\n                \"\r\n              >\r\n                <path\r\n                  d=\"M6 9L12 15L18 9\"\r\n                  stroke=\"currentColor\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n              </svg>\r\n            </button>\r\n          </div>\r\n        </div>\r\n        <div class=\"zone-content\" *ngIf=\"isZoneExpanded('prompt')\">\r\n          <div *ngIf=\"promptNodes.length === 0\" class=\"empty-state\">\r\n            Drop a prompt here\r\n          </div>\r\n          <div class=\"nodes-list\">\r\n            <div\r\n              *ngFor=\"let node of promptNodes; trackBy: trackByNodeId\"\r\n              class=\"kanban-card\"\r\n            >\r\n              <span class=\"card-title\">{{ node.data?.name || \"Prompt\" }}</span>\r\n              <button class=\"delete-btn\" (click)=\"onDeleteNode(node.id)\">\r\n                ×\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <!-- West: Knowledge -->\r\n      <div\r\n        class=\"drop-zone west-zone knowledge-zone\"\r\n        [class.has-nodes]=\"knowledgeNodes.length > 0\"\r\n        [class.collapsed]=\"!isZoneExpanded('knowledge')\"\r\n        (dragover)=\"onDragOver($event, 'knowledge')\"\r\n        (drop)=\"onDrop($event, 'knowledge')\"\r\n      >\r\n        <div class=\"zone-header\" (click)=\"toggleZone('knowledge')\">\r\n          <div class=\"header-content\">\r\n            <div class=\"header-icon\">\r\n              <svg\r\n                width=\"45\"\r\n                height=\"44\"\r\n                viewBox=\"0 0 45 44\"\r\n                fill=\"none\"\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n              >\r\n                <rect\r\n                  x=\"0.679688\"\r\n                  width=\"44\"\r\n                  height=\"44\"\r\n                  rx=\"8\"\r\n                  fill=\"#308666\"\r\n                />\r\n                <path\r\n                  d=\"M22.6797 17V31\"\r\n                  stroke=\"white\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n                <path\r\n                  d=\"M26.6797 22H28.6797\"\r\n                  stroke=\"white\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n                <path\r\n                  d=\"M26.6797 18H28.6797\"\r\n                  stroke=\"white\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n                <path\r\n                  d=\"M13.6797 28C13.4145 28 13.1601 27.8946 12.9726 27.7071C12.785 27.5196 12.6797 27.2652 12.6797 27V14C12.6797 13.7348 12.785 13.4804 12.9726 13.2929C13.1601 13.1054 13.4145 13 13.6797 13H18.6797C19.7406 13 20.758 13.4214 21.5081 14.1716C22.2583 14.9217 22.6797 15.9391 22.6797 17C22.6797 15.9391 23.1011 14.9217 23.8513 14.1716C24.6014 13.4214 25.6188 13 26.6797 13H31.6797C31.9449 13 32.1993 13.1054 32.3868 13.2929C32.5743 13.4804 32.6797 13.7348 32.6797 14V27C32.6797 27.2652 32.5743 27.5196 32.3868 27.7071C32.1993 27.8946 31.9449 28 31.6797 28H25.6797C24.884 28 24.121 28.3161 23.5584 28.8787C22.9958 29.4413 22.6797 30.2044 22.6797 31C22.6797 30.2044 22.3636 29.4413 21.801 28.8787C21.2384 28.3161 20.4753 28 19.6797 28H13.6797Z\"\r\n                  stroke=\"white\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n                <path\r\n                  d=\"M16.6797 22H18.6797\"\r\n                  stroke=\"white\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n                <path\r\n                  d=\"M16.6797 18H18.6797\"\r\n                  stroke=\"white\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n              </svg>\r\n            </div>\r\n            <h3 class=\"zone-title\">Knowledgebase</h3>\r\n          </div>\r\n          <div class=\"header-actions\">\r\n            <span class=\"optional-badge\">Optional</span>\r\n            <button\r\n              class=\"accordion-toggle\"\r\n              type=\"button\"\r\n              (click)=\"toggleZone('knowledge'); $event.stopPropagation()\"\r\n            >\r\n              <svg\r\n                width=\"16\"\r\n                height=\"16\"\r\n                viewBox=\"0 0 24 24\"\r\n                fill=\"none\"\r\n                [style.transform]=\"\r\n                  isZoneExpanded('knowledge')\r\n                    ? 'rotate(180deg)'\r\n                    : 'rotate(0deg)'\r\n                \"\r\n              >\r\n                <path\r\n                  d=\"M6 9L12 15L18 9\"\r\n                  stroke=\"currentColor\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n              </svg>\r\n            </button>\r\n          </div>\r\n        </div>\r\n        <div class=\"zone-content\" *ngIf=\"isZoneExpanded('knowledge')\">\r\n          <div *ngIf=\"knowledgeNodes.length === 0\" class=\"empty-state\">\r\n            Drop knowledge here\r\n          </div>\r\n          <div class=\"nodes-list\">\r\n            <div\r\n              *ngFor=\"let node of knowledgeNodes; trackBy: trackByNodeId\"\r\n              class=\"kanban-card\"\r\n            >\r\n              <span class=\"card-title\">{{\r\n                node.data?.name || \"Knowledge\"\r\n              }}</span>\r\n              <button class=\"delete-btn\" (click)=\"onDeleteNode(node.id)\">\r\n                ×\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <!-- <div class=\"central-progress\">\r\n        <div class=\"progress-ring\">\r\n          <svg class=\"progress-circle\" width=\"120\" height=\"120\">\r\n            <circle\r\n              class=\"progress-background\"\r\n              cx=\"60\"\r\n              cy=\"60\"\r\n              r=\"50\"\r\n              fill=\"none\"\r\n              stroke=\"#e5e7eb\"\r\n              stroke-width=\"8\"\r\n            />\r\n            <circle\r\n              class=\"progress-bar\"\r\n              cx=\"60\"\r\n              cy=\"60\"\r\n              r=\"50\"\r\n              fill=\"none\"\r\n              stroke=\"#3b82f6\"\r\n              stroke-width=\"8\"\r\n              stroke-linecap=\"round\"\r\n              [style.stroke-dasharray]=\"314\"\r\n              [style.stroke-dashoffset]=\"\r\n                314 - (314 * completionPercentage) / 100\r\n              \"\r\n              transform=\"rotate(-90 60 60)\"\r\n            />\r\n          </svg>\r\n          <div class=\"progress-content\">\r\n            <div class=\"progress-percentage\">{{ completionPercentage }}%</div>\r\n            <div class=\"progress-label\">Complete</div>\r\n          </div>\r\n        </div>\r\n      </div> -->\r\n    </div>\r\n    <div id=\"box2\">\r\n      <!-- East: Models -->\r\n      <div\r\n        class=\"drop-zone east-zone models-zone\"\r\n        [class.has-nodes]=\"modelNodes.length > 0\"\r\n        [class.collapsed]=\"!isZoneExpanded('model')\"\r\n        (dragover)=\"onDragOver($event, 'model')\"\r\n        (drop)=\"onDrop($event, 'model')\"\r\n      >\r\n        <div class=\"zone-header\" (click)=\"toggleZone('model')\">\r\n          <div class=\"header-content\">\r\n            <div class=\"header-icon\">\r\n              <svg\r\n                width=\"45\"\r\n                height=\"44\"\r\n                viewBox=\"0 0 45 44\"\r\n                fill=\"none\"\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                *ngIf=\"config.agentType === 'collaborative'\"\r\n              >\r\n                <rect\r\n                  x=\"0.679688\"\r\n                  width=\"44\"\r\n                  height=\"44\"\r\n                  rx=\"8\"\r\n                  fill=\"#997BCF\"\r\n                />\r\n                <path\r\n                  d=\"M31.6797 18C31.6793 17.6493 31.5867 17.3048 31.4112 17.0012C31.2357 16.6975 30.9834 16.4454 30.6797 16.27L23.6797 12.27C23.3756 12.0945 23.0308 12.0021 22.6797 12.0021C22.3286 12.0021 21.9837 12.0945 21.6797 12.27L14.6797 16.27C14.376 16.4454 14.1237 16.6975 13.9481 17.0012C13.7726 17.3048 13.68 17.6493 13.6797 18V26C13.68 26.3508 13.7726 26.6952 13.9481 26.9989C14.1237 27.3025 14.376 27.5547 14.6797 27.73L21.6797 31.73C21.9837 31.9056 22.3286 31.998 22.6797 31.998C23.0308 31.998 23.3756 31.9056 23.6797 31.73L30.6797 27.73C30.9834 27.5547 31.2357 27.3025 31.4112 26.9989C31.5867 26.6952 31.6793 26.3508 31.6797 26V18Z\"\r\n                  stroke=\"white\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n                <path\r\n                  d=\"M13.9795 17L22.6795 22L31.3795 17\"\r\n                  stroke=\"white\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n                <path\r\n                  d=\"M22.6797 32V22\"\r\n                  stroke=\"white\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n              </svg>\r\n            </div>\r\n            <h3 class=\"zone-title\">AI Model</h3>\r\n          </div>\r\n          <div class=\"header-actions\">\r\n            <span class=\"required-badge\">Required</span>\r\n            <button\r\n              class=\"accordion-toggle\"\r\n              type=\"button\"\r\n              (click)=\"toggleZone('model'); $event.stopPropagation()\"\r\n            >\r\n              <svg\r\n                width=\"16\"\r\n                height=\"16\"\r\n                viewBox=\"0 0 24 24\"\r\n                fill=\"none\"\r\n                [style.transform]=\"\r\n                  isZoneExpanded('model') ? 'rotate(180deg)' : 'rotate(0deg)'\r\n                \"\r\n              >\r\n                <path\r\n                  d=\"M6 9L12 15L18 9\"\r\n                  stroke=\"currentColor\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n              </svg>\r\n            </button>\r\n          </div>\r\n        </div>\r\n        <div class=\"zone-content\" *ngIf=\"isZoneExpanded('model')\">\r\n          <div *ngIf=\"modelNodes.length === 0\" class=\"empty-state\">\r\n            Drop a model here\r\n          </div>\r\n          <div class=\"nodes-list\">\r\n            <div\r\n              *ngFor=\"let node of modelNodes; trackBy: trackByNodeId\"\r\n              class=\"kanban-card\"\r\n            >\r\n              <span class=\"card-title\">{{ node.data?.name || \"Model\" }}</span>\r\n              <button class=\"delete-btn\" (click)=\"onDeleteNode(node.id)\">\r\n                ×\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- South: Guardrails/Tools -->\r\n      <div\r\n        class=\"drop-zone south-zone\"\r\n        [class.guardrails-zone]=\"config.agentType === 'individual'\"\r\n        [class.tools-zone]=\"config.agentType === 'collaborative'\"\r\n        [class.has-nodes]=\"bottomNodes.length > 0\"\r\n        [class.collapsed]=\"\r\n          !isZoneExpanded(\r\n            config.agentType === 'individual' ? 'guardrail' : 'tool'\r\n          )\r\n        \"\r\n        (dragover)=\"\r\n          onDragOver(\r\n            $event,\r\n            config.agentType === 'individual' ? 'guardrail' : 'tool'\r\n          )\r\n        \"\r\n        (drop)=\"\r\n          onDrop(\r\n            $event,\r\n            config.agentType === 'individual' ? 'guardrail' : 'tool'\r\n          )\r\n        \"\r\n      >\r\n        <div\r\n          class=\"zone-header\"\r\n          (click)=\"\r\n            toggleZone(config.agentType === 'individual' ? 'guardrail' : 'tool')\r\n          \"\r\n        >\r\n          <div class=\"header-content\">\r\n            <div class=\"header-icon\">\r\n              <svg\r\n                width=\"45\"\r\n                height=\"44\"\r\n                viewBox=\"0 0 45 44\"\r\n                fill=\"none\"\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                *ngIf=\"config.agentType === 'individual'\"\r\n              >\r\n                <rect\r\n                  x=\"0.679688\"\r\n                  width=\"44\"\r\n                  height=\"44\"\r\n                  rx=\"8\"\r\n                  fill=\"#DC2626\"\r\n                />\r\n                <path\r\n                  d=\"M30.6797 23C30.6797 28 27.1797 30.5 23.0197 31.95C22.8019 32.0238 22.5652 32.0202 22.3497 31.94C18.1797 30.5 14.6797 28 14.6797 23V16C14.6797 15.7347 14.785 15.4804 14.9726 15.2929C15.1601 15.1053 15.4145 15 15.6797 15C17.6797 15 20.1797 13.8 21.9197 12.28C22.1315 12.099 22.401 11.9995 22.6797 11.9995C22.9583 11.9995 23.2278 12.099 23.4397 12.28C25.1897 13.81 27.6797 15 29.6797 15C29.9449 15 30.1993 15.1053 30.3868 15.2929C30.5743 15.4804 30.6797 15.7347 30.6797 16V23Z\"\r\n                  stroke=\"white\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n              </svg>\r\n\r\n              <svg\r\n                width=\"45\"\r\n                height=\"44\"\r\n                viewBox=\"0 0 45 44\"\r\n                fill=\"none\"\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                *ngIf=\"config.agentType === 'collaborative'\"\r\n              >\r\n                <rect\r\n                  x=\"0.679688\"\r\n                  width=\"44\"\r\n                  height=\"44\"\r\n                  rx=\"8\"\r\n                  fill=\"#D97706\"\r\n                />\r\n                <path\r\n                  d=\"M25.3795 16.3C25.1963 16.4869 25.0937 16.7382 25.0937 17C25.0937 17.2617 25.1963 17.5131 25.3795 17.7L26.9795 19.3C27.1665 19.4832 27.4178 19.5858 27.6795 19.5858C27.9413 19.5858 28.1926 19.4832 28.3795 19.3L32.1495 15.53C32.6524 16.6412 32.8046 17.8792 32.586 19.0791C32.3674 20.279 31.7883 21.3838 30.9258 22.2463C30.0634 23.1087 28.9586 23.6878 27.7587 23.9064C26.5588 24.1251 25.3207 23.9728 24.2095 23.47L17.2995 30.38C16.9017 30.7778 16.3622 31.0013 15.7995 31.0013C15.2369 31.0013 14.6974 30.7778 14.2995 30.38C13.9017 29.9822 13.6782 29.4426 13.6782 28.88C13.6782 28.3174 13.9017 27.7778 14.2995 27.38L21.2095 20.47C20.7067 19.3588 20.5545 18.1207 20.7731 16.9208C20.9917 15.7209 21.5708 14.6161 22.4333 13.7537C23.2957 12.8913 24.4005 12.3121 25.6004 12.0935C26.8003 11.8749 28.0384 12.0271 29.1495 12.53L25.3895 16.29L25.3795 16.3Z\"\r\n                  stroke=\"white\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n              </svg>\r\n            </div>\r\n            <h3 class=\"zone-title\">\r\n              {{ config.agentType === \"individual\" ? \"Guardrails\" : \"Tools\" }}\r\n            </h3>\r\n          </div>\r\n          <div class=\"header-actions\">\r\n            <span class=\"optional-badge\">Optional</span>\r\n            <button\r\n              class=\"accordion-toggle\"\r\n              type=\"button\"\r\n              (click)=\"\r\n                toggleZone(\r\n                  config.agentType === 'individual' ? 'guardrail' : 'tool'\r\n                );\r\n                $event.stopPropagation()\r\n              \"\r\n            >\r\n              <svg\r\n                width=\"16\"\r\n                height=\"16\"\r\n                viewBox=\"0 0 24 24\"\r\n                fill=\"none\"\r\n                [style.transform]=\"\r\n                  isZoneExpanded(\r\n                    config.agentType === 'individual' ? 'guardrail' : 'tool'\r\n                  )\r\n                    ? 'rotate(180deg)'\r\n                    : 'rotate(0deg)'\r\n                \"\r\n              >\r\n                <path\r\n                  d=\"M6 9L12 15L18 9\"\r\n                  stroke=\"currentColor\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n              </svg>\r\n            </button>\r\n          </div>\r\n        </div>\r\n        <div\r\n          class=\"zone-content\"\r\n          *ngIf=\"\r\n            isZoneExpanded(\r\n              config.agentType === 'individual' ? 'guardrail' : 'tool'\r\n            )\r\n          \"\r\n        >\r\n          <div *ngIf=\"bottomNodes.length === 0\" class=\"empty-state\">\r\n            Drop\r\n            {{ config.agentType === \"individual\" ? \"guardrails\" : \"tools\" }}\r\n            here\r\n          </div>\r\n          <div class=\"nodes-list\">\r\n            <div\r\n              *ngFor=\"let node of bottomNodes; trackBy: trackByNodeId\"\r\n              class=\"kanban-card\"\r\n            >\r\n              <span class=\"card-title\">{{\r\n                node.data?.name ||\r\n                  (config.agentType === \"individual\" ? \"Guardrail\" : \"Tool\")\r\n              }}</span>\r\n              <button class=\"delete-btn\" (click)=\"onDeleteNode(node.id)\">\r\n                ×\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- </div> -->\r\n</div>\r\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAIEC,YAAY,QAGP,eAAe;;;;;;ICwIZC,EAAA,CAAAC,cAAA,cAA0D;IACxDD,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAMFH,EAJF,CAAAC,cAAA,cAGC,eAC0B;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjEH,EAAA,CAAAC,cAAA,iBAA2D;IAAhCD,EAAA,CAAAI,UAAA,mBAAAC,sEAAA;MAAA,MAAAC,OAAA,GAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAG,YAAA,CAAAP,OAAA,CAAAQ,EAAA,CAAqB;IAAA,EAAC;IACxDd,EAAA,CAAAE,MAAA,eACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;IAJqBH,EAAA,CAAAe,SAAA,GAAiC;IAAjCf,EAAA,CAAAgB,iBAAA,EAAAV,OAAA,CAAAW,IAAA,kBAAAX,OAAA,CAAAW,IAAA,CAAAC,IAAA,cAAiC;;;;;IAThElB,EAAA,CAAAC,cAAA,cAA2D;IACzDD,EAAA,CAAAmB,UAAA,IAAAC,6CAAA,kBAA0D;IAG1DpB,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAmB,UAAA,IAAAE,6CAAA,kBAGC;IAOLrB,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAdEH,EAAA,CAAAe,SAAA,EAA8B;IAA9Bf,EAAA,CAAAsB,UAAA,SAAAZ,MAAA,CAAAa,WAAA,CAAAC,MAAA,OAA8B;IAKfxB,EAAA,CAAAe,SAAA,GAAgB;IAAAf,EAAhB,CAAAsB,UAAA,YAAAZ,MAAA,CAAAa,WAAA,CAAgB,iBAAAb,MAAA,CAAAe,aAAA,CAAsB;;;;;IAgH3DzB,EAAA,CAAAC,cAAA,cAA6D;IAC3DD,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAMFH,EAJF,CAAAC,cAAA,cAGC,eAC0B;IAAAD,EAAA,CAAAE,MAAA,GAEvB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACTH,EAAA,CAAAC,cAAA,iBAA2D;IAAhCD,EAAA,CAAAI,UAAA,mBAAAsB,sEAAA;MAAA,MAAAC,OAAA,GAAA3B,EAAA,CAAAO,aAAA,CAAAqB,GAAA,EAAAnB,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAG,YAAA,CAAAc,OAAA,CAAAb,EAAA,CAAqB;IAAA,EAAC;IACxDd,EAAA,CAAAE,MAAA,eACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;IANqBH,EAAA,CAAAe,SAAA,GAEvB;IAFuBf,EAAA,CAAAgB,iBAAA,EAAAW,OAAA,CAAAV,IAAA,kBAAAU,OAAA,CAAAV,IAAA,CAAAC,IAAA,iBAEvB;;;;;IAXRlB,EAAA,CAAAC,cAAA,cAA8D;IAC5DD,EAAA,CAAAmB,UAAA,IAAAU,6CAAA,kBAA6D;IAG7D7B,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAmB,UAAA,IAAAW,6CAAA,kBAGC;IASL9B,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAhBEH,EAAA,CAAAe,SAAA,EAAiC;IAAjCf,EAAA,CAAAsB,UAAA,SAAAZ,MAAA,CAAAqB,cAAA,CAAAP,MAAA,OAAiC;IAKlBxB,EAAA,CAAAe,SAAA,GAAmB;IAAAf,EAAnB,CAAAsB,UAAA,YAAAZ,MAAA,CAAAqB,cAAA,CAAmB,iBAAArB,MAAA,CAAAe,aAAA,CAAsB;;;;;;IA4D1DzB,EAAA,CAAAC,cAAA,cAOC;IAsBCD,EArBA,CAAAgC,SAAA,eAME,eAOA,eAOA,eAOA;IACJhC,EAAA,CAAAG,YAAA,EAAM;;;;;IAgCVH,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAMFH,EAJF,CAAAC,cAAA,cAGC,eAC0B;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChEH,EAAA,CAAAC,cAAA,iBAA2D;IAAhCD,EAAA,CAAAI,UAAA,mBAAA6B,sEAAA;MAAA,MAAAC,OAAA,GAAAlC,EAAA,CAAAO,aAAA,CAAA4B,GAAA,EAAA1B,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAG,YAAA,CAAAqB,OAAA,CAAApB,EAAA,CAAqB;IAAA,EAAC;IACxDd,EAAA,CAAAE,MAAA,eACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;IAJqBH,EAAA,CAAAe,SAAA,GAAgC;IAAhCf,EAAA,CAAAgB,iBAAA,EAAAkB,OAAA,CAAAjB,IAAA,kBAAAiB,OAAA,CAAAjB,IAAA,CAAAC,IAAA,aAAgC;;;;;IAT/DlB,EAAA,CAAAC,cAAA,cAA0D;IACxDD,EAAA,CAAAmB,UAAA,IAAAiB,6CAAA,kBAAyD;IAGzDpC,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAmB,UAAA,IAAAkB,6CAAA,kBAGC;IAOLrC,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAdEH,EAAA,CAAAe,SAAA,EAA6B;IAA7Bf,EAAA,CAAAsB,UAAA,SAAAZ,MAAA,CAAA4B,UAAA,CAAAd,MAAA,OAA6B;IAKdxB,EAAA,CAAAe,SAAA,GAAe;IAAAf,EAAf,CAAAsB,UAAA,YAAAZ,MAAA,CAAA4B,UAAA,CAAe,iBAAA5B,MAAA,CAAAe,aAAA,CAAsB;;;;;;IA4CtDzB,EAAA,CAAAC,cAAA,cAOC;IAQCD,EAPA,CAAAgC,SAAA,eAME,eAOA;IACJhC,EAAA,CAAAG,YAAA,EAAM;;;;;;IAENH,EAAA,CAAAC,cAAA,cAOC;IAQCD,EAPA,CAAAgC,SAAA,eAME,eAOA;IACJhC,EAAA,CAAAG,YAAA,EAAM;;;;;IAkDVH,EAAA,CAAAC,cAAA,cAA0D;IACxDD,EAAA,CAAAE,MAAA,GAGF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAHJH,EAAA,CAAAe,SAAA,EAGF;IAHEf,EAAA,CAAAuC,kBAAA,WAAA7B,MAAA,CAAA8B,MAAA,CAAAC,SAAA,qDAGF;;;;;;IAMIzC,EAJF,CAAAC,cAAA,cAGC,eAC0B;IAAAD,EAAA,CAAAE,MAAA,GAGvB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACTH,EAAA,CAAAC,cAAA,iBAA2D;IAAhCD,EAAA,CAAAI,UAAA,mBAAAsC,sEAAA;MAAA,MAAAC,OAAA,GAAA3C,EAAA,CAAAO,aAAA,CAAAqC,GAAA,EAAAnC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAG,YAAA,CAAA8B,OAAA,CAAA7B,EAAA,CAAqB;IAAA,EAAC;IACxDd,EAAA,CAAAE,MAAA,eACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;;IAPqBH,EAAA,CAAAe,SAAA,GAGvB;IAHuBf,EAAA,CAAAgB,iBAAA,EAAA2B,OAAA,CAAA1B,IAAA,kBAAA0B,OAAA,CAAA1B,IAAA,CAAAC,IAAA,MAAAR,MAAA,CAAA8B,MAAA,CAAAC,SAAA,0CAGvB;;;;;IArBRzC,EAAA,CAAAC,cAAA,cAOC;IACCD,EAAA,CAAAmB,UAAA,IAAA0B,6CAAA,kBAA0D;IAK1D7C,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAmB,UAAA,IAAA2B,6CAAA,kBAGC;IAUL9C,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAnBEH,EAAA,CAAAe,SAAA,EAA8B;IAA9Bf,EAAA,CAAAsB,UAAA,SAAAZ,MAAA,CAAAqC,WAAA,CAAAvB,MAAA,OAA8B;IAOfxB,EAAA,CAAAe,SAAA,GAAgB;IAAAf,EAAhB,CAAAsB,UAAA,YAAAZ,MAAA,CAAAqC,WAAA,CAAgB,iBAAArC,MAAA,CAAAe,aAAA,CAAsB;;;AD/frE,WAAauB,uBAAuB;EAA9B,MAAOA,uBAAuB;IACJC,YAAY;IAEjCT,MAAM;IACNU,KAAK,GAAiB,EAAE;IAEvBC,WAAW,GAAG,IAAIpD,YAAY,EAIpC;IACMqD,WAAW,GAAG,IAAIrD,YAAY,EAAU;IAElD;IACAsD,UAAU,GAAG;MACXC,MAAM,EAAE,IAAI;MAAE;MACdC,KAAK,EAAE,IAAI;MAAE;MACbC,SAAS,EAAE,IAAI;MAAE;MACjBC,SAAS,EAAE,IAAI;MAAE;MACjBC,IAAI,EAAE,IAAI,CAAE;KACb;IAED;IACA,IAAInC,WAAWA,CAAA;MACb,OAAO,IAAI,CAAC2B,KAAK,CAACS,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAAC3C,IAAI,EAAE4C,IAAI,KAAK,QAAQ,CAAC;IAClE;IAEA,IAAIvB,UAAUA,CAAA;MACZ,OAAO,IAAI,CAACY,KAAK,CAACS,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAAC3C,IAAI,EAAE4C,IAAI,KAAK,OAAO,CAAC;IACjE;IAEA,IAAI9B,cAAcA,CAAA;MAChB,OAAO,IAAI,CAACmB,KAAK,CAACS,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAAC3C,IAAI,EAAE4C,IAAI,KAAK,WAAW,CAAC;IACrE;IAEA,IAAId,WAAWA,CAAA;MACb,IAAI,IAAI,CAACP,MAAM,CAACC,SAAS,KAAK,YAAY,EAAE;QAC1C,OAAO,IAAI,CAACS,KAAK,CAACS,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAAC3C,IAAI,EAAE4C,IAAI,KAAK,WAAW,CAAC;MACrE,CAAC,MAAM;QACL,OAAO,IAAI,CAACX,KAAK,CAACS,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAAC3C,IAAI,EAAE4C,IAAI,KAAK,MAAM,CAAC;MAChE;IACF;IAEA;IACA,IAAIC,oBAAoBA,CAAA;MACtB,IAAIC,SAAS,GAAG,CAAC;MACjB,IAAIC,KAAK,GAAG,CAAC,CAAC,CAAC;MAEf;MACA,IAAI,IAAI,CAACzC,WAAW,CAACC,MAAM,GAAG,CAAC,EAAEuC,SAAS,EAAE;MAC5C,IAAI,IAAI,CAACzB,UAAU,CAACd,MAAM,GAAG,CAAC,EAAEuC,SAAS,EAAE;MAE3C;MAEA,OAAOE,IAAI,CAACC,KAAK,CAAEH,SAAS,GAAGC,KAAK,GAAI,GAAG,CAAC;IAC9C;IAEAG,UAAUA,CAACC,KAAgB,EAAEC,QAAgB;MAC3CD,KAAK,CAACE,cAAc,EAAE;MACtB;MACA,MAAMC,QAAQ,GACZH,KAAK,CAACI,YAAY,EAAEC,OAAO,CAAC,uBAAuB,CAAC,IACpDL,KAAK,CAACI,YAAY,EAAEC,OAAO,CAAC,YAAY,CAAC;MAC3C,IAAIF,QAAQ,EAAE;QACZ,IAAI;UACF,MAAMtD,IAAI,GAAGyD,IAAI,CAACC,KAAK,CAACJ,QAAQ,CAAC;UACjC;UACA,IAAI,IAAI,CAACK,oBAAoB,CAAC3D,IAAI,EAAEoD,QAAQ,CAAC,EAAE;YAC7CD,KAAK,CAACI,YAAa,CAACK,UAAU,GAAG,MAAM;UACzC,CAAC,MAAM;YACLT,KAAK,CAACI,YAAa,CAACK,UAAU,GAAG,MAAM;UACzC;QACF,CAAC,CAAC,OAAOC,CAAC,EAAE;UACVV,KAAK,CAACI,YAAa,CAACK,UAAU,GAAG,MAAM;QACzC;MACF;IACF;IAEAE,MAAMA,CAACX,KAAgB,EAAEC,QAAgB;MACvCD,KAAK,CAACE,cAAc,EAAE;MACtB;MACA,MAAMC,QAAQ,GACZH,KAAK,CAACI,YAAY,EAAEC,OAAO,CAAC,uBAAuB,CAAC,IACpDL,KAAK,CAACI,YAAY,EAAEC,OAAO,CAAC,YAAY,CAAC;MAC3C,IAAIF,QAAQ,EAAE;QACZ,IAAI;UACF,MAAMtD,IAAI,GAAGyD,IAAI,CAACC,KAAK,CAACJ,QAAQ,CAAC;UAEjCS,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE;YAAEhE,IAAI;YAAEoD;UAAQ,CAAE,CAAC;UAE9D;UACA,IAAI,CAAC,IAAI,CAACO,oBAAoB,CAAC3D,IAAI,EAAEoD,QAAQ,CAAC,EAAE;YAC9CW,OAAO,CAACC,GAAG,CACT,uCAAuC,EACvChE,IAAI,CAAC4C,IAAI,EACT,IAAI,EACJQ,QAAQ,CACT;YACD,OAAO,CAAC;UACV;UAEAW,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;UAEzC;UACA,IAAI,CAAC,IAAI,CAACC,YAAY,CAACb,QAAQ,CAAC,EAAE;YAChCW,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;YAC1D;UACF;UAEA;UACA,MAAME,IAAI,GAAIf,KAAK,CAACgB,aAAyB,CAACC,qBAAqB,EAAE;UACrE,MAAMC,QAAQ,GAAG;YACfC,CAAC,EAAEnB,KAAK,CAACoB,OAAO,GAAGL,IAAI,CAACM,IAAI;YAC5BC,CAAC,EAAEtB,KAAK,CAACuB,OAAO,GAAGR,IAAI,CAACS;WACzB;UAEDZ,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;UAC3C,IAAI,CAAC9B,WAAW,CAAC0C,IAAI,CAAC;YACpBjC,IAAI,EAAE3C,IAAI;YACV6E,IAAI,EAAEzB,QAAQ;YACdiB;WACD,CAAC;QACJ,CAAC,CAAC,OAAOR,CAAC,EAAE;UACVE,OAAO,CAACe,KAAK,CAAC,oBAAoB,EAAEjB,CAAC,CAAC;QACxC;MACF;IACF;IAEAjE,YAAYA,CAACmF,MAAc;MACzB,IAAI,CAAC5C,WAAW,CAACyC,IAAI,CAACG,MAAM,CAAC;IAC/B;IAEAvE,aAAaA,CAACwE,KAAa,EAAErC,IAAS;MACpC,OAAOA,IAAI,CAAC9C,EAAE;IAChB;IAEA;IACAoF,UAAUA,CAAC7B,QAAgB;MACzB,IAAIA,QAAQ,KAAK,WAAW,IAAIA,QAAQ,KAAK,MAAM,EAAE;QACnD;QACA,MAAM8B,UAAU,GACd,IAAI,CAAC3D,MAAM,CAACC,SAAS,KAAK,YAAY,GAAG,WAAW,GAAG,MAAM;QAC/D,IAAI,CAACY,UAAU,CAAC8C,UAA0C,CAAC,GACzD,CAAC,IAAI,CAAC9C,UAAU,CAAC8C,UAA0C,CAAC;MAChE,CAAC,MAAM;QACL,IAAI,CAAC9C,UAAU,CAACgB,QAAwC,CAAC,GACvD,CAAC,IAAI,CAAChB,UAAU,CAACgB,QAAwC,CAAC;MAC9D;IACF;IAEA;IACA+B,cAAcA,CAAC/B,QAAgB;MAC7B,IAAIA,QAAQ,KAAK,WAAW,IAAIA,QAAQ,KAAK,MAAM,EAAE;QACnD,MAAM8B,UAAU,GACd,IAAI,CAAC3D,MAAM,CAACC,SAAS,KAAK,YAAY,GAAG,WAAW,GAAG,MAAM;QAC/D,OAAO,IAAI,CAACY,UAAU,CAAC8C,UAA0C,CAAC;MACpE;MACA,OAAO,IAAI,CAAC9C,UAAU,CAACgB,QAAwC,CAAC;IAClE;IAEQO,oBAAoBA,CAACL,QAAa,EAAEF,QAAgB;MAC1D,MAAMgC,QAAQ,GAAG9B,QAAQ,CAACV,IAAI,IAAIU,QAAQ,CAACtD,IAAI,EAAE4C,IAAI;MAErD,QAAQQ,QAAQ;QACd,KAAK,QAAQ;UACX,OAAOgC,QAAQ,KAAK,QAAQ;QAC9B,KAAK,OAAO;UACV,OAAOA,QAAQ,KAAK,OAAO;QAC7B,KAAK,WAAW;UACd,OAAOA,QAAQ,KAAK,WAAW;QACjC,KAAK,WAAW;UACd,OAAOA,QAAQ,KAAK,WAAW;QACjC,KAAK,MAAM;UACT,OAAOA,QAAQ,KAAK,MAAM;QAC5B;UACE,OAAO,KAAK;MAChB;IACF;IAEQnB,YAAYA,CAACb,QAAgB;MACnC,QAAQA,QAAQ;QACd,KAAK,QAAQ;UACX,OACE,IAAI,CAAC9C,WAAW,CAACC,MAAM,GAAG,IAAI,CAACgB,MAAM,CAAC8D,iBAAiB,CAACC,OAAO,CAACC,GAAG;QAEvE,KAAK,OAAO;UACV,OACE,IAAI,CAAClE,UAAU,CAACd,MAAM,GAAG,IAAI,CAACgB,MAAM,CAAC8D,iBAAiB,CAACG,MAAM,CAACD,GAAG;QAErE,KAAK,WAAW;UACd,OACE,IAAI,CAACzE,cAAc,CAACP,MAAM,GAC1B,IAAI,CAACgB,MAAM,CAAC8D,iBAAiB,CAAC9C,SAAS,CAACgD,GAAG;QAE/C,KAAK,WAAW;UACd,OACE,IAAI,CAACzD,WAAW,CAACvB,MAAM,GAAG,IAAI,CAACgB,MAAM,CAAC8D,iBAAiB,CAACI,UAAU,CAACF,GAAG;QAE1E,KAAK,MAAM;UACT,OACE,IAAI,CAACzD,WAAW,CAACvB,MAAM,GAAG,IAAI,CAACgB,MAAM,CAAC8D,iBAAiB,CAACK,KAAK,CAACH,GAAG;QAErE;UACE,OAAO,KAAK;MAChB;IACF;;uCA7MWxD,uBAAuB;IAAA;;YAAvBA,uBAAuB;MAAA4D,SAAA;MAAAC,cAAA,WAAAC,uCAAAC,EAAA,EAAAC,GAAA,EAAAC,QAAA;QAAA,IAAAF,EAAA;;;;;;;;;;;;;;;;;;;;;UChChC/G,EAHJ,CAAAC,cAAA,aAAwC,aAER,aACD;;UAGrBD,EAFJ,CAAAC,cAAA,aAAsD,WAC9C,wBAOH;UAECD,EADA,CAAAgC,SAAA,cAAoD,cACF;UAEtDhC,EADE,CAAAG,YAAA,EAAiB,EACZ;UAUPH,EATA,CAAAgC,SAAA,gBAQE,gBAaA;UACJhC,EAAA,CAAAG,YAAA,EAAM;;UAEJH,EADF,CAAAC,cAAA,cAA8B,eACK;UAAAD,EAAA,CAAAE,MAAA,IAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAClEH,EAAA,CAAAC,cAAA,eAA4B;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAG1CF,EAH0C,CAAAG,YAAA,EAAM,EACtC,EACF,EACF;UAQFH,EAFJ,CAAAC,cAAA,eAAqB,eACJ,eAOZ;UADCD,EADA,CAAAI,UAAA,sBAAA8G,0DAAAC,MAAA;YAAA,OAAYH,GAAA,CAAA7C,UAAA,CAAAgD,MAAA,EAAmB,QAAQ,CAAC;UAAA,EAAC,kBAAAC,sDAAAD,MAAA;YAAA,OACjCH,GAAA,CAAAjC,MAAA,CAAAoC,MAAA,EAAe,QAAQ,CAAC;UAAA,EAAC;UAEjCnH,EAAA,CAAAC,cAAA,eAAwD;UAA/BD,EAAA,CAAAI,UAAA,mBAAAiH,uDAAA;YAAA,OAASL,GAAA,CAAAd,UAAA,CAAW,QAAQ,CAAC;UAAA,EAAC;UAEnDlG,EADF,CAAAC,cAAA,eAA4B,eACD;;UACvBD,EAAA,CAAAC,cAAA,eAMC;UAoCCD,EAnCA,CAAAgC,SAAA,gBAME,gBAOA,gBAOA,gBAOA,gBAOA,gBAOA;UAENhC,EADE,CAAAG,YAAA,EAAM,EACF;;UACNH,EAAA,CAAAC,cAAA,cAAuB;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UACtCF,EADsC,CAAAG,YAAA,EAAK,EACrC;UAEJH,EADF,CAAAC,cAAA,eAA4B,gBACG;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5CH,EAAA,CAAAC,cAAA,kBAIC;UADCD,EAAA,CAAAI,UAAA,mBAAAkH,0DAAAH,MAAA;YAASH,GAAA,CAAAd,UAAA,CAAW,QAAQ,CAAC;YAAA,OAAEiB,MAAA,CAAAI,eAAA,EAAwB;UAAA,EAAC;;UAExDvH,EAAA,CAAAC,cAAA,eAQC;UACCD,EAAA,CAAAgC,SAAA,gBAME;UAIVhC,EAHM,CAAAG,YAAA,EAAM,EACC,EACL,EACF;UACNH,EAAA,CAAAmB,UAAA,KAAAqG,uCAAA,kBAA2D;UAgB7DxH,EAAA,CAAAG,YAAA,EAAM;;UAENH,EAAA,CAAAC,cAAA,eAMC;UADCD,EADA,CAAAI,UAAA,sBAAAqH,0DAAAN,MAAA;YAAA,OAAYH,GAAA,CAAA7C,UAAA,CAAAgD,MAAA,EAAmB,WAAW,CAAC;UAAA,EAAC,kBAAAO,sDAAAP,MAAA;YAAA,OACpCH,GAAA,CAAAjC,MAAA,CAAAoC,MAAA,EAAe,WAAW,CAAC;UAAA,EAAC;UAEpCnH,EAAA,CAAAC,cAAA,eAA2D;UAAlCD,EAAA,CAAAI,UAAA,mBAAAuH,uDAAA;YAAA,OAASX,GAAA,CAAAd,UAAA,CAAW,WAAW,CAAC;UAAA,EAAC;UAEtDlG,EADF,CAAAC,cAAA,eAA4B,eACD;;UACvBD,EAAA,CAAAC,cAAA,eAMC;UA2CCD,EA1CA,CAAAgC,SAAA,gBAME,gBAOA,gBAOA,gBAOA,gBAOA,gBAOA,gBAOA;UAENhC,EADE,CAAAG,YAAA,EAAM,EACF;;UACNH,EAAA,CAAAC,cAAA,cAAuB;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UACtCF,EADsC,CAAAG,YAAA,EAAK,EACrC;UAEJH,EADF,CAAAC,cAAA,eAA4B,gBACG;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5CH,EAAA,CAAAC,cAAA,kBAIC;UADCD,EAAA,CAAAI,UAAA,mBAAAwH,0DAAAT,MAAA;YAASH,GAAA,CAAAd,UAAA,CAAW,WAAW,CAAC;YAAA,OAAEiB,MAAA,CAAAI,eAAA,EAAwB;UAAA,EAAC;;UAE3DvH,EAAA,CAAAC,cAAA,eAUC;UACCD,EAAA,CAAAgC,SAAA,gBAME;UAIVhC,EAHM,CAAAG,YAAA,EAAM,EACC,EACL,EACF;UACNH,EAAA,CAAAmB,UAAA,KAAA0G,uCAAA,kBAA8D;UAqDlE7H,EAnCE,CAAAG,YAAA,EAAM,EAmCF;;UAGJH,EAFF,CAAAC,cAAA,eAAe,eAQZ;UADCD,EADA,CAAAI,UAAA,sBAAA0H,0DAAAX,MAAA;YAAA,OAAYH,GAAA,CAAA7C,UAAA,CAAAgD,MAAA,EAAmB,OAAO,CAAC;UAAA,EAAC,kBAAAY,sDAAAZ,MAAA;YAAA,OAChCH,GAAA,CAAAjC,MAAA,CAAAoC,MAAA,EAAe,OAAO,CAAC;UAAA,EAAC;UAEhCnH,EAAA,CAAAC,cAAA,eAAuD;UAA9BD,EAAA,CAAAI,UAAA,mBAAA4H,uDAAA;YAAA,OAAShB,GAAA,CAAAd,UAAA,CAAW,OAAO,CAAC;UAAA,EAAC;UAElDlG,EADF,CAAAC,cAAA,eAA4B,eACD;UACvBD,EAAA,CAAAmB,UAAA,KAAA8G,4CAAA,kBAOC;UA8BHjI,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,cAAuB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UACjCF,EADiC,CAAAG,YAAA,EAAK,EAChC;UAEJH,EADF,CAAAC,cAAA,eAA4B,gBACG;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5CH,EAAA,CAAAC,cAAA,kBAIC;UADCD,EAAA,CAAAI,UAAA,mBAAA8H,0DAAAf,MAAA;YAASH,GAAA,CAAAd,UAAA,CAAW,OAAO,CAAC;YAAA,OAAEiB,MAAA,CAAAI,eAAA,EAAwB;UAAA,EAAC;;UAEvDvH,EAAA,CAAAC,cAAA,eAQC;UACCD,EAAA,CAAAgC,SAAA,gBAME;UAIVhC,EAHM,CAAAG,YAAA,EAAM,EACC,EACL,EACF;UACNH,EAAA,CAAAmB,UAAA,KAAAgH,uCAAA,kBAA0D;UAgB5DnI,EAAA,CAAAG,YAAA,EAAM;;UAGNH,EAAA,CAAAC,cAAA,eAsBC;UANCD,EANA,CAAAI,UAAA,sBAAAgI,0DAAAjB,MAAA;YAAA,OACaH,GAAA,CAAA7C,UAAA,CAAAgD,MAAA,EAAAH,GAAA,CAAAxE,MAAA,CAAAC,SAAA,KAEqB,YAAY,GAAG,WAAW,GACnE,MAAM,CACD;UAAA,EAAG,kBAAA4F,sDAAAlB,MAAA;YAAA,OAGPH,GAAA,CAAAjC,MAAA,CAAAoC,MAAA,EAAAH,GAAA,CAAAxE,MAAA,CAAAC,SAAA,KACwC,YAAY,GAAG,WAAW,GACnE,MAAM,CACD;UAAA,EAAG;UAEDzC,EAAA,CAAAC,cAAA,eAKC;UAHCD,EAAA,CAAAI,UAAA,mBAAAkI,uDAAA;YAAA,OACetB,GAAA,CAAAd,UAAA,CAAAc,GAAA,CAAAxE,MAAA,CAAAC,SAAA,KAAgC,YAAY,GAAG,WACvE,GAAG,MAAM,CAAC;UAAA;UAGCzC,EADF,CAAAC,cAAA,eAA4B,eACD;UAyBvBD,EAxBA,CAAAmB,UAAA,KAAAoH,4CAAA,kBAOC,KAAAC,4CAAA,kBAwBA;UAgBHxI,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,cAAuB;UACrBD,EAAA,CAAAE,MAAA,IACF;UACFF,EADE,CAAAG,YAAA,EAAK,EACD;UAEJH,EADF,CAAAC,cAAA,eAA4B,gBACG;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5CH,EAAA,CAAAC,cAAA,kBASC;UANCD,EAAA,CAAAI,UAAA,mBAAAqI,0DAAAtB,MAAA;YAEVH,GAAA,CAAAd,UAAA,CAAAc,GAAA,CAAAxE,MAAA,CAAAC,SAAA,KAAmD,YAAY,GAAG,WACjE,GAAG,MAAM,CACF;YAAA,OAAkB0E,MAAA,CAAAI,eAAA,EAClB;UAAA,EAAG;;UAEDvH,EAAA,CAAAC,cAAA,eAYC;UACCD,EAAA,CAAAgC,SAAA,gBAME;UAIVhC,EAHM,CAAAG,YAAA,EAAM,EACC,EACL,EACF;UACNH,EAAA,CAAAmB,UAAA,KAAAuH,uCAAA,kBAOC;UA0BT1I,EALM,CAAAG,YAAA,EAAM,EACF,EACF,EAGF;;;UAjhBIH,EAAA,CAAAe,SAAA,GAA8B;UAC9Bf,EADA,CAAA2I,WAAA,yBAA8B,kCAAA3B,GAAA,CAAAlD,oBAAA,OACsC;UAKrC9D,EAAA,CAAAe,SAAA,GAA2B;UAA3Bf,EAAA,CAAAuC,kBAAA,KAAAyE,GAAA,CAAAlD,oBAAA,MAA2B;UAc5D9D,EAAA,CAAAe,SAAA,GAA0C;UAC1Cf,EADA,CAAA4I,WAAA,cAAA5B,GAAA,CAAAzF,WAAA,CAAAC,MAAA,KAA0C,eAAAwF,GAAA,CAAAZ,cAAA,WACG;UAwErCpG,EAAA,CAAAe,SAAA,IAEC;UAFDf,EAAA,CAAA2I,WAAA,cAAA3B,GAAA,CAAAZ,cAAA,+CAEC;UAakBpG,EAAA,CAAAe,SAAA,GAA8B;UAA9Bf,EAAA,CAAAsB,UAAA,SAAA0F,GAAA,CAAAZ,cAAA,WAA8B;UAoBzDpG,EAAA,CAAAe,SAAA,EAA6C;UAC7Cf,EADA,CAAA4I,WAAA,cAAA5B,GAAA,CAAAjF,cAAA,CAAAP,MAAA,KAA6C,eAAAwF,GAAA,CAAAZ,cAAA,cACG;UA+ExCpG,EAAA,CAAAe,SAAA,IAIC;UAJDf,EAAA,CAAA2I,WAAA,cAAA3B,GAAA,CAAAZ,cAAA,kDAIC;UAakBpG,EAAA,CAAAe,SAAA,GAAiC;UAAjCf,EAAA,CAAAsB,UAAA,SAAA0F,GAAA,CAAAZ,cAAA,cAAiC;UA0D5DpG,EAAA,CAAAe,SAAA,GAAyC;UACzCf,EADA,CAAA4I,WAAA,cAAA5B,GAAA,CAAA1E,UAAA,CAAAd,MAAA,KAAyC,eAAAwF,GAAA,CAAAZ,cAAA,UACG;UAanCpG,EAAA,CAAAe,SAAA,GAA0C;UAA1Cf,EAAA,CAAAsB,UAAA,SAAA0F,GAAA,CAAAxE,MAAA,CAAAC,SAAA,qBAA0C;UA8C3CzC,EAAA,CAAAe,SAAA,GAEC;UAFDf,EAAA,CAAA2I,WAAA,cAAA3B,GAAA,CAAAZ,cAAA,8CAEC;UAakBpG,EAAA,CAAAe,SAAA,GAA6B;UAA7Bf,EAAA,CAAAsB,UAAA,SAAA0F,GAAA,CAAAZ,cAAA,UAA6B;UAqBxDpG,EAAA,CAAAe,SAAA,EAA2D;UAG3Df,EAHA,CAAA4I,WAAA,oBAAA5B,GAAA,CAAAxE,MAAA,CAAAC,SAAA,kBAA2D,eAAAuE,GAAA,CAAAxE,MAAA,CAAAC,SAAA,qBACF,cAAAuE,GAAA,CAAAjE,WAAA,CAAAvB,MAAA,KACf,eAAAwF,GAAA,CAAAZ,cAAA,CAAAY,GAAA,CAAAxE,MAAA,CAAAC,SAAA,0CAKzC;UA4BQzC,EAAA,CAAAe,SAAA,GAAuC;UAAvCf,EAAA,CAAAsB,UAAA,SAAA0F,GAAA,CAAAxE,MAAA,CAAAC,SAAA,kBAAuC;UAwBvCzC,EAAA,CAAAe,SAAA,EAA0C;UAA1Cf,EAAA,CAAAsB,UAAA,SAAA0F,GAAA,CAAAxE,MAAA,CAAAC,SAAA,qBAA0C;UAmB7CzC,EAAA,CAAAe,SAAA,GACF;UADEf,EAAA,CAAAuC,kBAAA,MAAAyE,GAAA,CAAAxE,MAAA,CAAAC,SAAA,gDACF;UAmBIzC,EAAA,CAAAe,SAAA,GAMC;UANDf,EAAA,CAAA2I,WAAA,cAAA3B,GAAA,CAAAZ,cAAA,CAAAY,GAAA,CAAAxE,MAAA,CAAAC,SAAA,8EAMC;UAeNzC,EAAA,CAAAe,SAAA,GAIF;UAJEf,EAAA,CAAAsB,UAAA,SAAA0F,GAAA,CAAAZ,cAAA,CAAAY,GAAA,CAAAxE,MAAA,CAAAC,SAAA,0CAIF;;;qBD1fG3C,YAAY,EAAA+I,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA;MAAAC,MAAA;IAAA;;SAIXhG,uBAAuB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}