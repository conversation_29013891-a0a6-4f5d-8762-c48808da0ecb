{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormGroup, ReactiveFormsModule, FormControl, Validators } from '@angular/forms';\nimport { Subscription } from 'rxjs';\nimport { CodeEditorComponent } from '../../../../shared/components/code-editor/code-editor.component';\nimport toolsText from '../constants/tools.json';\nimport { PlaygroundComponent } from 'projects/console/src/app/shared/components/playground/playground.component';\nimport { AvaTextareaComponent, AvaTextboxComponent, ButtonComponent, PopupComponent } from '@ava/play-comp-library';\nimport { MainLayoutComponent } from 'projects/console/src/app/shared/components/main-layout/main-layout.component';\nimport { TOOL_CREATION_USECASE_IDENTIFIER, ToolModes } from '../constants/builtInTools';\nimport { AskAvaWrapperComponent } from 'projects/console/src/app/shared/components/ask-ava-wrapper/ask-ava-wrapper.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../../../../shared/services/tool-execution/tool-execution.service\";\nimport * as i4 from \"../../../../shared/services/tools.service\";\nimport * as i5 from \"../../../../shared/services/prompt-enhance.service\";\nimport * as i6 from \"@shared/auth/services/token-storage.service\";\nimport * as i7 from \"@angular/common\";\nconst _c0 = () => ({\n  \"border\": \"2px solid transparent\",\n  \"background-image\": \"linear-gradient(#ffffff, #ffffff), linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)\",\n  \"background-origin\": \"border-box\",\n  \"background-clip\": \"padding-box, border-box\",\n  \"--button-effect-color\": \"33, 90, 214\"\n});\nconst _c1 = () => ({\n  background: \"linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)\",\n  \"--button-effect-color\": \"33, 90, 214\"\n});\nfunction CreateToolsComponent_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"ava-button\", 35);\n    i0.ɵɵlistener(\"userClick\", function CreateToolsComponent_ng_container_5_Template_ava_button_userClick_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.handleApproval());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction CreateToolsComponent_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"ava-button\", 36);\n    i0.ɵɵlistener(\"userClick\", function CreateToolsComponent_ng_container_8_Template_ava_button_userClick_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSave());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"customStyles\", i0.ɵɵpureFunction0(1, _c1));\n  }\n}\nfunction CreateToolsComponent_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"ava-button\", 37);\n    i0.ɵɵlistener(\"userClick\", function CreateToolsComponent_ng_container_9_Template_ava_button_userClick_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onUpdate());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"customStyles\", i0.ɵɵpureFunction0(1, _c1));\n  }\n}\nfunction CreateToolsComponent_ng_container_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"ava-button\", 38);\n    i0.ɵɵlistener(\"userClick\", function CreateToolsComponent_ng_container_25_Template_ava_button_userClick_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSave());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"customStyles\", i0.ɵɵpureFunction0(1, _c1));\n  }\n}\nfunction CreateToolsComponent_ng_container_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"ava-button\", 39);\n    i0.ɵɵlistener(\"userClick\", function CreateToolsComponent_ng_container_26_Template_ava_button_userClick_1_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onUpdate());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"customStyles\", i0.ɵɵpureFunction0(1, _c1));\n  }\n}\nexport let CreateToolsComponent = /*#__PURE__*/(() => {\n  class CreateToolsComponent {\n    fb;\n    router;\n    route;\n    toolExecutionService;\n    toolsService;\n    promptGenerateService;\n    tokenStorage;\n    codeEditor;\n    toolId = null;\n    isEditMode = false;\n    isCloneMode = false;\n    isExecuteMode = true;\n    isFieldsDisabled = false;\n    showChatInterface = true;\n    selectedTool = null;\n    toolForm;\n    outputToolForm;\n    chatMessages = [];\n    isProcessingChat = false;\n    showAskAvaModal = false;\n    showToolOutput = false;\n    isLoading = false;\n    prompt = new FormControl('');\n    parameterState = {\n      parameters: [],\n      currentParameterIndex: 0,\n      collectedInputs: {},\n      isCollecting: false\n    };\n    executionSubscription = new Subscription();\n    waitingForRestartConfirmation = false;\n    placeholder = toolsText.TOOL_PLACEHOLDER.toolClassDef;\n    labels = toolsText.labels;\n    promptChange = new EventEmitter();\n    validationOutput = '';\n    showValidationOutput = false;\n    validationOutputEditorConfig = {\n      title: 'Tool Compiler',\n      language: 'json',\n      theme: 'light',\n      readOnly: true,\n      height: '250px',\n      placeholder: 'Test the tool before saving or updating for security validations'\n    };\n    editorActions = [{\n      label: 'Select All',\n      style: 'secondary',\n      customClass: '',\n      icon: ''\n    }, {\n      label: 'Reset',\n      style: 'secondary',\n      customClass: '',\n      icon: ''\n    }];\n    originalToolData = null;\n    codeEditorValueToSet = null;\n    // Popup properties\n    showSuccessPopup = false;\n    showErrorPopup = false;\n    popupTitle = '';\n    popupMessage = '';\n    shouldCallExtractParams = false;\n    createToolForm() {\n      return this.fb.group({\n        name: ['', [Validators.required, this.noWhitespaceValidator]],\n        description: ['', [Validators.required, this.noWhitespaceValidator]],\n        toolClassName: ['', [Validators.required, this.noWhitespaceValidator, Validators.pattern(/^[A-Z][a-zA-Z0-9]*$/)]],\n        classDefinition: ['', [Validators.required, this.noWhitespaceValidator]]\n      });\n    }\n    constructor(fb, router, route, toolExecutionService, toolsService, promptGenerateService, tokenStorage) {\n      this.fb = fb;\n      this.router = router;\n      this.route = route;\n      this.toolExecutionService = toolExecutionService;\n      this.toolsService = toolsService;\n      this.promptGenerateService = promptGenerateService;\n      this.tokenStorage = tokenStorage;\n      this.toolForm = this.createToolForm();\n      this.outputToolForm = this.createToolForm();\n    }\n    getUserSignature() {\n      const userSignature = this.tokenStorage.getDaUsername() || '<EMAIL>';\n      return userSignature;\n    }\n    formatLocalDateTime(date = new Date()) {\n      // Format date as LocalDateTime for Java backend (without timezone)\n      // Try to match the format from the example: 2025-06-10T05:09:39.136505\n      const year = date.getFullYear();\n      const month = String(date.getMonth() + 1).padStart(2, '0');\n      const day = String(date.getDate()).padStart(2, '0');\n      const hours = String(date.getHours()).padStart(2, '0');\n      const minutes = String(date.getMinutes()).padStart(2, '0');\n      const seconds = String(date.getSeconds()).padStart(2, '0');\n      // Generate 6-digit microseconds (similar to the example format)\n      const milliseconds = date.getMilliseconds();\n      const microseconds = String(milliseconds * 1000 + Math.floor(Math.random() * 1000)).padStart(6, '0');\n      return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.${microseconds}`;\n    }\n    ngOnInit() {\n      const toolIdParam = this.route.snapshot.paramMap.get('id');\n      this.toolId = toolIdParam ? parseInt(toolIdParam, 10) : null;\n      const urlPath = this.router.url;\n      this.isEditMode = urlPath.includes('/edit/');\n      this.isCloneMode = urlPath.includes('/clone/');\n      this.isExecuteMode = urlPath.includes('/execute/');\n      this.isFieldsDisabled = this.isExecuteMode;\n      console.log('ngOnInit - toolId:', this.toolId, 'isEditMode:', this.isEditMode, 'isCloneMode:', this.isCloneMode, 'isExecuteMode:', this.isExecuteMode);\n      if ((this.isEditMode || this.isCloneMode || this.isExecuteMode) && this.toolId) {\n        this.loadToolData(this.toolId);\n        if (this.isExecuteMode) {\n          this.initializeChatMessages();\n        }\n      }\n      // Add listener for toolClassName changes to update classDefinition validation\n      this.toolForm.controls['toolClassName'].valueChanges.subscribe(() => {\n        this.toolForm.controls['classDefinition'].updateValueAndValidity();\n      });\n      // Window resize will be handled by HostListener\n    }\n    ngAfterViewInit() {\n      // If value was loaded before editor was ready, set it now\n      if (this.codeEditor && this.codeEditorValueToSet) {\n        this.codeEditor.setValue(this.codeEditorValueToSet);\n        this.codeEditorValueToSet = null;\n        // Force Monaco editor layout after setting content\n        setTimeout(() => {\n          if (this.codeEditor?.isReady) {\n            this.codeEditor['editor']?.layout();\n          }\n        }, 100);\n      }\n    }\n    ngOnDestroy() {\n      if (this.executionSubscription) {\n        this.executionSubscription.unsubscribe();\n      }\n    }\n    initializeChatMessages() {\n      this.chatMessages = [{\n        from: 'ai',\n        text: \"Hi! Welcome to the tool testing playground. I'll help you test your tool by collecting the required parameters.\"\n      }];\n    }\n    onSave() {\n      // Mark all fields as touched to show validation errors\n      this.markFormGroupTouched(this.toolForm);\n      // Check if form is valid\n      if (!this.toolForm.valid) {\n        this.showValidationErrorPopup();\n        return;\n      }\n      // Handle create mode only\n      if (!this.isEditMode) {\n        // For create and clone\n        // New payload structure for creating tools\n        const createPayload = {\n          name: this.toolForm.get('name')?.value,\n          description: this.toolForm.get('description')?.value,\n          toolConfigs: {\n            image: '',\n            // Default empty image for new tools\n            tool_class_def: this.toolForm.get('classDefinition')?.value,\n            tool_class_name: this.toolForm.get('toolClassName')?.value\n          },\n          createdBy: this.getUserSignature(),\n          modifiedBy: this.getUserSignature()\n        };\n        console.log('Creating new tool with payload:', createPayload);\n        this.toolsService.addNewUserTool(createPayload).subscribe({\n          next: response => {\n            console.log('Tool creation response:', response);\n            this.isExecuteMode = true;\n            this.showChatInterface = true;\n            this.initializeChatMessages();\n            this.resetParameterState();\n            // Show success popup with API response\n            const successMessage = response?.message || 'Tool created successfully!';\n            this.showSuccessMessage('Tool Created', successMessage);\n          },\n          error: error => {\n            console.error('Error creating tool', error);\n            // Show error popup with API response\n            const errorMessage = error?.error?.message || error?.message || 'Error creating tool. Please try again.';\n            this.showErrorMessage('Creation Failed', errorMessage);\n          }\n        });\n      }\n    }\n    onUpdate() {\n      // Mark all fields as touched to show validation errors\n      this.markFormGroupTouched(this.toolForm);\n      // Check if form is valid\n      if (!this.toolForm.valid) {\n        this.showValidationErrorPopup();\n        return;\n      }\n      // Handle update mode for both edit and execute\n      if (this.isEditMode && this.toolId) {\n        // Transform to new API payload structure for update using change_request endpoint\n        const updatePayload = {\n          id: parseInt(this.toolId.toString()),\n          name: this.toolForm.get('name')?.value,\n          description: this.toolForm.get('description')?.value,\n          createdBy: this.originalToolData?.createdBy || this.getUserSignature(),\n          modifiedBy: this.getUserSignature(),\n          // Use original timestamp if available, otherwise format new one\n          createdAt: this.originalToolData?.createdAt || this.originalToolData?.createTimestamp || this.originalToolData?.createDate || this.formatLocalDateTime(),\n          modifiedAt: this.formatLocalDateTime(),\n          isDeleted: true,\n          // Set to true for change request\n          toolConfigs: {\n            image: '',\n            // Default empty image for new tools\n            tool_class_def: this.toolForm.get('classDefinition')?.value,\n            tool_class_name: this.toolForm.get('toolClassName')?.value\n          }\n        };\n        console.log('Update payload being sent:', updatePayload);\n        this.toolsService.updateUserTool(updatePayload).subscribe({\n          next: response => {\n            console.log('Tool update response:', response);\n            this.isExecuteMode = true;\n            this.showChatInterface = true;\n            this.initializeChatMessages();\n            this.resetParameterState();\n            // Show success popup with API response\n            const successMessage = response?.message || 'Tool updated successfully!';\n            this.showSuccessMessage('Tool Updated', successMessage);\n          },\n          error: error => {\n            console.error('Error updating tool:', error);\n            console.error('Error details:', error.error);\n            console.error('Status:', error.status);\n            // Show error popup with API response\n            const errorMessage = error?.error?.message || error?.message || 'Error updating tool. Please try again.';\n            this.showErrorMessage('Update Failed', errorMessage);\n          }\n        });\n      }\n    }\n    extractParameters() {\n      const toolClassDef = this.toolForm.get('classDefinition')?.value;\n      const toolClassName = this.toolForm.get('toolClassName')?.value;\n      const toolName = this.toolForm.get('name')?.value;\n      if (!toolClassDef || !toolClassName) {\n        this.addChatMessage('ai', 'Error: Tool class definition or class name is missing.');\n        return;\n      }\n      const useCase = 'PARAMETER_EXTRACTOR';\n      const useCaseIdentifier = 'PARAMETER_EXTRACTOR@ADD@GOPAL@TEST_GOPAL@GOPALTEST';\n      this.isProcessingChat = true;\n      this.addChatMessage('ai', 'Analyzing your tool to extract parameters...');\n      this.promptGenerateService.modelApi(toolClassDef, useCase, false, useCaseIdentifier).subscribe({\n        next: response => {\n          console.log('Parameter extraction response:', response);\n          try {\n            const parametersText = response?.response?.choices?.[0]?.text;\n            if (!parametersText) {\n              throw new Error('No parameters found in response');\n            }\n            const parametersObj = JSON.parse(parametersText);\n            const parameters = Object.keys(parametersObj).map(key => ({\n              name: key,\n              type: parametersObj[key]\n            }));\n            if (parameters.length === 0) {\n              this.isProcessingChat = false;\n              this.addChatMessage('ai', 'No parameters found for this tool. The tool might not require any input parameters.');\n              return;\n            }\n            this.parameterState = {\n              parameters: parameters,\n              currentParameterIndex: 0,\n              collectedInputs: {},\n              isCollecting: true\n            };\n            this.isProcessingChat = false;\n            this.promptForNextParameter();\n          } catch (error) {\n            console.error('Error parsing parameters:', error);\n            this.isProcessingChat = false;\n            this.addChatMessage('ai', 'Error: Failed to parse extracted parameters. Please check your tool definition.');\n          }\n        },\n        error: error => {\n          console.error('Parameter extraction error', error);\n          this.isProcessingChat = false;\n          this.addChatMessage('ai', 'Error: Failed to extract parameters from your tool. Please check your tool definition.');\n        }\n      });\n    }\n    promptForNextParameter() {\n      if (this.parameterState.currentParameterIndex < this.parameterState.parameters.length) {\n        const currentParam = this.parameterState.parameters[this.parameterState.currentParameterIndex];\n        const message = `Please enter input for parameter \"${currentParam.name}\" (type: ${currentParam.type}):`;\n        this.addChatMessage('ai', message);\n      }\n    }\n    addChatMessage(from, text) {\n      this.chatMessages = [...this.chatMessages, {\n        from,\n        text\n      }];\n    }\n    handleChatMessage(message) {\n      if (!this.isExecuteMode) return;\n      this.addChatMessage('user', message);\n      if (this.waitingForRestartConfirmation) {\n        if (message.toLowerCase().trim() === 'yes') {\n          this.waitingForRestartConfirmation = false;\n          this.resetParameterState();\n          this.addChatMessage('ai', 'Restarting the parameter collection process...');\n          this.extractParameters();\n        } else {\n          this.waitingForRestartConfirmation = false;\n          this.addChatMessage('ai', 'Okay, feel free to ask if you need any further assistance.');\n        }\n      } else if (this.parameterState.isCollecting) {\n        this.handleParameterInput(message);\n      } else {\n        this.addChatMessage('ai', 'Hello');\n      }\n    }\n    handleParameterInput(input) {\n      const currentParam = this.parameterState.parameters[this.parameterState.currentParameterIndex];\n      let processedInput = input;\n      try {\n        switch (currentParam.type) {\n          case 'number':\n            processedInput = parseFloat(input);\n            if (isNaN(processedInput)) {\n              this.addChatMessage('ai', `Invalid number format. Please enter a valid number for \"${currentParam.name}\":`);\n              return;\n            }\n            break;\n          case 'boolean':\n            const lowerInput = input.toLowerCase();\n            if (lowerInput === 'true' || lowerInput === '1' || lowerInput === 'yes') {\n              processedInput = true;\n            } else if (lowerInput === 'false' || lowerInput === '0' || lowerInput === 'no') {\n              processedInput = false;\n            } else {\n              this.addChatMessage('ai', `Invalid boolean format. Please enter true/false for \"${currentParam.name}\":`);\n              return;\n            }\n            break;\n          case 'object':\n            try {\n              processedInput = JSON.parse(input);\n            } catch {\n              this.addChatMessage('ai', `Invalid JSON format. Please enter a valid JSON object for \"${currentParam.name}\":`);\n              return;\n            }\n            break;\n          case 'array':\n            try {\n              processedInput = JSON.parse(input);\n              if (!Array.isArray(processedInput)) {\n                throw new Error('Not an array');\n              }\n            } catch {\n              this.addChatMessage('ai', `Invalid array format. Please enter a valid JSON array for \"${currentParam.name}\":`);\n              return;\n            }\n            break;\n        }\n        this.parameterState.collectedInputs[currentParam.name] = processedInput;\n        this.parameterState.currentParameterIndex++;\n        if (this.parameterState.currentParameterIndex >= this.parameterState.parameters.length) {\n          this.executeToolWithParameters();\n        } else {\n          this.promptForNextParameter();\n        }\n      } catch (error) {\n        console.error('Error processing parameter input:', error);\n        this.addChatMessage('ai', `Error processing input for \"${currentParam.name}\". Please try again:`);\n      }\n    }\n    executeToolWithParameters() {\n      this.parameterState.isCollecting = false;\n      this.isProcessingChat = true;\n      this.addChatMessage('ai', 'All parameters collected! Executing your tool...');\n      const payload = {\n        class_definition: this.toolForm.get('classDefinition')?.value,\n        class_name: this.toolForm.get('toolClassName')?.value,\n        inputs: this.parameterState.collectedInputs\n      };\n      this.toolsService.testTool(payload).subscribe({\n        next: response => {\n          console.log('Tool execution response:', response);\n          this.isProcessingChat = false;\n          if (response.status === 'success') {\n            this.addChatMessage('ai', `Tool executed successfully! Output: ${response.output}`);\n          } else {\n            this.addChatMessage('ai', `Tool execution failed: ${response.detail || 'Unknown error'}`);\n          }\n          this.waitingForRestartConfirmation = true;\n          setTimeout(() => {\n            this.addChatMessage('ai', 'Would you like to test the tool again with different parameters? (Type \"yes\" to restart or anything else to continue)');\n          }, 1000);\n        },\n        error: error => {\n          console.error('Tool execution error:', error);\n          this.isProcessingChat = false;\n          this.addChatMessage('ai', `Tool execution failed: ${error?.error?.message || 'Unknown error occurred'}`);\n        }\n      });\n    }\n    onExit() {\n      // Always navigate back to tools landing page regardless of mode\n      const returnPage = this.route.snapshot.queryParamMap.get('returnPage');\n      const pageNumber = returnPage ? parseInt(returnPage) : 1;\n      console.log('Exiting create-tools component, navigating to tools landing page');\n      this.router.navigate(['/libraries/tools'], {\n        queryParams: {\n          page: pageNumber\n        }\n      });\n    }\n    resetParameterState() {\n      this.parameterState = {\n        parameters: [],\n        currentParameterIndex: 0,\n        collectedInputs: {},\n        isCollecting: false\n      };\n    }\n    getControl(name) {\n      return this.toolForm.get(name);\n    }\n    // Custom validator for whitespace\n    noWhitespaceValidator(control) {\n      const value = control.value;\n      if (value && typeof value === 'string' && value.trim().length === 0) {\n        return {\n          invalidInput: true\n        };\n      }\n      return null;\n    }\n    // // Custom validator for class definition\n    // validateClassDef(control: AbstractControl): ValidationErrors | null {\n    //   const className = this.toolForm?.controls['toolClassName'].value?.trim();\n    //   const classDef = control.value;\n    //   if (!className) return null;\n    //   const classRegex = new RegExp(`class\\\\s+${className}\\\\s*\\\\(\\\\s*BaseTool\\\\s*\\\\)`);\n    //   if (!classRegex.test(classDef)) {\n    //     return { invalidClass: { invalidClass: `Please Define: \"class ${className}(BaseTool)\"` }};\n    //   }\n    //   if (!classDef.includes('_run')) {\n    //     return { invalidClass: { invalidClass: `Please Define: \"_run\" Method` }};\n    //   }\n    //   return null;\n    // }\n    // Helper method to mark all form fields as touched\n    markFormGroupTouched(formGroup) {\n      Object.keys(formGroup.controls).forEach(key => {\n        const control = formGroup.get(key);\n        control?.markAsTouched();\n        if (control instanceof FormGroup) {\n          this.markFormGroupTouched(control);\n        }\n      });\n    }\n    // Show validation error popup\n    showValidationErrorPopup() {\n      const errors = [];\n      // Check each field for errors\n      Object.keys(this.toolForm.controls).forEach(fieldName => {\n        const error = this.getFieldError(fieldName);\n        if (error) {\n          const fieldLabel = this.getFieldLabel(fieldName);\n          errors.push(`${fieldLabel}: ${error}`);\n        }\n      });\n      if (errors.length > 0) {\n        this.popupTitle = 'Validation Error';\n        this.popupMessage = errors.join('\\n\\n');\n        this.showErrorPopup = true;\n      }\n    }\n    // Get field label for error messages\n    getFieldLabel(fieldName) {\n      switch (fieldName) {\n        case 'name':\n          return this.labels.toolName;\n        case 'toolClassName':\n          return this.labels.toolClassName;\n        case 'description':\n          return this.labels.description;\n        case 'classDefinition':\n          return this.labels.toolClassDefinition;\n        default:\n          return fieldName;\n      }\n    }\n    // // Custom validator for whitespace\n    // noWhitespaceValidator(control: AbstractControl): ValidationErrors | null {\n    //   const value = control.value;\n    //   if (value && typeof value === 'string' && value.trim().length === 0) {\n    //     return { invalidInput: true };\n    //   }\n    //   return null;\n    // }\n    // // // Custom validator for class definition\n    // // validateClassDef(control: AbstractControl): ValidationErrors | null {\n    // //   const className = this.toolForm?.controls['toolClassName'].value?.trim();\n    // //   const classDef = control.value;\n    // //   if (!className) return null;\n    // //   const classRegex = new RegExp(`class\\\\s+${className}\\\\s*\\\\(\\\\s*BaseTool\\\\s*\\\\)`);\n    // //   if (!classRegex.test(classDef)) {\n    // //     return { invalidClass: { invalidClass: `Please Define: \"class ${className}(BaseTool)\"` }};\n    // //   }\n    // //   if (!classDef.includes('_run')) {\n    // //     return { invalidClass: { invalidClass: `Please Define: \"_run\" Method` }};\n    // //   }\n    // //   return null;\n    // // }\n    // // Helper method to mark all form fields as touched\n    // markFormGroupTouched(formGroup: FormGroup): void {\n    //   Object.keys(formGroup.controls).forEach(key => {\n    //     const control = formGroup.get(key);\n    //     control?.markAsTouched();\n    //     if (control instanceof FormGroup) {\n    //       this.markFormGroupTouched(control);\n    //     }\n    //   });\n    // }\n    // // Show validation error popup\n    // showValidationErrorPopup(): void {\n    //   const errors: string[] = [];\n    //   // Check each field for errors\n    //   Object.keys(this.toolForm.controls).forEach(fieldName => {\n    //     const error = this.getFieldError(fieldName);\n    //     if (error) {\n    //       const fieldLabel = this.getFieldLabel(fieldName);\n    //       errors.push(`${fieldLabel}: ${error}`);\n    //     }\n    //   });\n    //   if (errors.length > 0) {\n    //     this.popupTitle = 'Validation Error';\n    //     this.popupMessage = errors.join('\\n\\n');\n    //     this.showErrorPopup = true;\n    //   }\n    // }\n    // // Get field label for error messages\n    // getFieldLabel(fieldName: string): string {\n    //   switch (fieldName) {\n    //     case 'name': return this.labels.toolName;\n    //     case 'toolClassName': return this.labels.toolClassName;\n    //     case 'description': return this.labels.description;\n    //     case 'classDefinition': return this.labels.toolClassDefinition;\n    //     default: return fieldName;\n    //   }\n    // }\n    getOutputControl(name) {\n      return this.outputToolForm.get(name);\n    }\n    getFieldError(fieldName) {\n      const field = this.toolForm.get(fieldName);\n      if (field && field.invalid && (field.touched || field.dirty)) {\n        if (field.errors?.['required']) {\n          if (fieldName === 'name') return this.labels.errorToolNameRequired;\n          if (fieldName === 'toolClassName') return this.labels.errorToolClassNameRequired;\n          if (fieldName === 'description') return this.labels.errorDescriptionRequired;\n          if (fieldName === 'classDefinition') return this.labels.errorToolConfigRequired;\n          return this.labels.errorRequired || 'This field is required';\n        }\n        if (field.errors?.['invalidInput']) {\n          return 'Please enter a valid value (whitespace only is not allowed)';\n        }\n        if (field.errors?.['pattern']) {\n          if (fieldName === 'toolClassName') {\n            return 'Please enter a valid class name (must start with uppercase letter and contain only letters and numbers)';\n          }\n        }\n        if (field.errors?.['invalidClass']) {\n          return field.errors['invalidClass'].invalidClass;\n        }\n        // Add more error types as needed\n      }\n      return '';\n    }\n    validateCode = () => {\n      this.validateTool();\n    };\n    validateTool() {\n      this.showValidationOutput = false;\n      this.validationOutput = '';\n      const useCase = 'VALIDATE_TOOLS';\n      const useCaseIdentifier = 'VALIDATE_TOOLS@ASCENDION@PLATFORM_ENGINEERING@AVA@DIGITAL_ASCENDER';\n      const toolClassDef = this.toolForm.controls['classDefinition'].value;\n      this.promptGenerateService.modelApi(toolClassDef, useCase, false, useCaseIdentifier).subscribe({\n        next: res => {\n          let responseText = res?.response?.choices?.[0]?.text;\n          if (!responseText) {\n            this.validationOutput = 'Unable to validate, please try again.';\n            this.showValidationOutput = true;\n            if (this.codeEditor) this.codeEditor.hideProcessingLoader();\n            return;\n          }\n          // Remove markdown code block if present\n          responseText = responseText.replace(/```json\\n?/, '').replace(/```\\n?$/, '');\n          try {\n            const parsed = JSON.parse(responseText);\n            // Format as plain text\n            let formatted = '';\n            if (parsed.issues && Array.isArray(parsed.issues) && parsed.issues.length > 0) {\n              parsed.issues.forEach((issue, idx) => {\n                formatted += `Issue ${idx + 1}:\\n`;\n                formatted += `  Type: ${issue.type}\\n`;\n                formatted += `  Description: ${issue.description}\\n`;\n                formatted += `  Severity: ${issue.severity}\\n`;\n                formatted += `  Line Number: ${issue.line_number}\\n`;\n                formatted += `  Suggestion: ${issue.suggestion}\\n\\n`;\n              });\n            } else {\n              formatted = 'No issues found.';\n            }\n            this.validationOutput = formatted;\n          } catch (e) {\n            this.validationOutput = responseText;\n          }\n          this.showValidationOutput = true;\n          if (this.codeEditor) this.codeEditor.hideProcessingLoader();\n        },\n        error: e => {\n          this.validationOutput = 'Error: ' + (e?.error?.message || 'Unknown error');\n          this.showValidationOutput = true;\n          if (this.codeEditor) this.codeEditor.hideProcessingLoader();\n        }\n      });\n    }\n    loadToolData(toolId) {\n      this.toolsService.getUserToolDetails(toolId).subscribe(response => {\n        console.log('API response:', response); // Debug log\n        const tool = response.tools && response.tools[0];\n        console.log('Tool for patching:', tool); // Debug log\n        console.log('Tool ID:', tool?.toolId);\n        if (tool) {\n          this.originalToolData = tool;\n          console.log('Original tool data structure:', JSON.stringify(tool, null, 2));\n          this.toolForm.patchValue({\n            name: this.isCloneMode ? '' : tool.toolName || '',\n            description: tool.toolDescription || '',\n            toolClassName: tool.toolClassName || '',\n            classDefinition: tool.toolClassDef || ''\n          });\n          // Set code editor value, but only if editor is ready\n          if (this.codeEditor) {\n            this.codeEditor.setValue(tool.toolClassDef || '');\n            // Force Monaco editor layout after setting content\n            setTimeout(() => {\n              if (this.codeEditor?.isReady) {\n                this.codeEditor['editor']?.layout();\n              }\n            }, 100);\n          } else {\n            this.codeEditorValueToSet = tool.toolClassDef || '';\n          }\n          // Auto-start parameter extraction when in execute mode\n          if (this.isExecuteMode) {\n            console.log('Auto-starting parameter extraction in execute mode...');\n            // Small delay to ensure form is fully populated and UI is ready\n            setTimeout(() => {\n              this.extractParameters();\n            }, 500);\n          }\n        }\n      }, error => {\n        console.error('Error loading tool data:', error);\n      });\n    }\n    //Drop Down\n    promptOptions = [{\n      value: 'default',\n      name: 'Choose Prompt'\n    }, {\n      value: 'ruby-developer',\n      name: 'Senior Ruby Developer'\n    }, {\n      value: 'python-developer',\n      name: 'Python Developer'\n    }, {\n      value: 'data-scientist',\n      name: 'Data Scientist'\n    }, {\n      value: 'frontend-developer',\n      name: 'Frontend Developer'\n    }];\n    onPromptChanged(option) {\n      console.log('Prompt changed in parent:', option);\n      // your logic to handle selected prompt\n    }\n    onEditorAction(idx) {\n      if (!this.codeEditor) return;\n      if (idx === 0) this.codeEditor.selectAll();\n      if (idx === 1) this.codeEditor.clear();\n    }\n    handleApproval() {\n      if (!this.isEditMode || !this.toolId) {\n        console.error('Cannot send for approval: Tool must be saved first');\n        return;\n      }\n      const returnPage = this.route.snapshot.queryParamMap.get('returnPage');\n      const pageNumber = returnPage ? parseInt(returnPage) : 1;\n      // Create approval payload with the exact structure required\n      const approvalPayload = {\n        id: parseInt(this.toolId.toString()),\n        name: this.toolForm.get('name')?.value,\n        description: this.toolForm.get('description')?.value,\n        createdBy: this.originalToolData?.createdBy || this.getUserSignature(),\n        modifiedBy: this.getUserSignature(),\n        // Use current user signature for modifiedBy\n        createdAt: this.originalToolData?.createTimestamp || this.formatLocalDateTime(),\n        'put ': this.formatLocalDateTime(),\n        // Using LocalDateTime format without timezone\n        isDeleted: true,\n        // Set to true for approval request\n        toolConfigs: {\n          image: this.originalToolData?.toolImage || '',\n          def: this.toolForm.get('classDefinition')?.value,\n          CHANGE2: this.toolForm.get('toolClassName')?.value || 'TEST'\n        }\n      };\n      console.log('Sending for approval with payload:', approvalPayload);\n      console.log('Approval payload structure:', JSON.stringify(approvalPayload, null, 2));\n      console.log('API Endpoint: /v2/api/admin/ava/force/da/userTools/change_request');\n      this.toolsService.updateUserTool(approvalPayload).subscribe(response => {\n        console.log('Approval request successful:', response);\n        // You can add a success message or redirect here\n        this.router.navigate(['/libraries/tools'], {\n          queryParams: {\n            page: pageNumber\n          }\n        });\n      }, error => {\n        console.error('Error sending for approval:', error);\n        console.error('Error details:', error.error);\n        console.error('Status:', error.status);\n        console.error('Status text:', error.statusText);\n      });\n    }\n    // Popup handling methods\n    showSuccessMessage(title, message) {\n      console.log('showSuccessMessage called:', title, message);\n      this.popupTitle = title;\n      this.popupMessage = message;\n      this.showSuccessPopup = true;\n      this.shouldCallExtractParams = true;\n      console.log('showSuccessPopup set to:', this.showSuccessPopup);\n    }\n    showErrorMessage(title, message) {\n      console.log('showErrorMessage called:', title, message);\n      this.popupTitle = title;\n      this.popupMessage = message;\n      this.showErrorPopup = true;\n      this.shouldCallExtractParams = false;\n      console.log('showErrorPopup set to:', this.showErrorPopup);\n    }\n    closeSuccessPopup() {\n      console.log('closeSuccessPopup called');\n      this.showSuccessPopup = false;\n      this.popupTitle = '';\n      this.popupMessage = '';\n    }\n    closeErrorPopup() {\n      console.log('closeErrorPopup called');\n      this.showErrorPopup = false;\n      this.popupTitle = '';\n      this.popupMessage = '';\n    }\n    // Handle success popup confirmation - call extract params API\n    onSuccessConfirm() {\n      console.log('onSuccessConfirm called, shouldCallExtractParams:', this.shouldCallExtractParams);\n      this.closeSuccessPopup();\n      if (this.shouldCallExtractParams) {\n        console.log('Calling extractParameters...');\n        this.extractParameters();\n      }\n    }\n    // Get tool name for display in playground\n    getToolDisplayName() {\n      const toolName = this.toolForm.get('name')?.value;\n      return toolName || 'Tool Name';\n    }\n    onClickGenerate(prompt) {\n      this.isLoading = true;\n      this.promptGenerateService.modelApi(prompt, ToolModes.toolCreation, false, TOOL_CREATION_USECASE_IDENTIFIER).subscribe({\n        next: res => {\n          let generatedPrompt = {};\n          try {\n            generatedPrompt = JSON.parse(res?.response?.choices[0]?.text);\n          } catch (error) {\n            // this.askAiModal.disablePrimaryButton = true;\n            // this.errorMsg = 'Unable To Generate Tool Definition. Please Try Again.';\n            return;\n          }\n          // this.askAiModal.disablePrimaryButton = false;\n          const {\n            tool_name,\n            tool_class,\n            tool_description,\n            tool_class_defination\n          } = generatedPrompt;\n          this.outputToolForm.patchValue({\n            name: tool_name,\n            toolClassName: tool_class,\n            description: tool_description,\n            classDefinition: tool_class_defination.replace(/```\\n?$/, '')\n          });\n          this.showToolOutput = true;\n          this.isLoading = false;\n        },\n        error: () => {\n          this.isLoading = false;\n        }\n      });\n    }\n    toggleAskAvaModal(show = true) {\n      this.showAskAvaModal = show;\n    }\n    onUse() {\n      const formValue = this.outputToolForm.getRawValue();\n      this.toolForm.patchValue(formValue);\n      this.codeEditor.setValue(formValue.classDefinition);\n      this.outputToolForm.reset();\n      this.showToolOutput = false;\n      this.prompt.reset('');\n      this.toggleAskAvaModal(false);\n    }\n    onReset() {\n      this.outputToolForm.reset();\n      this.showToolOutput = false;\n    }\n    onCancle() {\n      this.prompt.reset('');\n      this.onReset();\n      this.toggleAskAvaModal(false);\n    }\n    onClose() {\n      this.toggleAskAvaModal(false);\n      this.onCancle();\n    }\n    static ɵfac = function CreateToolsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CreateToolsComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.ToolExecutionService), i0.ɵɵdirectiveInject(i4.ToolsService), i0.ɵɵdirectiveInject(i5.PromptEnhanceService), i0.ɵɵdirectiveInject(i6.TokenStorageService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CreateToolsComponent,\n      selectors: [[\"app-create-tools\"]],\n      viewQuery: function CreateToolsComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(CodeEditorComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.codeEditor = _t.first);\n        }\n      },\n      outputs: {\n        promptChange: \"promptChange\"\n      },\n      decls: 49,\n      vars: 93,\n      consts: [[\"codeEditor\", \"\"], [\"leftPaneTitle\", \"Tool Description\", \"rightPaneTitle\", \"Tool Playground\", 3, \"showRightPane\"], [\"right-header\", \"\"], [1, \"right-header-content\"], [1, \"pane-title\"], [4, \"ngIf\"], [\"center-header\", \"\", 1, \"center-header-buttons\"], [\"variant\", \"secondary\", \"size\", \"small\", 3, \"userClick\", \"label\", \"customStyles\"], [\"left\", \"\", 1, \"tool-description-pane\"], [1, \"form-field-group\"], [\"id\", \"toolName\", \"name\", \"toolName\", \"placeholder\", \"Enter tool name\", \"variant\", \"primary\", \"size\", \"md\", 3, \"formControl\", \"label\", \"fullWidth\", \"required\", \"readonly\"], [\"id\", \"toolClassName\", \"name\", \"toolClassName\", \"placeholder\", \"Enter tool class\", \"variant\", \"primary\", \"size\", \"md\", 3, \"formControl\", \"label\", \"fullWidth\", \"required\", \"readonly\"], [\"id\", \"description\", \"name\", \"description\", \"placeholder\", \"Enter description\", \"size\", \"md\", 3, \"label\", \"formControl\", \"rows\", \"fullWidth\", \"required\", \"readonly\"], [\"center\", \"\", 1, \"tool-editor-pane\"], [1, \"custom-center-header\"], [1, \"header-buttons\", \"d-flex\"], [\"variant\", \"secondary\", \"size\", \"small\", \"iconName\", \"WandSparkles\", 3, \"userClick\", \"label\", \"customStyles\"], [1, \"editor-container\"], [\"language\", \"python\", \"customCssClass\", \"tools-monaco-editor\", 3, \"primaryButtonSelected\", \"actionButtonClicked\", \"placeholder\", \"Control\", \"footerText\", \"actionButtons\", \"readonly\"], [1, \"validation-output-section\"], [\"customCssClass\", \"validation-json-editor\", 3, \"title\", \"language\", \"theme\", \"readonly\", \"height\", \"value\", \"placeholder\"], [\"right\", \"\", 1, \"tool-playground-pane\"], [1, \"playground-container\"], [3, \"promptChange\", \"messageSent\", \"approvalRequested\", \"promptOptions\", \"messages\", \"isLoading\", \"showChatInteractionToggles\", \"showAiPrincipleToggle\", \"showDropdown\", \"showAgentNameInput\", \"displayedAgentName\", \"agentNamePlaceholder\", \"showApprovalButton\"], [\"headerIconName\", \"check-circle\", \"iconColor\", \"#28a745\", 3, \"confirm\", \"closed\", \"show\", \"title\", \"message\", \"showHeaderIcon\", \"showClose\", \"showCancel\", \"showConfirm\", \"confirmButtonLabel\", \"confirmButtonVariant\", \"confirmButtonBackground\"], [\"headerIconName\", \"alert-circle\", \"iconColor\", \"#dc3545\", 3, \"confirm\", \"closed\", \"show\", \"title\", \"message\", \"showHeaderIcon\", \"showClose\", \"showCancel\", \"showConfirm\", \"confirmButtonLabel\", \"confirmButtonVariant\", \"confirmButtonBackground\"], [3, \"oNClickGenerate\", \"oNClickClosed\", \"oNClickUse\", \"oNClickReset\", \"oNClickCancel\", \"show\", \"prompt\", \"isLoading\", \"showOutput\"], [3, \"formGroup\"], [1, \"row\", \"g-2\"], [1, \"col-md-6\"], [\"id\", \"toolName\", \"name\", \"toolName\", \"placeholder\", \"Enter tool name\", \"variant\", \"primary\", \"size\", \"md\", 3, \"formControl\", \"label\", \"readonly\"], [\"id\", \"toolClassName\", \"name\", \"toolClassName\", \"placeholder\", \"Enter tool class\", \"variant\", \"primary\", \"size\", \"md\", 3, \"formControl\", \"label\", \"readonly\"], [1, \"col-md-12\"], [\"id\", \"description\", \"name\", \"description\", \"placeholder\", \"Enter description\", \"size\", \"md\", 3, \"label\", \"formControl\", \"rows\", \"fullWidth\", \"readonly\"], [\"id\", \"outputEditor\", \"language\", \"python\", \"customCssClass\", \"tools-monaco-editor\", 3, \"title\", \"value\", \"footerText\", \"readonly\"], [\"label\", \"Send for Approval\", \"variant\", \"primary\", \"size\", \"small\", 1, \"approval-btn\", 3, \"userClick\"], [\"label\", \"Save\", \"variant\", \"primary\", \"size\", \"small\", 3, \"userClick\", \"customStyles\"], [\"label\", \"Update\", \"variant\", \"primary\", \"size\", \"small\", 3, \"userClick\", \"customStyles\"], [\"label\", \"Save & Send Approval\", \"variant\", \"primary\", \"size\", \"small\", 3, \"userClick\", \"customStyles\"], [\"label\", \"Update & Send Approval\", \"variant\", \"primary\", \"size\", \"small\", 3, \"userClick\", \"customStyles\"]],\n      template: function CreateToolsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"main-layout\", 1);\n          i0.ɵɵelementContainerStart(1, 2);\n          i0.ɵɵelementStart(2, \"div\", 3)(3, \"span\", 4);\n          i0.ɵɵtext(4, \"Tool Playground\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(5, CreateToolsComponent_ng_container_5_Template, 2, 0, \"ng-container\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"ava-button\", 7);\n          i0.ɵɵlistener(\"userClick\", function CreateToolsComponent_Template_ava_button_userClick_7_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onExit());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, CreateToolsComponent_ng_container_8_Template, 2, 2, \"ng-container\", 5)(9, CreateToolsComponent_ng_container_9_Template, 2, 2, \"ng-container\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 8)(11, \"div\", 9);\n          i0.ɵɵelement(12, \"ava-textbox\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 9);\n          i0.ɵɵelement(14, \"ava-textbox\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"div\", 9);\n          i0.ɵɵelement(16, \"ava-textarea\", 12);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"div\", 13)(18, \"div\", 14)(19, \"span\", 4);\n          i0.ɵɵtext(20, \"Tool Editor\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(21, \"hr\");\n          i0.ɵɵelementStart(22, \"div\", 15)(23, \"ava-button\", 16);\n          i0.ɵɵlistener(\"userClick\", function CreateToolsComponent_Template_ava_button_userClick_23_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.toggleAskAvaModal());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"ava-button\", 7);\n          i0.ɵɵlistener(\"userClick\", function CreateToolsComponent_Template_ava_button_userClick_24_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onExit());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(25, CreateToolsComponent_ng_container_25_Template, 2, 2, \"ng-container\", 5)(26, CreateToolsComponent_ng_container_26_Template, 2, 2, \"ng-container\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"div\", 17)(28, \"app-code-editor\", 18, 0);\n          i0.ɵɵlistener(\"primaryButtonSelected\", function CreateToolsComponent_Template_app_code_editor_primaryButtonSelected_28_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.validateCode());\n          })(\"actionButtonClicked\", function CreateToolsComponent_Template_app_code_editor_actionButtonClicked_28_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onEditorAction($event));\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"div\", 19);\n          i0.ɵɵelement(31, \"app-code-editor\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"div\", 21)(33, \"div\", 22)(34, \"app-playground\", 23);\n          i0.ɵɵlistener(\"promptChange\", function CreateToolsComponent_Template_app_playground_promptChange_34_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onPromptChanged($event));\n          })(\"messageSent\", function CreateToolsComponent_Template_app_playground_messageSent_34_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.handleChatMessage($event));\n          })(\"approvalRequested\", function CreateToolsComponent_Template_app_playground_approvalRequested_34_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.handleApproval());\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(35, \"ava-popup\", 24);\n          i0.ɵɵlistener(\"confirm\", function CreateToolsComponent_Template_ava_popup_confirm_35_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSuccessConfirm());\n          })(\"closed\", function CreateToolsComponent_Template_ava_popup_closed_35_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.closeSuccessPopup());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"ava-popup\", 25);\n          i0.ɵɵlistener(\"confirm\", function CreateToolsComponent_Template_ava_popup_confirm_36_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.closeErrorPopup());\n          })(\"closed\", function CreateToolsComponent_Template_ava_popup_closed_36_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.closeErrorPopup());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"app-ask-ava-wrapper\", 26);\n          i0.ɵɵlistener(\"oNClickGenerate\", function CreateToolsComponent_Template_app_ask_ava_wrapper_oNClickGenerate_37_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onClickGenerate($event));\n          })(\"oNClickClosed\", function CreateToolsComponent_Template_app_ask_ava_wrapper_oNClickClosed_37_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onClose());\n          })(\"oNClickUse\", function CreateToolsComponent_Template_app_ask_ava_wrapper_oNClickUse_37_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onUse());\n          })(\"oNClickReset\", function CreateToolsComponent_Template_app_ask_ava_wrapper_oNClickReset_37_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onReset());\n          })(\"oNClickCancel\", function CreateToolsComponent_Template_app_ask_ava_wrapper_oNClickCancel_37_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onCancle());\n          });\n          i0.ɵɵelementStart(38, \"form\", 27)(39, \"div\", 28)(40, \"div\", 29);\n          i0.ɵɵelement(41, \"ava-textbox\", 30);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"div\", 29);\n          i0.ɵɵelement(43, \"ava-textbox\", 31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"div\", 32);\n          i0.ɵɵelement(45, \"ava-textarea\", 33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"div\", 32);\n          i0.ɵɵelement(47, \"app-code-editor\", 34, 0);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"showRightPane\", true);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.isEditMode && !ctx.isExecuteMode && ctx.toolId);\n          i0.ɵɵadvance(2);\n          i0.ɵɵpropertyInterpolate(\"label\", ctx.labels.exit);\n          i0.ɵɵproperty(\"customStyles\", i0.ɵɵpureFunction0(90, _c0));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isEditMode && !ctx.isExecuteMode);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isEditMode && !ctx.isExecuteMode);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"formControl\", ctx.getControl(\"name\"))(\"label\", ctx.labels.toolName)(\"fullWidth\", true)(\"required\", true)(\"readonly\", ctx.isFieldsDisabled);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formControl\", ctx.getControl(\"toolClassName\"))(\"label\", ctx.labels.toolClassName)(\"fullWidth\", true)(\"required\", true)(\"readonly\", ctx.isFieldsDisabled);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"label\", ctx.labels.description)(\"formControl\", ctx.getControl(\"description\"))(\"rows\", 6)(\"fullWidth\", true)(\"required\", true)(\"readonly\", ctx.isFieldsDisabled);\n          i0.ɵɵadvance(7);\n          i0.ɵɵpropertyInterpolate(\"label\", ctx.labels.askAva);\n          i0.ɵɵproperty(\"customStyles\", i0.ɵɵpureFunction0(91, _c0));\n          i0.ɵɵadvance();\n          i0.ɵɵpropertyInterpolate(\"label\", ctx.labels.exit);\n          i0.ɵɵproperty(\"customStyles\", i0.ɵɵpureFunction0(92, _c0));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isEditMode && !ctx.isExecuteMode);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isEditMode && !ctx.isExecuteMode);\n          i0.ɵɵadvance(2);\n          i0.ɵɵpropertyInterpolate(\"placeholder\", ctx.placeholder);\n          i0.ɵɵpropertyInterpolate(\"footerText\", ctx.labels.note);\n          i0.ɵɵproperty(\"Control\", ctx.getControl(\"classDefinition\"))(\"actionButtons\", ctx.editorActions)(\"readonly\", ctx.isFieldsDisabled);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"title\", ctx.validationOutputEditorConfig.title)(\"language\", ctx.validationOutputEditorConfig.language)(\"theme\", ctx.validationOutputEditorConfig.theme)(\"readonly\", ctx.validationOutputEditorConfig.readOnly)(\"height\", ctx.validationOutputEditorConfig.height)(\"value\", ctx.showValidationOutput ? ctx.validationOutput : \"\")(\"placeholder\", ctx.validationOutputEditorConfig.placeholder);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"promptOptions\", ctx.promptOptions)(\"messages\", ctx.chatMessages)(\"isLoading\", ctx.isProcessingChat)(\"showChatInteractionToggles\", false)(\"showAiPrincipleToggle\", true)(\"showDropdown\", false)(\"showAgentNameInput\", true)(\"displayedAgentName\", ctx.getToolDisplayName())(\"agentNamePlaceholder\", \"Current Tool Name\")(\"showApprovalButton\", false);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"show\", ctx.showSuccessPopup)(\"title\", ctx.popupTitle)(\"message\", ctx.popupMessage)(\"showHeaderIcon\", true)(\"showClose\", true)(\"showCancel\", false)(\"showConfirm\", true)(\"confirmButtonLabel\", \"OK\")(\"confirmButtonVariant\", \"primary\")(\"confirmButtonBackground\", \"#28a745\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"show\", ctx.showErrorPopup)(\"title\", ctx.popupTitle)(\"message\", ctx.popupMessage)(\"showHeaderIcon\", true)(\"showClose\", true)(\"showCancel\", false)(\"showConfirm\", true)(\"confirmButtonLabel\", \"OK\")(\"confirmButtonVariant\", \"primary\")(\"confirmButtonBackground\", \"#dc3545\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"show\", ctx.showAskAvaModal)(\"prompt\", ctx.prompt)(\"isLoading\", ctx.isLoading)(\"showOutput\", ctx.showToolOutput);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"formGroup\", ctx.outputToolForm);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"formControl\", ctx.getOutputControl(\"name\"))(\"label\", ctx.labels.toolName)(\"readonly\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formControl\", ctx.getOutputControl(\"toolClassName\"))(\"label\", ctx.labels.toolClassName)(\"readonly\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"label\", ctx.labels.description)(\"formControl\", ctx.getOutputControl(\"description\"))(\"rows\", 4)(\"fullWidth\", true)(\"readonly\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵpropertyInterpolate(\"title\", ctx.labels.toolClassDefinition);\n          i0.ɵɵpropertyInterpolate(\"footerText\", ctx.labels.note);\n          i0.ɵɵproperty(\"value\", ctx.getOutputControl(\"classDefinition\").value)(\"readonly\", true);\n        }\n      },\n      dependencies: [CommonModule, i7.NgIf, ReactiveFormsModule, i1.ɵNgNoValidate, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.FormControlDirective, i1.FormGroupDirective, AvaTextboxComponent, AvaTextareaComponent, ButtonComponent, PlaygroundComponent, CodeEditorComponent, MainLayoutComponent, PopupComponent, AskAvaWrapperComponent],\n      styles: [\"[_nghost-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\nmain-layout[_ngcontent-%COMP%] {\\n  height: 100%;\\n  width: 100%;\\n  border: 1px solid #e0e0e0;\\n}\\nmain-layout[_ngcontent-%COMP%]     .layout {\\n  background-color: #fafafa;\\n  height: calc(100vh - 2px);\\n  width: calc(98vw - 2px);\\n}\\nmain-layout[_ngcontent-%COMP%]     .left-pane, main-layout[_ngcontent-%COMP%]     .right-pane {\\n  background-color: #fafafa;\\n}\\nmain-layout[_ngcontent-%COMP%]     .center-pane {\\n  background-color: #fafafa;\\n  border-left: 1px solid #e9ecef;\\n  border-right: 1px solid #e9ecef;\\n  overflow: hidden;\\n  width: 51;\\n}\\nmain-layout[_ngcontent-%COMP%]     .center-pane   [center] {\\n  flex: 1;\\n  overflow-y: auto;\\n  display: flex;\\n  flex-direction: column;\\n}\\nmain-layout[_ngcontent-%COMP%]     .center-header {\\n  background-color: #ffffff;\\n  border-bottom: 1px solid #e9ecef;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 12px 16px;\\n  flex-shrink: 0;\\n}\\nmain-layout[_ngcontent-%COMP%]     .center-header .pane-title {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #212529;\\n}\\n\\n.center-header-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  align-items: center;\\n  margin-left: auto;\\n}\\n\\n.tool-description-pane[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n  height: 100%;\\n  overflow-y: auto;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n  background-color: #fafafa;\\n}\\n.tool-description-pane[_ngcontent-%COMP%]   .form-field-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  margin-bottom: 16px;\\n}\\n.tool-description-pane[_ngcontent-%COMP%]   .form-field-group[_ngcontent-%COMP%]:last-child {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.tool-description-pane[_ngcontent-%COMP%]   .form-field-group[_ngcontent-%COMP%]:last-child   ava-textarea[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 100%;\\n}\\n.tool-description-pane[_ngcontent-%COMP%]   .form-field-group[_ngcontent-%COMP%]:last-child   ava-textarea[_ngcontent-%COMP%]     .textarea-container {\\n  height: 100%;\\n}\\n.tool-description-pane[_ngcontent-%COMP%]   .form-field-group[_ngcontent-%COMP%]:last-child   ava-textarea[_ngcontent-%COMP%]     .textarea-container textarea {\\n  height: 100% !important;\\n  min-height: 120px;\\n  resize: vertical;\\n}\\n\\n.tool-editor-pane[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  background-color: #ffffff;\\n  padding: 0;\\n  overflow: hidden;\\n}\\n.tool-editor-pane[_ngcontent-%COMP%]   .custom-center-header[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n}\\n.tool-editor-pane[_ngcontent-%COMP%]   .editor-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n.tool-editor-pane[_ngcontent-%COMP%]   .editor-container[_ngcontent-%COMP%]   app-code-editor[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  min-height: 300px;\\n}\\n.tool-editor-pane[_ngcontent-%COMP%]   .editor-container[_ngcontent-%COMP%]   app-code-editor[_ngcontent-%COMP%]     .code-editor-wrapper {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n}\\n.tool-editor-pane[_ngcontent-%COMP%]   .editor-container[_ngcontent-%COMP%]   app-code-editor[_ngcontent-%COMP%]     .code-editor-wrapper .editor-header {\\n  flex-shrink: 0;\\n}\\n.tool-editor-pane[_ngcontent-%COMP%]   .editor-container[_ngcontent-%COMP%]   app-code-editor[_ngcontent-%COMP%]     .code-editor-wrapper .monaco-editor-container {\\n  flex: 1;\\n  overflow: hidden;\\n}\\n.tool-editor-pane[_ngcontent-%COMP%]   .validation-output-section[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  margin: 16px;\\n  background-color: #ffffff;\\n  border-radius: 8px;\\n  border: 1px solid #e9ecef;\\n}\\n.tool-editor-pane[_ngcontent-%COMP%]   .validation-output-section[_ngcontent-%COMP%]   .validation-json-editor[_ngcontent-%COMP%]     .monaco-editor-container {\\n  border: none;\\n  border-radius: 8px;\\n  max-height: 200px;\\n}\\n\\n.tool-playground-pane[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.tool-playground-pane[_ngcontent-%COMP%]   .playground-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.tool-playground-pane[_ngcontent-%COMP%]   .playground-container[_ngcontent-%COMP%]   app-playground[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 100%;\\n}\\n.tool-playground-pane[_ngcontent-%COMP%]   .playground-container[_ngcontent-%COMP%]   app-playground[_ngcontent-%COMP%]     .playground-wrapper {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.tool-playground-pane[_ngcontent-%COMP%]   .playground-container[_ngcontent-%COMP%]   app-playground[_ngcontent-%COMP%]     .chat-container {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  min-height: 0;\\n}\\n.tool-playground-pane[_ngcontent-%COMP%]   .playground-container[_ngcontent-%COMP%]   app-playground[_ngcontent-%COMP%]     .chat-messages {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 16px;\\n}\\n.tool-playground-pane[_ngcontent-%COMP%]   .playground-container[_ngcontent-%COMP%]   app-playground[_ngcontent-%COMP%]     .chat-input-container {\\n  flex-shrink: 0;\\n  padding: 16px;\\n  border-top: 1px solid #e9ecef;\\n  background-color: #ffffff;\\n}\\n\\n  ava-textbox .textbox-container {\\n  width: 100%;\\n}\\n  ava-textbox .textbox-container .textbox-label {\\n  font-weight: 600;\\n  color: #212529;\\n  margin-bottom: 8px;\\n  font-size: 14px;\\n}\\n  ava-textbox .textbox-container .textbox-input {\\n  width: 100%;\\n  padding: 8px 12px;\\n  border: 1px solid #ced4da;\\n  border-radius: 4px;\\n  font-size: 14px;\\n}\\n  ava-textbox .textbox-container .textbox-input:focus {\\n  border-color: #215AD6;\\n  box-shadow: 0 0 0 0.2rem rgba(33, 90, 214, 0.25);\\n  outline: none;\\n}\\n  ava-textbox .textbox-container .textbox-input[readonly] {\\n  background-color: #f8f9fa;\\n  opacity: 1;\\n}\\n  ava-textarea .textarea-container {\\n  width: 100%;\\n}\\n  ava-textarea .textarea-container .textarea-label {\\n  font-weight: 600;\\n  color: #212529;\\n  margin-bottom: 8px;\\n  font-size: 14px;\\n}\\n  ava-textarea .textarea-container .textarea-input {\\n  width: 100%;\\n  padding: 8px 12px;\\n  border: 1px solid #ced4da;\\n  border-radius: 4px;\\n  font-size: 14px;\\n  font-family: inherit;\\n  resize: vertical;\\n}\\n  ava-textarea .textarea-container .textarea-input:focus {\\n  border-color: #215AD6;\\n  box-shadow: 0 0 0 0.2rem rgba(33, 90, 214, 0.25);\\n  outline: none;\\n}\\n  ava-textarea .textarea-container .textarea-input[readonly] {\\n  background-color: #f8f9fa;\\n  opacity: 1;\\n}\\n  ava-button .btn {\\n  font-size: 14px;\\n  font-weight: 500;\\n  border-radius: 6px;\\n  transition: all 0.2s ease-in-out;\\n}\\n  ava-button .btn.btn-sm {\\n  padding: 6px 16px;\\n}\\n\\n@media (max-width: 1200px) {\\n  .tool-description-pane[_ngcontent-%COMP%], \\n   .tool-editor-pane[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .button-row[_ngcontent-%COMP%] {\\n    flex-wrap: wrap;\\n    gap: 8px;\\n  }\\n  .tool-description-pane[_ngcontent-%COMP%], \\n   .tool-editor-pane[_ngcontent-%COMP%] {\\n    padding: 8px;\\n  }\\n}\\n.custom-center-header[_ngcontent-%COMP%] {\\n  height: 48px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 0 8px;\\n  background: #ffffff;\\n  border-bottom: 1px solid #e0e0e0;\\n  z-index: 1;\\n}\\n\\n.header-buttons[_ngcontent-%COMP%] {\\n  margin-right: 0.8rem;\\n}\\n\\n[_nghost-%COMP%]     .tool-playground-pane, \\n[_nghost-%COMP%]     .tool-playground-pane .playground-container, \\n[_nghost-%COMP%]     .tool-playground-pane app-playground {\\n  height: 100vh !important;\\n  min-height: 0;\\n  max-height: 100vh !important;\\n  flex: 1 1 0;\\n  box-sizing: border-box;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.header-section[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.askava-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.place-end[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n}\\n\\n.place-start[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-start;\\n}\\n\\n.place-space-between[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n}\\n\\n.actons[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n  align-items: center;\\n}\\n\\n.askava-container[_ngcontent-%COMP%]  label {\\n  text-align: start !important;\\n}\\n\\n.generated-output__body[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  row-gap: 1rem;\\n}\\n\\n#outputEditor[_ngcontent-%COMP%]  .monaco-editor .view-lines {\\n  justify-content: flex-start !important;\\n  text-align: left !important;\\n}\\n#outputEditor[_ngcontent-%COMP%]  .monaco-editor .sticky-scroll {\\n  justify-content: flex-start !important;\\n  text-align: left !important;\\n  left: 0 !important;\\n}\\n#outputEditor[_ngcontent-%COMP%]  .monaco-editor .sticky-widget-lines-scrollable {\\n  left: 85px !important;\\n  text-align: start;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return CreateToolsComponent;\n})();", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormGroup", "ReactiveFormsModule", "FormControl", "Validators", "Subscription", "CodeEditorComponent", "toolsText", "PlaygroundComponent", "AvaTextareaComponent", "AvaTextboxComponent", "ButtonComponent", "PopupComponent", "MainLayoutComponent", "TOOL_CREATION_USECASE_IDENTIFIER", "ToolModes", "AskAvaWrapperComponent", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵlistener", "CreateToolsComponent_ng_container_5_Template_ava_button_userClick_1_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "handleApproval", "ɵɵelementEnd", "CreateToolsComponent_ng_container_8_Template_ava_button_userClick_1_listener", "_r4", "onSave", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c1", "CreateToolsComponent_ng_container_9_Template_ava_button_userClick_1_listener", "_r5", "onUpdate", "CreateToolsComponent_ng_container_25_Template_ava_button_userClick_1_listener", "_r6", "CreateToolsComponent_ng_container_26_Template_ava_button_userClick_1_listener", "_r7", "CreateToolsComponent", "fb", "router", "route", "toolExecutionService", "toolsService", "promptGenerateService", "tokenStorage", "codeEditor", "toolId", "isEditMode", "isCloneMode", "isExecuteMode", "isFieldsDisabled", "showChatInterface", "selectedTool", "toolForm", "outputToolForm", "chatMessages", "isProcessingChat", "showAskAvaModal", "showToolOutput", "isLoading", "prompt", "parameterState", "parameters", "currentParameterIndex", "collectedInputs", "isCollecting", "executionSubscription", "waitingForRestartConfirmation", "placeholder", "TOOL_PLACEHOLDER", "toolClassDef", "labels", "prompt<PERSON><PERSON>e", "validationOutput", "showValidationOutput", "validationOutputEditorConfig", "title", "language", "theme", "readOnly", "height", "editorActions", "label", "style", "customClass", "icon", "originalToolData", "codeEditorValueToSet", "showSuccessPopup", "showErrorPopup", "popupTitle", "popupMessage", "shouldCallExtractParams", "createToolForm", "group", "name", "required", "noWhitespaceValidator", "description", "toolClassName", "pattern", "classDefinition", "constructor", "getUserSignature", "userSignature", "getDaUsername", "formatLocalDateTime", "date", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "milliseconds", "getMilliseconds", "microseconds", "Math", "floor", "random", "ngOnInit", "toolIdParam", "snapshot", "paramMap", "get", "parseInt", "url<PERSON><PERSON>", "url", "includes", "console", "log", "loadToolData", "initializeChatMessages", "controls", "valueChanges", "subscribe", "updateValueAndValidity", "ngAfterViewInit", "setValue", "setTimeout", "isReady", "layout", "ngOnDestroy", "unsubscribe", "from", "text", "markFormGroupTouched", "valid", "showValidationErrorPopup", "createPayload", "value", "toolConfigs", "image", "tool_class_def", "tool_class_name", "created<PERSON>y", "modifiedBy", "addNewUserTool", "next", "response", "resetParameterState", "successMessage", "message", "showSuccessMessage", "error", "errorMessage", "showErrorMessage", "updatePayload", "id", "toString", "createdAt", "createTimestamp", "createDate", "modifiedAt", "isDeleted", "updateUserTool", "status", "extractParameters", "toolName", "addChatMessage", "useCase", "useCaseIdentifier", "modelApi", "parametersText", "choices", "Error", "parametersObj", "JSON", "parse", "Object", "keys", "map", "key", "type", "length", "promptForNextParameter", "currentParam", "handleChatMessage", "toLowerCase", "trim", "handleParameterInput", "input", "processedInput", "parseFloat", "isNaN", "lowerInput", "Array", "isArray", "executeToolWithParameters", "payload", "class_definition", "class_name", "inputs", "testTool", "output", "detail", "onExit", "returnPage", "queryParamMap", "pageNumber", "navigate", "queryParams", "page", "getControl", "control", "invalidInput", "formGroup", "for<PERSON>ach", "<PERSON><PERSON><PERSON><PERSON>ched", "errors", "fieldName", "getFieldError", "<PERSON><PERSON><PERSON><PERSON>", "getFieldLabel", "push", "join", "toolClassDefinition", "getOutputControl", "field", "invalid", "touched", "dirty", "errorToolNameRequired", "errorToolClassNameRequired", "errorDescriptionRequired", "errorToolConfigRequired", "errorRequired", "invalidClass", "validateCode", "validateTool", "res", "responseText", "hideProcessingLoader", "replace", "parsed", "formatted", "issues", "issue", "idx", "severity", "line_number", "suggestion", "e", "getUserToolDetails", "tool", "tools", "stringify", "patchValue", "toolDescription", "promptOptions", "onPromptChanged", "option", "onEditorAction", "selectAll", "clear", "approvalPayload", "toolImage", "def", "CHANGE2", "statusText", "closeSuccessPopup", "closeError<PERSON><PERSON><PERSON>", "onSuccessConfirm", "getToolDisplayName", "onClickGenerate", "toolCreation", "generatedPrompt", "tool_name", "tool_class", "tool_description", "tool_class_defination", "toggleAskAvaModal", "show", "onUse", "formValue", "getRawValue", "reset", "onReset", "onCancle", "onClose", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "Router", "ActivatedRoute", "i3", "ToolExecutionService", "i4", "ToolsService", "i5", "PromptEnhanceService", "i6", "TokenStorageService", "selectors", "viewQuery", "CreateToolsComponent_Query", "rf", "ctx", "ɵɵtext", "ɵɵtemplate", "CreateToolsComponent_ng_container_5_Template", "CreateToolsComponent_Template_ava_button_userClick_7_listener", "_r1", "CreateToolsComponent_ng_container_8_Template", "CreateToolsComponent_ng_container_9_Template", "ɵɵelement", "CreateToolsComponent_Template_ava_button_userClick_23_listener", "CreateToolsComponent_Template_ava_button_userClick_24_listener", "CreateToolsComponent_ng_container_25_Template", "CreateToolsComponent_ng_container_26_Template", "CreateToolsComponent_Template_app_code_editor_primaryButtonSelected_28_listener", "CreateToolsComponent_Template_app_code_editor_actionButtonClicked_28_listener", "$event", "CreateToolsComponent_Template_app_playground_promptChange_34_listener", "CreateToolsComponent_Template_app_playground_messageSent_34_listener", "CreateToolsComponent_Template_app_playground_approvalRequested_34_listener", "CreateToolsComponent_Template_ava_popup_confirm_35_listener", "CreateToolsComponent_Template_ava_popup_closed_35_listener", "CreateToolsComponent_Template_ava_popup_confirm_36_listener", "CreateToolsComponent_Template_ava_popup_closed_36_listener", "CreateToolsComponent_Template_app_ask_ava_wrapper_oNClickGenerate_37_listener", "CreateToolsComponent_Template_app_ask_ava_wrapper_oNClickClosed_37_listener", "CreateToolsComponent_Template_app_ask_ava_wrapper_oNClickUse_37_listener", "CreateToolsComponent_Template_app_ask_ava_wrapper_oNClickReset_37_listener", "CreateToolsComponent_Template_app_ask_ava_wrapper_oNClickCancel_37_listener", "ɵɵpropertyInterpolate", "exit", "_c0", "askAva", "note", "i7", "NgIf", "ɵNgNoValidate", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "FormControlDirective", "FormGroupDirective", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\libraries\\tools\\create-tools\\create-tools.component.ts", "C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\libraries\\tools\\create-tools\\create-tools.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  OnInit,\r\n  OnDestroy,\r\n  EventEmitter,\r\n  Output,\r\n  ViewChild,\r\n  AfterViewInit,\r\n} from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport {\r\n  FormBuilder,\r\n  FormGroup,\r\n  ReactiveFormsModule,\r\n  FormControl,\r\n  Validators,\r\n  AbstractControl,\r\n  ValidationErrors,\r\n} from '@angular/forms';\r\nimport { Router, ActivatedRoute } from '@angular/router';\r\nimport { ChatMessage } from '../../../../shared/components/chat-window/chat-window.component';\r\nimport { ToolExecutionService } from '../../../../shared/services/tool-execution/tool-execution.service';\r\nimport { ToolsService } from '../../../../shared/services/tools.service';\r\nimport { Subscription } from 'rxjs';\r\nimport { PromptEnhanceService } from '../../../../shared/services/prompt-enhance.service';\r\nimport { TokenStorageService } from '@shared/auth/services/token-storage.service';\r\nimport {\r\n  CodeEditorComponent,\r\n  CodeLanguage,\r\n  CodeEditorTheme,\r\n  EditorActionButton,\r\n} from '../../../../shared/components/code-editor/code-editor.component';\r\nimport toolsText from '../constants/tools.json';\r\nimport { PlaygroundComponent } from 'projects/console/src/app/shared/components/playground/playground.component';\r\nimport {\r\n  AvaTextareaComponent,\r\n  AvaTextboxComponent,\r\n  ButtonComponent,\r\n  DropdownOption,\r\n  PopupComponent,\r\n  IconComponent,\r\n} from '@ava/play-comp-library';\r\nimport { MainLayoutComponent } from 'projects/console/src/app/shared/components/main-layout/main-layout.component';\r\nimport {\r\n  TOOL_CREATION_USECASE_IDENTIFIER,\r\n  ToolModes,\r\n} from '../constants/builtInTools';\r\nimport { AskAvaWrapperComponent } from 'projects/console/src/app/shared/components/ask-ava-wrapper/ask-ava-wrapper.component';\r\n\r\ninterface ExtractedParameter {\r\n  name: string;\r\n  type: string;\r\n}\r\n\r\ninterface Tool {\r\n  id: number;\r\n  name: string;\r\n}\r\n\r\ninterface ParameterCollectionState {\r\n  parameters: ExtractedParameter[];\r\n  currentParameterIndex: number;\r\n  collectedInputs: { [key: string]: any };\r\n  isCollecting: boolean;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-create-tools',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    ReactiveFormsModule,\r\n    AvaTextboxComponent,\r\n    AvaTextareaComponent,\r\n    ButtonComponent,\r\n    PlaygroundComponent,\r\n    CodeEditorComponent,\r\n    MainLayoutComponent,\r\n    PopupComponent,\r\n    AskAvaWrapperComponent\r\n],\r\n  templateUrl: './create-tools.component.html',\r\n  styleUrls: ['./create-tools.component.scss'],\r\n})\r\nexport class CreateToolsComponent implements OnInit, OnDestroy, AfterViewInit {\r\n  @ViewChild(CodeEditorComponent) codeEditor!: CodeEditorComponent;\r\n\r\n  toolId: number | null = null;\r\n  isEditMode: boolean = false;\r\n  isCloneMode: boolean = false;\r\n  isExecuteMode: boolean = true;\r\n  isFieldsDisabled: boolean = false;\r\n  showChatInterface: boolean = true;\r\n  selectedTool: string | null = null;\r\n  toolForm: FormGroup;\r\n  outputToolForm!: FormGroup;\r\n  chatMessages: ChatMessage[] = [];\r\n  isProcessingChat: boolean = false;\r\n  showAskAvaModal = false;\r\n  showToolOutput = false;\r\n  isLoading = false;\r\n\r\n  prompt = new FormControl('');\r\n\r\n  parameterState: ParameterCollectionState = {\r\n    parameters: [],\r\n    currentParameterIndex: 0,\r\n    collectedInputs: {},\r\n    isCollecting: false,\r\n  };\r\n\r\n  private executionSubscription: Subscription = new Subscription();\r\n  private waitingForRestartConfirmation: boolean = false;\r\n  public placeholder: any = toolsText.TOOL_PLACEHOLDER.toolClassDef;\r\n  public labels: any = toolsText.labels;\r\n  @Output() promptChange = new EventEmitter<string>();\r\n  public validationOutput: string = '';\r\n  public showValidationOutput: boolean = false;\r\n  public validationOutputEditorConfig = {\r\n    title: 'Tool Compiler',\r\n    language: 'json' as CodeLanguage,\r\n    theme: 'light' as CodeEditorTheme,\r\n    readOnly: true,\r\n    height: '250px',\r\n    placeholder: 'Test the tool before saving or updating for security validations',\r\n  };\r\n\r\n  public editorActions: EditorActionButton[] = [\r\n    { label: 'Select All', style: 'secondary', customClass: '', icon: '' },\r\n    { label: 'Reset', style: 'secondary', customClass: '', icon: '' },\r\n  ];\r\n\r\n  private originalToolData: any = null;\r\n  private codeEditorValueToSet: string | null = null;\r\n\r\n  // Popup properties\r\n  showSuccessPopup = false;\r\n  showErrorPopup = false;\r\n  popupTitle = '';\r\n  popupMessage = '';\r\n  private shouldCallExtractParams = false;\r\n\r\n  createToolForm() {\r\n    return this.fb.group({\r\n        name: ['', [Validators.required, this.noWhitespaceValidator]],\r\n      description: ['', [Validators.required, this.noWhitespaceValidator]],\r\n      toolClassName: ['', [Validators.required, this.noWhitespaceValidator, Validators.pattern(/^[A-Z][a-zA-Z0-9]*$/)]],\r\n      classDefinition: ['', [Validators.required, this.noWhitespaceValidator],]\r\n    });\r\n  }\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private toolExecutionService: ToolExecutionService,\r\n    private toolsService: ToolsService,\r\n    private promptGenerateService: PromptEnhanceService,\r\n    private tokenStorage: TokenStorageService,\r\n  ) {\r\n    this.toolForm = this.createToolForm();\r\n    this.outputToolForm = this.createToolForm();\r\n  }\r\n\r\n  private getUserSignature(): string {\r\n    const userSignature =\r\n      this.tokenStorage.getDaUsername() || '<EMAIL>';\r\n    return userSignature;\r\n  }\r\n\r\n  private formatLocalDateTime(date: Date = new Date()): string {\r\n    // Format date as LocalDateTime for Java backend (without timezone)\r\n    // Try to match the format from the example: 2025-06-10T05:09:39.136505\r\n    const year = date.getFullYear();\r\n    const month = String(date.getMonth() + 1).padStart(2, '0');\r\n    const day = String(date.getDate()).padStart(2, '0');\r\n    const hours = String(date.getHours()).padStart(2, '0');\r\n    const minutes = String(date.getMinutes()).padStart(2, '0');\r\n    const seconds = String(date.getSeconds()).padStart(2, '0');\r\n\r\n    // Generate 6-digit microseconds (similar to the example format)\r\n    const milliseconds = date.getMilliseconds();\r\n    const microseconds = String(milliseconds * 1000 + Math.floor(Math.random() * 1000)).padStart(6, '0');\r\n\r\n    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.${microseconds}`;\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    const toolIdParam = this.route.snapshot.paramMap.get('id');\r\n    this.toolId = toolIdParam ? parseInt(toolIdParam, 10) : null;\r\n    const urlPath = this.router.url;\r\n    this.isEditMode = urlPath.includes('/edit/');\r\n    this.isCloneMode = urlPath.includes('/clone/');\r\n    this.isExecuteMode = urlPath.includes('/execute/');\r\n    this.isFieldsDisabled = this.isExecuteMode;\r\n\r\n    console.log(\r\n      'ngOnInit - toolId:',\r\n      this.toolId,\r\n      'isEditMode:',\r\n      this.isEditMode,\r\n      'isCloneMode:',\r\n      this.isCloneMode,\r\n      'isExecuteMode:',\r\n      this.isExecuteMode,\r\n    );\r\n\r\n    if (\r\n      (this.isEditMode || this.isCloneMode || this.isExecuteMode) &&\r\n      this.toolId\r\n    ) {\r\n      this.loadToolData(this.toolId);\r\n      if (this.isExecuteMode) {\r\n        this.initializeChatMessages();\r\n      }\r\n    }\r\n\r\n    // Add listener for toolClassName changes to update classDefinition validation\r\n    this.toolForm.controls['toolClassName'].valueChanges.subscribe(() => {\r\n      this.toolForm.controls['classDefinition'].updateValueAndValidity();\r\n    });\r\n\r\n    // Window resize will be handled by HostListener\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    // If value was loaded before editor was ready, set it now\r\n    if (this.codeEditor && this.codeEditorValueToSet) {\r\n      this.codeEditor.setValue(this.codeEditorValueToSet);\r\n      this.codeEditorValueToSet = null;\r\n\r\n      // Force Monaco editor layout after setting content\r\n      setTimeout(() => {\r\n        if (this.codeEditor?.isReady) {\r\n          this.codeEditor['editor']?.layout();\r\n        }\r\n      }, 100);\r\n    }\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    if (this.executionSubscription) {\r\n      this.executionSubscription.unsubscribe();\r\n    }\r\n  }\r\n\r\n  private initializeChatMessages(): void {\r\n    this.chatMessages = [\r\n      {\r\n        from: 'ai',\r\n        text: \"Hi! Welcome to the tool testing playground. I'll help you test your tool by collecting the required parameters.\",\r\n      },\r\n    ];\r\n  }\r\n\r\n  onSave(): void {\r\n    // Mark all fields as touched to show validation errors\r\n    this.markFormGroupTouched(this.toolForm);\r\n\r\n    // Check if form is valid\r\n    if (!this.toolForm.valid) {\r\n      this.showValidationErrorPopup();\r\n      return;\r\n    }\r\n\r\n    // Handle create mode only\r\n    if (!this.isEditMode) {\r\n      // For create and clone\r\n      // New payload structure for creating tools\r\n      const createPayload = {\r\n        name: this.toolForm.get('name')?.value,\r\n        description: this.toolForm.get('description')?.value,\r\n        toolConfigs: {\r\n          image: '', // Default empty image for new tools\r\n          tool_class_def: this.toolForm.get('classDefinition')?.value,\r\n          tool_class_name: this.toolForm.get('toolClassName')?.value,\r\n        },\r\n        createdBy: this.getUserSignature(),\r\n        modifiedBy: this.getUserSignature(),\r\n      };\r\n\r\n      console.log('Creating new tool with payload:', createPayload);\r\n      this.toolsService.addNewUserTool(createPayload).subscribe({\r\n        next: (response) => {\r\n          console.log('Tool creation response:', response);\r\n          this.isExecuteMode = true;\r\n          this.showChatInterface = true;\r\n          this.initializeChatMessages();\r\n          this.resetParameterState();\r\n\r\n          // Show success popup with API response\r\n          const successMessage = response?.message || 'Tool created successfully!';\r\n          this.showSuccessMessage('Tool Created', successMessage);\r\n        },\r\n        error: (error) => {\r\n          console.error('Error creating tool', error);\r\n\r\n          // Show error popup with API response\r\n          const errorMessage = error?.error?.message || error?.message || 'Error creating tool. Please try again.';\r\n          this.showErrorMessage('Creation Failed', errorMessage);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  onUpdate(): void {\r\n    // Mark all fields as touched to show validation errors\r\n    this.markFormGroupTouched(this.toolForm);\r\n\r\n    // Check if form is valid\r\n    if (!this.toolForm.valid) {\r\n      this.showValidationErrorPopup();\r\n      return;\r\n    }\r\n\r\n    // Handle update mode for both edit and execute\r\n    if (this.isEditMode && this.toolId) {\r\n      // Transform to new API payload structure for update using change_request endpoint\r\n      const updatePayload = {\r\n        id: parseInt(this.toolId.toString()),\r\n        name: this.toolForm.get('name')?.value,\r\n        description: this.toolForm.get('description')?.value,\r\n        createdBy: this.originalToolData?.createdBy || this.getUserSignature(),\r\n        modifiedBy: this.getUserSignature(),\r\n        // Use original timestamp if available, otherwise format new one\r\n        createdAt: this.originalToolData?.createdAt || this.originalToolData?.createTimestamp || this.originalToolData?.createDate || this.formatLocalDateTime(),\r\n        modifiedAt: this.formatLocalDateTime(),\r\n        isDeleted: true, // Set to true for change request\r\n         toolConfigs: {\r\n          image: '', // Default empty image for new tools\r\n          tool_class_def: this.toolForm.get('classDefinition')?.value,\r\n          tool_class_name: this.toolForm.get('toolClassName')?.value\r\n        },\r\n      };\r\n\r\n      console.log('Update payload being sent:', updatePayload);\r\n\r\n      this.toolsService.updateUserTool(updatePayload).subscribe({\r\n        next: (response) => {\r\n          console.log('Tool update response:', response);\r\n          this.isExecuteMode = true;\r\n          this.showChatInterface = true;\r\n          this.initializeChatMessages();\r\n          this.resetParameterState();\r\n\r\n          // Show success popup with API response\r\n          const successMessage = response?.message || 'Tool updated successfully!';\r\n          this.showSuccessMessage('Tool Updated', successMessage);\r\n        },\r\n        error: (error) => {\r\n          console.error('Error updating tool:', error);\r\n          console.error('Error details:', error.error);\r\n          console.error('Status:', error.status);\r\n\r\n          // Show error popup with API response\r\n          const errorMessage = error?.error?.message || error?.message || 'Error updating tool. Please try again.';\r\n          this.showErrorMessage('Update Failed', errorMessage);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n\r\n\r\n  private extractParameters(): void {\r\n    const toolClassDef = this.toolForm.get('classDefinition')?.value;\r\n    const toolClassName = this.toolForm.get('toolClassName')?.value;\r\n    const toolName = this.toolForm.get('name')?.value;\r\n    if (!toolClassDef || !toolClassName) {\r\n      this.addChatMessage(\r\n        'ai',\r\n        'Error: Tool class definition or class name is missing.',\r\n      );\r\n      return;\r\n    }\r\n    const useCase = 'PARAMETER_EXTRACTOR';\r\n    const useCaseIdentifier =\r\n      'PARAMETER_EXTRACTOR@ADD@GOPAL@TEST_GOPAL@GOPALTEST';\r\n\r\n    this.isProcessingChat = true;\r\n    this.addChatMessage('ai', 'Analyzing your tool to extract parameters...');\r\n\r\n    this.promptGenerateService\r\n      .modelApi(toolClassDef, useCase, false, useCaseIdentifier)\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          console.log('Parameter extraction response:', response);\r\n          try {\r\n            const parametersText = response?.response?.choices?.[0]?.text;\r\n            if (!parametersText) {\r\n              throw new Error('No parameters found in response');\r\n            }\r\n            const parametersObj = JSON.parse(parametersText);\r\n            const parameters: ExtractedParameter[] = Object.keys(\r\n              parametersObj,\r\n            ).map((key) => ({\r\n              name: key,\r\n              type: parametersObj[key],\r\n            }));\r\n\r\n            if (parameters.length === 0) {\r\n              this.isProcessingChat = false;\r\n              this.addChatMessage(\r\n                'ai',\r\n                'No parameters found for this tool. The tool might not require any input parameters.',\r\n              );\r\n              return;\r\n            }\r\n\r\n            this.parameterState = {\r\n              parameters: parameters,\r\n              currentParameterIndex: 0,\r\n              collectedInputs: {},\r\n              isCollecting: true,\r\n            };\r\n\r\n            this.isProcessingChat = false;\r\n            this.promptForNextParameter();\r\n          } catch (error) {\r\n            console.error('Error parsing parameters:', error);\r\n            this.isProcessingChat = false;\r\n            this.addChatMessage(\r\n              'ai',\r\n              'Error: Failed to parse extracted parameters. Please check your tool definition.',\r\n            );\r\n          }\r\n        },\r\n        error: (error) => {\r\n          console.error('Parameter extraction error', error);\r\n          this.isProcessingChat = false;\r\n          this.addChatMessage(\r\n            'ai',\r\n            'Error: Failed to extract parameters from your tool. Please check your tool definition.',\r\n          );\r\n        },\r\n      });\r\n  }\r\n\r\n  private promptForNextParameter(): void {\r\n    if (\r\n      this.parameterState.currentParameterIndex <\r\n      this.parameterState.parameters.length\r\n    ) {\r\n      const currentParam =\r\n        this.parameterState.parameters[\r\n          this.parameterState.currentParameterIndex\r\n        ];\r\n      const message = `Please enter input for parameter \"${currentParam.name}\" (type: ${currentParam.type}):`;\r\n      this.addChatMessage('ai', message);\r\n    }\r\n  }\r\n\r\n  private addChatMessage(from: 'ai' | 'user', text: string): void {\r\n    this.chatMessages = [...this.chatMessages, { from, text }];\r\n  }\r\n\r\n  handleChatMessage(message: string): void {\r\n    if (!this.isExecuteMode) return;\r\n    this.addChatMessage('user', message);\r\n    if (this.waitingForRestartConfirmation) {\r\n      if (message.toLowerCase().trim() === 'yes') {\r\n        this.waitingForRestartConfirmation = false;\r\n        this.resetParameterState();\r\n        this.addChatMessage(\r\n          'ai',\r\n          'Restarting the parameter collection process...',\r\n        );\r\n        this.extractParameters();\r\n      } else {\r\n        this.waitingForRestartConfirmation = false;\r\n        this.addChatMessage(\r\n          'ai',\r\n          'Okay, feel free to ask if you need any further assistance.',\r\n        );\r\n      }\r\n    } else if (this.parameterState.isCollecting) {\r\n      this.handleParameterInput(message);\r\n    }\r\n    else {\r\n      this.addChatMessage('ai', 'Hello');\r\n    }\r\n  }\r\n\r\n  private handleParameterInput(input: string): void {\r\n    const currentParam =\r\n      this.parameterState.parameters[this.parameterState.currentParameterIndex];\r\n    let processedInput: any = input;\r\n\r\n    try {\r\n      switch (currentParam.type) {\r\n        case 'number':\r\n          processedInput = parseFloat(input);\r\n          if (isNaN(processedInput)) {\r\n            this.addChatMessage(\r\n              'ai',\r\n              `Invalid number format. Please enter a valid number for \"${currentParam.name}\":`,\r\n            );\r\n            return;\r\n          }\r\n          break;\r\n        case 'boolean':\r\n          const lowerInput = input.toLowerCase();\r\n          if (\r\n            lowerInput === 'true' ||\r\n            lowerInput === '1' ||\r\n            lowerInput === 'yes'\r\n          ) {\r\n            processedInput = true;\r\n          } else if (\r\n            lowerInput === 'false' ||\r\n            lowerInput === '0' ||\r\n            lowerInput === 'no'\r\n          ) {\r\n            processedInput = false;\r\n          } else {\r\n            this.addChatMessage(\r\n              'ai',\r\n              `Invalid boolean format. Please enter true/false for \"${currentParam.name}\":`,\r\n            );\r\n            return;\r\n          }\r\n          break;\r\n        case 'object':\r\n          try {\r\n            processedInput = JSON.parse(input);\r\n          } catch {\r\n            this.addChatMessage(\r\n              'ai',\r\n              `Invalid JSON format. Please enter a valid JSON object for \"${currentParam.name}\":`,\r\n            );\r\n            return;\r\n          }\r\n          break;\r\n        case 'array':\r\n          try {\r\n            processedInput = JSON.parse(input);\r\n            if (!Array.isArray(processedInput)) {\r\n              throw new Error('Not an array');\r\n            }\r\n          } catch {\r\n            this.addChatMessage(\r\n              'ai',\r\n              `Invalid array format. Please enter a valid JSON array for \"${currentParam.name}\":`,\r\n            );\r\n            return;\r\n          }\r\n          break;\r\n      }\r\n      this.parameterState.collectedInputs[currentParam.name] = processedInput;\r\n      this.parameterState.currentParameterIndex++;\r\n      if (\r\n        this.parameterState.currentParameterIndex >=\r\n        this.parameterState.parameters.length\r\n      ) {\r\n        this.executeToolWithParameters();\r\n      } else {\r\n        this.promptForNextParameter();\r\n      }\r\n    } catch (error) {\r\n      console.error('Error processing parameter input:', error);\r\n      this.addChatMessage(\r\n        'ai',\r\n        `Error processing input for \"${currentParam.name}\". Please try again:`,\r\n      );\r\n    }\r\n  }\r\n\r\n  private executeToolWithParameters(): void {\r\n    this.parameterState.isCollecting = false;\r\n    this.isProcessingChat = true;\r\n\r\n    this.addChatMessage(\r\n      'ai',\r\n      'All parameters collected! Executing your tool...',\r\n    );\r\n\r\n    const payload = {\r\n      class_definition: this.toolForm.get('classDefinition')?.value,\r\n      class_name: this.toolForm.get('toolClassName')?.value,\r\n      inputs: this.parameterState.collectedInputs,\r\n    };\r\n\r\n    this.toolsService.testTool(payload).subscribe({\r\n      next: (response: any) => {\r\n        console.log('Tool execution response:', response);\r\n        this.isProcessingChat = false;\r\n\r\n        if (response.status === 'success') {\r\n          this.addChatMessage(\r\n            'ai',\r\n            `Tool executed successfully! Output: ${response.output}`,\r\n          );\r\n        } else {\r\n          this.addChatMessage(\r\n            'ai',\r\n            `Tool execution failed: ${response.detail || 'Unknown error'}`,\r\n          );\r\n        }\r\n        this.waitingForRestartConfirmation = true;\r\n        setTimeout(() => {\r\n          this.addChatMessage(\r\n            'ai',\r\n            'Would you like to test the tool again with different parameters? (Type \"yes\" to restart or anything else to continue)',\r\n          );\r\n        }, 1000);\r\n      },\r\n      error: (error) => {\r\n        console.error('Tool execution error:', error);\r\n        this.isProcessingChat = false;\r\n        this.addChatMessage(\r\n          'ai',\r\n          `Tool execution failed: ${error?.error?.message || 'Unknown error occurred'}`,\r\n        );\r\n      },\r\n    });\r\n  }\r\n\r\n  onExit(): void {\r\n    // Always navigate back to tools landing page regardless of mode\r\n    const returnPage = this.route.snapshot.queryParamMap.get('returnPage');\r\n    const pageNumber = returnPage ? parseInt(returnPage) : 1;\r\n\r\n    console.log('Exiting create-tools component, navigating to tools landing page');\r\n    this.router.navigate(['/libraries/tools'], {\r\n      queryParams: { page: pageNumber },\r\n    });\r\n  }\r\n\r\n  private resetParameterState(): void {\r\n    this.parameterState = {\r\n      parameters: [],\r\n      currentParameterIndex: 0,\r\n      collectedInputs: {},\r\n      isCollecting: false,\r\n    };\r\n  }\r\n\r\n  getControl(name: string): FormControl {\r\n    return this.toolForm.get(name) as FormControl;\r\n  }\r\n\r\n  // Custom validator for whitespace\r\n  noWhitespaceValidator(control: AbstractControl): ValidationErrors | null {\r\n    const value = control.value;\r\n    if (value && typeof value === 'string' && value.trim().length === 0) {\r\n      return { invalidInput: true };\r\n    }\r\n    return null;\r\n  }\r\n\r\n  // // Custom validator for class definition\r\n  // validateClassDef(control: AbstractControl): ValidationErrors | null {\r\n  //   const className = this.toolForm?.controls['toolClassName'].value?.trim();\r\n  //   const classDef = control.value;\r\n  //   if (!className) return null;\r\n  //   const classRegex = new RegExp(`class\\\\s+${className}\\\\s*\\\\(\\\\s*BaseTool\\\\s*\\\\)`);\r\n  //   if (!classRegex.test(classDef)) {\r\n  //     return { invalidClass: { invalidClass: `Please Define: \"class ${className}(BaseTool)\"` }};\r\n  //   }\r\n  //   if (!classDef.includes('_run')) {\r\n  //     return { invalidClass: { invalidClass: `Please Define: \"_run\" Method` }};\r\n  //   }\r\n  //   return null;\r\n  // }\r\n\r\n  // Helper method to mark all form fields as touched\r\n  markFormGroupTouched(formGroup: FormGroup): void {\r\n    Object.keys(formGroup.controls).forEach(key => {\r\n      const control = formGroup.get(key);\r\n      control?.markAsTouched();\r\n      if (control instanceof FormGroup) {\r\n        this.markFormGroupTouched(control);\r\n      }\r\n    });\r\n  }\r\n\r\n  // Show validation error popup\r\n  showValidationErrorPopup(): void {\r\n    const errors: string[] = [];\r\n\r\n    // Check each field for errors\r\n    Object.keys(this.toolForm.controls).forEach(fieldName => {\r\n      const error = this.getFieldError(fieldName);\r\n      if (error) {\r\n        const fieldLabel = this.getFieldLabel(fieldName);\r\n        errors.push(`${fieldLabel}: ${error}`);\r\n      }\r\n    });\r\n\r\n    if (errors.length > 0) {\r\n      this.popupTitle = 'Validation Error';\r\n      this.popupMessage = errors.join('\\n\\n');\r\n      this.showErrorPopup = true;\r\n    }\r\n  }\r\n\r\n  // Get field label for error messages\r\n  getFieldLabel(fieldName: string): string {\r\n    switch (fieldName) {\r\n      case 'name': return this.labels.toolName;\r\n      case 'toolClassName': return this.labels.toolClassName;\r\n      case 'description': return this.labels.description;\r\n      case 'classDefinition': return this.labels.toolClassDefinition;\r\n      default: return fieldName;\r\n    }\r\n  }\r\n\r\n  // // Custom validator for whitespace\r\n  // noWhitespaceValidator(control: AbstractControl): ValidationErrors | null {\r\n  //   const value = control.value;\r\n  //   if (value && typeof value === 'string' && value.trim().length === 0) {\r\n  //     return { invalidInput: true };\r\n  //   }\r\n  //   return null;\r\n  // }\r\n\r\n  // // // Custom validator for class definition\r\n  // // validateClassDef(control: AbstractControl): ValidationErrors | null {\r\n  // //   const className = this.toolForm?.controls['toolClassName'].value?.trim();\r\n  // //   const classDef = control.value;\r\n  // //   if (!className) return null;\r\n  // //   const classRegex = new RegExp(`class\\\\s+${className}\\\\s*\\\\(\\\\s*BaseTool\\\\s*\\\\)`);\r\n  // //   if (!classRegex.test(classDef)) {\r\n  // //     return { invalidClass: { invalidClass: `Please Define: \"class ${className}(BaseTool)\"` }};\r\n  // //   }\r\n  // //   if (!classDef.includes('_run')) {\r\n  // //     return { invalidClass: { invalidClass: `Please Define: \"_run\" Method` }};\r\n  // //   }\r\n  // //   return null;\r\n  // // }\r\n\r\n  // // Helper method to mark all form fields as touched\r\n  // markFormGroupTouched(formGroup: FormGroup): void {\r\n  //   Object.keys(formGroup.controls).forEach(key => {\r\n  //     const control = formGroup.get(key);\r\n  //     control?.markAsTouched();\r\n  //     if (control instanceof FormGroup) {\r\n  //       this.markFormGroupTouched(control);\r\n  //     }\r\n  //   });\r\n  // }\r\n\r\n  // // Show validation error popup\r\n  // showValidationErrorPopup(): void {\r\n  //   const errors: string[] = [];\r\n\r\n  //   // Check each field for errors\r\n  //   Object.keys(this.toolForm.controls).forEach(fieldName => {\r\n  //     const error = this.getFieldError(fieldName);\r\n  //     if (error) {\r\n  //       const fieldLabel = this.getFieldLabel(fieldName);\r\n  //       errors.push(`${fieldLabel}: ${error}`);\r\n  //     }\r\n  //   });\r\n\r\n  //   if (errors.length > 0) {\r\n  //     this.popupTitle = 'Validation Error';\r\n  //     this.popupMessage = errors.join('\\n\\n');\r\n  //     this.showErrorPopup = true;\r\n  //   }\r\n  // }\r\n\r\n  // // Get field label for error messages\r\n  // getFieldLabel(fieldName: string): string {\r\n  //   switch (fieldName) {\r\n  //     case 'name': return this.labels.toolName;\r\n  //     case 'toolClassName': return this.labels.toolClassName;\r\n  //     case 'description': return this.labels.description;\r\n  //     case 'classDefinition': return this.labels.toolClassDefinition;\r\n  //     default: return fieldName;\r\n  //   }\r\n  // }\r\n\r\n  getOutputControl(name: string): FormControl {\r\n    return this.outputToolForm.get(name) as FormControl;\r\n  }\r\n  getFieldError(fieldName: string): string {\r\n    const field = this.toolForm.get(fieldName);\r\n    if (field && field.invalid && (field.touched || field.dirty)) {\r\n      if (field.errors?.['required']) {\r\n        if (fieldName === 'name') return this.labels.errorToolNameRequired;\r\n        if (fieldName === 'toolClassName')\r\n          return this.labels.errorToolClassNameRequired;\r\n        if (fieldName === 'description')\r\n          return this.labels.errorDescriptionRequired;\r\n        if (fieldName === 'classDefinition')\r\n          return this.labels.errorToolConfigRequired;\r\n        return this.labels.errorRequired || 'This field is required';\r\n      }\r\n      if (field.errors?.['invalidInput']) {\r\n        return 'Please enter a valid value (whitespace only is not allowed)';\r\n      }\r\n      if (field.errors?.['pattern']) {\r\n        if (fieldName === 'toolClassName') {\r\n          return 'Please enter a valid class name (must start with uppercase letter and contain only letters and numbers)';\r\n        }\r\n      }\r\n      if (field.errors?.['invalidClass']) {\r\n        return field.errors['invalidClass'].invalidClass;\r\n      }\r\n      // Add more error types as needed\r\n    }\r\n    return '';\r\n  }\r\n\r\n  validateCode = (): void => {\r\n    this.validateTool();\r\n  };\r\n\r\n  public validateTool(): void {\r\n    this.showValidationOutput = false;\r\n    this.validationOutput = '';\r\n    const useCase = 'VALIDATE_TOOLS';\r\n    const useCaseIdentifier =\r\n      'VALIDATE_TOOLS@ASCENDION@PLATFORM_ENGINEERING@AVA@DIGITAL_ASCENDER';\r\n    const toolClassDef = this.toolForm.controls['classDefinition'].value;\r\n    this.promptGenerateService\r\n      .modelApi(toolClassDef, useCase, false, useCaseIdentifier)\r\n      .subscribe({\r\n        next: (res: any) => {\r\n          let responseText = res?.response?.choices?.[0]?.text;\r\n          if (!responseText) {\r\n            this.validationOutput = 'Unable to validate, please try again.';\r\n            this.showValidationOutput = true;\r\n            if (this.codeEditor) this.codeEditor.hideProcessingLoader();\r\n            return;\r\n          }\r\n          // Remove markdown code block if present\r\n          responseText = responseText\r\n            .replace(/```json\\n?/, '')\r\n            .replace(/```\\n?$/, '');\r\n          try {\r\n            const parsed = JSON.parse(responseText);\r\n            // Format as plain text\r\n            let formatted = '';\r\n            if (\r\n              parsed.issues &&\r\n              Array.isArray(parsed.issues) &&\r\n              parsed.issues.length > 0\r\n            ) {\r\n              parsed.issues.forEach((issue: any, idx: number) => {\r\n                formatted += `Issue ${idx + 1}:\\n`;\r\n                formatted += `  Type: ${issue.type}\\n`;\r\n                formatted += `  Description: ${issue.description}\\n`;\r\n                formatted += `  Severity: ${issue.severity}\\n`;\r\n                formatted += `  Line Number: ${issue.line_number}\\n`;\r\n                formatted += `  Suggestion: ${issue.suggestion}\\n\\n`;\r\n              });\r\n            } else {\r\n              formatted = 'No issues found.';\r\n            }\r\n            this.validationOutput = formatted;\r\n          } catch (e) {\r\n            this.validationOutput = responseText;\r\n          }\r\n          this.showValidationOutput = true;\r\n          if (this.codeEditor) this.codeEditor.hideProcessingLoader();\r\n        },\r\n        error: (e) => {\r\n          this.validationOutput =\r\n            'Error: ' + (e?.error?.message || 'Unknown error');\r\n          this.showValidationOutput = true;\r\n          if (this.codeEditor) this.codeEditor.hideProcessingLoader();\r\n        },\r\n      });\r\n  }\r\n\r\n  loadToolData(toolId: number): void {\r\n    this.toolsService.getUserToolDetails(toolId).subscribe(\r\n      (response) => {\r\n        console.log('API response:', response); // Debug log\r\n        const tool = response.tools && response.tools[0];\r\n        console.log('Tool for patching:', tool); // Debug log\r\n        console.log('Tool ID:', tool?.toolId);\r\n        if (tool) {\r\n          this.originalToolData = tool;\r\n          console.log('Original tool data structure:', JSON.stringify(tool, null, 2));\r\n          this.toolForm.patchValue({\r\n            name: this.isCloneMode ? '' : tool.toolName || '',\r\n            description: tool.toolDescription || '',\r\n            toolClassName: tool.toolClassName || '',\r\n            classDefinition: tool.toolClassDef || '',\r\n          });\r\n          // Set code editor value, but only if editor is ready\r\n          if (this.codeEditor) {\r\n            this.codeEditor.setValue(tool.toolClassDef || '');\r\n            // Force Monaco editor layout after setting content\r\n            setTimeout(() => {\r\n              if (this.codeEditor?.isReady) {\r\n                this.codeEditor['editor']?.layout();\r\n              }\r\n            }, 100);\r\n          } else {\r\n            this.codeEditorValueToSet = tool.toolClassDef || '';\r\n          }\r\n\r\n          // Auto-start parameter extraction when in execute mode\r\n          if (this.isExecuteMode) {\r\n            console.log('Auto-starting parameter extraction in execute mode...');\r\n            // Small delay to ensure form is fully populated and UI is ready\r\n            setTimeout(() => {\r\n              this.extractParameters();\r\n            }, 500);\r\n          }\r\n        }\r\n      },\r\n      (error) => {\r\n        console.error('Error loading tool data:', error);\r\n      },\r\n    );\r\n  }\r\n\r\n  //Drop Down\r\n  promptOptions: DropdownOption[] = [\r\n    { value: 'default', name: 'Choose Prompt' },\r\n    { value: 'ruby-developer', name: 'Senior Ruby Developer' },\r\n    { value: 'python-developer', name: 'Python Developer' },\r\n    { value: 'data-scientist', name: 'Data Scientist' },\r\n    { value: 'frontend-developer', name: 'Frontend Developer' },\r\n  ];\r\n\r\n  onPromptChanged(option: DropdownOption) {\r\n    console.log('Prompt changed in parent:', option);\r\n    // your logic to handle selected prompt\r\n  }\r\n\r\n  onEditorAction(idx: number) {\r\n    if (!this.codeEditor) return;\r\n    if (idx === 0) this.codeEditor.selectAll();\r\n    if (idx === 1) this.codeEditor.clear();\r\n  }\r\n\r\n  handleApproval() {\r\n    if (!this.isEditMode || !this.toolId) {\r\n      console.error('Cannot send for approval: Tool must be saved first');\r\n      return;\r\n    }\r\n\r\n    const returnPage = this.route.snapshot.queryParamMap.get('returnPage');\r\n    const pageNumber = returnPage ? parseInt(returnPage) : 1;\r\n\r\n    // Create approval payload with the exact structure required\r\n    const approvalPayload = {\r\n      id: parseInt(this.toolId.toString()),\r\n      name: this.toolForm.get('name')?.value,\r\n      description: this.toolForm.get('description')?.value,\r\n      createdBy: this.originalToolData?.createdBy || this.getUserSignature(),\r\n      modifiedBy: this.getUserSignature(), // Use current user signature for modifiedBy\r\n      createdAt:\r\n        this.originalToolData?.createTimestamp || this.formatLocalDateTime(),\r\n      'put ': this.formatLocalDateTime(), // Using LocalDateTime format without timezone\r\n      isDeleted: true, // Set to true for approval request\r\n      toolConfigs: {\r\n        image: this.originalToolData?.toolImage || '',\r\n        def: this.toolForm.get('classDefinition')?.value,\r\n        CHANGE2: this.toolForm.get('toolClassName')?.value || 'TEST',\r\n      },\r\n    };\r\n\r\n    console.log('Sending for approval with payload:', approvalPayload);\r\n    console.log(\r\n      'Approval payload structure:',\r\n      JSON.stringify(approvalPayload, null, 2),\r\n    );\r\n    console.log(\r\n      'API Endpoint: /v2/api/admin/ava/force/da/userTools/change_request',\r\n    );\r\n\r\n    this.toolsService.updateUserTool(approvalPayload).subscribe(\r\n      (response) => {\r\n        console.log('Approval request successful:', response);\r\n        // You can add a success message or redirect here\r\n        this.router.navigate(['/libraries/tools'], {\r\n          queryParams: { page: pageNumber },\r\n        });\r\n      },\r\n      (error) => {\r\n        console.error('Error sending for approval:', error);\r\n        console.error('Error details:', error.error);\r\n        console.error('Status:', error.status);\r\n        console.error('Status text:', error.statusText);\r\n      },\r\n    );\r\n  }\r\n\r\n  // Popup handling methods\r\n  showSuccessMessage(title: string, message: string): void {\r\n    console.log('showSuccessMessage called:', title, message);\r\n    this.popupTitle = title;\r\n    this.popupMessage = message;\r\n    this.showSuccessPopup = true;\r\n    this.shouldCallExtractParams = true;\r\n    console.log('showSuccessPopup set to:', this.showSuccessPopup);\r\n  }\r\n\r\n  showErrorMessage(title: string, message: string): void {\r\n    console.log('showErrorMessage called:', title, message);\r\n    this.popupTitle = title;\r\n    this.popupMessage = message;\r\n    this.showErrorPopup = true;\r\n    this.shouldCallExtractParams = false;\r\n    console.log('showErrorPopup set to:', this.showErrorPopup);\r\n  }\r\n\r\n  closeSuccessPopup(): void {\r\n    console.log('closeSuccessPopup called');\r\n    this.showSuccessPopup = false;\r\n    this.popupTitle = '';\r\n    this.popupMessage = '';\r\n  }\r\n\r\n  closeErrorPopup(): void {\r\n    console.log('closeErrorPopup called');\r\n    this.showErrorPopup = false;\r\n    this.popupTitle = '';\r\n    this.popupMessage = '';\r\n  }\r\n\r\n  // Handle success popup confirmation - call extract params API\r\n  onSuccessConfirm(): void {\r\n    console.log('onSuccessConfirm called, shouldCallExtractParams:', this.shouldCallExtractParams);\r\n    this.closeSuccessPopup();\r\n\r\n    if (this.shouldCallExtractParams) {\r\n      console.log('Calling extractParameters...');\r\n      this.extractParameters();\r\n    }\r\n  }\r\n\r\n  // Get tool name for display in playground\r\n  getToolDisplayName(): string {\r\n    const toolName = this.toolForm.get('name')?.value;\r\n    return toolName || 'Tool Name';\r\n  }\r\n\r\n  onClickGenerate(prompt: string) {\r\n    this.isLoading = true;\r\n    this.promptGenerateService\r\n      .modelApi(\r\n        prompt,\r\n        ToolModes.toolCreation,\r\n        false,\r\n        TOOL_CREATION_USECASE_IDENTIFIER,\r\n      )\r\n      .subscribe({\r\n        next: (res) => {\r\n          let generatedPrompt: any = {};\r\n          try {\r\n            generatedPrompt = JSON.parse(res?.response?.choices[0]?.text);\r\n          } catch (error) {\r\n            // this.askAiModal.disablePrimaryButton = true;\r\n            // this.errorMsg = 'Unable To Generate Tool Definition. Please Try Again.';\r\n            return;\r\n          }\r\n          // this.askAiModal.disablePrimaryButton = false;\r\n          const {\r\n            tool_name,\r\n            tool_class,\r\n            tool_description,\r\n            tool_class_defination,\r\n          } = generatedPrompt;\r\n          this.outputToolForm.patchValue({\r\n            name: tool_name,\r\n            toolClassName: tool_class,\r\n            description: tool_description,\r\n            classDefinition: tool_class_defination.replace(/```\\n?$/, ''),\r\n          });\r\n\r\n          this.showToolOutput = true;\r\n          this.isLoading = false;\r\n        },\r\n        error: () => {\r\n          this.isLoading = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  toggleAskAvaModal(show = true) {\r\n    this.showAskAvaModal = show;\r\n  }\r\n\r\n  onUse() {\r\n    const formValue = this.outputToolForm.getRawValue();\r\n    this.toolForm.patchValue(formValue);\r\n    this.codeEditor.setValue(formValue.classDefinition);\r\n    this.outputToolForm.reset();\r\n    this.showToolOutput = false;\r\n    this.prompt.reset('');\r\n    this.toggleAskAvaModal(false);\r\n  }\r\n\r\n  onReset() {\r\n    this.outputToolForm.reset();\r\n    this.showToolOutput = false;\r\n  }\r\n\r\n  onCancle() {\r\n    this.prompt.reset('');\r\n    this.onReset();\r\n    this.toggleAskAvaModal(false);\r\n  }\r\n\r\n  onClose() {\r\n    this.toggleAskAvaModal(false);\r\n    this.onCancle();\r\n  }\r\n}\r\n", "<main-layout [showRightPane]=\"true\" leftPaneTitle=\"Tool Description\" rightPaneTitle=\"Tool Playground\">\r\n  <ng-container right-header>\r\n    <div class=\"right-header-content\">\r\n      <span class=\"pane-title\">Tool Playground</span>\r\n      <ng-container *ngIf=\"isEditMode && !isExecuteMode && toolId\">\r\n        <ava-button label=\"Send for Approval\" variant=\"primary\" size=\"small\" class=\"approval-btn\"\r\n          (userClick)=\"handleApproval()\"></ava-button>\r\n      </ng-container>\r\n    </div>\r\n  </ng-container>\r\n\r\n  <!-- Custom header content for center pane -->\r\n  <div center-header class=\"center-header-buttons\">\r\n\r\n\r\n    <ava-button label=\"{{ labels.exit }}\" variant=\"secondary\" size=\"small\" [customStyles]=\"{\r\n        'border': '2px solid transparent',\r\n        'background-image': 'linear-gradient(#ffffff, #ffffff), linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n        'background-origin': 'border-box',\r\n        'background-clip': 'padding-box, border-box',\r\n        '--button-effect-color': '33, 90, 214'\r\n      }\" (userClick)=\"onExit()\"></ava-button>\r\n\r\n    <ng-container *ngIf=\"!isEditMode && !isExecuteMode\">\r\n      <ava-button\r\n        label=\"Save\"\r\n        variant=\"primary\"\r\n        size=\"small\"\r\n        [customStyles]=\"{\r\n          background: 'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n          '--button-effect-color': '33, 90, 214'\r\n        }\" (userClick)=\"onSave()\"></ava-button>\r\n    </ng-container>\r\n\r\n    <ng-container *ngIf=\"isEditMode && !isExecuteMode\">\r\n      <ava-button\r\n        label=\"Update\"\r\n        variant=\"primary\"\r\n        size=\"small\"\r\n        [customStyles]=\"{\r\n          background: 'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n          '--button-effect-color': '33, 90, 214'\r\n        }\"\r\n        (userClick)=\"onUpdate()\"\r\n      ></ava-button>\r\n    </ng-container>\r\n  </div>\r\n\r\n  <!-- Left Pane: Tool Description -->\r\n  <div left class=\"tool-description-pane\">\r\n    <div class=\"form-field-group\">\r\n      <ava-textbox [formControl]=\"getControl('name')\" [label]=\"labels.toolName\" id=\"toolName\" name=\"toolName\"\r\n        placeholder=\"Enter tool name\" variant=\"primary\" size=\"md\" [fullWidth]=\"true\" [required]=\"true\"\r\n        [readonly]=\"isFieldsDisabled\"></ava-textbox>\r\n    </div>\r\n\r\n    <div class=\"form-field-group\">\r\n      <ava-textbox [formControl]=\"getControl('toolClassName')\" [label]=\"labels.toolClassName\" id=\"toolClassName\"\r\n        name=\"toolClassName\" placeholder=\"Enter tool class\" variant=\"primary\" size=\"md\" [fullWidth]=\"true\"\r\n        [required]=\"true\" [readonly]=\"isFieldsDisabled\"></ava-textbox>\r\n    </div>\r\n\r\n    <div class=\"form-field-group\">\r\n      <ava-textarea id=\"description\" name=\"description\" [label]=\"labels.description\"\r\n        [formControl]=\"getControl('description')\" placeholder=\"Enter description\" [rows]=\"6\" size=\"md\"\r\n        [fullWidth]=\"true\" [required]=\"true\" [readonly]=\"isFieldsDisabled\"></ava-textarea>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Center Pane: Tool Editor -->\r\n  <div center class=\"tool-editor-pane\">\r\n    <!-- Custom Header with Title and Action Buttons -->\r\n    <div class=\"custom-center-header\">\r\n      <span class=\"pane-title\">Tool Editor</span>\r\n      <hr>\r\n      <div class=\"header-buttons d-flex\">\r\n        <ava-button label=\"{{ labels.askAva}}\" variant=\"secondary\" size=\"small\" [customStyles]=\"{\r\n            'border': '2px solid transparent',\r\n            'background-image': 'linear-gradient(#ffffff, #ffffff), linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n            'background-origin': 'border-box',\r\n            'background-clip': 'padding-box, border-box',\r\n            '--button-effect-color': '33, 90, 214'\r\n          }\"  iconName=\"WandSparkles\"\r\n          (userClick)=\"toggleAskAvaModal()\">\r\n        </ava-button>\r\n        <ava-button label=\"{{ labels.exit }}\" variant=\"secondary\" size=\"small\" [customStyles]=\"{\r\n            'border': '2px solid transparent',\r\n            'background-image': 'linear-gradient(#ffffff, #ffffff), linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n            'background-origin': 'border-box',\r\n            'background-clip': 'padding-box, border-box',\r\n            '--button-effect-color': '33, 90, 214'\r\n          }\" (userClick)=\"onExit()\"></ava-button>\r\n\r\n        <ng-container *ngIf=\"!isEditMode && !isExecuteMode\">\r\n          <ava-button\r\n            label=\"Save & Send Approval\"\r\n            variant=\"primary\"\r\n            size=\"small\"\r\n            [customStyles]=\"{\r\n              background: 'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n              '--button-effect-color': '33, 90, 214'\r\n            }\" (userClick)=\"onSave()\"></ava-button>\r\n        </ng-container>\r\n\r\n        <ng-container *ngIf=\"isEditMode && !isExecuteMode\">\r\n          <ava-button\r\n            label=\"Update & Send Approval\"\r\n            variant=\"primary\"\r\n            size=\"small\"\r\n            [customStyles]=\"{\r\n              background: 'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n              '--button-effect-color': '33, 90, 214'\r\n            }\"\r\n            (userClick)=\"onUpdate()\"\r\n          ></ava-button>\r\n        </ng-container>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Code Editor -->\r\n    <div class=\"editor-container\">\r\n      <app-code-editor #codeEditor placeholder=\"{{ placeholder }}\" language=\"python\"\r\n        [Control]=\"getControl('classDefinition')\" customCssClass=\"tools-monaco-editor\"\r\n        (primaryButtonSelected)=\"validateCode()\" footerText=\"{{ labels.note }}\" [actionButtons]=\"editorActions\"\r\n        (actionButtonClicked)=\"onEditorAction($event)\" [readonly]=\"isFieldsDisabled\"></app-code-editor>\r\n    </div>\r\n\r\n    <!-- Tool Compiler -->\r\n    <div class=\"validation-output-section\">\r\n      <app-code-editor\r\n        [title]=\"validationOutputEditorConfig.title\"\r\n        [language]=\"validationOutputEditorConfig.language\"\r\n        [theme]=\"validationOutputEditorConfig.theme\"\r\n        [readonly]=\"validationOutputEditorConfig.readOnly\"\r\n        [height]=\"validationOutputEditorConfig.height\"\r\n        [value]=\"showValidationOutput ? validationOutput : ''\"\r\n        [placeholder]=\"validationOutputEditorConfig.placeholder\"\r\n        customCssClass=\"validation-json-editor\"\r\n      ></app-code-editor>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Right Pane: Tool Playground -->\r\n  <div right class=\"tool-playground-pane\">\r\n    <div class=\"playground-container\">\r\n      <app-playground\r\n        [promptOptions]=\"promptOptions\"\r\n        [messages]=\"chatMessages\"\r\n        [isLoading]=\"isProcessingChat\"\r\n        [showChatInteractionToggles]=\"false\"\r\n        [showAiPrincipleToggle]=\"true\"\r\n        [showDropdown]=\"false\"\r\n        [showAgentNameInput]=\"true\"\r\n        [displayedAgentName]=\"getToolDisplayName()\"\r\n        [agentNamePlaceholder]=\"'Current Tool Name'\"\r\n        [showApprovalButton]=\"false\"\r\n        (promptChange)=\"onPromptChanged($event)\"\r\n        (messageSent)=\"handleChatMessage($event)\"\r\n        (approvalRequested)=\"handleApproval()\"\r\n      ></app-playground>\r\n    </div>\r\n  </div>\r\n</main-layout>\r\n\r\n<!-- Success Popup -->\r\n<ava-popup\r\n  [show]=\"showSuccessPopup\"\r\n  [title]=\"popupTitle\"\r\n  [message]=\"popupMessage\"\r\n  [showHeaderIcon]=\"true\"\r\n  headerIconName=\"check-circle\"\r\n  iconColor=\"#28a745\"\r\n  [showClose]=\"true\"\r\n  [showCancel]=\"false\"\r\n  [showConfirm]=\"true\"\r\n  [confirmButtonLabel]=\"'OK'\"\r\n  [confirmButtonVariant]=\"'primary'\"\r\n  [confirmButtonBackground]=\"'#28a745'\"\r\n  (confirm)=\"onSuccessConfirm()\"\r\n  (closed)=\"closeSuccessPopup()\"\r\n>\r\n</ava-popup>\r\n\r\n<!-- Error Popup -->\r\n<ava-popup\r\n  [show]=\"showErrorPopup\"\r\n  [title]=\"popupTitle\"\r\n  [message]=\"popupMessage\"\r\n  [showHeaderIcon]=\"true\"\r\n  headerIconName=\"alert-circle\"\r\n  iconColor=\"#dc3545\"\r\n  [showClose]=\"true\"\r\n  [showCancel]=\"false\"\r\n  [showConfirm]=\"true\"\r\n  [confirmButtonLabel]=\"'OK'\"\r\n  [confirmButtonVariant]=\"'primary'\"\r\n  [confirmButtonBackground]=\"'#dc3545'\"\r\n  (confirm)=\"closeErrorPopup()\"\r\n  (closed)=\"closeErrorPopup()\"\r\n>\r\n</ava-popup>\r\n\r\n<!-- Ask AVA Wrapper -->\r\n<app-ask-ava-wrapper [show]=\"showAskAvaModal\" [prompt]=\"prompt\" [isLoading]=\"isLoading\" [showOutput]=\"showToolOutput\"\r\n  (oNClickGenerate)=\"onClickGenerate($event)\" (oNClickClosed)=\"onClose()\" (oNClickUse)=\"onUse()\" (oNClickReset)=\"onReset()\"\r\n  (oNClickCancel)=\"onCancle()\">\r\n  <form [formGroup]=\"outputToolForm\">\r\n    <div class=\"row g-2\">\r\n      <div class=\"col-md-6\">\r\n        <ava-textbox [formControl]=\"getOutputControl('name')\" [label]=\"labels.toolName\" id=\"toolName\" name=\"toolName\"\r\n          placeholder=\"Enter tool name\" variant=\"primary\" size=\"md\" [readonly]=\"true\">\r\n        </ava-textbox>\r\n      </div>\r\n      <div class=\"col-md-6\">\r\n        <ava-textbox [formControl]=\"getOutputControl('toolClassName')\" [label]=\"labels.toolClassName\" id=\"toolClassName\"\r\n          name=\"toolClassName\" placeholder=\"Enter tool class\" variant=\"primary\" size=\"md\" [readonly]=\"true\">\r\n        </ava-textbox>\r\n      </div>\r\n      <div class=\"col-md-12\">\r\n        <ava-textarea id=\"description\" name=\"description\" [label]=\"labels.description\"\r\n          [formControl]=\"getOutputControl('description')\" placeholder=\"Enter description\" [rows]=\"4\" size=\"md\"\r\n          [fullWidth]=\"true\" [readonly]=\"true\">\r\n        </ava-textarea>\r\n      </div>\r\n      <div class=\"col-md-12\">\r\n        <app-code-editor id=\"outputEditor\" #codeEditor title=\"{{ labels.toolClassDefinition }}\"\r\n          language=\"python\" [value]=\"getOutputControl('classDefinition').value\"\r\n          customCssClass=\"tools-monaco-editor\" footerText=\"{{ labels.note }}\"\r\n          [readonly]=\"true\">\r\n        </app-code-editor>\r\n      </div>\r\n    </div>\r\n  </form>\r\n</app-ask-ava-wrapper>"], "mappings": "AAAA,SAIEA,YAAY,QAIP,eAAe;AACtB,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAEEC,SAAS,EACTC,mBAAmB,EACnBC,WAAW,EACXC,UAAU,QAGL,gBAAgB;AAKvB,SAASC,YAAY,QAAQ,MAAM;AAGnC,SACEC,mBAAmB,QAId,iEAAiE;AACxE,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,SAASC,mBAAmB,QAAQ,4EAA4E;AAChH,SACEC,oBAAoB,EACpBC,mBAAmB,EACnBC,eAAe,EAEfC,cAAc,QAET,wBAAwB;AAC/B,SAASC,mBAAmB,QAAQ,8EAA8E;AAClH,SACEC,gCAAgC,EAChCC,SAAS,QACJ,2BAA2B;AAClC,SAASC,sBAAsB,QAAQ,sFAAsF;;;;;;;;;;;;;;;;;;;;;;;IC3CvHC,EAAA,CAAAC,uBAAA,GAA6D;IAC3DD,EAAA,CAAAE,cAAA,qBACiC;IAA/BF,EAAA,CAAAG,UAAA,uBAAAC,6EAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAaF,MAAA,CAAAG,cAAA,EAAgB;IAAA,EAAC;IAACV,EAAA,CAAAW,YAAA,EAAa;;;;;;;IAiBlDX,EAAA,CAAAC,uBAAA,GAAoD;IAClDD,EAAA,CAAAE,cAAA,qBAO4B;IAAvBF,EAAA,CAAAG,UAAA,uBAAAS,6EAAA;MAAAZ,EAAA,CAAAK,aAAA,CAAAQ,GAAA;MAAA,MAAAN,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAaF,MAAA,CAAAO,MAAA,EAAQ;IAAA,EAAC;IAACd,EAAA,CAAAW,YAAA,EAAa;;;;IAHvCX,EAAA,CAAAe,SAAA,EAGE;IAHFf,EAAA,CAAAgB,UAAA,iBAAAhB,EAAA,CAAAiB,eAAA,IAAAC,GAAA,EAGE;;;;;;IAGNlB,EAAA,CAAAC,uBAAA,GAAmD;IACjDD,EAAA,CAAAE,cAAA,qBASC;IADCF,EAAA,CAAAG,UAAA,uBAAAgB,6EAAA;MAAAnB,EAAA,CAAAK,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAaF,MAAA,CAAAc,QAAA,EAAU;IAAA,EAAC;IACzBrB,EAAA,CAAAW,YAAA,EAAa;;;;IALZX,EAAA,CAAAe,SAAA,EAGE;IAHFf,EAAA,CAAAgB,UAAA,iBAAAhB,EAAA,CAAAiB,eAAA,IAAAC,GAAA,EAGE;;;;;;IAmDFlB,EAAA,CAAAC,uBAAA,GAAoD;IAClDD,EAAA,CAAAE,cAAA,qBAO4B;IAAvBF,EAAA,CAAAG,UAAA,uBAAAmB,8EAAA;MAAAtB,EAAA,CAAAK,aAAA,CAAAkB,GAAA;MAAA,MAAAhB,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAaF,MAAA,CAAAO,MAAA,EAAQ;IAAA,EAAC;IAACd,EAAA,CAAAW,YAAA,EAAa;;;;IAHvCX,EAAA,CAAAe,SAAA,EAGE;IAHFf,EAAA,CAAAgB,UAAA,iBAAAhB,EAAA,CAAAiB,eAAA,IAAAC,GAAA,EAGE;;;;;;IAGNlB,EAAA,CAAAC,uBAAA,GAAmD;IACjDD,EAAA,CAAAE,cAAA,qBASC;IADCF,EAAA,CAAAG,UAAA,uBAAAqB,8EAAA;MAAAxB,EAAA,CAAAK,aAAA,CAAAoB,GAAA;MAAA,MAAAlB,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAaF,MAAA,CAAAc,QAAA,EAAU;IAAA,EAAC;IACzBrB,EAAA,CAAAW,YAAA,EAAa;;;;IALZX,EAAA,CAAAe,SAAA,EAGE;IAHFf,EAAA,CAAAgB,UAAA,iBAAAhB,EAAA,CAAAiB,eAAA,IAAAC,GAAA,EAGE;;;AD5Bd,WAAaQ,oBAAoB;EAA3B,MAAOA,oBAAoB;IAoErBC,EAAA;IACAC,MAAA;IACAC,KAAA;IACAC,oBAAA;IACAC,YAAA;IACAC,qBAAA;IACAC,YAAA;IAzEsBC,UAAU;IAE1CC,MAAM,GAAkB,IAAI;IAC5BC,UAAU,GAAY,KAAK;IAC3BC,WAAW,GAAY,KAAK;IAC5BC,aAAa,GAAY,IAAI;IAC7BC,gBAAgB,GAAY,KAAK;IACjCC,iBAAiB,GAAY,IAAI;IACjCC,YAAY,GAAkB,IAAI;IAClCC,QAAQ;IACRC,cAAc;IACdC,YAAY,GAAkB,EAAE;IAChCC,gBAAgB,GAAY,KAAK;IACjCC,eAAe,GAAG,KAAK;IACvBC,cAAc,GAAG,KAAK;IACtBC,SAAS,GAAG,KAAK;IAEjBC,MAAM,GAAG,IAAI/D,WAAW,CAAC,EAAE,CAAC;IAE5BgE,cAAc,GAA6B;MACzCC,UAAU,EAAE,EAAE;MACdC,qBAAqB,EAAE,CAAC;MACxBC,eAAe,EAAE,EAAE;MACnBC,YAAY,EAAE;KACf;IAEOC,qBAAqB,GAAiB,IAAInE,YAAY,EAAE;IACxDoE,6BAA6B,GAAY,KAAK;IAC/CC,WAAW,GAAQnE,SAAS,CAACoE,gBAAgB,CAACC,YAAY;IAC1DC,MAAM,GAAQtE,SAAS,CAACsE,MAAM;IAC3BC,YAAY,GAAG,IAAI/E,YAAY,EAAU;IAC5CgF,gBAAgB,GAAW,EAAE;IAC7BC,oBAAoB,GAAY,KAAK;IACrCC,4BAA4B,GAAG;MACpCC,KAAK,EAAE,eAAe;MACtBC,QAAQ,EAAE,MAAsB;MAChCC,KAAK,EAAE,OAA0B;MACjCC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,OAAO;MACfZ,WAAW,EAAE;KACd;IAEMa,aAAa,GAAyB,CAC3C;MAAEC,KAAK,EAAE,YAAY;MAAEC,KAAK,EAAE,WAAW;MAAEC,WAAW,EAAE,EAAE;MAAEC,IAAI,EAAE;IAAE,CAAE,EACtE;MAAEH,KAAK,EAAE,OAAO;MAAEC,KAAK,EAAE,WAAW;MAAEC,WAAW,EAAE,EAAE;MAAEC,IAAI,EAAE;IAAE,CAAE,CAClE;IAEOC,gBAAgB,GAAQ,IAAI;IAC5BC,oBAAoB,GAAkB,IAAI;IAElD;IACAC,gBAAgB,GAAG,KAAK;IACxBC,cAAc,GAAG,KAAK;IACtBC,UAAU,GAAG,EAAE;IACfC,YAAY,GAAG,EAAE;IACTC,uBAAuB,GAAG,KAAK;IAEvCC,cAAcA,CAAA;MACZ,OAAO,IAAI,CAACvD,EAAE,CAACwD,KAAK,CAAC;QACjBC,IAAI,EAAE,CAAC,EAAE,EAAE,CAACjG,UAAU,CAACkG,QAAQ,EAAE,IAAI,CAACC,qBAAqB,CAAC,CAAC;QAC/DC,WAAW,EAAE,CAAC,EAAE,EAAE,CAACpG,UAAU,CAACkG,QAAQ,EAAE,IAAI,CAACC,qBAAqB,CAAC,CAAC;QACpEE,aAAa,EAAE,CAAC,EAAE,EAAE,CAACrG,UAAU,CAACkG,QAAQ,EAAE,IAAI,CAACC,qBAAqB,EAAEnG,UAAU,CAACsG,OAAO,CAAC,qBAAqB,CAAC,CAAC,CAAC;QACjHC,eAAe,EAAE,CAAC,EAAE,EAAE,CAACvG,UAAU,CAACkG,QAAQ,EAAE,IAAI,CAACC,qBAAqB,CAAC;OACxE,CAAC;IACJ;IAEAK,YACUhE,EAAe,EACfC,MAAc,EACdC,KAAqB,EACrBC,oBAA0C,EAC1CC,YAA0B,EAC1BC,qBAA2C,EAC3CC,YAAiC;MANjC,KAAAN,EAAE,GAAFA,EAAE;MACF,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,KAAK,GAALA,KAAK;MACL,KAAAC,oBAAoB,GAApBA,oBAAoB;MACpB,KAAAC,YAAY,GAAZA,YAAY;MACZ,KAAAC,qBAAqB,GAArBA,qBAAqB;MACrB,KAAAC,YAAY,GAAZA,YAAY;MAEpB,IAAI,CAACS,QAAQ,GAAG,IAAI,CAACwC,cAAc,EAAE;MACrC,IAAI,CAACvC,cAAc,GAAG,IAAI,CAACuC,cAAc,EAAE;IAC7C;IAEQU,gBAAgBA,CAAA;MACtB,MAAMC,aAAa,GACjB,IAAI,CAAC5D,YAAY,CAAC6D,aAAa,EAAE,IAAI,uBAAuB;MAC9D,OAAOD,aAAa;IACtB;IAEQE,mBAAmBA,CAACC,IAAA,GAAa,IAAIC,IAAI,EAAE;MACjD;MACA;MACA,MAAMC,IAAI,GAAGF,IAAI,CAACG,WAAW,EAAE;MAC/B,MAAMC,KAAK,GAAGC,MAAM,CAACL,IAAI,CAACM,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAC1D,MAAMC,GAAG,GAAGH,MAAM,CAACL,IAAI,CAACS,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACnD,MAAMG,KAAK,GAAGL,MAAM,CAACL,IAAI,CAACW,QAAQ,EAAE,CAAC,CAACJ,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACtD,MAAMK,OAAO,GAAGP,MAAM,CAACL,IAAI,CAACa,UAAU,EAAE,CAAC,CAACN,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAC1D,MAAMO,OAAO,GAAGT,MAAM,CAACL,IAAI,CAACe,UAAU,EAAE,CAAC,CAACR,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAE1D;MACA,MAAMS,YAAY,GAAGhB,IAAI,CAACiB,eAAe,EAAE;MAC3C,MAAMC,YAAY,GAAGb,MAAM,CAACW,YAAY,GAAG,IAAI,GAAGG,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAACd,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAEpG,OAAO,GAAGL,IAAI,IAAIE,KAAK,IAAII,GAAG,IAAIE,KAAK,IAAIE,OAAO,IAAIE,OAAO,IAAII,YAAY,EAAE;IACjF;IAEAI,QAAQA,CAAA;MACN,MAAMC,WAAW,GAAG,IAAI,CAAC1F,KAAK,CAAC2F,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;MAC1D,IAAI,CAACvF,MAAM,GAAGoF,WAAW,GAAGI,QAAQ,CAACJ,WAAW,EAAE,EAAE,CAAC,GAAG,IAAI;MAC5D,MAAMK,OAAO,GAAG,IAAI,CAAChG,MAAM,CAACiG,GAAG;MAC/B,IAAI,CAACzF,UAAU,GAAGwF,OAAO,CAACE,QAAQ,CAAC,QAAQ,CAAC;MAC5C,IAAI,CAACzF,WAAW,GAAGuF,OAAO,CAACE,QAAQ,CAAC,SAAS,CAAC;MAC9C,IAAI,CAACxF,aAAa,GAAGsF,OAAO,CAACE,QAAQ,CAAC,WAAW,CAAC;MAClD,IAAI,CAACvF,gBAAgB,GAAG,IAAI,CAACD,aAAa;MAE1CyF,OAAO,CAACC,GAAG,CACT,oBAAoB,EACpB,IAAI,CAAC7F,MAAM,EACX,aAAa,EACb,IAAI,CAACC,UAAU,EACf,cAAc,EACd,IAAI,CAACC,WAAW,EAChB,gBAAgB,EAChB,IAAI,CAACC,aAAa,CACnB;MAED,IACE,CAAC,IAAI,CAACF,UAAU,IAAI,IAAI,CAACC,WAAW,IAAI,IAAI,CAACC,aAAa,KAC1D,IAAI,CAACH,MAAM,EACX;QACA,IAAI,CAAC8F,YAAY,CAAC,IAAI,CAAC9F,MAAM,CAAC;QAC9B,IAAI,IAAI,CAACG,aAAa,EAAE;UACtB,IAAI,CAAC4F,sBAAsB,EAAE;QAC/B;MACF;MAEA;MACA,IAAI,CAACxF,QAAQ,CAACyF,QAAQ,CAAC,eAAe,CAAC,CAACC,YAAY,CAACC,SAAS,CAAC,MAAK;QAClE,IAAI,CAAC3F,QAAQ,CAACyF,QAAQ,CAAC,iBAAiB,CAAC,CAACG,sBAAsB,EAAE;MACpE,CAAC,CAAC;MAEF;IACF;IAEAC,eAAeA,CAAA;MACb;MACA,IAAI,IAAI,CAACrG,UAAU,IAAI,IAAI,CAAC0C,oBAAoB,EAAE;QAChD,IAAI,CAAC1C,UAAU,CAACsG,QAAQ,CAAC,IAAI,CAAC5D,oBAAoB,CAAC;QACnD,IAAI,CAACA,oBAAoB,GAAG,IAAI;QAEhC;QACA6D,UAAU,CAAC,MAAK;UACd,IAAI,IAAI,CAACvG,UAAU,EAAEwG,OAAO,EAAE;YAC5B,IAAI,CAACxG,UAAU,CAAC,QAAQ,CAAC,EAAEyG,MAAM,EAAE;UACrC;QACF,CAAC,EAAE,GAAG,CAAC;MACT;IACF;IAEAC,WAAWA,CAAA;MACT,IAAI,IAAI,CAACrF,qBAAqB,EAAE;QAC9B,IAAI,CAACA,qBAAqB,CAACsF,WAAW,EAAE;MAC1C;IACF;IAEQX,sBAAsBA,CAAA;MAC5B,IAAI,CAACtF,YAAY,GAAG,CAClB;QACEkG,IAAI,EAAE,IAAI;QACVC,IAAI,EAAE;OACP,CACF;IACH;IAEAjI,MAAMA,CAAA;MACJ;MACA,IAAI,CAACkI,oBAAoB,CAAC,IAAI,CAACtG,QAAQ,CAAC;MAExC;MACA,IAAI,CAAC,IAAI,CAACA,QAAQ,CAACuG,KAAK,EAAE;QACxB,IAAI,CAACC,wBAAwB,EAAE;QAC/B;MACF;MAEA;MACA,IAAI,CAAC,IAAI,CAAC9G,UAAU,EAAE;QACpB;QACA;QACA,MAAM+G,aAAa,GAAG;UACpB/D,IAAI,EAAE,IAAI,CAAC1C,QAAQ,CAACgF,GAAG,CAAC,MAAM,CAAC,EAAE0B,KAAK;UACtC7D,WAAW,EAAE,IAAI,CAAC7C,QAAQ,CAACgF,GAAG,CAAC,aAAa,CAAC,EAAE0B,KAAK;UACpDC,WAAW,EAAE;YACXC,KAAK,EAAE,EAAE;YAAE;YACXC,cAAc,EAAE,IAAI,CAAC7G,QAAQ,CAACgF,GAAG,CAAC,iBAAiB,CAAC,EAAE0B,KAAK;YAC3DI,eAAe,EAAE,IAAI,CAAC9G,QAAQ,CAACgF,GAAG,CAAC,eAAe,CAAC,EAAE0B;WACtD;UACDK,SAAS,EAAE,IAAI,CAAC7D,gBAAgB,EAAE;UAClC8D,UAAU,EAAE,IAAI,CAAC9D,gBAAgB;SAClC;QAEDmC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEmB,aAAa,CAAC;QAC7D,IAAI,CAACpH,YAAY,CAAC4H,cAAc,CAACR,aAAa,CAAC,CAACd,SAAS,CAAC;UACxDuB,IAAI,EAAGC,QAAQ,IAAI;YACjB9B,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE6B,QAAQ,CAAC;YAChD,IAAI,CAACvH,aAAa,GAAG,IAAI;YACzB,IAAI,CAACE,iBAAiB,GAAG,IAAI;YAC7B,IAAI,CAAC0F,sBAAsB,EAAE;YAC7B,IAAI,CAAC4B,mBAAmB,EAAE;YAE1B;YACA,MAAMC,cAAc,GAAGF,QAAQ,EAAEG,OAAO,IAAI,4BAA4B;YACxE,IAAI,CAACC,kBAAkB,CAAC,cAAc,EAAEF,cAAc,CAAC;UACzD,CAAC;UACDG,KAAK,EAAGA,KAAK,IAAI;YACfnC,OAAO,CAACmC,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;YAE3C;YACA,MAAMC,YAAY,GAAGD,KAAK,EAAEA,KAAK,EAAEF,OAAO,IAAIE,KAAK,EAAEF,OAAO,IAAI,wCAAwC;YACxG,IAAI,CAACI,gBAAgB,CAAC,iBAAiB,EAAED,YAAY,CAAC;UACxD;SACD,CAAC;MACJ;IACF;IAEA9I,QAAQA,CAAA;MACN;MACA,IAAI,CAAC2H,oBAAoB,CAAC,IAAI,CAACtG,QAAQ,CAAC;MAExC;MACA,IAAI,CAAC,IAAI,CAACA,QAAQ,CAACuG,KAAK,EAAE;QACxB,IAAI,CAACC,wBAAwB,EAAE;QAC/B;MACF;MAEA;MACA,IAAI,IAAI,CAAC9G,UAAU,IAAI,IAAI,CAACD,MAAM,EAAE;QAClC;QACA,MAAMkI,aAAa,GAAG;UACpBC,EAAE,EAAE3C,QAAQ,CAAC,IAAI,CAACxF,MAAM,CAACoI,QAAQ,EAAE,CAAC;UACpCnF,IAAI,EAAE,IAAI,CAAC1C,QAAQ,CAACgF,GAAG,CAAC,MAAM,CAAC,EAAE0B,KAAK;UACtC7D,WAAW,EAAE,IAAI,CAAC7C,QAAQ,CAACgF,GAAG,CAAC,aAAa,CAAC,EAAE0B,KAAK;UACpDK,SAAS,EAAE,IAAI,CAAC9E,gBAAgB,EAAE8E,SAAS,IAAI,IAAI,CAAC7D,gBAAgB,EAAE;UACtE8D,UAAU,EAAE,IAAI,CAAC9D,gBAAgB,EAAE;UACnC;UACA4E,SAAS,EAAE,IAAI,CAAC7F,gBAAgB,EAAE6F,SAAS,IAAI,IAAI,CAAC7F,gBAAgB,EAAE8F,eAAe,IAAI,IAAI,CAAC9F,gBAAgB,EAAE+F,UAAU,IAAI,IAAI,CAAC3E,mBAAmB,EAAE;UACxJ4E,UAAU,EAAE,IAAI,CAAC5E,mBAAmB,EAAE;UACtC6E,SAAS,EAAE,IAAI;UAAE;UAChBvB,WAAW,EAAE;YACZC,KAAK,EAAE,EAAE;YAAE;YACXC,cAAc,EAAE,IAAI,CAAC7G,QAAQ,CAACgF,GAAG,CAAC,iBAAiB,CAAC,EAAE0B,KAAK;YAC3DI,eAAe,EAAE,IAAI,CAAC9G,QAAQ,CAACgF,GAAG,CAAC,eAAe,CAAC,EAAE0B;;SAExD;QAEDrB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEqC,aAAa,CAAC;QAExD,IAAI,CAACtI,YAAY,CAAC8I,cAAc,CAACR,aAAa,CAAC,CAAChC,SAAS,CAAC;UACxDuB,IAAI,EAAGC,QAAQ,IAAI;YACjB9B,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE6B,QAAQ,CAAC;YAC9C,IAAI,CAACvH,aAAa,GAAG,IAAI;YACzB,IAAI,CAACE,iBAAiB,GAAG,IAAI;YAC7B,IAAI,CAAC0F,sBAAsB,EAAE;YAC7B,IAAI,CAAC4B,mBAAmB,EAAE;YAE1B;YACA,MAAMC,cAAc,GAAGF,QAAQ,EAAEG,OAAO,IAAI,4BAA4B;YACxE,IAAI,CAACC,kBAAkB,CAAC,cAAc,EAAEF,cAAc,CAAC;UACzD,CAAC;UACDG,KAAK,EAAGA,KAAK,IAAI;YACfnC,OAAO,CAACmC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;YAC5CnC,OAAO,CAACmC,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAACA,KAAK,CAAC;YAC5CnC,OAAO,CAACmC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAACY,MAAM,CAAC;YAEtC;YACA,MAAMX,YAAY,GAAGD,KAAK,EAAEA,KAAK,EAAEF,OAAO,IAAIE,KAAK,EAAEF,OAAO,IAAI,wCAAwC;YACxG,IAAI,CAACI,gBAAgB,CAAC,eAAe,EAAED,YAAY,CAAC;UACtD;SACD,CAAC;MACJ;IACF;IAIQY,iBAAiBA,CAAA;MACvB,MAAMpH,YAAY,GAAG,IAAI,CAACjB,QAAQ,CAACgF,GAAG,CAAC,iBAAiB,CAAC,EAAE0B,KAAK;MAChE,MAAM5D,aAAa,GAAG,IAAI,CAAC9C,QAAQ,CAACgF,GAAG,CAAC,eAAe,CAAC,EAAE0B,KAAK;MAC/D,MAAM4B,QAAQ,GAAG,IAAI,CAACtI,QAAQ,CAACgF,GAAG,CAAC,MAAM,CAAC,EAAE0B,KAAK;MACjD,IAAI,CAACzF,YAAY,IAAI,CAAC6B,aAAa,EAAE;QACnC,IAAI,CAACyF,cAAc,CACjB,IAAI,EACJ,wDAAwD,CACzD;QACD;MACF;MACA,MAAMC,OAAO,GAAG,qBAAqB;MACrC,MAAMC,iBAAiB,GACrB,oDAAoD;MAEtD,IAAI,CAACtI,gBAAgB,GAAG,IAAI;MAC5B,IAAI,CAACoI,cAAc,CAAC,IAAI,EAAE,8CAA8C,CAAC;MAEzE,IAAI,CAACjJ,qBAAqB,CACvBoJ,QAAQ,CAACzH,YAAY,EAAEuH,OAAO,EAAE,KAAK,EAAEC,iBAAiB,CAAC,CACzD9C,SAAS,CAAC;QACTuB,IAAI,EAAGC,QAAa,IAAI;UACtB9B,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE6B,QAAQ,CAAC;UACvD,IAAI;YACF,MAAMwB,cAAc,GAAGxB,QAAQ,EAAEA,QAAQ,EAAEyB,OAAO,GAAG,CAAC,CAAC,EAAEvC,IAAI;YAC7D,IAAI,CAACsC,cAAc,EAAE;cACnB,MAAM,IAAIE,KAAK,CAAC,iCAAiC,CAAC;YACpD;YACA,MAAMC,aAAa,GAAGC,IAAI,CAACC,KAAK,CAACL,cAAc,CAAC;YAChD,MAAMlI,UAAU,GAAyBwI,MAAM,CAACC,IAAI,CAClDJ,aAAa,CACd,CAACK,GAAG,CAAEC,GAAG,KAAM;cACd1G,IAAI,EAAE0G,GAAG;cACTC,IAAI,EAAEP,aAAa,CAACM,GAAG;aACxB,CAAC,CAAC;YAEH,IAAI3I,UAAU,CAAC6I,MAAM,KAAK,CAAC,EAAE;cAC3B,IAAI,CAACnJ,gBAAgB,GAAG,KAAK;cAC7B,IAAI,CAACoI,cAAc,CACjB,IAAI,EACJ,qFAAqF,CACtF;cACD;YACF;YAEA,IAAI,CAAC/H,cAAc,GAAG;cACpBC,UAAU,EAAEA,UAAU;cACtBC,qBAAqB,EAAE,CAAC;cACxBC,eAAe,EAAE,EAAE;cACnBC,YAAY,EAAE;aACf;YAED,IAAI,CAACT,gBAAgB,GAAG,KAAK;YAC7B,IAAI,CAACoJ,sBAAsB,EAAE;UAC/B,CAAC,CAAC,OAAO/B,KAAK,EAAE;YACdnC,OAAO,CAACmC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;YACjD,IAAI,CAACrH,gBAAgB,GAAG,KAAK;YAC7B,IAAI,CAACoI,cAAc,CACjB,IAAI,EACJ,iFAAiF,CAClF;UACH;QACF,CAAC;QACDf,KAAK,EAAGA,KAAK,IAAI;UACfnC,OAAO,CAACmC,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;UAClD,IAAI,CAACrH,gBAAgB,GAAG,KAAK;UAC7B,IAAI,CAACoI,cAAc,CACjB,IAAI,EACJ,wFAAwF,CACzF;QACH;OACD,CAAC;IACN;IAEQgB,sBAAsBA,CAAA;MAC5B,IACE,IAAI,CAAC/I,cAAc,CAACE,qBAAqB,GACzC,IAAI,CAACF,cAAc,CAACC,UAAU,CAAC6I,MAAM,EACrC;QACA,MAAME,YAAY,GAChB,IAAI,CAAChJ,cAAc,CAACC,UAAU,CAC5B,IAAI,CAACD,cAAc,CAACE,qBAAqB,CAC1C;QACH,MAAM4G,OAAO,GAAG,qCAAqCkC,YAAY,CAAC9G,IAAI,YAAY8G,YAAY,CAACH,IAAI,IAAI;QACvG,IAAI,CAACd,cAAc,CAAC,IAAI,EAAEjB,OAAO,CAAC;MACpC;IACF;IAEQiB,cAAcA,CAACnC,IAAmB,EAAEC,IAAY;MACtD,IAAI,CAACnG,YAAY,GAAG,CAAC,GAAG,IAAI,CAACA,YAAY,EAAE;QAAEkG,IAAI;QAAEC;MAAI,CAAE,CAAC;IAC5D;IAEAoD,iBAAiBA,CAACnC,OAAe;MAC/B,IAAI,CAAC,IAAI,CAAC1H,aAAa,EAAE;MACzB,IAAI,CAAC2I,cAAc,CAAC,MAAM,EAAEjB,OAAO,CAAC;MACpC,IAAI,IAAI,CAACxG,6BAA6B,EAAE;QACtC,IAAIwG,OAAO,CAACoC,WAAW,EAAE,CAACC,IAAI,EAAE,KAAK,KAAK,EAAE;UAC1C,IAAI,CAAC7I,6BAA6B,GAAG,KAAK;UAC1C,IAAI,CAACsG,mBAAmB,EAAE;UAC1B,IAAI,CAACmB,cAAc,CACjB,IAAI,EACJ,gDAAgD,CACjD;UACD,IAAI,CAACF,iBAAiB,EAAE;QAC1B,CAAC,MAAM;UACL,IAAI,CAACvH,6BAA6B,GAAG,KAAK;UAC1C,IAAI,CAACyH,cAAc,CACjB,IAAI,EACJ,4DAA4D,CAC7D;QACH;MACF,CAAC,MAAM,IAAI,IAAI,CAAC/H,cAAc,CAACI,YAAY,EAAE;QAC3C,IAAI,CAACgJ,oBAAoB,CAACtC,OAAO,CAAC;MACpC,CAAC,MACI;QACH,IAAI,CAACiB,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC;MACpC;IACF;IAEQqB,oBAAoBA,CAACC,KAAa;MACxC,MAAML,YAAY,GAChB,IAAI,CAAChJ,cAAc,CAACC,UAAU,CAAC,IAAI,CAACD,cAAc,CAACE,qBAAqB,CAAC;MAC3E,IAAIoJ,cAAc,GAAQD,KAAK;MAE/B,IAAI;QACF,QAAQL,YAAY,CAACH,IAAI;UACvB,KAAK,QAAQ;YACXS,cAAc,GAAGC,UAAU,CAACF,KAAK,CAAC;YAClC,IAAIG,KAAK,CAACF,cAAc,CAAC,EAAE;cACzB,IAAI,CAACvB,cAAc,CACjB,IAAI,EACJ,2DAA2DiB,YAAY,CAAC9G,IAAI,IAAI,CACjF;cACD;YACF;YACA;UACF,KAAK,SAAS;YACZ,MAAMuH,UAAU,GAAGJ,KAAK,CAACH,WAAW,EAAE;YACtC,IACEO,UAAU,KAAK,MAAM,IACrBA,UAAU,KAAK,GAAG,IAClBA,UAAU,KAAK,KAAK,EACpB;cACAH,cAAc,GAAG,IAAI;YACvB,CAAC,MAAM,IACLG,UAAU,KAAK,OAAO,IACtBA,UAAU,KAAK,GAAG,IAClBA,UAAU,KAAK,IAAI,EACnB;cACAH,cAAc,GAAG,KAAK;YACxB,CAAC,MAAM;cACL,IAAI,CAACvB,cAAc,CACjB,IAAI,EACJ,wDAAwDiB,YAAY,CAAC9G,IAAI,IAAI,CAC9E;cACD;YACF;YACA;UACF,KAAK,QAAQ;YACX,IAAI;cACFoH,cAAc,GAAGf,IAAI,CAACC,KAAK,CAACa,KAAK,CAAC;YACpC,CAAC,CAAC,MAAM;cACN,IAAI,CAACtB,cAAc,CACjB,IAAI,EACJ,8DAA8DiB,YAAY,CAAC9G,IAAI,IAAI,CACpF;cACD;YACF;YACA;UACF,KAAK,OAAO;YACV,IAAI;cACFoH,cAAc,GAAGf,IAAI,CAACC,KAAK,CAACa,KAAK,CAAC;cAClC,IAAI,CAACK,KAAK,CAACC,OAAO,CAACL,cAAc,CAAC,EAAE;gBAClC,MAAM,IAAIjB,KAAK,CAAC,cAAc,CAAC;cACjC;YACF,CAAC,CAAC,MAAM;cACN,IAAI,CAACN,cAAc,CACjB,IAAI,EACJ,8DAA8DiB,YAAY,CAAC9G,IAAI,IAAI,CACpF;cACD;YACF;YACA;QACJ;QACA,IAAI,CAAClC,cAAc,CAACG,eAAe,CAAC6I,YAAY,CAAC9G,IAAI,CAAC,GAAGoH,cAAc;QACvE,IAAI,CAACtJ,cAAc,CAACE,qBAAqB,EAAE;QAC3C,IACE,IAAI,CAACF,cAAc,CAACE,qBAAqB,IACzC,IAAI,CAACF,cAAc,CAACC,UAAU,CAAC6I,MAAM,EACrC;UACA,IAAI,CAACc,yBAAyB,EAAE;QAClC,CAAC,MAAM;UACL,IAAI,CAACb,sBAAsB,EAAE;QAC/B;MACF,CAAC,CAAC,OAAO/B,KAAK,EAAE;QACdnC,OAAO,CAACmC,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzD,IAAI,CAACe,cAAc,CACjB,IAAI,EACJ,+BAA+BiB,YAAY,CAAC9G,IAAI,sBAAsB,CACvE;MACH;IACF;IAEQ0H,yBAAyBA,CAAA;MAC/B,IAAI,CAAC5J,cAAc,CAACI,YAAY,GAAG,KAAK;MACxC,IAAI,CAACT,gBAAgB,GAAG,IAAI;MAE5B,IAAI,CAACoI,cAAc,CACjB,IAAI,EACJ,kDAAkD,CACnD;MAED,MAAM8B,OAAO,GAAG;QACdC,gBAAgB,EAAE,IAAI,CAACtK,QAAQ,CAACgF,GAAG,CAAC,iBAAiB,CAAC,EAAE0B,KAAK;QAC7D6D,UAAU,EAAE,IAAI,CAACvK,QAAQ,CAACgF,GAAG,CAAC,eAAe,CAAC,EAAE0B,KAAK;QACrD8D,MAAM,EAAE,IAAI,CAAChK,cAAc,CAACG;OAC7B;MAED,IAAI,CAACtB,YAAY,CAACoL,QAAQ,CAACJ,OAAO,CAAC,CAAC1E,SAAS,CAAC;QAC5CuB,IAAI,EAAGC,QAAa,IAAI;UACtB9B,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE6B,QAAQ,CAAC;UACjD,IAAI,CAAChH,gBAAgB,GAAG,KAAK;UAE7B,IAAIgH,QAAQ,CAACiB,MAAM,KAAK,SAAS,EAAE;YACjC,IAAI,CAACG,cAAc,CACjB,IAAI,EACJ,uCAAuCpB,QAAQ,CAACuD,MAAM,EAAE,CACzD;UACH,CAAC,MAAM;YACL,IAAI,CAACnC,cAAc,CACjB,IAAI,EACJ,0BAA0BpB,QAAQ,CAACwD,MAAM,IAAI,eAAe,EAAE,CAC/D;UACH;UACA,IAAI,CAAC7J,6BAA6B,GAAG,IAAI;UACzCiF,UAAU,CAAC,MAAK;YACd,IAAI,CAACwC,cAAc,CACjB,IAAI,EACJ,uHAAuH,CACxH;UACH,CAAC,EAAE,IAAI,CAAC;QACV,CAAC;QACDf,KAAK,EAAGA,KAAK,IAAI;UACfnC,OAAO,CAACmC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;UAC7C,IAAI,CAACrH,gBAAgB,GAAG,KAAK;UAC7B,IAAI,CAACoI,cAAc,CACjB,IAAI,EACJ,0BAA0Bf,KAAK,EAAEA,KAAK,EAAEF,OAAO,IAAI,wBAAwB,EAAE,CAC9E;QACH;OACD,CAAC;IACJ;IAEAsD,MAAMA,CAAA;MACJ;MACA,MAAMC,UAAU,GAAG,IAAI,CAAC1L,KAAK,CAAC2F,QAAQ,CAACgG,aAAa,CAAC9F,GAAG,CAAC,YAAY,CAAC;MACtE,MAAM+F,UAAU,GAAGF,UAAU,GAAG5F,QAAQ,CAAC4F,UAAU,CAAC,GAAG,CAAC;MAExDxF,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;MAC/E,IAAI,CAACpG,MAAM,CAAC8L,QAAQ,CAAC,CAAC,kBAAkB,CAAC,EAAE;QACzCC,WAAW,EAAE;UAAEC,IAAI,EAAEH;QAAU;OAChC,CAAC;IACJ;IAEQ3D,mBAAmBA,CAAA;MACzB,IAAI,CAAC5G,cAAc,GAAG;QACpBC,UAAU,EAAE,EAAE;QACdC,qBAAqB,EAAE,CAAC;QACxBC,eAAe,EAAE,EAAE;QACnBC,YAAY,EAAE;OACf;IACH;IAEAuK,UAAUA,CAACzI,IAAY;MACrB,OAAO,IAAI,CAAC1C,QAAQ,CAACgF,GAAG,CAACtC,IAAI,CAAgB;IAC/C;IAEA;IACAE,qBAAqBA,CAACwI,OAAwB;MAC5C,MAAM1E,KAAK,GAAG0E,OAAO,CAAC1E,KAAK;MAC3B,IAAIA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACiD,IAAI,EAAE,CAACL,MAAM,KAAK,CAAC,EAAE;QACnE,OAAO;UAAE+B,YAAY,EAAE;QAAI,CAAE;MAC/B;MACA,OAAO,IAAI;IACb;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA/E,oBAAoBA,CAACgF,SAAoB;MACvCrC,MAAM,CAACC,IAAI,CAACoC,SAAS,CAAC7F,QAAQ,CAAC,CAAC8F,OAAO,CAACnC,GAAG,IAAG;QAC5C,MAAMgC,OAAO,GAAGE,SAAS,CAACtG,GAAG,CAACoE,GAAG,CAAC;QAClCgC,OAAO,EAAEI,aAAa,EAAE;QACxB,IAAIJ,OAAO,YAAY9O,SAAS,EAAE;UAChC,IAAI,CAACgK,oBAAoB,CAAC8E,OAAO,CAAC;QACpC;MACF,CAAC,CAAC;IACJ;IAEA;IACA5E,wBAAwBA,CAAA;MACtB,MAAMiF,MAAM,GAAa,EAAE;MAE3B;MACAxC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAClJ,QAAQ,CAACyF,QAAQ,CAAC,CAAC8F,OAAO,CAACG,SAAS,IAAG;QACtD,MAAMlE,KAAK,GAAG,IAAI,CAACmE,aAAa,CAACD,SAAS,CAAC;QAC3C,IAAIlE,KAAK,EAAE;UACT,MAAMoE,UAAU,GAAG,IAAI,CAACC,aAAa,CAACH,SAAS,CAAC;UAChDD,MAAM,CAACK,IAAI,CAAC,GAAGF,UAAU,KAAKpE,KAAK,EAAE,CAAC;QACxC;MACF,CAAC,CAAC;MAEF,IAAIiE,MAAM,CAACnC,MAAM,GAAG,CAAC,EAAE;QACrB,IAAI,CAACjH,UAAU,GAAG,kBAAkB;QACpC,IAAI,CAACC,YAAY,GAAGmJ,MAAM,CAACM,IAAI,CAAC,MAAM,CAAC;QACvC,IAAI,CAAC3J,cAAc,GAAG,IAAI;MAC5B;IACF;IAEA;IACAyJ,aAAaA,CAACH,SAAiB;MAC7B,QAAQA,SAAS;QACf,KAAK,MAAM;UAAE,OAAO,IAAI,CAACxK,MAAM,CAACoH,QAAQ;QACxC,KAAK,eAAe;UAAE,OAAO,IAAI,CAACpH,MAAM,CAAC4B,aAAa;QACtD,KAAK,aAAa;UAAE,OAAO,IAAI,CAAC5B,MAAM,CAAC2B,WAAW;QAClD,KAAK,iBAAiB;UAAE,OAAO,IAAI,CAAC3B,MAAM,CAAC8K,mBAAmB;QAC9D;UAAS,OAAON,SAAS;MAC3B;IACF;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEAO,gBAAgBA,CAACvJ,IAAY;MAC3B,OAAO,IAAI,CAACzC,cAAc,CAAC+E,GAAG,CAACtC,IAAI,CAAgB;IACrD;IACAiJ,aAAaA,CAACD,SAAiB;MAC7B,MAAMQ,KAAK,GAAG,IAAI,CAAClM,QAAQ,CAACgF,GAAG,CAAC0G,SAAS,CAAC;MAC1C,IAAIQ,KAAK,IAAIA,KAAK,CAACC,OAAO,KAAKD,KAAK,CAACE,OAAO,IAAIF,KAAK,CAACG,KAAK,CAAC,EAAE;QAC5D,IAAIH,KAAK,CAACT,MAAM,GAAG,UAAU,CAAC,EAAE;UAC9B,IAAIC,SAAS,KAAK,MAAM,EAAE,OAAO,IAAI,CAACxK,MAAM,CAACoL,qBAAqB;UAClE,IAAIZ,SAAS,KAAK,eAAe,EAC/B,OAAO,IAAI,CAACxK,MAAM,CAACqL,0BAA0B;UAC/C,IAAIb,SAAS,KAAK,aAAa,EAC7B,OAAO,IAAI,CAACxK,MAAM,CAACsL,wBAAwB;UAC7C,IAAId,SAAS,KAAK,iBAAiB,EACjC,OAAO,IAAI,CAACxK,MAAM,CAACuL,uBAAuB;UAC5C,OAAO,IAAI,CAACvL,MAAM,CAACwL,aAAa,IAAI,wBAAwB;QAC9D;QACA,IAAIR,KAAK,CAACT,MAAM,GAAG,cAAc,CAAC,EAAE;UAClC,OAAO,6DAA6D;QACtE;QACA,IAAIS,KAAK,CAACT,MAAM,GAAG,SAAS,CAAC,EAAE;UAC7B,IAAIC,SAAS,KAAK,eAAe,EAAE;YACjC,OAAO,yGAAyG;UAClH;QACF;QACA,IAAIQ,KAAK,CAACT,MAAM,GAAG,cAAc,CAAC,EAAE;UAClC,OAAOS,KAAK,CAACT,MAAM,CAAC,cAAc,CAAC,CAACkB,YAAY;QAClD;QACA;MACF;MACA,OAAO,EAAE;IACX;IAEAC,YAAY,GAAGA,CAAA,KAAW;MACxB,IAAI,CAACC,YAAY,EAAE;IACrB,CAAC;IAEMA,YAAYA,CAAA;MACjB,IAAI,CAACxL,oBAAoB,GAAG,KAAK;MACjC,IAAI,CAACD,gBAAgB,GAAG,EAAE;MAC1B,MAAMoH,OAAO,GAAG,gBAAgB;MAChC,MAAMC,iBAAiB,GACrB,oEAAoE;MACtE,MAAMxH,YAAY,GAAG,IAAI,CAACjB,QAAQ,CAACyF,QAAQ,CAAC,iBAAiB,CAAC,CAACiB,KAAK;MACpE,IAAI,CAACpH,qBAAqB,CACvBoJ,QAAQ,CAACzH,YAAY,EAAEuH,OAAO,EAAE,KAAK,EAAEC,iBAAiB,CAAC,CACzD9C,SAAS,CAAC;QACTuB,IAAI,EAAG4F,GAAQ,IAAI;UACjB,IAAIC,YAAY,GAAGD,GAAG,EAAE3F,QAAQ,EAAEyB,OAAO,GAAG,CAAC,CAAC,EAAEvC,IAAI;UACpD,IAAI,CAAC0G,YAAY,EAAE;YACjB,IAAI,CAAC3L,gBAAgB,GAAG,uCAAuC;YAC/D,IAAI,CAACC,oBAAoB,GAAG,IAAI;YAChC,IAAI,IAAI,CAAC7B,UAAU,EAAE,IAAI,CAACA,UAAU,CAACwN,oBAAoB,EAAE;YAC3D;UACF;UACA;UACAD,YAAY,GAAGA,YAAY,CACxBE,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CACzBA,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;UACzB,IAAI;YACF,MAAMC,MAAM,GAAGnE,IAAI,CAACC,KAAK,CAAC+D,YAAY,CAAC;YACvC;YACA,IAAII,SAAS,GAAG,EAAE;YAClB,IACED,MAAM,CAACE,MAAM,IACblD,KAAK,CAACC,OAAO,CAAC+C,MAAM,CAACE,MAAM,CAAC,IAC5BF,MAAM,CAACE,MAAM,CAAC9D,MAAM,GAAG,CAAC,EACxB;cACA4D,MAAM,CAACE,MAAM,CAAC7B,OAAO,CAAC,CAAC8B,KAAU,EAAEC,GAAW,KAAI;gBAChDH,SAAS,IAAI,SAASG,GAAG,GAAG,CAAC,KAAK;gBAClCH,SAAS,IAAI,WAAWE,KAAK,CAAChE,IAAI,IAAI;gBACtC8D,SAAS,IAAI,kBAAkBE,KAAK,CAACxK,WAAW,IAAI;gBACpDsK,SAAS,IAAI,eAAeE,KAAK,CAACE,QAAQ,IAAI;gBAC9CJ,SAAS,IAAI,kBAAkBE,KAAK,CAACG,WAAW,IAAI;gBACpDL,SAAS,IAAI,iBAAiBE,KAAK,CAACI,UAAU,MAAM;cACtD,CAAC,CAAC;YACJ,CAAC,MAAM;cACLN,SAAS,GAAG,kBAAkB;YAChC;YACA,IAAI,CAAC/L,gBAAgB,GAAG+L,SAAS;UACnC,CAAC,CAAC,OAAOO,CAAC,EAAE;YACV,IAAI,CAACtM,gBAAgB,GAAG2L,YAAY;UACtC;UACA,IAAI,CAAC1L,oBAAoB,GAAG,IAAI;UAChC,IAAI,IAAI,CAAC7B,UAAU,EAAE,IAAI,CAACA,UAAU,CAACwN,oBAAoB,EAAE;QAC7D,CAAC;QACDxF,KAAK,EAAGkG,CAAC,IAAI;UACX,IAAI,CAACtM,gBAAgB,GACnB,SAAS,IAAIsM,CAAC,EAAElG,KAAK,EAAEF,OAAO,IAAI,eAAe,CAAC;UACpD,IAAI,CAACjG,oBAAoB,GAAG,IAAI;UAChC,IAAI,IAAI,CAAC7B,UAAU,EAAE,IAAI,CAACA,UAAU,CAACwN,oBAAoB,EAAE;QAC7D;OACD,CAAC;IACN;IAEAzH,YAAYA,CAAC9F,MAAc;MACzB,IAAI,CAACJ,YAAY,CAACsO,kBAAkB,CAAClO,MAAM,CAAC,CAACkG,SAAS,CACnDwB,QAAQ,IAAI;QACX9B,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE6B,QAAQ,CAAC,CAAC,CAAC;QACxC,MAAMyG,IAAI,GAAGzG,QAAQ,CAAC0G,KAAK,IAAI1G,QAAQ,CAAC0G,KAAK,CAAC,CAAC,CAAC;QAChDxI,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEsI,IAAI,CAAC,CAAC,CAAC;QACzCvI,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEsI,IAAI,EAAEnO,MAAM,CAAC;QACrC,IAAImO,IAAI,EAAE;UACR,IAAI,CAAC3L,gBAAgB,GAAG2L,IAAI;UAC5BvI,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEyD,IAAI,CAAC+E,SAAS,CAACF,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;UAC3E,IAAI,CAAC5N,QAAQ,CAAC+N,UAAU,CAAC;YACvBrL,IAAI,EAAE,IAAI,CAAC/C,WAAW,GAAG,EAAE,GAAGiO,IAAI,CAACtF,QAAQ,IAAI,EAAE;YACjDzF,WAAW,EAAE+K,IAAI,CAACI,eAAe,IAAI,EAAE;YACvClL,aAAa,EAAE8K,IAAI,CAAC9K,aAAa,IAAI,EAAE;YACvCE,eAAe,EAAE4K,IAAI,CAAC3M,YAAY,IAAI;WACvC,CAAC;UACF;UACA,IAAI,IAAI,CAACzB,UAAU,EAAE;YACnB,IAAI,CAACA,UAAU,CAACsG,QAAQ,CAAC8H,IAAI,CAAC3M,YAAY,IAAI,EAAE,CAAC;YACjD;YACA8E,UAAU,CAAC,MAAK;cACd,IAAI,IAAI,CAACvG,UAAU,EAAEwG,OAAO,EAAE;gBAC5B,IAAI,CAACxG,UAAU,CAAC,QAAQ,CAAC,EAAEyG,MAAM,EAAE;cACrC;YACF,CAAC,EAAE,GAAG,CAAC;UACT,CAAC,MAAM;YACL,IAAI,CAAC/D,oBAAoB,GAAG0L,IAAI,CAAC3M,YAAY,IAAI,EAAE;UACrD;UAEA;UACA,IAAI,IAAI,CAACrB,aAAa,EAAE;YACtByF,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;YACpE;YACAS,UAAU,CAAC,MAAK;cACd,IAAI,CAACsC,iBAAiB,EAAE;YAC1B,CAAC,EAAE,GAAG,CAAC;UACT;QACF;MACF,CAAC,EACAb,KAAK,IAAI;QACRnC,OAAO,CAACmC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAClD,CAAC,CACF;IACH;IAEA;IACAyG,aAAa,GAAqB,CAChC;MAAEvH,KAAK,EAAE,SAAS;MAAEhE,IAAI,EAAE;IAAe,CAAE,EAC3C;MAAEgE,KAAK,EAAE,gBAAgB;MAAEhE,IAAI,EAAE;IAAuB,CAAE,EAC1D;MAAEgE,KAAK,EAAE,kBAAkB;MAAEhE,IAAI,EAAE;IAAkB,CAAE,EACvD;MAAEgE,KAAK,EAAE,gBAAgB;MAAEhE,IAAI,EAAE;IAAgB,CAAE,EACnD;MAAEgE,KAAK,EAAE,oBAAoB;MAAEhE,IAAI,EAAE;IAAoB,CAAE,CAC5D;IAEDwL,eAAeA,CAACC,MAAsB;MACpC9I,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE6I,MAAM,CAAC;MAChD;IACF;IAEAC,cAAcA,CAACd,GAAW;MACxB,IAAI,CAAC,IAAI,CAAC9N,UAAU,EAAE;MACtB,IAAI8N,GAAG,KAAK,CAAC,EAAE,IAAI,CAAC9N,UAAU,CAAC6O,SAAS,EAAE;MAC1C,IAAIf,GAAG,KAAK,CAAC,EAAE,IAAI,CAAC9N,UAAU,CAAC8O,KAAK,EAAE;IACxC;IAEAtQ,cAAcA,CAAA;MACZ,IAAI,CAAC,IAAI,CAAC0B,UAAU,IAAI,CAAC,IAAI,CAACD,MAAM,EAAE;QACpC4F,OAAO,CAACmC,KAAK,CAAC,oDAAoD,CAAC;QACnE;MACF;MAEA,MAAMqD,UAAU,GAAG,IAAI,CAAC1L,KAAK,CAAC2F,QAAQ,CAACgG,aAAa,CAAC9F,GAAG,CAAC,YAAY,CAAC;MACtE,MAAM+F,UAAU,GAAGF,UAAU,GAAG5F,QAAQ,CAAC4F,UAAU,CAAC,GAAG,CAAC;MAExD;MACA,MAAM0D,eAAe,GAAG;QACtB3G,EAAE,EAAE3C,QAAQ,CAAC,IAAI,CAACxF,MAAM,CAACoI,QAAQ,EAAE,CAAC;QACpCnF,IAAI,EAAE,IAAI,CAAC1C,QAAQ,CAACgF,GAAG,CAAC,MAAM,CAAC,EAAE0B,KAAK;QACtC7D,WAAW,EAAE,IAAI,CAAC7C,QAAQ,CAACgF,GAAG,CAAC,aAAa,CAAC,EAAE0B,KAAK;QACpDK,SAAS,EAAE,IAAI,CAAC9E,gBAAgB,EAAE8E,SAAS,IAAI,IAAI,CAAC7D,gBAAgB,EAAE;QACtE8D,UAAU,EAAE,IAAI,CAAC9D,gBAAgB,EAAE;QAAE;QACrC4E,SAAS,EACP,IAAI,CAAC7F,gBAAgB,EAAE8F,eAAe,IAAI,IAAI,CAAC1E,mBAAmB,EAAE;QACtE,MAAM,EAAE,IAAI,CAACA,mBAAmB,EAAE;QAAE;QACpC6E,SAAS,EAAE,IAAI;QAAE;QACjBvB,WAAW,EAAE;UACXC,KAAK,EAAE,IAAI,CAAC3E,gBAAgB,EAAEuM,SAAS,IAAI,EAAE;UAC7CC,GAAG,EAAE,IAAI,CAACzO,QAAQ,CAACgF,GAAG,CAAC,iBAAiB,CAAC,EAAE0B,KAAK;UAChDgI,OAAO,EAAE,IAAI,CAAC1O,QAAQ,CAACgF,GAAG,CAAC,eAAe,CAAC,EAAE0B,KAAK,IAAI;;OAEzD;MAEDrB,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEiJ,eAAe,CAAC;MAClElJ,OAAO,CAACC,GAAG,CACT,6BAA6B,EAC7ByD,IAAI,CAAC+E,SAAS,CAACS,eAAe,EAAE,IAAI,EAAE,CAAC,CAAC,CACzC;MACDlJ,OAAO,CAACC,GAAG,CACT,mEAAmE,CACpE;MAED,IAAI,CAACjG,YAAY,CAAC8I,cAAc,CAACoG,eAAe,CAAC,CAAC5I,SAAS,CACxDwB,QAAQ,IAAI;QACX9B,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE6B,QAAQ,CAAC;QACrD;QACA,IAAI,CAACjI,MAAM,CAAC8L,QAAQ,CAAC,CAAC,kBAAkB,CAAC,EAAE;UACzCC,WAAW,EAAE;YAAEC,IAAI,EAAEH;UAAU;SAChC,CAAC;MACJ,CAAC,EACAvD,KAAK,IAAI;QACRnC,OAAO,CAACmC,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnDnC,OAAO,CAACmC,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAACA,KAAK,CAAC;QAC5CnC,OAAO,CAACmC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAACY,MAAM,CAAC;QACtC/C,OAAO,CAACmC,KAAK,CAAC,cAAc,EAAEA,KAAK,CAACmH,UAAU,CAAC;MACjD,CAAC,CACF;IACH;IAEA;IACApH,kBAAkBA,CAAChG,KAAa,EAAE+F,OAAe;MAC/CjC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE/D,KAAK,EAAE+F,OAAO,CAAC;MACzD,IAAI,CAACjF,UAAU,GAAGd,KAAK;MACvB,IAAI,CAACe,YAAY,GAAGgF,OAAO;MAC3B,IAAI,CAACnF,gBAAgB,GAAG,IAAI;MAC5B,IAAI,CAACI,uBAAuB,GAAG,IAAI;MACnC8C,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAACnD,gBAAgB,CAAC;IAChE;IAEAuF,gBAAgBA,CAACnG,KAAa,EAAE+F,OAAe;MAC7CjC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE/D,KAAK,EAAE+F,OAAO,CAAC;MACvD,IAAI,CAACjF,UAAU,GAAGd,KAAK;MACvB,IAAI,CAACe,YAAY,GAAGgF,OAAO;MAC3B,IAAI,CAAClF,cAAc,GAAG,IAAI;MAC1B,IAAI,CAACG,uBAAuB,GAAG,KAAK;MACpC8C,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAAClD,cAAc,CAAC;IAC5D;IAEAwM,iBAAiBA,CAAA;MACfvJ,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACvC,IAAI,CAACnD,gBAAgB,GAAG,KAAK;MAC7B,IAAI,CAACE,UAAU,GAAG,EAAE;MACpB,IAAI,CAACC,YAAY,GAAG,EAAE;IACxB;IAEAuM,eAAeA,CAAA;MACbxJ,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrC,IAAI,CAAClD,cAAc,GAAG,KAAK;MAC3B,IAAI,CAACC,UAAU,GAAG,EAAE;MACpB,IAAI,CAACC,YAAY,GAAG,EAAE;IACxB;IAEA;IACAwM,gBAAgBA,CAAA;MACdzJ,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAE,IAAI,CAAC/C,uBAAuB,CAAC;MAC9F,IAAI,CAACqM,iBAAiB,EAAE;MAExB,IAAI,IAAI,CAACrM,uBAAuB,EAAE;QAChC8C,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;QAC3C,IAAI,CAAC+C,iBAAiB,EAAE;MAC1B;IACF;IAEA;IACA0G,kBAAkBA,CAAA;MAChB,MAAMzG,QAAQ,GAAG,IAAI,CAACtI,QAAQ,CAACgF,GAAG,CAAC,MAAM,CAAC,EAAE0B,KAAK;MACjD,OAAO4B,QAAQ,IAAI,WAAW;IAChC;IAEA0G,eAAeA,CAACzO,MAAc;MAC5B,IAAI,CAACD,SAAS,GAAG,IAAI;MACrB,IAAI,CAAChB,qBAAqB,CACvBoJ,QAAQ,CACPnI,MAAM,EACNnD,SAAS,CAAC6R,YAAY,EACtB,KAAK,EACL9R,gCAAgC,CACjC,CACAwI,SAAS,CAAC;QACTuB,IAAI,EAAG4F,GAAG,IAAI;UACZ,IAAIoC,eAAe,GAAQ,EAAE;UAC7B,IAAI;YACFA,eAAe,GAAGnG,IAAI,CAACC,KAAK,CAAC8D,GAAG,EAAE3F,QAAQ,EAAEyB,OAAO,CAAC,CAAC,CAAC,EAAEvC,IAAI,CAAC;UAC/D,CAAC,CAAC,OAAOmB,KAAK,EAAE;YACd;YACA;YACA;UACF;UACA;UACA,MAAM;YACJ2H,SAAS;YACTC,UAAU;YACVC,gBAAgB;YAChBC;UAAqB,CACtB,GAAGJ,eAAe;UACnB,IAAI,CAACjP,cAAc,CAAC8N,UAAU,CAAC;YAC7BrL,IAAI,EAAEyM,SAAS;YACfrM,aAAa,EAAEsM,UAAU;YACzBvM,WAAW,EAAEwM,gBAAgB;YAC7BrM,eAAe,EAAEsM,qBAAqB,CAACrC,OAAO,CAAC,SAAS,EAAE,EAAE;WAC7D,CAAC;UAEF,IAAI,CAAC5M,cAAc,GAAG,IAAI;UAC1B,IAAI,CAACC,SAAS,GAAG,KAAK;QACxB,CAAC;QACDkH,KAAK,EAAEA,CAAA,KAAK;UACV,IAAI,CAAClH,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;IACN;IAEAiP,iBAAiBA,CAACC,IAAI,GAAG,IAAI;MAC3B,IAAI,CAACpP,eAAe,GAAGoP,IAAI;IAC7B;IAEAC,KAAKA,CAAA;MACH,MAAMC,SAAS,GAAG,IAAI,CAACzP,cAAc,CAAC0P,WAAW,EAAE;MACnD,IAAI,CAAC3P,QAAQ,CAAC+N,UAAU,CAAC2B,SAAS,CAAC;MACnC,IAAI,CAAClQ,UAAU,CAACsG,QAAQ,CAAC4J,SAAS,CAAC1M,eAAe,CAAC;MACnD,IAAI,CAAC/C,cAAc,CAAC2P,KAAK,EAAE;MAC3B,IAAI,CAACvP,cAAc,GAAG,KAAK;MAC3B,IAAI,CAACE,MAAM,CAACqP,KAAK,CAAC,EAAE,CAAC;MACrB,IAAI,CAACL,iBAAiB,CAAC,KAAK,CAAC;IAC/B;IAEAM,OAAOA,CAAA;MACL,IAAI,CAAC5P,cAAc,CAAC2P,KAAK,EAAE;MAC3B,IAAI,CAACvP,cAAc,GAAG,KAAK;IAC7B;IAEAyP,QAAQA,CAAA;MACN,IAAI,CAACvP,MAAM,CAACqP,KAAK,CAAC,EAAE,CAAC;MACrB,IAAI,CAACC,OAAO,EAAE;MACd,IAAI,CAACN,iBAAiB,CAAC,KAAK,CAAC;IAC/B;IAEAQ,OAAOA,CAAA;MACL,IAAI,CAACR,iBAAiB,CAAC,KAAK,CAAC;MAC7B,IAAI,CAACO,QAAQ,EAAE;IACjB;;uCA7/BW9Q,oBAAoB,EAAA1B,EAAA,CAAA0S,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA5S,EAAA,CAAA0S,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAA9S,EAAA,CAAA0S,iBAAA,CAAAG,EAAA,CAAAE,cAAA,GAAA/S,EAAA,CAAA0S,iBAAA,CAAAM,EAAA,CAAAC,oBAAA,GAAAjT,EAAA,CAAA0S,iBAAA,CAAAQ,EAAA,CAAAC,YAAA,GAAAnT,EAAA,CAAA0S,iBAAA,CAAAU,EAAA,CAAAC,oBAAA,GAAArT,EAAA,CAAA0S,iBAAA,CAAAY,EAAA,CAAAC,mBAAA;IAAA;;YAApB7R,oBAAoB;MAAA8R,SAAA;MAAAC,SAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBACpBtU,mBAAmB;;;;;;;;;;;;;;;;UCrFhCW,EAAA,CAAAE,cAAA,qBAAsG;UACpGF,EAAA,CAAAC,uBAAA,MAA2B;UAEvBD,EADF,CAAAE,cAAA,aAAkC,cACP;UAAAF,EAAA,CAAA6T,MAAA,sBAAe;UAAA7T,EAAA,CAAAW,YAAA,EAAO;UAC/CX,EAAA,CAAA8T,UAAA,IAAAC,4CAAA,0BAA6D;UAI/D/T,EAAA,CAAAW,YAAA,EAAM;;UAONX,EAHF,CAAAE,cAAA,aAAiD,oBASnB;UAAvBF,EAAA,CAAAG,UAAA,uBAAA6T,8DAAA;YAAAhU,EAAA,CAAAK,aAAA,CAAA4T,GAAA;YAAA,OAAAjU,EAAA,CAAAS,WAAA,CAAamT,GAAA,CAAAtG,MAAA,EAAQ;UAAA,EAAC;UAACtN,EAAA,CAAAW,YAAA,EAAa;UAazCX,EAXA,CAAA8T,UAAA,IAAAI,4CAAA,0BAAoD,IAAAC,4CAAA,0BAWD;UAYrDnU,EAAA,CAAAW,YAAA,EAAM;UAIJX,EADF,CAAAE,cAAA,cAAwC,cACR;UAC5BF,EAAA,CAAAoU,SAAA,uBAE8C;UAChDpU,EAAA,CAAAW,YAAA,EAAM;UAENX,EAAA,CAAAE,cAAA,cAA8B;UAC5BF,EAAA,CAAAoU,SAAA,uBAEgE;UAClEpU,EAAA,CAAAW,YAAA,EAAM;UAENX,EAAA,CAAAE,cAAA,cAA8B;UAC5BF,EAAA,CAAAoU,SAAA,wBAEoF;UAExFpU,EADE,CAAAW,YAAA,EAAM,EACF;UAMFX,EAHJ,CAAAE,cAAA,eAAqC,eAED,eACP;UAAAF,EAAA,CAAA6T,MAAA,mBAAW;UAAA7T,EAAA,CAAAW,YAAA,EAAO;UAC3CX,EAAA,CAAAoU,SAAA,UAAI;UAEFpU,EADF,CAAAE,cAAA,eAAmC,sBAQG;UAAlCF,EAAA,CAAAG,UAAA,uBAAAkU,+DAAA;YAAArU,EAAA,CAAAK,aAAA,CAAA4T,GAAA;YAAA,OAAAjU,EAAA,CAAAS,WAAA,CAAamT,GAAA,CAAA3B,iBAAA,EAAmB;UAAA,EAAC;UACnCjS,EAAA,CAAAW,YAAA,EAAa;UACbX,EAAA,CAAAE,cAAA,qBAM4B;UAAvBF,EAAA,CAAAG,UAAA,uBAAAmU,+DAAA;YAAAtU,EAAA,CAAAK,aAAA,CAAA4T,GAAA;YAAA,OAAAjU,EAAA,CAAAS,WAAA,CAAamT,GAAA,CAAAtG,MAAA,EAAQ;UAAA,EAAC;UAACtN,EAAA,CAAAW,YAAA,EAAa;UAazCX,EAXA,CAAA8T,UAAA,KAAAS,6CAAA,0BAAoD,KAAAC,6CAAA,0BAWD;UAavDxU,EADE,CAAAW,YAAA,EAAM,EACF;UAIJX,EADF,CAAAE,cAAA,eAA8B,8BAImD;UAA7EF,EADA,CAAAG,UAAA,mCAAAsU,gFAAA;YAAAzU,EAAA,CAAAK,aAAA,CAAA4T,GAAA;YAAA,OAAAjU,EAAA,CAAAS,WAAA,CAAyBmT,GAAA,CAAAtE,YAAA,EAAc;UAAA,EAAC,iCAAAoF,8EAAAC,MAAA;YAAA3U,EAAA,CAAAK,aAAA,CAAA4T,GAAA;YAAA,OAAAjU,EAAA,CAAAS,WAAA,CACjBmT,GAAA,CAAA9C,cAAA,CAAA6D,MAAA,CAAsB;UAAA,EAAC;UAClD3U,EADiF,CAAAW,YAAA,EAAkB,EAC7F;UAGNX,EAAA,CAAAE,cAAA,eAAuC;UACrCF,EAAA,CAAAoU,SAAA,2BASmB;UAEvBpU,EADE,CAAAW,YAAA,EAAM,EACF;UAKFX,EAFJ,CAAAE,cAAA,eAAwC,eACJ,0BAe/B;UADCF,EAFA,CAAAG,UAAA,0BAAAyU,sEAAAD,MAAA;YAAA3U,EAAA,CAAAK,aAAA,CAAA4T,GAAA;YAAA,OAAAjU,EAAA,CAAAS,WAAA,CAAgBmT,GAAA,CAAAhD,eAAA,CAAA+D,MAAA,CAAuB;UAAA,EAAC,yBAAAE,qEAAAF,MAAA;YAAA3U,EAAA,CAAAK,aAAA,CAAA4T,GAAA;YAAA,OAAAjU,EAAA,CAAAS,WAAA,CACzBmT,GAAA,CAAAzH,iBAAA,CAAAwI,MAAA,CAAyB;UAAA,EAAC,+BAAAG,2EAAA;YAAA9U,EAAA,CAAAK,aAAA,CAAA4T,GAAA;YAAA,OAAAjU,EAAA,CAAAS,WAAA,CACpBmT,GAAA,CAAAlT,cAAA,EAAgB;UAAA,EAAC;UAI9CV,EAHO,CAAAW,YAAA,EAAiB,EACd,EACF,EACM;UAGdX,EAAA,CAAAE,cAAA,qBAeC;UADCF,EADA,CAAAG,UAAA,qBAAA4U,4DAAA;YAAA/U,EAAA,CAAAK,aAAA,CAAA4T,GAAA;YAAA,OAAAjU,EAAA,CAAAS,WAAA,CAAWmT,GAAA,CAAApC,gBAAA,EAAkB;UAAA,EAAC,oBAAAwD,2DAAA;YAAAhV,EAAA,CAAAK,aAAA,CAAA4T,GAAA;YAAA,OAAAjU,EAAA,CAAAS,WAAA,CACpBmT,GAAA,CAAAtC,iBAAA,EAAmB;UAAA,EAAC;UAEhCtR,EAAA,CAAAW,YAAA,EAAY;UAGZX,EAAA,CAAAE,cAAA,qBAeC;UADCF,EADA,CAAAG,UAAA,qBAAA8U,4DAAA;YAAAjV,EAAA,CAAAK,aAAA,CAAA4T,GAAA;YAAA,OAAAjU,EAAA,CAAAS,WAAA,CAAWmT,GAAA,CAAArC,eAAA,EAAiB;UAAA,EAAC,oBAAA2D,2DAAA;YAAAlV,EAAA,CAAAK,aAAA,CAAA4T,GAAA;YAAA,OAAAjU,EAAA,CAAAS,WAAA,CACnBmT,GAAA,CAAArC,eAAA,EAAiB;UAAA,EAAC;UAE9BvR,EAAA,CAAAW,YAAA,EAAY;UAGZX,EAAA,CAAAE,cAAA,+BAE+B;UAA7BF,EADA,CAAAG,UAAA,6BAAAgV,8EAAAR,MAAA;YAAA3U,EAAA,CAAAK,aAAA,CAAA4T,GAAA;YAAA,OAAAjU,EAAA,CAAAS,WAAA,CAAmBmT,GAAA,CAAAlC,eAAA,CAAAiD,MAAA,CAAuB;UAAA,EAAC,2BAAAS,4EAAA;YAAApV,EAAA,CAAAK,aAAA,CAAA4T,GAAA;YAAA,OAAAjU,EAAA,CAAAS,WAAA,CAAkBmT,GAAA,CAAAnB,OAAA,EAAS;UAAA,EAAC,wBAAA4C,yEAAA;YAAArV,EAAA,CAAAK,aAAA,CAAA4T,GAAA;YAAA,OAAAjU,EAAA,CAAAS,WAAA,CAAemT,GAAA,CAAAzB,KAAA,EAAO;UAAA,EAAC,0BAAAmD,2EAAA;YAAAtV,EAAA,CAAAK,aAAA,CAAA4T,GAAA;YAAA,OAAAjU,EAAA,CAAAS,WAAA,CAAiBmT,GAAA,CAAArB,OAAA,EAAS;UAAA,EAAC,2BAAAgD,4EAAA;YAAAvV,EAAA,CAAAK,aAAA,CAAA4T,GAAA;YAAA,OAAAjU,EAAA,CAAAS,WAAA,CACxGmT,GAAA,CAAApB,QAAA,EAAU;UAAA,EAAC;UAGxBxS,EAFJ,CAAAE,cAAA,gBAAmC,eACZ,eACG;UACpBF,EAAA,CAAAoU,SAAA,uBAEc;UAChBpU,EAAA,CAAAW,YAAA,EAAM;UACNX,EAAA,CAAAE,cAAA,eAAsB;UACpBF,EAAA,CAAAoU,SAAA,uBAEc;UAChBpU,EAAA,CAAAW,YAAA,EAAM;UACNX,EAAA,CAAAE,cAAA,eAAuB;UACrBF,EAAA,CAAAoU,SAAA,wBAGe;UACjBpU,EAAA,CAAAW,YAAA,EAAM;UACNX,EAAA,CAAAE,cAAA,eAAuB;UACrBF,EAAA,CAAAoU,SAAA,8BAIkB;UAI1BpU,EAHM,CAAAW,YAAA,EAAM,EACF,EACD,EACa;;;UAzOTX,EAAA,CAAAgB,UAAA,uBAAsB;UAIdhB,EAAA,CAAAe,SAAA,GAA4C;UAA5Cf,EAAA,CAAAgB,UAAA,SAAA4S,GAAA,CAAAxR,UAAA,KAAAwR,GAAA,CAAAtR,aAAA,IAAAsR,GAAA,CAAAzR,MAAA,CAA4C;UAWjDnC,EAAA,CAAAe,SAAA,GAAyB;UAAzBf,EAAA,CAAAwV,qBAAA,UAAA5B,GAAA,CAAAhQ,MAAA,CAAA6R,IAAA,CAAyB;UAAkCzV,EAAA,CAAAgB,UAAA,iBAAAhB,EAAA,CAAAiB,eAAA,KAAAyU,GAAA,EAMnE;UAEW1V,EAAA,CAAAe,SAAA,EAAmC;UAAnCf,EAAA,CAAAgB,UAAA,UAAA4S,GAAA,CAAAxR,UAAA,KAAAwR,GAAA,CAAAtR,aAAA,CAAmC;UAWnCtC,EAAA,CAAAe,SAAA,EAAkC;UAAlCf,EAAA,CAAAgB,UAAA,SAAA4S,GAAA,CAAAxR,UAAA,KAAAwR,GAAA,CAAAtR,aAAA,CAAkC;UAiBlCtC,EAAA,CAAAe,SAAA,GAAkC;UAE7Cf,EAFW,CAAAgB,UAAA,gBAAA4S,GAAA,CAAA/F,UAAA,SAAkC,UAAA+F,GAAA,CAAAhQ,MAAA,CAAAoH,QAAA,CAA0B,mBACK,kBAAkB,aAAA4I,GAAA,CAAArR,gBAAA,CACjE;UAIlBvC,EAAA,CAAAe,SAAA,GAA2C;UAEpCf,EAFP,CAAAgB,UAAA,gBAAA4S,GAAA,CAAA/F,UAAA,kBAA2C,UAAA+F,GAAA,CAAAhQ,MAAA,CAAA4B,aAAA,CAA+B,mBACa,kBACjF,aAAAoO,GAAA,CAAArR,gBAAA,CAA8B;UAICvC,EAAA,CAAAe,SAAA,GAA4B;UAEvCf,EAFW,CAAAgB,UAAA,UAAA4S,GAAA,CAAAhQ,MAAA,CAAA2B,WAAA,CAA4B,gBAAAqO,GAAA,CAAA/F,UAAA,gBACnC,WAA2C,mBAClE,kBAAkB,aAAA+F,GAAA,CAAArR,gBAAA,CAA8B;UAWtDvC,EAAA,CAAAe,SAAA,GAA0B;UAA1Bf,EAAA,CAAAwV,qBAAA,UAAA5B,GAAA,CAAAhQ,MAAA,CAAA+R,MAAA,CAA0B;UAAkC3V,EAAA,CAAAgB,UAAA,iBAAAhB,EAAA,CAAAiB,eAAA,KAAAyU,GAAA,EAMpE;UAGQ1V,EAAA,CAAAe,SAAA,EAAyB;UAAzBf,EAAA,CAAAwV,qBAAA,UAAA5B,GAAA,CAAAhQ,MAAA,CAAA6R,IAAA,CAAyB;UAAkCzV,EAAA,CAAAgB,UAAA,iBAAAhB,EAAA,CAAAiB,eAAA,KAAAyU,GAAA,EAMnE;UAEW1V,EAAA,CAAAe,SAAA,EAAmC;UAAnCf,EAAA,CAAAgB,UAAA,UAAA4S,GAAA,CAAAxR,UAAA,KAAAwR,GAAA,CAAAtR,aAAA,CAAmC;UAWnCtC,EAAA,CAAAe,SAAA,EAAkC;UAAlCf,EAAA,CAAAgB,UAAA,SAAA4S,GAAA,CAAAxR,UAAA,KAAAwR,GAAA,CAAAtR,aAAA,CAAkC;UAiBtBtC,EAAA,CAAAe,SAAA,GAA+B;UAA/Bf,EAAA,CAAAwV,qBAAA,gBAAA5B,GAAA,CAAAnQ,WAAA,CAA+B;UAEjBzD,EAAA,CAAAwV,qBAAA,eAAA5B,GAAA,CAAAhQ,MAAA,CAAAgS,IAAA,CAA8B;UACxB5V,EAF/C,CAAAgB,UAAA,YAAA4S,GAAA,CAAA/F,UAAA,oBAAyC,kBAAA+F,GAAA,CAAAtP,aAAA,CAC8D,aAAAsP,GAAA,CAAArR,gBAAA,CAC3B;UAM5EvC,EAAA,CAAAe,SAAA,GAA4C;UAM5Cf,EANA,CAAAgB,UAAA,UAAA4S,GAAA,CAAA5P,4BAAA,CAAAC,KAAA,CAA4C,aAAA2P,GAAA,CAAA5P,4BAAA,CAAAE,QAAA,CACM,UAAA0P,GAAA,CAAA5P,4BAAA,CAAAG,KAAA,CACN,aAAAyP,GAAA,CAAA5P,4BAAA,CAAAI,QAAA,CACM,WAAAwP,GAAA,CAAA5P,4BAAA,CAAAK,MAAA,CACJ,UAAAuP,GAAA,CAAA7P,oBAAA,GAAA6P,GAAA,CAAA9P,gBAAA,MACQ,gBAAA8P,GAAA,CAAA5P,4BAAA,CAAAP,WAAA,CACE;UAUxDzD,EAAA,CAAAe,SAAA,GAA+B;UAS/Bf,EATA,CAAAgB,UAAA,kBAAA4S,GAAA,CAAAjD,aAAA,CAA+B,aAAAiD,GAAA,CAAAhR,YAAA,CACN,cAAAgR,GAAA,CAAA/Q,gBAAA,CACK,qCACM,+BACN,uBACR,4BACK,uBAAA+Q,GAAA,CAAAnC,kBAAA,GACgB,6CACC,6BAChB;UAWlCzR,EAAA,CAAAe,SAAA,EAAyB;UAWzBf,EAXA,CAAAgB,UAAA,SAAA4S,GAAA,CAAA/O,gBAAA,CAAyB,UAAA+O,GAAA,CAAA7O,UAAA,CACL,YAAA6O,GAAA,CAAA5O,YAAA,CACI,wBACD,mBAGL,qBACE,qBACA,4BACO,mCACO,sCACG;UAQrChF,EAAA,CAAAe,SAAA,EAAuB;UAWvBf,EAXA,CAAAgB,UAAA,SAAA4S,GAAA,CAAA9O,cAAA,CAAuB,UAAA8O,GAAA,CAAA7O,UAAA,CACH,YAAA6O,GAAA,CAAA5O,YAAA,CACI,wBACD,mBAGL,qBACE,qBACA,4BACO,mCACO,sCACG;UAOlBhF,EAAA,CAAAe,SAAA,EAAwB;UAA2Cf,EAAnE,CAAAgB,UAAA,SAAA4S,GAAA,CAAA9Q,eAAA,CAAwB,WAAA8Q,GAAA,CAAA3Q,MAAA,CAAkB,cAAA2Q,GAAA,CAAA5Q,SAAA,CAAwB,eAAA4Q,GAAA,CAAA7Q,cAAA,CAA8B;UAG7G/C,EAAA,CAAAe,SAAA,EAA4B;UAA5Bf,EAAA,CAAAgB,UAAA,cAAA4S,GAAA,CAAAjR,cAAA,CAA4B;UAGf3C,EAAA,CAAAe,SAAA,GAAwC;UACOf,EAD/C,CAAAgB,UAAA,gBAAA4S,GAAA,CAAAjF,gBAAA,SAAwC,UAAAiF,GAAA,CAAAhQ,MAAA,CAAAoH,QAAA,CAA0B,kBACF;UAIhEhL,EAAA,CAAAe,SAAA,GAAiD;UACoBf,EADrE,CAAAgB,UAAA,gBAAA4S,GAAA,CAAAjF,gBAAA,kBAAiD,UAAAiF,GAAA,CAAAhQ,MAAA,CAAA4B,aAAA,CAA+B,kBACM;UAIjDxF,EAAA,CAAAe,SAAA,GAA4B;UAEzDf,EAF6B,CAAAgB,UAAA,UAAA4S,GAAA,CAAAhQ,MAAA,CAAA2B,WAAA,CAA4B,gBAAAqO,GAAA,CAAAjF,gBAAA,gBAC7B,WAA2C,mBACxE,kBAAkB;UAIS3O,EAAA,CAAAe,SAAA,GAAwC;UAAxCf,EAAA,CAAAwV,qBAAA,UAAA5B,GAAA,CAAAhQ,MAAA,CAAA8K,mBAAA,CAAwC;UAEhD1O,EAAA,CAAAwV,qBAAA,eAAA5B,GAAA,CAAAhQ,MAAA,CAAAgS,IAAA,CAA8B;UACnE5V,EAFkB,CAAAgB,UAAA,UAAA4S,GAAA,CAAAjF,gBAAA,oBAAAvF,KAAA,CAAmD,kBAEpD;;;qBD9JvBrK,YAAY,EAAA8W,EAAA,CAAAC,IAAA,EACZ7W,mBAAmB,EAAA0T,EAAA,CAAAoD,aAAA,EAAApD,EAAA,CAAAqD,eAAA,EAAArD,EAAA,CAAAsD,oBAAA,EAAAtD,EAAA,CAAAuD,iBAAA,EAAAvD,EAAA,CAAAwD,oBAAA,EAAAxD,EAAA,CAAAyD,kBAAA,EACnB3W,mBAAmB,EACnBD,oBAAoB,EACpBE,eAAe,EACfH,mBAAmB,EACnBF,mBAAmB,EACnBO,mBAAmB,EACnBD,cAAc,EACdI,sBAAsB;MAAAsW,MAAA;IAAA;;SAKb3U,oBAAoB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}