{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, ViewChild } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Subject, takeUntil, switchMap, finalize, catchError, of } from 'rxjs';\nimport { FormsModule } from '@angular/forms';\n// Import child components\nimport { AgentExecutionPlaygroundComponent } from './components/agent-execution-playground/agent-execution-playground.component';\nimport { IconComponent } from '@ava/play-comp-library';\nimport { environment } from '@shared/environments/environment';\nimport { ExecutionStatus } from '@shared/models/execution.model';\n// Remove duplicate definitions - they're now in shared models\nlet AgentExecutionComponent = class AgentExecutionComponent {\n  route;\n  router;\n  agentService;\n  agentPlaygroundService;\n  tokenStorage;\n  loaderService;\n  formBuilder;\n  toolExecutionService;\n  navigationTabs = [{\n    id: 'nav-home',\n    label: 'Agent Activity'\n  }, {\n    id: 'nav-products',\n    label: 'Agent Output'\n  }, {\n    id: 'nav-services',\n    label: 'Preview',\n    disabled: true\n  }];\n  // Agent details\n  agentId = null;\n  agentType = 'individual';\n  agentName = 'Agent';\n  agentDetail = '';\n  playgroundComp;\n  // Activity logs\n  activityLogs = [];\n  activityProgress = 0;\n  executionDetails;\n  isRunning = false;\n  status = ExecutionStatus.notStarted;\n  // Chat messages\n  chatMessages = [];\n  isProcessingChat = false;\n  inputText = '';\n  // Enhanced chat interface properties\n  chatHistory = []; // Store all chat interactions with status\n  currentChatStatus = null;\n  showStatusMessage = false;\n  // Agent outputs\n  agentOutputs = [];\n  latestAgentResponse = null; // Store the latest agent response for display\n  agentForm;\n  fileType = '.pdf,.doc,.docx,.txt,.csv,.xlsx,.xls,.ppt,.pptx,.png,.jpg,.jpeg,.gif,.bmp,.svg';\n  // Execution state\n  executionStartTime = null;\n  executionCompleted = false;\n  executionId;\n  enableStreamingLog = environment.enableLogStreaming || 'all';\n  isExecutionComplete = false;\n  progressInterval;\n  destroy$ = new Subject();\n  selectedTab = 'Agent Activity';\n  demoTabs = [{\n    id: 'tab1',\n    label: 'History'\n  }, {\n    id: 'tab2',\n    label: 'Blueprint'\n  }, {\n    id: 'tab3',\n    label: 'Agent Output'\n  }];\n  errorMsg = false;\n  resMessage;\n  taskMessage = [];\n  isJsonValid = false;\n  disableChat = false;\n  selectedFiles = [];\n  agentNodes = [];\n  userInputList = [];\n  progress = 0;\n  isLoading = false;\n  loaderColor = '';\n  inputFieldOrder = [];\n  currentInputIndex = 0;\n  activeTabId = 'nav-home';\n  // Panel state properties\n  isLeftPanelCollapsed = false;\n  activeRightTab = 'blueprint';\n  // Agent-specific properties\n  currentAgentDetails = null;\n  buildAgentNodes = [];\n  canvasNodes = [];\n  canvasEdges = [];\n  selectedPrompt = '';\n  selectedAgentMode = '';\n  selectedUseCaseIdentifier = '';\n  agentFilesUploadedData = [];\n  agentAttachment = [];\n  isAgentPlaygroundLoading = false;\n  agentPlaygroundDestroy = new Subject();\n  agentChatPayload = [];\n  agentCode = '';\n  promptOptions = [];\n  // Custom Blueprint Display Properties\n  blueprintCompletionPercentage = 0;\n  blueprintPromptNodes = [];\n  blueprintModelNodes = [];\n  blueprintKnowledgeNodes = [];\n  blueprintGuardrailNodes = [];\n  blueprintToolNodes = [];\n  // Blueprint zone expansion state\n  blueprintZonesExpanded = {\n    prompt: true,\n    model: true,\n    knowledge: true,\n    guardrail: true,\n    tool: true\n  };\n  // Blueprint panel properties (using existing arrays above)\n  constructor(route, router, agentService, agentPlaygroundService, tokenStorage, loaderService, formBuilder, toolExecutionService) {\n    this.route = route;\n    this.router = router;\n    this.agentService = agentService;\n    this.agentPlaygroundService = agentPlaygroundService;\n    this.tokenStorage = tokenStorage;\n    this.loaderService = loaderService;\n    this.formBuilder = formBuilder;\n    this.toolExecutionService = toolExecutionService;\n    this.agentForm = this.formBuilder.group({\n      isConversational: [true],\n      isUseTemplate: [false]\n    });\n  }\n  ngOnInit() {\n    console.log('🌟 SHARED COMPONENT INITIALIZED! 🌟');\n    this.executionId = crypto.randomUUID();\n    this.route.params.subscribe(params => {\n      this.agentType = params['type'] || 'individual';\n      console.log('🌟 SHARED: Agent type set to:', this.agentType);\n    });\n    this.route.queryParams.subscribe(params => {\n      if (params['id']) {\n        this.agentId = params['id'];\n        this.loadAgentData(params['id']);\n      }\n    });\n    // Initialize chat messages\n    this.chatMessages = [{\n      from: 'ai',\n      text: `Hi there! I am ${this.agentName || 'your agent'}. How can I help you today?`\n    }];\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n    if (this.progressInterval) {\n      clearInterval(this.progressInterval);\n    }\n  }\n  onTabChange(event) {\n    this.activeTabId = event.id;\n    this.selectedTab = event.label;\n  }\n  loadAgentData(agentId) {\n    this.isLoading = true;\n    // Load agent data based on type\n    if (this.agentType === 'collaborative') {\n      this.agentService.getCollaborativeAgentDetailsById(agentId).subscribe({\n        next: response => {\n          this.handleAgentDataResponse(response);\n        },\n        error: error => {\n          console.error('Error loading collaborative agent:', error);\n          this.isLoading = false;\n        }\n      });\n    } else {\n      this.agentService.getAgentById(agentId).subscribe({\n        next: response => {\n          this.handleAgentDataResponse(response);\n        },\n        error: error => {\n          console.error('Error loading individual agent:', error);\n          this.isLoading = false;\n        }\n      });\n    }\n  }\n  handleAgentDataResponse(response) {\n    this.isLoading = false;\n    // Extract agent details\n    let agentData;\n    if (response.agentDetails && Array.isArray(response.agentDetails) && response.agentDetails.length > 0) {\n      agentData = response.agentDetails[0];\n    } else if (response.agentDetail) {\n      agentData = response.agentDetail;\n    } else if (response.data) {\n      agentData = response.data;\n    } else {\n      agentData = response;\n    }\n    if (agentData) {\n      this.currentAgentDetails = agentData;\n      this.agentName = agentData.name || agentData.agentName || 'Agent';\n      this.agentDetail = agentData.description || agentData.agentDetail || '';\n      // For individual agents, set up the required properties for playground functionality\n      if (this.agentType === 'individual') {\n        // Set selectedPrompt to agent name or useCaseName to bypass dropdown requirement\n        this.selectedPrompt = agentData.useCaseName || agentData.name || 'loaded-agent';\n        // Set selectedAgentMode for API calls - use useCaseCode if available\n        this.selectedAgentMode = agentData.useCaseCode || agentData.useCaseName || agentData.name || '';\n        // Set useCaseIdentifier - use organizationPath if available\n        if (agentData.organizationPath) {\n          this.selectedUseCaseIdentifier = agentData.organizationPath;\n        } else if (agentData.useCaseCode) {\n          this.selectedUseCaseIdentifier = agentData.useCaseCode;\n        } else if (agentData.useCaseName) {\n          this.selectedUseCaseIdentifier = agentData.useCaseName;\n        }\n      }\n      // Update chat message with agent name\n      if (this.chatMessages.length > 0) {\n        this.chatMessages[0].text = `Hi there! I am ${this.agentName}. How can I help you today?`;\n      }\n      // Load agent nodes and configuration\n      this.loadAgentNodes(agentData);\n    }\n  }\n  loadAgentNodes(agentData) {\n    // Map agent configuration to blueprint panel\n    this.mapAgentConfigurationToBlueprint(agentData);\n  }\n  handleChatMessage(message) {\n    if (this.agentType === 'individual') {\n      // For individual agents, use the loaded agent details instead of requiring dropdown selection\n      if (!this.currentAgentDetails && (!this.selectedPrompt || this.selectedPrompt === 'default')) {\n        this.showAgentError('Agent details are not loaded. Please try refreshing the page.');\n        return;\n      }\n      let displayMessage = message;\n      if (this.agentFilesUploadedData.length > 0) {\n        const fileNames = this.agentFilesUploadedData.map(file => file.documentName).join(', ');\n        displayMessage = `${message}\\n\\n📎 Attached files: ${fileNames}`;\n      }\n      // Add user message to chat\n      this.chatMessages = [...this.chatMessages, {\n        from: 'user',\n        text: displayMessage\n      }];\n      // Set loading state\n      this.isProcessingChat = true;\n      this.currentChatStatus = 'loading';\n      this.showStatusMessage = false;\n      // Add loading message\n      this.chatMessages = [...this.chatMessages, {\n        from: 'ai',\n        text: '...',\n        isLoading: true\n      }];\n      const isConversational = this.agentForm.get('isConversational')?.value || false;\n      const isUseTemplate = this.agentForm.get('isUseTemplate')?.value || false;\n      console.log('Chat message handling - isConversational:', isConversational, 'isUseTemplate:', isUseTemplate);\n      // Use agent details from the loaded agent data instead of dropdown selection\n      // Mode should be the useCaseCode, not useCaseName\n      const agentMode = this.agentCode || this.selectedAgentMode || this.currentAgentDetails?.useCaseCode || this.currentAgentDetails?.useCaseName || this.currentAgentDetails?.name || this.selectedPrompt;\n      let useCaseIdentifier = this.selectedUseCaseIdentifier;\n      if (!useCaseIdentifier) {\n        // Use organizationPath if available, otherwise build from agent details\n        if (this.currentAgentDetails?.organizationPath) {\n          useCaseIdentifier = this.currentAgentDetails.organizationPath;\n        } else {\n          const orgPath = this.buildOrganizationPath();\n          const agentIdentifier = this.currentAgentDetails?.useCaseCode || this.currentAgentDetails?.useCaseName || this.currentAgentDetails?.name || agentMode;\n          useCaseIdentifier = `${agentIdentifier}${orgPath}`;\n        }\n      }\n      if (this.agentFilesUploadedData.length > 0) {\n        this.processAgentFilesAndSendMessage(message, agentMode, useCaseIdentifier, isConversational, isUseTemplate);\n        return;\n      }\n      this.sendAgentMessageToAPI(message, agentMode, useCaseIdentifier, isConversational, isUseTemplate);\n    } else if (this.agentType === 'collaborative') {\n      this.isProcessingChat = true;\n      let payload = {\n        executionId: this.executionId,\n        agentId: Number(this.agentId),\n        user: this.tokenStorage.getDaUsername() || '<EMAIL>',\n        userInputs: {\n          question: message\n        }\n      };\n      if (this.agentFilesUploadedData.length > 0) {\n        const fileWrapper = this.agentFilesUploadedData[0];\n        let displayMessage;\n        if (this.agentFilesUploadedData.length > 0) {\n          const fileNames = this.agentFilesUploadedData.map(file => file.documentName).join(', ');\n          displayMessage = `📎 Attached files: ${fileNames}`;\n          this.chatMessages = [{\n            from: 'user',\n            text: displayMessage\n          }];\n        }\n        this.agentPlaygroundService.submitAgentExecuteWithFile(payload, fileWrapper).pipe(finalize(() => {\n          this.isProcessingChat = false;\n          this.isAgentPlaygroundLoading = false;\n        }), takeUntil(this.agentPlaygroundDestroy)).subscribe({\n          next: res => this.handleAgentExecuteResponse(res, message),\n          error: err => this.handleAgentExecuteError(err, message)\n        });\n      } else {\n        this.agentPlaygroundService.submitAgentExecute(payload).pipe(finalize(() => {\n          this.isProcessingChat = false;\n          this.isAgentPlaygroundLoading = false;\n        }), takeUntil(this.agentPlaygroundDestroy)).subscribe({\n          next: res => this.handleAgentExecuteResponse(res, message),\n          error: err => this.handleAgentExecuteError(err, message)\n        });\n      }\n    }\n  }\n  onPromptChanged(prompt) {\n    this.inputText = prompt.name || String(prompt.value) || '';\n  }\n  onPlaygroundConversationalToggle(value) {\n    // Update the form control\n    this.agentForm.get('isConversational')?.setValue(value);\n    // When conversational mode is turned off, clear the conversation history\n    // This ensures that the next message will be treated as a fresh start\n    if (!value) {\n      this.agentChatPayload = [];\n      console.log('Conversational mode disabled - cleared chat payload history');\n    } else {\n      console.log('Conversational mode enabled - will maintain chat history');\n    }\n  }\n  onPlaygroundTemplateToggle(value) {\n    // Update the form control\n    this.agentForm.get('isUseTemplate')?.setValue(value);\n    console.log('Template mode toggled:', value);\n  }\n  onFilesSelected(files) {\n    this.selectedFiles = files;\n    // Update agentFilesUploadedData for agent execution\n    this.agentFilesUploadedData = files;\n  }\n  onApprovalRequested() {\n    // Handle approval request\n  }\n  saveLogs() {\n    // Save execution logs\n  }\n  exportResults(section) {\n    // Export results\n  }\n  handleControlAction(action) {\n    // Handle execution control actions\n  }\n  navigateBack() {\n    this.router.navigate(['/build/agents', this.agentType], {\n      queryParams: {\n        id: this.agentId,\n        mode: 'view'\n      }\n    });\n  }\n  editAgent() {\n    this.router.navigate(['/build/agents', this.agentType], {\n      queryParams: {\n        id: this.agentId,\n        mode: 'edit'\n      }\n    });\n  }\n  navigateToAgentsList() {\n    this.router.navigate(['/build/agents']);\n  }\n  toggleLeftPanel() {\n    this.isLeftPanelCollapsed = !this.isLeftPanelCollapsed;\n  }\n  setActiveRightTab(tab) {\n    this.activeRightTab = tab;\n  }\n  // Blueprint zone management methods\n  toggleBlueprintZone(zoneType) {\n    this.blueprintZonesExpanded[zoneType] = !this.blueprintZonesExpanded[zoneType];\n  }\n  isBlueprintZoneExpanded(zoneType) {\n    return this.blueprintZonesExpanded[zoneType] || false;\n  }\n  // API and helper methods from build-agents component\n  showAgentError(message) {\n    this.chatMessages = [...this.chatMessages, {\n      from: 'ai',\n      text: message\n    }];\n  }\n  buildOrganizationPath() {\n    // Simple implementation - in real scenario this would be from navbar/metadata\n    return '';\n  }\n  getMetadataFromNavbar() {\n    // Simple implementation - in real scenario this would get org level mapping\n    return {};\n  }\n  handleAgentExecuteError(err, message) {\n    // Remove loading message\n    this.chatMessages = this.chatMessages.filter(m => !m.isLoading);\n    const errorMessageText = err?.error?.message || err?.message || 'Something went wrong.';\n    const errorMessage = {\n      from: 'ai',\n      text: errorMessageText,\n      status: 'failed',\n      timestamp: new Date()\n    };\n    this.chatMessages.push(errorMessage);\n    // Update history and status\n    this.chatHistory.push({\n      user: message,\n      ai: errorMessage\n    });\n    this.currentChatStatus = 'failed';\n    this.isProcessingChat = false;\n    this.showStatusMessage = true;\n    this.setActiveRightTab('output'); // Switch to output tab\n  }\n  handleAgentExecuteResponse(response, message) {\n    // Remove loading message\n    this.chatMessages = this.chatMessages.filter(m => !m.isLoading);\n    try {\n      this.latestAgentResponse = response;\n      const outputRaw = response?.agentResponse?.agent?.output;\n      let formattedOutput = '';\n      if (outputRaw) {\n        formattedOutput = outputRaw.replace(/\\\\n/g, '\\n');\n      } else {\n        formattedOutput = response?.agentResponse?.detail || 'No response from agent.';\n      }\n      // Update chat with success message\n      const successMessage = {\n        from: 'ai',\n        text: formattedOutput,\n        status: 'success',\n        timestamp: new Date()\n      };\n      this.chatMessages.push(successMessage);\n      // Update history and status\n      this.chatHistory.push({\n        user: message,\n        ai: successMessage\n      });\n      this.currentChatStatus = 'success';\n    } catch (err) {\n      const errorMessageText = err?.message || 'Agent response could not be processed.';\n      const errorMessage = {\n        from: 'ai',\n        text: errorMessageText,\n        status: 'failed',\n        timestamp: new Date()\n      };\n      this.chatMessages.push(errorMessage);\n      // Update history and status for error\n      this.chatHistory.push({\n        user: message,\n        ai: errorMessage\n      });\n      this.currentChatStatus = 'failed';\n    } finally {\n      this.isProcessingChat = false;\n      this.showStatusMessage = true;\n      this.setActiveRightTab('output'); // Switch to output tab\n    }\n  }\n  processAgentFilesAndSendMessage(message, mode, useCaseIdentifier, isConversational, isUseTemplate) {\n    const formData = new FormData();\n    this.agentFilesUploadedData.forEach(fileData => {\n      if (fileData.file) {\n        formData.append('files', fileData.file);\n      }\n    });\n    if (formData.has('files')) {\n      this.agentPlaygroundService.getFileToContent(formData).pipe(switchMap(fileResponse => {\n        const fileContent = fileResponse?.fileResponses?.map(response => response.fileContent)?.join('\\n') || '';\n        this.sendAgentMessageToAPIWithFiles(message, mode, useCaseIdentifier, isConversational, isUseTemplate, fileContent);\n        return of(null);\n      }), catchError(error => {\n        console.error('Error parsing files:', error);\n        this.sendAgentMessageToAPI(message, mode, useCaseIdentifier, isConversational, isUseTemplate);\n        return of(null);\n      })).subscribe();\n    } else {\n      this.sendAgentMessageToAPI(message, mode, useCaseIdentifier, isConversational, isUseTemplate);\n    }\n  }\n  sendAgentMessageToAPI(message, mode, useCaseIdentifier, isConversational, isUseTemplate) {\n    console.log('API Call Parameters:', {\n      message,\n      mode,\n      useCaseIdentifier,\n      isConversational,\n      isUseTemplate,\n      currentChatPayloadLength: this.agentChatPayload.length\n    });\n    if (isConversational) {\n      this.agentChatPayload.push({\n        content: message,\n        role: 'user'\n      });\n    }\n    const payload = isConversational ? this.agentChatPayload : message;\n    const {\n      levelId\n    } = this.getMetadataFromNavbar();\n    console.log('Final payload being sent:', payload);\n    this.agentPlaygroundService.generatePrompt(payload, mode, isConversational, isUseTemplate, this.agentAttachment, useCaseIdentifier, '', levelId).pipe(finalize(() => {\n      this.isProcessingChat = false;\n      this.isAgentPlaygroundLoading = false;\n    }), takeUntil(this.agentPlaygroundDestroy)).subscribe({\n      next: generatedResponse => {\n        // Store the latest response for display in the output panel\n        this.latestAgentResponse = generatedResponse;\n        if (generatedResponse?.response && generatedResponse?.response?.choices) {\n          const aiResponseText = generatedResponse.response.choices[0].text;\n          this.chatMessages = [...this.chatMessages, {\n            from: 'ai',\n            text: aiResponseText\n          }];\n          if (isConversational) {\n            this.agentChatPayload.push({\n              content: aiResponseText,\n              role: 'assistant'\n            });\n          }\n        } else {\n          console.warn('Unexpected API response format:', generatedResponse);\n          this.showAgentError('Received unexpected response format from API.');\n        }\n      },\n      error: error => {\n        console.error('API Error:', error);\n        const errorMessage = error?.error?.message || 'An error occurred while processing your request.';\n        this.showAgentError(errorMessage);\n        if (isConversational && this.agentChatPayload.length > 0) {\n          this.agentChatPayload.pop();\n        }\n      }\n    });\n  }\n  sendAgentMessageToAPIWithFiles(message, mode, useCaseIdentifier, isConversational, isUseTemplate, fileContents) {\n    if (isConversational) {\n      this.agentChatPayload.push({\n        content: message,\n        role: 'user'\n      });\n    }\n    const payload = isConversational ? this.agentChatPayload : message;\n    const {\n      levelId\n    } = this.getMetadataFromNavbar();\n    this.agentPlaygroundService.generatePrompt(payload, mode, isConversational, isUseTemplate, this.agentAttachment, useCaseIdentifier, fileContents, levelId).pipe(finalize(() => {\n      this.isProcessingChat = false;\n      this.isAgentPlaygroundLoading = false;\n    }), takeUntil(this.agentPlaygroundDestroy)).subscribe({\n      next: generatedResponse => {\n        if (generatedResponse?.response && generatedResponse?.response?.choices) {\n          const aiResponseText = generatedResponse.response.choices[0].text;\n          this.chatMessages = [...this.chatMessages, {\n            from: 'ai',\n            text: aiResponseText\n          }];\n          if (isConversational) {\n            this.agentChatPayload.push({\n              content: aiResponseText,\n              role: 'assistant'\n            });\n          }\n        } else {\n          console.warn('Unexpected API response format:', generatedResponse);\n          this.showAgentError('Received unexpected response format from API.');\n        }\n      },\n      error: error => {\n        console.error('API Error:', error);\n        const errorMessage = error?.error?.message || 'An error occurred while processing your request.';\n        this.showAgentError(errorMessage);\n        if (isConversational && this.agentChatPayload.length > 0) {\n          this.agentChatPayload.pop();\n        }\n      }\n    });\n  }\n  // Blueprint panel methods\n  mapAgentConfigurationToBlueprint(agentData) {\n    if (!agentData) {\n      console.warn('No agent data provided for blueprint');\n      return;\n    }\n    console.log('🔍 DEBUG: Full agent data received:', agentData);\n    console.log('🔍 DEBUG: Agent type:', this.agentType);\n    console.log('🔍 DEBUG: Agent data keys:', Object.keys(agentData));\n    // Clear existing nodes\n    this.buildAgentNodes = [];\n    this.canvasNodes = [];\n    let nodeCounter = 1;\n    // Map agent configuration to nodes based on agent type\n    if (this.agentType === 'individual') {\n      this.mapIndividualAgentToBlueprint(agentData, nodeCounter);\n    } else if (this.agentType === 'collaborative') {\n      this.mapCollaborativeAgentToBlueprint(agentData, nodeCounter);\n    }\n    console.log('🎯 Blueprint nodes mapped:', {\n      buildAgentNodes: this.buildAgentNodes,\n      canvasNodes: this.canvasNodes,\n      totalNodes: this.buildAgentNodes.length\n    });\n  }\n  mapIndividualAgentToBlueprint(agentData, nodeCounter) {\n    console.log('🔍 Individual agent mapping - checking fields:', {\n      config: agentData.config,\n      configLength: agentData.config?.length,\n      useCaseName: agentData.useCaseName,\n      prompt: agentData.prompt,\n      useCaseDetails: agentData.useCaseDetails\n    });\n    // Clear existing blueprint nodes\n    this.blueprintPromptNodes = [];\n    this.blueprintModelNodes = [];\n    this.blueprintKnowledgeNodes = [];\n    this.blueprintGuardrailNodes = [];\n    // Add prompt node from \"prompt\" field\n    if (agentData.prompt) {\n      this.blueprintPromptNodes.push({\n        id: `prompt-${nodeCounter++}`,\n        name: agentData.prompt,\n        type: 'prompt'\n      });\n      console.log('✅ Added prompt node:', agentData.prompt);\n    }\n    // Process the config array to extract model, knowledge bases, and guardrails\n    if (agentData.config && Array.isArray(agentData.config)) {\n      console.log('🔍 Processing config array with length:', agentData.config.length);\n      agentData.config.forEach((category, categoryIndex) => {\n        console.log(`🔍 Category ${categoryIndex} (ID: ${category.categoryId}):`, category.categoryName);\n        if (category.config && Array.isArray(category.config)) {\n          console.log(`🔍 Category ${categoryIndex} has ${category.config.length} config items`);\n          category.config.forEach((configItem, itemIndex) => {\n            console.log(`🔍 Config item ${categoryIndex}-${itemIndex}:`, {\n              configKey: configItem.configKey,\n              configValue: configItem.configValue,\n              categoryId: configItem.categoryId\n            });\n            // Handle AI Model from categoryId 1\n            if (configItem.categoryId === 1 && configItem.configKey === 'MODEL' && configItem.configValue) {\n              console.log('✅ Adding AI model node from categoryId 1:', configItem.configValue);\n              this.blueprintModelNodes.push({\n                id: `model-${nodeCounter++}`,\n                name: `${configItem.configKey}`,\n                type: 'model'\n              });\n            }\n            // Handle Knowledge Base from categoryId 2\n            if (configItem.categoryId === 2 && configItem.configKey === 'RAG_KNOWLEDGEBASE_NAME' && configItem.configValue) {\n              console.log('✅ Adding knowledge base nodes from categoryId 2:', configItem.configValue);\n              const kbValue = configItem.configValue.toString();\n              const kbIds = kbValue.split(',').map(id => id.trim()).filter(id => id);\n              kbIds.forEach(kbId => {\n                this.blueprintKnowledgeNodes.push({\n                  id: `knowledge-${nodeCounter++}`,\n                  name: `Knowledge Base: ${kbId}`,\n                  type: 'knowledge'\n                });\n              });\n            }\n            // Handle Guardrails from categoryId 3 where configValue is true\n            if (configItem.categoryId === 3 && configItem.configValue === 'true') {\n              console.log('✅ Found enabled guardrail from categoryId 3:', {\n                key: configItem.configKey,\n                value: configItem.configValue\n              });\n              if (configItem.configKey === 'ENABLE_GUARDRAILS') {\n                // Only add one general guardrail node if not already added\n                if (this.blueprintGuardrailNodes.length === 0) {\n                  this.blueprintGuardrailNodes.push({\n                    id: `guardrail-${nodeCounter++}`,\n                    name: 'Guardrails Enabled',\n                    type: 'guardrail'\n                  });\n                }\n              } else if (configItem.configKey.startsWith('GUARDRAIL_')) {\n                // Add specific guardrail nodes for enabled guardrails\n                let guardrailName = configItem.configKey;\n                if (guardrailName.startsWith('GUARDRAIL_')) {\n                  guardrailName = guardrailName.replace('GUARDRAIL_', '').replace(/_/g, ' ');\n                }\n                this.blueprintGuardrailNodes.push({\n                  id: `guardrail-${nodeCounter++}`,\n                  name: `${guardrailName}`,\n                  type: 'guardrail'\n                });\n              }\n            }\n          });\n        }\n      });\n    }\n    console.log('🎯 Final blueprint nodes:', {\n      promptNodes: this.blueprintPromptNodes,\n      modelNodes: this.blueprintModelNodes,\n      knowledgeNodes: this.blueprintKnowledgeNodes,\n      guardrailNodes: this.blueprintGuardrailNodes\n    });\n    // Calculate completion percentage\n    const totalRequired = 2; // Prompt + Model are required\n    const currentRequired = this.blueprintPromptNodes.length + this.blueprintModelNodes.length;\n    this.blueprintCompletionPercentage = Math.round(currentRequired / totalRequired * 100);\n  }\n  mapCollaborativeAgentToBlueprint(agentData, nodeCounter) {\n    console.log('🚀 SHARED COMPONENT: mapCollaborativeAgentToBlueprint called!');\n    console.log('🔍 DEBUG: Collaborative agent data received:', agentData);\n    console.log('🔍 DEBUG: Collaborative agent data keys:', Object.keys(agentData));\n    console.log('🔍 DEBUG: Agent type in component:', this.agentType);\n    console.log('🔍 DEBUG: Current nodeCounter:', nodeCounter);\n    // Clear existing blueprint nodes\n    this.blueprintPromptNodes = [];\n    this.blueprintModelNodes = [];\n    this.blueprintKnowledgeNodes = [];\n    this.blueprintToolNodes = [];\n    this.blueprintGuardrailNodes = [];\n    console.log('🔍 DEBUG: Cleared all blueprint node arrays');\n    // Add prompt node - handle different prompt structures for collaborative agents\n    const shouldCreatePromptNode = agentData.goal || agentData.role || agentData.description;\n    console.log('🔍 DEBUG: Checking prompt node creation:', {\n      goal: agentData.goal,\n      role: agentData.role,\n      description: agentData.description,\n      shouldCreatePromptNode\n    });\n    if (shouldCreatePromptNode) {\n      let promptNodeName = agentData.goal || agentData.role || agentData.description || 'Collaborative Agent Prompt';\n      // Truncate prompt if too long for display\n      if (promptNodeName.length > 150) {\n        promptNodeName = promptNodeName.substring(0, 150) + '...';\n      }\n      this.blueprintPromptNodes.push({\n        id: `prompt-${nodeCounter++}`,\n        name: promptNodeName,\n        type: 'prompt'\n      });\n      console.log('✅ Added collaborative prompt node:', promptNodeName);\n    }\n    // Add model nodes - handle both old and new API formats like build-agents\n    let modelReferences = [];\n    console.log('🔍 DEBUG: Checking model data:', {\n      hasAgentConfigs: !!agentData.agentConfigs,\n      agentConfigs: agentData.agentConfigs,\n      model: agentData.model,\n      modelName: agentData.modelName,\n      modelDetails: agentData.modelDetails\n    });\n    // New API format: agentConfigs.modelRef (array of model IDs or objects)\n    if (agentData.agentConfigs && agentData.agentConfigs.modelRef) {\n      const modelRefs = Array.isArray(agentData.agentConfigs.modelRef) ? agentData.agentConfigs.modelRef : [agentData.agentConfigs.modelRef];\n      modelReferences = modelRefs.map(ref => {\n        if (typeof ref === 'number' || typeof ref === 'string') {\n          return {\n            modelId: ref\n          };\n        }\n        return ref;\n      });\n    }\n    // Old API format: modelDetails\n    else if (agentData.modelDetails) {\n      modelReferences = [agentData.modelDetails];\n    }\n    // Fallback: check for model or modelName directly\n    else if (agentData.model || agentData.modelName) {\n      modelReferences = [{\n        modelId: agentData.model || agentData.modelName\n      }];\n    }\n    modelReferences.forEach(modelRef => {\n      const modelId = modelRef.modelId || modelRef.id;\n      const modelName = modelRef.model || modelRef.modelDeploymentName || `Model ID: ${modelId}`;\n      this.blueprintModelNodes.push({\n        id: `model-${nodeCounter++}`,\n        name: modelName,\n        type: 'model'\n      });\n      console.log('✅ Added collaborative model node:', modelName);\n    });\n    // Add knowledge base nodes - handle both old and new API formats\n    let knowledgeReferences = [];\n    // New API format: agentConfigs.knowledgeBaseRef (array of KB IDs or objects)\n    if (agentData.agentConfigs && agentData.agentConfigs.knowledgeBaseRef) {\n      const kbRefs = Array.isArray(agentData.agentConfigs.knowledgeBaseRef) ? agentData.agentConfigs.knowledgeBaseRef : [agentData.agentConfigs.knowledgeBaseRef];\n      knowledgeReferences = kbRefs.map(ref => {\n        if (typeof ref === 'number' || typeof ref === 'string') {\n          return {\n            knowledgeBaseId: ref\n          };\n        }\n        return ref;\n      });\n    }\n    // Old API format: knowledgeBase\n    else if (agentData.knowledgeBase && Array.isArray(agentData.knowledgeBase)) {\n      knowledgeReferences = agentData.knowledgeBase;\n    }\n    knowledgeReferences.forEach(kbRef => {\n      const kbId = kbRef.knowledgeBaseId || kbRef.id;\n      const collectionName = kbRef.indexCollectionName || kbRef.name;\n      const kbName = collectionName || `Knowledge Base ID: ${kbId}`;\n      this.blueprintKnowledgeNodes.push({\n        id: `knowledge-${nodeCounter++}`,\n        name: kbName,\n        type: 'knowledge'\n      });\n      console.log('✅ Added collaborative knowledge node:', kbName);\n    });\n    // Add tool nodes - handle both old and new API formats like build-agents\n    let toolReferences = [];\n    let userToolReferences = [];\n    console.log('🔍 DEBUG: Checking tool data:', {\n      hasAgentConfigs: !!agentData.agentConfigs,\n      agentConfigsContent: agentData.agentConfigs,\n      hasTools: agentData.tools,\n      toolsContent: agentData.tools,\n      hasUserTools: agentData.userTools,\n      userToolsContent: agentData.userTools\n    });\n    // New API format: agentConfigs.toolRef and agentConfigs.userToolRef\n    if (agentData.agentConfigs) {\n      if (agentData.agentConfigs.toolRef) {\n        const toolRefs = Array.isArray(agentData.agentConfigs.toolRef) ? agentData.agentConfigs.toolRef : [agentData.agentConfigs.toolRef];\n        toolReferences = toolRefs.map(ref => {\n          if (typeof ref === 'number' || typeof ref === 'string') {\n            return {\n              toolId: ref\n            };\n          }\n          return ref;\n        });\n      }\n      if (agentData.agentConfigs.userToolRef) {\n        const userToolRefs = Array.isArray(agentData.agentConfigs.userToolRef) ? agentData.agentConfigs.userToolRef : [agentData.agentConfigs.userToolRef];\n        userToolReferences = userToolRefs.map(ref => {\n          if (typeof ref === 'number' || typeof ref === 'string') {\n            return {\n              toolId: ref\n            };\n          }\n          return ref;\n        });\n      }\n    }\n    // Old API format: tools and userTools\n    else {\n      if (agentData.tools && Array.isArray(agentData.tools)) {\n        toolReferences = agentData.tools;\n      }\n      if (agentData.userTools && Array.isArray(agentData.userTools)) {\n        userToolReferences = agentData.userTools;\n      }\n    }\n    // Process built-in tools\n    toolReferences.forEach(tool => {\n      const toolId = tool.toolId || tool.id;\n      const toolName = tool.toolName || tool.name || `Tool ID: ${toolId}`;\n      this.blueprintToolNodes.push({\n        id: `tool-${nodeCounter++}`,\n        name: toolName,\n        type: 'tool'\n      });\n      console.log('✅ Added collaborative builtin tool node:', toolName);\n    });\n    // Process user tools\n    userToolReferences.forEach(userTool => {\n      const userToolId = userTool.toolId || userTool.id;\n      const userToolName = userTool.toolName || userTool.name || `User Tool ID: ${userToolId}`;\n      this.blueprintToolNodes.push({\n        id: `tool-${nodeCounter++}`,\n        name: userToolName,\n        type: 'tool'\n      });\n      console.log('✅ Added collaborative user tool node:', userToolName);\n    });\n    console.log('🎯 Final collaborative blueprint nodes:', {\n      promptNodes: this.blueprintPromptNodes,\n      modelNodes: this.blueprintModelNodes,\n      knowledgeNodes: this.blueprintKnowledgeNodes,\n      toolNodes: this.blueprintToolNodes,\n      guardrailNodes: this.blueprintGuardrailNodes\n    });\n    // Debug: Check blueprint node arrays lengths\n    console.log('📊 Blueprint node counts:', {\n      promptCount: this.blueprintPromptNodes.length,\n      modelCount: this.blueprintModelNodes.length,\n      knowledgeCount: this.blueprintKnowledgeNodes.length,\n      toolCount: this.blueprintToolNodes.length,\n      guardrailCount: this.blueprintGuardrailNodes.length\n    });\n    // Debug: Check if tools zone will be visible\n    console.log('🔧 Tools zone debug:', {\n      agentType: this.agentType,\n      isCollaborative: this.agentType === 'collaborative',\n      hasToolNodes: this.blueprintToolNodes.length > 0,\n      toolNodeNames: this.blueprintToolNodes.map(t => t.name)\n    });\n    // Calculate completion percentage\n    const totalRequired = 2; // Prompt + Model are required\n    const currentRequired = this.blueprintPromptNodes.length + this.blueprintModelNodes.length;\n    this.blueprintCompletionPercentage = Math.round(currentRequired / totalRequired * 100);\n  }\n};\n__decorate([ViewChild(AgentExecutionPlaygroundComponent, {\n  static: false\n})], AgentExecutionComponent.prototype, \"playgroundComp\", void 0);\nAgentExecutionComponent = __decorate([Component({\n  selector: 'app-agent-execution',\n  standalone: true,\n  imports: [CommonModule, FormsModule, AgentExecutionPlaygroundComponent, IconComponent],\n  templateUrl: './agent-execution.component.html',\n  styleUrls: ['./agent-execution.component.scss']\n})], AgentExecutionComponent);\nexport { AgentExecutionComponent };", "map": {"version": 3, "names": ["Component", "ViewChild", "CommonModule", "Subject", "takeUntil", "switchMap", "finalize", "catchError", "of", "FormsModule", "AgentExecutionPlaygroundComponent", "IconComponent", "environment", "ExecutionStatus", "AgentExecutionComponent", "route", "router", "agentService", "agentPlaygroundService", "tokenStorage", "loaderService", "formBuilder", "toolExecutionService", "navigationTabs", "id", "label", "disabled", "agentId", "agentType", "<PERSON><PERSON><PERSON>", "agentDetail", "playgroundComp", "activityLogs", "activityProgress", "executionDetails", "isRunning", "status", "notStarted", "chatMessages", "isProcessingChat", "inputText", "chatHistory", "currentChatStatus", "showStatusMessage", "agentOutputs", "latestAgentResponse", "agentForm", "fileType", "executionStartTime", "executionCompleted", "executionId", "enableStreamingLog", "enableLogStreaming", "isExecutionComplete", "progressInterval", "destroy$", "selectedTab", "demoTabs", "errorMsg", "resMessage", "taskMessage", "isJsonValid", "disable<PERSON>hat", "selectedFiles", "agentNodes", "userInputList", "progress", "isLoading", "loaderColor", "inputFieldOrder", "currentInputIndex", "activeTabId", "isLeftPanelCollapsed", "activeRightTab", "currentAgentDetails", "buildAgentNodes", "canvasNodes", "canvasEdges", "selected<PERSON><PERSON><PERSON>", "selectedAgentMode", "selectedUseCaseIdentifier", "agentFilesUploadedData", "agentAttachment", "isAgentPlaygroundLoading", "agentPlaygroundDestroy", "agentChatPayload", "agentCode", "promptOptions", "blueprintCompletionPercentage", "blueprintPromptNodes", "blueprintModelNodes", "blueprintKnowledgeNodes", "blueprintGuardrailNodes", "blueprintToolNodes", "blueprintZonesExpanded", "prompt", "model", "knowledge", "guardrail", "tool", "constructor", "group", "isConversational", "isUseTemplate", "ngOnInit", "console", "log", "crypto", "randomUUID", "params", "subscribe", "queryParams", "loadAgentData", "from", "text", "ngOnDestroy", "next", "complete", "clearInterval", "onTabChange", "event", "getCollaborativeAgentDetailsById", "response", "handleAgentDataResponse", "error", "getAgentById", "agentData", "agentDetails", "Array", "isArray", "length", "data", "name", "description", "useCaseName", "useCaseCode", "organizationPath", "loadAgentNodes", "mapAgentConfigurationToBlueprint", "handleChatMessage", "message", "showAgentError", "displayMessage", "fileNames", "map", "file", "documentName", "join", "get", "value", "agentMode", "useCaseIdentifier", "orgPath", "buildOrganizationPath", "agentIdentifier", "processAgentFilesAndSendMessage", "sendAgentMessageToAPI", "payload", "Number", "user", "getDaUsername", "userInputs", "question", "fileWrapper", "submitAgentExecuteWithFile", "pipe", "res", "handleAgentExecuteResponse", "err", "handleAgentExecuteError", "submitAgentExecute", "onPromptChanged", "String", "onPlaygroundConversationalToggle", "setValue", "onPlaygroundTemplateToggle", "onFilesSelected", "files", "onApprovalRequested", "saveLogs", "exportResults", "section", "handleControlAction", "action", "navigateBack", "navigate", "mode", "editAgent", "navigateToAgentsList", "toggleLeftPanel", "setActiveRightTab", "tab", "toggleBlueprintZone", "zoneType", "isBlueprintZoneExpanded", "getMetadataFromNavbar", "filter", "m", "errorMessageText", "errorMessage", "timestamp", "Date", "push", "ai", "outputRaw", "agentResponse", "agent", "output", "formattedOutput", "replace", "detail", "successMessage", "formData", "FormData", "for<PERSON>ach", "fileData", "append", "has", "getFileToContent", "fileResponse", "fileContent", "fileResponses", "sendAgentMessageToAPIWithFiles", "currentChatPayloadLength", "content", "role", "levelId", "generatePrompt", "generatedResponse", "choices", "aiResponseText", "warn", "pop", "fileContents", "Object", "keys", "nodeCounter", "mapIndividualAgentToBlueprint", "mapCollaborativeAgentToBlueprint", "totalNodes", "config", "config<PERSON><PERSON><PERSON>", "useCaseDetails", "type", "category", "categoryIndex", "categoryId", "categoryName", "configItem", "itemIndex", "config<PERSON><PERSON>", "config<PERSON><PERSON><PERSON>", "kbValue", "toString", "kbIds", "split", "trim", "kbId", "key", "startsWith", "guardrailName", "promptNodes", "modelNodes", "knowledgeNodes", "guardrailNodes", "totalRequired", "currentRequired", "Math", "round", "shouldCreatePromptNode", "goal", "promptNodeName", "substring", "modelReferences", "hasAgentConfigs", "agentConfigs", "modelName", "modelDetails", "modelRef", "modelRefs", "ref", "modelId", "modelDeploymentName", "knowledgeReferences", "knowledgeBaseRef", "kbRefs", "knowledgeBaseId", "knowledgeBase", "kbRef", "collectionName", "indexCollectionName", "kbName", "toolReferences", "userToolReferences", "agentConfigsContent", "hasTools", "tools", "toolsContent", "hasUserTools", "userTools", "userToolsContent", "toolRef", "toolRefs", "toolId", "userToolRef", "userToolRefs", "toolName", "userTool", "userToolId", "userToolName", "toolNodes", "promptCount", "modelCount", "knowledgeCount", "toolCount", "guardrailCount", "isCollaborative", "hasToolNodes", "toolNodeNames", "t", "__decorate", "static", "selector", "standalone", "imports", "templateUrl", "styleUrls"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\agents\\agent-execution\\agent-execution.component.ts"], "sourcesContent": ["import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Subject, takeUntil, switchMap, finalize, catchError, of } from 'rxjs';\r\nimport { FormBuilder, FormGroup, FormsModule } from '@angular/forms';\r\n\r\n// Import child components\r\nimport { AgentExecutionPlaygroundComponent } from './components/agent-execution-playground/agent-execution-playground.component';\r\nimport { ChatMessage } from '@shared/components/chat-window/chat-window.component';\r\n\r\n// Extended interface for enhanced chat features\r\ninterface EnhancedChatMessage extends ChatMessage {\r\n  isLoading?: boolean;\r\n  status?: 'success' | 'failed' | 'loading';\r\n  timestamp?: Date;\r\n}\r\nimport { IconComponent, TabItem, DropdownOption } from '@ava/play-comp-library';\r\nimport { AgentServiceService } from '../services/agent-service.service';\r\nimport { AgentPlaygroundService } from '../build-agents/services/agent-playground.service';\r\nimport { environment } from '@shared/environments/environment';\r\nimport { TokenStorageService } from '@shared/auth/services/token-storage.service';\r\nimport { LoaderService } from '@shared/services/loader/loader.service';\r\nimport { ToolExecutionService } from '@shared/services/tool-execution/tool-execution.service';\r\nimport { AvaTab } from '@shared/models/tab.model';\r\nimport { ExecutionStatus, ActivityLog, ExecutionDetails, OutputItem } from '@shared/models/execution.model';\r\n\r\n// Remove duplicate definitions - they're now in shared models\r\n\r\n@Component({\r\n  selector: 'app-agent-execution',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    AgentExecutionPlaygroundComponent,\r\n    IconComponent,\r\n  ],\r\n  templateUrl: './agent-execution.component.html',\r\n  styleUrls: ['./agent-execution.component.scss'],\r\n})\r\nexport class AgentExecutionComponent implements OnInit, OnDestroy {\r\n  navigationTabs: TabItem[] = [\r\n    { id: 'nav-home', label: 'Agent Activity' },\r\n    { id: 'nav-products', label: 'Agent Output' },\r\n    { id: 'nav-services', label: 'Preview', disabled: true },\r\n  ];\r\n\r\n  // Agent details\r\n  agentId: string | null = null;\r\n  agentType: string = 'individual';\r\n  agentName: string = 'Agent';\r\n  agentDetail: string = '';\r\n\r\n  @ViewChild(AgentExecutionPlaygroundComponent, { static: false })\r\n  playgroundComp!: AgentExecutionPlaygroundComponent;\r\n\r\n  // Activity logs\r\n  activityLogs: ActivityLog[] = [];\r\n  activityProgress: number = 0;\r\n  executionDetails?: ExecutionDetails;\r\n  isRunning: boolean = false;\r\n  status: ExecutionStatus = ExecutionStatus.notStarted;\r\n\r\n  // Chat messages\r\n  chatMessages: EnhancedChatMessage[] = [];\r\n  isProcessingChat: boolean = false;\r\n  inputText = '';\r\n  \r\n  // Enhanced chat interface properties\r\n  chatHistory: any[] = []; // Store all chat interactions with status\r\n  currentChatStatus: 'success' | 'failed' | 'loading' | null = null;\r\n  showStatusMessage: boolean = false;\r\n\r\n  // Agent outputs\r\n  agentOutputs: OutputItem[] = [];\r\n  latestAgentResponse: any = null; // Store the latest agent response for display\r\n  public agentForm!: FormGroup;\r\n  public fileType: string =\r\n    '.pdf,.doc,.docx,.txt,.csv,.xlsx,.xls,.ppt,.pptx,.png,.jpg,.jpeg,.gif,.bmp,.svg';\r\n\r\n  // Execution state\r\n  executionStartTime: Date | null = null;\r\n  executionCompleted: boolean = false;\r\n  executionId!: string;\r\n\r\n  enableStreamingLog = environment.enableLogStreaming || 'all';\r\n\r\n  public isExecutionComplete: boolean = false;\r\n  progressInterval: any;\r\n\r\n  private destroy$ = new Subject<void>();\r\n  selectedTab: string = 'Agent Activity';\r\n  demoTabs: AvaTab[] = [\r\n    { id: 'tab1', label: 'History' },\r\n    { id: 'tab2', label: 'Blueprint' },\r\n    { id: 'tab3', label: 'Agent Output' },\r\n  ];\r\n\r\n  errorMsg = false;\r\n  resMessage: any;\r\n  taskMessage: any[] = [];\r\n  isJsonValid = false;\r\n  disableChat: boolean = false;\r\n  selectedFiles: File[] = [];\r\n  agentNodes: any[] = [];\r\n  userInputList: any[] = [];\r\n  progress = 0;\r\n  isLoading = false;\r\n  loaderColor: string = '';\r\n\r\n  inputFieldOrder: string[] = [];\r\n  currentInputIndex: number = 0;\r\n  activeTabId: string = 'nav-home';\r\n\r\n  // Panel state properties\r\n  isLeftPanelCollapsed: boolean = false;\r\n  activeRightTab: string = 'blueprint';\r\n\r\n  // Agent-specific properties\r\n  currentAgentDetails: any = null;\r\n  buildAgentNodes: any[] = [];\r\n  canvasNodes: any[] = [];\r\n  canvasEdges: any[] = [];\r\n  selectedPrompt: string = '';\r\n  selectedAgentMode: string = '';\r\n  selectedUseCaseIdentifier: string = '';\r\n  agentFilesUploadedData: any[] = [];\r\n  agentAttachment: string[] = [];\r\n  isAgentPlaygroundLoading = false;\r\n  agentPlaygroundDestroy = new Subject<boolean>();\r\n  agentChatPayload: any[] = [];\r\n  agentCode: string = '';\r\n  promptOptions: DropdownOption[] = [];\r\n\r\n  // Custom Blueprint Display Properties\r\n  blueprintCompletionPercentage: number = 0;\r\n  blueprintPromptNodes: any[] = [];\r\n  blueprintModelNodes: any[] = [];\r\n  blueprintKnowledgeNodes: any[] = [];\r\n  blueprintGuardrailNodes: any[] = [];\r\n  blueprintToolNodes: any[] = [];\r\n\r\n  // Blueprint zone expansion state\r\n  private blueprintZonesExpanded: { [key: string]: boolean } = {\r\n    prompt: true,\r\n    model: true,\r\n    knowledge: true,\r\n    guardrail: true,\r\n    tool: true,\r\n  };\r\n\r\n  // Blueprint panel properties (using existing arrays above)\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private agentService: AgentServiceService,\r\n    private agentPlaygroundService: AgentPlaygroundService,\r\n    private tokenStorage: TokenStorageService,\r\n    private loaderService: LoaderService,\r\n    private formBuilder: FormBuilder,\r\n    private toolExecutionService: ToolExecutionService,\r\n  ) {\r\n    this.agentForm = this.formBuilder.group({\r\n      isConversational: [true],\r\n      isUseTemplate: [false],\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    console.log('🌟 SHARED COMPONENT INITIALIZED! 🌟');\r\n    this.executionId = crypto.randomUUID();\r\n\r\n    this.route.params.subscribe((params) => {\r\n      this.agentType = params['type'] || 'individual';\r\n      console.log('🌟 SHARED: Agent type set to:', this.agentType);\r\n    });\r\n\r\n    this.route.queryParams.subscribe((params) => {\r\n      if (params['id']) {\r\n        this.agentId = params['id'];\r\n        this.loadAgentData(params['id']);\r\n      }\r\n    });\r\n\r\n    // Initialize chat messages\r\n    this.chatMessages = [\r\n      {\r\n        from: 'ai',\r\n        text: `Hi there! I am ${this.agentName || 'your agent'}. How can I help you today?`,\r\n      },\r\n    ];\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n    if (this.progressInterval) {\r\n      clearInterval(this.progressInterval);\r\n    }\r\n  }\r\n\r\n  onTabChange(event: { id: string; label: string }) {\r\n    this.activeTabId = event.id;\r\n    this.selectedTab = event.label;\r\n  }\r\n\r\n  loadAgentData(agentId: string): void {\r\n    this.isLoading = true;\r\n\r\n    // Load agent data based on type\r\n    if (this.agentType === 'collaborative') {\r\n      this.agentService.getCollaborativeAgentDetailsById(agentId).subscribe({\r\n        next: (response: any) => {\r\n          this.handleAgentDataResponse(response);\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error loading collaborative agent:', error);\r\n          this.isLoading = false;\r\n        },\r\n      });\r\n    } else {\r\n      this.agentService.getAgentById(agentId).subscribe({\r\n        next: (response: any) => {\r\n          this.handleAgentDataResponse(response);\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error loading individual agent:', error);\r\n          this.isLoading = false;\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  private handleAgentDataResponse(response: any): void {\r\n    this.isLoading = false;\r\n\r\n    // Extract agent details\r\n    let agentData;\r\n    if (\r\n      response.agentDetails &&\r\n      Array.isArray(response.agentDetails) &&\r\n      response.agentDetails.length > 0\r\n    ) {\r\n      agentData = response.agentDetails[0];\r\n    } else if (response.agentDetail) {\r\n      agentData = response.agentDetail;\r\n    } else if (response.data) {\r\n      agentData = response.data;\r\n    } else {\r\n      agentData = response;\r\n    }\r\n\r\n    if (agentData) {\r\n      this.currentAgentDetails = agentData;\r\n      this.agentName = agentData.name || agentData.agentName || 'Agent';\r\n      this.agentDetail = agentData.description || agentData.agentDetail || '';\r\n\r\n      // For individual agents, set up the required properties for playground functionality\r\n      if (this.agentType === 'individual') {\r\n        // Set selectedPrompt to agent name or useCaseName to bypass dropdown requirement\r\n        this.selectedPrompt =\r\n          agentData.useCaseName || agentData.name || 'loaded-agent';\r\n\r\n        // Set selectedAgentMode for API calls - use useCaseCode if available\r\n        this.selectedAgentMode =\r\n          agentData.useCaseCode ||\r\n          agentData.useCaseName ||\r\n          agentData.name ||\r\n          '';\r\n\r\n        // Set useCaseIdentifier - use organizationPath if available\r\n        if (agentData.organizationPath) {\r\n          this.selectedUseCaseIdentifier = agentData.organizationPath;\r\n        } else if (agentData.useCaseCode) {\r\n          this.selectedUseCaseIdentifier = agentData.useCaseCode;\r\n        } else if (agentData.useCaseName) {\r\n          this.selectedUseCaseIdentifier = agentData.useCaseName;\r\n        }\r\n      }\r\n\r\n      // Update chat message with agent name\r\n      if (this.chatMessages.length > 0) {\r\n        this.chatMessages[0].text = `Hi there! I am ${this.agentName}. How can I help you today?`;\r\n      }\r\n\r\n      // Load agent nodes and configuration\r\n      this.loadAgentNodes(agentData);\r\n    }\r\n  }\r\n\r\n  private loadAgentNodes(agentData: any): void {\r\n    // Map agent configuration to blueprint panel\r\n    this.mapAgentConfigurationToBlueprint(agentData);\r\n  }\r\n\r\n  handleChatMessage(message: string): void {\r\n    if (this.agentType === 'individual') {\r\n      // For individual agents, use the loaded agent details instead of requiring dropdown selection\r\n      if (\r\n        !this.currentAgentDetails &&\r\n        (!this.selectedPrompt || this.selectedPrompt === 'default')\r\n      ) {\r\n        this.showAgentError(\r\n          'Agent details are not loaded. Please try refreshing the page.',\r\n        );\r\n        return;\r\n      }\r\n\r\n      let displayMessage = message;\r\n      if (this.agentFilesUploadedData.length > 0) {\r\n        const fileNames = this.agentFilesUploadedData\r\n          .map((file) => file.documentName)\r\n          .join(', ');\r\n        displayMessage = `${message}\\n\\n📎 Attached files: ${fileNames}`;\r\n      }\r\n\r\n      // Add user message to chat\r\n      this.chatMessages = [\r\n        ...this.chatMessages,\r\n        { from: 'user', text: displayMessage },\r\n      ];\r\n      \r\n      // Set loading state\r\n      this.isProcessingChat = true;\r\n      this.currentChatStatus = 'loading';\r\n      this.showStatusMessage = false;\r\n      \r\n      // Add loading message\r\n      this.chatMessages = [\r\n        ...this.chatMessages,\r\n        { from: 'ai', text: '...', isLoading: true },\r\n      ];\r\n\r\n      const isConversational =\r\n        this.agentForm.get('isConversational')?.value || false;\r\n      const isUseTemplate = this.agentForm.get('isUseTemplate')?.value || false;\r\n\r\n      console.log(\r\n        'Chat message handling - isConversational:',\r\n        isConversational,\r\n        'isUseTemplate:',\r\n        isUseTemplate,\r\n      );\r\n\r\n      // Use agent details from the loaded agent data instead of dropdown selection\r\n      // Mode should be the useCaseCode, not useCaseName\r\n      const agentMode =\r\n        this.agentCode ||\r\n        this.selectedAgentMode ||\r\n        this.currentAgentDetails?.useCaseCode ||\r\n        this.currentAgentDetails?.useCaseName ||\r\n        this.currentAgentDetails?.name ||\r\n        this.selectedPrompt;\r\n\r\n      let useCaseIdentifier = this.selectedUseCaseIdentifier;\r\n      if (!useCaseIdentifier) {\r\n        // Use organizationPath if available, otherwise build from agent details\r\n        if (this.currentAgentDetails?.organizationPath) {\r\n          useCaseIdentifier = this.currentAgentDetails.organizationPath;\r\n        } else {\r\n          const orgPath = this.buildOrganizationPath();\r\n          const agentIdentifier =\r\n            this.currentAgentDetails?.useCaseCode ||\r\n            this.currentAgentDetails?.useCaseName ||\r\n            this.currentAgentDetails?.name ||\r\n            agentMode;\r\n          useCaseIdentifier = `${agentIdentifier}${orgPath}`;\r\n        }\r\n      }\r\n\r\n      if (this.agentFilesUploadedData.length > 0) {\r\n        this.processAgentFilesAndSendMessage(\r\n          message,\r\n          agentMode,\r\n          useCaseIdentifier,\r\n          isConversational,\r\n          isUseTemplate,\r\n        );\r\n        return;\r\n      }\r\n\r\n      this.sendAgentMessageToAPI(\r\n        message,\r\n        agentMode,\r\n        useCaseIdentifier,\r\n        isConversational,\r\n        isUseTemplate,\r\n      );\r\n    } else if (this.agentType === 'collaborative') {\r\n      this.isProcessingChat = true;\r\n      let payload = {\r\n        executionId: this.executionId,\r\n        agentId: Number(this.agentId),\r\n        user: this.tokenStorage.getDaUsername() || '<EMAIL>',\r\n        userInputs: { question: message },\r\n      };\r\n\r\n      if (this.agentFilesUploadedData.length > 0) {\r\n        const fileWrapper = this.agentFilesUploadedData[0];\r\n        let displayMessage: string;\r\n        if (this.agentFilesUploadedData.length > 0) {\r\n          const fileNames = this.agentFilesUploadedData\r\n            .map((file) => file.documentName)\r\n            .join(', ');\r\n          displayMessage = `📎 Attached files: ${fileNames}`;\r\n\r\n          this.chatMessages = [{ from: 'user', text: displayMessage }];\r\n        }\r\n        this.agentPlaygroundService\r\n          .submitAgentExecuteWithFile(payload, fileWrapper)\r\n          .pipe(\r\n            finalize(() => {\r\n              this.isProcessingChat = false;\r\n              this.isAgentPlaygroundLoading = false;\r\n            }),\r\n            takeUntil(this.agentPlaygroundDestroy),\r\n          )\r\n          .subscribe({\r\n            next: (res) => this.handleAgentExecuteResponse(res, message),\r\n            error: (err: any) => this.handleAgentExecuteError(err, message),\r\n          });\r\n      } else {\r\n        this.agentPlaygroundService\r\n          .submitAgentExecute(payload)\r\n          .pipe(\r\n            finalize(() => {\r\n              this.isProcessingChat = false;\r\n              this.isAgentPlaygroundLoading = false;\r\n            }),\r\n            takeUntil(this.agentPlaygroundDestroy),\r\n          )\r\n          .subscribe({\r\n            next: (res) => this.handleAgentExecuteResponse(res, message),\r\n            error: (err: any) => this.handleAgentExecuteError(err, message),\r\n          });\r\n      }\r\n    }\r\n  }\r\n\r\n  onPromptChanged(prompt: DropdownOption): void {\r\n    this.inputText = prompt.name || String(prompt.value) || '';\r\n  }\r\n\r\n  onPlaygroundConversationalToggle(value: boolean): void {\r\n    // Update the form control\r\n    this.agentForm.get('isConversational')?.setValue(value);\r\n\r\n    // When conversational mode is turned off, clear the conversation history\r\n    // This ensures that the next message will be treated as a fresh start\r\n    if (!value) {\r\n      this.agentChatPayload = [];\r\n      console.log(\r\n        'Conversational mode disabled - cleared chat payload history',\r\n      );\r\n    } else {\r\n      console.log('Conversational mode enabled - will maintain chat history');\r\n    }\r\n  }\r\n\r\n  onPlaygroundTemplateToggle(value: boolean): void {\r\n    // Update the form control\r\n    this.agentForm.get('isUseTemplate')?.setValue(value);\r\n    console.log('Template mode toggled:', value);\r\n  }\r\n\r\n  onFilesSelected(files: any[]): void {\r\n    this.selectedFiles = files;\r\n    // Update agentFilesUploadedData for agent execution\r\n    this.agentFilesUploadedData = files;\r\n  }\r\n\r\n  onApprovalRequested(): void {\r\n    // Handle approval request\r\n  }\r\n\r\n  saveLogs(): void {\r\n    // Save execution logs\r\n  }\r\n\r\n  exportResults(section: 'activity' | 'output'): void {\r\n    // Export results\r\n  }\r\n\r\n  handleControlAction(action: 'play' | 'pause' | 'stop'): void {\r\n    // Handle execution control actions\r\n  }\r\n\r\n  navigateBack(): void {\r\n    this.router.navigate(['/build/agents', this.agentType], {\r\n      queryParams: { id: this.agentId, mode: 'view' },\r\n    });\r\n  }\r\n\r\n  editAgent(): void {\r\n    this.router.navigate(['/build/agents', this.agentType], {\r\n      queryParams: { id: this.agentId, mode: 'edit' },\r\n    });\r\n  }\r\n\r\n  navigateToAgentsList(): void {\r\n    this.router.navigate(['/build/agents']);\r\n  }\r\n\r\n  toggleLeftPanel(): void {\r\n    this.isLeftPanelCollapsed = !this.isLeftPanelCollapsed;\r\n  }\r\n\r\n  setActiveRightTab(tab: string): void {\r\n    this.activeRightTab = tab;\r\n  }\r\n\r\n  // Blueprint zone management methods\r\n  toggleBlueprintZone(zoneType: string): void {\r\n    this.blueprintZonesExpanded[zoneType] =\r\n      !this.blueprintZonesExpanded[zoneType];\r\n  }\r\n\r\n  isBlueprintZoneExpanded(zoneType: string): boolean {\r\n    return this.blueprintZonesExpanded[zoneType] || false;\r\n  }\r\n\r\n  // API and helper methods from build-agents component\r\n  private showAgentError(message: string): void {\r\n    this.chatMessages = [...this.chatMessages, { from: 'ai', text: message }];\r\n  }\r\n\r\n  private buildOrganizationPath(): string {\r\n    // Simple implementation - in real scenario this would be from navbar/metadata\r\n    return '';\r\n  }\r\n\r\n  private getMetadataFromNavbar(): { levelId?: number } {\r\n    // Simple implementation - in real scenario this would get org level mapping\r\n    return {};\r\n  }\r\n\r\n  private handleAgentExecuteError(err: any, message: string): void {\r\n    // Remove loading message\r\n    this.chatMessages = this.chatMessages.filter(m => !m.isLoading);\r\n\r\n    const errorMessageText = err?.error?.message || err?.message || 'Something went wrong.';\r\n    const errorMessage: EnhancedChatMessage = {\r\n      from: 'ai',\r\n      text: errorMessageText,\r\n      status: 'failed',\r\n      timestamp: new Date()\r\n    };\r\n    this.chatMessages.push(errorMessage);\r\n\r\n    // Update history and status\r\n    this.chatHistory.push({ user: message, ai: errorMessage });\r\n    this.currentChatStatus = 'failed';\r\n    this.isProcessingChat = false;\r\n    this.showStatusMessage = true;\r\n    this.setActiveRightTab('output'); // Switch to output tab\r\n  }\r\n\r\n  handleAgentExecuteResponse(response: any, message: string): void {\r\n    // Remove loading message\r\n    this.chatMessages = this.chatMessages.filter(m => !m.isLoading);\r\n\r\n    try {\r\n      this.latestAgentResponse = response;\r\n      const outputRaw = response?.agentResponse?.agent?.output;\r\n      let formattedOutput = '';\r\n\r\n      if (outputRaw) {\r\n        formattedOutput = outputRaw.replace(/\\\\n/g, '\\n');\r\n      } else {\r\n        formattedOutput = response?.agentResponse?.detail || 'No response from agent.';\r\n      }\r\n\r\n      // Update chat with success message\r\n      const successMessage: EnhancedChatMessage = {\r\n        from: 'ai',\r\n        text: formattedOutput,\r\n        status: 'success',\r\n        timestamp: new Date()\r\n      };\r\n      this.chatMessages.push(successMessage);\r\n\r\n      // Update history and status\r\n      this.chatHistory.push({ user: message, ai: successMessage });\r\n      this.currentChatStatus = 'success';\r\n\r\n    } catch (err: any) {\r\n      const errorMessageText = err?.message || 'Agent response could not be processed.';\r\n      const errorMessage: EnhancedChatMessage = {\r\n        from: 'ai',\r\n        text: errorMessageText,\r\n        status: 'failed',\r\n        timestamp: new Date()\r\n      };\r\n      this.chatMessages.push(errorMessage);\r\n\r\n      // Update history and status for error\r\n      this.chatHistory.push({ user: message, ai: errorMessage });\r\n      this.currentChatStatus = 'failed';\r\n    }\r\n    finally {\r\n        this.isProcessingChat = false;\r\n        this.showStatusMessage = true;\r\n        this.setActiveRightTab('output'); // Switch to output tab\r\n    }\r\n  }\r\n\r\n  private processAgentFilesAndSendMessage(\r\n    message: string,\r\n    mode: string,\r\n    useCaseIdentifier: string,\r\n    isConversational: boolean,\r\n    isUseTemplate: boolean,\r\n  ): void {\r\n    const formData = new FormData();\r\n    this.agentFilesUploadedData.forEach((fileData) => {\r\n      if (fileData.file) {\r\n        formData.append('files', fileData.file);\r\n      }\r\n    });\r\n\r\n    if (formData.has('files')) {\r\n      this.agentPlaygroundService\r\n        .getFileToContent(formData)\r\n        .pipe(\r\n          switchMap((fileResponse) => {\r\n            const fileContent =\r\n              fileResponse?.fileResponses\r\n                ?.map((response: any) => response.fileContent)\r\n                ?.join('\\n') || '';\r\n            this.sendAgentMessageToAPIWithFiles(\r\n              message,\r\n              mode,\r\n              useCaseIdentifier,\r\n              isConversational,\r\n              isUseTemplate,\r\n              fileContent,\r\n            );\r\n            return of(null);\r\n          }),\r\n          catchError((error) => {\r\n            console.error('Error parsing files:', error);\r\n            this.sendAgentMessageToAPI(\r\n              message,\r\n              mode,\r\n              useCaseIdentifier,\r\n              isConversational,\r\n              isUseTemplate,\r\n            );\r\n            return of(null);\r\n          }),\r\n        )\r\n        .subscribe();\r\n    } else {\r\n      this.sendAgentMessageToAPI(\r\n        message,\r\n        mode,\r\n        useCaseIdentifier,\r\n        isConversational,\r\n        isUseTemplate,\r\n      );\r\n    }\r\n  }\r\n\r\n  private sendAgentMessageToAPI(\r\n    message: string,\r\n    mode: string,\r\n    useCaseIdentifier: string,\r\n    isConversational: boolean,\r\n    isUseTemplate: boolean,\r\n  ): void {\r\n    console.log('API Call Parameters:', {\r\n      message,\r\n      mode,\r\n      useCaseIdentifier,\r\n      isConversational,\r\n      isUseTemplate,\r\n      currentChatPayloadLength: this.agentChatPayload.length,\r\n    });\r\n\r\n    if (isConversational) {\r\n      this.agentChatPayload.push({ content: message, role: 'user' });\r\n    }\r\n\r\n    const payload = isConversational ? this.agentChatPayload : message;\r\n    const { levelId } = this.getMetadataFromNavbar();\r\n\r\n    console.log('Final payload being sent:', payload);\r\n\r\n    this.agentPlaygroundService\r\n      .generatePrompt(\r\n        payload,\r\n        mode,\r\n        isConversational,\r\n        isUseTemplate,\r\n        this.agentAttachment,\r\n        useCaseIdentifier,\r\n        '',\r\n        levelId,\r\n      )\r\n      .pipe(\r\n        finalize(() => {\r\n          this.isProcessingChat = false;\r\n          this.isAgentPlaygroundLoading = false;\r\n        }),\r\n        takeUntil(this.agentPlaygroundDestroy),\r\n      )\r\n      .subscribe({\r\n        next: (generatedResponse: any) => {\r\n          // Store the latest response for display in the output panel\r\n          this.latestAgentResponse = generatedResponse;\r\n\r\n          if (\r\n            generatedResponse?.response &&\r\n            generatedResponse?.response?.choices\r\n          ) {\r\n            const aiResponseText = generatedResponse.response.choices[0].text;\r\n            this.chatMessages = [\r\n              ...this.chatMessages,\r\n              { from: 'ai', text: aiResponseText },\r\n            ];\r\n            if (isConversational) {\r\n              this.agentChatPayload.push({\r\n                content: aiResponseText,\r\n                role: 'assistant',\r\n              });\r\n            }\r\n          } else {\r\n            console.warn('Unexpected API response format:', generatedResponse);\r\n            this.showAgentError(\r\n              'Received unexpected response format from API.',\r\n            );\r\n          }\r\n        },\r\n        error: (error: any) => {\r\n          console.error('API Error:', error);\r\n          const errorMessage =\r\n            error?.error?.message ||\r\n            'An error occurred while processing your request.';\r\n          this.showAgentError(errorMessage);\r\n          if (isConversational && this.agentChatPayload.length > 0) {\r\n            this.agentChatPayload.pop();\r\n          }\r\n        },\r\n      });\r\n  }\r\n\r\n  private sendAgentMessageToAPIWithFiles(\r\n    message: string,\r\n    mode: string,\r\n    useCaseIdentifier: string,\r\n    isConversational: boolean,\r\n    isUseTemplate: boolean,\r\n    fileContents: string,\r\n  ): void {\r\n    if (isConversational) {\r\n      this.agentChatPayload.push({ content: message, role: 'user' });\r\n    }\r\n    const payload = isConversational ? this.agentChatPayload : message;\r\n    const { levelId } = this.getMetadataFromNavbar();\r\n\r\n    this.agentPlaygroundService\r\n      .generatePrompt(\r\n        payload,\r\n        mode,\r\n        isConversational,\r\n        isUseTemplate,\r\n        this.agentAttachment,\r\n        useCaseIdentifier,\r\n        fileContents,\r\n        levelId,\r\n      )\r\n      .pipe(\r\n        finalize(() => {\r\n          this.isProcessingChat = false;\r\n          this.isAgentPlaygroundLoading = false;\r\n        }),\r\n        takeUntil(this.agentPlaygroundDestroy),\r\n      )\r\n      .subscribe({\r\n        next: (generatedResponse: any) => {\r\n          if (\r\n            generatedResponse?.response &&\r\n            generatedResponse?.response?.choices\r\n          ) {\r\n            const aiResponseText = generatedResponse.response.choices[0].text;\r\n            this.chatMessages = [\r\n              ...this.chatMessages,\r\n              { from: 'ai', text: aiResponseText },\r\n            ];\r\n            if (isConversational) {\r\n              this.agentChatPayload.push({\r\n                content: aiResponseText,\r\n                role: 'assistant',\r\n              });\r\n            }\r\n          } else {\r\n            console.warn('Unexpected API response format:', generatedResponse);\r\n            this.showAgentError(\r\n              'Received unexpected response format from API.',\r\n            );\r\n          }\r\n        },\r\n        error: (error: any) => {\r\n          console.error('API Error:', error);\r\n          const errorMessage =\r\n            error?.error?.message ||\r\n            'An error occurred while processing your request.';\r\n          this.showAgentError(errorMessage);\r\n          if (isConversational && this.agentChatPayload.length > 0) {\r\n            this.agentChatPayload.pop();\r\n          }\r\n        },\r\n      });\r\n  }\r\n\r\n  // Blueprint panel methods\r\n  private mapAgentConfigurationToBlueprint(agentData: any): void {\r\n    if (!agentData) {\r\n      console.warn('No agent data provided for blueprint');\r\n      return;\r\n    }\r\n\r\n    console.log('🔍 DEBUG: Full agent data received:', agentData);\r\n    console.log('🔍 DEBUG: Agent type:', this.agentType);\r\n    console.log('🔍 DEBUG: Agent data keys:', Object.keys(agentData));\r\n\r\n    // Clear existing nodes\r\n    this.buildAgentNodes = [];\r\n    this.canvasNodes = [];\r\n\r\n    let nodeCounter = 1;\r\n\r\n    // Map agent configuration to nodes based on agent type\r\n    if (this.agentType === 'individual') {\r\n      this.mapIndividualAgentToBlueprint(agentData, nodeCounter);\r\n    } else if (this.agentType === 'collaborative') {\r\n      this.mapCollaborativeAgentToBlueprint(agentData, nodeCounter);\r\n    }\r\n\r\n    console.log('🎯 Blueprint nodes mapped:', {\r\n      buildAgentNodes: this.buildAgentNodes,\r\n      canvasNodes: this.canvasNodes,\r\n      totalNodes: this.buildAgentNodes.length,\r\n    });\r\n  }\r\n\r\n  private mapIndividualAgentToBlueprint(\r\n    agentData: any,\r\n    nodeCounter: number,\r\n  ): void {\r\n    console.log('🔍 Individual agent mapping - checking fields:', {\r\n      config: agentData.config,\r\n      configLength: agentData.config?.length,\r\n      useCaseName: agentData.useCaseName,\r\n      prompt: agentData.prompt,\r\n      useCaseDetails: agentData.useCaseDetails,\r\n    });\r\n\r\n    // Clear existing blueprint nodes\r\n    this.blueprintPromptNodes = [];\r\n    this.blueprintModelNodes = [];\r\n    this.blueprintKnowledgeNodes = [];\r\n    this.blueprintGuardrailNodes = [];\r\n\r\n    // Add prompt node from \"prompt\" field\r\n    if (agentData.prompt) {\r\n      this.blueprintPromptNodes.push({\r\n        id: `prompt-${nodeCounter++}`,\r\n        name: agentData.prompt,\r\n        type: 'prompt',\r\n      });\r\n      console.log('✅ Added prompt node:', agentData.prompt);\r\n    }\r\n\r\n    // Process the config array to extract model, knowledge bases, and guardrails\r\n    if (agentData.config && Array.isArray(agentData.config)) {\r\n      console.log(\r\n        '🔍 Processing config array with length:',\r\n        agentData.config.length,\r\n      );\r\n\r\n      agentData.config.forEach((category: any, categoryIndex: number) => {\r\n        console.log(\r\n          `🔍 Category ${categoryIndex} (ID: ${category.categoryId}):`,\r\n          category.categoryName,\r\n        );\r\n\r\n        if (category.config && Array.isArray(category.config)) {\r\n          console.log(\r\n            `🔍 Category ${categoryIndex} has ${category.config.length} config items`,\r\n          );\r\n\r\n          category.config.forEach((configItem: any, itemIndex: number) => {\r\n            console.log(`🔍 Config item ${categoryIndex}-${itemIndex}:`, {\r\n              configKey: configItem.configKey,\r\n              configValue: configItem.configValue,\r\n              categoryId: configItem.categoryId,\r\n            });\r\n\r\n            // Handle AI Model from categoryId 1\r\n            if (\r\n              configItem.categoryId === 1 &&\r\n              configItem.configKey === 'MODEL' &&\r\n              configItem.configValue\r\n            ) {\r\n              console.log(\r\n                '✅ Adding AI model node from categoryId 1:',\r\n                configItem.configValue,\r\n              );\r\n              this.blueprintModelNodes.push({\r\n                id: `model-${nodeCounter++}`,\r\n                name: `${configItem.configKey}`,\r\n                type: 'model',\r\n              });\r\n            }\r\n\r\n            // Handle Knowledge Base from categoryId 2\r\n            if (\r\n              configItem.categoryId === 2 &&\r\n              configItem.configKey === 'RAG_KNOWLEDGEBASE_NAME' &&\r\n              configItem.configValue\r\n            ) {\r\n              console.log(\r\n                '✅ Adding knowledge base nodes from categoryId 2:',\r\n                configItem.configValue,\r\n              );\r\n              const kbValue = configItem.configValue.toString();\r\n              const kbIds = kbValue\r\n                .split(',')\r\n                .map((id: string) => id.trim())\r\n                .filter((id: string) => id);\r\n\r\n              kbIds.forEach((kbId: string) => {\r\n                this.blueprintKnowledgeNodes.push({\r\n                  id: `knowledge-${nodeCounter++}`,\r\n                  name: `Knowledge Base: ${kbId}`,\r\n                  type: 'knowledge',\r\n                });\r\n              });\r\n            }\r\n\r\n            // Handle Guardrails from categoryId 3 where configValue is true\r\n            if (\r\n              configItem.categoryId === 3 &&\r\n              configItem.configValue === 'true'\r\n            ) {\r\n              console.log('✅ Found enabled guardrail from categoryId 3:', {\r\n                key: configItem.configKey,\r\n                value: configItem.configValue,\r\n              });\r\n\r\n              if (configItem.configKey === 'ENABLE_GUARDRAILS') {\r\n                // Only add one general guardrail node if not already added\r\n                if (this.blueprintGuardrailNodes.length === 0) {\r\n                  this.blueprintGuardrailNodes.push({\r\n                    id: `guardrail-${nodeCounter++}`,\r\n                    name: 'Guardrails Enabled',\r\n                    type: 'guardrail',\r\n                  });\r\n                }\r\n              } else if (configItem.configKey.startsWith('GUARDRAIL_')) {\r\n                // Add specific guardrail nodes for enabled guardrails\r\n                let guardrailName = configItem.configKey;\r\n                if (guardrailName.startsWith('GUARDRAIL_')) {\r\n                  guardrailName = guardrailName\r\n                    .replace('GUARDRAIL_', '')\r\n                    .replace(/_/g, ' ');\r\n                }\r\n\r\n                this.blueprintGuardrailNodes.push({\r\n                  id: `guardrail-${nodeCounter++}`,\r\n                  name: `${guardrailName}`,\r\n                  type: 'guardrail',\r\n                });\r\n              }\r\n            }\r\n          });\r\n        }\r\n      });\r\n    }\r\n\r\n    console.log('🎯 Final blueprint nodes:', {\r\n      promptNodes: this.blueprintPromptNodes,\r\n      modelNodes: this.blueprintModelNodes,\r\n      knowledgeNodes: this.blueprintKnowledgeNodes,\r\n      guardrailNodes: this.blueprintGuardrailNodes,\r\n    });\r\n\r\n    // Calculate completion percentage\r\n    const totalRequired = 2; // Prompt + Model are required\r\n    const currentRequired =\r\n      this.blueprintPromptNodes.length + this.blueprintModelNodes.length;\r\n    this.blueprintCompletionPercentage = Math.round(\r\n      (currentRequired / totalRequired) * 100,\r\n    );\r\n  }\r\n\r\n  private mapCollaborativeAgentToBlueprint(\r\n    agentData: any,\r\n    nodeCounter: number,\r\n  ): void {\r\n    console.log(\r\n      '🚀 SHARED COMPONENT: mapCollaborativeAgentToBlueprint called!',\r\n    );\r\n    console.log('🔍 DEBUG: Collaborative agent data received:', agentData);\r\n    console.log(\r\n      '🔍 DEBUG: Collaborative agent data keys:',\r\n      Object.keys(agentData),\r\n    );\r\n    console.log('🔍 DEBUG: Agent type in component:', this.agentType);\r\n    console.log('🔍 DEBUG: Current nodeCounter:', nodeCounter);\r\n\r\n    // Clear existing blueprint nodes\r\n    this.blueprintPromptNodes = [];\r\n    this.blueprintModelNodes = [];\r\n    this.blueprintKnowledgeNodes = [];\r\n    this.blueprintToolNodes = [];\r\n    this.blueprintGuardrailNodes = [];\r\n\r\n    console.log('🔍 DEBUG: Cleared all blueprint node arrays');\r\n\r\n    // Add prompt node - handle different prompt structures for collaborative agents\r\n    const shouldCreatePromptNode =\r\n      agentData.goal || agentData.role || agentData.description;\r\n\r\n    console.log('🔍 DEBUG: Checking prompt node creation:', {\r\n      goal: agentData.goal,\r\n      role: agentData.role,\r\n      description: agentData.description,\r\n      shouldCreatePromptNode,\r\n    });\r\n\r\n    if (shouldCreatePromptNode) {\r\n      let promptNodeName =\r\n        agentData.goal ||\r\n        agentData.role ||\r\n        agentData.description ||\r\n        'Collaborative Agent Prompt';\r\n\r\n      // Truncate prompt if too long for display\r\n      if (promptNodeName.length > 150) {\r\n        promptNodeName = promptNodeName.substring(0, 150) + '...';\r\n      }\r\n\r\n      this.blueprintPromptNodes.push({\r\n        id: `prompt-${nodeCounter++}`,\r\n        name: promptNodeName,\r\n        type: 'prompt',\r\n      });\r\n      console.log('✅ Added collaborative prompt node:', promptNodeName);\r\n    }\r\n\r\n    // Add model nodes - handle both old and new API formats like build-agents\r\n    let modelReferences = [];\r\n\r\n    console.log('🔍 DEBUG: Checking model data:', {\r\n      hasAgentConfigs: !!agentData.agentConfigs,\r\n      agentConfigs: agentData.agentConfigs,\r\n      model: agentData.model,\r\n      modelName: agentData.modelName,\r\n      modelDetails: agentData.modelDetails,\r\n    });\r\n\r\n    // New API format: agentConfigs.modelRef (array of model IDs or objects)\r\n    if (agentData.agentConfigs && agentData.agentConfigs.modelRef) {\r\n      const modelRefs = Array.isArray(agentData.agentConfigs.modelRef)\r\n        ? agentData.agentConfigs.modelRef\r\n        : [agentData.agentConfigs.modelRef];\r\n\r\n      modelReferences = modelRefs.map((ref: any) => {\r\n        if (typeof ref === 'number' || typeof ref === 'string') {\r\n          return { modelId: ref };\r\n        }\r\n        return ref;\r\n      });\r\n    }\r\n    // Old API format: modelDetails\r\n    else if (agentData.modelDetails) {\r\n      modelReferences = [agentData.modelDetails];\r\n    }\r\n    // Fallback: check for model or modelName directly\r\n    else if (agentData.model || agentData.modelName) {\r\n      modelReferences = [{ modelId: agentData.model || agentData.modelName }];\r\n    }\r\n\r\n    modelReferences.forEach((modelRef: any) => {\r\n      const modelId = modelRef.modelId || modelRef.id;\r\n      const modelName =\r\n        modelRef.model ||\r\n        modelRef.modelDeploymentName ||\r\n        `Model ID: ${modelId}`;\r\n\r\n      this.blueprintModelNodes.push({\r\n        id: `model-${nodeCounter++}`,\r\n        name: modelName,\r\n        type: 'model',\r\n      });\r\n      console.log('✅ Added collaborative model node:', modelName);\r\n    });\r\n\r\n    // Add knowledge base nodes - handle both old and new API formats\r\n    let knowledgeReferences = [];\r\n\r\n    // New API format: agentConfigs.knowledgeBaseRef (array of KB IDs or objects)\r\n    if (agentData.agentConfigs && agentData.agentConfigs.knowledgeBaseRef) {\r\n      const kbRefs = Array.isArray(agentData.agentConfigs.knowledgeBaseRef)\r\n        ? agentData.agentConfigs.knowledgeBaseRef\r\n        : [agentData.agentConfigs.knowledgeBaseRef];\r\n\r\n      knowledgeReferences = kbRefs.map((ref: any) => {\r\n        if (typeof ref === 'number' || typeof ref === 'string') {\r\n          return { knowledgeBaseId: ref };\r\n        }\r\n        return ref;\r\n      });\r\n    }\r\n    // Old API format: knowledgeBase\r\n    else if (\r\n      agentData.knowledgeBase &&\r\n      Array.isArray(agentData.knowledgeBase)\r\n    ) {\r\n      knowledgeReferences = agentData.knowledgeBase;\r\n    }\r\n\r\n    knowledgeReferences.forEach((kbRef: any) => {\r\n      const kbId = kbRef.knowledgeBaseId || kbRef.id;\r\n      const collectionName = kbRef.indexCollectionName || kbRef.name;\r\n      const kbName = collectionName || `Knowledge Base ID: ${kbId}`;\r\n\r\n      this.blueprintKnowledgeNodes.push({\r\n        id: `knowledge-${nodeCounter++}`,\r\n        name: kbName,\r\n        type: 'knowledge',\r\n      });\r\n      console.log('✅ Added collaborative knowledge node:', kbName);\r\n    });\r\n\r\n    // Add tool nodes - handle both old and new API formats like build-agents\r\n    let toolReferences = [];\r\n    let userToolReferences = [];\r\n\r\n    console.log('🔍 DEBUG: Checking tool data:', {\r\n      hasAgentConfigs: !!agentData.agentConfigs,\r\n      agentConfigsContent: agentData.agentConfigs,\r\n      hasTools: agentData.tools,\r\n      toolsContent: agentData.tools,\r\n      hasUserTools: agentData.userTools,\r\n      userToolsContent: agentData.userTools,\r\n    });\r\n\r\n    // New API format: agentConfigs.toolRef and agentConfigs.userToolRef\r\n    if (agentData.agentConfigs) {\r\n      if (agentData.agentConfigs.toolRef) {\r\n        const toolRefs = Array.isArray(agentData.agentConfigs.toolRef)\r\n          ? agentData.agentConfigs.toolRef\r\n          : [agentData.agentConfigs.toolRef];\r\n\r\n        toolReferences = toolRefs.map((ref: any) => {\r\n          if (typeof ref === 'number' || typeof ref === 'string') {\r\n            return { toolId: ref };\r\n          }\r\n          return ref;\r\n        });\r\n      }\r\n      if (agentData.agentConfigs.userToolRef) {\r\n        const userToolRefs = Array.isArray(agentData.agentConfigs.userToolRef)\r\n          ? agentData.agentConfigs.userToolRef\r\n          : [agentData.agentConfigs.userToolRef];\r\n\r\n        userToolReferences = userToolRefs.map((ref: any) => {\r\n          if (typeof ref === 'number' || typeof ref === 'string') {\r\n            return { toolId: ref };\r\n          }\r\n          return ref;\r\n        });\r\n      }\r\n    }\r\n    // Old API format: tools and userTools\r\n    else {\r\n      if (agentData.tools && Array.isArray(agentData.tools)) {\r\n        toolReferences = agentData.tools;\r\n      }\r\n      if (agentData.userTools && Array.isArray(agentData.userTools)) {\r\n        userToolReferences = agentData.userTools;\r\n      }\r\n    }\r\n\r\n    // Process built-in tools\r\n    toolReferences.forEach((tool: any) => {\r\n      const toolId = tool.toolId || tool.id;\r\n      const toolName = tool.toolName || tool.name || `Tool ID: ${toolId}`;\r\n\r\n      this.blueprintToolNodes.push({\r\n        id: `tool-${nodeCounter++}`,\r\n        name: toolName,\r\n        type: 'tool',\r\n      });\r\n      console.log('✅ Added collaborative builtin tool node:', toolName);\r\n    });\r\n\r\n    // Process user tools\r\n    userToolReferences.forEach((userTool: any) => {\r\n      const userToolId = userTool.toolId || userTool.id;\r\n      const userToolName =\r\n        userTool.toolName || userTool.name || `User Tool ID: ${userToolId}`;\r\n\r\n      this.blueprintToolNodes.push({\r\n        id: `tool-${nodeCounter++}`,\r\n        name: userToolName,\r\n        type: 'tool',\r\n      });\r\n      console.log('✅ Added collaborative user tool node:', userToolName);\r\n    });\r\n\r\n    console.log('🎯 Final collaborative blueprint nodes:', {\r\n      promptNodes: this.blueprintPromptNodes,\r\n      modelNodes: this.blueprintModelNodes,\r\n      knowledgeNodes: this.blueprintKnowledgeNodes,\r\n      toolNodes: this.blueprintToolNodes,\r\n      guardrailNodes: this.blueprintGuardrailNodes,\r\n    });\r\n\r\n    // Debug: Check blueprint node arrays lengths\r\n    console.log('📊 Blueprint node counts:', {\r\n      promptCount: this.blueprintPromptNodes.length,\r\n      modelCount: this.blueprintModelNodes.length,\r\n      knowledgeCount: this.blueprintKnowledgeNodes.length,\r\n      toolCount: this.blueprintToolNodes.length,\r\n      guardrailCount: this.blueprintGuardrailNodes.length,\r\n    });\r\n\r\n    // Debug: Check if tools zone will be visible\r\n    console.log('🔧 Tools zone debug:', {\r\n      agentType: this.agentType,\r\n      isCollaborative: this.agentType === 'collaborative',\r\n      hasToolNodes: this.blueprintToolNodes.length > 0,\r\n      toolNodeNames: this.blueprintToolNodes.map((t) => t.name),\r\n    });\r\n\r\n    // Calculate completion percentage\r\n    const totalRequired = 2; // Prompt + Model are required\r\n    const currentRequired =\r\n      this.blueprintPromptNodes.length + this.blueprintModelNodes.length;\r\n    this.blueprintCompletionPercentage = Math.round(\r\n      (currentRequired / totalRequired) * 100,\r\n    );\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAqBC,SAAS,QAAQ,eAAe;AAEvE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,OAAO,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,EAAE,QAAQ,MAAM;AAC9E,SAAiCC,WAAW,QAAQ,gBAAgB;AAEpE;AACA,SAASC,iCAAiC,QAAQ,8EAA8E;AAShI,SAASC,aAAa,QAAiC,wBAAwB;AAG/E,SAASC,WAAW,QAAQ,kCAAkC;AAK9D,SAASC,eAAe,QAAmD,gCAAgC;AAE3G;AAcO,IAAMC,uBAAuB,GAA7B,MAAMA,uBAAuB;EAkHxBC,KAAA;EACAC,MAAA;EACAC,YAAA;EACAC,sBAAA;EACAC,YAAA;EACAC,aAAA;EACAC,WAAA;EACAC,oBAAA;EAxHVC,cAAc,GAAc,CAC1B;IAAEC,EAAE,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAgB,CAAE,EAC3C;IAAED,EAAE,EAAE,cAAc;IAAEC,KAAK,EAAE;EAAc,CAAE,EAC7C;IAAED,EAAE,EAAE,cAAc;IAAEC,KAAK,EAAE,SAAS;IAAEC,QAAQ,EAAE;EAAI,CAAE,CACzD;EAED;EACAC,OAAO,GAAkB,IAAI;EAC7BC,SAAS,GAAW,YAAY;EAChCC,SAAS,GAAW,OAAO;EAC3BC,WAAW,GAAW,EAAE;EAGxBC,cAAc;EAEd;EACAC,YAAY,GAAkB,EAAE;EAChCC,gBAAgB,GAAW,CAAC;EAC5BC,gBAAgB;EAChBC,SAAS,GAAY,KAAK;EAC1BC,MAAM,GAAoBvB,eAAe,CAACwB,UAAU;EAEpD;EACAC,YAAY,GAA0B,EAAE;EACxCC,gBAAgB,GAAY,KAAK;EACjCC,SAAS,GAAG,EAAE;EAEd;EACAC,WAAW,GAAU,EAAE,CAAC,CAAC;EACzBC,iBAAiB,GAA4C,IAAI;EACjEC,iBAAiB,GAAY,KAAK;EAElC;EACAC,YAAY,GAAiB,EAAE;EAC/BC,mBAAmB,GAAQ,IAAI,CAAC,CAAC;EAC1BC,SAAS;EACTC,QAAQ,GACb,gFAAgF;EAElF;EACAC,kBAAkB,GAAgB,IAAI;EACtCC,kBAAkB,GAAY,KAAK;EACnCC,WAAW;EAEXC,kBAAkB,GAAGvC,WAAW,CAACwC,kBAAkB,IAAI,KAAK;EAErDC,mBAAmB,GAAY,KAAK;EAC3CC,gBAAgB;EAERC,QAAQ,GAAG,IAAIpD,OAAO,EAAQ;EACtCqD,WAAW,GAAW,gBAAgB;EACtCC,QAAQ,GAAa,CACnB;IAAEjC,EAAE,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAS,CAAE,EAChC;IAAED,EAAE,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAW,CAAE,EAClC;IAAED,EAAE,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAc,CAAE,CACtC;EAEDiC,QAAQ,GAAG,KAAK;EAChBC,UAAU;EACVC,WAAW,GAAU,EAAE;EACvBC,WAAW,GAAG,KAAK;EACnBC,WAAW,GAAY,KAAK;EAC5BC,aAAa,GAAW,EAAE;EAC1BC,UAAU,GAAU,EAAE;EACtBC,aAAa,GAAU,EAAE;EACzBC,QAAQ,GAAG,CAAC;EACZC,SAAS,GAAG,KAAK;EACjBC,WAAW,GAAW,EAAE;EAExBC,eAAe,GAAa,EAAE;EAC9BC,iBAAiB,GAAW,CAAC;EAC7BC,WAAW,GAAW,UAAU;EAEhC;EACAC,oBAAoB,GAAY,KAAK;EACrCC,cAAc,GAAW,WAAW;EAEpC;EACAC,mBAAmB,GAAQ,IAAI;EAC/BC,eAAe,GAAU,EAAE;EAC3BC,WAAW,GAAU,EAAE;EACvBC,WAAW,GAAU,EAAE;EACvBC,cAAc,GAAW,EAAE;EAC3BC,iBAAiB,GAAW,EAAE;EAC9BC,yBAAyB,GAAW,EAAE;EACtCC,sBAAsB,GAAU,EAAE;EAClCC,eAAe,GAAa,EAAE;EAC9BC,wBAAwB,GAAG,KAAK;EAChCC,sBAAsB,GAAG,IAAIjF,OAAO,EAAW;EAC/CkF,gBAAgB,GAAU,EAAE;EAC5BC,SAAS,GAAW,EAAE;EACtBC,aAAa,GAAqB,EAAE;EAEpC;EACAC,6BAA6B,GAAW,CAAC;EACzCC,oBAAoB,GAAU,EAAE;EAChCC,mBAAmB,GAAU,EAAE;EAC/BC,uBAAuB,GAAU,EAAE;EACnCC,uBAAuB,GAAU,EAAE;EACnCC,kBAAkB,GAAU,EAAE;EAE9B;EACQC,sBAAsB,GAA+B;IAC3DC,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,IAAI;IACfC,SAAS,EAAE,IAAI;IACfC,IAAI,EAAE;GACP;EAED;EAEAC,YACUrF,KAAqB,EACrBC,MAAc,EACdC,YAAiC,EACjCC,sBAA8C,EAC9CC,YAAiC,EACjCC,aAA4B,EAC5BC,WAAwB,EACxBC,oBAA0C;IAP1C,KAAAP,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,sBAAsB,GAAtBA,sBAAsB;IACtB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,oBAAoB,GAApBA,oBAAoB;IAE5B,IAAI,CAACwB,SAAS,GAAG,IAAI,CAACzB,WAAW,CAACgF,KAAK,CAAC;MACtCC,gBAAgB,EAAE,CAAC,IAAI,CAAC;MACxBC,aAAa,EAAE,CAAC,KAAK;KACtB,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;IAClD,IAAI,CAACxD,WAAW,GAAGyD,MAAM,CAACC,UAAU,EAAE;IAEtC,IAAI,CAAC7F,KAAK,CAAC8F,MAAM,CAACC,SAAS,CAAED,MAAM,IAAI;MACrC,IAAI,CAACjF,SAAS,GAAGiF,MAAM,CAAC,MAAM,CAAC,IAAI,YAAY;MAC/CJ,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAAC9E,SAAS,CAAC;IAC9D,CAAC,CAAC;IAEF,IAAI,CAACb,KAAK,CAACgG,WAAW,CAACD,SAAS,CAAED,MAAM,IAAI;MAC1C,IAAIA,MAAM,CAAC,IAAI,CAAC,EAAE;QAChB,IAAI,CAAClF,OAAO,GAAGkF,MAAM,CAAC,IAAI,CAAC;QAC3B,IAAI,CAACG,aAAa,CAACH,MAAM,CAAC,IAAI,CAAC,CAAC;MAClC;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAACvE,YAAY,GAAG,CAClB;MACE2E,IAAI,EAAE,IAAI;MACVC,IAAI,EAAE,kBAAkB,IAAI,CAACrF,SAAS,IAAI,YAAY;KACvD,CACF;EACH;EAEAsF,WAAWA,CAAA;IACT,IAAI,CAAC5D,QAAQ,CAAC6D,IAAI,EAAE;IACpB,IAAI,CAAC7D,QAAQ,CAAC8D,QAAQ,EAAE;IACxB,IAAI,IAAI,CAAC/D,gBAAgB,EAAE;MACzBgE,aAAa,CAAC,IAAI,CAAChE,gBAAgB,CAAC;IACtC;EACF;EAEAiE,WAAWA,CAACC,KAAoC;IAC9C,IAAI,CAACjD,WAAW,GAAGiD,KAAK,CAAChG,EAAE;IAC3B,IAAI,CAACgC,WAAW,GAAGgE,KAAK,CAAC/F,KAAK;EAChC;EAEAuF,aAAaA,CAACrF,OAAe;IAC3B,IAAI,CAACwC,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,IAAI,CAACvC,SAAS,KAAK,eAAe,EAAE;MACtC,IAAI,CAACX,YAAY,CAACwG,gCAAgC,CAAC9F,OAAO,CAAC,CAACmF,SAAS,CAAC;QACpEM,IAAI,EAAGM,QAAa,IAAI;UACtB,IAAI,CAACC,uBAAuB,CAACD,QAAQ,CAAC;QACxC,CAAC;QACDE,KAAK,EAAGA,KAAU,IAAI;UACpBnB,OAAO,CAACmB,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;UAC1D,IAAI,CAACzD,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAAClD,YAAY,CAAC4G,YAAY,CAAClG,OAAO,CAAC,CAACmF,SAAS,CAAC;QAChDM,IAAI,EAAGM,QAAa,IAAI;UACtB,IAAI,CAACC,uBAAuB,CAACD,QAAQ,CAAC;QACxC,CAAC;QACDE,KAAK,EAAGA,KAAU,IAAI;UACpBnB,OAAO,CAACmB,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;UACvD,IAAI,CAACzD,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;IACJ;EACF;EAEQwD,uBAAuBA,CAACD,QAAa;IAC3C,IAAI,CAACvD,SAAS,GAAG,KAAK;IAEtB;IACA,IAAI2D,SAAS;IACb,IACEJ,QAAQ,CAACK,YAAY,IACrBC,KAAK,CAACC,OAAO,CAACP,QAAQ,CAACK,YAAY,CAAC,IACpCL,QAAQ,CAACK,YAAY,CAACG,MAAM,GAAG,CAAC,EAChC;MACAJ,SAAS,GAAGJ,QAAQ,CAACK,YAAY,CAAC,CAAC,CAAC;IACtC,CAAC,MAAM,IAAIL,QAAQ,CAAC5F,WAAW,EAAE;MAC/BgG,SAAS,GAAGJ,QAAQ,CAAC5F,WAAW;IAClC,CAAC,MAAM,IAAI4F,QAAQ,CAACS,IAAI,EAAE;MACxBL,SAAS,GAAGJ,QAAQ,CAACS,IAAI;IAC3B,CAAC,MAAM;MACLL,SAAS,GAAGJ,QAAQ;IACtB;IAEA,IAAII,SAAS,EAAE;MACb,IAAI,CAACpD,mBAAmB,GAAGoD,SAAS;MACpC,IAAI,CAACjG,SAAS,GAAGiG,SAAS,CAACM,IAAI,IAAIN,SAAS,CAACjG,SAAS,IAAI,OAAO;MACjE,IAAI,CAACC,WAAW,GAAGgG,SAAS,CAACO,WAAW,IAAIP,SAAS,CAAChG,WAAW,IAAI,EAAE;MAEvE;MACA,IAAI,IAAI,CAACF,SAAS,KAAK,YAAY,EAAE;QACnC;QACA,IAAI,CAACkD,cAAc,GACjBgD,SAAS,CAACQ,WAAW,IAAIR,SAAS,CAACM,IAAI,IAAI,cAAc;QAE3D;QACA,IAAI,CAACrD,iBAAiB,GACpB+C,SAAS,CAACS,WAAW,IACrBT,SAAS,CAACQ,WAAW,IACrBR,SAAS,CAACM,IAAI,IACd,EAAE;QAEJ;QACA,IAAIN,SAAS,CAACU,gBAAgB,EAAE;UAC9B,IAAI,CAACxD,yBAAyB,GAAG8C,SAAS,CAACU,gBAAgB;QAC7D,CAAC,MAAM,IAAIV,SAAS,CAACS,WAAW,EAAE;UAChC,IAAI,CAACvD,yBAAyB,GAAG8C,SAAS,CAACS,WAAW;QACxD,CAAC,MAAM,IAAIT,SAAS,CAACQ,WAAW,EAAE;UAChC,IAAI,CAACtD,yBAAyB,GAAG8C,SAAS,CAACQ,WAAW;QACxD;MACF;MAEA;MACA,IAAI,IAAI,CAAChG,YAAY,CAAC4F,MAAM,GAAG,CAAC,EAAE;QAChC,IAAI,CAAC5F,YAAY,CAAC,CAAC,CAAC,CAAC4E,IAAI,GAAG,kBAAkB,IAAI,CAACrF,SAAS,6BAA6B;MAC3F;MAEA;MACA,IAAI,CAAC4G,cAAc,CAACX,SAAS,CAAC;IAChC;EACF;EAEQW,cAAcA,CAACX,SAAc;IACnC;IACA,IAAI,CAACY,gCAAgC,CAACZ,SAAS,CAAC;EAClD;EAEAa,iBAAiBA,CAACC,OAAe;IAC/B,IAAI,IAAI,CAAChH,SAAS,KAAK,YAAY,EAAE;MACnC;MACA,IACE,CAAC,IAAI,CAAC8C,mBAAmB,KACxB,CAAC,IAAI,CAACI,cAAc,IAAI,IAAI,CAACA,cAAc,KAAK,SAAS,CAAC,EAC3D;QACA,IAAI,CAAC+D,cAAc,CACjB,+DAA+D,CAChE;QACD;MACF;MAEA,IAAIC,cAAc,GAAGF,OAAO;MAC5B,IAAI,IAAI,CAAC3D,sBAAsB,CAACiD,MAAM,GAAG,CAAC,EAAE;QAC1C,MAAMa,SAAS,GAAG,IAAI,CAAC9D,sBAAsB,CAC1C+D,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACC,YAAY,CAAC,CAChCC,IAAI,CAAC,IAAI,CAAC;QACbL,cAAc,GAAG,GAAGF,OAAO,0BAA0BG,SAAS,EAAE;MAClE;MAEA;MACA,IAAI,CAACzG,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;QAAE2E,IAAI,EAAE,MAAM;QAAEC,IAAI,EAAE4B;MAAc,CAAE,CACvC;MAED;MACA,IAAI,CAACvG,gBAAgB,GAAG,IAAI;MAC5B,IAAI,CAACG,iBAAiB,GAAG,SAAS;MAClC,IAAI,CAACC,iBAAiB,GAAG,KAAK;MAE9B;MACA,IAAI,CAACL,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;QAAE2E,IAAI,EAAE,IAAI;QAAEC,IAAI,EAAE,KAAK;QAAE/C,SAAS,EAAE;MAAI,CAAE,CAC7C;MAED,MAAMmC,gBAAgB,GACpB,IAAI,CAACxD,SAAS,CAACsG,GAAG,CAAC,kBAAkB,CAAC,EAAEC,KAAK,IAAI,KAAK;MACxD,MAAM9C,aAAa,GAAG,IAAI,CAACzD,SAAS,CAACsG,GAAG,CAAC,eAAe,CAAC,EAAEC,KAAK,IAAI,KAAK;MAEzE5C,OAAO,CAACC,GAAG,CACT,2CAA2C,EAC3CJ,gBAAgB,EAChB,gBAAgB,EAChBC,aAAa,CACd;MAED;MACA;MACA,MAAM+C,SAAS,GACb,IAAI,CAAChE,SAAS,IACd,IAAI,CAACP,iBAAiB,IACtB,IAAI,CAACL,mBAAmB,EAAE6D,WAAW,IACrC,IAAI,CAAC7D,mBAAmB,EAAE4D,WAAW,IACrC,IAAI,CAAC5D,mBAAmB,EAAE0D,IAAI,IAC9B,IAAI,CAACtD,cAAc;MAErB,IAAIyE,iBAAiB,GAAG,IAAI,CAACvE,yBAAyB;MACtD,IAAI,CAACuE,iBAAiB,EAAE;QACtB;QACA,IAAI,IAAI,CAAC7E,mBAAmB,EAAE8D,gBAAgB,EAAE;UAC9Ce,iBAAiB,GAAG,IAAI,CAAC7E,mBAAmB,CAAC8D,gBAAgB;QAC/D,CAAC,MAAM;UACL,MAAMgB,OAAO,GAAG,IAAI,CAACC,qBAAqB,EAAE;UAC5C,MAAMC,eAAe,GACnB,IAAI,CAAChF,mBAAmB,EAAE6D,WAAW,IACrC,IAAI,CAAC7D,mBAAmB,EAAE4D,WAAW,IACrC,IAAI,CAAC5D,mBAAmB,EAAE0D,IAAI,IAC9BkB,SAAS;UACXC,iBAAiB,GAAG,GAAGG,eAAe,GAAGF,OAAO,EAAE;QACpD;MACF;MAEA,IAAI,IAAI,CAACvE,sBAAsB,CAACiD,MAAM,GAAG,CAAC,EAAE;QAC1C,IAAI,CAACyB,+BAA+B,CAClCf,OAAO,EACPU,SAAS,EACTC,iBAAiB,EACjBjD,gBAAgB,EAChBC,aAAa,CACd;QACD;MACF;MAEA,IAAI,CAACqD,qBAAqB,CACxBhB,OAAO,EACPU,SAAS,EACTC,iBAAiB,EACjBjD,gBAAgB,EAChBC,aAAa,CACd;IACH,CAAC,MAAM,IAAI,IAAI,CAAC3E,SAAS,KAAK,eAAe,EAAE;MAC7C,IAAI,CAACW,gBAAgB,GAAG,IAAI;MAC5B,IAAIsH,OAAO,GAAG;QACZ3G,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7BvB,OAAO,EAAEmI,MAAM,CAAC,IAAI,CAACnI,OAAO,CAAC;QAC7BoI,IAAI,EAAE,IAAI,CAAC5I,YAAY,CAAC6I,aAAa,EAAE,IAAI,uBAAuB;QAClEC,UAAU,EAAE;UAAEC,QAAQ,EAAEtB;QAAO;OAChC;MAED,IAAI,IAAI,CAAC3D,sBAAsB,CAACiD,MAAM,GAAG,CAAC,EAAE;QAC1C,MAAMiC,WAAW,GAAG,IAAI,CAAClF,sBAAsB,CAAC,CAAC,CAAC;QAClD,IAAI6D,cAAsB;QAC1B,IAAI,IAAI,CAAC7D,sBAAsB,CAACiD,MAAM,GAAG,CAAC,EAAE;UAC1C,MAAMa,SAAS,GAAG,IAAI,CAAC9D,sBAAsB,CAC1C+D,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACC,YAAY,CAAC,CAChCC,IAAI,CAAC,IAAI,CAAC;UACbL,cAAc,GAAG,sBAAsBC,SAAS,EAAE;UAElD,IAAI,CAACzG,YAAY,GAAG,CAAC;YAAE2E,IAAI,EAAE,MAAM;YAAEC,IAAI,EAAE4B;UAAc,CAAE,CAAC;QAC9D;QACA,IAAI,CAAC5H,sBAAsB,CACxBkJ,0BAA0B,CAACP,OAAO,EAAEM,WAAW,CAAC,CAChDE,IAAI,CACH/J,QAAQ,CAAC,MAAK;UACZ,IAAI,CAACiC,gBAAgB,GAAG,KAAK;UAC7B,IAAI,CAAC4C,wBAAwB,GAAG,KAAK;QACvC,CAAC,CAAC,EACF/E,SAAS,CAAC,IAAI,CAACgF,sBAAsB,CAAC,CACvC,CACA0B,SAAS,CAAC;UACTM,IAAI,EAAGkD,GAAG,IAAK,IAAI,CAACC,0BAA0B,CAACD,GAAG,EAAE1B,OAAO,CAAC;UAC5DhB,KAAK,EAAG4C,GAAQ,IAAK,IAAI,CAACC,uBAAuB,CAACD,GAAG,EAAE5B,OAAO;SAC/D,CAAC;MACN,CAAC,MAAM;QACL,IAAI,CAAC1H,sBAAsB,CACxBwJ,kBAAkB,CAACb,OAAO,CAAC,CAC3BQ,IAAI,CACH/J,QAAQ,CAAC,MAAK;UACZ,IAAI,CAACiC,gBAAgB,GAAG,KAAK;UAC7B,IAAI,CAAC4C,wBAAwB,GAAG,KAAK;QACvC,CAAC,CAAC,EACF/E,SAAS,CAAC,IAAI,CAACgF,sBAAsB,CAAC,CACvC,CACA0B,SAAS,CAAC;UACTM,IAAI,EAAGkD,GAAG,IAAK,IAAI,CAACC,0BAA0B,CAACD,GAAG,EAAE1B,OAAO,CAAC;UAC5DhB,KAAK,EAAG4C,GAAQ,IAAK,IAAI,CAACC,uBAAuB,CAACD,GAAG,EAAE5B,OAAO;SAC/D,CAAC;MACN;IACF;EACF;EAEA+B,eAAeA,CAAC5E,MAAsB;IACpC,IAAI,CAACvD,SAAS,GAAGuD,MAAM,CAACqC,IAAI,IAAIwC,MAAM,CAAC7E,MAAM,CAACsD,KAAK,CAAC,IAAI,EAAE;EAC5D;EAEAwB,gCAAgCA,CAACxB,KAAc;IAC7C;IACA,IAAI,CAACvG,SAAS,CAACsG,GAAG,CAAC,kBAAkB,CAAC,EAAE0B,QAAQ,CAACzB,KAAK,CAAC;IAEvD;IACA;IACA,IAAI,CAACA,KAAK,EAAE;MACV,IAAI,CAAChE,gBAAgB,GAAG,EAAE;MAC1BoB,OAAO,CAACC,GAAG,CACT,6DAA6D,CAC9D;IACH,CAAC,MAAM;MACLD,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;IACzE;EACF;EAEAqE,0BAA0BA,CAAC1B,KAAc;IACvC;IACA,IAAI,CAACvG,SAAS,CAACsG,GAAG,CAAC,eAAe,CAAC,EAAE0B,QAAQ,CAACzB,KAAK,CAAC;IACpD5C,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE2C,KAAK,CAAC;EAC9C;EAEA2B,eAAeA,CAACC,KAAY;IAC1B,IAAI,CAAClH,aAAa,GAAGkH,KAAK;IAC1B;IACA,IAAI,CAAChG,sBAAsB,GAAGgG,KAAK;EACrC;EAEAC,mBAAmBA,CAAA;IACjB;EAAA;EAGFC,QAAQA,CAAA;IACN;EAAA;EAGFC,aAAaA,CAACC,OAA8B;IAC1C;EAAA;EAGFC,mBAAmBA,CAACC,MAAiC;IACnD;EAAA;EAGFC,YAAYA,CAAA;IACV,IAAI,CAACxK,MAAM,CAACyK,QAAQ,CAAC,CAAC,eAAe,EAAE,IAAI,CAAC7J,SAAS,CAAC,EAAE;MACtDmF,WAAW,EAAE;QAAEvF,EAAE,EAAE,IAAI,CAACG,OAAO;QAAE+J,IAAI,EAAE;MAAM;KAC9C,CAAC;EACJ;EAEAC,SAASA,CAAA;IACP,IAAI,CAAC3K,MAAM,CAACyK,QAAQ,CAAC,CAAC,eAAe,EAAE,IAAI,CAAC7J,SAAS,CAAC,EAAE;MACtDmF,WAAW,EAAE;QAAEvF,EAAE,EAAE,IAAI,CAACG,OAAO;QAAE+J,IAAI,EAAE;MAAM;KAC9C,CAAC;EACJ;EAEAE,oBAAoBA,CAAA;IAClB,IAAI,CAAC5K,MAAM,CAACyK,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;EACzC;EAEAI,eAAeA,CAAA;IACb,IAAI,CAACrH,oBAAoB,GAAG,CAAC,IAAI,CAACA,oBAAoB;EACxD;EAEAsH,iBAAiBA,CAACC,GAAW;IAC3B,IAAI,CAACtH,cAAc,GAAGsH,GAAG;EAC3B;EAEA;EACAC,mBAAmBA,CAACC,QAAgB;IAClC,IAAI,CAACnG,sBAAsB,CAACmG,QAAQ,CAAC,GACnC,CAAC,IAAI,CAACnG,sBAAsB,CAACmG,QAAQ,CAAC;EAC1C;EAEAC,uBAAuBA,CAACD,QAAgB;IACtC,OAAO,IAAI,CAACnG,sBAAsB,CAACmG,QAAQ,CAAC,IAAI,KAAK;EACvD;EAEA;EACQpD,cAAcA,CAACD,OAAe;IACpC,IAAI,CAACtG,YAAY,GAAG,CAAC,GAAG,IAAI,CAACA,YAAY,EAAE;MAAE2E,IAAI,EAAE,IAAI;MAAEC,IAAI,EAAE0B;IAAO,CAAE,CAAC;EAC3E;EAEQa,qBAAqBA,CAAA;IAC3B;IACA,OAAO,EAAE;EACX;EAEQ0C,qBAAqBA,CAAA;IAC3B;IACA,OAAO,EAAE;EACX;EAEQ1B,uBAAuBA,CAACD,GAAQ,EAAE5B,OAAe;IACvD;IACA,IAAI,CAACtG,YAAY,GAAG,IAAI,CAACA,YAAY,CAAC8J,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAAClI,SAAS,CAAC;IAE/D,MAAMmI,gBAAgB,GAAG9B,GAAG,EAAE5C,KAAK,EAAEgB,OAAO,IAAI4B,GAAG,EAAE5B,OAAO,IAAI,uBAAuB;IACvF,MAAM2D,YAAY,GAAwB;MACxCtF,IAAI,EAAE,IAAI;MACVC,IAAI,EAAEoF,gBAAgB;MACtBlK,MAAM,EAAE,QAAQ;MAChBoK,SAAS,EAAE,IAAIC,IAAI;KACpB;IACD,IAAI,CAACnK,YAAY,CAACoK,IAAI,CAACH,YAAY,CAAC;IAEpC;IACA,IAAI,CAAC9J,WAAW,CAACiK,IAAI,CAAC;MAAE3C,IAAI,EAAEnB,OAAO;MAAE+D,EAAE,EAAEJ;IAAY,CAAE,CAAC;IAC1D,IAAI,CAAC7J,iBAAiB,GAAG,QAAQ;IACjC,IAAI,CAACH,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACI,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACmJ,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC;EACpC;EAEAvB,0BAA0BA,CAAC7C,QAAa,EAAEkB,OAAe;IACvD;IACA,IAAI,CAACtG,YAAY,GAAG,IAAI,CAACA,YAAY,CAAC8J,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAAClI,SAAS,CAAC;IAE/D,IAAI;MACF,IAAI,CAACtB,mBAAmB,GAAG6E,QAAQ;MACnC,MAAMkF,SAAS,GAAGlF,QAAQ,EAAEmF,aAAa,EAAEC,KAAK,EAAEC,MAAM;MACxD,IAAIC,eAAe,GAAG,EAAE;MAExB,IAAIJ,SAAS,EAAE;QACbI,eAAe,GAAGJ,SAAS,CAACK,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC;MACnD,CAAC,MAAM;QACLD,eAAe,GAAGtF,QAAQ,EAAEmF,aAAa,EAAEK,MAAM,IAAI,yBAAyB;MAChF;MAEA;MACA,MAAMC,cAAc,GAAwB;QAC1ClG,IAAI,EAAE,IAAI;QACVC,IAAI,EAAE8F,eAAe;QACrB5K,MAAM,EAAE,SAAS;QACjBoK,SAAS,EAAE,IAAIC,IAAI;OACpB;MACD,IAAI,CAACnK,YAAY,CAACoK,IAAI,CAACS,cAAc,CAAC;MAEtC;MACA,IAAI,CAAC1K,WAAW,CAACiK,IAAI,CAAC;QAAE3C,IAAI,EAAEnB,OAAO;QAAE+D,EAAE,EAAEQ;MAAc,CAAE,CAAC;MAC5D,IAAI,CAACzK,iBAAiB,GAAG,SAAS;IAEpC,CAAC,CAAC,OAAO8H,GAAQ,EAAE;MACjB,MAAM8B,gBAAgB,GAAG9B,GAAG,EAAE5B,OAAO,IAAI,wCAAwC;MACjF,MAAM2D,YAAY,GAAwB;QACxCtF,IAAI,EAAE,IAAI;QACVC,IAAI,EAAEoF,gBAAgB;QACtBlK,MAAM,EAAE,QAAQ;QAChBoK,SAAS,EAAE,IAAIC,IAAI;OACpB;MACD,IAAI,CAACnK,YAAY,CAACoK,IAAI,CAACH,YAAY,CAAC;MAEpC;MACA,IAAI,CAAC9J,WAAW,CAACiK,IAAI,CAAC;QAAE3C,IAAI,EAAEnB,OAAO;QAAE+D,EAAE,EAAEJ;MAAY,CAAE,CAAC;MAC1D,IAAI,CAAC7J,iBAAiB,GAAG,QAAQ;IACnC,CAAC,SACO;MACJ,IAAI,CAACH,gBAAgB,GAAG,KAAK;MAC7B,IAAI,CAACI,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAACmJ,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC;IACtC;EACF;EAEQnC,+BAA+BA,CACrCf,OAAe,EACf8C,IAAY,EACZnC,iBAAyB,EACzBjD,gBAAyB,EACzBC,aAAsB;IAEtB,MAAM6G,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/B,IAAI,CAACpI,sBAAsB,CAACqI,OAAO,CAAEC,QAAQ,IAAI;MAC/C,IAAIA,QAAQ,CAACtE,IAAI,EAAE;QACjBmE,QAAQ,CAACI,MAAM,CAAC,OAAO,EAAED,QAAQ,CAACtE,IAAI,CAAC;MACzC;IACF,CAAC,CAAC;IAEF,IAAImE,QAAQ,CAACK,GAAG,CAAC,OAAO,CAAC,EAAE;MACzB,IAAI,CAACvM,sBAAsB,CACxBwM,gBAAgB,CAACN,QAAQ,CAAC,CAC1B/C,IAAI,CACHhK,SAAS,CAAEsN,YAAY,IAAI;QACzB,MAAMC,WAAW,GACfD,YAAY,EAAEE,aAAa,EACvB7E,GAAG,CAAEtB,QAAa,IAAKA,QAAQ,CAACkG,WAAW,CAAC,EAC5CzE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;QACtB,IAAI,CAAC2E,8BAA8B,CACjClF,OAAO,EACP8C,IAAI,EACJnC,iBAAiB,EACjBjD,gBAAgB,EAChBC,aAAa,EACbqH,WAAW,CACZ;QACD,OAAOpN,EAAE,CAAC,IAAI,CAAC;MACjB,CAAC,CAAC,EACFD,UAAU,CAAEqH,KAAK,IAAI;QACnBnB,OAAO,CAACmB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,IAAI,CAACgC,qBAAqB,CACxBhB,OAAO,EACP8C,IAAI,EACJnC,iBAAiB,EACjBjD,gBAAgB,EAChBC,aAAa,CACd;QACD,OAAO/F,EAAE,CAAC,IAAI,CAAC;MACjB,CAAC,CAAC,CACH,CACAsG,SAAS,EAAE;IAChB,CAAC,MAAM;MACL,IAAI,CAAC8C,qBAAqB,CACxBhB,OAAO,EACP8C,IAAI,EACJnC,iBAAiB,EACjBjD,gBAAgB,EAChBC,aAAa,CACd;IACH;EACF;EAEQqD,qBAAqBA,CAC3BhB,OAAe,EACf8C,IAAY,EACZnC,iBAAyB,EACzBjD,gBAAyB,EACzBC,aAAsB;IAEtBE,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;MAClCkC,OAAO;MACP8C,IAAI;MACJnC,iBAAiB;MACjBjD,gBAAgB;MAChBC,aAAa;MACbwH,wBAAwB,EAAE,IAAI,CAAC1I,gBAAgB,CAAC6C;KACjD,CAAC;IAEF,IAAI5B,gBAAgB,EAAE;MACpB,IAAI,CAACjB,gBAAgB,CAACqH,IAAI,CAAC;QAAEsB,OAAO,EAAEpF,OAAO;QAAEqF,IAAI,EAAE;MAAM,CAAE,CAAC;IAChE;IAEA,MAAMpE,OAAO,GAAGvD,gBAAgB,GAAG,IAAI,CAACjB,gBAAgB,GAAGuD,OAAO;IAClE,MAAM;MAAEsF;IAAO,CAAE,GAAG,IAAI,CAAC/B,qBAAqB,EAAE;IAEhD1F,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEmD,OAAO,CAAC;IAEjD,IAAI,CAAC3I,sBAAsB,CACxBiN,cAAc,CACbtE,OAAO,EACP6B,IAAI,EACJpF,gBAAgB,EAChBC,aAAa,EACb,IAAI,CAACrB,eAAe,EACpBqE,iBAAiB,EACjB,EAAE,EACF2E,OAAO,CACR,CACA7D,IAAI,CACH/J,QAAQ,CAAC,MAAK;MACZ,IAAI,CAACiC,gBAAgB,GAAG,KAAK;MAC7B,IAAI,CAAC4C,wBAAwB,GAAG,KAAK;IACvC,CAAC,CAAC,EACF/E,SAAS,CAAC,IAAI,CAACgF,sBAAsB,CAAC,CACvC,CACA0B,SAAS,CAAC;MACTM,IAAI,EAAGgH,iBAAsB,IAAI;QAC/B;QACA,IAAI,CAACvL,mBAAmB,GAAGuL,iBAAiB;QAE5C,IACEA,iBAAiB,EAAE1G,QAAQ,IAC3B0G,iBAAiB,EAAE1G,QAAQ,EAAE2G,OAAO,EACpC;UACA,MAAMC,cAAc,GAAGF,iBAAiB,CAAC1G,QAAQ,CAAC2G,OAAO,CAAC,CAAC,CAAC,CAACnH,IAAI;UACjE,IAAI,CAAC5E,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;YAAE2E,IAAI,EAAE,IAAI;YAAEC,IAAI,EAAEoH;UAAc,CAAE,CACrC;UACD,IAAIhI,gBAAgB,EAAE;YACpB,IAAI,CAACjB,gBAAgB,CAACqH,IAAI,CAAC;cACzBsB,OAAO,EAAEM,cAAc;cACvBL,IAAI,EAAE;aACP,CAAC;UACJ;QACF,CAAC,MAAM;UACLxH,OAAO,CAAC8H,IAAI,CAAC,iCAAiC,EAAEH,iBAAiB,CAAC;UAClE,IAAI,CAACvF,cAAc,CACjB,+CAA+C,CAChD;QACH;MACF,CAAC;MACDjB,KAAK,EAAGA,KAAU,IAAI;QACpBnB,OAAO,CAACmB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;QAClC,MAAM2E,YAAY,GAChB3E,KAAK,EAAEA,KAAK,EAAEgB,OAAO,IACrB,kDAAkD;QACpD,IAAI,CAACC,cAAc,CAAC0D,YAAY,CAAC;QACjC,IAAIjG,gBAAgB,IAAI,IAAI,CAACjB,gBAAgB,CAAC6C,MAAM,GAAG,CAAC,EAAE;UACxD,IAAI,CAAC7C,gBAAgB,CAACmJ,GAAG,EAAE;QAC7B;MACF;KACD,CAAC;EACN;EAEQV,8BAA8BA,CACpClF,OAAe,EACf8C,IAAY,EACZnC,iBAAyB,EACzBjD,gBAAyB,EACzBC,aAAsB,EACtBkI,YAAoB;IAEpB,IAAInI,gBAAgB,EAAE;MACpB,IAAI,CAACjB,gBAAgB,CAACqH,IAAI,CAAC;QAAEsB,OAAO,EAAEpF,OAAO;QAAEqF,IAAI,EAAE;MAAM,CAAE,CAAC;IAChE;IACA,MAAMpE,OAAO,GAAGvD,gBAAgB,GAAG,IAAI,CAACjB,gBAAgB,GAAGuD,OAAO;IAClE,MAAM;MAAEsF;IAAO,CAAE,GAAG,IAAI,CAAC/B,qBAAqB,EAAE;IAEhD,IAAI,CAACjL,sBAAsB,CACxBiN,cAAc,CACbtE,OAAO,EACP6B,IAAI,EACJpF,gBAAgB,EAChBC,aAAa,EACb,IAAI,CAACrB,eAAe,EACpBqE,iBAAiB,EACjBkF,YAAY,EACZP,OAAO,CACR,CACA7D,IAAI,CACH/J,QAAQ,CAAC,MAAK;MACZ,IAAI,CAACiC,gBAAgB,GAAG,KAAK;MAC7B,IAAI,CAAC4C,wBAAwB,GAAG,KAAK;IACvC,CAAC,CAAC,EACF/E,SAAS,CAAC,IAAI,CAACgF,sBAAsB,CAAC,CACvC,CACA0B,SAAS,CAAC;MACTM,IAAI,EAAGgH,iBAAsB,IAAI;QAC/B,IACEA,iBAAiB,EAAE1G,QAAQ,IAC3B0G,iBAAiB,EAAE1G,QAAQ,EAAE2G,OAAO,EACpC;UACA,MAAMC,cAAc,GAAGF,iBAAiB,CAAC1G,QAAQ,CAAC2G,OAAO,CAAC,CAAC,CAAC,CAACnH,IAAI;UACjE,IAAI,CAAC5E,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;YAAE2E,IAAI,EAAE,IAAI;YAAEC,IAAI,EAAEoH;UAAc,CAAE,CACrC;UACD,IAAIhI,gBAAgB,EAAE;YACpB,IAAI,CAACjB,gBAAgB,CAACqH,IAAI,CAAC;cACzBsB,OAAO,EAAEM,cAAc;cACvBL,IAAI,EAAE;aACP,CAAC;UACJ;QACF,CAAC,MAAM;UACLxH,OAAO,CAAC8H,IAAI,CAAC,iCAAiC,EAAEH,iBAAiB,CAAC;UAClE,IAAI,CAACvF,cAAc,CACjB,+CAA+C,CAChD;QACH;MACF,CAAC;MACDjB,KAAK,EAAGA,KAAU,IAAI;QACpBnB,OAAO,CAACmB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;QAClC,MAAM2E,YAAY,GAChB3E,KAAK,EAAEA,KAAK,EAAEgB,OAAO,IACrB,kDAAkD;QACpD,IAAI,CAACC,cAAc,CAAC0D,YAAY,CAAC;QACjC,IAAIjG,gBAAgB,IAAI,IAAI,CAACjB,gBAAgB,CAAC6C,MAAM,GAAG,CAAC,EAAE;UACxD,IAAI,CAAC7C,gBAAgB,CAACmJ,GAAG,EAAE;QAC7B;MACF;KACD,CAAC;EACN;EAEA;EACQ9F,gCAAgCA,CAACZ,SAAc;IACrD,IAAI,CAACA,SAAS,EAAE;MACdrB,OAAO,CAAC8H,IAAI,CAAC,sCAAsC,CAAC;MACpD;IACF;IAEA9H,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEoB,SAAS,CAAC;IAC7DrB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC9E,SAAS,CAAC;IACpD6E,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEgI,MAAM,CAACC,IAAI,CAAC7G,SAAS,CAAC,CAAC;IAEjE;IACA,IAAI,CAACnD,eAAe,GAAG,EAAE;IACzB,IAAI,CAACC,WAAW,GAAG,EAAE;IAErB,IAAIgK,WAAW,GAAG,CAAC;IAEnB;IACA,IAAI,IAAI,CAAChN,SAAS,KAAK,YAAY,EAAE;MACnC,IAAI,CAACiN,6BAA6B,CAAC/G,SAAS,EAAE8G,WAAW,CAAC;IAC5D,CAAC,MAAM,IAAI,IAAI,CAAChN,SAAS,KAAK,eAAe,EAAE;MAC7C,IAAI,CAACkN,gCAAgC,CAAChH,SAAS,EAAE8G,WAAW,CAAC;IAC/D;IAEAnI,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE;MACxC/B,eAAe,EAAE,IAAI,CAACA,eAAe;MACrCC,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BmK,UAAU,EAAE,IAAI,CAACpK,eAAe,CAACuD;KAClC,CAAC;EACJ;EAEQ2G,6BAA6BA,CACnC/G,SAAc,EACd8G,WAAmB;IAEnBnI,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAE;MAC5DsI,MAAM,EAAElH,SAAS,CAACkH,MAAM;MACxBC,YAAY,EAAEnH,SAAS,CAACkH,MAAM,EAAE9G,MAAM;MACtCI,WAAW,EAAER,SAAS,CAACQ,WAAW;MAClCvC,MAAM,EAAE+B,SAAS,CAAC/B,MAAM;MACxBmJ,cAAc,EAAEpH,SAAS,CAACoH;KAC3B,CAAC;IAEF;IACA,IAAI,CAACzJ,oBAAoB,GAAG,EAAE;IAC9B,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAACC,uBAAuB,GAAG,EAAE;IACjC,IAAI,CAACC,uBAAuB,GAAG,EAAE;IAEjC;IACA,IAAIkC,SAAS,CAAC/B,MAAM,EAAE;MACpB,IAAI,CAACN,oBAAoB,CAACiH,IAAI,CAAC;QAC7BlL,EAAE,EAAE,UAAUoN,WAAW,EAAE,EAAE;QAC7BxG,IAAI,EAAEN,SAAS,CAAC/B,MAAM;QACtBoJ,IAAI,EAAE;OACP,CAAC;MACF1I,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEoB,SAAS,CAAC/B,MAAM,CAAC;IACvD;IAEA;IACA,IAAI+B,SAAS,CAACkH,MAAM,IAAIhH,KAAK,CAACC,OAAO,CAACH,SAAS,CAACkH,MAAM,CAAC,EAAE;MACvDvI,OAAO,CAACC,GAAG,CACT,yCAAyC,EACzCoB,SAAS,CAACkH,MAAM,CAAC9G,MAAM,CACxB;MAEDJ,SAAS,CAACkH,MAAM,CAAC1B,OAAO,CAAC,CAAC8B,QAAa,EAAEC,aAAqB,KAAI;QAChE5I,OAAO,CAACC,GAAG,CACT,eAAe2I,aAAa,SAASD,QAAQ,CAACE,UAAU,IAAI,EAC5DF,QAAQ,CAACG,YAAY,CACtB;QAED,IAAIH,QAAQ,CAACJ,MAAM,IAAIhH,KAAK,CAACC,OAAO,CAACmH,QAAQ,CAACJ,MAAM,CAAC,EAAE;UACrDvI,OAAO,CAACC,GAAG,CACT,eAAe2I,aAAa,QAAQD,QAAQ,CAACJ,MAAM,CAAC9G,MAAM,eAAe,CAC1E;UAEDkH,QAAQ,CAACJ,MAAM,CAAC1B,OAAO,CAAC,CAACkC,UAAe,EAAEC,SAAiB,KAAI;YAC7DhJ,OAAO,CAACC,GAAG,CAAC,kBAAkB2I,aAAa,IAAII,SAAS,GAAG,EAAE;cAC3DC,SAAS,EAAEF,UAAU,CAACE,SAAS;cAC/BC,WAAW,EAAEH,UAAU,CAACG,WAAW;cACnCL,UAAU,EAAEE,UAAU,CAACF;aACxB,CAAC;YAEF;YACA,IACEE,UAAU,CAACF,UAAU,KAAK,CAAC,IAC3BE,UAAU,CAACE,SAAS,KAAK,OAAO,IAChCF,UAAU,CAACG,WAAW,EACtB;cACAlJ,OAAO,CAACC,GAAG,CACT,2CAA2C,EAC3C8I,UAAU,CAACG,WAAW,CACvB;cACD,IAAI,CAACjK,mBAAmB,CAACgH,IAAI,CAAC;gBAC5BlL,EAAE,EAAE,SAASoN,WAAW,EAAE,EAAE;gBAC5BxG,IAAI,EAAE,GAAGoH,UAAU,CAACE,SAAS,EAAE;gBAC/BP,IAAI,EAAE;eACP,CAAC;YACJ;YAEA;YACA,IACEK,UAAU,CAACF,UAAU,KAAK,CAAC,IAC3BE,UAAU,CAACE,SAAS,KAAK,wBAAwB,IACjDF,UAAU,CAACG,WAAW,EACtB;cACAlJ,OAAO,CAACC,GAAG,CACT,kDAAkD,EAClD8I,UAAU,CAACG,WAAW,CACvB;cACD,MAAMC,OAAO,GAAGJ,UAAU,CAACG,WAAW,CAACE,QAAQ,EAAE;cACjD,MAAMC,KAAK,GAAGF,OAAO,CAClBG,KAAK,CAAC,GAAG,CAAC,CACV/G,GAAG,CAAExH,EAAU,IAAKA,EAAE,CAACwO,IAAI,EAAE,CAAC,CAC9B5D,MAAM,CAAE5K,EAAU,IAAKA,EAAE,CAAC;cAE7BsO,KAAK,CAACxC,OAAO,CAAE2C,IAAY,IAAI;gBAC7B,IAAI,CAACtK,uBAAuB,CAAC+G,IAAI,CAAC;kBAChClL,EAAE,EAAE,aAAaoN,WAAW,EAAE,EAAE;kBAChCxG,IAAI,EAAE,mBAAmB6H,IAAI,EAAE;kBAC/Bd,IAAI,EAAE;iBACP,CAAC;cACJ,CAAC,CAAC;YACJ;YAEA;YACA,IACEK,UAAU,CAACF,UAAU,KAAK,CAAC,IAC3BE,UAAU,CAACG,WAAW,KAAK,MAAM,EACjC;cACAlJ,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE;gBAC1DwJ,GAAG,EAAEV,UAAU,CAACE,SAAS;gBACzBrG,KAAK,EAAEmG,UAAU,CAACG;eACnB,CAAC;cAEF,IAAIH,UAAU,CAACE,SAAS,KAAK,mBAAmB,EAAE;gBAChD;gBACA,IAAI,IAAI,CAAC9J,uBAAuB,CAACsC,MAAM,KAAK,CAAC,EAAE;kBAC7C,IAAI,CAACtC,uBAAuB,CAAC8G,IAAI,CAAC;oBAChClL,EAAE,EAAE,aAAaoN,WAAW,EAAE,EAAE;oBAChCxG,IAAI,EAAE,oBAAoB;oBAC1B+G,IAAI,EAAE;mBACP,CAAC;gBACJ;cACF,CAAC,MAAM,IAAIK,UAAU,CAACE,SAAS,CAACS,UAAU,CAAC,YAAY,CAAC,EAAE;gBACxD;gBACA,IAAIC,aAAa,GAAGZ,UAAU,CAACE,SAAS;gBACxC,IAAIU,aAAa,CAACD,UAAU,CAAC,YAAY,CAAC,EAAE;kBAC1CC,aAAa,GAAGA,aAAa,CAC1BnD,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CACzBA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;gBACvB;gBAEA,IAAI,CAACrH,uBAAuB,CAAC8G,IAAI,CAAC;kBAChClL,EAAE,EAAE,aAAaoN,WAAW,EAAE,EAAE;kBAChCxG,IAAI,EAAE,GAAGgI,aAAa,EAAE;kBACxBjB,IAAI,EAAE;iBACP,CAAC;cACJ;YACF;UACF,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ;IAEA1I,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;MACvC2J,WAAW,EAAE,IAAI,CAAC5K,oBAAoB;MACtC6K,UAAU,EAAE,IAAI,CAAC5K,mBAAmB;MACpC6K,cAAc,EAAE,IAAI,CAAC5K,uBAAuB;MAC5C6K,cAAc,EAAE,IAAI,CAAC5K;KACtB,CAAC;IAEF;IACA,MAAM6K,aAAa,GAAG,CAAC,CAAC,CAAC;IACzB,MAAMC,eAAe,GACnB,IAAI,CAACjL,oBAAoB,CAACyC,MAAM,GAAG,IAAI,CAACxC,mBAAmB,CAACwC,MAAM;IACpE,IAAI,CAAC1C,6BAA6B,GAAGmL,IAAI,CAACC,KAAK,CAC5CF,eAAe,GAAGD,aAAa,GAAI,GAAG,CACxC;EACH;EAEQ3B,gCAAgCA,CACtChH,SAAc,EACd8G,WAAmB;IAEnBnI,OAAO,CAACC,GAAG,CACT,+DAA+D,CAChE;IACDD,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEoB,SAAS,CAAC;IACtErB,OAAO,CAACC,GAAG,CACT,0CAA0C,EAC1CgI,MAAM,CAACC,IAAI,CAAC7G,SAAS,CAAC,CACvB;IACDrB,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE,IAAI,CAAC9E,SAAS,CAAC;IACjE6E,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEkI,WAAW,CAAC;IAE1D;IACA,IAAI,CAACnJ,oBAAoB,GAAG,EAAE;IAC9B,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAACC,uBAAuB,GAAG,EAAE;IACjC,IAAI,CAACE,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACD,uBAAuB,GAAG,EAAE;IAEjCa,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;IAE1D;IACA,MAAMmK,sBAAsB,GAC1B/I,SAAS,CAACgJ,IAAI,IAAIhJ,SAAS,CAACmG,IAAI,IAAInG,SAAS,CAACO,WAAW;IAE3D5B,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE;MACtDoK,IAAI,EAAEhJ,SAAS,CAACgJ,IAAI;MACpB7C,IAAI,EAAEnG,SAAS,CAACmG,IAAI;MACpB5F,WAAW,EAAEP,SAAS,CAACO,WAAW;MAClCwI;KACD,CAAC;IAEF,IAAIA,sBAAsB,EAAE;MAC1B,IAAIE,cAAc,GAChBjJ,SAAS,CAACgJ,IAAI,IACdhJ,SAAS,CAACmG,IAAI,IACdnG,SAAS,CAACO,WAAW,IACrB,4BAA4B;MAE9B;MACA,IAAI0I,cAAc,CAAC7I,MAAM,GAAG,GAAG,EAAE;QAC/B6I,cAAc,GAAGA,cAAc,CAACC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK;MAC3D;MAEA,IAAI,CAACvL,oBAAoB,CAACiH,IAAI,CAAC;QAC7BlL,EAAE,EAAE,UAAUoN,WAAW,EAAE,EAAE;QAC7BxG,IAAI,EAAE2I,cAAc;QACpB5B,IAAI,EAAE;OACP,CAAC;MACF1I,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEqK,cAAc,CAAC;IACnE;IAEA;IACA,IAAIE,eAAe,GAAG,EAAE;IAExBxK,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;MAC5CwK,eAAe,EAAE,CAAC,CAACpJ,SAAS,CAACqJ,YAAY;MACzCA,YAAY,EAAErJ,SAAS,CAACqJ,YAAY;MACpCnL,KAAK,EAAE8B,SAAS,CAAC9B,KAAK;MACtBoL,SAAS,EAAEtJ,SAAS,CAACsJ,SAAS;MAC9BC,YAAY,EAAEvJ,SAAS,CAACuJ;KACzB,CAAC;IAEF;IACA,IAAIvJ,SAAS,CAACqJ,YAAY,IAAIrJ,SAAS,CAACqJ,YAAY,CAACG,QAAQ,EAAE;MAC7D,MAAMC,SAAS,GAAGvJ,KAAK,CAACC,OAAO,CAACH,SAAS,CAACqJ,YAAY,CAACG,QAAQ,CAAC,GAC5DxJ,SAAS,CAACqJ,YAAY,CAACG,QAAQ,GAC/B,CAACxJ,SAAS,CAACqJ,YAAY,CAACG,QAAQ,CAAC;MAErCL,eAAe,GAAGM,SAAS,CAACvI,GAAG,CAAEwI,GAAQ,IAAI;QAC3C,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;UACtD,OAAO;YAAEC,OAAO,EAAED;UAAG,CAAE;QACzB;QACA,OAAOA,GAAG;MACZ,CAAC,CAAC;IACJ;IACA;IAAA,KACK,IAAI1J,SAAS,CAACuJ,YAAY,EAAE;MAC/BJ,eAAe,GAAG,CAACnJ,SAAS,CAACuJ,YAAY,CAAC;IAC5C;IACA;IAAA,KACK,IAAIvJ,SAAS,CAAC9B,KAAK,IAAI8B,SAAS,CAACsJ,SAAS,EAAE;MAC/CH,eAAe,GAAG,CAAC;QAAEQ,OAAO,EAAE3J,SAAS,CAAC9B,KAAK,IAAI8B,SAAS,CAACsJ;MAAS,CAAE,CAAC;IACzE;IAEAH,eAAe,CAAC3D,OAAO,CAAEgE,QAAa,IAAI;MACxC,MAAMG,OAAO,GAAGH,QAAQ,CAACG,OAAO,IAAIH,QAAQ,CAAC9P,EAAE;MAC/C,MAAM4P,SAAS,GACbE,QAAQ,CAACtL,KAAK,IACdsL,QAAQ,CAACI,mBAAmB,IAC5B,aAAaD,OAAO,EAAE;MAExB,IAAI,CAAC/L,mBAAmB,CAACgH,IAAI,CAAC;QAC5BlL,EAAE,EAAE,SAASoN,WAAW,EAAE,EAAE;QAC5BxG,IAAI,EAAEgJ,SAAS;QACfjC,IAAI,EAAE;OACP,CAAC;MACF1I,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE0K,SAAS,CAAC;IAC7D,CAAC,CAAC;IAEF;IACA,IAAIO,mBAAmB,GAAG,EAAE;IAE5B;IACA,IAAI7J,SAAS,CAACqJ,YAAY,IAAIrJ,SAAS,CAACqJ,YAAY,CAACS,gBAAgB,EAAE;MACrE,MAAMC,MAAM,GAAG7J,KAAK,CAACC,OAAO,CAACH,SAAS,CAACqJ,YAAY,CAACS,gBAAgB,CAAC,GACjE9J,SAAS,CAACqJ,YAAY,CAACS,gBAAgB,GACvC,CAAC9J,SAAS,CAACqJ,YAAY,CAACS,gBAAgB,CAAC;MAE7CD,mBAAmB,GAAGE,MAAM,CAAC7I,GAAG,CAAEwI,GAAQ,IAAI;QAC5C,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;UACtD,OAAO;YAAEM,eAAe,EAAEN;UAAG,CAAE;QACjC;QACA,OAAOA,GAAG;MACZ,CAAC,CAAC;IACJ;IACA;IAAA,KACK,IACH1J,SAAS,CAACiK,aAAa,IACvB/J,KAAK,CAACC,OAAO,CAACH,SAAS,CAACiK,aAAa,CAAC,EACtC;MACAJ,mBAAmB,GAAG7J,SAAS,CAACiK,aAAa;IAC/C;IAEAJ,mBAAmB,CAACrE,OAAO,CAAE0E,KAAU,IAAI;MACzC,MAAM/B,IAAI,GAAG+B,KAAK,CAACF,eAAe,IAAIE,KAAK,CAACxQ,EAAE;MAC9C,MAAMyQ,cAAc,GAAGD,KAAK,CAACE,mBAAmB,IAAIF,KAAK,CAAC5J,IAAI;MAC9D,MAAM+J,MAAM,GAAGF,cAAc,IAAI,sBAAsBhC,IAAI,EAAE;MAE7D,IAAI,CAACtK,uBAAuB,CAAC+G,IAAI,CAAC;QAChClL,EAAE,EAAE,aAAaoN,WAAW,EAAE,EAAE;QAChCxG,IAAI,EAAE+J,MAAM;QACZhD,IAAI,EAAE;OACP,CAAC;MACF1I,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEyL,MAAM,CAAC;IAC9D,CAAC,CAAC;IAEF;IACA,IAAIC,cAAc,GAAG,EAAE;IACvB,IAAIC,kBAAkB,GAAG,EAAE;IAE3B5L,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;MAC3CwK,eAAe,EAAE,CAAC,CAACpJ,SAAS,CAACqJ,YAAY;MACzCmB,mBAAmB,EAAExK,SAAS,CAACqJ,YAAY;MAC3CoB,QAAQ,EAAEzK,SAAS,CAAC0K,KAAK;MACzBC,YAAY,EAAE3K,SAAS,CAAC0K,KAAK;MAC7BE,YAAY,EAAE5K,SAAS,CAAC6K,SAAS;MACjCC,gBAAgB,EAAE9K,SAAS,CAAC6K;KAC7B,CAAC;IAEF;IACA,IAAI7K,SAAS,CAACqJ,YAAY,EAAE;MAC1B,IAAIrJ,SAAS,CAACqJ,YAAY,CAAC0B,OAAO,EAAE;QAClC,MAAMC,QAAQ,GAAG9K,KAAK,CAACC,OAAO,CAACH,SAAS,CAACqJ,YAAY,CAAC0B,OAAO,CAAC,GAC1D/K,SAAS,CAACqJ,YAAY,CAAC0B,OAAO,GAC9B,CAAC/K,SAAS,CAACqJ,YAAY,CAAC0B,OAAO,CAAC;QAEpCT,cAAc,GAAGU,QAAQ,CAAC9J,GAAG,CAAEwI,GAAQ,IAAI;UACzC,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;YACtD,OAAO;cAAEuB,MAAM,EAAEvB;YAAG,CAAE;UACxB;UACA,OAAOA,GAAG;QACZ,CAAC,CAAC;MACJ;MACA,IAAI1J,SAAS,CAACqJ,YAAY,CAAC6B,WAAW,EAAE;QACtC,MAAMC,YAAY,GAAGjL,KAAK,CAACC,OAAO,CAACH,SAAS,CAACqJ,YAAY,CAAC6B,WAAW,CAAC,GAClElL,SAAS,CAACqJ,YAAY,CAAC6B,WAAW,GAClC,CAAClL,SAAS,CAACqJ,YAAY,CAAC6B,WAAW,CAAC;QAExCX,kBAAkB,GAAGY,YAAY,CAACjK,GAAG,CAAEwI,GAAQ,IAAI;UACjD,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;YACtD,OAAO;cAAEuB,MAAM,EAAEvB;YAAG,CAAE;UACxB;UACA,OAAOA,GAAG;QACZ,CAAC,CAAC;MACJ;IACF;IACA;IAAA,KACK;MACH,IAAI1J,SAAS,CAAC0K,KAAK,IAAIxK,KAAK,CAACC,OAAO,CAACH,SAAS,CAAC0K,KAAK,CAAC,EAAE;QACrDJ,cAAc,GAAGtK,SAAS,CAAC0K,KAAK;MAClC;MACA,IAAI1K,SAAS,CAAC6K,SAAS,IAAI3K,KAAK,CAACC,OAAO,CAACH,SAAS,CAAC6K,SAAS,CAAC,EAAE;QAC7DN,kBAAkB,GAAGvK,SAAS,CAAC6K,SAAS;MAC1C;IACF;IAEA;IACAP,cAAc,CAAC9E,OAAO,CAAEnH,IAAS,IAAI;MACnC,MAAM4M,MAAM,GAAG5M,IAAI,CAAC4M,MAAM,IAAI5M,IAAI,CAAC3E,EAAE;MACrC,MAAM0R,QAAQ,GAAG/M,IAAI,CAAC+M,QAAQ,IAAI/M,IAAI,CAACiC,IAAI,IAAI,YAAY2K,MAAM,EAAE;MAEnE,IAAI,CAAClN,kBAAkB,CAAC6G,IAAI,CAAC;QAC3BlL,EAAE,EAAE,QAAQoN,WAAW,EAAE,EAAE;QAC3BxG,IAAI,EAAE8K,QAAQ;QACd/D,IAAI,EAAE;OACP,CAAC;MACF1I,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEwM,QAAQ,CAAC;IACnE,CAAC,CAAC;IAEF;IACAb,kBAAkB,CAAC/E,OAAO,CAAE6F,QAAa,IAAI;MAC3C,MAAMC,UAAU,GAAGD,QAAQ,CAACJ,MAAM,IAAII,QAAQ,CAAC3R,EAAE;MACjD,MAAM6R,YAAY,GAChBF,QAAQ,CAACD,QAAQ,IAAIC,QAAQ,CAAC/K,IAAI,IAAI,iBAAiBgL,UAAU,EAAE;MAErE,IAAI,CAACvN,kBAAkB,CAAC6G,IAAI,CAAC;QAC3BlL,EAAE,EAAE,QAAQoN,WAAW,EAAE,EAAE;QAC3BxG,IAAI,EAAEiL,YAAY;QAClBlE,IAAI,EAAE;OACP,CAAC;MACF1I,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE2M,YAAY,CAAC;IACpE,CAAC,CAAC;IAEF5M,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE;MACrD2J,WAAW,EAAE,IAAI,CAAC5K,oBAAoB;MACtC6K,UAAU,EAAE,IAAI,CAAC5K,mBAAmB;MACpC6K,cAAc,EAAE,IAAI,CAAC5K,uBAAuB;MAC5C2N,SAAS,EAAE,IAAI,CAACzN,kBAAkB;MAClC2K,cAAc,EAAE,IAAI,CAAC5K;KACtB,CAAC;IAEF;IACAa,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;MACvC6M,WAAW,EAAE,IAAI,CAAC9N,oBAAoB,CAACyC,MAAM;MAC7CsL,UAAU,EAAE,IAAI,CAAC9N,mBAAmB,CAACwC,MAAM;MAC3CuL,cAAc,EAAE,IAAI,CAAC9N,uBAAuB,CAACuC,MAAM;MACnDwL,SAAS,EAAE,IAAI,CAAC7N,kBAAkB,CAACqC,MAAM;MACzCyL,cAAc,EAAE,IAAI,CAAC/N,uBAAuB,CAACsC;KAC9C,CAAC;IAEF;IACAzB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;MAClC9E,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBgS,eAAe,EAAE,IAAI,CAAChS,SAAS,KAAK,eAAe;MACnDiS,YAAY,EAAE,IAAI,CAAChO,kBAAkB,CAACqC,MAAM,GAAG,CAAC;MAChD4L,aAAa,EAAE,IAAI,CAACjO,kBAAkB,CAACmD,GAAG,CAAE+K,CAAC,IAAKA,CAAC,CAAC3L,IAAI;KACzD,CAAC;IAEF;IACA,MAAMqI,aAAa,GAAG,CAAC,CAAC,CAAC;IACzB,MAAMC,eAAe,GACnB,IAAI,CAACjL,oBAAoB,CAACyC,MAAM,GAAG,IAAI,CAACxC,mBAAmB,CAACwC,MAAM;IACpE,IAAI,CAAC1C,6BAA6B,GAAGmL,IAAI,CAACC,KAAK,CAC5CF,eAAe,GAAGD,aAAa,GAAI,GAAG,CACxC;EACH;CACD;AA1qCCuD,UAAA,EADC/T,SAAS,CAACS,iCAAiC,EAAE;EAAEuT,MAAM,EAAE;AAAK,CAAE,CAAC,C,8DACb;AAdxCnT,uBAAuB,GAAAkT,UAAA,EAZnChU,SAAS,CAAC;EACTkU,QAAQ,EAAE,qBAAqB;EAC/BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPlU,YAAY,EACZO,WAAW,EACXC,iCAAiC,EACjCC,aAAa,CACd;EACD0T,WAAW,EAAE,kCAAkC;EAC/CC,SAAS,EAAE,CAAC,kCAAkC;CAC/C,CAAC,C,EACWxT,uBAAuB,CAwrCnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}