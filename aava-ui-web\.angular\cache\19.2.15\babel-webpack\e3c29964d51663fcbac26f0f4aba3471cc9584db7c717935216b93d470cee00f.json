{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { DropdownItemComponent } from '../dropdown-item/dropdown-item.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction DropdownMenuComponent_div_2_app_dropdown_item_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-dropdown-item\", 5);\n    i0.ɵɵlistener(\"itemClick\", function DropdownMenuComponent_div_2_app_dropdown_item_1_Template_app_dropdown_item_itemClick_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onItemClick($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"icon\", item_r3.icon)(\"label\", item_r3.label)(\"description\", item_r3.description)(\"route\", item_r3.route);\n  }\n}\nfunction DropdownMenuComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵtemplate(1, DropdownMenuComponent_div_2_app_dropdown_item_1_Template, 1, 4, \"app-dropdown-item\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const column_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", column_r4);\n  }\n}\nexport let DropdownMenuComponent = /*#__PURE__*/(() => {\n  class DropdownMenuComponent {\n    el;\n    items = [];\n    visible = false;\n    itemSelected = new EventEmitter();\n    ITEMS_PER_COLUMN = 3;\n    constructor(el) {\n      this.el = el;\n    }\n    onItemClick(event) {\n      console.log('Dropdown menu item clicked:', event);\n      this.itemSelected.emit(event);\n    }\n    // Method to split items into columns\n    get columns() {\n      const columns = [];\n      const itemsPerColumn = this.ITEMS_PER_COLUMN;\n      for (let i = 0; i < this.items.length; i += itemsPerColumn) {\n        columns.push(this.items.slice(i, i + itemsPerColumn));\n      }\n      return columns;\n    }\n    static ɵfac = function DropdownMenuComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DropdownMenuComponent)(i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DropdownMenuComponent,\n      selectors: [[\"app-dropdown-menu\"]],\n      inputs: {\n        items: \"items\",\n        visible: \"visible\"\n      },\n      outputs: {\n        itemSelected: \"itemSelected\"\n      },\n      decls: 3,\n      vars: 3,\n      consts: [[1, \"dropdown-menu\"], [1, \"dropdown-columns\"], [\"class\", \"dropdown-column\", 4, \"ngFor\", \"ngForOf\"], [1, \"dropdown-column\"], [3, \"icon\", \"label\", \"description\", \"route\", \"itemClick\", 4, \"ngFor\", \"ngForOf\"], [3, \"itemClick\", \"icon\", \"label\", \"description\", \"route\"]],\n      template: function DropdownMenuComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵtemplate(2, DropdownMenuComponent_div_2_Template, 2, 1, \"div\", 2);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"visible\", ctx.visible);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.columns);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, DropdownItemComponent],\n      styles: [\".dropdown-menu[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 100%;\\n  left: 0;\\n  min-width: 280px;\\n  background-color: var(--dropdown-bg);\\n  border-radius: 8px;\\n  box-shadow: 0 4px 16px var(--dropdown-shadow);\\n  border: 1px solid var(--dropdown-menu-border, var(--dropdown-border));\\n  opacity: 0;\\n  visibility: hidden;\\n  transform: translateY(10px);\\n  pointer-events: none;\\n  transition: opacity 0.8s ease-in-out, transform 0.8s ease-in-out, visibility 0.8s ease-in-out;\\n  z-index: 1500;\\n  margin-top: 20px;\\n  padding: 8px;\\n}\\n.dropdown-menu.visible[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  visibility: visible;\\n  transform: translateY(0);\\n  pointer-events: auto;\\n}\\n\\n.dropdown-columns[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  gap: 16px;\\n}\\n\\n.dropdown-column[_ngcontent-%COMP%] {\\n  min-width: 280px;\\n  display: flex;\\n  flex-direction: column;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3Byb2plY3RzL2NvbnNvbGUvc3JjL2FwcC9zaGFyZWQvY29tcG9uZW50cy9kcm9wZG93bi1tZW51L2Ryb3Bkb3duLW1lbnUuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxrQkFBQTtFQUNBLFNBQUE7RUFDQSxPQUFBO0VBQ0EsZ0JBQUE7RUFDQSxvQ0FBQTtFQUNBLGtCQUFBO0VBQ0EsNkNBQUE7RUFDQSxxRUFBQTtFQUNBLFVBQUE7RUFDQSxrQkFBQTtFQUNBLDJCQUFBO0VBQ0Esb0JBQUE7RUFDQSw2RkFBQTtFQUNBLGFBQUE7RUFDQSxnQkFBQTtFQUNBLFlBQUE7QUFDRjtBQUNFO0VBQ0UsVUFBQTtFQUNBLG1CQUFBO0VBQ0Esd0JBQUE7RUFDQSxvQkFBQTtBQUNKOztBQUdBO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsU0FBQTtBQUFGOztBQUdBO0VBQ0UsZ0JBQUE7RUFDQSxhQUFBO0VBQ0Esc0JBQUE7QUFBRiIsInNvdXJjZXNDb250ZW50IjpbIi5kcm9wZG93bi1tZW51IHtcclxuICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgdG9wOiAxMDAlO1xyXG4gIGxlZnQ6IDA7XHJcbiAgbWluLXdpZHRoOiAyODBweDtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS1kcm9wZG93bi1iZyk7XHJcbiAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG4gIGJveC1zaGFkb3c6IDAgNHB4IDE2cHggdmFyKC0tZHJvcGRvd24tc2hhZG93KTtcclxuICBib3JkZXI6IDFweCBzb2xpZCB2YXIoLS1kcm9wZG93bi1tZW51LWJvcmRlciwgdmFyKC0tZHJvcGRvd24tYm9yZGVyKSk7XHJcbiAgb3BhY2l0eTogMDtcclxuICB2aXNpYmlsaXR5OiBoaWRkZW47XHJcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDEwcHgpO1xyXG4gIHBvaW50ZXItZXZlbnRzOiBub25lO1xyXG4gIHRyYW5zaXRpb246IG9wYWNpdHkgMC44cyBlYXNlLWluLW91dCwgdHJhbnNmb3JtIDAuOHMgZWFzZS1pbi1vdXQsIHZpc2liaWxpdHkgMC44cyBlYXNlLWluLW91dDtcclxuICB6LWluZGV4OiAxNTAwO1xyXG4gIG1hcmdpbi10b3A6IDIwcHg7XHJcbiAgcGFkZGluZzogOHB4O1xyXG5cclxuICAmLnZpc2libGUge1xyXG4gICAgb3BhY2l0eTogMTtcclxuICAgIHZpc2liaWxpdHk6IHZpc2libGU7XHJcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMCk7XHJcbiAgICBwb2ludGVyLWV2ZW50czogYXV0bztcclxuICB9XHJcbn1cclxuXHJcbi5kcm9wZG93bi1jb2x1bW5zIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGZsZXgtZGlyZWN0aW9uOiByb3c7XHJcbiAgZ2FwOiAxNnB4O1xyXG59XHJcblxyXG4uZHJvcGRvd24tY29sdW1uIHtcclxuICBtaW4td2lkdGg6IDI4MHB4O1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxufSAiXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n  return DropdownMenuComponent;\n})();", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "DropdownItemComponent", "i0", "ɵɵelementStart", "ɵɵlistener", "DropdownMenuComponent_div_2_app_dropdown_item_1_Template_app_dropdown_item_itemClick_0_listener", "$event", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onItemClick", "ɵɵelementEnd", "ɵɵproperty", "item_r3", "icon", "label", "description", "route", "ɵɵtemplate", "DropdownMenuComponent_div_2_app_dropdown_item_1_Template", "ɵɵadvance", "column_r4", "DropdownMenuComponent", "el", "items", "visible", "itemSelected", "ITEMS_PER_COLUMN", "constructor", "event", "console", "log", "emit", "columns", "itemsPerColumn", "i", "length", "push", "slice", "ɵɵdirectiveInject", "ElementRef", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "DropdownMenuComponent_Template", "rf", "ctx", "DropdownMenuComponent_div_2_Template", "ɵɵclassProp", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\shared\\components\\dropdown-menu\\dropdown-menu.component.ts", "C:\\console\\aava-ui-web\\projects\\console\\src\\app\\shared\\components\\dropdown-menu\\dropdown-menu.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, ElementRef } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { DropdownItemComponent } from '../dropdown-item/dropdown-item.component';\r\n\r\ninterface DropdownItem {\r\n  label: string;\r\n  description: string;\r\n  route: string;\r\n  icon: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-dropdown-menu',\r\n  standalone: true,\r\n  imports: [CommonModule, DropdownItemComponent],\r\n  templateUrl: './dropdown-menu.component.html',\r\n  styleUrl: './dropdown-menu.component.scss'\r\n})\r\nexport class DropdownMenuComponent {\r\n  @Input() items: DropdownItem[] = [];\r\n  @Input() visible: boolean = false;\r\n  @Output() itemSelected = new EventEmitter<{route: string, label: string}>();\r\n  \r\n  readonly ITEMS_PER_COLUMN = 3;\r\n  \r\n  constructor(private el: ElementRef) {}\r\n\r\n  onItemClick(event: {route: string, label: string}): void {\r\n    console.log('Dropdown menu item clicked:', event);\r\n    this.itemSelected.emit(event);\r\n  }\r\n  \r\n  // Method to split items into columns\r\n  get columns(): DropdownItem[][] {\r\n    const columns: DropdownItem[][] = [];\r\n    const itemsPerColumn = this.ITEMS_PER_COLUMN;\r\n    \r\n    for (let i = 0; i < this.items.length; i += itemsPerColumn) {\r\n      columns.push(this.items.slice(i, i + itemsPerColumn));\r\n    }\r\n    \r\n    return columns;\r\n  }\r\n} ", "<div class=\"dropdown-menu\" [class.visible]=\"visible\">\r\n  <div class=\"dropdown-columns\">\r\n    <div class=\"dropdown-column\" *ngFor=\"let column of columns\">\r\n      <app-dropdown-item \r\n        *ngFor=\"let item of column\" \r\n        [icon]=\"item.icon\" \r\n        [label]=\"item.label\" \r\n        [description]=\"item.description\" \r\n        [route]=\"item.route\"\r\n        (itemClick)=\"onItemClick($event)\">\r\n      </app-dropdown-item>\r\n    </div>\r\n  </div>\r\n</div> "], "mappings": "AAAA,SAAmCA,YAAY,QAAoB,eAAe;AAClF,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,qBAAqB,QAAQ,0CAA0C;;;;;;ICC1EC,EAAA,CAAAC,cAAA,2BAMoC;IAAlCD,EAAA,CAAAE,UAAA,uBAAAC,gGAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAaF,MAAA,CAAAG,WAAA,CAAAN,MAAA,CAAmB;IAAA,EAAC;IACnCJ,EAAA,CAAAW,YAAA,EAAoB;;;;IAFlBX,EAHA,CAAAY,UAAA,SAAAC,OAAA,CAAAC,IAAA,CAAkB,UAAAD,OAAA,CAAAE,KAAA,CACE,gBAAAF,OAAA,CAAAG,WAAA,CACY,UAAAH,OAAA,CAAAI,KAAA,CACZ;;;;;IANxBjB,EAAA,CAAAC,cAAA,aAA4D;IAC1DD,EAAA,CAAAkB,UAAA,IAAAC,wDAAA,+BAMoC;IAEtCnB,EAAA,CAAAW,YAAA,EAAM;;;;IAPeX,EAAA,CAAAoB,SAAA,EAAS;IAATpB,EAAA,CAAAY,UAAA,YAAAS,SAAA,CAAS;;;ADclC,WAAaC,qBAAqB;EAA5B,MAAOA,qBAAqB;IAOZC,EAAA;IANXC,KAAK,GAAmB,EAAE;IAC1BC,OAAO,GAAY,KAAK;IACvBC,YAAY,GAAG,IAAI7B,YAAY,EAAkC;IAElE8B,gBAAgB,GAAG,CAAC;IAE7BC,YAAoBL,EAAc;MAAd,KAAAA,EAAE,GAAFA,EAAE;IAAe;IAErCb,WAAWA,CAACmB,KAAqC;MAC/CC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,KAAK,CAAC;MACjD,IAAI,CAACH,YAAY,CAACM,IAAI,CAACH,KAAK,CAAC;IAC/B;IAEA;IACA,IAAII,OAAOA,CAAA;MACT,MAAMA,OAAO,GAAqB,EAAE;MACpC,MAAMC,cAAc,GAAG,IAAI,CAACP,gBAAgB;MAE5C,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACX,KAAK,CAACY,MAAM,EAAED,CAAC,IAAID,cAAc,EAAE;QAC1DD,OAAO,CAACI,IAAI,CAAC,IAAI,CAACb,KAAK,CAACc,KAAK,CAACH,CAAC,EAAEA,CAAC,GAAGD,cAAc,CAAC,CAAC;MACvD;MAEA,OAAOD,OAAO;IAChB;;uCAxBWX,qBAAqB,EAAAtB,EAAA,CAAAuC,iBAAA,CAAAvC,EAAA,CAAAwC,UAAA;IAAA;;YAArBlB,qBAAqB;MAAAmB,SAAA;MAAAC,MAAA;QAAAlB,KAAA;QAAAC,OAAA;MAAA;MAAAkB,OAAA;QAAAjB,YAAA;MAAA;MAAAkB,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjBhCjD,EADF,CAAAC,cAAA,aAAqD,aACrB;UAC5BD,EAAA,CAAAkB,UAAA,IAAAiC,oCAAA,iBAA4D;UAWhEnD,EADE,CAAAW,YAAA,EAAM,EACF;;;UAbqBX,EAAA,CAAAoD,WAAA,YAAAF,GAAA,CAAAzB,OAAA,CAAyB;UAEAzB,EAAA,CAAAoB,SAAA,GAAU;UAAVpB,EAAA,CAAAY,UAAA,YAAAsC,GAAA,CAAAjB,OAAA,CAAU;;;qBDYlDnC,YAAY,EAAAuD,EAAA,CAAAC,OAAA,EAAEvD,qBAAqB;MAAAwD,MAAA;IAAA;;SAIlCjC,qBAAqB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}