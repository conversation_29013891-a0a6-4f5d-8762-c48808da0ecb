{"ast": null, "code": "import { select } from \"d3-selection\";\nimport noevent, { nonpassivecapture } from \"./noevent.js\";\nexport default function (view) {\n  var root = view.document.documentElement,\n    selection = select(view).on(\"dragstart.drag\", noevent, nonpassivecapture);\n  if (\"onselectstart\" in root) {\n    selection.on(\"selectstart.drag\", noevent, nonpassivecapture);\n  } else {\n    root.__noselect = root.style.MozUserSelect;\n    root.style.MozUserSelect = \"none\";\n  }\n}\nexport function yesdrag(view, noclick) {\n  var root = view.document.documentElement,\n    selection = select(view).on(\"dragstart.drag\", null);\n  if (noclick) {\n    selection.on(\"click.drag\", noevent, nonpassivecapture);\n    setTimeout(function () {\n      selection.on(\"click.drag\", null);\n    }, 0);\n  }\n  if (\"onselectstart\" in root) {\n    selection.on(\"selectstart.drag\", null);\n  } else {\n    root.style.MozUserSelect = root.__noselect;\n    delete root.__noselect;\n  }\n}", "map": {"version": 3, "names": ["select", "noevent", "nonpassivecapture", "view", "root", "document", "documentElement", "selection", "on", "__noselect", "style", "MozUserSelect", "yesdrag", "noclick", "setTimeout"], "sources": ["C:/console/aava-ui-web/node_modules/d3-drag/src/nodrag.js"], "sourcesContent": ["import {select} from \"d3-selection\";\nimport noevent, {nonpassivecapture} from \"./noevent.js\";\n\nexport default function(view) {\n  var root = view.document.documentElement,\n      selection = select(view).on(\"dragstart.drag\", noevent, nonpassivecapture);\n  if (\"onselectstart\" in root) {\n    selection.on(\"selectstart.drag\", noevent, nonpassivecapture);\n  } else {\n    root.__noselect = root.style.MozUserSelect;\n    root.style.MozUserSelect = \"none\";\n  }\n}\n\nexport function yesdrag(view, noclick) {\n  var root = view.document.documentElement,\n      selection = select(view).on(\"dragstart.drag\", null);\n  if (noclick) {\n    selection.on(\"click.drag\", noevent, nonpassivecapture);\n    setTimeout(function() { selection.on(\"click.drag\", null); }, 0);\n  }\n  if (\"onselectstart\" in root) {\n    selection.on(\"selectstart.drag\", null);\n  } else {\n    root.style.MozUserSelect = root.__noselect;\n    delete root.__noselect;\n  }\n}\n"], "mappings": "AAAA,SAAQA,MAAM,QAAO,cAAc;AACnC,OAAOC,OAAO,IAAGC,iBAAiB,QAAO,cAAc;AAEvD,eAAe,UAASC,IAAI,EAAE;EAC5B,IAAIC,IAAI,GAAGD,IAAI,CAACE,QAAQ,CAACC,eAAe;IACpCC,SAAS,GAAGP,MAAM,CAACG,IAAI,CAAC,CAACK,EAAE,CAAC,gBAAgB,EAAEP,OAAO,EAAEC,iBAAiB,CAAC;EAC7E,IAAI,eAAe,IAAIE,IAAI,EAAE;IAC3BG,SAAS,CAACC,EAAE,CAAC,kBAAkB,EAAEP,OAAO,EAAEC,iBAAiB,CAAC;EAC9D,CAAC,MAAM;IACLE,IAAI,CAACK,UAAU,GAAGL,IAAI,CAACM,KAAK,CAACC,aAAa;IAC1CP,IAAI,CAACM,KAAK,CAACC,aAAa,GAAG,MAAM;EACnC;AACF;AAEA,OAAO,SAASC,OAAOA,CAACT,IAAI,EAAEU,OAAO,EAAE;EACrC,IAAIT,IAAI,GAAGD,IAAI,CAACE,QAAQ,CAACC,eAAe;IACpCC,SAAS,GAAGP,MAAM,CAACG,IAAI,CAAC,CAACK,EAAE,CAAC,gBAAgB,EAAE,IAAI,CAAC;EACvD,IAAIK,OAAO,EAAE;IACXN,SAAS,CAACC,EAAE,CAAC,YAAY,EAAEP,OAAO,EAAEC,iBAAiB,CAAC;IACtDY,UAAU,CAAC,YAAW;MAAEP,SAAS,CAACC,EAAE,CAAC,YAAY,EAAE,IAAI,CAAC;IAAE,CAAC,EAAE,CAAC,CAAC;EACjE;EACA,IAAI,eAAe,IAAIJ,IAAI,EAAE;IAC3BG,SAAS,CAACC,EAAE,CAAC,kBAAkB,EAAE,IAAI,CAAC;EACxC,CAAC,MAAM;IACLJ,IAAI,CAACM,KAAK,CAACC,aAAa,GAAGP,IAAI,CAACK,UAAU;IAC1C,OAAOL,IAAI,CAACK,UAAU;EACxB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}