{"ast": null, "code": "export { axisTop, axisRight, axisBottom, axisLeft } from \"./axis.js\";", "map": {"version": 3, "names": ["axisTop", "axisRight", "axisBottom", "axisLeft"], "sources": ["C:/console/aava-ui-web/node_modules/d3-axis/src/index.js"], "sourcesContent": ["export {\n  axisTop,\n  axisRight,\n  axisBottom,\n  axisLeft\n} from \"./axis.js\";\n"], "mappings": "AAAA,SACEA,OAAO,EACPC,SAAS,EACTC,UAAU,EACVC,QAAQ,QACH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}