{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ButtonComponent } from '@ava/play-comp-library';\nimport { ExecutionStatus } from '../../workflow-execution.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nconst _c0 = a0 => ({\n  active: a0\n});\nfunction AgentActivityComponent_div_6_ava_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ava-button\", 17);\n    i0.ɵɵlistener(\"userClick\", function AgentActivityComponent_div_6_ava_button_7_Template_ava_button_userClick_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onViewOutput());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentActivityComponent_div_6_div_8_div_1_p_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const log_r4 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"color\", log_r4.color);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", log_r4.content, \" \");\n  }\n}\nfunction AgentActivityComponent_div_6_div_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtemplate(1, AgentActivityComponent_div_6_div_8_div_1_p_1_Template, 2, 3, \"p\", 21);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.activityLogs);\n  }\n}\nfunction AgentActivityComponent_div_6_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtemplate(1, AgentActivityComponent_div_6_div_8_div_1_Template, 2, 1, \"div\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.activityLogs.length);\n  }\n}\nfunction AgentActivityComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7)(2, \"p\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"img\", 8);\n    i0.ɵɵelementStart(5, \"div\", 9)(6, \"ava-button\", 10);\n    i0.ɵɵlistener(\"userClick\", function AgentActivityComponent_div_6_Template_ava_button_userClick_6_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onViewActivity());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, AgentActivityComponent_div_6_ava_button_7_Template, 1, 0, \"ava-button\", 11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, AgentActivityComponent_div_6_div_8_Template, 2, 1, \"div\", 12);\n    i0.ɵɵelementStart(9, \"div\", 13)(10, \"span\");\n    i0.ɵɵtext(11, \" Progress - \");\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"span\", 14);\n    i0.ɵɵlistener(\"click\", function AgentActivityComponent_div_6_Template_span_click_14_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeOutput());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(15, \"svg\", 15);\n    i0.ɵɵelement(16, \"path\", 16);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.activityInfo.message);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.activityInfo.imgSrc, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.activityInfo.isCompleted);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showOutput);\n    i0.ɵɵadvance(4);\n    i0.ɵɵstyleProp(\"color\", ctx_r1.progress < 100 ? \"#0891B2\" : \"#059669\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.progress, \"% Complete\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c0, ctx_r1.showOutput));\n  }\n}\nexport let AgentActivityComponent = /*#__PURE__*/(() => {\n  class AgentActivityComponent {\n    activityLogs = [];\n    executionDetails;\n    progress = 0;\n    isRunning = false;\n    status;\n    loadColor;\n    get color() {\n      return this.loadColor;\n    }\n    executionStatus = ExecutionStatus;\n    showOutput = false;\n    saveLogs = new EventEmitter();\n    controlAction = new EventEmitter();\n    onOutPutBtnClick = new EventEmitter();\n    showDetails = true;\n    constructor() {}\n    ngOnInit() {\n      console.log(this.activityLogs);\n    }\n    toggleDetails() {\n      this.showDetails = !this.showDetails;\n    }\n    onSaveLogs() {\n      this.saveLogs.emit();\n    }\n    onControlAction(action) {\n      this.controlAction.emit(action);\n    }\n    get statusClass() {\n      if (!this.executionDetails) return '';\n      switch (this.executionDetails.status) {\n        case 'running':\n          return 'status-running';\n        case 'completed':\n          return 'status-completed';\n        case 'failed':\n          return 'status-failed';\n        case 'canceled':\n          return 'status-canceled';\n        default:\n          return '';\n      }\n    }\n    get activityInfo() {\n      let data = {\n        message: '',\n        imgSrc: '',\n        isCompleted: false\n      };\n      if (this.status === this.executionStatus.running) {\n        data = {\n          message: 'Hey! The Agents are working together creating a flow!',\n          imgSrc: 'svgs/workflow-execution.svg',\n          isCompleted: false\n        };\n      }\n      if (this.status === this.executionStatus.completed) {\n        data = {\n          message: 'The flow is complete!',\n          imgSrc: 'svgs/ava-flow-complete.svg',\n          isCompleted: true\n        };\n      }\n      return data;\n    }\n    onViewActivity() {\n      this.showOutput = true;\n    }\n    onViewOutput() {\n      this.onOutPutBtnClick.emit();\n    }\n    closeOutput() {\n      this.showOutput = false;\n    }\n    static ɵfac = function AgentActivityComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AgentActivityComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AgentActivityComponent,\n      selectors: [[\"app-agent-activity\"]],\n      inputs: {\n        activityLogs: \"activityLogs\",\n        executionDetails: \"executionDetails\",\n        progress: \"progress\",\n        isRunning: \"isRunning\",\n        status: \"status\",\n        loadColor: \"loadColor\"\n      },\n      outputs: {\n        saveLogs: \"saveLogs\",\n        controlAction: \"controlAction\",\n        onOutPutBtnClick: \"onOutPutBtnClick\"\n      },\n      decls: 7,\n      vars: 1,\n      consts: [[1, \"agent-activity\"], [\"width\", \"0\", \"height\", \"0\", 2, \"position\", \"absolute\"], [\"id\", \"activityGradient\", \"x1\", \"0%\", \"y1\", \"0%\", \"x2\", \"100%\", \"y2\", \"0%\"], [\"offset\", \"0%\", \"stop-color\", \"#6566CD\"], [\"offset\", \"100%\", \"stop-color\", \"#F96CAB\"], [\"class\", \"activity-content\", \"aria-labelledby\", \"activity-title\", 4, \"ngIf\"], [\"aria-labelledby\", \"activity-title\", 1, \"activity-content\"], [1, \"image-container\"], [\"width\", \"200px\", 3, \"src\"], [1, \"actions\"], [\"label\", \"View Activity\", \"variant\", \"secondary\", \"size\", \"medium\", 1, \"view-activity-button\", 3, \"userClick\"], [\"class\", \"view-activity-button\", \"label\", \"View Output\", \"variant\", \"secondary\", \"size\", \"medium\", 3, \"userClick\", 4, \"ngIf\"], [\"class\", \"output\", 4, \"ngIf\"], [1, \"progress-container\"], [1, \"icon-btn\", 3, \"click\", \"ngClass\"], [\"width\", \"24\", \"height\", \"24\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"d\", \"M6 9L12 15L18 9\", \"stroke\", \"black\", \"stroke-width\", \"1.5\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"label\", \"View Output\", \"variant\", \"secondary\", \"size\", \"medium\", 1, \"view-activity-button\", 3, \"userClick\"], [1, \"output\"], [\"class\", \"activity-text\", 4, \"ngIf\"], [1, \"activity-text\"], [3, \"color\", 4, \"ngFor\", \"ngForOf\"]],\n      template: function AgentActivityComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(1, \"svg\", 1)(2, \"defs\")(3, \"linearGradient\", 2);\n          i0.ɵɵelement(4, \"stop\", 3)(5, \"stop\", 4);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(6, AgentActivityComponent_div_6_Template, 17, 10, \"div\", 5);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.status !== ctx.executionStatus.notStarted);\n        }\n      },\n      dependencies: [CommonModule, i1.NgClass, i1.NgForOf, i1.NgIf, ButtonComponent],\n      styles: [\"[_nghost-%COMP%] {\\n  height: 100%;\\n  display: block;\\n}\\n\\n.agent-activity[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  padding: 20px;\\n  box-shadow: 0 4px 15px var(--card-shadow);\\n  background: var(--card-bg);\\n}\\n.agent-activity[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  width: 100%;\\n  flex: 1;\\n}\\n.agent-activity[_ngcontent-%COMP%]   .activity-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 24px;\\n}\\n.agent-activity[_ngcontent-%COMP%]   .activity-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: var(--text-color);\\n}\\n.agent-activity[_ngcontent-%COMP%]   .activity-header[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 10px 16px;\\n  background-color: var(--card-bg);\\n  position: relative;\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  color: transparent;\\n  background-image: var(--gradient-primary);\\n  background-clip: text;\\n  -webkit-background-clip: text;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.agent-activity[_ngcontent-%COMP%]   .activity-header[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  border-radius: 8px;\\n  padding: 1px;\\n  background: var(--gradient-primary);\\n  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);\\n  mask-composite: exclude;\\n  pointer-events: none;\\n}\\n.agent-activity[_ngcontent-%COMP%]   .activity-header[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%]:hover {\\n  background-color: var(--dropdown-hover-bg);\\n}\\n.agent-activity[_ngcontent-%COMP%]   .activity-header[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n  stroke: url(#activityGradient);\\n}\\n.agent-activity[_ngcontent-%COMP%]   .activity-header[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]   path[_ngcontent-%COMP%] {\\n  stroke: url(#activityGradient);\\n  stroke-width: 2;\\n}\\n.agent-activity[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%] {\\n  position: relative;\\n  flex-grow: 1;\\n  overflow-y: hidden;\\n  display: flex;\\n  flex-direction: column;\\n  margin-bottom: 20px;\\n}\\n.agent-activity[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n  height: 6px;\\n}\\n.agent-activity[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: var(--dashboard-scrollbar-track);\\n  border-radius: 3px;\\n}\\n.agent-activity[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: var(--dashboard-scrollbar-thumb);\\n  border-radius: 3px;\\n}\\n.agent-activity[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: var(--dashboard-scrollbar-thumb-hover);\\n}\\n.agent-activity[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]   .logs-section[_ngcontent-%COMP%] {\\n  background-color: var(--dashboard-bg-lighter);\\n  border-radius: 15px;\\n  padding: 24px;\\n}\\n.agent-activity[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]   .logs-section[_ngcontent-%COMP%]   .no-logs[_ngcontent-%COMP%] {\\n  color: var(--text-secondary);\\n  font-style: italic;\\n  text-align: center;\\n  padding: 20px 0;\\n}\\n.agent-activity[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]   .logs-section[_ngcontent-%COMP%]   .activity-text[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  line-height: 1.7;\\n  color: var(--text-secondary);\\n  margin-bottom: 20px;\\n  white-space: pre-line;\\n}\\n.agent-activity[_ngcontent-%COMP%]   .activity-content[_ngcontent-%COMP%]   .logs-section[_ngcontent-%COMP%]   .success-message[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: var(--success-color);\\n  font-weight: 500;\\n  margin-top: 20px;\\n  text-align: center;\\n}\\n.agent-activity[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-top: auto;\\n}\\n.agent-activity[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  margin-right: 20px;\\n}\\n.agent-activity[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 6px;\\n  background-color: var(--dashboard-bg-light);\\n  border-radius: 3px;\\n  overflow: hidden;\\n  margin-bottom: 8px;\\n}\\n.agent-activity[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: var(--gradient-primary);\\n  border-radius: 3px;\\n  transition: width 0.3s ease;\\n}\\n.agent-activity[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%]   .progress-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: var(--text-color);\\n}\\n.agent-activity[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .control-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n.agent-activity[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .control-buttons[_ngcontent-%COMP%]   .control-button[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background-color: var(--card-bg);\\n  border: 1px solid var(--dashboard-action-button-border);\\n  border-radius: 50%;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.agent-activity[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .control-buttons[_ngcontent-%COMP%]   .control-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  transition: color 0.2s ease;\\n}\\n.agent-activity[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .control-buttons[_ngcontent-%COMP%]   .control-button[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: var(--dashboard-bg-lighter);\\n}\\n.agent-activity[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .control-buttons[_ngcontent-%COMP%]   .control-button[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.agent-activity[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .control-buttons[_ngcontent-%COMP%]   .control-button.refresh[_ngcontent-%COMP%] {\\n  color: var(--dashboard-secondary);\\n}\\n.agent-activity[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .control-buttons[_ngcontent-%COMP%]   .control-button.stop[_ngcontent-%COMP%] {\\n  color: var(--error-color);\\n}\\n.agent-activity[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .control-buttons[_ngcontent-%COMP%]   .control-button.play[_ngcontent-%COMP%] {\\n  color: var(--success-color);\\n}\\n\\n.view-activity-button[_ngcontent-%COMP%] {\\n  margin-top: 24px;\\n}\\n\\n.progress-container[_ngcontent-%COMP%] {\\n  padding: 10px;\\n  border-radius: 8px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  border: 1px solid #d1d3d8;\\n}\\n.progress-container[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  margin-top: 5px;\\n}\\n\\n.output[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  background-color: white;\\n  border: 1px solid #d1d3d8;\\n  width: 100%;\\n  height: calc(100% - 70px);\\n  position: absolute;\\n  top: 0;\\n}\\n\\n.actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n}\\n\\n.icon-btn[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transform: translateY(-5px) rotate(180deg);\\n  transition: 0.6s ease-in-out;\\n  padding-bottom: 5px;\\n}\\n\\n.icon-btn.active[_ngcontent-%COMP%] {\\n  transform: rotate(0deg);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return AgentActivityComponent;\n})();", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "ButtonComponent", "ExecutionStatus", "i0", "ɵɵelementStart", "ɵɵlistener", "AgentActivityComponent_div_6_ava_button_7_Template_ava_button_userClick_0_listener", "ɵɵrestoreView", "_r3", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onViewOutput", "ɵɵelementEnd", "ɵɵtext", "ɵɵstyleProp", "log_r4", "color", "ɵɵadvance", "ɵɵtextInterpolate1", "content", "ɵɵtemplate", "AgentActivityComponent_div_6_div_8_div_1_p_1_Template", "ɵɵproperty", "activityLogs", "AgentActivityComponent_div_6_div_8_div_1_Template", "length", "ɵɵelement", "AgentActivityComponent_div_6_Template_ava_button_userClick_6_listener", "_r1", "onViewActivity", "AgentActivityComponent_div_6_ava_button_7_Template", "AgentActivityComponent_div_6_div_8_Template", "AgentActivityComponent_div_6_Template_span_click_14_listener", "closeOutput", "ɵɵtextInterpolate", "activityInfo", "message", "imgSrc", "ɵɵsanitizeUrl", "isCompleted", "showOutput", "progress", "ɵɵpureFunction1", "_c0", "AgentActivityComponent", "executionDetails", "isRunning", "status", "loadColor", "executionStatus", "saveLogs", "controlAction", "onOutPutBtnClick", "showDetails", "constructor", "ngOnInit", "console", "log", "toggleDetails", "onSaveLogs", "emit", "onControlAction", "action", "statusClass", "data", "running", "completed", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "AgentActivityComponent_Template", "rf", "ctx", "AgentActivityComponent_div_6_Template", "notStarted", "i1", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\workflows\\workflow-execution\\components\\agent-activity\\agent-activity.component.ts", "C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\workflows\\workflow-execution\\components\\agent-activity\\agent-activity.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport {\r\n  AccordionComponent,\r\n  ButtonComponent,\r\n  IconComponent,\r\n} from '@ava/play-comp-library';\r\nimport { ExecutionStatus } from '../../workflow-execution.component';\r\n\r\nexport interface ActivityLog {\r\n  content: string;\r\n  timestamp: string;\r\n  message: string;\r\n  color: string;\r\n  type?: 'info' | 'success' | 'warning' | 'error';\r\n}\r\n\r\nexport interface ExecutionDetails {\r\n  agentName: string;\r\n  executionId: string;\r\n  startTime: string;\r\n  endTime?: string;\r\n  status: 'running' | 'completed' | 'failed' | 'canceled';\r\n  steps?: string[];\r\n}\r\n\r\n@Component({\r\n  selector: 'app-agent-activity',\r\n  standalone: true,\r\n  imports: [CommonModule, ButtonComponent],\r\n  templateUrl: './agent-activity.component.html',\r\n  styleUrls: ['./agent-activity.component.scss'],\r\n})\r\nexport class AgentActivityComponent {\r\n  @Input() activityLogs: ActivityLog[] = [];\r\n  @Input() executionDetails?: ExecutionDetails;\r\n  @Input() progress: number = 0;\r\n  @Input() isRunning: boolean = false;\r\n  @Input() status!: ExecutionStatus;\r\n  @Input() loadColor!: string;\r\n\r\n  get color() {\r\n    return this.loadColor;\r\n  }\r\n  executionStatus = ExecutionStatus;\r\n\r\n  showOutput = false;\r\n\r\n  @Output() saveLogs = new EventEmitter<void>();\r\n  @Output() controlAction = new EventEmitter<'play' | 'pause' | 'stop'>();\r\n  @Output() onOutPutBtnClick = new EventEmitter<any>();\r\n\r\n  showDetails: boolean = true;\r\n\r\n  constructor() {}\r\n\r\n  ngOnInit(): void {\r\n    console.log(this.activityLogs);\r\n  }\r\n\r\n  toggleDetails(): void {\r\n    this.showDetails = !this.showDetails;\r\n  }\r\n\r\n  onSaveLogs(): void {\r\n    this.saveLogs.emit();\r\n  }\r\n\r\n  onControlAction(action: 'play' | 'pause' | 'stop'): void {\r\n    this.controlAction.emit(action);\r\n  }\r\n\r\n  get statusClass(): string {\r\n    if (!this.executionDetails) return '';\r\n\r\n    switch (this.executionDetails.status) {\r\n      case 'running':\r\n        return 'status-running';\r\n      case 'completed':\r\n        return 'status-completed';\r\n      case 'failed':\r\n        return 'status-failed';\r\n      case 'canceled':\r\n        return 'status-canceled';\r\n      default:\r\n        return '';\r\n    }\r\n  }\r\n\r\n  get activityInfo() {\r\n    let data = {\r\n      message: '',\r\n      imgSrc: '',\r\n      isCompleted: false,\r\n    };\r\n\r\n    if (this.status === this.executionStatus.running) {\r\n      data = {\r\n        message: 'Hey! The Agents are working together creating a flow!',\r\n        imgSrc: 'svgs/workflow-execution.svg',\r\n        isCompleted: false,\r\n      };\r\n    }\r\n\r\n    if (this.status === this.executionStatus.completed) {\r\n      data = {\r\n        message: 'The flow is complete!',\r\n        imgSrc: 'svgs/ava-flow-complete.svg',\r\n        isCompleted: true,\r\n      };\r\n    }\r\n\r\n    return data;\r\n  }\r\n\r\n  onViewActivity() {\r\n    this.showOutput = true;\r\n  }\r\n\r\n  onViewOutput() {\r\n    this.onOutPutBtnClick.emit();\r\n  }\r\n  closeOutput() {\r\n    this.showOutput = false;\r\n  }\r\n}\r\n", "<div class=\"agent-activity\">\r\n  <!-- SVG Gradient Definitions for Icons -->\r\n  <svg width=\"0\" height=\"0\" style=\"position: absolute\">\r\n    <defs>\r\n      <linearGradient id=\"activityGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\r\n        <stop offset=\"0%\" stop-color=\"#6566CD\" />\r\n        <stop offset=\"100%\" stop-color=\"#F96CAB\" />\r\n      </linearGradient>\r\n    </defs>\r\n  </svg>\r\n\r\n  <div\r\n    class=\"activity-content\"\r\n    aria-labelledby=\"activity-title\"\r\n    *ngIf=\"status !== executionStatus.notStarted\"\r\n  >\r\n    <div class=\"image-container\">\r\n      <p>{{ activityInfo.message }}</p>\r\n      <img width=\"200px\" [src]=\"activityInfo.imgSrc\" />\r\n      <div class=\"actions\">\r\n        <ava-button\r\n          (userClick)=\"onViewActivity()\"\r\n          class=\"view-activity-button\"\r\n          label=\"View Activity\"\r\n          variant=\"secondary\"\r\n          size=\"medium\"\r\n        ></ava-button>\r\n        <ava-button\r\n          *ngIf=\"activityInfo.isCompleted\"\r\n          (userClick)=\"onViewOutput()\"\r\n          class=\"view-activity-button\"\r\n          label=\"View Output\"\r\n          variant=\"secondary\"\r\n          size=\"medium\"\r\n        ></ava-button>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"output\" *ngIf=\"showOutput\">\r\n      <div class=\"activity-text\" *ngIf=\"activityLogs.length\">\r\n        <p [style.color]=\"log.color\" *ngFor=\"let log of activityLogs\">\r\n          {{ log.content }}\r\n        </p>\r\n      </div>\r\n    </div>\r\n    <div class=\"progress-container\">\r\n      <span>\r\n        Progress -\r\n        <span [style.color]=\"progress < 100 ? '#0891B2' : '#059669'\"\r\n          >{{ progress }}% Complete</span\r\n        >\r\n      </span>\r\n      <span\r\n        class=\"icon-btn\"\r\n        [ngClass]=\"{ active: showOutput }\"\r\n        (click)=\"closeOutput()\"\r\n      >\r\n        <svg\r\n          width=\"24\"\r\n          height=\"24\"\r\n          viewBox=\"0 0 24 24\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <path\r\n            d=\"M6 9L12 15L18 9\"\r\n            stroke=\"black\"\r\n            stroke-width=\"1.5\"\r\n            stroke-linecap=\"round\"\r\n            stroke-linejoin=\"round\"\r\n          />\r\n        </svg>\r\n      </span>\r\n    </div>\r\n    <!-- Activity Logs -->\r\n    <!-- <div class=\"logs-section\">\r\n      <p *ngIf=\"!activityLogs.length\" class=\"no-logs\" aria-live=\"polite\">\r\n        No activity logs available yet.\r\n      </p>\r\n\r\n      <div class=\"activity-text\" *ngIf=\"activityLogs.length\">\r\n        <p [style.color]=\"log.color\" *ngFor=\"let log of activityLogs\">{{log.content}}</p>\r\n      </div>\r\n\r\n      <div\r\n        class=\"success-message\"\r\n        *ngIf=\"activityLogs.length\"\r\n        role=\"status\"\r\n        aria-live=\"polite\"\r\n      >\r\n        - Agent Pipeline completed successfully -\r\n      </div>\r\n    </div> -->\r\n  </div>\r\n\r\n  <!-- Progress and Controls -->\r\n  <!-- <div class=\"progress-section\">\r\n    <div class=\"progress-info\">\r\n      <div class=\"progress-bar\">\r\n        <div class=\"progress-fill\" [style.width.%]=\"progress\"></div>\r\n      </div>\r\n      <span class=\"progress-label\" aria-live=\"polite\"\r\n        >Progress - {{ progress }}% Complete</span\r\n      >\r\n    </div>\r\n\r\n    <div\r\n      class=\"control-buttons\"\r\n      role=\"toolbar\"\r\n      aria-label=\"Workflow execution controls\"\r\n    >\r\n      <button\r\n        class=\"control-button refresh\"\r\n        (click)=\"onControlAction('play')\"\r\n        aria-label=\"Refresh workflow\"\r\n        title=\"Refresh\"\r\n      >\r\n        <svg\r\n          width=\"16\"\r\n          height=\"16\"\r\n          viewBox=\"0 0 24 24\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n          aria-hidden=\"true\"\r\n        >\r\n          <path\r\n            d=\"M23 4v6h-6M1 20v-6h6\"\r\n            stroke=\"currentColor\"\r\n            stroke-width=\"2\"\r\n            stroke-linecap=\"round\"\r\n            stroke-linejoin=\"round\"\r\n          />\r\n          <path\r\n            d=\"M3.51 9a9 9 0 0114.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0020.49 15\"\r\n            stroke=\"currentColor\"\r\n            stroke-width=\"2\"\r\n            stroke-linecap=\"round\"\r\n            stroke-linejoin=\"round\"\r\n          />\r\n        </svg>\r\n      </button>\r\n\r\n      <button\r\n        class=\"control-button stop\"\r\n        (click)=\"onControlAction('stop')\"\r\n        aria-label=\"Stop workflow execution\"\r\n        title=\"Stop\"\r\n        [disabled]=\"!isRunning\"\r\n      >\r\n        <svg\r\n          width=\"16\"\r\n          height=\"16\"\r\n          viewBox=\"0 0 24 24\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n          aria-hidden=\"true\"\r\n        >\r\n          <rect x=\"4\" y=\"4\" width=\"16\" height=\"16\" fill=\"currentColor\" />\r\n        </svg>\r\n      </button>\r\n\r\n      <button\r\n        class=\"control-button play\"\r\n        (click)=\"onControlAction('play')\"\r\n        aria-label=\"Start workflow execution\"\r\n        title=\"Start\"\r\n        [disabled]=\"isRunning\"\r\n      >\r\n        <svg\r\n          width=\"16\"\r\n          height=\"16\"\r\n          viewBox=\"0 0 24 24\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n          aria-hidden=\"true\"\r\n        >\r\n          <path d=\"M5 3l14 9-14 9V3z\" fill=\"currentColor\" />\r\n        </svg>\r\n      </button>\r\n    </div>\r\n  </div> -->\r\n</div>\r\n"], "mappings": "AAAA,SAAmCA,YAAY,QAAQ,eAAe;AACtE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAEEC,eAAe,QAEV,wBAAwB;AAC/B,SAASC,eAAe,QAAQ,oCAAoC;;;;;;;;;ICoB5DC,EAAA,CAAAC,cAAA,qBAOC;IALCD,EAAA,CAAAE,UAAA,uBAAAC,mFAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAaF,MAAA,CAAAG,YAAA,EAAc;IAAA,EAAC;IAK7BT,EAAA,CAAAU,YAAA,EAAa;;;;;IAMdV,EAAA,CAAAC,cAAA,QAA8D;IAC5DD,EAAA,CAAAW,MAAA,GACF;IAAAX,EAAA,CAAAU,YAAA,EAAI;;;;IAFDV,EAAA,CAAAY,WAAA,UAAAC,MAAA,CAAAC,KAAA,CAAyB;IAC1Bd,EAAA,CAAAe,SAAA,EACF;IADEf,EAAA,CAAAgB,kBAAA,MAAAH,MAAA,CAAAI,OAAA,MACF;;;;;IAHFjB,EAAA,CAAAC,cAAA,cAAuD;IACrDD,EAAA,CAAAkB,UAAA,IAAAC,qDAAA,gBAA8D;IAGhEnB,EAAA,CAAAU,YAAA,EAAM;;;;IAHyCV,EAAA,CAAAe,SAAA,EAAe;IAAff,EAAA,CAAAoB,UAAA,YAAAd,MAAA,CAAAe,YAAA,CAAe;;;;;IAFhErB,EAAA,CAAAC,cAAA,cAAuC;IACrCD,EAAA,CAAAkB,UAAA,IAAAI,iDAAA,kBAAuD;IAKzDtB,EAAA,CAAAU,YAAA,EAAM;;;;IALwBV,EAAA,CAAAe,SAAA,EAAyB;IAAzBf,EAAA,CAAAoB,UAAA,SAAAd,MAAA,CAAAe,YAAA,CAAAE,MAAA,CAAyB;;;;;;IAtBrDvB,EANJ,CAAAC,cAAA,aAIC,aAC8B,QACxB;IAAAD,EAAA,CAAAW,MAAA,GAA0B;IAAAX,EAAA,CAAAU,YAAA,EAAI;IACjCV,EAAA,CAAAwB,SAAA,aAAiD;IAE/CxB,EADF,CAAAC,cAAA,aAAqB,qBAOlB;IALCD,EAAA,CAAAE,UAAA,uBAAAuB,sEAAA;MAAAzB,EAAA,CAAAI,aAAA,CAAAsB,GAAA;MAAA,MAAApB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAaF,MAAA,CAAAqB,cAAA,EAAgB;IAAA,EAAC;IAK/B3B,EAAA,CAAAU,YAAA,EAAa;IACdV,EAAA,CAAAkB,UAAA,IAAAU,kDAAA,yBAOC;IAEL5B,EADE,CAAAU,YAAA,EAAM,EACF;IAENV,EAAA,CAAAkB,UAAA,IAAAW,2CAAA,kBAAuC;IAQrC7B,EADF,CAAAC,cAAA,cAAgC,YACxB;IACJD,EAAA,CAAAW,MAAA,oBACA;IAAAX,EAAA,CAAAC,cAAA,YACG;IAAAD,EAAA,CAAAW,MAAA,IAAwB;IAE7BX,EAF6B,CAAAU,YAAA,EAC1B,EACI;IACPV,EAAA,CAAAC,cAAA,gBAIC;IADCD,EAAA,CAAAE,UAAA,mBAAA4B,6DAAA;MAAA9B,EAAA,CAAAI,aAAA,CAAAsB,GAAA;MAAA,MAAApB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAyB,WAAA,EAAa;IAAA,EAAC;;IAEvB/B,EAAA,CAAAC,cAAA,eAMC;IACCD,EAAA,CAAAwB,SAAA,gBAME;IAuBVxB,EAtBM,CAAAU,YAAA,EAAM,EACD,EACH,EAoBF;;;;IA5ECV,EAAA,CAAAe,SAAA,GAA0B;IAA1Bf,EAAA,CAAAgC,iBAAA,CAAA1B,MAAA,CAAA2B,YAAA,CAAAC,OAAA,CAA0B;IACVlC,EAAA,CAAAe,SAAA,EAA2B;IAA3Bf,EAAA,CAAAoB,UAAA,QAAAd,MAAA,CAAA2B,YAAA,CAAAE,MAAA,EAAAnC,EAAA,CAAAoC,aAAA,CAA2B;IAUzCpC,EAAA,CAAAe,SAAA,GAA8B;IAA9Bf,EAAA,CAAAoB,UAAA,SAAAd,MAAA,CAAA2B,YAAA,CAAAI,WAAA,CAA8B;IAUhBrC,EAAA,CAAAe,SAAA,EAAgB;IAAhBf,EAAA,CAAAoB,UAAA,SAAAd,MAAA,CAAAgC,UAAA,CAAgB;IAU3BtC,EAAA,CAAAe,SAAA,GAAsD;IAAtDf,EAAA,CAAAY,WAAA,UAAAN,MAAA,CAAAiC,QAAA,+BAAsD;IACzDvC,EAAA,CAAAe,SAAA,EAAwB;IAAxBf,EAAA,CAAAgB,kBAAA,KAAAV,MAAA,CAAAiC,QAAA,eAAwB;IAK3BvC,EAAA,CAAAe,SAAA,EAAkC;IAAlCf,EAAA,CAAAoB,UAAA,YAAApB,EAAA,CAAAwC,eAAA,IAAAC,GAAA,EAAAnC,MAAA,CAAAgC,UAAA,EAAkC;;;ADrB1C,WAAaI,sBAAsB;EAA7B,MAAOA,sBAAsB;IACxBrB,YAAY,GAAkB,EAAE;IAChCsB,gBAAgB;IAChBJ,QAAQ,GAAW,CAAC;IACpBK,SAAS,GAAY,KAAK;IAC1BC,MAAM;IACNC,SAAS;IAElB,IAAIhC,KAAKA,CAAA;MACP,OAAO,IAAI,CAACgC,SAAS;IACvB;IACAC,eAAe,GAAGhD,eAAe;IAEjCuC,UAAU,GAAG,KAAK;IAERU,QAAQ,GAAG,IAAIpD,YAAY,EAAQ;IACnCqD,aAAa,GAAG,IAAIrD,YAAY,EAA6B;IAC7DsD,gBAAgB,GAAG,IAAItD,YAAY,EAAO;IAEpDuD,WAAW,GAAY,IAAI;IAE3BC,YAAA,GAAe;IAEfC,QAAQA,CAAA;MACNC,OAAO,CAACC,GAAG,CAAC,IAAI,CAAClC,YAAY,CAAC;IAChC;IAEAmC,aAAaA,CAAA;MACX,IAAI,CAACL,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;IACtC;IAEAM,UAAUA,CAAA;MACR,IAAI,CAACT,QAAQ,CAACU,IAAI,EAAE;IACtB;IAEAC,eAAeA,CAACC,MAAiC;MAC/C,IAAI,CAACX,aAAa,CAACS,IAAI,CAACE,MAAM,CAAC;IACjC;IAEA,IAAIC,WAAWA,CAAA;MACb,IAAI,CAAC,IAAI,CAAClB,gBAAgB,EAAE,OAAO,EAAE;MAErC,QAAQ,IAAI,CAACA,gBAAgB,CAACE,MAAM;QAClC,KAAK,SAAS;UACZ,OAAO,gBAAgB;QACzB,KAAK,WAAW;UACd,OAAO,kBAAkB;QAC3B,KAAK,QAAQ;UACX,OAAO,eAAe;QACxB,KAAK,UAAU;UACb,OAAO,iBAAiB;QAC1B;UACE,OAAO,EAAE;MACb;IACF;IAEA,IAAIZ,YAAYA,CAAA;MACd,IAAI6B,IAAI,GAAG;QACT5B,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE,EAAE;QACVE,WAAW,EAAE;OACd;MAED,IAAI,IAAI,CAACQ,MAAM,KAAK,IAAI,CAACE,eAAe,CAACgB,OAAO,EAAE;QAChDD,IAAI,GAAG;UACL5B,OAAO,EAAE,uDAAuD;UAChEC,MAAM,EAAE,6BAA6B;UACrCE,WAAW,EAAE;SACd;MACH;MAEA,IAAI,IAAI,CAACQ,MAAM,KAAK,IAAI,CAACE,eAAe,CAACiB,SAAS,EAAE;QAClDF,IAAI,GAAG;UACL5B,OAAO,EAAE,uBAAuB;UAChCC,MAAM,EAAE,4BAA4B;UACpCE,WAAW,EAAE;SACd;MACH;MAEA,OAAOyB,IAAI;IACb;IAEAnC,cAAcA,CAAA;MACZ,IAAI,CAACW,UAAU,GAAG,IAAI;IACxB;IAEA7B,YAAYA,CAAA;MACV,IAAI,CAACyC,gBAAgB,CAACQ,IAAI,EAAE;IAC9B;IACA3B,WAAWA,CAAA;MACT,IAAI,CAACO,UAAU,GAAG,KAAK;IACzB;;uCA3FWI,sBAAsB;IAAA;;YAAtBA,sBAAsB;MAAAuB,SAAA;MAAAC,MAAA;QAAA7C,YAAA;QAAAsB,gBAAA;QAAAJ,QAAA;QAAAK,SAAA;QAAAC,MAAA;QAAAC,SAAA;MAAA;MAAAqB,OAAA;QAAAnB,QAAA;QAAAC,aAAA;QAAAC,gBAAA;MAAA;MAAAkB,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjCnCzE,EAAA,CAAAC,cAAA,aAA4B;;UAItBD,EAFJ,CAAAC,cAAA,aAAqD,WAC7C,wBACoE;UAEtED,EADA,CAAAwB,SAAA,cAAyC,cACE;UAGjDxB,EAFI,CAAAU,YAAA,EAAiB,EACZ,EACH;UAENV,EAAA,CAAAkB,UAAA,IAAAyD,qCAAA,mBAIC;UAsKH3E,EAAA,CAAAU,YAAA,EAAM;;;UAvKDV,EAAA,CAAAe,SAAA,GAA2C;UAA3Cf,EAAA,CAAAoB,UAAA,SAAAsD,GAAA,CAAA7B,MAAA,KAAA6B,GAAA,CAAA3B,eAAA,CAAA6B,UAAA,CAA2C;;;qBDepC/E,YAAY,EAAAgF,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAElF,eAAe;MAAAmF,MAAA;IAAA;;SAI5BvC,sBAAsB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}