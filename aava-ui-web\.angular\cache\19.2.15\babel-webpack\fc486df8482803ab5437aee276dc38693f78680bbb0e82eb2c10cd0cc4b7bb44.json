{"ast": null, "code": "import array from \"../array.js\";\nimport lcg from \"../lcg.js\";\nimport { packEncloseRandom } from \"./enclose.js\";\nfunction place(b, a, c) {\n  var dx = b.x - a.x,\n    x,\n    a2,\n    dy = b.y - a.y,\n    y,\n    b2,\n    d2 = dx * dx + dy * dy;\n  if (d2) {\n    a2 = a.r + c.r, a2 *= a2;\n    b2 = b.r + c.r, b2 *= b2;\n    if (a2 > b2) {\n      x = (d2 + b2 - a2) / (2 * d2);\n      y = Math.sqrt(Math.max(0, b2 / d2 - x * x));\n      c.x = b.x - x * dx - y * dy;\n      c.y = b.y - x * dy + y * dx;\n    } else {\n      x = (d2 + a2 - b2) / (2 * d2);\n      y = Math.sqrt(Math.max(0, a2 / d2 - x * x));\n      c.x = a.x + x * dx - y * dy;\n      c.y = a.y + x * dy + y * dx;\n    }\n  } else {\n    c.x = a.x + c.r;\n    c.y = a.y;\n  }\n}\nfunction intersects(a, b) {\n  var dr = a.r + b.r - 1e-6,\n    dx = b.x - a.x,\n    dy = b.y - a.y;\n  return dr > 0 && dr * dr > dx * dx + dy * dy;\n}\nfunction score(node) {\n  var a = node._,\n    b = node.next._,\n    ab = a.r + b.r,\n    dx = (a.x * b.r + b.x * a.r) / ab,\n    dy = (a.y * b.r + b.y * a.r) / ab;\n  return dx * dx + dy * dy;\n}\nfunction Node(circle) {\n  this._ = circle;\n  this.next = null;\n  this.previous = null;\n}\nexport function packSiblingsRandom(circles, random) {\n  if (!(n = (circles = array(circles)).length)) return 0;\n  var a, b, c, n, aa, ca, i, j, k, sj, sk;\n\n  // Place the first circle.\n  a = circles[0], a.x = 0, a.y = 0;\n  if (!(n > 1)) return a.r;\n\n  // Place the second circle.\n  b = circles[1], a.x = -b.r, b.x = a.r, b.y = 0;\n  if (!(n > 2)) return a.r + b.r;\n\n  // Place the third circle.\n  place(b, a, c = circles[2]);\n\n  // Initialize the front-chain using the first three circles a, b and c.\n  a = new Node(a), b = new Node(b), c = new Node(c);\n  a.next = c.previous = b;\n  b.next = a.previous = c;\n  c.next = b.previous = a;\n\n  // Attempt to place each remaining circle…\n  pack: for (i = 3; i < n; ++i) {\n    place(a._, b._, c = circles[i]), c = new Node(c);\n\n    // Find the closest intersecting circle on the front-chain, if any.\n    // “Closeness” is determined by linear distance along the front-chain.\n    // “Ahead” or “behind” is likewise determined by linear distance.\n    j = b.next, k = a.previous, sj = b._.r, sk = a._.r;\n    do {\n      if (sj <= sk) {\n        if (intersects(j._, c._)) {\n          b = j, a.next = b, b.previous = a, --i;\n          continue pack;\n        }\n        sj += j._.r, j = j.next;\n      } else {\n        if (intersects(k._, c._)) {\n          a = k, a.next = b, b.previous = a, --i;\n          continue pack;\n        }\n        sk += k._.r, k = k.previous;\n      }\n    } while (j !== k.next);\n\n    // Success! Insert the new circle c between a and b.\n    c.previous = a, c.next = b, a.next = b.previous = b = c;\n\n    // Compute the new closest circle pair to the centroid.\n    aa = score(a);\n    while ((c = c.next) !== b) {\n      if ((ca = score(c)) < aa) {\n        a = c, aa = ca;\n      }\n    }\n    b = a.next;\n  }\n\n  // Compute the enclosing circle of the front chain.\n  a = [b._], c = b;\n  while ((c = c.next) !== b) a.push(c._);\n  c = packEncloseRandom(a, random);\n\n  // Translate the circles to put the enclosing circle around the origin.\n  for (i = 0; i < n; ++i) a = circles[i], a.x -= c.x, a.y -= c.y;\n  return c.r;\n}\nexport default function (circles) {\n  packSiblingsRandom(circles, lcg());\n  return circles;\n}", "map": {"version": 3, "names": ["array", "lcg", "packEncloseRandom", "place", "b", "a", "c", "dx", "x", "a2", "dy", "y", "b2", "d2", "r", "Math", "sqrt", "max", "intersects", "dr", "score", "node", "_", "next", "ab", "Node", "circle", "previous", "packSiblingsRandom", "circles", "random", "n", "length", "aa", "ca", "i", "j", "k", "sj", "sk", "pack", "push"], "sources": ["C:/console/aava-ui-web/node_modules/d3-hierarchy/src/pack/siblings.js"], "sourcesContent": ["import array from \"../array.js\";\nimport lcg from \"../lcg.js\";\nimport {packEncloseRandom} from \"./enclose.js\";\n\nfunction place(b, a, c) {\n  var dx = b.x - a.x, x, a2,\n      dy = b.y - a.y, y, b2,\n      d2 = dx * dx + dy * dy;\n  if (d2) {\n    a2 = a.r + c.r, a2 *= a2;\n    b2 = b.r + c.r, b2 *= b2;\n    if (a2 > b2) {\n      x = (d2 + b2 - a2) / (2 * d2);\n      y = Math.sqrt(Math.max(0, b2 / d2 - x * x));\n      c.x = b.x - x * dx - y * dy;\n      c.y = b.y - x * dy + y * dx;\n    } else {\n      x = (d2 + a2 - b2) / (2 * d2);\n      y = Math.sqrt(Math.max(0, a2 / d2 - x * x));\n      c.x = a.x + x * dx - y * dy;\n      c.y = a.y + x * dy + y * dx;\n    }\n  } else {\n    c.x = a.x + c.r;\n    c.y = a.y;\n  }\n}\n\nfunction intersects(a, b) {\n  var dr = a.r + b.r - 1e-6, dx = b.x - a.x, dy = b.y - a.y;\n  return dr > 0 && dr * dr > dx * dx + dy * dy;\n}\n\nfunction score(node) {\n  var a = node._,\n      b = node.next._,\n      ab = a.r + b.r,\n      dx = (a.x * b.r + b.x * a.r) / ab,\n      dy = (a.y * b.r + b.y * a.r) / ab;\n  return dx * dx + dy * dy;\n}\n\nfunction Node(circle) {\n  this._ = circle;\n  this.next = null;\n  this.previous = null;\n}\n\nexport function packSiblingsRandom(circles, random) {\n  if (!(n = (circles = array(circles)).length)) return 0;\n\n  var a, b, c, n, aa, ca, i, j, k, sj, sk;\n\n  // Place the first circle.\n  a = circles[0], a.x = 0, a.y = 0;\n  if (!(n > 1)) return a.r;\n\n  // Place the second circle.\n  b = circles[1], a.x = -b.r, b.x = a.r, b.y = 0;\n  if (!(n > 2)) return a.r + b.r;\n\n  // Place the third circle.\n  place(b, a, c = circles[2]);\n\n  // Initialize the front-chain using the first three circles a, b and c.\n  a = new Node(a), b = new Node(b), c = new Node(c);\n  a.next = c.previous = b;\n  b.next = a.previous = c;\n  c.next = b.previous = a;\n\n  // Attempt to place each remaining circle…\n  pack: for (i = 3; i < n; ++i) {\n    place(a._, b._, c = circles[i]), c = new Node(c);\n\n    // Find the closest intersecting circle on the front-chain, if any.\n    // “Closeness” is determined by linear distance along the front-chain.\n    // “Ahead” or “behind” is likewise determined by linear distance.\n    j = b.next, k = a.previous, sj = b._.r, sk = a._.r;\n    do {\n      if (sj <= sk) {\n        if (intersects(j._, c._)) {\n          b = j, a.next = b, b.previous = a, --i;\n          continue pack;\n        }\n        sj += j._.r, j = j.next;\n      } else {\n        if (intersects(k._, c._)) {\n          a = k, a.next = b, b.previous = a, --i;\n          continue pack;\n        }\n        sk += k._.r, k = k.previous;\n      }\n    } while (j !== k.next);\n\n    // Success! Insert the new circle c between a and b.\n    c.previous = a, c.next = b, a.next = b.previous = b = c;\n\n    // Compute the new closest circle pair to the centroid.\n    aa = score(a);\n    while ((c = c.next) !== b) {\n      if ((ca = score(c)) < aa) {\n        a = c, aa = ca;\n      }\n    }\n    b = a.next;\n  }\n\n  // Compute the enclosing circle of the front chain.\n  a = [b._], c = b; while ((c = c.next) !== b) a.push(c._); c = packEncloseRandom(a, random);\n\n  // Translate the circles to put the enclosing circle around the origin.\n  for (i = 0; i < n; ++i) a = circles[i], a.x -= c.x, a.y -= c.y;\n\n  return c.r;\n}\n\nexport default function(circles) {\n  packSiblingsRandom(circles, lcg());\n  return circles;\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,aAAa;AAC/B,OAAOC,GAAG,MAAM,WAAW;AAC3B,SAAQC,iBAAiB,QAAO,cAAc;AAE9C,SAASC,KAAKA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EACtB,IAAIC,EAAE,GAAGH,CAAC,CAACI,CAAC,GAAGH,CAAC,CAACG,CAAC;IAAEA,CAAC;IAAEC,EAAE;IACrBC,EAAE,GAAGN,CAAC,CAACO,CAAC,GAAGN,CAAC,CAACM,CAAC;IAAEA,CAAC;IAAEC,EAAE;IACrBC,EAAE,GAAGN,EAAE,GAAGA,EAAE,GAAGG,EAAE,GAAGA,EAAE;EAC1B,IAAIG,EAAE,EAAE;IACNJ,EAAE,GAAGJ,CAAC,CAACS,CAAC,GAAGR,CAAC,CAACQ,CAAC,EAAEL,EAAE,IAAIA,EAAE;IACxBG,EAAE,GAAGR,CAAC,CAACU,CAAC,GAAGR,CAAC,CAACQ,CAAC,EAAEF,EAAE,IAAIA,EAAE;IACxB,IAAIH,EAAE,GAAGG,EAAE,EAAE;MACXJ,CAAC,GAAG,CAACK,EAAE,GAAGD,EAAE,GAAGH,EAAE,KAAK,CAAC,GAAGI,EAAE,CAAC;MAC7BF,CAAC,GAAGI,IAAI,CAACC,IAAI,CAACD,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEL,EAAE,GAAGC,EAAE,GAAGL,CAAC,GAAGA,CAAC,CAAC,CAAC;MAC3CF,CAAC,CAACE,CAAC,GAAGJ,CAAC,CAACI,CAAC,GAAGA,CAAC,GAAGD,EAAE,GAAGI,CAAC,GAAGD,EAAE;MAC3BJ,CAAC,CAACK,CAAC,GAAGP,CAAC,CAACO,CAAC,GAAGH,CAAC,GAAGE,EAAE,GAAGC,CAAC,GAAGJ,EAAE;IAC7B,CAAC,MAAM;MACLC,CAAC,GAAG,CAACK,EAAE,GAAGJ,EAAE,GAAGG,EAAE,KAAK,CAAC,GAAGC,EAAE,CAAC;MAC7BF,CAAC,GAAGI,IAAI,CAACC,IAAI,CAACD,IAAI,CAACE,GAAG,CAAC,CAAC,EAAER,EAAE,GAAGI,EAAE,GAAGL,CAAC,GAAGA,CAAC,CAAC,CAAC;MAC3CF,CAAC,CAACE,CAAC,GAAGH,CAAC,CAACG,CAAC,GAAGA,CAAC,GAAGD,EAAE,GAAGI,CAAC,GAAGD,EAAE;MAC3BJ,CAAC,CAACK,CAAC,GAAGN,CAAC,CAACM,CAAC,GAAGH,CAAC,GAAGE,EAAE,GAAGC,CAAC,GAAGJ,EAAE;IAC7B;EACF,CAAC,MAAM;IACLD,CAAC,CAACE,CAAC,GAAGH,CAAC,CAACG,CAAC,GAAGF,CAAC,CAACQ,CAAC;IACfR,CAAC,CAACK,CAAC,GAAGN,CAAC,CAACM,CAAC;EACX;AACF;AAEA,SAASO,UAAUA,CAACb,CAAC,EAAED,CAAC,EAAE;EACxB,IAAIe,EAAE,GAAGd,CAAC,CAACS,CAAC,GAAGV,CAAC,CAACU,CAAC,GAAG,IAAI;IAAEP,EAAE,GAAGH,CAAC,CAACI,CAAC,GAAGH,CAAC,CAACG,CAAC;IAAEE,EAAE,GAAGN,CAAC,CAACO,CAAC,GAAGN,CAAC,CAACM,CAAC;EACzD,OAAOQ,EAAE,GAAG,CAAC,IAAIA,EAAE,GAAGA,EAAE,GAAGZ,EAAE,GAAGA,EAAE,GAAGG,EAAE,GAAGA,EAAE;AAC9C;AAEA,SAASU,KAAKA,CAACC,IAAI,EAAE;EACnB,IAAIhB,CAAC,GAAGgB,IAAI,CAACC,CAAC;IACVlB,CAAC,GAAGiB,IAAI,CAACE,IAAI,CAACD,CAAC;IACfE,EAAE,GAAGnB,CAAC,CAACS,CAAC,GAAGV,CAAC,CAACU,CAAC;IACdP,EAAE,GAAG,CAACF,CAAC,CAACG,CAAC,GAAGJ,CAAC,CAACU,CAAC,GAAGV,CAAC,CAACI,CAAC,GAAGH,CAAC,CAACS,CAAC,IAAIU,EAAE;IACjCd,EAAE,GAAG,CAACL,CAAC,CAACM,CAAC,GAAGP,CAAC,CAACU,CAAC,GAAGV,CAAC,CAACO,CAAC,GAAGN,CAAC,CAACS,CAAC,IAAIU,EAAE;EACrC,OAAOjB,EAAE,GAAGA,EAAE,GAAGG,EAAE,GAAGA,EAAE;AAC1B;AAEA,SAASe,IAAIA,CAACC,MAAM,EAAE;EACpB,IAAI,CAACJ,CAAC,GAAGI,MAAM;EACf,IAAI,CAACH,IAAI,GAAG,IAAI;EAChB,IAAI,CAACI,QAAQ,GAAG,IAAI;AACtB;AAEA,OAAO,SAASC,kBAAkBA,CAACC,OAAO,EAAEC,MAAM,EAAE;EAClD,IAAI,EAAEC,CAAC,GAAG,CAACF,OAAO,GAAG7B,KAAK,CAAC6B,OAAO,CAAC,EAAEG,MAAM,CAAC,EAAE,OAAO,CAAC;EAEtD,IAAI3B,CAAC,EAAED,CAAC,EAAEE,CAAC,EAAEyB,CAAC,EAAEE,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,EAAE,EAAEC,EAAE;;EAEvC;EACAlC,CAAC,GAAGwB,OAAO,CAAC,CAAC,CAAC,EAAExB,CAAC,CAACG,CAAC,GAAG,CAAC,EAAEH,CAAC,CAACM,CAAC,GAAG,CAAC;EAChC,IAAI,EAAEoB,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO1B,CAAC,CAACS,CAAC;;EAExB;EACAV,CAAC,GAAGyB,OAAO,CAAC,CAAC,CAAC,EAAExB,CAAC,CAACG,CAAC,GAAG,CAACJ,CAAC,CAACU,CAAC,EAAEV,CAAC,CAACI,CAAC,GAAGH,CAAC,CAACS,CAAC,EAAEV,CAAC,CAACO,CAAC,GAAG,CAAC;EAC9C,IAAI,EAAEoB,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO1B,CAAC,CAACS,CAAC,GAAGV,CAAC,CAACU,CAAC;;EAE9B;EACAX,KAAK,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,GAAGuB,OAAO,CAAC,CAAC,CAAC,CAAC;;EAE3B;EACAxB,CAAC,GAAG,IAAIoB,IAAI,CAACpB,CAAC,CAAC,EAAED,CAAC,GAAG,IAAIqB,IAAI,CAACrB,CAAC,CAAC,EAAEE,CAAC,GAAG,IAAImB,IAAI,CAACnB,CAAC,CAAC;EACjDD,CAAC,CAACkB,IAAI,GAAGjB,CAAC,CAACqB,QAAQ,GAAGvB,CAAC;EACvBA,CAAC,CAACmB,IAAI,GAAGlB,CAAC,CAACsB,QAAQ,GAAGrB,CAAC;EACvBA,CAAC,CAACiB,IAAI,GAAGnB,CAAC,CAACuB,QAAQ,GAAGtB,CAAC;;EAEvB;EACAmC,IAAI,EAAE,KAAKL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,CAAC,EAAE,EAAEI,CAAC,EAAE;IAC5BhC,KAAK,CAACE,CAAC,CAACiB,CAAC,EAAElB,CAAC,CAACkB,CAAC,EAAEhB,CAAC,GAAGuB,OAAO,CAACM,CAAC,CAAC,CAAC,EAAE7B,CAAC,GAAG,IAAImB,IAAI,CAACnB,CAAC,CAAC;;IAEhD;IACA;IACA;IACA8B,CAAC,GAAGhC,CAAC,CAACmB,IAAI,EAAEc,CAAC,GAAGhC,CAAC,CAACsB,QAAQ,EAAEW,EAAE,GAAGlC,CAAC,CAACkB,CAAC,CAACR,CAAC,EAAEyB,EAAE,GAAGlC,CAAC,CAACiB,CAAC,CAACR,CAAC;IAClD,GAAG;MACD,IAAIwB,EAAE,IAAIC,EAAE,EAAE;QACZ,IAAIrB,UAAU,CAACkB,CAAC,CAACd,CAAC,EAAEhB,CAAC,CAACgB,CAAC,CAAC,EAAE;UACxBlB,CAAC,GAAGgC,CAAC,EAAE/B,CAAC,CAACkB,IAAI,GAAGnB,CAAC,EAAEA,CAAC,CAACuB,QAAQ,GAAGtB,CAAC,EAAE,EAAE8B,CAAC;UACtC,SAASK,IAAI;QACf;QACAF,EAAE,IAAIF,CAAC,CAACd,CAAC,CAACR,CAAC,EAAEsB,CAAC,GAAGA,CAAC,CAACb,IAAI;MACzB,CAAC,MAAM;QACL,IAAIL,UAAU,CAACmB,CAAC,CAACf,CAAC,EAAEhB,CAAC,CAACgB,CAAC,CAAC,EAAE;UACxBjB,CAAC,GAAGgC,CAAC,EAAEhC,CAAC,CAACkB,IAAI,GAAGnB,CAAC,EAAEA,CAAC,CAACuB,QAAQ,GAAGtB,CAAC,EAAE,EAAE8B,CAAC;UACtC,SAASK,IAAI;QACf;QACAD,EAAE,IAAIF,CAAC,CAACf,CAAC,CAACR,CAAC,EAAEuB,CAAC,GAAGA,CAAC,CAACV,QAAQ;MAC7B;IACF,CAAC,QAAQS,CAAC,KAAKC,CAAC,CAACd,IAAI;;IAErB;IACAjB,CAAC,CAACqB,QAAQ,GAAGtB,CAAC,EAAEC,CAAC,CAACiB,IAAI,GAAGnB,CAAC,EAAEC,CAAC,CAACkB,IAAI,GAAGnB,CAAC,CAACuB,QAAQ,GAAGvB,CAAC,GAAGE,CAAC;;IAEvD;IACA2B,EAAE,GAAGb,KAAK,CAACf,CAAC,CAAC;IACb,OAAO,CAACC,CAAC,GAAGA,CAAC,CAACiB,IAAI,MAAMnB,CAAC,EAAE;MACzB,IAAI,CAAC8B,EAAE,GAAGd,KAAK,CAACd,CAAC,CAAC,IAAI2B,EAAE,EAAE;QACxB5B,CAAC,GAAGC,CAAC,EAAE2B,EAAE,GAAGC,EAAE;MAChB;IACF;IACA9B,CAAC,GAAGC,CAAC,CAACkB,IAAI;EACZ;;EAEA;EACAlB,CAAC,GAAG,CAACD,CAAC,CAACkB,CAAC,CAAC,EAAEhB,CAAC,GAAGF,CAAC;EAAE,OAAO,CAACE,CAAC,GAAGA,CAAC,CAACiB,IAAI,MAAMnB,CAAC,EAAEC,CAAC,CAACoC,IAAI,CAACnC,CAAC,CAACgB,CAAC,CAAC;EAAEhB,CAAC,GAAGJ,iBAAiB,CAACG,CAAC,EAAEyB,MAAM,CAAC;;EAE1F;EACA,KAAKK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,CAAC,EAAE,EAAEI,CAAC,EAAE9B,CAAC,GAAGwB,OAAO,CAACM,CAAC,CAAC,EAAE9B,CAAC,CAACG,CAAC,IAAIF,CAAC,CAACE,CAAC,EAAEH,CAAC,CAACM,CAAC,IAAIL,CAAC,CAACK,CAAC;EAE9D,OAAOL,CAAC,CAACQ,CAAC;AACZ;AAEA,eAAe,UAASe,OAAO,EAAE;EAC/BD,kBAAkB,CAACC,OAAO,EAAE5B,GAAG,CAAC,CAAC,CAAC;EAClC,OAAO4B,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}