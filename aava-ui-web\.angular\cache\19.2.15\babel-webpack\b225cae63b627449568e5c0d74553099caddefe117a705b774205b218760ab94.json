{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { map } from 'rxjs';\nimport { environment } from '@shared/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let PromptEnhanceService = /*#__PURE__*/(() => {\n  class PromptEnhanceService {\n    http;\n    apiServiceUrl = environment.consoleInstructionApi;\n    defaultStr = '/ava/force/individualAgent/execute';\n    defaultUserSignature = '<EMAIL>'; // Default user signature, can be customized\n    constructor(http) {\n      this.http = http;\n    }\n    modelApi(prompt, mode = '', promptOverride = false, useCaseIdentifier = '', yamlContent, colangContent) {\n      let payload = {\n        prompt: prompt,\n        mode: mode,\n        //Prompt Optimization\n        promptOverride: promptOverride,\n        useCaseIdentifier: useCaseIdentifier,\n        userSignature: this.defaultUserSignature\n      };\n      //colang and yaml are optional parameters for create-guardrails screen validateCode API\n      if (colangContent) {\n        payload.colangContent = colangContent;\n      }\n      if (yamlContent) {\n        payload.yamlContent = yamlContent;\n      }\n      console.log(payload, \"payload\");\n      const headers = {\n        headers: new HttpHeaders({\n          'Content-Type': 'application/json'\n        })\n      };\n      const url = `${this.apiServiceUrl}${this.defaultStr}`;\n      return this.http.post(url, payload, headers).pipe(map(response => {\n        return response;\n      }));\n    }\n    modelGuardrailApi(prompt, mode = '', promptOverride = false, yamlContent, colangContent) {\n      let payload = {\n        prompt: prompt,\n        mode: mode,\n        //Prompt Optimization\n        promptOverride: promptOverride,\n        colangContent: colangContent,\n        yamlContent: yamlContent,\n        userSignature: this.defaultUserSignature\n      };\n      //colang and yaml are optional parameters for create-guardrails screen validateCode API\n      console.log(payload, \"payload\");\n      const headers = {\n        headers: new HttpHeaders({\n          'Content-Type': 'application/json'\n        })\n      };\n      const url = `${this.apiServiceUrl}${this.defaultStr}`;\n      return this.http.post(url, payload, headers).pipe(map(response => {\n        return response;\n      }));\n    }\n    static ɵfac = function PromptEnhanceService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PromptEnhanceService)(i0.ɵɵinject(i1.HttpClient));\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: PromptEnhanceService,\n      factory: PromptEnhanceService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return PromptEnhanceService;\n})();", "map": {"version": 3, "names": ["HttpHeaders", "map", "environment", "PromptEnhanceService", "http", "apiServiceUrl", "consoleInstructionApi", "defaultStr", "defaultUserSignature", "constructor", "modelApi", "prompt", "mode", "promptOverride", "useCaseIdentifier", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "payload", "userSignature", "console", "log", "headers", "url", "post", "pipe", "response", "modelGuardrailApi", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\services\\prompt-enhance.service.ts"], "sourcesContent": ["import { HttpClient, HttpHeaders } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { map } from 'rxjs';\r\nimport { environment } from '@shared/environments/environment';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class PromptEnhanceService {\r\n\r\n  private apiServiceUrl = environment.consoleInstructionApi;\r\n  private defaultStr = '/ava/force/individualAgent/execute';\r\n  private defaultUserSignature = '<EMAIL>'; // Default user signature, can be customized\r\n\r\n  constructor(private http: HttpClient) { }\r\n\r\n  public modelApi(prompt: any, mode: string = '', promptOverride: boolean = false, useCaseIdentifier: string = '',yamlContent?:string,colangContent?:string) {\r\n    let payload: any = {\r\n      prompt: prompt,\r\n      mode: mode, //Prompt Optimization\r\n      promptOverride: promptOverride,\r\n      useCaseIdentifier: useCaseIdentifier,\r\n      userSignature: this.defaultUserSignature,\r\n    };\r\n    //colang and yaml are optional parameters for create-guardrails screen validateCode API\r\n    if(colangContent ){\r\n      payload.colangContent = colangContent;\r\n    }\r\n    if(yamlContent ){\r\n      payload.yamlContent = yamlContent;\r\n    }\r\n    console.log(payload,\"payload\");\r\n    const headers = { headers: new HttpHeaders({\r\n      'Content-Type': 'application/json',\r\n    })};\r\n    const url = `${this.apiServiceUrl}${this.defaultStr}`;\r\n\r\n    return this.http.post(url, payload, headers).pipe(\r\n      map((response: any) => {\r\n          return response;\r\n      })\r\n    );\r\n  }\r\n\r\n  public modelGuardrailApi(prompt: any, mode: string = '', promptOverride: boolean = false, yamlContent?:string, colangContent?:string){\r\n    let payload: any = {\r\n      prompt: prompt,\r\n      mode: mode, //Prompt Optimization\r\n      promptOverride: promptOverride,\r\n      colangContent: colangContent,\r\n      yamlContent: yamlContent,\r\n      userSignature: this.defaultUserSignature,\r\n    };\r\n    //colang and yaml are optional parameters for create-guardrails screen validateCode API\r\n\r\n    console.log(payload,\"payload\");\r\n    const headers = { headers: new HttpHeaders({\r\n      'Content-Type': 'application/json',\r\n    })};\r\n    const url = `${this.apiServiceUrl}${this.defaultStr}`;\r\n\r\n    return this.http.post(url, payload, headers).pipe(\r\n      map((response: any) => {\r\n          return response;\r\n      })\r\n    );\r\n  }\r\n}"], "mappings": "AAAA,SAAqBA,WAAW,QAAQ,sBAAsB;AAE9D,SAASC,GAAG,QAAQ,MAAM;AAC1B,SAASC,WAAW,QAAQ,kCAAkC;;;AAK9D,WAAaC,oBAAoB;EAA3B,MAAOA,oBAAoB;IAMXC,IAAA;IAJZC,aAAa,GAAGH,WAAW,CAACI,qBAAqB;IACjDC,UAAU,GAAG,oCAAoC;IACjDC,oBAAoB,GAAG,uBAAuB,CAAC,CAAC;IAExDC,YAAoBL,IAAgB;MAAhB,KAAAA,IAAI,GAAJA,IAAI;IAAgB;IAEjCM,QAAQA,CAACC,MAAW,EAAEC,IAAA,GAAe,EAAE,EAAEC,cAAA,GAA0B,KAAK,EAAEC,iBAAA,GAA4B,EAAE,EAACC,WAAmB,EAACC,aAAqB;MACvJ,IAAIC,OAAO,GAAQ;QACjBN,MAAM,EAAEA,MAAM;QACdC,IAAI,EAAEA,IAAI;QAAE;QACZC,cAAc,EAAEA,cAAc;QAC9BC,iBAAiB,EAAEA,iBAAiB;QACpCI,aAAa,EAAE,IAAI,CAACV;OACrB;MACD;MACA,IAAGQ,aAAa,EAAE;QAChBC,OAAO,CAACD,aAAa,GAAGA,aAAa;MACvC;MACA,IAAGD,WAAW,EAAE;QACdE,OAAO,CAACF,WAAW,GAAGA,WAAW;MACnC;MACAI,OAAO,CAACC,GAAG,CAACH,OAAO,EAAC,SAAS,CAAC;MAC9B,MAAMI,OAAO,GAAG;QAAEA,OAAO,EAAE,IAAIrB,WAAW,CAAC;UACzC,cAAc,EAAE;SACjB;MAAC,CAAC;MACH,MAAMsB,GAAG,GAAG,GAAG,IAAI,CAACjB,aAAa,GAAG,IAAI,CAACE,UAAU,EAAE;MAErD,OAAO,IAAI,CAACH,IAAI,CAACmB,IAAI,CAACD,GAAG,EAAEL,OAAO,EAAEI,OAAO,CAAC,CAACG,IAAI,CAC/CvB,GAAG,CAAEwB,QAAa,IAAI;QAClB,OAAOA,QAAQ;MACnB,CAAC,CAAC,CACH;IACH;IAEOC,iBAAiBA,CAACf,MAAW,EAAEC,IAAA,GAAe,EAAE,EAAEC,cAAA,GAA0B,KAAK,EAAEE,WAAmB,EAAEC,aAAqB;MAClI,IAAIC,OAAO,GAAQ;QACjBN,MAAM,EAAEA,MAAM;QACdC,IAAI,EAAEA,IAAI;QAAE;QACZC,cAAc,EAAEA,cAAc;QAC9BG,aAAa,EAAEA,aAAa;QAC5BD,WAAW,EAAEA,WAAW;QACxBG,aAAa,EAAE,IAAI,CAACV;OACrB;MACD;MAEAW,OAAO,CAACC,GAAG,CAACH,OAAO,EAAC,SAAS,CAAC;MAC9B,MAAMI,OAAO,GAAG;QAAEA,OAAO,EAAE,IAAIrB,WAAW,CAAC;UACzC,cAAc,EAAE;SACjB;MAAC,CAAC;MACH,MAAMsB,GAAG,GAAG,GAAG,IAAI,CAACjB,aAAa,GAAG,IAAI,CAACE,UAAU,EAAE;MAErD,OAAO,IAAI,CAACH,IAAI,CAACmB,IAAI,CAACD,GAAG,EAAEL,OAAO,EAAEI,OAAO,CAAC,CAACG,IAAI,CAC/CvB,GAAG,CAAEwB,QAAa,IAAI;QAClB,OAAOA,QAAQ;MACnB,CAAC,CAAC,CACH;IACH;;uCA1DWtB,oBAAoB,EAAAwB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;;aAApB3B,oBAAoB;MAAA4B,OAAA,EAApB5B,oBAAoB,CAAA6B,IAAA;MAAAC,UAAA,EAFnB;IAAM;;SAEP9B,oBAAoB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}