{"ast": null, "code": "export default function BrushEvent(type, {\n  sourceEvent,\n  target,\n  selection,\n  mode,\n  dispatch\n}) {\n  Object.defineProperties(this, {\n    type: {\n      value: type,\n      enumerable: true,\n      configurable: true\n    },\n    sourceEvent: {\n      value: sourceEvent,\n      enumerable: true,\n      configurable: true\n    },\n    target: {\n      value: target,\n      enumerable: true,\n      configurable: true\n    },\n    selection: {\n      value: selection,\n      enumerable: true,\n      configurable: true\n    },\n    mode: {\n      value: mode,\n      enumerable: true,\n      configurable: true\n    },\n    _: {\n      value: dispatch\n    }\n  });\n}", "map": {"version": 3, "names": ["BrushEvent", "type", "sourceEvent", "target", "selection", "mode", "dispatch", "Object", "defineProperties", "value", "enumerable", "configurable", "_"], "sources": ["C:/console/aava-ui-web/node_modules/d3-brush/src/event.js"], "sourcesContent": ["export default function BrushEvent(type, {\n  sourceEvent,\n  target,\n  selection,\n  mode,\n  dispatch\n}) {\n  Object.defineProperties(this, {\n    type: {value: type, enumerable: true, configurable: true},\n    sourceEvent: {value: sourceEvent, enumerable: true, configurable: true},\n    target: {value: target, enumerable: true, configurable: true},\n    selection: {value: selection, enumerable: true, configurable: true},\n    mode: {value: mode, enumerable: true, configurable: true},\n    _: {value: dispatch}\n  });\n}\n"], "mappings": "AAAA,eAAe,SAASA,UAAUA,CAACC,IAAI,EAAE;EACvCC,WAAW;EACXC,MAAM;EACNC,SAAS;EACTC,IAAI;EACJC;AACF,CAAC,EAAE;EACDC,MAAM,CAACC,gBAAgB,CAAC,IAAI,EAAE;IAC5BP,IAAI,EAAE;MAACQ,KAAK,EAAER,IAAI;MAAES,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE;IAAI,CAAC;IACzDT,WAAW,EAAE;MAACO,KAAK,EAAEP,WAAW;MAAEQ,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE;IAAI,CAAC;IACvER,MAAM,EAAE;MAACM,KAAK,EAAEN,MAAM;MAAEO,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE;IAAI,CAAC;IAC7DP,SAAS,EAAE;MAACK,KAAK,EAAEL,SAAS;MAAEM,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE;IAAI,CAAC;IACnEN,IAAI,EAAE;MAACI,KAAK,EAAEJ,IAAI;MAAEK,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE;IAAI,CAAC;IACzDC,CAAC,EAAE;MAACH,KAAK,EAAEH;IAAQ;EACrB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}