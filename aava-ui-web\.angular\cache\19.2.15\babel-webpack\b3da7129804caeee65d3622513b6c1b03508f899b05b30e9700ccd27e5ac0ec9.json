{"ast": null, "code": "import { input } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nconst _c0 = [[[\"\", \"panel-header\", \"\"]], [[\"\", \"panel-content\", \"\"]], [[\"\", \"panel-footer\", \"\"]]];\nconst _c1 = [\"[panel-header]\", \"[panel-content]\", \"[panel-footer]\"];\nfunction PreviewPanelComponent_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 2);\n  }\n}\nfunction PreviewPanelComponent_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 2);\n  }\n}\nexport let PreviewPanelComponent = /*#__PURE__*/(() => {\n  class PreviewPanelComponent {\n    divider = input(true);\n    showFooter = true;\n    static ɵfac = function PreviewPanelComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PreviewPanelComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PreviewPanelComponent,\n      selectors: [[\"app-preview-panel\"]],\n      inputs: {\n        divider: [1, \"divider\"],\n        showFooter: \"showFooter\"\n      },\n      ngContentSelectors: _c1,\n      decls: 9,\n      vars: 2,\n      consts: [[1, \"preview-panel\"], [1, \"preview-header\"], [1, \"divider\"], [1, \"preview-content\"], [1, \"preview-footer\"]],\n      template: function PreviewPanelComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c0);\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵprojection(2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(3, PreviewPanelComponent_Conditional_3_Template, 1, 0, \"div\", 2);\n          i0.ɵɵelementStart(4, \"div\", 3);\n          i0.ɵɵprojection(5, 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(6, PreviewPanelComponent_Conditional_6_Template, 1, 0, \"div\", 2);\n          i0.ɵɵelementStart(7, \"div\", 4);\n          i0.ɵɵprojection(8, 2);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵconditional(ctx.divider() ? 3 : -1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵconditional(ctx.divider() ? 6 : -1);\n        }\n      },\n      styles: [\".preview-panel[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  right: 0;\\n  width: 600px;\\n  height: 100vh;\\n  background: #fff;\\n  box-shadow: -2px 0 12px rgba(0, 0, 0, 0.08);\\n  display: flex;\\n  z-index: 200;\\n  flex-direction: column;\\n  padding: 0;\\n  border-left: 1px solid #eee;\\n}\\n\\n.preview-header[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  width: 100%;\\n  min-height: 56px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 20px 24px 0 24px;\\n}\\n\\n.divider[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 2px;\\n  background: #b4b4b4;\\n  margin: 20px 0;\\n}\\n\\n.preview-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  width: 100%;\\n  overflow-y: auto;\\n  padding: 0 32px;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.preview-footer[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  width: 100%;\\n  padding: 0 24px 20px 24px;\\n  box-sizing: border-box;\\n}\\n\\n@media (max-width: 600px) {\\n  .preview-panel[_ngcontent-%COMP%] {\\n    width: 100vw;\\n    left: 0;\\n    right: 0;\\n    padding: 16px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return PreviewPanelComponent;\n})();", "map": {"version": 3, "names": ["input", "i0", "ɵɵelement", "PreviewPanelComponent", "divider", "showFooter", "selectors", "inputs", "ngContentSelectors", "_c1", "decls", "vars", "consts", "template", "PreviewPanelComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵprojection", "ɵɵelementEnd", "ɵɵtemplate", "PreviewPanelComponent_Conditional_3_Template", "PreviewPanelComponent_Conditional_6_Template", "ɵɵadvance", "ɵɵconditional"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\shared\\components\\preview-panel\\preview-panel.component.ts", "C:\\console\\aava-ui-web\\projects\\console\\src\\app\\shared\\components\\preview-panel\\preview-panel.component.html"], "sourcesContent": ["import { Component, input, Input } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-preview-panel',\r\n  imports: [],\r\n  templateUrl: './preview-panel.component.html',\r\n  styleUrl: './preview-panel.component.scss'\r\n})\r\nexport class PreviewPanelComponent {\r\n  divider = input<boolean>(true);\r\n@Input() showFooter: boolean = true;\r\n}\r\n", "<div class=\"preview-panel\">\r\n  <div class=\"preview-header\">\r\n    <ng-content select=\"[panel-header]\"></ng-content>\r\n  </div>\r\n  @if(divider()) {\r\n    <div class=\"divider\"></div>\r\n  }\r\n  <div class=\"preview-content\">\r\n    <ng-content select=\"[panel-content]\"></ng-content>\r\n  </div>\r\n  @if(divider()) {\r\n    <div class=\"divider\"></div>\r\n  }\r\n  <div class=\"preview-footer\">\r\n    <ng-content select=\"[panel-footer]\"></ng-content>\r\n  </div>\r\n</div>"], "mappings": "AAAA,SAAoBA,KAAK,QAAe,eAAe;;;;;;ICKnDC,EAAA,CAAAC,SAAA,aAA2B;;;;;IAM3BD,EAAA,CAAAC,SAAA,aAA2B;;;ADH/B,WAAaC,qBAAqB;EAA5B,MAAOA,qBAAqB;IAChCC,OAAO,GAAGJ,KAAK,CAAU,IAAI,CAAC;IACvBK,UAAU,GAAY,IAAI;;uCAFtBF,qBAAqB;IAAA;;YAArBA,qBAAqB;MAAAG,SAAA;MAAAC,MAAA;QAAAH,OAAA;QAAAC,UAAA;MAAA;MAAAG,kBAAA,EAAAC,GAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCPhCd,EADF,CAAAgB,cAAA,aAA2B,aACG;UAC1BhB,EAAA,CAAAiB,YAAA,GAAiD;UACnDjB,EAAA,CAAAkB,YAAA,EAAM;UACNlB,EAAA,CAAAmB,UAAA,IAAAC,4CAAA,iBAAgB;UAGhBpB,EAAA,CAAAgB,cAAA,aAA6B;UAC3BhB,EAAA,CAAAiB,YAAA,MAAkD;UACpDjB,EAAA,CAAAkB,YAAA,EAAM;UACNlB,EAAA,CAAAmB,UAAA,IAAAE,4CAAA,iBAAgB;UAGhBrB,EAAA,CAAAgB,cAAA,aAA4B;UAC1BhB,EAAA,CAAAiB,YAAA,MAAiD;UAErDjB,EADE,CAAAkB,YAAA,EAAM,EACF;;;UAZJlB,EAAA,CAAAsB,SAAA,GAEC;UAFDtB,EAAA,CAAAuB,aAAA,CAAAR,GAAA,CAAAZ,OAAA,YAEC;UAIDH,EAAA,CAAAsB,SAAA,GAEC;UAFDtB,EAAA,CAAAuB,aAAA,CAAAR,GAAA,CAAAZ,OAAA,YAEC;;;;;;SDJUD,qBAAqB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}