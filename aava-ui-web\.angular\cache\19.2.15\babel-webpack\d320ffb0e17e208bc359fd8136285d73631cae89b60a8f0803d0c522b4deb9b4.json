{"ast": null, "code": "export default function (polygon) {\n  var i = -1,\n    n = polygon.length,\n    a,\n    b = polygon[n - 1],\n    area = 0;\n  while (++i < n) {\n    a = b;\n    b = polygon[i];\n    area += a[1] * b[0] - a[0] * b[1];\n  }\n  return area / 2;\n}", "map": {"version": 3, "names": ["polygon", "i", "n", "length", "a", "b", "area"], "sources": ["C:/console/aava-ui-web/node_modules/d3-polygon/src/area.js"], "sourcesContent": ["export default function(polygon) {\n  var i = -1,\n      n = polygon.length,\n      a,\n      b = polygon[n - 1],\n      area = 0;\n\n  while (++i < n) {\n    a = b;\n    b = polygon[i];\n    area += a[1] * b[0] - a[0] * b[1];\n  }\n\n  return area / 2;\n}\n"], "mappings": "AAAA,eAAe,UAASA,OAAO,EAAE;EAC/B,IAAIC,CAAC,GAAG,CAAC,CAAC;IACNC,CAAC,GAAGF,OAAO,CAACG,MAAM;IAClBC,CAAC;IACDC,CAAC,GAAGL,OAAO,CAACE,CAAC,GAAG,CAAC,CAAC;IAClBI,IAAI,GAAG,CAAC;EAEZ,OAAO,EAAEL,CAAC,GAAGC,CAAC,EAAE;IACdE,CAAC,GAAGC,CAAC;IACLA,CAAC,GAAGL,OAAO,CAACC,CAAC,CAAC;IACdK,IAAI,IAAIF,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC;EACnC;EAEA,OAAOC,IAAI,GAAG,CAAC;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}