{"ast": null, "code": "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\nexport var scheme = new Array(3).concat(\"fee8c8fdbb84e34a33\", \"fef0d9fdcc8afc8d59d7301f\", \"fef0d9fdcc8afc8d59e34a33b30000\", \"fef0d9fdd49efdbb84fc8d59e34a33b30000\", \"fef0d9fdd49efdbb84fc8d59ef6548d7301f990000\", \"fff7ecfee8c8fdd49efdbb84fc8d59ef6548d7301f990000\", \"fff7ecfee8c8fdd49efdbb84fc8d59ef6548d7301fb300007f0000\").map(colors);\nexport default ramp(scheme);", "map": {"version": 3, "names": ["colors", "ramp", "scheme", "Array", "concat", "map"], "sources": ["C:/console/aava-ui-web/node_modules/d3-scale-chromatic/src/sequential-multi/OrRd.js"], "sourcesContent": ["import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"fee8c8fdbb84e34a33\",\n  \"fef0d9fdcc8afc8d59d7301f\",\n  \"fef0d9fdcc8afc8d59e34a33b30000\",\n  \"fef0d9fdd49efdbb84fc8d59e34a33b30000\",\n  \"fef0d9fdd49efdbb84fc8d59ef6548d7301f990000\",\n  \"fff7ecfee8c8fdd49efdbb84fc8d59ef6548d7301f990000\",\n  \"fff7ecfee8c8fdd49efdbb84fc8d59ef6548d7301fb300007f0000\"\n).map(colors);\n\nexport default ramp(scheme);\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,cAAc;AACjC,OAAOC,IAAI,MAAM,YAAY;AAE7B,OAAO,IAAIC,MAAM,GAAG,IAAIC,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,CACrC,oBAAoB,EACpB,0BAA0B,EAC1B,gCAAgC,EAChC,sCAAsC,EACtC,4CAA4C,EAC5C,kDAAkD,EAClD,wDACF,CAAC,CAACC,GAAG,CAACL,MAAM,CAAC;AAEb,eAAeC,IAAI,CAACC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}