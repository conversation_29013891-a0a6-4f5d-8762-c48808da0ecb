{"ast": null, "code": "self[\"MonacoEnvironment\"] = function (paths) {\n  function stripTrailingSlash(str) {\n    return str.replace(/\\/$/, '');\n  }\n  return {\n    globalAPI: false,\n    getWorkerUrl: function (moduleId, label) {\n      var pathPrefix = typeof __webpack_public_path__ === 'string' ? __webpack_public_path__ : \"auto\";\n      var result = (pathPrefix ? stripTrailingSlash(pathPrefix) + '/' : '') + paths[label];\n      if (/^((http:)|(https:)|(file:)|(\\/\\/))/.test(result)) {\n        var currentUrl = String(window.location);\n        var currentOrigin = currentUrl.substr(0, currentUrl.length - window.location.hash.length - window.location.search.length - window.location.pathname.length);\n        if (result.substring(0, currentOrigin.length) !== currentOrigin) {\n          if (/^(\\/\\/)/.test(result)) {\n            result = window.location.protocol + result;\n          }\n          var js = '/*' + label + '*/importScripts(\"' + result + '\");';\n          var blob = new Blob([js], {\n            type: 'application/javascript'\n          });\n          return URL.createObjectURL(blob);\n        }\n      }\n      return result;\n    }\n  };\n}({\n  \"editorWorkerService\": \"editor.worker.js\",\n  \"typescript\": \"ts.worker.js\",\n  \"json\": \"json.worker.js\",\n  \"html\": \"html.worker.js\",\n  \"css\": \"css.worker.js\",\n  \"javascript\": \"ts.worker.js\",\n  \"less\": \"css.worker.js\",\n  \"scss\": \"css.worker.js\",\n  \"handlebars\": \"html.worker.js\",\n  \"razor\": \"html.worker.js\"\n});\nimport \"./contrib/anchorSelect/browser/anchorSelect.js\";\nimport \"./contrib/bracketMatching/browser/bracketMatching.js\";\nimport \"./browser/coreCommands.js\";\nimport \"./contrib/caretOperations/browser/caretOperations.js\";\nimport \"./contrib/caretOperations/browser/transpose.js\";\nimport \"./contrib/clipboard/browser/clipboard.js\";\nimport \"./contrib/codeAction/browser/codeActionContributions.js\";\nimport \"./browser/widget/codeEditor/codeEditorWidget.js\";\nimport \"./contrib/codelens/browser/codelensController.js\";\nimport \"./contrib/colorPicker/browser/colorContributions.js\";\nimport \"./contrib/colorPicker/browser/standaloneColorPickerActions.js\";\nimport \"./contrib/comment/browser/comment.js\";\nimport \"./contrib/contextmenu/browser/contextmenu.js\";\nimport \"./contrib/cursorUndo/browser/cursorUndo.js\";\nimport \"./browser/widget/diffEditor/diffEditor.contribution.js\";\nimport \"./contrib/diffEditorBreadcrumbs/browser/contribution.js\";\nimport \"./contrib/dnd/browser/dnd.js\";\nimport \"./contrib/documentSymbols/browser/documentSymbols.js\";\nimport \"./contrib/dropOrPasteInto/browser/copyPasteContribution.js\";\nimport \"./contrib/dropOrPasteInto/browser/dropIntoEditorContribution.js\";\nimport \"./contrib/find/browser/findController.js\";\nimport \"./contrib/folding/browser/folding.js\";\nimport \"./contrib/fontZoom/browser/fontZoom.js\";\nimport \"./contrib/format/browser/formatActions.js\";\nimport \"./contrib/gotoError/browser/gotoError.js\";\nimport \"./standalone/browser/quickAccess/standaloneGotoLineQuickAccess.js\";\nimport \"./contrib/gotoSymbol/browser/goToCommands.js\";\nimport \"./contrib/gotoSymbol/browser/link/goToDefinitionAtPosition.js\";\nimport \"./contrib/hover/browser/hoverContribution.js\";\nimport \"./standalone/browser/iPadShowKeyboard/iPadShowKeyboard.js\";\nimport \"./contrib/inPlaceReplace/browser/inPlaceReplace.js\";\nimport \"./contrib/indentation/browser/indentation.js\";\nimport \"./contrib/inlayHints/browser/inlayHintsContribution.js\";\nimport \"./contrib/inlineCompletions/browser/inlineCompletions.contribution.js\";\nimport \"./contrib/inlineEdit/browser/inlineEdit.contribution.js\";\nimport \"./contrib/inlineEdits/browser/inlineEdits.contribution.js\";\nimport \"./contrib/inlineProgress/browser/inlineProgress.js\";\nimport \"./standalone/browser/inspectTokens/inspectTokens.js\";\nimport \"./contrib/lineSelection/browser/lineSelection.js\";\nimport \"./contrib/linesOperations/browser/linesOperations.js\";\nimport \"./contrib/linkedEditing/browser/linkedEditing.js\";\nimport \"./contrib/links/browser/links.js\";\nimport \"./contrib/longLinesHelper/browser/longLinesHelper.js\";\nimport \"./contrib/multicursor/browser/multicursor.js\";\nimport \"./contrib/parameterHints/browser/parameterHints.js\";\nimport \"./contrib/placeholderText/browser/placeholderText.contribution.js\";\nimport \"./standalone/browser/quickAccess/standaloneCommandsQuickAccess.js\";\nimport \"./standalone/browser/quickAccess/standaloneHelpQuickAccess.js\";\nimport \"./standalone/browser/quickAccess/standaloneGotoSymbolQuickAccess.js\";\nimport \"./contrib/readOnlyMessage/browser/contribution.js\";\nimport \"./standalone/browser/referenceSearch/standaloneReferenceSearch.js\";\nimport \"./contrib/rename/browser/rename.js\";\nimport \"./contrib/sectionHeaders/browser/sectionHeaders.js\";\nimport \"./contrib/semanticTokens/browser/documentSemanticTokens.js\";\nimport \"./contrib/semanticTokens/browser/viewportSemanticTokens.js\";\nimport \"./contrib/smartSelect/browser/smartSelect.js\";\nimport \"./contrib/snippet/browser/snippetController2.js\";\nimport \"./contrib/stickyScroll/browser/stickyScrollContribution.js\";\nimport \"./contrib/suggest/browser/suggestController.js\";\nimport \"./contrib/suggest/browser/suggestInlineCompletions.js\";\nimport \"./standalone/browser/toggleHighContrast/toggleHighContrast.js\";\nimport \"./contrib/toggleTabFocusMode/browser/toggleTabFocusMode.js\";\nimport \"./contrib/tokenization/browser/tokenization.js\";\nimport \"./contrib/unicodeHighlighter/browser/unicodeHighlighter.js\";\nimport \"./contrib/unusualLineTerminators/browser/unusualLineTerminators.js\";\nimport \"./contrib/wordHighlighter/browser/wordHighlighter.js\";\nimport \"./contrib/wordOperations/browser/wordOperations.js\";\nimport \"./contrib/wordPartOperations/browser/wordPartOperations.js\";\nimport * as monaco from \"!!../../../../source-map-loader/dist/cjs.js??ruleSet[1].rules[3]!./editor.api.js\";\nexport * from \"!!../../../../source-map-loader/dist/cjs.js??ruleSet[1].rules[3]!./editor.api.js\";\nexport default monaco;\nimport \"../basic-languages/python/python.contribution.js\";\nimport \"../basic-languages/javascript/javascript.contribution.js\";\nimport \"../basic-languages/typescript/typescript.contribution.js\";\nimport \"../language/typescript/monaco.contribution.js\";\nimport \"../language/json/monaco.contribution.js\";\nimport \"../basic-languages/sql/sql.contribution.js\";\nimport \"../basic-languages/html/html.contribution.js\";\nimport \"../language/html/monaco.contribution.js\";\nimport \"../basic-languages/css/css.contribution.js\";\nimport \"../language/css/monaco.contribution.js\";\nimport \"../basic-languages/markdown/markdown.contribution.js\";\nimport \"../basic-languages/yaml/yaml.contribution.js\";\nimport \"../basic-languages/xml/xml.contribution.js\";", "map": {"version": 3, "names": ["self", "paths", "stripTrailingSlash", "str", "replace", "globalAPI", "getWorkerUrl", "moduleId", "label", "pathPrefix", "__webpack_public_path__", "result", "test", "currentUrl", "String", "window", "location", "<PERSON><PERSON><PERSON><PERSON>", "substr", "length", "hash", "search", "pathname", "substring", "protocol", "js", "blob", "Blob", "type", "URL", "createObjectURL", "monaco"], "sources": ["C:/console/aava-ui-web/node_modules/monaco-editor/esm/vs/editor/editor.api.js"], "sourcesContent": ["self[\"MonacoEnvironment\"] = (function (paths) {\n      function stripTrailingSlash(str) {\n        return str.replace(/\\/$/, '');\n      }\n      return {\n        globalAPI: false,\n        getWorkerUrl: function (moduleId, label) {\n          var pathPrefix = typeof __webpack_public_path__ === 'string' ? __webpack_public_path__ : \"auto\";\n          var result = (pathPrefix ? stripTrailingSlash(pathPrefix) + '/' : '') + paths[label];\n          if (/^((http:)|(https:)|(file:)|(\\/\\/))/.test(result)) {\n            var currentUrl = String(window.location);\n            var currentOrigin = currentUrl.substr(0, currentUrl.length - window.location.hash.length - window.location.search.length - window.location.pathname.length);\n            if (result.substring(0, currentOrigin.length) !== currentOrigin) {\n              if(/^(\\/\\/)/.test(result)) {\n                result = window.location.protocol + result\n              }\n              var js = '/*' + label + '*/importScripts(\"' + result + '\");';\n              var blob = new Blob([js], { type: 'application/javascript' });\n              return URL.createObjectURL(blob);\n            }\n          }\n          return result;\n        }\n      };\n    })({\n  \"editorWorkerService\": \"editor.worker.js\",\n  \"typescript\": \"ts.worker.js\",\n  \"json\": \"json.worker.js\",\n  \"html\": \"html.worker.js\",\n  \"css\": \"css.worker.js\",\n  \"javascript\": \"ts.worker.js\",\n  \"less\": \"css.worker.js\",\n  \"scss\": \"css.worker.js\",\n  \"handlebars\": \"html.worker.js\",\n  \"razor\": \"html.worker.js\"\n});\nimport \"./contrib/anchorSelect/browser/anchorSelect.js\";\nimport \"./contrib/bracketMatching/browser/bracketMatching.js\";\nimport \"./browser/coreCommands.js\";\nimport \"./contrib/caretOperations/browser/caretOperations.js\";\nimport \"./contrib/caretOperations/browser/transpose.js\";\nimport \"./contrib/clipboard/browser/clipboard.js\";\nimport \"./contrib/codeAction/browser/codeActionContributions.js\";\nimport \"./browser/widget/codeEditor/codeEditorWidget.js\";\nimport \"./contrib/codelens/browser/codelensController.js\";\nimport \"./contrib/colorPicker/browser/colorContributions.js\";\nimport \"./contrib/colorPicker/browser/standaloneColorPickerActions.js\";\nimport \"./contrib/comment/browser/comment.js\";\nimport \"./contrib/contextmenu/browser/contextmenu.js\";\nimport \"./contrib/cursorUndo/browser/cursorUndo.js\";\nimport \"./browser/widget/diffEditor/diffEditor.contribution.js\";\nimport \"./contrib/diffEditorBreadcrumbs/browser/contribution.js\";\nimport \"./contrib/dnd/browser/dnd.js\";\nimport \"./contrib/documentSymbols/browser/documentSymbols.js\";\nimport \"./contrib/dropOrPasteInto/browser/copyPasteContribution.js\";\nimport \"./contrib/dropOrPasteInto/browser/dropIntoEditorContribution.js\";\nimport \"./contrib/find/browser/findController.js\";\nimport \"./contrib/folding/browser/folding.js\";\nimport \"./contrib/fontZoom/browser/fontZoom.js\";\nimport \"./contrib/format/browser/formatActions.js\";\nimport \"./contrib/gotoError/browser/gotoError.js\";\nimport \"./standalone/browser/quickAccess/standaloneGotoLineQuickAccess.js\";\nimport \"./contrib/gotoSymbol/browser/goToCommands.js\";\nimport \"./contrib/gotoSymbol/browser/link/goToDefinitionAtPosition.js\";\nimport \"./contrib/hover/browser/hoverContribution.js\";\nimport \"./standalone/browser/iPadShowKeyboard/iPadShowKeyboard.js\";\nimport \"./contrib/inPlaceReplace/browser/inPlaceReplace.js\";\nimport \"./contrib/indentation/browser/indentation.js\";\nimport \"./contrib/inlayHints/browser/inlayHintsContribution.js\";\nimport \"./contrib/inlineCompletions/browser/inlineCompletions.contribution.js\";\nimport \"./contrib/inlineEdit/browser/inlineEdit.contribution.js\";\nimport \"./contrib/inlineEdits/browser/inlineEdits.contribution.js\";\nimport \"./contrib/inlineProgress/browser/inlineProgress.js\";\nimport \"./standalone/browser/inspectTokens/inspectTokens.js\";\nimport \"./contrib/lineSelection/browser/lineSelection.js\";\nimport \"./contrib/linesOperations/browser/linesOperations.js\";\nimport \"./contrib/linkedEditing/browser/linkedEditing.js\";\nimport \"./contrib/links/browser/links.js\";\nimport \"./contrib/longLinesHelper/browser/longLinesHelper.js\";\nimport \"./contrib/multicursor/browser/multicursor.js\";\nimport \"./contrib/parameterHints/browser/parameterHints.js\";\nimport \"./contrib/placeholderText/browser/placeholderText.contribution.js\";\nimport \"./standalone/browser/quickAccess/standaloneCommandsQuickAccess.js\";\nimport \"./standalone/browser/quickAccess/standaloneHelpQuickAccess.js\";\nimport \"./standalone/browser/quickAccess/standaloneGotoSymbolQuickAccess.js\";\nimport \"./contrib/readOnlyMessage/browser/contribution.js\";\nimport \"./standalone/browser/referenceSearch/standaloneReferenceSearch.js\";\nimport \"./contrib/rename/browser/rename.js\";\nimport \"./contrib/sectionHeaders/browser/sectionHeaders.js\";\nimport \"./contrib/semanticTokens/browser/documentSemanticTokens.js\";\nimport \"./contrib/semanticTokens/browser/viewportSemanticTokens.js\";\nimport \"./contrib/smartSelect/browser/smartSelect.js\";\nimport \"./contrib/snippet/browser/snippetController2.js\";\nimport \"./contrib/stickyScroll/browser/stickyScrollContribution.js\";\nimport \"./contrib/suggest/browser/suggestController.js\";\nimport \"./contrib/suggest/browser/suggestInlineCompletions.js\";\nimport \"./standalone/browser/toggleHighContrast/toggleHighContrast.js\";\nimport \"./contrib/toggleTabFocusMode/browser/toggleTabFocusMode.js\";\nimport \"./contrib/tokenization/browser/tokenization.js\";\nimport \"./contrib/unicodeHighlighter/browser/unicodeHighlighter.js\";\nimport \"./contrib/unusualLineTerminators/browser/unusualLineTerminators.js\";\nimport \"./contrib/wordHighlighter/browser/wordHighlighter.js\";\nimport \"./contrib/wordOperations/browser/wordOperations.js\";\nimport \"./contrib/wordPartOperations/browser/wordPartOperations.js\";\n\nimport * as monaco from \"!!../../../../source-map-loader/dist/cjs.js??ruleSet[1].rules[3]!./editor.api.js\";\nexport * from \"!!../../../../source-map-loader/dist/cjs.js??ruleSet[1].rules[3]!./editor.api.js\";\nexport default monaco;\n\t\t\nimport \"../basic-languages/python/python.contribution.js\";\nimport \"../basic-languages/javascript/javascript.contribution.js\";\nimport \"../basic-languages/typescript/typescript.contribution.js\";\nimport \"../language/typescript/monaco.contribution.js\";\nimport \"../language/json/monaco.contribution.js\";\nimport \"../basic-languages/sql/sql.contribution.js\";\nimport \"../basic-languages/html/html.contribution.js\";\nimport \"../language/html/monaco.contribution.js\";\nimport \"../basic-languages/css/css.contribution.js\";\nimport \"../language/css/monaco.contribution.js\";\nimport \"../basic-languages/markdown/markdown.contribution.js\";\nimport \"../basic-languages/yaml/yaml.contribution.js\";\nimport \"../basic-languages/xml/xml.contribution.js\";"], "mappings": "AAAAA,IAAI,CAAC,mBAAmB,CAAC,GAAI,UAAUC,KAAK,EAAE;EACxC,SAASC,kBAAkBA,CAACC,GAAG,EAAE;IAC/B,OAAOA,GAAG,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;EAC/B;EACA,OAAO;IACLC,SAAS,EAAE,KAAK;IAChBC,YAAY,EAAE,SAAAA,CAAUC,QAAQ,EAAEC,KAAK,EAAE;MACvC,IAAIC,UAAU,GAAG,OAAOC,uBAAuB,KAAK,QAAQ,GAAGA,uBAAuB,GAAG,MAAM;MAC/F,IAAIC,MAAM,GAAG,CAACF,UAAU,GAAGP,kBAAkB,CAACO,UAAU,CAAC,GAAG,GAAG,GAAG,EAAE,IAAIR,KAAK,CAACO,KAAK,CAAC;MACpF,IAAI,oCAAoC,CAACI,IAAI,CAACD,MAAM,CAAC,EAAE;QACrD,IAAIE,UAAU,GAAGC,MAAM,CAACC,MAAM,CAACC,QAAQ,CAAC;QACxC,IAAIC,aAAa,GAAGJ,UAAU,CAACK,MAAM,CAAC,CAAC,EAAEL,UAAU,CAACM,MAAM,GAAGJ,MAAM,CAACC,QAAQ,CAACI,IAAI,CAACD,MAAM,GAAGJ,MAAM,CAACC,QAAQ,CAACK,MAAM,CAACF,MAAM,GAAGJ,MAAM,CAACC,QAAQ,CAACM,QAAQ,CAACH,MAAM,CAAC;QAC3J,IAAIR,MAAM,CAACY,SAAS,CAAC,CAAC,EAAEN,aAAa,CAACE,MAAM,CAAC,KAAKF,aAAa,EAAE;UAC/D,IAAG,SAAS,CAACL,IAAI,CAACD,MAAM,CAAC,EAAE;YACzBA,MAAM,GAAGI,MAAM,CAACC,QAAQ,CAACQ,QAAQ,GAAGb,MAAM;UAC5C;UACA,IAAIc,EAAE,GAAG,IAAI,GAAGjB,KAAK,GAAG,mBAAmB,GAAGG,MAAM,GAAG,KAAK;UAC5D,IAAIe,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACF,EAAE,CAAC,EAAE;YAAEG,IAAI,EAAE;UAAyB,CAAC,CAAC;UAC7D,OAAOC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;QAClC;MACF;MACA,OAAOf,MAAM;IACf;EACF,CAAC;AACH,CAAC,CAAE;EACL,qBAAqB,EAAE,kBAAkB;EACzC,YAAY,EAAE,cAAc;EAC5B,MAAM,EAAE,gBAAgB;EACxB,MAAM,EAAE,gBAAgB;EACxB,KAAK,EAAE,eAAe;EACtB,YAAY,EAAE,cAAc;EAC5B,MAAM,EAAE,eAAe;EACvB,MAAM,EAAE,eAAe;EACvB,YAAY,EAAE,gBAAgB;EAC9B,OAAO,EAAE;AACX,CAAC,CAAC;AACF,OAAO,gDAAgD;AACvD,OAAO,sDAAsD;AAC7D,OAAO,2BAA2B;AAClC,OAAO,sDAAsD;AAC7D,OAAO,gDAAgD;AACvD,OAAO,0CAA0C;AACjD,OAAO,yDAAyD;AAChE,OAAO,iDAAiD;AACxD,OAAO,kDAAkD;AACzD,OAAO,qDAAqD;AAC5D,OAAO,+DAA+D;AACtE,OAAO,sCAAsC;AAC7C,OAAO,8CAA8C;AACrD,OAAO,4CAA4C;AACnD,OAAO,wDAAwD;AAC/D,OAAO,yDAAyD;AAChE,OAAO,8BAA8B;AACrC,OAAO,sDAAsD;AAC7D,OAAO,4DAA4D;AACnE,OAAO,iEAAiE;AACxE,OAAO,0CAA0C;AACjD,OAAO,sCAAsC;AAC7C,OAAO,wCAAwC;AAC/C,OAAO,2CAA2C;AAClD,OAAO,0CAA0C;AACjD,OAAO,mEAAmE;AAC1E,OAAO,8CAA8C;AACrD,OAAO,+DAA+D;AACtE,OAAO,8CAA8C;AACrD,OAAO,2DAA2D;AAClE,OAAO,oDAAoD;AAC3D,OAAO,8CAA8C;AACrD,OAAO,wDAAwD;AAC/D,OAAO,uEAAuE;AAC9E,OAAO,yDAAyD;AAChE,OAAO,2DAA2D;AAClE,OAAO,oDAAoD;AAC3D,OAAO,qDAAqD;AAC5D,OAAO,kDAAkD;AACzD,OAAO,sDAAsD;AAC7D,OAAO,kDAAkD;AACzD,OAAO,kCAAkC;AACzC,OAAO,sDAAsD;AAC7D,OAAO,8CAA8C;AACrD,OAAO,oDAAoD;AAC3D,OAAO,mEAAmE;AAC1E,OAAO,mEAAmE;AAC1E,OAAO,+DAA+D;AACtE,OAAO,qEAAqE;AAC5E,OAAO,mDAAmD;AAC1D,OAAO,mEAAmE;AAC1E,OAAO,oCAAoC;AAC3C,OAAO,oDAAoD;AAC3D,OAAO,4DAA4D;AACnE,OAAO,4DAA4D;AACnE,OAAO,8CAA8C;AACrD,OAAO,iDAAiD;AACxD,OAAO,4DAA4D;AACnE,OAAO,gDAAgD;AACvD,OAAO,uDAAuD;AAC9D,OAAO,+DAA+D;AACtE,OAAO,4DAA4D;AACnE,OAAO,gDAAgD;AACvD,OAAO,4DAA4D;AACnE,OAAO,oEAAoE;AAC3E,OAAO,sDAAsD;AAC7D,OAAO,oDAAoD;AAC3D,OAAO,4DAA4D;AAEnE,OAAO,KAAKoB,MAAM,MAAM,kFAAkF;AAC1G,cAAc,kFAAkF;AAChG,eAAeA,MAAM;AAErB,OAAO,kDAAkD;AACzD,OAAO,0DAA0D;AACjE,OAAO,0DAA0D;AACjE,OAAO,+CAA+C;AACtD,OAAO,yCAAyC;AAChD,OAAO,4CAA4C;AACnD,OAAO,8CAA8C;AACrD,OAAO,yCAAyC;AAChD,OAAO,4CAA4C;AACnD,OAAO,wCAAwC;AAC/C,OAAO,sDAAsD;AAC7D,OAAO,8CAA8C;AACrD,OAAO,4CAA4C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}