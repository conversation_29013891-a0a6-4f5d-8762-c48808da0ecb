{"ast": null, "code": "export default function (event) {\n  let sourceEvent;\n  while (sourceEvent = event.sourceEvent) event = sourceEvent;\n  return event;\n}", "map": {"version": 3, "names": ["event", "sourceEvent"], "sources": ["C:/console/aava-ui-web/node_modules/d3-selection/src/sourceEvent.js"], "sourcesContent": ["export default function(event) {\n  let sourceEvent;\n  while (sourceEvent = event.sourceEvent) event = sourceEvent;\n  return event;\n}\n"], "mappings": "AAAA,eAAe,UAASA,KAAK,EAAE;EAC7B,IAAIC,WAAW;EACf,OAAOA,WAAW,GAAGD,KAAK,CAACC,WAAW,EAAED,KAAK,GAAGC,WAAW;EAC3D,OAAOD,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}