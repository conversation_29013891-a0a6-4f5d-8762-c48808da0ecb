{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { inject } from '@angular/core';\nimport { AvaTagComponent, ButtonComponent, IconComponent } from '@ava/play-comp-library';\nimport { PreviewPanelComponent } from 'projects/console/src/app/shared/components/preview-panel/preview-panel.component';\nimport { Router } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nconst _c0 = () => ({\n  background: \"white\",\n  color: \"#DC2626\",\n  border: \"1px solid #DC2626\"\n});\nconst _c1 = () => ({\n  background: \"linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)\",\n  \"--button-effect-color\": \"33, 90, 214\"\n});\nconst _c2 = () => ({\n  background: \"#EDEDF3\",\n  color: \"#2D3036\"\n});\nfunction UserManagementPreviewPanelComponent_For_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ava-tag\", 12);\n  }\n  if (rf & 2) {\n    const realm_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"label\", realm_r1)(\"customStyle\", i0.ɵɵpureFunction0(3, _c2))(\"pill\", true);\n  }\n}\nexport let UserManagementPreviewPanelComponent = /*#__PURE__*/(() => {\n  class UserManagementPreviewPanelComponent {\n    metaData;\n    closePreview;\n    onParentAction;\n    router = inject(Router);\n    onButtonClick(event) {\n      this.closePreview();\n    }\n    onUpdateUser() {\n      this.closePreview();\n      this.router.navigate(['manage/admin-management/add-user'], {\n        queryParams: {\n          id: this.metaData?.userId,\n          mode: 'edit'\n        }\n      });\n    }\n    onRemoveUser() {\n      if (this.onParentAction && this.metaData) {\n        this.closePreview();\n        this.onParentAction(this.metaData.userId);\n      }\n    }\n    static ɵfac = function UserManagementPreviewPanelComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || UserManagementPreviewPanelComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: UserManagementPreviewPanelComponent,\n      selectors: [[\"app-user-management-preview-panel\"]],\n      inputs: {\n        metaData: \"metaData\",\n        closePreview: \"closePreview\",\n        onParentAction: \"onParentAction\"\n      },\n      decls: 43,\n      vars: 13,\n      consts: [[1, \"backdrop\", 3, \"click\"], [1, \"panel-container\", 3, \"click\", \"divider\"], [\"panel-header\", \"\", 1, \"preview-header\"], [1, \"panel-title\"], [\"iconName\", \"x\", \"iconColor\", \"black\", 1, \"close-btn\", 3, \"click\", \"cursor\"], [\"panel-content\", \"\", 1, \"preview-body\"], [1, \"row-container\"], [1, \"meta-label\"], [1, \"meta-value\"], [1, \"realm-container\"], [1, \"realm-title\"], [1, \"realm-tag\"], [\"size\", \"sm\", 3, \"label\", \"customStyle\", \"pill\"], [\"panel-footer\", \"\"], [1, \"action__button--container\"], [\"label\", \"Remove User\", \"variant\", \"primary\", \"size\", \"large\", \"iconName\", \"trash\", \"iconPosition\", \"right\", \"iconColor\", \"#DC2626\", 1, \"action-button\", 3, \"userClick\", \"customStyles\", \"width\"], [\"label\", \"Edit User Details\", \"variant\", \"primary\", \"size\", \"large\", \"iconName\", \"pencil\", \"iconPosition\", \"right\", \"iconColor\", \"white\", 1, \"action-button\", 3, \"userClick\", \"customStyles\", \"width\"]],\n      template: function UserManagementPreviewPanelComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵlistener(\"click\", function UserManagementPreviewPanelComponent_Template_div_click_0_listener() {\n            return ctx.closePreview();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(1, \"app-preview-panel\", 1);\n          i0.ɵɵlistener(\"click\", function UserManagementPreviewPanelComponent_Template_app_preview_panel_click_1_listener($event) {\n            return $event.stopPropagation();\n          });\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"h2\", 3);\n          i0.ɵɵtext(4, \"Metadata Information\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"ava-icon\", 4);\n          i0.ɵɵlistener(\"click\", function UserManagementPreviewPanelComponent_Template_ava_icon_click_5_listener() {\n            return ctx.closePreview();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6)(8, \"div\")(9, \"div\", 7);\n          i0.ɵɵtext(10, \"User Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 8);\n          i0.ɵɵtext(12);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"div\")(14, \"div\", 7);\n          i0.ɵɵtext(15, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"div\", 8);\n          i0.ɵɵtext(17);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\")(19, \"div\", 7);\n          i0.ɵɵtext(20, \"Role\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"div\", 8);\n          i0.ɵɵtext(22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"div\")(24, \"div\", 7);\n          i0.ɵɵtext(25, \"Authorized By\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"div\", 8);\n          i0.ɵɵtext(27);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"div\")(29, \"div\", 7);\n          i0.ɵɵtext(30, \"Created On\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"div\", 8);\n          i0.ɵɵtext(32);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(33, \"div\", 9)(34, \"div\", 10);\n          i0.ɵɵtext(35, \"Realms Assigned\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"div\", 11);\n          i0.ɵɵrepeaterCreate(37, UserManagementPreviewPanelComponent_For_38_Template, 1, 4, \"ava-tag\", 12, i0.ɵɵrepeaterTrackByIdentity);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(39, \"div\", 13)(40, \"div\", 14)(41, \"ava-button\", 15);\n          i0.ɵɵlistener(\"userClick\", function UserManagementPreviewPanelComponent_Template_ava_button_userClick_41_listener() {\n            return ctx.onRemoveUser();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"ava-button\", 16);\n          i0.ɵɵlistener(\"userClick\", function UserManagementPreviewPanelComponent_Template_ava_button_userClick_42_listener() {\n            return ctx.onUpdateUser();\n          });\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"divider\", false);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"cursor\", true);\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate(ctx.metaData == null ? null : ctx.metaData.userName);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.metaData == null ? null : ctx.metaData.email);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.metaData == null ? null : ctx.metaData.roles);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.metaData == null ? null : ctx.metaData.authorizedBy);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.metaData == null ? null : ctx.metaData.createdAt);\n          i0.ɵɵadvance(5);\n          i0.ɵɵrepeater(ctx.metaData == null ? null : ctx.metaData.realms);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"customStyles\", i0.ɵɵpureFunction0(11, _c0))(\"width\", \"100%\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"customStyles\", i0.ɵɵpureFunction0(12, _c1))(\"width\", \"100%\");\n        }\n      },\n      dependencies: [ButtonComponent, IconComponent, CommonModule, PreviewPanelComponent, AvaTagComponent],\n      styles: [\".backdrop[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: rgba(0, 0, 0, 0.5);\\n  z-index: 999;\\n  cursor: pointer;\\n}\\n\\n.panel-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1000;\\n}\\n\\n.preview-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  width: 100%;\\n  min-height: 56px;\\n  border-bottom: 1px solid #9CA1AA;\\n}\\n.preview-header[_ngcontent-%COMP%]   .panel-title[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #111;\\n  text-align: left;\\n  margin: 0;\\n  line-height: 1.2;\\n  flex: 1 1 auto;\\n}\\n\\n.preview-body[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n}\\n.preview-body[_ngcontent-%COMP%]   .row-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 24px;\\n}\\n\\n.meta-label[_ngcontent-%COMP%], \\n.meta-value[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  font-weight: 400;\\n  color: #111;\\n  font-family: \\\"Mulish\\\";\\n}\\n\\n.meta-label[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n}\\n\\n.realm-container[_ngcontent-%COMP%] {\\n  margin-top: 40px;\\n}\\n.realm-container[_ngcontent-%COMP%]   .realm-title[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  color: #111;\\n}\\n.realm-container[_ngcontent-%COMP%]   .realm-tag[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  margin: 10px 0 0 0;\\n  flex-wrap: wrap;\\n}\\n\\n.action__button--container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  justify-content: space-between;\\n}\\n.action__button--container[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return UserManagementPreviewPanelComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "inject", "AvaTagComponent", "ButtonComponent", "IconComponent", "PreviewPanelComponent", "Router", "i0", "ɵɵelement", "ɵɵproperty", "realm_r1", "ɵɵpureFunction0", "_c2", "UserManagementPreviewPanelComponent", "metaData", "closePreview", "onParentAction", "router", "onButtonClick", "event", "onUpdateUser", "navigate", "queryParams", "id", "userId", "mode", "onRemoveUser", "selectors", "inputs", "decls", "vars", "consts", "template", "UserManagementPreviewPanelComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵlistener", "UserManagementPreviewPanelComponent_Template_div_click_0_listener", "ɵɵelementEnd", "UserManagementPreviewPanelComponent_Template_app_preview_panel_click_1_listener", "$event", "stopPropagation", "ɵɵtext", "UserManagementPreviewPanelComponent_Template_ava_icon_click_5_listener", "ɵɵrepeaterCreate", "UserManagementPreviewPanelComponent_For_38_Template", "ɵɵrepeaterTrackByIdentity", "UserManagementPreviewPanelComponent_Template_ava_button_userClick_41_listener", "UserManagementPreviewPanelComponent_Template_ava_button_userClick_42_listener", "ɵɵadvance", "ɵɵtextInterpolate", "userName", "email", "roles", "authorizedBy", "createdAt", "ɵɵrepeater", "realms", "_c0", "_c1", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\manage\\admin-management\\components\\view-user-management\\renderer\\user-management-preview-panel.component.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\r\nimport { Component, inject, Input } from '@angular/core';\r\nimport { AvaTagComponent, ButtonComponent, IconComponent } from '@ava/play-comp-library';\r\nimport { PreviewPanelComponent } from 'projects/console/src/app/shared/components/preview-panel/preview-panel.component';\r\nimport { User } from '../../../models/user-management.model';\r\nimport { Router } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-user-management-preview-panel',\r\n  standalone: true,\r\n  imports: [\r\n    ButtonComponent,\r\n    IconComponent,\r\n    CommonModule,\r\n    PreviewPanelComponent,\r\n    AvaTagComponent\r\n    ],\r\n  template: `\r\n    <div class=\"backdrop\" (click)=\"closePreview()\"></div>\r\n    <app-preview-panel class=\"panel-container\" (click)=\"$event.stopPropagation()\" [divider]=\"false\">\r\n        <div panel-header class=\"preview-header\">\r\n            <h2 class=\"panel-title\">Metadata Information</h2>\r\n            <ava-icon iconName=\"x\" [cursor]=\"true\" iconColor=\"black\" class=\"close-btn\" (click)=\"closePreview()\"></ava-icon>\r\n        </div>\r\n        \r\n        <div panel-content class=\"preview-body\">\r\n            <div class=\"row-container\">\r\n                <div>\r\n                    <div class=\"meta-label\">User Name</div>\r\n                    <div class=\"meta-value\">{{metaData?.userName}}</div>\r\n                </div>\r\n                <div>\r\n                    <div class=\"meta-label\">Email</div>\r\n                    <div class=\"meta-value\">{{metaData?.email}}</div>\r\n                </div>\r\n                <div>\r\n                    <div class=\"meta-label\">Role</div>\r\n                    <div class=\"meta-value\">{{metaData?.roles}}</div>\r\n                </div>\r\n                <div>\r\n                    <div class=\"meta-label\">Authorized By</div>\r\n                    <div class=\"meta-value\">{{metaData?.authorizedBy}}</div>\r\n                </div>\r\n                <div>\r\n                    <div class=\"meta-label\">Created On</div>\r\n                    <div class=\"meta-value\">{{metaData?.createdAt}}</div>\r\n                </div>\r\n            </div>\r\n            <div class=\"realm-container\">\r\n                <div class=\"realm-title\">Realms Assigned</div>\r\n                <div class=\"realm-tag\"> \r\n                    @for(realm of metaData?.realms; track realm){\r\n                        <ava-tag [label]=\"realm\" [customStyle]=\"{ background: '#EDEDF3', color: '#2D3036' }\" [pill]=\"true\" size=\"sm\" ></ava-tag>\r\n                    }\r\n                </div>\r\n            </div>\r\n        </div>\r\n        \r\n        <div panel-footer >\r\n            <div class=\"action__button--container\"> \r\n                <ava-button\r\n                   label=\"Remove User\"\r\n                   variant=\"primary\"\r\n                   [customStyles]=\"{ background: 'white' , color: '#DC2626', border: '1px solid #DC2626' }\"\r\n                   size=\"large\"\r\n                   iconName=\"trash\"\r\n                   iconPosition= \"right\"\r\n                   iconColor=\"#DC2626\"\r\n                   (userClick)=\"onRemoveUser()\"\r\n                   [width]=\"'100%'\"\r\n                   class=\"action-button\"\r\n               >\r\n               </ava-button>\r\n               <ava-button\r\n                   label=\"Edit User Details\"\r\n                   variant=\"primary\"\r\n                   [customStyles]=\"{\r\n                   background: 'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n                   '--button-effect-color': '33, 90, 214',\r\n                   }\"\r\n                   size=\"large\"\r\n                   iconName=\"pencil\"\r\n                   iconPosition= \"right\"\r\n                   iconColor=\"white\"\r\n                   (userClick)=\"onUpdateUser()\"\r\n                   [width]=\"'100%'\"\r\n                   class=\"action-button\"\r\n               >\r\n               </ava-button>\r\n            </div>\r\n        </div>\r\n    </app-preview-panel>\r\n  `,\r\n  styles: [\r\n    `\r\n    .backdrop {\r\n        position: fixed;\r\n        top: 0;\r\n        left: 0;\r\n        width: 100%;\r\n        height: 100%;\r\n        background-color: rgba(0, 0, 0, 0.5);\r\n        z-index: 999;\r\n        cursor: pointer;\r\n    }\r\n    .panel-container {\r\n        position: relative;\r\n        z-index: 1000;\r\n    }\r\n    .preview-header {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        width: 100%;\r\n        min-height: 56px;\r\n        border-bottom: 1px solid #9CA1AA;\r\n        .panel-title {\r\n            font-weight: 600;\r\n            color: #111;\r\n            text-align: left;\r\n            margin: 0;\r\n            line-height: 1.2;\r\n            flex: 1 1 auto;\r\n        }\r\n    }\r\n    .preview-body{\r\n        margin-top: 20px;\r\n        .row-container {\r\n            display: flex;\r\n            flex-direction: column;\r\n            gap: 24px;\r\n        }\r\n    }\r\n    .meta-label,\r\n    .meta-value  {\r\n        font-size: 1rem;\r\n        font-weight: 400;\r\n        color: #111;\r\n        font-family: \"Mulish\"\r\n    }\r\n    .meta-label {\r\n        font-weight: 700;\r\n    }\r\n    .realm-container {\r\n        margin-top: 40px;\r\n        .realm-title {\r\n            font-size: 1.5rem;\r\n            font-weight: 700;\r\n            color: #111;\r\n        }\r\n        .realm-tag {\r\n            display: flex;\r\n            gap: 8px;\r\n            margin: 10px 0 0 0;\r\n            flex-wrap: wrap;\r\n        }\r\n    }\r\n    .action__button--container {\r\n        display: flex;\r\n        gap: 12px;\r\n        justify-content: space-between;\r\n        .action-button {\r\n            width: 100%;\r\n        }\r\n    }\r\n    `,\r\n  ],\r\n})\r\nexport class UserManagementPreviewPanelComponent {\r\n  @Input() metaData?: User;\r\n  @Input() closePreview!: () => void;\r\n  @Input() onParentAction?: (id: string | number) => void;\r\n\r\n  private router = inject(Router);\r\n  \r\n  onButtonClick(event: any): void {\r\n    this.closePreview();\r\n  }\r\n\r\n  onUpdateUser() {\r\n    this.closePreview();\r\n    this.router.navigate(['manage/admin-management/add-user'], { queryParams: {id: this.metaData?.userId, mode:'edit'}} );\r\n  }\r\n\r\n  onRemoveUser() {\r\n    if (this.onParentAction && this.metaData) {\r\n        this.closePreview();\r\n        this.onParentAction(this.metaData.userId);\r\n    }\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAoBC,MAAM,QAAe,eAAe;AACxD,SAASC,eAAe,EAAEC,eAAe,EAAEC,aAAa,QAAQ,wBAAwB;AACxF,SAASC,qBAAqB,QAAQ,kFAAkF;AAExH,SAASC,MAAM,QAAQ,iBAAiB;;;;;;;;;;;;;;;;;IA+ChBC,EAAA,CAAAC,SAAA,kBAAwH;;;;IAAnCD,EAA5E,CAAAE,UAAA,UAAAC,QAAA,CAAe,gBAAAH,EAAA,CAAAI,eAAA,IAAAC,GAAA,EAA4D,cAAc;;;AAoH1H,WAAaC,mCAAmC;EAA1C,MAAOA,mCAAmC;IACrCC,QAAQ;IACRC,YAAY;IACZC,cAAc;IAEfC,MAAM,GAAGhB,MAAM,CAACK,MAAM,CAAC;IAE/BY,aAAaA,CAACC,KAAU;MACtB,IAAI,CAACJ,YAAY,EAAE;IACrB;IAEAK,YAAYA,CAAA;MACV,IAAI,CAACL,YAAY,EAAE;MACnB,IAAI,CAACE,MAAM,CAACI,QAAQ,CAAC,CAAC,kCAAkC,CAAC,EAAE;QAAEC,WAAW,EAAE;UAACC,EAAE,EAAE,IAAI,CAACT,QAAQ,EAAEU,MAAM;UAAEC,IAAI,EAAC;QAAM;MAAC,CAAC,CAAE;IACvH;IAEAC,YAAYA,CAAA;MACV,IAAI,IAAI,CAACV,cAAc,IAAI,IAAI,CAACF,QAAQ,EAAE;QACtC,IAAI,CAACC,YAAY,EAAE;QACnB,IAAI,CAACC,cAAc,CAAC,IAAI,CAACF,QAAQ,CAACU,MAAM,CAAC;MAC7C;IACF;;uCArBWX,mCAAmC;IAAA;;YAAnCA,mCAAmC;MAAAc,SAAA;MAAAC,MAAA;QAAAd,QAAA;QAAAC,YAAA;QAAAC,cAAA;MAAA;MAAAa,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6CAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAtJ5C3B,EAAA,CAAA6B,cAAA,aAA+C;UAAzB7B,EAAA,CAAA8B,UAAA,mBAAAC,kEAAA;YAAA,OAASH,GAAA,CAAApB,YAAA,EAAc;UAAA,EAAC;UAACR,EAAA,CAAAgC,YAAA,EAAM;UACrDhC,EAAA,CAAA6B,cAAA,2BAAgG;UAArD7B,EAAA,CAAA8B,UAAA,mBAAAG,gFAAAC,MAAA;YAAA,OAASA,MAAA,CAAAC,eAAA,EAAwB;UAAA,EAAC;UAErEnC,EADJ,CAAA6B,cAAA,aAAyC,YACb;UAAA7B,EAAA,CAAAoC,MAAA,2BAAoB;UAAApC,EAAA,CAAAgC,YAAA,EAAK;UACjDhC,EAAA,CAAA6B,cAAA,kBAAoG;UAAzB7B,EAAA,CAAA8B,UAAA,mBAAAO,uEAAA;YAAA,OAAST,GAAA,CAAApB,YAAA,EAAc;UAAA,EAAC;UACvGR,EADwG,CAAAgC,YAAA,EAAW,EAC7G;UAKMhC,EAHZ,CAAA6B,cAAA,aAAwC,aACT,UAClB,aACuB;UAAA7B,EAAA,CAAAoC,MAAA,iBAAS;UAAApC,EAAA,CAAAgC,YAAA,EAAM;UACvChC,EAAA,CAAA6B,cAAA,cAAwB;UAAA7B,EAAA,CAAAoC,MAAA,IAAsB;UAClDpC,EADkD,CAAAgC,YAAA,EAAM,EAClD;UAEFhC,EADJ,CAAA6B,cAAA,WAAK,cACuB;UAAA7B,EAAA,CAAAoC,MAAA,aAAK;UAAApC,EAAA,CAAAgC,YAAA,EAAM;UACnChC,EAAA,CAAA6B,cAAA,cAAwB;UAAA7B,EAAA,CAAAoC,MAAA,IAAmB;UAC/CpC,EAD+C,CAAAgC,YAAA,EAAM,EAC/C;UAEFhC,EADJ,CAAA6B,cAAA,WAAK,cACuB;UAAA7B,EAAA,CAAAoC,MAAA,YAAI;UAAApC,EAAA,CAAAgC,YAAA,EAAM;UAClChC,EAAA,CAAA6B,cAAA,cAAwB;UAAA7B,EAAA,CAAAoC,MAAA,IAAmB;UAC/CpC,EAD+C,CAAAgC,YAAA,EAAM,EAC/C;UAEFhC,EADJ,CAAA6B,cAAA,WAAK,cACuB;UAAA7B,EAAA,CAAAoC,MAAA,qBAAa;UAAApC,EAAA,CAAAgC,YAAA,EAAM;UAC3ChC,EAAA,CAAA6B,cAAA,cAAwB;UAAA7B,EAAA,CAAAoC,MAAA,IAA0B;UACtDpC,EADsD,CAAAgC,YAAA,EAAM,EACtD;UAEFhC,EADJ,CAAA6B,cAAA,WAAK,cACuB;UAAA7B,EAAA,CAAAoC,MAAA,kBAAU;UAAApC,EAAA,CAAAgC,YAAA,EAAM;UACxChC,EAAA,CAAA6B,cAAA,cAAwB;UAAA7B,EAAA,CAAAoC,MAAA,IAAuB;UAEvDpC,EAFuD,CAAAgC,YAAA,EAAM,EACnD,EACJ;UAEFhC,EADJ,CAAA6B,cAAA,cAA6B,eACA;UAAA7B,EAAA,CAAAoC,MAAA,uBAAe;UAAApC,EAAA,CAAAgC,YAAA,EAAM;UAC9ChC,EAAA,CAAA6B,cAAA,eAAuB;UACnB7B,EAAA,CAAAsC,gBAAA,KAAAC,mDAAA,uBAAAvC,EAAA,CAAAwC,yBAAA,CAEC;UAGbxC,EAFQ,CAAAgC,YAAA,EAAM,EACJ,EACJ;UAIEhC,EAFR,CAAA6B,cAAA,eAAmB,eACwB,sBAYnC;UAHG7B,EAAA,CAAA8B,UAAA,uBAAAW,8EAAA;YAAA,OAAab,GAAA,CAAAT,YAAA,EAAc;UAAA,EAAC;UAIhCnB,EAAA,CAAAgC,YAAA,EAAa;UACbhC,EAAA,CAAA6B,cAAA,sBAcC;UAHG7B,EAAA,CAAA8B,UAAA,uBAAAY,8EAAA;YAAA,OAAad,GAAA,CAAAf,YAAA,EAAc;UAAA,EAAC;UAO3Cb,EAHW,CAAAgC,YAAA,EAAa,EACV,EACJ,EACU;;;UAxE0DhC,EAAA,CAAA2C,SAAA,EAAiB;UAAjB3C,EAAA,CAAAE,UAAA,kBAAiB;UAGhEF,EAAA,CAAA2C,SAAA,GAAe;UAAf3C,EAAA,CAAAE,UAAA,gBAAe;UAONF,EAAA,CAAA2C,SAAA,GAAsB;UAAtB3C,EAAA,CAAA4C,iBAAA,CAAAhB,GAAA,CAAArB,QAAA,kBAAAqB,GAAA,CAAArB,QAAA,CAAAsC,QAAA,CAAsB;UAItB7C,EAAA,CAAA2C,SAAA,GAAmB;UAAnB3C,EAAA,CAAA4C,iBAAA,CAAAhB,GAAA,CAAArB,QAAA,kBAAAqB,GAAA,CAAArB,QAAA,CAAAuC,KAAA,CAAmB;UAInB9C,EAAA,CAAA2C,SAAA,GAAmB;UAAnB3C,EAAA,CAAA4C,iBAAA,CAAAhB,GAAA,CAAArB,QAAA,kBAAAqB,GAAA,CAAArB,QAAA,CAAAwC,KAAA,CAAmB;UAInB/C,EAAA,CAAA2C,SAAA,GAA0B;UAA1B3C,EAAA,CAAA4C,iBAAA,CAAAhB,GAAA,CAAArB,QAAA,kBAAAqB,GAAA,CAAArB,QAAA,CAAAyC,YAAA,CAA0B;UAI1BhD,EAAA,CAAA2C,SAAA,GAAuB;UAAvB3C,EAAA,CAAA4C,iBAAA,CAAAhB,GAAA,CAAArB,QAAA,kBAAAqB,GAAA,CAAArB,QAAA,CAAA0C,SAAA,CAAuB;UAM/CjD,EAAA,CAAA2C,SAAA,GAEC;UAFD3C,EAAA,CAAAkD,UAAA,CAAAtB,GAAA,CAAArB,QAAA,kBAAAqB,GAAA,CAAArB,QAAA,CAAA4C,MAAA,CAEC;UAUFnD,EAAA,CAAA2C,SAAA,GAAwF;UAMxF3C,EANA,CAAAE,UAAA,iBAAAF,EAAA,CAAAI,eAAA,KAAAgD,GAAA,EAAwF,iBAMxE;UAOhBpD,EAAA,CAAA2C,SAAA,EAGE;UAMF3C,EATA,CAAAE,UAAA,iBAAAF,EAAA,CAAAI,eAAA,KAAAiD,GAAA,EAGE,iBAMc;;;qBA1E/BzD,eAAe,EACfC,aAAa,EACbJ,YAAY,EACZK,qBAAqB,EACrBH,eAAe;MAAA2D,MAAA;IAAA;;SAyJNhD,mCAAmC;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}