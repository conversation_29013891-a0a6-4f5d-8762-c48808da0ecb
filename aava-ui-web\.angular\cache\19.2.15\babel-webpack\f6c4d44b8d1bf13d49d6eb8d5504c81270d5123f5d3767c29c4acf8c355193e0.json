{"ast": null, "code": "export default function (polygon) {\n  var i = -1,\n    n = polygon.length,\n    x = 0,\n    y = 0,\n    a,\n    b = polygon[n - 1],\n    c,\n    k = 0;\n  while (++i < n) {\n    a = b;\n    b = polygon[i];\n    k += c = a[0] * b[1] - b[0] * a[1];\n    x += (a[0] + b[0]) * c;\n    y += (a[1] + b[1]) * c;\n  }\n  return k *= 3, [x / k, y / k];\n}", "map": {"version": 3, "names": ["polygon", "i", "n", "length", "x", "y", "a", "b", "c", "k"], "sources": ["C:/console/aava-ui-web/node_modules/d3-polygon/src/centroid.js"], "sourcesContent": ["export default function(polygon) {\n  var i = -1,\n      n = polygon.length,\n      x = 0,\n      y = 0,\n      a,\n      b = polygon[n - 1],\n      c,\n      k = 0;\n\n  while (++i < n) {\n    a = b;\n    b = polygon[i];\n    k += c = a[0] * b[1] - b[0] * a[1];\n    x += (a[0] + b[0]) * c;\n    y += (a[1] + b[1]) * c;\n  }\n\n  return k *= 3, [x / k, y / k];\n}\n"], "mappings": "AAAA,eAAe,UAASA,OAAO,EAAE;EAC/B,IAAIC,CAAC,GAAG,CAAC,CAAC;IACNC,CAAC,GAAGF,OAAO,CAACG,MAAM;IAClBC,CAAC,GAAG,CAAC;IACLC,CAAC,GAAG,CAAC;IACLC,CAAC;IACDC,CAAC,GAAGP,OAAO,CAACE,CAAC,GAAG,CAAC,CAAC;IAClBM,CAAC;IACDC,CAAC,GAAG,CAAC;EAET,OAAO,EAAER,CAAC,GAAGC,CAAC,EAAE;IACdI,CAAC,GAAGC,CAAC;IACLA,CAAC,GAAGP,OAAO,CAACC,CAAC,CAAC;IACdQ,CAAC,IAAID,CAAC,GAAGF,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC;IAClCF,CAAC,IAAI,CAACE,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,IAAIC,CAAC;IACtBH,CAAC,IAAI,CAACC,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,IAAIC,CAAC;EACxB;EAEA,OAAOC,CAAC,IAAI,CAAC,EAAE,CAACL,CAAC,GAAGK,CAAC,EAAEJ,CAAC,GAAGI,CAAC,CAAC;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}