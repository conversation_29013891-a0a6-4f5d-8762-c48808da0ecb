{"ast": null, "code": "import namespace from \"../namespace.js\";\nfunction attrRemove(name) {\n  return function () {\n    this.removeAttribute(name);\n  };\n}\nfunction attrRemoveNS(fullname) {\n  return function () {\n    this.removeAttributeNS(fullname.space, fullname.local);\n  };\n}\nfunction attrConstant(name, value) {\n  return function () {\n    this.setAttribute(name, value);\n  };\n}\nfunction attrConstantNS(fullname, value) {\n  return function () {\n    this.setAttributeNS(fullname.space, fullname.local, value);\n  };\n}\nfunction attrFunction(name, value) {\n  return function () {\n    var v = value.apply(this, arguments);\n    if (v == null) this.removeAttribute(name);else this.setAttribute(name, v);\n  };\n}\nfunction attrFunctionNS(fullname, value) {\n  return function () {\n    var v = value.apply(this, arguments);\n    if (v == null) this.removeAttributeNS(fullname.space, fullname.local);else this.setAttributeNS(fullname.space, fullname.local, v);\n  };\n}\nexport default function (name, value) {\n  var fullname = namespace(name);\n  if (arguments.length < 2) {\n    var node = this.node();\n    return fullname.local ? node.getAttributeNS(fullname.space, fullname.local) : node.getAttribute(fullname);\n  }\n  return this.each((value == null ? fullname.local ? attrRemoveNS : attrRemove : typeof value === \"function\" ? fullname.local ? attrFunctionNS : attrFunction : fullname.local ? attrConstantNS : attrConstant)(fullname, value));\n}", "map": {"version": 3, "names": ["namespace", "attrRemove", "name", "removeAttribute", "attrRemoveNS", "fullname", "removeAttributeNS", "space", "local", "attrConstant", "value", "setAttribute", "attrConstantNS", "setAttributeNS", "attrFunction", "v", "apply", "arguments", "attrFunctionNS", "length", "node", "getAttributeNS", "getAttribute", "each"], "sources": ["C:/console/aava-ui-web/node_modules/d3-selection/src/selection/attr.js"], "sourcesContent": ["import namespace from \"../namespace.js\";\n\nfunction attrRemove(name) {\n  return function() {\n    this.removeAttribute(name);\n  };\n}\n\nfunction attrRemoveNS(fullname) {\n  return function() {\n    this.removeAttributeNS(fullname.space, fullname.local);\n  };\n}\n\nfunction attrConstant(name, value) {\n  return function() {\n    this.setAttribute(name, value);\n  };\n}\n\nfunction attrConstantNS(fullname, value) {\n  return function() {\n    this.setAttributeNS(fullname.space, fullname.local, value);\n  };\n}\n\nfunction attrFunction(name, value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null) this.removeAttribute(name);\n    else this.setAttribute(name, v);\n  };\n}\n\nfunction attrFunctionNS(fullname, value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null) this.removeAttributeNS(fullname.space, fullname.local);\n    else this.setAttributeNS(fullname.space, fullname.local, v);\n  };\n}\n\nexport default function(name, value) {\n  var fullname = namespace(name);\n\n  if (arguments.length < 2) {\n    var node = this.node();\n    return fullname.local\n        ? node.getAttributeNS(fullname.space, fullname.local)\n        : node.getAttribute(fullname);\n  }\n\n  return this.each((value == null\n      ? (fullname.local ? attrRemoveNS : attrRemove) : (typeof value === \"function\"\n      ? (fullname.local ? attrFunctionNS : attrFunction)\n      : (fullname.local ? attrConstantNS : attrConstant)))(fullname, value));\n}\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AAEvC,SAASC,UAAUA,CAACC,IAAI,EAAE;EACxB,OAAO,YAAW;IAChB,IAAI,CAACC,eAAe,CAACD,IAAI,CAAC;EAC5B,CAAC;AACH;AAEA,SAASE,YAAYA,CAACC,QAAQ,EAAE;EAC9B,OAAO,YAAW;IAChB,IAAI,CAACC,iBAAiB,CAACD,QAAQ,CAACE,KAAK,EAAEF,QAAQ,CAACG,KAAK,CAAC;EACxD,CAAC;AACH;AAEA,SAASC,YAAYA,CAACP,IAAI,EAAEQ,KAAK,EAAE;EACjC,OAAO,YAAW;IAChB,IAAI,CAACC,YAAY,CAACT,IAAI,EAAEQ,KAAK,CAAC;EAChC,CAAC;AACH;AAEA,SAASE,cAAcA,CAACP,QAAQ,EAAEK,KAAK,EAAE;EACvC,OAAO,YAAW;IAChB,IAAI,CAACG,cAAc,CAACR,QAAQ,CAACE,KAAK,EAAEF,QAAQ,CAACG,KAAK,EAAEE,KAAK,CAAC;EAC5D,CAAC;AACH;AAEA,SAASI,YAAYA,CAACZ,IAAI,EAAEQ,KAAK,EAAE;EACjC,OAAO,YAAW;IAChB,IAAIK,CAAC,GAAGL,KAAK,CAACM,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IACpC,IAAIF,CAAC,IAAI,IAAI,EAAE,IAAI,CAACZ,eAAe,CAACD,IAAI,CAAC,CAAC,KACrC,IAAI,CAACS,YAAY,CAACT,IAAI,EAAEa,CAAC,CAAC;EACjC,CAAC;AACH;AAEA,SAASG,cAAcA,CAACb,QAAQ,EAAEK,KAAK,EAAE;EACvC,OAAO,YAAW;IAChB,IAAIK,CAAC,GAAGL,KAAK,CAACM,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IACpC,IAAIF,CAAC,IAAI,IAAI,EAAE,IAAI,CAACT,iBAAiB,CAACD,QAAQ,CAACE,KAAK,EAAEF,QAAQ,CAACG,KAAK,CAAC,CAAC,KACjE,IAAI,CAACK,cAAc,CAACR,QAAQ,CAACE,KAAK,EAAEF,QAAQ,CAACG,KAAK,EAAEO,CAAC,CAAC;EAC7D,CAAC;AACH;AAEA,eAAe,UAASb,IAAI,EAAEQ,KAAK,EAAE;EACnC,IAAIL,QAAQ,GAAGL,SAAS,CAACE,IAAI,CAAC;EAE9B,IAAIe,SAAS,CAACE,MAAM,GAAG,CAAC,EAAE;IACxB,IAAIC,IAAI,GAAG,IAAI,CAACA,IAAI,CAAC,CAAC;IACtB,OAAOf,QAAQ,CAACG,KAAK,GACfY,IAAI,CAACC,cAAc,CAAChB,QAAQ,CAACE,KAAK,EAAEF,QAAQ,CAACG,KAAK,CAAC,GACnDY,IAAI,CAACE,YAAY,CAACjB,QAAQ,CAAC;EACnC;EAEA,OAAO,IAAI,CAACkB,IAAI,CAAC,CAACb,KAAK,IAAI,IAAI,GACxBL,QAAQ,CAACG,KAAK,GAAGJ,YAAY,GAAGH,UAAU,GAAK,OAAOS,KAAK,KAAK,UAAU,GAC1EL,QAAQ,CAACG,KAAK,GAAGU,cAAc,GAAGJ,YAAY,GAC9CT,QAAQ,CAACG,KAAK,GAAGI,cAAc,GAAGH,YAAc,EAAEJ,QAAQ,EAAEK,KAAK,CAAC,CAAC;AAC5E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}