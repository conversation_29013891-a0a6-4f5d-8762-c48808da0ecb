{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { EventEmitter } from '@angular/core';\nimport { FormsModule, FormControl, ReactiveFormsModule } from '@angular/forms';\nimport { IconComponent, ToggleComponent } from '@ava/play-comp-library';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nconst _c0 = [\"messagesContainer\"];\nconst _c1 = [\"fileInput\"];\nconst _c2 = (a0, a1) => ({\n  \"bot-response\": a0,\n  \"user-message\": a1\n});\nfunction AgentExecutionPlaygroundComponent_div_3_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function AgentExecutionPlaygroundComponent_div_3_button_3_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const msg_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.copyToClipboard(msg_r3.text));\n    });\n    i0.ɵɵelement(1, \"ava-icon\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"iconSize\", 16);\n  }\n}\nfunction AgentExecutionPlaygroundComponent_div_3_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtext(1, \"Copied!\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionPlaygroundComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 16);\n    i0.ɵɵtext(2);\n    i0.ɵɵtemplate(3, AgentExecutionPlaygroundComponent_div_3_button_3_Template, 2, 1, \"button\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, AgentExecutionPlaygroundComponent_div_3_div_4_Template, 2, 0, \"div\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const msg_r3 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", msg_r3.from);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(5, _c2, msg_r3.from === \"ai\", msg_r3.from === \"user\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", msg_r3.text, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", msg_r3.from === \"ai\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.showCopiedToast);\n  }\n}\nfunction AgentExecutionPlaygroundComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23)(2, \"div\", 24);\n    i0.ɵɵelement(3, \"span\", 25)(4, \"span\", 25)(5, \"span\", 25);\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AgentExecutionPlaygroundComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 26);\n    i0.ɵɵtext(2, \"...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AgentExecutionPlaygroundComponent_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function AgentExecutionPlaygroundComponent_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      i0.ɵɵnextContext();\n      const fileInput_r6 = i0.ɵɵreference(10);\n      return i0.ɵɵresetView(fileInput_r6.click());\n    });\n    i0.ɵɵelement(1, \"ava-icon\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"iconSize\", 16);\n  }\n}\nfunction AgentExecutionPlaygroundComponent_div_11_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"span\", 32);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function AgentExecutionPlaygroundComponent_div_11_div_1_Template_button_click_3_listener() {\n      const i_r8 = i0.ɵɵrestoreView(_r7).index;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.removeFile(i_r8));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r9 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(file_r9.documentName);\n  }\n}\nfunction AgentExecutionPlaygroundComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtemplate(1, AgentExecutionPlaygroundComponent_div_11_div_1_Template, 4, 1, \"div\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.filesUploadedData);\n  }\n}\nfunction AgentExecutionPlaygroundComponent_div_15_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"ava-toggle\", 37);\n    i0.ɵɵlistener(\"checkedChange\", function AgentExecutionPlaygroundComponent_div_15_div_1_Template_ava_toggle_checkedChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onConversationalToggle($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"checked\", ctx_r3.isConvChecked)(\"title\", \"Conversational\")(\"position\", \"left\");\n  }\n}\nfunction AgentExecutionPlaygroundComponent_div_15_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"ava-toggle\", 37);\n    i0.ɵɵlistener(\"checkedChange\", function AgentExecutionPlaygroundComponent_div_15_div_2_Template_ava_toggle_checkedChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onTemplateToggle($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"checked\", ctx_r3.isUseTemplate)(\"title\", \"Use Template\")(\"position\", \"left\");\n  }\n}\nfunction AgentExecutionPlaygroundComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵtemplate(1, AgentExecutionPlaygroundComponent_div_15_div_1_Template, 2, 3, \"div\", 35)(2, AgentExecutionPlaygroundComponent_div_15_div_2_Template, 2, 3, \"div\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.showChatInteractionToggles);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.showChatInteractionToggles);\n  }\n}\nexport let AgentExecutionPlaygroundComponent = /*#__PURE__*/(() => {\n  class AgentExecutionPlaygroundComponent {\n    isMenuOpen = false;\n    isToolMenuOpen = false;\n    promptChange = new EventEmitter();\n    promptOptions = [];\n    selectedValue = 'default'; // Input for pre-selected value\n    agentType = 'individual'; // Input for agent type ('individual' or 'collaborative')\n    showChatInteractionToggles = false; // Input to show conversational and template toggles\n    showAiPrincipleToggle = false; // Input to show AI principle toggle\n    showDropdown = true; // Input to control dropdown visibility\n    showAgentNameInput = false; // Input to show disabled agent name input field\n    agentNamePlaceholder = 'Agent Name'; // Placeholder for agent name input\n    displayedAgentName = ''; // Agent name to display in disabled input\n    showFileUploadButton = false; // Controls visibility of attach file button\n    selectedPrompt = 'default';\n    // Form control for agent name display\n    agentNameDisplayControl = new FormControl({\n      value: '',\n      disabled: true\n    });\n    // Chat data\n    showCopiedToast = false;\n    inputText = '';\n    previousMessagesLength = 0;\n    shouldScrollToBottom = false;\n    messages = [];\n    isLoading = false;\n    isDisabled = false;\n    showLoader = true;\n    messageSent = new EventEmitter();\n    conversationalToggle = new EventEmitter();\n    templateToggle = new EventEmitter();\n    filesSelected = new EventEmitter();\n    messagesContainer;\n    fileInput;\n    showApprovalButton = true;\n    approvalRequested = new EventEmitter();\n    isMinimalView = false;\n    // Simple toggle properties for display only\n    isConvChecked = true;\n    isUseTemplate = false;\n    // File upload properties\n    filesUploadedData = [];\n    acceptedFileType = '.pdf,.doc,.docx,.txt,.csv,.xlsx,.xls,.ppt,.pptx,.png,.jpg,.jpeg,.gif,.bmp,.svg';\n    ngOnInit() {\n      this.messages = [{\n        from: 'ai',\n        text: 'Hi there, how can I help you today?'\n      }];\n      this.shouldScrollToBottom = true;\n      // Set selected prompt from input\n      if (this.selectedValue) {\n        this.selectedPrompt = this.selectedValue;\n        // Update displayed agent name if showing agent name input\n        if (this.showAgentNameInput && !this.displayedAgentName) {\n          this.displayedAgentName = this.selectedValue;\n          this.agentNameDisplayControl.setValue(this.selectedValue);\n        }\n      }\n      // Initialize agent name display control\n      if (this.displayedAgentName) {\n        this.agentNameDisplayControl.setValue(this.displayedAgentName);\n      }\n    }\n    ngOnChanges(changes) {\n      // Update selectedPrompt when selectedValue input changes\n      if (changes['selectedValue'] && changes['selectedValue'].currentValue) {\n        // The selectedValue from parent should be the name (for dropdown display)\n        this.selectedPrompt = changes['selectedValue'].currentValue;\n      }\n      // Update agent name display control when displayedAgentName input changes\n      if (changes['displayedAgentName'] && changes['displayedAgentName'].currentValue !== undefined) {\n        this.agentNameDisplayControl.setValue(changes['displayedAgentName'].currentValue);\n      }\n    }\n    ngAfterViewChecked() {\n      if (this.shouldScrollToBottom) {\n        this.scrollToBottom();\n        this.shouldScrollToBottom = false;\n      }\n    }\n    scrollToBottom() {\n      try {\n        if (this.messagesContainer && this.messagesContainer.nativeElement) {\n          // Scroll to bottom to show latest messages\n          this.messagesContainer.nativeElement.scrollTop = this.messagesContainer.nativeElement.scrollHeight;\n        }\n      } catch (err) {\n        console.error('Error scrolling to bottom:', err);\n      }\n    }\n    handleSendMessage() {\n      if (!this.inputText.trim() || this.isDisabled) {\n        return;\n      }\n      // Add user message to the chat\n      this.messages = [...this.messages, {\n        from: 'user',\n        text: this.inputText\n      }];\n      this.shouldScrollToBottom = true;\n      // Emit the message to parent component\n      const messageText = this.inputText;\n      this.inputText = '';\n      this.messageSent.emit(messageText);\n      // Clear uploaded files after sending message\n      this.clearFiles();\n    }\n    clearFiles() {\n      this.filesUploadedData = [];\n      this.filesSelected.emit(this.filesUploadedData);\n    }\n    toggleMenu() {\n      this.isMenuOpen = !this.isMenuOpen;\n    }\n    onAiPrincipleToggle(event) {\n      console.log('AI Principles toggle:', event);\n    }\n    onConversationalToggle(event) {\n      this.isConvChecked = event;\n      // If conversational is enabled, disable template\n      if (event && this.isUseTemplate) {\n        this.isUseTemplate = false;\n        this.templateToggle.emit(false);\n      }\n      console.log('Conversational mode:', event);\n      this.conversationalToggle.emit(event);\n    }\n    onTemplateToggle(event) {\n      this.isUseTemplate = event;\n      // If template is enabled, disable conversational\n      if (event && this.isConvChecked) {\n        this.isConvChecked = false;\n        this.conversationalToggle.emit(false);\n      }\n      console.log('Use template:', event);\n      this.templateToggle.emit(event);\n    }\n    onPromptChange(selectionData) {\n      // The dropdown component emits an object with selectedOptions and selectedValue\n      // selectedValue contains the name of the selected option\n      let selectedName;\n      if (typeof selectionData === 'string') {\n        selectedName = selectionData;\n      } else if (selectionData && selectionData.selectedValue) {\n        selectedName = selectionData.selectedValue;\n      } else if (selectionData && selectionData.selectedOptions && selectionData.selectedOptions.length > 0) {\n        selectedName = selectionData.selectedOptions[0].name;\n      } else {\n        return;\n      }\n      this.selectedPrompt = selectedName;\n      // Update displayed agent name if showing agent name input\n      if (this.showAgentNameInput) {\n        this.displayedAgentName = selectedName;\n        this.agentNameDisplayControl.setValue(selectedName);\n      }\n      // Find the option by name\n      const selectedOption = this.promptOptions.find(opt => opt.name === selectedName);\n      if (selectedOption) {\n        this.promptChange.emit(selectedOption);\n      }\n    }\n    copyToClipboard(text) {\n      navigator.clipboard.writeText(text).then(() => {\n        this.showCopiedToast = true;\n        setTimeout(() => {\n          this.showCopiedToast = false;\n        }, 2000);\n      });\n    }\n    save() {\n      this.isMenuOpen = false;\n      console.log('Save clicked');\n      // your save logic here\n    }\n    export() {\n      this.isMenuOpen = false;\n      console.log('Export clicked');\n      // your export logic here\n    }\n    // Hide menu when clicking outside\n    onClickOutside(event) {\n      const target = event.target;\n      if (!target.closest('.btn-menu')) {\n        this.isMenuOpen = false;\n      }\n    }\n    onEnterKeydown(event) {\n      // Only prevent default and send if Shift key is not pressed\n      if (!event.shiftKey) {\n        event.preventDefault();\n        this.handleSendMessage();\n      }\n    }\n    // File upload methods\n    onFileSelected(event) {\n      const files = event.target.files;\n      if (files && files.length > 0) {\n        this.filesUploadedData = [];\n        for (let i = 0; i < files.length; i++) {\n          const file = files[i];\n          this.filesUploadedData.push({\n            id: `file_${Date.now()}_${i}`,\n            documentName: file.name,\n            isImage: file.type.startsWith('image/'),\n            file: file\n          });\n        }\n        console.log('Files selected:', this.filesUploadedData);\n        this.filesSelected.emit(this.filesUploadedData);\n      }\n    }\n    removeFile(index) {\n      this.filesUploadedData.splice(index, 1);\n      this.filesSelected.emit(this.filesUploadedData);\n    }\n    // Track by function for ngFor performance\n    trackByIndex(index, _item) {\n      return index;\n    }\n    onApprovalClick() {\n      this.approvalRequested.emit();\n    }\n    static ɵfac = function AgentExecutionPlaygroundComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AgentExecutionPlaygroundComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AgentExecutionPlaygroundComponent,\n      selectors: [[\"app-agent-execution-playground\"]],\n      viewQuery: function AgentExecutionPlaygroundComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messagesContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      hostBindings: function AgentExecutionPlaygroundComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function AgentExecutionPlaygroundComponent_click_HostBindingHandler($event) {\n            return ctx.onClickOutside($event);\n          }, false, i0.ɵɵresolveDocument)(\"keydown.enter\", function AgentExecutionPlaygroundComponent_keydown_enter_HostBindingHandler($event) {\n            return ctx.onEnterKeydown($event);\n          });\n        }\n      },\n      inputs: {\n        promptOptions: \"promptOptions\",\n        selectedValue: \"selectedValue\",\n        agentType: \"agentType\",\n        showChatInteractionToggles: \"showChatInteractionToggles\",\n        showAiPrincipleToggle: \"showAiPrincipleToggle\",\n        showDropdown: \"showDropdown\",\n        showAgentNameInput: \"showAgentNameInput\",\n        agentNamePlaceholder: \"agentNamePlaceholder\",\n        displayedAgentName: \"displayedAgentName\",\n        showFileUploadButton: \"showFileUploadButton\",\n        messages: \"messages\",\n        isLoading: \"isLoading\",\n        isDisabled: \"isDisabled\",\n        showLoader: \"showLoader\",\n        showApprovalButton: \"showApprovalButton\",\n        isMinimalView: \"isMinimalView\",\n        acceptedFileType: \"acceptedFileType\"\n      },\n      outputs: {\n        promptChange: \"promptChange\",\n        messageSent: \"messageSent\",\n        conversationalToggle: \"conversationalToggle\",\n        templateToggle: \"templateToggle\",\n        filesSelected: \"filesSelected\",\n        approvalRequested: \"approvalRequested\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 16,\n      vars: 12,\n      consts: [[\"messagesContainer\", \"\"], [\"fileInput\", \"\"], [1, \"playground-container\"], [1, \"layout\"], [\"class\", \"message-row\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"message-row ai\", 4, \"ngIf\"], [1, \"input-container\"], [\"placeholder\", \"Enter something to test\", 3, \"ngModelChange\", \"keydown.enter\", \"ngModel\", \"disabled\"], [\"class\", \"attach-btn\", \"title\", \"Attach File\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"file\", \"multiple\", \"\", 2, \"display\", \"none\", 3, \"change\", \"accept\"], [\"class\", \"uploaded-files\", 4, \"ngIf\"], [1, \"right-icons\"], [\"title\", \"Send\", 1, \"send-btn\", 3, \"click\", \"disabled\"], [\"slot\", \"icon-start\", \"iconName\", \"send-horizontal\", \"iconColor\", \"var(--color-brand-primary)\", 3, \"iconSize\"], [\"class\", \"toggle-container\", 4, \"ngIf\"], [1, \"message-row\", 3, \"ngClass\"], [3, \"ngClass\"], [\"class\", \"copy-btn\", \"title\", \"Copy\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"copied-toast\", 4, \"ngIf\"], [\"title\", \"Copy\", 1, \"copy-btn\", 3, \"click\"], [\"slot\", \"icon-start\", \"iconName\", \"copy\", \"iconColor\", \"var(--color-brand-primary)\", 3, \"iconSize\"], [1, \"copied-toast\"], [1, \"message-row\", \"ai\"], [1, \"bot-response\", \"loading-message\"], [1, \"typing-indicator\"], [1, \"dot\"], [1, \"bot-response\"], [\"title\", \"Attach File\", 1, \"attach-btn\", 3, \"click\"], [\"slot\", \"icon-start\", \"iconName\", \"paperclip\", \"iconColor\", \"var(--color-brand-primary)\", 3, \"iconSize\"], [1, \"uploaded-files\"], [\"class\", \"file-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"file-item\"], [1, \"file-name\"], [\"title\", \"Remove file\", 1, \"remove-file\", 3, \"click\"], [1, \"toggle-container\"], [\"class\", \"toggle-row\", 4, \"ngIf\"], [1, \"toggle-row\"], [\"size\", \"small\", 3, \"checkedChange\", \"checked\", \"title\", \"position\"]],\n      template: function AgentExecutionPlaygroundComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3, 0);\n          i0.ɵɵtemplate(3, AgentExecutionPlaygroundComponent_div_3_Template, 5, 8, \"div\", 4)(4, AgentExecutionPlaygroundComponent_div_4_Template, 6, 0, \"div\", 5)(5, AgentExecutionPlaygroundComponent_div_5_Template, 3, 0, \"div\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"textarea\", 7);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function AgentExecutionPlaygroundComponent_Template_textarea_ngModelChange_7_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.inputText, $event) || (ctx.inputText = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"keydown.enter\", function AgentExecutionPlaygroundComponent_Template_textarea_keydown_enter_7_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            ctx.handleSendMessage();\n            return i0.ɵɵresetView($event.preventDefault());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, AgentExecutionPlaygroundComponent_button_8_Template, 2, 1, \"button\", 8);\n          i0.ɵɵelementStart(9, \"input\", 9, 1);\n          i0.ɵɵlistener(\"change\", function AgentExecutionPlaygroundComponent_Template_input_change_9_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onFileSelected($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(11, AgentExecutionPlaygroundComponent_div_11_Template, 2, 1, \"div\", 10);\n          i0.ɵɵelementStart(12, \"div\", 11)(13, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function AgentExecutionPlaygroundComponent_Template_button_click_13_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.handleSendMessage());\n          });\n          i0.ɵɵelement(14, \"ava-icon\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(15, AgentExecutionPlaygroundComponent_div_15_Template, 3, 2, \"div\", 14);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.messages)(\"ngForTrackBy\", ctx.trackByIndex);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading && ctx.showLoader);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading && !ctx.showLoader);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.inputText);\n          i0.ɵɵproperty(\"disabled\", ctx.isDisabled || ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showFileUploadButton);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"accept\", ctx.acceptedFileType);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.filesUploadedData.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading || ctx.isDisabled);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"iconSize\", 16);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showChatInteractionToggles || ctx.showAiPrincipleToggle);\n        }\n      },\n      dependencies: [CommonModule, i1.NgClass, i1.NgForOf, i1.NgIf, FormsModule, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgModel, ReactiveFormsModule, ToggleComponent, IconComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.playground-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  font-family: \\\"Inter\\\", sans-serif;\\n  width: 100%;\\n  height: 78vh; \\n\\n  max-height: 100%; \\n\\n  overflow: hidden; \\n\\n}\\n@media (max-width: 1400px) {\\n  .playground-container[_ngcontent-%COMP%] {\\n    width: 60%;\\n  }\\n}\\n@media (max-width: 1200px) {\\n  .playground-container[_ngcontent-%COMP%] {\\n    width: 60%;\\n  }\\n}\\n\\n\\n\\n.button-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 8px 12px;\\n  height: 48px;\\n  flex-shrink: 0;\\n  border-bottom: 1px solid #eee;\\n  background-color: #ffffff;\\n}\\n\\n\\n\\n.dropdown-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 8px 12px;\\n  border: 1px solid #ccc;\\n  border-radius: 6px;\\n  background: white;\\n  color: #333;\\n  font-size: 14px;\\n  cursor: pointer;\\n  min-width: 100px;\\n}\\n\\n.dropdown-btn[_ngcontent-%COMP%]   .arrow-down[_ngcontent-%COMP%] {\\n  width: 10px;\\n  height: 6px;\\n  margin-left: 8px;\\n}\\n\\n\\n\\n.btn-menu[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n\\n\\n.menu-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  gap: 3px;\\n  cursor: pointer;\\n}\\n\\n.menu-icon[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  display: block;\\n  width: 3px;\\n  height: 3px;\\n  background: black;\\n  border-radius: 50%;\\n}\\n\\n\\n\\n.layout[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n  flex: 1; \\n\\n  padding: 16px;\\n  overflow-y: auto; \\n\\n  overflow-x: hidden; \\n\\n  background: #fff;\\n  align-items: stretch;\\n  justify-content: flex-start;\\n  min-height: 300px; \\n\\n  max-height: none; \\n\\n}\\n\\n\\n\\n.message[_ngcontent-%COMP%] {\\n  max-width: 60%;\\n  font-size: 14px;\\n  border-radius: 8px;\\n  padding: 16px 24px;\\n}\\n\\n.message-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  width: 100%;\\n}\\n\\n.message-row.ai[_ngcontent-%COMP%] {\\n  justify-content: flex-start;\\n}\\n\\n.message-row.user[_ngcontent-%COMP%] {\\n  justify-content: flex-end;\\n}\\n\\n\\n\\n.user-message[_ngcontent-%COMP%] {\\n  display: inline-flex; \\n\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: flex-start; \\n\\n  align-self: flex-end; \\n\\n  background: #c2c4cd;\\n  color: #333;\\n  border-radius: 8px;\\n  padding: 16px 24px;\\n  max-width: 60%;\\n  word-wrap: break-word;\\n  text-align: left;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.user-message[_ngcontent-%COMP%]:empty {\\n  background: none;\\n  padding: 0;\\n}\\n\\n\\n\\n.bot-response[_ngcontent-%COMP%] {\\n  align-self: flex-start;\\n  background: #f1f1f1; \\n\\n  color: #333;\\n  display: flex;\\n  padding: 16px 24px;\\n  border-radius: 8px;\\n  flex-direction: column;\\n  justify-content: center;\\n  gap: 10px;\\n  margin-bottom: 0.5rem;\\n  position: relative;\\n  padding-right: 40px;\\n}\\n\\n.copy-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 16px;\\n  right: 16px;\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 4px;\\n  display: flex;\\n  align-items: center;\\n}\\n.copy-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  fill: var(--color-brand-primary, #144692);\\n  transition: fill 0.2s ease;\\n}\\n.copy-btn[_ngcontent-%COMP%]:hover   svg[_ngcontent-%COMP%] {\\n  fill: #1d4ed8;\\n}\\n\\n\\n\\n.loading-message[_ngcontent-%COMP%] {\\n  min-height: 20px;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n\\n\\n.typing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 50%;\\n  background-color: #666;\\n  animation: _ngcontent-%COMP%_typing 1.4s infinite ease-in-out;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%]:nth-child(1) {\\n  animation-delay: 0s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%]:nth-child(2) {\\n  animation-delay: 0.2s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%]:nth-child(3) {\\n  animation-delay: 0.4s;\\n}\\n\\n@keyframes _ngcontent-%COMP%_typing {\\n  0%, 60%, 100% {\\n    transform: scale(1);\\n    opacity: 0.5;\\n  }\\n  30% {\\n    transform: scale(1.2);\\n    opacity: 1;\\n  }\\n}\\n\\n\\n.result-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  width: 186px;\\n  padding: 8px 12px;\\n  justify-content: center;\\n  align-items: center;\\n  border-radius: 8px;\\n  background: #f1f1f1;\\n  font-size: 14px;\\n  color: #333;\\n  margin-bottom: 0.5rem;\\n}\\n\\n\\n\\n\\n\\n.toggle-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 14px;\\n  padding: 8px 24px;\\n  font-family: \\\"Inter\\\", sans-serif;\\n  flex-shrink: 0; \\n\\n  height: 40px; \\n\\n  min-height: 40px; \\n\\n}\\n\\n\\n\\n.toggle-switch[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: inline-block;\\n  width: 40px;\\n  height: 20px;\\n}\\n\\n.toggle-switch[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  opacity: 0;\\n  width: 0;\\n  height: 0;\\n}\\n\\n.slider[_ngcontent-%COMP%] {\\n  position: absolute;\\n  cursor: pointer;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background-color: #ccc;\\n  transition: 0.4s;\\n  border-radius: 20px;\\n}\\n\\n.slider[_ngcontent-%COMP%]:before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  height: 14px;\\n  width: 14px;\\n  left: 3px;\\n  bottom: 3px;\\n  background-color: white;\\n  transition: 0.4s;\\n  border-radius: 50%;\\n}\\n\\n.toggle-switch[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked    + .slider[_ngcontent-%COMP%] {\\n  background-color: #4caf50;\\n}\\n\\n.toggle-switch[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked    + .slider[_ngcontent-%COMP%]:before {\\n  transform: translateX(20px);\\n}\\n\\n.toggle-label[_ngcontent-%COMP%] {\\n  color: #333;\\n}\\n\\n.dot-dropdown-menu[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 12px;\\n  top: 120px;\\n  background: white;\\n  border: 1px solid #ccc;\\n  border-radius: 6px;\\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);\\n  z-index: 10;\\n  display: flex;\\n  flex-direction: column;\\n  min-width: 120px;\\n}\\n\\n.dot-dropdown-menu[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  background: #f0f0f0;\\n}\\n\\n.dot-dropdown-menu[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-start;\\n  gap: 8px;\\n  padding: 10px 16px;\\n  border: none;\\n  background: none;\\n  text-align: left;\\n  cursor: pointer;\\n  font-size: 14px;\\n  color: #333;\\n  width: 100%;\\n  line-height: 1.2;\\n}\\n\\n.dot-dropdown-menu[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  display: block;\\n  height: 16px;\\n  width: 16px;\\n  vertical-align: middle;\\n}\\n\\n.dropdown-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: inline-block;\\n  width: 30%;\\n}\\n\\n\\n\\n.agent-name-display[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: inline-block;\\n  width: 30%;\\n}\\n.agent-name-display[_ngcontent-%COMP%]   .disabled-agent-name-input[_ngcontent-%COMP%]     .ava-textbox input {\\n  background-color: #f8f9fa !important;\\n  color: #6c757d !important;\\n  cursor: not-allowed !important;\\n  border-color: #e9ecef !important;\\n}\\n\\n.tool-dropdown-menu[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 100%; \\n\\n  left: 0;\\n  display: flex;\\n  flex-direction: column;\\n  background: white;\\n  border: 1px solid #ccc;\\n  border-radius: 6px;\\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);\\n  z-index: 10;\\n  min-width: 140px;\\n}\\n\\n\\n\\n.toggle-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  gap: 16px;\\n  align-items: center;\\n  padding: 12px 16px;\\n  border-top: 1px solid #e9ecef;\\n  background: #f8f9fa;\\n  min-height: 56px;\\n  flex-shrink: 0; \\n\\n  border-bottom-left-radius: 8px;\\n  border-bottom-right-radius: 8px;\\n}\\n\\n.toggle-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-start;\\n  min-height: 32px;\\n  white-space: nowrap;\\n}\\n\\n\\n\\n.uploaded-files[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 100%;\\n  left: 0;\\n  right: 0;\\n  background: white;\\n  border: 1px solid #e0e0e0;\\n  border-bottom: none;\\n  border-radius: 8px 8px 0 0;\\n  padding: 12px;\\n  max-height: 120px;\\n  overflow-y: auto;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n.file-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background: #f8f9fa;\\n  border: 1px solid #e9ecef;\\n  border-radius: 8px;\\n  padding: 12px 16px;\\n  font-size: 14px;\\n  min-height: 48px;\\n  transition: all 0.2s ease;\\n  position: relative;\\n}\\n.file-item[_ngcontent-%COMP%]:hover {\\n  background: #e9ecef;\\n  border-color: #ced4da;\\n}\\n\\n.file-name[_ngcontent-%COMP%] {\\n  flex: 1;\\n  font-weight: 500;\\n  color: #495057;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n  margin-right: 12px;\\n}\\n.file-name[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDCC4\\\";\\n  margin-right: 8px;\\n  font-size: 16px;\\n}\\n\\n.remove-file[_ngcontent-%COMP%] {\\n  background: #dc3545;\\n  border: none;\\n  color: white;\\n  cursor: pointer;\\n  font-size: 12px;\\n  line-height: 1;\\n  padding: 4px 8px;\\n  border-radius: 4px;\\n  font-weight: 500;\\n  transition: background-color 0.2s ease;\\n}\\n.remove-file[_ngcontent-%COMP%]:hover {\\n  background: #c82333;\\n}\\n.remove-file[_ngcontent-%COMP%]::after {\\n  content: \\\"Remove\\\";\\n}\\n\\n\\n\\n[_nghost-%COMP%]     .message-content .file-attachment-info {\\n  background: #f8f9fa;\\n  border: 1px solid #e9ecef;\\n  border-radius: 6px;\\n  padding: 8px 12px;\\n  margin-top: 8px;\\n  font-size: 12px;\\n  color: #6c757d;\\n}\\n[_nghost-%COMP%]     .message-content .file-attachment-info::before {\\n  content: \\\"\\uD83D\\uDCCE\\\";\\n  margin-right: 6px;\\n}\\n\\n.copied-toast[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: 24px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  background-color: #333;\\n  color: #fff;\\n  padding: 8px 16px;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  z-index: 1000;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25);\\n  animation: _ngcontent-%COMP%_fadeInOut 2s ease-in-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeInOut {\\n  0% {\\n    opacity: 0;\\n    transform: translateX(-50%) translateY(10px);\\n  }\\n  10%, 90% {\\n    opacity: 1;\\n    transform: translateX(-50%) translateY(0);\\n  }\\n  100% {\\n    opacity: 0;\\n    transform: translateX(-50%) translateY(-10px);\\n  }\\n}\\n.input-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  background: #fff;\\n  border: 2px solid #03acc1;\\n  border-radius: 16px;\\n  padding: 12px;\\n  margin: 1rem;\\n  box-sizing: border-box;\\n  min-height: 80px;\\n}\\n\\n\\n\\n.input-container[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border: none;\\n  resize: none;\\n  background: transparent;\\n  font-size: 14px;\\n  font-family: \\\"Inter\\\", sans-serif;\\n  line-height: 1.4;\\n  outline: none;\\n  padding: 0;\\n  padding-right: 48px; \\n\\n  box-sizing: border-box;\\n  min-height: 3em; \\n\\n  max-height: 4.2em; \\n\\n  overflow-y: auto;\\n}\\n\\n.input-container[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]::placeholder {\\n  color: #999;\\n}\\n\\n\\n\\n.attach-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 8px;\\n  left: 12px;\\n  background: none;\\n  border: none;\\n  padding: 4px;\\n  cursor: pointer;\\n}\\n\\n.attach-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 18px;\\n  height: 18px;\\n  fill: #e91e63;\\n}\\n\\n\\n\\n.right-icons[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 8px;\\n  right: 12px;\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.right-icons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 4px;\\n}\\n\\n.right-icons[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 18px;\\n  height: 18px;\\n  fill: #e91e63;\\n}\\n\\n\\n\\n.agent-details-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  margin-right: 16px;\\n  padding: 12px 16px;\\n  background: var(--color-surface-secondary, #f8f9fa);\\n  border: 1px solid var(--color-border-primary, #e1e5e9);\\n  border-radius: 8px;\\n}\\n.agent-details-container[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%]   .agent-name[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: var(--color-text-primary, #1a1d21);\\n  margin: 0 0 4px 0;\\n}\\n.agent-details-container[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%]   .agent-description[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--color-text-secondary, #6c757d);\\n  margin: 0 0 8px 0;\\n  line-height: 1.4;\\n}\\n.agent-details-container[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%]   .agent-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n}\\n.agent-details-container[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%]   .agent-meta[_ngcontent-%COMP%]   .agent-role[_ngcontent-%COMP%], \\n.agent-details-container[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%]   .agent-meta[_ngcontent-%COMP%]   .agent-goal[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: var(--color-text-tertiary, #8e9297);\\n  font-weight: 500;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return AgentExecutionPlaygroundComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "EventEmitter", "FormsModule", "FormControl", "ReactiveFormsModule", "IconComponent", "ToggleComponent", "i0", "ɵɵelementStart", "ɵɵlistener", "AgentExecutionPlaygroundComponent_div_3_button_3_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "msg_r3", "ɵɵnextContext", "$implicit", "ctx_r3", "ɵɵresetView", "copyToClipboard", "text", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ɵɵtext", "ɵɵtemplate", "AgentExecutionPlaygroundComponent_div_3_button_3_Template", "AgentExecutionPlaygroundComponent_div_3_div_4_Template", "from", "ɵɵpureFunction2", "_c2", "ɵɵtextInterpolate1", "showCopiedToast", "AgentExecutionPlaygroundComponent_button_8_Template_button_click_0_listener", "_r5", "fileInput_r6", "ɵɵreference", "click", "AgentExecutionPlaygroundComponent_div_11_div_1_Template_button_click_3_listener", "i_r8", "_r7", "index", "removeFile", "ɵɵtextInterpolate", "file_r9", "documentName", "AgentExecutionPlaygroundComponent_div_11_div_1_Template", "filesUploadedData", "AgentExecutionPlaygroundComponent_div_15_div_1_Template_ava_toggle_checkedChange_1_listener", "$event", "_r10", "onConversationalToggle", "isConvChecked", "AgentExecutionPlaygroundComponent_div_15_div_2_Template_ava_toggle_checkedChange_1_listener", "_r11", "onTemplateToggle", "isUseTemplate", "AgentExecutionPlaygroundComponent_div_15_div_1_Template", "AgentExecutionPlaygroundComponent_div_15_div_2_Template", "showChatInteractionToggles", "AgentExecutionPlaygroundComponent", "isMenuOpen", "isToolMenuOpen", "prompt<PERSON><PERSON>e", "promptOptions", "selected<PERSON><PERSON><PERSON>", "agentType", "showAiPrincipleToggle", "showDropdown", "showAgentNameInput", "agentNamePlaceholder", "displayedAgentName", "showFileUploadButton", "selected<PERSON><PERSON><PERSON>", "agentNameDisplayControl", "value", "disabled", "inputText", "previousMessages<PERSON><PERSON><PERSON>", "shouldScrollToBottom", "messages", "isLoading", "isDisabled", "<PERSON><PERSON><PERSON><PERSON>", "messageSent", "conversationalToggle", "templateToggle", "filesSelected", "messagesContainer", "fileInput", "showApprovalButton", "approvalRequested", "isMinimalView", "acceptedFileType", "ngOnInit", "setValue", "ngOnChanges", "changes", "currentValue", "undefined", "ngAfterViewChecked", "scrollToBottom", "nativeElement", "scrollTop", "scrollHeight", "err", "console", "error", "handleSendMessage", "trim", "messageText", "emit", "clearFiles", "toggleMenu", "onAiPrincipleToggle", "event", "log", "onPromptChange", "selectionData", "<PERSON><PERSON><PERSON>", "selectedOptions", "length", "name", "selectedOption", "find", "opt", "navigator", "clipboard", "writeText", "then", "setTimeout", "save", "export", "onClickOutside", "target", "closest", "onEnterKeydown", "shift<PERSON>ey", "preventDefault", "onFileSelected", "files", "i", "file", "push", "id", "Date", "now", "isImage", "type", "startsWith", "splice", "trackByIndex", "_item", "onApprovalClick", "selectors", "viewQuery", "AgentExecutionPlaygroundComponent_Query", "rf", "ctx", "AgentExecutionPlaygroundComponent_click_HostBindingHandler", "ɵɵresolveDocument", "AgentExecutionPlaygroundComponent_keydown_enter_HostBindingHandler", "AgentExecutionPlaygroundComponent_div_3_Template", "AgentExecutionPlaygroundComponent_div_4_Template", "AgentExecutionPlaygroundComponent_div_5_Template", "ɵɵtwoWayListener", "AgentExecutionPlaygroundComponent_Template_textarea_ngModelChange_7_listener", "_r1", "ɵɵtwoWayBindingSet", "AgentExecutionPlaygroundComponent_Template_textarea_keydown_enter_7_listener", "AgentExecutionPlaygroundComponent_button_8_Template", "AgentExecutionPlaygroundComponent_Template_input_change_9_listener", "AgentExecutionPlaygroundComponent_div_11_Template", "AgentExecutionPlaygroundComponent_Template_button_click_13_listener", "AgentExecutionPlaygroundComponent_div_15_Template", "ɵɵtwoWayProperty", "i1", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\agents\\agent-execution\\components\\agent-execution-playground\\agent-execution-playground.component.ts", "C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\agents\\agent-execution\\components\\agent-execution-playground\\agent-execution-playground.component.html"], "sourcesContent": ["import { CommonModule } from '@angular/common';\r\nimport {\r\n  AfterViewChecked,\r\n  Component,\r\n  ElementRef,\r\n  EventEmitter,\r\n  HostListener,\r\n  Input,\r\n  OnChanges,\r\n  OnInit,\r\n  Output,\r\n  ViewChild,\r\n} from '@angular/core';\r\nimport { ChatMessage } from '../../../../../shared/components/chat-window';\r\nimport { FormsModule, FormControl, ReactiveFormsModule } from '@angular/forms';\r\nimport {\r\n  ButtonComponent,\r\n  DropdownComponent,\r\n  IconComponent,\r\n  ToggleComponent,\r\n  DropdownOption,\r\n  AvaTextboxComponent,\r\n} from '@ava/play-comp-library';\r\n\r\ninterface Tool {\r\n  id: number;\r\n  name: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-agent-execution-playground',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    DropdownComponent,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    ButtonComponent,\r\n    ToggleComponent,\r\n    IconComponent,\r\n    DropdownComponent,\r\n    AvaTextboxComponent,\r\n  ],\r\n  templateUrl: './agent-execution-playground.component.html',\r\n  styleUrl: './agent-execution-playground.component.scss',\r\n})\r\nexport class AgentExecutionPlaygroundComponent\r\n  implements OnInit, OnChanges, AfterViewChecked\r\n{\r\n  isMenuOpen = false;\r\n  isToolMenuOpen = false;\r\n  @Output() promptChange = new EventEmitter<DropdownOption>();\r\n  @Input() promptOptions: DropdownOption[] = [];\r\n  @Input() selectedValue: string = 'default'; // Input for pre-selected value\r\n  @Input() agentType: string = 'individual'; // Input for agent type ('individual' or 'collaborative')\r\n  @Input() showChatInteractionToggles: boolean = false; // Input to show conversational and template toggles\r\n  @Input() showAiPrincipleToggle: boolean = false; // Input to show AI principle toggle\r\n  @Input() showDropdown: boolean = true; // Input to control dropdown visibility\r\n  @Input() showAgentNameInput: boolean = false; // Input to show disabled agent name input field\r\n  @Input() agentNamePlaceholder: string = 'Agent Name'; // Placeholder for agent name input\r\n  @Input() displayedAgentName: string = ''; // Agent name to display in disabled input\r\n  @Input() showFileUploadButton: boolean = false; // Controls visibility of attach file button\r\n  selectedPrompt: string = 'default';\r\n\r\n  // Form control for agent name display\r\n  agentNameDisplayControl = new FormControl({ value: '', disabled: true });\r\n\r\n  // Chat data\r\n  showCopiedToast = false;\r\n  inputText: string = '';\r\n  previousMessagesLength = 0;\r\n  shouldScrollToBottom = false;\r\n  @Input() messages: ChatMessage[] = [];\r\n  @Input() isLoading: boolean = false;\r\n  @Input() isDisabled: boolean = false;\r\n  @Input() showLoader: boolean = true;\r\n  @Output() messageSent = new EventEmitter<string>();\r\n  @Output() conversationalToggle = new EventEmitter<boolean>();\r\n  @Output() templateToggle = new EventEmitter<boolean>();\r\n  @Output() filesSelected = new EventEmitter<any[]>();\r\n  @ViewChild('messagesContainer') messagesContainer!: ElementRef;\r\n  @ViewChild('fileInput') fileInput!: ElementRef;\r\n  @Input() showApprovalButton: boolean = true;\r\n  @Output() approvalRequested = new EventEmitter<void>();\r\n  @Input() isMinimalView: boolean = false;\r\n\r\n  // Simple toggle properties for display only\r\n  public isConvChecked: boolean = true;\r\n  public isUseTemplate: boolean = false;\r\n\r\n  // File upload properties\r\n  public filesUploadedData: any[] = [];\r\n  @Input() acceptedFileType: string =\r\n    '.pdf,.doc,.docx,.txt,.csv,.xlsx,.xls,.ppt,.pptx,.png,.jpg,.jpeg,.gif,.bmp,.svg';\r\n\r\n  ngOnInit(): void {\r\n    this.messages = [\r\n      {\r\n        from: 'ai',\r\n        text: 'Hi there, how can I help you today?',\r\n      },\r\n    ];\r\n    this.shouldScrollToBottom = true;\r\n\r\n    // Set selected prompt from input\r\n    if (this.selectedValue) {\r\n      this.selectedPrompt = this.selectedValue;\r\n      // Update displayed agent name if showing agent name input\r\n      if (this.showAgentNameInput && !this.displayedAgentName) {\r\n        this.displayedAgentName = this.selectedValue;\r\n        this.agentNameDisplayControl.setValue(this.selectedValue);\r\n      }\r\n    }\r\n\r\n    // Initialize agent name display control\r\n    if (this.displayedAgentName) {\r\n      this.agentNameDisplayControl.setValue(this.displayedAgentName);\r\n    }\r\n  }\r\n\r\n  ngOnChanges(changes: any): void {\r\n    // Update selectedPrompt when selectedValue input changes\r\n    if (changes['selectedValue'] && changes['selectedValue'].currentValue) {\r\n      // The selectedValue from parent should be the name (for dropdown display)\r\n      this.selectedPrompt = changes['selectedValue'].currentValue;\r\n    }\r\n\r\n    // Update agent name display control when displayedAgentName input changes\r\n    if (\r\n      changes['displayedAgentName'] &&\r\n      changes['displayedAgentName'].currentValue !== undefined\r\n    ) {\r\n      this.agentNameDisplayControl.setValue(\r\n        changes['displayedAgentName'].currentValue,\r\n      );\r\n    }\r\n  }\r\n\r\n  ngAfterViewChecked() {\r\n    if (this.shouldScrollToBottom) {\r\n      this.scrollToBottom();\r\n      this.shouldScrollToBottom = false;\r\n    }\r\n  }\r\n\r\n  scrollToBottom(): void {\r\n    try {\r\n      if (this.messagesContainer && this.messagesContainer.nativeElement) {\r\n        // Scroll to bottom to show latest messages\r\n        this.messagesContainer.nativeElement.scrollTop =\r\n          this.messagesContainer.nativeElement.scrollHeight;\r\n      }\r\n    } catch (err) {\r\n      console.error('Error scrolling to bottom:', err);\r\n    }\r\n  }\r\n\r\n  handleSendMessage(): void {\r\n    if (!this.inputText.trim() || this.isDisabled) {\r\n      return;\r\n    }\r\n\r\n    // Add user message to the chat\r\n    this.messages = [\r\n      ...this.messages,\r\n      {\r\n        from: 'user',\r\n        text: this.inputText,\r\n      },\r\n    ];\r\n    this.shouldScrollToBottom = true;\r\n\r\n    // Emit the message to parent component\r\n    const messageText = this.inputText;\r\n    this.inputText = '';\r\n    this.messageSent.emit(messageText);\r\n\r\n    // Clear uploaded files after sending message\r\n    this.clearFiles();\r\n  }\r\n\r\n  private clearFiles(): void {\r\n    this.filesUploadedData = [];\r\n    this.filesSelected.emit(this.filesUploadedData);\r\n  }\r\n\r\n  toggleMenu() {\r\n    this.isMenuOpen = !this.isMenuOpen;\r\n  }\r\n\r\n  onAiPrincipleToggle(event: any) {\r\n    console.log('AI Principles toggle:', event);\r\n  }\r\n\r\n  onConversationalToggle(event: any) {\r\n    this.isConvChecked = event;\r\n\r\n    // If conversational is enabled, disable template\r\n    if (event && this.isUseTemplate) {\r\n      this.isUseTemplate = false;\r\n      this.templateToggle.emit(false);\r\n    }\r\n\r\n    console.log('Conversational mode:', event);\r\n    this.conversationalToggle.emit(event);\r\n  }\r\n\r\n  onTemplateToggle(event: any) {\r\n    this.isUseTemplate = event;\r\n\r\n    // If template is enabled, disable conversational\r\n    if (event && this.isConvChecked) {\r\n      this.isConvChecked = false;\r\n      this.conversationalToggle.emit(false);\r\n    }\r\n\r\n    console.log('Use template:', event);\r\n    this.templateToggle.emit(event);\r\n  }\r\n\r\n  onPromptChange(selectionData: any): void {\r\n    // The dropdown component emits an object with selectedOptions and selectedValue\r\n    // selectedValue contains the name of the selected option\r\n    let selectedName: string;\r\n\r\n    if (typeof selectionData === 'string') {\r\n      selectedName = selectionData;\r\n    } else if (selectionData && selectionData.selectedValue) {\r\n      selectedName = selectionData.selectedValue;\r\n    } else if (\r\n      selectionData &&\r\n      selectionData.selectedOptions &&\r\n      selectionData.selectedOptions.length > 0\r\n    ) {\r\n      selectedName = selectionData.selectedOptions[0].name;\r\n    } else {\r\n      return;\r\n    }\r\n\r\n    this.selectedPrompt = selectedName;\r\n\r\n    // Update displayed agent name if showing agent name input\r\n    if (this.showAgentNameInput) {\r\n      this.displayedAgentName = selectedName;\r\n      this.agentNameDisplayControl.setValue(selectedName);\r\n    }\r\n\r\n    // Find the option by name\r\n    const selectedOption = this.promptOptions.find(\r\n      (opt) => opt.name === selectedName,\r\n    );\r\n\r\n    if (selectedOption) {\r\n      this.promptChange.emit(selectedOption);\r\n    }\r\n  }\r\n\r\n  copyToClipboard(text: string): void {\r\n    navigator.clipboard.writeText(text).then(() => {\r\n      this.showCopiedToast = true;\r\n      setTimeout(() => {\r\n        this.showCopiedToast = false;\r\n      }, 2000);\r\n    });\r\n  }\r\n  save() {\r\n    this.isMenuOpen = false;\r\n    console.log('Save clicked');\r\n    // your save logic here\r\n  }\r\n\r\n  export() {\r\n    this.isMenuOpen = false;\r\n    console.log('Export clicked');\r\n    // your export logic here\r\n  }\r\n\r\n  // Hide menu when clicking outside\r\n  @HostListener('document:click', ['$event'])\r\n  onClickOutside(event: Event) {\r\n    const target = event.target as HTMLElement;\r\n    if (!target.closest('.btn-menu')) {\r\n      this.isMenuOpen = false;\r\n    }\r\n  }\r\n\r\n  @HostListener('keydown.enter', ['$event'])\r\n  onEnterKeydown(event: KeyboardEvent): void {\r\n    // Only prevent default and send if Shift key is not pressed\r\n    if (!event.shiftKey) {\r\n      event.preventDefault();\r\n      this.handleSendMessage();\r\n    }\r\n  }\r\n\r\n  // File upload methods\r\n  onFileSelected(event: any): void {\r\n    const files = event.target.files;\r\n    if (files && files.length > 0) {\r\n      this.filesUploadedData = [];\r\n\r\n      for (let i = 0; i < files.length; i++) {\r\n        const file = files[i];\r\n        this.filesUploadedData.push({\r\n          id: `file_${Date.now()}_${i}`,\r\n          documentName: file.name,\r\n          isImage: file.type.startsWith('image/'),\r\n          file: file,\r\n        });\r\n      }\r\n\r\n      console.log('Files selected:', this.filesUploadedData);\r\n      this.filesSelected.emit(this.filesUploadedData);\r\n    }\r\n  }\r\n\r\n  removeFile(index: number): void {\r\n    this.filesUploadedData.splice(index, 1);\r\n    this.filesSelected.emit(this.filesUploadedData);\r\n  }\r\n\r\n  // Track by function for ngFor performance\r\n  trackByIndex(index: number, _item: any): number {\r\n    return index;\r\n  }\r\n\r\n  onApprovalClick() {\r\n    this.approvalRequested.emit();\r\n  }\r\n}\r\n", "<div class=\"playground-container\">\r\n  <!-- <div class=\"button-container\">\r\n    <!-- Agent Selection Dropdown -->\r\n  <!-- <div class=\"dropdown-container\" *ngIf=\"showDropdown\">\r\n      <ava-dropdown\r\n        *ngIf=\"!isMinimalView\"\r\n        dropdownTitle=\"Choose Agent\"\r\n        [options]=\"promptOptions\"\r\n        [selectedValue]=\"selectedPrompt\"\r\n        [enableSearch]=\"true\"\r\n        [singleSelect]=\"true\"\r\n        (selectionChange)=\"onPromptChange($event)\"\r\n      >\r\n      </ava-dropdown>\r\n    </div> -->\r\n\r\n  <!-- Disabled Agent Name Input Field -->\r\n  <!-- <div class=\"agent-name-display\" *ngIf=\"showAgentNameInput\">\r\n      <ava-textbox\r\n        [placeholder]=\"agentNamePlaceholder\"\r\n        [formControl]=\"agentNameDisplayControl\"\r\n        variant=\"default\"\r\n        size=\"md\"\r\n        class=\"disabled-agent-name-input\"\r\n      >\r\n      </ava-textbox>\r\n    </div> -->\r\n\r\n  <!-- <div class=\"btn-menu\">\r\n      <ava-button\r\n        *ngIf=\"showApprovalButton\"\r\n        label=\"Send for Approval\"\r\n        size=\"small\"\r\n        state=\"active\"\r\n        variant=\"primary\"\r\n        [customStyles]=\"{\r\n          background:\r\n            'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n          '--button-effect-color': '33, 90, 214',\r\n        }\"\r\n        (userClick)=\"onApprovalClick()\"\r\n      ></ava-button>\r\n      <div class=\"menu-icon\" (click)=\"toggleMenu()\" #menuIcon>\r\n        <span></span>\r\n        <span></span>\r\n        <span></span>\r\n      </div>\r\n\r\n      <div class=\"dot-dropdown-menu\" *ngIf=\"isMenuOpen\">\r\n        <button (click)=\"save()\">\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            width=\"24\"\r\n            height=\"24\"\r\n            fill=\"currentColor\"\r\n            viewBox=\"0 0 24 24\"\r\n          >\r\n            <path\r\n              d=\"M17 3H5a2 2 0 0 0-2 2v14l4-4h10a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2z\"\r\n            />\r\n          </svg>\r\n          Save\r\n        </button>\r\n\r\n        <button (click)=\"export()\">\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            width=\"24\"\r\n            height=\"24\"\r\n            fill=\"currentColor\"\r\n            viewBox=\"0 0 24 24\"\r\n          >\r\n            <path d=\"M5 20h14v-2H5m14-9h-4V3H9v6H5l7 7 7-7z\" />\r\n          </svg>\r\n          Export\r\n        </button>\r\n      </div>\r\n    </div> -->\r\n  <!-- </div> -->\r\n  <div class=\"layout\" #messagesContainer>\r\n    <!-- Messages in normal order -->\r\n    <div\r\n      *ngFor=\"let msg of messages; trackBy: trackByIndex\"\r\n      class=\"message-row\"\r\n      [ngClass]=\"msg.from\"\r\n    >\r\n      <div\r\n        [ngClass]=\"{\r\n          'bot-response': msg.from === 'ai',\r\n          'user-message': msg.from === 'user',\r\n        }\"\r\n      >\r\n        {{ msg.text }}\r\n        <button\r\n          *ngIf=\"msg.from === 'ai'\"\r\n          class=\"copy-btn\"\r\n          (click)=\"copyToClipboard(msg.text)\"\r\n          title=\"Copy\"\r\n        >\r\n          <ava-icon\r\n            slot=\"icon-start\"\r\n            iconName=\"copy\"\r\n            [iconSize]=\"16\"\r\n            iconColor=\"var(--color-brand-primary)\"\r\n          >\r\n          </ava-icon>\r\n        </button>\r\n      </div>\r\n      <div *ngIf=\"showCopiedToast\" class=\"copied-toast\">Copied!</div>\r\n    </div>\r\n\r\n    <!-- Animated loading indicator when API call is in progress -->\r\n    <div *ngIf=\"isLoading && showLoader\" class=\"message-row ai\">\r\n      <div class=\"bot-response loading-message\">\r\n        <div class=\"typing-indicator\">\r\n          <span class=\"dot\"></span>\r\n          <span class=\"dot\"></span>\r\n          <span class=\"dot\"></span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Simple text loading indicator for tool testing -->\r\n    <div *ngIf=\"isLoading && !showLoader\" class=\"message-row ai\">\r\n      <div class=\"bot-response\">...</div>\r\n    </div>\r\n  </div>\r\n\r\n  <div class=\"input-container\">\r\n    <textarea\r\n      [(ngModel)]=\"inputText\"\r\n      [disabled]=\"isDisabled || isLoading\"\r\n      (keydown.enter)=\"handleSendMessage(); $event.preventDefault()\"\r\n      placeholder=\"Enter something to test\"\r\n    ></textarea>\r\n\r\n    <button\r\n      *ngIf=\"showFileUploadButton\"\r\n      class=\"attach-btn\"\r\n      title=\"Attach File\"\r\n      (click)=\"fileInput.click()\"\r\n    >\r\n      <ava-icon\r\n        slot=\"icon-start\"\r\n        iconName=\"paperclip\"\r\n        [iconSize]=\"16\"\r\n        iconColor=\"var(--color-brand-primary)\"\r\n      >\r\n      </ava-icon>\r\n    </button>\r\n\r\n    <!-- Hidden file input -->\r\n    <input\r\n      #fileInput\r\n      type=\"file\"\r\n      [accept]=\"acceptedFileType\"\r\n      multiple\r\n      style=\"display: none\"\r\n      (change)=\"onFileSelected($event)\"\r\n    />\r\n\r\n    <!-- Display uploaded files -->\r\n    <div class=\"uploaded-files\" *ngIf=\"filesUploadedData.length > 0\">\r\n      <div\r\n        class=\"file-item\"\r\n        *ngFor=\"let file of filesUploadedData; let i = index\"\r\n      >\r\n        <span class=\"file-name\">{{ file.documentName }}</span>\r\n        <button\r\n          class=\"remove-file\"\r\n          (click)=\"removeFile(i)\"\r\n          title=\"Remove file\"\r\n        ></button>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"right-icons\">\r\n      <!-- <button class=\"edit-btn\" title=\"Edit\">\r\n     <ava-icon slot=\"icon-start\" iconName=\"wand-sparkles\" [iconSize]=\"16\" iconColor=\"var(--color-brand-primary)\">\r\n        </ava-icon>\r\n    </button> -->\r\n      <button\r\n        class=\"send-btn\"\r\n        title=\"Send\"\r\n        (click)=\"handleSendMessage()\"\r\n        [disabled]=\"isLoading || isDisabled\"\r\n      >\r\n        <ava-icon\r\n          slot=\"icon-start\"\r\n          iconName=\"send-horizontal\"\r\n          [iconSize]=\"16\"\r\n          iconColor=\"var(--color-brand-primary)\"\r\n        >\r\n        </ava-icon>\r\n      </button>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Toggles Container - All toggles in same line when present -->\r\n  <div\r\n    class=\"toggle-container\"\r\n    *ngIf=\"showChatInteractionToggles || showAiPrincipleToggle\"\r\n  >\r\n    <!-- Conversational Toggle -->\r\n    <div class=\"toggle-row\" *ngIf=\"showChatInteractionToggles\">\r\n      <ava-toggle\r\n        [checked]=\"isConvChecked\"\r\n        [title]=\"'Conversational'\"\r\n        [position]=\"'left'\"\r\n        size=\"small\"\r\n        (checkedChange)=\"onConversationalToggle($event)\"\r\n      >\r\n      </ava-toggle>\r\n    </div>\r\n\r\n    <!-- Use Template Toggle -->\r\n    <div class=\"toggle-row\" *ngIf=\"showChatInteractionToggles\">\r\n      <ava-toggle\r\n        [checked]=\"isUseTemplate\"\r\n        [title]=\"'Use Template'\"\r\n        [position]=\"'left'\"\r\n        size=\"small\"\r\n        (checkedChange)=\"onTemplateToggle($event)\"\r\n      >\r\n      </ava-toggle>\r\n    </div>\r\n\r\n    <!-- AI Principles Toggle -->\r\n    <!-- <div class=\"toggle-row\" *ngIf=\"showAiPrincipleToggle\">\r\n      <ava-toggle\r\n        [checked]=\"true\"\r\n        [title]=\"'AI Principles'\"\r\n        [position]=\"'left'\"\r\n        size=\"small\"\r\n        (checkedChange)=\"onAiPrincipleToggle($event)\">\r\n      </ava-toggle>\r\n    </div> -->\r\n  </div>\r\n</div>\r\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAIEC,YAAY,QAOP,eAAe;AAEtB,SAASC,WAAW,EAAEC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AAC9E,SAGEC,aAAa,EACbC,eAAe,QAGV,wBAAwB;;;;;;;;;;;;;ICuEvBC,EAAA,CAAAC,cAAA,iBAKC;IAFCD,EAAA,CAAAE,UAAA,mBAAAC,kFAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASD,MAAA,CAAAE,eAAA,CAAAL,MAAA,CAAAM,IAAA,CAAyB;IAAA,EAAC;IAGnCZ,EAAA,CAAAa,SAAA,mBAMW;IACbb,EAAA,CAAAc,YAAA,EAAS;;;IAJLd,EAAA,CAAAe,SAAA,EAAe;IAAff,EAAA,CAAAgB,UAAA,gBAAe;;;;;IAMrBhB,EAAA,CAAAC,cAAA,cAAkD;IAAAD,EAAA,CAAAiB,MAAA,cAAO;IAAAjB,EAAA,CAAAc,YAAA,EAAM;;;;;IAtB/Dd,EALF,CAAAC,cAAA,cAIC,cAME;IACCD,EAAA,CAAAiB,MAAA,GACA;IAAAjB,EAAA,CAAAkB,UAAA,IAAAC,yDAAA,qBAKC;IASHnB,EAAA,CAAAc,YAAA,EAAM;IACNd,EAAA,CAAAkB,UAAA,IAAAE,sDAAA,kBAAkD;IACpDpB,EAAA,CAAAc,YAAA,EAAM;;;;;IAzBJd,EAAA,CAAAgB,UAAA,YAAAV,MAAA,CAAAe,IAAA,CAAoB;IAGlBrB,EAAA,CAAAe,SAAA,EAGE;IAHFf,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAAsB,eAAA,IAAAC,GAAA,EAAAjB,MAAA,CAAAe,IAAA,WAAAf,MAAA,CAAAe,IAAA,aAGE;IAEFrB,EAAA,CAAAe,SAAA,EACA;IADAf,EAAA,CAAAwB,kBAAA,MAAAlB,MAAA,CAAAM,IAAA,MACA;IACGZ,EAAA,CAAAe,SAAA,EAAuB;IAAvBf,EAAA,CAAAgB,UAAA,SAAAV,MAAA,CAAAe,IAAA,UAAuB;IActBrB,EAAA,CAAAe,SAAA,EAAqB;IAArBf,EAAA,CAAAgB,UAAA,SAAAP,MAAA,CAAAgB,eAAA,CAAqB;;;;;IAMzBzB,EAFJ,CAAAC,cAAA,cAA4D,cAChB,cACV;IAG5BD,EAFA,CAAAa,SAAA,eAAyB,eACA,eACA;IAG/Bb,EAFI,CAAAc,YAAA,EAAM,EACF,EACF;;;;;IAIJd,EADF,CAAAC,cAAA,cAA6D,cACjC;IAAAD,EAAA,CAAAiB,MAAA,UAAG;IAC/BjB,EAD+B,CAAAc,YAAA,EAAM,EAC/B;;;;;;IAWNd,EAAA,CAAAC,cAAA,iBAKC;IADCD,EAAA,CAAAE,UAAA,mBAAAwB,4EAAA;MAAA1B,EAAA,CAAAI,aAAA,CAAAuB,GAAA;MAAA3B,EAAA,CAAAO,aAAA;MAAA,MAAAqB,YAAA,GAAA5B,EAAA,CAAA6B,WAAA;MAAA,OAAA7B,EAAA,CAAAU,WAAA,CAASkB,YAAA,CAAAE,KAAA,EAAiB;IAAA,EAAC;IAE3B9B,EAAA,CAAAa,SAAA,mBAMW;IACbb,EAAA,CAAAc,YAAA,EAAS;;;IAJLd,EAAA,CAAAe,SAAA,EAAe;IAAff,EAAA,CAAAgB,UAAA,gBAAe;;;;;;IAsBfhB,EAJF,CAAAC,cAAA,cAGC,eACyB;IAAAD,EAAA,CAAAiB,MAAA,GAAuB;IAAAjB,EAAA,CAAAc,YAAA,EAAO;IACtDd,EAAA,CAAAC,cAAA,iBAIC;IAFCD,EAAA,CAAAE,UAAA,mBAAA6B,gFAAA;MAAA,MAAAC,IAAA,GAAAhC,EAAA,CAAAI,aAAA,CAAA6B,GAAA,EAAAC,KAAA;MAAA,MAAAzB,MAAA,GAAAT,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASD,MAAA,CAAA0B,UAAA,CAAAH,IAAA,CAAa;IAAA,EAAC;IAG3BhC,EADG,CAAAc,YAAA,EAAS,EACN;;;;IANoBd,EAAA,CAAAe,SAAA,GAAuB;IAAvBf,EAAA,CAAAoC,iBAAA,CAAAC,OAAA,CAAAC,YAAA,CAAuB;;;;;IALnDtC,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAkB,UAAA,IAAAqB,uDAAA,kBAGC;IAQHvC,EAAA,CAAAc,YAAA,EAAM;;;;IATed,EAAA,CAAAe,SAAA,EAAsB;IAAtBf,EAAA,CAAAgB,UAAA,YAAAP,MAAA,CAAA+B,iBAAA,CAAsB;;;;;;IAwCzCxC,EADF,CAAAC,cAAA,cAA2D,qBAOxD;IADCD,EAAA,CAAAE,UAAA,2BAAAuC,4FAAAC,MAAA;MAAA1C,EAAA,CAAAI,aAAA,CAAAuC,IAAA;MAAA,MAAAlC,MAAA,GAAAT,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAiBD,MAAA,CAAAmC,sBAAA,CAAAF,MAAA,CAA8B;IAAA,EAAC;IAGpD1C,EADE,CAAAc,YAAA,EAAa,EACT;;;;IAPFd,EAAA,CAAAe,SAAA,EAAyB;IAEzBf,EAFA,CAAAgB,UAAA,YAAAP,MAAA,CAAAoC,aAAA,CAAyB,2BACC,oBACP;;;;;;IASrB7C,EADF,CAAAC,cAAA,cAA2D,qBAOxD;IADCD,EAAA,CAAAE,UAAA,2BAAA4C,4FAAAJ,MAAA;MAAA1C,EAAA,CAAAI,aAAA,CAAA2C,IAAA;MAAA,MAAAtC,MAAA,GAAAT,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAiBD,MAAA,CAAAuC,gBAAA,CAAAN,MAAA,CAAwB;IAAA,EAAC;IAG9C1C,EADE,CAAAc,YAAA,EAAa,EACT;;;;IAPFd,EAAA,CAAAe,SAAA,EAAyB;IAEzBf,EAFA,CAAAgB,UAAA,YAAAP,MAAA,CAAAwC,aAAA,CAAyB,yBACD,oBACL;;;;;IArBzBjD,EAAA,CAAAC,cAAA,cAGC;IAcCD,EAZA,CAAAkB,UAAA,IAAAgC,uDAAA,kBAA2D,IAAAC,uDAAA,kBAYA;IAqB7DnD,EAAA,CAAAc,YAAA,EAAM;;;;IAjCqBd,EAAA,CAAAe,SAAA,EAAgC;IAAhCf,EAAA,CAAAgB,UAAA,SAAAP,MAAA,CAAA2C,0BAAA,CAAgC;IAYhCpD,EAAA,CAAAe,SAAA,EAAgC;IAAhCf,EAAA,CAAAgB,UAAA,SAAAP,MAAA,CAAA2C,0BAAA,CAAgC;;;AD1K7D,WAAaC,iCAAiC;EAAxC,MAAOA,iCAAiC;IAG5CC,UAAU,GAAG,KAAK;IAClBC,cAAc,GAAG,KAAK;IACZC,YAAY,GAAG,IAAI9D,YAAY,EAAkB;IAClD+D,aAAa,GAAqB,EAAE;IACpCC,aAAa,GAAW,SAAS,CAAC,CAAC;IACnCC,SAAS,GAAW,YAAY,CAAC,CAAC;IAClCP,0BAA0B,GAAY,KAAK,CAAC,CAAC;IAC7CQ,qBAAqB,GAAY,KAAK,CAAC,CAAC;IACxCC,YAAY,GAAY,IAAI,CAAC,CAAC;IAC9BC,kBAAkB,GAAY,KAAK,CAAC,CAAC;IACrCC,oBAAoB,GAAW,YAAY,CAAC,CAAC;IAC7CC,kBAAkB,GAAW,EAAE,CAAC,CAAC;IACjCC,oBAAoB,GAAY,KAAK,CAAC,CAAC;IAChDC,cAAc,GAAW,SAAS;IAElC;IACAC,uBAAuB,GAAG,IAAIvE,WAAW,CAAC;MAAEwE,KAAK,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE,CAAC;IAExE;IACA5C,eAAe,GAAG,KAAK;IACvB6C,SAAS,GAAW,EAAE;IACtBC,sBAAsB,GAAG,CAAC;IAC1BC,oBAAoB,GAAG,KAAK;IACnBC,QAAQ,GAAkB,EAAE;IAC5BC,SAAS,GAAY,KAAK;IAC1BC,UAAU,GAAY,KAAK;IAC3BC,UAAU,GAAY,IAAI;IACzBC,WAAW,GAAG,IAAInF,YAAY,EAAU;IACxCoF,oBAAoB,GAAG,IAAIpF,YAAY,EAAW;IAClDqF,cAAc,GAAG,IAAIrF,YAAY,EAAW;IAC5CsF,aAAa,GAAG,IAAItF,YAAY,EAAS;IACnBuF,iBAAiB;IACzBC,SAAS;IACxBC,kBAAkB,GAAY,IAAI;IACjCC,iBAAiB,GAAG,IAAI1F,YAAY,EAAQ;IAC7C2F,aAAa,GAAY,KAAK;IAEvC;IACOxC,aAAa,GAAY,IAAI;IAC7BI,aAAa,GAAY,KAAK;IAErC;IACOT,iBAAiB,GAAU,EAAE;IAC3B8C,gBAAgB,GACvB,gFAAgF;IAElFC,QAAQA,CAAA;MACN,IAAI,CAACd,QAAQ,GAAG,CACd;QACEpD,IAAI,EAAE,IAAI;QACVT,IAAI,EAAE;OACP,CACF;MACD,IAAI,CAAC4D,oBAAoB,GAAG,IAAI;MAEhC;MACA,IAAI,IAAI,CAACd,aAAa,EAAE;QACtB,IAAI,CAACQ,cAAc,GAAG,IAAI,CAACR,aAAa;QACxC;QACA,IAAI,IAAI,CAACI,kBAAkB,IAAI,CAAC,IAAI,CAACE,kBAAkB,EAAE;UACvD,IAAI,CAACA,kBAAkB,GAAG,IAAI,CAACN,aAAa;UAC5C,IAAI,CAACS,uBAAuB,CAACqB,QAAQ,CAAC,IAAI,CAAC9B,aAAa,CAAC;QAC3D;MACF;MAEA;MACA,IAAI,IAAI,CAACM,kBAAkB,EAAE;QAC3B,IAAI,CAACG,uBAAuB,CAACqB,QAAQ,CAAC,IAAI,CAACxB,kBAAkB,CAAC;MAChE;IACF;IAEAyB,WAAWA,CAACC,OAAY;MACtB;MACA,IAAIA,OAAO,CAAC,eAAe,CAAC,IAAIA,OAAO,CAAC,eAAe,CAAC,CAACC,YAAY,EAAE;QACrE;QACA,IAAI,CAACzB,cAAc,GAAGwB,OAAO,CAAC,eAAe,CAAC,CAACC,YAAY;MAC7D;MAEA;MACA,IACED,OAAO,CAAC,oBAAoB,CAAC,IAC7BA,OAAO,CAAC,oBAAoB,CAAC,CAACC,YAAY,KAAKC,SAAS,EACxD;QACA,IAAI,CAACzB,uBAAuB,CAACqB,QAAQ,CACnCE,OAAO,CAAC,oBAAoB,CAAC,CAACC,YAAY,CAC3C;MACH;IACF;IAEAE,kBAAkBA,CAAA;MAChB,IAAI,IAAI,CAACrB,oBAAoB,EAAE;QAC7B,IAAI,CAACsB,cAAc,EAAE;QACrB,IAAI,CAACtB,oBAAoB,GAAG,KAAK;MACnC;IACF;IAEAsB,cAAcA,CAAA;MACZ,IAAI;QACF,IAAI,IAAI,CAACb,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,CAACc,aAAa,EAAE;UAClE;UACA,IAAI,CAACd,iBAAiB,CAACc,aAAa,CAACC,SAAS,GAC5C,IAAI,CAACf,iBAAiB,CAACc,aAAa,CAACE,YAAY;QACrD;MACF,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZC,OAAO,CAACC,KAAK,CAAC,4BAA4B,EAAEF,GAAG,CAAC;MAClD;IACF;IAEAG,iBAAiBA,CAAA;MACf,IAAI,CAAC,IAAI,CAAC/B,SAAS,CAACgC,IAAI,EAAE,IAAI,IAAI,CAAC3B,UAAU,EAAE;QAC7C;MACF;MAEA;MACA,IAAI,CAACF,QAAQ,GAAG,CACd,GAAG,IAAI,CAACA,QAAQ,EAChB;QACEpD,IAAI,EAAE,MAAM;QACZT,IAAI,EAAE,IAAI,CAAC0D;OACZ,CACF;MACD,IAAI,CAACE,oBAAoB,GAAG,IAAI;MAEhC;MACA,MAAM+B,WAAW,GAAG,IAAI,CAACjC,SAAS;MAClC,IAAI,CAACA,SAAS,GAAG,EAAE;MACnB,IAAI,CAACO,WAAW,CAAC2B,IAAI,CAACD,WAAW,CAAC;MAElC;MACA,IAAI,CAACE,UAAU,EAAE;IACnB;IAEQA,UAAUA,CAAA;MAChB,IAAI,CAACjE,iBAAiB,GAAG,EAAE;MAC3B,IAAI,CAACwC,aAAa,CAACwB,IAAI,CAAC,IAAI,CAAChE,iBAAiB,CAAC;IACjD;IAEAkE,UAAUA,CAAA;MACR,IAAI,CAACpD,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IACpC;IAEAqD,mBAAmBA,CAACC,KAAU;MAC5BT,OAAO,CAACU,GAAG,CAAC,uBAAuB,EAAED,KAAK,CAAC;IAC7C;IAEAhE,sBAAsBA,CAACgE,KAAU;MAC/B,IAAI,CAAC/D,aAAa,GAAG+D,KAAK;MAE1B;MACA,IAAIA,KAAK,IAAI,IAAI,CAAC3D,aAAa,EAAE;QAC/B,IAAI,CAACA,aAAa,GAAG,KAAK;QAC1B,IAAI,CAAC8B,cAAc,CAACyB,IAAI,CAAC,KAAK,CAAC;MACjC;MAEAL,OAAO,CAACU,GAAG,CAAC,sBAAsB,EAAED,KAAK,CAAC;MAC1C,IAAI,CAAC9B,oBAAoB,CAAC0B,IAAI,CAACI,KAAK,CAAC;IACvC;IAEA5D,gBAAgBA,CAAC4D,KAAU;MACzB,IAAI,CAAC3D,aAAa,GAAG2D,KAAK;MAE1B;MACA,IAAIA,KAAK,IAAI,IAAI,CAAC/D,aAAa,EAAE;QAC/B,IAAI,CAACA,aAAa,GAAG,KAAK;QAC1B,IAAI,CAACiC,oBAAoB,CAAC0B,IAAI,CAAC,KAAK,CAAC;MACvC;MAEAL,OAAO,CAACU,GAAG,CAAC,eAAe,EAAED,KAAK,CAAC;MACnC,IAAI,CAAC7B,cAAc,CAACyB,IAAI,CAACI,KAAK,CAAC;IACjC;IAEAE,cAAcA,CAACC,aAAkB;MAC/B;MACA;MACA,IAAIC,YAAoB;MAExB,IAAI,OAAOD,aAAa,KAAK,QAAQ,EAAE;QACrCC,YAAY,GAAGD,aAAa;MAC9B,CAAC,MAAM,IAAIA,aAAa,IAAIA,aAAa,CAACrD,aAAa,EAAE;QACvDsD,YAAY,GAAGD,aAAa,CAACrD,aAAa;MAC5C,CAAC,MAAM,IACLqD,aAAa,IACbA,aAAa,CAACE,eAAe,IAC7BF,aAAa,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EACxC;QACAF,YAAY,GAAGD,aAAa,CAACE,eAAe,CAAC,CAAC,CAAC,CAACE,IAAI;MACtD,CAAC,MAAM;QACL;MACF;MAEA,IAAI,CAACjD,cAAc,GAAG8C,YAAY;MAElC;MACA,IAAI,IAAI,CAAClD,kBAAkB,EAAE;QAC3B,IAAI,CAACE,kBAAkB,GAAGgD,YAAY;QACtC,IAAI,CAAC7C,uBAAuB,CAACqB,QAAQ,CAACwB,YAAY,CAAC;MACrD;MAEA;MACA,MAAMI,cAAc,GAAG,IAAI,CAAC3D,aAAa,CAAC4D,IAAI,CAC3CC,GAAG,IAAKA,GAAG,CAACH,IAAI,KAAKH,YAAY,CACnC;MAED,IAAII,cAAc,EAAE;QAClB,IAAI,CAAC5D,YAAY,CAACgD,IAAI,CAACY,cAAc,CAAC;MACxC;IACF;IAEAzG,eAAeA,CAACC,IAAY;MAC1B2G,SAAS,CAACC,SAAS,CAACC,SAAS,CAAC7G,IAAI,CAAC,CAAC8G,IAAI,CAAC,MAAK;QAC5C,IAAI,CAACjG,eAAe,GAAG,IAAI;QAC3BkG,UAAU,CAAC,MAAK;UACd,IAAI,CAAClG,eAAe,GAAG,KAAK;QAC9B,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,CAAC;IACJ;IACAmG,IAAIA,CAAA;MACF,IAAI,CAACtE,UAAU,GAAG,KAAK;MACvB6C,OAAO,CAACU,GAAG,CAAC,cAAc,CAAC;MAC3B;IACF;IAEAgB,MAAMA,CAAA;MACJ,IAAI,CAACvE,UAAU,GAAG,KAAK;MACvB6C,OAAO,CAACU,GAAG,CAAC,gBAAgB,CAAC;MAC7B;IACF;IAEA;IAEAiB,cAAcA,CAAClB,KAAY;MACzB,MAAMmB,MAAM,GAAGnB,KAAK,CAACmB,MAAqB;MAC1C,IAAI,CAACA,MAAM,CAACC,OAAO,CAAC,WAAW,CAAC,EAAE;QAChC,IAAI,CAAC1E,UAAU,GAAG,KAAK;MACzB;IACF;IAGA2E,cAAcA,CAACrB,KAAoB;MACjC;MACA,IAAI,CAACA,KAAK,CAACsB,QAAQ,EAAE;QACnBtB,KAAK,CAACuB,cAAc,EAAE;QACtB,IAAI,CAAC9B,iBAAiB,EAAE;MAC1B;IACF;IAEA;IACA+B,cAAcA,CAACxB,KAAU;MACvB,MAAMyB,KAAK,GAAGzB,KAAK,CAACmB,MAAM,CAACM,KAAK;MAChC,IAAIA,KAAK,IAAIA,KAAK,CAACnB,MAAM,GAAG,CAAC,EAAE;QAC7B,IAAI,CAAC1E,iBAAiB,GAAG,EAAE;QAE3B,KAAK,IAAI8F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACnB,MAAM,EAAEoB,CAAC,EAAE,EAAE;UACrC,MAAMC,IAAI,GAAGF,KAAK,CAACC,CAAC,CAAC;UACrB,IAAI,CAAC9F,iBAAiB,CAACgG,IAAI,CAAC;YAC1BC,EAAE,EAAE,QAAQC,IAAI,CAACC,GAAG,EAAE,IAAIL,CAAC,EAAE;YAC7BhG,YAAY,EAAEiG,IAAI,CAACpB,IAAI;YACvByB,OAAO,EAAEL,IAAI,CAACM,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC;YACvCP,IAAI,EAAEA;WACP,CAAC;QACJ;QAEApC,OAAO,CAACU,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAACrE,iBAAiB,CAAC;QACtD,IAAI,CAACwC,aAAa,CAACwB,IAAI,CAAC,IAAI,CAAChE,iBAAiB,CAAC;MACjD;IACF;IAEAL,UAAUA,CAACD,KAAa;MACtB,IAAI,CAACM,iBAAiB,CAACuG,MAAM,CAAC7G,KAAK,EAAE,CAAC,CAAC;MACvC,IAAI,CAAC8C,aAAa,CAACwB,IAAI,CAAC,IAAI,CAAChE,iBAAiB,CAAC;IACjD;IAEA;IACAwG,YAAYA,CAAC9G,KAAa,EAAE+G,KAAU;MACpC,OAAO/G,KAAK;IACd;IAEAgH,eAAeA,CAAA;MACb,IAAI,CAAC9D,iBAAiB,CAACoB,IAAI,EAAE;IAC/B;;uCA1RWnD,iCAAiC;IAAA;;YAAjCA,iCAAiC;MAAA8F,SAAA;MAAAC,SAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;UAAjCtJ,EAAA,CAAAE,UAAA,mBAAAsJ,2DAAA9G,MAAA;YAAA,OAAA6G,GAAA,CAAAzB,cAAA,CAAApF,MAAA,CAAsB;UAAA,UAAA1C,EAAA,CAAAyJ,iBAAA,CAAW,2BAAAC,mEAAAhH,MAAA;YAAA,OAAjC6G,GAAA,CAAAtB,cAAA,CAAAvF,MAAA,CAAsB;UAAA,EAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UCiC5C1C,EA/EF,CAAAC,cAAA,aAAkC,gBA+EO;UA4CrCD,EA1CA,CAAAkB,UAAA,IAAAyI,gDAAA,iBAIC,IAAAC,gDAAA,iBA2B2D,IAAAC,gDAAA,iBAWC;UAG/D7J,EAAA,CAAAc,YAAA,EAAM;UAGJd,EADF,CAAAC,cAAA,aAA6B,kBAM1B;UAJCD,EAAA,CAAA8J,gBAAA,2BAAAC,6EAAArH,MAAA;YAAA1C,EAAA,CAAAI,aAAA,CAAA4J,GAAA;YAAAhK,EAAA,CAAAiK,kBAAA,CAAAV,GAAA,CAAAjF,SAAA,EAAA5B,MAAA,MAAA6G,GAAA,CAAAjF,SAAA,GAAA5B,MAAA;YAAA,OAAA1C,EAAA,CAAAU,WAAA,CAAAgC,MAAA;UAAA,EAAuB;UAEvB1C,EAAA,CAAAE,UAAA,2BAAAgK,6EAAAxH,MAAA;YAAA1C,EAAA,CAAAI,aAAA,CAAA4J,GAAA;YAAiBT,GAAA,CAAAlD,iBAAA,EAAmB;YAAA,OAAArG,EAAA,CAAAU,WAAA,CAAEgC,MAAA,CAAAyF,cAAA,EAAuB;UAAA,EAAC;UAE/DnI,EAAA,CAAAc,YAAA,EAAW;UAEZd,EAAA,CAAAkB,UAAA,IAAAiJ,mDAAA,oBAKC;UAWDnK,EAAA,CAAAC,cAAA,kBAOE;UADAD,EAAA,CAAAE,UAAA,oBAAAkK,mEAAA1H,MAAA;YAAA1C,EAAA,CAAAI,aAAA,CAAA4J,GAAA;YAAA,OAAAhK,EAAA,CAAAU,WAAA,CAAU6I,GAAA,CAAAnB,cAAA,CAAA1F,MAAA,CAAsB;UAAA,EAAC;UANnC1C,EAAA,CAAAc,YAAA,EAOE;UAGFd,EAAA,CAAAkB,UAAA,KAAAmJ,iDAAA,kBAAiE;UAmB/DrK,EALF,CAAAC,cAAA,eAAyB,kBAUtB;UAFCD,EAAA,CAAAE,UAAA,mBAAAoK,oEAAA;YAAAtK,EAAA,CAAAI,aAAA,CAAA4J,GAAA;YAAA,OAAAhK,EAAA,CAAAU,WAAA,CAAS6I,GAAA,CAAAlD,iBAAA,EAAmB;UAAA,EAAC;UAG7BrG,EAAA,CAAAa,SAAA,oBAMW;UAGjBb,EAFI,CAAAc,YAAA,EAAS,EACL,EACF;UAGNd,EAAA,CAAAkB,UAAA,KAAAqJ,iDAAA,kBAGC;UAoCHvK,EAAA,CAAAc,YAAA,EAAM;;;UA5JgBd,EAAA,CAAAe,SAAA,GAAa;UAAAf,EAAb,CAAAgB,UAAA,YAAAuI,GAAA,CAAA9E,QAAA,CAAa,iBAAA8E,GAAA,CAAAP,YAAA,CAAqB;UA8B9ChJ,EAAA,CAAAe,SAAA,EAA6B;UAA7Bf,EAAA,CAAAgB,UAAA,SAAAuI,GAAA,CAAA7E,SAAA,IAAA6E,GAAA,CAAA3E,UAAA,CAA6B;UAW7B5E,EAAA,CAAAe,SAAA,EAA8B;UAA9Bf,EAAA,CAAAgB,UAAA,SAAAuI,GAAA,CAAA7E,SAAA,KAAA6E,GAAA,CAAA3E,UAAA,CAA8B;UAOlC5E,EAAA,CAAAe,SAAA,GAAuB;UAAvBf,EAAA,CAAAwK,gBAAA,YAAAjB,GAAA,CAAAjF,SAAA,CAAuB;UACvBtE,EAAA,CAAAgB,UAAA,aAAAuI,GAAA,CAAA5E,UAAA,IAAA4E,GAAA,CAAA7E,SAAA,CAAoC;UAMnC1E,EAAA,CAAAe,SAAA,EAA0B;UAA1Bf,EAAA,CAAAgB,UAAA,SAAAuI,GAAA,CAAAtF,oBAAA,CAA0B;UAkB3BjE,EAAA,CAAAe,SAAA,EAA2B;UAA3Bf,EAAA,CAAAgB,UAAA,WAAAuI,GAAA,CAAAjE,gBAAA,CAA2B;UAOAtF,EAAA,CAAAe,SAAA,GAAkC;UAAlCf,EAAA,CAAAgB,UAAA,SAAAuI,GAAA,CAAA/G,iBAAA,CAAA0E,MAAA,KAAkC;UAuB3DlH,EAAA,CAAAe,SAAA,GAAoC;UAApCf,EAAA,CAAAgB,UAAA,aAAAuI,GAAA,CAAA7E,SAAA,IAAA6E,GAAA,CAAA5E,UAAA,CAAoC;UAKlC3E,EAAA,CAAAe,SAAA,EAAe;UAAff,EAAA,CAAAgB,UAAA,gBAAe;UAWpBhB,EAAA,CAAAe,SAAA,EAAyD;UAAzDf,EAAA,CAAAgB,UAAA,SAAAuI,GAAA,CAAAnG,0BAAA,IAAAmG,GAAA,CAAA3F,qBAAA,CAAyD;;;qBDxK1DnE,YAAY,EAAAgL,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAEZjL,WAAW,EAAAkL,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EACXnL,mBAAmB,EAEnBE,eAAe,EACfD,aAAa;MAAAmL,MAAA;IAAA;;SAOJ5H,iCAAiC;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}