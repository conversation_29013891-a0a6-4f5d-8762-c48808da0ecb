{"ast": null, "code": "import { Node } from \"./hierarchy/index.js\";\nfunction defaultSeparation(a, b) {\n  return a.parent === b.parent ? 1 : 2;\n}\n\n// function radialSeparation(a, b) {\n//   return (a.parent === b.parent ? 1 : 2) / a.depth;\n// }\n\n// This function is used to traverse the left contour of a subtree (or\n// subforest). It returns the successor of v on this contour. This successor is\n// either given by the leftmost child of v or by the thread of v. The function\n// returns null if and only if v is on the highest level of its subtree.\nfunction nextLeft(v) {\n  var children = v.children;\n  return children ? children[0] : v.t;\n}\n\n// This function works analogously to nextLeft.\nfunction nextRight(v) {\n  var children = v.children;\n  return children ? children[children.length - 1] : v.t;\n}\n\n// Shifts the current subtree rooted at w+. This is done by increasing\n// prelim(w+) and mod(w+) by shift.\nfunction moveSubtree(wm, wp, shift) {\n  var change = shift / (wp.i - wm.i);\n  wp.c -= change;\n  wp.s += shift;\n  wm.c += change;\n  wp.z += shift;\n  wp.m += shift;\n}\n\n// All other shifts, applied to the smaller subtrees between w- and w+, are\n// performed by this function. To prepare the shifts, we have to adjust\n// change(w+), shift(w+), and change(w-).\nfunction executeShifts(v) {\n  var shift = 0,\n    change = 0,\n    children = v.children,\n    i = children.length,\n    w;\n  while (--i >= 0) {\n    w = children[i];\n    w.z += shift;\n    w.m += shift;\n    shift += w.s + (change += w.c);\n  }\n}\n\n// If vi-’s ancestor is a sibling of v, returns vi-’s ancestor. Otherwise,\n// returns the specified (default) ancestor.\nfunction nextAncestor(vim, v, ancestor) {\n  return vim.a.parent === v.parent ? vim.a : ancestor;\n}\nfunction TreeNode(node, i) {\n  this._ = node;\n  this.parent = null;\n  this.children = null;\n  this.A = null; // default ancestor\n  this.a = this; // ancestor\n  this.z = 0; // prelim\n  this.m = 0; // mod\n  this.c = 0; // change\n  this.s = 0; // shift\n  this.t = null; // thread\n  this.i = i; // number\n}\nTreeNode.prototype = Object.create(Node.prototype);\nfunction treeRoot(root) {\n  var tree = new TreeNode(root, 0),\n    node,\n    nodes = [tree],\n    child,\n    children,\n    i,\n    n;\n  while (node = nodes.pop()) {\n    if (children = node._.children) {\n      node.children = new Array(n = children.length);\n      for (i = n - 1; i >= 0; --i) {\n        nodes.push(child = node.children[i] = new TreeNode(children[i], i));\n        child.parent = node;\n      }\n    }\n  }\n  (tree.parent = new TreeNode(null, 0)).children = [tree];\n  return tree;\n}\n\n// Node-link tree diagram using the Reingold-Tilford \"tidy\" algorithm\nexport default function () {\n  var separation = defaultSeparation,\n    dx = 1,\n    dy = 1,\n    nodeSize = null;\n  function tree(root) {\n    var t = treeRoot(root);\n\n    // Compute the layout using Buchheim et al.’s algorithm.\n    t.eachAfter(firstWalk), t.parent.m = -t.z;\n    t.eachBefore(secondWalk);\n\n    // If a fixed node size is specified, scale x and y.\n    if (nodeSize) root.eachBefore(sizeNode);\n\n    // If a fixed tree size is specified, scale x and y based on the extent.\n    // Compute the left-most, right-most, and depth-most nodes for extents.\n    else {\n      var left = root,\n        right = root,\n        bottom = root;\n      root.eachBefore(function (node) {\n        if (node.x < left.x) left = node;\n        if (node.x > right.x) right = node;\n        if (node.depth > bottom.depth) bottom = node;\n      });\n      var s = left === right ? 1 : separation(left, right) / 2,\n        tx = s - left.x,\n        kx = dx / (right.x + s + tx),\n        ky = dy / (bottom.depth || 1);\n      root.eachBefore(function (node) {\n        node.x = (node.x + tx) * kx;\n        node.y = node.depth * ky;\n      });\n    }\n    return root;\n  }\n\n  // Computes a preliminary x-coordinate for v. Before that, FIRST WALK is\n  // applied recursively to the children of v, as well as the function\n  // APPORTION. After spacing out the children by calling EXECUTE SHIFTS, the\n  // node v is placed to the midpoint of its outermost children.\n  function firstWalk(v) {\n    var children = v.children,\n      siblings = v.parent.children,\n      w = v.i ? siblings[v.i - 1] : null;\n    if (children) {\n      executeShifts(v);\n      var midpoint = (children[0].z + children[children.length - 1].z) / 2;\n      if (w) {\n        v.z = w.z + separation(v._, w._);\n        v.m = v.z - midpoint;\n      } else {\n        v.z = midpoint;\n      }\n    } else if (w) {\n      v.z = w.z + separation(v._, w._);\n    }\n    v.parent.A = apportion(v, w, v.parent.A || siblings[0]);\n  }\n\n  // Computes all real x-coordinates by summing up the modifiers recursively.\n  function secondWalk(v) {\n    v._.x = v.z + v.parent.m;\n    v.m += v.parent.m;\n  }\n\n  // The core of the algorithm. Here, a new subtree is combined with the\n  // previous subtrees. Threads are used to traverse the inside and outside\n  // contours of the left and right subtree up to the highest common level. The\n  // vertices used for the traversals are vi+, vi-, vo-, and vo+, where the\n  // superscript o means outside and i means inside, the subscript - means left\n  // subtree and + means right subtree. For summing up the modifiers along the\n  // contour, we use respective variables si+, si-, so-, and so+. Whenever two\n  // nodes of the inside contours conflict, we compute the left one of the\n  // greatest uncommon ancestors using the function ANCESTOR and call MOVE\n  // SUBTREE to shift the subtree and prepare the shifts of smaller subtrees.\n  // Finally, we add a new thread (if necessary).\n  function apportion(v, w, ancestor) {\n    if (w) {\n      var vip = v,\n        vop = v,\n        vim = w,\n        vom = vip.parent.children[0],\n        sip = vip.m,\n        sop = vop.m,\n        sim = vim.m,\n        som = vom.m,\n        shift;\n      while (vim = nextRight(vim), vip = nextLeft(vip), vim && vip) {\n        vom = nextLeft(vom);\n        vop = nextRight(vop);\n        vop.a = v;\n        shift = vim.z + sim - vip.z - sip + separation(vim._, vip._);\n        if (shift > 0) {\n          moveSubtree(nextAncestor(vim, v, ancestor), v, shift);\n          sip += shift;\n          sop += shift;\n        }\n        sim += vim.m;\n        sip += vip.m;\n        som += vom.m;\n        sop += vop.m;\n      }\n      if (vim && !nextRight(vop)) {\n        vop.t = vim;\n        vop.m += sim - sop;\n      }\n      if (vip && !nextLeft(vom)) {\n        vom.t = vip;\n        vom.m += sip - som;\n        ancestor = v;\n      }\n    }\n    return ancestor;\n  }\n  function sizeNode(node) {\n    node.x *= dx;\n    node.y = node.depth * dy;\n  }\n  tree.separation = function (x) {\n    return arguments.length ? (separation = x, tree) : separation;\n  };\n  tree.size = function (x) {\n    return arguments.length ? (nodeSize = false, dx = +x[0], dy = +x[1], tree) : nodeSize ? null : [dx, dy];\n  };\n  tree.nodeSize = function (x) {\n    return arguments.length ? (nodeSize = true, dx = +x[0], dy = +x[1], tree) : nodeSize ? [dx, dy] : null;\n  };\n  return tree;\n}", "map": {"version": 3, "names": ["Node", "defaultSeparation", "a", "b", "parent", "nextLeft", "v", "children", "t", "nextRight", "length", "moveSubtree", "wm", "wp", "shift", "change", "i", "c", "s", "z", "m", "executeShifts", "w", "nextAncestor", "vim", "ancestor", "TreeNode", "node", "_", "A", "prototype", "Object", "create", "treeRoot", "root", "tree", "nodes", "child", "n", "pop", "Array", "push", "separation", "dx", "dy", "nodeSize", "eachAfter", "firstWalk", "eachBefore", "secondWalk", "sizeNode", "left", "right", "bottom", "x", "depth", "tx", "kx", "ky", "y", "siblings", "midpoint", "apportion", "vip", "vop", "vom", "sip", "sop", "sim", "som", "arguments", "size"], "sources": ["C:/console/aava-ui-web/node_modules/d3-hierarchy/src/tree.js"], "sourcesContent": ["import {Node} from \"./hierarchy/index.js\";\n\nfunction defaultSeparation(a, b) {\n  return a.parent === b.parent ? 1 : 2;\n}\n\n// function radialSeparation(a, b) {\n//   return (a.parent === b.parent ? 1 : 2) / a.depth;\n// }\n\n// This function is used to traverse the left contour of a subtree (or\n// subforest). It returns the successor of v on this contour. This successor is\n// either given by the leftmost child of v or by the thread of v. The function\n// returns null if and only if v is on the highest level of its subtree.\nfunction nextLeft(v) {\n  var children = v.children;\n  return children ? children[0] : v.t;\n}\n\n// This function works analogously to nextLeft.\nfunction nextRight(v) {\n  var children = v.children;\n  return children ? children[children.length - 1] : v.t;\n}\n\n// Shifts the current subtree rooted at w+. This is done by increasing\n// prelim(w+) and mod(w+) by shift.\nfunction moveSubtree(wm, wp, shift) {\n  var change = shift / (wp.i - wm.i);\n  wp.c -= change;\n  wp.s += shift;\n  wm.c += change;\n  wp.z += shift;\n  wp.m += shift;\n}\n\n// All other shifts, applied to the smaller subtrees between w- and w+, are\n// performed by this function. To prepare the shifts, we have to adjust\n// change(w+), shift(w+), and change(w-).\nfunction executeShifts(v) {\n  var shift = 0,\n      change = 0,\n      children = v.children,\n      i = children.length,\n      w;\n  while (--i >= 0) {\n    w = children[i];\n    w.z += shift;\n    w.m += shift;\n    shift += w.s + (change += w.c);\n  }\n}\n\n// If vi-’s ancestor is a sibling of v, returns vi-’s ancestor. Otherwise,\n// returns the specified (default) ancestor.\nfunction nextAncestor(vim, v, ancestor) {\n  return vim.a.parent === v.parent ? vim.a : ancestor;\n}\n\nfunction TreeNode(node, i) {\n  this._ = node;\n  this.parent = null;\n  this.children = null;\n  this.A = null; // default ancestor\n  this.a = this; // ancestor\n  this.z = 0; // prelim\n  this.m = 0; // mod\n  this.c = 0; // change\n  this.s = 0; // shift\n  this.t = null; // thread\n  this.i = i; // number\n}\n\nTreeNode.prototype = Object.create(Node.prototype);\n\nfunction treeRoot(root) {\n  var tree = new TreeNode(root, 0),\n      node,\n      nodes = [tree],\n      child,\n      children,\n      i,\n      n;\n\n  while (node = nodes.pop()) {\n    if (children = node._.children) {\n      node.children = new Array(n = children.length);\n      for (i = n - 1; i >= 0; --i) {\n        nodes.push(child = node.children[i] = new TreeNode(children[i], i));\n        child.parent = node;\n      }\n    }\n  }\n\n  (tree.parent = new TreeNode(null, 0)).children = [tree];\n  return tree;\n}\n\n// Node-link tree diagram using the Reingold-Tilford \"tidy\" algorithm\nexport default function() {\n  var separation = defaultSeparation,\n      dx = 1,\n      dy = 1,\n      nodeSize = null;\n\n  function tree(root) {\n    var t = treeRoot(root);\n\n    // Compute the layout using Buchheim et al.’s algorithm.\n    t.eachAfter(firstWalk), t.parent.m = -t.z;\n    t.eachBefore(secondWalk);\n\n    // If a fixed node size is specified, scale x and y.\n    if (nodeSize) root.eachBefore(sizeNode);\n\n    // If a fixed tree size is specified, scale x and y based on the extent.\n    // Compute the left-most, right-most, and depth-most nodes for extents.\n    else {\n      var left = root,\n          right = root,\n          bottom = root;\n      root.eachBefore(function(node) {\n        if (node.x < left.x) left = node;\n        if (node.x > right.x) right = node;\n        if (node.depth > bottom.depth) bottom = node;\n      });\n      var s = left === right ? 1 : separation(left, right) / 2,\n          tx = s - left.x,\n          kx = dx / (right.x + s + tx),\n          ky = dy / (bottom.depth || 1);\n      root.eachBefore(function(node) {\n        node.x = (node.x + tx) * kx;\n        node.y = node.depth * ky;\n      });\n    }\n\n    return root;\n  }\n\n  // Computes a preliminary x-coordinate for v. Before that, FIRST WALK is\n  // applied recursively to the children of v, as well as the function\n  // APPORTION. After spacing out the children by calling EXECUTE SHIFTS, the\n  // node v is placed to the midpoint of its outermost children.\n  function firstWalk(v) {\n    var children = v.children,\n        siblings = v.parent.children,\n        w = v.i ? siblings[v.i - 1] : null;\n    if (children) {\n      executeShifts(v);\n      var midpoint = (children[0].z + children[children.length - 1].z) / 2;\n      if (w) {\n        v.z = w.z + separation(v._, w._);\n        v.m = v.z - midpoint;\n      } else {\n        v.z = midpoint;\n      }\n    } else if (w) {\n      v.z = w.z + separation(v._, w._);\n    }\n    v.parent.A = apportion(v, w, v.parent.A || siblings[0]);\n  }\n\n  // Computes all real x-coordinates by summing up the modifiers recursively.\n  function secondWalk(v) {\n    v._.x = v.z + v.parent.m;\n    v.m += v.parent.m;\n  }\n\n  // The core of the algorithm. Here, a new subtree is combined with the\n  // previous subtrees. Threads are used to traverse the inside and outside\n  // contours of the left and right subtree up to the highest common level. The\n  // vertices used for the traversals are vi+, vi-, vo-, and vo+, where the\n  // superscript o means outside and i means inside, the subscript - means left\n  // subtree and + means right subtree. For summing up the modifiers along the\n  // contour, we use respective variables si+, si-, so-, and so+. Whenever two\n  // nodes of the inside contours conflict, we compute the left one of the\n  // greatest uncommon ancestors using the function ANCESTOR and call MOVE\n  // SUBTREE to shift the subtree and prepare the shifts of smaller subtrees.\n  // Finally, we add a new thread (if necessary).\n  function apportion(v, w, ancestor) {\n    if (w) {\n      var vip = v,\n          vop = v,\n          vim = w,\n          vom = vip.parent.children[0],\n          sip = vip.m,\n          sop = vop.m,\n          sim = vim.m,\n          som = vom.m,\n          shift;\n      while (vim = nextRight(vim), vip = nextLeft(vip), vim && vip) {\n        vom = nextLeft(vom);\n        vop = nextRight(vop);\n        vop.a = v;\n        shift = vim.z + sim - vip.z - sip + separation(vim._, vip._);\n        if (shift > 0) {\n          moveSubtree(nextAncestor(vim, v, ancestor), v, shift);\n          sip += shift;\n          sop += shift;\n        }\n        sim += vim.m;\n        sip += vip.m;\n        som += vom.m;\n        sop += vop.m;\n      }\n      if (vim && !nextRight(vop)) {\n        vop.t = vim;\n        vop.m += sim - sop;\n      }\n      if (vip && !nextLeft(vom)) {\n        vom.t = vip;\n        vom.m += sip - som;\n        ancestor = v;\n      }\n    }\n    return ancestor;\n  }\n\n  function sizeNode(node) {\n    node.x *= dx;\n    node.y = node.depth * dy;\n  }\n\n  tree.separation = function(x) {\n    return arguments.length ? (separation = x, tree) : separation;\n  };\n\n  tree.size = function(x) {\n    return arguments.length ? (nodeSize = false, dx = +x[0], dy = +x[1], tree) : (nodeSize ? null : [dx, dy]);\n  };\n\n  tree.nodeSize = function(x) {\n    return arguments.length ? (nodeSize = true, dx = +x[0], dy = +x[1], tree) : (nodeSize ? [dx, dy] : null);\n  };\n\n  return tree;\n}\n"], "mappings": "AAAA,SAAQA,IAAI,QAAO,sBAAsB;AAEzC,SAASC,iBAAiBA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC/B,OAAOD,CAAC,CAACE,MAAM,KAAKD,CAAC,CAACC,MAAM,GAAG,CAAC,GAAG,CAAC;AACtC;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,CAAC,EAAE;EACnB,IAAIC,QAAQ,GAAGD,CAAC,CAACC,QAAQ;EACzB,OAAOA,QAAQ,GAAGA,QAAQ,CAAC,CAAC,CAAC,GAAGD,CAAC,CAACE,CAAC;AACrC;;AAEA;AACA,SAASC,SAASA,CAACH,CAAC,EAAE;EACpB,IAAIC,QAAQ,GAAGD,CAAC,CAACC,QAAQ;EACzB,OAAOA,QAAQ,GAAGA,QAAQ,CAACA,QAAQ,CAACG,MAAM,GAAG,CAAC,CAAC,GAAGJ,CAAC,CAACE,CAAC;AACvD;;AAEA;AACA;AACA,SAASG,WAAWA,CAACC,EAAE,EAAEC,EAAE,EAAEC,KAAK,EAAE;EAClC,IAAIC,MAAM,GAAGD,KAAK,IAAID,EAAE,CAACG,CAAC,GAAGJ,EAAE,CAACI,CAAC,CAAC;EAClCH,EAAE,CAACI,CAAC,IAAIF,MAAM;EACdF,EAAE,CAACK,CAAC,IAAIJ,KAAK;EACbF,EAAE,CAACK,CAAC,IAAIF,MAAM;EACdF,EAAE,CAACM,CAAC,IAAIL,KAAK;EACbD,EAAE,CAACO,CAAC,IAAIN,KAAK;AACf;;AAEA;AACA;AACA;AACA,SAASO,aAAaA,CAACf,CAAC,EAAE;EACxB,IAAIQ,KAAK,GAAG,CAAC;IACTC,MAAM,GAAG,CAAC;IACVR,QAAQ,GAAGD,CAAC,CAACC,QAAQ;IACrBS,CAAC,GAAGT,QAAQ,CAACG,MAAM;IACnBY,CAAC;EACL,OAAO,EAAEN,CAAC,IAAI,CAAC,EAAE;IACfM,CAAC,GAAGf,QAAQ,CAACS,CAAC,CAAC;IACfM,CAAC,CAACH,CAAC,IAAIL,KAAK;IACZQ,CAAC,CAACF,CAAC,IAAIN,KAAK;IACZA,KAAK,IAAIQ,CAAC,CAACJ,CAAC,IAAIH,MAAM,IAAIO,CAAC,CAACL,CAAC,CAAC;EAChC;AACF;;AAEA;AACA;AACA,SAASM,YAAYA,CAACC,GAAG,EAAElB,CAAC,EAAEmB,QAAQ,EAAE;EACtC,OAAOD,GAAG,CAACtB,CAAC,CAACE,MAAM,KAAKE,CAAC,CAACF,MAAM,GAAGoB,GAAG,CAACtB,CAAC,GAAGuB,QAAQ;AACrD;AAEA,SAASC,QAAQA,CAACC,IAAI,EAAEX,CAAC,EAAE;EACzB,IAAI,CAACY,CAAC,GAAGD,IAAI;EACb,IAAI,CAACvB,MAAM,GAAG,IAAI;EAClB,IAAI,CAACG,QAAQ,GAAG,IAAI;EACpB,IAAI,CAACsB,CAAC,GAAG,IAAI,CAAC,CAAC;EACf,IAAI,CAAC3B,CAAC,GAAG,IAAI,CAAC,CAAC;EACf,IAAI,CAACiB,CAAC,GAAG,CAAC,CAAC,CAAC;EACZ,IAAI,CAACC,CAAC,GAAG,CAAC,CAAC,CAAC;EACZ,IAAI,CAACH,CAAC,GAAG,CAAC,CAAC,CAAC;EACZ,IAAI,CAACC,CAAC,GAAG,CAAC,CAAC,CAAC;EACZ,IAAI,CAACV,CAAC,GAAG,IAAI,CAAC,CAAC;EACf,IAAI,CAACQ,CAAC,GAAGA,CAAC,CAAC,CAAC;AACd;AAEAU,QAAQ,CAACI,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAChC,IAAI,CAAC8B,SAAS,CAAC;AAElD,SAASG,QAAQA,CAACC,IAAI,EAAE;EACtB,IAAIC,IAAI,GAAG,IAAIT,QAAQ,CAACQ,IAAI,EAAE,CAAC,CAAC;IAC5BP,IAAI;IACJS,KAAK,GAAG,CAACD,IAAI,CAAC;IACdE,KAAK;IACL9B,QAAQ;IACRS,CAAC;IACDsB,CAAC;EAEL,OAAOX,IAAI,GAAGS,KAAK,CAACG,GAAG,CAAC,CAAC,EAAE;IACzB,IAAIhC,QAAQ,GAAGoB,IAAI,CAACC,CAAC,CAACrB,QAAQ,EAAE;MAC9BoB,IAAI,CAACpB,QAAQ,GAAG,IAAIiC,KAAK,CAACF,CAAC,GAAG/B,QAAQ,CAACG,MAAM,CAAC;MAC9C,KAAKM,CAAC,GAAGsB,CAAC,GAAG,CAAC,EAAEtB,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;QAC3BoB,KAAK,CAACK,IAAI,CAACJ,KAAK,GAAGV,IAAI,CAACpB,QAAQ,CAACS,CAAC,CAAC,GAAG,IAAIU,QAAQ,CAACnB,QAAQ,CAACS,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC;QACnEqB,KAAK,CAACjC,MAAM,GAAGuB,IAAI;MACrB;IACF;EACF;EAEA,CAACQ,IAAI,CAAC/B,MAAM,GAAG,IAAIsB,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,EAAEnB,QAAQ,GAAG,CAAC4B,IAAI,CAAC;EACvD,OAAOA,IAAI;AACb;;AAEA;AACA,eAAe,YAAW;EACxB,IAAIO,UAAU,GAAGzC,iBAAiB;IAC9B0C,EAAE,GAAG,CAAC;IACNC,EAAE,GAAG,CAAC;IACNC,QAAQ,GAAG,IAAI;EAEnB,SAASV,IAAIA,CAACD,IAAI,EAAE;IAClB,IAAI1B,CAAC,GAAGyB,QAAQ,CAACC,IAAI,CAAC;;IAEtB;IACA1B,CAAC,CAACsC,SAAS,CAACC,SAAS,CAAC,EAAEvC,CAAC,CAACJ,MAAM,CAACgB,CAAC,GAAG,CAACZ,CAAC,CAACW,CAAC;IACzCX,CAAC,CAACwC,UAAU,CAACC,UAAU,CAAC;;IAExB;IACA,IAAIJ,QAAQ,EAAEX,IAAI,CAACc,UAAU,CAACE,QAAQ,CAAC;;IAEvC;IACA;IAAA,KACK;MACH,IAAIC,IAAI,GAAGjB,IAAI;QACXkB,KAAK,GAAGlB,IAAI;QACZmB,MAAM,GAAGnB,IAAI;MACjBA,IAAI,CAACc,UAAU,CAAC,UAASrB,IAAI,EAAE;QAC7B,IAAIA,IAAI,CAAC2B,CAAC,GAAGH,IAAI,CAACG,CAAC,EAAEH,IAAI,GAAGxB,IAAI;QAChC,IAAIA,IAAI,CAAC2B,CAAC,GAAGF,KAAK,CAACE,CAAC,EAAEF,KAAK,GAAGzB,IAAI;QAClC,IAAIA,IAAI,CAAC4B,KAAK,GAAGF,MAAM,CAACE,KAAK,EAAEF,MAAM,GAAG1B,IAAI;MAC9C,CAAC,CAAC;MACF,IAAIT,CAAC,GAAGiC,IAAI,KAAKC,KAAK,GAAG,CAAC,GAAGV,UAAU,CAACS,IAAI,EAAEC,KAAK,CAAC,GAAG,CAAC;QACpDI,EAAE,GAAGtC,CAAC,GAAGiC,IAAI,CAACG,CAAC;QACfG,EAAE,GAAGd,EAAE,IAAIS,KAAK,CAACE,CAAC,GAAGpC,CAAC,GAAGsC,EAAE,CAAC;QAC5BE,EAAE,GAAGd,EAAE,IAAIS,MAAM,CAACE,KAAK,IAAI,CAAC,CAAC;MACjCrB,IAAI,CAACc,UAAU,CAAC,UAASrB,IAAI,EAAE;QAC7BA,IAAI,CAAC2B,CAAC,GAAG,CAAC3B,IAAI,CAAC2B,CAAC,GAAGE,EAAE,IAAIC,EAAE;QAC3B9B,IAAI,CAACgC,CAAC,GAAGhC,IAAI,CAAC4B,KAAK,GAAGG,EAAE;MAC1B,CAAC,CAAC;IACJ;IAEA,OAAOxB,IAAI;EACb;;EAEA;EACA;EACA;EACA;EACA,SAASa,SAASA,CAACzC,CAAC,EAAE;IACpB,IAAIC,QAAQ,GAAGD,CAAC,CAACC,QAAQ;MACrBqD,QAAQ,GAAGtD,CAAC,CAACF,MAAM,CAACG,QAAQ;MAC5Be,CAAC,GAAGhB,CAAC,CAACU,CAAC,GAAG4C,QAAQ,CAACtD,CAAC,CAACU,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI;IACtC,IAAIT,QAAQ,EAAE;MACZc,aAAa,CAACf,CAAC,CAAC;MAChB,IAAIuD,QAAQ,GAAG,CAACtD,QAAQ,CAAC,CAAC,CAAC,CAACY,CAAC,GAAGZ,QAAQ,CAACA,QAAQ,CAACG,MAAM,GAAG,CAAC,CAAC,CAACS,CAAC,IAAI,CAAC;MACpE,IAAIG,CAAC,EAAE;QACLhB,CAAC,CAACa,CAAC,GAAGG,CAAC,CAACH,CAAC,GAAGuB,UAAU,CAACpC,CAAC,CAACsB,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC;QAChCtB,CAAC,CAACc,CAAC,GAAGd,CAAC,CAACa,CAAC,GAAG0C,QAAQ;MACtB,CAAC,MAAM;QACLvD,CAAC,CAACa,CAAC,GAAG0C,QAAQ;MAChB;IACF,CAAC,MAAM,IAAIvC,CAAC,EAAE;MACZhB,CAAC,CAACa,CAAC,GAAGG,CAAC,CAACH,CAAC,GAAGuB,UAAU,CAACpC,CAAC,CAACsB,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC;IAClC;IACAtB,CAAC,CAACF,MAAM,CAACyB,CAAC,GAAGiC,SAAS,CAACxD,CAAC,EAAEgB,CAAC,EAAEhB,CAAC,CAACF,MAAM,CAACyB,CAAC,IAAI+B,QAAQ,CAAC,CAAC,CAAC,CAAC;EACzD;;EAEA;EACA,SAASX,UAAUA,CAAC3C,CAAC,EAAE;IACrBA,CAAC,CAACsB,CAAC,CAAC0B,CAAC,GAAGhD,CAAC,CAACa,CAAC,GAAGb,CAAC,CAACF,MAAM,CAACgB,CAAC;IACxBd,CAAC,CAACc,CAAC,IAAId,CAAC,CAACF,MAAM,CAACgB,CAAC;EACnB;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS0C,SAASA,CAACxD,CAAC,EAAEgB,CAAC,EAAEG,QAAQ,EAAE;IACjC,IAAIH,CAAC,EAAE;MACL,IAAIyC,GAAG,GAAGzD,CAAC;QACP0D,GAAG,GAAG1D,CAAC;QACPkB,GAAG,GAAGF,CAAC;QACP2C,GAAG,GAAGF,GAAG,CAAC3D,MAAM,CAACG,QAAQ,CAAC,CAAC,CAAC;QAC5B2D,GAAG,GAAGH,GAAG,CAAC3C,CAAC;QACX+C,GAAG,GAAGH,GAAG,CAAC5C,CAAC;QACXgD,GAAG,GAAG5C,GAAG,CAACJ,CAAC;QACXiD,GAAG,GAAGJ,GAAG,CAAC7C,CAAC;QACXN,KAAK;MACT,OAAOU,GAAG,GAAGf,SAAS,CAACe,GAAG,CAAC,EAAEuC,GAAG,GAAG1D,QAAQ,CAAC0D,GAAG,CAAC,EAAEvC,GAAG,IAAIuC,GAAG,EAAE;QAC5DE,GAAG,GAAG5D,QAAQ,CAAC4D,GAAG,CAAC;QACnBD,GAAG,GAAGvD,SAAS,CAACuD,GAAG,CAAC;QACpBA,GAAG,CAAC9D,CAAC,GAAGI,CAAC;QACTQ,KAAK,GAAGU,GAAG,CAACL,CAAC,GAAGiD,GAAG,GAAGL,GAAG,CAAC5C,CAAC,GAAG+C,GAAG,GAAGxB,UAAU,CAAClB,GAAG,CAACI,CAAC,EAAEmC,GAAG,CAACnC,CAAC,CAAC;QAC5D,IAAId,KAAK,GAAG,CAAC,EAAE;UACbH,WAAW,CAACY,YAAY,CAACC,GAAG,EAAElB,CAAC,EAAEmB,QAAQ,CAAC,EAAEnB,CAAC,EAAEQ,KAAK,CAAC;UACrDoD,GAAG,IAAIpD,KAAK;UACZqD,GAAG,IAAIrD,KAAK;QACd;QACAsD,GAAG,IAAI5C,GAAG,CAACJ,CAAC;QACZ8C,GAAG,IAAIH,GAAG,CAAC3C,CAAC;QACZiD,GAAG,IAAIJ,GAAG,CAAC7C,CAAC;QACZ+C,GAAG,IAAIH,GAAG,CAAC5C,CAAC;MACd;MACA,IAAII,GAAG,IAAI,CAACf,SAAS,CAACuD,GAAG,CAAC,EAAE;QAC1BA,GAAG,CAACxD,CAAC,GAAGgB,GAAG;QACXwC,GAAG,CAAC5C,CAAC,IAAIgD,GAAG,GAAGD,GAAG;MACpB;MACA,IAAIJ,GAAG,IAAI,CAAC1D,QAAQ,CAAC4D,GAAG,CAAC,EAAE;QACzBA,GAAG,CAACzD,CAAC,GAAGuD,GAAG;QACXE,GAAG,CAAC7C,CAAC,IAAI8C,GAAG,GAAGG,GAAG;QAClB5C,QAAQ,GAAGnB,CAAC;MACd;IACF;IACA,OAAOmB,QAAQ;EACjB;EAEA,SAASyB,QAAQA,CAACvB,IAAI,EAAE;IACtBA,IAAI,CAAC2B,CAAC,IAAIX,EAAE;IACZhB,IAAI,CAACgC,CAAC,GAAGhC,IAAI,CAAC4B,KAAK,GAAGX,EAAE;EAC1B;EAEAT,IAAI,CAACO,UAAU,GAAG,UAASY,CAAC,EAAE;IAC5B,OAAOgB,SAAS,CAAC5D,MAAM,IAAIgC,UAAU,GAAGY,CAAC,EAAEnB,IAAI,IAAIO,UAAU;EAC/D,CAAC;EAEDP,IAAI,CAACoC,IAAI,GAAG,UAASjB,CAAC,EAAE;IACtB,OAAOgB,SAAS,CAAC5D,MAAM,IAAImC,QAAQ,GAAG,KAAK,EAAEF,EAAE,GAAG,CAACW,CAAC,CAAC,CAAC,CAAC,EAAEV,EAAE,GAAG,CAACU,CAAC,CAAC,CAAC,CAAC,EAAEnB,IAAI,IAAKU,QAAQ,GAAG,IAAI,GAAG,CAACF,EAAE,EAAEC,EAAE,CAAE;EAC3G,CAAC;EAEDT,IAAI,CAACU,QAAQ,GAAG,UAASS,CAAC,EAAE;IAC1B,OAAOgB,SAAS,CAAC5D,MAAM,IAAImC,QAAQ,GAAG,IAAI,EAAEF,EAAE,GAAG,CAACW,CAAC,CAAC,CAAC,CAAC,EAAEV,EAAE,GAAG,CAACU,CAAC,CAAC,CAAC,CAAC,EAAEnB,IAAI,IAAKU,QAAQ,GAAG,CAACF,EAAE,EAAEC,EAAE,CAAC,GAAG,IAAK;EAC1G,CAAC;EAED,OAAOT,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}