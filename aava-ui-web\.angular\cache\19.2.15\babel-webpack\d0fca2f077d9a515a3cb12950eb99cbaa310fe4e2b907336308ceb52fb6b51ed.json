{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IconComponent } from '@ava/play-comp-library';\nimport { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/search.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nfunction SearchBar_div_5_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const text_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(text_r1);\n  }\n}\nfunction SearchBar_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9);\n    i0.ɵɵtemplate(2, SearchBar_div_5_span_2_Template, 2, 1, \"span\", 10);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.placeholderTexts);\n  }\n}\nlet SearchBar = /*#__PURE__*/(() => {\n  class SearchBar {\n    searchService;\n    placeholderTexts = ['What you want to do today', 'How can I make your day productive?'];\n    searchValue = '';\n    searchResults = [];\n    isSearching = false;\n    sendClicked = new EventEmitter();\n    searchResultsChange = new EventEmitter();\n    searchQueryChange = new EventEmitter();\n    searchLoadingChange = new EventEmitter();\n    destroy$ = new Subject();\n    constructor(searchService) {\n      this.searchService = searchService;\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    onEnterPressed() {\n      if (this.searchValue.trim()) {\n        this.searchQueryChange.emit(this.searchValue.trim());\n        this.performSearch(this.searchValue.trim());\n        this.sendClicked.emit(this.searchValue.trim());\n      }\n    }\n    onSend() {\n      if (this.searchValue.trim()) {\n        this.searchQueryChange.emit(this.searchValue.trim());\n        this.performSearch(this.searchValue.trim());\n        this.sendClicked.emit(this.searchValue.trim());\n      }\n    }\n    /**\n     * Perform search using the unified search API\n     */\n    // private performSearch(query: string) {\n    //   if (!query.trim()) {\n    //     this.searchResults = [];\n    //     this.searchResultsChange.emit([]);\n    //     return;\n    //   }\n    //   this.searchService.searchAgents(query, 10).subscribe({\n    //     next: (results: EntityResult[]) => {\n    //       this.searchResults = results;\n    //       this.searchResultsChange.emit(results);\n    //     },\n    //     error: (error: any) => {\n    //       this.searchResults = [];\n    //       this.searchResultsChange.emit([]);\n    //     }\n    //   });\n    // }\n    /**\n     * Perform search using the Revelio search API\n     */\n    performSearch(query) {\n      if (!query.trim()) {\n        this.searchResults = [];\n        this.searchResultsChange.emit([]);\n        this.isSearching = false;\n        this.searchLoadingChange.emit(false);\n        return;\n      }\n      this.isSearching = true;\n      this.searchLoadingChange.emit(true);\n      this.searchService.revelioSearch({\n        query: query,\n        limit: 10,\n        threshold: 0\n      }).subscribe({\n        next: response => {\n          // Extract results from the Revelio response\n          const results = response.results || [];\n          console.log('Revelio search results:', results);\n          this.searchResults = results;\n          this.searchResultsChange.emit(results);\n          this.isSearching = false;\n          this.searchLoadingChange.emit(false);\n        },\n        error: error => {\n          console.error('Revelio search error:', error);\n          this.searchResults = [];\n          this.searchResultsChange.emit([]);\n          this.isSearching = false;\n          this.searchLoadingChange.emit(false);\n        }\n      });\n    }\n    /**\n     * Clear search\n     */\n    clearSearch() {\n      this.searchValue = '';\n      this.searchResults = [];\n      this.searchResultsChange.emit([]);\n      this.searchQueryChange.emit('');\n      this.isSearching = false;\n      this.searchLoadingChange.emit(false);\n    }\n    static ɵfac = function SearchBar_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SearchBar)(i0.ɵɵdirectiveInject(i1.SearchService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SearchBar,\n      selectors: [[\"app-search-bar\"]],\n      outputs: {\n        sendClicked: \"sendClicked\",\n        searchResultsChange: \"searchResultsChange\",\n        searchQueryChange: \"searchQueryChange\",\n        searchLoadingChange: \"searchLoadingChange\"\n      },\n      decls: 8,\n      vars: 2,\n      consts: [[1, \"search-container\"], [1, \"search-wrapper\"], [1, \"search-icon\"], [\"iconName\", \"search\", \"iconSize\", \"20px\", \"iconColor\", \"#666D99\", 1, \"search-svg-icon\"], [\"type\", \"text\", \"aria-label\", \"Search\", 1, \"search-input\", 3, \"ngModelChange\", \"keydown.enter\", \"ngModel\"], [\"class\", \"animated-placeholder-wrapper\", 4, \"ngIf\"], [1, \"send-button\", 3, \"click\"], [\"iconName\", \"send-horizontal\", \"iconSize\", \"24px\", \"iconColor\", \"black\"], [1, \"animated-placeholder-wrapper\"], [1, \"animated-placeholder\"], [4, \"ngFor\", \"ngForOf\"]],\n      template: function SearchBar_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"ava-icon\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"input\", 4);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SearchBar_Template_input_ngModelChange_4_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchValue, $event) || (ctx.searchValue = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"keydown.enter\", function SearchBar_Template_input_keydown_enter_4_listener() {\n            return ctx.onEnterPressed();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(5, SearchBar_div_5_Template, 3, 1, \"div\", 5);\n          i0.ɵɵelementStart(6, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function SearchBar_Template_button_click_6_listener() {\n            return ctx.onSend();\n          });\n          i0.ɵɵelement(7, \"ava-icon\", 7);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchValue);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.searchValue);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, FormsModule, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, IconComponent],\n      styles: [\".search-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  width: 100%;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: flex-start;\\n  gap: 10px;\\n  border-radius: 16px;\\n  box-sizing: border-box;\\n  margin: 0 auto;\\n  position: relative;\\n  z-index: 2;\\n  overflow: visible;\\n}\\n\\n.search-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  display: flex;\\n  align-items: center;\\n  z-index: 2;\\n}\\n\\n.search-icon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 16px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 1;\\n}\\n\\n.search-svg-icon[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  display: block;\\n}\\n\\n.search-input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 16px 60px 16px 48px;\\n  border-radius: 16px;\\n  background-color: #fff;\\n  color: #666D99;\\n  font-family: Mulish;\\n  font-size: 24px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: normal;\\n  outline: none;\\n  border: double 1px transparent;\\n  background-image: linear-gradient(white, white), linear-gradient(180deg, var(--Brand-Primary-300, #F06896) 0%, var(--Brand-Tertiary-300, #997BCF) 100%);\\n  background-origin: border-box;\\n  background-clip: padding-box, border-box;\\n  transition: all 0.3s ease;\\n}\\n.search-input[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.7;\\n  cursor: not-allowed;\\n}\\n.search-input[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 3px rgba(240, 104, 150, 0.1);\\n}\\n\\n.animated-placeholder-wrapper[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 48px;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  overflow: hidden;\\n  height: 24px;\\n  display: flex;\\n  align-items: center;\\n  pointer-events: none;\\n  width: calc(100% - 48px - 16px);\\n}\\n\\n.animated-placeholder[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  animation: _ngcontent-%COMP%_slide-up-down 10s ease-in-out infinite;\\n  line-height: 1.2;\\n}\\n\\n.animated-placeholder[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #666D99;\\n  font-family: \\\"Mulish\\\", -apple-system, \\\"Roboto\\\", \\\"Helvetica\\\", sans-serif;\\n  font-size: 20px;\\n  font-weight: 400;\\n  white-space: nowrap;\\n  height: 100%;\\n  display: flex;\\n  align-items: center;\\n  flex-shrink: 0;\\n  flex-grow: 0;\\n}\\n\\n.loading-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 80px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 3;\\n}\\n.loading-indicator[_ngcontent-%COMP%]   .spinning[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n.clear-button[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 80px;\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  transition: all 0.3s ease;\\n  z-index: 3;\\n}\\n.clear-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(102, 109, 153, 0.1);\\n}\\n.clear-button[_ngcontent-%COMP%]:active {\\n  transform: scale(0.95);\\n}\\n\\n.send-button[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 16px;\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  transition: all 0.3s ease;\\n  z-index: 3;\\n}\\n.send-button[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: rgba(0, 0, 0, 0.1);\\n}\\n.send-button[_ngcontent-%COMP%]:active:not(:disabled) {\\n  transform: scale(0.95);\\n}\\n.send-button[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slide-up-down {\\n  0%, 45% {\\n    transform: translateY(0);\\n  }\\n  50%, 95% {\\n    transform: translateY(-24px);\\n  }\\n  100% {\\n    transform: translateY(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_spin {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n\\n@media (max-width: 1200px) {\\n  .search-container[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .search-wrapper[_ngcontent-%COMP%] {\\n    max-width: 100%;\\n  }\\n  .search-container[_ngcontent-%COMP%] {\\n    height: auto;\\n  }\\n  .search-input[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n  .animated-placeholder[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n  .animated-placeholder-wrapper[_ngcontent-%COMP%] {\\n    height: 22px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return SearchBar;\n})();\nexport { SearchBar as default };", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "IconComponent", "Subject", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "text_r1", "ɵɵtemplate", "SearchBar_div_5_span_2_Template", "ɵɵproperty", "ctx_r1", "placeholderTexts", "SearchBar", "searchService", "searchValue", "searchResults", "isSearching", "sendClicked", "searchResultsChange", "searchQueryChange", "searchLoadingChange", "destroy$", "constructor", "ngOnDestroy", "next", "complete", "onEnterPressed", "trim", "emit", "performSearch", "onSend", "query", "revelioSearch", "limit", "threshold", "subscribe", "response", "results", "console", "log", "error", "clearSearch", "ɵɵdirectiveInject", "i1", "SearchService", "selectors", "outputs", "decls", "vars", "consts", "template", "SearchBar_Template", "rf", "ctx", "ɵɵelement", "ɵɵtwoWayListener", "SearchBar_Template_input_ngModelChange_4_listener", "$event", "ɵɵtwoWayBindingSet", "ɵɵlistener", "SearchBar_Template_input_keydown_enter_4_listener", "SearchBar_div_5_Template", "SearchBar_Template_button_click_6_listener", "ɵɵtwoWayProperty", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i3", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles", "default"], "sources": ["C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\shared\\components\\search-bar\\search-bar.component.ts", "C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\shared\\components\\search-bar\\search-bar.component.html"], "sourcesContent": ["import { Component, Output, EventEmitter, OnDestroy } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { IconComponent } from '@ava/play-comp-library';\r\nimport {\r\n  SearchService,\r\n  RevelioSearchResponse,\r\n  RevelioSearchResult,\r\n} from '../../services/search.service';\r\nimport { EntityResult } from '../../interfaces/agent-list.interface';\r\nimport { Subject } from 'rxjs';\r\n\r\n@Component({\r\n  selector: 'app-search-bar',\r\n  templateUrl: './search-bar.component.html',\r\n  styleUrls: ['./search-bar.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule, IconComponent],\r\n})\r\nexport default class SearchBar implements OnDestroy {\r\n  placeholderTexts: string[] = [\r\n    'What you want to do today',\r\n    'How can I make your day productive?',\r\n  ];\r\n  searchValue: string = '';\r\n  searchResults: RevelioSearchResult[] = [];\r\n  isSearching: boolean = false;\r\n\r\n  @Output() sendClicked = new EventEmitter<string>();\r\n  @Output() searchResultsChange = new EventEmitter<RevelioSearchResult[]>();\r\n  @Output() searchQueryChange = new EventEmitter<string>();\r\n  @Output() searchLoadingChange = new EventEmitter<boolean>();\r\n\r\n  private destroy$ = new Subject<void>();\r\n\r\n  constructor(private searchService: SearchService) {}\r\n\r\n  ngOnDestroy() {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n  }\r\n\r\n  onEnterPressed() {\r\n    if (this.searchValue.trim()) {\r\n      this.searchQueryChange.emit(this.searchValue.trim());\r\n      this.performSearch(this.searchValue.trim());\r\n      this.sendClicked.emit(this.searchValue.trim());\r\n    }\r\n  }\r\n\r\n  onSend() {\r\n    if (this.searchValue.trim()) {\r\n      this.searchQueryChange.emit(this.searchValue.trim());\r\n      this.performSearch(this.searchValue.trim());\r\n      this.sendClicked.emit(this.searchValue.trim());\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Perform search using the unified search API\r\n   */\r\n  // private performSearch(query: string) {\r\n  //   if (!query.trim()) {\r\n  //     this.searchResults = [];\r\n  //     this.searchResultsChange.emit([]);\r\n  //     return;\r\n  //   }\r\n  //   this.searchService.searchAgents(query, 10).subscribe({\r\n  //     next: (results: EntityResult[]) => {\r\n  //       this.searchResults = results;\r\n  //       this.searchResultsChange.emit(results);\r\n  //     },\r\n  //     error: (error: any) => {\r\n  //       this.searchResults = [];\r\n  //       this.searchResultsChange.emit([]);\r\n  //     }\r\n  //   });\r\n  // }\r\n\r\n  /**\r\n   * Perform search using the Revelio search API\r\n   */\r\n  private performSearch(query: string) {\r\n    if (!query.trim()) {\r\n      this.searchResults = [];\r\n      this.searchResultsChange.emit([]);\r\n      this.isSearching = false;\r\n      this.searchLoadingChange.emit(false);\r\n      return;\r\n    }\r\n\r\n    this.isSearching = true;\r\n    this.searchLoadingChange.emit(true);\r\n\r\n    this.searchService\r\n      .revelioSearch({\r\n        query: query,\r\n        limit: 10,\r\n        threshold: 0,\r\n      })\r\n      .subscribe({\r\n        next: (response: RevelioSearchResponse) => {\r\n          // Extract results from the Revelio response\r\n          const results: RevelioSearchResult[] = response.results || [];\r\n          console.log('Revelio search results:', results);\r\n          this.searchResults = results;\r\n          this.searchResultsChange.emit(results);\r\n          this.isSearching = false;\r\n          this.searchLoadingChange.emit(false);\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Revelio search error:', error);\r\n          this.searchResults = [];\r\n          this.searchResultsChange.emit([]);\r\n          this.isSearching = false;\r\n          this.searchLoadingChange.emit(false);\r\n        },\r\n      });\r\n  }\r\n\r\n  /**\r\n   * Clear search\r\n   */\r\n  clearSearch() {\r\n    this.searchValue = '';\r\n    this.searchResults = [];\r\n    this.searchResultsChange.emit([]);\r\n    this.searchQueryChange.emit('');\r\n    this.isSearching = false;\r\n    this.searchLoadingChange.emit(false);\r\n  }\r\n}\r\n", "<div class=\"search-container\">\r\n  <div class=\"search-wrapper\">\r\n    <div class=\"search-icon\">\r\n      <ava-icon iconName=\"search\" iconSize=\"20px\" iconColor=\"#666D99\" class=\"search-svg-icon\"></ava-icon>\r\n    </div>\r\n\r\n    <input\r\n      type=\"text\"\r\n      class=\"search-input\"\r\n      aria-label=\"Search\"\r\n      [(ngModel)]=\"searchValue\"\r\n      (keydown.enter)=\"onEnterPressed()\">\r\n    \r\n    <div class=\"animated-placeholder-wrapper\" *ngIf=\"!searchValue\">\r\n      <div class=\"animated-placeholder\">\r\n        <span *ngFor=\"let text of placeholderTexts\">{{ text }}</span>\r\n      </div>\r\n    </div>\r\n    <!-- Send Button -->\r\n    <button class=\"send-button\" (click)=\"onSend()\">\r\n      <ava-icon iconName=\"send-horizontal\" iconSize=\"24px\" iconColor=\"black\" ></ava-icon>\r\n    </button>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAAA,SAA4BA,YAAY,QAAmB,eAAe;AAC1E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,aAAa,QAAQ,wBAAwB;AAOtD,SAASC,OAAO,QAAQ,MAAM;;;;;;;ICKtBC,EAAA,CAAAC,cAAA,WAA4C;IAAAD,EAAA,CAAAE,MAAA,GAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAjBH,EAAA,CAAAI,SAAA,EAAU;IAAVJ,EAAA,CAAAK,iBAAA,CAAAC,OAAA,CAAU;;;;;IADxDN,EADF,CAAAC,cAAA,aAA+D,aAC3B;IAChCD,EAAA,CAAAO,UAAA,IAAAC,+BAAA,mBAA4C;IAEhDR,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAFqBH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAS,UAAA,YAAAC,MAAA,CAAAC,gBAAA,CAAmB;;;IDI7BC,SAAS;EAAhB,MAAOA,SAAS;IAgBRC,aAAA;IAfpBF,gBAAgB,GAAa,CAC3B,2BAA2B,EAC3B,qCAAqC,CACtC;IACDG,WAAW,GAAW,EAAE;IACxBC,aAAa,GAA0B,EAAE;IACzCC,WAAW,GAAY,KAAK;IAElBC,WAAW,GAAG,IAAItB,YAAY,EAAU;IACxCuB,mBAAmB,GAAG,IAAIvB,YAAY,EAAyB;IAC/DwB,iBAAiB,GAAG,IAAIxB,YAAY,EAAU;IAC9CyB,mBAAmB,GAAG,IAAIzB,YAAY,EAAW;IAEnD0B,QAAQ,GAAG,IAAItB,OAAO,EAAQ;IAEtCuB,YAAoBT,aAA4B;MAA5B,KAAAA,aAAa,GAAbA,aAAa;IAAkB;IAEnDU,WAAWA,CAAA;MACT,IAAI,CAACF,QAAQ,CAACG,IAAI,EAAE;MACpB,IAAI,CAACH,QAAQ,CAACI,QAAQ,EAAE;IAC1B;IAEAC,cAAcA,CAAA;MACZ,IAAI,IAAI,CAACZ,WAAW,CAACa,IAAI,EAAE,EAAE;QAC3B,IAAI,CAACR,iBAAiB,CAACS,IAAI,CAAC,IAAI,CAACd,WAAW,CAACa,IAAI,EAAE,CAAC;QACpD,IAAI,CAACE,aAAa,CAAC,IAAI,CAACf,WAAW,CAACa,IAAI,EAAE,CAAC;QAC3C,IAAI,CAACV,WAAW,CAACW,IAAI,CAAC,IAAI,CAACd,WAAW,CAACa,IAAI,EAAE,CAAC;MAChD;IACF;IAEAG,MAAMA,CAAA;MACJ,IAAI,IAAI,CAAChB,WAAW,CAACa,IAAI,EAAE,EAAE;QAC3B,IAAI,CAACR,iBAAiB,CAACS,IAAI,CAAC,IAAI,CAACd,WAAW,CAACa,IAAI,EAAE,CAAC;QACpD,IAAI,CAACE,aAAa,CAAC,IAAI,CAACf,WAAW,CAACa,IAAI,EAAE,CAAC;QAC3C,IAAI,CAACV,WAAW,CAACW,IAAI,CAAC,IAAI,CAACd,WAAW,CAACa,IAAI,EAAE,CAAC;MAChD;IACF;IAEA;;;IAGA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;;;IAGQE,aAAaA,CAACE,KAAa;MACjC,IAAI,CAACA,KAAK,CAACJ,IAAI,EAAE,EAAE;QACjB,IAAI,CAACZ,aAAa,GAAG,EAAE;QACvB,IAAI,CAACG,mBAAmB,CAACU,IAAI,CAAC,EAAE,CAAC;QACjC,IAAI,CAACZ,WAAW,GAAG,KAAK;QACxB,IAAI,CAACI,mBAAmB,CAACQ,IAAI,CAAC,KAAK,CAAC;QACpC;MACF;MAEA,IAAI,CAACZ,WAAW,GAAG,IAAI;MACvB,IAAI,CAACI,mBAAmB,CAACQ,IAAI,CAAC,IAAI,CAAC;MAEnC,IAAI,CAACf,aAAa,CACfmB,aAAa,CAAC;QACbD,KAAK,EAAEA,KAAK;QACZE,KAAK,EAAE,EAAE;QACTC,SAAS,EAAE;OACZ,CAAC,CACDC,SAAS,CAAC;QACTX,IAAI,EAAGY,QAA+B,IAAI;UACxC;UACA,MAAMC,OAAO,GAA0BD,QAAQ,CAACC,OAAO,IAAI,EAAE;UAC7DC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEF,OAAO,CAAC;UAC/C,IAAI,CAACtB,aAAa,GAAGsB,OAAO;UAC5B,IAAI,CAACnB,mBAAmB,CAACU,IAAI,CAACS,OAAO,CAAC;UACtC,IAAI,CAACrB,WAAW,GAAG,KAAK;UACxB,IAAI,CAACI,mBAAmB,CAACQ,IAAI,CAAC,KAAK,CAAC;QACtC,CAAC;QACDY,KAAK,EAAGA,KAAU,IAAI;UACpBF,OAAO,CAACE,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;UAC7C,IAAI,CAACzB,aAAa,GAAG,EAAE;UACvB,IAAI,CAACG,mBAAmB,CAACU,IAAI,CAAC,EAAE,CAAC;UACjC,IAAI,CAACZ,WAAW,GAAG,KAAK;UACxB,IAAI,CAACI,mBAAmB,CAACQ,IAAI,CAAC,KAAK,CAAC;QACtC;OACD,CAAC;IACN;IAEA;;;IAGAa,WAAWA,CAAA;MACT,IAAI,CAAC3B,WAAW,GAAG,EAAE;MACrB,IAAI,CAACC,aAAa,GAAG,EAAE;MACvB,IAAI,CAACG,mBAAmB,CAACU,IAAI,CAAC,EAAE,CAAC;MACjC,IAAI,CAACT,iBAAiB,CAACS,IAAI,CAAC,EAAE,CAAC;MAC/B,IAAI,CAACZ,WAAW,GAAG,KAAK;MACxB,IAAI,CAACI,mBAAmB,CAACQ,IAAI,CAAC,KAAK,CAAC;IACtC;;uCA/GmBhB,SAAS,EAAAZ,EAAA,CAAA0C,iBAAA,CAAAC,EAAA,CAAAC,aAAA;IAAA;;YAAThC,SAAS;MAAAiC,SAAA;MAAAC,OAAA;QAAA7B,WAAA;QAAAC,mBAAA;QAAAC,iBAAA;QAAAC,mBAAA;MAAA;MAAA2B,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjB1BpD,EAFJ,CAAAC,cAAA,aAA8B,aACA,aACD;UACvBD,EAAA,CAAAsD,SAAA,kBAAmG;UACrGtD,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAKqC;UADnCD,EAAA,CAAAuD,gBAAA,2BAAAC,kDAAAC,MAAA;YAAAzD,EAAA,CAAA0D,kBAAA,CAAAL,GAAA,CAAAvC,WAAA,EAAA2C,MAAA,MAAAJ,GAAA,CAAAvC,WAAA,GAAA2C,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAyB;UACzBzD,EAAA,CAAA2D,UAAA,2BAAAC,kDAAA;YAAA,OAAiBP,GAAA,CAAA3B,cAAA,EAAgB;UAAA,EAAC;UALpC1B,EAAA,CAAAG,YAAA,EAKqC;UAErCH,EAAA,CAAAO,UAAA,IAAAsD,wBAAA,iBAA+D;UAM/D7D,EAAA,CAAAC,cAAA,gBAA+C;UAAnBD,EAAA,CAAA2D,UAAA,mBAAAG,2CAAA;YAAA,OAAST,GAAA,CAAAvB,MAAA,EAAQ;UAAA,EAAC;UAC5C9B,EAAA,CAAAsD,SAAA,kBAAmF;UAGzFtD,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;UAbAH,EAAA,CAAAI,SAAA,GAAyB;UAAzBJ,EAAA,CAAA+D,gBAAA,YAAAV,GAAA,CAAAvC,WAAA,CAAyB;UAGgBd,EAAA,CAAAI,SAAA,EAAkB;UAAlBJ,EAAA,CAAAS,UAAA,UAAA4C,GAAA,CAAAvC,WAAA,CAAkB;;;qBDIrDlB,YAAY,EAAAoE,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAErE,WAAW,EAAAsE,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EAAExE,aAAa;MAAAyE,MAAA;IAAA;;SAE/B3D,SAAS;AAAA;AAAA,SAATA,SAAS,IAAA4D,OAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}