{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { Subject, takeUntil } from 'rxjs';\nimport { FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';\n// Import child components\nimport { ChatInterfaceComponent } from '@shared/components/chat-interface/chat-interface.component';\nimport { AgentActivityComponent } from './components/agent-activity/agent-activity.component';\nimport { AgentOutputComponent } from './components/agent-output/agent-output.component';\nimport { ButtonComponent, IconComponent, TabsComponent } from '@ava/play-comp-library';\nimport { environment } from '@shared/environments/environment';\nimport workflowConstants from './../constants/workflows.json';\nimport { ExecutionStatus } from '@shared/models/execution.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@shared/services/workflow.service\";\nimport * as i3 from \"@shared/index\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/common\";\nconst _c0 = () => ({\n  \"background\": \"#1A46A7\",\n  \"width\": \"100%\",\n  \"border-radius\": \"8px\"\n});\nconst _c1 = () => ({\n  background: \"linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)\",\n  \"--button-effect-color\": \"33, 90, 214\",\n  \"border-radius\": \"8px\",\n  \"box-shadow\": \"none\"\n});\nfunction WorkflowExecutionComponent_ng_container_15_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"span\", 38);\n    i0.ɵɵtext(2, \"Model:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 39);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const agent_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(agent_r4.model);\n  }\n}\nfunction WorkflowExecutionComponent_ng_container_15_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"span\", 38);\n    i0.ɵɵtext(2, \"Tools:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 39);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const agent_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(agent_r4.tools.join(\", \"));\n  }\n}\nfunction WorkflowExecutionComponent_ng_container_15_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 40);\n  }\n}\nfunction WorkflowExecutionComponent_ng_container_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 27)(2, \"div\", 28)(3, \"div\", 29);\n    i0.ɵɵelement(4, \"ava-icon\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 31)(6, \"h3\", 32);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"ava-icon\", 33);\n    i0.ɵɵlistener(\"click\", function WorkflowExecutionComponent_ng_container_15_Template_ava_icon_click_8_listener() {\n      const i_r2 = i0.ɵɵrestoreView(_r1).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleAgentExpansion(i_r2));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 34);\n    i0.ɵɵtemplate(10, WorkflowExecutionComponent_ng_container_15_div_10_Template, 5, 1, \"div\", 35)(11, WorkflowExecutionComponent_ng_container_15_div_11_Template, 5, 1, \"div\", 35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(12, WorkflowExecutionComponent_ng_container_15_div_12_Template, 1, 0, \"div\", 36);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const agent_r4 = ctx.$implicit;\n    const i_r2 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"completed\", ctx_r2.agentStatuses[i_r2] == null ? null : ctx_r2.agentStatuses[i_r2].completed);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"iconName\", (ctx_r2.agentStatuses[i_r2] == null ? null : ctx_r2.agentStatuses[i_r2].completed) ? \"check\" : \"folder\")(\"iconColor\", (ctx_r2.agentStatuses[i_r2] == null ? null : ctx_r2.agentStatuses[i_r2].completed) ? \"#22C55E\" : \"#1A46A7\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((agent_r4 == null ? null : agent_r4.name) || \"Agent \" + (i_r2 + 1));\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"expanded\", ctx_r2.expandedAgents[i_r2]);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"expanded\", ctx_r2.expandedAgents[i_r2]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", agent_r4 == null ? null : agent_r4.model);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", agent_r4 == null ? null : agent_r4.tools == null ? null : agent_r4.tools.length);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r2 < ctx_r2.workflowAgents.length - 1);\n  }\n}\nfunction WorkflowExecutionComponent_div_16_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 46)(2, \"label\", 47);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"textarea\", 48);\n    i0.ɵɵelementStart(5, \"div\", 49);\n    i0.ɵɵelement(6, \"ava-icon\", 50)(7, \"ava-icon\", 51)(8, \"ava-icon\", 52);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const input_r5 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"Input for \", input_r5.input, \" in \", input_r5.name, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formControlName\", input_r5.input)(\"placeholder\", \"Input Entered\");\n  }\n}\nfunction WorkflowExecutionComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"div\", 42)(2, \"div\", 29);\n    i0.ɵɵelement(3, \"ava-icon\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\", 44);\n    i0.ɵɵtext(5, \"Enter the input\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 45);\n    i0.ɵɵtemplate(7, WorkflowExecutionComponent_div_16_ng_container_7_Template, 9, 4, \"ng-container\", 14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.workflowForm);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.userInputList);\n  }\n}\nfunction WorkflowExecutionComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"app-agent-activity\", 54);\n    i0.ɵɵlistener(\"saveLogs\", function WorkflowExecutionComponent_div_29_Template_app_agent_activity_saveLogs_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.saveLogs());\n    })(\"controlAction\", function WorkflowExecutionComponent_div_29_Template_app_agent_activity_controlAction_1_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.handleControlAction($event));\n    })(\"onOutPutBtnClick\", function WorkflowExecutionComponent_div_29_Template_app_agent_activity_onOutPutBtnClick_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onTabChange({\n        id: \"nav-products\",\n        label: \"Agent Output\"\n      }));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"activityLogs\", ctx_r2.workflowLogs)(\"executionDetails\", ctx_r2.executionDetails)(\"progress\", ctx_r2.progress)(\"isRunning\", ctx_r2.isRunning)(\"status\", ctx_r2.status);\n  }\n}\nfunction WorkflowExecutionComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"app-agent-output\", 55);\n    i0.ɵɵlistener(\"export\", function WorkflowExecutionComponent_div_30_Template_app_agent_output_export_1_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.exportResults(\"output\"));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"outputs\", ctx_r2.taskMessage);\n  }\n}\nexport let WorkflowExecutionComponent = /*#__PURE__*/(() => {\n  class WorkflowExecutionComponent {\n    route;\n    router;\n    workflowService;\n    tokenStorage;\n    loaderService;\n    formBuilder;\n    navigationTabs = [{\n      id: 'nav-home',\n      label: 'Agent Activity'\n    }, {\n      id: 'nav-products',\n      label: 'Agent Output'\n    }, {\n      id: 'nav-services',\n      label: 'Preview',\n      disabled: true\n    }];\n    // Workflow details\n    workflowId = null;\n    workflowName = 'Workflow';\n    constants = workflowConstants;\n    chatInterfaceComp;\n    // Activity logs\n    activityLogs = [];\n    activityProgress = 0;\n    executionDetails;\n    isRunning = false;\n    status = ExecutionStatus.notStarted;\n    // Chat messages\n    chatMessages = [];\n    isProcessingChat = false;\n    inputText = '';\n    // Agent outputs\n    agentOutputs = [];\n    workflowForm;\n    fileType = '.zip';\n    // Execution state\n    executionStartTime = null;\n    executionCompleted = false;\n    executionId;\n    workflowLogs = [];\n    enableStreamingLog = environment.enableLogStreaming || 'all';\n    isExecutionComplete = false;\n    progressInterval;\n    // Component lifecycle\n    destroy$ = new Subject();\n    selectedTab = 'Agent Activity';\n    demoTabs = [{\n      id: 'activity',\n      label: 'Agent Activity'\n    }, {\n      id: 'agents',\n      label: 'Agent Output'\n    }, {\n      id: 'preview',\n      label: 'Preview',\n      disabled: true\n    }];\n    errorMsg = false;\n    resMessage;\n    taskMessage = [];\n    isJsonValid = false;\n    disableChat = false;\n    selectedFiles = [];\n    workflowAgents = [];\n    userInputList = [];\n    progress = 0;\n    isLoading = false;\n    loaderColor = '';\n    inputFieldOrder = [];\n    currentInputIndex = 0;\n    activeTabId = 'nav-home';\n    // Panel state\n    isLeftPanelCollapsed = false;\n    expandedAgents = [];\n    agentStatuses = [];\n    constructor(route, router, workflowService, tokenStorage, loaderService, formBuilder) {\n      this.route = route;\n      this.router = router;\n      this.workflowService = workflowService;\n      this.tokenStorage = tokenStorage;\n      this.loaderService = loaderService;\n      this.formBuilder = formBuilder;\n    }\n    ngOnInit() {\n      this.loaderService.disableLoader();\n      this.selectedTab = 'Agent Activity';\n      this.executionId = crypto.randomUUID();\n      // Get workflow ID from route params\n      this.route.paramMap.pipe(takeUntil(this.destroy$)).subscribe(params => {\n        this.workflowId = params.get('id');\n        if (this.workflowId) {\n          this.loadWorkflow(this.workflowId);\n        } else {\n          // No workflow ID, redirect back to workflows page\n          this.router.navigate(['/build/workflows']);\n        }\n      });\n      // this.executeWorkflow()\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n      this.loaderService.enableLoader();\n    }\n    onTabChange(event) {\n      this.selectedTab = event.label;\n      this.activeTabId = event.id;\n      console.log('Tab changed:', event);\n    }\n    // Load workflow data\n    loadWorkflow(id) {\n      // In a real app, this would fetch the workflow from a service\n      console.log(`Loading workflow with ID: ${id}`);\n      this.chatMessages = [{\n        from: 'ai',\n        text: 'I am your workflow assistant. I will help you in executing this workflow.'\n      }];\n      this.workflowForm = this.formBuilder.group({});\n      this.workflowService.getWorkflowById(id).subscribe({\n        next: res => {\n          this.workflowAgents = res.workflowAgents;\n          this.userInputList = this.extractInputField(this.workflowAgents);\n          if (this.userInputList.length === 0) {\n            this.disableChat = true;\n          }\n          this.workflowName = res.name;\n          this.initializeForm();\n          this.initializeAgentStates();\n          this.startInputCollection();\n        },\n        error: err => {\n          // Fallback to demo data for testing\n          this.loadDemoWorkflow();\n          this.disableChat = true;\n          this.chatInterfaceComp.addAiResponse('No input is required for this workflow. Click on send button to execute the workflow.');\n          console.log(err);\n        }\n      });\n    }\n    extractInputField(pipeLineAgents) {\n      const PLACEHOLDER_PATTERNS = /(%\\d+\\$s)|\\{\\{([a-zA-Z0-9-_]+)\\}\\}/g;\n      const placeholderMap = {};\n      pipeLineAgents.forEach(agent => {\n        const agentName = agent?.agentDetails?.name;\n        const agentDescription = agent?.agentDetails?.description;\n        const matches = agentDescription.matchAll(PLACEHOLDER_PATTERNS) || [];\n        for (const match of matches) {\n          const placeholder = match[1] || match[2];\n          const placeholderInput = match[0];\n          if (!placeholderMap[placeholder]) {\n            placeholderMap[placeholder] = {\n              agents: new Set(),\n              inputs: new Set()\n            };\n            ;\n          }\n          placeholderMap[placeholder].agents.add(agentName);\n          placeholderMap[placeholder].inputs.add(placeholderInput);\n        }\n      });\n      return Object.entries(placeholderMap).map(([placeholder, {\n        agents,\n        inputs\n      }]) => ({\n        name: [...agents].length > 2 ? `${[...agents].slice(0, -1).join(\", \")} and ${[...agents].at(-1)}` : [...agents].join(\" and \"),\n        placeholder,\n        input: [...inputs][0]\n      }));\n    }\n    isImageInput(input) {\n      const match = input.match(/{{(.*?)}}/);\n      if (match && match[1]) {\n        const variableName = match[1].trim();\n        return variableName.startsWith('image') || variableName.startsWith('Image');\n      }\n      return false;\n    }\n    initializeForm() {\n      this.userInputList.forEach(label => {\n        this.workflowForm.addControl(label.input, this.formBuilder.control('', Validators.required));\n      });\n    }\n    isInputValid() {\n      return this.workflowForm.valid && this.workflowId;\n    }\n    startFakeProgress() {\n      this.progress = 0;\n      this.progressInterval = setInterval(() => {\n        if (this.progress < 90) {\n          this.progress += 5; // Increase slowly\n        }\n      }, 200); // Adjust speed\n    }\n    stopFakeProgress() {\n      clearInterval(this.progressInterval);\n      this.progress = 100;\n      setTimeout(() => {\n        this.isLoading = false;\n      }, 500); // Small delay to let user see 100%\n    }\n    // Handle new chat message from user\n    handleChatMessage(message) {\n      // console.log('message ', message, 'is blank', message.trim() === '');\n      this.isProcessingChat = true;\n      if (message.trim() === '') {\n        if (this.inputFieldOrder.length === 0) {\n          this.chatInterfaceComp.addAiResponse('Executing the workflow...');\n          this.executeWorkflow();\n        }\n        return;\n      }\n      if (this.isExecutionComplete || this.currentInputIndex === this.inputFieldOrder.length) {\n        this.chatInterfaceComp.addAiResponse('Executing the workflow...');\n        this.executeWorkflow();\n        return;\n      }\n      const field = this.inputFieldOrder[this.currentInputIndex];\n      if (this.isImageInput(field)) {\n        // Ignore text input, wait for file input\n        this.chatInterfaceComp.addAiResponse(`Please upload an image file for ${field}`);\n        return;\n      }\n      this.workflowForm.get(field)?.setValue(message);\n      this.currentInputIndex++;\n      if (this.currentInputIndex < this.inputFieldOrder.length) {\n        this.promptForCurrentField();\n      } else {\n        this.chatInterfaceComp.addAiResponse('Thank you for the input! Executing the workflow...');\n        this.executeWorkflow();\n      }\n    }\n    // Save execution logs\n    saveLogs() {\n      console.log('Saving execution logs...');\n      // This would typically save to a service\n    }\n    // Export results\n    exportResults(section) {\n      console.log(`Exporting ${section} data...`);\n      if (section === 'activity') {\n        const data = this.activityLogs.map(log => `[${log.timestamp}] ${log.message}`).join('\\n');\n        this.downloadAsFile(data, 'workflow-activity-logs.txt', 'text/plain');\n      } else {\n        const data = JSON.stringify(this.agentOutputs, null, 2);\n        this.downloadAsFile(data, 'workflow-outputs.json', 'application/json');\n      }\n    }\n    // Helper method to download data as a file\n    downloadAsFile(data, filename, type) {\n      const blob = new Blob([data], {\n        type\n      });\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = filename;\n      link.click();\n      URL.revokeObjectURL(url);\n    }\n    // Handle controls for execution\n    handleControlAction(action) {\n      console.log(`Control action: ${action}`);\n      // In a real app, this would control the workflow execution\n      if (action === 'play') {\n        this.isRunning = true;\n      } else if (action === 'pause' || action === 'stop') {\n        this.isRunning = false;\n      }\n    }\n    // Navigate back to workflow listing\n    navigateBack() {\n      this.router.navigate(['/build/workflows']);\n    }\n    // Navigate to edit workflow\n    editWorkflow() {\n      if (this.workflowId) {\n        this.router.navigate(['/build/workflows/edit', this.workflowId]);\n      }\n    }\n    logExecutionStatus(delay = 2000) {\n      setTimeout(() => {\n        if (!this.isExecutionComplete) {\n          console.log(this.constants);\n          console.log(this.constants['labels'].workflowExecProcessing);\n          this.workflowLogs.push({\n            content: this.constants['labels'].workflowExecProcessing,\n            color: '#F9DB24'\n          });\n        }\n      }, delay);\n    }\n    // public parseAnsiString(ansiString: string) {\n    //   const regex = ansiRegex();\n    //   const parts = ansiString.split(regex);\n    //   const matches = [...ansiString.matchAll(regex)];\n    //   parts.forEach((part, index) => {\n    //     if (part.trim() !== '') {\n    //       let colorCode = matches[index - 1][0];\n    //       if (index - 2 >= 0 && matches[index - 2]?.includes('\\u001b[1m')) {\n    //         colorCode = `\\u001b[1m${colorCode}`;\n    //       }\n    //       this.workflowLogs.push({\n    //         content: part,\n    //         color: this.colorMap[colorCode] || 'white',\n    //       });\n    //     }\n    //   });\n    // }\n    getWorkflowLogs(executionId) {\n      this.workflowService.workflowLogConnect(executionId).pipe(takeUntil(this.destroy$)).subscribe({\n        next: message => {\n          console.log('message: ', message);\n          const {\n            content,\n            color\n          } = message;\n          if (color) {\n            this.workflowLogs.push({\n              content,\n              color\n            });\n          } else if (this.enableStreamingLog === 'all') {\n            // this.parseAnsiString(content);\n          }\n        },\n        error: err => {\n          this.workflowLogs.push({\n            content: this.constants['workflowLog'],\n            color: 'red'\n          });\n          console.error('WebSocket error:', err);\n        },\n        complete: () => {\n          this.logExecutionStatus();\n          console.log('WebSocket connection closed');\n        }\n      });\n    }\n    // public parseAnsiString(ansiString: string) {\n    //   const regex = ansiRegex();\n    //   const parts = ansiString.split(regex);\n    //   const matches = [...ansiString.matchAll(regex)];\n    //   parts.forEach((part, index) => {\n    //     if (part.trim() !== '') {\n    //       let colorCode = matches[index-1][0];\n    //       if(index - 2 >= 0 && matches[index-2]?.includes('\\u001b[1m')) {\n    //         colorCode = `\\u001b[1m${colorCode}`;\n    //       }\n    //       this.workflowLogs.push({\n    //         content: part, \n    //         color: this.colorMap[colorCode] || 'white', \n    //       });\n    //     }\n    //   });\n    // }\n    validateJson(output) {\n      this.isJsonValid = false;\n      try {\n        const parsedOutput = JSON.parse(output);\n        this.isJsonValid = true;\n        return parsedOutput;\n      } catch (e) {\n        return null;\n      }\n    }\n    executeWorkflow() {\n      let payload = new FormData();\n      let queryString = '';\n      this.status = ExecutionStatus.running;\n      if (this.selectedFiles.length) {\n        this.selectedFiles.forEach(file => {\n          payload.append('files', file);\n        });\n        payload.append('workflowId', this.workflowId);\n        payload.append('userInputs', JSON.stringify(this.workflowForm.value));\n        payload.append('user', this.tokenStorage.getDaUsername());\n        payload.append('executionId', this.executionId);\n        queryString = '/files';\n      } else {\n        payload = {\n          pipeLineId: this.workflowId,\n          userInputs: this.workflowForm.value,\n          executionId: this.executionId,\n          user: this.tokenStorage.getDaUsername()\n        };\n      }\n      this.getWorkflowLogs(this.executionId);\n      this.startFakeProgress();\n      this.workflowService.executeWorkflow(payload, queryString).pipe(takeUntil(this.destroy$)).subscribe({\n        next: res => {\n          this.isProcessingChat = false;\n          this.isRunning = false;\n          this.chatInterfaceComp.addAiResponse(res?.message || \"Workflow execution completed successfully!\");\n          if (res?.workflowResponse?.pipeline?.output) {\n            this.isExecutionComplete = true;\n            // console.log(this.constants['labels'].workflowExecComplete);\n            this.workflowLogs.push({\n              content: this.constants['labels'].workflowExecComplete,\n              color: '#0F8251'\n            });\n            this.errorMsg = false;\n            this.resMessage = res?.workflowResponse?.pipeline?.output;\n            this.agentOutputs = res?.workflowResponse?.pipeline?.tasksOutputs.map(task => {\n              return {\n                id: task?.id || '',\n                title: task?.title || '',\n                content: task?.content || '',\n                agentName: task?.agentName || '',\n                timestamp: task?.timestamp || '',\n                type: task?.type || '',\n                description: task?.description || '',\n                expected_output: task?.expected_output || '',\n                summary: task?.summary || '',\n                raw: task?.raw || ''\n              };\n            });\n            this.taskMessage = res?.workflowResponse?.pipeline?.tasksOutputs.map(task => {\n              return {\n                description: task.description,\n                summary: task.summary,\n                raw: task.raw,\n                expected_output: task.expected_output\n              };\n            });\n            // if(\"file_download_url\" in res?.pipeline){\n            //   this.isFileWriter = true;\n            //   this.fileDownloadLink = res?.pipeline?.file_download_url;\n            //   if(!this.fileDownloadLink){\n            //     this.fileDownloadUrlError = [];\n            //     this.fileDownloadUrlError.push(\"Output file is not generated yet!\")\n            //   }\n            // }\n            // this.isAccordian = true\n          }\n          this.validateJson(this.resMessage);\n          this.status = ExecutionStatus.completed;\n          this.stopFakeProgress();\n          this.selectedFiles = [];\n        },\n        error: error => {\n          this.isExecutionComplete = true;\n          this.isProcessingChat = false;\n          this.errorMsg = true;\n          this.resMessage = error?.error?.detail;\n          this.workflowService.workflowLogDisconnect();\n          this.workflowLogs.push({\n            content: this.constants['labels'].workflowLogFailed,\n            color: 'red'\n          });\n          this.chatInterfaceComp.addAiResponse('Something went wrong, Workflow execution has failed.');\n          this.selectedFiles = [];\n          this.stopFakeProgress();\n          console.log('error is', error.message);\n        }\n      });\n    }\n    // public asyncExecutePipeline() {\n    //   const payload: FormData = new FormData();\n    //   if (this.selectedFiles?.length) {\n    //     for (const element of this.selectedFiles) {\n    //       payload.append('files', element)\n    //     }\n    //   }\n    //   payload.append('pipeLineId', String(this.workflowId));\n    //   payload.append('userInputs', JSON.stringify(this.workflowForm.value));\n    //   payload.append('user', this.tokenStorage.getDaUsername() || '');\n    //   payload.append('executionId', this.executionId);\n    //   this.workflowService.asyncExecutePipeline(payload).pipe().subscribe({\n    //     next: (res: any) => {\n    //       if(res) {\n    //         // res handling\n    //         console.log(res);\n    //       }\n    //     },\n    //     error: e => {\n    //       // error handling\n    //       console.log(e);\n    //     }\n    //   })\n    // }\n    handleAttachment() {\n      console.log('handleAttachment');\n    }\n    onAttachmentsSelected(files) {\n      if (this.currentInputIndex === this.inputFieldOrder.length || this.inputFieldOrder.length === 0) {\n        this.selectedFiles = files;\n        return;\n      }\n      const field = this.inputFieldOrder[this.currentInputIndex];\n      if (this.isImageInput(field)) {\n        if (files && files.length > 0) {\n          this.onImageSelected(files[0]);\n        }\n      } else {\n        this.selectedFiles = files;\n      }\n    }\n    startInputCollection() {\n      this.inputFieldOrder = Object.keys(this.workflowForm.controls);\n      this.currentInputIndex = 0;\n      if (this.inputFieldOrder.length > 0) {\n        this.promptForCurrentField();\n      } else {\n        this.disableChat = true;\n        this.chatInterfaceComp.addAiResponse('No input is required for this workflow. Click on send button to execute the workflow.');\n      }\n    }\n    promptForCurrentField() {\n      const field = this.inputFieldOrder[this.currentInputIndex];\n      if (this.isImageInput(field)) {\n        this.fileType = '.jpeg,.png,.jpg,.svg';\n        this.chatInterfaceComp.addAiResponse(`Please upload an image for ${field}`);\n        // UI should now show a file input for the user\n      } else {\n        this.fileType = '.zip'; // or whatever default you want for non-image\n        this.chatInterfaceComp.addAiResponse(`Please enter the value of ${field}`);\n      }\n    }\n    onImageSelected(file) {\n      const field = this.inputFieldOrder[this.currentInputIndex];\n      if (!this.isImageInput(field)) return;\n      const reader = new FileReader();\n      reader.onload = () => {\n        const base64String = reader.result;\n        this.workflowForm.get(field)?.setValue(base64String);\n        this.currentInputIndex++;\n        if (this.currentInputIndex < this.inputFieldOrder.length) {\n          this.promptForCurrentField();\n        } else {\n          this.chatInterfaceComp.addAiResponse('Thank you! Running the workflow...');\n          this.executeWorkflow();\n        }\n      };\n      reader.readAsDataURL(file);\n    }\n    // Panel management methods\n    toggleLeftPanel() {\n      this.isLeftPanelCollapsed = !this.isLeftPanelCollapsed;\n    }\n    toggleAgentExpansion(index) {\n      this.expandedAgents[index] = !this.expandedAgents[index];\n    }\n    initializeAgentStates() {\n      this.expandedAgents = new Array(this.workflowAgents.length).fill(false);\n      this.agentStatuses = this.workflowAgents.map(() => ({\n        completed: false\n      }));\n    }\n    // Extract input fields method (similar to pipeline component)\n    extractInputField(workflowAgents) {\n      const PLACEHOLDER_PATTERNS = /(%\\d+\\$s)|\\{\\{([a-zA-Z0-9-_]+)\\}\\}/g;\n      const placeholderMap = {};\n      workflowAgents.forEach(agent => {\n        const agentName = agent?.name || `Agent ${workflowAgents.indexOf(agent) + 1}`;\n        const agentDescription = agent?.description || agent?.task?.description || '';\n        const matches = agentDescription.matchAll(PLACEHOLDER_PATTERNS) || [];\n        for (const match of matches) {\n          const placeholder = match[1] || match[2];\n          const placeholderInput = match[0];\n          if (!placeholderMap[placeholder]) {\n            placeholderMap[placeholder] = {\n              agents: new Set(),\n              inputs: new Set()\n            };\n          }\n          placeholderMap[placeholder].agents.add(agentName);\n          placeholderMap[placeholder].inputs.add(placeholderInput);\n        }\n      });\n      return Object.entries(placeholderMap).map(([placeholder, {\n        agents,\n        inputs\n      }]) => ({\n        name: [...agents].length > 2 ? `${[...agents].slice(0, -1).join(\", \")} and ${[...agents].at(-1)}` : [...agents].join(\" and \"),\n        placeholder,\n        input: [...inputs][0]\n      }));\n    }\n    // Input validation\n    isInputValid() {\n      return this.workflowForm.valid && this.workflowId;\n    }\n    static ɵfac = function WorkflowExecutionComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || WorkflowExecutionComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.WorkflowService), i0.ɵɵdirectiveInject(i3.TokenStorageService), i0.ɵɵdirectiveInject(i3.LoaderService), i0.ɵɵdirectiveInject(i4.FormBuilder));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: WorkflowExecutionComponent,\n      selectors: [[\"app-workflow-execution\"]],\n      viewQuery: function WorkflowExecutionComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(ChatInterfaceComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.chatInterfaceComp = _t.first);\n        }\n      },\n      decls: 31,\n      vars: 16,\n      consts: [[1, \"workflow-execution-container\"], [\"width\", \"0\", \"height\", \"0\", 2, \"position\", \"absolute\"], [\"id\", \"gradient\", \"x1\", \"0%\", \"y1\", \"0%\", \"x2\", \"100%\", \"y2\", \"0%\"], [\"offset\", \"0%\", \"stop-color\", \"#6566CD\"], [\"offset\", \"100%\", \"stop-color\", \"#F96CAB\"], [\"role\", \"main\", 1, \"execution-content\"], [\"role\", \"region\", \"aria-label\", \"Workflow Execution Panel\", 1, \"column\", \"execution-panel-column\"], [1, \"column-content\", \"execution-panel-content\"], [1, \"column-header\"], [1, \"header-left\"], [\"iconName\", \"arrowLeft\", \"iconColor\", \"#1A46A7\", 1, \"back-icon\", 3, \"click\"], [\"iconName\", \"panelLeft\", \"iconColor\", \"#1A46A7\", 1, \"panel-icon\", 3, \"click\"], [1, \"panel-content\"], [1, \"agents-section\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"input-section\", 4, \"ngIf\"], [1, \"info-message\"], [1, \"execute-section\"], [\"label\", \"\\u25B6 Execute Agent\", \"variant\", \"primary\", \"size\", \"large\", 3, \"click\", \"disabled\", \"customStyles\"], [\"role\", \"region\", \"aria-label\", \"Agent Output\", 1, \"column\", \"output-column\"], [1, \"column-content\"], [1, \"column-header\", \"row\"], [1, \"col-7\"], [\"variant\", \"button\", \"buttonShape\", \"pill\", \"ariaLabel\", \"Pill navigation tabs\", 3, \"tabChange\", \"tabs\", \"activeTabId\", \"showContentPanels\"], [1, \"col-5\", \"right-section-header\"], [\"label\", \"Send for Approval\", \"variant\", \"primary\", \"size\", \"medium\", 3, \"customStyles\"], [\"style\", \"height: 100%\", 4, \"ngIf\"], [1, \"agent-card\"], [1, \"agent-header\"], [1, \"status-icon\"], [3, \"iconName\", \"iconColor\"], [1, \"agent-info\"], [1, \"agent-name\"], [\"iconName\", \"chevronDown\", \"iconColor\", \"#6B7280\", 1, \"expand-icon\", 3, \"click\"], [1, \"agent-details\"], [\"class\", \"agent-detail-item\", 4, \"ngIf\"], [\"class\", \"connection-line\", 4, \"ngIf\"], [1, \"agent-detail-item\"], [1, \"detail-label\"], [1, \"detail-value\"], [1, \"connection-line\"], [1, \"input-section\"], [1, \"input-header\"], [\"iconName\", \"check\", \"iconColor\", \"#22C55E\"], [1, \"input-title\"], [1, \"input-container\", 3, \"formGroup\"], [1, \"input-field\"], [1, \"input-label\"], [1, \"input-textarea\", 3, \"formControlName\", \"placeholder\"], [1, \"input-actions\"], [\"iconName\", \"paperclip\", \"iconColor\", \"#6B7280\", 1, \"attach-icon\"], [\"iconName\", \"edit\", \"iconColor\", \"#6B7280\", 1, \"edit-icon\"], [\"iconName\", \"send\", \"iconColor\", \"#1A46A7\", 1, \"send-icon\"], [2, \"height\", \"100%\"], [3, \"saveLogs\", \"controlAction\", \"onOutPutBtnClick\", \"activityLogs\", \"executionDetails\", \"progress\", \"isRunning\", \"status\"], [3, \"export\", \"outputs\"]],\n      template: function WorkflowExecutionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(1, \"svg\", 1)(2, \"defs\")(3, \"linearGradient\", 2);\n          i0.ɵɵelement(4, \"stop\", 3)(5, \"stop\", 4);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6)(8, \"div\", 7)(9, \"div\", 8)(10, \"div\", 9)(11, \"ava-icon\", 10);\n          i0.ɵɵlistener(\"click\", function WorkflowExecutionComponent_Template_ava_icon_click_11_listener() {\n            return ctx.navigateBack();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"ava-icon\", 11);\n          i0.ɵɵlistener(\"click\", function WorkflowExecutionComponent_Template_ava_icon_click_12_listener() {\n            return ctx.toggleLeftPanel();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 12)(14, \"div\", 13);\n          i0.ɵɵtemplate(15, WorkflowExecutionComponent_ng_container_15_Template, 13, 12, \"ng-container\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(16, WorkflowExecutionComponent_div_16_Template, 8, 2, \"div\", 15);\n          i0.ɵɵelementStart(17, \"div\", 16)(18, \"p\");\n          i0.ɵɵtext(19, \"Agent input needed in order to execute Workflow\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 17)(21, \"ava-button\", 18);\n          i0.ɵɵlistener(\"click\", function WorkflowExecutionComponent_Template_ava_button_click_21_listener() {\n            return ctx.executeWorkflow();\n          });\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(22, \"div\", 19)(23, \"div\", 20)(24, \"div\", 21)(25, \"div\", 22)(26, \"ava-tabs\", 23);\n          i0.ɵɵlistener(\"tabChange\", function WorkflowExecutionComponent_Template_ava_tabs_tabChange_26_listener($event) {\n            return ctx.onTabChange($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"div\", 24);\n          i0.ɵɵelement(28, \"ava-button\", 25);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(29, WorkflowExecutionComponent_div_29_Template, 2, 5, \"div\", 26)(30, WorkflowExecutionComponent_div_30_Template, 2, 1, \"div\", 26);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵclassProp(\"collapsed\", ctx.isLeftPanelCollapsed);\n          i0.ɵɵadvance(6);\n          i0.ɵɵclassProp(\"hidden\", ctx.isLeftPanelCollapsed);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.workflowAgents);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.userInputList.length > 0);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"disabled\", !ctx.isInputValid())(\"customStyles\", i0.ɵɵpureFunction0(14, _c0));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"tabs\", ctx.navigationTabs)(\"activeTabId\", ctx.activeTabId)(\"showContentPanels\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"customStyles\", i0.ɵɵpureFunction0(15, _c1));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTab === \"Agent Activity\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTab === \"Agent Output\");\n        }\n      },\n      dependencies: [CommonModule, i5.NgForOf, i5.NgIf, FormsModule, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, ReactiveFormsModule, i4.FormGroupDirective, i4.FormControlName, AgentActivityComponent, AgentOutputComponent, TabsComponent, ButtonComponent, IconComponent],\n      styles: [\".ava-tabs {\\n  background: none !important;\\n}\\n\\n  .ava-tabs__container {\\n  border-radius: none !important;\\n  border: none !important;\\n  padding: 0 !important;\\n  box-shadow: none !important;\\n  background: none !important;\\n}\\n\\n  .ava-tabs__list {\\n  padding: 0 !important;\\n}\\n\\n.workflow-execution-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 93%;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 20px 24px;\\n  border-bottom: 1px solid var(--border-color, #e0e0e0);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-title[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 24px;\\n  font-weight: 600;\\n  color: var(--text-color, #333);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-title[_ngcontent-%COMP%]   .header-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%], \\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 8px 16px;\\n  border-radius: 5px;\\n  font-weight: 500;\\n  font-size: 14px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%], \\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  margin-right: 6px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%] {\\n  background-color: var(--bg-muted, #f5f5f5);\\n  border: 1px solid var(--border-color, #e0e0e0);\\n  color: var(--text-color, #333);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]:hover {\\n  background-color: var(--bg-muted-hover, #e9e9e9);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%] {\\n  background-color: var(--card-bg, #fff);\\n  border: 1px solid var(--border-color, #e0e0e0);\\n  color: var(--text-color, #333);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]:hover {\\n  background-color: var(--card-bg-hover, #f9f9f9);\\n  border-color: var(--border-color-dark, #d0d0d0);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-grow: 1;\\n  height: 95vh;\\n  overflow: hidden;\\n  gap: 20px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 8px 16px;\\n  background-color: #e9effd;\\n  color: #fff;\\n  height: 64px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: #1a46a7;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  padding: 8px 16px;\\n  background-color: var(--card-bg, white);\\n  position: relative;\\n  border: 1px solid transparent;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: transparent;\\n  background-image: linear-gradient(90deg, #6566cd 0%, #f96cab 100%);\\n  -webkit-background-clip: text;\\n  background-clip: text;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  border-radius: 8px;\\n  padding: 1px;\\n  background: linear-gradient(90deg, #6566cd 0%, #f96cab 100%);\\n  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);\\n  mask-composite: exclude;\\n  pointer-events: none;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%]:hover {\\n  background-color: var(--bg-muted-hover, #f9f5ff);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 18px;\\n  height: 18px;\\n  fill: none;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]   path[_ngcontent-%COMP%] {\\n  stroke-width: 2;\\n  stroke: url(#gradient);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 0;\\n  display: flex;\\n  flex-direction: column;\\n  scrollbar-width: none;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 8px;\\n  height: 8px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: var(--scrollbar-track, #f1f1f1);\\n  border-radius: 4px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: var(--scrollbar-thumb, #d1d1d1);\\n  border-radius: 4px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: var(--scrollbar-thumb-hover, #b1b1b1);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%]   .nav-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  gap: 16px;\\n  background-color: #e9effd;\\n  padding: 0 15px 0 0;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%]   .nav-wrapper[_ngcontent-%COMP%]   .right-inner-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%]   .nav-wrapper[_ngcontent-%COMP%]   .right-inner-wrapper[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #fff;\\n  padding: 0 10px;\\n  margin: 0px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .activity-column[_ngcontent-%COMP%] {\\n  background: transparent;\\n  box-shadow: none;\\n  border-radius: 8px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .activity-column[_ngcontent-%COMP%]   app-agent-activity[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%] {\\n  flex: 0 0 400px;\\n  background: #F0F4FF;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 15px var(--card-shadow, rgba(0, 0, 0, 0.05));\\n  transition: all 0.3s ease;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column.collapsed[_ngcontent-%COMP%] {\\n  flex: 0 0 60px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column.collapsed[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%] {\\n  background: #F0F4FF;\\n  border-bottom: 1px solid #E5E7EB;\\n  padding: 16px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  align-items: center;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .back-icon[_ngcontent-%COMP%], \\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .panel-icon[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transition: transform 0.2s ease;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .back-icon[_ngcontent-%COMP%]:hover, \\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .panel-icon[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%]   .execution-panel-content[_ngcontent-%COMP%] {\\n  padding: 0;\\n  height: 100%;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%]   .execution-panel-content[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  height: calc(100% - 80px);\\n  overflow-y: auto;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%]   .execution-panel-content[_ngcontent-%COMP%]   .panel-content.hidden[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .playground-column[_ngcontent-%COMP%] {\\n  flex: 3.5;\\n  background: var(--card-bg);\\n  border-radius: 8px;\\n  box-shadow: 0 4px 15px var(--card-shadow, rgba(0, 0, 0, 0.05));\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .playground-column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .header-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  gap: 12px;\\n  align-items: center;\\n  color: #1a46a7;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .playground-column[_ngcontent-%COMP%]   .playground-content[_ngcontent-%COMP%] {\\n  padding: 0;\\n  height: 100%;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .playground-column[_ngcontent-%COMP%]   .playground-content[_ngcontent-%COMP%]   .chat-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  min-height: 400px;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  position: relative;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .playground-column[_ngcontent-%COMP%]   .playground-content[_ngcontent-%COMP%]   .chat-container[_ngcontent-%COMP%]     .prompt-container {\\n  margin-top: 30px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .playground-column[_ngcontent-%COMP%]   .playground-content[_ngcontent-%COMP%]   .chat-container[_ngcontent-%COMP%]   .playground-title[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: var(--dashboard-primary, #6566cd);\\n  margin: 16px 16px;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .playground-column[_ngcontent-%COMP%]   .playground-content[_ngcontent-%COMP%]   .chat-container[_ngcontent-%COMP%]   app-chat-interface[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  flex: 1;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .output-column[_ngcontent-%COMP%] {\\n  flex: 5.5;\\n  background-color: var(--card-bg, white);\\n  border-radius: 8px;\\n  box-shadow: 0 4px 15px var(--card-shadow, rgba(0, 0, 0, 0.05));\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .output-column[_ngcontent-%COMP%]   app-agent-output[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n  nav.ava-tabs__list {\\n  background: #e9effd;\\n  padding: 4px;\\n}\\n\\n  button.ava-button.primary.active {\\n  background: #616161;\\n  color: #fff;\\n}\\n\\n  .column-header .ava-tabs[data-variant=button] .ava-tabs__tab--pill {\\n  border-radius: 8px !important;\\n  padding: 12px 16px !important;\\n  font-family: \\\"Mulish\\\";\\n}\\n\\n  .ava-tabs[data-variant=button] .ava-tabs__tab--active .ava-tabs__tab-text {\\n  color: white;\\n}\\n\\n  .ava-tabs__tab-text {\\n  color: #4c515b;\\n  font-family: \\\"Mulish\\\";\\n  font-weight: 600;\\n}\\n\\n  .right-section-header .ava-button.secondary {\\n  color: #1a46a7;\\n  border: none;\\n}\\n\\n  .right-section-header .ava-button.secondary:hover {\\n  color: #1a46a7;\\n  border: none;\\n}\\n\\n.right-section-header[_ngcontent-%COMP%] {\\n  text-align: end;\\n}\\n\\n.agents-section[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%] {\\n  background: #E8F0FE;\\n  border-radius: 12px;\\n  padding: 16px;\\n  margin-bottom: 8px;\\n  transition: all 0.3s ease;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card.completed[_ngcontent-%COMP%] {\\n  background: #F0F9FF;\\n  border-left: 4px solid #22C55E;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-header[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  background: white;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-header[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-header[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%]   .agent-name[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #1F2937;\\n  margin: 0;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-header[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%]   .expand-icon[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transition: transform 0.2s ease;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-header[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%]   .expand-icon.expanded[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-details[_ngcontent-%COMP%] {\\n  margin-top: 12px;\\n  max-height: 0;\\n  overflow: hidden;\\n  transition: max-height 0.3s ease;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-details.expanded[_ngcontent-%COMP%] {\\n  max-height: 200px;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-details[_ngcontent-%COMP%]   .agent-detail-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  padding: 4px 0;\\n  font-size: 14px;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-details[_ngcontent-%COMP%]   .agent-detail-item[_ngcontent-%COMP%]   .detail-label[_ngcontent-%COMP%] {\\n  color: #6B7280;\\n  font-weight: 500;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-details[_ngcontent-%COMP%]   .agent-detail-item[_ngcontent-%COMP%]   .detail-value[_ngcontent-%COMP%] {\\n  color: #1F2937;\\n  text-align: right;\\n  max-width: 200px;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.agents-section[_ngcontent-%COMP%]   .connection-line[_ngcontent-%COMP%] {\\n  width: 2px;\\n  height: 16px;\\n  background: #1A46A7;\\n  margin: 0 auto 8px auto;\\n}\\n\\n.input-section[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.input-section[_ngcontent-%COMP%]   .input-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  margin-bottom: 16px;\\n}\\n.input-section[_ngcontent-%COMP%]   .input-header[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  background: white;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.input-section[_ngcontent-%COMP%]   .input-header[_ngcontent-%COMP%]   .input-title[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #1F2937;\\n  margin: 0;\\n}\\n.input-section[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin-bottom: 16px;\\n}\\n.input-section[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .input-label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 12px;\\n  font-weight: 500;\\n  color: #6B7280;\\n  margin-bottom: 8px;\\n}\\n.input-section[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .input-textarea[_ngcontent-%COMP%] {\\n  width: 100%;\\n  min-height: 80px;\\n  padding: 12px 50px 12px 16px;\\n  border: 2px solid #E5E7EB;\\n  border-radius: 12px;\\n  font-size: 14px;\\n  resize: vertical;\\n  background: white;\\n}\\n.input-section[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .input-textarea[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #1A46A7;\\n}\\n.input-section[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .input-textarea[_ngcontent-%COMP%]::placeholder {\\n  color: #9CA3AF;\\n}\\n.input-section[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .input-actions[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 12px;\\n  bottom: 12px;\\n  display: flex;\\n  gap: 8px;\\n}\\n.input-section[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .input-actions[_ngcontent-%COMP%]   .attach-icon[_ngcontent-%COMP%], \\n.input-section[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .input-actions[_ngcontent-%COMP%]   .edit-icon[_ngcontent-%COMP%], \\n.input-section[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .input-actions[_ngcontent-%COMP%]   .send-icon[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transition: transform 0.2s ease;\\n}\\n.input-section[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .input-actions[_ngcontent-%COMP%]   .attach-icon[_ngcontent-%COMP%]:hover, \\n.input-section[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .input-actions[_ngcontent-%COMP%]   .edit-icon[_ngcontent-%COMP%]:hover, \\n.input-section[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .input-actions[_ngcontent-%COMP%]   .send-icon[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n}\\n\\n.info-message[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 24px;\\n}\\n.info-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #1A46A7;\\n  font-size: 14px;\\n  margin: 0;\\n}\\n\\n.execute-section[_ngcontent-%COMP%] {\\n  margin-top: auto;\\n  padding-top: 24px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return WorkflowExecutionComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "Subject", "takeUntil", "FormsModule", "ReactiveFormsModule", "Validators", "ChatInterfaceComponent", "AgentActivityComponent", "AgentOutputComponent", "ButtonComponent", "IconComponent", "TabsComponent", "environment", "workflowConstants", "ExecutionStatus", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "agent_r4", "model", "tools", "join", "ɵɵelement", "ɵɵelementContainerStart", "ɵɵlistener", "WorkflowExecutionComponent_ng_container_15_Template_ava_icon_click_8_listener", "i_r2", "ɵɵrestoreView", "_r1", "index", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "toggleAgentExpansion", "ɵɵtemplate", "WorkflowExecutionComponent_ng_container_15_div_10_Template", "WorkflowExecutionComponent_ng_container_15_div_11_Template", "WorkflowExecutionComponent_ng_container_15_div_12_Template", "ɵɵclassProp", "agentStatuses", "completed", "ɵɵproperty", "name", "expandedAgents", "length", "workflowAgents", "ɵɵtextInterpolate2", "input_r5", "input", "WorkflowExecutionComponent_div_16_ng_container_7_Template", "workflowForm", "userInputList", "WorkflowExecutionComponent_div_29_Template_app_agent_activity_saveLogs_1_listener", "_r6", "saveLogs", "WorkflowExecutionComponent_div_29_Template_app_agent_activity_controlAction_1_listener", "$event", "handleControlAction", "WorkflowExecutionComponent_div_29_Template_app_agent_activity_onOutPutBtnClick_1_listener", "onTabChange", "id", "label", "workflowLogs", "executionDetails", "progress", "isRunning", "status", "WorkflowExecutionComponent_div_30_Template_app_agent_output_export_1_listener", "_r7", "exportResults", "taskMessage", "WorkflowExecutionComponent", "route", "router", "workflowService", "tokenStorage", "loaderService", "formBuilder", "navigationTabs", "disabled", "workflowId", "workflowName", "constants", "chatInterfaceComp", "activityLogs", "activityProgress", "notStarted", "chatMessages", "isProcessingChat", "inputText", "agentOutputs", "fileType", "executionStartTime", "executionCompleted", "executionId", "enableStreamingLog", "enableLogStreaming", "isExecutionComplete", "progressInterval", "destroy$", "selectedTab", "demoTabs", "errorMsg", "resMessage", "isJsonValid", "disable<PERSON>hat", "selectedFiles", "isLoading", "loaderColor", "inputFieldOrder", "currentInputIndex", "activeTabId", "isLeftPanelCollapsed", "constructor", "ngOnInit", "disable<PERSON><PERSON><PERSON>", "crypto", "randomUUID", "paramMap", "pipe", "subscribe", "params", "get", "loadWorkflow", "navigate", "ngOnDestroy", "next", "complete", "<PERSON><PERSON><PERSON><PERSON>", "event", "console", "log", "from", "text", "group", "getWorkflowById", "res", "extractInputField", "initializeForm", "initializeAgentStates", "startInputCollection", "error", "err", "loadDemoWorkflow", "addAiResponse", "pipeLineAgents", "PLACEHOLDER_PATTERNS", "placeholderM<PERSON>", "for<PERSON>ach", "agent", "<PERSON><PERSON><PERSON>", "agentDetails", "agentDescription", "description", "matches", "matchAll", "match", "placeholder", "placeholderInput", "agents", "Set", "inputs", "add", "Object", "entries", "map", "slice", "at", "isImageInput", "variableName", "trim", "startsWith", "addControl", "control", "required", "isInputValid", "valid", "startFakeProgress", "setInterval", "stopFakeProgress", "clearInterval", "setTimeout", "handleChatMessage", "message", "executeWorkflow", "field", "setValue", "promptForCurrentField", "section", "data", "timestamp", "downloadAsFile", "JSON", "stringify", "filename", "type", "blob", "Blob", "url", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "click", "revokeObjectURL", "action", "navigateBack", "editWorkflow", "logExecutionStatus", "delay", "workflowExecProcessing", "push", "content", "color", "getWorkflowLogs", "workflowLogConnect", "validate<PERSON><PERSON>", "output", "parsedOutput", "parse", "e", "payload", "FormData", "queryString", "running", "file", "append", "value", "getDaUsername", "pipeLineId", "userInputs", "user", "workflowResponse", "pipeline", "workflowExecComplete", "tasksOutputs", "task", "title", "expected_output", "summary", "raw", "detail", "workflowLogDisconnect", "workflowLogFailed", "handleAttachment", "onAttachmentsSelected", "files", "onImageSelected", "keys", "controls", "reader", "FileReader", "onload", "base64String", "result", "readAsDataURL", "toggleLeftPanel", "Array", "fill", "indexOf", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "WorkflowService", "i3", "TokenStorageService", "LoaderService", "i4", "FormBuilder", "selectors", "viewQuery", "WorkflowExecutionComponent_Query", "rf", "ctx", "WorkflowExecutionComponent_Template_ava_icon_click_11_listener", "WorkflowExecutionComponent_Template_ava_icon_click_12_listener", "WorkflowExecutionComponent_ng_container_15_Template", "WorkflowExecutionComponent_div_16_Template", "WorkflowExecutionComponent_Template_ava_button_click_21_listener", "WorkflowExecutionComponent_Template_ava_tabs_tabChange_26_listener", "WorkflowExecutionComponent_div_29_Template", "WorkflowExecutionComponent_div_30_Template", "ɵɵpureFunction0", "_c0", "_c1", "i5", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\workflows\\workflow-execution\\workflow-execution.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\pages\\workflows\\workflow-execution\\workflow-execution.component.html"], "sourcesContent": ["import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';\r\n\r\n// Import child components\r\nimport { ChatInterfaceComponent } from '@shared/components/chat-interface/chat-interface.component';\r\nimport { ChatMessage } from '@shared/components/chat-window/chat-window.component';\r\nimport {\r\n  AgentActivityComponent,\r\n} from './components/agent-activity/agent-activity.component';\r\nimport {\r\n  AgentOutputComponent,\r\n  AgentOutput as OutputItem,\r\n} from './components/agent-output/agent-output.component';\r\nimport {\r\n  ButtonComponent,\r\n  IconComponent,\r\n  TabItem,\r\n  TabsComponent,\r\n} from '@ava/play-comp-library';\r\nimport { WorkflowService } from '@shared/services/workflow.service';\r\nimport { environment } from '@shared/environments/environment';\r\nimport workflowConstants from './../constants/workflows.json';\r\nimport { TokenStorageService, LoaderService } from '@shared/index';\r\nimport { AvaTab } from '@shared/models/tab.model';\r\nimport { ExecutionStatus, ActivityLog } from '@shared/models/execution.model';\r\nimport { AgentActivityExecutionDetails } from './components/agent-activity/agent-activity.component';\r\n\r\n\r\n@Component({\r\n  selector: 'app-workflow-execution',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    ChatInterfaceComponent,\r\n    AgentActivityComponent,\r\n    AgentOutputComponent,\r\n    TabsComponent,\r\n    ButtonComponent,\r\n    IconComponent,\r\n  ],\r\n  templateUrl: './workflow-execution.component.html',\r\n  styleUrls: ['./workflow-execution.component.scss'],\r\n})\r\nexport class WorkflowExecutionComponent implements OnInit, OnDestroy {\r\n  navigationTabs: TabItem[] = [\r\n    { id: 'nav-home', label: 'Agent Activity' },\r\n    { id: 'nav-products', label: 'Agent Output' },\r\n    { id: 'nav-services', label: 'Preview', disabled: true },\r\n  ];\r\n  // Workflow details\r\n  workflowId: string | null = null;\r\n  workflowName: string = 'Workflow';\r\n\r\n  constants = workflowConstants as Record<string, any>;\r\n\r\n  @ViewChild(ChatInterfaceComponent, { static: false })\r\n  chatInterfaceComp!: ChatInterfaceComponent;\r\n\r\n  // Activity logs\r\n  activityLogs: ActivityLog[] = [];\r\n  activityProgress: number = 0;\r\n  executionDetails?: AgentActivityExecutionDetails;\r\n  isRunning: boolean = false;\r\n  status: ExecutionStatus = ExecutionStatus.notStarted;\r\n\r\n  // Chat messages\r\n  chatMessages: ChatMessage[] = [];\r\n  isProcessingChat: boolean = false;\r\n  inputText = '';\r\n\r\n  // Agent outputs\r\n  agentOutputs: OutputItem[] = [];\r\n  public workflowForm!: FormGroup;\r\n  public fileType : string = '.zip';\r\n\r\n  // Execution state\r\n  executionStartTime: Date | null = null;\r\n  executionCompleted: boolean = false;\r\n  executionId!: string;\r\n\r\n  public workflowLogs: any[] = [];\r\n  enableStreamingLog = environment.enableLogStreaming || 'all';\r\n\r\n  public isExecutionComplete: boolean = false;\r\n  progressInterval: any;\r\n\r\n  // Component lifecycle\r\n  private destroy$ = new Subject<void>();\r\n  selectedTab: string = 'Agent Activity';\r\n  demoTabs: AvaTab[] = [\r\n    { id: 'activity', label: 'Agent Activity' },\r\n    { id: 'agents', label: 'Agent Output' },\r\n    { id: 'preview', label: 'Preview', disabled: true },\r\n  ];\r\n  errorMsg = false;\r\n  resMessage: any;\r\n  taskMessage = [];\r\n  isJsonValid = false;\r\n  disableChat : boolean = false;\r\n  selectedFiles: File[] = [];\r\n  workflowAgents: any[] = [];\r\n  userInputList: any[] = [];\r\n  progress = 0;\r\n  isLoading = false;\r\n  loaderColor: string = '';\r\n\r\n  inputFieldOrder: string[] = [];\r\n  currentInputIndex: number = 0;\r\n  activeTabId: string = 'nav-home';\r\n\r\n  // Panel state\r\n  isLeftPanelCollapsed = false;\r\n  expandedAgents: boolean[] = [];\r\n  agentStatuses: { completed: boolean }[] = [];\r\n  \r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private workflowService: WorkflowService,\r\n    private tokenStorage: TokenStorageService,\r\n    private loaderService: LoaderService,\r\n    private formBuilder: FormBuilder,\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loaderService.disableLoader();\r\n    this.selectedTab = 'Agent Activity';\r\n    this.executionId = crypto.randomUUID();\r\n    // Get workflow ID from route params\r\n    this.route.paramMap.pipe(takeUntil(this.destroy$)).subscribe((params) => {\r\n      this.workflowId = params.get('id');\r\n      if (this.workflowId) {\r\n        this.loadWorkflow(this.workflowId);\r\n      } else {\r\n        // No workflow ID, redirect back to workflows page\r\n        this.router.navigate(['/build/workflows']);\r\n      }\r\n    });\r\n    // this.executeWorkflow()\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n    this.loaderService.enableLoader();\r\n  }\r\n  onTabChange(event: { id: string, label: string }) {\r\n    this.selectedTab = event.label;\r\n    this.activeTabId = event.id;\r\n    console.log('Tab changed:', event);\r\n  }\r\n\r\n  // Load workflow data\r\n  loadWorkflow(id: string): void {\r\n    // In a real app, this would fetch the workflow from a service\r\n    console.log(`Loading workflow with ID: ${id}`);\r\n    this.chatMessages = [\r\n      {\r\n        from: 'ai',\r\n        text: 'I am your workflow assistant. I will help you in executing this workflow.',\r\n      } as ChatMessage,\r\n    ]\r\n    this.workflowForm = this.formBuilder.group({});\r\n\r\n    this.workflowService.getWorkflowById(id).subscribe({\r\n        next: (res) => {\r\n        this.workflowAgents = res.workflowAgents;\r\n        this.userInputList = this.extractInputField(this.workflowAgents);\r\n        if(this.userInputList.length === 0){\r\n          this.disableChat = true;\r\n        }\r\n        this.workflowName = res.name;\r\n        this.initializeForm();\r\n        this.initializeAgentStates();\r\n        this.startInputCollection();\r\n      },\r\n        error: (err) => {\r\n          // Fallback to demo data for testing\r\n          this.loadDemoWorkflow();\r\n          this.disableChat = true;\r\n          this.chatInterfaceComp.addAiResponse('No input is required for this workflow. Click on send button to execute the workflow.');\r\n          console.log(err);\r\n        }\r\n    });\r\n\r\n  }\r\n\r\n  public extractInputField(pipeLineAgents: any) {\r\n    const PLACEHOLDER_PATTERNS =  /(%\\d+\\$s)|\\{\\{([a-zA-Z0-9-_]+)\\}\\}/g;\r\n    const placeholderMap: { [key: string]: { agents: Set<string>; inputs: Set<string> } } = {};\r\n\r\n    pipeLineAgents.forEach((agent: any) => {\r\n      const agentName = agent?.agentDetails?.name;\r\n      const agentDescription = agent?.agentDetails?.description;\r\n      const matches = agentDescription.matchAll(PLACEHOLDER_PATTERNS) || [];\r\n\r\n      for (const match of matches) { \r\n        const placeholder = match[1] || match[2];\r\n        const placeholderInput = match[0];\r\n        if (!placeholderMap[placeholder]) {\r\n          placeholderMap[placeholder] = { agents: new Set(), inputs: new Set() };;\r\n        }\r\n        placeholderMap[placeholder].agents.add(agentName);\r\n        placeholderMap[placeholder].inputs.add(placeholderInput);\r\n      }\r\n    })\r\n\r\n    return Object.entries(placeholderMap).map(([placeholder, { agents, inputs }]) => ({\r\n      name: [...agents].length > 2\r\n        ? `${[...agents].slice(0, -1).join(\", \")} and ${[...agents].at(-1)}`\r\n        : [...agents].join(\" and \"),\r\n      placeholder,\r\n      input: [...inputs][0],\r\n    }));\r\n  }\r\n\r\n  public isImageInput(input: string): boolean {\r\n    const match = input.match(/{{(.*?)}}/);\r\n    if (match && match[1]) {\r\n      const variableName = match[1].trim();\r\n      return variableName.startsWith('image') || variableName.startsWith('Image');\r\n    }\r\n    return false;\r\n  }\r\n\r\n  public initializeForm() {   \r\n    this.userInputList.forEach((label: any) => {\r\n      this.workflowForm.addControl(label.input, this.formBuilder.control('', Validators.required));\r\n    })\r\n  }\r\n\r\n  public isInputValid() {\r\n    return this.workflowForm.valid && this.workflowId;\r\n  }\r\n\r\n  startFakeProgress() {\r\n    this.progress = 0;\r\n    this.progressInterval = setInterval(() => {\r\n      if (this.progress < 90) {\r\n        this.progress += 5; // Increase slowly\r\n      }\r\n    }, 200); // Adjust speed\r\n  }\r\n\r\n  stopFakeProgress() {\r\n    clearInterval(this.progressInterval);\r\n    this.progress = 100;\r\n\r\n    setTimeout(() => {\r\n      this.isLoading = false;\r\n    }, 500); // Small delay to let user see 100%\r\n  }\r\n\r\n  // Handle new chat message from user\r\n  handleChatMessage(message: string): void {\r\n    // console.log('message ', message, 'is blank', message.trim() === '');\r\n    this.isProcessingChat = true;\r\n    if(message.trim() === ''){\r\n      if(this.inputFieldOrder.length === 0){\r\n        this.chatInterfaceComp.addAiResponse('Executing the workflow...');\r\n        this.executeWorkflow();\r\n      }\r\n      return;\r\n    }\r\n\r\n    if(this.isExecutionComplete || this.currentInputIndex===this.inputFieldOrder.length){\r\n        this.chatInterfaceComp.addAiResponse('Executing the workflow...');\r\n        this.executeWorkflow();\r\n      return;\r\n    }\r\n\r\n    const field = this.inputFieldOrder[this.currentInputIndex];\r\n    if (this.isImageInput(field)) {\r\n      // Ignore text input, wait for file input\r\n      this.chatInterfaceComp.addAiResponse(`Please upload an image file for ${field}`);\r\n      return;\r\n    }\r\n\r\n    this.workflowForm.get(field)?.setValue(message);\r\n    this.currentInputIndex++;\r\n\r\n    if (this.currentInputIndex < this.inputFieldOrder.length) {\r\n      this.promptForCurrentField();\r\n    } else {\r\n      this.chatInterfaceComp.addAiResponse('Thank you for the input! Executing the workflow...');\r\n      this.executeWorkflow();\r\n    }\r\n  }\r\n\r\n  // Save execution logs\r\n  saveLogs(): void {\r\n    console.log('Saving execution logs...');\r\n    // This would typically save to a service\r\n  }\r\n\r\n  // Export results\r\n  exportResults(section: 'activity' | 'output'): void {\r\n    console.log(`Exporting ${section} data...`);\r\n\r\n    if (section === 'activity') {\r\n      const data = this.activityLogs\r\n        .map((log) => `[${log.timestamp}] ${log.message}`)\r\n        .join('\\n');\r\n      this.downloadAsFile(data, 'workflow-activity-logs.txt', 'text/plain');\r\n    } else {\r\n      const data = JSON.stringify(this.agentOutputs, null, 2);\r\n      this.downloadAsFile(data, 'workflow-outputs.json', 'application/json');\r\n    }\r\n  }\r\n\r\n  // Helper method to download data as a file\r\n  private downloadAsFile(data: string, filename: string, type: string): void {\r\n    const blob = new Blob([data], { type });\r\n    const url = URL.createObjectURL(blob);\r\n    const link = document.createElement('a');\r\n    link.href = url;\r\n    link.download = filename;\r\n    link.click();\r\n    URL.revokeObjectURL(url);\r\n  }\r\n\r\n  // Handle controls for execution\r\n  handleControlAction(action: 'play' | 'pause' | 'stop'): void {\r\n    console.log(`Control action: ${action}`);\r\n    // In a real app, this would control the workflow execution\r\n\r\n    if (action === 'play') {\r\n      this.isRunning = true;\r\n    } else if (action === 'pause' || action === 'stop') {\r\n      this.isRunning = false;\r\n    }\r\n  }\r\n\r\n  // Navigate back to workflow listing\r\n  navigateBack(): void {\r\n    this.router.navigate(['/build/workflows']);\r\n  }\r\n\r\n  // Navigate to edit workflow\r\n  editWorkflow(): void {\r\n    if (this.workflowId) {\r\n      this.router.navigate(['/build/workflows/edit', this.workflowId]);\r\n    }\r\n  }\r\n\r\n  public logExecutionStatus(delay: number = 2000) {\r\n    setTimeout(() => {\r\n      if (!this.isExecutionComplete) {\r\n        console.log(this.constants);\r\n        console.log(this.constants['labels'].workflowExecProcessing);\r\n        this.workflowLogs.push({\r\n          content: this.constants['labels'].workflowExecProcessing,\r\n          color: '#F9DB24',\r\n        });\r\n      }\r\n    }, delay);\r\n  }\r\n\r\n  // public parseAnsiString(ansiString: string) {\r\n  //   const regex = ansiRegex();\r\n  //   const parts = ansiString.split(regex);\r\n  //   const matches = [...ansiString.matchAll(regex)];\r\n  //   parts.forEach((part, index) => {\r\n  //     if (part.trim() !== '') {\r\n  //       let colorCode = matches[index - 1][0];\r\n  //       if (index - 2 >= 0 && matches[index - 2]?.includes('\\u001b[1m')) {\r\n  //         colorCode = `\\u001b[1m${colorCode}`;\r\n  //       }\r\n  //       this.workflowLogs.push({\r\n  //         content: part,\r\n  //         color: this.colorMap[colorCode] || 'white',\r\n  //       });\r\n  //     }\r\n  //   });\r\n  // }\r\n\r\n  public getWorkflowLogs(executionId: string) {\r\n    this.workflowService\r\n      .workflowLogConnect(executionId)\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (message) => {\r\n          console.log('message: ', message);\r\n          const { content, color } = message;\r\n          if (color) {\r\n            this.workflowLogs.push({ content, color });\r\n          } else if (this.enableStreamingLog === 'all') {\r\n            // this.parseAnsiString(content);\r\n          }\r\n        },\r\n        error: (err) => {\r\n          this.workflowLogs.push({\r\n            content: this.constants['workflowLog'],\r\n            color: 'red',\r\n          });\r\n          console.error('WebSocket error:', err);\r\n        },\r\n        complete: () => {\r\n          this.logExecutionStatus();\r\n          console.log('WebSocket connection closed');\r\n        },\r\n      });\r\n  }\r\n\r\n  // public parseAnsiString(ansiString: string) {\r\n  //   const regex = ansiRegex();\r\n  //   const parts = ansiString.split(regex);\r\n  //   const matches = [...ansiString.matchAll(regex)];\r\n  //   parts.forEach((part, index) => {\r\n  //     if (part.trim() !== '') {\r\n  //       let colorCode = matches[index-1][0];\r\n  //       if(index - 2 >= 0 && matches[index-2]?.includes('\\u001b[1m')) {\r\n  //         colorCode = `\\u001b[1m${colorCode}`;\r\n  //       }\r\n  //       this.workflowLogs.push({\r\n  //         content: part, \r\n  //         color: this.colorMap[colorCode] || 'white', \r\n  //       });\r\n  //     }\r\n  //   });\r\n  // }\r\n\r\n  public validateJson(output: string): any | null {\r\n    this.isJsonValid = false;\r\n    try {\r\n      const parsedOutput = JSON.parse(output);\r\n      this.isJsonValid = true;\r\n      return parsedOutput;\r\n    } catch (e) {\r\n      return null;\r\n    }\r\n  }\r\n\r\n  public executeWorkflow() {\r\n    let payload: FormData | Record<string, any> = new FormData();\r\n    let queryString = '';\r\n\r\n\r\n    this.status = ExecutionStatus.running;\r\n    if (this.selectedFiles.length) {\r\n      this.selectedFiles.forEach((file) => {\r\n        payload.append('files', file);\r\n      });\r\n      payload.append('workflowId', this.workflowId);\r\n      payload.append('userInputs', JSON.stringify(this.workflowForm.value));\r\n      payload.append('user', this.tokenStorage.getDaUsername());\r\n      payload.append('executionId', this.executionId);\r\n      queryString = '/files';\r\n    } else {\r\n      payload = {\r\n        pipeLineId: this.workflowId,\r\n        userInputs: this.workflowForm.value,\r\n        executionId: this.executionId,\r\n        user: this.tokenStorage.getDaUsername(),\r\n      };\r\n    }\r\n\r\n    this.getWorkflowLogs(this.executionId);\r\n    this.startFakeProgress();\r\n\r\n    this.workflowService\r\n      .executeWorkflow(payload, queryString)\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (res) => {\r\n          this.isProcessingChat = false;\r\n          this.isRunning = false;\r\n          this.chatInterfaceComp.addAiResponse(res?.message || \"Workflow execution completed successfully!\");\r\n\r\n          if (res?.workflowResponse?.pipeline?.output) {\r\n            this.isExecutionComplete = true;\r\n            // console.log(this.constants['labels'].workflowExecComplete);\r\n            this.workflowLogs.push({\r\n              content: this.constants['labels'].workflowExecComplete,\r\n              color: '#0F8251',\r\n            });\r\n            this.errorMsg = false;\r\n            this.resMessage = res?.workflowResponse?.pipeline?.output;\r\n            this.agentOutputs = res?.workflowResponse?.pipeline?.tasksOutputs.map((task: any) => {\r\n              return {\r\n                id: task?.id || '',\r\n                title: task?.title || '',\r\n                content: task?.content || '',\r\n                agentName: task?.agentName || '',\r\n                timestamp: task?.timestamp || '',\r\n                type: task?.type || '',\r\n                description: task?.description || '',\r\n                expected_output: task?.expected_output || '',\r\n                summary: task?.summary || '',\r\n                raw: task?.raw || '',\r\n              };\r\n            })\r\n\r\n            this.taskMessage = res?.workflowResponse?.pipeline?.tasksOutputs.map(\r\n              (task: {\r\n                description: any;\r\n                summary: any;\r\n                raw: any;\r\n                expected_output: any;\r\n              }) => {\r\n                return {\r\n                  description: task.description,\r\n                  summary: task.summary,\r\n                  raw: task.raw,\r\n                  expected_output: task.expected_output,\r\n                };\r\n              },\r\n            );\r\n\r\n            // if(\"file_download_url\" in res?.pipeline){\r\n            //   this.isFileWriter = true;\r\n            //   this.fileDownloadLink = res?.pipeline?.file_download_url;\r\n\r\n            //   if(!this.fileDownloadLink){\r\n            //     this.fileDownloadUrlError = [];\r\n            //     this.fileDownloadUrlError.push(\"Output file is not generated yet!\")\r\n            //   }\r\n            // }\r\n            // this.isAccordian = true\r\n          }\r\n          this.validateJson(this.resMessage);\r\n          this.status = ExecutionStatus.completed;\r\n          this.stopFakeProgress();\r\n          this.selectedFiles = [];\r\n        },\r\n        error: (error) => {\r\n          this.isExecutionComplete = true;\r\n          this.isProcessingChat = false;\r\n          this.errorMsg = true;\r\n          this.resMessage = error?.error?.detail;\r\n          this.workflowService.workflowLogDisconnect();\r\n          this.workflowLogs.push({\r\n            content: this.constants['labels'].workflowLogFailed,\r\n            color: 'red',\r\n          });\r\n          this.chatInterfaceComp.addAiResponse(\r\n            'Something went wrong, Workflow execution has failed.',\r\n          );\r\n          this.selectedFiles = [];\r\n          this.stopFakeProgress();\r\n          console.log('error is', error.message);\r\n        },\r\n      });\r\n  }\r\n\r\n  // public asyncExecutePipeline() {\r\n  //   const payload: FormData = new FormData();\r\n  //   if (this.selectedFiles?.length) {\r\n  //     for (const element of this.selectedFiles) {\r\n  //       payload.append('files', element)\r\n  //     }\r\n  //   }\r\n  //   payload.append('pipeLineId', String(this.workflowId));\r\n  //   payload.append('userInputs', JSON.stringify(this.workflowForm.value));\r\n  //   payload.append('user', this.tokenStorage.getDaUsername() || '');\r\n  //   payload.append('executionId', this.executionId);\r\n\r\n  //   this.workflowService.asyncExecutePipeline(payload).pipe().subscribe({\r\n  //     next: (res: any) => {\r\n  //       if(res) {\r\n  //         // res handling\r\n  //         console.log(res);\r\n  //       }\r\n  //     },\r\n  //     error: e => {\r\n  //       // error handling\r\n  //       console.log(e);\r\n  //     }\r\n  //   })\r\n  // }\r\n\r\n  handleAttachment() {\r\n    console.log('handleAttachment');\r\n  }\r\n\r\n  onAttachmentsSelected(files: File[]) {\r\n    if(this.currentInputIndex===this.inputFieldOrder.length || this.inputFieldOrder.length===0){\r\n      this.selectedFiles = files;\r\n      return;\r\n    }\r\n\r\n    const field = this.inputFieldOrder[this.currentInputIndex];\r\n    if(this.isImageInput(field)){\r\n      if (files && files.length > 0) {\r\n        this.onImageSelected(files[0]);\r\n      }\r\n    } else {\r\n      this.selectedFiles = files;\r\n    }\r\n  }\r\n\r\n  startInputCollection(){\r\n    this.inputFieldOrder = Object.keys(this.workflowForm.controls);\r\n    this.currentInputIndex = 0;\r\n    if (this.inputFieldOrder.length > 0) {\r\n      this.promptForCurrentField();\r\n    }\r\n    else{\r\n      this.disableChat = true;\r\n      this.chatInterfaceComp.addAiResponse('No input is required for this workflow. Click on send button to execute the workflow.');\r\n    }\r\n  }\r\n\r\n  promptForCurrentField() {\r\n    const field = this.inputFieldOrder[this.currentInputIndex];\r\n    if (this.isImageInput(field)) {\r\n      this.fileType = '.jpeg,.png,.jpg,.svg';\r\n      this.chatInterfaceComp.addAiResponse(`Please upload an image for ${field}`);\r\n      // UI should now show a file input for the user\r\n    } else {\r\n      this.fileType = '.zip'; // or whatever default you want for non-image\r\n      this.chatInterfaceComp.addAiResponse(`Please enter the value of ${field}`);\r\n    }\r\n  }\r\n\r\n  onImageSelected(file: File) {\r\n    const field = this.inputFieldOrder[this.currentInputIndex];\r\n    if (!this.isImageInput(field)) return;\r\n\r\n    const reader = new FileReader();\r\n    reader.onload = () => {\r\n      const base64String = (reader.result as string); \r\n      this.workflowForm.get(field)?.setValue(base64String);\r\n      this.currentInputIndex++;\r\n      if (this.currentInputIndex < this.inputFieldOrder.length) {\r\n        this.promptForCurrentField();\r\n      } else {\r\n        this.chatInterfaceComp.addAiResponse('Thank you! Running the workflow...');\r\n        this.executeWorkflow();\r\n      }\r\n    };\r\n    reader.readAsDataURL(file);\r\n  }\r\n\r\n  // Panel management methods\r\n  toggleLeftPanel(): void {\r\n    this.isLeftPanelCollapsed = !this.isLeftPanelCollapsed;\r\n  }\r\n\r\n  toggleAgentExpansion(index: number): void {\r\n    this.expandedAgents[index] = !this.expandedAgents[index];\r\n  }\r\n\r\n  initializeAgentStates(): void {\r\n    this.expandedAgents = new Array(this.workflowAgents.length).fill(false);\r\n    this.agentStatuses = this.workflowAgents.map(() => ({ completed: false }));\r\n  }\r\n\r\n  // Extract input fields method (similar to pipeline component)\r\n  public extractInputField(workflowAgents: any[]) {\r\n    const PLACEHOLDER_PATTERNS = /(%\\d+\\$s)|\\{\\{([a-zA-Z0-9-_]+)\\}\\}/g;\r\n    const placeholderMap: { [key: string]: { agents: Set<string>; inputs: Set<string> } } = {};\r\n\r\n    workflowAgents.forEach((agent: any) => {\r\n      const agentName = agent?.name || `Agent ${workflowAgents.indexOf(agent) + 1}`;\r\n      const agentDescription = agent?.description || agent?.task?.description || '';\r\n      const matches = agentDescription.matchAll(PLACEHOLDER_PATTERNS) || [];\r\n\r\n      for (const match of matches) {\r\n        const placeholder = match[1] || match[2];\r\n        const placeholderInput = match[0];\r\n        if (!placeholderMap[placeholder]) {\r\n          placeholderMap[placeholder] = { agents: new Set(), inputs: new Set() };\r\n        }\r\n        placeholderMap[placeholder].agents.add(agentName);\r\n        placeholderMap[placeholder].inputs.add(placeholderInput);\r\n      }\r\n    });\r\n\r\n    return Object.entries(placeholderMap).map(([placeholder, { agents, inputs }]) => ({\r\n      name: [...agents].length > 2\r\n        ? `${[...agents].slice(0, -1).join(\", \")} and ${[...agents].at(-1)}`\r\n        : [...agents].join(\" and \"),\r\n      placeholder,\r\n      input: [...inputs][0],\r\n    }));\r\n  }\r\n\r\n  // Input validation\r\n  isInputValid(): boolean {\r\n    return this.workflowForm.valid && this.workflowId;\r\n  }\r\n}\r\n", "<div class=\"workflow-execution-container\">\r\n  <!-- SVG Gradient Definitions for Icons -->\r\n  <svg width=\"0\" height=\"0\" style=\"position: absolute\">\r\n    <defs>\r\n      <linearGradient id=\"gradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\r\n        <stop offset=\"0%\" stop-color=\"#6566CD\" />\r\n        <stop offset=\"100%\" stop-color=\"#F96CAB\" />\r\n      </linearGradient>\r\n    </defs>\r\n  </svg>\r\n  <div class=\"execution-content\" role=\"main\">\r\n    <!-- Left Panel: Workflow Execution Panel -->\r\n    <div\r\n      class=\"column execution-panel-column\"\r\n      [class.collapsed]=\"isLeftPanelCollapsed\"\r\n      role=\"region\"\r\n      aria-label=\"Workflow Execution Panel\"\r\n    >\r\n      <div class=\"column-content execution-panel-content\">\r\n        <div class=\"column-header\">\r\n          <div class=\"header-left\">\r\n            <ava-icon\r\n              iconName=\"arrowLeft\"\r\n              iconColor=\"#1A46A7\"\r\n              class=\"back-icon\"\r\n              (click)=\"navigateBack()\"\r\n            ></ava-icon>\r\n            <ava-icon\r\n              iconName=\"panelLeft\"\r\n              iconColor=\"#1A46A7\"\r\n              class=\"panel-icon\"\r\n              (click)=\"toggleLeftPanel()\"\r\n            ></ava-icon>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"panel-content\" [class.hidden]=\"isLeftPanelCollapsed\">\r\n          <!-- Agent Cards -->\r\n          <div class=\"agents-section\">\r\n            <ng-container *ngFor=\"let agent of workflowAgents; let i = index\">\r\n              <div class=\"agent-card\" [class.completed]=\"agentStatuses[i]?.completed\">\r\n                <div class=\"agent-header\">\r\n                  <div class=\"status-icon\">\r\n                    <ava-icon\r\n                      [iconName]=\"agentStatuses[i]?.completed ? 'check' : 'folder'\"\r\n                      [iconColor]=\"agentStatuses[i]?.completed ? '#22C55E' : '#1A46A7'\"\r\n                    ></ava-icon>\r\n                  </div>\r\n                  <div class=\"agent-info\">\r\n                    <h3 class=\"agent-name\">{{ agent?.name || 'Agent ' + (i + 1) }}</h3>\r\n                    <ava-icon\r\n                      iconName=\"chevronDown\"\r\n                      iconColor=\"#6B7280\"\r\n                      class=\"expand-icon\"\r\n                      [class.expanded]=\"expandedAgents[i]\"\r\n                      (click)=\"toggleAgentExpansion(i)\"\r\n                    ></ava-icon>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Agent Details (Expandable) -->\r\n                <div class=\"agent-details\" [class.expanded]=\"expandedAgents[i]\">\r\n                  <div class=\"agent-detail-item\" *ngIf=\"agent?.model\">\r\n                    <span class=\"detail-label\">Model:</span>\r\n                    <span class=\"detail-value\">{{ agent.model }}</span>\r\n                  </div>\r\n                  <div class=\"agent-detail-item\" *ngIf=\"agent?.tools?.length\">\r\n                    <span class=\"detail-label\">Tools:</span>\r\n                    <span class=\"detail-value\">{{ agent.tools.join(', ') }}</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Connection Line -->\r\n              <div class=\"connection-line\" *ngIf=\"i < workflowAgents.length - 1\"></div>\r\n            </ng-container>\r\n          </div>\r\n\r\n          <!-- Input Section -->\r\n          <div class=\"input-section\" *ngIf=\"userInputList.length > 0\">\r\n            <div class=\"input-header\">\r\n              <div class=\"status-icon\">\r\n                <ava-icon\r\n                  iconName=\"check\"\r\n                  iconColor=\"#22C55E\"\r\n                ></ava-icon>\r\n              </div>\r\n              <h3 class=\"input-title\">Enter the input</h3>\r\n            </div>\r\n\r\n            <div class=\"input-container\" [formGroup]=\"workflowForm\">\r\n              <ng-container *ngFor=\"let input of userInputList; let i = index\">\r\n                <div class=\"input-field\">\r\n                  <label class=\"input-label\">Input for {{ input.input }} in {{ input.name }}</label>\r\n                  <textarea\r\n                    [formControlName]=\"input.input\"\r\n                    [placeholder]=\"'Input Entered'\"\r\n                    class=\"input-textarea\"\r\n                  ></textarea>\r\n                  <div class=\"input-actions\">\r\n                    <ava-icon iconName=\"paperclip\" iconColor=\"#6B7280\" class=\"attach-icon\"></ava-icon>\r\n                    <ava-icon iconName=\"edit\" iconColor=\"#6B7280\" class=\"edit-icon\"></ava-icon>\r\n                    <ava-icon iconName=\"send\" iconColor=\"#1A46A7\" class=\"send-icon\"></ava-icon>\r\n                  </div>\r\n                </div>\r\n              </ng-container>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Info Message -->\r\n          <div class=\"info-message\">\r\n            <p>Agent input needed in order to execute Workflow</p>\r\n          </div>\r\n\r\n          <!-- Execute Button -->\r\n          <div class=\"execute-section\">\r\n            <ava-button\r\n              label=\"▶ Execute Agent\"\r\n              variant=\"primary\"\r\n              size=\"large\"\r\n              [disabled]=\"!isInputValid()\"\r\n              (click)=\"executeWorkflow()\"\r\n              [customStyles]=\"{\r\n                'background': '#1A46A7',\r\n                'width': '100%',\r\n                'border-radius': '8px'\r\n              }\"\r\n            ></ava-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Right Column: Agent Output -->\r\n    <div class=\"column output-column\" role=\"region\" aria-label=\"Agent Output\">\r\n      <div class=\"column-content\">\r\n        <div class=\"column-header row\">\r\n          <div class=\"col-7\">\r\n            <ava-tabs\r\n              [tabs]=\"navigationTabs\"\r\n              [activeTabId]=\"activeTabId\"\r\n              variant=\"button\"\r\n              buttonShape=\"pill\"\r\n              [showContentPanels]=\"false\"\r\n              (tabChange)=\"onTabChange($event)\"\r\n              ariaLabel=\"Pill navigation tabs\"\r\n            ></ava-tabs>\r\n          </div>\r\n          <div class=\"col-5 right-section-header\">\r\n            <ava-button\r\n              label=\"Send for Approval\"\r\n              variant=\"primary\"\r\n              [customStyles]=\"{\r\n                background:\r\n                  'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n                '--button-effect-color': '33, 90, 214',\r\n                'border-radius': '8px',\r\n                'box-shadow': 'none',\r\n              }\"\r\n              size=\"medium\"\r\n            >\r\n            </ava-button>\r\n            <!-- <ava-button\r\n              label=\"Export\"\r\n              variant=\"secondary\"\r\n              size=\"medium\"\r\n              class=\"ms-2\"\r\n            >\r\n            </ava-button> -->\r\n          </div>\r\n        </div>\r\n        <!-- activity content -->\r\n        <div *ngIf=\"selectedTab === 'Agent Activity'\" style=\"height: 100%\">\r\n          <app-agent-activity\r\n            [activityLogs]=\"workflowLogs\"\r\n            [executionDetails]=\"executionDetails\"\r\n            [progress]=\"progress\"\r\n            [isRunning]=\"isRunning\"\r\n            (saveLogs)=\"saveLogs()\"\r\n            (controlAction)=\"handleControlAction($event)\"\r\n            [status]=\"status\"\r\n            (onOutPutBtnClick)=\"onTabChange({id: 'nav-products', label: 'Agent Output'})\"\r\n          ></app-agent-activity>\r\n        </div>\r\n        <!-- Agent Output Component -->\r\n        <div *ngIf=\"selectedTab === 'Agent Output'\" style=\"height: 100%\">\r\n          <app-agent-output\r\n            [outputs]=\"taskMessage\"\r\n            (export)=\"exportResults('output')\"\r\n          ></app-agent-output>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;AACzC,SAAiCC,WAAW,EAAEC,mBAAmB,EAAEC,UAAU,QAAQ,gBAAgB;AAErG;AACA,SAASC,sBAAsB,QAAQ,4DAA4D;AAEnG,SACEC,sBAAsB,QACjB,sDAAsD;AAC7D,SACEC,oBAAoB,QAEf,kDAAkD;AACzD,SACEC,eAAe,EACfC,aAAa,EAEbC,aAAa,QACR,wBAAwB;AAE/B,SAASC,WAAW,QAAQ,kCAAkC;AAC9D,OAAOC,iBAAiB,MAAM,+BAA+B;AAG7D,SAASC,eAAe,QAAqB,gCAAgC;;;;;;;;;;;;;;;;;;;;ICoCzDC,EADF,CAAAC,cAAA,cAAoD,eACvB;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxCH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAC9CF,EAD8C,CAAAG,YAAA,EAAO,EAC/C;;;;IADuBH,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAK,iBAAA,CAAAC,QAAA,CAAAC,KAAA,CAAiB;;;;;IAG5CP,EADF,CAAAC,cAAA,cAA4D,eAC/B;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxCH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,GAA4B;IACzDF,EADyD,CAAAG,YAAA,EAAO,EAC1D;;;;IADuBH,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAK,iBAAA,CAAAC,QAAA,CAAAE,KAAA,CAAAC,IAAA,OAA4B;;;;;IAM7DT,EAAA,CAAAU,SAAA,cAAyE;;;;;;IAnC3EV,EAAA,CAAAW,uBAAA,GAAkE;IAG5DX,EAFJ,CAAAC,cAAA,cAAwE,cAC5C,cACC;IACvBD,EAAA,CAAAU,SAAA,mBAGY;IACdV,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAAwB,aACC;IAAAD,EAAA,CAAAE,MAAA,GAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnEH,EAAA,CAAAC,cAAA,mBAMC;IADCD,EAAA,CAAAY,UAAA,mBAAAC,8EAAA;MAAA,MAAAC,IAAA,GAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA,EAAAC,KAAA;MAAA,MAAAC,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASF,MAAA,CAAAG,oBAAA,CAAAP,IAAA,CAAuB;IAAA,EAAC;IAGvCd,EAFK,CAAAG,YAAA,EAAW,EACR,EACF;IAGNH,EAAA,CAAAC,cAAA,cAAgE;IAK9DD,EAJA,CAAAsB,UAAA,KAAAC,0DAAA,kBAAoD,KAAAC,0DAAA,kBAIQ;IAKhExB,EADE,CAAAG,YAAA,EAAM,EACF;IAGNH,EAAA,CAAAsB,UAAA,KAAAG,0DAAA,kBAAmE;;;;;;;IAlC3CzB,EAAA,CAAAI,SAAA,EAA+C;IAA/CJ,EAAA,CAAA0B,WAAA,cAAAR,MAAA,CAAAS,aAAA,CAAAb,IAAA,mBAAAI,MAAA,CAAAS,aAAA,CAAAb,IAAA,EAAAc,SAAA,CAA+C;IAI/D5B,EAAA,CAAAI,SAAA,GAA6D;IAC7DJ,EADA,CAAA6B,UAAA,cAAAX,MAAA,CAAAS,aAAA,CAAAb,IAAA,mBAAAI,MAAA,CAAAS,aAAA,CAAAb,IAAA,EAAAc,SAAA,uBAA6D,eAAAV,MAAA,CAAAS,aAAA,CAAAb,IAAA,mBAAAI,MAAA,CAAAS,aAAA,CAAAb,IAAA,EAAAc,SAAA,0BACI;IAI5C5B,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAK,iBAAA,EAAAC,QAAA,kBAAAA,QAAA,CAAAwB,IAAA,iBAAAhB,IAAA,MAAuC;IAK5Dd,EAAA,CAAAI,SAAA,EAAoC;IAApCJ,EAAA,CAAA0B,WAAA,aAAAR,MAAA,CAAAa,cAAA,CAAAjB,IAAA,EAAoC;IAOfd,EAAA,CAAAI,SAAA,EAAoC;IAApCJ,EAAA,CAAA0B,WAAA,aAAAR,MAAA,CAAAa,cAAA,CAAAjB,IAAA,EAAoC;IAC7Bd,EAAA,CAAAI,SAAA,EAAkB;IAAlBJ,EAAA,CAAA6B,UAAA,SAAAvB,QAAA,kBAAAA,QAAA,CAAAC,KAAA,CAAkB;IAIlBP,EAAA,CAAAI,SAAA,EAA0B;IAA1BJ,EAAA,CAAA6B,UAAA,SAAAvB,QAAA,kBAAAA,QAAA,CAAAE,KAAA,kBAAAF,QAAA,CAAAE,KAAA,CAAAwB,MAAA,CAA0B;IAQhChC,EAAA,CAAAI,SAAA,EAAmC;IAAnCJ,EAAA,CAAA6B,UAAA,SAAAf,IAAA,GAAAI,MAAA,CAAAe,cAAA,CAAAD,MAAA,KAAmC;;;;;IAiBjEhC,EAAA,CAAAW,uBAAA,GAAiE;IAE7DX,EADF,CAAAC,cAAA,cAAyB,gBACI;IAAAD,EAAA,CAAAE,MAAA,GAA+C;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAClFH,EAAA,CAAAU,SAAA,mBAIY;IACZV,EAAA,CAAAC,cAAA,cAA2B;IAGzBD,EAFA,CAAAU,SAAA,mBAAkF,mBACP,mBACA;IAE/EV,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IAXuBH,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAAkC,kBAAA,eAAAC,QAAA,CAAAC,KAAA,UAAAD,QAAA,CAAAL,IAAA,KAA+C;IAExE9B,EAAA,CAAAI,SAAA,EAA+B;IAC/BJ,EADA,CAAA6B,UAAA,oBAAAM,QAAA,CAAAC,KAAA,CAA+B,gCACA;;;;;IAfrCpC,EAFJ,CAAAC,cAAA,cAA4D,cAChC,cACC;IACvBD,EAAA,CAAAU,SAAA,mBAGY;IACdV,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IACzCF,EADyC,CAAAG,YAAA,EAAK,EACxC;IAENH,EAAA,CAAAC,cAAA,cAAwD;IACtDD,EAAA,CAAAsB,UAAA,IAAAe,yDAAA,2BAAiE;IAgBrErC,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAjByBH,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAA6B,UAAA,cAAAX,MAAA,CAAAoB,YAAA,CAA0B;IACrBtC,EAAA,CAAAI,SAAA,EAAkB;IAAlBJ,EAAA,CAAA6B,UAAA,YAAAX,MAAA,CAAAqB,aAAA,CAAkB;;;;;;IAkFtDvC,EADF,CAAAC,cAAA,cAAmE,6BAUhE;IADCD,EAHA,CAAAY,UAAA,sBAAA4B,kFAAA;MAAAxC,EAAA,CAAAe,aAAA,CAAA0B,GAAA;MAAA,MAAAvB,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAYF,MAAA,CAAAwB,QAAA,EAAU;IAAA,EAAC,2BAAAC,uFAAAC,MAAA;MAAA5C,EAAA,CAAAe,aAAA,CAAA0B,GAAA;MAAA,MAAAvB,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CACNF,MAAA,CAAA2B,mBAAA,CAAAD,MAAA,CAA2B;IAAA,EAAC,8BAAAE,0FAAA;MAAA9C,EAAA,CAAAe,aAAA,CAAA0B,GAAA;MAAA,MAAAvB,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAEzBF,MAAA,CAAA6B,WAAA,CAAY;QAAAC,EAAA,EAAK,cAAc;QAAAC,KAAA,EAAS;MAAc,CAAC,CAAC;IAAA,EAAC;IAEjFjD,EADG,CAAAG,YAAA,EAAqB,EAClB;;;;IATFH,EAAA,CAAAI,SAAA,EAA6B;IAM7BJ,EANA,CAAA6B,UAAA,iBAAAX,MAAA,CAAAgC,YAAA,CAA6B,qBAAAhC,MAAA,CAAAiC,gBAAA,CACQ,aAAAjC,MAAA,CAAAkC,QAAA,CAChB,cAAAlC,MAAA,CAAAmC,SAAA,CACE,WAAAnC,MAAA,CAAAoC,MAAA,CAGN;;;;;;IAMnBtD,EADF,CAAAC,cAAA,cAAiE,2BAI9D;IADCD,EAAA,CAAAY,UAAA,oBAAA2C,8EAAA;MAAAvD,EAAA,CAAAe,aAAA,CAAAyC,GAAA;MAAA,MAAAtC,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAUF,MAAA,CAAAuC,aAAA,CAAc,QAAQ,CAAC;IAAA,EAAC;IAEtCzD,EADG,CAAAG,YAAA,EAAmB,EAChB;;;;IAHFH,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAA6B,UAAA,YAAAX,MAAA,CAAAwC,WAAA,CAAuB;;;AD3InC,WAAaC,0BAA0B;EAAjC,MAAOA,0BAA0B;IA0E3BC,KAAA;IACAC,MAAA;IACAC,eAAA;IACAC,YAAA;IACAC,aAAA;IACAC,WAAA;IA9EVC,cAAc,GAAc,CAC1B;MAAElB,EAAE,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAgB,CAAE,EAC3C;MAAED,EAAE,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAc,CAAE,EAC7C;MAAED,EAAE,EAAE,cAAc;MAAEC,KAAK,EAAE,SAAS;MAAEkB,QAAQ,EAAE;IAAI,CAAE,CACzD;IACD;IACAC,UAAU,GAAkB,IAAI;IAChCC,YAAY,GAAW,UAAU;IAEjCC,SAAS,GAAGxE,iBAAwC;IAGpDyE,iBAAiB;IAEjB;IACAC,YAAY,GAAkB,EAAE;IAChCC,gBAAgB,GAAW,CAAC;IAC5BtB,gBAAgB;IAChBE,SAAS,GAAY,KAAK;IAC1BC,MAAM,GAAoBvD,eAAe,CAAC2E,UAAU;IAEpD;IACAC,YAAY,GAAkB,EAAE;IAChCC,gBAAgB,GAAY,KAAK;IACjCC,SAAS,GAAG,EAAE;IAEd;IACAC,YAAY,GAAiB,EAAE;IACxBxC,YAAY;IACZyC,QAAQ,GAAY,MAAM;IAEjC;IACAC,kBAAkB,GAAgB,IAAI;IACtCC,kBAAkB,GAAY,KAAK;IACnCC,WAAW;IAEJhC,YAAY,GAAU,EAAE;IAC/BiC,kBAAkB,GAAGtF,WAAW,CAACuF,kBAAkB,IAAI,KAAK;IAErDC,mBAAmB,GAAY,KAAK;IAC3CC,gBAAgB;IAEhB;IACQC,QAAQ,GAAG,IAAIrG,OAAO,EAAQ;IACtCsG,WAAW,GAAW,gBAAgB;IACtCC,QAAQ,GAAa,CACnB;MAAEzC,EAAE,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAgB,CAAE,EAC3C;MAAED,EAAE,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAc,CAAE,EACvC;MAAED,EAAE,EAAE,SAAS;MAAEC,KAAK,EAAE,SAAS;MAAEkB,QAAQ,EAAE;IAAI,CAAE,CACpD;IACDuB,QAAQ,GAAG,KAAK;IAChBC,UAAU;IACVjC,WAAW,GAAG,EAAE;IAChBkC,WAAW,GAAG,KAAK;IACnBC,WAAW,GAAa,KAAK;IAC7BC,aAAa,GAAW,EAAE;IAC1B7D,cAAc,GAAU,EAAE;IAC1BM,aAAa,GAAU,EAAE;IACzBa,QAAQ,GAAG,CAAC;IACZ2C,SAAS,GAAG,KAAK;IACjBC,WAAW,GAAW,EAAE;IAExBC,eAAe,GAAa,EAAE;IAC9BC,iBAAiB,GAAW,CAAC;IAC7BC,WAAW,GAAW,UAAU;IAEhC;IACAC,oBAAoB,GAAG,KAAK;IAC5BrE,cAAc,GAAc,EAAE;IAC9BJ,aAAa,GAA6B,EAAE;IAG5C0E,YACUzC,KAAqB,EACrBC,MAAc,EACdC,eAAgC,EAChCC,YAAiC,EACjCC,aAA4B,EAC5BC,WAAwB;MALxB,KAAAL,KAAK,GAALA,KAAK;MACL,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,eAAe,GAAfA,eAAe;MACf,KAAAC,YAAY,GAAZA,YAAY;MACZ,KAAAC,aAAa,GAAbA,aAAa;MACb,KAAAC,WAAW,GAAXA,WAAW;IAClB;IAEHqC,QAAQA,CAAA;MACN,IAAI,CAACtC,aAAa,CAACuC,aAAa,EAAE;MAClC,IAAI,CAACf,WAAW,GAAG,gBAAgB;MACnC,IAAI,CAACN,WAAW,GAAGsB,MAAM,CAACC,UAAU,EAAE;MACtC;MACA,IAAI,CAAC7C,KAAK,CAAC8C,QAAQ,CAACC,IAAI,CAACxH,SAAS,CAAC,IAAI,CAACoG,QAAQ,CAAC,CAAC,CAACqB,SAAS,CAAEC,MAAM,IAAI;QACtE,IAAI,CAACzC,UAAU,GAAGyC,MAAM,CAACC,GAAG,CAAC,IAAI,CAAC;QAClC,IAAI,IAAI,CAAC1C,UAAU,EAAE;UACnB,IAAI,CAAC2C,YAAY,CAAC,IAAI,CAAC3C,UAAU,CAAC;QACpC,CAAC,MAAM;UACL;UACA,IAAI,CAACP,MAAM,CAACmD,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;QAC5C;MACF,CAAC,CAAC;MACF;IACF;IAEAC,WAAWA,CAAA;MACT,IAAI,CAAC1B,QAAQ,CAAC2B,IAAI,EAAE;MACpB,IAAI,CAAC3B,QAAQ,CAAC4B,QAAQ,EAAE;MACxB,IAAI,CAACnD,aAAa,CAACoD,YAAY,EAAE;IACnC;IACArE,WAAWA,CAACsE,KAAoC;MAC9C,IAAI,CAAC7B,WAAW,GAAG6B,KAAK,CAACpE,KAAK;MAC9B,IAAI,CAACkD,WAAW,GAAGkB,KAAK,CAACrE,EAAE;MAC3BsE,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEF,KAAK,CAAC;IACpC;IAEA;IACAN,YAAYA,CAAC/D,EAAU;MACrB;MACAsE,OAAO,CAACC,GAAG,CAAC,6BAA6BvE,EAAE,EAAE,CAAC;MAC9C,IAAI,CAAC2B,YAAY,GAAG,CAClB;QACE6C,IAAI,EAAE,IAAI;QACVC,IAAI,EAAE;OACQ,CACjB;MACD,IAAI,CAACnF,YAAY,GAAG,IAAI,CAAC2B,WAAW,CAACyD,KAAK,CAAC,EAAE,CAAC;MAE9C,IAAI,CAAC5D,eAAe,CAAC6D,eAAe,CAAC3E,EAAE,CAAC,CAAC4D,SAAS,CAAC;QAC/CM,IAAI,EAAGU,GAAG,IAAI;UACd,IAAI,CAAC3F,cAAc,GAAG2F,GAAG,CAAC3F,cAAc;UACxC,IAAI,CAACM,aAAa,GAAG,IAAI,CAACsF,iBAAiB,CAAC,IAAI,CAAC5F,cAAc,CAAC;UAChE,IAAG,IAAI,CAACM,aAAa,CAACP,MAAM,KAAK,CAAC,EAAC;YACjC,IAAI,CAAC6D,WAAW,GAAG,IAAI;UACzB;UACA,IAAI,CAACxB,YAAY,GAAGuD,GAAG,CAAC9F,IAAI;UAC5B,IAAI,CAACgG,cAAc,EAAE;UACrB,IAAI,CAACC,qBAAqB,EAAE;UAC5B,IAAI,CAACC,oBAAoB,EAAE;QAC7B,CAAC;QACCC,KAAK,EAAGC,GAAG,IAAI;UACb;UACA,IAAI,CAACC,gBAAgB,EAAE;UACvB,IAAI,CAACtC,WAAW,GAAG,IAAI;UACvB,IAAI,CAACtB,iBAAiB,CAAC6D,aAAa,CAAC,uFAAuF,CAAC;UAC7Hd,OAAO,CAACC,GAAG,CAACW,GAAG,CAAC;QAClB;OACH,CAAC;IAEJ;IAEOL,iBAAiBA,CAACQ,cAAmB;MAC1C,MAAMC,oBAAoB,GAAI,qCAAqC;MACnE,MAAMC,cAAc,GAAoE,EAAE;MAE1FF,cAAc,CAACG,OAAO,CAAEC,KAAU,IAAI;QACpC,MAAMC,SAAS,GAAGD,KAAK,EAAEE,YAAY,EAAE7G,IAAI;QAC3C,MAAM8G,gBAAgB,GAAGH,KAAK,EAAEE,YAAY,EAAEE,WAAW;QACzD,MAAMC,OAAO,GAAGF,gBAAgB,CAACG,QAAQ,CAACT,oBAAoB,CAAC,IAAI,EAAE;QAErE,KAAK,MAAMU,KAAK,IAAIF,OAAO,EAAE;UAC3B,MAAMG,WAAW,GAAGD,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC;UACxC,MAAME,gBAAgB,GAAGF,KAAK,CAAC,CAAC,CAAC;UACjC,IAAI,CAACT,cAAc,CAACU,WAAW,CAAC,EAAE;YAChCV,cAAc,CAACU,WAAW,CAAC,GAAG;cAAEE,MAAM,EAAE,IAAIC,GAAG,EAAE;cAAEC,MAAM,EAAE,IAAID,GAAG;YAAE,CAAE;YAAC;UACzE;UACAb,cAAc,CAACU,WAAW,CAAC,CAACE,MAAM,CAACG,GAAG,CAACZ,SAAS,CAAC;UACjDH,cAAc,CAACU,WAAW,CAAC,CAACI,MAAM,CAACC,GAAG,CAACJ,gBAAgB,CAAC;QAC1D;MACF,CAAC,CAAC;MAEF,OAAOK,MAAM,CAACC,OAAO,CAACjB,cAAc,CAAC,CAACkB,GAAG,CAAC,CAAC,CAACR,WAAW,EAAE;QAAEE,MAAM;QAAEE;MAAM,CAAE,CAAC,MAAM;QAChFvH,IAAI,EAAE,CAAC,GAAGqH,MAAM,CAAC,CAACnH,MAAM,GAAG,CAAC,GACxB,GAAG,CAAC,GAAGmH,MAAM,CAAC,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACjJ,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG0I,MAAM,CAAC,CAACQ,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,GAClE,CAAC,GAAGR,MAAM,CAAC,CAAC1I,IAAI,CAAC,OAAO,CAAC;QAC7BwI,WAAW;QACX7G,KAAK,EAAE,CAAC,GAAGiH,MAAM,CAAC,CAAC,CAAC;OACrB,CAAC,CAAC;IACL;IAEOO,YAAYA,CAACxH,KAAa;MAC/B,MAAM4G,KAAK,GAAG5G,KAAK,CAAC4G,KAAK,CAAC,WAAW,CAAC;MACtC,IAAIA,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,EAAE;QACrB,MAAMa,YAAY,GAAGb,KAAK,CAAC,CAAC,CAAC,CAACc,IAAI,EAAE;QACpC,OAAOD,YAAY,CAACE,UAAU,CAAC,OAAO,CAAC,IAAIF,YAAY,CAACE,UAAU,CAAC,OAAO,CAAC;MAC7E;MACA,OAAO,KAAK;IACd;IAEOjC,cAAcA,CAAA;MACnB,IAAI,CAACvF,aAAa,CAACiG,OAAO,CAAEvF,KAAU,IAAI;QACxC,IAAI,CAACX,YAAY,CAAC0H,UAAU,CAAC/G,KAAK,CAACb,KAAK,EAAE,IAAI,CAAC6B,WAAW,CAACgG,OAAO,CAAC,EAAE,EAAE3K,UAAU,CAAC4K,QAAQ,CAAC,CAAC;MAC9F,CAAC,CAAC;IACJ;IAEOC,YAAYA,CAAA;MACjB,OAAO,IAAI,CAAC7H,YAAY,CAAC8H,KAAK,IAAI,IAAI,CAAChG,UAAU;IACnD;IAEAiG,iBAAiBA,CAAA;MACf,IAAI,CAACjH,QAAQ,GAAG,CAAC;MACjB,IAAI,CAACkC,gBAAgB,GAAGgF,WAAW,CAAC,MAAK;QACvC,IAAI,IAAI,CAAClH,QAAQ,GAAG,EAAE,EAAE;UACtB,IAAI,CAACA,QAAQ,IAAI,CAAC,CAAC,CAAC;QACtB;MACF,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACX;IAEAmH,gBAAgBA,CAAA;MACdC,aAAa,CAAC,IAAI,CAAClF,gBAAgB,CAAC;MACpC,IAAI,CAAClC,QAAQ,GAAG,GAAG;MAEnBqH,UAAU,CAAC,MAAK;QACd,IAAI,CAAC1E,SAAS,GAAG,KAAK;MACxB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACX;IAEA;IACA2E,iBAAiBA,CAACC,OAAe;MAC/B;MACA,IAAI,CAAC/F,gBAAgB,GAAG,IAAI;MAC5B,IAAG+F,OAAO,CAACb,IAAI,EAAE,KAAK,EAAE,EAAC;QACvB,IAAG,IAAI,CAAC7D,eAAe,CAACjE,MAAM,KAAK,CAAC,EAAC;UACnC,IAAI,CAACuC,iBAAiB,CAAC6D,aAAa,CAAC,2BAA2B,CAAC;UACjE,IAAI,CAACwC,eAAe,EAAE;QACxB;QACA;MACF;MAEA,IAAG,IAAI,CAACvF,mBAAmB,IAAI,IAAI,CAACa,iBAAiB,KAAG,IAAI,CAACD,eAAe,CAACjE,MAAM,EAAC;QAChF,IAAI,CAACuC,iBAAiB,CAAC6D,aAAa,CAAC,2BAA2B,CAAC;QACjE,IAAI,CAACwC,eAAe,EAAE;QACxB;MACF;MAEA,MAAMC,KAAK,GAAG,IAAI,CAAC5E,eAAe,CAAC,IAAI,CAACC,iBAAiB,CAAC;MAC1D,IAAI,IAAI,CAAC0D,YAAY,CAACiB,KAAK,CAAC,EAAE;QAC5B;QACA,IAAI,CAACtG,iBAAiB,CAAC6D,aAAa,CAAC,mCAAmCyC,KAAK,EAAE,CAAC;QAChF;MACF;MAEA,IAAI,CAACvI,YAAY,CAACwE,GAAG,CAAC+D,KAAK,CAAC,EAAEC,QAAQ,CAACH,OAAO,CAAC;MAC/C,IAAI,CAACzE,iBAAiB,EAAE;MAExB,IAAI,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAACD,eAAe,CAACjE,MAAM,EAAE;QACxD,IAAI,CAAC+I,qBAAqB,EAAE;MAC9B,CAAC,MAAM;QACL,IAAI,CAACxG,iBAAiB,CAAC6D,aAAa,CAAC,oDAAoD,CAAC;QAC1F,IAAI,CAACwC,eAAe,EAAE;MACxB;IACF;IAEA;IACAlI,QAAQA,CAAA;MACN4E,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACvC;IACF;IAEA;IACA9D,aAAaA,CAACuH,OAA8B;MAC1C1D,OAAO,CAACC,GAAG,CAAC,aAAayD,OAAO,UAAU,CAAC;MAE3C,IAAIA,OAAO,KAAK,UAAU,EAAE;QAC1B,MAAMC,IAAI,GAAG,IAAI,CAACzG,YAAY,CAC3BiF,GAAG,CAAElC,GAAG,IAAK,IAAIA,GAAG,CAAC2D,SAAS,KAAK3D,GAAG,CAACoD,OAAO,EAAE,CAAC,CACjDlK,IAAI,CAAC,IAAI,CAAC;QACb,IAAI,CAAC0K,cAAc,CAACF,IAAI,EAAE,4BAA4B,EAAE,YAAY,CAAC;MACvE,CAAC,MAAM;QACL,MAAMA,IAAI,GAAGG,IAAI,CAACC,SAAS,CAAC,IAAI,CAACvG,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC;QACvD,IAAI,CAACqG,cAAc,CAACF,IAAI,EAAE,uBAAuB,EAAE,kBAAkB,CAAC;MACxE;IACF;IAEA;IACQE,cAAcA,CAACF,IAAY,EAAEK,QAAgB,EAAEC,IAAY;MACjE,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACR,IAAI,CAAC,EAAE;QAAEM;MAAI,CAAE,CAAC;MACvC,MAAMG,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;MACrC,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGN,GAAG;MACfG,IAAI,CAACI,QAAQ,GAAGX,QAAQ;MACxBO,IAAI,CAACK,KAAK,EAAE;MACZP,GAAG,CAACQ,eAAe,CAACT,GAAG,CAAC;IAC1B;IAEA;IACA7I,mBAAmBA,CAACuJ,MAAiC;MACnD9E,OAAO,CAACC,GAAG,CAAC,mBAAmB6E,MAAM,EAAE,CAAC;MACxC;MAEA,IAAIA,MAAM,KAAK,MAAM,EAAE;QACrB,IAAI,CAAC/I,SAAS,GAAG,IAAI;MACvB,CAAC,MAAM,IAAI+I,MAAM,KAAK,OAAO,IAAIA,MAAM,KAAK,MAAM,EAAE;QAClD,IAAI,CAAC/I,SAAS,GAAG,KAAK;MACxB;IACF;IAEA;IACAgJ,YAAYA,CAAA;MACV,IAAI,CAACxI,MAAM,CAACmD,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;IAC5C;IAEA;IACAsF,YAAYA,CAAA;MACV,IAAI,IAAI,CAAClI,UAAU,EAAE;QACnB,IAAI,CAACP,MAAM,CAACmD,QAAQ,CAAC,CAAC,uBAAuB,EAAE,IAAI,CAAC5C,UAAU,CAAC,CAAC;MAClE;IACF;IAEOmI,kBAAkBA,CAACC,KAAA,GAAgB,IAAI;MAC5C/B,UAAU,CAAC,MAAK;QACd,IAAI,CAAC,IAAI,CAACpF,mBAAmB,EAAE;UAC7BiC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACjD,SAAS,CAAC;UAC3BgD,OAAO,CAACC,GAAG,CAAC,IAAI,CAACjD,SAAS,CAAC,QAAQ,CAAC,CAACmI,sBAAsB,CAAC;UAC5D,IAAI,CAACvJ,YAAY,CAACwJ,IAAI,CAAC;YACrBC,OAAO,EAAE,IAAI,CAACrI,SAAS,CAAC,QAAQ,CAAC,CAACmI,sBAAsB;YACxDG,KAAK,EAAE;WACR,CAAC;QACJ;MACF,CAAC,EAAEJ,KAAK,CAAC;IACX;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEOK,eAAeA,CAAC3H,WAAmB;MACxC,IAAI,CAACpB,eAAe,CACjBgJ,kBAAkB,CAAC5H,WAAW,CAAC,CAC/ByB,IAAI,CAACxH,SAAS,CAAC,IAAI,CAACoG,QAAQ,CAAC,CAAC,CAC9BqB,SAAS,CAAC;QACTM,IAAI,EAAGyD,OAAO,IAAI;UAChBrD,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEoD,OAAO,CAAC;UACjC,MAAM;YAAEgC,OAAO;YAAEC;UAAK,CAAE,GAAGjC,OAAO;UAClC,IAAIiC,KAAK,EAAE;YACT,IAAI,CAAC1J,YAAY,CAACwJ,IAAI,CAAC;cAAEC,OAAO;cAAEC;YAAK,CAAE,CAAC;UAC5C,CAAC,MAAM,IAAI,IAAI,CAACzH,kBAAkB,KAAK,KAAK,EAAE;YAC5C;UAAA;QAEJ,CAAC;QACD8C,KAAK,EAAGC,GAAG,IAAI;UACb,IAAI,CAAChF,YAAY,CAACwJ,IAAI,CAAC;YACrBC,OAAO,EAAE,IAAI,CAACrI,SAAS,CAAC,aAAa,CAAC;YACtCsI,KAAK,EAAE;WACR,CAAC;UACFtF,OAAO,CAACW,KAAK,CAAC,kBAAkB,EAAEC,GAAG,CAAC;QACxC,CAAC;QACDf,QAAQ,EAAEA,CAAA,KAAK;UACb,IAAI,CAACoF,kBAAkB,EAAE;UACzBjF,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC5C;OACD,CAAC;IACN;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEOwF,YAAYA,CAACC,MAAc;MAChC,IAAI,CAACpH,WAAW,GAAG,KAAK;MACxB,IAAI;QACF,MAAMqH,YAAY,GAAG7B,IAAI,CAAC8B,KAAK,CAACF,MAAM,CAAC;QACvC,IAAI,CAACpH,WAAW,GAAG,IAAI;QACvB,OAAOqH,YAAY;MACrB,CAAC,CAAC,OAAOE,CAAC,EAAE;QACV,OAAO,IAAI;MACb;IACF;IAEOvC,eAAeA,CAAA;MACpB,IAAIwC,OAAO,GAAmC,IAAIC,QAAQ,EAAE;MAC5D,IAAIC,WAAW,GAAG,EAAE;MAGpB,IAAI,CAAChK,MAAM,GAAGvD,eAAe,CAACwN,OAAO;MACrC,IAAI,IAAI,CAACzH,aAAa,CAAC9D,MAAM,EAAE;QAC7B,IAAI,CAAC8D,aAAa,CAAC0C,OAAO,CAAEgF,IAAI,IAAI;UAClCJ,OAAO,CAACK,MAAM,CAAC,OAAO,EAAED,IAAI,CAAC;QAC/B,CAAC,CAAC;QACFJ,OAAO,CAACK,MAAM,CAAC,YAAY,EAAE,IAAI,CAACrJ,UAAU,CAAC;QAC7CgJ,OAAO,CAACK,MAAM,CAAC,YAAY,EAAErC,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC/I,YAAY,CAACoL,KAAK,CAAC,CAAC;QACrEN,OAAO,CAACK,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC1J,YAAY,CAAC4J,aAAa,EAAE,CAAC;QACzDP,OAAO,CAACK,MAAM,CAAC,aAAa,EAAE,IAAI,CAACvI,WAAW,CAAC;QAC/CoI,WAAW,GAAG,QAAQ;MACxB,CAAC,MAAM;QACLF,OAAO,GAAG;UACRQ,UAAU,EAAE,IAAI,CAACxJ,UAAU;UAC3ByJ,UAAU,EAAE,IAAI,CAACvL,YAAY,CAACoL,KAAK;UACnCxI,WAAW,EAAE,IAAI,CAACA,WAAW;UAC7B4I,IAAI,EAAE,IAAI,CAAC/J,YAAY,CAAC4J,aAAa;SACtC;MACH;MAEA,IAAI,CAACd,eAAe,CAAC,IAAI,CAAC3H,WAAW,CAAC;MACtC,IAAI,CAACmF,iBAAiB,EAAE;MAExB,IAAI,CAACvG,eAAe,CACjB8G,eAAe,CAACwC,OAAO,EAAEE,WAAW,CAAC,CACrC3G,IAAI,CAACxH,SAAS,CAAC,IAAI,CAACoG,QAAQ,CAAC,CAAC,CAC9BqB,SAAS,CAAC;QACTM,IAAI,EAAGU,GAAG,IAAI;UACZ,IAAI,CAAChD,gBAAgB,GAAG,KAAK;UAC7B,IAAI,CAACvB,SAAS,GAAG,KAAK;UACtB,IAAI,CAACkB,iBAAiB,CAAC6D,aAAa,CAACR,GAAG,EAAE+C,OAAO,IAAI,4CAA4C,CAAC;UAElG,IAAI/C,GAAG,EAAEmG,gBAAgB,EAAEC,QAAQ,EAAEhB,MAAM,EAAE;YAC3C,IAAI,CAAC3H,mBAAmB,GAAG,IAAI;YAC/B;YACA,IAAI,CAACnC,YAAY,CAACwJ,IAAI,CAAC;cACrBC,OAAO,EAAE,IAAI,CAACrI,SAAS,CAAC,QAAQ,CAAC,CAAC2J,oBAAoB;cACtDrB,KAAK,EAAE;aACR,CAAC;YACF,IAAI,CAAClH,QAAQ,GAAG,KAAK;YACrB,IAAI,CAACC,UAAU,GAAGiC,GAAG,EAAEmG,gBAAgB,EAAEC,QAAQ,EAAEhB,MAAM;YACzD,IAAI,CAAClI,YAAY,GAAG8C,GAAG,EAAEmG,gBAAgB,EAAEC,QAAQ,EAAEE,YAAY,CAACzE,GAAG,CAAE0E,IAAS,IAAI;cAClF,OAAO;gBACLnL,EAAE,EAAEmL,IAAI,EAAEnL,EAAE,IAAI,EAAE;gBAClBoL,KAAK,EAAED,IAAI,EAAEC,KAAK,IAAI,EAAE;gBACxBzB,OAAO,EAAEwB,IAAI,EAAExB,OAAO,IAAI,EAAE;gBAC5BjE,SAAS,EAAEyF,IAAI,EAAEzF,SAAS,IAAI,EAAE;gBAChCwC,SAAS,EAAEiD,IAAI,EAAEjD,SAAS,IAAI,EAAE;gBAChCK,IAAI,EAAE4C,IAAI,EAAE5C,IAAI,IAAI,EAAE;gBACtB1C,WAAW,EAAEsF,IAAI,EAAEtF,WAAW,IAAI,EAAE;gBACpCwF,eAAe,EAAEF,IAAI,EAAEE,eAAe,IAAI,EAAE;gBAC5CC,OAAO,EAAEH,IAAI,EAAEG,OAAO,IAAI,EAAE;gBAC5BC,GAAG,EAAEJ,IAAI,EAAEI,GAAG,IAAI;eACnB;YACH,CAAC,CAAC;YAEF,IAAI,CAAC7K,WAAW,GAAGkE,GAAG,EAAEmG,gBAAgB,EAAEC,QAAQ,EAAEE,YAAY,CAACzE,GAAG,CACjE0E,IAKA,IAAI;cACH,OAAO;gBACLtF,WAAW,EAAEsF,IAAI,CAACtF,WAAW;gBAC7ByF,OAAO,EAAEH,IAAI,CAACG,OAAO;gBACrBC,GAAG,EAAEJ,IAAI,CAACI,GAAG;gBACbF,eAAe,EAAEF,IAAI,CAACE;eACvB;YACH,CAAC,CACF;YAED;YACA;YACA;YAEA;YACA;YACA;YACA;YACA;YACA;UACF;UACA,IAAI,CAACtB,YAAY,CAAC,IAAI,CAACpH,UAAU,CAAC;UAClC,IAAI,CAACrC,MAAM,GAAGvD,eAAe,CAAC6B,SAAS;UACvC,IAAI,CAAC2I,gBAAgB,EAAE;UACvB,IAAI,CAACzE,aAAa,GAAG,EAAE;QACzB,CAAC;QACDmC,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAAC5C,mBAAmB,GAAG,IAAI;UAC/B,IAAI,CAACT,gBAAgB,GAAG,KAAK;UAC7B,IAAI,CAACc,QAAQ,GAAG,IAAI;UACpB,IAAI,CAACC,UAAU,GAAGsC,KAAK,EAAEA,KAAK,EAAEuG,MAAM;UACtC,IAAI,CAAC1K,eAAe,CAAC2K,qBAAqB,EAAE;UAC5C,IAAI,CAACvL,YAAY,CAACwJ,IAAI,CAAC;YACrBC,OAAO,EAAE,IAAI,CAACrI,SAAS,CAAC,QAAQ,CAAC,CAACoK,iBAAiB;YACnD9B,KAAK,EAAE;WACR,CAAC;UACF,IAAI,CAACrI,iBAAiB,CAAC6D,aAAa,CAClC,sDAAsD,CACvD;UACD,IAAI,CAACtC,aAAa,GAAG,EAAE;UACvB,IAAI,CAACyE,gBAAgB,EAAE;UACvBjD,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEU,KAAK,CAAC0C,OAAO,CAAC;QACxC;OACD,CAAC;IACN;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEAgE,gBAAgBA,CAAA;MACdrH,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;IACjC;IAEAqH,qBAAqBA,CAACC,KAAa;MACjC,IAAG,IAAI,CAAC3I,iBAAiB,KAAG,IAAI,CAACD,eAAe,CAACjE,MAAM,IAAI,IAAI,CAACiE,eAAe,CAACjE,MAAM,KAAG,CAAC,EAAC;QACzF,IAAI,CAAC8D,aAAa,GAAG+I,KAAK;QAC1B;MACF;MAEA,MAAMhE,KAAK,GAAG,IAAI,CAAC5E,eAAe,CAAC,IAAI,CAACC,iBAAiB,CAAC;MAC1D,IAAG,IAAI,CAAC0D,YAAY,CAACiB,KAAK,CAAC,EAAC;QAC1B,IAAIgE,KAAK,IAAIA,KAAK,CAAC7M,MAAM,GAAG,CAAC,EAAE;UAC7B,IAAI,CAAC8M,eAAe,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC;QAChC;MACF,CAAC,MAAM;QACL,IAAI,CAAC/I,aAAa,GAAG+I,KAAK;MAC5B;IACF;IAEA7G,oBAAoBA,CAAA;MAClB,IAAI,CAAC/B,eAAe,GAAGsD,MAAM,CAACwF,IAAI,CAAC,IAAI,CAACzM,YAAY,CAAC0M,QAAQ,CAAC;MAC9D,IAAI,CAAC9I,iBAAiB,GAAG,CAAC;MAC1B,IAAI,IAAI,CAACD,eAAe,CAACjE,MAAM,GAAG,CAAC,EAAE;QACnC,IAAI,CAAC+I,qBAAqB,EAAE;MAC9B,CAAC,MACG;QACF,IAAI,CAAClF,WAAW,GAAG,IAAI;QACvB,IAAI,CAACtB,iBAAiB,CAAC6D,aAAa,CAAC,uFAAuF,CAAC;MAC/H;IACF;IAEA2C,qBAAqBA,CAAA;MACnB,MAAMF,KAAK,GAAG,IAAI,CAAC5E,eAAe,CAAC,IAAI,CAACC,iBAAiB,CAAC;MAC1D,IAAI,IAAI,CAAC0D,YAAY,CAACiB,KAAK,CAAC,EAAE;QAC5B,IAAI,CAAC9F,QAAQ,GAAG,sBAAsB;QACtC,IAAI,CAACR,iBAAiB,CAAC6D,aAAa,CAAC,8BAA8ByC,KAAK,EAAE,CAAC;QAC3E;MACF,CAAC,MAAM;QACL,IAAI,CAAC9F,QAAQ,GAAG,MAAM,CAAC,CAAC;QACxB,IAAI,CAACR,iBAAiB,CAAC6D,aAAa,CAAC,6BAA6ByC,KAAK,EAAE,CAAC;MAC5E;IACF;IAEAiE,eAAeA,CAACtB,IAAU;MACxB,MAAM3C,KAAK,GAAG,IAAI,CAAC5E,eAAe,CAAC,IAAI,CAACC,iBAAiB,CAAC;MAC1D,IAAI,CAAC,IAAI,CAAC0D,YAAY,CAACiB,KAAK,CAAC,EAAE;MAE/B,MAAMoE,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,MAAM,GAAG,MAAK;QACnB,MAAMC,YAAY,GAAIH,MAAM,CAACI,MAAiB;QAC9C,IAAI,CAAC/M,YAAY,CAACwE,GAAG,CAAC+D,KAAK,CAAC,EAAEC,QAAQ,CAACsE,YAAY,CAAC;QACpD,IAAI,CAAClJ,iBAAiB,EAAE;QACxB,IAAI,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAACD,eAAe,CAACjE,MAAM,EAAE;UACxD,IAAI,CAAC+I,qBAAqB,EAAE;QAC9B,CAAC,MAAM;UACL,IAAI,CAACxG,iBAAiB,CAAC6D,aAAa,CAAC,oCAAoC,CAAC;UAC1E,IAAI,CAACwC,eAAe,EAAE;QACxB;MACF,CAAC;MACDqE,MAAM,CAACK,aAAa,CAAC9B,IAAI,CAAC;IAC5B;IAEA;IACA+B,eAAeA,CAAA;MACb,IAAI,CAACnJ,oBAAoB,GAAG,CAAC,IAAI,CAACA,oBAAoB;IACxD;IAEA/E,oBAAoBA,CAACJ,KAAa;MAChC,IAAI,CAACc,cAAc,CAACd,KAAK,CAAC,GAAG,CAAC,IAAI,CAACc,cAAc,CAACd,KAAK,CAAC;IAC1D;IAEA8G,qBAAqBA,CAAA;MACnB,IAAI,CAAChG,cAAc,GAAG,IAAIyN,KAAK,CAAC,IAAI,CAACvN,cAAc,CAACD,MAAM,CAAC,CAACyN,IAAI,CAAC,KAAK,CAAC;MACvE,IAAI,CAAC9N,aAAa,GAAG,IAAI,CAACM,cAAc,CAACwH,GAAG,CAAC,OAAO;QAAE7H,SAAS,EAAE;MAAK,CAAE,CAAC,CAAC;IAC5E;IAEA;IACOiG,iBAAiBA,CAAC5F,cAAqB;MAC5C,MAAMqG,oBAAoB,GAAG,qCAAqC;MAClE,MAAMC,cAAc,GAAoE,EAAE;MAE1FtG,cAAc,CAACuG,OAAO,CAAEC,KAAU,IAAI;QACpC,MAAMC,SAAS,GAAGD,KAAK,EAAE3G,IAAI,IAAI,SAASG,cAAc,CAACyN,OAAO,CAACjH,KAAK,CAAC,GAAG,CAAC,EAAE;QAC7E,MAAMG,gBAAgB,GAAGH,KAAK,EAAEI,WAAW,IAAIJ,KAAK,EAAE0F,IAAI,EAAEtF,WAAW,IAAI,EAAE;QAC7E,MAAMC,OAAO,GAAGF,gBAAgB,CAACG,QAAQ,CAACT,oBAAoB,CAAC,IAAI,EAAE;QAErE,KAAK,MAAMU,KAAK,IAAIF,OAAO,EAAE;UAC3B,MAAMG,WAAW,GAAGD,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC;UACxC,MAAME,gBAAgB,GAAGF,KAAK,CAAC,CAAC,CAAC;UACjC,IAAI,CAACT,cAAc,CAACU,WAAW,CAAC,EAAE;YAChCV,cAAc,CAACU,WAAW,CAAC,GAAG;cAAEE,MAAM,EAAE,IAAIC,GAAG,EAAE;cAAEC,MAAM,EAAE,IAAID,GAAG;YAAE,CAAE;UACxE;UACAb,cAAc,CAACU,WAAW,CAAC,CAACE,MAAM,CAACG,GAAG,CAACZ,SAAS,CAAC;UACjDH,cAAc,CAACU,WAAW,CAAC,CAACI,MAAM,CAACC,GAAG,CAACJ,gBAAgB,CAAC;QAC1D;MACF,CAAC,CAAC;MAEF,OAAOK,MAAM,CAACC,OAAO,CAACjB,cAAc,CAAC,CAACkB,GAAG,CAAC,CAAC,CAACR,WAAW,EAAE;QAAEE,MAAM;QAAEE;MAAM,CAAE,CAAC,MAAM;QAChFvH,IAAI,EAAE,CAAC,GAAGqH,MAAM,CAAC,CAACnH,MAAM,GAAG,CAAC,GACxB,GAAG,CAAC,GAAGmH,MAAM,CAAC,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACjJ,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG0I,MAAM,CAAC,CAACQ,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,GAClE,CAAC,GAAGR,MAAM,CAAC,CAAC1I,IAAI,CAAC,OAAO,CAAC;QAC7BwI,WAAW;QACX7G,KAAK,EAAE,CAAC,GAAGiH,MAAM,CAAC,CAAC,CAAC;OACrB,CAAC,CAAC;IACL;IAEA;IACAc,YAAYA,CAAA;MACV,OAAO,IAAI,CAAC7H,YAAY,CAAC8H,KAAK,IAAI,IAAI,CAAChG,UAAU;IACnD;;uCA/nBWT,0BAA0B,EAAA3D,EAAA,CAAA2P,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA7P,EAAA,CAAA2P,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA9P,EAAA,CAAA2P,iBAAA,CAAAI,EAAA,CAAAC,eAAA,GAAAhQ,EAAA,CAAA2P,iBAAA,CAAAM,EAAA,CAAAC,mBAAA,GAAAlQ,EAAA,CAAA2P,iBAAA,CAAAM,EAAA,CAAAE,aAAA,GAAAnQ,EAAA,CAAA2P,iBAAA,CAAAS,EAAA,CAAAC,WAAA;IAAA;;YAA1B1M,0BAA0B;MAAA2M,SAAA;MAAAC,SAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBAY1BlR,sBAAsB;;;;;;;;;;;;UC5DnCS,EAAA,CAAAC,cAAA,aAA0C;;UAIpCD,EAFJ,CAAAC,cAAA,aAAqD,WAC7C,wBAC4D;UAE9DD,EADA,CAAAU,SAAA,cAAyC,cACE;UAGjDV,EAFI,CAAAG,YAAA,EAAiB,EACZ,EACH;;UAYIH,EAXV,CAAAC,cAAA,aAA2C,aAOxC,aACqD,aACvB,cACA,oBAMtB;UADCD,EAAA,CAAAY,UAAA,mBAAA+P,+DAAA;YAAA,OAASD,GAAA,CAAArE,YAAA,EAAc;UAAA,EAAC;UACzBrM,EAAA,CAAAG,YAAA,EAAW;UACZH,EAAA,CAAAC,cAAA,oBAKC;UADCD,EAAA,CAAAY,UAAA,mBAAAgQ,+DAAA;YAAA,OAASF,GAAA,CAAAnB,eAAA,EAAiB;UAAA,EAAC;UAGjCvP,EAFK,CAAAG,YAAA,EAAW,EACR,EACF;UAIJH,EAFF,CAAAC,cAAA,eAAiE,eAEnC;UAC1BD,EAAA,CAAAsB,UAAA,KAAAuP,mDAAA,6BAAkE;UAqCpE7Q,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAsB,UAAA,KAAAwP,0CAAA,kBAA4D;UAgC1D9Q,EADF,CAAAC,cAAA,eAA0B,SACrB;UAAAD,EAAA,CAAAE,MAAA,uDAA+C;UACpDF,EADoD,CAAAG,YAAA,EAAI,EAClD;UAIJH,EADF,CAAAC,cAAA,eAA6B,sBAY1B;UANCD,EAAA,CAAAY,UAAA,mBAAAmQ,iEAAA;YAAA,OAASL,GAAA,CAAA9F,eAAA,EAAiB;UAAA,EAAC;UAUrC5K,EAJS,CAAAG,YAAA,EAAa,EACV,EACF,EACF,EACF;UAOEH,EAJR,CAAAC,cAAA,eAA0E,eAC5C,eACK,eACV,oBAShB;UAFCD,EAAA,CAAAY,UAAA,uBAAAoQ,mEAAApO,MAAA;YAAA,OAAa8N,GAAA,CAAA3N,WAAA,CAAAH,MAAA,CAAmB;UAAA,EAAC;UAGrC5C,EADG,CAAAG,YAAA,EAAW,EACR;UACNH,EAAA,CAAAC,cAAA,eAAwC;UACtCD,EAAA,CAAAU,SAAA,sBAYa;UASjBV,EADE,CAAAG,YAAA,EAAM,EACF;UAeNH,EAbA,CAAAsB,UAAA,KAAA2P,0CAAA,kBAAmE,KAAAC,0CAAA,kBAaF;UASzElR,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACF;;;UApLAH,EAAA,CAAAI,SAAA,GAAwC;UAAxCJ,EAAA,CAAA0B,WAAA,cAAAgP,GAAA,CAAAtK,oBAAA,CAAwC;UAsBXpG,EAAA,CAAAI,SAAA,GAAqC;UAArCJ,EAAA,CAAA0B,WAAA,WAAAgP,GAAA,CAAAtK,oBAAA,CAAqC;UAG5BpG,EAAA,CAAAI,SAAA,GAAmB;UAAnBJ,EAAA,CAAA6B,UAAA,YAAA6O,GAAA,CAAAzO,cAAA,CAAmB;UAwCzBjC,EAAA,CAAAI,SAAA,EAA8B;UAA9BJ,EAAA,CAAA6B,UAAA,SAAA6O,GAAA,CAAAnO,aAAA,CAAAP,MAAA,KAA8B;UAyCtDhC,EAAA,CAAAI,SAAA,GAA4B;UAE5BJ,EAFA,CAAA6B,UAAA,cAAA6O,GAAA,CAAAvG,YAAA,GAA4B,iBAAAnK,EAAA,CAAAmR,eAAA,KAAAC,GAAA,EAM1B;UAaFpR,EAAA,CAAAI,SAAA,GAAuB;UAIvBJ,EAJA,CAAA6B,UAAA,SAAA6O,GAAA,CAAAxM,cAAA,CAAuB,gBAAAwM,GAAA,CAAAvK,WAAA,CACI,4BAGA;UAS3BnG,EAAA,CAAAI,SAAA,GAME;UANFJ,EAAA,CAAA6B,UAAA,iBAAA7B,EAAA,CAAAmR,eAAA,KAAAE,GAAA,EAME;UAcFrR,EAAA,CAAAI,SAAA,EAAsC;UAAtCJ,EAAA,CAAA6B,UAAA,SAAA6O,GAAA,CAAAlL,WAAA,sBAAsC;UAatCxF,EAAA,CAAAI,SAAA,EAAoC;UAApCJ,EAAA,CAAA6B,UAAA,SAAA6O,GAAA,CAAAlL,WAAA,oBAAoC;;;qBDtJ9CvG,YAAY,EAAAqS,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZpS,WAAW,EAAAgR,EAAA,CAAAqB,oBAAA,EAAArB,EAAA,CAAAsB,eAAA,EAAAtB,EAAA,CAAAuB,oBAAA,EACXtS,mBAAmB,EAAA+Q,EAAA,CAAAwB,kBAAA,EAAAxB,EAAA,CAAAyB,eAAA,EAEnBrS,sBAAsB,EACtBC,oBAAoB,EACpBG,aAAa,EACbF,eAAe,EACfC,aAAa;MAAAmS,MAAA;IAAA;;SAKJnO,0BAA0B;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}