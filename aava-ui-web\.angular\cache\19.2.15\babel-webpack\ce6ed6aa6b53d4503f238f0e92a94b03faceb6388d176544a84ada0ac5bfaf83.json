{"ast": null, "code": "import { atan, cos, sin } from \"../math.js\";\nimport { azimuthalInvert } from \"./azimuthal.js\";\nimport projection from \"./index.js\";\nexport function gnomonicRaw(x, y) {\n  var cy = cos(y),\n    k = cos(x) * cy;\n  return [cy * sin(x) / k, sin(y) / k];\n}\ngnomonicRaw.invert = azimuthalInvert(atan);\nexport default function () {\n  return projection(gnomonicRaw).scale(144.049).clipAngle(60);\n}", "map": {"version": 3, "names": ["atan", "cos", "sin", "azimuthalInvert", "projection", "gnomonicRaw", "x", "y", "cy", "k", "invert", "scale", "clipAngle"], "sources": ["C:/console/aava-ui-web/node_modules/d3-geo/src/projection/gnomonic.js"], "sourcesContent": ["import {atan, cos, sin} from \"../math.js\";\nimport {azimuthalInvert} from \"./azimuthal.js\";\nimport projection from \"./index.js\";\n\nexport function gnomonicRaw(x, y) {\n  var cy = cos(y), k = cos(x) * cy;\n  return [cy * sin(x) / k, sin(y) / k];\n}\n\ngnomonicRaw.invert = azimuthalInvert(atan);\n\nexport default function() {\n  return projection(gnomonicRaw)\n      .scale(144.049)\n      .clipAngle(60);\n}\n"], "mappings": "AAAA,SAAQA,IAAI,EAAEC,GAAG,EAAEC,GAAG,QAAO,YAAY;AACzC,SAAQC,eAAe,QAAO,gBAAgB;AAC9C,OAAOC,UAAU,MAAM,YAAY;AAEnC,OAAO,SAASC,WAAWA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAChC,IAAIC,EAAE,GAAGP,GAAG,CAACM,CAAC,CAAC;IAAEE,CAAC,GAAGR,GAAG,CAACK,CAAC,CAAC,GAAGE,EAAE;EAChC,OAAO,CAACA,EAAE,GAAGN,GAAG,CAACI,CAAC,CAAC,GAAGG,CAAC,EAAEP,GAAG,CAACK,CAAC,CAAC,GAAGE,CAAC,CAAC;AACtC;AAEAJ,WAAW,CAACK,MAAM,GAAGP,eAAe,CAACH,IAAI,CAAC;AAE1C,eAAe,YAAW;EACxB,OAAOI,UAAU,CAACC,WAAW,CAAC,CACzBM,KAAK,CAAC,OAAO,CAAC,CACdC,SAAS,CAAC,EAAE,CAAC;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}