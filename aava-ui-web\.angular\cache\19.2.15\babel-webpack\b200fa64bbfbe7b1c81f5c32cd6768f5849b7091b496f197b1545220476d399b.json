{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport let LoaderService = /*#__PURE__*/(() => {\n  class LoaderService {\n    isLoadingSubject = new BehaviorSubject(false);\n    inFlightHttpCalls = [];\n    loaderPull = true;\n    isLoadedEnabled = true;\n    /**\n     * Observable to track loading state\n     */\n    get isLoading$() {\n      return this.isLoadingSubject.asObservable();\n    }\n    /**\n     * Get current loading state\n     */\n    get isLoading() {\n      return this.isLoadingSubject.value;\n    }\n    /**\n     * Show loader\n     */\n    show() {\n      if (this.isLoadedEnabled) {\n        this.isLoadingSubject.next(true);\n      }\n    }\n    /**\n     * Hide loader\n     */\n    hide() {\n      this.isLoadingSubject.next(false);\n    }\n    /**\n     * Add an in-flight HTTP request by unique ID\n     */\n    addInflightHttpRequest(requestId) {\n      if (!this.isAnyHttpRequestPending()) {\n        // Show loader when first http request starts\n        this.show();\n      }\n      this.inFlightHttpCalls.push(requestId);\n    }\n    /**\n     * Remove a completed HTTP request by unique ID\n     */\n    removeCompletedHttpRequest(requestId) {\n      const index = this.inFlightHttpCalls.indexOf(requestId);\n      if (index > -1) {\n        this.inFlightHttpCalls.splice(index, 1);\n      }\n      // Hide the loader only if all the requests are completed\n      if (!this.isAnyHttpRequestPending()) {\n        this.hide();\n      }\n    }\n    /**\n     * Check if any HTTP request is pending\n     */\n    isAnyHttpRequestPending() {\n      return this.inFlightHttpCalls.length > 0;\n    }\n    /**\n     * Service started - called when a request begins\n     */\n    serviceStarted(requestId) {\n      if (this.loaderPull) {\n        this.addInflightHttpRequest(requestId);\n      }\n    }\n    /**\n     * Service completed - called when a request ends\n     */\n    serviceCompleted(requestId) {\n      this.removeCompletedHttpRequest(requestId);\n    }\n    /**\n     * Reset loader state (useful for error handling or navigation)\n     */\n    reset() {\n      this.inFlightHttpCalls = [];\n      this.isLoadingSubject.next(false);\n    }\n    /**\n     * Call this on navigation events to ensure loader is reset\n     */\n    resetOnNavigation() {\n      this.reset();\n    }\n    disableLoader() {\n      this.isLoadedEnabled = false;\n    }\n    enableLoader() {\n      this.isLoadedEnabled = true;\n    }\n    static ɵfac = function LoaderService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LoaderService)();\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: LoaderService,\n      factory: LoaderService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return LoaderService;\n})();", "map": {"version": 3, "names": ["BehaviorSubject", "LoaderService", "isLoadingSubject", "inFlightHttpCalls", "loaderPull", "isLoadedEnabled", "isLoading$", "asObservable", "isLoading", "value", "show", "next", "hide", "addInflightHttpRequest", "requestId", "isAnyHttpRequestPending", "push", "removeCompletedHttpRequest", "index", "indexOf", "splice", "length", "serviceStarted", "serviceCompleted", "reset", "resetOnNavigation", "disable<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "factory", "ɵfac", "providedIn"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\services\\loader\\loader.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable, Subject } from 'rxjs';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class LoaderService {\r\n  private isLoadingSubject = new BehaviorSubject<boolean>(false);\r\n  private inFlightHttpCalls: string[] = [];\r\n  public loaderPull: boolean = true;\r\n  isLoadedEnabled = true;\r\n\r\n  /**\r\n   * Observable to track loading state\r\n   */\r\n  get isLoading$(): Observable<boolean> {\r\n    return this.isLoadingSubject.asObservable();\r\n  }\r\n\r\n  /**\r\n   * Get current loading state\r\n   */\r\n  get isLoading(): boolean {\r\n    return this.isLoadingSubject.value;\r\n  }\r\n\r\n  /**\r\n   * Show loader\r\n   */\r\n  show(): void {\r\n    if (this.isLoadedEnabled) {\r\n      this.isLoadingSubject.next(true);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Hide loader\r\n   */\r\n  hide(): void {\r\n    this.isLoadingSubject.next(false);\r\n  }\r\n\r\n  /**\r\n   * Add an in-flight HTTP request by unique ID\r\n   */\r\n  private addInflightHttpRequest(requestId: string): void {\r\n    if (!this.isAnyHttpRequestPending()) {\r\n      // Show loader when first http request starts\r\n      this.show();\r\n    }\r\n    this.inFlightHttpCalls.push(requestId);\r\n  }\r\n\r\n  /**\r\n   * Remove a completed HTTP request by unique ID\r\n   */\r\n  private removeCompletedHttpRequest(requestId: string): void {\r\n    const index = this.inFlightHttpCalls.indexOf(requestId);\r\n    if (index > -1) {\r\n      this.inFlightHttpCalls.splice(index, 1);\r\n    }\r\n    // Hide the loader only if all the requests are completed\r\n    if (!this.isAnyHttpRequestPending()) {\r\n      this.hide();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if any HTTP request is pending\r\n   */\r\n  private isAnyHttpRequestPending(): boolean {\r\n    return this.inFlightHttpCalls.length > 0;\r\n  }\r\n\r\n  /**\r\n   * Service started - called when a request begins\r\n   */\r\n  serviceStarted(requestId: string): void {\r\n    if (this.loaderPull) {\r\n      this.addInflightHttpRequest(requestId);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Service completed - called when a request ends\r\n   */\r\n  serviceCompleted(requestId: string): void {\r\n    this.removeCompletedHttpRequest(requestId);\r\n  }\r\n\r\n  /**\r\n   * Reset loader state (useful for error handling or navigation)\r\n   */\r\n  reset(): void {\r\n    this.inFlightHttpCalls = [];\r\n    this.isLoadingSubject.next(false);\r\n  }\r\n\r\n  /**\r\n   * Call this on navigation events to ensure loader is reset\r\n   */\r\n  resetOnNavigation(): void {\r\n    this.reset();\r\n  }\r\n\r\n  disableLoader() {\r\n    this.isLoadedEnabled = false;\r\n  }\r\n  enableLoader() {\r\n    this.isLoadedEnabled = true;\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,eAAe,QAA6B,MAAM;;AAK3D,WAAaC,aAAa;EAApB,MAAOA,aAAa;IAChBC,gBAAgB,GAAG,IAAIF,eAAe,CAAU,KAAK,CAAC;IACtDG,iBAAiB,GAAa,EAAE;IACjCC,UAAU,GAAY,IAAI;IACjCC,eAAe,GAAG,IAAI;IAEtB;;;IAGA,IAAIC,UAAUA,CAAA;MACZ,OAAO,IAAI,CAACJ,gBAAgB,CAACK,YAAY,EAAE;IAC7C;IAEA;;;IAGA,IAAIC,SAASA,CAAA;MACX,OAAO,IAAI,CAACN,gBAAgB,CAACO,KAAK;IACpC;IAEA;;;IAGAC,IAAIA,CAAA;MACF,IAAI,IAAI,CAACL,eAAe,EAAE;QACxB,IAAI,CAACH,gBAAgB,CAACS,IAAI,CAAC,IAAI,CAAC;MAClC;IACF;IAEA;;;IAGAC,IAAIA,CAAA;MACF,IAAI,CAACV,gBAAgB,CAACS,IAAI,CAAC,KAAK,CAAC;IACnC;IAEA;;;IAGQE,sBAAsBA,CAACC,SAAiB;MAC9C,IAAI,CAAC,IAAI,CAACC,uBAAuB,EAAE,EAAE;QACnC;QACA,IAAI,CAACL,IAAI,EAAE;MACb;MACA,IAAI,CAACP,iBAAiB,CAACa,IAAI,CAACF,SAAS,CAAC;IACxC;IAEA;;;IAGQG,0BAA0BA,CAACH,SAAiB;MAClD,MAAMI,KAAK,GAAG,IAAI,CAACf,iBAAiB,CAACgB,OAAO,CAACL,SAAS,CAAC;MACvD,IAAII,KAAK,GAAG,CAAC,CAAC,EAAE;QACd,IAAI,CAACf,iBAAiB,CAACiB,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MACzC;MACA;MACA,IAAI,CAAC,IAAI,CAACH,uBAAuB,EAAE,EAAE;QACnC,IAAI,CAACH,IAAI,EAAE;MACb;IACF;IAEA;;;IAGQG,uBAAuBA,CAAA;MAC7B,OAAO,IAAI,CAACZ,iBAAiB,CAACkB,MAAM,GAAG,CAAC;IAC1C;IAEA;;;IAGAC,cAAcA,CAACR,SAAiB;MAC9B,IAAI,IAAI,CAACV,UAAU,EAAE;QACnB,IAAI,CAACS,sBAAsB,CAACC,SAAS,CAAC;MACxC;IACF;IAEA;;;IAGAS,gBAAgBA,CAACT,SAAiB;MAChC,IAAI,CAACG,0BAA0B,CAACH,SAAS,CAAC;IAC5C;IAEA;;;IAGAU,KAAKA,CAAA;MACH,IAAI,CAACrB,iBAAiB,GAAG,EAAE;MAC3B,IAAI,CAACD,gBAAgB,CAACS,IAAI,CAAC,KAAK,CAAC;IACnC;IAEA;;;IAGAc,iBAAiBA,CAAA;MACf,IAAI,CAACD,KAAK,EAAE;IACd;IAEAE,aAAaA,CAAA;MACX,IAAI,CAACrB,eAAe,GAAG,KAAK;IAC9B;IACAsB,YAAYA,CAAA;MACV,IAAI,CAACtB,eAAe,GAAG,IAAI;IAC7B;;uCAxGWJ,aAAa;IAAA;;aAAbA,aAAa;MAAA2B,OAAA,EAAb3B,aAAa,CAAA4B,IAAA;MAAAC,UAAA,EAFZ;IAAM;;SAEP7B,aAAa;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}