{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, Input, Output, EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { IconComponent } from '@ava/play-comp-library';\nlet ConsoleCardComponent = class ConsoleCardComponent {\n  title = '';\n  description = '';\n  categoryIcon = 'bot';\n  categoryTitle = 'Agents';\n  categoryValue = '75';\n  author = '';\n  date = '';\n  variant = 'primary';\n  size = 'medium';\n  disabled = false;\n  loading = false;\n  skeleton = false;\n  // Dynamic action buttons\n  actions = [{\n    id: 'view',\n    icon: 'calendar-days',\n    label: 'View details',\n    tooltip: 'View Details'\n  }, {\n    id: 'delete',\n    icon: 'trash',\n    label: 'Delete item',\n    tooltip: 'Delete'\n  }, {\n    id: 'copy',\n    icon: 'copy',\n    label: 'Copy to clipboard',\n    tooltip: 'Copy'\n  }, {\n    id: 'play',\n    icon: 'play',\n    label: 'Execute or play',\n    tooltip: 'Play',\n    isPrimary: true\n  }];\n  // Event emitters for action clicks\n  actionClick = new EventEmitter();\n  constructor() {\n    // console.log('🟡 ConsoleCardComponent constructor called');\n  }\n  ngOnInit() {\n    // console.log('🟢 ConsoleCard ngOnInit - inputs:', {\n    //   title: this.title,\n    //   description: this.description,\n    //   categoryTitle: this.categoryTitle,\n    //   categoryValue: this.categoryValue,\n    //   author: this.author,\n    //   date: this.date,\n    //   variant: this.variant,\n    //   skeleton: this.skeleton,\n    //   actions: this.actions,\n    // });\n  }\n  ngAfterViewInit() {\n    // console.log(\n    //   '🔵 ConsoleCard ngAfterViewInit - all inputs should be set now:',\n    //   {\n    //     title: this.title,\n    //     description: this.description,\n    //     categoryTitle: this.categoryTitle,\n    //     categoryValue: this.categoryValue,\n    //     author: this.author,\n    //     date: this.date,\n    //     skeleton: this.skeleton,\n    //     actions: this.actions,\n    //   },\n    // );\n  }\n  onActionClick(action) {\n    if (!action.disabled && !this.disabled && !this.loading && !this.skeleton) {\n      console.log('🔄 Action clicked:', action);\n      this.actionClick.emit({\n        actionId: action.id,\n        action\n      });\n    }\n  }\n  /**\n   * Get truncated description (200 characters max)\n   */\n  get truncatedDescription() {\n    if (!this.description) return '';\n    if (this.description.length <= 200) return this.description;\n    return this.description.substring(0, 200) + '...';\n  }\n  /**\n   * Check if description is truncated\n   */\n  get isDescriptionTruncated() {\n    return !!(this.description && this.description.length > 200);\n  }\n};\n__decorate([Input()], ConsoleCardComponent.prototype, \"title\", void 0);\n__decorate([Input()], ConsoleCardComponent.prototype, \"description\", void 0);\n__decorate([Input()], ConsoleCardComponent.prototype, \"categoryIcon\", void 0);\n__decorate([Input()], ConsoleCardComponent.prototype, \"categoryTitle\", void 0);\n__decorate([Input()], ConsoleCardComponent.prototype, \"categoryValue\", void 0);\n__decorate([Input()], ConsoleCardComponent.prototype, \"author\", void 0);\n__decorate([Input()], ConsoleCardComponent.prototype, \"date\", void 0);\n__decorate([Input()], ConsoleCardComponent.prototype, \"variant\", void 0);\n__decorate([Input()], ConsoleCardComponent.prototype, \"size\", void 0);\n__decorate([Input()], ConsoleCardComponent.prototype, \"disabled\", void 0);\n__decorate([Input()], ConsoleCardComponent.prototype, \"loading\", void 0);\n__decorate([Input()], ConsoleCardComponent.prototype, \"skeleton\", void 0);\n__decorate([Input()], ConsoleCardComponent.prototype, \"actions\", void 0);\n__decorate([Output()], ConsoleCardComponent.prototype, \"actionClick\", void 0);\nConsoleCardComponent = __decorate([Component({\n  selector: 'ava-console-card',\n  standalone: true,\n  imports: [CommonModule, IconComponent],\n  templateUrl: './console-card.component.html',\n  styleUrl: './console-card.component.scss'\n})], ConsoleCardComponent);\nexport { ConsoleCardComponent };", "map": {"version": 3, "names": ["Component", "Input", "Output", "EventEmitter", "CommonModule", "IconComponent", "ConsoleCardComponent", "title", "description", "categoryIcon", "categoryTitle", "categoryValue", "author", "date", "variant", "size", "disabled", "loading", "skeleton", "actions", "id", "icon", "label", "tooltip", "isPrimary", "actionClick", "constructor", "ngOnInit", "ngAfterViewInit", "onActionClick", "action", "console", "log", "emit", "actionId", "truncatedDescription", "length", "substring", "isDescriptionTruncated", "__decorate", "selector", "standalone", "imports", "templateUrl", "styleUrl"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\components\\console-card\\console-card.component.ts"], "sourcesContent": ["import {\r\n  Component,\r\n  Input,\r\n  OnInit,\r\n  AfterViewInit,\r\n  Output,\r\n  EventEmitter,\r\n} from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { IconComponent } from '@ava/play-comp-library';\r\n\r\nexport interface ConsoleCardAction {\r\n  id: string;\r\n  icon: string;\r\n  label: string;\r\n  tooltip: string;\r\n  isPrimary?: boolean;\r\n  disabled?: boolean;\r\n}\r\n\r\n@Component({\r\n  selector: 'ava-console-card',\r\n  standalone: true,\r\n  imports: [CommonModule, IconComponent],\r\n  templateUrl: './console-card.component.html',\r\n  styleUrl: './console-card.component.scss',\r\n})\r\nexport class ConsoleCardComponent implements OnInit, AfterViewInit {\r\n  @Input() title = '';\r\n  @Input() description = '';\r\n  @Input() categoryIcon = 'bot';\r\n  @Input() categoryTitle = 'Agents';\r\n  @Input() categoryValue = '75';\r\n  @Input() author = '';\r\n  @Input() date = '';\r\n  @Input() variant:\r\n    | 'primary'\r\n    | 'secondary'\r\n    | 'tertiary'\r\n    | 'quaternary'\r\n    | 'quinary'\r\n    | 'senary' = 'primary';\r\n  @Input() size: 'small' | 'medium' | 'large' = 'medium';\r\n  @Input() disabled = false;\r\n  @Input() loading = false;\r\n  @Input() skeleton = false;\r\n\r\n  // Dynamic action buttons\r\n  @Input() actions: ConsoleCardAction[] = [\r\n    {\r\n      id: 'view',\r\n      icon: 'calendar-days',\r\n      label: 'View details',\r\n      tooltip: 'View Details',\r\n    },\r\n    {\r\n      id: 'delete',\r\n      icon: 'trash',\r\n      label: 'Delete item',\r\n      tooltip: 'Delete',\r\n    },\r\n    {\r\n      id: 'copy',\r\n      icon: 'copy',\r\n      label: 'Copy to clipboard',\r\n      tooltip: 'Copy',\r\n    },\r\n    {\r\n      id: 'play',\r\n      icon: 'play',\r\n      label: 'Execute or play',\r\n      tooltip: 'Play',\r\n      isPrimary: true,\r\n    },\r\n  ];\r\n\r\n  // Event emitters for action clicks\r\n  @Output() actionClick = new EventEmitter<{\r\n    actionId: string;\r\n    action: ConsoleCardAction;\r\n  }>();\r\n\r\n  constructor() {\r\n    // console.log('🟡 ConsoleCardComponent constructor called');\r\n  }\r\n\r\n  ngOnInit() {\r\n    // console.log('🟢 ConsoleCard ngOnInit - inputs:', {\r\n    //   title: this.title,\r\n    //   description: this.description,\r\n    //   categoryTitle: this.categoryTitle,\r\n    //   categoryValue: this.categoryValue,\r\n    //   author: this.author,\r\n    //   date: this.date,\r\n    //   variant: this.variant,\r\n    //   skeleton: this.skeleton,\r\n    //   actions: this.actions,\r\n    // });\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n    // console.log(\r\n    //   '🔵 ConsoleCard ngAfterViewInit - all inputs should be set now:',\r\n    //   {\r\n    //     title: this.title,\r\n    //     description: this.description,\r\n    //     categoryTitle: this.categoryTitle,\r\n    //     categoryValue: this.categoryValue,\r\n    //     author: this.author,\r\n    //     date: this.date,\r\n    //     skeleton: this.skeleton,\r\n    //     actions: this.actions,\r\n    //   },\r\n    // );\r\n  }\r\n\r\n  onActionClick(action: ConsoleCardAction): void {\r\n    if (!action.disabled && !this.disabled && !this.loading && !this.skeleton) {\r\n      console.log('🔄 Action clicked:', action);\r\n      this.actionClick.emit({ actionId: action.id, action });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get truncated description (200 characters max)\r\n   */\r\n  get truncatedDescription(): string {\r\n    if (!this.description) return '';\r\n    if (this.description.length <= 200) return this.description;\r\n    return this.description.substring(0, 200) + '...';\r\n  }\r\n\r\n  /**\r\n   * Check if description is truncated\r\n   */\r\n  get isDescriptionTruncated(): boolean {\r\n    return !!(this.description && this.description.length > 200);\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SACEA,SAAS,EACTC,KAAK,EAGLC,MAAM,EACNC,YAAY,QACP,eAAe;AACtB,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AAkB/C,IAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EACtBC,KAAK,GAAG,EAAE;EACVC,WAAW,GAAG,EAAE;EAChBC,YAAY,GAAG,KAAK;EACpBC,aAAa,GAAG,QAAQ;EACxBC,aAAa,GAAG,IAAI;EACpBC,MAAM,GAAG,EAAE;EACXC,IAAI,GAAG,EAAE;EACTC,OAAO,GAMD,SAAS;EACfC,IAAI,GAAiC,QAAQ;EAC7CC,QAAQ,GAAG,KAAK;EAChBC,OAAO,GAAG,KAAK;EACfC,QAAQ,GAAG,KAAK;EAEzB;EACSC,OAAO,GAAwB,CACtC;IACEC,EAAE,EAAE,MAAM;IACVC,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,cAAc;IACrBC,OAAO,EAAE;GACV,EACD;IACEH,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,OAAO;IACbC,KAAK,EAAE,aAAa;IACpBC,OAAO,EAAE;GACV,EACD;IACEH,EAAE,EAAE,MAAM;IACVC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,mBAAmB;IAC1BC,OAAO,EAAE;GACV,EACD;IACEH,EAAE,EAAE,MAAM;IACVC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,iBAAiB;IACxBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE;GACZ,CACF;EAED;EACUC,WAAW,GAAG,IAAItB,YAAY,EAGpC;EAEJuB,YAAA;IACE;EAAA;EAGFC,QAAQA,CAAA;IACN;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA;EAGFC,eAAeA,CAAA;IACb;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA;EAGFC,aAAaA,CAACC,MAAyB;IACrC,IAAI,CAACA,MAAM,CAACd,QAAQ,IAAI,CAAC,IAAI,CAACA,QAAQ,IAAI,CAAC,IAAI,CAACC,OAAO,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE;MACzEa,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEF,MAAM,CAAC;MACzC,IAAI,CAACL,WAAW,CAACQ,IAAI,CAAC;QAAEC,QAAQ,EAAEJ,MAAM,CAACV,EAAE;QAAEU;MAAM,CAAE,CAAC;IACxD;EACF;EAEA;;;EAGA,IAAIK,oBAAoBA,CAAA;IACtB,IAAI,CAAC,IAAI,CAAC3B,WAAW,EAAE,OAAO,EAAE;IAChC,IAAI,IAAI,CAACA,WAAW,CAAC4B,MAAM,IAAI,GAAG,EAAE,OAAO,IAAI,CAAC5B,WAAW;IAC3D,OAAO,IAAI,CAACA,WAAW,CAAC6B,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK;EACnD;EAEA;;;EAGA,IAAIC,sBAAsBA,CAAA;IACxB,OAAO,CAAC,EAAE,IAAI,CAAC9B,WAAW,IAAI,IAAI,CAACA,WAAW,CAAC4B,MAAM,GAAG,GAAG,CAAC;EAC9D;CACD;AA9GUG,UAAA,EAARtC,KAAK,EAAE,C,kDAAY;AACXsC,UAAA,EAARtC,KAAK,EAAE,C,wDAAkB;AACjBsC,UAAA,EAARtC,KAAK,EAAE,C,yDAAsB;AACrBsC,UAAA,EAARtC,KAAK,EAAE,C,0DAA0B;AACzBsC,UAAA,EAARtC,KAAK,EAAE,C,0DAAsB;AACrBsC,UAAA,EAARtC,KAAK,EAAE,C,mDAAa;AACZsC,UAAA,EAARtC,KAAK,EAAE,C,iDAAW;AACVsC,UAAA,EAARtC,KAAK,EAAE,C,oDAMiB;AAChBsC,UAAA,EAARtC,KAAK,EAAE,C,iDAA+C;AAC9CsC,UAAA,EAARtC,KAAK,EAAE,C,qDAAkB;AACjBsC,UAAA,EAARtC,KAAK,EAAE,C,oDAAiB;AAChBsC,UAAA,EAARtC,KAAK,EAAE,C,qDAAkB;AAGjBsC,UAAA,EAARtC,KAAK,EAAE,C,oDA0BN;AAGQsC,UAAA,EAATrC,MAAM,EAAE,C,wDAGJ;AArDMI,oBAAoB,GAAAiC,UAAA,EAPhCvC,SAAS,CAAC;EACTwC,QAAQ,EAAE,kBAAkB;EAC5BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACtC,YAAY,EAAEC,aAAa,CAAC;EACtCsC,WAAW,EAAE,+BAA+B;EAC5CC,QAAQ,EAAE;CACX,CAAC,C,EACWtC,oBAAoB,CA+GhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}