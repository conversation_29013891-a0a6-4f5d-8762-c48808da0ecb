{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { Subject, takeUntil } from 'rxjs';\nimport { FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';\n// Import child components\nimport { ChatInterfaceComponent } from '@shared/components/chat-interface/chat-interface.component';\nimport { AgentActivityComponent } from './components/agent-activity/agent-activity.component';\nimport { AgentOutputComponent } from './components/agent-output/agent-output.component';\nimport { ButtonComponent, IconComponent, TabsComponent, AvaStepperComponent } from '@ava/play-comp-library';\nimport { environment } from '@shared/environments/environment';\nimport workflowConstants from './../constants/workflows.json';\nimport { ExecutionStatus } from '@shared/models/execution.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@shared/services/workflow.service\";\nimport * as i3 from \"@shared/index\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/common\";\nconst _c0 = () => ({\n  background: \"linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)\",\n  \"--button-effect-color\": \"33, 90, 214\",\n  \"border-radius\": \"8px\",\n  \"box-shadow\": \"none\"\n});\nconst _c1 = () => ({\n  \"background\": \"#1A46A7\",\n  \"width\": \"100%\",\n  \"border-radius\": \"8px\"\n});\nfunction WorkflowExecutionComponent_div_16_div_9_ng_container_8_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"textarea\", 49);\n    i0.ɵɵlistener(\"keydown.enter\", function WorkflowExecutionComponent_div_16_div_9_ng_container_8_div_4_Template_textarea_keydown_enter_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const input_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.submitCurrentInput(input_r4));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 50)(3, \"ava-icon\", 51);\n    i0.ɵɵlistener(\"click\", function WorkflowExecutionComponent_div_16_div_9_ng_container_8_div_4_Template_ava_icon_click_3_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const input_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.submitCurrentInput(input_r4));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const input_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formControlName\", input_r4.input)(\"placeholder\", \"Enter your input here...\");\n  }\n}\nfunction WorkflowExecutionComponent_div_16_div_9_ng_container_8_div_5_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"ava-icon\", 51);\n    i0.ɵɵlistener(\"click\", function WorkflowExecutionComponent_div_16_div_9_ng_container_8_div_5_div_6_Template_ava_icon_click_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const input_r4 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.submitCurrentInput(input_r4));\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction WorkflowExecutionComponent_div_16_div_9_ng_container_8_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"div\", 53);\n    i0.ɵɵlistener(\"click\", function WorkflowExecutionComponent_div_16_div_9_ng_container_8_div_5_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const input_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.triggerFileInput(input_r4.input));\n    });\n    i0.ɵɵelement(2, \"ava-icon\", 54);\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"Click to upload image\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"input\", 55);\n    i0.ɵɵlistener(\"change\", function WorkflowExecutionComponent_div_16_div_9_ng_container_8_div_5_Template_input_change_5_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const input_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.handleFileUpload($event, input_r4.input));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, WorkflowExecutionComponent_div_16_div_9_ng_container_8_div_5_div_6_Template, 2, 0, \"div\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_7_0;\n    const input_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"id\", \"file-\" + input_r4.input);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_7_0 = ctx_r1.workflowForm.get(input_r4.input)) == null ? null : tmp_7_0.value);\n  }\n}\nfunction WorkflowExecutionComponent_div_16_div_9_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 44)(2, \"label\", 45);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, WorkflowExecutionComponent_div_16_div_9_ng_container_8_div_4_Template, 4, 2, \"div\", 46)(5, WorkflowExecutionComponent_div_16_div_9_ng_container_8_div_5_Template, 7, 2, \"div\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const input_r4 = ctx.$implicit;\n    const i_r7 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"hidden\", i_r7 > 0 && !ctx_r1.showAllInputsForCurrentAgent);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(input_r4.placeholder);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isImageInput(input_r4.input));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isImageInput(input_r4.input));\n  }\n}\nfunction WorkflowExecutionComponent_div_16_div_9_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"ava-button\", 58);\n    i0.ɵɵlistener(\"click\", function WorkflowExecutionComponent_div_16_div_9_div_9_Template_ava_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.toggleShowAllInputs());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", ctx_r1.showAllInputsForCurrentAgent ? \"Show Less\" : \"Show More (\" + (ctx_r1.getCurrentAgentInputs().length - 1) + \" more)\");\n  }\n}\nfunction WorkflowExecutionComponent_div_16_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"div\", 37)(2, \"div\", 27);\n    i0.ɵɵelement(3, \"ava-icon\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\", 39);\n    i0.ɵɵtext(5, \"Enter the input\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 40)(7, \"div\", 41);\n    i0.ɵɵtemplate(8, WorkflowExecutionComponent_div_16_div_9_ng_container_8_Template, 6, 5, \"ng-container\", 42)(9, WorkflowExecutionComponent_div_16_div_9_div_9_Template, 2, 1, \"div\", 43);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.workflowForm);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getCurrentAgentInputs());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getCurrentAgentInputs().length > 1);\n  }\n}\nfunction WorkflowExecutionComponent_div_16_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"p\");\n    i0.ɵɵtext(2, \"No input required for this agent\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ava-button\", 60);\n    i0.ɵɵlistener(\"click\", function WorkflowExecutionComponent_div_16_div_10_Template_ava_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.moveToNextAgent());\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction WorkflowExecutionComponent_div_16_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"ava-button\", 62);\n    i0.ɵɵlistener(\"click\", function WorkflowExecutionComponent_div_16_div_14_Template_ava_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.executeWorkflow());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.isInputValid())(\"customStyles\", i0.ɵɵpureFunction0(2, _c1));\n  }\n}\nfunction WorkflowExecutionComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25)(2, \"div\", 26)(3, \"div\", 27);\n    i0.ɵɵelement(4, \"ava-icon\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 29)(6, \"h3\", 30);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"ava-icon\", 31);\n    i0.ɵɵlistener(\"click\", function WorkflowExecutionComponent_div_16_Template_ava_icon_click_8_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleCurrentAgentExpansion());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(9, WorkflowExecutionComponent_div_16_div_9_Template, 10, 3, \"div\", 32)(10, WorkflowExecutionComponent_div_16_div_10_Template, 4, 0, \"div\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 34)(12, \"p\");\n    i0.ɵɵtext(13, \"Agent input needed in order to execute Workflow\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(14, WorkflowExecutionComponent_div_16_div_14_Template, 2, 3, \"div\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"iconName\", ctx_r1.isCurrentAgentCompleted() ? \"check\" : \"folder\")(\"iconColor\", ctx_r1.isCurrentAgentCompleted() ? \"#22C55E\" : \"#1A46A7\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.currentAgent == null ? null : ctx_r1.currentAgent.agent == null ? null : ctx_r1.currentAgent.agent.name);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"expanded\", ctx_r1.isCurrentAgentExpanded);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getCurrentAgentInputs().length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getCurrentAgentInputs().length === 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isAllInputsCompleted());\n  }\n}\nfunction WorkflowExecutionComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"app-agent-activity\", 64);\n    i0.ɵɵlistener(\"saveLogs\", function WorkflowExecutionComponent_div_24_Template_app_agent_activity_saveLogs_1_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.saveLogs());\n    })(\"controlAction\", function WorkflowExecutionComponent_div_24_Template_app_agent_activity_controlAction_1_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.handleControlAction($event));\n    })(\"onOutPutBtnClick\", function WorkflowExecutionComponent_div_24_Template_app_agent_activity_onOutPutBtnClick_1_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTabChange({\n        id: \"nav-products\",\n        label: \"Agent Output\"\n      }));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"activityLogs\", ctx_r1.workflowLogs)(\"executionDetails\", ctx_r1.executionDetails)(\"progress\", ctx_r1.progress)(\"isRunning\", ctx_r1.isRunning)(\"status\", ctx_r1.status);\n  }\n}\nfunction WorkflowExecutionComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"app-agent-output\", 65);\n    i0.ɵɵlistener(\"export\", function WorkflowExecutionComponent_div_25_Template_app_agent_output_export_1_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.exportResults(\"output\"));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"outputs\", ctx_r1.taskMessage);\n  }\n}\nexport let WorkflowExecutionComponent = /*#__PURE__*/(() => {\n  class WorkflowExecutionComponent {\n    route;\n    router;\n    workflowService;\n    tokenStorage;\n    loaderService;\n    formBuilder;\n    navigationTabs = [{\n      id: 'nav-home',\n      label: 'Agent Activity'\n    }, {\n      id: 'nav-products',\n      label: 'Agent Output'\n    }, {\n      id: 'nav-services',\n      label: 'Preview',\n      disabled: true\n    }];\n    // Workflow details\n    workflowId = null;\n    workflowName = 'Workflow';\n    constants = workflowConstants;\n    chatInterfaceComp;\n    // Activity logs\n    activityLogs = [];\n    activityProgress = 0;\n    executionDetails;\n    isRunning = false;\n    status = ExecutionStatus.notStarted;\n    // Chat messages\n    chatMessages = [];\n    isProcessingChat = false;\n    inputText = '';\n    // Agent outputs\n    agentOutputs = [];\n    workflowForm;\n    fileType = '.zip';\n    // Execution state\n    executionStartTime = null;\n    executionCompleted = false;\n    executionId;\n    workflowLogs = [];\n    enableStreamingLog = environment.enableLogStreaming || 'all';\n    isExecutionComplete = false;\n    progressInterval;\n    // Component lifecycle\n    destroy$ = new Subject();\n    selectedTab = 'Agent Activity';\n    demoTabs = [{\n      id: 'activity',\n      label: 'Agent Activity'\n    }, {\n      id: 'agents',\n      label: 'Agent Output'\n    }, {\n      id: 'preview',\n      label: 'Preview',\n      disabled: true\n    }];\n    errorMsg = false;\n    resMessage;\n    taskMessage = [];\n    isJsonValid = false;\n    disableChat = false;\n    selectedFiles = [];\n    workflowAgents = [];\n    userInputList = [];\n    progress = 0;\n    isLoading = false;\n    loaderColor = '';\n    inputFieldOrder = [];\n    currentInputIndex = 0;\n    activeTabId = 'nav-home';\n    // Panel state\n    isLeftPanelCollapsed = false;\n    expandedAgents = [];\n    agentStatuses = [];\n    // Stepper state\n    stepperSteps = [];\n    currentStepIndex = 0;\n    currentAgent = null;\n    isCurrentAgentExpanded = false;\n    showAllInputsForCurrentAgent = false;\n    agentInputsMap = {};\n    constructor(route, router, workflowService, tokenStorage, loaderService, formBuilder) {\n      this.route = route;\n      this.router = router;\n      this.workflowService = workflowService;\n      this.tokenStorage = tokenStorage;\n      this.loaderService = loaderService;\n      this.formBuilder = formBuilder;\n    }\n    ngOnInit() {\n      this.loaderService.disableLoader();\n      this.selectedTab = 'Agent Activity';\n      this.executionId = crypto.randomUUID();\n      // Get workflow ID from route params\n      this.route.paramMap.pipe(takeUntil(this.destroy$)).subscribe(params => {\n        this.workflowId = params.get('id');\n        if (this.workflowId) {\n          this.loadWorkflow(this.workflowId);\n        } else {\n          // No workflow ID, redirect back to workflows page\n          this.router.navigate(['/build/workflows']);\n        }\n      });\n      // this.executeWorkflow()\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n      this.loaderService.enableLoader();\n    }\n    onTabChange(event) {\n      this.selectedTab = event.label;\n      this.activeTabId = event.id;\n      console.log('Tab changed:', event);\n    }\n    // Load workflow data\n    loadWorkflow(id) {\n      // In a real app, this would fetch the workflow from a service\n      console.log(`Loading workflow with ID: ${id}`);\n      this.chatMessages = [{\n        from: 'ai',\n        text: 'I am your workflow assistant. I will help you in executing this workflow.'\n      }];\n      this.workflowForm = this.formBuilder.group({});\n      this.workflowService.getWorkflowById(id).subscribe({\n        next: res => {\n          this.workflowAgents = res.pipeline.pipeLineAgents;\n          this.workflowName = res.pipeline.name;\n          this.initializeStepper();\n          this.initializeAgentInputs();\n          this.initializeForm();\n          this.initializeAgentStates();\n          this.setCurrentAgent(0);\n          this.startInputCollection();\n        },\n        error: err => {\n          // Fallback to demo data for testing\n          this.loadDemoWorkflow();\n          this.disableChat = true;\n          this.chatInterfaceComp.addAiResponse('No input is required for this workflow. Click on send button to execute the workflow.');\n          console.log(err);\n        }\n      });\n    }\n    isImageInput(input) {\n      const match = input.match(/{{(.*?)}}/);\n      if (match && match[1]) {\n        const variableName = match[1].trim();\n        return variableName.startsWith('image') || variableName.startsWith('Image');\n      }\n      return false;\n    }\n    initializeForm() {\n      this.userInputList.forEach(label => {\n        this.workflowForm.addControl(label.input, this.formBuilder.control('', Validators.required));\n      });\n    }\n    startFakeProgress() {\n      this.progress = 0;\n      this.progressInterval = setInterval(() => {\n        if (this.progress < 90) {\n          this.progress += 5; // Increase slowly\n        }\n      }, 200); // Adjust speed\n    }\n    stopFakeProgress() {\n      clearInterval(this.progressInterval);\n      this.progress = 100;\n      setTimeout(() => {\n        this.isLoading = false;\n      }, 500); // Small delay to let user see 100%\n    }\n    // Handle new chat message from user\n    handleChatMessage(message) {\n      // console.log('message ', message, 'is blank', message.trim() === '');\n      this.isProcessingChat = true;\n      if (message.trim() === '') {\n        if (this.inputFieldOrder.length === 0) {\n          this.chatInterfaceComp.addAiResponse('Executing the workflow...');\n          this.executeWorkflow();\n        }\n        return;\n      }\n      if (this.isExecutionComplete || this.currentInputIndex === this.inputFieldOrder.length) {\n        this.chatInterfaceComp.addAiResponse('Executing the workflow...');\n        this.executeWorkflow();\n        return;\n      }\n      const field = this.inputFieldOrder[this.currentInputIndex];\n      if (this.isImageInput(field)) {\n        // Ignore text input, wait for file input\n        this.chatInterfaceComp.addAiResponse(`Please upload an image file for ${field}`);\n        return;\n      }\n      this.workflowForm.get(field)?.setValue(message);\n      this.currentInputIndex++;\n      if (this.currentInputIndex < this.inputFieldOrder.length) {\n        this.promptForCurrentField();\n      } else {\n        this.chatInterfaceComp.addAiResponse('Thank you for the input! Executing the workflow...');\n        this.executeWorkflow();\n      }\n    }\n    // Save execution logs\n    saveLogs() {\n      console.log('Saving execution logs...');\n      // This would typically save to a service\n    }\n    // Export results\n    exportResults(section) {\n      console.log(`Exporting ${section} data...`);\n      if (section === 'activity') {\n        const data = this.activityLogs.map(log => `[${log.timestamp}] ${log.message}`).join('\\n');\n        this.downloadAsFile(data, 'workflow-activity-logs.txt', 'text/plain');\n      } else {\n        const data = JSON.stringify(this.agentOutputs, null, 2);\n        this.downloadAsFile(data, 'workflow-outputs.json', 'application/json');\n      }\n    }\n    // Helper method to download data as a file\n    downloadAsFile(data, filename, type) {\n      const blob = new Blob([data], {\n        type\n      });\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = filename;\n      link.click();\n      URL.revokeObjectURL(url);\n    }\n    // Handle controls for execution\n    handleControlAction(action) {\n      console.log(`Control action: ${action}`);\n      // In a real app, this would control the workflow execution\n      if (action === 'play') {\n        this.isRunning = true;\n      } else if (action === 'pause' || action === 'stop') {\n        this.isRunning = false;\n      }\n    }\n    // Navigate back to workflow listing\n    navigateBack() {\n      this.router.navigate(['/build/workflows']);\n    }\n    // Navigate to edit workflow\n    editWorkflow() {\n      if (this.workflowId) {\n        this.router.navigate(['/build/workflows/edit', this.workflowId]);\n      }\n    }\n    logExecutionStatus(delay = 2000) {\n      setTimeout(() => {\n        if (!this.isExecutionComplete) {\n          console.log(this.constants);\n          console.log(this.constants['labels'].workflowExecProcessing);\n          this.workflowLogs.push({\n            content: this.constants['labels'].workflowExecProcessing,\n            color: '#F9DB24'\n          });\n        }\n      }, delay);\n    }\n    // public parseAnsiString(ansiString: string) {\n    //   const regex = ansiRegex();\n    //   const parts = ansiString.split(regex);\n    //   const matches = [...ansiString.matchAll(regex)];\n    //   parts.forEach((part, index) => {\n    //     if (part.trim() !== '') {\n    //       let colorCode = matches[index - 1][0];\n    //       if (index - 2 >= 0 && matches[index - 2]?.includes('\\u001b[1m')) {\n    //         colorCode = `\\u001b[1m${colorCode}`;\n    //       }\n    //       this.workflowLogs.push({\n    //         content: part,\n    //         color: this.colorMap[colorCode] || 'white',\n    //       });\n    //     }\n    //   });\n    // }\n    getWorkflowLogs(executionId) {\n      this.workflowService.workflowLogConnect(executionId).pipe(takeUntil(this.destroy$)).subscribe({\n        next: message => {\n          console.log('message: ', message);\n          const {\n            content,\n            color\n          } = message;\n          if (color) {\n            this.workflowLogs.push({\n              content,\n              color\n            });\n          } else if (this.enableStreamingLog === 'all') {\n            // this.parseAnsiString(content);\n          }\n        },\n        error: err => {\n          this.workflowLogs.push({\n            content: this.constants['workflowLog'],\n            color: 'red'\n          });\n          console.error('WebSocket error:', err);\n        },\n        complete: () => {\n          this.logExecutionStatus();\n          console.log('WebSocket connection closed');\n        }\n      });\n    }\n    // public parseAnsiString(ansiString: string) {\n    //   const regex = ansiRegex();\n    //   const parts = ansiString.split(regex);\n    //   const matches = [...ansiString.matchAll(regex)];\n    //   parts.forEach((part, index) => {\n    //     if (part.trim() !== '') {\n    //       let colorCode = matches[index-1][0];\n    //       if(index - 2 >= 0 && matches[index-2]?.includes('\\u001b[1m')) {\n    //         colorCode = `\\u001b[1m${colorCode}`;\n    //       }\n    //       this.workflowLogs.push({\n    //         content: part, \n    //         color: this.colorMap[colorCode] || 'white', \n    //       });\n    //     }\n    //   });\n    // }\n    validateJson(output) {\n      this.isJsonValid = false;\n      try {\n        const parsedOutput = JSON.parse(output);\n        this.isJsonValid = true;\n        return parsedOutput;\n      } catch (e) {\n        return null;\n      }\n    }\n    executeWorkflow() {\n      let payload = new FormData();\n      let queryString = '';\n      this.status = ExecutionStatus.running;\n      if (this.selectedFiles.length) {\n        this.selectedFiles.forEach(file => {\n          payload.append('files', file);\n        });\n        payload.append('workflowId', this.workflowId);\n        payload.append('userInputs', JSON.stringify(this.workflowForm.value));\n        payload.append('user', this.tokenStorage.getDaUsername());\n        payload.append('executionId', this.executionId);\n        queryString = '/files';\n      } else {\n        payload = {\n          pipeLineId: this.workflowId,\n          userInputs: this.workflowForm.value,\n          executionId: this.executionId,\n          user: this.tokenStorage.getDaUsername()\n        };\n      }\n      this.getWorkflowLogs(this.executionId);\n      this.startFakeProgress();\n      this.workflowService.executeWorkflow(payload, queryString).pipe(takeUntil(this.destroy$)).subscribe({\n        next: res => {\n          this.isProcessingChat = false;\n          this.isRunning = false;\n          this.chatInterfaceComp.addAiResponse(res?.message || \"Workflow execution completed successfully!\");\n          if (res?.workflowResponse?.pipeline?.output) {\n            this.isExecutionComplete = true;\n            // console.log(this.constants['labels'].workflowExecComplete);\n            this.workflowLogs.push({\n              content: this.constants['labels'].workflowExecComplete,\n              color: '#0F8251'\n            });\n            this.errorMsg = false;\n            this.resMessage = res?.workflowResponse?.pipeline?.output;\n            this.agentOutputs = res?.workflowResponse?.pipeline?.tasksOutputs.map(task => {\n              return {\n                id: task?.id || '',\n                title: task?.title || '',\n                content: task?.content || '',\n                agentName: task?.agentName || '',\n                timestamp: task?.timestamp || '',\n                type: task?.type || '',\n                description: task?.description || '',\n                expected_output: task?.expected_output || '',\n                summary: task?.summary || '',\n                raw: task?.raw || ''\n              };\n            });\n            this.taskMessage = res?.workflowResponse?.pipeline?.tasksOutputs.map(task => {\n              return {\n                description: task.description,\n                summary: task.summary,\n                raw: task.raw,\n                expected_output: task.expected_output\n              };\n            });\n            // if(\"file_download_url\" in res?.pipeline){\n            //   this.isFileWriter = true;\n            //   this.fileDownloadLink = res?.pipeline?.file_download_url;\n            //   if(!this.fileDownloadLink){\n            //     this.fileDownloadUrlError = [];\n            //     this.fileDownloadUrlError.push(\"Output file is not generated yet!\")\n            //   }\n            // }\n            // this.isAccordian = true\n          }\n          this.validateJson(this.resMessage);\n          this.status = ExecutionStatus.completed;\n          this.stopFakeProgress();\n          this.selectedFiles = [];\n        },\n        error: error => {\n          this.isExecutionComplete = true;\n          this.isProcessingChat = false;\n          this.errorMsg = true;\n          this.resMessage = error?.error?.detail;\n          this.workflowService.workflowLogDisconnect();\n          this.workflowLogs.push({\n            content: this.constants['labels'].workflowLogFailed,\n            color: 'red'\n          });\n          this.chatInterfaceComp.addAiResponse('Something went wrong, Workflow execution has failed.');\n          this.selectedFiles = [];\n          this.stopFakeProgress();\n          console.log('error is', error.message);\n        }\n      });\n    }\n    // public asyncExecutePipeline() {\n    //   const payload: FormData = new FormData();\n    //   if (this.selectedFiles?.length) {\n    //     for (const element of this.selectedFiles) {\n    //       payload.append('files', element)\n    //     }\n    //   }\n    //   payload.append('pipeLineId', String(this.workflowId));\n    //   payload.append('userInputs', JSON.stringify(this.workflowForm.value));\n    //   payload.append('user', this.tokenStorage.getDaUsername() || '');\n    //   payload.append('executionId', this.executionId);\n    //   this.workflowService.asyncExecutePipeline(payload).pipe().subscribe({\n    //     next: (res: any) => {\n    //       if(res) {\n    //         // res handling\n    //         console.log(res);\n    //       }\n    //     },\n    //     error: e => {\n    //       // error handling\n    //       console.log(e);\n    //     }\n    //   })\n    // }\n    handleAttachment() {\n      console.log('handleAttachment');\n    }\n    onAttachmentsSelected(files) {\n      if (this.currentInputIndex === this.inputFieldOrder.length || this.inputFieldOrder.length === 0) {\n        this.selectedFiles = files;\n        return;\n      }\n      const field = this.inputFieldOrder[this.currentInputIndex];\n      if (this.isImageInput(field)) {\n        if (files && files.length > 0) {\n          this.onImageSelected(files[0]);\n        }\n      } else {\n        this.selectedFiles = files;\n      }\n    }\n    startInputCollection() {\n      this.inputFieldOrder = Object.keys(this.workflowForm.controls);\n      this.currentInputIndex = 0;\n      if (this.inputFieldOrder.length > 0) {\n        this.promptForCurrentField();\n      } else {\n        this.disableChat = true;\n        this.chatInterfaceComp.addAiResponse('No input is required for this workflow. Click on send button to execute the workflow.');\n      }\n    }\n    promptForCurrentField() {\n      const field = this.inputFieldOrder[this.currentInputIndex];\n      if (this.isImageInput(field)) {\n        this.fileType = '.jpeg,.png,.jpg,.svg';\n        this.chatInterfaceComp.addAiResponse(`Please upload an image for ${field}`);\n        // UI should now show a file input for the user\n      } else {\n        this.fileType = '.zip'; // or whatever default you want for non-image\n        this.chatInterfaceComp.addAiResponse(`Please enter the value of ${field}`);\n      }\n    }\n    onImageSelected(file) {\n      const field = this.inputFieldOrder[this.currentInputIndex];\n      if (!this.isImageInput(field)) return;\n      const reader = new FileReader();\n      reader.onload = () => {\n        const base64String = reader.result;\n        this.workflowForm.get(field)?.setValue(base64String);\n        this.currentInputIndex++;\n        if (this.currentInputIndex < this.inputFieldOrder.length) {\n          this.promptForCurrentField();\n        } else {\n          this.chatInterfaceComp.addAiResponse('Thank you! Running the workflow...');\n          this.executeWorkflow();\n        }\n      };\n      reader.readAsDataURL(file);\n    }\n    // Panel management methods\n    toggleLeftPanel() {\n      this.isLeftPanelCollapsed = !this.isLeftPanelCollapsed;\n    }\n    toggleAgentExpansion(index) {\n      this.expandedAgents[index] = !this.expandedAgents[index];\n    }\n    toggleCurrentAgentExpansion() {\n      this.isCurrentAgentExpanded = !this.isCurrentAgentExpanded;\n    }\n    toggleShowAllInputs() {\n      this.showAllInputsForCurrentAgent = !this.showAllInputsForCurrentAgent;\n    }\n    initializeAgentStates() {\n      this.expandedAgents = new Array(this.workflowAgents.length).fill(false);\n      this.agentStatuses = this.workflowAgents.map(() => ({\n        completed: false\n      }));\n    }\n    // Stepper management methods\n    initializeStepper() {\n      this.stepperSteps = this.workflowAgents.map((agent, index) => ({\n        id: index,\n        label: agent.agent.name,\n        completed: false,\n        active: index === 0\n      }));\n    }\n    onStepChange(stepIndex) {\n      this.setCurrentAgent(stepIndex);\n    }\n    setCurrentAgent(index) {\n      this.currentStepIndex = index;\n      this.currentAgent = this.workflowAgents[index];\n      this.isCurrentAgentExpanded = false;\n      this.showAllInputsForCurrentAgent = false;\n      // Update stepper steps\n      this.stepperSteps.forEach((step, i) => {\n        step.active = i === index;\n      });\n    }\n    moveToNextAgent() {\n      if (this.currentStepIndex < this.workflowAgents.length - 1) {\n        // Mark current agent as completed\n        this.stepperSteps[this.currentStepIndex].completed = true;\n        this.agentStatuses[this.currentStepIndex].completed = true;\n        // Move to next agent\n        this.setCurrentAgent(this.currentStepIndex + 1);\n      }\n    }\n    isCurrentAgentCompleted() {\n      return this.agentStatuses[this.currentStepIndex]?.completed || false;\n    }\n    // Agent input management methods\n    initializeAgentInputs() {\n      this.workflowAgents.forEach((agent, index) => {\n        const agentInputs = this.extractInputFieldForAgent(agent);\n        this.agentInputsMap[agent.agent.id] = agentInputs;\n      });\n      // Create combined user input list for form initialization\n      this.userInputList = [];\n      Object.values(this.agentInputsMap).forEach(inputs => {\n        this.userInputList.push(...inputs);\n      });\n    }\n    extractInputFieldForAgent(agent) {\n      const PLACEHOLDER_PATTERNS = /(%\\d+\\$s)|\\{\\{([a-zA-Z0-9-_]+)\\}\\}/g;\n      const placeholderMap = {};\n      const agentDescription = agent?.agent?.task?.description || '';\n      const matches = agentDescription.matchAll(PLACEHOLDER_PATTERNS) || [];\n      for (const match of matches) {\n        const placeholder = match[1] || match[2];\n        const placeholderInput = match[0];\n        if (!placeholderMap[placeholder]) {\n          placeholderMap[placeholder] = {\n            inputs: new Set()\n          };\n        }\n        placeholderMap[placeholder].inputs.add(placeholderInput);\n      }\n      return Object.entries(placeholderMap).map(([placeholder, {\n        inputs\n      }]) => ({\n        placeholder,\n        input: [...inputs][0],\n        agentId: agent.agent.id,\n        agentName: agent.agent.name\n      }));\n    }\n    getCurrentAgentInputs() {\n      if (!this.currentAgent) return [];\n      return this.agentInputsMap[this.currentAgent.agent.id] || [];\n    }\n    getCombinedTools(agent) {\n      const tools = [...(agent.tools || []), ...(agent.userTools || [])];\n      return tools.map(tool => tool.toolName || tool).join(', ') || 'None';\n    }\n    handleInputSubmit(event, input) {\n      if (event.key === 'Enter' && !event.shiftKey) {\n        event.preventDefault();\n        this.submitCurrentInput(input);\n      }\n    }\n    submitCurrentInput(input) {\n      const formControl = this.workflowForm.get(input.input);\n      if (formControl && formControl.value && formControl.value.trim()) {\n        // Add to chat messages\n        this.chatMessages.push({\n          from: 'user',\n          text: formControl.value\n        });\n        // Move to next agent if all inputs for current agent are filled\n        if (this.areCurrentAgentInputsComplete()) {\n          this.moveToNextAgent();\n        }\n      }\n    }\n    handleFileUpload(event, inputName) {\n      const file = event.target.files[0];\n      if (file) {\n        const reader = new FileReader();\n        reader.onload = () => {\n          const base64String = reader.result;\n          this.workflowForm.get(inputName)?.setValue(base64String);\n          // Add to chat messages\n          this.chatMessages.push({\n            from: 'user',\n            text: `Uploaded image: ${file.name}`\n          });\n          // Move to next agent if all inputs for current agent are filled\n          if (this.areCurrentAgentInputsComplete()) {\n            this.moveToNextAgent();\n          }\n        };\n        reader.readAsDataURL(file);\n      }\n    }\n    triggerFileInput(inputName) {\n      const fileInput = document.getElementById('file-' + inputName);\n      if (fileInput) {\n        fileInput.click();\n      }\n    }\n    areCurrentAgentInputsComplete() {\n      const currentInputs = this.getCurrentAgentInputs();\n      return currentInputs.every(input => {\n        const formControl = this.workflowForm.get(input.input);\n        return formControl && formControl.value && formControl.value.trim();\n      });\n    }\n    isAllInputsCompleted() {\n      return this.currentStepIndex === this.workflowAgents.length - 1 && this.areCurrentAgentInputsComplete();\n    }\n    // Input validation\n    isInputValid() {\n      return this.workflowForm.valid && !!this.workflowId;\n    }\n    // Demo workflow for testing\n    loadDemoWorkflow() {\n      this.workflowAgents = [{\n        serial: 1,\n        agent: {\n          id: 1,\n          name: 'Agent Alpha',\n          role: 'Data Processor',\n          task: {\n            description: 'Process the {{user_input}} and analyze the data'\n          },\n          llm: {\n            modelDeploymentName: 'GPT-4'\n          },\n          tools: [{\n            toolName: 'Data Analyzer'\n          }, {\n            toolName: 'Text Processor'\n          }],\n          userTools: []\n        }\n      }, {\n        serial: 2,\n        agent: {\n          id: 2,\n          name: 'Agent Beta',\n          role: 'Report Generator',\n          task: {\n            description: 'Review the analysis and generate {{report_type}} report'\n          },\n          llm: {\n            modelDeploymentName: 'Claude-3'\n          },\n          tools: [{\n            toolName: 'Report Generator'\n          }],\n          userTools: []\n        }\n      }, {\n        serial: 3,\n        agent: {\n          id: 3,\n          name: 'Agent Gamma',\n          role: 'Output Manager',\n          task: {\n            description: 'Finalize the output and prepare for delivery'\n          },\n          llm: {\n            modelDeploymentName: 'GPT-4'\n          },\n          tools: [{\n            toolName: 'File Manager'\n          }, {\n            toolName: 'Email Service'\n          }],\n          userTools: []\n        }\n      }];\n      this.workflowName = 'Demo Workflow';\n      this.initializeStepper();\n      this.initializeAgentInputs();\n      this.initializeForm();\n      this.initializeAgentStates();\n      this.setCurrentAgent(0);\n      if (this.userInputList.length === 0) {\n        this.disableChat = true;\n      }\n    }\n    static ɵfac = function WorkflowExecutionComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || WorkflowExecutionComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.WorkflowService), i0.ɵɵdirectiveInject(i3.TokenStorageService), i0.ɵɵdirectiveInject(i3.LoaderService), i0.ɵɵdirectiveInject(i4.FormBuilder));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: WorkflowExecutionComponent,\n      selectors: [[\"app-workflow-execution\"]],\n      viewQuery: function WorkflowExecutionComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(ChatInterfaceComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.chatInterfaceComp = _t.first);\n        }\n      },\n      decls: 26,\n      vars: 17,\n      consts: [[1, \"workflow-execution-container\"], [\"width\", \"0\", \"height\", \"0\", 2, \"position\", \"absolute\"], [\"id\", \"gradient\", \"x1\", \"0%\", \"y1\", \"0%\", \"x2\", \"100%\", \"y2\", \"0%\"], [\"offset\", \"0%\", \"stop-color\", \"#6566CD\"], [\"offset\", \"100%\", \"stop-color\", \"#F96CAB\"], [\"role\", \"main\", 1, \"execution-content\"], [\"role\", \"region\", \"aria-label\", \"Workflow Execution Panel\", 1, \"column\", \"execution-panel-column\"], [1, \"column-content\", \"execution-panel-content\"], [1, \"column-header\"], [1, \"header-left\"], [\"iconName\", \"arrowLeft\", \"iconColor\", \"#1A46A7\", 1, \"back-icon\", 3, \"click\"], [\"iconName\", \"panelLeft\", \"iconColor\", \"#1A46A7\", 1, \"panel-icon\", 3, \"click\"], [1, \"panel-content\"], [1, \"stepper-section\"], [\"orientation\", \"vertical\", \"size\", \"small\", 3, \"stepChange\", \"steps\", \"currentStep\", \"iconColor\", \"iconSize\", \"showNavigation\"], [\"class\", \"current-agent-section\", 4, \"ngIf\"], [\"role\", \"region\", \"aria-label\", \"Agent Output\", 1, \"column\", \"output-column\"], [1, \"column-content\"], [1, \"column-header\", \"row\"], [1, \"col-7\"], [\"variant\", \"button\", \"buttonShape\", \"pill\", \"ariaLabel\", \"Pill navigation tabs\", 3, \"tabChange\", \"tabs\", \"activeTabId\", \"showContentPanels\"], [1, \"col-5\", \"right-section-header\"], [\"label\", \"Send for Approval\", \"variant\", \"primary\", \"size\", \"medium\", 3, \"customStyles\"], [\"style\", \"height: 100%\", 4, \"ngIf\"], [1, \"current-agent-section\"], [1, \"agent-card\", \"active\"], [1, \"agent-header\"], [1, \"status-icon\"], [3, \"iconName\", \"iconColor\"], [1, \"agent-info\"], [1, \"agent-name\"], [\"iconName\", \"chevronDown\", \"iconColor\", \"#6B7280\", 1, \"expand-icon\", 3, \"click\"], [\"class\", \"input-section\", 4, \"ngIf\"], [\"class\", \"no-input-message\", 4, \"ngIf\"], [1, \"info-message\"], [\"class\", \"execute-section\", 4, \"ngIf\"], [1, \"input-section\"], [1, \"input-header\"], [\"iconName\", \"edit\", \"iconColor\", \"#1A46A7\"], [1, \"input-title\"], [1, \"input-container\", 3, \"formGroup\"], [1, \"input-scroll-container\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"toggle-inputs\", 4, \"ngIf\"], [1, \"input-field\"], [1, \"input-label\"], [\"class\", \"text-input-container\", 4, \"ngIf\"], [\"class\", \"image-input-container\", 4, \"ngIf\"], [1, \"text-input-container\"], [1, \"input-textarea\", 3, \"keydown.enter\", \"formControlName\", \"placeholder\"], [1, \"input-actions\"], [\"iconName\", \"send\", \"iconColor\", \"#1A46A7\", 1, \"send-icon\", 3, \"click\"], [1, \"image-input-container\"], [1, \"file-upload-area\", 3, \"click\"], [\"iconName\", \"image\", \"iconColor\", \"#6B7280\", 1, \"upload-icon\"], [\"type\", \"file\", \"accept\", \"image/*\", 2, \"display\", \"none\", 3, \"change\", \"id\"], [\"class\", \"input-actions\", 4, \"ngIf\"], [1, \"toggle-inputs\"], [\"variant\", \"secondary\", \"size\", \"small\", 3, \"click\", \"label\"], [1, \"no-input-message\"], [\"label\", \"Continue to Next Agent\", \"variant\", \"primary\", \"size\", \"medium\", 3, \"click\"], [1, \"execute-section\"], [\"label\", \"\\u25B6 Execute Workflow\", \"variant\", \"primary\", \"size\", \"large\", 3, \"click\", \"disabled\", \"customStyles\"], [2, \"height\", \"100%\"], [3, \"saveLogs\", \"controlAction\", \"onOutPutBtnClick\", \"activityLogs\", \"executionDetails\", \"progress\", \"isRunning\", \"status\"], [3, \"export\", \"outputs\"]],\n      template: function WorkflowExecutionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(1, \"svg\", 1)(2, \"defs\")(3, \"linearGradient\", 2);\n          i0.ɵɵelement(4, \"stop\", 3)(5, \"stop\", 4);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6)(8, \"div\", 7)(9, \"div\", 8)(10, \"div\", 9)(11, \"ava-icon\", 10);\n          i0.ɵɵlistener(\"click\", function WorkflowExecutionComponent_Template_ava_icon_click_11_listener() {\n            return ctx.navigateBack();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"ava-icon\", 11);\n          i0.ɵɵlistener(\"click\", function WorkflowExecutionComponent_Template_ava_icon_click_12_listener() {\n            return ctx.toggleLeftPanel();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 12)(14, \"div\", 13)(15, \"ava-stepper\", 14);\n          i0.ɵɵlistener(\"stepChange\", function WorkflowExecutionComponent_Template_ava_stepper_stepChange_15_listener($event) {\n            return ctx.onStepChange($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(16, WorkflowExecutionComponent_div_16_Template, 15, 8, \"div\", 15);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"div\", 16)(18, \"div\", 17)(19, \"div\", 18)(20, \"div\", 19)(21, \"ava-tabs\", 20);\n          i0.ɵɵlistener(\"tabChange\", function WorkflowExecutionComponent_Template_ava_tabs_tabChange_21_listener($event) {\n            return ctx.onTabChange($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"div\", 21);\n          i0.ɵɵelement(23, \"ava-button\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(24, WorkflowExecutionComponent_div_24_Template, 2, 5, \"div\", 23)(25, WorkflowExecutionComponent_div_25_Template, 2, 1, \"div\", 23);\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵclassProp(\"collapsed\", ctx.isLeftPanelCollapsed);\n          i0.ɵɵadvance(6);\n          i0.ɵɵclassProp(\"hidden\", ctx.isLeftPanelCollapsed);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"steps\", ctx.stepperSteps)(\"currentStep\", ctx.currentStepIndex)(\"iconColor\", \"#ffff\")(\"iconSize\", \"16\")(\"showNavigation\", false);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentAgent);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"tabs\", ctx.navigationTabs)(\"activeTabId\", ctx.activeTabId)(\"showContentPanels\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"customStyles\", i0.ɵɵpureFunction0(16, _c0));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTab === \"Agent Activity\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTab === \"Agent Output\");\n        }\n      },\n      dependencies: [CommonModule, i5.NgForOf, i5.NgIf, FormsModule, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, ReactiveFormsModule, i4.FormGroupDirective, i4.FormControlName, AgentActivityComponent, AgentOutputComponent, TabsComponent, ButtonComponent, IconComponent, AvaStepperComponent],\n      styles: [\".ava-tabs {\\n  background: none !important;\\n}\\n\\n  .ava-tabs__container {\\n  border-radius: none !important;\\n  border: none !important;\\n  padding: 0 !important;\\n  box-shadow: none !important;\\n  background: none !important;\\n}\\n\\n  .ava-tabs__list {\\n  padding: 0 !important;\\n}\\n\\n.workflow-execution-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 93%;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 20px 24px;\\n  border-bottom: 1px solid var(--border-color, #e0e0e0);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-title[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 24px;\\n  font-weight: 600;\\n  color: var(--text-color, #333);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-title[_ngcontent-%COMP%]   .header-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%], \\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 8px 16px;\\n  border-radius: 5px;\\n  font-weight: 500;\\n  font-size: 14px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%], \\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  margin-right: 6px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%] {\\n  background-color: var(--bg-muted, #f5f5f5);\\n  border: 1px solid var(--border-color, #e0e0e0);\\n  color: var(--text-color, #333);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]:hover {\\n  background-color: var(--bg-muted-hover, #e9e9e9);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%] {\\n  background-color: var(--card-bg, #fff);\\n  border: 1px solid var(--border-color, #e0e0e0);\\n  color: var(--text-color, #333);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]:hover {\\n  background-color: var(--card-bg-hover, #f9f9f9);\\n  border-color: var(--border-color-dark, #d0d0d0);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-grow: 1;\\n  height: 95vh;\\n  overflow: hidden;\\n  gap: 20px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 8px 16px;\\n  background-color: #e9effd;\\n  color: #fff;\\n  height: 64px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: #1a46a7;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  padding: 8px 16px;\\n  background-color: var(--card-bg, white);\\n  position: relative;\\n  border: 1px solid transparent;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: transparent;\\n  background-image: linear-gradient(90deg, #6566cd 0%, #f96cab 100%);\\n  -webkit-background-clip: text;\\n  background-clip: text;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  border-radius: 8px;\\n  padding: 1px;\\n  background: linear-gradient(90deg, #6566cd 0%, #f96cab 100%);\\n  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);\\n  mask-composite: exclude;\\n  pointer-events: none;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%]:hover {\\n  background-color: var(--bg-muted-hover, #f9f5ff);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 18px;\\n  height: 18px;\\n  fill: none;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]   path[_ngcontent-%COMP%] {\\n  stroke-width: 2;\\n  stroke: url(#gradient);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 0;\\n  display: flex;\\n  flex-direction: column;\\n  scrollbar-width: none;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 8px;\\n  height: 8px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: var(--scrollbar-track, #f1f1f1);\\n  border-radius: 4px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: var(--scrollbar-thumb, #d1d1d1);\\n  border-radius: 4px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: var(--scrollbar-thumb-hover, #b1b1b1);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%]   .nav-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  gap: 16px;\\n  background-color: #e9effd;\\n  padding: 0 15px 0 0;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%]   .nav-wrapper[_ngcontent-%COMP%]   .right-inner-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%]   .nav-wrapper[_ngcontent-%COMP%]   .right-inner-wrapper[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #fff;\\n  padding: 0 10px;\\n  margin: 0px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .activity-column[_ngcontent-%COMP%] {\\n  background: transparent;\\n  box-shadow: none;\\n  border-radius: 8px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .activity-column[_ngcontent-%COMP%]   app-agent-activity[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%] {\\n  flex: 0 0 400px;\\n  background: #F0F4FF;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 15px var(--card-shadow, rgba(0, 0, 0, 0.05));\\n  transition: all 0.3s ease;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column.collapsed[_ngcontent-%COMP%] {\\n  flex: 0 0 60px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column.collapsed[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%] {\\n  background: #F0F4FF;\\n  border-bottom: 1px solid #E5E7EB;\\n  padding: 16px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  align-items: center;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .back-icon[_ngcontent-%COMP%], \\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .panel-icon[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transition: transform 0.2s ease;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .back-icon[_ngcontent-%COMP%]:hover, \\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .panel-icon[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%]   .execution-panel-content[_ngcontent-%COMP%] {\\n  padding: 0;\\n  height: 100%;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%]   .execution-panel-content[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  height: calc(100% - 80px);\\n  overflow-y: auto;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%]   .execution-panel-content[_ngcontent-%COMP%]   .panel-content.hidden[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .playground-column[_ngcontent-%COMP%] {\\n  flex: 3.5;\\n  background: var(--card-bg);\\n  border-radius: 8px;\\n  box-shadow: 0 4px 15px var(--card-shadow, rgba(0, 0, 0, 0.05));\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .playground-column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .header-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  gap: 12px;\\n  align-items: center;\\n  color: #1a46a7;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .playground-column[_ngcontent-%COMP%]   .playground-content[_ngcontent-%COMP%] {\\n  padding: 0;\\n  height: 100%;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .playground-column[_ngcontent-%COMP%]   .playground-content[_ngcontent-%COMP%]   .chat-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  min-height: 400px;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  position: relative;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .playground-column[_ngcontent-%COMP%]   .playground-content[_ngcontent-%COMP%]   .chat-container[_ngcontent-%COMP%]     .prompt-container {\\n  margin-top: 30px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .playground-column[_ngcontent-%COMP%]   .playground-content[_ngcontent-%COMP%]   .chat-container[_ngcontent-%COMP%]   .playground-title[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: var(--dashboard-primary, #6566cd);\\n  margin: 16px 16px;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .playground-column[_ngcontent-%COMP%]   .playground-content[_ngcontent-%COMP%]   .chat-container[_ngcontent-%COMP%]   app-chat-interface[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  flex: 1;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .output-column[_ngcontent-%COMP%] {\\n  flex: 5.5;\\n  background-color: var(--card-bg, white);\\n  border-radius: 8px;\\n  box-shadow: 0 4px 15px var(--card-shadow, rgba(0, 0, 0, 0.05));\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .output-column[_ngcontent-%COMP%]   app-agent-output[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n  nav.ava-tabs__list {\\n  background: #e9effd;\\n  padding: 4px;\\n}\\n\\n  button.ava-button.primary.active {\\n  background: #616161;\\n  color: #fff;\\n}\\n\\n  .column-header .ava-tabs[data-variant=button] .ava-tabs__tab--pill {\\n  border-radius: 8px !important;\\n  padding: 12px 16px !important;\\n  font-family: \\\"Mulish\\\";\\n}\\n\\n  .ava-tabs[data-variant=button] .ava-tabs__tab--active .ava-tabs__tab-text {\\n  color: white;\\n}\\n\\n  .ava-tabs__tab-text {\\n  color: #4c515b;\\n  font-family: \\\"Mulish\\\";\\n  font-weight: 600;\\n}\\n\\n  .right-section-header .ava-button.secondary {\\n  color: #1a46a7;\\n  border: none;\\n}\\n\\n  .right-section-header .ava-button.secondary:hover {\\n  color: #1a46a7;\\n  border: none;\\n}\\n\\n.right-section-header[_ngcontent-%COMP%] {\\n  text-align: end;\\n}\\n\\n.stepper-section[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n  padding: 16px;\\n  background: white;\\n  border-radius: 12px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.current-agent-section[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.current-agent-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%] {\\n  background: #E8F0FE;\\n  border-radius: 12px;\\n  padding: 16px;\\n  margin-bottom: 16px;\\n  transition: all 0.3s ease;\\n}\\n.current-agent-section[_ngcontent-%COMP%]   .agent-card.active[_ngcontent-%COMP%] {\\n  background: #F0F9FF;\\n  border: 2px solid #1A46A7;\\n}\\n.current-agent-section[_ngcontent-%COMP%]   .agent-card.completed[_ngcontent-%COMP%] {\\n  background: #F0F9FF;\\n  border-left: 4px solid #22C55E;\\n}\\n.current-agent-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.current-agent-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-header[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  background: white;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.current-agent-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-header[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.current-agent-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-header[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%]   .agent-name[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #1F2937;\\n  margin: 0;\\n}\\n.current-agent-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-header[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%]   .expand-icon[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transition: transform 0.2s ease;\\n}\\n.current-agent-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-header[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%]   .expand-icon.expanded[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n.current-agent-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-details[_ngcontent-%COMP%] {\\n  margin-top: 12px;\\n  max-height: 0;\\n  overflow: hidden;\\n  transition: max-height 0.3s ease;\\n}\\n.current-agent-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-details.expanded[_ngcontent-%COMP%] {\\n  max-height: 300px;\\n}\\n.current-agent-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-details[_ngcontent-%COMP%]   .agent-detail-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  padding: 4px 0;\\n  font-size: 14px;\\n}\\n.current-agent-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-details[_ngcontent-%COMP%]   .agent-detail-item[_ngcontent-%COMP%]   .detail-label[_ngcontent-%COMP%] {\\n  color: #6B7280;\\n  font-weight: 500;\\n}\\n.current-agent-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-details[_ngcontent-%COMP%]   .agent-detail-item[_ngcontent-%COMP%]   .detail-value[_ngcontent-%COMP%] {\\n  color: #1F2937;\\n  text-align: right;\\n  max-width: 200px;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n\\n.input-section[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.input-section[_ngcontent-%COMP%]   .input-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  margin-bottom: 16px;\\n}\\n.input-section[_ngcontent-%COMP%]   .input-header[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  background: white;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.input-section[_ngcontent-%COMP%]   .input-header[_ngcontent-%COMP%]   .input-title[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #1F2937;\\n  margin: 0;\\n}\\n.input-section[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-scroll-container[_ngcontent-%COMP%] {\\n  max-height: 400px;\\n  overflow-y: auto;\\n  padding-right: 8px;\\n}\\n.input-section[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-scroll-container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.input-section[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-scroll-container[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 3px;\\n}\\n.input-section[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-scroll-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 3px;\\n}\\n.input-section[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin-bottom: 16px;\\n}\\n.input-section[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-field.hidden[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n.input-section[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .input-label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 12px;\\n  font-weight: 500;\\n  color: #6B7280;\\n  margin-bottom: 8px;\\n}\\n.input-section[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .text-input-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.input-section[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .text-input-container[_ngcontent-%COMP%]   .input-textarea[_ngcontent-%COMP%] {\\n  width: 100%;\\n  min-height: 80px;\\n  padding: 12px 50px 12px 16px;\\n  border: 2px solid #E5E7EB;\\n  border-radius: 12px;\\n  font-size: 14px;\\n  resize: vertical;\\n  background: white;\\n}\\n.input-section[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .text-input-container[_ngcontent-%COMP%]   .input-textarea[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #1A46A7;\\n}\\n.input-section[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .text-input-container[_ngcontent-%COMP%]   .input-textarea[_ngcontent-%COMP%]::placeholder {\\n  color: #9CA3AF;\\n}\\n.input-section[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .image-input-container[_ngcontent-%COMP%]   .file-upload-area[_ngcontent-%COMP%] {\\n  width: 100%;\\n  min-height: 80px;\\n  border: 2px dashed #E5E7EB;\\n  border-radius: 12px;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  background: white;\\n}\\n.input-section[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .image-input-container[_ngcontent-%COMP%]   .file-upload-area[_ngcontent-%COMP%]:hover {\\n  border-color: #1A46A7;\\n  background: #F8FAFC;\\n}\\n.input-section[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .image-input-container[_ngcontent-%COMP%]   .file-upload-area[_ngcontent-%COMP%]   .upload-icon[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n}\\n.input-section[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .image-input-container[_ngcontent-%COMP%]   .file-upload-area[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #6B7280;\\n  font-size: 14px;\\n}\\n.input-section[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .input-actions[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 12px;\\n  bottom: 12px;\\n  display: flex;\\n  gap: 8px;\\n}\\n.input-section[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .input-actions[_ngcontent-%COMP%]   .send-icon[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transition: transform 0.2s ease;\\n}\\n.input-section[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .input-actions[_ngcontent-%COMP%]   .send-icon[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n}\\n.input-section[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .toggle-inputs[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 16px;\\n}\\n\\n.no-input-message[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 24px;\\n  background: #F8FAFC;\\n  border-radius: 12px;\\n  margin-bottom: 16px;\\n}\\n.no-input-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #6B7280;\\n  font-size: 14px;\\n  margin: 0 0 16px 0;\\n}\\n\\n.info-message[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 24px;\\n}\\n.info-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #1A46A7;\\n  font-size: 14px;\\n  margin: 0;\\n}\\n\\n.execute-section[_ngcontent-%COMP%] {\\n  margin-top: auto;\\n  padding-top: 24px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3Byb2plY3RzL3NoYXJlZC9wYWdlcy93b3JrZmxvd3Mvd29ya2Zsb3ctZXhlY3V0aW9uL3dvcmtmbG93LWV4ZWN1dGlvbi5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFHQTtFQUNFLDJCQUFBO0FBRkY7O0FBS0E7RUFDRSw4QkFBQTtFQUNBLHVCQUFBO0VBQ0EscUJBQUE7RUFDQSwyQkFBQTtFQUNBLDJCQUFBO0FBRkY7O0FBS0E7RUFDRSxxQkFBQTtBQUZGOztBQUtBO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsV0FBQTtBQUZGO0FBR0U7RUFDRSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQkFBQTtFQUNBLGtCQUFBO0VBQ0EscURBQUE7QUFESjtBQUlNO0VBQ0UsU0FBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLDhCQUFBO0FBRlI7QUFLTTtFQUNFLGFBQUE7RUFDQSxTQUFBO0FBSFI7QUFPSTtFQUNFLGFBQUE7RUFDQSxTQUFBO0FBTE47QUFPTTs7RUFFRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxpQkFBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxlQUFBO0VBQ0EsZUFBQTtFQUNBLHlCQUFBO0FBTFI7QUFPUTs7RUFDRSxpQkFBQTtBQUpWO0FBUU07RUFDRSwwQ0FBQTtFQUNBLDhDQUFBO0VBQ0EsOEJBQUE7QUFOUjtBQVFRO0VBQ0UsZ0RBQUE7QUFOVjtBQVVNO0VBQ0Usc0NBQUE7RUFDQSw4Q0FBQTtFQUNBLDhCQUFBO0FBUlI7QUFVUTtFQUNFLCtDQUFBO0VBQ0EsK0NBQUE7QUFSVjtBQWVFO0VBQ0UsYUFBQTtFQUNBLFlBQUE7RUFDQSxZQUFBO0VBQ0EsZ0JBQUE7RUFDQSxTQUFBO0FBYko7QUFnQkk7RUFDRSxPQUFBO0VBQ0EsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsZ0JBQUE7QUFkTjtBQWdCTTtFQUNFLGFBQUE7RUFDQSw4QkFBQTtFQUNBLG1CQUFBO0VBQ0EsaUJBQUE7RUFDQSx5QkEzR0k7RUE0R0osV0FBQTtFQUNBLFlBQUE7QUFkUjtBQWdCUTtFQUNFLFNBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFFQSxjQW5IRztBQW9HYjtBQWtCUTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFFBQUE7RUFDQSxpQkFBQTtFQUNBLHVDQUFBO0VBQ0Esa0JBQUE7RUFDQSw2QkFBQTtFQUNBLGtCQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0Esa0JBQUE7RUFDQSxrRUFBQTtFQUNBLDZCQUFBO0VBQ0EscUJBQUE7RUFDQSxlQUFBO0VBQ0EseUJBQUE7QUFoQlY7QUFrQlU7RUFDRSxXQUFBO0VBQ0Esa0JBQUE7RUFDQSxNQUFBO0VBQ0EsT0FBQTtFQUNBLFFBQUE7RUFDQSxTQUFBO0VBQ0Esa0JBQUE7RUFDQSxZQUFBO0VBQ0EsNERBQUE7RUFDQSw4RUFDRTtFQUdGLHVCQUFBO0VBQ0Esb0JBQUE7QUFsQlo7QUFxQlU7RUFDRSxnREFBQTtBQW5CWjtBQXNCVTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0EsVUFBQTtBQXBCWjtBQXNCWTtFQUNFLGVBQUE7RUFDQSxzQkFBQTtBQXBCZDtBQTBCTTtFQUNFLE9BQUE7RUFDQSxnQkFBQTtFQUNBLFVBQUE7RUFDQSxhQUFBO0VBQ0Esc0JBQUE7RUFFQSxxQkFBQTtBQXpCUjtBQTJCUTtFQUNFLFVBQUE7RUFDQSxXQUFBO0FBekJWO0FBNEJRO0VBQ0UsMkNBQUE7RUFDQSxrQkFBQTtBQTFCVjtBQTZCUTtFQUNFLDJDQUFBO0VBQ0Esa0JBQUE7QUEzQlY7QUE4QlE7RUFDRSxpREFBQTtBQTVCVjtBQThCUTtFQUNFLGFBQUE7RUFDQSw4QkFBQTtFQUNBLG1CQUFBO0VBQ0EsU0FBQTtFQUNBLHlCQTVNRTtFQTZNRixtQkFBQTtBQTVCVjtBQTZCVTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFFBQUE7QUEzQlo7QUE2Qlk7RUFDRSxXQUFBO0VBQ0EsZUFBQTtFQUNBLFdBQUE7QUEzQmQ7QUFtQ0k7RUFDRSx1QkFBQTtFQUNBLGdCQUFBO0VBQ0Esa0JBQUE7QUFqQ047QUFtQ007RUFDRSxZQUFBO0VBQ0EsYUFBQTtFQUNBLHNCQUFBO0FBakNSO0FBcUNJO0VBQ0UsZUFBQTtFQUNBLG1CQUFBO0VBQ0Esa0JBQUE7RUFDQSw4REFBQTtFQUNBLHlCQUFBO0FBbkNOO0FBcUNNO0VBQ0UsY0FBQTtBQW5DUjtBQXFDUTtFQUNFLGFBQUE7QUFuQ1Y7QUF1Q007RUFDRSxtQkFBQTtFQUNBLGdDQUFBO0VBQ0EsYUFBQTtBQXJDUjtBQXVDUTtFQUNFLGFBQUE7RUFDQSxTQUFBO0VBQ0EsbUJBQUE7QUFyQ1Y7QUF1Q1U7O0VBRUUsZUFBQTtFQUNBLCtCQUFBO0FBckNaO0FBdUNZOztFQUNFLHFCQUFBO0FBcENkO0FBMENNO0VBQ0UsVUFBQTtFQUNBLFlBQUE7QUF4Q1I7QUEwQ1E7RUFDRSxhQUFBO0VBQ0EseUJBQUE7RUFDQSxnQkFBQTtBQXhDVjtBQTBDVTtFQUNFLGFBQUE7QUF4Q1o7QUE4Q0k7RUFDRSxTQUFBO0VBQ0EsMEJBQUE7RUFDQSxrQkFBQTtFQUNBLDhEQUFBO0FBNUNOO0FBK0NRO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsU0FBQTtFQUNBLG1CQUFBO0VBQ0EsY0ExU0c7QUE2UGI7QUFpRE07RUFDRSxVQUFBO0VBQ0EsWUFBQTtBQS9DUjtBQWlEUTtFQUNFLGFBQUE7RUFDQSxPQUFBO0VBQ0EsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsWUFBQTtFQUNBLGlCQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLGtCQUFBO0FBL0NWO0FBaURVO0VBQ0UsZ0JBQUE7QUEvQ1o7QUFrRFU7RUFDRSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSx3Q0FBQTtFQUNBLGlCQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLHVCQUFBO0FBaERaO0FBbURVO0VBQ0UsWUFBQTtFQUNBLGFBQUE7RUFDQSxzQkFBQTtFQUNBLE9BQUE7QUFqRFo7QUF1REk7RUFDRSxTQUFBO0VBQ0EsdUNBQUE7RUFDQSxrQkFBQTtFQUNBLDhEQUFBO0FBckROO0FBdURNO0VBQ0UsWUFBQTtFQUNBLGFBQUE7RUFDQSxzQkFBQTtBQXJEUjs7QUEwREE7RUFDRSxtQkFyV1U7RUFzV1YsWUFBQTtBQXZERjs7QUEwREE7RUFDRSxtQkFBQTtFQUNBLFdBQUE7QUF2REY7O0FBMERBO0VBQ0UsNkJBQUE7RUFDQSw2QkFBQTtFQUNBLHFCQUFBO0FBdkRGOztBQTBEQTtFQUlFLFlBQUE7QUExREY7O0FBNkRBO0VBQ0UsY0FBQTtFQUNBLHFCQUFBO0VBQ0EsZ0JBQUE7QUExREY7O0FBNkRBO0VBQ0UsY0FBQTtFQUNBLFlBQUE7QUExREY7O0FBNkRBO0VBQ0UsY0FBQTtFQUNBLFlBQUE7QUExREY7O0FBNkRBO0VBQ0UsZUFBQTtBQTFERjs7QUE4REE7RUFDRSxtQkFBQTtFQUNBLGFBQUE7RUFDQSxpQkFBQTtFQUNBLG1CQUFBO0VBQ0Esd0NBQUE7QUEzREY7O0FBOERBO0VBQ0UsbUJBQUE7QUEzREY7QUE2REU7RUFDRSxtQkFBQTtFQUNBLG1CQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EseUJBQUE7QUEzREo7QUE2REk7RUFDRSxtQkFBQTtFQUNBLHlCQUFBO0FBM0ROO0FBOERJO0VBQ0UsbUJBQUE7RUFDQSw4QkFBQTtBQTVETjtBQStESTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFNBQUE7QUE3RE47QUErRE07RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLHdDQUFBO0FBN0RSO0FBZ0VNO0VBQ0UsT0FBQTtFQUNBLGFBQUE7RUFDQSw4QkFBQTtFQUNBLG1CQUFBO0FBOURSO0FBZ0VRO0VBQ0UsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLFNBQUE7QUE5RFY7QUFpRVE7RUFDRSxlQUFBO0VBQ0EsK0JBQUE7QUEvRFY7QUFpRVU7RUFDRSx5QkFBQTtBQS9EWjtBQXFFSTtFQUNFLGdCQUFBO0VBQ0EsYUFBQTtFQUNBLGdCQUFBO0VBQ0EsZ0NBQUE7QUFuRU47QUFxRU07RUFDRSxpQkFBQTtBQW5FUjtBQXNFTTtFQUNFLGFBQUE7RUFDQSw4QkFBQTtFQUNBLGNBQUE7RUFDQSxlQUFBO0FBcEVSO0FBc0VRO0VBQ0UsY0FBQTtFQUNBLGdCQUFBO0FBcEVWO0FBdUVRO0VBQ0UsY0FBQTtFQUNBLGlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQkFBQTtFQUNBLHVCQUFBO0VBQ0EsbUJBQUE7QUFyRVY7O0FBNEVBO0VBQ0UsbUJBQUE7QUF6RUY7QUEyRUU7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxTQUFBO0VBQ0EsbUJBQUE7QUF6RUo7QUEyRUk7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLHdDQUFBO0FBekVOO0FBNEVJO0VBQ0UsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLFNBQUE7QUExRU47QUErRUk7RUFDRSxpQkFBQTtFQUNBLGdCQUFBO0VBQ0Esa0JBQUE7QUE3RU47QUErRU07RUFDRSxVQUFBO0FBN0VSO0FBZ0ZNO0VBQ0UsbUJBQUE7RUFDQSxrQkFBQTtBQTlFUjtBQWlGTTtFQUNFLG1CQUFBO0VBQ0Esa0JBQUE7QUEvRVI7QUFtRkk7RUFDRSxrQkFBQTtFQUNBLG1CQUFBO0FBakZOO0FBbUZNO0VBQ0UsYUFBQTtBQWpGUjtBQW9GTTtFQUNFLGNBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7QUFsRlI7QUFxRk07RUFDRSxrQkFBQTtBQW5GUjtBQXFGUTtFQUNFLFdBQUE7RUFDQSxnQkFBQTtFQUNBLDRCQUFBO0VBQ0EseUJBQUE7RUFDQSxtQkFBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGlCQUFBO0FBbkZWO0FBcUZVO0VBQ0UsYUFBQTtFQUNBLHFCQUFBO0FBbkZaO0FBc0ZVO0VBQ0UsY0FBQTtBQXBGWjtBQTBGUTtFQUNFLFdBQUE7RUFDQSxnQkFBQTtFQUNBLDBCQUFBO0VBQ0EsbUJBQUE7RUFDQSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsZUFBQTtFQUNBLHlCQUFBO0VBQ0EsaUJBQUE7QUF4RlY7QUEwRlU7RUFDRSxxQkFBQTtFQUNBLG1CQUFBO0FBeEZaO0FBMkZVO0VBQ0Usa0JBQUE7QUF6Rlo7QUE0RlU7RUFDRSxTQUFBO0VBQ0EsY0FBQTtFQUNBLGVBQUE7QUExRlo7QUErRk07RUFDRSxrQkFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0EsYUFBQTtFQUNBLFFBQUE7QUE3RlI7QUErRlE7RUFDRSxlQUFBO0VBQ0EsK0JBQUE7QUE3RlY7QUErRlU7RUFDRSxxQkFBQTtBQTdGWjtBQW1HSTtFQUNFLGtCQUFBO0VBQ0EsZ0JBQUE7QUFqR047O0FBc0dBO0VBQ0Usa0JBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxtQkFBQTtFQUNBLG1CQUFBO0FBbkdGO0FBcUdFO0VBQ0UsY0FBQTtFQUNBLGVBQUE7RUFDQSxrQkFBQTtBQW5HSjs7QUF1R0E7RUFDRSxrQkFBQTtFQUNBLG1CQUFBO0FBcEdGO0FBc0dFO0VBQ0UsY0FBQTtFQUNBLGVBQUE7RUFDQSxTQUFBO0FBcEdKOztBQXdHQTtFQUNFLGdCQUFBO0VBQ0EsaUJBQUE7QUFyR0YiLCJzb3VyY2VzQ29udGVudCI6WyIkaGVhZGVyLWJnOiAjZTllZmZkO1xyXG4kbWFpbi1jb2xvcjogIzFhNDZhNztcclxuXHJcbjo6bmctZGVlcCAuYXZhLXRhYnMge1xyXG4gIGJhY2tncm91bmQ6IG5vbmUgIWltcG9ydGFudDtcclxufVxyXG5cclxuOjpuZy1kZWVwIC5hdmEtdGFic19fY29udGFpbmVyIHtcclxuICBib3JkZXItcmFkaXVzOiBub25lICFpbXBvcnRhbnQ7XHJcbiAgYm9yZGVyOiBub25lICFpbXBvcnRhbnQ7XHJcbiAgcGFkZGluZzogMCAhaW1wb3J0YW50O1xyXG4gIGJveC1zaGFkb3c6IG5vbmUgIWltcG9ydGFudDtcclxuICBiYWNrZ3JvdW5kOiBub25lICFpbXBvcnRhbnQ7XHJcbn1cclxuXHJcbjo6bmctZGVlcCAuYXZhLXRhYnNfX2xpc3Qge1xyXG4gIHBhZGRpbmc6IDAgIWltcG9ydGFudDtcclxufVxyXG5cclxuLndvcmtmbG93LWV4ZWN1dGlvbi1jb250YWluZXIge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICBoZWlnaHQ6IDkzJTtcclxuICAuZXhlY3V0aW9uLWhlYWRlciB7XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xyXG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgIHBhZGRpbmc6IDIwcHggMjRweDtcclxuICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCB2YXIoLS1ib3JkZXItY29sb3IsICNlMGUwZTApO1xyXG5cclxuICAgIC5leGVjdXRpb24tdGl0bGUge1xyXG4gICAgICBoMSB7XHJcbiAgICAgICAgbWFyZ2luOiAwO1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMjRweDtcclxuICAgICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICAgIGNvbG9yOiB2YXIoLS10ZXh0LWNvbG9yLCAjMzMzKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLmhlYWRlci1idXR0b25zIHtcclxuICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgIGdhcDogMTJweDtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC5leGVjdXRpb24tYWN0aW9ucyB7XHJcbiAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgIGdhcDogMTJweDtcclxuXHJcbiAgICAgIC5iYWNrLWJ1dHRvbixcclxuICAgICAgLmVkaXQtYnV0dG9uIHtcclxuICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgICAgcGFkZGluZzogOHB4IDE2cHg7XHJcbiAgICAgICAgYm9yZGVyLXJhZGl1czogNXB4O1xyXG4gICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICAgICAgZm9udC1zaXplOiAxNHB4O1xyXG4gICAgICAgIGN1cnNvcjogcG9pbnRlcjtcclxuICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlO1xyXG5cclxuICAgICAgICBzdmcge1xyXG4gICAgICAgICAgbWFyZ2luLXJpZ2h0OiA2cHg7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICAuYmFjay1idXR0b24ge1xyXG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6IHZhcigtLWJnLW11dGVkLCAjZjVmNWY1KTtcclxuICAgICAgICBib3JkZXI6IDFweCBzb2xpZCB2YXIoLS1ib3JkZXItY29sb3IsICNlMGUwZTApO1xyXG4gICAgICAgIGNvbG9yOiB2YXIoLS10ZXh0LWNvbG9yLCAjMzMzKTtcclxuXHJcbiAgICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS1iZy1tdXRlZC1ob3ZlciwgI2U5ZTllOSk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICAuZWRpdC1idXR0b24ge1xyXG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6IHZhcigtLWNhcmQtYmcsICNmZmYpO1xyXG4gICAgICAgIGJvcmRlcjogMXB4IHNvbGlkIHZhcigtLWJvcmRlci1jb2xvciwgI2UwZTBlMCk7XHJcbiAgICAgICAgY29sb3I6IHZhcigtLXRleHQtY29sb3IsICMzMzMpO1xyXG5cclxuICAgICAgICAmOmhvdmVyIHtcclxuICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6IHZhcigtLWNhcmQtYmctaG92ZXIsICNmOWY5ZjkpO1xyXG4gICAgICAgICAgYm9yZGVyLWNvbG9yOiB2YXIoLS1ib3JkZXItY29sb3ItZGFyaywgI2QwZDBkMCk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvLyBNYWluIGNvbnRlbnQgd2l0aCAzIGNvbHVtbnNcclxuICAuZXhlY3V0aW9uLWNvbnRlbnQge1xyXG4gICAgZGlzcGxheTogZmxleDtcclxuICAgIGZsZXgtZ3JvdzogMTtcclxuICAgIGhlaWdodDogOTV2aDtcclxuICAgIG92ZXJmbG93OiBoaWRkZW47XHJcbiAgICBnYXA6IDIwcHg7XHJcblxyXG4gICAgLy8gQ29tbW9uIGNvbHVtbiBzdHlsaW5nXHJcbiAgICAuY29sdW1uIHtcclxuICAgICAgZmxleDogMTtcclxuICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICAgICAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuXHJcbiAgICAgIC5jb2x1bW4taGVhZGVyIHtcclxuICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcclxuICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICAgIHBhZGRpbmc6IDhweCAxNnB4O1xyXG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6ICRoZWFkZXItYmc7XHJcbiAgICAgICAgY29sb3I6ICNmZmY7XHJcbiAgICAgICAgaGVpZ2h0OiA2NHB4O1xyXG5cclxuICAgICAgICBoMiB7XHJcbiAgICAgICAgICBtYXJnaW46IDA7XHJcbiAgICAgICAgICBmb250LXNpemU6IDIwcHg7XHJcbiAgICAgICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICAgICAgLy8gY29sb3I6IHZhcigtLXRleHQtY29sb3IsICMzMzMpO1xyXG4gICAgICAgICAgY29sb3I6ICRtYWluLWNvbG9yO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLmljb24tYnV0dG9uIHtcclxuICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICAgICAgZ2FwOiA2cHg7XHJcbiAgICAgICAgICBwYWRkaW5nOiA4cHggMTZweDtcclxuICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6IHZhcigtLWNhcmQtYmcsIHdoaXRlKTtcclxuICAgICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkIHRyYW5zcGFyZW50O1xyXG4gICAgICAgICAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG4gICAgICAgICAgZm9udC1zaXplOiAxNHB4O1xyXG4gICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgICAgICAgIGNvbG9yOiB0cmFuc3BhcmVudDtcclxuICAgICAgICAgIGJhY2tncm91bmQtaW1hZ2U6IGxpbmVhci1ncmFkaWVudCg5MGRlZywgIzY1NjZjZCAwJSwgI2Y5NmNhYiAxMDAlKTtcclxuICAgICAgICAgIC13ZWJraXQtYmFja2dyb3VuZC1jbGlwOiB0ZXh0O1xyXG4gICAgICAgICAgYmFja2dyb3VuZC1jbGlwOiB0ZXh0O1xyXG4gICAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZTtcclxuXHJcbiAgICAgICAgICAmOjpiZWZvcmUge1xyXG4gICAgICAgICAgICBjb250ZW50OiBcIlwiO1xyXG4gICAgICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgICAgICAgICAgIHRvcDogMDtcclxuICAgICAgICAgICAgbGVmdDogMDtcclxuICAgICAgICAgICAgcmlnaHQ6IDA7XHJcbiAgICAgICAgICAgIGJvdHRvbTogMDtcclxuICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG4gICAgICAgICAgICBwYWRkaW5nOiAxcHg7XHJcbiAgICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg5MGRlZywgIzY1NjZjZCAwJSwgI2Y5NmNhYiAxMDAlKTtcclxuICAgICAgICAgICAgLXdlYmtpdC1tYXNrOlxyXG4gICAgICAgICAgICAgIGxpbmVhci1ncmFkaWVudCgjZmZmIDAgMCkgY29udGVudC1ib3gsXHJcbiAgICAgICAgICAgICAgbGluZWFyLWdyYWRpZW50KCNmZmYgMCAwKTtcclxuICAgICAgICAgICAgLXdlYmtpdC1tYXNrLWNvbXBvc2l0ZTogeG9yO1xyXG4gICAgICAgICAgICBtYXNrLWNvbXBvc2l0ZTogZXhjbHVkZTtcclxuICAgICAgICAgICAgcG9pbnRlci1ldmVudHM6IG5vbmU7XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6IHZhcigtLWJnLW11dGVkLWhvdmVyLCAjZjlmNWZmKTtcclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICBzdmcge1xyXG4gICAgICAgICAgICB3aWR0aDogMThweDtcclxuICAgICAgICAgICAgaGVpZ2h0OiAxOHB4O1xyXG4gICAgICAgICAgICBmaWxsOiBub25lO1xyXG5cclxuICAgICAgICAgICAgcGF0aCB7XHJcbiAgICAgICAgICAgICAgc3Ryb2tlLXdpZHRoOiAyO1xyXG4gICAgICAgICAgICAgIHN0cm9rZTogdXJsKCNncmFkaWVudCk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC5jb2x1bW4tY29udGVudCB7XHJcbiAgICAgICAgZmxleDogMTtcclxuICAgICAgICBvdmVyZmxvdy15OiBhdXRvO1xyXG4gICAgICAgIHBhZGRpbmc6IDA7XHJcbiAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gICAgICAgIC8vIGhlaWdodDogMTAwJTtcclxuICAgICAgICBzY3JvbGxiYXItd2lkdGg6IG5vbmU7XHJcblxyXG4gICAgICAgICY6Oi13ZWJraXQtc2Nyb2xsYmFyIHtcclxuICAgICAgICAgIHdpZHRoOiA4cHg7XHJcbiAgICAgICAgICBoZWlnaHQ6IDhweDtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgICY6Oi13ZWJraXQtc2Nyb2xsYmFyLXRyYWNrIHtcclxuICAgICAgICAgIGJhY2tncm91bmQ6IHZhcigtLXNjcm9sbGJhci10cmFjaywgI2YxZjFmMSk7XHJcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAmOjotd2Via2l0LXNjcm9sbGJhci10aHVtYiB7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1zY3JvbGxiYXItdGh1bWIsICNkMWQxZDEpO1xyXG4gICAgICAgICAgYm9yZGVyLXJhZGl1czogNHB4O1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgJjo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWI6aG92ZXIge1xyXG4gICAgICAgICAgYmFja2dyb3VuZDogdmFyKC0tc2Nyb2xsYmFyLXRodW1iLWhvdmVyLCAjYjFiMWIxKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgLm5hdi13cmFwcGVyIHtcclxuICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XHJcbiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICAgICAgZ2FwOiAxNnB4O1xyXG4gICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogJGhlYWRlci1iZztcclxuICAgICAgICAgIHBhZGRpbmc6IDAgMTVweCAwIDA7XHJcbiAgICAgICAgICAucmlnaHQtaW5uZXItd3JhcHBlciB7XHJcbiAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgICAgICAgIGdhcDogOHB4O1xyXG5cclxuICAgICAgICAgICAgcCB7XHJcbiAgICAgICAgICAgICAgY29sb3I6ICNmZmY7XHJcbiAgICAgICAgICAgICAgcGFkZGluZzogMCAxMHB4O1xyXG4gICAgICAgICAgICAgIG1hcmdpbjogMHB4O1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLy8gU3BlY2lmaWMgY29sdW1uIHN0eWxlcyBmb3IgY2hpbGQgY29tcG9uZW50c1xyXG4gICAgLmFjdGl2aXR5LWNvbHVtbiB7XHJcbiAgICAgIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50O1xyXG4gICAgICBib3gtc2hhZG93OiBub25lO1xyXG4gICAgICBib3JkZXItcmFkaXVzOiA4cHg7XHJcblxyXG4gICAgICBhcHAtYWdlbnQtYWN0aXZpdHkge1xyXG4gICAgICAgIGhlaWdodDogMTAwJTtcclxuICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAuZXhlY3V0aW9uLXBhbmVsLWNvbHVtbiB7XHJcbiAgICAgIGZsZXg6IDAgMCA0MDBweDtcclxuICAgICAgYmFja2dyb3VuZDogI0YwRjRGRjtcclxuICAgICAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG4gICAgICBib3gtc2hhZG93OiAwIDRweCAxNXB4IHZhcigtLWNhcmQtc2hhZG93LCByZ2JhKDAsIDAsIDAsIDAuMDUpKTtcclxuICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcclxuXHJcbiAgICAgICYuY29sbGFwc2VkIHtcclxuICAgICAgICBmbGV4OiAwIDAgNjBweDtcclxuXHJcbiAgICAgICAgLnBhbmVsLWNvbnRlbnQge1xyXG4gICAgICAgICAgZGlzcGxheTogbm9uZTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC5jb2x1bW4taGVhZGVyIHtcclxuICAgICAgICBiYWNrZ3JvdW5kOiAjRjBGNEZGO1xyXG4gICAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjRTVFN0VCO1xyXG4gICAgICAgIHBhZGRpbmc6IDE2cHg7XHJcblxyXG4gICAgICAgIC5oZWFkZXItbGVmdCB7XHJcbiAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgICAgZ2FwOiAxMnB4O1xyXG4gICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuXHJcbiAgICAgICAgICAuYmFjay1pY29uLFxyXG4gICAgICAgICAgLnBhbmVsLWljb24ge1xyXG4gICAgICAgICAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICAgICAgICAgIHRyYW5zaXRpb246IHRyYW5zZm9ybSAwLjJzIGVhc2U7XHJcblxyXG4gICAgICAgICAgICAmOmhvdmVyIHtcclxuICAgICAgICAgICAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMSk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC5leGVjdXRpb24tcGFuZWwtY29udGVudCB7XHJcbiAgICAgICAgcGFkZGluZzogMDtcclxuICAgICAgICBoZWlnaHQ6IDEwMCU7XHJcblxyXG4gICAgICAgIC5wYW5lbC1jb250ZW50IHtcclxuICAgICAgICAgIHBhZGRpbmc6IDIwcHg7XHJcbiAgICAgICAgICBoZWlnaHQ6IGNhbGMoMTAwJSAtIDgwcHgpO1xyXG4gICAgICAgICAgb3ZlcmZsb3cteTogYXV0bztcclxuXHJcbiAgICAgICAgICAmLmhpZGRlbiB7XHJcbiAgICAgICAgICAgIGRpc3BsYXk6IG5vbmU7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLnBsYXlncm91bmQtY29sdW1uIHtcclxuICAgICAgZmxleDogMy41O1xyXG4gICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1jYXJkLWJnKTtcclxuICAgICAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG4gICAgICBib3gtc2hhZG93OiAwIDRweCAxNXB4IHZhcigtLWNhcmQtc2hhZG93LCByZ2JhKDAsIDAsIDAsIDAuMDUpKTtcclxuXHJcbiAgICAgIC5jb2x1bW4taGVhZGVyIHtcclxuICAgICAgICAuaGVhZGVyLWJ1dHRvbnMge1xyXG4gICAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiByb3c7XHJcbiAgICAgICAgICBnYXA6IDEycHg7XHJcbiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICAgICAgY29sb3I6ICRtYWluLWNvbG9yO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgLnBsYXlncm91bmQtY29udGVudCB7XHJcbiAgICAgICAgcGFkZGluZzogMDtcclxuICAgICAgICBoZWlnaHQ6IDEwMCU7XHJcblxyXG4gICAgICAgIC5jaGF0LWNvbnRhaW5lciB7XHJcbiAgICAgICAgICBwYWRkaW5nOiAyMHB4O1xyXG4gICAgICAgICAgZmxleDogMTtcclxuICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gICAgICAgICAgaGVpZ2h0OiAxMDAlO1xyXG4gICAgICAgICAgbWluLWhlaWdodDogNDAwcHg7XHJcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiAxMnB4O1xyXG4gICAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuICAgICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuXHJcbiAgICAgICAgICA6Om5nLWRlZXAgLnByb21wdC1jb250YWluZXIge1xyXG4gICAgICAgICAgICBtYXJnaW4tdG9wOiAzMHB4O1xyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIC5wbGF5Z3JvdW5kLXRpdGxlIHtcclxuICAgICAgICAgICAgZm9udC1zaXplOiAxNHB4O1xyXG4gICAgICAgICAgICBmb250LXdlaWdodDogNTAwO1xyXG4gICAgICAgICAgICBjb2xvcjogdmFyKC0tZGFzaGJvYXJkLXByaW1hcnksICM2NTY2Y2QpO1xyXG4gICAgICAgICAgICBtYXJnaW46IDE2cHggMTZweDtcclxuICAgICAgICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDtcclxuICAgICAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuICAgICAgICAgICAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgYXBwLWNoYXQtaW50ZXJmYWNlIHtcclxuICAgICAgICAgICAgaGVpZ2h0OiAxMDAlO1xyXG4gICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gICAgICAgICAgICBmbGV4OiAxO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC5vdXRwdXQtY29sdW1uIHtcclxuICAgICAgZmxleDogNS41O1xyXG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS1jYXJkLWJnLCB3aGl0ZSk7XHJcbiAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICAgICAgYm94LXNoYWRvdzogMCA0cHggMTVweCB2YXIoLS1jYXJkLXNoYWRvdywgcmdiYSgwLCAwLCAwLCAwLjA1KSk7XHJcblxyXG4gICAgICBhcHAtYWdlbnQtb3V0cHV0IHtcclxuICAgICAgICBoZWlnaHQ6IDEwMCU7XHJcbiAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcbjo6bmctZGVlcCBuYXYuYXZhLXRhYnNfX2xpc3Qge1xyXG4gIGJhY2tncm91bmQ6ICRoZWFkZXItYmc7XHJcbiAgcGFkZGluZzogNHB4O1xyXG59XHJcblxyXG46Om5nLWRlZXAgYnV0dG9uLmF2YS1idXR0b24ucHJpbWFyeS5hY3RpdmUge1xyXG4gIGJhY2tncm91bmQ6ICM2MTYxNjE7XHJcbiAgY29sb3I6ICNmZmY7XHJcbn1cclxuXHJcbjo6bmctZGVlcCAuY29sdW1uLWhlYWRlciAuYXZhLXRhYnNbZGF0YS12YXJpYW50PVwiYnV0dG9uXCJdIC5hdmEtdGFic19fdGFiLS1waWxsIHtcclxuICBib3JkZXItcmFkaXVzOiA4cHggIWltcG9ydGFudDtcclxuICBwYWRkaW5nOiAxMnB4IDE2cHggIWltcG9ydGFudDtcclxuICBmb250LWZhbWlseTogXCJNdWxpc2hcIjtcclxufVxyXG5cclxuOjpuZy1kZWVwXHJcbiAgLmF2YS10YWJzW2RhdGEtdmFyaWFudD1cImJ1dHRvblwiXVxyXG4gIC5hdmEtdGFic19fdGFiLS1hY3RpdmVcclxuICAuYXZhLXRhYnNfX3RhYi10ZXh0IHtcclxuICBjb2xvcjogd2hpdGU7XHJcbn1cclxuXHJcbjo6bmctZGVlcCAuYXZhLXRhYnNfX3RhYi10ZXh0IHtcclxuICBjb2xvcjogIzRjNTE1YjtcclxuICBmb250LWZhbWlseTogXCJNdWxpc2hcIjtcclxuICBmb250LXdlaWdodDogNjAwO1xyXG59XHJcblxyXG46Om5nLWRlZXAgLnJpZ2h0LXNlY3Rpb24taGVhZGVyIC5hdmEtYnV0dG9uLnNlY29uZGFyeSB7XHJcbiAgY29sb3I6ICMxYTQ2YTc7XHJcbiAgYm9yZGVyOiBub25lO1xyXG59XHJcblxyXG46Om5nLWRlZXAgLnJpZ2h0LXNlY3Rpb24taGVhZGVyIC5hdmEtYnV0dG9uLnNlY29uZGFyeTpob3ZlciB7XHJcbiAgY29sb3I6ICMxYTQ2YTc7XHJcbiAgYm9yZGVyOiBub25lO1xyXG59XHJcblxyXG4ucmlnaHQtc2VjdGlvbi1oZWFkZXIge1xyXG4gIHRleHQtYWxpZ246IGVuZDtcclxufVxyXG5cclxuLy8gRXhlY3V0aW9uIFBhbmVsIFN0eWxlc1xyXG4uc3RlcHBlci1zZWN0aW9uIHtcclxuICBtYXJnaW4tYm90dG9tOiAyNHB4O1xyXG4gIHBhZGRpbmc6IDE2cHg7XHJcbiAgYmFja2dyb3VuZDogd2hpdGU7XHJcbiAgYm9yZGVyLXJhZGl1czogMTJweDtcclxuICBib3gtc2hhZG93OiAwIDJweCA0cHggcmdiYSgwLCAwLCAwLCAwLjEpO1xyXG59XHJcblxyXG4uY3VycmVudC1hZ2VudC1zZWN0aW9uIHtcclxuICBtYXJnaW4tYm90dG9tOiAyNHB4O1xyXG5cclxuICAuYWdlbnQtY2FyZCB7XHJcbiAgICBiYWNrZ3JvdW5kOiAjRThGMEZFO1xyXG4gICAgYm9yZGVyLXJhZGl1czogMTJweDtcclxuICAgIHBhZGRpbmc6IDE2cHg7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAxNnB4O1xyXG4gICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcclxuXHJcbiAgICAmLmFjdGl2ZSB7XHJcbiAgICAgIGJhY2tncm91bmQ6ICNGMEY5RkY7XHJcbiAgICAgIGJvcmRlcjogMnB4IHNvbGlkICMxQTQ2QTc7XHJcbiAgICB9XHJcblxyXG4gICAgJi5jb21wbGV0ZWQge1xyXG4gICAgICBiYWNrZ3JvdW5kOiAjRjBGOUZGO1xyXG4gICAgICBib3JkZXItbGVmdDogNHB4IHNvbGlkICMyMkM1NUU7XHJcbiAgICB9XHJcblxyXG4gICAgLmFnZW50LWhlYWRlciB7XHJcbiAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgIGdhcDogMTJweDtcclxuXHJcbiAgICAgIC5zdGF0dXMtaWNvbiB7XHJcbiAgICAgICAgd2lkdGg6IDMycHg7XHJcbiAgICAgICAgaGVpZ2h0OiAzMnB4O1xyXG4gICAgICAgIGJhY2tncm91bmQ6IHdoaXRlO1xyXG4gICAgICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcclxuICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgICAgICAgYm94LXNoYWRvdzogMCAycHggNHB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLmFnZW50LWluZm8ge1xyXG4gICAgICAgIGZsZXg6IDE7XHJcbiAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XHJcbiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuXHJcbiAgICAgICAgLmFnZW50LW5hbWUge1xyXG4gICAgICAgICAgZm9udC1zaXplOiAxNnB4O1xyXG4gICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgICAgICAgIGNvbG9yOiAjMUYyOTM3O1xyXG4gICAgICAgICAgbWFyZ2luOiAwO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLmV4cGFuZC1pY29uIHtcclxuICAgICAgICAgIGN1cnNvcjogcG9pbnRlcjtcclxuICAgICAgICAgIHRyYW5zaXRpb246IHRyYW5zZm9ybSAwLjJzIGVhc2U7XHJcblxyXG4gICAgICAgICAgJi5leHBhbmRlZCB7XHJcbiAgICAgICAgICAgIHRyYW5zZm9ybTogcm90YXRlKDE4MGRlZyk7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLmFnZW50LWRldGFpbHMge1xyXG4gICAgICBtYXJnaW4tdG9wOiAxMnB4O1xyXG4gICAgICBtYXgtaGVpZ2h0OiAwO1xyXG4gICAgICBvdmVyZmxvdzogaGlkZGVuO1xyXG4gICAgICB0cmFuc2l0aW9uOiBtYXgtaGVpZ2h0IDAuM3MgZWFzZTtcclxuXHJcbiAgICAgICYuZXhwYW5kZWQge1xyXG4gICAgICAgIG1heC1oZWlnaHQ6IDMwMHB4O1xyXG4gICAgICB9XHJcblxyXG4gICAgICAuYWdlbnQtZGV0YWlsLWl0ZW0ge1xyXG4gICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xyXG4gICAgICAgIHBhZGRpbmc6IDRweCAwO1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMTRweDtcclxuXHJcbiAgICAgICAgLmRldGFpbC1sYWJlbCB7XHJcbiAgICAgICAgICBjb2xvcjogIzZCNzI4MDtcclxuICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAuZGV0YWlsLXZhbHVlIHtcclxuICAgICAgICAgIGNvbG9yOiAjMUYyOTM3O1xyXG4gICAgICAgICAgdGV4dC1hbGlnbjogcmlnaHQ7XHJcbiAgICAgICAgICBtYXgtd2lkdGg6IDIwMHB4O1xyXG4gICAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuICAgICAgICAgIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzO1xyXG4gICAgICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi5pbnB1dC1zZWN0aW9uIHtcclxuICBtYXJnaW4tYm90dG9tOiAyNHB4O1xyXG5cclxuICAuaW5wdXQtaGVhZGVyIHtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgZ2FwOiAxMnB4O1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMTZweDtcclxuXHJcbiAgICAuc3RhdHVzLWljb24ge1xyXG4gICAgICB3aWR0aDogMzJweDtcclxuICAgICAgaGVpZ2h0OiAzMnB4O1xyXG4gICAgICBiYWNrZ3JvdW5kOiB3aGl0ZTtcclxuICAgICAgYm9yZGVyLXJhZGl1czogNTAlO1xyXG4gICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICAgICAgYm94LXNoYWRvdzogMCAycHggNHB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcclxuICAgIH1cclxuXHJcbiAgICAuaW5wdXQtdGl0bGUge1xyXG4gICAgICBmb250LXNpemU6IDE2cHg7XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgIGNvbG9yOiAjMUYyOTM3O1xyXG4gICAgICBtYXJnaW46IDA7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAuaW5wdXQtY29udGFpbmVyIHtcclxuICAgIC5pbnB1dC1zY3JvbGwtY29udGFpbmVyIHtcclxuICAgICAgbWF4LWhlaWdodDogNDAwcHg7XHJcbiAgICAgIG92ZXJmbG93LXk6IGF1dG87XHJcbiAgICAgIHBhZGRpbmctcmlnaHQ6IDhweDtcclxuXHJcbiAgICAgICY6Oi13ZWJraXQtc2Nyb2xsYmFyIHtcclxuICAgICAgICB3aWR0aDogNnB4O1xyXG4gICAgICB9XHJcblxyXG4gICAgICAmOjotd2Via2l0LXNjcm9sbGJhci10cmFjayB7XHJcbiAgICAgICAgYmFja2dyb3VuZDogI2YxZjFmMTtcclxuICAgICAgICBib3JkZXItcmFkaXVzOiAzcHg7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgICY6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHtcclxuICAgICAgICBiYWNrZ3JvdW5kOiAjYzFjMWMxO1xyXG4gICAgICAgIGJvcmRlci1yYWRpdXM6IDNweDtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC5pbnB1dC1maWVsZCB7XHJcbiAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuICAgICAgbWFyZ2luLWJvdHRvbTogMTZweDtcclxuXHJcbiAgICAgICYuaGlkZGVuIHtcclxuICAgICAgICBkaXNwbGF5OiBub25lO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAuaW5wdXQtbGFiZWwge1xyXG4gICAgICAgIGRpc3BsYXk6IGJsb2NrO1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMTJweDtcclxuICAgICAgICBmb250LXdlaWdodDogNTAwO1xyXG4gICAgICAgIGNvbG9yOiAjNkI3MjgwO1xyXG4gICAgICAgIG1hcmdpbi1ib3R0b206IDhweDtcclxuICAgICAgfVxyXG5cclxuICAgICAgLnRleHQtaW5wdXQtY29udGFpbmVyIHtcclxuICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcblxyXG4gICAgICAgIC5pbnB1dC10ZXh0YXJlYSB7XHJcbiAgICAgICAgICB3aWR0aDogMTAwJTtcclxuICAgICAgICAgIG1pbi1oZWlnaHQ6IDgwcHg7XHJcbiAgICAgICAgICBwYWRkaW5nOiAxMnB4IDUwcHggMTJweCAxNnB4O1xyXG4gICAgICAgICAgYm9yZGVyOiAycHggc29saWQgI0U1RTdFQjtcclxuICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDEycHg7XHJcbiAgICAgICAgICBmb250LXNpemU6IDE0cHg7XHJcbiAgICAgICAgICByZXNpemU6IHZlcnRpY2FsO1xyXG4gICAgICAgICAgYmFja2dyb3VuZDogd2hpdGU7XHJcblxyXG4gICAgICAgICAgJjpmb2N1cyB7XHJcbiAgICAgICAgICAgIG91dGxpbmU6IG5vbmU7XHJcbiAgICAgICAgICAgIGJvcmRlci1jb2xvcjogIzFBNDZBNztcclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAmOjpwbGFjZWhvbGRlciB7XHJcbiAgICAgICAgICAgIGNvbG9yOiAjOUNBM0FGO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgLmltYWdlLWlucHV0LWNvbnRhaW5lciB7XHJcbiAgICAgICAgLmZpbGUtdXBsb2FkLWFyZWEge1xyXG4gICAgICAgICAgd2lkdGg6IDEwMCU7XHJcbiAgICAgICAgICBtaW4taGVpZ2h0OiA4MHB4O1xyXG4gICAgICAgICAgYm9yZGVyOiAycHggZGFzaGVkICNFNUU3RUI7XHJcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiAxMnB4O1xyXG4gICAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgICAgICAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlO1xyXG4gICAgICAgICAgYmFja2dyb3VuZDogd2hpdGU7XHJcblxyXG4gICAgICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgICAgIGJvcmRlci1jb2xvcjogIzFBNDZBNztcclxuICAgICAgICAgICAgYmFja2dyb3VuZDogI0Y4RkFGQztcclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAudXBsb2FkLWljb24ge1xyXG4gICAgICAgICAgICBtYXJnaW4tYm90dG9tOiA4cHg7XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgcCB7XHJcbiAgICAgICAgICAgIG1hcmdpbjogMDtcclxuICAgICAgICAgICAgY29sb3I6ICM2QjcyODA7XHJcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTRweDtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC5pbnB1dC1hY3Rpb25zIHtcclxuICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgICAgICAgcmlnaHQ6IDEycHg7XHJcbiAgICAgICAgYm90dG9tOiAxMnB4O1xyXG4gICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgZ2FwOiA4cHg7XHJcblxyXG4gICAgICAgIC5zZW5kLWljb24ge1xyXG4gICAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICAgICAgICAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuMnMgZWFzZTtcclxuXHJcbiAgICAgICAgICAmOmhvdmVyIHtcclxuICAgICAgICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxLjEpO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC50b2dnbGUtaW5wdXRzIHtcclxuICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG4gICAgICBtYXJnaW4tdG9wOiAxNnB4O1xyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLm5vLWlucHV0LW1lc3NhZ2Uge1xyXG4gIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICBwYWRkaW5nOiAyNHB4O1xyXG4gIGJhY2tncm91bmQ6ICNGOEZBRkM7XHJcbiAgYm9yZGVyLXJhZGl1czogMTJweDtcclxuICBtYXJnaW4tYm90dG9tOiAxNnB4O1xyXG5cclxuICBwIHtcclxuICAgIGNvbG9yOiAjNkI3MjgwO1xyXG4gICAgZm9udC1zaXplOiAxNHB4O1xyXG4gICAgbWFyZ2luOiAwIDAgMTZweCAwO1xyXG4gIH1cclxufVxyXG5cclxuLmluZm8tbWVzc2FnZSB7XHJcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG4gIG1hcmdpbi1ib3R0b206IDI0cHg7XHJcblxyXG4gIHAge1xyXG4gICAgY29sb3I6ICMxQTQ2QTc7XHJcbiAgICBmb250LXNpemU6IDE0cHg7XHJcbiAgICBtYXJnaW46IDA7XHJcbiAgfVxyXG59XHJcblxyXG4uZXhlY3V0ZS1zZWN0aW9uIHtcclxuICBtYXJnaW4tdG9wOiBhdXRvO1xyXG4gIHBhZGRpbmctdG9wOiAyNHB4O1xyXG59XHJcbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n  return WorkflowExecutionComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "Subject", "takeUntil", "FormsModule", "ReactiveFormsModule", "Validators", "ChatInterfaceComponent", "AgentActivityComponent", "AgentOutputComponent", "ButtonComponent", "IconComponent", "TabsComponent", "AvaStepperComponent", "environment", "workflowConstants", "ExecutionStatus", "i0", "ɵɵelementStart", "ɵɵlistener", "WorkflowExecutionComponent_div_16_div_9_ng_container_8_div_4_Template_textarea_keydown_enter_1_listener", "ɵɵrestoreView", "_r3", "input_r4", "ɵɵnextContext", "$implicit", "ctx_r1", "ɵɵresetView", "submitCurrentInput", "ɵɵelementEnd", "WorkflowExecutionComponent_div_16_div_9_ng_container_8_div_4_Template_ava_icon_click_3_listener", "ɵɵadvance", "ɵɵproperty", "input", "WorkflowExecutionComponent_div_16_div_9_ng_container_8_div_5_div_6_Template_ava_icon_click_1_listener", "_r6", "WorkflowExecutionComponent_div_16_div_9_ng_container_8_div_5_Template_div_click_1_listener", "_r5", "triggerFileInput", "ɵɵelement", "ɵɵtext", "WorkflowExecutionComponent_div_16_div_9_ng_container_8_div_5_Template_input_change_5_listener", "$event", "handleFileUpload", "ɵɵtemplate", "WorkflowExecutionComponent_div_16_div_9_ng_container_8_div_5_div_6_Template", "tmp_7_0", "workflowForm", "get", "value", "ɵɵelementContainerStart", "WorkflowExecutionComponent_div_16_div_9_ng_container_8_div_4_Template", "WorkflowExecutionComponent_div_16_div_9_ng_container_8_div_5_Template", "ɵɵclassProp", "i_r7", "showAllInputsForCurrentAgent", "ɵɵtextInterpolate", "placeholder", "isImageInput", "WorkflowExecutionComponent_div_16_div_9_div_9_Template_ava_button_click_1_listener", "_r8", "toggleShowAllInputs", "getCurrentAgentInputs", "length", "WorkflowExecutionComponent_div_16_div_9_ng_container_8_Template", "WorkflowExecutionComponent_div_16_div_9_div_9_Template", "WorkflowExecutionComponent_div_16_div_10_Template_ava_button_click_3_listener", "_r9", "moveToNextAgent", "WorkflowExecutionComponent_div_16_div_14_Template_ava_button_click_1_listener", "_r10", "executeWorkflow", "isInputValid", "ɵɵpureFunction0", "_c1", "WorkflowExecutionComponent_div_16_Template_ava_icon_click_8_listener", "_r1", "toggleCurrentAgentExpansion", "WorkflowExecutionComponent_div_16_div_9_Template", "WorkflowExecutionComponent_div_16_div_10_Template", "WorkflowExecutionComponent_div_16_div_14_Template", "isCurrentAgentCompleted", "currentAgent", "agent", "name", "isCurrentAgentExpanded", "isAllInputsCompleted", "WorkflowExecutionComponent_div_24_Template_app_agent_activity_saveLogs_1_listener", "_r11", "saveLogs", "WorkflowExecutionComponent_div_24_Template_app_agent_activity_controlAction_1_listener", "handleControlAction", "WorkflowExecutionComponent_div_24_Template_app_agent_activity_onOutPutBtnClick_1_listener", "onTabChange", "id", "label", "workflowLogs", "executionDetails", "progress", "isRunning", "status", "WorkflowExecutionComponent_div_25_Template_app_agent_output_export_1_listener", "_r12", "exportResults", "taskMessage", "WorkflowExecutionComponent", "route", "router", "workflowService", "tokenStorage", "loaderService", "formBuilder", "navigationTabs", "disabled", "workflowId", "workflowName", "constants", "chatInterfaceComp", "activityLogs", "activityProgress", "notStarted", "chatMessages", "isProcessingChat", "inputText", "agentOutputs", "fileType", "executionStartTime", "executionCompleted", "executionId", "enableStreamingLog", "enableLogStreaming", "isExecutionComplete", "progressInterval", "destroy$", "selectedTab", "demoTabs", "errorMsg", "resMessage", "isJsonValid", "disable<PERSON>hat", "selectedFiles", "workflowAgents", "userInputList", "isLoading", "loaderColor", "inputFieldOrder", "currentInputIndex", "activeTabId", "isLeftPanelCollapsed", "expandedAgents", "agentStatuses", "stepperSteps", "currentStepIndex", "agentInputsMap", "constructor", "ngOnInit", "disable<PERSON><PERSON><PERSON>", "crypto", "randomUUID", "paramMap", "pipe", "subscribe", "params", "loadWorkflow", "navigate", "ngOnDestroy", "next", "complete", "<PERSON><PERSON><PERSON><PERSON>", "event", "console", "log", "from", "text", "group", "getWorkflowById", "res", "pipeline", "pipeLineAgents", "initializeStepper", "initializeAgentInputs", "initializeForm", "initializeAgentStates", "setCurrentAgent", "startInputCollection", "error", "err", "loadDemoWorkflow", "addAiResponse", "match", "variableName", "trim", "startsWith", "for<PERSON>ach", "addControl", "control", "required", "startFakeProgress", "setInterval", "stopFakeProgress", "clearInterval", "setTimeout", "handleChatMessage", "message", "field", "setValue", "promptForCurrentField", "section", "data", "map", "timestamp", "join", "downloadAsFile", "JSON", "stringify", "filename", "type", "blob", "Blob", "url", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "click", "revokeObjectURL", "action", "navigateBack", "editWorkflow", "logExecutionStatus", "delay", "workflowExecProcessing", "push", "content", "color", "getWorkflowLogs", "workflowLogConnect", "validate<PERSON><PERSON>", "output", "parsedOutput", "parse", "e", "payload", "FormData", "queryString", "running", "file", "append", "getDaUsername", "pipeLineId", "userInputs", "user", "workflowResponse", "workflowExecComplete", "tasksOutputs", "task", "title", "<PERSON><PERSON><PERSON>", "description", "expected_output", "summary", "raw", "completed", "detail", "workflowLogDisconnect", "workflowLogFailed", "handleAttachment", "onAttachmentsSelected", "files", "onImageSelected", "Object", "keys", "controls", "reader", "FileReader", "onload", "base64String", "result", "readAsDataURL", "toggleLeftPanel", "toggleAgentExpansion", "index", "Array", "fill", "active", "onStepChange", "stepIndex", "step", "i", "agentInputs", "extractInputFieldForAgent", "values", "inputs", "PLACEHOLDER_PATTERNS", "placeholderM<PERSON>", "agentDescription", "matches", "matchAll", "placeholderInput", "Set", "add", "entries", "agentId", "getCombinedTools", "tools", "userTools", "tool", "toolName", "handleInputSubmit", "key", "shift<PERSON>ey", "preventDefault", "formControl", "areCurrentAgentInputsComplete", "inputName", "target", "fileInput", "getElementById", "currentInputs", "every", "valid", "serial", "role", "llm", "modelDeploymentName", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "WorkflowService", "i3", "TokenStorageService", "LoaderService", "i4", "FormBuilder", "selectors", "viewQuery", "WorkflowExecutionComponent_Query", "rf", "ctx", "WorkflowExecutionComponent_Template_ava_icon_click_11_listener", "WorkflowExecutionComponent_Template_ava_icon_click_12_listener", "WorkflowExecutionComponent_Template_ava_stepper_stepChange_15_listener", "WorkflowExecutionComponent_div_16_Template", "WorkflowExecutionComponent_Template_ava_tabs_tabChange_21_listener", "WorkflowExecutionComponent_div_24_Template", "WorkflowExecutionComponent_div_25_Template", "_c0", "i5", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\workflows\\workflow-execution\\workflow-execution.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\pages\\workflows\\workflow-execution\\workflow-execution.component.html"], "sourcesContent": ["import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';\r\n\r\n// Import child components\r\nimport { ChatInterfaceComponent } from '@shared/components/chat-interface/chat-interface.component';\r\nimport { ChatMessage } from '@shared/components/chat-window/chat-window.component';\r\nimport {\r\n  AgentActivityComponent,\r\n} from './components/agent-activity/agent-activity.component';\r\nimport {\r\n  AgentOutputComponent,\r\n  AgentOutput as OutputItem,\r\n} from './components/agent-output/agent-output.component';\r\nimport {\r\n  ButtonComponent,\r\n  IconComponent,\r\n  TabItem,\r\n  TabsComponent,\r\n  AvaStepperComponent,\r\n} from '@ava/play-comp-library';\r\nimport { WorkflowService } from '@shared/services/workflow.service';\r\nimport { environment } from '@shared/environments/environment';\r\nimport workflowConstants from './../constants/workflows.json';\r\nimport { TokenStorageService, LoaderService } from '@shared/index';\r\nimport { AvaTab } from '@shared/models/tab.model';\r\nimport { ExecutionStatus, ActivityLog } from '@shared/models/execution.model';\r\nimport { AgentActivityExecutionDetails } from './components/agent-activity/agent-activity.component';\r\n\r\n\r\n@Component({\r\n  selector: 'app-workflow-execution',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    ChatInterfaceComponent,\r\n    AgentActivityComponent,\r\n    AgentOutputComponent,\r\n    TabsComponent,\r\n    ButtonComponent,\r\n    IconComponent,\r\n    AvaStepperComponent,\r\n  ],\r\n  templateUrl: './workflow-execution.component.html',\r\n  styleUrls: ['./workflow-execution.component.scss'],\r\n})\r\nexport class WorkflowExecutionComponent implements OnInit, OnDestroy {\r\n  navigationTabs: TabItem[] = [\r\n    { id: 'nav-home', label: 'Agent Activity' },\r\n    { id: 'nav-products', label: 'Agent Output' },\r\n    { id: 'nav-services', label: 'Preview', disabled: true },\r\n  ];\r\n  // Workflow details\r\n  workflowId: string | null = null;\r\n  workflowName: string = 'Workflow';\r\n\r\n  constants = workflowConstants as Record<string, any>;\r\n\r\n  @ViewChild(ChatInterfaceComponent, { static: false })\r\n  chatInterfaceComp!: ChatInterfaceComponent;\r\n\r\n  // Activity logs\r\n  activityLogs: ActivityLog[] = [];\r\n  activityProgress: number = 0;\r\n  executionDetails?: AgentActivityExecutionDetails;\r\n  isRunning: boolean = false;\r\n  status: ExecutionStatus = ExecutionStatus.notStarted;\r\n\r\n  // Chat messages\r\n  chatMessages: ChatMessage[] = [];\r\n  isProcessingChat: boolean = false;\r\n  inputText = '';\r\n\r\n  // Agent outputs\r\n  agentOutputs: OutputItem[] = [];\r\n  public workflowForm!: FormGroup;\r\n  public fileType : string = '.zip';\r\n\r\n  // Execution state\r\n  executionStartTime: Date | null = null;\r\n  executionCompleted: boolean = false;\r\n  executionId!: string;\r\n\r\n  public workflowLogs: any[] = [];\r\n  enableStreamingLog = environment.enableLogStreaming || 'all';\r\n\r\n  public isExecutionComplete: boolean = false;\r\n  progressInterval: any;\r\n\r\n  // Component lifecycle\r\n  private destroy$ = new Subject<void>();\r\n  selectedTab: string = 'Agent Activity';\r\n  demoTabs: AvaTab[] = [\r\n    { id: 'activity', label: 'Agent Activity' },\r\n    { id: 'agents', label: 'Agent Output' },\r\n    { id: 'preview', label: 'Preview', disabled: true },\r\n  ];\r\n  errorMsg = false;\r\n  resMessage: any;\r\n  taskMessage = [];\r\n  isJsonValid = false;\r\n  disableChat : boolean = false;\r\n  selectedFiles: File[] = [];\r\n  workflowAgents: any[] = [];\r\n  userInputList: any[] = [];\r\n  progress = 0;\r\n  isLoading = false;\r\n  loaderColor: string = '';\r\n\r\n  inputFieldOrder: string[] = [];\r\n  currentInputIndex: number = 0;\r\n  activeTabId: string = 'nav-home';\r\n\r\n  // Panel state\r\n  isLeftPanelCollapsed = false;\r\n  expandedAgents: boolean[] = [];\r\n  agentStatuses: { completed: boolean }[] = [];\r\n\r\n  // Stepper state\r\n  stepperSteps: any[] = [];\r\n  currentStepIndex = 0;\r\n  currentAgent: any = null;\r\n  isCurrentAgentExpanded = false;\r\n  showAllInputsForCurrentAgent = false;\r\n  agentInputsMap: { [agentId: string]: any[] } = {};\r\n  \r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private workflowService: WorkflowService,\r\n    private tokenStorage: TokenStorageService,\r\n    private loaderService: LoaderService,\r\n    private formBuilder: FormBuilder,\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loaderService.disableLoader();\r\n    this.selectedTab = 'Agent Activity';\r\n    this.executionId = crypto.randomUUID();\r\n    // Get workflow ID from route params\r\n    this.route.paramMap.pipe(takeUntil(this.destroy$)).subscribe((params) => {\r\n      this.workflowId = params.get('id');\r\n      if (this.workflowId) {\r\n        this.loadWorkflow(this.workflowId);\r\n      } else {\r\n        // No workflow ID, redirect back to workflows page\r\n        this.router.navigate(['/build/workflows']);\r\n      }\r\n    });\r\n    // this.executeWorkflow()\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n    this.loaderService.enableLoader();\r\n  }\r\n  onTabChange(event: { id: string, label: string }) {\r\n    this.selectedTab = event.label;\r\n    this.activeTabId = event.id;\r\n    console.log('Tab changed:', event);\r\n  }\r\n\r\n  // Load workflow data\r\n  loadWorkflow(id: string): void {\r\n    // In a real app, this would fetch the workflow from a service\r\n    console.log(`Loading workflow with ID: ${id}`);\r\n    this.chatMessages = [\r\n      {\r\n        from: 'ai',\r\n        text: 'I am your workflow assistant. I will help you in executing this workflow.',\r\n      } as ChatMessage,\r\n    ]\r\n    this.workflowForm = this.formBuilder.group({});\r\n\r\n    this.workflowService.getWorkflowById(id).subscribe({\r\n        next: (res) => {\r\n        this.workflowAgents = res.pipeline.pipeLineAgents;\r\n        this.workflowName = res.pipeline.name;\r\n        this.initializeStepper();\r\n        this.initializeAgentInputs();\r\n        this.initializeForm();\r\n        this.initializeAgentStates();\r\n        this.setCurrentAgent(0);\r\n        this.startInputCollection();\r\n      },\r\n        error: (err) => {\r\n          // Fallback to demo data for testing\r\n          this.loadDemoWorkflow();\r\n          this.disableChat = true;\r\n          this.chatInterfaceComp.addAiResponse('No input is required for this workflow. Click on send button to execute the workflow.');\r\n          console.log(err);\r\n        }\r\n    });\r\n\r\n  }\r\n\r\n  public isImageInput(input: string): boolean {\r\n    const match = input.match(/{{(.*?)}}/);\r\n    if (match && match[1]) {\r\n      const variableName = match[1].trim();\r\n      return variableName.startsWith('image') || variableName.startsWith('Image');\r\n    }\r\n    return false;\r\n  }\r\n\r\n  public initializeForm() {\r\n    this.userInputList.forEach((label: any) => {\r\n      this.workflowForm.addControl(label.input, this.formBuilder.control('', Validators.required));\r\n    });\r\n  }\r\n\r\n  startFakeProgress() {\r\n    this.progress = 0;\r\n    this.progressInterval = setInterval(() => {\r\n      if (this.progress < 90) {\r\n        this.progress += 5; // Increase slowly\r\n      }\r\n    }, 200); // Adjust speed\r\n  }\r\n\r\n  stopFakeProgress() {\r\n    clearInterval(this.progressInterval);\r\n    this.progress = 100;\r\n\r\n    setTimeout(() => {\r\n      this.isLoading = false;\r\n    }, 500); // Small delay to let user see 100%\r\n  }\r\n\r\n  // Handle new chat message from user\r\n  handleChatMessage(message: string): void {\r\n    // console.log('message ', message, 'is blank', message.trim() === '');\r\n    this.isProcessingChat = true;\r\n    if(message.trim() === ''){\r\n      if(this.inputFieldOrder.length === 0){\r\n        this.chatInterfaceComp.addAiResponse('Executing the workflow...');\r\n        this.executeWorkflow();\r\n      }\r\n      return;\r\n    }\r\n\r\n    if(this.isExecutionComplete || this.currentInputIndex===this.inputFieldOrder.length){\r\n        this.chatInterfaceComp.addAiResponse('Executing the workflow...');\r\n        this.executeWorkflow();\r\n      return;\r\n    }\r\n\r\n    const field = this.inputFieldOrder[this.currentInputIndex];\r\n    if (this.isImageInput(field)) {\r\n      // Ignore text input, wait for file input\r\n      this.chatInterfaceComp.addAiResponse(`Please upload an image file for ${field}`);\r\n      return;\r\n    }\r\n\r\n    this.workflowForm.get(field)?.setValue(message);\r\n    this.currentInputIndex++;\r\n\r\n    if (this.currentInputIndex < this.inputFieldOrder.length) {\r\n      this.promptForCurrentField();\r\n    } else {\r\n      this.chatInterfaceComp.addAiResponse('Thank you for the input! Executing the workflow...');\r\n      this.executeWorkflow();\r\n    }\r\n  }\r\n\r\n  // Save execution logs\r\n  saveLogs(): void {\r\n    console.log('Saving execution logs...');\r\n    // This would typically save to a service\r\n  }\r\n\r\n  // Export results\r\n  exportResults(section: 'activity' | 'output'): void {\r\n    console.log(`Exporting ${section} data...`);\r\n\r\n    if (section === 'activity') {\r\n      const data = this.activityLogs\r\n        .map((log) => `[${log.timestamp}] ${log.message}`)\r\n        .join('\\n');\r\n      this.downloadAsFile(data, 'workflow-activity-logs.txt', 'text/plain');\r\n    } else {\r\n      const data = JSON.stringify(this.agentOutputs, null, 2);\r\n      this.downloadAsFile(data, 'workflow-outputs.json', 'application/json');\r\n    }\r\n  }\r\n\r\n  // Helper method to download data as a file\r\n  private downloadAsFile(data: string, filename: string, type: string): void {\r\n    const blob = new Blob([data], { type });\r\n    const url = URL.createObjectURL(blob);\r\n    const link = document.createElement('a');\r\n    link.href = url;\r\n    link.download = filename;\r\n    link.click();\r\n    URL.revokeObjectURL(url);\r\n  }\r\n\r\n  // Handle controls for execution\r\n  handleControlAction(action: 'play' | 'pause' | 'stop'): void {\r\n    console.log(`Control action: ${action}`);\r\n    // In a real app, this would control the workflow execution\r\n\r\n    if (action === 'play') {\r\n      this.isRunning = true;\r\n    } else if (action === 'pause' || action === 'stop') {\r\n      this.isRunning = false;\r\n    }\r\n  }\r\n\r\n  // Navigate back to workflow listing\r\n  navigateBack(): void {\r\n    this.router.navigate(['/build/workflows']);\r\n  }\r\n\r\n  // Navigate to edit workflow\r\n  editWorkflow(): void {\r\n    if (this.workflowId) {\r\n      this.router.navigate(['/build/workflows/edit', this.workflowId]);\r\n    }\r\n  }\r\n\r\n  public logExecutionStatus(delay: number = 2000) {\r\n    setTimeout(() => {\r\n      if (!this.isExecutionComplete) {\r\n        console.log(this.constants);\r\n        console.log(this.constants['labels'].workflowExecProcessing);\r\n        this.workflowLogs.push({\r\n          content: this.constants['labels'].workflowExecProcessing,\r\n          color: '#F9DB24',\r\n        });\r\n      }\r\n    }, delay);\r\n  }\r\n\r\n  // public parseAnsiString(ansiString: string) {\r\n  //   const regex = ansiRegex();\r\n  //   const parts = ansiString.split(regex);\r\n  //   const matches = [...ansiString.matchAll(regex)];\r\n  //   parts.forEach((part, index) => {\r\n  //     if (part.trim() !== '') {\r\n  //       let colorCode = matches[index - 1][0];\r\n  //       if (index - 2 >= 0 && matches[index - 2]?.includes('\\u001b[1m')) {\r\n  //         colorCode = `\\u001b[1m${colorCode}`;\r\n  //       }\r\n  //       this.workflowLogs.push({\r\n  //         content: part,\r\n  //         color: this.colorMap[colorCode] || 'white',\r\n  //       });\r\n  //     }\r\n  //   });\r\n  // }\r\n\r\n  public getWorkflowLogs(executionId: string) {\r\n    this.workflowService\r\n      .workflowLogConnect(executionId)\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (message) => {\r\n          console.log('message: ', message);\r\n          const { content, color } = message;\r\n          if (color) {\r\n            this.workflowLogs.push({ content, color });\r\n          } else if (this.enableStreamingLog === 'all') {\r\n            // this.parseAnsiString(content);\r\n          }\r\n        },\r\n        error: (err) => {\r\n          this.workflowLogs.push({\r\n            content: this.constants['workflowLog'],\r\n            color: 'red',\r\n          });\r\n          console.error('WebSocket error:', err);\r\n        },\r\n        complete: () => {\r\n          this.logExecutionStatus();\r\n          console.log('WebSocket connection closed');\r\n        },\r\n      });\r\n  }\r\n\r\n  // public parseAnsiString(ansiString: string) {\r\n  //   const regex = ansiRegex();\r\n  //   const parts = ansiString.split(regex);\r\n  //   const matches = [...ansiString.matchAll(regex)];\r\n  //   parts.forEach((part, index) => {\r\n  //     if (part.trim() !== '') {\r\n  //       let colorCode = matches[index-1][0];\r\n  //       if(index - 2 >= 0 && matches[index-2]?.includes('\\u001b[1m')) {\r\n  //         colorCode = `\\u001b[1m${colorCode}`;\r\n  //       }\r\n  //       this.workflowLogs.push({\r\n  //         content: part, \r\n  //         color: this.colorMap[colorCode] || 'white', \r\n  //       });\r\n  //     }\r\n  //   });\r\n  // }\r\n\r\n  public validateJson(output: string): any | null {\r\n    this.isJsonValid = false;\r\n    try {\r\n      const parsedOutput = JSON.parse(output);\r\n      this.isJsonValid = true;\r\n      return parsedOutput;\r\n    } catch (e) {\r\n      return null;\r\n    }\r\n  }\r\n\r\n  public executeWorkflow() {\r\n    let payload: FormData | Record<string, any> = new FormData();\r\n    let queryString = '';\r\n\r\n\r\n    this.status = ExecutionStatus.running;\r\n    if (this.selectedFiles.length) {\r\n      this.selectedFiles.forEach((file) => {\r\n        payload.append('files', file);\r\n      });\r\n      payload.append('workflowId', this.workflowId);\r\n      payload.append('userInputs', JSON.stringify(this.workflowForm.value));\r\n      payload.append('user', this.tokenStorage.getDaUsername());\r\n      payload.append('executionId', this.executionId);\r\n      queryString = '/files';\r\n    } else {\r\n      payload = {\r\n        pipeLineId: this.workflowId,\r\n        userInputs: this.workflowForm.value,\r\n        executionId: this.executionId,\r\n        user: this.tokenStorage.getDaUsername(),\r\n      };\r\n    }\r\n\r\n    this.getWorkflowLogs(this.executionId);\r\n    this.startFakeProgress();\r\n\r\n    this.workflowService\r\n      .executeWorkflow(payload, queryString)\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (res) => {\r\n          this.isProcessingChat = false;\r\n          this.isRunning = false;\r\n          this.chatInterfaceComp.addAiResponse(res?.message || \"Workflow execution completed successfully!\");\r\n\r\n          if (res?.workflowResponse?.pipeline?.output) {\r\n            this.isExecutionComplete = true;\r\n            // console.log(this.constants['labels'].workflowExecComplete);\r\n            this.workflowLogs.push({\r\n              content: this.constants['labels'].workflowExecComplete,\r\n              color: '#0F8251',\r\n            });\r\n            this.errorMsg = false;\r\n            this.resMessage = res?.workflowResponse?.pipeline?.output;\r\n            this.agentOutputs = res?.workflowResponse?.pipeline?.tasksOutputs.map((task: any) => {\r\n              return {\r\n                id: task?.id || '',\r\n                title: task?.title || '',\r\n                content: task?.content || '',\r\n                agentName: task?.agentName || '',\r\n                timestamp: task?.timestamp || '',\r\n                type: task?.type || '',\r\n                description: task?.description || '',\r\n                expected_output: task?.expected_output || '',\r\n                summary: task?.summary || '',\r\n                raw: task?.raw || '',\r\n              };\r\n            })\r\n\r\n            this.taskMessage = res?.workflowResponse?.pipeline?.tasksOutputs.map(\r\n              (task: {\r\n                description: any;\r\n                summary: any;\r\n                raw: any;\r\n                expected_output: any;\r\n              }) => {\r\n                return {\r\n                  description: task.description,\r\n                  summary: task.summary,\r\n                  raw: task.raw,\r\n                  expected_output: task.expected_output,\r\n                };\r\n              },\r\n            );\r\n\r\n            // if(\"file_download_url\" in res?.pipeline){\r\n            //   this.isFileWriter = true;\r\n            //   this.fileDownloadLink = res?.pipeline?.file_download_url;\r\n\r\n            //   if(!this.fileDownloadLink){\r\n            //     this.fileDownloadUrlError = [];\r\n            //     this.fileDownloadUrlError.push(\"Output file is not generated yet!\")\r\n            //   }\r\n            // }\r\n            // this.isAccordian = true\r\n          }\r\n          this.validateJson(this.resMessage);\r\n          this.status = ExecutionStatus.completed;\r\n          this.stopFakeProgress();\r\n          this.selectedFiles = [];\r\n        },\r\n        error: (error) => {\r\n          this.isExecutionComplete = true;\r\n          this.isProcessingChat = false;\r\n          this.errorMsg = true;\r\n          this.resMessage = error?.error?.detail;\r\n          this.workflowService.workflowLogDisconnect();\r\n          this.workflowLogs.push({\r\n            content: this.constants['labels'].workflowLogFailed,\r\n            color: 'red',\r\n          });\r\n          this.chatInterfaceComp.addAiResponse(\r\n            'Something went wrong, Workflow execution has failed.',\r\n          );\r\n          this.selectedFiles = [];\r\n          this.stopFakeProgress();\r\n          console.log('error is', error.message);\r\n        },\r\n      });\r\n  }\r\n\r\n  // public asyncExecutePipeline() {\r\n  //   const payload: FormData = new FormData();\r\n  //   if (this.selectedFiles?.length) {\r\n  //     for (const element of this.selectedFiles) {\r\n  //       payload.append('files', element)\r\n  //     }\r\n  //   }\r\n  //   payload.append('pipeLineId', String(this.workflowId));\r\n  //   payload.append('userInputs', JSON.stringify(this.workflowForm.value));\r\n  //   payload.append('user', this.tokenStorage.getDaUsername() || '');\r\n  //   payload.append('executionId', this.executionId);\r\n\r\n  //   this.workflowService.asyncExecutePipeline(payload).pipe().subscribe({\r\n  //     next: (res: any) => {\r\n  //       if(res) {\r\n  //         // res handling\r\n  //         console.log(res);\r\n  //       }\r\n  //     },\r\n  //     error: e => {\r\n  //       // error handling\r\n  //       console.log(e);\r\n  //     }\r\n  //   })\r\n  // }\r\n\r\n  handleAttachment() {\r\n    console.log('handleAttachment');\r\n  }\r\n\r\n  onAttachmentsSelected(files: File[]) {\r\n    if(this.currentInputIndex===this.inputFieldOrder.length || this.inputFieldOrder.length===0){\r\n      this.selectedFiles = files;\r\n      return;\r\n    }\r\n\r\n    const field = this.inputFieldOrder[this.currentInputIndex];\r\n    if(this.isImageInput(field)){\r\n      if (files && files.length > 0) {\r\n        this.onImageSelected(files[0]);\r\n      }\r\n    } else {\r\n      this.selectedFiles = files;\r\n    }\r\n  }\r\n\r\n  startInputCollection(){\r\n    this.inputFieldOrder = Object.keys(this.workflowForm.controls);\r\n    this.currentInputIndex = 0;\r\n    if (this.inputFieldOrder.length > 0) {\r\n      this.promptForCurrentField();\r\n    }\r\n    else{\r\n      this.disableChat = true;\r\n      this.chatInterfaceComp.addAiResponse('No input is required for this workflow. Click on send button to execute the workflow.');\r\n    }\r\n  }\r\n\r\n  promptForCurrentField() {\r\n    const field = this.inputFieldOrder[this.currentInputIndex];\r\n    if (this.isImageInput(field)) {\r\n      this.fileType = '.jpeg,.png,.jpg,.svg';\r\n      this.chatInterfaceComp.addAiResponse(`Please upload an image for ${field}`);\r\n      // UI should now show a file input for the user\r\n    } else {\r\n      this.fileType = '.zip'; // or whatever default you want for non-image\r\n      this.chatInterfaceComp.addAiResponse(`Please enter the value of ${field}`);\r\n    }\r\n  }\r\n\r\n  onImageSelected(file: File) {\r\n    const field = this.inputFieldOrder[this.currentInputIndex];\r\n    if (!this.isImageInput(field)) return;\r\n\r\n    const reader = new FileReader();\r\n    reader.onload = () => {\r\n      const base64String = (reader.result as string); \r\n      this.workflowForm.get(field)?.setValue(base64String);\r\n      this.currentInputIndex++;\r\n      if (this.currentInputIndex < this.inputFieldOrder.length) {\r\n        this.promptForCurrentField();\r\n      } else {\r\n        this.chatInterfaceComp.addAiResponse('Thank you! Running the workflow...');\r\n        this.executeWorkflow();\r\n      }\r\n    };\r\n    reader.readAsDataURL(file);\r\n  }\r\n\r\n  // Panel management methods\r\n  toggleLeftPanel(): void {\r\n    this.isLeftPanelCollapsed = !this.isLeftPanelCollapsed;\r\n  }\r\n\r\n  toggleAgentExpansion(index: number): void {\r\n    this.expandedAgents[index] = !this.expandedAgents[index];\r\n  }\r\n\r\n  toggleCurrentAgentExpansion(): void {\r\n    this.isCurrentAgentExpanded = !this.isCurrentAgentExpanded;\r\n  }\r\n\r\n  toggleShowAllInputs(): void {\r\n    this.showAllInputsForCurrentAgent = !this.showAllInputsForCurrentAgent;\r\n  }\r\n\r\n  initializeAgentStates(): void {\r\n    this.expandedAgents = new Array(this.workflowAgents.length).fill(false);\r\n    this.agentStatuses = this.workflowAgents.map(() => ({ completed: false }));\r\n  }\r\n\r\n  // Stepper management methods\r\n  initializeStepper(): void {\r\n    this.stepperSteps = this.workflowAgents.map((agent: any, index: number) => ({\r\n      id: index,\r\n      label: agent.agent.name,\r\n      completed: false,\r\n      active: index === 0\r\n    }));\r\n  }\r\n\r\n  onStepChange(stepIndex: number): void {\r\n    this.setCurrentAgent(stepIndex);\r\n  }\r\n\r\n  setCurrentAgent(index: number): void {\r\n    this.currentStepIndex = index;\r\n    this.currentAgent = this.workflowAgents[index];\r\n    this.isCurrentAgentExpanded = false;\r\n    this.showAllInputsForCurrentAgent = false;\r\n\r\n    // Update stepper steps\r\n    this.stepperSteps.forEach((step, i) => {\r\n      step.active = i === index;\r\n    });\r\n  }\r\n\r\n  moveToNextAgent(): void {\r\n    if (this.currentStepIndex < this.workflowAgents.length - 1) {\r\n      // Mark current agent as completed\r\n      this.stepperSteps[this.currentStepIndex].completed = true;\r\n      this.agentStatuses[this.currentStepIndex].completed = true;\r\n\r\n      // Move to next agent\r\n      this.setCurrentAgent(this.currentStepIndex + 1);\r\n    }\r\n  }\r\n\r\n  isCurrentAgentCompleted(): boolean {\r\n    return this.agentStatuses[this.currentStepIndex]?.completed || false;\r\n  }\r\n\r\n  // Agent input management methods\r\n  initializeAgentInputs(): void {\r\n    this.workflowAgents.forEach((agent: any, index: number) => {\r\n      const agentInputs = this.extractInputFieldForAgent(agent);\r\n      this.agentInputsMap[agent.agent.id] = agentInputs;\r\n    });\r\n\r\n    // Create combined user input list for form initialization\r\n    this.userInputList = [];\r\n    Object.values(this.agentInputsMap).forEach(inputs => {\r\n      this.userInputList.push(...inputs);\r\n    });\r\n  }\r\n\r\n  extractInputFieldForAgent(agent: any): any[] {\r\n    const PLACEHOLDER_PATTERNS = /(%\\d+\\$s)|\\{\\{([a-zA-Z0-9-_]+)\\}\\}/g;\r\n    const placeholderMap: { [key: string]: { inputs: Set<string> } } = {};\r\n\r\n    const agentDescription = agent?.agent?.task?.description || '';\r\n    const matches = agentDescription.matchAll(PLACEHOLDER_PATTERNS) || [];\r\n\r\n    for (const match of matches) {\r\n      const placeholder = match[1] || match[2];\r\n      const placeholderInput = match[0];\r\n      if (!placeholderMap[placeholder]) {\r\n        placeholderMap[placeholder] = { inputs: new Set() };\r\n      }\r\n      placeholderMap[placeholder].inputs.add(placeholderInput);\r\n    }\r\n\r\n    return Object.entries(placeholderMap).map(([placeholder, { inputs }]) => ({\r\n      placeholder,\r\n      input: [...inputs][0],\r\n      agentId: agent.agent.id,\r\n      agentName: agent.agent.name\r\n    }));\r\n  }\r\n\r\n  getCurrentAgentInputs(): any[] {\r\n    if (!this.currentAgent) return [];\r\n    return this.agentInputsMap[this.currentAgent.agent.id] || [];\r\n  }\r\n\r\n  getCombinedTools(agent: any): string {\r\n    const tools = [...(agent.tools || []), ...(agent.userTools || [])];\r\n    return tools.map(tool => tool.toolName || tool).join(', ') || 'None';\r\n  }\r\n\r\n  handleInputSubmit(event: KeyboardEvent, input: any): void {\r\n    if (event.key === 'Enter' && !event.shiftKey) {\r\n      event.preventDefault();\r\n      this.submitCurrentInput(input);\r\n    }\r\n  }\r\n\r\n  submitCurrentInput(input: any): void {\r\n    const formControl = this.workflowForm.get(input.input);\r\n    if (formControl && formControl.value && formControl.value.trim()) {\r\n      // Add to chat messages\r\n      this.chatMessages.push({\r\n        from: 'user',\r\n        text: formControl.value\r\n      });\r\n\r\n      // Move to next agent if all inputs for current agent are filled\r\n      if (this.areCurrentAgentInputsComplete()) {\r\n        this.moveToNextAgent();\r\n      }\r\n    }\r\n  }\r\n\r\n  handleFileUpload(event: any, inputName: string): void {\r\n    const file = event.target.files[0];\r\n    if (file) {\r\n      const reader = new FileReader();\r\n      reader.onload = () => {\r\n        const base64String = reader.result as string;\r\n        this.workflowForm.get(inputName)?.setValue(base64String);\r\n\r\n        // Add to chat messages\r\n        this.chatMessages.push({\r\n          from: 'user',\r\n          text: `Uploaded image: ${file.name}`\r\n        });\r\n\r\n        // Move to next agent if all inputs for current agent are filled\r\n        if (this.areCurrentAgentInputsComplete()) {\r\n          this.moveToNextAgent();\r\n        }\r\n      };\r\n      reader.readAsDataURL(file);\r\n    }\r\n  }\r\n\r\n  triggerFileInput(inputName: string): void {\r\n    const fileInput = document.getElementById('file-' + inputName) as HTMLInputElement;\r\n    if (fileInput) {\r\n      fileInput.click();\r\n    }\r\n  }\r\n\r\n  areCurrentAgentInputsComplete(): boolean {\r\n    const currentInputs = this.getCurrentAgentInputs();\r\n    return currentInputs.every(input => {\r\n      const formControl = this.workflowForm.get(input.input);\r\n      return formControl && formControl.value && formControl.value.trim();\r\n    });\r\n  }\r\n\r\n  isAllInputsCompleted(): boolean {\r\n    return this.currentStepIndex === this.workflowAgents.length - 1 && this.areCurrentAgentInputsComplete();\r\n  }\r\n\r\n  // Input validation\r\n  isInputValid(): boolean {\r\n    return this.workflowForm.valid && !!this.workflowId;\r\n  }\r\n\r\n  // Demo workflow for testing\r\n  loadDemoWorkflow(): void {\r\n    this.workflowAgents = [\r\n      {\r\n        serial: 1,\r\n        agent: {\r\n          id: 1,\r\n          name: 'Agent Alpha',\r\n          role: 'Data Processor',\r\n          task: {\r\n            description: 'Process the {{user_input}} and analyze the data'\r\n          },\r\n          llm: {\r\n            modelDeploymentName: 'GPT-4'\r\n          },\r\n          tools: [{ toolName: 'Data Analyzer' }, { toolName: 'Text Processor' }],\r\n          userTools: []\r\n        }\r\n      },\r\n      {\r\n        serial: 2,\r\n        agent: {\r\n          id: 2,\r\n          name: 'Agent Beta',\r\n          role: 'Report Generator',\r\n          task: {\r\n            description: 'Review the analysis and generate {{report_type}} report'\r\n          },\r\n          llm: {\r\n            modelDeploymentName: 'Claude-3'\r\n          },\r\n          tools: [{ toolName: 'Report Generator' }],\r\n          userTools: []\r\n        }\r\n      },\r\n      {\r\n        serial: 3,\r\n        agent: {\r\n          id: 3,\r\n          name: 'Agent Gamma',\r\n          role: 'Output Manager',\r\n          task: {\r\n            description: 'Finalize the output and prepare for delivery'\r\n          },\r\n          llm: {\r\n            modelDeploymentName: 'GPT-4'\r\n          },\r\n          tools: [{ toolName: 'File Manager' }, { toolName: 'Email Service' }],\r\n          userTools: []\r\n        }\r\n      }\r\n    ];\r\n\r\n    this.workflowName = 'Demo Workflow';\r\n    this.initializeStepper();\r\n    this.initializeAgentInputs();\r\n    this.initializeForm();\r\n    this.initializeAgentStates();\r\n    this.setCurrentAgent(0);\r\n\r\n    if (this.userInputList.length === 0) {\r\n      this.disableChat = true;\r\n    }\r\n  }\r\n}\r\n", "<div class=\"workflow-execution-container\">\r\n  <!-- SVG Gradient Definitions for Icons -->\r\n  <svg width=\"0\" height=\"0\" style=\"position: absolute\">\r\n    <defs>\r\n      <linearGradient id=\"gradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\r\n        <stop offset=\"0%\" stop-color=\"#6566CD\" />\r\n        <stop offset=\"100%\" stop-color=\"#F96CAB\" />\r\n      </linearGradient>\r\n    </defs>\r\n  </svg>\r\n  <div class=\"execution-content\" role=\"main\">\r\n    <!-- Left Panel: Workflow Execution Panel -->\r\n    <div\r\n      class=\"column execution-panel-column\"\r\n      [class.collapsed]=\"isLeftPanelCollapsed\"\r\n      role=\"region\"\r\n      aria-label=\"Workflow Execution Panel\"\r\n    >\r\n      <div class=\"column-content execution-panel-content\">\r\n        <div class=\"column-header\">\r\n          <div class=\"header-left\">\r\n            <ava-icon\r\n              iconName=\"arrowLeft\"\r\n              iconColor=\"#1A46A7\"\r\n              class=\"back-icon\"\r\n              (click)=\"navigateBack()\"\r\n            ></ava-icon>\r\n            <ava-icon\r\n              iconName=\"panelLeft\"\r\n              iconColor=\"#1A46A7\"\r\n              class=\"panel-icon\"\r\n              (click)=\"toggleLeftPanel()\"\r\n            ></ava-icon>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"panel-content\" [class.hidden]=\"isLeftPanelCollapsed\">\r\n          <!-- Stepper Section -->\r\n          <div class=\"stepper-section\">\r\n            <ava-stepper\r\n              [steps]=\"stepperSteps\"\r\n              [currentStep]=\"currentStepIndex\"\r\n              [iconColor]=\"'#ffff'\"\r\n              [iconSize]=\"'16'\"\r\n              orientation=\"vertical\"\r\n              size=\"small\"\r\n              [showNavigation]=\"false\"\r\n              (stepChange)=\"onStepChange($event)\">\r\n            </ava-stepper>\r\n          </div>\r\n\r\n          <!-- Current Agent Input Section -->\r\n          <div class=\"current-agent-section\" *ngIf=\"currentAgent\">\r\n            <div class=\"agent-card active\">\r\n              <div class=\"agent-header\">\r\n                <div class=\"status-icon\">\r\n                  <ava-icon\r\n                    [iconName]=\"isCurrentAgentCompleted() ? 'check' : 'folder'\"\r\n                    [iconColor]=\"isCurrentAgentCompleted() ? '#22C55E' : '#1A46A7'\"\r\n                  ></ava-icon>\r\n                </div>\r\n                <div class=\"agent-info\">\r\n                  <h3 class=\"agent-name\">{{ currentAgent?.agent?.name }}</h3>\r\n                  <ava-icon\r\n                    iconName=\"chevronDown\"\r\n                    iconColor=\"#6B7280\"\r\n                    class=\"expand-icon\"\r\n                    [class.expanded]=\"isCurrentAgentExpanded\"\r\n                    (click)=\"toggleCurrentAgentExpansion()\"\r\n                  ></ava-icon>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Agent Details (Expandable) -->\r\n              <!-- <div class=\"agent-details\" [class.expanded]=\"isCurrentAgentExpanded\">\r\n                <div class=\"agent-detail-item\" *ngIf=\"currentAgent?.agent?.llm?.modelDeploymentName\">\r\n                  <span class=\"detail-label\">Model:</span>\r\n                  <span class=\"detail-value\">{{ currentAgent.agent.llm.modelDeploymentName }}</span>\r\n                </div>\r\n                <div class=\"agent-detail-item\" *ngIf=\"currentAgent?.agent?.tools?.length || currentAgent?.agent?.userTools?.length\">\r\n                  <span class=\"detail-label\">Tools:</span>\r\n                  <span class=\"detail-value\">{{ getCombinedTools(currentAgent.agent) }}</span>\r\n                </div>\r\n                <div class=\"agent-detail-item\" *ngIf=\"currentAgent?.agent?.role\">\r\n                  <span class=\"detail-label\">Role:</span>\r\n                  <span class=\"detail-value\">{{ currentAgent.agent.role }}</span>\r\n                </div>\r\n              </div>\r\n            </div> -->\r\n\r\n            <!-- Input Section for Current Agent -->\r\n            <div class=\"input-section\" *ngIf=\"getCurrentAgentInputs().length > 0\">\r\n              <div class=\"input-header\">\r\n                <div class=\"status-icon\">\r\n                  <ava-icon\r\n                    iconName=\"edit\"\r\n                    iconColor=\"#1A46A7\"\r\n                  ></ava-icon>\r\n                </div>\r\n                <h3 class=\"input-title\">Enter the input</h3>\r\n              </div>\r\n\r\n              <div class=\"input-container\" [formGroup]=\"workflowForm\">\r\n                <div class=\"input-scroll-container\">\r\n                  <ng-container *ngFor=\"let input of getCurrentAgentInputs(); let i = index\">\r\n                    <div class=\"input-field\" [class.hidden]=\"i > 0 && !showAllInputsForCurrentAgent\">\r\n                      <label class=\"input-label\">{{ input.placeholder }}</label>\r\n\r\n                      <!-- Text Input -->\r\n                      <div *ngIf=\"!isImageInput(input.input)\" class=\"text-input-container\">\r\n                        <textarea\r\n                          [formControlName]=\"input.input\"\r\n                          [placeholder]=\"'Enter your input here...'\"\r\n                          class=\"input-textarea\"\r\n                          (keydown.enter)=\"submitCurrentInput(input)\"\r\n                        ></textarea>\r\n                        <div class=\"input-actions\">\r\n                          <ava-icon iconName=\"send\" iconColor=\"#1A46A7\" class=\"send-icon\" (click)=\"submitCurrentInput(input)\"></ava-icon>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <!-- Image Input -->\r\n                      <div *ngIf=\"isImageInput(input.input)\" class=\"image-input-container\">\r\n                        <div class=\"file-upload-area\" (click)=\"triggerFileInput(input.input)\">\r\n                          <ava-icon iconName=\"image\" iconColor=\"#6B7280\" class=\"upload-icon\"></ava-icon>\r\n                          <p>Click to upload image</p>\r\n                        </div>\r\n                        <input\r\n                          type=\"file\"\r\n                          [id]=\"'file-' + input.input\"\r\n                          accept=\"image/*\"\r\n                          (change)=\"handleFileUpload($event, input.input)\"\r\n                          style=\"display: none;\"\r\n                        />\r\n                        <div class=\"input-actions\" *ngIf=\"workflowForm.get(input.input)?.value\">\r\n                          <ava-icon iconName=\"send\" iconColor=\"#1A46A7\" class=\"send-icon\" (click)=\"submitCurrentInput(input)\"></ava-icon>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </ng-container>\r\n\r\n                  <!-- Show More/Less Toggle for Multiple Inputs -->\r\n                  <div class=\"toggle-inputs\" *ngIf=\"getCurrentAgentInputs().length > 1\">\r\n                    <ava-button\r\n                      [label]=\"showAllInputsForCurrentAgent ? 'Show Less' : 'Show More (' + (getCurrentAgentInputs().length - 1) + ' more)'\"\r\n                      variant=\"secondary\"\r\n                      size=\"small\"\r\n                      (click)=\"toggleShowAllInputs()\"\r\n                    ></ava-button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- No Input Required Message -->\r\n            <div class=\"no-input-message\" *ngIf=\"getCurrentAgentInputs().length === 0\">\r\n              <p>No input required for this agent</p>\r\n              <ava-button\r\n                label=\"Continue to Next Agent\"\r\n                variant=\"primary\"\r\n                size=\"medium\"\r\n                (click)=\"moveToNextAgent()\"\r\n              ></ava-button>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Info Message -->\r\n          <div class=\"info-message\">\r\n            <p>Agent input needed in order to execute Workflow</p>\r\n          </div>\r\n\r\n          <!-- Execute Button -->\r\n          <div class=\"execute-section\" *ngIf=\"isAllInputsCompleted()\">\r\n            <ava-button\r\n              label=\"▶ Execute Workflow\"\r\n              variant=\"primary\"\r\n              size=\"large\"\r\n              [disabled]=\"!isInputValid()\"\r\n              (click)=\"executeWorkflow()\"\r\n              [customStyles]=\"{\r\n                'background': '#1A46A7',\r\n                'width': '100%',\r\n                'border-radius': '8px'\r\n              }\"\r\n            ></ava-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Right Column: Agent Output -->\r\n    <div class=\"column output-column\" role=\"region\" aria-label=\"Agent Output\">\r\n      <div class=\"column-content\">\r\n        <div class=\"column-header row\">\r\n          <div class=\"col-7\">\r\n            <ava-tabs\r\n              [tabs]=\"navigationTabs\"\r\n              [activeTabId]=\"activeTabId\"\r\n              variant=\"button\"\r\n              buttonShape=\"pill\"\r\n              [showContentPanels]=\"false\"\r\n              (tabChange)=\"onTabChange($event)\"\r\n              ariaLabel=\"Pill navigation tabs\"\r\n            ></ava-tabs>\r\n          </div>\r\n          <div class=\"col-5 right-section-header\">\r\n            <ava-button\r\n              label=\"Send for Approval\"\r\n              variant=\"primary\"\r\n              [customStyles]=\"{\r\n                background:\r\n                  'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n                '--button-effect-color': '33, 90, 214',\r\n                'border-radius': '8px',\r\n                'box-shadow': 'none',\r\n              }\"\r\n              size=\"medium\"\r\n            >\r\n            </ava-button>\r\n            <!-- <ava-button\r\n              label=\"Export\"\r\n              variant=\"secondary\"\r\n              size=\"medium\"\r\n              class=\"ms-2\"\r\n            >\r\n            </ava-button> -->\r\n          </div>\r\n        </div>\r\n        <!-- activity content -->\r\n        <div *ngIf=\"selectedTab === 'Agent Activity'\" style=\"height: 100%\">\r\n          <app-agent-activity\r\n            [activityLogs]=\"workflowLogs\"\r\n            [executionDetails]=\"executionDetails\"\r\n            [progress]=\"progress\"\r\n            [isRunning]=\"isRunning\"\r\n            (saveLogs)=\"saveLogs()\"\r\n            (controlAction)=\"handleControlAction($event)\"\r\n            [status]=\"status\"\r\n            (onOutPutBtnClick)=\"onTabChange({id: 'nav-products', label: 'Agent Output'})\"\r\n          ></app-agent-activity>\r\n        </div>\r\n        <!-- Agent Output Component -->\r\n        <div *ngIf=\"selectedTab === 'Agent Output'\" style=\"height: 100%\">\r\n          <app-agent-output\r\n            [outputs]=\"taskMessage\"\r\n            (export)=\"exportResults('output')\"\r\n          ></app-agent-output>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;AACzC,SAAiCC,WAAW,EAAEC,mBAAmB,EAAEC,UAAU,QAAQ,gBAAgB;AAErG;AACA,SAASC,sBAAsB,QAAQ,4DAA4D;AAEnG,SACEC,sBAAsB,QACjB,sDAAsD;AAC7D,SACEC,oBAAoB,QAEf,kDAAkD;AACzD,SACEC,eAAe,EACfC,aAAa,EAEbC,aAAa,EACbC,mBAAmB,QACd,wBAAwB;AAE/B,SAASC,WAAW,QAAQ,kCAAkC;AAC9D,OAAOC,iBAAiB,MAAM,+BAA+B;AAG7D,SAASC,eAAe,QAAqB,gCAAgC;;;;;;;;;;;;;;;;;;;;;ICkFrDC,EADF,CAAAC,cAAA,cAAqE,mBAMlE;IADCD,EAAA,CAAAE,UAAA,2BAAAC,wGAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,QAAA,GAAAN,EAAA,CAAAO,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAiBD,MAAA,CAAAE,kBAAA,CAAAL,QAAA,CAAyB;IAAA,EAAC;IAC5CN,EAAA,CAAAY,YAAA,EAAW;IAEVZ,EADF,CAAAC,cAAA,cAA2B,mBAC2E;IAApCD,EAAA,CAAAE,UAAA,mBAAAW,gGAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,QAAA,GAAAN,EAAA,CAAAO,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASD,MAAA,CAAAE,kBAAA,CAAAL,QAAA,CAAyB;IAAA,EAAC;IAEvGN,EAFwG,CAAAY,YAAA,EAAW,EAC3G,EACF;;;;IARFZ,EAAA,CAAAc,SAAA,EAA+B;IAC/Bd,EADA,CAAAe,UAAA,oBAAAT,QAAA,CAAAU,KAAA,CAA+B,2CACW;;;;;;IAuB1ChB,EADF,CAAAC,cAAA,cAAwE,mBAC8B;IAApCD,EAAA,CAAAE,UAAA,mBAAAe,sGAAA;MAAAjB,EAAA,CAAAI,aAAA,CAAAc,GAAA;MAAA,MAAAZ,QAAA,GAAAN,EAAA,CAAAO,aAAA,IAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASD,MAAA,CAAAE,kBAAA,CAAAL,QAAA,CAAyB;IAAA,EAAC;IACrGN,EADsG,CAAAY,YAAA,EAAW,EAC3G;;;;;;IAbNZ,EADF,CAAAC,cAAA,cAAqE,cACG;IAAxCD,EAAA,CAAAE,UAAA,mBAAAiB,2FAAA;MAAAnB,EAAA,CAAAI,aAAA,CAAAgB,GAAA;MAAA,MAAAd,QAAA,GAAAN,EAAA,CAAAO,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASD,MAAA,CAAAY,gBAAA,CAAAf,QAAA,CAAAU,KAAA,CAA6B;IAAA,EAAC;IACnEhB,EAAA,CAAAsB,SAAA,mBAA8E;IAC9EtB,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAuB,MAAA,4BAAqB;IAC1BvB,EAD0B,CAAAY,YAAA,EAAI,EACxB;IACNZ,EAAA,CAAAC,cAAA,gBAME;IAFAD,EAAA,CAAAE,UAAA,oBAAAsB,8FAAAC,MAAA;MAAAzB,EAAA,CAAAI,aAAA,CAAAgB,GAAA;MAAA,MAAAd,QAAA,GAAAN,EAAA,CAAAO,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAUD,MAAA,CAAAiB,gBAAA,CAAAD,MAAA,EAAAnB,QAAA,CAAAU,KAAA,CAAqC;IAAA,EAAC;IAJlDhB,EAAA,CAAAY,YAAA,EAME;IACFZ,EAAA,CAAA2B,UAAA,IAAAC,2EAAA,kBAAwE;IAG1E5B,EAAA,CAAAY,YAAA,EAAM;;;;;;IARFZ,EAAA,CAAAc,SAAA,GAA4B;IAA5Bd,EAAA,CAAAe,UAAA,iBAAAT,QAAA,CAAAU,KAAA,CAA4B;IAKFhB,EAAA,CAAAc,SAAA,EAA0C;IAA1Cd,EAAA,CAAAe,UAAA,UAAAc,OAAA,GAAApB,MAAA,CAAAqB,YAAA,CAAAC,GAAA,CAAAzB,QAAA,CAAAU,KAAA,oBAAAa,OAAA,CAAAG,KAAA,CAA0C;;;;;IA9B5EhC,EAAA,CAAAiC,uBAAA,GAA2E;IAEvEjC,EADF,CAAAC,cAAA,cAAiF,gBACpD;IAAAD,EAAA,CAAAuB,MAAA,GAAuB;IAAAvB,EAAA,CAAAY,YAAA,EAAQ;IAgB1DZ,EAbA,CAAA2B,UAAA,IAAAO,qEAAA,kBAAqE,IAAAC,qEAAA,kBAaA;IAgBvEnC,EAAA,CAAAY,YAAA,EAAM;;;;;;;IAjCmBZ,EAAA,CAAAc,SAAA,EAAuD;IAAvDd,EAAA,CAAAoC,WAAA,WAAAC,IAAA,SAAA5B,MAAA,CAAA6B,4BAAA,CAAuD;IACnDtC,EAAA,CAAAc,SAAA,GAAuB;IAAvBd,EAAA,CAAAuC,iBAAA,CAAAjC,QAAA,CAAAkC,WAAA,CAAuB;IAG5CxC,EAAA,CAAAc,SAAA,EAAgC;IAAhCd,EAAA,CAAAe,UAAA,UAAAN,MAAA,CAAAgC,YAAA,CAAAnC,QAAA,CAAAU,KAAA,EAAgC;IAahChB,EAAA,CAAAc,SAAA,EAA+B;IAA/Bd,EAAA,CAAAe,UAAA,SAAAN,MAAA,CAAAgC,YAAA,CAAAnC,QAAA,CAAAU,KAAA,EAA+B;;;;;;IAqBvChB,EADF,CAAAC,cAAA,cAAsE,qBAMnE;IADCD,EAAA,CAAAE,UAAA,mBAAAwC,mFAAA;MAAA1C,EAAA,CAAAI,aAAA,CAAAuC,GAAA;MAAA,MAAAlC,MAAA,GAAAT,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASD,MAAA,CAAAmC,mBAAA,EAAqB;IAAA,EAAC;IAEnC5C,EADG,CAAAY,YAAA,EAAa,EACV;;;;IALFZ,EAAA,CAAAc,SAAA,EAAsH;IAAtHd,EAAA,CAAAe,UAAA,UAAAN,MAAA,CAAA6B,4BAAA,kCAAA7B,MAAA,CAAAoC,qBAAA,GAAAC,MAAA,iBAAsH;;;;;IAnD5H9C,EAFJ,CAAAC,cAAA,cAAsE,cAC1C,cACC;IACvBD,EAAA,CAAAsB,SAAA,mBAGY;IACdtB,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAuB,MAAA,sBAAe;IACzCvB,EADyC,CAAAY,YAAA,EAAK,EACxC;IAGJZ,EADF,CAAAC,cAAA,cAAwD,cAClB;IAuClCD,EAtCA,CAAA2B,UAAA,IAAAoB,+DAAA,2BAA2E,IAAAC,sDAAA,kBAsCL;IAU5EhD,EAFI,CAAAY,YAAA,EAAM,EACF,EACF;;;;IAlDyBZ,EAAA,CAAAc,SAAA,GAA0B;IAA1Bd,EAAA,CAAAe,UAAA,cAAAN,MAAA,CAAAqB,YAAA,CAA0B;IAEnB9B,EAAA,CAAAc,SAAA,GAA4B;IAA5Bd,EAAA,CAAAe,UAAA,YAAAN,MAAA,CAAAoC,qBAAA,GAA4B;IAsChC7C,EAAA,CAAAc,SAAA,EAAwC;IAAxCd,EAAA,CAAAe,UAAA,SAAAN,MAAA,CAAAoC,qBAAA,GAAAC,MAAA,KAAwC;;;;;;IAcxE9C,EADF,CAAAC,cAAA,cAA2E,QACtE;IAAAD,EAAA,CAAAuB,MAAA,uCAAgC;IAAAvB,EAAA,CAAAY,YAAA,EAAI;IACvCZ,EAAA,CAAAC,cAAA,qBAKC;IADCD,EAAA,CAAAE,UAAA,mBAAA+C,8EAAA;MAAAjD,EAAA,CAAAI,aAAA,CAAA8C,GAAA;MAAA,MAAAzC,MAAA,GAAAT,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASD,MAAA,CAAA0C,eAAA,EAAiB;IAAA,EAAC;IAE/BnD,EADG,CAAAY,YAAA,EAAa,EACV;;;;;;IAUNZ,EADF,CAAAC,cAAA,cAA4D,qBAYzD;IANCD,EAAA,CAAAE,UAAA,mBAAAkD,8EAAA;MAAApD,EAAA,CAAAI,aAAA,CAAAiD,IAAA;MAAA,MAAA5C,MAAA,GAAAT,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASD,MAAA,CAAA6C,eAAA,EAAiB;IAAA,EAAC;IAO/BtD,EADG,CAAAY,YAAA,EAAa,EACV;;;;IARFZ,EAAA,CAAAc,SAAA,EAA4B;IAE5Bd,EAFA,CAAAe,UAAA,cAAAN,MAAA,CAAA8C,YAAA,GAA4B,iBAAAvD,EAAA,CAAAwD,eAAA,IAAAC,GAAA,EAM1B;;;;;;IAhIAzD,EAHN,CAAAC,cAAA,cAAwD,cACvB,cACH,cACC;IACvBD,EAAA,CAAAsB,SAAA,mBAGY;IACdtB,EAAA,CAAAY,YAAA,EAAM;IAEJZ,EADF,CAAAC,cAAA,cAAwB,aACC;IAAAD,EAAA,CAAAuB,MAAA,GAA+B;IAAAvB,EAAA,CAAAY,YAAA,EAAK;IAC3DZ,EAAA,CAAAC,cAAA,mBAMC;IADCD,EAAA,CAAAE,UAAA,mBAAAwD,qEAAA;MAAA1D,EAAA,CAAAI,aAAA,CAAAuD,GAAA;MAAA,MAAAlD,MAAA,GAAAT,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASD,MAAA,CAAAmD,2BAAA,EAA6B;IAAA,EAAC;IAG7C5D,EAFK,CAAAY,YAAA,EAAW,EACR,EACF;IAoFRZ,EAhEA,CAAA2B,UAAA,IAAAkC,gDAAA,mBAAsE,KAAAC,iDAAA,kBAgEK;IAS7E9D,EAAA,CAAAY,YAAA,EAAM;IAIJZ,EADF,CAAAC,cAAA,eAA0B,SACrB;IAAAD,EAAA,CAAAuB,MAAA,uDAA+C;IACpDvB,EADoD,CAAAY,YAAA,EAAI,EAClD;IAGNZ,EAAA,CAAA2B,UAAA,KAAAoC,iDAAA,kBAA4D;IAc9D/D,EAAA,CAAAY,YAAA,EAAM;;;;IAjIMZ,EAAA,CAAAc,SAAA,GAA2D;IAC3Dd,EADA,CAAAe,UAAA,aAAAN,MAAA,CAAAuD,uBAAA,wBAA2D,cAAAvD,MAAA,CAAAuD,uBAAA,2BACI;IAI1ChE,EAAA,CAAAc,SAAA,GAA+B;IAA/Bd,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAAwD,YAAA,kBAAAxD,MAAA,CAAAwD,YAAA,CAAAC,KAAA,kBAAAzD,MAAA,CAAAwD,YAAA,CAAAC,KAAA,CAAAC,IAAA,CAA+B;IAKpDnE,EAAA,CAAAc,SAAA,EAAyC;IAAzCd,EAAA,CAAAoC,WAAA,aAAA3B,MAAA,CAAA2D,sBAAA,CAAyC;IAwBrBpE,EAAA,CAAAc,SAAA,EAAwC;IAAxCd,EAAA,CAAAe,UAAA,SAAAN,MAAA,CAAAoC,qBAAA,GAAAC,MAAA,KAAwC;IAgErC9C,EAAA,CAAAc,SAAA,EAA0C;IAA1Cd,EAAA,CAAAe,UAAA,SAAAN,MAAA,CAAAoC,qBAAA,GAAAC,MAAA,OAA0C;IAiB7C9C,EAAA,CAAAc,SAAA,GAA4B;IAA5Bd,EAAA,CAAAe,UAAA,SAAAN,MAAA,CAAA4D,oBAAA,GAA4B;;;;;;IA0D1DrE,EADF,CAAAC,cAAA,cAAmE,6BAUhE;IADCD,EAHA,CAAAE,UAAA,sBAAAoE,kFAAA;MAAAtE,EAAA,CAAAI,aAAA,CAAAmE,IAAA;MAAA,MAAA9D,MAAA,GAAAT,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAYD,MAAA,CAAA+D,QAAA,EAAU;IAAA,EAAC,2BAAAC,uFAAAhD,MAAA;MAAAzB,EAAA,CAAAI,aAAA,CAAAmE,IAAA;MAAA,MAAA9D,MAAA,GAAAT,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CACND,MAAA,CAAAiE,mBAAA,CAAAjD,MAAA,CAA2B;IAAA,EAAC,8BAAAkD,0FAAA;MAAA3E,EAAA,CAAAI,aAAA,CAAAmE,IAAA;MAAA,MAAA9D,MAAA,GAAAT,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAEzBD,MAAA,CAAAmE,WAAA,CAAY;QAAAC,EAAA,EAAK,cAAc;QAAAC,KAAA,EAAS;MAAc,CAAC,CAAC;IAAA,EAAC;IAEjF9E,EADG,CAAAY,YAAA,EAAqB,EAClB;;;;IATFZ,EAAA,CAAAc,SAAA,EAA6B;IAM7Bd,EANA,CAAAe,UAAA,iBAAAN,MAAA,CAAAsE,YAAA,CAA6B,qBAAAtE,MAAA,CAAAuE,gBAAA,CACQ,aAAAvE,MAAA,CAAAwE,QAAA,CAChB,cAAAxE,MAAA,CAAAyE,SAAA,CACE,WAAAzE,MAAA,CAAA0E,MAAA,CAGN;;;;;;IAMnBnF,EADF,CAAAC,cAAA,cAAiE,2BAI9D;IADCD,EAAA,CAAAE,UAAA,oBAAAkF,8EAAA;MAAApF,EAAA,CAAAI,aAAA,CAAAiF,IAAA;MAAA,MAAA5E,MAAA,GAAAT,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAUD,MAAA,CAAA6E,aAAA,CAAc,QAAQ,CAAC;IAAA,EAAC;IAEtCtF,EADG,CAAAY,YAAA,EAAmB,EAChB;;;;IAHFZ,EAAA,CAAAc,SAAA,EAAuB;IAAvBd,EAAA,CAAAe,UAAA,YAAAN,MAAA,CAAA8E,WAAA,CAAuB;;;ADlMnC,WAAaC,0BAA0B;EAAjC,MAAOA,0BAA0B;IAkF3BC,KAAA;IACAC,MAAA;IACAC,eAAA;IACAC,YAAA;IACAC,aAAA;IACAC,WAAA;IAtFVC,cAAc,GAAc,CAC1B;MAAElB,EAAE,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAgB,CAAE,EAC3C;MAAED,EAAE,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAc,CAAE,EAC7C;MAAED,EAAE,EAAE,cAAc;MAAEC,KAAK,EAAE,SAAS;MAAEkB,QAAQ,EAAE;IAAI,CAAE,CACzD;IACD;IACAC,UAAU,GAAkB,IAAI;IAChCC,YAAY,GAAW,UAAU;IAEjCC,SAAS,GAAGrG,iBAAwC;IAGpDsG,iBAAiB;IAEjB;IACAC,YAAY,GAAkB,EAAE;IAChCC,gBAAgB,GAAW,CAAC;IAC5BtB,gBAAgB;IAChBE,SAAS,GAAY,KAAK;IAC1BC,MAAM,GAAoBpF,eAAe,CAACwG,UAAU;IAEpD;IACAC,YAAY,GAAkB,EAAE;IAChCC,gBAAgB,GAAY,KAAK;IACjCC,SAAS,GAAG,EAAE;IAEd;IACAC,YAAY,GAAiB,EAAE;IACxB7E,YAAY;IACZ8E,QAAQ,GAAY,MAAM;IAEjC;IACAC,kBAAkB,GAAgB,IAAI;IACtCC,kBAAkB,GAAY,KAAK;IACnCC,WAAW;IAEJhC,YAAY,GAAU,EAAE;IAC/BiC,kBAAkB,GAAGnH,WAAW,CAACoH,kBAAkB,IAAI,KAAK;IAErDC,mBAAmB,GAAY,KAAK;IAC3CC,gBAAgB;IAEhB;IACQC,QAAQ,GAAG,IAAInI,OAAO,EAAQ;IACtCoI,WAAW,GAAW,gBAAgB;IACtCC,QAAQ,GAAa,CACnB;MAAEzC,EAAE,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAgB,CAAE,EAC3C;MAAED,EAAE,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAc,CAAE,EACvC;MAAED,EAAE,EAAE,SAAS;MAAEC,KAAK,EAAE,SAAS;MAAEkB,QAAQ,EAAE;IAAI,CAAE,CACpD;IACDuB,QAAQ,GAAG,KAAK;IAChBC,UAAU;IACVjC,WAAW,GAAG,EAAE;IAChBkC,WAAW,GAAG,KAAK;IACnBC,WAAW,GAAa,KAAK;IAC7BC,aAAa,GAAW,EAAE;IAC1BC,cAAc,GAAU,EAAE;IAC1BC,aAAa,GAAU,EAAE;IACzB5C,QAAQ,GAAG,CAAC;IACZ6C,SAAS,GAAG,KAAK;IACjBC,WAAW,GAAW,EAAE;IAExBC,eAAe,GAAa,EAAE;IAC9BC,iBAAiB,GAAW,CAAC;IAC7BC,WAAW,GAAW,UAAU;IAEhC;IACAC,oBAAoB,GAAG,KAAK;IAC5BC,cAAc,GAAc,EAAE;IAC9BC,aAAa,GAA6B,EAAE;IAE5C;IACAC,YAAY,GAAU,EAAE;IACxBC,gBAAgB,GAAG,CAAC;IACpBtE,YAAY,GAAQ,IAAI;IACxBG,sBAAsB,GAAG,KAAK;IAC9B9B,4BAA4B,GAAG,KAAK;IACpCkG,cAAc,GAAiC,EAAE;IAGjDC,YACUhD,KAAqB,EACrBC,MAAc,EACdC,eAAgC,EAChCC,YAAiC,EACjCC,aAA4B,EAC5BC,WAAwB;MALxB,KAAAL,KAAK,GAALA,KAAK;MACL,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,eAAe,GAAfA,eAAe;MACf,KAAAC,YAAY,GAAZA,YAAY;MACZ,KAAAC,aAAa,GAAbA,aAAa;MACb,KAAAC,WAAW,GAAXA,WAAW;IAClB;IAEH4C,QAAQA,CAAA;MACN,IAAI,CAAC7C,aAAa,CAAC8C,aAAa,EAAE;MAClC,IAAI,CAACtB,WAAW,GAAG,gBAAgB;MACnC,IAAI,CAACN,WAAW,GAAG6B,MAAM,CAACC,UAAU,EAAE;MACtC;MACA,IAAI,CAACpD,KAAK,CAACqD,QAAQ,CAACC,IAAI,CAAC7J,SAAS,CAAC,IAAI,CAACkI,QAAQ,CAAC,CAAC,CAAC4B,SAAS,CAAEC,MAAM,IAAI;QACtE,IAAI,CAAChD,UAAU,GAAGgD,MAAM,CAAClH,GAAG,CAAC,IAAI,CAAC;QAClC,IAAI,IAAI,CAACkE,UAAU,EAAE;UACnB,IAAI,CAACiD,YAAY,CAAC,IAAI,CAACjD,UAAU,CAAC;QACpC,CAAC,MAAM;UACL;UACA,IAAI,CAACP,MAAM,CAACyD,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;QAC5C;MACF,CAAC,CAAC;MACF;IACF;IAEAC,WAAWA,CAAA;MACT,IAAI,CAAChC,QAAQ,CAACiC,IAAI,EAAE;MACpB,IAAI,CAACjC,QAAQ,CAACkC,QAAQ,EAAE;MACxB,IAAI,CAACzD,aAAa,CAAC0D,YAAY,EAAE;IACnC;IACA3E,WAAWA,CAAC4E,KAAoC;MAC9C,IAAI,CAACnC,WAAW,GAAGmC,KAAK,CAAC1E,KAAK;MAC9B,IAAI,CAACoD,WAAW,GAAGsB,KAAK,CAAC3E,EAAE;MAC3B4E,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEF,KAAK,CAAC;IACpC;IAEA;IACAN,YAAYA,CAACrE,EAAU;MACrB;MACA4E,OAAO,CAACC,GAAG,CAAC,6BAA6B7E,EAAE,EAAE,CAAC;MAC9C,IAAI,CAAC2B,YAAY,GAAG,CAClB;QACEmD,IAAI,EAAE,IAAI;QACVC,IAAI,EAAE;OACQ,CACjB;MACD,IAAI,CAAC9H,YAAY,GAAG,IAAI,CAACgE,WAAW,CAAC+D,KAAK,CAAC,EAAE,CAAC;MAE9C,IAAI,CAAClE,eAAe,CAACmE,eAAe,CAACjF,EAAE,CAAC,CAACmE,SAAS,CAAC;QAC/CK,IAAI,EAAGU,GAAG,IAAI;UACd,IAAI,CAACnC,cAAc,GAAGmC,GAAG,CAACC,QAAQ,CAACC,cAAc;UACjD,IAAI,CAAC/D,YAAY,GAAG6D,GAAG,CAACC,QAAQ,CAAC7F,IAAI;UACrC,IAAI,CAAC+F,iBAAiB,EAAE;UACxB,IAAI,CAACC,qBAAqB,EAAE;UAC5B,IAAI,CAACC,cAAc,EAAE;UACrB,IAAI,CAACC,qBAAqB,EAAE;UAC5B,IAAI,CAACC,eAAe,CAAC,CAAC,CAAC;UACvB,IAAI,CAACC,oBAAoB,EAAE;QAC7B,CAAC;QACCC,KAAK,EAAGC,GAAG,IAAI;UACb;UACA,IAAI,CAACC,gBAAgB,EAAE;UACvB,IAAI,CAAChD,WAAW,GAAG,IAAI;UACvB,IAAI,CAACtB,iBAAiB,CAACuE,aAAa,CAAC,uFAAuF,CAAC;UAC7HlB,OAAO,CAACC,GAAG,CAACe,GAAG,CAAC;QAClB;OACH,CAAC;IAEJ;IAEOhI,YAAYA,CAACzB,KAAa;MAC/B,MAAM4J,KAAK,GAAG5J,KAAK,CAAC4J,KAAK,CAAC,WAAW,CAAC;MACtC,IAAIA,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,EAAE;QACrB,MAAMC,YAAY,GAAGD,KAAK,CAAC,CAAC,CAAC,CAACE,IAAI,EAAE;QACpC,OAAOD,YAAY,CAACE,UAAU,CAAC,OAAO,CAAC,IAAIF,YAAY,CAACE,UAAU,CAAC,OAAO,CAAC;MAC7E;MACA,OAAO,KAAK;IACd;IAEOX,cAAcA,CAAA;MACnB,IAAI,CAACvC,aAAa,CAACmD,OAAO,CAAElG,KAAU,IAAI;QACxC,IAAI,CAAChD,YAAY,CAACmJ,UAAU,CAACnG,KAAK,CAAC9D,KAAK,EAAE,IAAI,CAAC8E,WAAW,CAACoF,OAAO,CAAC,EAAE,EAAE7L,UAAU,CAAC8L,QAAQ,CAAC,CAAC;MAC9F,CAAC,CAAC;IACJ;IAEAC,iBAAiBA,CAAA;MACf,IAAI,CAACnG,QAAQ,GAAG,CAAC;MACjB,IAAI,CAACkC,gBAAgB,GAAGkE,WAAW,CAAC,MAAK;QACvC,IAAI,IAAI,CAACpG,QAAQ,GAAG,EAAE,EAAE;UACtB,IAAI,CAACA,QAAQ,IAAI,CAAC,CAAC,CAAC;QACtB;MACF,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACX;IAEAqG,gBAAgBA,CAAA;MACdC,aAAa,CAAC,IAAI,CAACpE,gBAAgB,CAAC;MACpC,IAAI,CAAClC,QAAQ,GAAG,GAAG;MAEnBuG,UAAU,CAAC,MAAK;QACd,IAAI,CAAC1D,SAAS,GAAG,KAAK;MACxB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACX;IAEA;IACA2D,iBAAiBA,CAACC,OAAe;MAC/B;MACA,IAAI,CAACjF,gBAAgB,GAAG,IAAI;MAC5B,IAAGiF,OAAO,CAACZ,IAAI,EAAE,KAAK,EAAE,EAAC;QACvB,IAAG,IAAI,CAAC9C,eAAe,CAAClF,MAAM,KAAK,CAAC,EAAC;UACnC,IAAI,CAACsD,iBAAiB,CAACuE,aAAa,CAAC,2BAA2B,CAAC;UACjE,IAAI,CAACrH,eAAe,EAAE;QACxB;QACA;MACF;MAEA,IAAG,IAAI,CAAC4D,mBAAmB,IAAI,IAAI,CAACe,iBAAiB,KAAG,IAAI,CAACD,eAAe,CAAClF,MAAM,EAAC;QAChF,IAAI,CAACsD,iBAAiB,CAACuE,aAAa,CAAC,2BAA2B,CAAC;QACjE,IAAI,CAACrH,eAAe,EAAE;QACxB;MACF;MAEA,MAAMqI,KAAK,GAAG,IAAI,CAAC3D,eAAe,CAAC,IAAI,CAACC,iBAAiB,CAAC;MAC1D,IAAI,IAAI,CAACxF,YAAY,CAACkJ,KAAK,CAAC,EAAE;QAC5B;QACA,IAAI,CAACvF,iBAAiB,CAACuE,aAAa,CAAC,mCAAmCgB,KAAK,EAAE,CAAC;QAChF;MACF;MAEA,IAAI,CAAC7J,YAAY,CAACC,GAAG,CAAC4J,KAAK,CAAC,EAAEC,QAAQ,CAACF,OAAO,CAAC;MAC/C,IAAI,CAACzD,iBAAiB,EAAE;MAExB,IAAI,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAACD,eAAe,CAAClF,MAAM,EAAE;QACxD,IAAI,CAAC+I,qBAAqB,EAAE;MAC9B,CAAC,MAAM;QACL,IAAI,CAACzF,iBAAiB,CAACuE,aAAa,CAAC,oDAAoD,CAAC;QAC1F,IAAI,CAACrH,eAAe,EAAE;MACxB;IACF;IAEA;IACAkB,QAAQA,CAAA;MACNiF,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACvC;IACF;IAEA;IACApE,aAAaA,CAACwG,OAA8B;MAC1CrC,OAAO,CAACC,GAAG,CAAC,aAAaoC,OAAO,UAAU,CAAC;MAE3C,IAAIA,OAAO,KAAK,UAAU,EAAE;QAC1B,MAAMC,IAAI,GAAG,IAAI,CAAC1F,YAAY,CAC3B2F,GAAG,CAAEtC,GAAG,IAAK,IAAIA,GAAG,CAACuC,SAAS,KAAKvC,GAAG,CAACgC,OAAO,EAAE,CAAC,CACjDQ,IAAI,CAAC,IAAI,CAAC;QACb,IAAI,CAACC,cAAc,CAACJ,IAAI,EAAE,4BAA4B,EAAE,YAAY,CAAC;MACvE,CAAC,MAAM;QACL,MAAMA,IAAI,GAAGK,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC1F,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC;QACvD,IAAI,CAACwF,cAAc,CAACJ,IAAI,EAAE,uBAAuB,EAAE,kBAAkB,CAAC;MACxE;IACF;IAEA;IACQI,cAAcA,CAACJ,IAAY,EAAEO,QAAgB,EAAEC,IAAY;MACjE,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACV,IAAI,CAAC,EAAE;QAAEQ;MAAI,CAAE,CAAC;MACvC,MAAMG,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;MACrC,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGN,GAAG;MACfG,IAAI,CAACI,QAAQ,GAAGX,QAAQ;MACxBO,IAAI,CAACK,KAAK,EAAE;MACZP,GAAG,CAACQ,eAAe,CAACT,GAAG,CAAC;IAC1B;IAEA;IACAhI,mBAAmBA,CAAC0I,MAAiC;MACnD3D,OAAO,CAACC,GAAG,CAAC,mBAAmB0D,MAAM,EAAE,CAAC;MACxC;MAEA,IAAIA,MAAM,KAAK,MAAM,EAAE;QACrB,IAAI,CAAClI,SAAS,GAAG,IAAI;MACvB,CAAC,MAAM,IAAIkI,MAAM,KAAK,OAAO,IAAIA,MAAM,KAAK,MAAM,EAAE;QAClD,IAAI,CAAClI,SAAS,GAAG,KAAK;MACxB;IACF;IAEA;IACAmI,YAAYA,CAAA;MACV,IAAI,CAAC3H,MAAM,CAACyD,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;IAC5C;IAEA;IACAmE,YAAYA,CAAA;MACV,IAAI,IAAI,CAACrH,UAAU,EAAE;QACnB,IAAI,CAACP,MAAM,CAACyD,QAAQ,CAAC,CAAC,uBAAuB,EAAE,IAAI,CAAClD,UAAU,CAAC,CAAC;MAClE;IACF;IAEOsH,kBAAkBA,CAACC,KAAA,GAAgB,IAAI;MAC5ChC,UAAU,CAAC,MAAK;QACd,IAAI,CAAC,IAAI,CAACtE,mBAAmB,EAAE;UAC7BuC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACvD,SAAS,CAAC;UAC3BsD,OAAO,CAACC,GAAG,CAAC,IAAI,CAACvD,SAAS,CAAC,QAAQ,CAAC,CAACsH,sBAAsB,CAAC;UAC5D,IAAI,CAAC1I,YAAY,CAAC2I,IAAI,CAAC;YACrBC,OAAO,EAAE,IAAI,CAACxH,SAAS,CAAC,QAAQ,CAAC,CAACsH,sBAAsB;YACxDG,KAAK,EAAE;WACR,CAAC;QACJ;MACF,CAAC,EAAEJ,KAAK,CAAC;IACX;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEOK,eAAeA,CAAC9G,WAAmB;MACxC,IAAI,CAACpB,eAAe,CACjBmI,kBAAkB,CAAC/G,WAAW,CAAC,CAC/BgC,IAAI,CAAC7J,SAAS,CAAC,IAAI,CAACkI,QAAQ,CAAC,CAAC,CAC9B4B,SAAS,CAAC;QACTK,IAAI,EAAGqC,OAAO,IAAI;UAChBjC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEgC,OAAO,CAAC;UACjC,MAAM;YAAEiC,OAAO;YAAEC;UAAK,CAAE,GAAGlC,OAAO;UAClC,IAAIkC,KAAK,EAAE;YACT,IAAI,CAAC7I,YAAY,CAAC2I,IAAI,CAAC;cAAEC,OAAO;cAAEC;YAAK,CAAE,CAAC;UAC5C,CAAC,MAAM,IAAI,IAAI,CAAC5G,kBAAkB,KAAK,KAAK,EAAE;YAC5C;UAAA;QAEJ,CAAC;QACDwD,KAAK,EAAGC,GAAG,IAAI;UACb,IAAI,CAAC1F,YAAY,CAAC2I,IAAI,CAAC;YACrBC,OAAO,EAAE,IAAI,CAACxH,SAAS,CAAC,aAAa,CAAC;YACtCyH,KAAK,EAAE;WACR,CAAC;UACFnE,OAAO,CAACe,KAAK,CAAC,kBAAkB,EAAEC,GAAG,CAAC;QACxC,CAAC;QACDnB,QAAQ,EAAEA,CAAA,KAAK;UACb,IAAI,CAACiE,kBAAkB,EAAE;UACzB9D,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC5C;OACD,CAAC;IACN;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEOqE,YAAYA,CAACC,MAAc;MAChC,IAAI,CAACvG,WAAW,GAAG,KAAK;MACxB,IAAI;QACF,MAAMwG,YAAY,GAAG7B,IAAI,CAAC8B,KAAK,CAACF,MAAM,CAAC;QACvC,IAAI,CAACvG,WAAW,GAAG,IAAI;QACvB,OAAOwG,YAAY;MACrB,CAAC,CAAC,OAAOE,CAAC,EAAE;QACV,OAAO,IAAI;MACb;IACF;IAEO7K,eAAeA,CAAA;MACpB,IAAI8K,OAAO,GAAmC,IAAIC,QAAQ,EAAE;MAC5D,IAAIC,WAAW,GAAG,EAAE;MAGpB,IAAI,CAACnJ,MAAM,GAAGpF,eAAe,CAACwO,OAAO;MACrC,IAAI,IAAI,CAAC5G,aAAa,CAAC7E,MAAM,EAAE;QAC7B,IAAI,CAAC6E,aAAa,CAACqD,OAAO,CAAEwD,IAAI,IAAI;UAClCJ,OAAO,CAACK,MAAM,CAAC,OAAO,EAAED,IAAI,CAAC;QAC/B,CAAC,CAAC;QACFJ,OAAO,CAACK,MAAM,CAAC,YAAY,EAAE,IAAI,CAACxI,UAAU,CAAC;QAC7CmI,OAAO,CAACK,MAAM,CAAC,YAAY,EAAErC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACvK,YAAY,CAACE,KAAK,CAAC,CAAC;QACrEoM,OAAO,CAACK,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC7I,YAAY,CAAC8I,aAAa,EAAE,CAAC;QACzDN,OAAO,CAACK,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC1H,WAAW,CAAC;QAC/CuH,WAAW,GAAG,QAAQ;MACxB,CAAC,MAAM;QACLF,OAAO,GAAG;UACRO,UAAU,EAAE,IAAI,CAAC1I,UAAU;UAC3B2I,UAAU,EAAE,IAAI,CAAC9M,YAAY,CAACE,KAAK;UACnC+E,WAAW,EAAE,IAAI,CAACA,WAAW;UAC7B8H,IAAI,EAAE,IAAI,CAACjJ,YAAY,CAAC8I,aAAa;SACtC;MACH;MAEA,IAAI,CAACb,eAAe,CAAC,IAAI,CAAC9G,WAAW,CAAC;MACtC,IAAI,CAACqE,iBAAiB,EAAE;MAExB,IAAI,CAACzF,eAAe,CACjBrC,eAAe,CAAC8K,OAAO,EAAEE,WAAW,CAAC,CACrCvF,IAAI,CAAC7J,SAAS,CAAC,IAAI,CAACkI,QAAQ,CAAC,CAAC,CAC9B4B,SAAS,CAAC;QACTK,IAAI,EAAGU,GAAG,IAAI;UACZ,IAAI,CAACtD,gBAAgB,GAAG,KAAK;UAC7B,IAAI,CAACvB,SAAS,GAAG,KAAK;UACtB,IAAI,CAACkB,iBAAiB,CAACuE,aAAa,CAACZ,GAAG,EAAE2B,OAAO,IAAI,4CAA4C,CAAC;UAElG,IAAI3B,GAAG,EAAE+E,gBAAgB,EAAE9E,QAAQ,EAAEgE,MAAM,EAAE;YAC3C,IAAI,CAAC9G,mBAAmB,GAAG,IAAI;YAC/B;YACA,IAAI,CAACnC,YAAY,CAAC2I,IAAI,CAAC;cACrBC,OAAO,EAAE,IAAI,CAACxH,SAAS,CAAC,QAAQ,CAAC,CAAC4I,oBAAoB;cACtDnB,KAAK,EAAE;aACR,CAAC;YACF,IAAI,CAACrG,QAAQ,GAAG,KAAK;YACrB,IAAI,CAACC,UAAU,GAAGuC,GAAG,EAAE+E,gBAAgB,EAAE9E,QAAQ,EAAEgE,MAAM;YACzD,IAAI,CAACrH,YAAY,GAAGoD,GAAG,EAAE+E,gBAAgB,EAAE9E,QAAQ,EAAEgF,YAAY,CAAChD,GAAG,CAAEiD,IAAS,IAAI;cAClF,OAAO;gBACLpK,EAAE,EAAEoK,IAAI,EAAEpK,EAAE,IAAI,EAAE;gBAClBqK,KAAK,EAAED,IAAI,EAAEC,KAAK,IAAI,EAAE;gBACxBvB,OAAO,EAAEsB,IAAI,EAAEtB,OAAO,IAAI,EAAE;gBAC5BwB,SAAS,EAAEF,IAAI,EAAEE,SAAS,IAAI,EAAE;gBAChClD,SAAS,EAAEgD,IAAI,EAAEhD,SAAS,IAAI,EAAE;gBAChCM,IAAI,EAAE0C,IAAI,EAAE1C,IAAI,IAAI,EAAE;gBACtB6C,WAAW,EAAEH,IAAI,EAAEG,WAAW,IAAI,EAAE;gBACpCC,eAAe,EAAEJ,IAAI,EAAEI,eAAe,IAAI,EAAE;gBAC5CC,OAAO,EAAEL,IAAI,EAAEK,OAAO,IAAI,EAAE;gBAC5BC,GAAG,EAAEN,IAAI,EAAEM,GAAG,IAAI;eACnB;YACH,CAAC,CAAC;YAEF,IAAI,CAAChK,WAAW,GAAGwE,GAAG,EAAE+E,gBAAgB,EAAE9E,QAAQ,EAAEgF,YAAY,CAAChD,GAAG,CACjEiD,IAKA,IAAI;cACH,OAAO;gBACLG,WAAW,EAAEH,IAAI,CAACG,WAAW;gBAC7BE,OAAO,EAAEL,IAAI,CAACK,OAAO;gBACrBC,GAAG,EAAEN,IAAI,CAACM,GAAG;gBACbF,eAAe,EAAEJ,IAAI,CAACI;eACvB;YACH,CAAC,CACF;YAED;YACA;YACA;YAEA;YACA;YACA;YACA;YACA;YACA;UACF;UACA,IAAI,CAACtB,YAAY,CAAC,IAAI,CAACvG,UAAU,CAAC;UAClC,IAAI,CAACrC,MAAM,GAAGpF,eAAe,CAACyP,SAAS;UACvC,IAAI,CAAClE,gBAAgB,EAAE;UACvB,IAAI,CAAC3D,aAAa,GAAG,EAAE;QACzB,CAAC;QACD6C,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACtD,mBAAmB,GAAG,IAAI;UAC/B,IAAI,CAACT,gBAAgB,GAAG,KAAK;UAC7B,IAAI,CAACc,QAAQ,GAAG,IAAI;UACpB,IAAI,CAACC,UAAU,GAAGgD,KAAK,EAAEA,KAAK,EAAEiF,MAAM;UACtC,IAAI,CAAC9J,eAAe,CAAC+J,qBAAqB,EAAE;UAC5C,IAAI,CAAC3K,YAAY,CAAC2I,IAAI,CAAC;YACrBC,OAAO,EAAE,IAAI,CAACxH,SAAS,CAAC,QAAQ,CAAC,CAACwJ,iBAAiB;YACnD/B,KAAK,EAAE;WACR,CAAC;UACF,IAAI,CAACxH,iBAAiB,CAACuE,aAAa,CAClC,sDAAsD,CACvD;UACD,IAAI,CAAChD,aAAa,GAAG,EAAE;UACvB,IAAI,CAAC2D,gBAAgB,EAAE;UACvB7B,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEc,KAAK,CAACkB,OAAO,CAAC;QACxC;OACD,CAAC;IACN;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEAkE,gBAAgBA,CAAA;MACdnG,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;IACjC;IAEAmG,qBAAqBA,CAACC,KAAa;MACjC,IAAG,IAAI,CAAC7H,iBAAiB,KAAG,IAAI,CAACD,eAAe,CAAClF,MAAM,IAAI,IAAI,CAACkF,eAAe,CAAClF,MAAM,KAAG,CAAC,EAAC;QACzF,IAAI,CAAC6E,aAAa,GAAGmI,KAAK;QAC1B;MACF;MAEA,MAAMnE,KAAK,GAAG,IAAI,CAAC3D,eAAe,CAAC,IAAI,CAACC,iBAAiB,CAAC;MAC1D,IAAG,IAAI,CAACxF,YAAY,CAACkJ,KAAK,CAAC,EAAC;QAC1B,IAAImE,KAAK,IAAIA,KAAK,CAAChN,MAAM,GAAG,CAAC,EAAE;UAC7B,IAAI,CAACiN,eAAe,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC;QAChC;MACF,CAAC,MAAM;QACL,IAAI,CAACnI,aAAa,GAAGmI,KAAK;MAC5B;IACF;IAEAvF,oBAAoBA,CAAA;MAClB,IAAI,CAACvC,eAAe,GAAGgI,MAAM,CAACC,IAAI,CAAC,IAAI,CAACnO,YAAY,CAACoO,QAAQ,CAAC;MAC9D,IAAI,CAACjI,iBAAiB,GAAG,CAAC;MAC1B,IAAI,IAAI,CAACD,eAAe,CAAClF,MAAM,GAAG,CAAC,EAAE;QACnC,IAAI,CAAC+I,qBAAqB,EAAE;MAC9B,CAAC,MACG;QACF,IAAI,CAACnE,WAAW,GAAG,IAAI;QACvB,IAAI,CAACtB,iBAAiB,CAACuE,aAAa,CAAC,uFAAuF,CAAC;MAC/H;IACF;IAEAkB,qBAAqBA,CAAA;MACnB,MAAMF,KAAK,GAAG,IAAI,CAAC3D,eAAe,CAAC,IAAI,CAACC,iBAAiB,CAAC;MAC1D,IAAI,IAAI,CAACxF,YAAY,CAACkJ,KAAK,CAAC,EAAE;QAC5B,IAAI,CAAC/E,QAAQ,GAAG,sBAAsB;QACtC,IAAI,CAACR,iBAAiB,CAACuE,aAAa,CAAC,8BAA8BgB,KAAK,EAAE,CAAC;QAC3E;MACF,CAAC,MAAM;QACL,IAAI,CAAC/E,QAAQ,GAAG,MAAM,CAAC,CAAC;QACxB,IAAI,CAACR,iBAAiB,CAACuE,aAAa,CAAC,6BAA6BgB,KAAK,EAAE,CAAC;MAC5E;IACF;IAEAoE,eAAeA,CAACvB,IAAU;MACxB,MAAM7C,KAAK,GAAG,IAAI,CAAC3D,eAAe,CAAC,IAAI,CAACC,iBAAiB,CAAC;MAC1D,IAAI,CAAC,IAAI,CAACxF,YAAY,CAACkJ,KAAK,CAAC,EAAE;MAE/B,MAAMwE,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,MAAM,GAAG,MAAK;QACnB,MAAMC,YAAY,GAAIH,MAAM,CAACI,MAAiB;QAC9C,IAAI,CAACzO,YAAY,CAACC,GAAG,CAAC4J,KAAK,CAAC,EAAEC,QAAQ,CAAC0E,YAAY,CAAC;QACpD,IAAI,CAACrI,iBAAiB,EAAE;QACxB,IAAI,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAACD,eAAe,CAAClF,MAAM,EAAE;UACxD,IAAI,CAAC+I,qBAAqB,EAAE;QAC9B,CAAC,MAAM;UACL,IAAI,CAACzF,iBAAiB,CAACuE,aAAa,CAAC,oCAAoC,CAAC;UAC1E,IAAI,CAACrH,eAAe,EAAE;QACxB;MACF,CAAC;MACD6M,MAAM,CAACK,aAAa,CAAChC,IAAI,CAAC;IAC5B;IAEA;IACAiC,eAAeA,CAAA;MACb,IAAI,CAACtI,oBAAoB,GAAG,CAAC,IAAI,CAACA,oBAAoB;IACxD;IAEAuI,oBAAoBA,CAACC,KAAa;MAChC,IAAI,CAACvI,cAAc,CAACuI,KAAK,CAAC,GAAG,CAAC,IAAI,CAACvI,cAAc,CAACuI,KAAK,CAAC;IAC1D;IAEA/M,2BAA2BA,CAAA;MACzB,IAAI,CAACQ,sBAAsB,GAAG,CAAC,IAAI,CAACA,sBAAsB;IAC5D;IAEAxB,mBAAmBA,CAAA;MACjB,IAAI,CAACN,4BAA4B,GAAG,CAAC,IAAI,CAACA,4BAA4B;IACxE;IAEA+H,qBAAqBA,CAAA;MACnB,IAAI,CAACjC,cAAc,GAAG,IAAIwI,KAAK,CAAC,IAAI,CAAChJ,cAAc,CAAC9E,MAAM,CAAC,CAAC+N,IAAI,CAAC,KAAK,CAAC;MACvE,IAAI,CAACxI,aAAa,GAAG,IAAI,CAACT,cAAc,CAACoE,GAAG,CAAC,OAAO;QAAEwD,SAAS,EAAE;MAAK,CAAE,CAAC,CAAC;IAC5E;IAEA;IACAtF,iBAAiBA,CAAA;MACf,IAAI,CAAC5B,YAAY,GAAG,IAAI,CAACV,cAAc,CAACoE,GAAG,CAAC,CAAC9H,KAAU,EAAEyM,KAAa,MAAM;QAC1E9L,EAAE,EAAE8L,KAAK;QACT7L,KAAK,EAAEZ,KAAK,CAACA,KAAK,CAACC,IAAI;QACvBqL,SAAS,EAAE,KAAK;QAChBsB,MAAM,EAAEH,KAAK,KAAK;OACnB,CAAC,CAAC;IACL;IAEAI,YAAYA,CAACC,SAAiB;MAC5B,IAAI,CAAC1G,eAAe,CAAC0G,SAAS,CAAC;IACjC;IAEA1G,eAAeA,CAACqG,KAAa;MAC3B,IAAI,CAACpI,gBAAgB,GAAGoI,KAAK;MAC7B,IAAI,CAAC1M,YAAY,GAAG,IAAI,CAAC2D,cAAc,CAAC+I,KAAK,CAAC;MAC9C,IAAI,CAACvM,sBAAsB,GAAG,KAAK;MACnC,IAAI,CAAC9B,4BAA4B,GAAG,KAAK;MAEzC;MACA,IAAI,CAACgG,YAAY,CAAC0C,OAAO,CAAC,CAACiG,IAAI,EAAEC,CAAC,KAAI;QACpCD,IAAI,CAACH,MAAM,GAAGI,CAAC,KAAKP,KAAK;MAC3B,CAAC,CAAC;IACJ;IAEAxN,eAAeA,CAAA;MACb,IAAI,IAAI,CAACoF,gBAAgB,GAAG,IAAI,CAACX,cAAc,CAAC9E,MAAM,GAAG,CAAC,EAAE;QAC1D;QACA,IAAI,CAACwF,YAAY,CAAC,IAAI,CAACC,gBAAgB,CAAC,CAACiH,SAAS,GAAG,IAAI;QACzD,IAAI,CAACnH,aAAa,CAAC,IAAI,CAACE,gBAAgB,CAAC,CAACiH,SAAS,GAAG,IAAI;QAE1D;QACA,IAAI,CAAClF,eAAe,CAAC,IAAI,CAAC/B,gBAAgB,GAAG,CAAC,CAAC;MACjD;IACF;IAEAvE,uBAAuBA,CAAA;MACrB,OAAO,IAAI,CAACqE,aAAa,CAAC,IAAI,CAACE,gBAAgB,CAAC,EAAEiH,SAAS,IAAI,KAAK;IACtE;IAEA;IACArF,qBAAqBA,CAAA;MACnB,IAAI,CAACvC,cAAc,CAACoD,OAAO,CAAC,CAAC9G,KAAU,EAAEyM,KAAa,KAAI;QACxD,MAAMQ,WAAW,GAAG,IAAI,CAACC,yBAAyB,CAAClN,KAAK,CAAC;QACzD,IAAI,CAACsE,cAAc,CAACtE,KAAK,CAACA,KAAK,CAACW,EAAE,CAAC,GAAGsM,WAAW;MACnD,CAAC,CAAC;MAEF;MACA,IAAI,CAACtJ,aAAa,GAAG,EAAE;MACvBmI,MAAM,CAACqB,MAAM,CAAC,IAAI,CAAC7I,cAAc,CAAC,CAACwC,OAAO,CAACsG,MAAM,IAAG;QAClD,IAAI,CAACzJ,aAAa,CAAC6F,IAAI,CAAC,GAAG4D,MAAM,CAAC;MACpC,CAAC,CAAC;IACJ;IAEAF,yBAAyBA,CAAClN,KAAU;MAClC,MAAMqN,oBAAoB,GAAG,qCAAqC;MAClE,MAAMC,cAAc,GAA+C,EAAE;MAErE,MAAMC,gBAAgB,GAAGvN,KAAK,EAAEA,KAAK,EAAE+K,IAAI,EAAEG,WAAW,IAAI,EAAE;MAC9D,MAAMsC,OAAO,GAAGD,gBAAgB,CAACE,QAAQ,CAACJ,oBAAoB,CAAC,IAAI,EAAE;MAErE,KAAK,MAAM3G,KAAK,IAAI8G,OAAO,EAAE;QAC3B,MAAMlP,WAAW,GAAGoI,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC;QACxC,MAAMgH,gBAAgB,GAAGhH,KAAK,CAAC,CAAC,CAAC;QACjC,IAAI,CAAC4G,cAAc,CAAChP,WAAW,CAAC,EAAE;UAChCgP,cAAc,CAAChP,WAAW,CAAC,GAAG;YAAE8O,MAAM,EAAE,IAAIO,GAAG;UAAE,CAAE;QACrD;QACAL,cAAc,CAAChP,WAAW,CAAC,CAAC8O,MAAM,CAACQ,GAAG,CAACF,gBAAgB,CAAC;MAC1D;MAEA,OAAO5B,MAAM,CAAC+B,OAAO,CAACP,cAAc,CAAC,CAACxF,GAAG,CAAC,CAAC,CAACxJ,WAAW,EAAE;QAAE8O;MAAM,CAAE,CAAC,MAAM;QACxE9O,WAAW;QACXxB,KAAK,EAAE,CAAC,GAAGsQ,MAAM,CAAC,CAAC,CAAC,CAAC;QACrBU,OAAO,EAAE9N,KAAK,CAACA,KAAK,CAACW,EAAE;QACvBsK,SAAS,EAAEjL,KAAK,CAACA,KAAK,CAACC;OACxB,CAAC,CAAC;IACL;IAEAtB,qBAAqBA,CAAA;MACnB,IAAI,CAAC,IAAI,CAACoB,YAAY,EAAE,OAAO,EAAE;MACjC,OAAO,IAAI,CAACuE,cAAc,CAAC,IAAI,CAACvE,YAAY,CAACC,KAAK,CAACW,EAAE,CAAC,IAAI,EAAE;IAC9D;IAEAoN,gBAAgBA,CAAC/N,KAAU;MACzB,MAAMgO,KAAK,GAAG,CAAC,IAAIhO,KAAK,CAACgO,KAAK,IAAI,EAAE,CAAC,EAAE,IAAIhO,KAAK,CAACiO,SAAS,IAAI,EAAE,CAAC,CAAC;MAClE,OAAOD,KAAK,CAAClG,GAAG,CAACoG,IAAI,IAAIA,IAAI,CAACC,QAAQ,IAAID,IAAI,CAAC,CAAClG,IAAI,CAAC,IAAI,CAAC,IAAI,MAAM;IACtE;IAEAoG,iBAAiBA,CAAC9I,KAAoB,EAAExI,KAAU;MAChD,IAAIwI,KAAK,CAAC+I,GAAG,KAAK,OAAO,IAAI,CAAC/I,KAAK,CAACgJ,QAAQ,EAAE;QAC5ChJ,KAAK,CAACiJ,cAAc,EAAE;QACtB,IAAI,CAAC9R,kBAAkB,CAACK,KAAK,CAAC;MAChC;IACF;IAEAL,kBAAkBA,CAACK,KAAU;MAC3B,MAAM0R,WAAW,GAAG,IAAI,CAAC5Q,YAAY,CAACC,GAAG,CAACf,KAAK,CAACA,KAAK,CAAC;MACtD,IAAI0R,WAAW,IAAIA,WAAW,CAAC1Q,KAAK,IAAI0Q,WAAW,CAAC1Q,KAAK,CAAC8I,IAAI,EAAE,EAAE;QAChE;QACA,IAAI,CAACtE,YAAY,CAACkH,IAAI,CAAC;UACrB/D,IAAI,EAAE,MAAM;UACZC,IAAI,EAAE8I,WAAW,CAAC1Q;SACnB,CAAC;QAEF;QACA,IAAI,IAAI,CAAC2Q,6BAA6B,EAAE,EAAE;UACxC,IAAI,CAACxP,eAAe,EAAE;QACxB;MACF;IACF;IAEAzB,gBAAgBA,CAAC8H,KAAU,EAAEoJ,SAAiB;MAC5C,MAAMpE,IAAI,GAAGhF,KAAK,CAACqJ,MAAM,CAAC/C,KAAK,CAAC,CAAC,CAAC;MAClC,IAAItB,IAAI,EAAE;QACR,MAAM2B,MAAM,GAAG,IAAIC,UAAU,EAAE;QAC/BD,MAAM,CAACE,MAAM,GAAG,MAAK;UACnB,MAAMC,YAAY,GAAGH,MAAM,CAACI,MAAgB;UAC5C,IAAI,CAACzO,YAAY,CAACC,GAAG,CAAC6Q,SAAS,CAAC,EAAEhH,QAAQ,CAAC0E,YAAY,CAAC;UAExD;UACA,IAAI,CAAC9J,YAAY,CAACkH,IAAI,CAAC;YACrB/D,IAAI,EAAE,MAAM;YACZC,IAAI,EAAE,mBAAmB4E,IAAI,CAACrK,IAAI;WACnC,CAAC;UAEF;UACA,IAAI,IAAI,CAACwO,6BAA6B,EAAE,EAAE;YACxC,IAAI,CAACxP,eAAe,EAAE;UACxB;QACF,CAAC;QACDgN,MAAM,CAACK,aAAa,CAAChC,IAAI,CAAC;MAC5B;IACF;IAEAnN,gBAAgBA,CAACuR,SAAiB;MAChC,MAAME,SAAS,GAAGhG,QAAQ,CAACiG,cAAc,CAAC,OAAO,GAAGH,SAAS,CAAqB;MAClF,IAAIE,SAAS,EAAE;QACbA,SAAS,CAAC5F,KAAK,EAAE;MACnB;IACF;IAEAyF,6BAA6BA,CAAA;MAC3B,MAAMK,aAAa,GAAG,IAAI,CAACnQ,qBAAqB,EAAE;MAClD,OAAOmQ,aAAa,CAACC,KAAK,CAACjS,KAAK,IAAG;QACjC,MAAM0R,WAAW,GAAG,IAAI,CAAC5Q,YAAY,CAACC,GAAG,CAACf,KAAK,CAACA,KAAK,CAAC;QACtD,OAAO0R,WAAW,IAAIA,WAAW,CAAC1Q,KAAK,IAAI0Q,WAAW,CAAC1Q,KAAK,CAAC8I,IAAI,EAAE;MACrE,CAAC,CAAC;IACJ;IAEAzG,oBAAoBA,CAAA;MAClB,OAAO,IAAI,CAACkE,gBAAgB,KAAK,IAAI,CAACX,cAAc,CAAC9E,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC6P,6BAA6B,EAAE;IACzG;IAEA;IACApP,YAAYA,CAAA;MACV,OAAO,IAAI,CAACzB,YAAY,CAACoR,KAAK,IAAI,CAAC,CAAC,IAAI,CAACjN,UAAU;IACrD;IAEA;IACAyE,gBAAgBA,CAAA;MACd,IAAI,CAAC9C,cAAc,GAAG,CACpB;QACEuL,MAAM,EAAE,CAAC;QACTjP,KAAK,EAAE;UACLW,EAAE,EAAE,CAAC;UACLV,IAAI,EAAE,aAAa;UACnBiP,IAAI,EAAE,gBAAgB;UACtBnE,IAAI,EAAE;YACJG,WAAW,EAAE;WACd;UACDiE,GAAG,EAAE;YACHC,mBAAmB,EAAE;WACtB;UACDpB,KAAK,EAAE,CAAC;YAAEG,QAAQ,EAAE;UAAe,CAAE,EAAE;YAAEA,QAAQ,EAAE;UAAgB,CAAE,CAAC;UACtEF,SAAS,EAAE;;OAEd,EACD;QACEgB,MAAM,EAAE,CAAC;QACTjP,KAAK,EAAE;UACLW,EAAE,EAAE,CAAC;UACLV,IAAI,EAAE,YAAY;UAClBiP,IAAI,EAAE,kBAAkB;UACxBnE,IAAI,EAAE;YACJG,WAAW,EAAE;WACd;UACDiE,GAAG,EAAE;YACHC,mBAAmB,EAAE;WACtB;UACDpB,KAAK,EAAE,CAAC;YAAEG,QAAQ,EAAE;UAAkB,CAAE,CAAC;UACzCF,SAAS,EAAE;;OAEd,EACD;QACEgB,MAAM,EAAE,CAAC;QACTjP,KAAK,EAAE;UACLW,EAAE,EAAE,CAAC;UACLV,IAAI,EAAE,aAAa;UACnBiP,IAAI,EAAE,gBAAgB;UACtBnE,IAAI,EAAE;YACJG,WAAW,EAAE;WACd;UACDiE,GAAG,EAAE;YACHC,mBAAmB,EAAE;WACtB;UACDpB,KAAK,EAAE,CAAC;YAAEG,QAAQ,EAAE;UAAc,CAAE,EAAE;YAAEA,QAAQ,EAAE;UAAe,CAAE,CAAC;UACpEF,SAAS,EAAE;;OAEd,CACF;MAED,IAAI,CAACjM,YAAY,GAAG,eAAe;MACnC,IAAI,CAACgE,iBAAiB,EAAE;MACxB,IAAI,CAACC,qBAAqB,EAAE;MAC5B,IAAI,CAACC,cAAc,EAAE;MACrB,IAAI,CAACC,qBAAqB,EAAE;MAC5B,IAAI,CAACC,eAAe,CAAC,CAAC,CAAC;MAEvB,IAAI,IAAI,CAACzC,aAAa,CAAC/E,MAAM,KAAK,CAAC,EAAE;QACnC,IAAI,CAAC4E,WAAW,GAAG,IAAI;MACzB;IACF;;uCA1yBWlC,0BAA0B,EAAAxF,EAAA,CAAAuT,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAzT,EAAA,CAAAuT,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA1T,EAAA,CAAAuT,iBAAA,CAAAI,EAAA,CAAAC,eAAA,GAAA5T,EAAA,CAAAuT,iBAAA,CAAAM,EAAA,CAAAC,mBAAA,GAAA9T,EAAA,CAAAuT,iBAAA,CAAAM,EAAA,CAAAE,aAAA,GAAA/T,EAAA,CAAAuT,iBAAA,CAAAS,EAAA,CAAAC,WAAA;IAAA;;YAA1BzO,0BAA0B;MAAA0O,SAAA;MAAAC,SAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBAY1B/U,sBAAsB;;;;;;;;;;;;UC9DnCU,EAAA,CAAAC,cAAA,aAA0C;;UAIpCD,EAFJ,CAAAC,cAAA,aAAqD,WAC7C,wBAC4D;UAE9DD,EADA,CAAAsB,SAAA,cAAyC,cACE;UAGjDtB,EAFI,CAAAY,YAAA,EAAiB,EACZ,EACH;;UAYIZ,EAXV,CAAAC,cAAA,aAA2C,aAOxC,aACqD,aACvB,cACA,oBAMtB;UADCD,EAAA,CAAAE,UAAA,mBAAAqU,+DAAA;YAAA,OAASD,GAAA,CAAAjH,YAAA,EAAc;UAAA,EAAC;UACzBrN,EAAA,CAAAY,YAAA,EAAW;UACZZ,EAAA,CAAAC,cAAA,oBAKC;UADCD,EAAA,CAAAE,UAAA,mBAAAsU,+DAAA;YAAA,OAASF,GAAA,CAAA7D,eAAA,EAAiB;UAAA,EAAC;UAGjCzQ,EAFK,CAAAY,YAAA,EAAW,EACR,EACF;UAKFZ,EAHJ,CAAAC,cAAA,eAAiE,eAElC,uBASW;UAApCD,EAAA,CAAAE,UAAA,wBAAAuU,uEAAAhT,MAAA;YAAA,OAAc6S,GAAA,CAAAvD,YAAA,CAAAtP,MAAA,CAAoB;UAAA,EAAC;UAEvCzB,EADE,CAAAY,YAAA,EAAc,EACV;UAGNZ,EAAA,CAAA2B,UAAA,KAAA+S,0CAAA,mBAAwD;UAwI9D1U,EADE,CAAAY,YAAA,EAAM,EACF;UAOEZ,EAJR,CAAAC,cAAA,eAA0E,eAC5C,eACK,eACV,oBAShB;UAFCD,EAAA,CAAAE,UAAA,uBAAAyU,mEAAAlT,MAAA;YAAA,OAAa6S,GAAA,CAAA1P,WAAA,CAAAnD,MAAA,CAAmB;UAAA,EAAC;UAGrCzB,EADG,CAAAY,YAAA,EAAW,EACR;UACNZ,EAAA,CAAAC,cAAA,eAAwC;UACtCD,EAAA,CAAAsB,SAAA,sBAYa;UASjBtB,EADE,CAAAY,YAAA,EAAM,EACF;UAeNZ,EAbA,CAAA2B,UAAA,KAAAiT,0CAAA,kBAAmE,KAAAC,0CAAA,kBAaF;UASzE7U,EAHM,CAAAY,YAAA,EAAM,EACF,EACF,EACF,EA3PoC;;;UAcpCZ,EAAA,CAAAc,SAAA,GAAwC;UAAxCd,EAAA,CAAAoC,WAAA,cAAAkS,GAAA,CAAAnM,oBAAA,CAAwC;UAsBXnI,EAAA,CAAAc,SAAA,GAAqC;UAArCd,EAAA,CAAAoC,WAAA,WAAAkS,GAAA,CAAAnM,oBAAA,CAAqC;UAI1DnI,EAAA,CAAAc,SAAA,GAAsB;UAMtBd,EANA,CAAAe,UAAA,UAAAuT,GAAA,CAAAhM,YAAA,CAAsB,gBAAAgM,GAAA,CAAA/L,gBAAA,CACU,sBACX,kBACJ,yBAGO;UAMQvI,EAAA,CAAAc,SAAA,EAAkB;UAAlBd,EAAA,CAAAe,UAAA,SAAAuT,GAAA,CAAArQ,YAAA,CAAkB;UAgJlDjE,EAAA,CAAAc,SAAA,GAAuB;UAIvBd,EAJA,CAAAe,UAAA,SAAAuT,GAAA,CAAAvO,cAAA,CAAuB,gBAAAuO,GAAA,CAAApM,WAAA,CACI,4BAGA;UAS3BlI,EAAA,CAAAc,SAAA,GAME;UANFd,EAAA,CAAAe,UAAA,iBAAAf,EAAA,CAAAwD,eAAA,KAAAsR,GAAA,EAME;UAcF9U,EAAA,CAAAc,SAAA,EAAsC;UAAtCd,EAAA,CAAAe,UAAA,SAAAuT,GAAA,CAAAjN,WAAA,sBAAsC;UAatCrH,EAAA,CAAAc,SAAA,EAAoC;UAApCd,EAAA,CAAAe,UAAA,SAAAuT,GAAA,CAAAjN,WAAA,oBAAoC;;;qBD9M9CrI,YAAY,EAAA+V,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZ9V,WAAW,EAAA6U,EAAA,CAAAkB,oBAAA,EAAAlB,EAAA,CAAAmB,eAAA,EAAAnB,EAAA,CAAAoB,oBAAA,EACXhW,mBAAmB,EAAA4U,EAAA,CAAAqB,kBAAA,EAAArB,EAAA,CAAAsB,eAAA,EAEnB/V,sBAAsB,EACtBC,oBAAoB,EACpBG,aAAa,EACbF,eAAe,EACfC,aAAa,EACbE,mBAAmB;MAAA2V,MAAA;IAAA;;SAKV/P,0BAA0B;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}