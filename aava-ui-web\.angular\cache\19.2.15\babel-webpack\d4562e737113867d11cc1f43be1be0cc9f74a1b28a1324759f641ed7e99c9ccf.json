{"ast": null, "code": "import { MarketplaceAuthGuard } from './shared/guards/marketplace-auth.guard';\nimport { LoginComponent } from '@shared/auth/components/login/login.component';\nimport { CallbackComponent } from '@shared/auth/components/callback/callback.component';\nimport { LaunchpadHomeComponent } from './pages/launchpad-home/launchpad-home.component';\nimport { AgentsFilterComponent } from './pages/agents-filter/agents-filter.component';\nimport { MyAgentHomeComponent } from './shared/components/my-agent-home/my-agent-home.component';\nimport { MarketplaceComponent } from './pages/marketplace/marketplace.component';\nimport { RootRedirectComponent } from './shared/components/root-redirect/root-redirect.component';\nimport { AgentsComponent } from './pages/agents/agents.component';\nimport { AnalyticsComponent } from './shared/components/analytics/analytics.component';\nimport { DocumentsComponent } from './shared/components/documents/documents.component';\nexport const routes = [{\n  path: 'marketplace',\n  component: MarketplaceComponent\n}, {\n  path: 'login',\n  component: LoginComponent\n}, {\n  path: 'callback',\n  component: CallbackComponent\n}, {\n  path: 'dashboard',\n  canActivate: [MarketplaceAuthGuard],\n  component: LaunchpadHomeComponent\n}, {\n  path: 'agents',\n  canActivate: [MarketplaceAuthGuard],\n  component: AgentsComponent\n}, {\n  path: 'agent-list',\n  canActivate: [MarketplaceAuthGuard],\n  component: AgentsFilterComponent\n}, {\n  path: 'my-agent-home',\n  canActivate: [MarketplaceAuthGuard],\n  component: MyAgentHomeComponent\n}, {\n  path: 'analytics',\n  canActivate: [MarketplaceAuthGuard],\n  component: AnalyticsComponent\n}, {\n  path: 'documents',\n  canActivate: [MarketplaceAuthGuard],\n  component: DocumentsComponent\n}, {\n  path: 'tools',\n  redirectTo: '/dashboard'\n}, {\n  path: 'team',\n  redirectTo: '/dashboard'\n}, {\n  path: '',\n  component: RootRedirectComponent,\n  pathMatch: 'full'\n}, {\n  path: '**',\n  redirectTo: '/marketplace'\n}];", "map": {"version": 3, "names": ["MarketplaceAuthGuard", "LoginComponent", "CallbackComponent", "LaunchpadHomeComponent", "AgentsFilterComponent", "MyAgentHomeComponent", "MarketplaceComponent", "RootRedirectComponent", "AgentsComponent", "AnalyticsComponent", "DocumentsComponent", "routes", "path", "component", "canActivate", "redirectTo", "pathMatch"], "sources": ["C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\app.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\r\nimport { MarketplaceAuthGuard } from './shared/guards/marketplace-auth.guard';\r\nimport { LoginComponent } from '@shared/auth/components/login/login.component';\r\nimport { CallbackComponent } from '@shared/auth/components/callback/callback.component';\r\nimport { LaunchpadHomeComponent } from './pages/launchpad-home/launchpad-home.component';\r\nimport { AgentsFilterComponent } from './pages/agents-filter/agents-filter.component';\r\nimport { MyAgentHomeComponent } from './shared/components/my-agent-home/my-agent-home.component';\r\nimport { MarketplaceComponent } from './pages/marketplace/marketplace.component';\r\nimport { RootRedirectComponent } from './shared/components/root-redirect/root-redirect.component';\r\nimport { AgentsComponent } from './pages/agents/agents.component';\r\nimport { AnalyticsComponent } from './shared/components/analytics/analytics.component';\r\nimport { DocumentsComponent } from './shared/components/documents/documents.component';\r\n\r\nexport const routes: Routes = [\r\n  { path: 'marketplace', component: MarketplaceComponent },\r\n  { path: 'login', component: LoginComponent },\r\n  { path: 'callback', component: CallbackComponent },\r\n\r\n  {\r\n    path: 'dashboard',\r\n    canActivate: [MarketplaceAuthGuard],\r\n    component: LaunchpadHomeComponent,\r\n  },\r\n  {\r\n    path: 'agents',\r\n    canActivate: [MarketplaceAuthGuard],\r\n    component: AgentsComponent,\r\n  },\r\n  {\r\n    path: 'agent-list',\r\n    canActivate: [MarketplaceAuthGuard],\r\n    component: AgentsFilterComponent,\r\n  },\r\n  {\r\n    path: 'my-agent-home',\r\n    canActivate: [MarketplaceAuthGuard],\r\n    component: MyAgentHomeComponent,\r\n  },\r\n  {\r\n    path: 'analytics',\r\n    canActivate: [MarketplaceAuthGuard],\r\n    component: AnalyticsComponent,\r\n  },\r\n  {\r\n    path: 'documents',\r\n    canActivate: [MarketplaceAuthGuard],\r\n    component: DocumentsComponent,\r\n  },\r\n  {\r\n    path: 'tools',\r\n    redirectTo: '/dashboard',\r\n  },\r\n  {\r\n    path: 'team',\r\n    redirectTo: '/dashboard',\r\n  },\r\n  {\r\n    path: '',\r\n    component: RootRedirectComponent,\r\n    pathMatch: 'full',\r\n  },\r\n  {\r\n    path: '**',\r\n    redirectTo: '/marketplace',\r\n  },\r\n];\r\n"], "mappings": "AACA,SAASA,oBAAoB,QAAQ,wCAAwC;AAC7E,SAASC,cAAc,QAAQ,+CAA+C;AAC9E,SAASC,iBAAiB,QAAQ,qDAAqD;AACvF,SAASC,sBAAsB,QAAQ,iDAAiD;AACxF,SAASC,qBAAqB,QAAQ,+CAA+C;AACrF,SAASC,oBAAoB,QAAQ,2DAA2D;AAChG,SAASC,oBAAoB,QAAQ,2CAA2C;AAChF,SAASC,qBAAqB,QAAQ,2DAA2D;AACjG,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,kBAAkB,QAAQ,mDAAmD;AACtF,SAASC,kBAAkB,QAAQ,mDAAmD;AAEtF,OAAO,MAAMC,MAAM,GAAW,CAC5B;EAAEC,IAAI,EAAE,aAAa;EAAEC,SAAS,EAAEP;AAAoB,CAAE,EACxD;EAAEM,IAAI,EAAE,OAAO;EAAEC,SAAS,EAAEZ;AAAc,CAAE,EAC5C;EAAEW,IAAI,EAAE,UAAU;EAAEC,SAAS,EAAEX;AAAiB,CAAE,EAElD;EACEU,IAAI,EAAE,WAAW;EACjBE,WAAW,EAAE,CAACd,oBAAoB,CAAC;EACnCa,SAAS,EAAEV;CACZ,EACD;EACES,IAAI,EAAE,QAAQ;EACdE,WAAW,EAAE,CAACd,oBAAoB,CAAC;EACnCa,SAAS,EAAEL;CACZ,EACD;EACEI,IAAI,EAAE,YAAY;EAClBE,WAAW,EAAE,CAACd,oBAAoB,CAAC;EACnCa,SAAS,EAAET;CACZ,EACD;EACEQ,IAAI,EAAE,eAAe;EACrBE,WAAW,EAAE,CAACd,oBAAoB,CAAC;EACnCa,SAAS,EAAER;CACZ,EACD;EACEO,IAAI,EAAE,WAAW;EACjBE,WAAW,EAAE,CAACd,oBAAoB,CAAC;EACnCa,SAAS,EAAEJ;CACZ,EACD;EACEG,IAAI,EAAE,WAAW;EACjBE,WAAW,EAAE,CAACd,oBAAoB,CAAC;EACnCa,SAAS,EAAEH;CACZ,EACD;EACEE,IAAI,EAAE,OAAO;EACbG,UAAU,EAAE;CACb,EACD;EACEH,IAAI,EAAE,MAAM;EACZG,UAAU,EAAE;CACb,EACD;EACEH,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEN,qBAAqB;EAChCS,SAAS,EAAE;CACZ,EACD;EACEJ,IAAI,EAAE,IAAI;EACVG,UAAU,EAAE;CACb,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}