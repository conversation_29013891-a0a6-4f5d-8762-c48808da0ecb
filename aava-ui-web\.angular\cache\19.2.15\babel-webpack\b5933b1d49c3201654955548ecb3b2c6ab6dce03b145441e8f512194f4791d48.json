{"ast": null, "code": "// Helper function to safely get environment variables from window.env\nconst getRequiredEnv = key => {\n  const envWindow = window;\n  const value = envWindow.env?.[key];\n  if (value === undefined || value === null) {\n    throw new Error(`Environment variable '${key}' is not defined in window.env.`);\n  }\n  return String(value);\n};\nconst dynamicBaseUrl = getRequiredEnv('baseUrl');\nexport const environment = {\n  production: false,\n  experienceStudioUrl: getRequiredEnv('experienceStudioUrl'),\n  productStudioUrl: getRequiredEnv('productStudioUrl'),\n  elderWandApiAuthUrl: getRequiredEnv('consoleApiAuthUrl'),\n  elderWandRedirectUrl: getRequiredEnv('elderWandUrl'),\n  apiBaseUrl: getRequiredEnv('baseUrl'),\n  apiUrl: getRequiredEnv('baseUrl'),\n  getApiUrl: endpoint => {\n    const baseUrl = getRequiredEnv('baseUrl');\n    return `${baseUrl}${endpoint}`;\n  }\n};", "map": {"version": 3, "names": ["getRequiredEnv", "key", "envWindow", "window", "value", "env", "undefined", "Error", "String", "dynamicBaseUrl", "environment", "production", "experienceStudioUrl", "productStudioUrl", "elderWandApiAuthUrl", "elderWandRedirectUrl", "apiBaseUrl", "apiUrl", "getApiUrl", "endpoint", "baseUrl"], "sources": ["C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\environments\\environment.ts"], "sourcesContent": ["// Helper function to safely get environment variables from window.env\r\nconst getRequiredEnv = (key: string): string => {\r\n  interface EnvWindow extends Window { env?: Record<string, string>; }\r\n  const envWindow = window as EnvWindow;\r\n  const value = envWindow.env?.[key];\r\n  if (value === undefined || value === null) {\r\n    throw new Error(`Environment variable '${key}' is not defined in window.env.`);\r\n  }\r\n  return String(value);\r\n};\r\n\r\nconst dynamicBaseUrl: string =getRequiredEnv('baseUrl');\r\n\r\nexport const environment = {\r\n  production: false,\r\n  experienceStudioUrl: getRequiredEnv('experienceStudioUrl'),\r\n  productStudioUrl: getRequiredEnv('productStudioUrl'),\r\n  elderWandApiAuthUrl: getRequiredEnv('consoleApiAuthUrl'),\r\n  elderWandRedirectUrl: getRequiredEnv('elderWandUrl'),\r\n  apiBaseUrl: getRequiredEnv('baseUrl'),\r\n  apiUrl: getRequiredEnv('baseUrl'),\r\n  getApiUrl: (endpoint: string) => {\r\n    const baseUrl = getRequiredEnv('baseUrl');\r\n    return `${baseUrl}${endpoint}`;\r\n  }\r\n};"], "mappings": "AAAA;AACA,MAAMA,cAAc,GAAIC,GAAW,IAAY;EAE7C,MAAMC,SAAS,GAAGC,MAAmB;EACrC,MAAMC,KAAK,GAAGF,SAAS,CAACG,GAAG,GAAGJ,GAAG,CAAC;EAClC,IAAIG,KAAK,KAAKE,SAAS,IAAIF,KAAK,KAAK,IAAI,EAAE;IACzC,MAAM,IAAIG,KAAK,CAAC,yBAAyBN,GAAG,iCAAiC,CAAC;EAChF;EACA,OAAOO,MAAM,CAACJ,KAAK,CAAC;AACtB,CAAC;AAED,MAAMK,cAAc,GAAUT,cAAc,CAAC,SAAS,CAAC;AAEvD,OAAO,MAAMU,WAAW,GAAG;EACzBC,UAAU,EAAE,KAAK;EACjBC,mBAAmB,EAAEZ,cAAc,CAAC,qBAAqB,CAAC;EAC1Da,gBAAgB,EAAEb,cAAc,CAAC,kBAAkB,CAAC;EACpDc,mBAAmB,EAAEd,cAAc,CAAC,mBAAmB,CAAC;EACxDe,oBAAoB,EAAEf,cAAc,CAAC,cAAc,CAAC;EACpDgB,UAAU,EAAEhB,cAAc,CAAC,SAAS,CAAC;EACrCiB,MAAM,EAAEjB,cAAc,CAAC,SAAS,CAAC;EACjCkB,SAAS,EAAGC,QAAgB,IAAI;IAC9B,MAAMC,OAAO,GAAGpB,cAAc,CAAC,SAAS,CAAC;IACzC,OAAO,GAAGoB,OAAO,GAAGD,QAAQ,EAAE;EAChC;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}