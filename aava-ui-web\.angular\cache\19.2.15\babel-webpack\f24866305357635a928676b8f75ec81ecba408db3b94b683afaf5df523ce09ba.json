{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { Subject, takeUntil, switchMap, finalize, catchError, of } from 'rxjs';\nimport { FormsModule } from '@angular/forms';\n// Import child components\nimport { AgentExecutionPlaygroundComponent } from './components/agent-execution-playground/agent-execution-playground.component';\nimport { IconComponent } from '@ava/play-comp-library';\nimport { environment } from '@shared/environments/environment';\nimport { ExecutionStatus } from '@shared/models/execution.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../services/agent-service.service\";\nimport * as i3 from \"../build-agents/services/agent-playground.service\";\nimport * as i4 from \"@shared/auth/services/token-storage.service\";\nimport * as i5 from \"@shared/services/loader/loader.service\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"@shared/services/tool-execution/tool-execution.service\";\nimport * as i8 from \"@angular/common\";\nfunction AgentExecutionComponent_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 19);\n    i0.ɵɵtext(1, \" History \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"app-agent-execution-playground\", 20);\n    i0.ɵɵlistener(\"promptChange\", function AgentExecutionComponent_div_13_Template_app_agent_execution_playground_promptChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPromptChanged($event));\n    })(\"messageSent\", function AgentExecutionComponent_div_13_Template_app_agent_execution_playground_messageSent_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.handleChatMessage($event));\n    })(\"conversationalToggle\", function AgentExecutionComponent_div_13_Template_app_agent_execution_playground_conversationalToggle_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPlaygroundConversationalToggle($event));\n    })(\"templateToggle\", function AgentExecutionComponent_div_13_Template_app_agent_execution_playground_templateToggle_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPlaygroundTemplateToggle($event));\n    })(\"filesSelected\", function AgentExecutionComponent_div_13_Template_app_agent_execution_playground_filesSelected_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFilesSelected($event));\n    })(\"approvalRequested\", function AgentExecutionComponent_div_13_Template_app_agent_execution_playground_approvalRequested_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onApprovalRequested());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"messages\", ctx_r1.chatMessages)(\"isLoading\", ctx_r1.isProcessingChat)(\"agentType\", ctx_r1.agentType)(\"showChatInteractionToggles\", ctx_r1.agentType === \"individual\")(\"showAiPrincipleToggle\", false)(\"showApprovalButton\", false)(\"showDropdown\", false)(\"showAgentNameInput\", true)(\"showFileUploadButton\", true)(\"showStatusOnly\", true)(\"displayedAgentName\", ctx_r1.agentName)(\"agentNamePlaceholder\", \"Current Agent Name\");\n  }\n}\nfunction AgentExecutionComponent_div_22_div_40_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵtext(1, \" No prompt configured \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_22_div_40_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"span\", 77);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(node_r4.name || \"Prompt\");\n  }\n}\nfunction AgentExecutionComponent_div_22_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_22_div_40_div_1_Template, 2, 0, \"div\", 72);\n    i0.ɵɵelementStart(2, \"div\", 73);\n    i0.ɵɵtemplate(3, AgentExecutionComponent_div_22_div_40_div_3_Template, 3, 1, \"div\", 74);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.blueprintPromptNodes.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.blueprintPromptNodes);\n  }\n}\nfunction AgentExecutionComponent_div_22_div_61_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵtext(1, \" No knowledge base configured \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_22_div_61_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"span\", 77);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(node_r5.name || \"Knowledge Base\");\n  }\n}\nfunction AgentExecutionComponent_div_22_div_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_22_div_61_div_1_Template, 2, 0, \"div\", 72);\n    i0.ɵɵelementStart(2, \"div\", 73);\n    i0.ɵɵtemplate(3, AgentExecutionComponent_div_22_div_61_div_3_Template, 3, 1, \"div\", 74);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.blueprintKnowledgeNodes.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.blueprintKnowledgeNodes);\n  }\n}\nfunction AgentExecutionComponent_div_22_div_79_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵtext(1, \" No model configured \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_22_div_79_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"span\", 77);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(node_r6.name || \"Model\");\n  }\n}\nfunction AgentExecutionComponent_div_22_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_22_div_79_div_1_Template, 2, 0, \"div\", 72);\n    i0.ɵɵelementStart(2, \"div\", 73);\n    i0.ɵɵtemplate(3, AgentExecutionComponent_div_22_div_79_div_3_Template, 3, 1, \"div\", 74);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.blueprintModelNodes.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.blueprintModelNodes);\n  }\n}\nfunction AgentExecutionComponent_div_22_div_80__svg_svg_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 45);\n    i0.ɵɵelement(1, \"rect\", 80)(2, \"path\", 81);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_22_div_80_div_13_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵtext(1, \" No tools configured \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_22_div_80_div_13_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"span\", 77);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r8 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(node_r8.name || \"Tool\");\n  }\n}\nfunction AgentExecutionComponent_div_22_div_80_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_22_div_80_div_13_div_1_Template, 2, 0, \"div\", 72);\n    i0.ɵɵelementStart(2, \"div\", 73);\n    i0.ɵɵtemplate(3, AgentExecutionComponent_div_22_div_80_div_13_div_3_Template, 3, 1, \"div\", 74);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.blueprintToolNodes.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.blueprintToolNodes);\n  }\n}\nfunction AgentExecutionComponent_div_22_div_80_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"div\", 42);\n    i0.ɵɵlistener(\"click\", function AgentExecutionComponent_div_22_div_80_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleBlueprintZone(\"tool\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 43)(3, \"div\", 44);\n    i0.ɵɵtemplate(4, AgentExecutionComponent_div_22_div_80__svg_svg_4_Template, 3, 0, \"svg\", 79);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h3\", 48);\n    i0.ɵɵtext(6, \"Tools\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 49)(8, \"span\", 63);\n    i0.ɵɵtext(9, \"Optional\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 51);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(11, \"svg\", 52);\n    i0.ɵɵelement(12, \"path\", 53);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(13, AgentExecutionComponent_div_22_div_80_div_13_Template, 4, 2, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"has-nodes\", ctx_r1.blueprintToolNodes.length > 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.agentType === \"collaborative\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵstyleProp(\"transform\", ctx_r1.isBlueprintZoneExpanded(\"tool\") ? \"rotate(180deg)\" : \"rotate(0deg)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isBlueprintZoneExpanded(\"tool\"));\n  }\n}\nfunction AgentExecutionComponent_div_22_div_81_div_15_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵtext(1, \" No guardrails configured \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_22_div_81_div_15_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"span\", 77);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r10 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(node_r10.name || \"Guardrail\");\n  }\n}\nfunction AgentExecutionComponent_div_22_div_81_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_22_div_81_div_15_div_1_Template, 2, 0, \"div\", 72);\n    i0.ɵɵelementStart(2, \"div\", 73);\n    i0.ɵɵtemplate(3, AgentExecutionComponent_div_22_div_81_div_15_div_3_Template, 3, 1, \"div\", 74);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.blueprintGuardrailNodes.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.blueprintGuardrailNodes);\n  }\n}\nfunction AgentExecutionComponent_div_22_div_81_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 82)(1, \"div\", 42);\n    i0.ɵɵlistener(\"click\", function AgentExecutionComponent_div_22_div_81_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleBlueprintZone(\"guardrail\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 43)(3, \"div\", 44);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(4, \"svg\", 45);\n    i0.ɵɵelement(5, \"rect\", 83)(6, \"path\", 84);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(7, \"h3\", 48);\n    i0.ɵɵtext(8, \"Guardrails\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 49)(10, \"span\", 63);\n    i0.ɵɵtext(11, \"Optional\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 51);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(13, \"svg\", 52);\n    i0.ɵɵelement(14, \"path\", 53);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(15, AgentExecutionComponent_div_22_div_81_div_15_Template, 4, 2, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"has-nodes\", ctx_r1.blueprintGuardrailNodes.length > 0);\n    i0.ɵɵadvance(13);\n    i0.ɵɵstyleProp(\"transform\", ctx_r1.isBlueprintZoneExpanded(\"guardrail\") ? \"rotate(180deg)\" : \"rotate(0deg)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isBlueprintZoneExpanded(\"guardrail\"));\n  }\n}\nfunction AgentExecutionComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22)(2, \"h3\");\n    i0.ɵɵtext(3, \"Agent Blueprint\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 23);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(5, \"svg\", 24);\n    i0.ɵɵelement(6, \"line\", 25)(7, \"line\", 26)(8, \"line\", 27)(9, \"line\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(10, \"div\", 29)(11, \"div\", 30);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(12, \"svg\", 31)(13, \"defs\")(14, \"linearGradient\", 32);\n    i0.ɵɵelement(15, \"stop\", 33)(16, \"stop\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(17, \"circle\", 35)(18, \"circle\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(19, \"div\", 37)(20, \"div\", 38);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 39);\n    i0.ɵɵtext(23, \"Complete\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(24, \"div\", 40)(25, \"div\", 41)(26, \"div\", 42);\n    i0.ɵɵlistener(\"click\", function AgentExecutionComponent_div_22_Template_div_click_26_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleBlueprintZone(\"prompt\"));\n    });\n    i0.ɵɵelementStart(27, \"div\", 43)(28, \"div\", 44);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(29, \"svg\", 45);\n    i0.ɵɵelement(30, \"rect\", 46)(31, \"path\", 47);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(32, \"h3\", 48);\n    i0.ɵɵtext(33, \"System Prompt\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 49)(35, \"span\", 50);\n    i0.ɵɵtext(36, \"Required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"button\", 51);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(38, \"svg\", 52);\n    i0.ɵɵelement(39, \"path\", 53);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(40, AgentExecutionComponent_div_22_div_40_Template, 4, 2, \"div\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(41, \"div\", 55)(42, \"div\", 42);\n    i0.ɵɵlistener(\"click\", function AgentExecutionComponent_div_22_Template_div_click_42_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleBlueprintZone(\"knowledge\"));\n    });\n    i0.ɵɵelementStart(43, \"div\", 43)(44, \"div\", 44);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(45, \"svg\", 45);\n    i0.ɵɵelement(46, \"rect\", 56)(47, \"path\", 57)(48, \"path\", 58)(49, \"path\", 59)(50, \"path\", 60)(51, \"path\", 61)(52, \"path\", 62);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(53, \"h3\", 48);\n    i0.ɵɵtext(54, \"Knowledgebase\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(55, \"div\", 49)(56, \"span\", 63);\n    i0.ɵɵtext(57, \"Optional\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"button\", 51);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(59, \"svg\", 52);\n    i0.ɵɵelement(60, \"path\", 53);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(61, AgentExecutionComponent_div_22_div_61_Template, 4, 2, \"div\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(62, \"div\", 64)(63, \"div\", 42);\n    i0.ɵɵlistener(\"click\", function AgentExecutionComponent_div_22_Template_div_click_63_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleBlueprintZone(\"model\"));\n    });\n    i0.ɵɵelementStart(64, \"div\", 43)(65, \"div\", 44);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(66, \"svg\", 45);\n    i0.ɵɵelement(67, \"rect\", 65)(68, \"path\", 66)(69, \"path\", 67)(70, \"path\", 68);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(71, \"h3\", 48);\n    i0.ɵɵtext(72, \"AI Model\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(73, \"div\", 49)(74, \"span\", 50);\n    i0.ɵɵtext(75, \"Required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(76, \"button\", 51);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(77, \"svg\", 52);\n    i0.ɵɵelement(78, \"path\", 53);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(79, AgentExecutionComponent_div_22_div_79_Template, 4, 2, \"div\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(80, AgentExecutionComponent_div_22_div_80_Template, 14, 6, \"div\", 69)(81, AgentExecutionComponent_div_22_div_81_Template, 16, 5, \"div\", 70);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(18);\n    i0.ɵɵstyleProp(\"stroke-dasharray\", 314)(\"stroke-dashoffset\", 314 - 314 * ctx_r1.blueprintCompletionPercentage / 100);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.blueprintCompletionPercentage, \"% \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"has-nodes\", ctx_r1.blueprintPromptNodes.length > 0);\n    i0.ɵɵadvance(13);\n    i0.ɵɵstyleProp(\"transform\", ctx_r1.isBlueprintZoneExpanded(\"prompt\") ? \"rotate(180deg)\" : \"rotate(0deg)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isBlueprintZoneExpanded(\"prompt\"));\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"has-nodes\", ctx_r1.blueprintKnowledgeNodes.length > 0);\n    i0.ɵɵadvance(18);\n    i0.ɵɵstyleProp(\"transform\", ctx_r1.isBlueprintZoneExpanded(\"knowledge\") ? \"rotate(180deg)\" : \"rotate(0deg)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isBlueprintZoneExpanded(\"knowledge\"));\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"has-nodes\", ctx_r1.blueprintModelNodes.length > 0);\n    i0.ɵɵadvance(15);\n    i0.ɵɵstyleProp(\"transform\", ctx_r1.isBlueprintZoneExpanded(\"model\") ? \"rotate(180deg)\" : \"rotate(0deg)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isBlueprintZoneExpanded(\"model\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.agentType === \"collaborative\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.agentType === \"individual\");\n  }\n}\nfunction AgentExecutionComponent_div_23_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"p\");\n    i0.ɵɵtext(2, \"No agent outputs available yet. Send a message to see the response.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AgentExecutionComponent_div_23_div_5_div_1_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 100)(1, \"div\", 101)(2, \"h4\");\n    i0.ɵɵtext(3, \"Response\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 102);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const execution_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (execution_r11.response == null ? null : execution_r11.response.response == null ? null : execution_r11.response.response.choices == null ? null : execution_r11.response.response.choices[0] == null ? null : execution_r11.response.response.choices[0].text) || (execution_r11.response == null ? null : execution_r11.response.agentResponse == null ? null : execution_r11.response.agentResponse.detail) || \"No response content available.\", \" \");\n  }\n}\nfunction AgentExecutionComponent_div_23_div_5_div_1_div_15_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 109)(1, \"strong\");\n    i0.ɵɵtext(2, \"Description:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const taskOutput_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", taskOutput_r12.description, \" \");\n  }\n}\nfunction AgentExecutionComponent_div_23_div_5_div_1_div_15_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 110)(1, \"strong\");\n    i0.ɵɵtext(2, \"Expected Output:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const taskOutput_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", taskOutput_r12.expected_output, \" \");\n  }\n}\nfunction AgentExecutionComponent_div_23_div_5_div_1_div_15_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 104);\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_23_div_5_div_1_div_15_div_1_div_1_Template, 4, 1, \"div\", 105)(2, AgentExecutionComponent_div_23_div_5_div_1_div_15_div_1_div_2_Template, 4, 1, \"div\", 106);\n    i0.ɵɵelementStart(3, \"div\", 107);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 108)(6, \"h4\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const taskOutput_r12 = ctx.$implicit;\n    const j_r13 = ctx.index;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", taskOutput_r12.description);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", taskOutput_r12.expected_output);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", taskOutput_r12.raw, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Summary: \", taskOutput_r12.summary || \"Task \" + (j_r13 + 1), \"\");\n  }\n}\nfunction AgentExecutionComponent_div_23_div_5_div_1_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 100);\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_23_div_5_div_1_div_15_div_1_Template, 8, 4, \"div\", 103);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const execution_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", execution_r11.response == null ? null : execution_r11.response.agentResponse == null ? null : execution_r11.response.agentResponse.agent == null ? null : execution_r11.response.agentResponse.agent.tasksOutputs);\n  }\n}\nfunction AgentExecutionComponent_div_23_div_5_div_1_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 111)(1, \"div\", 112)(2, \"strong\");\n    i0.ɵɵtext(3, \"Error:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" The agent execution failed. Please try again. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AgentExecutionComponent_div_23_div_5_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 92)(1, \"div\", 93)(2, \"div\", 5);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 94);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 95)(7, \"div\", 96)(8, \"strong\");\n    i0.ɵɵtext(9, \"Query:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 97);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(14, AgentExecutionComponent_div_23_div_5_div_1_div_14_Template, 6, 1, \"div\", 98)(15, AgentExecutionComponent_div_23_div_5_div_1_div_15_Template, 2, 1, \"div\", 98)(16, AgentExecutionComponent_div_23_div_5_div_1_div_16_Template, 5, 0, \"div\", 99);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const execution_r11 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"success\", execution_r11.status === \"success\")(\"failed\", execution_r11.status === \"failed\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(execution_r11.agentName);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(execution_r11.status);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", execution_r11.status === \"success\" ? \"Success\" : \"Failed\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", execution_r11.userMessage, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(13, 13, execution_r11.timestamp, \"medium\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", execution_r11.status === \"success\" && execution_r11.response && ctx_r1.agentType === \"individual\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", execution_r11.status === \"success\" && execution_r11.response && ctx_r1.agentType === \"collaborative\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", execution_r11.status === \"failed\");\n  }\n}\nfunction AgentExecutionComponent_div_23_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 90);\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_23_div_5_div_1_Template, 17, 16, \"div\", 91);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.executionHistory);\n  }\n}\nfunction AgentExecutionComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 85)(1, \"h3\");\n    i0.ɵɵtext(2, \"Agent Output\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 86);\n    i0.ɵɵtemplate(4, AgentExecutionComponent_div_23_div_4_Template, 3, 0, \"div\", 87)(5, AgentExecutionComponent_div_23_div_5_Template, 2, 1, \"div\", 88);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.executionHistory.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.executionHistory.length > 0);\n  }\n}\n// Remove duplicate definitions - they're now in shared models\nexport let AgentExecutionComponent = /*#__PURE__*/(() => {\n  class AgentExecutionComponent {\n    route;\n    router;\n    agentService;\n    agentPlaygroundService;\n    tokenStorage;\n    loaderService;\n    formBuilder;\n    toolExecutionService;\n    navigationTabs = [{\n      id: 'nav-home',\n      label: 'Agent Activity'\n    }, {\n      id: 'nav-products',\n      label: 'Agent Output'\n    }, {\n      id: 'nav-services',\n      label: 'Preview',\n      disabled: true\n    }];\n    // Agent details\n    agentId = null;\n    agentType = 'individual';\n    agentName = 'Agent';\n    agentDetail = '';\n    playgroundComp;\n    // Activity logs\n    activityLogs = [];\n    activityProgress = 0;\n    executionDetails;\n    isRunning = false;\n    status = ExecutionStatus.notStarted;\n    // Chat messages\n    chatMessages = [];\n    isProcessingChat = false;\n    inputText = '';\n    // Agent outputs\n    agentOutputs = [];\n    latestAgentResponse = null; // Store the latest agent response for display\n    // New properties for execution history\n    executionHistory = [];\n    agentForm;\n    // Execution state\n    executionStartTime = null;\n    executionCompleted = false;\n    executionId;\n    enableStreamingLog = environment.enableLogStreaming || 'all';\n    isExecutionComplete = false;\n    progressInterval;\n    destroy$ = new Subject();\n    selectedTab = 'Agent Activity';\n    demoTabs = [{\n      id: 'tab1',\n      label: 'History'\n    }, {\n      id: 'tab2',\n      label: 'Blueprint'\n    }, {\n      id: 'tab3',\n      label: 'Agent Output'\n    }];\n    errorMsg = false;\n    resMessage;\n    taskMessage = [];\n    isJsonValid = false;\n    disableChat = false;\n    selectedFiles = [];\n    agentNodes = [];\n    userInputList = [];\n    progress = 0;\n    isLoading = false;\n    loaderColor = '';\n    inputFieldOrder = [];\n    currentInputIndex = 0;\n    activeTabId = 'nav-home';\n    // Panel state properties\n    isLeftPanelCollapsed = false;\n    activeRightTab = 'blueprint';\n    // Agent-specific properties\n    currentAgentDetails = null;\n    buildAgentNodes = [];\n    canvasNodes = [];\n    canvasEdges = [];\n    selectedPrompt = '';\n    selectedAgentMode = '';\n    selectedUseCaseIdentifier = '';\n    agentFilesUploadedData = [];\n    agentAttachment = [];\n    isAgentPlaygroundLoading = false;\n    agentPlaygroundDestroy = new Subject();\n    agentChatPayload = [];\n    agentCode = '';\n    promptOptions = [];\n    // Custom Blueprint Display Properties\n    blueprintCompletionPercentage = 0;\n    blueprintPromptNodes = [];\n    blueprintModelNodes = [];\n    blueprintKnowledgeNodes = [];\n    blueprintGuardrailNodes = [];\n    blueprintToolNodes = [];\n    // Blueprint zone expansion state\n    blueprintZonesExpanded = {\n      prompt: true,\n      model: true,\n      knowledge: true,\n      guardrail: true,\n      tool: true\n    };\n    // Blueprint panel properties (using existing arrays above)\n    constructor(route, router, agentService, agentPlaygroundService, tokenStorage, loaderService, formBuilder, toolExecutionService) {\n      this.route = route;\n      this.router = router;\n      this.agentService = agentService;\n      this.agentPlaygroundService = agentPlaygroundService;\n      this.tokenStorage = tokenStorage;\n      this.loaderService = loaderService;\n      this.formBuilder = formBuilder;\n      this.toolExecutionService = toolExecutionService;\n      this.agentForm = this.formBuilder.group({\n        isConversational: [true],\n        isUseTemplate: [false]\n      });\n    }\n    ngOnInit() {\n      console.log('🌟 SHARED COMPONENT INITIALIZED! 🌟');\n      this.executionId = crypto.randomUUID();\n      this.route.params.subscribe(params => {\n        this.agentType = params['type'] || 'individual';\n        console.log('🌟 SHARED: Agent type set to:', this.agentType);\n      });\n      this.route.queryParams.subscribe(params => {\n        if (params['id']) {\n          this.agentId = params['id'];\n          this.loadAgentData(params['id']);\n        }\n      });\n      // Initialize chat messages\n      this.chatMessages = [{\n        from: 'ai',\n        text: `Hi there! I am ${this.agentName || 'your agent'}. How can I help you today?`\n      }];\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n      if (this.progressInterval) {\n        clearInterval(this.progressInterval);\n      }\n    }\n    onTabChange(event) {\n      this.activeTabId = event.id;\n      this.selectedTab = event.label;\n    }\n    loadAgentData(agentId) {\n      this.isLoading = true;\n      // Load agent data based on type\n      if (this.agentType === 'collaborative') {\n        this.agentService.getCollaborativeAgentDetailsById(agentId).subscribe({\n          next: response => {\n            this.handleAgentDataResponse(response);\n          },\n          error: error => {\n            console.error('Error loading collaborative agent:', error);\n            this.isLoading = false;\n          }\n        });\n      } else {\n        this.agentService.getAgentById(agentId).subscribe({\n          next: response => {\n            this.handleAgentDataResponse(response);\n          },\n          error: error => {\n            console.error('Error loading individual agent:', error);\n            this.isLoading = false;\n          }\n        });\n      }\n    }\n    handleAgentDataResponse(response) {\n      this.isLoading = false;\n      // Extract agent details\n      let agentData;\n      if (response.agentDetails && Array.isArray(response.agentDetails) && response.agentDetails.length > 0) {\n        agentData = response.agentDetails[0];\n      } else if (response.agentDetail) {\n        agentData = response.agentDetail;\n      } else if (response.data) {\n        agentData = response.data;\n      } else {\n        agentData = response;\n      }\n      if (agentData) {\n        this.currentAgentDetails = agentData;\n        this.agentName = agentData.name || agentData.agentName || 'Agent';\n        this.agentDetail = agentData.description || agentData.agentDetail || '';\n        // For individual agents, set up the required properties for playground functionality\n        if (this.agentType === 'individual') {\n          // Set selectedPrompt to agent name or useCaseName to bypass dropdown requirement\n          this.selectedPrompt = agentData.useCaseName || agentData.name || 'loaded-agent';\n          // Set selectedAgentMode for API calls - use useCaseCode if available\n          this.selectedAgentMode = agentData.useCaseCode || agentData.useCaseName || agentData.name || '';\n          // Set useCaseIdentifier - use organizationPath if available\n          if (agentData.organizationPath) {\n            this.selectedUseCaseIdentifier = agentData.organizationPath;\n          } else if (agentData.useCaseCode) {\n            this.selectedUseCaseIdentifier = agentData.useCaseCode;\n          } else if (agentData.useCaseName) {\n            this.selectedUseCaseIdentifier = agentData.useCaseName;\n          }\n        }\n        // Update chat message with agent name\n        if (this.chatMessages.length > 0) {\n          this.chatMessages[0].text = `Hi there! I am ${this.agentName}. How can I help you today?`;\n        }\n        // Load agent nodes and configuration\n        this.loadAgentNodes(agentData);\n      }\n    }\n    loadAgentNodes(agentData) {\n      // Map agent configuration to blueprint panel\n      this.mapAgentConfigurationToBlueprint(agentData);\n    }\n    handleChatMessage(message) {\n      if (this.agentType === 'individual') {\n        // For individual agents, use the loaded agent details instead of requiring dropdown selection\n        if (!this.currentAgentDetails && (!this.selectedPrompt || this.selectedPrompt === 'default')) {\n          this.showAgentError('Agent details are not loaded. Please try refreshing the page.');\n          return;\n        }\n        let displayMessage = message;\n        if (this.agentFilesUploadedData.length > 0) {\n          const fileNames = this.agentFilesUploadedData.map(file => file.documentName).join(', ');\n          displayMessage = `${message}\\n\\n📎 Attached files: ${fileNames}`;\n        }\n        // Add to execution history\n        const executionId = crypto.randomUUID();\n        this.executionHistory.push({\n          id: executionId,\n          agentName: this.agentName,\n          userMessage: message,\n          status: 'success',\n          // Will be updated based on API response\n          timestamp: new Date()\n        });\n        console.log('Added new execution to history:', this.executionHistory);\n        this.chatMessages = [...this.chatMessages, {\n          from: 'user',\n          text: displayMessage\n        }];\n        this.isProcessingChat = true;\n        const isConversational = this.agentForm.get('isConversational')?.value || false;\n        const isUseTemplate = this.agentForm.get('isUseTemplate')?.value || false;\n        console.log('Chat message handling - isConversational:', isConversational, 'isUseTemplate:', isUseTemplate);\n        // Use agent details from the loaded agent data instead of dropdown selection\n        // Mode should be the useCaseCode, not useCaseName\n        const agentMode = this.agentCode || this.selectedAgentMode || this.currentAgentDetails?.useCaseCode || this.currentAgentDetails?.useCaseName || this.currentAgentDetails?.name || this.selectedPrompt;\n        let useCaseIdentifier = this.selectedUseCaseIdentifier;\n        if (!useCaseIdentifier) {\n          // Use organizationPath if available, otherwise build from agent details\n          if (this.currentAgentDetails?.organizationPath) {\n            useCaseIdentifier = this.currentAgentDetails.organizationPath;\n          } else {\n            const orgPath = this.buildOrganizationPath();\n            const agentIdentifier = this.currentAgentDetails?.useCaseCode || this.currentAgentDetails?.useCaseName || this.currentAgentDetails?.name || agentMode;\n            useCaseIdentifier = `${agentIdentifier}${orgPath}`;\n          }\n        }\n        if (this.agentFilesUploadedData.length > 0) {\n          this.processAgentFilesAndSendMessage(message, agentMode, useCaseIdentifier, isConversational, isUseTemplate);\n          return;\n        }\n        this.sendAgentMessageToAPI(message, agentMode, useCaseIdentifier, isConversational, isUseTemplate);\n      } else if (this.agentType === 'collaborative') {\n        // Add to execution history for collaborative agents too\n        const executionId = crypto.randomUUID();\n        this.executionHistory.push({\n          id: executionId,\n          agentName: this.agentName,\n          userMessage: message,\n          status: 'success',\n          // Will be updated based on API response\n          timestamp: new Date()\n        });\n        console.log('Added new collaborative execution to history:', this.executionHistory);\n        this.isProcessingChat = true;\n        let payload = {\n          executionId: this.executionId,\n          agentId: Number(this.agentId),\n          user: this.tokenStorage.getDaUsername() || '<EMAIL>',\n          userInputs: {\n            question: message\n          }\n        };\n        if (this.agentFilesUploadedData.length > 0) {\n          const fileWrapper = this.agentFilesUploadedData[0];\n          let displayMessage;\n          if (this.agentFilesUploadedData.length > 0) {\n            const fileNames = this.agentFilesUploadedData.map(file => file.documentName).join(', ');\n            displayMessage = `📎 Attached files: ${fileNames}`;\n            this.chatMessages = [{\n              from: 'user',\n              text: displayMessage\n            }];\n          }\n          this.agentPlaygroundService.submitAgentExecuteWithFile(payload, fileWrapper).pipe(finalize(() => {\n            this.isProcessingChat = false;\n            this.isAgentPlaygroundLoading = false;\n          }), takeUntil(this.agentPlaygroundDestroy)).subscribe({\n            next: res => this.handleAgentExecuteResponse(res, message),\n            error: err => {\n              // Update execution history with failed status\n              if (this.executionHistory.length > 0) {\n                this.executionHistory[this.executionHistory.length - 1].status = 'failed';\n              }\n              this.chatMessages = [...this.chatMessages, {\n                from: 'user',\n                text: message\n              }, {\n                from: 'ai',\n                text: err?.error?.message || err?.message || 'Something went wrong.'\n              }];\n              // Switch to output tab even on failure\n              this.setActiveRightTab('output');\n            }\n          });\n        } else {\n          this.agentPlaygroundService.submitAgentExecute(payload).pipe(finalize(() => {\n            this.isProcessingChat = false;\n            this.isAgentPlaygroundLoading = false;\n          }), takeUntil(this.agentPlaygroundDestroy)).subscribe({\n            next: res => this.handleAgentExecuteResponse(res, message),\n            error: err => {\n              // Update execution history with failed status\n              if (this.executionHistory.length > 0) {\n                this.executionHistory[this.executionHistory.length - 1].status = 'failed';\n              }\n              this.chatMessages = [...this.chatMessages, {\n                from: 'user',\n                text: message\n              }, {\n                from: 'ai',\n                text: err?.error?.message || err?.message || 'Something went wrong.'\n              }];\n              // Switch to output tab even on failure\n              this.setActiveRightTab('output');\n            }\n          });\n        }\n      }\n    }\n    onPromptChanged(prompt) {\n      this.inputText = prompt.name || String(prompt.value) || '';\n    }\n    onPlaygroundConversationalToggle(value) {\n      // Update the form control\n      this.agentForm.get('isConversational')?.setValue(value);\n      // When conversational mode is turned off, clear the conversation history\n      // This ensures that the next message will be treated as a fresh start\n      if (!value) {\n        this.agentChatPayload = [];\n        console.log('Conversational mode disabled - cleared chat payload history');\n      } else {\n        console.log('Conversational mode enabled - will maintain chat history');\n      }\n    }\n    onPlaygroundTemplateToggle(value) {\n      // Update the form control\n      this.agentForm.get('isUseTemplate')?.setValue(value);\n      console.log('Template mode toggled:', value);\n    }\n    onFilesSelected(files) {\n      this.selectedFiles = files;\n      // Update agentFilesUploadedData for agent execution\n      this.agentFilesUploadedData = files;\n    }\n    onApprovalRequested() {\n      // Handle approval request\n    }\n    saveLogs() {\n      // Save execution logs\n    }\n    exportResults(section) {\n      // Export results\n    }\n    handleControlAction(action) {\n      // Handle execution control actions\n    }\n    navigateBack() {\n      this.router.navigate(['/build/agents', this.agentType], {\n        queryParams: {\n          id: this.agentId,\n          mode: 'view'\n        }\n      });\n    }\n    editAgent() {\n      this.router.navigate(['/build/agents', this.agentType], {\n        queryParams: {\n          id: this.agentId,\n          mode: 'edit'\n        }\n      });\n    }\n    navigateToAgentsList() {\n      this.router.navigate(['/build/agents']);\n    }\n    toggleLeftPanel() {\n      this.isLeftPanelCollapsed = !this.isLeftPanelCollapsed;\n    }\n    setActiveRightTab(tab) {\n      this.activeRightTab = tab;\n    }\n    // Blueprint zone management methods\n    toggleBlueprintZone(zoneType) {\n      this.blueprintZonesExpanded[zoneType] = !this.blueprintZonesExpanded[zoneType];\n    }\n    isBlueprintZoneExpanded(zoneType) {\n      return this.blueprintZonesExpanded[zoneType] || false;\n    }\n    // API and helper methods from build-agents component\n    showAgentError(message) {\n      this.chatMessages = [...this.chatMessages, {\n        from: 'ai',\n        text: message\n      }];\n    }\n    buildOrganizationPath() {\n      // Simple implementation - in real scenario this would be from navbar/metadata\n      return '';\n    }\n    getMetadataFromNavbar() {\n      // Simple implementation - in real scenario this would get org level mapping\n      return {};\n    }\n    handleAgentExecuteResponse(response, message) {\n      try {\n        // Store the latest response for display in the output panel\n        this.latestAgentResponse = response;\n        // Update the latest execution history entry with success status and response\n        if (this.executionHistory.length > 0) {\n          this.executionHistory[this.executionHistory.length - 1].status = 'success';\n          this.executionHistory[this.executionHistory.length - 1].response = response;\n          console.log('Updated execution history:', this.executionHistory);\n        }\n        const outputRaw = response?.agentResponse?.agent?.output;\n        let formattedOutput = '';\n        if (outputRaw) {\n          // Directly replace escaped \\n with real newlines\n          formattedOutput = outputRaw.replace(/\\\\n/g, '\\n');\n        } else {\n          formattedOutput = response?.agentResponse?.detail;\n        }\n        // In playground, only show agent name and status, not the full response\n        this.chatMessages = [...this.chatMessages, {\n          from: 'user',\n          text: message\n        }, {\n          from: 'ai',\n          text: `${this.agentName} - Status: Success`\n        }];\n        // Automatically switch to output tab after successful execution\n        this.setActiveRightTab('output');\n      } catch (err) {\n        // Update execution history with failed status\n        if (this.executionHistory.length > 0) {\n          this.executionHistory[this.executionHistory.length - 1].status = 'failed';\n          console.log('Updated execution history (failed):', this.executionHistory);\n        }\n        this.chatMessages = [...this.chatMessages, {\n          from: 'user',\n          text: message\n        }, {\n          from: 'ai',\n          text: `${this.agentName} - Status: Failed`\n        }];\n        // Switch to output tab even on failure\n        this.setActiveRightTab('output');\n      }\n    }\n    processAgentFilesAndSendMessage(message, mode, useCaseIdentifier, isConversational, isUseTemplate) {\n      const formData = new FormData();\n      this.agentFilesUploadedData.forEach(fileData => {\n        if (fileData.file) {\n          formData.append('files', fileData.file);\n        }\n      });\n      if (formData.has('files')) {\n        this.agentPlaygroundService.getFileToContent(formData).pipe(switchMap(fileResponse => {\n          const fileContent = fileResponse?.fileResponses?.map(response => response.fileContent)?.join('\\n') || '';\n          this.sendAgentMessageToAPIWithFiles(message, mode, useCaseIdentifier, isConversational, isUseTemplate, fileContent);\n          return of(null);\n        }), catchError(error => {\n          console.error('Error parsing files:', error);\n          this.sendAgentMessageToAPI(message, mode, useCaseIdentifier, isConversational, isUseTemplate);\n          return of(null);\n        })).subscribe();\n      } else {\n        this.sendAgentMessageToAPI(message, mode, useCaseIdentifier, isConversational, isUseTemplate);\n      }\n    }\n    sendAgentMessageToAPI(message, mode, useCaseIdentifier, isConversational, isUseTemplate) {\n      console.log('API Call Parameters:', {\n        message,\n        mode,\n        useCaseIdentifier,\n        isConversational,\n        isUseTemplate,\n        currentChatPayloadLength: this.agentChatPayload.length\n      });\n      if (isConversational) {\n        this.agentChatPayload.push({\n          content: message,\n          role: 'user'\n        });\n      }\n      const payload = isConversational ? this.agentChatPayload : message;\n      const {\n        levelId\n      } = this.getMetadataFromNavbar();\n      console.log('Final payload being sent:', payload);\n      this.agentPlaygroundService.generatePrompt(payload, mode, isConversational, isUseTemplate, this.agentAttachment, useCaseIdentifier, '', levelId).pipe(finalize(() => {\n        this.isProcessingChat = false;\n        this.isAgentPlaygroundLoading = false;\n      }), takeUntil(this.agentPlaygroundDestroy)).subscribe({\n        next: generatedResponse => {\n          // Store the latest response for display in the output panel\n          this.latestAgentResponse = generatedResponse;\n          if (generatedResponse?.response && generatedResponse?.response?.choices) {\n            const aiResponseText = generatedResponse.response.choices[0].text;\n            this.chatMessages = [...this.chatMessages, {\n              from: 'ai',\n              text: aiResponseText\n            }];\n            if (isConversational) {\n              this.agentChatPayload.push({\n                content: aiResponseText,\n                role: 'assistant'\n              });\n            }\n          } else {\n            console.warn('Unexpected API response format:', generatedResponse);\n            this.showAgentError('Received unexpected response format from API.');\n          }\n        },\n        error: error => {\n          console.error('API Error:', error);\n          const errorMessage = error?.error?.message || 'An error occurred while processing your request.';\n          this.showAgentError(errorMessage);\n          if (isConversational && this.agentChatPayload.length > 0) {\n            this.agentChatPayload.pop();\n          }\n        }\n      });\n    }\n    sendAgentMessageToAPIWithFiles(message, mode, useCaseIdentifier, isConversational, isUseTemplate, fileContents) {\n      if (isConversational) {\n        this.agentChatPayload.push({\n          content: message,\n          role: 'user'\n        });\n      }\n      const payload = isConversational ? this.agentChatPayload : message;\n      const {\n        levelId\n      } = this.getMetadataFromNavbar();\n      this.agentPlaygroundService.generatePrompt(payload, mode, isConversational, isUseTemplate, this.agentAttachment, useCaseIdentifier, fileContents, levelId).pipe(finalize(() => {\n        this.isProcessingChat = false;\n        this.isAgentPlaygroundLoading = false;\n      }), takeUntil(this.agentPlaygroundDestroy)).subscribe({\n        next: generatedResponse => {\n          if (generatedResponse?.response && generatedResponse?.response?.choices) {\n            const aiResponseText = generatedResponse.response.choices[0].text;\n            this.chatMessages = [...this.chatMessages, {\n              from: 'ai',\n              text: aiResponseText\n            }];\n            if (isConversational) {\n              this.agentChatPayload.push({\n                content: aiResponseText,\n                role: 'assistant'\n              });\n            }\n          } else {\n            console.warn('Unexpected API response format:', generatedResponse);\n            this.showAgentError('Received unexpected response format from API.');\n          }\n        },\n        error: error => {\n          console.error('API Error:', error);\n          const errorMessage = error?.error?.message || 'An error occurred while processing your request.';\n          this.showAgentError(errorMessage);\n          if (isConversational && this.agentChatPayload.length > 0) {\n            this.agentChatPayload.pop();\n          }\n        }\n      });\n    }\n    // Blueprint panel methods\n    mapAgentConfigurationToBlueprint(agentData) {\n      if (!agentData) {\n        console.warn('No agent data provided for blueprint');\n        return;\n      }\n      console.log('🔍 DEBUG: Full agent data received:', agentData);\n      console.log('🔍 DEBUG: Agent type:', this.agentType);\n      console.log('🔍 DEBUG: Agent data keys:', Object.keys(agentData));\n      // Clear existing nodes\n      this.buildAgentNodes = [];\n      this.canvasNodes = [];\n      let nodeCounter = 1;\n      // Map agent configuration to nodes based on agent type\n      if (this.agentType === 'individual') {\n        this.mapIndividualAgentToBlueprint(agentData, nodeCounter);\n      } else if (this.agentType === 'collaborative') {\n        this.mapCollaborativeAgentToBlueprint(agentData, nodeCounter);\n      }\n      console.log('🎯 Blueprint nodes mapped:', {\n        buildAgentNodes: this.buildAgentNodes,\n        canvasNodes: this.canvasNodes,\n        totalNodes: this.buildAgentNodes.length\n      });\n    }\n    mapIndividualAgentToBlueprint(agentData, nodeCounter) {\n      console.log('🔍 Individual agent mapping - checking fields:', {\n        config: agentData.config,\n        configLength: agentData.config?.length,\n        useCaseName: agentData.useCaseName,\n        prompt: agentData.prompt,\n        useCaseDetails: agentData.useCaseDetails\n      });\n      // Clear existing blueprint nodes\n      this.blueprintPromptNodes = [];\n      this.blueprintModelNodes = [];\n      this.blueprintKnowledgeNodes = [];\n      this.blueprintGuardrailNodes = [];\n      // Add prompt node from \"prompt\" field\n      if (agentData.prompt) {\n        this.blueprintPromptNodes.push({\n          id: `prompt-${nodeCounter++}`,\n          name: agentData.prompt,\n          type: 'prompt'\n        });\n        console.log('✅ Added prompt node:', agentData.prompt);\n      }\n      // Process the config array to extract model, knowledge bases, and guardrails\n      if (agentData.config && Array.isArray(agentData.config)) {\n        console.log('🔍 Processing config array with length:', agentData.config.length);\n        agentData.config.forEach((category, categoryIndex) => {\n          console.log(`🔍 Category ${categoryIndex} (ID: ${category.categoryId}):`, category.categoryName);\n          if (category.config && Array.isArray(category.config)) {\n            console.log(`🔍 Category ${categoryIndex} has ${category.config.length} config items`);\n            category.config.forEach((configItem, itemIndex) => {\n              console.log(`🔍 Config item ${categoryIndex}-${itemIndex}:`, {\n                configKey: configItem.configKey,\n                configValue: configItem.configValue,\n                categoryId: configItem.categoryId\n              });\n              // Handle AI Model from categoryId 1\n              if (configItem.categoryId === 1 && configItem.configKey === 'MODEL' && configItem.configValue) {\n                console.log('✅ Adding AI model node from categoryId 1:', configItem.configValue);\n                this.blueprintModelNodes.push({\n                  id: `model-${nodeCounter++}`,\n                  name: `${configItem.configKey}`,\n                  type: 'model'\n                });\n              }\n              // Handle Knowledge Base from categoryId 2\n              if (configItem.categoryId === 2 && configItem.configKey === 'RAG_KNOWLEDGEBASE_NAME' && configItem.configValue) {\n                console.log('✅ Adding knowledge base nodes from categoryId 2:', configItem.configValue);\n                const kbValue = configItem.configValue.toString();\n                const kbIds = kbValue.split(',').map(id => id.trim()).filter(id => id);\n                kbIds.forEach(kbId => {\n                  this.blueprintKnowledgeNodes.push({\n                    id: `knowledge-${nodeCounter++}`,\n                    name: `Knowledge Base: ${kbId}`,\n                    type: 'knowledge'\n                  });\n                });\n              }\n              // Handle Guardrails from categoryId 3 where configValue is true\n              if (configItem.categoryId === 3 && configItem.configValue === 'true') {\n                console.log('✅ Found enabled guardrail from categoryId 3:', {\n                  key: configItem.configKey,\n                  value: configItem.configValue\n                });\n                if (configItem.configKey === 'ENABLE_GUARDRAILS') {\n                  // Only add one general guardrail node if not already added\n                  if (this.blueprintGuardrailNodes.length === 0) {\n                    this.blueprintGuardrailNodes.push({\n                      id: `guardrail-${nodeCounter++}`,\n                      name: 'Guardrails Enabled',\n                      type: 'guardrail'\n                    });\n                  }\n                } else if (configItem.configKey.startsWith('GUARDRAIL_')) {\n                  // Add specific guardrail nodes for enabled guardrails\n                  let guardrailName = configItem.configKey;\n                  if (guardrailName.startsWith('GUARDRAIL_')) {\n                    guardrailName = guardrailName.replace('GUARDRAIL_', '').replace(/_/g, ' ');\n                  }\n                  this.blueprintGuardrailNodes.push({\n                    id: `guardrail-${nodeCounter++}`,\n                    name: `${guardrailName}`,\n                    type: 'guardrail'\n                  });\n                }\n              }\n            });\n          }\n        });\n      }\n      console.log('🎯 Final blueprint nodes:', {\n        promptNodes: this.blueprintPromptNodes,\n        modelNodes: this.blueprintModelNodes,\n        knowledgeNodes: this.blueprintKnowledgeNodes,\n        guardrailNodes: this.blueprintGuardrailNodes\n      });\n      // Calculate completion percentage\n      const totalRequired = 2; // Prompt + Model are required\n      const currentRequired = this.blueprintPromptNodes.length + this.blueprintModelNodes.length;\n      this.blueprintCompletionPercentage = Math.round(currentRequired / totalRequired * 100);\n    }\n    mapCollaborativeAgentToBlueprint(agentData, nodeCounter) {\n      console.log('🚀 SHARED COMPONENT: mapCollaborativeAgentToBlueprint called!');\n      console.log('🔍 DEBUG: Collaborative agent data received:', agentData);\n      console.log('🔍 DEBUG: Collaborative agent data keys:', Object.keys(agentData));\n      console.log('🔍 DEBUG: Agent type in component:', this.agentType);\n      console.log('🔍 DEBUG: Current nodeCounter:', nodeCounter);\n      // Clear existing blueprint nodes\n      this.blueprintPromptNodes = [];\n      this.blueprintModelNodes = [];\n      this.blueprintKnowledgeNodes = [];\n      this.blueprintToolNodes = [];\n      this.blueprintGuardrailNodes = [];\n      console.log('🔍 DEBUG: Cleared all blueprint node arrays');\n      // Add prompt node - handle different prompt structures for collaborative agents\n      const shouldCreatePromptNode = agentData.goal || agentData.role || agentData.description;\n      console.log('🔍 DEBUG: Checking prompt node creation:', {\n        goal: agentData.goal,\n        role: agentData.role,\n        description: agentData.description,\n        shouldCreatePromptNode\n      });\n      if (shouldCreatePromptNode) {\n        let promptNodeName = agentData.goal || agentData.role || agentData.description || 'Collaborative Agent Prompt';\n        // Truncate prompt if too long for display\n        if (promptNodeName.length > 150) {\n          promptNodeName = promptNodeName.substring(0, 150) + '...';\n        }\n        this.blueprintPromptNodes.push({\n          id: `prompt-${nodeCounter++}`,\n          name: promptNodeName,\n          type: 'prompt'\n        });\n        console.log('✅ Added collaborative prompt node:', promptNodeName);\n      }\n      // Add model nodes - handle both old and new API formats like build-agents\n      let modelReferences = [];\n      console.log('🔍 DEBUG: Checking model data:', {\n        hasAgentConfigs: !!agentData.agentConfigs,\n        agentConfigs: agentData.agentConfigs,\n        model: agentData.model,\n        modelName: agentData.modelName,\n        modelDetails: agentData.modelDetails\n      });\n      // New API format: agentConfigs.modelRef (array of model IDs or objects)\n      if (agentData.agentConfigs && agentData.agentConfigs.modelRef) {\n        const modelRefs = Array.isArray(agentData.agentConfigs.modelRef) ? agentData.agentConfigs.modelRef : [agentData.agentConfigs.modelRef];\n        modelReferences = modelRefs.map(ref => {\n          if (typeof ref === 'number' || typeof ref === 'string') {\n            return {\n              modelId: ref\n            };\n          }\n          return ref;\n        });\n      }\n      // Old API format: modelDetails\n      else if (agentData.modelDetails) {\n        modelReferences = [agentData.modelDetails];\n      }\n      // Fallback: check for model or modelName directly\n      else if (agentData.model || agentData.modelName) {\n        modelReferences = [{\n          modelId: agentData.model || agentData.modelName\n        }];\n      }\n      modelReferences.forEach(modelRef => {\n        const modelId = modelRef.modelId || modelRef.id;\n        const modelName = modelRef.model || modelRef.modelDeploymentName || `Model ID: ${modelId}`;\n        this.blueprintModelNodes.push({\n          id: `model-${nodeCounter++}`,\n          name: modelName,\n          type: 'model'\n        });\n        console.log('✅ Added collaborative model node:', modelName);\n      });\n      // Add knowledge base nodes - handle both old and new API formats\n      let knowledgeReferences = [];\n      // New API format: agentConfigs.knowledgeBaseRef (array of KB IDs or objects)\n      if (agentData.agentConfigs && agentData.agentConfigs.knowledgeBaseRef) {\n        const kbRefs = Array.isArray(agentData.agentConfigs.knowledgeBaseRef) ? agentData.agentConfigs.knowledgeBaseRef : [agentData.agentConfigs.knowledgeBaseRef];\n        knowledgeReferences = kbRefs.map(ref => {\n          if (typeof ref === 'number' || typeof ref === 'string') {\n            return {\n              knowledgeBaseId: ref\n            };\n          }\n          return ref;\n        });\n      }\n      // Old API format: knowledgeBase\n      else if (agentData.knowledgeBase && Array.isArray(agentData.knowledgeBase)) {\n        knowledgeReferences = agentData.knowledgeBase;\n      }\n      knowledgeReferences.forEach(kbRef => {\n        const kbId = kbRef.knowledgeBaseId || kbRef.id;\n        const collectionName = kbRef.indexCollectionName || kbRef.name;\n        const kbName = collectionName || `Knowledge Base ID: ${kbId}`;\n        this.blueprintKnowledgeNodes.push({\n          id: `knowledge-${nodeCounter++}`,\n          name: kbName,\n          type: 'knowledge'\n        });\n        console.log('✅ Added collaborative knowledge node:', kbName);\n      });\n      // Add tool nodes - handle both old and new API formats like build-agents\n      let toolReferences = [];\n      let userToolReferences = [];\n      console.log('🔍 DEBUG: Checking tool data:', {\n        hasAgentConfigs: !!agentData.agentConfigs,\n        agentConfigsContent: agentData.agentConfigs,\n        hasTools: agentData.tools,\n        toolsContent: agentData.tools,\n        hasUserTools: agentData.userTools,\n        userToolsContent: agentData.userTools\n      });\n      // New API format: agentConfigs.toolRef and agentConfigs.userToolRef\n      if (agentData.agentConfigs) {\n        if (agentData.agentConfigs.toolRef) {\n          const toolRefs = Array.isArray(agentData.agentConfigs.toolRef) ? agentData.agentConfigs.toolRef : [agentData.agentConfigs.toolRef];\n          toolReferences = toolRefs.map(ref => {\n            if (typeof ref === 'number' || typeof ref === 'string') {\n              return {\n                toolId: ref\n              };\n            }\n            return ref;\n          });\n        }\n        if (agentData.agentConfigs.userToolRef) {\n          const userToolRefs = Array.isArray(agentData.agentConfigs.userToolRef) ? agentData.agentConfigs.userToolRef : [agentData.agentConfigs.userToolRef];\n          userToolReferences = userToolRefs.map(ref => {\n            if (typeof ref === 'number' || typeof ref === 'string') {\n              return {\n                toolId: ref\n              };\n            }\n            return ref;\n          });\n        }\n      }\n      // Old API format: tools and userTools\n      else {\n        if (agentData.tools && Array.isArray(agentData.tools)) {\n          toolReferences = agentData.tools;\n        }\n        if (agentData.userTools && Array.isArray(agentData.userTools)) {\n          userToolReferences = agentData.userTools;\n        }\n      }\n      // Process built-in tools\n      toolReferences.forEach(tool => {\n        const toolId = tool.toolId || tool.id;\n        const toolName = tool.toolName || tool.name || `Tool ID: ${toolId}`;\n        this.blueprintToolNodes.push({\n          id: `tool-${nodeCounter++}`,\n          name: toolName,\n          type: 'tool'\n        });\n        console.log('✅ Added collaborative builtin tool node:', toolName);\n      });\n      // Process user tools\n      userToolReferences.forEach(userTool => {\n        const userToolId = userTool.toolId || userTool.id;\n        const userToolName = userTool.toolName || userTool.name || `User Tool ID: ${userToolId}`;\n        this.blueprintToolNodes.push({\n          id: `tool-${nodeCounter++}`,\n          name: userToolName,\n          type: 'tool'\n        });\n        console.log('✅ Added collaborative user tool node:', userToolName);\n      });\n      console.log('🎯 Final collaborative blueprint nodes:', {\n        promptNodes: this.blueprintPromptNodes,\n        modelNodes: this.blueprintModelNodes,\n        knowledgeNodes: this.blueprintKnowledgeNodes,\n        toolNodes: this.blueprintToolNodes,\n        guardrailNodes: this.blueprintGuardrailNodes\n      });\n      // Debug: Check blueprint node arrays lengths\n      console.log('📊 Blueprint node counts:', {\n        promptCount: this.blueprintPromptNodes.length,\n        modelCount: this.blueprintModelNodes.length,\n        knowledgeCount: this.blueprintKnowledgeNodes.length,\n        toolCount: this.blueprintToolNodes.length,\n        guardrailCount: this.blueprintGuardrailNodes.length\n      });\n      // Debug: Check if tools zone will be visible\n      console.log('🔧 Tools zone debug:', {\n        agentType: this.agentType,\n        isCollaborative: this.agentType === 'collaborative',\n        hasToolNodes: this.blueprintToolNodes.length > 0,\n        toolNodeNames: this.blueprintToolNodes.map(t => t.name)\n      });\n      // Calculate completion percentage\n      const totalRequired = 2; // Prompt + Model are required\n      const currentRequired = this.blueprintPromptNodes.length + this.blueprintModelNodes.length;\n      this.blueprintCompletionPercentage = Math.round(currentRequired / totalRequired * 100);\n    }\n    static ɵfac = function AgentExecutionComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AgentExecutionComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AgentServiceService), i0.ɵɵdirectiveInject(i3.AgentPlaygroundService), i0.ɵɵdirectiveInject(i4.TokenStorageService), i0.ɵɵdirectiveInject(i5.LoaderService), i0.ɵɵdirectiveInject(i6.FormBuilder), i0.ɵɵdirectiveInject(i7.ToolExecutionService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AgentExecutionComponent,\n      selectors: [[\"app-agent-execution\"]],\n      viewQuery: function AgentExecutionComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(AgentExecutionPlaygroundComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.playgroundComp = _t.first);\n        }\n      },\n      decls: 24,\n      vars: 12,\n      consts: [[1, \"agent-execution-container\"], [1, \"top-nav-bar\"], [1, \"nav-left\"], [\"type\", \"button\", 1, \"back-button\", 3, \"click\"], [\"iconName\", \"ArrowLeft\", \"iconSize\", \"16\", \"iconColor\", \"#374151\"], [1, \"agent-name\"], [1, \"main-content\"], [1, \"left-panel\"], [1, \"panel-header\"], [\"type\", \"button\", 1, \"collapse-btn\", 3, \"click\"], [\"iconSize\", \"16\", \"iconColor\", \"#6B7280\", 3, \"iconName\"], [\"class\", \"history-btn\", \"type\", \"button\", \"disabled\", \"\", 4, \"ngIf\"], [\"class\", \"panel-content\", 4, \"ngIf\"], [1, \"right-panel\"], [1, \"tabs-container\"], [1, \"tab-btn\", 3, \"click\"], [1, \"panel-content\"], [\"class\", \"blueprint-content\", 4, \"ngIf\"], [\"class\", \"output-content\", 4, \"ngIf\"], [\"type\", \"button\", \"disabled\", \"\", 1, \"history-btn\"], [3, \"promptChange\", \"messageSent\", \"conversationalToggle\", \"templateToggle\", \"filesSelected\", \"approvalRequested\", \"messages\", \"isLoading\", \"agentType\", \"showChatInteractionToggles\", \"showAiPrincipleToggle\", \"showApprovalButton\", \"showDropdown\", \"showAgentNameInput\", \"showFileUploadButton\", \"showStatusOnly\", \"displayedAgentName\", \"agentNamePlaceholder\"], [1, \"blueprint-content\"], [1, \"blueprint-header\"], [1, \"custom-blueprint-container\"], [\"viewBox\", \"0 0 100 100\", \"preserveAspectRatio\", \"none\", 1, \"connection-lines\"], [\"x1\", \"25\", \"y1\", \"25\", \"x2\", \"50\", \"y2\", \"50\", \"stroke\", \"#BBBEC5\", \"stroke-width\", \"0.3\"], [\"x1\", \"75\", \"y1\", \"25\", \"x2\", \"50\", \"y2\", \"50\", \"stroke\", \"#BBBEC5\", \"stroke-width\", \"0.3\"], [\"x1\", \"25\", \"y1\", \"75\", \"x2\", \"50\", \"y2\", \"50\", \"stroke\", \"#BBBEC5\", \"stroke-width\", \"0.3\"], [\"x1\", \"75\", \"y1\", \"75\", \"x2\", \"50\", \"y2\", \"50\", \"stroke\", \"#BBBEC5\", \"stroke-width\", \"0.3\"], [1, \"central-progress\"], [1, \"progress-ring\"], [\"width\", \"120\", \"height\", \"120\", 1, \"progress-circle\"], [\"id\", \"progressGradient\", \"x1\", \"0%\", \"y1\", \"0%\", \"x2\", \"100%\", \"y2\", \"100%\"], [\"offset\", \"33.91%\", 2, \"stop-color\", \"#0084ff\"], [\"offset\", \"100%\", 2, \"stop-color\", \"#03bdd4\"], [\"cx\", \"60\", \"cy\", \"60\", \"r\", \"50\", \"fill\", \"none\", \"stroke\", \"#e5e7eb\", \"stroke-width\", \"8\", 1, \"progress-background\"], [\"cx\", \"60\", \"cy\", \"60\", \"r\", \"50\", \"fill\", \"none\", \"stroke\", \"url(#progressGradient)\", \"stroke-width\", \"8\", \"stroke-linecap\", \"round\", \"transform\", \"rotate(180 60 60)\", 1, \"progress-bar\"], [1, \"progress-content\"], [1, \"progress-percentage\"], [1, \"progress-label\"], [\"id\", \"parent-box\"], [1, \"blueprint-zone\", \"north-zone\", \"prompts-zone\"], [1, \"zone-header\", 3, \"click\"], [1, \"header-content\"], [1, \"header-icon\"], [\"width\", \"45\", \"height\", \"44\", \"viewBox\", \"0 0 45 44\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"x\", \"0.679688\", \"width\", \"44\", \"height\", \"44\", \"rx\", \"8\", \"fill\", \"#5082EF\"], [\"d\", \"M20.6166 25.5C20.5273 25.1539 20.347 24.8381 20.0942 24.5854C19.8415 24.3327 19.5257 24.1523 19.1796 24.063L13.0446 22.481C12.94 22.4513 12.8478 22.3883 12.7822 22.3014C12.7166 22.2146 12.6812 22.1088 12.6812 22C12.6812 21.8912 12.7166 21.7854 12.7822 21.6986C12.8478 21.6118 12.94 21.5487 13.0446 21.519L19.1796 19.936C19.5256 19.8468 19.8413 19.6666 20.094 19.414C20.3467 19.1615 20.5272 18.8459 20.6166 18.5L22.1986 12.365C22.228 12.2599 22.291 12.1673 22.3779 12.1014C22.4649 12.0355 22.571 11.9998 22.6801 11.9998C22.7892 11.9998 22.8954 12.0355 22.9823 12.1014C23.0692 12.1673 23.1322 12.2599 23.1616 12.365L24.7426 18.5C24.8319 18.8461 25.0123 19.1619 25.265 19.4146C25.5177 19.6673 25.8336 19.8477 26.1796 19.937L32.3146 21.518C32.4201 21.5471 32.5132 21.61 32.5795 21.6971C32.6458 21.7841 32.6817 21.8906 32.6817 22C32.6817 22.1094 32.6458 22.2159 32.5795 22.3029C32.5132 22.39 32.4201 22.4529 32.3146 22.482L26.1796 24.063C25.8336 24.1523 25.5177 24.3327 25.265 24.5854C25.0123 24.8381 24.8319 25.1539 24.7426 25.5L23.1606 31.635C23.1312 31.7401 23.0682 31.8327 22.9813 31.8986C22.8944 31.9646 22.7882 32.0003 22.6791 32.0003C22.57 32.0003 22.4639 31.9646 22.3769 31.8986C22.29 31.8327 22.227 31.7401 22.1976 31.635L20.6166 25.5Z\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"zone-title\"], [1, \"header-actions\"], [1, \"required-badge\"], [\"type\", \"button\", 1, \"accordion-toggle\"], [\"width\", \"16\", \"height\", \"16\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\"], [\"d\", \"M6 9L12 15L18 9\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"class\", \"zone-content\", 4, \"ngIf\"], [1, \"blueprint-zone\", \"west-zone\", \"knowledge-zone\"], [\"x\", \"0.679688\", \"width\", \"44\", \"height\", \"44\", \"rx\", \"8\", \"fill\", \"#308666\"], [\"d\", \"M22.6797 17V31\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M26.6797 22H28.6797\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M26.6797 18H28.6797\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M13.6797 28C13.4145 28 13.1601 27.8946 12.9726 27.7071C12.785 27.5196 12.6797 27.2652 12.6797 27V14C12.6797 13.7348 12.785 13.4804 12.9726 13.2929C13.1601 13.1054 13.4145 13 13.6797 13H18.6797C19.7406 13 20.758 13.4214 21.5081 14.1716C22.2583 14.9217 22.6797 15.9391 22.6797 17C22.6797 15.9391 23.1011 14.9217 23.8513 14.1716C24.6014 13.4214 25.6188 13 26.6797 13H31.6797C31.9449 13 32.1993 13.1054 32.3868 13.2929C32.5743 13.4804 32.6797 13.7348 32.6797 14V27C32.6797 27.2652 32.5743 27.5196 32.3868 27.7071C32.1993 27.8946 31.9449 28 31.6797 28H25.6797C24.884 28 24.121 28.3161 23.5584 28.8787C22.9958 29.4413 22.6797 30.2044 22.6797 31C22.6797 30.2044 22.3636 29.4413 21.801 28.8787C21.2384 28.3161 20.4753 28 19.6797 28H13.6797Z\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M16.6797 22H18.6797\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M16.6797 18H18.6797\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"optional-badge\"], [1, \"blueprint-zone\", \"east-zone\", \"models-zone\"], [\"x\", \"0.679688\", \"width\", \"44\", \"height\", \"44\", \"rx\", \"8\", \"fill\", \"#997BCF\"], [\"d\", \"M31.6797 18C31.6793 17.6493 31.5867 17.3048 31.4112 17.0012C31.2357 16.6975 30.9834 16.4454 30.6797 16.27L23.6797 12.27C23.3756 12.0945 23.0308 12.0021 22.6797 12.0021C22.3286 12.0021 21.9837 12.0945 21.6797 12.27L14.6797 16.27C14.376 16.4454 14.1237 16.6975 13.9481 17.0012C13.7726 17.3048 13.68 17.6493 13.6797 18V26C13.68 26.3508 13.7726 26.6952 13.9481 26.9989C14.1237 27.3025 14.376 27.5547 14.6797 27.73L21.6797 31.73C21.9837 31.9056 22.3286 31.998 22.6797 31.998C23.0308 31.998 23.3756 31.9056 23.6797 31.73L30.6797 27.73C30.9834 27.5547 31.2357 27.3025 31.4112 26.9989C31.5867 26.6952 31.6793 26.3508 31.6797 26V18Z\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M13.9795 17L22.6795 22L31.3795 17\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M22.6797 32V22\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"class\", \"blueprint-zone south-zone tools-zone\", 3, \"has-nodes\", 4, \"ngIf\"], [\"class\", \"blueprint-zone south-zone guardrails-zone\", 3, \"has-nodes\", 4, \"ngIf\"], [1, \"zone-content\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [1, \"nodes-list\"], [\"class\", \"kanban-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"empty-state\"], [1, \"kanban-card\"], [1, \"card-title\"], [1, \"blueprint-zone\", \"south-zone\", \"tools-zone\"], [\"width\", \"45\", \"height\", \"44\", \"viewBox\", \"0 0 45 44\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\", 4, \"ngIf\"], [\"x\", \"0.679688\", \"width\", \"44\", \"height\", \"44\", \"rx\", \"8\", \"fill\", \"#D97706\"], [\"d\", \"M25.3795 16.3C25.1963 16.4869 25.0937 16.7382 25.0937 17C25.0937 17.2617 25.1963 17.5131 25.3795 17.7L26.9795 19.3C27.1665 19.4832 27.4178 19.5858 27.6795 19.5858C27.9413 19.5858 28.1926 19.4832 28.3795 19.3L32.1495 15.53C32.6524 16.6412 32.8046 17.8792 32.586 19.0791C32.3674 20.279 31.7883 21.3838 30.9258 22.2463C30.0634 23.1087 28.9586 23.6878 27.7587 23.9064C26.5588 24.1251 25.3207 23.9728 24.2095 23.47L17.2995 30.38C16.9017 30.7778 16.3622 31.0013 15.7995 31.0013C15.2369 31.0013 14.6974 30.7778 14.2995 30.38C13.9017 29.9822 13.6782 29.4426 13.6782 28.88C13.6782 28.3174 13.9017 27.7778 14.2995 27.38L21.2095 20.47C20.7067 19.3588 20.5545 18.1207 20.7731 16.9208C20.9917 15.7209 21.5708 14.6161 22.4333 13.7537C23.2957 12.8913 24.4005 12.3121 25.6004 12.0935C26.8003 11.8749 28.0384 12.0271 29.1495 12.53L25.3895 16.29L25.3795 16.3Z\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"blueprint-zone\", \"south-zone\", \"guardrails-zone\"], [\"x\", \"0.679688\", \"width\", \"44\", \"height\", \"44\", \"rx\", \"8\", \"fill\", \"#DC2626\"], [\"d\", \"M30.6797 23C30.6797 28 27.1797 30.5 23.0197 31.95C22.8019 32.0238 22.5652 32.0202 22.3497 31.94C18.1797 30.5 14.6797 28 14.6797 23V16C14.6797 15.7347 14.785 15.4804 14.9726 15.2929C15.1601 15.1053 15.4145 15 15.6797 15C17.6797 15 20.1797 13.8 21.9197 12.28C22.1315 12.099 22.401 11.9995 22.6797 11.9995C22.9583 11.9995 23.2278 12.099 23.4397 12.28C25.1897 13.81 27.6797 15 29.6797 15C29.9449 15 30.1993 15.1053 30.3868 15.2929C30.5743 15.4804 30.6797 15.7347 30.6797 16V23Z\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"output-content\"], [1, \"execution-history-container\"], [\"class\", \"no-output-message\", 4, \"ngIf\"], [\"class\", \"execution-history-list\", 4, \"ngIf\"], [1, \"no-output-message\"], [1, \"execution-history-list\"], [\"class\", \"execution-item\", 3, \"success\", \"failed\", 4, \"ngFor\", \"ngForOf\"], [1, \"execution-item\"], [1, \"execution-header\"], [1, \"execution-status\"], [1, \"execution-details\"], [1, \"user-message\"], [1, \"execution-timestamp\"], [\"class\", \"execution-response\", 4, \"ngIf\"], [\"class\", \"execution-error\", 4, \"ngIf\"], [1, \"execution-response\"], [1, \"response-section\"], [1, \"response-text\"], [\"class\", \"task-output\", 4, \"ngFor\", \"ngForOf\"], [1, \"task-output\"], [\"class\", \"task-description\", 4, \"ngIf\"], [\"class\", \"task-expected\", 4, \"ngIf\"], [1, \"task-content\"], [1, \"task-header\"], [1, \"task-description\"], [1, \"task-expected\"], [1, \"execution-error\"], [1, \"error-message\"]],\n      template: function AgentExecutionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function AgentExecutionComponent_Template_button_click_3_listener() {\n            return ctx.navigateBack();\n          });\n          i0.ɵɵelement(4, \"ava-icon\", 4);\n          i0.ɵɵelementStart(5, \"span\", 5);\n          i0.ɵɵtext(6);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"div\", 7)(9, \"div\", 8)(10, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function AgentExecutionComponent_Template_button_click_10_listener() {\n            return ctx.toggleLeftPanel();\n          });\n          i0.ɵɵelement(11, \"ava-icon\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(12, AgentExecutionComponent_button_12_Template, 2, 0, \"button\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(13, AgentExecutionComponent_div_13_Template, 2, 12, \"div\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"div\", 13)(15, \"div\", 8)(16, \"div\", 14)(17, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function AgentExecutionComponent_Template_button_click_17_listener() {\n            return ctx.setActiveRightTab(\"blueprint\");\n          });\n          i0.ɵɵtext(18, \" Blueprint \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function AgentExecutionComponent_Template_button_click_19_listener() {\n            return ctx.setActiveRightTab(\"output\");\n          });\n          i0.ɵɵtext(20, \" Agent Output \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"div\", 16);\n          i0.ɵɵtemplate(22, AgentExecutionComponent_div_22_Template, 82, 22, \"div\", 17)(23, AgentExecutionComponent_div_23_Template, 6, 2, \"div\", 18);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.agentName);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"collapsed\", ctx.isLeftPanelCollapsed);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"iconName\", ctx.isLeftPanelCollapsed ? \"ChevronRight\" : \"PanelLeft\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLeftPanelCollapsed);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLeftPanelCollapsed);\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"active\", ctx.activeRightTab === \"blueprint\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"active\", ctx.activeRightTab === \"output\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.activeRightTab === \"blueprint\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.activeRightTab === \"output\");\n        }\n      },\n      dependencies: [CommonModule, i8.NgForOf, i8.NgIf, i8.DatePipe, FormsModule, AgentExecutionPlaygroundComponent, IconComponent],\n      styles: [\".agent-execution-container[_ngcontent-%COMP%] {\\n  height: calc(100vh - 84px);\\n  display: flex;\\n  flex-direction: column;\\n  color: var(--color-text-primary);\\n  overflow: hidden;\\n}\\n\\n.top-nav-bar[_ngcontent-%COMP%] {\\n  padding: 16px 24px;\\n  padding-bottom: 0px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  min-height: 64px;\\n}\\n\\n.nav-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.back-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 8px 12px;\\n  border-radius: 6px;\\n  transition: background-color 0.2s ease;\\n}\\n.back-button[_ngcontent-%COMP%]:hover {\\n  background: var(--color-background-tertiary);\\n}\\n.back-button[_ngcontent-%COMP%]   .agent-name[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #000000;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  gap: 16px;\\n  padding: 16px;\\n  padding-top: 0px;\\n  height: calc(100vh - 96px);\\n  overflow: hidden;\\n}\\n\\n.left-panel[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 400px;\\n  max-width: 600px;\\n  background: white;\\n  border-radius: 12px;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n  transition: all 0.3s ease;\\n}\\n.left-panel.collapsed[_ngcontent-%COMP%] {\\n  flex: 0 0 48px;\\n  min-width: 48px;\\n  max-width: 48px;\\n}\\n\\n.right-panel[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 400px;\\n  background: white;\\n  border-radius: 12px;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n\\n.panel-header[_ngcontent-%COMP%] {\\n  padding: 16px 20px;\\n  background: #e9effd;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  max-height: 45px;\\n  border-radius: 12px 12px 0 0;\\n}\\n\\n.collapse-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 6px;\\n  border-radius: 4px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: background-color 0.2s ease;\\n  color: #1a46a7;\\n}\\n.collapse-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--color-background-quaternary);\\n}\\n\\n.history-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 8px 12px;\\n  border-radius: 6px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #1a46a7;\\n  transition: all 0.2s ease;\\n}\\n.history-btn[_ngcontent-%COMP%]:disabled {\\n  cursor: not-allowed;\\n}\\n.history-btn[_ngcontent-%COMP%]:hover {\\n  color: var(--color-text-primary);\\n  background: var(--color-background-quaternary);\\n}\\n\\n.tabs-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  padding: 4px;\\n}\\n\\n.tab-btn[_ngcontent-%COMP%] {\\n  padding: 8px 20px;\\n  border: none;\\n  background: transparent;\\n  color: var(--text-secondary);\\n  border-radius: 10px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  white-space: nowrap;\\n  color: #1a46a7;\\n}\\n.tab-btn.active[_ngcontent-%COMP%] {\\n  background: white;\\n  color: var(--nav-pill-selected-color);\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n  font-weight: 600;\\n}\\n\\n.panel-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.mock-content[_ngcontent-%COMP%] {\\n  padding: 24px;\\n  height: 100%;\\n  overflow-y: auto;\\n}\\n.mock-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: var(--color-text-primary);\\n}\\n.mock-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 24px 0;\\n  color: var(--color-text-secondary);\\n  font-size: 14px;\\n  line-height: 1.5;\\n}\\n\\n.blueprint-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n}\\n\\n.blueprint-header[_ngcontent-%COMP%] {\\n  padding: 10px;\\n}\\n.blueprint-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: black;\\n  margin: 0 0 5px 0;\\n  font-size: 18px;\\n  font-weight: 600;\\n  text-align: center;\\n}\\n.blueprint-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--color-text-secondary);\\n  margin: 0;\\n  font-size: 14px;\\n}\\n\\n.blueprint-canvas-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow: auto;\\n  padding: 10px;\\n  background: var(--color-background-primary);\\n}\\n\\n.custom-blueprint-container[_ngcontent-%COMP%] {\\n  height: 100%;\\n  width: 100%;\\n  min-height: 500px;\\n  background-color: #f8f9fa;\\n  background-image: radial-gradient(circle, #d1d5db 1px, transparent 1px);\\n  background-size: 20px 20px;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  border: 1px solid #d1d3d8;\\n  position: relative;\\n  padding: 5%;\\n  border-radius: 10px;\\n}\\n\\n.central-progress[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  z-index: 10;\\n  background-color: white;\\n  border-radius: 50%;\\n  padding: 8px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-ring[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-circle[_ngcontent-%COMP%] {\\n  transform: rotate(-90deg);\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-background[_ngcontent-%COMP%] {\\n  opacity: 0.15;\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  transition: stroke-dashoffset 0.3s ease;\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-content[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  text-align: center;\\n  color: #374151;\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-percentage[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  line-height: 1;\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-label[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #6b7280;\\n  margin-top: 2px;\\n}\\n\\n#parent-box[_ngcontent-%COMP%] {\\n  height: 100%;\\n  width: 100%;\\n  gap: 2rem;\\n  position: relative;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.blueprint-zones-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 80%;\\n  height: 80%;\\n  max-width: 800px;\\n  max-height: 600px;\\n}\\n\\n#box1[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: space-between;\\n  height: 300px;\\n  position: relative;\\n  border: 1px solid #bbbec5;\\n  position: fixed;\\n  top: 32%;\\n  left: 40%;\\n  border-left: none;\\n}\\n\\n#box2[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: space-between;\\n  height: 300px;\\n  position: relative;\\n  border: 1px solid #bbbec5;\\n  position: fixed;\\n  top: 32%;\\n  left: 60%;\\n  border-right: none;\\n}\\n\\n.blueprint-zone[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  padding: 16px;\\n  transition: all 0.3s ease;\\n  position: absolute;\\n  width: 280px;\\n  z-index: 5;\\n}\\n.blueprint-zone.has-nodes[_ngcontent-%COMP%] {\\n  border-style: solid;\\n}\\n.blueprint-zone.collapsed[_ngcontent-%COMP%] {\\n  min-height: 40px;\\n}\\n.blueprint-zone.collapsed[_ngcontent-%COMP%]   .zone-header[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n\\n.north-zone[_ngcontent-%COMP%] {\\n  background-color: #ecf0fa;\\n  color: #005eb5;\\n  border: 2px solid #9ab7f6;\\n  top: 0;\\n  left: 0;\\n  overflow: hidden;\\n}\\n\\n.east-zone[_ngcontent-%COMP%] {\\n  background-color: #f2ebfd;\\n  border: 2px solid #d6c2f9;\\n  color: #d6c2f9;\\n  top: 0;\\n  right: 0;\\n  overflow: hidden;\\n}\\n\\n.south-zone[_ngcontent-%COMP%] {\\n  background-color: #fbf6f7;\\n  border: 2px solid #fecaca;\\n  color: #dc2626 !important;\\n  bottom: 0;\\n  right: 0;\\n  overflow: hidden;\\n}\\n.south-zone.has-nodes[_ngcontent-%COMP%] {\\n  height: auto;\\n  min-height: 100px;\\n  max-height: 200px;\\n}\\n.south-zone.has-nodes[_ngcontent-%COMP%]:hover {\\n  overflow-y: auto;\\n  scrollbar-width: none !important;\\n}\\n\\n.west-zone[_ngcontent-%COMP%] {\\n  background-color: #ecf8f4;\\n  border: 2px solid #a9e1cc;\\n  color: #25684f;\\n  bottom: 0;\\n  left: 0;\\n  overflow: hidden;\\n}\\n.west-zone.has-nodes[_ngcontent-%COMP%] {\\n  height: auto;\\n  min-height: 100px;\\n  max-height: 200px;\\n}\\n.west-zone.has-nodes[_ngcontent-%COMP%]:hover {\\n  overflow-y: auto;\\n  scrollbar-width: none !important;\\n}\\n\\n.connection-lines[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  pointer-events: none;\\n  z-index: 1;\\n}\\n.connection-lines[_ngcontent-%COMP%]   line[_ngcontent-%COMP%] {\\n  opacity: 0.7;\\n  transition: opacity 0.3s ease;\\n}\\n.connection-lines[_ngcontent-%COMP%]   line[_ngcontent-%COMP%]:hover {\\n  opacity: 1;\\n}\\n\\n.central-progress[_ngcontent-%COMP%] {\\n  z-index: 10;\\n}\\n\\n.blueprint-zone[_ngcontent-%COMP%] {\\n  z-index: 5;\\n}\\n\\n.zone-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin-bottom: 16px;\\n  padding-bottom: 12px;\\n  cursor: pointer;\\n  -webkit-user-select: none;\\n          user-select: none;\\n  transition: all 0.2s ease;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 8px;\\n  background-color: rgba(59, 130, 246, 0.1);\\n  color: #3b82f6;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .zone-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #374151;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .accordion-toggle[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 4px;\\n  border-radius: 4px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.2s ease;\\n  color: #6b7280;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .accordion-toggle[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 0, 0, 0.05);\\n  color: #374151;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .accordion-toggle[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  transition: transform 0.3s ease;\\n}\\n\\n.required-badge[_ngcontent-%COMP%] {\\n  background-color: var(--status-error);\\n  color: white;\\n  font-size: 10px;\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n  font-weight: 500;\\n}\\n\\n.optional-badge[_ngcontent-%COMP%] {\\n  background-color: var(--status-warning);\\n  color: white;\\n  font-size: 10px;\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n  font-weight: 500;\\n}\\n\\n.zone-content[_ngcontent-%COMP%] {\\n  min-height: 60px;\\n  flex: 1;\\n  transition: all 0.3s ease-in-out;\\n  opacity: 1;\\n  overflow: hidden;\\n}\\n\\n.empty-state[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: var(--text-secondary);\\n  font-size: 12px;\\n  padding: 20px 0;\\n  font-style: italic;\\n}\\n\\n.nodes-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n}\\n\\n.kanban-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  padding: 12px 16px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n  transition: all 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n}\\n.kanban-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\\n  transform: translateY(-1px);\\n}\\n.kanban-card[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #374151;\\n  flex: 1;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n.blueprint-placeholder[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n\\n.blueprint-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 16px;\\n  background: var(--color-background-primary);\\n  border: 1px solid var(--color-border-primary);\\n  border-radius: 8px;\\n  transition: all 0.2s ease;\\n}\\n.blueprint-item[_ngcontent-%COMP%]:hover {\\n  border-color: var(--color-border-secondary);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.blueprint-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  width: 32px;\\n  height: 32px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.blueprint-text[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: var(--color-text-primary);\\n}\\n\\n.output-placeholder[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 24px;\\n}\\n\\n.output-section[_ngcontent-%COMP%] {\\n  background: var(--color-background-primary);\\n  border: 1px solid var(--color-border-primary);\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n.output-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 16px 16px 8px 16px;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: var(--color-text-primary);\\n}\\n.output-section[_ngcontent-%COMP%]   .output-meta[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 0 16px 16px 16px;\\n  font-size: 12px;\\n  color: var(--color-text-tertiary);\\n}\\n\\n.output-preview[_ngcontent-%COMP%] {\\n  border-top: 1px solid var(--color-border-primary);\\n}\\n.output-preview[_ngcontent-%COMP%]   .code-block[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  background: var(--color-background-secondary);\\n  font-family: \\\"Monaco\\\", \\\"Menlo\\\", \\\"Ubuntu Mono\\\", monospace;\\n  font-size: 12px;\\n  line-height: 1.6;\\n  color: var(--color-text-primary);\\n  border-bottom: 1px solid var(--color-border-primary);\\n}\\n.output-preview[_ngcontent-%COMP%]   .preview-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 12px 16px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: var(--color-brand-primary);\\n  transition: all 0.2s ease;\\n  width: 100%;\\n  text-align: left;\\n}\\n.output-preview[_ngcontent-%COMP%]   .preview-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--color-background-tertiary);\\n}\\n\\n@media (max-width: 768px) {\\n  .execution-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .playground-column[_ngcontent-%COMP%], \\n   .output-column[_ngcontent-%COMP%] {\\n    flex: none;\\n    height: 50%;\\n  }\\n  .playground-column[_ngcontent-%COMP%] {\\n    border-right: none;\\n    border-bottom: 1px solid var(--color-border-primary);\\n  }\\n}\\n.row[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  margin: 0;\\n}\\n\\n.col-7[_ngcontent-%COMP%] {\\n  flex: 0 0 58.333333%;\\n  max-width: 58.333333%;\\n}\\n\\n.col-5[_ngcontent-%COMP%] {\\n  flex: 0 0 41.666667%;\\n  max-width: 41.666667%;\\n}\\n\\n*[_ngcontent-%COMP%] {\\n  box-sizing: border-box;\\n}\\n\\n[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n  height: 6px;\\n}\\n\\n[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: var(--color-background-secondary);\\n}\\n\\n[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: var(--color-border-primary);\\n  border-radius: 3px;\\n}\\n\\n[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: var(--color-border-secondary);\\n}\\n\\n.activity-placeholder[_ngcontent-%COMP%], \\n.output-placeholder[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n.activity-placeholder[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], \\n.output-placeholder[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: var(--color-text-primary);\\n}\\n.activity-placeholder[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], \\n.output-placeholder[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: var(--color-text-secondary);\\n  font-size: 14px;\\n}\\n\\n.activity-list[_ngcontent-%COMP%], \\n.output-list[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  border: 1px solid var(--color-border-primary);\\n  border-radius: 8px;\\n  padding: 16px;\\n  background: var(--color-background-secondary);\\n}\\n\\n.activity-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n  padding: 12px 0;\\n  border-bottom: 1px solid var(--color-border-primary);\\n}\\n.activity-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.activity-item[_ngcontent-%COMP%]   .timestamp[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: var(--color-text-tertiary);\\n  font-weight: 500;\\n}\\n.activity-item[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--color-text-primary);\\n}\\n\\n.output-item[_ngcontent-%COMP%] {\\n  padding: 12px 0;\\n  border-bottom: 1px solid var(--color-border-primary);\\n}\\n.output-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.output-item[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--color-text-primary);\\n  line-height: 1.5;\\n}\\n\\n.individual-output[_ngcontent-%COMP%], \\n.collaborative-output[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n.individual-output[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], \\n.collaborative-output[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: var(--color-text-primary);\\n}\\n.individual-output[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], \\n.collaborative-output[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--color-text-secondary);\\n  font-size: 14px;\\n  margin: 0;\\n}\\n\\n.output-content[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.output-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: var(--color-text-primary);\\n}\\n\\n.execution-history-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.no-output-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  height: 100%;\\n}\\n.no-output-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--color-text-secondary);\\n  font-size: 14px;\\n  text-align: center;\\n}\\n\\n.execution-history-list[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding-right: 8px;\\n}\\n.execution-history-list[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.execution-history-list[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: var(--color-background-secondary);\\n  border-radius: 3px;\\n}\\n.execution-history-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: var(--color-border-primary);\\n  border-radius: 3px;\\n}\\n.execution-history-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: var(--color-border-secondary);\\n}\\n\\n.execution-item[_ngcontent-%COMP%] {\\n  background: var(--color-background-secondary);\\n  border: 1px solid var(--color-border-primary);\\n  border-radius: 8px;\\n  margin-bottom: 16px;\\n  overflow: hidden;\\n  transition: all 0.2s ease;\\n}\\n.execution-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.execution-item.success[_ngcontent-%COMP%] {\\n  border-left: 4px solid #10b981;\\n}\\n.execution-item.failed[_ngcontent-%COMP%] {\\n  border-left: 4px solid #ef4444;\\n}\\n\\n.execution-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 12px 16px;\\n  background: var(--color-background-primary);\\n  border-bottom: 1px solid var(--color-border-primary);\\n}\\n\\n.agent-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 14px;\\n  color: var(--color-text-primary);\\n}\\n\\n.execution-status[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 600;\\n  padding: 4px 8px;\\n  border-radius: 4px;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.execution-status.success[_ngcontent-%COMP%] {\\n  background: rgba(16, 185, 129, 0.1);\\n  color: #10b981;\\n}\\n.execution-status.failed[_ngcontent-%COMP%] {\\n  background: rgba(239, 68, 68, 0.1);\\n  color: #ef4444;\\n}\\n\\n.execution-details[_ngcontent-%COMP%] {\\n  padding: 12px 16px;\\n  border-bottom: 1px solid var(--color-border-primary);\\n}\\n\\n.user-message[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--color-text-primary);\\n  margin-bottom: 8px;\\n  line-height: 1.4;\\n}\\n.user-message[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: var(--color-text-primary);\\n}\\n\\n.execution-timestamp[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: var(--color-text-tertiary);\\n}\\n\\n.execution-response[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n\\n.response-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 12px 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: var(--color-text-primary);\\n}\\n\\n.response-text[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  line-height: 1.6;\\n  color: var(--color-text-primary);\\n  white-space: pre-wrap;\\n  word-wrap: break-word;\\n  background: var(--color-background-primary);\\n  padding: 12px;\\n  border-radius: 6px;\\n  border: 1px solid var(--color-border-secondary);\\n  max-height: 300px;\\n  overflow-y: auto;\\n}\\n\\n.task-output[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.task-output[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n\\n.task-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 12px 0 12px 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: var(--color-text-primary);\\n}\\n\\n.task-description[_ngcontent-%COMP%], \\n.task-expected[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n  font-size: 14px;\\n  color: var(--color-text-secondary);\\n}\\n.task-description[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%], \\n.task-expected[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: var(--color-text-primary);\\n}\\n\\n.task-content[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  line-height: 1.6;\\n  color: var(--color-text-primary);\\n  white-space: pre-wrap;\\n  word-wrap: break-word;\\n  background: var(--color-background-primary);\\n  padding: 12px;\\n  border-radius: 6px;\\n  border: 1px solid var(--color-border-secondary);\\n  max-height: 300px;\\n  overflow-y: auto;\\n}\\n\\n.execution-error[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #ef4444;\\n  background: rgba(239, 68, 68, 0.1);\\n  padding: 12px;\\n  border-radius: 6px;\\n  border: 1px solid rgba(239, 68, 68, 0.2);\\n}\\n.error-message[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #ef4444;\\n}\\n\\n.output-box[_ngcontent-%COMP%] {\\n  background: var(--color-background-secondary);\\n  border: 1px solid var(--color-border-primary);\\n  border-radius: 8px;\\n  margin-bottom: 16px;\\n}\\n.output-box[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n\\n.output-section[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n.output-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 12px 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: var(--color-text-primary);\\n}\\n.output-section[_ngcontent-%COMP%]   .task-description[_ngcontent-%COMP%], \\n.output-section[_ngcontent-%COMP%]   .task-expected[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n  font-size: 14px;\\n  color: var(--color-text-secondary);\\n}\\n.output-section[_ngcontent-%COMP%]   .task-description[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%], \\n.output-section[_ngcontent-%COMP%]   .task-expected[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: var(--color-text-primary);\\n}\\n\\n.output-text[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  line-height: 1.6;\\n  color: var(--color-text-primary);\\n  white-space: pre-wrap;\\n  word-wrap: break-word;\\n  background: var(--color-background-primary);\\n  padding: 12px;\\n  border-radius: 6px;\\n  border: 1px solid var(--color-border-secondary);\\n  max-height: 400px;\\n  overflow-y: auto;\\n}\\n\\n.tools-zone[_ngcontent-%COMP%] {\\n  border-color: #ff8c00;\\n}\\n.tools-zone[_ngcontent-%COMP%]   .zone-header[_ngcontent-%COMP%] {\\n  border-bottom-color: rgba(255, 140, 0, 0.2);\\n}\\n\\n.guardrails-zone[_ngcontent-%COMP%] {\\n  border-color: #dc2626;\\n}\\n.guardrails-zone[_ngcontent-%COMP%]   .zone-header[_ngcontent-%COMP%] {\\n  border-bottom-color: rgba(220, 38, 38, 0.2);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return AgentExecutionComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "Subject", "takeUntil", "switchMap", "finalize", "catchError", "of", "FormsModule", "AgentExecutionPlaygroundComponent", "IconComponent", "environment", "ExecutionStatus", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "AgentExecutionComponent_div_13_Template_app_agent_execution_playground_promptChange_1_listener", "$event", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onPromptChanged", "AgentExecutionComponent_div_13_Template_app_agent_execution_playground_messageSent_1_listener", "handleChatMessage", "AgentExecutionComponent_div_13_Template_app_agent_execution_playground_conversationalToggle_1_listener", "onPlaygroundConversationalToggle", "AgentExecutionComponent_div_13_Template_app_agent_execution_playground_templateToggle_1_listener", "onPlaygroundTemplateToggle", "AgentExecutionComponent_div_13_Template_app_agent_execution_playground_filesSelected_1_listener", "onFilesSelected", "AgentExecutionComponent_div_13_Template_app_agent_execution_playground_approvalRequested_1_listener", "onApprovalRequested", "ɵɵadvance", "ɵɵproperty", "chatMessages", "isProcessingChat", "agentType", "<PERSON><PERSON><PERSON>", "ɵɵtextInterpolate", "node_r4", "name", "ɵɵtemplate", "AgentExecutionComponent_div_22_div_40_div_1_Template", "AgentExecutionComponent_div_22_div_40_div_3_Template", "blueprintPromptNodes", "length", "node_r5", "AgentExecutionComponent_div_22_div_61_div_1_Template", "AgentExecutionComponent_div_22_div_61_div_3_Template", "blueprintKnowledgeNodes", "node_r6", "AgentExecutionComponent_div_22_div_79_div_1_Template", "AgentExecutionComponent_div_22_div_79_div_3_Template", "blueprintModelNodes", "ɵɵelement", "node_r8", "AgentExecutionComponent_div_22_div_80_div_13_div_1_Template", "AgentExecutionComponent_div_22_div_80_div_13_div_3_Template", "blueprintToolNodes", "AgentExecutionComponent_div_22_div_80_Template_div_click_1_listener", "_r7", "toggleBlueprintZone", "AgentExecutionComponent_div_22_div_80__svg_svg_4_Template", "AgentExecutionComponent_div_22_div_80_div_13_Template", "ɵɵclassProp", "ɵɵstyleProp", "isBlueprintZoneExpanded", "node_r10", "AgentExecutionComponent_div_22_div_81_div_15_div_1_Template", "AgentExecutionComponent_div_22_div_81_div_15_div_3_Template", "blueprintGuardrailNodes", "AgentExecutionComponent_div_22_div_81_Template_div_click_1_listener", "_r9", "AgentExecutionComponent_div_22_div_81_div_15_Template", "AgentExecutionComponent_div_22_Template_div_click_26_listener", "_r3", "AgentExecutionComponent_div_22_div_40_Template", "AgentExecutionComponent_div_22_Template_div_click_42_listener", "AgentExecutionComponent_div_22_div_61_Template", "AgentExecutionComponent_div_22_Template_div_click_63_listener", "AgentExecutionComponent_div_22_div_79_Template", "AgentExecutionComponent_div_22_div_80_Template", "AgentExecutionComponent_div_22_div_81_Template", "blueprintCompletionPercentage", "ɵɵtextInterpolate1", "execution_r11", "response", "choices", "text", "agentResponse", "detail", "taskOutput_r12", "description", "expected_output", "AgentExecutionComponent_div_23_div_5_div_1_div_15_div_1_div_1_Template", "AgentExecutionComponent_div_23_div_5_div_1_div_15_div_1_div_2_Template", "raw", "summary", "j_r13", "AgentExecutionComponent_div_23_div_5_div_1_div_15_div_1_Template", "agent", "tasksOutputs", "AgentExecutionComponent_div_23_div_5_div_1_div_14_Template", "AgentExecutionComponent_div_23_div_5_div_1_div_15_Template", "AgentExecutionComponent_div_23_div_5_div_1_div_16_Template", "status", "ɵɵclassMap", "userMessage", "ɵɵpipeBind2", "timestamp", "AgentExecutionComponent_div_23_div_5_div_1_Template", "executionHistory", "AgentExecutionComponent_div_23_div_4_Template", "AgentExecutionComponent_div_23_div_5_Template", "AgentExecutionComponent", "route", "router", "agentService", "agentPlaygroundService", "tokenStorage", "loaderService", "formBuilder", "toolExecutionService", "navigationTabs", "id", "label", "disabled", "agentId", "agentDetail", "playgroundComp", "activityLogs", "activityProgress", "executionDetails", "isRunning", "notStarted", "inputText", "agentOutputs", "latestAgentResponse", "agentForm", "executionStartTime", "executionCompleted", "executionId", "enableStreamingLog", "enableLogStreaming", "isExecutionComplete", "progressInterval", "destroy$", "selectedTab", "demoTabs", "errorMsg", "resMessage", "taskMessage", "isJsonValid", "disable<PERSON>hat", "selectedFiles", "agentNodes", "userInputList", "progress", "isLoading", "loaderColor", "inputFieldOrder", "currentInputIndex", "activeTabId", "isLeftPanelCollapsed", "activeRightTab", "currentAgentDetails", "buildAgentNodes", "canvasNodes", "canvasEdges", "selected<PERSON><PERSON><PERSON>", "selectedAgentMode", "selectedUseCaseIdentifier", "agentFilesUploadedData", "agentAttachment", "isAgentPlaygroundLoading", "agentPlaygroundDestroy", "agentChatPayload", "agentCode", "promptOptions", "blueprintZonesExpanded", "prompt", "model", "knowledge", "guardrail", "tool", "constructor", "group", "isConversational", "isUseTemplate", "ngOnInit", "console", "log", "crypto", "randomUUID", "params", "subscribe", "queryParams", "loadAgentData", "from", "ngOnDestroy", "next", "complete", "clearInterval", "onTabChange", "event", "getCollaborativeAgentDetailsById", "handleAgentDataResponse", "error", "getAgentById", "agentData", "agentDetails", "Array", "isArray", "data", "useCaseName", "useCaseCode", "organizationPath", "loadAgentNodes", "mapAgentConfigurationToBlueprint", "message", "showAgentError", "displayMessage", "fileNames", "map", "file", "documentName", "join", "push", "Date", "get", "value", "agentMode", "useCaseIdentifier", "orgPath", "buildOrganizationPath", "agentIdentifier", "processAgentFilesAndSendMessage", "sendAgentMessageToAPI", "payload", "Number", "user", "getDaUsername", "userInputs", "question", "fileWrapper", "submitAgentExecuteWithFile", "pipe", "res", "handleAgentExecuteResponse", "err", "setActiveRightTab", "submitAgentExecute", "String", "setValue", "files", "saveLogs", "exportResults", "section", "handleControlAction", "action", "navigateBack", "navigate", "mode", "editAgent", "navigateToAgentsList", "toggleLeftPanel", "tab", "zoneType", "getMetadataFromNavbar", "outputRaw", "output", "formattedOutput", "replace", "formData", "FormData", "for<PERSON>ach", "fileData", "append", "has", "getFileToContent", "fileResponse", "fileContent", "fileResponses", "sendAgentMessageToAPIWithFiles", "currentChatPayloadLength", "content", "role", "levelId", "generatePrompt", "generatedResponse", "aiResponseText", "warn", "errorMessage", "pop", "fileContents", "Object", "keys", "nodeCounter", "mapIndividualAgentToBlueprint", "mapCollaborativeAgentToBlueprint", "totalNodes", "config", "config<PERSON><PERSON><PERSON>", "useCaseDetails", "type", "category", "categoryIndex", "categoryId", "categoryName", "configItem", "itemIndex", "config<PERSON><PERSON>", "config<PERSON><PERSON><PERSON>", "kbValue", "toString", "kbIds", "split", "trim", "filter", "kbId", "key", "startsWith", "guardrailName", "promptNodes", "modelNodes", "knowledgeNodes", "guardrailNodes", "totalRequired", "currentRequired", "Math", "round", "shouldCreatePromptNode", "goal", "promptNodeName", "substring", "modelReferences", "hasAgentConfigs", "agentConfigs", "modelName", "modelDetails", "modelRef", "modelRefs", "ref", "modelId", "modelDeploymentName", "knowledgeReferences", "knowledgeBaseRef", "kbRefs", "knowledgeBaseId", "knowledgeBase", "kbRef", "collectionName", "indexCollectionName", "kbName", "toolReferences", "userToolReferences", "agentConfigsContent", "hasTools", "tools", "toolsContent", "hasUserTools", "userTools", "userToolsContent", "toolRef", "toolRefs", "toolId", "userToolRef", "userToolRefs", "toolName", "userTool", "userToolId", "userToolName", "toolNodes", "promptCount", "modelCount", "knowledgeCount", "toolCount", "guardrailCount", "isCollaborative", "hasToolNodes", "toolNodeNames", "t", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "AgentServiceService", "i3", "AgentPlaygroundService", "i4", "TokenStorageService", "i5", "LoaderService", "i6", "FormBuilder", "i7", "ToolExecutionService", "selectors", "viewQuery", "AgentExecutionComponent_Query", "rf", "ctx", "AgentExecutionComponent_Template_button_click_3_listener", "AgentExecutionComponent_Template_button_click_10_listener", "AgentExecutionComponent_button_12_Template", "AgentExecutionComponent_div_13_Template", "AgentExecutionComponent_Template_button_click_17_listener", "AgentExecutionComponent_Template_button_click_19_listener", "AgentExecutionComponent_div_22_Template", "AgentExecutionComponent_div_23_Template", "i8", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\agents\\agent-execution\\agent-execution.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\pages\\agents\\agent-execution\\agent-execution.component.html"], "sourcesContent": ["import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { CommonModule } from '@angular/common';\nimport { Subject, takeUntil, switchMap, finalize, catchError, of } from 'rxjs';\nimport { FormBuilder, FormGroup, FormsModule } from '@angular/forms';\n\n// Import child components\nimport { AgentExecutionPlaygroundComponent } from './components/agent-execution-playground/agent-execution-playground.component';\nimport { ChatMessage } from '@shared/components/chat-window/chat-window.component';\nimport { IconComponent, TabItem, DropdownOption } from '@ava/play-comp-library';\nimport { AgentServiceService } from '../services/agent-service.service';\nimport { AgentPlaygroundService } from '../build-agents/services/agent-playground.service';\nimport { environment } from '@shared/environments/environment';\nimport { TokenStorageService } from '@shared/auth/services/token-storage.service';\nimport { LoaderService } from '@shared/services/loader/loader.service';\nimport { ToolExecutionService } from '@shared/services/tool-execution/tool-execution.service';\nimport { AvaTab } from '@shared/models/tab.model';\nimport { ExecutionStatus, ActivityLog, ExecutionDetails, OutputItem } from '@shared/models/execution.model';\n\n// Remove duplicate definitions - they're now in shared models\n\n@Component({\n  selector: 'app-agent-execution',\n  standalone: true,\n  imports: [\n    CommonModule,\n    FormsModule,\n    AgentExecutionPlaygroundComponent,\n    IconComponent,\n  ],\n  templateUrl: './agent-execution.component.html',\n  styleUrls: ['./agent-execution.component.scss'],\n})\nexport class AgentExecutionComponent implements OnInit, OnDestroy {\n  navigationTabs: TabItem[] = [\n    { id: 'nav-home', label: 'Agent Activity' },\n    { id: 'nav-products', label: 'Agent Output' },\n    { id: 'nav-services', label: 'Preview', disabled: true },\n  ];\n\n  // Agent details\n  agentId: string | null = null;\n  agentType: string = 'individual';\n  agentName: string = 'Agent';\n  agentDetail: string = '';\n\n  @ViewChild(AgentExecutionPlaygroundComponent, { static: false })\n  playgroundComp!: AgentExecutionPlaygroundComponent;\n\n  // Activity logs\n  activityLogs: ActivityLog[] = [];\n  activityProgress: number = 0;\n  executionDetails?: ExecutionDetails;\n  isRunning: boolean = false;\n  status: ExecutionStatus = ExecutionStatus.notStarted;\n\n  // Chat messages\n  chatMessages: ChatMessage[] = [];\n  isProcessingChat: boolean = false;\n  inputText = '';\n\n  // Agent outputs\n  agentOutputs: OutputItem[] = [];\n  latestAgentResponse: any = null; // Store the latest agent response for display\n  \n  // New properties for execution history\n  executionHistory: Array<{\n    id: string;\n    agentName: string;\n    userMessage: string;\n    status: 'success' | 'failed';\n    response?: any;\n    timestamp: Date;\n  }> = [];\n\n  public agentForm!: FormGroup;\n\n  // Execution state\n  executionStartTime: Date | null = null;\n  executionCompleted: boolean = false;\n  executionId!: string;\n\n  enableStreamingLog = environment.enableLogStreaming || 'all';\n\n  public isExecutionComplete: boolean = false;\n  progressInterval: any;\n\n  private destroy$ = new Subject<void>();\n  selectedTab: string = 'Agent Activity';\n  demoTabs: AvaTab[] = [\n    { id: 'tab1', label: 'History' },\n    { id: 'tab2', label: 'Blueprint' },\n    { id: 'tab3', label: 'Agent Output' },\n  ];\n\n  errorMsg = false;\n  resMessage: any;\n  taskMessage: any[] = [];\n  isJsonValid = false;\n  disableChat: boolean = false;\n  selectedFiles: File[] = [];\n  agentNodes: any[] = [];\n  userInputList: any[] = [];\n  progress = 0;\n  isLoading = false;\n  loaderColor: string = '';\n\n  inputFieldOrder: string[] = [];\n  currentInputIndex: number = 0;\n  activeTabId: string = 'nav-home';\n\n  // Panel state properties\n  isLeftPanelCollapsed: boolean = false;\n  activeRightTab: string = 'blueprint';\n\n  // Agent-specific properties\n  currentAgentDetails: any = null;\n  buildAgentNodes: any[] = [];\n  canvasNodes: any[] = [];\n  canvasEdges: any[] = [];\n  selectedPrompt: string = '';\n  selectedAgentMode: string = '';\n  selectedUseCaseIdentifier: string = '';\n  agentFilesUploadedData: any[] = [];\n  agentAttachment: string[] = [];\n  isAgentPlaygroundLoading = false;\n  agentPlaygroundDestroy = new Subject<boolean>();\n  agentChatPayload: any[] = [];\n  agentCode: string = '';\n  promptOptions: DropdownOption[] = [];\n\n  // Custom Blueprint Display Properties\n  blueprintCompletionPercentage: number = 0;\n  blueprintPromptNodes: any[] = [];\n  blueprintModelNodes: any[] = [];\n  blueprintKnowledgeNodes: any[] = [];\n  blueprintGuardrailNodes: any[] = [];\n  blueprintToolNodes: any[] = [];\n\n  // Blueprint zone expansion state\n  private blueprintZonesExpanded: { [key: string]: boolean } = {\n    prompt: true,\n    model: true,\n    knowledge: true,\n    guardrail: true,\n    tool: true,\n  };\n\n  // Blueprint panel properties (using existing arrays above)\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private agentService: AgentServiceService,\n    private agentPlaygroundService: AgentPlaygroundService,\n    private tokenStorage: TokenStorageService,\n    private loaderService: LoaderService,\n    private formBuilder: FormBuilder,\n    private toolExecutionService: ToolExecutionService,\n  ) {\n    this.agentForm = this.formBuilder.group({\n      isConversational: [true],\n      isUseTemplate: [false],\n    });\n  }\n\n  ngOnInit(): void {\n    console.log('🌟 SHARED COMPONENT INITIALIZED! 🌟');\n    this.executionId = crypto.randomUUID();\n\n    this.route.params.subscribe((params) => {\n      this.agentType = params['type'] || 'individual';\n      console.log('🌟 SHARED: Agent type set to:', this.agentType);\n    });\n\n    this.route.queryParams.subscribe((params) => {\n      if (params['id']) {\n        this.agentId = params['id'];\n        this.loadAgentData(params['id']);\n      }\n    });\n\n    // Initialize chat messages\n    this.chatMessages = [\n      {\n        from: 'ai',\n        text: `Hi there! I am ${this.agentName || 'your agent'}. How can I help you today?`,\n      },\n    ];\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n    if (this.progressInterval) {\n      clearInterval(this.progressInterval);\n    }\n  }\n\n  onTabChange(event: { id: string; label: string }) {\n    this.activeTabId = event.id;\n    this.selectedTab = event.label;\n  }\n\n  loadAgentData(agentId: string): void {\n    this.isLoading = true;\n\n    // Load agent data based on type\n    if (this.agentType === 'collaborative') {\n      this.agentService.getCollaborativeAgentDetailsById(agentId).subscribe({\n        next: (response: any) => {\n          this.handleAgentDataResponse(response);\n        },\n        error: (error: any) => {\n          console.error('Error loading collaborative agent:', error);\n          this.isLoading = false;\n        },\n      });\n    } else {\n      this.agentService.getAgentById(agentId).subscribe({\n        next: (response: any) => {\n          this.handleAgentDataResponse(response);\n        },\n        error: (error: any) => {\n          console.error('Error loading individual agent:', error);\n          this.isLoading = false;\n        },\n      });\n    }\n  }\n\n  private handleAgentDataResponse(response: any): void {\n    this.isLoading = false;\n\n    // Extract agent details\n    let agentData;\n    if (\n      response.agentDetails &&\n      Array.isArray(response.agentDetails) &&\n      response.agentDetails.length > 0\n    ) {\n      agentData = response.agentDetails[0];\n    } else if (response.agentDetail) {\n      agentData = response.agentDetail;\n    } else if (response.data) {\n      agentData = response.data;\n    } else {\n      agentData = response;\n    }\n\n    if (agentData) {\n      this.currentAgentDetails = agentData;\n      this.agentName = agentData.name || agentData.agentName || 'Agent';\n      this.agentDetail = agentData.description || agentData.agentDetail || '';\n\n      // For individual agents, set up the required properties for playground functionality\n      if (this.agentType === 'individual') {\n        // Set selectedPrompt to agent name or useCaseName to bypass dropdown requirement\n        this.selectedPrompt =\n          agentData.useCaseName || agentData.name || 'loaded-agent';\n\n        // Set selectedAgentMode for API calls - use useCaseCode if available\n        this.selectedAgentMode =\n          agentData.useCaseCode ||\n          agentData.useCaseName ||\n          agentData.name ||\n          '';\n\n        // Set useCaseIdentifier - use organizationPath if available\n        if (agentData.organizationPath) {\n          this.selectedUseCaseIdentifier = agentData.organizationPath;\n        } else if (agentData.useCaseCode) {\n          this.selectedUseCaseIdentifier = agentData.useCaseCode;\n        } else if (agentData.useCaseName) {\n          this.selectedUseCaseIdentifier = agentData.useCaseName;\n        }\n      }\n\n      // Update chat message with agent name\n      if (this.chatMessages.length > 0) {\n        this.chatMessages[0].text = `Hi there! I am ${this.agentName}. How can I help you today?`;\n      }\n\n      // Load agent nodes and configuration\n      this.loadAgentNodes(agentData);\n    }\n  }\n\n  private loadAgentNodes(agentData: any): void {\n    // Map agent configuration to blueprint panel\n    this.mapAgentConfigurationToBlueprint(agentData);\n  }\n\n  handleChatMessage(message: string): void {\n    if (this.agentType === 'individual') {\n      // For individual agents, use the loaded agent details instead of requiring dropdown selection\n      if (\n        !this.currentAgentDetails &&\n        (!this.selectedPrompt || this.selectedPrompt === 'default')\n      ) {\n        this.showAgentError(\n          'Agent details are not loaded. Please try refreshing the page.',\n        );\n        return;\n      }\n\n      let displayMessage = message;\n      if (this.agentFilesUploadedData.length > 0) {\n        const fileNames = this.agentFilesUploadedData\n          .map((file) => file.documentName)\n          .join(', ');\n        displayMessage = `${message}\\n\\n📎 Attached files: ${fileNames}`;\n      }\n\n      // Add to execution history\n      const executionId = crypto.randomUUID();\n      this.executionHistory.push({\n        id: executionId,\n        agentName: this.agentName,\n        userMessage: message,\n        status: 'success', // Will be updated based on API response\n        timestamp: new Date()\n      });\n      console.log('Added new execution to history:', this.executionHistory);\n\n      this.chatMessages = [\n        ...this.chatMessages,\n        { from: 'user', text: displayMessage },\n      ];\n      this.isProcessingChat = true;\n\n      const isConversational =\n        this.agentForm.get('isConversational')?.value || false;\n      const isUseTemplate = this.agentForm.get('isUseTemplate')?.value || false;\n\n      console.log(\n        'Chat message handling - isConversational:',\n        isConversational,\n        'isUseTemplate:',\n        isUseTemplate,\n      );\n\n      // Use agent details from the loaded agent data instead of dropdown selection\n      // Mode should be the useCaseCode, not useCaseName\n      const agentMode =\n        this.agentCode ||\n        this.selectedAgentMode ||\n        this.currentAgentDetails?.useCaseCode ||\n        this.currentAgentDetails?.useCaseName ||\n        this.currentAgentDetails?.name ||\n        this.selectedPrompt;\n\n      let useCaseIdentifier = this.selectedUseCaseIdentifier;\n      if (!useCaseIdentifier) {\n        // Use organizationPath if available, otherwise build from agent details\n        if (this.currentAgentDetails?.organizationPath) {\n          useCaseIdentifier = this.currentAgentDetails.organizationPath;\n        } else {\n          const orgPath = this.buildOrganizationPath();\n          const agentIdentifier =\n            this.currentAgentDetails?.useCaseCode ||\n            this.currentAgentDetails?.useCaseName ||\n            this.currentAgentDetails?.name ||\n            agentMode;\n          useCaseIdentifier = `${agentIdentifier}${orgPath}`;\n        }\n      }\n\n      if (this.agentFilesUploadedData.length > 0) {\n        this.processAgentFilesAndSendMessage(\n          message,\n          agentMode,\n          useCaseIdentifier,\n          isConversational,\n          isUseTemplate,\n        );\n        return;\n      }\n\n      this.sendAgentMessageToAPI(\n        message,\n        agentMode,\n        useCaseIdentifier,\n        isConversational,\n        isUseTemplate,\n      );\n    } else if (this.agentType === 'collaborative') {\n      // Add to execution history for collaborative agents too\n      const executionId = crypto.randomUUID();\n      this.executionHistory.push({\n        id: executionId,\n        agentName: this.agentName,\n        userMessage: message,\n        status: 'success', // Will be updated based on API response\n        timestamp: new Date()\n      });\n      console.log('Added new collaborative execution to history:', this.executionHistory);\n\n      this.isProcessingChat = true;\n      let payload = {\n        executionId: this.executionId,\n        agentId: Number(this.agentId),\n        user: this.tokenStorage.getDaUsername() || '<EMAIL>',\n        userInputs: { question: message },\n      };\n\n      if (this.agentFilesUploadedData.length > 0) {\n        const fileWrapper = this.agentFilesUploadedData[0];\n        let displayMessage: string;\n        if (this.agentFilesUploadedData.length > 0) {\n          const fileNames = this.agentFilesUploadedData\n            .map((file) => file.documentName)\n            .join(', ');\n          displayMessage = `📎 Attached files: ${fileNames}`;\n\n          this.chatMessages = [{ from: 'user', text: displayMessage }];\n        }\n        this.agentPlaygroundService\n          .submitAgentExecuteWithFile(payload, fileWrapper)\n          .pipe(\n            finalize(() => {\n              this.isProcessingChat = false;\n              this.isAgentPlaygroundLoading = false;\n            }),\n            takeUntil(this.agentPlaygroundDestroy),\n          )\n          .subscribe({\n            next: (res) => this.handleAgentExecuteResponse(res, message),\n            error: (err: any) => {\n              // Update execution history with failed status\n              if (this.executionHistory.length > 0) {\n                this.executionHistory[this.executionHistory.length - 1].status = 'failed';\n              }\n              \n              this.chatMessages = [\n                ...this.chatMessages,\n                { from: 'user', text: message },\n                {\n                  from: 'ai',\n                  text:\n                    err?.error?.message ||\n                    err?.message ||\n                    'Something went wrong.',\n                },\n              ];\n              \n              // Switch to output tab even on failure\n              this.setActiveRightTab('output');\n            },\n          });\n      } else {\n        this.agentPlaygroundService\n          .submitAgentExecute(payload)\n          .pipe(\n            finalize(() => {\n              this.isProcessingChat = false;\n              this.isAgentPlaygroundLoading = false;\n            }),\n            takeUntil(this.agentPlaygroundDestroy),\n          )\n          .subscribe({\n            next: (res) => this.handleAgentExecuteResponse(res, message),\n            error: (err: any) => {\n              // Update execution history with failed status\n              if (this.executionHistory.length > 0) {\n                this.executionHistory[this.executionHistory.length - 1].status = 'failed';\n              }\n              \n              this.chatMessages = [\n                ...this.chatMessages,\n                { from: 'user', text: message },\n                {\n                  from: 'ai',\n                  text:\n                    err?.error?.message ||\n                    err?.message ||\n                    'Something went wrong.',\n                },\n              ];\n              \n              // Switch to output tab even on failure\n              this.setActiveRightTab('output');\n            },\n          });\n      }\n    }\n  }\n\n  onPromptChanged(prompt: DropdownOption): void {\n    this.inputText = prompt.name || String(prompt.value) || '';\n  }\n\n  onPlaygroundConversationalToggle(value: boolean): void {\n    // Update the form control\n    this.agentForm.get('isConversational')?.setValue(value);\n\n    // When conversational mode is turned off, clear the conversation history\n    // This ensures that the next message will be treated as a fresh start\n    if (!value) {\n      this.agentChatPayload = [];\n      console.log(\n        'Conversational mode disabled - cleared chat payload history',\n      );\n    } else {\n      console.log('Conversational mode enabled - will maintain chat history');\n    }\n  }\n\n  onPlaygroundTemplateToggle(value: boolean): void {\n    // Update the form control\n    this.agentForm.get('isUseTemplate')?.setValue(value);\n    console.log('Template mode toggled:', value);\n  }\n\n  onFilesSelected(files: any[]): void {\n    this.selectedFiles = files;\n    // Update agentFilesUploadedData for agent execution\n    this.agentFilesUploadedData = files;\n  }\n\n  onApprovalRequested(): void {\n    // Handle approval request\n  }\n\n  saveLogs(): void {\n    // Save execution logs\n  }\n\n  exportResults(section: 'activity' | 'output'): void {\n    // Export results\n  }\n\n  handleControlAction(action: 'play' | 'pause' | 'stop'): void {\n    // Handle execution control actions\n  }\n\n  navigateBack(): void {\n    this.router.navigate(['/build/agents', this.agentType], {\n      queryParams: { id: this.agentId, mode: 'view' },\n    });\n  }\n\n  editAgent(): void {\n    this.router.navigate(['/build/agents', this.agentType], {\n      queryParams: { id: this.agentId, mode: 'edit' },\n    });\n  }\n\n  navigateToAgentsList(): void {\n    this.router.navigate(['/build/agents']);\n  }\n\n  toggleLeftPanel(): void {\n    this.isLeftPanelCollapsed = !this.isLeftPanelCollapsed;\n  }\n\n  setActiveRightTab(tab: string): void {\n    this.activeRightTab = tab;\n  }\n\n  // Blueprint zone management methods\n  toggleBlueprintZone(zoneType: string): void {\n    this.blueprintZonesExpanded[zoneType] =\n      !this.blueprintZonesExpanded[zoneType];\n  }\n\n  isBlueprintZoneExpanded(zoneType: string): boolean {\n    return this.blueprintZonesExpanded[zoneType] || false;\n  }\n\n  // API and helper methods from build-agents component\n  private showAgentError(message: string): void {\n    this.chatMessages = [...this.chatMessages, { from: 'ai', text: message }];\n  }\n\n  private buildOrganizationPath(): string {\n    // Simple implementation - in real scenario this would be from navbar/metadata\n    return '';\n  }\n\n  private getMetadataFromNavbar(): { levelId?: number } {\n    // Simple implementation - in real scenario this would get org level mapping\n    return {};\n  }\n\n  handleAgentExecuteResponse(response: any, message: string): void {\n    try {\n      // Store the latest response for display in the output panel\n      this.latestAgentResponse = response;\n\n      // Update the latest execution history entry with success status and response\n      if (this.executionHistory.length > 0) {\n        this.executionHistory[this.executionHistory.length - 1].status = 'success';\n        this.executionHistory[this.executionHistory.length - 1].response = response;\n        console.log('Updated execution history:', this.executionHistory);\n      }\n\n      const outputRaw = response?.agentResponse?.agent?.output;\n      let formattedOutput = '';\n\n      if (outputRaw) {\n        // Directly replace escaped \\n with real newlines\n        formattedOutput = outputRaw.replace(/\\\\n/g, '\\n');\n      } else {\n        formattedOutput = response?.agentResponse?.detail;\n      }\n\n      // In playground, only show agent name and status, not the full response\n      this.chatMessages = [\n        ...this.chatMessages,\n        { from: 'user', text: message },\n        { \n          from: 'ai', \n          text: `${this.agentName} - Status: Success` \n        },\n      ];\n\n      // Automatically switch to output tab after successful execution\n      this.setActiveRightTab('output');\n    } catch (err: any) {\n      // Update execution history with failed status\n      if (this.executionHistory.length > 0) {\n        this.executionHistory[this.executionHistory.length - 1].status = 'failed';\n        console.log('Updated execution history (failed):', this.executionHistory);\n      }\n\n      this.chatMessages = [\n        ...this.chatMessages,\n        { from: 'user', text: message },\n        { \n          from: 'ai', \n          text: `${this.agentName} - Status: Failed` \n        },\n      ];\n      \n      // Switch to output tab even on failure\n      this.setActiveRightTab('output');\n    }\n  }\n\n  private processAgentFilesAndSendMessage(\n    message: string,\n    mode: string,\n    useCaseIdentifier: string,\n    isConversational: boolean,\n    isUseTemplate: boolean,\n  ): void {\n    const formData = new FormData();\n    this.agentFilesUploadedData.forEach((fileData) => {\n      if (fileData.file) {\n        formData.append('files', fileData.file);\n      }\n    });\n\n    if (formData.has('files')) {\n      this.agentPlaygroundService\n        .getFileToContent(formData)\n        .pipe(\n          switchMap((fileResponse) => {\n            const fileContent =\n              fileResponse?.fileResponses\n                ?.map((response: any) => response.fileContent)\n                ?.join('\\n') || '';\n            this.sendAgentMessageToAPIWithFiles(\n              message,\n              mode,\n              useCaseIdentifier,\n              isConversational,\n              isUseTemplate,\n              fileContent,\n            );\n            return of(null);\n          }),\n          catchError((error) => {\n            console.error('Error parsing files:', error);\n            this.sendAgentMessageToAPI(\n              message,\n              mode,\n              useCaseIdentifier,\n              isConversational,\n              isUseTemplate,\n            );\n            return of(null);\n          }),\n        )\n        .subscribe();\n    } else {\n      this.sendAgentMessageToAPI(\n        message,\n        mode,\n        useCaseIdentifier,\n        isConversational,\n        isUseTemplate,\n      );\n    }\n  }\n\n  private sendAgentMessageToAPI(\n    message: string,\n    mode: string,\n    useCaseIdentifier: string,\n    isConversational: boolean,\n    isUseTemplate: boolean,\n  ): void {\n    console.log('API Call Parameters:', {\n      message,\n      mode,\n      useCaseIdentifier,\n      isConversational,\n      isUseTemplate,\n      currentChatPayloadLength: this.agentChatPayload.length,\n    });\n\n    if (isConversational) {\n      this.agentChatPayload.push({ content: message, role: 'user' });\n    }\n\n    const payload = isConversational ? this.agentChatPayload : message;\n    const { levelId } = this.getMetadataFromNavbar();\n\n    console.log('Final payload being sent:', payload);\n\n    this.agentPlaygroundService\n      .generatePrompt(\n        payload,\n        mode,\n        isConversational,\n        isUseTemplate,\n        this.agentAttachment,\n        useCaseIdentifier,\n        '',\n        levelId,\n      )\n      .pipe(\n        finalize(() => {\n          this.isProcessingChat = false;\n          this.isAgentPlaygroundLoading = false;\n        }),\n        takeUntil(this.agentPlaygroundDestroy),\n      )\n      .subscribe({\n        next: (generatedResponse: any) => {\n          // Store the latest response for display in the output panel\n          this.latestAgentResponse = generatedResponse;\n\n          if (\n            generatedResponse?.response &&\n            generatedResponse?.response?.choices\n          ) {\n            const aiResponseText = generatedResponse.response.choices[0].text;\n            this.chatMessages = [\n              ...this.chatMessages,\n              { from: 'ai', text: aiResponseText },\n            ];\n            if (isConversational) {\n              this.agentChatPayload.push({\n                content: aiResponseText,\n                role: 'assistant',\n              });\n            }\n          } else {\n            console.warn('Unexpected API response format:', generatedResponse);\n            this.showAgentError(\n              'Received unexpected response format from API.',\n            );\n          }\n        },\n        error: (error: any) => {\n          console.error('API Error:', error);\n          const errorMessage =\n            error?.error?.message ||\n            'An error occurred while processing your request.';\n          this.showAgentError(errorMessage);\n          if (isConversational && this.agentChatPayload.length > 0) {\n            this.agentChatPayload.pop();\n          }\n        },\n      });\n  }\n\n  private sendAgentMessageToAPIWithFiles(\n    message: string,\n    mode: string,\n    useCaseIdentifier: string,\n    isConversational: boolean,\n    isUseTemplate: boolean,\n    fileContents: string,\n  ): void {\n    if (isConversational) {\n      this.agentChatPayload.push({ content: message, role: 'user' });\n    }\n    const payload = isConversational ? this.agentChatPayload : message;\n    const { levelId } = this.getMetadataFromNavbar();\n\n    this.agentPlaygroundService\n      .generatePrompt(\n        payload,\n        mode,\n        isConversational,\n        isUseTemplate,\n        this.agentAttachment,\n        useCaseIdentifier,\n        fileContents,\n        levelId,\n      )\n      .pipe(\n        finalize(() => {\n          this.isProcessingChat = false;\n          this.isAgentPlaygroundLoading = false;\n        }),\n        takeUntil(this.agentPlaygroundDestroy),\n      )\n      .subscribe({\n        next: (generatedResponse: any) => {\n          if (\n            generatedResponse?.response &&\n            generatedResponse?.response?.choices\n          ) {\n            const aiResponseText = generatedResponse.response.choices[0].text;\n            this.chatMessages = [\n              ...this.chatMessages,\n              { from: 'ai', text: aiResponseText },\n            ];\n            if (isConversational) {\n              this.agentChatPayload.push({\n                content: aiResponseText,\n                role: 'assistant',\n              });\n            }\n          } else {\n            console.warn('Unexpected API response format:', generatedResponse);\n            this.showAgentError(\n              'Received unexpected response format from API.',\n            );\n          }\n        },\n        error: (error: any) => {\n          console.error('API Error:', error);\n          const errorMessage =\n            error?.error?.message ||\n            'An error occurred while processing your request.';\n          this.showAgentError(errorMessage);\n          if (isConversational && this.agentChatPayload.length > 0) {\n            this.agentChatPayload.pop();\n          }\n        },\n      });\n  }\n\n  // Blueprint panel methods\n  private mapAgentConfigurationToBlueprint(agentData: any): void {\n    if (!agentData) {\n      console.warn('No agent data provided for blueprint');\n      return;\n    }\n\n    console.log('🔍 DEBUG: Full agent data received:', agentData);\n    console.log('🔍 DEBUG: Agent type:', this.agentType);\n    console.log('🔍 DEBUG: Agent data keys:', Object.keys(agentData));\n\n    // Clear existing nodes\n    this.buildAgentNodes = [];\n    this.canvasNodes = [];\n\n    let nodeCounter = 1;\n\n    // Map agent configuration to nodes based on agent type\n    if (this.agentType === 'individual') {\n      this.mapIndividualAgentToBlueprint(agentData, nodeCounter);\n    } else if (this.agentType === 'collaborative') {\n      this.mapCollaborativeAgentToBlueprint(agentData, nodeCounter);\n    }\n\n    console.log('🎯 Blueprint nodes mapped:', {\n      buildAgentNodes: this.buildAgentNodes,\n      canvasNodes: this.canvasNodes,\n      totalNodes: this.buildAgentNodes.length,\n    });\n  }\n\n  private mapIndividualAgentToBlueprint(\n    agentData: any,\n    nodeCounter: number,\n  ): void {\n    console.log('🔍 Individual agent mapping - checking fields:', {\n      config: agentData.config,\n      configLength: agentData.config?.length,\n      useCaseName: agentData.useCaseName,\n      prompt: agentData.prompt,\n      useCaseDetails: agentData.useCaseDetails,\n    });\n\n    // Clear existing blueprint nodes\n    this.blueprintPromptNodes = [];\n    this.blueprintModelNodes = [];\n    this.blueprintKnowledgeNodes = [];\n    this.blueprintGuardrailNodes = [];\n\n    // Add prompt node from \"prompt\" field\n    if (agentData.prompt) {\n      this.blueprintPromptNodes.push({\n        id: `prompt-${nodeCounter++}`,\n        name: agentData.prompt,\n        type: 'prompt',\n      });\n      console.log('✅ Added prompt node:', agentData.prompt);\n    }\n\n    // Process the config array to extract model, knowledge bases, and guardrails\n    if (agentData.config && Array.isArray(agentData.config)) {\n      console.log(\n        '🔍 Processing config array with length:',\n        agentData.config.length,\n      );\n\n      agentData.config.forEach((category: any, categoryIndex: number) => {\n        console.log(\n          `🔍 Category ${categoryIndex} (ID: ${category.categoryId}):`,\n          category.categoryName,\n        );\n\n        if (category.config && Array.isArray(category.config)) {\n          console.log(\n            `🔍 Category ${categoryIndex} has ${category.config.length} config items`,\n          );\n\n          category.config.forEach((configItem: any, itemIndex: number) => {\n            console.log(`🔍 Config item ${categoryIndex}-${itemIndex}:`, {\n              configKey: configItem.configKey,\n              configValue: configItem.configValue,\n              categoryId: configItem.categoryId,\n            });\n\n            // Handle AI Model from categoryId 1\n            if (\n              configItem.categoryId === 1 &&\n              configItem.configKey === 'MODEL' &&\n              configItem.configValue\n            ) {\n              console.log(\n                '✅ Adding AI model node from categoryId 1:',\n                configItem.configValue,\n              );\n              this.blueprintModelNodes.push({\n                id: `model-${nodeCounter++}`,\n                name: `${configItem.configKey}`,\n                type: 'model',\n              });\n            }\n\n            // Handle Knowledge Base from categoryId 2\n            if (\n              configItem.categoryId === 2 &&\n              configItem.configKey === 'RAG_KNOWLEDGEBASE_NAME' &&\n              configItem.configValue\n            ) {\n              console.log(\n                '✅ Adding knowledge base nodes from categoryId 2:',\n                configItem.configValue,\n              );\n              const kbValue = configItem.configValue.toString();\n              const kbIds = kbValue\n                .split(',')\n                .map((id: string) => id.trim())\n                .filter((id: string) => id);\n\n              kbIds.forEach((kbId: string) => {\n                this.blueprintKnowledgeNodes.push({\n                  id: `knowledge-${nodeCounter++}`,\n                  name: `Knowledge Base: ${kbId}`,\n                  type: 'knowledge',\n                });\n              });\n            }\n\n            // Handle Guardrails from categoryId 3 where configValue is true\n            if (\n              configItem.categoryId === 3 &&\n              configItem.configValue === 'true'\n            ) {\n              console.log('✅ Found enabled guardrail from categoryId 3:', {\n                key: configItem.configKey,\n                value: configItem.configValue,\n              });\n\n              if (configItem.configKey === 'ENABLE_GUARDRAILS') {\n                // Only add one general guardrail node if not already added\n                if (this.blueprintGuardrailNodes.length === 0) {\n                  this.blueprintGuardrailNodes.push({\n                    id: `guardrail-${nodeCounter++}`,\n                    name: 'Guardrails Enabled',\n                    type: 'guardrail',\n                  });\n                }\n              } else if (configItem.configKey.startsWith('GUARDRAIL_')) {\n                // Add specific guardrail nodes for enabled guardrails\n                let guardrailName = configItem.configKey;\n                if (guardrailName.startsWith('GUARDRAIL_')) {\n                  guardrailName = guardrailName\n                    .replace('GUARDRAIL_', '')\n                    .replace(/_/g, ' ');\n                }\n\n                this.blueprintGuardrailNodes.push({\n                  id: `guardrail-${nodeCounter++}`,\n                  name: `${guardrailName}`,\n                  type: 'guardrail',\n                });\n              }\n            }\n          });\n        }\n      });\n    }\n\n    console.log('🎯 Final blueprint nodes:', {\n      promptNodes: this.blueprintPromptNodes,\n      modelNodes: this.blueprintModelNodes,\n      knowledgeNodes: this.blueprintKnowledgeNodes,\n      guardrailNodes: this.blueprintGuardrailNodes,\n    });\n\n    // Calculate completion percentage\n    const totalRequired = 2; // Prompt + Model are required\n    const currentRequired =\n      this.blueprintPromptNodes.length + this.blueprintModelNodes.length;\n    this.blueprintCompletionPercentage = Math.round(\n      (currentRequired / totalRequired) * 100,\n    );\n  }\n\n  private mapCollaborativeAgentToBlueprint(\n    agentData: any,\n    nodeCounter: number,\n  ): void {\n    console.log(\n      '🚀 SHARED COMPONENT: mapCollaborativeAgentToBlueprint called!',\n    );\n    console.log('🔍 DEBUG: Collaborative agent data received:', agentData);\n    console.log(\n      '🔍 DEBUG: Collaborative agent data keys:',\n      Object.keys(agentData),\n    );\n    console.log('🔍 DEBUG: Agent type in component:', this.agentType);\n    console.log('🔍 DEBUG: Current nodeCounter:', nodeCounter);\n\n    // Clear existing blueprint nodes\n    this.blueprintPromptNodes = [];\n    this.blueprintModelNodes = [];\n    this.blueprintKnowledgeNodes = [];\n    this.blueprintToolNodes = [];\n    this.blueprintGuardrailNodes = [];\n\n    console.log('🔍 DEBUG: Cleared all blueprint node arrays');\n\n    // Add prompt node - handle different prompt structures for collaborative agents\n    const shouldCreatePromptNode =\n      agentData.goal || agentData.role || agentData.description;\n\n    console.log('🔍 DEBUG: Checking prompt node creation:', {\n      goal: agentData.goal,\n      role: agentData.role,\n      description: agentData.description,\n      shouldCreatePromptNode,\n    });\n\n    if (shouldCreatePromptNode) {\n      let promptNodeName =\n        agentData.goal ||\n        agentData.role ||\n        agentData.description ||\n        'Collaborative Agent Prompt';\n\n      // Truncate prompt if too long for display\n      if (promptNodeName.length > 150) {\n        promptNodeName = promptNodeName.substring(0, 150) + '...';\n      }\n\n      this.blueprintPromptNodes.push({\n        id: `prompt-${nodeCounter++}`,\n        name: promptNodeName,\n        type: 'prompt',\n      });\n      console.log('✅ Added collaborative prompt node:', promptNodeName);\n    }\n\n    // Add model nodes - handle both old and new API formats like build-agents\n    let modelReferences = [];\n\n    console.log('🔍 DEBUG: Checking model data:', {\n      hasAgentConfigs: !!agentData.agentConfigs,\n      agentConfigs: agentData.agentConfigs,\n      model: agentData.model,\n      modelName: agentData.modelName,\n      modelDetails: agentData.modelDetails,\n    });\n\n    // New API format: agentConfigs.modelRef (array of model IDs or objects)\n    if (agentData.agentConfigs && agentData.agentConfigs.modelRef) {\n      const modelRefs = Array.isArray(agentData.agentConfigs.modelRef)\n        ? agentData.agentConfigs.modelRef\n        : [agentData.agentConfigs.modelRef];\n\n      modelReferences = modelRefs.map((ref: any) => {\n        if (typeof ref === 'number' || typeof ref === 'string') {\n          return { modelId: ref };\n        }\n        return ref;\n      });\n    }\n    // Old API format: modelDetails\n    else if (agentData.modelDetails) {\n      modelReferences = [agentData.modelDetails];\n    }\n    // Fallback: check for model or modelName directly\n    else if (agentData.model || agentData.modelName) {\n      modelReferences = [{ modelId: agentData.model || agentData.modelName }];\n    }\n\n    modelReferences.forEach((modelRef: any) => {\n      const modelId = modelRef.modelId || modelRef.id;\n      const modelName =\n        modelRef.model ||\n        modelRef.modelDeploymentName ||\n        `Model ID: ${modelId}`;\n\n      this.blueprintModelNodes.push({\n        id: `model-${nodeCounter++}`,\n        name: modelName,\n        type: 'model',\n      });\n      console.log('✅ Added collaborative model node:', modelName);\n    });\n\n    // Add knowledge base nodes - handle both old and new API formats\n    let knowledgeReferences = [];\n\n    // New API format: agentConfigs.knowledgeBaseRef (array of KB IDs or objects)\n    if (agentData.agentConfigs && agentData.agentConfigs.knowledgeBaseRef) {\n      const kbRefs = Array.isArray(agentData.agentConfigs.knowledgeBaseRef)\n        ? agentData.agentConfigs.knowledgeBaseRef\n        : [agentData.agentConfigs.knowledgeBaseRef];\n\n      knowledgeReferences = kbRefs.map((ref: any) => {\n        if (typeof ref === 'number' || typeof ref === 'string') {\n          return { knowledgeBaseId: ref };\n        }\n        return ref;\n      });\n    }\n    // Old API format: knowledgeBase\n    else if (\n      agentData.knowledgeBase &&\n      Array.isArray(agentData.knowledgeBase)\n    ) {\n      knowledgeReferences = agentData.knowledgeBase;\n    }\n\n    knowledgeReferences.forEach((kbRef: any) => {\n      const kbId = kbRef.knowledgeBaseId || kbRef.id;\n      const collectionName = kbRef.indexCollectionName || kbRef.name;\n      const kbName = collectionName || `Knowledge Base ID: ${kbId}`;\n\n      this.blueprintKnowledgeNodes.push({\n        id: `knowledge-${nodeCounter++}`,\n        name: kbName,\n        type: 'knowledge',\n      });\n      console.log('✅ Added collaborative knowledge node:', kbName);\n    });\n\n    // Add tool nodes - handle both old and new API formats like build-agents\n    let toolReferences = [];\n    let userToolReferences = [];\n\n    console.log('🔍 DEBUG: Checking tool data:', {\n      hasAgentConfigs: !!agentData.agentConfigs,\n      agentConfigsContent: agentData.agentConfigs,\n      hasTools: agentData.tools,\n      toolsContent: agentData.tools,\n      hasUserTools: agentData.userTools,\n      userToolsContent: agentData.userTools,\n    });\n\n    // New API format: agentConfigs.toolRef and agentConfigs.userToolRef\n    if (agentData.agentConfigs) {\n      if (agentData.agentConfigs.toolRef) {\n        const toolRefs = Array.isArray(agentData.agentConfigs.toolRef)\n          ? agentData.agentConfigs.toolRef\n          : [agentData.agentConfigs.toolRef];\n\n        toolReferences = toolRefs.map((ref: any) => {\n          if (typeof ref === 'number' || typeof ref === 'string') {\n            return { toolId: ref };\n          }\n          return ref;\n        });\n      }\n      if (agentData.agentConfigs.userToolRef) {\n        const userToolRefs = Array.isArray(agentData.agentConfigs.userToolRef)\n          ? agentData.agentConfigs.userToolRef\n          : [agentData.agentConfigs.userToolRef];\n\n        userToolReferences = userToolRefs.map((ref: any) => {\n          if (typeof ref === 'number' || typeof ref === 'string') {\n            return { toolId: ref };\n          }\n          return ref;\n        });\n      }\n    }\n    // Old API format: tools and userTools\n    else {\n      if (agentData.tools && Array.isArray(agentData.tools)) {\n        toolReferences = agentData.tools;\n      }\n      if (agentData.userTools && Array.isArray(agentData.userTools)) {\n        userToolReferences = agentData.userTools;\n      }\n    }\n\n    // Process built-in tools\n    toolReferences.forEach((tool: any) => {\n      const toolId = tool.toolId || tool.id;\n      const toolName = tool.toolName || tool.name || `Tool ID: ${toolId}`;\n\n      this.blueprintToolNodes.push({\n        id: `tool-${nodeCounter++}`,\n        name: toolName,\n        type: 'tool',\n      });\n      console.log('✅ Added collaborative builtin tool node:', toolName);\n    });\n\n    // Process user tools\n    userToolReferences.forEach((userTool: any) => {\n      const userToolId = userTool.toolId || userTool.id;\n      const userToolName =\n        userTool.toolName || userTool.name || `User Tool ID: ${userToolId}`;\n\n      this.blueprintToolNodes.push({\n        id: `tool-${nodeCounter++}`,\n        name: userToolName,\n        type: 'tool',\n      });\n      console.log('✅ Added collaborative user tool node:', userToolName);\n    });\n\n    console.log('🎯 Final collaborative blueprint nodes:', {\n      promptNodes: this.blueprintPromptNodes,\n      modelNodes: this.blueprintModelNodes,\n      knowledgeNodes: this.blueprintKnowledgeNodes,\n      toolNodes: this.blueprintToolNodes,\n      guardrailNodes: this.blueprintGuardrailNodes,\n    });\n\n    // Debug: Check blueprint node arrays lengths\n    console.log('📊 Blueprint node counts:', {\n      promptCount: this.blueprintPromptNodes.length,\n      modelCount: this.blueprintModelNodes.length,\n      knowledgeCount: this.blueprintKnowledgeNodes.length,\n      toolCount: this.blueprintToolNodes.length,\n      guardrailCount: this.blueprintGuardrailNodes.length,\n    });\n\n    // Debug: Check if tools zone will be visible\n    console.log('🔧 Tools zone debug:', {\n      agentType: this.agentType,\n      isCollaborative: this.agentType === 'collaborative',\n      hasToolNodes: this.blueprintToolNodes.length > 0,\n      toolNodeNames: this.blueprintToolNodes.map((t) => t.name),\n    });\n\n    // Calculate completion percentage\n    const totalRequired = 2; // Prompt + Model are required\n    const currentRequired =\n      this.blueprintPromptNodes.length + this.blueprintModelNodes.length;\n    this.blueprintCompletionPercentage = Math.round(\n      (currentRequired / totalRequired) * 100,\n    );\n  }\n}\n", "<div class=\"agent-execution-container\">\n  <!-- Top Navigation Bar -->\n  <div class=\"top-nav-bar\">\n    <div class=\"nav-left\">\n      <button class=\"back-button\" (click)=\"navigateBack()\" type=\"button\">\n        <ava-icon\n          iconName=\"ArrowLeft\"\n          iconSize=\"16\"\n          iconColor=\"#374151\"\n        ></ava-icon>\n        <span class=\"agent-name\">{{ agentName }}</span>\n      </button>\n    </div>\n  </div>\n\n  <!-- Main Content with Two Panels -->\n  <div class=\"main-content\">\n    <!-- Left Panel: Playground -->\n    <div class=\"left-panel\" [class.collapsed]=\"isLeftPanelCollapsed\">\n      <div class=\"panel-header\">\n        <button class=\"collapse-btn\" (click)=\"toggleLeftPanel()\" type=\"button\">\n          <ava-icon\n            [iconName]=\"isLeftPanelCollapsed ? 'ChevronRight' : 'PanelLeft'\"\n            iconSize=\"16\"\n            iconColor=\"#6B7280\"\n          >\n          </ava-icon>\n        </button>\n        <button\n          class=\"history-btn\"\n          *ngIf=\"!isLeftPanelCollapsed\"\n          type=\"button\"\n          disabled\n        >\n          History\n        </button>\n      </div>\n\n      <div class=\"panel-content\" *ngIf=\"!isLeftPanelCollapsed\">\n        <app-agent-execution-playground\n          [messages]=\"chatMessages\"\n          [isLoading]=\"isProcessingChat\"\n          [agentType]=\"agentType\"\n          [showChatInteractionToggles]=\"agentType === 'individual'\"\n          [showAiPrincipleToggle]=\"false\"\n          [showApprovalButton]=\"false\"\n          [showDropdown]=\"false\"\n          [showAgentNameInput]=\"true\"\n          [showFileUploadButton]=\"true\"\n          [showStatusOnly]=\"true\"\n          [displayedAgentName]=\"agentName\"\n          [agentNamePlaceholder]=\"'Current Agent Name'\"\n          (promptChange)=\"onPromptChanged($event)\"\n          (messageSent)=\"handleChatMessage($event)\"\n          (conversationalToggle)=\"onPlaygroundConversationalToggle($event)\"\n          (templateToggle)=\"onPlaygroundTemplateToggle($event)\"\n          (filesSelected)=\"onFilesSelected($event)\"\n          (approvalRequested)=\"onApprovalRequested()\"\n        >\n        </app-agent-execution-playground>\n      </div>\n    </div>\n\n    <!-- Right Panel: Blueprint/Output -->\n    <div class=\"right-panel\">\n      <!-- Right Panel Header -->\n      <div class=\"panel-header\">\n        <div class=\"tabs-container\">\n          <button\n            class=\"tab-btn\"\n            [class.active]=\"activeRightTab === 'blueprint'\"\n            (click)=\"setActiveRightTab('blueprint')\"\n          >\n            Blueprint\n          </button>\n          <button\n            class=\"tab-btn\"\n            [class.active]=\"activeRightTab === 'output'\"\n            (click)=\"setActiveRightTab('output')\"\n          >\n            Agent Output\n          </button>\n        </div>\n      </div>\n\n      <div class=\"panel-content\">\n        <!-- Blueprint Content -->\n        <div *ngIf=\"activeRightTab === 'blueprint'\" class=\"blueprint-content\">\n          <div class=\"blueprint-header\">\n            <h3>Agent Blueprint</h3>\n          </div>\n\n          <!-- Custom Blueprint Display -->\n          <div class=\"custom-blueprint-container\">\n            <!-- SVG Connecting Lines -->\n            <svg\n              class=\"connection-lines\"\n              viewBox=\"0 0 100 100\"\n              preserveAspectRatio=\"none\"\n            >\n              <!-- Line from System Prompt (top-left) to center -->\n              <line\n                x1=\"25\"\n                y1=\"25\"\n                x2=\"50\"\n                y2=\"50\"\n                stroke=\"#BBBEC5\"\n                stroke-width=\"0.3\"\n              />\n\n              <!-- Line from AI Model (top-right) to center -->\n              <line\n                x1=\"75\"\n                y1=\"25\"\n                x2=\"50\"\n                y2=\"50\"\n                stroke=\"#BBBEC5\"\n                stroke-width=\"0.3\"\n              />\n\n              <!-- Line from Knowledge Base (bottom-left) to center -->\n              <line\n                x1=\"25\"\n                y1=\"75\"\n                x2=\"50\"\n                y2=\"50\"\n                stroke=\"#BBBEC5\"\n                stroke-width=\"0.3\"\n              />\n\n              <!-- Line from Guardrails (bottom-right) to center -->\n              <line\n                x1=\"75\"\n                y1=\"75\"\n                x2=\"50\"\n                y2=\"50\"\n                stroke=\"#BBBEC5\"\n                stroke-width=\"0.3\"\n              />\n            </svg>\n\n            <!-- Central Progress Bar -->\n            <div class=\"central-progress\">\n              <div class=\"progress-ring\">\n                <svg class=\"progress-circle\" width=\"120\" height=\"120\">\n                  <defs>\n                    <linearGradient\n                      id=\"progressGradient\"\n                      x1=\"0%\"\n                      y1=\"0%\"\n                      x2=\"100%\"\n                      y2=\"100%\"\n                    >\n                      <stop offset=\"33.91%\" style=\"stop-color: #0084ff\" />\n                      <stop offset=\"100%\" style=\"stop-color: #03bdd4\" />\n                    </linearGradient>\n                  </defs>\n                  <circle\n                    class=\"progress-background\"\n                    cx=\"60\"\n                    cy=\"60\"\n                    r=\"50\"\n                    fill=\"none\"\n                    stroke=\"#e5e7eb\"\n                    stroke-width=\"8\"\n                  />\n                  <circle\n                    class=\"progress-bar\"\n                    cx=\"60\"\n                    cy=\"60\"\n                    r=\"50\"\n                    fill=\"none\"\n                    stroke=\"url(#progressGradient)\"\n                    stroke-width=\"8\"\n                    stroke-linecap=\"round\"\n                    [style.stroke-dasharray]=\"314\"\n                    [style.stroke-dashoffset]=\"\n                      314 - (314 * blueprintCompletionPercentage) / 100\n                    \"\n                    transform=\"rotate(180 60 60)\"\n                  />\n                </svg>\n                <div class=\"progress-content\">\n                  <div class=\"progress-percentage\">\n                    {{ blueprintCompletionPercentage }}%\n                  </div>\n                  <div class=\"progress-label\">Complete</div>\n                </div>\n              </div>\n            </div>\n\n            <!-- Blueprint Zones Layout -->\n            <div id=\"parent-box\">\n              <!-- <div id=\"box1\"> -->\n              <!-- System Prompt Zone -->\n              <div\n                class=\"blueprint-zone north-zone prompts-zone\"\n                [class.has-nodes]=\"blueprintPromptNodes.length > 0\"\n              >\n                <div\n                  class=\"zone-header\"\n                  (click)=\"toggleBlueprintZone('prompt')\"\n                >\n                  <div class=\"header-content\">\n                    <div class=\"header-icon\">\n                      <svg\n                        width=\"45\"\n                        height=\"44\"\n                        viewBox=\"0 0 45 44\"\n                        fill=\"none\"\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                      >\n                        <rect\n                          x=\"0.679688\"\n                          width=\"44\"\n                          height=\"44\"\n                          rx=\"8\"\n                          fill=\"#5082EF\"\n                        />\n                        <path\n                          d=\"M20.6166 25.5C20.5273 25.1539 20.347 24.8381 20.0942 24.5854C19.8415 24.3327 19.5257 24.1523 19.1796 24.063L13.0446 22.481C12.94 22.4513 12.8478 22.3883 12.7822 22.3014C12.7166 22.2146 12.6812 22.1088 12.6812 22C12.6812 21.8912 12.7166 21.7854 12.7822 21.6986C12.8478 21.6118 12.94 21.5487 13.0446 21.519L19.1796 19.936C19.5256 19.8468 19.8413 19.6666 20.094 19.414C20.3467 19.1615 20.5272 18.8459 20.6166 18.5L22.1986 12.365C22.228 12.2599 22.291 12.1673 22.3779 12.1014C22.4649 12.0355 22.571 11.9998 22.6801 11.9998C22.7892 11.9998 22.8954 12.0355 22.9823 12.1014C23.0692 12.1673 23.1322 12.2599 23.1616 12.365L24.7426 18.5C24.8319 18.8461 25.0123 19.1619 25.265 19.4146C25.5177 19.6673 25.8336 19.8477 26.1796 19.937L32.3146 21.518C32.4201 21.5471 32.5132 21.61 32.5795 21.6971C32.6458 21.7841 32.6817 21.8906 32.6817 22C32.6817 22.1094 32.6458 22.2159 32.5795 22.3029C32.5132 22.39 32.4201 22.4529 32.3146 22.482L26.1796 24.063C25.8336 24.1523 25.5177 24.3327 25.265 24.5854C25.0123 24.8381 24.8319 25.1539 24.7426 25.5L23.1606 31.635C23.1312 31.7401 23.0682 31.8327 22.9813 31.8986C22.8944 31.9646 22.7882 32.0003 22.6791 32.0003C22.57 32.0003 22.4639 31.9646 22.3769 31.8986C22.29 31.8327 22.227 31.7401 22.1976 31.635L20.6166 25.5Z\"\n                          stroke=\"white\"\n                          stroke-width=\"2\"\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                        />\n                      </svg>\n                    </div>\n                    <h3 class=\"zone-title\">System Prompt</h3>\n                  </div>\n                  <div class=\"header-actions\">\n                    <span class=\"required-badge\">Required</span>\n                    <button class=\"accordion-toggle\" type=\"button\">\n                      <svg\n                        width=\"16\"\n                        height=\"16\"\n                        viewBox=\"0 0 24 24\"\n                        fill=\"none\"\n                        [style.transform]=\"\n                          isBlueprintZoneExpanded('prompt')\n                            ? 'rotate(180deg)'\n                            : 'rotate(0deg)'\n                        \"\n                      >\n                        <path\n                          d=\"M6 9L12 15L18 9\"\n                          stroke=\"currentColor\"\n                          stroke-width=\"2\"\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                        />\n                      </svg>\n                    </button>\n                  </div>\n                </div>\n                <div\n                  class=\"zone-content\"\n                  *ngIf=\"isBlueprintZoneExpanded('prompt')\"\n                >\n                  <div\n                    *ngIf=\"blueprintPromptNodes.length === 0\"\n                    class=\"empty-state\"\n                  >\n                    No prompt configured\n                  </div>\n                  <div class=\"nodes-list\">\n                    <div\n                      *ngFor=\"let node of blueprintPromptNodes\"\n                      class=\"kanban-card\"\n                    >\n                      <span class=\"card-title\">{{\n                        node.name || \"Prompt\"\n                      }}</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Knowledge Base Zone -->\n              <div\n                class=\"blueprint-zone west-zone knowledge-zone\"\n                [class.has-nodes]=\"blueprintKnowledgeNodes.length > 0\"\n              >\n                <div\n                  class=\"zone-header\"\n                  (click)=\"toggleBlueprintZone('knowledge')\"\n                >\n                  <div class=\"header-content\">\n                    <div class=\"header-icon\">\n                      <svg\n                        width=\"45\"\n                        height=\"44\"\n                        viewBox=\"0 0 45 44\"\n                        fill=\"none\"\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                      >\n                        <rect\n                          x=\"0.679688\"\n                          width=\"44\"\n                          height=\"44\"\n                          rx=\"8\"\n                          fill=\"#308666\"\n                        />\n                        <path\n                          d=\"M22.6797 17V31\"\n                          stroke=\"white\"\n                          stroke-width=\"2\"\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                        />\n                        <path\n                          d=\"M26.6797 22H28.6797\"\n                          stroke=\"white\"\n                          stroke-width=\"2\"\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                        />\n                        <path\n                          d=\"M26.6797 18H28.6797\"\n                          stroke=\"white\"\n                          stroke-width=\"2\"\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                        />\n                        <path\n                          d=\"M13.6797 28C13.4145 28 13.1601 27.8946 12.9726 27.7071C12.785 27.5196 12.6797 27.2652 12.6797 27V14C12.6797 13.7348 12.785 13.4804 12.9726 13.2929C13.1601 13.1054 13.4145 13 13.6797 13H18.6797C19.7406 13 20.758 13.4214 21.5081 14.1716C22.2583 14.9217 22.6797 15.9391 22.6797 17C22.6797 15.9391 23.1011 14.9217 23.8513 14.1716C24.6014 13.4214 25.6188 13 26.6797 13H31.6797C31.9449 13 32.1993 13.1054 32.3868 13.2929C32.5743 13.4804 32.6797 13.7348 32.6797 14V27C32.6797 27.2652 32.5743 27.5196 32.3868 27.7071C32.1993 27.8946 31.9449 28 31.6797 28H25.6797C24.884 28 24.121 28.3161 23.5584 28.8787C22.9958 29.4413 22.6797 30.2044 22.6797 31C22.6797 30.2044 22.3636 29.4413 21.801 28.8787C21.2384 28.3161 20.4753 28 19.6797 28H13.6797Z\"\n                          stroke=\"white\"\n                          stroke-width=\"2\"\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                        />\n                        <path\n                          d=\"M16.6797 22H18.6797\"\n                          stroke=\"white\"\n                          stroke-width=\"2\"\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                        />\n                        <path\n                          d=\"M16.6797 18H18.6797\"\n                          stroke=\"white\"\n                          stroke-width=\"2\"\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                        />\n                      </svg>\n                    </div>\n                    <h3 class=\"zone-title\">Knowledgebase</h3>\n                  </div>\n                  <div class=\"header-actions\">\n                    <span class=\"optional-badge\">Optional</span>\n                    <button class=\"accordion-toggle\" type=\"button\">\n                      <svg\n                        width=\"16\"\n                        height=\"16\"\n                        viewBox=\"0 0 24 24\"\n                        fill=\"none\"\n                        [style.transform]=\"\n                          isBlueprintZoneExpanded('knowledge')\n                            ? 'rotate(180deg)'\n                            : 'rotate(0deg)'\n                        \"\n                      >\n                        <path\n                          d=\"M6 9L12 15L18 9\"\n                          stroke=\"currentColor\"\n                          stroke-width=\"2\"\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                        />\n                      </svg>\n                    </button>\n                  </div>\n                </div>\n                <div\n                  class=\"zone-content\"\n                  *ngIf=\"isBlueprintZoneExpanded('knowledge')\"\n                >\n                  <div\n                    *ngIf=\"blueprintKnowledgeNodes.length === 0\"\n                    class=\"empty-state\"\n                  >\n                    No knowledge base configured\n                  </div>\n                  <div class=\"nodes-list\">\n                    <div\n                      *ngFor=\"let node of blueprintKnowledgeNodes\"\n                      class=\"kanban-card\"\n                    >\n                      <span class=\"card-title\">{{\n                        node.name || \"Knowledge Base\"\n                      }}</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n              <!-- </div> -->\n\n              <!-- <div id=\"box2\"> -->\n              <!-- AI Model Zone -->\n              <div\n                class=\"blueprint-zone east-zone models-zone\"\n                [class.has-nodes]=\"blueprintModelNodes.length > 0\"\n              >\n                <div class=\"zone-header\" (click)=\"toggleBlueprintZone('model')\">\n                  <div class=\"header-content\">\n                    <div class=\"header-icon\">\n                      <svg\n                        width=\"45\"\n                        height=\"44\"\n                        viewBox=\"0 0 45 44\"\n                        fill=\"none\"\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                      >\n                        <rect\n                          x=\"0.679688\"\n                          width=\"44\"\n                          height=\"44\"\n                          rx=\"8\"\n                          fill=\"#997BCF\"\n                        />\n                        <path\n                          d=\"M31.6797 18C31.6793 17.6493 31.5867 17.3048 31.4112 17.0012C31.2357 16.6975 30.9834 16.4454 30.6797 16.27L23.6797 12.27C23.3756 12.0945 23.0308 12.0021 22.6797 12.0021C22.3286 12.0021 21.9837 12.0945 21.6797 12.27L14.6797 16.27C14.376 16.4454 14.1237 16.6975 13.9481 17.0012C13.7726 17.3048 13.68 17.6493 13.6797 18V26C13.68 26.3508 13.7726 26.6952 13.9481 26.9989C14.1237 27.3025 14.376 27.5547 14.6797 27.73L21.6797 31.73C21.9837 31.9056 22.3286 31.998 22.6797 31.998C23.0308 31.998 23.3756 31.9056 23.6797 31.73L30.6797 27.73C30.9834 27.5547 31.2357 27.3025 31.4112 26.9989C31.5867 26.6952 31.6793 26.3508 31.6797 26V18Z\"\n                          stroke=\"white\"\n                          stroke-width=\"2\"\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                        />\n                        <path\n                          d=\"M13.9795 17L22.6795 22L31.3795 17\"\n                          stroke=\"white\"\n                          stroke-width=\"2\"\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                        />\n                        <path\n                          d=\"M22.6797 32V22\"\n                          stroke=\"white\"\n                          stroke-width=\"2\"\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                        />\n                      </svg>\n                    </div>\n                    <h3 class=\"zone-title\">AI Model</h3>\n                  </div>\n                  <div class=\"header-actions\">\n                    <span class=\"required-badge\">Required</span>\n                    <button class=\"accordion-toggle\" type=\"button\">\n                      <svg\n                        width=\"16\"\n                        height=\"16\"\n                        viewBox=\"0 0 24 24\"\n                        fill=\"none\"\n                        [style.transform]=\"\n                          isBlueprintZoneExpanded('model')\n                            ? 'rotate(180deg)'\n                            : 'rotate(0deg)'\n                        \"\n                      >\n                        <path\n                          d=\"M6 9L12 15L18 9\"\n                          stroke=\"currentColor\"\n                          stroke-width=\"2\"\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                        />\n                      </svg>\n                    </button>\n                  </div>\n                </div>\n                <div\n                  class=\"zone-content\"\n                  *ngIf=\"isBlueprintZoneExpanded('model')\"\n                >\n                  <div\n                    *ngIf=\"blueprintModelNodes.length === 0\"\n                    class=\"empty-state\"\n                  >\n                    No model configured\n                  </div>\n                  <div class=\"nodes-list\">\n                    <div\n                      *ngFor=\"let node of blueprintModelNodes\"\n                      class=\"kanban-card\"\n                    >\n                      <span class=\"card-title\">{{ node.name || \"Model\" }}</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Tools Zone (for Collaborative Agents) -->\n              <div\n                *ngIf=\"agentType === 'collaborative'\"\n                class=\"blueprint-zone south-zone tools-zone\"\n                [class.has-nodes]=\"blueprintToolNodes.length > 0\"\n              >\n                <div class=\"zone-header\" (click)=\"toggleBlueprintZone('tool')\">\n                  <div class=\"header-content\">\n                    <div class=\"header-icon\">\n                      <svg\n                        width=\"45\"\n                        height=\"44\"\n                        viewBox=\"0 0 45 44\"\n                        fill=\"none\"\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        *ngIf=\"agentType === 'collaborative'\"\n                      >\n                        <rect\n                          x=\"0.679688\"\n                          width=\"44\"\n                          height=\"44\"\n                          rx=\"8\"\n                          fill=\"#D97706\"\n                        />\n                        <path\n                          d=\"M25.3795 16.3C25.1963 16.4869 25.0937 16.7382 25.0937 17C25.0937 17.2617 25.1963 17.5131 25.3795 17.7L26.9795 19.3C27.1665 19.4832 27.4178 19.5858 27.6795 19.5858C27.9413 19.5858 28.1926 19.4832 28.3795 19.3L32.1495 15.53C32.6524 16.6412 32.8046 17.8792 32.586 19.0791C32.3674 20.279 31.7883 21.3838 30.9258 22.2463C30.0634 23.1087 28.9586 23.6878 27.7587 23.9064C26.5588 24.1251 25.3207 23.9728 24.2095 23.47L17.2995 30.38C16.9017 30.7778 16.3622 31.0013 15.7995 31.0013C15.2369 31.0013 14.6974 30.7778 14.2995 30.38C13.9017 29.9822 13.6782 29.4426 13.6782 28.88C13.6782 28.3174 13.9017 27.7778 14.2995 27.38L21.2095 20.47C20.7067 19.3588 20.5545 18.1207 20.7731 16.9208C20.9917 15.7209 21.5708 14.6161 22.4333 13.7537C23.2957 12.8913 24.4005 12.3121 25.6004 12.0935C26.8003 11.8749 28.0384 12.0271 29.1495 12.53L25.3895 16.29L25.3795 16.3Z\"\n                          stroke=\"white\"\n                          stroke-width=\"2\"\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                        />\n                      </svg>\n                    </div>\n                    <h3 class=\"zone-title\">Tools</h3>\n                  </div>\n                  <div class=\"header-actions\">\n                    <span class=\"optional-badge\">Optional</span>\n                    <button class=\"accordion-toggle\" type=\"button\">\n                      <svg\n                        width=\"16\"\n                        height=\"16\"\n                        viewBox=\"0 0 24 24\"\n                        fill=\"none\"\n                        [style.transform]=\"\n                          isBlueprintZoneExpanded('tool')\n                            ? 'rotate(180deg)'\n                            : 'rotate(0deg)'\n                        \"\n                      >\n                        <path\n                          d=\"M6 9L12 15L18 9\"\n                          stroke=\"currentColor\"\n                          stroke-width=\"2\"\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                        />\n                      </svg>\n                    </button>\n                  </div>\n                </div>\n                <div\n                  class=\"zone-content\"\n                  *ngIf=\"isBlueprintZoneExpanded('tool')\"\n                >\n                  <div\n                    *ngIf=\"blueprintToolNodes.length === 0\"\n                    class=\"empty-state\"\n                  >\n                    No tools configured\n                  </div>\n                  <div class=\"nodes-list\">\n                    <div\n                      *ngFor=\"let node of blueprintToolNodes\"\n                      class=\"kanban-card\"\n                    >\n                      <span class=\"card-title\">{{ node.name || \"Tool\" }}</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Guardrails Zone (for Individual Agents) -->\n              <div\n                *ngIf=\"agentType === 'individual'\"\n                class=\"blueprint-zone south-zone guardrails-zone\"\n                [class.has-nodes]=\"blueprintGuardrailNodes.length > 0\"\n              >\n                <div\n                  class=\"zone-header\"\n                  (click)=\"toggleBlueprintZone('guardrail')\"\n                >\n                  <div class=\"header-content\">\n                    <div class=\"header-icon\">\n                      <svg\n                        width=\"45\"\n                        height=\"44\"\n                        viewBox=\"0 0 45 44\"\n                        fill=\"none\"\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                      >\n                        <rect\n                          x=\"0.679688\"\n                          width=\"44\"\n                          height=\"44\"\n                          rx=\"8\"\n                          fill=\"#DC2626\"\n                        />\n                        <path\n                          d=\"M30.6797 23C30.6797 28 27.1797 30.5 23.0197 31.95C22.8019 32.0238 22.5652 32.0202 22.3497 31.94C18.1797 30.5 14.6797 28 14.6797 23V16C14.6797 15.7347 14.785 15.4804 14.9726 15.2929C15.1601 15.1053 15.4145 15 15.6797 15C17.6797 15 20.1797 13.8 21.9197 12.28C22.1315 12.099 22.401 11.9995 22.6797 11.9995C22.9583 11.9995 23.2278 12.099 23.4397 12.28C25.1897 13.81 27.6797 15 29.6797 15C29.9449 15 30.1993 15.1053 30.3868 15.2929C30.5743 15.4804 30.6797 15.7347 30.6797 16V23Z\"\n                          stroke=\"white\"\n                          stroke-width=\"2\"\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                        />\n                      </svg>\n                    </div>\n                    <h3 class=\"zone-title\">Guardrails</h3>\n                  </div>\n                  <div class=\"header-actions\">\n                    <span class=\"optional-badge\">Optional</span>\n                    <button class=\"accordion-toggle\" type=\"button\">\n                      <svg\n                        width=\"16\"\n                        height=\"16\"\n                        viewBox=\"0 0 24 24\"\n                        fill=\"none\"\n                        [style.transform]=\"\n                          isBlueprintZoneExpanded('guardrail')\n                            ? 'rotate(180deg)'\n                            : 'rotate(0deg)'\n                        \"\n                      >\n                        <path\n                          d=\"M6 9L12 15L18 9\"\n                          stroke=\"currentColor\"\n                          stroke-width=\"2\"\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                        />\n                      </svg>\n                    </button>\n                  </div>\n                </div>\n                <div\n                  class=\"zone-content\"\n                  *ngIf=\"isBlueprintZoneExpanded('guardrail')\"\n                >\n                  <div\n                    *ngIf=\"blueprintGuardrailNodes.length === 0\"\n                    class=\"empty-state\"\n                  >\n                    No guardrails configured\n                  </div>\n                  <div class=\"nodes-list\">\n                    <div\n                      *ngFor=\"let node of blueprintGuardrailNodes\"\n                      class=\"kanban-card\"\n                    >\n                      <span class=\"card-title\">{{\n                        node.name || \"Guardrail\"\n                      }}</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n              <!-- </div> -->\n            </div>\n          </div>\n        </div>\n\n        <!-- Agent Output Content -->\n        <div *ngIf=\"activeRightTab === 'output'\" class=\"output-content\">\n          <h3>Agent Output</h3>\n          \n          <!-- Execution History with Scroll -->\n          <div class=\"execution-history-container\">\n            <div *ngIf=\"executionHistory.length === 0\" class=\"no-output-message\">\n              <p>No agent outputs available yet. Send a message to see the response.</p>\n            </div>\n            \n            <div *ngIf=\"executionHistory.length > 0\" class=\"execution-history-list\">\n              <div \n                *ngFor=\"let execution of executionHistory; let i = index\" \n                class=\"execution-item\"\n                [class.success]=\"execution.status === 'success'\"\n                [class.failed]=\"execution.status === 'failed'\"\n              >\n                <div class=\"execution-header\">\n                  <div class=\"agent-name\">{{ execution.agentName }}</div>\n                  <div class=\"execution-status\" [class]=\"execution.status\">\n                    {{ execution.status === 'success' ? 'Success' : 'Failed' }}\n                  </div>\n                </div>\n                \n                <div class=\"execution-details\">\n                  <div class=\"user-message\">\n                    <strong>Query:</strong> {{ execution.userMessage }}\n                  </div>\n                  \n                  <div class=\"execution-timestamp\">\n                    {{ execution.timestamp | date:'medium' }}\n                  </div>\n                </div>\n                \n                <!-- Individual Agent Response -->\n                <div *ngIf=\"execution.status === 'success' && execution.response && agentType === 'individual'\" class=\"execution-response\">\n                  <div class=\"response-section\">\n                    <h4>Response</h4>\n                    <div class=\"response-text\">\n                      {{ execution.response?.response?.choices?.[0]?.text || execution.response?.agentResponse?.detail || 'No response content available.' }}\n                    </div>\n                  </div>\n                </div>\n                \n                <!-- Collaborative Agent Response -->\n                <div *ngIf=\"execution.status === 'success' && execution.response && agentType === 'collaborative'\" class=\"execution-response\">\n                  <div \n                    *ngFor=\"let taskOutput of execution.response?.agentResponse?.agent?.tasksOutputs; let j = index\"\n                    class=\"task-output\"\n                  >\n                    <div *ngIf=\"taskOutput.description\" class=\"task-description\">\n                      <strong>Description:</strong> {{ taskOutput.description }}\n                    </div>\n                    <div *ngIf=\"taskOutput.expected_output\" class=\"task-expected\">\n                      <strong>Expected Output:</strong> {{ taskOutput.expected_output }}\n                    </div>\n                    <div class=\"task-content\">\n                      {{ taskOutput.raw }}\n                    </div>\n                    <div class=\"task-header\">\n                      <h4>Summary: {{ taskOutput.summary || 'Task ' + (j + 1) }}</h4>\n                    </div>\n                  </div>\n                </div>\n                \n                <!-- Failed Response -->\n                <div *ngIf=\"execution.status === 'failed'\" class=\"execution-error\">\n                  <div class=\"error-message\">\n                    <strong>Error:</strong> The agent execution failed. Please try again.\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,OAAO,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,EAAE,QAAQ,MAAM;AAC9E,SAAiCC,WAAW,QAAQ,gBAAgB;AAEpE;AACA,SAASC,iCAAiC,QAAQ,8EAA8E;AAEhI,SAASC,aAAa,QAAiC,wBAAwB;AAG/E,SAASC,WAAW,QAAQ,kCAAkC;AAK9D,SAASC,eAAe,QAAmD,gCAAgC;;;;;;;;;;;;ICWnGC,EAAA,CAAAC,cAAA,iBAKC;IACCD,EAAA,CAAAE,MAAA,gBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAITH,EADF,CAAAC,cAAA,cAAyD,yCAoBtD;IADCD,EALA,CAAAI,UAAA,0BAAAC,+FAAAC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAgBF,MAAA,CAAAG,eAAA,CAAAN,MAAA,CAAuB;IAAA,EAAC,yBAAAO,8FAAAP,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CACzBF,MAAA,CAAAK,iBAAA,CAAAR,MAAA,CAAyB;IAAA,EAAC,kCAAAS,uGAAAT,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CACjBF,MAAA,CAAAO,gCAAA,CAAAV,MAAA,CAAwC;IAAA,EAAC,4BAAAW,iGAAAX,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAC/CF,MAAA,CAAAS,0BAAA,CAAAZ,MAAA,CAAkC;IAAA,EAAC,2BAAAa,gGAAAb,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CACpCF,MAAA,CAAAW,eAAA,CAAAd,MAAA,CAAuB;IAAA,EAAC,+BAAAe,oGAAA;MAAArB,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CACpBF,MAAA,CAAAa,mBAAA,EAAqB;IAAA,EAAC;IAG/CtB,EADE,CAAAG,YAAA,EAAiC,EAC7B;;;;IApBFH,EAAA,CAAAuB,SAAA,EAAyB;IAWzBvB,EAXA,CAAAwB,UAAA,aAAAf,MAAA,CAAAgB,YAAA,CAAyB,cAAAhB,MAAA,CAAAiB,gBAAA,CACK,cAAAjB,MAAA,CAAAkB,SAAA,CACP,+BAAAlB,MAAA,CAAAkB,SAAA,kBACkC,gCAC1B,6BACH,uBACN,4BACK,8BACE,wBACN,uBAAAlB,MAAA,CAAAmB,SAAA,CACS,8CACa;;;;;IAgNrC5B,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAMFH,EAJF,CAAAC,cAAA,cAGC,eAC0B;IAAAD,EAAA,CAAAE,MAAA,GAEvB;IACJF,EADI,CAAAG,YAAA,EAAO,EACL;;;;IAHqBH,EAAA,CAAAuB,SAAA,GAEvB;IAFuBvB,EAAA,CAAA6B,iBAAA,CAAAC,OAAA,CAAAC,IAAA,aAEvB;;;;;IAjBR/B,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAgC,UAAA,IAAAC,oDAAA,kBAGC;IAGDjC,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAgC,UAAA,IAAAE,oDAAA,kBAGC;IAMLlC,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAfDH,EAAA,CAAAuB,SAAA,EAAuC;IAAvCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAA0B,oBAAA,CAAAC,MAAA,OAAuC;IAOrBpC,EAAA,CAAAuB,SAAA,GAAuB;IAAvBvB,EAAA,CAAAwB,UAAA,YAAAf,MAAA,CAAA0B,oBAAA,CAAuB;;;;;IA+G5CnC,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,MAAA,qCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAMFH,EAJF,CAAAC,cAAA,cAGC,eAC0B;IAAAD,EAAA,CAAAE,MAAA,GAEvB;IACJF,EADI,CAAAG,YAAA,EAAO,EACL;;;;IAHqBH,EAAA,CAAAuB,SAAA,GAEvB;IAFuBvB,EAAA,CAAA6B,iBAAA,CAAAQ,OAAA,CAAAN,IAAA,qBAEvB;;;;;IAjBR/B,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAgC,UAAA,IAAAM,oDAAA,kBAGC;IAGDtC,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAgC,UAAA,IAAAO,oDAAA,kBAGC;IAMLvC,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAfDH,EAAA,CAAAuB,SAAA,EAA0C;IAA1CvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAA+B,uBAAA,CAAAJ,MAAA,OAA0C;IAOxBpC,EAAA,CAAAuB,SAAA,GAA0B;IAA1BvB,EAAA,CAAAwB,UAAA,YAAAf,MAAA,CAAA+B,uBAAA,CAA0B;;;;;IAyF/CxC,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAMFH,EAJF,CAAAC,cAAA,cAGC,eAC0B;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IACrDF,EADqD,CAAAG,YAAA,EAAO,EACtD;;;;IADqBH,EAAA,CAAAuB,SAAA,GAA0B;IAA1BvB,EAAA,CAAA6B,iBAAA,CAAAY,OAAA,CAAAV,IAAA,YAA0B;;;;;IAfzD/B,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAgC,UAAA,IAAAU,oDAAA,kBAGC;IAGD1C,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAgC,UAAA,IAAAW,oDAAA,kBAGC;IAIL3C,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAbDH,EAAA,CAAAuB,SAAA,EAAsC;IAAtCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAmC,mBAAA,CAAAR,MAAA,OAAsC;IAOpBpC,EAAA,CAAAuB,SAAA,GAAsB;IAAtBvB,EAAA,CAAAwB,UAAA,YAAAf,MAAA,CAAAmC,mBAAA,CAAsB;;;;;;IAkBvC5C,EAAA,CAAAC,cAAA,cAOC;IAQCD,EAPA,CAAA6C,SAAA,eAME,eAOA;IACJ7C,EAAA,CAAAG,YAAA,EAAM;;;;;IAiCVH,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAMFH,EAJF,CAAAC,cAAA,cAGC,eAC0B;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IACpDF,EADoD,CAAAG,YAAA,EAAO,EACrD;;;;IADqBH,EAAA,CAAAuB,SAAA,GAAyB;IAAzBvB,EAAA,CAAA6B,iBAAA,CAAAiB,OAAA,CAAAf,IAAA,WAAyB;;;;;IAfxD/B,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAgC,UAAA,IAAAe,2DAAA,kBAGC;IAGD/C,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAgC,UAAA,IAAAgB,2DAAA,kBAGC;IAILhD,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAbDH,EAAA,CAAAuB,SAAA,EAAqC;IAArCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAwC,kBAAA,CAAAb,MAAA,OAAqC;IAOnBpC,EAAA,CAAAuB,SAAA,GAAqB;IAArBvB,EAAA,CAAAwB,UAAA,YAAAf,MAAA,CAAAwC,kBAAA,CAAqB;;;;;;IAlE5CjD,EALF,CAAAC,cAAA,cAIC,cACgE;IAAtCD,EAAA,CAAAI,UAAA,mBAAA8C,oEAAA;MAAAlD,EAAA,CAAAO,aAAA,CAAA4C,GAAA;MAAA,MAAA1C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA2C,mBAAA,CAAoB,MAAM,CAAC;IAAA,EAAC;IAE1DpD,EADF,CAAAC,cAAA,cAA4B,cACD;IACvBD,EAAA,CAAAgC,UAAA,IAAAqB,yDAAA,kBAOC;IAgBHrD,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAC9BF,EAD8B,CAAAG,YAAA,EAAK,EAC7B;IAEJH,EADF,CAAAC,cAAA,cAA4B,eACG;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,kBAA+C;;IAC7CD,EAAA,CAAAC,cAAA,eAUC;IACCD,EAAA,CAAA6C,SAAA,gBAME;IAIV7C,EAHM,CAAAG,YAAA,EAAM,EACC,EACL,EACF;IACNH,EAAA,CAAAgC,UAAA,KAAAsB,qDAAA,kBAGC;IAgBHtD,EAAA,CAAAG,YAAA,EAAM;;;;IA3EJH,EAAA,CAAAuD,WAAA,cAAA9C,MAAA,CAAAwC,kBAAA,CAAAb,MAAA,KAAiD;IAWxCpC,EAAA,CAAAuB,SAAA,GAAmC;IAAnCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAkB,SAAA,qBAAmC;IA4BpC3B,EAAA,CAAAuB,SAAA,GAIC;IAJDvB,EAAA,CAAAwD,WAAA,cAAA/C,MAAA,CAAAgD,uBAAA,6CAIC;IAeNzD,EAAA,CAAAuB,SAAA,GAAqC;IAArCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAgD,uBAAA,SAAqC;;;;;IAqFtCzD,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAMFH,EAJF,CAAAC,cAAA,cAGC,eAC0B;IAAAD,EAAA,CAAAE,MAAA,GAEvB;IACJF,EADI,CAAAG,YAAA,EAAO,EACL;;;;IAHqBH,EAAA,CAAAuB,SAAA,GAEvB;IAFuBvB,EAAA,CAAA6B,iBAAA,CAAA6B,QAAA,CAAA3B,IAAA,gBAEvB;;;;;IAjBR/B,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAgC,UAAA,IAAA2B,2DAAA,kBAGC;IAGD3D,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAgC,UAAA,IAAA4B,2DAAA,kBAGC;IAML5D,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAfDH,EAAA,CAAAuB,SAAA,EAA0C;IAA1CvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAoD,uBAAA,CAAAzB,MAAA,OAA0C;IAOxBpC,EAAA,CAAAuB,SAAA,GAA0B;IAA1BvB,EAAA,CAAAwB,UAAA,YAAAf,MAAA,CAAAoD,uBAAA,CAA0B;;;;;;IApEjD7D,EALF,CAAAC,cAAA,cAIC,cAIE;IADCD,EAAA,CAAAI,UAAA,mBAAA0D,oEAAA;MAAA9D,EAAA,CAAAO,aAAA,CAAAwD,GAAA;MAAA,MAAAtD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA2C,mBAAA,CAAoB,WAAW,CAAC;IAAA,EAAC;IAGxCpD,EADF,CAAAC,cAAA,cAA4B,cACD;;IACvBD,EAAA,CAAAC,cAAA,cAMC;IAQCD,EAPA,CAAA6C,SAAA,eAME,eAOA;IAEN7C,EADE,CAAAG,YAAA,EAAM,EACF;;IACNH,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IACnCF,EADmC,CAAAG,YAAA,EAAK,EAClC;IAEJH,EADF,CAAAC,cAAA,cAA4B,gBACG;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,kBAA+C;;IAC7CD,EAAA,CAAAC,cAAA,eAUC;IACCD,EAAA,CAAA6C,SAAA,gBAME;IAIV7C,EAHM,CAAAG,YAAA,EAAM,EACC,EACL,EACF;IACNH,EAAA,CAAAgC,UAAA,KAAAgC,qDAAA,kBAGC;IAkBHhE,EAAA,CAAAG,YAAA,EAAM;;;;IA/EJH,EAAA,CAAAuD,WAAA,cAAA9C,MAAA,CAAAoD,uBAAA,CAAAzB,MAAA,KAAsD;IAyC9CpC,EAAA,CAAAuB,SAAA,IAIC;IAJDvB,EAAA,CAAAwD,WAAA,cAAA/C,MAAA,CAAAgD,uBAAA,kDAIC;IAeNzD,EAAA,CAAAuB,SAAA,GAA0C;IAA1CvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAgD,uBAAA,cAA0C;;;;;;IApiBjDzD,EAFJ,CAAAC,cAAA,cAAsE,cACtC,SACxB;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IACrBF,EADqB,CAAAG,YAAA,EAAK,EACpB;IAGNH,EAAA,CAAAC,cAAA,cAAwC;;IAEtCD,EAAA,CAAAC,cAAA,cAIC;IAgCCD,EA9BA,CAAA6C,SAAA,eAOE,eAUA,eAUA,eAUA;IACJ7C,EAAA,CAAAG,YAAA,EAAM;;IAIJH,EADF,CAAAC,cAAA,eAA8B,eACD;;IAGrBD,EAFJ,CAAAC,cAAA,eAAsD,YAC9C,0BAOH;IAECD,EADA,CAAA6C,SAAA,gBAAoD,gBACF;IAEtD7C,EADE,CAAAG,YAAA,EAAiB,EACZ;IAUPH,EATA,CAAA6C,SAAA,kBAQE,kBAeA;IACJ7C,EAAA,CAAAG,YAAA,EAAM;;IAEJH,EADF,CAAAC,cAAA,eAA8B,eACK;IAC/BD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAG1CF,EAH0C,CAAAG,YAAA,EAAM,EACtC,EACF,EACF;IAUFH,EAPJ,CAAAC,cAAA,eAAqB,eAMlB,eAIE;IADCD,EAAA,CAAAI,UAAA,mBAAA6D,8DAAA;MAAAjE,EAAA,CAAAO,aAAA,CAAA2D,GAAA;MAAA,MAAAzD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA2C,mBAAA,CAAoB,QAAQ,CAAC;IAAA,EAAC;IAGrCpD,EADF,CAAAC,cAAA,eAA4B,eACD;;IACvBD,EAAA,CAAAC,cAAA,eAMC;IAQCD,EAPA,CAAA6C,SAAA,gBAME,gBAOA;IAEN7C,EADE,CAAAG,YAAA,EAAM,EACF;;IACNH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IACtCF,EADsC,CAAAG,YAAA,EAAK,EACrC;IAEJH,EADF,CAAAC,cAAA,eAA4B,gBACG;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,kBAA+C;;IAC7CD,EAAA,CAAAC,cAAA,eAUC;IACCD,EAAA,CAAA6C,SAAA,gBAME;IAIV7C,EAHM,CAAAG,YAAA,EAAM,EACC,EACL,EACF;IACNH,EAAA,CAAAgC,UAAA,KAAAmC,8CAAA,kBAGC;IAkBHnE,EAAA,CAAAG,YAAA,EAAM;;IAOJH,EAJF,CAAAC,cAAA,eAGC,eAIE;IADCD,EAAA,CAAAI,UAAA,mBAAAgE,8DAAA;MAAApE,EAAA,CAAAO,aAAA,CAAA2D,GAAA;MAAA,MAAAzD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA2C,mBAAA,CAAoB,WAAW,CAAC;IAAA,EAAC;IAGxCpD,EADF,CAAAC,cAAA,eAA4B,eACD;;IACvBD,EAAA,CAAAC,cAAA,eAMC;IA2CCD,EA1CA,CAAA6C,SAAA,gBAME,gBAOA,gBAOA,gBAOA,gBAOA,gBAOA,gBAOA;IAEN7C,EADE,CAAAG,YAAA,EAAM,EACF;;IACNH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IACtCF,EADsC,CAAAG,YAAA,EAAK,EACrC;IAEJH,EADF,CAAAC,cAAA,eAA4B,gBACG;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,kBAA+C;;IAC7CD,EAAA,CAAAC,cAAA,eAUC;IACCD,EAAA,CAAA6C,SAAA,gBAME;IAIV7C,EAHM,CAAAG,YAAA,EAAM,EACC,EACL,EACF;IACNH,EAAA,CAAAgC,UAAA,KAAAqC,8CAAA,kBAGC;IAkBHrE,EAAA,CAAAG,YAAA,EAAM;;IASJH,EAJF,CAAAC,cAAA,eAGC,eACiE;IAAvCD,EAAA,CAAAI,UAAA,mBAAAkE,8DAAA;MAAAtE,EAAA,CAAAO,aAAA,CAAA2D,GAAA;MAAA,MAAAzD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA2C,mBAAA,CAAoB,OAAO,CAAC;IAAA,EAAC;IAE3DpD,EADF,CAAAC,cAAA,eAA4B,eACD;;IACvBD,EAAA,CAAAC,cAAA,eAMC;IAsBCD,EArBA,CAAA6C,SAAA,gBAME,gBAOA,gBAOA,gBAOA;IAEN7C,EADE,CAAAG,YAAA,EAAM,EACF;;IACNH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IACjCF,EADiC,CAAAG,YAAA,EAAK,EAChC;IAEJH,EADF,CAAAC,cAAA,eAA4B,gBACG;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,kBAA+C;;IAC7CD,EAAA,CAAAC,cAAA,eAUC;IACCD,EAAA,CAAA6C,SAAA,gBAME;IAIV7C,EAHM,CAAAG,YAAA,EAAM,EACC,EACL,EACF;IACNH,EAAA,CAAAgC,UAAA,KAAAuC,8CAAA,kBAGC;IAgBHvE,EAAA,CAAAG,YAAA,EAAM;IAoFNH,EAjFA,CAAAgC,UAAA,KAAAwC,8CAAA,mBAIC,KAAAC,8CAAA,mBAiFA;IAkFPzE,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IAreMH,EAAA,CAAAuB,SAAA,IAA8B;IAC9BvB,EADA,CAAAwD,WAAA,yBAA8B,kCAAA/C,MAAA,CAAAiE,6BAAA,OAG7B;IAMD1E,EAAA,CAAAuB,SAAA,GACF;IADEvB,EAAA,CAAA2E,kBAAA,MAAAlE,MAAA,CAAAiE,6BAAA,OACF;IAYF1E,EAAA,CAAAuB,SAAA,GAAmD;IAAnDvB,EAAA,CAAAuD,WAAA,cAAA9C,MAAA,CAAA0B,oBAAA,CAAAC,MAAA,KAAmD;IAyC3CpC,EAAA,CAAAuB,SAAA,IAIC;IAJDvB,EAAA,CAAAwD,WAAA,cAAA/C,MAAA,CAAAgD,uBAAA,+CAIC;IAeNzD,EAAA,CAAAuB,SAAA,GAAuC;IAAvCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAgD,uBAAA,WAAuC;IAwB1CzD,EAAA,CAAAuB,SAAA,EAAsD;IAAtDvB,EAAA,CAAAuD,WAAA,cAAA9C,MAAA,CAAA+B,uBAAA,CAAAJ,MAAA,KAAsD;IA4E9CpC,EAAA,CAAAuB,SAAA,IAIC;IAJDvB,EAAA,CAAAwD,WAAA,cAAA/C,MAAA,CAAAgD,uBAAA,kDAIC;IAeNzD,EAAA,CAAAuB,SAAA,GAA0C;IAA1CvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAgD,uBAAA,cAA0C;IA0B7CzD,EAAA,CAAAuB,SAAA,EAAkD;IAAlDvB,EAAA,CAAAuD,WAAA,cAAA9C,MAAA,CAAAmC,mBAAA,CAAAR,MAAA,KAAkD;IAoD1CpC,EAAA,CAAAuB,SAAA,IAIC;IAJDvB,EAAA,CAAAwD,WAAA,cAAA/C,MAAA,CAAAgD,uBAAA,8CAIC;IAeNzD,EAAA,CAAAuB,SAAA,GAAsC;IAAtCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAgD,uBAAA,UAAsC;IAqBxCzD,EAAA,CAAAuB,SAAA,EAAmC;IAAnCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAkB,SAAA,qBAAmC;IAiFnC3B,EAAA,CAAAuB,SAAA,EAAgC;IAAhCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAkB,SAAA,kBAAgC;;;;;IA8FnC3B,EADF,CAAAC,cAAA,cAAqE,QAChE;IAAAD,EAAA,CAAAE,MAAA,0EAAmE;IACxEF,EADwE,CAAAG,YAAA,EAAI,EACtE;;;;;IA6BEH,EAFJ,CAAAC,cAAA,eAA2H,eAC3F,SACxB;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAAE,MAAA,GACF;IAEJF,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IAHAH,EAAA,CAAAuB,SAAA,GACF;IADEvB,EAAA,CAAA2E,kBAAA,OAAAC,aAAA,CAAAC,QAAA,kBAAAD,aAAA,CAAAC,QAAA,CAAAA,QAAA,kBAAAD,aAAA,CAAAC,QAAA,CAAAA,QAAA,CAAAC,OAAA,kBAAAF,aAAA,CAAAC,QAAA,CAAAA,QAAA,CAAAC,OAAA,qBAAAF,aAAA,CAAAC,QAAA,CAAAA,QAAA,CAAAC,OAAA,IAAAC,IAAA,MAAAH,aAAA,CAAAC,QAAA,kBAAAD,aAAA,CAAAC,QAAA,CAAAG,aAAA,kBAAAJ,aAAA,CAAAC,QAAA,CAAAG,aAAA,CAAAC,MAAA,2CACF;;;;;IAWEjF,EADF,CAAAC,cAAA,eAA6D,aACnD;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,GAChC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAD0BH,EAAA,CAAAuB,SAAA,GAChC;IADgCvB,EAAA,CAAA2E,kBAAA,MAAAO,cAAA,CAAAC,WAAA,MAChC;;;;;IAEEnF,EADF,CAAAC,cAAA,eAA8D,aACpD;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,GACpC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAD8BH,EAAA,CAAAuB,SAAA,GACpC;IADoCvB,EAAA,CAAA2E,kBAAA,MAAAO,cAAA,CAAAE,eAAA,MACpC;;;;;IATFpF,EAAA,CAAAC,cAAA,eAGC;IAICD,EAHA,CAAAgC,UAAA,IAAAqD,sEAAA,mBAA6D,IAAAC,sEAAA,mBAGC;IAG9DtF,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAyB,SACnB;IAAAD,EAAA,CAAAE,MAAA,GAAsD;IAE9DF,EAF8D,CAAAG,YAAA,EAAK,EAC3D,EACF;;;;;IAZEH,EAAA,CAAAuB,SAAA,EAA4B;IAA5BvB,EAAA,CAAAwB,UAAA,SAAA0D,cAAA,CAAAC,WAAA,CAA4B;IAG5BnF,EAAA,CAAAuB,SAAA,EAAgC;IAAhCvB,EAAA,CAAAwB,UAAA,SAAA0D,cAAA,CAAAE,eAAA,CAAgC;IAIpCpF,EAAA,CAAAuB,SAAA,GACF;IADEvB,EAAA,CAAA2E,kBAAA,MAAAO,cAAA,CAAAK,GAAA,MACF;IAEMvF,EAAA,CAAAuB,SAAA,GAAsD;IAAtDvB,EAAA,CAAA2E,kBAAA,cAAAO,cAAA,CAAAM,OAAA,eAAAC,KAAA,UAAsD;;;;;IAfhEzF,EAAA,CAAAC,cAAA,eAA8H;IAC5HD,EAAA,CAAAgC,UAAA,IAAA0D,gEAAA,mBAGC;IAcH1F,EAAA,CAAAG,YAAA,EAAM;;;;IAhBqBH,EAAA,CAAAuB,SAAA,EAA2D;IAA3DvB,EAAA,CAAAwB,UAAA,YAAAoD,aAAA,CAAAC,QAAA,kBAAAD,aAAA,CAAAC,QAAA,CAAAG,aAAA,kBAAAJ,aAAA,CAAAC,QAAA,CAAAG,aAAA,CAAAW,KAAA,kBAAAf,aAAA,CAAAC,QAAA,CAAAG,aAAA,CAAAW,KAAA,CAAAC,YAAA,CAA2D;;;;;IAqBlF5F,EAFJ,CAAAC,cAAA,eAAmE,eACtC,aACjB;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,sDAC1B;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IApDJH,EAPJ,CAAAC,cAAA,cAKC,cAC+B,aACJ;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACvDH,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAIFH,EAFJ,CAAAC,cAAA,cAA+B,cACH,aAChB;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IAC1B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAAiC;IAC/BD,EAAA,CAAAE,MAAA,IACF;;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAkCNH,EA/BA,CAAAgC,UAAA,KAAA6D,0DAAA,kBAA2H,KAAAC,0DAAA,kBAUG,KAAAC,0DAAA,kBAqB3D;IAKrE/F,EAAA,CAAAG,YAAA,EAAM;;;;;IAxDJH,EADA,CAAAuD,WAAA,YAAAqB,aAAA,CAAAoB,MAAA,eAAgD,WAAApB,aAAA,CAAAoB,MAAA,cACF;IAGpBhG,EAAA,CAAAuB,SAAA,GAAyB;IAAzBvB,EAAA,CAAA6B,iBAAA,CAAA+C,aAAA,CAAAhD,SAAA,CAAyB;IACnB5B,EAAA,CAAAuB,SAAA,EAA0B;IAA1BvB,EAAA,CAAAiG,UAAA,CAAArB,aAAA,CAAAoB,MAAA,CAA0B;IACtDhG,EAAA,CAAAuB,SAAA,EACF;IADEvB,EAAA,CAAA2E,kBAAA,MAAAC,aAAA,CAAAoB,MAAA,2CACF;IAK0BhG,EAAA,CAAAuB,SAAA,GAC1B;IAD0BvB,EAAA,CAAA2E,kBAAA,MAAAC,aAAA,CAAAsB,WAAA,MAC1B;IAGElG,EAAA,CAAAuB,SAAA,GACF;IADEvB,EAAA,CAAA2E,kBAAA,MAAA3E,EAAA,CAAAmG,WAAA,SAAAvB,aAAA,CAAAwB,SAAA,iBACF;IAIIpG,EAAA,CAAAuB,SAAA,GAAwF;IAAxFvB,EAAA,CAAAwB,UAAA,SAAAoD,aAAA,CAAAoB,MAAA,kBAAApB,aAAA,CAAAC,QAAA,IAAApE,MAAA,CAAAkB,SAAA,kBAAwF;IAUxF3B,EAAA,CAAAuB,SAAA,EAA2F;IAA3FvB,EAAA,CAAAwB,UAAA,SAAAoD,aAAA,CAAAoB,MAAA,kBAAApB,aAAA,CAAAC,QAAA,IAAApE,MAAA,CAAAkB,SAAA,qBAA2F;IAqB3F3B,EAAA,CAAAuB,SAAA,EAAmC;IAAnCvB,EAAA,CAAAwB,UAAA,SAAAoD,aAAA,CAAAoB,MAAA,cAAmC;;;;;IAxD7ChG,EAAA,CAAAC,cAAA,cAAwE;IACtED,EAAA,CAAAgC,UAAA,IAAAqE,mDAAA,oBAKC;IAwDHrG,EAAA,CAAAG,YAAA,EAAM;;;;IA5DoBH,EAAA,CAAAuB,SAAA,EAAqB;IAArBvB,EAAA,CAAAwB,UAAA,YAAAf,MAAA,CAAA6F,gBAAA,CAAqB;;;;;IAVjDtG,EADF,CAAAC,cAAA,cAAgE,SAC1D;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGrBH,EAAA,CAAAC,cAAA,cAAyC;IAKvCD,EAJA,CAAAgC,UAAA,IAAAuE,6CAAA,kBAAqE,IAAAC,6CAAA,kBAIG;IAgE5ExG,EADE,CAAAG,YAAA,EAAM,EACF;;;;IApEIH,EAAA,CAAAuB,SAAA,GAAmC;IAAnCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAA6F,gBAAA,CAAAlE,MAAA,OAAmC;IAInCpC,EAAA,CAAAuB,SAAA,EAAiC;IAAjCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAA6F,gBAAA,CAAAlE,MAAA,KAAiC;;;AD7oBnD;AAcA,WAAaqE,uBAAuB;EAA9B,MAAOA,uBAAuB;IAsHxBC,KAAA;IACAC,MAAA;IACAC,YAAA;IACAC,sBAAA;IACAC,YAAA;IACAC,aAAA;IACAC,WAAA;IACAC,oBAAA;IA5HVC,cAAc,GAAc,CAC1B;MAAEC,EAAE,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAgB,CAAE,EAC3C;MAAED,EAAE,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAc,CAAE,EAC7C;MAAED,EAAE,EAAE,cAAc;MAAEC,KAAK,EAAE,SAAS;MAAEC,QAAQ,EAAE;IAAI,CAAE,CACzD;IAED;IACAC,OAAO,GAAkB,IAAI;IAC7B3F,SAAS,GAAW,YAAY;IAChCC,SAAS,GAAW,OAAO;IAC3B2F,WAAW,GAAW,EAAE;IAGxBC,cAAc;IAEd;IACAC,YAAY,GAAkB,EAAE;IAChCC,gBAAgB,GAAW,CAAC;IAC5BC,gBAAgB;IAChBC,SAAS,GAAY,KAAK;IAC1B5B,MAAM,GAAoBjG,eAAe,CAAC8H,UAAU;IAEpD;IACApG,YAAY,GAAkB,EAAE;IAChCC,gBAAgB,GAAY,KAAK;IACjCoG,SAAS,GAAG,EAAE;IAEd;IACAC,YAAY,GAAiB,EAAE;IAC/BC,mBAAmB,GAAQ,IAAI,CAAC,CAAC;IAEjC;IACA1B,gBAAgB,GAOX,EAAE;IAEA2B,SAAS;IAEhB;IACAC,kBAAkB,GAAgB,IAAI;IACtCC,kBAAkB,GAAY,KAAK;IACnCC,WAAW;IAEXC,kBAAkB,GAAGvI,WAAW,CAACwI,kBAAkB,IAAI,KAAK;IAErDC,mBAAmB,GAAY,KAAK;IAC3CC,gBAAgB;IAERC,QAAQ,GAAG,IAAIpJ,OAAO,EAAQ;IACtCqJ,WAAW,GAAW,gBAAgB;IACtCC,QAAQ,GAAa,CACnB;MAAExB,EAAE,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAS,CAAE,EAChC;MAAED,EAAE,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAW,CAAE,EAClC;MAAED,EAAE,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAc,CAAE,CACtC;IAEDwB,QAAQ,GAAG,KAAK;IAChBC,UAAU;IACVC,WAAW,GAAU,EAAE;IACvBC,WAAW,GAAG,KAAK;IACnBC,WAAW,GAAY,KAAK;IAC5BC,aAAa,GAAW,EAAE;IAC1BC,UAAU,GAAU,EAAE;IACtBC,aAAa,GAAU,EAAE;IACzBC,QAAQ,GAAG,CAAC;IACZC,SAAS,GAAG,KAAK;IACjBC,WAAW,GAAW,EAAE;IAExBC,eAAe,GAAa,EAAE;IAC9BC,iBAAiB,GAAW,CAAC;IAC7BC,WAAW,GAAW,UAAU;IAEhC;IACAC,oBAAoB,GAAY,KAAK;IACrCC,cAAc,GAAW,WAAW;IAEpC;IACAC,mBAAmB,GAAQ,IAAI;IAC/BC,eAAe,GAAU,EAAE;IAC3BC,WAAW,GAAU,EAAE;IACvBC,WAAW,GAAU,EAAE;IACvBC,cAAc,GAAW,EAAE;IAC3BC,iBAAiB,GAAW,EAAE;IAC9BC,yBAAyB,GAAW,EAAE;IACtCC,sBAAsB,GAAU,EAAE;IAClCC,eAAe,GAAa,EAAE;IAC9BC,wBAAwB,GAAG,KAAK;IAChCC,sBAAsB,GAAG,IAAIjL,OAAO,EAAW;IAC/CkL,gBAAgB,GAAU,EAAE;IAC5BC,SAAS,GAAW,EAAE;IACtBC,aAAa,GAAqB,EAAE;IAEpC;IACA/F,6BAA6B,GAAW,CAAC;IACzCvC,oBAAoB,GAAU,EAAE;IAChCS,mBAAmB,GAAU,EAAE;IAC/BJ,uBAAuB,GAAU,EAAE;IACnCqB,uBAAuB,GAAU,EAAE;IACnCZ,kBAAkB,GAAU,EAAE;IAE9B;IACQyH,sBAAsB,GAA+B;MAC3DC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE,IAAI;MACXC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE,IAAI;MACfC,IAAI,EAAE;KACP;IAED;IAEAC,YACUtE,KAAqB,EACrBC,MAAc,EACdC,YAAiC,EACjCC,sBAA8C,EAC9CC,YAAiC,EACjCC,aAA4B,EAC5BC,WAAwB,EACxBC,oBAA0C;MAP1C,KAAAP,KAAK,GAALA,KAAK;MACL,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,YAAY,GAAZA,YAAY;MACZ,KAAAC,sBAAsB,GAAtBA,sBAAsB;MACtB,KAAAC,YAAY,GAAZA,YAAY;MACZ,KAAAC,aAAa,GAAbA,aAAa;MACb,KAAAC,WAAW,GAAXA,WAAW;MACX,KAAAC,oBAAoB,GAApBA,oBAAoB;MAE5B,IAAI,CAACgB,SAAS,GAAG,IAAI,CAACjB,WAAW,CAACiE,KAAK,CAAC;QACtCC,gBAAgB,EAAE,CAAC,IAAI,CAAC;QACxBC,aAAa,EAAE,CAAC,KAAK;OACtB,CAAC;IACJ;IAEAC,QAAQA,CAAA;MACNC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MAClD,IAAI,CAAClD,WAAW,GAAGmD,MAAM,CAACC,UAAU,EAAE;MAEtC,IAAI,CAAC9E,KAAK,CAAC+E,MAAM,CAACC,SAAS,CAAED,MAAM,IAAI;QACrC,IAAI,CAAC9J,SAAS,GAAG8J,MAAM,CAAC,MAAM,CAAC,IAAI,YAAY;QAC/CJ,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAAC3J,SAAS,CAAC;MAC9D,CAAC,CAAC;MAEF,IAAI,CAAC+E,KAAK,CAACiF,WAAW,CAACD,SAAS,CAAED,MAAM,IAAI;QAC1C,IAAIA,MAAM,CAAC,IAAI,CAAC,EAAE;UAChB,IAAI,CAACnE,OAAO,GAAGmE,MAAM,CAAC,IAAI,CAAC;UAC3B,IAAI,CAACG,aAAa,CAACH,MAAM,CAAC,IAAI,CAAC,CAAC;QAClC;MACF,CAAC,CAAC;MAEF;MACA,IAAI,CAAChK,YAAY,GAAG,CAClB;QACEoK,IAAI,EAAE,IAAI;QACV9G,IAAI,EAAE,kBAAkB,IAAI,CAACnD,SAAS,IAAI,YAAY;OACvD,CACF;IACH;IAEAkK,WAAWA,CAAA;MACT,IAAI,CAACrD,QAAQ,CAACsD,IAAI,EAAE;MACpB,IAAI,CAACtD,QAAQ,CAACuD,QAAQ,EAAE;MACxB,IAAI,IAAI,CAACxD,gBAAgB,EAAE;QACzByD,aAAa,CAAC,IAAI,CAACzD,gBAAgB,CAAC;MACtC;IACF;IAEA0D,WAAWA,CAACC,KAAoC;MAC9C,IAAI,CAAC1C,WAAW,GAAG0C,KAAK,CAAChF,EAAE;MAC3B,IAAI,CAACuB,WAAW,GAAGyD,KAAK,CAAC/E,KAAK;IAChC;IAEAwE,aAAaA,CAACtE,OAAe;MAC3B,IAAI,CAAC+B,SAAS,GAAG,IAAI;MAErB;MACA,IAAI,IAAI,CAAC1H,SAAS,KAAK,eAAe,EAAE;QACtC,IAAI,CAACiF,YAAY,CAACwF,gCAAgC,CAAC9E,OAAO,CAAC,CAACoE,SAAS,CAAC;UACpEK,IAAI,EAAGlH,QAAa,IAAI;YACtB,IAAI,CAACwH,uBAAuB,CAACxH,QAAQ,CAAC;UACxC,CAAC;UACDyH,KAAK,EAAGA,KAAU,IAAI;YACpBjB,OAAO,CAACiB,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;YAC1D,IAAI,CAACjD,SAAS,GAAG,KAAK;UACxB;SACD,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAACzC,YAAY,CAAC2F,YAAY,CAACjF,OAAO,CAAC,CAACoE,SAAS,CAAC;UAChDK,IAAI,EAAGlH,QAAa,IAAI;YACtB,IAAI,CAACwH,uBAAuB,CAACxH,QAAQ,CAAC;UACxC,CAAC;UACDyH,KAAK,EAAGA,KAAU,IAAI;YACpBjB,OAAO,CAACiB,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;YACvD,IAAI,CAACjD,SAAS,GAAG,KAAK;UACxB;SACD,CAAC;MACJ;IACF;IAEQgD,uBAAuBA,CAACxH,QAAa;MAC3C,IAAI,CAACwE,SAAS,GAAG,KAAK;MAEtB;MACA,IAAImD,SAAS;MACb,IACE3H,QAAQ,CAAC4H,YAAY,IACrBC,KAAK,CAACC,OAAO,CAAC9H,QAAQ,CAAC4H,YAAY,CAAC,IACpC5H,QAAQ,CAAC4H,YAAY,CAACrK,MAAM,GAAG,CAAC,EAChC;QACAoK,SAAS,GAAG3H,QAAQ,CAAC4H,YAAY,CAAC,CAAC,CAAC;MACtC,CAAC,MAAM,IAAI5H,QAAQ,CAAC0C,WAAW,EAAE;QAC/BiF,SAAS,GAAG3H,QAAQ,CAAC0C,WAAW;MAClC,CAAC,MAAM,IAAI1C,QAAQ,CAAC+H,IAAI,EAAE;QACxBJ,SAAS,GAAG3H,QAAQ,CAAC+H,IAAI;MAC3B,CAAC,MAAM;QACLJ,SAAS,GAAG3H,QAAQ;MACtB;MAEA,IAAI2H,SAAS,EAAE;QACb,IAAI,CAAC5C,mBAAmB,GAAG4C,SAAS;QACpC,IAAI,CAAC5K,SAAS,GAAG4K,SAAS,CAACzK,IAAI,IAAIyK,SAAS,CAAC5K,SAAS,IAAI,OAAO;QACjE,IAAI,CAAC2F,WAAW,GAAGiF,SAAS,CAACrH,WAAW,IAAIqH,SAAS,CAACjF,WAAW,IAAI,EAAE;QAEvE;QACA,IAAI,IAAI,CAAC5F,SAAS,KAAK,YAAY,EAAE;UACnC;UACA,IAAI,CAACqI,cAAc,GACjBwC,SAAS,CAACK,WAAW,IAAIL,SAAS,CAACzK,IAAI,IAAI,cAAc;UAE3D;UACA,IAAI,CAACkI,iBAAiB,GACpBuC,SAAS,CAACM,WAAW,IACrBN,SAAS,CAACK,WAAW,IACrBL,SAAS,CAACzK,IAAI,IACd,EAAE;UAEJ;UACA,IAAIyK,SAAS,CAACO,gBAAgB,EAAE;YAC9B,IAAI,CAAC7C,yBAAyB,GAAGsC,SAAS,CAACO,gBAAgB;UAC7D,CAAC,MAAM,IAAIP,SAAS,CAACM,WAAW,EAAE;YAChC,IAAI,CAAC5C,yBAAyB,GAAGsC,SAAS,CAACM,WAAW;UACxD,CAAC,MAAM,IAAIN,SAAS,CAACK,WAAW,EAAE;YAChC,IAAI,CAAC3C,yBAAyB,GAAGsC,SAAS,CAACK,WAAW;UACxD;QACF;QAEA;QACA,IAAI,IAAI,CAACpL,YAAY,CAACW,MAAM,GAAG,CAAC,EAAE;UAChC,IAAI,CAACX,YAAY,CAAC,CAAC,CAAC,CAACsD,IAAI,GAAG,kBAAkB,IAAI,CAACnD,SAAS,6BAA6B;QAC3F;QAEA;QACA,IAAI,CAACoL,cAAc,CAACR,SAAS,CAAC;MAChC;IACF;IAEQQ,cAAcA,CAACR,SAAc;MACnC;MACA,IAAI,CAACS,gCAAgC,CAACT,SAAS,CAAC;IAClD;IAEA1L,iBAAiBA,CAACoM,OAAe;MAC/B,IAAI,IAAI,CAACvL,SAAS,KAAK,YAAY,EAAE;QACnC;QACA,IACE,CAAC,IAAI,CAACiI,mBAAmB,KACxB,CAAC,IAAI,CAACI,cAAc,IAAI,IAAI,CAACA,cAAc,KAAK,SAAS,CAAC,EAC3D;UACA,IAAI,CAACmD,cAAc,CACjB,+DAA+D,CAChE;UACD;QACF;QAEA,IAAIC,cAAc,GAAGF,OAAO;QAC5B,IAAI,IAAI,CAAC/C,sBAAsB,CAAC/H,MAAM,GAAG,CAAC,EAAE;UAC1C,MAAMiL,SAAS,GAAG,IAAI,CAAClD,sBAAsB,CAC1CmD,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACC,YAAY,CAAC,CAChCC,IAAI,CAAC,IAAI,CAAC;UACbL,cAAc,GAAG,GAAGF,OAAO,0BAA0BG,SAAS,EAAE;QAClE;QAEA;QACA,MAAMjF,WAAW,GAAGmD,MAAM,CAACC,UAAU,EAAE;QACvC,IAAI,CAAClF,gBAAgB,CAACoH,IAAI,CAAC;UACzBvG,EAAE,EAAEiB,WAAW;UACfxG,SAAS,EAAE,IAAI,CAACA,SAAS;UACzBsE,WAAW,EAAEgH,OAAO;UACpBlH,MAAM,EAAE,SAAS;UAAE;UACnBI,SAAS,EAAE,IAAIuH,IAAI;SACpB,CAAC;QACFtC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,IAAI,CAAChF,gBAAgB,CAAC;QAErE,IAAI,CAAC7E,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;UAAEoK,IAAI,EAAE,MAAM;UAAE9G,IAAI,EAAEqI;QAAc,CAAE,CACvC;QACD,IAAI,CAAC1L,gBAAgB,GAAG,IAAI;QAE5B,MAAMwJ,gBAAgB,GACpB,IAAI,CAACjD,SAAS,CAAC2F,GAAG,CAAC,kBAAkB,CAAC,EAAEC,KAAK,IAAI,KAAK;QACxD,MAAM1C,aAAa,GAAG,IAAI,CAAClD,SAAS,CAAC2F,GAAG,CAAC,eAAe,CAAC,EAAEC,KAAK,IAAI,KAAK;QAEzExC,OAAO,CAACC,GAAG,CACT,2CAA2C,EAC3CJ,gBAAgB,EAChB,gBAAgB,EAChBC,aAAa,CACd;QAED;QACA;QACA,MAAM2C,SAAS,GACb,IAAI,CAACtD,SAAS,IACd,IAAI,CAACP,iBAAiB,IACtB,IAAI,CAACL,mBAAmB,EAAEkD,WAAW,IACrC,IAAI,CAAClD,mBAAmB,EAAEiD,WAAW,IACrC,IAAI,CAACjD,mBAAmB,EAAE7H,IAAI,IAC9B,IAAI,CAACiI,cAAc;QAErB,IAAI+D,iBAAiB,GAAG,IAAI,CAAC7D,yBAAyB;QACtD,IAAI,CAAC6D,iBAAiB,EAAE;UACtB;UACA,IAAI,IAAI,CAACnE,mBAAmB,EAAEmD,gBAAgB,EAAE;YAC9CgB,iBAAiB,GAAG,IAAI,CAACnE,mBAAmB,CAACmD,gBAAgB;UAC/D,CAAC,MAAM;YACL,MAAMiB,OAAO,GAAG,IAAI,CAACC,qBAAqB,EAAE;YAC5C,MAAMC,eAAe,GACnB,IAAI,CAACtE,mBAAmB,EAAEkD,WAAW,IACrC,IAAI,CAAClD,mBAAmB,EAAEiD,WAAW,IACrC,IAAI,CAACjD,mBAAmB,EAAE7H,IAAI,IAC9B+L,SAAS;YACXC,iBAAiB,GAAG,GAAGG,eAAe,GAAGF,OAAO,EAAE;UACpD;QACF;QAEA,IAAI,IAAI,CAAC7D,sBAAsB,CAAC/H,MAAM,GAAG,CAAC,EAAE;UAC1C,IAAI,CAAC+L,+BAA+B,CAClCjB,OAAO,EACPY,SAAS,EACTC,iBAAiB,EACjB7C,gBAAgB,EAChBC,aAAa,CACd;UACD;QACF;QAEA,IAAI,CAACiD,qBAAqB,CACxBlB,OAAO,EACPY,SAAS,EACTC,iBAAiB,EACjB7C,gBAAgB,EAChBC,aAAa,CACd;MACH,CAAC,MAAM,IAAI,IAAI,CAACxJ,SAAS,KAAK,eAAe,EAAE;QAC7C;QACA,MAAMyG,WAAW,GAAGmD,MAAM,CAACC,UAAU,EAAE;QACvC,IAAI,CAAClF,gBAAgB,CAACoH,IAAI,CAAC;UACzBvG,EAAE,EAAEiB,WAAW;UACfxG,SAAS,EAAE,IAAI,CAACA,SAAS;UACzBsE,WAAW,EAAEgH,OAAO;UACpBlH,MAAM,EAAE,SAAS;UAAE;UACnBI,SAAS,EAAE,IAAIuH,IAAI;SACpB,CAAC;QACFtC,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAE,IAAI,CAAChF,gBAAgB,CAAC;QAEnF,IAAI,CAAC5E,gBAAgB,GAAG,IAAI;QAC5B,IAAI2M,OAAO,GAAG;UACZjG,WAAW,EAAE,IAAI,CAACA,WAAW;UAC7Bd,OAAO,EAAEgH,MAAM,CAAC,IAAI,CAAChH,OAAO,CAAC;UAC7BiH,IAAI,EAAE,IAAI,CAACzH,YAAY,CAAC0H,aAAa,EAAE,IAAI,uBAAuB;UAClEC,UAAU,EAAE;YAAEC,QAAQ,EAAExB;UAAO;SAChC;QAED,IAAI,IAAI,CAAC/C,sBAAsB,CAAC/H,MAAM,GAAG,CAAC,EAAE;UAC1C,MAAMuM,WAAW,GAAG,IAAI,CAACxE,sBAAsB,CAAC,CAAC,CAAC;UAClD,IAAIiD,cAAsB;UAC1B,IAAI,IAAI,CAACjD,sBAAsB,CAAC/H,MAAM,GAAG,CAAC,EAAE;YAC1C,MAAMiL,SAAS,GAAG,IAAI,CAAClD,sBAAsB,CAC1CmD,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACC,YAAY,CAAC,CAChCC,IAAI,CAAC,IAAI,CAAC;YACbL,cAAc,GAAG,sBAAsBC,SAAS,EAAE;YAElD,IAAI,CAAC5L,YAAY,GAAG,CAAC;cAAEoK,IAAI,EAAE,MAAM;cAAE9G,IAAI,EAAEqI;YAAc,CAAE,CAAC;UAC9D;UACA,IAAI,CAACvG,sBAAsB,CACxB+H,0BAA0B,CAACP,OAAO,EAAEM,WAAW,CAAC,CAChDE,IAAI,CACHrP,QAAQ,CAAC,MAAK;YACZ,IAAI,CAACkC,gBAAgB,GAAG,KAAK;YAC7B,IAAI,CAAC2I,wBAAwB,GAAG,KAAK;UACvC,CAAC,CAAC,EACF/K,SAAS,CAAC,IAAI,CAACgL,sBAAsB,CAAC,CACvC,CACAoB,SAAS,CAAC;YACTK,IAAI,EAAG+C,GAAG,IAAK,IAAI,CAACC,0BAA0B,CAACD,GAAG,EAAE5B,OAAO,CAAC;YAC5DZ,KAAK,EAAG0C,GAAQ,IAAI;cAClB;cACA,IAAI,IAAI,CAAC1I,gBAAgB,CAAClE,MAAM,GAAG,CAAC,EAAE;gBACpC,IAAI,CAACkE,gBAAgB,CAAC,IAAI,CAACA,gBAAgB,CAAClE,MAAM,GAAG,CAAC,CAAC,CAAC4D,MAAM,GAAG,QAAQ;cAC3E;cAEA,IAAI,CAACvE,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;gBAAEoK,IAAI,EAAE,MAAM;gBAAE9G,IAAI,EAAEmI;cAAO,CAAE,EAC/B;gBACErB,IAAI,EAAE,IAAI;gBACV9G,IAAI,EACFiK,GAAG,EAAE1C,KAAK,EAAEY,OAAO,IACnB8B,GAAG,EAAE9B,OAAO,IACZ;eACH,CACF;cAED;cACA,IAAI,CAAC+B,iBAAiB,CAAC,QAAQ,CAAC;YAClC;WACD,CAAC;QACN,CAAC,MAAM;UACL,IAAI,CAACpI,sBAAsB,CACxBqI,kBAAkB,CAACb,OAAO,CAAC,CAC3BQ,IAAI,CACHrP,QAAQ,CAAC,MAAK;YACZ,IAAI,CAACkC,gBAAgB,GAAG,KAAK;YAC7B,IAAI,CAAC2I,wBAAwB,GAAG,KAAK;UACvC,CAAC,CAAC,EACF/K,SAAS,CAAC,IAAI,CAACgL,sBAAsB,CAAC,CACvC,CACAoB,SAAS,CAAC;YACTK,IAAI,EAAG+C,GAAG,IAAK,IAAI,CAACC,0BAA0B,CAACD,GAAG,EAAE5B,OAAO,CAAC;YAC5DZ,KAAK,EAAG0C,GAAQ,IAAI;cAClB;cACA,IAAI,IAAI,CAAC1I,gBAAgB,CAAClE,MAAM,GAAG,CAAC,EAAE;gBACpC,IAAI,CAACkE,gBAAgB,CAAC,IAAI,CAACA,gBAAgB,CAAClE,MAAM,GAAG,CAAC,CAAC,CAAC4D,MAAM,GAAG,QAAQ;cAC3E;cAEA,IAAI,CAACvE,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;gBAAEoK,IAAI,EAAE,MAAM;gBAAE9G,IAAI,EAAEmI;cAAO,CAAE,EAC/B;gBACErB,IAAI,EAAE,IAAI;gBACV9G,IAAI,EACFiK,GAAG,EAAE1C,KAAK,EAAEY,OAAO,IACnB8B,GAAG,EAAE9B,OAAO,IACZ;eACH,CACF;cAED;cACA,IAAI,CAAC+B,iBAAiB,CAAC,QAAQ,CAAC;YAClC;WACD,CAAC;QACN;MACF;IACF;IAEArO,eAAeA,CAAC+J,MAAsB;MACpC,IAAI,CAAC7C,SAAS,GAAG6C,MAAM,CAAC5I,IAAI,IAAIoN,MAAM,CAACxE,MAAM,CAACkD,KAAK,CAAC,IAAI,EAAE;IAC5D;IAEA7M,gCAAgCA,CAAC6M,KAAc;MAC7C;MACA,IAAI,CAAC5F,SAAS,CAAC2F,GAAG,CAAC,kBAAkB,CAAC,EAAEwB,QAAQ,CAACvB,KAAK,CAAC;MAEvD;MACA;MACA,IAAI,CAACA,KAAK,EAAE;QACV,IAAI,CAACtD,gBAAgB,GAAG,EAAE;QAC1Bc,OAAO,CAACC,GAAG,CACT,6DAA6D,CAC9D;MACH,CAAC,MAAM;QACLD,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;MACzE;IACF;IAEApK,0BAA0BA,CAAC2M,KAAc;MACvC;MACA,IAAI,CAAC5F,SAAS,CAAC2F,GAAG,CAAC,eAAe,CAAC,EAAEwB,QAAQ,CAACvB,KAAK,CAAC;MACpDxC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEuC,KAAK,CAAC;IAC9C;IAEAzM,eAAeA,CAACiO,KAAY;MAC1B,IAAI,CAACpG,aAAa,GAAGoG,KAAK;MAC1B;MACA,IAAI,CAAClF,sBAAsB,GAAGkF,KAAK;IACrC;IAEA/N,mBAAmBA,CAAA;MACjB;IAAA;IAGFgO,QAAQA,CAAA;MACN;IAAA;IAGFC,aAAaA,CAACC,OAA8B;MAC1C;IAAA;IAGFC,mBAAmBA,CAACC,MAAiC;MACnD;IAAA;IAGFC,YAAYA,CAAA;MACV,IAAI,CAAChJ,MAAM,CAACiJ,QAAQ,CAAC,CAAC,eAAe,EAAE,IAAI,CAACjO,SAAS,CAAC,EAAE;QACtDgK,WAAW,EAAE;UAAExE,EAAE,EAAE,IAAI,CAACG,OAAO;UAAEuI,IAAI,EAAE;QAAM;OAC9C,CAAC;IACJ;IAEAC,SAASA,CAAA;MACP,IAAI,CAACnJ,MAAM,CAACiJ,QAAQ,CAAC,CAAC,eAAe,EAAE,IAAI,CAACjO,SAAS,CAAC,EAAE;QACtDgK,WAAW,EAAE;UAAExE,EAAE,EAAE,IAAI,CAACG,OAAO;UAAEuI,IAAI,EAAE;QAAM;OAC9C,CAAC;IACJ;IAEAE,oBAAoBA,CAAA;MAClB,IAAI,CAACpJ,MAAM,CAACiJ,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;IACzC;IAEAI,eAAeA,CAAA;MACb,IAAI,CAACtG,oBAAoB,GAAG,CAAC,IAAI,CAACA,oBAAoB;IACxD;IAEAuF,iBAAiBA,CAACgB,GAAW;MAC3B,IAAI,CAACtG,cAAc,GAAGsG,GAAG;IAC3B;IAEA;IACA7M,mBAAmBA,CAAC8M,QAAgB;MAClC,IAAI,CAACxF,sBAAsB,CAACwF,QAAQ,CAAC,GACnC,CAAC,IAAI,CAACxF,sBAAsB,CAACwF,QAAQ,CAAC;IAC1C;IAEAzM,uBAAuBA,CAACyM,QAAgB;MACtC,OAAO,IAAI,CAACxF,sBAAsB,CAACwF,QAAQ,CAAC,IAAI,KAAK;IACvD;IAEA;IACQ/C,cAAcA,CAACD,OAAe;MACpC,IAAI,CAACzL,YAAY,GAAG,CAAC,GAAG,IAAI,CAACA,YAAY,EAAE;QAAEoK,IAAI,EAAE,IAAI;QAAE9G,IAAI,EAAEmI;MAAO,CAAE,CAAC;IAC3E;IAEQe,qBAAqBA,CAAA;MAC3B;MACA,OAAO,EAAE;IACX;IAEQkC,qBAAqBA,CAAA;MAC3B;MACA,OAAO,EAAE;IACX;IAEApB,0BAA0BA,CAAClK,QAAa,EAAEqI,OAAe;MACvD,IAAI;QACF;QACA,IAAI,CAAClF,mBAAmB,GAAGnD,QAAQ;QAEnC;QACA,IAAI,IAAI,CAACyB,gBAAgB,CAAClE,MAAM,GAAG,CAAC,EAAE;UACpC,IAAI,CAACkE,gBAAgB,CAAC,IAAI,CAACA,gBAAgB,CAAClE,MAAM,GAAG,CAAC,CAAC,CAAC4D,MAAM,GAAG,SAAS;UAC1E,IAAI,CAACM,gBAAgB,CAAC,IAAI,CAACA,gBAAgB,CAAClE,MAAM,GAAG,CAAC,CAAC,CAACyC,QAAQ,GAAGA,QAAQ;UAC3EwG,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAAChF,gBAAgB,CAAC;QAClE;QAEA,MAAM8J,SAAS,GAAGvL,QAAQ,EAAEG,aAAa,EAAEW,KAAK,EAAE0K,MAAM;QACxD,IAAIC,eAAe,GAAG,EAAE;QAExB,IAAIF,SAAS,EAAE;UACb;UACAE,eAAe,GAAGF,SAAS,CAACG,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC;QACnD,CAAC,MAAM;UACLD,eAAe,GAAGzL,QAAQ,EAAEG,aAAa,EAAEC,MAAM;QACnD;QAEA;QACA,IAAI,CAACxD,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;UAAEoK,IAAI,EAAE,MAAM;UAAE9G,IAAI,EAAEmI;QAAO,CAAE,EAC/B;UACErB,IAAI,EAAE,IAAI;UACV9G,IAAI,EAAE,GAAG,IAAI,CAACnD,SAAS;SACxB,CACF;QAED;QACA,IAAI,CAACqN,iBAAiB,CAAC,QAAQ,CAAC;MAClC,CAAC,CAAC,OAAOD,GAAQ,EAAE;QACjB;QACA,IAAI,IAAI,CAAC1I,gBAAgB,CAAClE,MAAM,GAAG,CAAC,EAAE;UACpC,IAAI,CAACkE,gBAAgB,CAAC,IAAI,CAACA,gBAAgB,CAAClE,MAAM,GAAG,CAAC,CAAC,CAAC4D,MAAM,GAAG,QAAQ;UACzEqF,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE,IAAI,CAAChF,gBAAgB,CAAC;QAC3E;QAEA,IAAI,CAAC7E,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;UAAEoK,IAAI,EAAE,MAAM;UAAE9G,IAAI,EAAEmI;QAAO,CAAE,EAC/B;UACErB,IAAI,EAAE,IAAI;UACV9G,IAAI,EAAE,GAAG,IAAI,CAACnD,SAAS;SACxB,CACF;QAED;QACA,IAAI,CAACqN,iBAAiB,CAAC,QAAQ,CAAC;MAClC;IACF;IAEQd,+BAA+BA,CACrCjB,OAAe,EACf2C,IAAY,EACZ9B,iBAAyB,EACzB7C,gBAAyB,EACzBC,aAAsB;MAEtB,MAAMqF,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/B,IAAI,CAACtG,sBAAsB,CAACuG,OAAO,CAAEC,QAAQ,IAAI;QAC/C,IAAIA,QAAQ,CAACpD,IAAI,EAAE;UACjBiD,QAAQ,CAACI,MAAM,CAAC,OAAO,EAAED,QAAQ,CAACpD,IAAI,CAAC;QACzC;MACF,CAAC,CAAC;MAEF,IAAIiD,QAAQ,CAACK,GAAG,CAAC,OAAO,CAAC,EAAE;QACzB,IAAI,CAAChK,sBAAsB,CACxBiK,gBAAgB,CAACN,QAAQ,CAAC,CAC1B3B,IAAI,CACHtP,SAAS,CAAEwR,YAAY,IAAI;UACzB,MAAMC,WAAW,GACfD,YAAY,EAAEE,aAAa,EACvB3D,GAAG,CAAEzI,QAAa,IAAKA,QAAQ,CAACmM,WAAW,CAAC,EAC5CvD,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;UACtB,IAAI,CAACyD,8BAA8B,CACjChE,OAAO,EACP2C,IAAI,EACJ9B,iBAAiB,EACjB7C,gBAAgB,EAChBC,aAAa,EACb6F,WAAW,CACZ;UACD,OAAOtR,EAAE,CAAC,IAAI,CAAC;QACjB,CAAC,CAAC,EACFD,UAAU,CAAE6M,KAAK,IAAI;UACnBjB,OAAO,CAACiB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,IAAI,CAAC8B,qBAAqB,CACxBlB,OAAO,EACP2C,IAAI,EACJ9B,iBAAiB,EACjB7C,gBAAgB,EAChBC,aAAa,CACd;UACD,OAAOzL,EAAE,CAAC,IAAI,CAAC;QACjB,CAAC,CAAC,CACH,CACAgM,SAAS,EAAE;MAChB,CAAC,MAAM;QACL,IAAI,CAAC0C,qBAAqB,CACxBlB,OAAO,EACP2C,IAAI,EACJ9B,iBAAiB,EACjB7C,gBAAgB,EAChBC,aAAa,CACd;MACH;IACF;IAEQiD,qBAAqBA,CAC3BlB,OAAe,EACf2C,IAAY,EACZ9B,iBAAyB,EACzB7C,gBAAyB,EACzBC,aAAsB;MAEtBE,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;QAClC4B,OAAO;QACP2C,IAAI;QACJ9B,iBAAiB;QACjB7C,gBAAgB;QAChBC,aAAa;QACbgG,wBAAwB,EAAE,IAAI,CAAC5G,gBAAgB,CAACnI;OACjD,CAAC;MAEF,IAAI8I,gBAAgB,EAAE;QACpB,IAAI,CAACX,gBAAgB,CAACmD,IAAI,CAAC;UAAE0D,OAAO,EAAElE,OAAO;UAAEmE,IAAI,EAAE;QAAM,CAAE,CAAC;MAChE;MAEA,MAAMhD,OAAO,GAAGnD,gBAAgB,GAAG,IAAI,CAACX,gBAAgB,GAAG2C,OAAO;MAClE,MAAM;QAAEoE;MAAO,CAAE,GAAG,IAAI,CAACnB,qBAAqB,EAAE;MAEhD9E,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE+C,OAAO,CAAC;MAEjD,IAAI,CAACxH,sBAAsB,CACxB0K,cAAc,CACblD,OAAO,EACPwB,IAAI,EACJ3E,gBAAgB,EAChBC,aAAa,EACb,IAAI,CAACf,eAAe,EACpB2D,iBAAiB,EACjB,EAAE,EACFuD,OAAO,CACR,CACAzC,IAAI,CACHrP,QAAQ,CAAC,MAAK;QACZ,IAAI,CAACkC,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAAC2I,wBAAwB,GAAG,KAAK;MACvC,CAAC,CAAC,EACF/K,SAAS,CAAC,IAAI,CAACgL,sBAAsB,CAAC,CACvC,CACAoB,SAAS,CAAC;QACTK,IAAI,EAAGyF,iBAAsB,IAAI;UAC/B;UACA,IAAI,CAACxJ,mBAAmB,GAAGwJ,iBAAiB;UAE5C,IACEA,iBAAiB,EAAE3M,QAAQ,IAC3B2M,iBAAiB,EAAE3M,QAAQ,EAAEC,OAAO,EACpC;YACA,MAAM2M,cAAc,GAAGD,iBAAiB,CAAC3M,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,CAACC,IAAI;YACjE,IAAI,CAACtD,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;cAAEoK,IAAI,EAAE,IAAI;cAAE9G,IAAI,EAAE0M;YAAc,CAAE,CACrC;YACD,IAAIvG,gBAAgB,EAAE;cACpB,IAAI,CAACX,gBAAgB,CAACmD,IAAI,CAAC;gBACzB0D,OAAO,EAAEK,cAAc;gBACvBJ,IAAI,EAAE;eACP,CAAC;YACJ;UACF,CAAC,MAAM;YACLhG,OAAO,CAACqG,IAAI,CAAC,iCAAiC,EAAEF,iBAAiB,CAAC;YAClE,IAAI,CAACrE,cAAc,CACjB,+CAA+C,CAChD;UACH;QACF,CAAC;QACDb,KAAK,EAAGA,KAAU,IAAI;UACpBjB,OAAO,CAACiB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;UAClC,MAAMqF,YAAY,GAChBrF,KAAK,EAAEA,KAAK,EAAEY,OAAO,IACrB,kDAAkD;UACpD,IAAI,CAACC,cAAc,CAACwE,YAAY,CAAC;UACjC,IAAIzG,gBAAgB,IAAI,IAAI,CAACX,gBAAgB,CAACnI,MAAM,GAAG,CAAC,EAAE;YACxD,IAAI,CAACmI,gBAAgB,CAACqH,GAAG,EAAE;UAC7B;QACF;OACD,CAAC;IACN;IAEQV,8BAA8BA,CACpChE,OAAe,EACf2C,IAAY,EACZ9B,iBAAyB,EACzB7C,gBAAyB,EACzBC,aAAsB,EACtB0G,YAAoB;MAEpB,IAAI3G,gBAAgB,EAAE;QACpB,IAAI,CAACX,gBAAgB,CAACmD,IAAI,CAAC;UAAE0D,OAAO,EAAElE,OAAO;UAAEmE,IAAI,EAAE;QAAM,CAAE,CAAC;MAChE;MACA,MAAMhD,OAAO,GAAGnD,gBAAgB,GAAG,IAAI,CAACX,gBAAgB,GAAG2C,OAAO;MAClE,MAAM;QAAEoE;MAAO,CAAE,GAAG,IAAI,CAACnB,qBAAqB,EAAE;MAEhD,IAAI,CAACtJ,sBAAsB,CACxB0K,cAAc,CACblD,OAAO,EACPwB,IAAI,EACJ3E,gBAAgB,EAChBC,aAAa,EACb,IAAI,CAACf,eAAe,EACpB2D,iBAAiB,EACjB8D,YAAY,EACZP,OAAO,CACR,CACAzC,IAAI,CACHrP,QAAQ,CAAC,MAAK;QACZ,IAAI,CAACkC,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAAC2I,wBAAwB,GAAG,KAAK;MACvC,CAAC,CAAC,EACF/K,SAAS,CAAC,IAAI,CAACgL,sBAAsB,CAAC,CACvC,CACAoB,SAAS,CAAC;QACTK,IAAI,EAAGyF,iBAAsB,IAAI;UAC/B,IACEA,iBAAiB,EAAE3M,QAAQ,IAC3B2M,iBAAiB,EAAE3M,QAAQ,EAAEC,OAAO,EACpC;YACA,MAAM2M,cAAc,GAAGD,iBAAiB,CAAC3M,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,CAACC,IAAI;YACjE,IAAI,CAACtD,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;cAAEoK,IAAI,EAAE,IAAI;cAAE9G,IAAI,EAAE0M;YAAc,CAAE,CACrC;YACD,IAAIvG,gBAAgB,EAAE;cACpB,IAAI,CAACX,gBAAgB,CAACmD,IAAI,CAAC;gBACzB0D,OAAO,EAAEK,cAAc;gBACvBJ,IAAI,EAAE;eACP,CAAC;YACJ;UACF,CAAC,MAAM;YACLhG,OAAO,CAACqG,IAAI,CAAC,iCAAiC,EAAEF,iBAAiB,CAAC;YAClE,IAAI,CAACrE,cAAc,CACjB,+CAA+C,CAChD;UACH;QACF,CAAC;QACDb,KAAK,EAAGA,KAAU,IAAI;UACpBjB,OAAO,CAACiB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;UAClC,MAAMqF,YAAY,GAChBrF,KAAK,EAAEA,KAAK,EAAEY,OAAO,IACrB,kDAAkD;UACpD,IAAI,CAACC,cAAc,CAACwE,YAAY,CAAC;UACjC,IAAIzG,gBAAgB,IAAI,IAAI,CAACX,gBAAgB,CAACnI,MAAM,GAAG,CAAC,EAAE;YACxD,IAAI,CAACmI,gBAAgB,CAACqH,GAAG,EAAE;UAC7B;QACF;OACD,CAAC;IACN;IAEA;IACQ3E,gCAAgCA,CAACT,SAAc;MACrD,IAAI,CAACA,SAAS,EAAE;QACdnB,OAAO,CAACqG,IAAI,CAAC,sCAAsC,CAAC;QACpD;MACF;MAEArG,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEkB,SAAS,CAAC;MAC7DnB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC3J,SAAS,CAAC;MACpD0J,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEwG,MAAM,CAACC,IAAI,CAACvF,SAAS,CAAC,CAAC;MAEjE;MACA,IAAI,CAAC3C,eAAe,GAAG,EAAE;MACzB,IAAI,CAACC,WAAW,GAAG,EAAE;MAErB,IAAIkI,WAAW,GAAG,CAAC;MAEnB;MACA,IAAI,IAAI,CAACrQ,SAAS,KAAK,YAAY,EAAE;QACnC,IAAI,CAACsQ,6BAA6B,CAACzF,SAAS,EAAEwF,WAAW,CAAC;MAC5D,CAAC,MAAM,IAAI,IAAI,CAACrQ,SAAS,KAAK,eAAe,EAAE;QAC7C,IAAI,CAACuQ,gCAAgC,CAAC1F,SAAS,EAAEwF,WAAW,CAAC;MAC/D;MAEA3G,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE;QACxCzB,eAAe,EAAE,IAAI,CAACA,eAAe;QACrCC,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7BqI,UAAU,EAAE,IAAI,CAACtI,eAAe,CAACzH;OAClC,CAAC;IACJ;IAEQ6P,6BAA6BA,CACnCzF,SAAc,EACdwF,WAAmB;MAEnB3G,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAE;QAC5D8G,MAAM,EAAE5F,SAAS,CAAC4F,MAAM;QACxBC,YAAY,EAAE7F,SAAS,CAAC4F,MAAM,EAAEhQ,MAAM;QACtCyK,WAAW,EAAEL,SAAS,CAACK,WAAW;QAClClC,MAAM,EAAE6B,SAAS,CAAC7B,MAAM;QACxB2H,cAAc,EAAE9F,SAAS,CAAC8F;OAC3B,CAAC;MAEF;MACA,IAAI,CAACnQ,oBAAoB,GAAG,EAAE;MAC9B,IAAI,CAACS,mBAAmB,GAAG,EAAE;MAC7B,IAAI,CAACJ,uBAAuB,GAAG,EAAE;MACjC,IAAI,CAACqB,uBAAuB,GAAG,EAAE;MAEjC;MACA,IAAI2I,SAAS,CAAC7B,MAAM,EAAE;QACpB,IAAI,CAACxI,oBAAoB,CAACuL,IAAI,CAAC;UAC7BvG,EAAE,EAAE,UAAU6K,WAAW,EAAE,EAAE;UAC7BjQ,IAAI,EAAEyK,SAAS,CAAC7B,MAAM;UACtB4H,IAAI,EAAE;SACP,CAAC;QACFlH,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEkB,SAAS,CAAC7B,MAAM,CAAC;MACvD;MAEA;MACA,IAAI6B,SAAS,CAAC4F,MAAM,IAAI1F,KAAK,CAACC,OAAO,CAACH,SAAS,CAAC4F,MAAM,CAAC,EAAE;QACvD/G,OAAO,CAACC,GAAG,CACT,yCAAyC,EACzCkB,SAAS,CAAC4F,MAAM,CAAChQ,MAAM,CACxB;QAEDoK,SAAS,CAAC4F,MAAM,CAAC1B,OAAO,CAAC,CAAC8B,QAAa,EAAEC,aAAqB,KAAI;UAChEpH,OAAO,CAACC,GAAG,CACT,eAAemH,aAAa,SAASD,QAAQ,CAACE,UAAU,IAAI,EAC5DF,QAAQ,CAACG,YAAY,CACtB;UAED,IAAIH,QAAQ,CAACJ,MAAM,IAAI1F,KAAK,CAACC,OAAO,CAAC6F,QAAQ,CAACJ,MAAM,CAAC,EAAE;YACrD/G,OAAO,CAACC,GAAG,CACT,eAAemH,aAAa,QAAQD,QAAQ,CAACJ,MAAM,CAAChQ,MAAM,eAAe,CAC1E;YAEDoQ,QAAQ,CAACJ,MAAM,CAAC1B,OAAO,CAAC,CAACkC,UAAe,EAAEC,SAAiB,KAAI;cAC7DxH,OAAO,CAACC,GAAG,CAAC,kBAAkBmH,aAAa,IAAII,SAAS,GAAG,EAAE;gBAC3DC,SAAS,EAAEF,UAAU,CAACE,SAAS;gBAC/BC,WAAW,EAAEH,UAAU,CAACG,WAAW;gBACnCL,UAAU,EAAEE,UAAU,CAACF;eACxB,CAAC;cAEF;cACA,IACEE,UAAU,CAACF,UAAU,KAAK,CAAC,IAC3BE,UAAU,CAACE,SAAS,KAAK,OAAO,IAChCF,UAAU,CAACG,WAAW,EACtB;gBACA1H,OAAO,CAACC,GAAG,CACT,2CAA2C,EAC3CsH,UAAU,CAACG,WAAW,CACvB;gBACD,IAAI,CAACnQ,mBAAmB,CAAC8K,IAAI,CAAC;kBAC5BvG,EAAE,EAAE,SAAS6K,WAAW,EAAE,EAAE;kBAC5BjQ,IAAI,EAAE,GAAG6Q,UAAU,CAACE,SAAS,EAAE;kBAC/BP,IAAI,EAAE;iBACP,CAAC;cACJ;cAEA;cACA,IACEK,UAAU,CAACF,UAAU,KAAK,CAAC,IAC3BE,UAAU,CAACE,SAAS,KAAK,wBAAwB,IACjDF,UAAU,CAACG,WAAW,EACtB;gBACA1H,OAAO,CAACC,GAAG,CACT,kDAAkD,EAClDsH,UAAU,CAACG,WAAW,CACvB;gBACD,MAAMC,OAAO,GAAGJ,UAAU,CAACG,WAAW,CAACE,QAAQ,EAAE;gBACjD,MAAMC,KAAK,GAAGF,OAAO,CAClBG,KAAK,CAAC,GAAG,CAAC,CACV7F,GAAG,CAAEnG,EAAU,IAAKA,EAAE,CAACiM,IAAI,EAAE,CAAC,CAC9BC,MAAM,CAAElM,EAAU,IAAKA,EAAE,CAAC;gBAE7B+L,KAAK,CAACxC,OAAO,CAAE4C,IAAY,IAAI;kBAC7B,IAAI,CAAC9Q,uBAAuB,CAACkL,IAAI,CAAC;oBAChCvG,EAAE,EAAE,aAAa6K,WAAW,EAAE,EAAE;oBAChCjQ,IAAI,EAAE,mBAAmBuR,IAAI,EAAE;oBAC/Bf,IAAI,EAAE;mBACP,CAAC;gBACJ,CAAC,CAAC;cACJ;cAEA;cACA,IACEK,UAAU,CAACF,UAAU,KAAK,CAAC,IAC3BE,UAAU,CAACG,WAAW,KAAK,MAAM,EACjC;gBACA1H,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE;kBAC1DiI,GAAG,EAAEX,UAAU,CAACE,SAAS;kBACzBjF,KAAK,EAAE+E,UAAU,CAACG;iBACnB,CAAC;gBAEF,IAAIH,UAAU,CAACE,SAAS,KAAK,mBAAmB,EAAE;kBAChD;kBACA,IAAI,IAAI,CAACjP,uBAAuB,CAACzB,MAAM,KAAK,CAAC,EAAE;oBAC7C,IAAI,CAACyB,uBAAuB,CAAC6J,IAAI,CAAC;sBAChCvG,EAAE,EAAE,aAAa6K,WAAW,EAAE,EAAE;sBAChCjQ,IAAI,EAAE,oBAAoB;sBAC1BwQ,IAAI,EAAE;qBACP,CAAC;kBACJ;gBACF,CAAC,MAAM,IAAIK,UAAU,CAACE,SAAS,CAACU,UAAU,CAAC,YAAY,CAAC,EAAE;kBACxD;kBACA,IAAIC,aAAa,GAAGb,UAAU,CAACE,SAAS;kBACxC,IAAIW,aAAa,CAACD,UAAU,CAAC,YAAY,CAAC,EAAE;oBAC1CC,aAAa,GAAGA,aAAa,CAC1BlD,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CACzBA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;kBACvB;kBAEA,IAAI,CAAC1M,uBAAuB,CAAC6J,IAAI,CAAC;oBAChCvG,EAAE,EAAE,aAAa6K,WAAW,EAAE,EAAE;oBAChCjQ,IAAI,EAAE,GAAG0R,aAAa,EAAE;oBACxBlB,IAAI,EAAE;mBACP,CAAC;gBACJ;cACF;YACF,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ;MAEAlH,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;QACvCoI,WAAW,EAAE,IAAI,CAACvR,oBAAoB;QACtCwR,UAAU,EAAE,IAAI,CAAC/Q,mBAAmB;QACpCgR,cAAc,EAAE,IAAI,CAACpR,uBAAuB;QAC5CqR,cAAc,EAAE,IAAI,CAAChQ;OACtB,CAAC;MAEF;MACA,MAAMiQ,aAAa,GAAG,CAAC,CAAC,CAAC;MACzB,MAAMC,eAAe,GACnB,IAAI,CAAC5R,oBAAoB,CAACC,MAAM,GAAG,IAAI,CAACQ,mBAAmB,CAACR,MAAM;MACpE,IAAI,CAACsC,6BAA6B,GAAGsP,IAAI,CAACC,KAAK,CAC5CF,eAAe,GAAGD,aAAa,GAAI,GAAG,CACxC;IACH;IAEQ5B,gCAAgCA,CACtC1F,SAAc,EACdwF,WAAmB;MAEnB3G,OAAO,CAACC,GAAG,CACT,+DAA+D,CAChE;MACDD,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEkB,SAAS,CAAC;MACtEnB,OAAO,CAACC,GAAG,CACT,0CAA0C,EAC1CwG,MAAM,CAACC,IAAI,CAACvF,SAAS,CAAC,CACvB;MACDnB,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE,IAAI,CAAC3J,SAAS,CAAC;MACjE0J,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE0G,WAAW,CAAC;MAE1D;MACA,IAAI,CAAC7P,oBAAoB,GAAG,EAAE;MAC9B,IAAI,CAACS,mBAAmB,GAAG,EAAE;MAC7B,IAAI,CAACJ,uBAAuB,GAAG,EAAE;MACjC,IAAI,CAACS,kBAAkB,GAAG,EAAE;MAC5B,IAAI,CAACY,uBAAuB,GAAG,EAAE;MAEjCwH,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;MAE1D;MACA,MAAM4I,sBAAsB,GAC1B1H,SAAS,CAAC2H,IAAI,IAAI3H,SAAS,CAAC6E,IAAI,IAAI7E,SAAS,CAACrH,WAAW;MAE3DkG,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE;QACtD6I,IAAI,EAAE3H,SAAS,CAAC2H,IAAI;QACpB9C,IAAI,EAAE7E,SAAS,CAAC6E,IAAI;QACpBlM,WAAW,EAAEqH,SAAS,CAACrH,WAAW;QAClC+O;OACD,CAAC;MAEF,IAAIA,sBAAsB,EAAE;QAC1B,IAAIE,cAAc,GAChB5H,SAAS,CAAC2H,IAAI,IACd3H,SAAS,CAAC6E,IAAI,IACd7E,SAAS,CAACrH,WAAW,IACrB,4BAA4B;QAE9B;QACA,IAAIiP,cAAc,CAAChS,MAAM,GAAG,GAAG,EAAE;UAC/BgS,cAAc,GAAGA,cAAc,CAACC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK;QAC3D;QAEA,IAAI,CAAClS,oBAAoB,CAACuL,IAAI,CAAC;UAC7BvG,EAAE,EAAE,UAAU6K,WAAW,EAAE,EAAE;UAC7BjQ,IAAI,EAAEqS,cAAc;UACpB7B,IAAI,EAAE;SACP,CAAC;QACFlH,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE8I,cAAc,CAAC;MACnE;MAEA;MACA,IAAIE,eAAe,GAAG,EAAE;MAExBjJ,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;QAC5CiJ,eAAe,EAAE,CAAC,CAAC/H,SAAS,CAACgI,YAAY;QACzCA,YAAY,EAAEhI,SAAS,CAACgI,YAAY;QACpC5J,KAAK,EAAE4B,SAAS,CAAC5B,KAAK;QACtB6J,SAAS,EAAEjI,SAAS,CAACiI,SAAS;QAC9BC,YAAY,EAAElI,SAAS,CAACkI;OACzB,CAAC;MAEF;MACA,IAAIlI,SAAS,CAACgI,YAAY,IAAIhI,SAAS,CAACgI,YAAY,CAACG,QAAQ,EAAE;QAC7D,MAAMC,SAAS,GAAGlI,KAAK,CAACC,OAAO,CAACH,SAAS,CAACgI,YAAY,CAACG,QAAQ,CAAC,GAC5DnI,SAAS,CAACgI,YAAY,CAACG,QAAQ,GAC/B,CAACnI,SAAS,CAACgI,YAAY,CAACG,QAAQ,CAAC;QAErCL,eAAe,GAAGM,SAAS,CAACtH,GAAG,CAAEuH,GAAQ,IAAI;UAC3C,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;YACtD,OAAO;cAAEC,OAAO,EAAED;YAAG,CAAE;UACzB;UACA,OAAOA,GAAG;QACZ,CAAC,CAAC;MACJ;MACA;MAAA,KACK,IAAIrI,SAAS,CAACkI,YAAY,EAAE;QAC/BJ,eAAe,GAAG,CAAC9H,SAAS,CAACkI,YAAY,CAAC;MAC5C;MACA;MAAA,KACK,IAAIlI,SAAS,CAAC5B,KAAK,IAAI4B,SAAS,CAACiI,SAAS,EAAE;QAC/CH,eAAe,GAAG,CAAC;UAAEQ,OAAO,EAAEtI,SAAS,CAAC5B,KAAK,IAAI4B,SAAS,CAACiI;QAAS,CAAE,CAAC;MACzE;MAEAH,eAAe,CAAC5D,OAAO,CAAEiE,QAAa,IAAI;QACxC,MAAMG,OAAO,GAAGH,QAAQ,CAACG,OAAO,IAAIH,QAAQ,CAACxN,EAAE;QAC/C,MAAMsN,SAAS,GACbE,QAAQ,CAAC/J,KAAK,IACd+J,QAAQ,CAACI,mBAAmB,IAC5B,aAAaD,OAAO,EAAE;QAExB,IAAI,CAAClS,mBAAmB,CAAC8K,IAAI,CAAC;UAC5BvG,EAAE,EAAE,SAAS6K,WAAW,EAAE,EAAE;UAC5BjQ,IAAI,EAAE0S,SAAS;UACflC,IAAI,EAAE;SACP,CAAC;QACFlH,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEmJ,SAAS,CAAC;MAC7D,CAAC,CAAC;MAEF;MACA,IAAIO,mBAAmB,GAAG,EAAE;MAE5B;MACA,IAAIxI,SAAS,CAACgI,YAAY,IAAIhI,SAAS,CAACgI,YAAY,CAACS,gBAAgB,EAAE;QACrE,MAAMC,MAAM,GAAGxI,KAAK,CAACC,OAAO,CAACH,SAAS,CAACgI,YAAY,CAACS,gBAAgB,CAAC,GACjEzI,SAAS,CAACgI,YAAY,CAACS,gBAAgB,GACvC,CAACzI,SAAS,CAACgI,YAAY,CAACS,gBAAgB,CAAC;QAE7CD,mBAAmB,GAAGE,MAAM,CAAC5H,GAAG,CAAEuH,GAAQ,IAAI;UAC5C,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;YACtD,OAAO;cAAEM,eAAe,EAAEN;YAAG,CAAE;UACjC;UACA,OAAOA,GAAG;QACZ,CAAC,CAAC;MACJ;MACA;MAAA,KACK,IACHrI,SAAS,CAAC4I,aAAa,IACvB1I,KAAK,CAACC,OAAO,CAACH,SAAS,CAAC4I,aAAa,CAAC,EACtC;QACAJ,mBAAmB,GAAGxI,SAAS,CAAC4I,aAAa;MAC/C;MAEAJ,mBAAmB,CAACtE,OAAO,CAAE2E,KAAU,IAAI;QACzC,MAAM/B,IAAI,GAAG+B,KAAK,CAACF,eAAe,IAAIE,KAAK,CAAClO,EAAE;QAC9C,MAAMmO,cAAc,GAAGD,KAAK,CAACE,mBAAmB,IAAIF,KAAK,CAACtT,IAAI;QAC9D,MAAMyT,MAAM,GAAGF,cAAc,IAAI,sBAAsBhC,IAAI,EAAE;QAE7D,IAAI,CAAC9Q,uBAAuB,CAACkL,IAAI,CAAC;UAChCvG,EAAE,EAAE,aAAa6K,WAAW,EAAE,EAAE;UAChCjQ,IAAI,EAAEyT,MAAM;UACZjD,IAAI,EAAE;SACP,CAAC;QACFlH,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEkK,MAAM,CAAC;MAC9D,CAAC,CAAC;MAEF;MACA,IAAIC,cAAc,GAAG,EAAE;MACvB,IAAIC,kBAAkB,GAAG,EAAE;MAE3BrK,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;QAC3CiJ,eAAe,EAAE,CAAC,CAAC/H,SAAS,CAACgI,YAAY;QACzCmB,mBAAmB,EAAEnJ,SAAS,CAACgI,YAAY;QAC3CoB,QAAQ,EAAEpJ,SAAS,CAACqJ,KAAK;QACzBC,YAAY,EAAEtJ,SAAS,CAACqJ,KAAK;QAC7BE,YAAY,EAAEvJ,SAAS,CAACwJ,SAAS;QACjCC,gBAAgB,EAAEzJ,SAAS,CAACwJ;OAC7B,CAAC;MAEF;MACA,IAAIxJ,SAAS,CAACgI,YAAY,EAAE;QAC1B,IAAIhI,SAAS,CAACgI,YAAY,CAAC0B,OAAO,EAAE;UAClC,MAAMC,QAAQ,GAAGzJ,KAAK,CAACC,OAAO,CAACH,SAAS,CAACgI,YAAY,CAAC0B,OAAO,CAAC,GAC1D1J,SAAS,CAACgI,YAAY,CAAC0B,OAAO,GAC9B,CAAC1J,SAAS,CAACgI,YAAY,CAAC0B,OAAO,CAAC;UAEpCT,cAAc,GAAGU,QAAQ,CAAC7I,GAAG,CAAEuH,GAAQ,IAAI;YACzC,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;cACtD,OAAO;gBAAEuB,MAAM,EAAEvB;cAAG,CAAE;YACxB;YACA,OAAOA,GAAG;UACZ,CAAC,CAAC;QACJ;QACA,IAAIrI,SAAS,CAACgI,YAAY,CAAC6B,WAAW,EAAE;UACtC,MAAMC,YAAY,GAAG5J,KAAK,CAACC,OAAO,CAACH,SAAS,CAACgI,YAAY,CAAC6B,WAAW,CAAC,GAClE7J,SAAS,CAACgI,YAAY,CAAC6B,WAAW,GAClC,CAAC7J,SAAS,CAACgI,YAAY,CAAC6B,WAAW,CAAC;UAExCX,kBAAkB,GAAGY,YAAY,CAAChJ,GAAG,CAAEuH,GAAQ,IAAI;YACjD,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;cACtD,OAAO;gBAAEuB,MAAM,EAAEvB;cAAG,CAAE;YACxB;YACA,OAAOA,GAAG;UACZ,CAAC,CAAC;QACJ;MACF;MACA;MAAA,KACK;QACH,IAAIrI,SAAS,CAACqJ,KAAK,IAAInJ,KAAK,CAACC,OAAO,CAACH,SAAS,CAACqJ,KAAK,CAAC,EAAE;UACrDJ,cAAc,GAAGjJ,SAAS,CAACqJ,KAAK;QAClC;QACA,IAAIrJ,SAAS,CAACwJ,SAAS,IAAItJ,KAAK,CAACC,OAAO,CAACH,SAAS,CAACwJ,SAAS,CAAC,EAAE;UAC7DN,kBAAkB,GAAGlJ,SAAS,CAACwJ,SAAS;QAC1C;MACF;MAEA;MACAP,cAAc,CAAC/E,OAAO,CAAE3F,IAAS,IAAI;QACnC,MAAMqL,MAAM,GAAGrL,IAAI,CAACqL,MAAM,IAAIrL,IAAI,CAAC5D,EAAE;QACrC,MAAMoP,QAAQ,GAAGxL,IAAI,CAACwL,QAAQ,IAAIxL,IAAI,CAAChJ,IAAI,IAAI,YAAYqU,MAAM,EAAE;QAEnE,IAAI,CAACnT,kBAAkB,CAACyK,IAAI,CAAC;UAC3BvG,EAAE,EAAE,QAAQ6K,WAAW,EAAE,EAAE;UAC3BjQ,IAAI,EAAEwU,QAAQ;UACdhE,IAAI,EAAE;SACP,CAAC;QACFlH,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEiL,QAAQ,CAAC;MACnE,CAAC,CAAC;MAEF;MACAb,kBAAkB,CAAChF,OAAO,CAAE8F,QAAa,IAAI;QAC3C,MAAMC,UAAU,GAAGD,QAAQ,CAACJ,MAAM,IAAII,QAAQ,CAACrP,EAAE;QACjD,MAAMuP,YAAY,GAChBF,QAAQ,CAACD,QAAQ,IAAIC,QAAQ,CAACzU,IAAI,IAAI,iBAAiB0U,UAAU,EAAE;QAErE,IAAI,CAACxT,kBAAkB,CAACyK,IAAI,CAAC;UAC3BvG,EAAE,EAAE,QAAQ6K,WAAW,EAAE,EAAE;UAC3BjQ,IAAI,EAAE2U,YAAY;UAClBnE,IAAI,EAAE;SACP,CAAC;QACFlH,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEoL,YAAY,CAAC;MACpE,CAAC,CAAC;MAEFrL,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE;QACrDoI,WAAW,EAAE,IAAI,CAACvR,oBAAoB;QACtCwR,UAAU,EAAE,IAAI,CAAC/Q,mBAAmB;QACpCgR,cAAc,EAAE,IAAI,CAACpR,uBAAuB;QAC5CmU,SAAS,EAAE,IAAI,CAAC1T,kBAAkB;QAClC4Q,cAAc,EAAE,IAAI,CAAChQ;OACtB,CAAC;MAEF;MACAwH,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;QACvCsL,WAAW,EAAE,IAAI,CAACzU,oBAAoB,CAACC,MAAM;QAC7CyU,UAAU,EAAE,IAAI,CAACjU,mBAAmB,CAACR,MAAM;QAC3C0U,cAAc,EAAE,IAAI,CAACtU,uBAAuB,CAACJ,MAAM;QACnD2U,SAAS,EAAE,IAAI,CAAC9T,kBAAkB,CAACb,MAAM;QACzC4U,cAAc,EAAE,IAAI,CAACnT,uBAAuB,CAACzB;OAC9C,CAAC;MAEF;MACAiJ,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;QAClC3J,SAAS,EAAE,IAAI,CAACA,SAAS;QACzBsV,eAAe,EAAE,IAAI,CAACtV,SAAS,KAAK,eAAe;QACnDuV,YAAY,EAAE,IAAI,CAACjU,kBAAkB,CAACb,MAAM,GAAG,CAAC;QAChD+U,aAAa,EAAE,IAAI,CAAClU,kBAAkB,CAACqK,GAAG,CAAE8J,CAAC,IAAKA,CAAC,CAACrV,IAAI;OACzD,CAAC;MAEF;MACA,MAAM+R,aAAa,GAAG,CAAC,CAAC,CAAC;MACzB,MAAMC,eAAe,GACnB,IAAI,CAAC5R,oBAAoB,CAACC,MAAM,GAAG,IAAI,CAACQ,mBAAmB,CAACR,MAAM;MACpE,IAAI,CAACsC,6BAA6B,GAAGsP,IAAI,CAACC,KAAK,CAC5CF,eAAe,GAAGD,aAAa,GAAI,GAAG,CACxC;IACH;;uCA/tCWrN,uBAAuB,EAAAzG,EAAA,CAAAqX,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAvX,EAAA,CAAAqX,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAxX,EAAA,CAAAqX,iBAAA,CAAAI,EAAA,CAAAC,mBAAA,GAAA1X,EAAA,CAAAqX,iBAAA,CAAAM,EAAA,CAAAC,sBAAA,GAAA5X,EAAA,CAAAqX,iBAAA,CAAAQ,EAAA,CAAAC,mBAAA,GAAA9X,EAAA,CAAAqX,iBAAA,CAAAU,EAAA,CAAAC,aAAA,GAAAhY,EAAA,CAAAqX,iBAAA,CAAAY,EAAA,CAAAC,WAAA,GAAAlY,EAAA,CAAAqX,iBAAA,CAAAc,EAAA,CAAAC,oBAAA;IAAA;;YAAvB3R,uBAAuB;MAAA4R,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBAavB5Y,iCAAiC;;;;;;;;;;;;UC1CxCI,EAJN,CAAAC,cAAA,aAAuC,aAEZ,aACD,gBAC+C;UAAvCD,EAAA,CAAAI,UAAA,mBAAAsY,yDAAA;YAAA,OAASD,GAAA,CAAA9I,YAAA,EAAc;UAAA,EAAC;UAClD3P,EAAA,CAAA6C,SAAA,kBAIY;UACZ7C,EAAA,CAAAC,cAAA,cAAyB;UAAAD,EAAA,CAAAE,MAAA,GAAe;UAG9CF,EAH8C,CAAAG,YAAA,EAAO,EACxC,EACL,EACF;UAOAH,EAJN,CAAAC,cAAA,aAA0B,aAEyC,aACrC,iBAC+C;UAA1CD,EAAA,CAAAI,UAAA,mBAAAuY,0DAAA;YAAA,OAASF,GAAA,CAAAzI,eAAA,EAAiB;UAAA,EAAC;UACtDhQ,EAAA,CAAA6C,SAAA,oBAKW;UACb7C,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAgC,UAAA,KAAA4W,0CAAA,qBAKC;UAGH5Y,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAgC,UAAA,KAAA6W,uCAAA,mBAAyD;UAuB3D7Y,EAAA,CAAAG,YAAA,EAAM;UAOAH,EAJN,CAAAC,cAAA,eAAyB,cAEG,eACI,kBAKzB;UADCD,EAAA,CAAAI,UAAA,mBAAA0Y,0DAAA;YAAA,OAASL,GAAA,CAAAxJ,iBAAA,CAAkB,WAAW,CAAC;UAAA,EAAC;UAExCjP,EAAA,CAAAE,MAAA,mBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAIC;UADCD,EAAA,CAAAI,UAAA,mBAAA2Y,0DAAA;YAAA,OAASN,GAAA,CAAAxJ,iBAAA,CAAkB,QAAQ,CAAC;UAAA,EAAC;UAErCjP,EAAA,CAAAE,MAAA,sBACF;UAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;UAENH,EAAA,CAAAC,cAAA,eAA2B;UAkkBzBD,EAhkBA,CAAAgC,UAAA,KAAAgX,uCAAA,oBAAsE,KAAAC,uCAAA,kBAgkBN;UA6ExEjZ,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACF;;;UA1tB2BH,EAAA,CAAAuB,SAAA,GAAe;UAAfvB,EAAA,CAAA6B,iBAAA,CAAA4W,GAAA,CAAA7W,SAAA,CAAe;UAQpB5B,EAAA,CAAAuB,SAAA,GAAwC;UAAxCvB,EAAA,CAAAuD,WAAA,cAAAkV,GAAA,CAAA/O,oBAAA,CAAwC;UAIxD1J,EAAA,CAAAuB,SAAA,GAAgE;UAAhEvB,EAAA,CAAAwB,UAAA,aAAAiX,GAAA,CAAA/O,oBAAA,gCAAgE;UAQjE1J,EAAA,CAAAuB,SAAA,EAA2B;UAA3BvB,EAAA,CAAAwB,UAAA,UAAAiX,GAAA,CAAA/O,oBAAA,CAA2B;UAQJ1J,EAAA,CAAAuB,SAAA,EAA2B;UAA3BvB,EAAA,CAAAwB,UAAA,UAAAiX,GAAA,CAAA/O,oBAAA,CAA2B;UAgCjD1J,EAAA,CAAAuB,SAAA,GAA+C;UAA/CvB,EAAA,CAAAuD,WAAA,WAAAkV,GAAA,CAAA9O,cAAA,iBAA+C;UAO/C3J,EAAA,CAAAuB,SAAA,GAA4C;UAA5CvB,EAAA,CAAAuD,WAAA,WAAAkV,GAAA,CAAA9O,cAAA,cAA4C;UAU1C3J,EAAA,CAAAuB,SAAA,GAAoC;UAApCvB,EAAA,CAAAwB,UAAA,SAAAiX,GAAA,CAAA9O,cAAA,iBAAoC;UAgkBpC3J,EAAA,CAAAuB,SAAA,EAAiC;UAAjCvB,EAAA,CAAAwB,UAAA,SAAAiX,GAAA,CAAA9O,cAAA,cAAiC;;;qBD9nB3CvK,YAAY,EAAA8Z,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,QAAA,EACZ1Z,WAAW,EACXC,iCAAiC,EACjCC,aAAa;MAAAyZ,MAAA;IAAA;;SAKJ7S,uBAAuB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}