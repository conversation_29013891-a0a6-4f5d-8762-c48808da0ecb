{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { CommonModule, DatePipe } from '@angular/common';\nimport { Component, Input } from '@angular/core';\nimport { AvaTextboxComponent, ButtonComponent, IconComponent, SliderComponent } from '@ava/play-comp-library';\nimport { PreviewPanelComponent } from '../../../../components/preview-panel/preview-panel.component';\nlet AgentsPreviewPanelComponent = class AgentsPreviewPanelComponent {\n  previewData = null;\n  closePreview;\n  showMoreConfig = false;\n  toggleConfigDetails() {\n    this.showMoreConfig = !this.showMoreConfig;\n  }\n  onButtonClick(event) {\n    this.closePreview();\n  }\n  getAdditionalFields(data) {\n    const excludeFields = ['id', 'name', 'description', 'labelCode', 'categoryName', 'categoryId'];\n    return Object.keys(data).filter(key => !excludeFields.includes(key) && data[key] != null).map(key => ({\n      key,\n      value: data[key]\n    }));\n  }\n  getFileIconColor(index) {\n    const colors = ['#dc2626', '#2563eb', '#16a34a', '#f59e0b', '#8b5cf6'];\n    return colors[index % colors.length];\n  }\n  getButtonLabel() {\n    const type = this.previewData?.type;\n    switch (type) {\n      case 'model':\n        return 'Edit Model';\n      case 'tool':\n        return 'Edit Tool';\n      case 'prompt':\n        return 'Edit Prompt';\n      case 'knowledge':\n        return 'Edit Knowledge Base';\n      case 'guardrail':\n        return 'Edit Guardrail';\n      default:\n        return 'Edit';\n    }\n  }\n};\n__decorate([Input()], AgentsPreviewPanelComponent.prototype, \"previewData\", void 0);\n__decorate([Input()], AgentsPreviewPanelComponent.prototype, \"closePreview\", void 0);\nAgentsPreviewPanelComponent = __decorate([Component({\n  selector: 'app-agents-preview-panel',\n  imports: [PreviewPanelComponent, IconComponent, ButtonComponent, DatePipe, CommonModule, SliderComponent, AvaTextboxComponent],\n  templateUrl: './agents-preview-panel.component.html',\n  styleUrl: './agents-preview-panel.component.scss'\n})], AgentsPreviewPanelComponent);\nexport { AgentsPreviewPanelComponent };", "map": {"version": 3, "names": ["CommonModule", "DatePipe", "Component", "Input", "AvaTextboxComponent", "ButtonComponent", "IconComponent", "SliderComponent", "PreviewPanelComponent", "AgentsPreviewPanelComponent", "previewData", "closePreview", "showMoreConfig", "toggleConfigDetails", "onButtonClick", "event", "getAdditionalFields", "data", "excludeFields", "Object", "keys", "filter", "key", "includes", "map", "value", "getFileIconColor", "index", "colors", "length", "getButtonLabel", "type", "__decorate", "selector", "imports", "templateUrl", "styleUrl"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\agents\\build-agents\\agents-preview-panel\\agents-preview-panel.component.ts"], "sourcesContent": ["import { CommonModule, DatePipe } from '@angular/common';\r\nimport { Component, Input } from '@angular/core';\r\nimport { AvaTextboxComponent, ButtonComponent, IconComponent, SliderComponent } from '@ava/play-comp-library';\r\nimport { PreviewPanelComponent } from '../../../../components/preview-panel/preview-panel.component';\r\n\r\n@Component({\r\n  selector: 'app-agents-preview-panel',\r\n  imports: [PreviewPanelComponent,IconComponent,ButtonComponent,DatePipe, CommonModule,SliderComponent,AvaTextboxComponent],\r\n  templateUrl: './agents-preview-panel.component.html',\r\n  styleUrl: './agents-preview-panel.component.scss'\r\n})\r\nexport class AgentsPreviewPanelComponent {\r\n  @Input() previewData: any = null;\r\n  @Input() closePreview!: () => void;\r\n  \r\n  showMoreConfig = false;\r\n\r\n  toggleConfigDetails(): void {\r\n    this.showMoreConfig = !this.showMoreConfig;\r\n  }\r\n\r\n  onButtonClick(event: any): void {\r\n    this.closePreview();\r\n  }\r\n\r\n  getAdditionalFields(data: any): { key: string; value: any }[] {\r\n    const excludeFields = ['id', 'name', 'description', 'labelCode', 'categoryName', 'categoryId'];\r\n    return Object.keys(data)\r\n      .filter(key => !excludeFields.includes(key) && data[key] != null)\r\n      .map(key => ({ key, value: data[key] }));\r\n  }\r\n\r\n  getFileIconColor(index: number): string {\r\n    const colors = ['#dc2626', '#2563eb', '#16a34a', '#f59e0b', '#8b5cf6'];\r\n    return colors[index % colors.length];\r\n  }\r\n\r\n  getButtonLabel(): string {\r\n    const type = this.previewData?.type;\r\n    switch (type) {\r\n      case 'model':\r\n        return 'Edit Model';\r\n      case 'tool':\r\n        return 'Edit Tool';\r\n      case 'prompt':\r\n        return 'Edit Prompt';\r\n      case 'knowledge':\r\n        return 'Edit Knowledge Base';\r\n      case 'guardrail':\r\n        return 'Edit Guardrail';\r\n      default:\r\n        return 'Edit';\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,YAAY,EAAEC,QAAQ,QAAQ,iBAAiB;AACxD,SAASC,SAAS,EAAEC,KAAK,QAAQ,eAAe;AAChD,SAASC,mBAAmB,EAAEC,eAAe,EAAEC,aAAa,EAAEC,eAAe,QAAQ,wBAAwB;AAC7G,SAASC,qBAAqB,QAAQ,8DAA8D;AAQ7F,IAAMC,2BAA2B,GAAjC,MAAMA,2BAA2B;EAC7BC,WAAW,GAAQ,IAAI;EACvBC,YAAY;EAErBC,cAAc,GAAG,KAAK;EAEtBC,mBAAmBA,CAAA;IACjB,IAAI,CAACD,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;EAC5C;EAEAE,aAAaA,CAACC,KAAU;IACtB,IAAI,CAACJ,YAAY,EAAE;EACrB;EAEAK,mBAAmBA,CAACC,IAAS;IAC3B,MAAMC,aAAa,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,cAAc,EAAE,YAAY,CAAC;IAC9F,OAAOC,MAAM,CAACC,IAAI,CAACH,IAAI,CAAC,CACrBI,MAAM,CAACC,GAAG,IAAI,CAACJ,aAAa,CAACK,QAAQ,CAACD,GAAG,CAAC,IAAIL,IAAI,CAACK,GAAG,CAAC,IAAI,IAAI,CAAC,CAChEE,GAAG,CAACF,GAAG,KAAK;MAAEA,GAAG;MAAEG,KAAK,EAAER,IAAI,CAACK,GAAG;IAAC,CAAE,CAAC,CAAC;EAC5C;EAEAI,gBAAgBA,CAACC,KAAa;IAC5B,MAAMC,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;IACtE,OAAOA,MAAM,CAACD,KAAK,GAAGC,MAAM,CAACC,MAAM,CAAC;EACtC;EAEAC,cAAcA,CAAA;IACZ,MAAMC,IAAI,GAAG,IAAI,CAACrB,WAAW,EAAEqB,IAAI;IACnC,QAAQA,IAAI;MACV,KAAK,OAAO;QACV,OAAO,YAAY;MACrB,KAAK,MAAM;QACT,OAAO,WAAW;MACpB,KAAK,QAAQ;QACX,OAAO,aAAa;MACtB,KAAK,WAAW;QACd,OAAO,qBAAqB;MAC9B,KAAK,WAAW;QACd,OAAO,gBAAgB;MACzB;QACE,OAAO,MAAM;IACjB;EACF;CACD;AA1CUC,UAAA,EAAR7B,KAAK,EAAE,C,+DAAyB;AACxB6B,UAAA,EAAR7B,KAAK,EAAE,C,gEAA2B;AAFxBM,2BAA2B,GAAAuB,UAAA,EANvC9B,SAAS,CAAC;EACT+B,QAAQ,EAAE,0BAA0B;EACpCC,OAAO,EAAE,CAAC1B,qBAAqB,EAACF,aAAa,EAACD,eAAe,EAACJ,QAAQ,EAAED,YAAY,EAACO,eAAe,EAACH,mBAAmB,CAAC;EACzH+B,WAAW,EAAE,uCAAuC;EACpDC,QAAQ,EAAE;CACX,CAAC,C,EACW3B,2BAA2B,CA2CvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}