{"ast": null, "code": "function streamGeometry(geometry, stream) {\n  if (geometry && streamGeometryType.hasOwnProperty(geometry.type)) {\n    streamGeometryType[geometry.type](geometry, stream);\n  }\n}\nvar streamObjectType = {\n  Feature: function (object, stream) {\n    streamGeometry(object.geometry, stream);\n  },\n  FeatureCollection: function (object, stream) {\n    var features = object.features,\n      i = -1,\n      n = features.length;\n    while (++i < n) streamGeometry(features[i].geometry, stream);\n  }\n};\nvar streamGeometryType = {\n  Sphere: function (object, stream) {\n    stream.sphere();\n  },\n  Point: function (object, stream) {\n    object = object.coordinates;\n    stream.point(object[0], object[1], object[2]);\n  },\n  MultiPoint: function (object, stream) {\n    var coordinates = object.coordinates,\n      i = -1,\n      n = coordinates.length;\n    while (++i < n) object = coordinates[i], stream.point(object[0], object[1], object[2]);\n  },\n  LineString: function (object, stream) {\n    streamLine(object.coordinates, stream, 0);\n  },\n  MultiLineString: function (object, stream) {\n    var coordinates = object.coordinates,\n      i = -1,\n      n = coordinates.length;\n    while (++i < n) streamLine(coordinates[i], stream, 0);\n  },\n  Polygon: function (object, stream) {\n    streamPolygon(object.coordinates, stream);\n  },\n  MultiPolygon: function (object, stream) {\n    var coordinates = object.coordinates,\n      i = -1,\n      n = coordinates.length;\n    while (++i < n) streamPolygon(coordinates[i], stream);\n  },\n  GeometryCollection: function (object, stream) {\n    var geometries = object.geometries,\n      i = -1,\n      n = geometries.length;\n    while (++i < n) streamGeometry(geometries[i], stream);\n  }\n};\nfunction streamLine(coordinates, stream, closed) {\n  var i = -1,\n    n = coordinates.length - closed,\n    coordinate;\n  stream.lineStart();\n  while (++i < n) coordinate = coordinates[i], stream.point(coordinate[0], coordinate[1], coordinate[2]);\n  stream.lineEnd();\n}\nfunction streamPolygon(coordinates, stream) {\n  var i = -1,\n    n = coordinates.length;\n  stream.polygonStart();\n  while (++i < n) streamLine(coordinates[i], stream, 1);\n  stream.polygonEnd();\n}\nexport default function (object, stream) {\n  if (object && streamObjectType.hasOwnProperty(object.type)) {\n    streamObjectType[object.type](object, stream);\n  } else {\n    streamGeometry(object, stream);\n  }\n}", "map": {"version": 3, "names": ["streamGeometry", "geometry", "stream", "streamGeometryType", "hasOwnProperty", "type", "streamObjectType", "Feature", "object", "FeatureCollection", "features", "i", "n", "length", "Sphere", "sphere", "Point", "coordinates", "point", "MultiPoint", "LineString", "streamLine", "MultiLineString", "Polygon", "streamPolygon", "MultiPolygon", "GeometryCollection", "geometries", "closed", "coordinate", "lineStart", "lineEnd", "polygonStart", "polygonEnd"], "sources": ["C:/console/aava-ui-web/node_modules/d3-geo/src/stream.js"], "sourcesContent": ["function streamGeometry(geometry, stream) {\n  if (geometry && streamGeometryType.hasOwnProperty(geometry.type)) {\n    streamGeometryType[geometry.type](geometry, stream);\n  }\n}\n\nvar streamObjectType = {\n  Feature: function(object, stream) {\n    streamGeometry(object.geometry, stream);\n  },\n  FeatureCollection: function(object, stream) {\n    var features = object.features, i = -1, n = features.length;\n    while (++i < n) streamGeometry(features[i].geometry, stream);\n  }\n};\n\nvar streamGeometryType = {\n  Sphere: function(object, stream) {\n    stream.sphere();\n  },\n  Point: function(object, stream) {\n    object = object.coordinates;\n    stream.point(object[0], object[1], object[2]);\n  },\n  MultiPoint: function(object, stream) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) object = coordinates[i], stream.point(object[0], object[1], object[2]);\n  },\n  LineString: function(object, stream) {\n    streamLine(object.coordinates, stream, 0);\n  },\n  MultiLineString: function(object, stream) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) streamLine(coordinates[i], stream, 0);\n  },\n  Polygon: function(object, stream) {\n    streamPolygon(object.coordinates, stream);\n  },\n  MultiPolygon: function(object, stream) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) streamPolygon(coordinates[i], stream);\n  },\n  GeometryCollection: function(object, stream) {\n    var geometries = object.geometries, i = -1, n = geometries.length;\n    while (++i < n) streamGeometry(geometries[i], stream);\n  }\n};\n\nfunction streamLine(coordinates, stream, closed) {\n  var i = -1, n = coordinates.length - closed, coordinate;\n  stream.lineStart();\n  while (++i < n) coordinate = coordinates[i], stream.point(coordinate[0], coordinate[1], coordinate[2]);\n  stream.lineEnd();\n}\n\nfunction streamPolygon(coordinates, stream) {\n  var i = -1, n = coordinates.length;\n  stream.polygonStart();\n  while (++i < n) streamLine(coordinates[i], stream, 1);\n  stream.polygonEnd();\n}\n\nexport default function(object, stream) {\n  if (object && streamObjectType.hasOwnProperty(object.type)) {\n    streamObjectType[object.type](object, stream);\n  } else {\n    streamGeometry(object, stream);\n  }\n}\n"], "mappings": "AAAA,SAASA,cAAcA,CAACC,QAAQ,EAAEC,MAAM,EAAE;EACxC,IAAID,QAAQ,IAAIE,kBAAkB,CAACC,cAAc,CAACH,QAAQ,CAACI,IAAI,CAAC,EAAE;IAChEF,kBAAkB,CAACF,QAAQ,CAACI,IAAI,CAAC,CAACJ,QAAQ,EAAEC,MAAM,CAAC;EACrD;AACF;AAEA,IAAII,gBAAgB,GAAG;EACrBC,OAAO,EAAE,SAAAA,CAASC,MAAM,EAAEN,MAAM,EAAE;IAChCF,cAAc,CAACQ,MAAM,CAACP,QAAQ,EAAEC,MAAM,CAAC;EACzC,CAAC;EACDO,iBAAiB,EAAE,SAAAA,CAASD,MAAM,EAAEN,MAAM,EAAE;IAC1C,IAAIQ,QAAQ,GAAGF,MAAM,CAACE,QAAQ;MAAEC,CAAC,GAAG,CAAC,CAAC;MAAEC,CAAC,GAAGF,QAAQ,CAACG,MAAM;IAC3D,OAAO,EAAEF,CAAC,GAAGC,CAAC,EAAEZ,cAAc,CAACU,QAAQ,CAACC,CAAC,CAAC,CAACV,QAAQ,EAAEC,MAAM,CAAC;EAC9D;AACF,CAAC;AAED,IAAIC,kBAAkB,GAAG;EACvBW,MAAM,EAAE,SAAAA,CAASN,MAAM,EAAEN,MAAM,EAAE;IAC/BA,MAAM,CAACa,MAAM,CAAC,CAAC;EACjB,CAAC;EACDC,KAAK,EAAE,SAAAA,CAASR,MAAM,EAAEN,MAAM,EAAE;IAC9BM,MAAM,GAAGA,MAAM,CAACS,WAAW;IAC3Bf,MAAM,CAACgB,KAAK,CAACV,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;EAC/C,CAAC;EACDW,UAAU,EAAE,SAAAA,CAASX,MAAM,EAAEN,MAAM,EAAE;IACnC,IAAIe,WAAW,GAAGT,MAAM,CAACS,WAAW;MAAEN,CAAC,GAAG,CAAC,CAAC;MAAEC,CAAC,GAAGK,WAAW,CAACJ,MAAM;IACpE,OAAO,EAAEF,CAAC,GAAGC,CAAC,EAAEJ,MAAM,GAAGS,WAAW,CAACN,CAAC,CAAC,EAAET,MAAM,CAACgB,KAAK,CAACV,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;EACxF,CAAC;EACDY,UAAU,EAAE,SAAAA,CAASZ,MAAM,EAAEN,MAAM,EAAE;IACnCmB,UAAU,CAACb,MAAM,CAACS,WAAW,EAAEf,MAAM,EAAE,CAAC,CAAC;EAC3C,CAAC;EACDoB,eAAe,EAAE,SAAAA,CAASd,MAAM,EAAEN,MAAM,EAAE;IACxC,IAAIe,WAAW,GAAGT,MAAM,CAACS,WAAW;MAAEN,CAAC,GAAG,CAAC,CAAC;MAAEC,CAAC,GAAGK,WAAW,CAACJ,MAAM;IACpE,OAAO,EAAEF,CAAC,GAAGC,CAAC,EAAES,UAAU,CAACJ,WAAW,CAACN,CAAC,CAAC,EAAET,MAAM,EAAE,CAAC,CAAC;EACvD,CAAC;EACDqB,OAAO,EAAE,SAAAA,CAASf,MAAM,EAAEN,MAAM,EAAE;IAChCsB,aAAa,CAAChB,MAAM,CAACS,WAAW,EAAEf,MAAM,CAAC;EAC3C,CAAC;EACDuB,YAAY,EAAE,SAAAA,CAASjB,MAAM,EAAEN,MAAM,EAAE;IACrC,IAAIe,WAAW,GAAGT,MAAM,CAACS,WAAW;MAAEN,CAAC,GAAG,CAAC,CAAC;MAAEC,CAAC,GAAGK,WAAW,CAACJ,MAAM;IACpE,OAAO,EAAEF,CAAC,GAAGC,CAAC,EAAEY,aAAa,CAACP,WAAW,CAACN,CAAC,CAAC,EAAET,MAAM,CAAC;EACvD,CAAC;EACDwB,kBAAkB,EAAE,SAAAA,CAASlB,MAAM,EAAEN,MAAM,EAAE;IAC3C,IAAIyB,UAAU,GAAGnB,MAAM,CAACmB,UAAU;MAAEhB,CAAC,GAAG,CAAC,CAAC;MAAEC,CAAC,GAAGe,UAAU,CAACd,MAAM;IACjE,OAAO,EAAEF,CAAC,GAAGC,CAAC,EAAEZ,cAAc,CAAC2B,UAAU,CAAChB,CAAC,CAAC,EAAET,MAAM,CAAC;EACvD;AACF,CAAC;AAED,SAASmB,UAAUA,CAACJ,WAAW,EAAEf,MAAM,EAAE0B,MAAM,EAAE;EAC/C,IAAIjB,CAAC,GAAG,CAAC,CAAC;IAAEC,CAAC,GAAGK,WAAW,CAACJ,MAAM,GAAGe,MAAM;IAAEC,UAAU;EACvD3B,MAAM,CAAC4B,SAAS,CAAC,CAAC;EAClB,OAAO,EAAEnB,CAAC,GAAGC,CAAC,EAAEiB,UAAU,GAAGZ,WAAW,CAACN,CAAC,CAAC,EAAET,MAAM,CAACgB,KAAK,CAACW,UAAU,CAAC,CAAC,CAAC,EAAEA,UAAU,CAAC,CAAC,CAAC,EAAEA,UAAU,CAAC,CAAC,CAAC,CAAC;EACtG3B,MAAM,CAAC6B,OAAO,CAAC,CAAC;AAClB;AAEA,SAASP,aAAaA,CAACP,WAAW,EAAEf,MAAM,EAAE;EAC1C,IAAIS,CAAC,GAAG,CAAC,CAAC;IAAEC,CAAC,GAAGK,WAAW,CAACJ,MAAM;EAClCX,MAAM,CAAC8B,YAAY,CAAC,CAAC;EACrB,OAAO,EAAErB,CAAC,GAAGC,CAAC,EAAES,UAAU,CAACJ,WAAW,CAACN,CAAC,CAAC,EAAET,MAAM,EAAE,CAAC,CAAC;EACrDA,MAAM,CAAC+B,UAAU,CAAC,CAAC;AACrB;AAEA,eAAe,UAASzB,MAAM,EAAEN,MAAM,EAAE;EACtC,IAAIM,MAAM,IAAIF,gBAAgB,CAACF,cAAc,CAACI,MAAM,CAACH,IAAI,CAAC,EAAE;IAC1DC,gBAAgB,CAACE,MAAM,CAACH,IAAI,CAAC,CAACG,MAAM,EAAEN,MAAM,CAAC;EAC/C,CAAC,MAAM;IACLF,cAAc,CAACQ,MAAM,EAAEN,MAAM,CAAC;EAChC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}