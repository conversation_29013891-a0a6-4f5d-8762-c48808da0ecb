{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { Subject, takeUntil, switchMap, finalize, catchError, of } from 'rxjs';\nimport { FormsModule } from '@angular/forms';\nimport { AgentExecutionPlaygroundComponent } from './components/agent-execution-playground/agent-execution-playground.component';\nimport { IconComponent } from '@ava/play-comp-library';\nimport { environment } from 'projects/console/src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../services/agent-service.service\";\nimport * as i3 from \"../build-agents/services/agent-playground.service\";\nimport * as i4 from \"@shared/auth/services/token-storage.service\";\nimport * as i5 from \"../../../shared/services/loader/loader.service\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"../../../shared/services/tool-execution/tool-execution.service\";\nimport * as i8 from \"@angular/common\";\nfunction AgentExecutionComponent_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 19);\n    i0.ɵɵtext(1, \" History \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"app-agent-execution-playground\", 20);\n    i0.ɵɵlistener(\"promptChange\", function AgentExecutionComponent_div_13_Template_app_agent_execution_playground_promptChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPromptChanged($event));\n    })(\"messageSent\", function AgentExecutionComponent_div_13_Template_app_agent_execution_playground_messageSent_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.handleChatMessage($event));\n    })(\"conversationalToggle\", function AgentExecutionComponent_div_13_Template_app_agent_execution_playground_conversationalToggle_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPlaygroundConversationalToggle($event));\n    })(\"templateToggle\", function AgentExecutionComponent_div_13_Template_app_agent_execution_playground_templateToggle_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPlaygroundTemplateToggle($event));\n    })(\"filesSelected\", function AgentExecutionComponent_div_13_Template_app_agent_execution_playground_filesSelected_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFilesSelected($event));\n    })(\"approvalRequested\", function AgentExecutionComponent_div_13_Template_app_agent_execution_playground_approvalRequested_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onApprovalRequested());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"messages\", ctx_r1.chatMessages)(\"isLoading\", ctx_r1.isProcessingChat)(\"agentType\", ctx_r1.agentType)(\"showChatInteractionToggles\", ctx_r1.agentType === \"individual\")(\"showAiPrincipleToggle\", true)(\"showApprovalButton\", false)(\"showDropdown\", false)(\"showAgentNameInput\", true)(\"showFileUploadButton\", true)(\"displayedAgentName\", ctx_r1.agentName)(\"agentNamePlaceholder\", \"Current Agent Name\")(\"acceptedFileType\", ctx_r1.fileType);\n  }\n}\nfunction AgentExecutionComponent_div_22_div_40_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76);\n    i0.ɵɵtext(1, \" No prompt configured \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_22_div_40_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77)(1, \"span\", 78);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(node_r4.name || \"Prompt\");\n  }\n}\nfunction AgentExecutionComponent_div_22_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72);\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_22_div_40_div_1_Template, 2, 0, \"div\", 73);\n    i0.ɵɵelementStart(2, \"div\", 74);\n    i0.ɵɵtemplate(3, AgentExecutionComponent_div_22_div_40_div_3_Template, 3, 1, \"div\", 75);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.blueprintPromptNodes.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.blueprintPromptNodes);\n  }\n}\nfunction AgentExecutionComponent_div_22_div_61_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76);\n    i0.ɵɵtext(1, \" No knowledge base configured \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_22_div_61_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77)(1, \"span\", 78);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(node_r5.name || \"Knowledge Base\");\n  }\n}\nfunction AgentExecutionComponent_div_22_div_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72);\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_22_div_61_div_1_Template, 2, 0, \"div\", 73);\n    i0.ɵɵelementStart(2, \"div\", 74);\n    i0.ɵɵtemplate(3, AgentExecutionComponent_div_22_div_61_div_3_Template, 3, 1, \"div\", 75);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.blueprintKnowledgeNodes.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.blueprintKnowledgeNodes);\n  }\n}\nfunction AgentExecutionComponent_div_22_div_79_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76);\n    i0.ɵɵtext(1, \" No model configured \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_22_div_79_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77)(1, \"span\", 78);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(node_r6.name || \"Model\");\n  }\n}\nfunction AgentExecutionComponent_div_22_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72);\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_22_div_79_div_1_Template, 2, 0, \"div\", 73);\n    i0.ɵɵelementStart(2, \"div\", 74);\n    i0.ɵɵtemplate(3, AgentExecutionComponent_div_22_div_79_div_3_Template, 3, 1, \"div\", 75);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.blueprintModelNodes.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.blueprintModelNodes);\n  }\n}\nfunction AgentExecutionComponent_div_22_div_95_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76);\n    i0.ɵɵtext(1, \" No guardrails configured \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_22_div_95_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77)(1, \"span\", 78);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(node_r7.name || \"Guardrail\");\n  }\n}\nfunction AgentExecutionComponent_div_22_div_95_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72);\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_22_div_95_div_1_Template, 2, 0, \"div\", 73);\n    i0.ɵɵelementStart(2, \"div\", 74);\n    i0.ɵɵtemplate(3, AgentExecutionComponent_div_22_div_95_div_3_Template, 3, 1, \"div\", 75);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.blueprintGuardrailNodes.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.blueprintGuardrailNodes);\n  }\n}\nfunction AgentExecutionComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22)(2, \"h3\");\n    i0.ɵɵtext(3, \"Agent Blueprint\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 23);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(5, \"svg\", 24);\n    i0.ɵɵelement(6, \"line\", 25)(7, \"line\", 26)(8, \"line\", 27)(9, \"line\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(10, \"div\", 29)(11, \"div\", 30);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(12, \"svg\", 31)(13, \"defs\")(14, \"linearGradient\", 32);\n    i0.ɵɵelement(15, \"stop\", 33)(16, \"stop\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(17, \"circle\", 35)(18, \"circle\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(19, \"div\", 37)(20, \"div\", 38);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 39);\n    i0.ɵɵtext(23, \"Complete\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(24, \"div\", 40)(25, \"div\", 41)(26, \"div\", 42);\n    i0.ɵɵlistener(\"click\", function AgentExecutionComponent_div_22_Template_div_click_26_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleBlueprintZone(\"prompt\"));\n    });\n    i0.ɵɵelementStart(27, \"div\", 43)(28, \"div\", 44);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(29, \"svg\", 45);\n    i0.ɵɵelement(30, \"rect\", 46)(31, \"path\", 47);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(32, \"h3\", 48);\n    i0.ɵɵtext(33, \"System Prompt\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 49)(35, \"span\", 50);\n    i0.ɵɵtext(36, \"Required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"button\", 51);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(38, \"svg\", 52);\n    i0.ɵɵelement(39, \"path\", 53);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(40, AgentExecutionComponent_div_22_div_40_Template, 4, 2, \"div\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(41, \"div\", 55)(42, \"div\", 42);\n    i0.ɵɵlistener(\"click\", function AgentExecutionComponent_div_22_Template_div_click_42_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleBlueprintZone(\"knowledge\"));\n    });\n    i0.ɵɵelementStart(43, \"div\", 43)(44, \"div\", 44);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(45, \"svg\", 45);\n    i0.ɵɵelement(46, \"rect\", 56)(47, \"path\", 57)(48, \"path\", 58)(49, \"path\", 59)(50, \"path\", 60)(51, \"path\", 61)(52, \"path\", 62);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(53, \"h3\", 48);\n    i0.ɵɵtext(54, \"Knowledgebase\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(55, \"div\", 49)(56, \"span\", 63);\n    i0.ɵɵtext(57, \"Optional\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"button\", 51);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(59, \"svg\", 52);\n    i0.ɵɵelement(60, \"path\", 53);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(61, AgentExecutionComponent_div_22_div_61_Template, 4, 2, \"div\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(62, \"div\", 64)(63, \"div\", 42);\n    i0.ɵɵlistener(\"click\", function AgentExecutionComponent_div_22_Template_div_click_63_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleBlueprintZone(\"model\"));\n    });\n    i0.ɵɵelementStart(64, \"div\", 43)(65, \"div\", 44);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(66, \"svg\", 45);\n    i0.ɵɵelement(67, \"rect\", 65)(68, \"path\", 66)(69, \"path\", 67)(70, \"path\", 68);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(71, \"h3\", 48);\n    i0.ɵɵtext(72, \"AI Model\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(73, \"div\", 49)(74, \"span\", 50);\n    i0.ɵɵtext(75, \"Required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(76, \"button\", 51);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(77, \"svg\", 52);\n    i0.ɵɵelement(78, \"path\", 53);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(79, AgentExecutionComponent_div_22_div_79_Template, 4, 2, \"div\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(80, \"div\", 69)(81, \"div\", 42);\n    i0.ɵɵlistener(\"click\", function AgentExecutionComponent_div_22_Template_div_click_81_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleBlueprintZone(\"guardrail\"));\n    });\n    i0.ɵɵelementStart(82, \"div\", 43)(83, \"div\", 44);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(84, \"svg\", 45);\n    i0.ɵɵelement(85, \"rect\", 70)(86, \"path\", 71);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(87, \"h3\", 48);\n    i0.ɵɵtext(88, \"Guardrails\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(89, \"div\", 49)(90, \"span\", 63);\n    i0.ɵɵtext(91, \"Optional\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(92, \"button\", 51);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(93, \"svg\", 52);\n    i0.ɵɵelement(94, \"path\", 53);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(95, AgentExecutionComponent_div_22_div_95_Template, 4, 2, \"div\", 54);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(18);\n    i0.ɵɵstyleProp(\"stroke-dasharray\", 314)(\"stroke-dashoffset\", 314 - 314 * ctx_r1.blueprintCompletionPercentage / 100);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.blueprintCompletionPercentage, \"% \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"has-nodes\", ctx_r1.blueprintPromptNodes.length > 0);\n    i0.ɵɵadvance(13);\n    i0.ɵɵstyleProp(\"transform\", ctx_r1.isBlueprintZoneExpanded(\"prompt\") ? \"rotate(180deg)\" : \"rotate(0deg)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isBlueprintZoneExpanded(\"prompt\"));\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"has-nodes\", ctx_r1.blueprintKnowledgeNodes.length > 0);\n    i0.ɵɵadvance(18);\n    i0.ɵɵstyleProp(\"transform\", ctx_r1.isBlueprintZoneExpanded(\"knowledge\") ? \"rotate(180deg)\" : \"rotate(0deg)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isBlueprintZoneExpanded(\"knowledge\"));\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"has-nodes\", ctx_r1.blueprintModelNodes.length > 0);\n    i0.ɵɵadvance(15);\n    i0.ɵɵstyleProp(\"transform\", ctx_r1.isBlueprintZoneExpanded(\"model\") ? \"rotate(180deg)\" : \"rotate(0deg)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isBlueprintZoneExpanded(\"model\"));\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"has-nodes\", ctx_r1.blueprintGuardrailNodes.length > 0);\n    i0.ɵɵadvance(13);\n    i0.ɵɵstyleProp(\"transform\", ctx_r1.isBlueprintZoneExpanded(\"guardrail\") ? \"rotate(180deg)\" : \"rotate(0deg)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isBlueprintZoneExpanded(\"guardrail\"));\n  }\n}\nfunction AgentExecutionComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 79)(1, \"div\", 80)(2, \"h3\");\n    i0.ɵɵtext(3, \"Agent Output\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"Agent responses and generated content will be displayed here.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 81)(7, \"div\", 82)(8, \"h4\");\n    i0.ɵɵtext(9, \"Portfolio website HTML Code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 83);\n    i0.ɵɵtext(11, \"Test Agent 1's Output\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 84)(13, \"div\", 85);\n    i0.ɵɵtext(14, \" <!DOCTYPE html>\");\n    i0.ɵɵelement(15, \"br\");\n    i0.ɵɵtext(16, \" <html lang=\\\"en\\\">\");\n    i0.ɵɵelement(17, \"br\");\n    i0.ɵɵtext(18, \" <head>\");\n    i0.ɵɵelement(19, \"br\");\n    i0.ɵɵtext(20, \" \\u00A0\\u00A0<meta charset=\\\"UTF-8\\\">\");\n    i0.ɵɵelement(21, \"br\");\n    i0.ɵɵtext(22, \" \\u00A0\\u00A0<title>My Portfolio</title>\");\n    i0.ɵɵelement(23, \"br\");\n    i0.ɵɵtext(24, \" </head>\");\n    i0.ɵɵelement(25, \"br\");\n    i0.ɵɵtext(26, \" <body>\");\n    i0.ɵɵelement(27, \"br\");\n    i0.ɵɵtext(28, \" \\u00A0\\u00A0<h1>Jane Doe</h1>\");\n    i0.ɵɵelement(29, \"br\");\n    i0.ɵɵtext(30, \" </body>\");\n    i0.ɵɵelement(31, \"br\");\n    i0.ɵɵtext(32, \" </html> \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"button\", 86);\n    i0.ɵɵtext(34, \"Preview\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n}\nexport let AgentExecutionComponent = /*#__PURE__*/(() => {\n  class AgentExecutionComponent {\n    route;\n    router;\n    agentService;\n    agentPlaygroundService;\n    tokenStorage;\n    loaderService;\n    formBuilder;\n    toolExecutionService;\n    navigationTabs = [{\n      id: 'nav-home',\n      label: 'Agent Activity'\n    }, {\n      id: 'nav-products',\n      label: 'Agent Output'\n    }, {\n      id: 'nav-services',\n      label: 'Preview',\n      disabled: true\n    }];\n    agentId = null;\n    agentType = 'individual';\n    agentName = 'Agent';\n    agentDetail = '';\n    playgroundComp;\n    activityLogs = [];\n    activityProgress = 0;\n    executionDetails;\n    isRunning = false;\n    status = 'notStarted';\n    chatMessages = [];\n    isProcessingChat = false;\n    inputText = '';\n    agentOutputs = [];\n    agentForm;\n    fileType = '.pdf,.doc,.docx,.txt,.csv,.xlsx,.xls,.ppt,.pptx,.png,.jpg,.jpeg,.gif,.bmp,.svg';\n    executionStartTime = null;\n    executionCompleted = false;\n    executionId;\n    enableStreamingLog = environment.enableLogStreaming || 'all';\n    isExecutionComplete = false;\n    progressInterval;\n    destroy$ = new Subject();\n    selectedTab = 'Agent Activity';\n    demoTabs = [{\n      id: 'tab1',\n      label: 'History'\n    }, {\n      id: 'tab2',\n      label: 'Blueprint'\n    }, {\n      id: 'tab3',\n      label: 'Agent Output'\n    }];\n    errorMsg = false;\n    resMessage;\n    taskMessage = [];\n    isJsonValid = false;\n    disableChat = false;\n    selectedFiles = [];\n    agentNodes = [];\n    userInputList = [];\n    progress = 0;\n    isLoading = false;\n    loaderColor = '';\n    inputFieldOrder = [];\n    currentInputIndex = 0;\n    activeTabId = 'nav-home';\n    isLeftPanelCollapsed = false;\n    activeRightTab = 'blueprint';\n    currentAgentDetails = null;\n    buildAgentNodes = [];\n    canvasNodes = [];\n    canvasEdges = [];\n    selectedPrompt = '';\n    selectedAgentMode = '';\n    selectedUseCaseIdentifier = '';\n    agentFilesUploadedData = [];\n    agentAttachment = [];\n    isAgentPlaygroundLoading = false;\n    agentPlaygroundDestroy = new Subject();\n    agentChatPayload = [];\n    agentCode = '';\n    promptOptions = [];\n    blueprintCompletionPercentage = 0;\n    blueprintPromptNodes = [];\n    blueprintModelNodes = [];\n    blueprintKnowledgeNodes = [];\n    blueprintGuardrailNodes = [];\n    blueprintToolNodes = [];\n    blueprintZonesExpanded = {\n      prompt: true,\n      model: true,\n      knowledge: true,\n      guardrail: true,\n      tool: true\n    };\n    constructor(route, router, agentService, agentPlaygroundService, tokenStorage, loaderService, formBuilder, toolExecutionService) {\n      this.route = route;\n      this.router = router;\n      this.agentService = agentService;\n      this.agentPlaygroundService = agentPlaygroundService;\n      this.tokenStorage = tokenStorage;\n      this.loaderService = loaderService;\n      this.formBuilder = formBuilder;\n      this.toolExecutionService = toolExecutionService;\n      this.agentForm = this.formBuilder.group({\n        isConversational: [true],\n        isUseTemplate: [false]\n      });\n    }\n    ngOnInit() {\n      this.executionId = crypto.randomUUID();\n      this.route.params.subscribe(params => {\n        this.agentType = params['type'] || 'individual';\n      });\n      this.route.queryParams.subscribe(params => {\n        if (params['id']) {\n          this.agentId = params['id'];\n          this.loadAgentData(params['id']);\n        }\n      });\n      this.chatMessages = [{\n        from: 'ai',\n        text: `Hi there! I am ${this.agentName || 'your agent'}. How can I help you today?`\n      }];\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n      if (this.progressInterval) {\n        clearInterval(this.progressInterval);\n      }\n    }\n    onTabChange(event) {\n      this.activeTabId = event.id;\n      this.selectedTab = event.label;\n    }\n    loadAgentData(agentId) {\n      this.isLoading = true;\n      if (this.agentType === 'collaborative') {\n        this.agentService.getCollaborativeAgentDetailsById(agentId).subscribe({\n          next: response => {\n            this.handleAgentDataResponse(response);\n          },\n          error: error => {\n            console.error('Error loading collaborative agent:', error);\n            this.isLoading = false;\n          }\n        });\n      } else {\n        this.agentService.getAgentById(agentId).subscribe({\n          next: response => {\n            this.handleAgentDataResponse(response);\n          },\n          error: error => {\n            console.error('Error loading individual agent:', error);\n            this.isLoading = false;\n          }\n        });\n      }\n    }\n    handleAgentDataResponse(response) {\n      this.isLoading = false;\n      let agentData;\n      if (response.agentDetails && Array.isArray(response.agentDetails) && response.agentDetails.length > 0) {\n        agentData = response.agentDetails[0];\n      } else if (response.agentDetail) {\n        agentData = response.agentDetail;\n      } else if (response.data) {\n        agentData = response.data;\n      } else {\n        agentData = response;\n      }\n      if (agentData) {\n        this.currentAgentDetails = agentData;\n        this.agentName = agentData.name || agentData.agentName || 'Agent';\n        this.agentDetail = agentData.description || agentData.agentDetail || '';\n        this.selectedPrompt = this.agentName;\n        if (this.chatMessages.length > 0) {\n          this.chatMessages[0].text = `Hi there! I am ${this.agentName}. How can I help you today?`;\n        }\n        this.loadAgentNodes(agentData);\n      }\n    }\n    loadAgentNodes(agentData) {\n      this.mapAgentConfigurationToBlueprint(agentData);\n    }\n    handleChatMessage(message) {\n      if (this.agentType === 'individual') {\n        if (!this.selectedPrompt || this.selectedPrompt === 'default') {\n          this.showAgentError('Please select an agent from the dropdown before testing.');\n          return;\n        }\n        let displayMessage = message;\n        if (this.agentFilesUploadedData.length > 0) {\n          const fileNames = this.agentFilesUploadedData.map(file => file.documentName).join(', ');\n          displayMessage = `${message}\\n\\n📎 Attached files: ${fileNames}`;\n        }\n        this.chatMessages = [...this.chatMessages, {\n          from: 'user',\n          text: displayMessage\n        }];\n        this.isProcessingChat = true;\n        const isConversational = this.agentForm.get('isConversational')?.value || false;\n        const isUseTemplate = this.agentForm.get('isUseTemplate')?.value || false;\n        // Set mode and useCaseIdentifier based on agent details\n        const agentMode = this.currentAgentDetails.useCaseName || this.agentName;\n        const useCaseIdentifier = this.currentAgentDetails.organizationPath || this.currentAgentDetails.useCaseCode;\n        if (this.agentFilesUploadedData.length > 0) {\n          this.processAgentFilesAndSendMessage(message, agentMode, useCaseIdentifier, isConversational, isUseTemplate);\n          return;\n        }\n        this.sendAgentMessageToAPI(message, agentMode, useCaseIdentifier, isConversational, isUseTemplate);\n      } else if (this.agentType === 'collaborative') {\n        this.isProcessingChat = true;\n        let payload = {\n          executionId: this.executionId,\n          agentId: Number(this.agentId),\n          user: this.tokenStorage.getDaUsername() || '<EMAIL>',\n          userInputs: {\n            question: message\n          }\n        };\n        if (this.agentFilesUploadedData.length > 0) {\n          const fileWrapper = this.agentFilesUploadedData[0];\n          let displayMessage;\n          if (this.agentFilesUploadedData.length > 0) {\n            const fileNames = this.agentFilesUploadedData.map(file => file.documentName).join(', ');\n            displayMessage = `📎 Attached files: ${fileNames}`;\n            this.chatMessages = [{\n              from: 'user',\n              text: displayMessage\n            }];\n          }\n          this.agentPlaygroundService.submitAgentExecuteWithFile(payload, fileWrapper).pipe(finalize(() => {\n            this.isProcessingChat = false;\n            this.isAgentPlaygroundLoading = false;\n          }), takeUntil(this.agentPlaygroundDestroy)).subscribe({\n            next: res => this.handleAgentExecuteResponse(res, message),\n            error: err => {\n              this.chatMessages = [...this.chatMessages, {\n                from: 'user',\n                text: message\n              }, {\n                from: 'ai',\n                text: err?.error?.message || err?.message || 'Something went wrong.'\n              }];\n            }\n          });\n        } else {\n          this.agentPlaygroundService.submitAgentExecute(payload).pipe(finalize(() => {\n            this.isProcessingChat = false;\n            this.isAgentPlaygroundLoading = false;\n          }), takeUntil(this.agentPlaygroundDestroy)).subscribe({\n            next: res => this.handleAgentExecuteResponse(res, message),\n            error: err => {\n              this.chatMessages = [...this.chatMessages, {\n                from: 'user',\n                text: message\n              }, {\n                from: 'ai',\n                text: err?.error?.message || err?.message || 'Something went wrong.'\n              }];\n            }\n          });\n        }\n      }\n    }\n    onPromptChanged(prompt) {\n      this.inputText = prompt.name || String(prompt.value) || '';\n    }\n    onPlaygroundConversationalToggle(value) {}\n    onPlaygroundTemplateToggle(value) {}\n    onFilesSelected(files) {\n      this.selectedFiles = files;\n    }\n    onApprovalRequested() {}\n    saveLogs() {}\n    exportResults(section) {}\n    handleControlAction(action) {}\n    navigateBack() {\n      this.router.navigate(['/build/agents', this.agentType], {\n        queryParams: {\n          id: this.agentId,\n          mode: 'view'\n        }\n      });\n    }\n    editAgent() {\n      this.router.navigate(['/build/agents', this.agentType], {\n        queryParams: {\n          id: this.agentId,\n          mode: 'edit'\n        }\n      });\n    }\n    toggleLeftPanel() {\n      this.isLeftPanelCollapsed = !this.isLeftPanelCollapsed;\n    }\n    setActiveRightTab(tab) {\n      this.activeRightTab = tab;\n    }\n    toggleBlueprintZone(zoneType) {\n      this.blueprintZonesExpanded[zoneType] = !this.blueprintZonesExpanded[zoneType];\n    }\n    isBlueprintZoneExpanded(zoneType) {\n      return this.blueprintZonesExpanded[zoneType] || false;\n    }\n    showAgentError(message) {\n      this.chatMessages = [...this.chatMessages, {\n        from: 'ai',\n        text: message\n      }];\n    }\n    buildOrganizationPath() {\n      return '';\n    }\n    getMetadataFromNavbar() {\n      return {};\n    }\n    handleAgentExecuteResponse(response, message) {\n      try {\n        const outputRaw = response?.agentResponse?.agent?.output;\n        let formattedOutput = '';\n        if (outputRaw) {\n          formattedOutput = outputRaw.replace(/\\\\n/g, '\\n');\n        } else {\n          formattedOutput = response?.agentResponse?.detail;\n        }\n        this.chatMessages = [...this.chatMessages, {\n          from: 'user',\n          text: message\n        }, {\n          from: 'ai',\n          text: formattedOutput || 'No response from agent.'\n        }];\n      } catch (err) {\n        this.chatMessages = [...this.chatMessages, {\n          from: 'ai',\n          text: err?.message || 'Agent response could not be processed.'\n        }];\n      }\n    }\n    processAgentFilesAndSendMessage(message, mode, useCaseIdentifier, isConversational, isUseTemplate) {\n      const formData = new FormData();\n      this.agentFilesUploadedData.forEach(fileData => {\n        if (fileData.file) {\n          formData.append('files', fileData.file);\n        }\n      });\n      if (formData.has('files')) {\n        this.agentPlaygroundService.getFileToContent(formData).pipe(switchMap(fileResponse => {\n          const fileContent = fileResponse?.fileResponses?.map(response => response.fileContent)?.join('\\n') || '';\n          this.sendAgentMessageToAPIWithFiles(message, mode, useCaseIdentifier, isConversational, isUseTemplate, fileContent);\n          return of(null);\n        }), catchError(error => {\n          console.error('Error parsing files:', error);\n          this.sendAgentMessageToAPI(message, mode, useCaseIdentifier, isConversational, isUseTemplate);\n          return of(null);\n        })).subscribe();\n      } else {\n        this.sendAgentMessageToAPI(message, mode, useCaseIdentifier, isConversational, isUseTemplate);\n      }\n    }\n    sendAgentMessageToAPI(message, mode, useCaseIdentifier, isConversational, isUseTemplate) {\n      if (isConversational) {\n        this.agentChatPayload.push({\n          content: message,\n          role: 'user'\n        });\n      }\n      const payload = isConversational ? this.agentChatPayload : message;\n      const {\n        levelId\n      } = this.getMetadataFromNavbar();\n      this.agentPlaygroundService.generatePrompt(payload, mode, isConversational, isUseTemplate, this.agentAttachment, useCaseIdentifier, '', levelId).pipe(finalize(() => {\n        this.isProcessingChat = false;\n        this.isAgentPlaygroundLoading = false;\n      }), takeUntil(this.agentPlaygroundDestroy)).subscribe({\n        next: generatedResponse => {\n          if (generatedResponse?.response && generatedResponse?.response?.choices) {\n            const aiResponseText = generatedResponse.response.choices[0].text;\n            this.chatMessages = [...this.chatMessages, {\n              from: 'ai',\n              text: aiResponseText\n            }];\n            if (isConversational) {\n              this.agentChatPayload.push({\n                content: aiResponseText,\n                role: 'assistant'\n              });\n            }\n          } else {\n            console.warn('Unexpected API response format:', generatedResponse);\n            this.showAgentError('Received unexpected response format from API.');\n          }\n        },\n        error: error => {\n          console.error('API Error:', error);\n          const errorMessage = error?.error?.message || 'An error occurred while processing your request.';\n          this.showAgentError(errorMessage);\n          if (isConversational && this.agentChatPayload.length > 0) {\n            this.agentChatPayload.pop();\n          }\n        }\n      });\n    }\n    sendAgentMessageToAPIWithFiles(message, mode, useCaseIdentifier, isConversational, isUseTemplate, fileContents) {\n      if (isConversational) {\n        this.agentChatPayload.push({\n          content: message,\n          role: 'user'\n        });\n      }\n      const payload = isConversational ? this.agentChatPayload : message;\n      const {\n        levelId\n      } = this.getMetadataFromNavbar();\n      this.agentPlaygroundService.generatePrompt(payload, mode, isConversational, isUseTemplate, this.agentAttachment, useCaseIdentifier, fileContents, levelId).pipe(finalize(() => {\n        this.isProcessingChat = false;\n        this.isAgentPlaygroundLoading = false;\n      }), takeUntil(this.agentPlaygroundDestroy)).subscribe({\n        next: generatedResponse => {\n          if (generatedResponse?.response && generatedResponse?.response?.choices) {\n            const aiResponseText = generatedResponse.response.choices[0].text;\n            this.chatMessages = [...this.chatMessages, {\n              from: 'ai',\n              text: aiResponseText\n            }];\n            if (isConversational) {\n              this.agentChatPayload.push({\n                content: aiResponseText,\n                role: 'assistant'\n              });\n            }\n          } else {\n            console.warn('Unexpected API response format:', generatedResponse);\n            this.showAgentError('Received unexpected response format from API.');\n          }\n        },\n        error: error => {\n          console.error('API Error:', error);\n          const errorMessage = error?.error?.message || 'An error occurred while processing your request.';\n          this.showAgentError(errorMessage);\n          if (isConversational && this.agentChatPayload.length > 0) {\n            this.agentChatPayload.pop();\n          }\n        }\n      });\n    }\n    mapAgentConfigurationToBlueprint(agentData) {\n      if (!agentData) {\n        console.warn('No agent data provided for blueprint');\n        return;\n      }\n      this.buildAgentNodes = [];\n      this.canvasNodes = [];\n      let nodeCounter = 1;\n      if (this.agentType === 'individual') {\n        this.mapIndividualAgentToBlueprint(agentData, nodeCounter);\n      } else if (this.agentType === 'collaborative') {\n        this.mapCollaborativeAgentToBlueprint(agentData, nodeCounter);\n      }\n    }\n    mapIndividualAgentToBlueprint(agentData, nodeCounter) {\n      this.blueprintPromptNodes = [];\n      this.blueprintModelNodes = [];\n      this.blueprintKnowledgeNodes = [];\n      this.blueprintGuardrailNodes = [];\n      if (agentData.useCaseName || agentData.promptTemplate) {\n        this.blueprintPromptNodes.push({\n          id: `prompt-${nodeCounter++}`,\n          name: agentData.useCaseName || agentData.promptTemplate || 'System Prompt',\n          type: 'prompt'\n        });\n      }\n      if (agentData.config && Array.isArray(agentData.config)) {\n        agentData.config.forEach(category => {\n          if (category.config && Array.isArray(category.config)) {\n            category.config.forEach(configItem => {\n              if (configItem.configKey === 'MODEL' && configItem.configValue) {\n                this.blueprintModelNodes.push({\n                  id: `model-${nodeCounter++}`,\n                  name: `Model: ${configItem.configValue}`,\n                  type: 'model'\n                });\n              }\n              if (configItem.configKey === 'RAG_KNOWLEDGEBASE_NAME' && configItem.configValue) {\n                const kbValue = configItem.configValue.toString();\n                const kbIds = kbValue.split(',').map(id => id.trim()).filter(id => id);\n                kbIds.forEach(kbId => {\n                  this.blueprintKnowledgeNodes.push({\n                    id: `knowledge-${nodeCounter++}`,\n                    name: `Knowledge Base: ${kbId}`,\n                    type: 'knowledge'\n                  });\n                });\n              }\n              if ((configItem.configKey === 'GUARDRAIL' || configItem.configKey.startsWith('GUARDRAIL_')) && configItem.configValue && configItem.configValue !== 'false') {\n                let guardrailName = configItem.configKey;\n                if (guardrailName.startsWith('GUARDRAIL_')) {\n                  guardrailName = guardrailName.replace('GUARDRAIL_', '');\n                }\n                this.blueprintGuardrailNodes.push({\n                  id: `guardrail-${nodeCounter++}`,\n                  name: `Guardrail: ${guardrailName}`,\n                  type: 'guardrail'\n                });\n              }\n            });\n          }\n        });\n      }\n      const totalRequired = 2;\n      const totalOptional = this.blueprintKnowledgeNodes.length + this.blueprintGuardrailNodes.length;\n      const currentRequired = this.blueprintPromptNodes.length + this.blueprintModelNodes.length;\n      this.blueprintCompletionPercentage = Math.round(currentRequired / totalRequired * 100);\n    }\n    mapCollaborativeAgentToBlueprint(agentData, nodeCounter) {\n      this.blueprintPromptNodes = [];\n      this.blueprintModelNodes = [];\n      this.blueprintKnowledgeNodes = [];\n      this.blueprintToolNodes = [];\n      if (agentData.workflow && agentData.workflow.length > 0) {\n        const workflowItem = agentData.workflow[0];\n        if (workflowItem.goal) {\n          this.blueprintPromptNodes.push({\n            id: `prompt-${nodeCounter++}`,\n            name: workflowItem.goal || 'Collaborative Goal',\n            type: 'prompt'\n          });\n        }\n      }\n      if (agentData.model || agentData.modelName) {\n        this.blueprintModelNodes.push({\n          id: `model-${nodeCounter++}`,\n          name: agentData.model || agentData.modelName || 'Default Model',\n          type: 'model'\n        });\n      }\n      if (agentData.workflow && agentData.workflow.length > 0) {\n        agentData.workflow.forEach(workflowItem => {\n          if (workflowItem.tools && workflowItem.tools.length > 0) {\n            workflowItem.tools.forEach(tool => {\n              this.blueprintToolNodes.push({\n                id: `tool-${nodeCounter++}`,\n                name: tool.name || tool.toolName || 'Tool',\n                type: 'tool'\n              });\n            });\n          }\n        });\n      }\n      if (agentData.knowledgeBase && agentData.knowledgeBase.length > 0) {\n        agentData.knowledgeBase.forEach(kb => {\n          this.blueprintKnowledgeNodes.push({\n            id: `knowledge-${nodeCounter++}`,\n            name: kb.name || kb.documentName || 'Knowledge Base',\n            type: 'knowledge'\n          });\n        });\n      }\n      const totalRequired = 2;\n      const currentRequired = this.blueprintPromptNodes.length + this.blueprintModelNodes.length;\n      this.blueprintCompletionPercentage = Math.round(currentRequired / totalRequired * 100);\n    }\n    static ɵfac = function AgentExecutionComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AgentExecutionComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AgentServiceService), i0.ɵɵdirectiveInject(i3.AgentPlaygroundService), i0.ɵɵdirectiveInject(i4.TokenStorageService), i0.ɵɵdirectiveInject(i5.LoaderService), i0.ɵɵdirectiveInject(i6.FormBuilder), i0.ɵɵdirectiveInject(i7.ToolExecutionService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AgentExecutionComponent,\n      selectors: [[\"app-agent-execution\"]],\n      viewQuery: function AgentExecutionComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(AgentExecutionPlaygroundComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.playgroundComp = _t.first);\n        }\n      },\n      decls: 24,\n      vars: 12,\n      consts: [[1, \"agent-execution-container\"], [1, \"top-nav-bar\"], [1, \"nav-left\"], [\"type\", \"button\", 1, \"back-button\", 3, \"click\"], [\"iconName\", \"ArrowLeft\", \"iconSize\", \"16\", \"iconColor\", \"#374151\"], [1, \"agent-name\"], [1, \"main-content\"], [1, \"left-panel\"], [1, \"panel-header\"], [\"type\", \"button\", 1, \"collapse-btn\", 3, \"click\"], [\"iconSize\", \"16\", \"iconColor\", \"#6B7280\", 3, \"iconName\"], [\"class\", \"history-btn\", \"type\", \"button\", \"disabled\", \"\", 4, \"ngIf\"], [\"class\", \"panel-content\", 4, \"ngIf\"], [1, \"right-panel\"], [1, \"tabs-container\"], [1, \"tab-btn\", 3, \"click\"], [1, \"panel-content\"], [\"class\", \"blueprint-content\", 4, \"ngIf\"], [\"class\", \"output-content\", 4, \"ngIf\"], [\"type\", \"button\", \"disabled\", \"\", 1, \"history-btn\"], [3, \"promptChange\", \"messageSent\", \"conversationalToggle\", \"templateToggle\", \"filesSelected\", \"approvalRequested\", \"messages\", \"isLoading\", \"agentType\", \"showChatInteractionToggles\", \"showAiPrincipleToggle\", \"showApprovalButton\", \"showDropdown\", \"showAgentNameInput\", \"showFileUploadButton\", \"displayedAgentName\", \"agentNamePlaceholder\", \"acceptedFileType\"], [1, \"blueprint-content\"], [1, \"blueprint-header\"], [1, \"custom-blueprint-container\"], [\"viewBox\", \"0 0 100 100\", \"preserveAspectRatio\", \"none\", 1, \"connection-lines\"], [\"x1\", \"25\", \"y1\", \"25\", \"x2\", \"50\", \"y2\", \"50\", \"stroke\", \"#BBBEC5\", \"stroke-width\", \"0.3\"], [\"x1\", \"75\", \"y1\", \"25\", \"x2\", \"50\", \"y2\", \"50\", \"stroke\", \"#BBBEC5\", \"stroke-width\", \"0.3\"], [\"x1\", \"25\", \"y1\", \"75\", \"x2\", \"50\", \"y2\", \"50\", \"stroke\", \"#BBBEC5\", \"stroke-width\", \"0.3\"], [\"x1\", \"75\", \"y1\", \"75\", \"x2\", \"50\", \"y2\", \"50\", \"stroke\", \"#BBBEC5\", \"stroke-width\", \"0.3\"], [1, \"central-progress\"], [1, \"progress-ring\"], [\"width\", \"120\", \"height\", \"120\", 1, \"progress-circle\"], [\"id\", \"progressGradient\", \"x1\", \"0%\", \"y1\", \"0%\", \"x2\", \"100%\", \"y2\", \"100%\"], [\"offset\", \"33.91%\", 2, \"stop-color\", \"#0084ff\"], [\"offset\", \"100%\", 2, \"stop-color\", \"#03bdd4\"], [\"cx\", \"60\", \"cy\", \"60\", \"r\", \"50\", \"fill\", \"none\", \"stroke\", \"#e5e7eb\", \"stroke-width\", \"8\", 1, \"progress-background\"], [\"cx\", \"60\", \"cy\", \"60\", \"r\", \"50\", \"fill\", \"none\", \"stroke\", \"url(#progressGradient)\", \"stroke-width\", \"8\", \"stroke-linecap\", \"round\", \"transform\", \"rotate(180 60 60)\", 1, \"progress-bar\"], [1, \"progress-content\"], [1, \"progress-percentage\"], [1, \"progress-label\"], [\"id\", \"parent-box\"], [1, \"blueprint-zone\", \"north-zone\", \"prompts-zone\"], [1, \"zone-header\", 3, \"click\"], [1, \"header-content\"], [1, \"header-icon\"], [\"width\", \"45\", \"height\", \"44\", \"viewBox\", \"0 0 45 44\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"x\", \"0.679688\", \"width\", \"44\", \"height\", \"44\", \"rx\", \"8\", \"fill\", \"#5082EF\"], [\"d\", \"M20.6166 25.5C20.5273 25.1539 20.347 24.8381 20.0942 24.5854C19.8415 24.3327 19.5257 24.1523 19.1796 24.063L13.0446 22.481C12.94 22.4513 12.8478 22.3883 12.7822 22.3014C12.7166 22.2146 12.6812 22.1088 12.6812 22C12.6812 21.8912 12.7166 21.7854 12.7822 21.6986C12.8478 21.6118 12.94 21.5487 13.0446 21.519L19.1796 19.936C19.5256 19.8468 19.8413 19.6666 20.094 19.414C20.3467 19.1615 20.5272 18.8459 20.6166 18.5L22.1986 12.365C22.228 12.2599 22.291 12.1673 22.3779 12.1014C22.4649 12.0355 22.571 11.9998 22.6801 11.9998C22.7892 11.9998 22.8954 12.0355 22.9823 12.1014C23.0692 12.1673 23.1322 12.2599 23.1616 12.365L24.7426 18.5C24.8319 18.8461 25.0123 19.1619 25.265 19.4146C25.5177 19.6673 25.8336 19.8477 26.1796 19.937L32.3146 21.518C32.4201 21.5471 32.5132 21.61 32.5795 21.6971C32.6458 21.7841 32.6817 21.8906 32.6817 22C32.6817 22.1094 32.6458 22.2159 32.5795 22.3029C32.5132 22.39 32.4201 22.4529 32.3146 22.482L26.1796 24.063C25.8336 24.1523 25.5177 24.3327 25.265 24.5854C25.0123 24.8381 24.8319 25.1539 24.7426 25.5L23.1606 31.635C23.1312 31.7401 23.0682 31.8327 22.9813 31.8986C22.8944 31.9646 22.7882 32.0003 22.6791 32.0003C22.57 32.0003 22.4639 31.9646 22.3769 31.8986C22.29 31.8327 22.227 31.7401 22.1976 31.635L20.6166 25.5Z\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"zone-title\"], [1, \"header-actions\"], [1, \"required-badge\"], [\"type\", \"button\", 1, \"accordion-toggle\"], [\"width\", \"16\", \"height\", \"16\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\"], [\"d\", \"M6 9L12 15L18 9\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"class\", \"zone-content\", 4, \"ngIf\"], [1, \"blueprint-zone\", \"west-zone\", \"knowledge-zone\"], [\"x\", \"0.679688\", \"width\", \"44\", \"height\", \"44\", \"rx\", \"8\", \"fill\", \"#308666\"], [\"d\", \"M22.6797 17V31\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M26.6797 22H28.6797\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M26.6797 18H28.6797\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M13.6797 28C13.4145 28 13.1601 27.8946 12.9726 27.7071C12.785 27.5196 12.6797 27.2652 12.6797 27V14C12.6797 13.7348 12.785 13.4804 12.9726 13.2929C13.1601 13.1054 13.4145 13 13.6797 13H18.6797C19.7406 13 20.758 13.4214 21.5081 14.1716C22.2583 14.9217 22.6797 15.9391 22.6797 17C22.6797 15.9391 23.1011 14.9217 23.8513 14.1716C24.6014 13.4214 25.6188 13 26.6797 13H31.6797C31.9449 13 32.1993 13.1054 32.3868 13.2929C32.5743 13.4804 32.6797 13.7348 32.6797 14V27C32.6797 27.2652 32.5743 27.5196 32.3868 27.7071C32.1993 27.8946 31.9449 28 31.6797 28H25.6797C24.884 28 24.121 28.3161 23.5584 28.8787C22.9958 29.4413 22.6797 30.2044 22.6797 31C22.6797 30.2044 22.3636 29.4413 21.801 28.8787C21.2384 28.3161 20.4753 28 19.6797 28H13.6797Z\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M16.6797 22H18.6797\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M16.6797 18H18.6797\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"optional-badge\"], [1, \"blueprint-zone\", \"east-zone\", \"models-zone\"], [\"x\", \"0.679688\", \"width\", \"44\", \"height\", \"44\", \"rx\", \"8\", \"fill\", \"#997BCF\"], [\"d\", \"M31.6797 18C31.6793 17.6493 31.5867 17.3048 31.4112 17.0012C31.2357 16.6975 30.9834 16.4454 30.6797 16.27L23.6797 12.27C23.3756 12.0945 23.0308 12.0021 22.6797 12.0021C22.3286 12.0021 21.9837 12.0945 21.6797 12.27L14.6797 16.27C14.376 16.4454 14.1237 16.6975 13.9481 17.0012C13.7726 17.3048 13.68 17.6493 13.6797 18V26C13.68 26.3508 13.7726 26.6952 13.9481 26.9989C14.1237 27.3025 14.376 27.5547 14.6797 27.73L21.6797 31.73C21.9837 31.9056 22.3286 31.998 22.6797 31.998C23.0308 31.998 23.3756 31.9056 23.6797 31.73L30.6797 27.73C30.9834 27.5547 31.2357 27.3025 31.4112 26.9989C31.5867 26.6952 31.6793 26.3508 31.6797 26V18Z\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M13.9795 17L22.6795 22L31.3795 17\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M22.6797 32V22\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"blueprint-zone\", \"south-zone\", \"guardrails-zone\"], [\"x\", \"0.679688\", \"width\", \"44\", \"height\", \"44\", \"rx\", \"8\", \"fill\", \"#DC2626\"], [\"d\", \"M30.6797 23C30.6797 28 27.1797 30.5 23.0197 31.95C22.8019 32.0238 22.5652 32.0202 22.3497 31.94C18.1797 30.5 14.6797 28 14.6797 23V16C14.6797 15.7347 14.785 15.4804 14.9726 15.2929C15.1601 15.1053 15.4145 15 15.6797 15C17.6797 15 20.1797 13.8 21.9197 12.28C22.1315 12.099 22.401 11.9995 22.6797 11.9995C22.9583 11.9995 23.2278 12.099 23.4397 12.28C25.1897 13.81 27.6797 15 29.6797 15C29.9449 15 30.1993 15.1053 30.3868 15.2929C30.5743 15.4804 30.6797 15.7347 30.6797 16V23Z\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"zone-content\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [1, \"nodes-list\"], [\"class\", \"kanban-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"empty-state\"], [1, \"kanban-card\"], [1, \"card-title\"], [1, \"output-content\"], [1, \"mock-content\"], [1, \"output-placeholder\"], [1, \"output-section\"], [1, \"output-meta\"], [1, \"output-preview\"], [1, \"code-block\"], [\"type\", \"button\", 1, \"preview-btn\"]],\n      template: function AgentExecutionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function AgentExecutionComponent_Template_button_click_3_listener() {\n            return ctx.navigateBack();\n          });\n          i0.ɵɵelement(4, \"ava-icon\", 4);\n          i0.ɵɵelementStart(5, \"span\", 5);\n          i0.ɵɵtext(6);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"div\", 7)(9, \"div\", 8)(10, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function AgentExecutionComponent_Template_button_click_10_listener() {\n            return ctx.toggleLeftPanel();\n          });\n          i0.ɵɵelement(11, \"ava-icon\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(12, AgentExecutionComponent_button_12_Template, 2, 0, \"button\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(13, AgentExecutionComponent_div_13_Template, 2, 12, \"div\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"div\", 13)(15, \"div\", 8)(16, \"div\", 14)(17, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function AgentExecutionComponent_Template_button_click_17_listener() {\n            return ctx.setActiveRightTab(\"blueprint\");\n          });\n          i0.ɵɵtext(18, \" Blueprint \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function AgentExecutionComponent_Template_button_click_19_listener() {\n            return ctx.setActiveRightTab(\"output\");\n          });\n          i0.ɵɵtext(20, \" Agent Output \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"div\", 16);\n          i0.ɵɵtemplate(22, AgentExecutionComponent_div_22_Template, 96, 25, \"div\", 17)(23, AgentExecutionComponent_div_23_Template, 35, 0, \"div\", 18);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.agentName);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"collapsed\", ctx.isLeftPanelCollapsed);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"iconName\", ctx.isLeftPanelCollapsed ? \"ChevronRight\" : \"PanelLeft\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLeftPanelCollapsed);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLeftPanelCollapsed);\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"active\", ctx.activeRightTab === \"blueprint\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"active\", ctx.activeRightTab === \"output\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.activeRightTab === \"blueprint\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.activeRightTab === \"output\");\n        }\n      },\n      dependencies: [CommonModule, i8.NgForOf, i8.NgIf, FormsModule, AgentExecutionPlaygroundComponent, IconComponent],\n      styles: [\".agent-execution-container[_ngcontent-%COMP%] {\\n  height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n  color: var(--color-text-primary);\\n  overflow: hidden;\\n}\\n\\n.top-nav-bar[_ngcontent-%COMP%] {\\n  padding: 16px 24px;\\n  padding-bottom: 0px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  min-height: 64px;\\n}\\n\\n.nav-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.back-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 8px 12px;\\n  border-radius: 6px;\\n  transition: background-color 0.2s ease;\\n}\\n.back-button[_ngcontent-%COMP%]:hover {\\n  background: var(--color-background-tertiary);\\n}\\n.back-button[_ngcontent-%COMP%]   .agent-name[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #000000;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  gap: 16px;\\n  padding: 16px;\\n  padding-top: 0px;\\n  height: calc(100vh - 96px);\\n  overflow: hidden;\\n}\\n\\n.left-panel[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 400px;\\n  max-width: 600px;\\n  background: white;\\n  border-radius: 12px;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n  transition: all 0.3s ease;\\n}\\n.left-panel.collapsed[_ngcontent-%COMP%] {\\n  flex: 0 0 48px;\\n  min-width: 48px;\\n  max-width: 48px;\\n}\\n\\n.right-panel[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 400px;\\n  background: white;\\n  border-radius: 12px;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n\\n.panel-header[_ngcontent-%COMP%] {\\n  padding: 16px 20px;\\n  background: #e9effd;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  max-height: 45px;\\n  border-radius: 12px 12px 0 0;\\n}\\n\\n.collapse-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 6px;\\n  border-radius: 4px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: background-color 0.2s ease;\\n  color: #1a46a7;\\n}\\n.collapse-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--color-background-quaternary);\\n}\\n\\n.history-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 8px 12px;\\n  border-radius: 6px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #1a46a7;\\n  transition: all 0.2s ease;\\n}\\n.history-btn[_ngcontent-%COMP%]:disabled {\\n  cursor: not-allowed;\\n}\\n.history-btn[_ngcontent-%COMP%]:hover {\\n  color: var(--color-text-primary);\\n  background: var(--color-background-quaternary);\\n}\\n\\n.tabs-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  padding: 4px;\\n}\\n\\n.tab-btn[_ngcontent-%COMP%] {\\n  padding: 8px 20px;\\n  border: none;\\n  background: transparent;\\n  color: var(--text-secondary);\\n  border-radius: 10px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  white-space: nowrap;\\n  color: #1a46a7;\\n}\\n.tab-btn.active[_ngcontent-%COMP%] {\\n  background: white;\\n  color: var(--nav-pill-selected-color);\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n  font-weight: 600;\\n}\\n\\n.panel-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.mock-content[_ngcontent-%COMP%] {\\n  padding: 24px;\\n  height: 100%;\\n  overflow-y: auto;\\n}\\n.mock-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: var(--color-text-primary);\\n}\\n.mock-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 24px 0;\\n  color: var(--color-text-secondary);\\n  font-size: 14px;\\n  line-height: 1.5;\\n}\\n\\n.blueprint-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n}\\n\\n.blueprint-header[_ngcontent-%COMP%] {\\n  padding: 10px;\\n}\\n.blueprint-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: black;\\n  margin: 0 0 5px 0;\\n  font-size: 18px;\\n  font-weight: 600;\\n  text-align: center;\\n}\\n.blueprint-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--color-text-secondary);\\n  margin: 0;\\n  font-size: 14px;\\n}\\n\\n.blueprint-canvas-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow: auto;\\n  padding: 10px;\\n  background: var(--color-background-primary);\\n}\\n\\n.custom-blueprint-container[_ngcontent-%COMP%] {\\n  height: 100%;\\n  width: 100%;\\n  min-height: 500px;\\n  background-color: #f8f9fa;\\n  background-image: radial-gradient(circle, #d1d5db 1px, transparent 1px);\\n  background-size: 20px 20px;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  border: 1px solid #d1d3d8;\\n  position: relative;\\n  padding: 10%;\\n  border-radius: 10px;\\n}\\n\\n.central-progress[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  z-index: 10;\\n  background-color: white;\\n  border-radius: 50%;\\n  padding: 8px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-ring[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-circle[_ngcontent-%COMP%] {\\n  transform: rotate(-90deg);\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-background[_ngcontent-%COMP%] {\\n  opacity: 0.15;\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  transition: stroke-dashoffset 0.3s ease;\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-content[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  text-align: center;\\n  color: #374151;\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-percentage[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  line-height: 1;\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-label[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #6b7280;\\n  margin-top: 2px;\\n}\\n\\n#parent-box[_ngcontent-%COMP%] {\\n  height: 100%;\\n  width: 100%;\\n  gap: 2rem;\\n  position: relative;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.blueprint-zones-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 80%;\\n  height: 80%;\\n  max-width: 800px;\\n  max-height: 600px;\\n}\\n\\n#box1[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: space-between;\\n  height: 300px;\\n  position: relative;\\n  border: 1px solid #bbbec5;\\n  position: fixed;\\n  top: 32%;\\n  left: 40%;\\n  border-left: none;\\n}\\n\\n#box2[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: space-between;\\n  height: 300px;\\n  position: relative;\\n  border: 1px solid #bbbec5;\\n  position: fixed;\\n  top: 32%;\\n  left: 60%;\\n  border-right: none;\\n}\\n\\n.blueprint-zone[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  padding: 16px;\\n  transition: all 0.3s ease;\\n  position: absolute;\\n  width: 280px;\\n  z-index: 5;\\n}\\n.blueprint-zone.has-nodes[_ngcontent-%COMP%] {\\n  border-style: solid;\\n}\\n.blueprint-zone.collapsed[_ngcontent-%COMP%] {\\n  min-height: 40px;\\n}\\n.blueprint-zone.collapsed[_ngcontent-%COMP%]   .zone-header[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n\\n.north-zone[_ngcontent-%COMP%] {\\n  background-color: #ecf0fa;\\n  color: #005eb5;\\n  border: 2px solid #9ab7f6;\\n  top: 0;\\n  left: 0;\\n}\\n\\n.east-zone[_ngcontent-%COMP%] {\\n  background-color: #f2ebfd;\\n  border: 2px solid #d6c2f9;\\n  color: #d6c2f9;\\n  top: 0;\\n  right: 0;\\n}\\n\\n.south-zone[_ngcontent-%COMP%] {\\n  background-color: #fbf6f7;\\n  border: 2px solid #fecaca;\\n  color: #dc2626 !important;\\n  bottom: 0;\\n  right: 0;\\n}\\n.south-zone.has-nodes[_ngcontent-%COMP%] {\\n  height: auto;\\n  min-height: 100px;\\n  max-height: 200px;\\n  overflow-y: auto;\\n}\\n\\n.west-zone[_ngcontent-%COMP%] {\\n  background-color: #ecf8f4;\\n  border: 2px solid #a9e1cc;\\n  color: #25684f;\\n  bottom: 0;\\n  left: 0;\\n}\\n.west-zone.has-nodes[_ngcontent-%COMP%] {\\n  height: auto;\\n  min-height: 100px;\\n  max-height: 200px;\\n  overflow-y: auto;\\n}\\n\\n.connection-lines[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  pointer-events: none;\\n  z-index: 1;\\n}\\n.connection-lines[_ngcontent-%COMP%]   line[_ngcontent-%COMP%] {\\n  opacity: 0.7;\\n  transition: opacity 0.3s ease;\\n}\\n.connection-lines[_ngcontent-%COMP%]   line[_ngcontent-%COMP%]:hover {\\n  opacity: 1;\\n}\\n\\n.central-progress[_ngcontent-%COMP%] {\\n  z-index: 10;\\n}\\n\\n.blueprint-zone[_ngcontent-%COMP%] {\\n  z-index: 5;\\n}\\n\\n.zone-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin-bottom: 16px;\\n  padding-bottom: 12px;\\n  cursor: pointer;\\n  -webkit-user-select: none;\\n          user-select: none;\\n  transition: all 0.2s ease;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 8px;\\n  background-color: rgba(59, 130, 246, 0.1);\\n  color: #3b82f6;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .zone-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #374151;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .accordion-toggle[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 4px;\\n  border-radius: 4px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.2s ease;\\n  color: #6b7280;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .accordion-toggle[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 0, 0, 0.05);\\n  color: #374151;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .accordion-toggle[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  transition: transform 0.3s ease;\\n}\\n\\n.required-badge[_ngcontent-%COMP%] {\\n  background-color: var(--status-error);\\n  color: white;\\n  font-size: 10px;\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n  font-weight: 500;\\n}\\n\\n.optional-badge[_ngcontent-%COMP%] {\\n  background-color: var(--status-warning);\\n  color: white;\\n  font-size: 10px;\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n  font-weight: 500;\\n}\\n\\n.zone-content[_ngcontent-%COMP%] {\\n  min-height: 60px;\\n  flex: 1;\\n  transition: all 0.3s ease-in-out;\\n  opacity: 1;\\n  overflow: hidden;\\n}\\n\\n.empty-state[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: var(--text-secondary);\\n  font-size: 12px;\\n  padding: 20px 0;\\n  font-style: italic;\\n}\\n\\n.nodes-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n}\\n\\n.kanban-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  padding: 12px 16px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n  transition: all 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n}\\n.kanban-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\\n  transform: translateY(-1px);\\n}\\n.kanban-card[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #374151;\\n  flex: 1;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n.blueprint-placeholder[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n\\n.blueprint-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 16px;\\n  background: var(--color-background-primary);\\n  border: 1px solid var(--color-border-primary);\\n  border-radius: 8px;\\n  transition: all 0.2s ease;\\n}\\n.blueprint-item[_ngcontent-%COMP%]:hover {\\n  border-color: var(--color-border-secondary);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.blueprint-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  width: 32px;\\n  height: 32px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.blueprint-text[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: var(--color-text-primary);\\n}\\n\\n.output-placeholder[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 24px;\\n}\\n\\n.output-section[_ngcontent-%COMP%] {\\n  background: var(--color-background-primary);\\n  border: 1px solid var(--color-border-primary);\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n.output-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 16px 16px 8px 16px;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: var(--color-text-primary);\\n}\\n.output-section[_ngcontent-%COMP%]   .output-meta[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 0 16px 16px 16px;\\n  font-size: 12px;\\n  color: var(--color-text-tertiary);\\n}\\n\\n.output-preview[_ngcontent-%COMP%] {\\n  border-top: 1px solid var(--color-border-primary);\\n}\\n.output-preview[_ngcontent-%COMP%]   .code-block[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  background: var(--color-background-secondary);\\n  font-family: \\\"Monaco\\\", \\\"Menlo\\\", \\\"Ubuntu Mono\\\", monospace;\\n  font-size: 12px;\\n  line-height: 1.6;\\n  color: var(--color-text-primary);\\n  border-bottom: 1px solid var(--color-border-primary);\\n}\\n.output-preview[_ngcontent-%COMP%]   .preview-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 12px 16px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: var(--color-brand-primary);\\n  transition: all 0.2s ease;\\n  width: 100%;\\n  text-align: left;\\n}\\n.output-preview[_ngcontent-%COMP%]   .preview-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--color-background-tertiary);\\n}\\n\\n@media (max-width: 768px) {\\n  .execution-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .playground-column[_ngcontent-%COMP%], \\n   .output-column[_ngcontent-%COMP%] {\\n    flex: none;\\n    height: 50%;\\n  }\\n  .playground-column[_ngcontent-%COMP%] {\\n    border-right: none;\\n    border-bottom: 1px solid var(--color-border-primary);\\n  }\\n}\\n.row[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  margin: 0;\\n}\\n\\n.col-7[_ngcontent-%COMP%] {\\n  flex: 0 0 58.333333%;\\n  max-width: 58.333333%;\\n}\\n\\n.col-5[_ngcontent-%COMP%] {\\n  flex: 0 0 41.666667%;\\n  max-width: 41.666667%;\\n}\\n\\n*[_ngcontent-%COMP%] {\\n  box-sizing: border-box;\\n}\\n\\n[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n  height: 6px;\\n}\\n\\n[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: var(--color-background-secondary);\\n}\\n\\n[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: var(--color-border-primary);\\n  border-radius: 3px;\\n}\\n\\n[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: var(--color-border-secondary);\\n}\\n\\n.activity-placeholder[_ngcontent-%COMP%], \\n.output-placeholder[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n.activity-placeholder[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], \\n.output-placeholder[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: var(--color-text-primary);\\n}\\n.activity-placeholder[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], \\n.output-placeholder[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: var(--color-text-secondary);\\n  font-size: 14px;\\n}\\n\\n.activity-list[_ngcontent-%COMP%], \\n.output-list[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  border: 1px solid var(--color-border-primary);\\n  border-radius: 8px;\\n  padding: 16px;\\n  background: var(--color-background-secondary);\\n}\\n\\n.activity-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n  padding: 12px 0;\\n  border-bottom: 1px solid var(--color-border-primary);\\n}\\n.activity-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.activity-item[_ngcontent-%COMP%]   .timestamp[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: var(--color-text-tertiary);\\n  font-weight: 500;\\n}\\n.activity-item[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--color-text-primary);\\n}\\n\\n.output-item[_ngcontent-%COMP%] {\\n  padding: 12px 0;\\n  border-bottom: 1px solid var(--color-border-primary);\\n}\\n.output-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.output-item[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--color-text-primary);\\n  line-height: 1.5;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return AgentExecutionComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "Subject", "takeUntil", "switchMap", "finalize", "catchError", "of", "FormsModule", "AgentExecutionPlaygroundComponent", "IconComponent", "environment", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "AgentExecutionComponent_div_13_Template_app_agent_execution_playground_promptChange_1_listener", "$event", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onPromptChanged", "AgentExecutionComponent_div_13_Template_app_agent_execution_playground_messageSent_1_listener", "handleChatMessage", "AgentExecutionComponent_div_13_Template_app_agent_execution_playground_conversationalToggle_1_listener", "onPlaygroundConversationalToggle", "AgentExecutionComponent_div_13_Template_app_agent_execution_playground_templateToggle_1_listener", "onPlaygroundTemplateToggle", "AgentExecutionComponent_div_13_Template_app_agent_execution_playground_filesSelected_1_listener", "onFilesSelected", "AgentExecutionComponent_div_13_Template_app_agent_execution_playground_approvalRequested_1_listener", "onApprovalRequested", "ɵɵadvance", "ɵɵproperty", "chatMessages", "isProcessingChat", "agentType", "<PERSON><PERSON><PERSON>", "fileType", "ɵɵtextInterpolate", "node_r4", "name", "ɵɵtemplate", "AgentExecutionComponent_div_22_div_40_div_1_Template", "AgentExecutionComponent_div_22_div_40_div_3_Template", "blueprintPromptNodes", "length", "node_r5", "AgentExecutionComponent_div_22_div_61_div_1_Template", "AgentExecutionComponent_div_22_div_61_div_3_Template", "blueprintKnowledgeNodes", "node_r6", "AgentExecutionComponent_div_22_div_79_div_1_Template", "AgentExecutionComponent_div_22_div_79_div_3_Template", "blueprintModelNodes", "node_r7", "AgentExecutionComponent_div_22_div_95_div_1_Template", "AgentExecutionComponent_div_22_div_95_div_3_Template", "blueprintGuardrailNodes", "ɵɵelement", "AgentExecutionComponent_div_22_Template_div_click_26_listener", "_r3", "toggleBlueprintZone", "AgentExecutionComponent_div_22_div_40_Template", "AgentExecutionComponent_div_22_Template_div_click_42_listener", "AgentExecutionComponent_div_22_div_61_Template", "AgentExecutionComponent_div_22_Template_div_click_63_listener", "AgentExecutionComponent_div_22_div_79_Template", "AgentExecutionComponent_div_22_Template_div_click_81_listener", "AgentExecutionComponent_div_22_div_95_Template", "ɵɵstyleProp", "blueprintCompletionPercentage", "ɵɵtextInterpolate1", "ɵɵclassProp", "isBlueprintZoneExpanded", "AgentExecutionComponent", "route", "router", "agentService", "agentPlaygroundService", "tokenStorage", "loaderService", "formBuilder", "toolExecutionService", "navigationTabs", "id", "label", "disabled", "agentId", "agentDetail", "playgroundComp", "activityLogs", "activityProgress", "executionDetails", "isRunning", "status", "inputText", "agentOutputs", "agentForm", "executionStartTime", "executionCompleted", "executionId", "enableStreamingLog", "enableLogStreaming", "isExecutionComplete", "progressInterval", "destroy$", "selectedTab", "demoTabs", "errorMsg", "resMessage", "taskMessage", "isJsonValid", "disable<PERSON>hat", "selectedFiles", "agentNodes", "userInputList", "progress", "isLoading", "loaderColor", "inputFieldOrder", "currentInputIndex", "activeTabId", "isLeftPanelCollapsed", "activeRightTab", "currentAgentDetails", "buildAgentNodes", "canvasNodes", "canvasEdges", "selected<PERSON><PERSON><PERSON>", "selectedAgentMode", "selectedUseCaseIdentifier", "agentFilesUploadedData", "agentAttachment", "isAgentPlaygroundLoading", "agentPlaygroundDestroy", "agentChatPayload", "agentCode", "promptOptions", "blueprintToolNodes", "blueprintZonesExpanded", "prompt", "model", "knowledge", "guardrail", "tool", "constructor", "group", "isConversational", "isUseTemplate", "ngOnInit", "crypto", "randomUUID", "params", "subscribe", "queryParams", "loadAgentData", "from", "text", "ngOnDestroy", "next", "complete", "clearInterval", "onTabChange", "event", "getCollaborativeAgentDetailsById", "response", "handleAgentDataResponse", "error", "console", "getAgentById", "agentData", "agentDetails", "Array", "isArray", "data", "description", "loadAgentNodes", "mapAgentConfigurationToBlueprint", "message", "showAgentError", "displayMessage", "fileNames", "map", "file", "documentName", "join", "get", "value", "agentMode", "useCaseName", "useCaseIdentifier", "organizationPath", "useCaseCode", "processAgentFilesAndSendMessage", "sendAgentMessageToAPI", "payload", "Number", "user", "getDaUsername", "userInputs", "question", "fileWrapper", "submitAgentExecuteWithFile", "pipe", "res", "handleAgentExecuteResponse", "err", "submitAgentExecute", "String", "files", "saveLogs", "exportResults", "section", "handleControlAction", "action", "navigateBack", "navigate", "mode", "editAgent", "toggleLeftPanel", "setActiveRightTab", "tab", "zoneType", "buildOrganizationPath", "getMetadataFromNavbar", "outputRaw", "agentResponse", "agent", "output", "formattedOutput", "replace", "detail", "formData", "FormData", "for<PERSON>ach", "fileData", "append", "has", "getFileToContent", "fileResponse", "fileContent", "fileResponses", "sendAgentMessageToAPIWithFiles", "push", "content", "role", "levelId", "generatePrompt", "generatedResponse", "choices", "aiResponseText", "warn", "errorMessage", "pop", "fileContents", "nodeCounter", "mapIndividualAgentToBlueprint", "mapCollaborativeAgentToBlueprint", "promptTemplate", "type", "config", "category", "configItem", "config<PERSON><PERSON>", "config<PERSON><PERSON><PERSON>", "kbValue", "toString", "kbIds", "split", "trim", "filter", "kbId", "startsWith", "guardrailName", "totalRequired", "totalOptional", "currentRequired", "Math", "round", "workflow", "workflowItem", "goal", "modelName", "tools", "toolName", "knowledgeBase", "kb", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "AgentServiceService", "i3", "AgentPlaygroundService", "i4", "TokenStorageService", "i5", "LoaderService", "i6", "FormBuilder", "i7", "ToolExecutionService", "selectors", "viewQuery", "AgentExecutionComponent_Query", "rf", "ctx", "AgentExecutionComponent_Template_button_click_3_listener", "AgentExecutionComponent_Template_button_click_10_listener", "AgentExecutionComponent_button_12_Template", "AgentExecutionComponent_div_13_Template", "AgentExecutionComponent_Template_button_click_17_listener", "AgentExecutionComponent_Template_button_click_19_listener", "AgentExecutionComponent_div_22_Template", "AgentExecutionComponent_div_23_Template", "i8", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\agents\\agent-execution\\agent-execution.component.ts", "C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\agents\\agent-execution\\agent-execution.component.html"], "sourcesContent": ["import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Subject, takeUntil, switchMap, finalize, catchError, of } from 'rxjs';\r\nimport { FormBuilder, FormGroup, FormsModule, Validators } from '@angular/forms';\r\nimport { AgentExecutionPlaygroundComponent } from './components/agent-execution-playground/agent-execution-playground.component';\r\nimport { ChatMessage } from '../../../shared/components/chat-window/chat-window.component';\r\nimport { ButtonComponent, IconComponent, TabItem, TabsComponent, DropdownOption } from '@ava/play-comp-library';\r\nimport { AgentServiceService } from '../services/agent-service.service';\r\nimport { AgentPlaygroundService } from '../build-agents/services/agent-playground.service';\r\nimport { environment } from 'projects/console/src/environments/environment';\r\nimport { TokenStorageService } from '@shared/auth/services/token-storage.service';\r\nimport { LoaderService } from '../../../shared/services/loader/loader.service';\r\nimport { ToolExecutionService } from '../../../shared/services/tool-execution/tool-execution.service';\r\n\r\n@Component({\r\n  selector: 'app-agent-execution',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    AgentExecutionPlaygroundComponent,\r\n    TabsComponent,\r\n    ButtonComponent,\r\n    IconComponent,\r\n  ],\r\n  templateUrl: './agent-execution.component.html',\r\n  styleUrls: ['./agent-execution.component.scss'],\r\n})\r\nexport class AgentExecutionComponent implements OnInit, OnDestroy {\r\n  navigationTabs: TabItem[] = [\r\n    { id: 'nav-home', label: 'Agent Activity' },\r\n    { id: 'nav-products', label: 'Agent Output' },\r\n    { id: 'nav-services', label: 'Preview', disabled: true },\r\n  ];\r\n\r\n  agentId: string | null = null;\r\n  agentType: string = 'individual';\r\n  agentName: string = 'Agent';\r\n  agentDetail: string = '';\r\n\r\n  @ViewChild(AgentExecutionPlaygroundComponent, { static: false })\r\n  playgroundComp!: AgentExecutionPlaygroundComponent;\r\n\r\n  activityLogs: any[] = [];\r\n  activityProgress: number = 0;\r\n  executionDetails: any;\r\n  isRunning: boolean = false;\r\n  status: string = 'notStarted';\r\n  chatMessages: ChatMessage[] = [];\r\n  isProcessingChat: boolean = false;\r\n  inputText = '';\r\n  agentOutputs: any[] = [];\r\n  public agentForm!: FormGroup;\r\n  public fileType: string = '.pdf,.doc,.docx,.txt,.csv,.xlsx,.xls,.ppt,.pptx,.png,.jpg,.jpeg,.gif,.bmp,.svg';\r\n  executionStartTime: Date | null = null;\r\n  executionCompleted: boolean = false;\r\n  executionId!: string;\r\n  enableStreamingLog = environment.enableLogStreaming || 'all';\r\n  public isExecutionComplete: boolean = false;\r\n  progressInterval: any;\r\n  private destroy$ = new Subject<void>();\r\n  selectedTab: string = 'Agent Activity';\r\n  demoTabs: any[] = [\r\n    { id: 'tab1', label: 'History' },\r\n    { id: 'tab2', label: 'Blueprint' },\r\n    { id: 'tab3', label: 'Agent Output' },\r\n  ];\r\n  errorMsg = false;\r\n  resMessage: any;\r\n  taskMessage: any[] = [];\r\n  isJsonValid = false;\r\n  disableChat: boolean = false;\r\n  selectedFiles: File[] = [];\r\n  agentNodes: any[] = [];\r\n  userInputList: any[] = [];\r\n  progress = 0;\r\n  isLoading = false;\r\n  loaderColor: string = '';\r\n  inputFieldOrder: string[] = [];\r\n  currentInputIndex: number = 0;\r\n  activeTabId: string = 'nav-home';\r\n  isLeftPanelCollapsed: boolean = false;\r\n  activeRightTab: string = 'blueprint';\r\n  currentAgentDetails: any = null;\r\n  buildAgentNodes: any[] = [];\r\n  canvasNodes: any[] = [];\r\n  canvasEdges: any[] = [];\r\n  selectedPrompt: string = '';\r\n  selectedAgentMode: string = '';\r\n  selectedUseCaseIdentifier: string = '';\r\n  agentFilesUploadedData: any[] = [];\r\n  agentAttachment: string[] = [];\r\n  isAgentPlaygroundLoading = false;\r\n  agentPlaygroundDestroy = new Subject<boolean>();\r\n  agentChatPayload: any[] = [];\r\n  agentCode: string = '';\r\n  promptOptions: DropdownOption[] = [];\r\n\r\n  blueprintCompletionPercentage: number = 0;\r\n  blueprintPromptNodes: any[] = [];\r\n  blueprintModelNodes: any[] = [];\r\n  blueprintKnowledgeNodes: any[] = [];\r\n  blueprintGuardrailNodes: any[] = [];\r\n  blueprintToolNodes: any[] = [];\r\n\r\n  private blueprintZonesExpanded: { [key: string]: boolean } = {\r\n    prompt: true,\r\n    model: true,\r\n    knowledge: true,\r\n    guardrail: true,\r\n    tool: true,\r\n  };\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private agentService: AgentServiceService,\r\n    private agentPlaygroundService: AgentPlaygroundService,\r\n    private tokenStorage: TokenStorageService,\r\n    private loaderService: LoaderService,\r\n    private formBuilder: FormBuilder,\r\n    private toolExecutionService: ToolExecutionService,\r\n  ) {\r\n    this.agentForm = this.formBuilder.group({\r\n      isConversational: [true],\r\n      isUseTemplate: [false],\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.executionId = crypto.randomUUID();\r\n    this.route.params.subscribe((params) => {\r\n      this.agentType = params['type'] || 'individual';\r\n    });\r\n    this.route.queryParams.subscribe((params) => {\r\n      if (params['id']) {\r\n        this.agentId = params['id'];\r\n        this.loadAgentData(params['id']);\r\n      }\r\n    });\r\n\r\n    this.chatMessages = [\r\n      {\r\n        from: 'ai',\r\n        text: `Hi there! I am ${this.agentName || 'your agent'}. How can I help you today?`,\r\n      },\r\n    ];\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n    if (this.progressInterval) {\r\n      clearInterval(this.progressInterval);\r\n    }\r\n  }\r\n\r\n  onTabChange(event: { id: string; label: string }) {\r\n    this.activeTabId = event.id;\r\n    this.selectedTab = event.label;\r\n  }\r\n\r\n  loadAgentData(agentId: string): void {\r\n    this.isLoading = true;\r\n    if (this.agentType === 'collaborative') {\r\n      this.agentService.getCollaborativeAgentDetailsById(agentId).subscribe({\r\n        next: (response: any) => {\r\n          this.handleAgentDataResponse(response);\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error loading collaborative agent:', error);\r\n          this.isLoading = false;\r\n        },\r\n      });\r\n    } else {\r\n      this.agentService.getAgentById(agentId).subscribe({\r\n        next: (response: any) => {\r\n          this.handleAgentDataResponse(response);\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error loading individual agent:', error);\r\n          this.isLoading = false;\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  private handleAgentDataResponse(response: any): void {\r\n    this.isLoading = false;\r\n    let agentData;\r\n    if (response.agentDetails && Array.isArray(response.agentDetails) && response.agentDetails.length > 0) {\r\n      agentData = response.agentDetails[0];\r\n    } else if (response.agentDetail) {\r\n      agentData = response.agentDetail;\r\n    } else if (response.data) {\r\n      agentData = response.data;\r\n    } else {\r\n      agentData = response;\r\n    }\r\n\r\n    if (agentData) {\r\n      this.currentAgentDetails = agentData;\r\n      this.agentName = agentData.name || agentData.agentName || 'Agent';\r\n      this.agentDetail = agentData.description || agentData.agentDetail || '';\r\n      this.selectedPrompt = this.agentName;\r\n\r\n      if (this.chatMessages.length > 0) {\r\n        this.chatMessages[0].text = `Hi there! I am ${this.agentName}. How can I help you today?`;\r\n      }\r\n\r\n      this.loadAgentNodes(agentData);\r\n    }\r\n  }\r\n\r\n  private loadAgentNodes(agentData: any): void {\r\n    this.mapAgentConfigurationToBlueprint(agentData);\r\n  }\r\n\r\n  handleChatMessage(message: string): void {\r\n    if (this.agentType === 'individual') {\r\n      if (!this.selectedPrompt || this.selectedPrompt === 'default') {\r\n        this.showAgentError('Please select an agent from the dropdown before testing.');\r\n        return;\r\n      }\r\n  \r\n      let displayMessage = message;\r\n      if (this.agentFilesUploadedData.length > 0) {\r\n        const fileNames = this.agentFilesUploadedData.map((file) => file.documentName).join(', ');\r\n        displayMessage = `${message}\\n\\n📎 Attached files: ${fileNames}`;\r\n      }\r\n  \r\n      this.chatMessages = [...this.chatMessages, { from: 'user', text: displayMessage }];\r\n      this.isProcessingChat = true;\r\n      const isConversational = this.agentForm.get('isConversational')?.value || false;\r\n      const isUseTemplate = this.agentForm.get('isUseTemplate')?.value || false;\r\n  \r\n      // Set mode and useCaseIdentifier based on agent details\r\n      const agentMode = this.currentAgentDetails.useCaseName || this.agentName;\r\n      const useCaseIdentifier = this.currentAgentDetails.organizationPath || this.currentAgentDetails.useCaseCode;\r\n  \r\n      if (this.agentFilesUploadedData.length > 0) {\r\n        this.processAgentFilesAndSendMessage(message, agentMode, useCaseIdentifier, isConversational, isUseTemplate);\r\n        return;\r\n      }\r\n  \r\n      this.sendAgentMessageToAPI(message, agentMode, useCaseIdentifier, isConversational, isUseTemplate);\r\n    } else if (this.agentType === 'collaborative') {\r\n      this.isProcessingChat = true;\r\n      let payload = {\r\n        executionId: this.executionId,\r\n        agentId: Number(this.agentId),\r\n        user: this.tokenStorage.getDaUsername() || '<EMAIL>',\r\n        userInputs: { question: message },\r\n      };\r\n  \r\n      if (this.agentFilesUploadedData.length > 0) {\r\n        const fileWrapper = this.agentFilesUploadedData[0];\r\n        let displayMessage: string;\r\n        if (this.agentFilesUploadedData.length > 0) {\r\n          const fileNames = this.agentFilesUploadedData.map((file) => file.documentName).join(', ');\r\n          displayMessage = `📎 Attached files: ${fileNames}`;\r\n          this.chatMessages = [{ from: 'user', text: displayMessage }];\r\n        }\r\n  \r\n        this.agentPlaygroundService.submitAgentExecuteWithFile(payload, fileWrapper).pipe(\r\n          finalize(() => {\r\n            this.isProcessingChat = false;\r\n            this.isAgentPlaygroundLoading = false;\r\n          }),\r\n          takeUntil(this.agentPlaygroundDestroy),\r\n        ).subscribe({\r\n          next: (res) => this.handleAgentExecuteResponse(res, message),\r\n          error: (err: any) => {\r\n            this.chatMessages = [\r\n              ...this.chatMessages,\r\n              { from: 'user', text: message },\r\n              { from: 'ai', text: err?.error?.message || err?.message || 'Something went wrong.' },\r\n            ];\r\n          },\r\n        });\r\n      } else {\r\n        this.agentPlaygroundService.submitAgentExecute(payload).pipe(\r\n          finalize(() => {\r\n            this.isProcessingChat = false;\r\n            this.isAgentPlaygroundLoading = false;\r\n          }),\r\n          takeUntil(this.agentPlaygroundDestroy),\r\n        ).subscribe({\r\n          next: (res) => this.handleAgentExecuteResponse(res, message),\r\n          error: (err: any) => {\r\n            this.chatMessages = [\r\n              ...this.chatMessages,\r\n              { from: 'user', text: message },\r\n              { from: 'ai', text: err?.error?.message || err?.message || 'Something went wrong.' },\r\n            ];\r\n          },\r\n        });\r\n      }\r\n    }\r\n  }\r\n  \r\n\r\n  onPromptChanged(prompt: DropdownOption): void {\r\n    this.inputText = prompt.name || String(prompt.value) || '';\r\n  }\r\n\r\n  onPlaygroundConversationalToggle(value: boolean): void {}\r\n\r\n  onPlaygroundTemplateToggle(value: boolean): void {}\r\n\r\n  onFilesSelected(files: File[]): void {\r\n    this.selectedFiles = files;\r\n  }\r\n\r\n  onApprovalRequested(): void {}\r\n\r\n  saveLogs(): void {}\r\n\r\n  exportResults(section: 'activity' | 'output'): void {}\r\n\r\n  handleControlAction(action: 'play' | 'pause' | 'stop'): void {}\r\n\r\n  navigateBack(): void {\r\n    this.router.navigate(['/build/agents', this.agentType], {\r\n      queryParams: { id: this.agentId, mode: 'view' },\r\n    });\r\n  }\r\n\r\n  editAgent(): void {\r\n    this.router.navigate(['/build/agents', this.agentType], {\r\n      queryParams: { id: this.agentId, mode: 'edit' },\r\n    });\r\n  }\r\n\r\n  toggleLeftPanel(): void {\r\n    this.isLeftPanelCollapsed = !this.isLeftPanelCollapsed;\r\n  }\r\n\r\n  setActiveRightTab(tab: string): void {\r\n    this.activeRightTab = tab;\r\n  }\r\n\r\n  toggleBlueprintZone(zoneType: string): void {\r\n    this.blueprintZonesExpanded[zoneType] = !this.blueprintZonesExpanded[zoneType];\r\n  }\r\n\r\n  isBlueprintZoneExpanded(zoneType: string): boolean {\r\n    return this.blueprintZonesExpanded[zoneType] || false;\r\n  }\r\n\r\n  private showAgentError(message: string): void {\r\n    this.chatMessages = [...this.chatMessages, { from: 'ai', text: message }];\r\n  }\r\n\r\n  private buildOrganizationPath(): string {\r\n    return '';\r\n  }\r\n\r\n  private getMetadataFromNavbar(): { levelId?: number } {\r\n    return {};\r\n  }\r\n\r\n  handleAgentExecuteResponse(response: any, message: string): void {\r\n    try {\r\n      const outputRaw = response?.agentResponse?.agent?.output;\r\n      let formattedOutput = '';\r\n      if (outputRaw) {\r\n        formattedOutput = outputRaw.replace(/\\\\n/g, '\\n');\r\n      } else {\r\n        formattedOutput = response?.agentResponse?.detail;\r\n      }\r\n      this.chatMessages = [\r\n        ...this.chatMessages,\r\n        { from: 'user', text: message },\r\n        { from: 'ai', text: formattedOutput || 'No response from agent.' },\r\n      ];\r\n    } catch (err: any) {\r\n      this.chatMessages = [\r\n        ...this.chatMessages,\r\n        { from: 'ai', text: err?.message || 'Agent response could not be processed.' },\r\n      ];\r\n    }\r\n  }\r\n\r\n  private processAgentFilesAndSendMessage(\r\n    message: string,\r\n    mode: string,\r\n    useCaseIdentifier: string,\r\n    isConversational: boolean,\r\n    isUseTemplate: boolean,\r\n  ): void {\r\n    const formData = new FormData();\r\n    this.agentFilesUploadedData.forEach((fileData) => {\r\n      if (fileData.file) {\r\n        formData.append('files', fileData.file);\r\n      }\r\n    });\r\n\r\n    if (formData.has('files')) {\r\n      this.agentPlaygroundService.getFileToContent(formData).pipe(\r\n        switchMap((fileResponse) => {\r\n          const fileContent = fileResponse?.fileResponses?.map((response: any) => response.fileContent)?.join('\\n') || '';\r\n          this.sendAgentMessageToAPIWithFiles(message, mode, useCaseIdentifier, isConversational, isUseTemplate, fileContent);\r\n          return of(null);\r\n        }),\r\n        catchError((error) => {\r\n          console.error('Error parsing files:', error);\r\n          this.sendAgentMessageToAPI(message, mode, useCaseIdentifier, isConversational, isUseTemplate);\r\n          return of(null);\r\n        }),\r\n      ).subscribe();\r\n    } else {\r\n      this.sendAgentMessageToAPI(message, mode, useCaseIdentifier, isConversational, isUseTemplate);\r\n    }\r\n  }\r\n\r\n  private sendAgentMessageToAPI(\r\n    message: string,\r\n    mode: string,\r\n    useCaseIdentifier: string,\r\n    isConversational: boolean,\r\n    isUseTemplate: boolean,\r\n  ): void {\r\n    if (isConversational) {\r\n      this.agentChatPayload.push({ content: message, role: 'user' });\r\n    }\r\n\r\n    const payload = isConversational ? this.agentChatPayload : message;\r\n    const { levelId } = this.getMetadataFromNavbar();\r\n\r\n    this.agentPlaygroundService.generatePrompt(\r\n      payload,\r\n      mode,\r\n      isConversational,\r\n      isUseTemplate,\r\n      this.agentAttachment,\r\n      useCaseIdentifier,\r\n      '',\r\n      levelId,\r\n    ).pipe(\r\n      finalize(() => {\r\n        this.isProcessingChat = false;\r\n        this.isAgentPlaygroundLoading = false;\r\n      }),\r\n      takeUntil(this.agentPlaygroundDestroy),\r\n    ).subscribe({\r\n      next: (generatedResponse: any) => {\r\n        if (generatedResponse?.response && generatedResponse?.response?.choices) {\r\n          const aiResponseText = generatedResponse.response.choices[0].text;\r\n          this.chatMessages = [...this.chatMessages, { from: 'ai', text: aiResponseText }];\r\n          if (isConversational) {\r\n            this.agentChatPayload.push({ content: aiResponseText, role: 'assistant' });\r\n          }\r\n        } else {\r\n          console.warn('Unexpected API response format:', generatedResponse);\r\n          this.showAgentError('Received unexpected response format from API.');\r\n        }\r\n      },\r\n      error: (error: any) => {\r\n        console.error('API Error:', error);\r\n        const errorMessage = error?.error?.message || 'An error occurred while processing your request.';\r\n        this.showAgentError(errorMessage);\r\n        if (isConversational && this.agentChatPayload.length > 0) {\r\n          this.agentChatPayload.pop();\r\n        }\r\n      },\r\n    });\r\n  }\r\n\r\n  private sendAgentMessageToAPIWithFiles(\r\n    message: string,\r\n    mode: string,\r\n    useCaseIdentifier: string,\r\n    isConversational: boolean,\r\n    isUseTemplate: boolean,\r\n    fileContents: string,\r\n  ): void {\r\n    if (isConversational) {\r\n      this.agentChatPayload.push({ content: message, role: 'user' });\r\n    }\r\n\r\n    const payload = isConversational ? this.agentChatPayload : message;\r\n    const { levelId } = this.getMetadataFromNavbar();\r\n\r\n    this.agentPlaygroundService.generatePrompt(\r\n      payload,\r\n      mode,\r\n      isConversational,\r\n      isUseTemplate,\r\n      this.agentAttachment,\r\n      useCaseIdentifier,\r\n      fileContents,\r\n      levelId,\r\n    ).pipe(\r\n      finalize(() => {\r\n        this.isProcessingChat = false;\r\n        this.isAgentPlaygroundLoading = false;\r\n      }),\r\n      takeUntil(this.agentPlaygroundDestroy),\r\n    ).subscribe({\r\n      next: (generatedResponse: any) => {\r\n        if (generatedResponse?.response && generatedResponse?.response?.choices) {\r\n          const aiResponseText = generatedResponse.response.choices[0].text;\r\n          this.chatMessages = [...this.chatMessages, { from: 'ai', text: aiResponseText }];\r\n          if (isConversational) {\r\n            this.agentChatPayload.push({ content: aiResponseText, role: 'assistant' });\r\n          }\r\n        } else {\r\n          console.warn('Unexpected API response format:', generatedResponse);\r\n          this.showAgentError('Received unexpected response format from API.');\r\n        }\r\n      },\r\n      error: (error: any) => {\r\n        console.error('API Error:', error);\r\n        const errorMessage = error?.error?.message || 'An error occurred while processing your request.';\r\n        this.showAgentError(errorMessage);\r\n        if (isConversational && this.agentChatPayload.length > 0) {\r\n          this.agentChatPayload.pop();\r\n        }\r\n      },\r\n    });\r\n  }\r\n\r\n  private mapAgentConfigurationToBlueprint(agentData: any): void {\r\n    if (!agentData) {\r\n      console.warn('No agent data provided for blueprint');\r\n      return;\r\n    }\r\n\r\n    this.buildAgentNodes = [];\r\n    this.canvasNodes = [];\r\n    let nodeCounter = 1;\r\n\r\n    if (this.agentType === 'individual') {\r\n      this.mapIndividualAgentToBlueprint(agentData, nodeCounter);\r\n    } else if (this.agentType === 'collaborative') {\r\n      this.mapCollaborativeAgentToBlueprint(agentData, nodeCounter);\r\n    }\r\n  }\r\n\r\n  private mapIndividualAgentToBlueprint(agentData: any, nodeCounter: number): void {\r\n    this.blueprintPromptNodes = [];\r\n    this.blueprintModelNodes = [];\r\n    this.blueprintKnowledgeNodes = [];\r\n    this.blueprintGuardrailNodes = [];\r\n\r\n    if (agentData.useCaseName || agentData.promptTemplate) {\r\n      this.blueprintPromptNodes.push({\r\n        id: `prompt-${nodeCounter++}`,\r\n        name: agentData.useCaseName || agentData.promptTemplate || 'System Prompt',\r\n        type: 'prompt',\r\n      });\r\n    }\r\n\r\n    if (agentData.config && Array.isArray(agentData.config)) {\r\n      agentData.config.forEach((category: any) => {\r\n        if (category.config && Array.isArray(category.config)) {\r\n          category.config.forEach((configItem: any) => {\r\n            if (configItem.configKey === 'MODEL' && configItem.configValue) {\r\n              this.blueprintModelNodes.push({\r\n                id: `model-${nodeCounter++}`,\r\n                name: `Model: ${configItem.configValue}`,\r\n                type: 'model',\r\n              });\r\n            }\r\n\r\n            if (configItem.configKey === 'RAG_KNOWLEDGEBASE_NAME' && configItem.configValue) {\r\n              const kbValue = configItem.configValue.toString();\r\n              const kbIds = kbValue.split(',').map((id: string) => id.trim()).filter((id: string) => id);\r\n              kbIds.forEach((kbId: string) => {\r\n                this.blueprintKnowledgeNodes.push({\r\n                  id: `knowledge-${nodeCounter++}`,\r\n                  name: `Knowledge Base: ${kbId}`,\r\n                  type: 'knowledge',\r\n                });\r\n              });\r\n            }\r\n\r\n            if ((configItem.configKey === 'GUARDRAIL' || configItem.configKey.startsWith('GUARDRAIL_')) && configItem.configValue && configItem.configValue !== 'false') {\r\n              let guardrailName = configItem.configKey;\r\n              if (guardrailName.startsWith('GUARDRAIL_')) {\r\n                guardrailName = guardrailName.replace('GUARDRAIL_', '');\r\n              }\r\n              this.blueprintGuardrailNodes.push({\r\n                id: `guardrail-${nodeCounter++}`,\r\n                name: `Guardrail: ${guardrailName}`,\r\n                type: 'guardrail',\r\n              });\r\n            }\r\n          });\r\n        }\r\n      });\r\n    }\r\n\r\n    const totalRequired = 2;\r\n    const totalOptional = this.blueprintKnowledgeNodes.length + this.blueprintGuardrailNodes.length;\r\n    const currentRequired = this.blueprintPromptNodes.length + this.blueprintModelNodes.length;\r\n    this.blueprintCompletionPercentage = Math.round((currentRequired / totalRequired) * 100);\r\n  }\r\n\r\n  private mapCollaborativeAgentToBlueprint(agentData: any, nodeCounter: number): void {\r\n    this.blueprintPromptNodes = [];\r\n    this.blueprintModelNodes = [];\r\n    this.blueprintKnowledgeNodes = [];\r\n    this.blueprintToolNodes = [];\r\n\r\n    if (agentData.workflow && agentData.workflow.length > 0) {\r\n      const workflowItem = agentData.workflow[0];\r\n      if (workflowItem.goal) {\r\n        this.blueprintPromptNodes.push({\r\n          id: `prompt-${nodeCounter++}`,\r\n          name: workflowItem.goal || 'Collaborative Goal',\r\n          type: 'prompt',\r\n        });\r\n      }\r\n    }\r\n\r\n    if (agentData.model || agentData.modelName) {\r\n      this.blueprintModelNodes.push({\r\n        id: `model-${nodeCounter++}`,\r\n        name: agentData.model || agentData.modelName || 'Default Model',\r\n        type: 'model',\r\n      });\r\n    }\r\n\r\n    if (agentData.workflow && agentData.workflow.length > 0) {\r\n      agentData.workflow.forEach((workflowItem: any) => {\r\n        if (workflowItem.tools && workflowItem.tools.length > 0) {\r\n          workflowItem.tools.forEach((tool: any) => {\r\n            this.blueprintToolNodes.push({\r\n              id: `tool-${nodeCounter++}`,\r\n              name: tool.name || tool.toolName || 'Tool',\r\n              type: 'tool',\r\n            });\r\n          });\r\n        }\r\n      });\r\n    }\r\n\r\n    if (agentData.knowledgeBase && agentData.knowledgeBase.length > 0) {\r\n      agentData.knowledgeBase.forEach((kb: any) => {\r\n        this.blueprintKnowledgeNodes.push({\r\n          id: `knowledge-${nodeCounter++}`,\r\n          name: kb.name || kb.documentName || 'Knowledge Base',\r\n          type: 'knowledge',\r\n        });\r\n      });\r\n    }\r\n\r\n    const totalRequired = 2;\r\n    const currentRequired = this.blueprintPromptNodes.length + this.blueprintModelNodes.length;\r\n    this.blueprintCompletionPercentage = Math.round((currentRequired / totalRequired) * 100);\r\n  }\r\n}\r\n", "<div class=\"agent-execution-container\">\r\n  <!-- Top Navigation Bar -->\r\n  <div class=\"top-nav-bar\">\r\n    <div class=\"nav-left\">\r\n      <button class=\"back-button\" (click)=\"navigateBack()\" type=\"button\">\r\n        <ava-icon\r\n          iconName=\"ArrowLeft\"\r\n          iconSize=\"16\"\r\n          iconColor=\"#374151\"\r\n        ></ava-icon>\r\n        <span class=\"agent-name\">{{ agentName }}</span>\r\n      </button>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Main Content with Two Panels -->\r\n  <div class=\"main-content\">\r\n    <!-- Left Panel: Playground -->\r\n    <div class=\"left-panel\" [class.collapsed]=\"isLeftPanelCollapsed\">\r\n      <div class=\"panel-header\">\r\n        <button class=\"collapse-btn\" (click)=\"toggleLeftPanel()\" type=\"button\">\r\n          <ava-icon\r\n            [iconName]=\"isLeftPanelCollapsed ? 'ChevronRight' : 'PanelLeft'\"\r\n            iconSize=\"16\"\r\n            iconColor=\"#6B7280\"\r\n          >\r\n          </ava-icon>\r\n        </button>\r\n        <button\r\n          class=\"history-btn\"\r\n          *ngIf=\"!isLeftPanelCollapsed\"\r\n          type=\"button\"\r\n          disabled\r\n        >\r\n          History\r\n        </button>\r\n      </div>\r\n\r\n      <div class=\"panel-content\" *ngIf=\"!isLeftPanelCollapsed\">\r\n        <app-agent-execution-playground\r\n          [messages]=\"chatMessages\"\r\n          [isLoading]=\"isProcessingChat\"\r\n          [agentType]=\"agentType\"\r\n          [showChatInteractionToggles]=\"agentType === 'individual'\"\r\n          [showAiPrincipleToggle]=\"true\"\r\n          [showApprovalButton]=\"false\"\r\n          [showDropdown]=\"false\"\r\n          [showAgentNameInput]=\"true\"\r\n          [showFileUploadButton]=\"true\"\r\n          [displayedAgentName]=\"agentName\"\r\n          [agentNamePlaceholder]=\"'Current Agent Name'\"\r\n          [acceptedFileType]=\"fileType\"\r\n          (promptChange)=\"onPromptChanged($event)\"\r\n          (messageSent)=\"handleChatMessage($event)\"\r\n          (conversationalToggle)=\"onPlaygroundConversationalToggle($event)\"\r\n          (templateToggle)=\"onPlaygroundTemplateToggle($event)\"\r\n          (filesSelected)=\"onFilesSelected($event)\"\r\n          (approvalRequested)=\"onApprovalRequested()\"\r\n        >\r\n        </app-agent-execution-playground>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Right Panel: Blueprint/Output -->\r\n    <div class=\"right-panel\">\r\n      <!-- Right Panel Header -->\r\n      <div class=\"panel-header\">\r\n        <div class=\"tabs-container\">\r\n          <button\r\n            class=\"tab-btn\"\r\n            [class.active]=\"activeRightTab === 'blueprint'\"\r\n            (click)=\"setActiveRightTab('blueprint')\"\r\n          >\r\n            Blueprint\r\n          </button>\r\n          <button\r\n            class=\"tab-btn\"\r\n            [class.active]=\"activeRightTab === 'output'\"\r\n            (click)=\"setActiveRightTab('output')\"\r\n          >\r\n            Agent Output\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"panel-content\">\r\n        <!-- Blueprint Content -->\r\n        <div *ngIf=\"activeRightTab === 'blueprint'\" class=\"blueprint-content\">\r\n          <div class=\"blueprint-header\">\r\n            <h3>Agent Blueprint</h3>\r\n          </div>\r\n\r\n          <!-- Custom Blueprint Display -->\r\n          <div class=\"custom-blueprint-container\">\r\n            <!-- SVG Connecting Lines -->\r\n            <svg\r\n              class=\"connection-lines\"\r\n              viewBox=\"0 0 100 100\"\r\n              preserveAspectRatio=\"none\"\r\n            >\r\n              <!-- Line from System Prompt (top-left) to center -->\r\n              <line\r\n                x1=\"25\"\r\n                y1=\"25\"\r\n                x2=\"50\"\r\n                y2=\"50\"\r\n                stroke=\"#BBBEC5\"\r\n                stroke-width=\"0.3\"\r\n              />\r\n\r\n              <!-- Line from AI Model (top-right) to center -->\r\n              <line\r\n                x1=\"75\"\r\n                y1=\"25\"\r\n                x2=\"50\"\r\n                y2=\"50\"\r\n                stroke=\"#BBBEC5\"\r\n                stroke-width=\"0.3\"\r\n              />\r\n\r\n              <!-- Line from Knowledge Base (bottom-left) to center -->\r\n              <line\r\n                x1=\"25\"\r\n                y1=\"75\"\r\n                x2=\"50\"\r\n                y2=\"50\"\r\n                stroke=\"#BBBEC5\"\r\n                stroke-width=\"0.3\"\r\n              />\r\n\r\n              <!-- Line from Guardrails (bottom-right) to center -->\r\n              <line\r\n                x1=\"75\"\r\n                y1=\"75\"\r\n                x2=\"50\"\r\n                y2=\"50\"\r\n                stroke=\"#BBBEC5\"\r\n                stroke-width=\"0.3\"\r\n              />\r\n            </svg>\r\n\r\n            <!-- Central Progress Bar -->\r\n            <div class=\"central-progress\">\r\n              <div class=\"progress-ring\">\r\n                <svg class=\"progress-circle\" width=\"120\" height=\"120\">\r\n                  <defs>\r\n                    <linearGradient\r\n                      id=\"progressGradient\"\r\n                      x1=\"0%\"\r\n                      y1=\"0%\"\r\n                      x2=\"100%\"\r\n                      y2=\"100%\"\r\n                    >\r\n                      <stop offset=\"33.91%\" style=\"stop-color: #0084ff\" />\r\n                      <stop offset=\"100%\" style=\"stop-color: #03bdd4\" />\r\n                    </linearGradient>\r\n                  </defs>\r\n                  <circle\r\n                    class=\"progress-background\"\r\n                    cx=\"60\"\r\n                    cy=\"60\"\r\n                    r=\"50\"\r\n                    fill=\"none\"\r\n                    stroke=\"#e5e7eb\"\r\n                    stroke-width=\"8\"\r\n                  />\r\n                  <circle\r\n                    class=\"progress-bar\"\r\n                    cx=\"60\"\r\n                    cy=\"60\"\r\n                    r=\"50\"\r\n                    fill=\"none\"\r\n                    stroke=\"url(#progressGradient)\"\r\n                    stroke-width=\"8\"\r\n                    stroke-linecap=\"round\"\r\n                    [style.stroke-dasharray]=\"314\"\r\n                    [style.stroke-dashoffset]=\"\r\n                      314 - (314 * blueprintCompletionPercentage) / 100\r\n                    \"\r\n                    transform=\"rotate(180 60 60)\"\r\n                  />\r\n                </svg>\r\n                <div class=\"progress-content\">\r\n                  <div class=\"progress-percentage\">\r\n                    {{ blueprintCompletionPercentage }}%\r\n                  </div>\r\n                  <div class=\"progress-label\">Complete</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Blueprint Zones Layout -->\r\n            <div id=\"parent-box\">\r\n              <!-- <div id=\"box1\"> -->\r\n              <!-- System Prompt Zone -->\r\n              <div\r\n                class=\"blueprint-zone north-zone prompts-zone\"\r\n                [class.has-nodes]=\"blueprintPromptNodes.length > 0\"\r\n              >\r\n                <div\r\n                  class=\"zone-header\"\r\n                  (click)=\"toggleBlueprintZone('prompt')\"\r\n                >\r\n                  <div class=\"header-content\">\r\n                    <div class=\"header-icon\">\r\n                      <svg\r\n                        width=\"45\"\r\n                        height=\"44\"\r\n                        viewBox=\"0 0 45 44\"\r\n                        fill=\"none\"\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                      >\r\n                        <rect\r\n                          x=\"0.679688\"\r\n                          width=\"44\"\r\n                          height=\"44\"\r\n                          rx=\"8\"\r\n                          fill=\"#5082EF\"\r\n                        />\r\n                        <path\r\n                          d=\"M20.6166 25.5C20.5273 25.1539 20.347 24.8381 20.0942 24.5854C19.8415 24.3327 19.5257 24.1523 19.1796 24.063L13.0446 22.481C12.94 22.4513 12.8478 22.3883 12.7822 22.3014C12.7166 22.2146 12.6812 22.1088 12.6812 22C12.6812 21.8912 12.7166 21.7854 12.7822 21.6986C12.8478 21.6118 12.94 21.5487 13.0446 21.519L19.1796 19.936C19.5256 19.8468 19.8413 19.6666 20.094 19.414C20.3467 19.1615 20.5272 18.8459 20.6166 18.5L22.1986 12.365C22.228 12.2599 22.291 12.1673 22.3779 12.1014C22.4649 12.0355 22.571 11.9998 22.6801 11.9998C22.7892 11.9998 22.8954 12.0355 22.9823 12.1014C23.0692 12.1673 23.1322 12.2599 23.1616 12.365L24.7426 18.5C24.8319 18.8461 25.0123 19.1619 25.265 19.4146C25.5177 19.6673 25.8336 19.8477 26.1796 19.937L32.3146 21.518C32.4201 21.5471 32.5132 21.61 32.5795 21.6971C32.6458 21.7841 32.6817 21.8906 32.6817 22C32.6817 22.1094 32.6458 22.2159 32.5795 22.3029C32.5132 22.39 32.4201 22.4529 32.3146 22.482L26.1796 24.063C25.8336 24.1523 25.5177 24.3327 25.265 24.5854C25.0123 24.8381 24.8319 25.1539 24.7426 25.5L23.1606 31.635C23.1312 31.7401 23.0682 31.8327 22.9813 31.8986C22.8944 31.9646 22.7882 32.0003 22.6791 32.0003C22.57 32.0003 22.4639 31.9646 22.3769 31.8986C22.29 31.8327 22.227 31.7401 22.1976 31.635L20.6166 25.5Z\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                      </svg>\r\n                    </div>\r\n                    <h3 class=\"zone-title\">System Prompt</h3>\r\n                  </div>\r\n                  <div class=\"header-actions\">\r\n                    <span class=\"required-badge\">Required</span>\r\n                    <button class=\"accordion-toggle\" type=\"button\">\r\n                      <svg\r\n                        width=\"16\"\r\n                        height=\"16\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        fill=\"none\"\r\n                        [style.transform]=\"\r\n                          isBlueprintZoneExpanded('prompt')\r\n                            ? 'rotate(180deg)'\r\n                            : 'rotate(0deg)'\r\n                        \"\r\n                      >\r\n                        <path\r\n                          d=\"M6 9L12 15L18 9\"\r\n                          stroke=\"currentColor\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                      </svg>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n                <div\r\n                  class=\"zone-content\"\r\n                  *ngIf=\"isBlueprintZoneExpanded('prompt')\"\r\n                >\r\n                  <div\r\n                    *ngIf=\"blueprintPromptNodes.length === 0\"\r\n                    class=\"empty-state\"\r\n                  >\r\n                    No prompt configured\r\n                  </div>\r\n                  <div class=\"nodes-list\">\r\n                    <div\r\n                      *ngFor=\"let node of blueprintPromptNodes\"\r\n                      class=\"kanban-card\"\r\n                    >\r\n                      <span class=\"card-title\">{{\r\n                        node.name || \"Prompt\"\r\n                      }}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Knowledge Base Zone -->\r\n              <div\r\n                class=\"blueprint-zone west-zone knowledge-zone\"\r\n                [class.has-nodes]=\"blueprintKnowledgeNodes.length > 0\"\r\n              >\r\n                <div\r\n                  class=\"zone-header\"\r\n                  (click)=\"toggleBlueprintZone('knowledge')\"\r\n                >\r\n                  <div class=\"header-content\">\r\n                    <div class=\"header-icon\">\r\n                      <svg\r\n                        width=\"45\"\r\n                        height=\"44\"\r\n                        viewBox=\"0 0 45 44\"\r\n                        fill=\"none\"\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                      >\r\n                        <rect\r\n                          x=\"0.679688\"\r\n                          width=\"44\"\r\n                          height=\"44\"\r\n                          rx=\"8\"\r\n                          fill=\"#308666\"\r\n                        />\r\n                        <path\r\n                          d=\"M22.6797 17V31\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                        <path\r\n                          d=\"M26.6797 22H28.6797\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                        <path\r\n                          d=\"M26.6797 18H28.6797\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                        <path\r\n                          d=\"M13.6797 28C13.4145 28 13.1601 27.8946 12.9726 27.7071C12.785 27.5196 12.6797 27.2652 12.6797 27V14C12.6797 13.7348 12.785 13.4804 12.9726 13.2929C13.1601 13.1054 13.4145 13 13.6797 13H18.6797C19.7406 13 20.758 13.4214 21.5081 14.1716C22.2583 14.9217 22.6797 15.9391 22.6797 17C22.6797 15.9391 23.1011 14.9217 23.8513 14.1716C24.6014 13.4214 25.6188 13 26.6797 13H31.6797C31.9449 13 32.1993 13.1054 32.3868 13.2929C32.5743 13.4804 32.6797 13.7348 32.6797 14V27C32.6797 27.2652 32.5743 27.5196 32.3868 27.7071C32.1993 27.8946 31.9449 28 31.6797 28H25.6797C24.884 28 24.121 28.3161 23.5584 28.8787C22.9958 29.4413 22.6797 30.2044 22.6797 31C22.6797 30.2044 22.3636 29.4413 21.801 28.8787C21.2384 28.3161 20.4753 28 19.6797 28H13.6797Z\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                        <path\r\n                          d=\"M16.6797 22H18.6797\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                        <path\r\n                          d=\"M16.6797 18H18.6797\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                      </svg>\r\n                    </div>\r\n                    <h3 class=\"zone-title\">Knowledgebase</h3>\r\n                  </div>\r\n                  <div class=\"header-actions\">\r\n                    <span class=\"optional-badge\">Optional</span>\r\n                    <button class=\"accordion-toggle\" type=\"button\">\r\n                      <svg\r\n                        width=\"16\"\r\n                        height=\"16\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        fill=\"none\"\r\n                        [style.transform]=\"\r\n                          isBlueprintZoneExpanded('knowledge')\r\n                            ? 'rotate(180deg)'\r\n                            : 'rotate(0deg)'\r\n                        \"\r\n                      >\r\n                        <path\r\n                          d=\"M6 9L12 15L18 9\"\r\n                          stroke=\"currentColor\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                      </svg>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n                <div\r\n                  class=\"zone-content\"\r\n                  *ngIf=\"isBlueprintZoneExpanded('knowledge')\"\r\n                >\r\n                  <div\r\n                    *ngIf=\"blueprintKnowledgeNodes.length === 0\"\r\n                    class=\"empty-state\"\r\n                  >\r\n                    No knowledge base configured\r\n                  </div>\r\n                  <div class=\"nodes-list\">\r\n                    <div\r\n                      *ngFor=\"let node of blueprintKnowledgeNodes\"\r\n                      class=\"kanban-card\"\r\n                    >\r\n                      <span class=\"card-title\">{{\r\n                        node.name || \"Knowledge Base\"\r\n                      }}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <!-- </div> -->\r\n\r\n              <!-- <div id=\"box2\"> -->\r\n              <!-- AI Model Zone -->\r\n              <div\r\n                class=\"blueprint-zone east-zone models-zone\"\r\n                [class.has-nodes]=\"blueprintModelNodes.length > 0\"\r\n              >\r\n                <div class=\"zone-header\" (click)=\"toggleBlueprintZone('model')\">\r\n                  <div class=\"header-content\">\r\n                    <div class=\"header-icon\">\r\n                      <svg\r\n                        width=\"45\"\r\n                        height=\"44\"\r\n                        viewBox=\"0 0 45 44\"\r\n                        fill=\"none\"\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                      >\r\n                        <rect\r\n                          x=\"0.679688\"\r\n                          width=\"44\"\r\n                          height=\"44\"\r\n                          rx=\"8\"\r\n                          fill=\"#997BCF\"\r\n                        />\r\n                        <path\r\n                          d=\"M31.6797 18C31.6793 17.6493 31.5867 17.3048 31.4112 17.0012C31.2357 16.6975 30.9834 16.4454 30.6797 16.27L23.6797 12.27C23.3756 12.0945 23.0308 12.0021 22.6797 12.0021C22.3286 12.0021 21.9837 12.0945 21.6797 12.27L14.6797 16.27C14.376 16.4454 14.1237 16.6975 13.9481 17.0012C13.7726 17.3048 13.68 17.6493 13.6797 18V26C13.68 26.3508 13.7726 26.6952 13.9481 26.9989C14.1237 27.3025 14.376 27.5547 14.6797 27.73L21.6797 31.73C21.9837 31.9056 22.3286 31.998 22.6797 31.998C23.0308 31.998 23.3756 31.9056 23.6797 31.73L30.6797 27.73C30.9834 27.5547 31.2357 27.3025 31.4112 26.9989C31.5867 26.6952 31.6793 26.3508 31.6797 26V18Z\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                        <path\r\n                          d=\"M13.9795 17L22.6795 22L31.3795 17\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                        <path\r\n                          d=\"M22.6797 32V22\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                      </svg>\r\n                    </div>\r\n                    <h3 class=\"zone-title\">AI Model</h3>\r\n                  </div>\r\n                  <div class=\"header-actions\">\r\n                    <span class=\"required-badge\">Required</span>\r\n                    <button class=\"accordion-toggle\" type=\"button\">\r\n                      <svg\r\n                        width=\"16\"\r\n                        height=\"16\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        fill=\"none\"\r\n                        [style.transform]=\"\r\n                          isBlueprintZoneExpanded('model')\r\n                            ? 'rotate(180deg)'\r\n                            : 'rotate(0deg)'\r\n                        \"\r\n                      >\r\n                        <path\r\n                          d=\"M6 9L12 15L18 9\"\r\n                          stroke=\"currentColor\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                      </svg>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n                <div\r\n                  class=\"zone-content\"\r\n                  *ngIf=\"isBlueprintZoneExpanded('model')\"\r\n                >\r\n                  <div\r\n                    *ngIf=\"blueprintModelNodes.length === 0\"\r\n                    class=\"empty-state\"\r\n                  >\r\n                    No model configured\r\n                  </div>\r\n                  <div class=\"nodes-list\">\r\n                    <div\r\n                      *ngFor=\"let node of blueprintModelNodes\"\r\n                      class=\"kanban-card\"\r\n                    >\r\n                      <span class=\"card-title\">{{ node.name || \"Model\" }}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Guardrails Zone -->\r\n              <div\r\n                class=\"blueprint-zone south-zone guardrails-zone\"\r\n                [class.has-nodes]=\"blueprintGuardrailNodes.length > 0\"\r\n              >\r\n                <div\r\n                  class=\"zone-header\"\r\n                  (click)=\"toggleBlueprintZone('guardrail')\"\r\n                >\r\n                  <div class=\"header-content\">\r\n                    <div class=\"header-icon\">\r\n                      <svg\r\n                        width=\"45\"\r\n                        height=\"44\"\r\n                        viewBox=\"0 0 45 44\"\r\n                        fill=\"none\"\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                      >\r\n                        <rect\r\n                          x=\"0.679688\"\r\n                          width=\"44\"\r\n                          height=\"44\"\r\n                          rx=\"8\"\r\n                          fill=\"#DC2626\"\r\n                        />\r\n                        <path\r\n                          d=\"M30.6797 23C30.6797 28 27.1797 30.5 23.0197 31.95C22.8019 32.0238 22.5652 32.0202 22.3497 31.94C18.1797 30.5 14.6797 28 14.6797 23V16C14.6797 15.7347 14.785 15.4804 14.9726 15.2929C15.1601 15.1053 15.4145 15 15.6797 15C17.6797 15 20.1797 13.8 21.9197 12.28C22.1315 12.099 22.401 11.9995 22.6797 11.9995C22.9583 11.9995 23.2278 12.099 23.4397 12.28C25.1897 13.81 27.6797 15 29.6797 15C29.9449 15 30.1993 15.1053 30.3868 15.2929C30.5743 15.4804 30.6797 15.7347 30.6797 16V23Z\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                      </svg>\r\n                    </div>\r\n                    <h3 class=\"zone-title\">Guardrails</h3>\r\n                  </div>\r\n                  <div class=\"header-actions\">\r\n                    <span class=\"optional-badge\">Optional</span>\r\n                    <button class=\"accordion-toggle\" type=\"button\">\r\n                      <svg\r\n                        width=\"16\"\r\n                        height=\"16\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        fill=\"none\"\r\n                        [style.transform]=\"\r\n                          isBlueprintZoneExpanded('guardrail')\r\n                            ? 'rotate(180deg)'\r\n                            : 'rotate(0deg)'\r\n                        \"\r\n                      >\r\n                        <path\r\n                          d=\"M6 9L12 15L18 9\"\r\n                          stroke=\"currentColor\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                      </svg>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n                <div\r\n                  class=\"zone-content\"\r\n                  *ngIf=\"isBlueprintZoneExpanded('guardrail')\"\r\n                >\r\n                  <div\r\n                    *ngIf=\"blueprintGuardrailNodes.length === 0\"\r\n                    class=\"empty-state\"\r\n                  >\r\n                    No guardrails configured\r\n                  </div>\r\n                  <div class=\"nodes-list\">\r\n                    <div\r\n                      *ngFor=\"let node of blueprintGuardrailNodes\"\r\n                      class=\"kanban-card\"\r\n                    >\r\n                      <span class=\"card-title\">{{\r\n                        node.name || \"Guardrail\"\r\n                      }}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <!-- </div> -->\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Agent Output Content -->\r\n        <div *ngIf=\"activeRightTab === 'output'\" class=\"output-content\">\r\n          <div class=\"mock-content\">\r\n            <h3>Agent Output</h3>\r\n            <p>Agent responses and generated content will be displayed here.</p>\r\n            <div class=\"output-placeholder\">\r\n              <div class=\"output-section\">\r\n                <h4>Portfolio website HTML Code</h4>\r\n                <p class=\"output-meta\">Test Agent 1's Output</p>\r\n                <div class=\"output-preview\">\r\n                  <div class=\"code-block\">\r\n                    &lt;!DOCTYPE html&gt;<br />\r\n                    &lt;html lang=\"en\"&gt;<br />\r\n                    &lt;head&gt;<br />\r\n                    &nbsp;&nbsp;&lt;meta charset=\"UTF-8\"&gt;<br />\r\n                    &nbsp;&nbsp;&lt;title&gt;My Portfolio&lt;/title&gt;<br />\r\n                    &lt;/head&gt;<br />\r\n                    &lt;body&gt;<br />\r\n                    &nbsp;&nbsp;&lt;h1&gt;Jane Doe&lt;/h1&gt;<br />\r\n                    &lt;/body&gt;<br />\r\n                    &lt;/html&gt;\r\n                  </div>\r\n                  <button class=\"preview-btn\" type=\"button\">Preview</button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,OAAO,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,EAAE,QAAQ,MAAM;AAC9E,SAAiCC,WAAW,QAAoB,gBAAgB;AAChF,SAASC,iCAAiC,QAAQ,8EAA8E;AAEhI,SAA0BC,aAAa,QAAgD,wBAAwB;AAG/G,SAASC,WAAW,QAAQ,+CAA+C;;;;;;;;;;;;ICkBnEC,EAAA,CAAAC,cAAA,iBAKC;IACCD,EAAA,CAAAE,MAAA,gBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAITH,EADF,CAAAC,cAAA,cAAyD,yCAoBtD;IADCD,EALA,CAAAI,UAAA,0BAAAC,+FAAAC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAgBF,MAAA,CAAAG,eAAA,CAAAN,MAAA,CAAuB;IAAA,EAAC,yBAAAO,8FAAAP,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CACzBF,MAAA,CAAAK,iBAAA,CAAAR,MAAA,CAAyB;IAAA,EAAC,kCAAAS,uGAAAT,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CACjBF,MAAA,CAAAO,gCAAA,CAAAV,MAAA,CAAwC;IAAA,EAAC,4BAAAW,iGAAAX,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAC/CF,MAAA,CAAAS,0BAAA,CAAAZ,MAAA,CAAkC;IAAA,EAAC,2BAAAa,gGAAAb,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CACpCF,MAAA,CAAAW,eAAA,CAAAd,MAAA,CAAuB;IAAA,EAAC,+BAAAe,oGAAA;MAAArB,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CACpBF,MAAA,CAAAa,mBAAA,EAAqB;IAAA,EAAC;IAG/CtB,EADE,CAAAG,YAAA,EAAiC,EAC7B;;;;IApBFH,EAAA,CAAAuB,SAAA,EAAyB;IAWzBvB,EAXA,CAAAwB,UAAA,aAAAf,MAAA,CAAAgB,YAAA,CAAyB,cAAAhB,MAAA,CAAAiB,gBAAA,CACK,cAAAjB,MAAA,CAAAkB,SAAA,CACP,+BAAAlB,MAAA,CAAAkB,SAAA,kBACkC,+BAC3B,6BACF,uBACN,4BACK,8BACE,uBAAAlB,MAAA,CAAAmB,SAAA,CACG,8CACa,qBAAAnB,MAAA,CAAAoB,QAAA,CAChB;;;;;IAgNrB7B,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAMFH,EAJF,CAAAC,cAAA,cAGC,eAC0B;IAAAD,EAAA,CAAAE,MAAA,GAEvB;IACJF,EADI,CAAAG,YAAA,EAAO,EACL;;;;IAHqBH,EAAA,CAAAuB,SAAA,GAEvB;IAFuBvB,EAAA,CAAA8B,iBAAA,CAAAC,OAAA,CAAAC,IAAA,aAEvB;;;;;IAjBRhC,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAiC,UAAA,IAAAC,oDAAA,kBAGC;IAGDlC,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAiC,UAAA,IAAAE,oDAAA,kBAGC;IAMLnC,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAfDH,EAAA,CAAAuB,SAAA,EAAuC;IAAvCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAA2B,oBAAA,CAAAC,MAAA,OAAuC;IAOrBrC,EAAA,CAAAuB,SAAA,GAAuB;IAAvBvB,EAAA,CAAAwB,UAAA,YAAAf,MAAA,CAAA2B,oBAAA,CAAuB;;;;;IA+G5CpC,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,MAAA,qCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAMFH,EAJF,CAAAC,cAAA,cAGC,eAC0B;IAAAD,EAAA,CAAAE,MAAA,GAEvB;IACJF,EADI,CAAAG,YAAA,EAAO,EACL;;;;IAHqBH,EAAA,CAAAuB,SAAA,GAEvB;IAFuBvB,EAAA,CAAA8B,iBAAA,CAAAQ,OAAA,CAAAN,IAAA,qBAEvB;;;;;IAjBRhC,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAiC,UAAA,IAAAM,oDAAA,kBAGC;IAGDvC,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAiC,UAAA,IAAAO,oDAAA,kBAGC;IAMLxC,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAfDH,EAAA,CAAAuB,SAAA,EAA0C;IAA1CvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAgC,uBAAA,CAAAJ,MAAA,OAA0C;IAOxBrC,EAAA,CAAAuB,SAAA,GAA0B;IAA1BvB,EAAA,CAAAwB,UAAA,YAAAf,MAAA,CAAAgC,uBAAA,CAA0B;;;;;IAyF/CzC,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAMFH,EAJF,CAAAC,cAAA,cAGC,eAC0B;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IACrDF,EADqD,CAAAG,YAAA,EAAO,EACtD;;;;IADqBH,EAAA,CAAAuB,SAAA,GAA0B;IAA1BvB,EAAA,CAAA8B,iBAAA,CAAAY,OAAA,CAAAV,IAAA,YAA0B;;;;;IAfzDhC,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAiC,UAAA,IAAAU,oDAAA,kBAGC;IAGD3C,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAiC,UAAA,IAAAW,oDAAA,kBAGC;IAIL5C,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAbDH,EAAA,CAAAuB,SAAA,EAAsC;IAAtCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAoC,mBAAA,CAAAR,MAAA,OAAsC;IAOpBrC,EAAA,CAAAuB,SAAA,GAAsB;IAAtBvB,EAAA,CAAAwB,UAAA,YAAAf,MAAA,CAAAoC,mBAAA,CAAsB;;;;;IA0E3C7C,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAMFH,EAJF,CAAAC,cAAA,cAGC,eAC0B;IAAAD,EAAA,CAAAE,MAAA,GAEvB;IACJF,EADI,CAAAG,YAAA,EAAO,EACL;;;;IAHqBH,EAAA,CAAAuB,SAAA,GAEvB;IAFuBvB,EAAA,CAAA8B,iBAAA,CAAAgB,OAAA,CAAAd,IAAA,gBAEvB;;;;;IAjBRhC,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAiC,UAAA,IAAAc,oDAAA,kBAGC;IAGD/C,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAiC,UAAA,IAAAe,oDAAA,kBAGC;IAMLhD,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAfDH,EAAA,CAAAuB,SAAA,EAA0C;IAA1CvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAwC,uBAAA,CAAAZ,MAAA,OAA0C;IAOxBrC,EAAA,CAAAuB,SAAA,GAA0B;IAA1BvB,EAAA,CAAAwB,UAAA,YAAAf,MAAA,CAAAwC,uBAAA,CAA0B;;;;;;IA5drDjD,EAFJ,CAAAC,cAAA,cAAsE,cACtC,SACxB;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IACrBF,EADqB,CAAAG,YAAA,EAAK,EACpB;IAGNH,EAAA,CAAAC,cAAA,cAAwC;;IAEtCD,EAAA,CAAAC,cAAA,cAIC;IAgCCD,EA9BA,CAAAkD,SAAA,eAOE,eAUA,eAUA,eAUA;IACJlD,EAAA,CAAAG,YAAA,EAAM;;IAIJH,EADF,CAAAC,cAAA,eAA8B,eACD;;IAGrBD,EAFJ,CAAAC,cAAA,eAAsD,YAC9C,0BAOH;IAECD,EADA,CAAAkD,SAAA,gBAAoD,gBACF;IAEtDlD,EADE,CAAAG,YAAA,EAAiB,EACZ;IAUPH,EATA,CAAAkD,SAAA,kBAQE,kBAeA;IACJlD,EAAA,CAAAG,YAAA,EAAM;;IAEJH,EADF,CAAAC,cAAA,eAA8B,eACK;IAC/BD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAG1CF,EAH0C,CAAAG,YAAA,EAAM,EACtC,EACF,EACF;IAUFH,EAPJ,CAAAC,cAAA,eAAqB,eAMlB,eAIE;IADCD,EAAA,CAAAI,UAAA,mBAAA+C,8DAAA;MAAAnD,EAAA,CAAAO,aAAA,CAAA6C,GAAA;MAAA,MAAA3C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA4C,mBAAA,CAAoB,QAAQ,CAAC;IAAA,EAAC;IAGrCrD,EADF,CAAAC,cAAA,eAA4B,eACD;;IACvBD,EAAA,CAAAC,cAAA,eAMC;IAQCD,EAPA,CAAAkD,SAAA,gBAME,gBAOA;IAENlD,EADE,CAAAG,YAAA,EAAM,EACF;;IACNH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IACtCF,EADsC,CAAAG,YAAA,EAAK,EACrC;IAEJH,EADF,CAAAC,cAAA,eAA4B,gBACG;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,kBAA+C;;IAC7CD,EAAA,CAAAC,cAAA,eAUC;IACCD,EAAA,CAAAkD,SAAA,gBAME;IAIVlD,EAHM,CAAAG,YAAA,EAAM,EACC,EACL,EACF;IACNH,EAAA,CAAAiC,UAAA,KAAAqB,8CAAA,kBAGC;IAkBHtD,EAAA,CAAAG,YAAA,EAAM;;IAOJH,EAJF,CAAAC,cAAA,eAGC,eAIE;IADCD,EAAA,CAAAI,UAAA,mBAAAmD,8DAAA;MAAAvD,EAAA,CAAAO,aAAA,CAAA6C,GAAA;MAAA,MAAA3C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA4C,mBAAA,CAAoB,WAAW,CAAC;IAAA,EAAC;IAGxCrD,EADF,CAAAC,cAAA,eAA4B,eACD;;IACvBD,EAAA,CAAAC,cAAA,eAMC;IA2CCD,EA1CA,CAAAkD,SAAA,gBAME,gBAOA,gBAOA,gBAOA,gBAOA,gBAOA,gBAOA;IAENlD,EADE,CAAAG,YAAA,EAAM,EACF;;IACNH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IACtCF,EADsC,CAAAG,YAAA,EAAK,EACrC;IAEJH,EADF,CAAAC,cAAA,eAA4B,gBACG;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,kBAA+C;;IAC7CD,EAAA,CAAAC,cAAA,eAUC;IACCD,EAAA,CAAAkD,SAAA,gBAME;IAIVlD,EAHM,CAAAG,YAAA,EAAM,EACC,EACL,EACF;IACNH,EAAA,CAAAiC,UAAA,KAAAuB,8CAAA,kBAGC;IAkBHxD,EAAA,CAAAG,YAAA,EAAM;;IASJH,EAJF,CAAAC,cAAA,eAGC,eACiE;IAAvCD,EAAA,CAAAI,UAAA,mBAAAqD,8DAAA;MAAAzD,EAAA,CAAAO,aAAA,CAAA6C,GAAA;MAAA,MAAA3C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA4C,mBAAA,CAAoB,OAAO,CAAC;IAAA,EAAC;IAE3DrD,EADF,CAAAC,cAAA,eAA4B,eACD;;IACvBD,EAAA,CAAAC,cAAA,eAMC;IAsBCD,EArBA,CAAAkD,SAAA,gBAME,gBAOA,gBAOA,gBAOA;IAENlD,EADE,CAAAG,YAAA,EAAM,EACF;;IACNH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IACjCF,EADiC,CAAAG,YAAA,EAAK,EAChC;IAEJH,EADF,CAAAC,cAAA,eAA4B,gBACG;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,kBAA+C;;IAC7CD,EAAA,CAAAC,cAAA,eAUC;IACCD,EAAA,CAAAkD,SAAA,gBAME;IAIVlD,EAHM,CAAAG,YAAA,EAAM,EACC,EACL,EACF;IACNH,EAAA,CAAAiC,UAAA,KAAAyB,8CAAA,kBAGC;IAgBH1D,EAAA,CAAAG,YAAA,EAAM;;IAOJH,EAJF,CAAAC,cAAA,eAGC,eAIE;IADCD,EAAA,CAAAI,UAAA,mBAAAuD,8DAAA;MAAA3D,EAAA,CAAAO,aAAA,CAAA6C,GAAA;MAAA,MAAA3C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA4C,mBAAA,CAAoB,WAAW,CAAC;IAAA,EAAC;IAGxCrD,EADF,CAAAC,cAAA,eAA4B,eACD;;IACvBD,EAAA,CAAAC,cAAA,eAMC;IAQCD,EAPA,CAAAkD,SAAA,gBAME,gBAOA;IAENlD,EADE,CAAAG,YAAA,EAAM,EACF;;IACNH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IACnCF,EADmC,CAAAG,YAAA,EAAK,EAClC;IAEJH,EADF,CAAAC,cAAA,eAA4B,gBACG;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,kBAA+C;;IAC7CD,EAAA,CAAAC,cAAA,eAUC;IACCD,EAAA,CAAAkD,SAAA,gBAME;IAIVlD,EAHM,CAAAG,YAAA,EAAM,EACC,EACL,EACF;IACNH,EAAA,CAAAiC,UAAA,KAAA2B,8CAAA,kBAGC;IAsBT5D,EAJM,CAAAG,YAAA,EAAM,EAEF,EACF,EACF;;;;IAnZMH,EAAA,CAAAuB,SAAA,IAA8B;IAC9BvB,EADA,CAAA6D,WAAA,yBAA8B,kCAAApD,MAAA,CAAAqD,6BAAA,OAG7B;IAMD9D,EAAA,CAAAuB,SAAA,GACF;IADEvB,EAAA,CAAA+D,kBAAA,MAAAtD,MAAA,CAAAqD,6BAAA,OACF;IAYF9D,EAAA,CAAAuB,SAAA,GAAmD;IAAnDvB,EAAA,CAAAgE,WAAA,cAAAvD,MAAA,CAAA2B,oBAAA,CAAAC,MAAA,KAAmD;IAyC3CrC,EAAA,CAAAuB,SAAA,IAIC;IAJDvB,EAAA,CAAA6D,WAAA,cAAApD,MAAA,CAAAwD,uBAAA,+CAIC;IAeNjE,EAAA,CAAAuB,SAAA,GAAuC;IAAvCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAwD,uBAAA,WAAuC;IAwB1CjE,EAAA,CAAAuB,SAAA,EAAsD;IAAtDvB,EAAA,CAAAgE,WAAA,cAAAvD,MAAA,CAAAgC,uBAAA,CAAAJ,MAAA,KAAsD;IA4E9CrC,EAAA,CAAAuB,SAAA,IAIC;IAJDvB,EAAA,CAAA6D,WAAA,cAAApD,MAAA,CAAAwD,uBAAA,kDAIC;IAeNjE,EAAA,CAAAuB,SAAA,GAA0C;IAA1CvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAwD,uBAAA,cAA0C;IA0B7CjE,EAAA,CAAAuB,SAAA,EAAkD;IAAlDvB,EAAA,CAAAgE,WAAA,cAAAvD,MAAA,CAAAoC,mBAAA,CAAAR,MAAA,KAAkD;IAoD1CrC,EAAA,CAAAuB,SAAA,IAIC;IAJDvB,EAAA,CAAA6D,WAAA,cAAApD,MAAA,CAAAwD,uBAAA,8CAIC;IAeNjE,EAAA,CAAAuB,SAAA,GAAsC;IAAtCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAwD,uBAAA,UAAsC;IAsBzCjE,EAAA,CAAAuB,SAAA,EAAsD;IAAtDvB,EAAA,CAAAgE,WAAA,cAAAvD,MAAA,CAAAwC,uBAAA,CAAAZ,MAAA,KAAsD;IAyC9CrC,EAAA,CAAAuB,SAAA,IAIC;IAJDvB,EAAA,CAAA6D,WAAA,cAAApD,MAAA,CAAAwD,uBAAA,kDAIC;IAeNjE,EAAA,CAAAuB,SAAA,GAA0C;IAA1CvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAwD,uBAAA,cAA0C;;;;;IA4BjDjE,EAFJ,CAAAC,cAAA,cAAgE,cACpC,SACpB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,oEAA6D;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAGhEH,EAFJ,CAAAC,cAAA,cAAgC,cACF,SACtB;IAAAD,EAAA,CAAAE,MAAA,kCAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpCH,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAE,MAAA,6BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAE9CH,EADF,CAAAC,cAAA,eAA4B,eACF;IACtBD,EAAA,CAAAE,MAAA,wBAAqB;IAAAF,EAAA,CAAAkD,SAAA,UAAM;IAC3BlD,EAAA,CAAAE,MAAA,2BAAsB;IAAAF,EAAA,CAAAkD,SAAA,UAAM;IAC5BlD,EAAA,CAAAE,MAAA,eAAY;IAAAF,EAAA,CAAAkD,SAAA,UAAM;IAClBlD,EAAA,CAAAE,MAAA,6CAAwC;IAAAF,EAAA,CAAAkD,SAAA,UAAM;IAC9ClD,EAAA,CAAAE,MAAA,gDAAmD;IAAAF,EAAA,CAAAkD,SAAA,UAAM;IACzDlD,EAAA,CAAAE,MAAA,gBAAa;IAAAF,EAAA,CAAAkD,SAAA,UAAM;IACnBlD,EAAA,CAAAE,MAAA,eAAY;IAAAF,EAAA,CAAAkD,SAAA,UAAM;IAClBlD,EAAA,CAAAE,MAAA,sCAAyC;IAAAF,EAAA,CAAAkD,SAAA,UAAM;IAC/ClD,EAAA,CAAAE,MAAA,gBAAa;IAAAF,EAAA,CAAAkD,SAAA,UAAM;IACnBlD,EAAA,CAAAE,MAAA,iBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,kBAA0C;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAK3DF,EAL2D,CAAAG,YAAA,EAAS,EACtD,EACF,EACF,EACF,EACF;;;ADlkBd,WAAa+D,uBAAuB;EAA9B,MAAOA,uBAAuB;IAsFxBC,KAAA;IACAC,MAAA;IACAC,YAAA;IACAC,sBAAA;IACAC,YAAA;IACAC,aAAA;IACAC,WAAA;IACAC,oBAAA;IA5FVC,cAAc,GAAc,CAC1B;MAAEC,EAAE,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAgB,CAAE,EAC3C;MAAED,EAAE,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAc,CAAE,EAC7C;MAAED,EAAE,EAAE,cAAc;MAAEC,KAAK,EAAE,SAAS;MAAEC,QAAQ,EAAE;IAAI,CAAE,CACzD;IAEDC,OAAO,GAAkB,IAAI;IAC7BpD,SAAS,GAAW,YAAY;IAChCC,SAAS,GAAW,OAAO;IAC3BoD,WAAW,GAAW,EAAE;IAGxBC,cAAc;IAEdC,YAAY,GAAU,EAAE;IACxBC,gBAAgB,GAAW,CAAC;IAC5BC,gBAAgB;IAChBC,SAAS,GAAY,KAAK;IAC1BC,MAAM,GAAW,YAAY;IAC7B7D,YAAY,GAAkB,EAAE;IAChCC,gBAAgB,GAAY,KAAK;IACjC6D,SAAS,GAAG,EAAE;IACdC,YAAY,GAAU,EAAE;IACjBC,SAAS;IACT5D,QAAQ,GAAW,gFAAgF;IAC1G6D,kBAAkB,GAAgB,IAAI;IACtCC,kBAAkB,GAAY,KAAK;IACnCC,WAAW;IACXC,kBAAkB,GAAG9F,WAAW,CAAC+F,kBAAkB,IAAI,KAAK;IACrDC,mBAAmB,GAAY,KAAK;IAC3CC,gBAAgB;IACRC,QAAQ,GAAG,IAAI3G,OAAO,EAAQ;IACtC4G,WAAW,GAAW,gBAAgB;IACtCC,QAAQ,GAAU,CAChB;MAAEvB,EAAE,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAS,CAAE,EAChC;MAAED,EAAE,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAW,CAAE,EAClC;MAAED,EAAE,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAc,CAAE,CACtC;IACDuB,QAAQ,GAAG,KAAK;IAChBC,UAAU;IACVC,WAAW,GAAU,EAAE;IACvBC,WAAW,GAAG,KAAK;IACnBC,WAAW,GAAY,KAAK;IAC5BC,aAAa,GAAW,EAAE;IAC1BC,UAAU,GAAU,EAAE;IACtBC,aAAa,GAAU,EAAE;IACzBC,QAAQ,GAAG,CAAC;IACZC,SAAS,GAAG,KAAK;IACjBC,WAAW,GAAW,EAAE;IACxBC,eAAe,GAAa,EAAE;IAC9BC,iBAAiB,GAAW,CAAC;IAC7BC,WAAW,GAAW,UAAU;IAChCC,oBAAoB,GAAY,KAAK;IACrCC,cAAc,GAAW,WAAW;IACpCC,mBAAmB,GAAQ,IAAI;IAC/BC,eAAe,GAAU,EAAE;IAC3BC,WAAW,GAAU,EAAE;IACvBC,WAAW,GAAU,EAAE;IACvBC,cAAc,GAAW,EAAE;IAC3BC,iBAAiB,GAAW,EAAE;IAC9BC,yBAAyB,GAAW,EAAE;IACtCC,sBAAsB,GAAU,EAAE;IAClCC,eAAe,GAAa,EAAE;IAC9BC,wBAAwB,GAAG,KAAK;IAChCC,sBAAsB,GAAG,IAAIxI,OAAO,EAAW;IAC/CyI,gBAAgB,GAAU,EAAE;IAC5BC,SAAS,GAAW,EAAE;IACtBC,aAAa,GAAqB,EAAE;IAEpCnE,6BAA6B,GAAW,CAAC;IACzC1B,oBAAoB,GAAU,EAAE;IAChCS,mBAAmB,GAAU,EAAE;IAC/BJ,uBAAuB,GAAU,EAAE;IACnCQ,uBAAuB,GAAU,EAAE;IACnCiF,kBAAkB,GAAU,EAAE;IAEtBC,sBAAsB,GAA+B;MAC3DC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE,IAAI;MACXC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE,IAAI;MACfC,IAAI,EAAE;KACP;IAEDC,YACUtE,KAAqB,EACrBC,MAAc,EACdC,YAAiC,EACjCC,sBAA8C,EAC9CC,YAAiC,EACjCC,aAA4B,EAC5BC,WAAwB,EACxBC,oBAA0C;MAP1C,KAAAP,KAAK,GAALA,KAAK;MACL,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,YAAY,GAAZA,YAAY;MACZ,KAAAC,sBAAsB,GAAtBA,sBAAsB;MACtB,KAAAC,YAAY,GAAZA,YAAY;MACZ,KAAAC,aAAa,GAAbA,aAAa;MACb,KAAAC,WAAW,GAAXA,WAAW;MACX,KAAAC,oBAAoB,GAApBA,oBAAoB;MAE5B,IAAI,CAACe,SAAS,GAAG,IAAI,CAAChB,WAAW,CAACiE,KAAK,CAAC;QACtCC,gBAAgB,EAAE,CAAC,IAAI,CAAC;QACxBC,aAAa,EAAE,CAAC,KAAK;OACtB,CAAC;IACJ;IAEAC,QAAQA,CAAA;MACN,IAAI,CAACjD,WAAW,GAAGkD,MAAM,CAACC,UAAU,EAAE;MACtC,IAAI,CAAC5E,KAAK,CAAC6E,MAAM,CAACC,SAAS,CAAED,MAAM,IAAI;QACrC,IAAI,CAACrH,SAAS,GAAGqH,MAAM,CAAC,MAAM,CAAC,IAAI,YAAY;MACjD,CAAC,CAAC;MACF,IAAI,CAAC7E,KAAK,CAAC+E,WAAW,CAACD,SAAS,CAAED,MAAM,IAAI;QAC1C,IAAIA,MAAM,CAAC,IAAI,CAAC,EAAE;UAChB,IAAI,CAACjE,OAAO,GAAGiE,MAAM,CAAC,IAAI,CAAC;UAC3B,IAAI,CAACG,aAAa,CAACH,MAAM,CAAC,IAAI,CAAC,CAAC;QAClC;MACF,CAAC,CAAC;MAEF,IAAI,CAACvH,YAAY,GAAG,CAClB;QACE2H,IAAI,EAAE,IAAI;QACVC,IAAI,EAAE,kBAAkB,IAAI,CAACzH,SAAS,IAAI,YAAY;OACvD,CACF;IACH;IAEA0H,WAAWA,CAAA;MACT,IAAI,CAACrD,QAAQ,CAACsD,IAAI,EAAE;MACpB,IAAI,CAACtD,QAAQ,CAACuD,QAAQ,EAAE;MACxB,IAAI,IAAI,CAACxD,gBAAgB,EAAE;QACzByD,aAAa,CAAC,IAAI,CAACzD,gBAAgB,CAAC;MACtC;IACF;IAEA0D,WAAWA,CAACC,KAAoC;MAC9C,IAAI,CAAC1C,WAAW,GAAG0C,KAAK,CAAC/E,EAAE;MAC3B,IAAI,CAACsB,WAAW,GAAGyD,KAAK,CAAC9E,KAAK;IAChC;IAEAsE,aAAaA,CAACpE,OAAe;MAC3B,IAAI,CAAC8B,SAAS,GAAG,IAAI;MACrB,IAAI,IAAI,CAAClF,SAAS,KAAK,eAAe,EAAE;QACtC,IAAI,CAAC0C,YAAY,CAACuF,gCAAgC,CAAC7E,OAAO,CAAC,CAACkE,SAAS,CAAC;UACpEM,IAAI,EAAGM,QAAa,IAAI;YACtB,IAAI,CAACC,uBAAuB,CAACD,QAAQ,CAAC;UACxC,CAAC;UACDE,KAAK,EAAGA,KAAU,IAAI;YACpBC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;YAC1D,IAAI,CAAClD,SAAS,GAAG,KAAK;UACxB;SACD,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAACxC,YAAY,CAAC4F,YAAY,CAAClF,OAAO,CAAC,CAACkE,SAAS,CAAC;UAChDM,IAAI,EAAGM,QAAa,IAAI;YACtB,IAAI,CAACC,uBAAuB,CAACD,QAAQ,CAAC;UACxC,CAAC;UACDE,KAAK,EAAGA,KAAU,IAAI;YACpBC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;YACvD,IAAI,CAAClD,SAAS,GAAG,KAAK;UACxB;SACD,CAAC;MACJ;IACF;IAEQiD,uBAAuBA,CAACD,QAAa;MAC3C,IAAI,CAAChD,SAAS,GAAG,KAAK;MACtB,IAAIqD,SAAS;MACb,IAAIL,QAAQ,CAACM,YAAY,IAAIC,KAAK,CAACC,OAAO,CAACR,QAAQ,CAACM,YAAY,CAAC,IAAIN,QAAQ,CAACM,YAAY,CAAC9H,MAAM,GAAG,CAAC,EAAE;QACrG6H,SAAS,GAAGL,QAAQ,CAACM,YAAY,CAAC,CAAC,CAAC;MACtC,CAAC,MAAM,IAAIN,QAAQ,CAAC7E,WAAW,EAAE;QAC/BkF,SAAS,GAAGL,QAAQ,CAAC7E,WAAW;MAClC,CAAC,MAAM,IAAI6E,QAAQ,CAACS,IAAI,EAAE;QACxBJ,SAAS,GAAGL,QAAQ,CAACS,IAAI;MAC3B,CAAC,MAAM;QACLJ,SAAS,GAAGL,QAAQ;MACtB;MAEA,IAAIK,SAAS,EAAE;QACb,IAAI,CAAC9C,mBAAmB,GAAG8C,SAAS;QACpC,IAAI,CAACtI,SAAS,GAAGsI,SAAS,CAAClI,IAAI,IAAIkI,SAAS,CAACtI,SAAS,IAAI,OAAO;QACjE,IAAI,CAACoD,WAAW,GAAGkF,SAAS,CAACK,WAAW,IAAIL,SAAS,CAAClF,WAAW,IAAI,EAAE;QACvE,IAAI,CAACwC,cAAc,GAAG,IAAI,CAAC5F,SAAS;QAEpC,IAAI,IAAI,CAACH,YAAY,CAACY,MAAM,GAAG,CAAC,EAAE;UAChC,IAAI,CAACZ,YAAY,CAAC,CAAC,CAAC,CAAC4H,IAAI,GAAG,kBAAkB,IAAI,CAACzH,SAAS,6BAA6B;QAC3F;QAEA,IAAI,CAAC4I,cAAc,CAACN,SAAS,CAAC;MAChC;IACF;IAEQM,cAAcA,CAACN,SAAc;MACnC,IAAI,CAACO,gCAAgC,CAACP,SAAS,CAAC;IAClD;IAEApJ,iBAAiBA,CAAC4J,OAAe;MAC/B,IAAI,IAAI,CAAC/I,SAAS,KAAK,YAAY,EAAE;QACnC,IAAI,CAAC,IAAI,CAAC6F,cAAc,IAAI,IAAI,CAACA,cAAc,KAAK,SAAS,EAAE;UAC7D,IAAI,CAACmD,cAAc,CAAC,0DAA0D,CAAC;UAC/E;QACF;QAEA,IAAIC,cAAc,GAAGF,OAAO;QAC5B,IAAI,IAAI,CAAC/C,sBAAsB,CAACtF,MAAM,GAAG,CAAC,EAAE;UAC1C,MAAMwI,SAAS,GAAG,IAAI,CAAClD,sBAAsB,CAACmD,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACC,YAAY,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;UACzFL,cAAc,GAAG,GAAGF,OAAO,0BAA0BG,SAAS,EAAE;QAClE;QAEA,IAAI,CAACpJ,YAAY,GAAG,CAAC,GAAG,IAAI,CAACA,YAAY,EAAE;UAAE2H,IAAI,EAAE,MAAM;UAAEC,IAAI,EAAEuB;QAAc,CAAE,CAAC;QAClF,IAAI,CAAClJ,gBAAgB,GAAG,IAAI;QAC5B,MAAMiH,gBAAgB,GAAG,IAAI,CAAClD,SAAS,CAACyF,GAAG,CAAC,kBAAkB,CAAC,EAAEC,KAAK,IAAI,KAAK;QAC/E,MAAMvC,aAAa,GAAG,IAAI,CAACnD,SAAS,CAACyF,GAAG,CAAC,eAAe,CAAC,EAAEC,KAAK,IAAI,KAAK;QAEzE;QACA,MAAMC,SAAS,GAAG,IAAI,CAAChE,mBAAmB,CAACiE,WAAW,IAAI,IAAI,CAACzJ,SAAS;QACxE,MAAM0J,iBAAiB,GAAG,IAAI,CAAClE,mBAAmB,CAACmE,gBAAgB,IAAI,IAAI,CAACnE,mBAAmB,CAACoE,WAAW;QAE3G,IAAI,IAAI,CAAC7D,sBAAsB,CAACtF,MAAM,GAAG,CAAC,EAAE;UAC1C,IAAI,CAACoJ,+BAA+B,CAACf,OAAO,EAAEU,SAAS,EAAEE,iBAAiB,EAAE3C,gBAAgB,EAAEC,aAAa,CAAC;UAC5G;QACF;QAEA,IAAI,CAAC8C,qBAAqB,CAAChB,OAAO,EAAEU,SAAS,EAAEE,iBAAiB,EAAE3C,gBAAgB,EAAEC,aAAa,CAAC;MACpG,CAAC,MAAM,IAAI,IAAI,CAACjH,SAAS,KAAK,eAAe,EAAE;QAC7C,IAAI,CAACD,gBAAgB,GAAG,IAAI;QAC5B,IAAIiK,OAAO,GAAG;UACZ/F,WAAW,EAAE,IAAI,CAACA,WAAW;UAC7Bb,OAAO,EAAE6G,MAAM,CAAC,IAAI,CAAC7G,OAAO,CAAC;UAC7B8G,IAAI,EAAE,IAAI,CAACtH,YAAY,CAACuH,aAAa,EAAE,IAAI,uBAAuB;UAClEC,UAAU,EAAE;YAAEC,QAAQ,EAAEtB;UAAO;SAChC;QAED,IAAI,IAAI,CAAC/C,sBAAsB,CAACtF,MAAM,GAAG,CAAC,EAAE;UAC1C,MAAM4J,WAAW,GAAG,IAAI,CAACtE,sBAAsB,CAAC,CAAC,CAAC;UAClD,IAAIiD,cAAsB;UAC1B,IAAI,IAAI,CAACjD,sBAAsB,CAACtF,MAAM,GAAG,CAAC,EAAE;YAC1C,MAAMwI,SAAS,GAAG,IAAI,CAAClD,sBAAsB,CAACmD,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACC,YAAY,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;YACzFL,cAAc,GAAG,sBAAsBC,SAAS,EAAE;YAClD,IAAI,CAACpJ,YAAY,GAAG,CAAC;cAAE2H,IAAI,EAAE,MAAM;cAAEC,IAAI,EAAEuB;YAAc,CAAE,CAAC;UAC9D;UAEA,IAAI,CAACtG,sBAAsB,CAAC4H,0BAA0B,CAACP,OAAO,EAAEM,WAAW,CAAC,CAACE,IAAI,CAC/E1M,QAAQ,CAAC,MAAK;YACZ,IAAI,CAACiC,gBAAgB,GAAG,KAAK;YAC7B,IAAI,CAACmG,wBAAwB,GAAG,KAAK;UACvC,CAAC,CAAC,EACFtI,SAAS,CAAC,IAAI,CAACuI,sBAAsB,CAAC,CACvC,CAACmB,SAAS,CAAC;YACVM,IAAI,EAAG6C,GAAG,IAAK,IAAI,CAACC,0BAA0B,CAACD,GAAG,EAAE1B,OAAO,CAAC;YAC5DX,KAAK,EAAGuC,GAAQ,IAAI;cAClB,IAAI,CAAC7K,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;gBAAE2H,IAAI,EAAE,MAAM;gBAAEC,IAAI,EAAEqB;cAAO,CAAE,EAC/B;gBAAEtB,IAAI,EAAE,IAAI;gBAAEC,IAAI,EAAEiD,GAAG,EAAEvC,KAAK,EAAEW,OAAO,IAAI4B,GAAG,EAAE5B,OAAO,IAAI;cAAuB,CAAE,CACrF;YACH;WACD,CAAC;QACJ,CAAC,MAAM;UACL,IAAI,CAACpG,sBAAsB,CAACiI,kBAAkB,CAACZ,OAAO,CAAC,CAACQ,IAAI,CAC1D1M,QAAQ,CAAC,MAAK;YACZ,IAAI,CAACiC,gBAAgB,GAAG,KAAK;YAC7B,IAAI,CAACmG,wBAAwB,GAAG,KAAK;UACvC,CAAC,CAAC,EACFtI,SAAS,CAAC,IAAI,CAACuI,sBAAsB,CAAC,CACvC,CAACmB,SAAS,CAAC;YACVM,IAAI,EAAG6C,GAAG,IAAK,IAAI,CAACC,0BAA0B,CAACD,GAAG,EAAE1B,OAAO,CAAC;YAC5DX,KAAK,EAAGuC,GAAQ,IAAI;cAClB,IAAI,CAAC7K,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;gBAAE2H,IAAI,EAAE,MAAM;gBAAEC,IAAI,EAAEqB;cAAO,CAAE,EAC/B;gBAAEtB,IAAI,EAAE,IAAI;gBAAEC,IAAI,EAAEiD,GAAG,EAAEvC,KAAK,EAAEW,OAAO,IAAI4B,GAAG,EAAE5B,OAAO,IAAI;cAAuB,CAAE,CACrF;YACH;WACD,CAAC;QACJ;MACF;IACF;IAGA9J,eAAeA,CAACwH,MAAsB;MACpC,IAAI,CAAC7C,SAAS,GAAG6C,MAAM,CAACpG,IAAI,IAAIwK,MAAM,CAACpE,MAAM,CAAC+C,KAAK,CAAC,IAAI,EAAE;IAC5D;IAEAnK,gCAAgCA,CAACmK,KAAc,GAAS;IAExDjK,0BAA0BA,CAACiK,KAAc,GAAS;IAElD/J,eAAeA,CAACqL,KAAa;MAC3B,IAAI,CAAChG,aAAa,GAAGgG,KAAK;IAC5B;IAEAnL,mBAAmBA,CAAA,GAAU;IAE7BoL,QAAQA,CAAA,GAAU;IAElBC,aAAaA,CAACC,OAA8B,GAAS;IAErDC,mBAAmBA,CAACC,MAAiC,GAAS;IAE9DC,YAAYA,CAAA;MACV,IAAI,CAAC3I,MAAM,CAAC4I,QAAQ,CAAC,CAAC,eAAe,EAAE,IAAI,CAACrL,SAAS,CAAC,EAAE;QACtDuH,WAAW,EAAE;UAAEtE,EAAE,EAAE,IAAI,CAACG,OAAO;UAAEkI,IAAI,EAAE;QAAM;OAC9C,CAAC;IACJ;IAEAC,SAASA,CAAA;MACP,IAAI,CAAC9I,MAAM,CAAC4I,QAAQ,CAAC,CAAC,eAAe,EAAE,IAAI,CAACrL,SAAS,CAAC,EAAE;QACtDuH,WAAW,EAAE;UAAEtE,EAAE,EAAE,IAAI,CAACG,OAAO;UAAEkI,IAAI,EAAE;QAAM;OAC9C,CAAC;IACJ;IAEAE,eAAeA,CAAA;MACb,IAAI,CAACjG,oBAAoB,GAAG,CAAC,IAAI,CAACA,oBAAoB;IACxD;IAEAkG,iBAAiBA,CAACC,GAAW;MAC3B,IAAI,CAAClG,cAAc,GAAGkG,GAAG;IAC3B;IAEAhK,mBAAmBA,CAACiK,QAAgB;MAClC,IAAI,CAACnF,sBAAsB,CAACmF,QAAQ,CAAC,GAAG,CAAC,IAAI,CAACnF,sBAAsB,CAACmF,QAAQ,CAAC;IAChF;IAEArJ,uBAAuBA,CAACqJ,QAAgB;MACtC,OAAO,IAAI,CAACnF,sBAAsB,CAACmF,QAAQ,CAAC,IAAI,KAAK;IACvD;IAEQ3C,cAAcA,CAACD,OAAe;MACpC,IAAI,CAACjJ,YAAY,GAAG,CAAC,GAAG,IAAI,CAACA,YAAY,EAAE;QAAE2H,IAAI,EAAE,IAAI;QAAEC,IAAI,EAAEqB;MAAO,CAAE,CAAC;IAC3E;IAEQ6C,qBAAqBA,CAAA;MAC3B,OAAO,EAAE;IACX;IAEQC,qBAAqBA,CAAA;MAC3B,OAAO,EAAE;IACX;IAEAnB,0BAA0BA,CAACxC,QAAa,EAAEa,OAAe;MACvD,IAAI;QACF,MAAM+C,SAAS,GAAG5D,QAAQ,EAAE6D,aAAa,EAAEC,KAAK,EAAEC,MAAM;QACxD,IAAIC,eAAe,GAAG,EAAE;QACxB,IAAIJ,SAAS,EAAE;UACbI,eAAe,GAAGJ,SAAS,CAACK,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC;QACnD,CAAC,MAAM;UACLD,eAAe,GAAGhE,QAAQ,EAAE6D,aAAa,EAAEK,MAAM;QACnD;QACA,IAAI,CAACtM,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;UAAE2H,IAAI,EAAE,MAAM;UAAEC,IAAI,EAAEqB;QAAO,CAAE,EAC/B;UAAEtB,IAAI,EAAE,IAAI;UAAEC,IAAI,EAAEwE,eAAe,IAAI;QAAyB,CAAE,CACnE;MACH,CAAC,CAAC,OAAOvB,GAAQ,EAAE;QACjB,IAAI,CAAC7K,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;UAAE2H,IAAI,EAAE,IAAI;UAAEC,IAAI,EAAEiD,GAAG,EAAE5B,OAAO,IAAI;QAAwC,CAAE,CAC/E;MACH;IACF;IAEQe,+BAA+BA,CACrCf,OAAe,EACfuC,IAAY,EACZ3B,iBAAyB,EACzB3C,gBAAyB,EACzBC,aAAsB;MAEtB,MAAMoF,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/B,IAAI,CAACtG,sBAAsB,CAACuG,OAAO,CAAEC,QAAQ,IAAI;QAC/C,IAAIA,QAAQ,CAACpD,IAAI,EAAE;UACjBiD,QAAQ,CAACI,MAAM,CAAC,OAAO,EAAED,QAAQ,CAACpD,IAAI,CAAC;QACzC;MACF,CAAC,CAAC;MAEF,IAAIiD,QAAQ,CAACK,GAAG,CAAC,OAAO,CAAC,EAAE;QACzB,IAAI,CAAC/J,sBAAsB,CAACgK,gBAAgB,CAACN,QAAQ,CAAC,CAAC7B,IAAI,CACzD3M,SAAS,CAAE+O,YAAY,IAAI;UACzB,MAAMC,WAAW,GAAGD,YAAY,EAAEE,aAAa,EAAE3D,GAAG,CAAEjB,QAAa,IAAKA,QAAQ,CAAC2E,WAAW,CAAC,EAAEvD,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;UAC/G,IAAI,CAACyD,8BAA8B,CAAChE,OAAO,EAAEuC,IAAI,EAAE3B,iBAAiB,EAAE3C,gBAAgB,EAAEC,aAAa,EAAE4F,WAAW,CAAC;UACnH,OAAO7O,EAAE,CAAC,IAAI,CAAC;QACjB,CAAC,CAAC,EACFD,UAAU,CAAEqK,KAAK,IAAI;UACnBC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,IAAI,CAAC2B,qBAAqB,CAAChB,OAAO,EAAEuC,IAAI,EAAE3B,iBAAiB,EAAE3C,gBAAgB,EAAEC,aAAa,CAAC;UAC7F,OAAOjJ,EAAE,CAAC,IAAI,CAAC;QACjB,CAAC,CAAC,CACH,CAACsJ,SAAS,EAAE;MACf,CAAC,MAAM;QACL,IAAI,CAACyC,qBAAqB,CAAChB,OAAO,EAAEuC,IAAI,EAAE3B,iBAAiB,EAAE3C,gBAAgB,EAAEC,aAAa,CAAC;MAC/F;IACF;IAEQ8C,qBAAqBA,CAC3BhB,OAAe,EACfuC,IAAY,EACZ3B,iBAAyB,EACzB3C,gBAAyB,EACzBC,aAAsB;MAEtB,IAAID,gBAAgB,EAAE;QACpB,IAAI,CAACZ,gBAAgB,CAAC4G,IAAI,CAAC;UAAEC,OAAO,EAAElE,OAAO;UAAEmE,IAAI,EAAE;QAAM,CAAE,CAAC;MAChE;MAEA,MAAMlD,OAAO,GAAGhD,gBAAgB,GAAG,IAAI,CAACZ,gBAAgB,GAAG2C,OAAO;MAClE,MAAM;QAAEoE;MAAO,CAAE,GAAG,IAAI,CAACtB,qBAAqB,EAAE;MAEhD,IAAI,CAAClJ,sBAAsB,CAACyK,cAAc,CACxCpD,OAAO,EACPsB,IAAI,EACJtE,gBAAgB,EAChBC,aAAa,EACb,IAAI,CAAChB,eAAe,EACpB0D,iBAAiB,EACjB,EAAE,EACFwD,OAAO,CACR,CAAC3C,IAAI,CACJ1M,QAAQ,CAAC,MAAK;QACZ,IAAI,CAACiC,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACmG,wBAAwB,GAAG,KAAK;MACvC,CAAC,CAAC,EACFtI,SAAS,CAAC,IAAI,CAACuI,sBAAsB,CAAC,CACvC,CAACmB,SAAS,CAAC;QACVM,IAAI,EAAGyF,iBAAsB,IAAI;UAC/B,IAAIA,iBAAiB,EAAEnF,QAAQ,IAAImF,iBAAiB,EAAEnF,QAAQ,EAAEoF,OAAO,EAAE;YACvE,MAAMC,cAAc,GAAGF,iBAAiB,CAACnF,QAAQ,CAACoF,OAAO,CAAC,CAAC,CAAC,CAAC5F,IAAI;YACjE,IAAI,CAAC5H,YAAY,GAAG,CAAC,GAAG,IAAI,CAACA,YAAY,EAAE;cAAE2H,IAAI,EAAE,IAAI;cAAEC,IAAI,EAAE6F;YAAc,CAAE,CAAC;YAChF,IAAIvG,gBAAgB,EAAE;cACpB,IAAI,CAACZ,gBAAgB,CAAC4G,IAAI,CAAC;gBAAEC,OAAO,EAAEM,cAAc;gBAAEL,IAAI,EAAE;cAAW,CAAE,CAAC;YAC5E;UACF,CAAC,MAAM;YACL7E,OAAO,CAACmF,IAAI,CAAC,iCAAiC,EAAEH,iBAAiB,CAAC;YAClE,IAAI,CAACrE,cAAc,CAAC,+CAA+C,CAAC;UACtE;QACF,CAAC;QACDZ,KAAK,EAAGA,KAAU,IAAI;UACpBC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;UAClC,MAAMqF,YAAY,GAAGrF,KAAK,EAAEA,KAAK,EAAEW,OAAO,IAAI,kDAAkD;UAChG,IAAI,CAACC,cAAc,CAACyE,YAAY,CAAC;UACjC,IAAIzG,gBAAgB,IAAI,IAAI,CAACZ,gBAAgB,CAAC1F,MAAM,GAAG,CAAC,EAAE;YACxD,IAAI,CAAC0F,gBAAgB,CAACsH,GAAG,EAAE;UAC7B;QACF;OACD,CAAC;IACJ;IAEQX,8BAA8BA,CACpChE,OAAe,EACfuC,IAAY,EACZ3B,iBAAyB,EACzB3C,gBAAyB,EACzBC,aAAsB,EACtB0G,YAAoB;MAEpB,IAAI3G,gBAAgB,EAAE;QACpB,IAAI,CAACZ,gBAAgB,CAAC4G,IAAI,CAAC;UAAEC,OAAO,EAAElE,OAAO;UAAEmE,IAAI,EAAE;QAAM,CAAE,CAAC;MAChE;MAEA,MAAMlD,OAAO,GAAGhD,gBAAgB,GAAG,IAAI,CAACZ,gBAAgB,GAAG2C,OAAO;MAClE,MAAM;QAAEoE;MAAO,CAAE,GAAG,IAAI,CAACtB,qBAAqB,EAAE;MAEhD,IAAI,CAAClJ,sBAAsB,CAACyK,cAAc,CACxCpD,OAAO,EACPsB,IAAI,EACJtE,gBAAgB,EAChBC,aAAa,EACb,IAAI,CAAChB,eAAe,EACpB0D,iBAAiB,EACjBgE,YAAY,EACZR,OAAO,CACR,CAAC3C,IAAI,CACJ1M,QAAQ,CAAC,MAAK;QACZ,IAAI,CAACiC,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACmG,wBAAwB,GAAG,KAAK;MACvC,CAAC,CAAC,EACFtI,SAAS,CAAC,IAAI,CAACuI,sBAAsB,CAAC,CACvC,CAACmB,SAAS,CAAC;QACVM,IAAI,EAAGyF,iBAAsB,IAAI;UAC/B,IAAIA,iBAAiB,EAAEnF,QAAQ,IAAImF,iBAAiB,EAAEnF,QAAQ,EAAEoF,OAAO,EAAE;YACvE,MAAMC,cAAc,GAAGF,iBAAiB,CAACnF,QAAQ,CAACoF,OAAO,CAAC,CAAC,CAAC,CAAC5F,IAAI;YACjE,IAAI,CAAC5H,YAAY,GAAG,CAAC,GAAG,IAAI,CAACA,YAAY,EAAE;cAAE2H,IAAI,EAAE,IAAI;cAAEC,IAAI,EAAE6F;YAAc,CAAE,CAAC;YAChF,IAAIvG,gBAAgB,EAAE;cACpB,IAAI,CAACZ,gBAAgB,CAAC4G,IAAI,CAAC;gBAAEC,OAAO,EAAEM,cAAc;gBAAEL,IAAI,EAAE;cAAW,CAAE,CAAC;YAC5E;UACF,CAAC,MAAM;YACL7E,OAAO,CAACmF,IAAI,CAAC,iCAAiC,EAAEH,iBAAiB,CAAC;YAClE,IAAI,CAACrE,cAAc,CAAC,+CAA+C,CAAC;UACtE;QACF,CAAC;QACDZ,KAAK,EAAGA,KAAU,IAAI;UACpBC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;UAClC,MAAMqF,YAAY,GAAGrF,KAAK,EAAEA,KAAK,EAAEW,OAAO,IAAI,kDAAkD;UAChG,IAAI,CAACC,cAAc,CAACyE,YAAY,CAAC;UACjC,IAAIzG,gBAAgB,IAAI,IAAI,CAACZ,gBAAgB,CAAC1F,MAAM,GAAG,CAAC,EAAE;YACxD,IAAI,CAAC0F,gBAAgB,CAACsH,GAAG,EAAE;UAC7B;QACF;OACD,CAAC;IACJ;IAEQ5E,gCAAgCA,CAACP,SAAc;MACrD,IAAI,CAACA,SAAS,EAAE;QACdF,OAAO,CAACmF,IAAI,CAAC,sCAAsC,CAAC;QACpD;MACF;MAEA,IAAI,CAAC9H,eAAe,GAAG,EAAE;MACzB,IAAI,CAACC,WAAW,GAAG,EAAE;MACrB,IAAIiI,WAAW,GAAG,CAAC;MAEnB,IAAI,IAAI,CAAC5N,SAAS,KAAK,YAAY,EAAE;QACnC,IAAI,CAAC6N,6BAA6B,CAACtF,SAAS,EAAEqF,WAAW,CAAC;MAC5D,CAAC,MAAM,IAAI,IAAI,CAAC5N,SAAS,KAAK,eAAe,EAAE;QAC7C,IAAI,CAAC8N,gCAAgC,CAACvF,SAAS,EAAEqF,WAAW,CAAC;MAC/D;IACF;IAEQC,6BAA6BA,CAACtF,SAAc,EAAEqF,WAAmB;MACvE,IAAI,CAACnN,oBAAoB,GAAG,EAAE;MAC9B,IAAI,CAACS,mBAAmB,GAAG,EAAE;MAC7B,IAAI,CAACJ,uBAAuB,GAAG,EAAE;MACjC,IAAI,CAACQ,uBAAuB,GAAG,EAAE;MAEjC,IAAIiH,SAAS,CAACmB,WAAW,IAAInB,SAAS,CAACwF,cAAc,EAAE;QACrD,IAAI,CAACtN,oBAAoB,CAACuM,IAAI,CAAC;UAC7B/J,EAAE,EAAE,UAAU2K,WAAW,EAAE,EAAE;UAC7BvN,IAAI,EAAEkI,SAAS,CAACmB,WAAW,IAAInB,SAAS,CAACwF,cAAc,IAAI,eAAe;UAC1EC,IAAI,EAAE;SACP,CAAC;MACJ;MAEA,IAAIzF,SAAS,CAAC0F,MAAM,IAAIxF,KAAK,CAACC,OAAO,CAACH,SAAS,CAAC0F,MAAM,CAAC,EAAE;QACvD1F,SAAS,CAAC0F,MAAM,CAAC1B,OAAO,CAAE2B,QAAa,IAAI;UACzC,IAAIA,QAAQ,CAACD,MAAM,IAAIxF,KAAK,CAACC,OAAO,CAACwF,QAAQ,CAACD,MAAM,CAAC,EAAE;YACrDC,QAAQ,CAACD,MAAM,CAAC1B,OAAO,CAAE4B,UAAe,IAAI;cAC1C,IAAIA,UAAU,CAACC,SAAS,KAAK,OAAO,IAAID,UAAU,CAACE,WAAW,EAAE;gBAC9D,IAAI,CAACnN,mBAAmB,CAAC8L,IAAI,CAAC;kBAC5B/J,EAAE,EAAE,SAAS2K,WAAW,EAAE,EAAE;kBAC5BvN,IAAI,EAAE,UAAU8N,UAAU,CAACE,WAAW,EAAE;kBACxCL,IAAI,EAAE;iBACP,CAAC;cACJ;cAEA,IAAIG,UAAU,CAACC,SAAS,KAAK,wBAAwB,IAAID,UAAU,CAACE,WAAW,EAAE;gBAC/E,MAAMC,OAAO,GAAGH,UAAU,CAACE,WAAW,CAACE,QAAQ,EAAE;gBACjD,MAAMC,KAAK,GAAGF,OAAO,CAACG,KAAK,CAAC,GAAG,CAAC,CAACtF,GAAG,CAAElG,EAAU,IAAKA,EAAE,CAACyL,IAAI,EAAE,CAAC,CAACC,MAAM,CAAE1L,EAAU,IAAKA,EAAE,CAAC;gBAC1FuL,KAAK,CAACjC,OAAO,CAAEqC,IAAY,IAAI;kBAC7B,IAAI,CAAC9N,uBAAuB,CAACkM,IAAI,CAAC;oBAChC/J,EAAE,EAAE,aAAa2K,WAAW,EAAE,EAAE;oBAChCvN,IAAI,EAAE,mBAAmBuO,IAAI,EAAE;oBAC/BZ,IAAI,EAAE;mBACP,CAAC;gBACJ,CAAC,CAAC;cACJ;cAEA,IAAI,CAACG,UAAU,CAACC,SAAS,KAAK,WAAW,IAAID,UAAU,CAACC,SAAS,CAACS,UAAU,CAAC,YAAY,CAAC,KAAKV,UAAU,CAACE,WAAW,IAAIF,UAAU,CAACE,WAAW,KAAK,OAAO,EAAE;gBAC3J,IAAIS,aAAa,GAAGX,UAAU,CAACC,SAAS;gBACxC,IAAIU,aAAa,CAACD,UAAU,CAAC,YAAY,CAAC,EAAE;kBAC1CC,aAAa,GAAGA,aAAa,CAAC3C,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;gBACzD;gBACA,IAAI,CAAC7K,uBAAuB,CAAC0L,IAAI,CAAC;kBAChC/J,EAAE,EAAE,aAAa2K,WAAW,EAAE,EAAE;kBAChCvN,IAAI,EAAE,cAAcyO,aAAa,EAAE;kBACnCd,IAAI,EAAE;iBACP,CAAC;cACJ;YACF,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ;MAEA,MAAMe,aAAa,GAAG,CAAC;MACvB,MAAMC,aAAa,GAAG,IAAI,CAAClO,uBAAuB,CAACJ,MAAM,GAAG,IAAI,CAACY,uBAAuB,CAACZ,MAAM;MAC/F,MAAMuO,eAAe,GAAG,IAAI,CAACxO,oBAAoB,CAACC,MAAM,GAAG,IAAI,CAACQ,mBAAmB,CAACR,MAAM;MAC1F,IAAI,CAACyB,6BAA6B,GAAG+M,IAAI,CAACC,KAAK,CAAEF,eAAe,GAAGF,aAAa,GAAI,GAAG,CAAC;IAC1F;IAEQjB,gCAAgCA,CAACvF,SAAc,EAAEqF,WAAmB;MAC1E,IAAI,CAACnN,oBAAoB,GAAG,EAAE;MAC9B,IAAI,CAACS,mBAAmB,GAAG,EAAE;MAC7B,IAAI,CAACJ,uBAAuB,GAAG,EAAE;MACjC,IAAI,CAACyF,kBAAkB,GAAG,EAAE;MAE5B,IAAIgC,SAAS,CAAC6G,QAAQ,IAAI7G,SAAS,CAAC6G,QAAQ,CAAC1O,MAAM,GAAG,CAAC,EAAE;QACvD,MAAM2O,YAAY,GAAG9G,SAAS,CAAC6G,QAAQ,CAAC,CAAC,CAAC;QAC1C,IAAIC,YAAY,CAACC,IAAI,EAAE;UACrB,IAAI,CAAC7O,oBAAoB,CAACuM,IAAI,CAAC;YAC7B/J,EAAE,EAAE,UAAU2K,WAAW,EAAE,EAAE;YAC7BvN,IAAI,EAAEgP,YAAY,CAACC,IAAI,IAAI,oBAAoB;YAC/CtB,IAAI,EAAE;WACP,CAAC;QACJ;MACF;MAEA,IAAIzF,SAAS,CAAC7B,KAAK,IAAI6B,SAAS,CAACgH,SAAS,EAAE;QAC1C,IAAI,CAACrO,mBAAmB,CAAC8L,IAAI,CAAC;UAC5B/J,EAAE,EAAE,SAAS2K,WAAW,EAAE,EAAE;UAC5BvN,IAAI,EAAEkI,SAAS,CAAC7B,KAAK,IAAI6B,SAAS,CAACgH,SAAS,IAAI,eAAe;UAC/DvB,IAAI,EAAE;SACP,CAAC;MACJ;MAEA,IAAIzF,SAAS,CAAC6G,QAAQ,IAAI7G,SAAS,CAAC6G,QAAQ,CAAC1O,MAAM,GAAG,CAAC,EAAE;QACvD6H,SAAS,CAAC6G,QAAQ,CAAC7C,OAAO,CAAE8C,YAAiB,IAAI;UAC/C,IAAIA,YAAY,CAACG,KAAK,IAAIH,YAAY,CAACG,KAAK,CAAC9O,MAAM,GAAG,CAAC,EAAE;YACvD2O,YAAY,CAACG,KAAK,CAACjD,OAAO,CAAE1F,IAAS,IAAI;cACvC,IAAI,CAACN,kBAAkB,CAACyG,IAAI,CAAC;gBAC3B/J,EAAE,EAAE,QAAQ2K,WAAW,EAAE,EAAE;gBAC3BvN,IAAI,EAAEwG,IAAI,CAACxG,IAAI,IAAIwG,IAAI,CAAC4I,QAAQ,IAAI,MAAM;gBAC1CzB,IAAI,EAAE;eACP,CAAC;YACJ,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ;MAEA,IAAIzF,SAAS,CAACmH,aAAa,IAAInH,SAAS,CAACmH,aAAa,CAAChP,MAAM,GAAG,CAAC,EAAE;QACjE6H,SAAS,CAACmH,aAAa,CAACnD,OAAO,CAAEoD,EAAO,IAAI;UAC1C,IAAI,CAAC7O,uBAAuB,CAACkM,IAAI,CAAC;YAChC/J,EAAE,EAAE,aAAa2K,WAAW,EAAE,EAAE;YAChCvN,IAAI,EAAEsP,EAAE,CAACtP,IAAI,IAAIsP,EAAE,CAACtG,YAAY,IAAI,gBAAgB;YACpD2E,IAAI,EAAE;WACP,CAAC;QACJ,CAAC,CAAC;MACJ;MAEA,MAAMe,aAAa,GAAG,CAAC;MACvB,MAAME,eAAe,GAAG,IAAI,CAACxO,oBAAoB,CAACC,MAAM,GAAG,IAAI,CAACQ,mBAAmB,CAACR,MAAM;MAC1F,IAAI,CAACyB,6BAA6B,GAAG+M,IAAI,CAACC,KAAK,CAAEF,eAAe,GAAGF,aAAa,GAAI,GAAG,CAAC;IAC1F;;uCAhnBWxM,uBAAuB,EAAAlE,EAAA,CAAAuR,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAzR,EAAA,CAAAuR,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA1R,EAAA,CAAAuR,iBAAA,CAAAI,EAAA,CAAAC,mBAAA,GAAA5R,EAAA,CAAAuR,iBAAA,CAAAM,EAAA,CAAAC,sBAAA,GAAA9R,EAAA,CAAAuR,iBAAA,CAAAQ,EAAA,CAAAC,mBAAA,GAAAhS,EAAA,CAAAuR,iBAAA,CAAAU,EAAA,CAAAC,aAAA,GAAAlS,EAAA,CAAAuR,iBAAA,CAAAY,EAAA,CAAAC,WAAA,GAAApS,EAAA,CAAAuR,iBAAA,CAAAc,EAAA,CAAAC,oBAAA;IAAA;;YAAvBpO,uBAAuB;MAAAqO,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBAYvB7S,iCAAiC;;;;;;;;;;;;UCrCxCG,EAJN,CAAAC,cAAA,aAAuC,aAEZ,aACD,gBAC+C;UAAvCD,EAAA,CAAAI,UAAA,mBAAAwS,yDAAA;YAAA,OAASD,GAAA,CAAA5F,YAAA,EAAc;UAAA,EAAC;UAClD/M,EAAA,CAAAkD,SAAA,kBAIY;UACZlD,EAAA,CAAAC,cAAA,cAAyB;UAAAD,EAAA,CAAAE,MAAA,GAAe;UAG9CF,EAH8C,CAAAG,YAAA,EAAO,EACxC,EACL,EACF;UAOAH,EAJN,CAAAC,cAAA,aAA0B,aAEyC,aACrC,iBAC+C;UAA1CD,EAAA,CAAAI,UAAA,mBAAAyS,0DAAA;YAAA,OAASF,GAAA,CAAAxF,eAAA,EAAiB;UAAA,EAAC;UACtDnN,EAAA,CAAAkD,SAAA,oBAKW;UACblD,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAiC,UAAA,KAAA6Q,0CAAA,qBAKC;UAGH9S,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAiC,UAAA,KAAA8Q,uCAAA,mBAAyD;UAuB3D/S,EAAA,CAAAG,YAAA,EAAM;UAOAH,EAJN,CAAAC,cAAA,eAAyB,cAEG,eACI,kBAKzB;UADCD,EAAA,CAAAI,UAAA,mBAAA4S,0DAAA;YAAA,OAASL,GAAA,CAAAvF,iBAAA,CAAkB,WAAW,CAAC;UAAA,EAAC;UAExCpN,EAAA,CAAAE,MAAA,mBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAIC;UADCD,EAAA,CAAAI,UAAA,mBAAA6S,0DAAA;YAAA,OAASN,GAAA,CAAAvF,iBAAA,CAAkB,QAAQ,CAAC;UAAA,EAAC;UAErCpN,EAAA,CAAAE,MAAA,sBACF;UAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;UAENH,EAAA,CAAAC,cAAA,eAA2B;UAgfzBD,EA9eA,CAAAiC,UAAA,KAAAiR,uCAAA,oBAAsE,KAAAC,uCAAA,mBA8eN;UA8BxEnT,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACF;;;UAzlB2BH,EAAA,CAAAuB,SAAA,GAAe;UAAfvB,EAAA,CAAA8B,iBAAA,CAAA6Q,GAAA,CAAA/Q,SAAA,CAAe;UAQpB5B,EAAA,CAAAuB,SAAA,GAAwC;UAAxCvB,EAAA,CAAAgE,WAAA,cAAA2O,GAAA,CAAAzL,oBAAA,CAAwC;UAIxDlH,EAAA,CAAAuB,SAAA,GAAgE;UAAhEvB,EAAA,CAAAwB,UAAA,aAAAmR,GAAA,CAAAzL,oBAAA,gCAAgE;UAQjElH,EAAA,CAAAuB,SAAA,EAA2B;UAA3BvB,EAAA,CAAAwB,UAAA,UAAAmR,GAAA,CAAAzL,oBAAA,CAA2B;UAQJlH,EAAA,CAAAuB,SAAA,EAA2B;UAA3BvB,EAAA,CAAAwB,UAAA,UAAAmR,GAAA,CAAAzL,oBAAA,CAA2B;UAgCjDlH,EAAA,CAAAuB,SAAA,GAA+C;UAA/CvB,EAAA,CAAAgE,WAAA,WAAA2O,GAAA,CAAAxL,cAAA,iBAA+C;UAO/CnH,EAAA,CAAAuB,SAAA,GAA4C;UAA5CvB,EAAA,CAAAgE,WAAA,WAAA2O,GAAA,CAAAxL,cAAA,cAA4C;UAU1CnH,EAAA,CAAAuB,SAAA,GAAoC;UAApCvB,EAAA,CAAAwB,UAAA,SAAAmR,GAAA,CAAAxL,cAAA,iBAAoC;UA8epCnH,EAAA,CAAAuB,SAAA,EAAiC;UAAjCvB,EAAA,CAAAwB,UAAA,SAAAmR,GAAA,CAAAxL,cAAA,cAAiC;;;qBDljB3C9H,YAAY,EAAA+T,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZ1T,WAAW,EACXC,iCAAiC,EAGjCC,aAAa;MAAAyT,MAAA;IAAA;;SAKJrP,uBAAuB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}