{"ast": null, "code": "import { MarketplaceAuthGuard } from './shared/guards/marketplace-auth.guard';\nimport { LoginComponent } from '@shared/auth/components/login/login.component';\nimport { CallbackComponent } from '@shared/auth/components/callback/callback.component';\nimport { LaunchpadHomeComponent } from './pages/launchpad-home/launchpad-home.component';\nimport { AgentsFilterComponent } from './pages/agents-filter/agents-filter.component';\nimport { MyAgentHomeComponent } from './shared/components/my-agent-home/my-agent-home.component';\nimport { MarketplaceComponent } from './pages/marketplace/marketplace.component';\nimport { RootRedirectComponent } from './shared/components/root-redirect/root-redirect.component';\nexport const routes = [{\n  path: 'marketplace',\n  component: MarketplaceComponent\n}, {\n  path: 'login',\n  component: LoginComponent\n}, {\n  path: 'callback',\n  component: CallbackComponent\n}, {\n  path: 'dashboard',\n  // canActivate: [MarketplaceAuthGuard],\n  component: LaunchpadHomeComponent\n}, {\n  path: 'agent-list',\n  canActivate: [MarketplaceAuthGuard],\n  component: AgentsFilterComponent\n}, {\n  path: 'my-agent-home',\n  canActivate: [MarketplaceAuthGuard],\n  component: MyAgentHomeComponent\n}, {\n  path: '',\n  component: RootRedirectComponent,\n  pathMatch: 'full'\n}, {\n  path: '**',\n  redirectTo: '/marketplace'\n}];", "map": {"version": 3, "names": ["MarketplaceAuthGuard", "LoginComponent", "CallbackComponent", "LaunchpadHomeComponent", "AgentsFilterComponent", "MyAgentHomeComponent", "MarketplaceComponent", "RootRedirectComponent", "routes", "path", "component", "canActivate", "pathMatch", "redirectTo"], "sources": ["C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\app.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\r\nimport { MarketplaceAuthGuard } from './shared/guards/marketplace-auth.guard';\r\nimport { LoginComponent } from '@shared/auth/components/login/login.component';\r\nimport { CallbackComponent } from '@shared/auth/components/callback/callback.component';\r\nimport { LaunchpadHomeComponent } from './pages/launchpad-home/launchpad-home.component';\r\nimport { AgentsFilterComponent } from './pages/agents-filter/agents-filter.component';\r\nimport { MyAgentHomeComponent } from './shared/components/my-agent-home/my-agent-home.component';\r\nimport { MarketplaceComponent } from './pages/marketplace/marketplace.component';\r\nimport { RootRedirectComponent } from './shared/components/root-redirect/root-redirect.component';\r\n\r\nexport const routes: Routes = [\r\n  { path: 'marketplace', component: MarketplaceComponent },\r\n  { path: 'login', component: LoginComponent },\r\n  { path: 'callback', component: CallbackComponent },\r\n\r\n  {\r\n    path: 'dashboard',\r\n    // canActivate: [MarketplaceAuthGuard],\r\n    component: LaunchpadHomeComponent,\r\n  },\r\n  {\r\n    path: 'agent-list',\r\n    canActivate: [MarketplaceAuthGuard],\r\n    component: AgentsFilterComponent,\r\n  },\r\n  {\r\n    path: 'my-agent-home',\r\n    canActivate: [MarketplaceAuthGuard],\r\n    component: MyAgentHomeComponent,\r\n  },\r\n  {\r\n    path: '',\r\n    component: RootRedirectComponent,\r\n    pathMatch: 'full',\r\n  },\r\n  {\r\n    path: '**',\r\n    redirectTo: '/marketplace',\r\n  },\r\n];\r\n"], "mappings": "AACA,SAASA,oBAAoB,QAAQ,wCAAwC;AAC7E,SAASC,cAAc,QAAQ,+CAA+C;AAC9E,SAASC,iBAAiB,QAAQ,qDAAqD;AACvF,SAASC,sBAAsB,QAAQ,iDAAiD;AACxF,SAASC,qBAAqB,QAAQ,+CAA+C;AACrF,SAASC,oBAAoB,QAAQ,2DAA2D;AAChG,SAASC,oBAAoB,QAAQ,2CAA2C;AAChF,SAASC,qBAAqB,QAAQ,2DAA2D;AAEjG,OAAO,MAAMC,MAAM,GAAW,CAC5B;EAAEC,IAAI,EAAE,aAAa;EAAEC,SAAS,EAAEJ;AAAoB,CAAE,EACxD;EAAEG,IAAI,EAAE,OAAO;EAAEC,SAAS,EAAET;AAAc,CAAE,EAC5C;EAAEQ,IAAI,EAAE,UAAU;EAAEC,SAAS,EAAER;AAAiB,CAAE,EAElD;EACEO,IAAI,EAAE,WAAW;EACjB;EACAC,SAAS,EAAEP;CACZ,EACD;EACEM,IAAI,EAAE,YAAY;EAClBE,WAAW,EAAE,CAACX,oBAAoB,CAAC;EACnCU,SAAS,EAAEN;CACZ,EACD;EACEK,IAAI,EAAE,eAAe;EACrBE,WAAW,EAAE,CAACX,oBAAoB,CAAC;EACnCU,SAAS,EAAEL;CACZ,EACD;EACEI,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH,qBAAqB;EAChCK,SAAS,EAAE;CACZ,EACD;EACEH,IAAI,EAAE,IAAI;EACVI,UAAU,EAAE;CACb,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}