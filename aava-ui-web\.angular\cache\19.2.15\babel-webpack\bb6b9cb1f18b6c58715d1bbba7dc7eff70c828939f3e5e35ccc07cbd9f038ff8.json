{"ast": null, "code": "import { asin, atan2, cos, sin, sqrt } from \"./math.js\";\nexport function spherical(cartesian) {\n  return [atan2(cartesian[1], cartesian[0]), asin(cartesian[2])];\n}\nexport function cartesian(spherical) {\n  var lambda = spherical[0],\n    phi = spherical[1],\n    cosPhi = cos(phi);\n  return [cosPhi * cos(lambda), cosPhi * sin(lambda), sin(phi)];\n}\nexport function cartesianDot(a, b) {\n  return a[0] * b[0] + a[1] * b[1] + a[2] * b[2];\n}\nexport function cartesianCross(a, b) {\n  return [a[1] * b[2] - a[2] * b[1], a[2] * b[0] - a[0] * b[2], a[0] * b[1] - a[1] * b[0]];\n}\n\n// TODO return a\nexport function cartesianAddInPlace(a, b) {\n  a[0] += b[0], a[1] += b[1], a[2] += b[2];\n}\nexport function cartesianScale(vector, k) {\n  return [vector[0] * k, vector[1] * k, vector[2] * k];\n}\n\n// TODO return d\nexport function cartesianNormalizeInPlace(d) {\n  var l = sqrt(d[0] * d[0] + d[1] * d[1] + d[2] * d[2]);\n  d[0] /= l, d[1] /= l, d[2] /= l;\n}", "map": {"version": 3, "names": ["asin", "atan2", "cos", "sin", "sqrt", "spherical", "cartesian", "lambda", "phi", "cosPhi", "cartesianDot", "a", "b", "cartesianCross", "cartesianAddInPlace", "cartesianScale", "vector", "k", "cartesianNormalizeInPlace", "d", "l"], "sources": ["C:/console/aava-ui-web/node_modules/d3-geo/src/cartesian.js"], "sourcesContent": ["import {asin, atan2, cos, sin, sqrt} from \"./math.js\";\n\nexport function spherical(cartesian) {\n  return [atan2(cartesian[1], cartesian[0]), asin(cartesian[2])];\n}\n\nexport function cartesian(spherical) {\n  var lambda = spherical[0], phi = spherical[1], cosPhi = cos(phi);\n  return [cosPhi * cos(lambda), cosPhi * sin(lambda), sin(phi)];\n}\n\nexport function cartesianDot(a, b) {\n  return a[0] * b[0] + a[1] * b[1] + a[2] * b[2];\n}\n\nexport function cartesianCross(a, b) {\n  return [a[1] * b[2] - a[2] * b[1], a[2] * b[0] - a[0] * b[2], a[0] * b[1] - a[1] * b[0]];\n}\n\n// TODO return a\nexport function cartesianAddInPlace(a, b) {\n  a[0] += b[0], a[1] += b[1], a[2] += b[2];\n}\n\nexport function cartesianScale(vector, k) {\n  return [vector[0] * k, vector[1] * k, vector[2] * k];\n}\n\n// TODO return d\nexport function cartesianNormalizeInPlace(d) {\n  var l = sqrt(d[0] * d[0] + d[1] * d[1] + d[2] * d[2]);\n  d[0] /= l, d[1] /= l, d[2] /= l;\n}\n"], "mappings": "AAAA,SAAQA,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,QAAO,WAAW;AAErD,OAAO,SAASC,SAASA,CAACC,SAAS,EAAE;EACnC,OAAO,CAACL,KAAK,CAACK,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,CAAC,EAAEN,IAAI,CAACM,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;AAChE;AAEA,OAAO,SAASA,SAASA,CAACD,SAAS,EAAE;EACnC,IAAIE,MAAM,GAAGF,SAAS,CAAC,CAAC,CAAC;IAAEG,GAAG,GAAGH,SAAS,CAAC,CAAC,CAAC;IAAEI,MAAM,GAAGP,GAAG,CAACM,GAAG,CAAC;EAChE,OAAO,CAACC,MAAM,GAAGP,GAAG,CAACK,MAAM,CAAC,EAAEE,MAAM,GAAGN,GAAG,CAACI,MAAM,CAAC,EAAEJ,GAAG,CAACK,GAAG,CAAC,CAAC;AAC/D;AAEA,OAAO,SAASE,YAAYA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACjC,OAAOD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC;AAChD;AAEA,OAAO,SAASC,cAAcA,CAACF,CAAC,EAAEC,CAAC,EAAE;EACnC,OAAO,CAACD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,EAAED,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,EAAED,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1F;;AAEA;AACA,OAAO,SAASE,mBAAmBA,CAACH,CAAC,EAAEC,CAAC,EAAE;EACxCD,CAAC,CAAC,CAAC,CAAC,IAAIC,CAAC,CAAC,CAAC,CAAC,EAAED,CAAC,CAAC,CAAC,CAAC,IAAIC,CAAC,CAAC,CAAC,CAAC,EAAED,CAAC,CAAC,CAAC,CAAC,IAAIC,CAAC,CAAC,CAAC,CAAC;AAC1C;AAEA,OAAO,SAASG,cAAcA,CAACC,MAAM,EAAEC,CAAC,EAAE;EACxC,OAAO,CAACD,MAAM,CAAC,CAAC,CAAC,GAAGC,CAAC,EAAED,MAAM,CAAC,CAAC,CAAC,GAAGC,CAAC,EAAED,MAAM,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC;AACtD;;AAEA;AACA,OAAO,SAASC,yBAAyBA,CAACC,CAAC,EAAE;EAC3C,IAAIC,CAAC,GAAGhB,IAAI,CAACe,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,CAAC;EACrDA,CAAC,CAAC,CAAC,CAAC,IAAIC,CAAC,EAAED,CAAC,CAAC,CAAC,CAAC,IAAIC,CAAC,EAAED,CAAC,CAAC,CAAC,CAAC,IAAIC,CAAC;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}