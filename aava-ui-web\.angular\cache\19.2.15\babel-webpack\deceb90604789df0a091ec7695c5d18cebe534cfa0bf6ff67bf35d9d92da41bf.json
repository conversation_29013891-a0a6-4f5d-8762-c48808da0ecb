{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ClientCardComponent } from '../client-card/client-card.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction ClientsSectionComponent_app_client_card_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-client-card\", 7);\n  }\n  if (rf & 2) {\n    const client_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"client\", client_r1)(\"variant\", \"default\");\n  }\n}\nexport let ClientsSectionComponent = /*#__PURE__*/(() => {\n  class ClientsSectionComponent {\n    clients = [{\n      id: 1,\n      companyName: 'CVS Health',\n      companyLogo: 'assets/icons/cvs.png',\n      testimonial: 'Uses Growthly as the source of truth for all its product data, and to determine where the team should focus its time.',\n      authorName: 'Cyhntya Rebecca',\n      authorRole: 'Dev at CVS Health',\n      authorAvatar: 'assets/icons/rebbeca.png'\n    }, {\n      id: 2,\n      companyName: 'AXOS',\n      companyLogo: 'assets/icons/axos.png',\n      testimonial: 'Uses Growthly as the source of truth for all its product data, and to determine where the team should focus its time.',\n      authorName: 'Cyhntya Rebecca',\n      authorRole: 'Designer at AXOS',\n      authorAvatar: 'assets/icons/rebbeca.png'\n    }, {\n      id: 3,\n      companyName: 'LoanDepot',\n      companyLogo: 'assets/icons/loandepot.png',\n      testimonial: 'Uses Growthly as the source of truth for all its product data, and to determine where the team should focus its time.',\n      authorName: 'Cyhntya Rebecca',\n      authorRole: 'Dev at HP',\n      authorAvatar: 'assets/icons/rebbeca.png'\n    }, {\n      id: 4,\n      companyName: 'HP',\n      companyLogo: 'assets/icons/hp.png',\n      testimonial: 'Uses Growthly as the source of truth for all its product data, and to determine where the team should focus its time.',\n      authorName: 'Cyhntya Rebecca',\n      authorRole: 'Dev at HP',\n      authorAvatar: 'assets/icons/rebbeca.png'\n    }, {\n      id: 5,\n      companyName: 'HP',\n      companyLogo: 'assets/icons/hp.png',\n      testimonial: 'Uses Growthly as the source of truth for all its product data, and to determine where the team should focus its time.',\n      authorName: 'Cyhntya Rebecca',\n      authorRole: 'Dev at HP',\n      authorAvatar: 'assets/icons/rebbeca.png'\n    }];\n    trackByClient(index, client) {\n      return client.id || index;\n    }\n    static ɵfac = function ClientsSectionComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ClientsSectionComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ClientsSectionComponent,\n      selectors: [[\"app-clients-section\"]],\n      decls: 9,\n      vars: 2,\n      consts: [[1, \"clients-section\"], [1, \"container\"], [1, \"section-header\"], [1, \"section-title\"], [1, \"highlight\"], [1, \"clients-grid\"], [3, \"client\", \"variant\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [3, \"client\", \"variant\"]],\n      template: function ClientsSectionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h2\", 3);\n          i0.ɵɵtext(4, \"Our \");\n          i0.ɵɵelementStart(5, \"span\", 4);\n          i0.ɵɵtext(6, \"Clients\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(7, \"div\", 5);\n          i0.ɵɵtemplate(8, ClientsSectionComponent_app_client_card_8_Template, 1, 2, \"app-client-card\", 6);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngForOf\", ctx.clients)(\"ngForTrackBy\", ctx.trackByClient);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, ClientCardComponent],\n      styles: [\".clients-section[_ngcontent-%COMP%] {\\n  padding: 40px 0;\\n  background: #ffffff;\\n  margin-bottom: 40px;\\n}\\n.clients-section[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 0 32px;\\n}\\n.clients-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 24px;\\n}\\n.clients-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  color: #3B3F46;\\n  text-align: center;\\n  font-family: Mulish;\\n  font-size: 48px;\\n  font-style: normal;\\n  font-weight: 700;\\n  line-height: normal;\\n  letter-spacing: -0.912px;\\n  margin: 0 0 16px 0;\\n}\\n.clients-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .highlight[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #6566CD 36.04%, #F96CAB 110.55%);\\n  background-clip: text;\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  font-family: Mulish;\\n  font-size: 48px;\\n  font-style: normal;\\n  font-weight: 700;\\n  line-height: normal;\\n  letter-spacing: -0.912px;\\n}\\n@media (max-width: 768px) {\\n  .clients-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n    font-size: 36px;\\n    letter-spacing: -0.684px;\\n  }\\n  .clients-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .highlight[_ngcontent-%COMP%] {\\n    font-size: 36px;\\n    letter-spacing: -0.684px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .clients-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n    font-size: 28px;\\n    letter-spacing: -0.532px;\\n  }\\n  .clients-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .highlight[_ngcontent-%COMP%] {\\n    font-size: 28px;\\n    letter-spacing: -0.532px;\\n  }\\n}\\n.clients-section[_ngcontent-%COMP%]   .clients-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(5, 1fr);\\n  gap: 40px;\\n  align-items: stretch;\\n  width: 100%;\\n  margin: 0 auto;\\n  justify-items: center;\\n}\\n@media (max-width: 1200px) {\\n  .clients-section[_ngcontent-%COMP%]   .clients-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(3, 1fr);\\n    gap: 32px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .clients-section[_ngcontent-%COMP%]   .clients-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n    gap: 24px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .clients-section[_ngcontent-%COMP%]   .clients-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 16px;\\n  }\\n}\\n\\n.clients-section[_ngcontent-%COMP%]     app-client-card .client-card {\\n  background: #ffffff;\\n  border: 1px solid #e2e8f0;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);\\n  border-radius: 12px;\\n  padding: 24px;\\n  height: 100%;\\n  margin: 0;\\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.clients-section[_ngcontent-%COMP%]     app-client-card .client-card:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);\\n}\\n.clients-section[_ngcontent-%COMP%]     app-client-card .client-logo {\\n  margin-bottom: 16px;\\n}\\n.clients-section[_ngcontent-%COMP%]     app-client-card .client-logo .logo-image {\\n  height: 32px;\\n  max-width: 120px;\\n}\\n.clients-section[_ngcontent-%COMP%]     app-client-card .client-testimonial {\\n  margin-bottom: 16px;\\n  flex: 1;\\n}\\n.clients-section[_ngcontent-%COMP%]     app-client-card .client-testimonial .testimonial-text {\\n  color: var(--Greyscale-Grey, #5D6180);\\n  font-family: Mulish;\\n  font-size: 16px;\\n  font-style: italic;\\n  font-weight: 400;\\n  line-height: 150%; \\n\\n  margin: 0;\\n}\\n.clients-section[_ngcontent-%COMP%]     app-client-card .client-author {\\n  gap: 12px;\\n}\\n.clients-section[_ngcontent-%COMP%]     app-client-card .client-author .author-avatar .avatar-image {\\n  width: 40px;\\n  height: 40px;\\n  border: 2px solid #e2e8f0;\\n}\\n.clients-section[_ngcontent-%COMP%]     app-client-card .client-author .author-info .author-name {\\n  color: var(--Greyscale-Dark-Grey, #060C3C);\\n  font-variant-numeric: ordinal;\\n  font-family: Mulish;\\n  font-size: 18px;\\n  font-style: normal;\\n  font-weight: 700;\\n  line-height: 24px; \\n\\n  margin-bottom: 2px;\\n}\\n.clients-section[_ngcontent-%COMP%]     app-client-card .client-author .author-info .author-role {\\n  color: var(--Purple-Purple, #6241D4);\\n  font-family: Mulish;\\n  font-size: 16px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: 26px; \\n\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return ClientsSectionComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "ClientCardComponent", "i0", "ɵɵelement", "ɵɵproperty", "client_r1", "ClientsSectionComponent", "clients", "id", "companyName", "companyLogo", "testimonial", "<PERSON><PERSON><PERSON>", "author<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "trackByClient", "index", "client", "selectors", "decls", "vars", "consts", "template", "ClientsSectionComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "ClientsSectionComponent_app_client_card_8_Template", "ɵɵadvance", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\shared\\components\\clients-section\\clients-section.component.ts", "C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\shared\\components\\clients-section\\clients-section.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ClientCardComponent, Client } from '../client-card/client-card.component';\r\n\r\n@Component({\r\n  selector: 'app-clients-section',\r\n  standalone: true,\r\n  imports: [CommonModule, ClientCardComponent],\r\n  templateUrl: './clients-section.component.html',\r\n  styleUrl: './clients-section.component.scss',\r\n})\r\nexport class ClientsSectionComponent {\r\n  clients: Client[] = [\r\n    {\r\n      id: 1,\r\n      companyName: 'CVS Health',\r\n      companyLogo: 'assets/icons/cvs.png',\r\n      testimonial: 'Uses Growthly as the source of truth for all its product data, and to determine where the team should focus its time.',\r\n      authorName: '<PERSON>hn<PERSON><PERSON>',\r\n      authorRole: 'Dev at CVS Health',\r\n      authorAvatar: 'assets/icons/rebbeca.png'\r\n    },\r\n    {\r\n      id: 2,\r\n      companyName: 'AXOS',\r\n      companyLogo: 'assets/icons/axos.png',\r\n      testimonial: 'Uses Growthly as the source of truth for all its product data, and to determine where the team should focus its time.',\r\n      authorName: '<PERSON><PERSON><PERSON><PERSON>',\r\n      authorRole: 'Designer at AXOS',\r\n      authorAvatar: 'assets/icons/rebbeca.png'\r\n    },\r\n    {\r\n      id: 3,\r\n      companyName: 'LoanDepot',\r\n      companyLogo: 'assets/icons/loandepot.png',\r\n      testimonial: 'Uses Growthly as the source of truth for all its product data, and to determine where the team should focus its time.',\r\n      authorName: 'Cyhntya Rebecca',\r\n      authorRole: 'Dev at HP',\r\n      authorAvatar: 'assets/icons/rebbeca.png'\r\n    },\r\n    {\r\n      id: 4,\r\n      companyName: 'HP',\r\n      companyLogo: 'assets/icons/hp.png',\r\n      testimonial: 'Uses Growthly as the source of truth for all its product data, and to determine where the team should focus its time.',\r\n      authorName: 'Cyhntya Rebecca',\r\n      authorRole: 'Dev at HP',\r\n      authorAvatar: 'assets/icons/rebbeca.png'\r\n    },\r\n    {\r\n      id: 5,\r\n      companyName: 'HP',\r\n      companyLogo: 'assets/icons/hp.png',\r\n      testimonial: 'Uses Growthly as the source of truth for all its product data, and to determine where the team should focus its time.',\r\n      authorName: 'Cyhntya Rebecca',\r\n      authorRole: 'Dev at HP',\r\n      authorAvatar: 'assets/icons/rebbeca.png'\r\n    }\r\n  ];\r\n\r\n  trackByClient(index: number, client: Client): any {\r\n    return client.id || index;\r\n  }\r\n} ", "<section class=\"clients-section\">\r\n  <div class=\"container\">\r\n    <div class=\"section-header\">\r\n      <h2 class=\"section-title\">Our <span class=\"highlight\">Clients</span></h2>\r\n    </div>\r\n    \r\n    <div class=\"clients-grid\">\r\n      <app-client-card\r\n        *ngFor=\"let client of clients; trackBy: trackByClient\"\r\n        [client]=\"client\"\r\n        [variant]=\"'default'\"\r\n      ></app-client-card>\r\n    </div>\r\n  </div>\r\n</section> "], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAgB,sCAAsC;;;;;ICK5EC,EAAA,CAAAC,SAAA,yBAImB;;;;IADjBD,EADA,CAAAE,UAAA,WAAAC,SAAA,CAAiB,sBACI;;;ADC7B,WAAaC,uBAAuB;EAA9B,MAAOA,uBAAuB;IAClCC,OAAO,GAAa,CAClB;MACEC,EAAE,EAAE,CAAC;MACLC,WAAW,EAAE,YAAY;MACzBC,WAAW,EAAE,sBAAsB;MACnCC,WAAW,EAAE,uHAAuH;MACpIC,UAAU,EAAE,iBAAiB;MAC7BC,UAAU,EAAE,mBAAmB;MAC/BC,YAAY,EAAE;KACf,EACD;MACEN,EAAE,EAAE,CAAC;MACLC,WAAW,EAAE,MAAM;MACnBC,WAAW,EAAE,uBAAuB;MACpCC,WAAW,EAAE,uHAAuH;MACpIC,UAAU,EAAE,iBAAiB;MAC7BC,UAAU,EAAE,kBAAkB;MAC9BC,YAAY,EAAE;KACf,EACD;MACEN,EAAE,EAAE,CAAC;MACLC,WAAW,EAAE,WAAW;MACxBC,WAAW,EAAE,4BAA4B;MACzCC,WAAW,EAAE,uHAAuH;MACpIC,UAAU,EAAE,iBAAiB;MAC7BC,UAAU,EAAE,WAAW;MACvBC,YAAY,EAAE;KACf,EACD;MACEN,EAAE,EAAE,CAAC;MACLC,WAAW,EAAE,IAAI;MACjBC,WAAW,EAAE,qBAAqB;MAClCC,WAAW,EAAE,uHAAuH;MACpIC,UAAU,EAAE,iBAAiB;MAC7BC,UAAU,EAAE,WAAW;MACvBC,YAAY,EAAE;KACf,EACD;MACEN,EAAE,EAAE,CAAC;MACLC,WAAW,EAAE,IAAI;MACjBC,WAAW,EAAE,qBAAqB;MAClCC,WAAW,EAAE,uHAAuH;MACpIC,UAAU,EAAE,iBAAiB;MAC7BC,UAAU,EAAE,WAAW;MACvBC,YAAY,EAAE;KACf,CACF;IAEDC,aAAaA,CAACC,KAAa,EAAEC,MAAc;MACzC,OAAOA,MAAM,CAACT,EAAE,IAAIQ,KAAK;IAC3B;;uCAnDWV,uBAAuB;IAAA;;YAAvBA,uBAAuB;MAAAY,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCR9BtB,EAHN,CAAAwB,cAAA,iBAAiC,aACR,aACO,YACA;UAAAxB,EAAA,CAAAyB,MAAA,WAAI;UAAAzB,EAAA,CAAAwB,cAAA,cAAwB;UAAAxB,EAAA,CAAAyB,MAAA,cAAO;UAC/DzB,EAD+D,CAAA0B,YAAA,EAAO,EAAK,EACrE;UAEN1B,EAAA,CAAAwB,cAAA,aAA0B;UACxBxB,EAAA,CAAA2B,UAAA,IAAAC,kDAAA,6BAIC;UAGP5B,EAFI,CAAA0B,YAAA,EAAM,EACF,EACE;;;UANiB1B,EAAA,CAAA6B,SAAA,GAAY;UAAA7B,EAAZ,CAAAE,UAAA,YAAAqB,GAAA,CAAAlB,OAAA,CAAY,iBAAAkB,GAAA,CAAAV,aAAA,CAAsB;;;qBDDjDf,YAAY,EAAAgC,EAAA,CAAAC,OAAA,EAAEhC,mBAAmB;MAAAiC,MAAA;IAAA;;SAIhC5B,uBAAuB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}