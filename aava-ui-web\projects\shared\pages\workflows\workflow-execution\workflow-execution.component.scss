$header-bg: #e9effd;
$main-color: #1a46a7;

::ng-deep .ava-tabs {
  background: none !important;
}

::ng-deep .ava-tabs__container {
  border-radius: none !important;
  border: none !important;
  padding: 0 !important;
  box-shadow: none !important;
  background: none !important;
}

::ng-deep .ava-tabs__list {
  padding: 0 !important;
}

.workflow-execution-container {
  display: flex;
  flex-direction: column;
  height: 93%;
  .execution-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color, #e0e0e0);

    .execution-title {
      h1 {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
        color: var(--text-color, #333);
      }

      .header-buttons {
        display: flex;
        gap: 12px;
      }
    }

    .execution-actions {
      display: flex;
      gap: 12px;

      .back-button,
      .edit-button {
        display: flex;
        align-items: center;
        padding: 8px 16px;
        border-radius: 5px;
        font-weight: 500;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s ease;

        svg {
          margin-right: 6px;
        }
      }

      .back-button {
        background-color: var(--bg-muted, #f5f5f5);
        border: 1px solid var(--border-color, #e0e0e0);
        color: var(--text-color, #333);

        &:hover {
          background-color: var(--bg-muted-hover, #e9e9e9);
        }
      }

      .edit-button {
        background-color: var(--card-bg, #fff);
        border: 1px solid var(--border-color, #e0e0e0);
        color: var(--text-color, #333);

        &:hover {
          background-color: var(--card-bg-hover, #f9f9f9);
          border-color: var(--border-color-dark, #d0d0d0);
        }
      }
    }
  }

  // Main content with 3 columns
  .execution-content {
    display: flex;
    flex-grow: 1;
    height: 95vh;
    overflow: hidden;
    gap: 20px;

    // Common column styling
    .column {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .column-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 16px;
        background-color: $header-bg;
        color: #fff;
        height: 64px;

        h2 {
          margin: 0;
          font-size: 20px;
          font-weight: 600;
          // color: var(--text-color, #333);
          color: $main-color;
        }

        .icon-button {
          display: flex;
          align-items: center;
          gap: 6px;
          padding: 8px 16px;
          background-color: var(--card-bg, white);
          position: relative;
          border: 1px solid transparent;
          border-radius: 8px;
          font-size: 14px;
          font-weight: 500;
          color: transparent;
          background-image: linear-gradient(90deg, #6566cd 0%, #f96cab 100%);
          -webkit-background-clip: text;
          background-clip: text;
          cursor: pointer;
          transition: all 0.2s ease;

          &::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 8px;
            padding: 1px;
            background: linear-gradient(90deg, #6566cd 0%, #f96cab 100%);
            -webkit-mask:
              linear-gradient(#fff 0 0) content-box,
              linear-gradient(#fff 0 0);
            -webkit-mask-composite: xor;
            mask-composite: exclude;
            pointer-events: none;
          }

          &:hover {
            background-color: var(--bg-muted-hover, #f9f5ff);
          }

          svg {
            width: 18px;
            height: 18px;
            fill: none;

            path {
              stroke-width: 2;
              stroke: url(#gradient);
            }
          }
        }
      }

      .column-content {
        flex: 1;
        overflow-y: auto;
        padding: 0;
        display: flex;
        flex-direction: column;
        // height: 100%;
        scrollbar-width: none;

        &::-webkit-scrollbar {
          width: 8px;
          height: 8px;
        }

        &::-webkit-scrollbar-track {
          background: var(--scrollbar-track, #f1f1f1);
          border-radius: 4px;
        }

        &::-webkit-scrollbar-thumb {
          background: var(--scrollbar-thumb, #d1d1d1);
          border-radius: 4px;
        }

        &::-webkit-scrollbar-thumb:hover {
          background: var(--scrollbar-thumb-hover, #b1b1b1);
        }
        .nav-wrapper {
          display: flex;
          justify-content: space-between;
          align-items: center;
          gap: 16px;
          background-color: $header-bg;
          padding: 0 15px 0 0;
          .right-inner-wrapper {
            display: flex;
            align-items: center;
            gap: 8px;

            p {
              color: #fff;
              padding: 0 10px;
              margin: 0px;
            }
          }
        }
      }
    }

    // Specific column styles for child components
    .activity-column {
      background: transparent;
      box-shadow: none;
      border-radius: 8px;

      app-agent-activity {
        height: 100%;
        display: flex;
        flex-direction: column;
      }
    }

    .execution-panel-column {
      flex: 0 0 400px;
      background: #F0F4FF;
      border-radius: 8px;
      box-shadow: 0 4px 15px var(--card-shadow, rgba(0, 0, 0, 0.05));
      transition: all 0.3s ease;

      &.collapsed {
        flex: 0 0 60px;

        .panel-content {
          display: none;
        }
      }

      .column-header {
        background: #F0F4FF;
        border-bottom: 1px solid #E5E7EB;
        padding: 16px;

        .header-left {
          display: flex;
          gap: 12px;
          align-items: center;

          .back-icon,
          .panel-icon {
            cursor: pointer;
            transition: transform 0.2s ease;

            &:hover {
              transform: scale(1.1);
            }
          }
        }
      }

      .execution-panel-content {
        padding: 0;
        height: 100%;

        .panel-content {
          padding: 20px;
          height: calc(100% - 80px);
          overflow-y: auto;

          &.hidden {
            display: none;
          }
        }
      }
    }

    .playground-column {
      flex: 3.5;
      background: var(--card-bg);
      border-radius: 8px;
      box-shadow: 0 4px 15px var(--card-shadow, rgba(0, 0, 0, 0.05));

      .column-header {
        .header-buttons {
          display: flex;
          flex-direction: row;
          gap: 12px;
          align-items: center;
          color: $main-color;
        }
      }

      .playground-content {
        padding: 0;
        height: 100%;

        .chat-container {
          padding: 20px;
          flex: 1;
          display: flex;
          flex-direction: column;
          height: 100%;
          min-height: 400px;
          border-radius: 12px;
          overflow: hidden;
          position: relative;

          ::ng-deep .prompt-container {
            margin-top: 30px;
          }

          .playground-title {
            font-size: 14px;
            font-weight: 500;
            color: var(--dashboard-primary, #6566cd);
            margin: 16px 16px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          app-chat-interface {
            height: 100%;
            display: flex;
            flex-direction: column;
            flex: 1;
          }
        }
      }
    }

    .output-column {
      flex: 5.5;
      background-color: var(--card-bg, white);
      border-radius: 8px;
      box-shadow: 0 4px 15px var(--card-shadow, rgba(0, 0, 0, 0.05));

      app-agent-output {
        height: 100%;
        display: flex;
        flex-direction: column;
      }
    }
  }
}
::ng-deep nav.ava-tabs__list {
  background: $header-bg;
  padding: 4px;
}

::ng-deep button.ava-button.primary.active {
  background: #616161;
  color: #fff;
}

::ng-deep .column-header .ava-tabs[data-variant="button"] .ava-tabs__tab--pill {
  border-radius: 8px !important;
  padding: 12px 16px !important;
  font-family: "Mulish";
}

::ng-deep
  .ava-tabs[data-variant="button"]
  .ava-tabs__tab--active
  .ava-tabs__tab-text {
  color: white;
}

::ng-deep .ava-tabs__tab-text {
  color: #4c515b;
  font-family: "Mulish";
  font-weight: 600;
}

::ng-deep .right-section-header .ava-button.secondary {
  color: #1a46a7;
  border: none;
}

::ng-deep .right-section-header .ava-button.secondary:hover {
  color: #1a46a7;
  border: none;
}

.right-section-header {
  text-align: end;
}

// Execution Panel Styles
.agents-section {
  margin-bottom: 24px;

  .agent-card {
    background: #E8F0FE;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 8px;
    transition: all 0.3s ease;

    &.completed {
      background: #F0F9FF;
      border-left: 4px solid #22C55E;
    }

    .agent-header {
      display: flex;
      align-items: center;
      gap: 12px;

      .status-icon {
        width: 32px;
        height: 32px;
        background: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .agent-info {
        flex: 1;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .agent-name {
          font-size: 16px;
          font-weight: 600;
          color: #1F2937;
          margin: 0;
        }

        .expand-icon {
          cursor: pointer;
          transition: transform 0.2s ease;

          &.expanded {
            transform: rotate(180deg);
          }
        }
      }
    }

    .agent-details {
      margin-top: 12px;
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.3s ease;

      &.expanded {
        max-height: 200px;
      }

      .agent-detail-item {
        display: flex;
        justify-content: space-between;
        padding: 4px 0;
        font-size: 14px;

        .detail-label {
          color: #6B7280;
          font-weight: 500;
        }

        .detail-value {
          color: #1F2937;
          text-align: right;
          max-width: 200px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }

  .connection-line {
    width: 2px;
    height: 16px;
    background: #1A46A7;
    margin: 0 auto 8px auto;
  }
}

.input-section {
  margin-bottom: 24px;

  .input-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;

    .status-icon {
      width: 32px;
      height: 32px;
      background: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .input-title {
      font-size: 16px;
      font-weight: 600;
      color: #1F2937;
      margin: 0;
    }
  }

  .input-container {
    .input-field {
      position: relative;
      margin-bottom: 16px;

      .input-label {
        display: block;
        font-size: 12px;
        font-weight: 500;
        color: #6B7280;
        margin-bottom: 8px;
      }

      .input-textarea {
        width: 100%;
        min-height: 80px;
        padding: 12px 50px 12px 16px;
        border: 2px solid #E5E7EB;
        border-radius: 12px;
        font-size: 14px;
        resize: vertical;
        background: white;

        &:focus {
          outline: none;
          border-color: #1A46A7;
        }

        &::placeholder {
          color: #9CA3AF;
        }
      }

      .input-actions {
        position: absolute;
        right: 12px;
        bottom: 12px;
        display: flex;
        gap: 8px;

        .attach-icon,
        .edit-icon,
        .send-icon {
          cursor: pointer;
          transition: transform 0.2s ease;

          &:hover {
            transform: scale(1.1);
          }
        }
      }
    }
  }
}

.info-message {
  text-align: center;
  margin-bottom: 24px;

  p {
    color: #1A46A7;
    font-size: 14px;
    margin: 0;
  }
}

.execute-section {
  margin-top: auto;
  padding-top: 24px;
}
