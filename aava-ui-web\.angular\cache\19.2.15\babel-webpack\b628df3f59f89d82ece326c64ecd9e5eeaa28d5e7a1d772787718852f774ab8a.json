{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport let DropdownItemComponent = /*#__PURE__*/(() => {\n  class DropdownItemComponent {\n    router;\n    icon = '';\n    label = '';\n    description = '';\n    route = '';\n    itemClick = new EventEmitter();\n    constructor(router) {\n      this.router = router;\n    }\n    // Check if this item is active based on the current route\n    get isActive() {\n      return this.router.url === this.route;\n    }\n    onClick(event) {\n      // Stop the event from bubbling up to parent elements\n      event.stopPropagation();\n      event.preventDefault();\n      console.log(`Dropdown item clicked: ${this.label}`);\n      this.itemClick.emit({\n        route: this.route,\n        label: this.label\n      });\n    }\n    static ɵfac = function DropdownItemComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DropdownItemComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DropdownItemComponent,\n      selectors: [[\"app-dropdown-item\"]],\n      inputs: {\n        icon: \"icon\",\n        label: \"label\",\n        description: \"description\",\n        route: \"route\"\n      },\n      outputs: {\n        itemClick: \"itemClick\"\n      },\n      decls: 8,\n      vars: 13,\n      consts: [[1, \"dropdown-item\", 3, \"click\"], [1, \"dropdown-item-icon\"], [\"alt\", \"\", 1, \"dropdown-icon\", 3, \"src\"], [1, \"dropdown-item-content\"], [1, \"dropdown-item-title\"], [1, \"dropdown-item-desc\"]],\n      template: function DropdownItemComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"button\", 0);\n          i0.ɵɵlistener(\"click\", function DropdownItemComponent_Template_button_click_0_listener($event) {\n            return ctx.onClick($event);\n          });\n          i0.ɵɵelementStart(1, \"div\", 1);\n          i0.ɵɵelement(2, \"img\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4);\n          i0.ɵɵtext(5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 5);\n          i0.ɵɵtext(7);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"active\", ctx.isActive);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"active\", ctx.isActive);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"active\", ctx.isActive);\n          i0.ɵɵproperty(\"src\", ctx.icon, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"active\", ctx.isActive);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate(ctx.label);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"active\", ctx.isActive);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.description, \" \");\n        }\n      },\n      dependencies: [CommonModule],\n      styles: [\".dropdown-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  padding: 12px;\\n  border-radius: 6px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  position: relative;\\n  z-index: 10;\\n  background: var(--nav-pills-bg, rgba(255, 255, 255, 0.9));\\n  border: none;\\n  width: 100%;\\n  text-align: left;\\n  outline: none;\\n  margin: 2px 0;\\n  appearance: none;\\n  color: var(--nav-text, #333333);\\n}\\n.dropdown-item[_ngcontent-%COMP%]:hover {\\n  background-color: #e7eaef;\\n}\\n.dropdown-item[_ngcontent-%COMP%]:first-child {\\n  margin-top: 4px;\\n}\\n.dropdown-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 4px;\\n}\\n\\n.dropdown-item-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: var(--nav-text, #333333);\\n  flex-shrink: 0;\\n  width: 24px;\\n  height: 24px;\\n  pointer-events: none;\\n}\\n.dropdown-item-icon.active[_ngcontent-%COMP%] {\\n  color: var(--nav-pill-selected-color, #ffffff);\\n}\\n\\n.dropdown-icon[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  object-fit: contain;\\n  pointer-events: none;\\n  transition: filter 0.2s ease;\\n}\\n.dropdown-icon.active[_ngcontent-%COMP%] {\\n  filter: var(--nav-pill-selected-icon-filter, brightness(0) invert(1));\\n}\\n\\n.dropdown-item-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n  width: 100%;\\n  pointer-events: none;\\n}\\n\\n.dropdown-item-title[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n  font-size: 16px;\\n  color: var(--nav-text, #333333);\\n  transition: color 0.2s ease;\\n  pointer-events: none;\\n  line-height: 1.3;\\n}\\n.dropdown-item-title.active[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n}\\n\\n.dropdown-item-desc[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--text-secondary, #666666);\\n  transition: color 0.2s ease;\\n  pointer-events: none;\\n  line-height: 1.4;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return DropdownItemComponent;\n})();", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "DropdownItemComponent", "router", "icon", "label", "description", "route", "itemClick", "constructor", "isActive", "url", "onClick", "event", "stopPropagation", "preventDefault", "console", "log", "emit", "i0", "ɵɵdirectiveInject", "i1", "Router", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "DropdownItemComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵlistener", "DropdownItemComponent_Template_button_click_0_listener", "$event", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵclassProp", "ɵɵadvance", "ɵɵproperty", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "ɵɵtextInterpolate1", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\shared\\components\\dropdown-item\\dropdown-item.component.ts", "C:\\console\\aava-ui-web\\projects\\console\\src\\app\\shared\\components\\dropdown-item\\dropdown-item.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Router } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-dropdown-item',\r\n  standalone: true,\r\n  imports: [CommonModule],\r\n  templateUrl: './dropdown-item.component.html',\r\n  styleUrl: './dropdown-item.component.scss',\r\n})\r\nexport class DropdownItemComponent {\r\n  @Input() icon: string = '';\r\n  @Input() label: string = '';\r\n  @Input() description: string = '';\r\n  @Input() route: string = '';\r\n  @Output() itemClick = new EventEmitter<{ route: string; label: string }>();\r\n\r\n  constructor(private router: Router) {}\r\n\r\n  // Check if this item is active based on the current route\r\n  get isActive(): boolean {\r\n    return this.router.url === this.route;\r\n  }\r\n\r\n  onClick(event: MouseEvent): void {\r\n    // Stop the event from bubbling up to parent elements\r\n    event.stopPropagation();\r\n    event.preventDefault();\r\n\r\n    console.log(`Dropdown item clicked: ${this.label}`);\r\n\r\n    this.itemClick.emit({\r\n      route: this.route,\r\n      label: this.label,\r\n    });\r\n  }\r\n}\r\n", "<button\r\n  class=\"dropdown-item\"\r\n  [class.active]=\"isActive\"\r\n  (click)=\"onClick($event)\"\r\n>\r\n  <div class=\"dropdown-item-icon\" [class.active]=\"isActive\">\r\n    <img [src]=\"icon\" alt=\"\" class=\"dropdown-icon\" [class.active]=\"isActive\" />\r\n  </div>\r\n  <div class=\"dropdown-item-content\">\r\n    <div class=\"dropdown-item-title\" [class.active]=\"isActive\">{{ label }}</div>\r\n    <div class=\"dropdown-item-desc\" [class.active]=\"isActive\">\r\n      {{ description }}\r\n    </div>\r\n  </div>\r\n</button>\r\n"], "mappings": "AAAA,SAAmCA,YAAY,QAAQ,eAAe;AACtE,SAASC,YAAY,QAAQ,iBAAiB;;;AAU9C,WAAaC,qBAAqB;EAA5B,MAAOA,qBAAqB;IAOZC,MAAA;IANXC,IAAI,GAAW,EAAE;IACjBC,KAAK,GAAW,EAAE;IAClBC,WAAW,GAAW,EAAE;IACxBC,KAAK,GAAW,EAAE;IACjBC,SAAS,GAAG,IAAIR,YAAY,EAAoC;IAE1ES,YAAoBN,MAAc;MAAd,KAAAA,MAAM,GAANA,MAAM;IAAW;IAErC;IACA,IAAIO,QAAQA,CAAA;MACV,OAAO,IAAI,CAACP,MAAM,CAACQ,GAAG,KAAK,IAAI,CAACJ,KAAK;IACvC;IAEAK,OAAOA,CAACC,KAAiB;MACvB;MACAA,KAAK,CAACC,eAAe,EAAE;MACvBD,KAAK,CAACE,cAAc,EAAE;MAEtBC,OAAO,CAACC,GAAG,CAAC,0BAA0B,IAAI,CAACZ,KAAK,EAAE,CAAC;MAEnD,IAAI,CAACG,SAAS,CAACU,IAAI,CAAC;QAClBX,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBF,KAAK,EAAE,IAAI,CAACA;OACb,CAAC;IACJ;;uCAzBWH,qBAAqB,EAAAiB,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;;YAArBpB,qBAAqB;MAAAqB,SAAA;MAAAC,MAAA;QAAApB,IAAA;QAAAC,KAAA;QAAAC,WAAA;QAAAC,KAAA;MAAA;MAAAkB,OAAA;QAAAjB,SAAA;MAAA;MAAAkB,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXlCZ,EAAA,CAAAc,cAAA,gBAIC;UADCd,EAAA,CAAAe,UAAA,mBAAAC,uDAAAC,MAAA;YAAA,OAASJ,GAAA,CAAApB,OAAA,CAAAwB,MAAA,CAAe;UAAA,EAAC;UAEzBjB,EAAA,CAAAc,cAAA,aAA0D;UACxDd,EAAA,CAAAkB,SAAA,aAA2E;UAC7ElB,EAAA,CAAAmB,YAAA,EAAM;UAEJnB,EADF,CAAAc,cAAA,aAAmC,aAC0B;UAAAd,EAAA,CAAAoB,MAAA,GAAW;UAAApB,EAAA,CAAAmB,YAAA,EAAM;UAC5EnB,EAAA,CAAAc,cAAA,aAA0D;UACxDd,EAAA,CAAAoB,MAAA,GACF;UAEJpB,EAFI,CAAAmB,YAAA,EAAM,EACF,EACC;;;UAZPnB,EAAA,CAAAqB,WAAA,WAAAR,GAAA,CAAAtB,QAAA,CAAyB;UAGOS,EAAA,CAAAsB,SAAA,EAAyB;UAAzBtB,EAAA,CAAAqB,WAAA,WAAAR,GAAA,CAAAtB,QAAA,CAAyB;UACRS,EAAA,CAAAsB,SAAA,EAAyB;UAAzBtB,EAAA,CAAAqB,WAAA,WAAAR,GAAA,CAAAtB,QAAA,CAAyB;UAAnES,EAAA,CAAAuB,UAAA,QAAAV,GAAA,CAAA5B,IAAA,EAAAe,EAAA,CAAAwB,aAAA,CAAY;UAGgBxB,EAAA,CAAAsB,SAAA,GAAyB;UAAzBtB,EAAA,CAAAqB,WAAA,WAAAR,GAAA,CAAAtB,QAAA,CAAyB;UAACS,EAAA,CAAAsB,SAAA,EAAW;UAAXtB,EAAA,CAAAyB,iBAAA,CAAAZ,GAAA,CAAA3B,KAAA,CAAW;UACtCc,EAAA,CAAAsB,SAAA,EAAyB;UAAzBtB,EAAA,CAAAqB,WAAA,WAAAR,GAAA,CAAAtB,QAAA,CAAyB;UACvDS,EAAA,CAAAsB,SAAA,EACF;UADEtB,EAAA,CAAA0B,kBAAA,MAAAb,GAAA,CAAA1B,WAAA,MACF;;;qBDLQL,YAAY;MAAA6C,MAAA;IAAA;;SAIX5C,qBAAqB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}