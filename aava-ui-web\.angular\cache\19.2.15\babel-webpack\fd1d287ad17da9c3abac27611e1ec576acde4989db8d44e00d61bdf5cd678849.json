{"ast": null, "code": "// Helper function to safely get environment variables from window.env\n// It will throw an error if a key is not found, ensuring all required envs are present.\nconst getRequiredEnv = key => {\n  const envWindow = window;\n  const value = envWindow.env?.[key];\n  if (value === undefined || value === null) {\n    throw new Error(`Environment variable '${key}' is not defined in window.env.`);\n  }\n  return String(value); // Ensure the value is returned as a string\n};\n// ---\n// Dynamically retrieve the base URL first, as it's used for other URLs\nconst dynamicBaseUrl = getRequiredEnv('baseUrl');\n// ---\n// Environment configuration for Elder Wand Application\n// All values are dynamically retrieved from window.env.\n// If a variable is not found, an error will be thrown.\nexport const environment = {\n  production: false,\n  // This often remains a static build-time flag\n  // Application URLs (constructed dynamically)\n  elderWandUrl: getRequiredEnv('elderWandUrl'),\n  experienceStudioUrl: getRequiredEnv('experienceStudioUrl'),\n  productStudioUrl: getRequiredEnv('productStudioUrl'),\n  consoleRedirectUrl: getRequiredEnv('consoleRedirectUrl'),\n  consoleUrl: getRequiredEnv('consoleUrl'),\n  consoleRedirectUri: getRequiredEnv('consoleRedirectUri'),\n  // Often same as consoleRedirectUrl\n  // API Configuration (constructed dynamically or directly from window.env)\n  apiVersion: getRequiredEnv('apiVersion'),\n  baseUrl: getRequiredEnv('baseUrl'),\n  // The base URL itself\n  consoleApi: getRequiredEnv('consoleApi'),\n  consoleApiV2: getRequiredEnv('consoleApiV2'),\n  consoleApiAuthUrl: getRequiredEnv('consoleApiAuthUrl'),\n  consoleEmbeddingApi: getRequiredEnv('consoleEmbeddingApi'),\n  consoleInstructionApi: getRequiredEnv('consoleInstructionApi'),\n  consoleLangfuseUrl: getRequiredEnv('consoleLangfuseUrl'),\n  consoleTruelensUrl: getRequiredEnv('consoleTruelensUrl'),\n  consolePipelineApi: getRequiredEnv('consolePipelineApi'),\n  experienceApiUrl: getRequiredEnv('experienceApiUrl'),\n  productApiUrl: getRequiredEnv('productApiUrl'),\n  // Legacy properties for backward compatibility\n  apiBaseUrl: getRequiredEnv('baseUrl'),\n  apiUrl: getRequiredEnv('baseUrl'),\n  elderWandApiAuthUrl: getRequiredEnv('consoleApiAuthUrl'),\n  elderWandRedirectUrl: getRequiredEnv('elderWandUrl'),\n  // Logging and App Specific\n  enableLogStreaming: getRequiredEnv('enableLogStreaming'),\n  logStreamingApiUrl: getRequiredEnv('logStreamingApiUrl'),\n  appVersion: getRequiredEnv('appVersion'),\n  workflowExecutionMode: getRequiredEnv('workflowExecutionMode'),\n  useBasicLogin: getRequiredEnv('useBasicLogin'),\n  // Assuming this is a string \"true\" or \"false\"\n  // Utility function for constructing API URLs\n  getApiUrl: endpoint => {\n    const baseUrl = getRequiredEnv('baseUrl');\n    return `${baseUrl}${endpoint}`;\n  }\n};\n// Log the environment configuration for debugging purposes\nconsole.log('Environment configuration loaded dynamically:', environment);", "map": {"version": 3, "names": ["getRequiredEnv", "key", "envWindow", "window", "value", "env", "undefined", "Error", "String", "dynamicBaseUrl", "environment", "production", "elderWandUrl", "experienceStudioUrl", "productStudioUrl", "consoleRedirectUrl", "consoleUrl", "consoleRedirectUri", "apiVersion", "baseUrl", "consoleApi", "consoleApiV2", "consoleApiAuthUrl", "consoleEmbeddingApi", "consoleInstructionApi", "consoleLangfuseUrl", "consoleTruelensUrl", "consolePipelineApi", "experienceApiUrl", "productApiUrl", "apiBaseUrl", "apiUrl", "elderWandApiAuthUrl", "elderWandRedirectUrl", "enableLogStreaming", "logStreamingApiUrl", "appVersion", "workflowExecutionMode", "useBasicLogin", "getApiUrl", "endpoint", "console", "log"], "sources": ["C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\environments\\environment.ts"], "sourcesContent": ["// Helper function to safely get environment variables from window.env\r\n// It will throw an error if a key is not found, ensuring all required envs are present.\r\nconst getRequiredEnv = (key: string): string => {\r\n  // Extend the Window interface to include 'env'\r\n  interface EnvWindow extends Window {\r\n    env?: Record<string, string>;\r\n  }\r\n  const envWindow = window as EnvWindow;\r\n  const value = envWindow.env?.[key];\r\n  if (value === undefined || value === null) {\r\n    throw new Error(`Environment variable '${key}' is not defined in window.env.`);\r\n  }\r\n  return String(value); // Ensure the value is returned as a string\r\n};\r\n\r\n// ---\r\n\r\n// Dynamically retrieve the base URL first, as it's used for other URLs\r\nconst dynamicBaseUrl: string = getRequiredEnv('baseUrl');\r\n\r\n// ---\r\n\r\n// Environment configuration for Elder Wand Application\r\n// All values are dynamically retrieved from window.env.\r\n// If a variable is not found, an error will be thrown.\r\nexport const environment = {\r\n  production: false, // This often remains a static build-time flag\r\n\r\n  // Application URLs (constructed dynamically)\r\n  elderWandUrl: getRequiredEnv('elderWandUrl'),\r\n  experienceStudioUrl: getRequiredEnv('experienceStudioUrl'),\r\n  productStudioUrl: getRequiredEnv('productStudioUrl'),\r\n  consoleRedirectUrl: getRequiredEnv('consoleRedirectUrl'),\r\n  consoleUrl: getRequiredEnv('consoleUrl'),\r\n  consoleRedirectUri: getRequiredEnv('consoleRedirectUri'), // Often same as consoleRedirectUrl\r\n\r\n  // API Configuration (constructed dynamically or directly from window.env)\r\n  apiVersion: getRequiredEnv('apiVersion'),\r\n  baseUrl: getRequiredEnv('baseUrl'), // The base URL itself\r\n  consoleApi: getRequiredEnv('consoleApi'),\r\n  consoleApiV2: getRequiredEnv('consoleApiV2'),\r\n  consoleApiAuthUrl: getRequiredEnv('consoleApiAuthUrl'),\r\n  consoleEmbeddingApi: getRequiredEnv('consoleEmbeddingApi'),\r\n  consoleInstructionApi: getRequiredEnv('consoleInstructionApi'),\r\n  consoleLangfuseUrl: getRequiredEnv('consoleLangfuseUrl'),\r\n  consoleTruelensUrl: getRequiredEnv('consoleTruelensUrl'),\r\n  consolePipelineApi: getRequiredEnv('consolePipelineApi'),\r\n  experienceApiUrl: getRequiredEnv('experienceApiUrl'),\r\n  productApiUrl: getRequiredEnv('productApiUrl'),\r\n\r\n  // Legacy properties for backward compatibility\r\n  apiBaseUrl: getRequiredEnv('baseUrl'),\r\n  apiUrl: getRequiredEnv('baseUrl'),\r\n  elderWandApiAuthUrl: getRequiredEnv('consoleApiAuthUrl'),\r\n  elderWandRedirectUrl: getRequiredEnv('elderWandUrl'),\r\n\r\n  // Logging and App Specific\r\n  enableLogStreaming: getRequiredEnv('enableLogStreaming'),\r\n  logStreamingApiUrl: getRequiredEnv('logStreamingApiUrl'),\r\n  appVersion: getRequiredEnv('appVersion'),\r\n  workflowExecutionMode: getRequiredEnv('workflowExecutionMode'),\r\n  useBasicLogin: getRequiredEnv('useBasicLogin'), // Assuming this is a string \"true\" or \"false\"\r\n\r\n  // Utility function for constructing API URLs\r\n  getApiUrl: (endpoint: string) => {\r\n    const baseUrl = getRequiredEnv('baseUrl');\r\n    return `${baseUrl}${endpoint}`;\r\n  }\r\n};\r\n\r\n// Log the environment configuration for debugging purposes\r\nconsole.log('Environment configuration loaded dynamically:', environment);"], "mappings": "AAAA;AACA;AACA,MAAMA,cAAc,GAAIC,GAAW,IAAY;EAK7C,MAAMC,SAAS,GAAGC,MAAmB;EACrC,MAAMC,KAAK,GAAGF,SAAS,CAACG,GAAG,GAAGJ,GAAG,CAAC;EAClC,IAAIG,KAAK,KAAKE,SAAS,IAAIF,KAAK,KAAK,IAAI,EAAE;IACzC,MAAM,IAAIG,KAAK,CAAC,yBAAyBN,GAAG,iCAAiC,CAAC;EAChF;EACA,OAAOO,MAAM,CAACJ,KAAK,CAAC,CAAC,CAAC;AACxB,CAAC;AAED;AAEA;AACA,MAAMK,cAAc,GAAWT,cAAc,CAAC,SAAS,CAAC;AAExD;AAEA;AACA;AACA;AACA,OAAO,MAAMU,WAAW,GAAG;EACzBC,UAAU,EAAE,KAAK;EAAE;EAEnB;EACAC,YAAY,EAAEZ,cAAc,CAAC,cAAc,CAAC;EAC5Ca,mBAAmB,EAAEb,cAAc,CAAC,qBAAqB,CAAC;EAC1Dc,gBAAgB,EAAEd,cAAc,CAAC,kBAAkB,CAAC;EACpDe,kBAAkB,EAAEf,cAAc,CAAC,oBAAoB,CAAC;EACxDgB,UAAU,EAAEhB,cAAc,CAAC,YAAY,CAAC;EACxCiB,kBAAkB,EAAEjB,cAAc,CAAC,oBAAoB,CAAC;EAAE;EAE1D;EACAkB,UAAU,EAAElB,cAAc,CAAC,YAAY,CAAC;EACxCmB,OAAO,EAAEnB,cAAc,CAAC,SAAS,CAAC;EAAE;EACpCoB,UAAU,EAAEpB,cAAc,CAAC,YAAY,CAAC;EACxCqB,YAAY,EAAErB,cAAc,CAAC,cAAc,CAAC;EAC5CsB,iBAAiB,EAAEtB,cAAc,CAAC,mBAAmB,CAAC;EACtDuB,mBAAmB,EAAEvB,cAAc,CAAC,qBAAqB,CAAC;EAC1DwB,qBAAqB,EAAExB,cAAc,CAAC,uBAAuB,CAAC;EAC9DyB,kBAAkB,EAAEzB,cAAc,CAAC,oBAAoB,CAAC;EACxD0B,kBAAkB,EAAE1B,cAAc,CAAC,oBAAoB,CAAC;EACxD2B,kBAAkB,EAAE3B,cAAc,CAAC,oBAAoB,CAAC;EACxD4B,gBAAgB,EAAE5B,cAAc,CAAC,kBAAkB,CAAC;EACpD6B,aAAa,EAAE7B,cAAc,CAAC,eAAe,CAAC;EAE9C;EACA8B,UAAU,EAAE9B,cAAc,CAAC,SAAS,CAAC;EACrC+B,MAAM,EAAE/B,cAAc,CAAC,SAAS,CAAC;EACjCgC,mBAAmB,EAAEhC,cAAc,CAAC,mBAAmB,CAAC;EACxDiC,oBAAoB,EAAEjC,cAAc,CAAC,cAAc,CAAC;EAEpD;EACAkC,kBAAkB,EAAElC,cAAc,CAAC,oBAAoB,CAAC;EACxDmC,kBAAkB,EAAEnC,cAAc,CAAC,oBAAoB,CAAC;EACxDoC,UAAU,EAAEpC,cAAc,CAAC,YAAY,CAAC;EACxCqC,qBAAqB,EAAErC,cAAc,CAAC,uBAAuB,CAAC;EAC9DsC,aAAa,EAAEtC,cAAc,CAAC,eAAe,CAAC;EAAE;EAEhD;EACAuC,SAAS,EAAGC,QAAgB,IAAI;IAC9B,MAAMrB,OAAO,GAAGnB,cAAc,CAAC,SAAS,CAAC;IACzC,OAAO,GAAGmB,OAAO,GAAGqB,QAAQ,EAAE;EAChC;CACD;AAED;AACAC,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAEhC,WAAW,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}