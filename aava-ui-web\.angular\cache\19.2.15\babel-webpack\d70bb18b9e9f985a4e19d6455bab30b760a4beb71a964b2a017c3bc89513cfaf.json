{"ast": null, "code": "import { CommonModule, DatePipe } from '@angular/common';\nimport { AvaTextboxComponent, IconComponent, SliderComponent } from '@ava/play-comp-library';\nimport { PreviewPanelComponent } from '../../../../components/preview-panel/preview-panel.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction AgentsPreviewPanelComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵelement(1, \"div\", 11);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading details...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"label\");\n    i0.ɵɵtext(2, \"Model Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 29);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate((ctx_r1.previewData == null ? null : ctx_r1.previewData.title) || ctx_r1.previewData.data.name);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"label\");\n    i0.ɵɵtext(2, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 30);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.modelDescription || ctx_r1.previewData.data.description);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"label\");\n    i0.ɵɵtext(2, \"Added by\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 29);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.createdBy);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"label\");\n    i0.ɵɵtext(2, \"Added on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 29);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 1, ctx_r1.previewData.data.createdOn || ctx_r1.previewData.data.createdDate, \"MM/dd/yyyy\"));\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"label\");\n    i0.ɵɵtext(2, \"Select Model\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 33)(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.modelType);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"label\");\n    i0.ɵɵtext(2, \"Temperature\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"ava-slider\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"min\", 0)(\"max\", 1)(\"step\", 0.01)(\"value\", ctx_r1.previewData.data.temperature);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_15_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"label\");\n    i0.ɵɵtext(2, \"Max Token\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"ava-textbox\", 38);\n    i0.ɵɵelementStart(4, \"div\", 39);\n    i0.ɵɵtext(5, \"4096 Tokens used\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"value\", ctx_r1.previewData.data.maxToken)(\"disabled\", true);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_15_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"label\");\n    i0.ɵɵtext(2, \"Top P\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"ava-textbox\", 40);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"value\", ctx_r1.previewData.data.topP)(\"disabled\", true);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtemplate(1, AgentsPreviewPanelComponent_div_9_div_1_div_15_div_1_Template, 6, 2, \"div\", 36)(2, AgentsPreviewPanelComponent_div_9_div_1_div_15_div_2_Template, 4, 2, \"div\", 36);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.maxToken);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.topP);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"label\");\n    i0.ɵɵtext(2, \"Max Iteration\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"ava-textbox\", 41);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"value\", ctx_r1.previewData.data.maxIteration)(\"disabled\", true);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"label\");\n    i0.ɵɵtext(2, \"AI Engine\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 33);\n    i0.ɵɵelement(4, \"span\", 42);\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.aiEngine);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"label\");\n    i0.ɵɵtext(2, \"Model Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 33)(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.modelType);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"label\");\n    i0.ɵɵtext(2, \"Base URL\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 43);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.baseurl || ctx_r1.previewData.data.baseUrl);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"label\");\n    i0.ɵɵtext(2, \"LLM Deployment Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 43);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.llmDeploymentName);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"label\");\n    i0.ɵɵtext(2, \"API Key Encoded\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 43);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.apiKey);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"label\");\n    i0.ɵɵtext(2, \"API Version\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 43);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.apiVersion);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 18)(2, \"h3\");\n    i0.ɵɵtext(3, \"Model Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, AgentsPreviewPanelComponent_div_9_div_1_div_4_Template, 5, 1, \"div\", 19)(5, AgentsPreviewPanelComponent_div_9_div_1_div_5_Template, 5, 1, \"div\", 19);\n    i0.ɵɵelementStart(6, \"div\", 20)(7, \"div\", 21);\n    i0.ɵɵtemplate(8, AgentsPreviewPanelComponent_div_9_div_1_div_8_Template, 5, 1, \"div\", 22)(9, AgentsPreviewPanelComponent_div_9_div_1_div_9_Template, 6, 4, \"div\", 22);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 18)(11, \"h3\");\n    i0.ɵɵtext(12, \"Model Configuration\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, AgentsPreviewPanelComponent_div_9_div_1_div_13_Template, 6, 1, \"div\", 23)(14, AgentsPreviewPanelComponent_div_9_div_1_div_14_Template, 4, 4, \"div\", 23)(15, AgentsPreviewPanelComponent_div_9_div_1_div_15_Template, 3, 2, \"div\", 24)(16, AgentsPreviewPanelComponent_div_9_div_1_div_16_Template, 4, 2, \"div\", 23);\n    i0.ɵɵelementStart(17, \"div\", 25)(18, \"span\", 26);\n    i0.ɵɵlistener(\"click\", function AgentsPreviewPanelComponent_div_9_div_1_Template_span_click_18_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleConfigDetails());\n    });\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 27);\n    i0.ɵɵtemplate(21, AgentsPreviewPanelComponent_div_9_div_1_div_21_Template, 7, 1, \"div\", 23)(22, AgentsPreviewPanelComponent_div_9_div_1_div_22_Template, 6, 1, \"div\", 23)(23, AgentsPreviewPanelComponent_div_9_div_1_div_23_Template, 5, 1, \"div\", 23)(24, AgentsPreviewPanelComponent_div_9_div_1_div_24_Template, 5, 1, \"div\", 23)(25, AgentsPreviewPanelComponent_div_9_div_1_div_25_Template, 5, 1, \"div\", 23)(26, AgentsPreviewPanelComponent_div_9_div_1_div_26_Template, 5, 1, \"div\", 23);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.previewData == null ? null : ctx_r1.previewData.title) || ctx_r1.previewData.data.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.modelDescription || ctx_r1.previewData.data.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.createdBy);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.createdOn || ctx_r1.previewData.data.createdDate);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.modelType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.temperature !== undefined && ctx_r1.previewData.data.temperature !== null);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.maxToken || ctx_r1.previewData.data.topP);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.maxIteration);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" View More Configuration Details \", ctx_r1.showMoreConfig ? \"-\" : \"+\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"expanded\", ctx_r1.showMoreConfig);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.aiEngine);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.modelType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.baseurl || ctx_r1.previewData.data.baseUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.llmDeploymentName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.apiKey);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.apiVersion);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_2_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"label\");\n    i0.ɵɵtext(2, \"Tool Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 29);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate((ctx_r1.previewData == null ? null : ctx_r1.previewData.title) || ctx_r1.previewData.data.name);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_2_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"label\");\n    i0.ɵɵtext(2, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 30);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.description || ctx_r1.previewData.data.appDescription);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_2_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"label\");\n    i0.ɵɵtext(2, \"Area of Scope\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 30);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.areaOfScope);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_2_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"label\");\n    i0.ɵɵtext(2, \"Added by\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 29);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.createdBy);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_2_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"label\");\n    i0.ɵɵtext(2, \"Added on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 29);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 1, ctx_r1.previewData.data.createdOn || ctx_r1.previewData.data.createdDate, \"MM/dd/yyyy\"));\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_2_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"div\", 45);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.functionality || ctx_r1.previewData.data.content || ctx_r1.previewData.data.toolClassDef || \"No tool definition available\");\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"div\", 18)(2, \"h3\");\n    i0.ɵɵtext(3, \"Tool Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, AgentsPreviewPanelComponent_div_9_div_2_div_4_Template, 5, 1, \"div\", 19)(5, AgentsPreviewPanelComponent_div_9_div_2_div_5_Template, 5, 1, \"div\", 19)(6, AgentsPreviewPanelComponent_div_9_div_2_div_6_Template, 5, 1, \"div\", 19);\n    i0.ɵɵelementStart(7, \"div\", 20)(8, \"div\", 21);\n    i0.ɵɵtemplate(9, AgentsPreviewPanelComponent_div_9_div_2_div_9_Template, 5, 1, \"div\", 22)(10, AgentsPreviewPanelComponent_div_9_div_2_div_10_Template, 6, 4, \"div\", 22);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 18)(12, \"h3\");\n    i0.ɵɵtext(13, \"Tool Configuration\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, AgentsPreviewPanelComponent_div_9_div_2_div_14_Template, 3, 1, \"div\", 23);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.previewData == null ? null : ctx_r1.previewData.title) || ctx_r1.previewData.data.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.description || ctx_r1.previewData.data.appDescription);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.areaOfScope);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.createdBy);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.createdOn || ctx_r1.previewData.data.createdDate);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.functionality || ctx_r1.previewData.data.content || ctx_r1.previewData.data.toolClassDef);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_3_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"label\");\n    i0.ɵɵtext(2, \"Prompt Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 29);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate((ctx_r1.previewData == null ? null : ctx_r1.previewData.title) || ctx_r1.previewData.data.name);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_3_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"label\");\n    i0.ɵɵtext(2, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 30);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.description);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_3_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"label\");\n    i0.ɵɵtext(2, \"Role\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 29);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.role);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_3_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"label\");\n    i0.ɵɵtext(2, \"Goal\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 30);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.goal);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_3_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"label\");\n    i0.ɵɵtext(2, \"Backstory\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 30);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.backstory);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_3_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"label\");\n    i0.ɵɵtext(2, \"Added by\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 29);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.createdBy);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_3_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"label\");\n    i0.ɵɵtext(2, \"Added on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 29);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 1, ctx_r1.previewData.data.updatedAt || ctx_r1.previewData.data.createdAt, \"MM/dd/yyyy\"));\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_3_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"label\");\n    i0.ɵɵtext(2, \"Freeform Prompt \");\n    i0.ɵɵelementStart(3, \"span\", 47);\n    i0.ɵɵtext(4, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 48);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.promptTask || ctx_r1.previewData.data.template || ctx_r1.previewData.data.content);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_3_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"label\");\n    i0.ɵɵtext(2, \"Expected Output\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 48);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.expectedOutput);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 18)(2, \"h3\");\n    i0.ɵɵtext(3, \"Prompt Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, AgentsPreviewPanelComponent_div_9_div_3_div_4_Template, 5, 1, \"div\", 19)(5, AgentsPreviewPanelComponent_div_9_div_3_div_5_Template, 5, 1, \"div\", 19)(6, AgentsPreviewPanelComponent_div_9_div_3_div_6_Template, 5, 1, \"div\", 19)(7, AgentsPreviewPanelComponent_div_9_div_3_div_7_Template, 5, 1, \"div\", 19)(8, AgentsPreviewPanelComponent_div_9_div_3_div_8_Template, 5, 1, \"div\", 19);\n    i0.ɵɵelementStart(9, \"div\", 20)(10, \"div\", 21);\n    i0.ɵɵtemplate(11, AgentsPreviewPanelComponent_div_9_div_3_div_11_Template, 5, 1, \"div\", 22)(12, AgentsPreviewPanelComponent_div_9_div_3_div_12_Template, 6, 4, \"div\", 22);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"div\", 18)(14, \"h3\");\n    i0.ɵɵtext(15, \"Prompt Configuration\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, AgentsPreviewPanelComponent_div_9_div_3_div_16_Template, 7, 1, \"div\", 23)(17, AgentsPreviewPanelComponent_div_9_div_3_div_17_Template, 5, 1, \"div\", 23);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.previewData == null ? null : ctx_r1.previewData.title) || ctx_r1.previewData.data.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.description);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.role);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.goal);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.backstory);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.createdBy);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.updatedAt || ctx_r1.previewData.data.createdAt);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.promptTask || ctx_r1.previewData.data.template || ctx_r1.previewData.data.content);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.expectedOutput);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_4_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"label\");\n    i0.ɵɵtext(2, \"Knowledge Base Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 29);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.name || (ctx_r1.previewData == null ? null : ctx_r1.previewData.title));\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_4_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"label\");\n    i0.ɵɵtext(2, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 30);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.description);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_4_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"label\");\n    i0.ɵɵtext(2, \"Added by\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 29);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.createdBy);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_4_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"label\");\n    i0.ɵɵtext(2, \"Added on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 29);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 1, ctx_r1.previewData.data.createdDate || ctx_r1.previewData.data.createdOn, \"MM/dd/yyyy\"));\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_4_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"label\");\n    i0.ɵɵtext(2, \"Embedding Model\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 33)(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.embeddingModel);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_4_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"label\");\n    i0.ɵɵtext(2, \"Split Size\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"ava-slider\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"min\", 0)(\"max\", 1)(\"step\", 0.01)(\"value\", ctx_r1.previewData.data.splitSize);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_4_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"label\");\n    i0.ɵɵtext(2, \"Upload Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 43);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.uploadType);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_4_div_16_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52);\n    i0.ɵɵelement(1, \"ava-icon\", 53);\n    i0.ɵɵelementStart(2, \"span\", 54);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r3 = ctx.$implicit;\n    const i_r4 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"iconColor\", ctx_r1.getFileIconColor(i_r4))(\"iconSize\", 16);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(file_r3.fileName || file_r3.name || \"Knowledge Base Data\");\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_4_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"label\");\n    i0.ɵɵtext(2, \"Files Uploaded\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 50);\n    i0.ɵɵtemplate(4, AgentsPreviewPanelComponent_div_9_div_4_div_16_div_4_Template, 4, 3, \"div\", 51);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.previewData.data.files);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"div\", 18)(2, \"h3\");\n    i0.ɵɵtext(3, \"Knowledge Base Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, AgentsPreviewPanelComponent_div_9_div_4_div_4_Template, 5, 1, \"div\", 19)(5, AgentsPreviewPanelComponent_div_9_div_4_div_5_Template, 5, 1, \"div\", 19);\n    i0.ɵɵelementStart(6, \"div\", 20)(7, \"div\", 21);\n    i0.ɵɵtemplate(8, AgentsPreviewPanelComponent_div_9_div_4_div_8_Template, 5, 1, \"div\", 22)(9, AgentsPreviewPanelComponent_div_9_div_4_div_9_Template, 6, 4, \"div\", 22);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 18)(11, \"h3\");\n    i0.ɵɵtext(12, \"Knowledge Base Configuration\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, AgentsPreviewPanelComponent_div_9_div_4_div_13_Template, 6, 1, \"div\", 23)(14, AgentsPreviewPanelComponent_div_9_div_4_div_14_Template, 4, 4, \"div\", 23)(15, AgentsPreviewPanelComponent_div_9_div_4_div_15_Template, 5, 1, \"div\", 23)(16, AgentsPreviewPanelComponent_div_9_div_4_div_16_Template, 5, 1, \"div\", 23);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.name || (ctx_r1.previewData == null ? null : ctx_r1.previewData.title));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.createdBy);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.createdDate || ctx_r1.previewData.data.createdOn);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.embeddingModel);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.splitSize !== undefined && ctx_r1.previewData.data.splitSize !== null);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.uploadType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.files && ctx_r1.previewData.data.files.length > 0);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_5_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"label\");\n    i0.ɵɵtext(2, \"Guardrail Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 29);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate((ctx_r1.previewData == null ? null : ctx_r1.previewData.title) || ctx_r1.previewData.data.name);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_5_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"label\");\n    i0.ɵɵtext(2, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 30);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.description);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_5_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"label\");\n    i0.ɵɵtext(2, \"Added by\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 29);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.createdBy);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_5_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"label\");\n    i0.ɵɵtext(2, \"Added on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 29);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 1, ctx_r1.previewData.data.createdDate || ctx_r1.previewData.data.createdOn, \"MM/dd/yyyy\"));\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_5_div_10_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"label\");\n    i0.ɵɵtext(2, \"Guardrail in Colang\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 45);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.content);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_5_div_10_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"label\");\n    i0.ɵɵtext(2, \"Guardrail in Yml\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 45);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.yamlContent);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_5_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"h3\");\n    i0.ɵɵtext(2, \"Guardrail Configuration\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, AgentsPreviewPanelComponent_div_9_div_5_div_10_div_3_Template, 5, 1, \"div\", 23)(4, AgentsPreviewPanelComponent_div_9_div_5_div_10_div_4_Template, 5, 1, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.content);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.yamlContent);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"div\", 18)(2, \"h3\");\n    i0.ɵɵtext(3, \"Guardrail Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, AgentsPreviewPanelComponent_div_9_div_5_div_4_Template, 5, 1, \"div\", 19)(5, AgentsPreviewPanelComponent_div_9_div_5_div_5_Template, 5, 1, \"div\", 19);\n    i0.ɵɵelementStart(6, \"div\", 20)(7, \"div\", 21);\n    i0.ɵɵtemplate(8, AgentsPreviewPanelComponent_div_9_div_5_div_8_Template, 5, 1, \"div\", 22)(9, AgentsPreviewPanelComponent_div_9_div_5_div_9_Template, 6, 4, \"div\", 22);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(10, AgentsPreviewPanelComponent_div_9_div_5_div_10_Template, 5, 2, \"div\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.previewData == null ? null : ctx_r1.previewData.title) || ctx_r1.previewData.data.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.createdBy);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.createdDate || ctx_r1.previewData.data.createdOn);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.yamlContent || ctx_r1.previewData.data.content);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, AgentsPreviewPanelComponent_div_9_div_1_Template, 27, 17, \"div\", 12)(2, AgentsPreviewPanelComponent_div_9_div_2_Template, 15, 6, \"div\", 13)(3, AgentsPreviewPanelComponent_div_9_div_3_Template, 18, 9, \"div\", 14)(4, AgentsPreviewPanelComponent_div_9_div_4_Template, 17, 8, \"div\", 15)(5, AgentsPreviewPanelComponent_div_9_div_5_Template, 11, 5, \"div\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.previewData == null ? null : ctx_r1.previewData.type) === \"model\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.previewData == null ? null : ctx_r1.previewData.type) === \"tool\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.previewData == null ? null : ctx_r1.previewData.type) === \"prompt\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.previewData == null ? null : ctx_r1.previewData.type) === \"knowledge\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.previewData == null ? null : ctx_r1.previewData.type) === \"guardrail\");\n  }\n}\nfunction AgentsPreviewPanelComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"p\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.error);\n  }\n}\nexport let AgentsPreviewPanelComponent = /*#__PURE__*/(() => {\n  class AgentsPreviewPanelComponent {\n    previewData = null;\n    closePreview;\n    showMoreConfig = false;\n    toggleConfigDetails() {\n      this.showMoreConfig = !this.showMoreConfig;\n    }\n    onButtonClick(event) {\n      this.closePreview();\n    }\n    getAdditionalFields(data) {\n      const excludeFields = ['id', 'name', 'description', 'labelCode', 'categoryName', 'categoryId'];\n      return Object.keys(data).filter(key => !excludeFields.includes(key) && data[key] != null).map(key => ({\n        key,\n        value: data[key]\n      }));\n    }\n    getFileIconColor(index) {\n      const colors = ['#dc2626', '#2563eb', '#16a34a', '#f59e0b', '#8b5cf6'];\n      return colors[index % colors.length];\n    }\n    getButtonLabel() {\n      const type = this.previewData?.type;\n      switch (type) {\n        case 'model':\n          return 'Edit Model';\n        case 'tool':\n          return 'Edit Tool';\n        case 'prompt':\n          return 'Edit Prompt';\n        case 'knowledge':\n          return 'Edit Knowledge Base';\n        case 'guardrail':\n          return 'Edit Guardrail';\n        default:\n          return 'Edit';\n      }\n    }\n    static ɵfac = function AgentsPreviewPanelComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AgentsPreviewPanelComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AgentsPreviewPanelComponent,\n      selectors: [[\"app-agents-preview-panel\"]],\n      inputs: {\n        previewData: \"previewData\",\n        closePreview: \"closePreview\"\n      },\n      decls: 11,\n      vars: 4,\n      consts: [[1, \"preview-panel\"], [1, \"backdrop\", 3, \"click\"], [1, \"panel-container\", 3, \"click\", \"divider\"], [\"panel-header\", \"\", 1, \"preview-header\"], [1, \"panel-title\"], [\"iconName\", \"x\", \"iconColor\", \"black\", 1, \"close-btn\", 3, \"click\"], [\"panel-content\", \"\", 1, \"preview-content\"], [\"class\", \"preview-loading\", 4, \"ngIf\"], [4, \"ngIf\"], [\"class\", \"preview-error\", 4, \"ngIf\"], [1, \"preview-loading\"], [1, \"loading-spinner\"], [\"class\", \"model-preview\", 4, \"ngIf\"], [\"class\", \"tool-preview\", 4, \"ngIf\"], [\"class\", \"prompt-preview\", 4, \"ngIf\"], [\"class\", \"knowledge-preview\", 4, \"ngIf\"], [\"class\", \"guardrail-preview\", 4, \"ngIf\"], [1, \"model-preview\"], [1, \"model-section\"], [\"class\", \"model-field\", 4, \"ngIf\"], [1, \"model-meta\"], [1, \"meta-row\"], [\"class\", \"meta-item\", 4, \"ngIf\"], [\"class\", \"config-field\", 4, \"ngIf\"], [\"class\", \"config-row\", 4, \"ngIf\"], [1, \"config-toggle\"], [1, \"toggle-text\", 3, \"click\"], [1, \"config-details\"], [1, \"model-field\"], [1, \"field-value\"], [1, \"field-value\", \"description-text\"], [1, \"meta-item\"], [1, \"config-field\"], [1, \"dropdown-display\"], [1, \"disabled-slider\", 3, \"min\", \"max\", \"step\", \"value\"], [1, \"config-row\"], [\"class\", \"config-field half-width\", 4, \"ngIf\"], [1, \"config-field\", \"half-width\"], [\"placeholder\", \"4000\", \"type\", \"number\", 3, \"value\", \"disabled\"], [1, \"field-hint\"], [\"placeholder\", \"0.95\", \"type\", \"number\", \"step\", \"0.01\", 3, \"value\", \"disabled\"], [\"placeholder\", \"1\", \"type\", \"number\", 3, \"value\", \"disabled\"], [1, \"engine-icon\"], [1, \"input-display\"], [1, \"tool-preview\"], [1, \"code-content\"], [1, \"prompt-preview\"], [1, \"required\"], [1, \"prompt-content\"], [1, \"knowledge-preview\"], [1, \"files-list\"], [\"class\", \"file-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"file-item\"], [\"iconName\", \"file-text\", 3, \"iconColor\", \"iconSize\"], [1, \"file-name\"], [1, \"guardrail-preview\"], [\"class\", \"model-section\", 4, \"ngIf\"], [1, \"preview-error\"]],\n      template: function AgentsPreviewPanelComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵlistener(\"click\", function AgentsPreviewPanelComponent_Template_div_click_1_listener() {\n            return ctx.closePreview();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"app-preview-panel\", 2);\n          i0.ɵɵlistener(\"click\", function AgentsPreviewPanelComponent_Template_app_preview_panel_click_2_listener($event) {\n            return $event.stopPropagation();\n          });\n          i0.ɵɵelementStart(3, \"div\", 3)(4, \"span\", 4);\n          i0.ɵɵtext(5, \"Metadata Information\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"ava-icon\", 5);\n          i0.ɵɵlistener(\"click\", function AgentsPreviewPanelComponent_Template_ava_icon_click_6_listener() {\n            return ctx.closePreview();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 6);\n          i0.ɵɵtemplate(8, AgentsPreviewPanelComponent_div_8_Template, 4, 0, \"div\", 7)(9, AgentsPreviewPanelComponent_div_9_Template, 6, 5, \"div\", 8)(10, AgentsPreviewPanelComponent_div_10_Template, 3, 1, \"div\", 9);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"divider\", false);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.previewData == null ? null : ctx.previewData.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (ctx.previewData == null ? null : ctx.previewData.data) && !(ctx.previewData == null ? null : ctx.previewData.loading));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.previewData == null ? null : ctx.previewData.error);\n        }\n      },\n      dependencies: [PreviewPanelComponent, IconComponent, DatePipe, CommonModule, i1.NgForOf, i1.NgIf, SliderComponent, AvaTextboxComponent],\n      styles: [\".preview-panel[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  z-index: 1000;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.backdrop[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(0, 0, 0, 0.5);\\n  -webkit-backdrop-filter: blur(4px);\\n          backdrop-filter: blur(4px);\\n}\\n\\n.panel-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1000;\\n  width: 480px;\\n  max-height: 90vh;\\n  background: #fff;\\n  border-radius: 12px;\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);\\n  overflow: hidden;\\n  animation: slideIn 0.3s ease-out;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.preview-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  width: 100%;\\n}\\n\\n.panel-title[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin: 0;\\n}\\n\\n.close-btn[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  background: transparent;\\n  border: none;\\n  color: #6b7280;\\n  cursor: pointer;\\n  border-radius: 8px;\\n  transition: all 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.close-btn[_ngcontent-%COMP%]:hover {\\n  background: #f3f4f6;\\n  color: #374151;\\n}\\n\\n.preview-content[_ngcontent-%COMP%] {\\n  position: relative;\\n  flex: 1;\\n  overflow-y: auto;\\n  overflow-x: hidden; \\n\\n  max-height: calc(90vh - 140px);\\n  padding-top: 40px;\\n  border-top: 1px solid #e5e7eb;\\n}\\n.preview-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.preview-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: rgba(0, 0, 0, 0.1);\\n  border-radius: 3px;\\n}\\n.preview-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(0, 0, 0, 0.3);\\n  border-radius: 3px;\\n}\\n.preview-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: rgba(0, 0, 0, 0.5);\\n}\\n\\n.config-field[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.config-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin-bottom: 8px;\\n}\\n.config-field[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #1a1d29;\\n  line-height: 1.5;\\n}\\n.config-field[_ngcontent-%COMP%]   .field-hint[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #6b7280;\\n  margin-top: 4px;\\n}\\n\\n.code-content[_ngcontent-%COMP%], .prompt-content[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  background: #f8f9fa;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  color: #1a1d29;\\n  font-family: \\\"SF Mono\\\", Monaco, \\\"Cascadia Code\\\", monospace;\\n  font-size: 0.875rem;\\n  line-height: 1.6;\\n  min-height: 120px;\\n  white-space: pre-wrap;\\n  overflow-y: auto;\\n  overflow-x: hidden;\\n  scrollbar-width: none;\\n  -ms-overflow-style: none;\\n}\\n.code-content[_ngcontent-%COMP%]::-webkit-scrollbar, .prompt-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none;\\n}\\n.code-content[_ngcontent-%COMP%]:empty::before, .prompt-content[_ngcontent-%COMP%]:empty::before {\\n  content: \\\"No configuration available\\\";\\n  color: #9ca3af;\\n  font-style: italic;\\n}\\n\\n.model-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin: 0 0 24px 0;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  margin-bottom: 20px;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-row[_ngcontent-%COMP%]   .config-field.half-width[_ngcontent-%COMP%] {\\n  flex: 1;\\n  margin-bottom: 0;\\n  min-width: 0;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n  padding-bottom: 24px;\\n  border-bottom: 1px solid #e5e7eb;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10rem;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #1a1d29;\\n  margin-bottom: 4px;\\n  font-weight: 600;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #1a1d29;\\n  font-size: 0.875rem;\\n}\\n\\n.tool-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%], .prompt-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .prompt-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin: 0 0 24px 0;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%], .prompt-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%], .prompt-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin-bottom: 8px;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%], .prompt-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  line-height: 1.5;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value.description-text[_ngcontent-%COMP%], .prompt-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value.description-text[_ngcontent-%COMP%] {\\n  font-weight: 400;\\n  color: #6b7280;\\n  line-height: 1.6;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .input-display[_ngcontent-%COMP%], .prompt-preview[_ngcontent-%COMP%]   .input-display[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 12px 16px;\\n  background: #f8f9fa;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  color: #1a1d29;\\n  min-height: 48px;\\n  font-size: 0.875rem;\\n}\\n\\n  .preview-panel [panel-footer] {\\n  padding: 20px 24px 24px 24px;\\n  border-top: 1px solid #f0f1f2;\\n  background: #fafbfc;\\n}\\n  .preview-panel [panel-footer] ava-button {\\n  width: 100%;\\n}\\n  .preview-panel [panel-footer] ava-button button {\\n  width: 100%;\\n  height: 44px;\\n  font-weight: 600;\\n  border-radius: 8px;\\n  transition: all 0.2s ease;\\n}\\n  .preview-panel [panel-footer] ava-button button:hover {\\n  transform: translateY(-1px);\\n}\\n\\n.preview-loading[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 20px;\\n}\\n.preview-loading[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border: 3px solid #f3f4f6;\\n  border-top: 3px solid #3b82f6;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n  margin-bottom: 16px;\\n}\\n.preview-loading[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #6b7280;\\n  font-size: 0.875rem;\\n}\\n\\n.preview-error[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: #dc2626;\\n  padding: 40px 20px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .panel-container[_ngcontent-%COMP%] {\\n    width: 95vw;\\n    max-width: 400px;\\n  }\\n  .panel-title[_ngcontent-%COMP%] {\\n    font-size: 1.25rem;\\n  }\\n}\\n.model-preview[_ngcontent-%COMP%]   .preview-field[_ngcontent-%COMP%] {\\n  border-left-color: #10b981;\\n}\\n\\n.tool-preview[_ngcontent-%COMP%]   .preview-field[_ngcontent-%COMP%] {\\n  border-left-color: #f59e0b;\\n}\\n\\n.prompt-preview[_ngcontent-%COMP%]   .preview-field[_ngcontent-%COMP%] {\\n  border-left-color: #8b5cf6;\\n}\\n\\n.knowledge-preview[_ngcontent-%COMP%]   .preview-field[_ngcontent-%COMP%] {\\n  border-left-color: #06b6d4;\\n}\\n\\n.guardrail-preview[_ngcontent-%COMP%]   .preview-field[_ngcontent-%COMP%] {\\n  border-left-color: #ef4444;\\n}\\n\\n.model-preview[_ngcontent-%COMP%] {\\n  height: 100%;\\n  overflow-y: auto;\\n  overflow-x: hidden; \\n\\n  padding-bottom: 20px;\\n  width: 100%;\\n  box-sizing: border-box;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin: 0 0 24px 0;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  margin-bottom: 20px;\\n  width: 100%;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-row[_ngcontent-%COMP%]   .config-field.half-width[_ngcontent-%COMP%] {\\n  flex: 1;\\n  margin-bottom: 0;\\n  min-width: 0;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-row[_ngcontent-%COMP%]   .config-field.half-width[_ngcontent-%COMP%]     ava-textbox {\\n  width: 100%;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-row[_ngcontent-%COMP%]   .config-field.half-width[_ngcontent-%COMP%]     ava-textbox .textbox-container {\\n  width: 100%;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-row[_ngcontent-%COMP%]   .config-field.half-width[_ngcontent-%COMP%]     ava-textbox .textbox-container input {\\n  width: 100%;\\n  box-sizing: border-box;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  width: 100%;\\n  box-sizing: border-box;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin-bottom: 8px;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .field-hint[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #6b7280;\\n  margin-top: 4px;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]     ava-textbox {\\n  width: 100%;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]     ava-textbox .textbox-container {\\n  width: 100%;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]     ava-textbox .textbox-container input {\\n  width: 100%;\\n  box-sizing: border-box;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #374151;\\n  margin-bottom: 8px;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  line-height: 1.5;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value.description-text[_ngcontent-%COMP%] {\\n  font-weight: 400;\\n  color: #6b7280;\\n  line-height: 1.6;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n  padding-bottom: 24px;\\n  border-bottom: 1px solid #e5e7eb;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10rem;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  color: #6b7280;\\n  margin-bottom: 4px;\\n  font-weight: 400;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #1a1d29;\\n  font-size: 0.875rem;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #374151;\\n  margin-bottom: 8px;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .dropdown-display[_ngcontent-%COMP%], \\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .input-display[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 12px 16px;\\n  background: #f8f9fa;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  color: #1a1d29;\\n  min-height: 48px;\\n  box-sizing: border-box;\\n  font-size: 0.875rem;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .dropdown-display[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  width: 0;\\n  height: 0;\\n  border-left: 6px solid transparent;\\n  border-right: 6px solid transparent;\\n  border-top: 8px solid #6b7280;\\n  margin-left: 8px;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .dropdown-display[_ngcontent-%COMP%]   .engine-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 1rem;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]     ava-slider .slider-container {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]     ava-slider .slider-container .slider-track {\\n  flex: 1;\\n  height: 6px;\\n  background: #e5e7eb;\\n  border-radius: 3px;\\n  position: relative;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]     ava-slider .slider-container .slider-track .slider-fill {\\n  height: 100%;\\n  background: #3b82f6;\\n  border-radius: 3px;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]     ava-slider .slider-container .slider-thumb {\\n  width: 20px;\\n  height: 20px;\\n  background: #3b82f6;\\n  border-radius: 50%;\\n  cursor: pointer;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\\n  border: none;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]     ava-slider .slider-container .slider-value {\\n  min-width: 60px;\\n  padding: 8px 12px;\\n  background: #f8f9fa;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 6px;\\n  text-align: center;\\n  font-size: 0.875rem;\\n  color: #374151;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-toggle[_ngcontent-%COMP%] {\\n  margin: 16px 0;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-toggle[_ngcontent-%COMP%]   .toggle-text[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  cursor: pointer;\\n  font-size: 14px;\\n  color: #007bff;\\n  transition: color 0.2s ease;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-toggle[_ngcontent-%COMP%]   .toggle-text[_ngcontent-%COMP%]:hover {\\n  color: #0056b3;\\n}\\n\\n.config-toggle[_ngcontent-%COMP%] {\\n  margin: 16px 0;\\n}\\n.config-toggle[_ngcontent-%COMP%]   .toggle-text[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  cursor: pointer;\\n  font-size: 14px;\\n  color: #007bff;\\n  transition: color 0.2s ease;\\n}\\n.config-toggle[_ngcontent-%COMP%]   .toggle-text[_ngcontent-%COMP%]:hover {\\n  color: #0056b3;\\n}\\n\\n.config-details[_ngcontent-%COMP%] {\\n  max-height: 0;\\n  overflow: hidden;\\n  transition: max-height 0.3s ease;\\n}\\n.config-details.expanded[_ngcontent-%COMP%] {\\n  max-height: 500px;\\n}\\n\\n.prompt-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.prompt-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin: 0 0 24px 0;\\n}\\n.prompt-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.prompt-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #374151;\\n  margin-bottom: 8px;\\n}\\n.prompt-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  line-height: 1.5;\\n}\\n.prompt-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value.description-text[_ngcontent-%COMP%] {\\n  font-weight: 400;\\n  color: #6b7280;\\n  line-height: 1.6;\\n}\\n.prompt-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n  padding-bottom: 24px;\\n  border-bottom: 1px solid #e5e7eb;\\n}\\n.prompt-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10rem;\\n}\\n.prompt-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  color: #6b7280;\\n  margin-bottom: 4px;\\n  font-weight: 400;\\n}\\n.prompt-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #1a1d29;\\n  font-size: 0.875rem;\\n}\\n.prompt-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.prompt-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #374151;\\n  margin-bottom: 8px;\\n}\\n.prompt-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]   .required[_ngcontent-%COMP%] {\\n  color: #ef4444;\\n  margin-left: 2px;\\n}\\n.prompt-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .prompt-content[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  background: #f8f9fa;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  color: #1a1d29;\\n  font-size: 0.875rem;\\n  line-height: 1.6;\\n  min-height: 120px;\\n  white-space: pre-wrap;\\n}\\n\\n.guardrail-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.guardrail-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin: 0 0 24px 0;\\n}\\n.guardrail-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.guardrail-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin-bottom: 8px;\\n}\\n.guardrail-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 400;\\n  color: #1a1d29;\\n  line-height: 1.5;\\n}\\n.guardrail-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value.description-text[_ngcontent-%COMP%] {\\n  font-weight: 400;\\n  color: #6b7280;\\n  line-height: 1.6;\\n}\\n.guardrail-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n  padding-bottom: 24px;\\n  border-bottom: 1px solid #e5e7eb;\\n}\\n.guardrail-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10rem;\\n}\\n.guardrail-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  color: #1a1d29;\\n  margin-bottom: 4px;\\n  font-weight: 600;\\n}\\n.guardrail-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #1a1d29;\\n  font-size: 0.875rem;\\n}\\n.guardrail-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.guardrail-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin-bottom: 8px;\\n}\\n.guardrail-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .code-content[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  background: #f8f9fa;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  color: #1a1d29;\\n  font-family: \\\"SF Mono\\\", Monaco, \\\"Cascadia Code\\\", monospace;\\n  font-size: 0.875rem;\\n  line-height: 1.6;\\n  min-height: 200px;\\n  white-space: pre-wrap;\\n  overflow-y: auto;\\n  max-height: 400px;\\n}\\n\\n.knowledge-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin: 0 0 24px 0;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin-bottom: 8px;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  line-height: 1.5;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value.description-text[_ngcontent-%COMP%] {\\n  font-weight: 400;\\n  color: #6b7280;\\n  line-height: 1.6;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n  padding-bottom: 24px;\\n  border-bottom: 1px solid #e5e7eb;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10rem;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  color: #1a1d29;\\n  margin-bottom: 4px;\\n  font-weight: 600;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #1a1d29;\\n  font-size: 0.875rem;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin-bottom: 8px;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .dropdown-display[_ngcontent-%COMP%], \\n.knowledge-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .input-display[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 12px 16px;\\n  background: #f8f9fa;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  color: #1a1d29;\\n  min-height: 48px;\\n  box-sizing: border-box;\\n  font-size: 0.875rem;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .dropdown-display[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  width: 0;\\n  height: 0;\\n  border-left: 6px solid transparent;\\n  border-right: 6px solid transparent;\\n  border-top: 8px solid #6b7280;\\n  margin-left: 8px;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .slider-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .slider-container[_ngcontent-%COMP%]   .slider-track[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 6px;\\n  background: #e5e7eb;\\n  border-radius: 3px;\\n  position: relative;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .slider-container[_ngcontent-%COMP%]   .slider-track[_ngcontent-%COMP%]   .slider-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: #3b82f6;\\n  border-radius: 3px;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .slider-container[_ngcontent-%COMP%]   .slider-value[_ngcontent-%COMP%] {\\n  min-width: 60px;\\n  padding: 8px 12px;\\n  background: #f8f9fa;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 6px;\\n  text-align: center;\\n  font-size: 0.875rem;\\n  color: #374151;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .files-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 8px 12px;\\n  background: #f8f9fa;\\n  border-radius: 6px;\\n  margin-bottom: 8px;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .files-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-name[_ngcontent-%COMP%] {\\n  color: #3b82f6;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n}\\n\\n.tool-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin: 0 0 24px 0;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin-bottom: 8px;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  line-height: 1.5;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value.description-text[_ngcontent-%COMP%] {\\n  font-weight: 400;\\n  color: #6b7280;\\n  line-height: 1.6;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n  padding-bottom: 24px;\\n  border-bottom: 1px solid #e5e7eb;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10rem;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  color: #1a1d29;\\n  margin-bottom: 4px;\\n  font-weight: 600;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #1a1d29;\\n  font-size: 0.875rem;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin-bottom: 8px;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .input-display[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 12px 16px;\\n  background: #f8f9fa;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  color: #1a1d29;\\n  min-height: 48px;\\n  box-sizing: border-box;\\n  font-size: 0.875rem;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .code-content[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  background: #f8f9fa;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  color: #1a1d29;\\n  font-family: \\\"SF Mono\\\", Monaco, \\\"Cascadia Code\\\", monospace;\\n  font-size: 0.875rem;\\n  line-height: 1.6;\\n  min-height: 200px;\\n  white-space: pre-wrap;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .code-content[_ngcontent-%COMP%]:empty::before {\\n  content: \\\"No configuration available\\\";\\n  color: #9ca3af;\\n  font-style: italic;\\n}\\n\\n[panel-footer][_ngcontent-%COMP%] {\\n  position: sticky;\\n  bottom: 0;\\n  background: white;\\n  padding: 16px;\\n  border-top: 1px solid #f0f1f2;\\n  margin-top: auto;\\n}\\n\\n.code-content[_ngcontent-%COMP%] {\\n  overflow-y: auto;\\n  overflow-x: hidden;\\n  scrollbar-width: none; \\n\\n  -ms-overflow-style: none; \\n\\n}\\n.code-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none; \\n\\n}\\n\\n.config-field[_ngcontent-%COMP%]     ava-textbox .textbox-container {\\n  box-shadow: none !important;\\n}\\n.config-field[_ngcontent-%COMP%]     ava-textbox .textbox-container input {\\n  box-shadow: none !important;\\n}\\n\\n  app-preview-panel .preview-panel > .preview-header {\\n  padding: 24px !important;\\n}\\n\\n  app-preview-panel .preview-panel > .preview-content {\\n  padding: 0 24px !important;\\n}\\n\\n.disabled-slider[_ngcontent-%COMP%] {\\n  pointer-events: none;\\n  opacity: 0.6;\\n}\\n.disabled-slider[_ngcontent-%COMP%]     .slider-container {\\n  cursor: not-allowed;\\n}\\n.disabled-slider[_ngcontent-%COMP%]     .slider-container .slider-thumb {\\n  cursor: not-allowed;\\n}\\n\\n  app-loader {\\n  visibility: hidden !important;\\n  pointer-events: none !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3Byb2plY3RzL3NoYXJlZC9wYWdlcy9hZ2VudHMvYnVpbGQtYWdlbnRzL2FnZW50cy1wcmV2aWV3LXBhbmVsL2FnZW50cy1wcmV2aWV3LXBhbmVsLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUVBO0VBQ0UsZUFBQTtFQUNBLE1BQUE7RUFDQSxPQUFBO0VBQ0EsUUFBQTtFQUNBLFNBQUE7RUFDQSxhQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7QUFERjs7QUFJQTtFQUNFLGtCQUFBO0VBQ0EsTUFBQTtFQUNBLE9BQUE7RUFDQSxRQUFBO0VBQ0EsU0FBQTtFQUNBLDhCQUFBO0VBQ0Esa0NBQUE7VUFBQSwwQkFBQTtBQURGOztBQUlBO0VBQ0Usa0JBQUE7RUFDQSxhQUFBO0VBQ0EsWUFBQTtFQUNBLGdCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxtQkFBQTtFQUNBLDJDQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQ0FBQTtFQUNBLGFBQUE7RUFDQSxzQkFBQTtBQURGOztBQUlBO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsOEJBQUE7RUFDQSxXQUFBO0FBREY7O0FBSUU7RUFDRSxpQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLFNBQUE7QUFESjs7QUFJRTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0EsdUJBQUE7RUFDQSxZQUFBO0VBQ0EsY0FBQTtFQUNBLGVBQUE7RUFDQSxrQkFBQTtFQUNBLHlCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7QUFESjtBQUdJO0VBQ0UsbUJBQUE7RUFDQSxjQUFBO0FBRE47O0FBS0E7RUFDRSxrQkFBQTtFQUNBLE9BQUE7RUFDQSxnQkFBQTtFQUNBLGtCQUFBLEVBQUEsZ0NBQUE7RUFDQSw4QkFBQTtFQUNBLGlCQUFBO0VBQ0EsNkJBQUE7QUFGRjtBQUdFO0VBQ0UsVUFBQTtBQURKO0FBSUU7RUFDRSw4QkFBQTtFQUNBLGtCQUFBO0FBRko7QUFLRTtFQUNFLDhCQUFBO0VBQ0Esa0JBQUE7QUFISjtBQUtJO0VBQ0UsOEJBQUE7QUFITjs7QUFTQTtFQUNFLG1CQUFBO0FBTkY7QUFRRTtFQUNFLGNBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0FBTko7QUFTRTtFQUNFLG1CQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0FBUEo7QUFVRTtFQUNFLGtCQUFBO0VBQ0EsY0FBQTtFQUNBLGVBQUE7QUFSSjs7QUFhQTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxjQUFBO0VBQ0EsMERBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EsaUJBQUE7RUFDQSxxQkFBQTtFQUNBLGdCQUFBO0VBQ0Esa0JBQUE7RUFDQSxxQkFBQTtFQUNBLHdCQUFBO0FBVkY7QUFZRTtFQUNFLGFBQUE7QUFWSjtBQWFFO0VBQ0UscUNBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7QUFYSjs7QUFpQkU7RUFDRSxtQkFBQTtBQWRKO0FBZ0JJO0VBQ0Usa0JBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxrQkFBQTtBQWROO0FBa0JFO0VBQ0UsYUFBQTtFQUNBLFNBQUE7RUFDQSxtQkFBQTtBQWhCSjtBQWtCSTtFQUNFLE9BQUE7RUFDQSxnQkFBQTtFQUNBLFlBQUE7QUFoQk47QUFvQkU7RUFDRSxtQkFBQTtFQUNBLG9CQUFBO0VBQ0EsZ0NBQUE7QUFsQko7QUFvQkk7RUFDRSxhQUFBO0VBQ0EsVUFBQTtBQWxCTjtBQXFCUTtFQUNFLG1CQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7QUFuQlY7QUFzQlE7RUFDRSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxtQkFBQTtBQXBCVjs7QUE2QkU7RUFDRSxtQkFBQTtBQTFCSjtBQTRCSTtFQUNFLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7QUExQk47QUE4QkU7RUFDRSxtQkFBQTtBQTVCSjtBQThCSTtFQUNFLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7QUE1Qk47QUErQkk7RUFDRSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0FBN0JOO0FBK0JNO0VBQ0UsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EsZ0JBQUE7QUE3QlI7QUFrQ0U7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSw4QkFBQTtFQUNBLGtCQUFBO0VBQ0EsbUJBQUE7RUFDQSx5QkFBQTtFQUNBLGtCQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0VBQ0EsbUJBQUE7QUFoQ0o7O0FBcUNBO0VBQ0UsNEJBQUE7RUFDQSw2QkFBQTtFQUNBLG1CQUFBO0FBbENGO0FBb0NFO0VBQ0UsV0FBQTtBQWxDSjtBQW9DSTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0EsZ0JBQUE7RUFDQSxrQkFBQTtFQUNBLHlCQUFBO0FBbENOO0FBb0NNO0VBQ0UsMkJBQUE7QUFsQ1I7O0FBMENBO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLGtCQUFBO0FBdkNGO0FBeUNFO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSx5QkFBQTtFQUNBLDZCQUFBO0VBQ0Esa0JBQUE7RUFDQSxrQ0FBQTtFQUNBLG1CQUFBO0FBdkNKO0FBMENFO0VBQ0UsY0FBQTtFQUNBLG1CQUFBO0FBeENKOztBQTRDQTtFQUNFLGtCQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0FBekNGOztBQTRDQTtFQUNFO0lBQUssdUJBQUE7RUF4Q0w7RUF5Q0E7SUFBTyx5QkFBQTtFQXRDUDtBQUNGO0FBeUNBO0VBQ0U7SUFDRSxXQUFBO0lBQ0EsZ0JBQUE7RUF2Q0Y7RUEwQ0E7SUFDRSxrQkFBQTtFQXhDRjtBQUNGO0FBOENBO0VBQ0UsMEJBQUE7QUE1Q0Y7O0FBK0NBO0VBQ0UsMEJBQUE7QUE1Q0Y7O0FBK0NBO0VBQ0UsMEJBQUE7QUE1Q0Y7O0FBK0NBO0VBQ0UsMEJBQUE7QUE1Q0Y7O0FBK0NBO0VBQ0UsMEJBQUE7QUE1Q0Y7O0FBK0NBO0VBQ0UsWUFBQTtFQUNBLGdCQUFBO0VBQ0Esa0JBQUEsRUFBQSxnQ0FBQTtFQUNBLG9CQUFBO0VBQ0EsV0FBQTtFQUNBLHNCQUFBO0FBNUNGO0FBOENFO0VBQ0UsbUJBQUE7QUE1Q0o7QUE4Q0k7RUFDRSxrQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0FBNUNOO0FBZ0RFO0VBQ0UsYUFBQTtFQUNBLFNBQUE7RUFDQSxtQkFBQTtFQUNBLFdBQUE7QUE5Q0o7QUFnREk7RUFDRSxPQUFBO0VBQ0EsZ0JBQUE7RUFDQSxZQUFBO0FBOUNOO0FBZ0RNO0VBQ0UsV0FBQTtBQTlDUjtBQWdEUTtFQUNFLFdBQUE7QUE5Q1Y7QUFnRFU7RUFDRSxXQUFBO0VBQ0Esc0JBQUE7QUE5Q1o7QUFxREU7RUFDRSxtQkFBQTtFQUNBLFdBQUE7RUFDQSxzQkFBQTtBQW5ESjtBQXFESTtFQUNFLGNBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0FBbkROO0FBc0RJO0VBQ0Usa0JBQUE7RUFDQSxjQUFBO0VBQ0EsZUFBQTtBQXBETjtBQXVESTtFQUNFLFdBQUE7QUFyRE47QUF1RE07RUFDRSxXQUFBO0FBckRSO0FBdURRO0VBQ0UsV0FBQTtFQUNBLHNCQUFBO0FBckRWO0FBMkRFO0VBQ0UsbUJBQUE7QUF6REo7QUEyREk7RUFDRSxjQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxrQkFBQTtBQXpETjtBQTRESTtFQUNFLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EsZ0JBQUE7QUExRE47QUE0RE07RUFDRSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxnQkFBQTtBQTFEUjtBQStERTtFQUNFLG1CQUFBO0VBQ0Esb0JBQUE7RUFDQSxnQ0FBQTtBQTdESjtBQStESTtFQUNFLGFBQUE7RUFDQSxVQUFBO0FBN0ROO0FBZ0VRO0VBQ0UsY0FBQTtFQUNBLG1CQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7QUE5RFY7QUFpRVE7RUFDRSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxtQkFBQTtBQS9EVjtBQXFFRTtFQUNFLG1CQUFBO0FBbkVKO0FBcUVJO0VBQ0UsY0FBQTtFQUNBLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7QUFuRU47QUFzRUk7O0VBRUUsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsOEJBQUE7RUFDQSxrQkFBQTtFQUNBLG1CQUFBO0VBQ0EseUJBQUE7RUFDQSxrQkFBQTtFQUNBLGNBQUE7RUFDQSxnQkFBQTtFQUNBLHNCQUFBO0VBQ0EsbUJBQUE7QUFwRU47QUF3RU07RUFDRSxXQUFBO0VBQ0EsUUFBQTtFQUNBLFNBQUE7RUFDQSxrQ0FBQTtFQUNBLG1DQUFBO0VBQ0EsNkJBQUE7RUFDQSxnQkFBQTtBQXRFUjtBQXlFTTtFQUNFLGlCQUFBO0VBQ0EsZUFBQTtBQXZFUjtBQTZFTTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFNBQUE7QUEzRVI7QUE2RVE7RUFDRSxPQUFBO0VBQ0EsV0FBQTtFQUNBLG1CQUFBO0VBQ0Esa0JBQUE7RUFDQSxrQkFBQTtBQTNFVjtBQTZFVTtFQUNFLFlBQUE7RUFDQSxtQkFBQTtFQUNBLGtCQUFBO0FBM0VaO0FBK0VRO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxtQkFBQTtFQUNBLGtCQUFBO0VBQ0EsZUFBQTtFQUNBLHdDQUFBO0VBQ0EsWUFBQTtBQTdFVjtBQWdGUTtFQUNFLGVBQUE7RUFDQSxpQkFBQTtFQUNBLG1CQUFBO0VBQ0EseUJBQUE7RUFDQSxrQkFBQTtFQUNBLGtCQUFBO0VBQ0EsbUJBQUE7RUFDQSxjQUFBO0FBOUVWO0FBb0ZFO0VBQ0UsY0FBQTtBQWxGSjtBQW9GSTtFQUNFLHFCQUFBO0VBQ0EsZUFBQTtFQUNBLGVBQUE7RUFDQSxjQUFBO0VBQ0EsMkJBQUE7QUFsRk47QUFvRk07RUFDRSxjQUFBO0FBbEZSOztBQWlHQTtFQUNFLGNBQUE7QUE5RkY7QUFnR0U7RUFDRSxxQkFBQTtFQUNBLGVBQUE7RUFDQSxlQUFBO0VBQ0EsY0FBQTtFQUNBLDJCQUFBO0FBOUZKO0FBZ0dJO0VBQ0UsY0FBQTtBQTlGTjs7QUFtR0E7RUFDRSxhQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQ0FBQTtBQWhHRjtBQWtHRTtFQUNFLGlCQUFBO0FBaEdKOztBQXFHRTtFQUNFLG1CQUFBO0FBbEdKO0FBb0dJO0VBQ0Usa0JBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxrQkFBQTtBQWxHTjtBQXNHRTtFQUNFLG1CQUFBO0FBcEdKO0FBc0dJO0VBQ0UsY0FBQTtFQUNBLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7QUFwR047QUF1R0k7RUFDRSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0FBckdOO0FBdUdNO0VBQ0UsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EsZ0JBQUE7QUFyR1I7QUEwR0U7RUFDRSxtQkFBQTtFQUNBLG9CQUFBO0VBQ0EsZ0NBQUE7QUF4R0o7QUEwR0k7RUFDRSxhQUFBO0VBQ0EsVUFBQTtBQXhHTjtBQTJHUTtFQUNFLGNBQUE7RUFDQSxtQkFBQTtFQUNBLGNBQUE7RUFDQSxrQkFBQTtFQUNBLGdCQUFBO0FBekdWO0FBNEdRO0VBQ0UsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EsbUJBQUE7QUExR1Y7QUFnSEU7RUFDRSxtQkFBQTtBQTlHSjtBQWdISTtFQUNFLGNBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0FBOUdOO0FBZ0hNO0VBQ0UsY0FBQTtFQUNBLGdCQUFBO0FBOUdSO0FBa0hJO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EseUJBQUE7RUFDQSxrQkFBQTtFQUNBLGNBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EsaUJBQUE7RUFDQSxxQkFBQTtBQWhITjs7QUFzSEU7RUFDRSxtQkFBQTtBQW5ISjtBQXFISTtFQUNFLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7QUFuSE47QUF1SEU7RUFDRSxtQkFBQTtBQXJISjtBQXVISTtFQUNFLGNBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0FBckhOO0FBd0hJO0VBQ0UsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxnQkFBQTtBQXRITjtBQXdITTtFQUNFLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0FBdEhSO0FBMkhFO0VBQ0UsbUJBQUE7RUFDQSxvQkFBQTtFQUNBLGdDQUFBO0FBekhKO0FBMkhJO0VBQ0UsYUFBQTtFQUNBLFVBQUE7QUF6SE47QUE0SFE7RUFDRSxjQUFBO0VBQ0EsbUJBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtBQTFIVjtBQTZIUTtFQUNFLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLG1CQUFBO0FBM0hWO0FBaUlFO0VBQ0UsbUJBQUE7QUEvSEo7QUFpSUk7RUFDRSxjQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxrQkFBQTtBQS9ITjtBQWtJSTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxjQUFBO0VBQ0EsMERBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EsaUJBQUE7RUFDQSxxQkFBQTtFQUNBLGdCQUFBO0VBQ0EsaUJBQUE7QUFoSU47O0FBc0lFO0VBQ0UsbUJBQUE7QUFuSUo7QUFxSUk7RUFDRSxrQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0FBbklOO0FBdUlFO0VBQ0UsbUJBQUE7QUFySUo7QUF1SUk7RUFDRSxjQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxrQkFBQTtBQXJJTjtBQXdJSTtFQUNFLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EsZ0JBQUE7QUF0SU47QUF3SU07RUFDRSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxnQkFBQTtBQXRJUjtBQTJJRTtFQUNFLG1CQUFBO0VBQ0Esb0JBQUE7RUFDQSxnQ0FBQTtBQXpJSjtBQTJJSTtFQUNFLGFBQUE7RUFDQSxVQUFBO0FBeklOO0FBNElRO0VBQ0UsY0FBQTtFQUNBLG1CQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7QUExSVY7QUE2SVE7RUFDRSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxtQkFBQTtBQTNJVjtBQWlKRTtFQUNFLG1CQUFBO0FBL0lKO0FBaUpJO0VBQ0UsY0FBQTtFQUNBLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7QUEvSU47QUFrSkk7O0VBRUUsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsOEJBQUE7RUFDQSxrQkFBQTtFQUNBLG1CQUFBO0VBQ0EseUJBQUE7RUFDQSxrQkFBQTtFQUNBLGNBQUE7RUFDQSxnQkFBQTtFQUNBLHNCQUFBO0VBQ0EsbUJBQUE7QUFoSk47QUFvSk07RUFDRSxXQUFBO0VBQ0EsUUFBQTtFQUNBLFNBQUE7RUFDQSxrQ0FBQTtFQUNBLG1DQUFBO0VBQ0EsNkJBQUE7RUFDQSxnQkFBQTtBQWxKUjtBQXNKSTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFNBQUE7QUFwSk47QUFzSk07RUFDRSxPQUFBO0VBQ0EsV0FBQTtFQUNBLG1CQUFBO0VBQ0Esa0JBQUE7RUFDQSxrQkFBQTtBQXBKUjtBQXNKUTtFQUNFLFlBQUE7RUFDQSxtQkFBQTtFQUNBLGtCQUFBO0FBcEpWO0FBd0pNO0VBQ0UsZUFBQTtFQUNBLGlCQUFBO0VBQ0EsbUJBQUE7RUFDQSx5QkFBQTtFQUNBLGtCQUFBO0VBQ0Esa0JBQUE7RUFDQSxtQkFBQTtFQUNBLGNBQUE7QUF0SlI7QUE0Skk7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxRQUFBO0VBQ0EsaUJBQUE7RUFDQSxtQkFBQTtFQUNBLGtCQUFBO0VBQ0Esa0JBQUE7QUExSk47QUE0Sk07RUFDRSxjQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtBQTFKUjs7QUFpS0U7RUFDRSxtQkFBQTtBQTlKSjtBQWdLSTtFQUNFLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7QUE5Sk47QUFrS0U7RUFDRSxtQkFBQTtBQWhLSjtBQWtLSTtFQUNFLGNBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0FBaEtOO0FBbUtJO0VBQ0UsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxnQkFBQTtBQWpLTjtBQW1LTTtFQUNFLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0FBaktSO0FBc0tFO0VBQ0UsbUJBQUE7RUFDQSxvQkFBQTtFQUNBLGdDQUFBO0FBcEtKO0FBc0tJO0VBQ0UsYUFBQTtFQUNBLFVBQUE7QUFwS047QUF1S1E7RUFDRSxjQUFBO0VBQ0EsbUJBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtBQXJLVjtBQXdLUTtFQUNFLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLG1CQUFBO0FBdEtWO0FBNEtFO0VBQ0UsbUJBQUE7QUExS0o7QUE0S0k7RUFDRSxjQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxrQkFBQTtBQTFLTjtBQTZLSTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLDhCQUFBO0VBQ0Esa0JBQUE7RUFDQSxtQkFBQTtFQUNBLHlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxjQUFBO0VBQ0EsZ0JBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQUFBO0FBM0tOO0FBOEtJO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EseUJBQUE7RUFDQSxrQkFBQTtFQUNBLGNBQUE7RUFDQSwwREFBQTtFQUNBLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSxpQkFBQTtFQUNBLHFCQUFBO0FBNUtOO0FBK0tNO0VBQ0UscUNBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7QUE3S1I7O0FBb0xBO0VBQ0UsZ0JBQUE7RUFDQSxTQUFBO0VBQ0EsaUJBQUE7RUFDQSxhQUFBO0VBQ0EsNkJBQUE7RUFDQSxnQkFBQTtBQWpMRjs7QUFvTEE7RUFFRSxnQkFBQTtFQUNBLGtCQUFBO0VBQ0EscUJBQUEsRUFBQSxZQUFBO0VBQ0Esd0JBQUEsRUFBQSwwQkFBQTtBQWxMRjtBQW9MRTtFQUNFLGFBQUEsRUFBQSx5QkFBQTtBQWxMSjs7QUF5TEk7RUFDRSwyQkFBQTtBQXRMTjtBQXdMTTtFQUNFLDJCQUFBO0FBdExSOztBQTJMQTtFQUNFLHdCQUFBO0FBeExGOztBQTJMQTtFQUNFLDBCQUFBO0FBeExGOztBQTJMQTtFQUNFLG9CQUFBO0VBQ0EsWUFBQTtBQXhMRjtBQTBMRTtFQUNFLG1CQUFBO0FBeExKO0FBMExJO0VBQ0UsbUJBQUE7QUF4TE47O0FBNExBO0VBQ0UsNkJBQUE7RUFDQSwrQkFBQTtBQXpMRiIsInNvdXJjZXNDb250ZW50IjpbIlxyXG4vLyBNYWluIHBhbmVsIHN0cnVjdHVyZVxyXG4ucHJldmlldy1wYW5lbCB7XHJcbiAgcG9zaXRpb246IGZpeGVkO1xyXG4gIHRvcDogMDtcclxuICBsZWZ0OiAwO1xyXG4gIHJpZ2h0OiAwO1xyXG4gIGJvdHRvbTogMDtcclxuICB6LWluZGV4OiAxMDAwO1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxufVxyXG5cclxuLmJhY2tkcm9wIHtcclxuICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgdG9wOiAwO1xyXG4gIGxlZnQ6IDA7XHJcbiAgcmlnaHQ6IDA7XHJcbiAgYm90dG9tOiAwO1xyXG4gIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC41KTtcclxuICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoNHB4KTtcclxufVxyXG5cclxuLnBhbmVsLWNvbnRhaW5lciB7XHJcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gIHotaW5kZXg6IDEwMDA7XHJcbiAgd2lkdGg6IDQ4MHB4O1xyXG4gIG1heC1oZWlnaHQ6IDkwdmg7XHJcbiAgYmFja2dyb3VuZDogI2ZmZjtcclxuICBib3JkZXItcmFkaXVzOiAxMnB4O1xyXG4gIGJveC1zaGFkb3c6IDAgMjBweCA0MHB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7XHJcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuICBhbmltYXRpb246IHNsaWRlSW4gMC4zcyBlYXNlLW91dDtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbn1cclxuXHJcbi5wcmV2aWV3LWhlYWRlciB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcclxuICB3aWR0aDogMTAwJTtcclxufVxyXG5cclxuICAucGFuZWwtdGl0bGUge1xyXG4gICAgZm9udC1zaXplOiAxLjVyZW07XHJcbiAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgY29sb3I6ICMxYTFkMjk7XHJcbiAgICBtYXJnaW46IDA7XHJcbiAgfVxyXG4gIFxyXG4gIC5jbG9zZS1idG4ge1xyXG4gICAgd2lkdGg6IDMycHg7XHJcbiAgICBoZWlnaHQ6IDMycHg7XHJcbiAgICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDtcclxuICAgIGJvcmRlcjogbm9uZTtcclxuICAgIGNvbG9yOiAjNmI3MjgwO1xyXG4gICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG4gICAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZTtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcblxyXG4gICAgJjpob3ZlciB7XHJcbiAgICAgIGJhY2tncm91bmQ6ICNmM2Y0ZjY7XHJcbiAgICAgIGNvbG9yOiAjMzc0MTUxO1xyXG4gICAgfVxyXG4gIH1cclxuLy8gQ29udGVudCBhcmVhXHJcbi5wcmV2aWV3LWNvbnRlbnQge1xyXG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuICBmbGV4OiAxO1xyXG4gIG92ZXJmbG93LXk6IGF1dG87XHJcbiAgb3ZlcmZsb3cteDogaGlkZGVuOyAvKiBQcmV2ZW50IGhvcml6b250YWwgb3ZlcmZsb3cgKi9cclxuICBtYXgtaGVpZ2h0OiBjYWxjKDkwdmggLSAxNDBweCk7ICBcclxuICBwYWRkaW5nLXRvcDo0MHB4O1xyXG4gIGJvcmRlci10b3A6IDFweCBzb2xpZCAjZTVlN2ViO1xyXG4gICY6Oi13ZWJraXQtc2Nyb2xsYmFyIHtcclxuICAgIHdpZHRoOiA2cHg7XHJcbiAgfVxyXG5cclxuICAmOjotd2Via2l0LXNjcm9sbGJhci10cmFjayB7XHJcbiAgICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuMSk7XHJcbiAgICBib3JkZXItcmFkaXVzOiAzcHg7XHJcbiAgfVxyXG5cclxuICAmOjotd2Via2l0LXNjcm9sbGJhci10aHVtYiB7XHJcbiAgICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuMyk7XHJcbiAgICBib3JkZXItcmFkaXVzOiAzcHg7XHJcblxyXG4gICAgJjpob3ZlciB7XHJcbiAgICAgIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC41KTtcclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi8vIENvbW1vbiBmaWVsZCBzdHlsaW5nXHJcbi5jb25maWctZmllbGQge1xyXG4gIG1hcmdpbi1ib3R0b206IDIwcHg7XHJcbiAgXHJcbiAgbGFiZWwge1xyXG4gICAgZGlzcGxheTogYmxvY2s7XHJcbiAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgIGNvbG9yOiAjMWExZDI5O1xyXG4gICAgbWFyZ2luLWJvdHRvbTogOHB4O1xyXG4gIH1cclxuICBcclxuICAuZmllbGQtdmFsdWUge1xyXG4gICAgZm9udC1zaXplOiAwLjg3NXJlbTtcclxuICAgIGNvbG9yOiAjMWExZDI5O1xyXG4gICAgbGluZS1oZWlnaHQ6IDEuNTtcclxuICB9XHJcbiAgXHJcbiAgLmZpZWxkLWhpbnQge1xyXG4gICAgZm9udC1zaXplOiAwLjc1cmVtO1xyXG4gICAgY29sb3I6ICM2YjcyODA7XHJcbiAgICBtYXJnaW4tdG9wOiA0cHg7XHJcbiAgfVxyXG59XHJcblxyXG4vLyBDb2RlIGNvbnRlbnQgc3R5bGluZ1xyXG4uY29kZS1jb250ZW50LCAucHJvbXB0LWNvbnRlbnQge1xyXG4gIHBhZGRpbmc6IDE2cHg7XHJcbiAgYmFja2dyb3VuZDogI2Y4ZjlmYTtcclxuICBib3JkZXI6IDFweCBzb2xpZCAjZTVlN2ViO1xyXG4gIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICBjb2xvcjogIzFhMWQyOTtcclxuICBmb250LWZhbWlseTogJ1NGIE1vbm8nLCBNb25hY28sICdDYXNjYWRpYSBDb2RlJywgbW9ub3NwYWNlO1xyXG4gIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgbGluZS1oZWlnaHQ6IDEuNjtcclxuICBtaW4taGVpZ2h0OiAxMjBweDtcclxuICB3aGl0ZS1zcGFjZTogcHJlLXdyYXA7XHJcbiAgb3ZlcmZsb3cteTogYXV0bztcclxuICBvdmVyZmxvdy14OiBoaWRkZW47XHJcbiAgc2Nyb2xsYmFyLXdpZHRoOiBub25lO1xyXG4gIC1tcy1vdmVyZmxvdy1zdHlsZTogbm9uZTtcclxuICBcclxuICAmOjotd2Via2l0LXNjcm9sbGJhciB7XHJcbiAgICBkaXNwbGF5OiBub25lO1xyXG4gIH1cclxuICBcclxuICAmOmVtcHR5OjpiZWZvcmUge1xyXG4gICAgY29udGVudDogJ05vIGNvbmZpZ3VyYXRpb24gYXZhaWxhYmxlJztcclxuICAgIGNvbG9yOiAjOWNhM2FmO1xyXG4gICAgZm9udC1zdHlsZTogaXRhbGljO1xyXG4gIH1cclxufVxyXG5cclxuLy8gTW9kZWwgcHJldmlldyBzcGVjaWZpY1xyXG4ubW9kZWwtcHJldmlldyB7XHJcbiAgLm1vZGVsLXNlY3Rpb24ge1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMzJweDtcclxuICAgIFxyXG4gICAgaDMge1xyXG4gICAgICBmb250LXNpemU6IDEuMjVyZW07XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgIGNvbG9yOiAjMWExZDI5O1xyXG4gICAgICBtYXJnaW46IDAgMCAyNHB4IDA7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAuY29uZmlnLXJvdyB7XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgZ2FwOiAxMnB4O1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMjBweDtcclxuICAgIFxyXG4gICAgLmNvbmZpZy1maWVsZC5oYWxmLXdpZHRoIHtcclxuICAgICAgZmxleDogMTtcclxuICAgICAgbWFyZ2luLWJvdHRvbTogMDtcclxuICAgICAgbWluLXdpZHRoOiAwO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLm1vZGVsLW1ldGEge1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMzJweDtcclxuICAgIHBhZGRpbmctYm90dG9tOiAyNHB4O1xyXG4gICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlNWU3ZWI7XHJcbiAgICBcclxuICAgIC5tZXRhLXJvdyB7XHJcbiAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgIGdhcDogMTByZW07XHJcbiAgICAgIFxyXG4gICAgICAubWV0YS1pdGVtIHtcclxuICAgICAgICBsYWJlbCB7XHJcbiAgICAgICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICAgICAgY29sb3I6ICMxYTFkMjk7XHJcbiAgICAgICAgICBtYXJnaW4tYm90dG9tOiA0cHg7XHJcbiAgICAgICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICAgIH1cclxuICAgICAgICBcclxuICAgICAgICAuZmllbGQtdmFsdWUge1xyXG4gICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgICAgICAgIGNvbG9yOiAjMWExZDI5O1xyXG4gICAgICAgICAgZm9udC1zaXplOiAwLjg3NXJlbTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi8vIFRvb2wgYW5kIHByb21wdCBwcmV2aWV3XHJcbi50b29sLXByZXZpZXcsIC5wcm9tcHQtcHJldmlldyB7XHJcbiAgLm1vZGVsLXNlY3Rpb24ge1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMzJweDtcclxuICAgIFxyXG4gICAgaDMge1xyXG4gICAgICBmb250LXNpemU6IDEuMjVyZW07XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgIGNvbG9yOiAjMWExZDI5O1xyXG4gICAgICBtYXJnaW46IDAgMCAyNHB4IDA7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAubW9kZWwtZmllbGQge1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMjRweDtcclxuICAgIFxyXG4gICAgbGFiZWwge1xyXG4gICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICBjb2xvcjogIzFhMWQyOTtcclxuICAgICAgbWFyZ2luLWJvdHRvbTogOHB4O1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICAuZmllbGQtdmFsdWUge1xyXG4gICAgICBmb250LXNpemU6IDEuMTI1cmVtO1xyXG4gICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICBjb2xvcjogIzFhMWQyOTtcclxuICAgICAgbGluZS1oZWlnaHQ6IDEuNTtcclxuICAgICAgXHJcbiAgICAgICYuZGVzY3JpcHRpb24tdGV4dCB7XHJcbiAgICAgICAgZm9udC13ZWlnaHQ6IDQwMDtcclxuICAgICAgICBjb2xvcjogIzZiNzI4MDtcclxuICAgICAgICBsaW5lLWhlaWdodDogMS42O1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAuaW5wdXQtZGlzcGxheSB7XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcclxuICAgIHBhZGRpbmc6IDEycHggMTZweDtcclxuICAgIGJhY2tncm91bmQ6ICNmOGY5ZmE7XHJcbiAgICBib3JkZXI6IDFweCBzb2xpZCAjZTVlN2ViO1xyXG4gICAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG4gICAgY29sb3I6ICMxYTFkMjk7XHJcbiAgICBtaW4taGVpZ2h0OiA0OHB4O1xyXG4gICAgZm9udC1zaXplOiAwLjg3NXJlbTtcclxuICB9XHJcbn1cclxuXHJcbi8vIEZvb3RlclxyXG46Om5nLWRlZXAgLnByZXZpZXctcGFuZWwgW3BhbmVsLWZvb3Rlcl0ge1xyXG4gIHBhZGRpbmc6IDIwcHggMjRweCAyNHB4IDI0cHg7XHJcbiAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNmMGYxZjI7XHJcbiAgYmFja2dyb3VuZDogI2ZhZmJmYztcclxuICBcclxuICBhdmEtYnV0dG9uIHtcclxuICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgXHJcbiAgICBidXR0b24ge1xyXG4gICAgICB3aWR0aDogMTAwJTtcclxuICAgICAgaGVpZ2h0OiA0NHB4O1xyXG4gICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICBib3JkZXItcmFkaXVzOiA4cHg7XHJcbiAgICAgIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7XHJcbiAgICAgIFxyXG4gICAgICAmOmhvdmVyIHtcclxuICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTFweCk7XHJcbiAgICAgICAgLy8gYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDM3LCA5OSwgMjM1LCAwLjMpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4vLyBMb2FkaW5nIGFuZCBlcnJvciBzdGF0ZXNcclxuLnByZXZpZXctbG9hZGluZyB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgcGFkZGluZzogNDBweCAyMHB4O1xyXG4gIFxyXG4gIC5sb2FkaW5nLXNwaW5uZXIge1xyXG4gICAgd2lkdGg6IDMycHg7XHJcbiAgICBoZWlnaHQ6IDMycHg7XHJcbiAgICBib3JkZXI6IDNweCBzb2xpZCAjZjNmNGY2O1xyXG4gICAgYm9yZGVyLXRvcDogM3B4IHNvbGlkICMzYjgyZjY7XHJcbiAgICBib3JkZXItcmFkaXVzOiA1MCU7XHJcbiAgICBhbmltYXRpb246IHNwaW4gMXMgbGluZWFyIGluZmluaXRlO1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMTZweDtcclxuICB9XHJcbiAgXHJcbiAgcCB7XHJcbiAgICBjb2xvcjogIzZiNzI4MDtcclxuICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgfVxyXG59XHJcblxyXG4ucHJldmlldy1lcnJvciB7XHJcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG4gIGNvbG9yOiAjZGMyNjI2O1xyXG4gIHBhZGRpbmc6IDQwcHggMjBweDtcclxufVxyXG5cclxuQGtleWZyYW1lcyBzcGluIHtcclxuICAwJSB7IHRyYW5zZm9ybTogcm90YXRlKDBkZWcpOyB9XHJcbiAgMTAwJSB7IHRyYW5zZm9ybTogcm90YXRlKDM2MGRlZyk7IH1cclxufVxyXG5cclxuLy8gUmVzcG9uc2l2ZSBkZXNpZ25cclxuQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XHJcbiAgLnBhbmVsLWNvbnRhaW5lciB7XHJcbiAgICB3aWR0aDogOTV2dztcclxuICAgIG1heC13aWR0aDogNDAwcHg7XHJcbiAgfVxyXG4gIFxyXG4gIC5wYW5lbC10aXRsZSB7XHJcbiAgICBmb250LXNpemU6IDEuMjVyZW07XHJcbiAgfVxyXG4gIFxyXG59XHJcblxyXG5cclxuLy8gVHlwZS1zcGVjaWZpYyBzdHlsaW5nXHJcbi5tb2RlbC1wcmV2aWV3IC5wcmV2aWV3LWZpZWxkIHtcclxuICBib3JkZXItbGVmdC1jb2xvcjogIzEwYjk4MTtcclxufVxyXG5cclxuLnRvb2wtcHJldmlldyAucHJldmlldy1maWVsZCB7XHJcbiAgYm9yZGVyLWxlZnQtY29sb3I6ICNmNTllMGI7XHJcbn1cclxuXHJcbi5wcm9tcHQtcHJldmlldyAucHJldmlldy1maWVsZCB7XHJcbiAgYm9yZGVyLWxlZnQtY29sb3I6ICM4YjVjZjY7XHJcbn1cclxuXHJcbi5rbm93bGVkZ2UtcHJldmlldyAucHJldmlldy1maWVsZCB7XHJcbiAgYm9yZGVyLWxlZnQtY29sb3I6ICMwNmI2ZDQ7XHJcbn1cclxuXHJcbi5ndWFyZHJhaWwtcHJldmlldyAucHJldmlldy1maWVsZCB7XHJcbiAgYm9yZGVyLWxlZnQtY29sb3I6ICNlZjQ0NDQ7XHJcbn1cclxuXHJcbi5tb2RlbC1wcmV2aWV3IHtcclxuICBoZWlnaHQ6IDEwMCU7XHJcbiAgb3ZlcmZsb3cteTogYXV0bztcclxuICBvdmVyZmxvdy14OiBoaWRkZW47IC8qIFByZXZlbnQgaG9yaXpvbnRhbCBvdmVyZmxvdyAqL1xyXG4gIHBhZGRpbmctYm90dG9tOiAyMHB4O1xyXG4gIHdpZHRoOiAxMDAlO1xyXG4gIGJveC1zaXppbmc6IGJvcmRlci1ib3g7XHJcbiAgXHJcbiAgLm1vZGVsLXNlY3Rpb24ge1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMzJweDtcclxuICAgIFxyXG4gICAgaDMge1xyXG4gICAgICBmb250LXNpemU6IDEuMjVyZW07XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgIGNvbG9yOiAjMWExZDI5O1xyXG4gICAgICBtYXJnaW46IDAgMCAyNHB4IDA7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAuY29uZmlnLXJvdyB7XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgZ2FwOiAxMnB4O1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMjBweDtcclxuICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgXHJcbiAgICAuY29uZmlnLWZpZWxkLmhhbGYtd2lkdGgge1xyXG4gICAgICBmbGV4OiAxO1xyXG4gICAgICBtYXJnaW4tYm90dG9tOiAwO1xyXG4gICAgICBtaW4td2lkdGg6IDA7XHJcbiAgICAgIFxyXG4gICAgICA6Om5nLWRlZXAgYXZhLXRleHRib3gge1xyXG4gICAgICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgICAgIFxyXG4gICAgICAgIC50ZXh0Ym94LWNvbnRhaW5lciB7XHJcbiAgICAgICAgICB3aWR0aDogMTAwJTtcclxuICAgICAgICAgIFxyXG4gICAgICAgICAgaW5wdXQge1xyXG4gICAgICAgICAgICB3aWR0aDogMTAwJTtcclxuICAgICAgICAgICAgYm94LXNpemluZzogYm9yZGVyLWJveDtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcblxyXG4gIC5jb25maWctZmllbGQge1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMjBweDtcclxuICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgYm94LXNpemluZzogYm9yZGVyLWJveDtcclxuICAgIFxyXG4gICAgbGFiZWwge1xyXG4gICAgICBkaXNwbGF5OiBibG9jaztcclxuICAgICAgZm9udC1zaXplOiAwLjg3NXJlbTtcclxuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgICAgY29sb3I6ICMxYTFkMjk7XHJcbiAgICAgIG1hcmdpbi1ib3R0b206IDhweDtcclxuICAgIH1cclxuICAgIFxyXG4gICAgLmZpZWxkLWhpbnQge1xyXG4gICAgICBmb250LXNpemU6IDAuNzVyZW07XHJcbiAgICAgIGNvbG9yOiAjNmI3MjgwO1xyXG4gICAgICBtYXJnaW4tdG9wOiA0cHg7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIDo6bmctZGVlcCBhdmEtdGV4dGJveCB7XHJcbiAgICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgICBcclxuICAgICAgLnRleHRib3gtY29udGFpbmVyIHtcclxuICAgICAgICB3aWR0aDogMTAwJTtcclxuICAgICAgICBcclxuICAgICAgICBpbnB1dCB7XHJcbiAgICAgICAgICB3aWR0aDogMTAwJTtcclxuICAgICAgICAgIGJveC1zaXppbmc6IGJvcmRlci1ib3g7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAubW9kZWwtZmllbGQge1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMjRweDtcclxuICAgIFxyXG4gICAgbGFiZWwge1xyXG4gICAgICBkaXNwbGF5OiBibG9jaztcclxuICAgICAgZm9udC1zaXplOiAwLjg3NXJlbTtcclxuICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgICAgY29sb3I6ICMzNzQxNTE7XHJcbiAgICAgIG1hcmdpbi1ib3R0b206IDhweDtcclxuICAgIH1cclxuICAgIFxyXG4gICAgLmZpZWxkLXZhbHVlIHtcclxuICAgICAgZm9udC1zaXplOiAxLjEyNXJlbTtcclxuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgICAgY29sb3I6ICMxYTFkMjk7XHJcbiAgICAgIGxpbmUtaGVpZ2h0OiAxLjU7XHJcbiAgICAgIFxyXG4gICAgICAmLmRlc2NyaXB0aW9uLXRleHQge1xyXG4gICAgICAgIGZvbnQtd2VpZ2h0OiA0MDA7XHJcbiAgICAgICAgY29sb3I6ICM2YjcyODA7XHJcbiAgICAgICAgbGluZS1oZWlnaHQ6IDEuNjtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLm1vZGVsLW1ldGEge1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMzJweDtcclxuICAgIHBhZGRpbmctYm90dG9tOiAyNHB4O1xyXG4gICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlNWU3ZWI7XHJcbiAgICBcclxuICAgIC5tZXRhLXJvdyB7XHJcbiAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgIGdhcDogMTByZW07XHJcbiAgICAgIFxyXG4gICAgICAubWV0YS1pdGVtIHtcclxuICAgICAgICBsYWJlbCB7XHJcbiAgICAgICAgICBkaXNwbGF5OiBibG9jaztcclxuICAgICAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgICAgICBjb2xvcjogIzZiNzI4MDtcclxuICAgICAgICAgIG1hcmdpbi1ib3R0b206IDRweDtcclxuICAgICAgICAgIGZvbnQtd2VpZ2h0OiA0MDA7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIFxyXG4gICAgICAgIC5maWVsZC12YWx1ZSB7XHJcbiAgICAgICAgICBmb250LXdlaWdodDogNTAwO1xyXG4gICAgICAgICAgY29sb3I6ICMxYTFkMjk7XHJcbiAgICAgICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLmNvbmZpZy1maWVsZCB7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAyMHB4O1xyXG4gICAgXHJcbiAgICBsYWJlbCB7XHJcbiAgICAgIGRpc3BsYXk6IGJsb2NrO1xyXG4gICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICBmb250LXdlaWdodDogNTAwO1xyXG4gICAgICBjb2xvcjogIzM3NDE1MTtcclxuICAgICAgbWFyZ2luLWJvdHRvbTogOHB4O1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICAuZHJvcGRvd24tZGlzcGxheSxcclxuICAgIC5pbnB1dC1kaXNwbGF5IHtcclxuICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xyXG4gICAgICBwYWRkaW5nOiAxMnB4IDE2cHg7XHJcbiAgICAgIGJhY2tncm91bmQ6ICNmOGY5ZmE7XHJcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNlNWU3ZWI7XHJcbiAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICAgICAgY29sb3I6ICMxYTFkMjk7XHJcbiAgICAgIG1pbi1oZWlnaHQ6IDQ4cHg7XHJcbiAgICAgIGJveC1zaXppbmc6IGJvcmRlci1ib3g7XHJcbiAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICB9XHJcbiAgICBcclxuICAgIC5kcm9wZG93bi1kaXNwbGF5IHtcclxuICAgICAgJjo6YWZ0ZXIge1xyXG4gICAgICAgIGNvbnRlbnQ6ICcnO1xyXG4gICAgICAgIHdpZHRoOiAwO1xyXG4gICAgICAgIGhlaWdodDogMDtcclxuICAgICAgICBib3JkZXItbGVmdDogNnB4IHNvbGlkIHRyYW5zcGFyZW50O1xyXG4gICAgICAgIGJvcmRlci1yaWdodDogNnB4IHNvbGlkIHRyYW5zcGFyZW50O1xyXG4gICAgICAgIGJvcmRlci10b3A6IDhweCBzb2xpZCAjNmI3MjgwO1xyXG4gICAgICAgIG1hcmdpbi1sZWZ0OiA4cHg7XHJcbiAgICAgIH1cclxuICAgICAgXHJcbiAgICAgIC5lbmdpbmUtaWNvbiB7XHJcbiAgICAgICAgbWFyZ2luLXJpZ2h0OiA4cHg7XHJcbiAgICAgICAgZm9udC1zaXplOiAxcmVtO1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLy8gVGVtcGVyYXR1cmUgc2xpZGVyIHN0eWxpbmdcclxuICAgIDo6bmctZGVlcCBhdmEtc2xpZGVyIHtcclxuICAgICAgLnNsaWRlci1jb250YWluZXIge1xyXG4gICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgICBnYXA6IDE2cHg7XHJcbiAgICAgICAgXHJcbiAgICAgICAgLnNsaWRlci10cmFjayB7XHJcbiAgICAgICAgICBmbGV4OiAxO1xyXG4gICAgICAgICAgaGVpZ2h0OiA2cHg7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kOiAjZTVlN2ViO1xyXG4gICAgICAgICAgYm9yZGVyLXJhZGl1czogM3B4O1xyXG4gICAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gICAgICAgICAgXHJcbiAgICAgICAgICAuc2xpZGVyLWZpbGwge1xyXG4gICAgICAgICAgICBoZWlnaHQ6IDEwMCU7XHJcbiAgICAgICAgICAgIGJhY2tncm91bmQ6ICMzYjgyZjY7XHJcbiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDNweDtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgXHJcbiAgICAgICAgLnNsaWRlci10aHVtYiB7XHJcbiAgICAgICAgICB3aWR0aDogMjBweDtcclxuICAgICAgICAgIGhlaWdodDogMjBweDtcclxuICAgICAgICAgIGJhY2tncm91bmQ6ICMzYjgyZjY7XHJcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiA1MCU7XHJcbiAgICAgICAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICAgICAgICBib3gtc2hhZG93OiAwIDJweCA0cHggcmdiYSgwLCAwLCAwLCAwLjIpO1xyXG4gICAgICAgICAgYm9yZGVyOiBub25lO1xyXG4gICAgICAgIH1cclxuICAgICAgICBcclxuICAgICAgICAuc2xpZGVyLXZhbHVlIHtcclxuICAgICAgICAgIG1pbi13aWR0aDogNjBweDtcclxuICAgICAgICAgIHBhZGRpbmc6IDhweCAxMnB4O1xyXG4gICAgICAgICAgYmFja2dyb3VuZDogI2Y4ZjlmYTtcclxuICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNlNWU3ZWI7XHJcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiA2cHg7XHJcbiAgICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcbiAgICAgICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICAgICAgY29sb3I6ICMzNzQxNTE7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAuY29uZmlnLXRvZ2dsZSB7XHJcbiAgICBtYXJnaW46IDE2cHggMDtcclxuICAgIFxyXG4gICAgLnRvZ2dsZS10ZXh0IHtcclxuICAgICAgZGlzcGxheTogaW5saW5lLWJsb2NrO1xyXG4gICAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICAgIGZvbnQtc2l6ZTogMTRweDtcclxuICAgICAgY29sb3I6ICMwMDdiZmY7XHJcbiAgICAgIHRyYW5zaXRpb246IGNvbG9yIDAuMnMgZWFzZTtcclxuXHJcbiAgICAgICY6aG92ZXIge1xyXG4gICAgICAgIGNvbG9yOiAjMDA1NmIzO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvLyAuY29uZmlnLWRldGFpbHMge1xyXG4gIC8vICAgbWF4LWhlaWdodDogbm9uZTsgLy8gUmVtb3ZlIGhlaWdodCByZXN0cmljdGlvblxyXG4gIC8vICAgb3ZlcmZsb3c6IHZpc2libGU7XHJcbiAgLy8gICB0cmFuc2l0aW9uOiBtYXgtaGVpZ2h0IDAuM3MgZWFzZTtcclxuXHJcbiAgLy8gICAmLmV4cGFuZGVkIHtcclxuICAvLyAgICAgbWF4LWhlaWdodDogbm9uZTsgLy8gUmVtb3ZlIGhlaWdodCByZXN0cmljdGlvbiB3aGVuIGV4cGFuZGVkXHJcbiAgLy8gICB9XHJcbiAgLy8gfVxyXG59XHJcbi5jb25maWctdG9nZ2xlIHtcclxuICBtYXJnaW46IDE2cHggMDtcclxuICBcclxuICAudG9nZ2xlLXRleHQge1xyXG4gICAgZGlzcGxheTogaW5saW5lLWJsb2NrO1xyXG4gICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICAgZm9udC1zaXplOiAxNHB4O1xyXG4gICAgY29sb3I6ICMwMDdiZmY7XHJcbiAgICB0cmFuc2l0aW9uOiBjb2xvciAwLjJzIGVhc2U7XHJcblxyXG4gICAgJjpob3ZlciB7XHJcbiAgICAgIGNvbG9yOiAjMDA1NmIzO1xyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLmNvbmZpZy1kZXRhaWxzIHtcclxuICBtYXgtaGVpZ2h0OiAwO1xyXG4gIG92ZXJmbG93OiBoaWRkZW47XHJcbiAgdHJhbnNpdGlvbjogbWF4LWhlaWdodCAwLjNzIGVhc2U7XHJcblxyXG4gICYuZXhwYW5kZWQge1xyXG4gICAgbWF4LWhlaWdodDogNTAwcHg7XHJcbiAgfVxyXG59XHJcblxyXG4ucHJvbXB0LXByZXZpZXcge1xyXG4gIC5tb2RlbC1zZWN0aW9uIHtcclxuICAgIG1hcmdpbi1ib3R0b206IDMycHg7XHJcbiAgICBcclxuICAgIGgzIHtcclxuICAgICAgZm9udC1zaXplOiAxLjI1cmVtO1xyXG4gICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICBjb2xvcjogIzFhMWQyOTtcclxuICAgICAgbWFyZ2luOiAwIDAgMjRweCAwO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLm1vZGVsLWZpZWxkIHtcclxuICAgIG1hcmdpbi1ib3R0b206IDI0cHg7XHJcbiAgICBcclxuICAgIGxhYmVsIHtcclxuICAgICAgZGlzcGxheTogYmxvY2s7XHJcbiAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICAgIGNvbG9yOiAjMzc0MTUxO1xyXG4gICAgICBtYXJnaW4tYm90dG9tOiA4cHg7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIC5maWVsZC12YWx1ZSB7XHJcbiAgICAgIGZvbnQtc2l6ZTogMS4xMjVyZW07XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgIGNvbG9yOiAjMWExZDI5O1xyXG4gICAgICBsaW5lLWhlaWdodDogMS41O1xyXG4gICAgICBcclxuICAgICAgJi5kZXNjcmlwdGlvbi10ZXh0IHtcclxuICAgICAgICBmb250LXdlaWdodDogNDAwO1xyXG4gICAgICAgIGNvbG9yOiAjNmI3MjgwO1xyXG4gICAgICAgIGxpbmUtaGVpZ2h0OiAxLjY7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcblxyXG4gIC5tb2RlbC1tZXRhIHtcclxuICAgIG1hcmdpbi1ib3R0b206IDMycHg7XHJcbiAgICBwYWRkaW5nLWJvdHRvbTogMjRweDtcclxuICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTVlN2ViO1xyXG4gICAgXHJcbiAgICAubWV0YS1yb3cge1xyXG4gICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICBnYXA6IDEwcmVtO1xyXG4gICAgICBcclxuICAgICAgLm1ldGEtaXRlbSB7XHJcbiAgICAgICAgbGFiZWwge1xyXG4gICAgICAgICAgZGlzcGxheTogYmxvY2s7XHJcbiAgICAgICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICAgICAgY29sb3I6ICM2YjcyODA7XHJcbiAgICAgICAgICBtYXJnaW4tYm90dG9tOiA0cHg7XHJcbiAgICAgICAgICBmb250LXdlaWdodDogNDAwO1xyXG4gICAgICAgIH1cclxuICAgICAgICBcclxuICAgICAgICAuZmllbGQtdmFsdWUge1xyXG4gICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgICAgICAgIGNvbG9yOiAjMWExZDI5O1xyXG4gICAgICAgICAgZm9udC1zaXplOiAwLjg3NXJlbTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcblxyXG4gIC5jb25maWctZmllbGQge1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMjBweDtcclxuICAgIFxyXG4gICAgbGFiZWwge1xyXG4gICAgICBkaXNwbGF5OiBibG9jaztcclxuICAgICAgZm9udC1zaXplOiAwLjg3NXJlbTtcclxuICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgICAgY29sb3I6ICMzNzQxNTE7XHJcbiAgICAgIG1hcmdpbi1ib3R0b206IDhweDtcclxuICAgICAgXHJcbiAgICAgIC5yZXF1aXJlZCB7XHJcbiAgICAgICAgY29sb3I6ICNlZjQ0NDQ7XHJcbiAgICAgICAgbWFyZ2luLWxlZnQ6IDJweDtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gICAgXHJcbiAgICAucHJvbXB0LWNvbnRlbnQge1xyXG4gICAgICBwYWRkaW5nOiAxNnB4O1xyXG4gICAgICBiYWNrZ3JvdW5kOiAjZjhmOWZhO1xyXG4gICAgICBib3JkZXI6IDFweCBzb2xpZCAjZTVlN2ViO1xyXG4gICAgICBib3JkZXItcmFkaXVzOiA4cHg7XHJcbiAgICAgIGNvbG9yOiAjMWExZDI5O1xyXG4gICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICBsaW5lLWhlaWdodDogMS42O1xyXG4gICAgICBtaW4taGVpZ2h0OiAxMjBweDtcclxuICAgICAgd2hpdGUtc3BhY2U6IHByZS13cmFwO1xyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLmd1YXJkcmFpbC1wcmV2aWV3IHtcclxuICAubW9kZWwtc2VjdGlvbiB7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAzMnB4O1xyXG4gICAgXHJcbiAgICBoMyB7XHJcbiAgICAgIGZvbnQtc2l6ZTogMS4yNXJlbTtcclxuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgICAgY29sb3I6ICMxYTFkMjk7XHJcbiAgICAgIG1hcmdpbjogMCAwIDI0cHggMDtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC5tb2RlbC1maWVsZCB7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAyNHB4O1xyXG4gICAgXHJcbiAgICBsYWJlbCB7XHJcbiAgICAgIGRpc3BsYXk6IGJsb2NrO1xyXG4gICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICBjb2xvcjogIzFhMWQyOTtcclxuICAgICAgbWFyZ2luLWJvdHRvbTogOHB4O1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICAuZmllbGQtdmFsdWUge1xyXG4gICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICBmb250LXdlaWdodDogNDAwO1xyXG4gICAgICBjb2xvcjogIzFhMWQyOTtcclxuICAgICAgbGluZS1oZWlnaHQ6IDEuNTtcclxuICAgICAgXHJcbiAgICAgICYuZGVzY3JpcHRpb24tdGV4dCB7XHJcbiAgICAgICAgZm9udC13ZWlnaHQ6IDQwMDtcclxuICAgICAgICBjb2xvcjogIzZiNzI4MDtcclxuICAgICAgICBsaW5lLWhlaWdodDogMS42O1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAubW9kZWwtbWV0YSB7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAzMnB4O1xyXG4gICAgcGFkZGluZy1ib3R0b206IDI0cHg7XHJcbiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U1ZTdlYjtcclxuICAgIFxyXG4gICAgLm1ldGEtcm93IHtcclxuICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgZ2FwOiAxMHJlbTtcclxuICAgICAgXHJcbiAgICAgIC5tZXRhLWl0ZW0ge1xyXG4gICAgICAgIGxhYmVsIHtcclxuICAgICAgICAgIGRpc3BsYXk6IGJsb2NrO1xyXG4gICAgICAgICAgZm9udC1zaXplOiAwLjg3NXJlbTtcclxuICAgICAgICAgIGNvbG9yOiAjMWExZDI5O1xyXG4gICAgICAgICAgbWFyZ2luLWJvdHRvbTogNHB4O1xyXG4gICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgICAgICB9XHJcbiAgICAgICAgXHJcbiAgICAgICAgLmZpZWxkLXZhbHVlIHtcclxuICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICAgICAgICBjb2xvcjogIzFhMWQyOTtcclxuICAgICAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAuY29uZmlnLWZpZWxkIHtcclxuICAgIG1hcmdpbi1ib3R0b206IDIwcHg7XHJcbiAgICBcclxuICAgIGxhYmVsIHtcclxuICAgICAgZGlzcGxheTogYmxvY2s7XHJcbiAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgIGNvbG9yOiAjMWExZDI5O1xyXG4gICAgICBtYXJnaW4tYm90dG9tOiA4cHg7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIC5jb2RlLWNvbnRlbnQge1xyXG4gICAgICBwYWRkaW5nOiAxNnB4O1xyXG4gICAgICBiYWNrZ3JvdW5kOiAjZjhmOWZhO1xyXG4gICAgICBib3JkZXI6IDFweCBzb2xpZCAjZTVlN2ViO1xyXG4gICAgICBib3JkZXItcmFkaXVzOiA4cHg7XHJcbiAgICAgIGNvbG9yOiAjMWExZDI5O1xyXG4gICAgICBmb250LWZhbWlseTogJ1NGIE1vbm8nLCBNb25hY28sICdDYXNjYWRpYSBDb2RlJywgbW9ub3NwYWNlO1xyXG4gICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICBsaW5lLWhlaWdodDogMS42O1xyXG4gICAgICBtaW4taGVpZ2h0OiAyMDBweDtcclxuICAgICAgd2hpdGUtc3BhY2U6IHByZS13cmFwO1xyXG4gICAgICBvdmVyZmxvdy15OiBhdXRvO1xyXG4gICAgICBtYXgtaGVpZ2h0OiA0MDBweDtcclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi5rbm93bGVkZ2UtcHJldmlldyB7XHJcbiAgLm1vZGVsLXNlY3Rpb24ge1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMzJweDtcclxuICAgIFxyXG4gICAgaDMge1xyXG4gICAgICBmb250LXNpemU6IDEuMjVyZW07XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgIGNvbG9yOiAjMWExZDI5O1xyXG4gICAgICBtYXJnaW46IDAgMCAyNHB4IDA7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAubW9kZWwtZmllbGQge1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMjRweDtcclxuICAgIFxyXG4gICAgbGFiZWwge1xyXG4gICAgICBkaXNwbGF5OiBibG9jaztcclxuICAgICAgZm9udC1zaXplOiAwLjg3NXJlbTtcclxuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgICAgY29sb3I6ICMxYTFkMjk7XHJcbiAgICAgIG1hcmdpbi1ib3R0b206IDhweDtcclxuICAgIH1cclxuICAgIFxyXG4gICAgLmZpZWxkLXZhbHVlIHtcclxuICAgICAgZm9udC1zaXplOiAxLjEyNXJlbTtcclxuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgICAgY29sb3I6ICMxYTFkMjk7XHJcbiAgICAgIGxpbmUtaGVpZ2h0OiAxLjU7XHJcbiAgICAgIFxyXG4gICAgICAmLmRlc2NyaXB0aW9uLXRleHQge1xyXG4gICAgICAgIGZvbnQtd2VpZ2h0OiA0MDA7XHJcbiAgICAgICAgY29sb3I6ICM2YjcyODA7XHJcbiAgICAgICAgbGluZS1oZWlnaHQ6IDEuNjtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLm1vZGVsLW1ldGEge1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMzJweDtcclxuICAgIHBhZGRpbmctYm90dG9tOiAyNHB4O1xyXG4gICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlNWU3ZWI7XHJcbiAgICBcclxuICAgIC5tZXRhLXJvdyB7XHJcbiAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgIGdhcDogMTByZW07XHJcbiAgICAgIFxyXG4gICAgICAubWV0YS1pdGVtIHtcclxuICAgICAgICBsYWJlbCB7XHJcbiAgICAgICAgICBkaXNwbGF5OiBibG9jaztcclxuICAgICAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgICAgICBjb2xvcjogIzFhMWQyOTtcclxuICAgICAgICAgIG1hcmdpbi1ib3R0b206IDRweDtcclxuICAgICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIFxyXG4gICAgICAgIC5maWVsZC12YWx1ZSB7XHJcbiAgICAgICAgICBmb250LXdlaWdodDogNTAwO1xyXG4gICAgICAgICAgY29sb3I6ICMxYTFkMjk7XHJcbiAgICAgICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLmNvbmZpZy1maWVsZCB7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAyNHB4O1xyXG4gICAgXHJcbiAgICBsYWJlbCB7XHJcbiAgICAgIGRpc3BsYXk6IGJsb2NrO1xyXG4gICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICBjb2xvcjogIzFhMWQyOTtcclxuICAgICAgbWFyZ2luLWJvdHRvbTogOHB4O1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICAuZHJvcGRvd24tZGlzcGxheSxcclxuICAgIC5pbnB1dC1kaXNwbGF5IHtcclxuICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xyXG4gICAgICBwYWRkaW5nOiAxMnB4IDE2cHg7XHJcbiAgICAgIGJhY2tncm91bmQ6ICNmOGY5ZmE7XHJcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNlNWU3ZWI7XHJcbiAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICAgICAgY29sb3I6ICMxYTFkMjk7XHJcbiAgICAgIG1pbi1oZWlnaHQ6IDQ4cHg7XHJcbiAgICAgIGJveC1zaXppbmc6IGJvcmRlci1ib3g7XHJcbiAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICB9XHJcbiAgICBcclxuICAgIC5kcm9wZG93bi1kaXNwbGF5IHtcclxuICAgICAgJjo6YWZ0ZXIge1xyXG4gICAgICAgIGNvbnRlbnQ6ICcnO1xyXG4gICAgICAgIHdpZHRoOiAwO1xyXG4gICAgICAgIGhlaWdodDogMDtcclxuICAgICAgICBib3JkZXItbGVmdDogNnB4IHNvbGlkIHRyYW5zcGFyZW50O1xyXG4gICAgICAgIGJvcmRlci1yaWdodDogNnB4IHNvbGlkIHRyYW5zcGFyZW50O1xyXG4gICAgICAgIGJvcmRlci10b3A6IDhweCBzb2xpZCAjNmI3MjgwO1xyXG4gICAgICAgIG1hcmdpbi1sZWZ0OiA4cHg7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAuc2xpZGVyLWNvbnRhaW5lciB7XHJcbiAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgIGdhcDogMTZweDtcclxuICAgICAgXHJcbiAgICAgIC5zbGlkZXItdHJhY2sge1xyXG4gICAgICAgIGZsZXg6IDE7XHJcbiAgICAgICAgaGVpZ2h0OiA2cHg7XHJcbiAgICAgICAgYmFja2dyb3VuZDogI2U1ZTdlYjtcclxuICAgICAgICBib3JkZXItcmFkaXVzOiAzcHg7XHJcbiAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gICAgICAgIFxyXG4gICAgICAgIC5zbGlkZXItZmlsbCB7XHJcbiAgICAgICAgICBoZWlnaHQ6IDEwMCU7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kOiAjM2I4MmY2O1xyXG4gICAgICAgICAgYm9yZGVyLXJhZGl1czogM3B4O1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgICBcclxuICAgICAgLnNsaWRlci12YWx1ZSB7XHJcbiAgICAgICAgbWluLXdpZHRoOiA2MHB4O1xyXG4gICAgICAgIHBhZGRpbmc6IDhweCAxMnB4O1xyXG4gICAgICAgIGJhY2tncm91bmQ6ICNmOGY5ZmE7XHJcbiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2U1ZTdlYjtcclxuICAgICAgICBib3JkZXItcmFkaXVzOiA2cHg7XHJcbiAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgICAgY29sb3I6ICMzNzQxNTE7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcblxyXG4gIC5maWxlcy1saXN0IHtcclxuICAgIC5maWxlLWl0ZW0ge1xyXG4gICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICBnYXA6IDhweDtcclxuICAgICAgcGFkZGluZzogOHB4IDEycHg7XHJcbiAgICAgIGJhY2tncm91bmQ6ICNmOGY5ZmE7XHJcbiAgICAgIGJvcmRlci1yYWRpdXM6IDZweDtcclxuICAgICAgbWFyZ2luLWJvdHRvbTogOHB4O1xyXG4gICAgICBcclxuICAgICAgLmZpbGUtbmFtZSB7XHJcbiAgICAgICAgY29sb3I6ICMzYjgyZjY7XHJcbiAgICAgICAgZm9udC1zaXplOiAwLjg3NXJlbTtcclxuICAgICAgICBmb250LXdlaWdodDogNTAwO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4udG9vbC1wcmV2aWV3IHtcclxuICAubW9kZWwtc2VjdGlvbiB7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAzMnB4O1xyXG4gICAgXHJcbiAgICBoMyB7XHJcbiAgICAgIGZvbnQtc2l6ZTogMS4yNXJlbTtcclxuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgICAgY29sb3I6ICMxYTFkMjk7XHJcbiAgICAgIG1hcmdpbjogMCAwIDI0cHggMDtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC5tb2RlbC1maWVsZCB7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAyNHB4O1xyXG4gICAgXHJcbiAgICBsYWJlbCB7XHJcbiAgICAgIGRpc3BsYXk6IGJsb2NrO1xyXG4gICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICBjb2xvcjogIzFhMWQyOTtcclxuICAgICAgbWFyZ2luLWJvdHRvbTogOHB4O1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICAuZmllbGQtdmFsdWUge1xyXG4gICAgICBmb250LXNpemU6IDEuMTI1cmVtO1xyXG4gICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICBjb2xvcjogIzFhMWQyOTtcclxuICAgICAgbGluZS1oZWlnaHQ6IDEuNTtcclxuICAgICAgXHJcbiAgICAgICYuZGVzY3JpcHRpb24tdGV4dCB7XHJcbiAgICAgICAgZm9udC13ZWlnaHQ6IDQwMDtcclxuICAgICAgICBjb2xvcjogIzZiNzI4MDtcclxuICAgICAgICBsaW5lLWhlaWdodDogMS42O1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAubW9kZWwtbWV0YSB7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAzMnB4O1xyXG4gICAgcGFkZGluZy1ib3R0b206IDI0cHg7XHJcbiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U1ZTdlYjtcclxuICAgIFxyXG4gICAgLm1ldGEtcm93IHtcclxuICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgZ2FwOiAxMHJlbTtcclxuICAgICAgXHJcbiAgICAgIC5tZXRhLWl0ZW0ge1xyXG4gICAgICAgIGxhYmVsIHtcclxuICAgICAgICAgIGRpc3BsYXk6IGJsb2NrO1xyXG4gICAgICAgICAgZm9udC1zaXplOiAwLjg3NXJlbTtcclxuICAgICAgICAgIGNvbG9yOiAjMWExZDI5O1xyXG4gICAgICAgICAgbWFyZ2luLWJvdHRvbTogNHB4O1xyXG4gICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgICAgICB9XHJcbiAgICAgICAgXHJcbiAgICAgICAgLmZpZWxkLXZhbHVlIHtcclxuICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICAgICAgICBjb2xvcjogIzFhMWQyOTtcclxuICAgICAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAuY29uZmlnLWZpZWxkIHtcclxuICAgIG1hcmdpbi1ib3R0b206IDIwcHg7XHJcbiAgICBcclxuICAgIGxhYmVsIHtcclxuICAgICAgZGlzcGxheTogYmxvY2s7XHJcbiAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgIGNvbG9yOiAjMWExZDI5O1xyXG4gICAgICBtYXJnaW4tYm90dG9tOiA4cHg7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIC5pbnB1dC1kaXNwbGF5IHtcclxuICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xyXG4gICAgICBwYWRkaW5nOiAxMnB4IDE2cHg7XHJcbiAgICAgIGJhY2tncm91bmQ6ICNmOGY5ZmE7XHJcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNlNWU3ZWI7XHJcbiAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICAgICAgY29sb3I6ICMxYTFkMjk7XHJcbiAgICAgIG1pbi1oZWlnaHQ6IDQ4cHg7XHJcbiAgICAgIGJveC1zaXppbmc6IGJvcmRlci1ib3g7XHJcbiAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICB9XHJcbiAgICBcclxuICAgIC5jb2RlLWNvbnRlbnQge1xyXG4gICAgICBwYWRkaW5nOiAxNnB4O1xyXG4gICAgICBiYWNrZ3JvdW5kOiAjZjhmOWZhO1xyXG4gICAgICBib3JkZXI6IDFweCBzb2xpZCAjZTVlN2ViO1xyXG4gICAgICBib3JkZXItcmFkaXVzOiA4cHg7XHJcbiAgICAgIGNvbG9yOiAjMWExZDI5O1xyXG4gICAgICBmb250LWZhbWlseTogJ1NGIE1vbm8nLCBNb25hY28sICdDYXNjYWRpYSBDb2RlJywgbW9ub3NwYWNlO1xyXG4gICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICBsaW5lLWhlaWdodDogMS42O1xyXG4gICAgICBtaW4taGVpZ2h0OiAyMDBweDtcclxuICAgICAgd2hpdGUtc3BhY2U6IHByZS13cmFwO1xyXG4gICAgICBcclxuICAgICAgLy8gQWx3YXlzIHNob3cgdGhlIGJvcmRlciBhbmQgYmFja2dyb3VuZCwgZXZlbiB3aGVuIGVtcHR5XHJcbiAgICAgICY6ZW1wdHk6OmJlZm9yZSB7XHJcbiAgICAgICAgY29udGVudDogJ05vIGNvbmZpZ3VyYXRpb24gYXZhaWxhYmxlJztcclxuICAgICAgICBjb2xvcjogIzljYTNhZjtcclxuICAgICAgICBmb250LXN0eWxlOiBpdGFsaWM7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi8vIEVuc3VyZSB0aGUgcGFuZWwgZm9vdGVyIGlzIHBvc2l0aW9uZWQgY29ycmVjdGx5XHJcbltwYW5lbC1mb290ZXJdIHtcclxuICBwb3NpdGlvbjogc3RpY2t5O1xyXG4gIGJvdHRvbTogMDtcclxuICBiYWNrZ3JvdW5kOiB3aGl0ZTtcclxuICBwYWRkaW5nOiAxNnB4O1xyXG4gIGJvcmRlci10b3A6IDFweCBzb2xpZCAjZjBmMWYyO1xyXG4gIG1hcmdpbi10b3A6IGF1dG87XHJcbn1cclxuXHJcbi5jb2RlLWNvbnRlbnQge1xyXG4gIC8vIEhpZGUgc2Nyb2xsYmFyIHdoaWxlIGtlZXBpbmcgc2Nyb2xsIGZ1bmN0aW9uYWxpdHlcclxuICBvdmVyZmxvdy15OiBhdXRvO1xyXG4gIG92ZXJmbG93LXg6IGhpZGRlbjtcclxuICBzY3JvbGxiYXItd2lkdGg6IG5vbmU7IC8qIEZpcmVmb3ggKi9cclxuICAtbXMtb3ZlcmZsb3ctc3R5bGU6IG5vbmU7IC8qIEludGVybmV0IEV4cGxvcmVyIDEwKyAqL1xyXG4gIFxyXG4gICY6Oi13ZWJraXQtc2Nyb2xsYmFyIHtcclxuICAgIGRpc3BsYXk6IG5vbmU7IC8qIENocm9tZSwgU2FmYXJpLCBFZGdlICovXHJcbiAgfVxyXG59XHJcblxyXG4vLyBSZW1vdmUgc2hhZG93IGZyb20gdGV4dGJveGVzXHJcbi5jb25maWctZmllbGQge1xyXG4gIDo6bmctZGVlcCBhdmEtdGV4dGJveCB7XHJcbiAgICAudGV4dGJveC1jb250YWluZXIge1xyXG4gICAgICBib3gtc2hhZG93OiBub25lICFpbXBvcnRhbnQ7XHJcbiAgICAgIFxyXG4gICAgICBpbnB1dCB7XHJcbiAgICAgICAgYm94LXNoYWRvdzogbm9uZSAhaW1wb3J0YW50O1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcbjo6bmctZGVlcCBhcHAtcHJldmlldy1wYW5lbCAucHJldmlldy1wYW5lbCA+IC5wcmV2aWV3LWhlYWRlciB7XHJcbiAgcGFkZGluZzogMjRweCAhaW1wb3J0YW50O1xyXG59XHJcblxyXG46Om5nLWRlZXAgYXBwLXByZXZpZXctcGFuZWwgLnByZXZpZXctcGFuZWwgPiAucHJldmlldy1jb250ZW50IHtcclxuICBwYWRkaW5nOiAwIDI0cHggIWltcG9ydGFudDtcclxufVxyXG5cclxuLmRpc2FibGVkLXNsaWRlciB7XHJcbiAgcG9pbnRlci1ldmVudHM6IG5vbmU7XHJcbiAgb3BhY2l0eTogMC42O1xyXG4gIFxyXG4gIDo6bmctZGVlcCAuc2xpZGVyLWNvbnRhaW5lciB7XHJcbiAgICBjdXJzb3I6IG5vdC1hbGxvd2VkO1xyXG4gICAgXHJcbiAgICAuc2xpZGVyLXRodW1iIHtcclxuICAgICAgY3Vyc29yOiBub3QtYWxsb3dlZDtcclxuICAgIH1cclxuICB9XHJcbn1cclxuOjpuZy1kZWVwIGFwcC1sb2FkZXIge1xyXG4gIHZpc2liaWxpdHk6IGhpZGRlbiAhaW1wb3J0YW50O1xyXG4gIHBvaW50ZXItZXZlbnRzOiBub25lICFpbXBvcnRhbnQ7XHJcbn1cclxuXHJcbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n  return AgentsPreviewPanelComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "DatePipe", "AvaTextboxComponent", "IconComponent", "SliderComponent", "PreviewPanelComponent", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "previewData", "title", "data", "name", "modelDescription", "description", "created<PERSON>y", "ɵɵpipeBind2", "createdOn", "createdDate", "modelType", "ɵɵproperty", "temperature", "maxToken", "topP", "ɵɵtemplate", "AgentsPreviewPanelComponent_div_9_div_1_div_15_div_1_Template", "AgentsPreviewPanelComponent_div_9_div_1_div_15_div_2_Template", "maxIteration", "aiEngine", "baseurl", "baseUrl", "llmDeploymentName", "<PERSON><PERSON><PERSON><PERSON>", "apiVersion", "AgentsPreviewPanelComponent_div_9_div_1_div_4_Template", "AgentsPreviewPanelComponent_div_9_div_1_div_5_Template", "AgentsPreviewPanelComponent_div_9_div_1_div_8_Template", "AgentsPreviewPanelComponent_div_9_div_1_div_9_Template", "AgentsPreviewPanelComponent_div_9_div_1_div_13_Template", "AgentsPreviewPanelComponent_div_9_div_1_div_14_Template", "AgentsPreviewPanelComponent_div_9_div_1_div_15_Template", "AgentsPreviewPanelComponent_div_9_div_1_div_16_Template", "ɵɵlistener", "AgentsPreviewPanelComponent_div_9_div_1_Template_span_click_18_listener", "ɵɵrestoreView", "_r1", "ɵɵnextContext", "ɵɵresetView", "toggleConfigDetails", "AgentsPreviewPanelComponent_div_9_div_1_div_21_Template", "AgentsPreviewPanelComponent_div_9_div_1_div_22_Template", "AgentsPreviewPanelComponent_div_9_div_1_div_23_Template", "AgentsPreviewPanelComponent_div_9_div_1_div_24_Template", "AgentsPreviewPanelComponent_div_9_div_1_div_25_Template", "AgentsPreviewPanelComponent_div_9_div_1_div_26_Template", "undefined", "ɵɵtextInterpolate1", "showMoreConfig", "ɵɵclassProp", "appDescription", "areaOfScope", "functionality", "content", "toolClassDef", "AgentsPreviewPanelComponent_div_9_div_2_div_4_Template", "AgentsPreviewPanelComponent_div_9_div_2_div_5_Template", "AgentsPreviewPanelComponent_div_9_div_2_div_6_Template", "AgentsPreviewPanelComponent_div_9_div_2_div_9_Template", "AgentsPreviewPanelComponent_div_9_div_2_div_10_Template", "AgentsPreviewPanelComponent_div_9_div_2_div_14_Template", "role", "goal", "backstory", "updatedAt", "createdAt", "promptTask", "template", "expectedOutput", "AgentsPreviewPanelComponent_div_9_div_3_div_4_Template", "AgentsPreviewPanelComponent_div_9_div_3_div_5_Template", "AgentsPreviewPanelComponent_div_9_div_3_div_6_Template", "AgentsPreviewPanelComponent_div_9_div_3_div_7_Template", "AgentsPreviewPanelComponent_div_9_div_3_div_8_Template", "AgentsPreviewPanelComponent_div_9_div_3_div_11_Template", "AgentsPreviewPanelComponent_div_9_div_3_div_12_Template", "AgentsPreviewPanelComponent_div_9_div_3_div_16_Template", "AgentsPreviewPanelComponent_div_9_div_3_div_17_Template", "embeddingModel", "splitSize", "uploadType", "getFileIconColor", "i_r4", "file_r3", "fileName", "AgentsPreviewPanelComponent_div_9_div_4_div_16_div_4_Template", "files", "AgentsPreviewPanelComponent_div_9_div_4_div_4_Template", "AgentsPreviewPanelComponent_div_9_div_4_div_5_Template", "AgentsPreviewPanelComponent_div_9_div_4_div_8_Template", "AgentsPreviewPanelComponent_div_9_div_4_div_9_Template", "AgentsPreviewPanelComponent_div_9_div_4_div_13_Template", "AgentsPreviewPanelComponent_div_9_div_4_div_14_Template", "AgentsPreviewPanelComponent_div_9_div_4_div_15_Template", "AgentsPreviewPanelComponent_div_9_div_4_div_16_Template", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AgentsPreviewPanelComponent_div_9_div_5_div_10_div_3_Template", "AgentsPreviewPanelComponent_div_9_div_5_div_10_div_4_Template", "AgentsPreviewPanelComponent_div_9_div_5_div_4_Template", "AgentsPreviewPanelComponent_div_9_div_5_div_5_Template", "AgentsPreviewPanelComponent_div_9_div_5_div_8_Template", "AgentsPreviewPanelComponent_div_9_div_5_div_9_Template", "AgentsPreviewPanelComponent_div_9_div_5_div_10_Template", "AgentsPreviewPanelComponent_div_9_div_1_Template", "AgentsPreviewPanelComponent_div_9_div_2_Template", "AgentsPreviewPanelComponent_div_9_div_3_Template", "AgentsPreviewPanelComponent_div_9_div_4_Template", "AgentsPreviewPanelComponent_div_9_div_5_Template", "type", "error", "AgentsPreviewPanelComponent", "closePreview", "onButtonClick", "event", "getAdditionalFields", "excludeFields", "Object", "keys", "filter", "key", "includes", "map", "value", "index", "colors", "getButtonLabel", "selectors", "inputs", "decls", "vars", "consts", "AgentsPreviewPanelComponent_Template", "rf", "ctx", "AgentsPreviewPanelComponent_Template_div_click_1_listener", "AgentsPreviewPanelComponent_Template_app_preview_panel_click_2_listener", "$event", "stopPropagation", "AgentsPreviewPanelComponent_Template_ava_icon_click_6_listener", "AgentsPreviewPanelComponent_div_8_Template", "AgentsPreviewPanelComponent_div_9_Template", "AgentsPreviewPanelComponent_div_10_Template", "loading", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\agents\\build-agents\\agents-preview-panel\\agents-preview-panel.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\pages\\agents\\build-agents\\agents-preview-panel\\agents-preview-panel.component.html"], "sourcesContent": ["import { CommonModule, DatePipe } from '@angular/common';\r\nimport { Component, Input } from '@angular/core';\r\nimport {\r\n  AvaTextboxComponent,\r\n  IconComponent,\r\n  SliderComponent,\r\n} from '@ava/play-comp-library';\r\nimport { PreviewPanelComponent } from '../../../../components/preview-panel/preview-panel.component';\r\n\r\n@Component({\r\n  selector: 'app-agents-preview-panel',\r\n  standalone: true,\r\n  imports: [\r\n    PreviewPanelComponent,\r\n    IconComponent,\r\n    DatePipe,\r\n    CommonModule,\r\n    SliderComponent,\r\n    AvaTextboxComponent,\r\n  ],\r\n  templateUrl: './agents-preview-panel.component.html',\r\n  styleUrl: './agents-preview-panel.component.scss',\r\n})\r\nexport class AgentsPreviewPanelComponent {\r\n  @Input() previewData: any = null;\r\n  @Input() closePreview!: () => void;\r\n\r\n  showMoreConfig = false;\r\n\r\n  toggleConfigDetails(): void {\r\n    this.showMoreConfig = !this.showMoreConfig;\r\n  }\r\n\r\n  onButtonClick(event: any): void {\r\n    this.closePreview();\r\n  }\r\n\r\n  getAdditionalFields(data: any): { key: string; value: any }[] {\r\n    const excludeFields = [\r\n      'id',\r\n      'name',\r\n      'description',\r\n      'labelCode',\r\n      'categoryName',\r\n      'categoryId',\r\n    ];\r\n    return Object.keys(data)\r\n      .filter((key) => !excludeFields.includes(key) && data[key] != null)\r\n      .map((key) => ({ key, value: data[key] }));\r\n  }\r\n\r\n  getFileIconColor(index: number): string {\r\n    const colors = ['#dc2626', '#2563eb', '#16a34a', '#f59e0b', '#8b5cf6'];\r\n    return colors[index % colors.length];\r\n  }\r\n\r\n  getButtonLabel(): string {\r\n    const type = this.previewData?.type;\r\n    switch (type) {\r\n      case 'model':\r\n        return 'Edit Model';\r\n      case 'tool':\r\n        return 'Edit Tool';\r\n      case 'prompt':\r\n        return 'Edit Prompt';\r\n      case 'knowledge':\r\n        return 'Edit Knowledge Base';\r\n      case 'guardrail':\r\n        return 'Edit Guardrail';\r\n      default:\r\n        return 'Edit';\r\n    }\r\n  }\r\n}\r\n", "<div class=\"preview-panel\">\r\n  <div class=\"backdrop\" (click)=\"closePreview()\"></div>\r\n<app-preview-panel class=\"panel-container\" [divider]=\"false\" (click)=\"$event.stopPropagation()\">\r\n<div panel-header class=\"preview-header\">\r\n  <span class=\"panel-title\">Metadata Information</span>\r\n  <ava-icon iconName=\"x\" iconColor=\"black\" class=\"close-btn\" (click)=\"closePreview()\"></ava-icon>\r\n</div>\r\n<div panel-content class=\"preview-content\">\r\n  <!-- Loading State -->\r\n  <div *ngIf=\"previewData?.loading\" class=\"preview-loading\">\r\n    <div class=\"loading-spinner\"></div>\r\n    <p>Loading details...</p>\r\n  </div>\r\n\r\n  <!-- Content based on preview data -->\r\n  <div *ngIf=\"previewData?.data && !previewData?.loading\">\r\n    <!-- Model Preview -->\r\n    <div *ngIf=\"previewData?.type === 'model'\" class=\"model-preview\">\r\n      <div class=\"model-section\">\r\n        <h3>Model Details</h3>\r\n        \r\n        <div class=\"model-field\" *ngIf=\"previewData?.title || previewData.data.name\">\r\n          <label>Model Name</label>\r\n          <div class=\"field-value\">{{ previewData?.title || previewData.data.name }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-field\" *ngIf=\"previewData.data.modelDescription || previewData.data.description\">\r\n          <label>Description</label>\r\n          <div class=\"field-value description-text\">{{ previewData.data.modelDescription || previewData.data.description }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-meta\">\r\n          <div class=\"meta-row\">\r\n            <div class=\"meta-item\" *ngIf=\"previewData.data.createdBy\">\r\n              <label>Added by</label>\r\n              <div class=\"field-value\">{{ previewData.data.createdBy}}</div>\r\n            </div>\r\n            <div class=\"meta-item\" *ngIf=\"previewData.data.createdOn || previewData.data.createdDate\">\r\n              <label>Added on</label>\r\n              <div class=\"field-value\">{{ previewData.data.createdOn || previewData.data.createdDate | date:'MM/dd/yyyy' }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"model-section\">\r\n        <h3>Model Configuration</h3>\r\n        \r\n        <div class=\"config-field\" *ngIf=\"previewData.data.modelType\">\r\n          <label>Select Model</label>\r\n          <div class=\"dropdown-display\">\r\n            <span>{{ previewData.data.modelType}}</span>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Temperature -->\r\n        <div class=\"config-field\" *ngIf=\"previewData.data.temperature !== undefined && previewData.data.temperature !== null\">\r\n          <label>Temperature</label>\r\n          <ava-slider [min]=\"0\" [max]=\"1\" [step]=\"0.01\"\r\n              [value]=\"previewData.data.temperature\" class=\"disabled-slider\"></ava-slider>\r\n        </div>\r\n\r\n        <!-- Max Tokens and Top P in same row -->\r\n        <div class=\"config-row\" *ngIf=\"previewData.data.maxToken || previewData.data.topP\">\r\n          <div class=\"config-field half-width\" *ngIf=\"previewData.data.maxToken\">\r\n            <label>Max Token</label>\r\n            <ava-textbox \r\n              [value]=\"previewData.data.maxToken\" \r\n              placeholder=\"4000\"\r\n              type=\"number\"\r\n              [disabled]=\"true\">\r\n            </ava-textbox>\r\n            <div class=\"field-hint\">4096 Tokens used</div>\r\n          </div>\r\n\r\n          <div class=\"config-field half-width\" *ngIf=\"previewData.data.topP\">\r\n            <label>Top P</label>\r\n            <ava-textbox \r\n              [value]=\"previewData.data.topP\" \r\n              placeholder=\"0.95\"\r\n              type=\"number\"\r\n              step=\"0.01\"\r\n              [disabled]=\"true\">\r\n            </ava-textbox>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Max Iteration -->\r\n        <div class=\"config-field\" *ngIf=\"previewData.data.maxIteration\">\r\n          <label>Max Iteration</label>\r\n          <ava-textbox \r\n            [value]=\"previewData.data.maxIteration\" \r\n            placeholder=\"1\"\r\n            type=\"number\"\r\n            [disabled]=\"true\">\r\n          </ava-textbox>\r\n        </div>\r\n\r\n        <div class=\"config-toggle\">\r\n          <span class=\"toggle-text\" (click)=\"toggleConfigDetails()\">\r\n            View More Configuration Details {{ showMoreConfig ? '-' : '+' }}\r\n          </span>\r\n        </div>\r\n\r\n        <!-- Collapsible Configuration Fields -->\r\n        <div class=\"config-details\" [class.expanded]=\"showMoreConfig\">\r\n          <div class=\"config-field\" *ngIf=\"previewData.data.aiEngine\">\r\n            <label>AI Engine</label>\r\n            <div class=\"dropdown-display\">\r\n              <span class=\"engine-icon\"></span>\r\n              <span>{{ previewData.data.aiEngine}}</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"config-field\" *ngIf=\"previewData.data.modelType\">\r\n            <label>Model Type</label>\r\n            <div class=\"dropdown-display\">\r\n              <span>{{ previewData.data.modelType}}</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"config-field\" *ngIf=\"previewData.data.baseurl || previewData.data.baseUrl\">\r\n            <label>Base URL</label>\r\n            <div class=\"input-display\">{{ previewData.data.baseurl || previewData.data.baseUrl }}</div>\r\n          </div>\r\n\r\n          <div class=\"config-field\" *ngIf=\"previewData.data.llmDeploymentName\">\r\n            <label>LLM Deployment Name</label>\r\n            <div class=\"input-display\">{{ previewData.data.llmDeploymentName }}</div>\r\n          </div>\r\n\r\n          <div class=\"config-field\" *ngIf=\"previewData.data.apiKey\">\r\n            <label>API Key Encoded</label>\r\n            <div class=\"input-display\">{{ previewData.data.apiKey}}</div>\r\n          </div>\r\n\r\n          <div class=\"config-field\" *ngIf=\"previewData.data.apiVersion\">\r\n            <label>API Version</label>\r\n            <div class=\"input-display\">{{ previewData.data.apiVersion }}</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Tool Preview -->\r\n    <div *ngIf=\"previewData?.type === 'tool'\" class=\"tool-preview\">\r\n      <div class=\"model-section\">\r\n        <h3>Tool Details</h3>\r\n        \r\n        <div class=\"model-field\" *ngIf=\"previewData?.title || previewData.data.name\">\r\n          <label>Tool Name</label>\r\n          <div class=\"field-value\">{{ previewData?.title || previewData.data.name }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-field\" *ngIf=\"previewData.data.description || previewData.data.appDescription\">\r\n          <label>Description</label>\r\n          <div class=\"field-value description-text\">{{ previewData.data.description || previewData.data.appDescription }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-field\" *ngIf=\"previewData.data.areaOfScope\">\r\n          <label>Area of Scope</label>\r\n          <div class=\"field-value description-text\">{{ previewData.data.areaOfScope }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-meta\">\r\n          <div class=\"meta-row\">\r\n            <div class=\"meta-item\" *ngIf=\"previewData.data.createdBy\">\r\n              <label>Added by</label>\r\n              <div class=\"field-value\">{{ previewData.data.createdBy }}</div>\r\n            </div>\r\n            <div class=\"meta-item\" *ngIf=\"previewData.data.createdOn || previewData.data.createdDate\">\r\n              <label>Added on</label>\r\n              <div class=\"field-value\">{{ previewData.data.createdOn || previewData.data.createdDate | date:'MM/dd/yyyy' }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"model-section\">\r\n        <h3>Tool Configuration</h3>\r\n        \r\n        <div class=\"config-field\" *ngIf=\"previewData.data.functionality || previewData.data.content || previewData.data.toolClassDef\">\r\n          <div class=\"code-content\">{{ previewData.data.functionality || previewData.data.content || previewData.data.toolClassDef || 'No tool definition available' }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Prompt Preview -->\r\n    <div *ngIf=\"previewData?.type === 'prompt'\" class=\"prompt-preview\">\r\n      <div class=\"model-section\">\r\n        <h3>Prompt Details</h3>\r\n        \r\n        <div class=\"model-field\" *ngIf=\"previewData?.title || previewData.data.name\">\r\n          <label>Prompt Name</label>\r\n          <div class=\"field-value\">{{ previewData?.title || previewData.data.name }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-field\" *ngIf=\"previewData.data.description\">\r\n          <label>Description</label>\r\n          <div class=\"field-value description-text\">{{ previewData.data.description }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-field\" *ngIf=\"previewData.data.role\">\r\n          <label>Role</label>\r\n          <div class=\"field-value\">{{ previewData.data.role }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-field\" *ngIf=\"previewData.data.goal\">\r\n          <label>Goal</label>\r\n          <div class=\"field-value description-text\">{{ previewData.data.goal }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-field\" *ngIf=\"previewData.data.backstory\">\r\n          <label>Backstory</label>\r\n          <div class=\"field-value description-text\">{{ previewData.data.backstory }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-meta\">\r\n          <div class=\"meta-row\">\r\n            <div class=\"meta-item\" *ngIf=\"previewData.data.createdBy\">\r\n              <label>Added by</label>\r\n              <div class=\"field-value\">{{ previewData.data.createdBy }}</div>\r\n            </div>\r\n            <div class=\"meta-item\" *ngIf=\"previewData.data.updatedAt || previewData.data.createdAt\">\r\n              <label>Added on</label>\r\n              <div class=\"field-value\">{{ previewData.data.updatedAt || previewData.data.createdAt | date:'MM/dd/yyyy' }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"model-section\">\r\n        <h3>Prompt Configuration</h3>\r\n        \r\n        <div class=\"config-field\" *ngIf=\"previewData.data.promptTask || previewData.data.template || previewData.data.content\">\r\n          <label>Freeform Prompt <span class=\"required\">*</span></label>\r\n          <div class=\"prompt-content\">{{ previewData.data.promptTask || previewData.data.template || previewData.data.content }}</div>\r\n        </div>\r\n\r\n        <div class=\"config-field\" *ngIf=\"previewData.data.expectedOutput\">\r\n          <label>Expected Output</label>\r\n          <div class=\"prompt-content\">{{ previewData.data.expectedOutput }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Knowledge Base Preview -->\r\n    <div *ngIf=\"previewData?.type === 'knowledge'\" class=\"knowledge-preview\">\r\n      <div class=\"model-section\">\r\n        <h3>Knowledge Base Details</h3>\r\n        \r\n        <div class=\"model-field\" *ngIf=\"previewData.data.name || previewData?.title\">\r\n          <label>Knowledge Base Name</label>\r\n          <div class=\"field-value\">{{ previewData.data.name || previewData?.title }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-field\" *ngIf=\"previewData.data.description\">\r\n          <label>Description</label>\r\n          <div class=\"field-value description-text\">{{ previewData.data.description }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-meta\">\r\n          <div class=\"meta-row\">\r\n            <div class=\"meta-item\" *ngIf=\"previewData.data.createdBy\">\r\n              <label>Added by</label>\r\n              <div class=\"field-value\">{{ previewData.data.createdBy}}</div>\r\n            </div>\r\n            <div class=\"meta-item\" *ngIf=\"previewData.data.createdDate || previewData.data.createdOn\">\r\n              <label>Added on</label>\r\n              <div class=\"field-value\">{{ previewData.data.createdDate || previewData.data.createdOn | date:'MM/dd/yyyy' }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"model-section\">\r\n        <h3>Knowledge Base Configuration</h3>\r\n        \r\n        <div class=\"config-field\" *ngIf=\"previewData.data.embeddingModel\">\r\n          <label>Embedding Model</label>\r\n          <div class=\"dropdown-display\">\r\n            <span>{{ previewData.data.embeddingModel }}</span>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"config-field\" *ngIf=\"previewData.data.splitSize !== undefined && previewData.data.splitSize !== null\">\r\n          <label>Split Size</label>\r\n          <ava-slider [min]=\"0\" [max]=\"1\" [step]=\"0.01\"\r\n              [value]=\"previewData.data.splitSize\" class=\"disabled-slider\"></ava-slider>\r\n        </div>\r\n\r\n        <div class=\"config-field\" *ngIf=\"previewData.data.uploadType\">\r\n          <label>Upload Type</label>\r\n          <div class=\"input-display\">{{ previewData.data.uploadType}}</div>\r\n        </div>\r\n\r\n        <div class=\"config-field\" *ngIf=\"previewData.data.files && previewData.data.files.length > 0\">\r\n          <label>Files Uploaded</label>\r\n          <div class=\"files-list\">\r\n            <div *ngFor=\"let file of previewData.data.files; let i = index\" class=\"file-item\">\r\n              <ava-icon iconName=\"file-text\" [iconColor]=\"getFileIconColor(i)\" [iconSize]=\"16\"></ava-icon>\r\n              <span class=\"file-name\">{{ file.fileName || file.name || \"Knowledge Base Data\" }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Guardrail Preview -->\r\n    <div *ngIf=\"previewData?.type === 'guardrail'\" class=\"guardrail-preview\">\r\n      <div class=\"model-section\">\r\n        <h3>Guardrail Details</h3>\r\n        \r\n        <div class=\"model-field\" *ngIf=\"previewData?.title || previewData.data.name\">\r\n          <label>Guardrail Name</label>\r\n          <div class=\"field-value\">{{ previewData?.title || previewData.data.name }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-field\" *ngIf=\"previewData.data.description\">\r\n          <label>Description</label>\r\n          <div class=\"field-value description-text\">{{ previewData.data.description }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-meta\">\r\n          <div class=\"meta-row\">\r\n            <div class=\"meta-item\" *ngIf=\"previewData.data.createdBy\">\r\n              <label>Added by</label>\r\n              <div class=\"field-value\">{{ previewData.data.createdBy}}</div>\r\n            </div>\r\n            <div class=\"meta-item\" *ngIf=\"previewData.data.createdDate || previewData.data.createdOn\">\r\n              <label>Added on</label>\r\n              <div class=\"field-value\">{{ previewData.data.createdDate || previewData.data.createdOn | date:'MM/dd/yyyy' }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"model-section\"  *ngIf=\"previewData.data.yamlContent || previewData.data.content\" >\r\n        <h3>Guardrail Configuration</h3>\r\n        \r\n        <div class=\"config-field\" *ngIf=\"previewData.data.content\">\r\n          <label>Guardrail in Colang</label>\r\n          <div class=\"code-content\">{{ previewData.data.content }}</div>\r\n        </div>\r\n         <div class=\"config-field\" *ngIf=\"previewData.data.yamlContent\">\r\n          <label>Guardrail in Yml</label>\r\n          <div class=\"code-content\">{{ previewData.data.yamlContent }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Error State -->\r\n  <div *ngIf=\"previewData?.error\" class=\"preview-error\">\r\n    <p>{{ previewData.error }}</p>\r\n  </div>\r\n</div>\r\n <!-- <div panel-footer>\r\n    <ava-button [label]=\"getButtonLabel()\" variant=\"info\" width=\"100%\" (userClick)=\"onButtonClick($event)\"></ava-button>\r\n  </div> -->\r\n</app-preview-panel>\r\n</div>\r\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,QAAQ,QAAQ,iBAAiB;AAExD,SACEC,mBAAmB,EACnBC,aAAa,EACbC,eAAe,QACV,wBAAwB;AAC/B,SAASC,qBAAqB,QAAQ,8DAA8D;;;;;ICElGC,EAAA,CAAAC,cAAA,cAA0D;IACxDD,EAAA,CAAAE,SAAA,cAAmC;IACnCF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,yBAAkB;IACvBH,EADuB,CAAAI,YAAA,EAAI,EACrB;;;;;IAUEJ,EADF,CAAAC,cAAA,cAA6E,YACpE;IAAAD,EAAA,CAAAG,MAAA,iBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACzBJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAiD;IAC5EH,EAD4E,CAAAI,YAAA,EAAM,EAC5E;;;;IADqBJ,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAM,iBAAA,EAAAC,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,KAAA,KAAAF,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAC,IAAA,CAAiD;;;;;IAI1EX,EADF,CAAAC,cAAA,cAAmG,YAC1F;IAAAD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC1BJ,EAAA,CAAAC,cAAA,cAA0C;IAAAD,EAAA,CAAAG,MAAA,GAAuE;IACnHH,EADmH,CAAAI,YAAA,EAAM,EACnH;;;;IADsCJ,EAAA,CAAAK,SAAA,GAAuE;IAAvEL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAE,gBAAA,IAAAL,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAG,WAAA,CAAuE;;;;;IAM7Gb,EADF,CAAAC,cAAA,cAA0D,YACjD;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvBJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAA+B;IAC1DH,EAD0D,CAAAI,YAAA,EAAM,EAC1D;;;;IADqBJ,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAI,SAAA,CAA+B;;;;;IAGxDd,EADF,CAAAC,cAAA,cAA0F,YACjF;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvBJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAoF;;IAC/GH,EAD+G,CAAAI,YAAA,EAAM,EAC/G;;;;IADqBJ,EAAA,CAAAK,SAAA,GAAoF;IAApFL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAAe,WAAA,OAAAR,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAM,SAAA,IAAAT,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAO,WAAA,gBAAoF;;;;;IAUjHjB,EADF,CAAAC,cAAA,cAA6D,YACpD;IAAAD,EAAA,CAAAG,MAAA,mBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAEzBJ,EADF,CAAAC,cAAA,cAA8B,WACtB;IAAAD,EAAA,CAAAG,MAAA,GAA+B;IAEzCH,EAFyC,CAAAI,YAAA,EAAO,EACxC,EACF;;;;IAFIJ,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAQ,SAAA,CAA+B;;;;;IAMvClB,EADF,CAAAC,cAAA,cAAsH,YAC7G;IAAAD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC1BJ,EAAA,CAAAE,SAAA,qBACgF;IAClFF,EAAA,CAAAI,YAAA,EAAM;;;;IAFQJ,EAAA,CAAAK,SAAA,GAAS;IACjBL,EADQ,CAAAmB,UAAA,UAAS,UAAU,cAAc,UAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAU,WAAA,CACH;;;;;IAMxCpB,EADF,CAAAC,cAAA,cAAuE,YAC9D;IAAAD,EAAA,CAAAG,MAAA,gBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACxBJ,EAAA,CAAAE,SAAA,sBAKc;IACdF,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAG,MAAA,uBAAgB;IAC1CH,EAD0C,CAAAI,YAAA,EAAM,EAC1C;;;;IANFJ,EAAA,CAAAK,SAAA,GAAmC;IAGnCL,EAHA,CAAAmB,UAAA,UAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAW,QAAA,CAAmC,kBAGlB;;;;;IAMnBrB,EADF,CAAAC,cAAA,cAAmE,YAC1D;IAAAD,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACpBJ,EAAA,CAAAE,SAAA,sBAMc;IAChBF,EAAA,CAAAI,YAAA,EAAM;;;;IANFJ,EAAA,CAAAK,SAAA,GAA+B;IAI/BL,EAJA,CAAAmB,UAAA,UAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAY,IAAA,CAA+B,kBAId;;;;;IAnBvBtB,EAAA,CAAAC,cAAA,cAAmF;IAYjFD,EAXA,CAAAuB,UAAA,IAAAC,6DAAA,kBAAuE,IAAAC,6DAAA,kBAWJ;IAUrEzB,EAAA,CAAAI,YAAA,EAAM;;;;IArBkCJ,EAAA,CAAAK,SAAA,EAA+B;IAA/BL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAW,QAAA,CAA+B;IAW/BrB,EAAA,CAAAK,SAAA,EAA2B;IAA3BL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAY,IAAA,CAA2B;;;;;IAcjEtB,EADF,CAAAC,cAAA,cAAgE,YACvD;IAAAD,EAAA,CAAAG,MAAA,oBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC5BJ,EAAA,CAAAE,SAAA,sBAKc;IAChBF,EAAA,CAAAI,YAAA,EAAM;;;;IALFJ,EAAA,CAAAK,SAAA,GAAuC;IAGvCL,EAHA,CAAAmB,UAAA,UAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAgB,YAAA,CAAuC,kBAGtB;;;;;IAajB1B,EADF,CAAAC,cAAA,cAA4D,YACnD;IAAAD,EAAA,CAAAG,MAAA,gBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACxBJ,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAE,SAAA,eAAiC;IACjCF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,GAA8B;IAExCH,EAFwC,CAAAI,YAAA,EAAO,EACvC,EACF;;;;IAFIJ,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAiB,QAAA,CAA8B;;;;;IAKtC3B,EADF,CAAAC,cAAA,cAA6D,YACpD;IAAAD,EAAA,CAAAG,MAAA,iBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAEvBJ,EADF,CAAAC,cAAA,cAA8B,WACtB;IAAAD,EAAA,CAAAG,MAAA,GAA+B;IAEzCH,EAFyC,CAAAI,YAAA,EAAO,EACxC,EACF;;;;IAFIJ,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAQ,SAAA,CAA+B;;;;;IAKvClB,EADF,CAAAC,cAAA,cAAuF,YAC9E;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvBJ,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAG,MAAA,GAA0D;IACvFH,EADuF,CAAAI,YAAA,EAAM,EACvF;;;;IADuBJ,EAAA,CAAAK,SAAA,GAA0D;IAA1DL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAkB,OAAA,IAAArB,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAmB,OAAA,CAA0D;;;;;IAIrF7B,EADF,CAAAC,cAAA,cAAqE,YAC5D;IAAAD,EAAA,CAAAG,MAAA,0BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAClCJ,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAG,MAAA,GAAwC;IACrEH,EADqE,CAAAI,YAAA,EAAM,EACrE;;;;IADuBJ,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAoB,iBAAA,CAAwC;;;;;IAInE9B,EADF,CAAAC,cAAA,cAA0D,YACjD;IAAAD,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC9BJ,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAG,MAAA,GAA4B;IACzDH,EADyD,CAAAI,YAAA,EAAM,EACzD;;;;IADuBJ,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAqB,MAAA,CAA4B;;;;;IAIvD/B,EADF,CAAAC,cAAA,cAA8D,YACrD;IAAAD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC1BJ,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAG,MAAA,GAAiC;IAC9DH,EAD8D,CAAAI,YAAA,EAAM,EAC9D;;;;IADuBJ,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAsB,UAAA,CAAiC;;;;;;IAvHhEhC,EAFJ,CAAAC,cAAA,cAAiE,cACpC,SACrB;IAAAD,EAAA,CAAAG,MAAA,oBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAOtBJ,EALA,CAAAuB,UAAA,IAAAU,sDAAA,kBAA6E,IAAAC,sDAAA,kBAKsB;IAMjGlC,EADF,CAAAC,cAAA,cAAwB,cACA;IAKpBD,EAJA,CAAAuB,UAAA,IAAAY,sDAAA,kBAA0D,IAAAC,sDAAA,kBAIgC;IAMhGpC,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;IAGJJ,EADF,CAAAC,cAAA,eAA2B,UACrB;IAAAD,EAAA,CAAAG,MAAA,2BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IA0C5BJ,EAxCA,CAAAuB,UAAA,KAAAc,uDAAA,kBAA6D,KAAAC,uDAAA,kBAQyD,KAAAC,uDAAA,kBAOnC,KAAAC,uDAAA,kBAyBnB;IAW9DxC,EADF,CAAAC,cAAA,eAA2B,gBACiC;IAAhCD,EAAA,CAAAyC,UAAA,mBAAAC,wEAAA;MAAA1C,EAAA,CAAA2C,aAAA,CAAAC,GAAA;MAAA,MAAArC,MAAA,GAAAP,EAAA,CAAA6C,aAAA;MAAA,OAAA7C,EAAA,CAAA8C,WAAA,CAASvC,MAAA,CAAAwC,mBAAA,EAAqB;IAAA,EAAC;IACvD/C,EAAA,CAAAG,MAAA,IACF;IACFH,EADE,CAAAI,YAAA,EAAO,EACH;IAGNJ,EAAA,CAAAC,cAAA,eAA8D;IA+B5DD,EA9BA,CAAAuB,UAAA,KAAAyB,uDAAA,kBAA4D,KAAAC,uDAAA,kBAQC,KAAAC,uDAAA,kBAO0B,KAAAC,uDAAA,kBAKlB,KAAAC,uDAAA,kBAKX,KAAAC,uDAAA,kBAKI;IAMpErD,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;;;;IAzHwBJ,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAmB,UAAA,UAAAZ,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,KAAA,KAAAF,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAC,IAAA,CAAiD;IAKjDX,EAAA,CAAAK,SAAA,EAAuE;IAAvEL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAE,gBAAA,IAAAL,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAG,WAAA,CAAuE;IAOrEb,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAI,SAAA,CAAgC;IAIhCd,EAAA,CAAAK,SAAA,EAAgE;IAAhEL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAM,SAAA,IAAAT,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAO,WAAA,CAAgE;IAWjEjB,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAQ,SAAA,CAAgC;IAQhClB,EAAA,CAAAK,SAAA,EAAyF;IAAzFL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAU,WAAA,KAAAkC,SAAA,IAAA/C,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAU,WAAA,UAAyF;IAO3FpB,EAAA,CAAAK,SAAA,EAAwD;IAAxDL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAW,QAAA,IAAAd,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAY,IAAA,CAAwD;IAyBtDtB,EAAA,CAAAK,SAAA,EAAmC;IAAnCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAgB,YAAA,CAAmC;IAY1D1B,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAuD,kBAAA,sCAAAhD,MAAA,CAAAiD,cAAA,kBACF;IAI0BxD,EAAA,CAAAK,SAAA,EAAiC;IAAjCL,EAAA,CAAAyD,WAAA,aAAAlD,MAAA,CAAAiD,cAAA,CAAiC;IAChCxD,EAAA,CAAAK,SAAA,EAA+B;IAA/BL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAiB,QAAA,CAA+B;IAQ/B3B,EAAA,CAAAK,SAAA,EAAgC;IAAhCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAQ,SAAA,CAAgC;IAOhClB,EAAA,CAAAK,SAAA,EAA0D;IAA1DL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAkB,OAAA,IAAArB,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAmB,OAAA,CAA0D;IAK1D7B,EAAA,CAAAK,SAAA,EAAwC;IAAxCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAoB,iBAAA,CAAwC;IAKxC9B,EAAA,CAAAK,SAAA,EAA6B;IAA7BL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAqB,MAAA,CAA6B;IAK7B/B,EAAA,CAAAK,SAAA,EAAiC;IAAjCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAsB,UAAA,CAAiC;;;;;IAc5DhC,EADF,CAAAC,cAAA,cAA6E,YACpE;IAAAD,EAAA,CAAAG,MAAA,gBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACxBJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAiD;IAC5EH,EAD4E,CAAAI,YAAA,EAAM,EAC5E;;;;IADqBJ,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAM,iBAAA,EAAAC,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,KAAA,KAAAF,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAC,IAAA,CAAiD;;;;;IAI1EX,EADF,CAAAC,cAAA,cAAiG,YACxF;IAAAD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC1BJ,EAAA,CAAAC,cAAA,cAA0C;IAAAD,EAAA,CAAAG,MAAA,GAAqE;IACjHH,EADiH,CAAAI,YAAA,EAAM,EACjH;;;;IADsCJ,EAAA,CAAAK,SAAA,GAAqE;IAArEL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAG,WAAA,IAAAN,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAgD,cAAA,CAAqE;;;;;IAI/G1D,EADF,CAAAC,cAAA,cAA8D,YACrD;IAAAD,EAAA,CAAAG,MAAA,oBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC5BJ,EAAA,CAAAC,cAAA,cAA0C;IAAAD,EAAA,CAAAG,MAAA,GAAkC;IAC9EH,EAD8E,CAAAI,YAAA,EAAM,EAC9E;;;;IADsCJ,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAiD,WAAA,CAAkC;;;;;IAMxE3D,EADF,CAAAC,cAAA,cAA0D,YACjD;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvBJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAgC;IAC3DH,EAD2D,CAAAI,YAAA,EAAM,EAC3D;;;;IADqBJ,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAI,SAAA,CAAgC;;;;;IAGzDd,EADF,CAAAC,cAAA,cAA0F,YACjF;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvBJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAoF;;IAC/GH,EAD+G,CAAAI,YAAA,EAAM,EAC/G;;;;IADqBJ,EAAA,CAAAK,SAAA,GAAoF;IAApFL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAAe,WAAA,OAAAR,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAM,SAAA,IAAAT,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAO,WAAA,gBAAoF;;;;;IAUjHjB,EADF,CAAAC,cAAA,cAA8H,cAClG;IAAAD,EAAA,CAAAG,MAAA,GAAmI;IAC/JH,EAD+J,CAAAI,YAAA,EAAM,EAC/J;;;;IADsBJ,EAAA,CAAAK,SAAA,GAAmI;IAAnIL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAkD,aAAA,IAAArD,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAmD,OAAA,IAAAtD,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAoD,YAAA,mCAAmI;;;;;IAnC/J9D,EAFJ,CAAAC,cAAA,cAA+D,cAClC,SACrB;IAAAD,EAAA,CAAAG,MAAA,mBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAYrBJ,EAVA,CAAAuB,UAAA,IAAAwC,sDAAA,kBAA6E,IAAAC,sDAAA,kBAKoB,IAAAC,sDAAA,kBAKnC;IAM5DjE,EADF,CAAAC,cAAA,cAAwB,cACA;IAKpBD,EAJA,CAAAuB,UAAA,IAAA2C,sDAAA,kBAA0D,KAAAC,uDAAA,kBAIgC;IAMhGnE,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;IAGJJ,EADF,CAAAC,cAAA,eAA2B,UACrB;IAAAD,EAAA,CAAAG,MAAA,0BAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAE3BJ,EAAA,CAAAuB,UAAA,KAAA6C,uDAAA,kBAA8H;IAIlIpE,EADE,CAAAI,YAAA,EAAM,EACF;;;;IApCwBJ,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAmB,UAAA,UAAAZ,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,KAAA,KAAAF,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAC,IAAA,CAAiD;IAKjDX,EAAA,CAAAK,SAAA,EAAqE;IAArEL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAG,WAAA,IAAAN,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAgD,cAAA,CAAqE;IAKrE1D,EAAA,CAAAK,SAAA,EAAkC;IAAlCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAiD,WAAA,CAAkC;IAOhC3D,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAI,SAAA,CAAgC;IAIhCd,EAAA,CAAAK,SAAA,EAAgE;IAAhEL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAM,SAAA,IAAAT,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAO,WAAA,CAAgE;IAWjEjB,EAAA,CAAAK,SAAA,GAAiG;IAAjGL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAkD,aAAA,IAAArD,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAmD,OAAA,IAAAtD,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAoD,YAAA,CAAiG;;;;;IAY1H9D,EADF,CAAAC,cAAA,cAA6E,YACpE;IAAAD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC1BJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAiD;IAC5EH,EAD4E,CAAAI,YAAA,EAAM,EAC5E;;;;IADqBJ,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAM,iBAAA,EAAAC,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,KAAA,KAAAF,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAC,IAAA,CAAiD;;;;;IAI1EX,EADF,CAAAC,cAAA,cAA8D,YACrD;IAAAD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC1BJ,EAAA,CAAAC,cAAA,cAA0C;IAAAD,EAAA,CAAAG,MAAA,GAAkC;IAC9EH,EAD8E,CAAAI,YAAA,EAAM,EAC9E;;;;IADsCJ,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAG,WAAA,CAAkC;;;;;IAI5Eb,EADF,CAAAC,cAAA,cAAuD,YAC9C;IAAAD,EAAA,CAAAG,MAAA,WAAI;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACnBJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAA2B;IACtDH,EADsD,CAAAI,YAAA,EAAM,EACtD;;;;IADqBJ,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA2D,IAAA,CAA2B;;;;;IAIpDrE,EADF,CAAAC,cAAA,cAAuD,YAC9C;IAAAD,EAAA,CAAAG,MAAA,WAAI;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACnBJ,EAAA,CAAAC,cAAA,cAA0C;IAAAD,EAAA,CAAAG,MAAA,GAA2B;IACvEH,EADuE,CAAAI,YAAA,EAAM,EACvE;;;;IADsCJ,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA4D,IAAA,CAA2B;;;;;IAIrEtE,EADF,CAAAC,cAAA,cAA4D,YACnD;IAAAD,EAAA,CAAAG,MAAA,gBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACxBJ,EAAA,CAAAC,cAAA,cAA0C;IAAAD,EAAA,CAAAG,MAAA,GAAgC;IAC5EH,EAD4E,CAAAI,YAAA,EAAM,EAC5E;;;;IADsCJ,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA6D,SAAA,CAAgC;;;;;IAMtEvE,EADF,CAAAC,cAAA,cAA0D,YACjD;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvBJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAgC;IAC3DH,EAD2D,CAAAI,YAAA,EAAM,EAC3D;;;;IADqBJ,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAI,SAAA,CAAgC;;;;;IAGzDd,EADF,CAAAC,cAAA,cAAwF,YAC/E;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvBJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAkF;;IAC7GH,EAD6G,CAAAI,YAAA,EAAM,EAC7G;;;;IADqBJ,EAAA,CAAAK,SAAA,GAAkF;IAAlFL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAAe,WAAA,OAAAR,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA8D,SAAA,IAAAjE,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA+D,SAAA,gBAAkF;;;;;IAU/GzE,EADF,CAAAC,cAAA,cAAuH,YAC9G;IAAAD,EAAA,CAAAG,MAAA,uBAAgB;IAAAH,EAAA,CAAAC,cAAA,eAAuB;IAAAD,EAAA,CAAAG,MAAA,QAAC;IAAOH,EAAP,CAAAI,YAAA,EAAO,EAAQ;IAC9DJ,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAG,MAAA,GAA0F;IACxHH,EADwH,CAAAI,YAAA,EAAM,EACxH;;;;IADwBJ,EAAA,CAAAK,SAAA,GAA0F;IAA1FL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAgE,UAAA,IAAAnE,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAiE,QAAA,IAAApE,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAmD,OAAA,CAA0F;;;;;IAItH7D,EADF,CAAAC,cAAA,cAAkE,YACzD;IAAAD,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC9BJ,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAG,MAAA,GAAqC;IACnEH,EADmE,CAAAI,YAAA,EAAM,EACnE;;;;IADwBJ,EAAA,CAAAK,SAAA,GAAqC;IAArCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAkE,cAAA,CAAqC;;;;;IAnDnE5E,EAFJ,CAAAC,cAAA,cAAmE,cACtC,SACrB;IAAAD,EAAA,CAAAG,MAAA,qBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAsBvBJ,EApBA,CAAAuB,UAAA,IAAAsD,sDAAA,kBAA6E,IAAAC,sDAAA,kBAKf,IAAAC,sDAAA,kBAKP,IAAAC,sDAAA,kBAKA,IAAAC,sDAAA,kBAKK;IAM1DjF,EADF,CAAAC,cAAA,cAAwB,eACA;IAKpBD,EAJA,CAAAuB,UAAA,KAAA2D,uDAAA,kBAA0D,KAAAC,uDAAA,kBAI8B;IAM9FnF,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;IAGJJ,EADF,CAAAC,cAAA,eAA2B,UACrB;IAAAD,EAAA,CAAAG,MAAA,4BAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAO7BJ,EALA,CAAAuB,UAAA,KAAA6D,uDAAA,kBAAuH,KAAAC,uDAAA,kBAKrD;IAKtErF,EADE,CAAAI,YAAA,EAAM,EACF;;;;IApDwBJ,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAmB,UAAA,UAAAZ,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,KAAA,KAAAF,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAC,IAAA,CAAiD;IAKjDX,EAAA,CAAAK,SAAA,EAAkC;IAAlCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAG,WAAA,CAAkC;IAKlCb,EAAA,CAAAK,SAAA,EAA2B;IAA3BL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA2D,IAAA,CAA2B;IAK3BrE,EAAA,CAAAK,SAAA,EAA2B;IAA3BL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA4D,IAAA,CAA2B;IAK3BtE,EAAA,CAAAK,SAAA,EAAgC;IAAhCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA6D,SAAA,CAAgC;IAO9BvE,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAI,SAAA,CAAgC;IAIhCd,EAAA,CAAAK,SAAA,EAA8D;IAA9DL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA8D,SAAA,IAAAjE,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA+D,SAAA,CAA8D;IAW/DzE,EAAA,CAAAK,SAAA,GAA0F;IAA1FL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAgE,UAAA,IAAAnE,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAiE,QAAA,IAAApE,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAmD,OAAA,CAA0F;IAK1F7D,EAAA,CAAAK,SAAA,EAAqC;IAArCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAkE,cAAA,CAAqC;;;;;IAa9D5E,EADF,CAAAC,cAAA,cAA6E,YACpE;IAAAD,EAAA,CAAAG,MAAA,0BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAClCJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAiD;IAC5EH,EAD4E,CAAAI,YAAA,EAAM,EAC5E;;;;IADqBJ,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAC,IAAA,KAAAJ,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,KAAA,EAAiD;;;;;IAI1ET,EADF,CAAAC,cAAA,cAA8D,YACrD;IAAAD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC1BJ,EAAA,CAAAC,cAAA,cAA0C;IAAAD,EAAA,CAAAG,MAAA,GAAkC;IAC9EH,EAD8E,CAAAI,YAAA,EAAM,EAC9E;;;;IADsCJ,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAG,WAAA,CAAkC;;;;;IAMxEb,EADF,CAAAC,cAAA,cAA0D,YACjD;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvBJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAA+B;IAC1DH,EAD0D,CAAAI,YAAA,EAAM,EAC1D;;;;IADqBJ,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAI,SAAA,CAA+B;;;;;IAGxDd,EADF,CAAAC,cAAA,cAA0F,YACjF;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvBJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAoF;;IAC/GH,EAD+G,CAAAI,YAAA,EAAM,EAC/G;;;;IADqBJ,EAAA,CAAAK,SAAA,GAAoF;IAApFL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAAe,WAAA,OAAAR,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAO,WAAA,IAAAV,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAM,SAAA,gBAAoF;;;;;IAUjHhB,EADF,CAAAC,cAAA,cAAkE,YACzD;IAAAD,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAE5BJ,EADF,CAAAC,cAAA,cAA8B,WACtB;IAAAD,EAAA,CAAAG,MAAA,GAAqC;IAE/CH,EAF+C,CAAAI,YAAA,EAAO,EAC9C,EACF;;;;IAFIJ,EAAA,CAAAK,SAAA,GAAqC;IAArCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA4E,cAAA,CAAqC;;;;;IAK7CtF,EADF,CAAAC,cAAA,cAAkH,YACzG;IAAAD,EAAA,CAAAG,MAAA,iBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACzBJ,EAAA,CAAAE,SAAA,qBAC8E;IAChFF,EAAA,CAAAI,YAAA,EAAM;;;;IAFQJ,EAAA,CAAAK,SAAA,GAAS;IACjBL,EADQ,CAAAmB,UAAA,UAAS,UAAU,cAAc,UAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA6E,SAAA,CACL;;;;;IAIxCvF,EADF,CAAAC,cAAA,cAA8D,YACrD;IAAAD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC1BJ,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAG,MAAA,GAAgC;IAC7DH,EAD6D,CAAAI,YAAA,EAAM,EAC7D;;;;IADuBJ,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA8E,UAAA,CAAgC;;;;;IAMzDxF,EAAA,CAAAC,cAAA,cAAkF;IAChFD,EAAA,CAAAE,SAAA,mBAA4F;IAC5FF,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAG,MAAA,GAAyD;IACnFH,EADmF,CAAAI,YAAA,EAAO,EACpF;;;;;;IAF2BJ,EAAA,CAAAK,SAAA,EAAiC;IAACL,EAAlC,CAAAmB,UAAA,cAAAZ,MAAA,CAAAkF,gBAAA,CAAAC,IAAA,EAAiC,gBAAgB;IACxD1F,EAAA,CAAAK,SAAA,GAAyD;IAAzDL,EAAA,CAAAM,iBAAA,CAAAqF,OAAA,CAAAC,QAAA,IAAAD,OAAA,CAAAhF,IAAA,0BAAyD;;;;;IAJrFX,EADF,CAAAC,cAAA,cAA8F,YACrF;IAAAD,EAAA,CAAAG,MAAA,qBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC7BJ,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAuB,UAAA,IAAAsE,6DAAA,kBAAkF;IAKtF7F,EADE,CAAAI,YAAA,EAAM,EACF;;;;IALoBJ,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAAmB,UAAA,YAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAoF,KAAA,CAA2B;;;;;IAlDrD9F,EAFJ,CAAAC,cAAA,cAAyE,cAC5C,SACrB;IAAAD,EAAA,CAAAG,MAAA,6BAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAO/BJ,EALA,CAAAuB,UAAA,IAAAwE,sDAAA,kBAA6E,IAAAC,sDAAA,kBAKf;IAM5DhG,EADF,CAAAC,cAAA,cAAwB,cACA;IAKpBD,EAJA,CAAAuB,UAAA,IAAA0E,sDAAA,kBAA0D,IAAAC,sDAAA,kBAIgC;IAMhGlG,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;IAGJJ,EADF,CAAAC,cAAA,eAA2B,UACrB;IAAAD,EAAA,CAAAG,MAAA,oCAA4B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAoBrCJ,EAlBA,CAAAuB,UAAA,KAAA4E,uDAAA,kBAAkE,KAAAC,uDAAA,kBAOgD,KAAAC,uDAAA,kBAMpD,KAAAC,uDAAA,kBAKgC;IAUlGtG,EADE,CAAAI,YAAA,EAAM,EACF;;;;IAvDwBJ,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAC,IAAA,KAAAJ,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,KAAA,EAAiD;IAKjDT,EAAA,CAAAK,SAAA,EAAkC;IAAlCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAG,WAAA,CAAkC;IAOhCb,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAI,SAAA,CAAgC;IAIhCd,EAAA,CAAAK,SAAA,EAAgE;IAAhEL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAO,WAAA,IAAAV,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAM,SAAA,CAAgE;IAWjEhB,EAAA,CAAAK,SAAA,GAAqC;IAArCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA4E,cAAA,CAAqC;IAOrCtF,EAAA,CAAAK,SAAA,EAAqF;IAArFL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA6E,SAAA,KAAAjC,SAAA,IAAA/C,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA6E,SAAA,UAAqF;IAMrFvF,EAAA,CAAAK,SAAA,EAAiC;IAAjCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA8E,UAAA,CAAiC;IAKjCxF,EAAA,CAAAK,SAAA,EAAiE;IAAjEL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAoF,KAAA,IAAAvF,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAoF,KAAA,CAAAS,MAAA,KAAiE;;;;;IAkB1FvG,EADF,CAAAC,cAAA,cAA6E,YACpE;IAAAD,EAAA,CAAAG,MAAA,qBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC7BJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAiD;IAC5EH,EAD4E,CAAAI,YAAA,EAAM,EAC5E;;;;IADqBJ,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAM,iBAAA,EAAAC,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,KAAA,KAAAF,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAC,IAAA,CAAiD;;;;;IAI1EX,EADF,CAAAC,cAAA,cAA8D,YACrD;IAAAD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC1BJ,EAAA,CAAAC,cAAA,cAA0C;IAAAD,EAAA,CAAAG,MAAA,GAAkC;IAC9EH,EAD8E,CAAAI,YAAA,EAAM,EAC9E;;;;IADsCJ,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAG,WAAA,CAAkC;;;;;IAMxEb,EADF,CAAAC,cAAA,cAA0D,YACjD;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvBJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAA+B;IAC1DH,EAD0D,CAAAI,YAAA,EAAM,EAC1D;;;;IADqBJ,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAI,SAAA,CAA+B;;;;;IAGxDd,EADF,CAAAC,cAAA,cAA0F,YACjF;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvBJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAoF;;IAC/GH,EAD+G,CAAAI,YAAA,EAAM,EAC/G;;;;IADqBJ,EAAA,CAAAK,SAAA,GAAoF;IAApFL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAAe,WAAA,OAAAR,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAO,WAAA,IAAAV,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAM,SAAA,gBAAoF;;;;;IAUjHhB,EADF,CAAAC,cAAA,cAA2D,YAClD;IAAAD,EAAA,CAAAG,MAAA,0BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAClCJ,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAG,MAAA,GAA8B;IAC1DH,EAD0D,CAAAI,YAAA,EAAM,EAC1D;;;;IADsBJ,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAmD,OAAA,CAA8B;;;;;IAGxD7D,EADD,CAAAC,cAAA,cAA+D,YACvD;IAAAD,EAAA,CAAAG,MAAA,uBAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC/BJ,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAG,MAAA,GAAkC;IAC9DH,EAD8D,CAAAI,YAAA,EAAM,EAC9D;;;;IADsBJ,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA8F,WAAA,CAAkC;;;;;IAR9DxG,EADF,CAAAC,cAAA,cAA8F,SACxF;IAAAD,EAAA,CAAAG,MAAA,8BAAuB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAM/BJ,EAJD,CAAAuB,UAAA,IAAAkF,6DAAA,kBAA2D,IAAAC,6DAAA,kBAIK;IAIlE1G,EAAA,CAAAI,YAAA,EAAM;;;;IARuBJ,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAmD,OAAA,CAA8B;IAI7B7D,EAAA,CAAAK,SAAA,EAAkC;IAAlCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA8F,WAAA,CAAkC;;;;;IAjC9DxG,EAFJ,CAAAC,cAAA,cAAyE,cAC5C,SACrB;IAAAD,EAAA,CAAAG,MAAA,wBAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAO1BJ,EALA,CAAAuB,UAAA,IAAAoF,sDAAA,kBAA6E,IAAAC,sDAAA,kBAKf;IAM5D5G,EADF,CAAAC,cAAA,cAAwB,cACA;IAKpBD,EAJA,CAAAuB,UAAA,IAAAsF,sDAAA,kBAA0D,IAAAC,sDAAA,kBAIgC;IAMhG9G,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;IAENJ,EAAA,CAAAuB,UAAA,KAAAwF,uDAAA,kBAA8F;IAYhG/G,EAAA,CAAAI,YAAA,EAAM;;;;IApCwBJ,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAmB,UAAA,UAAAZ,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,KAAA,KAAAF,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAC,IAAA,CAAiD;IAKjDX,EAAA,CAAAK,SAAA,EAAkC;IAAlCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAG,WAAA,CAAkC;IAOhCb,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAI,SAAA,CAAgC;IAIhCd,EAAA,CAAAK,SAAA,EAAgE;IAAhEL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAO,WAAA,IAAAV,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAM,SAAA,CAAgE;IAQjEhB,EAAA,CAAAK,SAAA,EAA8D;IAA9DL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA8F,WAAA,IAAAjG,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAmD,OAAA,CAA8D;;;;;IAlU/F7D,EAAA,CAAAC,cAAA,UAAwD;IAsStDD,EApSA,CAAAuB,UAAA,IAAAyF,gDAAA,oBAAiE,IAAAC,gDAAA,mBAgIF,IAAAC,gDAAA,mBA2CI,IAAAC,gDAAA,mBA2DM,IAAAC,gDAAA,mBA8DA;IAyC3EpH,EAAA,CAAAI,YAAA,EAAM;;;;IA7UEJ,EAAA,CAAAK,SAAA,EAAmC;IAAnCL,EAAA,CAAAmB,UAAA,UAAAZ,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAA6G,IAAA,cAAmC;IAgInCrH,EAAA,CAAAK,SAAA,EAAkC;IAAlCL,EAAA,CAAAmB,UAAA,UAAAZ,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAA6G,IAAA,aAAkC;IA2ClCrH,EAAA,CAAAK,SAAA,EAAoC;IAApCL,EAAA,CAAAmB,UAAA,UAAAZ,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAA6G,IAAA,eAAoC;IA2DpCrH,EAAA,CAAAK,SAAA,EAAuC;IAAvCL,EAAA,CAAAmB,UAAA,UAAAZ,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAA6G,IAAA,kBAAuC;IA8DvCrH,EAAA,CAAAK,SAAA,EAAuC;IAAvCL,EAAA,CAAAmB,UAAA,UAAAZ,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAA6G,IAAA,kBAAuC;;;;;IA6C7CrH,EADF,CAAAC,cAAA,cAAsD,QACjD;IAAAD,EAAA,CAAAG,MAAA,GAAuB;IAC5BH,EAD4B,CAAAI,YAAA,EAAI,EAC1B;;;;IADDJ,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAA8G,KAAA,CAAuB;;;AD3U9B,WAAaC,2BAA2B;EAAlC,MAAOA,2BAA2B;IAC7B/G,WAAW,GAAQ,IAAI;IACvBgH,YAAY;IAErBhE,cAAc,GAAG,KAAK;IAEtBT,mBAAmBA,CAAA;MACjB,IAAI,CAACS,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC5C;IAEAiE,aAAaA,CAACC,KAAU;MACtB,IAAI,CAACF,YAAY,EAAE;IACrB;IAEAG,mBAAmBA,CAACjH,IAAS;MAC3B,MAAMkH,aAAa,GAAG,CACpB,IAAI,EACJ,MAAM,EACN,aAAa,EACb,WAAW,EACX,cAAc,EACd,YAAY,CACb;MACD,OAAOC,MAAM,CAACC,IAAI,CAACpH,IAAI,CAAC,CACrBqH,MAAM,CAAEC,GAAG,IAAK,CAACJ,aAAa,CAACK,QAAQ,CAACD,GAAG,CAAC,IAAItH,IAAI,CAACsH,GAAG,CAAC,IAAI,IAAI,CAAC,CAClEE,GAAG,CAAEF,GAAG,KAAM;QAAEA,GAAG;QAAEG,KAAK,EAAEzH,IAAI,CAACsH,GAAG;MAAC,CAAE,CAAC,CAAC;IAC9C;IAEAvC,gBAAgBA,CAAC2C,KAAa;MAC5B,MAAMC,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MACtE,OAAOA,MAAM,CAACD,KAAK,GAAGC,MAAM,CAAC9B,MAAM,CAAC;IACtC;IAEA+B,cAAcA,CAAA;MACZ,MAAMjB,IAAI,GAAG,IAAI,CAAC7G,WAAW,EAAE6G,IAAI;MACnC,QAAQA,IAAI;QACV,KAAK,OAAO;UACV,OAAO,YAAY;QACrB,KAAK,MAAM;UACT,OAAO,WAAW;QACpB,KAAK,QAAQ;UACX,OAAO,aAAa;QACtB,KAAK,WAAW;UACd,OAAO,qBAAqB;QAC9B,KAAK,WAAW;UACd,OAAO,gBAAgB;QACzB;UACE,OAAO,MAAM;MACjB;IACF;;uCAjDWE,2BAA2B;IAAA;;YAA3BA,2BAA2B;MAAAgB,SAAA;MAAAC,MAAA;QAAAhI,WAAA;QAAAgH,YAAA;MAAA;MAAAiB,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAhE,QAAA,WAAAiE,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCtBtC7I,EADF,CAAAC,cAAA,aAA2B,aACsB;UAAzBD,EAAA,CAAAyC,UAAA,mBAAAsG,0DAAA;YAAA,OAASD,GAAA,CAAAtB,YAAA,EAAc;UAAA,EAAC;UAACxH,EAAA,CAAAI,YAAA,EAAM;UACvDJ,EAAA,CAAAC,cAAA,2BAAgG;UAAnCD,EAAA,CAAAyC,UAAA,mBAAAuG,wEAAAC,MAAA;YAAA,OAASA,MAAA,CAAAC,eAAA,EAAwB;UAAA,EAAC;UAE7FlJ,EADF,CAAAC,cAAA,aAAyC,cACb;UAAAD,EAAA,CAAAG,MAAA,2BAAoB;UAAAH,EAAA,CAAAI,YAAA,EAAO;UACrDJ,EAAA,CAAAC,cAAA,kBAAoF;UAAzBD,EAAA,CAAAyC,UAAA,mBAAA0G,+DAAA;YAAA,OAASL,GAAA,CAAAtB,YAAA,EAAc;UAAA,EAAC;UACrFxH,EADsF,CAAAI,YAAA,EAAW,EAC3F;UACNJ,EAAA,CAAAC,cAAA,aAA2C;UA0VzCD,EAxVA,CAAAuB,UAAA,IAAA6H,0CAAA,iBAA0D,IAAAC,0CAAA,iBAMF,KAAAC,2CAAA,iBAkVF;UAQxDtJ,EALA,CAAAI,YAAA,EAAM,EAIc,EACd;;;UAvWqCJ,EAAA,CAAAK,SAAA,GAAiB;UAAjBL,EAAA,CAAAmB,UAAA,kBAAiB;UAOpDnB,EAAA,CAAAK,SAAA,GAA0B;UAA1BL,EAAA,CAAAmB,UAAA,SAAA2H,GAAA,CAAAtI,WAAA,kBAAAsI,GAAA,CAAAtI,WAAA,CAAA+I,OAAA,CAA0B;UAM1BvJ,EAAA,CAAAK,SAAA,EAAgD;UAAhDL,EAAA,CAAAmB,UAAA,UAAA2H,GAAA,CAAAtI,WAAA,kBAAAsI,GAAA,CAAAtI,WAAA,CAAAE,IAAA,OAAAoI,GAAA,CAAAtI,WAAA,kBAAAsI,GAAA,CAAAtI,WAAA,CAAA+I,OAAA,EAAgD;UAkVhDvJ,EAAA,CAAAK,SAAA,EAAwB;UAAxBL,EAAA,CAAAmB,UAAA,SAAA2H,GAAA,CAAAtI,WAAA,kBAAAsI,GAAA,CAAAtI,WAAA,CAAA8G,KAAA,CAAwB;;;qBDpV5BvH,qBAAqB,EACrBF,aAAa,EACbF,QAAQ,EACRD,YAAY,EAAA8J,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZ5J,eAAe,EACfF,mBAAmB;MAAA+J,MAAA;IAAA;;SAKVpC,2BAA2B;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}