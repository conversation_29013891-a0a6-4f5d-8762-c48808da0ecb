{"ast": null, "code": "import { inject, signal } from '@angular/core';\nimport { UserManagementService } from '../../services/user-management.service';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { IconComponent, AvaAutocompleteComponent, AvaOptionComponent, AvaTagComponent, AvaTextboxComponent, ButtonComponent, DropdownComponent, PopupComponent } from '@ava/play-comp-library';\nimport { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { OrgConfigService } from '../../../../org-config/services/org-config.service';\nimport { TokenStorageService } from '@shared/auth/services/token-storage.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nconst _c0 = () => ({\n  background: \"#E9F0FC\",\n  color: \"#2D3036\"\n});\nconst _c1 = () => [1, 2, 3];\nconst _c2 = () => ({\n  background: \"#E9F0FC\",\n  color: \"#11387C\",\n  border: \"1px solid #6898EB\"\n});\nconst _c3 = () => ({\n  background: \"#EDEDF3\",\n  color: \"#2D3036\"\n});\nconst _forTrack0 = ($index, $item) => $item.pageId;\nfunction AddUserComponent_Conditional_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"label\", 32);\n    i0.ɵɵtext(2, \"User Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"ava-textbox\", 33);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"error\", ctx_r0.getFieldError(\"email\"))(\"fullWidth\", false);\n  }\n}\nfunction AddUserComponent_Conditional_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵelement(1, \"div\", 34)(2, \"div\", 35);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddUserComponent_Conditional_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"label\", 36);\n    i0.ɵɵtext(2, \"Role\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ava-dropdown\", 37);\n    i0.ɵɵlistener(\"selectionChange\", function AddUserComponent_Conditional_10_Template_ava_dropdown_selectionChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onRoleSelectionChange($event));\n    });\n    i0.ɵɵelementStart(4, \"ava-option\", 38);\n    i0.ɵɵlistener(\"click\", function AddUserComponent_Conditional_10_Template_ava_option_click_4_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.addNewRole());\n    });\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6, \"Create New Role +\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"options\", ctx_r0.roleList())(\"error\", ctx_r0.getFieldError(\"role\"))(\"selectedValue\", ctx_r0.selectedRoleValue);\n  }\n}\nfunction AddUserComponent_Conditional_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵelement(1, \"div\", 34)(2, \"div\", 39);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddUserComponent_Conditional_15_For_1_For_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ava-tag\", 47);\n    i0.ɵɵlistener(\"removed\", function AddUserComponent_Conditional_15_For_1_For_9_Template_ava_tag_removed_0_listener() {\n      const tag_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const page_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.removeTag(tag_r6, page_r4.pageId.toString()));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tag_r6 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"label\", tag_r6.name)(\"customStyle\", i0.ɵɵpureFunction0(4, _c0))(\"pill\", true)(\"removable\", ctx_r0.selectedTagRemovable());\n  }\n}\nfunction AddUserComponent_Conditional_15_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"label\", 41);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ava-dropdown\", 42);\n    i0.ɵɵlistener(\"selectionChange\", function AddUserComponent_Conditional_15_For_1_Template_ava_dropdown_selectionChange_3_listener($event) {\n      const page_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onSelectionChange($event, page_r4.pageId.toString()));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 43)(5, \"label\", 44);\n    i0.ɵɵtext(6, \"Selected Values\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 45);\n    i0.ɵɵrepeaterCreate(8, AddUserComponent_Conditional_15_For_1_For_9_Template, 1, 5, \"ava-tag\", 46, i0.ɵɵrepeaterTrackByIdentity);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const page_r4 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r4.pageName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", page_r4.pageId)(\"disabled\", false)(\"selectedValues\", ctx_r0.selectedValues[page_r4.pageId.toString()])(\"options\", ctx_r0.actionList())(\"checkboxOptions\", ctx_r0.actionCheckboxList())(\"disabled\", !ctx_r0.isAccessControl());\n    i0.ɵɵadvance(5);\n    i0.ɵɵrepeater(ctx_r0.selectedTags[page_r4.pageId.toString()]);\n  }\n}\nfunction AddUserComponent_Conditional_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵrepeaterCreate(0, AddUserComponent_Conditional_15_For_1_Template, 10, 7, \"div\", 40, _forTrack0);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵrepeater(ctx_r0.pageList());\n  }\n}\nfunction AddUserComponent_Conditional_16_For_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵelement(1, \"div\", 49)(2, \"div\", 39)(3, \"div\", 50);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddUserComponent_Conditional_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵrepeaterCreate(1, AddUserComponent_Conditional_16_For_2_Template, 4, 0, \"div\", 48, i0.ɵɵrepeaterTrackByIdentity);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(i0.ɵɵpureFunction0(0, _c1));\n  }\n}\nfunction AddUserComponent_Conditional_21_For_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ava-tag\", 56);\n    i0.ɵɵlistener(\"removed\", function AddUserComponent_Conditional_21_For_5_Template_ava_tag_removed_0_listener() {\n      const tag_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.removeRealmTag(tag_r9.value));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tag_r9 = ctx.$implicit;\n    i0.ɵɵproperty(\"label\", tag_r9.label)(\"customStyle\", i0.ɵɵpureFunction0(4, _c3))(\"pill\", true)(\"removable\", true);\n  }\n}\nfunction AddUserComponent_Conditional_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"ava-autocomplete\", 52);\n    i0.ɵɵlistener(\"optionSelected\", function AddUserComponent_Conditional_21_Template_ava_autocomplete_optionSelected_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onOptionSelected($event));\n    })(\"valueChange\", function AddUserComponent_Conditional_21_Template_ava_autocomplete_valueChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onValueChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"ava-button\", 53);\n    i0.ɵɵlistener(\"userClick\", function AddUserComponent_Conditional_21_Template_ava_button_userClick_2_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.openRealmPopup());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"div\", 54);\n    i0.ɵɵrepeaterCreate(4, AddUserComponent_Conditional_21_For_5_Template, 1, 5, \"ava-tag\", 55, i0.ɵɵrepeaterTrackByIdentity);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r0.realmsList())(\"showDefaultOptions\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"customStyles\", i0.ɵɵpureFunction0(4, _c2))(\"width\", \"100%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵrepeater(ctx_r0.selectedRealmTag);\n  }\n}\nfunction AddUserComponent_Conditional_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵelement(1, \"div\", 57)(2, \"div\", 58);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddUserComponent_Conditional_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"div\", 24)(2, \"label\", 60);\n    i0.ɵɵtext(3, \"Name of the Realm\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"ava-textbox\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 59)(7, \"div\", 24)(8, \"label\", 60);\n    i0.ɵɵtext(9, \"Choose Organization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"ava-dropdown\", 62);\n    i0.ɵɵlistener(\"selectionChange\", function AddUserComponent_Conditional_29_Template_ava_dropdown_selectionChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onOrgSelect($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 24)(12, \"label\", 60);\n    i0.ɵɵtext(13, \"Choose Domain\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"ava-dropdown\", 62);\n    i0.ɵɵlistener(\"selectionChange\", function AddUserComponent_Conditional_29_Template_ava_dropdown_selectionChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onDomainSelect($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 59)(16, \"div\", 24)(17, \"label\", 60);\n    i0.ɵɵtext(18, \"Choose Project\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"ava-dropdown\", 62);\n    i0.ɵɵlistener(\"selectionChange\", function AddUserComponent_Conditional_29_Template_ava_dropdown_selectionChange_19_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onProjectSelect($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 24)(21, \"label\", 60);\n    i0.ɵɵtext(22, \"Choose Team\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"ava-dropdown\", 62);\n    i0.ɵɵlistener(\"selectionChange\", function AddUserComponent_Conditional_29_Template_ava_dropdown_selectionChange_23_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onTeamSelect($event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"required\", true)(\"fullWidth\", false);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"dropdownTitle\", \"Select Organization\")(\"options\", ctx_r0.orgOptions)(\"selectedValue\", ctx_r0.selectedOrgName)(\"disabled\", false)(\"search\", true)(\"enableSearch\", true);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"dropdownTitle\", \"Select Domain\")(\"options\", ctx_r0.domainOptions)(\"selectedValue\", ctx_r0.selectedDomainName)(\"disabled\", !ctx_r0.selectedOrg)(\"search\", true)(\"enableSearch\", true);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"dropdownTitle\", \"Select Project\")(\"options\", ctx_r0.projectOptions)(\"selectedValue\", ctx_r0.selectedProjectName)(\"disabled\", !ctx_r0.selectedDomain)(\"search\", true)(\"enableSearch\", true);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"dropdownTitle\", \"Select Team\")(\"options\", ctx_r0.teamOptions)(\"selectedValue\", ctx_r0.selectedTeamName)(\"disabled\", !ctx_r0.selectedProject)(\"search\", true)(\"enableSearch\", true);\n  }\n}\nfunction AddUserComponent_Conditional_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"div\", 24);\n    i0.ɵɵelement(2, \"div\", 34)(3, \"div\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 59)(6, \"div\", 24);\n    i0.ɵɵelement(7, \"div\", 34)(8, \"div\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 24);\n    i0.ɵɵelement(10, \"div\", 34)(11, \"div\", 39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 59)(13, \"div\", 24);\n    i0.ɵɵelement(14, \"div\", 34)(15, \"div\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 24);\n    i0.ɵɵelement(17, \"div\", 34)(18, \"div\", 39);\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let AddUserComponent = /*#__PURE__*/(() => {\n  class AddUserComponent {\n    userManagementForm;\n    addRealmForm;\n    addRoleForm;\n    roleList = signal([]);\n    pageList = signal([]);\n    actionList = signal([]);\n    actionCheckboxList = signal([]);\n    realmsList = signal([]);\n    selectedTags = {};\n    selectedRealmTag = [];\n    selectedValues = {};\n    selectedRealmsTag = [];\n    showRealmPopup = signal(false);\n    showCreateRolePopup = signal(false);\n    hierarchyData = signal([]);\n    showStatusPopup = signal(false);\n    isAccessControl = signal(false);\n    accessControlList = signal([]);\n    selectedTagRemovable = signal(true);\n    selectedRoleValue = '';\n    userId;\n    showUserSuccessPopup = false;\n    showErrorPopup = false;\n    userSuccessMessage = '';\n    statusSuccessMessage = '';\n    popupErrorMessage = '';\n    orgOptions = [];\n    domainOptions = [];\n    projectOptions = [];\n    teamOptions = [];\n    selectedOrgName = '';\n    selectedDomainName = '';\n    selectedProjectName = '';\n    selectedTeamName = '';\n    selectedOrg = '';\n    selectedDomain = '';\n    selectedProject = '';\n    selectedTeam = '';\n    userManagementService = inject(UserManagementService);\n    orgConfigService = inject(OrgConfigService);\n    router = inject(Router);\n    formBuilder = inject(FormBuilder);\n    tokenStorage = inject(TokenStorageService);\n    activatedRoute = inject(ActivatedRoute);\n    // Loading states for skeleton loaders\n    roleListLoading = signal(false);\n    pageListLoading = signal(false);\n    realmsListLoading = signal(false);\n    orgDataLoading = signal(false);\n    userDataLoading = signal(false);\n    ngOnInit() {\n      this.getRoleList();\n      this.getPageList();\n      this.getActionList();\n      this.getRealmsList();\n      this.getOrgList();\n      this.userManagementForm = this.getUserManagementForm();\n      this.addRealmForm = this.getAddRealmForm();\n      this.addRoleForm = this.getAddRoleForm();\n      this.activatedRoute.queryParams.subscribe(params => {\n        if (params?.id) {\n          this.userId = params.id;\n          this.getViewUser(params.id);\n        }\n      });\n    }\n    getViewUser(id) {\n      this.userDataLoading.set(true);\n      this.userManagementService.getUserDetails(id).subscribe({\n        next: res => {\n          this.userManagementForm.patchValue({\n            email: res.email,\n            role: res.roles[0].roleId\n          });\n          this.selectedRoleValue = res.roles[0].roleName;\n          this.onRoleSelectionChange(res.roles[0].roleId);\n          this.selectedRealmTag = res.realms.map(realm => ({\n            label: realm.realmName,\n            value: realm.realmId\n          }));\n          this.userDataLoading.set(false);\n        },\n        error: e => {\n          console.error(e);\n          this.userDataLoading.set(false);\n        }\n      });\n    }\n    getUserManagementForm() {\n      return this.formBuilder.group({\n        email: ['', [Validators.required]],\n        role: ['', [Validators.required]]\n      });\n    }\n    getAddRealmForm() {\n      return this.formBuilder.group({\n        realmName: [null, [Validators.required]],\n        orgId: [null, [Validators.required]],\n        domainId: [null, [Validators.required]],\n        projectId: [null, [Validators.required]],\n        teamId: [null, [Validators.required]]\n      });\n    }\n    getAddRoleForm() {\n      return this.formBuilder.group({\n        roleName: ['', [Validators.required]],\n        description: ['', [Validators.required]]\n      });\n    }\n    getRoleList() {\n      this.roleListLoading.set(true);\n      this.userManagementService.getAllRoles().subscribe({\n        next: userMgmtResponse => {\n          this.roleList.set(userMgmtResponse.map(opt => ({\n            name: opt.roleName,\n            value: opt.roleId\n          })));\n          this.roleListLoading.set(false);\n        },\n        error: e => {\n          console.error(e);\n          this.roleListLoading.set(false);\n        }\n      });\n    }\n    getPageList() {\n      this.pageListLoading.set(true);\n      this.userManagementService.getAllPages().subscribe({\n        next: pages => {\n          this.pageList.set(pages);\n          this.initializePageControl();\n          this.pageListLoading.set(false);\n        },\n        error: e => {\n          console.error(e);\n          this.pageListLoading.set(false);\n        }\n      });\n    }\n    getActionList() {\n      this.userManagementService.getAllActions().subscribe({\n        next: actions => {\n          this.actionList.set(actions.map(opt => ({\n            name: opt.actionName,\n            value: opt.actionId\n          })));\n          this.actionCheckboxList.set(actions.map(opt => opt.actionName));\n        },\n        error: e => console.error(e)\n      });\n    }\n    getRealmsList() {\n      this.realmsListLoading.set(true);\n      this.userManagementService.getAllRealms().subscribe({\n        next: realms => {\n          this.realmsList.set(realms.map(opt => ({\n            label: opt.realmName,\n            value: opt.realmId\n          })));\n          this.realmsListLoading.set(false);\n        },\n        error: e => {\n          console.error(e);\n          this.realmsListLoading.set(false);\n        }\n      });\n    }\n    getOrgList() {\n      this.orgDataLoading.set(true);\n      this.orgConfigService.getOrganizationHierarchy().subscribe({\n        next: data => {\n          this.hierarchyData.set(data);\n          this.loadOrganizations();\n          this.orgDataLoading.set(false);\n        },\n        error: e => {\n          console.error(e);\n          this.orgDataLoading.set(false);\n        }\n      });\n    }\n    loadOrganizations() {\n      this.orgOptions = this.hierarchyData().map(org => ({\n        name: org.organizationName,\n        value: org.orgId.toString()\n      }));\n    }\n    initializePageControl() {\n      this.pageList().forEach(page => {\n        this.userManagementForm.addControl(page.pageId, this.formBuilder.control([]));\n      });\n    }\n    getControl(name) {\n      return this.userManagementForm.get(name);\n    }\n    getSelectedValues(pageId) {\n      const control = this.userManagementForm.get(pageId);\n      if (!control || !control.value) {\n        return [];\n      }\n      return [control.value];\n    }\n    getFieldError(fieldName) {\n      // const field = this.knowledgeBaseForm.get(fieldName);\n      // // Capitalize only if first letter is not already uppercase\n      // const formattedFieldName = /^[A-Z]/.test(fieldName)\n      //   ? fieldName\n      //   : fieldName.charAt(0).toUpperCase() + fieldName.slice(1);\n      // if (field && field.invalid && (field.touched || field.dirty)) {\n      //   if (field.errors?.['required']) {\n      //     return `${formattedFieldName} is required`;\n      //   }\n      //   if (field.errors?.['email']) {\n      //     return 'Please enter a valid email address';\n      //   }\n      //   if (field.errors?.['minlength']) {\n      //     return `${formattedFieldName} must be at least ${field.errors['minlength'].requiredLength} characters long`;\n      //   }\n      // } else if(fieldName == 'scheme' || fieldName == 'embeddingModel') {\n      //    if (field && field.errors?.['required']) {\n      //     return `${formattedFieldName} is required`;\n      //   }\n      // }\n      return '';\n    }\n    onSelectionChange(event, formControl) {\n      const selectedValues = event.selectedOptions.map(option => option.value);\n      this.userManagementForm.get(formControl)?.setValue(selectedValues);\n      this.selectedTags[formControl] = event.selectedOptions;\n    }\n    addNewRole() {\n      this.showCreateRolePopup.set(true);\n    }\n    onValueChange(event) {}\n    onOptionSelected(event) {\n      const exists = this.selectedRealmTag.some(item => item.value === event.value);\n      if (!exists) {\n        this.selectedRealmTag.push(event);\n      }\n    }\n    removeRealmTag(tagValue) {\n      this.selectedRealmTag = this.selectedRealmTag.filter(tag => tag.value !== tagValue);\n    }\n    removeTag(event, formControl) {\n      this.selectedTags[formControl] = this.selectedTags[formControl].filter(option => option.value !== event.value);\n      const values = this.selectedTags[formControl].map(option => option.value);\n      const DropdownSelectedvalues = this.selectedTags[formControl].map(option => option.name);\n      this.userManagementForm.get(formControl)?.setValue(values);\n      this.selectedValues[formControl] = DropdownSelectedvalues;\n    }\n    onRemoveRealm() {}\n    openRealmPopup() {\n      this.showRealmPopup.set(true);\n    }\n    onOrgSelect(event) {\n      const selectedOrgId = event.selectedOptions?.[0]?.value;\n      const selectedOrgName = event.selectedOptions?.[0]?.name;\n      if (selectedOrgId) {\n        this.selectedOrg = selectedOrgId;\n        this.selectedOrgName = selectedOrgName;\n        this.addRealmForm.patchValue({\n          orgId: selectedOrgId,\n          domainId: '',\n          projectId: '',\n          teamId: ''\n        });\n        this.loadDomains(selectedOrgId);\n        this.selectedDomain = '';\n        this.selectedProject = '';\n        this.selectedTeam = '';\n        this.selectedDomainName = '';\n        this.selectedProjectName = '';\n        this.selectedTeamName = '';\n        this.projectOptions = [];\n        this.teamOptions = [];\n      }\n    }\n    loadDomains(orgId) {\n      const org = this.hierarchyData().find(o => o.orgId.toString() === orgId);\n      if (org) {\n        this.domainOptions = org.domains.map(domain => ({\n          name: domain.domainName,\n          value: domain.domainId.toString()\n        }));\n      } else {\n        this.domainOptions = [];\n      }\n    }\n    onDomainSelect(event) {\n      const selectedDomainId = event.selectedOptions?.[0]?.value;\n      const selectedDomainName = event.selectedOptions?.[0]?.name;\n      if (selectedDomainId) {\n        this.selectedDomain = selectedDomainId;\n        this.selectedDomainName = selectedDomainName;\n        this.addRealmForm.patchValue({\n          domainId: selectedDomainId,\n          projectId: '',\n          teamId: ''\n        });\n        this.loadProjects(selectedDomainId);\n        this.selectedProject = '';\n        this.selectedTeam = '';\n        this.selectedProjectName = '';\n        this.selectedTeamName = '';\n        this.teamOptions = [];\n      }\n    }\n    loadProjects(domainId) {\n      const org = this.hierarchyData().find(o => o.domains.some(d => d.domainId.toString() === domainId));\n      if (org) {\n        const domain = org.domains.find(d => d.domainId.toString() === domainId);\n        if (domain) {\n          this.projectOptions = domain.projects.map(project => ({\n            name: project.projectName,\n            value: project.projectId.toString()\n          }));\n        } else {\n          this.projectOptions = [];\n        }\n      } else {\n        this.projectOptions = [];\n      }\n    }\n    onProjectSelect(event) {\n      const selectedProjectId = event.selectedOptions?.[0]?.value;\n      const selectedProjectName = event.selectedOptions?.[0]?.name;\n      if (selectedProjectId) {\n        this.selectedProject = selectedProjectId;\n        this.selectedProjectName = selectedProjectName;\n        this.addRealmForm.patchValue({\n          projectId: selectedProjectId,\n          team: ''\n        });\n        this.loadTeams(selectedProjectId);\n        this.selectedTeam = '';\n        this.selectedTeamName = '';\n      }\n    }\n    loadTeams(projectId) {\n      const org = this.hierarchyData().find(o => o.domains.some(d => d.projects.some(p => p.projectId.toString() === projectId)));\n      if (org) {\n        const domain = org.domains.find(d => d.projects.some(p => p.projectId.toString() === projectId));\n        if (domain) {\n          const project = domain.projects.find(p => p.projectId.toString() === projectId);\n          if (project) {\n            this.teamOptions = project.teams.map(team => ({\n              name: team.teamName,\n              value: team.teamId.toString()\n            }));\n          } else {\n            this.teamOptions = [];\n          }\n        } else {\n          this.teamOptions = [];\n        }\n      } else {\n        this.teamOptions = [];\n      }\n    }\n    onTeamSelect(event) {\n      const selectedTeamId = event.selectedOptions?.[0]?.value;\n      const selectedTeamName = event.selectedOptions?.[0]?.name;\n      if (selectedTeamId) {\n        this.selectedTeam = selectedTeamId;\n        this.selectedTeamName = selectedTeamName;\n        this.addRealmForm.patchValue({\n          teamId: selectedTeamId\n        });\n      }\n    }\n    createRole() {\n      if (this.addRoleForm.valid) {\n        const payload = this.addRoleForm.value;\n        this.userManagementService.createRole(payload).subscribe({\n          next: res => {\n            this.roleList.update(currentList => [...currentList, {\n              name: res.roleName,\n              value: res.roleId\n            }]);\n            this.selectedRoleValue = res.roleName;\n            this.onRoleSelectionChange(res.roleId);\n            this.closeRealmPopup();\n            this.addRoleForm.reset();\n            this.statusSuccessMessage = 'The Role has been successfully created!';\n            this.showStatusPopup.set(true);\n          },\n          error: e => console.error(e)\n        });\n      }\n    }\n    createRealm() {\n      if (this.addRealmForm.valid) {\n        const {\n          realmName,\n          teamId\n        } = this.addRealmForm.value;\n        this.userManagementService.createRealm(realmName, teamId).subscribe({\n          next: res => {\n            this.selectedRealmTag.push({\n              label: res.realmName,\n              value: res.realmId\n            });\n            this.closeRealmPopup();\n            this.statusSuccessMessage = 'The Realm has been successfully created!';\n            this.showStatusPopup.set(true);\n          },\n          error: e => console.error(e)\n        });\n      }\n    }\n    closeRealmPopup() {\n      this.showRealmPopup.set(false);\n      this.showCreateRolePopup.set(false);\n      this.addRoleForm.reset();\n    }\n    closeSuccessPopup() {\n      this.showStatusPopup.set(false);\n    }\n    onRoleSelectionChange(event) {\n      const roleId = event?.selectedOptions?.[0]?.value ?? event;\n      this.userManagementForm.get('role')?.patchValue(roleId);\n      this.pageListLoading.set(true);\n      this.userManagementService.getExistingAccessControl(roleId).subscribe({\n        next: res => {\n          this.resetAccessControll();\n          this.accessControlList.set(res.accessControl);\n          if (!res.accessControl?.length) {\n            this.isAccessControl.set(true);\n            this.selectedTagRemovable.set(true);\n            return;\n          }\n          this.isAccessControl.set(false);\n          this.selectedTagRemovable.set(false);\n          this.updatedUserManagementFormValues();\n        },\n        error: e => console.error(e),\n        complete: () => this.pageListLoading.set(false)\n      });\n    }\n    updatedUserManagementFormValues() {\n      const actionMap = new Map(this.accessControlList().map(item => [item.page, item.actions]));\n      const actionIdsMap = new Map(this.actionList().map(item => [item.name, item.value]));\n      this.pageList().forEach(page => {\n        const pageActions = this.accessControlList().find(pa => pa.page === page.pageName);\n        if (pageActions) {\n          this.selectedValues[page.pageId.toString()] = pageActions.actions;\n          this.selectedTags[page.pageId.toString()] = pageActions.actions.map(op => ({\n            name: op\n          }));\n        }\n        const formControlName = page.pageId.toString();\n        const formControl = this.userManagementForm.get(formControlName);\n        const actionNames = actionMap.get(page.pageName);\n        if (formControl && actionNames) {\n          const actionIds = actionNames.map(name => actionIdsMap.get(name)).filter(id => id !== undefined);\n          formControl.patchValue(actionIds);\n        }\n      });\n    }\n    resetAccessControll() {\n      this.pageList().forEach(page => {\n        const formControlName = page.pageId.toString();\n        this.selectedValues[formControlName] = [];\n        this.selectedTags[formControlName] = [];\n        this.userManagementForm.get(formControlName)?.patchValue([]);\n      });\n    }\n    onExit() {\n      this.router.navigate(['manage/admin-management']);\n    }\n    addNewUser() {\n      if (this.userManagementForm.valid && this.selectedRealmTag?.length) {\n        const {\n          email,\n          role,\n          ...pagePermissions\n        } = this.userManagementForm.value;\n        const roleId = parseInt(role);\n        const roleIds = [roleId];\n        const payload = {\n          email: email,\n          roleIds: roleIds,\n          realmIds: this.selectedRealmTag.map(realm => realm.value),\n          authorizedBy: this.tokenStorage.getDaUsername() || '<EMAIL>'\n        };\n        if (this.selectedTagRemovable()) {\n          const permissions = [];\n          Object.entries(pagePermissions).forEach(([pageId, actionIds]) => {\n            if (Array.isArray(actionIds) && actionIds.length > 0) {\n              actionIds.forEach(actionId => {\n                permissions.push({\n                  pageId: parseInt(pageId),\n                  actionId: actionId\n                });\n              });\n            }\n          });\n          payload.rolePermessionRequest = {\n            roleId: roleId,\n            permissions: permissions\n          };\n        }\n        if (this.userId) {\n          this.userManagementService.updateUser(payload, this.userId).subscribe({\n            next: res => {\n              this.userSuccessMessage = 'User update successfully';\n              this.showUserSuccessPopup = true;\n            },\n            error: e => {\n              console.error(e);\n              this.popupErrorMessage = e.error.message || e.error;\n              this.showErrorPopup = true;\n            }\n          });\n        } else {\n          this.userManagementService.addNewUser(payload).subscribe({\n            next: res => {\n              this.userSuccessMessage = 'User added successfully';\n              this.showUserSuccessPopup = true;\n            },\n            error: e => {\n              console.error(e);\n              this.popupErrorMessage = e.error.message || e.error;\n              this.showErrorPopup = true;\n            }\n          });\n        }\n      }\n    }\n    onUserSuccessConfirm() {\n      this.showUserSuccessPopup = false;\n      this.router.navigate(['manage/admin-management']);\n    }\n    isUserButtonEnabled() {\n      return this.userManagementForm.valid && this.selectedRealmTag?.length > 0;\n    }\n    closeErrorPopup() {\n      this.showErrorPopup = false;\n    }\n    static ɵfac = function AddUserComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AddUserComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AddUserComponent,\n      selectors: [[\"app-add-user\"]],\n      decls: 52,\n      vars: 65,\n      consts: [[1, \"user__management--wrapper\"], [1, \"user__management--header\"], [\"iconName\", \"arrow-left\", \"iconColor\", \"#4C515B\", 3, \"userClick\", \"cursor\", \"iconSize\"], [1, \"user__management--container\"], [3, \"formGroup\"], [1, \"basic__info--section\"], [1, \"input__field\", \"input\"], [1, \"input__field\", \"dropdown\"], [1, \"access__control--container\"], [1, \"skeleton-access-control\"], [1, \"realm-container\"], [1, \"required\"], [1, \"realm-section\"], [1, \"skeleton-realm-section\"], [1, \"button__container\", \"main\"], [\"label\", \"Exit\", \"variant\", \"secondary\", \"size\", \"large\", 1, \"action-button\", 3, \"userClick\", \"width\"], [\"label\", \"Add User\", \"variant\", \"primary\", \"size\", \"large\", 1, \"action-button\", 3, \"userClick\", \"width\", \"disabled\"], [\"message\", \"\", 3, \"show\", \"showTitle\", \"showHeaderIcon\", \"showClose\", \"showInlineMessage\", \"showConfirm\", \"showCancel\", \"popupWidth\"], [1, \"add__realm--container\"], [1, \"realm__form\", 3, \"formGroup\"], [1, \"button__container\"], [\"label\", \"Cancel\", \"variant\", \"secondary\", \"size\", \"large\", 1, \"action-button\", 3, \"userClick\", \"width\"], [\"label\", \"Create\", \"variant\", \"primary\", \"size\", \"large\", 1, \"action-button\", 3, \"userClick\", \"width\", \"disabled\"], [1, \"role__form-fields\"], [1, \"input__field--wrapper\"], [\"for\", \"roleName\", 1, \"filter-label\", \"required\"], [\"formControlName\", \"roleName\", \"id\", \"roleName\", \"name\", \"roleName\", \"placeholder\", \"Enter Role Name\", \"size\", \"md\", 3, \"required\", \"fullWidth\"], [\"for\", \"description\", 1, \"filter-label\", \"required\"], [\"formControlName\", \"description\", \"id\", \"description\", \"name\", \"description\", \"placeholder\", \"Enter Description\", \"size\", \"md\", 3, \"required\", \"fullWidth\"], [\"headerIconName\", \"check-circle\", \"iconColor\", \"#28a745\", 3, \"closed\", \"show\", \"title\", \"message\", \"showHeaderIcon\", \"showClose\", \"showCancel\"], [\"headerIconName\", \"check-circle\", \"iconColor\", \"#28a745\", 3, \"confirm\", \"closed\", \"show\", \"title\", \"message\", \"showHeaderIcon\", \"showClose\", \"showCancel\", \"showConfirm\", \"confirmButtonLabel\", \"confirmButtonVariant\", \"confirmButtonBackground\"], [\"headerIconName\", \"alert-circle\", \"iconColor\", \"#dc3545\", 3, \"confirm\", \"closed\", \"show\", \"title\", \"message\", \"showHeaderIcon\", \"showClose\", \"showCancel\", \"showConfirm\", \"confirmButtonLabel\", \"confirmButtonVariant\", \"confirmButtonBackground\"], [\"for\", \"email\", 1, \"filter-label\", \"required\"], [\"formControlName\", \"email\", \"id\", \"email\", \"name\", \"email\", \"placeholder\", \"Enter Email\", \"size\", \"md\", 3, \"error\", \"fullWidth\"], [1, \"skeleton-loader\", \"skeleton-label\"], [1, \"skeleton-loader\", \"skeleton-input\"], [\"for\", \"role\", 1, \"filter-label\", \"required\"], [\"id\", \"role\", \"dropdownTitle\", \"Select a Role\", 3, \"selectionChange\", \"options\", \"error\", \"selectedValue\"], [3, \"click\"], [1, \"skeleton-loader\", \"skeleton-dropdown\"], [1, \"access__control--section\"], [\"for\", \"page.pageId\", 1, \"title\"], [\"dropdownTitle\", \"Select a Category\", 1, \"dropdown\", 3, \"selectionChange\", \"id\", \"disabled\", \"selectedValues\", \"options\", \"checkboxOptions\"], [1, \"dropdown-tag\"], [1, \"title-sm\"], [1, \"tag--container\"], [\"size\", \"sm\", \"iconColor\", \"#000000\", 3, \"label\", \"customStyle\", \"pill\", \"removable\"], [\"size\", \"sm\", \"iconColor\", \"#000000\", 3, \"removed\", \"label\", \"customStyle\", \"pill\", \"removable\"], [1, \"skeleton-section\"], [1, \"skeleton-loader\", \"skeleton-section-label\"], [1, \"skeleton-loader\", \"skeleton-section-label\", 2, \"width\", \"80px\", \"margin-top\", \"12px\"], [1, \"realm__filter--section\"], [\"placeholder\", \"Search for a Realms\", \"startIcon\", \"search\", \"startIconColor\", \"#6b7280\", 3, \"optionSelected\", \"valueChange\", \"options\", \"showDefaultOptions\"], [\"label\", \"Add Realm\", \"variant\", \"primary\", \"size\", \"large\", 1, \"action-button\", 3, \"userClick\", \"customStyles\", \"width\"], [1, \"realm\", \"tag--container\"], [\"size\", \"sm\", \"iconColor\", \"#2D3036\", 3, \"label\", \"customStyle\", \"pill\", \"removable\"], [\"size\", \"sm\", \"iconColor\", \"#2D3036\", 3, \"removed\", \"label\", \"customStyle\", \"pill\", \"removable\"], [1, \"skeleton-loader\", \"skeleton-autocomplete\"], [1, \"skeleton-loader\", \"skeleton-button\"], [1, \"form-fields\"], [1, \"filter-label\", \"required\"], [\"formControlName\", \"realmName\", \"id\", \"realmName\", \"name\", \"realmName\", \"placeholder\", \"Enter Realm Name\", \"size\", \"md\", 1, \"input-field\", 3, \"required\", \"fullWidth\"], [3, \"selectionChange\", \"dropdownTitle\", \"options\", \"selectedValue\", \"disabled\", \"search\", \"enableSearch\"]],\n      template: function AddUserComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"ava-icon\", 2);\n          i0.ɵɵlistener(\"userClick\", function AddUserComponent_Template_ava_icon_userClick_2_listener() {\n            return ctx.onExit();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"h2\");\n          i0.ɵɵtext(4, \"Add User\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"div\", 3)(6, \"form\", 4)(7, \"div\", 5);\n          i0.ɵɵtemplate(8, AddUserComponent_Conditional_8_Template, 4, 2, \"div\", 6)(9, AddUserComponent_Conditional_9_Template, 3, 0, \"div\", 6)(10, AddUserComponent_Conditional_10_Template, 7, 3, \"div\", 7)(11, AddUserComponent_Conditional_11_Template, 3, 0, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"div\", 8)(13, \"h3\");\n          i0.ɵɵtext(14, \"Access Control\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(15, AddUserComponent_Conditional_15_Template, 2, 0)(16, AddUserComponent_Conditional_16_Template, 3, 1, \"div\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\", 10)(18, \"h3\", 11);\n          i0.ɵɵtext(19, \"Assign Realm\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"div\", 12);\n          i0.ɵɵtemplate(21, AddUserComponent_Conditional_21_Template, 6, 5)(22, AddUserComponent_Conditional_22_Template, 3, 0, \"div\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(23, \"div\", 14)(24, \"ava-button\", 15);\n          i0.ɵɵlistener(\"userClick\", function AddUserComponent_Template_ava_button_userClick_24_listener() {\n            return ctx.onExit();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"ava-button\", 16);\n          i0.ɵɵlistener(\"userClick\", function AddUserComponent_Template_ava_button_userClick_25_listener() {\n            return ctx.addNewUser();\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(26, \"ava-popup\", 17)(27, \"div\", 18)(28, \"form\", 19);\n          i0.ɵɵtemplate(29, AddUserComponent_Conditional_29_Template, 24, 26)(30, AddUserComponent_Conditional_30_Template, 19, 0);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"div\", 20)(32, \"ava-button\", 21);\n          i0.ɵɵlistener(\"userClick\", function AddUserComponent_Template_ava_button_userClick_32_listener() {\n            return ctx.closeRealmPopup();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"ava-button\", 22);\n          i0.ɵɵlistener(\"userClick\", function AddUserComponent_Template_ava_button_userClick_33_listener() {\n            return ctx.createRealm();\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(34, \"ava-popup\", 17)(35, \"div\", 18)(36, \"form\", 19)(37, \"div\", 23)(38, \"div\", 24)(39, \"label\", 25);\n          i0.ɵɵtext(40, \"Name of the Role\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(41, \"ava-textbox\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"div\", 24)(43, \"label\", 27);\n          i0.ɵɵtext(44, \"Description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(45, \"ava-textbox\", 28);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(46, \"div\", 20)(47, \"ava-button\", 21);\n          i0.ɵɵlistener(\"userClick\", function AddUserComponent_Template_ava_button_userClick_47_listener() {\n            return ctx.closeRealmPopup();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"ava-button\", 22);\n          i0.ɵɵlistener(\"userClick\", function AddUserComponent_Template_ava_button_userClick_48_listener() {\n            return ctx.createRole();\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(49, \"ava-popup\", 29);\n          i0.ɵɵlistener(\"closed\", function AddUserComponent_Template_ava_popup_closed_49_listener() {\n            return ctx.closeSuccessPopup();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"ava-popup\", 30);\n          i0.ɵɵlistener(\"confirm\", function AddUserComponent_Template_ava_popup_confirm_50_listener() {\n            return ctx.onUserSuccessConfirm();\n          })(\"closed\", function AddUserComponent_Template_ava_popup_closed_50_listener() {\n            return ctx.onUserSuccessConfirm();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"ava-popup\", 31);\n          i0.ɵɵlistener(\"confirm\", function AddUserComponent_Template_ava_popup_confirm_51_listener() {\n            return ctx.closeErrorPopup();\n          })(\"closed\", function AddUserComponent_Template_ava_popup_closed_51_listener() {\n            return ctx.closeErrorPopup();\n          });\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"cursor\", true)(\"iconSize\", 24);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"formGroup\", ctx.userManagementForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(!ctx.userDataLoading() ? 8 : 9);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(!ctx.roleListLoading() ? 10 : 11);\n          i0.ɵɵadvance(5);\n          i0.ɵɵconditional(!ctx.pageListLoading() ? 15 : 16);\n          i0.ɵɵadvance(6);\n          i0.ɵɵconditional(!ctx.realmsListLoading() ? 21 : 22);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"width\", \"100%\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"width\", \"100%\")(\"disabled\", !ctx.isUserButtonEnabled());\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"show\", ctx.showRealmPopup())(\"showTitle\", false)(\"showHeaderIcon\", false)(\"showClose\", false)(\"showInlineMessage\", false)(\"showConfirm\", false)(\"showCancel\", false)(\"popupWidth\", \"744px\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.addRealmForm);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(!ctx.orgDataLoading() ? 29 : 30);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"width\", \"100%\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"width\", \"100%\")(\"disabled\", !ctx.addRealmForm.valid);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"show\", ctx.showCreateRolePopup())(\"showTitle\", false)(\"showHeaderIcon\", false)(\"showClose\", false)(\"showInlineMessage\", false)(\"showConfirm\", false)(\"showCancel\", false)(\"popupWidth\", \"410px\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.addRoleForm);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"required\", true)(\"fullWidth\", false);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"required\", true)(\"fullWidth\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"width\", \"100%\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"width\", \"100%\")(\"disabled\", !ctx.addRoleForm.valid);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"show\", ctx.showStatusPopup())(\"title\", \"Success\")(\"message\", ctx.statusSuccessMessage)(\"showHeaderIcon\", true)(\"showClose\", true)(\"showCancel\", false);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"show\", ctx.showUserSuccessPopup)(\"title\", \"Success\")(\"message\", ctx.userSuccessMessage)(\"showHeaderIcon\", true)(\"showClose\", true)(\"showCancel\", false)(\"showConfirm\", true)(\"confirmButtonLabel\", \"OK\")(\"confirmButtonVariant\", \"primary\")(\"confirmButtonBackground\", \"#28a745\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"show\", ctx.showErrorPopup)(\"title\", \"Failed\")(\"message\", ctx.popupErrorMessage)(\"showHeaderIcon\", true)(\"showClose\", true)(\"showCancel\", false)(\"showConfirm\", true)(\"confirmButtonLabel\", \"OK\")(\"confirmButtonVariant\", \"primary\")(\"confirmButtonBackground\", \"#dc3545\");\n        }\n      },\n      dependencies: [ReactiveFormsModule, i1.ɵNgNoValidate, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.FormGroupDirective, i1.FormControlName, AvaTextboxComponent, DropdownComponent, AvaOptionComponent, AvaTagComponent, AvaAutocompleteComponent, ButtonComponent, PopupComponent, IconComponent],\n      styles: [\"[_nghost-%COMP%] {\\n  padding: 0 1rem;\\n  overflow: auto;\\n  scrollbar-width: none;\\n  -ms-overflow-style: none;\\n}\\n[_nghost-%COMP%]::-webkit-scrollbar {\\n  display: none;\\n}\\n\\n.user__management--header[_ngcontent-%COMP%] {\\n  margin: 2rem 0 1rem 0;\\n  display: flex;\\n  gap: 8px;\\n}\\n\\nh2[_ngcontent-%COMP%], \\nh3[_ngcontent-%COMP%] {\\n  color: #4c515b;\\n}\\n\\n.basic__info--section[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 24px;\\n}\\n\\n.filter-label[_ngcontent-%COMP%] {\\n  margin-bottom: 4px;\\n}\\n\\n.input[_ngcontent-%COMP%] {\\n  width: 412px;\\n}\\n\\n.dropdown[_ngcontent-%COMP%] {\\n  width: 336px;\\n}\\n\\n.access__control--container[_ngcontent-%COMP%] {\\n  margin-top: 32px;\\n}\\n\\n.access__control--section[_ngcontent-%COMP%] {\\n  display: flex;\\n  border-bottom: 1px solid #bbbec5;\\n  gap: 16px;\\n  align-items: center;\\n  padding: 24px 0 20px 0;\\n}\\n.access__control--section[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 500;\\n  font-family: Mulish;\\n  width: 180px;\\n  color: #000000;\\n}\\n\\n.access__control--section[_ngcontent-%COMP%]:first-of-type {\\n  padding: 0 0 20px 0;\\n}\\n\\n.tag--container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n}\\n\\n.dropdown-tag[_ngcontent-%COMP%] {\\n  padding: 0 0 0 40px;\\n}\\n\\n.realm-container[_ngcontent-%COMP%] {\\n  margin-top: 46px;\\n}\\n\\n.realm-section[_ngcontent-%COMP%] {\\n  height: 218px;\\n  border: 1px solid #bbbec5;\\n  border-radius: 1rem;\\n  padding: 1rem;\\n  margin: 24px 0 0 0;\\n}\\n.realm-section[_ngcontent-%COMP%]   .realm__filter--section[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n}\\n.realm-section[_ngcontent-%COMP%]   .realm__filter--section[_ngcontent-%COMP%]   ava-autocomplete[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.realm-section[_ngcontent-%COMP%]   .realm[_ngcontent-%COMP%] {\\n  margin: 24px 0 0 0;\\n}\\n\\n.title-sm[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #898e99;\\n  font-weight: 600;\\n}\\n\\n.realm__form[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-flow: column;\\n  gap: 16px;\\n}\\n\\n.form-fields[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 24px;\\n}\\n\\n.input__field--wrapper[_ngcontent-%COMP%] {\\n  flex: 1;\\n  width: 360px;\\n}\\n\\n.filter-label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-weight: 500;\\n  text-align: left;\\n  color: #14161f;\\n}\\n\\n.required[_ngcontent-%COMP%]::after {\\n  content: \\\" *\\\";\\n  color: red;\\n  font-weight: bold;\\n}\\n\\n.button__container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  justify-content: center;\\n  margin: 16px 0 16px 0;\\n}\\n.button__container.main[_ngcontent-%COMP%] {\\n  justify-content: flex-end;\\n}\\n\\n.role__form-fields[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-flow: column;\\n  gap: 16px;\\n}\\n\\n\\n\\n.skeleton-loader[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_shimmer 1.5s infinite ease-in-out;\\n  border-radius: 8px;\\n}\\n\\n.skeleton-input[_ngcontent-%COMP%] {\\n  height: 42px;\\n  width: 100%;\\n  margin-bottom: 8px;\\n}\\n\\n.skeleton-dropdown[_ngcontent-%COMP%] {\\n  height: 42px;\\n  width: 100%;\\n  margin-bottom: 8px;\\n}\\n\\n.skeleton-label[_ngcontent-%COMP%] {\\n  height: 16px;\\n  width: 120px;\\n  margin-bottom: 8px;\\n}\\n\\n.skeleton-access-control[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.skeleton-access-control[_ngcontent-%COMP%]   .skeleton-title[_ngcontent-%COMP%] {\\n  height: 20px;\\n  width: 150px;\\n  margin-bottom: 16px;\\n}\\n.skeleton-access-control[_ngcontent-%COMP%]   .skeleton-section[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.skeleton-access-control[_ngcontent-%COMP%]   .skeleton-section[_ngcontent-%COMP%]   .skeleton-section-label[_ngcontent-%COMP%] {\\n  height: 16px;\\n  width: 100px;\\n  margin-bottom: 8px;\\n}\\n\\n.skeleton-realm-section[_ngcontent-%COMP%]   .skeleton-autocomplete[_ngcontent-%COMP%] {\\n  height: 42px;\\n  width: 100%;\\n  margin-bottom: 12px;\\n}\\n.skeleton-realm-section[_ngcontent-%COMP%]   .skeleton-button[_ngcontent-%COMP%] {\\n  height: 42px;\\n  width: 100%;\\n  margin-bottom: 16px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_shimmer {\\n  0% {\\n    background-position: -200% 0;\\n  }\\n  100% {\\n    background-position: 200% 0;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return AddUserComponent;\n})();", "map": {"version": 3, "names": ["inject", "signal", "UserManagementService", "ActivatedRoute", "Router", "IconComponent", "AvaAutocompleteComponent", "AvaOptionComponent", "AvaTagComponent", "AvaTextboxComponent", "ButtonComponent", "DropdownComponent", "PopupComponent", "FormBuilder", "ReactiveFormsModule", "Validators", "OrgConfigService", "TokenStorageService", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "getFieldError", "ɵɵlistener", "AddUserComponent_Conditional_10_Template_ava_dropdown_selectionChange_3_listener", "$event", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "onRoleSelectionChange", "AddUserComponent_Conditional_10_Template_ava_option_click_4_listener", "addNewRole", "roleList", "selected<PERSON>ole<PERSON><PERSON>ue", "AddUserComponent_Conditional_15_For_1_For_9_Template_ava_tag_removed_0_listener", "tag_r6", "_r5", "$implicit", "page_r4", "removeTag", "pageId", "toString", "name", "ɵɵpureFunction0", "_c0", "selectedTagRemovable", "AddUserComponent_Conditional_15_For_1_Template_ava_dropdown_selectionChange_3_listener", "_r3", "onSelectionChange", "ɵɵrepeaterCreate", "AddUserComponent_Conditional_15_For_1_For_9_Template", "ɵɵrepeaterTrackByIdentity", "ɵɵtextInterpolate", "pageName", "<PERSON><PERSON><PERSON><PERSON>", "actionList", "actionCheckboxList", "isAccessControl", "ɵɵrepeater", "selectedTags", "AddUserComponent_Conditional_15_For_1_Template", "_forTrack0", "pageList", "AddUserComponent_Conditional_16_For_2_Template", "_c1", "AddUserComponent_Conditional_21_For_5_Template_ava_tag_removed_0_listener", "tag_r9", "_r8", "removeRealmTag", "value", "label", "_c3", "AddUserComponent_Conditional_21_Template_ava_autocomplete_optionSelected_1_listener", "_r7", "onOptionSelected", "AddUserComponent_Conditional_21_Template_ava_autocomplete_valueChange_1_listener", "onValueChange", "AddUserComponent_Conditional_21_Template_ava_button_userClick_2_listener", "openRealmPopup", "AddUserComponent_Conditional_21_For_5_Template", "realmsList", "_c2", "selectedRealmTag", "AddUserComponent_Conditional_29_Template_ava_dropdown_selectionChange_10_listener", "_r10", "onOrgSelect", "AddUserComponent_Conditional_29_Template_ava_dropdown_selectionChange_14_listener", "onDomainSelect", "AddUserComponent_Conditional_29_Template_ava_dropdown_selectionChange_19_listener", "onProjectSelect", "AddUserComponent_Conditional_29_Template_ava_dropdown_selectionChange_23_listener", "onTeamSelect", "orgOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "domainOptions", "selected<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "projectOptions", "selectedProjectName", "<PERSON><PERSON><PERSON><PERSON>", "teamOptions", "selectedTeamName", "selectedProject", "AddUserComponent", "userManagementForm", "addRealmForm", "addRoleForm", "selectedRealmsTag", "showRealmPopup", "showCreateRolePopup", "hierarchyData", "showStatusPopup", "accessControlList", "userId", "showUserSuccessPopup", "showErrorPopup", "userSuccessMessage", "statusSuccessMessage", "popupErrorMessage", "selectedTeam", "userManagementService", "orgConfigService", "router", "formBuilder", "tokenStorage", "activatedRoute", "roleListLoading", "pageListLoading", "realmsListLoading", "orgDataLoading", "userDataLoading", "ngOnInit", "getRoleList", "getPageList", "getActionList", "getRealmsList", "getOrgList", "getUserManagementForm", "getAddRealmForm", "getAddRoleForm", "queryParams", "subscribe", "params", "id", "getViewUser", "set", "getUserDetails", "next", "res", "patchValue", "email", "role", "roles", "roleId", "<PERSON><PERSON><PERSON>", "realms", "map", "realm", "realmName", "realmId", "error", "e", "console", "group", "required", "orgId", "domainId", "projectId", "teamId", "description", "getAllRoles", "userMgmtResponse", "opt", "getAllPages", "pages", "initializePageControl", "getAllActions", "actions", "actionName", "actionId", "getAllRealms", "getOrganizationHierarchy", "data", "loadOrganizations", "org", "organizationName", "for<PERSON>ach", "page", "addControl", "control", "getControl", "get", "getSelectedValues", "fieldName", "event", "formControl", "selectedOptions", "option", "setValue", "exists", "some", "item", "push", "tagValue", "filter", "tag", "values", "DropdownSelectedvalues", "onRemoveRealm", "selectedOrgId", "loadDomains", "find", "o", "domains", "domain", "domainName", "selectedDomainId", "loadProjects", "d", "projects", "project", "projectName", "selectedProjectId", "team", "loadTeams", "p", "teams", "teamName", "selectedTeamId", "createRole", "valid", "payload", "update", "currentList", "closeRealmPopup", "reset", "createRealm", "closeSuccessPopup", "getExistingAccessControl", "resetAccessControll", "accessControl", "length", "updatedUserManagementFormValues", "complete", "actionMap", "Map", "actionIdsMap", "pageActions", "pa", "op", "formControlName", "actionNames", "actionIds", "undefined", "onExit", "navigate", "addNewUser", "pagePermissions", "parseInt", "roleIds", "realmIds", "authorizedBy", "getDaUsername", "permissions", "Object", "entries", "Array", "isArray", "rolePermessionRequest", "updateUser", "message", "onUserSuccessConfirm", "isUserButtonEnabled", "closeError<PERSON><PERSON><PERSON>", "selectors", "decls", "vars", "consts", "template", "AddUserComponent_Template", "rf", "ctx", "AddUserComponent_Template_ava_icon_userClick_2_listener", "ɵɵtemplate", "AddUserComponent_Conditional_8_Template", "AddUserComponent_Conditional_9_Template", "AddUserComponent_Conditional_10_Template", "AddUserComponent_Conditional_11_Template", "AddUserComponent_Conditional_15_Template", "AddUserComponent_Conditional_16_Template", "AddUserComponent_Conditional_21_Template", "AddUserComponent_Conditional_22_Template", "AddUserComponent_Template_ava_button_userClick_24_listener", "AddUserComponent_Template_ava_button_userClick_25_listener", "AddUserComponent_Conditional_29_Template", "AddUserComponent_Conditional_30_Template", "AddUserComponent_Template_ava_button_userClick_32_listener", "AddUserComponent_Template_ava_button_userClick_33_listener", "AddUserComponent_Template_ava_button_userClick_47_listener", "AddUserComponent_Template_ava_button_userClick_48_listener", "AddUserComponent_Template_ava_popup_closed_49_listener", "AddUserComponent_Template_ava_popup_confirm_50_listener", "AddUserComponent_Template_ava_popup_closed_50_listener", "AddUserComponent_Template_ava_popup_confirm_51_listener", "AddUserComponent_Template_ava_popup_closed_51_listener", "ɵɵconditional", "i1", "ɵNgNoValidate", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "FormGroupDirective", "FormControlName", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\manage\\admin-management\\components\\add-user\\add-user.component.ts", "C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\manage\\admin-management\\components\\add-user\\add-user.component.html"], "sourcesContent": ["import { Component, inject, signal } from '@angular/core';\r\nimport { UserManagementService } from '../../services/user-management.service';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport {\r\n  IconComponent,\r\n  AvaAutocompleteComponent,\r\n  AvaO<PERSON>Component,\r\n  AvaTagComponent,\r\n  AvaTextboxComponent,\r\n  ButtonComponent,\r\n  DropdownComponent,\r\n  DropdownOption,\r\n  PopupComponent,\r\n} from '@ava/play-comp-library';\r\nimport {\r\n  FormBuilder,\r\n  FormControl,\r\n  FormGroup,\r\n  ReactiveFormsModule,\r\n  Validators,\r\n} from '@angular/forms';\r\nimport { DropdownActionOption, Page } from '../../models/user-management.model';\r\nimport { OrgConfigService } from '../../../../org-config/services/org-config.service';\r\nimport { TokenStorageService } from '@shared/auth/services/token-storage.service';\r\n@Component({\r\n  selector: 'app-add-user',\r\n  imports: [\r\n    ReactiveFormsModule,\r\n    AvaTextboxComponent,\r\n    DropdownComponent,\r\n    AvaO<PERSON>Component,\r\n    Ava<PERSON>agComponent,\r\n    AvaAutocompleteComponent,\r\n    ButtonComponent,\r\n    PopupComponent,\r\n    IconComponent,\r\n  ],\r\n  templateUrl: './add-user.component.html',\r\n  styleUrl: './add-user.component.scss',\r\n})\r\nexport class AddUserComponent {\r\n  userManagementForm!: FormGroup;\r\n  addRealmForm!: FormGroup;\r\n  addRoleForm!: FormGroup;\r\n  roleList = signal<DropdownOption[]>([]);\r\n  pageList = signal<Page[]>([]);\r\n  actionList = signal<DropdownOption[]>([]);\r\n  actionCheckboxList = signal<string[]>([]);\r\n  realmsList = signal<any[]>([]);\r\n  selectedTags: { [key: string]: DropdownActionOption[] } = {};\r\n  selectedRealmTag: any[] = [];\r\n  selectedValues: { [key: string]: string[] } = {};\r\n  selectedRealmsTag: any[] = [];\r\n  showRealmPopup = signal<boolean>(false);\r\n  showCreateRolePopup = signal<boolean>(false);\r\n  hierarchyData = signal<any[]>([]);\r\n  showStatusPopup = signal<boolean>(false);\r\n  isAccessControl = signal<boolean>(false);\r\n  accessControlList = signal<any[]>([]);\r\n  selectedTagRemovable = signal<boolean>(true);\r\n  selectedRoleValue: string = '';\r\n  userId!: number;\r\n  showUserSuccessPopup: boolean = false;\r\n  showErrorPopup: boolean = false;\r\n  userSuccessMessage: string = '';\r\n  statusSuccessMessage: string = '';\r\n  popupErrorMessage: string = '';\r\n\r\n  orgOptions: DropdownOption[] = [];\r\n  domainOptions: DropdownOption[] = [];\r\n  projectOptions: DropdownOption[] = [];\r\n  teamOptions: DropdownOption[] = [];\r\n\r\n  selectedOrgName: string = '';\r\n  selectedDomainName: string = '';\r\n  selectedProjectName: string = '';\r\n  selectedTeamName: string = '';\r\n\r\n  selectedOrg: string = '';\r\n  selectedDomain: string = '';\r\n  selectedProject: string = '';\r\n  selectedTeam: string = '';\r\n\r\n  private userManagementService = inject(UserManagementService);\r\n  private orgConfigService = inject(OrgConfigService);\r\n  private router = inject(Router);\r\n  private formBuilder = inject(FormBuilder);\r\n  private tokenStorage = inject(TokenStorageService);\r\n  private activatedRoute = inject(ActivatedRoute);\r\n\r\n  // Loading states for skeleton loaders\r\n  roleListLoading = signal<boolean>(false);\r\n  pageListLoading = signal<boolean>(false);\r\n  realmsListLoading = signal<boolean>(false);\r\n  orgDataLoading = signal<boolean>(false);\r\n  userDataLoading = signal<boolean>(false);\r\n\r\n  ngOnInit(): void {\r\n    this.getRoleList();\r\n    this.getPageList();\r\n    this.getActionList();\r\n    this.getRealmsList();\r\n    this.getOrgList();\r\n    this.userManagementForm = this.getUserManagementForm();\r\n    this.addRealmForm = this.getAddRealmForm();\r\n    this.addRoleForm = this.getAddRoleForm();\r\n    this.activatedRoute.queryParams.subscribe((params: any) => {\r\n      if (params?.id) {\r\n        this.userId = params.id;\r\n        this.getViewUser(params.id);\r\n      }\r\n    });\r\n  }\r\n\r\n  getViewUser(id: number) {\r\n    this.userDataLoading.set(true);\r\n    this.userManagementService.getUserDetails(id).subscribe({\r\n      next: (res: any) => {\r\n        this.userManagementForm.patchValue({\r\n          email: res.email,\r\n          role: res.roles[0].roleId,\r\n        });\r\n        this.selectedRoleValue = res.roles[0].roleName;\r\n        this.onRoleSelectionChange(res.roles[0].roleId);\r\n        this.selectedRealmTag = res.realms.map((realm: any) => ({\r\n          label: realm.realmName,\r\n          value: realm.realmId,\r\n        }));\r\n        this.userDataLoading.set(false);\r\n      },\r\n      error: (e) => {\r\n        console.error(e);\r\n        this.userDataLoading.set(false);\r\n      },\r\n    });\r\n  }\r\n\r\n  getUserManagementForm() {\r\n    return this.formBuilder.group({\r\n      email: ['', [Validators.required]],\r\n      role: ['', [Validators.required]],\r\n    });\r\n  }\r\n\r\n  getAddRealmForm() {\r\n    return this.formBuilder.group({\r\n      realmName: [null, [Validators.required]],\r\n      orgId: [null, [Validators.required]],\r\n      domainId: [null, [Validators.required]],\r\n      projectId: [null, [Validators.required]],\r\n      teamId: [null, [Validators.required]],\r\n    });\r\n  }\r\n\r\n  getAddRoleForm() {\r\n    return this.formBuilder.group({\r\n      roleName: ['', [Validators.required]],\r\n      description: ['', [Validators.required]],\r\n    });\r\n  }\r\n\r\n  getRoleList() {\r\n    this.roleListLoading.set(true);\r\n    this.userManagementService.getAllRoles().subscribe({\r\n      next: (userMgmtResponse: any) => {\r\n        this.roleList.set(\r\n          userMgmtResponse.map((opt: any) => ({\r\n            name: opt.roleName,\r\n            value: opt.roleId,\r\n          })),\r\n        );\r\n        this.roleListLoading.set(false);\r\n      },\r\n      error: (e) => {\r\n        console.error(e);\r\n        this.roleListLoading.set(false);\r\n      },\r\n    });\r\n  }\r\n\r\n  getPageList() {\r\n    this.pageListLoading.set(true);\r\n    this.userManagementService.getAllPages().subscribe({\r\n      next: (pages: any) => {\r\n        this.pageList.set(pages);\r\n        this.initializePageControl();\r\n        this.pageListLoading.set(false);\r\n      },\r\n      error: (e) => {\r\n        console.error(e);\r\n        this.pageListLoading.set(false);\r\n      },\r\n    });\r\n  }\r\n\r\n  getActionList() {\r\n    this.userManagementService.getAllActions().subscribe({\r\n      next: (actions: any) => {\r\n        this.actionList.set(\r\n          actions.map((opt: any) => ({\r\n            name: opt.actionName,\r\n            value: opt.actionId,\r\n          })),\r\n        );\r\n        this.actionCheckboxList.set(actions.map((opt: any) => opt.actionName));\r\n      },\r\n      error: (e) => console.error(e),\r\n    });\r\n  }\r\n\r\n  getRealmsList() {\r\n    this.realmsListLoading.set(true);\r\n    this.userManagementService.getAllRealms().subscribe({\r\n      next: (realms: any) => {\r\n        this.realmsList.set(\r\n          realms.map((opt: any) => ({\r\n            label: opt.realmName,\r\n            value: opt.realmId,\r\n          })),\r\n        );\r\n        this.realmsListLoading.set(false);\r\n      },\r\n      error: (e) => {\r\n        console.error(e);\r\n        this.realmsListLoading.set(false);\r\n      },\r\n    });\r\n  }\r\n\r\n  getOrgList() {\r\n    this.orgDataLoading.set(true);\r\n    this.orgConfigService.getOrganizationHierarchy().subscribe({\r\n      next: (data: any) => {\r\n        this.hierarchyData.set(data);\r\n        this.loadOrganizations();\r\n        this.orgDataLoading.set(false);\r\n      },\r\n      error: (e) => {\r\n        console.error(e);\r\n        this.orgDataLoading.set(false);\r\n      },\r\n    });\r\n  }\r\n\r\n  loadOrganizations(): void {\r\n    this.orgOptions = this.hierarchyData().map((org) => ({\r\n      name: org.organizationName,\r\n      value: org.orgId.toString(),\r\n    }));\r\n  }\r\n\r\n  initializePageControl() {\r\n    this.pageList().forEach((page: any) => {\r\n      this.userManagementForm.addControl(\r\n        page.pageId,\r\n        this.formBuilder.control([]),\r\n      );\r\n    });\r\n  }\r\n\r\n  getControl(name: string): FormControl {\r\n    return this.userManagementForm.get(name) as FormControl;\r\n  }\r\n\r\n  getSelectedValues(pageId: string): any[] {\r\n    const control = this.userManagementForm.get(pageId);\r\n    if (!control || !control.value) {\r\n      return [];\r\n    }\r\n    return [control.value];\r\n  }\r\n\r\n  getFieldError(fieldName: string): string {\r\n    // const field = this.knowledgeBaseForm.get(fieldName);\r\n    // // Capitalize only if first letter is not already uppercase\r\n    // const formattedFieldName = /^[A-Z]/.test(fieldName)\r\n    //   ? fieldName\r\n    //   : fieldName.charAt(0).toUpperCase() + fieldName.slice(1);\r\n    // if (field && field.invalid && (field.touched || field.dirty)) {\r\n    //   if (field.errors?.['required']) {\r\n    //     return `${formattedFieldName} is required`;\r\n    //   }\r\n    //   if (field.errors?.['email']) {\r\n    //     return 'Please enter a valid email address';\r\n    //   }\r\n    //   if (field.errors?.['minlength']) {\r\n    //     return `${formattedFieldName} must be at least ${field.errors['minlength'].requiredLength} characters long`;\r\n    //   }\r\n    // } else if(fieldName == 'scheme' || fieldName == 'embeddingModel') {\r\n    //    if (field && field.errors?.['required']) {\r\n    //     return `${formattedFieldName} is required`;\r\n    //   }\r\n    // }\r\n    return '';\r\n  }\r\n\r\n  onSelectionChange(event: any, formControl: string) {\r\n    const selectedValues = event.selectedOptions.map(\r\n      (option: any) => option.value,\r\n    );\r\n    this.userManagementForm.get(formControl)?.setValue(selectedValues);\r\n    this.selectedTags[formControl] = event.selectedOptions;\r\n  }\r\n\r\n  addNewRole() {\r\n    this.showCreateRolePopup.set(true);\r\n  }\r\n\r\n  onValueChange(event: any) {}\r\n\r\n  onOptionSelected(event: any) {\r\n    const exists = this.selectedRealmTag.some(\r\n      (item) => item.value === event.value,\r\n    );\r\n    if (!exists) {\r\n      this.selectedRealmTag.push(event);\r\n    }\r\n  }\r\n\r\n  removeRealmTag(tagValue: number) {\r\n    this.selectedRealmTag = this.selectedRealmTag.filter(\r\n      (tag) => tag.value !== tagValue,\r\n    );\r\n  }\r\n\r\n  removeTag(event: any, formControl: string) {\r\n    this.selectedTags[formControl] = this.selectedTags[formControl].filter(\r\n      (option) => option.value !== event.value,\r\n    );\r\n    const values = this.selectedTags[formControl].map((option) => option.value);\r\n    const DropdownSelectedvalues = this.selectedTags[formControl].map(\r\n      (option) => option.name,\r\n    );\r\n    this.userManagementForm.get(formControl)?.setValue(values);\r\n    this.selectedValues[formControl] = DropdownSelectedvalues;\r\n  }\r\n\r\n  onRemoveRealm() {}\r\n\r\n  openRealmPopup() {\r\n    this.showRealmPopup.set(true);\r\n  }\r\n\r\n  onOrgSelect(event: any) {\r\n    const selectedOrgId = event.selectedOptions?.[0]?.value;\r\n    const selectedOrgName = event.selectedOptions?.[0]?.name;\r\n    if (selectedOrgId) {\r\n      this.selectedOrg = selectedOrgId;\r\n      this.selectedOrgName = selectedOrgName;\r\n      this.addRealmForm.patchValue({\r\n        orgId: selectedOrgId,\r\n        domainId: '',\r\n        projectId: '',\r\n        teamId: '',\r\n      });\r\n      this.loadDomains(selectedOrgId);\r\n      this.selectedDomain = '';\r\n      this.selectedProject = '';\r\n      this.selectedTeam = '';\r\n      this.selectedDomainName = '';\r\n      this.selectedProjectName = '';\r\n      this.selectedTeamName = '';\r\n      this.projectOptions = [];\r\n      this.teamOptions = [];\r\n    }\r\n  }\r\n\r\n  loadDomains(orgId: string): void {\r\n    const org = this.hierarchyData().find((o) => o.orgId.toString() === orgId);\r\n    if (org) {\r\n      this.domainOptions = org.domains.map((domain: any) => ({\r\n        name: domain.domainName,\r\n        value: domain.domainId.toString(),\r\n      }));\r\n    } else {\r\n      this.domainOptions = [];\r\n    }\r\n  }\r\n\r\n  onDomainSelect(event: any): void {\r\n    const selectedDomainId = event.selectedOptions?.[0]?.value;\r\n    const selectedDomainName = event.selectedOptions?.[0]?.name;\r\n    if (selectedDomainId) {\r\n      this.selectedDomain = selectedDomainId;\r\n      this.selectedDomainName = selectedDomainName;\r\n      this.addRealmForm.patchValue({\r\n        domainId: selectedDomainId,\r\n        projectId: '',\r\n        teamId: '',\r\n      });\r\n      this.loadProjects(selectedDomainId);\r\n      this.selectedProject = '';\r\n      this.selectedTeam = '';\r\n      this.selectedProjectName = '';\r\n      this.selectedTeamName = '';\r\n      this.teamOptions = [];\r\n    }\r\n  }\r\n\r\n  loadProjects(domainId: string): void {\r\n    const org = this.hierarchyData().find((o) =>\r\n      o.domains.some((d: any) => d.domainId.toString() === domainId),\r\n    );\r\n    if (org) {\r\n      const domain = org.domains.find(\r\n        (d: any) => d.domainId.toString() === domainId,\r\n      );\r\n      if (domain) {\r\n        this.projectOptions = domain.projects.map((project: any) => ({\r\n          name: project.projectName,\r\n          value: project.projectId.toString(),\r\n        }));\r\n      } else {\r\n        this.projectOptions = [];\r\n      }\r\n    } else {\r\n      this.projectOptions = [];\r\n    }\r\n  }\r\n\r\n  onProjectSelect(event: any): void {\r\n    const selectedProjectId = event.selectedOptions?.[0]?.value;\r\n    const selectedProjectName = event.selectedOptions?.[0]?.name;\r\n    if (selectedProjectId) {\r\n      this.selectedProject = selectedProjectId;\r\n      this.selectedProjectName = selectedProjectName;\r\n      this.addRealmForm.patchValue({ projectId: selectedProjectId, team: '' });\r\n      this.loadTeams(selectedProjectId);\r\n      this.selectedTeam = '';\r\n      this.selectedTeamName = '';\r\n    }\r\n  }\r\n\r\n  loadTeams(projectId: string): void {\r\n    const org = this.hierarchyData().find((o) =>\r\n      o.domains.some((d: any) =>\r\n        d.projects.some((p: any) => p.projectId.toString() === projectId),\r\n      ),\r\n    );\r\n    if (org) {\r\n      const domain = org.domains.find((d: any) =>\r\n        d.projects.some((p: any) => p.projectId.toString() === projectId),\r\n      );\r\n      if (domain) {\r\n        const project = domain.projects.find(\r\n          (p: any) => p.projectId.toString() === projectId,\r\n        );\r\n        if (project) {\r\n          this.teamOptions = project.teams.map((team: any) => ({\r\n            name: team.teamName,\r\n            value: team.teamId.toString(),\r\n          }));\r\n        } else {\r\n          this.teamOptions = [];\r\n        }\r\n      } else {\r\n        this.teamOptions = [];\r\n      }\r\n    } else {\r\n      this.teamOptions = [];\r\n    }\r\n  }\r\n\r\n  onTeamSelect(event: any): void {\r\n    const selectedTeamId = event.selectedOptions?.[0]?.value;\r\n    const selectedTeamName = event.selectedOptions?.[0]?.name;\r\n    if (selectedTeamId) {\r\n      this.selectedTeam = selectedTeamId;\r\n      this.selectedTeamName = selectedTeamName;\r\n      this.addRealmForm.patchValue({ teamId: selectedTeamId });\r\n    }\r\n  }\r\n\r\n  createRole() {\r\n    if (this.addRoleForm.valid) {\r\n      const payload = this.addRoleForm.value;\r\n      this.userManagementService.createRole(payload).subscribe({\r\n        next: (res: any) => {\r\n          this.roleList.update((currentList) => [\r\n            ...currentList,\r\n            { name: res.roleName, value: res.roleId },\r\n          ]);\r\n          this.selectedRoleValue = res.roleName;\r\n          this.onRoleSelectionChange(res.roleId);\r\n          this.closeRealmPopup();\r\n          this.addRoleForm.reset();\r\n          this.statusSuccessMessage = 'The Role has been successfully created!';\r\n          this.showStatusPopup.set(true);\r\n        },\r\n        error: (e) => console.error(e),\r\n      });\r\n    }\r\n  }\r\n\r\n  createRealm() {\r\n    if (this.addRealmForm.valid) {\r\n      const { realmName, teamId } = this.addRealmForm.value;\r\n      this.userManagementService.createRealm(realmName, teamId).subscribe({\r\n        next: (res: any) => {\r\n          this.selectedRealmTag.push({\r\n            label: res.realmName,\r\n            value: res.realmId,\r\n          });\r\n          this.closeRealmPopup();\r\n          this.statusSuccessMessage =\r\n            'The Realm has been successfully created!';\r\n          this.showStatusPopup.set(true);\r\n        },\r\n        error: (e) => console.error(e),\r\n      });\r\n    }\r\n  }\r\n\r\n  closeRealmPopup() {\r\n    this.showRealmPopup.set(false);\r\n    this.showCreateRolePopup.set(false);\r\n    this.addRoleForm.reset();\r\n  }\r\n\r\n  closeSuccessPopup() {\r\n    this.showStatusPopup.set(false);\r\n  }\r\n\r\n  onRoleSelectionChange(event: any) {\r\n    const roleId = event?.selectedOptions?.[0]?.value ?? event;\r\n    this.userManagementForm.get('role')?.patchValue(roleId);\r\n    this.pageListLoading.set(true);\r\n    this.userManagementService.getExistingAccessControl(roleId).subscribe({\r\n      next: (res: any) => {\r\n        this.resetAccessControll();\r\n        this.accessControlList.set(res.accessControl);\r\n        if (!res.accessControl?.length) {\r\n          this.isAccessControl.set(true);\r\n          this.selectedTagRemovable.set(true);\r\n          return;\r\n        }\r\n        this.isAccessControl.set(false);\r\n        this.selectedTagRemovable.set(false);\r\n        this.updatedUserManagementFormValues();\r\n      },\r\n      error: (e) => console.error(e),\r\n      complete: () => this.pageListLoading.set(false)\r\n    });\r\n  }\r\n\r\n  updatedUserManagementFormValues() {\r\n    const actionMap = new Map(\r\n      this.accessControlList().map((item) => [item.page, item.actions]),\r\n    );\r\n    const actionIdsMap = new Map(\r\n      this.actionList().map((item) => [item.name, item.value]),\r\n    );\r\n\r\n    this.pageList().forEach((page) => {\r\n      const pageActions = this.accessControlList().find(\r\n        (pa) => pa.page === page.pageName,\r\n      );\r\n      if (pageActions) {\r\n        this.selectedValues[page.pageId.toString()] = pageActions.actions;\r\n        this.selectedTags[page.pageId.toString()] = pageActions.actions.map(\r\n          (op: string) => ({ name: op }),\r\n        );\r\n      }\r\n\r\n      const formControlName = page.pageId.toString();\r\n      const formControl = this.userManagementForm.get(formControlName);\r\n      const actionNames = actionMap.get(page.pageName);\r\n      if (formControl && actionNames) {\r\n        const actionIds = actionNames\r\n          .map((name: any) => actionIdsMap.get(name))\r\n          .filter((id: any) => id !== undefined);\r\n        formControl.patchValue(actionIds);\r\n      }\r\n    });\r\n  }\r\n\r\n  resetAccessControll() {\r\n    this.pageList().forEach((page) => {\r\n      const formControlName = page.pageId.toString();\r\n      this.selectedValues[formControlName] = [];\r\n      this.selectedTags[formControlName] = [];\r\n      this.userManagementForm.get(formControlName)?.patchValue([]);\r\n    });\r\n  }\r\n\r\n  onExit() {\r\n    this.router.navigate(['manage/admin-management']);\r\n  }\r\n\r\n  addNewUser() {\r\n    if (this.userManagementForm.valid && this.selectedRealmTag?.length) {\r\n      const { email, role, ...pagePermissions } = this.userManagementForm.value;\r\n      const roleId = parseInt(role);\r\n      const roleIds = [roleId];\r\n      const payload: any = {\r\n        email: email,\r\n        roleIds: roleIds,\r\n        realmIds: this.selectedRealmTag.map((realm) => realm.value),\r\n        authorizedBy:\r\n          this.tokenStorage.getDaUsername() || '<EMAIL>',\r\n      };\r\n\r\n      if (this.selectedTagRemovable()) {\r\n        const permissions: Array<{ pageId: number; actionId: number }> = [];\r\n\r\n        Object.entries(pagePermissions).forEach(([pageId, actionIds]) => {\r\n          if (Array.isArray(actionIds) && actionIds.length > 0) {\r\n            actionIds.forEach((actionId: number) => {\r\n              permissions.push({\r\n                pageId: parseInt(pageId),\r\n                actionId: actionId,\r\n              });\r\n            });\r\n          }\r\n        });\r\n\r\n        payload.rolePermessionRequest = {\r\n          roleId: roleId,\r\n          permissions: permissions,\r\n        };\r\n      }\r\n\r\n      if (this.userId) {\r\n        this.userManagementService.updateUser(payload, this.userId).subscribe({\r\n          next: (res: any) => {\r\n            this.userSuccessMessage = 'User update successfully';\r\n            this.showUserSuccessPopup = true;\r\n          },\r\n          error: (e) => {\r\n            console.error(e);\r\n            this.popupErrorMessage = e.error.message || e.error;\r\n            this.showErrorPopup = true;\r\n          },\r\n        });\r\n      } else {\r\n        this.userManagementService.addNewUser(payload).subscribe({\r\n          next: (res: any) => {\r\n            this.userSuccessMessage = 'User added successfully';\r\n            this.showUserSuccessPopup = true;\r\n          },\r\n          error: (e) => {\r\n            console.error(e);\r\n            this.popupErrorMessage = e.error.message || e.error;\r\n            this.showErrorPopup = true;\r\n          },\r\n        });\r\n      }\r\n    }\r\n  }\r\n\r\n  onUserSuccessConfirm() {\r\n    this.showUserSuccessPopup = false;\r\n    this.router.navigate(['manage/admin-management']);\r\n  }\r\n\r\n  isUserButtonEnabled(): boolean {\r\n    return this.userManagementForm.valid && this.selectedRealmTag?.length > 0;\r\n  }\r\n\r\n  closeErrorPopup() {\r\n    this.showErrorPopup = false;\r\n  }\r\n}\r\n", "<div class=\"user__management--wrapper\">\r\n  <div class=\"user__management--header\">\r\n    <ava-icon\r\n      (userClick)=\"onExit()\"\r\n      [cursor]=\"true\"\r\n      [iconSize]=\"24\"\r\n      iconName=\"arrow-left\"\r\n      iconColor=\"#4C515B\"\r\n    ></ava-icon>\r\n    <h2>Add User</h2>\r\n  </div>\r\n  <div class=\"user__management--container\">\r\n    <form [formGroup]=\"userManagementForm\">\r\n      <div class=\"basic__info--section\">\r\n        <!-- Email Field -->\r\n        @if (!userDataLoading()) {\r\n          <div class=\"input__field input\">\r\n            <label for=\"email\" class=\"filter-label required\">User Email</label>\r\n            <ava-textbox\r\n              formControlName=\"email\"\r\n              id=\"email\"\r\n              name=\"email\"\r\n              placeholder=\"Enter Email\"\r\n              [error]=\"getFieldError('email')\"\r\n              [fullWidth]=\"false\"\r\n              size=\"md\"\r\n            >\r\n            </ava-textbox>\r\n          </div>\r\n        } @else {\r\n          <div class=\"input__field input\">\r\n            <div class=\"skeleton-loader skeleton-label\"></div>\r\n            <div class=\"skeleton-loader skeleton-input\"></div>\r\n          </div>\r\n        }\r\n\r\n        <!-- Role Field -->\r\n        @if (!roleListLoading()) {\r\n          <div class=\"input__field dropdown\">\r\n            <label for=\"role\" class=\"filter-label required\">Role</label>\r\n            <ava-dropdown\r\n              id=\"role\"\r\n              [options]=\"roleList()\"\r\n              dropdownTitle=\"Select a Role\"\r\n              [error]=\"getFieldError('role')\"\r\n              [selectedValue]=\"selectedRoleValue\"\r\n              (selectionChange)=\"onRoleSelectionChange($event)\"\r\n            >\r\n              <ava-option (click)=\"addNewRole()\">\r\n                <span>Create New Role +</span>\r\n              </ava-option>\r\n            </ava-dropdown>\r\n          </div>\r\n        } @else {\r\n          <div class=\"input__field dropdown\">\r\n            <div class=\"skeleton-loader skeleton-label\"></div>\r\n            <div class=\"skeleton-loader skeleton-dropdown\"></div>\r\n          </div>\r\n        }\r\n      </div>\r\n\r\n      <div class=\"access__control--container\">\r\n        <h3>Access Control</h3>\r\n        @if (!pageListLoading()) {\r\n          @for (page of pageList(); track page.pageId) {\r\n            <div class=\"access__control--section\">\r\n              <label class=\"title\" for=\"page.pageId\">{{ page.pageName }}</label>\r\n              <ava-dropdown\r\n                class=\"dropdown\"\r\n                [id]=\"page.pageId\"\r\n                [disabled]=\"false\"\r\n                [selectedValues]=\"selectedValues[page.pageId.toString()]\"\r\n                dropdownTitle=\"Select a Category\"\r\n                [options]=\"actionList()\"\r\n                [checkboxOptions]=\"actionCheckboxList()\"\r\n                [disabled]=\"!isAccessControl()\"\r\n                (selectionChange)=\"\r\n                  onSelectionChange($event, page.pageId.toString())\r\n                \"\r\n              >\r\n              </ava-dropdown>\r\n              <div class=\"dropdown-tag\">\r\n                <label class=\"title-sm\">Selected Values</label>\r\n                <div class=\"tag--container\">\r\n                  @for (\r\n                    tag of selectedTags[page.pageId.toString()];\r\n                    track tag\r\n                  ) {\r\n                    <ava-tag\r\n                      [label]=\"tag.name\"\r\n                      [customStyle]=\"{\r\n                        background: '#E9F0FC',\r\n                        color: '#2D3036',\r\n                      }\"\r\n                      [pill]=\"true\"\r\n                      size=\"sm\"\r\n                      [removable]=\"selectedTagRemovable()\"\r\n                      (removed)=\"removeTag(tag, page.pageId.toString())\"\r\n                      iconColor=\"#000000\"\r\n                    ></ava-tag>\r\n                  }\r\n                </div>\r\n              </div>\r\n            </div>\r\n          }\r\n        } @else {\r\n          <div class=\"skeleton-access-control\">\r\n            @for (item of [1, 2, 3]; track item) {\r\n              <div class=\"skeleton-section\">\r\n                <div class=\"skeleton-loader skeleton-section-label\"></div>\r\n                <div class=\"skeleton-loader skeleton-dropdown\"></div>\r\n                <div\r\n                  class=\"skeleton-loader skeleton-section-label\"\r\n                  style=\"width: 80px; margin-top: 12px\"\r\n                ></div>\r\n              </div>\r\n            }\r\n          </div>\r\n        }\r\n      </div>\r\n\r\n      <div class=\"realm-container\">\r\n        <h3 class=\"required\">Assign Realm</h3>\r\n        <div class=\"realm-section\">\r\n          @if (!realmsListLoading()) {\r\n            <div class=\"realm__filter--section\">\r\n              <ava-autocomplete\r\n                [options]=\"realmsList()\"\r\n                placeholder=\"Search for a Realms\"\r\n                startIcon=\"search\"\r\n                startIconColor=\"#6b7280\"\r\n                (optionSelected)=\"onOptionSelected($event)\"\r\n                [showDefaultOptions]=\"true\"\r\n                (valueChange)=\"onValueChange($event)\"\r\n              >\r\n              </ava-autocomplete>\r\n              <ava-button\r\n                label=\"Add Realm\"\r\n                variant=\"primary\"\r\n                [customStyles]=\"{\r\n                  background: '#E9F0FC',\r\n                  color: '#11387C',\r\n                  border: '1px solid #6898EB',\r\n                }\"\r\n                size=\"large\"\r\n                (userClick)=\"openRealmPopup()\"\r\n                [width]=\"'100%'\"\r\n                class=\"action-button\"\r\n              >\r\n              </ava-button>\r\n            </div>\r\n            <div class=\"realm tag--container\">\r\n              @for (tag of selectedRealmTag; track tag) {\r\n                <ava-tag\r\n                  [label]=\"tag.label\"\r\n                  [customStyle]=\"{ background: '#EDEDF3', color: '#2D3036' }\"\r\n                  [pill]=\"true\"\r\n                  size=\"sm\"\r\n                  [removable]=\"true\"\r\n                  iconColor=\"#2D3036\"\r\n                  (removed)=\"removeRealmTag(tag.value)\"\r\n                ></ava-tag>\r\n              }\r\n            </div>\r\n          } @else {\r\n            <div class=\"skeleton-realm-section\">\r\n              <div class=\"skeleton-loader skeleton-autocomplete\"></div>\r\n              <div class=\"skeleton-loader skeleton-button\"></div>\r\n            </div>\r\n          }\r\n        </div>\r\n      </div>\r\n    </form>\r\n    <div class=\"button__container main\">\r\n      <ava-button\r\n        label=\"Exit\"\r\n        variant=\"secondary\"\r\n        size=\"large\"\r\n        (userClick)=\"onExit()\"\r\n        [width]=\"'100%'\"\r\n        class=\"action-button\"\r\n      >\r\n      </ava-button>\r\n      <ava-button\r\n        label=\"Add User\"\r\n        variant=\"primary\"\r\n        size=\"large\"\r\n        (userClick)=\"addNewUser()\"\r\n        [width]=\"'100%'\"\r\n        class=\"action-button\"\r\n        [disabled]=\"!isUserButtonEnabled()\"\r\n      >\r\n      </ava-button>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<ava-popup\r\n  [show]=\"showRealmPopup()\"\r\n  [showTitle]=\"false\"\r\n  [showHeaderIcon]=\"false\"\r\n  [showClose]=\"false\"\r\n  [showInlineMessage]=\"false\"\r\n  [showConfirm]=\"false\"\r\n  message=\"\"\r\n  [showCancel]=\"false\"\r\n  [popupWidth]=\"'744px'\"\r\n>\r\n  <div class=\"add__realm--container\">\r\n    <form [formGroup]=\"addRealmForm\" class=\"realm__form\">\r\n      @if (!orgDataLoading()) {\r\n        <div class=\"form-fields\">\r\n          <div class=\"input__field--wrapper\">\r\n            <label class=\"filter-label required\">Name of the Realm</label>\r\n            <ava-textbox\r\n              class=\"input-field\"\r\n              formControlName=\"realmName\"\r\n              id=\"realmName\"\r\n              name=\"realmName\"\r\n              placeholder=\"Enter Realm Name\"\r\n              [required]=\"true\"\r\n              [fullWidth]=\"false\"\r\n              size=\"md\"\r\n            >\r\n            </ava-textbox>\r\n          </div>\r\n          <div class=\"input__field--wrapper\"></div>\r\n        </div>\r\n\r\n        <div class=\"form-fields\">\r\n          <div class=\"input__field--wrapper\">\r\n            <label class=\"filter-label required\">Choose Organization</label>\r\n            <ava-dropdown\r\n              [dropdownTitle]=\"'Select Organization'\"\r\n              [options]=\"orgOptions\"\r\n              [selectedValue]=\"selectedOrgName\"\r\n              [disabled]=\"false\"\r\n              (selectionChange)=\"onOrgSelect($event)\"\r\n              [search]=\"true\"\r\n              [enableSearch]=\"true\"\r\n            ></ava-dropdown>\r\n          </div>\r\n          <div class=\"input__field--wrapper\">\r\n            <label class=\"filter-label required\">Choose Domain</label>\r\n            <ava-dropdown\r\n              [dropdownTitle]=\"'Select Domain'\"\r\n              [options]=\"domainOptions\"\r\n              [selectedValue]=\"selectedDomainName\"\r\n              [disabled]=\"!selectedOrg\"\r\n              (selectionChange)=\"onDomainSelect($event)\"\r\n              [search]=\"true\"\r\n              [enableSearch]=\"true\"\r\n            ></ava-dropdown>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"form-fields\">\r\n          <div class=\"input__field--wrapper\">\r\n            <label class=\"filter-label required\">Choose Project</label>\r\n            <ava-dropdown\r\n              [dropdownTitle]=\"'Select Project'\"\r\n              [options]=\"projectOptions\"\r\n              [selectedValue]=\"selectedProjectName\"\r\n              [disabled]=\"!selectedDomain\"\r\n              (selectionChange)=\"onProjectSelect($event)\"\r\n              [search]=\"true\"\r\n              [enableSearch]=\"true\"\r\n            ></ava-dropdown>\r\n          </div>\r\n          <div class=\"input__field--wrapper\">\r\n            <label class=\"filter-label required\">Choose Team</label>\r\n            <ava-dropdown\r\n              [dropdownTitle]=\"'Select Team'\"\r\n              [options]=\"teamOptions\"\r\n              [selectedValue]=\"selectedTeamName\"\r\n              [disabled]=\"!selectedProject\"\r\n              (selectionChange)=\"onTeamSelect($event)\"\r\n              [search]=\"true\"\r\n              [enableSearch]=\"true\"\r\n            ></ava-dropdown>\r\n          </div>\r\n        </div>\r\n      } @else {\r\n        <div class=\"form-fields\">\r\n          <div class=\"input__field--wrapper\">\r\n            <div class=\"skeleton-loader skeleton-label\"></div>\r\n            <div class=\"skeleton-loader skeleton-input\"></div>\r\n          </div>\r\n          <div class=\"input__field--wrapper\"></div>\r\n        </div>\r\n\r\n        <div class=\"form-fields\">\r\n          <div class=\"input__field--wrapper\">\r\n            <div class=\"skeleton-loader skeleton-label\"></div>\r\n            <div class=\"skeleton-loader skeleton-dropdown\"></div>\r\n          </div>\r\n          <div class=\"input__field--wrapper\">\r\n            <div class=\"skeleton-loader skeleton-label\"></div>\r\n            <div class=\"skeleton-loader skeleton-dropdown\"></div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"form-fields\">\r\n          <div class=\"input__field--wrapper\">\r\n            <div class=\"skeleton-loader skeleton-label\"></div>\r\n            <div class=\"skeleton-loader skeleton-dropdown\"></div>\r\n          </div>\r\n          <div class=\"input__field--wrapper\">\r\n            <div class=\"skeleton-loader skeleton-label\"></div>\r\n            <div class=\"skeleton-loader skeleton-dropdown\"></div>\r\n          </div>\r\n        </div>\r\n      }\r\n    </form>\r\n    <div class=\"button__container\">\r\n      <ava-button\r\n        label=\"Cancel\"\r\n        variant=\"secondary\"\r\n        size=\"large\"\r\n        (userClick)=\"closeRealmPopup()\"\r\n        [width]=\"'100%'\"\r\n        class=\"action-button\"\r\n      >\r\n      </ava-button>\r\n      <ava-button\r\n        label=\"Create\"\r\n        variant=\"primary\"\r\n        size=\"large\"\r\n        (userClick)=\"createRealm()\"\r\n        [width]=\"'100%'\"\r\n        class=\"action-button\"\r\n        [disabled]=\"!addRealmForm.valid\"\r\n      >\r\n      </ava-button>\r\n    </div>\r\n  </div>\r\n</ava-popup>\r\n\r\n<ava-popup\r\n  [show]=\"showCreateRolePopup()\"\r\n  [showTitle]=\"false\"\r\n  [showHeaderIcon]=\"false\"\r\n  [showClose]=\"false\"\r\n  [showInlineMessage]=\"false\"\r\n  [showConfirm]=\"false\"\r\n  message=\"\"\r\n  [showCancel]=\"false\"\r\n  [popupWidth]=\"'410px'\"\r\n>\r\n  <div class=\"add__realm--container\">\r\n    <form [formGroup]=\"addRoleForm\" class=\"realm__form\">\r\n      <div class=\"role__form-fields\">\r\n        <div class=\"input__field--wrapper\">\r\n          <label for=\"roleName\" class=\"filter-label required\"\r\n            >Name of the Role</label\r\n          >\r\n          <ava-textbox\r\n            formControlName=\"roleName\"\r\n            id=\"roleName\"\r\n            name=\"roleName\"\r\n            placeholder=\"Enter Role Name\"\r\n            [required]=\"true\"\r\n            [fullWidth]=\"false\"\r\n            size=\"md\"\r\n          >\r\n          </ava-textbox>\r\n        </div>\r\n\r\n        <div class=\"input__field--wrapper\">\r\n          <label for=\"description\" class=\"filter-label required\"\r\n            >Description</label\r\n          >\r\n          <ava-textbox\r\n            formControlName=\"description\"\r\n            id=\"description\"\r\n            name=\"description\"\r\n            placeholder=\"Enter Description\"\r\n            [required]=\"true\"\r\n            [fullWidth]=\"false\"\r\n            size=\"md\"\r\n          >\r\n          </ava-textbox>\r\n        </div>\r\n      </div>\r\n    </form>\r\n    <div class=\"button__container\">\r\n      <ava-button\r\n        label=\"Cancel\"\r\n        variant=\"secondary\"\r\n        size=\"large\"\r\n        (userClick)=\"closeRealmPopup()\"\r\n        [width]=\"'100%'\"\r\n        class=\"action-button\"\r\n      >\r\n      </ava-button>\r\n      <ava-button\r\n        label=\"Create\"\r\n        variant=\"primary\"\r\n        size=\"large\"\r\n        (userClick)=\"createRole()\"\r\n        [width]=\"'100%'\"\r\n        class=\"action-button\"\r\n        [disabled]=\"!addRoleForm.valid\"\r\n      >\r\n      </ava-button>\r\n    </div>\r\n  </div>\r\n</ava-popup>\r\n\r\n<!-- success status popup for role and realm user -->\r\n<ava-popup\r\n  [show]=\"showStatusPopup()\"\r\n  [title]=\"'Success'\"\r\n  [message]=\"statusSuccessMessage\"\r\n  [showHeaderIcon]=\"true\"\r\n  headerIconName=\"check-circle\"\r\n  iconColor=\"#28a745\"\r\n  [showClose]=\"true\"\r\n  [showCancel]=\"false\"\r\n  (closed)=\"closeSuccessPopup()\"\r\n>\r\n</ava-popup>\r\n\r\n<!-- success popup for add and update user -->\r\n<ava-popup\r\n  [show]=\"showUserSuccessPopup\"\r\n  [title]=\"'Success'\"\r\n  [message]=\"userSuccessMessage\"\r\n  [showHeaderIcon]=\"true\"\r\n  headerIconName=\"check-circle\"\r\n  iconColor=\"#28a745\"\r\n  [showClose]=\"true\"\r\n  [showCancel]=\"false\"\r\n  [showConfirm]=\"true\"\r\n  [confirmButtonLabel]=\"'OK'\"\r\n  [confirmButtonVariant]=\"'primary'\"\r\n  [confirmButtonBackground]=\"'#28a745'\"\r\n  (confirm)=\"onUserSuccessConfirm()\"\r\n  (closed)=\"onUserSuccessConfirm()\"\r\n>\r\n</ava-popup>\r\n\r\n<!-- Error Popup -->\r\n<ava-popup\r\n  [show]=\"showErrorPopup\"\r\n  [title]=\"'Failed'\"\r\n  [message]=\"popupErrorMessage\"\r\n  [showHeaderIcon]=\"true\"\r\n  headerIconName=\"alert-circle\"\r\n  iconColor=\"#dc3545\"\r\n  [showClose]=\"true\"\r\n  [showCancel]=\"false\"\r\n  [showConfirm]=\"true\"\r\n  [confirmButtonLabel]=\"'OK'\"\r\n  [confirmButtonVariant]=\"'primary'\"\r\n  [confirmButtonBackground]=\"'#dc3545'\"\r\n  (confirm)=\"closeErrorPopup()\"\r\n  (closed)=\"closeErrorPopup()\"\r\n>\r\n</ava-popup>\r\n"], "mappings": "AAAA,SAAoBA,MAAM,EAAEC,MAAM,QAAQ,eAAe;AACzD,SAASC,qBAAqB,QAAQ,wCAAwC;AAC9E,SAASC,cAAc,EAAEC,MAAM,QAAQ,iBAAiB;AACxD,SACEC,aAAa,EACbC,wBAAwB,EACxBC,kBAAkB,EAClBC,eAAe,EACfC,mBAAmB,EACnBC,eAAe,EACfC,iBAAiB,EAEjBC,cAAc,QACT,wBAAwB;AAC/B,SACEC,WAAW,EAGXC,mBAAmB,EACnBC,UAAU,QACL,gBAAgB;AAEvB,SAASC,gBAAgB,QAAQ,oDAAoD;AACrF,SAASC,mBAAmB,QAAQ,6CAA6C;;;;;;;mBCoFpE,CAAC,EAAE,CAAC,EAAE,CAAC;;;;;;;;;;;;;IA1FRC,EADF,CAAAC,cAAA,aAAgC,gBACmB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACnEH,EAAA,CAAAI,SAAA,sBASc;IAChBJ,EAAA,CAAAG,YAAA,EAAM;;;;IALFH,EAAA,CAAAK,SAAA,GAAgC;IAChCL,EADA,CAAAM,UAAA,UAAAC,MAAA,CAAAC,aAAA,UAAgC,oBACb;;;;;IAMvBR,EAAA,CAAAC,cAAA,aAAgC;IAE9BD,EADA,CAAAI,SAAA,cAAkD,cACA;IACpDJ,EAAA,CAAAG,YAAA,EAAM;;;;;;IAMJH,EADF,CAAAC,cAAA,aAAmC,gBACe;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC5DH,EAAA,CAAAC,cAAA,uBAOC;IADCD,EAAA,CAAAS,UAAA,6BAAAC,iFAAAC,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAN,MAAA,GAAAP,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAmBR,MAAA,CAAAS,qBAAA,CAAAL,MAAA,CAA6B;IAAA,EAAC;IAEjDX,EAAA,CAAAC,cAAA,qBAAmC;IAAvBD,EAAA,CAAAS,UAAA,mBAAAQ,qEAAA;MAAAjB,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAN,MAAA,GAAAP,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASR,MAAA,CAAAW,UAAA,EAAY;IAAA,EAAC;IAChClB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAG7BF,EAH6B,CAAAG,YAAA,EAAO,EACnB,EACA,EACX;;;;IAVFH,EAAA,CAAAK,SAAA,GAAsB;IAGtBL,EAHA,CAAAM,UAAA,YAAAC,MAAA,CAAAY,QAAA,GAAsB,UAAAZ,MAAA,CAAAC,aAAA,SAES,kBAAAD,MAAA,CAAAa,iBAAA,CACI;;;;;IASvCpB,EAAA,CAAAC,cAAA,aAAmC;IAEjCD,EADA,CAAAI,SAAA,cAAkD,cACG;IACvDJ,EAAA,CAAAG,YAAA,EAAM;;;;;;IA+BIH,EAAA,CAAAC,cAAA,kBAWC;IAFCD,EAAA,CAAAS,UAAA,qBAAAY,gFAAA;MAAA,MAAAC,MAAA,GAAAtB,EAAA,CAAAY,aAAA,CAAAW,GAAA,EAAAC,SAAA;MAAA,MAAAC,OAAA,GAAAzB,EAAA,CAAAc,aAAA,GAAAU,SAAA;MAAA,MAAAjB,MAAA,GAAAP,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAWR,MAAA,CAAAmB,SAAA,CAAAJ,MAAA,EAAeG,OAAA,CAAAE,MAAA,CAAAC,QAAA,EAAsB,CAAC;IAAA,EAAC;IAEnD5B,EAAA,CAAAG,YAAA,EAAU;;;;;IAHTH,EAPA,CAAAM,UAAA,UAAAgB,MAAA,CAAAO,IAAA,CAAkB,gBAAA7B,EAAA,CAAA8B,eAAA,IAAAC,GAAA,EAIhB,cACW,cAAAxB,MAAA,CAAAyB,oBAAA,GAEuB;;;;;;IA9B5ChC,EADF,CAAAC,cAAA,cAAsC,gBACG;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAClEH,EAAA,CAAAC,cAAA,uBAYC;IAHCD,EAAA,CAAAS,UAAA,6BAAAwB,uFAAAtB,MAAA;MAAA,MAAAc,OAAA,GAAAzB,EAAA,CAAAY,aAAA,CAAAsB,GAAA,EAAAV,SAAA;MAAA,MAAAjB,MAAA,GAAAP,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CACqBR,MAAA,CAAA4B,iBAAA,CAAAxB,MAAA,EAA0Bc,OAAA,CAAAE,MAAA,CAAAC,QAAA,EAC/C,CAAC;IAAA;IAEH5B,EAAA,CAAAG,YAAA,EAAe;IAEbH,EADF,CAAAC,cAAA,cAA0B,gBACA;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC/CH,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAAoC,gBAAA,IAAAC,oDAAA,uBAAArC,EAAA,CAAAsC,yBAAA,CAgBC;IAGPtC,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;;IArCmCH,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAuC,iBAAA,CAAAd,OAAA,CAAAe,QAAA,CAAmB;IAGxDxC,EAAA,CAAAK,SAAA,EAAkB;IAMlBL,EANA,CAAAM,UAAA,OAAAmB,OAAA,CAAAE,MAAA,CAAkB,mBACA,mBAAApB,MAAA,CAAAkC,cAAA,CAAAhB,OAAA,CAAAE,MAAA,CAAAC,QAAA,IACuC,YAAArB,MAAA,CAAAmC,UAAA,GAEjC,oBAAAnC,MAAA,CAAAoC,kBAAA,GACgB,cAAApC,MAAA,CAAAqC,eAAA,GACT;IAS7B5C,EAAA,CAAAK,SAAA,GAgBC;IAhBDL,EAAA,CAAA6C,UAAA,CAAAtC,MAAA,CAAAuC,YAAA,CACbrB,OAAA,CAAAE,MAAA,CAAAC,QAAA,EAAsB,EAeR;;;;;IApCT5B,EAAA,CAAAoC,gBAAA,IAAAW,8CAAA,oBAAAC,UAAA,CAwCC;;;;IAxCDhD,EAAA,CAAA6C,UAAA,CAAAtC,MAAA,CAAA0C,QAAA,EAAU,CAwCT;;;;;IAIGjD,EAAA,CAAAC,cAAA,cAA8B;IAG5BD,EAFA,CAAAI,SAAA,cAA0D,cACL,cAI9C;IACTJ,EAAA,CAAAG,YAAA,EAAM;;;;;IATVH,EAAA,CAAAC,cAAA,aAAqC;IACnCD,EAAA,CAAAoC,gBAAA,IAAAc,8CAAA,mBAAAlD,EAAA,CAAAsC,yBAAA,CASC;IACHtC,EAAA,CAAAG,YAAA,EAAM;;;IAVJH,EAAA,CAAAK,SAAA,EASC;IATDL,EAAA,CAAA6C,UAAA,CAAA7C,EAAA,CAAA8B,eAAA,IAAAqB,GAAA,EASC;;;;;;IAqCGnD,EAAA,CAAAC,cAAA,kBAQC;IADCD,EAAA,CAAAS,UAAA,qBAAA2C,0EAAA;MAAA,MAAAC,MAAA,GAAArD,EAAA,CAAAY,aAAA,CAAA0C,GAAA,EAAA9B,SAAA;MAAA,MAAAjB,MAAA,GAAAP,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAWR,MAAA,CAAAgD,cAAA,CAAAF,MAAA,CAAAG,KAAA,CAAyB;IAAA,EAAC;IACtCxD,EAAA,CAAAG,YAAA,EAAU;;;;IAHTH,EAJA,CAAAM,UAAA,UAAA+C,MAAA,CAAAI,KAAA,CAAmB,gBAAAzD,EAAA,CAAA8B,eAAA,IAAA4B,GAAA,EACwC,cAC9C,mBAEK;;;;;;IAhCtB1D,EADF,CAAAC,cAAA,cAAoC,2BASjC;IADCD,EAFA,CAAAS,UAAA,4BAAAkD,oFAAAhD,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAgD,GAAA;MAAA,MAAArD,MAAA,GAAAP,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAkBR,MAAA,CAAAsD,gBAAA,CAAAlD,MAAA,CAAwB;IAAA,EAAC,yBAAAmD,iFAAAnD,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAgD,GAAA;MAAA,MAAArD,MAAA,GAAAP,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAE5BR,MAAA,CAAAwD,aAAA,CAAApD,MAAA,CAAqB;IAAA,EAAC;IAEvCX,EAAA,CAAAG,YAAA,EAAmB;IACnBH,EAAA,CAAAC,cAAA,qBAYC;IAHCD,EAAA,CAAAS,UAAA,uBAAAuD,yEAAA;MAAAhE,EAAA,CAAAY,aAAA,CAAAgD,GAAA;MAAA,MAAArD,MAAA,GAAAP,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAaR,MAAA,CAAA0D,cAAA,EAAgB;IAAA,EAAC;IAKlCjE,EADE,CAAAG,YAAA,EAAa,EACT;IACNH,EAAA,CAAAC,cAAA,cAAkC;IAChCD,EAAA,CAAAoC,gBAAA,IAAA8B,8CAAA,uBAAAlE,EAAA,CAAAsC,yBAAA,CAUC;IACHtC,EAAA,CAAAG,YAAA,EAAM;;;;IApCFH,EAAA,CAAAK,SAAA,EAAwB;IAKxBL,EALA,CAAAM,UAAA,YAAAC,MAAA,CAAA4D,UAAA,GAAwB,4BAKG;IAO3BnE,EAAA,CAAAK,SAAA,EAIE;IAGFL,EAPA,CAAAM,UAAA,iBAAAN,EAAA,CAAA8B,eAAA,IAAAsC,GAAA,EAIE,iBAGc;IAMlBpE,EAAA,CAAAK,SAAA,GAUC;IAVDL,EAAA,CAAA6C,UAAA,CAAAtC,MAAA,CAAA8D,gBAAA,CAUC;;;;;IAGHrE,EAAA,CAAAC,cAAA,cAAoC;IAElCD,EADA,CAAAI,SAAA,cAAyD,cACN;IACrDJ,EAAA,CAAAG,YAAA,EAAM;;;;;;IA6CNH,EAFJ,CAAAC,cAAA,cAAyB,cACY,gBACI;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9DH,EAAA,CAAAI,SAAA,sBAUc;IAChBJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,SAAA,cAAyC;IAC3CJ,EAAA,CAAAG,YAAA,EAAM;IAIFH,EAFJ,CAAAC,cAAA,cAAyB,cACY,gBACI;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAChEH,EAAA,CAAAC,cAAA,wBAQC;IAHCD,EAAA,CAAAS,UAAA,6BAAA6D,kFAAA3D,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAA2D,IAAA;MAAA,MAAAhE,MAAA,GAAAP,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAmBR,MAAA,CAAAiE,WAAA,CAAA7D,MAAA,CAAmB;IAAA,EAAC;IAI3CX,EADG,CAAAG,YAAA,EAAe,EACZ;IAEJH,EADF,CAAAC,cAAA,eAAmC,iBACI;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC1DH,EAAA,CAAAC,cAAA,wBAQC;IAHCD,EAAA,CAAAS,UAAA,6BAAAgE,kFAAA9D,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAA2D,IAAA;MAAA,MAAAhE,MAAA,GAAAP,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAmBR,MAAA,CAAAmE,cAAA,CAAA/D,MAAA,CAAsB;IAAA,EAAC;IAKhDX,EAFK,CAAAG,YAAA,EAAe,EACZ,EACF;IAIFH,EAFJ,CAAAC,cAAA,eAAyB,eACY,iBACI;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC3DH,EAAA,CAAAC,cAAA,wBAQC;IAHCD,EAAA,CAAAS,UAAA,6BAAAkE,kFAAAhE,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAA2D,IAAA;MAAA,MAAAhE,MAAA,GAAAP,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAmBR,MAAA,CAAAqE,eAAA,CAAAjE,MAAA,CAAuB;IAAA,EAAC;IAI/CX,EADG,CAAAG,YAAA,EAAe,EACZ;IAEJH,EADF,CAAAC,cAAA,eAAmC,iBACI;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACxDH,EAAA,CAAAC,cAAA,wBAQC;IAHCD,EAAA,CAAAS,UAAA,6BAAAoE,kFAAAlE,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAA2D,IAAA;MAAA,MAAAhE,MAAA,GAAAP,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAmBR,MAAA,CAAAuE,YAAA,CAAAnE,MAAA,CAAoB;IAAA,EAAC;IAK9CX,EAFK,CAAAG,YAAA,EAAe,EACZ,EACF;;;;IA7DAH,EAAA,CAAAK,SAAA,GAAiB;IACjBL,EADA,CAAAM,UAAA,kBAAiB,oBACE;IAYnBN,EAAA,CAAAK,SAAA,GAAuC;IAMvCL,EANA,CAAAM,UAAA,wCAAuC,YAAAC,MAAA,CAAAwE,UAAA,CACjB,kBAAAxE,MAAA,CAAAyE,eAAA,CACW,mBACf,gBAEH,sBACM;IAMrBhF,EAAA,CAAAK,SAAA,GAAiC;IAMjCL,EANA,CAAAM,UAAA,kCAAiC,YAAAC,MAAA,CAAA0E,aAAA,CACR,kBAAA1E,MAAA,CAAA2E,kBAAA,CACW,cAAA3E,MAAA,CAAA4E,WAAA,CACX,gBAEV,sBACM;IASrBnF,EAAA,CAAAK,SAAA,GAAkC;IAMlCL,EANA,CAAAM,UAAA,mCAAkC,YAAAC,MAAA,CAAA6E,cAAA,CACR,kBAAA7E,MAAA,CAAA8E,mBAAA,CACW,cAAA9E,MAAA,CAAA+E,cAAA,CACT,gBAEb,sBACM;IAMrBtF,EAAA,CAAAK,SAAA,GAA+B;IAM/BL,EANA,CAAAM,UAAA,gCAA+B,YAAAC,MAAA,CAAAgF,WAAA,CACR,kBAAAhF,MAAA,CAAAiF,gBAAA,CACW,cAAAjF,MAAA,CAAAkF,eAAA,CACL,gBAEd,sBACM;;;;;IAMzBzF,EADF,CAAAC,cAAA,cAAyB,cACY;IAEjCD,EADA,CAAAI,SAAA,cAAkD,cACA;IACpDJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,SAAA,cAAyC;IAC3CJ,EAAA,CAAAG,YAAA,EAAM;IAGJH,EADF,CAAAC,cAAA,cAAyB,cACY;IAEjCD,EADA,CAAAI,SAAA,cAAkD,cACG;IACvDJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAmC;IAEjCD,EADA,CAAAI,SAAA,eAAkD,eACG;IAEzDJ,EADE,CAAAG,YAAA,EAAM,EACF;IAGJH,EADF,CAAAC,cAAA,eAAyB,eACY;IAEjCD,EADA,CAAAI,SAAA,eAAkD,eACG;IACvDJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAmC;IAEjCD,EADA,CAAAI,SAAA,eAAkD,eACG;IAEzDJ,EADE,CAAAG,YAAA,EAAM,EACF;;;AD/Qd,WAAauF,gBAAgB;EAAvB,MAAOA,gBAAgB;IAC3BC,kBAAkB;IAClBC,YAAY;IACZC,WAAW;IACX1E,QAAQ,GAAGpC,MAAM,CAAmB,EAAE,CAAC;IACvCkE,QAAQ,GAAGlE,MAAM,CAAS,EAAE,CAAC;IAC7B2D,UAAU,GAAG3D,MAAM,CAAmB,EAAE,CAAC;IACzC4D,kBAAkB,GAAG5D,MAAM,CAAW,EAAE,CAAC;IACzCoF,UAAU,GAAGpF,MAAM,CAAQ,EAAE,CAAC;IAC9B+D,YAAY,GAA8C,EAAE;IAC5DuB,gBAAgB,GAAU,EAAE;IAC5B5B,cAAc,GAAgC,EAAE;IAChDqD,iBAAiB,GAAU,EAAE;IAC7BC,cAAc,GAAGhH,MAAM,CAAU,KAAK,CAAC;IACvCiH,mBAAmB,GAAGjH,MAAM,CAAU,KAAK,CAAC;IAC5CkH,aAAa,GAAGlH,MAAM,CAAQ,EAAE,CAAC;IACjCmH,eAAe,GAAGnH,MAAM,CAAU,KAAK,CAAC;IACxC6D,eAAe,GAAG7D,MAAM,CAAU,KAAK,CAAC;IACxCoH,iBAAiB,GAAGpH,MAAM,CAAQ,EAAE,CAAC;IACrCiD,oBAAoB,GAAGjD,MAAM,CAAU,IAAI,CAAC;IAC5CqC,iBAAiB,GAAW,EAAE;IAC9BgF,MAAM;IACNC,oBAAoB,GAAY,KAAK;IACrCC,cAAc,GAAY,KAAK;IAC/BC,kBAAkB,GAAW,EAAE;IAC/BC,oBAAoB,GAAW,EAAE;IACjCC,iBAAiB,GAAW,EAAE;IAE9B1B,UAAU,GAAqB,EAAE;IACjCE,aAAa,GAAqB,EAAE;IACpCG,cAAc,GAAqB,EAAE;IACrCG,WAAW,GAAqB,EAAE;IAElCP,eAAe,GAAW,EAAE;IAC5BE,kBAAkB,GAAW,EAAE;IAC/BG,mBAAmB,GAAW,EAAE;IAChCG,gBAAgB,GAAW,EAAE;IAE7BL,WAAW,GAAW,EAAE;IACxBG,cAAc,GAAW,EAAE;IAC3BG,eAAe,GAAW,EAAE;IAC5BiB,YAAY,GAAW,EAAE;IAEjBC,qBAAqB,GAAG7H,MAAM,CAACE,qBAAqB,CAAC;IACrD4H,gBAAgB,GAAG9H,MAAM,CAACgB,gBAAgB,CAAC;IAC3C+G,MAAM,GAAG/H,MAAM,CAACI,MAAM,CAAC;IACvB4H,WAAW,GAAGhI,MAAM,CAACa,WAAW,CAAC;IACjCoH,YAAY,GAAGjI,MAAM,CAACiB,mBAAmB,CAAC;IAC1CiH,cAAc,GAAGlI,MAAM,CAACG,cAAc,CAAC;IAE/C;IACAgI,eAAe,GAAGlI,MAAM,CAAU,KAAK,CAAC;IACxCmI,eAAe,GAAGnI,MAAM,CAAU,KAAK,CAAC;IACxCoI,iBAAiB,GAAGpI,MAAM,CAAU,KAAK,CAAC;IAC1CqI,cAAc,GAAGrI,MAAM,CAAU,KAAK,CAAC;IACvCsI,eAAe,GAAGtI,MAAM,CAAU,KAAK,CAAC;IAExCuI,QAAQA,CAAA;MACN,IAAI,CAACC,WAAW,EAAE;MAClB,IAAI,CAACC,WAAW,EAAE;MAClB,IAAI,CAACC,aAAa,EAAE;MACpB,IAAI,CAACC,aAAa,EAAE;MACpB,IAAI,CAACC,UAAU,EAAE;MACjB,IAAI,CAAChC,kBAAkB,GAAG,IAAI,CAACiC,qBAAqB,EAAE;MACtD,IAAI,CAAChC,YAAY,GAAG,IAAI,CAACiC,eAAe,EAAE;MAC1C,IAAI,CAAChC,WAAW,GAAG,IAAI,CAACiC,cAAc,EAAE;MACxC,IAAI,CAACd,cAAc,CAACe,WAAW,CAACC,SAAS,CAAEC,MAAW,IAAI;QACxD,IAAIA,MAAM,EAAEC,EAAE,EAAE;UACd,IAAI,CAAC9B,MAAM,GAAG6B,MAAM,CAACC,EAAE;UACvB,IAAI,CAACC,WAAW,CAACF,MAAM,CAACC,EAAE,CAAC;QAC7B;MACF,CAAC,CAAC;IACJ;IAEAC,WAAWA,CAACD,EAAU;MACpB,IAAI,CAACb,eAAe,CAACe,GAAG,CAAC,IAAI,CAAC;MAC9B,IAAI,CAACzB,qBAAqB,CAAC0B,cAAc,CAACH,EAAE,CAAC,CAACF,SAAS,CAAC;QACtDM,IAAI,EAAGC,GAAQ,IAAI;UACjB,IAAI,CAAC5C,kBAAkB,CAAC6C,UAAU,CAAC;YACjCC,KAAK,EAAEF,GAAG,CAACE,KAAK;YAChBC,IAAI,EAAEH,GAAG,CAACI,KAAK,CAAC,CAAC,CAAC,CAACC;WACpB,CAAC;UACF,IAAI,CAACxH,iBAAiB,GAAGmH,GAAG,CAACI,KAAK,CAAC,CAAC,CAAC,CAACE,QAAQ;UAC9C,IAAI,CAAC7H,qBAAqB,CAACuH,GAAG,CAACI,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC;UAC/C,IAAI,CAACvE,gBAAgB,GAAGkE,GAAG,CAACO,MAAM,CAACC,GAAG,CAAEC,KAAU,KAAM;YACtDvF,KAAK,EAAEuF,KAAK,CAACC,SAAS;YACtBzF,KAAK,EAAEwF,KAAK,CAACE;WACd,CAAC,CAAC;UACH,IAAI,CAAC7B,eAAe,CAACe,GAAG,CAAC,KAAK,CAAC;QACjC,CAAC;QACDe,KAAK,EAAGC,CAAC,IAAI;UACXC,OAAO,CAACF,KAAK,CAACC,CAAC,CAAC;UAChB,IAAI,CAAC/B,eAAe,CAACe,GAAG,CAAC,KAAK,CAAC;QACjC;OACD,CAAC;IACJ;IAEAR,qBAAqBA,CAAA;MACnB,OAAO,IAAI,CAACd,WAAW,CAACwC,KAAK,CAAC;QAC5Bb,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC5I,UAAU,CAAC0J,QAAQ,CAAC,CAAC;QAClCb,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC7I,UAAU,CAAC0J,QAAQ,CAAC;OACjC,CAAC;IACJ;IAEA1B,eAAeA,CAAA;MACb,OAAO,IAAI,CAACf,WAAW,CAACwC,KAAK,CAAC;QAC5BL,SAAS,EAAE,CAAC,IAAI,EAAE,CAACpJ,UAAU,CAAC0J,QAAQ,CAAC,CAAC;QACxCC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC3J,UAAU,CAAC0J,QAAQ,CAAC,CAAC;QACpCE,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC5J,UAAU,CAAC0J,QAAQ,CAAC,CAAC;QACvCG,SAAS,EAAE,CAAC,IAAI,EAAE,CAAC7J,UAAU,CAAC0J,QAAQ,CAAC,CAAC;QACxCI,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC9J,UAAU,CAAC0J,QAAQ,CAAC;OACrC,CAAC;IACJ;IAEAzB,cAAcA,CAAA;MACZ,OAAO,IAAI,CAAChB,WAAW,CAACwC,KAAK,CAAC;QAC5BT,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAChJ,UAAU,CAAC0J,QAAQ,CAAC,CAAC;QACrCK,WAAW,EAAE,CAAC,EAAE,EAAE,CAAC/J,UAAU,CAAC0J,QAAQ,CAAC;OACxC,CAAC;IACJ;IAEAhC,WAAWA,CAAA;MACT,IAAI,CAACN,eAAe,CAACmB,GAAG,CAAC,IAAI,CAAC;MAC9B,IAAI,CAACzB,qBAAqB,CAACkD,WAAW,EAAE,CAAC7B,SAAS,CAAC;QACjDM,IAAI,EAAGwB,gBAAqB,IAAI;UAC9B,IAAI,CAAC3I,QAAQ,CAACiH,GAAG,CACf0B,gBAAgB,CAACf,GAAG,CAAEgB,GAAQ,KAAM;YAClClI,IAAI,EAAEkI,GAAG,CAAClB,QAAQ;YAClBrF,KAAK,EAAEuG,GAAG,CAACnB;WACZ,CAAC,CAAC,CACJ;UACD,IAAI,CAAC3B,eAAe,CAACmB,GAAG,CAAC,KAAK,CAAC;QACjC,CAAC;QACDe,KAAK,EAAGC,CAAC,IAAI;UACXC,OAAO,CAACF,KAAK,CAACC,CAAC,CAAC;UAChB,IAAI,CAACnC,eAAe,CAACmB,GAAG,CAAC,KAAK,CAAC;QACjC;OACD,CAAC;IACJ;IAEAZ,WAAWA,CAAA;MACT,IAAI,CAACN,eAAe,CAACkB,GAAG,CAAC,IAAI,CAAC;MAC9B,IAAI,CAACzB,qBAAqB,CAACqD,WAAW,EAAE,CAAChC,SAAS,CAAC;QACjDM,IAAI,EAAG2B,KAAU,IAAI;UACnB,IAAI,CAAChH,QAAQ,CAACmF,GAAG,CAAC6B,KAAK,CAAC;UACxB,IAAI,CAACC,qBAAqB,EAAE;UAC5B,IAAI,CAAChD,eAAe,CAACkB,GAAG,CAAC,KAAK,CAAC;QACjC,CAAC;QACDe,KAAK,EAAGC,CAAC,IAAI;UACXC,OAAO,CAACF,KAAK,CAACC,CAAC,CAAC;UAChB,IAAI,CAAClC,eAAe,CAACkB,GAAG,CAAC,KAAK,CAAC;QACjC;OACD,CAAC;IACJ;IAEAX,aAAaA,CAAA;MACX,IAAI,CAACd,qBAAqB,CAACwD,aAAa,EAAE,CAACnC,SAAS,CAAC;QACnDM,IAAI,EAAG8B,OAAY,IAAI;UACrB,IAAI,CAAC1H,UAAU,CAAC0F,GAAG,CACjBgC,OAAO,CAACrB,GAAG,CAAEgB,GAAQ,KAAM;YACzBlI,IAAI,EAAEkI,GAAG,CAACM,UAAU;YACpB7G,KAAK,EAAEuG,GAAG,CAACO;WACZ,CAAC,CAAC,CACJ;UACD,IAAI,CAAC3H,kBAAkB,CAACyF,GAAG,CAACgC,OAAO,CAACrB,GAAG,CAAEgB,GAAQ,IAAKA,GAAG,CAACM,UAAU,CAAC,CAAC;QACxE,CAAC;QACDlB,KAAK,EAAGC,CAAC,IAAKC,OAAO,CAACF,KAAK,CAACC,CAAC;OAC9B,CAAC;IACJ;IAEA1B,aAAaA,CAAA;MACX,IAAI,CAACP,iBAAiB,CAACiB,GAAG,CAAC,IAAI,CAAC;MAChC,IAAI,CAACzB,qBAAqB,CAAC4D,YAAY,EAAE,CAACvC,SAAS,CAAC;QAClDM,IAAI,EAAGQ,MAAW,IAAI;UACpB,IAAI,CAAC3E,UAAU,CAACiE,GAAG,CACjBU,MAAM,CAACC,GAAG,CAAEgB,GAAQ,KAAM;YACxBtG,KAAK,EAAEsG,GAAG,CAACd,SAAS;YACpBzF,KAAK,EAAEuG,GAAG,CAACb;WACZ,CAAC,CAAC,CACJ;UACD,IAAI,CAAC/B,iBAAiB,CAACiB,GAAG,CAAC,KAAK,CAAC;QACnC,CAAC;QACDe,KAAK,EAAGC,CAAC,IAAI;UACXC,OAAO,CAACF,KAAK,CAACC,CAAC,CAAC;UAChB,IAAI,CAACjC,iBAAiB,CAACiB,GAAG,CAAC,KAAK,CAAC;QACnC;OACD,CAAC;IACJ;IAEAT,UAAUA,CAAA;MACR,IAAI,CAACP,cAAc,CAACgB,GAAG,CAAC,IAAI,CAAC;MAC7B,IAAI,CAACxB,gBAAgB,CAAC4D,wBAAwB,EAAE,CAACxC,SAAS,CAAC;QACzDM,IAAI,EAAGmC,IAAS,IAAI;UAClB,IAAI,CAACxE,aAAa,CAACmC,GAAG,CAACqC,IAAI,CAAC;UAC5B,IAAI,CAACC,iBAAiB,EAAE;UACxB,IAAI,CAACtD,cAAc,CAACgB,GAAG,CAAC,KAAK,CAAC;QAChC,CAAC;QACDe,KAAK,EAAGC,CAAC,IAAI;UACXC,OAAO,CAACF,KAAK,CAACC,CAAC,CAAC;UAChB,IAAI,CAAChC,cAAc,CAACgB,GAAG,CAAC,KAAK,CAAC;QAChC;OACD,CAAC;IACJ;IAEAsC,iBAAiBA,CAAA;MACf,IAAI,CAAC3F,UAAU,GAAG,IAAI,CAACkB,aAAa,EAAE,CAAC8C,GAAG,CAAE4B,GAAG,KAAM;QACnD9I,IAAI,EAAE8I,GAAG,CAACC,gBAAgB;QAC1BpH,KAAK,EAAEmH,GAAG,CAACnB,KAAK,CAAC5H,QAAQ;OAC1B,CAAC,CAAC;IACL;IAEAsI,qBAAqBA,CAAA;MACnB,IAAI,CAACjH,QAAQ,EAAE,CAAC4H,OAAO,CAAEC,IAAS,IAAI;QACpC,IAAI,CAACnF,kBAAkB,CAACoF,UAAU,CAChCD,IAAI,CAACnJ,MAAM,EACX,IAAI,CAACmF,WAAW,CAACkE,OAAO,CAAC,EAAE,CAAC,CAC7B;MACH,CAAC,CAAC;IACJ;IAEAC,UAAUA,CAACpJ,IAAY;MACrB,OAAO,IAAI,CAAC8D,kBAAkB,CAACuF,GAAG,CAACrJ,IAAI,CAAgB;IACzD;IAEAsJ,iBAAiBA,CAACxJ,MAAc;MAC9B,MAAMqJ,OAAO,GAAG,IAAI,CAACrF,kBAAkB,CAACuF,GAAG,CAACvJ,MAAM,CAAC;MACnD,IAAI,CAACqJ,OAAO,IAAI,CAACA,OAAO,CAACxH,KAAK,EAAE;QAC9B,OAAO,EAAE;MACX;MACA,OAAO,CAACwH,OAAO,CAACxH,KAAK,CAAC;IACxB;IAEAhD,aAAaA,CAAC4K,SAAiB;MAC7B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,OAAO,EAAE;IACX;IAEAjJ,iBAAiBA,CAACkJ,KAAU,EAAEC,WAAmB;MAC/C,MAAM7I,cAAc,GAAG4I,KAAK,CAACE,eAAe,CAACxC,GAAG,CAC7CyC,MAAW,IAAKA,MAAM,CAAChI,KAAK,CAC9B;MACD,IAAI,CAACmC,kBAAkB,CAACuF,GAAG,CAACI,WAAW,CAAC,EAAEG,QAAQ,CAAChJ,cAAc,CAAC;MAClE,IAAI,CAACK,YAAY,CAACwI,WAAW,CAAC,GAAGD,KAAK,CAACE,eAAe;IACxD;IAEArK,UAAUA,CAAA;MACR,IAAI,CAAC8E,mBAAmB,CAACoC,GAAG,CAAC,IAAI,CAAC;IACpC;IAEArE,aAAaA,CAACsH,KAAU,GAAG;IAE3BxH,gBAAgBA,CAACwH,KAAU;MACzB,MAAMK,MAAM,GAAG,IAAI,CAACrH,gBAAgB,CAACsH,IAAI,CACtCC,IAAI,IAAKA,IAAI,CAACpI,KAAK,KAAK6H,KAAK,CAAC7H,KAAK,CACrC;MACD,IAAI,CAACkI,MAAM,EAAE;QACX,IAAI,CAACrH,gBAAgB,CAACwH,IAAI,CAACR,KAAK,CAAC;MACnC;IACF;IAEA9H,cAAcA,CAACuI,QAAgB;MAC7B,IAAI,CAACzH,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAAC0H,MAAM,CACjDC,GAAG,IAAKA,GAAG,CAACxI,KAAK,KAAKsI,QAAQ,CAChC;IACH;IAEApK,SAASA,CAAC2J,KAAU,EAAEC,WAAmB;MACvC,IAAI,CAACxI,YAAY,CAACwI,WAAW,CAAC,GAAG,IAAI,CAACxI,YAAY,CAACwI,WAAW,CAAC,CAACS,MAAM,CACnEP,MAAM,IAAKA,MAAM,CAAChI,KAAK,KAAK6H,KAAK,CAAC7H,KAAK,CACzC;MACD,MAAMyI,MAAM,GAAG,IAAI,CAACnJ,YAAY,CAACwI,WAAW,CAAC,CAACvC,GAAG,CAAEyC,MAAM,IAAKA,MAAM,CAAChI,KAAK,CAAC;MAC3E,MAAM0I,sBAAsB,GAAG,IAAI,CAACpJ,YAAY,CAACwI,WAAW,CAAC,CAACvC,GAAG,CAC9DyC,MAAM,IAAKA,MAAM,CAAC3J,IAAI,CACxB;MACD,IAAI,CAAC8D,kBAAkB,CAACuF,GAAG,CAACI,WAAW,CAAC,EAAEG,QAAQ,CAACQ,MAAM,CAAC;MAC1D,IAAI,CAACxJ,cAAc,CAAC6I,WAAW,CAAC,GAAGY,sBAAsB;IAC3D;IAEAC,aAAaA,CAAA,GAAI;IAEjBlI,cAAcA,CAAA;MACZ,IAAI,CAAC8B,cAAc,CAACqC,GAAG,CAAC,IAAI,CAAC;IAC/B;IAEA5D,WAAWA,CAAC6G,KAAU;MACpB,MAAMe,aAAa,GAAGf,KAAK,CAACE,eAAe,GAAG,CAAC,CAAC,EAAE/H,KAAK;MACvD,MAAMwB,eAAe,GAAGqG,KAAK,CAACE,eAAe,GAAG,CAAC,CAAC,EAAE1J,IAAI;MACxD,IAAIuK,aAAa,EAAE;QACjB,IAAI,CAACjH,WAAW,GAAGiH,aAAa;QAChC,IAAI,CAACpH,eAAe,GAAGA,eAAe;QACtC,IAAI,CAACY,YAAY,CAAC4C,UAAU,CAAC;UAC3BgB,KAAK,EAAE4C,aAAa;UACpB3C,QAAQ,EAAE,EAAE;UACZC,SAAS,EAAE,EAAE;UACbC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAAC0C,WAAW,CAACD,aAAa,CAAC;QAC/B,IAAI,CAAC9G,cAAc,GAAG,EAAE;QACxB,IAAI,CAACG,eAAe,GAAG,EAAE;QACzB,IAAI,CAACiB,YAAY,GAAG,EAAE;QACtB,IAAI,CAACxB,kBAAkB,GAAG,EAAE;QAC5B,IAAI,CAACG,mBAAmB,GAAG,EAAE;QAC7B,IAAI,CAACG,gBAAgB,GAAG,EAAE;QAC1B,IAAI,CAACJ,cAAc,GAAG,EAAE;QACxB,IAAI,CAACG,WAAW,GAAG,EAAE;MACvB;IACF;IAEA8G,WAAWA,CAAC7C,KAAa;MACvB,MAAMmB,GAAG,GAAG,IAAI,CAAC1E,aAAa,EAAE,CAACqG,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAAC/C,KAAK,CAAC5H,QAAQ,EAAE,KAAK4H,KAAK,CAAC;MAC1E,IAAImB,GAAG,EAAE;QACP,IAAI,CAAC1F,aAAa,GAAG0F,GAAG,CAAC6B,OAAO,CAACzD,GAAG,CAAE0D,MAAW,KAAM;UACrD5K,IAAI,EAAE4K,MAAM,CAACC,UAAU;UACvBlJ,KAAK,EAAEiJ,MAAM,CAAChD,QAAQ,CAAC7H,QAAQ;SAChC,CAAC,CAAC;MACL,CAAC,MAAM;QACL,IAAI,CAACqD,aAAa,GAAG,EAAE;MACzB;IACF;IAEAP,cAAcA,CAAC2G,KAAU;MACvB,MAAMsB,gBAAgB,GAAGtB,KAAK,CAACE,eAAe,GAAG,CAAC,CAAC,EAAE/H,KAAK;MAC1D,MAAM0B,kBAAkB,GAAGmG,KAAK,CAACE,eAAe,GAAG,CAAC,CAAC,EAAE1J,IAAI;MAC3D,IAAI8K,gBAAgB,EAAE;QACpB,IAAI,CAACrH,cAAc,GAAGqH,gBAAgB;QACtC,IAAI,CAACzH,kBAAkB,GAAGA,kBAAkB;QAC5C,IAAI,CAACU,YAAY,CAAC4C,UAAU,CAAC;UAC3BiB,QAAQ,EAAEkD,gBAAgB;UAC1BjD,SAAS,EAAE,EAAE;UACbC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAACiD,YAAY,CAACD,gBAAgB,CAAC;QACnC,IAAI,CAAClH,eAAe,GAAG,EAAE;QACzB,IAAI,CAACiB,YAAY,GAAG,EAAE;QACtB,IAAI,CAACrB,mBAAmB,GAAG,EAAE;QAC7B,IAAI,CAACG,gBAAgB,GAAG,EAAE;QAC1B,IAAI,CAACD,WAAW,GAAG,EAAE;MACvB;IACF;IAEAqH,YAAYA,CAACnD,QAAgB;MAC3B,MAAMkB,GAAG,GAAG,IAAI,CAAC1E,aAAa,EAAE,CAACqG,IAAI,CAAEC,CAAC,IACtCA,CAAC,CAACC,OAAO,CAACb,IAAI,CAAEkB,CAAM,IAAKA,CAAC,CAACpD,QAAQ,CAAC7H,QAAQ,EAAE,KAAK6H,QAAQ,CAAC,CAC/D;MACD,IAAIkB,GAAG,EAAE;QACP,MAAM8B,MAAM,GAAG9B,GAAG,CAAC6B,OAAO,CAACF,IAAI,CAC5BO,CAAM,IAAKA,CAAC,CAACpD,QAAQ,CAAC7H,QAAQ,EAAE,KAAK6H,QAAQ,CAC/C;QACD,IAAIgD,MAAM,EAAE;UACV,IAAI,CAACrH,cAAc,GAAGqH,MAAM,CAACK,QAAQ,CAAC/D,GAAG,CAAEgE,OAAY,KAAM;YAC3DlL,IAAI,EAAEkL,OAAO,CAACC,WAAW;YACzBxJ,KAAK,EAAEuJ,OAAO,CAACrD,SAAS,CAAC9H,QAAQ;WAClC,CAAC,CAAC;QACL,CAAC,MAAM;UACL,IAAI,CAACwD,cAAc,GAAG,EAAE;QAC1B;MACF,CAAC,MAAM;QACL,IAAI,CAACA,cAAc,GAAG,EAAE;MAC1B;IACF;IAEAR,eAAeA,CAACyG,KAAU;MACxB,MAAM4B,iBAAiB,GAAG5B,KAAK,CAACE,eAAe,GAAG,CAAC,CAAC,EAAE/H,KAAK;MAC3D,MAAM6B,mBAAmB,GAAGgG,KAAK,CAACE,eAAe,GAAG,CAAC,CAAC,EAAE1J,IAAI;MAC5D,IAAIoL,iBAAiB,EAAE;QACrB,IAAI,CAACxH,eAAe,GAAGwH,iBAAiB;QACxC,IAAI,CAAC5H,mBAAmB,GAAGA,mBAAmB;QAC9C,IAAI,CAACO,YAAY,CAAC4C,UAAU,CAAC;UAAEkB,SAAS,EAAEuD,iBAAiB;UAAEC,IAAI,EAAE;QAAE,CAAE,CAAC;QACxE,IAAI,CAACC,SAAS,CAACF,iBAAiB,CAAC;QACjC,IAAI,CAACvG,YAAY,GAAG,EAAE;QACtB,IAAI,CAAClB,gBAAgB,GAAG,EAAE;MAC5B;IACF;IAEA2H,SAASA,CAACzD,SAAiB;MACzB,MAAMiB,GAAG,GAAG,IAAI,CAAC1E,aAAa,EAAE,CAACqG,IAAI,CAAEC,CAAC,IACtCA,CAAC,CAACC,OAAO,CAACb,IAAI,CAAEkB,CAAM,IACpBA,CAAC,CAACC,QAAQ,CAACnB,IAAI,CAAEyB,CAAM,IAAKA,CAAC,CAAC1D,SAAS,CAAC9H,QAAQ,EAAE,KAAK8H,SAAS,CAAC,CAClE,CACF;MACD,IAAIiB,GAAG,EAAE;QACP,MAAM8B,MAAM,GAAG9B,GAAG,CAAC6B,OAAO,CAACF,IAAI,CAAEO,CAAM,IACrCA,CAAC,CAACC,QAAQ,CAACnB,IAAI,CAAEyB,CAAM,IAAKA,CAAC,CAAC1D,SAAS,CAAC9H,QAAQ,EAAE,KAAK8H,SAAS,CAAC,CAClE;QACD,IAAI+C,MAAM,EAAE;UACV,MAAMM,OAAO,GAAGN,MAAM,CAACK,QAAQ,CAACR,IAAI,CACjCc,CAAM,IAAKA,CAAC,CAAC1D,SAAS,CAAC9H,QAAQ,EAAE,KAAK8H,SAAS,CACjD;UACD,IAAIqD,OAAO,EAAE;YACX,IAAI,CAACxH,WAAW,GAAGwH,OAAO,CAACM,KAAK,CAACtE,GAAG,CAAEmE,IAAS,KAAM;cACnDrL,IAAI,EAAEqL,IAAI,CAACI,QAAQ;cACnB9J,KAAK,EAAE0J,IAAI,CAACvD,MAAM,CAAC/H,QAAQ;aAC5B,CAAC,CAAC;UACL,CAAC,MAAM;YACL,IAAI,CAAC2D,WAAW,GAAG,EAAE;UACvB;QACF,CAAC,MAAM;UACL,IAAI,CAACA,WAAW,GAAG,EAAE;QACvB;MACF,CAAC,MAAM;QACL,IAAI,CAACA,WAAW,GAAG,EAAE;MACvB;IACF;IAEAT,YAAYA,CAACuG,KAAU;MACrB,MAAMkC,cAAc,GAAGlC,KAAK,CAACE,eAAe,GAAG,CAAC,CAAC,EAAE/H,KAAK;MACxD,MAAMgC,gBAAgB,GAAG6F,KAAK,CAACE,eAAe,GAAG,CAAC,CAAC,EAAE1J,IAAI;MACzD,IAAI0L,cAAc,EAAE;QAClB,IAAI,CAAC7G,YAAY,GAAG6G,cAAc;QAClC,IAAI,CAAC/H,gBAAgB,GAAGA,gBAAgB;QACxC,IAAI,CAACI,YAAY,CAAC4C,UAAU,CAAC;UAAEmB,MAAM,EAAE4D;QAAc,CAAE,CAAC;MAC1D;IACF;IAEAC,UAAUA,CAAA;MACR,IAAI,IAAI,CAAC3H,WAAW,CAAC4H,KAAK,EAAE;QAC1B,MAAMC,OAAO,GAAG,IAAI,CAAC7H,WAAW,CAACrC,KAAK;QACtC,IAAI,CAACmD,qBAAqB,CAAC6G,UAAU,CAACE,OAAO,CAAC,CAAC1F,SAAS,CAAC;UACvDM,IAAI,EAAGC,GAAQ,IAAI;YACjB,IAAI,CAACpH,QAAQ,CAACwM,MAAM,CAAEC,WAAW,IAAK,CACpC,GAAGA,WAAW,EACd;cAAE/L,IAAI,EAAE0G,GAAG,CAACM,QAAQ;cAAErF,KAAK,EAAE+E,GAAG,CAACK;YAAM,CAAE,CAC1C,CAAC;YACF,IAAI,CAACxH,iBAAiB,GAAGmH,GAAG,CAACM,QAAQ;YACrC,IAAI,CAAC7H,qBAAqB,CAACuH,GAAG,CAACK,MAAM,CAAC;YACtC,IAAI,CAACiF,eAAe,EAAE;YACtB,IAAI,CAAChI,WAAW,CAACiI,KAAK,EAAE;YACxB,IAAI,CAACtH,oBAAoB,GAAG,yCAAyC;YACrE,IAAI,CAACN,eAAe,CAACkC,GAAG,CAAC,IAAI,CAAC;UAChC,CAAC;UACDe,KAAK,EAAGC,CAAC,IAAKC,OAAO,CAACF,KAAK,CAACC,CAAC;SAC9B,CAAC;MACJ;IACF;IAEA2E,WAAWA,CAAA;MACT,IAAI,IAAI,CAACnI,YAAY,CAAC6H,KAAK,EAAE;QAC3B,MAAM;UAAExE,SAAS;UAAEU;QAAM,CAAE,GAAG,IAAI,CAAC/D,YAAY,CAACpC,KAAK;QACrD,IAAI,CAACmD,qBAAqB,CAACoH,WAAW,CAAC9E,SAAS,EAAEU,MAAM,CAAC,CAAC3B,SAAS,CAAC;UAClEM,IAAI,EAAGC,GAAQ,IAAI;YACjB,IAAI,CAAClE,gBAAgB,CAACwH,IAAI,CAAC;cACzBpI,KAAK,EAAE8E,GAAG,CAACU,SAAS;cACpBzF,KAAK,EAAE+E,GAAG,CAACW;aACZ,CAAC;YACF,IAAI,CAAC2E,eAAe,EAAE;YACtB,IAAI,CAACrH,oBAAoB,GACvB,0CAA0C;YAC5C,IAAI,CAACN,eAAe,CAACkC,GAAG,CAAC,IAAI,CAAC;UAChC,CAAC;UACDe,KAAK,EAAGC,CAAC,IAAKC,OAAO,CAACF,KAAK,CAACC,CAAC;SAC9B,CAAC;MACJ;IACF;IAEAyE,eAAeA,CAAA;MACb,IAAI,CAAC9H,cAAc,CAACqC,GAAG,CAAC,KAAK,CAAC;MAC9B,IAAI,CAACpC,mBAAmB,CAACoC,GAAG,CAAC,KAAK,CAAC;MACnC,IAAI,CAACvC,WAAW,CAACiI,KAAK,EAAE;IAC1B;IAEAE,iBAAiBA,CAAA;MACf,IAAI,CAAC9H,eAAe,CAACkC,GAAG,CAAC,KAAK,CAAC;IACjC;IAEApH,qBAAqBA,CAACqK,KAAU;MAC9B,MAAMzC,MAAM,GAAGyC,KAAK,EAAEE,eAAe,GAAG,CAAC,CAAC,EAAE/H,KAAK,IAAI6H,KAAK;MAC1D,IAAI,CAAC1F,kBAAkB,CAACuF,GAAG,CAAC,MAAM,CAAC,EAAE1C,UAAU,CAACI,MAAM,CAAC;MACvD,IAAI,CAAC1B,eAAe,CAACkB,GAAG,CAAC,IAAI,CAAC;MAC9B,IAAI,CAACzB,qBAAqB,CAACsH,wBAAwB,CAACrF,MAAM,CAAC,CAACZ,SAAS,CAAC;QACpEM,IAAI,EAAGC,GAAQ,IAAI;UACjB,IAAI,CAAC2F,mBAAmB,EAAE;UAC1B,IAAI,CAAC/H,iBAAiB,CAACiC,GAAG,CAACG,GAAG,CAAC4F,aAAa,CAAC;UAC7C,IAAI,CAAC5F,GAAG,CAAC4F,aAAa,EAAEC,MAAM,EAAE;YAC9B,IAAI,CAACxL,eAAe,CAACwF,GAAG,CAAC,IAAI,CAAC;YAC9B,IAAI,CAACpG,oBAAoB,CAACoG,GAAG,CAAC,IAAI,CAAC;YACnC;UACF;UACA,IAAI,CAACxF,eAAe,CAACwF,GAAG,CAAC,KAAK,CAAC;UAC/B,IAAI,CAACpG,oBAAoB,CAACoG,GAAG,CAAC,KAAK,CAAC;UACpC,IAAI,CAACiG,+BAA+B,EAAE;QACxC,CAAC;QACDlF,KAAK,EAAGC,CAAC,IAAKC,OAAO,CAACF,KAAK,CAACC,CAAC,CAAC;QAC9BkF,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAACpH,eAAe,CAACkB,GAAG,CAAC,KAAK;OAC/C,CAAC;IACJ;IAEAiG,+BAA+BA,CAAA;MAC7B,MAAME,SAAS,GAAG,IAAIC,GAAG,CACvB,IAAI,CAACrI,iBAAiB,EAAE,CAAC4C,GAAG,CAAE6C,IAAI,IAAK,CAACA,IAAI,CAACd,IAAI,EAAEc,IAAI,CAACxB,OAAO,CAAC,CAAC,CAClE;MACD,MAAMqE,YAAY,GAAG,IAAID,GAAG,CAC1B,IAAI,CAAC9L,UAAU,EAAE,CAACqG,GAAG,CAAE6C,IAAI,IAAK,CAACA,IAAI,CAAC/J,IAAI,EAAE+J,IAAI,CAACpI,KAAK,CAAC,CAAC,CACzD;MAED,IAAI,CAACP,QAAQ,EAAE,CAAC4H,OAAO,CAAEC,IAAI,IAAI;QAC/B,MAAM4D,WAAW,GAAG,IAAI,CAACvI,iBAAiB,EAAE,CAACmG,IAAI,CAC9CqC,EAAE,IAAKA,EAAE,CAAC7D,IAAI,KAAKA,IAAI,CAACtI,QAAQ,CAClC;QACD,IAAIkM,WAAW,EAAE;UACf,IAAI,CAACjM,cAAc,CAACqI,IAAI,CAACnJ,MAAM,CAACC,QAAQ,EAAE,CAAC,GAAG8M,WAAW,CAACtE,OAAO;UACjE,IAAI,CAACtH,YAAY,CAACgI,IAAI,CAACnJ,MAAM,CAACC,QAAQ,EAAE,CAAC,GAAG8M,WAAW,CAACtE,OAAO,CAACrB,GAAG,CAChE6F,EAAU,KAAM;YAAE/M,IAAI,EAAE+M;UAAE,CAAE,CAAC,CAC/B;QACH;QAEA,MAAMC,eAAe,GAAG/D,IAAI,CAACnJ,MAAM,CAACC,QAAQ,EAAE;QAC9C,MAAM0J,WAAW,GAAG,IAAI,CAAC3F,kBAAkB,CAACuF,GAAG,CAAC2D,eAAe,CAAC;QAChE,MAAMC,WAAW,GAAGP,SAAS,CAACrD,GAAG,CAACJ,IAAI,CAACtI,QAAQ,CAAC;QAChD,IAAI8I,WAAW,IAAIwD,WAAW,EAAE;UAC9B,MAAMC,SAAS,GAAGD,WAAW,CAC1B/F,GAAG,CAAElH,IAAS,IAAK4M,YAAY,CAACvD,GAAG,CAACrJ,IAAI,CAAC,CAAC,CAC1CkK,MAAM,CAAE7D,EAAO,IAAKA,EAAE,KAAK8G,SAAS,CAAC;UACxC1D,WAAW,CAAC9C,UAAU,CAACuG,SAAS,CAAC;QACnC;MACF,CAAC,CAAC;IACJ;IAEAb,mBAAmBA,CAAA;MACjB,IAAI,CAACjL,QAAQ,EAAE,CAAC4H,OAAO,CAAEC,IAAI,IAAI;QAC/B,MAAM+D,eAAe,GAAG/D,IAAI,CAACnJ,MAAM,CAACC,QAAQ,EAAE;QAC9C,IAAI,CAACa,cAAc,CAACoM,eAAe,CAAC,GAAG,EAAE;QACzC,IAAI,CAAC/L,YAAY,CAAC+L,eAAe,CAAC,GAAG,EAAE;QACvC,IAAI,CAAClJ,kBAAkB,CAACuF,GAAG,CAAC2D,eAAe,CAAC,EAAErG,UAAU,CAAC,EAAE,CAAC;MAC9D,CAAC,CAAC;IACJ;IAEAyG,MAAMA,CAAA;MACJ,IAAI,CAACpI,MAAM,CAACqI,QAAQ,CAAC,CAAC,yBAAyB,CAAC,CAAC;IACnD;IAEAC,UAAUA,CAAA;MACR,IAAI,IAAI,CAACxJ,kBAAkB,CAAC8H,KAAK,IAAI,IAAI,CAACpJ,gBAAgB,EAAE+J,MAAM,EAAE;QAClE,MAAM;UAAE3F,KAAK;UAAEC,IAAI;UAAE,GAAG0G;QAAe,CAAE,GAAG,IAAI,CAACzJ,kBAAkB,CAACnC,KAAK;QACzE,MAAMoF,MAAM,GAAGyG,QAAQ,CAAC3G,IAAI,CAAC;QAC7B,MAAM4G,OAAO,GAAG,CAAC1G,MAAM,CAAC;QACxB,MAAM8E,OAAO,GAAQ;UACnBjF,KAAK,EAAEA,KAAK;UACZ6G,OAAO,EAAEA,OAAO;UAChBC,QAAQ,EAAE,IAAI,CAAClL,gBAAgB,CAAC0E,GAAG,CAAEC,KAAK,IAAKA,KAAK,CAACxF,KAAK,CAAC;UAC3DgM,YAAY,EACV,IAAI,CAACzI,YAAY,CAAC0I,aAAa,EAAE,IAAI;SACxC;QAED,IAAI,IAAI,CAACzN,oBAAoB,EAAE,EAAE;UAC/B,MAAM0N,WAAW,GAAgD,EAAE;UAEnEC,MAAM,CAACC,OAAO,CAACR,eAAe,CAAC,CAACvE,OAAO,CAAC,CAAC,CAAClJ,MAAM,EAAEoN,SAAS,CAAC,KAAI;YAC9D,IAAIc,KAAK,CAACC,OAAO,CAACf,SAAS,CAAC,IAAIA,SAAS,CAACX,MAAM,GAAG,CAAC,EAAE;cACpDW,SAAS,CAAClE,OAAO,CAAEP,QAAgB,IAAI;gBACrCoF,WAAW,CAAC7D,IAAI,CAAC;kBACflK,MAAM,EAAE0N,QAAQ,CAAC1N,MAAM,CAAC;kBACxB2I,QAAQ,EAAEA;iBACX,CAAC;cACJ,CAAC,CAAC;YACJ;UACF,CAAC,CAAC;UAEFoD,OAAO,CAACqC,qBAAqB,GAAG;YAC9BnH,MAAM,EAAEA,MAAM;YACd8G,WAAW,EAAEA;WACd;QACH;QAEA,IAAI,IAAI,CAACtJ,MAAM,EAAE;UACf,IAAI,CAACO,qBAAqB,CAACqJ,UAAU,CAACtC,OAAO,EAAE,IAAI,CAACtH,MAAM,CAAC,CAAC4B,SAAS,CAAC;YACpEM,IAAI,EAAGC,GAAQ,IAAI;cACjB,IAAI,CAAChC,kBAAkB,GAAG,0BAA0B;cACpD,IAAI,CAACF,oBAAoB,GAAG,IAAI;YAClC,CAAC;YACD8C,KAAK,EAAGC,CAAC,IAAI;cACXC,OAAO,CAACF,KAAK,CAACC,CAAC,CAAC;cAChB,IAAI,CAAC3C,iBAAiB,GAAG2C,CAAC,CAACD,KAAK,CAAC8G,OAAO,IAAI7G,CAAC,CAACD,KAAK;cACnD,IAAI,CAAC7C,cAAc,GAAG,IAAI;YAC5B;WACD,CAAC;QACJ,CAAC,MAAM;UACL,IAAI,CAACK,qBAAqB,CAACwI,UAAU,CAACzB,OAAO,CAAC,CAAC1F,SAAS,CAAC;YACvDM,IAAI,EAAGC,GAAQ,IAAI;cACjB,IAAI,CAAChC,kBAAkB,GAAG,yBAAyB;cACnD,IAAI,CAACF,oBAAoB,GAAG,IAAI;YAClC,CAAC;YACD8C,KAAK,EAAGC,CAAC,IAAI;cACXC,OAAO,CAACF,KAAK,CAACC,CAAC,CAAC;cAChB,IAAI,CAAC3C,iBAAiB,GAAG2C,CAAC,CAACD,KAAK,CAAC8G,OAAO,IAAI7G,CAAC,CAACD,KAAK;cACnD,IAAI,CAAC7C,cAAc,GAAG,IAAI;YAC5B;WACD,CAAC;QACJ;MACF;IACF;IAEA4J,oBAAoBA,CAAA;MAClB,IAAI,CAAC7J,oBAAoB,GAAG,KAAK;MACjC,IAAI,CAACQ,MAAM,CAACqI,QAAQ,CAAC,CAAC,yBAAyB,CAAC,CAAC;IACnD;IAEAiB,mBAAmBA,CAAA;MACjB,OAAO,IAAI,CAACxK,kBAAkB,CAAC8H,KAAK,IAAI,IAAI,CAACpJ,gBAAgB,EAAE+J,MAAM,GAAG,CAAC;IAC3E;IAEAgC,eAAeA,CAAA;MACb,IAAI,CAAC9J,cAAc,GAAG,KAAK;IAC7B;;uCA7mBWZ,gBAAgB;IAAA;;YAAhBA,gBAAgB;MAAA2K,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCtCzB3Q,EAFJ,CAAAC,cAAA,aAAuC,aACC,kBAOnC;UALCD,EAAA,CAAAS,UAAA,uBAAAoQ,wDAAA;YAAA,OAAaD,GAAA,CAAA3B,MAAA,EAAQ;UAAA,EAAC;UAKvBjP,EAAA,CAAAG,YAAA,EAAW;UACZH,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAE,MAAA,eAAQ;UACdF,EADc,CAAAG,YAAA,EAAK,EACb;UAGFH,EAFJ,CAAAC,cAAA,aAAyC,cACA,aACH;UAwC9BD,EAtCF,CAAA8Q,UAAA,IAAAC,uCAAA,iBAA0B,IAAAC,uCAAA,iBAcjB,KAAAC,wCAAA,iBAQiB,KAAAC,wCAAA,iBAgBjB;UAMXlR,EAAA,CAAAG,YAAA,EAAM;UAGJH,EADF,CAAAC,cAAA,cAAwC,UAClC;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAK;UA2CrBH,EA1CF,CAAA8Q,UAAA,KAAAK,wCAAA,OAA0B,KAAAC,wCAAA,iBA0CjB;UAcXpR,EAAA,CAAAG,YAAA,EAAM;UAGJH,EADF,CAAAC,cAAA,eAA6B,cACN;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACtCH,EAAA,CAAAC,cAAA,eAA2B;UAyCvBD,EAxCF,CAAA8Q,UAAA,KAAAO,wCAAA,OAA4B,KAAAC,wCAAA,kBAwCnB;UAQftR,EAFI,CAAAG,YAAA,EAAM,EACF,EACD;UAELH,EADF,CAAAC,cAAA,eAAoC,sBAQjC;UAHCD,EAAA,CAAAS,UAAA,uBAAA8Q,2DAAA;YAAA,OAAaX,GAAA,CAAA3B,MAAA,EAAQ;UAAA,EAAC;UAIxBjP,EAAA,CAAAG,YAAA,EAAa;UACbH,EAAA,CAAAC,cAAA,sBAQC;UAJCD,EAAA,CAAAS,UAAA,uBAAA+Q,2DAAA;YAAA,OAAaZ,GAAA,CAAAzB,UAAA,EAAY;UAAA,EAAC;UAQlCnP,EAHM,CAAAG,YAAA,EAAa,EACT,EACF,EACF;UAcFH,EAZJ,CAAAC,cAAA,qBAUC,eACoC,gBACoB;UAyEjDD,EAxEF,CAAA8Q,UAAA,KAAAW,wCAAA,SAAyB,KAAAC,wCAAA,QAwEhB;UA+BX1R,EAAA,CAAAG,YAAA,EAAO;UAELH,EADF,CAAAC,cAAA,eAA+B,sBAQ5B;UAHCD,EAAA,CAAAS,UAAA,uBAAAkR,2DAAA;YAAA,OAAaf,GAAA,CAAA/C,eAAA,EAAiB;UAAA,EAAC;UAIjC7N,EAAA,CAAAG,YAAA,EAAa;UACbH,EAAA,CAAAC,cAAA,sBAQC;UAJCD,EAAA,CAAAS,UAAA,uBAAAmR,2DAAA;YAAA,OAAahB,GAAA,CAAA7C,WAAA,EAAa;UAAA,EAAC;UAQnC/N,EAHM,CAAAG,YAAA,EAAa,EACT,EACF,EACI;UAiBFH,EAfV,CAAAC,cAAA,qBAUC,eACoC,gBACmB,eACnB,eACM,iBAE9B;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAClB;UACDH,EAAA,CAAAI,SAAA,uBASc;UAChBJ,EAAA,CAAAG,YAAA,EAAM;UAGJH,EADF,CAAAC,cAAA,eAAmC,iBAE9B;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EACb;UACDH,EAAA,CAAAI,SAAA,uBASc;UAGpBJ,EAFI,CAAAG,YAAA,EAAM,EACF,EACD;UAELH,EADF,CAAAC,cAAA,eAA+B,sBAQ5B;UAHCD,EAAA,CAAAS,UAAA,uBAAAoR,2DAAA;YAAA,OAAajB,GAAA,CAAA/C,eAAA,EAAiB;UAAA,EAAC;UAIjC7N,EAAA,CAAAG,YAAA,EAAa;UACbH,EAAA,CAAAC,cAAA,sBAQC;UAJCD,EAAA,CAAAS,UAAA,uBAAAqR,2DAAA;YAAA,OAAalB,GAAA,CAAApD,UAAA,EAAY;UAAA,EAAC;UAQlCxN,EAHM,CAAAG,YAAA,EAAa,EACT,EACF,EACI;UAGZH,EAAA,CAAAC,cAAA,qBAUC;UADCD,EAAA,CAAAS,UAAA,oBAAAsR,uDAAA;YAAA,OAAUnB,GAAA,CAAA5C,iBAAA,EAAmB;UAAA,EAAC;UAEhChO,EAAA,CAAAG,YAAA,EAAY;UAGZH,EAAA,CAAAC,cAAA,qBAeC;UADCD,EADA,CAAAS,UAAA,qBAAAuR,wDAAA;YAAA,OAAWpB,GAAA,CAAAV,oBAAA,EAAsB;UAAA,EAAC,oBAAA+B,uDAAA;YAAA,OACxBrB,GAAA,CAAAV,oBAAA,EAAsB;UAAA,EAAC;UAEnClQ,EAAA,CAAAG,YAAA,EAAY;UAGZH,EAAA,CAAAC,cAAA,qBAeC;UADCD,EADA,CAAAS,UAAA,qBAAAyR,wDAAA;YAAA,OAAWtB,GAAA,CAAAR,eAAA,EAAiB;UAAA,EAAC,oBAAA+B,uDAAA;YAAA,OACnBvB,GAAA,CAAAR,eAAA,EAAiB;UAAA,EAAC;UAE9BpQ,EAAA,CAAAG,YAAA,EAAY;;;UAvcNH,EAAA,CAAAK,SAAA,GAAe;UACfL,EADA,CAAAM,UAAA,gBAAe,gBACA;UAOXN,EAAA,CAAAK,SAAA,GAAgC;UAAhCL,EAAA,CAAAM,UAAA,cAAAsQ,GAAA,CAAAjL,kBAAA,CAAgC;UAGlC3F,EAAA,CAAAK,SAAA,GAmBC;UAnBDL,EAAA,CAAAoS,aAAA,EAAAxB,GAAA,CAAAvJ,eAAA,WAmBC;UAGDrH,EAAA,CAAAK,SAAA,GAqBC;UArBDL,EAAA,CAAAoS,aAAA,EAAAxB,GAAA,CAAA3J,eAAA,aAqBC;UAKDjH,EAAA,CAAAK,SAAA,GAuDC;UAvDDL,EAAA,CAAAoS,aAAA,EAAAxB,GAAA,CAAA1J,eAAA,aAuDC;UAMClH,EAAA,CAAAK,SAAA,GA6CC;UA7CDL,EAAA,CAAAoS,aAAA,EAAAxB,GAAA,CAAAzJ,iBAAA,aA6CC;UAUHnH,EAAA,CAAAK,SAAA,GAAgB;UAAhBL,EAAA,CAAAM,UAAA,iBAAgB;UAShBN,EAAA,CAAAK,SAAA,EAAgB;UAEhBL,EAFA,CAAAM,UAAA,iBAAgB,cAAAsQ,GAAA,CAAAT,mBAAA,GAEmB;UAQzCnQ,EAAA,CAAAK,SAAA,EAAyB;UAQzBL,EARA,CAAAM,UAAA,SAAAsQ,GAAA,CAAA7K,cAAA,GAAyB,oBACN,yBACK,oBACL,4BACQ,sBACN,qBAED,uBACE;UAGd/F,EAAA,CAAAK,SAAA,GAA0B;UAA1BL,EAAA,CAAAM,UAAA,cAAAsQ,GAAA,CAAAhL,YAAA,CAA0B;UAC9B5F,EAAA,CAAAK,SAAA,EAsGC;UAtGDL,EAAA,CAAAoS,aAAA,EAAAxB,GAAA,CAAAxJ,cAAA,aAsGC;UAQCpH,EAAA,CAAAK,SAAA,GAAgB;UAAhBL,EAAA,CAAAM,UAAA,iBAAgB;UAShBN,EAAA,CAAAK,SAAA,EAAgB;UAEhBL,EAFA,CAAAM,UAAA,iBAAgB,cAAAsQ,GAAA,CAAAhL,YAAA,CAAA6H,KAAA,CAEgB;UAQtCzN,EAAA,CAAAK,SAAA,EAA8B;UAQ9BL,EARA,CAAAM,UAAA,SAAAsQ,GAAA,CAAA5K,mBAAA,GAA8B,oBACX,yBACK,oBACL,4BACQ,sBACN,qBAED,uBACE;UAGdhG,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAAM,UAAA,cAAAsQ,GAAA,CAAA/K,WAAA,CAAyB;UAWvB7F,EAAA,CAAAK,SAAA,GAAiB;UACjBL,EADA,CAAAM,UAAA,kBAAiB,oBACE;UAenBN,EAAA,CAAAK,SAAA,GAAiB;UACjBL,EADA,CAAAM,UAAA,kBAAiB,oBACE;UAavBN,EAAA,CAAAK,SAAA,GAAgB;UAAhBL,EAAA,CAAAM,UAAA,iBAAgB;UAShBN,EAAA,CAAAK,SAAA,EAAgB;UAEhBL,EAFA,CAAAM,UAAA,iBAAgB,cAAAsQ,GAAA,CAAA/K,WAAA,CAAA4H,KAAA,CAEe;UASrCzN,EAAA,CAAAK,SAAA,EAA0B;UAO1BL,EAPA,CAAAM,UAAA,SAAAsQ,GAAA,CAAA1K,eAAA,GAA0B,oBACP,YAAA0K,GAAA,CAAApK,oBAAA,CACa,wBACT,mBAGL,qBACE;UAOpBxG,EAAA,CAAAK,SAAA,EAA6B;UAW7BL,EAXA,CAAAM,UAAA,SAAAsQ,GAAA,CAAAvK,oBAAA,CAA6B,oBACV,YAAAuK,GAAA,CAAArK,kBAAA,CACW,wBACP,mBAGL,qBACE,qBACA,4BACO,mCACO,sCACG;UAQrCvG,EAAA,CAAAK,SAAA,EAAuB;UAWvBL,EAXA,CAAAM,UAAA,SAAAsQ,GAAA,CAAAtK,cAAA,CAAuB,mBACL,YAAAsK,GAAA,CAAAnK,iBAAA,CACW,wBACN,mBAGL,qBACE,qBACA,4BACO,mCACO,sCACG;;;qBD5anC7G,mBAAmB,EAAAyS,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,oBAAA,EAAAH,EAAA,CAAAI,iBAAA,EAAAJ,EAAA,CAAAK,kBAAA,EAAAL,EAAA,CAAAM,eAAA,EACnBpT,mBAAmB,EACnBE,iBAAiB,EACjBJ,kBAAkB,EAClBC,eAAe,EACfF,wBAAwB,EACxBI,eAAe,EACfE,cAAc,EACdP,aAAa;MAAAyT,MAAA;IAAA;;SAKJlN,gBAAgB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}