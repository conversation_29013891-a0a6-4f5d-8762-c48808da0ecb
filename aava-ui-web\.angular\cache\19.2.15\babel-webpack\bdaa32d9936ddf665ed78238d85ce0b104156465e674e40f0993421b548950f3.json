{"ast": null, "code": "function contextListener(listener) {\n  return function (event) {\n    listener.call(this, event, this.__data__);\n  };\n}\nfunction parseTypenames(typenames) {\n  return typenames.trim().split(/^|\\s+/).map(function (t) {\n    var name = \"\",\n      i = t.indexOf(\".\");\n    if (i >= 0) name = t.slice(i + 1), t = t.slice(0, i);\n    return {\n      type: t,\n      name: name\n    };\n  });\n}\nfunction onRemove(typename) {\n  return function () {\n    var on = this.__on;\n    if (!on) return;\n    for (var j = 0, i = -1, m = on.length, o; j < m; ++j) {\n      if (o = on[j], (!typename.type || o.type === typename.type) && o.name === typename.name) {\n        this.removeEventListener(o.type, o.listener, o.options);\n      } else {\n        on[++i] = o;\n      }\n    }\n    if (++i) on.length = i;else delete this.__on;\n  };\n}\nfunction onAdd(typename, value, options) {\n  return function () {\n    var on = this.__on,\n      o,\n      listener = contextListener(value);\n    if (on) for (var j = 0, m = on.length; j < m; ++j) {\n      if ((o = on[j]).type === typename.type && o.name === typename.name) {\n        this.removeEventListener(o.type, o.listener, o.options);\n        this.addEventListener(o.type, o.listener = listener, o.options = options);\n        o.value = value;\n        return;\n      }\n    }\n    this.addEventListener(typename.type, listener, options);\n    o = {\n      type: typename.type,\n      name: typename.name,\n      value: value,\n      listener: listener,\n      options: options\n    };\n    if (!on) this.__on = [o];else on.push(o);\n  };\n}\nexport default function (typename, value, options) {\n  var typenames = parseTypenames(typename + \"\"),\n    i,\n    n = typenames.length,\n    t;\n  if (arguments.length < 2) {\n    var on = this.node().__on;\n    if (on) for (var j = 0, m = on.length, o; j < m; ++j) {\n      for (i = 0, o = on[j]; i < n; ++i) {\n        if ((t = typenames[i]).type === o.type && t.name === o.name) {\n          return o.value;\n        }\n      }\n    }\n    return;\n  }\n  on = value ? onAdd : onRemove;\n  for (i = 0; i < n; ++i) this.each(on(typenames[i], value, options));\n  return this;\n}", "map": {"version": 3, "names": ["contextListener", "listener", "event", "call", "__data__", "parseTypenames", "typenames", "trim", "split", "map", "t", "name", "i", "indexOf", "slice", "type", "onRemove", "typename", "on", "__on", "j", "m", "length", "o", "removeEventListener", "options", "onAdd", "value", "addEventListener", "push", "n", "arguments", "node", "each"], "sources": ["C:/console/aava-ui-web/node_modules/d3-selection/src/selection/on.js"], "sourcesContent": ["function contextListener(listener) {\n  return function(event) {\n    listener.call(this, event, this.__data__);\n  };\n}\n\nfunction parseTypenames(typenames) {\n  return typenames.trim().split(/^|\\s+/).map(function(t) {\n    var name = \"\", i = t.indexOf(\".\");\n    if (i >= 0) name = t.slice(i + 1), t = t.slice(0, i);\n    return {type: t, name: name};\n  });\n}\n\nfunction onRemove(typename) {\n  return function() {\n    var on = this.__on;\n    if (!on) return;\n    for (var j = 0, i = -1, m = on.length, o; j < m; ++j) {\n      if (o = on[j], (!typename.type || o.type === typename.type) && o.name === typename.name) {\n        this.removeEventListener(o.type, o.listener, o.options);\n      } else {\n        on[++i] = o;\n      }\n    }\n    if (++i) on.length = i;\n    else delete this.__on;\n  };\n}\n\nfunction onAdd(typename, value, options) {\n  return function() {\n    var on = this.__on, o, listener = contextListener(value);\n    if (on) for (var j = 0, m = on.length; j < m; ++j) {\n      if ((o = on[j]).type === typename.type && o.name === typename.name) {\n        this.removeEventListener(o.type, o.listener, o.options);\n        this.addEventListener(o.type, o.listener = listener, o.options = options);\n        o.value = value;\n        return;\n      }\n    }\n    this.addEventListener(typename.type, listener, options);\n    o = {type: typename.type, name: typename.name, value: value, listener: listener, options: options};\n    if (!on) this.__on = [o];\n    else on.push(o);\n  };\n}\n\nexport default function(typename, value, options) {\n  var typenames = parseTypenames(typename + \"\"), i, n = typenames.length, t;\n\n  if (arguments.length < 2) {\n    var on = this.node().__on;\n    if (on) for (var j = 0, m = on.length, o; j < m; ++j) {\n      for (i = 0, o = on[j]; i < n; ++i) {\n        if ((t = typenames[i]).type === o.type && t.name === o.name) {\n          return o.value;\n        }\n      }\n    }\n    return;\n  }\n\n  on = value ? onAdd : onRemove;\n  for (i = 0; i < n; ++i) this.each(on(typenames[i], value, options));\n  return this;\n}\n"], "mappings": "AAAA,SAASA,eAAeA,CAACC,QAAQ,EAAE;EACjC,OAAO,UAASC,KAAK,EAAE;IACrBD,QAAQ,CAACE,IAAI,CAAC,IAAI,EAAED,KAAK,EAAE,IAAI,CAACE,QAAQ,CAAC;EAC3C,CAAC;AACH;AAEA,SAASC,cAAcA,CAACC,SAAS,EAAE;EACjC,OAAOA,SAAS,CAACC,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,OAAO,CAAC,CAACC,GAAG,CAAC,UAASC,CAAC,EAAE;IACrD,IAAIC,IAAI,GAAG,EAAE;MAAEC,CAAC,GAAGF,CAAC,CAACG,OAAO,CAAC,GAAG,CAAC;IACjC,IAAID,CAAC,IAAI,CAAC,EAAED,IAAI,GAAGD,CAAC,CAACI,KAAK,CAACF,CAAC,GAAG,CAAC,CAAC,EAAEF,CAAC,GAAGA,CAAC,CAACI,KAAK,CAAC,CAAC,EAAEF,CAAC,CAAC;IACpD,OAAO;MAACG,IAAI,EAAEL,CAAC;MAAEC,IAAI,EAAEA;IAAI,CAAC;EAC9B,CAAC,CAAC;AACJ;AAEA,SAASK,QAAQA,CAACC,QAAQ,EAAE;EAC1B,OAAO,YAAW;IAChB,IAAIC,EAAE,GAAG,IAAI,CAACC,IAAI;IAClB,IAAI,CAACD,EAAE,EAAE;IACT,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAER,CAAC,GAAG,CAAC,CAAC,EAAES,CAAC,GAAGH,EAAE,CAACI,MAAM,EAAEC,CAAC,EAAEH,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;MACpD,IAAIG,CAAC,GAAGL,EAAE,CAACE,CAAC,CAAC,EAAE,CAAC,CAACH,QAAQ,CAACF,IAAI,IAAIQ,CAAC,CAACR,IAAI,KAAKE,QAAQ,CAACF,IAAI,KAAKQ,CAAC,CAACZ,IAAI,KAAKM,QAAQ,CAACN,IAAI,EAAE;QACvF,IAAI,CAACa,mBAAmB,CAACD,CAAC,CAACR,IAAI,EAAEQ,CAAC,CAACtB,QAAQ,EAAEsB,CAAC,CAACE,OAAO,CAAC;MACzD,CAAC,MAAM;QACLP,EAAE,CAAC,EAAEN,CAAC,CAAC,GAAGW,CAAC;MACb;IACF;IACA,IAAI,EAAEX,CAAC,EAAEM,EAAE,CAACI,MAAM,GAAGV,CAAC,CAAC,KAClB,OAAO,IAAI,CAACO,IAAI;EACvB,CAAC;AACH;AAEA,SAASO,KAAKA,CAACT,QAAQ,EAAEU,KAAK,EAAEF,OAAO,EAAE;EACvC,OAAO,YAAW;IAChB,IAAIP,EAAE,GAAG,IAAI,CAACC,IAAI;MAAEI,CAAC;MAAEtB,QAAQ,GAAGD,eAAe,CAAC2B,KAAK,CAAC;IACxD,IAAIT,EAAE,EAAE,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGH,EAAE,CAACI,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;MACjD,IAAI,CAACG,CAAC,GAAGL,EAAE,CAACE,CAAC,CAAC,EAAEL,IAAI,KAAKE,QAAQ,CAACF,IAAI,IAAIQ,CAAC,CAACZ,IAAI,KAAKM,QAAQ,CAACN,IAAI,EAAE;QAClE,IAAI,CAACa,mBAAmB,CAACD,CAAC,CAACR,IAAI,EAAEQ,CAAC,CAACtB,QAAQ,EAAEsB,CAAC,CAACE,OAAO,CAAC;QACvD,IAAI,CAACG,gBAAgB,CAACL,CAAC,CAACR,IAAI,EAAEQ,CAAC,CAACtB,QAAQ,GAAGA,QAAQ,EAAEsB,CAAC,CAACE,OAAO,GAAGA,OAAO,CAAC;QACzEF,CAAC,CAACI,KAAK,GAAGA,KAAK;QACf;MACF;IACF;IACA,IAAI,CAACC,gBAAgB,CAACX,QAAQ,CAACF,IAAI,EAAEd,QAAQ,EAAEwB,OAAO,CAAC;IACvDF,CAAC,GAAG;MAACR,IAAI,EAAEE,QAAQ,CAACF,IAAI;MAAEJ,IAAI,EAAEM,QAAQ,CAACN,IAAI;MAAEgB,KAAK,EAAEA,KAAK;MAAE1B,QAAQ,EAAEA,QAAQ;MAAEwB,OAAO,EAAEA;IAAO,CAAC;IAClG,IAAI,CAACP,EAAE,EAAE,IAAI,CAACC,IAAI,GAAG,CAACI,CAAC,CAAC,CAAC,KACpBL,EAAE,CAACW,IAAI,CAACN,CAAC,CAAC;EACjB,CAAC;AACH;AAEA,eAAe,UAASN,QAAQ,EAAEU,KAAK,EAAEF,OAAO,EAAE;EAChD,IAAInB,SAAS,GAAGD,cAAc,CAACY,QAAQ,GAAG,EAAE,CAAC;IAAEL,CAAC;IAAEkB,CAAC,GAAGxB,SAAS,CAACgB,MAAM;IAAEZ,CAAC;EAEzE,IAAIqB,SAAS,CAACT,MAAM,GAAG,CAAC,EAAE;IACxB,IAAIJ,EAAE,GAAG,IAAI,CAACc,IAAI,CAAC,CAAC,CAACb,IAAI;IACzB,IAAID,EAAE,EAAE,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGH,EAAE,CAACI,MAAM,EAAEC,CAAC,EAAEH,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;MACpD,KAAKR,CAAC,GAAG,CAAC,EAAEW,CAAC,GAAGL,EAAE,CAACE,CAAC,CAAC,EAAER,CAAC,GAAGkB,CAAC,EAAE,EAAElB,CAAC,EAAE;QACjC,IAAI,CAACF,CAAC,GAAGJ,SAAS,CAACM,CAAC,CAAC,EAAEG,IAAI,KAAKQ,CAAC,CAACR,IAAI,IAAIL,CAAC,CAACC,IAAI,KAAKY,CAAC,CAACZ,IAAI,EAAE;UAC3D,OAAOY,CAAC,CAACI,KAAK;QAChB;MACF;IACF;IACA;EACF;EAEAT,EAAE,GAAGS,KAAK,GAAGD,KAAK,GAAGV,QAAQ;EAC7B,KAAKJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkB,CAAC,EAAE,EAAElB,CAAC,EAAE,IAAI,CAACqB,IAAI,CAACf,EAAE,CAACZ,SAAS,CAACM,CAAC,CAAC,EAAEe,KAAK,EAAEF,OAAO,CAAC,CAAC;EACnE,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}