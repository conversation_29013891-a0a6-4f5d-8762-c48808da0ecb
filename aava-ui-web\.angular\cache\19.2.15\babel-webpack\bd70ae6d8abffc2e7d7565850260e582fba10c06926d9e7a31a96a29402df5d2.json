{"ast": null, "code": "export { default as blob } from \"./blob.js\";\nexport { default as buffer } from \"./buffer.js\";\nexport { default as dsv, csv, tsv } from \"./dsv.js\";\nexport { default as image } from \"./image.js\";\nexport { default as json } from \"./json.js\";\nexport { default as text } from \"./text.js\";\nexport { default as xml, html, svg } from \"./xml.js\";", "map": {"version": 3, "names": ["default", "blob", "buffer", "dsv", "csv", "tsv", "image", "json", "text", "xml", "html", "svg"], "sources": ["C:/console/aava-ui-web/node_modules/d3-fetch/src/index.js"], "sourcesContent": ["export {default as blob} from \"./blob.js\";\nexport {default as buffer} from \"./buffer.js\";\nexport {default as dsv, csv, tsv} from \"./dsv.js\";\nexport {default as image} from \"./image.js\";\nexport {default as json} from \"./json.js\";\nexport {default as text} from \"./text.js\";\nexport {default as xml, html, svg} from \"./xml.js\";\n"], "mappings": "AAAA,SAAQA,OAAO,IAAIC,IAAI,QAAO,WAAW;AACzC,SAAQD,OAAO,IAAIE,MAAM,QAAO,aAAa;AAC7C,SAAQF,OAAO,IAAIG,GAAG,EAAEC,GAAG,EAAEC,GAAG,QAAO,UAAU;AACjD,SAAQL,OAAO,IAAIM,KAAK,QAAO,YAAY;AAC3C,SAAQN,OAAO,IAAIO,IAAI,QAAO,WAAW;AACzC,SAAQP,OAAO,IAAIQ,IAAI,QAAO,WAAW;AACzC,SAAQR,OAAO,IAAIS,GAAG,EAAEC,IAAI,EAAEC,GAAG,QAAO,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}