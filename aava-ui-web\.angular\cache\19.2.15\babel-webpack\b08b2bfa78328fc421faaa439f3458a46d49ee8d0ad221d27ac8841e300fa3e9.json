{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { CommonModule, formatDate } from '@angular/common';\nimport { Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { ApprovalCardComponent, IconComponent, AvaTextboxComponent, TextCardComponent, PopupComponent, ConfirmationPopupComponent, AvaTagComponent, ButtonComponent } from '@ava/play-comp-library';\nimport approvalText from '../constants/approval.json';\nimport { debounceTime, distinctUntilChanged, map, startWith } from 'rxjs';\nlet ApprovalWorkflowsComponent = class ApprovalWorkflowsComponent {\n  router;\n  approvalService;\n  fb;\n  appLabels = approvalText.labels;\n  totalApprovedApprovals = 20;\n  totalPendingApprovals = 15;\n  totalApprovals = 60;\n  isBasicCollapsed = false;\n  quickActionsExpanded = true;\n  consoleApproval = {};\n  options = [];\n  basicSidebarItems = [];\n  quickActions = [];\n  toolReviews = [];\n  workflowReviews = [];\n  filteredWorkflowReviews = [];\n  agentsReviews = [];\n  currentToolsPage = 1;\n  currentAgentsPage = 1;\n  currentWorkflowsPage = 1;\n  pageSize = 50;\n  totalRecords = 0;\n  isDeleted = false;\n  currentTab = 'Workflows';\n  showToolApprovalPopup = false;\n  showInfoPopup = false;\n  showErrorPopup = false;\n  infoMessage = '';\n  selectedIndex = 0;\n  showFeedbackPopup = false;\n  searchForm;\n  labels = approvalText.labels;\n  approvedAgentId = null;\n  constructor(router, approvalService, fb) {\n    this.router = router;\n    this.approvalService = approvalService;\n    this.fb = fb;\n    this.labels = approvalText.labels;\n    this.options = [{\n      name: this.labels.electronics,\n      value: 'electronics'\n    }, {\n      name: this.labels.clothing,\n      value: 'clothing'\n    }, {\n      name: this.labels.books,\n      value: 'books'\n    }];\n    this.basicSidebarItems = [{\n      id: '1',\n      icon: 'hammer',\n      text: this.labels.agents,\n      route: '',\n      active: true\n    }, {\n      id: '2',\n      icon: 'circle-check',\n      text: this.labels.workflows,\n      route: ''\n    }, {\n      id: '3',\n      icon: 'bot',\n      text: this.labels.tools,\n      route: ''\n    }];\n    this.quickActions = [{\n      icon: 'awe_agents',\n      label: this.labels.agents,\n      route: ''\n    }, {\n      icon: 'awe_workflows',\n      label: this.labels.workflows,\n      route: ''\n    }, {\n      icon: 'awe_tools',\n      label: this.labels.tools,\n      route: ''\n    }];\n    this.searchForm = this.fb.group({\n      search: ['']\n    });\n  }\n  ngOnInit() {\n    this.searchList();\n    this.totalApprovals = 60;\n    this.loadWorkflowReviews();\n  }\n  searchList() {\n    console.log(this.searchForm.get('search')?.value);\n    this.searchForm.get('search').valueChanges.pipe(startWith(''), debounceTime(300), distinctUntilChanged(), map(value => value?.toLowerCase() ?? '')).subscribe(searchText => {\n      this.applyFilter(searchText);\n    });\n  }\n  applyFilter(text) {\n    const lower = text;\n    if (!text) {\n      this.updateConsoleApproval(this.workflowReviews, 'workflow');\n      return;\n    }\n    this.filteredWorkflowReviews = this.workflowReviews.filter(item => item.workflowName?.toLowerCase().includes(lower));\n    this.updateConsoleApproval(this.filteredWorkflowReviews, 'workflow');\n  }\n  onSelectionChange(data) {\n    console.log('Selection changed:', data);\n  }\n  uClick(i) {\n    console.log('log' + i);\n  }\n  toggleQuickActions() {\n    this.quickActionsExpanded = !this.quickActionsExpanded;\n  }\n  onBasicCollapseToggle(isCollapsed) {\n    this.isBasicCollapsed = isCollapsed;\n    console.log('Basic sidebar collapsed:', isCollapsed);\n  }\n  onBasicItemClick(item) {\n    this.basicSidebarItems.forEach(i => i.active = false);\n    item.active = true;\n    console.log(item);\n  }\n  toRequestStatus(value) {\n    return value === 'approved' || value === 'rejected' || value === 'review' ? value : 'review';\n  }\n  loadWorkflowReviews() {\n    this.approvalService.getAllReviewWorkflows(this.currentWorkflowsPage, this.pageSize, this.isDeleted).subscribe(response => {\n      if (this.currentWorkflowsPage > 1) {\n        this.workflowReviews = [...this.workflowReviews, ...response.workflowReviewDetails];\n      } else {\n        this.workflowReviews = response?.workflowReviewDetails;\n      }\n      this.workflowReviews = this.workflowReviews.filter(r => r.status !== 'approved');\n      this.filteredWorkflowReviews = this.workflowReviews;\n      this.totalRecords = this.workflowReviews.length;\n      //   console.log('workflow reviews ', this.workflowReviews);\n      this.updateConsoleApproval(this.workflowReviews, 'workflow');\n    });\n  }\n  loadMoreWorkflows(page) {\n    this.currentWorkflowsPage = page;\n    this.loadWorkflowReviews();\n  }\n  loadReviews(name) {\n    this.currentTab = name;\n    this.loadWorkflowReviews();\n  }\n  rejectApproval(idx) {\n    console.log(idx);\n    this.selectedIndex = idx;\n    this.showFeedbackPopup = true;\n  }\n  approveApproval(idx) {\n    console.log(idx);\n    //   console.log(this.filteredWorkflowReviews);\n    this.selectedIndex = idx;\n    this.showToolApprovalPopup = true;\n  }\n  handleApproval() {\n    this.handleWorkflowApproval();\n  }\n  handleRejection(feedback) {\n    this.handleWorkflowRejection(feedback);\n  }\n  handleWorkflowApproval() {\n    const workflowDetails = this.filteredWorkflowReviews[this.selectedIndex];\n    const id = workflowDetails?.id;\n    const workflowId = workflowDetails?.workflowId;\n    const status = 'approved';\n    const reviewedBy = workflowDetails?.reviewedBy;\n    console.log(id, workflowId, status, reviewedBy);\n    this.approvalService.approveWorkflow(id, workflowId, status, reviewedBy).subscribe({\n      next: response => {\n        this.infoMessage = response?.message || this.labels.workflowSuccessApproveMessage;\n        this.showInfoPopup = true;\n      },\n      error: error => {\n        this.showErrorPopup = true;\n        this.infoMessage = error?.error?.message || this.labels.defaultErrorMessage;\n        console.error('Error:', error);\n      }\n    });\n  }\n  handleWorkflowRejection(feedback) {\n    const workflowDetails = this.workflowReviews[this.selectedIndex];\n    const id = workflowDetails?.id;\n    const workflowId = workflowDetails?.workflowId;\n    const status = 'rejected';\n    const reviewedBy = workflowDetails?.reviewedBy;\n    const message = feedback;\n    console.log(id, workflowId, status, reviewedBy, message);\n    this.approvalService.rejectWorkflow(id, workflowId, status, reviewedBy, message).subscribe({\n      next: response => {\n        this.infoMessage = response?.message || this.labels.workflowSuccessRejectMessage;\n        this.showInfoPopup = true;\n      },\n      error: error => {\n        this.showErrorPopup = true;\n        this.infoMessage = error?.error?.message || this.labels.defaultErrorMessage;\n        console.error('Error:', error);\n      }\n    });\n  }\n  handleTesting(index) {\n    console.log(index);\n    const workflowId = this.filteredWorkflowReviews[index].workflowId;\n    this.redirectToWorkflowPlayground(workflowId);\n  }\n  redirectToWorkflowPlayground(id) {\n    this.router.navigate(['/build/workflows/execute', id]);\n  }\n  handleInfoPopup() {\n    this.showInfoPopup = false;\n    this.loadWorkflowReviews();\n  }\n  updateConsoleApproval(data, type) {\n    this.consoleApproval = {\n      contents: data?.map(req => {\n        const statusIcons = {\n          approved: 'circle-check-big',\n          rejected: 'circle-x',\n          review: 'clock'\n        };\n        const statusTexts = {\n          approved: this.labels.approved,\n          rejected: this.labels.rejected,\n          review: this.labels.review\n        };\n        const statusKey = this.toRequestStatus(req?.status);\n        const specificId = req.workflowId;\n        const title = req.workflowName;\n        return {\n          id: req.id,\n          refId: specificId,\n          type: type,\n          session1: {\n            title: title,\n            labels: [{\n              name: type,\n              color: 'success',\n              background: 'red',\n              type: 'normal'\n            }, {\n              name: req.changeRequestType,\n              color: req.changeRequestType === 'update' ? 'error' : 'info',\n              background: 'red',\n              type: 'pill'\n            }]\n          },\n          session2: [{\n            name: type,\n            color: 'default',\n            background: 'red',\n            type: 'normal'\n          }, {\n            name: req.status,\n            color: 'default',\n            background: 'red',\n            type: 'normal'\n          }],\n          session3: [{\n            iconName: 'user',\n            label: req.requestedBy\n          }, {\n            iconName: 'calendar-days',\n            label: formatDate(req?.requestedAt, 'dd MMM yyyy', 'en-IN')\n          }],\n          session4: {\n            status: statusTexts[statusKey],\n            iconName: statusIcons[statusKey]\n          }\n        };\n      }),\n      footer: {}\n    };\n  }\n};\nApprovalWorkflowsComponent = __decorate([Component({\n  selector: 'app-approval-workflows',\n  imports: [CommonModule, RouterModule, ApprovalCardComponent, IconComponent, AvaTextboxComponent, TextCardComponent, ReactiveFormsModule, AvaTagComponent, ButtonComponent, PopupComponent, ConfirmationPopupComponent],\n  schemas: [CUSTOM_ELEMENTS_SCHEMA],\n  templateUrl: './approval-workflows.component.html',\n  styleUrls: ['./approval-workflows.component.scss']\n})], ApprovalWorkflowsComponent);\nexport { ApprovalWorkflowsComponent };", "map": {"version": 3, "names": ["CommonModule", "formatDate", "Component", "CUSTOM_ELEMENTS_SCHEMA", "ReactiveFormsModule", "RouterModule", "ApprovalCardComponent", "IconComponent", "AvaTextboxComponent", "TextCardComponent", "PopupComponent", "ConfirmationPopupComponent", "AvaTagComponent", "ButtonComponent", "approvalText", "debounceTime", "distinctUntilChanged", "map", "startWith", "ApprovalWorkflowsComponent", "router", "approvalService", "fb", "appLabels", "labels", "totalApprovedApprovals", "totalPendingApprovals", "totalApprovals", "isBasicCollapsed", "quickActionsExpanded", "consoleApproval", "options", "basicSidebarItems", "quickActions", "toolReviews", "workflowReviews", "filteredWorkflowReviews", "agentsReviews", "currentToolsPage", "currentAgentsPage", "currentWorkflowsPage", "pageSize", "totalRecords", "isDeleted", "currentTab", "showToolApprovalPopup", "showInfoPopup", "showErrorPopup", "infoMessage", "selectedIndex", "showFeedbackPopup", "searchForm", "approvedAgentId", "constructor", "name", "electronics", "value", "clothing", "books", "id", "icon", "text", "agents", "route", "active", "workflows", "tools", "label", "group", "search", "ngOnInit", "searchList", "loadWorkflowReviews", "console", "log", "get", "valueChanges", "pipe", "toLowerCase", "subscribe", "searchText", "applyFilter", "lower", "updateConsoleApproval", "filter", "item", "workflowName", "includes", "onSelectionChange", "data", "uClick", "i", "toggleQuickActions", "onBasicCollapseToggle", "isCollapsed", "onBasicItemClick", "for<PERSON>ach", "toRequestStatus", "getAllReviewWorkflows", "response", "workflowReviewDetails", "r", "status", "length", "loadMoreWorkflows", "page", "loadReviews", "rejectApproval", "idx", "approveApproval", "handleApproval", "handleWorkflowApproval", "handleRejection", "feedback", "handleWorkflowRejection", "workflowDetails", "workflowId", "reviewedBy", "approveWorkflow", "next", "message", "workflowSuccessApproveMessage", "error", "defaultErrorMessage", "rejectWorkflow", "workflowSuccessRejectMessage", "handleTesting", "index", "redirectToWorkflowPlayground", "navigate", "handleInfoPopup", "type", "contents", "req", "statusIcons", "approved", "rejected", "review", "statusTexts", "statusKey", "specificId", "title", "refId", "session1", "color", "background", "changeRequestType", "session2", "session3", "iconName", "requestedBy", "requestedAt", "session4", "footer", "__decorate", "selector", "imports", "schemas", "templateUrl", "styleUrls"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\approval\\approval-workflows\\approval-workflows.component.ts"], "sourcesContent": ["import { CommonModule, formatDate } from '@angular/common';\r\nimport { Component, CUSTOM_ELEMENTS_SCHEMA, OnInit } from '@angular/core';\r\nimport { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';\r\nimport { Router, RouterModule } from '@angular/router';\r\nimport {\r\n  ApprovalCardComponent,\r\n  IconComponent,\r\n  AvaTextboxComponent,\r\n  TextCardComponent,\r\n  PopupComponent,\r\n  ConfirmationPopupComponent,\r\n  AvaTagComponent,\r\n  DropdownOption,\r\n  ButtonComponent,\r\n} from '@ava/play-comp-library';\r\nimport approvalText from '../constants/approval.json';\r\nimport { ApprovalService } from '../../../shared/services/approval.service';\r\nimport { debounceTime, distinctUntilChanged, map, startWith } from 'rxjs';\r\n\r\ntype RequestStatus = 'approved' | 'rejected' | 'review';\r\n\r\n@Component({\r\n  selector: 'app-approval-workflows',\r\n  imports: [\r\n    CommonModule,\r\n    RouterModule,\r\n    ApprovalCardComponent,\r\n    IconComponent,\r\n    AvaTextboxComponent,\r\n    TextCardComponent,\r\n    ReactiveFormsModule,\r\n    AvaTagComponent,\r\n    ButtonComponent,\r\n    PopupComponent,\r\n    ConfirmationPopupComponent,\r\n  ],\r\n  schemas: [CUSTOM_ELEMENTS_SCHEMA],\r\n  templateUrl: './approval-workflows.component.html',\r\n  styleUrls: ['./approval-workflows.component.scss'],\r\n})\r\nexport class ApprovalWorkflowsComponent implements OnInit {\r\n  appLabels = approvalText.labels;\r\n  public totalApprovedApprovals: number = 20;\r\n  public totalPendingApprovals: number = 15;\r\n  public totalApprovals: number = 60;\r\n  public isBasicCollapsed: boolean = false;\r\n  public quickActionsExpanded: boolean = true;\r\n  public consoleApproval: any = {};\r\n  public options: DropdownOption[] = [];\r\n  public basicSidebarItems: any[] = [];\r\n  public quickActions: any[] = [];\r\n  public toolReviews: any[] = [];\r\n  public workflowReviews: any[] = [];\r\n  public filteredWorkflowReviews: any[] = [];\r\n  public agentsReviews: any[] = [];\r\n  public currentToolsPage = 1;\r\n  public currentAgentsPage = 1;\r\n  public currentWorkflowsPage = 1;\r\n  public pageSize = 50;\r\n  public totalRecords = 0;\r\n  public isDeleted = false;\r\n  public currentTab = 'Workflows';\r\n  public showToolApprovalPopup = false;\r\n  public showInfoPopup = false;\r\n  public showErrorPopup = false;\r\n  public infoMessage = '';\r\n  public selectedIndex = 0;\r\n  public showFeedbackPopup = false;\r\n  public searchForm!: FormGroup;\r\n  public labels: any = approvalText.labels;\r\n  public approvedAgentId: number | null = null;\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private approvalService: ApprovalService,\r\n    private fb: FormBuilder,\r\n  ) {\r\n    this.labels = approvalText.labels;\r\n    this.options = [\r\n      { name: this.labels.electronics, value: 'electronics' },\r\n      { name: this.labels.clothing, value: 'clothing' },\r\n      { name: this.labels.books, value: 'books' },\r\n    ];\r\n    this.basicSidebarItems = [\r\n      {\r\n        id: '1',\r\n        icon: 'hammer',\r\n        text: this.labels.agents,\r\n        route: '',\r\n        active: true,\r\n      },\r\n      { id: '2', icon: 'circle-check', text: this.labels.workflows, route: '' },\r\n      { id: '3', icon: 'bot', text: this.labels.tools, route: '' },\r\n    ];\r\n    this.quickActions = [\r\n      {\r\n        icon: 'awe_agents',\r\n        label: this.labels.agents,\r\n        route: '',\r\n      },\r\n      {\r\n        icon: 'awe_workflows',\r\n        label: this.labels.workflows,\r\n        route: '',\r\n      },\r\n      {\r\n        icon: 'awe_tools',\r\n        label: this.labels.tools,\r\n        route: '',\r\n      },\r\n    ];\r\n    this.searchForm = this.fb.group({\r\n      search: [''],\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.searchList();\r\n    this.totalApprovals = 60;\r\n    this.loadWorkflowReviews();\r\n  }\r\n\r\n  public searchList() {\r\n    console.log(this.searchForm.get('search')?.value);\r\n    this.searchForm\r\n      .get('search')!\r\n      .valueChanges.pipe(\r\n        startWith(''),\r\n        debounceTime(300),\r\n        distinctUntilChanged(),\r\n        map((value) => value?.toLowerCase() ?? ''),\r\n      )\r\n      .subscribe((searchText) => {\r\n        this.applyFilter(searchText);\r\n      });\r\n  }\r\n\r\n  public applyFilter(text: string) {\r\n    const lower = text;\r\n    if (!text) {\r\n      this.updateConsoleApproval(this.workflowReviews, 'workflow');\r\n      return;\r\n    }\r\n\r\n    this.filteredWorkflowReviews = this.workflowReviews.filter((item) =>\r\n      item.workflowName?.toLowerCase().includes(lower),\r\n    );\r\n    this.updateConsoleApproval(this.filteredWorkflowReviews, 'workflow');\r\n  }\r\n\r\n  public onSelectionChange(data: any) {\r\n    console.log('Selection changed:', data);\r\n  }\r\n\r\n  public uClick(i: any) {\r\n    console.log('log' + i);\r\n  }\r\n\r\n  public toggleQuickActions(): void {\r\n    this.quickActionsExpanded = !this.quickActionsExpanded;\r\n  }\r\n\r\n  public onBasicCollapseToggle(isCollapsed: boolean): void {\r\n    this.isBasicCollapsed = isCollapsed;\r\n    console.log('Basic sidebar collapsed:', isCollapsed);\r\n  }\r\n\r\n  public onBasicItemClick(item: any): void {\r\n    this.basicSidebarItems.forEach((i) => (i.active = false));\r\n    item.active = true;\r\n    console.log(item);\r\n  }\r\n\r\n  public toRequestStatus(value: string | null | undefined): RequestStatus {\r\n    return value === 'approved' || value === 'rejected' || value === 'review'\r\n      ? value\r\n      : 'review';\r\n  }\r\n\r\n  public loadWorkflowReviews() {\r\n    this.approvalService\r\n      .getAllReviewWorkflows(\r\n        this.currentWorkflowsPage,\r\n        this.pageSize,\r\n        this.isDeleted,\r\n      )\r\n      .subscribe((response) => {\r\n        if (this.currentWorkflowsPage > 1) {\r\n          this.workflowReviews = [\r\n            ...this.workflowReviews,\r\n            ...response.workflowReviewDetails,\r\n          ];\r\n        } else {\r\n          this.workflowReviews = response?.workflowReviewDetails;\r\n        }\r\n        this.workflowReviews = this.workflowReviews.filter(\r\n          (r) => r.status !== 'approved',\r\n        );\r\n        this.filteredWorkflowReviews = this.workflowReviews;\r\n        this.totalRecords = this.workflowReviews.length;\r\n        //   console.log('workflow reviews ', this.workflowReviews);\r\n        this.updateConsoleApproval(this.workflowReviews, 'workflow');\r\n      });\r\n  }\r\n\r\n  public loadMoreWorkflows(page: number) {\r\n    this.currentWorkflowsPage = page;\r\n    this.loadWorkflowReviews();\r\n  }\r\n\r\n  public loadReviews(name: string) {\r\n    this.currentTab = name;\r\n    this.loadWorkflowReviews();\r\n  }\r\n\r\n  public rejectApproval(idx: any) {\r\n    console.log(idx);\r\n    this.selectedIndex = idx;\r\n    this.showFeedbackPopup = true;\r\n  }\r\n\r\n  public approveApproval(idx: any) {\r\n    console.log(idx);\r\n    //   console.log(this.filteredWorkflowReviews);\r\n    this.selectedIndex = idx;\r\n    this.showToolApprovalPopup = true;\r\n  }\r\n\r\n  public handleApproval() {\r\n    this.handleWorkflowApproval();\r\n  }\r\n\r\n  public handleRejection(feedback: any) {\r\n    this.handleWorkflowRejection(feedback);\r\n  }\r\n\r\n  public handleWorkflowApproval() {\r\n    const workflowDetails = this.filteredWorkflowReviews[this.selectedIndex];\r\n    const id = workflowDetails?.id;\r\n    const workflowId = workflowDetails?.workflowId;\r\n    const status = 'approved';\r\n    const reviewedBy = workflowDetails?.reviewedBy;\r\n    console.log(id, workflowId, status, reviewedBy);\r\n    this.approvalService\r\n      .approveWorkflow(id, workflowId, status, reviewedBy)\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.infoMessage =\r\n            response?.message || this.labels.workflowSuccessApproveMessage;\r\n          this.showInfoPopup = true;\r\n        },\r\n        error: (error) => {\r\n          this.showErrorPopup = true;\r\n          this.infoMessage =\r\n            error?.error?.message || this.labels.defaultErrorMessage;\r\n          console.error('Error:', error);\r\n        },\r\n      });\r\n  }\r\n\r\n  public handleWorkflowRejection(feedback: any) {\r\n    const workflowDetails = this.workflowReviews[this.selectedIndex];\r\n    const id = workflowDetails?.id;\r\n    const workflowId = workflowDetails?.workflowId;\r\n    const status = 'rejected';\r\n    const reviewedBy = workflowDetails?.reviewedBy;\r\n    const message = feedback;\r\n    console.log(id, workflowId, status, reviewedBy, message);\r\n    this.approvalService\r\n      .rejectWorkflow(id, workflowId, status, reviewedBy, message)\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.infoMessage =\r\n            response?.message || this.labels.workflowSuccessRejectMessage;\r\n          this.showInfoPopup = true;\r\n        },\r\n        error: (error) => {\r\n          this.showErrorPopup = true;\r\n          this.infoMessage =\r\n            error?.error?.message || this.labels.defaultErrorMessage;\r\n          console.error('Error:', error);\r\n        },\r\n      });\r\n  }\r\n\r\n  public handleTesting(index : any){\r\n    console.log(index);\r\n    const workflowId = this.filteredWorkflowReviews[index].workflowId;\r\n    this.redirectToWorkflowPlayground(workflowId);\r\n  }\r\n\r\n  public redirectToWorkflowPlayground(id: number): void {\r\n    this.router.navigate(['/build/workflows/execute', id]);\r\n  }\r\n\r\n  public handleInfoPopup() {\r\n    this.showInfoPopup = false;\r\n    this.loadWorkflowReviews();\r\n  }\r\n\r\n  public updateConsoleApproval(data: any[], type: string) {\r\n    this.consoleApproval = {\r\n      contents: data?.map((req: any) => {\r\n        const statusIcons: Record<RequestStatus, string> = {\r\n          approved: 'circle-check-big',\r\n          rejected: 'circle-x',\r\n          review: 'clock',\r\n        };\r\n        const statusTexts: Record<RequestStatus, string> = {\r\n          approved: this.labels.approved,\r\n          rejected: this.labels.rejected,\r\n          review: this.labels.review,\r\n        };\r\n        const statusKey = this.toRequestStatus(req?.status);\r\n        const specificId = req.workflowId;\r\n        const title = req.workflowName;\r\n\r\n        return {\r\n          id: req.id,\r\n          refId: specificId,\r\n          type: type,\r\n          session1: {\r\n            title: title,\r\n            labels: [\r\n              {\r\n                name: type,\r\n                color: 'success',\r\n                background: 'red',\r\n                type: 'normal',\r\n              },\r\n              {\r\n                name: req.changeRequestType,\r\n                color: req.changeRequestType === 'update' ? 'error' : 'info',\r\n                background: 'red',\r\n                type: 'pill',\r\n              },\r\n            ],\r\n          },\r\n          session2: [\r\n            {\r\n              name: type,\r\n              color: 'default',\r\n              background: 'red',\r\n              type: 'normal',\r\n            },\r\n            {\r\n              name: req.status,\r\n              color: 'default',\r\n              background: 'red',\r\n              type: 'normal',\r\n            },\r\n          ],\r\n          session3: [\r\n            {\r\n              iconName: 'user',\r\n              label: req.requestedBy,\r\n            },\r\n            {\r\n              iconName: 'calendar-days',\r\n              label: formatDate(req?.requestedAt, 'dd MMM yyyy', 'en-IN'),\r\n            },\r\n          ],\r\n          session4: {\r\n            status: statusTexts[statusKey],\r\n            iconName: statusIcons[statusKey],\r\n          },\r\n        };\r\n      }),\r\n      footer: {},\r\n    };\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,YAAY,EAAEC,UAAU,QAAQ,iBAAiB;AAC1D,SAASC,SAAS,EAAEC,sBAAsB,QAAgB,eAAe;AACzE,SAAiCC,mBAAmB,QAAQ,gBAAgB;AAC5E,SAAiBC,YAAY,QAAQ,iBAAiB;AACtD,SACEC,qBAAqB,EACrBC,aAAa,EACbC,mBAAmB,EACnBC,iBAAiB,EACjBC,cAAc,EACdC,0BAA0B,EAC1BC,eAAe,EAEfC,eAAe,QACV,wBAAwB;AAC/B,OAAOC,YAAY,MAAM,4BAA4B;AAErD,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,GAAG,EAAEC,SAAS,QAAQ,MAAM;AAuBlE,IAAMC,0BAA0B,GAAhC,MAAMA,0BAA0B;EAiC3BC,MAAA;EACAC,eAAA;EACAC,EAAA;EAlCVC,SAAS,GAAGT,YAAY,CAACU,MAAM;EACxBC,sBAAsB,GAAW,EAAE;EACnCC,qBAAqB,GAAW,EAAE;EAClCC,cAAc,GAAW,EAAE;EAC3BC,gBAAgB,GAAY,KAAK;EACjCC,oBAAoB,GAAY,IAAI;EACpCC,eAAe,GAAQ,EAAE;EACzBC,OAAO,GAAqB,EAAE;EAC9BC,iBAAiB,GAAU,EAAE;EAC7BC,YAAY,GAAU,EAAE;EACxBC,WAAW,GAAU,EAAE;EACvBC,eAAe,GAAU,EAAE;EAC3BC,uBAAuB,GAAU,EAAE;EACnCC,aAAa,GAAU,EAAE;EACzBC,gBAAgB,GAAG,CAAC;EACpBC,iBAAiB,GAAG,CAAC;EACrBC,oBAAoB,GAAG,CAAC;EACxBC,QAAQ,GAAG,EAAE;EACbC,YAAY,GAAG,CAAC;EAChBC,SAAS,GAAG,KAAK;EACjBC,UAAU,GAAG,WAAW;EACxBC,qBAAqB,GAAG,KAAK;EAC7BC,aAAa,GAAG,KAAK;EACrBC,cAAc,GAAG,KAAK;EACtBC,WAAW,GAAG,EAAE;EAChBC,aAAa,GAAG,CAAC;EACjBC,iBAAiB,GAAG,KAAK;EACzBC,UAAU;EACV3B,MAAM,GAAQV,YAAY,CAACU,MAAM;EACjC4B,eAAe,GAAkB,IAAI;EAE5CC,YACUjC,MAAc,EACdC,eAAgC,EAChCC,EAAe;IAFf,KAAAF,MAAM,GAANA,MAAM;IACN,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,EAAE,GAAFA,EAAE;IAEV,IAAI,CAACE,MAAM,GAAGV,YAAY,CAACU,MAAM;IACjC,IAAI,CAACO,OAAO,GAAG,CACb;MAAEuB,IAAI,EAAE,IAAI,CAAC9B,MAAM,CAAC+B,WAAW;MAAEC,KAAK,EAAE;IAAa,CAAE,EACvD;MAAEF,IAAI,EAAE,IAAI,CAAC9B,MAAM,CAACiC,QAAQ;MAAED,KAAK,EAAE;IAAU,CAAE,EACjD;MAAEF,IAAI,EAAE,IAAI,CAAC9B,MAAM,CAACkC,KAAK;MAAEF,KAAK,EAAE;IAAO,CAAE,CAC5C;IACD,IAAI,CAACxB,iBAAiB,GAAG,CACvB;MACE2B,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,IAAI,CAACrC,MAAM,CAACsC,MAAM;MACxBC,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE;KACT,EACD;MAAEL,EAAE,EAAE,GAAG;MAAEC,IAAI,EAAE,cAAc;MAAEC,IAAI,EAAE,IAAI,CAACrC,MAAM,CAACyC,SAAS;MAAEF,KAAK,EAAE;IAAE,CAAE,EACzE;MAAEJ,EAAE,EAAE,GAAG;MAAEC,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE,IAAI,CAACrC,MAAM,CAAC0C,KAAK;MAAEH,KAAK,EAAE;IAAE,CAAE,CAC7D;IACD,IAAI,CAAC9B,YAAY,GAAG,CAClB;MACE2B,IAAI,EAAE,YAAY;MAClBO,KAAK,EAAE,IAAI,CAAC3C,MAAM,CAACsC,MAAM;MACzBC,KAAK,EAAE;KACR,EACD;MACEH,IAAI,EAAE,eAAe;MACrBO,KAAK,EAAE,IAAI,CAAC3C,MAAM,CAACyC,SAAS;MAC5BF,KAAK,EAAE;KACR,EACD;MACEH,IAAI,EAAE,WAAW;MACjBO,KAAK,EAAE,IAAI,CAAC3C,MAAM,CAAC0C,KAAK;MACxBH,KAAK,EAAE;KACR,CACF;IACD,IAAI,CAACZ,UAAU,GAAG,IAAI,CAAC7B,EAAE,CAAC8C,KAAK,CAAC;MAC9BC,MAAM,EAAE,CAAC,EAAE;KACZ,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,UAAU,EAAE;IACjB,IAAI,CAAC5C,cAAc,GAAG,EAAE;IACxB,IAAI,CAAC6C,mBAAmB,EAAE;EAC5B;EAEOD,UAAUA,CAAA;IACfE,OAAO,CAACC,GAAG,CAAC,IAAI,CAACvB,UAAU,CAACwB,GAAG,CAAC,QAAQ,CAAC,EAAEnB,KAAK,CAAC;IACjD,IAAI,CAACL,UAAU,CACZwB,GAAG,CAAC,QAAQ,CAAE,CACdC,YAAY,CAACC,IAAI,CAChB3D,SAAS,CAAC,EAAE,CAAC,EACbH,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,GAAG,CAAEuC,KAAK,IAAKA,KAAK,EAAEsB,WAAW,EAAE,IAAI,EAAE,CAAC,CAC3C,CACAC,SAAS,CAAEC,UAAU,IAAI;MACxB,IAAI,CAACC,WAAW,CAACD,UAAU,CAAC;IAC9B,CAAC,CAAC;EACN;EAEOC,WAAWA,CAACpB,IAAY;IAC7B,MAAMqB,KAAK,GAAGrB,IAAI;IAClB,IAAI,CAACA,IAAI,EAAE;MACT,IAAI,CAACsB,qBAAqB,CAAC,IAAI,CAAChD,eAAe,EAAE,UAAU,CAAC;MAC5D;IACF;IAEA,IAAI,CAACC,uBAAuB,GAAG,IAAI,CAACD,eAAe,CAACiD,MAAM,CAAEC,IAAI,IAC9DA,IAAI,CAACC,YAAY,EAAER,WAAW,EAAE,CAACS,QAAQ,CAACL,KAAK,CAAC,CACjD;IACD,IAAI,CAACC,qBAAqB,CAAC,IAAI,CAAC/C,uBAAuB,EAAE,UAAU,CAAC;EACtE;EAEOoD,iBAAiBA,CAACC,IAAS;IAChChB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEe,IAAI,CAAC;EACzC;EAEOC,MAAMA,CAACC,CAAM;IAClBlB,OAAO,CAACC,GAAG,CAAC,KAAK,GAAGiB,CAAC,CAAC;EACxB;EAEOC,kBAAkBA,CAAA;IACvB,IAAI,CAAC/D,oBAAoB,GAAG,CAAC,IAAI,CAACA,oBAAoB;EACxD;EAEOgE,qBAAqBA,CAACC,WAAoB;IAC/C,IAAI,CAAClE,gBAAgB,GAAGkE,WAAW;IACnCrB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEoB,WAAW,CAAC;EACtD;EAEOC,gBAAgBA,CAACV,IAAS;IAC/B,IAAI,CAACrD,iBAAiB,CAACgE,OAAO,CAAEL,CAAC,IAAMA,CAAC,CAAC3B,MAAM,GAAG,KAAM,CAAC;IACzDqB,IAAI,CAACrB,MAAM,GAAG,IAAI;IAClBS,OAAO,CAACC,GAAG,CAACW,IAAI,CAAC;EACnB;EAEOY,eAAeA,CAACzC,KAAgC;IACrD,OAAOA,KAAK,KAAK,UAAU,IAAIA,KAAK,KAAK,UAAU,IAAIA,KAAK,KAAK,QAAQ,GACrEA,KAAK,GACL,QAAQ;EACd;EAEOgB,mBAAmBA,CAAA;IACxB,IAAI,CAACnD,eAAe,CACjB6E,qBAAqB,CACpB,IAAI,CAAC1D,oBAAoB,EACzB,IAAI,CAACC,QAAQ,EACb,IAAI,CAACE,SAAS,CACf,CACAoC,SAAS,CAAEoB,QAAQ,IAAI;MACtB,IAAI,IAAI,CAAC3D,oBAAoB,GAAG,CAAC,EAAE;QACjC,IAAI,CAACL,eAAe,GAAG,CACrB,GAAG,IAAI,CAACA,eAAe,EACvB,GAAGgE,QAAQ,CAACC,qBAAqB,CAClC;MACH,CAAC,MAAM;QACL,IAAI,CAACjE,eAAe,GAAGgE,QAAQ,EAAEC,qBAAqB;MACxD;MACA,IAAI,CAACjE,eAAe,GAAG,IAAI,CAACA,eAAe,CAACiD,MAAM,CAC/CiB,CAAC,IAAKA,CAAC,CAACC,MAAM,KAAK,UAAU,CAC/B;MACD,IAAI,CAAClE,uBAAuB,GAAG,IAAI,CAACD,eAAe;MACnD,IAAI,CAACO,YAAY,GAAG,IAAI,CAACP,eAAe,CAACoE,MAAM;MAC/C;MACA,IAAI,CAACpB,qBAAqB,CAAC,IAAI,CAAChD,eAAe,EAAE,UAAU,CAAC;IAC9D,CAAC,CAAC;EACN;EAEOqE,iBAAiBA,CAACC,IAAY;IACnC,IAAI,CAACjE,oBAAoB,GAAGiE,IAAI;IAChC,IAAI,CAACjC,mBAAmB,EAAE;EAC5B;EAEOkC,WAAWA,CAACpD,IAAY;IAC7B,IAAI,CAACV,UAAU,GAAGU,IAAI;IACtB,IAAI,CAACkB,mBAAmB,EAAE;EAC5B;EAEOmC,cAAcA,CAACC,GAAQ;IAC5BnC,OAAO,CAACC,GAAG,CAACkC,GAAG,CAAC;IAChB,IAAI,CAAC3D,aAAa,GAAG2D,GAAG;IACxB,IAAI,CAAC1D,iBAAiB,GAAG,IAAI;EAC/B;EAEO2D,eAAeA,CAACD,GAAQ;IAC7BnC,OAAO,CAACC,GAAG,CAACkC,GAAG,CAAC;IAChB;IACA,IAAI,CAAC3D,aAAa,GAAG2D,GAAG;IACxB,IAAI,CAAC/D,qBAAqB,GAAG,IAAI;EACnC;EAEOiE,cAAcA,CAAA;IACnB,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEOC,eAAeA,CAACC,QAAa;IAClC,IAAI,CAACC,uBAAuB,CAACD,QAAQ,CAAC;EACxC;EAEOF,sBAAsBA,CAAA;IAC3B,MAAMI,eAAe,GAAG,IAAI,CAAC/E,uBAAuB,CAAC,IAAI,CAACa,aAAa,CAAC;IACxE,MAAMU,EAAE,GAAGwD,eAAe,EAAExD,EAAE;IAC9B,MAAMyD,UAAU,GAAGD,eAAe,EAAEC,UAAU;IAC9C,MAAMd,MAAM,GAAG,UAAU;IACzB,MAAMe,UAAU,GAAGF,eAAe,EAAEE,UAAU;IAC9C5C,OAAO,CAACC,GAAG,CAACf,EAAE,EAAEyD,UAAU,EAAEd,MAAM,EAAEe,UAAU,CAAC;IAC/C,IAAI,CAAChG,eAAe,CACjBiG,eAAe,CAAC3D,EAAE,EAAEyD,UAAU,EAAEd,MAAM,EAAEe,UAAU,CAAC,CACnDtC,SAAS,CAAC;MACTwC,IAAI,EAAGpB,QAAa,IAAI;QACtB,IAAI,CAACnD,WAAW,GACdmD,QAAQ,EAAEqB,OAAO,IAAI,IAAI,CAAChG,MAAM,CAACiG,6BAA6B;QAChE,IAAI,CAAC3E,aAAa,GAAG,IAAI;MAC3B,CAAC;MACD4E,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC3E,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACC,WAAW,GACd0E,KAAK,EAAEA,KAAK,EAAEF,OAAO,IAAI,IAAI,CAAChG,MAAM,CAACmG,mBAAmB;QAC1DlD,OAAO,CAACiD,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;MAChC;KACD,CAAC;EACN;EAEOR,uBAAuBA,CAACD,QAAa;IAC1C,MAAME,eAAe,GAAG,IAAI,CAAChF,eAAe,CAAC,IAAI,CAACc,aAAa,CAAC;IAChE,MAAMU,EAAE,GAAGwD,eAAe,EAAExD,EAAE;IAC9B,MAAMyD,UAAU,GAAGD,eAAe,EAAEC,UAAU;IAC9C,MAAMd,MAAM,GAAG,UAAU;IACzB,MAAMe,UAAU,GAAGF,eAAe,EAAEE,UAAU;IAC9C,MAAMG,OAAO,GAAGP,QAAQ;IACxBxC,OAAO,CAACC,GAAG,CAACf,EAAE,EAAEyD,UAAU,EAAEd,MAAM,EAAEe,UAAU,EAAEG,OAAO,CAAC;IACxD,IAAI,CAACnG,eAAe,CACjBuG,cAAc,CAACjE,EAAE,EAAEyD,UAAU,EAAEd,MAAM,EAAEe,UAAU,EAAEG,OAAO,CAAC,CAC3DzC,SAAS,CAAC;MACTwC,IAAI,EAAGpB,QAAa,IAAI;QACtB,IAAI,CAACnD,WAAW,GACdmD,QAAQ,EAAEqB,OAAO,IAAI,IAAI,CAAChG,MAAM,CAACqG,4BAA4B;QAC/D,IAAI,CAAC/E,aAAa,GAAG,IAAI;MAC3B,CAAC;MACD4E,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC3E,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACC,WAAW,GACd0E,KAAK,EAAEA,KAAK,EAAEF,OAAO,IAAI,IAAI,CAAChG,MAAM,CAACmG,mBAAmB;QAC1DlD,OAAO,CAACiD,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;MAChC;KACD,CAAC;EACN;EAEOI,aAAaA,CAACC,KAAW;IAC9BtD,OAAO,CAACC,GAAG,CAACqD,KAAK,CAAC;IAClB,MAAMX,UAAU,GAAG,IAAI,CAAChF,uBAAuB,CAAC2F,KAAK,CAAC,CAACX,UAAU;IACjE,IAAI,CAACY,4BAA4B,CAACZ,UAAU,CAAC;EAC/C;EAEOY,4BAA4BA,CAACrE,EAAU;IAC5C,IAAI,CAACvC,MAAM,CAAC6G,QAAQ,CAAC,CAAC,0BAA0B,EAAEtE,EAAE,CAAC,CAAC;EACxD;EAEOuE,eAAeA,CAAA;IACpB,IAAI,CAACpF,aAAa,GAAG,KAAK;IAC1B,IAAI,CAAC0B,mBAAmB,EAAE;EAC5B;EAEOW,qBAAqBA,CAACM,IAAW,EAAE0C,IAAY;IACpD,IAAI,CAACrG,eAAe,GAAG;MACrBsG,QAAQ,EAAE3C,IAAI,EAAExE,GAAG,CAAEoH,GAAQ,IAAI;QAC/B,MAAMC,WAAW,GAAkC;UACjDC,QAAQ,EAAE,kBAAkB;UAC5BC,QAAQ,EAAE,UAAU;UACpBC,MAAM,EAAE;SACT;QACD,MAAMC,WAAW,GAAkC;UACjDH,QAAQ,EAAE,IAAI,CAAC/G,MAAM,CAAC+G,QAAQ;UAC9BC,QAAQ,EAAE,IAAI,CAAChH,MAAM,CAACgH,QAAQ;UAC9BC,MAAM,EAAE,IAAI,CAACjH,MAAM,CAACiH;SACrB;QACD,MAAME,SAAS,GAAG,IAAI,CAAC1C,eAAe,CAACoC,GAAG,EAAE/B,MAAM,CAAC;QACnD,MAAMsC,UAAU,GAAGP,GAAG,CAACjB,UAAU;QACjC,MAAMyB,KAAK,GAAGR,GAAG,CAAC/C,YAAY;QAE9B,OAAO;UACL3B,EAAE,EAAE0E,GAAG,CAAC1E,EAAE;UACVmF,KAAK,EAAEF,UAAU;UACjBT,IAAI,EAAEA,IAAI;UACVY,QAAQ,EAAE;YACRF,KAAK,EAAEA,KAAK;YACZrH,MAAM,EAAE,CACN;cACE8B,IAAI,EAAE6E,IAAI;cACVa,KAAK,EAAE,SAAS;cAChBC,UAAU,EAAE,KAAK;cACjBd,IAAI,EAAE;aACP,EACD;cACE7E,IAAI,EAAE+E,GAAG,CAACa,iBAAiB;cAC3BF,KAAK,EAAEX,GAAG,CAACa,iBAAiB,KAAK,QAAQ,GAAG,OAAO,GAAG,MAAM;cAC5DD,UAAU,EAAE,KAAK;cACjBd,IAAI,EAAE;aACP;WAEJ;UACDgB,QAAQ,EAAE,CACR;YACE7F,IAAI,EAAE6E,IAAI;YACVa,KAAK,EAAE,SAAS;YAChBC,UAAU,EAAE,KAAK;YACjBd,IAAI,EAAE;WACP,EACD;YACE7E,IAAI,EAAE+E,GAAG,CAAC/B,MAAM;YAChB0C,KAAK,EAAE,SAAS;YAChBC,UAAU,EAAE,KAAK;YACjBd,IAAI,EAAE;WACP,CACF;UACDiB,QAAQ,EAAE,CACR;YACEC,QAAQ,EAAE,MAAM;YAChBlF,KAAK,EAAEkE,GAAG,CAACiB;WACZ,EACD;YACED,QAAQ,EAAE,eAAe;YACzBlF,KAAK,EAAElE,UAAU,CAACoI,GAAG,EAAEkB,WAAW,EAAE,aAAa,EAAE,OAAO;WAC3D,CACF;UACDC,QAAQ,EAAE;YACRlD,MAAM,EAAEoC,WAAW,CAACC,SAAS,CAAC;YAC9BU,QAAQ,EAAEf,WAAW,CAACK,SAAS;;SAElC;MACH,CAAC,CAAC;MACFc,MAAM,EAAE;KACT;EACH;CACD;AA3UYtI,0BAA0B,GAAAuI,UAAA,EAnBtCxJ,SAAS,CAAC;EACTyJ,QAAQ,EAAE,wBAAwB;EAClCC,OAAO,EAAE,CACP5J,YAAY,EACZK,YAAY,EACZC,qBAAqB,EACrBC,aAAa,EACbC,mBAAmB,EACnBC,iBAAiB,EACjBL,mBAAmB,EACnBQ,eAAe,EACfC,eAAe,EACfH,cAAc,EACdC,0BAA0B,CAC3B;EACDkJ,OAAO,EAAE,CAAC1J,sBAAsB,CAAC;EACjC2J,WAAW,EAAE,qCAAqC;EAClDC,SAAS,EAAE,CAAC,qCAAqC;CAClD,CAAC,C,EACW5I,0BAA0B,CA2UtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}