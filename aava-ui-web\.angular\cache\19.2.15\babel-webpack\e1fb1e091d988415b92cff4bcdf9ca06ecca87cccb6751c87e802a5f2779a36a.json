{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let DrawerService = /*#__PURE__*/(() => {\n  class DrawerService {\n    containerRef;\n    registerViewContainer(container) {\n      this.containerRef = container;\n    }\n    open(component, data) {\n      if (!this.containerRef) {\n        return null;\n      }\n      const componentRef = this.containerRef.createComponent(component);\n      if (data) {\n        Object.assign(componentRef.instance, data);\n      }\n      return componentRef;\n    }\n    clear() {\n      this.containerRef?.clear();\n    }\n    static ɵfac = function DrawerService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DrawerService)();\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: DrawerService,\n      factory: DrawerService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return DrawerService;\n})();", "map": {"version": 3, "names": ["DrawerService", "containerRef", "registerViewContainer", "container", "open", "component", "data", "componentRef", "createComponent", "Object", "assign", "instance", "clear", "factory", "ɵfac", "providedIn"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\services\\drawer\\drawer.service.ts"], "sourcesContent": ["import { ComponentRef, Injectable, Type, ViewContainerRef } from '@angular/core';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class DrawerService {\r\n private containerRef!: ViewContainerRef;\r\n\r\n  registerViewContainer(container: ViewContainerRef) {\r\n    this.containerRef = container;\r\n  }\r\n\r\n  open<T extends object>(component: Type<T>, data?: Partial<T>): ComponentRef<T> | null {\r\n    if (!this.containerRef) {\r\n      return null;\r\n    }\r\n\r\n    const componentRef = this.containerRef.createComponent<T>(component);\r\n\r\n    if (data) {\r\n      Object.assign(componentRef.instance, data);\r\n    }\r\n\r\n    return componentRef;\r\n  }\r\n\r\n  clear() {\r\n    this.containerRef?.clear();\r\n  }\r\n}\r\n"], "mappings": ";AAKA,WAAaA,aAAa;EAApB,MAAOA,aAAa;IACjBC,YAAY;IAEnBC,qBAAqBA,CAACC,SAA2B;MAC/C,IAAI,CAACF,YAAY,GAAGE,SAAS;IAC/B;IAEAC,IAAIA,CAAmBC,SAAkB,EAAEC,IAAiB;MAC1D,IAAI,CAAC,IAAI,CAACL,YAAY,EAAE;QACtB,OAAO,IAAI;MACb;MAEA,MAAMM,YAAY,GAAG,IAAI,CAACN,YAAY,CAACO,eAAe,CAAIH,SAAS,CAAC;MAEpE,IAAIC,IAAI,EAAE;QACRG,MAAM,CAACC,MAAM,CAACH,YAAY,CAACI,QAAQ,EAAEL,IAAI,CAAC;MAC5C;MAEA,OAAOC,YAAY;IACrB;IAEAK,KAAKA,CAAA;MACH,IAAI,CAACX,YAAY,EAAEW,KAAK,EAAE;IAC5B;;uCAvBWZ,aAAa;IAAA;;aAAbA,aAAa;MAAAa,OAAA,EAAbb,aAAa,CAAAc,IAAA;MAAAC,UAAA,EAFZ;IAAM;;SAEPf,aAAa;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}