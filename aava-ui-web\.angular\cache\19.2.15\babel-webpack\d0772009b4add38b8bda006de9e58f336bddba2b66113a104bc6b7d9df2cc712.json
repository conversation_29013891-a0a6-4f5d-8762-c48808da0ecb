{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nconst _c0 = [\"nodeTemplate\"];\nfunction DropZoneCanvasComponent_div_36_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtext(1, \" Drop a prompt here \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DropZoneCanvasComponent_div_36_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"span\", 55);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function DropZoneCanvasComponent_div_36_div_3_Template_button_click_3_listener() {\n      const node_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onDeleteNode(node_r2.id));\n    });\n    i0.ɵɵtext(4, \" \\u00D7 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((node_r2.data == null ? null : node_r2.data.name) || \"Prompt\");\n  }\n}\nfunction DropZoneCanvasComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵtemplate(1, DropZoneCanvasComponent_div_36_div_1_Template, 2, 0, \"div\", 50);\n    i0.ɵɵelementStart(2, \"div\", 51);\n    i0.ɵɵtemplate(3, DropZoneCanvasComponent_div_36_div_3_Template, 5, 1, \"div\", 52);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.promptNodes.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.promptNodes)(\"ngForTrackBy\", ctx_r2.trackByNodeId);\n  }\n}\nfunction DropZoneCanvasComponent_div_57_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtext(1, \" Drop knowledge here \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DropZoneCanvasComponent_div_57_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"span\", 55);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function DropZoneCanvasComponent_div_57_div_3_Template_button_click_3_listener() {\n      const node_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onDeleteNode(node_r5.id));\n    });\n    i0.ɵɵtext(4, \" \\u00D7 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((node_r5.data == null ? null : node_r5.data.name) || \"Knowledge\");\n  }\n}\nfunction DropZoneCanvasComponent_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵtemplate(1, DropZoneCanvasComponent_div_57_div_1_Template, 2, 0, \"div\", 50);\n    i0.ɵɵelementStart(2, \"div\", 51);\n    i0.ɵɵtemplate(3, DropZoneCanvasComponent_div_57_div_3_Template, 5, 1, \"div\", 52);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.knowledgeNodes.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.knowledgeNodes)(\"ngForTrackBy\", ctx_r2.trackByNodeId);\n  }\n}\nfunction DropZoneCanvasComponent_div_76_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtext(1, \" Drop a model here \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DropZoneCanvasComponent_div_76_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"span\", 55);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function DropZoneCanvasComponent_div_76_div_3_Template_button_click_3_listener() {\n      const node_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onDeleteNode(node_r7.id));\n    });\n    i0.ɵɵtext(4, \" \\u00D7 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((node_r7.data == null ? null : node_r7.data.name) || \"Model\");\n  }\n}\nfunction DropZoneCanvasComponent_div_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵtemplate(1, DropZoneCanvasComponent_div_76_div_1_Template, 2, 0, \"div\", 50);\n    i0.ɵɵelementStart(2, \"div\", 51);\n    i0.ɵɵtemplate(3, DropZoneCanvasComponent_div_76_div_3_Template, 5, 1, \"div\", 52);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.modelNodes.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.modelNodes)(\"ngForTrackBy\", ctx_r2.trackByNodeId);\n  }\n}\nfunction DropZoneCanvasComponent__svg_svg_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 18);\n    i0.ɵɵelement(1, \"rect\", 57)(2, \"path\", 58);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DropZoneCanvasComponent__svg_svg_82_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 18);\n    i0.ɵɵelement(1, \"rect\", 59)(2, \"path\", 60);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DropZoneCanvasComponent_div_91_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" Drop \", ctx_r2.config.agentType === \"individual\" ? \"guardrails\" : \"tools\", \" here \");\n  }\n}\nfunction DropZoneCanvasComponent_div_91_div_3_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"label\", 66)(2, \"input\", 67);\n    i0.ɵɵlistener(\"change\", function DropZoneCanvasComponent_div_91_div_3_div_4_Template_input_change_2_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const node_r9 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onGuardrailCheckboxChange($event, node_r9.id));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"span\", 68);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r9 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", ctx_r2.isGuardrailEnabled(node_r9.id));\n  }\n}\nfunction DropZoneCanvasComponent_div_91_div_3_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function DropZoneCanvasComponent_div_91_div_3_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const node_r9 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onDeleteNode(node_r9.id));\n    });\n    i0.ɵɵtext(1, \" \\u00D7 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DropZoneCanvasComponent_div_91_div_3_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function DropZoneCanvasComponent_div_91_div_3_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const node_r9 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onDeleteNode(node_r9.id));\n    });\n    i0.ɵɵtext(1, \" \\u00D7 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DropZoneCanvasComponent_div_91_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 62)(2, \"span\", 55);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, DropZoneCanvasComponent_div_91_div_3_div_4_Template, 4, 1, \"div\", 63)(5, DropZoneCanvasComponent_div_91_div_3_button_5_Template, 2, 0, \"button\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, DropZoneCanvasComponent_div_91_div_3_button_6_Template, 2, 0, \"button\", 64);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const node_r9 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"guardrail-card\", ctx_r2.config.agentType === \"individual\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((node_r9.data == null ? null : node_r9.data.name) || (ctx_r2.config.agentType === \"individual\" ? \"Guardrail\" : \"Tool\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.config.agentType === \"individual\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.config.agentType === \"individual\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.config.agentType === \"collaborative\");\n  }\n}\nfunction DropZoneCanvasComponent_div_91_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵtemplate(1, DropZoneCanvasComponent_div_91_div_1_Template, 2, 1, \"div\", 50);\n    i0.ɵɵelementStart(2, \"div\", 51);\n    i0.ɵɵtemplate(3, DropZoneCanvasComponent_div_91_div_3_Template, 7, 6, \"div\", 61);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.bottomNodes.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.bottomNodes)(\"ngForTrackBy\", ctx_r2.trackByNodeId);\n  }\n}\nexport let DropZoneCanvasComponent = /*#__PURE__*/(() => {\n  class DropZoneCanvasComponent {\n    nodeTemplate;\n    config;\n    nodes = [];\n    nodeDropped = new EventEmitter();\n    nodeDeleted = new EventEmitter();\n    guardrailToggled = new EventEmitter();\n    // Accordion state management\n    zoneStates = {\n      prompt: true,\n      // expanded by default\n      model: true,\n      // expanded by default\n      knowledge: true,\n      // expanded by default\n      guardrail: true,\n      // expanded by default\n      tool: true // expanded by default\n    };\n    // Track guardrail enable/disable states\n    guardrailStates = {};\n    // Computed properties for categorized nodes\n    get promptNodes() {\n      return this.nodes.filter(node => node.data?.type === 'prompt');\n    }\n    get modelNodes() {\n      return this.nodes.filter(node => node.data?.type === 'model');\n    }\n    get knowledgeNodes() {\n      return this.nodes.filter(node => node.data?.type === 'knowledge');\n    }\n    get bottomNodes() {\n      if (this.config.agentType === 'individual') {\n        return this.nodes.filter(node => node.data?.type === 'guardrail');\n      } else {\n        return this.nodes.filter(node => node.data?.type === 'tool');\n      }\n    }\n    // Progress calculation\n    get completionPercentage() {\n      let completed = 0;\n      let total = 2; // Only 2 required: prompt and model\n      // Check required components only\n      if (this.promptNodes.length > 0) completed++;\n      if (this.modelNodes.length > 0) completed++;\n      // Knowledge, Guardrails, and Tools are now optional (don't count toward completion)\n      return Math.round(completed / total * 100);\n    }\n    onDragOver(event, zoneType) {\n      event.preventDefault();\n      // Try both possible drag data formats\n      const dragData = event.dataTransfer?.getData('application/reactflow') || event.dataTransfer?.getData('text/plain');\n      if (dragData) {\n        try {\n          const data = JSON.parse(dragData);\n          // Check if the dragged item is compatible with this zone\n          if (this.isCompatibleWithZone(data, zoneType)) {\n            event.dataTransfer.dropEffect = 'copy';\n          } else {\n            event.dataTransfer.dropEffect = 'none';\n          }\n        } catch (e) {\n          event.dataTransfer.dropEffect = 'none';\n        }\n      }\n    }\n    onDrop(event, zoneType) {\n      event.preventDefault();\n      // Try both possible drag data formats\n      const dragData = event.dataTransfer?.getData('application/reactflow') || event.dataTransfer?.getData('text/plain');\n      if (dragData) {\n        try {\n          const data = JSON.parse(dragData);\n          console.log('🎯 Drop zone received data:', {\n            data,\n            zoneType\n          });\n          // Check compatibility\n          if (!this.isCompatibleWithZone(data, zoneType)) {\n            console.log('Incompatible node type for this zone:', data.type, 'vs', zoneType);\n            return; // Bounce back\n          }\n          console.log('✅ Compatible drop detected');\n          // Check limits\n          if (!this.canAddToZone(zoneType)) {\n            console.log('Zone limit reached, replacing existing node');\n            // For single-node zones, we'll replace the existing node\n          }\n          // Calculate position within the zone\n          const rect = event.currentTarget.getBoundingClientRect();\n          const position = {\n            x: event.clientX - rect.left,\n            y: event.clientY - rect.top\n          };\n          console.log('✅ Emitting nodeDropped event');\n          this.nodeDropped.emit({\n            node: data,\n            zone: zoneType,\n            position\n          });\n        } catch (e) {\n          console.error('Invalid drag data:', e);\n        }\n      }\n    }\n    onDeleteNode(nodeId) {\n      // Clean up guardrail state if it's a guardrail node\n      this.cleanupGuardrailState(nodeId);\n      this.nodeDeleted.emit(nodeId);\n    }\n    trackByNodeId(index, node) {\n      return node.id;\n    }\n    // Guardrail toggle functionality\n    onGuardrailToggle(nodeId, enabled) {\n      this.guardrailStates[nodeId] = enabled;\n      this.guardrailToggled.emit({\n        nodeId,\n        enabled\n      });\n    }\n    onGuardrailCheckboxChange(event, nodeId) {\n      const checkbox = event.target;\n      if (checkbox) {\n        this.onGuardrailToggle(nodeId, checkbox.checked);\n      }\n    }\n    isGuardrailEnabled(nodeId) {\n      // Default to true (enabled) if not explicitly set\n      return this.guardrailStates[nodeId] !== false;\n    }\n    // Initialize guardrail state when node is added\n    initializeGuardrailState(nodeId, enabled = true) {\n      if (this.guardrailStates[nodeId] === undefined) {\n        this.guardrailStates[nodeId] = enabled;\n      }\n    }\n    // Clean up guardrail state when node is deleted\n    cleanupGuardrailState(nodeId) {\n      delete this.guardrailStates[nodeId];\n    }\n    // Accordion toggle functionality\n    toggleZone(zoneType) {\n      if (zoneType === 'guardrail' || zoneType === 'tool') {\n        // Handle both guardrail and tool zones\n        const actualType = this.config.agentType === 'individual' ? 'guardrail' : 'tool';\n        this.zoneStates[actualType] = !this.zoneStates[actualType];\n      } else {\n        this.zoneStates[zoneType] = !this.zoneStates[zoneType];\n      }\n    }\n    // Get zone state for template\n    isZoneExpanded(zoneType) {\n      if (zoneType === 'guardrail' || zoneType === 'tool') {\n        const actualType = this.config.agentType === 'individual' ? 'guardrail' : 'tool';\n        return this.zoneStates[actualType];\n      }\n      return this.zoneStates[zoneType];\n    }\n    isCompatibleWithZone(dragData, zoneType) {\n      const nodeType = dragData.type || dragData.data?.type;\n      switch (zoneType) {\n        case 'prompt':\n          return nodeType === 'prompt';\n        case 'model':\n          return nodeType === 'model';\n        case 'knowledge':\n          return nodeType === 'knowledge';\n        case 'guardrail':\n          return nodeType === 'guardrail';\n        case 'tool':\n          return nodeType === 'tool';\n        default:\n          return false;\n      }\n    }\n    canAddToZone(zoneType) {\n      switch (zoneType) {\n        case 'prompt':\n          return this.promptNodes.length < this.config.allowedCategories.prompts.max;\n        case 'model':\n          return this.modelNodes.length < this.config.allowedCategories.models.max;\n        case 'knowledge':\n          return this.knowledgeNodes.length < this.config.allowedCategories.knowledge.max;\n        case 'guardrail':\n          return this.bottomNodes.length < this.config.allowedCategories.guardrails.max;\n        case 'tool':\n          return this.bottomNodes.length < this.config.allowedCategories.tools.max;\n        default:\n          return false;\n      }\n    }\n    static ɵfac = function DropZoneCanvasComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DropZoneCanvasComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DropZoneCanvasComponent,\n      selectors: [[\"app-drop-zone-canvas\"]],\n      contentQueries: function DropZoneCanvasComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, _c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.nodeTemplate = _t.first);\n        }\n      },\n      inputs: {\n        config: \"config\",\n        nodes: \"nodes\"\n      },\n      outputs: {\n        nodeDropped: \"nodeDropped\",\n        nodeDeleted: \"nodeDeleted\",\n        guardrailToggled: \"guardrailToggled\"\n      },\n      decls: 92,\n      vars: 40,\n      consts: [[1, \"drop-zone-canvas-container\"], [1, \"central-progress\"], [1, \"progress-ring\"], [\"width\", \"120\", \"height\", \"120\", 1, \"progress-circle\"], [\"id\", \"progressGradient\", \"x1\", \"0%\", \"y1\", \"0%\", \"x2\", \"100%\", \"y2\", \"100%\"], [\"offset\", \"33.91%\", 2, \"stop-color\", \"#0084ff\"], [\"offset\", \"100%\", 2, \"stop-color\", \"#03bdd4\"], [\"cx\", \"60\", \"cy\", \"60\", \"r\", \"50\", \"fill\", \"none\", \"stroke\", \"#e5e7eb\", \"stroke-width\", \"8\", 1, \"progress-background\"], [\"cx\", \"60\", \"cy\", \"60\", \"r\", \"50\", \"fill\", \"none\", \"stroke\", \"url(#progressGradient)\", \"stroke-width\", \"8\", \"stroke-linecap\", \"round\", \"transform\", \"rotate(180 60 60)\", 1, \"progress-bar\"], [1, \"progress-content\"], [1, \"progress-percentage\"], [1, \"progress-label\"], [\"id\", \"parent-box\"], [\"id\", \"box1\"], [1, \"drop-zone\", \"north-zone\", \"prompts-zone\", 3, \"dragover\", \"drop\"], [1, \"zone-header\", 3, \"click\"], [1, \"header-content\"], [1, \"header-icon\"], [\"width\", \"45\", \"height\", \"44\", \"viewBox\", \"0 0 45 44\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"x\", \"0.679688\", \"width\", \"44\", \"height\", \"44\", \"rx\", \"8\", \"fill\", \"#5082EF\"], [\"d\", \"M20.6166 25.5C20.5273 25.1539 20.347 24.8381 20.0942 24.5854C19.8415 24.3327 19.5257 24.1523 19.1796 24.063L13.0446 22.481C12.94 22.4513 12.8478 22.3883 12.7822 22.3014C12.7166 22.2146 12.6812 22.1088 12.6812 22C12.6812 21.8912 12.7166 21.7854 12.7822 21.6986C12.8478 21.6118 12.94 21.5487 13.0446 21.519L19.1796 19.936C19.5256 19.8468 19.8413 19.6666 20.094 19.414C20.3467 19.1615 20.5272 18.8459 20.6166 18.5L22.1986 12.365C22.228 12.2599 22.291 12.1673 22.3779 12.1014C22.4649 12.0355 22.571 11.9998 22.6801 11.9998C22.7892 11.9998 22.8954 12.0355 22.9823 12.1014C23.0692 12.1673 23.1322 12.2599 23.1616 12.365L24.7426 18.5C24.8319 18.8461 25.0123 19.1619 25.265 19.4146C25.5177 19.6673 25.8336 19.8477 26.1796 19.937L32.3146 21.518C32.4201 21.5471 32.5132 21.61 32.5795 21.6971C32.6458 21.7841 32.6817 21.8906 32.6817 22C32.6817 22.1094 32.6458 22.2159 32.5795 22.3029C32.5132 22.39 32.4201 22.4529 32.3146 22.482L26.1796 24.063C25.8336 24.1523 25.5177 24.3327 25.265 24.5854C25.0123 24.8381 24.8319 25.1539 24.7426 25.5L23.1606 31.635C23.1312 31.7401 23.0682 31.8327 22.9813 31.8986C22.8944 31.9646 22.7882 32.0003 22.6791 32.0003C22.57 32.0003 22.4639 31.9646 22.3769 31.8986C22.29 31.8327 22.227 31.7401 22.1976 31.635L20.6166 25.5Z\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M30.6797 13V17\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M32.6797 15H28.6797\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M14.6797 27V29\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M15.6797 28H13.6797\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"zone-title\"], [1, \"header-actions\"], [1, \"required-badge\"], [\"type\", \"button\", 1, \"accordion-toggle\", 3, \"click\"], [\"width\", \"16\", \"height\", \"16\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\"], [\"d\", \"M6 9L12 15L18 9\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"class\", \"zone-content\", 4, \"ngIf\"], [1, \"drop-zone\", \"west-zone\", \"knowledge-zone\", 3, \"dragover\", \"drop\"], [\"x\", \"0.679688\", \"width\", \"44\", \"height\", \"44\", \"rx\", \"8\", \"fill\", \"#308666\"], [\"d\", \"M22.6797 17V31\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M26.6797 22H28.6797\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M26.6797 18H28.6797\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M13.6797 28C13.4145 28 13.1601 27.8946 12.9726 27.7071C12.785 27.5196 12.6797 27.2652 12.6797 27V14C12.6797 13.7348 12.785 13.4804 12.9726 13.2929C13.1601 13.1054 13.4145 13 13.6797 13H18.6797C19.7406 13 20.758 13.4214 21.5081 14.1716C22.2583 14.9217 22.6797 15.9391 22.6797 17C22.6797 15.9391 23.1011 14.9217 23.8513 14.1716C24.6014 13.4214 25.6188 13 26.6797 13H31.6797C31.9449 13 32.1993 13.1054 32.3868 13.2929C32.5743 13.4804 32.6797 13.7348 32.6797 14V27C32.6797 27.2652 32.5743 27.5196 32.3868 27.7071C32.1993 27.8946 31.9449 28 31.6797 28H25.6797C24.884 28 24.121 28.3161 23.5584 28.8787C22.9958 29.4413 22.6797 30.2044 22.6797 31C22.6797 30.2044 22.3636 29.4413 21.801 28.8787C21.2384 28.3161 20.4753 28 19.6797 28H13.6797Z\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M16.6797 22H18.6797\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M16.6797 18H18.6797\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"optional-badge\"], [\"id\", \"box2\"], [1, \"drop-zone\", \"east-zone\", \"models-zone\", 3, \"dragover\", \"drop\"], [\"x\", \"0.679688\", \"width\", \"44\", \"height\", \"44\", \"rx\", \"8\", \"fill\", \"#997BCF\"], [\"d\", \"M31.6797 18C31.6793 17.6493 31.5867 17.3048 31.4112 17.0012C31.2357 16.6975 30.9834 16.4454 30.6797 16.27L23.6797 12.27C23.3756 12.0945 23.0308 12.0021 22.6797 12.0021C22.3286 12.0021 21.9837 12.0945 21.6797 12.27L14.6797 16.27C14.376 16.4454 14.1237 16.6975 13.9481 17.0012C13.7726 17.3048 13.68 17.6493 13.6797 18V26C13.68 26.3508 13.7726 26.6952 13.9481 26.9989C14.1237 27.3025 14.376 27.5547 14.6797 27.73L21.6797 31.73C21.9837 31.9056 22.3286 31.998 22.6797 31.998C23.0308 31.998 23.3756 31.9056 23.6797 31.73L30.6797 27.73C30.9834 27.5547 31.2357 27.3025 31.4112 26.9989C31.5867 26.6952 31.6793 26.3508 31.6797 26V18Z\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M13.9795 17L22.6795 22L31.3795 17\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M22.6797 32V22\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"drop-zone\", \"south-zone\", 3, \"dragover\", \"drop\"], [\"width\", \"45\", \"height\", \"44\", \"viewBox\", \"0 0 45 44\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\", 4, \"ngIf\"], [1, \"zone-content\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [1, \"nodes-list\"], [\"class\", \"kanban-card\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"empty-state\"], [1, \"kanban-card\"], [1, \"card-title\"], [1, \"delete-btn\", 3, \"click\"], [\"x\", \"0.679688\", \"width\", \"44\", \"height\", \"44\", \"rx\", \"8\", \"fill\", \"#DC2626\"], [\"d\", \"M30.6797 23C30.6797 28 27.1797 30.5 23.0197 31.95C22.8019 32.0238 22.5652 32.0202 22.3497 31.94C18.1797 30.5 14.6797 28 14.6797 23V16C14.6797 15.7347 14.785 15.4804 14.9726 15.2929C15.1601 15.1053 15.4145 15 15.6797 15C17.6797 15 20.1797 13.8 21.9197 12.28C22.1315 12.099 22.401 11.9995 22.6797 11.9995C22.9583 11.9995 23.2278 12.099 23.4397 12.28C25.1897 13.81 27.6797 15 29.6797 15C29.9449 15 30.1993 15.1053 30.3868 15.2929C30.5743 15.4804 30.6797 15.7347 30.6797 16V23Z\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"x\", \"0.679688\", \"width\", \"44\", \"height\", \"44\", \"rx\", \"8\", \"fill\", \"#D97706\"], [\"d\", \"M25.3795 16.3C25.1963 16.4869 25.0937 16.7382 25.0937 17C25.0937 17.2617 25.1963 17.5131 25.3795 17.7L26.9795 19.3C27.1665 19.4832 27.4178 19.5858 27.6795 19.5858C27.9413 19.5858 28.1926 19.4832 28.3795 19.3L32.1495 15.53C32.6524 16.6412 32.8046 17.8792 32.586 19.0791C32.3674 20.279 31.7883 21.3838 30.9258 22.2463C30.0634 23.1087 28.9586 23.6878 27.7587 23.9064C26.5588 24.1251 25.3207 23.9728 24.2095 23.47L17.2995 30.38C16.9017 30.7778 16.3622 31.0013 15.7995 31.0013C15.2369 31.0013 14.6974 30.7778 14.2995 30.38C13.9017 29.9822 13.6782 29.4426 13.6782 28.88C13.6782 28.3174 13.9017 27.7778 14.2995 27.38L21.2095 20.47C20.7067 19.3588 20.5545 18.1207 20.7731 16.9208C20.9917 15.7209 21.5708 14.6161 22.4333 13.7537C23.2957 12.8913 24.4005 12.3121 25.6004 12.0935C26.8003 11.8749 28.0384 12.0271 29.1495 12.53L25.3895 16.29L25.3795 16.3Z\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"class\", \"kanban-card\", 3, \"guardrail-card\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"card-content\"], [\"class\", \"guardrail-toggle\", 4, \"ngIf\"], [\"class\", \"delete-btn\", 3, \"click\", 4, \"ngIf\"], [1, \"guardrail-toggle\"], [1, \"toggle-switch\"], [\"type\", \"checkbox\", 3, \"change\", \"checked\"], [1, \"toggle-slider\"]],\n      template: function DropZoneCanvasComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(3, \"svg\", 3)(4, \"defs\")(5, \"linearGradient\", 4);\n          i0.ɵɵelement(6, \"stop\", 5)(7, \"stop\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(8, \"circle\", 7)(9, \"circle\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(10, \"div\", 9)(11, \"div\", 10);\n          i0.ɵɵtext(12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 11);\n          i0.ɵɵtext(14, \"Complete\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(15, \"div\", 12)(16, \"div\", 13)(17, \"div\", 14);\n          i0.ɵɵlistener(\"dragover\", function DropZoneCanvasComponent_Template_div_dragover_17_listener($event) {\n            return ctx.onDragOver($event, \"prompt\");\n          })(\"drop\", function DropZoneCanvasComponent_Template_div_drop_17_listener($event) {\n            return ctx.onDrop($event, \"prompt\");\n          });\n          i0.ɵɵelementStart(18, \"div\", 15);\n          i0.ɵɵlistener(\"click\", function DropZoneCanvasComponent_Template_div_click_18_listener() {\n            return ctx.toggleZone(\"prompt\");\n          });\n          i0.ɵɵelementStart(19, \"div\", 16)(20, \"div\", 17);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(21, \"svg\", 18);\n          i0.ɵɵelement(22, \"rect\", 19)(23, \"path\", 20)(24, \"path\", 21)(25, \"path\", 22)(26, \"path\", 23)(27, \"path\", 24);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(28, \"h3\", 25);\n          i0.ɵɵtext(29, \"System Prompt\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"div\", 26)(31, \"span\", 27);\n          i0.ɵɵtext(32, \"Required\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"button\", 28);\n          i0.ɵɵlistener(\"click\", function DropZoneCanvasComponent_Template_button_click_33_listener($event) {\n            ctx.toggleZone(\"prompt\");\n            return $event.stopPropagation();\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(34, \"svg\", 29);\n          i0.ɵɵelement(35, \"path\", 30);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(36, DropZoneCanvasComponent_div_36_Template, 4, 3, \"div\", 31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(37, \"div\", 32);\n          i0.ɵɵlistener(\"dragover\", function DropZoneCanvasComponent_Template_div_dragover_37_listener($event) {\n            return ctx.onDragOver($event, \"knowledge\");\n          })(\"drop\", function DropZoneCanvasComponent_Template_div_drop_37_listener($event) {\n            return ctx.onDrop($event, \"knowledge\");\n          });\n          i0.ɵɵelementStart(38, \"div\", 15);\n          i0.ɵɵlistener(\"click\", function DropZoneCanvasComponent_Template_div_click_38_listener() {\n            return ctx.toggleZone(\"knowledge\");\n          });\n          i0.ɵɵelementStart(39, \"div\", 16)(40, \"div\", 17);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(41, \"svg\", 18);\n          i0.ɵɵelement(42, \"rect\", 33)(43, \"path\", 34)(44, \"path\", 35)(45, \"path\", 36)(46, \"path\", 37)(47, \"path\", 38)(48, \"path\", 39);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(49, \"h3\", 25);\n          i0.ɵɵtext(50, \"Knowledgebase\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"div\", 26)(52, \"span\", 40);\n          i0.ɵɵtext(53, \"Optional\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"button\", 28);\n          i0.ɵɵlistener(\"click\", function DropZoneCanvasComponent_Template_button_click_54_listener($event) {\n            ctx.toggleZone(\"knowledge\");\n            return $event.stopPropagation();\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(55, \"svg\", 29);\n          i0.ɵɵelement(56, \"path\", 30);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(57, DropZoneCanvasComponent_div_57_Template, 4, 3, \"div\", 31);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(58, \"div\", 41)(59, \"div\", 42);\n          i0.ɵɵlistener(\"dragover\", function DropZoneCanvasComponent_Template_div_dragover_59_listener($event) {\n            return ctx.onDragOver($event, \"model\");\n          })(\"drop\", function DropZoneCanvasComponent_Template_div_drop_59_listener($event) {\n            return ctx.onDrop($event, \"model\");\n          });\n          i0.ɵɵelementStart(60, \"div\", 15);\n          i0.ɵɵlistener(\"click\", function DropZoneCanvasComponent_Template_div_click_60_listener() {\n            return ctx.toggleZone(\"model\");\n          });\n          i0.ɵɵelementStart(61, \"div\", 16)(62, \"div\", 17);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(63, \"svg\", 18);\n          i0.ɵɵelement(64, \"rect\", 43)(65, \"path\", 44)(66, \"path\", 45)(67, \"path\", 46);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(68, \"h3\", 25);\n          i0.ɵɵtext(69, \"AI Model\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(70, \"div\", 26)(71, \"span\", 27);\n          i0.ɵɵtext(72, \"Required\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"button\", 28);\n          i0.ɵɵlistener(\"click\", function DropZoneCanvasComponent_Template_button_click_73_listener($event) {\n            ctx.toggleZone(\"model\");\n            return $event.stopPropagation();\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(74, \"svg\", 29);\n          i0.ɵɵelement(75, \"path\", 30);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(76, DropZoneCanvasComponent_div_76_Template, 4, 3, \"div\", 31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(77, \"div\", 47);\n          i0.ɵɵlistener(\"dragover\", function DropZoneCanvasComponent_Template_div_dragover_77_listener($event) {\n            return ctx.onDragOver($event, ctx.config.agentType === \"individual\" ? \"guardrail\" : \"tool\");\n          })(\"drop\", function DropZoneCanvasComponent_Template_div_drop_77_listener($event) {\n            return ctx.onDrop($event, ctx.config.agentType === \"individual\" ? \"guardrail\" : \"tool\");\n          });\n          i0.ɵɵelementStart(78, \"div\", 15);\n          i0.ɵɵlistener(\"click\", function DropZoneCanvasComponent_Template_div_click_78_listener() {\n            return ctx.toggleZone(ctx.config.agentType === \"individual\" ? \"guardrail\" : \"tool\");\n          });\n          i0.ɵɵelementStart(79, \"div\", 16)(80, \"div\", 17);\n          i0.ɵɵtemplate(81, DropZoneCanvasComponent__svg_svg_81_Template, 3, 0, \"svg\", 48)(82, DropZoneCanvasComponent__svg_svg_82_Template, 3, 0, \"svg\", 48);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(83, \"h3\", 25);\n          i0.ɵɵtext(84);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(85, \"div\", 26)(86, \"span\", 40);\n          i0.ɵɵtext(87, \"Optional\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(88, \"button\", 28);\n          i0.ɵɵlistener(\"click\", function DropZoneCanvasComponent_Template_button_click_88_listener($event) {\n            ctx.toggleZone(ctx.config.agentType === \"individual\" ? \"guardrail\" : \"tool\");\n            return $event.stopPropagation();\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(89, \"svg\", 29);\n          i0.ɵɵelement(90, \"path\", 30);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(91, DropZoneCanvasComponent_div_91_Template, 4, 3, \"div\", 31);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵstyleProp(\"stroke-dasharray\", 314)(\"stroke-dashoffset\", 314 - 314 * ctx.completionPercentage / 100);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\"\", ctx.completionPercentage, \"%\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"has-nodes\", ctx.promptNodes.length > 0)(\"collapsed\", !ctx.isZoneExpanded(\"prompt\"));\n          i0.ɵɵadvance(17);\n          i0.ɵɵstyleProp(\"transform\", ctx.isZoneExpanded(\"prompt\") ? \"rotate(180deg)\" : \"rotate(0deg)\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isZoneExpanded(\"prompt\"));\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"has-nodes\", ctx.knowledgeNodes.length > 0)(\"collapsed\", !ctx.isZoneExpanded(\"knowledge\"));\n          i0.ɵɵadvance(18);\n          i0.ɵɵstyleProp(\"transform\", ctx.isZoneExpanded(\"knowledge\") ? \"rotate(180deg)\" : \"rotate(0deg)\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isZoneExpanded(\"knowledge\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"has-nodes\", ctx.modelNodes.length > 0)(\"collapsed\", !ctx.isZoneExpanded(\"model\"));\n          i0.ɵɵadvance(15);\n          i0.ɵɵstyleProp(\"transform\", ctx.isZoneExpanded(\"model\") ? \"rotate(180deg)\" : \"rotate(0deg)\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isZoneExpanded(\"model\"));\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"guardrails-zone\", ctx.config.agentType === \"individual\")(\"tools-zone\", ctx.config.agentType === \"collaborative\")(\"has-nodes\", ctx.bottomNodes.length > 0)(\"collapsed\", !ctx.isZoneExpanded(ctx.config.agentType === \"individual\" ? \"guardrail\" : \"tool\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.config.agentType === \"individual\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.config.agentType === \"collaborative\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.config.agentType === \"individual\" ? \"Guardrails\" : \"Tools\", \" \");\n          i0.ɵɵadvance(5);\n          i0.ɵɵstyleProp(\"transform\", ctx.isZoneExpanded(ctx.config.agentType === \"individual\" ? \"guardrail\" : \"tool\") ? \"rotate(180deg)\" : \"rotate(0deg)\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isZoneExpanded(ctx.config.agentType === \"individual\" ? \"guardrail\" : \"tool\"));\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf],\n      styles: [\".drop-zone-canvas-container[_ngcontent-%COMP%] {\\n  height: 100%;\\n  width: 100%;\\n  min-height: 500px;\\n  background-color: #f8f9fa;\\n  background-image: radial-gradient(circle, #d1d5db 1px, transparent 1px);\\n  background-size: 20px 20px;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  border: 1px solid #d1d3d8;\\n}\\n\\n.central-progress[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 58%;\\n  transform: translate(-50%, -50%);\\n  z-index: 10;\\n  background-color: white;\\n  border-radius: 50%;\\n  padding: 8px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n.progress-ring[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.progress-circle[_ngcontent-%COMP%] {\\n  transform: rotate(-90deg);\\n}\\n\\n.progress-background[_ngcontent-%COMP%] {\\n  opacity: 0.15;\\n}\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  transition: stroke-dashoffset 0.3s ease;\\n}\\n\\n.progress-content[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  text-align: center;\\n  color: #374151;\\n}\\n\\n.progress-percentage[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  line-height: 1;\\n}\\n\\n.progress-label[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #6b7280;\\n  margin-top: 2px;\\n}\\n\\n.drop-zone[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  padding: 20px;\\n  transition: all 0.3s ease;\\n}\\n.drop-zone.has-nodes[_ngcontent-%COMP%] {\\n  border-style: solid;\\n}\\n.drop-zone.collapsed[_ngcontent-%COMP%] {\\n  min-height: 40px;\\n}\\n.drop-zone.collapsed[_ngcontent-%COMP%]   .zone-header[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n\\n.north-zone[_ngcontent-%COMP%] {\\n  width: 300px;\\n  height: auto;\\n  background-color: #ecf0fa;\\n  color: #005eb5;\\n  border: 2px solid #9ab7f6;\\n  position: relative;\\n  top: -2.5rem;\\n  left: -30%;\\n}\\n\\n.east-zone[_ngcontent-%COMP%] {\\n  width: 300px;\\n  height: auto;\\n  background-color: #f2ebfd;\\n  border: 2px solid #d6c2f9;\\n  color: #d6c2f9;\\n  position: relative;\\n  top: -2.5rem;\\n  right: -30%;\\n}\\n\\n.south-zone[_ngcontent-%COMP%] {\\n  width: 300px;\\n  height: auto;\\n  background-color: #fbf6f7;\\n  border: 2px solid #fecaca;\\n  color: #dc2626 !important;\\n  position: absolute;\\n  bottom: -3.5rem;\\n  right: -30%;\\n}\\n.south-zone.has-nodes[_ngcontent-%COMP%] {\\n  height: auto;\\n  min-height: 100px;\\n  max-height: 200px;\\n  overflow-y: auto;\\n}\\n\\n.west-zone[_ngcontent-%COMP%] {\\n  width: 300px;\\n  height: auto;\\n  background-color: #ecf8f4;\\n  border: 2px solid #a9e1cc;\\n  color: #25684f;\\n  position: absolute;\\n  bottom: -3.5rem;\\n  left: -30%;\\n}\\n.west-zone.has-nodes[_ngcontent-%COMP%] {\\n  height: auto;\\n  min-height: 100px;\\n  max-height: 200px;\\n  overflow-y: auto;\\n}\\n\\n#parent-box[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  height: 100%;\\n  width: 100%;\\n  gap: 2rem;\\n}\\n\\n#box1[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: space-between;\\n  height: 300px;\\n  position: relative;\\n  border: 1px solid #bbbec5;\\n  position: fixed;\\n  top: 32%;\\n  left: 40%;\\n  border-left: none;\\n}\\n\\n#box2[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: space-between;\\n  height: 300px;\\n  position: relative;\\n  border: 1px solid #bbbec5;\\n  position: fixed;\\n  top: 32%;\\n  left: 60%;\\n  border-right: none;\\n}\\n\\n.zone-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin-bottom: 16px;\\n  padding-bottom: 12px;\\n  cursor: pointer;\\n  -webkit-user-select: none;\\n          user-select: none;\\n  transition: all 0.2s ease;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 8px;\\n  background-color: rgba(59, 130, 246, 0.1);\\n  color: #3b82f6;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .zone-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #374151;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .accordion-toggle[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 4px;\\n  border-radius: 4px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.2s ease;\\n  color: #6b7280;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .accordion-toggle[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 0, 0, 0.05);\\n  color: #374151;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .accordion-toggle[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  transition: transform 0.3s ease;\\n}\\n\\n.required-badge[_ngcontent-%COMP%] {\\n  background-color: var(--status-error);\\n  color: white;\\n  font-size: 10px;\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n  font-weight: 500;\\n}\\n\\n.optional-badge[_ngcontent-%COMP%] {\\n  background-color: var(--status-warning);\\n  color: white;\\n  font-size: 10px;\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n  font-weight: 500;\\n}\\n\\n.zone-content[_ngcontent-%COMP%] {\\n  min-height: 60px;\\n  flex: 1;\\n  transition: all 0.3s ease-in-out;\\n  opacity: 1;\\n  overflow: hidden;\\n}\\n\\n.west-zone[_ngcontent-%COMP%], \\n.south-zone[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n.west-zone[_ngcontent-%COMP%]   .zone-content[_ngcontent-%COMP%], \\n.south-zone[_ngcontent-%COMP%]   .zone-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  overflow-y: auto;\\n  max-height: 200px;\\n}\\n.west-zone[_ngcontent-%COMP%]   .nodes-list[_ngcontent-%COMP%], \\n.south-zone[_ngcontent-%COMP%]   .nodes-list[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n}\\n\\n.empty-state[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: var(--text-secondary);\\n  font-size: 12px;\\n  padding: 20px 0;\\n  font-style: italic;\\n}\\n\\n.nodes-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n}\\n\\n.kanban-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  padding: 12px 16px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n  transition: all 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n}\\n.kanban-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\\n  transform: translateY(-1px);\\n}\\n.kanban-card[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #374151;\\n  flex: 1;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  max-width: calc(100% - 50px);\\n}\\n.kanban-card[_ngcontent-%COMP%]   .delete-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #9ca3af;\\n  font-size: 16px;\\n  cursor: pointer;\\n  padding: 0;\\n  width: 20px;\\n  height: 20px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 4px;\\n  margin-left: 8px;\\n  flex-shrink: 0;\\n}\\n.kanban-card[_ngcontent-%COMP%]   .delete-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #fef2f2;\\n  color: #ef4444;\\n}\\n\\n.guardrail-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  width: 100%;\\n}\\n.guardrail-card[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\\n  flex: 1;\\n  margin-right: 0;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n  min-width: 0;\\n  max-width: calc(100% - 80px) !important;\\n}\\n.guardrail-card[_ngcontent-%COMP%]   .guardrail-toggle[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n}\\n\\n.toggle-switch[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: inline-block;\\n  width: 40px;\\n  height: 20px;\\n  cursor: pointer;\\n}\\n.toggle-switch[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  opacity: 0;\\n  width: 0;\\n  height: 0;\\n}\\n.toggle-switch[_ngcontent-%COMP%]   .toggle-slider[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background-color: #ccc;\\n  border-radius: 20px;\\n  transition: 0.3s;\\n}\\n.toggle-switch[_ngcontent-%COMP%]   .toggle-slider[_ngcontent-%COMP%]::before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  height: 16px;\\n  width: 16px;\\n  left: 2px;\\n  bottom: 2px;\\n  background-color: white;\\n  border-radius: 50%;\\n  transition: 0.3s;\\n}\\n.toggle-switch[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked    + .toggle-slider[_ngcontent-%COMP%] {\\n  background-color: #ef4444;\\n}\\n.toggle-switch[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked    + .toggle-slider[_ngcontent-%COMP%]::before {\\n  transform: translateX(20px);\\n}\\n.toggle-switch[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus    + .toggle-slider[_ngcontent-%COMP%] {\\n  box-shadow: 0 0 1px #ef4444;\\n}\\n\\n.prompts-zone.has-nodes[_ngcontent-%COMP%] {\\n  border-color: #3b82f6;\\n}\\n.prompts-zone[_ngcontent-%COMP%]   .zone-title[_ngcontent-%COMP%] {\\n  color: #005eb5 !important;\\n}\\n.prompts-zone[_ngcontent-%COMP%]   .required-badge[_ngcontent-%COMP%] {\\n  background-color: #3b82f6;\\n}\\n\\n.models-zone.has-nodes[_ngcontent-%COMP%] {\\n  border-color: #8b5cf6;\\n}\\n.models-zone[_ngcontent-%COMP%]   .zone-title[_ngcontent-%COMP%] {\\n  color: #673ab7 !important;\\n}\\n.models-zone[_ngcontent-%COMP%]   .required-badge[_ngcontent-%COMP%] {\\n  background-color: #8b5cf6;\\n}\\n\\n.knowledge-zone.has-nodes[_ngcontent-%COMP%] {\\n  border-color: #10b981;\\n}\\n.knowledge-zone[_ngcontent-%COMP%]   .zone-title[_ngcontent-%COMP%] {\\n  color: #25684f !important;\\n}\\n.knowledge-zone[_ngcontent-%COMP%]   .optional-badge[_ngcontent-%COMP%] {\\n  background-color: #10b981;\\n}\\n\\n.guardrails-zone.has-nodes[_ngcontent-%COMP%] {\\n  border-color: #ef4444;\\n}\\n.guardrails-zone[_ngcontent-%COMP%]   .zone-title[_ngcontent-%COMP%] {\\n  color: #dc2626 !important;\\n}\\n.guardrails-zone[_ngcontent-%COMP%]   .optional-badge[_ngcontent-%COMP%] {\\n  background-color: #ef4444;\\n}\\n\\n.tools-zone.has-nodes[_ngcontent-%COMP%] {\\n  border-color: #f59e0b;\\n}\\n.tools-zone[_ngcontent-%COMP%]   .zone-title[_ngcontent-%COMP%] {\\n  color: #d97706 !important;\\n}\\n.tools-zone[_ngcontent-%COMP%]   .optional-badge[_ngcontent-%COMP%] {\\n  background-color: #f59e0b;\\n}\\n\\n@media (max-width: 1600px) {\\n  #box1[_ngcontent-%COMP%] {\\n    top: 32%;\\n    left: 35%;\\n  }\\n  #box2[_ngcontent-%COMP%] {\\n    top: 32%;\\n    left: 60%;\\n  }\\n}\\n@media (max-width: 1400px) {\\n  #box1[_ngcontent-%COMP%] {\\n    top: 32%;\\n    left: 30%;\\n  }\\n  #box2[_ngcontent-%COMP%] {\\n    top: 32%;\\n    left: 60%;\\n  }\\n  .connection-system[_ngcontent-%COMP%]   .prompt-connection[_ngcontent-%COMP%]::before {\\n    width: 70px;\\n    left: 155px;\\n  }\\n  .connection-system[_ngcontent-%COMP%]   .prompt-connection[_ngcontent-%COMP%]::after {\\n    left: 223px;\\n  }\\n  .connection-system[_ngcontent-%COMP%]   .model-connection[_ngcontent-%COMP%]::before {\\n    width: 70px;\\n    right: 155px;\\n  }\\n  .connection-system[_ngcontent-%COMP%]   .model-connection[_ngcontent-%COMP%]::after {\\n    right: 223px;\\n  }\\n  .connection-system[_ngcontent-%COMP%]   .knowledge-connection[_ngcontent-%COMP%]::before {\\n    width: calc(26% - 50px);\\n  }\\n  .connection-system[_ngcontent-%COMP%]   .bottom-connection[_ngcontent-%COMP%]::before {\\n    width: calc(26% - 50px);\\n  }\\n}\\n@media (max-width: 1200px) {\\n  .connection-system[_ngcontent-%COMP%]   .prompt-connection[_ngcontent-%COMP%]::before {\\n    width: 60px;\\n    left: 160px;\\n  }\\n  .connection-system[_ngcontent-%COMP%]   .prompt-connection[_ngcontent-%COMP%]::after {\\n    left: 218px;\\n  }\\n  .connection-system[_ngcontent-%COMP%]   .model-connection[_ngcontent-%COMP%]::before {\\n    width: 60px;\\n    right: 160px;\\n  }\\n  .connection-system[_ngcontent-%COMP%]   .model-connection[_ngcontent-%COMP%]::after {\\n    right: 218px;\\n  }\\n  .connection-system[_ngcontent-%COMP%]   .knowledge-connection[_ngcontent-%COMP%]::before {\\n    width: calc(23% - 40px);\\n  }\\n  .connection-system[_ngcontent-%COMP%]   .bottom-connection[_ngcontent-%COMP%]::before {\\n    width: calc(23% - 40px);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .connection-system[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n.drop-zone.drag-over[_ngcontent-%COMP%] {\\n  border-color: var(--brand-primary);\\n  background-color: var(--brand-primary-10);\\n  transform: scale(1.02);\\n}\\n.drop-zone.drag-reject[_ngcontent-%COMP%] {\\n  border-color: var(--status-error);\\n  background-color: rgba(239, 68, 68, 0.1);\\n  animation: _ngcontent-%COMP%_shake 0.3s ease-in-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_shake {\\n  0%, 100% {\\n    transform: translateX(0);\\n  }\\n  25% {\\n    transform: translateX(-2px);\\n  }\\n  75% {\\n    transform: translateX(2px);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .drop-zone-canvas-container[_ngcontent-%COMP%] {\\n    height: 500px;\\n  }\\n  .drop-zone[_ngcontent-%COMP%] {\\n    width: 150px;\\n    height: 100px;\\n    padding: 12px;\\n  }\\n  .north-zone[_ngcontent-%COMP%], \\n   .south-zone[_ngcontent-%COMP%] {\\n    width: 150px;\\n  }\\n  .east-zone[_ngcontent-%COMP%], \\n   .west-zone[_ngcontent-%COMP%] {\\n    width: 150px;\\n    height: 100px;\\n  }\\n  .central-progress[_ngcontent-%COMP%]   .progress-circle[_ngcontent-%COMP%] {\\n    width: 100px;\\n    height: 100px;\\n  }\\n  .central-progress[_ngcontent-%COMP%]   .progress-percentage[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return DropZoneCanvasComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "EventEmitter", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "DropZoneCanvasComponent_div_36_div_3_Template_button_click_3_listener", "node_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "onDeleteNode", "id", "ɵɵadvance", "ɵɵtextInterpolate", "data", "name", "ɵɵtemplate", "DropZoneCanvasComponent_div_36_div_1_Template", "DropZoneCanvasComponent_div_36_div_3_Template", "ɵɵproperty", "promptNodes", "length", "trackByNodeId", "DropZoneCanvasComponent_div_57_div_3_Template_button_click_3_listener", "node_r5", "_r4", "DropZoneCanvasComponent_div_57_div_1_Template", "DropZoneCanvasComponent_div_57_div_3_Template", "knowledgeNodes", "DropZoneCanvasComponent_div_76_div_3_Template_button_click_3_listener", "node_r7", "_r6", "DropZoneCanvasComponent_div_76_div_1_Template", "DropZoneCanvasComponent_div_76_div_3_Template", "modelNodes", "ɵɵelement", "ɵɵtextInterpolate1", "config", "agentType", "DropZoneCanvasComponent_div_91_div_3_div_4_Template_input_change_2_listener", "$event", "_r8", "node_r9", "onGuardrailCheckboxChange", "isGuardrailEnabled", "DropZoneCanvasComponent_div_91_div_3_button_5_Template_button_click_0_listener", "_r10", "DropZoneCanvasComponent_div_91_div_3_button_6_Template_button_click_0_listener", "_r11", "DropZoneCanvasComponent_div_91_div_3_div_4_Template", "DropZoneCanvasComponent_div_91_div_3_button_5_Template", "DropZoneCanvasComponent_div_91_div_3_button_6_Template", "ɵɵclassProp", "DropZoneCanvasComponent_div_91_div_1_Template", "DropZoneCanvasComponent_div_91_div_3_Template", "bottomNodes", "DropZoneCanvasComponent", "nodeTemplate", "nodes", "nodeDropped", "nodeDeleted", "guardrailToggled", "zoneStates", "prompt", "model", "knowledge", "guardrail", "tool", "guardrailStates", "filter", "node", "type", "completionPercentage", "completed", "total", "Math", "round", "onDragOver", "event", "zoneType", "preventDefault", "dragData", "dataTransfer", "getData", "JSON", "parse", "isCompatibleWithZone", "dropEffect", "e", "onDrop", "console", "log", "canAddToZone", "rect", "currentTarget", "getBoundingClientRect", "position", "x", "clientX", "left", "y", "clientY", "top", "emit", "zone", "error", "nodeId", "cleanupGuardrailState", "index", "onGuardrailToggle", "enabled", "checkbox", "target", "checked", "initializeGuardrailState", "undefined", "toggleZone", "actualType", "isZoneExpanded", "nodeType", "allowedCategories", "prompts", "max", "models", "guardrails", "tools", "selectors", "contentQueries", "DropZoneCanvasComponent_ContentQueries", "rf", "ctx", "dirIndex", "DropZoneCanvasComponent_Template_div_dragover_17_listener", "DropZoneCanvasComponent_Template_div_drop_17_listener", "DropZoneCanvasComponent_Template_div_click_18_listener", "DropZoneCanvasComponent_Template_button_click_33_listener", "stopPropagation", "DropZoneCanvasComponent_div_36_Template", "DropZoneCanvasComponent_Template_div_dragover_37_listener", "DropZoneCanvasComponent_Template_div_drop_37_listener", "DropZoneCanvasComponent_Template_div_click_38_listener", "DropZoneCanvasComponent_Template_button_click_54_listener", "DropZoneCanvasComponent_div_57_Template", "DropZoneCanvasComponent_Template_div_dragover_59_listener", "DropZoneCanvasComponent_Template_div_drop_59_listener", "DropZoneCanvasComponent_Template_div_click_60_listener", "DropZoneCanvasComponent_Template_button_click_73_listener", "DropZoneCanvasComponent_div_76_Template", "DropZoneCanvasComponent_Template_div_dragover_77_listener", "DropZoneCanvasComponent_Template_div_drop_77_listener", "DropZoneCanvasComponent_Template_div_click_78_listener", "DropZoneCanvasComponent__svg_svg_81_Template", "DropZoneCanvasComponent__svg_svg_82_Template", "DropZoneCanvasComponent_Template_button_click_88_listener", "DropZoneCanvasComponent_div_91_Template", "ɵɵstyleProp", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\components\\drop-zone-canvas\\drop-zone-canvas.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\components\\drop-zone-canvas\\drop-zone-canvas.component.html"], "sourcesContent": ["import { CommonModule } from '@angular/common';\r\nimport {\r\n  Component,\r\n  Input,\r\n  Output,\r\n  EventEmitter,\r\n  TemplateRef,\r\n  ContentChild,\r\n} from '@angular/core';\r\nimport { CanvasNode } from '../canvas-board/canvas-board.component';\r\n\r\nexport interface DropZoneNode {\r\n  id: string;\r\n  type: 'prompt' | 'model' | 'knowledge' | 'guardrail' | 'tool';\r\n  data: any;\r\n}\r\n\r\nexport interface DropZoneConfig {\r\n  agentType: 'individual' | 'collaborative';\r\n  allowedCategories: {\r\n    prompts: { max: number; required: boolean };\r\n    models: { max: number; required: boolean };\r\n    knowledge: { max: number; required: boolean };\r\n    guardrails: { max: number; required: boolean };\r\n    tools: { max: number; required: boolean };\r\n  };\r\n}\r\n\r\n@Component({\r\n  selector: 'app-drop-zone-canvas',\r\n  standalone: true,\r\n  imports: [CommonModule],\r\n  templateUrl: './drop-zone-canvas.component.html',\r\n  styleUrls: ['./drop-zone-canvas.component.scss'],\r\n})\r\nexport class DropZoneCanvasComponent {\r\n  @ContentChild('nodeTemplate') nodeTemplate!: TemplateRef<any>;\r\n\r\n  @Input() config!: DropZoneConfig;\r\n  @Input() nodes: CanvasNode[] = [];\r\n\r\n  @Output() nodeDropped = new EventEmitter<{\r\n    node: any;\r\n    zone: string;\r\n    position: { x: number; y: number };\r\n  }>();\r\n  @Output() nodeDeleted = new EventEmitter<string>();\r\n  @Output() guardrailToggled = new EventEmitter<{\r\n    nodeId: string;\r\n    enabled: boolean;\r\n  }>();\r\n\r\n  // Accordion state management\r\n  zoneStates = {\r\n    prompt: true, // expanded by default\r\n    model: true, // expanded by default\r\n    knowledge: true, // expanded by default\r\n    guardrail: true, // expanded by default\r\n    tool: true, // expanded by default\r\n  };\r\n\r\n  // Track guardrail enable/disable states\r\n  guardrailStates: { [nodeId: string]: boolean } = {};\r\n\r\n  // Computed properties for categorized nodes\r\n  get promptNodes(): CanvasNode[] {\r\n    return this.nodes.filter((node) => node.data?.type === 'prompt');\r\n  }\r\n\r\n  get modelNodes(): CanvasNode[] {\r\n    return this.nodes.filter((node) => node.data?.type === 'model');\r\n  }\r\n\r\n  get knowledgeNodes(): CanvasNode[] {\r\n    return this.nodes.filter((node) => node.data?.type === 'knowledge');\r\n  }\r\n\r\n  get bottomNodes(): CanvasNode[] {\r\n    if (this.config.agentType === 'individual') {\r\n      return this.nodes.filter((node) => node.data?.type === 'guardrail');\r\n    } else {\r\n      return this.nodes.filter((node) => node.data?.type === 'tool');\r\n    }\r\n  }\r\n\r\n  // Progress calculation\r\n  get completionPercentage(): number {\r\n    let completed = 0;\r\n    let total = 2; // Only 2 required: prompt and model\r\n\r\n    // Check required components only\r\n    if (this.promptNodes.length > 0) completed++;\r\n    if (this.modelNodes.length > 0) completed++;\r\n\r\n    // Knowledge, Guardrails, and Tools are now optional (don't count toward completion)\r\n\r\n    return Math.round((completed / total) * 100);\r\n  }\r\n\r\n  onDragOver(event: DragEvent, zoneType: string): void {\r\n    event.preventDefault();\r\n    // Try both possible drag data formats\r\n    const dragData =\r\n      event.dataTransfer?.getData('application/reactflow') ||\r\n      event.dataTransfer?.getData('text/plain');\r\n    if (dragData) {\r\n      try {\r\n        const data = JSON.parse(dragData);\r\n        // Check if the dragged item is compatible with this zone\r\n        if (this.isCompatibleWithZone(data, zoneType)) {\r\n          event.dataTransfer!.dropEffect = 'copy';\r\n        } else {\r\n          event.dataTransfer!.dropEffect = 'none';\r\n        }\r\n      } catch (e) {\r\n        event.dataTransfer!.dropEffect = 'none';\r\n      }\r\n    }\r\n  }\r\n\r\n  onDrop(event: DragEvent, zoneType: string): void {\r\n    event.preventDefault();\r\n    // Try both possible drag data formats\r\n    const dragData =\r\n      event.dataTransfer?.getData('application/reactflow') ||\r\n      event.dataTransfer?.getData('text/plain');\r\n    if (dragData) {\r\n      try {\r\n        const data = JSON.parse(dragData);\r\n\r\n        console.log('🎯 Drop zone received data:', { data, zoneType });\r\n\r\n        // Check compatibility\r\n        if (!this.isCompatibleWithZone(data, zoneType)) {\r\n          console.log(\r\n            'Incompatible node type for this zone:',\r\n            data.type,\r\n            'vs',\r\n            zoneType,\r\n          );\r\n          return; // Bounce back\r\n        }\r\n\r\n        console.log('✅ Compatible drop detected');\r\n\r\n        // Check limits\r\n        if (!this.canAddToZone(zoneType)) {\r\n          console.log('Zone limit reached, replacing existing node');\r\n          // For single-node zones, we'll replace the existing node\r\n        }\r\n\r\n        // Calculate position within the zone\r\n        const rect = (event.currentTarget as Element).getBoundingClientRect();\r\n        const position = {\r\n          x: event.clientX - rect.left,\r\n          y: event.clientY - rect.top,\r\n        };\r\n\r\n        console.log('✅ Emitting nodeDropped event');\r\n        this.nodeDropped.emit({\r\n          node: data,\r\n          zone: zoneType,\r\n          position,\r\n        });\r\n      } catch (e) {\r\n        console.error('Invalid drag data:', e);\r\n      }\r\n    }\r\n  }\r\n\r\n  onDeleteNode(nodeId: string): void {\r\n    // Clean up guardrail state if it's a guardrail node\r\n    this.cleanupGuardrailState(nodeId);\r\n\r\n    this.nodeDeleted.emit(nodeId);\r\n  }\r\n\r\n  trackByNodeId(index: number, node: any): string {\r\n    return node.id;\r\n  }\r\n\r\n  // Guardrail toggle functionality\r\n  onGuardrailToggle(nodeId: string, enabled: boolean): void {\r\n    this.guardrailStates[nodeId] = enabled;\r\n    this.guardrailToggled.emit({ nodeId, enabled });\r\n  }\r\n\r\n  onGuardrailCheckboxChange(event: Event, nodeId: string): void {\r\n    const checkbox = event.target as HTMLInputElement;\r\n    if (checkbox) {\r\n      this.onGuardrailToggle(nodeId, checkbox.checked);\r\n    }\r\n  }\r\n\r\n  isGuardrailEnabled(nodeId: string): boolean {\r\n    // Default to true (enabled) if not explicitly set\r\n    return this.guardrailStates[nodeId] !== false;\r\n  }\r\n\r\n  // Initialize guardrail state when node is added\r\n  initializeGuardrailState(nodeId: string, enabled: boolean = true): void {\r\n    if (this.guardrailStates[nodeId] === undefined) {\r\n      this.guardrailStates[nodeId] = enabled;\r\n    }\r\n  }\r\n\r\n  // Clean up guardrail state when node is deleted\r\n  cleanupGuardrailState(nodeId: string): void {\r\n    delete this.guardrailStates[nodeId];\r\n  }\r\n\r\n  // Accordion toggle functionality\r\n  toggleZone(zoneType: string): void {\r\n    if (zoneType === 'guardrail' || zoneType === 'tool') {\r\n      // Handle both guardrail and tool zones\r\n      const actualType =\r\n        this.config.agentType === 'individual' ? 'guardrail' : 'tool';\r\n      this.zoneStates[actualType as keyof typeof this.zoneStates] =\r\n        !this.zoneStates[actualType as keyof typeof this.zoneStates];\r\n    } else {\r\n      this.zoneStates[zoneType as keyof typeof this.zoneStates] =\r\n        !this.zoneStates[zoneType as keyof typeof this.zoneStates];\r\n    }\r\n  }\r\n\r\n  // Get zone state for template\r\n  isZoneExpanded(zoneType: string): boolean {\r\n    if (zoneType === 'guardrail' || zoneType === 'tool') {\r\n      const actualType =\r\n        this.config.agentType === 'individual' ? 'guardrail' : 'tool';\r\n      return this.zoneStates[actualType as keyof typeof this.zoneStates];\r\n    }\r\n    return this.zoneStates[zoneType as keyof typeof this.zoneStates];\r\n  }\r\n\r\n  private isCompatibleWithZone(dragData: any, zoneType: string): boolean {\r\n    const nodeType = dragData.type || dragData.data?.type;\r\n\r\n    switch (zoneType) {\r\n      case 'prompt':\r\n        return nodeType === 'prompt';\r\n      case 'model':\r\n        return nodeType === 'model';\r\n      case 'knowledge':\r\n        return nodeType === 'knowledge';\r\n      case 'guardrail':\r\n        return nodeType === 'guardrail';\r\n      case 'tool':\r\n        return nodeType === 'tool';\r\n      default:\r\n        return false;\r\n    }\r\n  }\r\n\r\n  private canAddToZone(zoneType: string): boolean {\r\n    switch (zoneType) {\r\n      case 'prompt':\r\n        return (\r\n          this.promptNodes.length < this.config.allowedCategories.prompts.max\r\n        );\r\n      case 'model':\r\n        return (\r\n          this.modelNodes.length < this.config.allowedCategories.models.max\r\n        );\r\n      case 'knowledge':\r\n        return (\r\n          this.knowledgeNodes.length <\r\n          this.config.allowedCategories.knowledge.max\r\n        );\r\n      case 'guardrail':\r\n        return (\r\n          this.bottomNodes.length < this.config.allowedCategories.guardrails.max\r\n        );\r\n      case 'tool':\r\n        return (\r\n          this.bottomNodes.length < this.config.allowedCategories.tools.max\r\n        );\r\n      default:\r\n        return false;\r\n    }\r\n  }\r\n}\r\n", "<div class=\"drop-zone-canvas-container\">\r\n  <!-- Central Progress Bar -->\r\n  <div class=\"central-progress\">\r\n    <div class=\"progress-ring\">\r\n      <svg class=\"progress-circle\" width=\"120\" height=\"120\">\r\n        <defs>\r\n          <linearGradient\r\n            id=\"progressGradient\"\r\n            x1=\"0%\"\r\n            y1=\"0%\"\r\n            x2=\"100%\"\r\n            y2=\"100%\"\r\n          >\r\n            <stop offset=\"33.91%\" style=\"stop-color: #0084ff\" />\r\n            <stop offset=\"100%\" style=\"stop-color: #03bdd4\" />\r\n          </linearGradient>\r\n        </defs>\r\n        <circle\r\n          class=\"progress-background\"\r\n          cx=\"60\"\r\n          cy=\"60\"\r\n          r=\"50\"\r\n          fill=\"none\"\r\n          stroke=\"#e5e7eb\"\r\n          stroke-width=\"8\"\r\n        />\r\n        <circle\r\n          class=\"progress-bar\"\r\n          cx=\"60\"\r\n          cy=\"60\"\r\n          r=\"50\"\r\n          fill=\"none\"\r\n          stroke=\"url(#progressGradient)\"\r\n          stroke-width=\"8\"\r\n          stroke-linecap=\"round\"\r\n          [style.stroke-dasharray]=\"314\"\r\n          [style.stroke-dashoffset]=\"314 - (314 * completionPercentage) / 100\"\r\n          transform=\"rotate(180 60 60)\"\r\n        />\r\n      </svg>\r\n      <div class=\"progress-content\">\r\n        <div class=\"progress-percentage\">{{ completionPercentage }}%</div>\r\n        <div class=\"progress-label\">Complete</div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Drop Zones -->\r\n  <!-- <div class=\"drop-zones\"> -->\r\n  <!-- North: Prompts -->\r\n\r\n  <div id=\"parent-box\">\r\n    <div id=\"box1\">\r\n      <div\r\n        class=\"drop-zone north-zone prompts-zone\"\r\n        [class.has-nodes]=\"promptNodes.length > 0\"\r\n        [class.collapsed]=\"!isZoneExpanded('prompt')\"\r\n        (dragover)=\"onDragOver($event, 'prompt')\"\r\n        (drop)=\"onDrop($event, 'prompt')\"\r\n      >\r\n        <div class=\"zone-header\" (click)=\"toggleZone('prompt')\">\r\n          <div class=\"header-content\">\r\n            <div class=\"header-icon\">\r\n              <svg\r\n                width=\"45\"\r\n                height=\"44\"\r\n                viewBox=\"0 0 45 44\"\r\n                fill=\"none\"\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n              >\r\n                <rect\r\n                  x=\"0.679688\"\r\n                  width=\"44\"\r\n                  height=\"44\"\r\n                  rx=\"8\"\r\n                  fill=\"#5082EF\"\r\n                />\r\n                <path\r\n                  d=\"M20.6166 25.5C20.5273 25.1539 20.347 24.8381 20.0942 24.5854C19.8415 24.3327 19.5257 24.1523 19.1796 24.063L13.0446 22.481C12.94 22.4513 12.8478 22.3883 12.7822 22.3014C12.7166 22.2146 12.6812 22.1088 12.6812 22C12.6812 21.8912 12.7166 21.7854 12.7822 21.6986C12.8478 21.6118 12.94 21.5487 13.0446 21.519L19.1796 19.936C19.5256 19.8468 19.8413 19.6666 20.094 19.414C20.3467 19.1615 20.5272 18.8459 20.6166 18.5L22.1986 12.365C22.228 12.2599 22.291 12.1673 22.3779 12.1014C22.4649 12.0355 22.571 11.9998 22.6801 11.9998C22.7892 11.9998 22.8954 12.0355 22.9823 12.1014C23.0692 12.1673 23.1322 12.2599 23.1616 12.365L24.7426 18.5C24.8319 18.8461 25.0123 19.1619 25.265 19.4146C25.5177 19.6673 25.8336 19.8477 26.1796 19.937L32.3146 21.518C32.4201 21.5471 32.5132 21.61 32.5795 21.6971C32.6458 21.7841 32.6817 21.8906 32.6817 22C32.6817 22.1094 32.6458 22.2159 32.5795 22.3029C32.5132 22.39 32.4201 22.4529 32.3146 22.482L26.1796 24.063C25.8336 24.1523 25.5177 24.3327 25.265 24.5854C25.0123 24.8381 24.8319 25.1539 24.7426 25.5L23.1606 31.635C23.1312 31.7401 23.0682 31.8327 22.9813 31.8986C22.8944 31.9646 22.7882 32.0003 22.6791 32.0003C22.57 32.0003 22.4639 31.9646 22.3769 31.8986C22.29 31.8327 22.227 31.7401 22.1976 31.635L20.6166 25.5Z\"\r\n                  stroke=\"white\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n                <path\r\n                  d=\"M30.6797 13V17\"\r\n                  stroke=\"white\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n                <path\r\n                  d=\"M32.6797 15H28.6797\"\r\n                  stroke=\"white\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n                <path\r\n                  d=\"M14.6797 27V29\"\r\n                  stroke=\"white\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n                <path\r\n                  d=\"M15.6797 28H13.6797\"\r\n                  stroke=\"white\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n              </svg>\r\n            </div>\r\n            <h3 class=\"zone-title\">System Prompt</h3>\r\n          </div>\r\n          <div class=\"header-actions\">\r\n            <span class=\"required-badge\">Required</span>\r\n            <button\r\n              class=\"accordion-toggle\"\r\n              type=\"button\"\r\n              (click)=\"toggleZone('prompt'); $event.stopPropagation()\"\r\n            >\r\n              <svg\r\n                width=\"16\"\r\n                height=\"16\"\r\n                viewBox=\"0 0 24 24\"\r\n                fill=\"none\"\r\n                [style.transform]=\"\r\n                  isZoneExpanded('prompt') ? 'rotate(180deg)' : 'rotate(0deg)'\r\n                \"\r\n              >\r\n                <path\r\n                  d=\"M6 9L12 15L18 9\"\r\n                  stroke=\"currentColor\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n              </svg>\r\n            </button>\r\n          </div>\r\n        </div>\r\n        <div class=\"zone-content\" *ngIf=\"isZoneExpanded('prompt')\">\r\n          <div *ngIf=\"promptNodes.length === 0\" class=\"empty-state\">\r\n            Drop a prompt here\r\n          </div>\r\n          <div class=\"nodes-list\">\r\n            <div\r\n              *ngFor=\"let node of promptNodes; trackBy: trackByNodeId\"\r\n              class=\"kanban-card\"\r\n            >\r\n              <span class=\"card-title\">{{ node.data?.name || \"Prompt\" }}</span>\r\n              <button class=\"delete-btn\" (click)=\"onDeleteNode(node.id)\">\r\n                ×\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <!-- West: Knowledge -->\r\n      <div\r\n        class=\"drop-zone west-zone knowledge-zone\"\r\n        [class.has-nodes]=\"knowledgeNodes.length > 0\"\r\n        [class.collapsed]=\"!isZoneExpanded('knowledge')\"\r\n        (dragover)=\"onDragOver($event, 'knowledge')\"\r\n        (drop)=\"onDrop($event, 'knowledge')\"\r\n      >\r\n        <div class=\"zone-header\" (click)=\"toggleZone('knowledge')\">\r\n          <div class=\"header-content\">\r\n            <div class=\"header-icon\">\r\n              <svg\r\n                width=\"45\"\r\n                height=\"44\"\r\n                viewBox=\"0 0 45 44\"\r\n                fill=\"none\"\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n              >\r\n                <rect\r\n                  x=\"0.679688\"\r\n                  width=\"44\"\r\n                  height=\"44\"\r\n                  rx=\"8\"\r\n                  fill=\"#308666\"\r\n                />\r\n                <path\r\n                  d=\"M22.6797 17V31\"\r\n                  stroke=\"white\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n                <path\r\n                  d=\"M26.6797 22H28.6797\"\r\n                  stroke=\"white\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n                <path\r\n                  d=\"M26.6797 18H28.6797\"\r\n                  stroke=\"white\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n                <path\r\n                  d=\"M13.6797 28C13.4145 28 13.1601 27.8946 12.9726 27.7071C12.785 27.5196 12.6797 27.2652 12.6797 27V14C12.6797 13.7348 12.785 13.4804 12.9726 13.2929C13.1601 13.1054 13.4145 13 13.6797 13H18.6797C19.7406 13 20.758 13.4214 21.5081 14.1716C22.2583 14.9217 22.6797 15.9391 22.6797 17C22.6797 15.9391 23.1011 14.9217 23.8513 14.1716C24.6014 13.4214 25.6188 13 26.6797 13H31.6797C31.9449 13 32.1993 13.1054 32.3868 13.2929C32.5743 13.4804 32.6797 13.7348 32.6797 14V27C32.6797 27.2652 32.5743 27.5196 32.3868 27.7071C32.1993 27.8946 31.9449 28 31.6797 28H25.6797C24.884 28 24.121 28.3161 23.5584 28.8787C22.9958 29.4413 22.6797 30.2044 22.6797 31C22.6797 30.2044 22.3636 29.4413 21.801 28.8787C21.2384 28.3161 20.4753 28 19.6797 28H13.6797Z\"\r\n                  stroke=\"white\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n                <path\r\n                  d=\"M16.6797 22H18.6797\"\r\n                  stroke=\"white\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n                <path\r\n                  d=\"M16.6797 18H18.6797\"\r\n                  stroke=\"white\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n              </svg>\r\n            </div>\r\n            <h3 class=\"zone-title\">Knowledgebase</h3>\r\n          </div>\r\n          <div class=\"header-actions\">\r\n            <span class=\"optional-badge\">Optional</span>\r\n            <button\r\n              class=\"accordion-toggle\"\r\n              type=\"button\"\r\n              (click)=\"toggleZone('knowledge'); $event.stopPropagation()\"\r\n            >\r\n              <svg\r\n                width=\"16\"\r\n                height=\"16\"\r\n                viewBox=\"0 0 24 24\"\r\n                fill=\"none\"\r\n                [style.transform]=\"\r\n                  isZoneExpanded('knowledge')\r\n                    ? 'rotate(180deg)'\r\n                    : 'rotate(0deg)'\r\n                \"\r\n              >\r\n                <path\r\n                  d=\"M6 9L12 15L18 9\"\r\n                  stroke=\"currentColor\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n              </svg>\r\n            </button>\r\n          </div>\r\n        </div>\r\n        <div class=\"zone-content\" *ngIf=\"isZoneExpanded('knowledge')\">\r\n          <div *ngIf=\"knowledgeNodes.length === 0\" class=\"empty-state\">\r\n            Drop knowledge here\r\n          </div>\r\n          <div class=\"nodes-list\">\r\n            <div\r\n              *ngFor=\"let node of knowledgeNodes; trackBy: trackByNodeId\"\r\n              class=\"kanban-card\"\r\n            >\r\n              <span class=\"card-title\">{{\r\n                node.data?.name || \"Knowledge\"\r\n              }}</span>\r\n              <button class=\"delete-btn\" (click)=\"onDeleteNode(node.id)\">\r\n                ×\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <!-- <div class=\"central-progress\">\r\n        <div class=\"progress-ring\">\r\n          <svg class=\"progress-circle\" width=\"120\" height=\"120\">\r\n            <circle\r\n              class=\"progress-background\"\r\n              cx=\"60\"\r\n              cy=\"60\"\r\n              r=\"50\"\r\n              fill=\"none\"\r\n              stroke=\"#e5e7eb\"\r\n              stroke-width=\"8\"\r\n            />\r\n            <circle\r\n              class=\"progress-bar\"\r\n              cx=\"60\"\r\n              cy=\"60\"\r\n              r=\"50\"\r\n              fill=\"none\"\r\n              stroke=\"#3b82f6\"\r\n              stroke-width=\"8\"\r\n              stroke-linecap=\"round\"\r\n              [style.stroke-dasharray]=\"314\"\r\n              [style.stroke-dashoffset]=\"\r\n                314 - (314 * completionPercentage) / 100\r\n              \"\r\n              transform=\"rotate(-90 60 60)\"\r\n            />\r\n          </svg>\r\n          <div class=\"progress-content\">\r\n            <div class=\"progress-percentage\">{{ completionPercentage }}%</div>\r\n            <div class=\"progress-label\">Complete</div>\r\n          </div>\r\n        </div>\r\n      </div> -->\r\n    </div>\r\n    <div id=\"box2\">\r\n      <!-- East: Models -->\r\n      <div\r\n        class=\"drop-zone east-zone models-zone\"\r\n        [class.has-nodes]=\"modelNodes.length > 0\"\r\n        [class.collapsed]=\"!isZoneExpanded('model')\"\r\n        (dragover)=\"onDragOver($event, 'model')\"\r\n        (drop)=\"onDrop($event, 'model')\"\r\n      >\r\n        <div class=\"zone-header\" (click)=\"toggleZone('model')\">\r\n          <div class=\"header-content\">\r\n            <div class=\"header-icon\">\r\n              <svg\r\n                width=\"45\"\r\n                height=\"44\"\r\n                viewBox=\"0 0 45 44\"\r\n                fill=\"none\"\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n              >\r\n                <rect\r\n                  x=\"0.679688\"\r\n                  width=\"44\"\r\n                  height=\"44\"\r\n                  rx=\"8\"\r\n                  fill=\"#997BCF\"\r\n                />\r\n                <path\r\n                  d=\"M31.6797 18C31.6793 17.6493 31.5867 17.3048 31.4112 17.0012C31.2357 16.6975 30.9834 16.4454 30.6797 16.27L23.6797 12.27C23.3756 12.0945 23.0308 12.0021 22.6797 12.0021C22.3286 12.0021 21.9837 12.0945 21.6797 12.27L14.6797 16.27C14.376 16.4454 14.1237 16.6975 13.9481 17.0012C13.7726 17.3048 13.68 17.6493 13.6797 18V26C13.68 26.3508 13.7726 26.6952 13.9481 26.9989C14.1237 27.3025 14.376 27.5547 14.6797 27.73L21.6797 31.73C21.9837 31.9056 22.3286 31.998 22.6797 31.998C23.0308 31.998 23.3756 31.9056 23.6797 31.73L30.6797 27.73C30.9834 27.5547 31.2357 27.3025 31.4112 26.9989C31.5867 26.6952 31.6793 26.3508 31.6797 26V18Z\"\r\n                  stroke=\"white\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n                <path\r\n                  d=\"M13.9795 17L22.6795 22L31.3795 17\"\r\n                  stroke=\"white\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n                <path\r\n                  d=\"M22.6797 32V22\"\r\n                  stroke=\"white\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n              </svg>\r\n            </div>\r\n            <h3 class=\"zone-title\">AI Model</h3>\r\n          </div>\r\n          <div class=\"header-actions\">\r\n            <span class=\"required-badge\">Required</span>\r\n            <button\r\n              class=\"accordion-toggle\"\r\n              type=\"button\"\r\n              (click)=\"toggleZone('model'); $event.stopPropagation()\"\r\n            >\r\n              <svg\r\n                width=\"16\"\r\n                height=\"16\"\r\n                viewBox=\"0 0 24 24\"\r\n                fill=\"none\"\r\n                [style.transform]=\"\r\n                  isZoneExpanded('model') ? 'rotate(180deg)' : 'rotate(0deg)'\r\n                \"\r\n              >\r\n                <path\r\n                  d=\"M6 9L12 15L18 9\"\r\n                  stroke=\"currentColor\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n              </svg>\r\n            </button>\r\n          </div>\r\n        </div>\r\n        <div class=\"zone-content\" *ngIf=\"isZoneExpanded('model')\">\r\n          <div *ngIf=\"modelNodes.length === 0\" class=\"empty-state\">\r\n            Drop a model here\r\n          </div>\r\n          <div class=\"nodes-list\">\r\n            <div\r\n              *ngFor=\"let node of modelNodes; trackBy: trackByNodeId\"\r\n              class=\"kanban-card\"\r\n            >\r\n              <span class=\"card-title\">{{ node.data?.name || \"Model\" }}</span>\r\n              <button class=\"delete-btn\" (click)=\"onDeleteNode(node.id)\">\r\n                ×\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- South: Guardrails/Tools -->\r\n      <div\r\n        class=\"drop-zone south-zone\"\r\n        [class.guardrails-zone]=\"config.agentType === 'individual'\"\r\n        [class.tools-zone]=\"config.agentType === 'collaborative'\"\r\n        [class.has-nodes]=\"bottomNodes.length > 0\"\r\n        [class.collapsed]=\"\r\n          !isZoneExpanded(\r\n            config.agentType === 'individual' ? 'guardrail' : 'tool'\r\n          )\r\n        \"\r\n        (dragover)=\"\r\n          onDragOver(\r\n            $event,\r\n            config.agentType === 'individual' ? 'guardrail' : 'tool'\r\n          )\r\n        \"\r\n        (drop)=\"\r\n          onDrop(\r\n            $event,\r\n            config.agentType === 'individual' ? 'guardrail' : 'tool'\r\n          )\r\n        \"\r\n      >\r\n        <div\r\n          class=\"zone-header\"\r\n          (click)=\"\r\n            toggleZone(config.agentType === 'individual' ? 'guardrail' : 'tool')\r\n          \"\r\n        >\r\n          <div class=\"header-content\">\r\n            <div class=\"header-icon\">\r\n              <svg\r\n                width=\"45\"\r\n                height=\"44\"\r\n                viewBox=\"0 0 45 44\"\r\n                fill=\"none\"\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                *ngIf=\"config.agentType === 'individual'\"\r\n              >\r\n                <rect\r\n                  x=\"0.679688\"\r\n                  width=\"44\"\r\n                  height=\"44\"\r\n                  rx=\"8\"\r\n                  fill=\"#DC2626\"\r\n                />\r\n                <path\r\n                  d=\"M30.6797 23C30.6797 28 27.1797 30.5 23.0197 31.95C22.8019 32.0238 22.5652 32.0202 22.3497 31.94C18.1797 30.5 14.6797 28 14.6797 23V16C14.6797 15.7347 14.785 15.4804 14.9726 15.2929C15.1601 15.1053 15.4145 15 15.6797 15C17.6797 15 20.1797 13.8 21.9197 12.28C22.1315 12.099 22.401 11.9995 22.6797 11.9995C22.9583 11.9995 23.2278 12.099 23.4397 12.28C25.1897 13.81 27.6797 15 29.6797 15C29.9449 15 30.1993 15.1053 30.3868 15.2929C30.5743 15.4804 30.6797 15.7347 30.6797 16V23Z\"\r\n                  stroke=\"white\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n              </svg>\r\n\r\n              <svg\r\n                width=\"45\"\r\n                height=\"44\"\r\n                viewBox=\"0 0 45 44\"\r\n                fill=\"none\"\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                *ngIf=\"config.agentType === 'collaborative'\"\r\n              >\r\n                <rect\r\n                  x=\"0.679688\"\r\n                  width=\"44\"\r\n                  height=\"44\"\r\n                  rx=\"8\"\r\n                  fill=\"#D97706\"\r\n                />\r\n                <path\r\n                  d=\"M25.3795 16.3C25.1963 16.4869 25.0937 16.7382 25.0937 17C25.0937 17.2617 25.1963 17.5131 25.3795 17.7L26.9795 19.3C27.1665 19.4832 27.4178 19.5858 27.6795 19.5858C27.9413 19.5858 28.1926 19.4832 28.3795 19.3L32.1495 15.53C32.6524 16.6412 32.8046 17.8792 32.586 19.0791C32.3674 20.279 31.7883 21.3838 30.9258 22.2463C30.0634 23.1087 28.9586 23.6878 27.7587 23.9064C26.5588 24.1251 25.3207 23.9728 24.2095 23.47L17.2995 30.38C16.9017 30.7778 16.3622 31.0013 15.7995 31.0013C15.2369 31.0013 14.6974 30.7778 14.2995 30.38C13.9017 29.9822 13.6782 29.4426 13.6782 28.88C13.6782 28.3174 13.9017 27.7778 14.2995 27.38L21.2095 20.47C20.7067 19.3588 20.5545 18.1207 20.7731 16.9208C20.9917 15.7209 21.5708 14.6161 22.4333 13.7537C23.2957 12.8913 24.4005 12.3121 25.6004 12.0935C26.8003 11.8749 28.0384 12.0271 29.1495 12.53L25.3895 16.29L25.3795 16.3Z\"\r\n                  stroke=\"white\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n              </svg>\r\n            </div>\r\n            <h3 class=\"zone-title\">\r\n              {{ config.agentType === \"individual\" ? \"Guardrails\" : \"Tools\" }}\r\n            </h3>\r\n          </div>\r\n          <div class=\"header-actions\">\r\n            <span class=\"optional-badge\">Optional</span>\r\n            <button\r\n              class=\"accordion-toggle\"\r\n              type=\"button\"\r\n              (click)=\"\r\n                toggleZone(\r\n                  config.agentType === 'individual' ? 'guardrail' : 'tool'\r\n                );\r\n                $event.stopPropagation()\r\n              \"\r\n            >\r\n              <svg\r\n                width=\"16\"\r\n                height=\"16\"\r\n                viewBox=\"0 0 24 24\"\r\n                fill=\"none\"\r\n                [style.transform]=\"\r\n                  isZoneExpanded(\r\n                    config.agentType === 'individual' ? 'guardrail' : 'tool'\r\n                  )\r\n                    ? 'rotate(180deg)'\r\n                    : 'rotate(0deg)'\r\n                \"\r\n              >\r\n                <path\r\n                  d=\"M6 9L12 15L18 9\"\r\n                  stroke=\"currentColor\"\r\n                  stroke-width=\"2\"\r\n                  stroke-linecap=\"round\"\r\n                  stroke-linejoin=\"round\"\r\n                />\r\n              </svg>\r\n            </button>\r\n          </div>\r\n        </div>\r\n        <div\r\n          class=\"zone-content\"\r\n          *ngIf=\"\r\n            isZoneExpanded(\r\n              config.agentType === 'individual' ? 'guardrail' : 'tool'\r\n            )\r\n          \"\r\n        >\r\n          <div *ngIf=\"bottomNodes.length === 0\" class=\"empty-state\">\r\n            Drop\r\n            {{ config.agentType === \"individual\" ? \"guardrails\" : \"tools\" }}\r\n            here\r\n          </div>\r\n          <div class=\"nodes-list\">\r\n            <div\r\n              *ngFor=\"let node of bottomNodes; trackBy: trackByNodeId\"\r\n              class=\"kanban-card\"\r\n              [class.guardrail-card]=\"config.agentType === 'individual'\"\r\n            >\r\n              <div class=\"card-content\">\r\n                <span class=\"card-title\">{{\r\n                  node.data?.name ||\r\n                    (config.agentType === \"individual\" ? \"Guardrail\" : \"Tool\")\r\n                }}</span>\r\n\r\n                <!-- Toggle switch for guardrails only -->\r\n                <div\r\n                  *ngIf=\"config.agentType === 'individual'\"\r\n                  class=\"guardrail-toggle\"\r\n                >\r\n                  <label class=\"toggle-switch\">\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      [checked]=\"isGuardrailEnabled(node.id)\"\r\n                      (change)=\"onGuardrailCheckboxChange($event, node.id)\"\r\n                    />\r\n                    <span class=\"toggle-slider\"></span>\r\n                  </label>\r\n                </div>\r\n                <button\r\n                  *ngIf=\"config.agentType === 'individual'\"\r\n                  class=\"delete-btn\"\r\n                  (click)=\"onDeleteNode(node.id)\"\r\n                >\r\n                  ×\r\n                </button>\r\n              </div>\r\n              <button\r\n                *ngIf=\"config.agentType === 'collaborative'\"\r\n                class=\"delete-btn\"\r\n                (click)=\"onDeleteNode(node.id)\"\r\n              >\r\n                ×\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- </div> -->\r\n</div>\r\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAIEC,YAAY,QAGP,eAAe;;;;;;ICwIZC,EAAA,CAAAC,cAAA,cAA0D;IACxDD,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAMFH,EAJF,CAAAC,cAAA,cAGC,eAC0B;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjEH,EAAA,CAAAC,cAAA,iBAA2D;IAAhCD,EAAA,CAAAI,UAAA,mBAAAC,sEAAA;MAAA,MAAAC,OAAA,GAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAG,YAAA,CAAAP,OAAA,CAAAQ,EAAA,CAAqB;IAAA,EAAC;IACxDd,EAAA,CAAAE,MAAA,eACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;IAJqBH,EAAA,CAAAe,SAAA,GAAiC;IAAjCf,EAAA,CAAAgB,iBAAA,EAAAV,OAAA,CAAAW,IAAA,kBAAAX,OAAA,CAAAW,IAAA,CAAAC,IAAA,cAAiC;;;;;IAThElB,EAAA,CAAAC,cAAA,cAA2D;IACzDD,EAAA,CAAAmB,UAAA,IAAAC,6CAAA,kBAA0D;IAG1DpB,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAmB,UAAA,IAAAE,6CAAA,kBAGC;IAOLrB,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAdEH,EAAA,CAAAe,SAAA,EAA8B;IAA9Bf,EAAA,CAAAsB,UAAA,SAAAZ,MAAA,CAAAa,WAAA,CAAAC,MAAA,OAA8B;IAKfxB,EAAA,CAAAe,SAAA,GAAgB;IAAAf,EAAhB,CAAAsB,UAAA,YAAAZ,MAAA,CAAAa,WAAA,CAAgB,iBAAAb,MAAA,CAAAe,aAAA,CAAsB;;;;;IAgH3DzB,EAAA,CAAAC,cAAA,cAA6D;IAC3DD,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAMFH,EAJF,CAAAC,cAAA,cAGC,eAC0B;IAAAD,EAAA,CAAAE,MAAA,GAEvB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACTH,EAAA,CAAAC,cAAA,iBAA2D;IAAhCD,EAAA,CAAAI,UAAA,mBAAAsB,sEAAA;MAAA,MAAAC,OAAA,GAAA3B,EAAA,CAAAO,aAAA,CAAAqB,GAAA,EAAAnB,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAG,YAAA,CAAAc,OAAA,CAAAb,EAAA,CAAqB;IAAA,EAAC;IACxDd,EAAA,CAAAE,MAAA,eACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;IANqBH,EAAA,CAAAe,SAAA,GAEvB;IAFuBf,EAAA,CAAAgB,iBAAA,EAAAW,OAAA,CAAAV,IAAA,kBAAAU,OAAA,CAAAV,IAAA,CAAAC,IAAA,iBAEvB;;;;;IAXRlB,EAAA,CAAAC,cAAA,cAA8D;IAC5DD,EAAA,CAAAmB,UAAA,IAAAU,6CAAA,kBAA6D;IAG7D7B,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAmB,UAAA,IAAAW,6CAAA,kBAGC;IASL9B,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAhBEH,EAAA,CAAAe,SAAA,EAAiC;IAAjCf,EAAA,CAAAsB,UAAA,SAAAZ,MAAA,CAAAqB,cAAA,CAAAP,MAAA,OAAiC;IAKlBxB,EAAA,CAAAe,SAAA,GAAmB;IAAAf,EAAnB,CAAAsB,UAAA,YAAAZ,MAAA,CAAAqB,cAAA,CAAmB,iBAAArB,MAAA,CAAAe,aAAA,CAAsB;;;;;IA+H9DzB,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAMFH,EAJF,CAAAC,cAAA,cAGC,eAC0B;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChEH,EAAA,CAAAC,cAAA,iBAA2D;IAAhCD,EAAA,CAAAI,UAAA,mBAAA4B,sEAAA;MAAA,MAAAC,OAAA,GAAAjC,EAAA,CAAAO,aAAA,CAAA2B,GAAA,EAAAzB,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAG,YAAA,CAAAoB,OAAA,CAAAnB,EAAA,CAAqB;IAAA,EAAC;IACxDd,EAAA,CAAAE,MAAA,eACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;IAJqBH,EAAA,CAAAe,SAAA,GAAgC;IAAhCf,EAAA,CAAAgB,iBAAA,EAAAiB,OAAA,CAAAhB,IAAA,kBAAAgB,OAAA,CAAAhB,IAAA,CAAAC,IAAA,aAAgC;;;;;IAT/DlB,EAAA,CAAAC,cAAA,cAA0D;IACxDD,EAAA,CAAAmB,UAAA,IAAAgB,6CAAA,kBAAyD;IAGzDnC,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAmB,UAAA,IAAAiB,6CAAA,kBAGC;IAOLpC,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAdEH,EAAA,CAAAe,SAAA,EAA6B;IAA7Bf,EAAA,CAAAsB,UAAA,SAAAZ,MAAA,CAAA2B,UAAA,CAAAb,MAAA,OAA6B;IAKdxB,EAAA,CAAAe,SAAA,GAAe;IAAAf,EAAf,CAAAsB,UAAA,YAAAZ,MAAA,CAAA2B,UAAA,CAAe,iBAAA3B,MAAA,CAAAe,aAAA,CAAsB;;;;;;IA4CtDzB,EAAA,CAAAC,cAAA,cAOC;IAQCD,EAPA,CAAAsC,SAAA,eAME,eAOA;IACJtC,EAAA,CAAAG,YAAA,EAAM;;;;;;IAENH,EAAA,CAAAC,cAAA,cAOC;IAQCD,EAPA,CAAAsC,SAAA,eAME,eAOA;IACJtC,EAAA,CAAAG,YAAA,EAAM;;;;;IAkDVH,EAAA,CAAAC,cAAA,cAA0D;IACxDD,EAAA,CAAAE,MAAA,GAGF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAHJH,EAAA,CAAAe,SAAA,EAGF;IAHEf,EAAA,CAAAuC,kBAAA,WAAA7B,MAAA,CAAA8B,MAAA,CAAAC,SAAA,qDAGF;;;;;;IAmBUzC,EALJ,CAAAC,cAAA,cAGC,gBAC8B,gBAKzB;IADAD,EAAA,CAAAI,UAAA,oBAAAsC,4EAAAC,MAAA;MAAA3C,EAAA,CAAAO,aAAA,CAAAqC,GAAA;MAAA,MAAAC,OAAA,GAAA7C,EAAA,CAAAW,aAAA,GAAAF,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAUF,MAAA,CAAAoC,yBAAA,CAAAH,MAAA,EAAAE,OAAA,CAAA/B,EAAA,CAA0C;IAAA,EAAC;IAHvDd,EAAA,CAAAG,YAAA,EAIE;IACFH,EAAA,CAAAsC,SAAA,eAAmC;IAEvCtC,EADE,CAAAG,YAAA,EAAQ,EACJ;;;;;IALAH,EAAA,CAAAe,SAAA,GAAuC;IAAvCf,EAAA,CAAAsB,UAAA,YAAAZ,MAAA,CAAAqC,kBAAA,CAAAF,OAAA,CAAA/B,EAAA,EAAuC;;;;;;IAM7Cd,EAAA,CAAAC,cAAA,iBAIC;IADCD,EAAA,CAAAI,UAAA,mBAAA4C,+EAAA;MAAAhD,EAAA,CAAAO,aAAA,CAAA0C,IAAA;MAAA,MAAAJ,OAAA,GAAA7C,EAAA,CAAAW,aAAA,GAAAF,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAG,YAAA,CAAAgC,OAAA,CAAA/B,EAAA,CAAqB;IAAA,EAAC;IAE/Bd,EAAA,CAAAE,MAAA,eACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAEXH,EAAA,CAAAC,cAAA,iBAIC;IADCD,EAAA,CAAAI,UAAA,mBAAA8C,+EAAA;MAAAlD,EAAA,CAAAO,aAAA,CAAA4C,IAAA;MAAA,MAAAN,OAAA,GAAA7C,EAAA,CAAAW,aAAA,GAAAF,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAG,YAAA,CAAAgC,OAAA,CAAA/B,EAAA,CAAqB;IAAA,EAAC;IAE/Bd,EAAA,CAAAE,MAAA,eACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAjCPH,EANJ,CAAAC,cAAA,cAIC,cAC2B,eACC;IAAAD,EAAA,CAAAE,MAAA,GAGvB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAgBTH,EAbA,CAAAmB,UAAA,IAAAiC,mDAAA,kBAGC,IAAAC,sDAAA,qBAcA;IAGHrD,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAmB,UAAA,IAAAmC,sDAAA,qBAIC;IAGHtD,EAAA,CAAAG,YAAA,EAAM;;;;;IArCJH,EAAA,CAAAuD,WAAA,mBAAA7C,MAAA,CAAA8B,MAAA,CAAAC,SAAA,kBAA0D;IAG/BzC,EAAA,CAAAe,SAAA,GAGvB;IAHuBf,EAAA,CAAAgB,iBAAA,EAAA6B,OAAA,CAAA5B,IAAA,kBAAA4B,OAAA,CAAA5B,IAAA,CAAAC,IAAA,MAAAR,MAAA,CAAA8B,MAAA,CAAAC,SAAA,0CAGvB;IAICzC,EAAA,CAAAe,SAAA,EAAuC;IAAvCf,EAAA,CAAAsB,UAAA,SAAAZ,MAAA,CAAA8B,MAAA,CAAAC,SAAA,kBAAuC;IAavCzC,EAAA,CAAAe,SAAA,EAAuC;IAAvCf,EAAA,CAAAsB,UAAA,SAAAZ,MAAA,CAAA8B,MAAA,CAAAC,SAAA,kBAAuC;IAQzCzC,EAAA,CAAAe,SAAA,EAA0C;IAA1Cf,EAAA,CAAAsB,UAAA,SAAAZ,MAAA,CAAA8B,MAAA,CAAAC,SAAA,qBAA0C;;;;;IAhDnDzC,EAAA,CAAAC,cAAA,cAOC;IACCD,EAAA,CAAAmB,UAAA,IAAAqC,6CAAA,kBAA0D;IAK1DxD,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAmB,UAAA,IAAAsC,6CAAA,kBAIC;IAsCLzD,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAhDEH,EAAA,CAAAe,SAAA,EAA8B;IAA9Bf,EAAA,CAAAsB,UAAA,SAAAZ,MAAA,CAAAgD,WAAA,CAAAlC,MAAA,OAA8B;IAOfxB,EAAA,CAAAe,SAAA,GAAgB;IAAAf,EAAhB,CAAAsB,UAAA,YAAAZ,MAAA,CAAAgD,WAAA,CAAgB,iBAAAhD,MAAA,CAAAe,aAAA,CAAsB;;;AD9frE,WAAakC,uBAAuB;EAA9B,MAAOA,uBAAuB;IACJC,YAAY;IAEjCpB,MAAM;IACNqB,KAAK,GAAiB,EAAE;IAEvBC,WAAW,GAAG,IAAI/D,YAAY,EAIpC;IACMgE,WAAW,GAAG,IAAIhE,YAAY,EAAU;IACxCiE,gBAAgB,GAAG,IAAIjE,YAAY,EAGzC;IAEJ;IACAkE,UAAU,GAAG;MACXC,MAAM,EAAE,IAAI;MAAE;MACdC,KAAK,EAAE,IAAI;MAAE;MACbC,SAAS,EAAE,IAAI;MAAE;MACjBC,SAAS,EAAE,IAAI;MAAE;MACjBC,IAAI,EAAE,IAAI,CAAE;KACb;IAED;IACAC,eAAe,GAAkC,EAAE;IAEnD;IACA,IAAIhD,WAAWA,CAAA;MACb,OAAO,IAAI,CAACsC,KAAK,CAACW,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACxD,IAAI,EAAEyD,IAAI,KAAK,QAAQ,CAAC;IAClE;IAEA,IAAIrC,UAAUA,CAAA;MACZ,OAAO,IAAI,CAACwB,KAAK,CAACW,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACxD,IAAI,EAAEyD,IAAI,KAAK,OAAO,CAAC;IACjE;IAEA,IAAI3C,cAAcA,CAAA;MAChB,OAAO,IAAI,CAAC8B,KAAK,CAACW,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACxD,IAAI,EAAEyD,IAAI,KAAK,WAAW,CAAC;IACrE;IAEA,IAAIhB,WAAWA,CAAA;MACb,IAAI,IAAI,CAAClB,MAAM,CAACC,SAAS,KAAK,YAAY,EAAE;QAC1C,OAAO,IAAI,CAACoB,KAAK,CAACW,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACxD,IAAI,EAAEyD,IAAI,KAAK,WAAW,CAAC;MACrE,CAAC,MAAM;QACL,OAAO,IAAI,CAACb,KAAK,CAACW,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACxD,IAAI,EAAEyD,IAAI,KAAK,MAAM,CAAC;MAChE;IACF;IAEA;IACA,IAAIC,oBAAoBA,CAAA;MACtB,IAAIC,SAAS,GAAG,CAAC;MACjB,IAAIC,KAAK,GAAG,CAAC,CAAC,CAAC;MAEf;MACA,IAAI,IAAI,CAACtD,WAAW,CAACC,MAAM,GAAG,CAAC,EAAEoD,SAAS,EAAE;MAC5C,IAAI,IAAI,CAACvC,UAAU,CAACb,MAAM,GAAG,CAAC,EAAEoD,SAAS,EAAE;MAE3C;MAEA,OAAOE,IAAI,CAACC,KAAK,CAAEH,SAAS,GAAGC,KAAK,GAAI,GAAG,CAAC;IAC9C;IAEAG,UAAUA,CAACC,KAAgB,EAAEC,QAAgB;MAC3CD,KAAK,CAACE,cAAc,EAAE;MACtB;MACA,MAAMC,QAAQ,GACZH,KAAK,CAACI,YAAY,EAAEC,OAAO,CAAC,uBAAuB,CAAC,IACpDL,KAAK,CAACI,YAAY,EAAEC,OAAO,CAAC,YAAY,CAAC;MAC3C,IAAIF,QAAQ,EAAE;QACZ,IAAI;UACF,MAAMnE,IAAI,GAAGsE,IAAI,CAACC,KAAK,CAACJ,QAAQ,CAAC;UACjC;UACA,IAAI,IAAI,CAACK,oBAAoB,CAACxE,IAAI,EAAEiE,QAAQ,CAAC,EAAE;YAC7CD,KAAK,CAACI,YAAa,CAACK,UAAU,GAAG,MAAM;UACzC,CAAC,MAAM;YACLT,KAAK,CAACI,YAAa,CAACK,UAAU,GAAG,MAAM;UACzC;QACF,CAAC,CAAC,OAAOC,CAAC,EAAE;UACVV,KAAK,CAACI,YAAa,CAACK,UAAU,GAAG,MAAM;QACzC;MACF;IACF;IAEAE,MAAMA,CAACX,KAAgB,EAAEC,QAAgB;MACvCD,KAAK,CAACE,cAAc,EAAE;MACtB;MACA,MAAMC,QAAQ,GACZH,KAAK,CAACI,YAAY,EAAEC,OAAO,CAAC,uBAAuB,CAAC,IACpDL,KAAK,CAACI,YAAY,EAAEC,OAAO,CAAC,YAAY,CAAC;MAC3C,IAAIF,QAAQ,EAAE;QACZ,IAAI;UACF,MAAMnE,IAAI,GAAGsE,IAAI,CAACC,KAAK,CAACJ,QAAQ,CAAC;UAEjCS,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE;YAAE7E,IAAI;YAAEiE;UAAQ,CAAE,CAAC;UAE9D;UACA,IAAI,CAAC,IAAI,CAACO,oBAAoB,CAACxE,IAAI,EAAEiE,QAAQ,CAAC,EAAE;YAC9CW,OAAO,CAACC,GAAG,CACT,uCAAuC,EACvC7E,IAAI,CAACyD,IAAI,EACT,IAAI,EACJQ,QAAQ,CACT;YACD,OAAO,CAAC;UACV;UAEAW,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;UAEzC;UACA,IAAI,CAAC,IAAI,CAACC,YAAY,CAACb,QAAQ,CAAC,EAAE;YAChCW,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;YAC1D;UACF;UAEA;UACA,MAAME,IAAI,GAAIf,KAAK,CAACgB,aAAyB,CAACC,qBAAqB,EAAE;UACrE,MAAMC,QAAQ,GAAG;YACfC,CAAC,EAAEnB,KAAK,CAACoB,OAAO,GAAGL,IAAI,CAACM,IAAI;YAC5BC,CAAC,EAAEtB,KAAK,CAACuB,OAAO,GAAGR,IAAI,CAACS;WACzB;UAEDZ,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;UAC3C,IAAI,CAAChC,WAAW,CAAC4C,IAAI,CAAC;YACpBjC,IAAI,EAAExD,IAAI;YACV0F,IAAI,EAAEzB,QAAQ;YACdiB;WACD,CAAC;QACJ,CAAC,CAAC,OAAOR,CAAC,EAAE;UACVE,OAAO,CAACe,KAAK,CAAC,oBAAoB,EAAEjB,CAAC,CAAC;QACxC;MACF;IACF;IAEA9E,YAAYA,CAACgG,MAAc;MACzB;MACA,IAAI,CAACC,qBAAqB,CAACD,MAAM,CAAC;MAElC,IAAI,CAAC9C,WAAW,CAAC2C,IAAI,CAACG,MAAM,CAAC;IAC/B;IAEApF,aAAaA,CAACsF,KAAa,EAAEtC,IAAS;MACpC,OAAOA,IAAI,CAAC3D,EAAE;IAChB;IAEA;IACAkG,iBAAiBA,CAACH,MAAc,EAAEI,OAAgB;MAChD,IAAI,CAAC1C,eAAe,CAACsC,MAAM,CAAC,GAAGI,OAAO;MACtC,IAAI,CAACjD,gBAAgB,CAAC0C,IAAI,CAAC;QAAEG,MAAM;QAAEI;MAAO,CAAE,CAAC;IACjD;IAEAnE,yBAAyBA,CAACmC,KAAY,EAAE4B,MAAc;MACpD,MAAMK,QAAQ,GAAGjC,KAAK,CAACkC,MAA0B;MACjD,IAAID,QAAQ,EAAE;QACZ,IAAI,CAACF,iBAAiB,CAACH,MAAM,EAAEK,QAAQ,CAACE,OAAO,CAAC;MAClD;IACF;IAEArE,kBAAkBA,CAAC8D,MAAc;MAC/B;MACA,OAAO,IAAI,CAACtC,eAAe,CAACsC,MAAM,CAAC,KAAK,KAAK;IAC/C;IAEA;IACAQ,wBAAwBA,CAACR,MAAc,EAAEI,OAAA,GAAmB,IAAI;MAC9D,IAAI,IAAI,CAAC1C,eAAe,CAACsC,MAAM,CAAC,KAAKS,SAAS,EAAE;QAC9C,IAAI,CAAC/C,eAAe,CAACsC,MAAM,CAAC,GAAGI,OAAO;MACxC;IACF;IAEA;IACAH,qBAAqBA,CAACD,MAAc;MAClC,OAAO,IAAI,CAACtC,eAAe,CAACsC,MAAM,CAAC;IACrC;IAEA;IACAU,UAAUA,CAACrC,QAAgB;MACzB,IAAIA,QAAQ,KAAK,WAAW,IAAIA,QAAQ,KAAK,MAAM,EAAE;QACnD;QACA,MAAMsC,UAAU,GACd,IAAI,CAAChF,MAAM,CAACC,SAAS,KAAK,YAAY,GAAG,WAAW,GAAG,MAAM;QAC/D,IAAI,CAACwB,UAAU,CAACuD,UAA0C,CAAC,GACzD,CAAC,IAAI,CAACvD,UAAU,CAACuD,UAA0C,CAAC;MAChE,CAAC,MAAM;QACL,IAAI,CAACvD,UAAU,CAACiB,QAAwC,CAAC,GACvD,CAAC,IAAI,CAACjB,UAAU,CAACiB,QAAwC,CAAC;MAC9D;IACF;IAEA;IACAuC,cAAcA,CAACvC,QAAgB;MAC7B,IAAIA,QAAQ,KAAK,WAAW,IAAIA,QAAQ,KAAK,MAAM,EAAE;QACnD,MAAMsC,UAAU,GACd,IAAI,CAAChF,MAAM,CAACC,SAAS,KAAK,YAAY,GAAG,WAAW,GAAG,MAAM;QAC/D,OAAO,IAAI,CAACwB,UAAU,CAACuD,UAA0C,CAAC;MACpE;MACA,OAAO,IAAI,CAACvD,UAAU,CAACiB,QAAwC,CAAC;IAClE;IAEQO,oBAAoBA,CAACL,QAAa,EAAEF,QAAgB;MAC1D,MAAMwC,QAAQ,GAAGtC,QAAQ,CAACV,IAAI,IAAIU,QAAQ,CAACnE,IAAI,EAAEyD,IAAI;MAErD,QAAQQ,QAAQ;QACd,KAAK,QAAQ;UACX,OAAOwC,QAAQ,KAAK,QAAQ;QAC9B,KAAK,OAAO;UACV,OAAOA,QAAQ,KAAK,OAAO;QAC7B,KAAK,WAAW;UACd,OAAOA,QAAQ,KAAK,WAAW;QACjC,KAAK,WAAW;UACd,OAAOA,QAAQ,KAAK,WAAW;QACjC,KAAK,MAAM;UACT,OAAOA,QAAQ,KAAK,MAAM;QAC5B;UACE,OAAO,KAAK;MAChB;IACF;IAEQ3B,YAAYA,CAACb,QAAgB;MACnC,QAAQA,QAAQ;QACd,KAAK,QAAQ;UACX,OACE,IAAI,CAAC3D,WAAW,CAACC,MAAM,GAAG,IAAI,CAACgB,MAAM,CAACmF,iBAAiB,CAACC,OAAO,CAACC,GAAG;QAEvE,KAAK,OAAO;UACV,OACE,IAAI,CAACxF,UAAU,CAACb,MAAM,GAAG,IAAI,CAACgB,MAAM,CAACmF,iBAAiB,CAACG,MAAM,CAACD,GAAG;QAErE,KAAK,WAAW;UACd,OACE,IAAI,CAAC9F,cAAc,CAACP,MAAM,GAC1B,IAAI,CAACgB,MAAM,CAACmF,iBAAiB,CAACvD,SAAS,CAACyD,GAAG;QAE/C,KAAK,WAAW;UACd,OACE,IAAI,CAACnE,WAAW,CAAClC,MAAM,GAAG,IAAI,CAACgB,MAAM,CAACmF,iBAAiB,CAACI,UAAU,CAACF,GAAG;QAE1E,KAAK,MAAM;UACT,OACE,IAAI,CAACnE,WAAW,CAAClC,MAAM,GAAG,IAAI,CAACgB,MAAM,CAACmF,iBAAiB,CAACK,KAAK,CAACH,GAAG;QAErE;UACE,OAAO,KAAK;MAChB;IACF;;uCArPWlE,uBAAuB;IAAA;;YAAvBA,uBAAuB;MAAAsE,SAAA;MAAAC,cAAA,WAAAC,uCAAAC,EAAA,EAAAC,GAAA,EAAAC,QAAA;QAAA,IAAAF,EAAA;;;;;;;;;;;;;;;;;;;;;;UChChCpI,EAHJ,CAAAC,cAAA,aAAwC,aAER,aACD;;UAGrBD,EAFJ,CAAAC,cAAA,aAAsD,WAC9C,wBAOH;UAECD,EADA,CAAAsC,SAAA,cAAoD,cACF;UAEtDtC,EADE,CAAAG,YAAA,EAAiB,EACZ;UAUPH,EATA,CAAAsC,SAAA,gBAQE,gBAaA;UACJtC,EAAA,CAAAG,YAAA,EAAM;;UAEJH,EADF,CAAAC,cAAA,cAA8B,eACK;UAAAD,EAAA,CAAAE,MAAA,IAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAClEH,EAAA,CAAAC,cAAA,eAA4B;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAG1CF,EAH0C,CAAAG,YAAA,EAAM,EACtC,EACF,EACF;UAQFH,EAFJ,CAAAC,cAAA,eAAqB,eACJ,eAOZ;UADCD,EADA,CAAAI,UAAA,sBAAAmI,0DAAA5F,MAAA;YAAA,OAAY0F,GAAA,CAAArD,UAAA,CAAArC,MAAA,EAAmB,QAAQ,CAAC;UAAA,EAAC,kBAAA6F,sDAAA7F,MAAA;YAAA,OACjC0F,GAAA,CAAAzC,MAAA,CAAAjD,MAAA,EAAe,QAAQ,CAAC;UAAA,EAAC;UAEjC3C,EAAA,CAAAC,cAAA,eAAwD;UAA/BD,EAAA,CAAAI,UAAA,mBAAAqI,uDAAA;YAAA,OAASJ,GAAA,CAAAd,UAAA,CAAW,QAAQ,CAAC;UAAA,EAAC;UAEnDvH,EADF,CAAAC,cAAA,eAA4B,eACD;;UACvBD,EAAA,CAAAC,cAAA,eAMC;UAoCCD,EAnCA,CAAAsC,SAAA,gBAME,gBAOA,gBAOA,gBAOA,gBAOA,gBAOA;UAENtC,EADE,CAAAG,YAAA,EAAM,EACF;;UACNH,EAAA,CAAAC,cAAA,cAAuB;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UACtCF,EADsC,CAAAG,YAAA,EAAK,EACrC;UAEJH,EADF,CAAAC,cAAA,eAA4B,gBACG;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5CH,EAAA,CAAAC,cAAA,kBAIC;UADCD,EAAA,CAAAI,UAAA,mBAAAsI,0DAAA/F,MAAA;YAAS0F,GAAA,CAAAd,UAAA,CAAW,QAAQ,CAAC;YAAA,OAAE5E,MAAA,CAAAgG,eAAA,EAAwB;UAAA,EAAC;;UAExD3I,EAAA,CAAAC,cAAA,eAQC;UACCD,EAAA,CAAAsC,SAAA,gBAME;UAIVtC,EAHM,CAAAG,YAAA,EAAM,EACC,EACL,EACF;UACNH,EAAA,CAAAmB,UAAA,KAAAyH,uCAAA,kBAA2D;UAgB7D5I,EAAA,CAAAG,YAAA,EAAM;;UAENH,EAAA,CAAAC,cAAA,eAMC;UADCD,EADA,CAAAI,UAAA,sBAAAyI,0DAAAlG,MAAA;YAAA,OAAY0F,GAAA,CAAArD,UAAA,CAAArC,MAAA,EAAmB,WAAW,CAAC;UAAA,EAAC,kBAAAmG,sDAAAnG,MAAA;YAAA,OACpC0F,GAAA,CAAAzC,MAAA,CAAAjD,MAAA,EAAe,WAAW,CAAC;UAAA,EAAC;UAEpC3C,EAAA,CAAAC,cAAA,eAA2D;UAAlCD,EAAA,CAAAI,UAAA,mBAAA2I,uDAAA;YAAA,OAASV,GAAA,CAAAd,UAAA,CAAW,WAAW,CAAC;UAAA,EAAC;UAEtDvH,EADF,CAAAC,cAAA,eAA4B,eACD;;UACvBD,EAAA,CAAAC,cAAA,eAMC;UA2CCD,EA1CA,CAAAsC,SAAA,gBAME,gBAOA,gBAOA,gBAOA,gBAOA,gBAOA,gBAOA;UAENtC,EADE,CAAAG,YAAA,EAAM,EACF;;UACNH,EAAA,CAAAC,cAAA,cAAuB;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UACtCF,EADsC,CAAAG,YAAA,EAAK,EACrC;UAEJH,EADF,CAAAC,cAAA,eAA4B,gBACG;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5CH,EAAA,CAAAC,cAAA,kBAIC;UADCD,EAAA,CAAAI,UAAA,mBAAA4I,0DAAArG,MAAA;YAAS0F,GAAA,CAAAd,UAAA,CAAW,WAAW,CAAC;YAAA,OAAE5E,MAAA,CAAAgG,eAAA,EAAwB;UAAA,EAAC;;UAE3D3I,EAAA,CAAAC,cAAA,eAUC;UACCD,EAAA,CAAAsC,SAAA,gBAME;UAIVtC,EAHM,CAAAG,YAAA,EAAM,EACC,EACL,EACF;UACNH,EAAA,CAAAmB,UAAA,KAAA8H,uCAAA,kBAA8D;UAqDlEjJ,EAnCE,CAAAG,YAAA,EAAM,EAmCF;;UAGJH,EAFF,CAAAC,cAAA,eAAe,eAQZ;UADCD,EADA,CAAAI,UAAA,sBAAA8I,0DAAAvG,MAAA;YAAA,OAAY0F,GAAA,CAAArD,UAAA,CAAArC,MAAA,EAAmB,OAAO,CAAC;UAAA,EAAC,kBAAAwG,sDAAAxG,MAAA;YAAA,OAChC0F,GAAA,CAAAzC,MAAA,CAAAjD,MAAA,EAAe,OAAO,CAAC;UAAA,EAAC;UAEhC3C,EAAA,CAAAC,cAAA,eAAuD;UAA9BD,EAAA,CAAAI,UAAA,mBAAAgJ,uDAAA;YAAA,OAASf,GAAA,CAAAd,UAAA,CAAW,OAAO,CAAC;UAAA,EAAC;UAElDvH,EADF,CAAAC,cAAA,eAA4B,eACD;;UACvBD,EAAA,CAAAC,cAAA,eAMC;UAsBCD,EArBA,CAAAsC,SAAA,gBAME,gBAOA,gBAOA,gBAOA;UAENtC,EADE,CAAAG,YAAA,EAAM,EACF;;UACNH,EAAA,CAAAC,cAAA,cAAuB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UACjCF,EADiC,CAAAG,YAAA,EAAK,EAChC;UAEJH,EADF,CAAAC,cAAA,eAA4B,gBACG;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5CH,EAAA,CAAAC,cAAA,kBAIC;UADCD,EAAA,CAAAI,UAAA,mBAAAiJ,0DAAA1G,MAAA;YAAS0F,GAAA,CAAAd,UAAA,CAAW,OAAO,CAAC;YAAA,OAAE5E,MAAA,CAAAgG,eAAA,EAAwB;UAAA,EAAC;;UAEvD3I,EAAA,CAAAC,cAAA,eAQC;UACCD,EAAA,CAAAsC,SAAA,gBAME;UAIVtC,EAHM,CAAAG,YAAA,EAAM,EACC,EACL,EACF;UACNH,EAAA,CAAAmB,UAAA,KAAAmI,uCAAA,kBAA0D;UAgB5DtJ,EAAA,CAAAG,YAAA,EAAM;;UAGNH,EAAA,CAAAC,cAAA,eAsBC;UANCD,EANA,CAAAI,UAAA,sBAAAmJ,0DAAA5G,MAAA;YAAA,OACa0F,GAAA,CAAArD,UAAA,CAAArC,MAAA,EAAA0F,GAAA,CAAA7F,MAAA,CAAAC,SAAA,KAEqB,YAAY,GAAG,WAAW,GACnE,MAAM,CACD;UAAA,EAAG,kBAAA+G,sDAAA7G,MAAA;YAAA,OAGP0F,GAAA,CAAAzC,MAAA,CAAAjD,MAAA,EAAA0F,GAAA,CAAA7F,MAAA,CAAAC,SAAA,KACwC,YAAY,GAAG,WAAW,GACnE,MAAM,CACD;UAAA,EAAG;UAEDzC,EAAA,CAAAC,cAAA,eAKC;UAHCD,EAAA,CAAAI,UAAA,mBAAAqJ,uDAAA;YAAA,OACepB,GAAA,CAAAd,UAAA,CAAAc,GAAA,CAAA7F,MAAA,CAAAC,SAAA,KAAgC,YAAY,GAAG,WACvE,GAAG,MAAM,CAAC;UAAA;UAGCzC,EADF,CAAAC,cAAA,eAA4B,eACD;UAyBvBD,EAxBA,CAAAmB,UAAA,KAAAuI,4CAAA,kBAOC,KAAAC,4CAAA,kBAwBA;UAgBH3J,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,cAAuB;UACrBD,EAAA,CAAAE,MAAA,IACF;UACFF,EADE,CAAAG,YAAA,EAAK,EACD;UAEJH,EADF,CAAAC,cAAA,eAA4B,gBACG;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5CH,EAAA,CAAAC,cAAA,kBASC;UANCD,EAAA,CAAAI,UAAA,mBAAAwJ,0DAAAjH,MAAA;YAEV0F,GAAA,CAAAd,UAAA,CAAAc,GAAA,CAAA7F,MAAA,CAAAC,SAAA,KAAmD,YAAY,GAAG,WACjE,GAAG,MAAM,CACF;YAAA,OAAkBE,MAAA,CAAAgG,eAAA,EAClB;UAAA,EAAG;;UAED3I,EAAA,CAAAC,cAAA,eAYC;UACCD,EAAA,CAAAsC,SAAA,gBAME;UAIVtC,EAHM,CAAAG,YAAA,EAAM,EACC,EACL,EACF;UACNH,EAAA,CAAAmB,UAAA,KAAA0I,uCAAA,kBAOC;UAuDT7J,EALM,CAAAG,YAAA,EAAM,EACF,EACF,EAGF;;;UA7iBIH,EAAA,CAAAe,SAAA,GAA8B;UAC9Bf,EADA,CAAA8J,WAAA,yBAA8B,kCAAAzB,GAAA,CAAA1D,oBAAA,OACsC;UAKrC3E,EAAA,CAAAe,SAAA,GAA2B;UAA3Bf,EAAA,CAAAuC,kBAAA,KAAA8F,GAAA,CAAA1D,oBAAA,MAA2B;UAc5D3E,EAAA,CAAAe,SAAA,GAA0C;UAC1Cf,EADA,CAAAuD,WAAA,cAAA8E,GAAA,CAAA9G,WAAA,CAAAC,MAAA,KAA0C,eAAA6G,GAAA,CAAAZ,cAAA,WACG;UAwErCzH,EAAA,CAAAe,SAAA,IAEC;UAFDf,EAAA,CAAA8J,WAAA,cAAAzB,GAAA,CAAAZ,cAAA,+CAEC;UAakBzH,EAAA,CAAAe,SAAA,GAA8B;UAA9Bf,EAAA,CAAAsB,UAAA,SAAA+G,GAAA,CAAAZ,cAAA,WAA8B;UAoBzDzH,EAAA,CAAAe,SAAA,EAA6C;UAC7Cf,EADA,CAAAuD,WAAA,cAAA8E,GAAA,CAAAtG,cAAA,CAAAP,MAAA,KAA6C,eAAA6G,GAAA,CAAAZ,cAAA,cACG;UA+ExCzH,EAAA,CAAAe,SAAA,IAIC;UAJDf,EAAA,CAAA8J,WAAA,cAAAzB,GAAA,CAAAZ,cAAA,kDAIC;UAakBzH,EAAA,CAAAe,SAAA,GAAiC;UAAjCf,EAAA,CAAAsB,UAAA,SAAA+G,GAAA,CAAAZ,cAAA,cAAiC;UA0D5DzH,EAAA,CAAAe,SAAA,GAAyC;UACzCf,EADA,CAAAuD,WAAA,cAAA8E,GAAA,CAAAhG,UAAA,CAAAb,MAAA,KAAyC,eAAA6G,GAAA,CAAAZ,cAAA,UACG;UA0DpCzH,EAAA,CAAAe,SAAA,IAEC;UAFDf,EAAA,CAAA8J,WAAA,cAAAzB,GAAA,CAAAZ,cAAA,8CAEC;UAakBzH,EAAA,CAAAe,SAAA,GAA6B;UAA7Bf,EAAA,CAAAsB,UAAA,SAAA+G,GAAA,CAAAZ,cAAA,UAA6B;UAqBxDzH,EAAA,CAAAe,SAAA,EAA2D;UAG3Df,EAHA,CAAAuD,WAAA,oBAAA8E,GAAA,CAAA7F,MAAA,CAAAC,SAAA,kBAA2D,eAAA4F,GAAA,CAAA7F,MAAA,CAAAC,SAAA,qBACF,cAAA4F,GAAA,CAAA3E,WAAA,CAAAlC,MAAA,KACf,eAAA6G,GAAA,CAAAZ,cAAA,CAAAY,GAAA,CAAA7F,MAAA,CAAAC,SAAA,0CAKzC;UA4BQzC,EAAA,CAAAe,SAAA,GAAuC;UAAvCf,EAAA,CAAAsB,UAAA,SAAA+G,GAAA,CAAA7F,MAAA,CAAAC,SAAA,kBAAuC;UAwBvCzC,EAAA,CAAAe,SAAA,EAA0C;UAA1Cf,EAAA,CAAAsB,UAAA,SAAA+G,GAAA,CAAA7F,MAAA,CAAAC,SAAA,qBAA0C;UAmB7CzC,EAAA,CAAAe,SAAA,GACF;UADEf,EAAA,CAAAuC,kBAAA,MAAA8F,GAAA,CAAA7F,MAAA,CAAAC,SAAA,gDACF;UAmBIzC,EAAA,CAAAe,SAAA,GAMC;UANDf,EAAA,CAAA8J,WAAA,cAAAzB,GAAA,CAAAZ,cAAA,CAAAY,GAAA,CAAA7F,MAAA,CAAAC,SAAA,8EAMC;UAeNzC,EAAA,CAAAe,SAAA,GAIF;UAJEf,EAAA,CAAAsB,UAAA,SAAA+G,GAAA,CAAAZ,cAAA,CAAAY,GAAA,CAAA7F,MAAA,CAAAC,SAAA,0CAIF;;;qBDzfG3C,YAAY,EAAAiK,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA;MAAAC,MAAA;IAAA;;SAIXvG,uBAAuB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}