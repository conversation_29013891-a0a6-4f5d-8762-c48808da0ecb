{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * @name isLeapYear\n * @category Year Helpers\n * @summary Is the given date in the leap year?\n *\n * @description\n * Is the given date in the leap year?\n *\n * @param date - The date to check\n * @param options - The options object\n *\n * @returns The date is in the leap year\n *\n * @example\n * // Is 1 September 2012 in the leap year?\n * const result = isLeapYear(new Date(2012, 8, 1))\n * //=> true\n */\nexport function isLeapYear(date, options) {\n  const _date = toDate(date, options?.in);\n  const year = _date.getFullYear();\n  return year % 400 === 0 || year % 4 === 0 && year % 100 !== 0;\n}\n\n// Fallback for modularized imports:\nexport default isLeapYear;", "map": {"version": 3, "names": ["toDate", "isLeapYear", "date", "options", "_date", "in", "year", "getFullYear"], "sources": ["C:/console/aava-ui-web/node_modules/date-fns/isLeapYear.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * @name isLeapYear\n * @category Year Helpers\n * @summary Is the given date in the leap year?\n *\n * @description\n * Is the given date in the leap year?\n *\n * @param date - The date to check\n * @param options - The options object\n *\n * @returns The date is in the leap year\n *\n * @example\n * // Is 1 September 2012 in the leap year?\n * const result = isLeapYear(new Date(2012, 8, 1))\n * //=> true\n */\nexport function isLeapYear(date, options) {\n  const _date = toDate(date, options?.in);\n  const year = _date.getFullYear();\n  return year % 400 === 0 || (year % 4 === 0 && year % 100 !== 0);\n}\n\n// Fallback for modularized imports:\nexport default isLeapYear;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACxC,MAAMC,KAAK,GAAGJ,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEE,EAAE,CAAC;EACvC,MAAMC,IAAI,GAAGF,KAAK,CAACG,WAAW,CAAC,CAAC;EAChC,OAAOD,IAAI,GAAG,GAAG,KAAK,CAAC,IAAKA,IAAI,GAAG,CAAC,KAAK,CAAC,IAAIA,IAAI,GAAG,GAAG,KAAK,CAAE;AACjE;;AAEA;AACA,eAAeL,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}