{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, EventEmitter, Injectable, Directive, Output, Input, NgModule } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\n\n/**\n * Injection token used to inject the document into Directionality.\n * This is used so that the value can be faked in tests.\n *\n * We can't use the real document in tests because changing the real `dir` causes geometry-based\n * tests in Safari to fail.\n *\n * We also can't re-provide the DOCUMENT token from platform-browser because the unit tests\n * themselves use things like `querySelector` in test code.\n *\n * This token is defined in a separate file from Directionality as a workaround for\n * https://github.com/angular/angular/issues/22559\n *\n * @docs-private\n */\nconst DIR_DOCUMENT = /*#__PURE__*/new InjectionToken('cdk-dir-doc', {\n  providedIn: 'root',\n  factory: DIR_DOCUMENT_FACTORY\n});\n/** @docs-private */\nfunction DIR_DOCUMENT_FACTORY() {\n  return inject(DOCUMENT);\n}\n\n/** Regex that matches locales with an RTL script. Taken from `goog.i18n.bidi.isRtlLanguage`. */\nconst RTL_LOCALE_PATTERN = /^(ar|ckb|dv|he|iw|fa|nqo|ps|sd|ug|ur|yi|.*[-_](Adlm|Arab|Hebr|Nkoo|Rohg|Thaa))(?!.*[-_](Latn|Cyrl)($|-|_))($|-|_)/i;\n/** Resolves a string value to a specific direction. */\nfunction _resolveDirectionality(rawValue) {\n  const value = rawValue?.toLowerCase() || '';\n  if (value === 'auto' && typeof navigator !== 'undefined' && navigator?.language) {\n    return RTL_LOCALE_PATTERN.test(navigator.language) ? 'rtl' : 'ltr';\n  }\n  return value === 'rtl' ? 'rtl' : 'ltr';\n}\n/**\n * The directionality (LTR / RTL) context for the application (or a subtree of it).\n * Exposes the current direction and a stream of direction changes.\n */\nlet Directionality = /*#__PURE__*/(() => {\n  class Directionality {\n    /** The current 'ltr' or 'rtl' value. */\n    value = 'ltr';\n    /** Stream that emits whenever the 'ltr' / 'rtl' state changes. */\n    change = new EventEmitter();\n    constructor() {\n      const _document = inject(DIR_DOCUMENT, {\n        optional: true\n      });\n      if (_document) {\n        const bodyDir = _document.body ? _document.body.dir : null;\n        const htmlDir = _document.documentElement ? _document.documentElement.dir : null;\n        this.value = _resolveDirectionality(bodyDir || htmlDir || 'ltr');\n      }\n    }\n    ngOnDestroy() {\n      this.change.complete();\n    }\n    static ɵfac = function Directionality_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || Directionality)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: Directionality,\n      factory: Directionality.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return Directionality;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Directive to listen for changes of direction of part of the DOM.\n *\n * Provides itself as Directionality such that descendant directives only need to ever inject\n * Directionality to get the closest direction.\n */\nlet Dir = /*#__PURE__*/(() => {\n  class Dir {\n    /** Normalized direction that accounts for invalid/unsupported values. */\n    _dir = 'ltr';\n    /** Whether the `value` has been set to its initial value. */\n    _isInitialized = false;\n    /** Direction as passed in by the consumer. */\n    _rawDir;\n    /** Event emitted when the direction changes. */\n    change = new EventEmitter();\n    /** @docs-private */\n    get dir() {\n      return this._dir;\n    }\n    set dir(value) {\n      const previousValue = this._dir;\n      // Note: `_resolveDirectionality` resolves the language based on the browser's language,\n      // whereas the browser does it based on the content of the element. Since doing so based\n      // on the content can be expensive, for now we're doing the simpler matching.\n      this._dir = _resolveDirectionality(value);\n      this._rawDir = value;\n      if (previousValue !== this._dir && this._isInitialized) {\n        this.change.emit(this._dir);\n      }\n    }\n    /** Current layout direction of the element. */\n    get value() {\n      return this.dir;\n    }\n    /** Initialize once default value has been set. */\n    ngAfterContentInit() {\n      this._isInitialized = true;\n    }\n    ngOnDestroy() {\n      this.change.complete();\n    }\n    static ɵfac = function Dir_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || Dir)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: Dir,\n      selectors: [[\"\", \"dir\", \"\"]],\n      hostVars: 1,\n      hostBindings: function Dir_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"dir\", ctx._rawDir);\n        }\n      },\n      inputs: {\n        dir: \"dir\"\n      },\n      outputs: {\n        change: \"dirChange\"\n      },\n      exportAs: [\"dir\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: Directionality,\n        useExisting: Dir\n      }])]\n    });\n  }\n  return Dir;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet BidiModule = /*#__PURE__*/(() => {\n  class BidiModule {\n    static ɵfac = function BidiModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BidiModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: BidiModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n  return BidiModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BidiModule, DIR_DOCUMENT, Dir, Directionality };", "map": {"version": 3, "names": ["i0", "InjectionToken", "inject", "EventEmitter", "Injectable", "Directive", "Output", "Input", "NgModule", "DOCUMENT", "DIR_DOCUMENT", "providedIn", "factory", "DIR_DOCUMENT_FACTORY", "RTL_LOCALE_PATTERN", "_resolveDirectionality", "rawValue", "value", "toLowerCase", "navigator", "language", "test", "Directionality", "change", "constructor", "_document", "optional", "bodyDir", "body", "dir", "htmlDir", "documentElement", "ngOnDestroy", "complete", "ɵfac", "Directionality_Factory", "__ngFactoryType__", "ɵprov", "ɵɵdefineInjectable", "token", "ngDevMode", "<PERSON><PERSON>", "_dir", "_isInitialized", "_rawDir", "previousValue", "emit", "ngAfterContentInit", "Dir_Factory", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostVars", "hostBindings", "Dir_<PERSON><PERSON><PERSON><PERSON>", "rf", "ctx", "ɵɵattribute", "inputs", "outputs", "exportAs", "features", "ɵɵProvidersFeature", "provide", "useExisting", "BidiModule", "BidiModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector"], "sources": ["C:/console/aava-ui-web/node_modules/@angular/cdk/fesm2022/bidi.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, inject, EventEmitter, Injectable, Directive, Output, Input, NgModule } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\n\n/**\n * Injection token used to inject the document into Directionality.\n * This is used so that the value can be faked in tests.\n *\n * We can't use the real document in tests because changing the real `dir` causes geometry-based\n * tests in Safari to fail.\n *\n * We also can't re-provide the DOCUMENT token from platform-browser because the unit tests\n * themselves use things like `querySelector` in test code.\n *\n * This token is defined in a separate file from Directionality as a workaround for\n * https://github.com/angular/angular/issues/22559\n *\n * @docs-private\n */\nconst DIR_DOCUMENT = new InjectionToken('cdk-dir-doc', {\n    providedIn: 'root',\n    factory: DIR_DOCUMENT_FACTORY,\n});\n/** @docs-private */\nfunction DIR_DOCUMENT_FACTORY() {\n    return inject(DOCUMENT);\n}\n\n/** Regex that matches locales with an RTL script. Taken from `goog.i18n.bidi.isRtlLanguage`. */\nconst RTL_LOCALE_PATTERN = /^(ar|ckb|dv|he|iw|fa|nqo|ps|sd|ug|ur|yi|.*[-_](Adlm|Arab|Hebr|Nkoo|Rohg|Thaa))(?!.*[-_](Latn|Cyrl)($|-|_))($|-|_)/i;\n/** Resolves a string value to a specific direction. */\nfunction _resolveDirectionality(rawValue) {\n    const value = rawValue?.toLowerCase() || '';\n    if (value === 'auto' && typeof navigator !== 'undefined' && navigator?.language) {\n        return RTL_LOCALE_PATTERN.test(navigator.language) ? 'rtl' : 'ltr';\n    }\n    return value === 'rtl' ? 'rtl' : 'ltr';\n}\n/**\n * The directionality (LTR / RTL) context for the application (or a subtree of it).\n * Exposes the current direction and a stream of direction changes.\n */\nclass Directionality {\n    /** The current 'ltr' or 'rtl' value. */\n    value = 'ltr';\n    /** Stream that emits whenever the 'ltr' / 'rtl' state changes. */\n    change = new EventEmitter();\n    constructor() {\n        const _document = inject(DIR_DOCUMENT, { optional: true });\n        if (_document) {\n            const bodyDir = _document.body ? _document.body.dir : null;\n            const htmlDir = _document.documentElement ? _document.documentElement.dir : null;\n            this.value = _resolveDirectionality(bodyDir || htmlDir || 'ltr');\n        }\n    }\n    ngOnDestroy() {\n        this.change.complete();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.0-rc.0\", ngImport: i0, type: Directionality, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.0-rc.0\", ngImport: i0, type: Directionality, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.0-rc.0\", ngImport: i0, type: Directionality, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\n/**\n * Directive to listen for changes of direction of part of the DOM.\n *\n * Provides itself as Directionality such that descendant directives only need to ever inject\n * Directionality to get the closest direction.\n */\nclass Dir {\n    /** Normalized direction that accounts for invalid/unsupported values. */\n    _dir = 'ltr';\n    /** Whether the `value` has been set to its initial value. */\n    _isInitialized = false;\n    /** Direction as passed in by the consumer. */\n    _rawDir;\n    /** Event emitted when the direction changes. */\n    change = new EventEmitter();\n    /** @docs-private */\n    get dir() {\n        return this._dir;\n    }\n    set dir(value) {\n        const previousValue = this._dir;\n        // Note: `_resolveDirectionality` resolves the language based on the browser's language,\n        // whereas the browser does it based on the content of the element. Since doing so based\n        // on the content can be expensive, for now we're doing the simpler matching.\n        this._dir = _resolveDirectionality(value);\n        this._rawDir = value;\n        if (previousValue !== this._dir && this._isInitialized) {\n            this.change.emit(this._dir);\n        }\n    }\n    /** Current layout direction of the element. */\n    get value() {\n        return this.dir;\n    }\n    /** Initialize once default value has been set. */\n    ngAfterContentInit() {\n        this._isInitialized = true;\n    }\n    ngOnDestroy() {\n        this.change.complete();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.0-rc.0\", ngImport: i0, type: Dir, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.0-rc.0\", type: Dir, isStandalone: true, selector: \"[dir]\", inputs: { dir: \"dir\" }, outputs: { change: \"dirChange\" }, host: { properties: { \"attr.dir\": \"_rawDir\" } }, providers: [{ provide: Directionality, useExisting: Dir }], exportAs: [\"dir\"], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.0-rc.0\", ngImport: i0, type: Dir, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[dir]',\n                    providers: [{ provide: Directionality, useExisting: Dir }],\n                    host: { '[attr.dir]': '_rawDir' },\n                    exportAs: 'dir',\n                }]\n        }], propDecorators: { change: [{\n                type: Output,\n                args: ['dirChange']\n            }], dir: [{\n                type: Input\n            }] } });\n\nclass BidiModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.0-rc.0\", ngImport: i0, type: BidiModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.0-rc.0\", ngImport: i0, type: BidiModule, imports: [Dir], exports: [Dir] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.0-rc.0\", ngImport: i0, type: BidiModule });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.0-rc.0\", ngImport: i0, type: BidiModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [Dir],\n                    exports: [Dir],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BidiModule, DIR_DOCUMENT, Dir, Directionality };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,YAAY,EAAEC,UAAU,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AACpH,SAASC,QAAQ,QAAQ,iBAAiB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,gBAAG,IAAIT,cAAc,CAAC,aAAa,EAAE;EACnDU,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEC;AACb,CAAC,CAAC;AACF;AACA,SAASA,oBAAoBA,CAAA,EAAG;EAC5B,OAAOX,MAAM,CAACO,QAAQ,CAAC;AAC3B;;AAEA;AACA,MAAMK,kBAAkB,GAAG,oHAAoH;AAC/I;AACA,SAASC,sBAAsBA,CAACC,QAAQ,EAAE;EACtC,MAAMC,KAAK,GAAGD,QAAQ,EAAEE,WAAW,CAAC,CAAC,IAAI,EAAE;EAC3C,IAAID,KAAK,KAAK,MAAM,IAAI,OAAOE,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAEC,QAAQ,EAAE;IAC7E,OAAON,kBAAkB,CAACO,IAAI,CAACF,SAAS,CAACC,QAAQ,CAAC,GAAG,KAAK,GAAG,KAAK;EACtE;EACA,OAAOH,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;AAC1C;AACA;AACA;AACA;AACA;AAHA,IAIMK,cAAc;EAApB,MAAMA,cAAc,CAAC;IACjB;IACAL,KAAK,GAAG,KAAK;IACb;IACAM,MAAM,GAAG,IAAIpB,YAAY,CAAC,CAAC;IAC3BqB,WAAWA,CAAA,EAAG;MACV,MAAMC,SAAS,GAAGvB,MAAM,CAACQ,YAAY,EAAE;QAAEgB,QAAQ,EAAE;MAAK,CAAC,CAAC;MAC1D,IAAID,SAAS,EAAE;QACX,MAAME,OAAO,GAAGF,SAAS,CAACG,IAAI,GAAGH,SAAS,CAACG,IAAI,CAACC,GAAG,GAAG,IAAI;QAC1D,MAAMC,OAAO,GAAGL,SAAS,CAACM,eAAe,GAAGN,SAAS,CAACM,eAAe,CAACF,GAAG,GAAG,IAAI;QAChF,IAAI,CAACZ,KAAK,GAAGF,sBAAsB,CAACY,OAAO,IAAIG,OAAO,IAAI,KAAK,CAAC;MACpE;IACJ;IACAE,WAAWA,CAAA,EAAG;MACV,IAAI,CAACT,MAAM,CAACU,QAAQ,CAAC,CAAC;IAC1B;IACA,OAAOC,IAAI,YAAAC,uBAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAA6Fd,cAAc;IAAA;IACtH,OAAOe,KAAK,kBADkFrC,EAAE,CAAAsC,kBAAA;MAAAC,KAAA,EACYjB,cAAc;MAAAV,OAAA,EAAdU,cAAc,CAAAY,IAAA;MAAAvB,UAAA,EAAc;IAAM;EAClJ;EAAC,OAlBKW,cAAc;AAAA;AAmBpB;EAAA,QAAAkB,SAAA,oBAAAA,SAAA;AAAA;;AAKA;AACA;AACA;AACA;AACA;AACA;AALA,IAMMC,GAAG;EAAT,MAAMA,GAAG,CAAC;IACN;IACAC,IAAI,GAAG,KAAK;IACZ;IACAC,cAAc,GAAG,KAAK;IACtB;IACAC,OAAO;IACP;IACArB,MAAM,GAAG,IAAIpB,YAAY,CAAC,CAAC;IAC3B;IACA,IAAI0B,GAAGA,CAAA,EAAG;MACN,OAAO,IAAI,CAACa,IAAI;IACpB;IACA,IAAIb,GAAGA,CAACZ,KAAK,EAAE;MACX,MAAM4B,aAAa,GAAG,IAAI,CAACH,IAAI;MAC/B;MACA;MACA;MACA,IAAI,CAACA,IAAI,GAAG3B,sBAAsB,CAACE,KAAK,CAAC;MACzC,IAAI,CAAC2B,OAAO,GAAG3B,KAAK;MACpB,IAAI4B,aAAa,KAAK,IAAI,CAACH,IAAI,IAAI,IAAI,CAACC,cAAc,EAAE;QACpD,IAAI,CAACpB,MAAM,CAACuB,IAAI,CAAC,IAAI,CAACJ,IAAI,CAAC;MAC/B;IACJ;IACA;IACA,IAAIzB,KAAKA,CAAA,EAAG;MACR,OAAO,IAAI,CAACY,GAAG;IACnB;IACA;IACAkB,kBAAkBA,CAAA,EAAG;MACjB,IAAI,CAACJ,cAAc,GAAG,IAAI;IAC9B;IACAX,WAAWA,CAAA,EAAG;MACV,IAAI,CAACT,MAAM,CAACU,QAAQ,CAAC,CAAC;IAC1B;IACA,OAAOC,IAAI,YAAAc,YAAAZ,iBAAA;MAAA,YAAAA,iBAAA,IAA6FK,GAAG;IAAA;IAC3G,OAAOQ,IAAI,kBAlDmFjD,EAAE,CAAAkD,iBAAA;MAAAC,IAAA,EAkDJV,GAAG;MAAAW,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,iBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAlDDxD,EAAE,CAAA0D,WAAA,QAAAD,GAAA,CAAAb,OAAA;QAAA;MAAA;MAAAe,MAAA;QAAA9B,GAAA;MAAA;MAAA+B,OAAA;QAAArC,MAAA;MAAA;MAAAsC,QAAA;MAAAC,QAAA,GAAF9D,EAAE,CAAA+D,kBAAA,CAkD8J,CAAC;QAAEC,OAAO,EAAE1C,cAAc;QAAE2C,WAAW,EAAExB;MAAI,CAAC,CAAC;IAAA;EACjT;EAAC,OArCKA,GAAG;AAAA;AAsCT;EAAA,QAAAD,SAAA,oBAAAA,SAAA;AAAA;AAaoB,IAEd0B,UAAU;EAAhB,MAAMA,UAAU,CAAC;IACb,OAAOhC,IAAI,YAAAiC,mBAAA/B,iBAAA;MAAA,YAAAA,iBAAA,IAA6F8B,UAAU;IAAA;IAClH,OAAOE,IAAI,kBArEmFpE,EAAE,CAAAqE,gBAAA;MAAAlB,IAAA,EAqESe;IAAU;IACnH,OAAOI,IAAI,kBAtEmFtE,EAAE,CAAAuE,gBAAA;EAuEpG;EAAC,OAJKL,UAAU;AAAA;AAKhB;EAAA,QAAA1B,SAAA,oBAAAA,SAAA;AAAA;;AAQA;AACA;AACA;;AAEA,SAAS0B,UAAU,EAAExD,YAAY,EAAE+B,GAAG,EAAEnB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}