{"ast": null, "code": "import { addLeadingZeros } from \"./_lib/addLeadingZeros.js\";\nimport { isValid } from \"./isValid.js\";\nimport { toDate } from \"./toDate.js\";\nconst days = [\"<PERSON>\", \"<PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"Sat\"];\nconst months = [\"Jan\", \"Feb\", \"Mar\", \"Apr\", \"May\", \"Jun\", \"Jul\", \"Aug\", \"Sep\", \"Oct\", \"Nov\", \"Dec\"];\n\n/**\n * @name formatRFC7231\n * @category Common Helpers\n * @summary Format the date according to the RFC 7231 standard (https://tools.ietf.org/html/rfc7231#section-*******).\n *\n * @description\n * Return the formatted date string in RFC 7231 format.\n * The result will always be in UTC timezone.\n *\n * @param date - The original date\n *\n * @returns The formatted date string\n *\n * @throws `date` must not be Invalid Date\n *\n * @example\n * // Represent 18 September 2019 in RFC 7231 format:\n * const result = formatRFC7231(new Date(2019, 8, 18, 19, 0, 52))\n * //=> 'Wed, 18 Sep 2019 19:00:52 GMT'\n */\nexport function formatRFC7231(date) {\n  const _date = toDate(date);\n  if (!isValid(_date)) {\n    throw new RangeError(\"Invalid time value\");\n  }\n  const dayName = days[_date.getUTCDay()];\n  const dayOfMonth = addLeadingZeros(_date.getUTCDate(), 2);\n  const monthName = months[_date.getUTCMonth()];\n  const year = _date.getUTCFullYear();\n  const hour = addLeadingZeros(_date.getUTCHours(), 2);\n  const minute = addLeadingZeros(_date.getUTCMinutes(), 2);\n  const second = addLeadingZeros(_date.getUTCSeconds(), 2);\n\n  // Result variables.\n  return `${dayName}, ${dayOfMonth} ${monthName} ${year} ${hour}:${minute}:${second} GMT`;\n}\n\n// Fallback for modularized imports:\nexport default formatRFC7231;", "map": {"version": 3, "names": ["addLeadingZeros", "<PERSON><PERSON><PERSON><PERSON>", "toDate", "days", "months", "formatRFC7231", "date", "_date", "RangeError", "day<PERSON><PERSON>", "getUTCDay", "dayOfMonth", "getUTCDate", "monthName", "getUTCMonth", "year", "getUTCFullYear", "hour", "getUTCHours", "minute", "getUTCMinutes", "second", "getUTCSeconds"], "sources": ["C:/console/aava-ui-web/node_modules/date-fns/formatRFC7231.js"], "sourcesContent": ["import { addLeadingZeros } from \"./_lib/addLeadingZeros.js\";\nimport { isValid } from \"./isValid.js\";\nimport { toDate } from \"./toDate.js\";\n\nconst days = [\"<PERSON>\", \"<PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"<PERSON><PERSON>\", \"Sat\"];\n\nconst months = [\n  \"Jan\",\n  \"Feb\",\n  \"Mar\",\n  \"Apr\",\n  \"May\",\n  \"Jun\",\n  \"Jul\",\n  \"Aug\",\n  \"Sep\",\n  \"Oct\",\n  \"Nov\",\n  \"Dec\",\n];\n\n/**\n * @name formatRFC7231\n * @category Common Helpers\n * @summary Format the date according to the RFC 7231 standard (https://tools.ietf.org/html/rfc7231#section-*******).\n *\n * @description\n * Return the formatted date string in RFC 7231 format.\n * The result will always be in UTC timezone.\n *\n * @param date - The original date\n *\n * @returns The formatted date string\n *\n * @throws `date` must not be Invalid Date\n *\n * @example\n * // Represent 18 September 2019 in RFC 7231 format:\n * const result = formatRFC7231(new Date(2019, 8, 18, 19, 0, 52))\n * //=> 'Wed, 18 Sep 2019 19:00:52 GMT'\n */\nexport function formatRFC7231(date) {\n  const _date = toDate(date);\n\n  if (!isValid(_date)) {\n    throw new RangeError(\"Invalid time value\");\n  }\n\n  const dayName = days[_date.getUTCDay()];\n  const dayOfMonth = addLeadingZeros(_date.getUTCDate(), 2);\n  const monthName = months[_date.getUTCMonth()];\n  const year = _date.getUTCFullYear();\n\n  const hour = addLeadingZeros(_date.getUTCHours(), 2);\n  const minute = addLeadingZeros(_date.getUTCMinutes(), 2);\n  const second = addLeadingZeros(_date.getUTCSeconds(), 2);\n\n  // Result variables.\n  return `${dayName}, ${dayOfMonth} ${monthName} ${year} ${hour}:${minute}:${second} GMT`;\n}\n\n// Fallback for modularized imports:\nexport default formatRFC7231;\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,MAAM,QAAQ,aAAa;AAEpC,MAAMC,IAAI,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;AAE9D,MAAMC,MAAM,GAAG,CACb,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACN;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,aAAaA,CAACC,IAAI,EAAE;EAClC,MAAMC,KAAK,GAAGL,MAAM,CAACI,IAAI,CAAC;EAE1B,IAAI,CAACL,OAAO,CAACM,KAAK,CAAC,EAAE;IACnB,MAAM,IAAIC,UAAU,CAAC,oBAAoB,CAAC;EAC5C;EAEA,MAAMC,OAAO,GAAGN,IAAI,CAACI,KAAK,CAACG,SAAS,CAAC,CAAC,CAAC;EACvC,MAAMC,UAAU,GAAGX,eAAe,CAACO,KAAK,CAACK,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;EACzD,MAAMC,SAAS,GAAGT,MAAM,CAACG,KAAK,CAACO,WAAW,CAAC,CAAC,CAAC;EAC7C,MAAMC,IAAI,GAAGR,KAAK,CAACS,cAAc,CAAC,CAAC;EAEnC,MAAMC,IAAI,GAAGjB,eAAe,CAACO,KAAK,CAACW,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC;EACpD,MAAMC,MAAM,GAAGnB,eAAe,CAACO,KAAK,CAACa,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC;EACxD,MAAMC,MAAM,GAAGrB,eAAe,CAACO,KAAK,CAACe,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC;;EAExD;EACA,OAAO,GAAGb,OAAO,KAAKE,UAAU,IAAIE,SAAS,IAAIE,IAAI,IAAIE,IAAI,IAAIE,MAAM,IAAIE,MAAM,MAAM;AACzF;;AAEA;AACA,eAAehB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}