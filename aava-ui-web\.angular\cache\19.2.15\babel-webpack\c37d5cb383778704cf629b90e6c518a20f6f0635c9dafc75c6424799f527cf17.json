{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { FilterTabsComponent } from '../filter-tabs/filter-tabs.component';\nimport { IconComponent } from \"@ava/play-comp-library\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../services/drawer.service\";\nimport * as i3 from \"@angular/common\";\nfunction AgentsComponent_app_filter_tabs_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-filter-tabs\", 4);\n    i0.ɵɵlistener(\"tabChange\", function AgentsComponent_app_filter_tabs_1_Template_app_filter_tabs_tabChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFilterChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"tabs\", ctx_r1.filterTabs)(\"activeTab\", ctx_r1.activeFilter);\n  }\n}\nfunction AgentsComponent_div_3_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14);\n    i0.ɵɵelement(2, \"ava-icon\", 15);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 16);\n    i0.ɵɵtext(5, \" 3 days ago \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const agent_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", agent_r4.users, \" \");\n  }\n}\nfunction AgentsComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6);\n    i0.ɵɵlistener(\"click\", function AgentsComponent_div_3_Template_div_click_1_listener() {\n      const agent_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showAgentDetails(agent_r4));\n    });\n    i0.ɵɵelementStart(2, \"div\", 7)(3, \"div\", 8)(4, \"h2\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 9);\n    i0.ɵɵelement(7, \"ava-icon\", 10);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"p\", 11);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"truncate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, AgentsComponent_div_3_div_12_Template, 6, 1, \"div\", 12);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const agent_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.showTwoColumns ? \"col-12 col-md-6\" : \"col-12 col-md-6 col-lg-3\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"marketplace-card\", ctx_r1.isMarketplace);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(agent_r4.title);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", agent_r4.rating, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 7, agent_r4.description, 75));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isMarketplace);\n  }\n}\nexport let TruncatePipe = /*#__PURE__*/(() => {\n  class TruncatePipe {\n    transform(value, limit = 75) {\n      if (!value) return '';\n      if (value.length <= limit) return value;\n      return value.substring(0, limit) + '...';\n    }\n    static ɵfac = function TruncatePipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TruncatePipe)();\n    };\n    static ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"truncate\",\n      type: TruncatePipe,\n      pure: true\n    });\n  }\n  return TruncatePipe;\n})();\nexport let AgentsComponent = /*#__PURE__*/(() => {\n  class AgentsComponent {\n    router;\n    drawerService;\n    agents = [];\n    showExploreButton = true;\n    showTwoColumns = false;\n    isMarketplace = false; // New input to detect marketplace context\n    originalAgents = [];\n    // New property to track the selected agent for details panel\n    selectedAgent = null;\n    // Removed agentHoverState and studioColors logic\n    activeFilter = 'all';\n    filterTabs = [{\n      id: 'all',\n      label: 'All',\n      priority: 100,\n      disabled: false\n    }, {\n      id: 'experience',\n      label: 'Experience Studio',\n      icon: 'lightbulb',\n      priority: 90,\n      disabled: true\n    }, {\n      id: 'product',\n      label: 'Product Studio',\n      icon: 'box',\n      priority: 80\n    }, {\n      id: 'data',\n      label: 'Data Studio',\n      icon: 'database',\n      priority: 70\n    }, {\n      id: 'finops',\n      label: 'FinOps Studio',\n      icon: 'dollar-sign',\n      priority: 60\n    }];\n    constructor(router, drawerService) {\n      this.router = router;\n      this.drawerService = drawerService;\n    }\n    ngOnInit() {\n      this.originalAgents = [...this.agents];\n    }\n    onFilterChange(filterId) {\n      this.activeFilter = filterId;\n      // Filter agents by studio type if not 'all'\n      if (filterId === 'all') {\n        this.agents = [...this.originalAgents];\n      } else {\n        const studioMap = {\n          experience: 'Experience Studio',\n          product: 'Product Studio',\n          data: 'Data Studio',\n          finops: 'FinOps Studio'\n        };\n        this.agents = this.originalAgents.filter(agent => agent.studio?.type === studioMap[filterId]);\n      }\n    }\n    navigate() {\n      this.router.navigateByUrl('/agent-list');\n    }\n    /**\n     * Shows the agent details panel for the selected agent\n     * @param agent The agent to display details for\n     */\n    showAgentDetails(agent) {\n      this.selectedAgent = agent;\n      this.drawerService.open({\n        data: agent,\n        width: '650px',\n        position: 'right',\n        onGoToPlayground: selectedAgent => this.goToPlayground(selectedAgent)\n      });\n    }\n    /**\n     * Navigates to the playground for the selected agent\n     */\n    goToPlayground(agent) {\n      const targetAgent = agent || this.selectedAgent;\n      if (targetAgent) {\n        // You can replace this with the actual navigation logic\n        this.router.navigate(['/playground', targetAgent.id]);\n        // Close the drawer after navigation\n        this.drawerService.close();\n      }\n    }\n    /**\n     * Gets the agents to display based on the showTwoColumns setting\n     * Returns only 6 agents (3 rows x 2 columns) when showTwoColumns is true\n     */\n    getDisplayedAgents() {\n      if (this.showTwoColumns) {\n        return this.agents.slice(0, 6); // Show only 6 agents for 3 rows x 2 columns\n      }\n      return this.agents; // Show all agents in normal mode\n    }\n    /**\n     * Clean up when component is destroyed\n     */\n    ngOnDestroy() {\n      // Close the drawer if it's open when component is destroyed\n      if (this.drawerService.isOpen) {\n        this.drawerService.close();\n      }\n    }\n    static ɵfac = function AgentsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AgentsComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.DrawerService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AgentsComponent,\n      selectors: [[\"app-agents\"]],\n      inputs: {\n        agents: \"agents\",\n        showExploreButton: \"showExploreButton\",\n        showTwoColumns: \"showTwoColumns\",\n        isMarketplace: \"isMarketplace\"\n      },\n      decls: 4,\n      vars: 2,\n      consts: [[1, \"agents-container\"], [3, \"tabs\", \"activeTab\", \"tabChange\", 4, \"ngIf\"], [1, \"agents-grid\", \"row\"], [3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [3, \"tabChange\", \"tabs\", \"activeTab\"], [3, \"ngClass\"], [1, \"agent-card\", 3, \"click\"], [1, \"card-content\"], [1, \"card-header\"], [1, \"rating\"], [\"iconName\", \"star\", \"iconSize\", \"18px\", \"iconColor\", \"#FFD700\", 1, \"agent_star-icon\"], [1, \"description\"], [\"class\", \"card-footer\", 4, \"ngIf\"], [1, \"card-footer\"], [1, \"users\"], [\"iconName\", \"user\", \"iconSize\", \"16px\", \"iconColor\", \"#858aad\", 1, \"profile-svg-icon\"], [1, \"agent-time-ago\"]],\n      template: function AgentsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, AgentsComponent_app_filter_tabs_1_Template, 1, 2, \"app-filter-tabs\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2);\n          i0.ɵɵtemplate(3, AgentsComponent_div_3_Template, 13, 10, \"div\", 3);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.showExploreButton);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.getDisplayedAgents());\n        }\n      },\n      dependencies: [CommonModule, i3.NgClass, i3.NgForOf, i3.NgIf, RouterModule, FilterTabsComponent, TruncatePipe, IconComponent],\n      styles: [\".electric-card, .agents-container .agent-card {\\n  background: rgba(255, 255, 255, 0.2);\\n  backdrop-filter: blur(10px);\\n  -webkit-backdrop-filter: blur(10px);\\n  border-radius: 12px;\\n  padding: 20px;\\n  margin-bottom: 30px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\\n  cursor: pointer;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.electric-card::before, .agents-container .agent-card::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, rgba(140, 101, 247, 0.1), rgba(232, 67, 147, 0.1));\\n  opacity: 0;\\n  z-index: 0;\\n}\\n.electric-card::after, .agents-container .agent-card::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  inset: 0;\\n  padding: 2px;\\n  border-radius: 12px;\\n  background: linear-gradient(90deg, rgba(255, 255, 255, 0.8) 0%, rgba(173, 216, 230, 0.8) 25%, rgba(255, 255, 255, 0.8) 50%, rgba(173, 216, 230, 0.8) 75%, rgba(255, 255, 255, 0.8) 100%);\\n  background-size: 400% 100%;\\n  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);\\n  mask-composite: exclude;\\n  opacity: 0;\\n  pointer-events: none;\\n  filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.7)) drop-shadow(0 0 4px rgba(173, 216, 230, 0.5));\\n}\\n.electric-card .card-content, .agents-container .agent-card .card-content {\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.agents-container {\\n  display: flex;\\n  flex-direction: column;\\n}\\n.agents-container .agents-header h1 {\\n  font-family: Mulish;\\n  font-size: 40px;\\n  font-style: normal;\\n  font-weight: 700;\\n  line-height: normal;\\n  letter-spacing: -0.76px;\\n  background: linear-gradient(90deg, #6566CD 36.04%, #F96CAB 118.04%);\\n  background-clip: text;\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  margin: 0;\\n}\\n.agents-container .agent-card {\\n  /* Remove border-color and transition for border color */\\n  margin: 0 12px 12px 0px;\\n}\\n.agents-container .agent-card .card-header {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  padding: 0px;\\n}\\n.agents-container .agent-card .card-header h2 {\\n  color: #4C515B;\\n  font-family: Mulish;\\n  font-size: 20px;\\n  font-weight: 600;\\n  letter-spacing: -0.456px;\\n  margin: 0;\\n}\\n.agents-container .agent-card .card-header .rating {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  font-weight: 500;\\n}\\n.agents-container .agent-card .card-header .rating .star {\\n  color: #FFD700;\\n}\\n.agents-container .agent-card .description {\\n  color: #474C6B;\\n  text-align: left;\\n  font-family: Inter;\\n  font-size: 14px;\\n  font-weight: 400;\\n  margin: 0;\\n}\\n.agents-container .agent-card .card-footer {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 0px;\\n  width: 100%;\\n}\\n.agents-container .agent-card .card-footer .users {\\n  color: #858aad;\\n  font-family: Mulish;\\n  font-size: 16px;\\n  font-style: normal;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n.agents-container .agent-card .card-footer .agent-time-ago {\\n  color: var(--Neutral-N-800, #474C6B);\\n  font-family: Mulish;\\n  font-size: 16px;\\n  font-style: normal;\\n  font-weight: 500;\\n  line-height: normal;\\n}\\n.agents-container .agent-card.marketplace-card .card-content {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n.agents-container .explore-more {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  width: 100%;\\n  margin-bottom: 140px;\\n}\\n.agents-container awe-button span {\\n  font-size: 24px;\\n}\\n.agents-container awe-button button {\\n  border-radius: 8px;\\n}\\n.agents-container .agent-details-overlay {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background-color: rgba(0, 0, 0, 0.5);\\n  -webkit-backdrop-filter: blur(8px);\\n          backdrop-filter: blur(8px);\\n  z-index: 1000;\\n  display: flex;\\n  justify-content: flex-end;\\n  animation: fadeIn 0.3s ease;\\n  font-family: \\\"Mulish\\\", sans-serif;\\n}\\n@keyframes fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n@keyframes slideIn {\\n  from {\\n    transform: translateX(100%);\\n  }\\n  to {\\n    transform: translateX(0);\\n  }\\n}\\n.agents-container .agent-details-panel {\\n  width: 650px;\\n  height: 100%;\\n  background-color: #fff;\\n  box-shadow: -4px 0 24px rgba(0, 0, 0, 0.1);\\n  overflow-y: auto;\\n  animation: slideIn 0.3s ease;\\n  display: flex;\\n  flex-direction: column;\\n  position: relative;\\n}\\n.agents-container .agent-details-panel .details-header {\\n  padding: 0;\\n  position: absolute;\\n  top: 10px;\\n  right: 10px;\\n  z-index: 10;\\n}\\n.agents-container .agent-details-panel .details-header .close-btn {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  font-size: 24px;\\n  color: #666;\\n  padding: 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n}\\n.agents-container .agent-details-panel .details-content {\\n  padding: 52px 32px 32px 32px;\\n  display: flex;\\n  flex-direction: column;\\n  min-height: 100%;\\n  justify-content: space-between;\\n}\\n.agents-container .agent-details-panel .details-content .upper-content {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 30px;\\n  padding-bottom: 30px;\\n}\\n.agents-container .agent-details-panel .details-content .add-to-list {\\n  display: flex;\\n  align-items: center;\\n  background-color: #EDEDF3;\\n  border-radius: 24px;\\n  height: 32px;\\n  width: fit-content;\\n  padding: 0 16px;\\n  gap: 0;\\n  font-size: 16px;\\n  font-weight: 500;\\n  color: #23262F;\\n  margin: 0 0 32px 0;\\n}\\n.agents-container .agent-details-panel .details-content .add-to-list ava-icon {\\n  margin-right: 2px;\\n  display: flex;\\n  align-items: center;\\n}\\n.agents-container .agent-details-panel .details-content .add-to-list button,\\n.agents-container .agent-details-panel .details-content .add-to-list awe-button,\\n.agents-container .agent-details-panel .details-content .add-to-list awe-button button {\\n  min-width: 0 !important;\\n}\\n.agents-container .agent-details-panel .details-content .action-button {\\n  margin-top: auto;\\n  width: 100%;\\n  align-self: flex-end;\\n}\\n.agents-container .agent-details-panel .details-content .agent-tags {\\n  display: flex;\\n  flex-wrap: wrap;\\n  align-items: flex-start;\\n  gap: 12px;\\n  margin: 12px 0 16px 0;\\n  padding: 0;\\n  width: auto;\\n  justify-content: flex-start;\\n  margin: 0 0 32px 0;\\n}\\n.agents-container .agent-details-panel .details-content .agent-tags .tag {\\n  display: flex;\\n  align-items: center;\\n  text-align: center;\\n  color: #6B7280;\\n  font-family: Inter;\\n  font-size: 16px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: 24px;\\n  padding: 12px 24px;\\n  border-radius: 24px;\\n  height: 38px;\\n  border: 1px solid orange;\\n  background: #fff;\\n  max-width: 170px;\\n}\\n.agents-container .agent-details-panel .details-content .details-title h2 {\\n  color: #4C515B;\\n  font-family: Mulish;\\n  font-size: 32px;\\n  font-style: normal;\\n  font-weight: 700;\\n  line-height: 38.4px;\\n  margin-bottom: 12px;\\n  margin-top: 0;\\n}\\n.agents-container .agent-details-panel .details-content .details-description {\\n  color: #6B7280;\\n  font-family: Mulish;\\n  font-size: 20px;\\n  font-style: normal;\\n  font-weight: 600;\\n  line-height: 24px;\\n  margin-bottom: 8px;\\n  text-align: left;\\n  margin-top: 0;\\n  margin-top: 10px;\\n  margin-bottom: 12px;\\n}\\n.agents-container .agent-details-panel .details-content .details-metrics {\\n  display: flex;\\n  flex-direction: column;\\n  margin-bottom: 10px;\\n  padding: 0;\\n}\\n.agents-container .agent-details-panel .details-content .details-metrics .metrics-row {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.agents-container .agent-details-panel .details-content .details-metrics .metric {\\n  flex: 1;\\n  min-width: auto;\\n  text-align: center;\\n}\\n.agents-container .agent-details-panel .details-content .details-metrics .metric .label-1 {\\n  color: #858AAD;\\n  text-align: center;\\n  font-family: Mulish;\\n  font-size: 16px;\\n  font-style: normal;\\n  font-weight: 800;\\n  line-height: 110%;\\n}\\n.agents-container .agent-details-panel .details-content .details-metrics .metric .label {\\n  color: #666D99;\\n  font-family: Mulish;\\n  font-size: 19px;\\n  font-style: normal;\\n  font-weight: 600;\\n  line-height: 97%;\\n}\\n.agents-container .agent-details-panel .details-content .details-metrics .metric .score {\\n  color: #616874;\\n  font-family: Mulish;\\n  font-size: 20px;\\n  font-style: normal;\\n  font-weight: 700;\\n  line-height: 150%;\\n}\\n.agents-container .agent-details-panel .details-content .details-metrics .metric .rating {\\n  color: #616874;\\n  font-family: Mulish;\\n  font-size: 20px;\\n  font-style: normal;\\n  font-weight: 700;\\n  line-height: 150%;\\n}\\n.agents-container .agent-details-panel .details-content .details-metrics .metric .rating .agent-star-icon {\\n  padding-bottom: 6px;\\n  margin-left: 4px;\\n}\\n.agents-container .agent-details-panel .details-content .details-section {\\n  margin-bottom: 15px;\\n  border-radius: 12px;\\n  background: linear-gradient(180deg, rgba(250, 167, 74, 0.3) 0%, rgba(67, 131, 230, 0.3) 100%);\\n  padding: 24px;\\n}\\n.agents-container .agent-details-panel .details-content .details-section:last-child {\\n  margin-bottom: 0;\\n}\\n.agents-container .agent-details-panel .details-content .details-section h3 {\\n  color: #4C515B;\\n  font-family: Mulish;\\n  font-size: 24px;\\n  font-style: normal;\\n  font-weight: 600;\\n  line-height: 28.8px;\\n  text-align: left;\\n  margin-bottom: 12px;\\n  margin-top: 0;\\n}\\n.agents-container .agent-details-panel .details-content .details-section p {\\n  color: #616874;\\n  font-family: Inter;\\n  font-size: 1rem;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: 150%;\\n  text-align: left;\\n}\\n.agents-container .agent-details-panel .details-content .details-section .type-badge {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 8px 16px;\\n  border-radius: 4px;\\n  font-size: 0.875;\\n  font-family: \\\"Mulish\\\", sans-serif;\\n}\\n.agents-container .agent-details-panel .details-content .details-section .type-badge .icon {\\n  font-size: 1rem;\\n}\\n\\n:host ::ng-deep body.details-panel-open {\\n  overflow: hidden;\\n}\\n\\nbutton {\\n  min-width: 0 !important;\\n}\\n\\n.agent-divider {\\n  width: 100%;\\n  height: 1px;\\n  background: orange;\\n  border: none;\\n  margin: 32px 0;\\n  border-radius: 1px;\\n}\\n\\n:host ::ng-deep awe-button {\\n  margin-top: 0 !important;\\n  padding: 0 !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      encapsulation: 2\n    });\n  }\n  return AgentsComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "FilterTabsComponent", "IconComponent", "i0", "ɵɵelementStart", "ɵɵlistener", "AgentsComponent_app_filter_tabs_1_Template_app_filter_tabs_tabChange_0_listener", "$event", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onFilterChange", "ɵɵelementEnd", "ɵɵproperty", "filterTabs", "activeFilter", "ɵɵelement", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "agent_r4", "users", "AgentsComponent_div_3_Template_div_click_1_listener", "_r3", "$implicit", "showAgentDetails", "ɵɵtemplate", "AgentsComponent_div_3_div_12_Template", "showTwoColumns", "ɵɵclassProp", "isMarketplace", "ɵɵtextInterpolate", "title", "rating", "ɵɵpipeBind2", "description", "TruncatePipe", "transform", "value", "limit", "length", "substring", "pure", "AgentsComponent", "router", "drawerService", "agents", "showExploreButton", "originalAgents", "selectedAgent", "id", "label", "priority", "disabled", "icon", "constructor", "ngOnInit", "filterId", "studioMap", "experience", "product", "data", "finops", "filter", "agent", "studio", "type", "navigate", "navigateByUrl", "open", "width", "position", "onGoToPlayground", "goToPlayground", "targetAgent", "close", "getDisplayedAgents", "slice", "ngOnDestroy", "isOpen", "ɵɵdirectiveInject", "i1", "Router", "i2", "DrawerService", "selectors", "inputs", "decls", "vars", "consts", "template", "AgentsComponent_Template", "rf", "ctx", "AgentsComponent_app_filter_tabs_1_Template", "AgentsComponent_div_3_Template", "i3", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles", "encapsulation"], "sources": ["C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\shared\\components\\agents\\agents.component.ts", "C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\shared\\components\\agents\\agents.component.html"], "sourcesContent": ["import { Component, Input, Pipe, PipeTransform, OnDestroy, ViewEncapsulation, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Router, RouterModule } from '@angular/router';\r\n\r\nimport { Agent } from '../../interfaces/agent-list.interface';\r\nimport {\r\n  FilterTabsComponent,\r\n  FilterTab,\r\n} from '../filter-tabs/filter-tabs.component';\r\nimport { IconComponent } from \"@ava/play-comp-library\";\r\nimport { DrawerService } from '../../services/drawer.service';\r\n\r\n@Pipe({\r\n  name: 'truncate',\r\n  standalone: true\r\n})\r\nexport class TruncatePipe implements PipeTransform {\r\n  transform(value: string, limit = 75): string {\r\n    if (!value) return '';\r\n    if (value.length <= limit) return value;\r\n    return value.substring(0, limit) + '...';\r\n  }\r\n}\r\n\r\n@Component({\r\n  selector: 'app-agents',\r\n  templateUrl: './agents.component.html',\r\n  styleUrls: ['./agents.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, RouterModule, FilterTabsComponent, TruncatePipe, IconComponent],\r\n  encapsulation: ViewEncapsulation.None\r\n})\r\nexport class AgentsComponent implements OnInit, OnDestroy {\r\n  @Input() agents: Agent[] = [];\r\n  @Input() showExploreButton = true;\r\n  @Input() showTwoColumns = false;\r\n  @Input() isMarketplace = false; // New input to detect marketplace context\r\n\r\n  private originalAgents: Agent[] = [];\r\n\r\n  // New property to track the selected agent for details panel\r\n  selectedAgent: Agent | null = null;\r\n\r\n  // Removed agentHoverState and studioColors logic\r\n\r\n  activeFilter = 'all';\r\n  filterTabs: FilterTab[] = [\r\n    { id: 'all', label: 'All', priority: 100, disabled:false },\r\n    { id: 'experience', label: 'Experience Studio', icon: 'lightbulb', priority: 90,disabled:true },\r\n    { id: 'product', label: 'Product Studio', icon: 'box', priority: 80 },\r\n    { id: 'data', label: 'Data Studio', icon: 'database', priority: 70 },\r\n    { id: 'finops', label: 'FinOps Studio', icon: 'dollar-sign', priority: 60 },\r\n  ];\r\n\r\n  constructor(\r\n    private readonly router: Router,\r\n    private readonly drawerService: DrawerService\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    this.originalAgents = [...this.agents];\r\n  }\r\n\r\n  onFilterChange(filterId: string) {\r\n    this.activeFilter = filterId;\r\n    // Filter agents by studio type if not 'all'\r\n    if (filterId === 'all') {\r\n      this.agents = [...this.originalAgents];\r\n    } else {\r\n      const studioMap: any = {\r\n        experience: 'Experience Studio',\r\n        product: 'Product Studio',\r\n        data: 'Data Studio',\r\n        finops: 'FinOps Studio',\r\n      };\r\n      this.agents = this.originalAgents.filter((agent: Agent) => agent.studio?.type === studioMap[filterId]);\r\n    }\r\n  }\r\n\r\n  navigate() {\r\n    this.router.navigateByUrl('/agent-list');\r\n  }\r\n\r\n  /**\r\n   * Shows the agent details panel for the selected agent\r\n   * @param agent The agent to display details for\r\n   */\r\n  showAgentDetails(agent: Agent): void {\r\n    this.selectedAgent = agent;\r\n    this.drawerService.open({\r\n      data: agent,\r\n      width: '650px',\r\n      position: 'right',\r\n      onGoToPlayground: (selectedAgent: Agent) => this.goToPlayground(selectedAgent)\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Navigates to the playground for the selected agent\r\n   */\r\n  goToPlayground(agent?: Agent): void {\r\n    const targetAgent = agent || this.selectedAgent;\r\n    if (targetAgent) {\r\n      // You can replace this with the actual navigation logic\r\n      this.router.navigate(['/playground', targetAgent.id]);\r\n      // Close the drawer after navigation\r\n      this.drawerService.close();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Gets the agents to display based on the showTwoColumns setting\r\n   * Returns only 6 agents (3 rows x 2 columns) when showTwoColumns is true\r\n   */\r\n  getDisplayedAgents(): Agent[] {\r\n    if (this.showTwoColumns) {\r\n      return this.agents.slice(0, 6); // Show only 6 agents for 3 rows x 2 columns\r\n    }\r\n    return this.agents; // Show all agents in normal mode\r\n  }\r\n\r\n  /**\r\n   * Clean up when component is destroyed\r\n   */\r\n  ngOnDestroy(): void {\r\n    // Close the drawer if it's open when component is destroyed\r\n    if (this.drawerService.isOpen) {\r\n      this.drawerService.close();\r\n    }\r\n  }\r\n}\r\n", "<div class=\"agents-container\">\r\n  <app-filter-tabs *ngIf=\"!showExploreButton\" [tabs]=\"filterTabs\" [activeTab]=\"activeFilter\"\r\n    (tabChange)=\"onFilterChange($event)\">\r\n  </app-filter-tabs>\r\n\r\n  <div class=\"agents-grid row\">\r\n    <div [ngClass]=\"showTwoColumns ? 'col-12 col-md-6' : 'col-12 col-md-6 col-lg-3'\" *ngFor=\"let agent of getDisplayedAgents(); let i = index\">\r\n      <div class=\"agent-card\" [class.marketplace-card]=\"isMarketplace\" (click)=\"showAgentDetails(agent)\"\r\n        >\r\n        <div class=\"card-content\">\r\n          <div class=\"card-header\">\r\n            <h2>{{ agent.title }}</h2>\r\n            <div class=\"rating\">\r\n              <ava-icon iconName=\"star\" iconSize=\"18px\" iconColor=\"#FFD700\" class=\"agent_star-icon\"></ava-icon>\r\n              {{ agent.rating }}\r\n            </div>\r\n          </div>\r\n\r\n          <p class=\"description\">{{ agent.description | truncate: 75 }}</p>\r\n\r\n          <!-- Footer comments - only show in marketplace -->\r\n          <div class=\"card-footer\" *ngIf=\"isMarketplace\">\r\n            <div class=\"users\">\r\n              <ava-icon iconName=\"user\" iconSize=\"16px\" iconColor=\"#858aad\" class=\"profile-svg-icon\"></ava-icon>\r\n              {{ agent.users }}\r\n            </div>\r\n            <div class=\"agent-time-ago\">\r\n              3 days ago\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- <div class=\"explore-more\" *ngIf=\"showExploreButton\">\r\n    <awe-button label=\"Explore More\" variant=\"primary\" class=\"w-100\" width=\"245px\"\r\n      gradient=\"linear-gradient(90deg, #FAA74A 0%, #4383E6 100%)\" (click)=\"navigate()\"></awe-button>\r\n  </div> -->\r\n\r\n\r\n</div>\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAiBC,YAAY,QAAQ,iBAAiB;AAGtD,SACEC,mBAAmB,QAEd,sCAAsC;AAC7C,SAASC,aAAa,QAAQ,wBAAwB;;;;;;;;ICRpDC,EAAA,CAAAC,cAAA,yBACuC;IAArCD,EAAA,CAAAE,UAAA,uBAAAC,gFAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAaF,MAAA,CAAAG,cAAA,CAAAN,MAAA,CAAsB;IAAA,EAAC;IACtCJ,EAAA,CAAAW,YAAA,EAAkB;;;;IAF8CX,EAApB,CAAAY,UAAA,SAAAL,MAAA,CAAAM,UAAA,CAAmB,cAAAN,MAAA,CAAAO,YAAA,CAA2B;;;;;IAqBhFd,EADF,CAAAC,cAAA,cAA+C,cAC1B;IACjBD,EAAA,CAAAe,SAAA,mBAAkG;IAClGf,EAAA,CAAAgB,MAAA,GACF;IAAAhB,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAAgB,MAAA,mBACF;IACFhB,EADE,CAAAW,YAAA,EAAM,EACF;;;;IALFX,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAkB,kBAAA,MAAAC,QAAA,CAAAC,KAAA,MACF;;;;;;IAlBNpB,EADF,CAAAC,cAAA,aAA2I,aAEtI;IAD8DD,EAAA,CAAAE,UAAA,mBAAAmB,oDAAA;MAAA,MAAAF,QAAA,GAAAnB,EAAA,CAAAK,aAAA,CAAAiB,GAAA,EAAAC,SAAA;MAAA,MAAAhB,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAiB,gBAAA,CAAAL,QAAA,CAAuB;IAAA,EAAC;IAI5FnB,EAFJ,CAAAC,cAAA,aAA0B,aACC,SACnB;IAAAD,EAAA,CAAAgB,MAAA,GAAiB;IAAAhB,EAAA,CAAAW,YAAA,EAAK;IAC1BX,EAAA,CAAAC,cAAA,aAAoB;IAClBD,EAAA,CAAAe,SAAA,mBAAiG;IACjGf,EAAA,CAAAgB,MAAA,GACF;IACFhB,EADE,CAAAW,YAAA,EAAM,EACF;IAENX,EAAA,CAAAC,cAAA,YAAuB;IAAAD,EAAA,CAAAgB,MAAA,IAAsC;;IAAAhB,EAAA,CAAAW,YAAA,EAAI;IAGjEX,EAAA,CAAAyB,UAAA,KAAAC,qCAAA,kBAA+C;IAWrD1B,EAFI,CAAAW,YAAA,EAAM,EACF,EACF;;;;;IA1BDX,EAAA,CAAAY,UAAA,YAAAL,MAAA,CAAAoB,cAAA,kDAA2E;IACtD3B,EAAA,CAAAiB,SAAA,EAAwC;IAAxCjB,EAAA,CAAA4B,WAAA,qBAAArB,MAAA,CAAAsB,aAAA,CAAwC;IAItD7B,EAAA,CAAAiB,SAAA,GAAiB;IAAjBjB,EAAA,CAAA8B,iBAAA,CAAAX,QAAA,CAAAY,KAAA,CAAiB;IAGnB/B,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAkB,kBAAA,MAAAC,QAAA,CAAAa,MAAA,MACF;IAGqBhC,EAAA,CAAAiB,SAAA,GAAsC;IAAtCjB,EAAA,CAAA8B,iBAAA,CAAA9B,EAAA,CAAAiC,WAAA,QAAAd,QAAA,CAAAe,WAAA,MAAsC;IAGnClC,EAAA,CAAAiB,SAAA,GAAmB;IAAnBjB,EAAA,CAAAY,UAAA,SAAAL,MAAA,CAAAsB,aAAA,CAAmB;;;ADLvD,WAAaM,YAAY;EAAnB,MAAOA,YAAY;IACvBC,SAASA,CAACC,KAAa,EAAEC,KAAK,GAAG,EAAE;MACjC,IAAI,CAACD,KAAK,EAAE,OAAO,EAAE;MACrB,IAAIA,KAAK,CAACE,MAAM,IAAID,KAAK,EAAE,OAAOD,KAAK;MACvC,OAAOA,KAAK,CAACG,SAAS,CAAC,CAAC,EAAEF,KAAK,CAAC,GAAG,KAAK;IAC1C;;uCALWH,YAAY;IAAA;;;YAAZA,YAAY;MAAAM,IAAA;IAAA;;SAAZN,YAAY;AAAA;AAgBzB,WAAaO,eAAe;EAAtB,MAAOA,eAAe;IAuBPC,MAAA;IACAC,aAAA;IAvBVC,MAAM,GAAY,EAAE;IACpBC,iBAAiB,GAAG,IAAI;IACxBnB,cAAc,GAAG,KAAK;IACtBE,aAAa,GAAG,KAAK,CAAC,CAAC;IAExBkB,cAAc,GAAY,EAAE;IAEpC;IACAC,aAAa,GAAiB,IAAI;IAElC;IAEAlC,YAAY,GAAG,KAAK;IACpBD,UAAU,GAAgB,CACxB;MAAEoC,EAAE,EAAE,KAAK;MAAEC,KAAK,EAAE,KAAK;MAAEC,QAAQ,EAAE,GAAG;MAAEC,QAAQ,EAAC;IAAK,CAAE,EAC1D;MAAEH,EAAE,EAAE,YAAY;MAAEC,KAAK,EAAE,mBAAmB;MAAEG,IAAI,EAAE,WAAW;MAAEF,QAAQ,EAAE,EAAE;MAACC,QAAQ,EAAC;IAAI,CAAE,EAC/F;MAAEH,EAAE,EAAE,SAAS;MAAEC,KAAK,EAAE,gBAAgB;MAAEG,IAAI,EAAE,KAAK;MAAEF,QAAQ,EAAE;IAAE,CAAE,EACrE;MAAEF,EAAE,EAAE,MAAM;MAAEC,KAAK,EAAE,aAAa;MAAEG,IAAI,EAAE,UAAU;MAAEF,QAAQ,EAAE;IAAE,CAAE,EACpE;MAAEF,EAAE,EAAE,QAAQ;MAAEC,KAAK,EAAE,eAAe;MAAEG,IAAI,EAAE,aAAa;MAAEF,QAAQ,EAAE;IAAE,CAAE,CAC5E;IAEDG,YACmBX,MAAc,EACdC,aAA4B;MAD5B,KAAAD,MAAM,GAANA,MAAM;MACN,KAAAC,aAAa,GAAbA,aAAa;IAC5B;IAEJW,QAAQA,CAAA;MACN,IAAI,CAACR,cAAc,GAAG,CAAC,GAAG,IAAI,CAACF,MAAM,CAAC;IACxC;IAEAnC,cAAcA,CAAC8C,QAAgB;MAC7B,IAAI,CAAC1C,YAAY,GAAG0C,QAAQ;MAC5B;MACA,IAAIA,QAAQ,KAAK,KAAK,EAAE;QACtB,IAAI,CAACX,MAAM,GAAG,CAAC,GAAG,IAAI,CAACE,cAAc,CAAC;MACxC,CAAC,MAAM;QACL,MAAMU,SAAS,GAAQ;UACrBC,UAAU,EAAE,mBAAmB;UAC/BC,OAAO,EAAE,gBAAgB;UACzBC,IAAI,EAAE,aAAa;UACnBC,MAAM,EAAE;SACT;QACD,IAAI,CAAChB,MAAM,GAAG,IAAI,CAACE,cAAc,CAACe,MAAM,CAAEC,KAAY,IAAKA,KAAK,CAACC,MAAM,EAAEC,IAAI,KAAKR,SAAS,CAACD,QAAQ,CAAC,CAAC;MACxG;IACF;IAEAU,QAAQA,CAAA;MACN,IAAI,CAACvB,MAAM,CAACwB,aAAa,CAAC,aAAa,CAAC;IAC1C;IAEA;;;;IAIA3C,gBAAgBA,CAACuC,KAAY;MAC3B,IAAI,CAACf,aAAa,GAAGe,KAAK;MAC1B,IAAI,CAACnB,aAAa,CAACwB,IAAI,CAAC;QACtBR,IAAI,EAAEG,KAAK;QACXM,KAAK,EAAE,OAAO;QACdC,QAAQ,EAAE,OAAO;QACjBC,gBAAgB,EAAGvB,aAAoB,IAAK,IAAI,CAACwB,cAAc,CAACxB,aAAa;OAC9E,CAAC;IACJ;IAEA;;;IAGAwB,cAAcA,CAACT,KAAa;MAC1B,MAAMU,WAAW,GAAGV,KAAK,IAAI,IAAI,CAACf,aAAa;MAC/C,IAAIyB,WAAW,EAAE;QACf;QACA,IAAI,CAAC9B,MAAM,CAACuB,QAAQ,CAAC,CAAC,aAAa,EAAEO,WAAW,CAACxB,EAAE,CAAC,CAAC;QACrD;QACA,IAAI,CAACL,aAAa,CAAC8B,KAAK,EAAE;MAC5B;IACF;IAEA;;;;IAIAC,kBAAkBA,CAAA;MAChB,IAAI,IAAI,CAAChD,cAAc,EAAE;QACvB,OAAO,IAAI,CAACkB,MAAM,CAAC+B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAClC;MACA,OAAO,IAAI,CAAC/B,MAAM,CAAC,CAAC;IACtB;IAEA;;;IAGAgC,WAAWA,CAAA;MACT;MACA,IAAI,IAAI,CAACjC,aAAa,CAACkC,MAAM,EAAE;QAC7B,IAAI,CAAClC,aAAa,CAAC8B,KAAK,EAAE;MAC5B;IACF;;uCAjGWhC,eAAe,EAAA1C,EAAA,CAAA+E,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAjF,EAAA,CAAA+E,iBAAA,CAAAG,EAAA,CAAAC,aAAA;IAAA;;YAAfzC,eAAe;MAAA0C,SAAA;MAAAC,MAAA;QAAAxC,MAAA;QAAAC,iBAAA;QAAAnB,cAAA;QAAAE,aAAA;MAAA;MAAAyD,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChC5B3F,EAAA,CAAAC,cAAA,aAA8B;UAC5BD,EAAA,CAAAyB,UAAA,IAAAoE,0CAAA,6BACuC;UAGvC7F,EAAA,CAAAC,cAAA,aAA6B;UAC3BD,EAAA,CAAAyB,UAAA,IAAAqE,8BAAA,mBAA2I;UAmC/I9F,EARE,CAAAW,YAAA,EAAM,EAQF;;;UAxCcX,EAAA,CAAAiB,SAAA,EAAwB;UAAxBjB,EAAA,CAAAY,UAAA,UAAAgF,GAAA,CAAA9C,iBAAA,CAAwB;UAK2D9C,EAAA,CAAAiB,SAAA,GAAyB;UAAzBjB,EAAA,CAAAY,UAAA,YAAAgF,GAAA,CAAAjB,kBAAA,GAAyB;;;qBDuBpH/E,YAAY,EAAAmG,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAErG,YAAY,EAAEC,mBAAmB,EAb9CqC,YAAY,EAakDpC,aAAa;MAAAoG,MAAA;MAAAC,aAAA;IAAA;;SAG3E1D,eAAe;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}