{"ast": null, "code": "import { ViewContainerRef } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { AgentNodeComponent } from './components/agent-node/agent-node.component';\nimport { debounceTime, distinctUntilChanged, map, startWith } from 'rxjs';\nimport { DragDropModule } from '@angular/cdk/drag-drop';\nimport workflowLabels from './../constants/workflows.json';\nimport { CanvasBoardComponent } from '@shared/components/canvas-board/canvas-board.component';\nimport { AvaTextboxComponent, ButtonComponent, DropdownComponent, IconComponent, DialogService, SliderComponent, ToggleComponent } from '@ava/play-comp-library';\nimport { WorkflowModes } from '../constants/workflow.constants';\nimport { WorkflowPreviewPanelComponent } from './workflow-preview-panel/workflow-preview-panel.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"./services/workflow-graph.service\";\nimport * as i4 from \"@shared/services/workflow.service\";\nimport * as i5 from \"./services/react-flow.service\";\nimport * as i6 from \"@shared/pages/agents/services/agent-service.service\";\nimport * as i7 from \"@shared/index\";\nimport * as i8 from \"@shared/services/drawer/drawer.service\";\nimport * as i9 from \"@ava/play-comp-library\";\nimport * as i10 from \"@angular/common\";\nconst _c0 = [\"drawerContainer\"];\nconst _c1 = [\"llmSettingsRef\"];\nconst _c2 = a0 => ({\n  \"p-3\": a0\n});\nconst _c3 = () => ({\n  background: \"linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)\",\n  \"--button-effect-color\": \"33, 90, 214\",\n  \"border-radius\": \"8px\"\n});\nfunction WorkflowEditorComponent_div_14_div_12_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 43);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const capability_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(capability_r5);\n  }\n}\nfunction WorkflowEditorComponent_div_14_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵtemplate(1, WorkflowEditorComponent_div_14_div_12_span_1_Template, 2, 1, \"span\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const agent_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", agent_r3.capabilities);\n  }\n}\nfunction WorkflowEditorComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵlistener(\"dragstart\", function WorkflowEditorComponent_div_14_Template_div_dragstart_0_listener($event) {\n      const agent_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onDragStart($event, agent_r3));\n    });\n    i0.ɵɵelementStart(1, \"div\", 30)(2, \"div\", 31);\n    i0.ɵɵelement(3, \"ava-icon\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h4\", 33);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 34);\n    i0.ɵɵelement(7, \"ava-icon\", 35);\n    i0.ɵɵelementStart(8, \"span\", 36);\n    i0.ɵɵtext(9, \"120\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"p\", 37);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, WorkflowEditorComponent_div_14_div_12_Template, 2, 1, \"div\", 38);\n    i0.ɵɵelementStart(13, \"div\", 39)(14, \"ava-button\", 40);\n    i0.ɵɵlistener(\"userClick\", function WorkflowEditorComponent_div_14_Template_ava_button_userClick_14_listener() {\n      const agent_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onItemPreview(agent_r3));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const agent_r3 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(agent_r3.name);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(agent_r3.description);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", agent_r3.capabilities && agent_r3.capabilities.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"pill\", true);\n  }\n}\nfunction WorkflowEditorComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"div\", 45);\n    i0.ɵɵelement(2, \"ava-icon\", 46);\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No agents found matching your criteria\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction WorkflowEditorComponent_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-agent-node\", 47);\n    i0.ɵɵlistener(\"deleteNode\", function WorkflowEditorComponent_ng_template_20_Template_app_agent_node_deleteNode_0_listener($event) {\n      const onDelete_r7 = i0.ɵɵrestoreView(_r6).onDelete;\n      return i0.ɵɵresetView(onDelete_r7($event));\n    })(\"moveNode\", function WorkflowEditorComponent_ng_template_20_Template_app_agent_node_moveNode_0_listener($event) {\n      const onMove_r8 = i0.ɵɵrestoreView(_r6).onMove;\n      return i0.ɵɵresetView(onMove_r8($event));\n    })(\"nodeSelected\", function WorkflowEditorComponent_ng_template_20_Template_app_agent_node_nodeSelected_0_listener($event) {\n      const onSelect_r9 = i0.ɵɵrestoreView(_r6).onSelect;\n      return i0.ɵɵresetView(onSelect_r9($event));\n    })(\"startConnection\", function WorkflowEditorComponent_ng_template_20_Template_app_agent_node_startConnection_0_listener($event) {\n      const onStartConnection_r10 = i0.ɵɵrestoreView(_r6).onStartConnection;\n      return i0.ɵɵresetView(onStartConnection_r10($event));\n    })(\"nodePositionChanged\", function WorkflowEditorComponent_ng_template_20_Template_app_agent_node_nodePositionChanged_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.updateNodePosition($event));\n    })(\"toolValuesChanged\", function WorkflowEditorComponent_ng_template_20_Template_app_agent_node_toolValuesChanged_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.builtInToolValuesChanged($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const node_r11 = ctx.$implicit;\n    const selected_r12 = ctx.selected;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"node\", node_r11)(\"selected\", selected_r12)(\"patchData\", ctx_r3.getToolPatchValueForAgent(node_r11 == null ? null : node_r11.data == null ? null : node_r11.data.agentId));\n  }\n}\nfunction WorkflowEditorComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const modelValue_r13 = ctx.ngIf;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" You are currently using \", modelValue_r13, \" \");\n  }\n}\nfunction WorkflowEditorComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49, 2)(2, \"div\", 50)(3, \"h3\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"ava-dropdown\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 50)(7, \"h3\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(9, \"ava-slider\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 50)(11, \"h3\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"input\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 50)(15, \"h3\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(17, \"input\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 50)(19, \"h3\");\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 55);\n    i0.ɵɵelement(22, \"input\", 54);\n    i0.ɵɵelementStart(23, \"span\", 56);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"div\", 50)(26, \"h3\");\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(28, \"input\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 50)(30, \"h3\");\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(32, \"input\", 54);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r3.workFlowLabels.model);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r3.modelList)(\"formControl\", ctx_r3.getControl(\"modelDeploymentName\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.workFlowLabels.temperature);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"min\", 0)(\"max\", 1)(\"step\", 0.1)(\"formControl\", ctx_r3.getControl(\"temperature\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.workFlowLabels.topP);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formControl\", ctx_r3.getControl(\"topP\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.workFlowLabels.maxRPM);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formControl\", ctx_r3.getControl(\"maxRPM\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.workFlowLabels.maxToken);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"formControl\", ctx_r3.getControl(\"maxToken\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r3.getControl(\"maxToken\").value, \"/\", ctx_r3.workFlowLabels.tokensUsed, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.workFlowLabels.maxIteration);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formControl\", ctx_r3.getControl(\"maxIteration\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.workFlowLabels.maxExecutionTime);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formControl\", ctx_r3.getControl(\"maxExecutionTime\"));\n  }\n}\nexport let WorkflowEditorComponent = /*#__PURE__*/(() => {\n  class WorkflowEditorComponent {\n    fb;\n    router;\n    route;\n    workflowGraphService;\n    workflowService;\n    reactFlowService;\n    cdr;\n    agentService;\n    tokenStorage;\n    drawerService;\n    dialogService;\n    drawerContainer;\n    primaryButtonText = '';\n    workflowId = null;\n    isEditMode = false;\n    isDuplicateMode = false;\n    workflowForm;\n    modelList = [];\n    savedWorkFlowDetils = {};\n    // Labels used across the Knowledge Base UI components (titles)\n    workFlowLabels = workflowLabels.labels;\n    showLlm = false;\n    levels = ['organization', 'domain', 'project', 'team'];\n    filterLabels = ['organization', 'domain', 'project', 'team'];\n    optionsMap = {};\n    levelOptionsMap = {};\n    inputFieldsConfig = {\n      // agentName: { enabled: true, placeholder: 'Workflow Name', required: true },\n      agentName: {\n        enabled: true,\n        placeholder: 'Agent Name',\n        required: true\n      },\n      agentDetails: {\n        enabled: true,\n        // Re-enabled - this is the correct Workflow Details section\n        label: 'Workflow Details',\n        namePlaceholder: 'Enter Workflow name',\n        detailPlaceholder: 'Enter workflow description',\n        detailLabel: 'Description'\n      }\n    };\n    // Canvas board properties\n    canvasNodes = [];\n    canvasEdges = [];\n    navigationHints = [\n      // `${workflowLabels.labels.alt} + ${workflowLabels.labels.drag} ${workflowLabels.labels.toPanCanvas}`,\n      // `${workflowLabels.labels.mouseWheel} ${workflowLabels.labels.toZoom}`,\n      // `${workflowLabels.labels.space} ${workflowLabels.labels.toResetView}`\n    ];\n    // Agent library\n    agents = [{\n      id: 'agent1',\n      name: 'Test Agent Agent Agent Agent Agent Agent',\n      description: 'Translating user needs into actionable development tasks.',\n      type: 'Individual',\n      capabilities: ['Code Generation', 'Translation']\n    }, {\n      id: 'agent2',\n      name: 'Test Agent 2',\n      description: 'Processing data and generating insights.',\n      type: 'Individual',\n      capabilities: ['Data Analysis', 'Visualization']\n    }, {\n      id: 'agent3',\n      name: 'Test Agent 3',\n      description: 'Handling complex natural language processing tasks.',\n      type: 'Collaborative',\n      capabilities: ['NLP', 'Summarization', 'Translation']\n    }];\n    // Filtered agents for search\n    filteredAgents = [];\n    // Available agents (filtered and not used yet)\n    availableAgents = [];\n    // Workflow nodes and edges (original format)\n    nodes = [];\n    edges = [];\n    // Selected node\n    selectedNodeId = null;\n    // Subscriptions\n    subscriptions = [];\n    // Track used agent IDs\n    usedAgentIds = new Set();\n    // Panel state\n    isPanelCollapsed = false;\n    currentPage = 1;\n    pageSize = 50;\n    isDeleted = false;\n    // Search form for the panel\n    searchForm;\n    builtInToolValues = [];\n    nodeToolValues = new Map();\n    workflowData = {};\n    totalNumberOfRecords = 0;\n    llmSettingsRef;\n    constructor(fb, router, route, workflowGraphService, workflowService, reactFlowService, cdr, agentService, tokenStorage, drawerService, dialogService) {\n      this.fb = fb;\n      this.router = router;\n      this.route = route;\n      this.workflowGraphService = workflowGraphService;\n      this.workflowService = workflowService;\n      this.reactFlowService = reactFlowService;\n      this.cdr = cdr;\n      this.agentService = agentService;\n      this.tokenStorage = tokenStorage;\n      this.drawerService = drawerService;\n      this.dialogService = dialogService;\n      this.workflowForm = this.fb.group({\n        // Workflow details\n        name: [''],\n        description: [''],\n        // Filters\n        organization: [''],\n        domain: [''],\n        project: [''],\n        team: [''],\n        // Enable Manager LLM\n        enableManagerLLM: [false],\n        // LLM Configuration settings\n        modelDeploymentName: [],\n        temperature: [0.3],\n        topP: [0.95],\n        maxRPM: [0],\n        maxToken: [4000],\n        maxIteration: [1],\n        maxExecutionTime: [30],\n        // Search filter\n        agentFilter: ['']\n      });\n      // Initialize search form for the panel\n      this.searchForm = this.fb.group({\n        agentFilter: ['']\n      });\n    }\n    searchList() {\n      this.searchForm.get('agentFilter').valueChanges.pipe(startWith(''), debounceTime(300), distinctUntilChanged(), map(value => value?.toLowerCase() ?? '')).subscribe(searchText => {\n        this.filterWorkflow(searchText);\n      });\n    }\n    filterWorkflow(searchText) {\n      this.filteredAgents = this.availableAgents.filter(res => {\n        const inTitle = res.name?.toLowerCase().includes(searchText);\n        return inTitle;\n      });\n    }\n    // Panel toggle functionality\n    togglePanel() {\n      this.isPanelCollapsed = !this.isPanelCollapsed;\n    }\n    // Item preview functionality\n    onItemPreview(agent) {\n      console.log('Preview agent:', agent);\n      // Implement preview functionality\n      this.drawerService.open(WorkflowPreviewPanelComponent, {\n        previewData: {\n          type: 'agent',\n          title: agent.name,\n          data: agent\n        },\n        closePreview: () => this.drawerService.clear()\n      });\n    }\n    // Create new agent functionality\n    onCreateNewAgent() {\n      console.log('Create new agent');\n      // Navigate to agent creation page or open modal\n      this.router.navigate(['/build/agents/create']);\n    }\n    isModeDuplicate() {\n      return this.route.snapshot.queryParams['mode'] === WorkflowModes.duplicate;\n    }\n    getAndPatchWorkFlowDetails(id) {\n      const isDuplicateMode = this.isModeDuplicate();\n      this.workflowService.getWorkflowById(id).subscribe(response => {\n        this.savedWorkFlowDetils = response;\n        // console.log(this.savedWorkFlowDetils);\n        const agents = response?.workflowAgents || [];\n        agents.sort((a, b) => a.serial - b.serial);\n        let x = 400;\n        let y = 150;\n        // console.log(agents);\n        agents.forEach((agent, i) => {\n          this.createNewNode(agent.agentDetails, {\n            x,\n            y\n          });\n          x += 400;\n          const position = i + 1;\n          if (position % 3 == 0) {\n            y += 120;\n            x = 40;\n          }\n        });\n        this.workflowData = response;\n        const formData = {};\n        if (!this.isEditMode) {\n          formData['createdBy'] = response?.createdBy;\n        }\n        if (!isDuplicateMode) {\n          formData['name'] = response?.name;\n          formData['description'] = response?.description;\n        }\n        if (response?.managerLlm) {\n          formData['enableManagerLLM'] = true;\n          Object.assign(formData, response?.managerLlm);\n        }\n        this.workflowForm.patchValue(formData);\n      });\n    }\n    ngOnInit() {\n      this.searchList();\n      // this.fetchChildOptions(0, -1);\n      // Check if we're in edit mode\n      this.workflowId = this.route.snapshot.paramMap.get('id') || this.route.snapshot.queryParams['id'];\n      this.isEditMode = !!this.workflowId;\n      const mode = this.route.snapshot.queryParams['mode'];\n      if (mode === 'duplicate') {\n        this.isEditMode = false;\n        this.isDuplicateMode = true;\n      }\n      if (this.isEditMode && this.workflowId) {\n        // In a real app, you would fetch the workflow data by ID\n        console.log(`Editing workflow with ID: ${this.workflowId}`);\n        this.getAndPatchWorkFlowDetails(this.workflowId);\n        // this.loadWorkflowData(this.workflowId);\n      }\n      if (this.isDuplicateMode && this.workflowId) {\n        console.log(`Cloning workflow with ID: ${this.workflowId}`);\n        this.getAndPatchWorkFlowDetails(this.workflowId);\n      }\n      // Subscribe to nodes to track used agents\n      this.subscriptions.push(this.workflowGraphService.nodes$.subscribe(nodes => {\n        this.nodes = nodes;\n        this.canvasNodes = this.convertToCanvasNodes(nodes);\n        // Update used agent IDs\n        this.updateUsedAgentIds();\n      }));\n      this.subscriptions.push(this.workflowGraphService.edges$.subscribe(edges => {\n        this.edges = edges;\n        this.canvasEdges = this.convertToCanvasEdges(edges);\n      }));\n      // Subscribe to the search filter changes\n      this.subscriptions.push(this.getControl('agentFilter').valueChanges.subscribe(filterValue => {\n        this.filterAgents(filterValue);\n      }));\n      this.getCollaborativeAgents();\n      this.workflowService.getAllGenerativeModels().subscribe(response => {\n        this.modelList = response.map(modle => {\n          return {\n            name: modle.modelDeploymentName,\n            value: modle.modelDeploymentName\n          };\n        });\n      });\n    }\n    ngAfterViewInit() {\n      // Canvas board handles its own initialization\n      this.drawerService.registerViewContainer(this.drawerContainer);\n    }\n    getCollaborativeAgents() {\n      this.agentService.getAllCollaborativeAgentsPagination(this.currentPage, this.totalNumberOfRecords, this.isDeleted).subscribe(response => {\n        this.agents = response.agentDetails;\n        this.filterAgents(this.getControl('agentFilter').value);\n        // Making sure the dropdown value get patched agin after options are added\n        setTimeout(() => {\n          this.workflowForm.patchValue({\n            modelDeploymentName: this.savedWorkFlowDetils['managerLlm']?.modelDeploymentName\n          });\n        });\n      });\n    }\n    // HostListener to close llm-settings when clicking outside\n    onDocumentClick(event) {\n      if (this.showLlm && this.llmSettingsRef) {\n        const target = event.target;\n        if (!this.llmSettingsRef.nativeElement.contains(target)) {\n          this.showLlm = false;\n        }\n      }\n    }\n    // Convert WorkflowNode to CanvasNode\n    convertToCanvasNodes(nodes) {\n      return nodes.map(node => ({\n        id: node.id,\n        type: node.type,\n        data: node.data,\n        position: node.position\n      }));\n    }\n    // Convert WorkflowEdge to CanvasEdge\n    convertToCanvasEdges(edges) {\n      return edges.map(edge => ({\n        id: edge.id,\n        source: edge.source,\n        target: edge.target,\n        animated: edge.animated\n      }));\n    }\n    convertNodesToEdges(nodes) {\n      return nodes.slice(1).map((node, i) => {\n        const perviousNode = i <= 0 ? nodes[0] : nodes[i - 1];\n        return {\n          id: `${perviousNode.id}-${node.id}`,\n          source: perviousNode.id,\n          target: node.id\n          // animated: node.animated,\n        };\n      });\n    }\n    onLevelChange(level, selectedValue) {\n      const selected = Array.isArray(selectedValue) ? selectedValue[0] : selectedValue;\n      if (!selected) {\n        return;\n      }\n      const controlName = this.filterLabels[level];\n      const control = this.workflowForm.get(controlName);\n      if (control) {\n        control.setValue(selected);\n      }\n      // Reset controls and options for levels below the current one\n      for (let i = level + 1; i < this.levels.length; i++) {\n        this.resetControlAtLevel(i);\n        this.levelOptionsMap[i] = [];\n      }\n      const selectedNumber = Number(selected);\n      if (!isNaN(selectedNumber)) {\n        this.fetchChildOptions(level + 1, selectedNumber);\n      }\n    }\n    resetControlAtLevel(level) {\n      const controlName = this.filterLabels[level];\n      const control = this.workflowForm.get(controlName);\n      if (control) {\n        control.setValue(null);\n      }\n    }\n    getOptionsForLevel(level) {\n      return this.levelOptionsMap[level] || [];\n    }\n    fetchChildOptions(level, parentId) {\n      if (!this.filterLabels[level]) return;\n      this.workflowService.getDropdownList(level, parentId).subscribe({\n        next: res => {\n          this.levelOptionsMap[level] = Array.isArray(res) ? res : [];\n        },\n        error: () => {\n          this.levelOptionsMap[level] = [];\n        }\n      });\n    }\n    ngOnDestroy() {\n      // Clean up subscriptions\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n      this.workflowGraphService.clearWorkflow();\n    }\n    createNewNode(agent, position) {\n      // console.log(agent);\n      const agentTools = agent?.agentConfigs?.toolRef || [];\n      const agentUserTools = agent?.agentConfigs?.userToolRef || [];\n      const tools = [...agentTools, ...agentUserTools].map(tool => tool?.toolName).join(', ');\n      const model = agent?.agentConfigs?.modelRef[0];\n      const newNode = {\n        id: this.workflowGraphService.generateNodeId(),\n        type: 'agent',\n        data: {\n          label: agent.name,\n          agentId: agent.id,\n          agentName: agent.name,\n          description: agent.description,\n          capabilities: agent.expeectedOutput,\n          width: 280,\n          // Default width\n          model: model?.modelDeploymentName,\n          tools,\n          agentTools\n        },\n        position: position\n      };\n      this.workflowGraphService.addNode(newNode);\n      if (this.getControl('enableManagerLLM').value) return;\n      const nodes = this.workflowGraphService.getAllNodes();\n      const lastNode = nodes.at(-2);\n      if (lastNode) {\n        this.workflowGraphService.addEdge({\n          id: `${lastNode.id}-${newNode.id}`,\n          source: lastNode.id,\n          target: newNode.id\n        });\n      }\n    }\n    // Canvas board event handlers\n    onCanvasDropped(event) {\n      const dragEvent = event.event;\n      const position = event.position;\n      if (dragEvent.dataTransfer) {\n        const agentData = dragEvent.dataTransfer.getData('application/reactflow');\n        if (agentData) {\n          try {\n            const agent = JSON.parse(agentData);\n            // const agentTools = agent?.tools || [];\n            // const agentUserTools = agent?.userTools || [];\n            // const tools = [...agentTools, ...agentUserTools]\n            //   .map((tool) => tool?.toolName)\n            //   .join(', ');\n            // // Create a new node for the agent\n            // const newNode: WorkflowNode = {\n            //   id: this.workflowGraphService.generateNodeId(),\n            //   type: 'agent',\n            //   data: {\n            //     label: agent.name,\n            //     agentId: agent.id,\n            //     agentName: agent.name,\n            //     description: agent.description,\n            //     capabilities: agent.capabilities,\n            //     width: 280, // Default width\n            //     model: agent?.modelDetails?.modelDeploymentName,\n            //     tools,\n            //   },\n            //   position: position,\n            // };\n            // this.workflowGraphService.addNode(newNode);\n            // const nodes = this.workflowGraphService.getAllNodes();\n            // const lastNode = nodes.at(-2);\n            // if (lastNode) {\n            //   this.workflowGraphService.addEdge({\n            //     id: `${lastNode.id}-${newNode.id}`,\n            //     source: lastNode.id,\n            //     target: newNode.id,\n            //   });\n            // }\n            this.createNewNode(agent, position);\n          } catch (error) {\n            console.error('Error adding node:', error);\n          }\n        }\n      }\n    }\n    onConnectionCreated(edge) {\n      const newEdge = {\n        id: edge.id,\n        source: edge.source,\n        target: edge.target,\n        animated: edge.animated || true\n      };\n      this.workflowGraphService.addEdge(newEdge);\n    }\n    filterAgents(filterValue) {\n      // First filter by search term\n      if (!filterValue || filterValue.trim() === '') {\n        this.filteredAgents = [...this.agents];\n      } else {\n        filterValue = filterValue.toLowerCase().trim();\n        this.filteredAgents = this.agents.filter(agent => agent.name.toLowerCase().includes(filterValue) || agent.description.toLowerCase().includes(filterValue) || agent.type && agent.type.toLowerCase().includes(filterValue) || agent.capabilities && agent.capabilities.some(cap => cap.toLowerCase().includes(filterValue)));\n      }\n      // Then filter out agents that are already used\n      this.updateAvailableAgents();\n    }\n    updateAvailableAgents() {\n      this.availableAgents = this.agents.filter(agent => !this.usedAgentIds.has(agent.id));\n    }\n    onDragStart(event, agent) {\n      if (event.dataTransfer) {\n        event.dataTransfer.setData('application/reactflow', JSON.stringify(agent));\n        event.dataTransfer.effectAllowed = 'move';\n      }\n    }\n    onDeleteNode(nodeId) {\n      this.workflowGraphService.removeNode(nodeId);\n      // If the deleted node was selected, clear selection\n      if (this.selectedNodeId === nodeId) {\n        this.selectedNodeId = null;\n      }\n      this.nodeToolValues.delete(nodeId);\n    }\n    onNodeSelected(nodeId) {\n      this.selectedNodeId = nodeId;\n    }\n    onNodeMoved(data) {\n      // Find the node index\n      const nodeIndex = this.nodes.findIndex(node => node.id === data.nodeId);\n      if (nodeIndex === -1) return;\n      // Create a new array with the updated node\n      const updatedNodes = [...this.nodes];\n      updatedNodes[nodeIndex] = {\n        ...this.nodes[nodeIndex],\n        position: data.position\n      };\n      // Update the node positions through service\n      this.workflowGraphService.updateNodePositions(updatedNodes);\n      // Force a change detection cycle\n      this.cdr.detectChanges();\n    }\n    onStartConnection(data) {\n      // Canvas board handles connection logic\n      // This method is called when a connection starts but the canvas board manages the temp connection\n    }\n    updateNodePosition(data) {\n      // Canvas board handles connection point updates\n    }\n    builtInToolValuesChanged(event) {\n      if (!event || !event.nodeId || !event.toolValues) {\n        console.warn('Invalid tool values event:', event);\n        return;\n      }\n      // console.log('Tool values changed for node:', event.nodeId, event.toolValues);\n      this.nodeToolValues.set(event.nodeId, event.toolValues);\n      const allToolValues = Array.from(this.nodeToolValues.values()).filter(values => values != null).flat().filter(tool => tool != null);\n      this.builtInToolValues = allToolValues;\n      // console.log('All tool values updated:', this.builtInToolValues);\n    }\n    onSave() {\n      // Check if workflow name is provided\n      if (!this.workflowForm.get('name')?.value?.trim()) {\n        this.dialogService.warning({\n          title: 'Warning',\n          message: 'Please provide a name for the workflow before saving.',\n          showProceedButton: true,\n          proceedButtonText: 'OK'\n        });\n        return;\n      }\n      // Show confirmation dialog before saving\n      this.dialogService.confirm({\n        title: 'Save Workflow',\n        message: 'Are you sure you want to save this workflow?',\n        confirmText: 'Save',\n        cancelText: 'Cancel'\n      }).then(confirmed => {\n        if (!confirmed) {\n          return; // User cancelled the save operation\n        }\n        // Show loading dialog\n        const loadingDialog = this.dialogService.loading({\n          message: 'Saving workflow...',\n          showSpinner: true\n        });\n        const payload = this.makeWorkFlowPayload();\n        // Determine if we're creating or updating\n        const saveOperation = this.workflowId && !this.isDuplicateMode ? this.workflowService.updateWorkFlow(payload) : this.workflowService.saveWorkFlow(payload);\n        saveOperation.subscribe({\n          next: response => {\n            // Close loading dialog\n            loadingDialog.close();\n            // Show success message\n            this.dialogService.success({\n              title: 'Success',\n              message: 'Workflow saved successfully!',\n              showProceedButton: true,\n              proceedButtonText: 'OK'\n            });\n            // If this was a new workflow, update the URL with the new ID\n            if (!this.workflowId || this.isDuplicateMode) {\n              this.workflowId = response.workflowId || response.id;\n              this.isEditMode = true;\n              this.isDuplicateMode = false;\n              // Update URL without reloading\n              this.router.navigate([], {\n                relativeTo: this.route,\n                queryParams: {\n                  id: this.workflowId\n                },\n                replaceUrl: true\n              });\n            }\n          },\n          error: error => {\n            // Close loading dialog\n            loadingDialog.close();\n            // Check if this is a duplicate name error (adjust based on your API error structure)\n            const errorMessage = error?.error?.message || 'An error occurred while saving the workflow.';\n            const isDuplicateError = errorMessage.toLowerCase().includes('duplicate') || error.status === 409; // 409 is Conflict\n            if (isDuplicateError) {\n              this.dialogService.warning({\n                title: 'Duplicate Workflow',\n                message: 'A workflow with this name already exists. Please choose a different name.',\n                showProceedButton: true,\n                proceedButtonText: 'OK'\n              });\n            } else {\n              this.dialogService.error({\n                title: 'Error',\n                message: errorMessage,\n                showProceedButton: true,\n                proceedButtonText: 'OK'\n              });\n            }\n          }\n        });\n      });\n    }\n    onExit() {\n      this.router.navigate(['/build/workflows']);\n    }\n    onReset() {\n      this.workflowGraphService.clearWorkflow();\n      this.selectedNodeId = null;\n    }\n    onUndo() {\n      console.log('Undo triggered from workflow editor');\n    }\n    onRedo() {\n      console.log('Redo triggered from workflow editor');\n    }\n    onCanvasStateChanged(state) {\n      console.log('Canvas state changed:', state);\n      // Convert canvas nodes back to workflow nodes\n      const workflowNodes = state.nodes.map(node => ({\n        id: node.id,\n        type: node.type,\n        data: node.data,\n        position: node.position\n      }));\n      // Convert canvas edges back to workflow edges\n      const workflowEdges = state.edges.map(edge => ({\n        id: edge.id,\n        source: edge.source,\n        target: edge.target,\n        animated: edge.animated\n      }));\n      // Update the workflow service to sync the state\n      this.workflowGraphService.setNodes(workflowNodes);\n      this.workflowGraphService.setEdges(workflowEdges);\n    }\n    makeWorkFlowPayload() {\n      const formValue = this.workflowForm.getRawValue();\n      const payload = {\n        name: formValue?.name || null,\n        description: formValue?.description || null,\n        workflowAgents: [],\n        workflowConfigs: {\n          levelId: 199,\n          modelRef: [],\n          workflowAgentTools: [],\n          managerLlm: [],\n          topP: formValue?.topP || 0.95,\n          maxToken: formValue?.maxToken || 1500,\n          temperature: formValue?.temperature || 0.3,\n          enableAgenticMemory: false,\n          embeddingModelRef: []\n        },\n        createdBy: this.isEditMode ? formValue?.createdBy : this.tokenStorage.getDaUsername(),\n        modifiedBy: this.tokenStorage.getDaUsername()\n      };\n      const nodes = this.workflowGraphService.getAllNodes();\n      if (nodes.length) {\n        payload['workflowAgents'] = nodes.map((node, index) => ({\n          serial: index + 1,\n          agentId: node.data.agentId\n        }));\n      }\n      if (this.builtInToolValues.length > 0) {\n        const toolEntries = [];\n        this.builtInToolValues.forEach(toolGroup => {\n          toolGroup.parameters.forEach(param => {\n            toolEntries.push({\n              agentId: param.agentId,\n              toolId: toolGroup.toolId,\n              toolParameterId: param.agentToolId || param.toolParameterId,\n              parameterName: param.parameterName,\n              value: param.value\n            });\n          });\n        });\n        payload['workflowConfigs'].workflowAgentTools = toolEntries;\n      }\n      if (formValue?.modelDeploymentName) {\n        const managerModel = {\n          id: formValue.modelRefId,\n          modelDeploymentName: formValue.modelDeploymentName,\n          topP: formValue.topP,\n          maxToken: formValue.maxToken,\n          temperature: formValue.temperature\n        };\n        payload['workflowConfigs'].managerLlm = [managerModel];\n      }\n      if (formValue?.embeddingModelRefs?.length) {\n        payload['workflowConfigs'].embeddingModelRef = [...formValue.embeddingModelRefs];\n        payload['workflowConfigs'].modelRef = [...formValue.embeddingModelRefs];\n      }\n      if (this.workflowId && !this.isModeDuplicate()) {\n        payload['id'] = this.workflowId;\n      }\n      return payload;\n    }\n    onExecute() {\n      // Navigate to workflow execution page\n      if (!this.workflowForm.get('name')?.value) {\n        this.dialogService.warning({\n          title: 'Warning',\n          message: 'Please provide the workflow name before executing the workflow.',\n          showProceedButton: true,\n          proceedButtonText: 'Ok'\n        });\n        return;\n      }\n      const payload = this.makeWorkFlowPayload();\n      if (this.workflowId && !this.isDuplicateMode) {\n        this.workflowService.updateWorkFlow(payload).subscribe(() => {\n          this.router.navigate(['/build/workflows/execute', this.workflowId]);\n        });\n      } else {\n        // For new workflows, save first then navigate\n        this.workflowService.saveWorkFlow(payload).subscribe(response => {\n          this.router.navigate(['/build/workflows/execute', response.workflowId]);\n        });\n        // alert('Please save the workflow before executing it.');\n      }\n    }\n    // Helper method to get form controls easily from the template\n    getControl(name) {\n      return this.workflowForm.get(name);\n    }\n    /**\n     * Check if an agent is already used in the workflow\n     * @param agentId ID of the agent to check\n     */\n    isAgentUsed(agentId) {\n      return this.usedAgentIds.has(agentId);\n    }\n    /**\n     * Update the set of used agent IDs\n     */\n    updateUsedAgentIds() {\n      this.usedAgentIds.clear();\n      this.nodes.forEach(node => {\n        if (node.data && node.data.agentId) {\n          this.usedAgentIds.add(node.data.agentId);\n        }\n      });\n      // Update available agents whenever used agents change\n      this.updateAvailableAgents();\n    }\n    savedEdges = [];\n    onToggleChnage(event) {\n      console.log(event);\n      this.getControl('enableManagerLLM').setValue(event);\n      this.showLlm = event;\n      if (event) {\n        this.savedEdges = this.workflowGraphService.getAllEdges();\n        this.workflowGraphService.setEdges([]);\n      } else {\n        const nodes = this.workflowGraphService.getAllNodes();\n        const edges = this.convertNodesToEdges(nodes);\n        this.workflowGraphService.setEdges(edges);\n        if (!this.savedWorkFlowDetils['managerLlm']) {\n          this.getControl('modelDeploymentName').setValue(null);\n        }\n      }\n    }\n    toggleLlm(show) {\n      this.showLlm = show;\n    }\n    getToolPatchValueForAgent(agentId) {\n      const patchTool = this.workflowData?.workflowConfigs?.workflowAgentTools;\n      if (patchTool?.agentId === agentId) {\n        return {\n          toolId: patchTool.toolId,\n          parameterName: patchTool.parameterName,\n          value: patchTool.value\n        };\n      }\n      return null;\n    }\n    static ɵfac = function WorkflowEditorComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || WorkflowEditorComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.WorkflowGraphService), i0.ɵɵdirectiveInject(i4.WorkflowService), i0.ɵɵdirectiveInject(i5.ReactFlowService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i6.AgentServiceService), i0.ɵɵdirectiveInject(i7.TokenStorageService), i0.ɵɵdirectiveInject(i8.DrawerService), i0.ɵɵdirectiveInject(i9.DialogService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: WorkflowEditorComponent,\n      selectors: [[\"app-workflow-editor\"]],\n      viewQuery: function WorkflowEditorComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5, ViewContainerRef);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.drawerContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.llmSettingsRef = _t.first);\n        }\n      },\n      hostBindings: function WorkflowEditorComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"mousedown\", function WorkflowEditorComponent_mousedown_HostBindingHandler($event) {\n            return ctx.onDocumentClick($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      inputs: {\n        primaryButtonText: \"primaryButtonText\"\n      },\n      features: [i0.ɵɵProvidersFeature([DialogService])],\n      decls: 33,\n      vars: 30,\n      consts: [[\"nodeTemplate\", \"\"], [\"drawerContainer\", \"\"], [\"llmSettingsRef\", \"\"], [1, \"workflow-editor-container\"], [1, \"main-content\"], [1, \"canvas-area\"], [1, \"agent-library-panel\", 3, \"ngClass\"], [1, \"panel-header\", 3, \"click\"], [\"iconSize\", \"16\", \"iconColor\", \"var(--text-secondary)\", 3, \"iconName\"], [1, \"panel-content\"], [1, \"search-section\"], [3, \"formGroup\"], [\"placeholder\", \"Search \\\"Agents\\\"\", \"hoverEffect\", \"glow\", \"pressedEffect\", \"solid\", \"formControlName\", \"agentFilter\"], [\"slot\", \"icon-start\", \"iconName\", \"search\", \"iconColor\", \"var(--color-brand-primary)\", 3, \"iconSize\"], [1, \"agents-list\"], [\"class\", \"agent-item\", \"draggable\", \"true\", 3, \"dragstart\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"no-results-message\", 4, \"ngIf\"], [1, \"create-agent-section\"], [\"label\", \"Create New Agent\", \"variant\", \"primary\", \"size\", \"large\", \"iconName\", \"Plus\", \"iconColor\", \"white\", 3, \"userClick\", \"customStyles\", \"width\"], [1, \"editor-canvas\"], [3, \"canvasDropped\", \"nodeSelected\", \"nodeMoved\", \"nodeRemoved\", \"connectionStarted\", \"connectionCreated\", \"undoAction\", \"redoAction\", \"resetAction\", \"primaryButtonClicked\", \"stateChanged\", \"nodes\", \"edges\", \"navigationHints\", \"fallbackMessage\", \"primaryButtonText\", \"showLeftActions\", \"showHeaderInputs\", \"inputFieldsConfig\", \"agentDetailNameControl\", \"agentDetailControl\"], [\"info\", \"\", \"class\", \"model-info\", 4, \"ngIf\"], [\"actionLeft\", \"\"], [1, \"sidebar-section\", \"llm-toggle\", \"full-width\"], [1, \"llm-toggle-container\"], [1, \"toggle-container\"], [1, \"toggle-label\"], [\"size\", \"small\", \"position\", \"left\", 3, \"checkedChange\", \"animation\", \"checked\"], [\"class\", \"llm-settings\", 4, \"ngIf\"], [\"draggable\", \"true\", 1, \"agent-item\", 3, \"dragstart\"], [1, \"agent-header\"], [1, \"agent-icon-box\"], [\"iconName\", \"User\", \"iconSize\", \"20\", \"iconColor\", \"#ffffff\"], [1, \"agent-name\"], [1, \"agent-count\"], [\"iconName\", \"Users\", \"iconSize\", \"16\", \"iconColor\", \"#9CA3AF\"], [1, \"count-text\"], [1, \"agent-description\"], [\"class\", \"agent-tags\", 4, \"ngIf\"], [1, \"agent-actions\"], [\"label\", \"Preview\", \"size\", \"small\", \"variant\", \"secondary\", 1, \"preview-btn\", 3, \"userClick\", \"pill\"], [1, \"agent-tags\"], [\"class\", \"agent-tag\", 4, \"ngFor\", \"ngForOf\"], [1, \"agent-tag\"], [1, \"no-results-message\"], [1, \"no-results-content\"], [\"iconName\", \"Search\", \"iconSize\", \"24\", \"iconColor\", \"#9CA3AF\"], [3, \"deleteNode\", \"moveNode\", \"nodeSelected\", \"startConnection\", \"nodePositionChanged\", \"toolValuesChanged\", \"node\", \"selected\", \"patchData\"], [\"info\", \"\", 1, \"model-info\"], [1, \"llm-settings\"], [1, \"setting-item\"], [\"dropdownTitle\", \"choose agent\", 3, \"options\", \"formControl\"], [3, \"min\", \"max\", \"step\", \"formControl\"], [\"type\", \"number\", \"min\", \"0\", \"max\", \"1\", \"step\", \"0.01\", 1, \"setting-input\", 3, \"formControl\"], [\"type\", \"number\", \"min\", \"0\", 1, \"setting-input\", 3, \"formControl\"], [1, \"token-container\"], [1, \"tokens-used\"], [\"type\", \"number\", \"min\", \"1\", 1, \"setting-input\", 3, \"formControl\"]],\n      template: function WorkflowEditorComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5)(3, \"div\", 6)(4, \"div\", 7);\n          i0.ɵɵlistener(\"click\", function WorkflowEditorComponent_Template_div_click_4_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.togglePanel());\n          });\n          i0.ɵɵelementStart(5, \"h3\");\n          i0.ɵɵtext(6, \"Agent Library\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(7, \"ava-icon\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 9)(9, \"div\", 10)(10, \"form\", 11)(11, \"ava-textbox\", 12);\n          i0.ɵɵelement(12, \"ava-icon\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 14);\n          i0.ɵɵtemplate(14, WorkflowEditorComponent_div_14_Template, 15, 4, \"div\", 15)(15, WorkflowEditorComponent_div_15_Template, 5, 0, \"div\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"div\", 17)(17, \"ava-button\", 18);\n          i0.ɵɵlistener(\"userClick\", function WorkflowEditorComponent_Template_ava_button_userClick_17_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onCreateNewAgent());\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(18, \"div\", 19)(19, \"app-canvas-board\", 20);\n          i0.ɵɵlistener(\"canvasDropped\", function WorkflowEditorComponent_Template_app_canvas_board_canvasDropped_19_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onCanvasDropped($event));\n          })(\"nodeSelected\", function WorkflowEditorComponent_Template_app_canvas_board_nodeSelected_19_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onNodeSelected($event));\n          })(\"nodeMoved\", function WorkflowEditorComponent_Template_app_canvas_board_nodeMoved_19_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onNodeMoved($event));\n          })(\"nodeRemoved\", function WorkflowEditorComponent_Template_app_canvas_board_nodeRemoved_19_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onDeleteNode($event));\n          })(\"connectionStarted\", function WorkflowEditorComponent_Template_app_canvas_board_connectionStarted_19_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onStartConnection($event));\n          })(\"connectionCreated\", function WorkflowEditorComponent_Template_app_canvas_board_connectionCreated_19_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onConnectionCreated($event));\n          })(\"undoAction\", function WorkflowEditorComponent_Template_app_canvas_board_undoAction_19_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onUndo());\n          })(\"redoAction\", function WorkflowEditorComponent_Template_app_canvas_board_redoAction_19_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onRedo());\n          })(\"resetAction\", function WorkflowEditorComponent_Template_app_canvas_board_resetAction_19_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onReset());\n          })(\"primaryButtonClicked\", function WorkflowEditorComponent_Template_app_canvas_board_primaryButtonClicked_19_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onExecute());\n          })(\"stateChanged\", function WorkflowEditorComponent_Template_app_canvas_board_stateChanged_19_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onCanvasStateChanged($event));\n          });\n          i0.ɵɵtemplate(20, WorkflowEditorComponent_ng_template_20_Template, 1, 3, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(22, WorkflowEditorComponent_div_22_Template, 2, 1, \"div\", 21);\n          i0.ɵɵelementContainerStart(23, 22);\n          i0.ɵɵelementStart(24, \"div\", 23)(25, \"div\", 24)(26, \"div\", 25)(27, \"span\", 26);\n          i0.ɵɵtext(28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"ava-toggle\", 27);\n          i0.ɵɵlistener(\"checkedChange\", function WorkflowEditorComponent_Template_ava_toggle_checkedChange_29_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onToggleChnage($event));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(30, WorkflowEditorComponent_div_30_Template, 33, 20, \"div\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelement(31, \"div\", null, 1);\n        }\n        if (rf & 2) {\n          let tmp_22_0;\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"collapsed\", ctx.isPanelCollapsed);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(27, _c2, !ctx.isPanelCollapsed));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"iconName\", ctx.isPanelCollapsed ? \"ChevronDown\" : \"ChevronUp\");\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"hidden\", ctx.isPanelCollapsed);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.searchForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"iconSize\", 16);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredAgents);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.availableAgents.length === 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"customStyles\", i0.ɵɵpureFunction0(29, _c3))(\"width\", \"100%\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"nodes\", ctx.canvasNodes)(\"edges\", ctx.canvasEdges)(\"navigationHints\", ctx.navigationHints)(\"fallbackMessage\", ctx.workFlowLabels.fallbackMessage)(\"primaryButtonText\", ctx.workFlowLabels.execute)(\"showLeftActions\", true)(\"showHeaderInputs\", true)(\"inputFieldsConfig\", ctx.inputFieldsConfig)(\"agentDetailNameControl\", ctx.getControl(\"name\"))(\"agentDetailControl\", ctx.getControl(\"description\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.getControl(\"enableManagerLLM\").value && ((tmp_22_0 = ctx.getControl(\"modelDeploymentName\")) == null ? null : tmp_22_0.value));\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.workFlowLabels.managerLLMToggleLabel);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"animation\", true)(\"checked\", !!ctx.getControl(\"enableManagerLLM\").value);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.getControl(\"enableManagerLLM\").value && ctx.showLlm);\n        }\n      },\n      dependencies: [CommonModule, i10.NgClass, i10.NgForOf, i10.NgIf, ReactiveFormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinValidator, i1.MaxValidator, i1.FormControlDirective, i1.FormGroupDirective, i1.FormControlName, RouterModule, AgentNodeComponent, DragDropModule, CanvasBoardComponent, ToggleComponent, ButtonComponent, IconComponent, AvaTextboxComponent, SliderComponent, DropdownComponent],\n      styles: [\".workflow-editor-container[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  height: 87vh;\\n  display: flex;\\n  flex-direction: column;\\n  background-color: var(--background-primary);\\n  overflow: hidden;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .header-nav[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  min-height: 60px;\\n  background-color: var(--card-bg);\\n  border-bottom: 1px solid var(--border-color);\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .header-nav[_ngcontent-%COMP%]   .breadcrumb[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  flex: 1;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .header-nav[_ngcontent-%COMP%]   .breadcrumb[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--text-secondary);\\n  cursor: pointer;\\n  transition: color 0.2s ease;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .header-nav[_ngcontent-%COMP%]   .breadcrumb[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]:hover {\\n  color: var(--text-primary);\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .header-nav[_ngcontent-%COMP%]   .breadcrumb[_ngcontent-%COMP%]   .nav-item.active[_ngcontent-%COMP%] {\\n  color: var(--text-primary);\\n  font-weight: 500;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .header-nav[_ngcontent-%COMP%]   .breadcrumb[_ngcontent-%COMP%]   .separator[_ngcontent-%COMP%] {\\n  color: var(--text-tertiary);\\n  font-size: 14px;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .header-nav[_ngcontent-%COMP%]   .breadcrumb[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: var(--text-secondary);\\n  cursor: pointer;\\n  padding: 4px;\\n  border-radius: 8px;\\n  margin-left: 16px;\\n  transition: all 0.2s ease;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .header-nav[_ngcontent-%COMP%]   .breadcrumb[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]:hover {\\n  background-color: var(--hover-bg);\\n  color: var(--text-primary);\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .header-nav[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .header-nav[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-group[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: 1px solid var(--border-color);\\n  color: var(--text-secondary);\\n  cursor: pointer;\\n  padding: 8px;\\n  border-radius: 8px;\\n  transition: all 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .header-nav[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-group[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover {\\n  background-color: var(--hover-bg);\\n  border-color: var(--border-hover);\\n  color: var(--text-primary);\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .header-nav[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-group[_ngcontent-%COMP%]   .run-btn[_ngcontent-%COMP%] {\\n  background: var(--dashboard-primary);\\n  border: 1px solid var(--dashboard-primary);\\n  color: white;\\n  cursor: pointer;\\n  padding: 8px 16px;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  transition: all 0.2s ease;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .header-nav[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .action-group[_ngcontent-%COMP%]   .run-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--dashboard-primary-hover);\\n  border-color: var(--dashboard-primary-hover);\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .delete-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 40px;\\n  right: 25px;\\n  width: 20px;\\n  height: 20px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background-color: #ef4444;\\n  border: none;\\n  border-radius: 50%;\\n  padding: 0;\\n  color: white;\\n  cursor: pointer;\\n  z-index: 999;\\n  transition: all 0.2s ease;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .delete-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #dc2626;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .llm-settings[_ngcontent-%COMP%] {\\n  margin-top: 24px;\\n  overflow-y: auto;\\n  max-height: 348px;\\n  box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.2509803922);\\n  background-color: white;\\n  padding: 16px 15px;\\n  border: 1px solid #d4d4d4;\\n  border-radius: 8px;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .llm-settings[_ngcontent-%COMP%]   .setting-item[_ngcontent-%COMP%] {\\n  margin: 10px 0 24px 0;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .llm-settings[_ngcontent-%COMP%]   .setting-item[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  margin-bottom: 12px;\\n  font-size: 16px;\\n  font-weight: 500;\\n  color: var(--text-color);\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .llm-settings[_ngcontent-%COMP%]   .setting-item[_ngcontent-%COMP%]   .slider-with-value[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .llm-settings[_ngcontent-%COMP%]   .setting-item[_ngcontent-%COMP%]   .slider-with-value[_ngcontent-%COMP%]   .setting-slider[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n  height: 6px;\\n  appearance: none;\\n  background: var(--agent-slider-bg);\\n  border-radius: 3px;\\n  outline: none;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .llm-settings[_ngcontent-%COMP%]   .setting-item[_ngcontent-%COMP%]   .slider-with-value[_ngcontent-%COMP%]   .setting-slider[_ngcontent-%COMP%]::-webkit-slider-thumb {\\n  appearance: none;\\n  width: 18px;\\n  height: 18px;\\n  border-radius: 50%;\\n  background: var(--agent-slider-thumb-bg);\\n  cursor: pointer;\\n  box-shadow: var(--agent-slider-thumb-shadow);\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .llm-settings[_ngcontent-%COMP%]   .setting-item[_ngcontent-%COMP%]   .slider-with-value[_ngcontent-%COMP%]   .setting-slider[_ngcontent-%COMP%]::-moz-range-thumb {\\n  width: 18px;\\n  height: 18px;\\n  border-radius: 50%;\\n  background: var(--agent-slider-thumb-bg);\\n  cursor: pointer;\\n  border: none;\\n  box-shadow: var(--agent-slider-thumb-shadow);\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .llm-settings[_ngcontent-%COMP%]   .setting-item[_ngcontent-%COMP%]   .slider-with-value[_ngcontent-%COMP%]   .value-display[_ngcontent-%COMP%] {\\n  min-width: 40px;\\n  height: 40px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 14px;\\n  color: var(--form-input-color);\\n  background: var(--form-input-bg);\\n  border-radius: 6px;\\n  padding: 0 10px;\\n  border: 1px solid var(--form-input-border);\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .llm-settings[_ngcontent-%COMP%]   .setting-item[_ngcontent-%COMP%]   .setting-input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px;\\n  border: 1px solid var(--form-input-border);\\n  border-radius: 6px;\\n  font-size: 14px;\\n  background-color: var(--form-input-bg);\\n  color: var(--form-input-color);\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .llm-settings[_ngcontent-%COMP%]   .setting-item[_ngcontent-%COMP%]   .token-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .llm-settings[_ngcontent-%COMP%]   .setting-item[_ngcontent-%COMP%]   .token-container[_ngcontent-%COMP%]   .tokens-used[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 0;\\n  top: -20px;\\n  font-size: 12px;\\n  color: var(--text-secondary);\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .llm-toggle[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  flex: 1;\\n  overflow: hidden;\\n  height: calc(100vh - 60px);\\n  max-height: calc(100vh - 60px);\\n  position: relative;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background-color: var(--background-primary);\\n  overflow: hidden;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .agent-library-panel[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 20px;\\n  left: 20px;\\n  width: 380px;\\n  height: calc(100vh - 160px);\\n  background-color: #ffffff;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n  z-index: 10;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .agent-library-panel.collapsed[_ngcontent-%COMP%] {\\n  width: auto;\\n  min-width: 200px;\\n  height: auto;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .agent-library-panel.collapsed[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%] {\\n  padding: 12px 16px;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  background-color: #ffffff;\\n  border-bottom: none;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .agent-library-panel.collapsed[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: var(--text-primary);\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .agent-library-panel.collapsed[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   ava-icon[_ngcontent-%COMP%] {\\n  transition: transform 0.3s ease;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .agent-library-panel.collapsed[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .agent-library-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  background-color: #ffffff;\\n  transition: all 0.3s ease;\\n  flex-shrink: 0;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .agent-library-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]:hover {\\n  background-color: #f9fafb;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .agent-library-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 18px;\\n  font-weight: 700;\\n  color: #111827;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .agent-library-panel[_ngcontent-%COMP%]   .panel-header[_ngcontent-%COMP%]   ava-icon[_ngcontent-%COMP%] {\\n  transition: transform 0.3s ease;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .agent-library-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n  flex: 1;\\n  overflow: hidden;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  display: flex;\\n  flex-direction: column;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .agent-library-panel[_ngcontent-%COMP%]   .panel-content.hidden[_ngcontent-%COMP%] {\\n  max-height: 0;\\n  opacity: 0;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .agent-library-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   form[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .agent-library-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]     ava-textbox .textbox-container {\\n  border-radius: 8px;\\n  border: 1px solid #e5e7eb;\\n  background-color: #f9fafb;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .agent-library-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]     ava-textbox .textbox-container:focus-within {\\n  border-color: #3b82f6;\\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .agent-library-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]     ava-textbox .textbox-container input {\\n  background-color: transparent;\\n  color: #111827;\\n  font-size: 14px;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .agent-library-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]     ava-textbox .textbox-container input::placeholder {\\n  color: #9ca3af;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .agent-library-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]   .agents-list[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  overflow-x: hidden;\\n  scrollbar-width: none;\\n  -ms-overflow-style: none;\\n  margin-bottom: 16px;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .agent-library-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]   .agents-list[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none;\\n  width: 0;\\n  height: 0;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .agent-library-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]   .agents-list[_ngcontent-%COMP%]   .agent-item[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  padding: 16px;\\n  margin-top: 16px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .agent-library-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]   .agents-list[_ngcontent-%COMP%]   .agent-item[_ngcontent-%COMP%]:hover {\\n  border-color: #3b82f6;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  transform: translateY(-1px);\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .agent-library-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]   .agents-list[_ngcontent-%COMP%]   .agent-item[_ngcontent-%COMP%]   .agent-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  margin-bottom: 8px;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .agent-library-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]   .agents-list[_ngcontent-%COMP%]   .agent-item[_ngcontent-%COMP%]   .agent-header[_ngcontent-%COMP%]   .agent-icon-box[_ngcontent-%COMP%] {\\n  background: var(--Global-colors-Royal-blue-50, #e9effd);\\n  border-radius: 999px;\\n  width: 36px;\\n  height: 36px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .agent-library-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]   .agents-list[_ngcontent-%COMP%]   .agent-item[_ngcontent-%COMP%]   .agent-header[_ngcontent-%COMP%]   .agent-name[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: var(--Brand-Neutral-n-800, #3b3f46) !important;\\n  flex: 1;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .agent-library-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]   .agents-list[_ngcontent-%COMP%]   .agent-item[_ngcontent-%COMP%]   .agent-header[_ngcontent-%COMP%]   .agent-count[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  font-size: 13px;\\n  color: #6b7280;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .agent-library-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]   .agents-list[_ngcontent-%COMP%]   .agent-item[_ngcontent-%COMP%]   .agent-header[_ngcontent-%COMP%]   .agent-count[_ngcontent-%COMP%]   .count-text[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  font-size: 16px;\\n  color: var(--Colors-Text-primary, #4c515b);\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .agent-library-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]   .agents-list[_ngcontent-%COMP%]   .agent-item[_ngcontent-%COMP%]   .agent-description[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  font-size: 14px;\\n  color: var(--Colors-Text-secondary, #616874);\\n  line-height: 1.5;\\n  display: -webkit-box;\\n  -webkit-line-clamp: 2;\\n  -webkit-box-orient: vertical;\\n  overflow: hidden;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .agent-library-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]   .agents-list[_ngcontent-%COMP%]   .agent-item[_ngcontent-%COMP%]   .agent-tags[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 6px;\\n  margin-bottom: 12px;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .agent-library-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]   .agents-list[_ngcontent-%COMP%]   .agent-item[_ngcontent-%COMP%]   .agent-tags[_ngcontent-%COMP%]   .agent-tag[_ngcontent-%COMP%] {\\n  background-color: #f3f4f6;\\n  color: #374151;\\n  font-size: 12px;\\n  padding: 4px 8px;\\n  border-radius: 8px;\\n  font-weight: 500;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .agent-library-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]   .agents-list[_ngcontent-%COMP%]   .agent-item[_ngcontent-%COMP%]   .agent-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .agent-library-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]   .agents-list[_ngcontent-%COMP%]   .agent-item[_ngcontent-%COMP%]   .agent-actions[_ngcontent-%COMP%]   .preview-btn[_ngcontent-%COMP%]     .button-container {\\n  border-radius: 8px;\\n  font-size: 12px;\\n  padding: 6px 12px;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .agent-library-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]   .agents-list[_ngcontent-%COMP%]   .no-results-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 2rem 1rem;\\n  text-align: center;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .agent-library-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]   .agents-list[_ngcontent-%COMP%]   .no-results-message[_ngcontent-%COMP%]   .no-results-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .agent-library-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]   .agents-list[_ngcontent-%COMP%]   .no-results-message[_ngcontent-%COMP%]   .no-results-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #6b7280;\\n  font-size: 14px;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .agent-library-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]   .create-agent-section[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  flex-shrink: 0;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .agent-library-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]   .create-agent-section[_ngcontent-%COMP%]     ava-button .button-container {\\n  border-radius: 8px;\\n  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\\n  border: none;\\n  font-weight: 600;\\n  font-size: 14px;\\n  padding: 12px 20px;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .agent-library-panel[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%]   .create-agent-section[_ngcontent-%COMP%]     ava-button .button-container:hover {\\n  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .action-buttons-section[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 20px;\\n  right: 20px;\\n  z-index: 10;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .action-buttons-section[_ngcontent-%COMP%]   .action-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .action-buttons-section[_ngcontent-%COMP%]   .action-group[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border: 1px solid #e5e7eb;\\n  color: var(--text-secondary);\\n  cursor: pointer;\\n  padding: 8px;\\n  border-radius: 8px;\\n  transition: all 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .action-buttons-section[_ngcontent-%COMP%]   .action-group[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #f9fafb;\\n  border-color: #3b82f6;\\n  color: var(--text-primary);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .action-buttons-section[_ngcontent-%COMP%]   .action-group[_ngcontent-%COMP%]   .run-btn[_ngcontent-%COMP%] {\\n  background: var(--dashboard-primary);\\n  border: 1px solid var(--dashboard-primary);\\n  color: white;\\n  cursor: pointer;\\n  padding: 8px 16px;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  transition: all 0.2s ease;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .action-buttons-section[_ngcontent-%COMP%]   .action-group[_ngcontent-%COMP%]   .run-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--dashboard-primary-hover);\\n  border-color: var(--dashboard-primary-hover);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .editor-canvas[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  background-color: var(--background-primary);\\n  position: relative;\\n  overflow: hidden;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .editor-canvas[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%] {\\n  height: 100%;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .editor-canvas[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  margin-bottom: 12px;\\n  font-size: 16px;\\n  font-weight: 500;\\n  color: var(--text-color);\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .editor-canvas[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]   h3.description-label[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .editor-canvas[_ngcontent-%COMP%]     app-canvas-board {\\n  width: 100%;\\n  height: 100%;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .editor-canvas[_ngcontent-%COMP%]     app-canvas-board .header-inputs-section {\\n  position: absolute;\\n  top: 20px;\\n  left: 420px;\\n  z-index: 10;\\n  max-width: calc(100% - 440px);\\n  overflow: visible;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .editor-canvas[_ngcontent-%COMP%]     app-canvas-board .header-inputs-section .header-inputs-container {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  flex-wrap: nowrap;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .editor-canvas[_ngcontent-%COMP%]     app-canvas-board .llm-toggle-container {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 8px 16px;\\n  background-color: #ffffff;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  margin-left: 16px;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .editor-canvas[_ngcontent-%COMP%]     app-canvas-board .llm-toggle-container .toggle-container {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  gap: 12px;\\n  width: 100%;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .editor-canvas[_ngcontent-%COMP%]     app-canvas-board .llm-toggle-container .toggle-container .toggle-label {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #111827;\\n  white-space: nowrap;\\n  cursor: pointer;\\n}\\n.workflow-editor-container[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .canvas-area[_ngcontent-%COMP%]   .editor-canvas[_ngcontent-%COMP%]     app-canvas-board .llm-toggle-container .toggle-container   ava-toggle .toggle-container {\\n  border-radius: 8px;\\n}\\n\\n.llm-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n}\\n\\n.model-info[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: fit-content;\\n  margin-top: 20px;\\n  height: 51px;\\n  opacity: 1;\\n  top: 50px;\\n  left: 933px;\\n  border-radius: 8px;\\n  border-width: 1px;\\n  padding-top: 8px;\\n  padding-right: 16px;\\n  padding-bottom: 8px;\\n  padding-left: 16px;\\n  gap: 16px;\\n  background-color: white;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  border: 1px solid #DADCE7;\\n}\\n\\n  .agent-icon-box svg {\\n  stroke: var(--Global-colors-Royal-blue-700, #1a46a7);\\n}\\n\\n  .agent-actions .ava-button.secondary {\\n  --button-effect-color: var(--button-variant-secondary-effect-color);\\n  border-radius: 999px !important;\\n  background-color: var(--Global-colors-Royal-blue-50, #e9effd) !important;\\n  border: none !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3Byb2plY3RzL3NoYXJlZC9wYWdlcy93b3JrZmxvd3Mvd29ya2Zsb3ctZWRpdG9yL3dvcmtmbG93LWVkaXRvci5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFnQkE7RUFDRSxrQkFBQTtFQUNBLFlBQUE7RUFDQSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSwyQ0FBQTtFQUNBLGdCQUFBO0FBZkY7QUFpQkU7RUFDRSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EsZ0NBQUE7RUFDQSw0Q0FBQTtBQWZKO0FBaUJJO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsUUFBQTtFQUNBLE9BQUE7QUFmTjtBQWlCTTtFQUNFLGVBQUE7RUFDQSw0QkFBQTtFQUNBLGVBQUE7RUFDQSwyQkFBQTtBQWZSO0FBaUJRO0VBQ0UsMEJBQUE7QUFmVjtBQWtCUTtFQUNFLDBCQUFBO0VBQ0EsZ0JBQUE7QUFoQlY7QUFvQk07RUFDRSwyQkFBQTtFQUNBLGVBQUE7QUFsQlI7QUFxQk07RUFDRSxnQkFBQTtFQUNBLFlBQUE7RUFDQSw0QkFBQTtFQUNBLGVBQUE7RUFDQSxZQUFBO0VBQ0Esa0JBQUE7RUFDQSxpQkFBQTtFQUNBLHlCQUFBO0FBbkJSO0FBcUJRO0VBQ0UsaUNBQUE7RUFDQSwwQkFBQTtBQW5CVjtBQXlCTTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFFBQUE7QUF2QlI7QUF5QlE7RUFDRSxnQkFBQTtFQUNBLHFDQUFBO0VBQ0EsNEJBQUE7RUFDQSxlQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EseUJBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtBQXZCVjtBQXlCVTtFQUNFLGlDQUFBO0VBQ0EsaUNBQUE7RUFDQSwwQkFBQTtBQXZCWjtBQTJCUTtFQUNFLG9DQUFBO0VBQ0EsMENBQUE7RUFDQSxZQUFBO0VBQ0EsZUFBQTtFQUNBLGlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxRQUFBO0VBQ0EseUJBQUE7QUF6QlY7QUEyQlU7RUFDRSwwQ0FBQTtFQUNBLDRDQUFBO0FBekJaO0FBZ0NFO0VBQ0Usa0JBQUE7RUFDQSxTQUFBO0VBQ0EsV0FBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSx5QkFBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtFQUNBLFVBQUE7RUFDQSxZQUFBO0VBQ0EsZUFBQTtFQUNBLFlBQUE7RUFDQSx5QkFBQTtBQTlCSjtBQWlDRTtFQUNFLHlCQUFBO0FBL0JKO0FBa0NFO0VBQ0UsZ0JBQUE7RUFDQSxnQkFBQTtFQUNBLGlCQUFBO0VBQ0Esd0RBQUE7RUFDQSx1QkFBQTtFQUNBLGtCQUFBO0VBQ0EseUJBQUE7RUFDQSxrQkFBQTtBQWhDSjtBQWtDSTtFQUNFLHFCQUFBO0FBaENOO0FBa0NNO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0Esd0JBQUE7QUFoQ1I7QUFtQ007RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxTQUFBO0FBakNSO0FBbUNRO0VBQ0UsWUFBQTtFQUNBLFdBQUE7RUFFQSxnQkFBQTtFQUNBLGtDQUFBO0VBQ0Esa0JBQUE7RUFDQSxhQUFBO0FBakNWO0FBbUNVO0VBRUUsZ0JBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0Esd0NBQUE7RUFDQSxlQUFBO0VBQ0EsNENBQUE7QUFqQ1o7QUFvQ1U7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0Esd0NBQUE7RUFDQSxlQUFBO0VBQ0EsWUFBQTtFQUNBLDRDQUFBO0FBbENaO0FBc0NRO0VBQ0UsZUFBQTtFQUNBLFlBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLGVBQUE7RUFDQSw4QkFBQTtFQUNBLGdDQUFBO0VBQ0Esa0JBQUE7RUFDQSxlQUFBO0VBQ0EsMENBQUE7QUFwQ1Y7QUF3Q007RUFDRSxXQUFBO0VBQ0EsYUFBQTtFQUNBLDBDQUFBO0VBQ0Esa0JBQUE7RUFDQSxlQUFBO0VBQ0Esc0NBQUE7RUFDQSw4QkFBQTtBQXRDUjtBQXlDTTtFQUNFLGtCQUFBO0FBdkNSO0FBeUNRO0VBQ0Usa0JBQUE7RUFDQSxRQUFBO0VBQ0EsVUFBQTtFQUNBLGVBQUE7RUFDQSw0QkFBQTtBQXZDVjtBQTZDRTtFQUNFLFdBQUE7QUEzQ0o7QUE4Q0U7RUFDRSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxPQUFBO0VBQ0EsZ0JBQUE7RUFDQSwwQkFBQTtFQUNBLDhCQUFBO0VBQ0Esa0JBQUE7QUE1Q0o7QUE4Q0k7RUFDRSxPQUFBO0VBQ0EsMkNBQUE7RUFDQSxnQkFBQTtFQUNBLGlEQUFBO0VBQ0Esa0JBQUE7QUE1Q047QUErQ007RUFDRSxrQkFBQTtFQUNBLFNBQUE7RUFDQSxVQUFBO0VBQ0EsWUFBQTtFQUNBLDJCQUFBO0VBQ0EseUJBQUE7RUFDQSx5QkFBQTtFQUNBLGtCQUFBO0VBQ0EsMENBQUE7RUFDQSxXQUFBO0VBQ0EsaURBQUE7RUFDQSxnQkFBQTtFQUNBLGFBQUE7RUFDQSxzQkFBQTtBQTdDUjtBQWdEUTtFQUNFLFdBQUE7RUFDQSxnQkFBQTtFQUNBLFlBQUE7QUE5Q1Y7QUFnRFU7RUFDRSxrQkFBQTtFQUNBLGVBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSw4QkFBQTtFQUNBLHlCQUFBO0VBQ0EsbUJBQUE7QUE5Q1o7QUFnRFk7RUFDRSxTQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsMEJBQUE7QUE5Q2Q7QUFpRFk7RUFDRSwrQkFBQTtBQS9DZDtBQW1EVTtFQUNFLGFBQUE7QUFqRFo7QUFxRFE7RUFDRSxlQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsOEJBQUE7RUFDQSx5QkFBQTtFQUNBLHlCQUFBO0VBQ0EsY0FBQTtBQW5EVjtBQXFEVTtFQUNFLHlCQUFBO0FBbkRaO0FBc0RVO0VBQ0UsU0FBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7QUFwRFo7QUF1RFU7RUFDRSwrQkFBQTtBQXJEWjtBQXlEUTtFQUNFLGdCQUFBO0VBQ0EsT0FBQTtFQUNBLGdCQUFBO0VBQ0EsaURBQUE7RUFDQSxhQUFBO0VBQ0Esc0JBQUE7QUF2RFY7QUF5RFU7RUFDRSxhQUFBO0VBQ0EsVUFBQTtBQXZEWjtBQTJEWTtFQUNFLFdBQUE7QUF6RGQ7QUE4RGM7RUFDRSxrQkFBQTtFQUNBLHlCQUFBO0VBQ0EseUJBQUE7QUE1RGhCO0FBOERnQjtFQUNFLHFCQUFBO0VBQ0EsNkNBQUE7QUE1RGxCO0FBK0RnQjtFQUNFLDZCQUFBO0VBQ0EsY0FBQTtFQUNBLGVBQUE7QUE3RGxCO0FBK0RrQjtFQUNFLGNBQUE7QUE3RHBCO0FBb0VVO0VBQ0UsT0FBQTtFQUNBLGdCQUFBO0VBQ0Esa0JBQUE7RUFyWFYscUJBQUE7RUFHQSx3QkFBQTtFQW9YVSxtQkFBQTtBQWpFWjtBQTdURTtFQUNFLGFBQUE7RUFDQSxRQUFBO0VBQ0EsU0FBQTtBQStUSjtBQThEWTtFQUNFLHlCQUFBO0VBQ0EseUJBQUE7RUFDQSxrQkFBQTtFQUNBLGFBQUE7RUFDQSxnQkFBQTtFQUNBLGVBQUE7RUFDQSx5QkFBQTtFQUNBLHlDQUFBO0FBNURkO0FBOERjO0VBQ0UscUJBQUE7RUFDQSx5Q0FBQTtFQUNBLDJCQUFBO0FBNURoQjtBQStEYztFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFNBQUE7RUFDQSxrQkFBQTtBQTdEaEI7QUErRGdCO0VBQ0UsdURBQUE7RUFDQSxvQkFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxjQUFBO0FBN0RsQjtBQWdFZ0I7RUFDRSxTQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EscURBQUE7RUFDQSxPQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLHVCQUFBO0FBOURsQjtBQWlFZ0I7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxRQUFBO0VBQ0EsZUFBQTtFQUNBLGNBQUE7QUEvRGxCO0FBaUVrQjtFQUNFLGdCQUFBO0VBQ0EsZUFBQTtFQUNBLDBDQUFBO0FBL0RwQjtBQW9FYztFQUNFLGtCQUFBO0VBQ0EsZUFBQTtFQUNBLDRDQUFBO0VBQ0EsZ0JBQUE7RUFDQSxvQkFBQTtFQUNBLHFCQUFBO0VBQ0EsNEJBQUE7RUFDQSxnQkFBQTtBQWxFaEI7QUFxRWM7RUFDRSxhQUFBO0VBQ0EsZUFBQTtFQUNBLFFBQUE7RUFDQSxtQkFBQTtBQW5FaEI7QUFxRWdCO0VBQ0UseUJBQUE7RUFDQSxjQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtBQW5FbEI7QUF1RWM7RUFDRSxhQUFBO0VBQ0EseUJBQUE7QUFyRWhCO0FBd0VrQjtFQUNFLGtCQUFBO0VBQ0EsZUFBQTtFQUNBLGlCQUFBO0FBdEVwQjtBQTRFWTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0Esa0JBQUE7RUFDQSxrQkFBQTtBQTFFZDtBQTRFYztFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQUFBO0VBQ0EsV0FBQTtBQTFFaEI7QUE0RWdCO0VBQ0UsU0FBQTtFQUNBLGNBQUE7RUFDQSxlQUFBO0FBMUVsQjtBQWdGVTtFQUNFLHlCQUFBO0VBQ0EsY0FBQTtBQTlFWjtBQWtGYztFQUNFLGtCQUFBO0VBQ0EsNkRBQUE7RUFDQSxZQUFBO0VBQ0EsZ0JBQUE7RUFDQSxlQUFBO0VBQ0Esa0JBQUE7QUFoRmhCO0FBa0ZnQjtFQUNFLDZEQUFBO0VBQ0EsMkJBQUE7RUFDQSw4Q0FBQTtBQWhGbEI7QUF5Rk07RUFDRSxrQkFBQTtFQUNBLFNBQUE7RUFDQSxXQUFBO0VBQ0EsV0FBQTtBQXZGUjtBQXlGUTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFFBQUE7QUF2RlY7QUF5RlU7RUFDRSxtQkFBQTtFQUNBLHlCQUFBO0VBQ0EsNEJBQUE7RUFDQSxlQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EseUJBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLHlDQUFBO0FBdkZaO0FBeUZZO0VBQ0UseUJBQUE7RUFDQSxxQkFBQTtFQUNBLDBCQUFBO0VBQ0EseUNBQUE7QUF2RmQ7QUEyRlU7RUFDRSxvQ0FBQTtFQUNBLDBDQUFBO0VBQ0EsWUFBQTtFQUNBLGVBQUE7RUFDQSxpQkFBQTtFQUNBLGtCQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsUUFBQTtFQUNBLHlCQUFBO0VBQ0EseUNBQUE7QUF6Rlo7QUEyRlk7RUFDRSwwQ0FBQTtFQUNBLDRDQUFBO0VBQ0EseUNBQUE7QUF6RmQ7QUFnR007RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLDJDQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtBQTlGUjtBQWdHUTtFQUVFLFlBQUE7QUEvRlY7QUFpR1U7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSx3QkFBQTtBQS9GWjtBQWlHWTtFQUNFLGdCQUFBO0FBL0ZkO0FBcUdRO0VBQ0UsV0FBQTtFQUNBLFlBQUE7QUFuR1Y7QUFzR1U7RUFDRSxrQkFBQTtFQUNBLFNBQUE7RUFDQSxXQUFBO0VBQ0EsV0FBQTtFQUNBLDZCQUFBO0VBQ0EsaUJBQUE7QUFwR1o7QUFzR1k7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxTQUFBO0VBQ0EsaUJBQUE7QUFwR2Q7QUF5R1U7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxTQUFBO0VBQ0EsaUJBQUE7RUFDQSx5QkFBQTtFQUNBLHlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxpQkFBQTtBQXZHWjtBQXlHWTtFQUNFLGFBQUE7RUFDQSw4QkFBQTtFQUNBLG1CQUFBO0VBQ0EsU0FBQTtFQUNBLFdBQUE7QUF2R2Q7QUF5R2M7RUFDRSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EsbUJBQUE7RUFDQSxlQUFBO0FBdkdoQjtBQTRHZ0I7RUFDRSxrQkFBQTtBQTFHbEI7O0FBcUhBO0VBQ0UsYUFBQTtFQUNBLHlCQUFBO0FBbEhGOztBQXFIQTtFQUNFLGtCQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtFQUNBLFlBQUE7RUFDQSxVQUFBO0VBQ0EsU0FBQTtFQUNBLFdBQUE7RUFDQSxrQkFBQTtFQUNBLGlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxtQkFBQTtFQUNBLG1CQUFBO0VBQ0Esa0JBQUE7RUFDQSxTQUFBO0VBQ0EsdUJBQUE7RUFDQSxTQUFBO0VBQ0EsMkJBQUE7RUFDQSx5QkFBQTtBQWxIRjs7QUFzSEE7RUFDRSxvREFBQTtBQW5IRjs7QUFzSEE7RUFDRSxtRUFBQTtFQUNBLCtCQUFBO0VBQ0Esd0VBQUE7RUFDQSx1QkFBQTtBQW5IRiIsInNvdXJjZXNDb250ZW50IjpbIi8vIFV0aWxpdHkgbWl4aW4gZm9yIGhpZGluZyBzY3JvbGxiYXJzIHdoaWxlIGtlZXBpbmcgc2Nyb2xsIGZ1bmN0aW9uYWxpdHlcclxuQG1peGluIGhpZGUtc2Nyb2xsYmFyIHtcclxuICAvLyBIaWRlIHNjcm9sbGJhciBmb3Igd2Via2l0IGJyb3dzZXJzIChDaHJvbWUsIFNhZmFyaSwgRWRnZSlcclxuICAmOjotd2Via2l0LXNjcm9sbGJhciB7XHJcbiAgICBkaXNwbGF5OiBub25lO1xyXG4gICAgd2lkdGg6IDA7XHJcbiAgICBoZWlnaHQ6IDA7XHJcbiAgfVxyXG5cclxuICAvLyBIaWRlIHNjcm9sbGJhciBmb3IgRmlyZWZveFxyXG4gIHNjcm9sbGJhci13aWR0aDogbm9uZTtcclxuXHJcbiAgLy8gRm9yIEludGVybmV0IEV4cGxvcmVyIGFuZCBFZGdlIGxlZ2FjeVxyXG4gIC1tcy1vdmVyZmxvdy1zdHlsZTogbm9uZTtcclxufVxyXG5cclxuLndvcmtmbG93LWVkaXRvci1jb250YWluZXIge1xyXG4gIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICBoZWlnaHQ6IDg3dmg7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gIGJhY2tncm91bmQtY29sb3I6IHZhcigtLWJhY2tncm91bmQtcHJpbWFyeSk7XHJcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuXHJcbiAgLmhlYWRlci1uYXYge1xyXG4gICAgZGlzcGxheTogZmxleDtcclxuICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcclxuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICBtaW4taGVpZ2h0OiA2MHB4O1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0tY2FyZC1iZyk7XHJcbiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgdmFyKC0tYm9yZGVyLWNvbG9yKTtcclxuXHJcbiAgICAuYnJlYWRjcnVtYiB7XHJcbiAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgIGdhcDogOHB4O1xyXG4gICAgICBmbGV4OiAxO1xyXG5cclxuICAgICAgLm5hdi1pdGVtIHtcclxuICAgICAgICBmb250LXNpemU6IDE0cHg7XHJcbiAgICAgICAgY29sb3I6IHZhcigtLXRleHQtc2Vjb25kYXJ5KTtcclxuICAgICAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICAgICAgdHJhbnNpdGlvbjogY29sb3IgMC4ycyBlYXNlO1xyXG5cclxuICAgICAgICAmOmhvdmVyIHtcclxuICAgICAgICAgIGNvbG9yOiB2YXIoLS10ZXh0LXByaW1hcnkpO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgJi5hY3RpdmUge1xyXG4gICAgICAgICAgY29sb3I6IHZhcigtLXRleHQtcHJpbWFyeSk7XHJcbiAgICAgICAgICBmb250LXdlaWdodDogNTAwO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgLnNlcGFyYXRvciB7XHJcbiAgICAgICAgY29sb3I6IHZhcigtLXRleHQtdGVydGlhcnkpO1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMTRweDtcclxuICAgICAgfVxyXG5cclxuICAgICAgLmNsb3NlLWJ0biB7XHJcbiAgICAgICAgYmFja2dyb3VuZDogbm9uZTtcclxuICAgICAgICBib3JkZXI6IG5vbmU7XHJcbiAgICAgICAgY29sb3I6IHZhcigtLXRleHQtc2Vjb25kYXJ5KTtcclxuICAgICAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICAgICAgcGFkZGluZzogNHB4O1xyXG4gICAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICAgICAgICBtYXJnaW4tbGVmdDogMTZweDtcclxuICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlO1xyXG5cclxuICAgICAgICAmOmhvdmVyIHtcclxuICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6IHZhcigtLWhvdmVyLWJnKTtcclxuICAgICAgICAgIGNvbG9yOiB2YXIoLS10ZXh0LXByaW1hcnkpO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC5oZWFkZXItYWN0aW9ucyB7XHJcbiAgICAgIC5hY3Rpb24tZ3JvdXAge1xyXG4gICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgICBnYXA6IDhweDtcclxuXHJcbiAgICAgICAgLmFjdGlvbi1idG4ge1xyXG4gICAgICAgICAgYmFja2dyb3VuZDogbm9uZTtcclxuICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkIHZhcigtLWJvcmRlci1jb2xvcik7XHJcbiAgICAgICAgICBjb2xvcjogdmFyKC0tdGV4dC1zZWNvbmRhcnkpO1xyXG4gICAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICAgICAgICAgcGFkZGluZzogOHB4O1xyXG4gICAgICAgICAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG4gICAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZTtcclxuICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcblxyXG4gICAgICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6IHZhcigtLWhvdmVyLWJnKTtcclxuICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiB2YXIoLS1ib3JkZXItaG92ZXIpO1xyXG4gICAgICAgICAgICBjb2xvcjogdmFyKC0tdGV4dC1wcmltYXJ5KTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC5ydW4tYnRuIHtcclxuICAgICAgICAgIGJhY2tncm91bmQ6IHZhcigtLWRhc2hib2FyZC1wcmltYXJ5KTtcclxuICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkIHZhcigtLWRhc2hib2FyZC1wcmltYXJ5KTtcclxuICAgICAgICAgIGNvbG9yOiB3aGl0ZTtcclxuICAgICAgICAgIGN1cnNvcjogcG9pbnRlcjtcclxuICAgICAgICAgIHBhZGRpbmc6IDhweCAxNnB4O1xyXG4gICAgICAgICAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG4gICAgICAgICAgZm9udC1zaXplOiAxNHB4O1xyXG4gICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICAgICAgZ2FwOiA2cHg7XHJcbiAgICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlO1xyXG5cclxuICAgICAgICAgICY6aG92ZXIge1xyXG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1kYXNoYm9hcmQtcHJpbWFyeS1ob3Zlcik7XHJcbiAgICAgICAgICAgIGJvcmRlci1jb2xvcjogdmFyKC0tZGFzaGJvYXJkLXByaW1hcnktaG92ZXIpO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLmRlbGV0ZS1idG4ge1xyXG4gICAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gICAgdG9wOiA0MHB4O1xyXG4gICAgcmlnaHQ6IDI1cHg7XHJcbiAgICB3aWR0aDogMjBweDtcclxuICAgIGhlaWdodDogMjBweDtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZWY0NDQ0O1xyXG4gICAgYm9yZGVyOiBub25lO1xyXG4gICAgYm9yZGVyLXJhZGl1czogNTAlO1xyXG4gICAgcGFkZGluZzogMDtcclxuICAgIGNvbG9yOiB3aGl0ZTtcclxuICAgIGN1cnNvcjogcG9pbnRlcjtcclxuICAgIHotaW5kZXg6IDk5OTtcclxuICAgIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7XHJcbiAgfVxyXG4gIFxyXG4gIC5kZWxldGUtYnRuOmhvdmVyIHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICNkYzI2MjY7XHJcbiAgfVxyXG5cclxuICAubGxtLXNldHRpbmdzIHtcclxuICAgIG1hcmdpbi10b3A6IDI0cHg7XHJcbiAgICBvdmVyZmxvdy15OiBhdXRvO1xyXG4gICAgbWF4LWhlaWdodDogMzQ4cHg7XHJcbiAgICBib3gtc2hhZG93OiAwcHggNHB4IDEycHggMHB4ICMwMDAwMDA0MDtcclxuICAgIGJhY2tncm91bmQtY29sb3I6IHdoaXRlO1xyXG4gICAgcGFkZGluZzogMTZweCAxNXB4O1xyXG4gICAgYm9yZGVyOiAxcHggc29saWQgI2Q0ZDRkNDtcclxuICAgIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICBcclxuICAgIC5zZXR0aW5nLWl0ZW0ge1xyXG4gICAgICBtYXJnaW46IDEwcHggMCAyNHB4IDA7XHJcbiAgXHJcbiAgICAgIGgzIHtcclxuICAgICAgICBtYXJnaW4tdG9wOiAwO1xyXG4gICAgICAgIG1hcmdpbi1ib3R0b206IDEycHg7XHJcbiAgICAgICAgZm9udC1zaXplOiAxNnB4O1xyXG4gICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICAgICAgY29sb3I6IHZhcigtLXRleHQtY29sb3IpO1xyXG4gICAgICB9XHJcbiAgXHJcbiAgICAgIC5zbGlkZXItd2l0aC12YWx1ZSB7XHJcbiAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICAgIGdhcDogMTJweDtcclxuICBcclxuICAgICAgICAuc2V0dGluZy1zbGlkZXIge1xyXG4gICAgICAgICAgZmxleC1ncm93OiAxO1xyXG4gICAgICAgICAgaGVpZ2h0OiA2cHg7XHJcbiAgICAgICAgICAtd2Via2l0LWFwcGVhcmFuY2U6IG5vbmU7XHJcbiAgICAgICAgICBhcHBlYXJhbmNlOiBub25lO1xyXG4gICAgICAgICAgYmFja2dyb3VuZDogdmFyKC0tYWdlbnQtc2xpZGVyLWJnKTtcclxuICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDNweDtcclxuICAgICAgICAgIG91dGxpbmU6IG5vbmU7XHJcbiAgXHJcbiAgICAgICAgICAmOjotd2Via2l0LXNsaWRlci10aHVtYiB7XHJcbiAgICAgICAgICAgIC13ZWJraXQtYXBwZWFyYW5jZTogbm9uZTtcclxuICAgICAgICAgICAgYXBwZWFyYW5jZTogbm9uZTtcclxuICAgICAgICAgICAgd2lkdGg6IDE4cHg7XHJcbiAgICAgICAgICAgIGhlaWdodDogMThweDtcclxuICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogNTAlO1xyXG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1hZ2VudC1zbGlkZXItdGh1bWItYmcpO1xyXG4gICAgICAgICAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICAgICAgICAgIGJveC1zaGFkb3c6IHZhcigtLWFnZW50LXNsaWRlci10aHVtYi1zaGFkb3cpO1xyXG4gICAgICAgICAgfVxyXG4gIFxyXG4gICAgICAgICAgJjo6LW1vei1yYW5nZS10aHVtYiB7XHJcbiAgICAgICAgICAgIHdpZHRoOiAxOHB4O1xyXG4gICAgICAgICAgICBoZWlnaHQ6IDE4cHg7XHJcbiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcclxuICAgICAgICAgICAgYmFja2dyb3VuZDogdmFyKC0tYWdlbnQtc2xpZGVyLXRodW1iLWJnKTtcclxuICAgICAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICAgICAgICAgICBib3JkZXI6IG5vbmU7XHJcbiAgICAgICAgICAgIGJveC1zaGFkb3c6IHZhcigtLWFnZW50LXNsaWRlci10aHVtYi1zaGFkb3cpO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICBcclxuICAgICAgICAudmFsdWUtZGlzcGxheSB7XHJcbiAgICAgICAgICBtaW4td2lkdGg6IDQwcHg7XHJcbiAgICAgICAgICBoZWlnaHQ6IDQwcHg7XHJcbiAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gICAgICAgICAgZm9udC1zaXplOiAxNHB4O1xyXG4gICAgICAgICAgY29sb3I6IHZhcigtLWZvcm0taW5wdXQtY29sb3IpO1xyXG4gICAgICAgICAgYmFja2dyb3VuZDogdmFyKC0tZm9ybS1pbnB1dC1iZyk7XHJcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiA2cHg7XHJcbiAgICAgICAgICBwYWRkaW5nOiAwIDEwcHg7XHJcbiAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCB2YXIoLS1mb3JtLWlucHV0LWJvcmRlcik7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgXHJcbiAgICAgIC5zZXR0aW5nLWlucHV0IHtcclxuICAgICAgICB3aWR0aDogMTAwJTtcclxuICAgICAgICBwYWRkaW5nOiAxMHB4O1xyXG4gICAgICAgIGJvcmRlcjogMXB4IHNvbGlkIHZhcigtLWZvcm0taW5wdXQtYm9yZGVyKTtcclxuICAgICAgICBib3JkZXItcmFkaXVzOiA2cHg7XHJcbiAgICAgICAgZm9udC1zaXplOiAxNHB4O1xyXG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6IHZhcigtLWZvcm0taW5wdXQtYmcpO1xyXG4gICAgICAgIGNvbG9yOiB2YXIoLS1mb3JtLWlucHV0LWNvbG9yKTtcclxuICAgICAgfVxyXG4gIFxyXG4gICAgICAudG9rZW4tY29udGFpbmVyIHtcclxuICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcbiAgXHJcbiAgICAgICAgLnRva2Vucy11c2VkIHtcclxuICAgICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcclxuICAgICAgICAgIHJpZ2h0OiAwO1xyXG4gICAgICAgICAgdG9wOiAtMjBweDtcclxuICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDtcclxuICAgICAgICAgIGNvbG9yOiB2YXIoLS10ZXh0LXNlY29uZGFyeSk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG4gIFxyXG4gIC5sbG0tdG9nZ2xlIHtcclxuICAgIHdpZHRoOiAxMDAlO1xyXG4gIH1cclxuXHJcbiAgLm1haW4tY29udGVudCB7XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICAgIGZsZXg6IDE7XHJcbiAgICBvdmVyZmxvdzogaGlkZGVuO1xyXG4gICAgaGVpZ2h0OiBjYWxjKDEwMHZoIC0gNjBweCk7XHJcbiAgICBtYXgtaGVpZ2h0OiBjYWxjKDEwMHZoIC0gNjBweCk7XHJcbiAgICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcblxyXG4gICAgLmNhbnZhcy1hcmVhIHtcclxuICAgICAgZmxleDogMTtcclxuICAgICAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0tYmFja2dyb3VuZC1wcmltYXJ5KTtcclxuICAgICAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgY3ViaWMtYmV6aWVyKDAuNCwgMCwgMC4yLCAxKTtcclxuICAgICAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG5cclxuICAgICAgLy8gQWdlbnQgTGlicmFyeSBGbG9hdGluZyBQYW5lbCAtIFBvc2l0aW9uZWQgb24gY2FudmFzIGFyZWFcclxuICAgICAgLmFnZW50LWxpYnJhcnktcGFuZWwge1xyXG4gICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcclxuICAgICAgICB0b3A6IDIwcHg7XHJcbiAgICAgICAgbGVmdDogMjBweDtcclxuICAgICAgICB3aWR0aDogMzgwcHg7XHJcbiAgICAgICAgaGVpZ2h0OiBjYWxjKDEwMHZoIC0gMTYwcHgpO1xyXG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmZmZmY7XHJcbiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2U1ZTdlYjtcclxuICAgICAgICBib3JkZXItcmFkaXVzOiA4cHg7XHJcbiAgICAgICAgYm94LXNoYWRvdzogMCA0cHggMjBweCByZ2JhKDAsIDAsIDAsIDAuMDgpO1xyXG4gICAgICAgIHotaW5kZXg6IDEwO1xyXG4gICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGN1YmljLWJlemllcigwLjQsIDAsIDAuMiwgMSk7XHJcbiAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcblxyXG4gICAgICAgIC8vIENvbGxhcHNlZCBzdGF0ZVxyXG4gICAgICAgICYuY29sbGFwc2VkIHtcclxuICAgICAgICAgIHdpZHRoOiBhdXRvO1xyXG4gICAgICAgICAgbWluLXdpZHRoOiAyMDBweDtcclxuICAgICAgICAgIGhlaWdodDogYXV0bztcclxuXHJcbiAgICAgICAgICAucGFuZWwtaGVhZGVyIHtcclxuICAgICAgICAgICAgcGFkZGluZzogMTJweCAxNnB4O1xyXG4gICAgICAgICAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcclxuICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZmZmZjtcclxuICAgICAgICAgICAgYm9yZGVyLWJvdHRvbTogbm9uZTtcclxuXHJcbiAgICAgICAgICAgIGgzIHtcclxuICAgICAgICAgICAgICBtYXJnaW46IDA7XHJcbiAgICAgICAgICAgICAgZm9udC1zaXplOiAxNHB4O1xyXG4gICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgICAgICAgICAgY29sb3I6IHZhcigtLXRleHQtcHJpbWFyeSk7XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIGF2YS1pY29uIHtcclxuICAgICAgICAgICAgICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4zcyBlYXNlO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgLnBhbmVsLWNvbnRlbnQge1xyXG4gICAgICAgICAgICBkaXNwbGF5OiBub25lO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLnBhbmVsLWhlYWRlciB7XHJcbiAgICAgICAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcclxuICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmZmZmY7XHJcbiAgICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xyXG4gICAgICAgICAgZmxleC1zaHJpbms6IDA7XHJcblxyXG4gICAgICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmOWZhZmI7XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgaDMge1xyXG4gICAgICAgICAgICBtYXJnaW46IDA7XHJcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMThweDtcclxuICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDcwMDtcclxuICAgICAgICAgICAgY29sb3I6ICMxMTE4Mjc7XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgYXZhLWljb24ge1xyXG4gICAgICAgICAgICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4zcyBlYXNlO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLnBhbmVsLWNvbnRlbnQge1xyXG4gICAgICAgICAgbWFyZ2luLXRvcDogMXJlbTtcclxuICAgICAgICAgIGZsZXg6IDE7XHJcbiAgICAgICAgICBvdmVyZmxvdzogaGlkZGVuO1xyXG4gICAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgY3ViaWMtYmV6aWVyKDAuNCwgMCwgMC4yLCAxKTtcclxuICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG5cclxuICAgICAgICAgICYuaGlkZGVuIHtcclxuICAgICAgICAgICAgbWF4LWhlaWdodDogMDtcclxuICAgICAgICAgICAgb3BhY2l0eTogMDtcclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAuc2VhcmNoLXNlY3Rpb24ge1xyXG4gICAgICAgICAgICBmb3JtIHtcclxuICAgICAgICAgICAgICB3aWR0aDogMTAwJTtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgLy8gU2VhcmNoIGlucHV0IHN0eWxpbmdcclxuICAgICAgICAgICAgOjpuZy1kZWVwIGF2YS10ZXh0Ym94IHtcclxuICAgICAgICAgICAgICAudGV4dGJveC1jb250YWluZXIge1xyXG4gICAgICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG4gICAgICAgICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2U1ZTdlYjtcclxuICAgICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmOWZhZmI7XHJcblxyXG4gICAgICAgICAgICAgICAgJjpmb2N1cy13aXRoaW4ge1xyXG4gICAgICAgICAgICAgICAgICBib3JkZXItY29sb3I6ICMzYjgyZjY7XHJcbiAgICAgICAgICAgICAgICAgIGJveC1zaGFkb3c6IDAgMCAwIDNweCByZ2JhKDU5LCAxMzAsIDI0NiwgMC4xKTtcclxuICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICBpbnB1dCB7XHJcbiAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6IHRyYW5zcGFyZW50O1xyXG4gICAgICAgICAgICAgICAgICBjb2xvcjogIzExMTgyNztcclxuICAgICAgICAgICAgICAgICAgZm9udC1zaXplOiAxNHB4O1xyXG5cclxuICAgICAgICAgICAgICAgICAgJjo6cGxhY2Vob2xkZXIge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbG9yOiAjOWNhM2FmO1xyXG4gICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgLmFnZW50cy1saXN0IHtcclxuICAgICAgICAgICAgZmxleDogMTtcclxuICAgICAgICAgICAgb3ZlcmZsb3cteTogYXV0bztcclxuICAgICAgICAgICAgb3ZlcmZsb3cteDogaGlkZGVuO1xyXG4gICAgICAgICAgICBAaW5jbHVkZSBoaWRlLXNjcm9sbGJhcjtcclxuICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMTZweDtcclxuXHJcbiAgICAgICAgICAgIC5hZ2VudC1pdGVtIHtcclxuICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmZmZmO1xyXG4gICAgICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNlNWU3ZWI7XHJcbiAgICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG4gICAgICAgICAgICAgIHBhZGRpbmc6IDE2cHg7XHJcbiAgICAgICAgICAgICAgbWFyZ2luLXRvcDogMTZweDtcclxuICAgICAgICAgICAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICAgICAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZTtcclxuICAgICAgICAgICAgICBib3gtc2hhZG93OiAwIDFweCAzcHggcmdiYSgwLCAwLCAwLCAwLjA1KTtcclxuXHJcbiAgICAgICAgICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgICAgICAgICBib3JkZXItY29sb3I6ICMzYjgyZjY7XHJcbiAgICAgICAgICAgICAgICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcclxuICAgICAgICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTtcclxuICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgIC5hZ2VudC1oZWFkZXIge1xyXG4gICAgICAgICAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgICAgICAgICAgICBnYXA6IDEycHg7XHJcbiAgICAgICAgICAgICAgICBtYXJnaW4tYm90dG9tOiA4cHg7XHJcblxyXG4gICAgICAgICAgICAgICAgLmFnZW50LWljb24tYm94IHtcclxuICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogdmFyKC0tR2xvYmFsLWNvbG9ycy1Sb3lhbC1ibHVlLTUwLCAjZTllZmZkKTtcclxuICAgICAgICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogOTk5cHg7XHJcbiAgICAgICAgICAgICAgICAgIHdpZHRoOiAzNnB4O1xyXG4gICAgICAgICAgICAgICAgICBoZWlnaHQ6IDM2cHg7XHJcbiAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgICAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gICAgICAgICAgICAgICAgICBmbGV4LXNocmluazogMDtcclxuICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAuYWdlbnQtbmFtZSB7XHJcbiAgICAgICAgICAgICAgICAgIG1hcmdpbjogMDtcclxuICAgICAgICAgICAgICAgICAgZm9udC1zaXplOiAxNnB4O1xyXG4gICAgICAgICAgICAgICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICAgICAgICAgICAgICBjb2xvcjogdmFyKC0tQnJhbmQtTmV1dHJhbC1uLTgwMCwgIzNiM2Y0NikgIWltcG9ydGFudDtcclxuICAgICAgICAgICAgICAgICAgZmxleDogMTtcclxuICAgICAgICAgICAgICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDtcclxuICAgICAgICAgICAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuICAgICAgICAgICAgICAgICAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7XHJcbiAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgLmFnZW50LWNvdW50IHtcclxuICAgICAgICAgICAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgICAgICAgICAgICAgZ2FwOiA0cHg7XHJcbiAgICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMTNweDtcclxuICAgICAgICAgICAgICAgICAgY29sb3I6ICM2YjcyODA7XHJcblxyXG4gICAgICAgICAgICAgICAgICAuY291bnQtdGV4dCB7XHJcbiAgICAgICAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgICAgICAgICAgICAgICAgICBmb250LXNpemU6IDE2cHg7XHJcbiAgICAgICAgICAgICAgICAgICAgY29sb3I6IHZhcigtLUNvbG9ycy1UZXh0LXByaW1hcnksICM0YzUxNWIpO1xyXG4gICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAuYWdlbnQtZGVzY3JpcHRpb24ge1xyXG4gICAgICAgICAgICAgICAgbWFyZ2luOiAwIDAgMTZweCAwO1xyXG4gICAgICAgICAgICAgICAgZm9udC1zaXplOiAxNHB4O1xyXG4gICAgICAgICAgICAgICAgY29sb3I6IHZhcigtLUNvbG9ycy1UZXh0LXNlY29uZGFyeSwgIzYxNjg3NCk7XHJcbiAgICAgICAgICAgICAgICBsaW5lLWhlaWdodDogMS41O1xyXG4gICAgICAgICAgICAgICAgZGlzcGxheTogLXdlYmtpdC1ib3g7XHJcbiAgICAgICAgICAgICAgICAtd2Via2l0LWxpbmUtY2xhbXA6IDI7XHJcbiAgICAgICAgICAgICAgICAtd2Via2l0LWJveC1vcmllbnQ6IHZlcnRpY2FsO1xyXG4gICAgICAgICAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgIC5hZ2VudC10YWdzIHtcclxuICAgICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICAgICAgICBmbGV4LXdyYXA6IHdyYXA7XHJcbiAgICAgICAgICAgICAgICBnYXA6IDZweDtcclxuICAgICAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDEycHg7XHJcblxyXG4gICAgICAgICAgICAgICAgLmFnZW50LXRhZyB7XHJcbiAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmM2Y0ZjY7XHJcbiAgICAgICAgICAgICAgICAgIGNvbG9yOiAjMzc0MTUxO1xyXG4gICAgICAgICAgICAgICAgICBmb250LXNpemU6IDEycHg7XHJcbiAgICAgICAgICAgICAgICAgIHBhZGRpbmc6IDRweCA4cHg7XHJcbiAgICAgICAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICAgICAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgIC5hZ2VudC1hY3Rpb25zIHtcclxuICAgICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtZW5kO1xyXG5cclxuICAgICAgICAgICAgICAgIC5wcmV2aWV3LWJ0biB7XHJcbiAgICAgICAgICAgICAgICAgIDo6bmctZGVlcCAuYnV0dG9uLWNvbnRhaW5lciB7XHJcbiAgICAgICAgICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG4gICAgICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDtcclxuICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiA2cHggMTJweDtcclxuICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgLm5vLXJlc3VsdHMtbWVzc2FnZSB7XHJcbiAgICAgICAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gICAgICAgICAgICAgIHBhZGRpbmc6IDJyZW0gMXJlbTtcclxuICAgICAgICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcblxyXG4gICAgICAgICAgICAgIC5uby1yZXN1bHRzLWNvbnRlbnQge1xyXG4gICAgICAgICAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgICAgICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICAgICAgICAgICAgZ2FwOiAwLjVyZW07XHJcblxyXG4gICAgICAgICAgICAgICAgcCB7XHJcbiAgICAgICAgICAgICAgICAgIG1hcmdpbjogMDtcclxuICAgICAgICAgICAgICAgICAgY29sb3I6ICM2YjcyODA7XHJcbiAgICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMTRweDtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAuY3JlYXRlLWFnZW50LXNlY3Rpb24ge1xyXG4gICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmZmZmO1xyXG4gICAgICAgICAgICBmbGV4LXNocmluazogMDtcclxuXHJcbiAgICAgICAgICAgIC8vIENyZWF0ZSBidXR0b24gc3R5bGluZ1xyXG4gICAgICAgICAgICA6Om5nLWRlZXAgYXZhLWJ1dHRvbiB7XHJcbiAgICAgICAgICAgICAgLmJ1dHRvbi1jb250YWluZXIge1xyXG4gICAgICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzNiODJmNiAwJSwgIzFkNGVkOCAxMDAlKTtcclxuICAgICAgICAgICAgICAgIGJvcmRlcjogbm9uZTtcclxuICAgICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgICAgICAgICAgICBmb250LXNpemU6IDE0cHg7XHJcbiAgICAgICAgICAgICAgICBwYWRkaW5nOiAxMnB4IDIwcHg7XHJcblxyXG4gICAgICAgICAgICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMyNTYzZWIgMCUsICMxZTQwYWYgMTAwJSk7XHJcbiAgICAgICAgICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTtcclxuICAgICAgICAgICAgICAgICAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDU5LCAxMzAsIDI0NiwgMC4zKTtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIEFjdGlvbiBCdXR0b25zIFNlY3Rpb24gLSBQb3NpdGlvbmVkIG9uIHRoZSByaWdodFxyXG4gICAgICAuYWN0aW9uLWJ1dHRvbnMtc2VjdGlvbiB7XHJcbiAgICAgICAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gICAgICAgIHRvcDogMjBweDtcclxuICAgICAgICByaWdodDogMjBweDtcclxuICAgICAgICB6LWluZGV4OiAxMDtcclxuXHJcbiAgICAgICAgLmFjdGlvbi1ncm91cCB7XHJcbiAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgICAgIGdhcDogOHB4O1xyXG5cclxuICAgICAgICAgIC5hY3Rpb24tYnRuIHtcclxuICAgICAgICAgICAgYmFja2dyb3VuZDogI2ZmZmZmZjtcclxuICAgICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2U1ZTdlYjtcclxuICAgICAgICAgICAgY29sb3I6IHZhcigtLXRleHQtc2Vjb25kYXJ5KTtcclxuICAgICAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICAgICAgICAgICBwYWRkaW5nOiA4cHg7XHJcbiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICAgICAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZTtcclxuICAgICAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgICAgICAgICAgIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMDYpO1xyXG5cclxuICAgICAgICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y5ZmFmYjtcclxuICAgICAgICAgICAgICBib3JkZXItY29sb3I6ICMzYjgyZjY7XHJcbiAgICAgICAgICAgICAgY29sb3I6IHZhcigtLXRleHQtcHJpbWFyeSk7XHJcbiAgICAgICAgICAgICAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMSk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAucnVuLWJ0biB7XHJcbiAgICAgICAgICAgIGJhY2tncm91bmQ6IHZhcigtLWRhc2hib2FyZC1wcmltYXJ5KTtcclxuICAgICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgdmFyKC0tZGFzaGJvYXJkLXByaW1hcnkpO1xyXG4gICAgICAgICAgICBjb2xvcjogd2hpdGU7XHJcbiAgICAgICAgICAgIGN1cnNvcjogcG9pbnRlcjtcclxuICAgICAgICAgICAgcGFkZGluZzogOHB4IDE2cHg7XHJcbiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICAgICAgICAgICAgZm9udC1zaXplOiAxNHB4O1xyXG4gICAgICAgICAgICBmb250LXdlaWdodDogNTAwO1xyXG4gICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICAgICAgICBnYXA6IDZweDtcclxuICAgICAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZTtcclxuICAgICAgICAgICAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMCwgMCwgMCwgMC4wNik7XHJcblxyXG4gICAgICAgICAgICAmOmhvdmVyIHtcclxuICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1kYXNoYm9hcmQtcHJpbWFyeS1ob3Zlcik7XHJcbiAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiB2YXIoLS1kYXNoYm9hcmQtcHJpbWFyeS1ob3Zlcik7XHJcbiAgICAgICAgICAgICAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMSk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIFdvcmtmbG93IEVkaXRvciBDYW52YXNcclxuICAgICAgLmVkaXRvci1jYW52YXMge1xyXG4gICAgICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgICAgIGhlaWdodDogMTAwJTtcclxuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS1iYWNrZ3JvdW5kLXByaW1hcnkpO1xyXG4gICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuICAgICAgICBvdmVyZmxvdzogaGlkZGVuO1xyXG5cclxuICAgICAgICAuc2lkZWJhci1zZWN0aW9uIHtcclxuICAgICAgICAgIC8vIHBhZGRpbmc6IDIwcHg7XHJcbiAgICAgICAgICBoZWlnaHQ6IDEwMCU7XHJcbiAgICBcclxuICAgICAgICAgIGgzIHtcclxuICAgICAgICAgICAgbWFyZ2luLXRvcDogMDtcclxuICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMTJweDtcclxuICAgICAgICAgICAgZm9udC1zaXplOiAxNnB4O1xyXG4gICAgICAgICAgICBmb250LXdlaWdodDogNTAwO1xyXG4gICAgICAgICAgICBjb2xvcjogdmFyKC0tdGV4dC1jb2xvcik7XHJcbiAgICBcclxuICAgICAgICAgICAgJi5kZXNjcmlwdGlvbi1sYWJlbCB7XHJcbiAgICAgICAgICAgICAgbWFyZ2luLXRvcDogMjBweDtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gQ2FudmFzIGJvYXJkIHN0eWxpbmdcclxuICAgICAgICA6Om5nLWRlZXAgYXBwLWNhbnZhcy1ib2FyZCB7XHJcbiAgICAgICAgICB3aWR0aDogMTAwJTtcclxuICAgICAgICAgIGhlaWdodDogMTAwJTtcclxuXHJcbiAgICAgICAgICAvLyBPdmVycmlkZSBoZWFkZXIgcG9zaXRpb25pbmcgdG8gcGxhY2UgYmVzaWRlIEFnZW50IExpYnJhcnkgcGFuZWxcclxuICAgICAgICAgIC5oZWFkZXItaW5wdXRzLXNlY3Rpb24ge1xyXG4gICAgICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgICAgICAgICAgIHRvcDogMjBweDtcclxuICAgICAgICAgICAgbGVmdDogNDIwcHg7IC8vIFBvc2l0aW9uIGFmdGVyIEFnZW50IExpYnJhcnkgUGFuZWwgKDM4MHB4ICsgNDBweCBnYXApXHJcbiAgICAgICAgICAgIHotaW5kZXg6IDEwO1xyXG4gICAgICAgICAgICBtYXgtd2lkdGg6IGNhbGMoMTAwJSAtIDQ0MHB4KTsgLy8gUHJldmVudCBvdmVyZmxvd1xyXG4gICAgICAgICAgICBvdmVyZmxvdzogdmlzaWJsZTtcclxuXHJcbiAgICAgICAgICAgIC5oZWFkZXItaW5wdXRzLWNvbnRhaW5lciB7XHJcbiAgICAgICAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICAgICAgICAgIGdhcDogMTZweDtcclxuICAgICAgICAgICAgICBmbGV4LXdyYXA6IG5vd3JhcDtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIC8vIExMTSBUb2dnbGUgQ29udGFpbmVyIHN0eWxpbmdcclxuICAgICAgICAgIC5sbG0tdG9nZ2xlLWNvbnRhaW5lciB7XHJcbiAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgICAgICAgIGdhcDogMTJweDtcclxuICAgICAgICAgICAgcGFkZGluZzogOHB4IDE2cHg7XHJcbiAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmZmZmY7XHJcbiAgICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNlNWU3ZWI7XHJcbiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICAgICAgICAgICAgbWFyZ2luLWxlZnQ6IDE2cHg7XHJcblxyXG4gICAgICAgICAgICAudG9nZ2xlLWNvbnRhaW5lciB7XHJcbiAgICAgICAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XHJcbiAgICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgICAgICAgICBnYXA6IDEycHg7XHJcbiAgICAgICAgICAgICAgd2lkdGg6IDEwMCU7XHJcblxyXG4gICAgICAgICAgICAgIC50b2dnbGUtbGFiZWwge1xyXG4gICAgICAgICAgICAgICAgZm9udC1zaXplOiAxNHB4O1xyXG4gICAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgICAgICAgICAgICAgIGNvbG9yOiAjMTExODI3O1xyXG4gICAgICAgICAgICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDtcclxuICAgICAgICAgICAgICAgIGN1cnNvcjogcG9pbnRlcjtcclxuICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgIC8vIFRvZ2dsZSBzdHlsaW5nXHJcbiAgICAgICAgICAgICAgOjpuZy1kZWVwIGF2YS10b2dnbGUge1xyXG4gICAgICAgICAgICAgICAgLnRvZ2dsZS1jb250YWluZXIge1xyXG4gICAgICAgICAgICAgICAgICBib3JkZXItcmFkaXVzOiA4cHg7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4ubGxtLWFjdGlvbnMge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZDtcclxufVxyXG5cclxuLm1vZGVsLWluZm8ge1xyXG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcclxuICB3aWR0aDogZml0LWNvbnRlbnQ7XHJcbiAgbWFyZ2luLXRvcDogMjBweDtcclxuICBoZWlnaHQ6IDUxcHg7XHJcbiAgb3BhY2l0eTogMTtcclxuICB0b3A6IDUwcHg7XHJcbiAgbGVmdDogOTMzcHg7XHJcbiAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG4gIGJvcmRlci13aWR0aDogMXB4O1xyXG4gIHBhZGRpbmctdG9wOiA4cHg7XHJcbiAgcGFkZGluZy1yaWdodDogMTZweDtcclxuICBwYWRkaW5nLWJvdHRvbTogOHB4O1xyXG4gIHBhZGRpbmctbGVmdDogMTZweDtcclxuICBnYXA6IDE2cHg7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogd2hpdGU7XHJcbiAgbGVmdDogNTAlO1xyXG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgtNTAlKTtcclxuICBib3JkZXI6IDFweCBzb2xpZCAjREFEQ0U3O1xyXG59XHJcblxyXG5cclxuOjpuZy1kZWVwIC5hZ2VudC1pY29uLWJveCBzdmcge1xyXG4gIHN0cm9rZTogdmFyKC0tR2xvYmFsLWNvbG9ycy1Sb3lhbC1ibHVlLTcwMCwgIzFhNDZhNyk7XHJcbn1cclxuXHJcbjo6bmctZGVlcCAuYWdlbnQtYWN0aW9ucyAuYXZhLWJ1dHRvbi5zZWNvbmRhcnkge1xyXG4gIC0tYnV0dG9uLWVmZmVjdC1jb2xvcjogdmFyKC0tYnV0dG9uLXZhcmlhbnQtc2Vjb25kYXJ5LWVmZmVjdC1jb2xvcik7XHJcbiAgYm9yZGVyLXJhZGl1czogOTk5cHggIWltcG9ydGFudDtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS1HbG9iYWwtY29sb3JzLVJveWFsLWJsdWUtNTAsICNlOWVmZmQpICFpbXBvcnRhbnQ7XHJcbiAgYm9yZGVyOiBub25lICFpbXBvcnRhbnQ7XHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n  return WorkflowEditorComponent;\n})();", "map": {"version": 3, "names": ["ViewContainerRef", "CommonModule", "ReactiveFormsModule", "RouterModule", "AgentNodeComponent", "debounceTime", "distinctUntilChanged", "map", "startWith", "DragDropModule", "workflowLabels", "CanvasBoardComponent", "AvaTextboxComponent", "ButtonComponent", "DropdownComponent", "IconComponent", "DialogService", "SliderComponent", "ToggleComponent", "WorkflowModes", "WorkflowPreviewPanelComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "capability_r5", "ɵɵtemplate", "WorkflowEditorComponent_div_14_div_12_span_1_Template", "ɵɵproperty", "agent_r3", "capabilities", "ɵɵlistener", "WorkflowEditorComponent_div_14_Template_div_dragstart_0_listener", "$event", "ɵɵrestoreView", "_r2", "$implicit", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "onDragStart", "ɵɵelement", "WorkflowEditorComponent_div_14_div_12_Template", "WorkflowEditorComponent_div_14_Template_ava_button_userClick_14_listener", "onItemPreview", "name", "description", "length", "WorkflowEditorComponent_ng_template_20_Template_app_agent_node_deleteNode_0_listener", "onDelete_r7", "_r6", "onDelete", "WorkflowEditorComponent_ng_template_20_Template_app_agent_node_moveNode_0_listener", "onMove_r8", "onMove", "WorkflowEditorComponent_ng_template_20_Template_app_agent_node_nodeSelected_0_listener", "onSelect_r9", "onSelect", "WorkflowEditorComponent_ng_template_20_Template_app_agent_node_startConnection_0_listener", "onStartConnection_r10", "onStartConnection", "WorkflowEditorComponent_ng_template_20_Template_app_agent_node_nodePositionChanged_0_listener", "updateNodePosition", "WorkflowEditorComponent_ng_template_20_Template_app_agent_node_toolValuesChanged_0_listener", "builtInToolValuesChanged", "node_r11", "selected_r12", "getToolPatchValueForAgent", "data", "agentId", "ɵɵtextInterpolate1", "modelValue_r13", "workFlowLabels", "model", "modelList", "getControl", "temperature", "topP", "maxRPM", "maxToken", "ɵɵtextInterpolate2", "value", "tokensUsed", "maxIteration", "maxExecutionTime", "WorkflowEditorComponent", "fb", "router", "route", "workflowGraphService", "workflowService", "reactFlowService", "cdr", "agentService", "tokenStorage", "drawerService", "dialogService", "drawerContainer", "primaryButtonText", "workflowId", "isEditMode", "isDuplicateMode", "workflowForm", "savedWorkFlowDetils", "labels", "showLlm", "levels", "filterLabels", "optionsMap", "levelOptionsMap", "inputFieldsConfig", "<PERSON><PERSON><PERSON>", "enabled", "placeholder", "required", "agentDetails", "label", "namePlaceholder", "detailPlaceholder", "detail<PERSON><PERSON><PERSON>", "canvasNodes", "canvasEdges", "navigationHints", "agents", "id", "type", "filteredAgents", "availableAgents", "nodes", "edges", "selectedNodeId", "subscriptions", "usedAgentIds", "Set", "isPanelCollapsed", "currentPage", "pageSize", "isDeleted", "searchForm", "builtInToolValues", "nodeToolValues", "Map", "workflowData", "totalNumberOfRecords", "llmSettingsRef", "constructor", "group", "organization", "domain", "project", "team", "enableManagerLLM", "modelDeploymentName", "<PERSON><PERSON><PERSON><PERSON>", "searchList", "get", "valueChanges", "pipe", "toLowerCase", "subscribe", "searchText", "filterWorkflow", "filter", "res", "inTitle", "includes", "togglePanel", "agent", "console", "log", "open", "previewData", "title", "closePreview", "clear", "onCreateNewAgent", "navigate", "isModeDuplicate", "snapshot", "queryParams", "duplicate", "getAndPatchWorkFlowDetails", "getWorkflowById", "response", "workflowAgents", "sort", "a", "b", "serial", "x", "y", "for<PERSON>ach", "i", "createNewNode", "position", "formData", "created<PERSON>y", "manager<PERSON><PERSON>", "Object", "assign", "patchValue", "ngOnInit", "paramMap", "mode", "push", "nodes$", "convertToCanvasNodes", "updateUsedAgentIds", "edges$", "convertToCanvasEdges", "filterValue", "filterAgents", "getCollaborativeAgents", "getAllGenerativeModels", "modle", "ngAfterViewInit", "registerViewContainer", "getAllCollaborativeAgentsPagination", "setTimeout", "onDocumentClick", "event", "target", "nativeElement", "contains", "node", "edge", "source", "animated", "convertNodesToEdges", "slice", "perviousNode", "onLevelChange", "level", "selected<PERSON><PERSON><PERSON>", "selected", "Array", "isArray", "controlName", "control", "setValue", "resetControlAtLevel", "<PERSON><PERSON><PERSON><PERSON>", "Number", "isNaN", "fetchChildOptions", "getOptionsForLevel", "parentId", "getDropdownList", "next", "error", "ngOnDestroy", "sub", "unsubscribe", "clearWorkflow", "agentTools", "agentConfigs", "toolRef", "agentUserTools", "userToolRef", "tools", "tool", "toolName", "join", "modelRef", "newNode", "generateNodeId", "expeectedOutput", "width", "addNode", "getAllNodes", "lastNode", "at", "addEdge", "onCanvasDropped", "dragEvent", "dataTransfer", "agentData", "getData", "JSON", "parse", "onConnectionCreated", "newEdge", "trim", "some", "cap", "updateAvailableAgents", "has", "setData", "stringify", "effectAllowed", "onDeleteNode", "nodeId", "removeNode", "delete", "onNodeSelected", "onNodeMoved", "nodeIndex", "findIndex", "updatedNodes", "updateNodePositions", "detectChanges", "toolValues", "warn", "set", "allToolValues", "from", "values", "flat", "onSave", "warning", "message", "showProceedButton", "proceedButtonText", "confirm", "confirmText", "cancelText", "then", "confirmed", "loadingDialog", "loading", "showSpinner", "payload", "makeWorkFlowPayload", "saveOperation", "updateWorkFlow", "saveWorkFlow", "close", "success", "relativeTo", "replaceUrl", "errorMessage", "isDuplicateError", "status", "onExit", "onReset", "onUndo", "onRedo", "onCanvasStateChanged", "state", "workflowNodes", "workflowEdges", "setNodes", "set<PERSON><PERSON>", "formValue", "getRawValue", "workflowConfigs", "levelId", "workflowAgentTools", "enableAgenticMemory", "embeddingModelRef", "getDaUsername", "modifiedBy", "index", "toolEntries", "toolGroup", "parameters", "param", "toolId", "toolParameterId", "agentToolId", "parameterName", "manager<PERSON><PERSON><PERSON>", "modelRefId", "embeddingModelRefs", "onExecute", "isAgentUsed", "add", "savedEdges", "onToggleChnage", "getAllEdges", "toggleLlm", "show", "patchTool", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "Router", "ActivatedRoute", "i3", "WorkflowGraphService", "i4", "WorkflowService", "i5", "ReactFlowService", "ChangeDetectorRef", "i6", "AgentServiceService", "i7", "TokenStorageService", "i8", "DrawerService", "i9", "selectors", "viewQuery", "WorkflowEditorComponent_Query", "rf", "ctx", "WorkflowEditorComponent_mousedown_HostBindingHandler", "ɵɵresolveDocument", "decls", "vars", "consts", "template", "WorkflowEditorComponent_Template", "WorkflowEditorComponent_Template_div_click_4_listener", "_r1", "WorkflowEditorComponent_div_14_Template", "WorkflowEditorComponent_div_15_Template", "WorkflowEditorComponent_Template_ava_button_userClick_17_listener", "WorkflowEditorComponent_Template_app_canvas_board_canvasDropped_19_listener", "WorkflowEditorComponent_Template_app_canvas_board_nodeSelected_19_listener", "WorkflowEditorComponent_Template_app_canvas_board_nodeMoved_19_listener", "WorkflowEditorComponent_Template_app_canvas_board_nodeRemoved_19_listener", "WorkflowEditorComponent_Template_app_canvas_board_connectionStarted_19_listener", "WorkflowEditorComponent_Template_app_canvas_board_connectionCreated_19_listener", "WorkflowEditorComponent_Template_app_canvas_board_undoAction_19_listener", "WorkflowEditorComponent_Template_app_canvas_board_redoAction_19_listener", "WorkflowEditorComponent_Template_app_canvas_board_resetAction_19_listener", "WorkflowEditorComponent_Template_app_canvas_board_primaryButtonClicked_19_listener", "WorkflowEditorComponent_Template_app_canvas_board_stateChanged_19_listener", "WorkflowEditorComponent_ng_template_20_Template", "ɵɵtemplateRefExtractor", "WorkflowEditorComponent_div_22_Template", "ɵɵelementContainerStart", "WorkflowEditorComponent_Template_ava_toggle_checkedChange_29_listener", "WorkflowEditorComponent_div_30_Template", "ɵɵclassProp", "ɵɵpureFunction1", "_c2", "ɵɵpureFunction0", "_c3", "fallbackMessage", "execute", "tmp_22_0", "manager<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "i10", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "ɵNgNoValidate", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "NgControlStatusGroup", "MinValidator", "MaxValidator", "FormControlDirective", "FormGroupDirective", "FormControlName", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\workflows\\workflow-editor\\workflow-editor.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\pages\\workflows\\workflow-editor\\workflow-editor.component.html"], "sourcesContent": ["import {\r\n  <PERSON>mpo<PERSON>,\r\n  <PERSON><PERSON><PERSON>t,\r\n  <PERSON><PERSON><PERSON><PERSON>,\r\n  AfterViewInit,\r\n  ChangeDetectorRef,\r\n  Input,\r\n  ElementRef,\r\n  ViewChild,\r\n  HostListener,\r\n  ViewContainerRef,\r\n} from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport {\r\n  FormBuilder,\r\n  FormGroup,\r\n  ReactiveFormsModule,\r\n  FormControl,\r\n} from '@angular/forms';\r\nimport { Router, ActivatedRoute, RouterModule } from '@angular/router';\r\nimport {\r\n  WorkflowGraphService,\r\n  WorkflowNode,\r\n  WorkflowEdge,\r\n} from './services/workflow-graph.service';\r\nimport { ReactFlowService } from './services/react-flow.service';\r\nimport { AgentNodeComponent } from './components/agent-node/agent-node.component';\r\nimport { debounceTime, distinctUntilChanged, map, startWith, Subscription } from 'rxjs';\r\nimport { Agent } from './models/agent.model';\r\nimport { DragDropModule } from '@angular/cdk/drag-drop';\r\nimport workflowLabels from './../constants/workflows.json';\r\nimport { WorkflowService } from '@shared/services/workflow.service';\r\nimport {\r\n  CanvasBoardComponent,\r\n  CanvasNode,\r\n  CanvasEdge,\r\n} from '@shared/components/canvas-board/canvas-board.component';\r\n\r\nimport {\r\n  AvaTextboxComponent,\r\n  ButtonComponent,\r\n  DropdownComponent,\r\n  DropdownOption,\r\n  IconComponent,\r\n  DialogService,\r\n  SliderComponent,\r\n  ToggleComponent,\r\n} from '@ava/play-comp-library';\r\nimport { TokenStorageService } from '@shared/index';\r\nimport { Model } from '@shared/models/card.model';\r\nimport { WorkflowModes } from '../constants/workflow.constants';\r\nimport { AgentServiceService } from '@shared/pages/agents/services/agent-service.service';\r\nimport { WorkflowPreviewPanelComponent } from './workflow-preview-panel/workflow-preview-panel.component';\r\nimport { DrawerService } from '@shared/services/drawer/drawer.service';\r\n\r\ninterface NodePosition {\r\n  x: number;\r\n  y: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-workflow-editor',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    ReactiveFormsModule,\r\n    RouterModule,\r\n    AgentNodeComponent,\r\n    DragDropModule,\r\n    CanvasBoardComponent,\r\n    ToggleComponent,\r\n    ButtonComponent,\r\n    IconComponent,\r\n    AvaTextboxComponent,\r\n    SliderComponent,\r\n    DropdownComponent\r\n  ],\r\n  providers: [DialogService],\r\n  templateUrl: './workflow-editor.component.html',\r\n  styleUrls: ['./workflow-editor.component.scss'],\r\n})\r\nexport class WorkflowEditorComponent\r\n  implements OnInit, AfterViewInit, OnDestroy\r\n{\r\n  @ViewChild('drawerContainer', { read: ViewContainerRef }) drawerContainer!: ViewContainerRef;\r\n  @Input() primaryButtonText: string = '';\r\n\r\n  workflowId: string | null = null;\r\n  isEditMode: boolean = false;\r\n  isDuplicateMode: boolean = false;\r\n  workflowForm: FormGroup;\r\n  modelList: DropdownOption[] = [];\r\n\r\n  savedWorkFlowDetils: Record<string, any> = {};\r\n\r\n  // Labels used across the Knowledge Base UI components (titles)\r\n  public workFlowLabels = workflowLabels.labels;\r\n\r\n  showLlm = false;\r\n\r\n  levels = ['organization', 'domain', 'project', 'team'];\r\n  filterLabels = ['organization', 'domain', 'project', 'team'];\r\n  optionsMap: { [level: number]: any[] } = {};\r\n  levelOptionsMap: Record<number, any[]> = {};\r\n\r\n  inputFieldsConfig = {\r\n    // agentName: { enabled: true, placeholder: 'Workflow Name', required: true },\r\n    agentName: { enabled: true, placeholder: 'Agent Name', required: true },\r\n    agentDetails: {\r\n      enabled: true, // Re-enabled - this is the correct Workflow Details section\r\n      label: 'Workflow Details',\r\n      namePlaceholder: 'Enter Workflow name',\r\n      detailPlaceholder: 'Enter workflow description',\r\n      detailLabel: 'Description',\r\n    },\r\n  };\r\n\r\n  // Canvas board properties\r\n  canvasNodes: CanvasNode[] = [];\r\n  canvasEdges: CanvasEdge[] = [];\r\n  navigationHints: string[] = [\r\n    // `${workflowLabels.labels.alt} + ${workflowLabels.labels.drag} ${workflowLabels.labels.toPanCanvas}`,\r\n    // `${workflowLabels.labels.mouseWheel} ${workflowLabels.labels.toZoom}`,\r\n    // `${workflowLabels.labels.space} ${workflowLabels.labels.toResetView}`\r\n  ];\r\n\r\n  // Agent library\r\n  agents: Agent[] = [\r\n    {\r\n      id: 'agent1',\r\n      name: 'Test Agent Agent Agent Agent Agent Agent',\r\n      description: 'Translating user needs into actionable development tasks.',\r\n      type: 'Individual',\r\n      capabilities: ['Code Generation', 'Translation'],\r\n    },\r\n    {\r\n      id: 'agent2',\r\n      name: 'Test Agent 2',\r\n      description: 'Processing data and generating insights.',\r\n      type: 'Individual',\r\n      capabilities: ['Data Analysis', 'Visualization'],\r\n    },\r\n    {\r\n      id: 'agent3',\r\n      name: 'Test Agent 3',\r\n      description: 'Handling complex natural language processing tasks.',\r\n      type: 'Collaborative',\r\n      capabilities: ['NLP', 'Summarization', 'Translation'],\r\n    },\r\n  ];\r\n\r\n  // Filtered agents for search\r\n  filteredAgents: Agent[] = [];\r\n\r\n  // Available agents (filtered and not used yet)\r\n  availableAgents: Agent[] = [];\r\n\r\n  // Workflow nodes and edges (original format)\r\n  nodes: WorkflowNode[] = [];\r\n  edges: WorkflowEdge[] = [];\r\n\r\n  // Selected node\r\n  selectedNodeId: string | null = null;\r\n\r\n  // Subscriptions\r\n  private subscriptions: Subscription[] = [];\r\n\r\n  // Track used agent IDs\r\n  usedAgentIds: Set<string> = new Set();\r\n\r\n  // Panel state\r\n  isPanelCollapsed: boolean = false;\r\n  public currentPage = 1;\r\n  public pageSize = 50;\r\n  public isDeleted = false;\r\n\r\n  // Search form for the panel\r\n  searchForm: FormGroup;\r\n  builtInToolValues: any[] = [];\r\n  private nodeToolValues: Map<string, any[]> = new Map();\r\n  workflowData: any = {};\r\n  totalNumberOfRecords: number = 0;\r\n\r\n  @ViewChild('llmSettingsRef') llmSettingsRef!: ElementRef;\r\n\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private workflowGraphService: WorkflowGraphService,\r\n    private workflowService: WorkflowService,\r\n    private reactFlowService: ReactFlowService,\r\n    private cdr: ChangeDetectorRef,\r\n    private agentService: AgentServiceService,\r\n    private tokenStorage: TokenStorageService,\r\n    private drawerService: DrawerService,\r\n    private dialogService: DialogService,\r\n  ) {\r\n    this.workflowForm = this.fb.group({\r\n      // Workflow details\r\n      name: [''],\r\n      description: [''],\r\n\r\n      // Filters\r\n      organization: [''],\r\n      domain: [''],\r\n      project: [''],\r\n      team: [''],\r\n\r\n      // Enable Manager LLM\r\n      enableManagerLLM: [false],\r\n\r\n      // LLM Configuration settings\r\n      modelDeploymentName: [],\r\n      temperature: [0.3],\r\n      topP: [0.95],\r\n      maxRPM: [0],\r\n      maxToken: [4000],\r\n      maxIteration: [1],\r\n      maxExecutionTime: [30],\r\n\r\n      // Search filter\r\n      agentFilter: [''],\r\n    });\r\n\r\n    // Initialize search form for the panel\r\n    this.searchForm = this.fb.group({\r\n      agentFilter: [''],\r\n    });\r\n  }\r\n\r\n  searchList() {\r\n    this.searchForm\r\n      .get('agentFilter')!\r\n      .valueChanges.pipe(\r\n        startWith(''),\r\n        debounceTime(300),\r\n        distinctUntilChanged(),\r\n        map((value) => value?.toLowerCase() ?? ''),\r\n      )\r\n      .subscribe((searchText) => {\r\n        this.filterWorkflow(searchText);\r\n      });\r\n  }\r\n\r\n  filterWorkflow(searchText: string): void {\r\n    this.filteredAgents = this.availableAgents.filter((res: any) => {\r\n      const inTitle = res.name?.toLowerCase().includes(searchText);\r\n    \r\n      return inTitle;\r\n    });\r\n  }\r\n\r\n  // Panel toggle functionality\r\n  togglePanel(): void {\r\n    this.isPanelCollapsed = !this.isPanelCollapsed;\r\n  }\r\n\r\n  // Item preview functionality\r\n  onItemPreview(agent: Agent): void {\r\n    console.log('Preview agent:', agent);\r\n    // Implement preview functionality\r\n    this.drawerService.open(WorkflowPreviewPanelComponent, {\r\n      previewData: {\r\n        type: 'agent',\r\n        title: agent.name,\r\n        data: agent,\r\n      },\r\n      closePreview: () => this.drawerService.clear(),\r\n    });\r\n  }\r\n\r\n  // Create new agent functionality\r\n  onCreateNewAgent(): void {\r\n    console.log('Create new agent');\r\n    // Navigate to agent creation page or open modal\r\n    this.router.navigate(['/build/agents/create']);\r\n  }\r\n\r\n  isModeDuplicate() {\r\n    return this.route.snapshot.queryParams['mode'] === WorkflowModes.duplicate;\r\n  }\r\n\r\n  getAndPatchWorkFlowDetails(id: string) {\r\n    const isDuplicateMode = this.isModeDuplicate();\r\n\r\n    this.workflowService.getWorkflowById(id).subscribe((response) => {\r\n      this.savedWorkFlowDetils = response;\r\n      // console.log(this.savedWorkFlowDetils);\r\n      const agents = (response?.workflowAgents || []) as any[];\r\n      agents.sort((a: any, b: any) => a.serial - b.serial);\r\n      let x = 400;\r\n      let y = 150;\r\n      // console.log(agents);\r\n      agents.forEach((agent, i) => {\r\n        this.createNewNode(agent.agentDetails, { x, y });\r\n        x += 400;\r\n        const position = i + 1;\r\n        if (position % 3 == 0) {\r\n          y += 120;\r\n          x = 40;\r\n        }\r\n      });\r\n      this.workflowData = response;\r\n\r\n      const formData: Record<string, any> = {};\r\n\r\n      if(!this.isEditMode){\r\n        formData['createdBy'] = response?.createdBy;\r\n      }\r\n\r\n      if (!isDuplicateMode) {\r\n        formData['name'] = response?.name;\r\n        formData['description'] = response?.description;\r\n      }\r\n\r\n      if (response?.managerLlm) {\r\n        formData['enableManagerLLM'] = true;\r\n        Object.assign(formData, response?.managerLlm);\r\n      }\r\n\r\n      this.workflowForm.patchValue(formData);\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.searchList();\r\n    // this.fetchChildOptions(0, -1);\r\n    // Check if we're in edit mode\r\n    this.workflowId =\r\n      this.route.snapshot.paramMap.get('id') ||\r\n      this.route.snapshot.queryParams['id'];\r\n    this.isEditMode = !!this.workflowId;\r\n\r\n    const mode = this.route.snapshot.queryParams['mode'];\r\n    if (mode === 'duplicate') {\r\n      this.isEditMode = false;\r\n      this.isDuplicateMode = true;\r\n    }\r\n\r\n    if (this.isEditMode && this.workflowId) {\r\n      // In a real app, you would fetch the workflow data by ID\r\n      console.log(`Editing workflow with ID: ${this.workflowId}`);\r\n      this.getAndPatchWorkFlowDetails(this.workflowId);\r\n      // this.loadWorkflowData(this.workflowId);\r\n    }\r\n\r\n    if(this.isDuplicateMode && this.workflowId){\r\n      console.log(`Cloning workflow with ID: ${this.workflowId}`);\r\n      this.getAndPatchWorkFlowDetails(this.workflowId);\r\n    }\r\n\r\n    // Subscribe to nodes to track used agents\r\n    this.subscriptions.push(\r\n      this.workflowGraphService.nodes$.subscribe((nodes) => {\r\n        this.nodes = nodes;\r\n        this.canvasNodes = this.convertToCanvasNodes(nodes);\r\n\r\n        // Update used agent IDs\r\n        this.updateUsedAgentIds();\r\n      }),\r\n    );\r\n\r\n    this.subscriptions.push(\r\n      this.workflowGraphService.edges$.subscribe((edges) => {\r\n        this.edges = edges;\r\n        this.canvasEdges = this.convertToCanvasEdges(edges);\r\n      }),\r\n    );\r\n\r\n    // Subscribe to the search filter changes\r\n    this.subscriptions.push(\r\n      this.getControl('agentFilter').valueChanges.subscribe((filterValue) => {\r\n        this.filterAgents(filterValue);\r\n      }),\r\n    );\r\n\r\n    this.getCollaborativeAgents();      \r\n\r\n    this.workflowService.getAllGenerativeModels().subscribe((response) => {\r\n      this.modelList = response.map((modle: Model) => {\r\n        return {\r\n          name: modle.modelDeploymentName,\r\n          value: modle.modelDeploymentName,\r\n        };\r\n      });\r\n    });\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    // Canvas board handles its own initialization\r\n     this.drawerService.registerViewContainer(this.drawerContainer);\r\n  }\r\n\r\n  private getCollaborativeAgents(){\r\n    this.agentService\r\n      .getAllCollaborativeAgentsPagination(this.currentPage, this.totalNumberOfRecords, this.isDeleted)\r\n      .subscribe((response : any) => {\r\n        this.agents = response.agentDetails;\r\n        this.filterAgents(this.getControl('agentFilter').value);\r\n        // Making sure the dropdown value get patched agin after options are added\r\n        setTimeout(() => {\r\n          this.workflowForm.patchValue({\r\n            modelDeploymentName:\r\n              this.savedWorkFlowDetils['managerLlm']?.modelDeploymentName,\r\n          });\r\n        });\r\n      });\r\n  }\r\n\r\n  // HostListener to close llm-settings when clicking outside\r\n  @HostListener('document:mousedown', ['$event'])\r\n  onDocumentClick(event: MouseEvent) {\r\n    if (this.showLlm && this.llmSettingsRef) {\r\n      const target = event.target as HTMLElement;\r\n      if (!this.llmSettingsRef.nativeElement.contains(target)) {\r\n        this.showLlm = false;\r\n      }\r\n    }\r\n  }\r\n\r\n  // Convert WorkflowNode to CanvasNode\r\n  private convertToCanvasNodes(nodes: WorkflowNode[]): CanvasNode[] {\r\n    return nodes.map((node) => ({\r\n      id: node.id,\r\n      type: node.type,\r\n      data: node.data,\r\n      position: node.position,\r\n    }));\r\n  }\r\n\r\n  // Convert WorkflowEdge to CanvasEdge\r\n  private convertToCanvasEdges(edges: WorkflowEdge[]): CanvasEdge[] {\r\n    return edges.map((edge) => ({\r\n      id: edge.id,\r\n      source: edge.source,\r\n      target: edge.target,\r\n      animated: edge.animated,\r\n    }));\r\n  }\r\n\r\n  private convertNodesToEdges(nodes: WorkflowNode[]) {\r\n    return nodes.slice(1).map((node, i) => {\r\n      const perviousNode = i <= 0 ? nodes[0] : nodes[i - 1];\r\n\r\n      return {\r\n        id: `${perviousNode.id}-${node.id}`,\r\n        source: perviousNode.id,\r\n        target: node.id,\r\n        // animated: node.animated,\r\n      };\r\n    });\r\n  }\r\n\r\n  onLevelChange(level: number, selectedValue: string | string[]): void {\r\n    const selected = Array.isArray(selectedValue)\r\n      ? selectedValue[0]\r\n      : selectedValue;\r\n\r\n    if (!selected) {\r\n      return;\r\n    }\r\n\r\n    const controlName = this.filterLabels[level];\r\n    const control = this.workflowForm.get(controlName);\r\n\r\n    if (control) {\r\n      control.setValue(selected);\r\n    }\r\n\r\n    // Reset controls and options for levels below the current one\r\n    for (let i = level + 1; i < this.levels.length; i++) {\r\n      this.resetControlAtLevel(i);\r\n      this.levelOptionsMap[i] = [];\r\n    }\r\n\r\n    const selectedNumber = Number(selected);\r\n    if (!isNaN(selectedNumber)) {\r\n      this.fetchChildOptions(level + 1, selectedNumber);\r\n    }\r\n  }\r\n\r\n  resetControlAtLevel(level: number): void {\r\n    const controlName = this.filterLabels[level];\r\n    const control = this.workflowForm.get(controlName);\r\n    if (control) {\r\n      control.setValue(null);\r\n    }\r\n  }\r\n\r\n  getOptionsForLevel(level: number): any[] {\r\n    return this.levelOptionsMap[level] || [];\r\n  }\r\n\r\n  fetchChildOptions(level: number, parentId: number) {\r\n    if (!this.filterLabels[level]) return;\r\n\r\n    this.workflowService.getDropdownList(level, parentId).subscribe({\r\n      next: (res) => {\r\n        this.levelOptionsMap[level] = Array.isArray(res) ? res : [];\r\n      },\r\n      error: () => {\r\n        this.levelOptionsMap[level] = [];\r\n      },\r\n    });\r\n  }\r\n  ngOnDestroy(): void {\r\n    // Clean up subscriptions\r\n    this.subscriptions.forEach((sub) => sub.unsubscribe());\r\n    this.workflowGraphService.clearWorkflow();\r\n  }\r\n\r\n  createNewNode(agent: any, position: { x: number; y: number }) {\r\n    // console.log(agent);\r\n    const agentTools = agent?.agentConfigs?.toolRef || [];\r\n    const agentUserTools = agent?.agentConfigs?.userToolRef || [];\r\n    const tools = [...agentTools, ...agentUserTools]\r\n      .map((tool) => tool?.toolName)\r\n      .join(', ');\r\n    const model = agent?.agentConfigs?.modelRef[0];\r\n    const newNode: WorkflowNode = {\r\n      id: this.workflowGraphService.generateNodeId(),\r\n      type: 'agent',\r\n      data: {\r\n        label: agent.name,\r\n        agentId: agent.id,\r\n        agentName: agent.name,\r\n        description: agent.description,\r\n        capabilities: agent.expeectedOutput,\r\n        width: 280, // Default width\r\n        model: model?.modelDeploymentName,\r\n        tools,\r\n        agentTools\r\n      },\r\n      position: position,\r\n    };\r\n\r\n    this.workflowGraphService.addNode(newNode);\r\n\r\n    if (this.getControl('enableManagerLLM').value) return;\r\n    const nodes = this.workflowGraphService.getAllNodes();\r\n    const lastNode = nodes.at(-2);\r\n    if (lastNode) {\r\n      this.workflowGraphService.addEdge({\r\n        id: `${lastNode.id}-${newNode.id}`,\r\n        source: lastNode.id,\r\n        target: newNode.id,\r\n      });\r\n    }\r\n  }\r\n\r\n  // Canvas board event handlers\r\n  onCanvasDropped(event: {\r\n    event: DragEvent;\r\n    position: { x: number; y: number };\r\n  }): void {\r\n    const dragEvent = event.event;\r\n    const position = event.position;\r\n\r\n    if (dragEvent.dataTransfer) {\r\n      const agentData = dragEvent.dataTransfer.getData('application/reactflow');\r\n\r\n      if (agentData) {\r\n        try {\r\n          const agent = JSON.parse(agentData);\r\n          // const agentTools = agent?.tools || [];\r\n          // const agentUserTools = agent?.userTools || [];\r\n          // const tools = [...agentTools, ...agentUserTools]\r\n          //   .map((tool) => tool?.toolName)\r\n          //   .join(', ');\r\n\r\n          // // Create a new node for the agent\r\n          // const newNode: WorkflowNode = {\r\n          //   id: this.workflowGraphService.generateNodeId(),\r\n          //   type: 'agent',\r\n          //   data: {\r\n          //     label: agent.name,\r\n          //     agentId: agent.id,\r\n          //     agentName: agent.name,\r\n          //     description: agent.description,\r\n          //     capabilities: agent.capabilities,\r\n          //     width: 280, // Default width\r\n          //     model: agent?.modelDetails?.modelDeploymentName,\r\n          //     tools,\r\n          //   },\r\n          //   position: position,\r\n          // };\r\n\r\n          // this.workflowGraphService.addNode(newNode);\r\n          // const nodes = this.workflowGraphService.getAllNodes();\r\n          // const lastNode = nodes.at(-2);\r\n          // if (lastNode) {\r\n          //   this.workflowGraphService.addEdge({\r\n          //     id: `${lastNode.id}-${newNode.id}`,\r\n          //     source: lastNode.id,\r\n          //     target: newNode.id,\r\n          //   });\r\n          // }\r\n\r\n          this.createNewNode(agent, position);\r\n        } catch (error) {\r\n          console.error('Error adding node:', error);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  onConnectionCreated(edge: CanvasEdge): void {\r\n    const newEdge: WorkflowEdge = {\r\n      id: edge.id,\r\n      source: edge.source,\r\n      target: edge.target,\r\n      animated: edge.animated || true,\r\n    };\r\n\r\n    this.workflowGraphService.addEdge(newEdge);\r\n  }\r\n\r\n  filterAgents(filterValue: string): void {\r\n    // First filter by search term\r\n    if (!filterValue || filterValue.trim() === '') {\r\n      this.filteredAgents = [...this.agents];\r\n    } else {\r\n      filterValue = filterValue.toLowerCase().trim();\r\n      this.filteredAgents = this.agents.filter(\r\n        (agent) =>\r\n          agent.name.toLowerCase().includes(filterValue) ||\r\n          agent.description.toLowerCase().includes(filterValue) ||\r\n          (agent.type && agent.type.toLowerCase().includes(filterValue)) ||\r\n          (agent.capabilities &&\r\n            agent.capabilities.some((cap) =>\r\n              cap.toLowerCase().includes(filterValue),\r\n            )),\r\n      );\r\n    }\r\n\r\n    // Then filter out agents that are already used\r\n    this.updateAvailableAgents();\r\n  }\r\n\r\n  updateAvailableAgents(): void {\r\n    this.availableAgents = this.agents.filter(\r\n      (agent) => !this.usedAgentIds.has(agent.id),\r\n    );\r\n  }\r\n\r\n  onDragStart(event: DragEvent, agent: Agent): void {\r\n    if (event.dataTransfer) {\r\n      event.dataTransfer.setData(\r\n        'application/reactflow',\r\n        JSON.stringify(agent),\r\n      );\r\n      event.dataTransfer.effectAllowed = 'move';\r\n    }\r\n  }\r\n\r\n  onDeleteNode(nodeId: string): void {\r\n    this.workflowGraphService.removeNode(nodeId);\r\n\r\n    // If the deleted node was selected, clear selection\r\n    if (this.selectedNodeId === nodeId) {\r\n      this.selectedNodeId = null;\r\n    }\r\n    this.nodeToolValues.delete(nodeId);\r\n  }\r\n\r\n  onNodeSelected(nodeId: string): void {\r\n    this.selectedNodeId = nodeId;\r\n  }\r\n\r\n  onNodeMoved(data: { nodeId: string; position: NodePosition }): void {\r\n    // Find the node index\r\n    const nodeIndex = this.nodes.findIndex((node) => node.id === data.nodeId);\r\n    if (nodeIndex === -1) return;\r\n\r\n    // Create a new array with the updated node\r\n    const updatedNodes = [...this.nodes];\r\n    updatedNodes[nodeIndex] = {\r\n      ...this.nodes[nodeIndex],\r\n      position: data.position,\r\n    };\r\n\r\n    // Update the node positions through service\r\n    this.workflowGraphService.updateNodePositions(updatedNodes);\r\n\r\n    // Force a change detection cycle\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  onStartConnection(data: {\r\n    nodeId: string;\r\n    handleType: 'source' | 'target';\r\n    event: MouseEvent;\r\n  }): void {\r\n    // Canvas board handles connection logic\r\n    // This method is called when a connection starts but the canvas board manages the temp connection\r\n  }\r\n\r\n  updateNodePosition(data: {\r\n    nodeId: string;\r\n    position: { x: number; y: number };\r\n  }): void {\r\n    // Canvas board handles connection point updates\r\n  }\r\n  \r\n  builtInToolValuesChanged(event: { nodeId: string, toolValues: any[] }): void {\r\n    if (!event || !event.nodeId || !event.toolValues) {\r\n      console.warn('Invalid tool values event:', event);\r\n      return;\r\n    }\r\n\r\n    // console.log('Tool values changed for node:', event.nodeId, event.toolValues);\r\n\r\n    this.nodeToolValues.set(event.nodeId, event.toolValues);\r\n\r\n    const allToolValues = Array.from(this.nodeToolValues.values())\r\n      .filter(values => values != null)\r\n      .flat()\r\n      .filter(tool => tool != null);\r\n\r\n    this.builtInToolValues = allToolValues;\r\n\r\n    // console.log('All tool values updated:', this.builtInToolValues);\r\n  }\r\n\r\n\r\n  onSave(): void {\r\n    // Check if workflow name is provided\r\n    if (!this.workflowForm.get('name')?.value?.trim()) {\r\n      this.dialogService.warning({\r\n        title: 'Warning',\r\n        message: 'Please provide a name for the workflow before saving.',\r\n        showProceedButton: true,\r\n        proceedButtonText: 'OK'\r\n      });\r\n      return;\r\n    }\r\n\r\n    // Show confirmation dialog before saving\r\n    this.dialogService.confirm({\r\n      title: 'Save Workflow',\r\n      message: 'Are you sure you want to save this workflow?',\r\n      confirmText: 'Save',\r\n      cancelText: 'Cancel'\r\n    }).then((confirmed) => {\r\n      if (!confirmed) {\r\n        return; // User cancelled the save operation\r\n      }\r\n\r\n      // Show loading dialog\r\n      const loadingDialog = this.dialogService.loading({\r\n        message: 'Saving workflow...',\r\n        showSpinner: true\r\n      });\r\n\r\n      const payload = this.makeWorkFlowPayload();\r\n      \r\n      // Determine if we're creating or updating\r\n      const saveOperation = this.workflowId && !this.isDuplicateMode\r\n        ? this.workflowService.updateWorkFlow(payload)\r\n        : this.workflowService.saveWorkFlow(payload);\r\n\r\n      saveOperation.subscribe({\r\n        next: (response: any) => {\r\n          // Close loading dialog\r\n          loadingDialog.close();\r\n          \r\n          // Show success message\r\n          this.dialogService.success({\r\n            title: 'Success',\r\n            message: 'Workflow saved successfully!',\r\n            showProceedButton: true,\r\n            proceedButtonText: 'OK'\r\n          });\r\n\r\n          // If this was a new workflow, update the URL with the new ID\r\n          if (!this.workflowId || this.isDuplicateMode) {\r\n            this.workflowId = response.workflowId || response.id;\r\n            this.isEditMode = true;\r\n            this.isDuplicateMode = false;\r\n            // Update URL without reloading\r\n            this.router.navigate([], {\r\n              relativeTo: this.route,\r\n              queryParams: { id: this.workflowId },\r\n              replaceUrl: true\r\n            });\r\n          }\r\n        },\r\n        error: (error) => {\r\n          // Close loading dialog\r\n          loadingDialog.close();\r\n          \r\n          // Check if this is a duplicate name error (adjust based on your API error structure)\r\n          const errorMessage = error?.error?.message || 'An error occurred while saving the workflow.';\r\n          const isDuplicateError = errorMessage.toLowerCase().includes('duplicate') || \r\n                                 error.status === 409; // 409 is Conflict\r\n\r\n          if (isDuplicateError) {\r\n            this.dialogService.warning({\r\n              title: 'Duplicate Workflow',\r\n              message: 'A workflow with this name already exists. Please choose a different name.',\r\n              showProceedButton: true,\r\n              proceedButtonText: 'OK'\r\n            });\r\n          } else {\r\n            this.dialogService.error({\r\n              title: 'Error',\r\n              message: errorMessage,\r\n              showProceedButton: true,\r\n              proceedButtonText: 'OK'\r\n            });\r\n          }\r\n        }\r\n      });\r\n    });\r\n  }\r\n\r\n  onExit(): void {\r\n    this.router.navigate(['/build/workflows']);\r\n  }\r\n\r\n  onReset(): void {\r\n    this.workflowGraphService.clearWorkflow();\r\n    this.selectedNodeId = null;\r\n  }\r\n\r\n  onUndo(): void {\r\n    console.log('Undo triggered from workflow editor');\r\n  }\r\n\r\n  onRedo(): void {\r\n    console.log('Redo triggered from workflow editor');\r\n  }\r\n\r\n  onCanvasStateChanged(state: {\r\n    nodes: CanvasNode[];\r\n    edges: CanvasEdge[];\r\n  }): void {\r\n    console.log('Canvas state changed:', state);\r\n\r\n    // Convert canvas nodes back to workflow nodes\r\n    const workflowNodes: WorkflowNode[] = state.nodes.map((node) => ({\r\n      id: node.id,\r\n      type: node.type,\r\n      data: node.data,\r\n      position: node.position,\r\n    }));\r\n\r\n    // Convert canvas edges back to workflow edges\r\n    const workflowEdges: WorkflowEdge[] = state.edges.map((edge) => ({\r\n      id: edge.id,\r\n      source: edge.source,\r\n      target: edge.target,\r\n      animated: edge.animated,\r\n    }));\r\n\r\n    // Update the workflow service to sync the state\r\n    this.workflowGraphService.setNodes(workflowNodes);\r\n    this.workflowGraphService.setEdges(workflowEdges);\r\n  }\r\n\r\n  makeWorkFlowPayload() {\r\n    const formValue = this.workflowForm.getRawValue();\r\n    const payload: Record<string, any> = {\r\n      name: formValue?.name || null,\r\n      description: formValue?.description || null,\r\n      workflowAgents: [],\r\n      workflowConfigs: {\r\n        levelId: 199, \r\n        modelRef: [],\r\n        workflowAgentTools: [],\r\n        managerLlm: [],\r\n        topP: formValue?.topP || 0.95,\r\n        maxToken: formValue?.maxToken || 1500,\r\n        temperature: formValue?.temperature || 0.3,\r\n        enableAgenticMemory: false,\r\n        embeddingModelRef: [],\r\n      },\r\n      createdBy: this.isEditMode ? formValue?.createdBy : this.tokenStorage.getDaUsername(),\r\n      modifiedBy: this.tokenStorage.getDaUsername()\r\n    };\r\n\r\n    const nodes = this.workflowGraphService.getAllNodes();\r\n\r\n    if (nodes.length) {\r\n      payload['workflowAgents'] = nodes.map((node, index) => ({\r\n        serial: index + 1,\r\n        agentId: node.data.agentId,\r\n      }));\r\n    }\r\n\r\n    if (this.builtInToolValues.length > 0) {\r\n      const toolEntries : any[] = [];\r\n      this.builtInToolValues.forEach((toolGroup) => {\r\n        toolGroup.parameters.forEach((param : any) => {\r\n          toolEntries.push({\r\n            agentId: param.agentId,\r\n            toolId: toolGroup.toolId,\r\n            toolParameterId: param.agentToolId || param.toolParameterId, \r\n            parameterName: param.parameterName,\r\n            value: param.value\r\n          });\r\n        });\r\n      });\r\n\r\n      payload['workflowConfigs'].workflowAgentTools = toolEntries;\r\n    }\r\n\r\n    if (formValue?.modelDeploymentName) {\r\n      const managerModel = {\r\n        id: formValue.modelRefId, \r\n        modelDeploymentName: formValue.modelDeploymentName,\r\n        topP: formValue.topP,\r\n        maxToken: formValue.maxToken,\r\n        temperature: formValue.temperature\r\n      };\r\n\r\n      payload['workflowConfigs'].managerLlm = [managerModel];\r\n    }\r\n\r\n    if(formValue?.embeddingModelRefs?.length){\r\n      payload['workflowConfigs'].embeddingModelRef = [...formValue.embeddingModelRefs];\r\n      payload['workflowConfigs'].modelRef = [...formValue.embeddingModelRefs];\r\n    }\r\n\r\n    if (this.workflowId && !this.isModeDuplicate()) {\r\n      payload['id'] = this.workflowId;\r\n    }\r\n\r\n    return payload;\r\n  }\r\n\r\n\r\n  onExecute(): void {\r\n    // Navigate to workflow execution page\r\n    if (!this.workflowForm.get('name')?.value) {\r\n      this.dialogService.warning({\r\n        title: 'Warning',\r\n        message: 'Please provide the workflow name before executing the workflow.',\r\n        showProceedButton: true,\r\n        proceedButtonText: 'Ok'      \r\n      });\r\n      return;\r\n    }\r\n    const payload = this.makeWorkFlowPayload();\r\n    if (this.workflowId && !this.isDuplicateMode) {\r\n      this.workflowService.updateWorkFlow(payload).subscribe(() => {\r\n        this.router.navigate(['/build/workflows/execute', this.workflowId]);\r\n      });\r\n    } else {\r\n      // For new workflows, save first then navigate\r\n      this.workflowService.saveWorkFlow(payload).subscribe((response: any) => {\r\n        this.router.navigate(['/build/workflows/execute', response.workflowId]);\r\n      });\r\n      // alert('Please save the workflow before executing it.');\r\n    }\r\n  }\r\n\r\n  // Helper method to get form controls easily from the template\r\n  getControl(name: string): FormControl {\r\n    return this.workflowForm.get(name) as FormControl;\r\n  }\r\n\r\n  /**\r\n   * Check if an agent is already used in the workflow\r\n   * @param agentId ID of the agent to check\r\n   */\r\n  isAgentUsed(agentId: string): boolean {\r\n    return this.usedAgentIds.has(agentId);\r\n  }\r\n\r\n  /**\r\n   * Update the set of used agent IDs\r\n   */\r\n  updateUsedAgentIds(): void {\r\n    this.usedAgentIds.clear();\r\n    this.nodes.forEach((node) => {\r\n      if (node.data && node.data.agentId) {\r\n        this.usedAgentIds.add(node.data.agentId);\r\n      }\r\n    });\r\n\r\n    // Update available agents whenever used agents change\r\n    this.updateAvailableAgents();\r\n  }\r\n  savedEdges: WorkflowEdge[] = [];\r\n  onToggleChnage(event: boolean) {\r\n    console.log(event);\r\n    this.getControl('enableManagerLLM').setValue(event);\r\n    this.showLlm = event;\r\n    if (event) {\r\n      this.savedEdges = this.workflowGraphService.getAllEdges();\r\n      this.workflowGraphService.setEdges([]);\r\n    } else {\r\n      const nodes = this.workflowGraphService.getAllNodes();\r\n      const edges = this.convertNodesToEdges(nodes);\r\n      this.workflowGraphService.setEdges(edges);\r\n      if (!this.savedWorkFlowDetils['managerLlm']) {\r\n        this.getControl('modelDeploymentName').setValue(null);\r\n      }\r\n    }\r\n  }\r\n  toggleLlm(show: boolean) {\r\n    this.showLlm = show;\r\n  }\r\n\r\n  getToolPatchValueForAgent(agentId: number): { toolId: number, parameterName: string, value: any } | null {\r\n    const patchTool = this.workflowData?.workflowConfigs?.workflowAgentTools;\r\n\r\n    if (patchTool?.agentId === agentId) {\r\n      return {\r\n        toolId: patchTool.toolId,\r\n        parameterName: patchTool.parameterName,\r\n        value: patchTool.value\r\n      };\r\n    }\r\n    return null;\r\n  }\r\n\r\n}\r\n", "<div class=\"workflow-editor-container\">\r\n  <!-- Main Content Area -->\r\n  <div class=\"main-content\">\r\n    <!-- Full Width Canvas Area -->\r\n    <div class=\"canvas-area\">\r\n      <!-- Agent Library Floating Panel -->\r\n      <div\r\n        class=\"agent-library-panel\"\r\n        [class.collapsed]=\"isPanelCollapsed\"\r\n        [ngClass]=\"{ 'p-3': !isPanelCollapsed }\"\r\n      >\r\n        <div class=\"panel-header\" (click)=\"togglePanel()\">\r\n          <h3>Agent Library</h3>\r\n          <ava-icon\r\n            [iconName]=\"isPanelCollapsed ? 'ChevronDown' : 'ChevronUp'\"\r\n            iconSize=\"16\"\r\n            iconColor=\"var(--text-secondary)\"\r\n          >\r\n          </ava-icon>\r\n        </div>\r\n\r\n        <!-- Panel Content -->\r\n        <div class=\"panel-content\" [class.hidden]=\"isPanelCollapsed\">\r\n          <!-- Search Section -->\r\n          <div class=\"search-section\">\r\n            <form [formGroup]=\"searchForm\">\r\n              <ava-textbox\r\n                placeholder='Search \"Agents\"'\r\n                hoverEffect=\"glow\"\r\n                pressedEffect=\"solid\"\r\n                formControlName=\"agentFilter\"\r\n              >\r\n                <ava-icon\r\n                  slot=\"icon-start\"\r\n                  iconName=\"search\"\r\n                  [iconSize]=\"16\"\r\n                  iconColor=\"var(--color-brand-primary)\"\r\n                >\r\n                </ava-icon>\r\n              </ava-textbox>\r\n            </form>\r\n          </div>\r\n\r\n          <!-- Agent List -->\r\n          <div class=\"agents-list\">\r\n            <!-- Agent Items -->\r\n            <div\r\n              *ngFor=\"let agent of filteredAgents\"\r\n              class=\"agent-item\"\r\n              draggable=\"true\"\r\n              (dragstart)=\"onDragStart($event, agent)\"\r\n            >\r\n              <div class=\"agent-header\">\r\n                <div class=\"agent-icon-box\">\r\n                  <ava-icon\r\n                    iconName=\"User\"\r\n                    iconSize=\"20\"\r\n                    iconColor=\"#ffffff\"\r\n                  ></ava-icon>\r\n                </div>\r\n                <h4 class=\"agent-name\">{{ agent.name }}</h4>\r\n                <div class=\"agent-count\">\r\n                  <ava-icon\r\n                    iconName=\"Users\"\r\n                    iconSize=\"16\"\r\n                    iconColor=\"#9CA3AF\"\r\n                  ></ava-icon>\r\n                  <span class=\"count-text\">120</span>\r\n                </div>\r\n              </div>\r\n              <p class=\"agent-description\">{{ agent.description }}</p>\r\n\r\n              <!-- Agent Tags -->\r\n              <div\r\n                class=\"agent-tags\"\r\n                *ngIf=\"agent.capabilities && agent.capabilities.length > 0\"\r\n              >\r\n                <span\r\n                  class=\"agent-tag\"\r\n                  *ngFor=\"let capability of agent.capabilities\"\r\n                  >{{ capability }}</span\r\n                >\r\n              </div>\r\n\r\n              <!-- Preview button -->\r\n              <div class=\"agent-actions\">\r\n                <ava-button\r\n                  label=\"Preview\"\r\n                  size=\"small\"\r\n                  [pill]=\"true\"\r\n                  variant=\"secondary\"\r\n                  (userClick)=\"onItemPreview(agent)\"\r\n                  class=\"preview-btn\"\r\n                >\r\n                </ava-button>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- No results message -->\r\n            <div\r\n              class=\"no-results-message\"\r\n              *ngIf=\"availableAgents.length === 0\"\r\n            >\r\n              <div class=\"no-results-content\">\r\n                <ava-icon\r\n                  iconName=\"Search\"\r\n                  iconSize=\"24\"\r\n                  iconColor=\"#9CA3AF\"\r\n                ></ava-icon>\r\n                <p>No agents found matching your criteria</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Create New Agent Button -->\r\n          <div class=\"create-agent-section\">\r\n            <ava-button\r\n              label=\"Create New Agent\"\r\n              variant=\"primary\"\r\n              size=\"large\"\r\n              [customStyles]=\"{\r\n                background:\r\n                  'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n                '--button-effect-color': '33, 90, 214',\r\n                'border-radius': '8px',\r\n              }\"\r\n              iconName=\"Plus\"\r\n              iconColor=\"white\"\r\n              (userClick)=\"onCreateNewAgent()\"\r\n              [width]=\"'100%'\"\r\n            >\r\n            </ava-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Workflow Editor Canvas -->\r\n      <div class=\"editor-canvas\">\r\n        <app-canvas-board\r\n          [nodes]=\"canvasNodes\"\r\n          [edges]=\"canvasEdges\"\r\n          [navigationHints]=\"navigationHints\"\r\n          [fallbackMessage]=\"workFlowLabels.fallbackMessage\"\r\n          [primaryButtonText]=\"workFlowLabels.execute\"\r\n          (canvasDropped)=\"onCanvasDropped($event)\"\r\n          (nodeSelected)=\"onNodeSelected($event)\"\r\n          (nodeMoved)=\"onNodeMoved($event)\"\r\n          (nodeRemoved)=\"onDeleteNode($event)\"\r\n          (connectionStarted)=\"onStartConnection($event)\"\r\n          (connectionCreated)=\"onConnectionCreated($event)\"\r\n          (undoAction)=\"onUndo()\"\r\n          (redoAction)=\"onRedo()\"\r\n          (resetAction)=\"onReset()\"\r\n          (primaryButtonClicked)=\"onExecute()\"\r\n          (stateChanged)=\"onCanvasStateChanged($event)\"\r\n          [showLeftActions]=\"true\"\r\n          [showHeaderInputs]=\"true\"\r\n          [inputFieldsConfig]=\"inputFieldsConfig\"\r\n          [agentDetailNameControl]=\"getControl('name')\"\r\n          [agentDetailControl]=\"getControl('description')\"\r\n        >\r\n          <!-- Node template for rendering agent nodes -->\r\n          <ng-template\r\n            #nodeTemplate\r\n            let-node\r\n            let-selected=\"selected\"\r\n            let-onDelete=\"onDelete\"\r\n            let-onMove=\"onMove\"\r\n            let-onSelect=\"onSelect\"\r\n            let-onStartConnection=\"onStartConnection\"\r\n          >\r\n            <app-agent-node\r\n              [node]=\"node\"\r\n              [selected]=\"selected\"\r\n              (deleteNode)=\"onDelete($event)\"\r\n              (moveNode)=\"onMove($event)\"\r\n              (nodeSelected)=\"onSelect($event)\"\r\n              (startConnection)=\"onStartConnection($event)\"\r\n              (nodePositionChanged)=\"updateNodePosition($event)\"\r\n              (toolValuesChanged)=\"builtInToolValuesChanged($event)\"\r\n              [patchData]=\"getToolPatchValueForAgent(node?.data?.agentId)\"\r\n            >\r\n            </app-agent-node>\r\n          </ng-template>\r\n\r\n          <div\r\n            info\r\n            class=\"model-info\"\r\n            *ngIf=\"\r\n              getControl('enableManagerLLM').value &&\r\n              getControl('modelDeploymentName')?.value as modelValue\r\n            \"\r\n          >\r\n            You are currently using {{ modelValue }}\r\n          </div>\r\n\r\n          <ng-container actionLeft>\r\n            <div class=\"sidebar-section llm-toggle full-width\">\r\n              <div class=\"llm-toggle-container\">\r\n                <div class=\"toggle-container\">\r\n                  <span class=\"toggle-label\">{{\r\n                    workFlowLabels.managerLLMToggleLabel\r\n                    }}</span>\r\n                  <ava-toggle size=\"small\" (checkedChange)=\"onToggleChnage($event)\" position=\"left\" [animation]=\"true\"\r\n                    [checked]=\"!!getControl('enableManagerLLM').value\">\r\n                  </ava-toggle>\r\n                </div>\r\n              </div>\r\n              <div class=\"llm-settings\" *ngIf=\"getControl('enableManagerLLM').value && showLlm\" #llmSettingsRef>\r\n                <!-- Model List -->\r\n                <div class=\"setting-item\">\r\n                  <h3>{{workFlowLabels.model}}</h3>\r\n                  <ava-dropdown dropdownTitle=\"choose agent\" [options]=\"modelList\"\r\n                    [formControl]=\"getControl('modelDeploymentName')\">\r\n                  </ava-dropdown>\r\n                </div>\r\n                <!-- Temperature -->\r\n                <div class=\"setting-item\">\r\n                  <h3>{{workFlowLabels.temperature}}</h3>\r\n                  <ava-slider [min]=\"0\" [max]=\"1\" [step]=\"0.1\" [formControl]=\"getControl('temperature')\"></ava-slider>\r\n                </div>\r\n          \r\n                <!-- Top P -->\r\n                <div class=\"setting-item\">\r\n                  <h3>{{workFlowLabels.topP}}</h3>\r\n                  <input type=\"number\" min=\"0\" max=\"1\" step=\"0.01\" [formControl]=\"getControl('topP')\" class=\"setting-input\" />\r\n                </div>\r\n          \r\n                <!-- Max RPM -->\r\n                <div class=\"setting-item\">\r\n                  <h3>{{workFlowLabels.maxRPM}}</h3>\r\n                  <input type=\"number\" min=\"0\" [formControl]=\"getControl('maxRPM')\" class=\"setting-input\" />\r\n                </div>\r\n          \r\n                <!-- Max Token -->\r\n                <div class=\"setting-item\">\r\n                  <h3>{{workFlowLabels.maxToken}}</h3>\r\n                  <div class=\"token-container\">\r\n                    <input type=\"number\" min=\"0\" [formControl]=\"getControl('maxToken')\" class=\"setting-input\" />\r\n                    <span class=\"tokens-used\">\r\n                      {{ getControl(\"maxToken\").value }}/{{workFlowLabels.tokensUsed}}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n          \r\n                <!-- Max Iteration -->\r\n                <div class=\"setting-item\">\r\n                  <h3>{{workFlowLabels.maxIteration}}</h3>\r\n                  <input type=\"number\" min=\"1\" [formControl]=\"getControl('maxIteration')\" class=\"setting-input\" />\r\n                </div>\r\n          \r\n                <!-- Max Execution Time -->\r\n                <div class=\"setting-item\">\r\n                  <h3>{{workFlowLabels.maxExecutionTime}}</h3>\r\n                  <input type=\"number\" min=\"0\" [formControl]=\"getControl('maxExecutionTime')\" class=\"setting-input\" />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </ng-container>\r\n        </app-canvas-board>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<!-- Add this container somewhere in your template -->\r\n<div #drawerContainer></div>\r\n"], "mappings": "AAAA,SAUEA,gBAAgB,QACX,eAAe;AACtB,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAGEC,mBAAmB,QAEd,gBAAgB;AACvB,SAAiCC,YAAY,QAAQ,iBAAiB;AAOtE,SAASC,kBAAkB,QAAQ,8CAA8C;AACjF,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,GAAG,EAAEC,SAAS,QAAsB,MAAM;AAEvF,SAASC,cAAc,QAAQ,wBAAwB;AACvD,OAAOC,cAAc,MAAM,+BAA+B;AAE1D,SACEC,oBAAoB,QAGf,wDAAwD;AAE/D,SACEC,mBAAmB,EACnBC,eAAe,EACfC,iBAAiB,EAEjBC,aAAa,EACbC,aAAa,EACbC,eAAe,EACfC,eAAe,QACV,wBAAwB;AAG/B,SAASC,aAAa,QAAQ,iCAAiC;AAE/D,SAASC,6BAA6B,QAAQ,2DAA2D;;;;;;;;;;;;;;;;;;;;;;;;ICyBzFC,EAAA,CAAAC,cAAA,eAGG;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAClB;;;;IADEH,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAC,aAAA,CAAgB;;;;;IAPrBN,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAO,UAAA,IAAAC,qDAAA,mBAGG;IAELR,EAAA,CAAAG,YAAA,EAAM;;;;IAHqBH,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAS,UAAA,YAAAC,QAAA,CAAAC,YAAA,CAAqB;;;;;;IAjClDX,EAAA,CAAAC,cAAA,cAKC;IADCD,EAAA,CAAAY,UAAA,uBAAAC,iEAAAC,MAAA;MAAA,MAAAJ,QAAA,GAAAV,EAAA,CAAAe,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAaF,MAAA,CAAAG,WAAA,CAAAP,MAAA,EAAAJ,QAAA,CAA0B;IAAA,EAAC;IAGtCV,EADF,CAAAC,cAAA,cAA0B,cACI;IAC1BD,EAAA,CAAAsB,SAAA,mBAIY;IACdtB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5CH,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAsB,SAAA,mBAIY;IACZtB,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,UAAG;IAEhCF,EAFgC,CAAAG,YAAA,EAAO,EAC/B,EACF;IACNH,EAAA,CAAAC,cAAA,aAA6B;IAAAD,EAAA,CAAAE,MAAA,IAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAGxDH,EAAA,CAAAO,UAAA,KAAAgB,8CAAA,kBAGC;IAUCvB,EADF,CAAAC,cAAA,eAA2B,sBAQxB;IAFCD,EAAA,CAAAY,UAAA,uBAAAY,yEAAA;MAAA,MAAAd,QAAA,GAAAV,EAAA,CAAAe,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAaF,MAAA,CAAAO,aAAA,CAAAf,QAAA,CAAoB;IAAA,EAAC;IAKxCV,EAFI,CAAAG,YAAA,EAAa,EACT,EACF;;;;IApCqBH,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAK,QAAA,CAAAgB,IAAA,CAAgB;IAUZ1B,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAK,iBAAA,CAAAK,QAAA,CAAAiB,WAAA,CAAuB;IAKjD3B,EAAA,CAAAI,SAAA,EAAyD;IAAzDJ,EAAA,CAAAS,UAAA,SAAAC,QAAA,CAAAC,YAAA,IAAAD,QAAA,CAAAC,YAAA,CAAAiB,MAAA,KAAyD;IAcxD5B,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAS,UAAA,cAAa;;;;;IAcjBT,EAJF,CAAAC,cAAA,cAGC,cACiC;IAC9BD,EAAA,CAAAsB,SAAA,mBAIY;IACZtB,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,6CAAsC;IAE7CF,EAF6C,CAAAG,YAAA,EAAI,EACzC,EACF;;;;;;IA4DNH,EAAA,CAAAC,cAAA,yBAUC;IAFCD,EALA,CAAAY,UAAA,wBAAAiB,qFAAAf,MAAA;MAAA,MAAAgB,WAAA,GAAA9B,EAAA,CAAAe,aAAA,CAAAgB,GAAA,EAAAC,QAAA;MAAA,OAAAhC,EAAA,CAAAoB,WAAA,CAAcU,WAAA,CAAAhB,MAAA,CAAgB;IAAA,EAAC,sBAAAmB,mFAAAnB,MAAA;MAAA,MAAAoB,SAAA,GAAAlC,EAAA,CAAAe,aAAA,CAAAgB,GAAA,EAAAI,MAAA;MAAA,OAAAnC,EAAA,CAAAoB,WAAA,CACnBc,SAAA,CAAApB,MAAA,CAAc;IAAA,EAAC,0BAAAsB,uFAAAtB,MAAA;MAAA,MAAAuB,WAAA,GAAArC,EAAA,CAAAe,aAAA,CAAAgB,GAAA,EAAAO,QAAA;MAAA,OAAAtC,EAAA,CAAAoB,WAAA,CACXiB,WAAA,CAAAvB,MAAA,CAAgB;IAAA,EAAC,6BAAAyB,0FAAAzB,MAAA;MAAA,MAAA0B,qBAAA,GAAAxC,EAAA,CAAAe,aAAA,CAAAgB,GAAA,EAAAU,iBAAA;MAAA,OAAAzC,EAAA,CAAAoB,WAAA,CACdoB,qBAAA,CAAA1B,MAAA,CAAyB;IAAA,EAAC,iCAAA4B,8FAAA5B,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAgB,GAAA;MAAA,MAAAb,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CACtBF,MAAA,CAAAyB,kBAAA,CAAA7B,MAAA,CAA0B;IAAA,EAAC,+BAAA8B,4FAAA9B,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAgB,GAAA;MAAA,MAAAb,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAC7BF,MAAA,CAAA2B,wBAAA,CAAA/B,MAAA,CAAgC;IAAA,EAAC;IAGxDd,EAAA,CAAAG,YAAA,EAAiB;;;;;;IAFfH,EARA,CAAAS,UAAA,SAAAqC,QAAA,CAAa,aAAAC,YAAA,CACQ,cAAA7B,MAAA,CAAA8B,yBAAA,CAAAF,QAAA,kBAAAA,QAAA,CAAAG,IAAA,kBAAAH,QAAA,CAAAG,IAAA,CAAAC,OAAA,EAOuC;;;;;IAKhElD,EAAA,CAAAC,cAAA,cAOC;IACCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAmD,kBAAA,8BAAAC,cAAA,MACF;;;;;IAiBQpD,EAHJ,CAAAC,cAAA,iBAAkG,cAEtE,SACpB;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjCH,EAAA,CAAAsB,SAAA,uBAEe;IACjBtB,EAAA,CAAAG,YAAA,EAAM;IAGJH,EADF,CAAAC,cAAA,cAA0B,SACpB;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAsB,SAAA,qBAAoG;IACtGtB,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,eAA0B,UACpB;IAAAD,EAAA,CAAAE,MAAA,IAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChCH,EAAA,CAAAsB,SAAA,iBAA4G;IAC9GtB,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,eAA0B,UACpB;IAAAD,EAAA,CAAAE,MAAA,IAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAsB,SAAA,iBAA0F;IAC5FtB,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,eAA0B,UACpB;IAAAD,EAAA,CAAAE,MAAA,IAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpCH,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAAsB,SAAA,iBAA4F;IAC5FtB,EAAA,CAAAC,cAAA,gBAA0B;IACxBD,EAAA,CAAAE,MAAA,IACF;IAEJF,EAFI,CAAAG,YAAA,EAAO,EACH,EACF;IAIJH,EADF,CAAAC,cAAA,eAA0B,UACpB;IAAAD,EAAA,CAAAE,MAAA,IAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAsB,SAAA,iBAAgG;IAClGtB,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,eAA0B,UACpB;IAAAD,EAAA,CAAAE,MAAA,IAAmC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5CH,EAAA,CAAAsB,SAAA,iBAAoG;IAExGtB,EADE,CAAAG,YAAA,EAAM,EACF;;;;IA7CEH,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAK,iBAAA,CAAAa,MAAA,CAAAmC,cAAA,CAAAC,KAAA,CAAwB;IACetD,EAAA,CAAAI,SAAA,EAAqB;IAC9DJ,EADyC,CAAAS,UAAA,YAAAS,MAAA,CAAAqC,SAAA,CAAqB,gBAAArC,MAAA,CAAAsC,UAAA,wBACb;IAK/CxD,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAK,iBAAA,CAAAa,MAAA,CAAAmC,cAAA,CAAAI,WAAA,CAA8B;IACtBzD,EAAA,CAAAI,SAAA,EAAS;IAAwBJ,EAAjC,CAAAS,UAAA,UAAS,UAAU,aAAa,gBAAAS,MAAA,CAAAsC,UAAA,gBAA0C;IAKlFxD,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAK,iBAAA,CAAAa,MAAA,CAAAmC,cAAA,CAAAK,IAAA,CAAuB;IACsB1D,EAAA,CAAAI,SAAA,EAAkC;IAAlCJ,EAAA,CAAAS,UAAA,gBAAAS,MAAA,CAAAsC,UAAA,SAAkC;IAK/ExD,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAK,iBAAA,CAAAa,MAAA,CAAAmC,cAAA,CAAAM,MAAA,CAAyB;IACA3D,EAAA,CAAAI,SAAA,EAAoC;IAApCJ,EAAA,CAAAS,UAAA,gBAAAS,MAAA,CAAAsC,UAAA,WAAoC;IAK7DxD,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAK,iBAAA,CAAAa,MAAA,CAAAmC,cAAA,CAAAO,QAAA,CAA2B;IAEA5D,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAS,UAAA,gBAAAS,MAAA,CAAAsC,UAAA,aAAsC;IAEjExD,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAA6D,kBAAA,MAAA3C,MAAA,CAAAsC,UAAA,aAAAM,KAAA,OAAA5C,MAAA,CAAAmC,cAAA,CAAAU,UAAA,MACF;IAME/D,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAK,iBAAA,CAAAa,MAAA,CAAAmC,cAAA,CAAAW,YAAA,CAA+B;IACNhE,EAAA,CAAAI,SAAA,EAA0C;IAA1CJ,EAAA,CAAAS,UAAA,gBAAAS,MAAA,CAAAsC,UAAA,iBAA0C;IAKnExD,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAK,iBAAA,CAAAa,MAAA,CAAAmC,cAAA,CAAAY,gBAAA,CAAmC;IACVjE,EAAA,CAAAI,SAAA,EAA8C;IAA9CJ,EAAA,CAAAS,UAAA,gBAAAS,MAAA,CAAAsC,UAAA,qBAA8C;;;AD7K7F,WAAaU,uBAAuB;EAA9B,MAAOA,uBAAuB;IA0GxBC,EAAA;IACAC,MAAA;IACAC,KAAA;IACAC,oBAAA;IACAC,eAAA;IACAC,gBAAA;IACAC,GAAA;IACAC,YAAA;IACAC,YAAA;IACAC,aAAA;IACAC,aAAA;IAjHgDC,eAAe;IAChEC,iBAAiB,GAAW,EAAE;IAEvCC,UAAU,GAAkB,IAAI;IAChCC,UAAU,GAAY,KAAK;IAC3BC,eAAe,GAAY,KAAK;IAChCC,YAAY;IACZ5B,SAAS,GAAqB,EAAE;IAEhC6B,mBAAmB,GAAwB,EAAE;IAE7C;IACO/B,cAAc,GAAGhE,cAAc,CAACgG,MAAM;IAE7CC,OAAO,GAAG,KAAK;IAEfC,MAAM,GAAG,CAAC,cAAc,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,CAAC;IACtDC,YAAY,GAAG,CAAC,cAAc,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,CAAC;IAC5DC,UAAU,GAA+B,EAAE;IAC3CC,eAAe,GAA0B,EAAE;IAE3CC,iBAAiB,GAAG;MAClB;MACAC,SAAS,EAAE;QAAEC,OAAO,EAAE,IAAI;QAAEC,WAAW,EAAE,YAAY;QAAEC,QAAQ,EAAE;MAAI,CAAE;MACvEC,YAAY,EAAE;QACZH,OAAO,EAAE,IAAI;QAAE;QACfI,KAAK,EAAE,kBAAkB;QACzBC,eAAe,EAAE,qBAAqB;QACtCC,iBAAiB,EAAE,4BAA4B;QAC/CC,WAAW,EAAE;;KAEhB;IAED;IACAC,WAAW,GAAiB,EAAE;IAC9BC,WAAW,GAAiB,EAAE;IAC9BC,eAAe,GAAa;MAC1B;MACA;MACA;IAAA,CACD;IAED;IACAC,MAAM,GAAY,CAChB;MACEC,EAAE,EAAE,QAAQ;MACZ/E,IAAI,EAAE,0CAA0C;MAChDC,WAAW,EAAE,2DAA2D;MACxE+E,IAAI,EAAE,YAAY;MAClB/F,YAAY,EAAE,CAAC,iBAAiB,EAAE,aAAa;KAChD,EACD;MACE8F,EAAE,EAAE,QAAQ;MACZ/E,IAAI,EAAE,cAAc;MACpBC,WAAW,EAAE,0CAA0C;MACvD+E,IAAI,EAAE,YAAY;MAClB/F,YAAY,EAAE,CAAC,eAAe,EAAE,eAAe;KAChD,EACD;MACE8F,EAAE,EAAE,QAAQ;MACZ/E,IAAI,EAAE,cAAc;MACpBC,WAAW,EAAE,qDAAqD;MAClE+E,IAAI,EAAE,eAAe;MACrB/F,YAAY,EAAE,CAAC,KAAK,EAAE,eAAe,EAAE,aAAa;KACrD,CACF;IAED;IACAgG,cAAc,GAAY,EAAE;IAE5B;IACAC,eAAe,GAAY,EAAE;IAE7B;IACAC,KAAK,GAAmB,EAAE;IAC1BC,KAAK,GAAmB,EAAE;IAE1B;IACAC,cAAc,GAAkB,IAAI;IAEpC;IACQC,aAAa,GAAmB,EAAE;IAE1C;IACAC,YAAY,GAAgB,IAAIC,GAAG,EAAE;IAErC;IACAC,gBAAgB,GAAY,KAAK;IAC1BC,WAAW,GAAG,CAAC;IACfC,QAAQ,GAAG,EAAE;IACbC,SAAS,GAAG,KAAK;IAExB;IACAC,UAAU;IACVC,iBAAiB,GAAU,EAAE;IACrBC,cAAc,GAAuB,IAAIC,GAAG,EAAE;IACtDC,YAAY,GAAQ,EAAE;IACtBC,oBAAoB,GAAW,CAAC;IAEHC,cAAc;IAG3CC,YACU3D,EAAe,EACfC,MAAc,EACdC,KAAqB,EACrBC,oBAA0C,EAC1CC,eAAgC,EAChCC,gBAAkC,EAClCC,GAAsB,EACtBC,YAAiC,EACjCC,YAAiC,EACjCC,aAA4B,EAC5BC,aAA4B;MAV5B,KAAAV,EAAE,GAAFA,EAAE;MACF,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,KAAK,GAALA,KAAK;MACL,KAAAC,oBAAoB,GAApBA,oBAAoB;MACpB,KAAAC,eAAe,GAAfA,eAAe;MACf,KAAAC,gBAAgB,GAAhBA,gBAAgB;MAChB,KAAAC,GAAG,GAAHA,GAAG;MACH,KAAAC,YAAY,GAAZA,YAAY;MACZ,KAAAC,YAAY,GAAZA,YAAY;MACZ,KAAAC,aAAa,GAAbA,aAAa;MACb,KAAAC,aAAa,GAAbA,aAAa;MAErB,IAAI,CAACM,YAAY,GAAG,IAAI,CAAChB,EAAE,CAAC4D,KAAK,CAAC;QAChC;QACArG,IAAI,EAAE,CAAC,EAAE,CAAC;QACVC,WAAW,EAAE,CAAC,EAAE,CAAC;QAEjB;QACAqG,YAAY,EAAE,CAAC,EAAE,CAAC;QAClBC,MAAM,EAAE,CAAC,EAAE,CAAC;QACZC,OAAO,EAAE,CAAC,EAAE,CAAC;QACbC,IAAI,EAAE,CAAC,EAAE,CAAC;QAEV;QACAC,gBAAgB,EAAE,CAAC,KAAK,CAAC;QAEzB;QACAC,mBAAmB,EAAE,EAAE;QACvB5E,WAAW,EAAE,CAAC,GAAG,CAAC;QAClBC,IAAI,EAAE,CAAC,IAAI,CAAC;QACZC,MAAM,EAAE,CAAC,CAAC,CAAC;QACXC,QAAQ,EAAE,CAAC,IAAI,CAAC;QAChBI,YAAY,EAAE,CAAC,CAAC,CAAC;QACjBC,gBAAgB,EAAE,CAAC,EAAE,CAAC;QAEtB;QACAqE,WAAW,EAAE,CAAC,EAAE;OACjB,CAAC;MAEF;MACA,IAAI,CAACf,UAAU,GAAG,IAAI,CAACpD,EAAE,CAAC4D,KAAK,CAAC;QAC9BO,WAAW,EAAE,CAAC,EAAE;OACjB,CAAC;IACJ;IAEAC,UAAUA,CAAA;MACR,IAAI,CAAChB,UAAU,CACZiB,GAAG,CAAC,aAAa,CAAE,CACnBC,YAAY,CAACC,IAAI,CAChBvJ,SAAS,CAAC,EAAE,CAAC,EACbH,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,GAAG,CAAE4E,KAAK,IAAKA,KAAK,EAAE6E,WAAW,EAAE,IAAI,EAAE,CAAC,CAC3C,CACAC,SAAS,CAAEC,UAAU,IAAI;QACxB,IAAI,CAACC,cAAc,CAACD,UAAU,CAAC;MACjC,CAAC,CAAC;IACN;IAEAC,cAAcA,CAACD,UAAkB;MAC/B,IAAI,CAAClC,cAAc,GAAG,IAAI,CAACC,eAAe,CAACmC,MAAM,CAAEC,GAAQ,IAAI;QAC7D,MAAMC,OAAO,GAAGD,GAAG,CAACtH,IAAI,EAAEiH,WAAW,EAAE,CAACO,QAAQ,CAACL,UAAU,CAAC;QAE5D,OAAOI,OAAO;MAChB,CAAC,CAAC;IACJ;IAEA;IACAE,WAAWA,CAAA;MACT,IAAI,CAAChC,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;IAChD;IAEA;IACA1F,aAAaA,CAAC2H,KAAY;MACxBC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEF,KAAK,CAAC;MACpC;MACA,IAAI,CAACxE,aAAa,CAAC2E,IAAI,CAACxJ,6BAA6B,EAAE;QACrDyJ,WAAW,EAAE;UACX9C,IAAI,EAAE,OAAO;UACb+C,KAAK,EAAEL,KAAK,CAAC1H,IAAI;UACjBuB,IAAI,EAAEmG;SACP;QACDM,YAAY,EAAEA,CAAA,KAAM,IAAI,CAAC9E,aAAa,CAAC+E,KAAK;OAC7C,CAAC;IACJ;IAEA;IACAC,gBAAgBA,CAAA;MACdP,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;MAC/B;MACA,IAAI,CAAClF,MAAM,CAACyF,QAAQ,CAAC,CAAC,sBAAsB,CAAC,CAAC;IAChD;IAEAC,eAAeA,CAAA;MACb,OAAO,IAAI,CAACzF,KAAK,CAAC0F,QAAQ,CAACC,WAAW,CAAC,MAAM,CAAC,KAAKlK,aAAa,CAACmK,SAAS;IAC5E;IAEAC,0BAA0BA,CAACzD,EAAU;MACnC,MAAMvB,eAAe,GAAG,IAAI,CAAC4E,eAAe,EAAE;MAE9C,IAAI,CAACvF,eAAe,CAAC4F,eAAe,CAAC1D,EAAE,CAAC,CAACmC,SAAS,CAAEwB,QAAQ,IAAI;QAC9D,IAAI,CAAChF,mBAAmB,GAAGgF,QAAQ;QACnC;QACA,MAAM5D,MAAM,GAAI4D,QAAQ,EAAEC,cAAc,IAAI,EAAY;QACxD7D,MAAM,CAAC8D,IAAI,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAKD,CAAC,CAACE,MAAM,GAAGD,CAAC,CAACC,MAAM,CAAC;QACpD,IAAIC,CAAC,GAAG,GAAG;QACX,IAAIC,CAAC,GAAG,GAAG;QACX;QACAnE,MAAM,CAACoE,OAAO,CAAC,CAACxB,KAAK,EAAEyB,CAAC,KAAI;UAC1B,IAAI,CAACC,aAAa,CAAC1B,KAAK,CAACpD,YAAY,EAAE;YAAE0E,CAAC;YAAEC;UAAC,CAAE,CAAC;UAChDD,CAAC,IAAI,GAAG;UACR,MAAMK,QAAQ,GAAGF,CAAC,GAAG,CAAC;UACtB,IAAIE,QAAQ,GAAG,CAAC,IAAI,CAAC,EAAE;YACrBJ,CAAC,IAAI,GAAG;YACRD,CAAC,GAAG,EAAE;UACR;QACF,CAAC,CAAC;QACF,IAAI,CAAC/C,YAAY,GAAGyC,QAAQ;QAE5B,MAAMY,QAAQ,GAAwB,EAAE;QAExC,IAAG,CAAC,IAAI,CAAC/F,UAAU,EAAC;UAClB+F,QAAQ,CAAC,WAAW,CAAC,GAAGZ,QAAQ,EAAEa,SAAS;QAC7C;QAEA,IAAI,CAAC/F,eAAe,EAAE;UACpB8F,QAAQ,CAAC,MAAM,CAAC,GAAGZ,QAAQ,EAAE1I,IAAI;UACjCsJ,QAAQ,CAAC,aAAa,CAAC,GAAGZ,QAAQ,EAAEzI,WAAW;QACjD;QAEA,IAAIyI,QAAQ,EAAEc,UAAU,EAAE;UACxBF,QAAQ,CAAC,kBAAkB,CAAC,GAAG,IAAI;UACnCG,MAAM,CAACC,MAAM,CAACJ,QAAQ,EAAEZ,QAAQ,EAAEc,UAAU,CAAC;QAC/C;QAEA,IAAI,CAAC/F,YAAY,CAACkG,UAAU,CAACL,QAAQ,CAAC;MACxC,CAAC,CAAC;IACJ;IAEAM,QAAQA,CAAA;MACN,IAAI,CAAC/C,UAAU,EAAE;MACjB;MACA;MACA,IAAI,CAACvD,UAAU,GACb,IAAI,CAACX,KAAK,CAAC0F,QAAQ,CAACwB,QAAQ,CAAC/C,GAAG,CAAC,IAAI,CAAC,IACtC,IAAI,CAACnE,KAAK,CAAC0F,QAAQ,CAACC,WAAW,CAAC,IAAI,CAAC;MACvC,IAAI,CAAC/E,UAAU,GAAG,CAAC,CAAC,IAAI,CAACD,UAAU;MAEnC,MAAMwG,IAAI,GAAG,IAAI,CAACnH,KAAK,CAAC0F,QAAQ,CAACC,WAAW,CAAC,MAAM,CAAC;MACpD,IAAIwB,IAAI,KAAK,WAAW,EAAE;QACxB,IAAI,CAACvG,UAAU,GAAG,KAAK;QACvB,IAAI,CAACC,eAAe,GAAG,IAAI;MAC7B;MAEA,IAAI,IAAI,CAACD,UAAU,IAAI,IAAI,CAACD,UAAU,EAAE;QACtC;QACAqE,OAAO,CAACC,GAAG,CAAC,6BAA6B,IAAI,CAACtE,UAAU,EAAE,CAAC;QAC3D,IAAI,CAACkF,0BAA0B,CAAC,IAAI,CAAClF,UAAU,CAAC;QAChD;MACF;MAEA,IAAG,IAAI,CAACE,eAAe,IAAI,IAAI,CAACF,UAAU,EAAC;QACzCqE,OAAO,CAACC,GAAG,CAAC,6BAA6B,IAAI,CAACtE,UAAU,EAAE,CAAC;QAC3D,IAAI,CAACkF,0BAA0B,CAAC,IAAI,CAAClF,UAAU,CAAC;MAClD;MAEA;MACA,IAAI,CAACgC,aAAa,CAACyE,IAAI,CACrB,IAAI,CAACnH,oBAAoB,CAACoH,MAAM,CAAC9C,SAAS,CAAE/B,KAAK,IAAI;QACnD,IAAI,CAACA,KAAK,GAAGA,KAAK;QAClB,IAAI,CAACR,WAAW,GAAG,IAAI,CAACsF,oBAAoB,CAAC9E,KAAK,CAAC;QAEnD;QACA,IAAI,CAAC+E,kBAAkB,EAAE;MAC3B,CAAC,CAAC,CACH;MAED,IAAI,CAAC5E,aAAa,CAACyE,IAAI,CACrB,IAAI,CAACnH,oBAAoB,CAACuH,MAAM,CAACjD,SAAS,CAAE9B,KAAK,IAAI;QACnD,IAAI,CAACA,KAAK,GAAGA,KAAK;QAClB,IAAI,CAACR,WAAW,GAAG,IAAI,CAACwF,oBAAoB,CAAChF,KAAK,CAAC;MACrD,CAAC,CAAC,CACH;MAED;MACA,IAAI,CAACE,aAAa,CAACyE,IAAI,CACrB,IAAI,CAACjI,UAAU,CAAC,aAAa,CAAC,CAACiF,YAAY,CAACG,SAAS,CAAEmD,WAAW,IAAI;QACpE,IAAI,CAACC,YAAY,CAACD,WAAW,CAAC;MAChC,CAAC,CAAC,CACH;MAED,IAAI,CAACE,sBAAsB,EAAE;MAE7B,IAAI,CAAC1H,eAAe,CAAC2H,sBAAsB,EAAE,CAACtD,SAAS,CAAEwB,QAAQ,IAAI;QACnE,IAAI,CAAC7G,SAAS,GAAG6G,QAAQ,CAAClL,GAAG,CAAEiN,KAAY,IAAI;UAC7C,OAAO;YACLzK,IAAI,EAAEyK,KAAK,CAAC9D,mBAAmB;YAC/BvE,KAAK,EAAEqI,KAAK,CAAC9D;WACd;QACH,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IAEA+D,eAAeA,CAAA;MACb;MACC,IAAI,CAACxH,aAAa,CAACyH,qBAAqB,CAAC,IAAI,CAACvH,eAAe,CAAC;IACjE;IAEQmH,sBAAsBA,CAAA;MAC5B,IAAI,CAACvH,YAAY,CACd4H,mCAAmC,CAAC,IAAI,CAAClF,WAAW,EAAE,IAAI,CAACQ,oBAAoB,EAAE,IAAI,CAACN,SAAS,CAAC,CAChGsB,SAAS,CAAEwB,QAAc,IAAI;QAC5B,IAAI,CAAC5D,MAAM,GAAG4D,QAAQ,CAACpE,YAAY;QACnC,IAAI,CAACgG,YAAY,CAAC,IAAI,CAACxI,UAAU,CAAC,aAAa,CAAC,CAACM,KAAK,CAAC;QACvD;QACAyI,UAAU,CAAC,MAAK;UACd,IAAI,CAACpH,YAAY,CAACkG,UAAU,CAAC;YAC3BhD,mBAAmB,EACjB,IAAI,CAACjD,mBAAmB,CAAC,YAAY,CAAC,EAAEiD;WAC3C,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,CAAC;IACN;IAEA;IAEAmE,eAAeA,CAACC,KAAiB;MAC/B,IAAI,IAAI,CAACnH,OAAO,IAAI,IAAI,CAACuC,cAAc,EAAE;QACvC,MAAM6E,MAAM,GAAGD,KAAK,CAACC,MAAqB;QAC1C,IAAI,CAAC,IAAI,CAAC7E,cAAc,CAAC8E,aAAa,CAACC,QAAQ,CAACF,MAAM,CAAC,EAAE;UACvD,IAAI,CAACpH,OAAO,GAAG,KAAK;QACtB;MACF;IACF;IAEA;IACQqG,oBAAoBA,CAAC9E,KAAqB;MAChD,OAAOA,KAAK,CAAC3H,GAAG,CAAE2N,IAAI,KAAM;QAC1BpG,EAAE,EAAEoG,IAAI,CAACpG,EAAE;QACXC,IAAI,EAAEmG,IAAI,CAACnG,IAAI;QACfzD,IAAI,EAAE4J,IAAI,CAAC5J,IAAI;QACf8H,QAAQ,EAAE8B,IAAI,CAAC9B;OAChB,CAAC,CAAC;IACL;IAEA;IACQe,oBAAoBA,CAAChF,KAAqB;MAChD,OAAOA,KAAK,CAAC5H,GAAG,CAAE4N,IAAI,KAAM;QAC1BrG,EAAE,EAAEqG,IAAI,CAACrG,EAAE;QACXsG,MAAM,EAAED,IAAI,CAACC,MAAM;QACnBL,MAAM,EAAEI,IAAI,CAACJ,MAAM;QACnBM,QAAQ,EAAEF,IAAI,CAACE;OAChB,CAAC,CAAC;IACL;IAEQC,mBAAmBA,CAACpG,KAAqB;MAC/C,OAAOA,KAAK,CAACqG,KAAK,CAAC,CAAC,CAAC,CAAChO,GAAG,CAAC,CAAC2N,IAAI,EAAEhC,CAAC,KAAI;QACpC,MAAMsC,YAAY,GAAGtC,CAAC,IAAI,CAAC,GAAGhE,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAACgE,CAAC,GAAG,CAAC,CAAC;QAErD,OAAO;UACLpE,EAAE,EAAE,GAAG0G,YAAY,CAAC1G,EAAE,IAAIoG,IAAI,CAACpG,EAAE,EAAE;UACnCsG,MAAM,EAAEI,YAAY,CAAC1G,EAAE;UACvBiG,MAAM,EAAEG,IAAI,CAACpG;UACb;SACD;MACH,CAAC,CAAC;IACJ;IAEA2G,aAAaA,CAACC,KAAa,EAAEC,aAAgC;MAC3D,MAAMC,QAAQ,GAAGC,KAAK,CAACC,OAAO,CAACH,aAAa,CAAC,GACzCA,aAAa,CAAC,CAAC,CAAC,GAChBA,aAAa;MAEjB,IAAI,CAACC,QAAQ,EAAE;QACb;MACF;MAEA,MAAMG,WAAW,GAAG,IAAI,CAAClI,YAAY,CAAC6H,KAAK,CAAC;MAC5C,MAAMM,OAAO,GAAG,IAAI,CAACxI,YAAY,CAACqD,GAAG,CAACkF,WAAW,CAAC;MAElD,IAAIC,OAAO,EAAE;QACXA,OAAO,CAACC,QAAQ,CAACL,QAAQ,CAAC;MAC5B;MAEA;MACA,KAAK,IAAI1C,CAAC,GAAGwC,KAAK,GAAG,CAAC,EAAExC,CAAC,GAAG,IAAI,CAACtF,MAAM,CAAC3D,MAAM,EAAEiJ,CAAC,EAAE,EAAE;QACnD,IAAI,CAACgD,mBAAmB,CAAChD,CAAC,CAAC;QAC3B,IAAI,CAACnF,eAAe,CAACmF,CAAC,CAAC,GAAG,EAAE;MAC9B;MAEA,MAAMiD,cAAc,GAAGC,MAAM,CAACR,QAAQ,CAAC;MACvC,IAAI,CAACS,KAAK,CAACF,cAAc,CAAC,EAAE;QAC1B,IAAI,CAACG,iBAAiB,CAACZ,KAAK,GAAG,CAAC,EAAES,cAAc,CAAC;MACnD;IACF;IAEAD,mBAAmBA,CAACR,KAAa;MAC/B,MAAMK,WAAW,GAAG,IAAI,CAAClI,YAAY,CAAC6H,KAAK,CAAC;MAC5C,MAAMM,OAAO,GAAG,IAAI,CAACxI,YAAY,CAACqD,GAAG,CAACkF,WAAW,CAAC;MAClD,IAAIC,OAAO,EAAE;QACXA,OAAO,CAACC,QAAQ,CAAC,IAAI,CAAC;MACxB;IACF;IAEAM,kBAAkBA,CAACb,KAAa;MAC9B,OAAO,IAAI,CAAC3H,eAAe,CAAC2H,KAAK,CAAC,IAAI,EAAE;IAC1C;IAEAY,iBAAiBA,CAACZ,KAAa,EAAEc,QAAgB;MAC/C,IAAI,CAAC,IAAI,CAAC3I,YAAY,CAAC6H,KAAK,CAAC,EAAE;MAE/B,IAAI,CAAC9I,eAAe,CAAC6J,eAAe,CAACf,KAAK,EAAEc,QAAQ,CAAC,CAACvF,SAAS,CAAC;QAC9DyF,IAAI,EAAGrF,GAAG,IAAI;UACZ,IAAI,CAACtD,eAAe,CAAC2H,KAAK,CAAC,GAAGG,KAAK,CAACC,OAAO,CAACzE,GAAG,CAAC,GAAGA,GAAG,GAAG,EAAE;QAC7D,CAAC;QACDsF,KAAK,EAAEA,CAAA,KAAK;UACV,IAAI,CAAC5I,eAAe,CAAC2H,KAAK,CAAC,GAAG,EAAE;QAClC;OACD,CAAC;IACJ;IACAkB,WAAWA,CAAA;MACT;MACA,IAAI,CAACvH,aAAa,CAAC4D,OAAO,CAAE4D,GAAG,IAAKA,GAAG,CAACC,WAAW,EAAE,CAAC;MACtD,IAAI,CAACnK,oBAAoB,CAACoK,aAAa,EAAE;IAC3C;IAEA5D,aAAaA,CAAC1B,KAAU,EAAE2B,QAAkC;MAC1D;MACA,MAAM4D,UAAU,GAAGvF,KAAK,EAAEwF,YAAY,EAAEC,OAAO,IAAI,EAAE;MACrD,MAAMC,cAAc,GAAG1F,KAAK,EAAEwF,YAAY,EAAEG,WAAW,IAAI,EAAE;MAC7D,MAAMC,KAAK,GAAG,CAAC,GAAGL,UAAU,EAAE,GAAGG,cAAc,CAAC,CAC7C5P,GAAG,CAAE+P,IAAI,IAAKA,IAAI,EAAEC,QAAQ,CAAC,CAC7BC,IAAI,CAAC,IAAI,CAAC;MACb,MAAM7L,KAAK,GAAG8F,KAAK,EAAEwF,YAAY,EAAEQ,QAAQ,CAAC,CAAC,CAAC;MAC9C,MAAMC,OAAO,GAAiB;QAC5B5I,EAAE,EAAE,IAAI,CAACnC,oBAAoB,CAACgL,cAAc,EAAE;QAC9C5I,IAAI,EAAE,OAAO;QACbzD,IAAI,EAAE;UACJgD,KAAK,EAAEmD,KAAK,CAAC1H,IAAI;UACjBwB,OAAO,EAAEkG,KAAK,CAAC3C,EAAE;UACjBb,SAAS,EAAEwD,KAAK,CAAC1H,IAAI;UACrBC,WAAW,EAAEyH,KAAK,CAACzH,WAAW;UAC9BhB,YAAY,EAAEyI,KAAK,CAACmG,eAAe;UACnCC,KAAK,EAAE,GAAG;UAAE;UACZlM,KAAK,EAAEA,KAAK,EAAE+E,mBAAmB;UACjC2G,KAAK;UACLL;SACD;QACD5D,QAAQ,EAAEA;OACX;MAED,IAAI,CAACzG,oBAAoB,CAACmL,OAAO,CAACJ,OAAO,CAAC;MAE1C,IAAI,IAAI,CAAC7L,UAAU,CAAC,kBAAkB,CAAC,CAACM,KAAK,EAAE;MAC/C,MAAM+C,KAAK,GAAG,IAAI,CAACvC,oBAAoB,CAACoL,WAAW,EAAE;MACrD,MAAMC,QAAQ,GAAG9I,KAAK,CAAC+I,EAAE,CAAC,CAAC,CAAC,CAAC;MAC7B,IAAID,QAAQ,EAAE;QACZ,IAAI,CAACrL,oBAAoB,CAACuL,OAAO,CAAC;UAChCpJ,EAAE,EAAE,GAAGkJ,QAAQ,CAAClJ,EAAE,IAAI4I,OAAO,CAAC5I,EAAE,EAAE;UAClCsG,MAAM,EAAE4C,QAAQ,CAAClJ,EAAE;UACnBiG,MAAM,EAAE2C,OAAO,CAAC5I;SACjB,CAAC;MACJ;IACF;IAEA;IACAqJ,eAAeA,CAACrD,KAGf;MACC,MAAMsD,SAAS,GAAGtD,KAAK,CAACA,KAAK;MAC7B,MAAM1B,QAAQ,GAAG0B,KAAK,CAAC1B,QAAQ;MAE/B,IAAIgF,SAAS,CAACC,YAAY,EAAE;QAC1B,MAAMC,SAAS,GAAGF,SAAS,CAACC,YAAY,CAACE,OAAO,CAAC,uBAAuB,CAAC;QAEzE,IAAID,SAAS,EAAE;UACb,IAAI;YACF,MAAM7G,KAAK,GAAG+G,IAAI,CAACC,KAAK,CAACH,SAAS,CAAC;YACnC;YACA;YACA;YACA;YACA;YAEA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YAEA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YAEA,IAAI,CAACnF,aAAa,CAAC1B,KAAK,EAAE2B,QAAQ,CAAC;UACrC,CAAC,CAAC,OAAOuD,KAAK,EAAE;YACdjF,OAAO,CAACiF,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;UAC5C;QACF;MACF;IACF;IAEA+B,mBAAmBA,CAACvD,IAAgB;MAClC,MAAMwD,OAAO,GAAiB;QAC5B7J,EAAE,EAAEqG,IAAI,CAACrG,EAAE;QACXsG,MAAM,EAAED,IAAI,CAACC,MAAM;QACnBL,MAAM,EAAEI,IAAI,CAACJ,MAAM;QACnBM,QAAQ,EAAEF,IAAI,CAACE,QAAQ,IAAI;OAC5B;MAED,IAAI,CAAC1I,oBAAoB,CAACuL,OAAO,CAACS,OAAO,CAAC;IAC5C;IAEAtE,YAAYA,CAACD,WAAmB;MAC9B;MACA,IAAI,CAACA,WAAW,IAAIA,WAAW,CAACwE,IAAI,EAAE,KAAK,EAAE,EAAE;QAC7C,IAAI,CAAC5J,cAAc,GAAG,CAAC,GAAG,IAAI,CAACH,MAAM,CAAC;MACxC,CAAC,MAAM;QACLuF,WAAW,GAAGA,WAAW,CAACpD,WAAW,EAAE,CAAC4H,IAAI,EAAE;QAC9C,IAAI,CAAC5J,cAAc,GAAG,IAAI,CAACH,MAAM,CAACuC,MAAM,CACrCK,KAAK,IACJA,KAAK,CAAC1H,IAAI,CAACiH,WAAW,EAAE,CAACO,QAAQ,CAAC6C,WAAW,CAAC,IAC9C3C,KAAK,CAACzH,WAAW,CAACgH,WAAW,EAAE,CAACO,QAAQ,CAAC6C,WAAW,CAAC,IACpD3C,KAAK,CAAC1C,IAAI,IAAI0C,KAAK,CAAC1C,IAAI,CAACiC,WAAW,EAAE,CAACO,QAAQ,CAAC6C,WAAW,CAAE,IAC7D3C,KAAK,CAACzI,YAAY,IACjByI,KAAK,CAACzI,YAAY,CAAC6P,IAAI,CAAEC,GAAG,IAC1BA,GAAG,CAAC9H,WAAW,EAAE,CAACO,QAAQ,CAAC6C,WAAW,CAAC,CACvC,CACP;MACH;MAEA;MACA,IAAI,CAAC2E,qBAAqB,EAAE;IAC9B;IAEAA,qBAAqBA,CAAA;MACnB,IAAI,CAAC9J,eAAe,GAAG,IAAI,CAACJ,MAAM,CAACuC,MAAM,CACtCK,KAAK,IAAK,CAAC,IAAI,CAACnC,YAAY,CAAC0J,GAAG,CAACvH,KAAK,CAAC3C,EAAE,CAAC,CAC5C;IACH;IAEApF,WAAWA,CAACoL,KAAgB,EAAErD,KAAY;MACxC,IAAIqD,KAAK,CAACuD,YAAY,EAAE;QACtBvD,KAAK,CAACuD,YAAY,CAACY,OAAO,CACxB,uBAAuB,EACvBT,IAAI,CAACU,SAAS,CAACzH,KAAK,CAAC,CACtB;QACDqD,KAAK,CAACuD,YAAY,CAACc,aAAa,GAAG,MAAM;MAC3C;IACF;IAEAC,YAAYA,CAACC,MAAc;MACzB,IAAI,CAAC1M,oBAAoB,CAAC2M,UAAU,CAACD,MAAM,CAAC;MAE5C;MACA,IAAI,IAAI,CAACjK,cAAc,KAAKiK,MAAM,EAAE;QAClC,IAAI,CAACjK,cAAc,GAAG,IAAI;MAC5B;MACA,IAAI,CAACU,cAAc,CAACyJ,MAAM,CAACF,MAAM,CAAC;IACpC;IAEAG,cAAcA,CAACH,MAAc;MAC3B,IAAI,CAACjK,cAAc,GAAGiK,MAAM;IAC9B;IAEAI,WAAWA,CAACnO,IAAgD;MAC1D;MACA,MAAMoO,SAAS,GAAG,IAAI,CAACxK,KAAK,CAACyK,SAAS,CAAEzE,IAAI,IAAKA,IAAI,CAACpG,EAAE,KAAKxD,IAAI,CAAC+N,MAAM,CAAC;MACzE,IAAIK,SAAS,KAAK,CAAC,CAAC,EAAE;MAEtB;MACA,MAAME,YAAY,GAAG,CAAC,GAAG,IAAI,CAAC1K,KAAK,CAAC;MACpC0K,YAAY,CAACF,SAAS,CAAC,GAAG;QACxB,GAAG,IAAI,CAACxK,KAAK,CAACwK,SAAS,CAAC;QACxBtG,QAAQ,EAAE9H,IAAI,CAAC8H;OAChB;MAED;MACA,IAAI,CAACzG,oBAAoB,CAACkN,mBAAmB,CAACD,YAAY,CAAC;MAE3D;MACA,IAAI,CAAC9M,GAAG,CAACgN,aAAa,EAAE;IAC1B;IAEAhP,iBAAiBA,CAACQ,IAIjB;MACC;MACA;IAAA;IAGFN,kBAAkBA,CAACM,IAGlB;MACC;IAAA;IAGFJ,wBAAwBA,CAAC4J,KAA4C;MACnE,IAAI,CAACA,KAAK,IAAI,CAACA,KAAK,CAACuE,MAAM,IAAI,CAACvE,KAAK,CAACiF,UAAU,EAAE;QAChDrI,OAAO,CAACsI,IAAI,CAAC,4BAA4B,EAAElF,KAAK,CAAC;QACjD;MACF;MAEA;MAEA,IAAI,CAAChF,cAAc,CAACmK,GAAG,CAACnF,KAAK,CAACuE,MAAM,EAAEvE,KAAK,CAACiF,UAAU,CAAC;MAEvD,MAAMG,aAAa,GAAGrE,KAAK,CAACsE,IAAI,CAAC,IAAI,CAACrK,cAAc,CAACsK,MAAM,EAAE,CAAC,CAC3DhJ,MAAM,CAACgJ,MAAM,IAAIA,MAAM,IAAI,IAAI,CAAC,CAChCC,IAAI,EAAE,CACNjJ,MAAM,CAACkG,IAAI,IAAIA,IAAI,IAAI,IAAI,CAAC;MAE/B,IAAI,CAACzH,iBAAiB,GAAGqK,aAAa;MAEtC;IACF;IAGAI,MAAMA,CAAA;MACJ;MACA,IAAI,CAAC,IAAI,CAAC9M,YAAY,CAACqD,GAAG,CAAC,MAAM,CAAC,EAAE1E,KAAK,EAAEyM,IAAI,EAAE,EAAE;QACjD,IAAI,CAAC1L,aAAa,CAACqN,OAAO,CAAC;UACzBzI,KAAK,EAAE,SAAS;UAChB0I,OAAO,EAAE,uDAAuD;UAChEC,iBAAiB,EAAE,IAAI;UACvBC,iBAAiB,EAAE;SACpB,CAAC;QACF;MACF;MAEA;MACA,IAAI,CAACxN,aAAa,CAACyN,OAAO,CAAC;QACzB7I,KAAK,EAAE,eAAe;QACtB0I,OAAO,EAAE,8CAA8C;QACvDI,WAAW,EAAE,MAAM;QACnBC,UAAU,EAAE;OACb,CAAC,CAACC,IAAI,CAAEC,SAAS,IAAI;QACpB,IAAI,CAACA,SAAS,EAAE;UACd,OAAO,CAAC;QACV;QAEA;QACA,MAAMC,aAAa,GAAG,IAAI,CAAC9N,aAAa,CAAC+N,OAAO,CAAC;UAC/CT,OAAO,EAAE,oBAAoB;UAC7BU,WAAW,EAAE;SACd,CAAC;QAEF,MAAMC,OAAO,GAAG,IAAI,CAACC,mBAAmB,EAAE;QAE1C;QACA,MAAMC,aAAa,GAAG,IAAI,CAAChO,UAAU,IAAI,CAAC,IAAI,CAACE,eAAe,GAC1D,IAAI,CAACX,eAAe,CAAC0O,cAAc,CAACH,OAAO,CAAC,GAC5C,IAAI,CAACvO,eAAe,CAAC2O,YAAY,CAACJ,OAAO,CAAC;QAE9CE,aAAa,CAACpK,SAAS,CAAC;UACtByF,IAAI,EAAGjE,QAAa,IAAI;YACtB;YACAuI,aAAa,CAACQ,KAAK,EAAE;YAErB;YACA,IAAI,CAACtO,aAAa,CAACuO,OAAO,CAAC;cACzB3J,KAAK,EAAE,SAAS;cAChB0I,OAAO,EAAE,8BAA8B;cACvCC,iBAAiB,EAAE,IAAI;cACvBC,iBAAiB,EAAE;aACpB,CAAC;YAEF;YACA,IAAI,CAAC,IAAI,CAACrN,UAAU,IAAI,IAAI,CAACE,eAAe,EAAE;cAC5C,IAAI,CAACF,UAAU,GAAGoF,QAAQ,CAACpF,UAAU,IAAIoF,QAAQ,CAAC3D,EAAE;cACpD,IAAI,CAACxB,UAAU,GAAG,IAAI;cACtB,IAAI,CAACC,eAAe,GAAG,KAAK;cAC5B;cACA,IAAI,CAACd,MAAM,CAACyF,QAAQ,CAAC,EAAE,EAAE;gBACvBwJ,UAAU,EAAE,IAAI,CAAChP,KAAK;gBACtB2F,WAAW,EAAE;kBAAEvD,EAAE,EAAE,IAAI,CAACzB;gBAAU,CAAE;gBACpCsO,UAAU,EAAE;eACb,CAAC;YACJ;UACF,CAAC;UACDhF,KAAK,EAAGA,KAAK,IAAI;YACf;YACAqE,aAAa,CAACQ,KAAK,EAAE;YAErB;YACA,MAAMI,YAAY,GAAGjF,KAAK,EAAEA,KAAK,EAAE6D,OAAO,IAAI,8CAA8C;YAC5F,MAAMqB,gBAAgB,GAAGD,YAAY,CAAC5K,WAAW,EAAE,CAACO,QAAQ,CAAC,WAAW,CAAC,IAClDoF,KAAK,CAACmF,MAAM,KAAK,GAAG,CAAC,CAAC;YAE7C,IAAID,gBAAgB,EAAE;cACpB,IAAI,CAAC3O,aAAa,CAACqN,OAAO,CAAC;gBACzBzI,KAAK,EAAE,oBAAoB;gBAC3B0I,OAAO,EAAE,2EAA2E;gBACpFC,iBAAiB,EAAE,IAAI;gBACvBC,iBAAiB,EAAE;eACpB,CAAC;YACJ,CAAC,MAAM;cACL,IAAI,CAACxN,aAAa,CAACyJ,KAAK,CAAC;gBACvB7E,KAAK,EAAE,OAAO;gBACd0I,OAAO,EAAEoB,YAAY;gBACrBnB,iBAAiB,EAAE,IAAI;gBACvBC,iBAAiB,EAAE;eACpB,CAAC;YACJ;UACF;SACD,CAAC;MACJ,CAAC,CAAC;IACJ;IAEAqB,MAAMA,CAAA;MACJ,IAAI,CAACtP,MAAM,CAACyF,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;IAC5C;IAEA8J,OAAOA,CAAA;MACL,IAAI,CAACrP,oBAAoB,CAACoK,aAAa,EAAE;MACzC,IAAI,CAAC3H,cAAc,GAAG,IAAI;IAC5B;IAEA6M,MAAMA,CAAA;MACJvK,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;IACpD;IAEAuK,MAAMA,CAAA;MACJxK,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;IACpD;IAEAwK,oBAAoBA,CAACC,KAGpB;MACC1K,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEyK,KAAK,CAAC;MAE3C;MACA,MAAMC,aAAa,GAAmBD,KAAK,CAAClN,KAAK,CAAC3H,GAAG,CAAE2N,IAAI,KAAM;QAC/DpG,EAAE,EAAEoG,IAAI,CAACpG,EAAE;QACXC,IAAI,EAAEmG,IAAI,CAACnG,IAAI;QACfzD,IAAI,EAAE4J,IAAI,CAAC5J,IAAI;QACf8H,QAAQ,EAAE8B,IAAI,CAAC9B;OAChB,CAAC,CAAC;MAEH;MACA,MAAMkJ,aAAa,GAAmBF,KAAK,CAACjN,KAAK,CAAC5H,GAAG,CAAE4N,IAAI,KAAM;QAC/DrG,EAAE,EAAEqG,IAAI,CAACrG,EAAE;QACXsG,MAAM,EAAED,IAAI,CAACC,MAAM;QACnBL,MAAM,EAAEI,IAAI,CAACJ,MAAM;QACnBM,QAAQ,EAAEF,IAAI,CAACE;OAChB,CAAC,CAAC;MAEH;MACA,IAAI,CAAC1I,oBAAoB,CAAC4P,QAAQ,CAACF,aAAa,CAAC;MACjD,IAAI,CAAC1P,oBAAoB,CAAC6P,QAAQ,CAACF,aAAa,CAAC;IACnD;IAEAlB,mBAAmBA,CAAA;MACjB,MAAMqB,SAAS,GAAG,IAAI,CAACjP,YAAY,CAACkP,WAAW,EAAE;MACjD,MAAMvB,OAAO,GAAwB;QACnCpR,IAAI,EAAE0S,SAAS,EAAE1S,IAAI,IAAI,IAAI;QAC7BC,WAAW,EAAEyS,SAAS,EAAEzS,WAAW,IAAI,IAAI;QAC3C0I,cAAc,EAAE,EAAE;QAClBiK,eAAe,EAAE;UACfC,OAAO,EAAE,GAAG;UACZnF,QAAQ,EAAE,EAAE;UACZoF,kBAAkB,EAAE,EAAE;UACtBtJ,UAAU,EAAE,EAAE;UACdxH,IAAI,EAAE0Q,SAAS,EAAE1Q,IAAI,IAAI,IAAI;UAC7BE,QAAQ,EAAEwQ,SAAS,EAAExQ,QAAQ,IAAI,IAAI;UACrCH,WAAW,EAAE2Q,SAAS,EAAE3Q,WAAW,IAAI,GAAG;UAC1CgR,mBAAmB,EAAE,KAAK;UAC1BC,iBAAiB,EAAE;SACpB;QACDzJ,SAAS,EAAE,IAAI,CAAChG,UAAU,GAAGmP,SAAS,EAAEnJ,SAAS,GAAG,IAAI,CAACtG,YAAY,CAACgQ,aAAa,EAAE;QACrFC,UAAU,EAAE,IAAI,CAACjQ,YAAY,CAACgQ,aAAa;OAC5C;MAED,MAAM9N,KAAK,GAAG,IAAI,CAACvC,oBAAoB,CAACoL,WAAW,EAAE;MAErD,IAAI7I,KAAK,CAACjF,MAAM,EAAE;QAChBkR,OAAO,CAAC,gBAAgB,CAAC,GAAGjM,KAAK,CAAC3H,GAAG,CAAC,CAAC2N,IAAI,EAAEgI,KAAK,MAAM;UACtDpK,MAAM,EAAEoK,KAAK,GAAG,CAAC;UACjB3R,OAAO,EAAE2J,IAAI,CAAC5J,IAAI,CAACC;SACpB,CAAC,CAAC;MACL;MAEA,IAAI,IAAI,CAACsE,iBAAiB,CAAC5F,MAAM,GAAG,CAAC,EAAE;QACrC,MAAMkT,WAAW,GAAW,EAAE;QAC9B,IAAI,CAACtN,iBAAiB,CAACoD,OAAO,CAAEmK,SAAS,IAAI;UAC3CA,SAAS,CAACC,UAAU,CAACpK,OAAO,CAAEqK,KAAW,IAAI;YAC3CH,WAAW,CAACrJ,IAAI,CAAC;cACfvI,OAAO,EAAE+R,KAAK,CAAC/R,OAAO;cACtBgS,MAAM,EAAEH,SAAS,CAACG,MAAM;cACxBC,eAAe,EAAEF,KAAK,CAACG,WAAW,IAAIH,KAAK,CAACE,eAAe;cAC3DE,aAAa,EAAEJ,KAAK,CAACI,aAAa;cAClCvR,KAAK,EAAEmR,KAAK,CAACnR;aACd,CAAC;UACJ,CAAC,CAAC;QACJ,CAAC,CAAC;QAEFgP,OAAO,CAAC,iBAAiB,CAAC,CAAC0B,kBAAkB,GAAGM,WAAW;MAC7D;MAEA,IAAIV,SAAS,EAAE/L,mBAAmB,EAAE;QAClC,MAAMiN,YAAY,GAAG;UACnB7O,EAAE,EAAE2N,SAAS,CAACmB,UAAU;UACxBlN,mBAAmB,EAAE+L,SAAS,CAAC/L,mBAAmB;UAClD3E,IAAI,EAAE0Q,SAAS,CAAC1Q,IAAI;UACpBE,QAAQ,EAAEwQ,SAAS,CAACxQ,QAAQ;UAC5BH,WAAW,EAAE2Q,SAAS,CAAC3Q;SACxB;QAEDqP,OAAO,CAAC,iBAAiB,CAAC,CAAC5H,UAAU,GAAG,CAACoK,YAAY,CAAC;MACxD;MAEA,IAAGlB,SAAS,EAAEoB,kBAAkB,EAAE5T,MAAM,EAAC;QACvCkR,OAAO,CAAC,iBAAiB,CAAC,CAAC4B,iBAAiB,GAAG,CAAC,GAAGN,SAAS,CAACoB,kBAAkB,CAAC;QAChF1C,OAAO,CAAC,iBAAiB,CAAC,CAAC1D,QAAQ,GAAG,CAAC,GAAGgF,SAAS,CAACoB,kBAAkB,CAAC;MACzE;MAEA,IAAI,IAAI,CAACxQ,UAAU,IAAI,CAAC,IAAI,CAAC8E,eAAe,EAAE,EAAE;QAC9CgJ,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC9N,UAAU;MACjC;MAEA,OAAO8N,OAAO;IAChB;IAGA2C,SAASA,CAAA;MACP;MACA,IAAI,CAAC,IAAI,CAACtQ,YAAY,CAACqD,GAAG,CAAC,MAAM,CAAC,EAAE1E,KAAK,EAAE;QACzC,IAAI,CAACe,aAAa,CAACqN,OAAO,CAAC;UACzBzI,KAAK,EAAE,SAAS;UAChB0I,OAAO,EAAE,iEAAiE;UAC1EC,iBAAiB,EAAE,IAAI;UACvBC,iBAAiB,EAAE;SACpB,CAAC;QACF;MACF;MACA,MAAMS,OAAO,GAAG,IAAI,CAACC,mBAAmB,EAAE;MAC1C,IAAI,IAAI,CAAC/N,UAAU,IAAI,CAAC,IAAI,CAACE,eAAe,EAAE;QAC5C,IAAI,CAACX,eAAe,CAAC0O,cAAc,CAACH,OAAO,CAAC,CAAClK,SAAS,CAAC,MAAK;UAC1D,IAAI,CAACxE,MAAM,CAACyF,QAAQ,CAAC,CAAC,0BAA0B,EAAE,IAAI,CAAC7E,UAAU,CAAC,CAAC;QACrE,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,IAAI,CAACT,eAAe,CAAC2O,YAAY,CAACJ,OAAO,CAAC,CAAClK,SAAS,CAAEwB,QAAa,IAAI;UACrE,IAAI,CAAChG,MAAM,CAACyF,QAAQ,CAAC,CAAC,0BAA0B,EAAEO,QAAQ,CAACpF,UAAU,CAAC,CAAC;QACzE,CAAC,CAAC;QACF;MACF;IACF;IAEA;IACAxB,UAAUA,CAAC9B,IAAY;MACrB,OAAO,IAAI,CAACyD,YAAY,CAACqD,GAAG,CAAC9G,IAAI,CAAgB;IACnD;IAEA;;;;IAIAgU,WAAWA,CAACxS,OAAe;MACzB,OAAO,IAAI,CAAC+D,YAAY,CAAC0J,GAAG,CAACzN,OAAO,CAAC;IACvC;IAEA;;;IAGA0I,kBAAkBA,CAAA;MAChB,IAAI,CAAC3E,YAAY,CAAC0C,KAAK,EAAE;MACzB,IAAI,CAAC9C,KAAK,CAAC+D,OAAO,CAAEiC,IAAI,IAAI;QAC1B,IAAIA,IAAI,CAAC5J,IAAI,IAAI4J,IAAI,CAAC5J,IAAI,CAACC,OAAO,EAAE;UAClC,IAAI,CAAC+D,YAAY,CAAC0O,GAAG,CAAC9I,IAAI,CAAC5J,IAAI,CAACC,OAAO,CAAC;QAC1C;MACF,CAAC,CAAC;MAEF;MACA,IAAI,CAACwN,qBAAqB,EAAE;IAC9B;IACAkF,UAAU,GAAmB,EAAE;IAC/BC,cAAcA,CAACpJ,KAAc;MAC3BpD,OAAO,CAACC,GAAG,CAACmD,KAAK,CAAC;MAClB,IAAI,CAACjJ,UAAU,CAAC,kBAAkB,CAAC,CAACoK,QAAQ,CAACnB,KAAK,CAAC;MACnD,IAAI,CAACnH,OAAO,GAAGmH,KAAK;MACpB,IAAIA,KAAK,EAAE;QACT,IAAI,CAACmJ,UAAU,GAAG,IAAI,CAACtR,oBAAoB,CAACwR,WAAW,EAAE;QACzD,IAAI,CAACxR,oBAAoB,CAAC6P,QAAQ,CAAC,EAAE,CAAC;MACxC,CAAC,MAAM;QACL,MAAMtN,KAAK,GAAG,IAAI,CAACvC,oBAAoB,CAACoL,WAAW,EAAE;QACrD,MAAM5I,KAAK,GAAG,IAAI,CAACmG,mBAAmB,CAACpG,KAAK,CAAC;QAC7C,IAAI,CAACvC,oBAAoB,CAAC6P,QAAQ,CAACrN,KAAK,CAAC;QACzC,IAAI,CAAC,IAAI,CAAC1B,mBAAmB,CAAC,YAAY,CAAC,EAAE;UAC3C,IAAI,CAAC5B,UAAU,CAAC,qBAAqB,CAAC,CAACoK,QAAQ,CAAC,IAAI,CAAC;QACvD;MACF;IACF;IACAmI,SAASA,CAACC,IAAa;MACrB,IAAI,CAAC1Q,OAAO,GAAG0Q,IAAI;IACrB;IAEAhT,yBAAyBA,CAACE,OAAe;MACvC,MAAM+S,SAAS,GAAG,IAAI,CAACtO,YAAY,EAAE2M,eAAe,EAAEE,kBAAkB;MAExE,IAAIyB,SAAS,EAAE/S,OAAO,KAAKA,OAAO,EAAE;QAClC,OAAO;UACLgS,MAAM,EAAEe,SAAS,CAACf,MAAM;UACxBG,aAAa,EAAEY,SAAS,CAACZ,aAAa;UACtCvR,KAAK,EAAEmS,SAAS,CAACnS;SAClB;MACH;MACA,OAAO,IAAI;IACb;;uCAz6BWI,uBAAuB,EAAAlE,EAAA,CAAAkW,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAApW,EAAA,CAAAkW,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAtW,EAAA,CAAAkW,iBAAA,CAAAG,EAAA,CAAAE,cAAA,GAAAvW,EAAA,CAAAkW,iBAAA,CAAAM,EAAA,CAAAC,oBAAA,GAAAzW,EAAA,CAAAkW,iBAAA,CAAAQ,EAAA,CAAAC,eAAA,GAAA3W,EAAA,CAAAkW,iBAAA,CAAAU,EAAA,CAAAC,gBAAA,GAAA7W,EAAA,CAAAkW,iBAAA,CAAAlW,EAAA,CAAA8W,iBAAA,GAAA9W,EAAA,CAAAkW,iBAAA,CAAAa,EAAA,CAAAC,mBAAA,GAAAhX,EAAA,CAAAkW,iBAAA,CAAAe,EAAA,CAAAC,mBAAA,GAAAlX,EAAA,CAAAkW,iBAAA,CAAAiB,EAAA,CAAAC,aAAA,GAAApX,EAAA,CAAAkW,iBAAA,CAAAmB,EAAA,CAAA1X,aAAA;IAAA;;YAAvBuE,uBAAuB;MAAAoT,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;iCAGI9Y,gBAAgB;;;;;;;;;;;UAH3CqB,EAAA,CAAAY,UAAA,uBAAA+W,qDAAA7W,MAAA;YAAA,OAAA4W,GAAA,CAAAlL,eAAA,CAAA1L,MAAA,CAAuB;UAAA,UAAAd,EAAA,CAAA4X,iBAAA;;;;;;uCAJvB,CAACjY,aAAa,CAAC;MAAAkY,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAR,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UClEpBzX,EAXR,CAAAC,cAAA,aAAuC,aAEX,aAEC,aAMtB,aACmD;UAAxBD,EAAA,CAAAY,UAAA,mBAAAsX,sDAAA;YAAAlY,EAAA,CAAAe,aAAA,CAAAoX,GAAA;YAAA,OAAAnY,EAAA,CAAAoB,WAAA,CAASsW,GAAA,CAAAvO,WAAA,EAAa;UAAA,EAAC;UAC/CnJ,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAE,MAAA,oBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACtBH,EAAA,CAAAsB,SAAA,kBAKW;UACbtB,EAAA,CAAAG,YAAA,EAAM;UAOAH,EAJN,CAAAC,cAAA,aAA6D,cAE/B,gBACK,uBAM5B;UACCD,EAAA,CAAAsB,SAAA,oBAMW;UAGjBtB,EAFI,CAAAG,YAAA,EAAc,EACT,EACH;UAGNH,EAAA,CAAAC,cAAA,eAAyB;UAuDvBD,EArDA,CAAAO,UAAA,KAAA6X,uCAAA,mBAKC,KAAAC,uCAAA,kBAmDA;UAUHrY,EAAA,CAAAG,YAAA,EAAM;UAIJH,EADF,CAAAC,cAAA,eAAkC,sBAe/B;UAFCD,EAAA,CAAAY,UAAA,uBAAA0X,kEAAA;YAAAtY,EAAA,CAAAe,aAAA,CAAAoX,GAAA;YAAA,OAAAnY,EAAA,CAAAoB,WAAA,CAAasW,GAAA,CAAA9N,gBAAA,EAAkB;UAAA,EAAC;UAMxC5J,EAHM,CAAAG,YAAA,EAAa,EACT,EACF,EACF;UAIJH,EADF,CAAAC,cAAA,eAA2B,4BAuBxB;UANCD,EAVA,CAAAY,UAAA,2BAAA2X,4EAAAzX,MAAA;YAAAd,EAAA,CAAAe,aAAA,CAAAoX,GAAA;YAAA,OAAAnY,EAAA,CAAAoB,WAAA,CAAiBsW,GAAA,CAAA5H,eAAA,CAAAhP,MAAA,CAAuB;UAAA,EAAC,0BAAA0X,2EAAA1X,MAAA;YAAAd,EAAA,CAAAe,aAAA,CAAAoX,GAAA;YAAA,OAAAnY,EAAA,CAAAoB,WAAA,CACzBsW,GAAA,CAAAvG,cAAA,CAAArQ,MAAA,CAAsB;UAAA,EAAC,uBAAA2X,wEAAA3X,MAAA;YAAAd,EAAA,CAAAe,aAAA,CAAAoX,GAAA;YAAA,OAAAnY,EAAA,CAAAoB,WAAA,CAC1BsW,GAAA,CAAAtG,WAAA,CAAAtQ,MAAA,CAAmB;UAAA,EAAC,yBAAA4X,0EAAA5X,MAAA;YAAAd,EAAA,CAAAe,aAAA,CAAAoX,GAAA;YAAA,OAAAnY,EAAA,CAAAoB,WAAA,CAClBsW,GAAA,CAAA3G,YAAA,CAAAjQ,MAAA,CAAoB;UAAA,EAAC,+BAAA6X,gFAAA7X,MAAA;YAAAd,EAAA,CAAAe,aAAA,CAAAoX,GAAA;YAAA,OAAAnY,EAAA,CAAAoB,WAAA,CACfsW,GAAA,CAAAjV,iBAAA,CAAA3B,MAAA,CAAyB;UAAA,EAAC,+BAAA8X,gFAAA9X,MAAA;YAAAd,EAAA,CAAAe,aAAA,CAAAoX,GAAA;YAAA,OAAAnY,EAAA,CAAAoB,WAAA,CAC1BsW,GAAA,CAAArH,mBAAA,CAAAvP,MAAA,CAA2B;UAAA,EAAC,wBAAA+X,yEAAA;YAAA7Y,EAAA,CAAAe,aAAA,CAAAoX,GAAA;YAAA,OAAAnY,EAAA,CAAAoB,WAAA,CACnCsW,GAAA,CAAA9D,MAAA,EAAQ;UAAA,EAAC,wBAAAkF,yEAAA;YAAA9Y,EAAA,CAAAe,aAAA,CAAAoX,GAAA;YAAA,OAAAnY,EAAA,CAAAoB,WAAA,CACTsW,GAAA,CAAA7D,MAAA,EAAQ;UAAA,EAAC,yBAAAkF,0EAAA;YAAA/Y,EAAA,CAAAe,aAAA,CAAAoX,GAAA;YAAA,OAAAnY,EAAA,CAAAoB,WAAA,CACRsW,GAAA,CAAA/D,OAAA,EAAS;UAAA,EAAC,kCAAAqF,mFAAA;YAAAhZ,EAAA,CAAAe,aAAA,CAAAoX,GAAA;YAAA,OAAAnY,EAAA,CAAAoB,WAAA,CACDsW,GAAA,CAAAjC,SAAA,EAAW;UAAA,EAAC,0BAAAwD,2EAAAnY,MAAA;YAAAd,EAAA,CAAAe,aAAA,CAAAoX,GAAA;YAAA,OAAAnY,EAAA,CAAAoB,WAAA,CACpBsW,GAAA,CAAA5D,oBAAA,CAAAhT,MAAA,CAA4B;UAAA,EAAC;UA+B7Cd,EAvBA,CAAAO,UAAA,KAAA2Y,+CAAA,gCAAAlZ,EAAA,CAAAmZ,sBAAA,CAQC,KAAAC,uCAAA,kBAsBA;UAIDpZ,EAAA,CAAAqZ,uBAAA,QAAyB;UAIjBrZ,EAHN,CAAAC,cAAA,eAAmD,eACf,eACF,gBACD;UAAAD,EAAA,CAAAE,MAAA,IAEvB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACXH,EAAA,CAAAC,cAAA,sBACqD;UAD5BD,EAAA,CAAAY,UAAA,2BAAA0Y,sEAAAxY,MAAA;YAAAd,EAAA,CAAAe,aAAA,CAAAoX,GAAA;YAAA,OAAAnY,EAAA,CAAAoB,WAAA,CAAiBsW,GAAA,CAAA7B,cAAA,CAAA/U,MAAA,CAAsB;UAAA,EAAC;UAIrEd,EAFI,CAAAG,YAAA,EAAa,EACT,EACF;UACNH,EAAA,CAAAO,UAAA,KAAAgZ,uCAAA,oBAAkG;UAiDpGvZ,EAAA,CAAAG,YAAA,EAAM;;UAMlBH,EAJQ,CAAAG,YAAA,EAAmB,EACf,EACF,EACF,EACF;UAGNH,EAAA,CAAAsB,SAAA,oBAA4B;;;;UAlQpBtB,EAAA,CAAAI,SAAA,GAAoC;UAApCJ,EAAA,CAAAwZ,WAAA,cAAA9B,GAAA,CAAAvQ,gBAAA,CAAoC;UACpCnH,EAAA,CAAAS,UAAA,YAAAT,EAAA,CAAAyZ,eAAA,KAAAC,GAAA,GAAAhC,GAAA,CAAAvQ,gBAAA,EAAwC;UAKpCnH,EAAA,CAAAI,SAAA,GAA2D;UAA3DJ,EAAA,CAAAS,UAAA,aAAAiX,GAAA,CAAAvQ,gBAAA,+BAA2D;UAQpCnH,EAAA,CAAAI,SAAA,EAAiC;UAAjCJ,EAAA,CAAAwZ,WAAA,WAAA9B,GAAA,CAAAvQ,gBAAA,CAAiC;UAGlDnH,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAAS,UAAA,cAAAiX,GAAA,CAAAnQ,UAAA,CAAwB;UAUxBvH,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAS,UAAA,gBAAe;UAYDT,EAAA,CAAAI,SAAA,GAAiB;UAAjBJ,EAAA,CAAAS,UAAA,YAAAiX,GAAA,CAAA/Q,cAAA,CAAiB;UAsDlC3G,EAAA,CAAAI,SAAA,EAAkC;UAAlCJ,EAAA,CAAAS,UAAA,SAAAiX,GAAA,CAAA9Q,eAAA,CAAAhF,MAAA,OAAkC;UAmBnC5B,EAAA,CAAAI,SAAA,GAKE;UAIFJ,EATA,CAAAS,UAAA,iBAAAT,EAAA,CAAA2Z,eAAA,KAAAC,GAAA,EAKE,iBAIc;UAUpB5Z,EAAA,CAAAI,SAAA,GAAqB;UAoBrBJ,EApBA,CAAAS,UAAA,UAAAiX,GAAA,CAAArR,WAAA,CAAqB,UAAAqR,GAAA,CAAApR,WAAA,CACA,oBAAAoR,GAAA,CAAAnR,eAAA,CACc,oBAAAmR,GAAA,CAAArU,cAAA,CAAAwW,eAAA,CACe,sBAAAnC,GAAA,CAAArU,cAAA,CAAAyW,OAAA,CACN,yBAYpB,0BACC,sBAAApC,GAAA,CAAA/R,iBAAA,CACc,2BAAA+R,GAAA,CAAAlU,UAAA,SACM,uBAAAkU,GAAA,CAAAlU,UAAA,gBACG;UA6B7CxD,EAAA,CAAAI,SAAA,GAEwD;UAFxDJ,EAAA,CAAAS,UAAA,SAAAiX,GAAA,CAAAlU,UAAA,qBAAAM,KAAA,MAAAiW,QAAA,GAAArC,GAAA,CAAAlU,UAAA,0CAAAuW,QAAA,CAAAjW,KAAA,EAEwD;UAUxB9D,EAAA,CAAAI,SAAA,GAEvB;UAFuBJ,EAAA,CAAAK,iBAAA,CAAAqX,GAAA,CAAArU,cAAA,CAAA2W,qBAAA,CAEvB;UAC8Eha,EAAA,CAAAI,SAAA,EAAkB;UAClGJ,EADgF,CAAAS,UAAA,mBAAkB,cAAAiX,GAAA,CAAAlU,UAAA,qBAAAM,KAAA,CAChD;UAI7B9D,EAAA,CAAAI,SAAA,EAAqD;UAArDJ,EAAA,CAAAS,UAAA,SAAAiX,GAAA,CAAAlU,UAAA,qBAAAM,KAAA,IAAA4T,GAAA,CAAApS,OAAA,CAAqD;;;qBDhJ1F1G,YAAY,EAAAqb,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,OAAA,EAAAF,GAAA,CAAAG,IAAA,EACZvb,mBAAmB,EAAAsX,EAAA,CAAAkE,aAAA,EAAAlE,EAAA,CAAAmE,oBAAA,EAAAnE,EAAA,CAAAoE,mBAAA,EAAApE,EAAA,CAAAqE,eAAA,EAAArE,EAAA,CAAAsE,oBAAA,EAAAtE,EAAA,CAAAuE,YAAA,EAAAvE,EAAA,CAAAwE,YAAA,EAAAxE,EAAA,CAAAyE,oBAAA,EAAAzE,EAAA,CAAA0E,kBAAA,EAAA1E,EAAA,CAAA2E,eAAA,EACnBhc,YAAY,EACZC,kBAAkB,EAClBK,cAAc,EACdE,oBAAoB,EACpBO,eAAe,EACfL,eAAe,EACfE,aAAa,EACbH,mBAAmB,EACnBK,eAAe,EACfH,iBAAiB;MAAAsb,MAAA;IAAA;;SAMR7W,uBAAuB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}