{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { HeaderComponent } from '@awe/play-comp-library';\nimport { NavigationEnd } from '@angular/router';\nimport { filter } from 'rxjs/operators';\nimport { Subscription } from 'rxjs';\nimport { DropdownComponent, IconComponent } from '@ava/play-comp-library';\nimport { ButtonComponent } from '@ava/play-comp-library';\nimport { SharedNavItemComponent } from '../nav-item/nav-item.component';\nimport { Validators } from '@angular/forms';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../auth/services/token-storage.service\";\nimport * as i3 from \"../../auth/services/auth.service\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/common\";\nconst _c0 = [\"orgPathTrigger\"];\nconst _c1 = [\"popover\"];\nconst _c2 = () => [];\nfunction SharedAppHeaderComponent_div_0_div_9_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 25);\n  }\n  if (rf & 2) {\n    const i_r3 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"active\", i_r3 === ctx_r1.currentLogoIndex);\n  }\n}\nfunction SharedAppHeaderComponent_div_0_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtemplate(1, SharedAppHeaderComponent_div_0_div_9_div_1_Template, 1, 2, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.studioLogos);\n  }\n}\nfunction SharedAppHeaderComponent_div_0_shared_nav_item_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"shared-nav-item\", 26);\n    i0.ɵɵlistener(\"toggleDropdownEvent\", function SharedAppHeaderComponent_div_0_shared_nav_item_15_Template_shared_nav_item_toggleDropdownEvent_0_listener() {\n      const i_r5 = i0.ɵɵrestoreView(_r4).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleDropdown(i_r5));\n    })(\"navigateEvent\", function SharedAppHeaderComponent_div_0_shared_nav_item_15_Template_shared_nav_item_navigateEvent_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.navigateTo($event));\n    })(\"selectEvent\", function SharedAppHeaderComponent_div_0_shared_nav_item_15_Template_shared_nav_item_selectEvent_0_listener() {\n      const i_r5 = i0.ɵɵrestoreView(_r4).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.selectMenuItem(i_r5));\n    })(\"dropdownItemSelected\", function SharedAppHeaderComponent_div_0_shared_nav_item_15_Template_shared_nav_item_dropdownItemSelected_0_listener($event) {\n      const i_r5 = i0.ɵɵrestoreView(_r4).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onDropdownItemSelected($event, i_r5));\n    })(\"dropdownPortalOpen\", function SharedAppHeaderComponent_div_0_shared_nav_item_15_Template_shared_nav_item_dropdownPortalOpen_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onDropdownPortalOpen($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"label\", item_r6.label)(\"route\", item_r6.route)(\"selected\", item_r6.selected)(\"hasDropdown\", item_r6.hasDropdown)(\"dropdownOpen\", item_r6.dropdownOpen || false)(\"dropdownItems\", item_r6.dropdownItems || i0.ɵɵpureFunction0(8, _c2))(\"icon\", item_r6.icon)(\"disabled\", item_r6.disabled || false);\n  }\n}\nfunction SharedAppHeaderComponent_div_0_div_17_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37, 1)(2, \"form\", 38)(3, \"div\", 39);\n    i0.ɵɵtext(4, \"Filter Configuration\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 40)(6, \"label\", 41);\n    i0.ɵɵtext(7, \"Choose Organization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"ava-dropdown\", 42);\n    i0.ɵɵlistener(\"selectionChange\", function SharedAppHeaderComponent_div_0_div_17_div_11_Template_ava_dropdown_selectionChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onOrgSelect($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"label\", 41);\n    i0.ɵɵtext(10, \"Choose Domain\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"ava-dropdown\", 42);\n    i0.ɵɵlistener(\"selectionChange\", function SharedAppHeaderComponent_div_0_div_17_div_11_Template_ava_dropdown_selectionChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onDomainSelect($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"label\", 41);\n    i0.ɵɵtext(13, \"Choose Project\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"ava-dropdown\", 42);\n    i0.ɵɵlistener(\"selectionChange\", function SharedAppHeaderComponent_div_0_div_17_div_11_Template_ava_dropdown_selectionChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onProjectSelect($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"label\", 41);\n    i0.ɵɵtext(16, \"Choose Team\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"ava-dropdown\", 42);\n    i0.ɵɵlistener(\"selectionChange\", function SharedAppHeaderComponent_div_0_div_17_div_11_Template_ava_dropdown_selectionChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onTeamSelect($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 43)(19, \"ava-button\", 44);\n    i0.ɵɵlistener(\"userClick\", function SharedAppHeaderComponent_div_0_div_17_div_11_Template_ava_button_userClick_19_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.closeOrgDialog());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"ava-button\", 45);\n    i0.ɵɵlistener(\"userClick\", function SharedAppHeaderComponent_div_0_div_17_div_11_Template_ava_button_userClick_20_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.saveOrgPathAndClose());\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.popoverAlign);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.headerConfigForm);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"dropdownTitle\", \"Select Organization\")(\"options\", ctx_r1.orgOptions)(\"selectedValue\", ctx_r1.selectedOrgName)(\"disabled\", false)(\"search\", true)(\"enableSearch\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"dropdownTitle\", \"Select Domain\")(\"options\", ctx_r1.domainOptions)(\"selectedValue\", ctx_r1.selectedDomainName)(\"disabled\", !ctx_r1.selectedOrg)(\"search\", true)(\"enableSearch\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"dropdownTitle\", \"Select Project\")(\"options\", ctx_r1.projectOptions)(\"selectedValue\", ctx_r1.selectedProjectName)(\"disabled\", !ctx_r1.selectedDomain)(\"search\", true)(\"enableSearch\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"dropdownTitle\", \"Select Team\")(\"options\", ctx_r1.teamOptions)(\"selectedValue\", ctx_r1.selectedTeamName)(\"disabled\", !ctx_r1.selectedProject)(\"search\", true)(\"enableSearch\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.headerConfigForm.valid);\n  }\n}\nfunction SharedAppHeaderComponent_div_0_div_17_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵlistener(\"click\", function SharedAppHeaderComponent_div_0_div_17_div_12_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.closeOrgDialog());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SharedAppHeaderComponent_div_0_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 28, 0);\n    i0.ɵɵlistener(\"click\", function SharedAppHeaderComponent_div_0_div_17_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleOrgDialog());\n    });\n    i0.ɵɵelementStart(3, \"span\", 29);\n    i0.ɵɵelement(4, \"img\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 31)(6, \"span\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"span\", 32);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(9, \"svg\", 33);\n    i0.ɵɵelement(10, \"path\", 34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(11, SharedAppHeaderComponent_div_0_div_17_div_11_Template, 21, 27, \"div\", 35)(12, SharedAppHeaderComponent_div_0_div_17_div_12_Template, 1, 0, \"div\", 36);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.orgLabel);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"open\", ctx_r1.isOrgDialogOpen);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isOrgDialogOpen);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isOrgDialogOpen);\n  }\n}\nfunction SharedAppHeaderComponent_div_0_div_18_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵlistener(\"click\", function SharedAppHeaderComponent_div_0_div_18_div_3_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.closeAppDrawer());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SharedAppHeaderComponent_div_0_div_18_div_7_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const app_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", app_r13.description, \" \");\n  }\n}\nfunction SharedAppHeaderComponent_div_0_div_18_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 56);\n    i0.ɵɵlistener(\"click\", function SharedAppHeaderComponent_div_0_div_18_div_7_Template_div_click_0_listener() {\n      const app_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.navigateToApp(app_r13));\n    });\n    i0.ɵɵelementStart(1, \"div\", 57);\n    i0.ɵɵelement(2, \"img\", 58);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 59)(4, \"div\", 60);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, SharedAppHeaderComponent_div_0_div_18_div_7_div_6_Template, 2, 1, \"div\", 61);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const app_r13 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", app_r13.icon, i0.ɵɵsanitizeUrl)(\"alt\", app_r13.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(app_r13.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", app_r13.description);\n  }\n}\nfunction SharedAppHeaderComponent_div_0_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"div\", 48);\n    i0.ɵɵlistener(\"click\", function SharedAppHeaderComponent_div_0_div_18_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleAppDrawer());\n    });\n    i0.ɵɵelement(2, \"ava-icon\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SharedAppHeaderComponent_div_0_div_18_div_3_Template, 1, 0, \"div\", 50);\n    i0.ɵɵelementStart(4, \"div\", 51)(5, \"div\", 52)(6, \"div\", 53);\n    i0.ɵɵtemplate(7, SharedAppHeaderComponent_div_0_div_18_div_7_Template, 7, 4, \"div\", 54);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isAppDrawerOpen);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isAppDrawerOpen);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"visible\", ctx_r1.isAppDrawerOpen);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getFilteredApps());\n  }\n}\nfunction SharedAppHeaderComponent_div_0_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵlistener(\"click\", function SharedAppHeaderComponent_div_0_div_19_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleTheme());\n    });\n    i0.ɵɵelement(1, \"img\", 64);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.themeMenuIcon, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction SharedAppHeaderComponent_div_0_div_20_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 85);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.userDesignation, \" \");\n  }\n}\nfunction SharedAppHeaderComponent_div_0_div_20_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.userEmail, \" \");\n  }\n}\nfunction SharedAppHeaderComponent_div_0_div_20_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 87)(1, \"div\", 88)(2, \"span\", 89);\n    i0.ɵɵtext(3, \"Mode\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 90)(5, \"button\", 91);\n    i0.ɵɵlistener(\"click\", function SharedAppHeaderComponent_div_0_div_20_div_14_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.currentTheme !== \"light\" && ctx_r1.toggleTheme());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(6, \"svg\", 81);\n    i0.ɵɵelement(7, \"circle\", 92)(8, \"path\", 93);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(9, \" Light \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(10, \"button\", 91);\n    i0.ɵɵlistener(\"click\", function SharedAppHeaderComponent_div_0_div_20_div_14_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.currentTheme !== \"dark\" && ctx_r1.toggleTheme());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(11, \"svg\", 81);\n    i0.ɵɵelement(12, \"path\", 94);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13, \" Dark \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"active\", ctx_r1.currentTheme === \"light\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"active\", ctx_r1.currentTheme === \"dark\");\n  }\n}\nfunction SharedAppHeaderComponent_div_0_div_20_div_15_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 97);\n    i0.ɵɵlistener(\"click\", function SharedAppHeaderComponent_div_0_div_20_div_15_button_5_Template_button_click_0_listener() {\n      const language_r18 = i0.ɵɵrestoreView(_r17).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.switchLanguage(language_r18.code));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const language_r18 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassProp(\"active\", ctx_r1.currentLanguage === language_r18.code);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", language_r18.name, \" \");\n  }\n}\nfunction SharedAppHeaderComponent_div_0_div_20_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 87)(1, \"div\", 88)(2, \"span\", 89);\n    i0.ɵɵtext(3, \"Language\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 95);\n    i0.ɵɵtemplate(5, SharedAppHeaderComponent_div_0_div_20_div_15_button_5_Template, 2, 3, \"button\", 96);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.config.availableLanguages);\n  }\n}\nfunction SharedAppHeaderComponent_div_0_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"div\", 66);\n    i0.ɵɵlistener(\"click\", function SharedAppHeaderComponent_div_0_div_20_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleProfileDropdown());\n    });\n    i0.ɵɵelement(2, \"img\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 68)(4, \"div\", 69)(5, \"div\", 70)(6, \"div\", 71);\n    i0.ɵɵelement(7, \"img\", 72);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 73)(9, \"div\", 74);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, SharedAppHeaderComponent_div_0_div_20_div_11_Template, 2, 1, \"div\", 75)(12, SharedAppHeaderComponent_div_0_div_20_div_12_Template, 2, 1, \"div\", 76);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(13, \"div\", 77);\n    i0.ɵɵtemplate(14, SharedAppHeaderComponent_div_0_div_20_div_14_Template, 14, 4, \"div\", 78)(15, SharedAppHeaderComponent_div_0_div_20_div_15_Template, 6, 1, \"div\", 78);\n    i0.ɵɵelement(16, \"div\", 77);\n    i0.ɵɵelementStart(17, \"div\", 79)(18, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function SharedAppHeaderComponent_div_0_div_20_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.logout());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(19, \"svg\", 81);\n    i0.ɵɵelement(20, \"path\", 82)(21, \"polyline\", 83)(22, \"line\", 84);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23, \" Sign Out \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r1.profileDropdownOpen);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.userAvatar, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"visible\", ctx_r1.profileDropdownOpen);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", ctx_r1.userAvatar, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.userName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.userDesignation);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.userEmail);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.config.showThemeToggleInProfile);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.config.showLanguageSwitcher);\n  }\n}\nfunction SharedAppHeaderComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 4)(2, \"defs\")(3, \"clipPath\", 5);\n    i0.ɵɵelement(4, \"path\", 6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"awe-header\", 7)(6, \"div\", 8)(7, \"div\", 9);\n    i0.ɵɵlistener(\"mouseenter\", function SharedAppHeaderComponent_div_0_Template_div_mouseenter_7_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.pauseLogoAnimation());\n    })(\"mouseleave\", function SharedAppHeaderComponent_div_0_Template_div_mouseleave_7_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.resumeLogoAnimation());\n    });\n    i0.ɵɵelement(8, \"img\", 10);\n    i0.ɵɵtemplate(9, SharedAppHeaderComponent_div_0_div_9_Template, 2, 1, \"div\", 11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 12)(11, \"div\", 13);\n    i0.ɵɵelement(12, \"div\", 14);\n    i0.ɵɵelementStart(13, \"div\", 15)(14, \"div\", 16);\n    i0.ɵɵtemplate(15, SharedAppHeaderComponent_div_0_shared_nav_item_15_Template, 1, 9, \"shared-nav-item\", 17);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(16, \"div\", 18);\n    i0.ɵɵtemplate(17, SharedAppHeaderComponent_div_0_div_17_Template, 13, 5, \"div\", 19)(18, SharedAppHeaderComponent_div_0_div_18_Template, 8, 6, \"div\", 20)(19, SharedAppHeaderComponent_div_0_div_19_Template, 2, 1, \"div\", 21)(20, SharedAppHeaderComponent_div_0_div_20_Template, 24, 11, \"div\", 22);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵattribute(\"data-studio\", ctx_r1.currentStudioName)(\"data-index\", ctx_r1.currentLogoIndex);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"logo-transitioning\", ctx_r1.isLogoAnimating);\n    i0.ɵɵproperty(\"src\", ctx_r1.currentLogo, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.currentStudioName + \" Logo\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.studioLogos.length > 1);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.config.navItems);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.config.showOrgSelector);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.config.showAppDrawer);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.config.showThemeToggle && !ctx_r1.config.showAppDrawer);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.config.showProfileDropdown);\n  }\n}\nfunction SharedAppHeaderComponent_div_1_div_2_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 106);\n  }\n  if (rf & 2) {\n    const item_r20 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", item_r20.icon, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction SharedAppHeaderComponent_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 101);\n    i0.ɵɵlistener(\"click\", function SharedAppHeaderComponent_div_1_div_2_Template_div_click_0_listener() {\n      const item_r20 = i0.ɵɵrestoreView(_r19).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onDropdownItemSelected({\n        route: item_r20.route,\n        label: item_r20.label\n      }, 0));\n    });\n    i0.ɵɵtemplate(1, SharedAppHeaderComponent_div_1_div_2_img_1_Template, 1, 1, \"img\", 102);\n    i0.ɵɵelementStart(2, \"div\", 103)(3, \"div\", 104);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 105);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r20 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r20.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r20.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r20.description);\n  }\n}\nfunction SharedAppHeaderComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98)(1, \"div\", 99);\n    i0.ɵɵtemplate(2, SharedAppHeaderComponent_div_1_div_2_Template, 7, 3, \"div\", 100);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"top\", ctx_r1.dropdownPortal.rect.bottom + 4 + \"px\")(\"left\", ctx_r1.dropdownPortal.rect.left + \"px\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.dropdownPortal.items);\n  }\n}\nexport let SharedAppHeaderComponent = /*#__PURE__*/(() => {\n  class SharedAppHeaderComponent {\n    router;\n    cdr;\n    renderer;\n    elementRef;\n    tokenStorage;\n    authService;\n    formBuilder;\n    config;\n    customNavItemComponent; // Allow custom nav item component\n    orgConfigService; // Allow injecting org config service from parent\n    themeService; // Allow injecting theme service from parent\n    // Header state\n    currentTheme = 'light';\n    profileDropdownOpen = false;\n    userName = '';\n    userEmail = '';\n    userDesignation = '';\n    userAvatar = '';\n    themeMenuIcon = '';\n    isLoginPage = false; // Added to track login pages\n    // Animated logo management\n    studioLogos = ['assets/svgs/ascendion-logo/header-ascendion-logo.svg', 'assets/svgs/ascendion-logo-light.svg', 'assets/svgs/ascendion-logo-dark.svg'];\n    studioNames = ['Console', 'Experience Studio', 'Product Studio'];\n    currentLogoIndex = 0;\n    currentLogo = '';\n    currentStudioName = '';\n    logoAnimationInterval;\n    isLogoAnimating = false;\n    // Language management\n    currentLanguage = 'en';\n    // Events for parent components to handle\n    navigationEvent = new EventEmitter();\n    dropdownItemSelected = new EventEmitter();\n    profileAction = new EventEmitter();\n    themeToggle = new EventEmitter();\n    languageChange = new EventEmitter();\n    orgConfigChange = new EventEmitter();\n    // Organization selector state (if enabled)\n    isOrgDialogOpen = false;\n    // App drawer state\n    isAppDrawerOpen = false;\n    // Organization dropdown state\n    orgOptions = [];\n    domainOptions = [];\n    projectOptions = [];\n    teamOptions = [];\n    // Selected values (IDs for form)\n    selectedOrg = '';\n    selectedDomain = '';\n    selectedProject = '';\n    selectedTeam = '';\n    // Selected names (for dropdown pre-selection)\n    selectedOrgName = '';\n    selectedDomainName = '';\n    selectedProjectName = '';\n    selectedTeamName = '';\n    // Form for organization config\n    headerConfigForm;\n    // Store the hierarchy data from API\n    hierarchyData = [];\n    orgPathTrigger;\n    popoverRef;\n    popoverAlign = 'left';\n    // Dropdown portal state\n    dropdownPortal = {\n      open: false,\n      rect: null,\n      items: [],\n      parentLabel: '',\n      navItemId: ''\n    };\n    // Subscription management\n    subscriptions = new Subscription();\n    constructor(router, cdr, renderer, elementRef, tokenStorage, authService, formBuilder) {\n      this.router = router;\n      this.cdr = cdr;\n      this.renderer = renderer;\n      this.elementRef = elementRef;\n      this.tokenStorage = tokenStorage;\n      this.authService = authService;\n      this.formBuilder = formBuilder;\n    }\n    ngOnInit() {\n      this.initializeHeader();\n      this.setupRouterSubscription();\n      this.setupThemeSubscription();\n      this.loadUserInfo();\n      this.initializeForm();\n      this.initializeLogo();\n      // Initialize organization selector if enabled\n      if (this.config.showOrgSelector) {\n        this.initOrgPathFromCookie();\n      }\n    }\n    ngAfterViewInit() {\n      this.updateActiveMenuItemByRoute(this.router.url);\n    }\n    ngOnDestroy() {\n      this.subscriptions.unsubscribe();\n      if (this.logoAnimationInterval) {\n        clearInterval(this.logoAnimationInterval);\n      }\n    }\n    // ========================================\n    // LOGO ANIMATION METHODS\n    // ========================================\n    initializeLogo() {\n      // Use config logos if provided, otherwise use defaults\n      if (this.config.studioLogos && this.config.studioLogos.length > 0) {\n        this.studioLogos = this.config.studioLogos;\n        console.log('🎨 Using custom studio logos:', this.studioLogos);\n      } else {\n        console.log('🎨 Using default studio logos:', this.studioLogos);\n      }\n      if (this.config.studioNames && this.config.studioNames.length > 0) {\n        this.studioNames = this.config.studioNames;\n      }\n      // Set initial logo - use animated logos when animation is enabled\n      if (this.config.enableLogoAnimation !== false && this.studioLogos.length > 0) {\n        // Use first logo from animated array\n        this.currentLogo = this.studioLogos[0];\n        this.currentStudioName = this.studioNames[0];\n        console.log('🎨 Initial animated logo set to:', this.currentLogo);\n        console.log('🎨 Initial studio name set to:', this.currentStudioName);\n      } else {\n        // Fallback to config logoSrc when animation is disabled\n        this.currentLogo = this.config.logoSrc;\n        this.currentStudioName = this.config.projectName || 'Studio';\n        console.log('🎨 Using static logo:', this.currentLogo);\n      }\n      // Start animation cycle only if enabled (default: true)\n      if (this.config.enableLogoAnimation !== false) {\n        this.startLogoAnimation();\n      }\n    }\n    startLogoAnimation() {\n      // Clear any existing interval\n      if (this.logoAnimationInterval) {\n        clearInterval(this.logoAnimationInterval);\n      }\n      // Use config interval or default to 3 seconds\n      const interval = this.config.logoAnimationInterval || 3000;\n      // Start the animation cycle\n      this.logoAnimationInterval = setInterval(() => {\n        this.animateToNextLogo();\n      }, interval);\n    }\n    animateToNextLogo() {\n      if (this.isLogoAnimating) return;\n      this.isLogoAnimating = true;\n      // Get animation style from config (default: 'rotate')\n      const animationStyle = this.config.logoAnimationStyle || 'rotate';\n      // Add rotation animation class\n      const logoElement = document.querySelector('.animated-logo');\n      if (logoElement) {\n        logoElement.classList.add('logo-transitioning');\n        // Apply specific animation style\n        logoElement.classList.add(`logo-${animationStyle}`);\n      }\n      // Determine timing based on animation style\n      const timings = {\n        rotate: {\n          changeAt: 400,\n          duration: 800\n        },\n        'fade-rotate': {\n          changeAt: 300,\n          duration: 600\n        },\n        flip: {\n          changeAt: 200,\n          duration: 400\n        }\n      };\n      const timing = timings[animationStyle] || timings['rotate'];\n      // Change the image source at the optimal point in the animation\n      setTimeout(() => {\n        // Move to next logo index\n        this.currentLogoIndex = (this.currentLogoIndex + 1) % this.studioLogos.length;\n        this.currentLogo = this.studioLogos[this.currentLogoIndex];\n        this.currentStudioName = this.studioNames[this.currentLogoIndex];\n        // Trigger change detection\n        this.cdr.detectChanges();\n      }, timing.changeAt);\n      // Remove animation classes after animation completes\n      setTimeout(() => {\n        if (logoElement) {\n          logoElement.classList.remove('logo-transitioning');\n          logoElement.classList.remove(`logo-${animationStyle}`);\n        }\n        this.isLogoAnimating = false;\n      }, timing.duration);\n    }\n    pauseLogoAnimation() {\n      if (this.logoAnimationInterval) {\n        clearInterval(this.logoAnimationInterval);\n        this.logoAnimationInterval = undefined;\n      }\n    }\n    resumeLogoAnimation() {\n      if (!this.logoAnimationInterval) {\n        this.startLogoAnimation();\n      }\n    }\n    initializeHeader() {\n      if (!this.config) {\n        console.warn('SharedAppHeaderComponent: config is required');\n        return;\n      }\n      // Set default values\n      this.config.showOrgSelector = this.config.showOrgSelector ?? false;\n      this.config.showThemeToggle = this.config.showThemeToggle ?? true;\n      this.config.showAppDrawer = this.config.showAppDrawer ?? false;\n      this.config.showProfileDropdown = this.config.showProfileDropdown ?? true;\n      this.config.projectName = this.config.projectName ?? 'Application';\n      this.config.redirectUrl = this.config.redirectUrl ?? '/';\n      this.config.currentApp = this.config.currentApp ?? '';\n      // Default studio apps\n      this.config.availableApps = this.config.availableApps ?? [{\n        name: 'Console',\n        route: '/console',\n        icon: 'assets/svgs/studios/console-icon.svg',\n        description: 'Agent & Workflow Management'\n      }, {\n        name: 'Experience Studio',\n        route: '/experience-studio',\n        icon: 'assets/svgs/studios/experience-studio-icon.svg',\n        description: 'UI/UX Design & Prototyping'\n      }, {\n        name: 'Product Studio',\n        route: '/product-studio',\n        icon: 'assets/svgs/studios/product-studio-icon.svg',\n        description: 'Product Development'\n      }, {\n        name: 'Launchpad',\n        route: '/launchpad',\n        icon: 'assets/svgs/studios/launchpad-icon.svg',\n        description: 'Project Launch Hub'\n      }];\n      // Profile dropdown defaults\n      this.config.showThemeToggleInProfile = this.config.showThemeToggleInProfile ?? true;\n      this.config.showLanguageSwitcher = this.config.showLanguageSwitcher ?? false;\n      this.config.availableLanguages = this.config.availableLanguages ?? [{\n        code: 'en',\n        name: 'English'\n      }, {\n        code: 'fil',\n        name: 'Filipino'\n      }, {\n        code: 'es',\n        name: 'Español'\n      }];\n    }\n    initializeForm() {\n      this.headerConfigForm = this.formBuilder.group({\n        org: ['', Validators.required],\n        domain: ['', Validators.required],\n        project: ['', Validators.required],\n        team: ['', Validators.required]\n      });\n    }\n    setupRouterSubscription() {\n      const routerSub = this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(event => {\n        // Check if current route is a login page\n        this.isLoginPage = this.checkIfLoginPage(event.url);\n        this.updateActiveMenuItemByRoute(event.url);\n      });\n      this.subscriptions.add(routerSub);\n      // Check initial route\n      this.isLoginPage = this.checkIfLoginPage(this.router.url);\n    }\n    checkIfLoginPage(url) {\n      // Check for login routes across different studios\n      const loginRoutes = ['/login', '/auth/login', '/experience/login', '/product/login', '/console/login'];\n      return loginRoutes.some(route => url.includes(route));\n    }\n    setupThemeSubscription() {\n      // If theme service is provided, subscribe to theme changes\n      if (this.themeService && this.themeService.themeObservable) {\n        this.subscriptions.add(this.themeService.themeObservable.subscribe(theme => {\n          this.currentTheme = theme;\n          this.updateThemeAssets();\n          this.cdr.markForCheck();\n        }));\n      }\n      this.updateThemeAssets();\n    }\n    loadUserInfo() {\n      this.userName = this.tokenStorage.getDaUsername() || 'User';\n      this.userEmail = this.tokenStorage.getDaUserEmail?.() || ''; // Safe call if method exists\n      this.userDesignation = this.tokenStorage.getDaUserDesignation?.() || 'Employee'; // Safe call if method exists\n      this.generateUserAvatar();\n    }\n    generateUserAvatar() {\n      // Generate avatar from user initials if no profile picture is available\n      if (this.userName) {\n        const nameParts = this.userName.trim().split(' ');\n        let initials = '';\n        if (nameParts.length >= 2) {\n          // First letter of first name and first letter of last name\n          initials = nameParts[0][0] + nameParts[nameParts.length - 1][0];\n        } else if (nameParts.length === 1) {\n          // Just first letter if only one name\n          initials = nameParts[0][0];\n        } else {\n          initials = 'U'; // Default to 'U' for User\n        }\n        initials = initials.toUpperCase();\n        // Generate a colored avatar with initials\n        const colors = ['#8B5CF6', '#06B6D4', '#10B981', '#F59E0B', '#EF4444', '#8B5A2B', '#6366F1', '#EC4899'];\n        const colorIndex = this.userName.length % colors.length;\n        const backgroundColor = colors[colorIndex];\n        this.userAvatar = `data:image/svg+xml;base64,${btoa(`\n        <svg width=\"40\" height=\"40\" xmlns=\"http://www.w3.org/2000/svg\">\n          <circle cx=\"20\" cy=\"20\" r=\"20\" fill=\"${backgroundColor}\"/>\n          <text x=\"20\" y=\"26\" font-family=\"Inter, Arial, sans-serif\" font-size=\"14\" font-weight=\"600\" fill=\"white\" text-anchor=\"middle\">${initials}</text>\n        </svg>\n      `)}`;\n      }\n    }\n    updateThemeAssets() {\n      // Update theme-specific assets\n      this.themeMenuIcon = this.currentTheme === 'light' ? 'assets/svgs/header/toggle-theme/toggle-to-dark.svg' : 'assets/svgs/header/toggle-theme/toggle-to-light.svg';\n    }\n    // Organization selector methods\n    initOrgPathFromCookie() {\n      const path = this.tokenStorage.getCookie('org_path');\n      if (path) {\n        const parts = path.split('::');\n        const usecasePath = parts[0] || '';\n        const usecaseIdPath = parts[1] || '';\n        // Parse the IDs\n        const ids = usecaseIdPath.split('@').map(Number);\n        // Set form values (IDs)\n        this.headerConfigForm.patchValue({\n          org: ids[0]?.toString() || '',\n          domain: ids[1]?.toString() || '',\n          project: ids[2]?.toString() || '',\n          team: ids[3]?.toString() || ''\n        });\n        // Store the IDs for form and the names for dropdown pre-selection\n        this.selectedOrg = ids[0]?.toString() || '';\n        this.selectedDomain = ids[1]?.toString() || '';\n        this.selectedProject = ids[2]?.toString() || '';\n        this.selectedTeam = ids[3]?.toString() || '';\n        // Store the names for dropdown pre-selection\n        const pathParts = usecasePath.split('@');\n        this.selectedOrgName = pathParts[0] || '';\n        this.selectedDomainName = pathParts[1] || '';\n        this.selectedProjectName = pathParts[2] || '';\n        this.selectedTeamName = pathParts[3] || '';\n        // Load dropdown options\n        this.loadData();\n      } else {\n        this.loadData();\n      }\n    }\n    loadData() {\n      if (this.orgConfigService) {\n        this.orgConfigService.getOrganizationHierarchy().subscribe({\n          next: data => {\n            this.hierarchyData = data;\n            this.loadOrganizations();\n            // After loading organizations, load cascading dropdowns if we have pre-selected values\n            if (this.selectedOrg) {\n              this.loadDomains(this.selectedOrg);\n              if (this.selectedDomain) {\n                this.loadProjects(this.selectedDomain);\n                if (this.selectedProject) {\n                  this.loadTeams(this.selectedProject);\n                }\n              }\n            }\n          },\n          error: error => {\n            console.error('Error loading organization data:', error);\n          }\n        });\n      }\n    }\n    loadOrganizations() {\n      this.orgOptions = this.hierarchyData.map(org => ({\n        name: org.organizationName,\n        value: org.orgId.toString()\n      }));\n    }\n    onOrgSelect(event) {\n      const selectedOrgId = event.selectedOptions?.[0]?.value;\n      const selectedOrgName = event.selectedOptions?.[0]?.name;\n      if (selectedOrgId) {\n        this.selectedOrg = selectedOrgId;\n        this.selectedOrgName = selectedOrgName;\n        this.headerConfigForm.patchValue({\n          org: selectedOrgId\n        });\n        this.loadDomains(selectedOrgId);\n        // Clear dependent dropdowns\n        this.headerConfigForm.patchValue({\n          domain: '',\n          project: '',\n          team: ''\n        });\n        this.selectedDomain = '';\n        this.selectedProject = '';\n        this.selectedTeam = '';\n        this.selectedDomainName = '';\n        this.selectedProjectName = '';\n        this.selectedTeamName = '';\n        this.projectOptions = [];\n        this.teamOptions = [];\n      }\n    }\n    loadDomains(orgId) {\n      const org = this.hierarchyData.find(o => o.orgId.toString() === orgId);\n      if (org) {\n        this.domainOptions = org.domains.map(domain => ({\n          name: domain.domainName,\n          value: domain.domainId.toString()\n        }));\n      } else {\n        this.domainOptions = [];\n      }\n    }\n    onDomainSelect(event) {\n      const selectedDomainId = event.selectedOptions?.[0]?.value;\n      const selectedDomainName = event.selectedOptions?.[0]?.name;\n      if (selectedDomainId) {\n        this.selectedDomain = selectedDomainId;\n        this.selectedDomainName = selectedDomainName;\n        this.headerConfigForm.patchValue({\n          domain: selectedDomainId\n        });\n        this.loadProjects(selectedDomainId);\n        // Clear dependent dropdowns\n        this.headerConfigForm.patchValue({\n          project: '',\n          team: ''\n        });\n        this.selectedProject = '';\n        this.selectedTeam = '';\n        this.selectedProjectName = '';\n        this.selectedTeamName = '';\n        this.teamOptions = [];\n      }\n    }\n    loadProjects(domainId) {\n      const org = this.hierarchyData.find(o => o.domains.some(d => d.domainId.toString() === domainId));\n      if (org) {\n        const domain = org.domains.find(d => d.domainId.toString() === domainId);\n        if (domain) {\n          this.projectOptions = domain.projects.map(project => ({\n            name: project.projectName,\n            value: project.projectId.toString()\n          }));\n        } else {\n          this.projectOptions = [];\n        }\n      } else {\n        this.projectOptions = [];\n      }\n    }\n    onProjectSelect(event) {\n      const selectedProjectId = event.selectedOptions?.[0]?.value;\n      const selectedProjectName = event.selectedOptions?.[0]?.name;\n      if (selectedProjectId) {\n        this.selectedProject = selectedProjectId;\n        this.selectedProjectName = selectedProjectName;\n        this.headerConfigForm.patchValue({\n          project: selectedProjectId\n        });\n        this.loadTeams(selectedProjectId);\n        // Clear dependent dropdowns\n        this.headerConfigForm.patchValue({\n          team: ''\n        });\n        this.selectedTeam = '';\n        this.selectedTeamName = '';\n      }\n    }\n    loadTeams(projectId) {\n      const org = this.hierarchyData.find(o => o.domains.some(d => d.projects.some(p => p.projectId.toString() === projectId)));\n      if (org) {\n        const domain = org.domains.find(d => d.projects.some(p => p.projectId.toString() === projectId));\n        if (domain) {\n          const project = domain.projects.find(p => p.projectId.toString() === projectId);\n          if (project) {\n            this.teamOptions = project.teams.map(team => ({\n              name: team.teamName,\n              value: team.teamId.toString()\n            }));\n          } else {\n            this.teamOptions = [];\n          }\n        } else {\n          this.teamOptions = [];\n        }\n      } else {\n        this.teamOptions = [];\n      }\n    }\n    onTeamSelect(event) {\n      const selectedTeamId = event.selectedOptions?.[0]?.value;\n      const selectedTeamName = event.selectedOptions?.[0]?.name;\n      if (selectedTeamId) {\n        this.selectedTeam = selectedTeamId;\n        this.selectedTeamName = selectedTeamName;\n        this.headerConfigForm.patchValue({\n          team: selectedTeamId\n        });\n      }\n    }\n    get orgLabel() {\n      // Try to get the org name from the org_path cookie\n      const orgPath = this.tokenStorage.getCookie('org_path');\n      if (orgPath) {\n        const orgName = orgPath.split('::')[0].split('@')[0];\n        if (orgName) return orgName;\n      }\n      // Fallback to dropdown label\n      return this.orgOptions.find(o => o.value === this.selectedOrg)?.name || 'Select Organization';\n    }\n    saveOrgPathAndClose() {\n      if (this.headerConfigForm.valid) {\n        const formValue = this.headerConfigForm.value;\n        // Build the org path string\n        const orgPath = `${this.selectedOrgName}@${this.selectedDomainName}@${this.selectedProjectName}@${this.selectedTeamName}::${formValue.org}@${formValue.domain}@${formValue.project}@${formValue.team}`;\n        // Save to cookie\n        this.tokenStorage.setCookie('org_path', orgPath);\n        // Emit the change event\n        this.orgConfigChange.emit({\n          orgPath,\n          selectedValues: {\n            org: this.selectedOrg,\n            domain: this.selectedDomain,\n            project: this.selectedProject,\n            team: this.selectedTeam\n          },\n          selectedNames: {\n            org: this.selectedOrgName,\n            domain: this.selectedDomainName,\n            project: this.selectedProjectName,\n            team: this.selectedTeamName\n          }\n        });\n        this.closeOrgDialog();\n      }\n    }\n    // Navigation methods\n    navigateTo(route) {\n      this.router.navigate([route]);\n      this.navigationEvent.emit(route);\n    }\n    selectMenuItem(index) {\n      this.config.navItems.forEach((item, i) => {\n        item.selected = i === index;\n      });\n    }\n    toggleDropdown(index) {\n      this.config.navItems[index].dropdownOpen = !this.config.navItems[index].dropdownOpen;\n    }\n    onDropdownItemSelected(event, parentIndex) {\n      this.navigateTo(event.route);\n      this.dropdownItemSelected.emit(event);\n      this.selectMenuItem(parentIndex);\n      this.closeDropdownPortal();\n    }\n    onDropdownPortalOpen(event) {\n      this.dropdownPortal = {\n        open: true,\n        rect: event.rect,\n        items: event.items,\n        parentLabel: event.parentLabel,\n        navItemId: event.navItemId\n      };\n    }\n    closeDropdownPortal() {\n      this.dropdownPortal.open = false;\n      this.config.navItems.forEach(item => item.dropdownOpen = false);\n    }\n    // Profile dropdown methods\n    toggleProfileDropdown() {\n      this.profileDropdownOpen = !this.profileDropdownOpen;\n    }\n    logout() {\n      this.authService.logout();\n      this.profileAction.emit('logout');\n    }\n    // Theme methods\n    toggleTheme() {\n      if (this.themeService && this.themeService.toggleTheme) {\n        // Use the injected theme service if available\n        this.themeService.toggleTheme();\n      } else {\n        // Fallback to local theme toggle\n        const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';\n        this.currentTheme = newTheme;\n        this.updateThemeAssets();\n        this.themeToggle.emit(newTheme);\n      }\n    }\n    // Language methods\n    switchLanguage(languageCode) {\n      this.currentLanguage = languageCode;\n      this.languageChange.emit(languageCode);\n    }\n    getCurrentLanguageName() {\n      const language = this.config.availableLanguages?.find(lang => lang.code === this.currentLanguage);\n      return language?.name || 'English';\n    }\n    // App drawer methods\n    toggleAppDrawer() {\n      this.isAppDrawerOpen = !this.isAppDrawerOpen;\n      if (this.isAppDrawerOpen) {\n        // Calculate position to prevent overflow\n        setTimeout(() => {\n          this.calculateAppDrawerPosition();\n        });\n      }\n    }\n    closeAppDrawer() {\n      this.isAppDrawerOpen = false;\n    }\n    navigateToApp(app) {\n      if (app.route.startsWith('http')) {\n        // External URL\n        window.open(app.route, '_blank');\n      } else {\n        // Internal route\n        this.router.navigate([app.route]);\n      }\n      this.closeAppDrawer();\n      this.navigationEvent.emit(app.route);\n    }\n    getFilteredApps() {\n      if (!this.config.availableApps) return [];\n      // Filter out the current app\n      return this.config.availableApps.filter(app => app.name !== this.config.currentApp);\n    }\n    // Organization selector methods (if enabled)\n    toggleOrgDialog() {\n      if (this.config.showOrgSelector) {\n        this.isOrgDialogOpen = !this.isOrgDialogOpen;\n        if (this.isOrgDialogOpen) {\n          // Calculate popover position\n          setTimeout(() => {\n            this.calculatePopoverPosition();\n          });\n        }\n      }\n    }\n    closeOrgDialog() {\n      this.isOrgDialogOpen = false;\n    }\n    calculatePopoverPosition() {\n      if (this.orgPathTrigger && this.popoverRef) {\n        const triggerRect = this.orgPathTrigger.nativeElement.getBoundingClientRect();\n        const popoverRect = this.popoverRef.nativeElement.getBoundingClientRect();\n        const viewportWidth = window.innerWidth;\n        // Check if popover would overflow on the right\n        if (triggerRect.left + popoverRect.width > viewportWidth - 20) {\n          this.popoverAlign = 'right';\n        } else {\n          this.popoverAlign = 'left';\n        }\n      }\n    }\n    calculateAppDrawerPosition() {\n      // Find the app drawer dropdown element\n      const appDrawerDropdown = this.elementRef.nativeElement.querySelector('.app-drawer-dropdown');\n      if (appDrawerDropdown) {\n        const dropdownRect = appDrawerDropdown.getBoundingClientRect();\n        const viewportWidth = window.innerWidth;\n        // If dropdown would overflow on the right, adjust position\n        if (dropdownRect.right > viewportWidth - 20) {\n          const overflow = dropdownRect.right - viewportWidth + 20;\n          appDrawerDropdown.style.right = `${overflow}px`;\n        } else {\n          appDrawerDropdown.style.right = '0px';\n        }\n      }\n    }\n    // Navigation item click handler\n    onNavItemClick(item, index, event) {\n      if (item.disabled) {\n        event.preventDefault();\n        event.stopPropagation();\n        return;\n      }\n      if (item.hasDropdown && item.dropdownItems) {\n        event.stopPropagation();\n        const rect = event.currentTarget.getBoundingClientRect();\n        this.onDropdownPortalOpen({\n          rect,\n          items: item.dropdownItems,\n          parentLabel: item.label,\n          navItemId: item.label\n        });\n        this.toggleDropdown(index);\n      } else {\n        this.navigateTo(item.route);\n        this.selectMenuItem(index);\n      }\n    }\n    // Route-based active menu item update\n    updateActiveMenuItemByRoute(url) {\n      // Reset all selections\n      this.config.navItems.forEach(item => {\n        item.selected = false;\n      });\n      // Find the matching parent route or parent of a child route\n      const parentItem = this.config.navItems.find(item => {\n        // Check if this is a direct match for the parent route\n        if (url === item.route) {\n          return true;\n        }\n        // Check if this is a dropdown parent with a matching child\n        if (item.hasDropdown && item.dropdownItems) {\n          // Check if the URL starts with the parent route path (for nested routes)\n          // OR if any child route exactly matches the URL\n          return url.startsWith(item.route + '/') || item.dropdownItems.some(child => url === child.route);\n        }\n        // Even if hasDropdown is false, check for dropdownItems\n        if (!item.hasDropdown && item.dropdownItems) {\n          return item.dropdownItems.some(child => url === child.route);\n        }\n        return false;\n      });\n      if (parentItem) {\n        parentItem.selected = true;\n      } else {\n        // Default to first non-disabled item if no match found\n        const defaultItem = this.config.navItems.find(item => !item.disabled);\n        if (defaultItem) {\n          defaultItem.selected = true;\n        }\n      }\n    }\n    // Document click listener to close dropdowns\n    onDocumentClick(event) {\n      const target = event.target;\n      // Close profile dropdown if clicking outside\n      if (this.profileDropdownOpen && !this.elementRef.nativeElement.contains(target)) {\n        this.profileDropdownOpen = false;\n      }\n      // Close org dialog if clicking outside\n      if (this.isOrgDialogOpen && !target.closest('.org-path-dropdown-container')) {\n        this.closeOrgDialog();\n      }\n      // Close app drawer if clicking outside\n      if (this.isAppDrawerOpen && !target.closest('.app-drawer-container')) {\n        this.closeAppDrawer();\n      }\n      // Close dropdown portal if clicking outside\n      if (this.dropdownPortal.open && !target.closest('.dropdown-portal-menu') && !target.closest('.nav-item-wrapper')) {\n        this.closeDropdownPortal();\n      }\n    }\n    static ɵfac = function SharedAppHeaderComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SharedAppHeaderComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.TokenStorageService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.FormBuilder));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SharedAppHeaderComponent,\n      selectors: [[\"shared-app-header\"]],\n      viewQuery: function SharedAppHeaderComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.orgPathTrigger = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.popoverRef = _t.first);\n        }\n      },\n      hostBindings: function SharedAppHeaderComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function SharedAppHeaderComponent_click_HostBindingHandler($event) {\n            return ctx.onDocumentClick($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      inputs: {\n        config: \"config\",\n        customNavItemComponent: \"customNavItemComponent\",\n        orgConfigService: \"orgConfigService\",\n        themeService: \"themeService\"\n      },\n      outputs: {\n        navigationEvent: \"navigationEvent\",\n        dropdownItemSelected: \"dropdownItemSelected\",\n        profileAction: \"profileAction\",\n        themeToggle: \"themeToggle\",\n        languageChange: \"languageChange\",\n        orgConfigChange: \"orgConfigChange\"\n      },\n      decls: 2,\n      vars: 2,\n      consts: [[\"orgPathTrigger\", \"\"], [\"popover\", \"\"], [4, \"ngIf\"], [\"class\", \"dropdown-portal-menu\", 3, \"top\", \"left\", 4, \"ngIf\"], [\"width\", \"0\", \"height\", \"0\", 2, \"position\", \"absolute\"], [\"id\", \"headerClip\", \"clipPathUnits\", \"objectBoundingBox\"], [\"d\", \"\\n          M 0.03,0 \\n          L 0.97,0 \\n          L 0.95,0.71 \\n          Q 0.939,1    0.91,1 \\n          L 0.09,1 \\n          Q 0.061,1    0.05,0.69 \\n          Z\"], [\"theme\", \"light\"], [\"left-content\", \"\"], [1, \"animated-logo-container\", 3, \"mouseenter\", \"mouseleave\"], [1, \"header-logo\", \"animated-logo\", 3, \"src\", \"alt\"], [\"class\", \"studio-indicators\", 4, \"ngIf\"], [\"center-content\", \"\"], [1, \"header-wrapper\"], [1, \"header-shadow\"], [1, \"nav-menu\"], [1, \"nav-items\"], [\"class\", \"nav-item-wrapper\", 3, \"label\", \"route\", \"selected\", \"hasDropdown\", \"dropdownOpen\", \"dropdownItems\", \"icon\", \"disabled\", \"toggleDropdownEvent\", \"navigateEvent\", \"selectEvent\", \"dropdownItemSelected\", \"dropdownPortalOpen\", 4, \"ngFor\", \"ngForOf\"], [\"right-content\", \"\", 1, \"user-info-container\"], [\"class\", \"org-path-dropdown-container\", 4, \"ngIf\"], [\"class\", \"app-drawer-container\", 4, \"ngIf\"], [\"class\", \"theme-toggle\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"profile-container\", 4, \"ngIf\"], [1, \"studio-indicators\"], [\"class\", \"studio-dot\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"], [1, \"studio-dot\"], [1, \"nav-item-wrapper\", 3, \"toggleDropdownEvent\", \"navigateEvent\", \"selectEvent\", \"dropdownItemSelected\", \"dropdownPortalOpen\", \"label\", \"route\", \"selected\", \"hasDropdown\", \"dropdownOpen\", \"dropdownItems\", \"icon\", \"disabled\"], [1, \"org-path-dropdown-container\"], [1, \"org-path-trigger\", 3, \"click\"], [1, \"org-icon\"], [\"src\", \"assets/svgs/ascendion-logo/header-ascendion-logo.svg\", \"alt\", \"Organization Logo\", \"width\", \"40\", \"height\", \"40\"], [1, \"org-label-text\"], [1, \"org-dropdown-arrow\"], [\"width\", \"16\", \"height\", \"16\", \"viewBox\", \"0 0 12 12\", \"fill\", \"none\"], [\"d\", \"M2.5 4L6 7.5L9.5 4\", \"stroke\", \"currentColor\", \"stroke-width\", \"1.5\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"class\", \"org-path-popover\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"org-path-backdrop\", 3, \"click\", 4, \"ngIf\"], [1, \"org-path-popover\", 3, \"ngClass\"], [3, \"formGroup\"], [1, \"filter-config-title\"], [1, \"dropdown-row-vertical\"], [1, \"filter-label\", \"required\"], [3, \"selectionChange\", \"dropdownTitle\", \"options\", \"selectedValue\", \"disabled\", \"search\", \"enableSearch\"], [1, \"popover-actions\"], [\"label\", \"Cancel\", \"variant\", \"secondary\", \"size\", \"medium\", 3, \"userClick\"], [\"label\", \"Apply\", \"variant\", \"primary\", \"size\", \"medium\", 3, \"userClick\", \"disabled\"], [1, \"org-path-backdrop\", 3, \"click\"], [1, \"app-drawer-container\"], [1, \"app-drawer-trigger\", 3, \"click\"], [\"iconName\", \"layout-grid\"], [\"class\", \"app-drawer-backdrop\", 3, \"click\", 4, \"ngIf\"], [1, \"app-drawer-dropdown\"], [1, \"app-drawer-content\"], [1, \"app-drawer-grid\"], [\"class\", \"app-drawer-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"app-drawer-backdrop\", 3, \"click\"], [1, \"app-drawer-item\", 3, \"click\"], [1, \"app-icon\"], [3, \"src\", \"alt\"], [1, \"app-info\"], [1, \"app-name\"], [\"class\", \"app-description\", 4, \"ngIf\"], [1, \"app-description\"], [1, \"theme-toggle\", 3, \"click\"], [\"alt\", \"Toggle Theme\", \"width\", \"24\", \"height\", \"24\", 3, \"src\"], [1, \"profile-container\"], [1, \"profile-trigger\", 3, \"click\"], [\"alt\", \"User Profile\", 1, \"profile-avatar\", 3, \"src\"], [1, \"profile-dropdown\"], [1, \"profile-dropdown-content\"], [1, \"profile-user-info\"], [1, \"profile-avatar-large\"], [\"alt\", \"User Profile\", 3, \"src\"], [1, \"profile-details\"], [1, \"profile-name\"], [\"class\", \"profile-designation\", 4, \"ngIf\"], [\"class\", \"profile-email\", 4, \"ngIf\"], [1, \"profile-divider\"], [\"class\", \"profile-section\", 4, \"ngIf\"], [1, \"profile-actions\"], [1, \"profile-action-item\", \"logout-btn\", 3, \"click\"], [\"width\", \"16\", \"height\", \"16\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\"], [\"d\", \"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4\"], [\"points\", \"16,17 21,12 16,7\"], [\"x1\", \"21\", \"y1\", \"12\", \"x2\", \"9\", \"y2\", \"12\"], [1, \"profile-designation\"], [1, \"profile-email\"], [1, \"profile-section\"], [1, \"profile-section-header\"], [1, \"section-title\"], [1, \"theme-toggle-container\"], [1, \"theme-option\", 3, \"click\"], [\"cx\", \"12\", \"cy\", \"12\", \"r\", \"5\"], [\"d\", \"M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42\"], [\"d\", \"M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z\"], [1, \"language-options\"], [\"class\", \"language-option\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"language-option\", 3, \"click\"], [1, \"dropdown-portal-menu\"], [1, \"dropdown-menu\"], [\"class\", \"dropdown-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"dropdown-item\", 3, \"click\"], [\"alt\", \"\", \"class\", \"dropdown-icon\", 3, \"src\", 4, \"ngIf\"], [1, \"dropdown-content\"], [1, \"dropdown-label\"], [1, \"dropdown-description\"], [\"alt\", \"\", 1, \"dropdown-icon\", 3, \"src\"]],\n      template: function SharedAppHeaderComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, SharedAppHeaderComponent_div_0_Template, 21, 12, \"div\", 2)(1, SharedAppHeaderComponent_div_1_Template, 3, 5, \"div\", 3);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoginPage);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.dropdownPortal.open && ctx.dropdownPortal.rect);\n        }\n      },\n      dependencies: [HeaderComponent, CommonModule, i5.NgClass, i5.NgForOf, i5.NgIf, SharedNavItemComponent, DropdownComponent, ButtonComponent, ReactiveFormsModule, i4.ɵNgNoValidate, i4.NgControlStatusGroup, i4.FormGroupDirective, IconComponent],\n      styles: [\"[_nghost-%COMP%] {\\n  --header-bg: #ffffff;\\n  --header-text: #374151;\\n  --header-border: rgba(0, 0, 0, 0.08);\\n  --nav-bg: #ffffff;\\n  --nav-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  --nav-text: #000000;\\n  --nav-arrow: #222222;\\n  --dropdown-bg: #ffffff;\\n  --dropdown-text: #374151;\\n  --dropdown-border: #e5e7eb;\\n  --dropdown-hover: #f3f4f6;\\n  display: block;\\n  padding: 1.5rem 0 0 0;\\n  margin: 0;\\n  width: 100%;\\n  position: relative;\\n}\\n.theme-dark[_nghost-%COMP%], .dark-theme   [_nghost-%COMP%] {\\n  --header-bg: #1f2937;\\n  --header-text: #f9fafb;\\n  --header-border: rgba(255, 255, 255, 0.1);\\n  --nav-bg: #374151;\\n  --nav-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);\\n  --nav-text: #ffffff;\\n  --nav-arrow: #d1d5db;\\n  --dropdown-bg: #374151;\\n  --dropdown-text: #f9fafb;\\n  --dropdown-border: #4b5563;\\n  --dropdown-hover: #4b5563;\\n}\\n@media (min-width: 1200px) {\\n  [_nghost-%COMP%] {\\n    padding-top: 0;\\n  }\\n}\\n@media (min-width: 1400px) {\\n  [_nghost-%COMP%] {\\n    padding-top: 0;\\n  }\\n}\\n\\n  .outer-box.light {\\n  background-color: transparent !important;\\n  box-shadow: none !important;\\n  margin: 0 !important;\\n  min-height: auto !important;\\n  width: 100% !important;\\n}\\n\\n  awe-header {\\n  width: 100% !important;\\n  margin: 0 !important;\\n  padding: 0 !important;\\n  background: var(--header-bg) !important;\\n  color: var(--header-text) !important;\\n  border-bottom: 1px solid var(--header-border) !important;\\n}\\n  awe-header .header-content {\\n  gap: 0.125rem !important;\\n  padding: 0 0.25rem !important;\\n}\\n  awe-header [left-content] {\\n  flex: 0 0 auto !important;\\n  margin-right: 0.125rem !important;\\n}\\n  awe-header [center-content] {\\n  flex: 1 1 auto !important;\\n  display: flex !important;\\n  justify-content: center !important;\\n}\\n  awe-header [right-content] {\\n  flex: 0 0 auto !important;\\n  margin-left: 0.125rem !important;\\n}\\n\\n.header-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  justify-content: center;\\n  align-items: flex-start;\\n  background: transparent;\\n  box-shadow: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n.header-shadow[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 846px;\\n  height: 60px;\\n  background: #215ad6;\\n  border-radius: 0 0 55px 55px;\\n  filter: blur(10px);\\n  opacity: 0.18;\\n  z-index: 1;\\n  pointer-events: none;\\n}\\n\\n.header-logo[_ngcontent-%COMP%] {\\n  max-height: 40px !important;\\n  max-width: 160px !important;\\n  width: auto !important;\\n  height: auto !important;\\n  padding: 0 0.5rem !important;\\n  margin: 0px 0px 25px 0px;\\n}\\n@media (min-width: 1200px) {\\n  .header-logo[_ngcontent-%COMP%] {\\n    max-height: 50px !important;\\n    padding: 0 !important;\\n    margin-top: 5.6px;\\n  }\\n}\\n@media (min-width: 1400px) {\\n  .header-logo[_ngcontent-%COMP%] {\\n    max-height: 40px !important;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .header-logo[_ngcontent-%COMP%] {\\n    max-height: 28px !important;\\n    padding: 0 0.25rem !important;\\n  }\\n}\\n\\n.animated-logo-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: inline-block;\\n  cursor: pointer;\\n  perspective: 1000px;\\n}\\n.animated-logo-container[_ngcontent-%COMP%]   .animated-logo[_ngcontent-%COMP%] {\\n  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);\\n  transform: rotateY(0deg) scale(1);\\n  opacity: 1;\\n  filter: brightness(1) saturate(1);\\n  transform-style: preserve-3d;\\n}\\n.animated-logo-container[_ngcontent-%COMP%]   .animated-logo[_ngcontent-%COMP%]:hover {\\n  transform: rotateY(0deg) scale(1.05);\\n  filter: brightness(1.1) saturate(1.1);\\n}\\n.animated-logo-container[_ngcontent-%COMP%]:hover::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: -2px;\\n  left: -2px;\\n  right: -2px;\\n  bottom: -2px;\\n  background: linear-gradient(45deg, rgba(139, 92, 246, 0.3), rgba(236, 72, 153, 0.3), rgba(59, 130, 246, 0.3));\\n  border-radius: 8px;\\n  filter: blur(8px);\\n  opacity: 0;\\n  animation: _ngcontent-%COMP%_logoGlow 0.3s ease-in-out forwards;\\n  z-index: -1;\\n  pointer-events: none;\\n}\\n\\n@keyframes _ngcontent-%COMP%_logoRotateTransition {\\n  0% {\\n    transform: rotateY(0deg) scale(1);\\n    opacity: 1;\\n  }\\n  25% {\\n    transform: rotateY(45deg) scale(1.1);\\n    opacity: 0.8;\\n  }\\n  50% {\\n    transform: rotateY(90deg) scale(1.2);\\n    opacity: 0.6;\\n  }\\n  75% {\\n    transform: rotateY(135deg) scale(1.1);\\n    opacity: 0.8;\\n  }\\n  100% {\\n    transform: rotateY(180deg) scale(1);\\n    opacity: 1;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_logoFadeRotation {\\n  0% {\\n    transform: rotate(0deg) scale(1);\\n    opacity: 1;\\n  }\\n  50% {\\n    transform: rotate(180deg) scale(0.8);\\n    opacity: 0.3;\\n  }\\n  100% {\\n    transform: rotate(360deg) scale(1);\\n    opacity: 1;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_logoFlip {\\n  0% {\\n    transform: rotateX(0deg) scale(1);\\n    opacity: 1;\\n  }\\n  50% {\\n    transform: rotateX(90deg) scale(0.8);\\n    opacity: 0.4;\\n  }\\n  100% {\\n    transform: rotateX(0deg) scale(1);\\n    opacity: 1;\\n  }\\n}\\n.animated-logo.logo-transitioning.logo-rotate[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_logoRotateTransition 0.8s ease-in-out;\\n}\\n.animated-logo.logo-transitioning.logo-fade-rotate[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_logoFadeRotation 0.6s ease-in-out;\\n}\\n.animated-logo.logo-transitioning.logo-flip[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_logoFlip 0.4s ease-in-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_logoGlow {\\n  0% {\\n    opacity: 0;\\n    transform: scale(0.8);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: scale(1);\\n  }\\n}\\n.animated-logo-container[_ngcontent-%COMP%]::after {\\n  content: attr(data-studio);\\n  position: absolute;\\n  bottom: -20px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  font-size: 10px;\\n  font-weight: 600;\\n  color: var(--nav-arrow);\\n  opacity: 0;\\n  transition: opacity 0.2s ease;\\n  pointer-events: none;\\n  white-space: nowrap;\\n}\\n\\n.animated-logo-container[_ngcontent-%COMP%]:hover::after {\\n  opacity: 0.7;\\n}\\n\\n.studio-indicators[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: -30px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  display: flex;\\n  gap: 4px;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n.studio-indicators[_ngcontent-%COMP%]   .studio-dot[_ngcontent-%COMP%] {\\n  width: 6px;\\n  height: 6px;\\n  border-radius: 50%;\\n  background: var(--nav-arrow);\\n  opacity: 0.4;\\n  transition: all 0.3s ease;\\n}\\n.studio-indicators[_ngcontent-%COMP%]   .studio-dot.active[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  background: linear-gradient(135deg, #8b5cf6, #ec4899);\\n  transform: scale(1.3);\\n}\\n\\n.animated-logo-container[_ngcontent-%COMP%]:hover   .studio-indicators[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n\\n@media (max-width: 768px) {\\n  .animated-logo-container[_ngcontent-%COMP%]   .animated-logo[_ngcontent-%COMP%]:hover {\\n    transform: scale(1.02);\\n  }\\n  .animated-logo-container[_ngcontent-%COMP%]   .animated-logo.logo-transitioning[_ngcontent-%COMP%] {\\n    transform: scale(0.98);\\n  }\\n  .animated-logo-container[_ngcontent-%COMP%]::after {\\n    display: none;\\n  }\\n  .animated-logo-container[_ngcontent-%COMP%]:hover::before {\\n    display: none;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .animated-logo-container[_ngcontent-%COMP%]   .animated-logo[_ngcontent-%COMP%] {\\n    transition: all 0.2s ease;\\n  }\\n}\\n.nav-menu[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 2;\\n  width: 100%;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.nav-items[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  height: auto;\\n  margin: 0px 0px 20px 0px;\\n  background: var(--nav-bg) !important;\\n  border-radius: 0 0 40px 40px;\\n  clip-path: url(#headerClip);\\n  min-width: 900px;\\n  min-height: 56px;\\n  gap: 8px;\\n  padding: 8px 16px;\\n  box-shadow: var(--nav-shadow) !important;\\n  border: 1px solid var(--header-border) !important;\\n}\\n\\n.nav-item-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 5;\\n}\\n\\n  shared-nav-item .nav-item-container {\\n  position: relative;\\n}\\n  shared-nav-item .nav-item {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 16px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  padding: 10px 16px;\\n  border-radius: 24px;\\n  color: #64748b;\\n  background: transparent;\\n  border: none;\\n  white-space: nowrap;\\n  min-height: 40px;\\n  position: relative;\\n  overflow: hidden;\\n}\\n  shared-nav-item .nav-item:hover {\\n  background: rgba(59, 130, 246, 0.08);\\n  color: #3b82f6;\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);\\n}\\n  shared-nav-item .nav-item.selected {\\n  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\\n  color: white;\\n  font-weight: 600;\\n  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);\\n  transform: translateY(-2px);\\n}\\n  shared-nav-item .nav-item.selected .nav-icon {\\n  filter: brightness(0) invert(1);\\n}\\n  shared-nav-item .nav-item.selected .dropdown-arrow svg {\\n  color: white;\\n}\\n  shared-nav-item .nav-item.disabled {\\n  opacity: 0.4;\\n  cursor: not-allowed;\\n  pointer-events: none;\\n  color: #94a3b8;\\n}\\n  shared-nav-item .nav-item.disabled:hover {\\n  background: transparent;\\n  transform: none;\\n  box-shadow: none;\\n}\\n  shared-nav-item .nav-item:focus-visible {\\n  outline: 2px solid #3b82f6;\\n  outline-offset: 2px;\\n}\\n  shared-nav-item .nav-item .item-icon {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 20px;\\n  height: 20px;\\n  transition: all 0.3s ease;\\n}\\n  shared-nav-item .nav-item .nav-icon {\\n  width: 20px;\\n  height: 20px;\\n  object-fit: contain;\\n  transition: all 0.3s ease;\\n}\\n  shared-nav-item .nav-item .item-label {\\n  font-family: \\\"Inter\\\", \\\"Segoe UI\\\", sans-serif;\\n  font-size: 15px;\\n  font-weight: 500;\\n  letter-spacing: -0.01em;\\n  transition: all 0.3s ease;\\n}\\n  shared-nav-item .nav-item .dropdown-arrow {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 16px;\\n  height: 16px;\\n  margin-left: 4px;\\n  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n  shared-nav-item .nav-item .dropdown-arrow.open {\\n  transform: rotate(180deg);\\n}\\n  shared-nav-item .nav-item .dropdown-arrow svg {\\n  width: 14px;\\n  height: 14px;\\n  transition: color 0.3s ease;\\n}\\n  shared-nav-item .nav-item::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  width: 0;\\n  height: 0;\\n  border-radius: 50%;\\n  background: rgba(59, 130, 246, 0.3);\\n  transition: width 0.6s, height 0.6s, top 0.6s, left 0.6s;\\n  transform: translate(-50%, -50%);\\n  z-index: -1;\\n}\\n  shared-nav-item .nav-item:active::before {\\n  width: 200px;\\n  height: 200px;\\n  top: 50%;\\n  left: 50%;\\n}\\n\\n.user-info-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n@media (min-width: 1200px) {\\n  .user-info-container[_ngcontent-%COMP%] {\\n    gap: 16px;\\n  }\\n}\\n\\n.org-path-dropdown-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: inline-block;\\n}\\n.org-path-dropdown-container[_ngcontent-%COMP%]   .org-path-trigger[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background: var(--nav-bg) !important;\\n  border-radius: 20px;\\n  padding: 8px 14px;\\n  height: 40px;\\n  font-size: 14px;\\n  box-shadow: var(--nav-shadow) !important;\\n  cursor: pointer;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  border: 1px solid var(--header-border) !important;\\n}\\n.org-path-dropdown-container[_ngcontent-%COMP%]   .org-path-trigger[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\\n  transform: translateY(-1px);\\n}\\n.org-path-dropdown-container[_ngcontent-%COMP%]   .org-path-trigger[_ngcontent-%COMP%]   .org-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-right: 8px;\\n  border-radius: 50%;\\n  background: transparent;\\n  flex-shrink: 0;\\n}\\n.org-path-dropdown-container[_ngcontent-%COMP%]   .org-path-trigger[_ngcontent-%COMP%]   .org-icon[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n.org-path-dropdown-container[_ngcontent-%COMP%]   .org-path-trigger[_ngcontent-%COMP%]   .org-label-text[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: var(--nav-text) !important;\\n  margin-right: 8px;\\n  display: flex;\\n  align-items: center;\\n  flex: 1;\\n  min-width: 0;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n.org-path-dropdown-container[_ngcontent-%COMP%]   .org-path-trigger[_ngcontent-%COMP%]   .org-dropdown-arrow[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  transition: transform 0.3s ease;\\n  margin-left: auto;\\n}\\n.org-path-dropdown-container[_ngcontent-%COMP%]   .org-path-trigger[_ngcontent-%COMP%]   .org-dropdown-arrow.open[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n.org-path-dropdown-container[_ngcontent-%COMP%]   .org-path-trigger[_ngcontent-%COMP%]   .org-dropdown-arrow[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 14px;\\n  height: 14px;\\n  color: var(--nav-arrow) !important;\\n  transition: transform 0.4s ease-in-out;\\n}\\n@media screen and (min-width: 1200px) {\\n  .org-path-dropdown-container[_ngcontent-%COMP%]   .org-path-trigger[_ngcontent-%COMP%] {\\n    padding: 10px 16px;\\n    height: 44px;\\n  }\\n  .org-path-dropdown-container[_ngcontent-%COMP%]   .org-path-trigger[_ngcontent-%COMP%]   .org-icon[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 24px;\\n    height: 24px;\\n  }\\n  .org-path-dropdown-container[_ngcontent-%COMP%]   .org-path-trigger[_ngcontent-%COMP%]   .org-label-text[_ngcontent-%COMP%] {\\n    font-size: 15px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .org-path-dropdown-container[_ngcontent-%COMP%]   .org-path-trigger[_ngcontent-%COMP%] {\\n    padding: 6px 10px;\\n    height: 36px;\\n  }\\n  .org-path-dropdown-container[_ngcontent-%COMP%]   .org-path-trigger[_ngcontent-%COMP%]   .org-label-text[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n.org-path-dropdown-container[_ngcontent-%COMP%]   .org-path-popover[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 100%;\\n  left: 0;\\n  margin-top: 8px;\\n  z-index: 20;\\n  background: var(--dropdown-bg) !important;\\n  border-radius: 16px;\\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);\\n  border: 1px solid var(--dropdown-border) !important;\\n  padding: 24px;\\n  overflow-y: auto;\\n  overflow-x: hidden;\\n  max-height: 80vh;\\n  max-width: 400px;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n}\\n.org-path-dropdown-container[_ngcontent-%COMP%]   .org-path-popover.right[_ngcontent-%COMP%] {\\n  left: auto;\\n  right: 0;\\n}\\n.org-path-dropdown-container[_ngcontent-%COMP%]   .org-path-popover.left[_ngcontent-%COMP%] {\\n  left: 0;\\n  right: auto;\\n}\\n.org-path-dropdown-container[_ngcontent-%COMP%]   .org-path-popover[_ngcontent-%COMP%]   .filter-config-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 700;\\n  margin-bottom: 20px;\\n  color: var(--dropdown-text) !important;\\n}\\n.org-path-dropdown-container[_ngcontent-%COMP%]   .org-path-popover[_ngcontent-%COMP%]   .dropdown-row-vertical[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n  align-items: stretch;\\n  width: 100%;\\n}\\n.org-path-dropdown-container[_ngcontent-%COMP%]   .org-path-popover[_ngcontent-%COMP%]   .filter-label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-weight: 600;\\n  font-size: 14px;\\n  color: var(--dropdown-text) !important;\\n  margin-bottom: 6px;\\n}\\n.org-path-dropdown-container[_ngcontent-%COMP%]   .org-path-popover[_ngcontent-%COMP%]   .filter-label.required[_ngcontent-%COMP%]::after {\\n  content: \\\" *\\\";\\n  color: #ef4444;\\n  font-weight: bold;\\n}\\n.org-path-dropdown-container[_ngcontent-%COMP%]   .org-path-popover[_ngcontent-%COMP%]   .popover-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  justify-content: flex-end;\\n  width: 100%;\\n  margin-top: 24px;\\n}\\n@media (max-width: 768px) {\\n  .org-path-dropdown-container[_ngcontent-%COMP%]   .org-path-popover[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n    min-width: 300px;\\n    padding: 20px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .org-path-dropdown-container[_ngcontent-%COMP%]   .org-path-popover[_ngcontent-%COMP%] {\\n    max-width: 98vw;\\n    min-width: 280px;\\n    padding: 16px;\\n    margin-top: 4px;\\n  }\\n  .org-path-dropdown-container[_ngcontent-%COMP%]   .org-path-popover[_ngcontent-%COMP%]   .filter-config-title[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n    margin-bottom: 16px;\\n  }\\n  .org-path-dropdown-container[_ngcontent-%COMP%]   .org-path-popover[_ngcontent-%COMP%]   .dropdown-row-vertical[_ngcontent-%COMP%] {\\n    gap: 12px;\\n  }\\n  .org-path-dropdown-container[_ngcontent-%COMP%]   .org-path-popover[_ngcontent-%COMP%]   .popover-actions[_ngcontent-%COMP%] {\\n    margin-top: 20px;\\n  }\\n}\\n\\n.org-path-backdrop[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: transparent;\\n  z-index: 19;\\n  cursor: default;\\n}\\n\\n.app-drawer-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n}\\n.app-drawer-container[_ngcontent-%COMP%]   .app-drawer-trigger[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  color: var(--nav-arrow) !important;\\n}\\n.app-drawer-container[_ngcontent-%COMP%]   .app-drawer-trigger[_ngcontent-%COMP%]:hover {\\n  background: var(--dropdown-hover) !important;\\n  color: var(--dropdown-text) !important;\\n}\\n.app-drawer-container[_ngcontent-%COMP%]   .app-drawer-trigger.active[_ngcontent-%COMP%] {\\n  background: rgba(139, 92, 246, 0.1);\\n  color: #8b5cf6;\\n}\\n.app-drawer-container[_ngcontent-%COMP%]   .app-drawer-trigger[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n.app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: calc(100% + 8px);\\n  right: 0;\\n  z-index: 1000;\\n  background: var(--dropdown-bg) !important;\\n  border-radius: 16px;\\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);\\n  border: 1px solid var(--dropdown-border) !important;\\n  width: 320px;\\n  max-width: calc(100vw - 40px);\\n  opacity: 0;\\n  visibility: hidden;\\n  transform: translateY(-10px);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  overflow: hidden;\\n}\\n.app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown.visible[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  visibility: visible;\\n  transform: translateY(0);\\n}\\n.app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%]   .app-drawer-content[_ngcontent-%COMP%] {\\n  padding: 0;\\n  overflow: hidden;\\n}\\n.app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%]   .app-drawer-header[_ngcontent-%COMP%] {\\n  padding: 20px 24px 16px 24px;\\n  background: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%);\\n  color: white;\\n}\\n.app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%]   .app-drawer-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 18px;\\n  font-weight: 700;\\n  text-align: center;\\n}\\n.app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%]   .app-drawer-grid[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n  padding: 16px 20px 20px 20px;\\n}\\n.app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%]   .app-drawer-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 12px;\\n  border-radius: 12px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  border: 1px solid transparent;\\n}\\n.app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%]   .app-drawer-item[_ngcontent-%COMP%]:hover {\\n  background: var(--dropdown-hover) !important;\\n  border-color: rgba(139, 92, 246, 0.2);\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.1);\\n}\\n.app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%]   .app-drawer-item[_ngcontent-%COMP%]   .app-icon[_ngcontent-%COMP%] {\\n  margin-right: 12px;\\n  width: 40px;\\n  height: 40px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: var(--dropdown-hover) !important;\\n  border-radius: 10px;\\n  flex-shrink: 0;\\n}\\n.app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%]   .app-drawer-item[_ngcontent-%COMP%]   .app-icon[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  object-fit: contain;\\n}\\n.app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%]   .app-drawer-item[_ngcontent-%COMP%]   .app-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n.app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%]   .app-drawer-item[_ngcontent-%COMP%]   .app-info[_ngcontent-%COMP%]   .app-name[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: var(--dropdown-text) !important;\\n  margin-bottom: 2px;\\n  word-wrap: break-word;\\n  overflow-wrap: break-word;\\n}\\n.app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%]   .app-drawer-item[_ngcontent-%COMP%]   .app-info[_ngcontent-%COMP%]   .app-description[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: var(--nav-arrow) !important;\\n  line-height: 1.3;\\n  word-wrap: break-word;\\n  overflow-wrap: break-word;\\n  white-space: normal;\\n}\\n@media (max-width: 768px) {\\n  .app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%] {\\n    width: 280px;\\n    max-width: calc(100vw - 20px);\\n    right: -10px;\\n  }\\n  .app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%]   .app-drawer-header[_ngcontent-%COMP%] {\\n    padding: 16px 20px 12px 20px;\\n  }\\n  .app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%]   .app-drawer-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%]   .app-drawer-grid[_ngcontent-%COMP%] {\\n    gap: 6px;\\n    padding: 12px 16px 16px 16px;\\n  }\\n  .app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%]   .app-drawer-item[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n  .app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%]   .app-drawer-item[_ngcontent-%COMP%]   .app-icon[_ngcontent-%COMP%] {\\n    width: 36px;\\n    height: 36px;\\n  }\\n  .app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%]   .app-drawer-item[_ngcontent-%COMP%]   .app-icon[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 20px;\\n    height: 20px;\\n  }\\n  .app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%]   .app-drawer-item[_ngcontent-%COMP%]   .app-info[_ngcontent-%COMP%]   .app-name[_ngcontent-%COMP%] {\\n    font-size: 13px;\\n  }\\n  .app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%]   .app-drawer-item[_ngcontent-%COMP%]   .app-info[_ngcontent-%COMP%]   .app-description[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%] {\\n    width: 260px;\\n    max-width: calc(100vw - 10px);\\n    right: -5px;\\n  }\\n  .app-drawer-container[_ngcontent-%COMP%]   .app-drawer-dropdown[_ngcontent-%COMP%]   .app-drawer-grid[_ngcontent-%COMP%] {\\n    padding: 10px 12px 12px 12px;\\n  }\\n}\\n\\n.theme-toggle[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  cursor: pointer;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  background: rgba(255, 255, 255, 0.8);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.theme-toggle[_ngcontent-%COMP%]:hover {\\n  background: rgb(255, 255, 255);\\n  transform: scale(1.05);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n}\\n.theme-toggle[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  transition: transform 0.3s ease;\\n}\\n.theme-toggle[_ngcontent-%COMP%]:active   img[_ngcontent-%COMP%] {\\n  transform: scale(0.95);\\n}\\n\\n.profile-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-trigger[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n  padding: 2px;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-trigger[_ngcontent-%COMP%]:hover {\\n  background: rgba(0, 0, 0, 0.05);\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-trigger.active[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.1);\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-trigger[_ngcontent-%COMP%]   .profile-avatar[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 2px solid rgba(255, 255, 255, 0.2);\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: calc(100% + 8px);\\n  right: 0;\\n  z-index: 1000;\\n  background: var(--dropdown-bg) !important;\\n  border-radius: 16px;\\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);\\n  border: 1px solid var(--dropdown-border) !important;\\n  min-width: 320px;\\n  max-width: 400px;\\n  opacity: 0;\\n  visibility: hidden;\\n  transform: translateY(-10px);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown.visible[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  visibility: visible;\\n  transform: translateY(0);\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-dropdown-content[_ngcontent-%COMP%] {\\n  padding: 0;\\n  overflow: hidden;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-user-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 12px;\\n  padding: 24px 24px 20px 24px;\\n  background: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%);\\n  color: white;\\n  position: relative;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-user-info[_ngcontent-%COMP%]   .profile-avatar-large[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-user-info[_ngcontent-%COMP%]   .profile-avatar-large[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 56px;\\n  height: 56px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 3px solid rgba(255, 255, 255, 0.3);\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-user-info[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-user-info[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]   .profile-name[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 700;\\n  margin-bottom: 4px;\\n  color: white;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-user-info[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]   .profile-designation[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  opacity: 0.9;\\n  margin-bottom: 2px;\\n  color: rgba(255, 255, 255, 0.9);\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-user-info[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]   .profile-email[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  opacity: 0.8;\\n  color: rgba(255, 255, 255, 0.8);\\n  word-break: break-word;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-user-info[_ngcontent-%COMP%]   .profile-close-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 16px;\\n  right: 16px;\\n  background: none;\\n  border: none;\\n  color: rgba(255, 255, 255, 0.8);\\n  cursor: pointer;\\n  padding: 4px;\\n  border-radius: 4px;\\n  transition: all 0.2s ease;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-user-info[_ngcontent-%COMP%]   .profile-close-btn[_ngcontent-%COMP%]:hover {\\n  color: white;\\n  background: rgba(255, 255, 255, 0.1);\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-user-info[_ngcontent-%COMP%]   .profile-close-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-divider[_ngcontent-%COMP%] {\\n  height: 1px;\\n  background: var(--dropdown-border) !important;\\n  margin: 0;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-section[_ngcontent-%COMP%] {\\n  padding: 16px 24px;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-section[_ngcontent-%COMP%]   .profile-section-header[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-section[_ngcontent-%COMP%]   .profile-section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: var(--dropdown-text) !important;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .theme-toggle-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .theme-toggle-container[_ngcontent-%COMP%]   .theme-option[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 10px 12px;\\n  border: 1px solid var(--dropdown-border) !important;\\n  border-radius: 8px;\\n  background: var(--dropdown-bg) !important;\\n  color: var(--nav-arrow) !important;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .theme-toggle-container[_ngcontent-%COMP%]   .theme-option[_ngcontent-%COMP%]:hover {\\n  border-color: var(--dropdown-border) !important;\\n  background: var(--dropdown-hover) !important;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .theme-toggle-container[_ngcontent-%COMP%]   .theme-option.active[_ngcontent-%COMP%] {\\n  border-color: #8b5cf6;\\n  background: var(--dropdown-hover) !important;\\n  color: #8b5cf6;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .theme-toggle-container[_ngcontent-%COMP%]   .theme-option.active[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  color: #8b5cf6;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .theme-toggle-container[_ngcontent-%COMP%]   .theme-option[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  color: currentColor;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .language-options[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .language-options[_ngcontent-%COMP%]   .language-option[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  width: 100%;\\n  padding: 10px 12px;\\n  border: none;\\n  border-radius: 6px;\\n  background: transparent;\\n  color: var(--nav-arrow) !important;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  text-align: left;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .language-options[_ngcontent-%COMP%]   .language-option[_ngcontent-%COMP%]:hover {\\n  background: var(--dropdown-hover) !important;\\n  color: var(--dropdown-text) !important;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .language-options[_ngcontent-%COMP%]   .language-option.active[_ngcontent-%COMP%] {\\n  background: rgba(139, 92, 246, 0.1) !important;\\n  color: #8b5cf6;\\n  font-weight: 600;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-actions[_ngcontent-%COMP%] {\\n  padding: 16px 24px 24px 24px;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-actions[_ngcontent-%COMP%]   .profile-action-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  width: 100%;\\n  padding: 12px 16px;\\n  border: none;\\n  border-radius: 8px;\\n  background: transparent;\\n  color: #dc2626;\\n  font-size: 14px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-actions[_ngcontent-%COMP%]   .profile-action-item[_ngcontent-%COMP%]:hover {\\n  background: #fef2f2;\\n  color: #b91c1c;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-actions[_ngcontent-%COMP%]   .profile-action-item[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  color: currentColor;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-actions[_ngcontent-%COMP%]   .logout-btn[_ngcontent-%COMP%] {\\n  justify-content: center;\\n  background: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%);\\n  color: white;\\n}\\n.profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-actions[_ngcontent-%COMP%]   .logout-btn[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #7c3aed 0%, #db2777 100%);\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);\\n}\\n@media (max-width: 768px) {\\n  .profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%] {\\n    min-width: 280px;\\n    right: -20px;\\n  }\\n  .profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-user-info[_ngcontent-%COMP%] {\\n    padding: 20px;\\n  }\\n  .profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-user-info[_ngcontent-%COMP%]   .profile-avatar-large[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 48px;\\n    height: 48px;\\n  }\\n  .profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-user-info[_ngcontent-%COMP%]   .profile-details[_ngcontent-%COMP%]   .profile-name[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-section[_ngcontent-%COMP%] {\\n    padding: 12px 20px;\\n  }\\n  .profile-container[_ngcontent-%COMP%]   .profile-dropdown[_ngcontent-%COMP%]   .profile-actions[_ngcontent-%COMP%] {\\n    padding: 12px 20px 20px 20px;\\n  }\\n}\\n\\n.dropdown-portal-menu[_ngcontent-%COMP%] {\\n  position: fixed;\\n  z-index: 2000;\\n}\\n\\n.dropdown-menu[_ngcontent-%COMP%] {\\n  background: var(--dropdown-bg) !important;\\n  border-radius: 16px;\\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);\\n  border: 1px solid var(--dropdown-border) !important;\\n  min-width: 320px;\\n  padding: 8px;\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  margin-top: 8px;\\n}\\n\\n.dropdown-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 16px;\\n  border-radius: 12px;\\n  cursor: pointer;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.dropdown-item[_ngcontent-%COMP%]:hover {\\n  background: var(--dropdown-hover) !important;\\n  transform: translateX(2px);\\n}\\n\\n.dropdown-icon[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  object-fit: contain;\\n  flex-shrink: 0;\\n}\\n\\n.dropdown-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.dropdown-label[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 16px;\\n  color: var(--dropdown-text) !important;\\n  margin-bottom: 4px;\\n}\\n\\n.dropdown-description[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--nav-arrow) !important;\\n  line-height: 1.5;\\n}\\n\\n@media (max-width: 900px) {\\n  .header-shadow[_ngcontent-%COMP%], \\n   .nav-items[_ngcontent-%COMP%] {\\n    width: 98vw;\\n    min-width: unset;\\n    padding: 8px 12px;\\n  }\\n}\\n@media (max-width: 600px) {\\n  .header-shadow[_ngcontent-%COMP%], \\n   .nav-items[_ngcontent-%COMP%] {\\n    width: 100vw;\\n    min-width: unset;\\n    padding: 6px 8px;\\n    gap: 4px;\\n  }\\n  .nav-items[_ngcontent-%COMP%] {\\n    min-height: 48px;\\n  }\\n    shared-nav-item .nav-item {\\n    padding: 8px 12px;\\n    font-size: 14px;\\n    border-radius: 20px;\\n  }\\n    shared-nav-item .nav-item .item-label {\\n    font-size: 14px;\\n  }\\n}\\n@media (max-width: 768px) {\\n    shared-nav-item .nav-item {\\n    padding: 8px 14px;\\n    font-size: 15px;\\n  }\\n  .user-info-container[_ngcontent-%COMP%] {\\n    gap: 8px;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_fadeInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.nav-items[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return SharedAppHeaderComponent;\n})();", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "HeaderComponent", "NavigationEnd", "filter", "Subscription", "DropdownComponent", "IconComponent", "ButtonComponent", "SharedNavItemComponent", "Validators", "ReactiveFormsModule", "i0", "ɵɵelement", "ɵɵclassProp", "i_r3", "ctx_r1", "currentLogoIndex", "ɵɵelementStart", "ɵɵtemplate", "SharedAppHeaderComponent_div_0_div_9_div_1_Template", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "studioLogos", "ɵɵlistener", "SharedAppHeaderComponent_div_0_shared_nav_item_15_Template_shared_nav_item_toggleDropdownEvent_0_listener", "i_r5", "ɵɵrestoreView", "_r4", "index", "ɵɵnextContext", "ɵɵresetView", "toggleDropdown", "SharedAppHeaderComponent_div_0_shared_nav_item_15_Template_shared_nav_item_navigateEvent_0_listener", "$event", "navigateTo", "SharedAppHeaderComponent_div_0_shared_nav_item_15_Template_shared_nav_item_selectEvent_0_listener", "selectMenuItem", "SharedAppHeaderComponent_div_0_shared_nav_item_15_Template_shared_nav_item_dropdownItemSelected_0_listener", "onDropdownItemSelected", "SharedAppHeaderComponent_div_0_shared_nav_item_15_Template_shared_nav_item_dropdownPortalOpen_0_listener", "onDropdownPortalOpen", "item_r6", "label", "route", "selected", "hasDropdown", "dropdownOpen", "dropdownItems", "ɵɵpureFunction0", "_c2", "icon", "disabled", "ɵɵtext", "SharedAppHeaderComponent_div_0_div_17_div_11_Template_ava_dropdown_selectionChange_8_listener", "_r8", "onOrgSelect", "SharedAppHeaderComponent_div_0_div_17_div_11_Template_ava_dropdown_selectionChange_11_listener", "onDomainSelect", "SharedAppHeaderComponent_div_0_div_17_div_11_Template_ava_dropdown_selectionChange_14_listener", "onProjectSelect", "SharedAppHeaderComponent_div_0_div_17_div_11_Template_ava_dropdown_selectionChange_17_listener", "onTeamSelect", "SharedAppHeaderComponent_div_0_div_17_div_11_Template_ava_button_userClick_19_listener", "closeOrgDialog", "SharedAppHeaderComponent_div_0_div_17_div_11_Template_ava_button_userClick_20_listener", "saveOrgPathAndClose", "popoverAlign", "headerConfigForm", "orgOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "domainOptions", "selected<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "projectOptions", "selectedProjectName", "<PERSON><PERSON><PERSON><PERSON>", "teamOptions", "selectedTeamName", "selectedProject", "valid", "SharedAppHeaderComponent_div_0_div_17_div_12_Template_div_click_0_listener", "_r9", "SharedAppHeaderComponent_div_0_div_17_Template_div_click_1_listener", "_r7", "toggleOrgDialog", "SharedAppHeaderComponent_div_0_div_17_div_11_Template", "SharedAppHeaderComponent_div_0_div_17_div_12_Template", "ɵɵtextInterpolate", "orgLabel", "isOrgDialogOpen", "SharedAppHeaderComponent_div_0_div_18_div_3_Template_div_click_0_listener", "_r11", "closeAppDrawer", "ɵɵtextInterpolate1", "app_r13", "description", "SharedAppHeaderComponent_div_0_div_18_div_7_Template_div_click_0_listener", "_r12", "$implicit", "navigateToApp", "SharedAppHeaderComponent_div_0_div_18_div_7_div_6_Template", "ɵɵsanitizeUrl", "name", "SharedAppHeaderComponent_div_0_div_18_Template_div_click_1_listener", "_r10", "toggleAppDrawer", "SharedAppHeaderComponent_div_0_div_18_div_3_Template", "SharedAppHeaderComponent_div_0_div_18_div_7_Template", "isAppDrawerOpen", "getFilteredApps", "SharedAppHeaderComponent_div_0_div_19_Template_div_click_0_listener", "_r14", "toggleTheme", "themeMenuIcon", "userDesignation", "userEmail", "SharedAppHeaderComponent_div_0_div_20_div_14_Template_button_click_5_listener", "_r16", "currentTheme", "SharedAppHeaderComponent_div_0_div_20_div_14_Template_button_click_10_listener", "SharedAppHeaderComponent_div_0_div_20_div_15_button_5_Template_button_click_0_listener", "language_r18", "_r17", "switchLanguage", "code", "currentLanguage", "SharedAppHeaderComponent_div_0_div_20_div_15_button_5_Template", "config", "availableLanguages", "SharedAppHeaderComponent_div_0_div_20_Template_div_click_1_listener", "_r15", "toggleProfileDropdown", "SharedAppHeaderComponent_div_0_div_20_div_11_Template", "SharedAppHeaderComponent_div_0_div_20_div_12_Template", "SharedAppHeaderComponent_div_0_div_20_div_14_Template", "SharedAppHeaderComponent_div_0_div_20_div_15_Template", "SharedAppHeaderComponent_div_0_div_20_Template_button_click_18_listener", "logout", "profileDropdownOpen", "userAvatar", "userName", "showThemeToggleInProfile", "showLanguageSwitcher", "SharedAppHeaderComponent_div_0_Template_div_mouseenter_7_listener", "_r1", "pauseLogoAnimation", "SharedAppHeaderComponent_div_0_Template_div_mouseleave_7_listener", "resumeLogoAnimation", "SharedAppHeaderComponent_div_0_div_9_Template", "SharedAppHeaderComponent_div_0_shared_nav_item_15_Template", "SharedAppHeaderComponent_div_0_div_17_Template", "SharedAppHeaderComponent_div_0_div_18_Template", "SharedAppHeaderComponent_div_0_div_19_Template", "SharedAppHeaderComponent_div_0_div_20_Template", "isLogoAnimating", "currentLogo", "currentStudioName", "length", "navItems", "showOrgSelector", "showAppDrawer", "showThemeToggle", "showProfileDropdown", "item_r20", "SharedAppHeaderComponent_div_1_div_2_Template_div_click_0_listener", "_r19", "SharedAppHeaderComponent_div_1_div_2_img_1_Template", "SharedAppHeaderComponent_div_1_div_2_Template", "ɵɵstyleProp", "dropdownPortal", "rect", "bottom", "left", "items", "SharedAppHeaderComponent", "router", "cdr", "renderer", "elementRef", "tokenStorage", "authService", "formBuilder", "customNavItemComponent", "orgConfigService", "themeService", "isLoginPage", "studioNames", "logoAnimationInterval", "navigationEvent", "dropdownItemSelected", "profileAction", "themeToggle", "languageChange", "orgConfigChange", "selectedTeam", "hierarchyData", "orgPathTrigger", "popoverRef", "open", "parentLabel", "navItemId", "subscriptions", "constructor", "ngOnInit", "initializeHeader", "setupRouterSubscription", "setupThemeSubscription", "loadUserInfo", "initializeForm", "initializeLogo", "initOrgPathFromCookie", "ngAfterViewInit", "updateActiveMenuItemByRoute", "url", "ngOnDestroy", "unsubscribe", "clearInterval", "console", "log", "enableLogoAnimation", "logoSrc", "projectName", "startLogoAnimation", "interval", "setInterval", "animateToNextLogo", "animationStyle", "logoAnimationStyle", "logoElement", "document", "querySelector", "classList", "add", "timings", "rotate", "changeAt", "duration", "flip", "timing", "setTimeout", "detectChanges", "remove", "undefined", "warn", "redirectUrl", "currentApp", "availableApps", "group", "org", "required", "domain", "project", "team", "routerSub", "events", "pipe", "event", "subscribe", "checkIfLoginPage", "loginRoutes", "some", "includes", "themeObservable", "theme", "updateThemeAssets", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getDaUsername", "getDaUserEmail", "getDaUserDesignation", "generateUserAvatar", "nameParts", "trim", "split", "initials", "toUpperCase", "colors", "colorIndex", "backgroundColor", "btoa", "path", "<PERSON><PERSON><PERSON><PERSON>", "parts", "usecasePath", "usecaseIdPath", "ids", "map", "Number", "patchValue", "toString", "pathParts", "loadData", "getOrganizationHierarchy", "next", "data", "loadOrganizations", "loadDomains", "loadProjects", "loadTeams", "error", "organizationName", "value", "orgId", "selectedOrgId", "selectedOptions", "find", "o", "domains", "domainName", "domainId", "selectedDomainId", "d", "projects", "projectId", "selectedProjectId", "p", "teams", "teamName", "teamId", "selectedTeamId", "orgPath", "orgName", "formValue", "<PERSON><PERSON><PERSON><PERSON>", "emit", "<PERSON><PERSON><PERSON><PERSON>", "selected<PERSON><PERSON><PERSON>", "navigate", "for<PERSON>ach", "item", "i", "parentIndex", "closeDropdownPortal", "newTheme", "languageCode", "getCurrentLanguageName", "language", "lang", "calculateAppDrawerPosition", "app", "startsWith", "window", "calculatePopoverPosition", "triggerRect", "nativeElement", "getBoundingClientRect", "popoverRect", "viewportWidth", "innerWidth", "width", "appDrawerDropdown", "dropdownRect", "right", "overflow", "style", "onNavItemClick", "preventDefault", "stopPropagation", "currentTarget", "parentItem", "child", "defaultItem", "onDocumentClick", "target", "contains", "closest", "ɵɵdirectiveInject", "i1", "Router", "ChangeDetectorRef", "Renderer2", "ElementRef", "i2", "TokenStorageService", "i3", "AuthService", "i4", "FormBuilder", "selectors", "viewQuery", "SharedAppHeaderComponent_Query", "rf", "ctx", "SharedAppHeaderComponent_click_HostBindingHandler", "ɵɵresolveDocument", "SharedAppHeaderComponent_div_0_Template", "SharedAppHeaderComponent_div_1_Template", "i5", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "ɵNgNoValidate", "NgControlStatusGroup", "FormGroupDirective", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\components\\app-header\\app-header.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\components\\app-header\\app-header.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  Input,\r\n  Output,\r\n  EventEmitter,\r\n  <PERSON><PERSON>nit,\r\n  On<PERSON><PERSON>roy,\r\n  HostListener,\r\n  ElementRef,\r\n  ChangeDetectorRef,\r\n  ViewChild,\r\n  Renderer2,\r\n  AfterViewInit,\r\n  inject,\r\n  Optional,\r\n} from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { HeaderComponent } from '@awe/play-comp-library';\r\nimport { Router, NavigationEnd } from '@angular/router';\r\nimport { filter } from 'rxjs/operators';\r\nimport { Subscription } from 'rxjs';\r\n// import { ThemeService } from '../../services/theme/theme.service'; // Optional - can be injected if available\r\nimport { TokenStorageService } from '../../auth/services/token-storage.service';\r\nimport { AuthService } from '../../auth/services/auth.service';\r\nimport {\r\n  DropdownComponent,\r\n  DropdownOption,\r\n  IconComponent,\r\n} from '@ava/play-comp-library';\r\nimport { ButtonComponent } from '@ava/play-comp-library';\r\nimport { SharedNavItemComponent } from '../nav-item/nav-item.component';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { ReactiveFormsModule } from '@angular/forms';\r\n\r\n// Organization hierarchy interfaces\r\ninterface Team {\r\n  teamId: number;\r\n  teamName: string;\r\n}\r\n\r\ninterface Project {\r\n  projectId: number;\r\n  projectName: string;\r\n  teams: Team[];\r\n}\r\n\r\ninterface Domain {\r\n  domainId: number;\r\n  domainName: string;\r\n  projects: Project[];\r\n}\r\n\r\ninterface Organization {\r\n  orgId: number;\r\n  organizationName: string;\r\n  domains: Domain[];\r\n}\r\n\r\n// Shared interfaces for navigation configuration\r\nexport interface SharedDropdownItem {\r\n  label: string;\r\n  description: string;\r\n  route: string;\r\n  icon: string;\r\n}\r\n\r\nexport interface SharedNavItem {\r\n  label: string;\r\n  route: string;\r\n  selected: boolean;\r\n  hasDropdown: boolean;\r\n  dropdownOpen?: boolean;\r\n  icon: string;\r\n  dropdownItems?: SharedDropdownItem[];\r\n  disabled?: boolean;\r\n}\r\n\r\nexport interface StudioApp {\r\n  name: string;\r\n  route: string;\r\n  icon: string;\r\n  description?: string;\r\n}\r\n\r\nexport interface HeaderConfig {\r\n  logoSrc: string;\r\n  navItems: SharedNavItem[];\r\n  showOrgSelector?: boolean;\r\n  showThemeToggle?: boolean;\r\n  showAppDrawer?: boolean;\r\n  showProfileDropdown?: boolean;\r\n  projectName?: string;\r\n  redirectUrl?: string;\r\n  currentApp?: string; // Current app name to filter from drawer\r\n  availableApps?: StudioApp[]; // Available studio apps\r\n  // Profile dropdown configuration\r\n  showThemeToggleInProfile?: boolean;\r\n  showLanguageSwitcher?: boolean;\r\n  availableLanguages?: { code: string; name: string; flag?: string }[];\r\n  // Logo animation configuration\r\n  enableLogoAnimation?: boolean;\r\n  logoAnimationInterval?: number; // Animation interval in milliseconds (default: 3000)\r\n  logoAnimationStyle?: 'rotate' | 'fade-rotate' | 'flip'; // Animation style (default: 'rotate')\r\n  studioLogos?: string[]; // Custom studio logos array\r\n  studioNames?: string[]; // Custom studio names array\r\n}\r\n\r\n@Component({\r\n  selector: 'shared-app-header',\r\n  standalone: true,\r\n  imports: [\r\n    HeaderComponent,\r\n    CommonModule,\r\n    SharedNavItemComponent,\r\n    DropdownComponent,\r\n    ButtonComponent,\r\n    ReactiveFormsModule,\r\n    IconComponent,\r\n  ],\r\n  templateUrl: './app-header.component.html',\r\n  styleUrls: ['./app-header.component.scss'],\r\n})\r\nexport class SharedAppHeaderComponent\r\n  implements OnInit, OnDestroy, AfterViewInit\r\n{\r\n  @Input() config!: HeaderConfig;\r\n  @Input() customNavItemComponent?: any; // Allow custom nav item component\r\n  @Input() orgConfigService?: any; // Allow injecting org config service from parent\r\n  @Input() themeService?: any; // Allow injecting theme service from parent\r\n\r\n  // Header state\r\n  currentTheme: 'light' | 'dark' = 'light';\r\n  profileDropdownOpen: boolean = false;\r\n  userName: string = '';\r\n  userEmail: string = '';\r\n  userDesignation: string = '';\r\n  userAvatar: string = '';\r\n  themeMenuIcon: string = '';\r\n  isLoginPage: boolean = false; // Added to track login pages\r\n\r\n  // Animated logo management\r\n  studioLogos: string[] = [\r\n    'assets/svgs/ascendion-logo/header-ascendion-logo.svg',\r\n    'assets/svgs/ascendion-logo-light.svg',\r\n    'assets/svgs/ascendion-logo-dark.svg',\r\n  ];\r\n  studioNames: string[] = ['Console', 'Experience Studio', 'Product Studio'];\r\n  currentLogoIndex: number = 0;\r\n  currentLogo: string = '';\r\n  currentStudioName: string = '';\r\n  logoAnimationInterval?: any;\r\n  isLogoAnimating: boolean = false;\r\n\r\n  // Language management\r\n  currentLanguage: string = 'en';\r\n\r\n  // Events for parent components to handle\r\n  @Output() navigationEvent = new EventEmitter<string>();\r\n  @Output() dropdownItemSelected = new EventEmitter<{\r\n    route: string;\r\n    label: string;\r\n  }>();\r\n  @Output() profileAction = new EventEmitter<string>();\r\n  @Output() themeToggle = new EventEmitter<'light' | 'dark'>();\r\n  @Output() languageChange = new EventEmitter<string>();\r\n  @Output() orgConfigChange = new EventEmitter<any>();\r\n\r\n  // Organization selector state (if enabled)\r\n  isOrgDialogOpen: boolean = false;\r\n\r\n  // App drawer state\r\n  isAppDrawerOpen: boolean = false;\r\n\r\n  // Organization dropdown state\r\n  orgOptions: DropdownOption[] = [];\r\n  domainOptions: DropdownOption[] = [];\r\n  projectOptions: DropdownOption[] = [];\r\n  teamOptions: DropdownOption[] = [];\r\n\r\n  // Selected values (IDs for form)\r\n  selectedOrg: string = '';\r\n  selectedDomain: string = '';\r\n  selectedProject: string = '';\r\n  selectedTeam: string = '';\r\n\r\n  // Selected names (for dropdown pre-selection)\r\n  selectedOrgName: string = '';\r\n  selectedDomainName: string = '';\r\n  selectedProjectName: string = '';\r\n  selectedTeamName: string = '';\r\n\r\n  // Form for organization config\r\n  headerConfigForm!: FormGroup;\r\n\r\n  // Store the hierarchy data from API\r\n  private hierarchyData: Organization[] = [];\r\n\r\n  @ViewChild('orgPathTrigger', { static: false }) orgPathTrigger!: ElementRef;\r\n  @ViewChild('popover', { static: false }) popoverRef!: ElementRef;\r\n  popoverAlign: 'left' | 'right' = 'left';\r\n\r\n  // Dropdown portal state\r\n  dropdownPortal: {\r\n    open: boolean;\r\n    rect: DOMRect | null;\r\n    items: SharedDropdownItem[];\r\n    parentLabel: string;\r\n    navItemId: string;\r\n  } = {\r\n    open: false,\r\n    rect: null,\r\n    items: [],\r\n    parentLabel: '',\r\n    navItemId: '',\r\n  };\r\n\r\n  // Subscription management\r\n  private subscriptions = new Subscription();\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private cdr: ChangeDetectorRef,\r\n    private renderer: Renderer2,\r\n    private elementRef: ElementRef,\r\n    private tokenStorage: TokenStorageService,\r\n    private authService: AuthService,\r\n    private formBuilder: FormBuilder,\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.initializeHeader();\r\n    this.setupRouterSubscription();\r\n    this.setupThemeSubscription();\r\n    this.loadUserInfo();\r\n    this.initializeForm();\r\n    this.initializeLogo();\r\n\r\n    // Initialize organization selector if enabled\r\n    if (this.config.showOrgSelector) {\r\n      this.initOrgPathFromCookie();\r\n    }\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    this.updateActiveMenuItemByRoute(this.router.url);\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.subscriptions.unsubscribe();\r\n    if (this.logoAnimationInterval) {\r\n      clearInterval(this.logoAnimationInterval);\r\n    }\r\n  }\r\n\r\n  // ========================================\r\n  // LOGO ANIMATION METHODS\r\n  // ========================================\r\n\r\n  private initializeLogo(): void {\r\n    // Use config logos if provided, otherwise use defaults\r\n    if (this.config.studioLogos && this.config.studioLogos.length > 0) {\r\n      this.studioLogos = this.config.studioLogos;\r\n      console.log('🎨 Using custom studio logos:', this.studioLogos);\r\n    } else {\r\n      console.log('🎨 Using default studio logos:', this.studioLogos);\r\n    }\r\n    \r\n    if (this.config.studioNames && this.config.studioNames.length > 0) {\r\n      this.studioNames = this.config.studioNames;\r\n    }\r\n    \r\n    // Set initial logo - use animated logos when animation is enabled\r\n    if (this.config.enableLogoAnimation !== false && this.studioLogos.length > 0) {\r\n      // Use first logo from animated array\r\n      this.currentLogo = this.studioLogos[0];\r\n      this.currentStudioName = this.studioNames[0];\r\n      console.log('🎨 Initial animated logo set to:', this.currentLogo);\r\n      console.log('🎨 Initial studio name set to:', this.currentStudioName);\r\n    } else {\r\n      // Fallback to config logoSrc when animation is disabled\r\n      this.currentLogo = this.config.logoSrc;\r\n      this.currentStudioName = this.config.projectName || 'Studio';\r\n      console.log('🎨 Using static logo:', this.currentLogo);\r\n    }\r\n    \r\n    // Start animation cycle only if enabled (default: true)\r\n    if (this.config.enableLogoAnimation !== false) {\r\n      this.startLogoAnimation();\r\n    }\r\n  }\r\n\r\n  private startLogoAnimation(): void {\r\n    // Clear any existing interval\r\n    if (this.logoAnimationInterval) {\r\n      clearInterval(this.logoAnimationInterval);\r\n    }\r\n\r\n    // Use config interval or default to 3 seconds\r\n    const interval = this.config.logoAnimationInterval || 3000;\r\n\r\n    // Start the animation cycle\r\n    this.logoAnimationInterval = setInterval(() => {\r\n      this.animateToNextLogo();\r\n    }, interval);\r\n  }\r\n\r\n  private animateToNextLogo(): void {\r\n    if (this.isLogoAnimating) return;\r\n\r\n    this.isLogoAnimating = true;\r\n\r\n    // Get animation style from config (default: 'rotate')\r\n    const animationStyle = this.config.logoAnimationStyle || 'rotate';\r\n\r\n    // Add rotation animation class\r\n    const logoElement = document.querySelector('.animated-logo');\r\n    if (logoElement) {\r\n      logoElement.classList.add('logo-transitioning');\r\n\r\n      // Apply specific animation style\r\n      logoElement.classList.add(`logo-${animationStyle}`);\r\n    }\r\n\r\n    // Determine timing based on animation style\r\n    const timings = {\r\n      rotate: { changeAt: 400, duration: 800 },\r\n      'fade-rotate': { changeAt: 300, duration: 600 },\r\n      flip: { changeAt: 200, duration: 400 },\r\n    };\r\n\r\n    const timing = timings[animationStyle] || timings['rotate'];\r\n\r\n    // Change the image source at the optimal point in the animation\r\n    setTimeout(() => {\r\n      // Move to next logo index\r\n      this.currentLogoIndex =\r\n        (this.currentLogoIndex + 1) % this.studioLogos.length;\r\n      this.currentLogo = this.studioLogos[this.currentLogoIndex];\r\n      this.currentStudioName = this.studioNames[this.currentLogoIndex];\r\n\r\n      // Trigger change detection\r\n      this.cdr.detectChanges();\r\n    }, timing.changeAt);\r\n\r\n    // Remove animation classes after animation completes\r\n    setTimeout(() => {\r\n      if (logoElement) {\r\n        logoElement.classList.remove('logo-transitioning');\r\n        logoElement.classList.remove(`logo-${animationStyle}`);\r\n      }\r\n      this.isLogoAnimating = false;\r\n    }, timing.duration);\r\n  }\r\n\r\n  public pauseLogoAnimation(): void {\r\n    if (this.logoAnimationInterval) {\r\n      clearInterval(this.logoAnimationInterval);\r\n      this.logoAnimationInterval = undefined;\r\n    }\r\n  }\r\n\r\n  public resumeLogoAnimation(): void {\r\n    if (!this.logoAnimationInterval) {\r\n      this.startLogoAnimation();\r\n    }\r\n  }\r\n\r\n  private initializeHeader(): void {\r\n    if (!this.config) {\r\n      console.warn('SharedAppHeaderComponent: config is required');\r\n      return;\r\n    }\r\n\r\n    // Set default values\r\n    this.config.showOrgSelector = this.config.showOrgSelector ?? false;\r\n    this.config.showThemeToggle = this.config.showThemeToggle ?? true;\r\n    this.config.showAppDrawer = this.config.showAppDrawer ?? false;\r\n    this.config.showProfileDropdown = this.config.showProfileDropdown ?? true;\r\n    this.config.projectName = this.config.projectName ?? 'Application';\r\n    this.config.redirectUrl = this.config.redirectUrl ?? '/';\r\n    this.config.currentApp = this.config.currentApp ?? '';\r\n\r\n    // Default studio apps\r\n    this.config.availableApps = this.config.availableApps ?? [\r\n      {\r\n        name: 'Console',\r\n        route: '/console',\r\n        icon: 'assets/svgs/studios/console-icon.svg',\r\n        description: 'Agent & Workflow Management',\r\n      },\r\n      {\r\n        name: 'Experience Studio',\r\n        route: '/experience-studio',\r\n        icon: 'assets/svgs/studios/experience-studio-icon.svg',\r\n        description: 'UI/UX Design & Prototyping',\r\n      },\r\n      {\r\n        name: 'Product Studio',\r\n        route: '/product-studio',\r\n        icon: 'assets/svgs/studios/product-studio-icon.svg',\r\n        description: 'Product Development',\r\n      },\r\n      {\r\n        name: 'Launchpad',\r\n        route: '/launchpad',\r\n        icon: 'assets/svgs/studios/launchpad-icon.svg',\r\n        description: 'Project Launch Hub',\r\n      },\r\n    ];\r\n\r\n    // Profile dropdown defaults\r\n    this.config.showThemeToggleInProfile =\r\n      this.config.showThemeToggleInProfile ?? true;\r\n    this.config.showLanguageSwitcher =\r\n      this.config.showLanguageSwitcher ?? false;\r\n    this.config.availableLanguages = this.config.availableLanguages ?? [\r\n      { code: 'en', name: 'English' },\r\n      { code: 'fil', name: 'Filipino' },\r\n      { code: 'es', name: 'Español' },\r\n    ];\r\n  }\r\n\r\n  private initializeForm(): void {\r\n    this.headerConfigForm = this.formBuilder.group({\r\n      org: ['', Validators.required],\r\n      domain: ['', Validators.required],\r\n      project: ['', Validators.required],\r\n      team: ['', Validators.required],\r\n    });\r\n  }\r\n\r\n  private setupRouterSubscription(): void {\r\n    const routerSub = this.router.events\r\n      .pipe(filter((event) => event instanceof NavigationEnd))\r\n      .subscribe((event: NavigationEnd) => {\r\n        // Check if current route is a login page\r\n        this.isLoginPage = this.checkIfLoginPage(event.url);\r\n        this.updateActiveMenuItemByRoute(event.url);\r\n      });\r\n    this.subscriptions.add(routerSub);\r\n\r\n    // Check initial route\r\n    this.isLoginPage = this.checkIfLoginPage(this.router.url);\r\n  }\r\n\r\n  private checkIfLoginPage(url: string): boolean {\r\n    // Check for login routes across different studios\r\n    const loginRoutes = [\r\n      '/login',\r\n      '/auth/login',\r\n      '/experience/login',\r\n      '/product/login',\r\n      '/console/login',\r\n    ];\r\n    return loginRoutes.some((route) => url.includes(route));\r\n  }\r\n\r\n  private setupThemeSubscription(): void {\r\n    // If theme service is provided, subscribe to theme changes\r\n    if (this.themeService && this.themeService.themeObservable) {\r\n      this.subscriptions.add(\r\n        this.themeService.themeObservable.subscribe(\r\n          (theme: 'light' | 'dark') => {\r\n            this.currentTheme = theme;\r\n            this.updateThemeAssets();\r\n            this.cdr.markForCheck();\r\n          },\r\n        ),\r\n      );\r\n    }\r\n    this.updateThemeAssets();\r\n  }\r\n\r\n  private loadUserInfo(): void {\r\n    this.userName = this.tokenStorage.getDaUsername() || 'User';\r\n    this.userEmail = (this.tokenStorage as any).getDaUserEmail?.() || ''; // Safe call if method exists\r\n    this.userDesignation =\r\n      (this.tokenStorage as any).getDaUserDesignation?.() || 'Employee'; // Safe call if method exists\r\n    this.generateUserAvatar();\r\n  }\r\n\r\n  private generateUserAvatar(): void {\r\n    // Generate avatar from user initials if no profile picture is available\r\n    if (this.userName) {\r\n      const nameParts = this.userName.trim().split(' ');\r\n      let initials = '';\r\n\r\n      if (nameParts.length >= 2) {\r\n        // First letter of first name and first letter of last name\r\n        initials = nameParts[0][0] + nameParts[nameParts.length - 1][0];\r\n      } else if (nameParts.length === 1) {\r\n        // Just first letter if only one name\r\n        initials = nameParts[0][0];\r\n      } else {\r\n        initials = 'U'; // Default to 'U' for User\r\n      }\r\n\r\n      initials = initials.toUpperCase();\r\n\r\n      // Generate a colored avatar with initials\r\n      const colors = [\r\n        '#8B5CF6',\r\n        '#06B6D4',\r\n        '#10B981',\r\n        '#F59E0B',\r\n        '#EF4444',\r\n        '#8B5A2B',\r\n        '#6366F1',\r\n        '#EC4899',\r\n      ];\r\n      const colorIndex = this.userName.length % colors.length;\r\n      const backgroundColor = colors[colorIndex];\r\n\r\n      this.userAvatar = `data:image/svg+xml;base64,${btoa(`\r\n        <svg width=\"40\" height=\"40\" xmlns=\"http://www.w3.org/2000/svg\">\r\n          <circle cx=\"20\" cy=\"20\" r=\"20\" fill=\"${backgroundColor}\"/>\r\n          <text x=\"20\" y=\"26\" font-family=\"Inter, Arial, sans-serif\" font-size=\"14\" font-weight=\"600\" fill=\"white\" text-anchor=\"middle\">${initials}</text>\r\n        </svg>\r\n      `)}`;\r\n    }\r\n  }\r\n\r\n  private updateThemeAssets(): void {\r\n    // Update theme-specific assets\r\n    this.themeMenuIcon =\r\n      this.currentTheme === 'light'\r\n        ? 'assets/svgs/header/toggle-theme/toggle-to-dark.svg'\r\n        : 'assets/svgs/header/toggle-theme/toggle-to-light.svg';\r\n  }\r\n\r\n  // Organization selector methods\r\n  initOrgPathFromCookie(): void {\r\n    const path = this.tokenStorage.getCookie('org_path');\r\n    if (path) {\r\n      const parts = path.split('::');\r\n      const usecasePath = parts[0] || '';\r\n      const usecaseIdPath = parts[1] || '';\r\n\r\n      // Parse the IDs\r\n      const ids = usecaseIdPath.split('@').map(Number);\r\n\r\n      // Set form values (IDs)\r\n      this.headerConfigForm.patchValue({\r\n        org: ids[0]?.toString() || '',\r\n        domain: ids[1]?.toString() || '',\r\n        project: ids[2]?.toString() || '',\r\n        team: ids[3]?.toString() || '',\r\n      });\r\n\r\n      // Store the IDs for form and the names for dropdown pre-selection\r\n      this.selectedOrg = ids[0]?.toString() || '';\r\n      this.selectedDomain = ids[1]?.toString() || '';\r\n      this.selectedProject = ids[2]?.toString() || '';\r\n      this.selectedTeam = ids[3]?.toString() || '';\r\n\r\n      // Store the names for dropdown pre-selection\r\n      const pathParts = usecasePath.split('@');\r\n      this.selectedOrgName = pathParts[0] || '';\r\n      this.selectedDomainName = pathParts[1] || '';\r\n      this.selectedProjectName = pathParts[2] || '';\r\n      this.selectedTeamName = pathParts[3] || '';\r\n\r\n      // Load dropdown options\r\n      this.loadData();\r\n    } else {\r\n      this.loadData();\r\n    }\r\n  }\r\n\r\n  loadData(): void {\r\n    if (this.orgConfigService) {\r\n      this.orgConfigService.getOrganizationHierarchy().subscribe({\r\n        next: (data: Organization[]) => {\r\n          this.hierarchyData = data;\r\n          this.loadOrganizations();\r\n\r\n          // After loading organizations, load cascading dropdowns if we have pre-selected values\r\n          if (this.selectedOrg) {\r\n            this.loadDomains(this.selectedOrg);\r\n            if (this.selectedDomain) {\r\n              this.loadProjects(this.selectedDomain);\r\n              if (this.selectedProject) {\r\n                this.loadTeams(this.selectedProject);\r\n              }\r\n            }\r\n          }\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error loading organization data:', error);\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  loadOrganizations(): void {\r\n    this.orgOptions = this.hierarchyData.map((org) => ({\r\n      name: org.organizationName,\r\n      value: org.orgId.toString(),\r\n    }));\r\n  }\r\n\r\n  onOrgSelect(event: any): void {\r\n    const selectedOrgId = event.selectedOptions?.[0]?.value;\r\n    const selectedOrgName = event.selectedOptions?.[0]?.name;\r\n    if (selectedOrgId) {\r\n      this.selectedOrg = selectedOrgId;\r\n      this.selectedOrgName = selectedOrgName;\r\n      this.headerConfigForm.patchValue({ org: selectedOrgId });\r\n      this.loadDomains(selectedOrgId);\r\n      // Clear dependent dropdowns\r\n      this.headerConfigForm.patchValue({ domain: '', project: '', team: '' });\r\n      this.selectedDomain = '';\r\n      this.selectedProject = '';\r\n      this.selectedTeam = '';\r\n      this.selectedDomainName = '';\r\n      this.selectedProjectName = '';\r\n      this.selectedTeamName = '';\r\n      this.projectOptions = [];\r\n      this.teamOptions = [];\r\n    }\r\n  }\r\n\r\n  loadDomains(orgId: string): void {\r\n    const org = this.hierarchyData.find((o) => o.orgId.toString() === orgId);\r\n    if (org) {\r\n      this.domainOptions = org.domains.map((domain) => ({\r\n        name: domain.domainName,\r\n        value: domain.domainId.toString(),\r\n      }));\r\n    } else {\r\n      this.domainOptions = [];\r\n    }\r\n  }\r\n\r\n  onDomainSelect(event: any): void {\r\n    const selectedDomainId = event.selectedOptions?.[0]?.value;\r\n    const selectedDomainName = event.selectedOptions?.[0]?.name;\r\n    if (selectedDomainId) {\r\n      this.selectedDomain = selectedDomainId;\r\n      this.selectedDomainName = selectedDomainName;\r\n      this.headerConfigForm.patchValue({ domain: selectedDomainId });\r\n      this.loadProjects(selectedDomainId);\r\n      // Clear dependent dropdowns\r\n      this.headerConfigForm.patchValue({ project: '', team: '' });\r\n      this.selectedProject = '';\r\n      this.selectedTeam = '';\r\n      this.selectedProjectName = '';\r\n      this.selectedTeamName = '';\r\n      this.teamOptions = [];\r\n    }\r\n  }\r\n\r\n  loadProjects(domainId: string): void {\r\n    const org = this.hierarchyData.find((o) =>\r\n      o.domains.some((d) => d.domainId.toString() === domainId),\r\n    );\r\n    if (org) {\r\n      const domain = org.domains.find(\r\n        (d) => d.domainId.toString() === domainId,\r\n      );\r\n      if (domain) {\r\n        this.projectOptions = domain.projects.map((project) => ({\r\n          name: project.projectName,\r\n          value: project.projectId.toString(),\r\n        }));\r\n      } else {\r\n        this.projectOptions = [];\r\n      }\r\n    } else {\r\n      this.projectOptions = [];\r\n    }\r\n  }\r\n\r\n  onProjectSelect(event: any): void {\r\n    const selectedProjectId = event.selectedOptions?.[0]?.value;\r\n    const selectedProjectName = event.selectedOptions?.[0]?.name;\r\n    if (selectedProjectId) {\r\n      this.selectedProject = selectedProjectId;\r\n      this.selectedProjectName = selectedProjectName;\r\n      this.headerConfigForm.patchValue({ project: selectedProjectId });\r\n      this.loadTeams(selectedProjectId);\r\n      // Clear dependent dropdowns\r\n      this.headerConfigForm.patchValue({ team: '' });\r\n      this.selectedTeam = '';\r\n      this.selectedTeamName = '';\r\n    }\r\n  }\r\n\r\n  loadTeams(projectId: string): void {\r\n    const org = this.hierarchyData.find((o) =>\r\n      o.domains.some((d) =>\r\n        d.projects.some((p) => p.projectId.toString() === projectId),\r\n      ),\r\n    );\r\n    if (org) {\r\n      const domain = org.domains.find((d) =>\r\n        d.projects.some((p) => p.projectId.toString() === projectId),\r\n      );\r\n      if (domain) {\r\n        const project = domain.projects.find(\r\n          (p) => p.projectId.toString() === projectId,\r\n        );\r\n        if (project) {\r\n          this.teamOptions = project.teams.map((team) => ({\r\n            name: team.teamName,\r\n            value: team.teamId.toString(),\r\n          }));\r\n        } else {\r\n          this.teamOptions = [];\r\n        }\r\n      } else {\r\n        this.teamOptions = [];\r\n      }\r\n    } else {\r\n      this.teamOptions = [];\r\n    }\r\n  }\r\n\r\n  onTeamSelect(event: any): void {\r\n    const selectedTeamId = event.selectedOptions?.[0]?.value;\r\n    const selectedTeamName = event.selectedOptions?.[0]?.name;\r\n    if (selectedTeamId) {\r\n      this.selectedTeam = selectedTeamId;\r\n      this.selectedTeamName = selectedTeamName;\r\n      this.headerConfigForm.patchValue({ team: selectedTeamId });\r\n    }\r\n  }\r\n\r\n  get orgLabel(): string {\r\n    // Try to get the org name from the org_path cookie\r\n    const orgPath = this.tokenStorage.getCookie('org_path');\r\n    if (orgPath) {\r\n      const orgName = orgPath.split('::')[0].split('@')[0];\r\n      if (orgName) return orgName;\r\n    }\r\n    // Fallback to dropdown label\r\n    return (\r\n      this.orgOptions.find((o) => o.value === this.selectedOrg)?.name ||\r\n      'Select Organization'\r\n    );\r\n  }\r\n\r\n  saveOrgPathAndClose(): void {\r\n    if (this.headerConfigForm.valid) {\r\n      const formValue = this.headerConfigForm.value;\r\n\r\n      // Build the org path string\r\n      const orgPath = `${this.selectedOrgName}@${this.selectedDomainName}@${this.selectedProjectName}@${this.selectedTeamName}::${formValue.org}@${formValue.domain}@${formValue.project}@${formValue.team}`;\r\n\r\n      // Save to cookie\r\n      this.tokenStorage.setCookie('org_path', orgPath);\r\n\r\n      // Emit the change event\r\n      this.orgConfigChange.emit({\r\n        orgPath,\r\n        selectedValues: {\r\n          org: this.selectedOrg,\r\n          domain: this.selectedDomain,\r\n          project: this.selectedProject,\r\n          team: this.selectedTeam,\r\n        },\r\n        selectedNames: {\r\n          org: this.selectedOrgName,\r\n          domain: this.selectedDomainName,\r\n          project: this.selectedProjectName,\r\n          team: this.selectedTeamName,\r\n        },\r\n      });\r\n\r\n      this.closeOrgDialog();\r\n    }\r\n  }\r\n\r\n  // Navigation methods\r\n  navigateTo(route: string): void {\r\n    this.router.navigate([route]);\r\n    this.navigationEvent.emit(route);\r\n  }\r\n\r\n  selectMenuItem(index: number): void {\r\n    this.config.navItems.forEach((item, i) => {\r\n      item.selected = i === index;\r\n    });\r\n  }\r\n\r\n  toggleDropdown(index: number): void {\r\n    this.config.navItems[index].dropdownOpen =\r\n      !this.config.navItems[index].dropdownOpen;\r\n  }\r\n\r\n  onDropdownItemSelected(\r\n    event: { route: string; label: string },\r\n    parentIndex: number,\r\n  ): void {\r\n    this.navigateTo(event.route);\r\n    this.dropdownItemSelected.emit(event);\r\n    this.selectMenuItem(parentIndex);\r\n    this.closeDropdownPortal();\r\n  }\r\n\r\n  onDropdownPortalOpen(event: {\r\n    rect: DOMRect;\r\n    items: SharedDropdownItem[];\r\n    parentLabel: string;\r\n    navItemId: string;\r\n  }): void {\r\n    this.dropdownPortal = {\r\n      open: true,\r\n      rect: event.rect,\r\n      items: event.items,\r\n      parentLabel: event.parentLabel,\r\n      navItemId: event.navItemId,\r\n    };\r\n  }\r\n\r\n  closeDropdownPortal(): void {\r\n    this.dropdownPortal.open = false;\r\n    this.config.navItems.forEach((item) => (item.dropdownOpen = false));\r\n  }\r\n\r\n  // Profile dropdown methods\r\n  toggleProfileDropdown(): void {\r\n    this.profileDropdownOpen = !this.profileDropdownOpen;\r\n  }\r\n\r\n  logout(): void {\r\n    this.authService.logout();\r\n    this.profileAction.emit('logout');\r\n  }\r\n\r\n  // Theme methods\r\n  toggleTheme(): void {\r\n    if (this.themeService && this.themeService.toggleTheme) {\r\n      // Use the injected theme service if available\r\n      this.themeService.toggleTheme();\r\n    } else {\r\n      // Fallback to local theme toggle\r\n      const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';\r\n      this.currentTheme = newTheme;\r\n      this.updateThemeAssets();\r\n      this.themeToggle.emit(newTheme);\r\n    }\r\n  }\r\n\r\n  // Language methods\r\n  switchLanguage(languageCode: string): void {\r\n    this.currentLanguage = languageCode;\r\n    this.languageChange.emit(languageCode);\r\n  }\r\n\r\n  getCurrentLanguageName(): string {\r\n    const language = this.config.availableLanguages?.find(\r\n      (lang) => lang.code === this.currentLanguage,\r\n    );\r\n    return language?.name || 'English';\r\n  }\r\n\r\n  // App drawer methods\r\n  toggleAppDrawer(): void {\r\n    this.isAppDrawerOpen = !this.isAppDrawerOpen;\r\n\r\n    if (this.isAppDrawerOpen) {\r\n      // Calculate position to prevent overflow\r\n      setTimeout(() => {\r\n        this.calculateAppDrawerPosition();\r\n      });\r\n    }\r\n  }\r\n\r\n  closeAppDrawer(): void {\r\n    this.isAppDrawerOpen = false;\r\n  }\r\n\r\n  navigateToApp(app: StudioApp): void {\r\n    if (app.route.startsWith('http')) {\r\n      // External URL\r\n      window.open(app.route, '_blank');\r\n    } else {\r\n      // Internal route\r\n      this.router.navigate([app.route]);\r\n    }\r\n    this.closeAppDrawer();\r\n    this.navigationEvent.emit(app.route);\r\n  }\r\n\r\n  getFilteredApps(): StudioApp[] {\r\n    if (!this.config.availableApps) return [];\r\n\r\n    // Filter out the current app\r\n    return this.config.availableApps.filter(\r\n      (app) => app.name !== this.config.currentApp,\r\n    );\r\n  }\r\n\r\n  // Organization selector methods (if enabled)\r\n  toggleOrgDialog(): void {\r\n    if (this.config.showOrgSelector) {\r\n      this.isOrgDialogOpen = !this.isOrgDialogOpen;\r\n\r\n      if (this.isOrgDialogOpen) {\r\n        // Calculate popover position\r\n        setTimeout(() => {\r\n          this.calculatePopoverPosition();\r\n        });\r\n      }\r\n    }\r\n  }\r\n\r\n  closeOrgDialog(): void {\r\n    this.isOrgDialogOpen = false;\r\n  }\r\n\r\n  private calculatePopoverPosition(): void {\r\n    if (this.orgPathTrigger && this.popoverRef) {\r\n      const triggerRect =\r\n        this.orgPathTrigger.nativeElement.getBoundingClientRect();\r\n      const popoverRect = this.popoverRef.nativeElement.getBoundingClientRect();\r\n      const viewportWidth = window.innerWidth;\r\n\r\n      // Check if popover would overflow on the right\r\n      if (triggerRect.left + popoverRect.width > viewportWidth - 20) {\r\n        this.popoverAlign = 'right';\r\n      } else {\r\n        this.popoverAlign = 'left';\r\n      }\r\n    }\r\n  }\r\n\r\n  private calculateAppDrawerPosition(): void {\r\n    // Find the app drawer dropdown element\r\n    const appDrawerDropdown = this.elementRef.nativeElement.querySelector(\r\n      '.app-drawer-dropdown',\r\n    );\r\n    if (appDrawerDropdown) {\r\n      const dropdownRect = appDrawerDropdown.getBoundingClientRect();\r\n      const viewportWidth = window.innerWidth;\r\n\r\n      // If dropdown would overflow on the right, adjust position\r\n      if (dropdownRect.right > viewportWidth - 20) {\r\n        const overflow = dropdownRect.right - viewportWidth + 20;\r\n        appDrawerDropdown.style.right = `${overflow}px`;\r\n      } else {\r\n        appDrawerDropdown.style.right = '0px';\r\n      }\r\n    }\r\n  }\r\n\r\n  // Navigation item click handler\r\n  onNavItemClick(item: SharedNavItem, index: number, event: MouseEvent): void {\r\n    if (item.disabled) {\r\n      event.preventDefault();\r\n      event.stopPropagation();\r\n      return;\r\n    }\r\n\r\n    if (item.hasDropdown && item.dropdownItems) {\r\n      event.stopPropagation();\r\n      const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();\r\n      this.onDropdownPortalOpen({\r\n        rect,\r\n        items: item.dropdownItems,\r\n        parentLabel: item.label,\r\n        navItemId: item.label,\r\n      });\r\n      this.toggleDropdown(index);\r\n    } else {\r\n      this.navigateTo(item.route);\r\n      this.selectMenuItem(index);\r\n    }\r\n  }\r\n\r\n  // Route-based active menu item update\r\n  updateActiveMenuItemByRoute(url: string): void {\r\n    // Reset all selections\r\n    this.config.navItems.forEach((item) => {\r\n      item.selected = false;\r\n    });\r\n\r\n    // Find the matching parent route or parent of a child route\r\n    const parentItem = this.config.navItems.find((item) => {\r\n      // Check if this is a direct match for the parent route\r\n      if (url === item.route) {\r\n        return true;\r\n      }\r\n\r\n      // Check if this is a dropdown parent with a matching child\r\n      if (item.hasDropdown && item.dropdownItems) {\r\n        // Check if the URL starts with the parent route path (for nested routes)\r\n        // OR if any child route exactly matches the URL\r\n        return (\r\n          url.startsWith(item.route + '/') ||\r\n          item.dropdownItems.some((child) => url === child.route)\r\n        );\r\n      }\r\n\r\n      // Even if hasDropdown is false, check for dropdownItems\r\n      if (!item.hasDropdown && item.dropdownItems) {\r\n        return item.dropdownItems.some((child) => url === child.route);\r\n      }\r\n\r\n      return false;\r\n    });\r\n\r\n    if (parentItem) {\r\n      parentItem.selected = true;\r\n    } else {\r\n      // Default to first non-disabled item if no match found\r\n      const defaultItem = this.config.navItems.find((item) => !item.disabled);\r\n      if (defaultItem) {\r\n        defaultItem.selected = true;\r\n      }\r\n    }\r\n  }\r\n\r\n  // Document click listener to close dropdowns\r\n  @HostListener('document:click', ['$event'])\r\n  onDocumentClick(event: MouseEvent): void {\r\n    const target = event.target as HTMLElement;\r\n\r\n    // Close profile dropdown if clicking outside\r\n    if (\r\n      this.profileDropdownOpen &&\r\n      !this.elementRef.nativeElement.contains(target)\r\n    ) {\r\n      this.profileDropdownOpen = false;\r\n    }\r\n\r\n    // Close org dialog if clicking outside\r\n    if (\r\n      this.isOrgDialogOpen &&\r\n      !target.closest('.org-path-dropdown-container')\r\n    ) {\r\n      this.closeOrgDialog();\r\n    }\r\n\r\n    // Close app drawer if clicking outside\r\n    if (this.isAppDrawerOpen && !target.closest('.app-drawer-container')) {\r\n      this.closeAppDrawer();\r\n    }\r\n\r\n    // Close dropdown portal if clicking outside\r\n    if (\r\n      this.dropdownPortal.open &&\r\n      !target.closest('.dropdown-portal-menu') &&\r\n      !target.closest('.nav-item-wrapper')\r\n    ) {\r\n      this.closeDropdownPortal();\r\n    }\r\n  }\r\n}\r\n", "<!-- Hide header on login pages -->\r\n<div *ngIf=\"!isLoginPage\">\r\n  <!-- SVG Clip Path Definition for Header Design -->\r\n  <svg width=\"0\" height=\"0\" style=\"position: absolute\">\r\n    <defs>\r\n      <clipPath id=\"headerClip\" clipPathUnits=\"objectBoundingBox\">\r\n        <path\r\n          d=\"\r\n          M 0.03,0 \r\n          L 0.97,0 \r\n          L 0.95,0.71 \r\n          Q 0.939,1    0.91,1 \r\n          L 0.09,1 \r\n          Q 0.061,1    0.05,0.69 \r\n          Z\"\r\n        />\r\n      </clipPath>\r\n    </defs>\r\n  </svg>\r\n\r\n  <awe-header theme=\"light\">\r\n    <div left-content>\r\n      <div class=\"animated-logo-container\" \r\n           [attr.data-studio]=\"currentStudioName\"\r\n           [attr.data-index]=\"currentLogoIndex\"\r\n           (mouseenter)=\"pauseLogoAnimation()\" \r\n           (mouseleave)=\"resumeLogoAnimation()\">\r\n        <img [src]=\"currentLogo\" \r\n             class=\"header-logo animated-logo\" \r\n             [class.logo-transitioning]=\"isLogoAnimating\"\r\n             [alt]=\"currentStudioName + ' Logo'\" />\r\n        \r\n        <!-- Optional: Studio indicator dots -->\r\n        <div class=\"studio-indicators\" *ngIf=\"studioLogos.length > 1\">\r\n          <div *ngFor=\"let logo of studioLogos; let i = index\" \r\n               class=\"studio-dot\" \r\n               [class.active]=\"i === currentLogoIndex\">\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div center-content>\r\n      <div class=\"header-wrapper\">\r\n        <div class=\"header-shadow\"></div>\r\n        <div class=\"nav-menu\">\r\n          <div class=\"nav-items\">\r\n            <!-- Navigation items -->\r\n            <shared-nav-item\r\n              *ngFor=\"let item of config.navItems; let i = index\"\r\n              [label]=\"item.label\"\r\n              [route]=\"item.route\"\r\n              [selected]=\"item.selected\"\r\n              [hasDropdown]=\"item.hasDropdown\"\r\n              [dropdownOpen]=\"item.dropdownOpen || false\"\r\n              [dropdownItems]=\"item.dropdownItems || []\"\r\n              [icon]=\"item.icon\"\r\n              [disabled]=\"item.disabled || false\"\r\n              (toggleDropdownEvent)=\"toggleDropdown(i)\"\r\n              (navigateEvent)=\"navigateTo($event)\"\r\n              (selectEvent)=\"selectMenuItem(i)\"\r\n              (dropdownItemSelected)=\"onDropdownItemSelected($event, i)\"\r\n              (dropdownPortalOpen)=\"onDropdownPortalOpen($event)\"\r\n              class=\"nav-item-wrapper\"\r\n            >\r\n            </shared-nav-item>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div right-content class=\"user-info-container\">\r\n      <!-- Organization Selector (if enabled) -->\r\n      <div *ngIf=\"config.showOrgSelector\" class=\"org-path-dropdown-container\">\r\n        <div\r\n          class=\"org-path-trigger\"\r\n          (click)=\"toggleOrgDialog()\"\r\n          #orgPathTrigger\r\n        >\r\n          <span class=\"org-icon\">\r\n            <img\r\n              src=\"assets/svgs/ascendion-logo/header-ascendion-logo.svg\"\r\n              alt=\"Organization Logo\"\r\n              width=\"40\"\r\n              height=\"40\"\r\n            />\r\n          </span>\r\n          <span class=\"org-label-text\">\r\n            <span>{{ orgLabel }}</span>\r\n          </span>\r\n          <span class=\"org-dropdown-arrow\" [class.open]=\"isOrgDialogOpen\">\r\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 12 12\" fill=\"none\">\r\n              <path\r\n                d=\"M2.5 4L6 7.5L9.5 4\"\r\n                stroke=\"currentColor\"\r\n                stroke-width=\"1.5\"\r\n                stroke-linecap=\"round\"\r\n                stroke-linejoin=\"round\"\r\n              />\r\n            </svg>\r\n          </span>\r\n        </div>\r\n\r\n        <!-- Organization Configuration Dialog -->\r\n        <div\r\n          *ngIf=\"isOrgDialogOpen\"\r\n          class=\"org-path-popover\"\r\n          #popover\r\n          [ngClass]=\"popoverAlign\"\r\n        >\r\n          <form [formGroup]=\"headerConfigForm\">\r\n            <div class=\"filter-config-title\">Filter Configuration</div>\r\n            <div class=\"dropdown-row-vertical\">\r\n              <label class=\"filter-label required\">Choose Organization</label>\r\n              <ava-dropdown\r\n                [dropdownTitle]=\"'Select Organization'\"\r\n                [options]=\"orgOptions\"\r\n                [selectedValue]=\"selectedOrgName\"\r\n                [disabled]=\"false\"\r\n                (selectionChange)=\"onOrgSelect($event)\"\r\n                [search]=\"true\"\r\n                [enableSearch]=\"true\"\r\n              >\r\n              </ava-dropdown>\r\n\r\n              <label class=\"filter-label required\">Choose Domain</label>\r\n              <ava-dropdown\r\n                [dropdownTitle]=\"'Select Domain'\"\r\n                [options]=\"domainOptions\"\r\n                [selectedValue]=\"selectedDomainName\"\r\n                [disabled]=\"!selectedOrg\"\r\n                (selectionChange)=\"onDomainSelect($event)\"\r\n                [search]=\"true\"\r\n                [enableSearch]=\"true\"\r\n              >\r\n              </ava-dropdown>\r\n\r\n              <label class=\"filter-label required\">Choose Project</label>\r\n              <ava-dropdown\r\n                [dropdownTitle]=\"'Select Project'\"\r\n                [options]=\"projectOptions\"\r\n                [selectedValue]=\"selectedProjectName\"\r\n                [disabled]=\"!selectedDomain\"\r\n                (selectionChange)=\"onProjectSelect($event)\"\r\n                [search]=\"true\"\r\n                [enableSearch]=\"true\"\r\n              >\r\n              </ava-dropdown>\r\n\r\n              <label class=\"filter-label required\">Choose Team</label>\r\n              <ava-dropdown\r\n                [dropdownTitle]=\"'Select Team'\"\r\n                [options]=\"teamOptions\"\r\n                [selectedValue]=\"selectedTeamName\"\r\n                [disabled]=\"!selectedProject\"\r\n                (selectionChange)=\"onTeamSelect($event)\"\r\n                [search]=\"true\"\r\n                [enableSearch]=\"true\"\r\n              >\r\n              </ava-dropdown>\r\n            </div>\r\n            <div class=\"popover-actions\">\r\n              <ava-button\r\n                label=\"Cancel\"\r\n                variant=\"secondary\"\r\n                size=\"medium\"\r\n                (userClick)=\"closeOrgDialog()\"\r\n              >\r\n              </ava-button>\r\n              <ava-button\r\n                label=\"Apply\"\r\n                variant=\"primary\"\r\n                size=\"medium\"\r\n                [disabled]=\"!headerConfigForm.valid\"\r\n                (userClick)=\"saveOrgPathAndClose()\"\r\n              >\r\n              </ava-button>\r\n            </div>\r\n          </form>\r\n        </div>\r\n\r\n        <!-- Organization dialog backdrop -->\r\n        <div\r\n          *ngIf=\"isOrgDialogOpen\"\r\n          class=\"org-path-backdrop\"\r\n          (click)=\"closeOrgDialog()\"\r\n        ></div>\r\n      </div>\r\n\r\n      <!-- App Drawer (if enabled) -->\r\n      <div *ngIf=\"config.showAppDrawer\" class=\"app-drawer-container\">\r\n        <div\r\n          class=\"app-drawer-trigger\"\r\n          [class.active]=\"isAppDrawerOpen\"\r\n          (click)=\"toggleAppDrawer()\"\r\n        >\r\n         <ava-icon iconName=\"layout-grid\"></ava-icon>\r\n        </div>\r\n\r\n        <!-- App Drawer Backdrop -->\r\n        <div\r\n          *ngIf=\"isAppDrawerOpen\"\r\n          class=\"app-drawer-backdrop\"\r\n          (click)=\"closeAppDrawer()\"\r\n        ></div>\r\n\r\n        <!-- App Drawer Dropdown -->\r\n        <div class=\"app-drawer-dropdown\" [class.visible]=\"isAppDrawerOpen\">\r\n          <div class=\"app-drawer-content\">\r\n            <!-- <div class=\"app-drawer-header\">\r\n            <h3>Ascendion Studios</h3>\r\n          </div> -->\r\n            <div class=\"app-drawer-grid\">\r\n              <div\r\n                *ngFor=\"let app of getFilteredApps()\"\r\n                class=\"app-drawer-item\"\r\n                (click)=\"navigateToApp(app)\"\r\n              >\r\n                <div class=\"app-icon\">\r\n                  <img [src]=\"app.icon\" [alt]=\"app.name\" />\r\n                </div>\r\n                <div class=\"app-info\">\r\n                  <div class=\"app-name\">{{ app.name }}</div>\r\n                  <div class=\"app-description\" *ngIf=\"app.description\">\r\n                    {{ app.description }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Theme Toggle (if enabled and app drawer is disabled) -->\r\n      <div\r\n        *ngIf=\"config.showThemeToggle && !config.showAppDrawer\"\r\n        class=\"theme-toggle\"\r\n        (click)=\"toggleTheme()\"\r\n      >\r\n        <img [src]=\"themeMenuIcon\" alt=\"Toggle Theme\" width=\"24\" height=\"24\" />\r\n      </div>\r\n\r\n      <!-- Profile Dropdown -->\r\n      <div *ngIf=\"config.showProfileDropdown\" class=\"profile-container\">\r\n        <div\r\n          class=\"profile-trigger\"\r\n          [class.active]=\"profileDropdownOpen\"\r\n          (click)=\"toggleProfileDropdown()\"\r\n        >\r\n          <img [src]=\"userAvatar\" alt=\"User Profile\" class=\"profile-avatar\" />\r\n        </div>\r\n\r\n        <!-- Enhanced Profile Dropdown -->\r\n        <div class=\"profile-dropdown\" [class.visible]=\"profileDropdownOpen\">\r\n          <div class=\"profile-dropdown-content\">\r\n            <!-- User Info Section -->\r\n            <div class=\"profile-user-info\">\r\n              <div class=\"profile-avatar-large\">\r\n                <img [src]=\"userAvatar\" alt=\"User Profile\" />\r\n              </div>\r\n              <div class=\"profile-details\">\r\n                <div class=\"profile-name\">{{ userName }}</div>\r\n                <div class=\"profile-designation\" *ngIf=\"userDesignation\">\r\n                  {{ userDesignation }}\r\n                </div>\r\n                <div class=\"profile-email\" *ngIf=\"userEmail\">\r\n                  {{ userEmail }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"profile-divider\"></div>\r\n\r\n            <!-- Theme Toggle Section (if enabled) -->\r\n            <div\r\n              *ngIf=\"config.showThemeToggleInProfile\"\r\n              class=\"profile-section\"\r\n            >\r\n              <div class=\"profile-section-header\">\r\n                <span class=\"section-title\">Mode</span>\r\n              </div>\r\n              <div class=\"theme-toggle-container\">\r\n                <button\r\n                  class=\"theme-option\"\r\n                  [class.active]=\"currentTheme === 'light'\"\r\n                  (click)=\"currentTheme !== 'light' && toggleTheme()\"\r\n                >\r\n                  <svg\r\n                    width=\"16\"\r\n                    height=\"16\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    fill=\"none\"\r\n                    stroke=\"currentColor\"\r\n                    stroke-width=\"2\"\r\n                  >\r\n                    <circle cx=\"12\" cy=\"12\" r=\"5\" />\r\n                    <path\r\n                      d=\"M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42\"\r\n                    />\r\n                  </svg>\r\n                  Light\r\n                </button>\r\n                <button\r\n                  class=\"theme-option\"\r\n                  [class.active]=\"currentTheme === 'dark'\"\r\n                  (click)=\"currentTheme !== 'dark' && toggleTheme()\"\r\n                >\r\n                  <svg\r\n                    width=\"16\"\r\n                    height=\"16\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    fill=\"none\"\r\n                    stroke=\"currentColor\"\r\n                    stroke-width=\"2\"\r\n                  >\r\n                    <path d=\"M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z\" />\r\n                  </svg>\r\n                  Dark\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Language Switcher Section (if enabled) -->\r\n            <div *ngIf=\"config.showLanguageSwitcher\" class=\"profile-section\">\r\n              <div class=\"profile-section-header\">\r\n                <span class=\"section-title\">Language</span>\r\n              </div>\r\n              <div class=\"language-options\">\r\n                <button\r\n                  *ngFor=\"let language of config.availableLanguages\"\r\n                  class=\"language-option\"\r\n                  [class.active]=\"currentLanguage === language.code\"\r\n                  (click)=\"switchLanguage(language.code)\"\r\n                >\r\n                  {{ language.name }}\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"profile-divider\"></div>\r\n\r\n            <!-- Actions Section -->\r\n            <div class=\"profile-actions\">\r\n              <button class=\"profile-action-item logout-btn\" (click)=\"logout()\">\r\n                <svg\r\n                  width=\"16\"\r\n                  height=\"16\"\r\n                  viewBox=\"0 0 24 24\"\r\n                  fill=\"none\"\r\n                  stroke=\"currentColor\"\r\n                  stroke-width=\"2\"\r\n                >\r\n                  <path d=\"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4\" />\r\n                  <polyline points=\"16,17 21,12 16,7\" />\r\n                  <line x1=\"21\" y1=\"12\" x2=\"9\" y2=\"12\" />\r\n                </svg>\r\n                Sign Out\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </awe-header>\r\n</div>\r\n\r\n<!-- Dropdown Portal Container -->\r\n<div\r\n  *ngIf=\"dropdownPortal.open && dropdownPortal.rect\"\r\n  class=\"dropdown-portal-menu\"\r\n  [style.top]=\"dropdownPortal.rect.bottom + 4 + 'px'\"\r\n  [style.left]=\"dropdownPortal.rect.left + 'px'\"\r\n>\r\n  <div class=\"dropdown-menu\">\r\n    <div\r\n      *ngFor=\"let item of dropdownPortal.items\"\r\n      class=\"dropdown-item\"\r\n      (click)=\"\r\n        onDropdownItemSelected({ route: item.route, label: item.label }, 0)\r\n      \"\r\n    >\r\n      <img *ngIf=\"item.icon\" [src]=\"item.icon\" alt=\"\" class=\"dropdown-icon\" />\r\n      <div class=\"dropdown-content\">\r\n        <div class=\"dropdown-label\">{{ item.label }}</div>\r\n        <div class=\"dropdown-description\">{{ item.description }}</div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAAA,SAIEA,YAAY,QAWP,eAAe;AACtB,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAAiBC,aAAa,QAAQ,iBAAiB;AACvD,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,YAAY,QAAQ,MAAM;AAInC,SACEC,iBAAiB,EAEjBC,aAAa,QACR,wBAAwB;AAC/B,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,sBAAsB,QAAQ,gCAAgC;AACvE,SAAiCC,UAAU,QAAQ,gBAAgB;AACnE,SAASC,mBAAmB,QAAQ,gBAAgB;;;;;;;;;;;;ICE1CC,EAAA,CAAAC,SAAA,cAGM;;;;;IADDD,EAAA,CAAAE,WAAA,WAAAC,IAAA,KAAAC,MAAA,CAAAC,gBAAA,CAAuC;;;;;IAH9CL,EAAA,CAAAM,cAAA,cAA8D;IAC5DN,EAAA,CAAAO,UAAA,IAAAC,mDAAA,kBAE6C;IAE/CR,EAAA,CAAAS,YAAA,EAAM;;;;IAJkBT,EAAA,CAAAU,SAAA,EAAgB;IAAhBV,EAAA,CAAAW,UAAA,YAAAP,MAAA,CAAAQ,WAAA,CAAgB;;;;;;IAcpCZ,EAAA,CAAAM,cAAA,0BAgBC;IAFCN,EAJA,CAAAa,UAAA,iCAAAC,0GAAA;MAAA,MAAAC,IAAA,GAAAf,EAAA,CAAAgB,aAAA,CAAAC,GAAA,EAAAC,KAAA;MAAA,MAAAd,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAuBhB,MAAA,CAAAiB,cAAA,CAAAN,IAAA,CAAiB;IAAA,EAAC,2BAAAO,oGAAAC,MAAA;MAAAvB,EAAA,CAAAgB,aAAA,CAAAC,GAAA;MAAA,MAAAb,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CACxBhB,MAAA,CAAAoB,UAAA,CAAAD,MAAA,CAAkB;IAAA,EAAC,yBAAAE,kGAAA;MAAA,MAAAV,IAAA,GAAAf,EAAA,CAAAgB,aAAA,CAAAC,GAAA,EAAAC,KAAA;MAAA,MAAAd,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CACrBhB,MAAA,CAAAsB,cAAA,CAAAX,IAAA,CAAiB;IAAA,EAAC,kCAAAY,2GAAAJ,MAAA;MAAA,MAAAR,IAAA,GAAAf,EAAA,CAAAgB,aAAA,CAAAC,GAAA,EAAAC,KAAA;MAAA,MAAAd,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CACThB,MAAA,CAAAwB,sBAAA,CAAAL,MAAA,EAAAR,IAAA,CAAiC;IAAA,EAAC,gCAAAc,yGAAAN,MAAA;MAAAvB,EAAA,CAAAgB,aAAA,CAAAC,GAAA;MAAA,MAAAb,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CACpChB,MAAA,CAAA0B,oBAAA,CAAAP,MAAA,CAA4B;IAAA,EAAC;IAGrDvB,EAAA,CAAAS,YAAA,EAAkB;;;;IARhBT,EAPA,CAAAW,UAAA,UAAAoB,OAAA,CAAAC,KAAA,CAAoB,UAAAD,OAAA,CAAAE,KAAA,CACA,aAAAF,OAAA,CAAAG,QAAA,CACM,gBAAAH,OAAA,CAAAI,WAAA,CACM,iBAAAJ,OAAA,CAAAK,YAAA,UACW,kBAAAL,OAAA,CAAAM,aAAA,IAAArC,EAAA,CAAAsC,eAAA,IAAAC,GAAA,EACD,SAAAR,OAAA,CAAAS,IAAA,CACxB,aAAAT,OAAA,CAAAU,QAAA,UACiB;;;;;;IAsDrCzC,EAPJ,CAAAM,cAAA,iBAKC,eACsC,cACF;IAAAN,EAAA,CAAA0C,MAAA,2BAAoB;IAAA1C,EAAA,CAAAS,YAAA,EAAM;IAEzDT,EADF,CAAAM,cAAA,cAAmC,gBACI;IAAAN,EAAA,CAAA0C,MAAA,0BAAmB;IAAA1C,EAAA,CAAAS,YAAA,EAAQ;IAChET,EAAA,CAAAM,cAAA,uBAQC;IAHCN,EAAA,CAAAa,UAAA,6BAAA8B,8FAAApB,MAAA;MAAAvB,EAAA,CAAAgB,aAAA,CAAA4B,GAAA;MAAA,MAAAxC,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAmBhB,MAAA,CAAAyC,WAAA,CAAAtB,MAAA,CAAmB;IAAA,EAAC;IAIzCvB,EAAA,CAAAS,YAAA,EAAe;IAEfT,EAAA,CAAAM,cAAA,gBAAqC;IAAAN,EAAA,CAAA0C,MAAA,qBAAa;IAAA1C,EAAA,CAAAS,YAAA,EAAQ;IAC1DT,EAAA,CAAAM,cAAA,wBAQC;IAHCN,EAAA,CAAAa,UAAA,6BAAAiC,+FAAAvB,MAAA;MAAAvB,EAAA,CAAAgB,aAAA,CAAA4B,GAAA;MAAA,MAAAxC,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAmBhB,MAAA,CAAA2C,cAAA,CAAAxB,MAAA,CAAsB;IAAA,EAAC;IAI5CvB,EAAA,CAAAS,YAAA,EAAe;IAEfT,EAAA,CAAAM,cAAA,iBAAqC;IAAAN,EAAA,CAAA0C,MAAA,sBAAc;IAAA1C,EAAA,CAAAS,YAAA,EAAQ;IAC3DT,EAAA,CAAAM,cAAA,wBAQC;IAHCN,EAAA,CAAAa,UAAA,6BAAAmC,+FAAAzB,MAAA;MAAAvB,EAAA,CAAAgB,aAAA,CAAA4B,GAAA;MAAA,MAAAxC,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAmBhB,MAAA,CAAA6C,eAAA,CAAA1B,MAAA,CAAuB;IAAA,EAAC;IAI7CvB,EAAA,CAAAS,YAAA,EAAe;IAEfT,EAAA,CAAAM,cAAA,iBAAqC;IAAAN,EAAA,CAAA0C,MAAA,mBAAW;IAAA1C,EAAA,CAAAS,YAAA,EAAQ;IACxDT,EAAA,CAAAM,cAAA,wBAQC;IAHCN,EAAA,CAAAa,UAAA,6BAAAqC,+FAAA3B,MAAA;MAAAvB,EAAA,CAAAgB,aAAA,CAAA4B,GAAA;MAAA,MAAAxC,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAmBhB,MAAA,CAAA+C,YAAA,CAAA5B,MAAA,CAAoB;IAAA,EAAC;IAK5CvB,EADE,CAAAS,YAAA,EAAe,EACX;IAEJT,EADF,CAAAM,cAAA,eAA6B,sBAM1B;IADCN,EAAA,CAAAa,UAAA,uBAAAuC,uFAAA;MAAApD,EAAA,CAAAgB,aAAA,CAAA4B,GAAA;MAAA,MAAAxC,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAahB,MAAA,CAAAiD,cAAA,EAAgB;IAAA,EAAC;IAEhCrD,EAAA,CAAAS,YAAA,EAAa;IACbT,EAAA,CAAAM,cAAA,sBAMC;IADCN,EAAA,CAAAa,UAAA,uBAAAyC,uFAAA;MAAAtD,EAAA,CAAAgB,aAAA,CAAA4B,GAAA;MAAA,MAAAxC,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAahB,MAAA,CAAAmD,mBAAA,EAAqB;IAAA,EAAC;IAK3CvD,EAHM,CAAAS,YAAA,EAAa,EACT,EACD,EACH;;;;IAvEJT,EAAA,CAAAW,UAAA,YAAAP,MAAA,CAAAoD,YAAA,CAAwB;IAElBxD,EAAA,CAAAU,SAAA,GAA8B;IAA9BV,EAAA,CAAAW,UAAA,cAAAP,MAAA,CAAAqD,gBAAA,CAA8B;IAK9BzD,EAAA,CAAAU,SAAA,GAAuC;IAMvCV,EANA,CAAAW,UAAA,wCAAuC,YAAAP,MAAA,CAAAsD,UAAA,CACjB,kBAAAtD,MAAA,CAAAuD,eAAA,CACW,mBACf,gBAEH,sBACM;IAMrB3D,EAAA,CAAAU,SAAA,GAAiC;IAMjCV,EANA,CAAAW,UAAA,kCAAiC,YAAAP,MAAA,CAAAwD,aAAA,CACR,kBAAAxD,MAAA,CAAAyD,kBAAA,CACW,cAAAzD,MAAA,CAAA0D,WAAA,CACX,gBAEV,sBACM;IAMrB9D,EAAA,CAAAU,SAAA,GAAkC;IAMlCV,EANA,CAAAW,UAAA,mCAAkC,YAAAP,MAAA,CAAA2D,cAAA,CACR,kBAAA3D,MAAA,CAAA4D,mBAAA,CACW,cAAA5D,MAAA,CAAA6D,cAAA,CACT,gBAEb,sBACM;IAMrBjE,EAAA,CAAAU,SAAA,GAA+B;IAM/BV,EANA,CAAAW,UAAA,gCAA+B,YAAAP,MAAA,CAAA8D,WAAA,CACR,kBAAA9D,MAAA,CAAA+D,gBAAA,CACW,cAAA/D,MAAA,CAAAgE,eAAA,CACL,gBAEd,sBACM;IAgBrBpE,EAAA,CAAAU,SAAA,GAAoC;IAApCV,EAAA,CAAAW,UAAA,cAAAP,MAAA,CAAAqD,gBAAA,CAAAY,KAAA,CAAoC;;;;;;IAS5CrE,EAAA,CAAAM,cAAA,cAIC;IADCN,EAAA,CAAAa,UAAA,mBAAAyD,2EAAA;MAAAtE,EAAA,CAAAgB,aAAA,CAAAuD,GAAA;MAAA,MAAAnE,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAShB,MAAA,CAAAiD,cAAA,EAAgB;IAAA,EAAC;IAC3BrD,EAAA,CAAAS,YAAA,EAAM;;;;;;IAhHPT,EADF,CAAAM,cAAA,cAAwE,iBAKrE;IAFCN,EAAA,CAAAa,UAAA,mBAAA2D,oEAAA;MAAAxE,EAAA,CAAAgB,aAAA,CAAAyD,GAAA;MAAA,MAAArE,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAShB,MAAA,CAAAsE,eAAA,EAAiB;IAAA,EAAC;IAG3B1E,EAAA,CAAAM,cAAA,eAAuB;IACrBN,EAAA,CAAAC,SAAA,cAKE;IACJD,EAAA,CAAAS,YAAA,EAAO;IAELT,EADF,CAAAM,cAAA,eAA6B,WACrB;IAAAN,EAAA,CAAA0C,MAAA,GAAc;IACtB1C,EADsB,CAAAS,YAAA,EAAO,EACtB;IACPT,EAAA,CAAAM,cAAA,eAAgE;;IAC9DN,EAAA,CAAAM,cAAA,cAA4D;IAC1DN,EAAA,CAAAC,SAAA,gBAME;IAGRD,EAFI,CAAAS,YAAA,EAAM,EACD,EACH;IAiFNT,EA9EA,CAAAO,UAAA,KAAAoE,qDAAA,oBAKC,KAAAC,qDAAA,kBA6EA;IACH5E,EAAA,CAAAS,YAAA,EAAM;;;;IAnGMT,EAAA,CAAAU,SAAA,GAAc;IAAdV,EAAA,CAAA6E,iBAAA,CAAAzE,MAAA,CAAA0E,QAAA,CAAc;IAEW9E,EAAA,CAAAU,SAAA,EAA8B;IAA9BV,EAAA,CAAAE,WAAA,SAAAE,MAAA,CAAA2E,eAAA,CAA8B;IAe9D/E,EAAA,CAAAU,SAAA,GAAqB;IAArBV,EAAA,CAAAW,UAAA,SAAAP,MAAA,CAAA2E,eAAA,CAAqB;IA8ErB/E,EAAA,CAAAU,SAAA,EAAqB;IAArBV,EAAA,CAAAW,UAAA,SAAAP,MAAA,CAAA2E,eAAA,CAAqB;;;;;;IAiBxB/E,EAAA,CAAAM,cAAA,cAIC;IADCN,EAAA,CAAAa,UAAA,mBAAAmE,0EAAA;MAAAhF,EAAA,CAAAgB,aAAA,CAAAiE,IAAA;MAAA,MAAA7E,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAShB,MAAA,CAAA8E,cAAA,EAAgB;IAAA,EAAC;IAC3BlF,EAAA,CAAAS,YAAA,EAAM;;;;;IAmBGT,EAAA,CAAAM,cAAA,cAAqD;IACnDN,EAAA,CAAA0C,MAAA,GACF;IAAA1C,EAAA,CAAAS,YAAA,EAAM;;;;IADJT,EAAA,CAAAU,SAAA,EACF;IADEV,EAAA,CAAAmF,kBAAA,MAAAC,OAAA,CAAAC,WAAA,MACF;;;;;;IAZJrF,EAAA,CAAAM,cAAA,cAIC;IADCN,EAAA,CAAAa,UAAA,mBAAAyE,0EAAA;MAAA,MAAAF,OAAA,GAAApF,EAAA,CAAAgB,aAAA,CAAAuE,IAAA,EAAAC,SAAA;MAAA,MAAApF,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAShB,MAAA,CAAAqF,aAAA,CAAAL,OAAA,CAAkB;IAAA,EAAC;IAE5BpF,EAAA,CAAAM,cAAA,cAAsB;IACpBN,EAAA,CAAAC,SAAA,cAAyC;IAC3CD,EAAA,CAAAS,YAAA,EAAM;IAEJT,EADF,CAAAM,cAAA,cAAsB,cACE;IAAAN,EAAA,CAAA0C,MAAA,GAAc;IAAA1C,EAAA,CAAAS,YAAA,EAAM;IAC1CT,EAAA,CAAAO,UAAA,IAAAmF,0DAAA,kBAAqD;IAIzD1F,EADE,CAAAS,YAAA,EAAM,EACF;;;;IARGT,EAAA,CAAAU,SAAA,GAAgB;IAACV,EAAjB,CAAAW,UAAA,QAAAyE,OAAA,CAAA5C,IAAA,EAAAxC,EAAA,CAAA2F,aAAA,CAAgB,QAAAP,OAAA,CAAAQ,IAAA,CAAiB;IAGhB5F,EAAA,CAAAU,SAAA,GAAc;IAAdV,EAAA,CAAA6E,iBAAA,CAAAO,OAAA,CAAAQ,IAAA,CAAc;IACN5F,EAAA,CAAAU,SAAA,EAAqB;IAArBV,EAAA,CAAAW,UAAA,SAAAyE,OAAA,CAAAC,WAAA,CAAqB;;;;;;IAhC7DrF,EADF,CAAAM,cAAA,cAA+D,cAK5D;IADCN,EAAA,CAAAa,UAAA,mBAAAgF,oEAAA;MAAA7F,EAAA,CAAAgB,aAAA,CAAA8E,IAAA;MAAA,MAAA1F,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAShB,MAAA,CAAA2F,eAAA,EAAiB;IAAA,EAAC;IAE5B/F,EAAA,CAAAC,SAAA,mBAA4C;IAC7CD,EAAA,CAAAS,YAAA,EAAM;IAGNT,EAAA,CAAAO,UAAA,IAAAyF,oDAAA,kBAIC;IAQGhG,EALJ,CAAAM,cAAA,cAAmE,cACjC,cAID;IAC3BN,EAAA,CAAAO,UAAA,IAAA0F,oDAAA,kBAIC;IAcTjG,EAHM,CAAAS,YAAA,EAAM,EACF,EACF,EACF;;;;IAtCFT,EAAA,CAAAU,SAAA,EAAgC;IAAhCV,EAAA,CAAAE,WAAA,WAAAE,MAAA,CAAA8F,eAAA,CAAgC;IAQ/BlG,EAAA,CAAAU,SAAA,GAAqB;IAArBV,EAAA,CAAAW,UAAA,SAAAP,MAAA,CAAA8F,eAAA,CAAqB;IAMSlG,EAAA,CAAAU,SAAA,EAAiC;IAAjCV,EAAA,CAAAE,WAAA,YAAAE,MAAA,CAAA8F,eAAA,CAAiC;IAO1ClG,EAAA,CAAAU,SAAA,GAAoB;IAApBV,EAAA,CAAAW,UAAA,YAAAP,MAAA,CAAA+F,eAAA,GAAoB;;;;;;IAoB9CnG,EAAA,CAAAM,cAAA,cAIC;IADCN,EAAA,CAAAa,UAAA,mBAAAuF,oEAAA;MAAApG,EAAA,CAAAgB,aAAA,CAAAqF,IAAA;MAAA,MAAAjG,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAShB,MAAA,CAAAkG,WAAA,EAAa;IAAA,EAAC;IAEvBtG,EAAA,CAAAC,SAAA,cAAuE;IACzED,EAAA,CAAAS,YAAA,EAAM;;;;IADCT,EAAA,CAAAU,SAAA,EAAqB;IAArBV,EAAA,CAAAW,UAAA,QAAAP,MAAA,CAAAmG,aAAA,EAAAvG,EAAA,CAAA2F,aAAA,CAAqB;;;;;IAuBlB3F,EAAA,CAAAM,cAAA,cAAyD;IACvDN,EAAA,CAAA0C,MAAA,GACF;IAAA1C,EAAA,CAAAS,YAAA,EAAM;;;;IADJT,EAAA,CAAAU,SAAA,EACF;IADEV,EAAA,CAAAmF,kBAAA,MAAA/E,MAAA,CAAAoG,eAAA,MACF;;;;;IACAxG,EAAA,CAAAM,cAAA,cAA6C;IAC3CN,EAAA,CAAA0C,MAAA,GACF;IAAA1C,EAAA,CAAAS,YAAA,EAAM;;;;IADJT,EAAA,CAAAU,SAAA,EACF;IADEV,EAAA,CAAAmF,kBAAA,MAAA/E,MAAA,CAAAqG,SAAA,MACF;;;;;;IAYAzG,EALJ,CAAAM,cAAA,cAGC,cACqC,eACN;IAAAN,EAAA,CAAA0C,MAAA,WAAI;IAClC1C,EADkC,CAAAS,YAAA,EAAO,EACnC;IAEJT,EADF,CAAAM,cAAA,cAAoC,iBAKjC;IADCN,EAAA,CAAAa,UAAA,mBAAA6F,8EAAA;MAAA1G,EAAA,CAAAgB,aAAA,CAAA2F,IAAA;MAAA,MAAAvG,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAAhB,MAAA,CAAAwG,YAAA,KAA0B,OAAO,IAAIxG,MAAA,CAAAkG,WAAA,EAAa;IAAA,EAAC;;IAEnDtG,EAAA,CAAAM,cAAA,cAOC;IAECN,EADA,CAAAC,SAAA,iBAAgC,eAG9B;IACJD,EAAA,CAAAS,YAAA,EAAM;IACNT,EAAA,CAAA0C,MAAA,cACF;IAAA1C,EAAA,CAAAS,YAAA,EAAS;;IACTT,EAAA,CAAAM,cAAA,kBAIC;IADCN,EAAA,CAAAa,UAAA,mBAAAgG,+EAAA;MAAA7G,EAAA,CAAAgB,aAAA,CAAA2F,IAAA;MAAA,MAAAvG,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAAhB,MAAA,CAAAwG,YAAA,KAA0B,MAAM,IAAIxG,MAAA,CAAAkG,WAAA,EAAa;IAAA,EAAC;;IAElDtG,EAAA,CAAAM,cAAA,eAOC;IACCN,EAAA,CAAAC,SAAA,gBAA4D;IAC9DD,EAAA,CAAAS,YAAA,EAAM;IACNT,EAAA,CAAA0C,MAAA,cACF;IAEJ1C,EAFI,CAAAS,YAAA,EAAS,EACL,EACF;;;;IApCAT,EAAA,CAAAU,SAAA,GAAyC;IAAzCV,EAAA,CAAAE,WAAA,WAAAE,MAAA,CAAAwG,YAAA,aAAyC;IAoBzC5G,EAAA,CAAAU,SAAA,GAAwC;IAAxCV,EAAA,CAAAE,WAAA,WAAAE,MAAA,CAAAwG,YAAA,YAAwC;;;;;;IAwB1C5G,EAAA,CAAAM,cAAA,iBAKC;IADCN,EAAA,CAAAa,UAAA,mBAAAiG,uFAAA;MAAA,MAAAC,YAAA,GAAA/G,EAAA,CAAAgB,aAAA,CAAAgG,IAAA,EAAAxB,SAAA;MAAA,MAAApF,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAShB,MAAA,CAAA6G,cAAA,CAAAF,YAAA,CAAAG,IAAA,CAA6B;IAAA,EAAC;IAEvClH,EAAA,CAAA0C,MAAA,GACF;IAAA1C,EAAA,CAAAS,YAAA,EAAS;;;;;IAJPT,EAAA,CAAAE,WAAA,WAAAE,MAAA,CAAA+G,eAAA,KAAAJ,YAAA,CAAAG,IAAA,CAAkD;IAGlDlH,EAAA,CAAAU,SAAA,EACF;IADEV,EAAA,CAAAmF,kBAAA,MAAA4B,YAAA,CAAAnB,IAAA,MACF;;;;;IAVA5F,EAFJ,CAAAM,cAAA,cAAiE,cAC3B,eACN;IAAAN,EAAA,CAAA0C,MAAA,eAAQ;IACtC1C,EADsC,CAAAS,YAAA,EAAO,EACvC;IACNT,EAAA,CAAAM,cAAA,cAA8B;IAC5BN,EAAA,CAAAO,UAAA,IAAA6G,8DAAA,qBAKC;IAILpH,EADE,CAAAS,YAAA,EAAM,EACF;;;;IARqBT,EAAA,CAAAU,SAAA,GAA4B;IAA5BV,EAAA,CAAAW,UAAA,YAAAP,MAAA,CAAAiH,MAAA,CAAAC,kBAAA,CAA4B;;;;;;IArF3DtH,EADF,CAAAM,cAAA,cAAkE,cAK/D;IADCN,EAAA,CAAAa,UAAA,mBAAA0G,oEAAA;MAAAvH,EAAA,CAAAgB,aAAA,CAAAwG,IAAA;MAAA,MAAApH,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAShB,MAAA,CAAAqH,qBAAA,EAAuB;IAAA,EAAC;IAEjCzH,EAAA,CAAAC,SAAA,cAAoE;IACtED,EAAA,CAAAS,YAAA,EAAM;IAOAT,EAJN,CAAAM,cAAA,cAAoE,cAC5B,cAEL,cACK;IAChCN,EAAA,CAAAC,SAAA,cAA6C;IAC/CD,EAAA,CAAAS,YAAA,EAAM;IAEJT,EADF,CAAAM,cAAA,cAA6B,cACD;IAAAN,EAAA,CAAA0C,MAAA,IAAc;IAAA1C,EAAA,CAAAS,YAAA,EAAM;IAI9CT,EAHA,CAAAO,UAAA,KAAAmH,qDAAA,kBAAyD,KAAAC,qDAAA,kBAGZ;IAIjD3H,EADE,CAAAS,YAAA,EAAM,EACF;IAENT,EAAA,CAAAC,SAAA,eAAmC;IAoDnCD,EAjDA,CAAAO,UAAA,KAAAqH,qDAAA,mBAGC,KAAAC,qDAAA,kBA8CgE;IAgBjE7H,EAAA,CAAAC,SAAA,eAAmC;IAIjCD,EADF,CAAAM,cAAA,eAA6B,kBACuC;IAAnBN,EAAA,CAAAa,UAAA,mBAAAiH,wEAAA;MAAA9H,EAAA,CAAAgB,aAAA,CAAAwG,IAAA;MAAA,MAAApH,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAShB,MAAA,CAAA2H,MAAA,EAAQ;IAAA,EAAC;;IAC/D/H,EAAA,CAAAM,cAAA,eAOC;IAGCN,EAFA,CAAAC,SAAA,gBAAoD,oBACd,gBACC;IACzCD,EAAA,CAAAS,YAAA,EAAM;IACNT,EAAA,CAAA0C,MAAA,kBACF;IAIR1C,EAJQ,CAAAS,YAAA,EAAS,EACL,EACF,EACF,EACF;;;;IAnHFT,EAAA,CAAAU,SAAA,EAAoC;IAApCV,EAAA,CAAAE,WAAA,WAAAE,MAAA,CAAA4H,mBAAA,CAAoC;IAG/BhI,EAAA,CAAAU,SAAA,EAAkB;IAAlBV,EAAA,CAAAW,UAAA,QAAAP,MAAA,CAAA6H,UAAA,EAAAjI,EAAA,CAAA2F,aAAA,CAAkB;IAIK3F,EAAA,CAAAU,SAAA,EAAqC;IAArCV,EAAA,CAAAE,WAAA,YAAAE,MAAA,CAAA4H,mBAAA,CAAqC;IAKtDhI,EAAA,CAAAU,SAAA,GAAkB;IAAlBV,EAAA,CAAAW,UAAA,QAAAP,MAAA,CAAA6H,UAAA,EAAAjI,EAAA,CAAA2F,aAAA,CAAkB;IAGG3F,EAAA,CAAAU,SAAA,GAAc;IAAdV,EAAA,CAAA6E,iBAAA,CAAAzE,MAAA,CAAA8H,QAAA,CAAc;IACNlI,EAAA,CAAAU,SAAA,EAAqB;IAArBV,EAAA,CAAAW,UAAA,SAAAP,MAAA,CAAAoG,eAAA,CAAqB;IAG3BxG,EAAA,CAAAU,SAAA,EAAe;IAAfV,EAAA,CAAAW,UAAA,SAAAP,MAAA,CAAAqG,SAAA,CAAe;IAU5CzG,EAAA,CAAAU,SAAA,GAAqC;IAArCV,EAAA,CAAAW,UAAA,SAAAP,MAAA,CAAAiH,MAAA,CAAAc,wBAAA,CAAqC;IAgDlCnI,EAAA,CAAAU,SAAA,EAAiC;IAAjCV,EAAA,CAAAW,UAAA,SAAAP,MAAA,CAAAiH,MAAA,CAAAe,oBAAA,CAAiC;;;;;;IAlUnDpI,EAAA,CAAAM,cAAA,UAA0B;;IAIpBN,EAFJ,CAAAM,cAAA,aAAqD,WAC7C,kBACwD;IAC1DN,EAAA,CAAAC,SAAA,cASE;IAGRD,EAFI,CAAAS,YAAA,EAAW,EACN,EACH;;IAIFT,EAFJ,CAAAM,cAAA,oBAA0B,aACN,aAK0B;IAArCN,EADA,CAAAa,UAAA,wBAAAwH,kEAAA;MAAArI,EAAA,CAAAgB,aAAA,CAAAsH,GAAA;MAAA,MAAAlI,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAchB,MAAA,CAAAmI,kBAAA,EAAoB;IAAA,EAAC,wBAAAC,kEAAA;MAAAxI,EAAA,CAAAgB,aAAA,CAAAsH,GAAA;MAAA,MAAAlI,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CACrBhB,MAAA,CAAAqI,mBAAA,EAAqB;IAAA,EAAC;IACvCzI,EAAA,CAAAC,SAAA,cAG2C;IAG3CD,EAAA,CAAAO,UAAA,IAAAmI,6CAAA,kBAA8D;IAOlE1I,EADE,CAAAS,YAAA,EAAM,EACF;IAGJT,EADF,CAAAM,cAAA,eAAoB,eACU;IAC1BN,EAAA,CAAAC,SAAA,eAAiC;IAE/BD,EADF,CAAAM,cAAA,eAAsB,eACG;IAErBN,EAAA,CAAAO,UAAA,KAAAoI,0DAAA,8BAgBC;IAKT3I,EAHM,CAAAS,YAAA,EAAM,EACF,EACF,EACF;IAENT,EAAA,CAAAM,cAAA,eAA+C;IA4K7CN,EA1KA,CAAAO,UAAA,KAAAqI,8CAAA,mBAAwE,KAAAC,8CAAA,kBAqHT,KAAAC,8CAAA,kBAgD9D,KAAAC,8CAAA,oBAKiE;IAyHxE/I,EAFI,CAAAS,YAAA,EAAM,EACK,EACT;;;;IArVKT,EAAA,CAAAU,SAAA,GAAsC;;IAMpCV,EAAA,CAAAU,SAAA,EAA4C;IAA5CV,EAAA,CAAAE,WAAA,uBAAAE,MAAA,CAAA4I,eAAA,CAA4C;IAC5ChJ,EAHA,CAAAW,UAAA,QAAAP,MAAA,CAAA6I,WAAA,EAAAjJ,EAAA,CAAA2F,aAAA,CAAmB,QAAAvF,MAAA,CAAA8I,iBAAA,WAGgB;IAGRlJ,EAAA,CAAAU,SAAA,EAA4B;IAA5BV,EAAA,CAAAW,UAAA,SAAAP,MAAA,CAAAQ,WAAA,CAAAuI,MAAA,KAA4B;IAgBrCnJ,EAAA,CAAAU,SAAA,GAAoB;IAApBV,EAAA,CAAAW,UAAA,YAAAP,MAAA,CAAAiH,MAAA,CAAA+B,QAAA,CAAoB;IAwBvCpJ,EAAA,CAAAU,SAAA,GAA4B;IAA5BV,EAAA,CAAAW,UAAA,SAAAP,MAAA,CAAAiH,MAAA,CAAAgC,eAAA,CAA4B;IAqH5BrJ,EAAA,CAAAU,SAAA,EAA0B;IAA1BV,EAAA,CAAAW,UAAA,SAAAP,MAAA,CAAAiH,MAAA,CAAAiC,aAAA,CAA0B;IA6C7BtJ,EAAA,CAAAU,SAAA,EAAqD;IAArDV,EAAA,CAAAW,UAAA,SAAAP,MAAA,CAAAiH,MAAA,CAAAkC,eAAA,KAAAnJ,MAAA,CAAAiH,MAAA,CAAAiC,aAAA,CAAqD;IAQlDtJ,EAAA,CAAAU,SAAA,EAAgC;IAAhCV,EAAA,CAAAW,UAAA,SAAAP,MAAA,CAAAiH,MAAA,CAAAmC,mBAAA,CAAgC;;;;;IA0ItCxJ,EAAA,CAAAC,SAAA,eAAwE;;;;IAAjDD,EAAA,CAAAW,UAAA,QAAA8I,QAAA,CAAAjH,IAAA,EAAAxC,EAAA,CAAA2F,aAAA,CAAiB;;;;;;IAP1C3F,EAAA,CAAAM,cAAA,eAMC;IAHCN,EAAA,CAAAa,UAAA,mBAAA6I,mEAAA;MAAA,MAAAD,QAAA,GAAAzJ,EAAA,CAAAgB,aAAA,CAAA2I,IAAA,EAAAnE,SAAA;MAAA,MAAApF,MAAA,GAAAJ,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CACWhB,MAAA,CAAAwB,sBAAA,CAAuB;QAAAK,KAAA,EAAAwH,QAAA,CAAAxH,KAAA;QAAAD,KAAA,EAAAyH,QAAA,CAAAzH;MAAA,CACrC,EAAE,CAAC,CAAC;IAAA;IAEDhC,EAAA,CAAAO,UAAA,IAAAqJ,mDAAA,mBAAwE;IAEtE5J,EADF,CAAAM,cAAA,eAA8B,eACA;IAAAN,EAAA,CAAA0C,MAAA,GAAgB;IAAA1C,EAAA,CAAAS,YAAA,EAAM;IAClDT,EAAA,CAAAM,cAAA,eAAkC;IAAAN,EAAA,CAAA0C,MAAA,GAAsB;IAE5D1C,EAF4D,CAAAS,YAAA,EAAM,EAC1D,EACF;;;;IALET,EAAA,CAAAU,SAAA,EAAe;IAAfV,EAAA,CAAAW,UAAA,SAAA8I,QAAA,CAAAjH,IAAA,CAAe;IAESxC,EAAA,CAAAU,SAAA,GAAgB;IAAhBV,EAAA,CAAA6E,iBAAA,CAAA4E,QAAA,CAAAzH,KAAA,CAAgB;IACVhC,EAAA,CAAAU,SAAA,GAAsB;IAAtBV,EAAA,CAAA6E,iBAAA,CAAA4E,QAAA,CAAApE,WAAA,CAAsB;;;;;IAX9DrF,EANF,CAAAM,cAAA,cAKC,cAC4B;IACzBN,EAAA,CAAAO,UAAA,IAAAsJ,6CAAA,mBAMC;IAQL7J,EADE,CAAAS,YAAA,EAAM,EACF;;;;IAjBJT,EADA,CAAA8J,WAAA,QAAA1J,MAAA,CAAA2J,cAAA,CAAAC,IAAA,CAAAC,MAAA,YAAmD,SAAA7J,MAAA,CAAA2J,cAAA,CAAAC,IAAA,CAAAE,IAAA,QACL;IAIzBlK,EAAA,CAAAU,SAAA,GAAuB;IAAvBV,EAAA,CAAAW,UAAA,YAAAP,MAAA,CAAA2J,cAAA,CAAAI,KAAA,CAAuB;;;AD7P9C,WAAaC,wBAAwB;EAA/B,MAAOA,wBAAwB;IAkGzBC,MAAA;IACAC,GAAA;IACAC,QAAA;IACAC,UAAA;IACAC,YAAA;IACAC,WAAA;IACAC,WAAA;IArGDtD,MAAM;IACNuD,sBAAsB,CAAO,CAAC;IAC9BC,gBAAgB,CAAO,CAAC;IACxBC,YAAY,CAAO,CAAC;IAE7B;IACAlE,YAAY,GAAqB,OAAO;IACxCoB,mBAAmB,GAAY,KAAK;IACpCE,QAAQ,GAAW,EAAE;IACrBzB,SAAS,GAAW,EAAE;IACtBD,eAAe,GAAW,EAAE;IAC5ByB,UAAU,GAAW,EAAE;IACvB1B,aAAa,GAAW,EAAE;IAC1BwE,WAAW,GAAY,KAAK,CAAC,CAAC;IAE9B;IACAnK,WAAW,GAAa,CACtB,sDAAsD,EACtD,sCAAsC,EACtC,qCAAqC,CACtC;IACDoK,WAAW,GAAa,CAAC,SAAS,EAAE,mBAAmB,EAAE,gBAAgB,CAAC;IAC1E3K,gBAAgB,GAAW,CAAC;IAC5B4I,WAAW,GAAW,EAAE;IACxBC,iBAAiB,GAAW,EAAE;IAC9B+B,qBAAqB;IACrBjC,eAAe,GAAY,KAAK;IAEhC;IACA7B,eAAe,GAAW,IAAI;IAE9B;IACU+D,eAAe,GAAG,IAAI9L,YAAY,EAAU;IAC5C+L,oBAAoB,GAAG,IAAI/L,YAAY,EAG7C;IACMgM,aAAa,GAAG,IAAIhM,YAAY,EAAU;IAC1CiM,WAAW,GAAG,IAAIjM,YAAY,EAAoB;IAClDkM,cAAc,GAAG,IAAIlM,YAAY,EAAU;IAC3CmM,eAAe,GAAG,IAAInM,YAAY,EAAO;IAEnD;IACA2F,eAAe,GAAY,KAAK;IAEhC;IACAmB,eAAe,GAAY,KAAK;IAEhC;IACAxC,UAAU,GAAqB,EAAE;IACjCE,aAAa,GAAqB,EAAE;IACpCG,cAAc,GAAqB,EAAE;IACrCG,WAAW,GAAqB,EAAE;IAElC;IACAJ,WAAW,GAAW,EAAE;IACxBG,cAAc,GAAW,EAAE;IAC3BG,eAAe,GAAW,EAAE;IAC5BoH,YAAY,GAAW,EAAE;IAEzB;IACA7H,eAAe,GAAW,EAAE;IAC5BE,kBAAkB,GAAW,EAAE;IAC/BG,mBAAmB,GAAW,EAAE;IAChCG,gBAAgB,GAAW,EAAE;IAE7B;IACAV,gBAAgB;IAEhB;IACQgI,aAAa,GAAmB,EAAE;IAEMC,cAAc;IACrBC,UAAU;IACnDnI,YAAY,GAAqB,MAAM;IAEvC;IACAuG,cAAc,GAMV;MACF6B,IAAI,EAAE,KAAK;MACX5B,IAAI,EAAE,IAAI;MACVG,KAAK,EAAE,EAAE;MACT0B,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE;KACZ;IAED;IACQC,aAAa,GAAG,IAAItM,YAAY,EAAE;IAE1CuM,YACU3B,MAAc,EACdC,GAAsB,EACtBC,QAAmB,EACnBC,UAAsB,EACtBC,YAAiC,EACjCC,WAAwB,EACxBC,WAAwB;MANxB,KAAAN,MAAM,GAANA,MAAM;MACN,KAAAC,GAAG,GAAHA,GAAG;MACH,KAAAC,QAAQ,GAARA,QAAQ;MACR,KAAAC,UAAU,GAAVA,UAAU;MACV,KAAAC,YAAY,GAAZA,YAAY;MACZ,KAAAC,WAAW,GAAXA,WAAW;MACX,KAAAC,WAAW,GAAXA,WAAW;IAClB;IAEHsB,QAAQA,CAAA;MACN,IAAI,CAACC,gBAAgB,EAAE;MACvB,IAAI,CAACC,uBAAuB,EAAE;MAC9B,IAAI,CAACC,sBAAsB,EAAE;MAC7B,IAAI,CAACC,YAAY,EAAE;MACnB,IAAI,CAACC,cAAc,EAAE;MACrB,IAAI,CAACC,cAAc,EAAE;MAErB;MACA,IAAI,IAAI,CAAClF,MAAM,CAACgC,eAAe,EAAE;QAC/B,IAAI,CAACmD,qBAAqB,EAAE;MAC9B;IACF;IAEAC,eAAeA,CAAA;MACb,IAAI,CAACC,2BAA2B,CAAC,IAAI,CAACrC,MAAM,CAACsC,GAAG,CAAC;IACnD;IAEAC,WAAWA,CAAA;MACT,IAAI,CAACb,aAAa,CAACc,WAAW,EAAE;MAChC,IAAI,IAAI,CAAC5B,qBAAqB,EAAE;QAC9B6B,aAAa,CAAC,IAAI,CAAC7B,qBAAqB,CAAC;MAC3C;IACF;IAEA;IACA;IACA;IAEQsB,cAAcA,CAAA;MACpB;MACA,IAAI,IAAI,CAAClF,MAAM,CAACzG,WAAW,IAAI,IAAI,CAACyG,MAAM,CAACzG,WAAW,CAACuI,MAAM,GAAG,CAAC,EAAE;QACjE,IAAI,CAACvI,WAAW,GAAG,IAAI,CAACyG,MAAM,CAACzG,WAAW;QAC1CmM,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAACpM,WAAW,CAAC;MAChE,CAAC,MAAM;QACLmM,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE,IAAI,CAACpM,WAAW,CAAC;MACjE;MAEA,IAAI,IAAI,CAACyG,MAAM,CAAC2D,WAAW,IAAI,IAAI,CAAC3D,MAAM,CAAC2D,WAAW,CAAC7B,MAAM,GAAG,CAAC,EAAE;QACjE,IAAI,CAAC6B,WAAW,GAAG,IAAI,CAAC3D,MAAM,CAAC2D,WAAW;MAC5C;MAEA;MACA,IAAI,IAAI,CAAC3D,MAAM,CAAC4F,mBAAmB,KAAK,KAAK,IAAI,IAAI,CAACrM,WAAW,CAACuI,MAAM,GAAG,CAAC,EAAE;QAC5E;QACA,IAAI,CAACF,WAAW,GAAG,IAAI,CAACrI,WAAW,CAAC,CAAC,CAAC;QACtC,IAAI,CAACsI,iBAAiB,GAAG,IAAI,CAAC8B,WAAW,CAAC,CAAC,CAAC;QAC5C+B,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAAC/D,WAAW,CAAC;QACjE8D,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE,IAAI,CAAC9D,iBAAiB,CAAC;MACvE,CAAC,MAAM;QACL;QACA,IAAI,CAACD,WAAW,GAAG,IAAI,CAAC5B,MAAM,CAAC6F,OAAO;QACtC,IAAI,CAAChE,iBAAiB,GAAG,IAAI,CAAC7B,MAAM,CAAC8F,WAAW,IAAI,QAAQ;QAC5DJ,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC/D,WAAW,CAAC;MACxD;MAEA;MACA,IAAI,IAAI,CAAC5B,MAAM,CAAC4F,mBAAmB,KAAK,KAAK,EAAE;QAC7C,IAAI,CAACG,kBAAkB,EAAE;MAC3B;IACF;IAEQA,kBAAkBA,CAAA;MACxB;MACA,IAAI,IAAI,CAACnC,qBAAqB,EAAE;QAC9B6B,aAAa,CAAC,IAAI,CAAC7B,qBAAqB,CAAC;MAC3C;MAEA;MACA,MAAMoC,QAAQ,GAAG,IAAI,CAAChG,MAAM,CAAC4D,qBAAqB,IAAI,IAAI;MAE1D;MACA,IAAI,CAACA,qBAAqB,GAAGqC,WAAW,CAAC,MAAK;QAC5C,IAAI,CAACC,iBAAiB,EAAE;MAC1B,CAAC,EAAEF,QAAQ,CAAC;IACd;IAEQE,iBAAiBA,CAAA;MACvB,IAAI,IAAI,CAACvE,eAAe,EAAE;MAE1B,IAAI,CAACA,eAAe,GAAG,IAAI;MAE3B;MACA,MAAMwE,cAAc,GAAG,IAAI,CAACnG,MAAM,CAACoG,kBAAkB,IAAI,QAAQ;MAEjE;MACA,MAAMC,WAAW,GAAGC,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC;MAC5D,IAAIF,WAAW,EAAE;QACfA,WAAW,CAACG,SAAS,CAACC,GAAG,CAAC,oBAAoB,CAAC;QAE/C;QACAJ,WAAW,CAACG,SAAS,CAACC,GAAG,CAAC,QAAQN,cAAc,EAAE,CAAC;MACrD;MAEA;MACA,MAAMO,OAAO,GAAG;QACdC,MAAM,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEC,QAAQ,EAAE;QAAG,CAAE;QACxC,aAAa,EAAE;UAAED,QAAQ,EAAE,GAAG;UAAEC,QAAQ,EAAE;QAAG,CAAE;QAC/CC,IAAI,EAAE;UAAEF,QAAQ,EAAE,GAAG;UAAEC,QAAQ,EAAE;QAAG;OACrC;MAED,MAAME,MAAM,GAAGL,OAAO,CAACP,cAAc,CAAC,IAAIO,OAAO,CAAC,QAAQ,CAAC;MAE3D;MACAM,UAAU,CAAC,MAAK;QACd;QACA,IAAI,CAAChO,gBAAgB,GACnB,CAAC,IAAI,CAACA,gBAAgB,GAAG,CAAC,IAAI,IAAI,CAACO,WAAW,CAACuI,MAAM;QACvD,IAAI,CAACF,WAAW,GAAG,IAAI,CAACrI,WAAW,CAAC,IAAI,CAACP,gBAAgB,CAAC;QAC1D,IAAI,CAAC6I,iBAAiB,GAAG,IAAI,CAAC8B,WAAW,CAAC,IAAI,CAAC3K,gBAAgB,CAAC;QAEhE;QACA,IAAI,CAACiK,GAAG,CAACgE,aAAa,EAAE;MAC1B,CAAC,EAAEF,MAAM,CAACH,QAAQ,CAAC;MAEnB;MACAI,UAAU,CAAC,MAAK;QACd,IAAIX,WAAW,EAAE;UACfA,WAAW,CAACG,SAAS,CAACU,MAAM,CAAC,oBAAoB,CAAC;UAClDb,WAAW,CAACG,SAAS,CAACU,MAAM,CAAC,QAAQf,cAAc,EAAE,CAAC;QACxD;QACA,IAAI,CAACxE,eAAe,GAAG,KAAK;MAC9B,CAAC,EAAEoF,MAAM,CAACF,QAAQ,CAAC;IACrB;IAEO3F,kBAAkBA,CAAA;MACvB,IAAI,IAAI,CAAC0C,qBAAqB,EAAE;QAC9B6B,aAAa,CAAC,IAAI,CAAC7B,qBAAqB,CAAC;QACzC,IAAI,CAACA,qBAAqB,GAAGuD,SAAS;MACxC;IACF;IAEO/F,mBAAmBA,CAAA;MACxB,IAAI,CAAC,IAAI,CAACwC,qBAAqB,EAAE;QAC/B,IAAI,CAACmC,kBAAkB,EAAE;MAC3B;IACF;IAEQlB,gBAAgBA,CAAA;MACtB,IAAI,CAAC,IAAI,CAAC7E,MAAM,EAAE;QAChB0F,OAAO,CAAC0B,IAAI,CAAC,8CAA8C,CAAC;QAC5D;MACF;MAEA;MACA,IAAI,CAACpH,MAAM,CAACgC,eAAe,GAAG,IAAI,CAAChC,MAAM,CAACgC,eAAe,IAAI,KAAK;MAClE,IAAI,CAAChC,MAAM,CAACkC,eAAe,GAAG,IAAI,CAAClC,MAAM,CAACkC,eAAe,IAAI,IAAI;MACjE,IAAI,CAAClC,MAAM,CAACiC,aAAa,GAAG,IAAI,CAACjC,MAAM,CAACiC,aAAa,IAAI,KAAK;MAC9D,IAAI,CAACjC,MAAM,CAACmC,mBAAmB,GAAG,IAAI,CAACnC,MAAM,CAACmC,mBAAmB,IAAI,IAAI;MACzE,IAAI,CAACnC,MAAM,CAAC8F,WAAW,GAAG,IAAI,CAAC9F,MAAM,CAAC8F,WAAW,IAAI,aAAa;MAClE,IAAI,CAAC9F,MAAM,CAACqH,WAAW,GAAG,IAAI,CAACrH,MAAM,CAACqH,WAAW,IAAI,GAAG;MACxD,IAAI,CAACrH,MAAM,CAACsH,UAAU,GAAG,IAAI,CAACtH,MAAM,CAACsH,UAAU,IAAI,EAAE;MAErD;MACA,IAAI,CAACtH,MAAM,CAACuH,aAAa,GAAG,IAAI,CAACvH,MAAM,CAACuH,aAAa,IAAI,CACvD;QACEhJ,IAAI,EAAE,SAAS;QACf3D,KAAK,EAAE,UAAU;QACjBO,IAAI,EAAE,sCAAsC;QAC5C6C,WAAW,EAAE;OACd,EACD;QACEO,IAAI,EAAE,mBAAmB;QACzB3D,KAAK,EAAE,oBAAoB;QAC3BO,IAAI,EAAE,gDAAgD;QACtD6C,WAAW,EAAE;OACd,EACD;QACEO,IAAI,EAAE,gBAAgB;QACtB3D,KAAK,EAAE,iBAAiB;QACxBO,IAAI,EAAE,6CAA6C;QACnD6C,WAAW,EAAE;OACd,EACD;QACEO,IAAI,EAAE,WAAW;QACjB3D,KAAK,EAAE,YAAY;QACnBO,IAAI,EAAE,wCAAwC;QAC9C6C,WAAW,EAAE;OACd,CACF;MAED;MACA,IAAI,CAACgC,MAAM,CAACc,wBAAwB,GAClC,IAAI,CAACd,MAAM,CAACc,wBAAwB,IAAI,IAAI;MAC9C,IAAI,CAACd,MAAM,CAACe,oBAAoB,GAC9B,IAAI,CAACf,MAAM,CAACe,oBAAoB,IAAI,KAAK;MAC3C,IAAI,CAACf,MAAM,CAACC,kBAAkB,GAAG,IAAI,CAACD,MAAM,CAACC,kBAAkB,IAAI,CACjE;QAAEJ,IAAI,EAAE,IAAI;QAAEtB,IAAI,EAAE;MAAS,CAAE,EAC/B;QAAEsB,IAAI,EAAE,KAAK;QAAEtB,IAAI,EAAE;MAAU,CAAE,EACjC;QAAEsB,IAAI,EAAE,IAAI;QAAEtB,IAAI,EAAE;MAAS,CAAE,CAChC;IACH;IAEQ0G,cAAcA,CAAA;MACpB,IAAI,CAAC7I,gBAAgB,GAAG,IAAI,CAACkH,WAAW,CAACkE,KAAK,CAAC;QAC7CC,GAAG,EAAE,CAAC,EAAE,EAAEhP,UAAU,CAACiP,QAAQ,CAAC;QAC9BC,MAAM,EAAE,CAAC,EAAE,EAAElP,UAAU,CAACiP,QAAQ,CAAC;QACjCE,OAAO,EAAE,CAAC,EAAE,EAAEnP,UAAU,CAACiP,QAAQ,CAAC;QAClCG,IAAI,EAAE,CAAC,EAAE,EAAEpP,UAAU,CAACiP,QAAQ;OAC/B,CAAC;IACJ;IAEQ5C,uBAAuBA,CAAA;MAC7B,MAAMgD,SAAS,GAAG,IAAI,CAAC9E,MAAM,CAAC+E,MAAM,CACjCC,IAAI,CAAC7P,MAAM,CAAE8P,KAAK,IAAKA,KAAK,YAAY/P,aAAa,CAAC,CAAC,CACvDgQ,SAAS,CAAED,KAAoB,IAAI;QAClC;QACA,IAAI,CAACvE,WAAW,GAAG,IAAI,CAACyE,gBAAgB,CAACF,KAAK,CAAC3C,GAAG,CAAC;QACnD,IAAI,CAACD,2BAA2B,CAAC4C,KAAK,CAAC3C,GAAG,CAAC;MAC7C,CAAC,CAAC;MACJ,IAAI,CAACZ,aAAa,CAAC+B,GAAG,CAACqB,SAAS,CAAC;MAEjC;MACA,IAAI,CAACpE,WAAW,GAAG,IAAI,CAACyE,gBAAgB,CAAC,IAAI,CAACnF,MAAM,CAACsC,GAAG,CAAC;IAC3D;IAEQ6C,gBAAgBA,CAAC7C,GAAW;MAClC;MACA,MAAM8C,WAAW,GAAG,CAClB,QAAQ,EACR,aAAa,EACb,mBAAmB,EACnB,gBAAgB,EAChB,gBAAgB,CACjB;MACD,OAAOA,WAAW,CAACC,IAAI,CAAEzN,KAAK,IAAK0K,GAAG,CAACgD,QAAQ,CAAC1N,KAAK,CAAC,CAAC;IACzD;IAEQmK,sBAAsBA,CAAA;MAC5B;MACA,IAAI,IAAI,CAACtB,YAAY,IAAI,IAAI,CAACA,YAAY,CAAC8E,eAAe,EAAE;QAC1D,IAAI,CAAC7D,aAAa,CAAC+B,GAAG,CACpB,IAAI,CAAChD,YAAY,CAAC8E,eAAe,CAACL,SAAS,CACxCM,KAAuB,IAAI;UAC1B,IAAI,CAACjJ,YAAY,GAAGiJ,KAAK;UACzB,IAAI,CAACC,iBAAiB,EAAE;UACxB,IAAI,CAACxF,GAAG,CAACyF,YAAY,EAAE;QACzB,CAAC,CACF,CACF;MACH;MACA,IAAI,CAACD,iBAAiB,EAAE;IAC1B;IAEQzD,YAAYA,CAAA;MAClB,IAAI,CAACnE,QAAQ,GAAG,IAAI,CAACuC,YAAY,CAACuF,aAAa,EAAE,IAAI,MAAM;MAC3D,IAAI,CAACvJ,SAAS,GAAI,IAAI,CAACgE,YAAoB,CAACwF,cAAc,GAAE,CAAE,IAAI,EAAE,CAAC,CAAC;MACtE,IAAI,CAACzJ,eAAe,GACjB,IAAI,CAACiE,YAAoB,CAACyF,oBAAoB,GAAE,CAAE,IAAI,UAAU,CAAC,CAAC;MACrE,IAAI,CAACC,kBAAkB,EAAE;IAC3B;IAEQA,kBAAkBA,CAAA;MACxB;MACA,IAAI,IAAI,CAACjI,QAAQ,EAAE;QACjB,MAAMkI,SAAS,GAAG,IAAI,CAAClI,QAAQ,CAACmI,IAAI,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;QACjD,IAAIC,QAAQ,GAAG,EAAE;QAEjB,IAAIH,SAAS,CAACjH,MAAM,IAAI,CAAC,EAAE;UACzB;UACAoH,QAAQ,GAAGH,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,SAAS,CAACA,SAAS,CAACjH,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACjE,CAAC,MAAM,IAAIiH,SAAS,CAACjH,MAAM,KAAK,CAAC,EAAE;UACjC;UACAoH,QAAQ,GAAGH,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,MAAM;UACLG,QAAQ,GAAG,GAAG,CAAC,CAAC;QAClB;QAEAA,QAAQ,GAAGA,QAAQ,CAACC,WAAW,EAAE;QAEjC;QACA,MAAMC,MAAM,GAAG,CACb,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;QACD,MAAMC,UAAU,GAAG,IAAI,CAACxI,QAAQ,CAACiB,MAAM,GAAGsH,MAAM,CAACtH,MAAM;QACvD,MAAMwH,eAAe,GAAGF,MAAM,CAACC,UAAU,CAAC;QAE1C,IAAI,CAACzI,UAAU,GAAG,6BAA6B2I,IAAI,CAAC;;iDAETD,eAAe;0IAC0EJ,QAAQ;;OAE3I,CAAC,EAAE;MACN;IACF;IAEQT,iBAAiBA,CAAA;MACvB;MACA,IAAI,CAACvJ,aAAa,GAChB,IAAI,CAACK,YAAY,KAAK,OAAO,GACzB,oDAAoD,GACpD,qDAAqD;IAC7D;IAEA;IACA4F,qBAAqBA,CAAA;MACnB,MAAMqE,IAAI,GAAG,IAAI,CAACpG,YAAY,CAACqG,SAAS,CAAC,UAAU,CAAC;MACpD,IAAID,IAAI,EAAE;QACR,MAAME,KAAK,GAAGF,IAAI,CAACP,KAAK,CAAC,IAAI,CAAC;QAC9B,MAAMU,WAAW,GAAGD,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE;QAClC,MAAME,aAAa,GAAGF,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE;QAEpC;QACA,MAAMG,GAAG,GAAGD,aAAa,CAACX,KAAK,CAAC,GAAG,CAAC,CAACa,GAAG,CAACC,MAAM,CAAC;QAEhD;QACA,IAAI,CAAC3N,gBAAgB,CAAC4N,UAAU,CAAC;UAC/BvC,GAAG,EAAEoC,GAAG,CAAC,CAAC,CAAC,EAAEI,QAAQ,EAAE,IAAI,EAAE;UAC7BtC,MAAM,EAAEkC,GAAG,CAAC,CAAC,CAAC,EAAEI,QAAQ,EAAE,IAAI,EAAE;UAChCrC,OAAO,EAAEiC,GAAG,CAAC,CAAC,CAAC,EAAEI,QAAQ,EAAE,IAAI,EAAE;UACjCpC,IAAI,EAAEgC,GAAG,CAAC,CAAC,CAAC,EAAEI,QAAQ,EAAE,IAAI;SAC7B,CAAC;QAEF;QACA,IAAI,CAACxN,WAAW,GAAGoN,GAAG,CAAC,CAAC,CAAC,EAAEI,QAAQ,EAAE,IAAI,EAAE;QAC3C,IAAI,CAACrN,cAAc,GAAGiN,GAAG,CAAC,CAAC,CAAC,EAAEI,QAAQ,EAAE,IAAI,EAAE;QAC9C,IAAI,CAAClN,eAAe,GAAG8M,GAAG,CAAC,CAAC,CAAC,EAAEI,QAAQ,EAAE,IAAI,EAAE;QAC/C,IAAI,CAAC9F,YAAY,GAAG0F,GAAG,CAAC,CAAC,CAAC,EAAEI,QAAQ,EAAE,IAAI,EAAE;QAE5C;QACA,MAAMC,SAAS,GAAGP,WAAW,CAACV,KAAK,CAAC,GAAG,CAAC;QACxC,IAAI,CAAC3M,eAAe,GAAG4N,SAAS,CAAC,CAAC,CAAC,IAAI,EAAE;QACzC,IAAI,CAAC1N,kBAAkB,GAAG0N,SAAS,CAAC,CAAC,CAAC,IAAI,EAAE;QAC5C,IAAI,CAACvN,mBAAmB,GAAGuN,SAAS,CAAC,CAAC,CAAC,IAAI,EAAE;QAC7C,IAAI,CAACpN,gBAAgB,GAAGoN,SAAS,CAAC,CAAC,CAAC,IAAI,EAAE;QAE1C;QACA,IAAI,CAACC,QAAQ,EAAE;MACjB,CAAC,MAAM;QACL,IAAI,CAACA,QAAQ,EAAE;MACjB;IACF;IAEAA,QAAQA,CAAA;MACN,IAAI,IAAI,CAAC3G,gBAAgB,EAAE;QACzB,IAAI,CAACA,gBAAgB,CAAC4G,wBAAwB,EAAE,CAAClC,SAAS,CAAC;UACzDmC,IAAI,EAAGC,IAAoB,IAAI;YAC7B,IAAI,CAAClG,aAAa,GAAGkG,IAAI;YACzB,IAAI,CAACC,iBAAiB,EAAE;YAExB;YACA,IAAI,IAAI,CAAC9N,WAAW,EAAE;cACpB,IAAI,CAAC+N,WAAW,CAAC,IAAI,CAAC/N,WAAW,CAAC;cAClC,IAAI,IAAI,CAACG,cAAc,EAAE;gBACvB,IAAI,CAAC6N,YAAY,CAAC,IAAI,CAAC7N,cAAc,CAAC;gBACtC,IAAI,IAAI,CAACG,eAAe,EAAE;kBACxB,IAAI,CAAC2N,SAAS,CAAC,IAAI,CAAC3N,eAAe,CAAC;gBACtC;cACF;YACF;UACF,CAAC;UACD4N,KAAK,EAAGA,KAAU,IAAI;YACpBjF,OAAO,CAACiF,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;UAC1D;SACD,CAAC;MACJ;IACF;IAEAJ,iBAAiBA,CAAA;MACf,IAAI,CAAClO,UAAU,GAAG,IAAI,CAAC+H,aAAa,CAAC0F,GAAG,CAAErC,GAAG,KAAM;QACjDlJ,IAAI,EAAEkJ,GAAG,CAACmD,gBAAgB;QAC1BC,KAAK,EAAEpD,GAAG,CAACqD,KAAK,CAACb,QAAQ;OAC1B,CAAC,CAAC;IACL;IAEAzO,WAAWA,CAACyM,KAAU;MACpB,MAAM8C,aAAa,GAAG9C,KAAK,CAAC+C,eAAe,GAAG,CAAC,CAAC,EAAEH,KAAK;MACvD,MAAMvO,eAAe,GAAG2L,KAAK,CAAC+C,eAAe,GAAG,CAAC,CAAC,EAAEzM,IAAI;MACxD,IAAIwM,aAAa,EAAE;QACjB,IAAI,CAACtO,WAAW,GAAGsO,aAAa;QAChC,IAAI,CAACzO,eAAe,GAAGA,eAAe;QACtC,IAAI,CAACF,gBAAgB,CAAC4N,UAAU,CAAC;UAAEvC,GAAG,EAAEsD;QAAa,CAAE,CAAC;QACxD,IAAI,CAACP,WAAW,CAACO,aAAa,CAAC;QAC/B;QACA,IAAI,CAAC3O,gBAAgB,CAAC4N,UAAU,CAAC;UAAErC,MAAM,EAAE,EAAE;UAAEC,OAAO,EAAE,EAAE;UAAEC,IAAI,EAAE;QAAE,CAAE,CAAC;QACvE,IAAI,CAACjL,cAAc,GAAG,EAAE;QACxB,IAAI,CAACG,eAAe,GAAG,EAAE;QACzB,IAAI,CAACoH,YAAY,GAAG,EAAE;QACtB,IAAI,CAAC3H,kBAAkB,GAAG,EAAE;QAC5B,IAAI,CAACG,mBAAmB,GAAG,EAAE;QAC7B,IAAI,CAACG,gBAAgB,GAAG,EAAE;QAC1B,IAAI,CAACJ,cAAc,GAAG,EAAE;QACxB,IAAI,CAACG,WAAW,GAAG,EAAE;MACvB;IACF;IAEA2N,WAAWA,CAACM,KAAa;MACvB,MAAMrD,GAAG,GAAG,IAAI,CAACrD,aAAa,CAAC6G,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACJ,KAAK,CAACb,QAAQ,EAAE,KAAKa,KAAK,CAAC;MACxE,IAAIrD,GAAG,EAAE;QACP,IAAI,CAAClL,aAAa,GAAGkL,GAAG,CAAC0D,OAAO,CAACrB,GAAG,CAAEnC,MAAM,KAAM;UAChDpJ,IAAI,EAAEoJ,MAAM,CAACyD,UAAU;UACvBP,KAAK,EAAElD,MAAM,CAAC0D,QAAQ,CAACpB,QAAQ;SAChC,CAAC,CAAC;MACL,CAAC,MAAM;QACL,IAAI,CAAC1N,aAAa,GAAG,EAAE;MACzB;IACF;IAEAb,cAAcA,CAACuM,KAAU;MACvB,MAAMqD,gBAAgB,GAAGrD,KAAK,CAAC+C,eAAe,GAAG,CAAC,CAAC,EAAEH,KAAK;MAC1D,MAAMrO,kBAAkB,GAAGyL,KAAK,CAAC+C,eAAe,GAAG,CAAC,CAAC,EAAEzM,IAAI;MAC3D,IAAI+M,gBAAgB,EAAE;QACpB,IAAI,CAAC1O,cAAc,GAAG0O,gBAAgB;QACtC,IAAI,CAAC9O,kBAAkB,GAAGA,kBAAkB;QAC5C,IAAI,CAACJ,gBAAgB,CAAC4N,UAAU,CAAC;UAAErC,MAAM,EAAE2D;QAAgB,CAAE,CAAC;QAC9D,IAAI,CAACb,YAAY,CAACa,gBAAgB,CAAC;QACnC;QACA,IAAI,CAAClP,gBAAgB,CAAC4N,UAAU,CAAC;UAAEpC,OAAO,EAAE,EAAE;UAAEC,IAAI,EAAE;QAAE,CAAE,CAAC;QAC3D,IAAI,CAAC9K,eAAe,GAAG,EAAE;QACzB,IAAI,CAACoH,YAAY,GAAG,EAAE;QACtB,IAAI,CAACxH,mBAAmB,GAAG,EAAE;QAC7B,IAAI,CAACG,gBAAgB,GAAG,EAAE;QAC1B,IAAI,CAACD,WAAW,GAAG,EAAE;MACvB;IACF;IAEA4N,YAAYA,CAACY,QAAgB;MAC3B,MAAM5D,GAAG,GAAG,IAAI,CAACrD,aAAa,CAAC6G,IAAI,CAAEC,CAAC,IACpCA,CAAC,CAACC,OAAO,CAAC9C,IAAI,CAAEkD,CAAC,IAAKA,CAAC,CAACF,QAAQ,CAACpB,QAAQ,EAAE,KAAKoB,QAAQ,CAAC,CAC1D;MACD,IAAI5D,GAAG,EAAE;QACP,MAAME,MAAM,GAAGF,GAAG,CAAC0D,OAAO,CAACF,IAAI,CAC5BM,CAAC,IAAKA,CAAC,CAACF,QAAQ,CAACpB,QAAQ,EAAE,KAAKoB,QAAQ,CAC1C;QACD,IAAI1D,MAAM,EAAE;UACV,IAAI,CAACjL,cAAc,GAAGiL,MAAM,CAAC6D,QAAQ,CAAC1B,GAAG,CAAElC,OAAO,KAAM;YACtDrJ,IAAI,EAAEqJ,OAAO,CAAC9B,WAAW;YACzB+E,KAAK,EAAEjD,OAAO,CAAC6D,SAAS,CAACxB,QAAQ;WAClC,CAAC,CAAC;QACL,CAAC,MAAM;UACL,IAAI,CAACvN,cAAc,GAAG,EAAE;QAC1B;MACF,CAAC,MAAM;QACL,IAAI,CAACA,cAAc,GAAG,EAAE;MAC1B;IACF;IAEAd,eAAeA,CAACqM,KAAU;MACxB,MAAMyD,iBAAiB,GAAGzD,KAAK,CAAC+C,eAAe,GAAG,CAAC,CAAC,EAAEH,KAAK;MAC3D,MAAMlO,mBAAmB,GAAGsL,KAAK,CAAC+C,eAAe,GAAG,CAAC,CAAC,EAAEzM,IAAI;MAC5D,IAAImN,iBAAiB,EAAE;QACrB,IAAI,CAAC3O,eAAe,GAAG2O,iBAAiB;QACxC,IAAI,CAAC/O,mBAAmB,GAAGA,mBAAmB;QAC9C,IAAI,CAACP,gBAAgB,CAAC4N,UAAU,CAAC;UAAEpC,OAAO,EAAE8D;QAAiB,CAAE,CAAC;QAChE,IAAI,CAAChB,SAAS,CAACgB,iBAAiB,CAAC;QACjC;QACA,IAAI,CAACtP,gBAAgB,CAAC4N,UAAU,CAAC;UAAEnC,IAAI,EAAE;QAAE,CAAE,CAAC;QAC9C,IAAI,CAAC1D,YAAY,GAAG,EAAE;QACtB,IAAI,CAACrH,gBAAgB,GAAG,EAAE;MAC5B;IACF;IAEA4N,SAASA,CAACe,SAAiB;MACzB,MAAMhE,GAAG,GAAG,IAAI,CAACrD,aAAa,CAAC6G,IAAI,CAAEC,CAAC,IACpCA,CAAC,CAACC,OAAO,CAAC9C,IAAI,CAAEkD,CAAC,IACfA,CAAC,CAACC,QAAQ,CAACnD,IAAI,CAAEsD,CAAC,IAAKA,CAAC,CAACF,SAAS,CAACxB,QAAQ,EAAE,KAAKwB,SAAS,CAAC,CAC7D,CACF;MACD,IAAIhE,GAAG,EAAE;QACP,MAAME,MAAM,GAAGF,GAAG,CAAC0D,OAAO,CAACF,IAAI,CAAEM,CAAC,IAChCA,CAAC,CAACC,QAAQ,CAACnD,IAAI,CAAEsD,CAAC,IAAKA,CAAC,CAACF,SAAS,CAACxB,QAAQ,EAAE,KAAKwB,SAAS,CAAC,CAC7D;QACD,IAAI9D,MAAM,EAAE;UACV,MAAMC,OAAO,GAAGD,MAAM,CAAC6D,QAAQ,CAACP,IAAI,CACjCU,CAAC,IAAKA,CAAC,CAACF,SAAS,CAACxB,QAAQ,EAAE,KAAKwB,SAAS,CAC5C;UACD,IAAI7D,OAAO,EAAE;YACX,IAAI,CAAC/K,WAAW,GAAG+K,OAAO,CAACgE,KAAK,CAAC9B,GAAG,CAAEjC,IAAI,KAAM;cAC9CtJ,IAAI,EAAEsJ,IAAI,CAACgE,QAAQ;cACnBhB,KAAK,EAAEhD,IAAI,CAACiE,MAAM,CAAC7B,QAAQ;aAC5B,CAAC,CAAC;UACL,CAAC,MAAM;YACL,IAAI,CAACpN,WAAW,GAAG,EAAE;UACvB;QACF,CAAC,MAAM;UACL,IAAI,CAACA,WAAW,GAAG,EAAE;QACvB;MACF,CAAC,MAAM;QACL,IAAI,CAACA,WAAW,GAAG,EAAE;MACvB;IACF;IAEAf,YAAYA,CAACmM,KAAU;MACrB,MAAM8D,cAAc,GAAG9D,KAAK,CAAC+C,eAAe,GAAG,CAAC,CAAC,EAAEH,KAAK;MACxD,MAAM/N,gBAAgB,GAAGmL,KAAK,CAAC+C,eAAe,GAAG,CAAC,CAAC,EAAEzM,IAAI;MACzD,IAAIwN,cAAc,EAAE;QAClB,IAAI,CAAC5H,YAAY,GAAG4H,cAAc;QAClC,IAAI,CAACjP,gBAAgB,GAAGA,gBAAgB;QACxC,IAAI,CAACV,gBAAgB,CAAC4N,UAAU,CAAC;UAAEnC,IAAI,EAAEkE;QAAc,CAAE,CAAC;MAC5D;IACF;IAEA,IAAItO,QAAQA,CAAA;MACV;MACA,MAAMuO,OAAO,GAAG,IAAI,CAAC5I,YAAY,CAACqG,SAAS,CAAC,UAAU,CAAC;MACvD,IAAIuC,OAAO,EAAE;QACX,MAAMC,OAAO,GAAGD,OAAO,CAAC/C,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACpD,IAAIgD,OAAO,EAAE,OAAOA,OAAO;MAC7B;MACA;MACA,OACE,IAAI,CAAC5P,UAAU,CAAC4O,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACL,KAAK,KAAK,IAAI,CAACpO,WAAW,CAAC,EAAE8B,IAAI,IAC/D,qBAAqB;IAEzB;IAEArC,mBAAmBA,CAAA;MACjB,IAAI,IAAI,CAACE,gBAAgB,CAACY,KAAK,EAAE;QAC/B,MAAMkP,SAAS,GAAG,IAAI,CAAC9P,gBAAgB,CAACyO,KAAK;QAE7C;QACA,MAAMmB,OAAO,GAAG,GAAG,IAAI,CAAC1P,eAAe,IAAI,IAAI,CAACE,kBAAkB,IAAI,IAAI,CAACG,mBAAmB,IAAI,IAAI,CAACG,gBAAgB,KAAKoP,SAAS,CAACzE,GAAG,IAAIyE,SAAS,CAACvE,MAAM,IAAIuE,SAAS,CAACtE,OAAO,IAAIsE,SAAS,CAACrE,IAAI,EAAE;QAEtM;QACA,IAAI,CAACzE,YAAY,CAAC+I,SAAS,CAAC,UAAU,EAAEH,OAAO,CAAC;QAEhD;QACA,IAAI,CAAC9H,eAAe,CAACkI,IAAI,CAAC;UACxBJ,OAAO;UACPK,cAAc,EAAE;YACd5E,GAAG,EAAE,IAAI,CAAChL,WAAW;YACrBkL,MAAM,EAAE,IAAI,CAAC/K,cAAc;YAC3BgL,OAAO,EAAE,IAAI,CAAC7K,eAAe;YAC7B8K,IAAI,EAAE,IAAI,CAAC1D;WACZ;UACDmI,aAAa,EAAE;YACb7E,GAAG,EAAE,IAAI,CAACnL,eAAe;YACzBqL,MAAM,EAAE,IAAI,CAACnL,kBAAkB;YAC/BoL,OAAO,EAAE,IAAI,CAACjL,mBAAmB;YACjCkL,IAAI,EAAE,IAAI,CAAC/K;;SAEd,CAAC;QAEF,IAAI,CAACd,cAAc,EAAE;MACvB;IACF;IAEA;IACA7B,UAAUA,CAACS,KAAa;MACtB,IAAI,CAACoI,MAAM,CAACuJ,QAAQ,CAAC,CAAC3R,KAAK,CAAC,CAAC;MAC7B,IAAI,CAACiJ,eAAe,CAACuI,IAAI,CAACxR,KAAK,CAAC;IAClC;IAEAP,cAAcA,CAACR,KAAa;MAC1B,IAAI,CAACmG,MAAM,CAAC+B,QAAQ,CAACyK,OAAO,CAAC,CAACC,IAAI,EAAEC,CAAC,KAAI;QACvCD,IAAI,CAAC5R,QAAQ,GAAG6R,CAAC,KAAK7S,KAAK;MAC7B,CAAC,CAAC;IACJ;IAEAG,cAAcA,CAACH,KAAa;MAC1B,IAAI,CAACmG,MAAM,CAAC+B,QAAQ,CAAClI,KAAK,CAAC,CAACkB,YAAY,GACtC,CAAC,IAAI,CAACiF,MAAM,CAAC+B,QAAQ,CAAClI,KAAK,CAAC,CAACkB,YAAY;IAC7C;IAEAR,sBAAsBA,CACpB0N,KAAuC,EACvC0E,WAAmB;MAEnB,IAAI,CAACxS,UAAU,CAAC8N,KAAK,CAACrN,KAAK,CAAC;MAC5B,IAAI,CAACkJ,oBAAoB,CAACsI,IAAI,CAACnE,KAAK,CAAC;MACrC,IAAI,CAAC5N,cAAc,CAACsS,WAAW,CAAC;MAChC,IAAI,CAACC,mBAAmB,EAAE;IAC5B;IAEAnS,oBAAoBA,CAACwN,KAKpB;MACC,IAAI,CAACvF,cAAc,GAAG;QACpB6B,IAAI,EAAE,IAAI;QACV5B,IAAI,EAAEsF,KAAK,CAACtF,IAAI;QAChBG,KAAK,EAAEmF,KAAK,CAACnF,KAAK;QAClB0B,WAAW,EAAEyD,KAAK,CAACzD,WAAW;QAC9BC,SAAS,EAAEwD,KAAK,CAACxD;OAClB;IACH;IAEAmI,mBAAmBA,CAAA;MACjB,IAAI,CAAClK,cAAc,CAAC6B,IAAI,GAAG,KAAK;MAChC,IAAI,CAACvE,MAAM,CAAC+B,QAAQ,CAACyK,OAAO,CAAEC,IAAI,IAAMA,IAAI,CAAC1R,YAAY,GAAG,KAAM,CAAC;IACrE;IAEA;IACAqF,qBAAqBA,CAAA;MACnB,IAAI,CAACO,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;IACtD;IAEAD,MAAMA,CAAA;MACJ,IAAI,CAAC2C,WAAW,CAAC3C,MAAM,EAAE;MACzB,IAAI,CAACqD,aAAa,CAACqI,IAAI,CAAC,QAAQ,CAAC;IACnC;IAEA;IACAnN,WAAWA,CAAA;MACT,IAAI,IAAI,CAACwE,YAAY,IAAI,IAAI,CAACA,YAAY,CAACxE,WAAW,EAAE;QACtD;QACA,IAAI,CAACwE,YAAY,CAACxE,WAAW,EAAE;MACjC,CAAC,MAAM;QACL;QACA,MAAM4N,QAAQ,GAAG,IAAI,CAACtN,YAAY,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO;QACjE,IAAI,CAACA,YAAY,GAAGsN,QAAQ;QAC5B,IAAI,CAACpE,iBAAiB,EAAE;QACxB,IAAI,CAACzE,WAAW,CAACoI,IAAI,CAACS,QAAQ,CAAC;MACjC;IACF;IAEA;IACAjN,cAAcA,CAACkN,YAAoB;MACjC,IAAI,CAAChN,eAAe,GAAGgN,YAAY;MACnC,IAAI,CAAC7I,cAAc,CAACmI,IAAI,CAACU,YAAY,CAAC;IACxC;IAEAC,sBAAsBA,CAAA;MACpB,MAAMC,QAAQ,GAAG,IAAI,CAAChN,MAAM,CAACC,kBAAkB,EAAEgL,IAAI,CAClDgC,IAAI,IAAKA,IAAI,CAACpN,IAAI,KAAK,IAAI,CAACC,eAAe,CAC7C;MACD,OAAOkN,QAAQ,EAAEzO,IAAI,IAAI,SAAS;IACpC;IAEA;IACAG,eAAeA,CAAA;MACb,IAAI,CAACG,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;MAE5C,IAAI,IAAI,CAACA,eAAe,EAAE;QACxB;QACAmI,UAAU,CAAC,MAAK;UACd,IAAI,CAACkG,0BAA0B,EAAE;QACnC,CAAC,CAAC;MACJ;IACF;IAEArP,cAAcA,CAAA;MACZ,IAAI,CAACgB,eAAe,GAAG,KAAK;IAC9B;IAEAT,aAAaA,CAAC+O,GAAc;MAC1B,IAAIA,GAAG,CAACvS,KAAK,CAACwS,UAAU,CAAC,MAAM,CAAC,EAAE;QAChC;QACAC,MAAM,CAAC9I,IAAI,CAAC4I,GAAG,CAACvS,KAAK,EAAE,QAAQ,CAAC;MAClC,CAAC,MAAM;QACL;QACA,IAAI,CAACoI,MAAM,CAACuJ,QAAQ,CAAC,CAACY,GAAG,CAACvS,KAAK,CAAC,CAAC;MACnC;MACA,IAAI,CAACiD,cAAc,EAAE;MACrB,IAAI,CAACgG,eAAe,CAACuI,IAAI,CAACe,GAAG,CAACvS,KAAK,CAAC;IACtC;IAEAkE,eAAeA,CAAA;MACb,IAAI,CAAC,IAAI,CAACkB,MAAM,CAACuH,aAAa,EAAE,OAAO,EAAE;MAEzC;MACA,OAAO,IAAI,CAACvH,MAAM,CAACuH,aAAa,CAACpP,MAAM,CACpCgV,GAAG,IAAKA,GAAG,CAAC5O,IAAI,KAAK,IAAI,CAACyB,MAAM,CAACsH,UAAU,CAC7C;IACH;IAEA;IACAjK,eAAeA,CAAA;MACb,IAAI,IAAI,CAAC2C,MAAM,CAACgC,eAAe,EAAE;QAC/B,IAAI,CAACtE,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;QAE5C,IAAI,IAAI,CAACA,eAAe,EAAE;UACxB;UACAsJ,UAAU,CAAC,MAAK;YACd,IAAI,CAACsG,wBAAwB,EAAE;UACjC,CAAC,CAAC;QACJ;MACF;IACF;IAEAtR,cAAcA,CAAA;MACZ,IAAI,CAAC0B,eAAe,GAAG,KAAK;IAC9B;IAEQ4P,wBAAwBA,CAAA;MAC9B,IAAI,IAAI,CAACjJ,cAAc,IAAI,IAAI,CAACC,UAAU,EAAE;QAC1C,MAAMiJ,WAAW,GACf,IAAI,CAAClJ,cAAc,CAACmJ,aAAa,CAACC,qBAAqB,EAAE;QAC3D,MAAMC,WAAW,GAAG,IAAI,CAACpJ,UAAU,CAACkJ,aAAa,CAACC,qBAAqB,EAAE;QACzE,MAAME,aAAa,GAAGN,MAAM,CAACO,UAAU;QAEvC;QACA,IAAIL,WAAW,CAAC1K,IAAI,GAAG6K,WAAW,CAACG,KAAK,GAAGF,aAAa,GAAG,EAAE,EAAE;UAC7D,IAAI,CAACxR,YAAY,GAAG,OAAO;QAC7B,CAAC,MAAM;UACL,IAAI,CAACA,YAAY,GAAG,MAAM;QAC5B;MACF;IACF;IAEQ+Q,0BAA0BA,CAAA;MAChC;MACA,MAAMY,iBAAiB,GAAG,IAAI,CAAC3K,UAAU,CAACqK,aAAa,CAACjH,aAAa,CACnE,sBAAsB,CACvB;MACD,IAAIuH,iBAAiB,EAAE;QACrB,MAAMC,YAAY,GAAGD,iBAAiB,CAACL,qBAAqB,EAAE;QAC9D,MAAME,aAAa,GAAGN,MAAM,CAACO,UAAU;QAEvC;QACA,IAAIG,YAAY,CAACC,KAAK,GAAGL,aAAa,GAAG,EAAE,EAAE;UAC3C,MAAMM,QAAQ,GAAGF,YAAY,CAACC,KAAK,GAAGL,aAAa,GAAG,EAAE;UACxDG,iBAAiB,CAACI,KAAK,CAACF,KAAK,GAAG,GAAGC,QAAQ,IAAI;QACjD,CAAC,MAAM;UACLH,iBAAiB,CAACI,KAAK,CAACF,KAAK,GAAG,KAAK;QACvC;MACF;IACF;IAEA;IACAG,cAAcA,CAAC1B,IAAmB,EAAE5S,KAAa,EAAEoO,KAAiB;MAClE,IAAIwE,IAAI,CAACrR,QAAQ,EAAE;QACjB6M,KAAK,CAACmG,cAAc,EAAE;QACtBnG,KAAK,CAACoG,eAAe,EAAE;QACvB;MACF;MAEA,IAAI5B,IAAI,CAAC3R,WAAW,IAAI2R,IAAI,CAACzR,aAAa,EAAE;QAC1CiN,KAAK,CAACoG,eAAe,EAAE;QACvB,MAAM1L,IAAI,GAAIsF,KAAK,CAACqG,aAA6B,CAACb,qBAAqB,EAAE;QACzE,IAAI,CAAChT,oBAAoB,CAAC;UACxBkI,IAAI;UACJG,KAAK,EAAE2J,IAAI,CAACzR,aAAa;UACzBwJ,WAAW,EAAEiI,IAAI,CAAC9R,KAAK;UACvB8J,SAAS,EAAEgI,IAAI,CAAC9R;SACjB,CAAC;QACF,IAAI,CAACX,cAAc,CAACH,KAAK,CAAC;MAC5B,CAAC,MAAM;QACL,IAAI,CAACM,UAAU,CAACsS,IAAI,CAAC7R,KAAK,CAAC;QAC3B,IAAI,CAACP,cAAc,CAACR,KAAK,CAAC;MAC5B;IACF;IAEA;IACAwL,2BAA2BA,CAACC,GAAW;MACrC;MACA,IAAI,CAACtF,MAAM,CAAC+B,QAAQ,CAACyK,OAAO,CAAEC,IAAI,IAAI;QACpCA,IAAI,CAAC5R,QAAQ,GAAG,KAAK;MACvB,CAAC,CAAC;MAEF;MACA,MAAM0T,UAAU,GAAG,IAAI,CAACvO,MAAM,CAAC+B,QAAQ,CAACkJ,IAAI,CAAEwB,IAAI,IAAI;QACpD;QACA,IAAInH,GAAG,KAAKmH,IAAI,CAAC7R,KAAK,EAAE;UACtB,OAAO,IAAI;QACb;QAEA;QACA,IAAI6R,IAAI,CAAC3R,WAAW,IAAI2R,IAAI,CAACzR,aAAa,EAAE;UAC1C;UACA;UACA,OACEsK,GAAG,CAAC8H,UAAU,CAACX,IAAI,CAAC7R,KAAK,GAAG,GAAG,CAAC,IAChC6R,IAAI,CAACzR,aAAa,CAACqN,IAAI,CAAEmG,KAAK,IAAKlJ,GAAG,KAAKkJ,KAAK,CAAC5T,KAAK,CAAC;QAE3D;QAEA;QACA,IAAI,CAAC6R,IAAI,CAAC3R,WAAW,IAAI2R,IAAI,CAACzR,aAAa,EAAE;UAC3C,OAAOyR,IAAI,CAACzR,aAAa,CAACqN,IAAI,CAAEmG,KAAK,IAAKlJ,GAAG,KAAKkJ,KAAK,CAAC5T,KAAK,CAAC;QAChE;QAEA,OAAO,KAAK;MACd,CAAC,CAAC;MAEF,IAAI2T,UAAU,EAAE;QACdA,UAAU,CAAC1T,QAAQ,GAAG,IAAI;MAC5B,CAAC,MAAM;QACL;QACA,MAAM4T,WAAW,GAAG,IAAI,CAACzO,MAAM,CAAC+B,QAAQ,CAACkJ,IAAI,CAAEwB,IAAI,IAAK,CAACA,IAAI,CAACrR,QAAQ,CAAC;QACvE,IAAIqT,WAAW,EAAE;UACfA,WAAW,CAAC5T,QAAQ,GAAG,IAAI;QAC7B;MACF;IACF;IAEA;IAEA6T,eAAeA,CAACzG,KAAiB;MAC/B,MAAM0G,MAAM,GAAG1G,KAAK,CAAC0G,MAAqB;MAE1C;MACA,IACE,IAAI,CAAChO,mBAAmB,IACxB,CAAC,IAAI,CAACwC,UAAU,CAACqK,aAAa,CAACoB,QAAQ,CAACD,MAAM,CAAC,EAC/C;QACA,IAAI,CAAChO,mBAAmB,GAAG,KAAK;MAClC;MAEA;MACA,IACE,IAAI,CAACjD,eAAe,IACpB,CAACiR,MAAM,CAACE,OAAO,CAAC,8BAA8B,CAAC,EAC/C;QACA,IAAI,CAAC7S,cAAc,EAAE;MACvB;MAEA;MACA,IAAI,IAAI,CAAC6C,eAAe,IAAI,CAAC8P,MAAM,CAACE,OAAO,CAAC,uBAAuB,CAAC,EAAE;QACpE,IAAI,CAAChR,cAAc,EAAE;MACvB;MAEA;MACA,IACE,IAAI,CAAC6E,cAAc,CAAC6B,IAAI,IACxB,CAACoK,MAAM,CAACE,OAAO,CAAC,uBAAuB,CAAC,IACxC,CAACF,MAAM,CAACE,OAAO,CAAC,mBAAmB,CAAC,EACpC;QACA,IAAI,CAACjC,mBAAmB,EAAE;MAC5B;IACF;;uCA95BW7J,wBAAwB,EAAApK,EAAA,CAAAmW,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAArW,EAAA,CAAAmW,iBAAA,CAAAnW,EAAA,CAAAsW,iBAAA,GAAAtW,EAAA,CAAAmW,iBAAA,CAAAnW,EAAA,CAAAuW,SAAA,GAAAvW,EAAA,CAAAmW,iBAAA,CAAAnW,EAAA,CAAAwW,UAAA,GAAAxW,EAAA,CAAAmW,iBAAA,CAAAM,EAAA,CAAAC,mBAAA,GAAA1W,EAAA,CAAAmW,iBAAA,CAAAQ,EAAA,CAAAC,WAAA,GAAA5W,EAAA,CAAAmW,iBAAA,CAAAU,EAAA,CAAAC,WAAA;IAAA;;YAAxB1M,wBAAwB;MAAA2M,SAAA;MAAAC,SAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;UAAxBlX,EAAA,CAAAa,UAAA,mBAAAuW,kDAAA7V,MAAA;YAAA,OAAA4V,GAAA,CAAApB,eAAA,CAAAxU,MAAA,CAAuB;UAAA,UAAAvB,EAAA,CAAAqX,iBAAA,CAAC;;;;;;;;;;;;;;;;;;;;;;UCqPrCrX,EA9WA,CAAAO,UAAA,IAAA+W,uCAAA,mBAA0B,IAAAC,uCAAA,iBAmXzB;;;UAnXKvX,EAAA,CAAAW,UAAA,UAAAwW,GAAA,CAAApM,WAAA,CAAkB;UA+WrB/K,EAAA,CAAAU,SAAA,EAAgD;UAAhDV,EAAA,CAAAW,UAAA,SAAAwW,GAAA,CAAApN,cAAA,CAAA6B,IAAA,IAAAuL,GAAA,CAAApN,cAAA,CAAAC,IAAA,CAAgD;;;qBDjQ/C1K,eAAe,EACfD,YAAY,EAAAmY,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EACZ9X,sBAAsB,EACtBH,iBAAiB,EACjBE,eAAe,EACfG,mBAAmB,EAAA8W,EAAA,CAAAe,aAAA,EAAAf,EAAA,CAAAgB,oBAAA,EAAAhB,EAAA,CAAAiB,kBAAA,EACnBnY,aAAa;MAAAoY,MAAA;IAAA;;SAKJ3N,wBAAwB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}