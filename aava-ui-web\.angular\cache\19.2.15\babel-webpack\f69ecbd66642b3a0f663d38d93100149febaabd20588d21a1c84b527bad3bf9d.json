{"ast": null, "code": "export default function (input, init) {\n  return new Promise(function (resolve, reject) {\n    var image = new Image();\n    for (var key in init) image[key] = init[key];\n    image.onerror = reject;\n    image.onload = function () {\n      resolve(image);\n    };\n    image.src = input;\n  });\n}", "map": {"version": 3, "names": ["input", "init", "Promise", "resolve", "reject", "image", "Image", "key", "onerror", "onload", "src"], "sources": ["C:/console/aava-ui-web/node_modules/d3-fetch/src/image.js"], "sourcesContent": ["export default function(input, init) {\n  return new Promise(function(resolve, reject) {\n    var image = new Image;\n    for (var key in init) image[key] = init[key];\n    image.onerror = reject;\n    image.onload = function() { resolve(image); };\n    image.src = input;\n  });\n}\n"], "mappings": "AAAA,eAAe,UAASA,KAAK,EAAEC,IAAI,EAAE;EACnC,OAAO,IAAIC,OAAO,CAAC,UAASC,OAAO,EAAEC,MAAM,EAAE;IAC3C,IAAIC,KAAK,GAAG,IAAIC,KAAK,CAAD,CAAC;IACrB,KAAK,IAAIC,GAAG,IAAIN,IAAI,EAAEI,KAAK,CAACE,GAAG,CAAC,GAAGN,IAAI,CAACM,GAAG,CAAC;IAC5CF,KAAK,CAACG,OAAO,GAAGJ,MAAM;IACtBC,KAAK,CAACI,MAAM,GAAG,YAAW;MAAEN,OAAO,CAACE,KAAK,CAAC;IAAE,CAAC;IAC7CA,KAAK,CAACK,GAAG,GAAGV,KAAK;EACnB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}