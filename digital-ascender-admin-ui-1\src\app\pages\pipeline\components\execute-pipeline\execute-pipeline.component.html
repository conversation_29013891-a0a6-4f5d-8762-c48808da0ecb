<app-toast-messages></app-toast-messages>
<div class="agent-container">
    <div class="container__mainContent--body">
            <div class="agents-container">
                <ng-container *ngFor="let agent of pipelineAgents; let i = index">
                    <div class="agent">
                        <div class="agent-header">
                            <div class="agent-name">{{ agent?.agent.name }}</div>
                        </div>
                        <div class="agent-underline"></div>
                        <div class="agent-details">
                            <div class="agent-detail-item" *ngIf="extractedData[i]?.modelDeploymentName">
                                <collab-comp-img-generator altText="LLM Model" imageType="image"
                                    [imgUrl]="'assets/icons/LLM.svg'" class="detail-icon">
                                </collab-comp-img-generator>
                                <span class="detail-text truncate">{{ extractedData[i]?.modelDeploymentName
                                    }}</span>
                            </div>
                            <div class="agent-detail-item" *ngIf="extractedData[i]?.combinedToolNames?.length">
                                <collab-comp-img-generator altText="Tools" imageType="image"
                                    [imgUrl]="'assets/icons/tool.svg'" class="detail-icon">
                                </collab-comp-img-generator>
                                <span class="detail-text truncate" [tooltip]="extractedData[i]?.combinedToolNames"
                                    placement="auto" [delay]=500>
                                    {{ extractedData[i]?.combinedToolNames }}
                                </span>
                            </div>
                            <div class="agent-detail-item" *ngIf="extractedData[i]?.placeholders?.length">
                                <collab-comp-img-generator itemtype="icon" [fontClassName]="'icon-Submit-Document'"
                                    class="detail-icon">
                                </collab-comp-img-generator>
                                <span class="detail-text truncate" [tooltip]="extractedData[i]?.placeholders"
                                    placement="auto" [delay]=500>
                                    {{ extractedData[i]?.placeholders }}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div *ngIf="i < pipelineAgents.length - 1"class="arrow">
                    </div>
                </ng-container>
            </div>
        <div class="form-fields column-gap" *ngIf="useNewPipelineApi">
            <div class="input__field--wrapper">
                <label for="file" class="tooltip">Source
                    <span [tooltip]='tooltipContent.source' placement="auto" [delay]=500>
                        <svg class="icon-info" icon="Info"></svg>
                    </span>
                </label>
                <div class="upload-container">
                    <file-upload  [uploadBtnText]="'Upload File(s)'" #fileUploadref [acceptedFileType]="allowedFileType"
                        imagePath="upload_files.png" [fileUploadType]="'primary'" [files]="files" [fileSize]="100"
                        [errorExt]="isErrorExtn" typeOfFile="test data files(s)" [isMultiple]="true"
                        dragDropText="Drag and Drop your File(s) Or Upload File(s)">
                    </file-upload>
                </div>
            </div>
        </div>
        
        <div class="input-conteiner" [formGroup]="workflowForm">
            <ng-container *ngFor="let label of workflowInputList">
                <div class="form-fields">
                  <div class="input__field--wrapper">
                    
                    @if(!isImageInput(label.input)){
                        <label class="required" [for]="label.placeholder">Input for {{label.input}} in {{label.name}}</label>
                        <textarea [id]="label.placeholder" [formControlName]="label.input" placeholder="Enter your Input"></textarea>
                    }
                    @else{
                        <div class="image-input">
                            <label class="required" [for]="label.placeholder">Input for {{label.input}} in {{label.name}}</label>
                            <app-file-input id="image" #fileInput (fileUploaded)="changeFileUpload($event, label.input)"
                                [ngClass]="{disable__only: workflowForm.disabled}" [acceptedFileType]="allowedFileTypeForImage">
                            </app-file-input>
                        </div>
                    }
                  </div>
                </div>
              </ng-container>
            <ng-container *ngIf="!workflowInputList.length">
                <div class="form-fields">
                    <div class="input__field--wrapper">
                        <label for="input">Input</label>
                        <textarea id="input" class="disable__only" placeholder="Enter your Input" readonly>No Input is Required</textarea>
                    </div>
                </div>
            </ng-container>
        </div>

        <div class="button-container">
            <button-tertiary *ngIf="enableStreamingLog !== 'none' && visibleLogButton" (click)="openLogsModal('workflowLogs')">View Logs</button-tertiary>
            <div class="action-button">
                <collab-comp-button type="button" [buttonConfig]="cancelButtonConfig"
                    (onBtnClick)="onCancel()"></collab-comp-button>
                @if(workflowExecutionType !== 'async') {
                    <collab-comp-button type="button" [buttonConfig]="executeButtonConfig" [isBtnDisbaled]="!isInputValid()"
                        (onBtnClick)="executePipeline()"></collab-comp-button>
                }
                @if(workflowExecutionType !== 'sync') {
                    <collab-comp-button type="button" [buttonConfig]="asyncExecuteButtonConfig" [isBtnDisbaled]="!isInputValid()"
                        (onBtnClick)="asyncExecutePipeline()"></collab-comp-button>
                }
            </div>
        </div>

        <div class="output-container" *ngIf="resMessage">
            <div class="input__field--wrapper">
                <label for="output">Output</label>
                <div class="outputRes">
                    <div class="innerRes" [ngClass]="{'innerResError': errorMsg}">{{resMessage}}</div>
                    <div class="icon-copy_container" *ngIf="resMessage">
                        <collab-comp-img-generator (click)="copyText()" class="extra-icon" itemtype="icon"
                            fontClassName='icon-Copy icon-size-xl' style="cursor: pointer;">
                        </collab-comp-img-generator>
                    </div>
                </div>
                @if(isFileWriter){
                    @if(fileDownloadLink){
                        <div class="save-button_container">
                            <collab-comp-button [buttonConfig]="downloadOutputButtonConfig"
                            (onBtnClick)="downloadOutput()"></collab-comp-button>
                        </div>
                    }
                    @else {
                        <div>
                            <form-inline-error [errors]="fileDownloadUrlError"></form-inline-error>   
                        </div>
                    }
                }
            </div>
        </div>
    </div>

    <div class="accordion" *ngIf="isAccordian">
        <div class="input__field--wrapper">
            <label for="agentOutput">Agent Outputs</label>
        </div>
        <app-accordion-group>
            <ng-container *ngFor="let output of taskMessage; let i = index">
                <app-accordion [title]="output.description" [deleteButton]="false"
                    [panelHeight]="'fit-content'">
                    <div class="message">
                        <h3>Description</h3>
                        <p>{{ output?.description }}</p>
                    </div>
                    <div class="message">
                        <h3>Expected Output</h3>
                        <p>{{ output?.expected_output }}</p>
                    </div>
                    <div class="message">
                        <h3>Summary</h3>
                        <p>{{ output?.summary }}</p>
                    </div>
                    <div class="message">
                        <h3>Raw Output</h3>
                        <p>{{ output?.raw }}</p>
                    </div>
                    <div class="save-button_container">
                        <collab-comp-button [buttonConfig]="saveOutputFileButtonConfig"
                        (onBtnClick)="saveToFile(output?.raw, i)"></collab-comp-button>
                    </div>
                </app-accordion>
            </ng-container>
        </app-accordion-group>
    </div>
    <div class="export-button"  *ngIf="isAccordian">
        <collab-comp-button [buttonConfig]="saveAllOutputFileButtonConfig"
            (onBtnClick)="saveAllFile()"></collab-comp-button>
        <collab-comp-button [buttonConfig]="exportButtonConfig"
            (onBtnClick)="exportPdf()"></collab-comp-button>
        <collab-comp-button [buttonConfig]="exportDocButtonConfig"
            (onBtnClick)="exportDoc()"></collab-comp-button>
        <div *ngIf="isJsonValid">
            <collab-comp-button [buttonConfig]="exportExcelButtonConfig"
                  (onBtnClick)="saveToExcel()"></collab-comp-button>
        </div>
    </div>
</div>

<collab-comp-modal id="workflowLogs" [modalConfig]="workflowLogConfig" 
 (primaryButtonSelected)="saveLogs()" (secondaryButtonSelected)="closeWorkflowLogModal('workflowLogs')">
    <div class="logs-container" #logsContainer>
        <code class="log-content">
            <div class="log-line" *ngFor="let log of workflowLogs" #logRef>
                <span [class]="log.color">{{log.content}}</span>
            </div>
            <div class="log-line" *ngIf="!workflowLogs.length">
                <span>Streaming ...</span>
            </div>
        </code>
    </div>
</collab-comp-modal>

<collab-comp-modal [modalConfig]="asyncExecModal"  (primaryButtonSelected)="closeAsyncExecModal()">
    <div class="status-container">
        <collab-comp-img-generator [fontClassName]="!statusMsg.error ? 'icon-Success text--success icon--xxxl' : 'icon-Warning icon--xxl'"></collab-comp-img-generator>
        <div class="notification-text">{{statusMsg.message}}</div>
        @if(workflowExecutionId) {
            <div class="execution-container">
                <strong>Execution Id:</strong>&nbsp;{{workflowExecutionId}}
                <div class="icon-copy">
                    <collab-comp-img-generator itemtype="icon"
                        [fontClassName]="copiedExecId ? 'icon-Check icon-size-xl' : 'icon-Copy icon-size-xl'" (click)="onCopy()">
                    </collab-comp-img-generator>
                </div>
            </div>   
        }
    </div>
</collab-comp-modal>