{"ast": null, "code": "import { blur2, max, ticks } from \"d3-array\";\nimport { slice } from \"./array.js\";\nimport constant from \"./constant.js\";\nimport Contours from \"./contours.js\";\nfunction defaultX(d) {\n  return d[0];\n}\nfunction defaultY(d) {\n  return d[1];\n}\nfunction defaultWeight() {\n  return 1;\n}\nexport default function () {\n  var x = defaultX,\n    y = defaultY,\n    weight = defaultWeight,\n    dx = 960,\n    dy = 500,\n    r = 20,\n    // blur radius\n    k = 2,\n    // log2(grid cell size)\n    o = r * 3,\n    // grid offset, to pad for blur\n    n = dx + o * 2 >> k,\n    // grid width\n    m = dy + o * 2 >> k,\n    // grid height\n    threshold = constant(20);\n  function grid(data) {\n    var values = new Float32Array(n * m),\n      pow2k = Math.pow(2, -k),\n      i = -1;\n    for (const d of data) {\n      var xi = (x(d, ++i, data) + o) * pow2k,\n        yi = (y(d, i, data) + o) * pow2k,\n        wi = +weight(d, i, data);\n      if (wi && xi >= 0 && xi < n && yi >= 0 && yi < m) {\n        var x0 = Math.floor(xi),\n          y0 = Math.floor(yi),\n          xt = xi - x0 - 0.5,\n          yt = yi - y0 - 0.5;\n        values[x0 + y0 * n] += (1 - xt) * (1 - yt) * wi;\n        values[x0 + 1 + y0 * n] += xt * (1 - yt) * wi;\n        values[x0 + 1 + (y0 + 1) * n] += xt * yt * wi;\n        values[x0 + (y0 + 1) * n] += (1 - xt) * yt * wi;\n      }\n    }\n    blur2({\n      data: values,\n      width: n,\n      height: m\n    }, r * pow2k);\n    return values;\n  }\n  function density(data) {\n    var values = grid(data),\n      tz = threshold(values),\n      pow4k = Math.pow(2, 2 * k);\n\n    // Convert number of thresholds into uniform thresholds.\n    if (!Array.isArray(tz)) {\n      tz = ticks(Number.MIN_VALUE, max(values) / pow4k, tz);\n    }\n    return Contours().size([n, m]).thresholds(tz.map(d => d * pow4k))(values).map((c, i) => (c.value = +tz[i], transform(c)));\n  }\n  density.contours = function (data) {\n    var values = grid(data),\n      contours = Contours().size([n, m]),\n      pow4k = Math.pow(2, 2 * k),\n      contour = value => {\n        value = +value;\n        var c = transform(contours.contour(values, value * pow4k));\n        c.value = value; // preserve exact threshold value\n        return c;\n      };\n    Object.defineProperty(contour, \"max\", {\n      get: () => max(values) / pow4k\n    });\n    return contour;\n  };\n  function transform(geometry) {\n    geometry.coordinates.forEach(transformPolygon);\n    return geometry;\n  }\n  function transformPolygon(coordinates) {\n    coordinates.forEach(transformRing);\n  }\n  function transformRing(coordinates) {\n    coordinates.forEach(transformPoint);\n  }\n\n  // TODO Optimize.\n  function transformPoint(coordinates) {\n    coordinates[0] = coordinates[0] * Math.pow(2, k) - o;\n    coordinates[1] = coordinates[1] * Math.pow(2, k) - o;\n  }\n  function resize() {\n    o = r * 3;\n    n = dx + o * 2 >> k;\n    m = dy + o * 2 >> k;\n    return density;\n  }\n  density.x = function (_) {\n    return arguments.length ? (x = typeof _ === \"function\" ? _ : constant(+_), density) : x;\n  };\n  density.y = function (_) {\n    return arguments.length ? (y = typeof _ === \"function\" ? _ : constant(+_), density) : y;\n  };\n  density.weight = function (_) {\n    return arguments.length ? (weight = typeof _ === \"function\" ? _ : constant(+_), density) : weight;\n  };\n  density.size = function (_) {\n    if (!arguments.length) return [dx, dy];\n    var _0 = +_[0],\n      _1 = +_[1];\n    if (!(_0 >= 0 && _1 >= 0)) throw new Error(\"invalid size\");\n    return dx = _0, dy = _1, resize();\n  };\n  density.cellSize = function (_) {\n    if (!arguments.length) return 1 << k;\n    if (!((_ = +_) >= 1)) throw new Error(\"invalid cell size\");\n    return k = Math.floor(Math.log(_) / Math.LN2), resize();\n  };\n  density.thresholds = function (_) {\n    return arguments.length ? (threshold = typeof _ === \"function\" ? _ : Array.isArray(_) ? constant(slice.call(_)) : constant(_), density) : threshold;\n  };\n  density.bandwidth = function (_) {\n    if (!arguments.length) return Math.sqrt(r * (r + 1));\n    if (!((_ = +_) >= 0)) throw new Error(\"invalid bandwidth\");\n    return r = (Math.sqrt(4 * _ * _ + 1) - 1) / 2, resize();\n  };\n  return density;\n}", "map": {"version": 3, "names": ["blur2", "max", "ticks", "slice", "constant", "Contours", "defaultX", "d", "defaultY", "defaultWeight", "x", "y", "weight", "dx", "dy", "r", "k", "o", "n", "m", "threshold", "grid", "data", "values", "Float32Array", "pow2k", "Math", "pow", "i", "xi", "yi", "wi", "x0", "floor", "y0", "xt", "yt", "width", "height", "density", "tz", "pow4k", "Array", "isArray", "Number", "MIN_VALUE", "size", "thresholds", "map", "c", "value", "transform", "contours", "contour", "Object", "defineProperty", "get", "geometry", "coordinates", "for<PERSON>ach", "transformPolygon", "transformRing", "transformPoint", "resize", "_", "arguments", "length", "_0", "_1", "Error", "cellSize", "log", "LN2", "call", "bandwidth", "sqrt"], "sources": ["C:/console/aava-ui-web/node_modules/d3-contour/src/density.js"], "sourcesContent": ["import {blur2, max, ticks} from \"d3-array\";\nimport {slice} from \"./array.js\";\nimport constant from \"./constant.js\";\nimport Contours from \"./contours.js\";\n\nfunction defaultX(d) {\n  return d[0];\n}\n\nfunction defaultY(d) {\n  return d[1];\n}\n\nfunction defaultWeight() {\n  return 1;\n}\n\nexport default function() {\n  var x = defaultX,\n      y = defaultY,\n      weight = defaultWeight,\n      dx = 960,\n      dy = 500,\n      r = 20, // blur radius\n      k = 2, // log2(grid cell size)\n      o = r * 3, // grid offset, to pad for blur\n      n = (dx + o * 2) >> k, // grid width\n      m = (dy + o * 2) >> k, // grid height\n      threshold = constant(20);\n\n  function grid(data) {\n    var values = new Float32Array(n * m),\n        pow2k = Math.pow(2, -k),\n        i = -1;\n\n    for (const d of data) {\n      var xi = (x(d, ++i, data) + o) * pow2k,\n          yi = (y(d, i, data) + o) * pow2k,\n          wi = +weight(d, i, data);\n      if (wi && xi >= 0 && xi < n && yi >= 0 && yi < m) {\n        var x0 = Math.floor(xi),\n            y0 = Math.floor(yi),\n            xt = xi - x0 - 0.5,\n            yt = yi - y0 - 0.5;\n        values[x0 + y0 * n] += (1 - xt) * (1 - yt) * wi;\n        values[x0 + 1 + y0 * n] += xt * (1 - yt) * wi;\n        values[x0 + 1 + (y0 + 1) * n] += xt * yt * wi;\n        values[x0 + (y0 + 1) * n] += (1 - xt) * yt * wi;\n      }\n    }\n\n    blur2({data: values, width: n, height: m}, r * pow2k);\n    return values;\n  }\n\n  function density(data) {\n    var values = grid(data),\n        tz = threshold(values),\n        pow4k = Math.pow(2, 2 * k);\n\n    // Convert number of thresholds into uniform thresholds.\n    if (!Array.isArray(tz)) {\n      tz = ticks(Number.MIN_VALUE, max(values) / pow4k, tz);\n    }\n\n    return Contours()\n        .size([n, m])\n        .thresholds(tz.map(d => d * pow4k))\n      (values)\n        .map((c, i) => (c.value = +tz[i], transform(c)));\n  }\n\n  density.contours = function(data) {\n    var values = grid(data),\n        contours = Contours().size([n, m]),\n        pow4k = Math.pow(2, 2 * k),\n        contour = value => {\n          value = +value;\n          var c = transform(contours.contour(values, value * pow4k));\n          c.value = value; // preserve exact threshold value\n          return c;\n        };\n    Object.defineProperty(contour, \"max\", {get: () => max(values) / pow4k});\n    return contour;\n  };\n\n  function transform(geometry) {\n    geometry.coordinates.forEach(transformPolygon);\n    return geometry;\n  }\n\n  function transformPolygon(coordinates) {\n    coordinates.forEach(transformRing);\n  }\n\n  function transformRing(coordinates) {\n    coordinates.forEach(transformPoint);\n  }\n\n  // TODO Optimize.\n  function transformPoint(coordinates) {\n    coordinates[0] = coordinates[0] * Math.pow(2, k) - o;\n    coordinates[1] = coordinates[1] * Math.pow(2, k) - o;\n  }\n\n  function resize() {\n    o = r * 3;\n    n = (dx + o * 2) >> k;\n    m = (dy + o * 2) >> k;\n    return density;\n  }\n\n  density.x = function(_) {\n    return arguments.length ? (x = typeof _ === \"function\" ? _ : constant(+_), density) : x;\n  };\n\n  density.y = function(_) {\n    return arguments.length ? (y = typeof _ === \"function\" ? _ : constant(+_), density) : y;\n  };\n\n  density.weight = function(_) {\n    return arguments.length ? (weight = typeof _ === \"function\" ? _ : constant(+_), density) : weight;\n  };\n\n  density.size = function(_) {\n    if (!arguments.length) return [dx, dy];\n    var _0 = +_[0], _1 = +_[1];\n    if (!(_0 >= 0 && _1 >= 0)) throw new Error(\"invalid size\");\n    return dx = _0, dy = _1, resize();\n  };\n\n  density.cellSize = function(_) {\n    if (!arguments.length) return 1 << k;\n    if (!((_ = +_) >= 1)) throw new Error(\"invalid cell size\");\n    return k = Math.floor(Math.log(_) / Math.LN2), resize();\n  };\n\n  density.thresholds = function(_) {\n    return arguments.length ? (threshold = typeof _ === \"function\" ? _ : Array.isArray(_) ? constant(slice.call(_)) : constant(_), density) : threshold;\n  };\n\n  density.bandwidth = function(_) {\n    if (!arguments.length) return Math.sqrt(r * (r + 1));\n    if (!((_ = +_) >= 0)) throw new Error(\"invalid bandwidth\");\n    return r = (Math.sqrt(4 * _ * _ + 1) - 1) / 2, resize();\n  };\n\n  return density;\n}\n"], "mappings": "AAAA,SAAQA,KAAK,EAAEC,GAAG,EAAEC,KAAK,QAAO,UAAU;AAC1C,SAAQC,KAAK,QAAO,YAAY;AAChC,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,QAAQ,MAAM,eAAe;AAEpC,SAASC,QAAQA,CAACC,CAAC,EAAE;EACnB,OAAOA,CAAC,CAAC,CAAC,CAAC;AACb;AAEA,SAASC,QAAQA,CAACD,CAAC,EAAE;EACnB,OAAOA,CAAC,CAAC,CAAC,CAAC;AACb;AAEA,SAASE,aAAaA,CAAA,EAAG;EACvB,OAAO,CAAC;AACV;AAEA,eAAe,YAAW;EACxB,IAAIC,CAAC,GAAGJ,QAAQ;IACZK,CAAC,GAAGH,QAAQ;IACZI,MAAM,GAAGH,aAAa;IACtBI,EAAE,GAAG,GAAG;IACRC,EAAE,GAAG,GAAG;IACRC,CAAC,GAAG,EAAE;IAAE;IACRC,CAAC,GAAG,CAAC;IAAE;IACPC,CAAC,GAAGF,CAAC,GAAG,CAAC;IAAE;IACXG,CAAC,GAAIL,EAAE,GAAGI,CAAC,GAAG,CAAC,IAAKD,CAAC;IAAE;IACvBG,CAAC,GAAIL,EAAE,GAAGG,CAAC,GAAG,CAAC,IAAKD,CAAC;IAAE;IACvBI,SAAS,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAE5B,SAASiB,IAAIA,CAACC,IAAI,EAAE;IAClB,IAAIC,MAAM,GAAG,IAAIC,YAAY,CAACN,CAAC,GAAGC,CAAC,CAAC;MAChCM,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAACX,CAAC,CAAC;MACvBY,CAAC,GAAG,CAAC,CAAC;IAEV,KAAK,MAAMrB,CAAC,IAAIe,IAAI,EAAE;MACpB,IAAIO,EAAE,GAAG,CAACnB,CAAC,CAACH,CAAC,EAAE,EAAEqB,CAAC,EAAEN,IAAI,CAAC,GAAGL,CAAC,IAAIQ,KAAK;QAClCK,EAAE,GAAG,CAACnB,CAAC,CAACJ,CAAC,EAAEqB,CAAC,EAAEN,IAAI,CAAC,GAAGL,CAAC,IAAIQ,KAAK;QAChCM,EAAE,GAAG,CAACnB,MAAM,CAACL,CAAC,EAAEqB,CAAC,EAAEN,IAAI,CAAC;MAC5B,IAAIS,EAAE,IAAIF,EAAE,IAAI,CAAC,IAAIA,EAAE,GAAGX,CAAC,IAAIY,EAAE,IAAI,CAAC,IAAIA,EAAE,GAAGX,CAAC,EAAE;QAChD,IAAIa,EAAE,GAAGN,IAAI,CAACO,KAAK,CAACJ,EAAE,CAAC;UACnBK,EAAE,GAAGR,IAAI,CAACO,KAAK,CAACH,EAAE,CAAC;UACnBK,EAAE,GAAGN,EAAE,GAAGG,EAAE,GAAG,GAAG;UAClBI,EAAE,GAAGN,EAAE,GAAGI,EAAE,GAAG,GAAG;QACtBX,MAAM,CAACS,EAAE,GAAGE,EAAE,GAAGhB,CAAC,CAAC,IAAI,CAAC,CAAC,GAAGiB,EAAE,KAAK,CAAC,GAAGC,EAAE,CAAC,GAAGL,EAAE;QAC/CR,MAAM,CAACS,EAAE,GAAG,CAAC,GAAGE,EAAE,GAAGhB,CAAC,CAAC,IAAIiB,EAAE,IAAI,CAAC,GAAGC,EAAE,CAAC,GAAGL,EAAE;QAC7CR,MAAM,CAACS,EAAE,GAAG,CAAC,GAAG,CAACE,EAAE,GAAG,CAAC,IAAIhB,CAAC,CAAC,IAAIiB,EAAE,GAAGC,EAAE,GAAGL,EAAE;QAC7CR,MAAM,CAACS,EAAE,GAAG,CAACE,EAAE,GAAG,CAAC,IAAIhB,CAAC,CAAC,IAAI,CAAC,CAAC,GAAGiB,EAAE,IAAIC,EAAE,GAAGL,EAAE;MACjD;IACF;IAEA/B,KAAK,CAAC;MAACsB,IAAI,EAAEC,MAAM;MAAEc,KAAK,EAAEnB,CAAC;MAAEoB,MAAM,EAAEnB;IAAC,CAAC,EAAEJ,CAAC,GAAGU,KAAK,CAAC;IACrD,OAAOF,MAAM;EACf;EAEA,SAASgB,OAAOA,CAACjB,IAAI,EAAE;IACrB,IAAIC,MAAM,GAAGF,IAAI,CAACC,IAAI,CAAC;MACnBkB,EAAE,GAAGpB,SAAS,CAACG,MAAM,CAAC;MACtBkB,KAAK,GAAGf,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAGX,CAAC,CAAC;;IAE9B;IACA,IAAI,CAAC0B,KAAK,CAACC,OAAO,CAACH,EAAE,CAAC,EAAE;MACtBA,EAAE,GAAGtC,KAAK,CAAC0C,MAAM,CAACC,SAAS,EAAE5C,GAAG,CAACsB,MAAM,CAAC,GAAGkB,KAAK,EAAED,EAAE,CAAC;IACvD;IAEA,OAAOnC,QAAQ,CAAC,CAAC,CACZyC,IAAI,CAAC,CAAC5B,CAAC,EAAEC,CAAC,CAAC,CAAC,CACZ4B,UAAU,CAACP,EAAE,CAACQ,GAAG,CAACzC,CAAC,IAAIA,CAAC,GAAGkC,KAAK,CAAC,CAAC,CACpClB,MAAM,CAAC,CACLyB,GAAG,CAAC,CAACC,CAAC,EAAErB,CAAC,MAAMqB,CAAC,CAACC,KAAK,GAAG,CAACV,EAAE,CAACZ,CAAC,CAAC,EAAEuB,SAAS,CAACF,CAAC,CAAC,CAAC,CAAC;EACtD;EAEAV,OAAO,CAACa,QAAQ,GAAG,UAAS9B,IAAI,EAAE;IAChC,IAAIC,MAAM,GAAGF,IAAI,CAACC,IAAI,CAAC;MACnB8B,QAAQ,GAAG/C,QAAQ,CAAC,CAAC,CAACyC,IAAI,CAAC,CAAC5B,CAAC,EAAEC,CAAC,CAAC,CAAC;MAClCsB,KAAK,GAAGf,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAGX,CAAC,CAAC;MAC1BqC,OAAO,GAAGH,KAAK,IAAI;QACjBA,KAAK,GAAG,CAACA,KAAK;QACd,IAAID,CAAC,GAAGE,SAAS,CAACC,QAAQ,CAACC,OAAO,CAAC9B,MAAM,EAAE2B,KAAK,GAAGT,KAAK,CAAC,CAAC;QAC1DQ,CAAC,CAACC,KAAK,GAAGA,KAAK,CAAC,CAAC;QACjB,OAAOD,CAAC;MACV,CAAC;IACLK,MAAM,CAACC,cAAc,CAACF,OAAO,EAAE,KAAK,EAAE;MAACG,GAAG,EAAEA,CAAA,KAAMvD,GAAG,CAACsB,MAAM,CAAC,GAAGkB;IAAK,CAAC,CAAC;IACvE,OAAOY,OAAO;EAChB,CAAC;EAED,SAASF,SAASA,CAACM,QAAQ,EAAE;IAC3BA,QAAQ,CAACC,WAAW,CAACC,OAAO,CAACC,gBAAgB,CAAC;IAC9C,OAAOH,QAAQ;EACjB;EAEA,SAASG,gBAAgBA,CAACF,WAAW,EAAE;IACrCA,WAAW,CAACC,OAAO,CAACE,aAAa,CAAC;EACpC;EAEA,SAASA,aAAaA,CAACH,WAAW,EAAE;IAClCA,WAAW,CAACC,OAAO,CAACG,cAAc,CAAC;EACrC;;EAEA;EACA,SAASA,cAAcA,CAACJ,WAAW,EAAE;IACnCA,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC,GAAGhC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEX,CAAC,CAAC,GAAGC,CAAC;IACpDyC,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC,GAAGhC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEX,CAAC,CAAC,GAAGC,CAAC;EACtD;EAEA,SAAS8C,MAAMA,CAAA,EAAG;IAChB9C,CAAC,GAAGF,CAAC,GAAG,CAAC;IACTG,CAAC,GAAIL,EAAE,GAAGI,CAAC,GAAG,CAAC,IAAKD,CAAC;IACrBG,CAAC,GAAIL,EAAE,GAAGG,CAAC,GAAG,CAAC,IAAKD,CAAC;IACrB,OAAOuB,OAAO;EAChB;EAEAA,OAAO,CAAC7B,CAAC,GAAG,UAASsD,CAAC,EAAE;IACtB,OAAOC,SAAS,CAACC,MAAM,IAAIxD,CAAC,GAAG,OAAOsD,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG5D,QAAQ,CAAC,CAAC4D,CAAC,CAAC,EAAEzB,OAAO,IAAI7B,CAAC;EACzF,CAAC;EAED6B,OAAO,CAAC5B,CAAC,GAAG,UAASqD,CAAC,EAAE;IACtB,OAAOC,SAAS,CAACC,MAAM,IAAIvD,CAAC,GAAG,OAAOqD,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG5D,QAAQ,CAAC,CAAC4D,CAAC,CAAC,EAAEzB,OAAO,IAAI5B,CAAC;EACzF,CAAC;EAED4B,OAAO,CAAC3B,MAAM,GAAG,UAASoD,CAAC,EAAE;IAC3B,OAAOC,SAAS,CAACC,MAAM,IAAItD,MAAM,GAAG,OAAOoD,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG5D,QAAQ,CAAC,CAAC4D,CAAC,CAAC,EAAEzB,OAAO,IAAI3B,MAAM;EACnG,CAAC;EAED2B,OAAO,CAACO,IAAI,GAAG,UAASkB,CAAC,EAAE;IACzB,IAAI,CAACC,SAAS,CAACC,MAAM,EAAE,OAAO,CAACrD,EAAE,EAAEC,EAAE,CAAC;IACtC,IAAIqD,EAAE,GAAG,CAACH,CAAC,CAAC,CAAC,CAAC;MAAEI,EAAE,GAAG,CAACJ,CAAC,CAAC,CAAC,CAAC;IAC1B,IAAI,EAAEG,EAAE,IAAI,CAAC,IAAIC,EAAE,IAAI,CAAC,CAAC,EAAE,MAAM,IAAIC,KAAK,CAAC,cAAc,CAAC;IAC1D,OAAOxD,EAAE,GAAGsD,EAAE,EAAErD,EAAE,GAAGsD,EAAE,EAAEL,MAAM,CAAC,CAAC;EACnC,CAAC;EAEDxB,OAAO,CAAC+B,QAAQ,GAAG,UAASN,CAAC,EAAE;IAC7B,IAAI,CAACC,SAAS,CAACC,MAAM,EAAE,OAAO,CAAC,IAAIlD,CAAC;IACpC,IAAI,EAAE,CAACgD,CAAC,GAAG,CAACA,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,IAAIK,KAAK,CAAC,mBAAmB,CAAC;IAC1D,OAAOrD,CAAC,GAAGU,IAAI,CAACO,KAAK,CAACP,IAAI,CAAC6C,GAAG,CAACP,CAAC,CAAC,GAAGtC,IAAI,CAAC8C,GAAG,CAAC,EAAET,MAAM,CAAC,CAAC;EACzD,CAAC;EAEDxB,OAAO,CAACQ,UAAU,GAAG,UAASiB,CAAC,EAAE;IAC/B,OAAOC,SAAS,CAACC,MAAM,IAAI9C,SAAS,GAAG,OAAO4C,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGtB,KAAK,CAACC,OAAO,CAACqB,CAAC,CAAC,GAAG5D,QAAQ,CAACD,KAAK,CAACsE,IAAI,CAACT,CAAC,CAAC,CAAC,GAAG5D,QAAQ,CAAC4D,CAAC,CAAC,EAAEzB,OAAO,IAAInB,SAAS;EACrJ,CAAC;EAEDmB,OAAO,CAACmC,SAAS,GAAG,UAASV,CAAC,EAAE;IAC9B,IAAI,CAACC,SAAS,CAACC,MAAM,EAAE,OAAOxC,IAAI,CAACiD,IAAI,CAAC5D,CAAC,IAAIA,CAAC,GAAG,CAAC,CAAC,CAAC;IACpD,IAAI,EAAE,CAACiD,CAAC,GAAG,CAACA,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,IAAIK,KAAK,CAAC,mBAAmB,CAAC;IAC1D,OAAOtD,CAAC,GAAG,CAACW,IAAI,CAACiD,IAAI,CAAC,CAAC,GAAGX,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAED,MAAM,CAAC,CAAC;EACzD,CAAC;EAED,OAAOxB,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}