{"ast": null, "code": "import { agentsData } from '../../../assets/data/agents.mock';\nimport { AgentsComponent } from '../../shared/components/agents/agents.component';\nimport Hero from '../../shared/components/hero/hero.component';\nimport * as i0 from \"@angular/core\";\nexport let AgentsFilterComponent = /*#__PURE__*/(() => {\n  class AgentsFilterComponent {\n    agentsData = agentsData;\n    static ɵfac = function AgentsFilterComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AgentsFilterComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AgentsFilterComponent,\n      selectors: [[\"app-agents-filter\"]],\n      decls: 3,\n      vars: 6,\n      consts: [[1, \"marketplace-container\"], [\"heroType\", \"Marketplace\", 3, \"searchResultsChange\", \"searchQueryChange\", \"searchLoadingChange\", \"sendClicked\"], [3, \"agents\", \"showExploreButton\", \"isMarketplace\", \"searchResults\", \"searchQuery\", \"isSearchLoading\"]],\n      template: function AgentsFilterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"app-hero\", 1);\n          i0.ɵɵlistener(\"searchResultsChange\", function AgentsFilterComponent_Template_app_hero_searchResultsChange_1_listener($event) {\n            return ctx.onSearchResultsChange($event);\n          })(\"searchQueryChange\", function AgentsFilterComponent_Template_app_hero_searchQueryChange_1_listener($event) {\n            return ctx.onSearchQueryChange($event);\n          })(\"searchLoadingChange\", function AgentsFilterComponent_Template_app_hero_searchLoadingChange_1_listener($event) {\n            return ctx.onSearchLoadingChange($event);\n          })(\"sendClicked\", function AgentsFilterComponent_Template_app_hero_sendClicked_1_listener($event) {\n            return ctx.onSendClicked($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(2, \"app-agents\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"agents\", ctx.agentsData)(\"showExploreButton\", false)(\"isMarketplace\", true)(\"searchResults\", ctx.searchResults)(\"searchQuery\", ctx.searchQuery)(\"isSearchLoading\", ctx.isSearchLoading);\n        }\n      },\n      dependencies: [AgentsComponent, Hero],\n      styles: [\"app-agents[_ngcontent-%COMP%] {\\n  margin-top: 64px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3Byb2plY3RzL2VsZGVyLXdhbmQvc3JjL2FwcC9wYWdlcy9hZ2VudHMtZmlsdGVyL2FnZW50cy1maWx0ZXIuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQ0E7RUFDRSxnQkFBQTtBQUFGIiwic291cmNlc0NvbnRlbnQiOlsiLy8gQWRkIG1vcmUgZ2FwIGJldHdlZW4gaGVybyBhbmQgYWdlbnRzIGNvbnRlbnRcclxuYXBwLWFnZW50cyB7XHJcbiAgbWFyZ2luLXRvcDogNjRweDtcclxufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n  return AgentsFilterComponent;\n})();", "map": {"version": 3, "names": ["agentsData", "AgentsComponent", "Hero", "AgentsFilterComponent", "selectors", "decls", "vars", "consts", "template", "AgentsFilterComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵlistener", "AgentsFilterComponent_Template_app_hero_searchResultsChange_1_listener", "$event", "onSearchResultsChange", "AgentsFilterComponent_Template_app_hero_searchQueryChange_1_listener", "onSearchQueryChange", "AgentsFilterComponent_Template_app_hero_searchLoadingChange_1_listener", "onSearchLoadingChange", "AgentsFilterComponent_Template_app_hero_sendClicked_1_listener", "onSendClicked", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "searchResults", "searchQuery", "isSearchLoading", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\pages\\agents-filter\\agents-filter.component.ts", "C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\pages\\agents-filter\\agents-filter.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { Agent } from '../../shared/interfaces/agent-list.interface';\r\nimport { agentsData } from '../../../assets/data/agents.mock';\r\nimport { AgentsComponent } from '../../shared/components/agents/agents.component';\r\nimport Hero from '../../shared/components/hero/hero.component';\r\n\r\n@Component({\r\n  selector: 'app-agents-filter',\r\n  imports: [AgentsComponent, Hero],\r\n  templateUrl: './agents-filter.component.html',\r\n  styleUrl: './agents-filter.component.scss',\r\n})\r\nexport class AgentsFilterComponent {\r\n  agentsData: Agent[] = agentsData;\r\n}\r\n", "<div class=\"marketplace-container\">\r\n  <app-hero\r\n    heroType=\"Marketplace\"\r\n    (searchResultsChange)=\"onSearchResultsChange($event)\"\r\n    (searchQueryChange)=\"onSearchQueryChange($event)\"\r\n    (searchLoadingChange)=\"onSearchLoadingChange($event)\"\r\n    (sendClicked)=\"onSendClicked($event)\"\r\n  >\r\n  </app-hero>\r\n  <app-agents\r\n    [agents]=\"agentsData\"\r\n    [showExploreButton]=\"false\"\r\n    [isMarketplace]=\"true\"\r\n    [searchResults]=\"searchResults\"\r\n    [searchQuery]=\"searchQuery\"\r\n    [isSearchLoading]=\"isSearchLoading\"\r\n  >\r\n  </app-agents>\r\n</div>\r\n"], "mappings": "AAEA,SAASA,UAAU,QAAQ,kCAAkC;AAC7D,SAASC,eAAe,QAAQ,iDAAiD;AACjF,OAAOC,IAAI,MAAM,6CAA6C;;AAQ9D,WAAaC,qBAAqB;EAA5B,MAAOA,qBAAqB;IAChCH,UAAU,GAAYA,UAAU;;uCADrBG,qBAAqB;IAAA;;YAArBA,qBAAqB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXhCE,EADF,CAAAC,cAAA,aAAmC,kBAOhC;UADCD,EAHA,CAAAE,UAAA,iCAAAC,uEAAAC,MAAA;YAAA,OAAuBL,GAAA,CAAAM,qBAAA,CAAAD,MAAA,CAA6B;UAAA,EAAC,+BAAAE,qEAAAF,MAAA;YAAA,OAChCL,GAAA,CAAAQ,mBAAA,CAAAH,MAAA,CAA2B;UAAA,EAAC,iCAAAI,uEAAAJ,MAAA;YAAA,OAC1BL,GAAA,CAAAU,qBAAA,CAAAL,MAAA,CAA6B;UAAA,EAAC,yBAAAM,+DAAAN,MAAA;YAAA,OACtCL,GAAA,CAAAY,aAAA,CAAAP,MAAA,CAAqB;UAAA,EAAC;UAEvCJ,EAAA,CAAAY,YAAA,EAAW;UACXZ,EAAA,CAAAa,SAAA,oBAQa;UACfb,EAAA,CAAAY,YAAA,EAAM;;;UARFZ,EAAA,CAAAc,SAAA,GAAqB;UAKrBd,EALA,CAAAe,UAAA,WAAAhB,GAAA,CAAAX,UAAA,CAAqB,4BACM,uBACL,kBAAAW,GAAA,CAAAiB,aAAA,CACS,gBAAAjB,GAAA,CAAAkB,WAAA,CACJ,oBAAAlB,GAAA,CAAAmB,eAAA,CACQ;;;qBDP3B7B,eAAe,EAAEC,IAAI;MAAA6B,MAAA;IAAA;;SAIpB5B,qBAAqB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}