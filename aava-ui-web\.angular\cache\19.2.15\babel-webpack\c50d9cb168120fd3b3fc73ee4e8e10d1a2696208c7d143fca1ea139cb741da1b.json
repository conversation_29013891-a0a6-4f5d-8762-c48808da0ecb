{"ast": null, "code": "import { defaultLocale } from \"./_lib/defaultLocale.js\";\nimport { getDefaultOptions } from \"./_lib/defaultOptions.js\";\nimport { getTimezoneOffsetInMilliseconds } from \"./_lib/getTimezoneOffsetInMilliseconds.js\";\nimport { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { compareAsc } from \"./compareAsc.js\";\nimport { minutesInDay, minutesInMonth } from \"./constants.js\";\nimport { differenceInMonths } from \"./differenceInMonths.js\";\nimport { differenceInSeconds } from \"./differenceInSeconds.js\";\n\n/**\n * The {@link formatDistance} function options.\n */\n\n/**\n * @name formatDistance\n * @category Common Helpers\n * @summary Return the distance between the given dates in words.\n *\n * @description\n * Return the distance between the given dates in words.\n *\n * | Distance between dates                                            | Result              |\n * |-------------------------------------------------------------------|---------------------|\n * | 0 ... 30 secs                                                     | less than a minute  |\n * | 30 secs ... 1 min 30 secs                                         | 1 minute            |\n * | 1 min 30 secs ... 44 mins 30 secs                                 | [2..44] minutes     |\n * | 44 mins ... 30 secs ... 89 mins 30 secs                           | about 1 hour        |\n * | 89 mins 30 secs ... 23 hrs 59 mins 30 secs                        | about [2..24] hours |\n * | 23 hrs 59 mins 30 secs ... 41 hrs 59 mins 30 secs                 | 1 day               |\n * | 41 hrs 59 mins 30 secs ... 29 days 23 hrs 59 mins 30 secs         | [2..30] days        |\n * | 29 days 23 hrs 59 mins 30 secs ... 44 days 23 hrs 59 mins 30 secs | about 1 month       |\n * | 44 days 23 hrs 59 mins 30 secs ... 59 days 23 hrs 59 mins 30 secs | about 2 months      |\n * | 59 days 23 hrs 59 mins 30 secs ... 1 yr                           | [2..12] months      |\n * | 1 yr ... 1 yr 3 months                                            | about 1 year        |\n * | 1 yr 3 months ... 1 yr 9 month s                                  | over 1 year         |\n * | 1 yr 9 months ... 2 yrs                                           | almost 2 years      |\n * | N yrs ... N yrs 3 months                                          | about N years       |\n * | N yrs 3 months ... N yrs 9 months                                 | over N years        |\n * | N yrs 9 months ... N+1 yrs                                        | almost N+1 years    |\n *\n * With `options.includeSeconds == true`:\n * | Distance between dates | Result               |\n * |------------------------|----------------------|\n * | 0 secs ... 5 secs      | less than 5 seconds  |\n * | 5 secs ... 10 secs     | less than 10 seconds |\n * | 10 secs ... 20 secs    | less than 20 seconds |\n * | 20 secs ... 40 secs    | half a minute        |\n * | 40 secs ... 60 secs    | less than a minute   |\n * | 60 secs ... 90 secs    | 1 minute             |\n *\n * @param laterDate - The date\n * @param earlierDate - The date to compare with\n * @param options - An object with options\n *\n * @returns The distance in words\n *\n * @throws `date` must not be Invalid Date\n * @throws `baseDate` must not be Invalid Date\n * @throws `options.locale` must contain `formatDistance` property\n *\n * @example\n * // What is the distance between 2 July 2014 and 1 January 2015?\n * const result = formatDistance(new Date(2014, 6, 2), new Date(2015, 0, 1))\n * //=> '6 months'\n *\n * @example\n * // What is the distance between 1 January 2015 00:00:15\n * // and 1 January 2015 00:00:00, including seconds?\n * const result = formatDistance(\n *   new Date(2015, 0, 1, 0, 0, 15),\n *   new Date(2015, 0, 1, 0, 0, 0),\n *   { includeSeconds: true }\n * )\n * //=> 'less than 20 seconds'\n *\n * @example\n * // What is the distance from 1 January 2016\n * // to 1 January 2015, with a suffix?\n * const result = formatDistance(new Date(2015, 0, 1), new Date(2016, 0, 1), {\n *   addSuffix: true\n * })\n * //=> 'about 1 year ago'\n *\n * @example\n * // What is the distance between 1 August 2016 and 1 January 2015 in Esperanto?\n * import { eoLocale } from 'date-fns/locale/eo'\n * const result = formatDistance(new Date(2016, 7, 1), new Date(2015, 0, 1), {\n *   locale: eoLocale\n * })\n * //=> 'pli ol 1 jaro'\n */\nexport function formatDistance(laterDate, earlierDate, options) {\n  const defaultOptions = getDefaultOptions();\n  const locale = options?.locale ?? defaultOptions.locale ?? defaultLocale;\n  const minutesInAlmostTwoDays = 2520;\n  const comparison = compareAsc(laterDate, earlierDate);\n  if (isNaN(comparison)) throw new RangeError(\"Invalid time value\");\n  const localizeOptions = Object.assign({}, options, {\n    addSuffix: options?.addSuffix,\n    comparison: comparison\n  });\n  const [laterDate_, earlierDate_] = normalizeDates(options?.in, ...(comparison > 0 ? [earlierDate, laterDate] : [laterDate, earlierDate]));\n  const seconds = differenceInSeconds(earlierDate_, laterDate_);\n  const offsetInSeconds = (getTimezoneOffsetInMilliseconds(earlierDate_) - getTimezoneOffsetInMilliseconds(laterDate_)) / 1000;\n  const minutes = Math.round((seconds - offsetInSeconds) / 60);\n  let months;\n\n  // 0 up to 2 mins\n  if (minutes < 2) {\n    if (options?.includeSeconds) {\n      if (seconds < 5) {\n        return locale.formatDistance(\"lessThanXSeconds\", 5, localizeOptions);\n      } else if (seconds < 10) {\n        return locale.formatDistance(\"lessThanXSeconds\", 10, localizeOptions);\n      } else if (seconds < 20) {\n        return locale.formatDistance(\"lessThanXSeconds\", 20, localizeOptions);\n      } else if (seconds < 40) {\n        return locale.formatDistance(\"halfAMinute\", 0, localizeOptions);\n      } else if (seconds < 60) {\n        return locale.formatDistance(\"lessThanXMinutes\", 1, localizeOptions);\n      } else {\n        return locale.formatDistance(\"xMinutes\", 1, localizeOptions);\n      }\n    } else {\n      if (minutes === 0) {\n        return locale.formatDistance(\"lessThanXMinutes\", 1, localizeOptions);\n      } else {\n        return locale.formatDistance(\"xMinutes\", minutes, localizeOptions);\n      }\n    }\n\n    // 2 mins up to 0.75 hrs\n  } else if (minutes < 45) {\n    return locale.formatDistance(\"xMinutes\", minutes, localizeOptions);\n\n    // 0.75 hrs up to 1.5 hrs\n  } else if (minutes < 90) {\n    return locale.formatDistance(\"aboutXHours\", 1, localizeOptions);\n\n    // 1.5 hrs up to 24 hrs\n  } else if (minutes < minutesInDay) {\n    const hours = Math.round(minutes / 60);\n    return locale.formatDistance(\"aboutXHours\", hours, localizeOptions);\n\n    // 1 day up to 1.75 days\n  } else if (minutes < minutesInAlmostTwoDays) {\n    return locale.formatDistance(\"xDays\", 1, localizeOptions);\n\n    // 1.75 days up to 30 days\n  } else if (minutes < minutesInMonth) {\n    const days = Math.round(minutes / minutesInDay);\n    return locale.formatDistance(\"xDays\", days, localizeOptions);\n\n    // 1 month up to 2 months\n  } else if (minutes < minutesInMonth * 2) {\n    months = Math.round(minutes / minutesInMonth);\n    return locale.formatDistance(\"aboutXMonths\", months, localizeOptions);\n  }\n  months = differenceInMonths(earlierDate_, laterDate_);\n\n  // 2 months up to 12 months\n  if (months < 12) {\n    const nearestMonth = Math.round(minutes / minutesInMonth);\n    return locale.formatDistance(\"xMonths\", nearestMonth, localizeOptions);\n\n    // 1 year up to max Date\n  } else {\n    const monthsSinceStartOfYear = months % 12;\n    const years = Math.trunc(months / 12);\n\n    // N years up to 1 years 3 months\n    if (monthsSinceStartOfYear < 3) {\n      return locale.formatDistance(\"aboutXYears\", years, localizeOptions);\n\n      // N years 3 months up to N years 9 months\n    } else if (monthsSinceStartOfYear < 9) {\n      return locale.formatDistance(\"overXYears\", years, localizeOptions);\n\n      // N years 9 months up to N year 12 months\n    } else {\n      return locale.formatDistance(\"almostXYears\", years + 1, localizeOptions);\n    }\n  }\n}\n\n// Fallback for modularized imports:\nexport default formatDistance;", "map": {"version": 3, "names": ["defaultLocale", "getDefaultOptions", "getTimezoneOffsetInMilliseconds", "normalizeDates", "compareAsc", "minutesInDay", "minutesInMonth", "differenceInMonths", "differenceInSeconds", "formatDistance", "laterDate", "earlierDate", "options", "defaultOptions", "locale", "minutesInAlmostTwoDays", "comparison", "isNaN", "RangeError", "localizeOptions", "Object", "assign", "addSuffix", "laterDate_", "earlierDate_", "in", "seconds", "offsetInSeconds", "minutes", "Math", "round", "months", "includeSeconds", "hours", "days", "nearestMonth", "monthsSinceStartOfYear", "years", "trunc"], "sources": ["C:/console/aava-ui-web/node_modules/date-fns/formatDistance.js"], "sourcesContent": ["import { defaultLocale } from \"./_lib/defaultLocale.js\";\nimport { getDefaultOptions } from \"./_lib/defaultOptions.js\";\nimport { getTimezoneOffsetInMilliseconds } from \"./_lib/getTimezoneOffsetInMilliseconds.js\";\nimport { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { compareAsc } from \"./compareAsc.js\";\nimport { minutesInDay, minutesInMonth } from \"./constants.js\";\nimport { differenceInMonths } from \"./differenceInMonths.js\";\nimport { differenceInSeconds } from \"./differenceInSeconds.js\";\n\n/**\n * The {@link formatDistance} function options.\n */\n\n/**\n * @name formatDistance\n * @category Common Helpers\n * @summary Return the distance between the given dates in words.\n *\n * @description\n * Return the distance between the given dates in words.\n *\n * | Distance between dates                                            | Result              |\n * |-------------------------------------------------------------------|---------------------|\n * | 0 ... 30 secs                                                     | less than a minute  |\n * | 30 secs ... 1 min 30 secs                                         | 1 minute            |\n * | 1 min 30 secs ... 44 mins 30 secs                                 | [2..44] minutes     |\n * | 44 mins ... 30 secs ... 89 mins 30 secs                           | about 1 hour        |\n * | 89 mins 30 secs ... 23 hrs 59 mins 30 secs                        | about [2..24] hours |\n * | 23 hrs 59 mins 30 secs ... 41 hrs 59 mins 30 secs                 | 1 day               |\n * | 41 hrs 59 mins 30 secs ... 29 days 23 hrs 59 mins 30 secs         | [2..30] days        |\n * | 29 days 23 hrs 59 mins 30 secs ... 44 days 23 hrs 59 mins 30 secs | about 1 month       |\n * | 44 days 23 hrs 59 mins 30 secs ... 59 days 23 hrs 59 mins 30 secs | about 2 months      |\n * | 59 days 23 hrs 59 mins 30 secs ... 1 yr                           | [2..12] months      |\n * | 1 yr ... 1 yr 3 months                                            | about 1 year        |\n * | 1 yr 3 months ... 1 yr 9 month s                                  | over 1 year         |\n * | 1 yr 9 months ... 2 yrs                                           | almost 2 years      |\n * | N yrs ... N yrs 3 months                                          | about N years       |\n * | N yrs 3 months ... N yrs 9 months                                 | over N years        |\n * | N yrs 9 months ... N+1 yrs                                        | almost N+1 years    |\n *\n * With `options.includeSeconds == true`:\n * | Distance between dates | Result               |\n * |------------------------|----------------------|\n * | 0 secs ... 5 secs      | less than 5 seconds  |\n * | 5 secs ... 10 secs     | less than 10 seconds |\n * | 10 secs ... 20 secs    | less than 20 seconds |\n * | 20 secs ... 40 secs    | half a minute        |\n * | 40 secs ... 60 secs    | less than a minute   |\n * | 60 secs ... 90 secs    | 1 minute             |\n *\n * @param laterDate - The date\n * @param earlierDate - The date to compare with\n * @param options - An object with options\n *\n * @returns The distance in words\n *\n * @throws `date` must not be Invalid Date\n * @throws `baseDate` must not be Invalid Date\n * @throws `options.locale` must contain `formatDistance` property\n *\n * @example\n * // What is the distance between 2 July 2014 and 1 January 2015?\n * const result = formatDistance(new Date(2014, 6, 2), new Date(2015, 0, 1))\n * //=> '6 months'\n *\n * @example\n * // What is the distance between 1 January 2015 00:00:15\n * // and 1 January 2015 00:00:00, including seconds?\n * const result = formatDistance(\n *   new Date(2015, 0, 1, 0, 0, 15),\n *   new Date(2015, 0, 1, 0, 0, 0),\n *   { includeSeconds: true }\n * )\n * //=> 'less than 20 seconds'\n *\n * @example\n * // What is the distance from 1 January 2016\n * // to 1 January 2015, with a suffix?\n * const result = formatDistance(new Date(2015, 0, 1), new Date(2016, 0, 1), {\n *   addSuffix: true\n * })\n * //=> 'about 1 year ago'\n *\n * @example\n * // What is the distance between 1 August 2016 and 1 January 2015 in Esperanto?\n * import { eoLocale } from 'date-fns/locale/eo'\n * const result = formatDistance(new Date(2016, 7, 1), new Date(2015, 0, 1), {\n *   locale: eoLocale\n * })\n * //=> 'pli ol 1 jaro'\n */\nexport function formatDistance(laterDate, earlierDate, options) {\n  const defaultOptions = getDefaultOptions();\n  const locale = options?.locale ?? defaultOptions.locale ?? defaultLocale;\n  const minutesInAlmostTwoDays = 2520;\n\n  const comparison = compareAsc(laterDate, earlierDate);\n\n  if (isNaN(comparison)) throw new RangeError(\"Invalid time value\");\n\n  const localizeOptions = Object.assign({}, options, {\n    addSuffix: options?.addSuffix,\n    comparison: comparison,\n  });\n\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    ...(comparison > 0 ? [earlierDate, laterDate] : [laterDate, earlierDate]),\n  );\n\n  const seconds = differenceInSeconds(earlierDate_, laterDate_);\n  const offsetInSeconds =\n    (getTimezoneOffsetInMilliseconds(earlierDate_) -\n      getTimezoneOffsetInMilliseconds(laterDate_)) /\n    1000;\n  const minutes = Math.round((seconds - offsetInSeconds) / 60);\n  let months;\n\n  // 0 up to 2 mins\n  if (minutes < 2) {\n    if (options?.includeSeconds) {\n      if (seconds < 5) {\n        return locale.formatDistance(\"lessThanXSeconds\", 5, localizeOptions);\n      } else if (seconds < 10) {\n        return locale.formatDistance(\"lessThanXSeconds\", 10, localizeOptions);\n      } else if (seconds < 20) {\n        return locale.formatDistance(\"lessThanXSeconds\", 20, localizeOptions);\n      } else if (seconds < 40) {\n        return locale.formatDistance(\"halfAMinute\", 0, localizeOptions);\n      } else if (seconds < 60) {\n        return locale.formatDistance(\"lessThanXMinutes\", 1, localizeOptions);\n      } else {\n        return locale.formatDistance(\"xMinutes\", 1, localizeOptions);\n      }\n    } else {\n      if (minutes === 0) {\n        return locale.formatDistance(\"lessThanXMinutes\", 1, localizeOptions);\n      } else {\n        return locale.formatDistance(\"xMinutes\", minutes, localizeOptions);\n      }\n    }\n\n    // 2 mins up to 0.75 hrs\n  } else if (minutes < 45) {\n    return locale.formatDistance(\"xMinutes\", minutes, localizeOptions);\n\n    // 0.75 hrs up to 1.5 hrs\n  } else if (minutes < 90) {\n    return locale.formatDistance(\"aboutXHours\", 1, localizeOptions);\n\n    // 1.5 hrs up to 24 hrs\n  } else if (minutes < minutesInDay) {\n    const hours = Math.round(minutes / 60);\n    return locale.formatDistance(\"aboutXHours\", hours, localizeOptions);\n\n    // 1 day up to 1.75 days\n  } else if (minutes < minutesInAlmostTwoDays) {\n    return locale.formatDistance(\"xDays\", 1, localizeOptions);\n\n    // 1.75 days up to 30 days\n  } else if (minutes < minutesInMonth) {\n    const days = Math.round(minutes / minutesInDay);\n    return locale.formatDistance(\"xDays\", days, localizeOptions);\n\n    // 1 month up to 2 months\n  } else if (minutes < minutesInMonth * 2) {\n    months = Math.round(minutes / minutesInMonth);\n    return locale.formatDistance(\"aboutXMonths\", months, localizeOptions);\n  }\n\n  months = differenceInMonths(earlierDate_, laterDate_);\n\n  // 2 months up to 12 months\n  if (months < 12) {\n    const nearestMonth = Math.round(minutes / minutesInMonth);\n    return locale.formatDistance(\"xMonths\", nearestMonth, localizeOptions);\n\n    // 1 year up to max Date\n  } else {\n    const monthsSinceStartOfYear = months % 12;\n    const years = Math.trunc(months / 12);\n\n    // N years up to 1 years 3 months\n    if (monthsSinceStartOfYear < 3) {\n      return locale.formatDistance(\"aboutXYears\", years, localizeOptions);\n\n      // N years 3 months up to N years 9 months\n    } else if (monthsSinceStartOfYear < 9) {\n      return locale.formatDistance(\"overXYears\", years, localizeOptions);\n\n      // N years 9 months up to N year 12 months\n    } else {\n      return locale.formatDistance(\"almostXYears\", years + 1, localizeOptions);\n    }\n  }\n}\n\n// Fallback for modularized imports:\nexport default formatDistance;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,yBAAyB;AACvD,SAASC,iBAAiB,QAAQ,0BAA0B;AAC5D,SAASC,+BAA+B,QAAQ,2CAA2C;AAC3F,SAASC,cAAc,QAAQ,0BAA0B;AACzD,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,YAAY,EAAEC,cAAc,QAAQ,gBAAgB;AAC7D,SAASC,kBAAkB,QAAQ,yBAAyB;AAC5D,SAASC,mBAAmB,QAAQ,0BAA0B;;AAE9D;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAE;EAC9D,MAAMC,cAAc,GAAGZ,iBAAiB,CAAC,CAAC;EAC1C,MAAMa,MAAM,GAAGF,OAAO,EAAEE,MAAM,IAAID,cAAc,CAACC,MAAM,IAAId,aAAa;EACxE,MAAMe,sBAAsB,GAAG,IAAI;EAEnC,MAAMC,UAAU,GAAGZ,UAAU,CAACM,SAAS,EAAEC,WAAW,CAAC;EAErD,IAAIM,KAAK,CAACD,UAAU,CAAC,EAAE,MAAM,IAAIE,UAAU,CAAC,oBAAoB,CAAC;EAEjE,MAAMC,eAAe,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAET,OAAO,EAAE;IACjDU,SAAS,EAAEV,OAAO,EAAEU,SAAS;IAC7BN,UAAU,EAAEA;EACd,CAAC,CAAC;EAEF,MAAM,CAACO,UAAU,EAAEC,YAAY,CAAC,GAAGrB,cAAc,CAC/CS,OAAO,EAAEa,EAAE,EACX,IAAIT,UAAU,GAAG,CAAC,GAAG,CAACL,WAAW,EAAED,SAAS,CAAC,GAAG,CAACA,SAAS,EAAEC,WAAW,CAAC,CAC1E,CAAC;EAED,MAAMe,OAAO,GAAGlB,mBAAmB,CAACgB,YAAY,EAAED,UAAU,CAAC;EAC7D,MAAMI,eAAe,GACnB,CAACzB,+BAA+B,CAACsB,YAAY,CAAC,GAC5CtB,+BAA+B,CAACqB,UAAU,CAAC,IAC7C,IAAI;EACN,MAAMK,OAAO,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACJ,OAAO,GAAGC,eAAe,IAAI,EAAE,CAAC;EAC5D,IAAII,MAAM;;EAEV;EACA,IAAIH,OAAO,GAAG,CAAC,EAAE;IACf,IAAIhB,OAAO,EAAEoB,cAAc,EAAE;MAC3B,IAAIN,OAAO,GAAG,CAAC,EAAE;QACf,OAAOZ,MAAM,CAACL,cAAc,CAAC,kBAAkB,EAAE,CAAC,EAAEU,eAAe,CAAC;MACtE,CAAC,MAAM,IAAIO,OAAO,GAAG,EAAE,EAAE;QACvB,OAAOZ,MAAM,CAACL,cAAc,CAAC,kBAAkB,EAAE,EAAE,EAAEU,eAAe,CAAC;MACvE,CAAC,MAAM,IAAIO,OAAO,GAAG,EAAE,EAAE;QACvB,OAAOZ,MAAM,CAACL,cAAc,CAAC,kBAAkB,EAAE,EAAE,EAAEU,eAAe,CAAC;MACvE,CAAC,MAAM,IAAIO,OAAO,GAAG,EAAE,EAAE;QACvB,OAAOZ,MAAM,CAACL,cAAc,CAAC,aAAa,EAAE,CAAC,EAAEU,eAAe,CAAC;MACjE,CAAC,MAAM,IAAIO,OAAO,GAAG,EAAE,EAAE;QACvB,OAAOZ,MAAM,CAACL,cAAc,CAAC,kBAAkB,EAAE,CAAC,EAAEU,eAAe,CAAC;MACtE,CAAC,MAAM;QACL,OAAOL,MAAM,CAACL,cAAc,CAAC,UAAU,EAAE,CAAC,EAAEU,eAAe,CAAC;MAC9D;IACF,CAAC,MAAM;MACL,IAAIS,OAAO,KAAK,CAAC,EAAE;QACjB,OAAOd,MAAM,CAACL,cAAc,CAAC,kBAAkB,EAAE,CAAC,EAAEU,eAAe,CAAC;MACtE,CAAC,MAAM;QACL,OAAOL,MAAM,CAACL,cAAc,CAAC,UAAU,EAAEmB,OAAO,EAAET,eAAe,CAAC;MACpE;IACF;;IAEA;EACF,CAAC,MAAM,IAAIS,OAAO,GAAG,EAAE,EAAE;IACvB,OAAOd,MAAM,CAACL,cAAc,CAAC,UAAU,EAAEmB,OAAO,EAAET,eAAe,CAAC;;IAElE;EACF,CAAC,MAAM,IAAIS,OAAO,GAAG,EAAE,EAAE;IACvB,OAAOd,MAAM,CAACL,cAAc,CAAC,aAAa,EAAE,CAAC,EAAEU,eAAe,CAAC;;IAE/D;EACF,CAAC,MAAM,IAAIS,OAAO,GAAGvB,YAAY,EAAE;IACjC,MAAM4B,KAAK,GAAGJ,IAAI,CAACC,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;IACtC,OAAOd,MAAM,CAACL,cAAc,CAAC,aAAa,EAAEwB,KAAK,EAAEd,eAAe,CAAC;;IAEnE;EACF,CAAC,MAAM,IAAIS,OAAO,GAAGb,sBAAsB,EAAE;IAC3C,OAAOD,MAAM,CAACL,cAAc,CAAC,OAAO,EAAE,CAAC,EAAEU,eAAe,CAAC;;IAEzD;EACF,CAAC,MAAM,IAAIS,OAAO,GAAGtB,cAAc,EAAE;IACnC,MAAM4B,IAAI,GAAGL,IAAI,CAACC,KAAK,CAACF,OAAO,GAAGvB,YAAY,CAAC;IAC/C,OAAOS,MAAM,CAACL,cAAc,CAAC,OAAO,EAAEyB,IAAI,EAAEf,eAAe,CAAC;;IAE5D;EACF,CAAC,MAAM,IAAIS,OAAO,GAAGtB,cAAc,GAAG,CAAC,EAAE;IACvCyB,MAAM,GAAGF,IAAI,CAACC,KAAK,CAACF,OAAO,GAAGtB,cAAc,CAAC;IAC7C,OAAOQ,MAAM,CAACL,cAAc,CAAC,cAAc,EAAEsB,MAAM,EAAEZ,eAAe,CAAC;EACvE;EAEAY,MAAM,GAAGxB,kBAAkB,CAACiB,YAAY,EAAED,UAAU,CAAC;;EAErD;EACA,IAAIQ,MAAM,GAAG,EAAE,EAAE;IACf,MAAMI,YAAY,GAAGN,IAAI,CAACC,KAAK,CAACF,OAAO,GAAGtB,cAAc,CAAC;IACzD,OAAOQ,MAAM,CAACL,cAAc,CAAC,SAAS,EAAE0B,YAAY,EAAEhB,eAAe,CAAC;;IAEtE;EACF,CAAC,MAAM;IACL,MAAMiB,sBAAsB,GAAGL,MAAM,GAAG,EAAE;IAC1C,MAAMM,KAAK,GAAGR,IAAI,CAACS,KAAK,CAACP,MAAM,GAAG,EAAE,CAAC;;IAErC;IACA,IAAIK,sBAAsB,GAAG,CAAC,EAAE;MAC9B,OAAOtB,MAAM,CAACL,cAAc,CAAC,aAAa,EAAE4B,KAAK,EAAElB,eAAe,CAAC;;MAEnE;IACF,CAAC,MAAM,IAAIiB,sBAAsB,GAAG,CAAC,EAAE;MACrC,OAAOtB,MAAM,CAACL,cAAc,CAAC,YAAY,EAAE4B,KAAK,EAAElB,eAAe,CAAC;;MAElE;IACF,CAAC,MAAM;MACL,OAAOL,MAAM,CAACL,cAAc,CAAC,cAAc,EAAE4B,KAAK,GAAG,CAAC,EAAElB,eAAe,CAAC;IAC1E;EACF;AACF;;AAEA;AACA,eAAeV,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}