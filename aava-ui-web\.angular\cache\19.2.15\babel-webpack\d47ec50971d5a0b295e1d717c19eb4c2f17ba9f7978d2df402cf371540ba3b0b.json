{"ast": null, "code": "import { asin, cos, epsilon, sin } from \"../math.js\";\nimport { azimuthalInvert } from \"./azimuthal.js\";\nimport projection from \"./index.js\";\nexport function orthographicRaw(x, y) {\n  return [cos(y) * sin(x), sin(y)];\n}\northographicRaw.invert = azimuthalInvert(asin);\nexport default function () {\n  return projection(orthographicRaw).scale(249.5).clipAngle(90 + epsilon);\n}", "map": {"version": 3, "names": ["asin", "cos", "epsilon", "sin", "azimuthalInvert", "projection", "orthographicRaw", "x", "y", "invert", "scale", "clipAngle"], "sources": ["C:/console/aava-ui-web/node_modules/d3-geo/src/projection/orthographic.js"], "sourcesContent": ["import {asin, cos, epsilon, sin} from \"../math.js\";\nimport {azimuthalInvert} from \"./azimuthal.js\";\nimport projection from \"./index.js\";\n\nexport function orthographicRaw(x, y) {\n  return [cos(y) * sin(x), sin(y)];\n}\n\northographicRaw.invert = azimuthalInvert(asin);\n\nexport default function() {\n  return projection(orthographicRaw)\n      .scale(249.5)\n      .clipAngle(90 + epsilon);\n}\n"], "mappings": "AAAA,SAAQA,IAAI,EAAEC,GAAG,EAAEC,OAAO,EAAEC,GAAG,QAAO,YAAY;AAClD,SAAQC,eAAe,QAAO,gBAAgB;AAC9C,OAAOC,UAAU,MAAM,YAAY;AAEnC,OAAO,SAASC,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACpC,OAAO,CAACP,GAAG,CAACO,CAAC,CAAC,GAAGL,GAAG,CAACI,CAAC,CAAC,EAAEJ,GAAG,CAACK,CAAC,CAAC,CAAC;AAClC;AAEAF,eAAe,CAACG,MAAM,GAAGL,eAAe,CAACJ,IAAI,CAAC;AAE9C,eAAe,YAAW;EACxB,OAAOK,UAAU,CAACC,eAAe,CAAC,CAC7BI,KAAK,CAAC,KAAK,CAAC,CACZC,SAAS,CAAC,EAAE,GAAGT,OAAO,CAAC;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}