{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let ThemeService = /*#__PURE__*/(() => {\n  class ThemeService {\n    currentTheme = 'light';\n    getCurrentTheme() {\n      return this.currentTheme;\n    }\n    toggleTheme() {\n      this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';\n      document.body.className = this.currentTheme;\n    }\n    static ɵfac = function ThemeService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ThemeService)();\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ThemeService,\n      factory: ThemeService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return ThemeService;\n})();", "map": {"version": 3, "names": ["ThemeService", "currentTheme", "getCurrentTheme", "toggleTheme", "document", "body", "className", "factory", "ɵfac", "providedIn"], "sources": ["C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\shared\\services\\theme.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ThemeService {\r\n  private currentTheme: 'light' | 'dark' = 'light';\r\n\r\n  getCurrentTheme(): 'light' | 'dark' {\r\n    return this.currentTheme;\r\n  }\r\n\r\n  toggleTheme(): void {\r\n    this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';\r\n    document.body.className = this.currentTheme;\r\n  }\r\n} "], "mappings": ";AAKA,WAAaA,YAAY;EAAnB,MAAOA,YAAY;IACfC,YAAY,GAAqB,OAAO;IAEhDC,eAAeA,CAAA;MACb,OAAO,IAAI,CAACD,YAAY;IAC1B;IAEAE,WAAWA,CAAA;MACT,IAAI,CAACF,YAAY,GAAG,IAAI,CAACA,YAAY,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO;MACpEG,QAAQ,CAACC,IAAI,CAACC,SAAS,GAAG,IAAI,CAACL,YAAY;IAC7C;;uCAVWD,YAAY;IAAA;;aAAZA,YAAY;MAAAO,OAAA,EAAZP,YAAY,CAAAQ,IAAA;MAAAC,UAAA,EAFX;IAAM;;SAEPT,YAAY;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}