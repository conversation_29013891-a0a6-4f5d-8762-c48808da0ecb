{"ast": null, "code": "import { asin, sqrt } from \"../math.js\";\nimport { azimuthalRaw, azimuthalInvert } from \"./azimuthal.js\";\nimport projection from \"./index.js\";\nexport var azimuthalEqualAreaRaw = azimuthalRaw(function (cxcy) {\n  return sqrt(2 / (1 + cxcy));\n});\nazimuthalEqualAreaRaw.invert = azimuthalInvert(function (z) {\n  return 2 * asin(z / 2);\n});\nexport default function () {\n  return projection(azimuthalEqualAreaRaw).scale(124.75).clipAngle(180 - 1e-3);\n}", "map": {"version": 3, "names": ["asin", "sqrt", "azimuthalRaw", "azimuthalInvert", "projection", "azimuthalEqualAreaRaw", "cxcy", "invert", "z", "scale", "clipAngle"], "sources": ["C:/console/aava-ui-web/node_modules/d3-geo/src/projection/azimuthalEqualArea.js"], "sourcesContent": ["import {asin, sqrt} from \"../math.js\";\nimport {azimuthalRaw, azimuthalInvert} from \"./azimuthal.js\";\nimport projection from \"./index.js\";\n\nexport var azimuthalEqualAreaRaw = azimuthalRaw(function(cxcy) {\n  return sqrt(2 / (1 + cxcy));\n});\n\nazimuthalEqualAreaRaw.invert = azimuthalInvert(function(z) {\n  return 2 * asin(z / 2);\n});\n\nexport default function() {\n  return projection(azimuthalEqualAreaRaw)\n      .scale(124.75)\n      .clipAngle(180 - 1e-3);\n}\n"], "mappings": "AAAA,SAAQA,IAAI,EAAEC,IAAI,QAAO,YAAY;AACrC,SAAQC,YAAY,EAAEC,eAAe,QAAO,gBAAgB;AAC5D,OAAOC,UAAU,MAAM,YAAY;AAEnC,OAAO,IAAIC,qBAAqB,GAAGH,YAAY,CAAC,UAASI,IAAI,EAAE;EAC7D,OAAOL,IAAI,CAAC,CAAC,IAAI,CAAC,GAAGK,IAAI,CAAC,CAAC;AAC7B,CAAC,CAAC;AAEFD,qBAAqB,CAACE,MAAM,GAAGJ,eAAe,CAAC,UAASK,CAAC,EAAE;EACzD,OAAO,CAAC,GAAGR,IAAI,CAACQ,CAAC,GAAG,CAAC,CAAC;AACxB,CAAC,CAAC;AAEF,eAAe,YAAW;EACxB,OAAOJ,UAAU,CAACC,qBAAqB,CAAC,CACnCI,KAAK,CAAC,MAAM,CAAC,CACbC,SAAS,CAAC,GAAG,GAAG,IAAI,CAAC;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}