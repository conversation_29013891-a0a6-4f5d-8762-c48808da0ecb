{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormControl, ReactiveFormsModule } from '@angular/forms';\nimport { ButtonComponent, AvaTextboxComponent, AvaTextareaComponent, IconComponent } from '@ava/play-comp-library';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nconst _c0 = [\"nodeTemplate\"];\nconst _c1 = [\"canvasContainer\"];\nconst _c2 = [[[\"\", \"info\", \"\"]], [[\"\", \"actionLeft\", \"\"]]];\nconst _c3 = [\"[info]\", \"[actionLeft]\"];\nconst _c4 = () => ({\n  \"border-radius\": \"8px\",\n  border: \"1px solid var(--Global-colors-Royal-blue-500, #2563EB) !important\"\n});\nconst _c5 = () => ({\n  background: \"linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)\",\n  \"--button-effect-color\": \"33, 90, 214\",\n  \"border-radius\": \"8px\"\n});\nconst _c6 = () => ({\n  background: \"linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)\",\n  \"--button-effect-color\": \"33, 90, 214\"\n});\nconst _c7 = (a0, a1, a2, a3, a4, a5, a6, a7, a8) => ({\n  $implicit: a0,\n  selected: a1,\n  onDelete: a2,\n  onMove: a3,\n  onSelect: a4,\n  onDoubleClick: a5,\n  onStartConnection: a6,\n  mouseInteractionsEnabled: a7,\n  canvasMode: a8\n});\nfunction CanvasBoardComponent_div_1_div_2_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25)(2, \"div\", 26)(3, \"label\", 27);\n    i0.ɵɵtext(4, \"Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"ava-textbox\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 26)(7, \"label\", 27);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(9, \"ava-textarea\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 30)(11, \"ava-button\", 31);\n    i0.ɵɵlistener(\"userClick\", function CanvasBoardComponent_div_1_div_2_div_6_Template_ava_button_userClick_11_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.cancelAgentDetails());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"ava-button\", 32);\n    i0.ɵɵlistener(\"userClick\", function CanvasBoardComponent_div_1_div_2_div_6_Template_ava_button_userClick_12_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.applyAgentDetails());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"placeholder\", (ctx_r2.inputFieldsConfig.agentDetails == null ? null : ctx_r2.inputFieldsConfig.agentDetails.namePlaceholder) || \"Enter name\")(\"formControl\", ctx_r2.agentDetailNameControl)(\"fullWidth\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((ctx_r2.inputFieldsConfig.agentDetails == null ? null : ctx_r2.inputFieldsConfig.agentDetails.detailLabel) || \"Description\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"placeholder\", (ctx_r2.inputFieldsConfig.agentDetails == null ? null : ctx_r2.inputFieldsConfig.agentDetails.detailPlaceholder) || \"Enter agent description\")(\"formControl\", ctx_r2.agentDetailControl)(\"rows\", 4)(\"fullWidth\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"customStyles\", i0.ɵɵpureFunction0(10, _c4));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"customStyles\", i0.ɵɵpureFunction0(11, _c5));\n  }\n}\nfunction CanvasBoardComponent_div_1_div_2_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵprojection(1, 1);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CanvasBoardComponent_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19)(2, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function CanvasBoardComponent_div_1_div_2_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.toggleAgentDetailsDropdown());\n    });\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"ava-icon\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, CanvasBoardComponent_div_1_div_2_div_6_Template, 13, 12, \"div\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, CanvasBoardComponent_div_1_div_2_div_7_Template, 2, 0, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"open\", ctx_r2.isAgentDetailsDropdownOpen);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"open\", ctx_r2.isAgentDetailsDropdownOpen);\n    i0.ɵɵattribute(\"aria-expanded\", ctx_r2.isAgentDetailsDropdownOpen);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((ctx_r2.inputFieldsConfig.agentDetails == null ? null : ctx_r2.inputFieldsConfig.agentDetails.label) || \"Agent Details\");\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"transform\", ctx_r2.isAgentDetailsDropdownOpen ? \"rotate(180deg)\" : \"rotate(0deg)\");\n    i0.ɵɵproperty(\"iconSize\", 16);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isAgentDetailsDropdownOpen);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showLeftActions);\n  }\n}\nfunction CanvasBoardComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 16);\n    i0.ɵɵtemplate(2, CanvasBoardComponent_div_1_div_2_Template, 8, 11, \"div\", 17);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.inputFieldsConfig.agentDetails == null ? null : ctx_r2.inputFieldsConfig.agentDetails.enabled);\n  }\n}\nfunction CanvasBoardComponent_div_2_span_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" | \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CanvasBoardComponent_div_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, CanvasBoardComponent_div_2_span_1_span_2_Template, 2, 0, \"span\", 36);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const hint_r5 = ctx.$implicit;\n    const last_r6 = ctx.last;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", hint_r5, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !last_r6);\n  }\n}\nfunction CanvasBoardComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵtemplate(1, CanvasBoardComponent_div_2_span_1_Template, 3, 2, \"span\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.navigationHints);\n  }\n}\nfunction CanvasBoardComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"ava-button\", 38);\n    i0.ɵɵlistener(\"userClick\", function CanvasBoardComponent_div_6_Template_ava_button_userClick_1_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.setSelectMode());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"ava-button\", 39);\n    i0.ɵɵlistener(\"userClick\", function CanvasBoardComponent_div_6_Template_ava_button_userClick_2_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.setPanMode());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ava-button\", 40);\n    i0.ɵɵlistener(\"userClick\", function CanvasBoardComponent_div_6_Template_ava_button_userClick_3_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      ctx_r2.zoomIn();\n      return i0.ɵɵresetView(ctx_r2.setZoomMode());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"ava-button\", 41);\n    i0.ɵɵlistener(\"userClick\", function CanvasBoardComponent_div_6_Template_ava_button_userClick_4_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      ctx_r2.zoomOut();\n      return i0.ɵɵresetView(ctx_r2.setZoomMode());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"state\", ctx_r2.canvasMode === \"select\" ? \"active\" : \"default\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"iconColor\", \"var(--rgb-brand-primary)\")(\"state\", ctx_r2.canvasMode === \"pan\" ? \"active\" : \"default\");\n  }\n}\nfunction CanvasBoardComponent_div_7_ava_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ava-button\", 48);\n    i0.ɵɵlistener(\"userClick\", function CanvasBoardComponent_div_7_ava_button_2_Template_ava_button_userClick_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onUndo());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CanvasBoardComponent_div_7_ava_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ava-button\", 49);\n    i0.ɵɵlistener(\"userClick\", function CanvasBoardComponent_div_7_ava_button_3_Template_ava_button_userClick_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onRedo());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CanvasBoardComponent_div_7_ava_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ava-button\", 50);\n    i0.ɵɵlistener(\"userClick\", function CanvasBoardComponent_div_7_ava_button_4_Template_ava_button_userClick_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onPrimaryButtonClick());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"customStyles\", i0.ɵɵpureFunction0(1, _c6));\n  }\n}\nfunction CanvasBoardComponent_div_7_ava_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ava-button\", 51);\n    i0.ɵɵlistener(\"userClick\", function CanvasBoardComponent_div_7_ava_button_5_Template_ava_button_userClick_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onPrimaryButtonClick());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"label\", ctx_r2.primaryButtonText)(\"customStyles\", i0.ɵɵpureFunction0(3, _c5))(\"iconName\", ctx_r2.primaryButtonIcon ? \"\" : \"Play\");\n  }\n}\nfunction CanvasBoardComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"div\", 43);\n    i0.ɵɵtemplate(2, CanvasBoardComponent_div_7_ava_button_2_Template, 1, 0, \"ava-button\", 44)(3, CanvasBoardComponent_div_7_ava_button_3_Template, 1, 0, \"ava-button\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, CanvasBoardComponent_div_7_ava_button_4_Template, 1, 2, \"ava-button\", 46)(5, CanvasBoardComponent_div_7_ava_button_5_Template, 1, 4, \"ava-button\", 47);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.enableUndo && !ctx_r2.isExecuteMode);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.enableRedo && !ctx_r2.isExecuteMode);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isExecuteMode);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isExecuteMode);\n  }\n}\nfunction CanvasBoardComponent__svg_svg_8__svg_path_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelement(0, \"path\", 56);\n  }\n  if (rf & 2) {\n    const edge_r12 = ctx.$implicit;\n    i0.ɵɵclassProp(\"edge-animated\", edge_r12.animated);\n    i0.ɵɵattribute(\"d\", edge_r12.pathData || \"\")(\"id\", edge_r12.id);\n  }\n}\nfunction CanvasBoardComponent__svg_svg_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 52);\n    i0.ɵɵtemplate(1, CanvasBoardComponent__svg_svg_8__svg_path_1_Template, 1, 4, \"path\", 53);\n    i0.ɵɵelementStart(2, \"defs\")(3, \"marker\", 54);\n    i0.ɵɵelement(4, \"path\", 55);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"position\", \"absolute\")(\"top\", \"0\")(\"left\", \"0\")(\"width\", \"100%\")(\"height\", \"100%\")(\"pointer-events\", \"none\")(\"z-index\", \"5\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.edges);\n  }\n}\nfunction CanvasBoardComponent__svg_svg_11__svg_path_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelement(0, \"path\", 61);\n  }\n  if (rf & 2) {\n    const edge_r13 = ctx.$implicit;\n    i0.ɵɵclassProp(\"edge-animated\", edge_r13.animated);\n    i0.ɵɵattribute(\"d\", edge_r13.pathData || \"\")(\"id\", edge_r13.id);\n  }\n}\nfunction CanvasBoardComponent__svg_svg_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 57);\n    i0.ɵɵtemplate(1, CanvasBoardComponent__svg_svg_11__svg_path_1_Template, 1, 4, \"path\", 58);\n    i0.ɵɵelementStart(2, \"defs\")(3, \"marker\", 59);\n    i0.ɵɵelement(4, \"path\", 60);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.edges);\n  }\n}\nfunction CanvasBoardComponent_ng_container_13_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction CanvasBoardComponent_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, CanvasBoardComponent_ng_container_13_ng_container_1_Template, 1, 0, \"ng-container\", 62);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const node_r14 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.nodeTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunctionV(2, _c7, [node_r14, ctx_r2.selectedNodeId === node_r14.id, ctx_r2.onDeleteNode.bind(ctx_r2), ctx_r2.onNodeMoved.bind(ctx_r2), ctx_r2.onNodeSelected.bind(ctx_r2), ctx_r2.onNodeDoubleClicked.bind(ctx_r2), ctx_r2.onStartConnection.bind(ctx_r2), ctx_r2.mouseInteractionsEnabled, ctx_r2.isExecuteMode ? \"execute\" : \"build\"]));\n  }\n}\nfunction CanvasBoardComponent__svg_svg_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 63);\n    i0.ɵɵelement(1, \"path\", 64);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"d\", ctx_r2.getTempConnectionPath());\n  }\n}\nfunction CanvasBoardComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.fallbackMessage, \" \");\n  }\n}\nexport let CanvasBoardComponent = /*#__PURE__*/(() => {\n  class CanvasBoardComponent {\n    cdr;\n    canvasContainer;\n    nodeTemplate;\n    // Inputs\n    nodes = [];\n    edges = [];\n    showGrid = true;\n    enablePan = false; // Disabled by default\n    enableZoom = false; // Disabled by default\n    enableConnections = true;\n    minZoom = 0.1;\n    maxZoom = 2;\n    connectionColor = '#9ca3af'; // Grey color for connections\n    fallbackMessage = 'Drop items here to get started';\n    navigationHints = ['Select toolbar options to enable canvas interactions', 'Alt + Drag to pan canvas (when enabled)', 'Mouse wheel to zoom (when enabled)', 'Space to reset view'];\n    showToolbar = true;\n    primaryButtonText = 'Execute';\n    primaryButtonIcon = '';\n    enableUndo = true;\n    enableRedo = true;\n    enableReset = true;\n    showCanvasTools = true;\n    enableGridToggle = true;\n    enablePanMode = true;\n    enableSelectionMode = true;\n    enableZoomControls = true;\n    isExecuteMode = false;\n    showLeftActions = true;\n    mouseInteractionsEnabled = true;\n    // Built-in input fields configuration\n    showHeaderInputs = false;\n    inputFieldsConfig = {};\n    // Input properties for initial values\n    initialAgentName = '';\n    initialAgentDetails = '';\n    initialMetadata = {\n      org: '',\n      domain: '',\n      project: '',\n      team: ''\n    };\n    // Built-in metadata dropdown state\n    isMetadataDropdownOpen = false;\n    metadataStatus = 'Metadata Information not saved';\n    // Agent details dropdown state\n    isAgentDetailsDropdownOpen = false;\n    // Form controls for built-in inputs\n    agentNameControl = new FormControl('');\n    agentTypeControl = new FormControl('individual');\n    agentTypeDisplayControl = new FormControl('individual');\n    // Agent details form controls\n    agentDetailNameControl = new FormControl('');\n    agentDetailControl = new FormControl('');\n    // Metadata form controls\n    orgControl = new FormControl('');\n    domainControl = new FormControl('');\n    projectControl = new FormControl('');\n    teamControl = new FormControl('');\n    // Dropdown options\n    dropdownValues = {\n      org: [{\n        value: 'ascendion',\n        label: 'Ascendion'\n      }, {\n        value: 'company2',\n        label: 'Company 2'\n      }, {\n        value: 'company3',\n        label: 'Company 3'\n      }],\n      domain: [],\n      project: [],\n      team: []\n    };\n    // Outputs\n    nodeAdded = new EventEmitter();\n    nodeRemoved = new EventEmitter();\n    nodeMoved = new EventEmitter();\n    nodeSelected = new EventEmitter();\n    nodeDoubleClicked = new EventEmitter();\n    connectionStarted = new EventEmitter();\n    connectionCreated = new EventEmitter();\n    canvasDropped = new EventEmitter();\n    viewportChanged = new EventEmitter();\n    undoAction = new EventEmitter();\n    redoAction = new EventEmitter();\n    resetAction = new EventEmitter();\n    primaryButtonClicked = new EventEmitter();\n    stateChanged = new EventEmitter();\n    agentNameChanged = new EventEmitter();\n    agentTypeChanged = new EventEmitter();\n    metadataChanged = new EventEmitter();\n    agentDetailsChanged = new EventEmitter();\n    // Internal state\n    selectedNodeId = null;\n    tempConnection = {\n      isActive: false\n    };\n    nodeConnectionPoints = {};\n    viewport = {\n      zoom: 1,\n      x: 0,\n      y: 0,\n      isDragging: false,\n      lastMouseX: 0,\n      lastMouseY: 0\n    };\n    // Performance optimization for connection updates\n    updateConnectionPointsFrame = null;\n    // Internal history management\n    history = [];\n    historyIndex = -1;\n    maxHistorySize = 50;\n    isRestoringState = false;\n    // Canvas tool states\n    canvasMode = 'select'; // Start with select mode\n    showGridDots = true;\n    // Mouse function controls\n    // mouseInteractionsEnabled: boolean = true; // Enable mouse interactions by default\n    // Toolbar is now fixed at bottom center - no drag state needed\n    constructor(cdr) {\n      this.cdr = cdr;\n    }\n    ngOnInit() {\n      // Set initial values\n      this.setInitialValues();\n      // Subscribe to agent name changes\n      this.agentNameControl.valueChanges.subscribe(value => {\n        if (value !== null && value !== undefined) {\n          this.agentNameChanged.emit(value);\n        }\n      });\n      // Subscribe to agent type changes\n      this.agentTypeControl.valueChanges.subscribe(value => {\n        if (value !== null && value !== undefined) {\n          this.agentTypeChanged.emit(value);\n        }\n      });\n    }\n    setInitialValues() {\n      // Set agent name\n      if (this.initialAgentName) {\n        this.agentNameControl.setValue(this.initialAgentName, {\n          emitEvent: false\n        });\n      }\n      // Set agent details\n      if (this.initialAgentName) {\n        this.agentDetailNameControl.setValue(this.initialAgentName, {\n          emitEvent: false\n        });\n      }\n      if (this.initialAgentDetails) {\n        this.agentDetailControl.setValue(this.initialAgentDetails, {\n          emitEvent: false\n        });\n      }\n      // Set metadata\n      if (this.initialMetadata) {\n        this.orgControl.setValue(this.initialMetadata.org, {\n          emitEvent: false\n        });\n        this.domainControl.setValue(this.initialMetadata.domain, {\n          emitEvent: false\n        });\n        this.projectControl.setValue(this.initialMetadata.project, {\n          emitEvent: false\n        });\n        this.teamControl.setValue(this.initialMetadata.team, {\n          emitEvent: false\n        });\n      }\n    }\n    // TrackBy function for nodes to maintain DOM element identity\n    trackByNodeId(index, node) {\n      return node.id;\n    }\n    ngOnChanges(changes) {\n      // Update initial values when inputs change\n      if (changes['initialAgentName'] || changes['initialAgentDetails'] || changes['initialMetadata']) {\n        this.setInitialValues();\n      }\n      // Update internal state when inputs change (but not during state restoration)\n      if (!this.isRestoringState) {\n        if (changes.nodes && changes.nodes.currentValue) {\n          console.log('🔗 ngOnChanges - Received new nodes:', changes.nodes.currentValue.map(n => ({\n            id: n.id,\n            position: n.position\n          })));\n          this.nodes = [...changes.nodes.currentValue];\n        }\n        if (changes.edges && changes.edges.currentValue) {\n          console.log('🔗 ngOnChanges - Received new edges:', changes.edges.currentValue.map(e => ({\n            id: e.id,\n            source: e.source,\n            target: e.target\n          })));\n          this.edges = [...changes.edges.currentValue];\n        }\n        // Update connection points when nodes change - IMMEDIATE\n        if (changes.nodes) {\n          console.log('🔗 ngOnChanges - Updating for node changes');\n          this.updateNodeConnectionPoints();\n          this.updateEdgePaths();\n          // Force additional update after DOM settles\n          setTimeout(() => this.updateNodeConnectionPoints(), 0);\n        }\n        if (changes.edges) {\n          console.log('🔗 ngOnChanges - Updating for edge changes');\n          this.updateEdgePaths();\n        }\n      }\n      // Update connection points when execute mode changes\n      if (changes['isExecuteMode']) {\n        // Toolbar is now fixed at bottom center via CSS\n        this.updateNodeConnectionPoints();\n      }\n      if (changes['mouseInteractionsEnabled']) {\n        this.mouseInteractionsEnabled = changes['mouseInteractionsEnabled'].currentValue;\n      }\n    }\n    ngAfterViewInit() {\n      this.setupCanvasNavigation();\n      // Use requestAnimationFrame for smooth initialization\n      requestAnimationFrame(() => {\n        this.updateNodeConnectionPoints();\n        // Initialize history with current state\n        this.saveToHistory();\n        // Toolbar is now fixed at bottom center via CSS\n        // Set initial cursor to default arrow\n        this.setSelectMode();\n      });\n    }\n    ngOnDestroy() {\n      // Cleanup event listeners\n      document.removeEventListener('keydown', this.handleKeyDown);\n      // Cancel any pending animation frame\n      if (this.updateConnectionPointsFrame) {\n        cancelAnimationFrame(this.updateConnectionPointsFrame);\n        this.updateConnectionPointsFrame = null;\n      }\n    }\n    setupCanvasNavigation() {\n      const element = this.canvasContainer?.nativeElement;\n      if (element) {\n        document.addEventListener('keydown', this.handleKeyDown.bind(this));\n        element.addEventListener('mouseup', () => {\n          if (this.viewport.isDragging) {\n            this.updateNodeConnectionPoints();\n          }\n        });\n      }\n    }\n    handleKeyDown = event => {\n      if (event.key === ' ') {\n        // Only reset viewport if the user is not typing in an input field\n        const target = event.target;\n        const isInputField = target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.contentEditable === 'true' || target.closest('ava-textbox') || target.closest('ava-textarea') || target.closest('.form-field') || target.closest('.input-container') || target.closest('.chat-input') || target.closest('textarea') || target.closest('input');\n        if (!isInputField) {\n          this.resetViewport();\n          event.preventDefault();\n        }\n      }\n    };\n    resetViewport() {\n      this.viewport = {\n        zoom: 1,\n        x: 0,\n        y: 0,\n        isDragging: false,\n        lastMouseX: 0,\n        lastMouseY: 0\n      };\n      this.updateNodeConnectionPoints();\n      this.viewportChanged.emit(this.viewport);\n      this.cdr.detectChanges();\n    }\n    onDragOver(event) {\n      if (!this.enableConnections || !this.mouseInteractionsEnabled) return;\n      event.preventDefault();\n      if (event.dataTransfer) {\n        event.dataTransfer.dropEffect = 'move';\n      }\n    }\n    onDrop(event) {\n      if (!this.mouseInteractionsEnabled) return;\n      event.preventDefault();\n      // Save history before adding new node\n      this.saveHistoryBeforeAction();\n      const canvasBounds = this.canvasContainer.nativeElement.getBoundingClientRect();\n      const position = {\n        x: (event.clientX - canvasBounds.left - this.viewport.x) / this.viewport.zoom,\n        y: (event.clientY - canvasBounds.top - this.viewport.y) / this.viewport.zoom\n      };\n      const safePosition = {\n        x: Math.max(0, position.x),\n        y: Math.max(0, position.y)\n      };\n      this.canvasDropped.emit({\n        event,\n        position: safePosition\n      });\n    }\n    onNodeSelected(nodeId) {\n      this.selectedNodeId = nodeId;\n      this.nodeSelected.emit(nodeId);\n    }\n    onNodeDoubleClicked(nodeId) {\n      this.nodeDoubleClicked.emit(nodeId);\n    }\n    onNodeMoved(data) {\n      // Save history before moving node\n      this.saveHistoryBeforeAction();\n      // Update node position in internal state\n      const nodeIndex = this.nodes.findIndex(node => node.id === data.nodeId);\n      if (nodeIndex !== -1) {\n        this.nodes[nodeIndex] = {\n          ...this.nodes[nodeIndex],\n          position: data.position\n        };\n      }\n      // CRITICAL: Update edge paths immediately when node moves\n      console.log('🔗 Node moved - updating edge paths for:', data.nodeId);\n      this.updateEdgePaths();\n      this.nodeMoved.emit(data);\n      this.updateNodeConnectionPoints();\n      // Force immediate change detection to update connections\n      this.cdr.detectChanges();\n    }\n    onStartConnection(data) {\n      if (!this.enableConnections) return;\n      const node = this.nodes.find(n => n.id === data.nodeId);\n      if (!node) return;\n      // Enhanced connection starting - use mouse position on the node as starting point\n      const canvasRect = this.canvasContainer.nativeElement.getBoundingClientRect();\n      // Calculate the exact mouse position in canvas coordinates\n      const mouseX = (data.event.clientX - canvasRect.left - this.viewport.x) / this.viewport.zoom;\n      const mouseY = (data.event.clientY - canvasRect.top - this.viewport.y) / this.viewport.zoom;\n      // Use the mouse position as the starting point for more natural connections\n      let handleX = mouseX;\n      let handleY = mouseY;\n      // Get the node's connection points for fallback\n      const connectionPoints = this.nodeConnectionPoints[data.nodeId];\n      if (connectionPoints) {\n        // If we have calculated connection points, use the nearest edge point\n        const nodeElement = document.querySelector(`[data-node-id=\"${data.nodeId}\"]`);\n        if (nodeElement) {\n          const rect = nodeElement.getBoundingClientRect();\n          const nodeX = (rect.left - canvasRect.left - this.viewport.x) / this.viewport.zoom;\n          const nodeY = (rect.top - canvasRect.top - this.viewport.y) / this.viewport.zoom;\n          const nodeWidth = rect.width / this.viewport.zoom;\n          const nodeHeight = rect.height / this.viewport.zoom;\n          // Determine which edge of the node is closest to the mouse\n          const relativeX = mouseX - nodeX;\n          const relativeY = mouseY - nodeY;\n          // Clamp to node edges for clean connection start points\n          if (relativeX <= 0) {\n            // Left edge\n            handleX = nodeX;\n            handleY = Math.max(nodeY, Math.min(nodeY + nodeHeight, mouseY));\n          } else if (relativeX >= nodeWidth) {\n            // Right edge\n            handleX = nodeX + nodeWidth;\n            handleY = Math.max(nodeY, Math.min(nodeY + nodeHeight, mouseY));\n          } else if (relativeY <= 0) {\n            // Top edge\n            handleX = Math.max(nodeX, Math.min(nodeX + nodeWidth, mouseX));\n            handleY = nodeY;\n          } else if (relativeY >= nodeHeight) {\n            // Bottom edge\n            handleX = Math.max(nodeX, Math.min(nodeX + nodeWidth, mouseX));\n            handleY = nodeY + nodeHeight;\n          }\n        }\n      }\n      this.tempConnection = {\n        isActive: true,\n        sourceNodeId: data.nodeId,\n        sourceHandleType: data.handleType,\n        sourceX: handleX,\n        sourceY: handleY,\n        targetX: mouseX,\n        targetY: mouseY\n      };\n      this.connectionStarted.emit(data);\n    }\n    onDeleteNode(nodeId) {\n      // Save history before deleting node\n      this.saveHistoryBeforeAction();\n      // Remove node from internal state\n      this.nodes = this.nodes.filter(node => node.id !== nodeId);\n      // Remove any edges connected to this node\n      this.edges = this.edges.filter(edge => edge.source !== nodeId && edge.target !== nodeId);\n      this.nodeRemoved.emit(nodeId);\n      if (this.selectedNodeId === nodeId) {\n        this.selectedNodeId = null;\n      }\n      this.updateNodeConnectionPoints();\n    }\n    onCanvasMouseMove(event) {\n      // Handle canvas panning\n      if (this.viewport.isDragging && this.enablePan) {\n        const deltaX = event.clientX - this.viewport.lastMouseX;\n        const deltaY = event.clientY - this.viewport.lastMouseY;\n        this.viewport.x += deltaX;\n        this.viewport.y += deltaY;\n        this.viewport.lastMouseX = event.clientX;\n        this.viewport.lastMouseY = event.clientY;\n        this.cdr.detectChanges();\n        return;\n      }\n      if (!this.tempConnection.isActive) return;\n      // Update the temporary connection endpoint\n      const canvasRect = this.canvasContainer.nativeElement.getBoundingClientRect();\n      const targetX = (event.clientX - canvasRect.left - this.viewport.x) / this.viewport.zoom;\n      const targetY = (event.clientY - canvasRect.top - this.viewport.y) / this.viewport.zoom;\n      this.tempConnection = {\n        ...this.tempConnection,\n        targetX,\n        targetY\n      };\n      this.cdr.detectChanges();\n    }\n    onCanvasWheel(event) {\n      if (!this.enableZoom || !this.mouseInteractionsEnabled) return;\n      event.preventDefault();\n      const delta = -event.deltaY;\n      const zoomSpeed = 0.001;\n      const newZoom = Math.max(this.minZoom, Math.min(this.maxZoom, this.viewport.zoom + delta * zoomSpeed));\n      if (newZoom !== this.viewport.zoom) {\n        const rect = this.canvasContainer.nativeElement.getBoundingClientRect();\n        const mouseX = event.clientX - rect.left;\n        const mouseY = event.clientY - rect.top;\n        const zoomRatio = newZoom / this.viewport.zoom;\n        const newX = mouseX - (mouseX - this.viewport.x) * zoomRatio;\n        const newY = mouseY - (mouseY - this.viewport.y) * zoomRatio;\n        this.viewport.zoom = newZoom;\n        this.viewport.x = newX;\n        this.viewport.y = newY;\n        this.updateNodeConnectionPoints();\n        this.viewportChanged.emit(this.viewport);\n        this.cdr.detectChanges();\n      }\n    }\n    onCanvasMouseDown(event) {\n      if (!this.enablePan || !this.mouseInteractionsEnabled) return;\n      // Start canvas dragging when:\n      // 1. Middle mouse button is pressed\n      // 2. Alt+left click\n      // 3. Pan mode is active and left click\n      if (event.button === 1 || event.button === 0 && event.altKey || event.button === 0 && this.canvasMode === 'pan') {\n        this.viewport.isDragging = true;\n        this.viewport.lastMouseX = event.clientX;\n        this.viewport.lastMouseY = event.clientY;\n        // Update cursor during drag\n        if (this.canvasContainer) {\n          this.canvasContainer.nativeElement.style.cursor = 'grabbing';\n        }\n        event.preventDefault();\n      }\n    }\n    onCanvasMouseUp(event) {\n      this.viewport.isDragging = false;\n      // Restore cursor based on current mode\n      if (this.canvasContainer) {\n        this.canvasContainer.nativeElement.style.cursor = this.canvasMode === 'pan' ? 'grab' : 'default';\n      }\n      if (!this.tempConnection.isActive || !this.enableConnections) return;\n      const target = event.target;\n      // Enhanced connection logic - allow connections to any part of a node\n      const nodeElement = target.closest('[data-node-id]');\n      if (nodeElement && this.tempConnection.sourceNodeId) {\n        const sourceNodeId = this.tempConnection.sourceNodeId;\n        const targetNodeId = nodeElement.getAttribute('data-node-id');\n        // Allow connections between different nodes\n        if (targetNodeId && sourceNodeId !== targetNodeId) {\n          // Check if connection already exists to prevent duplicates\n          const existingConnection = this.edges.find(edge => edge.source === sourceNodeId && edge.target === targetNodeId || edge.source === targetNodeId && edge.target === sourceNodeId);\n          if (!existingConnection) {\n            // Save history before creating connection\n            this.saveHistoryBeforeAction();\n            const newEdge = {\n              id: `${sourceNodeId}-${targetNodeId}`,\n              source: sourceNodeId,\n              target: targetNodeId,\n              animated: false // Beautiful static connections\n            };\n            // Add edge to internal state\n            this.edges.push(newEdge);\n            this.connectionCreated.emit(newEdge);\n          }\n        }\n      }\n      this.tempConnection = {\n        isActive: false\n      };\n    }\n    updateNodeConnectionPoints() {\n      if (this.updateConnectionPointsFrame) {\n        cancelAnimationFrame(this.updateConnectionPointsFrame);\n      }\n      // SIMPLIFIED: Immediate update for consistent connection rendering\n      this.updateConnectionPointsFrame = requestAnimationFrame(() => {\n        this.nodeConnectionPoints = {};\n        for (const node of this.nodes) {\n          const nodeElement = document.querySelector(`[data-node-id=\"${node.id}\"]`);\n          // Calculate connection points even for nodes outside viewport\n          let nodeX, nodeY, nodeWidth, nodeHeight;\n          if (nodeElement) {\n            // Node is in DOM - use actual measurements\n            const rect = nodeElement.getBoundingClientRect();\n            const canvasRect = this.canvasContainer?.nativeElement.getBoundingClientRect();\n            if (!canvasRect) continue;\n            // In execute mode, always use the actual DOM position since CSS transforms the layout\n            if (this.isExecuteMode) {\n              // Use actual DOM position without viewport adjustments for execute mode\n              nodeX = rect.left - canvasRect.left;\n              nodeY = rect.top - canvasRect.top;\n              nodeWidth = rect.width;\n              nodeHeight = rect.height;\n            } else {\n              // Build mode - use viewport-adjusted coordinates\n              nodeX = (rect.left - canvasRect.left - this.viewport.x) / this.viewport.zoom;\n              nodeY = (rect.top - canvasRect.top - this.viewport.y) / this.viewport.zoom;\n              nodeWidth = rect.width / this.viewport.zoom;\n              nodeHeight = rect.height / this.viewport.zoom;\n            }\n          } else {\n            // Node is outside viewport - use node position data\n            nodeX = node.position.x;\n            nodeY = node.position.y;\n            // Use default dimensions based on mode\n            nodeWidth = this.isExecuteMode ? 55 : 90; // Execute mode nodes are 55px (circular with border)\n            nodeHeight = this.isExecuteMode ? 55 : 48; // Execute mode nodes are 55px (circular with border)\n          }\n          if (this.isExecuteMode) {\n            // Execute mode - icon center to icon center connections\n            const centerX = nodeX + nodeWidth / 2;\n            const centerY = nodeY + nodeHeight / 2;\n            // All connection points are at the center of the circular icon for icon-to-icon connections\n            this.nodeConnectionPoints[node.id] = {\n              top: {\n                x: centerX,\n                y: centerY\n              },\n              right: {\n                x: centerX,\n                y: centerY\n              },\n              bottom: {\n                x: centerX,\n                y: centerY\n              },\n              left: {\n                x: centerX,\n                y: centerY\n              }\n            };\n            console.log(`Execute mode node ${node.id}: center(${centerX}, ${centerY})`);\n          } else {\n            // Build mode - account for node structure and padding\n            // The node has 10px padding and the icon is 40px with center at 30px from left edge\n            const iconCenterX = nodeX + 30; // 10px padding + 20px to icon center\n            const centerY = nodeY + nodeHeight / 2; // Vertical center\n            // Create connection points optimized for the node structure\n            this.nodeConnectionPoints[node.id] = {\n              top: {\n                x: iconCenterX,\n                y: nodeY\n              },\n              right: {\n                x: nodeX + nodeWidth,\n                y: centerY\n              },\n              bottom: {\n                x: iconCenterX,\n                y: nodeY + nodeHeight\n              },\n              left: {\n                x: nodeX,\n                y: centerY\n              }\n            };\n          }\n        }\n        // Trigger change detection immediately for consistent rendering\n        this.cdr.detectChanges();\n        this.updateConnectionPointsFrame = null;\n      });\n    }\n    // REMOVED getEdgePath getter to prevent infinite loops\n    // The template now uses edge.pathData directly\n    calculateEdgePath(sourceNode, targetNode) {\n      // Calculate connection points based on mode\n      let sourceX, sourceY, targetX, targetY;\n      if (this.isExecuteMode) {\n        // Execute mode - center to center\n        sourceX = sourceNode.position.x + 27.5; // Center of 55px node\n        sourceY = sourceNode.position.y + 27.5;\n        targetX = targetNode.position.x + 27.5;\n        targetY = targetNode.position.y + 27.5;\n      } else {\n        // Build mode - icon center to icon center for consistency\n        sourceX = sourceNode.position.x + 30; // Icon center (10px padding + 20px to center)\n        sourceY = sourceNode.position.y + 30; // Icon center\n        targetX = targetNode.position.x + 30;\n        targetY = targetNode.position.y + 30;\n      }\n      // Create simple, reliable connection path\n      return this.createConnectionPath({\n        x: sourceX,\n        y: sourceY\n      }, {\n        x: targetX,\n        y: targetY\n      });\n    }\n    // Pre-calculate all edge paths when nodes or edges change\n    updateEdgePaths() {\n      console.log('🔗 updateEdgePaths - Current nodes:', this.nodes.map(n => ({\n        id: n.id,\n        position: n.position\n      })));\n      console.log('🔗 updateEdgePaths - Current edges:', this.edges.map(e => ({\n        id: e.id,\n        source: e.source,\n        target: e.target\n      })));\n      this.edges = this.edges.map(edge => {\n        const sourceNode = this.nodes.find(n => n.id === edge.source);\n        const targetNode = this.nodes.find(n => n.id === edge.target);\n        if (sourceNode && targetNode) {\n          const pathData = this.calculateEdgePath(sourceNode, targetNode);\n          console.log(`🔗 SIMPLE: ${edge.id} -> ${pathData}`);\n          console.log(`🔗 SOURCE: ${sourceNode.id} at (${sourceNode.position.x}, ${sourceNode.position.y})`);\n          console.log(`🔗 TARGET: ${targetNode.id} at (${targetNode.position.x}, ${targetNode.position.y})`);\n          return {\n            ...edge,\n            pathData: pathData\n          };\n        } else {\n          // Fallback for missing nodes\n          console.log(`🔗 WARNING: Missing nodes for edge ${edge.id}, using empty path`);\n          return {\n            ...edge,\n            pathData: ''\n          };\n        }\n      });\n      console.log('🔗 updateEdgePaths completed - All edges now have pathData');\n      this.cdr.detectChanges();\n    }\n    findNearestConnectionPoints(sourcePoints, targetPoints) {\n      if (this.isExecuteMode) {\n        // Execute mode - icon center to icon center connections\n        // Since all connection points are at the center, use the center point directly\n        return {\n          sourcePoint: sourcePoints.top,\n          // All points are the same (center), so any will work\n          targetPoint: targetPoints.top // All points are the same (center), so any will work\n        };\n      } else {\n        // Build mode - find optimal connection points for any node placement\n        // Calculate all possible connection combinations\n        const sourcePointOptions = [{\n          point: sourcePoints.right,\n          name: 'right'\n        }, {\n          point: sourcePoints.bottom,\n          name: 'bottom'\n        }, {\n          point: sourcePoints.left,\n          name: 'left'\n        }, {\n          point: sourcePoints.top,\n          name: 'top'\n        }];\n        const targetPointOptions = [{\n          point: targetPoints.left,\n          name: 'left'\n        }, {\n          point: targetPoints.top,\n          name: 'top'\n        }, {\n          point: targetPoints.right,\n          name: 'right'\n        }, {\n          point: targetPoints.bottom,\n          name: 'bottom'\n        }];\n        let bestDistance = Infinity;\n        let bestSourcePoint = sourcePoints.right;\n        let bestTargetPoint = targetPoints.left;\n        // Find the shortest distance combination\n        for (const sourceOption of sourcePointOptions) {\n          for (const targetOption of targetPointOptions) {\n            const distance = Math.sqrt(Math.pow(targetOption.point.x - sourceOption.point.x, 2) + Math.pow(targetOption.point.y - sourceOption.point.y, 2));\n            if (distance < bestDistance) {\n              bestDistance = distance;\n              bestSourcePoint = sourceOption.point;\n              bestTargetPoint = targetOption.point;\n            }\n          }\n        }\n        return {\n          sourcePoint: bestSourcePoint,\n          targetPoint: bestTargetPoint\n        };\n      }\n    }\n    getConnectionDirection(sourcePoint, targetPoint) {\n      const dx = targetPoint.x - sourcePoint.x;\n      const dy = targetPoint.y - sourcePoint.y;\n      if (Math.abs(dx) > Math.abs(dy)) {\n        return dx > 0 ? 'right' : 'left';\n      } else {\n        return dy > 0 ? 'bottom' : 'top';\n      }\n    }\n    adjustTargetForArrow(targetPoint, direction, offset) {\n      switch (direction) {\n        case 'right':\n          return {\n            x: targetPoint.x - offset,\n            y: targetPoint.y\n          };\n        case 'left':\n          return {\n            x: targetPoint.x + offset,\n            y: targetPoint.y\n          };\n        case 'bottom':\n          return {\n            x: targetPoint.x,\n            y: targetPoint.y - offset\n          };\n        case 'top':\n          return {\n            x: targetPoint.x,\n            y: targetPoint.y + offset\n          };\n        default:\n          return targetPoint;\n      }\n    }\n    createConnectionPath(sourcePoint, targetPoint) {\n      // SIMPLIFIED: Create straight lines for consistent vertical stacking\n      const sourceX = sourcePoint.x;\n      const sourceY = sourcePoint.y;\n      const targetX = targetPoint.x;\n      const targetY = targetPoint.y;\n      if (this.isExecuteMode) {\n        // Execute mode - direct line between centers with arrow spacing\n        const dx = targetX - sourceX;\n        const dy = targetY - sourceY;\n        const length = Math.sqrt(dx * dx + dy * dy);\n        if (length < 20) {\n          return `M ${sourceX} ${sourceY} L ${targetX} ${targetY}`;\n        }\n        const unitX = dx / length;\n        const unitY = dy / length;\n        const sourceRadius = 27;\n        const targetRadius = 20;\n        const arrowSpace = 8;\n        const adjustedSourceX = sourceX + unitX * sourceRadius;\n        const adjustedSourceY = sourceY + unitY * sourceRadius;\n        const adjustedTargetX = targetX - unitX * (targetRadius + arrowSpace);\n        const adjustedTargetY = targetY - unitY * (targetRadius + arrowSpace);\n        return `M ${adjustedSourceX} ${adjustedSourceY} L ${adjustedTargetX} ${adjustedTargetY}`;\n      } else {\n        // Build mode - ALWAYS STRAIGHT LINES regardless of node positioning\n        const dx = targetX - sourceX;\n        const dy = targetY - sourceY;\n        const distance = Math.sqrt(dx * dx + dy * dy);\n        if (distance < 20) {\n          // Very close nodes - direct line\n          return `M ${sourceX} ${sourceY} L ${targetX} ${targetY}`;\n        }\n        // Calculate arrow spacing to ensure arrows are visible\n        const unitX = dx / distance;\n        const unitY = dy / distance;\n        // Adjust source to start from node edge\n        const sourceRadius = 30; // Start from edge of source node\n        const adjustedSourceX = sourceX + unitX * sourceRadius;\n        const adjustedSourceY = sourceY + unitY * sourceRadius;\n        // Adjust target to end before the node (leave space for arrow)\n        const targetNodeRadius = 30; // Target node radius\n        const arrowLength = 15; // Space for the arrow itself\n        const adjustedTargetX = targetX - unitX * (targetNodeRadius + arrowLength);\n        const adjustedTargetY = targetY - unitY * (targetNodeRadius + arrowLength);\n        // ALWAYS use straight lines - no curves\n        return `M ${adjustedSourceX} ${adjustedSourceY} L ${adjustedTargetX} ${adjustedTargetY}`;\n      }\n    }\n    getTempConnectionPath() {\n      if (!this.tempConnection.isActive || this.tempConnection.sourceX === undefined || this.tempConnection.sourceY === undefined || this.tempConnection.targetX === undefined || this.tempConnection.targetY === undefined) {\n        return '';\n      }\n      const sourceX = this.tempConnection.sourceX;\n      const sourceY = this.tempConnection.sourceY;\n      const targetX = this.tempConnection.targetX;\n      const targetY = this.tempConnection.targetY;\n      // Create beautiful Bézier curves exactly like workflow editor\n      const dx = targetX - sourceX;\n      const dy = targetY - sourceY;\n      const distance = Math.sqrt(dx * dx + dy * dy);\n      // Use the exact same offset calculation as your workflow editor\n      const baseOffset = Math.min(100, distance * 0.5);\n      const offset = Math.max(50, baseOffset);\n      // Create horizontal Bézier curve with proper control points\n      const controlPointX1 = sourceX + offset;\n      const controlPointX2 = targetX - offset;\n      return `M ${sourceX} ${sourceY} C ${controlPointX1} ${sourceY}, ${controlPointX2} ${targetY}, ${targetX} ${targetY}`;\n    }\n    // Toolbar actions\n    onUndo() {\n      if (this.historyIndex > 0) {\n        this.historyIndex--;\n        const state = this.history[this.historyIndex];\n        this.restoreState(state);\n      }\n      this.undoAction.emit();\n    }\n    onRedo() {\n      if (this.historyIndex < this.history.length - 1) {\n        this.historyIndex++;\n        const state = this.history[this.historyIndex];\n        this.restoreState(state);\n      }\n      this.redoAction.emit();\n    }\n    onReset() {\n      // Save current state before reset\n      this.saveToHistory();\n      // Clear all nodes and edges\n      this.nodes = [];\n      this.edges = [];\n      this.selectedNodeId = null;\n      this.tempConnection = {\n        isActive: false\n      };\n      // Reset viewport\n      this.resetViewport();\n      // Emit state change to parent component\n      this.stateChanged.emit({\n        nodes: [],\n        edges: []\n      });\n      // Emit events\n      this.nodeRemoved.emit('all'); // Special case for clearing all\n      this.resetAction.emit();\n    }\n    onPrimaryButtonClick() {\n      this.primaryButtonClicked.emit();\n    }\n    // History management methods\n    saveToHistory() {\n      // Don't save history during state restoration\n      if (this.isRestoringState) return;\n      // Remove any history after current index (when we're not at the end)\n      this.history = this.history.slice(0, this.historyIndex + 1);\n      // Add current state to history\n      const currentState = {\n        nodes: JSON.parse(JSON.stringify(this.nodes)),\n        edges: JSON.parse(JSON.stringify(this.edges))\n      };\n      this.history.push(currentState);\n      this.historyIndex = this.history.length - 1;\n      // Limit history size\n      if (this.history.length > this.maxHistorySize) {\n        this.history.shift();\n        this.historyIndex--;\n      }\n    }\n    restoreState(state) {\n      this.isRestoringState = true;\n      // Update the internal state\n      this.nodes = [...state.nodes];\n      this.edges = [...state.edges];\n      // Clear selection and temp connections\n      this.selectedNodeId = null;\n      this.tempConnection = {\n        isActive: false\n      };\n      // Update connection points\n      this.updateNodeConnectionPoints();\n      // Emit state change to parent component\n      this.stateChanged.emit({\n        nodes: [...this.nodes],\n        edges: [...this.edges]\n      });\n      // Reset flag after a short delay\n      setTimeout(() => {\n        this.isRestoringState = false;\n      }, 100);\n    }\n    // Save history before actions\n    saveHistoryBeforeAction() {\n      this.saveToHistory();\n    }\n    // Public methods for external components to add/remove nodes and edges\n    addNode(node) {\n      this.saveHistoryBeforeAction();\n      this.nodes.push(node);\n      this.updateNodeConnectionPoints();\n      this.nodeAdded.emit(node);\n    }\n    addEdge(edge) {\n      this.saveHistoryBeforeAction();\n      this.edges.push(edge);\n    }\n    removeNode(nodeId) {\n      this.onDeleteNode(nodeId);\n    }\n    removeEdge(edgeId) {\n      this.saveHistoryBeforeAction();\n      this.edges = this.edges.filter(edge => edge.id !== edgeId);\n    }\n    // Canvas tool methods\n    toggleGrid() {\n      this.showGridDots = !this.showGridDots;\n      this.showGrid = this.showGridDots;\n    }\n    setPanMode() {\n      this.canvasMode = 'pan';\n      this.mouseInteractionsEnabled = true;\n      if (this.canvasContainer) {\n        this.canvasContainer.nativeElement.style.cursor = 'grab';\n      }\n    }\n    setSelectMode() {\n      this.canvasMode = 'select';\n      this.mouseInteractionsEnabled = true;\n      if (this.canvasContainer) {\n        this.canvasContainer.nativeElement.style.cursor = 'default';\n      }\n    }\n    setZoomMode() {\n      this.canvasMode = 'zoom';\n      this.mouseInteractionsEnabled = true;\n      if (this.canvasContainer) {\n        this.canvasContainer.nativeElement.style.cursor = 'zoom-in';\n      }\n    }\n    disableMouseInteractions() {\n      this.canvasMode = 'disabled';\n      this.mouseInteractionsEnabled = false;\n      if (this.canvasContainer) {\n        this.canvasContainer.nativeElement.style.cursor = 'not-allowed';\n      }\n    }\n    zoomIn() {\n      const newZoom = Math.min(this.maxZoom, this.viewport.zoom * 1.2);\n      this.setZoom(newZoom);\n    }\n    zoomOut() {\n      const newZoom = Math.max(this.minZoom, this.viewport.zoom / 1.2);\n      this.setZoom(newZoom);\n    }\n    setZoom(newZoom) {\n      if (newZoom !== this.viewport.zoom) {\n        // Get canvas center for zoom\n        const rect = this.canvasContainer.nativeElement.getBoundingClientRect();\n        const centerX = rect.width / 2;\n        const centerY = rect.height / 2;\n        // Calculate new position to zoom towards center\n        const zoomRatio = newZoom / this.viewport.zoom;\n        const newX = centerX - (centerX - this.viewport.x) * zoomRatio;\n        const newY = centerY - (centerY - this.viewport.y) * zoomRatio;\n        // Update viewport\n        this.viewport.zoom = newZoom;\n        this.viewport.x = newX;\n        this.viewport.y = newY;\n        // Update connection points\n        this.updateNodeConnectionPoints();\n        // Emit viewport change\n        this.viewportChanged.emit(this.viewport);\n      }\n    }\n    // Built-in input field methods\n    onAgentNameChange(value) {\n      this.agentNameControl.setValue(value);\n      this.agentNameChanged.emit(value);\n    }\n    onAgentTypeChange(value) {\n      const finalValue = Array.isArray(value) ? value[0] : value;\n      this.agentTypeControl.setValue(finalValue);\n      this.agentTypeChanged.emit(finalValue);\n    }\n    // Built-in metadata dropdown methods\n    toggleMetadataDropdown() {\n      this.isMetadataDropdownOpen = !this.isMetadataDropdownOpen;\n    }\n    closeMetadataDropdown() {\n      this.isMetadataDropdownOpen = false;\n    }\n    onDropdownSelect(selectedValue, level, currentType, nextType) {\n      const value = Array.isArray(selectedValue) ? selectedValue[0] : selectedValue;\n      if (!value) return;\n      // Update the current dropdown value\n      switch (currentType) {\n        case 'org':\n          this.orgControl.setValue(value);\n          // Reset dependent dropdowns\n          this.domainControl.setValue('');\n          this.projectControl.setValue('');\n          this.teamControl.setValue('');\n          // Load domain options\n          this.loadDomainOptions(value);\n          break;\n        case 'domain':\n          this.domainControl.setValue(value);\n          // Reset dependent dropdowns\n          this.projectControl.setValue('');\n          this.teamControl.setValue('');\n          // Load project options\n          this.loadProjectOptions(this.orgControl.value || '', value);\n          break;\n        case 'project':\n          this.projectControl.setValue(value);\n          // Reset dependent dropdown\n          this.teamControl.setValue('');\n          // Load team options\n          this.loadTeamOptions(this.orgControl.value || '', this.domainControl.value || '', value);\n          break;\n        case 'team':\n          this.teamControl.setValue(value);\n          break;\n      }\n    }\n    applyMetadata() {\n      const hasValues = this.orgControl.value || this.domainControl.value || this.projectControl.value || this.teamControl.value;\n      if (hasValues) {\n        this.metadataStatus = this.inputFieldsConfig.metadata?.statusText?.saved || 'Metadata Information saved';\n      } else {\n        this.metadataStatus = this.inputFieldsConfig.metadata?.statusText?.notSaved || 'Metadata Information not saved';\n      }\n      // Emit metadata change event\n      this.metadataChanged.emit({\n        org: this.orgControl.value || '',\n        domain: this.domainControl.value || '',\n        project: this.projectControl.value || '',\n        team: this.teamControl.value || ''\n      });\n      this.closeMetadataDropdown();\n    }\n    cancelMetadata() {\n      this.closeMetadataDropdown();\n    }\n    // Load dropdown options (same API pattern as nav-item)\n    loadDomainOptions(org) {\n      const domainOptionsMap = {\n        ascendion: [{\n          value: 'engineering',\n          label: 'Engineering'\n        }, {\n          value: 'marketing',\n          label: 'Marketing'\n        }, {\n          value: 'sales',\n          label: 'Sales'\n        }],\n        company2: [{\n          value: 'tech',\n          label: 'Technology'\n        }, {\n          value: 'operations',\n          label: 'Operations'\n        }],\n        company3: [{\n          value: 'research',\n          label: 'Research'\n        }, {\n          value: 'development',\n          label: 'Development'\n        }]\n      };\n      this.dropdownValues['domain'] = domainOptionsMap[org] || [];\n    }\n    loadProjectOptions(org, domain) {\n      const projectOptions = [{\n        value: 'project1',\n        label: 'Project Alpha'\n      }, {\n        value: 'project2',\n        label: 'Project Beta'\n      }, {\n        value: 'project3',\n        label: 'Project Gamma'\n      }];\n      this.dropdownValues['project'] = projectOptions;\n    }\n    loadTeamOptions(org, domain, project) {\n      const teamOptions = [{\n        value: 'team1',\n        label: 'Team Alpha'\n      }, {\n        value: 'team2',\n        label: 'Team Beta'\n      }, {\n        value: 'team3',\n        label: 'Team Gamma'\n      }];\n      this.dropdownValues['team'] = teamOptions;\n    }\n    // Toolbar is now fixed at bottom center - no drag methods needed\n    // Toolbar is now fixed at bottom center via CSS - no positioning method needed\n    // Agent details dropdown methods\n    toggleAgentDetailsDropdown() {\n      this.isAgentDetailsDropdownOpen = !this.isAgentDetailsDropdownOpen;\n    }\n    closeAgentDetailsDropdown() {\n      this.isAgentDetailsDropdownOpen = false;\n    }\n    applyAgentDetails() {\n      const name = this.agentDetailNameControl.value || '';\n      const useCaseDetails = this.agentDetailControl.value || '';\n      // Emit agent details change event\n      this.agentDetailsChanged.emit({\n        name: name,\n        useCaseDetails: useCaseDetails\n      });\n      this.closeAgentDetailsDropdown();\n    }\n    cancelAgentDetails() {\n      this.closeAgentDetailsDropdown();\n    }\n    static ɵfac = function CanvasBoardComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CanvasBoardComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CanvasBoardComponent,\n      selectors: [[\"app-canvas-board\"]],\n      contentQueries: function CanvasBoardComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, _c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.nodeTemplate = _t.first);\n        }\n      },\n      viewQuery: function CanvasBoardComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.canvasContainer = _t.first);\n        }\n      },\n      inputs: {\n        nodes: \"nodes\",\n        edges: \"edges\",\n        showGrid: \"showGrid\",\n        enablePan: \"enablePan\",\n        enableZoom: \"enableZoom\",\n        enableConnections: \"enableConnections\",\n        minZoom: \"minZoom\",\n        maxZoom: \"maxZoom\",\n        connectionColor: \"connectionColor\",\n        fallbackMessage: \"fallbackMessage\",\n        navigationHints: \"navigationHints\",\n        showToolbar: \"showToolbar\",\n        primaryButtonText: \"primaryButtonText\",\n        primaryButtonIcon: \"primaryButtonIcon\",\n        enableUndo: \"enableUndo\",\n        enableRedo: \"enableRedo\",\n        enableReset: \"enableReset\",\n        showCanvasTools: \"showCanvasTools\",\n        enableGridToggle: \"enableGridToggle\",\n        enablePanMode: \"enablePanMode\",\n        enableSelectionMode: \"enableSelectionMode\",\n        enableZoomControls: \"enableZoomControls\",\n        isExecuteMode: \"isExecuteMode\",\n        showLeftActions: \"showLeftActions\",\n        mouseInteractionsEnabled: \"mouseInteractionsEnabled\",\n        showHeaderInputs: \"showHeaderInputs\",\n        inputFieldsConfig: \"inputFieldsConfig\",\n        initialAgentName: \"initialAgentName\",\n        initialAgentDetails: \"initialAgentDetails\",\n        initialMetadata: \"initialMetadata\",\n        agentNameControl: \"agentNameControl\",\n        agentDetailNameControl: \"agentDetailNameControl\",\n        agentDetailControl: \"agentDetailControl\"\n      },\n      outputs: {\n        nodeAdded: \"nodeAdded\",\n        nodeRemoved: \"nodeRemoved\",\n        nodeMoved: \"nodeMoved\",\n        nodeSelected: \"nodeSelected\",\n        nodeDoubleClicked: \"nodeDoubleClicked\",\n        connectionStarted: \"connectionStarted\",\n        connectionCreated: \"connectionCreated\",\n        canvasDropped: \"canvasDropped\",\n        viewportChanged: \"viewportChanged\",\n        undoAction: \"undoAction\",\n        redoAction: \"redoAction\",\n        resetAction: \"resetAction\",\n        primaryButtonClicked: \"primaryButtonClicked\",\n        stateChanged: \"stateChanged\",\n        agentNameChanged: \"agentNameChanged\",\n        agentTypeChanged: \"agentTypeChanged\",\n        metadataChanged: \"metadataChanged\",\n        agentDetailsChanged: \"agentDetailsChanged\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      ngContentSelectors: _c3,\n      decls: 16,\n      vars: 18,\n      consts: [[\"canvasContainer\", \"\"], [1, \"canvas-board-container\"], [\"class\", \"header-inputs-section\", 4, \"ngIf\"], [\"class\", \"navigation-hint\", 4, \"ngIf\"], [1, \"canvas-container\", 3, \"dragover\", \"drop\", \"mouseup\", \"mousemove\", \"wheel\", \"mousedown\"], [\"class\", \"canvas-tools-toolbar\", 4, \"ngIf\"], [\"class\", \"floating-toolbar\", 4, \"ngIf\"], [\"class\", \"connections-layer canvas-edges execute-connections\", 3, \"position\", \"top\", \"left\", \"width\", \"height\", \"pointer-events\", \"z-index\", 4, \"ngIf\"], [1, \"canvas-viewport\"], [1, \"canvas-content\"], [\"class\", \"connections-layer canvas-edges\", 4, \"ngIf\"], [1, \"nodes-container\", \"canvas-nodes\"], [4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"temp-connection-layer\", 4, \"ngIf\"], [\"class\", \"no-nodes-message\", 4, \"ngIf\"], [1, \"header-inputs-section\"], [1, \"header-inputs-container\"], [\"class\", \"input-group agent-details-dropdown\", 4, \"ngIf\"], [1, \"input-group\", \"agent-details-dropdown\"], [1, \"dropdown-container\"], [\"type\", \"button\", 1, \"dropdown-toggle\", 3, \"click\"], [\"iconName\", \"ChevronDown\", 3, \"iconSize\"], [\"class\", \"dropdown-content\", 4, \"ngIf\"], [\"class\", \"canvas-action\", 4, \"ngIf\"], [1, \"dropdown-content\"], [1, \"form-fields\"], [1, \"field-group\"], [1, \"field-label\"], [\"variant\", \"primary\", \"size\", \"md\", 1, \"agent-detail-name-field\", 3, \"placeholder\", \"formControl\", \"fullWidth\"], [1, \"agent-detail-field\", 3, \"placeholder\", \"formControl\", \"rows\", \"fullWidth\"], [1, \"dropdown-actions\"], [\"label\", \"Cancel\", \"variant\", \"secondary\", \"size\", \"small\", 3, \"userClick\", \"customStyles\"], [\"label\", \"Apply\", \"variant\", \"primary\", \"size\", \"small\", 3, \"userClick\", \"customStyles\"], [1, \"canvas-action\"], [1, \"navigation-hint\"], [4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [1, \"canvas-tools-toolbar\"], [\"label\", \"\", \"variant\", \"secondary\", \"size\", \"small\", \"iconName\", \"MousePointer2\", \"iconPosition\", \"only\", \"title\", \"Selection Mode\", 3, \"userClick\", \"state\"], [\"label\", \"\", \"variant\", \"secondary\", \"size\", \"small\", \"iconName\", \"Hand\", \"iconPosition\", \"only\", \"title\", \"Pan Mode\", 3, \"userClick\", \"iconColor\", \"state\"], [\"label\", \"\", \"variant\", \"secondary\", \"size\", \"small\", \"iconName\", \"ZoomIn\", \"iconPosition\", \"only\", \"title\", \"Zoom In\", 3, \"userClick\"], [\"label\", \"\", \"variant\", \"secondary\", \"size\", \"small\", \"iconName\", \"ZoomOut\", \"iconPosition\", \"only\", \"title\", \"Zoom Out\", 3, \"userClick\"], [1, \"floating-toolbar\"], [1, \"black-icons\"], [\"label\", \"\", \"variant\", \"secondary\", \"size\", \"small\", \"iconName\", \"Undo-2\", \"iconPosition\", \"only\", \"title\", \"Undo\", 3, \"userClick\", 4, \"ngIf\"], [\"label\", \"\", \"variant\", \"secondary\", \"size\", \"small\", \"iconName\", \"Redo-2\", \"iconPosition\", \"only\", \"title\", \"Redo\", 3, \"userClick\", 4, \"ngIf\"], [\"label\", \"\", \"variant\", \"primary\", \"size\", \"small\", \"iconName\", \"Play\", \"iconPosition\", \"only\", \"title\", \"Run Agent\", 3, \"customStyles\", \"userClick\", 4, \"ngIf\"], [\"variant\", \"primary\", \"size\", \"small\", \"iconPosition\", \"right\", 3, \"label\", \"customStyles\", \"iconName\", \"userClick\", 4, \"ngIf\"], [\"label\", \"\", \"variant\", \"secondary\", \"size\", \"small\", \"iconName\", \"Undo-2\", \"iconPosition\", \"only\", \"title\", \"Undo\", 3, \"userClick\"], [\"label\", \"\", \"variant\", \"secondary\", \"size\", \"small\", \"iconName\", \"Redo-2\", \"iconPosition\", \"only\", \"title\", \"Redo\", 3, \"userClick\"], [\"label\", \"\", \"variant\", \"primary\", \"size\", \"small\", \"iconName\", \"Play\", \"iconPosition\", \"only\", \"title\", \"Run Agent\", 3, \"userClick\", \"customStyles\"], [\"variant\", \"primary\", \"size\", \"small\", \"iconPosition\", \"right\", 3, \"userClick\", \"label\", \"customStyles\", \"iconName\"], [1, \"connections-layer\", \"canvas-edges\", \"execute-connections\"], [\"class\", \"edge-path\", \"marker-end\", \"url(#arrow-execute)\", 3, \"edge-animated\", 4, \"ngFor\", \"ngForOf\"], [\"id\", \"arrow-execute\", \"viewBox\", \"0 0 8 8\", \"refX\", \"7\", \"refY\", \"4\", \"markerWidth\", \"6\", \"markerHeight\", \"6\", \"orient\", \"auto\", \"markerUnits\", \"strokeWidth\", 1, \"edge-marker\"], [\"d\", \"M 1 1 L 7 4 L 1 7 z\", 1, \"arrow-head\"], [\"marker-end\", \"url(#arrow-execute)\", 1, \"edge-path\"], [1, \"connections-layer\", \"canvas-edges\"], [\"class\", \"edge-path\", \"marker-end\", \"url(#arrow)\", 3, \"edge-animated\", 4, \"ngFor\", \"ngForOf\"], [\"id\", \"arrow\", \"viewBox\", \"0 0 12 12\", \"refX\", \"10\", \"refY\", \"6\", \"markerWidth\", \"10\", \"markerHeight\", \"10\", \"orient\", \"auto\", \"markerUnits\", \"strokeWidth\", 1, \"edge-marker\"], [\"d\", \"M 2 2 L 10 6 L 2 10 z\", 1, \"arrow-head\"], [\"marker-end\", \"url(#arrow)\", 1, \"edge-path\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"temp-connection-layer\"], [\"marker-end\", \"url(#arrow)\", 1, \"temp-connection-path\"], [1, \"no-nodes-message\"]],\n      template: function CanvasBoardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef(_c2);\n          i0.ɵɵelementStart(0, \"div\", 1);\n          i0.ɵɵtemplate(1, CanvasBoardComponent_div_1_Template, 3, 1, \"div\", 2)(2, CanvasBoardComponent_div_2_Template, 2, 1, \"div\", 3);\n          i0.ɵɵelementStart(3, \"div\", 4, 0);\n          i0.ɵɵlistener(\"dragover\", function CanvasBoardComponent_Template_div_dragover_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onDragOver($event));\n          })(\"drop\", function CanvasBoardComponent_Template_div_drop_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onDrop($event));\n          })(\"mouseup\", function CanvasBoardComponent_Template_div_mouseup_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onCanvasMouseUp($event));\n          })(\"mousemove\", function CanvasBoardComponent_Template_div_mousemove_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onCanvasMouseMove($event));\n          })(\"wheel\", function CanvasBoardComponent_Template_div_wheel_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onCanvasWheel($event));\n          })(\"mousedown\", function CanvasBoardComponent_Template_div_mousedown_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onCanvasMouseDown($event));\n          });\n          i0.ɵɵprojection(5);\n          i0.ɵɵtemplate(6, CanvasBoardComponent_div_6_Template, 5, 3, \"div\", 5)(7, CanvasBoardComponent_div_7_Template, 6, 4, \"div\", 6)(8, CanvasBoardComponent__svg_svg_8_Template, 5, 15, \"svg\", 7);\n          i0.ɵɵelementStart(9, \"div\", 8)(10, \"div\", 9);\n          i0.ɵɵtemplate(11, CanvasBoardComponent__svg_svg_11_Template, 5, 1, \"svg\", 10);\n          i0.ɵɵelementStart(12, \"div\", 11);\n          i0.ɵɵtemplate(13, CanvasBoardComponent_ng_container_13_Template, 2, 12, \"ng-container\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(14, CanvasBoardComponent__svg_svg_14_Template, 2, 1, \"svg\", 13)(15, CanvasBoardComponent_div_15_Template, 2, 1, \"div\", 14);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"execute-mode\", ctx.isExecuteMode);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showHeaderInputs);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.navigationHints.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"show-grid\", ctx.showGrid)(\"disabled\", !ctx.mouseInteractionsEnabled);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.showCanvasTools);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showToolbar);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.enableConnections && ctx.isExecuteMode);\n          i0.ɵɵadvance();\n          i0.ɵɵstyleProp(\"transform\", ctx.isExecuteMode ? \"none\" : \"translate(\" + ctx.viewport.x + \"px, \" + ctx.viewport.y + \"px) scale(\" + ctx.viewport.zoom + \")\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.enableConnections && !ctx.isExecuteMode);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.nodes)(\"ngForTrackBy\", ctx.trackByNodeId);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.tempConnection.isActive && ctx.enableConnections);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.nodes.length === 0);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, ReactiveFormsModule, i2.NgControlStatus, i2.FormControlDirective, ButtonComponent, AvaTextboxComponent, AvaTextareaComponent, IconComponent],\n      styles: [\".canvas-board-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  width: 100%;\\n  background-color: #f8f9fa;\\n  background-image: radial-gradient(circle, #d1d5db 1px, transparent 1px);\\n  background-size: 20px 20px;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  border: 1px solid #d1d3d8;\\n}\\n\\n.header-inputs-section[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 20px;\\n  left: 20px;\\n  z-index: 10;\\n  max-width: calc(100% - 40px);\\n  overflow: visible;\\n}\\n.header-inputs-section[_ngcontent-%COMP%]   .header-inputs-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  flex-wrap: nowrap;\\n}\\n.header-inputs-section[_ngcontent-%COMP%]   .header-inputs-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 8px;\\n  position: relative;\\n}\\n.header-inputs-section[_ngcontent-%COMP%]   .header-inputs-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .edit-icon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 12px;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  color: #9ca3af;\\n  pointer-events: none;\\n  z-index: 10;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 16px;\\n  height: 16px;\\n  transition: color 0.2s ease;\\n}\\n.header-inputs-section[_ngcontent-%COMP%]   .header-inputs-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .edit-icon[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n}\\n.header-inputs-section[_ngcontent-%COMP%]   .header-inputs-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .edit-icon[_ngcontent-%COMP%]:hover {\\n  color: #6b7280;\\n}\\n.header-inputs-section[_ngcontent-%COMP%]   .agent-details-dropdown[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.header-inputs-section[_ngcontent-%COMP%]   .agent-details-dropdown[_ngcontent-%COMP%]   .dropdown-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  min-width: 200px;\\n  width: 300px;\\n}\\n.header-inputs-section[_ngcontent-%COMP%]   .agent-details-dropdown[_ngcontent-%COMP%]   .dropdown-container[_ngcontent-%COMP%]   .dropdown-toggle[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  width: 100%;\\n  padding: 8px 12px;\\n  background: white;\\n  border: 1px solid #d1d5db;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  color: #374151;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.header-inputs-section[_ngcontent-%COMP%]   .agent-details-dropdown[_ngcontent-%COMP%]   .dropdown-container[_ngcontent-%COMP%]   .dropdown-toggle[_ngcontent-%COMP%]:hover {\\n  border-color: #9ca3af;\\n}\\n.header-inputs-section[_ngcontent-%COMP%]   .agent-details-dropdown[_ngcontent-%COMP%]   .dropdown-container[_ngcontent-%COMP%]   .dropdown-toggle.open[_ngcontent-%COMP%] {\\n  border-color: #3b82f6;\\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\\n}\\n.header-inputs-section[_ngcontent-%COMP%]   .agent-details-dropdown[_ngcontent-%COMP%]   .dropdown-container[_ngcontent-%COMP%]   .dropdown-toggle[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n}\\n.header-inputs-section[_ngcontent-%COMP%]   .agent-details-dropdown[_ngcontent-%COMP%]   .dropdown-container[_ngcontent-%COMP%]   .dropdown-toggle[_ngcontent-%COMP%]   ava-icon[_ngcontent-%COMP%] {\\n  transition: transform 0.2s ease;\\n}\\n.header-inputs-section[_ngcontent-%COMP%]   .agent-details-dropdown[_ngcontent-%COMP%]   .dropdown-container[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 0;\\n  right: 0;\\n  background: white;\\n  border: 1px solid #d1d5db;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\\n  z-index: 1000;\\n  margin-top: 16px;\\n  padding: 16px;\\n}\\n.header-inputs-section[_ngcontent-%COMP%]   .agent-details-dropdown[_ngcontent-%COMP%]   .dropdown-container[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .form-fields[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n.header-inputs-section[_ngcontent-%COMP%]   .agent-details-dropdown[_ngcontent-%COMP%]   .dropdown-container[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .form-fields[_ngcontent-%COMP%]   .field-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 6px;\\n}\\n.header-inputs-section[_ngcontent-%COMP%]   .agent-details-dropdown[_ngcontent-%COMP%]   .dropdown-container[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .form-fields[_ngcontent-%COMP%]   .field-group[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #374151;\\n}\\n.header-inputs-section[_ngcontent-%COMP%]   .agent-details-dropdown[_ngcontent-%COMP%]   .dropdown-container[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .dropdown-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  gap: 8px;\\n  margin-top: 16px;\\n  padding-top: 16px;\\n  border-top: 1px solid #e5e7eb;\\n}\\n.header-inputs-section[_ngcontent-%COMP%]   .agent-details-dropdown[_ngcontent-%COMP%]   .dropdown-container[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .dropdown-actions[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%], \\n.header-inputs-section[_ngcontent-%COMP%]   .agent-details-dropdown[_ngcontent-%COMP%]   .dropdown-container[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .dropdown-actions[_ngcontent-%COMP%]   .apply-btn[_ngcontent-%COMP%] {\\n  padding: 8px 16px;\\n  border-radius: 6px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.header-inputs-section[_ngcontent-%COMP%]   .agent-details-dropdown[_ngcontent-%COMP%]   .dropdown-container[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .dropdown-actions[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #d1d5db;\\n  color: #374151;\\n}\\n.header-inputs-section[_ngcontent-%COMP%]   .agent-details-dropdown[_ngcontent-%COMP%]   .dropdown-container[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .dropdown-actions[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]:hover {\\n  background: #f9fafb;\\n  border-color: #9ca3af;\\n}\\n.header-inputs-section[_ngcontent-%COMP%]   .agent-details-dropdown[_ngcontent-%COMP%]   .dropdown-container[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .dropdown-actions[_ngcontent-%COMP%]   .apply-btn[_ngcontent-%COMP%] {\\n  background: #3b82f6;\\n  border: 1px solid #3b82f6;\\n  color: white;\\n}\\n.header-inputs-section[_ngcontent-%COMP%]   .agent-details-dropdown[_ngcontent-%COMP%]   .dropdown-container[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .dropdown-actions[_ngcontent-%COMP%]   .apply-btn[_ngcontent-%COMP%]:hover {\\n  background: #2563eb;\\n  border-color: #2563eb;\\n}\\n\\n.input-fields-section[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  background-color: var(--dashboard-bg-lighter);\\n  border-bottom: 1px solid var(--form-input-border);\\n}\\n.input-fields-section[_ngcontent-%COMP%]   .input-fields-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 16px;\\n}\\n.input-fields-section[_ngcontent-%COMP%]   .input-fields-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  min-width: 200px;\\n  flex: 1;\\n}\\n.input-fields-section[_ngcontent-%COMP%]   .input-fields-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: var(--text-primary);\\n  margin-bottom: 6px;\\n}\\n.input-fields-section[_ngcontent-%COMP%]   .input-fields-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%]   .required-asterisk[_ngcontent-%COMP%] {\\n  color: var(--error-color);\\n  margin-left: 2px;\\n}\\n.input-fields-section[_ngcontent-%COMP%]   .input-fields-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .field-input[_ngcontent-%COMP%], \\n.input-fields-section[_ngcontent-%COMP%]   .input-fields-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .field-textarea[_ngcontent-%COMP%] {\\n  padding: 8px 12px;\\n  border: 1px solid var(--form-input-border);\\n  border-radius: 6px;\\n  font-size: 14px;\\n  color: var(--text-primary);\\n  background-color: var(--form-input-bg);\\n  transition: border-color 0.15s ease, box-shadow 0.15s ease;\\n}\\n.input-fields-section[_ngcontent-%COMP%]   .input-fields-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .field-input[_ngcontent-%COMP%]:focus, \\n.input-fields-section[_ngcontent-%COMP%]   .input-fields-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .field-textarea[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: var(--dashboard-primary);\\n  box-shadow: 0 0 0 2px rgba(var(--dashboard-primary-rgb), 0.1);\\n}\\n.input-fields-section[_ngcontent-%COMP%]   .input-fields-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .field-input[_ngcontent-%COMP%]:disabled, \\n.input-fields-section[_ngcontent-%COMP%]   .input-fields-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .field-textarea[_ngcontent-%COMP%]:disabled {\\n  background-color: var(--form-input-disabled-bg);\\n  color: var(--text-disabled);\\n  cursor: not-allowed;\\n}\\n.input-fields-section[_ngcontent-%COMP%]   .input-fields-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .field-input[_ngcontent-%COMP%]::placeholder, \\n.input-fields-section[_ngcontent-%COMP%]   .input-fields-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .field-textarea[_ngcontent-%COMP%]::placeholder {\\n  color: var(--text-placeholder);\\n}\\n.input-fields-section[_ngcontent-%COMP%]   .input-fields-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .field-textarea[_ngcontent-%COMP%] {\\n  resize: vertical;\\n  min-height: 80px;\\n}\\n\\n.metadata-section[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  background-color: #f9fafb;\\n  border-bottom: 1px solid #d1d5db;\\n}\\n.metadata-section[_ngcontent-%COMP%]   .metadata-container[_ngcontent-%COMP%]   .metadata-title[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #374151;\\n  margin-bottom: 16px;\\n}\\n.metadata-section[_ngcontent-%COMP%]   .metadata-container[_ngcontent-%COMP%]   .dropdown-row-vertical[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n}\\n@media (min-width: 768px) {\\n  .metadata-section[_ngcontent-%COMP%]   .metadata-container[_ngcontent-%COMP%]   .dropdown-row-vertical[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    flex-wrap: wrap;\\n  }\\n  .metadata-section[_ngcontent-%COMP%]   .metadata-container[_ngcontent-%COMP%]   .dropdown-row-vertical[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%] {\\n    flex: 1;\\n    min-width: 200px;\\n  }\\n}\\n\\n.agent-name-input[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 180px;\\n}\\n\\n.agent-type-tag[_ngcontent-%COMP%]   .type-tag[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 4px 10px;\\n  background-color: #e9effd;\\n  color: #215ad6;\\n  font-size: 12px;\\n  font-weight: 500;\\n  border-radius: 16px;\\n  border: 1px solid #6c96f2;\\n  white-space: nowrap;\\n  height: 24px;\\n}\\n\\n.canvas-board-container.execute-mode[_ngcontent-%COMP%]   .agent-name-input[_ngcontent-%COMP%] {\\n  width: 180px !important;\\n}\\n.canvas-board-container.execute-mode[_ngcontent-%COMP%]   .agent-name-input[_ngcontent-%COMP%]     .ava-textbox {\\n  width: 180px !important;\\n}\\n.canvas-board-container.execute-mode[_ngcontent-%COMP%]   .floating-toolbar[_ngcontent-%COMP%] {\\n  gap: 6px !important;\\n  top: 20px !important;\\n  right: 20px !important;\\n}\\n.canvas-board-container.execute-mode[_ngcontent-%COMP%]   .floating-toolbar[_ngcontent-%COMP%]   .primary-btn[_ngcontent-%COMP%] {\\n  min-width: 36px !important;\\n  height: 36px !important;\\n  padding: 8px !important;\\n}\\n.canvas-board-container.execute-mode[_ngcontent-%COMP%]   .agent-details-dropdown[_ngcontent-%COMP%]   .dropdown-container[_ngcontent-%COMP%] {\\n  width: 76% !important;\\n  min-width: 200px !important;\\n}\\n.canvas-board-container.execute-mode[_ngcontent-%COMP%]   .header-inputs-section[_ngcontent-%COMP%] {\\n  top: 20px !important;\\n  left: 20px !important;\\n}\\n\\n.canvas-tools-toolbar[_ngcontent-%COMP%] {\\n  position: absolute;\\n  display: flex;\\n  gap: 4px;\\n  z-index: 20;\\n  background: white;\\n  border: 1px solid #d1d5db;\\n  border-radius: 8px;\\n  padding: 8px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  -webkit-user-select: none;\\n          user-select: none;\\n  align-items: center;\\n  bottom: 20px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n}\\n\\n.canvas-action[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  box-shadow: none;\\n  width: fit-content;\\n  width: 300px;\\n}\\n\\n  canvas-tools-toolbar svg {\\n  stroke: rgb(var(--rgb-brand-primary)) !important;\\n}\\n\\n.canvas-tools-toolbar[_ngcontent-%COMP%]     ava-button .ava-button {\\n  min-width: 32px !important;\\n  width: 32px !important;\\n  height: 32px !important;\\n  padding: 6px !important;\\n  border-radius: 6px !important;\\n}\\n\\n.floating-toolbar[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 20px;\\n  right: 20px;\\n  display: flex;\\n  align-items: center;\\n  z-index: 1000;\\n}\\n\\n.navigation-hint[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n  margin-bottom: 10px;\\n  font-size: 11px;\\n  color: var(--text-color);\\n}\\n.navigation-hint[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n.navigation-hint[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]   kbd[_ngcontent-%COMP%] {\\n  background-color: var(--form-input-bg);\\n  border: 1px solid var(--form-input-border);\\n  border-radius: 3px;\\n  padding: 2px 6px;\\n  font-size: 10px;\\n  font-family: monospace;\\n  color: var(--text-secondary);\\n}\\n\\n.canvas-container[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n  background-color: var(--dashboard-bg-lighter);\\n  position: relative;\\n  cursor: grab;\\n  border-radius: 8px;\\n  transition: none;\\n}\\n.canvas-container.show-grid[_ngcontent-%COMP%] {\\n  background-image: linear-gradient(rgba(200, 200, 200, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(200, 200, 200, 0.1) 1px, transparent 1px);\\n  background-size: 20px 20px;\\n}\\n.canvas-container[_ngcontent-%COMP%]:active {\\n  cursor: grabbing;\\n}\\n.canvas-container.disabled[_ngcontent-%COMP%] {\\n  cursor: not-allowed;\\n  opacity: 0.8;\\n}\\n.canvas-container.disabled[_ngcontent-%COMP%]::after {\\n  content: \\\"Canvas interactions are disabled\\\";\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  background-color: rgba(0, 0, 0, 0.8);\\n  color: white;\\n  padding: 8px 16px;\\n  border-radius: 4px;\\n  font-size: 12px;\\n  pointer-events: none;\\n  z-index: 1001;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n.canvas-container.disabled[_ngcontent-%COMP%]:hover::after {\\n  opacity: 1;\\n}\\n\\n.canvas-viewport[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  position: absolute;\\n  transform-origin: 0 0;\\n  transition: none;\\n}\\n\\n.canvas-content[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  position: relative;\\n  overflow: visible;\\n}\\n\\n.connections-layer[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  z-index: 5;\\n  pointer-events: none;\\n}\\n.connections-layer[_ngcontent-%COMP%]   .edge-path[_ngcontent-%COMP%] {\\n  fill: none;\\n  stroke-width: 1.5px;\\n  pointer-events: all;\\n  cursor: pointer;\\n  stroke: #9ca1aa;\\n  transition: none;\\n}\\n.connections-layer[_ngcontent-%COMP%]   .edge-path.edge-animated[_ngcontent-%COMP%] {\\n  stroke-dasharray: 5;\\n  animation: _ngcontent-%COMP%_dash 1s linear infinite;\\n}\\n.connections-layer[_ngcontent-%COMP%]   .edge-path[_ngcontent-%COMP%]:hover {\\n  stroke-width: 2px;\\n  opacity: 0.8;\\n  transition: stroke-width 0.1s ease;\\n}\\n.connections-layer[_ngcontent-%COMP%]   .arrow-head[_ngcontent-%COMP%] {\\n  fill: #9ca1aa;\\n  stroke: none;\\n  opacity: 1;\\n}\\n.connections-layer[_ngcontent-%COMP%]   .edge-marker[_ngcontent-%COMP%] {\\n  overflow: visible;\\n}\\n.connections-layer[_ngcontent-%COMP%]   marker[_ngcontent-%COMP%] {\\n  overflow: visible;\\n}\\n.canvas-area.execute-mode[_ngcontent-%COMP%]   .connections-layer[_ngcontent-%COMP%]   .edge-path[_ngcontent-%COMP%] {\\n  stroke: #9ca1aa;\\n  stroke-width: 1.5px;\\n  stroke-linecap: round;\\n}\\n.canvas-area.execute-mode[_ngcontent-%COMP%]   .connections-layer[_ngcontent-%COMP%]   .edge-path[_ngcontent-%COMP%]:hover {\\n  stroke: #9ca1aa;\\n  stroke-width: 2px;\\n}\\n.canvas-area.execute-mode[_ngcontent-%COMP%]   .connections-layer[_ngcontent-%COMP%]   .arrow-head[_ngcontent-%COMP%] {\\n  fill: #9ca1aa;\\n}\\n\\n.nodes-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  height: 100%;\\n  z-index: 10;\\n}\\n\\n.temp-connection-layer[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  z-index: 100;\\n  pointer-events: none;\\n}\\n.temp-connection-layer[_ngcontent-%COMP%]   .temp-connection-path[_ngcontent-%COMP%] {\\n  fill: none;\\n  stroke-width: 1.5px;\\n  stroke-dasharray: 5;\\n  opacity: 0.7;\\n  stroke: #9ca1aa;\\n  animation: _ngcontent-%COMP%_dash 1s linear infinite;\\n}\\n.temp-connection-layer[_ngcontent-%COMP%]   marker[_ngcontent-%COMP%] {\\n  overflow: visible;\\n}\\n\\n.no-nodes-message[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  color: var(--text-secondary);\\n  font-size: 16px;\\n  text-align: center;\\n  pointer-events: none;\\n  z-index: 5;\\n}\\n\\n@keyframes _ngcontent-%COMP%_dash {\\n  from {\\n    stroke-dashoffset: 10;\\n  }\\n  to {\\n    stroke-dashoffset: 0;\\n  }\\n}\\n.navigation-hint[_ngcontent-%COMP%] {\\n  padding: 8px 16px;\\n  background-color: var(--dashboard-bg-lighter);\\n  border-bottom: 1px solid var(--form-input-border);\\n  font-size: 12px;\\n  color: var(--text-secondary);\\n  text-align: center;\\n}\\n.navigation-hint[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  margin: 0 4px;\\n}\\n\\n  .ava-button.secondary {\\n  border: 1px solid var(--Brand-Neutral-n-100, #d1d3d8) !important;\\n  border-radius: 8px !important;\\n  border-radius: 8px;\\n  background-color: white !important;\\n}\\n\\n  .black-icons svg {\\n  stroke: var(--Brand-Neutral-n-800, #3b3f46);\\n}\\n\\n  .canvas-tools-toolbar svg {\\n  stroke: var(--Brand-Neutral-n-800, #3b3f46);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return CanvasBoardComponent;\n})();", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormControl", "ReactiveFormsModule", "ButtonComponent", "AvaTextboxComponent", "AvaTextareaComponent", "IconComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵlistener", "CanvasBoardComponent_div_1_div_2_div_6_Template_ava_button_userClick_11_listener", "ɵɵrestoreView", "_r4", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "cancelAgentDetails", "CanvasBoardComponent_div_1_div_2_div_6_Template_ava_button_userClick_12_listener", "applyAgentDetails", "ɵɵadvance", "ɵɵproperty", "inputFieldsConfig", "agentDetails", "namePlaceholder", "agentDetailNameControl", "ɵɵtextInterpolate", "detail<PERSON><PERSON><PERSON>", "detailPlaceholder", "agentDetailControl", "ɵɵpureFunction0", "_c4", "_c5", "ɵɵprojection", "CanvasBoardComponent_div_1_div_2_Template_button_click_2_listener", "_r2", "toggleAgentDetailsDropdown", "ɵɵtemplate", "CanvasBoardComponent_div_1_div_2_div_6_Template", "CanvasBoardComponent_div_1_div_2_div_7_Template", "ɵɵclassProp", "isAgentDetailsDropdownOpen", "label", "ɵɵstyleProp", "showLeftActions", "CanvasBoardComponent_div_1_div_2_Template", "enabled", "CanvasBoardComponent_div_2_span_1_span_2_Template", "ɵɵtextInterpolate1", "hint_r5", "last_r6", "CanvasBoardComponent_div_2_span_1_Template", "navigationHints", "CanvasBoardComponent_div_6_Template_ava_button_userClick_1_listener", "_r7", "setSelectMode", "CanvasBoardComponent_div_6_Template_ava_button_userClick_2_listener", "setPanMode", "CanvasBoardComponent_div_6_Template_ava_button_userClick_3_listener", "zoomIn", "setZoomMode", "CanvasBoardComponent_div_6_Template_ava_button_userClick_4_listener", "zoomOut", "canvasMode", "CanvasBoardComponent_div_7_ava_button_2_Template_ava_button_userClick_0_listener", "_r8", "onUndo", "CanvasBoardComponent_div_7_ava_button_3_Template_ava_button_userClick_0_listener", "_r9", "onRedo", "CanvasBoardComponent_div_7_ava_button_4_Template_ava_button_userClick_0_listener", "_r10", "onPrimaryButtonClick", "_c6", "CanvasBoardComponent_div_7_ava_button_5_Template_ava_button_userClick_0_listener", "_r11", "primaryButtonText", "primaryButtonIcon", "CanvasBoardComponent_div_7_ava_button_2_Template", "CanvasBoardComponent_div_7_ava_button_3_Template", "CanvasBoardComponent_div_7_ava_button_4_Template", "CanvasBoardComponent_div_7_ava_button_5_Template", "enableUndo", "isExecuteMode", "enableRedo", "edge_r12", "animated", "CanvasBoardComponent__svg_svg_8__svg_path_1_Template", "edges", "edge_r13", "CanvasBoardComponent__svg_svg_11__svg_path_1_Template", "ɵɵelementContainer", "ɵɵelementContainerStart", "CanvasBoardComponent_ng_container_13_ng_container_1_Template", "nodeTemplate", "ɵɵpureFunctionV", "_c7", "node_r14", "selectedNodeId", "id", "onDeleteNode", "bind", "onNodeMoved", "onNodeSelected", "onNodeDoubleClicked", "onStartConnection", "mouseInteractionsEnabled", "fallbackMessage", "CanvasBoardComponent", "cdr", "canvasContainer", "nodes", "showGrid", "enablePan", "enableZoom", "enableConnections", "minZoom", "max<PERSON><PERSON>", "connectionColor", "showToolbar", "enableReset", "showCanvasTools", "enableGridToggle", "enablePanMode", "enableSelectionMode", "enableZoomControls", "showHeaderInputs", "initialAgentName", "initialAgentDetails", "initialMetadata", "org", "domain", "project", "team", "isMetadataDropdownOpen", "metadataStatus", "agentNameControl", "agentTypeControl", "agentTypeDisplayControl", "orgControl", "domainControl", "projectControl", "teamControl", "dropdownValues", "value", "nodeAdded", "nodeRemoved", "nodeMoved", "nodeSelected", "nodeDoubleClicked", "connectionStarted", "connectionCreated", "canvasDropped", "viewportChanged", "undoAction", "redoAction", "resetAction", "primaryButtonClicked", "stateChanged", "agentNameChanged", "agentTypeChanged", "metadataChanged", "agentDetailsChanged", "tempConnection", "isActive", "nodeConnectionPoints", "viewport", "zoom", "x", "y", "isDragging", "lastMouseX", "lastMouseY", "updateConnectionPointsFrame", "history", "historyIndex", "maxHistorySize", "isRestoringState", "showGridDots", "constructor", "ngOnInit", "setInitialValues", "valueChanges", "subscribe", "undefined", "emit", "setValue", "emitEvent", "trackByNodeId", "index", "node", "ngOnChanges", "changes", "currentValue", "console", "log", "map", "n", "position", "e", "source", "target", "updateNodeConnectionPoints", "updateEdgePaths", "setTimeout", "ngAfterViewInit", "setupCanvasNavigation", "requestAnimationFrame", "saveToHistory", "ngOnDestroy", "document", "removeEventListener", "handleKeyDown", "cancelAnimationFrame", "element", "nativeElement", "addEventListener", "event", "key", "isInputField", "tagName", "contentEditable", "closest", "resetViewport", "preventDefault", "detectChanges", "onDragOver", "dataTransfer", "dropEffect", "onDrop", "saveHistoryBeforeAction", "canvasBounds", "getBoundingClientRect", "clientX", "left", "clientY", "top", "safePosition", "Math", "max", "nodeId", "data", "nodeIndex", "findIndex", "find", "canvasRect", "mouseX", "mouseY", "handleX", "handleY", "connectionPoints", "nodeElement", "querySelector", "rect", "nodeX", "nodeY", "nodeWidth", "width", "nodeHeight", "height", "relativeX", "relativeY", "min", "sourceNodeId", "sourceHandleType", "handleType", "sourceX", "sourceY", "targetX", "targetY", "filter", "edge", "onCanvasMouseMove", "deltaX", "deltaY", "onCanvasWheel", "delta", "zoomSpeed", "newZoom", "zoomRatio", "newX", "newY", "onCanvasMouseDown", "button", "altKey", "style", "cursor", "onCanvasMouseUp", "targetNodeId", "getAttribute", "existingConnection", "newEdge", "push", "centerX", "centerY", "right", "bottom", "iconCenterX", "calculateEdgePath", "sourceNode", "targetNode", "createConnectionPath", "pathData", "findNearestConnectionPoints", "sourcePoints", "targetPoints", "sourcePoint", "targetPoint", "sourcePointOptions", "point", "name", "targetPointOptions", "bestDistance", "Infinity", "bestSourcePoint", "bestTargetPoint", "sourceOption", "targetOption", "distance", "sqrt", "pow", "getConnectionDirection", "dx", "dy", "abs", "adjustTargetForArrow", "direction", "offset", "length", "unitX", "unitY", "sourceRadius", "targetRadius", "arrowSpace", "adjustedSourceX", "adjustedSourceY", "adjustedTargetX", "adjustedTargetY", "targetNodeRadius", "<PERSON><PERSON><PERSON><PERSON>", "getTempConnectionPath", "baseOffset", "controlPointX1", "controlPointX2", "state", "restoreState", "onReset", "slice", "currentState", "JSON", "parse", "stringify", "shift", "addNode", "addEdge", "removeNode", "removeEdge", "edgeId", "to<PERSON><PERSON><PERSON>", "disableMouseInteractions", "setZoom", "onAgentNameChange", "onAgentTypeChange", "finalValue", "Array", "isArray", "toggleMetadataDropdown", "closeMetadataDropdown", "onDropdownSelect", "selected<PERSON><PERSON><PERSON>", "level", "currentType", "nextType", "loadDomainOptions", "loadProjectOptions", "loadTeamOptions", "applyMetadata", "<PERSON><PERSON><PERSON><PERSON>", "metadata", "statusText", "saved", "notSaved", "cancelMetadata", "domainOptionsMap", "ascendion", "company2", "company3", "projectOptions", "teamOptions", "closeAgentDetailsDropdown", "useCaseDetails", "ɵɵdirectiveInject", "ChangeDetectorRef", "selectors", "contentQueries", "CanvasBoardComponent_ContentQueries", "rf", "ctx", "dirIndex", "CanvasBoardComponent_div_1_Template", "CanvasBoardComponent_div_2_Template", "CanvasBoardComponent_Template_div_dragover_3_listener", "$event", "_r1", "CanvasBoardComponent_Template_div_drop_3_listener", "CanvasBoardComponent_Template_div_mouseup_3_listener", "CanvasBoardComponent_Template_div_mousemove_3_listener", "CanvasBoardComponent_Template_div_wheel_3_listener", "CanvasBoardComponent_Template_div_mousedown_3_listener", "CanvasBoardComponent_div_6_Template", "CanvasBoardComponent_div_7_Template", "CanvasBoardComponent__svg_svg_8_Template", "CanvasBoardComponent__svg_svg_11_Template", "CanvasBoardComponent_ng_container_13_Template", "CanvasBoardComponent__svg_svg_14_Template", "CanvasBoardComponent_div_15_Template", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "i2", "NgControlStatus", "FormControlDirective", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\components\\canvas-board\\canvas-board.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\components\\canvas-board\\canvas-board.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  Input,\r\n  Output,\r\n  EventEmitter,\r\n  ViewChild,\r\n  ElementRef,\r\n  AfterViewInit,\r\n  OnDestroy,\r\n  OnChanges,\r\n  OnInit,\r\n  ChangeDetectorRef,\r\n  ContentChild,\r\n  TemplateRef,\r\n} from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormControl, ReactiveFormsModule } from '@angular/forms';\r\nimport {\r\n  ButtonComponent,\r\n  AvaTextboxComponent,\r\n  AvaTextareaComponent,\r\n  IconComponent,\r\n} from '@ava/play-comp-library';\r\n\r\nexport interface CanvasNode {\r\n  id: string;\r\n  type: string;\r\n  data: any;\r\n  position: { x: number; y: number };\r\n}\r\n\r\nexport interface CanvasEdge {\r\n  id: string;\r\n  source: string;\r\n  target: string;\r\n  animated?: boolean;\r\n  pathData?: string; // Pre-calculated SVG path data\r\n}\r\n\r\nexport interface SelectOption {\r\n  value: string;\r\n  label: string;\r\n}\r\n\r\nexport interface CustomInputField {\r\n  id: string;\r\n  label: string;\r\n  type: 'text' | 'textarea' | 'select' | 'number' | 'email';\r\n  placeholder?: string;\r\n  required?: boolean;\r\n  disabled?: boolean;\r\n  options?: SelectOption[]; // For select type\r\n  control?: FormControl;\r\n  value?: any;\r\n  width?: string;\r\n  height?: string;\r\n}\r\n\r\nexport interface MetadataConfig {\r\n  enabled: boolean;\r\n  orgOptions: SelectOption[];\r\n  domainOptions: SelectOption[];\r\n  projectOptions: SelectOption[];\r\n  teamOptions: SelectOption[];\r\n  orgControl: FormControl;\r\n  domainControl: FormControl;\r\n  projectControl: FormControl;\r\n  teamControl: FormControl;\r\n}\r\n\r\ninterface TempConnection {\r\n  isActive: boolean;\r\n  sourceNodeId?: string;\r\n  targetNodeId?: string;\r\n  sourceHandleType?: 'source' | 'target';\r\n  sourceX?: number;\r\n  sourceY?: number;\r\n  targetX?: number;\r\n  targetY?: number;\r\n}\r\n\r\ntype CanvasToolMode = 'select' | 'pan' | 'zoom' | 'disabled';\r\n\r\ninterface CanvasViewport {\r\n  zoom: number;\r\n  x: number;\r\n  y: number;\r\n  isDragging: boolean;\r\n  lastMouseX: number;\r\n  lastMouseY: number;\r\n}\r\n\r\ninterface NodeConnectionPoints {\r\n  [nodeId: string]: {\r\n    top: { x: number; y: number };\r\n    right: { x: number; y: number };\r\n    bottom: { x: number; y: number };\r\n    left: { x: number; y: number };\r\n  };\r\n}\r\n\r\n@Component({\r\n  selector: 'app-canvas-board',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    ReactiveFormsModule,\r\n    ButtonComponent,\r\n    AvaTextboxComponent,\r\n    AvaTextareaComponent,\r\n    IconComponent,\r\n  ],\r\n  templateUrl: './canvas-board.component.html',\r\n  styleUrls: ['./canvas-board.component.scss'],\r\n})\r\nexport class CanvasBoardComponent\r\n  implements OnInit, AfterViewInit, OnDestroy, OnChanges\r\n{\r\n  @ViewChild('canvasContainer') canvasContainer!: ElementRef;\r\n  @ContentChild('nodeTemplate') nodeTemplate!: TemplateRef<any>;\r\n\r\n  // Inputs\r\n  @Input() nodes: CanvasNode[] = [];\r\n  @Input() edges: CanvasEdge[] = [];\r\n  @Input() showGrid: boolean = true;\r\n  @Input() enablePan: boolean = false; // Disabled by default\r\n  @Input() enableZoom: boolean = false; // Disabled by default\r\n  @Input() enableConnections: boolean = true;\r\n  @Input() minZoom: number = 0.1;\r\n  @Input() maxZoom: number = 2;\r\n  @Input() connectionColor: string = '#9ca3af'; // Grey color for connections\r\n  @Input() fallbackMessage: string = 'Drop items here to get started';\r\n  @Input() navigationHints: string[] = [\r\n    'Select toolbar options to enable canvas interactions',\r\n    'Alt + Drag to pan canvas (when enabled)',\r\n    'Mouse wheel to zoom (when enabled)',\r\n    'Space to reset view',\r\n  ];\r\n  @Input() showToolbar: boolean = true;\r\n  @Input() primaryButtonText: string = 'Execute';\r\n  @Input() primaryButtonIcon: string = '';\r\n  @Input() enableUndo: boolean = true;\r\n  @Input() enableRedo: boolean = true;\r\n  @Input() enableReset: boolean = true;\r\n  @Input() showCanvasTools: boolean = true;\r\n  @Input() enableGridToggle: boolean = true;\r\n  @Input() enablePanMode: boolean = true;\r\n  @Input() enableSelectionMode: boolean = true;\r\n  @Input() enableZoomControls: boolean = true;\r\n  @Input() isExecuteMode: boolean = false;\r\n  @Input() showLeftActions: boolean = true;\r\n  @Input() mouseInteractionsEnabled: boolean = true;\r\n\r\n  // Built-in input fields configuration\r\n  @Input() showHeaderInputs: boolean = false;\r\n  @Input() inputFieldsConfig: {\r\n    agentName?: {\r\n      enabled: boolean;\r\n      placeholder?: string;\r\n      required?: boolean;\r\n      style?: Record<string, any>;\r\n    };\r\n    agentType?: {\r\n      enabled: boolean;\r\n      options?: SelectOption[];\r\n      defaultValue?: string;\r\n    };\r\n    agentTypeTag?: {\r\n      enabled: boolean;\r\n      value?: string;\r\n      showInAgentsBuilderOnly?: boolean;\r\n    };\r\n    metadata?: {\r\n      enabled: boolean;\r\n      label?: string;\r\n      statusText?: { saved: string; notSaved: string };\r\n    };\r\n    agentDetails?: {\r\n      enabled: boolean;\r\n      label?: string;\r\n      namePlaceholder?: string;\r\n      detailPlaceholder?: string;\r\n      detailLabel?: string;\r\n    };\r\n  } = {};\r\n\r\n  // Input properties for initial values\r\n  @Input() initialAgentName: string = '';\r\n  @Input() initialAgentDetails: string = '';\r\n  @Input() initialMetadata: {\r\n    org: string;\r\n    domain: string;\r\n    project: string;\r\n    team: string;\r\n  } = {\r\n    org: '',\r\n    domain: '',\r\n    project: '',\r\n    team: '',\r\n  };\r\n\r\n  // Built-in metadata dropdown state\r\n  isMetadataDropdownOpen = false;\r\n  metadataStatus = 'Metadata Information not saved';\r\n\r\n  // Agent details dropdown state\r\n  isAgentDetailsDropdownOpen = false;\r\n\r\n  // Form controls for built-in inputs\r\n  @Input() agentNameControl = new FormControl('');\r\n  agentTypeControl = new FormControl('individual');\r\n  agentTypeDisplayControl = new FormControl('individual');\r\n\r\n  // Agent details form controls\r\n  @Input() agentDetailNameControl = new FormControl('');\r\n  @Input() agentDetailControl = new FormControl('');\r\n\r\n  // Metadata form controls\r\n  orgControl = new FormControl('');\r\n  domainControl = new FormControl('');\r\n  projectControl = new FormControl('');\r\n  teamControl = new FormControl('');\r\n\r\n  // Dropdown options\r\n  dropdownValues: { [key: string]: SelectOption[] } = {\r\n    org: [\r\n      { value: 'ascendion', label: 'Ascendion' },\r\n      { value: 'company2', label: 'Company 2' },\r\n      { value: 'company3', label: 'Company 3' },\r\n    ],\r\n    domain: [],\r\n    project: [],\r\n    team: [],\r\n  };\r\n\r\n  // Outputs\r\n  @Output() nodeAdded = new EventEmitter<CanvasNode>();\r\n  @Output() nodeRemoved = new EventEmitter<string>();\r\n  @Output() nodeMoved = new EventEmitter<{\r\n    nodeId: string;\r\n    position: { x: number; y: number };\r\n  }>();\r\n  @Output() nodeSelected = new EventEmitter<string>();\r\n  @Output() nodeDoubleClicked = new EventEmitter<string>();\r\n\r\n  @Output() connectionStarted = new EventEmitter<{\r\n    nodeId: string;\r\n    handleType: 'source' | 'target';\r\n    event: MouseEvent;\r\n  }>();\r\n  @Output() connectionCreated = new EventEmitter<CanvasEdge>();\r\n  @Output() canvasDropped = new EventEmitter<{\r\n    event: DragEvent;\r\n    position: { x: number; y: number };\r\n  }>();\r\n  @Output() viewportChanged = new EventEmitter<CanvasViewport>();\r\n  @Output() undoAction = new EventEmitter<void>();\r\n  @Output() redoAction = new EventEmitter<void>();\r\n  @Output() resetAction = new EventEmitter<void>();\r\n  @Output() primaryButtonClicked = new EventEmitter<void>();\r\n  @Output() stateChanged = new EventEmitter<{\r\n    nodes: CanvasNode[];\r\n    edges: CanvasEdge[];\r\n  }>();\r\n  @Output() agentNameChanged = new EventEmitter<string>();\r\n  @Output() agentTypeChanged = new EventEmitter<string>();\r\n  @Output() metadataChanged = new EventEmitter<{\r\n    org: string;\r\n    domain: string;\r\n    project: string;\r\n    team: string;\r\n  }>();\r\n  @Output() agentDetailsChanged = new EventEmitter<{\r\n    name: string;\r\n    useCaseDetails: string;\r\n  }>();\r\n\r\n  // Internal state\r\n  selectedNodeId: string | null = null;\r\n  tempConnection: TempConnection = { isActive: false };\r\n  nodeConnectionPoints: NodeConnectionPoints = {};\r\n  viewport: CanvasViewport = {\r\n    zoom: 1,\r\n    x: 0,\r\n    y: 0,\r\n    isDragging: false,\r\n    lastMouseX: 0,\r\n    lastMouseY: 0,\r\n  };\r\n\r\n  // Performance optimization for connection updates\r\n  private updateConnectionPointsFrame: number | null = null;\r\n\r\n  // Internal history management\r\n  private history: { nodes: CanvasNode[]; edges: CanvasEdge[] }[] = [];\r\n  private historyIndex: number = -1;\r\n  private maxHistorySize: number = 50;\r\n  private isRestoringState: boolean = false;\r\n\r\n  // Canvas tool states\r\n  canvasMode: CanvasToolMode = 'select'; // Start with select mode\r\n  showGridDots: boolean = true;\r\n\r\n  // Mouse function controls\r\n  // mouseInteractionsEnabled: boolean = true; // Enable mouse interactions by default\r\n\r\n  // Toolbar is now fixed at bottom center - no drag state needed\r\n\r\n  constructor(private cdr: ChangeDetectorRef) {}\r\n\r\n  ngOnInit(): void {\r\n    // Set initial values\r\n    this.setInitialValues();\r\n\r\n    // Subscribe to agent name changes\r\n    this.agentNameControl.valueChanges.subscribe((value) => {\r\n      if (value !== null && value !== undefined) {\r\n        this.agentNameChanged.emit(value);\r\n      }\r\n    });\r\n\r\n    // Subscribe to agent type changes\r\n    this.agentTypeControl.valueChanges.subscribe((value) => {\r\n      if (value !== null && value !== undefined) {\r\n        this.agentTypeChanged.emit(value);\r\n      }\r\n    });\r\n  }\r\n\r\n  private setInitialValues(): void {\r\n    // Set agent name\r\n    if (this.initialAgentName) {\r\n      this.agentNameControl.setValue(this.initialAgentName, {\r\n        emitEvent: false,\r\n      });\r\n    }\r\n\r\n    // Set agent details\r\n    if (this.initialAgentName) {\r\n      this.agentDetailNameControl.setValue(this.initialAgentName, {\r\n        emitEvent: false,\r\n      });\r\n    }\r\n    if (this.initialAgentDetails) {\r\n      this.agentDetailControl.setValue(this.initialAgentDetails, {\r\n        emitEvent: false,\r\n      });\r\n    }\r\n\r\n    // Set metadata\r\n    if (this.initialMetadata) {\r\n      this.orgControl.setValue(this.initialMetadata.org, { emitEvent: false });\r\n      this.domainControl.setValue(this.initialMetadata.domain, {\r\n        emitEvent: false,\r\n      });\r\n      this.projectControl.setValue(this.initialMetadata.project, {\r\n        emitEvent: false,\r\n      });\r\n      this.teamControl.setValue(this.initialMetadata.team, {\r\n        emitEvent: false,\r\n      });\r\n    }\r\n  }\r\n\r\n  // TrackBy function for nodes to maintain DOM element identity\r\n  trackByNodeId(index: number, node: CanvasNode): string {\r\n    return node.id;\r\n  }\r\n\r\n  ngOnChanges(changes: any): void {\r\n    // Update initial values when inputs change\r\n    if (\r\n      changes['initialAgentName'] ||\r\n      changes['initialAgentDetails'] ||\r\n      changes['initialMetadata']\r\n    ) {\r\n      this.setInitialValues();\r\n    }\r\n\r\n    // Update internal state when inputs change (but not during state restoration)\r\n    if (!this.isRestoringState) {\r\n      if (changes.nodes && changes.nodes.currentValue) {\r\n        console.log(\r\n          '🔗 ngOnChanges - Received new nodes:',\r\n          changes.nodes.currentValue.map((n: any) => ({\r\n            id: n.id,\r\n            position: n.position,\r\n          })),\r\n        );\r\n        this.nodes = [...changes.nodes.currentValue];\r\n      }\r\n      if (changes.edges && changes.edges.currentValue) {\r\n        console.log(\r\n          '🔗 ngOnChanges - Received new edges:',\r\n          changes.edges.currentValue.map((e: any) => ({\r\n            id: e.id,\r\n            source: e.source,\r\n            target: e.target,\r\n          })),\r\n        );\r\n        this.edges = [...changes.edges.currentValue];\r\n      }\r\n\r\n      // Update connection points when nodes change - IMMEDIATE\r\n      if (changes.nodes) {\r\n        console.log('🔗 ngOnChanges - Updating for node changes');\r\n        this.updateNodeConnectionPoints();\r\n        this.updateEdgePaths();\r\n        // Force additional update after DOM settles\r\n        setTimeout(() => this.updateNodeConnectionPoints(), 0);\r\n      }\r\n\r\n      if (changes.edges) {\r\n        console.log('🔗 ngOnChanges - Updating for edge changes');\r\n        this.updateEdgePaths();\r\n      }\r\n    }\r\n\r\n    // Update connection points when execute mode changes\r\n    if (changes['isExecuteMode']) {\r\n      // Toolbar is now fixed at bottom center via CSS\r\n      this.updateNodeConnectionPoints();\r\n    }\r\n\r\n    if (changes['mouseInteractionsEnabled']) {\r\n      this.mouseInteractionsEnabled =\r\n        changes['mouseInteractionsEnabled'].currentValue;\r\n    }\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    this.setupCanvasNavigation();\r\n    // Use requestAnimationFrame for smooth initialization\r\n    requestAnimationFrame(() => {\r\n      this.updateNodeConnectionPoints();\r\n      // Initialize history with current state\r\n      this.saveToHistory();\r\n      // Toolbar is now fixed at bottom center via CSS\r\n      // Set initial cursor to default arrow\r\n      this.setSelectMode();\r\n    });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // Cleanup event listeners\r\n    document.removeEventListener('keydown', this.handleKeyDown);\r\n\r\n    // Cancel any pending animation frame\r\n    if (this.updateConnectionPointsFrame) {\r\n      cancelAnimationFrame(this.updateConnectionPointsFrame);\r\n      this.updateConnectionPointsFrame = null;\r\n    }\r\n  }\r\n\r\n  private setupCanvasNavigation(): void {\r\n    const element = this.canvasContainer?.nativeElement;\r\n    if (element) {\r\n      document.addEventListener('keydown', this.handleKeyDown.bind(this));\r\n\r\n      element.addEventListener('mouseup', () => {\r\n        if (this.viewport.isDragging) {\r\n          this.updateNodeConnectionPoints();\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  private handleKeyDown = (event: KeyboardEvent): void => {\r\n    if (event.key === ' ') {\r\n      // Only reset viewport if the user is not typing in an input field\r\n      const target = event.target as HTMLElement;\r\n      const isInputField =\r\n        target.tagName === 'INPUT' ||\r\n        target.tagName === 'TEXTAREA' ||\r\n        target.contentEditable === 'true' ||\r\n        target.closest('ava-textbox') ||\r\n        target.closest('ava-textarea') ||\r\n        target.closest('.form-field') ||\r\n        target.closest('.input-container') ||\r\n        target.closest('.chat-input') ||\r\n        target.closest('textarea') ||\r\n        target.closest('input');\r\n\r\n      if (!isInputField) {\r\n        this.resetViewport();\r\n        event.preventDefault();\r\n      }\r\n    }\r\n  };\r\n\r\n  resetViewport(): void {\r\n    this.viewport = {\r\n      zoom: 1,\r\n      x: 0,\r\n      y: 0,\r\n      isDragging: false,\r\n      lastMouseX: 0,\r\n      lastMouseY: 0,\r\n    };\r\n\r\n    this.updateNodeConnectionPoints();\r\n    this.viewportChanged.emit(this.viewport);\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  onDragOver(event: DragEvent): void {\r\n    if (!this.enableConnections || !this.mouseInteractionsEnabled) return;\r\n    event.preventDefault();\r\n    if (event.dataTransfer) {\r\n      event.dataTransfer.dropEffect = 'move';\r\n    }\r\n  }\r\n\r\n  onDrop(event: DragEvent): void {\r\n    if (!this.mouseInteractionsEnabled) return;\r\n    event.preventDefault();\r\n\r\n    // Save history before adding new node\r\n    this.saveHistoryBeforeAction();\r\n\r\n    const canvasBounds =\r\n      this.canvasContainer.nativeElement.getBoundingClientRect();\r\n    const position = {\r\n      x:\r\n        (event.clientX - canvasBounds.left - this.viewport.x) /\r\n        this.viewport.zoom,\r\n      y:\r\n        (event.clientY - canvasBounds.top - this.viewport.y) /\r\n        this.viewport.zoom,\r\n    };\r\n\r\n    const safePosition = {\r\n      x: Math.max(0, position.x),\r\n      y: Math.max(0, position.y),\r\n    };\r\n\r\n    this.canvasDropped.emit({ event, position: safePosition });\r\n  }\r\n\r\n  onNodeSelected(nodeId: string): void {\r\n    this.selectedNodeId = nodeId;\r\n    this.nodeSelected.emit(nodeId);\r\n  }\r\n\r\n  onNodeDoubleClicked(nodeId: string): void {\r\n    this.nodeDoubleClicked.emit(nodeId);\r\n  }\r\n\r\n  onNodeMoved(data: {\r\n    nodeId: string;\r\n    position: { x: number; y: number };\r\n  }): void {\r\n    // Save history before moving node\r\n    this.saveHistoryBeforeAction();\r\n\r\n    // Update node position in internal state\r\n    const nodeIndex = this.nodes.findIndex((node) => node.id === data.nodeId);\r\n    if (nodeIndex !== -1) {\r\n      this.nodes[nodeIndex] = {\r\n        ...this.nodes[nodeIndex],\r\n        position: data.position,\r\n      };\r\n    }\r\n\r\n    // CRITICAL: Update edge paths immediately when node moves\r\n    console.log('🔗 Node moved - updating edge paths for:', data.nodeId);\r\n    this.updateEdgePaths();\r\n\r\n    this.nodeMoved.emit(data);\r\n    this.updateNodeConnectionPoints();\r\n\r\n    // Force immediate change detection to update connections\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  onStartConnection(data: {\r\n    nodeId: string;\r\n    handleType: 'source' | 'target';\r\n    event: MouseEvent;\r\n  }): void {\r\n    if (!this.enableConnections) return;\r\n\r\n    const node = this.nodes.find((n) => n.id === data.nodeId);\r\n    if (!node) return;\r\n\r\n    // Enhanced connection starting - use mouse position on the node as starting point\r\n    const canvasRect =\r\n      this.canvasContainer.nativeElement.getBoundingClientRect();\r\n\r\n    // Calculate the exact mouse position in canvas coordinates\r\n    const mouseX =\r\n      (data.event.clientX - canvasRect.left - this.viewport.x) /\r\n      this.viewport.zoom;\r\n    const mouseY =\r\n      (data.event.clientY - canvasRect.top - this.viewport.y) /\r\n      this.viewport.zoom;\r\n\r\n    // Use the mouse position as the starting point for more natural connections\r\n    let handleX = mouseX;\r\n    let handleY = mouseY;\r\n\r\n    // Get the node's connection points for fallback\r\n    const connectionPoints = this.nodeConnectionPoints[data.nodeId];\r\n    if (connectionPoints) {\r\n      // If we have calculated connection points, use the nearest edge point\r\n      const nodeElement = document.querySelector(\r\n        `[data-node-id=\"${data.nodeId}\"]`,\r\n      ) as HTMLElement;\r\n      if (nodeElement) {\r\n        const rect = nodeElement.getBoundingClientRect();\r\n        const nodeX =\r\n          (rect.left - canvasRect.left - this.viewport.x) / this.viewport.zoom;\r\n        const nodeY =\r\n          (rect.top - canvasRect.top - this.viewport.y) / this.viewport.zoom;\r\n        const nodeWidth = rect.width / this.viewport.zoom;\r\n        const nodeHeight = rect.height / this.viewport.zoom;\r\n\r\n        // Determine which edge of the node is closest to the mouse\r\n        const relativeX = mouseX - nodeX;\r\n        const relativeY = mouseY - nodeY;\r\n\r\n        // Clamp to node edges for clean connection start points\r\n        if (relativeX <= 0) {\r\n          // Left edge\r\n          handleX = nodeX;\r\n          handleY = Math.max(nodeY, Math.min(nodeY + nodeHeight, mouseY));\r\n        } else if (relativeX >= nodeWidth) {\r\n          // Right edge\r\n          handleX = nodeX + nodeWidth;\r\n          handleY = Math.max(nodeY, Math.min(nodeY + nodeHeight, mouseY));\r\n        } else if (relativeY <= 0) {\r\n          // Top edge\r\n          handleX = Math.max(nodeX, Math.min(nodeX + nodeWidth, mouseX));\r\n          handleY = nodeY;\r\n        } else if (relativeY >= nodeHeight) {\r\n          // Bottom edge\r\n          handleX = Math.max(nodeX, Math.min(nodeX + nodeWidth, mouseX));\r\n          handleY = nodeY + nodeHeight;\r\n        }\r\n      }\r\n    }\r\n\r\n    this.tempConnection = {\r\n      isActive: true,\r\n      sourceNodeId: data.nodeId,\r\n      sourceHandleType: data.handleType,\r\n      sourceX: handleX,\r\n      sourceY: handleY,\r\n      targetX: mouseX,\r\n      targetY: mouseY,\r\n    };\r\n\r\n    this.connectionStarted.emit(data);\r\n  }\r\n\r\n  onDeleteNode(nodeId: string): void {\r\n    // Save history before deleting node\r\n    this.saveHistoryBeforeAction();\r\n\r\n    // Remove node from internal state\r\n    this.nodes = this.nodes.filter((node) => node.id !== nodeId);\r\n\r\n    // Remove any edges connected to this node\r\n    this.edges = this.edges.filter(\r\n      (edge) => edge.source !== nodeId && edge.target !== nodeId,\r\n    );\r\n\r\n    this.nodeRemoved.emit(nodeId);\r\n    if (this.selectedNodeId === nodeId) {\r\n      this.selectedNodeId = null;\r\n    }\r\n\r\n    this.updateNodeConnectionPoints();\r\n  }\r\n\r\n  onCanvasMouseMove(event: MouseEvent): void {\r\n    // Handle canvas panning\r\n    if (this.viewport.isDragging && this.enablePan) {\r\n      const deltaX = event.clientX - this.viewport.lastMouseX;\r\n      const deltaY = event.clientY - this.viewport.lastMouseY;\r\n\r\n      this.viewport.x += deltaX;\r\n      this.viewport.y += deltaY;\r\n\r\n      this.viewport.lastMouseX = event.clientX;\r\n      this.viewport.lastMouseY = event.clientY;\r\n\r\n      this.cdr.detectChanges();\r\n      return;\r\n    }\r\n\r\n    if (!this.tempConnection.isActive) return;\r\n\r\n    // Update the temporary connection endpoint\r\n    const canvasRect =\r\n      this.canvasContainer.nativeElement.getBoundingClientRect();\r\n    const targetX =\r\n      (event.clientX - canvasRect.left - this.viewport.x) / this.viewport.zoom;\r\n    const targetY =\r\n      (event.clientY - canvasRect.top - this.viewport.y) / this.viewport.zoom;\r\n\r\n    this.tempConnection = {\r\n      ...this.tempConnection,\r\n      targetX,\r\n      targetY,\r\n    };\r\n\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  onCanvasWheel(event: WheelEvent): void {\r\n    if (!this.enableZoom || !this.mouseInteractionsEnabled) return;\r\n    event.preventDefault();\r\n\r\n    const delta = -event.deltaY;\r\n    const zoomSpeed = 0.001;\r\n    const newZoom = Math.max(\r\n      this.minZoom,\r\n      Math.min(this.maxZoom, this.viewport.zoom + delta * zoomSpeed),\r\n    );\r\n\r\n    if (newZoom !== this.viewport.zoom) {\r\n      const rect = this.canvasContainer.nativeElement.getBoundingClientRect();\r\n      const mouseX = event.clientX - rect.left;\r\n      const mouseY = event.clientY - rect.top;\r\n\r\n      const zoomRatio = newZoom / this.viewport.zoom;\r\n      const newX = mouseX - (mouseX - this.viewport.x) * zoomRatio;\r\n      const newY = mouseY - (mouseY - this.viewport.y) * zoomRatio;\r\n\r\n      this.viewport.zoom = newZoom;\r\n      this.viewport.x = newX;\r\n      this.viewport.y = newY;\r\n\r\n      this.updateNodeConnectionPoints();\r\n      this.viewportChanged.emit(this.viewport);\r\n      this.cdr.detectChanges();\r\n    }\r\n  }\r\n\r\n  onCanvasMouseDown(event: MouseEvent): void {\r\n    if (!this.enablePan || !this.mouseInteractionsEnabled) return;\r\n\r\n    // Start canvas dragging when:\r\n    // 1. Middle mouse button is pressed\r\n    // 2. Alt+left click\r\n    // 3. Pan mode is active and left click\r\n    if (\r\n      event.button === 1 ||\r\n      (event.button === 0 && event.altKey) ||\r\n      (event.button === 0 && this.canvasMode === 'pan')\r\n    ) {\r\n      this.viewport.isDragging = true;\r\n      this.viewport.lastMouseX = event.clientX;\r\n      this.viewport.lastMouseY = event.clientY;\r\n\r\n      // Update cursor during drag\r\n      if (this.canvasContainer) {\r\n        this.canvasContainer.nativeElement.style.cursor = 'grabbing';\r\n      }\r\n\r\n      event.preventDefault();\r\n    }\r\n  }\r\n\r\n  onCanvasMouseUp(event: MouseEvent): void {\r\n    this.viewport.isDragging = false;\r\n\r\n    // Restore cursor based on current mode\r\n    if (this.canvasContainer) {\r\n      this.canvasContainer.nativeElement.style.cursor =\r\n        this.canvasMode === 'pan' ? 'grab' : 'default';\r\n    }\r\n\r\n    if (!this.tempConnection.isActive || !this.enableConnections) return;\r\n\r\n    const target = event.target as HTMLElement;\r\n\r\n    // Enhanced connection logic - allow connections to any part of a node\r\n    const nodeElement = target.closest('[data-node-id]');\r\n\r\n    if (nodeElement && this.tempConnection.sourceNodeId) {\r\n      const sourceNodeId = this.tempConnection.sourceNodeId;\r\n      const targetNodeId = nodeElement.getAttribute('data-node-id');\r\n\r\n      // Allow connections between different nodes\r\n      if (targetNodeId && sourceNodeId !== targetNodeId) {\r\n        // Check if connection already exists to prevent duplicates\r\n        const existingConnection = this.edges.find(\r\n          (edge) =>\r\n            (edge.source === sourceNodeId && edge.target === targetNodeId) ||\r\n            (edge.source === targetNodeId && edge.target === sourceNodeId),\r\n        );\r\n\r\n        if (!existingConnection) {\r\n          // Save history before creating connection\r\n          this.saveHistoryBeforeAction();\r\n\r\n          const newEdge: CanvasEdge = {\r\n            id: `${sourceNodeId}-${targetNodeId}`,\r\n            source: sourceNodeId,\r\n            target: targetNodeId,\r\n            animated: false, // Beautiful static connections\r\n          };\r\n\r\n          // Add edge to internal state\r\n          this.edges.push(newEdge);\r\n\r\n          this.connectionCreated.emit(newEdge);\r\n        }\r\n      }\r\n    }\r\n\r\n    this.tempConnection = { isActive: false };\r\n  }\r\n\r\n  updateNodeConnectionPoints(): void {\r\n    if (this.updateConnectionPointsFrame) {\r\n      cancelAnimationFrame(this.updateConnectionPointsFrame);\r\n    }\r\n\r\n    // SIMPLIFIED: Immediate update for consistent connection rendering\r\n    this.updateConnectionPointsFrame = requestAnimationFrame(() => {\r\n      this.nodeConnectionPoints = {};\r\n\r\n      for (const node of this.nodes) {\r\n        const nodeElement = document.querySelector(\r\n          `[data-node-id=\"${node.id}\"]`,\r\n        ) as HTMLElement;\r\n\r\n        // Calculate connection points even for nodes outside viewport\r\n        let nodeX: number, nodeY: number, nodeWidth: number, nodeHeight: number;\r\n\r\n        if (nodeElement) {\r\n          // Node is in DOM - use actual measurements\r\n          const rect = nodeElement.getBoundingClientRect();\r\n          const canvasRect =\r\n            this.canvasContainer?.nativeElement.getBoundingClientRect();\r\n          if (!canvasRect) continue;\r\n\r\n          // In execute mode, always use the actual DOM position since CSS transforms the layout\r\n          if (this.isExecuteMode) {\r\n            // Use actual DOM position without viewport adjustments for execute mode\r\n            nodeX = rect.left - canvasRect.left;\r\n            nodeY = rect.top - canvasRect.top;\r\n            nodeWidth = rect.width;\r\n            nodeHeight = rect.height;\r\n          } else {\r\n            // Build mode - use viewport-adjusted coordinates\r\n            nodeX =\r\n              (rect.left - canvasRect.left - this.viewport.x) /\r\n              this.viewport.zoom;\r\n            nodeY =\r\n              (rect.top - canvasRect.top - this.viewport.y) /\r\n              this.viewport.zoom;\r\n            nodeWidth = rect.width / this.viewport.zoom;\r\n            nodeHeight = rect.height / this.viewport.zoom;\r\n          }\r\n        } else {\r\n          // Node is outside viewport - use node position data\r\n          nodeX = node.position.x;\r\n          nodeY = node.position.y;\r\n          // Use default dimensions based on mode\r\n          nodeWidth = this.isExecuteMode ? 55 : 90; // Execute mode nodes are 55px (circular with border)\r\n          nodeHeight = this.isExecuteMode ? 55 : 48; // Execute mode nodes are 55px (circular with border)\r\n        }\r\n\r\n        if (this.isExecuteMode) {\r\n          // Execute mode - icon center to icon center connections\r\n          const centerX = nodeX + nodeWidth / 2;\r\n          const centerY = nodeY + nodeHeight / 2;\r\n\r\n          // All connection points are at the center of the circular icon for icon-to-icon connections\r\n          this.nodeConnectionPoints[node.id] = {\r\n            top: { x: centerX, y: centerY },\r\n            right: { x: centerX, y: centerY },\r\n            bottom: { x: centerX, y: centerY },\r\n            left: { x: centerX, y: centerY },\r\n          };\r\n\r\n          console.log(\r\n            `Execute mode node ${node.id}: center(${centerX}, ${centerY})`,\r\n          );\r\n        } else {\r\n          // Build mode - account for node structure and padding\r\n          // The node has 10px padding and the icon is 40px with center at 30px from left edge\r\n          const iconCenterX = nodeX + 30; // 10px padding + 20px to icon center\r\n          const centerY = nodeY + nodeHeight / 2; // Vertical center\r\n\r\n          // Create connection points optimized for the node structure\r\n          this.nodeConnectionPoints[node.id] = {\r\n            top: { x: iconCenterX, y: nodeY },\r\n            right: { x: nodeX + nodeWidth, y: centerY },\r\n            bottom: { x: iconCenterX, y: nodeY + nodeHeight },\r\n            left: { x: nodeX, y: centerY },\r\n          };\r\n        }\r\n      }\r\n\r\n      // Trigger change detection immediately for consistent rendering\r\n      this.cdr.detectChanges();\r\n      this.updateConnectionPointsFrame = null;\r\n    });\r\n  }\r\n\r\n  // REMOVED getEdgePath getter to prevent infinite loops\r\n  // The template now uses edge.pathData directly\r\n\r\n  private calculateEdgePath(\r\n    sourceNode: CanvasNode,\r\n    targetNode: CanvasNode,\r\n  ): string {\r\n    // Calculate connection points based on mode\r\n    let sourceX: number, sourceY: number, targetX: number, targetY: number;\r\n\r\n    if (this.isExecuteMode) {\r\n      // Execute mode - center to center\r\n      sourceX = sourceNode.position.x + 27.5; // Center of 55px node\r\n      sourceY = sourceNode.position.y + 27.5;\r\n      targetX = targetNode.position.x + 27.5;\r\n      targetY = targetNode.position.y + 27.5;\r\n    } else {\r\n      // Build mode - icon center to icon center for consistency\r\n      sourceX = sourceNode.position.x + 30; // Icon center (10px padding + 20px to center)\r\n      sourceY = sourceNode.position.y + 30; // Icon center\r\n      targetX = targetNode.position.x + 30;\r\n      targetY = targetNode.position.y + 30;\r\n    }\r\n\r\n    // Create simple, reliable connection path\r\n    return this.createConnectionPath(\r\n      { x: sourceX, y: sourceY },\r\n      { x: targetX, y: targetY },\r\n    );\r\n  }\r\n\r\n  // Pre-calculate all edge paths when nodes or edges change\r\n  public updateEdgePaths(): void {\r\n    console.log(\r\n      '🔗 updateEdgePaths - Current nodes:',\r\n      this.nodes.map((n) => ({ id: n.id, position: n.position })),\r\n    );\r\n    console.log(\r\n      '🔗 updateEdgePaths - Current edges:',\r\n      this.edges.map((e) => ({ id: e.id, source: e.source, target: e.target })),\r\n    );\r\n\r\n    this.edges = this.edges.map((edge) => {\r\n      const sourceNode = this.nodes.find((n) => n.id === edge.source);\r\n      const targetNode = this.nodes.find((n) => n.id === edge.target);\r\n\r\n      if (sourceNode && targetNode) {\r\n        const pathData = this.calculateEdgePath(sourceNode, targetNode);\r\n        console.log(`🔗 SIMPLE: ${edge.id} -> ${pathData}`);\r\n        console.log(\r\n          `🔗 SOURCE: ${sourceNode.id} at (${sourceNode.position.x}, ${sourceNode.position.y})`,\r\n        );\r\n        console.log(\r\n          `🔗 TARGET: ${targetNode.id} at (${targetNode.position.x}, ${targetNode.position.y})`,\r\n        );\r\n        return {\r\n          ...edge,\r\n          pathData: pathData,\r\n        } as any;\r\n      } else {\r\n        // Fallback for missing nodes\r\n        console.log(\r\n          `🔗 WARNING: Missing nodes for edge ${edge.id}, using empty path`,\r\n        );\r\n        return {\r\n          ...edge,\r\n          pathData: '',\r\n        } as any;\r\n      }\r\n    });\r\n\r\n    console.log('🔗 updateEdgePaths completed - All edges now have pathData');\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  private findNearestConnectionPoints(\r\n    sourcePoints: any,\r\n    targetPoints: any,\r\n  ): {\r\n    sourcePoint: { x: number; y: number };\r\n    targetPoint: { x: number; y: number };\r\n  } {\r\n    if (this.isExecuteMode) {\r\n      // Execute mode - icon center to icon center connections\r\n      // Since all connection points are at the center, use the center point directly\r\n      return {\r\n        sourcePoint: sourcePoints.top, // All points are the same (center), so any will work\r\n        targetPoint: targetPoints.top, // All points are the same (center), so any will work\r\n      };\r\n    } else {\r\n      // Build mode - find optimal connection points for any node placement\r\n      // Calculate all possible connection combinations\r\n      const sourcePointOptions = [\r\n        { point: sourcePoints.right, name: 'right' },\r\n        { point: sourcePoints.bottom, name: 'bottom' },\r\n        { point: sourcePoints.left, name: 'left' },\r\n        { point: sourcePoints.top, name: 'top' },\r\n      ];\r\n\r\n      const targetPointOptions = [\r\n        { point: targetPoints.left, name: 'left' },\r\n        { point: targetPoints.top, name: 'top' },\r\n        { point: targetPoints.right, name: 'right' },\r\n        { point: targetPoints.bottom, name: 'bottom' },\r\n      ];\r\n\r\n      let bestDistance = Infinity;\r\n      let bestSourcePoint = sourcePoints.right;\r\n      let bestTargetPoint = targetPoints.left;\r\n\r\n      // Find the shortest distance combination\r\n      for (const sourceOption of sourcePointOptions) {\r\n        for (const targetOption of targetPointOptions) {\r\n          const distance = Math.sqrt(\r\n            Math.pow(targetOption.point.x - sourceOption.point.x, 2) +\r\n              Math.pow(targetOption.point.y - sourceOption.point.y, 2),\r\n          );\r\n\r\n          if (distance < bestDistance) {\r\n            bestDistance = distance;\r\n            bestSourcePoint = sourceOption.point;\r\n            bestTargetPoint = targetOption.point;\r\n          }\r\n        }\r\n      }\r\n\r\n      return {\r\n        sourcePoint: bestSourcePoint,\r\n        targetPoint: bestTargetPoint,\r\n      };\r\n    }\r\n  }\r\n\r\n  private getConnectionDirection(\r\n    sourcePoint: { x: number; y: number },\r\n    targetPoint: { x: number; y: number },\r\n  ): string {\r\n    const dx = targetPoint.x - sourcePoint.x;\r\n    const dy = targetPoint.y - sourcePoint.y;\r\n\r\n    if (Math.abs(dx) > Math.abs(dy)) {\r\n      return dx > 0 ? 'right' : 'left';\r\n    } else {\r\n      return dy > 0 ? 'bottom' : 'top';\r\n    }\r\n  }\r\n\r\n  private adjustTargetForArrow(\r\n    targetPoint: { x: number; y: number },\r\n    direction: string,\r\n    offset: number,\r\n  ): { x: number; y: number } {\r\n    switch (direction) {\r\n      case 'right':\r\n        return { x: targetPoint.x - offset, y: targetPoint.y };\r\n      case 'left':\r\n        return { x: targetPoint.x + offset, y: targetPoint.y };\r\n      case 'bottom':\r\n        return { x: targetPoint.x, y: targetPoint.y - offset };\r\n      case 'top':\r\n        return { x: targetPoint.x, y: targetPoint.y + offset };\r\n      default:\r\n        return targetPoint;\r\n    }\r\n  }\r\n\r\n  private createConnectionPath(\r\n    sourcePoint: { x: number; y: number },\r\n    targetPoint: { x: number; y: number },\r\n  ): string {\r\n    // SIMPLIFIED: Create straight lines for consistent vertical stacking\r\n    const sourceX = sourcePoint.x;\r\n    const sourceY = sourcePoint.y;\r\n    const targetX = targetPoint.x;\r\n    const targetY = targetPoint.y;\r\n\r\n    if (this.isExecuteMode) {\r\n      // Execute mode - direct line between centers with arrow spacing\r\n      const dx = targetX - sourceX;\r\n      const dy = targetY - sourceY;\r\n      const length = Math.sqrt(dx * dx + dy * dy);\r\n\r\n      if (length < 20) {\r\n        return `M ${sourceX} ${sourceY} L ${targetX} ${targetY}`;\r\n      }\r\n\r\n      const unitX = dx / length;\r\n      const unitY = dy / length;\r\n      const sourceRadius = 27;\r\n      const targetRadius = 20;\r\n      const arrowSpace = 8;\r\n\r\n      const adjustedSourceX = sourceX + unitX * sourceRadius;\r\n      const adjustedSourceY = sourceY + unitY * sourceRadius;\r\n      const adjustedTargetX = targetX - unitX * (targetRadius + arrowSpace);\r\n      const adjustedTargetY = targetY - unitY * (targetRadius + arrowSpace);\r\n\r\n      return `M ${adjustedSourceX} ${adjustedSourceY} L ${adjustedTargetX} ${adjustedTargetY}`;\r\n    } else {\r\n      // Build mode - ALWAYS STRAIGHT LINES regardless of node positioning\r\n      const dx = targetX - sourceX;\r\n      const dy = targetY - sourceY;\r\n      const distance = Math.sqrt(dx * dx + dy * dy);\r\n\r\n      if (distance < 20) {\r\n        // Very close nodes - direct line\r\n        return `M ${sourceX} ${sourceY} L ${targetX} ${targetY}`;\r\n      }\r\n\r\n      // Calculate arrow spacing to ensure arrows are visible\r\n      const unitX = dx / distance;\r\n      const unitY = dy / distance;\r\n\r\n      // Adjust source to start from node edge\r\n      const sourceRadius = 30; // Start from edge of source node\r\n      const adjustedSourceX = sourceX + unitX * sourceRadius;\r\n      const adjustedSourceY = sourceY + unitY * sourceRadius;\r\n\r\n      // Adjust target to end before the node (leave space for arrow)\r\n      const targetNodeRadius = 30; // Target node radius\r\n      const arrowLength = 15; // Space for the arrow itself\r\n      const adjustedTargetX =\r\n        targetX - unitX * (targetNodeRadius + arrowLength);\r\n      const adjustedTargetY =\r\n        targetY - unitY * (targetNodeRadius + arrowLength);\r\n\r\n      // ALWAYS use straight lines - no curves\r\n      return `M ${adjustedSourceX} ${adjustedSourceY} L ${adjustedTargetX} ${adjustedTargetY}`;\r\n    }\r\n  }\r\n\r\n  getTempConnectionPath(): string {\r\n    if (\r\n      !this.tempConnection.isActive ||\r\n      this.tempConnection.sourceX === undefined ||\r\n      this.tempConnection.sourceY === undefined ||\r\n      this.tempConnection.targetX === undefined ||\r\n      this.tempConnection.targetY === undefined\r\n    ) {\r\n      return '';\r\n    }\r\n\r\n    const sourceX = this.tempConnection.sourceX;\r\n    const sourceY = this.tempConnection.sourceY;\r\n    const targetX = this.tempConnection.targetX;\r\n    const targetY = this.tempConnection.targetY;\r\n\r\n    // Create beautiful Bézier curves exactly like workflow editor\r\n    const dx = targetX - sourceX;\r\n    const dy = targetY - sourceY;\r\n    const distance = Math.sqrt(dx * dx + dy * dy);\r\n\r\n    // Use the exact same offset calculation as your workflow editor\r\n    const baseOffset = Math.min(100, distance * 0.5);\r\n    const offset = Math.max(50, baseOffset);\r\n\r\n    // Create horizontal Bézier curve with proper control points\r\n    const controlPointX1 = sourceX + offset;\r\n    const controlPointX2 = targetX - offset;\r\n\r\n    return `M ${sourceX} ${sourceY} C ${controlPointX1} ${sourceY}, ${controlPointX2} ${targetY}, ${targetX} ${targetY}`;\r\n  }\r\n\r\n  // Toolbar actions\r\n  onUndo(): void {\r\n    if (this.historyIndex > 0) {\r\n      this.historyIndex--;\r\n      const state = this.history[this.historyIndex];\r\n      this.restoreState(state);\r\n    }\r\n    this.undoAction.emit();\r\n  }\r\n\r\n  onRedo(): void {\r\n    if (this.historyIndex < this.history.length - 1) {\r\n      this.historyIndex++;\r\n      const state = this.history[this.historyIndex];\r\n      this.restoreState(state);\r\n    }\r\n    this.redoAction.emit();\r\n  }\r\n\r\n  onReset(): void {\r\n    // Save current state before reset\r\n    this.saveToHistory();\r\n\r\n    // Clear all nodes and edges\r\n    this.nodes = [];\r\n    this.edges = [];\r\n    this.selectedNodeId = null;\r\n    this.tempConnection = { isActive: false };\r\n\r\n    // Reset viewport\r\n    this.resetViewport();\r\n\r\n    // Emit state change to parent component\r\n    this.stateChanged.emit({\r\n      nodes: [],\r\n      edges: [],\r\n    });\r\n\r\n    // Emit events\r\n    this.nodeRemoved.emit('all'); // Special case for clearing all\r\n    this.resetAction.emit();\r\n  }\r\n\r\n  onPrimaryButtonClick(): void {\r\n    this.primaryButtonClicked.emit();\r\n  }\r\n\r\n  // History management methods\r\n  private saveToHistory(): void {\r\n    // Don't save history during state restoration\r\n    if (this.isRestoringState) return;\r\n\r\n    // Remove any history after current index (when we're not at the end)\r\n    this.history = this.history.slice(0, this.historyIndex + 1);\r\n\r\n    // Add current state to history\r\n    const currentState = {\r\n      nodes: JSON.parse(JSON.stringify(this.nodes)),\r\n      edges: JSON.parse(JSON.stringify(this.edges)),\r\n    };\r\n\r\n    this.history.push(currentState);\r\n    this.historyIndex = this.history.length - 1;\r\n\r\n    // Limit history size\r\n    if (this.history.length > this.maxHistorySize) {\r\n      this.history.shift();\r\n      this.historyIndex--;\r\n    }\r\n  }\r\n\r\n  private restoreState(state: {\r\n    nodes: CanvasNode[];\r\n    edges: CanvasEdge[];\r\n  }): void {\r\n    this.isRestoringState = true;\r\n\r\n    // Update the internal state\r\n    this.nodes = [...state.nodes];\r\n    this.edges = [...state.edges];\r\n\r\n    // Clear selection and temp connections\r\n    this.selectedNodeId = null;\r\n    this.tempConnection = { isActive: false };\r\n\r\n    // Update connection points\r\n    this.updateNodeConnectionPoints();\r\n\r\n    // Emit state change to parent component\r\n    this.stateChanged.emit({\r\n      nodes: [...this.nodes],\r\n      edges: [...this.edges],\r\n    });\r\n\r\n    // Reset flag after a short delay\r\n    setTimeout(() => {\r\n      this.isRestoringState = false;\r\n    }, 100);\r\n  }\r\n\r\n  // Save history before actions\r\n  private saveHistoryBeforeAction(): void {\r\n    this.saveToHistory();\r\n  }\r\n\r\n  // Public methods for external components to add/remove nodes and edges\r\n  addNode(node: CanvasNode): void {\r\n    this.saveHistoryBeforeAction();\r\n    this.nodes.push(node);\r\n    this.updateNodeConnectionPoints();\r\n    this.nodeAdded.emit(node);\r\n  }\r\n\r\n  addEdge(edge: CanvasEdge): void {\r\n    this.saveHistoryBeforeAction();\r\n    this.edges.push(edge);\r\n  }\r\n\r\n  removeNode(nodeId: string): void {\r\n    this.onDeleteNode(nodeId);\r\n  }\r\n\r\n  removeEdge(edgeId: string): void {\r\n    this.saveHistoryBeforeAction();\r\n    this.edges = this.edges.filter((edge) => edge.id !== edgeId);\r\n  }\r\n\r\n  // Canvas tool methods\r\n  toggleGrid(): void {\r\n    this.showGridDots = !this.showGridDots;\r\n    this.showGrid = this.showGridDots;\r\n  }\r\n\r\n  setPanMode(): void {\r\n    this.canvasMode = 'pan';\r\n    this.mouseInteractionsEnabled = true;\r\n    if (this.canvasContainer) {\r\n      this.canvasContainer.nativeElement.style.cursor = 'grab';\r\n    }\r\n  }\r\n\r\n  setSelectMode(): void {\r\n    this.canvasMode = 'select';\r\n    this.mouseInteractionsEnabled = true;\r\n    if (this.canvasContainer) {\r\n      this.canvasContainer.nativeElement.style.cursor = 'default';\r\n    }\r\n  }\r\n\r\n  setZoomMode(): void {\r\n    this.canvasMode = 'zoom';\r\n    this.mouseInteractionsEnabled = true;\r\n    if (this.canvasContainer) {\r\n      this.canvasContainer.nativeElement.style.cursor = 'zoom-in';\r\n    }\r\n  }\r\n\r\n  disableMouseInteractions(): void {\r\n    this.canvasMode = 'disabled';\r\n    this.mouseInteractionsEnabled = false;\r\n    if (this.canvasContainer) {\r\n      this.canvasContainer.nativeElement.style.cursor = 'not-allowed';\r\n    }\r\n  }\r\n\r\n  zoomIn(): void {\r\n    const newZoom = Math.min(this.maxZoom, this.viewport.zoom * 1.2);\r\n    this.setZoom(newZoom);\r\n  }\r\n\r\n  zoomOut(): void {\r\n    const newZoom = Math.max(this.minZoom, this.viewport.zoom / 1.2);\r\n    this.setZoom(newZoom);\r\n  }\r\n\r\n  private setZoom(newZoom: number): void {\r\n    if (newZoom !== this.viewport.zoom) {\r\n      // Get canvas center for zoom\r\n      const rect = this.canvasContainer.nativeElement.getBoundingClientRect();\r\n      const centerX = rect.width / 2;\r\n      const centerY = rect.height / 2;\r\n\r\n      // Calculate new position to zoom towards center\r\n      const zoomRatio = newZoom / this.viewport.zoom;\r\n      const newX = centerX - (centerX - this.viewport.x) * zoomRatio;\r\n      const newY = centerY - (centerY - this.viewport.y) * zoomRatio;\r\n\r\n      // Update viewport\r\n      this.viewport.zoom = newZoom;\r\n      this.viewport.x = newX;\r\n      this.viewport.y = newY;\r\n\r\n      // Update connection points\r\n      this.updateNodeConnectionPoints();\r\n\r\n      // Emit viewport change\r\n      this.viewportChanged.emit(this.viewport);\r\n    }\r\n  }\r\n\r\n  // Built-in input field methods\r\n  onAgentNameChange(value: string): void {\r\n    this.agentNameControl.setValue(value);\r\n    this.agentNameChanged.emit(value);\r\n  }\r\n\r\n  onAgentTypeChange(value: string | string[]): void {\r\n    const finalValue = Array.isArray(value) ? value[0] : value;\r\n    this.agentTypeControl.setValue(finalValue);\r\n    this.agentTypeChanged.emit(finalValue);\r\n  }\r\n\r\n  // Built-in metadata dropdown methods\r\n  toggleMetadataDropdown(): void {\r\n    this.isMetadataDropdownOpen = !this.isMetadataDropdownOpen;\r\n  }\r\n\r\n  closeMetadataDropdown(): void {\r\n    this.isMetadataDropdownOpen = false;\r\n  }\r\n\r\n  onDropdownSelect(\r\n    selectedValue: string | string[],\r\n    level: number,\r\n    currentType: string,\r\n    nextType: string,\r\n  ): void {\r\n    const value = Array.isArray(selectedValue)\r\n      ? selectedValue[0]\r\n      : selectedValue;\r\n    if (!value) return;\r\n\r\n    // Update the current dropdown value\r\n    switch (currentType) {\r\n      case 'org':\r\n        this.orgControl.setValue(value);\r\n        // Reset dependent dropdowns\r\n        this.domainControl.setValue('');\r\n        this.projectControl.setValue('');\r\n        this.teamControl.setValue('');\r\n        // Load domain options\r\n        this.loadDomainOptions(value);\r\n        break;\r\n      case 'domain':\r\n        this.domainControl.setValue(value);\r\n        // Reset dependent dropdowns\r\n        this.projectControl.setValue('');\r\n        this.teamControl.setValue('');\r\n        // Load project options\r\n        this.loadProjectOptions(this.orgControl.value || '', value);\r\n        break;\r\n      case 'project':\r\n        this.projectControl.setValue(value);\r\n        // Reset dependent dropdown\r\n        this.teamControl.setValue('');\r\n        // Load team options\r\n        this.loadTeamOptions(\r\n          this.orgControl.value || '',\r\n          this.domainControl.value || '',\r\n          value,\r\n        );\r\n        break;\r\n      case 'team':\r\n        this.teamControl.setValue(value);\r\n        break;\r\n    }\r\n  }\r\n\r\n  applyMetadata(): void {\r\n    const hasValues =\r\n      this.orgControl.value ||\r\n      this.domainControl.value ||\r\n      this.projectControl.value ||\r\n      this.teamControl.value;\r\n\r\n    if (hasValues) {\r\n      this.metadataStatus =\r\n        this.inputFieldsConfig.metadata?.statusText?.saved ||\r\n        'Metadata Information saved';\r\n    } else {\r\n      this.metadataStatus =\r\n        this.inputFieldsConfig.metadata?.statusText?.notSaved ||\r\n        'Metadata Information not saved';\r\n    }\r\n\r\n    // Emit metadata change event\r\n    this.metadataChanged.emit({\r\n      org: this.orgControl.value || '',\r\n      domain: this.domainControl.value || '',\r\n      project: this.projectControl.value || '',\r\n      team: this.teamControl.value || '',\r\n    });\r\n\r\n    this.closeMetadataDropdown();\r\n  }\r\n\r\n  cancelMetadata(): void {\r\n    this.closeMetadataDropdown();\r\n  }\r\n\r\n  // Load dropdown options (same API pattern as nav-item)\r\n  private loadDomainOptions(org: string): void {\r\n    const domainOptionsMap: { [key: string]: SelectOption[] } = {\r\n      ascendion: [\r\n        { value: 'engineering', label: 'Engineering' },\r\n        { value: 'marketing', label: 'Marketing' },\r\n        { value: 'sales', label: 'Sales' },\r\n      ],\r\n      company2: [\r\n        { value: 'tech', label: 'Technology' },\r\n        { value: 'operations', label: 'Operations' },\r\n      ],\r\n      company3: [\r\n        { value: 'research', label: 'Research' },\r\n        { value: 'development', label: 'Development' },\r\n      ],\r\n    };\r\n\r\n    this.dropdownValues['domain'] = domainOptionsMap[org] || [];\r\n  }\r\n\r\n  private loadProjectOptions(org: string, domain: string): void {\r\n    const projectOptions: SelectOption[] = [\r\n      { value: 'project1', label: 'Project Alpha' },\r\n      { value: 'project2', label: 'Project Beta' },\r\n      { value: 'project3', label: 'Project Gamma' },\r\n    ];\r\n\r\n    this.dropdownValues['project'] = projectOptions;\r\n  }\r\n\r\n  private loadTeamOptions(org: string, domain: string, project: string): void {\r\n    const teamOptions: SelectOption[] = [\r\n      { value: 'team1', label: 'Team Alpha' },\r\n      { value: 'team2', label: 'Team Beta' },\r\n      { value: 'team3', label: 'Team Gamma' },\r\n    ];\r\n\r\n    this.dropdownValues['team'] = teamOptions;\r\n  }\r\n\r\n  // Toolbar is now fixed at bottom center - no drag methods needed\r\n\r\n  // Toolbar is now fixed at bottom center via CSS - no positioning method needed\r\n\r\n  // Agent details dropdown methods\r\n  toggleAgentDetailsDropdown(): void {\r\n    this.isAgentDetailsDropdownOpen = !this.isAgentDetailsDropdownOpen;\r\n  }\r\n\r\n  closeAgentDetailsDropdown(): void {\r\n    this.isAgentDetailsDropdownOpen = false;\r\n  }\r\n\r\n  applyAgentDetails(): void {\r\n    const name = this.agentDetailNameControl.value || '';\r\n    const useCaseDetails = this.agentDetailControl.value || '';\r\n\r\n    // Emit agent details change event\r\n    this.agentDetailsChanged.emit({\r\n      name: name,\r\n      useCaseDetails: useCaseDetails,\r\n    });\r\n\r\n    this.closeAgentDetailsDropdown();\r\n  }\r\n\r\n  cancelAgentDetails(): void {\r\n    this.closeAgentDetailsDropdown();\r\n  }\r\n}\r\n", "<div class=\"canvas-board-container\" [class.execute-mode]=\"isExecuteMode\">\r\n  <!-- Built-in Header Inputs -->\r\n  <div class=\"header-inputs-section\" *ngIf=\"showHeaderInputs\">\r\n    <div class=\"header-inputs-container\">\r\n      <!-- Agent Details Dropdown -->\r\n      <div\r\n        class=\"input-group agent-details-dropdown\"\r\n        *ngIf=\"inputFieldsConfig.agentDetails?.enabled\"\r\n      >\r\n        <div\r\n          class=\"dropdown-container\"\r\n          [class.open]=\"isAgentDetailsDropdownOpen\"\r\n        >\r\n          <!-- Dropdown Toggle -->\r\n          <button\r\n            type=\"button\"\r\n            class=\"dropdown-toggle\"\r\n            [class.open]=\"isAgentDetailsDropdownOpen\"\r\n            (click)=\"toggleAgentDetailsDropdown()\"\r\n            [attr.aria-expanded]=\"isAgentDetailsDropdownOpen\"\r\n          >\r\n            <span>{{\r\n              inputFieldsConfig.agentDetails?.label || \"Agent Details\"\r\n            }}</span>\r\n            <ava-icon\r\n              iconName=\"ChevronDown\"\r\n              [iconSize]=\"16\"\r\n              [style.transform]=\"\r\n                isAgentDetailsDropdownOpen ? 'rotate(180deg)' : 'rotate(0deg)'\r\n              \"\r\n            >\r\n            </ava-icon>\r\n          </button>\r\n\r\n          <!-- Dropdown Content -->\r\n          <div class=\"dropdown-content\" *ngIf=\"isAgentDetailsDropdownOpen\">\r\n            <div class=\"form-fields\">\r\n              <!-- Agent Name Field -->\r\n              <div class=\"field-group\">\r\n                <label class=\"field-label\">Name</label>\r\n                <ava-textbox\r\n                  [placeholder]=\"\r\n                    inputFieldsConfig.agentDetails?.namePlaceholder ||\r\n                    'Enter name'\r\n                  \"\r\n                  [formControl]=\"agentDetailNameControl\"\r\n                  variant=\"primary\"\r\n                  size=\"md\"\r\n                  [fullWidth]=\"true\"\r\n                  class=\"agent-detail-name-field\"\r\n                >\r\n                </ava-textbox>\r\n              </div>\r\n\r\n              <!-- Agent Detail Field -->\r\n              <div class=\"field-group\">\r\n                <label class=\"field-label\">{{\r\n                  inputFieldsConfig.agentDetails?.detailLabel || \"Description\"\r\n                }}</label>\r\n                <ava-textarea\r\n                  [placeholder]=\"\r\n                    inputFieldsConfig.agentDetails?.detailPlaceholder ||\r\n                    'Enter agent description'\r\n                  \"\r\n                  [formControl]=\"agentDetailControl\"\r\n                  [rows]=\"4\"\r\n                  [fullWidth]=\"true\"\r\n                  class=\"agent-detail-field\"\r\n                >\r\n                </ava-textarea>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Action Buttons -->\r\n            <div class=\"dropdown-actions\">\r\n              <ava-button\r\n                label=\"Cancel\"\r\n                variant=\"secondary\"\r\n                [customStyles]=\"{\r\n                  'border-radius': '8px',\r\n                  border:\r\n                    '1px solid var(--Global-colors-Royal-blue-500, #2563EB) !important',\r\n                }\"\r\n                size=\"small\"\r\n                (userClick)=\"cancelAgentDetails()\"\r\n              >\r\n              </ava-button>\r\n              <ava-button\r\n                label=\"Apply\"\r\n                variant=\"primary\"\r\n                [customStyles]=\"{\r\n                  background:\r\n                    'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n                  '--button-effect-color': '33, 90, 214',\r\n                  'border-radius': '8px',\r\n                }\"\r\n                size=\"small\"\r\n                (userClick)=\"applyAgentDetails()\"\r\n              >\r\n              </ava-button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"canvas-action\" *ngIf=\"showLeftActions\">\r\n          <ng-content select=\"[actionLeft]\"></ng-content>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Navigation hints -->\r\n  <div class=\"navigation-hint\" *ngIf=\"navigationHints.length > 0\">\r\n    <span *ngFor=\"let hint of navigationHints; let last = last\">\r\n      {{ hint }}<span *ngIf=\"!last\"> | </span>\r\n    </span>\r\n  </div>\r\n\r\n  <!-- Canvas container -->\r\n  <div\r\n    class=\"canvas-container\"\r\n    #canvasContainer\r\n    [class.show-grid]=\"showGrid\"\r\n    [class.disabled]=\"!mouseInteractionsEnabled\"\r\n    (dragover)=\"onDragOver($event)\"\r\n    (drop)=\"onDrop($event)\"\r\n    (mouseup)=\"onCanvasMouseUp($event)\"\r\n    (mousemove)=\"onCanvasMouseMove($event)\"\r\n    (wheel)=\"onCanvasWheel($event)\"\r\n    (mousedown)=\"onCanvasMouseDown($event)\"\r\n  >\r\n    <!-- Canvas actions placed on the left -->\r\n\r\n    <!-- Canvas Tools Toolbar - fixed at bottom center -->\r\n    <ng-content select=\"[info]\"></ng-content>\r\n\r\n    <!-- Canvas Tools Toolbar - positioned dynamically -->\r\n    <div class=\"canvas-tools-toolbar\" *ngIf=\"showCanvasTools\">\r\n      <!-- Fixed positioning at bottom center -->\r\n\r\n      <!-- Selection Mode -->\r\n      <ava-button\r\n        label=\"\"\r\n        variant=\"secondary\"\r\n        size=\"small\"\r\n        iconName=\"MousePointer2\"\r\n        iconPosition=\"only\"\r\n        [state]=\"canvasMode === 'select' ? 'active' : 'default'\"\r\n        (userClick)=\"setSelectMode()\"\r\n        title=\"Selection Mode\"\r\n      >\r\n      </ava-button>\r\n\r\n      <!-- Pan Mode -->\r\n      <ava-button\r\n        label=\"\"\r\n        variant=\"secondary\"\r\n        size=\"small\"\r\n        iconName=\"Hand\"\r\n        [iconColor]=\"'var(--rgb-brand-primary)'\"\r\n        iconPosition=\"only\"\r\n        [state]=\"canvasMode === 'pan' ? 'active' : 'default'\"\r\n        (userClick)=\"setPanMode()\"\r\n        title=\"Pan Mode\"\r\n      >\r\n      </ava-button>\r\n\r\n      <!-- Zoom In -->\r\n      <ava-button\r\n        label=\"\"\r\n        variant=\"secondary\"\r\n        size=\"small\"\r\n        iconName=\"ZoomIn\"\r\n        iconPosition=\"only\"\r\n        (userClick)=\"zoomIn(); setZoomMode()\"\r\n        title=\"Zoom In\"\r\n      >\r\n      </ava-button>\r\n\r\n      <!-- Zoom Out -->\r\n      <ava-button\r\n        label=\"\"\r\n        variant=\"secondary\"\r\n        size=\"small\"\r\n        iconName=\"ZoomOut\"\r\n        iconPosition=\"only\"\r\n        (userClick)=\"zoomOut(); setZoomMode()\"\r\n        title=\"Zoom Out\"\r\n      >\r\n      </ava-button>\r\n    </div>\r\n\r\n    <!-- Floating Toolbar - positioned in top-right corner -->\r\n    <div class=\"floating-toolbar\" *ngIf=\"showToolbar\">\r\n      <div class=\"black-icons\">\r\n        <ava-button\r\n          *ngIf=\"enableUndo && !isExecuteMode\"\r\n          label=\"\"\r\n          variant=\"secondary\"\r\n          size=\"small\"\r\n          iconName=\"Undo-2\"\r\n          iconPosition=\"only\"\r\n          (userClick)=\"onUndo()\"\r\n          title=\"Undo\"\r\n        >\r\n        </ava-button>\r\n        <ava-button\r\n          *ngIf=\"enableRedo && !isExecuteMode\"\r\n          label=\"\"\r\n          variant=\"secondary\"\r\n          size=\"small\"\r\n          iconName=\"Redo-2\"\r\n          iconPosition=\"only\"\r\n          (userClick)=\"onRedo()\"\r\n          title=\"Redo\"\r\n        >\r\n        </ava-button>\r\n      </div>\r\n\r\n      <!-- Execute Mode Button - Just Play Icon -->\r\n      <ava-button\r\n        *ngIf=\"isExecuteMode\"\r\n        label=\"\"\r\n        variant=\"primary\"\r\n        [customStyles]=\"{\r\n          background:\r\n            'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n          '--button-effect-color': '33, 90, 214',\r\n        }\"\r\n        size=\"small\"\r\n        iconName=\"Play\"\r\n        iconPosition=\"only\"\r\n        (userClick)=\"onPrimaryButtonClick()\"\r\n        title=\"Run Agent\"\r\n      >\r\n      </ava-button>\r\n\r\n      <!-- Normal Mode Button - Execute with Text -->\r\n      <ava-button\r\n        *ngIf=\"!isExecuteMode\"\r\n        [label]=\"primaryButtonText\"\r\n        variant=\"primary\"\r\n        size=\"small\"\r\n        [customStyles]=\"{\r\n          background:\r\n            'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n          '--button-effect-color': '33, 90, 214',\r\n          'border-radius': '8px',\r\n        }\"\r\n        (userClick)=\"onPrimaryButtonClick()\"\r\n        [iconName]=\"primaryButtonIcon ? '' : 'Play'\"\r\n        iconPosition=\"right\"\r\n      >\r\n      </ava-button>\r\n    </div>\r\n    <!-- Execute mode connections - positioned absolutely without viewport transform -->\r\n    <svg\r\n      class=\"connections-layer canvas-edges execute-connections\"\r\n      *ngIf=\"enableConnections && isExecuteMode\"\r\n      [style.position]=\"'absolute'\"\r\n      [style.top]=\"'0'\"\r\n      [style.left]=\"'0'\"\r\n      [style.width]=\"'100%'\"\r\n      [style.height]=\"'100%'\"\r\n      [style.pointer-events]=\"'none'\"\r\n      [style.z-index]=\"'5'\"\r\n    >\r\n      <!-- Static connections for execute mode -->\r\n      <path\r\n        *ngFor=\"let edge of edges\"\r\n        [attr.d]=\"edge.pathData || ''\"\r\n        [attr.id]=\"edge.id\"\r\n        class=\"edge-path\"\r\n        [class.edge-animated]=\"edge.animated\"\r\n        marker-end=\"url(#arrow-execute)\"\r\n      ></path>\r\n\r\n      <!-- Arrow marker definition for execute mode - smaller for short connections -->\r\n      <defs>\r\n        <marker\r\n          id=\"arrow-execute\"\r\n          viewBox=\"0 0 8 8\"\r\n          refX=\"7\"\r\n          refY=\"4\"\r\n          markerWidth=\"6\"\r\n          markerHeight=\"6\"\r\n          orient=\"auto\"\r\n          markerUnits=\"strokeWidth\"\r\n          class=\"edge-marker\"\r\n        >\r\n          <path d=\"M 1 1 L 7 4 L 1 7 z\" class=\"arrow-head\"></path>\r\n        </marker>\r\n      </defs>\r\n    </svg>\r\n\r\n    <!-- Viewport container that applies the transform -->\r\n    <div\r\n      class=\"canvas-viewport\"\r\n      [style.transform]=\"\r\n        isExecuteMode\r\n          ? 'none'\r\n          : 'translate(' +\r\n            viewport.x +\r\n            'px, ' +\r\n            viewport.y +\r\n            'px) scale(' +\r\n            viewport.zoom +\r\n            ')'\r\n      \"\r\n    >\r\n      <!-- All elements in the same transformed container -->\r\n      <div class=\"canvas-content\">\r\n        <!-- Connections between nodes for build mode -->\r\n        <svg\r\n          class=\"connections-layer canvas-edges\"\r\n          *ngIf=\"enableConnections && !isExecuteMode\"\r\n        >\r\n          <!-- Static connections -->\r\n          <path\r\n            *ngFor=\"let edge of edges\"\r\n            [attr.d]=\"edge.pathData || ''\"\r\n            [attr.id]=\"edge.id\"\r\n            class=\"edge-path\"\r\n            [class.edge-animated]=\"edge.animated\"\r\n            marker-end=\"url(#arrow)\"\r\n          ></path>\r\n\r\n          <!-- Arrow marker definition -->\r\n          <defs>\r\n            <marker\r\n              id=\"arrow\"\r\n              viewBox=\"0 0 12 12\"\r\n              refX=\"10\"\r\n              refY=\"6\"\r\n              markerWidth=\"10\"\r\n              markerHeight=\"10\"\r\n              orient=\"auto\"\r\n              markerUnits=\"strokeWidth\"\r\n              class=\"edge-marker\"\r\n            >\r\n              <path d=\"M 2 2 L 10 6 L 2 10 z\" class=\"arrow-head\"></path>\r\n            </marker>\r\n          </defs>\r\n        </svg>\r\n\r\n        <!-- Nodes container -->\r\n        <div class=\"nodes-container canvas-nodes\">\r\n          <!-- Render nodes using content projection with trackBy -->\r\n          <ng-container *ngFor=\"let node of nodes; trackBy: trackByNodeId\">\r\n            <ng-container\r\n              *ngTemplateOutlet=\"\r\n                nodeTemplate;\r\n                context: {\r\n                  $implicit: node,\r\n                  selected: selectedNodeId === node.id,\r\n                  onDelete: onDeleteNode.bind(this),\r\n                  onMove: onNodeMoved.bind(this),\r\n                  onSelect: onNodeSelected.bind(this),\r\n                  onDoubleClick: onNodeDoubleClicked.bind(this),\r\n                  onStartConnection: onStartConnection.bind(this),\r\n                  mouseInteractionsEnabled: mouseInteractionsEnabled,\r\n                  canvasMode: isExecuteMode ? 'execute' : 'build',\r\n                }\r\n              \"\r\n            ></ng-container>\r\n          </ng-container>\r\n        </div>\r\n\r\n        <!-- Temporary connection line - render on top -->\r\n        <svg\r\n          *ngIf=\"tempConnection.isActive && enableConnections\"\r\n          class=\"temp-connection-layer\"\r\n        >\r\n          <path\r\n            [attr.d]=\"getTempConnectionPath()\"\r\n            class=\"temp-connection-path\"\r\n            marker-end=\"url(#arrow)\"\r\n          ></path>\r\n        </svg>\r\n\r\n        <!-- Fallback message -->\r\n        <div *ngIf=\"nodes.length === 0\" class=\"no-nodes-message\">\r\n          {{ fallbackMessage }}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAAA,SAIEA,YAAY,QAUP,eAAe;AACtB,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SACEC,eAAe,EACfC,mBAAmB,EACnBC,oBAAoB,EACpBC,aAAa,QACR,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICiBfC,EAJN,CAAAC,cAAA,cAAiE,cACtC,cAEE,gBACI;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvCH,EAAA,CAAAI,SAAA,sBAWc;IAChBJ,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,cAAyB,gBACI;IAAAD,EAAA,CAAAE,MAAA,GAEzB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACVH,EAAA,CAAAI,SAAA,uBAUe;IAEnBJ,EADE,CAAAG,YAAA,EAAM,EACF;IAIJH,EADF,CAAAC,cAAA,eAA8B,sBAW3B;IADCD,EAAA,CAAAK,UAAA,uBAAAC,iFAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAaF,MAAA,CAAAG,kBAAA,EAAoB;IAAA,EAAC;IAEpCZ,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAC,cAAA,sBAWC;IADCD,EAAA,CAAAK,UAAA,uBAAAQ,iFAAA;MAAAb,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAaF,MAAA,CAAAK,iBAAA,EAAmB;IAAA,EAAC;IAIvCd,EAFI,CAAAG,YAAA,EAAa,EACT,EACF;;;;IA5DEH,EAAA,CAAAe,SAAA,GAGC;IAIDf,EAPA,CAAAgB,UAAA,iBAAAP,MAAA,CAAAQ,iBAAA,CAAAC,YAAA,kBAAAT,MAAA,CAAAQ,iBAAA,CAAAC,YAAA,CAAAC,eAAA,kBAGC,gBAAAV,MAAA,CAAAW,sBAAA,CACqC,mBAGpB;IAQOpB,EAAA,CAAAe,SAAA,GAEzB;IAFyBf,EAAA,CAAAqB,iBAAA,EAAAZ,MAAA,CAAAQ,iBAAA,CAAAC,YAAA,kBAAAT,MAAA,CAAAQ,iBAAA,CAAAC,YAAA,CAAAI,WAAA,mBAEzB;IAEAtB,EAAA,CAAAe,SAAA,EAGC;IAGDf,EANA,CAAAgB,UAAA,iBAAAP,MAAA,CAAAQ,iBAAA,CAAAC,YAAA,kBAAAT,MAAA,CAAAQ,iBAAA,CAAAC,YAAA,CAAAK,iBAAA,+BAGC,gBAAAd,MAAA,CAAAe,kBAAA,CACiC,WACxB,mBACQ;IAYpBxB,EAAA,CAAAe,SAAA,GAIE;IAJFf,EAAA,CAAAgB,UAAA,iBAAAhB,EAAA,CAAAyB,eAAA,KAAAC,GAAA,EAIE;IAQF1B,EAAA,CAAAe,SAAA,EAKE;IALFf,EAAA,CAAAgB,UAAA,iBAAAhB,EAAA,CAAAyB,eAAA,KAAAE,GAAA,EAKE;;;;;IAQV3B,EAAA,CAAAC,cAAA,cAAmD;IACjDD,EAAA,CAAA4B,YAAA,MAA+C;IACjD5B,EAAA,CAAAG,YAAA,EAAM;;;;;;IA3FJH,EATJ,CAAAC,cAAA,cAGC,cAIE,iBAQE;IAFCD,EAAA,CAAAK,UAAA,mBAAAwB,kEAAA;MAAA7B,EAAA,CAAAO,aAAA,CAAAuB,GAAA;MAAA,MAAArB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAsB,0BAAA,EAA4B;IAAA,EAAC;IAGtC/B,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAEJ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACTH,EAAA,CAAAI,SAAA,mBAOW;IACbJ,EAAA,CAAAG,YAAA,EAAS;IAGTH,EAAA,CAAAgC,UAAA,IAAAC,+CAAA,oBAAiE;IAmEnEjC,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAgC,UAAA,IAAAE,+CAAA,kBAAmD;IAGrDlC,EAAA,CAAAG,YAAA,EAAM;;;;IA/FFH,EAAA,CAAAe,SAAA,EAAyC;IAAzCf,EAAA,CAAAmC,WAAA,SAAA1B,MAAA,CAAA2B,0BAAA,CAAyC;IAMvCpC,EAAA,CAAAe,SAAA,EAAyC;IAAzCf,EAAA,CAAAmC,WAAA,SAAA1B,MAAA,CAAA2B,0BAAA,CAAyC;;IAInCpC,EAAA,CAAAe,SAAA,GAEJ;IAFIf,EAAA,CAAAqB,iBAAA,EAAAZ,MAAA,CAAAQ,iBAAA,CAAAC,YAAA,kBAAAT,MAAA,CAAAQ,iBAAA,CAAAC,YAAA,CAAAmB,KAAA,qBAEJ;IAIArC,EAAA,CAAAe,SAAA,EAEC;IAFDf,EAAA,CAAAsC,WAAA,cAAA7B,MAAA,CAAA2B,0BAAA,qCAEC;IAHDpC,EAAA,CAAAgB,UAAA,gBAAe;IASYhB,EAAA,CAAAe,SAAA,EAAgC;IAAhCf,EAAA,CAAAgB,UAAA,SAAAP,MAAA,CAAA2B,0BAAA,CAAgC;IAoErCpC,EAAA,CAAAe,SAAA,EAAqB;IAArBf,EAAA,CAAAgB,UAAA,SAAAP,MAAA,CAAA8B,eAAA,CAAqB;;;;;IApGrDvC,EADF,CAAAC,cAAA,cAA4D,cACrB;IAEnCD,EAAA,CAAAgC,UAAA,IAAAQ,yCAAA,mBAGC;IAoGLxC,EADE,CAAAG,YAAA,EAAM,EACF;;;;IArGCH,EAAA,CAAAe,SAAA,GAA6C;IAA7Cf,EAAA,CAAAgB,UAAA,SAAAP,MAAA,CAAAQ,iBAAA,CAAAC,YAAA,kBAAAT,MAAA,CAAAQ,iBAAA,CAAAC,YAAA,CAAAuB,OAAA,CAA6C;;;;;IA0GtCzC,EAAA,CAAAC,cAAA,WAAoB;IAACD,EAAA,CAAAE,MAAA,UAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAD1CH,EAAA,CAAAC,cAAA,WAA4D;IAC1DD,EAAA,CAAAE,MAAA,GAAU;IAAAF,EAAA,CAAAgC,UAAA,IAAAU,iDAAA,mBAAoB;IAChC1C,EAAA,CAAAG,YAAA,EAAO;;;;;IADLH,EAAA,CAAAe,SAAA,EAAU;IAAVf,EAAA,CAAA2C,kBAAA,MAAAC,OAAA,KAAU;IAAO5C,EAAA,CAAAe,SAAA,EAAW;IAAXf,EAAA,CAAAgB,UAAA,UAAA6B,OAAA,CAAW;;;;;IAFhC7C,EAAA,CAAAC,cAAA,cAAgE;IAC9DD,EAAA,CAAAgC,UAAA,IAAAc,0CAAA,mBAA4D;IAG9D9C,EAAA,CAAAG,YAAA,EAAM;;;;IAHmBH,EAAA,CAAAe,SAAA,EAAoB;IAApBf,EAAA,CAAAgB,UAAA,YAAAP,MAAA,CAAAsC,eAAA,CAAoB;;;;;;IA4BzC/C,EAJF,CAAAC,cAAA,cAA0D,qBAavD;IAFCD,EAAA,CAAAK,UAAA,uBAAA2C,oEAAA;MAAAhD,EAAA,CAAAO,aAAA,CAAA0C,GAAA;MAAA,MAAAxC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAaF,MAAA,CAAAyC,aAAA,EAAe;IAAA,EAAC;IAG/BlD,EAAA,CAAAG,YAAA,EAAa;IAGbH,EAAA,CAAAC,cAAA,qBAUC;IAFCD,EAAA,CAAAK,UAAA,uBAAA8C,oEAAA;MAAAnD,EAAA,CAAAO,aAAA,CAAA0C,GAAA;MAAA,MAAAxC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAaF,MAAA,CAAA2C,UAAA,EAAY;IAAA,EAAC;IAG5BpD,EAAA,CAAAG,YAAA,EAAa;IAGbH,EAAA,CAAAC,cAAA,qBAQC;IAFCD,EAAA,CAAAK,UAAA,uBAAAgD,oEAAA;MAAArD,EAAA,CAAAO,aAAA,CAAA0C,GAAA;MAAA,MAAAxC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAaD,MAAA,CAAA6C,MAAA,EAAQ;MAAA,OAAAtD,EAAA,CAAAW,WAAA,CAAEF,MAAA,CAAA8C,WAAA,EAAa;IAAA,EAAC;IAGvCvD,EAAA,CAAAG,YAAA,EAAa;IAGbH,EAAA,CAAAC,cAAA,qBAQC;IAFCD,EAAA,CAAAK,UAAA,uBAAAmD,oEAAA;MAAAxD,EAAA,CAAAO,aAAA,CAAA0C,GAAA;MAAA,MAAAxC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAaD,MAAA,CAAAgD,OAAA,EAAS;MAAA,OAAAzD,EAAA,CAAAW,WAAA,CAAEF,MAAA,CAAA8C,WAAA,EAAa;IAAA,EAAC;IAI1CvD,EADE,CAAAG,YAAA,EAAa,EACT;;;;IA3CFH,EAAA,CAAAe,SAAA,EAAwD;IAAxDf,EAAA,CAAAgB,UAAA,UAAAP,MAAA,CAAAiD,UAAA,qCAAwD;IAYxD1D,EAAA,CAAAe,SAAA,EAAwC;IAExCf,EAFA,CAAAgB,UAAA,yCAAwC,UAAAP,MAAA,CAAAiD,UAAA,kCAEa;;;;;;IAkCrD1D,EAAA,CAAAC,cAAA,qBASC;IAFCD,EAAA,CAAAK,UAAA,uBAAAsD,iFAAA;MAAA3D,EAAA,CAAAO,aAAA,CAAAqD,GAAA;MAAA,MAAAnD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAaF,MAAA,CAAAoD,MAAA,EAAQ;IAAA,EAAC;IAGxB7D,EAAA,CAAAG,YAAA,EAAa;;;;;;IACbH,EAAA,CAAAC,cAAA,qBASC;IAFCD,EAAA,CAAAK,UAAA,uBAAAyD,iFAAA;MAAA9D,EAAA,CAAAO,aAAA,CAAAwD,GAAA;MAAA,MAAAtD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAaF,MAAA,CAAAuD,MAAA,EAAQ;IAAA,EAAC;IAGxBhE,EAAA,CAAAG,YAAA,EAAa;;;;;;IAIfH,EAAA,CAAAC,cAAA,qBAcC;IAFCD,EAAA,CAAAK,UAAA,uBAAA4D,iFAAA;MAAAjE,EAAA,CAAAO,aAAA,CAAA2D,IAAA;MAAA,MAAAzD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAaF,MAAA,CAAA0D,oBAAA,EAAsB;IAAA,EAAC;IAGtCnE,EAAA,CAAAG,YAAA,EAAa;;;IAXXH,EAAA,CAAAgB,UAAA,iBAAAhB,EAAA,CAAAyB,eAAA,IAAA2C,GAAA,EAIE;;;;;;IAUJpE,EAAA,CAAAC,cAAA,qBAcC;IAHCD,EAAA,CAAAK,UAAA,uBAAAgE,iFAAA;MAAArE,EAAA,CAAAO,aAAA,CAAA+D,IAAA;MAAA,MAAA7D,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAaF,MAAA,CAAA0D,oBAAA,EAAsB;IAAA,EAAC;IAItCnE,EAAA,CAAAG,YAAA,EAAa;;;;IAHXH,EAVA,CAAAgB,UAAA,UAAAP,MAAA,CAAA8D,iBAAA,CAA2B,iBAAAvE,EAAA,CAAAyB,eAAA,IAAAE,GAAA,EAQzB,aAAAlB,MAAA,CAAA+D,iBAAA,eAE0C;;;;;IAxD9CxE,EADF,CAAAC,cAAA,cAAkD,cACvB;IAYvBD,EAXA,CAAAgC,UAAA,IAAAyC,gDAAA,yBASC,IAAAC,gDAAA,yBAWA;IAEH1E,EAAA,CAAAG,YAAA,EAAM;IAqBNH,EAlBA,CAAAgC,UAAA,IAAA2C,gDAAA,yBAcC,IAAAC,gDAAA,yBAkBA;IAEH5E,EAAA,CAAAG,YAAA,EAAM;;;;IA1DCH,EAAA,CAAAe,SAAA,GAAkC;IAAlCf,EAAA,CAAAgB,UAAA,SAAAP,MAAA,CAAAoE,UAAA,KAAApE,MAAA,CAAAqE,aAAA,CAAkC;IAWlC9E,EAAA,CAAAe,SAAA,EAAkC;IAAlCf,EAAA,CAAAgB,UAAA,SAAAP,MAAA,CAAAsE,UAAA,KAAAtE,MAAA,CAAAqE,aAAA,CAAkC;IAcpC9E,EAAA,CAAAe,SAAA,EAAmB;IAAnBf,EAAA,CAAAgB,UAAA,SAAAP,MAAA,CAAAqE,aAAA,CAAmB;IAkBnB9E,EAAA,CAAAe,SAAA,EAAoB;IAApBf,EAAA,CAAAgB,UAAA,UAAAP,MAAA,CAAAqE,aAAA,CAAoB;;;;;;IA6BvB9E,EAAA,CAAAI,SAAA,eAOQ;;;;IAFNJ,EAAA,CAAAmC,WAAA,kBAAA6C,QAAA,CAAAC,QAAA,CAAqC;;;;;;;IAjBzCjF,EAAA,CAAAC,cAAA,cAUC;IAECD,EAAA,CAAAgC,UAAA,IAAAkD,oDAAA,mBAOC;IAIClF,EADF,CAAAC,cAAA,WAAM,iBAWH;IACCD,EAAA,CAAAI,SAAA,eAAwD;IAG9DJ,EAFI,CAAAG,YAAA,EAAS,EACJ,EACH;;;;IA5BJH,EANA,CAAAsC,WAAA,wBAA6B,YACZ,aACC,iBACI,kBACC,0BACQ,gBACV;IAIFtC,EAAA,CAAAe,SAAA,EAAQ;IAARf,EAAA,CAAAgB,UAAA,YAAAP,MAAA,CAAA0E,KAAA,CAAQ;;;;;;IAiDvBnF,EAAA,CAAAI,SAAA,eAOQ;;;;IAFNJ,EAAA,CAAAmC,WAAA,kBAAAiD,QAAA,CAAAH,QAAA,CAAqC;;;;;;;IAVzCjF,EAAA,CAAAC,cAAA,cAGC;IAECD,EAAA,CAAAgC,UAAA,IAAAqD,qDAAA,mBAOC;IAICrF,EADF,CAAAC,cAAA,WAAM,iBAWH;IACCD,EAAA,CAAAI,SAAA,eAA0D;IAGhEJ,EAFI,CAAAG,YAAA,EAAS,EACJ,EACH;;;;IAxBeH,EAAA,CAAAe,SAAA,EAAQ;IAARf,EAAA,CAAAgB,UAAA,YAAAP,MAAA,CAAA0E,KAAA,CAAQ;;;;;IA8BzBnF,EAAA,CAAAsF,kBAAA,GAegB;;;;;IAhBlBtF,EAAA,CAAAuF,uBAAA,GAAiE;IAC/DvF,EAAA,CAAAgC,UAAA,IAAAwD,4DAAA,2BAeC;;;;;;IAdExF,EAAA,CAAAe,SAAA,EAGX;IAAAf,EAHW,CAAAgB,UAAA,qBAAAP,MAAA,CAAAgF,YAAA,CAGX,4BAAAzF,EAAA,CAAA0F,eAAA,IAAAC,GAAA,GAAAC,QAAA,EAAAnF,MAAA,CAAAoF,cAAA,KAAAD,QAAA,CAAAE,EAAA,EAAArF,MAAA,CAAAsF,YAAA,CAAAC,IAAA,CAAAvF,MAAA,GAAAA,MAAA,CAAAwF,WAAA,CAAAD,IAAA,CAAAvF,MAAA,GAAAA,MAAA,CAAAyF,cAAA,CAAAF,IAAA,CAAAvF,MAAA,GAAAA,MAAA,CAAA0F,mBAAA,CAAAH,IAAA,CAAAvF,MAAA,GAAAA,MAAA,CAAA2F,iBAAA,CAAAJ,IAAA,CAAAvF,MAAA,GAAAA,MAAA,CAAA4F,wBAAA,EAAA5F,MAAA,CAAAqE,aAAA,yBAUA;;;;;;IAMI9E,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAI,SAAA,eAIQ;IACVJ,EAAA,CAAAG,YAAA,EAAM;;;;IAJFH,EAAA,CAAAe,SAAA,EAAkC;;;;;;IAOtCf,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAe,SAAA,EACF;IADEf,EAAA,CAAA2C,kBAAA,MAAAlC,MAAA,CAAA6F,eAAA,MACF;;;AD3QR,WAAaC,oBAAoB;EAA3B,MAAOA,oBAAoB;IAiMXC,GAAA;IA9LUC,eAAe;IACfhB,YAAY;IAE1C;IACSiB,KAAK,GAAiB,EAAE;IACxBvB,KAAK,GAAiB,EAAE;IACxBwB,QAAQ,GAAY,IAAI;IACxBC,SAAS,GAAY,KAAK,CAAC,CAAC;IAC5BC,UAAU,GAAY,KAAK,CAAC,CAAC;IAC7BC,iBAAiB,GAAY,IAAI;IACjCC,OAAO,GAAW,GAAG;IACrBC,OAAO,GAAW,CAAC;IACnBC,eAAe,GAAW,SAAS,CAAC,CAAC;IACrCX,eAAe,GAAW,gCAAgC;IAC1DvD,eAAe,GAAa,CACnC,sDAAsD,EACtD,yCAAyC,EACzC,oCAAoC,EACpC,qBAAqB,CACtB;IACQmE,WAAW,GAAY,IAAI;IAC3B3C,iBAAiB,GAAW,SAAS;IACrCC,iBAAiB,GAAW,EAAE;IAC9BK,UAAU,GAAY,IAAI;IAC1BE,UAAU,GAAY,IAAI;IAC1BoC,WAAW,GAAY,IAAI;IAC3BC,eAAe,GAAY,IAAI;IAC/BC,gBAAgB,GAAY,IAAI;IAChCC,aAAa,GAAY,IAAI;IAC7BC,mBAAmB,GAAY,IAAI;IACnCC,kBAAkB,GAAY,IAAI;IAClC1C,aAAa,GAAY,KAAK;IAC9BvC,eAAe,GAAY,IAAI;IAC/B8D,wBAAwB,GAAY,IAAI;IAEjD;IACSoB,gBAAgB,GAAY,KAAK;IACjCxG,iBAAiB,GA6BtB,EAAE;IAEN;IACSyG,gBAAgB,GAAW,EAAE;IAC7BC,mBAAmB,GAAW,EAAE;IAChCC,eAAe,GAKpB;MACFC,GAAG,EAAE,EAAE;MACPC,MAAM,EAAE,EAAE;MACVC,OAAO,EAAE,EAAE;MACXC,IAAI,EAAE;KACP;IAED;IACAC,sBAAsB,GAAG,KAAK;IAC9BC,cAAc,GAAG,gCAAgC;IAEjD;IACA9F,0BAA0B,GAAG,KAAK;IAElC;IACS+F,gBAAgB,GAAG,IAAIzI,WAAW,CAAC,EAAE,CAAC;IAC/C0I,gBAAgB,GAAG,IAAI1I,WAAW,CAAC,YAAY,CAAC;IAChD2I,uBAAuB,GAAG,IAAI3I,WAAW,CAAC,YAAY,CAAC;IAEvD;IACS0B,sBAAsB,GAAG,IAAI1B,WAAW,CAAC,EAAE,CAAC;IAC5C8B,kBAAkB,GAAG,IAAI9B,WAAW,CAAC,EAAE,CAAC;IAEjD;IACA4I,UAAU,GAAG,IAAI5I,WAAW,CAAC,EAAE,CAAC;IAChC6I,aAAa,GAAG,IAAI7I,WAAW,CAAC,EAAE,CAAC;IACnC8I,cAAc,GAAG,IAAI9I,WAAW,CAAC,EAAE,CAAC;IACpC+I,WAAW,GAAG,IAAI/I,WAAW,CAAC,EAAE,CAAC;IAEjC;IACAgJ,cAAc,GAAsC;MAClDb,GAAG,EAAE,CACH;QAAEc,KAAK,EAAE,WAAW;QAAEtG,KAAK,EAAE;MAAW,CAAE,EAC1C;QAAEsG,KAAK,EAAE,UAAU;QAAEtG,KAAK,EAAE;MAAW,CAAE,EACzC;QAAEsG,KAAK,EAAE,UAAU;QAAEtG,KAAK,EAAE;MAAW,CAAE,CAC1C;MACDyF,MAAM,EAAE,EAAE;MACVC,OAAO,EAAE,EAAE;MACXC,IAAI,EAAE;KACP;IAED;IACUY,SAAS,GAAG,IAAIpJ,YAAY,EAAc;IAC1CqJ,WAAW,GAAG,IAAIrJ,YAAY,EAAU;IACxCsJ,SAAS,GAAG,IAAItJ,YAAY,EAGlC;IACMuJ,YAAY,GAAG,IAAIvJ,YAAY,EAAU;IACzCwJ,iBAAiB,GAAG,IAAIxJ,YAAY,EAAU;IAE9CyJ,iBAAiB,GAAG,IAAIzJ,YAAY,EAI1C;IACM0J,iBAAiB,GAAG,IAAI1J,YAAY,EAAc;IAClD2J,aAAa,GAAG,IAAI3J,YAAY,EAGtC;IACM4J,eAAe,GAAG,IAAI5J,YAAY,EAAkB;IACpD6J,UAAU,GAAG,IAAI7J,YAAY,EAAQ;IACrC8J,UAAU,GAAG,IAAI9J,YAAY,EAAQ;IACrC+J,WAAW,GAAG,IAAI/J,YAAY,EAAQ;IACtCgK,oBAAoB,GAAG,IAAIhK,YAAY,EAAQ;IAC/CiK,YAAY,GAAG,IAAIjK,YAAY,EAGrC;IACMkK,gBAAgB,GAAG,IAAIlK,YAAY,EAAU;IAC7CmK,gBAAgB,GAAG,IAAInK,YAAY,EAAU;IAC7CoK,eAAe,GAAG,IAAIpK,YAAY,EAKxC;IACMqK,mBAAmB,GAAG,IAAIrK,YAAY,EAG5C;IAEJ;IACAqG,cAAc,GAAkB,IAAI;IACpCiE,cAAc,GAAmB;MAAEC,QAAQ,EAAE;IAAK,CAAE;IACpDC,oBAAoB,GAAyB,EAAE;IAC/CC,QAAQ,GAAmB;MACzBC,IAAI,EAAE,CAAC;MACPC,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJC,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE,CAAC;MACbC,UAAU,EAAE;KACb;IAED;IACQC,2BAA2B,GAAkB,IAAI;IAEzD;IACQC,OAAO,GAAmD,EAAE;IAC5DC,YAAY,GAAW,CAAC,CAAC;IACzBC,cAAc,GAAW,EAAE;IAC3BC,gBAAgB,GAAY,KAAK;IAEzC;IACAlH,UAAU,GAAmB,QAAQ,CAAC,CAAC;IACvCmH,YAAY,GAAY,IAAI;IAE5B;IACA;IAEA;IAEAC,YAAoBtE,GAAsB;MAAtB,KAAAA,GAAG,GAAHA,GAAG;IAAsB;IAE7CuE,QAAQA,CAAA;MACN;MACA,IAAI,CAACC,gBAAgB,EAAE;MAEvB;MACA,IAAI,CAAC7C,gBAAgB,CAAC8C,YAAY,CAACC,SAAS,CAAEvC,KAAK,IAAI;QACrD,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKwC,SAAS,EAAE;UACzC,IAAI,CAACzB,gBAAgB,CAAC0B,IAAI,CAACzC,KAAK,CAAC;QACnC;MACF,CAAC,CAAC;MAEF;MACA,IAAI,CAACP,gBAAgB,CAAC6C,YAAY,CAACC,SAAS,CAAEvC,KAAK,IAAI;QACrD,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKwC,SAAS,EAAE;UACzC,IAAI,CAACxB,gBAAgB,CAACyB,IAAI,CAACzC,KAAK,CAAC;QACnC;MACF,CAAC,CAAC;IACJ;IAEQqC,gBAAgBA,CAAA;MACtB;MACA,IAAI,IAAI,CAACtD,gBAAgB,EAAE;QACzB,IAAI,CAACS,gBAAgB,CAACkD,QAAQ,CAAC,IAAI,CAAC3D,gBAAgB,EAAE;UACpD4D,SAAS,EAAE;SACZ,CAAC;MACJ;MAEA;MACA,IAAI,IAAI,CAAC5D,gBAAgB,EAAE;QACzB,IAAI,CAACtG,sBAAsB,CAACiK,QAAQ,CAAC,IAAI,CAAC3D,gBAAgB,EAAE;UAC1D4D,SAAS,EAAE;SACZ,CAAC;MACJ;MACA,IAAI,IAAI,CAAC3D,mBAAmB,EAAE;QAC5B,IAAI,CAACnG,kBAAkB,CAAC6J,QAAQ,CAAC,IAAI,CAAC1D,mBAAmB,EAAE;UACzD2D,SAAS,EAAE;SACZ,CAAC;MACJ;MAEA;MACA,IAAI,IAAI,CAAC1D,eAAe,EAAE;QACxB,IAAI,CAACU,UAAU,CAAC+C,QAAQ,CAAC,IAAI,CAACzD,eAAe,CAACC,GAAG,EAAE;UAAEyD,SAAS,EAAE;QAAK,CAAE,CAAC;QACxE,IAAI,CAAC/C,aAAa,CAAC8C,QAAQ,CAAC,IAAI,CAACzD,eAAe,CAACE,MAAM,EAAE;UACvDwD,SAAS,EAAE;SACZ,CAAC;QACF,IAAI,CAAC9C,cAAc,CAAC6C,QAAQ,CAAC,IAAI,CAACzD,eAAe,CAACG,OAAO,EAAE;UACzDuD,SAAS,EAAE;SACZ,CAAC;QACF,IAAI,CAAC7C,WAAW,CAAC4C,QAAQ,CAAC,IAAI,CAACzD,eAAe,CAACI,IAAI,EAAE;UACnDsD,SAAS,EAAE;SACZ,CAAC;MACJ;IACF;IAEA;IACAC,aAAaA,CAACC,KAAa,EAAEC,IAAgB;MAC3C,OAAOA,IAAI,CAAC3F,EAAE;IAChB;IAEA4F,WAAWA,CAACC,OAAY;MACtB;MACA,IACEA,OAAO,CAAC,kBAAkB,CAAC,IAC3BA,OAAO,CAAC,qBAAqB,CAAC,IAC9BA,OAAO,CAAC,iBAAiB,CAAC,EAC1B;QACA,IAAI,CAACX,gBAAgB,EAAE;MACzB;MAEA;MACA,IAAI,CAAC,IAAI,CAACJ,gBAAgB,EAAE;QAC1B,IAAIe,OAAO,CAACjF,KAAK,IAAIiF,OAAO,CAACjF,KAAK,CAACkF,YAAY,EAAE;UAC/CC,OAAO,CAACC,GAAG,CACT,sCAAsC,EACtCH,OAAO,CAACjF,KAAK,CAACkF,YAAY,CAACG,GAAG,CAAEC,CAAM,KAAM;YAC1ClG,EAAE,EAAEkG,CAAC,CAAClG,EAAE;YACRmG,QAAQ,EAAED,CAAC,CAACC;WACb,CAAC,CAAC,CACJ;UACD,IAAI,CAACvF,KAAK,GAAG,CAAC,GAAGiF,OAAO,CAACjF,KAAK,CAACkF,YAAY,CAAC;QAC9C;QACA,IAAID,OAAO,CAACxG,KAAK,IAAIwG,OAAO,CAACxG,KAAK,CAACyG,YAAY,EAAE;UAC/CC,OAAO,CAACC,GAAG,CACT,sCAAsC,EACtCH,OAAO,CAACxG,KAAK,CAACyG,YAAY,CAACG,GAAG,CAAEG,CAAM,KAAM;YAC1CpG,EAAE,EAAEoG,CAAC,CAACpG,EAAE;YACRqG,MAAM,EAAED,CAAC,CAACC,MAAM;YAChBC,MAAM,EAAEF,CAAC,CAACE;WACX,CAAC,CAAC,CACJ;UACD,IAAI,CAACjH,KAAK,GAAG,CAAC,GAAGwG,OAAO,CAACxG,KAAK,CAACyG,YAAY,CAAC;QAC9C;QAEA;QACA,IAAID,OAAO,CAACjF,KAAK,EAAE;UACjBmF,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;UACzD,IAAI,CAACO,0BAA0B,EAAE;UACjC,IAAI,CAACC,eAAe,EAAE;UACtB;UACAC,UAAU,CAAC,MAAM,IAAI,CAACF,0BAA0B,EAAE,EAAE,CAAC,CAAC;QACxD;QAEA,IAAIV,OAAO,CAACxG,KAAK,EAAE;UACjB0G,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;UACzD,IAAI,CAACQ,eAAe,EAAE;QACxB;MACF;MAEA;MACA,IAAIX,OAAO,CAAC,eAAe,CAAC,EAAE;QAC5B;QACA,IAAI,CAACU,0BAA0B,EAAE;MACnC;MAEA,IAAIV,OAAO,CAAC,0BAA0B,CAAC,EAAE;QACvC,IAAI,CAACtF,wBAAwB,GAC3BsF,OAAO,CAAC,0BAA0B,CAAC,CAACC,YAAY;MACpD;IACF;IAEAY,eAAeA,CAAA;MACb,IAAI,CAACC,qBAAqB,EAAE;MAC5B;MACAC,qBAAqB,CAAC,MAAK;QACzB,IAAI,CAACL,0BAA0B,EAAE;QACjC;QACA,IAAI,CAACM,aAAa,EAAE;QACpB;QACA;QACA,IAAI,CAACzJ,aAAa,EAAE;MACtB,CAAC,CAAC;IACJ;IAEA0J,WAAWA,CAAA;MACT;MACAC,QAAQ,CAACC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACC,aAAa,CAAC;MAE3D;MACA,IAAI,IAAI,CAACvC,2BAA2B,EAAE;QACpCwC,oBAAoB,CAAC,IAAI,CAACxC,2BAA2B,CAAC;QACtD,IAAI,CAACA,2BAA2B,GAAG,IAAI;MACzC;IACF;IAEQiC,qBAAqBA,CAAA;MAC3B,MAAMQ,OAAO,GAAG,IAAI,CAACxG,eAAe,EAAEyG,aAAa;MACnD,IAAID,OAAO,EAAE;QACXJ,QAAQ,CAACM,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACJ,aAAa,CAAC/G,IAAI,CAAC,IAAI,CAAC,CAAC;QAEnEiH,OAAO,CAACE,gBAAgB,CAAC,SAAS,EAAE,MAAK;UACvC,IAAI,IAAI,CAAClD,QAAQ,CAACI,UAAU,EAAE;YAC5B,IAAI,CAACgC,0BAA0B,EAAE;UACnC;QACF,CAAC,CAAC;MACJ;IACF;IAEQU,aAAa,GAAIK,KAAoB,IAAU;MACrD,IAAIA,KAAK,CAACC,GAAG,KAAK,GAAG,EAAE;QACrB;QACA,MAAMjB,MAAM,GAAGgB,KAAK,CAAChB,MAAqB;QAC1C,MAAMkB,YAAY,GAChBlB,MAAM,CAACmB,OAAO,KAAK,OAAO,IAC1BnB,MAAM,CAACmB,OAAO,KAAK,UAAU,IAC7BnB,MAAM,CAACoB,eAAe,KAAK,MAAM,IACjCpB,MAAM,CAACqB,OAAO,CAAC,aAAa,CAAC,IAC7BrB,MAAM,CAACqB,OAAO,CAAC,cAAc,CAAC,IAC9BrB,MAAM,CAACqB,OAAO,CAAC,aAAa,CAAC,IAC7BrB,MAAM,CAACqB,OAAO,CAAC,kBAAkB,CAAC,IAClCrB,MAAM,CAACqB,OAAO,CAAC,aAAa,CAAC,IAC7BrB,MAAM,CAACqB,OAAO,CAAC,UAAU,CAAC,IAC1BrB,MAAM,CAACqB,OAAO,CAAC,OAAO,CAAC;QAEzB,IAAI,CAACH,YAAY,EAAE;UACjB,IAAI,CAACI,aAAa,EAAE;UACpBN,KAAK,CAACO,cAAc,EAAE;QACxB;MACF;IACF,CAAC;IAEDD,aAAaA,CAAA;MACX,IAAI,CAACzD,QAAQ,GAAG;QACdC,IAAI,EAAE,CAAC;QACPC,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE,CAAC;QACJC,UAAU,EAAE,KAAK;QACjBC,UAAU,EAAE,CAAC;QACbC,UAAU,EAAE;OACb;MAED,IAAI,CAAC8B,0BAA0B,EAAE;MACjC,IAAI,CAACjD,eAAe,CAACgC,IAAI,CAAC,IAAI,CAACnB,QAAQ,CAAC;MACxC,IAAI,CAACzD,GAAG,CAACoH,aAAa,EAAE;IAC1B;IAEAC,UAAUA,CAACT,KAAgB;MACzB,IAAI,CAAC,IAAI,CAACtG,iBAAiB,IAAI,CAAC,IAAI,CAACT,wBAAwB,EAAE;MAC/D+G,KAAK,CAACO,cAAc,EAAE;MACtB,IAAIP,KAAK,CAACU,YAAY,EAAE;QACtBV,KAAK,CAACU,YAAY,CAACC,UAAU,GAAG,MAAM;MACxC;IACF;IAEAC,MAAMA,CAACZ,KAAgB;MACrB,IAAI,CAAC,IAAI,CAAC/G,wBAAwB,EAAE;MACpC+G,KAAK,CAACO,cAAc,EAAE;MAEtB;MACA,IAAI,CAACM,uBAAuB,EAAE;MAE9B,MAAMC,YAAY,GAChB,IAAI,CAACzH,eAAe,CAACyG,aAAa,CAACiB,qBAAqB,EAAE;MAC5D,MAAMlC,QAAQ,GAAG;QACf9B,CAAC,EACC,CAACiD,KAAK,CAACgB,OAAO,GAAGF,YAAY,CAACG,IAAI,GAAG,IAAI,CAACpE,QAAQ,CAACE,CAAC,IACpD,IAAI,CAACF,QAAQ,CAACC,IAAI;QACpBE,CAAC,EACC,CAACgD,KAAK,CAACkB,OAAO,GAAGJ,YAAY,CAACK,GAAG,GAAG,IAAI,CAACtE,QAAQ,CAACG,CAAC,IACnD,IAAI,CAACH,QAAQ,CAACC;OACjB;MAED,MAAMsE,YAAY,GAAG;QACnBrE,CAAC,EAAEsE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEzC,QAAQ,CAAC9B,CAAC,CAAC;QAC1BC,CAAC,EAAEqE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEzC,QAAQ,CAAC7B,CAAC;OAC1B;MAED,IAAI,CAACjB,aAAa,CAACiC,IAAI,CAAC;QAAEgC,KAAK;QAAEnB,QAAQ,EAAEuC;MAAY,CAAE,CAAC;IAC5D;IAEAtI,cAAcA,CAACyI,MAAc;MAC3B,IAAI,CAAC9I,cAAc,GAAG8I,MAAM;MAC5B,IAAI,CAAC5F,YAAY,CAACqC,IAAI,CAACuD,MAAM,CAAC;IAChC;IAEAxI,mBAAmBA,CAACwI,MAAc;MAChC,IAAI,CAAC3F,iBAAiB,CAACoC,IAAI,CAACuD,MAAM,CAAC;IACrC;IAEA1I,WAAWA,CAAC2I,IAGX;MACC;MACA,IAAI,CAACX,uBAAuB,EAAE;MAE9B;MACA,MAAMY,SAAS,GAAG,IAAI,CAACnI,KAAK,CAACoI,SAAS,CAAErD,IAAI,IAAKA,IAAI,CAAC3F,EAAE,KAAK8I,IAAI,CAACD,MAAM,CAAC;MACzE,IAAIE,SAAS,KAAK,CAAC,CAAC,EAAE;QACpB,IAAI,CAACnI,KAAK,CAACmI,SAAS,CAAC,GAAG;UACtB,GAAG,IAAI,CAACnI,KAAK,CAACmI,SAAS,CAAC;UACxB5C,QAAQ,EAAE2C,IAAI,CAAC3C;SAChB;MACH;MAEA;MACAJ,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE8C,IAAI,CAACD,MAAM,CAAC;MACpE,IAAI,CAACrC,eAAe,EAAE;MAEtB,IAAI,CAACxD,SAAS,CAACsC,IAAI,CAACwD,IAAI,CAAC;MACzB,IAAI,CAACvC,0BAA0B,EAAE;MAEjC;MACA,IAAI,CAAC7F,GAAG,CAACoH,aAAa,EAAE;IAC1B;IAEAxH,iBAAiBA,CAACwI,IAIjB;MACC,IAAI,CAAC,IAAI,CAAC9H,iBAAiB,EAAE;MAE7B,MAAM2E,IAAI,GAAG,IAAI,CAAC/E,KAAK,CAACqI,IAAI,CAAE/C,CAAC,IAAKA,CAAC,CAAClG,EAAE,KAAK8I,IAAI,CAACD,MAAM,CAAC;MACzD,IAAI,CAAClD,IAAI,EAAE;MAEX;MACA,MAAMuD,UAAU,GACd,IAAI,CAACvI,eAAe,CAACyG,aAAa,CAACiB,qBAAqB,EAAE;MAE5D;MACA,MAAMc,MAAM,GACV,CAACL,IAAI,CAACxB,KAAK,CAACgB,OAAO,GAAGY,UAAU,CAACX,IAAI,GAAG,IAAI,CAACpE,QAAQ,CAACE,CAAC,IACvD,IAAI,CAACF,QAAQ,CAACC,IAAI;MACpB,MAAMgF,MAAM,GACV,CAACN,IAAI,CAACxB,KAAK,CAACkB,OAAO,GAAGU,UAAU,CAACT,GAAG,GAAG,IAAI,CAACtE,QAAQ,CAACG,CAAC,IACtD,IAAI,CAACH,QAAQ,CAACC,IAAI;MAEpB;MACA,IAAIiF,OAAO,GAAGF,MAAM;MACpB,IAAIG,OAAO,GAAGF,MAAM;MAEpB;MACA,MAAMG,gBAAgB,GAAG,IAAI,CAACrF,oBAAoB,CAAC4E,IAAI,CAACD,MAAM,CAAC;MAC/D,IAAIU,gBAAgB,EAAE;QACpB;QACA,MAAMC,WAAW,GAAGzC,QAAQ,CAAC0C,aAAa,CACxC,kBAAkBX,IAAI,CAACD,MAAM,IAAI,CACnB;QAChB,IAAIW,WAAW,EAAE;UACf,MAAME,IAAI,GAAGF,WAAW,CAACnB,qBAAqB,EAAE;UAChD,MAAMsB,KAAK,GACT,CAACD,IAAI,CAACnB,IAAI,GAAGW,UAAU,CAACX,IAAI,GAAG,IAAI,CAACpE,QAAQ,CAACE,CAAC,IAAI,IAAI,CAACF,QAAQ,CAACC,IAAI;UACtE,MAAMwF,KAAK,GACT,CAACF,IAAI,CAACjB,GAAG,GAAGS,UAAU,CAACT,GAAG,GAAG,IAAI,CAACtE,QAAQ,CAACG,CAAC,IAAI,IAAI,CAACH,QAAQ,CAACC,IAAI;UACpE,MAAMyF,SAAS,GAAGH,IAAI,CAACI,KAAK,GAAG,IAAI,CAAC3F,QAAQ,CAACC,IAAI;UACjD,MAAM2F,UAAU,GAAGL,IAAI,CAACM,MAAM,GAAG,IAAI,CAAC7F,QAAQ,CAACC,IAAI;UAEnD;UACA,MAAM6F,SAAS,GAAGd,MAAM,GAAGQ,KAAK;UAChC,MAAMO,SAAS,GAAGd,MAAM,GAAGQ,KAAK;UAEhC;UACA,IAAIK,SAAS,IAAI,CAAC,EAAE;YAClB;YACAZ,OAAO,GAAGM,KAAK;YACfL,OAAO,GAAGX,IAAI,CAACC,GAAG,CAACgB,KAAK,EAAEjB,IAAI,CAACwB,GAAG,CAACP,KAAK,GAAGG,UAAU,EAAEX,MAAM,CAAC,CAAC;UACjE,CAAC,MAAM,IAAIa,SAAS,IAAIJ,SAAS,EAAE;YACjC;YACAR,OAAO,GAAGM,KAAK,GAAGE,SAAS;YAC3BP,OAAO,GAAGX,IAAI,CAACC,GAAG,CAACgB,KAAK,EAAEjB,IAAI,CAACwB,GAAG,CAACP,KAAK,GAAGG,UAAU,EAAEX,MAAM,CAAC,CAAC;UACjE,CAAC,MAAM,IAAIc,SAAS,IAAI,CAAC,EAAE;YACzB;YACAb,OAAO,GAAGV,IAAI,CAACC,GAAG,CAACe,KAAK,EAAEhB,IAAI,CAACwB,GAAG,CAACR,KAAK,GAAGE,SAAS,EAAEV,MAAM,CAAC,CAAC;YAC9DG,OAAO,GAAGM,KAAK;UACjB,CAAC,MAAM,IAAIM,SAAS,IAAIH,UAAU,EAAE;YAClC;YACAV,OAAO,GAAGV,IAAI,CAACC,GAAG,CAACe,KAAK,EAAEhB,IAAI,CAACwB,GAAG,CAACR,KAAK,GAAGE,SAAS,EAAEV,MAAM,CAAC,CAAC;YAC9DG,OAAO,GAAGM,KAAK,GAAGG,UAAU;UAC9B;QACF;MACF;MAEA,IAAI,CAAC/F,cAAc,GAAG;QACpBC,QAAQ,EAAE,IAAI;QACdmG,YAAY,EAAEtB,IAAI,CAACD,MAAM;QACzBwB,gBAAgB,EAAEvB,IAAI,CAACwB,UAAU;QACjCC,OAAO,EAAElB,OAAO;QAChBmB,OAAO,EAAElB,OAAO;QAChBmB,OAAO,EAAEtB,MAAM;QACfuB,OAAO,EAAEtB;OACV;MAED,IAAI,CAACjG,iBAAiB,CAACmC,IAAI,CAACwD,IAAI,CAAC;IACnC;IAEA7I,YAAYA,CAAC4I,MAAc;MACzB;MACA,IAAI,CAACV,uBAAuB,EAAE;MAE9B;MACA,IAAI,CAACvH,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC+J,MAAM,CAAEhF,IAAI,IAAKA,IAAI,CAAC3F,EAAE,KAAK6I,MAAM,CAAC;MAE5D;MACA,IAAI,CAACxJ,KAAK,GAAG,IAAI,CAACA,KAAK,CAACsL,MAAM,CAC3BC,IAAI,IAAKA,IAAI,CAACvE,MAAM,KAAKwC,MAAM,IAAI+B,IAAI,CAACtE,MAAM,KAAKuC,MAAM,CAC3D;MAED,IAAI,CAAC9F,WAAW,CAACuC,IAAI,CAACuD,MAAM,CAAC;MAC7B,IAAI,IAAI,CAAC9I,cAAc,KAAK8I,MAAM,EAAE;QAClC,IAAI,CAAC9I,cAAc,GAAG,IAAI;MAC5B;MAEA,IAAI,CAACwG,0BAA0B,EAAE;IACnC;IAEAsE,iBAAiBA,CAACvD,KAAiB;MACjC;MACA,IAAI,IAAI,CAACnD,QAAQ,CAACI,UAAU,IAAI,IAAI,CAACzD,SAAS,EAAE;QAC9C,MAAMgK,MAAM,GAAGxD,KAAK,CAACgB,OAAO,GAAG,IAAI,CAACnE,QAAQ,CAACK,UAAU;QACvD,MAAMuG,MAAM,GAAGzD,KAAK,CAACkB,OAAO,GAAG,IAAI,CAACrE,QAAQ,CAACM,UAAU;QAEvD,IAAI,CAACN,QAAQ,CAACE,CAAC,IAAIyG,MAAM;QACzB,IAAI,CAAC3G,QAAQ,CAACG,CAAC,IAAIyG,MAAM;QAEzB,IAAI,CAAC5G,QAAQ,CAACK,UAAU,GAAG8C,KAAK,CAACgB,OAAO;QACxC,IAAI,CAACnE,QAAQ,CAACM,UAAU,GAAG6C,KAAK,CAACkB,OAAO;QAExC,IAAI,CAAC9H,GAAG,CAACoH,aAAa,EAAE;QACxB;MACF;MAEA,IAAI,CAAC,IAAI,CAAC9D,cAAc,CAACC,QAAQ,EAAE;MAEnC;MACA,MAAMiF,UAAU,GACd,IAAI,CAACvI,eAAe,CAACyG,aAAa,CAACiB,qBAAqB,EAAE;MAC5D,MAAMoC,OAAO,GACX,CAACnD,KAAK,CAACgB,OAAO,GAAGY,UAAU,CAACX,IAAI,GAAG,IAAI,CAACpE,QAAQ,CAACE,CAAC,IAAI,IAAI,CAACF,QAAQ,CAACC,IAAI;MAC1E,MAAMsG,OAAO,GACX,CAACpD,KAAK,CAACkB,OAAO,GAAGU,UAAU,CAACT,GAAG,GAAG,IAAI,CAACtE,QAAQ,CAACG,CAAC,IAAI,IAAI,CAACH,QAAQ,CAACC,IAAI;MAEzE,IAAI,CAACJ,cAAc,GAAG;QACpB,GAAG,IAAI,CAACA,cAAc;QACtByG,OAAO;QACPC;OACD;MAED,IAAI,CAAChK,GAAG,CAACoH,aAAa,EAAE;IAC1B;IAEAkD,aAAaA,CAAC1D,KAAiB;MAC7B,IAAI,CAAC,IAAI,CAACvG,UAAU,IAAI,CAAC,IAAI,CAACR,wBAAwB,EAAE;MACxD+G,KAAK,CAACO,cAAc,EAAE;MAEtB,MAAMoD,KAAK,GAAG,CAAC3D,KAAK,CAACyD,MAAM;MAC3B,MAAMG,SAAS,GAAG,KAAK;MACvB,MAAMC,OAAO,GAAGxC,IAAI,CAACC,GAAG,CACtB,IAAI,CAAC3H,OAAO,EACZ0H,IAAI,CAACwB,GAAG,CAAC,IAAI,CAACjJ,OAAO,EAAE,IAAI,CAACiD,QAAQ,CAACC,IAAI,GAAG6G,KAAK,GAAGC,SAAS,CAAC,CAC/D;MAED,IAAIC,OAAO,KAAK,IAAI,CAAChH,QAAQ,CAACC,IAAI,EAAE;QAClC,MAAMsF,IAAI,GAAG,IAAI,CAAC/I,eAAe,CAACyG,aAAa,CAACiB,qBAAqB,EAAE;QACvE,MAAMc,MAAM,GAAG7B,KAAK,CAACgB,OAAO,GAAGoB,IAAI,CAACnB,IAAI;QACxC,MAAMa,MAAM,GAAG9B,KAAK,CAACkB,OAAO,GAAGkB,IAAI,CAACjB,GAAG;QAEvC,MAAM2C,SAAS,GAAGD,OAAO,GAAG,IAAI,CAAChH,QAAQ,CAACC,IAAI;QAC9C,MAAMiH,IAAI,GAAGlC,MAAM,GAAG,CAACA,MAAM,GAAG,IAAI,CAAChF,QAAQ,CAACE,CAAC,IAAI+G,SAAS;QAC5D,MAAME,IAAI,GAAGlC,MAAM,GAAG,CAACA,MAAM,GAAG,IAAI,CAACjF,QAAQ,CAACG,CAAC,IAAI8G,SAAS;QAE5D,IAAI,CAACjH,QAAQ,CAACC,IAAI,GAAG+G,OAAO;QAC5B,IAAI,CAAChH,QAAQ,CAACE,CAAC,GAAGgH,IAAI;QACtB,IAAI,CAAClH,QAAQ,CAACG,CAAC,GAAGgH,IAAI;QAEtB,IAAI,CAAC/E,0BAA0B,EAAE;QACjC,IAAI,CAACjD,eAAe,CAACgC,IAAI,CAAC,IAAI,CAACnB,QAAQ,CAAC;QACxC,IAAI,CAACzD,GAAG,CAACoH,aAAa,EAAE;MAC1B;IACF;IAEAyD,iBAAiBA,CAACjE,KAAiB;MACjC,IAAI,CAAC,IAAI,CAACxG,SAAS,IAAI,CAAC,IAAI,CAACP,wBAAwB,EAAE;MAEvD;MACA;MACA;MACA;MACA,IACE+G,KAAK,CAACkE,MAAM,KAAK,CAAC,IACjBlE,KAAK,CAACkE,MAAM,KAAK,CAAC,IAAIlE,KAAK,CAACmE,MAAO,IACnCnE,KAAK,CAACkE,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC5N,UAAU,KAAK,KAAM,EACjD;QACA,IAAI,CAACuG,QAAQ,CAACI,UAAU,GAAG,IAAI;QAC/B,IAAI,CAACJ,QAAQ,CAACK,UAAU,GAAG8C,KAAK,CAACgB,OAAO;QACxC,IAAI,CAACnE,QAAQ,CAACM,UAAU,GAAG6C,KAAK,CAACkB,OAAO;QAExC;QACA,IAAI,IAAI,CAAC7H,eAAe,EAAE;UACxB,IAAI,CAACA,eAAe,CAACyG,aAAa,CAACsE,KAAK,CAACC,MAAM,GAAG,UAAU;QAC9D;QAEArE,KAAK,CAACO,cAAc,EAAE;MACxB;IACF;IAEA+D,eAAeA,CAACtE,KAAiB;MAC/B,IAAI,CAACnD,QAAQ,CAACI,UAAU,GAAG,KAAK;MAEhC;MACA,IAAI,IAAI,CAAC5D,eAAe,EAAE;QACxB,IAAI,CAACA,eAAe,CAACyG,aAAa,CAACsE,KAAK,CAACC,MAAM,GAC7C,IAAI,CAAC/N,UAAU,KAAK,KAAK,GAAG,MAAM,GAAG,SAAS;MAClD;MAEA,IAAI,CAAC,IAAI,CAACoG,cAAc,CAACC,QAAQ,IAAI,CAAC,IAAI,CAACjD,iBAAiB,EAAE;MAE9D,MAAMsF,MAAM,GAAGgB,KAAK,CAAChB,MAAqB;MAE1C;MACA,MAAMkD,WAAW,GAAGlD,MAAM,CAACqB,OAAO,CAAC,gBAAgB,CAAC;MAEpD,IAAI6B,WAAW,IAAI,IAAI,CAACxF,cAAc,CAACoG,YAAY,EAAE;QACnD,MAAMA,YAAY,GAAG,IAAI,CAACpG,cAAc,CAACoG,YAAY;QACrD,MAAMyB,YAAY,GAAGrC,WAAW,CAACsC,YAAY,CAAC,cAAc,CAAC;QAE7D;QACA,IAAID,YAAY,IAAIzB,YAAY,KAAKyB,YAAY,EAAE;UACjD;UACA,MAAME,kBAAkB,GAAG,IAAI,CAAC1M,KAAK,CAAC4J,IAAI,CACvC2B,IAAI,IACFA,IAAI,CAACvE,MAAM,KAAK+D,YAAY,IAAIQ,IAAI,CAACtE,MAAM,KAAKuF,YAAY,IAC5DjB,IAAI,CAACvE,MAAM,KAAKwF,YAAY,IAAIjB,IAAI,CAACtE,MAAM,KAAK8D,YAAa,CACjE;UAED,IAAI,CAAC2B,kBAAkB,EAAE;YACvB;YACA,IAAI,CAAC5D,uBAAuB,EAAE;YAE9B,MAAM6D,OAAO,GAAe;cAC1BhM,EAAE,EAAE,GAAGoK,YAAY,IAAIyB,YAAY,EAAE;cACrCxF,MAAM,EAAE+D,YAAY;cACpB9D,MAAM,EAAEuF,YAAY;cACpB1M,QAAQ,EAAE,KAAK,CAAE;aAClB;YAED;YACA,IAAI,CAACE,KAAK,CAAC4M,IAAI,CAACD,OAAO,CAAC;YAExB,IAAI,CAAC5I,iBAAiB,CAACkC,IAAI,CAAC0G,OAAO,CAAC;UACtC;QACF;MACF;MAEA,IAAI,CAAChI,cAAc,GAAG;QAAEC,QAAQ,EAAE;MAAK,CAAE;IAC3C;IAEAsC,0BAA0BA,CAAA;MACxB,IAAI,IAAI,CAAC7B,2BAA2B,EAAE;QACpCwC,oBAAoB,CAAC,IAAI,CAACxC,2BAA2B,CAAC;MACxD;MAEA;MACA,IAAI,CAACA,2BAA2B,GAAGkC,qBAAqB,CAAC,MAAK;QAC5D,IAAI,CAAC1C,oBAAoB,GAAG,EAAE;QAE9B,KAAK,MAAMyB,IAAI,IAAI,IAAI,CAAC/E,KAAK,EAAE;UAC7B,MAAM4I,WAAW,GAAGzC,QAAQ,CAAC0C,aAAa,CACxC,kBAAkB9D,IAAI,CAAC3F,EAAE,IAAI,CACf;UAEhB;UACA,IAAI2J,KAAa,EAAEC,KAAa,EAAEC,SAAiB,EAAEE,UAAkB;UAEvE,IAAIP,WAAW,EAAE;YACf;YACA,MAAME,IAAI,GAAGF,WAAW,CAACnB,qBAAqB,EAAE;YAChD,MAAMa,UAAU,GACd,IAAI,CAACvI,eAAe,EAAEyG,aAAa,CAACiB,qBAAqB,EAAE;YAC7D,IAAI,CAACa,UAAU,EAAE;YAEjB;YACA,IAAI,IAAI,CAAClK,aAAa,EAAE;cACtB;cACA2K,KAAK,GAAGD,IAAI,CAACnB,IAAI,GAAGW,UAAU,CAACX,IAAI;cACnCqB,KAAK,GAAGF,IAAI,CAACjB,GAAG,GAAGS,UAAU,CAACT,GAAG;cACjCoB,SAAS,GAAGH,IAAI,CAACI,KAAK;cACtBC,UAAU,GAAGL,IAAI,CAACM,MAAM;YAC1B,CAAC,MAAM;cACL;cACAL,KAAK,GACH,CAACD,IAAI,CAACnB,IAAI,GAAGW,UAAU,CAACX,IAAI,GAAG,IAAI,CAACpE,QAAQ,CAACE,CAAC,IAC9C,IAAI,CAACF,QAAQ,CAACC,IAAI;cACpBwF,KAAK,GACH,CAACF,IAAI,CAACjB,GAAG,GAAGS,UAAU,CAACT,GAAG,GAAG,IAAI,CAACtE,QAAQ,CAACG,CAAC,IAC5C,IAAI,CAACH,QAAQ,CAACC,IAAI;cACpByF,SAAS,GAAGH,IAAI,CAACI,KAAK,GAAG,IAAI,CAAC3F,QAAQ,CAACC,IAAI;cAC3C2F,UAAU,GAAGL,IAAI,CAACM,MAAM,GAAG,IAAI,CAAC7F,QAAQ,CAACC,IAAI;YAC/C;UACF,CAAC,MAAM;YACL;YACAuF,KAAK,GAAGhE,IAAI,CAACQ,QAAQ,CAAC9B,CAAC;YACvBuF,KAAK,GAAGjE,IAAI,CAACQ,QAAQ,CAAC7B,CAAC;YACvB;YACAuF,SAAS,GAAG,IAAI,CAAC7K,aAAa,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;YAC1C+K,UAAU,GAAG,IAAI,CAAC/K,aAAa,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;UAC7C;UAEA,IAAI,IAAI,CAACA,aAAa,EAAE;YACtB;YACA,MAAMkN,OAAO,GAAGvC,KAAK,GAAGE,SAAS,GAAG,CAAC;YACrC,MAAMsC,OAAO,GAAGvC,KAAK,GAAGG,UAAU,GAAG,CAAC;YAEtC;YACA,IAAI,CAAC7F,oBAAoB,CAACyB,IAAI,CAAC3F,EAAE,CAAC,GAAG;cACnCyI,GAAG,EAAE;gBAAEpE,CAAC,EAAE6H,OAAO;gBAAE5H,CAAC,EAAE6H;cAAO,CAAE;cAC/BC,KAAK,EAAE;gBAAE/H,CAAC,EAAE6H,OAAO;gBAAE5H,CAAC,EAAE6H;cAAO,CAAE;cACjCE,MAAM,EAAE;gBAAEhI,CAAC,EAAE6H,OAAO;gBAAE5H,CAAC,EAAE6H;cAAO,CAAE;cAClC5D,IAAI,EAAE;gBAAElE,CAAC,EAAE6H,OAAO;gBAAE5H,CAAC,EAAE6H;cAAO;aAC/B;YAEDpG,OAAO,CAACC,GAAG,CACT,qBAAqBL,IAAI,CAAC3F,EAAE,YAAYkM,OAAO,KAAKC,OAAO,GAAG,CAC/D;UACH,CAAC,MAAM;YACL;YACA;YACA,MAAMG,WAAW,GAAG3C,KAAK,GAAG,EAAE,CAAC,CAAC;YAChC,MAAMwC,OAAO,GAAGvC,KAAK,GAAGG,UAAU,GAAG,CAAC,CAAC,CAAC;YAExC;YACA,IAAI,CAAC7F,oBAAoB,CAACyB,IAAI,CAAC3F,EAAE,CAAC,GAAG;cACnCyI,GAAG,EAAE;gBAAEpE,CAAC,EAAEiI,WAAW;gBAAEhI,CAAC,EAAEsF;cAAK,CAAE;cACjCwC,KAAK,EAAE;gBAAE/H,CAAC,EAAEsF,KAAK,GAAGE,SAAS;gBAAEvF,CAAC,EAAE6H;cAAO,CAAE;cAC3CE,MAAM,EAAE;gBAAEhI,CAAC,EAAEiI,WAAW;gBAAEhI,CAAC,EAAEsF,KAAK,GAAGG;cAAU,CAAE;cACjDxB,IAAI,EAAE;gBAAElE,CAAC,EAAEsF,KAAK;gBAAErF,CAAC,EAAE6H;cAAO;aAC7B;UACH;QACF;QAEA;QACA,IAAI,CAACzL,GAAG,CAACoH,aAAa,EAAE;QACxB,IAAI,CAACpD,2BAA2B,GAAG,IAAI;MACzC,CAAC,CAAC;IACJ;IAEA;IACA;IAEQ6H,iBAAiBA,CACvBC,UAAsB,EACtBC,UAAsB;MAEtB;MACA,IAAIlC,OAAe,EAAEC,OAAe,EAAEC,OAAe,EAAEC,OAAe;MAEtE,IAAI,IAAI,CAAC1L,aAAa,EAAE;QACtB;QACAuL,OAAO,GAAGiC,UAAU,CAACrG,QAAQ,CAAC9B,CAAC,GAAG,IAAI,CAAC,CAAC;QACxCmG,OAAO,GAAGgC,UAAU,CAACrG,QAAQ,CAAC7B,CAAC,GAAG,IAAI;QACtCmG,OAAO,GAAGgC,UAAU,CAACtG,QAAQ,CAAC9B,CAAC,GAAG,IAAI;QACtCqG,OAAO,GAAG+B,UAAU,CAACtG,QAAQ,CAAC7B,CAAC,GAAG,IAAI;MACxC,CAAC,MAAM;QACL;QACAiG,OAAO,GAAGiC,UAAU,CAACrG,QAAQ,CAAC9B,CAAC,GAAG,EAAE,CAAC,CAAC;QACtCmG,OAAO,GAAGgC,UAAU,CAACrG,QAAQ,CAAC7B,CAAC,GAAG,EAAE,CAAC,CAAC;QACtCmG,OAAO,GAAGgC,UAAU,CAACtG,QAAQ,CAAC9B,CAAC,GAAG,EAAE;QACpCqG,OAAO,GAAG+B,UAAU,CAACtG,QAAQ,CAAC7B,CAAC,GAAG,EAAE;MACtC;MAEA;MACA,OAAO,IAAI,CAACoI,oBAAoB,CAC9B;QAAErI,CAAC,EAAEkG,OAAO;QAAEjG,CAAC,EAAEkG;MAAO,CAAE,EAC1B;QAAEnG,CAAC,EAAEoG,OAAO;QAAEnG,CAAC,EAAEoG;MAAO,CAAE,CAC3B;IACH;IAEA;IACOlE,eAAeA,CAAA;MACpBT,OAAO,CAACC,GAAG,CACT,qCAAqC,EACrC,IAAI,CAACpF,KAAK,CAACqF,GAAG,CAAEC,CAAC,KAAM;QAAElG,EAAE,EAAEkG,CAAC,CAAClG,EAAE;QAAEmG,QAAQ,EAAED,CAAC,CAACC;MAAQ,CAAE,CAAC,CAAC,CAC5D;MACDJ,OAAO,CAACC,GAAG,CACT,qCAAqC,EACrC,IAAI,CAAC3G,KAAK,CAAC4G,GAAG,CAAEG,CAAC,KAAM;QAAEpG,EAAE,EAAEoG,CAAC,CAACpG,EAAE;QAAEqG,MAAM,EAAED,CAAC,CAACC,MAAM;QAAEC,MAAM,EAAEF,CAAC,CAACE;MAAM,CAAE,CAAC,CAAC,CAC1E;MAED,IAAI,CAACjH,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC4G,GAAG,CAAE2E,IAAI,IAAI;QACnC,MAAM4B,UAAU,GAAG,IAAI,CAAC5L,KAAK,CAACqI,IAAI,CAAE/C,CAAC,IAAKA,CAAC,CAAClG,EAAE,KAAK4K,IAAI,CAACvE,MAAM,CAAC;QAC/D,MAAMoG,UAAU,GAAG,IAAI,CAAC7L,KAAK,CAACqI,IAAI,CAAE/C,CAAC,IAAKA,CAAC,CAAClG,EAAE,KAAK4K,IAAI,CAACtE,MAAM,CAAC;QAE/D,IAAIkG,UAAU,IAAIC,UAAU,EAAE;UAC5B,MAAME,QAAQ,GAAG,IAAI,CAACJ,iBAAiB,CAACC,UAAU,EAAEC,UAAU,CAAC;UAC/D1G,OAAO,CAACC,GAAG,CAAC,cAAc4E,IAAI,CAAC5K,EAAE,OAAO2M,QAAQ,EAAE,CAAC;UACnD5G,OAAO,CAACC,GAAG,CACT,cAAcwG,UAAU,CAACxM,EAAE,QAAQwM,UAAU,CAACrG,QAAQ,CAAC9B,CAAC,KAAKmI,UAAU,CAACrG,QAAQ,CAAC7B,CAAC,GAAG,CACtF;UACDyB,OAAO,CAACC,GAAG,CACT,cAAcyG,UAAU,CAACzM,EAAE,QAAQyM,UAAU,CAACtG,QAAQ,CAAC9B,CAAC,KAAKoI,UAAU,CAACtG,QAAQ,CAAC7B,CAAC,GAAG,CACtF;UACD,OAAO;YACL,GAAGsG,IAAI;YACP+B,QAAQ,EAAEA;WACJ;QACV,CAAC,MAAM;UACL;UACA5G,OAAO,CAACC,GAAG,CACT,sCAAsC4E,IAAI,CAAC5K,EAAE,oBAAoB,CAClE;UACD,OAAO;YACL,GAAG4K,IAAI;YACP+B,QAAQ,EAAE;WACJ;QACV;MACF,CAAC,CAAC;MAEF5G,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;MACzE,IAAI,CAACtF,GAAG,CAACoH,aAAa,EAAE;IAC1B;IAEQ8E,2BAA2BA,CACjCC,YAAiB,EACjBC,YAAiB;MAKjB,IAAI,IAAI,CAAC9N,aAAa,EAAE;QACtB;QACA;QACA,OAAO;UACL+N,WAAW,EAAEF,YAAY,CAACpE,GAAG;UAAE;UAC/BuE,WAAW,EAAEF,YAAY,CAACrE,GAAG,CAAE;SAChC;MACH,CAAC,MAAM;QACL;QACA;QACA,MAAMwE,kBAAkB,GAAG,CACzB;UAAEC,KAAK,EAAEL,YAAY,CAACT,KAAK;UAAEe,IAAI,EAAE;QAAO,CAAE,EAC5C;UAAED,KAAK,EAAEL,YAAY,CAACR,MAAM;UAAEc,IAAI,EAAE;QAAQ,CAAE,EAC9C;UAAED,KAAK,EAAEL,YAAY,CAACtE,IAAI;UAAE4E,IAAI,EAAE;QAAM,CAAE,EAC1C;UAAED,KAAK,EAAEL,YAAY,CAACpE,GAAG;UAAE0E,IAAI,EAAE;QAAK,CAAE,CACzC;QAED,MAAMC,kBAAkB,GAAG,CACzB;UAAEF,KAAK,EAAEJ,YAAY,CAACvE,IAAI;UAAE4E,IAAI,EAAE;QAAM,CAAE,EAC1C;UAAED,KAAK,EAAEJ,YAAY,CAACrE,GAAG;UAAE0E,IAAI,EAAE;QAAK,CAAE,EACxC;UAAED,KAAK,EAAEJ,YAAY,CAACV,KAAK;UAAEe,IAAI,EAAE;QAAO,CAAE,EAC5C;UAAED,KAAK,EAAEJ,YAAY,CAACT,MAAM;UAAEc,IAAI,EAAE;QAAQ,CAAE,CAC/C;QAED,IAAIE,YAAY,GAAGC,QAAQ;QAC3B,IAAIC,eAAe,GAAGV,YAAY,CAACT,KAAK;QACxC,IAAIoB,eAAe,GAAGV,YAAY,CAACvE,IAAI;QAEvC;QACA,KAAK,MAAMkF,YAAY,IAAIR,kBAAkB,EAAE;UAC7C,KAAK,MAAMS,YAAY,IAAIN,kBAAkB,EAAE;YAC7C,MAAMO,QAAQ,GAAGhF,IAAI,CAACiF,IAAI,CACxBjF,IAAI,CAACkF,GAAG,CAACH,YAAY,CAACR,KAAK,CAAC7I,CAAC,GAAGoJ,YAAY,CAACP,KAAK,CAAC7I,CAAC,EAAE,CAAC,CAAC,GACtDsE,IAAI,CAACkF,GAAG,CAACH,YAAY,CAACR,KAAK,CAAC5I,CAAC,GAAGmJ,YAAY,CAACP,KAAK,CAAC5I,CAAC,EAAE,CAAC,CAAC,CAC3D;YAED,IAAIqJ,QAAQ,GAAGN,YAAY,EAAE;cAC3BA,YAAY,GAAGM,QAAQ;cACvBJ,eAAe,GAAGE,YAAY,CAACP,KAAK;cACpCM,eAAe,GAAGE,YAAY,CAACR,KAAK;YACtC;UACF;QACF;QAEA,OAAO;UACLH,WAAW,EAAEQ,eAAe;UAC5BP,WAAW,EAAEQ;SACd;MACH;IACF;IAEQM,sBAAsBA,CAC5Bf,WAAqC,EACrCC,WAAqC;MAErC,MAAMe,EAAE,GAAGf,WAAW,CAAC3I,CAAC,GAAG0I,WAAW,CAAC1I,CAAC;MACxC,MAAM2J,EAAE,GAAGhB,WAAW,CAAC1I,CAAC,GAAGyI,WAAW,CAACzI,CAAC;MAExC,IAAIqE,IAAI,CAACsF,GAAG,CAACF,EAAE,CAAC,GAAGpF,IAAI,CAACsF,GAAG,CAACD,EAAE,CAAC,EAAE;QAC/B,OAAOD,EAAE,GAAG,CAAC,GAAG,OAAO,GAAG,MAAM;MAClC,CAAC,MAAM;QACL,OAAOC,EAAE,GAAG,CAAC,GAAG,QAAQ,GAAG,KAAK;MAClC;IACF;IAEQE,oBAAoBA,CAC1BlB,WAAqC,EACrCmB,SAAiB,EACjBC,MAAc;MAEd,QAAQD,SAAS;QACf,KAAK,OAAO;UACV,OAAO;YAAE9J,CAAC,EAAE2I,WAAW,CAAC3I,CAAC,GAAG+J,MAAM;YAAE9J,CAAC,EAAE0I,WAAW,CAAC1I;UAAC,CAAE;QACxD,KAAK,MAAM;UACT,OAAO;YAAED,CAAC,EAAE2I,WAAW,CAAC3I,CAAC,GAAG+J,MAAM;YAAE9J,CAAC,EAAE0I,WAAW,CAAC1I;UAAC,CAAE;QACxD,KAAK,QAAQ;UACX,OAAO;YAAED,CAAC,EAAE2I,WAAW,CAAC3I,CAAC;YAAEC,CAAC,EAAE0I,WAAW,CAAC1I,CAAC,GAAG8J;UAAM,CAAE;QACxD,KAAK,KAAK;UACR,OAAO;YAAE/J,CAAC,EAAE2I,WAAW,CAAC3I,CAAC;YAAEC,CAAC,EAAE0I,WAAW,CAAC1I,CAAC,GAAG8J;UAAM,CAAE;QACxD;UACE,OAAOpB,WAAW;MACtB;IACF;IAEQN,oBAAoBA,CAC1BK,WAAqC,EACrCC,WAAqC;MAErC;MACA,MAAMzC,OAAO,GAAGwC,WAAW,CAAC1I,CAAC;MAC7B,MAAMmG,OAAO,GAAGuC,WAAW,CAACzI,CAAC;MAC7B,MAAMmG,OAAO,GAAGuC,WAAW,CAAC3I,CAAC;MAC7B,MAAMqG,OAAO,GAAGsC,WAAW,CAAC1I,CAAC;MAE7B,IAAI,IAAI,CAACtF,aAAa,EAAE;QACtB;QACA,MAAM+O,EAAE,GAAGtD,OAAO,GAAGF,OAAO;QAC5B,MAAMyD,EAAE,GAAGtD,OAAO,GAAGF,OAAO;QAC5B,MAAM6D,MAAM,GAAG1F,IAAI,CAACiF,IAAI,CAACG,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;QAE3C,IAAIK,MAAM,GAAG,EAAE,EAAE;UACf,OAAO,KAAK9D,OAAO,IAAIC,OAAO,MAAMC,OAAO,IAAIC,OAAO,EAAE;QAC1D;QAEA,MAAM4D,KAAK,GAAGP,EAAE,GAAGM,MAAM;QACzB,MAAME,KAAK,GAAGP,EAAE,GAAGK,MAAM;QACzB,MAAMG,YAAY,GAAG,EAAE;QACvB,MAAMC,YAAY,GAAG,EAAE;QACvB,MAAMC,UAAU,GAAG,CAAC;QAEpB,MAAMC,eAAe,GAAGpE,OAAO,GAAG+D,KAAK,GAAGE,YAAY;QACtD,MAAMI,eAAe,GAAGpE,OAAO,GAAG+D,KAAK,GAAGC,YAAY;QACtD,MAAMK,eAAe,GAAGpE,OAAO,GAAG6D,KAAK,IAAIG,YAAY,GAAGC,UAAU,CAAC;QACrE,MAAMI,eAAe,GAAGpE,OAAO,GAAG6D,KAAK,IAAIE,YAAY,GAAGC,UAAU,CAAC;QAErE,OAAO,KAAKC,eAAe,IAAIC,eAAe,MAAMC,eAAe,IAAIC,eAAe,EAAE;MAC1F,CAAC,MAAM;QACL;QACA,MAAMf,EAAE,GAAGtD,OAAO,GAAGF,OAAO;QAC5B,MAAMyD,EAAE,GAAGtD,OAAO,GAAGF,OAAO;QAC5B,MAAMmD,QAAQ,GAAGhF,IAAI,CAACiF,IAAI,CAACG,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;QAE7C,IAAIL,QAAQ,GAAG,EAAE,EAAE;UACjB;UACA,OAAO,KAAKpD,OAAO,IAAIC,OAAO,MAAMC,OAAO,IAAIC,OAAO,EAAE;QAC1D;QAEA;QACA,MAAM4D,KAAK,GAAGP,EAAE,GAAGJ,QAAQ;QAC3B,MAAMY,KAAK,GAAGP,EAAE,GAAGL,QAAQ;QAE3B;QACA,MAAMa,YAAY,GAAG,EAAE,CAAC,CAAC;QACzB,MAAMG,eAAe,GAAGpE,OAAO,GAAG+D,KAAK,GAAGE,YAAY;QACtD,MAAMI,eAAe,GAAGpE,OAAO,GAAG+D,KAAK,GAAGC,YAAY;QAEtD;QACA,MAAMO,gBAAgB,GAAG,EAAE,CAAC,CAAC;QAC7B,MAAMC,WAAW,GAAG,EAAE,CAAC,CAAC;QACxB,MAAMH,eAAe,GACnBpE,OAAO,GAAG6D,KAAK,IAAIS,gBAAgB,GAAGC,WAAW,CAAC;QACpD,MAAMF,eAAe,GACnBpE,OAAO,GAAG6D,KAAK,IAAIQ,gBAAgB,GAAGC,WAAW,CAAC;QAEpD;QACA,OAAO,KAAKL,eAAe,IAAIC,eAAe,MAAMC,eAAe,IAAIC,eAAe,EAAE;MAC1F;IACF;IAEAG,qBAAqBA,CAAA;MACnB,IACE,CAAC,IAAI,CAACjL,cAAc,CAACC,QAAQ,IAC7B,IAAI,CAACD,cAAc,CAACuG,OAAO,KAAKlF,SAAS,IACzC,IAAI,CAACrB,cAAc,CAACwG,OAAO,KAAKnF,SAAS,IACzC,IAAI,CAACrB,cAAc,CAACyG,OAAO,KAAKpF,SAAS,IACzC,IAAI,CAACrB,cAAc,CAAC0G,OAAO,KAAKrF,SAAS,EACzC;QACA,OAAO,EAAE;MACX;MAEA,MAAMkF,OAAO,GAAG,IAAI,CAACvG,cAAc,CAACuG,OAAO;MAC3C,MAAMC,OAAO,GAAG,IAAI,CAACxG,cAAc,CAACwG,OAAO;MAC3C,MAAMC,OAAO,GAAG,IAAI,CAACzG,cAAc,CAACyG,OAAO;MAC3C,MAAMC,OAAO,GAAG,IAAI,CAAC1G,cAAc,CAAC0G,OAAO;MAE3C;MACA,MAAMqD,EAAE,GAAGtD,OAAO,GAAGF,OAAO;MAC5B,MAAMyD,EAAE,GAAGtD,OAAO,GAAGF,OAAO;MAC5B,MAAMmD,QAAQ,GAAGhF,IAAI,CAACiF,IAAI,CAACG,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;MAE7C;MACA,MAAMkB,UAAU,GAAGvG,IAAI,CAACwB,GAAG,CAAC,GAAG,EAAEwD,QAAQ,GAAG,GAAG,CAAC;MAChD,MAAMS,MAAM,GAAGzF,IAAI,CAACC,GAAG,CAAC,EAAE,EAAEsG,UAAU,CAAC;MAEvC;MACA,MAAMC,cAAc,GAAG5E,OAAO,GAAG6D,MAAM;MACvC,MAAMgB,cAAc,GAAG3E,OAAO,GAAG2D,MAAM;MAEvC,OAAO,KAAK7D,OAAO,IAAIC,OAAO,MAAM2E,cAAc,IAAI3E,OAAO,KAAK4E,cAAc,IAAI1E,OAAO,KAAKD,OAAO,IAAIC,OAAO,EAAE;IACtH;IAEA;IACA3M,MAAMA,CAAA;MACJ,IAAI,IAAI,CAAC6G,YAAY,GAAG,CAAC,EAAE;QACzB,IAAI,CAACA,YAAY,EAAE;QACnB,MAAMyK,KAAK,GAAG,IAAI,CAAC1K,OAAO,CAAC,IAAI,CAACC,YAAY,CAAC;QAC7C,IAAI,CAAC0K,YAAY,CAACD,KAAK,CAAC;MAC1B;MACA,IAAI,CAAC9L,UAAU,CAAC+B,IAAI,EAAE;IACxB;IAEApH,MAAMA,CAAA;MACJ,IAAI,IAAI,CAAC0G,YAAY,GAAG,IAAI,CAACD,OAAO,CAAC0J,MAAM,GAAG,CAAC,EAAE;QAC/C,IAAI,CAACzJ,YAAY,EAAE;QACnB,MAAMyK,KAAK,GAAG,IAAI,CAAC1K,OAAO,CAAC,IAAI,CAACC,YAAY,CAAC;QAC7C,IAAI,CAAC0K,YAAY,CAACD,KAAK,CAAC;MAC1B;MACA,IAAI,CAAC7L,UAAU,CAAC8B,IAAI,EAAE;IACxB;IAEAiK,OAAOA,CAAA;MACL;MACA,IAAI,CAAC1I,aAAa,EAAE;MAEpB;MACA,IAAI,CAACjG,KAAK,GAAG,EAAE;MACf,IAAI,CAACvB,KAAK,GAAG,EAAE;MACf,IAAI,CAACU,cAAc,GAAG,IAAI;MAC1B,IAAI,CAACiE,cAAc,GAAG;QAAEC,QAAQ,EAAE;MAAK,CAAE;MAEzC;MACA,IAAI,CAAC2D,aAAa,EAAE;MAEpB;MACA,IAAI,CAACjE,YAAY,CAAC2B,IAAI,CAAC;QACrB1E,KAAK,EAAE,EAAE;QACTvB,KAAK,EAAE;OACR,CAAC;MAEF;MACA,IAAI,CAAC0D,WAAW,CAACuC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;MAC9B,IAAI,CAAC7B,WAAW,CAAC6B,IAAI,EAAE;IACzB;IAEAjH,oBAAoBA,CAAA;MAClB,IAAI,CAACqF,oBAAoB,CAAC4B,IAAI,EAAE;IAClC;IAEA;IACQuB,aAAaA,CAAA;MACnB;MACA,IAAI,IAAI,CAAC/B,gBAAgB,EAAE;MAE3B;MACA,IAAI,CAACH,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC6K,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC5K,YAAY,GAAG,CAAC,CAAC;MAE3D;MACA,MAAM6K,YAAY,GAAG;QACnB7O,KAAK,EAAE8O,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAAC,IAAI,CAAChP,KAAK,CAAC,CAAC;QAC7CvB,KAAK,EAAEqQ,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAAC,IAAI,CAACvQ,KAAK,CAAC;OAC7C;MAED,IAAI,CAACsF,OAAO,CAACsH,IAAI,CAACwD,YAAY,CAAC;MAC/B,IAAI,CAAC7K,YAAY,GAAG,IAAI,CAACD,OAAO,CAAC0J,MAAM,GAAG,CAAC;MAE3C;MACA,IAAI,IAAI,CAAC1J,OAAO,CAAC0J,MAAM,GAAG,IAAI,CAACxJ,cAAc,EAAE;QAC7C,IAAI,CAACF,OAAO,CAACkL,KAAK,EAAE;QACpB,IAAI,CAACjL,YAAY,EAAE;MACrB;IACF;IAEQ0K,YAAYA,CAACD,KAGpB;MACC,IAAI,CAACvK,gBAAgB,GAAG,IAAI;MAE5B;MACA,IAAI,CAAClE,KAAK,GAAG,CAAC,GAAGyO,KAAK,CAACzO,KAAK,CAAC;MAC7B,IAAI,CAACvB,KAAK,GAAG,CAAC,GAAGgQ,KAAK,CAAChQ,KAAK,CAAC;MAE7B;MACA,IAAI,CAACU,cAAc,GAAG,IAAI;MAC1B,IAAI,CAACiE,cAAc,GAAG;QAAEC,QAAQ,EAAE;MAAK,CAAE;MAEzC;MACA,IAAI,CAACsC,0BAA0B,EAAE;MAEjC;MACA,IAAI,CAAC5C,YAAY,CAAC2B,IAAI,CAAC;QACrB1E,KAAK,EAAE,CAAC,GAAG,IAAI,CAACA,KAAK,CAAC;QACtBvB,KAAK,EAAE,CAAC,GAAG,IAAI,CAACA,KAAK;OACtB,CAAC;MAEF;MACAoH,UAAU,CAAC,MAAK;QACd,IAAI,CAAC3B,gBAAgB,GAAG,KAAK;MAC/B,CAAC,EAAE,GAAG,CAAC;IACT;IAEA;IACQqD,uBAAuBA,CAAA;MAC7B,IAAI,CAACtB,aAAa,EAAE;IACtB;IAEA;IACAiJ,OAAOA,CAACnK,IAAgB;MACtB,IAAI,CAACwC,uBAAuB,EAAE;MAC9B,IAAI,CAACvH,KAAK,CAACqL,IAAI,CAACtG,IAAI,CAAC;MACrB,IAAI,CAACY,0BAA0B,EAAE;MACjC,IAAI,CAACzD,SAAS,CAACwC,IAAI,CAACK,IAAI,CAAC;IAC3B;IAEAoK,OAAOA,CAACnF,IAAgB;MACtB,IAAI,CAACzC,uBAAuB,EAAE;MAC9B,IAAI,CAAC9I,KAAK,CAAC4M,IAAI,CAACrB,IAAI,CAAC;IACvB;IAEAoF,UAAUA,CAACnH,MAAc;MACvB,IAAI,CAAC5I,YAAY,CAAC4I,MAAM,CAAC;IAC3B;IAEAoH,UAAUA,CAACC,MAAc;MACvB,IAAI,CAAC/H,uBAAuB,EAAE;MAC9B,IAAI,CAAC9I,KAAK,GAAG,IAAI,CAACA,KAAK,CAACsL,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAAC5K,EAAE,KAAKkQ,MAAM,CAAC;IAC9D;IAEA;IACAC,UAAUA,CAAA;MACR,IAAI,CAACpL,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;MACtC,IAAI,CAAClE,QAAQ,GAAG,IAAI,CAACkE,YAAY;IACnC;IAEAzH,UAAUA,CAAA;MACR,IAAI,CAACM,UAAU,GAAG,KAAK;MACvB,IAAI,CAAC2C,wBAAwB,GAAG,IAAI;MACpC,IAAI,IAAI,CAACI,eAAe,EAAE;QACxB,IAAI,CAACA,eAAe,CAACyG,aAAa,CAACsE,KAAK,CAACC,MAAM,GAAG,MAAM;MAC1D;IACF;IAEAvO,aAAaA,CAAA;MACX,IAAI,CAACQ,UAAU,GAAG,QAAQ;MAC1B,IAAI,CAAC2C,wBAAwB,GAAG,IAAI;MACpC,IAAI,IAAI,CAACI,eAAe,EAAE;QACxB,IAAI,CAACA,eAAe,CAACyG,aAAa,CAACsE,KAAK,CAACC,MAAM,GAAG,SAAS;MAC7D;IACF;IAEAlO,WAAWA,CAAA;MACT,IAAI,CAACG,UAAU,GAAG,MAAM;MACxB,IAAI,CAAC2C,wBAAwB,GAAG,IAAI;MACpC,IAAI,IAAI,CAACI,eAAe,EAAE;QACxB,IAAI,CAACA,eAAe,CAACyG,aAAa,CAACsE,KAAK,CAACC,MAAM,GAAG,SAAS;MAC7D;IACF;IAEAyE,wBAAwBA,CAAA;MACtB,IAAI,CAACxS,UAAU,GAAG,UAAU;MAC5B,IAAI,CAAC2C,wBAAwB,GAAG,KAAK;MACrC,IAAI,IAAI,CAACI,eAAe,EAAE;QACxB,IAAI,CAACA,eAAe,CAACyG,aAAa,CAACsE,KAAK,CAACC,MAAM,GAAG,aAAa;MACjE;IACF;IAEAnO,MAAMA,CAAA;MACJ,MAAM2N,OAAO,GAAGxC,IAAI,CAACwB,GAAG,CAAC,IAAI,CAACjJ,OAAO,EAAE,IAAI,CAACiD,QAAQ,CAACC,IAAI,GAAG,GAAG,CAAC;MAChE,IAAI,CAACiM,OAAO,CAAClF,OAAO,CAAC;IACvB;IAEAxN,OAAOA,CAAA;MACL,MAAMwN,OAAO,GAAGxC,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC3H,OAAO,EAAE,IAAI,CAACkD,QAAQ,CAACC,IAAI,GAAG,GAAG,CAAC;MAChE,IAAI,CAACiM,OAAO,CAAClF,OAAO,CAAC;IACvB;IAEQkF,OAAOA,CAAClF,OAAe;MAC7B,IAAIA,OAAO,KAAK,IAAI,CAAChH,QAAQ,CAACC,IAAI,EAAE;QAClC;QACA,MAAMsF,IAAI,GAAG,IAAI,CAAC/I,eAAe,CAACyG,aAAa,CAACiB,qBAAqB,EAAE;QACvE,MAAM6D,OAAO,GAAGxC,IAAI,CAACI,KAAK,GAAG,CAAC;QAC9B,MAAMqC,OAAO,GAAGzC,IAAI,CAACM,MAAM,GAAG,CAAC;QAE/B;QACA,MAAMoB,SAAS,GAAGD,OAAO,GAAG,IAAI,CAAChH,QAAQ,CAACC,IAAI;QAC9C,MAAMiH,IAAI,GAAGa,OAAO,GAAG,CAACA,OAAO,GAAG,IAAI,CAAC/H,QAAQ,CAACE,CAAC,IAAI+G,SAAS;QAC9D,MAAME,IAAI,GAAGa,OAAO,GAAG,CAACA,OAAO,GAAG,IAAI,CAAChI,QAAQ,CAACG,CAAC,IAAI8G,SAAS;QAE9D;QACA,IAAI,CAACjH,QAAQ,CAACC,IAAI,GAAG+G,OAAO;QAC5B,IAAI,CAAChH,QAAQ,CAACE,CAAC,GAAGgH,IAAI;QACtB,IAAI,CAAClH,QAAQ,CAACG,CAAC,GAAGgH,IAAI;QAEtB;QACA,IAAI,CAAC/E,0BAA0B,EAAE;QAEjC;QACA,IAAI,CAACjD,eAAe,CAACgC,IAAI,CAAC,IAAI,CAACnB,QAAQ,CAAC;MAC1C;IACF;IAEA;IACAmM,iBAAiBA,CAACzN,KAAa;MAC7B,IAAI,CAACR,gBAAgB,CAACkD,QAAQ,CAAC1C,KAAK,CAAC;MACrC,IAAI,CAACe,gBAAgB,CAAC0B,IAAI,CAACzC,KAAK,CAAC;IACnC;IAEA0N,iBAAiBA,CAAC1N,KAAwB;MACxC,MAAM2N,UAAU,GAAGC,KAAK,CAACC,OAAO,CAAC7N,KAAK,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK;MAC1D,IAAI,CAACP,gBAAgB,CAACiD,QAAQ,CAACiL,UAAU,CAAC;MAC1C,IAAI,CAAC3M,gBAAgB,CAACyB,IAAI,CAACkL,UAAU,CAAC;IACxC;IAEA;IACAG,sBAAsBA,CAAA;MACpB,IAAI,CAACxO,sBAAsB,GAAG,CAAC,IAAI,CAACA,sBAAsB;IAC5D;IAEAyO,qBAAqBA,CAAA;MACnB,IAAI,CAACzO,sBAAsB,GAAG,KAAK;IACrC;IAEA0O,gBAAgBA,CACdC,aAAgC,EAChCC,KAAa,EACbC,WAAmB,EACnBC,QAAgB;MAEhB,MAAMpO,KAAK,GAAG4N,KAAK,CAACC,OAAO,CAACI,aAAa,CAAC,GACtCA,aAAa,CAAC,CAAC,CAAC,GAChBA,aAAa;MACjB,IAAI,CAACjO,KAAK,EAAE;MAEZ;MACA,QAAQmO,WAAW;QACjB,KAAK,KAAK;UACR,IAAI,CAACxO,UAAU,CAAC+C,QAAQ,CAAC1C,KAAK,CAAC;UAC/B;UACA,IAAI,CAACJ,aAAa,CAAC8C,QAAQ,CAAC,EAAE,CAAC;UAC/B,IAAI,CAAC7C,cAAc,CAAC6C,QAAQ,CAAC,EAAE,CAAC;UAChC,IAAI,CAAC5C,WAAW,CAAC4C,QAAQ,CAAC,EAAE,CAAC;UAC7B;UACA,IAAI,CAAC2L,iBAAiB,CAACrO,KAAK,CAAC;UAC7B;QACF,KAAK,QAAQ;UACX,IAAI,CAACJ,aAAa,CAAC8C,QAAQ,CAAC1C,KAAK,CAAC;UAClC;UACA,IAAI,CAACH,cAAc,CAAC6C,QAAQ,CAAC,EAAE,CAAC;UAChC,IAAI,CAAC5C,WAAW,CAAC4C,QAAQ,CAAC,EAAE,CAAC;UAC7B;UACA,IAAI,CAAC4L,kBAAkB,CAAC,IAAI,CAAC3O,UAAU,CAACK,KAAK,IAAI,EAAE,EAAEA,KAAK,CAAC;UAC3D;QACF,KAAK,SAAS;UACZ,IAAI,CAACH,cAAc,CAAC6C,QAAQ,CAAC1C,KAAK,CAAC;UACnC;UACA,IAAI,CAACF,WAAW,CAAC4C,QAAQ,CAAC,EAAE,CAAC;UAC7B;UACA,IAAI,CAAC6L,eAAe,CAClB,IAAI,CAAC5O,UAAU,CAACK,KAAK,IAAI,EAAE,EAC3B,IAAI,CAACJ,aAAa,CAACI,KAAK,IAAI,EAAE,EAC9BA,KAAK,CACN;UACD;QACF,KAAK,MAAM;UACT,IAAI,CAACF,WAAW,CAAC4C,QAAQ,CAAC1C,KAAK,CAAC;UAChC;MACJ;IACF;IAEAwO,aAAaA,CAAA;MACX,MAAMC,SAAS,GACb,IAAI,CAAC9O,UAAU,CAACK,KAAK,IACrB,IAAI,CAACJ,aAAa,CAACI,KAAK,IACxB,IAAI,CAACH,cAAc,CAACG,KAAK,IACzB,IAAI,CAACF,WAAW,CAACE,KAAK;MAExB,IAAIyO,SAAS,EAAE;QACb,IAAI,CAAClP,cAAc,GACjB,IAAI,CAACjH,iBAAiB,CAACoW,QAAQ,EAAEC,UAAU,EAAEC,KAAK,IAClD,4BAA4B;MAChC,CAAC,MAAM;QACL,IAAI,CAACrP,cAAc,GACjB,IAAI,CAACjH,iBAAiB,CAACoW,QAAQ,EAAEC,UAAU,EAAEE,QAAQ,IACrD,gCAAgC;MACpC;MAEA;MACA,IAAI,CAAC5N,eAAe,CAACwB,IAAI,CAAC;QACxBvD,GAAG,EAAE,IAAI,CAACS,UAAU,CAACK,KAAK,IAAI,EAAE;QAChCb,MAAM,EAAE,IAAI,CAACS,aAAa,CAACI,KAAK,IAAI,EAAE;QACtCZ,OAAO,EAAE,IAAI,CAACS,cAAc,CAACG,KAAK,IAAI,EAAE;QACxCX,IAAI,EAAE,IAAI,CAACS,WAAW,CAACE,KAAK,IAAI;OACjC,CAAC;MAEF,IAAI,CAAC+N,qBAAqB,EAAE;IAC9B;IAEAe,cAAcA,CAAA;MACZ,IAAI,CAACf,qBAAqB,EAAE;IAC9B;IAEA;IACQM,iBAAiBA,CAACnP,GAAW;MACnC,MAAM6P,gBAAgB,GAAsC;QAC1DC,SAAS,EAAE,CACT;UAAEhP,KAAK,EAAE,aAAa;UAAEtG,KAAK,EAAE;QAAa,CAAE,EAC9C;UAAEsG,KAAK,EAAE,WAAW;UAAEtG,KAAK,EAAE;QAAW,CAAE,EAC1C;UAAEsG,KAAK,EAAE,OAAO;UAAEtG,KAAK,EAAE;QAAO,CAAE,CACnC;QACDuV,QAAQ,EAAE,CACR;UAAEjP,KAAK,EAAE,MAAM;UAAEtG,KAAK,EAAE;QAAY,CAAE,EACtC;UAAEsG,KAAK,EAAE,YAAY;UAAEtG,KAAK,EAAE;QAAY,CAAE,CAC7C;QACDwV,QAAQ,EAAE,CACR;UAAElP,KAAK,EAAE,UAAU;UAAEtG,KAAK,EAAE;QAAU,CAAE,EACxC;UAAEsG,KAAK,EAAE,aAAa;UAAEtG,KAAK,EAAE;QAAa,CAAE;OAEjD;MAED,IAAI,CAACqG,cAAc,CAAC,QAAQ,CAAC,GAAGgP,gBAAgB,CAAC7P,GAAG,CAAC,IAAI,EAAE;IAC7D;IAEQoP,kBAAkBA,CAACpP,GAAW,EAAEC,MAAc;MACpD,MAAMgQ,cAAc,GAAmB,CACrC;QAAEnP,KAAK,EAAE,UAAU;QAAEtG,KAAK,EAAE;MAAe,CAAE,EAC7C;QAAEsG,KAAK,EAAE,UAAU;QAAEtG,KAAK,EAAE;MAAc,CAAE,EAC5C;QAAEsG,KAAK,EAAE,UAAU;QAAEtG,KAAK,EAAE;MAAe,CAAE,CAC9C;MAED,IAAI,CAACqG,cAAc,CAAC,SAAS,CAAC,GAAGoP,cAAc;IACjD;IAEQZ,eAAeA,CAACrP,GAAW,EAAEC,MAAc,EAAEC,OAAe;MAClE,MAAMgQ,WAAW,GAAmB,CAClC;QAAEpP,KAAK,EAAE,OAAO;QAAEtG,KAAK,EAAE;MAAY,CAAE,EACvC;QAAEsG,KAAK,EAAE,OAAO;QAAEtG,KAAK,EAAE;MAAW,CAAE,EACtC;QAAEsG,KAAK,EAAE,OAAO;QAAEtG,KAAK,EAAE;MAAY,CAAE,CACxC;MAED,IAAI,CAACqG,cAAc,CAAC,MAAM,CAAC,GAAGqP,WAAW;IAC3C;IAEA;IAEA;IAEA;IACAhW,0BAA0BA,CAAA;MACxB,IAAI,CAACK,0BAA0B,GAAG,CAAC,IAAI,CAACA,0BAA0B;IACpE;IAEA4V,yBAAyBA,CAAA;MACvB,IAAI,CAAC5V,0BAA0B,GAAG,KAAK;IACzC;IAEAtB,iBAAiBA,CAAA;MACf,MAAMmS,IAAI,GAAG,IAAI,CAAC7R,sBAAsB,CAACuH,KAAK,IAAI,EAAE;MACpD,MAAMsP,cAAc,GAAG,IAAI,CAACzW,kBAAkB,CAACmH,KAAK,IAAI,EAAE;MAE1D;MACA,IAAI,CAACkB,mBAAmB,CAACuB,IAAI,CAAC;QAC5B6H,IAAI,EAAEA,IAAI;QACVgF,cAAc,EAAEA;OACjB,CAAC;MAEF,IAAI,CAACD,yBAAyB,EAAE;IAClC;IAEApX,kBAAkBA,CAAA;MAChB,IAAI,CAACoX,yBAAyB,EAAE;IAClC;;uCA94CWzR,oBAAoB,EAAAvG,EAAA,CAAAkY,iBAAA,CAAAlY,EAAA,CAAAmY,iBAAA;IAAA;;YAApB5R,oBAAoB;MAAA6R,SAAA;MAAAC,cAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA,EAAAC,QAAA;QAAA,IAAAF,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UCnHjCvY,EAAA,CAAAC,cAAA,aAAyE;UA+GvED,EA7GA,CAAAgC,UAAA,IAAA0W,mCAAA,iBAA4D,IAAAC,mCAAA,iBA6GI;UAOhE3Y,EAAA,CAAAC,cAAA,gBAWC;UADCD,EALA,CAAAK,UAAA,sBAAAuY,sDAAAC,MAAA;YAAA7Y,EAAA,CAAAO,aAAA,CAAAuY,GAAA;YAAA,OAAA9Y,EAAA,CAAAW,WAAA,CAAY6X,GAAA,CAAA3K,UAAA,CAAAgL,MAAA,CAAkB;UAAA,EAAC,kBAAAE,kDAAAF,MAAA;YAAA7Y,EAAA,CAAAO,aAAA,CAAAuY,GAAA;YAAA,OAAA9Y,EAAA,CAAAW,WAAA,CACvB6X,GAAA,CAAAxK,MAAA,CAAA6K,MAAA,CAAc;UAAA,EAAC,qBAAAG,qDAAAH,MAAA;YAAA7Y,EAAA,CAAAO,aAAA,CAAAuY,GAAA;YAAA,OAAA9Y,EAAA,CAAAW,WAAA,CACZ6X,GAAA,CAAA9G,eAAA,CAAAmH,MAAA,CAAuB;UAAA,EAAC,uBAAAI,uDAAAJ,MAAA;YAAA7Y,EAAA,CAAAO,aAAA,CAAAuY,GAAA;YAAA,OAAA9Y,EAAA,CAAAW,WAAA,CACtB6X,GAAA,CAAA7H,iBAAA,CAAAkI,MAAA,CAAyB;UAAA,EAAC,mBAAAK,mDAAAL,MAAA;YAAA7Y,EAAA,CAAAO,aAAA,CAAAuY,GAAA;YAAA,OAAA9Y,EAAA,CAAAW,WAAA,CAC9B6X,GAAA,CAAA1H,aAAA,CAAA+H,MAAA,CAAqB;UAAA,EAAC,uBAAAM,uDAAAN,MAAA;YAAA7Y,EAAA,CAAAO,aAAA,CAAAuY,GAAA;YAAA,OAAA9Y,EAAA,CAAAW,WAAA,CAClB6X,GAAA,CAAAnH,iBAAA,CAAAwH,MAAA,CAAyB;UAAA,EAAC;UAKvC7Y,EAAA,CAAA4B,YAAA,GAAyC;UA0HzC5B,EAvHA,CAAAgC,UAAA,IAAAoX,mCAAA,iBAA0D,IAAAC,mCAAA,iBAwDR,IAAAC,wCAAA,kBAyEjD;UA6CCtZ,EAfF,CAAAC,cAAA,aAaC,cAE6B;UAE1BD,EAAA,CAAAgC,UAAA,KAAAuX,yCAAA,kBAGC;UA8BDvZ,EAAA,CAAAC,cAAA,eAA0C;UAExCD,EAAA,CAAAgC,UAAA,KAAAwX,6CAAA,4BAAiE;UAkBnExZ,EAAA,CAAAG,YAAA,EAAM;UAeNH,EAZA,CAAAgC,UAAA,KAAAyX,yCAAA,kBAGC,KAAAC,oCAAA,kBASwD;UAMjE1Z,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACF;;;UAlY8BH,EAAA,CAAAmC,WAAA,iBAAAqW,GAAA,CAAA1T,aAAA,CAAoC;UAElC9E,EAAA,CAAAe,SAAA,EAAsB;UAAtBf,EAAA,CAAAgB,UAAA,SAAAwX,GAAA,CAAA/Q,gBAAA,CAAsB;UA6G5BzH,EAAA,CAAAe,SAAA,EAAgC;UAAhCf,EAAA,CAAAgB,UAAA,SAAAwX,GAAA,CAAAzV,eAAA,CAAAoR,MAAA,KAAgC;UAU5DnU,EAAA,CAAAe,SAAA,EAA4B;UAC5Bf,EADA,CAAAmC,WAAA,cAAAqW,GAAA,CAAA7R,QAAA,CAA4B,cAAA6R,GAAA,CAAAnS,wBAAA,CACgB;UAcTrG,EAAA,CAAAe,SAAA,GAAqB;UAArBf,EAAA,CAAAgB,UAAA,SAAAwX,GAAA,CAAApR,eAAA,CAAqB;UAwDzBpH,EAAA,CAAAe,SAAA,EAAiB;UAAjBf,EAAA,CAAAgB,UAAA,SAAAwX,GAAA,CAAAtR,WAAA,CAAiB;UAiE7ClH,EAAA,CAAAe,SAAA,EAAwC;UAAxCf,EAAA,CAAAgB,UAAA,SAAAwX,GAAA,CAAA1R,iBAAA,IAAA0R,GAAA,CAAA1T,aAAA,CAAwC;UAwCzC9E,EAAA,CAAAe,SAAA,EAUC;UAVDf,EAAA,CAAAsC,WAAA,cAAAkW,GAAA,CAAA1T,aAAA,2BAAA0T,GAAA,CAAAvO,QAAA,CAAAE,CAAA,YAAAqO,GAAA,CAAAvO,QAAA,CAAAG,CAAA,kBAAAoO,GAAA,CAAAvO,QAAA,CAAAC,IAAA,OAUC;UAOIlK,EAAA,CAAAe,SAAA,GAAyC;UAAzCf,EAAA,CAAAgB,UAAA,SAAAwX,GAAA,CAAA1R,iBAAA,KAAA0R,GAAA,CAAA1T,aAAA,CAAyC;UAiCX9E,EAAA,CAAAe,SAAA,GAAU;UAAAf,EAAV,CAAAgB,UAAA,YAAAwX,GAAA,CAAA9R,KAAA,CAAU,iBAAA8R,GAAA,CAAAjN,aAAA,CAAsB;UAsB9DvL,EAAA,CAAAe,SAAA,EAAkD;UAAlDf,EAAA,CAAAgB,UAAA,SAAAwX,GAAA,CAAA1O,cAAA,CAAAC,QAAA,IAAAyO,GAAA,CAAA1R,iBAAA,CAAkD;UAW/C9G,EAAA,CAAAe,SAAA,EAAwB;UAAxBf,EAAA,CAAAgB,UAAA,SAAAwX,GAAA,CAAA9R,KAAA,CAAAyN,MAAA,OAAwB;;;qBDnRlC1U,YAAY,EAAAka,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,gBAAA,EACZna,mBAAmB,EAAAoa,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,oBAAA,EACnBra,eAAe,EACfC,mBAAmB,EACnBC,oBAAoB,EACpBC,aAAa;MAAAma,MAAA;IAAA;;SAKJ3T,oBAAoB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}