{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { map } from 'rxjs';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let AnalyticsService = /*#__PURE__*/(() => {\n  class AnalyticsService {\n    http;\n    apiServiceUrl = environment?.consoleApi;\n    constructor(http) {\n      this.http = http;\n    }\n    getHeaders() {\n      return {\n        headers: new HttpHeaders({\n          'Content-Type': 'application/json'\n        })\n      };\n    }\n    getDownloadHeaders() {\n      return new HttpHeaders({\n        'Content-Type': 'application/json'\n      });\n    }\n    getAllAnalyticsData(payload) {\n      const fromDate = payload['fromDate'];\n      const toDate = payload['toDate'];\n      const url = `${this.apiServiceUrl}/ava/force/analytics?dateStart=${fromDate}&dateEnd=${toDate}`;\n      return this.http.get(url, this.getHeaders());\n    }\n    // Usecase Analytics Methods\n    totalRequestCount(payload) {\n      const fromDate = payload['fromDate'];\n      const toDate = payload['toDate'];\n      const headers = {\n        headers: new HttpHeaders({\n          'Content-Type': 'application/json'\n        })\n      };\n      const url = `${this.apiServiceUrl}/ava/force/analytics?dateStart=${fromDate}&dateEnd=${toDate}`;\n      const result = {\n        totalBugs: 0,\n        totalUnitTest: 0,\n        totalStory: 0,\n        totalCodeOptimization: 0\n      };\n      return this.http.get(url, headers).pipe(map(response => {\n        let totalStoryOrEpic = 0;\n        if (response?.useCaseLevelAnalytics) {\n          response.useCaseLevelAnalytics.forEach(data => {\n            switch (data.useCaseCode) {\n              case 'FIND_BUG':\n                result.totalBugs = data.count;\n                break;\n              case 'GENERATE_UNIT_TESTS':\n                result.totalUnitTest = data.count;\n                break;\n              case 'STORY_OR_EPIC':\n              case 'CREATE_STORY':\n                totalStoryOrEpic += data.count;\n                break;\n              case 'OPTIMIZE_CODE':\n                result.totalCodeOptimization = data.count;\n                break;\n            }\n          });\n        }\n        result.totalStory = totalStoryOrEpic;\n        return result;\n      }));\n    }\n    getLinesOfCodeProcessed(payload) {\n      const fromDate = payload['fromDate'];\n      const toDate = payload['toDate'];\n      const headers = {\n        headers: new HttpHeaders({\n          'Content-Type': 'application/json'\n        })\n      };\n      const url = `${this.apiServiceUrl}/ava/force/analytics?dateStart=${fromDate}&dateEnd=${toDate}`;\n      const result = {\n        categories: [],\n        ySeries: []\n      };\n      return this.http.get(url, headers).pipe(map(response => {\n        if (response?.linesOfCodeAnalytics) {\n          response.linesOfCodeAnalytics.forEach(data => {\n            result.categories.push(data.date);\n            result.ySeries.push(data.count);\n          });\n        }\n        return result;\n      }));\n    }\n    getTopUseCases(payload) {\n      const fromDate = payload['fromDate'];\n      const toDate = payload['toDate'];\n      const headers = {\n        headers: new HttpHeaders({\n          'Content-Type': 'application/json'\n        })\n      };\n      const url = `${this.apiServiceUrl}/ava/force/analytics?dateStart=${fromDate}&dateEnd=${toDate}`;\n      const result = {\n        categories: [],\n        ySeries: []\n      };\n      return this.http.get(url, headers).pipe(map(response => {\n        if (response?.useCaseLevelAnalytics) {\n          response.useCaseLevelAnalytics.slice(0, 5).forEach(data => {\n            result.categories.push(data.useCaseCode);\n            result.ySeries.push(data.count);\n          });\n        }\n        return result;\n      }));\n    }\n    getTotalNumberOfRequests(payload) {\n      const fromDate = payload['fromDate'];\n      const toDate = payload['toDate'];\n      const headers = {\n        headers: new HttpHeaders({\n          'Content-Type': 'application/json'\n        })\n      };\n      const url = `${this.apiServiceUrl}/ava/force/analytics?dateStart=${fromDate}&dateEnd=${toDate}`;\n      const result = {\n        categories: [],\n        ySeries: []\n      };\n      return this.http.get(url, headers).pipe(map(response => {\n        if (response?.numberOfRequestAnalytics) {\n          response.numberOfRequestAnalytics.forEach(data => {\n            result.categories.push(data.date);\n            result.ySeries.push(data.requestCount);\n          });\n        }\n        return result;\n      }));\n    }\n    getTopLanguages(payload) {\n      const fromDate = payload['fromDate'];\n      const toDate = payload['toDate'];\n      const headers = {\n        headers: new HttpHeaders({\n          'Content-Type': 'application/json'\n        })\n      };\n      const url = `${this.apiServiceUrl}/ava/force/analytics?dateStart=${fromDate}&dateEnd=${toDate}`;\n      const result = {\n        categories: [],\n        ySeries: []\n      };\n      return this.http.get(url, headers).pipe(map(response => {\n        if (response?.programmingLanguageAnalytics) {\n          response.programmingLanguageAnalytics.slice(0, 5).forEach(data => {\n            result.categories.push(data.programmingLanguage);\n            result.ySeries.push(data.count);\n          });\n        }\n        return result;\n      }));\n    }\n    getUserResponse(payload) {\n      const fromDate = payload['fromDate'];\n      const toDate = payload['toDate'];\n      const headers = {\n        headers: new HttpHeaders({\n          'Content-Type': 'application/json'\n        })\n      };\n      const url = `${this.apiServiceUrl}/ava/force/analytics?dateStart=${fromDate}&dateEnd=${toDate}`;\n      const result = {\n        categories: [],\n        ySeries: []\n      };\n      return this.http.get(url, headers).pipe(map(response => {\n        let zeroCount = 0;\n        if (response?.userResponseAnalytics) {\n          response.userResponseAnalytics.forEach(data => {\n            result.categories.push(data.response);\n            result.ySeries.push([data.response, data.percentage]);\n            if (data.percentage === 0) {\n              zeroCount++;\n            }\n          });\n        }\n        // Return empty result if all percentages are 0\n        if (zeroCount === 3) {\n          return {\n            categories: [],\n            ySeries: []\n          };\n        }\n        return result;\n      }));\n    }\n    getUserUsageAnalytics(payload) {\n      const fromDate = payload['fromDate'];\n      const toDate = payload['toDate'];\n      const headers = {\n        headers: new HttpHeaders({\n          'Content-Type': 'application/json'\n        })\n      };\n      const url = `${this.apiServiceUrl}/ava/force/user/analytics?dateStart=${fromDate}&dateEnd=${toDate}`;\n      return this.http.get(url, headers);\n    }\n    getUserActivity(payload) {\n      const fromDate = payload['fromDate'];\n      const toDate = payload['toDate'];\n      const headers = {\n        headers: new HttpHeaders({\n          'Content-Type': 'application/json'\n        })\n      };\n      const url = `${this.apiServiceUrl}/ava/force/useractivity/analytics?dateStart=${fromDate}&dateEnd=${toDate}`;\n      return this.http.get(url, headers);\n    }\n    getResponseTime(payload) {\n      const fromDate = payload['fromDate'];\n      const toDate = payload['toDate'];\n      const headers = {\n        headers: new HttpHeaders({\n          'Content-Type': 'application/json'\n        })\n      };\n      const url = `${this.apiServiceUrl}/ava/force/apiresponse/analytics?dateStart=${fromDate}&dateEnd=${toDate}`;\n      return this.http.get(url, headers);\n    }\n    // Agent Analytics Methods\n    getAgenticAIAnalytics(payload) {\n      const fromDate = payload['fromDate'];\n      const toDate = payload['toDate'];\n      const headers = {\n        headers: new HttpHeaders({\n          'Content-Type': 'application/json'\n        })\n      };\n      const url = `${this.apiServiceUrl}/ava/force/analytics/agenticAI?dateStart=${fromDate}&dateEnd=${toDate}`;\n      return this.http.get(url, headers).pipe(map(response => {\n        const toolUsage = (response.toolUsage || []).filter(tool => tool.usageCount !== null && tool.usageCount > 0);\n        const topAgents = (response.topAgents || []).slice(0, 5);\n        const validAgents = (response.agentCreated || []).filter(agent => agent.teamName && agent.usageCount > 0);\n        const top3AgentsCreated = validAgents.slice(0, 3);\n        return {\n          totalAgentsCreated: response.totalAgentsCreated,\n          totalAgentsReused: response.totalAgentsReused,\n          totalTools: response.totalTools,\n          studioUsage: response.studioUsage,\n          topAgents: topAgents,\n          agentCreated: top3AgentsCreated,\n          agentMetrics: response.agentMetrics,\n          toolAnalytics: response.toolAnalytics,\n          toolUsage: toolUsage,\n          adoptionRate: response.adoptionRate,\n          userConsumption: response.userConsumption,\n          userActivityStats: response.userActivityStats\n        };\n      }));\n    }\n    // Filter Analytics Data\n    filterAnalyticsData(payload, type, listOfNames) {\n      const fromDate = payload['fromDate'];\n      const toDate = payload['toDate'];\n      const headers = {\n        headers: new HttpHeaders({\n          'Content-Type': 'application/json'\n        })\n      };\n      let listParams = '';\n      listOfNames.forEach(name => {\n        listParams += `&listOfNames=${encodeURIComponent(name)}`;\n      });\n      const url = `${this.apiServiceUrl}/ava/force/analytics/usecaseoragent?dateStart=${fromDate}&dateEnd=${toDate}&type=${type}${listParams}`;\n      return this.http.get(url, headers);\n    }\n    // Download Methods\n    downloadChartExcel(date, analyticsName, fileFormat) {\n      const fromDate = date['fromDate'];\n      const toDate = date['toDate'];\n      const headers = this.getDownloadHeaders();\n      const url = `${this.apiServiceUrl}/ava/force/analytics/download?dateStart=${fromDate}&dateEnd=${toDate}&analyticsName=${analyticsName}&fileFormat=${fileFormat}`;\n      this.http.get(url, {\n        headers,\n        responseType: 'blob'\n      }).subscribe({\n        next: blob => {\n          const fileName = `${analyticsName}.xlsx`;\n          const link = document.createElement('a');\n          link.href = window.URL.createObjectURL(blob);\n          link.download = fileName;\n          link.click();\n          window.URL.revokeObjectURL(link.href);\n        },\n        error: error => {\n          console.error('Error downloading chart excel:', error);\n        }\n      });\n    }\n    downloadDump(date) {\n      const fromDate = date['fromDate'];\n      const toDate = date['toDate'];\n      const headers = this.getDownloadHeaders();\n      const url = `${this.apiServiceUrl}/ava/force/analytics/dump/data/download?dateStart=${fromDate}&dateEnd=${toDate}`;\n      this.http.get(url, {\n        headers,\n        responseType: 'blob'\n      }).subscribe({\n        next: blob => {\n          const fileName = 'AnalyticsDump.xlsx';\n          const link = document.createElement('a');\n          link.href = window.URL.createObjectURL(blob);\n          link.download = fileName;\n          link.click();\n          window.URL.revokeObjectURL(link.href);\n        },\n        error: error => {\n          console.error('Error downloading data dump:', error);\n        }\n      });\n    }\n    downloadUsecaseChartExcel(date, analyticsName, fileFormat, usecases) {\n      const fromDate = date['fromDate'];\n      const toDate = date['toDate'];\n      const headers = new HttpHeaders({\n        'Content-Type': 'application/json'\n      });\n      const url = `${this.apiServiceUrl}/ava/force/analytics/download?dateStart=${fromDate}&dateEnd=${toDate}&analyticsName=${analyticsName}&fileFormat=${fileFormat}&usecases=${usecases}`;\n      this.http.get(url, {\n        headers,\n        responseType: 'blob'\n      }).subscribe({\n        next: blob => {\n          const fileName = `${analyticsName}.xlsx`;\n          const link = document.createElement('a');\n          link.href = window.URL.createObjectURL(blob);\n          link.download = fileName;\n          link.click();\n          window.URL.revokeObjectURL(link.href);\n        },\n        error: error => {\n          console.error('Error downloading the file', error);\n        }\n      });\n    }\n    downloadAgenticAIExcel(date, analyticsName, fileFormat) {\n      const fromDate = date['fromDate'];\n      const toDate = date['toDate'];\n      const headers = this.getDownloadHeaders();\n      const url = `${this.apiServiceUrl}/ava/force/analytics/download/agenticAI?dateStart=${fromDate}&dateEnd=${toDate}&analytics_name=${analyticsName}&fileFormat=${fileFormat}`;\n      this.http.get(url, {\n        headers,\n        responseType: 'blob'\n      }).subscribe({\n        next: blob => {\n          const fileName = `${analyticsName}.xlsx`;\n          const link = document.createElement('a');\n          link.href = window.URL.createObjectURL(blob);\n          link.download = fileName;\n          link.click();\n          window.URL.revokeObjectURL(link.href);\n        },\n        error: error => {\n          console.error('Error downloading agenticAI excel:', error);\n        }\n      });\n    }\n    static ɵfac = function AnalyticsService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AnalyticsService)(i0.ɵɵinject(i1.HttpClient));\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AnalyticsService,\n      factory: AnalyticsService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return AnalyticsService;\n})();", "map": {"version": 3, "names": ["HttpHeaders", "map", "environment", "AnalyticsService", "http", "apiServiceUrl", "consoleApi", "constructor", "getHeaders", "headers", "getDownloadHeaders", "getAllAnalyticsData", "payload", "fromDate", "toDate", "url", "get", "totalRequestCount", "result", "totalBugs", "totalUnitTest", "totalStory", "totalCodeOptimization", "pipe", "response", "totalStoryOrEpic", "useCaseLevelAnalytics", "for<PERSON>ach", "data", "useCaseCode", "count", "getLinesOfCodeProcessed", "categories", "ySeries", "linesOfCodeAnalytics", "push", "date", "getTopUseCases", "slice", "getTotalNumberOfRequests", "numberOfRequestAnalytics", "requestCount", "getTopLanguages", "programmingLanguageAnalytics", "programmingLanguage", "getUserResponse", "zeroCount", "userResponseAnalytics", "percentage", "getUserUsageAnalytics", "getUserActivity", "getResponseTime", "getAgenticAIAnalytics", "toolUsage", "filter", "tool", "usageCount", "topAgents", "validAgents", "agentCreated", "agent", "teamName", "top3AgentsCreated", "totalAgentsCreated", "totalAgentsReused", "totalTools", "studioUsage", "agentMetrics", "toolAnalytics", "adoptionRate", "userConsumption", "userActivityStats", "filterAnalyticsData", "type", "listOfNames", "listParams", "name", "encodeURIComponent", "downloadChartExcel", "analyticsName", "fileFormat", "responseType", "subscribe", "next", "blob", "fileName", "link", "document", "createElement", "href", "window", "URL", "createObjectURL", "download", "click", "revokeObjectURL", "error", "console", "downloadDump", "downloadUsecaseChartExcel", "usecases", "downloadAgenticAIExcel", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\shared\\services\\analytics.service.ts"], "sourcesContent": ["import { HttpClient, HttpHeaders } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { map, Observable } from 'rxjs';\r\nimport { environment } from '../../../environments/environment';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class AnalyticsService {\r\n  private apiServiceUrl = environment?.consoleApi;\r\n\r\n  constructor(private http: HttpClient) { }\r\n  private getHeaders(): { headers: HttpHeaders } {\r\n    return {\r\n      headers: new HttpHeaders({\r\n        'Content-Type': 'application/json',\r\n      })\r\n    };\r\n  }\r\n\r\n  private getDownloadHeaders(): HttpHeaders {\r\n    return new HttpHeaders({\r\n      'Content-Type': 'application/json',\r\n    });\r\n  }\r\n\r\n  public getAllAnalyticsData(payload: any): Observable<any> {\r\n    const fromDate = payload['fromDate'];\r\n    const toDate = payload['toDate'];\r\n    const url = `${this.apiServiceUrl}/ava/force/analytics?dateStart=${fromDate}&dateEnd=${toDate}`;\r\n\r\n    return this.http.get(url, this.getHeaders());\r\n  }\r\n\r\n  // Usecase Analytics Methods\r\n  public totalRequestCount(payload: any): Observable<any> {\r\n    const fromDate = payload['fromDate'];\r\n    const toDate = payload['toDate'];\r\n    const headers = {\r\n      headers: new HttpHeaders({\r\n        'Content-Type': 'application/json'\r\n      })\r\n    };\r\n    const url = `${this.apiServiceUrl}/ava/force/analytics?dateStart=${fromDate}&dateEnd=${toDate}`;\r\n\r\n    const result = {\r\n      totalBugs: 0,\r\n      totalUnitTest: 0,\r\n      totalStory: 0,\r\n      totalCodeOptimization: 0\r\n    };\r\n\r\n    return this.http.get(url, headers).pipe(\r\n      map((response: any) => {\r\n        let totalStoryOrEpic = 0;\r\n        if (response?.useCaseLevelAnalytics) {\r\n          response.useCaseLevelAnalytics.forEach((data: any) => {\r\n            switch (data.useCaseCode) {\r\n              case 'FIND_BUG':\r\n                result.totalBugs = data.count;\r\n                break;\r\n              case 'GENERATE_UNIT_TESTS':\r\n                result.totalUnitTest = data.count;\r\n                break;\r\n              case 'STORY_OR_EPIC':\r\n              case 'CREATE_STORY':\r\n                totalStoryOrEpic += data.count;\r\n                break;\r\n              case 'OPTIMIZE_CODE':\r\n                result.totalCodeOptimization = data.count;\r\n                break;\r\n            }\r\n          });\r\n        }\r\n        result.totalStory = totalStoryOrEpic;\r\n        return result;\r\n      })\r\n    );\r\n  }\r\n\r\n  public getLinesOfCodeProcessed(payload: any): Observable<any> {\r\n    const fromDate = payload['fromDate'];\r\n    const toDate = payload['toDate'];\r\n    const headers = {\r\n      headers: new HttpHeaders({\r\n        'Content-Type': 'application/json',\r\n      })\r\n    };\r\n    const url = `${this.apiServiceUrl}/ava/force/analytics?dateStart=${fromDate}&dateEnd=${toDate}`;\r\n\r\n    const result: any = {\r\n      categories: [],\r\n      ySeries: [],\r\n    };\r\n\r\n    return this.http.get(url, headers).pipe(\r\n      map((response: any) => {\r\n        if (response?.linesOfCodeAnalytics) {\r\n          response.linesOfCodeAnalytics.forEach((data: any) => {\r\n            result.categories.push(data.date);\r\n            result.ySeries.push(data.count);\r\n          });\r\n        }\r\n        return result;\r\n      })\r\n    );\r\n  }\r\n\r\n  public getTopUseCases(payload: any): Observable<any> {\r\n    const fromDate = payload['fromDate'];\r\n    const toDate = payload['toDate'];\r\n    const headers = {\r\n      headers: new HttpHeaders({\r\n        'Content-Type': 'application/json',\r\n      })\r\n    };\r\n    const url = `${this.apiServiceUrl}/ava/force/analytics?dateStart=${fromDate}&dateEnd=${toDate}`;\r\n\r\n    const result: any = {\r\n      categories: [],\r\n      ySeries: [],\r\n    };\r\n\r\n    return this.http.get(url, headers).pipe(\r\n      map((response: any) => {\r\n        if (response?.useCaseLevelAnalytics) {\r\n          response.useCaseLevelAnalytics.slice(0, 5).forEach((data: any) => {\r\n            result.categories.push(data.useCaseCode);\r\n            result.ySeries.push(data.count);\r\n          });\r\n        }\r\n        return result;\r\n      })\r\n    );\r\n  }\r\n\r\n  public getTotalNumberOfRequests(payload: any): Observable<any> {\r\n    const fromDate = payload['fromDate'];\r\n    const toDate = payload['toDate'];\r\n    const headers = {\r\n      headers: new HttpHeaders({\r\n        'Content-Type': 'application/json',\r\n      })\r\n    };\r\n    const url = `${this.apiServiceUrl}/ava/force/analytics?dateStart=${fromDate}&dateEnd=${toDate}`;\r\n\r\n    const result: any = {\r\n      categories: [],\r\n      ySeries: [],\r\n    };\r\n\r\n    return this.http.get(url, headers).pipe(\r\n      map((response: any) => {\r\n        if (response?.numberOfRequestAnalytics) {\r\n          response.numberOfRequestAnalytics.forEach((data: any) => {\r\n            result.categories.push(data.date);\r\n            result.ySeries.push(data.requestCount);\r\n          });\r\n        }\r\n        return result;\r\n      })\r\n    );\r\n  }\r\n\r\n  public getTopLanguages(payload: any): Observable<any> {\r\n    const fromDate = payload['fromDate'];\r\n    const toDate = payload['toDate'];\r\n    const headers = {\r\n      headers: new HttpHeaders({\r\n        'Content-Type': 'application/json',\r\n      })\r\n    };\r\n    const url = `${this.apiServiceUrl}/ava/force/analytics?dateStart=${fromDate}&dateEnd=${toDate}`;\r\n\r\n    const result: any = {\r\n      categories: [],\r\n      ySeries: [],\r\n    };\r\n\r\n    return this.http.get(url, headers).pipe(\r\n      map((response: any) => {\r\n        if (response?.programmingLanguageAnalytics) {\r\n          response.programmingLanguageAnalytics.slice(0, 5).forEach((data: any) => {\r\n            result.categories.push(data.programmingLanguage);\r\n            result.ySeries.push(data.count);\r\n          });\r\n        }\r\n        return result;\r\n      })\r\n    );\r\n  }\r\n\r\n  public getUserResponse(payload: any): Observable<any> {\r\n    const fromDate = payload['fromDate'];\r\n    const toDate = payload['toDate'];\r\n    const headers = {\r\n      headers: new HttpHeaders({\r\n        'Content-Type': 'application/json',\r\n      })\r\n    };\r\n    const url = `${this.apiServiceUrl}/ava/force/analytics?dateStart=${fromDate}&dateEnd=${toDate}`;\r\n\r\n    const result: any = {\r\n      categories: [],\r\n      ySeries: [],\r\n    };\r\n\r\n    return this.http.get(url, headers).pipe(\r\n      map((response: any) => {\r\n        let zeroCount = 0;\r\n        if (response?.userResponseAnalytics) {\r\n          response.userResponseAnalytics.forEach((data: any) => {\r\n            result.categories.push(data.response);\r\n            result.ySeries.push([data.response, data.percentage]);\r\n            if (data.percentage === 0) {\r\n              zeroCount++;\r\n            }\r\n          });\r\n        }\r\n\r\n        // Return empty result if all percentages are 0\r\n        if (zeroCount === 3) {\r\n          return { categories: [], ySeries: [] };\r\n        }\r\n        return result;\r\n      })\r\n    );\r\n  }\r\n\r\n  public getUserUsageAnalytics(payload: any): Observable<any> {\r\n    const fromDate = payload['fromDate'];\r\n    const toDate = payload['toDate'];\r\n    const headers = {\r\n      headers: new HttpHeaders({\r\n        'Content-Type': 'application/json',\r\n      })\r\n    };\r\n    const url = `${this.apiServiceUrl}/ava/force/user/analytics?dateStart=${fromDate}&dateEnd=${toDate}`;\r\n    return this.http.get(url, headers);\r\n  }\r\n\r\n  public getUserActivity(payload: any): Observable<any> {\r\n    const fromDate = payload['fromDate'];\r\n    const toDate = payload['toDate'];\r\n    const headers = {\r\n      headers: new HttpHeaders({\r\n        'Content-Type': 'application/json',\r\n      })\r\n    };\r\n    const url = `${this.apiServiceUrl}/ava/force/useractivity/analytics?dateStart=${fromDate}&dateEnd=${toDate}`;\r\n    return this.http.get(url, headers);\r\n  }\r\n\r\n  public getResponseTime(payload: any): Observable<any> {\r\n    const fromDate = payload['fromDate'];\r\n    const toDate = payload['toDate'];\r\n    const headers = {\r\n      headers: new HttpHeaders({\r\n        'Content-Type': 'application/json',\r\n      })\r\n    };\r\n    const url = `${this.apiServiceUrl}/ava/force/apiresponse/analytics?dateStart=${fromDate}&dateEnd=${toDate}`;\r\n    return this.http.get(url, headers);\r\n  }\r\n\r\n  // Agent Analytics Methods\r\n  public getAgenticAIAnalytics(payload: any): Observable<any> {\r\n    const fromDate = payload['fromDate'];\r\n    const toDate = payload['toDate'];\r\n    const headers = {\r\n      headers: new HttpHeaders({\r\n        'Content-Type': 'application/json',\r\n      })\r\n    };\r\n    const url = `${this.apiServiceUrl}/ava/force/analytics/agenticAI?dateStart=${fromDate}&dateEnd=${toDate}`;\r\n\r\n    return this.http.get(url, headers).pipe(\r\n      map((response: any) => {\r\n        const toolUsage = (response.toolUsage || []).filter((tool: any) =>\r\n          tool.usageCount !== null && tool.usageCount > 0\r\n        );\r\n        const topAgents = (response.topAgents || []).slice(0, 5);\r\n        const validAgents = (response.agentCreated || []).filter((agent: any) =>\r\n          agent.teamName && agent.usageCount > 0\r\n        );\r\n        const top3AgentsCreated = validAgents.slice(0, 3);\r\n\r\n        return {\r\n          totalAgentsCreated: response.totalAgentsCreated,\r\n          totalAgentsReused: response.totalAgentsReused,\r\n          totalTools: response.totalTools,\r\n          studioUsage: response.studioUsage,\r\n          topAgents: topAgents,\r\n          agentCreated: top3AgentsCreated,\r\n          agentMetrics: response.agentMetrics,\r\n          toolAnalytics: response.toolAnalytics,\r\n          toolUsage: toolUsage,\r\n          adoptionRate: response.adoptionRate,\r\n          userConsumption: response.userConsumption,\r\n          userActivityStats: response.userActivityStats,\r\n        };\r\n      })\r\n    );\r\n  }\r\n\r\n  // Filter Analytics Data\r\n  public filterAnalyticsData(payload: any, type: string, listOfNames: string[]): Observable<any> {\r\n    const fromDate = payload['fromDate'];\r\n    const toDate = payload['toDate'];\r\n    const headers = {\r\n      headers: new HttpHeaders({\r\n        'Content-Type': 'application/json',\r\n      })\r\n    };\r\n\r\n    let listParams = '';\r\n    listOfNames.forEach(name => {\r\n      listParams += `&listOfNames=${encodeURIComponent(name)}`;\r\n    });\r\n\r\n    const url = `${this.apiServiceUrl}/ava/force/analytics/usecaseoragent?dateStart=${fromDate}&dateEnd=${toDate}&type=${type}${listParams}`;\r\n    return this.http.get(url, headers);\r\n  }\r\n\r\n  // Download Methods\r\n  public downloadChartExcel(date: any, analyticsName: string, fileFormat: string): void {\r\n    const fromDate = date['fromDate'];\r\n    const toDate = date['toDate'];\r\n\r\n    const headers = this.getDownloadHeaders();\r\n    const url = `${this.apiServiceUrl}/ava/force/analytics/download?dateStart=${fromDate}&dateEnd=${toDate}&analyticsName=${analyticsName}&fileFormat=${fileFormat}`;\r\n\r\n    this.http.get(url, { headers, responseType: 'blob' }).subscribe({\r\n      next: (blob) => {\r\n        const fileName = `${analyticsName}.xlsx`;\r\n        const link = document.createElement('a');\r\n        link.href = window.URL.createObjectURL(blob);\r\n        link.download = fileName;\r\n        link.click();\r\n        window.URL.revokeObjectURL(link.href);\r\n      },\r\n      error: (error) => {\r\n        console.error('Error downloading chart excel:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  public downloadDump(date: any): void {\r\n    const fromDate = date['fromDate'];\r\n    const toDate = date['toDate'];\r\n\r\n    const headers = this.getDownloadHeaders();\r\n    const url = `${this.apiServiceUrl}/ava/force/analytics/dump/data/download?dateStart=${fromDate}&dateEnd=${toDate}`;\r\n\r\n    this.http.get(url, { headers, responseType: 'blob' }).subscribe({\r\n      next: (blob) => {\r\n        const fileName = 'AnalyticsDump.xlsx';\r\n        const link = document.createElement('a');\r\n        link.href = window.URL.createObjectURL(blob);\r\n        link.download = fileName;\r\n        link.click();\r\n        window.URL.revokeObjectURL(link.href);\r\n      },\r\n      error: (error) => {\r\n        console.error('Error downloading data dump:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  public downloadUsecaseChartExcel(date: any, analyticsName: string, fileFormat: string, usecases: string): void {\r\n    const fromDate = date['fromDate'];\r\n    const toDate = date['toDate'];\r\n\r\n    const headers = new HttpHeaders({\r\n      'Content-Type': 'application/json',\r\n    });\r\n\r\n    const url = `${this.apiServiceUrl}/ava/force/analytics/download?dateStart=${fromDate}&dateEnd=${toDate}&analyticsName=${analyticsName}&fileFormat=${fileFormat}&usecases=${usecases}`;\r\n\r\n    this.http.get(url, { headers, responseType: 'blob' }).subscribe({\r\n      next: (blob) => {\r\n        const fileName = `${analyticsName}.xlsx`;\r\n        const link = document.createElement('a');\r\n        link.href = window.URL.createObjectURL(blob);\r\n        link.download = fileName;\r\n        link.click();\r\n        window.URL.revokeObjectURL(link.href);\r\n      },\r\n      error: (error) => {\r\n        console.error('Error downloading the file', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  public downloadAgenticAIExcel(date: any, analyticsName: string, fileFormat: string): void {\r\n    const fromDate = date['fromDate'];\r\n    const toDate = date['toDate'];\r\n\r\n    const headers = this.getDownloadHeaders();\r\n    const url = `${this.apiServiceUrl}/ava/force/analytics/download/agenticAI?dateStart=${fromDate}&dateEnd=${toDate}&analytics_name=${analyticsName}&fileFormat=${fileFormat}`;\r\n\r\n    this.http.get(url, { headers, responseType: 'blob' }).subscribe({\r\n      next: (blob) => {\r\n        const fileName = `${analyticsName}.xlsx`;\r\n        const link = document.createElement('a');\r\n        link.href = window.URL.createObjectURL(blob);\r\n        link.download = fileName;\r\n        link.click();\r\n        window.URL.revokeObjectURL(link.href);\r\n      },\r\n      error: (error) => {\r\n        console.error('Error downloading agenticAI excel:', error);\r\n      }\r\n    });\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,WAAW,QAAQ,sBAAsB;AAE9D,SAASC,GAAG,QAAoB,MAAM;AACtC,SAASC,WAAW,QAAQ,mCAAmC;;;AAK/D,WAAaC,gBAAgB;EAAvB,MAAOA,gBAAgB;IAGPC,IAAA;IAFZC,aAAa,GAAGH,WAAW,EAAEI,UAAU;IAE/CC,YAAoBH,IAAgB;MAAhB,KAAAA,IAAI,GAAJA,IAAI;IAAgB;IAChCI,UAAUA,CAAA;MAChB,OAAO;QACLC,OAAO,EAAE,IAAIT,WAAW,CAAC;UACvB,cAAc,EAAE;SACjB;OACF;IACH;IAEQU,kBAAkBA,CAAA;MACxB,OAAO,IAAIV,WAAW,CAAC;QACrB,cAAc,EAAE;OACjB,CAAC;IACJ;IAEOW,mBAAmBA,CAACC,OAAY;MACrC,MAAMC,QAAQ,GAAGD,OAAO,CAAC,UAAU,CAAC;MACpC,MAAME,MAAM,GAAGF,OAAO,CAAC,QAAQ,CAAC;MAChC,MAAMG,GAAG,GAAG,GAAG,IAAI,CAACV,aAAa,kCAAkCQ,QAAQ,YAAYC,MAAM,EAAE;MAE/F,OAAO,IAAI,CAACV,IAAI,CAACY,GAAG,CAACD,GAAG,EAAE,IAAI,CAACP,UAAU,EAAE,CAAC;IAC9C;IAEA;IACOS,iBAAiBA,CAACL,OAAY;MACnC,MAAMC,QAAQ,GAAGD,OAAO,CAAC,UAAU,CAAC;MACpC,MAAME,MAAM,GAAGF,OAAO,CAAC,QAAQ,CAAC;MAChC,MAAMH,OAAO,GAAG;QACdA,OAAO,EAAE,IAAIT,WAAW,CAAC;UACvB,cAAc,EAAE;SACjB;OACF;MACD,MAAMe,GAAG,GAAG,GAAG,IAAI,CAACV,aAAa,kCAAkCQ,QAAQ,YAAYC,MAAM,EAAE;MAE/F,MAAMI,MAAM,GAAG;QACbC,SAAS,EAAE,CAAC;QACZC,aAAa,EAAE,CAAC;QAChBC,UAAU,EAAE,CAAC;QACbC,qBAAqB,EAAE;OACxB;MAED,OAAO,IAAI,CAAClB,IAAI,CAACY,GAAG,CAACD,GAAG,EAAEN,OAAO,CAAC,CAACc,IAAI,CACrCtB,GAAG,CAAEuB,QAAa,IAAI;QACpB,IAAIC,gBAAgB,GAAG,CAAC;QACxB,IAAID,QAAQ,EAAEE,qBAAqB,EAAE;UACnCF,QAAQ,CAACE,qBAAqB,CAACC,OAAO,CAAEC,IAAS,IAAI;YACnD,QAAQA,IAAI,CAACC,WAAW;cACtB,KAAK,UAAU;gBACbX,MAAM,CAACC,SAAS,GAAGS,IAAI,CAACE,KAAK;gBAC7B;cACF,KAAK,qBAAqB;gBACxBZ,MAAM,CAACE,aAAa,GAAGQ,IAAI,CAACE,KAAK;gBACjC;cACF,KAAK,eAAe;cACpB,KAAK,cAAc;gBACjBL,gBAAgB,IAAIG,IAAI,CAACE,KAAK;gBAC9B;cACF,KAAK,eAAe;gBAClBZ,MAAM,CAACI,qBAAqB,GAAGM,IAAI,CAACE,KAAK;gBACzC;YACJ;UACF,CAAC,CAAC;QACJ;QACAZ,MAAM,CAACG,UAAU,GAAGI,gBAAgB;QACpC,OAAOP,MAAM;MACf,CAAC,CAAC,CACH;IACH;IAEOa,uBAAuBA,CAACnB,OAAY;MACzC,MAAMC,QAAQ,GAAGD,OAAO,CAAC,UAAU,CAAC;MACpC,MAAME,MAAM,GAAGF,OAAO,CAAC,QAAQ,CAAC;MAChC,MAAMH,OAAO,GAAG;QACdA,OAAO,EAAE,IAAIT,WAAW,CAAC;UACvB,cAAc,EAAE;SACjB;OACF;MACD,MAAMe,GAAG,GAAG,GAAG,IAAI,CAACV,aAAa,kCAAkCQ,QAAQ,YAAYC,MAAM,EAAE;MAE/F,MAAMI,MAAM,GAAQ;QAClBc,UAAU,EAAE,EAAE;QACdC,OAAO,EAAE;OACV;MAED,OAAO,IAAI,CAAC7B,IAAI,CAACY,GAAG,CAACD,GAAG,EAAEN,OAAO,CAAC,CAACc,IAAI,CACrCtB,GAAG,CAAEuB,QAAa,IAAI;QACpB,IAAIA,QAAQ,EAAEU,oBAAoB,EAAE;UAClCV,QAAQ,CAACU,oBAAoB,CAACP,OAAO,CAAEC,IAAS,IAAI;YAClDV,MAAM,CAACc,UAAU,CAACG,IAAI,CAACP,IAAI,CAACQ,IAAI,CAAC;YACjClB,MAAM,CAACe,OAAO,CAACE,IAAI,CAACP,IAAI,CAACE,KAAK,CAAC;UACjC,CAAC,CAAC;QACJ;QACA,OAAOZ,MAAM;MACf,CAAC,CAAC,CACH;IACH;IAEOmB,cAAcA,CAACzB,OAAY;MAChC,MAAMC,QAAQ,GAAGD,OAAO,CAAC,UAAU,CAAC;MACpC,MAAME,MAAM,GAAGF,OAAO,CAAC,QAAQ,CAAC;MAChC,MAAMH,OAAO,GAAG;QACdA,OAAO,EAAE,IAAIT,WAAW,CAAC;UACvB,cAAc,EAAE;SACjB;OACF;MACD,MAAMe,GAAG,GAAG,GAAG,IAAI,CAACV,aAAa,kCAAkCQ,QAAQ,YAAYC,MAAM,EAAE;MAE/F,MAAMI,MAAM,GAAQ;QAClBc,UAAU,EAAE,EAAE;QACdC,OAAO,EAAE;OACV;MAED,OAAO,IAAI,CAAC7B,IAAI,CAACY,GAAG,CAACD,GAAG,EAAEN,OAAO,CAAC,CAACc,IAAI,CACrCtB,GAAG,CAAEuB,QAAa,IAAI;QACpB,IAAIA,QAAQ,EAAEE,qBAAqB,EAAE;UACnCF,QAAQ,CAACE,qBAAqB,CAACY,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACX,OAAO,CAAEC,IAAS,IAAI;YAC/DV,MAAM,CAACc,UAAU,CAACG,IAAI,CAACP,IAAI,CAACC,WAAW,CAAC;YACxCX,MAAM,CAACe,OAAO,CAACE,IAAI,CAACP,IAAI,CAACE,KAAK,CAAC;UACjC,CAAC,CAAC;QACJ;QACA,OAAOZ,MAAM;MACf,CAAC,CAAC,CACH;IACH;IAEOqB,wBAAwBA,CAAC3B,OAAY;MAC1C,MAAMC,QAAQ,GAAGD,OAAO,CAAC,UAAU,CAAC;MACpC,MAAME,MAAM,GAAGF,OAAO,CAAC,QAAQ,CAAC;MAChC,MAAMH,OAAO,GAAG;QACdA,OAAO,EAAE,IAAIT,WAAW,CAAC;UACvB,cAAc,EAAE;SACjB;OACF;MACD,MAAMe,GAAG,GAAG,GAAG,IAAI,CAACV,aAAa,kCAAkCQ,QAAQ,YAAYC,MAAM,EAAE;MAE/F,MAAMI,MAAM,GAAQ;QAClBc,UAAU,EAAE,EAAE;QACdC,OAAO,EAAE;OACV;MAED,OAAO,IAAI,CAAC7B,IAAI,CAACY,GAAG,CAACD,GAAG,EAAEN,OAAO,CAAC,CAACc,IAAI,CACrCtB,GAAG,CAAEuB,QAAa,IAAI;QACpB,IAAIA,QAAQ,EAAEgB,wBAAwB,EAAE;UACtChB,QAAQ,CAACgB,wBAAwB,CAACb,OAAO,CAAEC,IAAS,IAAI;YACtDV,MAAM,CAACc,UAAU,CAACG,IAAI,CAACP,IAAI,CAACQ,IAAI,CAAC;YACjClB,MAAM,CAACe,OAAO,CAACE,IAAI,CAACP,IAAI,CAACa,YAAY,CAAC;UACxC,CAAC,CAAC;QACJ;QACA,OAAOvB,MAAM;MACf,CAAC,CAAC,CACH;IACH;IAEOwB,eAAeA,CAAC9B,OAAY;MACjC,MAAMC,QAAQ,GAAGD,OAAO,CAAC,UAAU,CAAC;MACpC,MAAME,MAAM,GAAGF,OAAO,CAAC,QAAQ,CAAC;MAChC,MAAMH,OAAO,GAAG;QACdA,OAAO,EAAE,IAAIT,WAAW,CAAC;UACvB,cAAc,EAAE;SACjB;OACF;MACD,MAAMe,GAAG,GAAG,GAAG,IAAI,CAACV,aAAa,kCAAkCQ,QAAQ,YAAYC,MAAM,EAAE;MAE/F,MAAMI,MAAM,GAAQ;QAClBc,UAAU,EAAE,EAAE;QACdC,OAAO,EAAE;OACV;MAED,OAAO,IAAI,CAAC7B,IAAI,CAACY,GAAG,CAACD,GAAG,EAAEN,OAAO,CAAC,CAACc,IAAI,CACrCtB,GAAG,CAAEuB,QAAa,IAAI;QACpB,IAAIA,QAAQ,EAAEmB,4BAA4B,EAAE;UAC1CnB,QAAQ,CAACmB,4BAA4B,CAACL,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACX,OAAO,CAAEC,IAAS,IAAI;YACtEV,MAAM,CAACc,UAAU,CAACG,IAAI,CAACP,IAAI,CAACgB,mBAAmB,CAAC;YAChD1B,MAAM,CAACe,OAAO,CAACE,IAAI,CAACP,IAAI,CAACE,KAAK,CAAC;UACjC,CAAC,CAAC;QACJ;QACA,OAAOZ,MAAM;MACf,CAAC,CAAC,CACH;IACH;IAEO2B,eAAeA,CAACjC,OAAY;MACjC,MAAMC,QAAQ,GAAGD,OAAO,CAAC,UAAU,CAAC;MACpC,MAAME,MAAM,GAAGF,OAAO,CAAC,QAAQ,CAAC;MAChC,MAAMH,OAAO,GAAG;QACdA,OAAO,EAAE,IAAIT,WAAW,CAAC;UACvB,cAAc,EAAE;SACjB;OACF;MACD,MAAMe,GAAG,GAAG,GAAG,IAAI,CAACV,aAAa,kCAAkCQ,QAAQ,YAAYC,MAAM,EAAE;MAE/F,MAAMI,MAAM,GAAQ;QAClBc,UAAU,EAAE,EAAE;QACdC,OAAO,EAAE;OACV;MAED,OAAO,IAAI,CAAC7B,IAAI,CAACY,GAAG,CAACD,GAAG,EAAEN,OAAO,CAAC,CAACc,IAAI,CACrCtB,GAAG,CAAEuB,QAAa,IAAI;QACpB,IAAIsB,SAAS,GAAG,CAAC;QACjB,IAAItB,QAAQ,EAAEuB,qBAAqB,EAAE;UACnCvB,QAAQ,CAACuB,qBAAqB,CAACpB,OAAO,CAAEC,IAAS,IAAI;YACnDV,MAAM,CAACc,UAAU,CAACG,IAAI,CAACP,IAAI,CAACJ,QAAQ,CAAC;YACrCN,MAAM,CAACe,OAAO,CAACE,IAAI,CAAC,CAACP,IAAI,CAACJ,QAAQ,EAAEI,IAAI,CAACoB,UAAU,CAAC,CAAC;YACrD,IAAIpB,IAAI,CAACoB,UAAU,KAAK,CAAC,EAAE;cACzBF,SAAS,EAAE;YACb;UACF,CAAC,CAAC;QACJ;QAEA;QACA,IAAIA,SAAS,KAAK,CAAC,EAAE;UACnB,OAAO;YAAEd,UAAU,EAAE,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;QACxC;QACA,OAAOf,MAAM;MACf,CAAC,CAAC,CACH;IACH;IAEO+B,qBAAqBA,CAACrC,OAAY;MACvC,MAAMC,QAAQ,GAAGD,OAAO,CAAC,UAAU,CAAC;MACpC,MAAME,MAAM,GAAGF,OAAO,CAAC,QAAQ,CAAC;MAChC,MAAMH,OAAO,GAAG;QACdA,OAAO,EAAE,IAAIT,WAAW,CAAC;UACvB,cAAc,EAAE;SACjB;OACF;MACD,MAAMe,GAAG,GAAG,GAAG,IAAI,CAACV,aAAa,uCAAuCQ,QAAQ,YAAYC,MAAM,EAAE;MACpG,OAAO,IAAI,CAACV,IAAI,CAACY,GAAG,CAACD,GAAG,EAAEN,OAAO,CAAC;IACpC;IAEOyC,eAAeA,CAACtC,OAAY;MACjC,MAAMC,QAAQ,GAAGD,OAAO,CAAC,UAAU,CAAC;MACpC,MAAME,MAAM,GAAGF,OAAO,CAAC,QAAQ,CAAC;MAChC,MAAMH,OAAO,GAAG;QACdA,OAAO,EAAE,IAAIT,WAAW,CAAC;UACvB,cAAc,EAAE;SACjB;OACF;MACD,MAAMe,GAAG,GAAG,GAAG,IAAI,CAACV,aAAa,+CAA+CQ,QAAQ,YAAYC,MAAM,EAAE;MAC5G,OAAO,IAAI,CAACV,IAAI,CAACY,GAAG,CAACD,GAAG,EAAEN,OAAO,CAAC;IACpC;IAEO0C,eAAeA,CAACvC,OAAY;MACjC,MAAMC,QAAQ,GAAGD,OAAO,CAAC,UAAU,CAAC;MACpC,MAAME,MAAM,GAAGF,OAAO,CAAC,QAAQ,CAAC;MAChC,MAAMH,OAAO,GAAG;QACdA,OAAO,EAAE,IAAIT,WAAW,CAAC;UACvB,cAAc,EAAE;SACjB;OACF;MACD,MAAMe,GAAG,GAAG,GAAG,IAAI,CAACV,aAAa,8CAA8CQ,QAAQ,YAAYC,MAAM,EAAE;MAC3G,OAAO,IAAI,CAACV,IAAI,CAACY,GAAG,CAACD,GAAG,EAAEN,OAAO,CAAC;IACpC;IAEA;IACO2C,qBAAqBA,CAACxC,OAAY;MACvC,MAAMC,QAAQ,GAAGD,OAAO,CAAC,UAAU,CAAC;MACpC,MAAME,MAAM,GAAGF,OAAO,CAAC,QAAQ,CAAC;MAChC,MAAMH,OAAO,GAAG;QACdA,OAAO,EAAE,IAAIT,WAAW,CAAC;UACvB,cAAc,EAAE;SACjB;OACF;MACD,MAAMe,GAAG,GAAG,GAAG,IAAI,CAACV,aAAa,4CAA4CQ,QAAQ,YAAYC,MAAM,EAAE;MAEzG,OAAO,IAAI,CAACV,IAAI,CAACY,GAAG,CAACD,GAAG,EAAEN,OAAO,CAAC,CAACc,IAAI,CACrCtB,GAAG,CAAEuB,QAAa,IAAI;QACpB,MAAM6B,SAAS,GAAG,CAAC7B,QAAQ,CAAC6B,SAAS,IAAI,EAAE,EAAEC,MAAM,CAAEC,IAAS,IAC5DA,IAAI,CAACC,UAAU,KAAK,IAAI,IAAID,IAAI,CAACC,UAAU,GAAG,CAAC,CAChD;QACD,MAAMC,SAAS,GAAG,CAACjC,QAAQ,CAACiC,SAAS,IAAI,EAAE,EAAEnB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QACxD,MAAMoB,WAAW,GAAG,CAAClC,QAAQ,CAACmC,YAAY,IAAI,EAAE,EAAEL,MAAM,CAAEM,KAAU,IAClEA,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACJ,UAAU,GAAG,CAAC,CACvC;QACD,MAAMM,iBAAiB,GAAGJ,WAAW,CAACpB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAEjD,OAAO;UACLyB,kBAAkB,EAAEvC,QAAQ,CAACuC,kBAAkB;UAC/CC,iBAAiB,EAAExC,QAAQ,CAACwC,iBAAiB;UAC7CC,UAAU,EAAEzC,QAAQ,CAACyC,UAAU;UAC/BC,WAAW,EAAE1C,QAAQ,CAAC0C,WAAW;UACjCT,SAAS,EAAEA,SAAS;UACpBE,YAAY,EAAEG,iBAAiB;UAC/BK,YAAY,EAAE3C,QAAQ,CAAC2C,YAAY;UACnCC,aAAa,EAAE5C,QAAQ,CAAC4C,aAAa;UACrCf,SAAS,EAAEA,SAAS;UACpBgB,YAAY,EAAE7C,QAAQ,CAAC6C,YAAY;UACnCC,eAAe,EAAE9C,QAAQ,CAAC8C,eAAe;UACzCC,iBAAiB,EAAE/C,QAAQ,CAAC+C;SAC7B;MACH,CAAC,CAAC,CACH;IACH;IAEA;IACOC,mBAAmBA,CAAC5D,OAAY,EAAE6D,IAAY,EAAEC,WAAqB;MAC1E,MAAM7D,QAAQ,GAAGD,OAAO,CAAC,UAAU,CAAC;MACpC,MAAME,MAAM,GAAGF,OAAO,CAAC,QAAQ,CAAC;MAChC,MAAMH,OAAO,GAAG;QACdA,OAAO,EAAE,IAAIT,WAAW,CAAC;UACvB,cAAc,EAAE;SACjB;OACF;MAED,IAAI2E,UAAU,GAAG,EAAE;MACnBD,WAAW,CAAC/C,OAAO,CAACiD,IAAI,IAAG;QACzBD,UAAU,IAAI,gBAAgBE,kBAAkB,CAACD,IAAI,CAAC,EAAE;MAC1D,CAAC,CAAC;MAEF,MAAM7D,GAAG,GAAG,GAAG,IAAI,CAACV,aAAa,iDAAiDQ,QAAQ,YAAYC,MAAM,SAAS2D,IAAI,GAAGE,UAAU,EAAE;MACxI,OAAO,IAAI,CAACvE,IAAI,CAACY,GAAG,CAACD,GAAG,EAAEN,OAAO,CAAC;IACpC;IAEA;IACOqE,kBAAkBA,CAAC1C,IAAS,EAAE2C,aAAqB,EAAEC,UAAkB;MAC5E,MAAMnE,QAAQ,GAAGuB,IAAI,CAAC,UAAU,CAAC;MACjC,MAAMtB,MAAM,GAAGsB,IAAI,CAAC,QAAQ,CAAC;MAE7B,MAAM3B,OAAO,GAAG,IAAI,CAACC,kBAAkB,EAAE;MACzC,MAAMK,GAAG,GAAG,GAAG,IAAI,CAACV,aAAa,2CAA2CQ,QAAQ,YAAYC,MAAM,kBAAkBiE,aAAa,eAAeC,UAAU,EAAE;MAEhK,IAAI,CAAC5E,IAAI,CAACY,GAAG,CAACD,GAAG,EAAE;QAAEN,OAAO;QAAEwE,YAAY,EAAE;MAAM,CAAE,CAAC,CAACC,SAAS,CAAC;QAC9DC,IAAI,EAAGC,IAAI,IAAI;UACb,MAAMC,QAAQ,GAAG,GAAGN,aAAa,OAAO;UACxC,MAAMO,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;UACxCF,IAAI,CAACG,IAAI,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACR,IAAI,CAAC;UAC5CE,IAAI,CAACO,QAAQ,GAAGR,QAAQ;UACxBC,IAAI,CAACQ,KAAK,EAAE;UACZJ,MAAM,CAACC,GAAG,CAACI,eAAe,CAACT,IAAI,CAACG,IAAI,CAAC;QACvC,CAAC;QACDO,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACxD;OACD,CAAC;IACJ;IAEOE,YAAYA,CAAC9D,IAAS;MAC3B,MAAMvB,QAAQ,GAAGuB,IAAI,CAAC,UAAU,CAAC;MACjC,MAAMtB,MAAM,GAAGsB,IAAI,CAAC,QAAQ,CAAC;MAE7B,MAAM3B,OAAO,GAAG,IAAI,CAACC,kBAAkB,EAAE;MACzC,MAAMK,GAAG,GAAG,GAAG,IAAI,CAACV,aAAa,qDAAqDQ,QAAQ,YAAYC,MAAM,EAAE;MAElH,IAAI,CAACV,IAAI,CAACY,GAAG,CAACD,GAAG,EAAE;QAAEN,OAAO;QAAEwE,YAAY,EAAE;MAAM,CAAE,CAAC,CAACC,SAAS,CAAC;QAC9DC,IAAI,EAAGC,IAAI,IAAI;UACb,MAAMC,QAAQ,GAAG,oBAAoB;UACrC,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;UACxCF,IAAI,CAACG,IAAI,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACR,IAAI,CAAC;UAC5CE,IAAI,CAACO,QAAQ,GAAGR,QAAQ;UACxBC,IAAI,CAACQ,KAAK,EAAE;UACZJ,MAAM,CAACC,GAAG,CAACI,eAAe,CAACT,IAAI,CAACG,IAAI,CAAC;QACvC,CAAC;QACDO,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACtD;OACD,CAAC;IACJ;IAEOG,yBAAyBA,CAAC/D,IAAS,EAAE2C,aAAqB,EAAEC,UAAkB,EAAEoB,QAAgB;MACrG,MAAMvF,QAAQ,GAAGuB,IAAI,CAAC,UAAU,CAAC;MACjC,MAAMtB,MAAM,GAAGsB,IAAI,CAAC,QAAQ,CAAC;MAE7B,MAAM3B,OAAO,GAAG,IAAIT,WAAW,CAAC;QAC9B,cAAc,EAAE;OACjB,CAAC;MAEF,MAAMe,GAAG,GAAG,GAAG,IAAI,CAACV,aAAa,2CAA2CQ,QAAQ,YAAYC,MAAM,kBAAkBiE,aAAa,eAAeC,UAAU,aAAaoB,QAAQ,EAAE;MAErL,IAAI,CAAChG,IAAI,CAACY,GAAG,CAACD,GAAG,EAAE;QAAEN,OAAO;QAAEwE,YAAY,EAAE;MAAM,CAAE,CAAC,CAACC,SAAS,CAAC;QAC9DC,IAAI,EAAGC,IAAI,IAAI;UACb,MAAMC,QAAQ,GAAG,GAAGN,aAAa,OAAO;UACxC,MAAMO,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;UACxCF,IAAI,CAACG,IAAI,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACR,IAAI,CAAC;UAC5CE,IAAI,CAACO,QAAQ,GAAGR,QAAQ;UACxBC,IAAI,CAACQ,KAAK,EAAE;UACZJ,MAAM,CAACC,GAAG,CAACI,eAAe,CAACT,IAAI,CAACG,IAAI,CAAC;QACvC,CAAC;QACDO,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QACpD;OACD,CAAC;IACJ;IAEOK,sBAAsBA,CAACjE,IAAS,EAAE2C,aAAqB,EAAEC,UAAkB;MAChF,MAAMnE,QAAQ,GAAGuB,IAAI,CAAC,UAAU,CAAC;MACjC,MAAMtB,MAAM,GAAGsB,IAAI,CAAC,QAAQ,CAAC;MAE7B,MAAM3B,OAAO,GAAG,IAAI,CAACC,kBAAkB,EAAE;MACzC,MAAMK,GAAG,GAAG,GAAG,IAAI,CAACV,aAAa,qDAAqDQ,QAAQ,YAAYC,MAAM,mBAAmBiE,aAAa,eAAeC,UAAU,EAAE;MAE3K,IAAI,CAAC5E,IAAI,CAACY,GAAG,CAACD,GAAG,EAAE;QAAEN,OAAO;QAAEwE,YAAY,EAAE;MAAM,CAAE,CAAC,CAACC,SAAS,CAAC;QAC9DC,IAAI,EAAGC,IAAI,IAAI;UACb,MAAMC,QAAQ,GAAG,GAAGN,aAAa,OAAO;UACxC,MAAMO,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;UACxCF,IAAI,CAACG,IAAI,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACR,IAAI,CAAC;UAC5CE,IAAI,CAACO,QAAQ,GAAGR,QAAQ;UACxBC,IAAI,CAACQ,KAAK,EAAE;UACZJ,MAAM,CAACC,GAAG,CAACI,eAAe,CAACT,IAAI,CAACG,IAAI,CAAC;QACvC,CAAC;QACDO,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC5D;OACD,CAAC;IACJ;;uCAtZW7F,gBAAgB,EAAAmG,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;;aAAhBtG,gBAAgB;MAAAuG,OAAA,EAAhBvG,gBAAgB,CAAAwG,IAAA;MAAAC,UAAA,EAFf;IAAM;;SAEPzG,gBAAgB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}