{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\auth\\interfaces\\auth-config.interface.ts"], "sourcesContent": ["export interface AuthConfig {\r\n  apiAuthUrl: string;\r\n  redirectUrl: string;\r\n  postLoginRedirectUrl?: string;\r\n  appName?: string;\r\n} "], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}