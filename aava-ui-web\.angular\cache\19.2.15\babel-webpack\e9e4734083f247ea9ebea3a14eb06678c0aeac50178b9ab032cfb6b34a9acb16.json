{"ast": null, "code": "// Helper function to safely get environment variables from window.env\n// It will throw an error if a key is not found, ensuring all required envs are present.\nconst getRequiredEnv = key => {\n  const envWindow = window;\n  const value = envWindow.env?.[key];\n  if (value === undefined || value === null) {\n    throw new Error(`Environment variable '${key}' is not defined in window.env.`);\n  }\n  return String(value); // Ensure the value is returned as a string\n};\n// ---\n// Dynamically retrieve the base URL first, as it's used for other URLs\nconst dynamicBaseUrl = getRequiredEnv('baseUrl');\n// ---\n// Environment configuration for Console Application\n// All values are dynamically retrieved from window.env.\n// If a variable is not found, an error will be thrown.\nexport const environment = {\n  production: false,\n  // This often remains a static build-time flag\n  // Application URLs (constructed dynamically)\n  elderWandUrl: getRequiredEnv('elderWandUrl'),\n  experienceStudioUrl: getRequiredEnv('experienceStudioUrl'),\n  productStudioUrl: getRequiredEnv('productStudioUrl'),\n  consoleRedirectUrl: getRequiredEnv('consoleRedirectUrl'),\n  consoleUrl: getRequiredEnv('consoleUrl'),\n  consoleRedirectUri: getRequiredEnv('consoleRedirectUri'),\n  // Often same as consoleRedirectUrl\n  // API Configuration (constructed dynamically or directly from window.env)\n  // accessKey: getRequiredEnv('accessKey'),\n  apiVersion: getRequiredEnv('apiVersion'),\n  baseUrl: getRequiredEnv('baseUrl'),\n  // The base URL itself\n  consoleApi: getRequiredEnv('consoleApi'),\n  consoleApiV2: getRequiredEnv('consoleApiV2'),\n  consoleApiAuthUrl: getRequiredEnv('consoleApiAuthUrl'),\n  consoleEmbeddingApi: getRequiredEnv('consoleEmbeddingApi'),\n  consoleInstructionApi: getRequiredEnv('consoleInstructionApi'),\n  consoleLangfuseUrl: getRequiredEnv('consoleLangfuseUrl'),\n  consoleTruelensUrl: getRequiredEnv('consoleTruelensUrl'),\n  consolePipelineApi: getRequiredEnv('consolePipelineApi'),\n  experienceApiUrl: getRequiredEnv('experienceApiUrl'),\n  productApiUrl: getRequiredEnv('productApiUrl'),\n  // Logging and App Specific\n  enableLogStreaming: getRequiredEnv('enableLogStreaming'),\n  logStreamingApiUrl: getRequiredEnv('logStreamingApiUrl'),\n  appVersion: getRequiredEnv('appVersion'),\n  workflowExecutionMode: getRequiredEnv('workflowExecutionMode'),\n  useBasicLogin: getRequiredEnv('useBasicLogin') // Assuming this is a string \"true\" or \"false\"\n};\n// Log the environment configuration for debugging purposes\nconsole.log('Environment configuration loaded dynamically:', environment);", "map": {"version": 3, "names": ["getRequiredEnv", "key", "envWindow", "window", "value", "env", "undefined", "Error", "String", "dynamicBaseUrl", "environment", "production", "elderWandUrl", "experienceStudioUrl", "productStudioUrl", "consoleRedirectUrl", "consoleUrl", "consoleRedirectUri", "apiVersion", "baseUrl", "consoleApi", "consoleApiV2", "consoleApiAuthUrl", "consoleEmbeddingApi", "consoleInstructionApi", "consoleLangfuseUrl", "consoleTruelensUrl", "consolePipelineApi", "experienceApiUrl", "productApiUrl", "enableLogStreaming", "logStreamingApiUrl", "appVersion", "workflowExecutionMode", "useBasicLogin", "console", "log"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\environments\\environment.ts"], "sourcesContent": ["// Helper function to safely get environment variables from window.env\r\n// It will throw an error if a key is not found, ensuring all required envs are present.\r\nconst getRequiredEnv = (key: string): string => {\r\n  // Extend the Window interface to include 'env'\r\n  interface EnvWindow extends Window {\r\n    env?: Record<string, string>;\r\n  }\r\n  const envWindow = window as EnvWindow;\r\n  const value = envWindow.env?.[key];\r\n  if (value === undefined || value === null) {\r\n    throw new Error(`Environment variable '${key}' is not defined in window.env.`);\r\n  }\r\n  return String(value); // Ensure the value is returned as a string\r\n};\r\n\r\n// ---\r\n\r\n// Dynamically retrieve the base URL first, as it's used for other URLs\r\nconst dynamicBaseUrl: string = getRequiredEnv('baseUrl');\r\n\r\n// ---\r\n\r\n// Environment configuration for Console Application\r\n// All values are dynamically retrieved from window.env.\r\n// If a variable is not found, an error will be thrown.\r\nexport const environment = {\r\n  production: false, // This often remains a static build-time flag\r\n\r\n  // Application URLs (constructed dynamically)\r\n  elderWandUrl:  getRequiredEnv('elderWandUrl'),\r\n  experienceStudioUrl:  getRequiredEnv('experienceStudioUrl'),\r\n  productStudioUrl:  getRequiredEnv('productStudioUrl'),\r\n  consoleRedirectUrl:  getRequiredEnv('consoleRedirectUrl'),\r\n  consoleUrl:  getRequiredEnv('consoleUrl'),\r\n  consoleRedirectUri:  getRequiredEnv('consoleRedirectUri'), // Often same as consoleRedirectUrl\r\n\r\n  // API Configuration (constructed dynamically or directly from window.env)\r\n  // accessKey: getRequiredEnv('accessKey'),\r\n  apiVersion: getRequiredEnv('apiVersion'),\r\n  baseUrl: getRequiredEnv('baseUrl'), // The base URL itself\r\n  consoleApi:  getRequiredEnv('consoleApi'),\r\n  consoleApiV2:  getRequiredEnv('consoleApiV2'),\r\n  consoleApiAuthUrl:  getRequiredEnv('consoleApiAuthUrl'),\r\n  consoleEmbeddingApi:  getRequiredEnv('consoleEmbeddingApi'),\r\n  consoleInstructionApi:  getRequiredEnv('consoleInstructionApi'),\r\n  consoleLangfuseUrl: getRequiredEnv('consoleLangfuseUrl'),\r\n  consoleTruelensUrl:  getRequiredEnv('consoleTruelensUrl'),\r\n  consolePipelineApi:  getRequiredEnv('consolePipelineApi'),\r\n  experienceApiUrl:  getRequiredEnv('experienceApiUrl'),\r\n  productApiUrl:  getRequiredEnv('productApiUrl'),\r\n\r\n  // Logging and App Specific\r\n  enableLogStreaming: getRequiredEnv('enableLogStreaming'),\r\n  logStreamingApiUrl:  getRequiredEnv('logStreamingApiUrl'),\r\n  appVersion: getRequiredEnv('appVersion'),\r\n  workflowExecutionMode: getRequiredEnv('workflowExecutionMode'),\r\n  useBasicLogin: getRequiredEnv('useBasicLogin'), // Assuming this is a string \"true\" or \"false\"\r\n};\r\n\r\n// Log the environment configuration for debugging purposes\r\nconsole.log('Environment configuration loaded dynamically:', environment);"], "mappings": "AAAA;AACA;AACA,MAAMA,cAAc,GAAIC,GAAW,IAAY;EAK7C,MAAMC,SAAS,GAAGC,MAAmB;EACrC,MAAMC,KAAK,GAAGF,SAAS,CAACG,GAAG,GAAGJ,GAAG,CAAC;EAClC,IAAIG,KAAK,KAAKE,SAAS,IAAIF,KAAK,KAAK,IAAI,EAAE;IACzC,MAAM,IAAIG,KAAK,CAAC,yBAAyBN,GAAG,iCAAiC,CAAC;EAChF;EACA,OAAOO,MAAM,CAACJ,KAAK,CAAC,CAAC,CAAC;AACxB,CAAC;AAED;AAEA;AACA,MAAMK,cAAc,GAAWT,cAAc,CAAC,SAAS,CAAC;AAExD;AAEA;AACA;AACA;AACA,OAAO,MAAMU,WAAW,GAAG;EACzBC,UAAU,EAAE,KAAK;EAAE;EAEnB;EACAC,YAAY,EAAGZ,cAAc,CAAC,cAAc,CAAC;EAC7Ca,mBAAmB,EAAGb,cAAc,CAAC,qBAAqB,CAAC;EAC3Dc,gBAAgB,EAAGd,cAAc,CAAC,kBAAkB,CAAC;EACrDe,kBAAkB,EAAGf,cAAc,CAAC,oBAAoB,CAAC;EACzDgB,UAAU,EAAGhB,cAAc,CAAC,YAAY,CAAC;EACzCiB,kBAAkB,EAAGjB,cAAc,CAAC,oBAAoB,CAAC;EAAE;EAE3D;EACA;EACAkB,UAAU,EAAElB,cAAc,CAAC,YAAY,CAAC;EACxCmB,OAAO,EAAEnB,cAAc,CAAC,SAAS,CAAC;EAAE;EACpCoB,UAAU,EAAGpB,cAAc,CAAC,YAAY,CAAC;EACzCqB,YAAY,EAAGrB,cAAc,CAAC,cAAc,CAAC;EAC7CsB,iBAAiB,EAAGtB,cAAc,CAAC,mBAAmB,CAAC;EACvDuB,mBAAmB,EAAGvB,cAAc,CAAC,qBAAqB,CAAC;EAC3DwB,qBAAqB,EAAGxB,cAAc,CAAC,uBAAuB,CAAC;EAC/DyB,kBAAkB,EAAEzB,cAAc,CAAC,oBAAoB,CAAC;EACxD0B,kBAAkB,EAAG1B,cAAc,CAAC,oBAAoB,CAAC;EACzD2B,kBAAkB,EAAG3B,cAAc,CAAC,oBAAoB,CAAC;EACzD4B,gBAAgB,EAAG5B,cAAc,CAAC,kBAAkB,CAAC;EACrD6B,aAAa,EAAG7B,cAAc,CAAC,eAAe,CAAC;EAE/C;EACA8B,kBAAkB,EAAE9B,cAAc,CAAC,oBAAoB,CAAC;EACxD+B,kBAAkB,EAAG/B,cAAc,CAAC,oBAAoB,CAAC;EACzDgC,UAAU,EAAEhC,cAAc,CAAC,YAAY,CAAC;EACxCiC,qBAAqB,EAAEjC,cAAc,CAAC,uBAAuB,CAAC;EAC9DkC,aAAa,EAAElC,cAAc,CAAC,eAAe,CAAC,CAAE;CACjD;AAED;AACAmC,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAE1B,WAAW,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}