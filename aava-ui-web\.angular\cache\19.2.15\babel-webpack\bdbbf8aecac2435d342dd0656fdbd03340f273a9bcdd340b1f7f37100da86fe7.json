{"ast": null, "code": "import { formatDistanceToNow } from 'date-fns';\nimport * as i0 from \"@angular/core\";\nexport let TimeAgoPipe = /*#__PURE__*/(() => {\n  class TimeAgoPipe {\n    transform(value) {\n      if (!value) return '';\n      const date = new Date(value);\n      if (isNaN(date.getTime())) return 'Invalid date';\n      return formatDistanceToNow(date, {\n        addSuffix: true\n      });\n    }\n    static ɵfac = function TimeAgoPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TimeAgoPipe)();\n    };\n    static ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"timeAgo\",\n      type: TimeAgoPipe,\n      pure: true\n    });\n  }\n  return TimeAgoPipe;\n})();", "map": {"version": 3, "names": ["formatDistanceToNow", "TimeAgoPipe", "transform", "value", "date", "Date", "isNaN", "getTime", "addSuffix", "pure"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pipes\\time-ago.pipe.ts"], "sourcesContent": ["// time-ago.pipe.ts\r\nimport { Pipe, PipeTransform } from '@angular/core';\r\nimport { formatDistanceToNow } from 'date-fns';\r\n\r\n@Pipe({\r\n  name: 'timeAgo'\r\n})\r\nexport class TimeAgoPipe implements PipeTransform {\r\n  transform(value: string | Date): string {\r\n    if (!value) return '';\r\n\r\n    const date = new Date(value);\r\n    if (isNaN(date.getTime())) return 'Invalid date';\r\n\r\n    return formatDistanceToNow(date, { addSuffix: true });\r\n  }\r\n}\r\n"], "mappings": "AAEA,SAASA,mBAAmB,QAAQ,UAAU;;AAK9C,WAAaC,WAAW;EAAlB,MAAOA,WAAW;IACtBC,SAASA,CAACC,KAAoB;MAC5B,IAAI,CAACA,KAAK,EAAE,OAAO,EAAE;MAErB,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,KAAK,CAAC;MAC5B,IAAIG,KAAK,CAACF,IAAI,CAACG,OAAO,EAAE,CAAC,EAAE,OAAO,cAAc;MAEhD,OAAOP,mBAAmB,CAACI,IAAI,EAAE;QAAEI,SAAS,EAAE;MAAI,CAAE,CAAC;IACvD;;uCARWP,WAAW;IAAA;;;YAAXA,WAAW;MAAAQ,IAAA;IAAA;;SAAXR,WAAW;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}