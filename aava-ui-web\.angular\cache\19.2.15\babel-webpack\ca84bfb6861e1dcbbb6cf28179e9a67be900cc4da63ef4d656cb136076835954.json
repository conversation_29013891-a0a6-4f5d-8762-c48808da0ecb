{"ast": null, "code": "import { Subject, of } from 'rxjs';\nimport { debounceTime, switchMap, catchError, shareReplay } from 'rxjs/operators';\nimport { environment } from '../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let DebouncedSearchService = /*#__PURE__*/(() => {\n  class DebouncedSearchService {\n    http;\n    consoleApiV1 = environment.consoleApi;\n    consoleApiV2 = environment.consoleApiV2;\n    searchSubject = new Subject();\n    // Map of API endpoints for each module type and filter type\n    apiMap = {\n      agents: {\n        individual: `${this.consoleApiV1}/ava/force/individualAgents?search=`,\n        collaborative: `${this.consoleApiV2}/ava/force/da/agent?isDeleted=false&search=`,\n        default: ''\n      },\n      workflows: {\n        default: `${this.consoleApiV2}/ava/force/da/workflow?isDeleted=false&search=`\n      },\n      tools: {\n        default: `${this.consoleApiV2}/ava/force/da/userTools?isDeleted=false&search=`\n      }\n    };\n    // Observable that emits debounced search results based on input query and module/filter type\n    searchResults$ = this.searchSubject.asObservable().pipe(debounceTime(300),\n    // Wait 300ms before firing search\n    switchMap(({\n      query,\n      moduleType,\n      filterType = 'default'\n    }) => {\n      // Select the correct API URL based on module and filter type\n      const moduleFilters = this.apiMap[moduleType];\n      const validFilterType = filterType in moduleFilters ? filterType : 'default';\n      const apiUrl = moduleFilters[validFilterType];\n      const fullUrl = `${apiUrl}${encodeURIComponent(query.trim())}`;\n      //const fullUrl = `${apiUrl}${query.trim().replace(/\\s+/g, '')}`;\n      // Make the HTTP GET request and catch errors\n      return this.http.get(fullUrl).pipe(catchError(error => {\n        console.error('[Search Error]', error);\n        return of([]);\n      }));\n    }), shareReplay(1) // Share the latest value among subscribers\n    );\n    constructor(http) {\n      this.http = http;\n    }\n    /**\n     * Triggers a new search with the given query, module, and filter type.\n     * @param query The search keyword\n     * @param moduleType The module type (e.g., 'agents', 'workflows')\n     * @param filterType Optional filter type ('individual', 'collaborative', etc.)\n     */\n    triggerSearch(query, moduleType, filterType = 'default') {\n      this.searchSubject.next({\n        query,\n        moduleType,\n        filterType\n      });\n    }\n    /**\n     * Returns the full API URL based on the given module and filter type.\n     * @param moduleType The module type (e.g., 'agents', 'workflows')\n     * @param filterType Optional filter type\n     * @returns Full API URL as a string\n     */\n    getApiUrl(moduleType, filterType = 'default') {\n      const moduleFilters = this.apiMap[moduleType];\n      const validFilterType = filterType in moduleFilters ? filterType : 'default';\n      return moduleFilters[validFilterType] || '';\n    }\n    static ɵfac = function DebouncedSearchService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DebouncedSearchService)(i0.ɵɵinject(i1.HttpClient));\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: DebouncedSearchService,\n      factory: DebouncedSearchService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return DebouncedSearchService;\n})();", "map": {"version": 3, "names": ["Subject", "of", "debounceTime", "switchMap", "catchError", "shareReplay", "environment", "DebouncedSearchService", "http", "consoleApiV1", "consoleApi", "consoleApiV2", "searchSubject", "apiMap", "agents", "individual", "collaborative", "default", "workflows", "tools", "searchResults$", "asObservable", "pipe", "query", "moduleType", "filterType", "moduleFilters", "validFilterType", "apiUrl", "fullUrl", "encodeURIComponent", "trim", "get", "error", "console", "constructor", "triggerSearch", "next", "getApiUrl", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\services\\debounced-search.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { Subject, Observable, of } from 'rxjs';\r\nimport { debounceTime, switchMap, catchError, shareReplay } from 'rxjs/operators';\r\nimport { environment } from '../environments/environment';\r\n\r\ntype ModuleType = 'agents' | 'workflows' | 'tools';\r\ntype FilterType = 'individual' | 'collaborative' | 'default';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class DebouncedSearchService {\r\n  private consoleApiV1 = environment.consoleApi;\r\n  private consoleApiV2 = environment.consoleApiV2;\r\n\r\n  private searchSubject = new Subject<{\r\n    query: string;\r\n    moduleType: ModuleType;\r\n    filterType?: FilterType;\r\n  }>();\r\n\r\n  // Map of API endpoints for each module type and filter type\r\n  private readonly apiMap: Record<ModuleType, Partial<Record<FilterType, string>>> = {\r\n    agents: {\r\n      individual: `${this.consoleApiV1}/ava/force/individualAgents?search=`,\r\n      collaborative: `${this.consoleApiV2}/ava/force/da/agent?isDeleted=false&search=`,\r\n      default: '',\r\n    },\r\n    workflows: {\r\n      default: `${this.consoleApiV2}/ava/force/da/workflow?isDeleted=false&search=`,\r\n    },\r\n    tools: {\r\n      default: `${this.consoleApiV2}/ava/force/da/userTools?isDeleted=false&search=`,\r\n    },\r\n  };\r\n\r\n  // Observable that emits debounced search results based on input query and module/filter type\r\n  searchResults$: Observable<any> = this.searchSubject.asObservable().pipe(\r\n    debounceTime(300), // Wait 300ms before firing search\r\n    switchMap(({ query, moduleType, filterType = 'default' }) => {\r\n      // Select the correct API URL based on module and filter type\r\n      const moduleFilters = this.apiMap[moduleType];\r\n      const validFilterType = filterType in moduleFilters ? filterType : 'default';\r\n\r\n      const apiUrl = moduleFilters[validFilterType];\r\n      const fullUrl = `${apiUrl}${encodeURIComponent(query.trim())}`;\r\n      //const fullUrl = `${apiUrl}${query.trim().replace(/\\s+/g, '')}`;\r\n\r\n\r\n      // Make the HTTP GET request and catch errors\r\n      return this.http.get<any>(fullUrl).pipe(\r\n        catchError((error) => {\r\n          console.error('[Search Error]', error);\r\n          return of([]);\r\n        })\r\n      );\r\n    }),\r\n    shareReplay(1) // Share the latest value among subscribers\r\n  );\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  /**\r\n   * Triggers a new search with the given query, module, and filter type.\r\n   * @param query The search keyword\r\n   * @param moduleType The module type (e.g., 'agents', 'workflows')\r\n   * @param filterType Optional filter type ('individual', 'collaborative', etc.)\r\n   */\r\n  triggerSearch(\r\n    query: string,\r\n    moduleType: ModuleType,\r\n    filterType: FilterType = 'default'\r\n  ): void {\r\n    this.searchSubject.next({ query, moduleType, filterType });\r\n  }\r\n\r\n  /**\r\n   * Returns the full API URL based on the given module and filter type.\r\n   * @param moduleType The module type (e.g., 'agents', 'workflows')\r\n   * @param filterType Optional filter type\r\n   * @returns Full API URL as a string\r\n   */\r\n  getApiUrl(moduleType: ModuleType, filterType: FilterType = 'default'): string {\r\n    const moduleFilters = this.apiMap[moduleType];\r\n    const validFilterType = filterType in moduleFilters ? filterType : 'default';\r\n    return moduleFilters[validFilterType] || '';\r\n  }\r\n}\r\n"], "mappings": "AAEA,SAASA,OAAO,EAAcC,EAAE,QAAQ,MAAM;AAC9C,SAASC,YAAY,EAAEC,SAAS,EAAEC,UAAU,EAAEC,WAAW,QAAQ,gBAAgB;AACjF,SAASC,WAAW,QAAQ,6BAA6B;;;AAQzD,WAAaC,sBAAsB;EAA7B,MAAOA,sBAAsB;IAiDbC,IAAA;IAhDZC,YAAY,GAAGH,WAAW,CAACI,UAAU;IACrCC,YAAY,GAAGL,WAAW,CAACK,YAAY;IAEvCC,aAAa,GAAG,IAAIZ,OAAO,EAI/B;IAEJ;IACiBa,MAAM,GAA4D;MACjFC,MAAM,EAAE;QACNC,UAAU,EAAE,GAAG,IAAI,CAACN,YAAY,qCAAqC;QACrEO,aAAa,EAAE,GAAG,IAAI,CAACL,YAAY,6CAA6C;QAChFM,OAAO,EAAE;OACV;MACDC,SAAS,EAAE;QACTD,OAAO,EAAE,GAAG,IAAI,CAACN,YAAY;OAC9B;MACDQ,KAAK,EAAE;QACLF,OAAO,EAAE,GAAG,IAAI,CAACN,YAAY;;KAEhC;IAED;IACAS,cAAc,GAAoB,IAAI,CAACR,aAAa,CAACS,YAAY,EAAE,CAACC,IAAI,CACtEpB,YAAY,CAAC,GAAG,CAAC;IAAE;IACnBC,SAAS,CAAC,CAAC;MAAEoB,KAAK;MAAEC,UAAU;MAAEC,UAAU,GAAG;IAAS,CAAE,KAAI;MAC1D;MACA,MAAMC,aAAa,GAAG,IAAI,CAACb,MAAM,CAACW,UAAU,CAAC;MAC7C,MAAMG,eAAe,GAAGF,UAAU,IAAIC,aAAa,GAAGD,UAAU,GAAG,SAAS;MAE5E,MAAMG,MAAM,GAAGF,aAAa,CAACC,eAAe,CAAC;MAC7C,MAAME,OAAO,GAAG,GAAGD,MAAM,GAAGE,kBAAkB,CAACP,KAAK,CAACQ,IAAI,EAAE,CAAC,EAAE;MAC9D;MAGA;MACA,OAAO,IAAI,CAACvB,IAAI,CAACwB,GAAG,CAAMH,OAAO,CAAC,CAACP,IAAI,CACrClB,UAAU,CAAE6B,KAAK,IAAI;QACnBC,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;QACtC,OAAOhC,EAAE,CAAC,EAAE,CAAC;MACf,CAAC,CAAC,CACH;IACH,CAAC,CAAC,EACFI,WAAW,CAAC,CAAC,CAAC,CAAC;KAChB;IAED8B,YAAoB3B,IAAgB;MAAhB,KAAAA,IAAI,GAAJA,IAAI;IAAe;IAEvC;;;;;;IAMA4B,aAAaA,CACXb,KAAa,EACbC,UAAsB,EACtBC,UAAA,GAAyB,SAAS;MAElC,IAAI,CAACb,aAAa,CAACyB,IAAI,CAAC;QAAEd,KAAK;QAAEC,UAAU;QAAEC;MAAU,CAAE,CAAC;IAC5D;IAEA;;;;;;IAMAa,SAASA,CAACd,UAAsB,EAAEC,UAAA,GAAyB,SAAS;MAClE,MAAMC,aAAa,GAAG,IAAI,CAACb,MAAM,CAACW,UAAU,CAAC;MAC7C,MAAMG,eAAe,GAAGF,UAAU,IAAIC,aAAa,GAAGD,UAAU,GAAG,SAAS;MAC5E,OAAOC,aAAa,CAACC,eAAe,CAAC,IAAI,EAAE;IAC7C;;uCA3EWpB,sBAAsB,EAAAgC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;;aAAtBnC,sBAAsB;MAAAoC,OAAA,EAAtBpC,sBAAsB,CAAAqC,IAAA;MAAAC,UAAA,EAFrB;IAAM;;SAEPtC,sBAAsB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}