{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { IconComponent } from \"@ava/play-comp-library\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction FilterTabsComponent_button_3_ava_icon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ava-icon\", 6);\n  }\n  if (rf & 2) {\n    const tab_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"iconName\", tab_r2.icon)(\"iconColor\", tab_r2.iconColor || \"#000\");\n  }\n}\nfunction FilterTabsComponent_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 4);\n    i0.ɵɵlistener(\"click\", function FilterTabsComponent_button_3_Template_button_click_0_listener() {\n      const tab_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onTabClick(tab_r2.id));\n    });\n    i0.ɵɵtemplate(1, FilterTabsComponent_button_3_ava_icon_1_Template, 1, 2, \"ava-icon\", 5);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tab_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r2.isActiveTab(tab_r2.id))(\"disabled\", tab_r2.disabled);\n    i0.ɵɵproperty(\"disabled\", tab_r2.disabled);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", tab_r2.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tab_r2.label);\n  }\n}\nexport let FilterTabsComponent = /*#__PURE__*/(() => {\n  class FilterTabsComponent {\n    tabs = [];\n    activeTab = 'all';\n    tabChange = new EventEmitter();\n    visibleTabs = [];\n    dropdownTabs = [];\n    showDropdown = false;\n    constructor() {}\n    ngOnInit() {\n      // Sort tabs by priority if provided\n      this.tabs = this.tabs.map((tab, index) => ({\n        ...tab,\n        priority: tab.priority ?? this.tabs.length - index // Default priority based on order\n      })).sort((a, b) => (b.priority || 0) - (a.priority || 0));\n      this.updateTabsVisibility();\n    }\n    ngAfterViewInit() {\n      setTimeout(() => {\n        this.updateTabsVisibility();\n      });\n    }\n    onResize() {\n      this.updateTabsVisibility();\n    }\n    onDocumentClick(event) {\n      const filterButton = document.querySelector('.filter-dropdown-btn');\n      const dropdown = document.querySelector('.filter-dropdown');\n      if (!filterButton?.contains(event.target) && !dropdown?.contains(event.target)) {\n        this.showDropdown = false;\n      }\n    }\n    updateTabsVisibility() {\n      const container = document.querySelector('.tabs-container');\n      if (!container) return;\n      const containerWidth = container.clientWidth;\n      const filterButtonWidth = 100; // Width reserved for filter button\n      const availableWidth = containerWidth - filterButtonWidth;\n      // Reset tabs\n      this.visibleTabs = [];\n      this.dropdownTabs = [];\n      let currentWidth = 0;\n      const averageTabWidth = availableWidth / this.tabs.length;\n      // Distribute tabs based on available space\n      for (const tab of this.tabs) {\n        const estimatedWidth = this.calculateTabWidth(tab);\n        if (currentWidth + estimatedWidth <= availableWidth && estimatedWidth <= averageTabWidth * 1.5) {\n          this.visibleTabs.push(tab);\n          currentWidth += estimatedWidth;\n        } else {\n          this.dropdownTabs.push(tab);\n        }\n      }\n    }\n    calculateTabWidth(tab) {\n      // Approximate width calculation based on text length and padding\n      const textWidth = tab.label.length * 8; // Approximate 8px per character\n      const padding = 32; // Left and right padding\n      const iconWidth = tab.icon ? 24 : 0; // Icon width if present\n      const gap = 8; // Gap between icon and text\n      return textWidth + padding + iconWidth + gap;\n    }\n    toggleDropdown(event) {\n      event.stopPropagation();\n      this.showDropdown = !this.showDropdown;\n    }\n    onTabClick(tabId) {\n      const tab = this.tabs.find(t => t.id === tabId);\n      if (tab?.disabled) {\n        return; // Don't allow clicking disabled tabs\n      }\n      if (this.activeTab !== tabId) {\n        this.activeTab = tabId;\n        this.tabChange.emit(tabId);\n      }\n      this.showDropdown = false;\n    }\n    isActiveTab(tabId) {\n      return this.activeTab === tabId;\n    }\n    static ɵfac = function FilterTabsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FilterTabsComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FilterTabsComponent,\n      selectors: [[\"app-filter-tabs\"]],\n      hostBindings: function FilterTabsComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"resize\", function FilterTabsComponent_resize_HostBindingHandler() {\n            return ctx.onResize();\n          }, false, i0.ɵɵresolveWindow)(\"click\", function FilterTabsComponent_click_HostBindingHandler($event) {\n            return ctx.onDocumentClick($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      inputs: {\n        tabs: \"tabs\",\n        activeTab: \"activeTab\"\n      },\n      outputs: {\n        tabChange: \"tabChange\"\n      },\n      decls: 4,\n      vars: 1,\n      consts: [[1, \"filter-tabs\"], [1, \"tabs-wrapper\"], [1, \"tabs-container\"], [\"class\", \"tab-item\", 3, \"active\", \"disabled\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"tab-item\", 3, \"click\", \"disabled\"], [\"iconSize\", \"24px\", 3, \"iconName\", \"iconColor\", 4, \"ngIf\"], [\"iconSize\", \"24px\", 3, \"iconName\", \"iconColor\"]],\n      template: function FilterTabsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtemplate(3, FilterTabsComponent_button_3_Template, 4, 7, \"button\", 3);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.visibleTabs);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, IconComponent],\n      styles: [\".filter-tabs[_ngcontent-%COMP%] {\\n  background-color: rgba(237, 237, 243, 0.5);\\n  border-radius: 12px;\\n  padding: 8px;\\n  margin-bottom: 24px;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\\n  border: 1px solid var(--Border-Color, #E5E7EB);\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .tabs-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 4px;\\n  position: relative;\\n  width: 100%;\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .tabs-wrapper[_ngcontent-%COMP%]   .tabs-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex: 1;\\n  gap: 8px;\\n  overflow-x: auto;\\n  scrollbar-width: none;\\n  \\n\\n  -ms-overflow-style: none;\\n  \\n\\n  margin-right: 8px;\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .tabs-wrapper[_ngcontent-%COMP%]   .tabs-container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none;\\n  \\n\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .tabs-wrapper[_ngcontent-%COMP%]   .tabs-container[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: fit-content;\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n  padding: 8px 0;\\n  border-radius: 8px;\\n  border: none;\\n  background: transparent;\\n  color: var(--Text-Body, #000);\\n  font-family: Mulish;\\n  font-size: 18px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: normal;\\n  cursor: pointer;\\n  transition: all 0.2s ease-in-out;\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .tab-item.disabled[_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n  pointer-events: none;\\n  color: var(--Text-Disabled, #9CA3AF);\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .tab-item.disabled[_ngcontent-%COMP%]:hover {\\n  background: transparent;\\n  transform: none;\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]:hover {\\n  background-color: white;\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .tab-item.active[_ngcontent-%COMP%] {\\n  background-color: white;\\n  background-image: linear-gradient(90deg, rgb(247, 145, 28) 40%, rgb(67, 131, 230) 70%);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n  color: transparent;\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .tab-item.active[_ngcontent-%COMP%]   ava-icon[_ngcontent-%COMP%] {\\n  background: none !important;\\n  -webkit-background-clip: initial !important;\\n  -webkit-text-fill-color: initial !important;\\n  background-clip: initial !important;\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .tab-item.active[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  background: inherit;\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .filter-dropdown-container[_ngcontent-%COMP%] {\\n  position: sticky;\\n  right: 4px;\\n  top: 4px;\\n  margin-left: 8px;\\n  z-index: 10;\\n  background-color: var(--background-color);\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .filter-dropdown-container[_ngcontent-%COMP%]   .filter-dropdown-btn[_ngcontent-%COMP%] {\\n  border: 1px solid var(--Border-Color, #E5E7EB);\\n  padding-right: 12px;\\n  white-space: nowrap;\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .filter-dropdown-container[_ngcontent-%COMP%]   .filter-dropdown-btn[_ngcontent-%COMP%]   i.fa-chevron-down[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  transition: transform 0.2s ease-in-out;\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .filter-dropdown-container[_ngcontent-%COMP%]   .filter-dropdown-btn.active[_ngcontent-%COMP%]   i.fa-chevron-down[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .filter-dropdown-container[_ngcontent-%COMP%]   .filter-dropdown[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: calc(100% + 8px);\\n  right: 0;\\n  background-color: var(--background-color);\\n  border-radius: 12px;\\n  border: 1px solid var(--Border-Color, #E5E7EB);\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\\n  min-width: 200px;\\n  z-index: 1000;\\n  padding: 8px;\\n  animation: _ngcontent-%COMP%_fadeIn 0.2s ease-in-out;\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .filter-dropdown-container[_ngcontent-%COMP%]   .filter-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  width: 100%;\\n  padding: 8px 16px;\\n  border: none;\\n  background: transparent;\\n  color: var(--Text-Body, #666D99);\\n  font-size: 14px;\\n  font-weight: 500;\\n  text-align: left;\\n  cursor: pointer;\\n  transition: all 0.2s ease-in-out;\\n  border-radius: 6px;\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .filter-dropdown-container[_ngcontent-%COMP%]   .filter-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 20px;\\n  text-align: center;\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .filter-dropdown-container[_ngcontent-%COMP%]   .filter-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]:hover {\\n  background-color: var(--Hover-Color, rgba(0, 0, 0, 0.05));\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .filter-dropdown-container[_ngcontent-%COMP%]   .filter-dropdown[_ngcontent-%COMP%]   .dropdown-item.active[_ngcontent-%COMP%] {\\n  background-color: white;\\n  background-image: linear-gradient(90deg, #8B8DDA 0%, #F63B8F 100%);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n  color: transparent;\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .filter-dropdown-container[_ngcontent-%COMP%]   .filter-dropdown[_ngcontent-%COMP%]   .dropdown-item.active[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  background: inherit;\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .filter-tabs[_ngcontent-%COMP%] {\\n    padding: 6px;\\n  }\\n  .filter-tabs[_ngcontent-%COMP%]   .tabs-wrapper[_ngcontent-%COMP%] {\\n    gap: 6px;\\n  }\\n  .filter-tabs[_ngcontent-%COMP%]   .tabs-wrapper[_ngcontent-%COMP%]   .tabs-container[_ngcontent-%COMP%] {\\n    gap: 6px;\\n  }\\n  .filter-tabs[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%] {\\n    padding: 6px 12px;\\n    font-size: 13px;\\n  }\\n  .filter-tabs[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .filter-tabs[_ngcontent-%COMP%]   .filter-dropdown-container[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%] {\\n    padding: 6px 12px;\\n    font-size: 13px;\\n  }\\n  .filter-tabs[_ngcontent-%COMP%]   .filter-dropdown-container[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return FilterTabsComponent;\n})();", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "IconComponent", "i0", "ɵɵelement", "ɵɵproperty", "tab_r2", "icon", "iconColor", "ɵɵelementStart", "ɵɵlistener", "FilterTabsComponent_button_3_Template_button_click_0_listener", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "onTabClick", "id", "ɵɵtemplate", "FilterTabsComponent_button_3_ava_icon_1_Template", "ɵɵtext", "ɵɵelementEnd", "ɵɵclassProp", "isActiveTab", "disabled", "ɵɵadvance", "ɵɵtextInterpolate", "label", "FilterTabsComponent", "tabs", "activeTab", "tabChange", "visibleTabs", "dropdownTabs", "showDropdown", "constructor", "ngOnInit", "map", "tab", "index", "priority", "length", "sort", "a", "b", "updateTabsVisibility", "ngAfterViewInit", "setTimeout", "onResize", "onDocumentClick", "event", "filterButton", "document", "querySelector", "dropdown", "contains", "target", "container", "containerWidth", "clientWidth", "filter<PERSON>utton<PERSON><PERSON><PERSON>", "availableWidth", "currentWidth", "averageTabWidth", "estimatedWidth", "calculateTabWidth", "push", "textWidth", "padding", "iconWidth", "gap", "toggleDropdown", "stopPropagation", "tabId", "find", "t", "emit", "selectors", "hostBindings", "FilterTabsComponent_HostBindings", "rf", "ctx", "FilterTabsComponent_resize_HostBindingHandler", "ɵɵresolveWindow", "FilterTabsComponent_click_HostBindingHandler", "$event", "ɵɵresolveDocument", "FilterTabsComponent_button_3_Template", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\shared\\components\\filter-tabs\\filter-tabs.component.ts", "C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\shared\\components\\filter-tabs\\filter-tabs.component.html"], "sourcesContent": ["import {\n  Component,\n  Input,\n  Output,\n  EventEmitter,\n  HostListener,\n  OnInit,\n  AfterViewInit,\n} from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { IconComponent } from \"@ava/play-comp-library\";\n\nexport interface FilterTab {\n  id: string;\n  label: string;\n  icon?: string;\n  iconColor?: string;\n  priority?: number;\n  disabled?: boolean;\n}\n\n@Component({\n  selector: 'app-filter-tabs',\n  templateUrl: './filter-tabs.component.html',\n  styleUrls: ['./filter-tabs.component.scss'],\n  standalone: true,\n  imports: [CommonModule, IconComponent],\n})\nexport class FilterTabsComponent implements OnInit, AfterViewInit {\n  @Input() tabs: FilterTab[] = [];\n  @Input() activeTab: string = 'all';\n  @Output() tabChange = new EventEmitter<string>();\n\n  visibleTabs: FilterTab[] = [];\n  dropdownTabs: FilterTab[] = [];\n  showDropdown = false;\n\n  constructor() { }\n\n  ngOnInit(): void {\n    // Sort tabs by priority if provided\n    this.tabs = this.tabs\n      .map((tab, index) => ({\n        ...tab,\n        priority: tab.priority ?? this.tabs.length - index, // Default priority based on order\n      }))\n      .sort((a, b) => (b.priority || 0) - (a.priority || 0));\n\n    this.updateTabsVisibility();\n  }\n\n  ngAfterViewInit() {\n    setTimeout(() => {\n      this.updateTabsVisibility();\n    });\n  }\n\n  @HostListener('window:resize')\n  onResize() {\n    this.updateTabsVisibility();\n  }\n\n  @HostListener('document:click', ['$event'])\n  onDocumentClick(event: MouseEvent) {\n    const filterButton = document.querySelector('.filter-dropdown-btn');\n    const dropdown = document.querySelector('.filter-dropdown');\n    if (\n      !filterButton?.contains(event.target as Node) &&\n      !dropdown?.contains(event.target as Node)\n    ) {\n      this.showDropdown = false;\n    }\n  }\n\n  updateTabsVisibility() {\n    const container = document.querySelector('.tabs-container');\n    if (!container) return;\n\n    const containerWidth = container.clientWidth;\n    const filterButtonWidth = 100; // Width reserved for filter button\n    const availableWidth = containerWidth - filterButtonWidth;\n\n    // Reset tabs\n    this.visibleTabs = [];\n    this.dropdownTabs = [];\n\n    let currentWidth = 0;\n    const averageTabWidth = availableWidth / this.tabs.length;\n\n    // Distribute tabs based on available space\n    for (const tab of this.tabs) {\n      const estimatedWidth = this.calculateTabWidth(tab);\n      if (currentWidth + estimatedWidth <= availableWidth && estimatedWidth <= averageTabWidth * 1.5) {\n        this.visibleTabs.push(tab);\n        currentWidth += estimatedWidth;\n      } else {\n        this.dropdownTabs.push(tab);\n      }\n    }\n  }\n\n  private calculateTabWidth(tab: FilterTab): number {\n    // Approximate width calculation based on text length and padding\n    const textWidth = tab.label.length * 8; // Approximate 8px per character\n    const padding = 32; // Left and right padding\n    const iconWidth = tab.icon ? 24 : 0; // Icon width if present\n    const gap = 8; // Gap between icon and text\n    return textWidth + padding + iconWidth + gap;\n  }\n\n  toggleDropdown(event: Event) {\n    event.stopPropagation();\n    this.showDropdown = !this.showDropdown;\n  }\n\n  onTabClick(tabId: string): void {\n    const tab = this.tabs.find(t => t.id === tabId);\n    if (tab?.disabled) {\n      return; // Don't allow clicking disabled tabs\n    }\n    \n    if (this.activeTab !== tabId) {\n      this.activeTab = tabId;\n      this.tabChange.emit(tabId);\n    }\n    this.showDropdown = false;\n  }\n\n  isActiveTab(tabId: string): boolean {\n    return this.activeTab === tabId;\n  }\n}\n", "<div class=\"filter-tabs\">\n  <div class=\"tabs-wrapper\">\n    <!-- Visible Tabs Container -->\n    <div class=\"tabs-container\">\n      <button *ngFor=\"let tab of visibleTabs\" \n              class=\"tab-item\" \n              [class.active]=\"isActiveTab(tab.id)\"\n              [class.disabled]=\"tab.disabled\"\n              [disabled]=\"tab.disabled\"\n              (click)=\"onTabClick(tab.id)\">\n        <ava-icon *ngIf=\"tab.icon\" [iconName]=\"tab.icon\" iconSize=\"24px\" [iconColor]=\"tab.iconColor || '#000'\"></ava-icon>\n        <span>{{ tab.label }}</span>\n      </button>\n    </div>\n  </div>\n</div>"], "mappings": "AAAA,SAIEA,YAAY,QAIP,eAAe;AACtB,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;;;;;ICA9CC,EAAA,CAAAC,SAAA,kBAAkH;;;;IAAjDD,EAAtC,CAAAE,UAAA,aAAAC,MAAA,CAAAC,IAAA,CAAqB,cAAAD,MAAA,CAAAE,SAAA,WAAsD;;;;;;IANxGL,EAAA,CAAAM,cAAA,gBAKqC;IAA7BN,EAAA,CAAAO,UAAA,mBAAAC,8DAAA;MAAA,MAAAL,MAAA,GAAAH,EAAA,CAAAS,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAZ,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASF,MAAA,CAAAG,UAAA,CAAAZ,MAAA,CAAAa,EAAA,CAAkB;IAAA,EAAC;IAClChB,EAAA,CAAAiB,UAAA,IAAAC,gDAAA,sBAAuG;IACvGlB,EAAA,CAAAM,cAAA,WAAM;IAAAN,EAAA,CAAAmB,MAAA,GAAe;IACvBnB,EADuB,CAAAoB,YAAA,EAAO,EACrB;;;;;IALDpB,EADA,CAAAqB,WAAA,WAAAT,MAAA,CAAAU,WAAA,CAAAnB,MAAA,CAAAa,EAAA,EAAoC,aAAAb,MAAA,CAAAoB,QAAA,CACL;IAC/BvB,EAAA,CAAAE,UAAA,aAAAC,MAAA,CAAAoB,QAAA,CAAyB;IAEpBvB,EAAA,CAAAwB,SAAA,EAAc;IAAdxB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAC,IAAA,CAAc;IACnBJ,EAAA,CAAAwB,SAAA,GAAe;IAAfxB,EAAA,CAAAyB,iBAAA,CAAAtB,MAAA,CAAAuB,KAAA,CAAe;;;ADiB7B,WAAaC,mBAAmB;EAA1B,MAAOA,mBAAmB;IACrBC,IAAI,GAAgB,EAAE;IACtBC,SAAS,GAAW,KAAK;IACxBC,SAAS,GAAG,IAAIjC,YAAY,EAAU;IAEhDkC,WAAW,GAAgB,EAAE;IAC7BC,YAAY,GAAgB,EAAE;IAC9BC,YAAY,GAAG,KAAK;IAEpBC,YAAA,GAAgB;IAEhBC,QAAQA,CAAA;MACN;MACA,IAAI,CAACP,IAAI,GAAG,IAAI,CAACA,IAAI,CAClBQ,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,MAAM;QACpB,GAAGD,GAAG;QACNE,QAAQ,EAAEF,GAAG,CAACE,QAAQ,IAAI,IAAI,CAACX,IAAI,CAACY,MAAM,GAAGF,KAAK,CAAE;OACrD,CAAC,CAAC,CACFG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACA,CAAC,CAACJ,QAAQ,IAAI,CAAC,KAAKG,CAAC,CAACH,QAAQ,IAAI,CAAC,CAAC,CAAC;MAExD,IAAI,CAACK,oBAAoB,EAAE;IAC7B;IAEAC,eAAeA,CAAA;MACbC,UAAU,CAAC,MAAK;QACd,IAAI,CAACF,oBAAoB,EAAE;MAC7B,CAAC,CAAC;IACJ;IAGAG,QAAQA,CAAA;MACN,IAAI,CAACH,oBAAoB,EAAE;IAC7B;IAGAI,eAAeA,CAACC,KAAiB;MAC/B,MAAMC,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,sBAAsB,CAAC;MACnE,MAAMC,QAAQ,GAAGF,QAAQ,CAACC,aAAa,CAAC,kBAAkB,CAAC;MAC3D,IACE,CAACF,YAAY,EAAEI,QAAQ,CAACL,KAAK,CAACM,MAAc,CAAC,IAC7C,CAACF,QAAQ,EAAEC,QAAQ,CAACL,KAAK,CAACM,MAAc,CAAC,EACzC;QACA,IAAI,CAACtB,YAAY,GAAG,KAAK;MAC3B;IACF;IAEAW,oBAAoBA,CAAA;MAClB,MAAMY,SAAS,GAAGL,QAAQ,CAACC,aAAa,CAAC,iBAAiB,CAAC;MAC3D,IAAI,CAACI,SAAS,EAAE;MAEhB,MAAMC,cAAc,GAAGD,SAAS,CAACE,WAAW;MAC5C,MAAMC,iBAAiB,GAAG,GAAG,CAAC,CAAC;MAC/B,MAAMC,cAAc,GAAGH,cAAc,GAAGE,iBAAiB;MAEzD;MACA,IAAI,CAAC5B,WAAW,GAAG,EAAE;MACrB,IAAI,CAACC,YAAY,GAAG,EAAE;MAEtB,IAAI6B,YAAY,GAAG,CAAC;MACpB,MAAMC,eAAe,GAAGF,cAAc,GAAG,IAAI,CAAChC,IAAI,CAACY,MAAM;MAEzD;MACA,KAAK,MAAMH,GAAG,IAAI,IAAI,CAACT,IAAI,EAAE;QAC3B,MAAMmC,cAAc,GAAG,IAAI,CAACC,iBAAiB,CAAC3B,GAAG,CAAC;QAClD,IAAIwB,YAAY,GAAGE,cAAc,IAAIH,cAAc,IAAIG,cAAc,IAAID,eAAe,GAAG,GAAG,EAAE;UAC9F,IAAI,CAAC/B,WAAW,CAACkC,IAAI,CAAC5B,GAAG,CAAC;UAC1BwB,YAAY,IAAIE,cAAc;QAChC,CAAC,MAAM;UACL,IAAI,CAAC/B,YAAY,CAACiC,IAAI,CAAC5B,GAAG,CAAC;QAC7B;MACF;IACF;IAEQ2B,iBAAiBA,CAAC3B,GAAc;MACtC;MACA,MAAM6B,SAAS,GAAG7B,GAAG,CAACX,KAAK,CAACc,MAAM,GAAG,CAAC,CAAC,CAAC;MACxC,MAAM2B,OAAO,GAAG,EAAE,CAAC,CAAC;MACpB,MAAMC,SAAS,GAAG/B,GAAG,CAACjC,IAAI,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;MACrC,MAAMiE,GAAG,GAAG,CAAC,CAAC,CAAC;MACf,OAAOH,SAAS,GAAGC,OAAO,GAAGC,SAAS,GAAGC,GAAG;IAC9C;IAEAC,cAAcA,CAACrB,KAAY;MACzBA,KAAK,CAACsB,eAAe,EAAE;MACvB,IAAI,CAACtC,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;IACxC;IAEAlB,UAAUA,CAACyD,KAAa;MACtB,MAAMnC,GAAG,GAAG,IAAI,CAACT,IAAI,CAAC6C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC1D,EAAE,KAAKwD,KAAK,CAAC;MAC/C,IAAInC,GAAG,EAAEd,QAAQ,EAAE;QACjB,OAAO,CAAC;MACV;MAEA,IAAI,IAAI,CAACM,SAAS,KAAK2C,KAAK,EAAE;QAC5B,IAAI,CAAC3C,SAAS,GAAG2C,KAAK;QACtB,IAAI,CAAC1C,SAAS,CAAC6C,IAAI,CAACH,KAAK,CAAC;MAC5B;MACA,IAAI,CAACvC,YAAY,GAAG,KAAK;IAC3B;IAEAX,WAAWA,CAACkD,KAAa;MACvB,OAAO,IAAI,CAAC3C,SAAS,KAAK2C,KAAK;IACjC;;uCAtGW7C,mBAAmB;IAAA;;YAAnBA,mBAAmB;MAAAiD,SAAA;MAAAC,YAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAnB/E,EAAA,CAAAO,UAAA,oBAAA0E,8CAAA;YAAA,OAAAD,GAAA,CAAAjC,QAAA,EAAU;UAAA,UAAA/C,EAAA,CAAAkF,eAAA,CAAS,mBAAAC,6CAAAC,MAAA;YAAA,OAAnBJ,GAAA,CAAAhC,eAAA,CAAAoC,MAAA,CAAuB;UAAA,UAAApF,EAAA,CAAAqF,iBAAA,CAAJ;;;;;;;;;;;;;;;UCzB5BrF,EAHJ,CAAAM,cAAA,aAAyB,aACG,aAEI;UAC1BN,EAAA,CAAAiB,UAAA,IAAAqE,qCAAA,oBAKqC;UAM3CtF,EAFI,CAAAoB,YAAA,EAAM,EACF,EACF;;;UAXwBpB,EAAA,CAAAwB,SAAA,GAAc;UAAdxB,EAAA,CAAAE,UAAA,YAAA8E,GAAA,CAAAjD,WAAA,CAAc;;;qBDsBhCjC,YAAY,EAAAyF,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE1F,aAAa;MAAA2F,MAAA;IAAA;;SAE1B/D,mBAAmB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}