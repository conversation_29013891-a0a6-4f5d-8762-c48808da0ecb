{"ast": null, "code": "import { inject } from '@angular/core';\nimport { throwError } from 'rxjs';\nimport { catchError, finalize } from 'rxjs/operators';\nimport { LoaderService } from '../services/loader/loader.service';\nimport { Router } from '@angular/router';\nexport const LoaderInterceptor = (request, next) => {\n  const loaderService = inject(LoaderService);\n  const router = inject(Router);\n  const excludedEndpoints = ['/auth/login-url', '/auth/logout-url', '/auth/basic/refresh/token', '/ava/force/individualAgent/execute', '/force/platform/pipeline/api/v1/test_tool', '/ava/force/userTools', '/ava/force/model', '/ava/force/guardrail', '/auth/organization/hierarchy', 'da/userTools?pag', '/ava/force/agent-execute', '/ava/force/agent-execute/files', '/ava/force/contents', '/auth/refresh-token', '/auth/roles', '/auth/pages', '/auth/use', '/api/auth/realms', '/api/auth/actions', '/api/auth/access/permissions'];\n  const excludedRoutes = ['/build/agents', '/libraries/prompts', '/libraries/tools', 'libraries/tools?page', '/build/workflows', '/libraries/knowledge-base', '/manage/admin-management', '/manage/admin-management/add-user'];\n  function isExcludedRoute(url) {\n    return excludedRoutes.some(route => url === route);\n  }\n  function isExcludeRealmCondition() {\n    return request.url.includes('api/auth/realms') && router.url.includes('manage/realm-management');\n  }\n  const shouldExclude = excludedEndpoints.some(endpoint => request.url.includes(endpoint)) || isExcludedRoute(router.url) || isExcludeRealmCondition();\n  // Generate a unique request ID for tracking\n  const requestId = `${request.url}|${Date.now()}|${Math.random().toString(36).substr(2, 9)}`;\n  if (!shouldExclude) {\n    loaderService.serviceStarted(requestId);\n  }\n  return next(request).pipe(catchError(error => {\n    return throwError(() => error);\n  }), finalize(() => {\n    if (!shouldExclude) {\n      loaderService.serviceCompleted(requestId);\n    }\n  }));\n};", "map": {"version": 3, "names": ["inject", "throwError", "catchError", "finalize", "LoaderService", "Router", "LoaderInterceptor", "request", "next", "loaderService", "router", "excludedEndpoints", "excludedRoutes", "isExcludedRoute", "url", "some", "route", "isExcludeRealmCondition", "includes", "shouldExclude", "endpoint", "requestId", "Date", "now", "Math", "random", "toString", "substr", "serviceStarted", "pipe", "error", "serviceCompleted"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\shared\\interceptors\\loader.interceptor.ts"], "sourcesContent": ["import { HttpInterceptorFn, HttpErrorResponse } from '@angular/common/http';\r\nimport { inject } from '@angular/core';\r\nimport { throwError } from 'rxjs';\r\nimport { catchError, finalize } from 'rxjs/operators';\r\nimport { LoaderService } from '../services/loader/loader.service';\r\nimport { Router } from '@angular/router';\r\n\r\nexport const LoaderInterceptor: HttpInterceptorFn = (request, next) => {\r\n  const loaderService = inject(LoaderService);\r\n  const router = inject(Router);\r\n\r\n  const excludedEndpoints = [\r\n    '/auth/login-url',\r\n    '/auth/logout-url',\r\n    '/auth/basic/refresh/token',\r\n    '/ava/force/individualAgent/execute',\r\n    '/force/platform/pipeline/api/v1/test_tool',\r\n    '/ava/force/userTools',\r\n    '/ava/force/model',\r\n    '/ava/force/guardrail',\r\n    '/auth/organization/hierarchy',\r\n    'da/userTools?pag',\r\n    '/ava/force/agent-execute',\r\n    '/ava/force/agent-execute/files',\r\n    '/ava/force/contents',\r\n    '/auth/refresh-token',\r\n    '/auth/roles',\r\n    '/auth/pages',\r\n    '/auth/use',\r\n    '/api/auth/realms',\r\n    '/api/auth/actions',\r\n    '/api/auth/access/permissions'\r\n  ];\r\n\r\n  const excludedRoutes = [\r\n    '/build/agents',\r\n    '/libraries/prompts',\r\n    '/libraries/tools',\r\n    'libraries/tools?page',\r\n    '/build/workflows',\r\n    '/libraries/knowledge-base',\r\n    '/manage/admin-management',\r\n    '/manage/admin-management/add-user',\r\n  ];\r\n\r\n  function isExcludedRoute(url: string): boolean {\r\n    return excludedRoutes.some((route) => url === route);\r\n  }\r\n\r\n  function isExcludeRealmCondition(): boolean {\r\n    return  request.url.includes('api/auth/realms') && router.url.includes('manage/realm-management');\r\n  }\r\n\r\n  const shouldExclude =\r\n    excludedEndpoints.some((endpoint) => request.url.includes(endpoint)) ||\r\n    isExcludedRoute(router.url) ||\r\n    isExcludeRealmCondition();\r\n\r\n  // Generate a unique request ID for tracking\r\n  const requestId = `${request.url}|${Date.now()}|${Math.random().toString(36).substr(2, 9)}`;\r\n\r\n  if (!shouldExclude) {\r\n    loaderService.serviceStarted(requestId);\r\n  }\r\n\r\n  return next(request).pipe(\r\n    catchError((error: HttpErrorResponse) => {\r\n      return throwError(() => error);\r\n    }),\r\n    finalize(() => {\r\n      if (!shouldExclude) {\r\n        loaderService.serviceCompleted(requestId);\r\n      }\r\n    }),\r\n  );\r\n};\r\n"], "mappings": "AACA,SAASA,MAAM,QAAQ,eAAe;AACtC,SAASC,UAAU,QAAQ,MAAM;AACjC,SAASC,UAAU,EAAEC,QAAQ,QAAQ,gBAAgB;AACrD,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,MAAM,QAAQ,iBAAiB;AAExC,OAAO,MAAMC,iBAAiB,GAAsBA,CAACC,OAAO,EAAEC,IAAI,KAAI;EACpE,MAAMC,aAAa,GAAGT,MAAM,CAACI,aAAa,CAAC;EAC3C,MAAMM,MAAM,GAAGV,MAAM,CAACK,MAAM,CAAC;EAE7B,MAAMM,iBAAiB,GAAG,CACxB,iBAAiB,EACjB,kBAAkB,EAClB,2BAA2B,EAC3B,oCAAoC,EACpC,2CAA2C,EAC3C,sBAAsB,EACtB,kBAAkB,EAClB,sBAAsB,EACtB,8BAA8B,EAC9B,kBAAkB,EAClB,0BAA0B,EAC1B,gCAAgC,EAChC,qBAAqB,EACrB,qBAAqB,EACrB,aAAa,EACb,aAAa,EACb,WAAW,EACX,kBAAkB,EAClB,mBAAmB,EACnB,8BAA8B,CAC/B;EAED,MAAMC,cAAc,GAAG,CACrB,eAAe,EACf,oBAAoB,EACpB,kBAAkB,EAClB,sBAAsB,EACtB,kBAAkB,EAClB,2BAA2B,EAC3B,0BAA0B,EAC1B,mCAAmC,CACpC;EAED,SAASC,eAAeA,CAACC,GAAW;IAClC,OAAOF,cAAc,CAACG,IAAI,CAAEC,KAAK,IAAKF,GAAG,KAAKE,KAAK,CAAC;EACtD;EAEA,SAASC,uBAAuBA,CAAA;IAC9B,OAAQV,OAAO,CAACO,GAAG,CAACI,QAAQ,CAAC,iBAAiB,CAAC,IAAIR,MAAM,CAACI,GAAG,CAACI,QAAQ,CAAC,yBAAyB,CAAC;EACnG;EAEA,MAAMC,aAAa,GACjBR,iBAAiB,CAACI,IAAI,CAAEK,QAAQ,IAAKb,OAAO,CAACO,GAAG,CAACI,QAAQ,CAACE,QAAQ,CAAC,CAAC,IACpEP,eAAe,CAACH,MAAM,CAACI,GAAG,CAAC,IAC3BG,uBAAuB,EAAE;EAE3B;EACA,MAAMI,SAAS,GAAG,GAAGd,OAAO,CAACO,GAAG,IAAIQ,IAAI,CAACC,GAAG,EAAE,IAAIC,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;EAE3F,IAAI,CAACR,aAAa,EAAE;IAClBV,aAAa,CAACmB,cAAc,CAACP,SAAS,CAAC;EACzC;EAEA,OAAOb,IAAI,CAACD,OAAO,CAAC,CAACsB,IAAI,CACvB3B,UAAU,CAAE4B,KAAwB,IAAI;IACtC,OAAO7B,UAAU,CAAC,MAAM6B,KAAK,CAAC;EAChC,CAAC,CAAC,EACF3B,QAAQ,CAAC,MAAK;IACZ,IAAI,CAACgB,aAAa,EAAE;MAClBV,aAAa,CAACsB,gBAAgB,CAACV,SAAS,CAAC;IAC3C;EACF,CAAC,CAAC,CACH;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}