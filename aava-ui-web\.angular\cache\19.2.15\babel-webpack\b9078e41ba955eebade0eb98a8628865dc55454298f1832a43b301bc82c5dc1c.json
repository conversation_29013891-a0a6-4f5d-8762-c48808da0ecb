{"ast": null, "code": "import defaultSource from \"./defaultSource.js\";\nimport beta from \"./beta.js\";\nimport geometric from \"./geometric.js\";\nexport default (function sourceRandomBinomial(source) {\n  var G = geometric.source(source),\n    B = beta.source(source);\n  function randomBinomial(n, p) {\n    n = +n;\n    if ((p = +p) >= 1) return () => n;\n    if (p <= 0) return () => 0;\n    return function () {\n      var acc = 0,\n        nn = n,\n        pp = p;\n      while (nn * pp > 16 && nn * (1 - pp) > 16) {\n        var i = Math.floor((nn + 1) * pp),\n          y = B(i, nn - i + 1)();\n        if (y <= pp) {\n          acc += i;\n          nn -= i;\n          pp = (pp - y) / (1 - y);\n        } else {\n          nn = i - 1;\n          pp /= y;\n        }\n      }\n      var sign = pp < 0.5,\n        pFinal = sign ? pp : 1 - pp,\n        g = G(pFinal);\n      for (var s = g(), k = 0; s <= nn; ++k) s += g();\n      return acc + (sign ? k : nn - k);\n    };\n  }\n  randomBinomial.source = sourceRandomBinomial;\n  return randomBinomial;\n})(defaultSource);", "map": {"version": 3, "names": ["defaultSource", "beta", "geometric", "sourceRandomBinomial", "source", "G", "B", "randomBinomial", "n", "p", "acc", "nn", "pp", "i", "Math", "floor", "y", "sign", "pFinal", "g", "s", "k"], "sources": ["C:/console/aava-ui-web/node_modules/d3-random/src/binomial.js"], "sourcesContent": ["import defaultSource from \"./defaultSource.js\";\nimport beta from \"./beta.js\";\nimport geometric from \"./geometric.js\";\n\nexport default (function sourceRandomBinomial(source) {\n  var G = geometric.source(source),\n      B = beta.source(source);\n\n  function randomBinomial(n, p) {\n    n = +n;\n    if ((p = +p) >= 1) return () => n;\n    if (p <= 0) return () => 0;\n    return function() {\n      var acc = 0, nn = n, pp = p;\n      while (nn * pp > 16 && nn * (1 - pp) > 16) {\n        var i = Math.floor((nn + 1) * pp),\n            y = B(i, nn - i + 1)();\n        if (y <= pp) {\n          acc += i;\n          nn -= i;\n          pp = (pp - y) / (1 - y);\n        } else {\n          nn = i - 1;\n          pp /= y;\n        }\n      }\n      var sign = pp < 0.5,\n          pFinal = sign ? pp : 1 - pp,\n          g = G(pFinal);\n      for (var s = g(), k = 0; s <= nn; ++k) s += g();\n      return acc + (sign ? k : nn - k);\n    };\n  }\n\n  randomBinomial.source = sourceRandomBinomial;\n\n  return randomBinomial;\n})(defaultSource);\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,IAAI,MAAM,WAAW;AAC5B,OAAOC,SAAS,MAAM,gBAAgB;AAEtC,eAAe,CAAC,SAASC,oBAAoBA,CAACC,MAAM,EAAE;EACpD,IAAIC,CAAC,GAAGH,SAAS,CAACE,MAAM,CAACA,MAAM,CAAC;IAC5BE,CAAC,GAAGL,IAAI,CAACG,MAAM,CAACA,MAAM,CAAC;EAE3B,SAASG,cAAcA,CAACC,CAAC,EAAEC,CAAC,EAAE;IAC5BD,CAAC,GAAG,CAACA,CAAC;IACN,IAAI,CAACC,CAAC,GAAG,CAACA,CAAC,KAAK,CAAC,EAAE,OAAO,MAAMD,CAAC;IACjC,IAAIC,CAAC,IAAI,CAAC,EAAE,OAAO,MAAM,CAAC;IAC1B,OAAO,YAAW;MAChB,IAAIC,GAAG,GAAG,CAAC;QAAEC,EAAE,GAAGH,CAAC;QAAEI,EAAE,GAAGH,CAAC;MAC3B,OAAOE,EAAE,GAAGC,EAAE,GAAG,EAAE,IAAID,EAAE,IAAI,CAAC,GAAGC,EAAE,CAAC,GAAG,EAAE,EAAE;QACzC,IAAIC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACJ,EAAE,GAAG,CAAC,IAAIC,EAAE,CAAC;UAC7BI,CAAC,GAAGV,CAAC,CAACO,CAAC,EAAEF,EAAE,GAAGE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAIG,CAAC,IAAIJ,EAAE,EAAE;UACXF,GAAG,IAAIG,CAAC;UACRF,EAAE,IAAIE,CAAC;UACPD,EAAE,GAAG,CAACA,EAAE,GAAGI,CAAC,KAAK,CAAC,GAAGA,CAAC,CAAC;QACzB,CAAC,MAAM;UACLL,EAAE,GAAGE,CAAC,GAAG,CAAC;UACVD,EAAE,IAAII,CAAC;QACT;MACF;MACA,IAAIC,IAAI,GAAGL,EAAE,GAAG,GAAG;QACfM,MAAM,GAAGD,IAAI,GAAGL,EAAE,GAAG,CAAC,GAAGA,EAAE;QAC3BO,CAAC,GAAGd,CAAC,CAACa,MAAM,CAAC;MACjB,KAAK,IAAIE,CAAC,GAAGD,CAAC,CAAC,CAAC,EAAEE,CAAC,GAAG,CAAC,EAAED,CAAC,IAAIT,EAAE,EAAE,EAAEU,CAAC,EAAED,CAAC,IAAID,CAAC,CAAC,CAAC;MAC/C,OAAOT,GAAG,IAAIO,IAAI,GAAGI,CAAC,GAAGV,EAAE,GAAGU,CAAC,CAAC;IAClC,CAAC;EACH;EAEAd,cAAc,CAACH,MAAM,GAAGD,oBAAoB;EAE5C,OAAOI,cAAc;AACvB,CAAC,EAAEP,aAAa,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}