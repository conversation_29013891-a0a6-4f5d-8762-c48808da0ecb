{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { map, catchError, of, BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./environment.service\";\nexport let KnowledgeBaseService = /*#__PURE__*/(() => {\n  class KnowledgeBaseService {\n    http;\n    environmentService;\n    headers = {\n      headers: new HttpHeaders({\n        'Content-Type': 'application/json'\n      })\n    };\n    constructor(http, environmentService) {\n      this.http = http;\n      this.environmentService = environmentService;\n    }\n    get baseUrl() {\n      return this.environmentService.consoleApi;\n    }\n    /**\n    * Fetches all knowledge base entries from the API.\n    *\n    * @returns Observable emitting an array of CardData items\n    * - On success: logs and returns the fetched data\n    * - On error: logs the error and returns an empty array\n    */\n    fetchAllKnowledge() {\n      const url = `${this.environmentService.consoleEmbeddingApi}/ava/force/knowledge`;\n      return this.http.get(url, this.headers).pipe(map(response => {\n        return response;\n      }), catchError(error => {\n        console.error('Knowledge Base API error:', error);\n        return of([]); // Return empty array on error\n      }));\n    }\n    /**\n       * Deletes all documents by collection name\n       * @param collectionName The name of the collection to delete from\n       * @returns Observable of the delete operation result\n       */\n    deleteByCollection(collectionName) {\n      const url = `${this.environmentService.consoleEmbeddingApi}/ava/force/knowledge?collectionName=${collectionName}`;\n      return this.http.delete(url, this.headers);\n    }\n    getEmbeddingModelOptions() {\n      const url = `${this.baseUrl}/ava/force/model?modelType=Embedding`;\n      return this.http.get(url).pipe(map(response => {\n        if (!Array.isArray(response?.models)) {\n          console.error('Invalid or missing \"models\" array in API response');\n          return [];\n        }\n        return response.models;\n      }), catchError(error => {\n        console.error('Knowledge Base API error:', error);\n        return of([]);\n      }));\n    }\n    /**\n     * Converts a key-value object into an instance of HttpParams.\n     * Filters out empty string values and appends only valid entries.\n     *\n     * @param data - Object containing query parameters\n     * @returns HttpParams instance with all valid key-value pairs\n     */\n    makeParams(data) {\n      let params = new BehaviorSubject(null);\n      if (data) {\n        Object.entries(data).forEach(([key, value]) => {\n          let strValue = `${value}`;\n          if (strValue && strValue !== ' ') {\n            params.next(params.value ? {\n              ...params.value,\n              [key]: strValue\n            } : {\n              [key]: strValue\n            });\n          }\n        });\n      }\n      return params.value;\n    }\n    /**\n    * Submits the knowledge base upload request to the API.\n    * Accepts form data or JSON, appends query params from formValue, and posts to a dynamic blob endpoint.\n    *\n    * @param payload - The upload content (can be FormData or plain object)\n    * @param formValue - Key-value pair used to build query params\n    * @param blob - Dynamic URL suffix to append to the upload endpoint\n    * @returns Observable of the API response\n    */\n    submitUpload(payload, formValue, blob) {\n      const options = {\n        params: this.makeParams(formValue)\n      };\n      const url = `${this.environmentService.consoleEmbeddingApi}//ava/force/knowledge${blob}`;\n      return this.http.post(url, payload, options);\n    }\n    /**\n     * Fetches the available upload type options for the upload type dropdown.\n     *\n     * @returns Observable emitting the list of upload types or an empty array on error\n     */\n    getUploadDropdowns() {\n      const url = `${this.baseUrl}/ava/force/refdata?ref_key=Upload Type`;\n      return this.http.get(url, this.headers).pipe(map(response => {\n        return response;\n      }), catchError(error => {\n        console.error('Knowledge Base Dropdowm API error:', error);\n        return of([]); // Return empty array on error\n      }));\n    }\n    /**\n     * Fetches the available upload type options for the upload type dropdown.\n     *\n     * @returns Observable emitting the list of upload types or an empty array on error\n     */\n    getSchemeDropdowns() {\n      const url = `${this.baseUrl}/ava/force/refdata?ref_key=Scheme`;\n      return this.http.get(url, this.headers).pipe(map(response => {\n        return response;\n      }), catchError(error => {\n        console.error('Knowledge Base Dropdowm API error:', error);\n        return of([]); // Return empty array on error\n      }));\n    }\n    /**\n     * Fetches knowledge base files by collection ID\n     * @param collectionId - The collection ID to fetch files for\n     * @returns Observable emitting the knowledge base files data\n     */\n    getKnowledgeBaseById(collectionId) {\n      const embeddingUrl = this.environmentService.consoleEmbeddingApi;\n      const url = `${embeddingUrl}/ava/force/knowledge/files?collection_id=${collectionId}`;\n      return this.http.get(url, this.headers).pipe(map(response => {\n        return response;\n      }), catchError(error => {\n        console.error('API error fetching knowledge base by ID:', error);\n        return of(null);\n      }));\n    }\n    static ɵfac = function KnowledgeBaseService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || KnowledgeBaseService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.EnvironmentService));\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: KnowledgeBaseService,\n      factory: KnowledgeBaseService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return KnowledgeBaseService;\n})();", "map": {"version": 3, "names": ["HttpHeaders", "map", "catchError", "of", "BehaviorSubject", "KnowledgeBaseService", "http", "environmentService", "headers", "constructor", "baseUrl", "consoleApi", "fetchAllKnowledge", "url", "consoleEmbeddingApi", "get", "pipe", "response", "error", "console", "deleteByCollection", "collectionName", "delete", "getEmbeddingModelOptions", "Array", "isArray", "models", "makeParams", "data", "params", "Object", "entries", "for<PERSON>ach", "key", "value", "strValue", "next", "submitUpload", "payload", "formValue", "blob", "options", "post", "getUploadDropdowns", "getSchemeDropdowns", "getKnowledgeBaseById", "collectionId", "embeddingUrl", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "EnvironmentService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\services\\knowledge-base.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\r\nimport { EnvironmentService } from './environment.service';\r\nimport { Observable, map, catchError, of, BehaviorSubject } from 'rxjs';\r\nimport { CardData } from '../../shared/models/card.model';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class KnowledgeBaseService {\r\n  private headers = {\r\n    headers: new HttpHeaders({\r\n      'Content-Type': 'application/json',\r\n    })\r\n  };\r\n\r\n  constructor(\r\n    private http: HttpClient,\r\n    private environmentService: EnvironmentService\r\n  ) { }\r\n\r\n  private get baseUrl(): string {\r\n    return this.environmentService.consoleApi;\r\n  }\r\n\r\n  /**\r\n * Fetches all knowledge base entries from the API.\r\n *\r\n * @returns Observable emitting an array of CardData items\r\n * - On success: logs and returns the fetched data\r\n * - On error: logs the error and returns an empty array\r\n */\r\n\r\n  fetchAllKnowledge(): Observable<CardData[]> {\r\n    const url = `${this.environmentService.consoleEmbeddingApi}/ava/force/knowledge`;\r\n    return this.http.get<CardData[]>(url, this.headers).pipe(\r\n      map((response: CardData[]) => {\r\n        return response;\r\n      }),\r\n      catchError((error: any) => {\r\n        console.error('Knowledge Base API error:', error);\r\n        return of([]); // Return empty array on error\r\n      })\r\n    );\r\n  }\r\n\r\n  /**\r\n     * Deletes all documents by collection name\r\n     * @param collectionName The name of the collection to delete from\r\n     * @returns Observable of the delete operation result\r\n     */\r\n  deleteByCollection(collectionName: string): Observable<any> {\r\n    const url = `${this.environmentService.consoleEmbeddingApi}/ava/force/knowledge?collectionName=${collectionName}`;\r\n    return this.http.delete(url, this.headers);\r\n  }\r\n\r\n\r\n getEmbeddingModelOptions(): Observable<{ name: string; value: string }[]> {\r\n  const url = `${this.baseUrl}/ava/force/model?modelType=Embedding`;\r\n\r\n  return this.http.get<any>(url).pipe(\r\n    map((response: any) => {\r\n      if (!Array.isArray(response?.models)) {\r\n        console.error('Invalid or missing \"models\" array in API response');\r\n        return [];\r\n      }\r\n\r\n      return response.models;\r\n    }),\r\n    catchError((error: any) => {\r\n      console.error('Knowledge Base API error:', error);\r\n      return of([]);\r\n    })\r\n  );\r\n}\r\n\r\n/**\r\n * Converts a key-value object into an instance of HttpParams.\r\n * Filters out empty string values and appends only valid entries.\r\n *\r\n * @param data - Object containing query parameters\r\n * @returns HttpParams instance with all valid key-value pairs\r\n */\r\nmakeParams(data: any): any {\r\n  let params = new BehaviorSubject<any>(null);\r\n  if (data) {\r\n    Object.entries(data).forEach(([key, value]) => {\r\n      let strValue: string = `${value}`;\r\n      if (strValue && strValue !== ' ') {\r\n        params.next(params.value ? { ...params.value, [key]: strValue } : { [key]: strValue });\r\n      }\r\n    });\r\n  }\r\n  return params.value;\r\n}\r\n\r\n /**\r\n * Submits the knowledge base upload request to the API.\r\n * Accepts form data or JSON, appends query params from formValue, and posts to a dynamic blob endpoint.\r\n *\r\n * @param payload - The upload content (can be FormData or plain object)\r\n * @param formValue - Key-value pair used to build query params\r\n * @param blob - Dynamic URL suffix to append to the upload endpoint\r\n * @returns Observable of the API response\r\n */\r\nsubmitUpload(payload: FormData | Record<string, any>, formValue: any, blob: string): Observable<any> {\r\n  const options = {\r\n    params: this.makeParams(formValue),\r\n  };\r\n  const url = `${this.environmentService.consoleEmbeddingApi}//ava/force/knowledge${blob}`;\r\n  return this.http.post(url, payload, options);\r\n}\r\n\r\n\r\n/**\r\n * Fetches the available upload type options for the upload type dropdown.\r\n *\r\n * @returns Observable emitting the list of upload types or an empty array on error\r\n */\r\ngetUploadDropdowns(): Observable<any> {\r\n  const url = `${this.baseUrl}/ava/force/refdata?ref_key=Upload Type`;\r\n  return this.http.get(url, this.headers).pipe(\r\n    map((response: any) => {\r\n      return response;\r\n    }),\r\n    catchError((error: any) => {\r\n      console.error('Knowledge Base Dropdowm API error:', error);\r\n      return of([]); // Return empty array on error\r\n    })\r\n  );\r\n}\r\n\r\n\r\n/**\r\n * Fetches the available upload type options for the upload type dropdown.\r\n *\r\n * @returns Observable emitting the list of upload types or an empty array on error\r\n */\r\ngetSchemeDropdowns(): Observable<any> {\r\n  const url = `${this.baseUrl}/ava/force/refdata?ref_key=Scheme`;\r\n  return this.http.get(url, this.headers).pipe(\r\n    map((response: any) => {\r\n      return response;\r\n    }),\r\n    catchError((error: any) => {\r\n      console.error('Knowledge Base Dropdowm API error:', error);\r\n      return of([]); // Return empty array on error\r\n    })\r\n  );\r\n}\r\n\r\n\r\n  /**\r\n   * Fetches knowledge base files by collection ID\r\n   * @param collectionId - The collection ID to fetch files for\r\n   * @returns Observable emitting the knowledge base files data\r\n   */\r\n  getKnowledgeBaseById(collectionId: string | number): Observable<any> {\r\n    const embeddingUrl = this.environmentService.consoleEmbeddingApi;\r\n    const url = `${embeddingUrl}/ava/force/knowledge/files?collection_id=${collectionId}`;\r\n    return this.http.get<any>(url, this.headers).pipe(\r\n      map((response: any) => {\r\n        return response;\r\n      }),\r\n      catchError((error: any) => {\r\n        console.error('API error fetching knowledge base by ID:', error);\r\n        return of(null);\r\n      })\r\n    );\r\n  }\r\n\r\n}\r\n"], "mappings": "AACA,SAAqBA,WAAW,QAAQ,sBAAsB;AAE9D,SAAqBC,GAAG,EAAEC,UAAU,EAAEC,EAAE,EAAEC,eAAe,QAAQ,MAAM;;;;AAMvE,WAAaC,oBAAoB;EAA3B,MAAOA,oBAAoB;IAQrBC,IAAA;IACAC,kBAAA;IARFC,OAAO,GAAG;MAChBA,OAAO,EAAE,IAAIR,WAAW,CAAC;QACvB,cAAc,EAAE;OACjB;KACF;IAEDS,YACUH,IAAgB,EAChBC,kBAAsC;MADtC,KAAAD,IAAI,GAAJA,IAAI;MACJ,KAAAC,kBAAkB,GAAlBA,kBAAkB;IACxB;IAEJ,IAAYG,OAAOA,CAAA;MACjB,OAAO,IAAI,CAACH,kBAAkB,CAACI,UAAU;IAC3C;IAEA;;;;;;;IAQAC,iBAAiBA,CAAA;MACf,MAAMC,GAAG,GAAG,GAAG,IAAI,CAACN,kBAAkB,CAACO,mBAAmB,sBAAsB;MAChF,OAAO,IAAI,CAACR,IAAI,CAACS,GAAG,CAAaF,GAAG,EAAE,IAAI,CAACL,OAAO,CAAC,CAACQ,IAAI,CACtDf,GAAG,CAAEgB,QAAoB,IAAI;QAC3B,OAAOA,QAAQ;MACjB,CAAC,CAAC,EACFf,UAAU,CAAEgB,KAAU,IAAI;QACxBC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,OAAOf,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CACH;IACH;IAEA;;;;;IAKAiB,kBAAkBA,CAACC,cAAsB;MACvC,MAAMR,GAAG,GAAG,GAAG,IAAI,CAACN,kBAAkB,CAACO,mBAAmB,uCAAuCO,cAAc,EAAE;MACjH,OAAO,IAAI,CAACf,IAAI,CAACgB,MAAM,CAACT,GAAG,EAAE,IAAI,CAACL,OAAO,CAAC;IAC5C;IAGDe,wBAAwBA,CAAA;MACvB,MAAMV,GAAG,GAAG,GAAG,IAAI,CAACH,OAAO,sCAAsC;MAEjE,OAAO,IAAI,CAACJ,IAAI,CAACS,GAAG,CAAMF,GAAG,CAAC,CAACG,IAAI,CACjCf,GAAG,CAAEgB,QAAa,IAAI;QACpB,IAAI,CAACO,KAAK,CAACC,OAAO,CAACR,QAAQ,EAAES,MAAM,CAAC,EAAE;UACpCP,OAAO,CAACD,KAAK,CAAC,mDAAmD,CAAC;UAClE,OAAO,EAAE;QACX;QAEA,OAAOD,QAAQ,CAACS,MAAM;MACxB,CAAC,CAAC,EACFxB,UAAU,CAAEgB,KAAU,IAAI;QACxBC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,OAAOf,EAAE,CAAC,EAAE,CAAC;MACf,CAAC,CAAC,CACH;IACH;IAEA;;;;;;;IAOAwB,UAAUA,CAACC,IAAS;MAClB,IAAIC,MAAM,GAAG,IAAIzB,eAAe,CAAM,IAAI,CAAC;MAC3C,IAAIwB,IAAI,EAAE;QACRE,MAAM,CAACC,OAAO,CAACH,IAAI,CAAC,CAACI,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAI;UAC5C,IAAIC,QAAQ,GAAW,GAAGD,KAAK,EAAE;UACjC,IAAIC,QAAQ,IAAIA,QAAQ,KAAK,GAAG,EAAE;YAChCN,MAAM,CAACO,IAAI,CAACP,MAAM,CAACK,KAAK,GAAG;cAAE,GAAGL,MAAM,CAACK,KAAK;cAAE,CAACD,GAAG,GAAGE;YAAQ,CAAE,GAAG;cAAE,CAACF,GAAG,GAAGE;YAAQ,CAAE,CAAC;UACxF;QACF,CAAC,CAAC;MACJ;MACA,OAAON,MAAM,CAACK,KAAK;IACrB;IAEC;;;;;;;;;IASDG,YAAYA,CAACC,OAAuC,EAAEC,SAAc,EAAEC,IAAY;MAChF,MAAMC,OAAO,GAAG;QACdZ,MAAM,EAAE,IAAI,CAACF,UAAU,CAACY,SAAS;OAClC;MACD,MAAM1B,GAAG,GAAG,GAAG,IAAI,CAACN,kBAAkB,CAACO,mBAAmB,wBAAwB0B,IAAI,EAAE;MACxF,OAAO,IAAI,CAAClC,IAAI,CAACoC,IAAI,CAAC7B,GAAG,EAAEyB,OAAO,EAAEG,OAAO,CAAC;IAC9C;IAGA;;;;;IAKAE,kBAAkBA,CAAA;MAChB,MAAM9B,GAAG,GAAG,GAAG,IAAI,CAACH,OAAO,wCAAwC;MACnE,OAAO,IAAI,CAACJ,IAAI,CAACS,GAAG,CAACF,GAAG,EAAE,IAAI,CAACL,OAAO,CAAC,CAACQ,IAAI,CAC1Cf,GAAG,CAAEgB,QAAa,IAAI;QACpB,OAAOA,QAAQ;MACjB,CAAC,CAAC,EACFf,UAAU,CAAEgB,KAAU,IAAI;QACxBC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1D,OAAOf,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CACH;IACH;IAGA;;;;;IAKAyC,kBAAkBA,CAAA;MAChB,MAAM/B,GAAG,GAAG,GAAG,IAAI,CAACH,OAAO,mCAAmC;MAC9D,OAAO,IAAI,CAACJ,IAAI,CAACS,GAAG,CAACF,GAAG,EAAE,IAAI,CAACL,OAAO,CAAC,CAACQ,IAAI,CAC1Cf,GAAG,CAAEgB,QAAa,IAAI;QACpB,OAAOA,QAAQ;MACjB,CAAC,CAAC,EACFf,UAAU,CAAEgB,KAAU,IAAI;QACxBC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1D,OAAOf,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CACH;IACH;IAGE;;;;;IAKA0C,oBAAoBA,CAACC,YAA6B;MAChD,MAAMC,YAAY,GAAG,IAAI,CAACxC,kBAAkB,CAACO,mBAAmB;MAChE,MAAMD,GAAG,GAAG,GAAGkC,YAAY,4CAA4CD,YAAY,EAAE;MACrF,OAAO,IAAI,CAACxC,IAAI,CAACS,GAAG,CAAMF,GAAG,EAAE,IAAI,CAACL,OAAO,CAAC,CAACQ,IAAI,CAC/Cf,GAAG,CAAEgB,QAAa,IAAI;QACpB,OAAOA,QAAQ;MACjB,CAAC,CAAC,EACFf,UAAU,CAAEgB,KAAU,IAAI;QACxBC,OAAO,CAACD,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;QAChE,OAAOf,EAAE,CAAC,IAAI,CAAC;MACjB,CAAC,CAAC,CACH;IACH;;uCAhKWE,oBAAoB,EAAA2C,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,kBAAA;IAAA;;aAApBhD,oBAAoB;MAAAiD,OAAA,EAApBjD,oBAAoB,CAAAkD,IAAA;MAAAC,UAAA,EAFnB;IAAM;;SAEPnD,oBAAoB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}