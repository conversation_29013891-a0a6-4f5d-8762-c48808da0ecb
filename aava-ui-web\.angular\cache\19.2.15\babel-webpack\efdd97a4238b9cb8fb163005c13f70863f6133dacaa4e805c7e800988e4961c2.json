{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, Input, Output, EventEmitter, ViewChild } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { DragDropModule } from '@angular/cdk/drag-drop';\nimport { IconComponent } from '@ava/play-comp-library';\nlet BuildAgentNodeComponent = class BuildAgentNodeComponent {\n  nodeElement;\n  cdkDrag;\n  node;\n  selected = false;\n  mouseInteractionsEnabled = true;\n  canvasMode = 'build'; // 'build' or 'execute'\n  executeNodeData; // For execute mode consolidated nodes\n  forceAbsolutePositioning = false; // Force absolute positioning (for edit/view modes)\n  hasBeenDragged = false; // Track if node has been dragged by user\n  dragStartPosition = {\n    x: 0,\n    y: 0\n  }; // Store drag start position\n  deleteNode = new EventEmitter();\n  moveNode = new EventEmitter();\n  nodeSelected = new EventEmitter();\n  nodeDoubleClicked = new EventEmitter();\n  // Removed nodePositionChanged to avoid conflicts with moveNode\n  startConnection = new EventEmitter();\n  isDragging = false;\n  showTooltip = false;\n  nodeConfig = {\n    prompt: {\n      icon: 'FileText',\n      useAsset: false\n    },\n    model: {\n      icon: 'Box',\n      useAsset: false\n    },\n    knowledge: {\n      icon: 'BookOpen',\n      useAsset: false\n    },\n    tool: {\n      icon: 'Wrench',\n      useAsset: false\n    },\n    guardrail: {\n      icon: 'Swords',\n      useAsset: false\n    }\n  };\n  ngOnChanges(changes) {\n    if (changes['node']?.currentValue) {\n      const currentNode = changes['node'].currentValue;\n      const previousNode = changes['node'].previousValue;\n      if (this.hasPositionChanged(currentNode, previousNode)) {\n        // Only update position if we're not currently dragging\n        if (!this.isDragging) {\n          // Use CSS variables for positioning in execute mode, otherwise use CDK Drag\n          if (this.isExecuteMode) {\n            this.setCSSPosition(currentNode.data.position);\n          } else {\n            // Always update CDK drag position for non-execute modes\n            this.updateCdkDragPosition(currentNode.data.position);\n          }\n        }\n      }\n    }\n  }\n  hasPositionChanged(currentNode, previousNode) {\n    if (!currentNode?.data?.position) return false;\n    if (!previousNode?.data?.position) return true;\n    const current = currentNode.data.position;\n    const previous = previousNode.data.position;\n    return current.x !== previous.x || current.y !== previous.y;\n  }\n  ngAfterViewInit() {\n    const nodeData = this.currentNodeData;\n    if (nodeData) {\n      if (this.isExecuteMode) {\n        // Set CSS position immediately for execute mode\n        this.setCSSPosition(nodeData.position);\n      } else {\n        // Set CDK drag position for other modes\n        setTimeout(() => {\n          this.updateCdkDragPosition(nodeData.position);\n        }, 0);\n      }\n    }\n  }\n  updateCdkDragPosition(position) {\n    if (this.isDragging) return;\n    console.log('🔧 updateCdkDragPosition called:', {\n      nodeId: this.currentNodeData.id,\n      position,\n      hasCdkDrag: !!this.cdkDrag\n    });\n    // Set absolute position only - let CDK handle its own drag state\n    try {\n      if (this.nodeElement?.nativeElement) {\n        const element = this.nodeElement.nativeElement;\n        // Set absolute positioning\n        element.style.position = 'absolute';\n        element.style.left = `${position.x}px`;\n        element.style.top = `${position.y}px`;\n        element.style.transform = 'none';\n        console.log('🔧 Absolute positioning set:', {\n          nodeId: this.currentNodeData.id,\n          position: position,\n          left: element.style.left,\n          top: element.style.top\n        });\n      }\n    } catch (error) {\n      console.warn('Error updating position:', error);\n    }\n  }\n  setCSSPosition(position) {\n    console.log('🔧 setCSSPosition called:', {\n      nodeId: this.currentNodeData.id,\n      nodeName: this.currentNodeData.name,\n      position,\n      hasElement: !!this.nodeElement?.nativeElement,\n      canvasMode: this.canvasMode,\n      shouldUseAbsolute: this.shouldUseAbsolutePositioning\n    });\n    if (this.nodeElement?.nativeElement) {\n      const element = this.nodeElement.nativeElement;\n      element.style.setProperty('--node-x', `${position.x}px`);\n      element.style.setProperty('--node-y', `${position.y}px`);\n      console.log('🔧 CSS variables set:', {\n        nodeX: element.style.getPropertyValue('--node-x'),\n        nodeY: element.style.getPropertyValue('--node-y'),\n        computedStyle: window.getComputedStyle(element).position\n      });\n    } else {\n      console.warn('🔧 setCSSPosition failed - no element available');\n    }\n  }\n  get currentNodeData() {\n    return this.node?.data || {};\n  }\n  get isExecuteMode() {\n    return this.canvasMode === 'execute';\n  }\n  get shouldUseAbsolutePositioning() {\n    // Only use absolute positioning for execute mode or when CDK drag is disabled\n    const result = this.isExecuteMode || !this.mouseInteractionsEnabled;\n    console.log('🔧 shouldUseAbsolutePositioning check:', {\n      nodeId: this.currentNodeData.id,\n      isExecuteMode: this.isExecuteMode,\n      mouseInteractionsEnabled: this.mouseInteractionsEnabled,\n      forceAbsolutePositioning: this.forceAbsolutePositioning,\n      hasBeenDragged: this.hasBeenDragged,\n      canvasMode: this.canvasMode,\n      result\n    });\n    return result;\n  }\n  get executeTooltipText() {\n    if (!this.isExecuteMode || !this.executeNodeData) return '';\n    const nodeNames = this.executeNodeData.nodes.map(node => node.name);\n    if (nodeNames.length === 1) {\n      return nodeNames[0];\n    } else if (nodeNames.length > 1) {\n      const typeLabel = this.executeNodeData.type === 'knowledge' ? 'knowledge bases' : `${this.executeNodeData.type}s`;\n      return `${nodeNames.length} ${typeLabel}:\\n${nodeNames.join('\\n')}`;\n    }\n    return '';\n  }\n  get isTooltipMultiLine() {\n    return this.executeTooltipText.includes('\\n') || this.executeTooltipText.length > 50;\n  }\n  get nodePosition() {\n    if (this.isExecuteMode && this.executeNodeData) {\n      return this.executeNodeData.position || {\n        x: 0,\n        y: 0\n      };\n    }\n    return this.currentNodeData.position || {\n      x: 0,\n      y: 0\n    };\n  }\n  get isCurrentlySelected() {\n    return this.selected;\n  }\n  get currentConfig() {\n    if (this.isExecuteMode && this.executeNodeData) {\n      return this.nodeConfig[this.executeNodeData.type] || this.nodeConfig.prompt;\n    }\n    const nodeData = this.currentNodeData;\n    return this.nodeConfig[nodeData.type] || this.nodeConfig.prompt;\n  }\n  get shouldUseAssetIcon() {\n    const config = this.currentConfig;\n    return config.useAsset || false;\n  }\n  get iconName() {\n    const config = this.currentConfig;\n    if (this.isExecuteMode && this.executeNodeData) {\n      // In execute mode, always use Lucide icons\n      return config.icon;\n    }\n    const nodeData = this.currentNodeData;\n    // For asset icons (prompts), use the node's icon or config icon\n    if (config.useAsset) {\n      return nodeData.icon || config.icon;\n    }\n    // For Lucide icons, always use the config icon (which contains the correct Lucide icon name)\n    // Ignore any asset paths that might be in nodeData.icon for non-prompt types\n    return config.icon;\n  }\n  onImageError(event) {\n    // Fallback to the default icon if the image fails to load\n    const fallbackIcon = 'assets/images/build.png';\n    event.target.src = fallbackIcon;\n    console.warn(`Failed to load icon for ${this.currentNodeData?.type} node, using fallback:`, fallbackIcon);\n  }\n  onNodeClick() {\n    if (this.isExecuteMode) {\n      // In execute mode, don't emit selection events\n      return;\n    }\n    if (this.mouseInteractionsEnabled && this.currentNodeData.id) {\n      this.nodeSelected.emit(this.currentNodeData.id);\n    }\n  }\n  onNodeDoubleClick() {\n    if (this.isExecuteMode) {\n      // In execute mode, don't emit double-click events\n      return;\n    }\n    if (this.mouseInteractionsEnabled && this.currentNodeData.id) {\n      this.nodeDoubleClicked.emit(this.currentNodeData.id);\n    }\n  }\n  onMouseEnter() {\n    if (this.isExecuteMode && this.executeTooltipText) {\n      this.showTooltip = true;\n    }\n  }\n  onMouseLeave() {\n    if (this.isExecuteMode) {\n      this.showTooltip = false;\n    }\n  }\n  onDeleteClick(event) {\n    event.stopPropagation();\n    if (this.currentNodeData.id) {\n      this.deleteNode.emit(this.currentNodeData.id);\n    }\n  }\n  onDragStarted(event) {\n    this.isDragging = true;\n    this.hasBeenDragged = true; // Mark that user has dragged this node\n    // Store the starting position for manual drag calculation\n    const element = this.nodeElement?.nativeElement;\n    if (element) {\n      this.dragStartPosition = {\n        x: parseInt(element.style.left) || 0,\n        y: parseInt(element.style.top) || 0\n      };\n    }\n    console.log('🔧 Drag started:', {\n      nodeId: this.currentNodeData.id,\n      hasBeenDragged: this.hasBeenDragged,\n      dragStartPosition: this.dragStartPosition\n    });\n  }\n  onDragEnded(event) {\n    if (!this.mouseInteractionsEnabled) return;\n    this.isDragging = false;\n    const nodeData = this.currentNodeData;\n    if (!nodeData?.id) return;\n    // Calculate new position from drag start + CDK transform\n    const element = this.nodeElement?.nativeElement;\n    if (element) {\n      const transform = event.source.getFreeDragPosition();\n      const absolutePosition = {\n        x: Math.max(0, Math.round(this.dragStartPosition.x + transform.x)),\n        y: Math.max(0, Math.round(this.dragStartPosition.y + transform.y))\n      };\n      console.log('🔧 Drag ended - calculated position:', {\n        nodeId: nodeData.id,\n        dragStartPosition: this.dragStartPosition,\n        transform: transform,\n        newAbsolutePosition: absolutePosition\n      });\n      // Emit the new position\n      this.moveNode.emit({\n        nodeId: nodeData.id,\n        position: absolutePosition\n      });\n    }\n  }\n  onNodeMouseDown(event) {\n    if (this.mouseInteractionsEnabled) {\n      // Check for connection starting gesture (Ctrl+click or right-click)\n      if (event.ctrlKey || event.button === 2) {\n        // Start connection from this node\n        event.preventDefault();\n        event.stopPropagation();\n        // Make sure position is up to date before starting connection (like workflow editor)\n        // Position changes handled by moveNode instead\n        this.startConnection.emit({\n          nodeId: this.currentNodeData.id,\n          handleType: 'source',\n          event: event\n        });\n        return;\n      }\n      this.isDragging = true;\n      this.onNodeClick();\n    }\n  }\n};\n__decorate([ViewChild('nodeElement')], BuildAgentNodeComponent.prototype, \"nodeElement\", void 0);\n__decorate([ViewChild('dragRef')], BuildAgentNodeComponent.prototype, \"cdkDrag\", void 0);\n__decorate([Input()], BuildAgentNodeComponent.prototype, \"node\", void 0);\n__decorate([Input()], BuildAgentNodeComponent.prototype, \"selected\", void 0);\n__decorate([Input()], BuildAgentNodeComponent.prototype, \"mouseInteractionsEnabled\", void 0);\n__decorate([Input()], BuildAgentNodeComponent.prototype, \"canvasMode\", void 0);\n__decorate([Input()], BuildAgentNodeComponent.prototype, \"executeNodeData\", void 0);\n__decorate([Input()], BuildAgentNodeComponent.prototype, \"forceAbsolutePositioning\", void 0);\n__decorate([Output()], BuildAgentNodeComponent.prototype, \"deleteNode\", void 0);\n__decorate([Output()], BuildAgentNodeComponent.prototype, \"moveNode\", void 0);\n__decorate([Output()], BuildAgentNodeComponent.prototype, \"nodeSelected\", void 0);\n__decorate([Output()], BuildAgentNodeComponent.prototype, \"nodeDoubleClicked\", void 0);\n__decorate([Output()], BuildAgentNodeComponent.prototype, \"startConnection\", void 0);\nBuildAgentNodeComponent = __decorate([Component({\n  selector: 'app-build-agent-node',\n  standalone: true,\n  imports: [CommonModule, DragDropModule, IconComponent],\n  templateUrl: './build-agent-node.component.html',\n  styleUrls: ['./build-agent-node.component.scss']\n})], BuildAgentNodeComponent);\nexport { BuildAgentNodeComponent };", "map": {"version": 3, "names": ["Component", "Input", "Output", "EventEmitter", "ViewChild", "CommonModule", "DragDropModule", "IconComponent", "BuildAgentNodeComponent", "nodeElement", "cdkDrag", "node", "selected", "mouseInteractionsEnabled", "canvasMode", "executeNodeData", "forceAbsolutePositioning", "hasBeenDragged", "dragStartPosition", "x", "y", "deleteNode", "moveNode", "nodeSelected", "nodeDoubleClicked", "startConnection", "isDragging", "showTooltip", "nodeConfig", "prompt", "icon", "useAsset", "model", "knowledge", "tool", "guardrail", "ngOnChanges", "changes", "currentValue", "currentNode", "previousNode", "previousValue", "hasPositionChanged", "isExecuteMode", "setCSSPosition", "data", "position", "updateCdkDragPosition", "current", "previous", "ngAfterViewInit", "nodeData", "currentNodeData", "setTimeout", "console", "log", "nodeId", "id", "hasCdkDrag", "nativeElement", "element", "style", "left", "top", "transform", "error", "warn", "nodeName", "name", "hasElement", "shouldUseAbsolute", "shouldUseAbsolutePositioning", "setProperty", "nodeX", "getPropertyValue", "nodeY", "computedStyle", "window", "getComputedStyle", "result", "executeTooltipText", "nodeNames", "nodes", "map", "length", "typeLabel", "type", "join", "isTooltipMultiLine", "includes", "nodePosition", "isCurrentlySelected", "currentConfig", "shouldUseAssetIcon", "config", "iconName", "onImageError", "event", "fallbackIcon", "target", "src", "onNodeClick", "emit", "onNodeDoubleClick", "onMouseEnter", "onMouseLeave", "onDeleteClick", "stopPropagation", "onDragStarted", "parseInt", "onDragEnded", "source", "getFreeDragPosition", "absolutePosition", "Math", "max", "round", "newAbsolutePosition", "onNodeMouseDown", "ctrl<PERSON>ey", "button", "preventDefault", "handleType", "__decorate", "selector", "standalone", "imports", "templateUrl", "styleUrls"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\agents\\build-agents\\components\\build-agent-node\\build-agent-node.component.ts"], "sourcesContent": ["import {\r\n  Component,\r\n  Input,\r\n  Output,\r\n  EventEmitter,\r\n  ElementRef,\r\n  ViewChild,\r\n  AfterViewInit,\r\n  OnChanges,\r\n  SimpleChanges,\r\n} from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { DragDropModule, CdkDragEnd, CdkDrag } from '@angular/cdk/drag-drop';\r\nimport { IconComponent } from '@ava/play-comp-library';\r\n\r\nexport interface BuildAgentNodeData {\r\n  id: string;\r\n  type: 'prompt' | 'model' | 'knowledge' | 'tool' | 'guardrail';\r\n  name: string;\r\n  icon?: string;\r\n  position: { x: number; y: number };\r\n  originalToolData?: any; // Store the original tool data for configuration purposes\r\n}\r\n\r\nexport interface ExecuteNodeData {\r\n  type: 'prompt' | 'model' | 'knowledge' | 'tool' | 'guardrail';\r\n  nodes: BuildAgentNodeData[]; // All nodes of this type\r\n  position: { x: number; y: number };\r\n}\r\n\r\n@Component({\r\n  selector: 'app-build-agent-node',\r\n  standalone: true,\r\n  imports: [CommonModule, DragDropModule, IconComponent],\r\n  templateUrl: './build-agent-node.component.html',\r\n  styleUrls: ['./build-agent-node.component.scss'],\r\n})\r\nexport class BuildAgentNodeComponent implements AfterViewInit, OnChanges {\r\n  @ViewChild('nodeElement') nodeElement!: ElementRef;\r\n  @ViewChild('dragRef') cdkDrag!: CdkDrag;\r\n\r\n  @Input() node: any;\r\n  @Input() selected: boolean = false;\r\n  @Input() mouseInteractionsEnabled: boolean = true;\r\n  @Input() canvasMode: string = 'build'; // 'build' or 'execute'\r\n  @Input() executeNodeData?: ExecuteNodeData; // For execute mode consolidated nodes\r\n  @Input() forceAbsolutePositioning: boolean = false; // Force absolute positioning (for edit/view modes)\r\n\r\n  private hasBeenDragged: boolean = false; // Track if node has been dragged by user\r\n  private dragStartPosition: { x: number; y: number } = { x: 0, y: 0 }; // Store drag start position\r\n\r\n  @Output() deleteNode = new EventEmitter<string>();\r\n  @Output() moveNode = new EventEmitter<{\r\n    nodeId: string;\r\n    position: { x: number; y: number };\r\n  }>();\r\n  @Output() nodeSelected = new EventEmitter<string>();\r\n  @Output() nodeDoubleClicked = new EventEmitter<string>();\r\n  // Removed nodePositionChanged to avoid conflicts with moveNode\r\n  @Output() startConnection = new EventEmitter<{\r\n    nodeId: string;\r\n    handleType: 'source' | 'target';\r\n    event: MouseEvent;\r\n  }>();\r\n\r\n  isDragging: boolean = false;\r\n  showTooltip: boolean = false;\r\n\r\n  private readonly nodeConfig = {\r\n    prompt: { icon: 'FileText', useAsset: false },\r\n    model: { icon: 'Box', useAsset: false },\r\n    knowledge: { icon: 'BookOpen', useAsset: false },\r\n    tool: { icon: 'Wrench', useAsset: false },\r\n    guardrail: { icon: 'Swords', useAsset: false },\r\n  };\r\n\r\n  ngOnChanges(changes: SimpleChanges): void {\r\n    if (changes['node']?.currentValue) {\r\n      const currentNode = changes['node'].currentValue;\r\n      const previousNode = changes['node'].previousValue;\r\n\r\n      if (this.hasPositionChanged(currentNode, previousNode)) {\r\n        // Only update position if we're not currently dragging\r\n        if (!this.isDragging) {\r\n          // Use CSS variables for positioning in execute mode, otherwise use CDK Drag\r\n          if (this.isExecuteMode) {\r\n            this.setCSSPosition(currentNode.data.position);\r\n          } else {\r\n            // Always update CDK drag position for non-execute modes\r\n            this.updateCdkDragPosition(currentNode.data.position);\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  private hasPositionChanged(currentNode: any, previousNode: any): boolean {\r\n    if (!currentNode?.data?.position) return false;\r\n    if (!previousNode?.data?.position) return true;\r\n\r\n    const current = currentNode.data.position;\r\n    const previous = previousNode.data.position;\r\n    return current.x !== previous.x || current.y !== previous.y;\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    const nodeData = this.currentNodeData;\r\n    if (nodeData) {\r\n      if (this.isExecuteMode) {\r\n        // Set CSS position immediately for execute mode\r\n        this.setCSSPosition(nodeData.position);\r\n      } else {\r\n        // Set CDK drag position for other modes\r\n        setTimeout(() => {\r\n          this.updateCdkDragPosition(nodeData.position);\r\n        }, 0);\r\n      }\r\n    }\r\n  }\r\n\r\n  private updateCdkDragPosition(position: { x: number; y: number }): void {\r\n    if (this.isDragging) return;\r\n\r\n    console.log('🔧 updateCdkDragPosition called:', {\r\n      nodeId: this.currentNodeData.id,\r\n      position,\r\n      hasCdkDrag: !!this.cdkDrag,\r\n    });\r\n\r\n    // Set absolute position only - let CDK handle its own drag state\r\n    try {\r\n      if (this.nodeElement?.nativeElement) {\r\n        const element = this.nodeElement.nativeElement;\r\n\r\n        // Set absolute positioning\r\n        element.style.position = 'absolute';\r\n        element.style.left = `${position.x}px`;\r\n        element.style.top = `${position.y}px`;\r\n        element.style.transform = 'none';\r\n\r\n        console.log('🔧 Absolute positioning set:', {\r\n          nodeId: this.currentNodeData.id,\r\n          position: position,\r\n          left: element.style.left,\r\n          top: element.style.top,\r\n        });\r\n      }\r\n    } catch (error) {\r\n      console.warn('Error updating position:', error);\r\n    }\r\n  }\r\n\r\n  private setCSSPosition(position: { x: number; y: number }): void {\r\n    console.log('🔧 setCSSPosition called:', {\r\n      nodeId: this.currentNodeData.id,\r\n      nodeName: this.currentNodeData.name,\r\n      position,\r\n      hasElement: !!this.nodeElement?.nativeElement,\r\n      canvasMode: this.canvasMode,\r\n      shouldUseAbsolute: this.shouldUseAbsolutePositioning,\r\n    });\r\n\r\n    if (this.nodeElement?.nativeElement) {\r\n      const element = this.nodeElement.nativeElement;\r\n      element.style.setProperty('--node-x', `${position.x}px`);\r\n      element.style.setProperty('--node-y', `${position.y}px`);\r\n\r\n      console.log('🔧 CSS variables set:', {\r\n        nodeX: element.style.getPropertyValue('--node-x'),\r\n        nodeY: element.style.getPropertyValue('--node-y'),\r\n        computedStyle: window.getComputedStyle(element).position,\r\n      });\r\n    } else {\r\n      console.warn('🔧 setCSSPosition failed - no element available');\r\n    }\r\n  }\r\n\r\n  get currentNodeData(): BuildAgentNodeData {\r\n    return this.node?.data || {};\r\n  }\r\n\r\n  get isExecuteMode(): boolean {\r\n    return this.canvasMode === 'execute';\r\n  }\r\n\r\n  get shouldUseAbsolutePositioning(): boolean {\r\n    // Only use absolute positioning for execute mode or when CDK drag is disabled\r\n    const result = this.isExecuteMode || !this.mouseInteractionsEnabled;\r\n    console.log('🔧 shouldUseAbsolutePositioning check:', {\r\n      nodeId: this.currentNodeData.id,\r\n      isExecuteMode: this.isExecuteMode,\r\n      mouseInteractionsEnabled: this.mouseInteractionsEnabled,\r\n      forceAbsolutePositioning: this.forceAbsolutePositioning,\r\n      hasBeenDragged: this.hasBeenDragged,\r\n      canvasMode: this.canvasMode,\r\n      result,\r\n    });\r\n    return result;\r\n  }\r\n\r\n  get executeTooltipText(): string {\r\n    if (!this.isExecuteMode || !this.executeNodeData) return '';\r\n\r\n    const nodeNames = this.executeNodeData.nodes.map((node) => node.name);\r\n    if (nodeNames.length === 1) {\r\n      return nodeNames[0];\r\n    } else if (nodeNames.length > 1) {\r\n      const typeLabel =\r\n        this.executeNodeData.type === 'knowledge'\r\n          ? 'knowledge bases'\r\n          : `${this.executeNodeData.type}s`;\r\n      return `${nodeNames.length} ${typeLabel}:\\n${nodeNames.join('\\n')}`;\r\n    }\r\n    return '';\r\n  }\r\n\r\n  get isTooltipMultiLine(): boolean {\r\n    return (\r\n      this.executeTooltipText.includes('\\n') ||\r\n      this.executeTooltipText.length > 50\r\n    );\r\n  }\r\n\r\n  get nodePosition(): { x: number; y: number } {\r\n    if (this.isExecuteMode && this.executeNodeData) {\r\n      return this.executeNodeData.position || { x: 0, y: 0 };\r\n    }\r\n    return this.currentNodeData.position || { x: 0, y: 0 };\r\n  }\r\n\r\n  get isCurrentlySelected(): boolean {\r\n    return this.selected;\r\n  }\r\n\r\n  get currentConfig() {\r\n    if (this.isExecuteMode && this.executeNodeData) {\r\n      return (\r\n        this.nodeConfig[this.executeNodeData.type] || this.nodeConfig.prompt\r\n      );\r\n    }\r\n    const nodeData = this.currentNodeData;\r\n    return this.nodeConfig[nodeData.type] || this.nodeConfig.prompt;\r\n  }\r\n\r\n  get shouldUseAssetIcon(): boolean {\r\n    const config = this.currentConfig;\r\n    return config.useAsset || false;\r\n  }\r\n\r\n  get iconName(): string {\r\n    const config = this.currentConfig;\r\n\r\n    if (this.isExecuteMode && this.executeNodeData) {\r\n      // In execute mode, always use Lucide icons\r\n      return config.icon;\r\n    }\r\n\r\n    const nodeData = this.currentNodeData;\r\n\r\n    // For asset icons (prompts), use the node's icon or config icon\r\n    if (config.useAsset) {\r\n      return nodeData.icon || config.icon;\r\n    }\r\n\r\n    // For Lucide icons, always use the config icon (which contains the correct Lucide icon name)\r\n    // Ignore any asset paths that might be in nodeData.icon for non-prompt types\r\n    return config.icon;\r\n  }\r\n\r\n  onImageError(event: any): void {\r\n    // Fallback to the default icon if the image fails to load\r\n    const fallbackIcon = 'assets/images/build.png';\r\n    event.target.src = fallbackIcon;\r\n    console.warn(\r\n      `Failed to load icon for ${this.currentNodeData?.type} node, using fallback:`,\r\n      fallbackIcon,\r\n    );\r\n  }\r\n\r\n  onNodeClick(): void {\r\n    if (this.isExecuteMode) {\r\n      // In execute mode, don't emit selection events\r\n      return;\r\n    }\r\n    if (this.mouseInteractionsEnabled && this.currentNodeData.id) {\r\n      this.nodeSelected.emit(this.currentNodeData.id);\r\n    }\r\n  }\r\n\r\n  onNodeDoubleClick(): void {\r\n    if (this.isExecuteMode) {\r\n      // In execute mode, don't emit double-click events\r\n      return;\r\n    }\r\n    if (this.mouseInteractionsEnabled && this.currentNodeData.id) {\r\n      this.nodeDoubleClicked.emit(this.currentNodeData.id);\r\n    }\r\n  }\r\n\r\n  onMouseEnter(): void {\r\n    if (this.isExecuteMode && this.executeTooltipText) {\r\n      this.showTooltip = true;\r\n    }\r\n  }\r\n\r\n  onMouseLeave(): void {\r\n    if (this.isExecuteMode) {\r\n      this.showTooltip = false;\r\n    }\r\n  }\r\n\r\n  onDeleteClick(event: Event): void {\r\n    event.stopPropagation();\r\n    if (this.currentNodeData.id) {\r\n      this.deleteNode.emit(this.currentNodeData.id);\r\n    }\r\n  }\r\n\r\n  onDragStarted(event: any): void {\r\n    this.isDragging = true;\r\n    this.hasBeenDragged = true; // Mark that user has dragged this node\r\n\r\n    // Store the starting position for manual drag calculation\r\n    const element = this.nodeElement?.nativeElement;\r\n    if (element) {\r\n      this.dragStartPosition = {\r\n        x: parseInt(element.style.left) || 0,\r\n        y: parseInt(element.style.top) || 0,\r\n      };\r\n    }\r\n\r\n    console.log('🔧 Drag started:', {\r\n      nodeId: this.currentNodeData.id,\r\n      hasBeenDragged: this.hasBeenDragged,\r\n      dragStartPosition: this.dragStartPosition,\r\n    });\r\n  }\r\n\r\n  onDragEnded(event: CdkDragEnd): void {\r\n    if (!this.mouseInteractionsEnabled) return;\r\n\r\n    this.isDragging = false;\r\n    const nodeData = this.currentNodeData;\r\n    if (!nodeData?.id) return;\r\n\r\n    // Calculate new position from drag start + CDK transform\r\n    const element = this.nodeElement?.nativeElement;\r\n    if (element) {\r\n      const transform = event.source.getFreeDragPosition();\r\n\r\n      const absolutePosition = {\r\n        x: Math.max(0, Math.round(this.dragStartPosition.x + transform.x)),\r\n        y: Math.max(0, Math.round(this.dragStartPosition.y + transform.y)),\r\n      };\r\n\r\n      console.log('🔧 Drag ended - calculated position:', {\r\n        nodeId: nodeData.id,\r\n        dragStartPosition: this.dragStartPosition,\r\n        transform: transform,\r\n        newAbsolutePosition: absolutePosition,\r\n      });\r\n\r\n      // Emit the new position\r\n      this.moveNode.emit({ nodeId: nodeData.id, position: absolutePosition });\r\n    }\r\n  }\r\n\r\n  onNodeMouseDown(event: MouseEvent): void {\r\n    if (this.mouseInteractionsEnabled) {\r\n      // Check for connection starting gesture (Ctrl+click or right-click)\r\n      if (event.ctrlKey || event.button === 2) {\r\n        // Start connection from this node\r\n        event.preventDefault();\r\n        event.stopPropagation();\r\n\r\n        // Make sure position is up to date before starting connection (like workflow editor)\r\n        // Position changes handled by moveNode instead\r\n\r\n        this.startConnection.emit({\r\n          nodeId: this.currentNodeData.id,\r\n          handleType: 'source',\r\n          event: event,\r\n        });\r\n        return;\r\n      }\r\n\r\n      this.isDragging = true;\r\n      this.onNodeClick();\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SACEA,SAAS,EACTC,KAAK,EACLC,MAAM,EACNC,YAAY,EAEZC,SAAS,QAIJ,eAAe;AACtB,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAA6B,wBAAwB;AAC5E,SAASC,aAAa,QAAQ,wBAAwB;AAwB/C,IAAMC,uBAAuB,GAA7B,MAAMA,uBAAuB;EACRC,WAAW;EACfC,OAAO;EAEpBC,IAAI;EACJC,QAAQ,GAAY,KAAK;EACzBC,wBAAwB,GAAY,IAAI;EACxCC,UAAU,GAAW,OAAO,CAAC,CAAC;EAC9BC,eAAe,CAAmB,CAAC;EACnCC,wBAAwB,GAAY,KAAK,CAAC,CAAC;EAE5CC,cAAc,GAAY,KAAK,CAAC,CAAC;EACjCC,iBAAiB,GAA6B;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAC,CAAE,CAAC,CAAC;EAE5DC,UAAU,GAAG,IAAIlB,YAAY,EAAU;EACvCmB,QAAQ,GAAG,IAAInB,YAAY,EAGjC;EACMoB,YAAY,GAAG,IAAIpB,YAAY,EAAU;EACzCqB,iBAAiB,GAAG,IAAIrB,YAAY,EAAU;EACxD;EACUsB,eAAe,GAAG,IAAItB,YAAY,EAIxC;EAEJuB,UAAU,GAAY,KAAK;EAC3BC,WAAW,GAAY,KAAK;EAEXC,UAAU,GAAG;IAC5BC,MAAM,EAAE;MAAEC,IAAI,EAAE,UAAU;MAAEC,QAAQ,EAAE;IAAK,CAAE;IAC7CC,KAAK,EAAE;MAAEF,IAAI,EAAE,KAAK;MAAEC,QAAQ,EAAE;IAAK,CAAE;IACvCE,SAAS,EAAE;MAAEH,IAAI,EAAE,UAAU;MAAEC,QAAQ,EAAE;IAAK,CAAE;IAChDG,IAAI,EAAE;MAAEJ,IAAI,EAAE,QAAQ;MAAEC,QAAQ,EAAE;IAAK,CAAE;IACzCI,SAAS,EAAE;MAAEL,IAAI,EAAE,QAAQ;MAAEC,QAAQ,EAAE;IAAK;GAC7C;EAEDK,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAAC,MAAM,CAAC,EAAEC,YAAY,EAAE;MACjC,MAAMC,WAAW,GAAGF,OAAO,CAAC,MAAM,CAAC,CAACC,YAAY;MAChD,MAAME,YAAY,GAAGH,OAAO,CAAC,MAAM,CAAC,CAACI,aAAa;MAElD,IAAI,IAAI,CAACC,kBAAkB,CAACH,WAAW,EAAEC,YAAY,CAAC,EAAE;QACtD;QACA,IAAI,CAAC,IAAI,CAACd,UAAU,EAAE;UACpB;UACA,IAAI,IAAI,CAACiB,aAAa,EAAE;YACtB,IAAI,CAACC,cAAc,CAACL,WAAW,CAACM,IAAI,CAACC,QAAQ,CAAC;UAChD,CAAC,MAAM;YACL;YACA,IAAI,CAACC,qBAAqB,CAACR,WAAW,CAACM,IAAI,CAACC,QAAQ,CAAC;UACvD;QACF;MACF;IACF;EACF;EAEQJ,kBAAkBA,CAACH,WAAgB,EAAEC,YAAiB;IAC5D,IAAI,CAACD,WAAW,EAAEM,IAAI,EAAEC,QAAQ,EAAE,OAAO,KAAK;IAC9C,IAAI,CAACN,YAAY,EAAEK,IAAI,EAAEC,QAAQ,EAAE,OAAO,IAAI;IAE9C,MAAME,OAAO,GAAGT,WAAW,CAACM,IAAI,CAACC,QAAQ;IACzC,MAAMG,QAAQ,GAAGT,YAAY,CAACK,IAAI,CAACC,QAAQ;IAC3C,OAAOE,OAAO,CAAC7B,CAAC,KAAK8B,QAAQ,CAAC9B,CAAC,IAAI6B,OAAO,CAAC5B,CAAC,KAAK6B,QAAQ,CAAC7B,CAAC;EAC7D;EAEA8B,eAAeA,CAAA;IACb,MAAMC,QAAQ,GAAG,IAAI,CAACC,eAAe;IACrC,IAAID,QAAQ,EAAE;MACZ,IAAI,IAAI,CAACR,aAAa,EAAE;QACtB;QACA,IAAI,CAACC,cAAc,CAACO,QAAQ,CAACL,QAAQ,CAAC;MACxC,CAAC,MAAM;QACL;QACAO,UAAU,CAAC,MAAK;UACd,IAAI,CAACN,qBAAqB,CAACI,QAAQ,CAACL,QAAQ,CAAC;QAC/C,CAAC,EAAE,CAAC,CAAC;MACP;IACF;EACF;EAEQC,qBAAqBA,CAACD,QAAkC;IAC9D,IAAI,IAAI,CAACpB,UAAU,EAAE;IAErB4B,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE;MAC9CC,MAAM,EAAE,IAAI,CAACJ,eAAe,CAACK,EAAE;MAC/BX,QAAQ;MACRY,UAAU,EAAE,CAAC,CAAC,IAAI,CAAChD;KACpB,CAAC;IAEF;IACA,IAAI;MACF,IAAI,IAAI,CAACD,WAAW,EAAEkD,aAAa,EAAE;QACnC,MAAMC,OAAO,GAAG,IAAI,CAACnD,WAAW,CAACkD,aAAa;QAE9C;QACAC,OAAO,CAACC,KAAK,CAACf,QAAQ,GAAG,UAAU;QACnCc,OAAO,CAACC,KAAK,CAACC,IAAI,GAAG,GAAGhB,QAAQ,CAAC3B,CAAC,IAAI;QACtCyC,OAAO,CAACC,KAAK,CAACE,GAAG,GAAG,GAAGjB,QAAQ,CAAC1B,CAAC,IAAI;QACrCwC,OAAO,CAACC,KAAK,CAACG,SAAS,GAAG,MAAM;QAEhCV,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE;UAC1CC,MAAM,EAAE,IAAI,CAACJ,eAAe,CAACK,EAAE;UAC/BX,QAAQ,EAAEA,QAAQ;UAClBgB,IAAI,EAAEF,OAAO,CAACC,KAAK,CAACC,IAAI;UACxBC,GAAG,EAAEH,OAAO,CAACC,KAAK,CAACE;SACpB,CAAC;MACJ;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdX,OAAO,CAACY,IAAI,CAAC,0BAA0B,EAAED,KAAK,CAAC;IACjD;EACF;EAEQrB,cAAcA,CAACE,QAAkC;IACvDQ,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;MACvCC,MAAM,EAAE,IAAI,CAACJ,eAAe,CAACK,EAAE;MAC/BU,QAAQ,EAAE,IAAI,CAACf,eAAe,CAACgB,IAAI;MACnCtB,QAAQ;MACRuB,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC5D,WAAW,EAAEkD,aAAa;MAC7C7C,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BwD,iBAAiB,EAAE,IAAI,CAACC;KACzB,CAAC;IAEF,IAAI,IAAI,CAAC9D,WAAW,EAAEkD,aAAa,EAAE;MACnC,MAAMC,OAAO,GAAG,IAAI,CAACnD,WAAW,CAACkD,aAAa;MAC9CC,OAAO,CAACC,KAAK,CAACW,WAAW,CAAC,UAAU,EAAE,GAAG1B,QAAQ,CAAC3B,CAAC,IAAI,CAAC;MACxDyC,OAAO,CAACC,KAAK,CAACW,WAAW,CAAC,UAAU,EAAE,GAAG1B,QAAQ,CAAC1B,CAAC,IAAI,CAAC;MAExDkC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;QACnCkB,KAAK,EAAEb,OAAO,CAACC,KAAK,CAACa,gBAAgB,CAAC,UAAU,CAAC;QACjDC,KAAK,EAAEf,OAAO,CAACC,KAAK,CAACa,gBAAgB,CAAC,UAAU,CAAC;QACjDE,aAAa,EAAEC,MAAM,CAACC,gBAAgB,CAAClB,OAAO,CAAC,CAACd;OACjD,CAAC;IACJ,CAAC,MAAM;MACLQ,OAAO,CAACY,IAAI,CAAC,iDAAiD,CAAC;IACjE;EACF;EAEA,IAAId,eAAeA,CAAA;IACjB,OAAO,IAAI,CAACzC,IAAI,EAAEkC,IAAI,IAAI,EAAE;EAC9B;EAEA,IAAIF,aAAaA,CAAA;IACf,OAAO,IAAI,CAAC7B,UAAU,KAAK,SAAS;EACtC;EAEA,IAAIyD,4BAA4BA,CAAA;IAC9B;IACA,MAAMQ,MAAM,GAAG,IAAI,CAACpC,aAAa,IAAI,CAAC,IAAI,CAAC9B,wBAAwB;IACnEyC,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE;MACpDC,MAAM,EAAE,IAAI,CAACJ,eAAe,CAACK,EAAE;MAC/Bd,aAAa,EAAE,IAAI,CAACA,aAAa;MACjC9B,wBAAwB,EAAE,IAAI,CAACA,wBAAwB;MACvDG,wBAAwB,EAAE,IAAI,CAACA,wBAAwB;MACvDC,cAAc,EAAE,IAAI,CAACA,cAAc;MACnCH,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BiE;KACD,CAAC;IACF,OAAOA,MAAM;EACf;EAEA,IAAIC,kBAAkBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACrC,aAAa,IAAI,CAAC,IAAI,CAAC5B,eAAe,EAAE,OAAO,EAAE;IAE3D,MAAMkE,SAAS,GAAG,IAAI,CAAClE,eAAe,CAACmE,KAAK,CAACC,GAAG,CAAExE,IAAI,IAAKA,IAAI,CAACyD,IAAI,CAAC;IACrE,IAAIa,SAAS,CAACG,MAAM,KAAK,CAAC,EAAE;MAC1B,OAAOH,SAAS,CAAC,CAAC,CAAC;IACrB,CAAC,MAAM,IAAIA,SAAS,CAACG,MAAM,GAAG,CAAC,EAAE;MAC/B,MAAMC,SAAS,GACb,IAAI,CAACtE,eAAe,CAACuE,IAAI,KAAK,WAAW,GACrC,iBAAiB,GACjB,GAAG,IAAI,CAACvE,eAAe,CAACuE,IAAI,GAAG;MACrC,OAAO,GAAGL,SAAS,CAACG,MAAM,IAAIC,SAAS,MAAMJ,SAAS,CAACM,IAAI,CAAC,IAAI,CAAC,EAAE;IACrE;IACA,OAAO,EAAE;EACX;EAEA,IAAIC,kBAAkBA,CAAA;IACpB,OACE,IAAI,CAACR,kBAAkB,CAACS,QAAQ,CAAC,IAAI,CAAC,IACtC,IAAI,CAACT,kBAAkB,CAACI,MAAM,GAAG,EAAE;EAEvC;EAEA,IAAIM,YAAYA,CAAA;IACd,IAAI,IAAI,CAAC/C,aAAa,IAAI,IAAI,CAAC5B,eAAe,EAAE;MAC9C,OAAO,IAAI,CAACA,eAAe,CAAC+B,QAAQ,IAAI;QAAE3B,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAC,CAAE;IACxD;IACA,OAAO,IAAI,CAACgC,eAAe,CAACN,QAAQ,IAAI;MAAE3B,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAC,CAAE;EACxD;EAEA,IAAIuE,mBAAmBA,CAAA;IACrB,OAAO,IAAI,CAAC/E,QAAQ;EACtB;EAEA,IAAIgF,aAAaA,CAAA;IACf,IAAI,IAAI,CAACjD,aAAa,IAAI,IAAI,CAAC5B,eAAe,EAAE;MAC9C,OACE,IAAI,CAACa,UAAU,CAAC,IAAI,CAACb,eAAe,CAACuE,IAAI,CAAC,IAAI,IAAI,CAAC1D,UAAU,CAACC,MAAM;IAExE;IACA,MAAMsB,QAAQ,GAAG,IAAI,CAACC,eAAe;IACrC,OAAO,IAAI,CAACxB,UAAU,CAACuB,QAAQ,CAACmC,IAAI,CAAC,IAAI,IAAI,CAAC1D,UAAU,CAACC,MAAM;EACjE;EAEA,IAAIgE,kBAAkBA,CAAA;IACpB,MAAMC,MAAM,GAAG,IAAI,CAACF,aAAa;IACjC,OAAOE,MAAM,CAAC/D,QAAQ,IAAI,KAAK;EACjC;EAEA,IAAIgE,QAAQA,CAAA;IACV,MAAMD,MAAM,GAAG,IAAI,CAACF,aAAa;IAEjC,IAAI,IAAI,CAACjD,aAAa,IAAI,IAAI,CAAC5B,eAAe,EAAE;MAC9C;MACA,OAAO+E,MAAM,CAAChE,IAAI;IACpB;IAEA,MAAMqB,QAAQ,GAAG,IAAI,CAACC,eAAe;IAErC;IACA,IAAI0C,MAAM,CAAC/D,QAAQ,EAAE;MACnB,OAAOoB,QAAQ,CAACrB,IAAI,IAAIgE,MAAM,CAAChE,IAAI;IACrC;IAEA;IACA;IACA,OAAOgE,MAAM,CAAChE,IAAI;EACpB;EAEAkE,YAAYA,CAACC,KAAU;IACrB;IACA,MAAMC,YAAY,GAAG,yBAAyB;IAC9CD,KAAK,CAACE,MAAM,CAACC,GAAG,GAAGF,YAAY;IAC/B5C,OAAO,CAACY,IAAI,CACV,2BAA2B,IAAI,CAACd,eAAe,EAAEkC,IAAI,wBAAwB,EAC7EY,YAAY,CACb;EACH;EAEAG,WAAWA,CAAA;IACT,IAAI,IAAI,CAAC1D,aAAa,EAAE;MACtB;MACA;IACF;IACA,IAAI,IAAI,CAAC9B,wBAAwB,IAAI,IAAI,CAACuC,eAAe,CAACK,EAAE,EAAE;MAC5D,IAAI,CAAClC,YAAY,CAAC+E,IAAI,CAAC,IAAI,CAAClD,eAAe,CAACK,EAAE,CAAC;IACjD;EACF;EAEA8C,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAAC5D,aAAa,EAAE;MACtB;MACA;IACF;IACA,IAAI,IAAI,CAAC9B,wBAAwB,IAAI,IAAI,CAACuC,eAAe,CAACK,EAAE,EAAE;MAC5D,IAAI,CAACjC,iBAAiB,CAAC8E,IAAI,CAAC,IAAI,CAAClD,eAAe,CAACK,EAAE,CAAC;IACtD;EACF;EAEA+C,YAAYA,CAAA;IACV,IAAI,IAAI,CAAC7D,aAAa,IAAI,IAAI,CAACqC,kBAAkB,EAAE;MACjD,IAAI,CAACrD,WAAW,GAAG,IAAI;IACzB;EACF;EAEA8E,YAAYA,CAAA;IACV,IAAI,IAAI,CAAC9D,aAAa,EAAE;MACtB,IAAI,CAAChB,WAAW,GAAG,KAAK;IAC1B;EACF;EAEA+E,aAAaA,CAACT,KAAY;IACxBA,KAAK,CAACU,eAAe,EAAE;IACvB,IAAI,IAAI,CAACvD,eAAe,CAACK,EAAE,EAAE;MAC3B,IAAI,CAACpC,UAAU,CAACiF,IAAI,CAAC,IAAI,CAAClD,eAAe,CAACK,EAAE,CAAC;IAC/C;EACF;EAEAmD,aAAaA,CAACX,KAAU;IACtB,IAAI,CAACvE,UAAU,GAAG,IAAI;IACtB,IAAI,CAACT,cAAc,GAAG,IAAI,CAAC,CAAC;IAE5B;IACA,MAAM2C,OAAO,GAAG,IAAI,CAACnD,WAAW,EAAEkD,aAAa;IAC/C,IAAIC,OAAO,EAAE;MACX,IAAI,CAAC1C,iBAAiB,GAAG;QACvBC,CAAC,EAAE0F,QAAQ,CAACjD,OAAO,CAACC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC;QACpC1C,CAAC,EAAEyF,QAAQ,CAACjD,OAAO,CAACC,KAAK,CAACE,GAAG,CAAC,IAAI;OACnC;IACH;IAEAT,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE;MAC9BC,MAAM,EAAE,IAAI,CAACJ,eAAe,CAACK,EAAE;MAC/BxC,cAAc,EAAE,IAAI,CAACA,cAAc;MACnCC,iBAAiB,EAAE,IAAI,CAACA;KACzB,CAAC;EACJ;EAEA4F,WAAWA,CAACb,KAAiB;IAC3B,IAAI,CAAC,IAAI,CAACpF,wBAAwB,EAAE;IAEpC,IAAI,CAACa,UAAU,GAAG,KAAK;IACvB,MAAMyB,QAAQ,GAAG,IAAI,CAACC,eAAe;IACrC,IAAI,CAACD,QAAQ,EAAEM,EAAE,EAAE;IAEnB;IACA,MAAMG,OAAO,GAAG,IAAI,CAACnD,WAAW,EAAEkD,aAAa;IAC/C,IAAIC,OAAO,EAAE;MACX,MAAMI,SAAS,GAAGiC,KAAK,CAACc,MAAM,CAACC,mBAAmB,EAAE;MAEpD,MAAMC,gBAAgB,GAAG;QACvB9F,CAAC,EAAE+F,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,KAAK,CAAC,IAAI,CAAClG,iBAAiB,CAACC,CAAC,GAAG6C,SAAS,CAAC7C,CAAC,CAAC,CAAC;QAClEC,CAAC,EAAE8F,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,KAAK,CAAC,IAAI,CAAClG,iBAAiB,CAACE,CAAC,GAAG4C,SAAS,CAAC5C,CAAC,CAAC;OAClE;MAEDkC,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE;QAClDC,MAAM,EAAEL,QAAQ,CAACM,EAAE;QACnBvC,iBAAiB,EAAE,IAAI,CAACA,iBAAiB;QACzC8C,SAAS,EAAEA,SAAS;QACpBqD,mBAAmB,EAAEJ;OACtB,CAAC;MAEF;MACA,IAAI,CAAC3F,QAAQ,CAACgF,IAAI,CAAC;QAAE9C,MAAM,EAAEL,QAAQ,CAACM,EAAE;QAAEX,QAAQ,EAAEmE;MAAgB,CAAE,CAAC;IACzE;EACF;EAEAK,eAAeA,CAACrB,KAAiB;IAC/B,IAAI,IAAI,CAACpF,wBAAwB,EAAE;MACjC;MACA,IAAIoF,KAAK,CAACsB,OAAO,IAAItB,KAAK,CAACuB,MAAM,KAAK,CAAC,EAAE;QACvC;QACAvB,KAAK,CAACwB,cAAc,EAAE;QACtBxB,KAAK,CAACU,eAAe,EAAE;QAEvB;QACA;QAEA,IAAI,CAAClF,eAAe,CAAC6E,IAAI,CAAC;UACxB9C,MAAM,EAAE,IAAI,CAACJ,eAAe,CAACK,EAAE;UAC/BiE,UAAU,EAAE,QAAQ;UACpBzB,KAAK,EAAEA;SACR,CAAC;QACF;MACF;MAEA,IAAI,CAACvE,UAAU,GAAG,IAAI;MACtB,IAAI,CAAC2E,WAAW,EAAE;IACpB;EACF;CACD;AAhW2BsB,UAAA,EAAzBvH,SAAS,CAAC,aAAa,CAAC,C,2DAA0B;AAC7BuH,UAAA,EAArBvH,SAAS,CAAC,SAAS,CAAC,C,uDAAmB;AAE/BuH,UAAA,EAAR1H,KAAK,EAAE,C,oDAAW;AACV0H,UAAA,EAAR1H,KAAK,EAAE,C,wDAA2B;AAC1B0H,UAAA,EAAR1H,KAAK,EAAE,C,wEAA0C;AACzC0H,UAAA,EAAR1H,KAAK,EAAE,C,0DAA8B;AAC7B0H,UAAA,EAAR1H,KAAK,EAAE,C,+DAAmC;AAClC0H,UAAA,EAAR1H,KAAK,EAAE,C,wEAA2C;AAKzC0H,UAAA,EAATzH,MAAM,EAAE,C,0DAAyC;AACxCyH,UAAA,EAATzH,MAAM,EAAE,C,wDAGJ;AACKyH,UAAA,EAATzH,MAAM,EAAE,C,4DAA2C;AAC1CyH,UAAA,EAATzH,MAAM,EAAE,C,iEAAgD;AAE/CyH,UAAA,EAATzH,MAAM,EAAE,C,+DAIJ;AA1BMM,uBAAuB,GAAAmH,UAAA,EAPnC3H,SAAS,CAAC;EACT4H,QAAQ,EAAE,sBAAsB;EAChCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACzH,YAAY,EAAEC,cAAc,EAAEC,aAAa,CAAC;EACtDwH,WAAW,EAAE,mCAAmC;EAChDC,SAAS,EAAE,CAAC,mCAAmC;CAChD,CAAC,C,EACWxH,uBAAuB,CAiWnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}