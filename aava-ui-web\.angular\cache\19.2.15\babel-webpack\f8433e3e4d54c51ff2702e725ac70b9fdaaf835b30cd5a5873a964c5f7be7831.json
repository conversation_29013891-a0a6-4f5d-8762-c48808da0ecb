{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, ViewChild } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Subject, takeUntil } from 'rxjs';\nimport { FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';\n// Import child components\nimport { ChatInterfaceComponent } from '@shared/components/chat-interface/chat-interface.component';\nimport { AgentActivityComponent } from './components/agent-activity/agent-activity.component';\nimport { AgentOutputComponent } from './components/agent-output/agent-output.component';\nimport { ButtonComponent, IconComponent, TabsComponent } from '@ava/play-comp-library';\nimport { environment } from '@shared/environments/environment';\nimport workflowConstants from './../constants/workflows.json';\nimport { ExecutionStatus } from '@shared/models/execution.model';\nlet WorkflowExecutionComponent = class WorkflowExecutionComponent {\n  route;\n  router;\n  workflowService;\n  tokenStorage;\n  loaderService;\n  formBuilder;\n  navigationTabs = [{\n    id: 'nav-home',\n    label: 'Agent Activity'\n  }, {\n    id: 'nav-products',\n    label: 'Agent Output'\n  }, {\n    id: 'nav-services',\n    label: 'Preview',\n    disabled: true\n  }];\n  // Workflow details\n  workflowId = null;\n  workflowName = 'Workflow';\n  constants = workflowConstants;\n  chatInterfaceComp;\n  // Activity logs\n  activityLogs = [];\n  activityProgress = 0;\n  executionDetails;\n  isRunning = false;\n  status = ExecutionStatus.notStarted;\n  // Chat messages\n  chatMessages = [];\n  isProcessingChat = false;\n  inputText = '';\n  // Agent outputs\n  agentOutputs = [];\n  workflowForm;\n  fileType = '.zip';\n  // Execution state\n  executionStartTime = null;\n  executionCompleted = false;\n  executionId;\n  workflowLogs = [];\n  enableStreamingLog = environment.enableLogStreaming || 'all';\n  isExecutionComplete = false;\n  progressInterval;\n  // Component lifecycle\n  destroy$ = new Subject();\n  selectedTab = 'Agent Activity';\n  demoTabs = [{\n    id: 'activity',\n    label: 'Agent Activity'\n  }, {\n    id: 'agents',\n    label: 'Agent Output'\n  }, {\n    id: 'preview',\n    label: 'Preview',\n    disabled: true\n  }];\n  errorMsg = false;\n  resMessage;\n  taskMessage = [];\n  isJsonValid = false;\n  disableChat = false;\n  selectedFiles = [];\n  workflowAgents = [];\n  userInputList = [];\n  progress = 0;\n  isLoading = false;\n  loaderColor = '';\n  inputFieldOrder = [];\n  currentInputIndex = 0;\n  activeTabId = 'nav-home';\n  // Panel state\n  isLeftPanelCollapsed = false;\n  expandedAgents = [];\n  agentStatuses = [];\n  showAllInputs = [];\n  // Stepper state\n  stepperSteps = [];\n  currentStepperStep = 0;\n  // Pipeline data\n  pipelineAgents = [];\n  agentInputsMap = {};\n  constructor(route, router, workflowService, tokenStorage, loaderService, formBuilder) {\n    this.route = route;\n    this.router = router;\n    this.workflowService = workflowService;\n    this.tokenStorage = tokenStorage;\n    this.loaderService = loaderService;\n    this.formBuilder = formBuilder;\n  }\n  ngOnInit() {\n    this.loaderService.disableLoader();\n    this.selectedTab = 'Agent Activity';\n    this.executionId = crypto.randomUUID();\n    // Get workflow ID from route params\n    this.route.paramMap.pipe(takeUntil(this.destroy$)).subscribe(params => {\n      this.workflowId = params.get('id');\n      if (this.workflowId) {\n        this.loadWorkflow(this.workflowId);\n      } else {\n        // No workflow ID, redirect back to workflows page\n        this.router.navigate(['/build/workflows']);\n      }\n    });\n    // this.executeWorkflow()\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n    this.loaderService.enableLoader();\n  }\n  onTabChange(event) {\n    this.selectedTab = event.label;\n    this.activeTabId = event.id;\n    console.log('Tab changed:', event);\n  }\n  // Load workflow data\n  loadWorkflow(id) {\n    // In a real app, this would fetch the workflow from a service\n    console.log(`Loading workflow with ID: ${id}`);\n    this.chatMessages = [{\n      from: 'ai',\n      text: 'I am your workflow assistant. I will help you in executing this workflow.'\n    }];\n    this.workflowForm = this.formBuilder.group({});\n    this.workflowService.getWorkflowById(id).subscribe({\n      next: res => {\n        // Handle pipeline response structure\n        if (res.pipeline && res.pipeline.pipeLineAgents) {\n          this.pipelineAgents = res.pipeline.pipeLineAgents;\n          this.workflowName = res.pipeline.name;\n          this.processPipelineAgents();\n        } else {\n          // Handle workflow response structure\n          this.workflowAgents = res.workflowAgents;\n          this.workflowName = res.name;\n          this.userInputList = this.extractInputField(this.workflowAgents);\n        }\n        if (this.userInputList.length === 0) {\n          this.disableChat = true;\n        }\n        this.initializeForm();\n        this.initializeAgentStates();\n        this.initializeStepper();\n        this.startInputCollection();\n      },\n      error: err => {\n        // Fallback to demo data for testing\n        this.loadDemoWorkflow();\n        console.log('Loading demo workflow due to error:', err);\n      }\n    });\n  }\n  isImageInput(input) {\n    const match = input.match(/{{(.*?)}}/);\n    if (match && match[1]) {\n      const variableName = match[1].trim();\n      return variableName.startsWith('image') || variableName.startsWith('Image');\n    }\n    return false;\n  }\n  initializeForm() {\n    this.userInputList.forEach(label => {\n      this.workflowForm.addControl(label.input, this.formBuilder.control('', Validators.required));\n    });\n  }\n  startFakeProgress() {\n    this.progress = 0;\n    this.progressInterval = setInterval(() => {\n      if (this.progress < 90) {\n        this.progress += 5; // Increase slowly\n      }\n    }, 200); // Adjust speed\n  }\n  stopFakeProgress() {\n    clearInterval(this.progressInterval);\n    this.progress = 100;\n    setTimeout(() => {\n      this.isLoading = false;\n    }, 500); // Small delay to let user see 100%\n  }\n  // Handle new chat message from user\n  handleChatMessage(message) {\n    // console.log('message ', message, 'is blank', message.trim() === '');\n    this.isProcessingChat = true;\n    if (message.trim() === '') {\n      if (this.inputFieldOrder.length === 0) {\n        this.chatInterfaceComp.addAiResponse('Executing the workflow...');\n        this.executeWorkflow();\n      }\n      return;\n    }\n    if (this.isExecutionComplete || this.currentInputIndex === this.inputFieldOrder.length) {\n      this.chatInterfaceComp.addAiResponse('Executing the workflow...');\n      this.executeWorkflow();\n      return;\n    }\n    const field = this.inputFieldOrder[this.currentInputIndex];\n    if (this.isImageInput(field)) {\n      // Ignore text input, wait for file input\n      this.chatInterfaceComp.addAiResponse(`Please upload an image file for ${field}`);\n      return;\n    }\n    this.workflowForm.get(field)?.setValue(message);\n    this.currentInputIndex++;\n    if (this.currentInputIndex < this.inputFieldOrder.length) {\n      this.promptForCurrentField();\n    } else {\n      this.chatInterfaceComp.addAiResponse('Thank you for the input! Executing the workflow...');\n      this.executeWorkflow();\n    }\n  }\n  // Save execution logs\n  saveLogs() {\n    console.log('Saving execution logs...');\n    // This would typically save to a service\n  }\n  // Export results\n  exportResults(section) {\n    console.log(`Exporting ${section} data...`);\n    if (section === 'activity') {\n      const data = this.activityLogs.map(log => `[${log.timestamp}] ${log.message}`).join('\\n');\n      this.downloadAsFile(data, 'workflow-activity-logs.txt', 'text/plain');\n    } else {\n      const data = JSON.stringify(this.agentOutputs, null, 2);\n      this.downloadAsFile(data, 'workflow-outputs.json', 'application/json');\n    }\n  }\n  // Helper method to download data as a file\n  downloadAsFile(data, filename, type) {\n    const blob = new Blob([data], {\n      type\n    });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = filename;\n    link.click();\n    URL.revokeObjectURL(url);\n  }\n  // Handle controls for execution\n  handleControlAction(action) {\n    console.log(`Control action: ${action}`);\n    // In a real app, this would control the workflow execution\n    if (action === 'play') {\n      this.isRunning = true;\n    } else if (action === 'pause' || action === 'stop') {\n      this.isRunning = false;\n    }\n  }\n  // Navigate back to workflow listing\n  navigateBack() {\n    this.router.navigate(['/build/workflows']);\n  }\n  // Navigate to edit workflow\n  editWorkflow() {\n    if (this.workflowId) {\n      this.router.navigate(['/build/workflows/edit', this.workflowId]);\n    }\n  }\n  logExecutionStatus(delay = 2000) {\n    setTimeout(() => {\n      if (!this.isExecutionComplete) {\n        console.log(this.constants);\n        console.log(this.constants['labels'].workflowExecProcessing);\n        this.workflowLogs.push({\n          content: this.constants['labels'].workflowExecProcessing,\n          color: '#F9DB24'\n        });\n      }\n    }, delay);\n  }\n  // public parseAnsiString(ansiString: string) {\n  //   const regex = ansiRegex();\n  //   const parts = ansiString.split(regex);\n  //   const matches = [...ansiString.matchAll(regex)];\n  //   parts.forEach((part, index) => {\n  //     if (part.trim() !== '') {\n  //       let colorCode = matches[index - 1][0];\n  //       if (index - 2 >= 0 && matches[index - 2]?.includes('\\u001b[1m')) {\n  //         colorCode = `\\u001b[1m${colorCode}`;\n  //       }\n  //       this.workflowLogs.push({\n  //         content: part,\n  //         color: this.colorMap[colorCode] || 'white',\n  //       });\n  //     }\n  //   });\n  // }\n  getWorkflowLogs(executionId) {\n    this.workflowService.workflowLogConnect(executionId).pipe(takeUntil(this.destroy$)).subscribe({\n      next: message => {\n        console.log('message: ', message);\n        const {\n          content,\n          color\n        } = message;\n        if (color) {\n          this.workflowLogs.push({\n            content,\n            color\n          });\n        } else if (this.enableStreamingLog === 'all') {\n          // this.parseAnsiString(content);\n        }\n      },\n      error: err => {\n        this.workflowLogs.push({\n          content: this.constants['workflowLog'],\n          color: 'red'\n        });\n        console.error('WebSocket error:', err);\n      },\n      complete: () => {\n        this.logExecutionStatus();\n        console.log('WebSocket connection closed');\n      }\n    });\n  }\n  // public parseAnsiString(ansiString: string) {\n  //   const regex = ansiRegex();\n  //   const parts = ansiString.split(regex);\n  //   const matches = [...ansiString.matchAll(regex)];\n  //   parts.forEach((part, index) => {\n  //     if (part.trim() !== '') {\n  //       let colorCode = matches[index-1][0];\n  //       if(index - 2 >= 0 && matches[index-2]?.includes('\\u001b[1m')) {\n  //         colorCode = `\\u001b[1m${colorCode}`;\n  //       }\n  //       this.workflowLogs.push({\n  //         content: part, \n  //         color: this.colorMap[colorCode] || 'white', \n  //       });\n  //     }\n  //   });\n  // }\n  validateJson(output) {\n    this.isJsonValid = false;\n    try {\n      const parsedOutput = JSON.parse(output);\n      this.isJsonValid = true;\n      return parsedOutput;\n    } catch (e) {\n      return null;\n    }\n  }\n  executeWorkflow() {\n    let payload = new FormData();\n    let queryString = '';\n    this.status = ExecutionStatus.running;\n    if (this.selectedFiles.length) {\n      this.selectedFiles.forEach(file => {\n        payload.append('files', file);\n      });\n      payload.append('workflowId', this.workflowId);\n      payload.append('userInputs', JSON.stringify(this.workflowForm.value));\n      payload.append('user', this.tokenStorage.getDaUsername());\n      payload.append('executionId', this.executionId);\n      queryString = '/files';\n    } else {\n      payload = {\n        pipeLineId: this.workflowId,\n        userInputs: this.workflowForm.value,\n        executionId: this.executionId,\n        user: this.tokenStorage.getDaUsername()\n      };\n    }\n    this.getWorkflowLogs(this.executionId);\n    this.startFakeProgress();\n    this.workflowService.executeWorkflow(payload, queryString).pipe(takeUntil(this.destroy$)).subscribe({\n      next: res => {\n        this.isProcessingChat = false;\n        this.isRunning = false;\n        this.chatInterfaceComp.addAiResponse(res?.message || \"Workflow execution completed successfully!\");\n        if (res?.workflowResponse?.pipeline?.output) {\n          this.isExecutionComplete = true;\n          // console.log(this.constants['labels'].workflowExecComplete);\n          this.workflowLogs.push({\n            content: this.constants['labels'].workflowExecComplete,\n            color: '#0F8251'\n          });\n          this.errorMsg = false;\n          this.resMessage = res?.workflowResponse?.pipeline?.output;\n          this.agentOutputs = res?.workflowResponse?.pipeline?.tasksOutputs.map(task => {\n            return {\n              id: task?.id || '',\n              title: task?.title || '',\n              content: task?.content || '',\n              agentName: task?.agentName || '',\n              timestamp: task?.timestamp || '',\n              type: task?.type || '',\n              description: task?.description || '',\n              expected_output: task?.expected_output || '',\n              summary: task?.summary || '',\n              raw: task?.raw || ''\n            };\n          });\n          this.taskMessage = res?.workflowResponse?.pipeline?.tasksOutputs.map(task => {\n            return {\n              description: task.description,\n              summary: task.summary,\n              raw: task.raw,\n              expected_output: task.expected_output\n            };\n          });\n          // if(\"file_download_url\" in res?.pipeline){\n          //   this.isFileWriter = true;\n          //   this.fileDownloadLink = res?.pipeline?.file_download_url;\n          //   if(!this.fileDownloadLink){\n          //     this.fileDownloadUrlError = [];\n          //     this.fileDownloadUrlError.push(\"Output file is not generated yet!\")\n          //   }\n          // }\n          // this.isAccordian = true\n        }\n        this.validateJson(this.resMessage);\n        this.status = ExecutionStatus.completed;\n        this.stopFakeProgress();\n        this.selectedFiles = [];\n      },\n      error: error => {\n        this.isExecutionComplete = true;\n        this.isProcessingChat = false;\n        this.errorMsg = true;\n        this.resMessage = error?.error?.detail;\n        this.workflowService.workflowLogDisconnect();\n        this.workflowLogs.push({\n          content: this.constants['labels'].workflowLogFailed,\n          color: 'red'\n        });\n        this.chatInterfaceComp.addAiResponse('Something went wrong, Workflow execution has failed.');\n        this.selectedFiles = [];\n        this.stopFakeProgress();\n        console.log('error is', error.message);\n      }\n    });\n  }\n  // public asyncExecutePipeline() {\n  //   const payload: FormData = new FormData();\n  //   if (this.selectedFiles?.length) {\n  //     for (const element of this.selectedFiles) {\n  //       payload.append('files', element)\n  //     }\n  //   }\n  //   payload.append('pipeLineId', String(this.workflowId));\n  //   payload.append('userInputs', JSON.stringify(this.workflowForm.value));\n  //   payload.append('user', this.tokenStorage.getDaUsername() || '');\n  //   payload.append('executionId', this.executionId);\n  //   this.workflowService.asyncExecutePipeline(payload).pipe().subscribe({\n  //     next: (res: any) => {\n  //       if(res) {\n  //         // res handling\n  //         console.log(res);\n  //       }\n  //     },\n  //     error: e => {\n  //       // error handling\n  //       console.log(e);\n  //     }\n  //   })\n  // }\n  handleAttachment() {\n    console.log('handleAttachment');\n  }\n  onAttachmentsSelected(files) {\n    if (this.currentInputIndex === this.inputFieldOrder.length || this.inputFieldOrder.length === 0) {\n      this.selectedFiles = files;\n      return;\n    }\n    const field = this.inputFieldOrder[this.currentInputIndex];\n    if (this.isImageInput(field)) {\n      if (files && files.length > 0) {\n        this.onImageSelected(files[0]);\n      }\n    } else {\n      this.selectedFiles = files;\n    }\n  }\n  startInputCollection() {\n    this.inputFieldOrder = Object.keys(this.workflowForm.controls);\n    this.currentInputIndex = 0;\n    if (this.inputFieldOrder.length > 0) {\n      this.promptForCurrentField();\n    } else {\n      this.disableChat = true;\n      this.chatInterfaceComp.addAiResponse('No input is required for this workflow. Click on send button to execute the workflow.');\n    }\n  }\n  promptForCurrentField() {\n    const field = this.inputFieldOrder[this.currentInputIndex];\n    if (this.isImageInput(field)) {\n      this.fileType = '.jpeg,.png,.jpg,.svg';\n      this.chatInterfaceComp.addAiResponse(`Please upload an image for ${field}`);\n      // UI should now show a file input for the user\n    } else {\n      this.fileType = '.zip'; // or whatever default you want for non-image\n      this.chatInterfaceComp.addAiResponse(`Please enter the value of ${field}`);\n    }\n  }\n  onImageSelected(file) {\n    const field = this.inputFieldOrder[this.currentInputIndex];\n    if (!this.isImageInput(field)) return;\n    const reader = new FileReader();\n    reader.onload = () => {\n      const base64String = reader.result;\n      this.workflowForm.get(field)?.setValue(base64String);\n      this.currentInputIndex++;\n      if (this.currentInputIndex < this.inputFieldOrder.length) {\n        this.promptForCurrentField();\n      } else {\n        this.chatInterfaceComp.addAiResponse('Thank you! Running the workflow...');\n        this.executeWorkflow();\n      }\n    };\n    reader.readAsDataURL(file);\n  }\n  // Panel management methods\n  toggleLeftPanel() {\n    this.isLeftPanelCollapsed = !this.isLeftPanelCollapsed;\n  }\n  toggleAgentExpansion(index) {\n    this.expandedAgents[index] = !this.expandedAgents[index];\n  }\n  initializeAgentStates() {\n    const agentCount = this.pipelineAgents.length || this.workflowAgents.length;\n    this.expandedAgents = new Array(agentCount).fill(false);\n    this.agentStatuses = new Array(agentCount).fill(0).map(() => ({\n      completed: false\n    }));\n    this.showAllInputs = new Array(agentCount).fill(false);\n  }\n  // Process pipeline agents and extract inputs\n  processPipelineAgents() {\n    this.pipelineAgents.forEach((pipelineAgent, index) => {\n      const agent = pipelineAgent.agent;\n      const agentInputs = this.extractAgentInputs(agent);\n      this.agentInputsMap[index] = agentInputs;\n    });\n    // Create combined user input list for form initialization\n    this.userInputList = [];\n    Object.values(this.agentInputsMap).forEach(inputs => {\n      this.userInputList.push(...inputs);\n    });\n  }\n  // Extract inputs for a specific agent\n  extractAgentInputs(agent) {\n    const PLACEHOLDER_PATTERNS = /\\{\\{([a-zA-Z0-9-_]+)\\}\\}/g;\n    const description = agent.task?.description || '';\n    const matches = [...description.matchAll(PLACEHOLDER_PATTERNS)];\n    return matches.map(match => ({\n      input: match[0],\n      // Full placeholder like {{image_1}}\n      placeholder: match[1],\n      // Just the variable name like image_1\n      label: this.formatInputLabel(match[1]),\n      agentName: agent.name\n    }));\n  }\n  // Format input label for display\n  formatInputLabel(placeholder) {\n    return placeholder.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase());\n  }\n  // Initialize stepper\n  initializeStepper() {\n    this.stepperSteps = this.pipelineAgents.map((pipelineAgent, index) => ({\n      id: index,\n      label: pipelineAgent.agent.name,\n      completed: false,\n      active: index === 0\n    }));\n    this.currentStepperStep = 0;\n  }\n  // Extract input fields method (similar to pipeline component)\n  extractInputField(workflowAgents) {\n    const PLACEHOLDER_PATTERNS = /(%\\d+\\$s)|\\{\\{([a-zA-Z0-9-_]+)\\}\\}/g;\n    const placeholderMap = {};\n    workflowAgents.forEach(agent => {\n      const agentName = agent?.name || `Agent ${workflowAgents.indexOf(agent) + 1}`;\n      const agentDescription = agent?.description || agent?.task?.description || '';\n      const matches = agentDescription.matchAll(PLACEHOLDER_PATTERNS) || [];\n      for (const match of matches) {\n        const placeholder = match[1] || match[2];\n        const placeholderInput = match[0];\n        if (!placeholderMap[placeholder]) {\n          placeholderMap[placeholder] = {\n            agents: new Set(),\n            inputs: new Set()\n          };\n        }\n        placeholderMap[placeholder].agents.add(agentName);\n        placeholderMap[placeholder].inputs.add(placeholderInput);\n      }\n    });\n    return Object.entries(placeholderMap).map(([placeholder, {\n      agents,\n      inputs\n    }]) => ({\n      name: [...agents].length > 2 ? `${[...agents].slice(0, -1).join(\", \")} and ${[...agents].at(-1)}` : [...agents].join(\" and \"),\n      placeholder,\n      input: [...inputs][0]\n    }));\n  }\n  // Input validation\n  isInputValid() {\n    return this.workflowForm.valid && !!this.workflowId;\n  }\n  // Stepper event handlers\n  onStepperChange(step) {\n    this.currentStepperStep = step.id;\n    this.expandedAgents[step.id] = true; // Auto-expand current agent\n  }\n  // Get inputs for current step\n  getCurrentStepInputs() {\n    return this.agentInputsMap[this.currentStepperStep] || [];\n  }\n  // Get current agent name\n  getCurrentAgentName() {\n    if (this.pipelineAgents[this.currentStepperStep]) {\n      return this.pipelineAgents[this.currentStepperStep].agent.name;\n    }\n    return `Agent ${this.currentStepperStep + 1}`;\n  }\n  // Get inputs for specific agent\n  getAgentInputs(agentIndex) {\n    return this.agentInputsMap[agentIndex] || [];\n  }\n  // Get combined tools for agent\n  getCombinedTools(agent) {\n    const tools = [...(agent.tools || []), ...(agent.userTools || [])];\n    return tools.map(tool => tool.toolName || tool).join(', ') || 'No tools';\n  }\n  // Toggle show all inputs for agent\n  toggleShowAllInputs(agentIndex) {\n    this.showAllInputs[agentIndex] = !this.showAllInputs[agentIndex];\n  }\n  // Handle input submission\n  onInputSubmit(event) {\n    if (event && event.shiftKey) {\n      return; // Allow shift+enter for new line\n    }\n    if (event) {\n      event.preventDefault();\n    }\n    // Check if current step inputs are valid\n    if (this.isCurrentStepValid()) {\n      this.moveToNextStep();\n    }\n  }\n  // Move to next step\n  moveToNextStep() {\n    if (this.currentStepperStep < this.pipelineAgents.length - 1) {\n      // Mark current step as completed\n      this.agentStatuses[this.currentStepperStep].completed = true;\n      this.stepperSteps[this.currentStepperStep].completed = true;\n      // Move to next step\n      this.currentStepperStep++;\n      this.stepperSteps[this.currentStepperStep].active = true;\n      // Auto-expand next agent\n      this.expandedAgents[this.currentStepperStep] = true;\n      // Add AI response for next step\n      const nextAgent = this.pipelineAgents[this.currentStepperStep];\n      this.chatInterfaceComp.addAiResponse(`Moving to ${nextAgent.agent.name}. Please provide the required inputs.`);\n    } else {\n      // All steps completed, ready to execute\n      this.agentStatuses[this.currentStepperStep].completed = true;\n      this.stepperSteps[this.currentStepperStep].completed = true;\n      this.chatInterfaceComp.addAiResponse('All inputs collected! Ready to execute the workflow.');\n    }\n  }\n  // Check if current step is valid\n  isCurrentStepValid() {\n    const currentInputs = this.getCurrentStepInputs();\n    return currentInputs.every(input => {\n      const control = this.workflowForm.get(input.input);\n      return control && control.valid && control.value;\n    });\n  }\n  // Execute current step\n  executeCurrentStep() {\n    if (this.isCurrentStepValid()) {\n      this.moveToNextStep();\n    }\n  }\n  // Handle image selection\n  onImageSelected(event, inputName) {\n    const file = event.target.files[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = () => {\n        const base64String = reader.result;\n        this.workflowForm.get(inputName)?.setValue(base64String);\n        // Auto-advance if this was the last required input for current step\n        setTimeout(() => {\n          if (this.isCurrentStepValid()) {\n            this.moveToNextStep();\n          }\n        }, 100);\n      };\n      reader.readAsDataURL(file);\n    }\n  }\n  // Trigger file input\n  triggerFileInput(inputName) {\n    const fileInput = document.getElementById('file-' + inputName) || document.getElementById('current-file-' + inputName);\n    if (fileInput) {\n      fileInput.click();\n    }\n  }\n  // Demo workflow for testing\n  loadDemoWorkflow() {\n    this.pipelineAgents = [{\n      serial: 1,\n      agent: {\n        name: 'OCR Agent',\n        task: {\n          description: 'Extract text from {{image_1}} and {{image_2}}'\n        },\n        llm: {\n          modelDeploymentName: 'GPT-4'\n        },\n        tools: [],\n        userTools: []\n      }\n    }, {\n      serial: 2,\n      agent: {\n        name: 'Analysis Agent',\n        task: {\n          description: 'Analyze the extracted text and generate {{report_type}} report'\n        },\n        llm: {\n          modelDeploymentName: 'Claude-3'\n        },\n        tools: [],\n        userTools: []\n      }\n    }];\n    this.workflowName = 'Demo OCR Workflow';\n    this.processPipelineAgents();\n    this.initializeForm();\n    this.initializeAgentStates();\n    this.initializeStepper();\n    if (this.userInputList.length === 0) {\n      this.disableChat = true;\n    }\n  }\n};\n__decorate([ViewChild(ChatInterfaceComponent, {\n  static: false\n})], WorkflowExecutionComponent.prototype, \"chatInterfaceComp\", void 0);\nWorkflowExecutionComponent = __decorate([Component({\n  selector: 'app-workflow-execution',\n  standalone: true,\n  imports: [CommonModule, FormsModule, ReactiveFormsModule, ChatInterfaceComponent, AgentActivityComponent, AgentOutputComponent, TabsComponent, ButtonComponent, IconComponent, StepperComponent],\n  templateUrl: './workflow-execution.component.html',\n  styleUrls: ['./workflow-execution.component.scss']\n})], WorkflowExecutionComponent);\nexport { WorkflowExecutionComponent };", "map": {"version": 3, "names": ["Component", "ViewChild", "CommonModule", "Subject", "takeUntil", "FormsModule", "ReactiveFormsModule", "Validators", "ChatInterfaceComponent", "AgentActivityComponent", "AgentOutputComponent", "ButtonComponent", "IconComponent", "TabsComponent", "environment", "workflowConstants", "ExecutionStatus", "WorkflowExecutionComponent", "route", "router", "workflowService", "tokenStorage", "loaderService", "formBuilder", "navigationTabs", "id", "label", "disabled", "workflowId", "workflowName", "constants", "chatInterfaceComp", "activityLogs", "activityProgress", "executionDetails", "isRunning", "status", "notStarted", "chatMessages", "isProcessingChat", "inputText", "agentOutputs", "workflowForm", "fileType", "executionStartTime", "executionCompleted", "executionId", "workflowLogs", "enableStreamingLog", "enableLogStreaming", "isExecutionComplete", "progressInterval", "destroy$", "selectedTab", "demoTabs", "errorMsg", "resMessage", "taskMessage", "isJsonValid", "disable<PERSON>hat", "selectedFiles", "workflowAgents", "userInputList", "progress", "isLoading", "loaderColor", "inputFieldOrder", "currentInputIndex", "activeTabId", "isLeftPanelCollapsed", "expandedAgents", "agentStatuses", "showAllInputs", "stepperSteps", "currentStepperStep", "pipelineAgents", "agentInputsMap", "constructor", "ngOnInit", "disable<PERSON><PERSON><PERSON>", "crypto", "randomUUID", "paramMap", "pipe", "subscribe", "params", "get", "loadWorkflow", "navigate", "ngOnDestroy", "next", "complete", "<PERSON><PERSON><PERSON><PERSON>", "onTabChange", "event", "console", "log", "from", "text", "group", "getWorkflowById", "res", "pipeline", "pipeLineAgents", "name", "processPipelineAgents", "extractInputField", "length", "initializeForm", "initializeAgentStates", "initializeStepper", "startInputCollection", "error", "err", "loadDemoWorkflow", "isImageInput", "input", "match", "variableName", "trim", "startsWith", "for<PERSON>ach", "addControl", "control", "required", "startFakeProgress", "setInterval", "stopFakeProgress", "clearInterval", "setTimeout", "handleChatMessage", "message", "addAiResponse", "executeWorkflow", "field", "setValue", "promptForCurrentField", "saveLogs", "exportResults", "section", "data", "map", "timestamp", "join", "downloadAsFile", "JSON", "stringify", "filename", "type", "blob", "Blob", "url", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "click", "revokeObjectURL", "handleControlAction", "action", "navigateBack", "editWorkflow", "logExecutionStatus", "delay", "workflowExecProcessing", "push", "content", "color", "getWorkflowLogs", "workflowLogConnect", "validate<PERSON><PERSON>", "output", "parsedOutput", "parse", "e", "payload", "FormData", "queryString", "running", "file", "append", "value", "getDaUsername", "pipeLineId", "userInputs", "user", "workflowResponse", "workflowExecComplete", "tasksOutputs", "task", "title", "<PERSON><PERSON><PERSON>", "description", "expected_output", "summary", "raw", "completed", "detail", "workflowLogDisconnect", "workflowLogFailed", "handleAttachment", "onAttachmentsSelected", "files", "onImageSelected", "Object", "keys", "controls", "reader", "FileReader", "onload", "base64String", "result", "readAsDataURL", "toggleLeftPanel", "toggleAgentExpansion", "index", "agentCount", "Array", "fill", "pipelineAgent", "agent", "agentInputs", "extractAgentInputs", "values", "inputs", "PLACEHOLDER_PATTERNS", "matches", "matchAll", "placeholder", "formatInputLabel", "replace", "l", "toUpperCase", "active", "placeholderM<PERSON>", "indexOf", "agentDescription", "placeholderInput", "agents", "Set", "add", "entries", "slice", "at", "isInputValid", "valid", "onStepperChange", "step", "getCurrentStepInputs", "getCurrentAgentName", "getAgentInputs", "agentIndex", "getCombinedTools", "tools", "userTools", "tool", "toolName", "toggleShowAllInputs", "onInputSubmit", "shift<PERSON>ey", "preventDefault", "isCurrentStepValid", "moveToNextStep", "nextAgent", "currentInputs", "every", "executeCurrentStep", "inputName", "target", "triggerFileInput", "fileInput", "getElementById", "serial", "llm", "modelDeploymentName", "__decorate", "static", "selector", "standalone", "imports", "StepperComponent", "templateUrl", "styleUrls"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\workflows\\workflow-execution\\workflow-execution.component.ts"], "sourcesContent": ["import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';\r\n\r\n// Import child components\r\nimport { ChatInterfaceComponent } from '@shared/components/chat-interface/chat-interface.component';\r\nimport { ChatMessage } from '@shared/components/chat-window/chat-window.component';\r\nimport {\r\n  AgentActivityComponent,\r\n} from './components/agent-activity/agent-activity.component';\r\nimport {\r\n  AgentOutputComponent,\r\n  AgentOutput as OutputItem,\r\n} from './components/agent-output/agent-output.component';\r\nimport {\r\n  AvaStepperComponent,\r\n  ButtonComponent,\r\n  IconComponent,\r\n  TabItem,\r\n  TabsComponent,\r\n} from '@ava/play-comp-library';\r\nimport { WorkflowService } from '@shared/services/workflow.service';\r\nimport { environment } from '@shared/environments/environment';\r\nimport workflowConstants from './../constants/workflows.json';\r\nimport { TokenStorageService, LoaderService } from '@shared/index';\r\nimport { AvaTab } from '@shared/models/tab.model';\r\nimport { ExecutionStatus, ActivityLog } from '@shared/models/execution.model';\r\nimport { AgentActivityExecutionDetails } from './components/agent-activity/agent-activity.component';\r\n\r\n\r\n@Component({\r\n  selector: 'app-workflow-execution',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    ChatInterfaceComponent,\r\n    AgentActivityComponent,\r\n    AgentOutputComponent,\r\n    TabsComponent,\r\n    ButtonComponent,\r\n    IconComponent,\r\n    StepperComponent,\r\n  ],\r\n  templateUrl: './workflow-execution.component.html',\r\n  styleUrls: ['./workflow-execution.component.scss'],\r\n})\r\nexport class WorkflowExecutionComponent implements OnInit, OnDestroy {\r\n  navigationTabs: TabItem[] = [\r\n    { id: 'nav-home', label: 'Agent Activity' },\r\n    { id: 'nav-products', label: 'Agent Output' },\r\n    { id: 'nav-services', label: 'Preview', disabled: true },\r\n  ];\r\n  // Workflow details\r\n  workflowId: string | null = null;\r\n  workflowName: string = 'Workflow';\r\n\r\n  constants = workflowConstants as Record<string, any>;\r\n\r\n  @ViewChild(ChatInterfaceComponent, { static: false })\r\n  chatInterfaceComp!: ChatInterfaceComponent;\r\n\r\n  // Activity logs\r\n  activityLogs: ActivityLog[] = [];\r\n  activityProgress: number = 0;\r\n  executionDetails?: AgentActivityExecutionDetails;\r\n  isRunning: boolean = false;\r\n  status: ExecutionStatus = ExecutionStatus.notStarted;\r\n\r\n  // Chat messages\r\n  chatMessages: ChatMessage[] = [];\r\n  isProcessingChat: boolean = false;\r\n  inputText = '';\r\n\r\n  // Agent outputs\r\n  agentOutputs: OutputItem[] = [];\r\n  public workflowForm!: FormGroup;\r\n  public fileType : string = '.zip';\r\n\r\n  // Execution state\r\n  executionStartTime: Date | null = null;\r\n  executionCompleted: boolean = false;\r\n  executionId!: string;\r\n\r\n  public workflowLogs: any[] = [];\r\n  enableStreamingLog = environment.enableLogStreaming || 'all';\r\n\r\n  public isExecutionComplete: boolean = false;\r\n  progressInterval: any;\r\n\r\n  // Component lifecycle\r\n  private destroy$ = new Subject<void>();\r\n  selectedTab: string = 'Agent Activity';\r\n  demoTabs: AvaTab[] = [\r\n    { id: 'activity', label: 'Agent Activity' },\r\n    { id: 'agents', label: 'Agent Output' },\r\n    { id: 'preview', label: 'Preview', disabled: true },\r\n  ];\r\n  errorMsg = false;\r\n  resMessage: any;\r\n  taskMessage = [];\r\n  isJsonValid = false;\r\n  disableChat : boolean = false;\r\n  selectedFiles: File[] = [];\r\n  workflowAgents: any[] = [];\r\n  userInputList: any[] = [];\r\n  progress = 0;\r\n  isLoading = false;\r\n  loaderColor: string = '';\r\n\r\n  inputFieldOrder: string[] = [];\r\n  currentInputIndex: number = 0;\r\n  activeTabId: string = 'nav-home';\r\n\r\n  // Panel state\r\n  isLeftPanelCollapsed = false;\r\n  expandedAgents: boolean[] = [];\r\n  agentStatuses: { completed: boolean }[] = [];\r\n  showAllInputs: boolean[] = [];\r\n\r\n  // Stepper state\r\n  stepperSteps: any[] = [];\r\n  currentStepperStep = 0;\r\n\r\n  // Pipeline data\r\n  pipelineAgents: any[] = [];\r\n  agentInputsMap: { [agentIndex: number]: any[] } = {};\r\n  \r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private workflowService: WorkflowService,\r\n    private tokenStorage: TokenStorageService,\r\n    private loaderService: LoaderService,\r\n    private formBuilder: FormBuilder,\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loaderService.disableLoader();\r\n    this.selectedTab = 'Agent Activity';\r\n    this.executionId = crypto.randomUUID();\r\n    // Get workflow ID from route params\r\n    this.route.paramMap.pipe(takeUntil(this.destroy$)).subscribe((params) => {\r\n      this.workflowId = params.get('id');\r\n      if (this.workflowId) {\r\n        this.loadWorkflow(this.workflowId);\r\n      } else {\r\n        // No workflow ID, redirect back to workflows page\r\n        this.router.navigate(['/build/workflows']);\r\n      }\r\n    });\r\n    // this.executeWorkflow()\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n    this.loaderService.enableLoader();\r\n  }\r\n  onTabChange(event: { id: string, label: string }) {\r\n    this.selectedTab = event.label;\r\n    this.activeTabId = event.id;\r\n    console.log('Tab changed:', event);\r\n  }\r\n\r\n  // Load workflow data\r\n  loadWorkflow(id: string): void {\r\n    // In a real app, this would fetch the workflow from a service\r\n    console.log(`Loading workflow with ID: ${id}`);\r\n    this.chatMessages = [\r\n      {\r\n        from: 'ai',\r\n        text: 'I am your workflow assistant. I will help you in executing this workflow.',\r\n      } as ChatMessage,\r\n    ]\r\n    this.workflowForm = this.formBuilder.group({});\r\n\r\n    this.workflowService.getWorkflowById(id).subscribe({\r\n        next: (res) => {\r\n        // Handle pipeline response structure\r\n        if (res.pipeline && res.pipeline.pipeLineAgents) {\r\n          this.pipelineAgents = res.pipeline.pipeLineAgents;\r\n          this.workflowName = res.pipeline.name;\r\n          this.processPipelineAgents();\r\n        } else {\r\n          // Handle workflow response structure\r\n          this.workflowAgents = res.workflowAgents;\r\n          this.workflowName = res.name;\r\n          this.userInputList = this.extractInputField(this.workflowAgents);\r\n        }\r\n\r\n        if(this.userInputList.length === 0){\r\n          this.disableChat = true;\r\n        }\r\n\r\n        this.initializeForm();\r\n        this.initializeAgentStates();\r\n        this.initializeStepper();\r\n        this.startInputCollection();\r\n      },\r\n        error: (err) => {\r\n          // Fallback to demo data for testing\r\n          this.loadDemoWorkflow();\r\n          console.log('Loading demo workflow due to error:', err);\r\n        }\r\n    });\r\n\r\n  }\r\n\r\n  public isImageInput(input: string): boolean {\r\n    const match = input.match(/{{(.*?)}}/);\r\n    if (match && match[1]) {\r\n      const variableName = match[1].trim();\r\n      return variableName.startsWith('image') || variableName.startsWith('Image');\r\n    }\r\n    return false;\r\n  }\r\n\r\n  public initializeForm() {   \r\n    this.userInputList.forEach((label: any) => {\r\n      this.workflowForm.addControl(label.input, this.formBuilder.control('', Validators.required));\r\n    })\r\n  }\r\n\r\n  startFakeProgress() {\r\n    this.progress = 0;\r\n    this.progressInterval = setInterval(() => {\r\n      if (this.progress < 90) {\r\n        this.progress += 5; // Increase slowly\r\n      }\r\n    }, 200); // Adjust speed\r\n  }\r\n\r\n  stopFakeProgress() {\r\n    clearInterval(this.progressInterval);\r\n    this.progress = 100;\r\n\r\n    setTimeout(() => {\r\n      this.isLoading = false;\r\n    }, 500); // Small delay to let user see 100%\r\n  }\r\n\r\n  // Handle new chat message from user\r\n  handleChatMessage(message: string): void {\r\n    // console.log('message ', message, 'is blank', message.trim() === '');\r\n    this.isProcessingChat = true;\r\n    if(message.trim() === ''){\r\n      if(this.inputFieldOrder.length === 0){\r\n        this.chatInterfaceComp.addAiResponse('Executing the workflow...');\r\n        this.executeWorkflow();\r\n      }\r\n      return;\r\n    }\r\n\r\n    if(this.isExecutionComplete || this.currentInputIndex===this.inputFieldOrder.length){\r\n        this.chatInterfaceComp.addAiResponse('Executing the workflow...');\r\n        this.executeWorkflow();\r\n      return;\r\n    }\r\n\r\n    const field = this.inputFieldOrder[this.currentInputIndex];\r\n    if (this.isImageInput(field)) {\r\n      // Ignore text input, wait for file input\r\n      this.chatInterfaceComp.addAiResponse(`Please upload an image file for ${field}`);\r\n      return;\r\n    }\r\n\r\n    this.workflowForm.get(field)?.setValue(message);\r\n    this.currentInputIndex++;\r\n\r\n    if (this.currentInputIndex < this.inputFieldOrder.length) {\r\n      this.promptForCurrentField();\r\n    } else {\r\n      this.chatInterfaceComp.addAiResponse('Thank you for the input! Executing the workflow...');\r\n      this.executeWorkflow();\r\n    }\r\n  }\r\n\r\n  // Save execution logs\r\n  saveLogs(): void {\r\n    console.log('Saving execution logs...');\r\n    // This would typically save to a service\r\n  }\r\n\r\n  // Export results\r\n  exportResults(section: 'activity' | 'output'): void {\r\n    console.log(`Exporting ${section} data...`);\r\n\r\n    if (section === 'activity') {\r\n      const data = this.activityLogs\r\n        .map((log) => `[${log.timestamp}] ${log.message}`)\r\n        .join('\\n');\r\n      this.downloadAsFile(data, 'workflow-activity-logs.txt', 'text/plain');\r\n    } else {\r\n      const data = JSON.stringify(this.agentOutputs, null, 2);\r\n      this.downloadAsFile(data, 'workflow-outputs.json', 'application/json');\r\n    }\r\n  }\r\n\r\n  // Helper method to download data as a file\r\n  private downloadAsFile(data: string, filename: string, type: string): void {\r\n    const blob = new Blob([data], { type });\r\n    const url = URL.createObjectURL(blob);\r\n    const link = document.createElement('a');\r\n    link.href = url;\r\n    link.download = filename;\r\n    link.click();\r\n    URL.revokeObjectURL(url);\r\n  }\r\n\r\n  // Handle controls for execution\r\n  handleControlAction(action: 'play' | 'pause' | 'stop'): void {\r\n    console.log(`Control action: ${action}`);\r\n    // In a real app, this would control the workflow execution\r\n\r\n    if (action === 'play') {\r\n      this.isRunning = true;\r\n    } else if (action === 'pause' || action === 'stop') {\r\n      this.isRunning = false;\r\n    }\r\n  }\r\n\r\n  // Navigate back to workflow listing\r\n  navigateBack(): void {\r\n    this.router.navigate(['/build/workflows']);\r\n  }\r\n\r\n  // Navigate to edit workflow\r\n  editWorkflow(): void {\r\n    if (this.workflowId) {\r\n      this.router.navigate(['/build/workflows/edit', this.workflowId]);\r\n    }\r\n  }\r\n\r\n  public logExecutionStatus(delay: number = 2000) {\r\n    setTimeout(() => {\r\n      if (!this.isExecutionComplete) {\r\n        console.log(this.constants);\r\n        console.log(this.constants['labels'].workflowExecProcessing);\r\n        this.workflowLogs.push({\r\n          content: this.constants['labels'].workflowExecProcessing,\r\n          color: '#F9DB24',\r\n        });\r\n      }\r\n    }, delay);\r\n  }\r\n\r\n  // public parseAnsiString(ansiString: string) {\r\n  //   const regex = ansiRegex();\r\n  //   const parts = ansiString.split(regex);\r\n  //   const matches = [...ansiString.matchAll(regex)];\r\n  //   parts.forEach((part, index) => {\r\n  //     if (part.trim() !== '') {\r\n  //       let colorCode = matches[index - 1][0];\r\n  //       if (index - 2 >= 0 && matches[index - 2]?.includes('\\u001b[1m')) {\r\n  //         colorCode = `\\u001b[1m${colorCode}`;\r\n  //       }\r\n  //       this.workflowLogs.push({\r\n  //         content: part,\r\n  //         color: this.colorMap[colorCode] || 'white',\r\n  //       });\r\n  //     }\r\n  //   });\r\n  // }\r\n\r\n  public getWorkflowLogs(executionId: string) {\r\n    this.workflowService\r\n      .workflowLogConnect(executionId)\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (message) => {\r\n          console.log('message: ', message);\r\n          const { content, color } = message;\r\n          if (color) {\r\n            this.workflowLogs.push({ content, color });\r\n          } else if (this.enableStreamingLog === 'all') {\r\n            // this.parseAnsiString(content);\r\n          }\r\n        },\r\n        error: (err) => {\r\n          this.workflowLogs.push({\r\n            content: this.constants['workflowLog'],\r\n            color: 'red',\r\n          });\r\n          console.error('WebSocket error:', err);\r\n        },\r\n        complete: () => {\r\n          this.logExecutionStatus();\r\n          console.log('WebSocket connection closed');\r\n        },\r\n      });\r\n  }\r\n\r\n  // public parseAnsiString(ansiString: string) {\r\n  //   const regex = ansiRegex();\r\n  //   const parts = ansiString.split(regex);\r\n  //   const matches = [...ansiString.matchAll(regex)];\r\n  //   parts.forEach((part, index) => {\r\n  //     if (part.trim() !== '') {\r\n  //       let colorCode = matches[index-1][0];\r\n  //       if(index - 2 >= 0 && matches[index-2]?.includes('\\u001b[1m')) {\r\n  //         colorCode = `\\u001b[1m${colorCode}`;\r\n  //       }\r\n  //       this.workflowLogs.push({\r\n  //         content: part, \r\n  //         color: this.colorMap[colorCode] || 'white', \r\n  //       });\r\n  //     }\r\n  //   });\r\n  // }\r\n\r\n  public validateJson(output: string): any | null {\r\n    this.isJsonValid = false;\r\n    try {\r\n      const parsedOutput = JSON.parse(output);\r\n      this.isJsonValid = true;\r\n      return parsedOutput;\r\n    } catch (e) {\r\n      return null;\r\n    }\r\n  }\r\n\r\n  public executeWorkflow() {\r\n    let payload: FormData | Record<string, any> = new FormData();\r\n    let queryString = '';\r\n\r\n\r\n    this.status = ExecutionStatus.running;\r\n    if (this.selectedFiles.length) {\r\n      this.selectedFiles.forEach((file) => {\r\n        payload.append('files', file);\r\n      });\r\n      payload.append('workflowId', this.workflowId);\r\n      payload.append('userInputs', JSON.stringify(this.workflowForm.value));\r\n      payload.append('user', this.tokenStorage.getDaUsername());\r\n      payload.append('executionId', this.executionId);\r\n      queryString = '/files';\r\n    } else {\r\n      payload = {\r\n        pipeLineId: this.workflowId,\r\n        userInputs: this.workflowForm.value,\r\n        executionId: this.executionId,\r\n        user: this.tokenStorage.getDaUsername(),\r\n      };\r\n    }\r\n\r\n    this.getWorkflowLogs(this.executionId);\r\n    this.startFakeProgress();\r\n\r\n    this.workflowService\r\n      .executeWorkflow(payload, queryString)\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (res) => {\r\n          this.isProcessingChat = false;\r\n          this.isRunning = false;\r\n          this.chatInterfaceComp.addAiResponse(res?.message || \"Workflow execution completed successfully!\");\r\n\r\n          if (res?.workflowResponse?.pipeline?.output) {\r\n            this.isExecutionComplete = true;\r\n            // console.log(this.constants['labels'].workflowExecComplete);\r\n            this.workflowLogs.push({\r\n              content: this.constants['labels'].workflowExecComplete,\r\n              color: '#0F8251',\r\n            });\r\n            this.errorMsg = false;\r\n            this.resMessage = res?.workflowResponse?.pipeline?.output;\r\n            this.agentOutputs = res?.workflowResponse?.pipeline?.tasksOutputs.map((task: any) => {\r\n              return {\r\n                id: task?.id || '',\r\n                title: task?.title || '',\r\n                content: task?.content || '',\r\n                agentName: task?.agentName || '',\r\n                timestamp: task?.timestamp || '',\r\n                type: task?.type || '',\r\n                description: task?.description || '',\r\n                expected_output: task?.expected_output || '',\r\n                summary: task?.summary || '',\r\n                raw: task?.raw || '',\r\n              };\r\n            })\r\n\r\n            this.taskMessage = res?.workflowResponse?.pipeline?.tasksOutputs.map(\r\n              (task: {\r\n                description: any;\r\n                summary: any;\r\n                raw: any;\r\n                expected_output: any;\r\n              }) => {\r\n                return {\r\n                  description: task.description,\r\n                  summary: task.summary,\r\n                  raw: task.raw,\r\n                  expected_output: task.expected_output,\r\n                };\r\n              },\r\n            );\r\n\r\n            // if(\"file_download_url\" in res?.pipeline){\r\n            //   this.isFileWriter = true;\r\n            //   this.fileDownloadLink = res?.pipeline?.file_download_url;\r\n\r\n            //   if(!this.fileDownloadLink){\r\n            //     this.fileDownloadUrlError = [];\r\n            //     this.fileDownloadUrlError.push(\"Output file is not generated yet!\")\r\n            //   }\r\n            // }\r\n            // this.isAccordian = true\r\n          }\r\n          this.validateJson(this.resMessage);\r\n          this.status = ExecutionStatus.completed;\r\n          this.stopFakeProgress();\r\n          this.selectedFiles = [];\r\n        },\r\n        error: (error) => {\r\n          this.isExecutionComplete = true;\r\n          this.isProcessingChat = false;\r\n          this.errorMsg = true;\r\n          this.resMessage = error?.error?.detail;\r\n          this.workflowService.workflowLogDisconnect();\r\n          this.workflowLogs.push({\r\n            content: this.constants['labels'].workflowLogFailed,\r\n            color: 'red',\r\n          });\r\n          this.chatInterfaceComp.addAiResponse(\r\n            'Something went wrong, Workflow execution has failed.',\r\n          );\r\n          this.selectedFiles = [];\r\n          this.stopFakeProgress();\r\n          console.log('error is', error.message);\r\n        },\r\n      });\r\n  }\r\n\r\n  // public asyncExecutePipeline() {\r\n  //   const payload: FormData = new FormData();\r\n  //   if (this.selectedFiles?.length) {\r\n  //     for (const element of this.selectedFiles) {\r\n  //       payload.append('files', element)\r\n  //     }\r\n  //   }\r\n  //   payload.append('pipeLineId', String(this.workflowId));\r\n  //   payload.append('userInputs', JSON.stringify(this.workflowForm.value));\r\n  //   payload.append('user', this.tokenStorage.getDaUsername() || '');\r\n  //   payload.append('executionId', this.executionId);\r\n\r\n  //   this.workflowService.asyncExecutePipeline(payload).pipe().subscribe({\r\n  //     next: (res: any) => {\r\n  //       if(res) {\r\n  //         // res handling\r\n  //         console.log(res);\r\n  //       }\r\n  //     },\r\n  //     error: e => {\r\n  //       // error handling\r\n  //       console.log(e);\r\n  //     }\r\n  //   })\r\n  // }\r\n\r\n  handleAttachment() {\r\n    console.log('handleAttachment');\r\n  }\r\n\r\n  onAttachmentsSelected(files: File[]) {\r\n    if(this.currentInputIndex===this.inputFieldOrder.length || this.inputFieldOrder.length===0){\r\n      this.selectedFiles = files;\r\n      return;\r\n    }\r\n\r\n    const field = this.inputFieldOrder[this.currentInputIndex];\r\n    if(this.isImageInput(field)){\r\n      if (files && files.length > 0) {\r\n        this.onImageSelected(files[0]);\r\n      }\r\n    } else {\r\n      this.selectedFiles = files;\r\n    }\r\n  }\r\n\r\n  startInputCollection(){\r\n    this.inputFieldOrder = Object.keys(this.workflowForm.controls);\r\n    this.currentInputIndex = 0;\r\n    if (this.inputFieldOrder.length > 0) {\r\n      this.promptForCurrentField();\r\n    }\r\n    else{\r\n      this.disableChat = true;\r\n      this.chatInterfaceComp.addAiResponse('No input is required for this workflow. Click on send button to execute the workflow.');\r\n    }\r\n  }\r\n\r\n  promptForCurrentField() {\r\n    const field = this.inputFieldOrder[this.currentInputIndex];\r\n    if (this.isImageInput(field)) {\r\n      this.fileType = '.jpeg,.png,.jpg,.svg';\r\n      this.chatInterfaceComp.addAiResponse(`Please upload an image for ${field}`);\r\n      // UI should now show a file input for the user\r\n    } else {\r\n      this.fileType = '.zip'; // or whatever default you want for non-image\r\n      this.chatInterfaceComp.addAiResponse(`Please enter the value of ${field}`);\r\n    }\r\n  }\r\n\r\n  onImageSelected(file: File) {\r\n    const field = this.inputFieldOrder[this.currentInputIndex];\r\n    if (!this.isImageInput(field)) return;\r\n\r\n    const reader = new FileReader();\r\n    reader.onload = () => {\r\n      const base64String = (reader.result as string); \r\n      this.workflowForm.get(field)?.setValue(base64String);\r\n      this.currentInputIndex++;\r\n      if (this.currentInputIndex < this.inputFieldOrder.length) {\r\n        this.promptForCurrentField();\r\n      } else {\r\n        this.chatInterfaceComp.addAiResponse('Thank you! Running the workflow...');\r\n        this.executeWorkflow();\r\n      }\r\n    };\r\n    reader.readAsDataURL(file);\r\n  }\r\n\r\n  // Panel management methods\r\n  toggleLeftPanel(): void {\r\n    this.isLeftPanelCollapsed = !this.isLeftPanelCollapsed;\r\n  }\r\n\r\n  toggleAgentExpansion(index: number): void {\r\n    this.expandedAgents[index] = !this.expandedAgents[index];\r\n  }\r\n\r\n  initializeAgentStates(): void {\r\n    const agentCount = this.pipelineAgents.length || this.workflowAgents.length;\r\n    this.expandedAgents = new Array(agentCount).fill(false);\r\n    this.agentStatuses = new Array(agentCount).fill(0).map(() => ({ completed: false }));\r\n    this.showAllInputs = new Array(agentCount).fill(false);\r\n  }\r\n\r\n  // Process pipeline agents and extract inputs\r\n  processPipelineAgents(): void {\r\n    this.pipelineAgents.forEach((pipelineAgent, index) => {\r\n      const agent = pipelineAgent.agent;\r\n      const agentInputs = this.extractAgentInputs(agent);\r\n      this.agentInputsMap[index] = agentInputs;\r\n    });\r\n\r\n    // Create combined user input list for form initialization\r\n    this.userInputList = [];\r\n    Object.values(this.agentInputsMap).forEach(inputs => {\r\n      this.userInputList.push(...inputs);\r\n    });\r\n  }\r\n\r\n  // Extract inputs for a specific agent\r\n  extractAgentInputs(agent: any): any[] {\r\n    const PLACEHOLDER_PATTERNS = /\\{\\{([a-zA-Z0-9-_]+)\\}\\}/g;\r\n    const description = agent.task?.description || '';\r\n    const matches = [...description.matchAll(PLACEHOLDER_PATTERNS)];\r\n\r\n    return matches.map(match => ({\r\n      input: match[0], // Full placeholder like {{image_1}}\r\n      placeholder: match[1], // Just the variable name like image_1\r\n      label: this.formatInputLabel(match[1]),\r\n      agentName: agent.name\r\n    }));\r\n  }\r\n\r\n  // Format input label for display\r\n  formatInputLabel(placeholder: string): string {\r\n    return placeholder\r\n      .replace(/_/g, ' ')\r\n      .replace(/\\b\\w/g, l => l.toUpperCase());\r\n  }\r\n\r\n  // Initialize stepper\r\n  initializeStepper(): void {\r\n    this.stepperSteps = this.pipelineAgents.map((pipelineAgent, index) => ({\r\n      id: index,\r\n      label: pipelineAgent.agent.name,\r\n      completed: false,\r\n      active: index === 0\r\n    }));\r\n    this.currentStepperStep = 0;\r\n  }\r\n\r\n  // Extract input fields method (similar to pipeline component)\r\n  public extractInputField(workflowAgents: any[]) {\r\n    const PLACEHOLDER_PATTERNS = /(%\\d+\\$s)|\\{\\{([a-zA-Z0-9-_]+)\\}\\}/g;\r\n    const placeholderMap: { [key: string]: { agents: Set<string>; inputs: Set<string> } } = {};\r\n\r\n    workflowAgents.forEach((agent: any) => {\r\n      const agentName = agent?.name || `Agent ${workflowAgents.indexOf(agent) + 1}`;\r\n      const agentDescription = agent?.description || agent?.task?.description || '';\r\n      const matches = agentDescription.matchAll(PLACEHOLDER_PATTERNS) || [];\r\n\r\n      for (const match of matches) {\r\n        const placeholder = match[1] || match[2];\r\n        const placeholderInput = match[0];\r\n        if (!placeholderMap[placeholder]) {\r\n          placeholderMap[placeholder] = { agents: new Set(), inputs: new Set() };\r\n        }\r\n        placeholderMap[placeholder].agents.add(agentName);\r\n        placeholderMap[placeholder].inputs.add(placeholderInput);\r\n      }\r\n    });\r\n\r\n    return Object.entries(placeholderMap).map(([placeholder, { agents, inputs }]) => ({\r\n      name: [...agents].length > 2\r\n        ? `${[...agents].slice(0, -1).join(\", \")} and ${[...agents].at(-1)}`\r\n        : [...agents].join(\" and \"),\r\n      placeholder,\r\n      input: [...inputs][0],\r\n    }));\r\n  }\r\n\r\n  // Input validation\r\n  isInputValid(): boolean {\r\n    return this.workflowForm.valid && !!this.workflowId;\r\n  }\r\n\r\n  // Stepper event handlers\r\n  onStepperChange(step: any): void {\r\n    this.currentStepperStep = step.id;\r\n    this.expandedAgents[step.id] = true; // Auto-expand current agent\r\n  }\r\n\r\n  // Get inputs for current step\r\n  getCurrentStepInputs(): any[] {\r\n    return this.agentInputsMap[this.currentStepperStep] || [];\r\n  }\r\n\r\n  // Get current agent name\r\n  getCurrentAgentName(): string {\r\n    if (this.pipelineAgents[this.currentStepperStep]) {\r\n      return this.pipelineAgents[this.currentStepperStep].agent.name;\r\n    }\r\n    return `Agent ${this.currentStepperStep + 1}`;\r\n  }\r\n\r\n  // Get inputs for specific agent\r\n  getAgentInputs(agentIndex: number): any[] {\r\n    return this.agentInputsMap[agentIndex] || [];\r\n  }\r\n\r\n  // Get combined tools for agent\r\n  getCombinedTools(agent: any): string {\r\n    const tools = [...(agent.tools || []), ...(agent.userTools || [])];\r\n    return tools.map(tool => tool.toolName || tool).join(', ') || 'No tools';\r\n  }\r\n\r\n  // Toggle show all inputs for agent\r\n  toggleShowAllInputs(agentIndex: number): void {\r\n    this.showAllInputs[agentIndex] = !this.showAllInputs[agentIndex];\r\n  }\r\n\r\n  // Handle input submission\r\n  onInputSubmit(event?: KeyboardEvent): void {\r\n    if (event && event.shiftKey) {\r\n      return; // Allow shift+enter for new line\r\n    }\r\n\r\n    if (event) {\r\n      event.preventDefault();\r\n    }\r\n\r\n    // Check if current step inputs are valid\r\n    if (this.isCurrentStepValid()) {\r\n      this.moveToNextStep();\r\n    }\r\n  }\r\n\r\n  // Move to next step\r\n  moveToNextStep(): void {\r\n    if (this.currentStepperStep < this.pipelineAgents.length - 1) {\r\n      // Mark current step as completed\r\n      this.agentStatuses[this.currentStepperStep].completed = true;\r\n      this.stepperSteps[this.currentStepperStep].completed = true;\r\n\r\n      // Move to next step\r\n      this.currentStepperStep++;\r\n      this.stepperSteps[this.currentStepperStep].active = true;\r\n\r\n      // Auto-expand next agent\r\n      this.expandedAgents[this.currentStepperStep] = true;\r\n\r\n      // Add AI response for next step\r\n      const nextAgent = this.pipelineAgents[this.currentStepperStep];\r\n      this.chatInterfaceComp.addAiResponse(`Moving to ${nextAgent.agent.name}. Please provide the required inputs.`);\r\n    } else {\r\n      // All steps completed, ready to execute\r\n      this.agentStatuses[this.currentStepperStep].completed = true;\r\n      this.stepperSteps[this.currentStepperStep].completed = true;\r\n      this.chatInterfaceComp.addAiResponse('All inputs collected! Ready to execute the workflow.');\r\n    }\r\n  }\r\n\r\n  // Check if current step is valid\r\n  isCurrentStepValid(): boolean {\r\n    const currentInputs = this.getCurrentStepInputs();\r\n    return currentInputs.every(input => {\r\n      const control = this.workflowForm.get(input.input);\r\n      return control && control.valid && control.value;\r\n    });\r\n  }\r\n\r\n  // Execute current step\r\n  executeCurrentStep(): void {\r\n    if (this.isCurrentStepValid()) {\r\n      this.moveToNextStep();\r\n    }\r\n  }\r\n\r\n  // Handle image selection\r\n  onImageSelected(event: any, inputName: string): void {\r\n    const file = event.target.files[0];\r\n    if (file) {\r\n      const reader = new FileReader();\r\n      reader.onload = () => {\r\n        const base64String = reader.result as string;\r\n        this.workflowForm.get(inputName)?.setValue(base64String);\r\n\r\n        // Auto-advance if this was the last required input for current step\r\n        setTimeout(() => {\r\n          if (this.isCurrentStepValid()) {\r\n            this.moveToNextStep();\r\n          }\r\n        }, 100);\r\n      };\r\n      reader.readAsDataURL(file);\r\n    }\r\n  }\r\n\r\n  // Trigger file input\r\n  triggerFileInput(inputName: string): void {\r\n    const fileInput = document.getElementById('file-' + inputName) ||\r\n                     document.getElementById('current-file-' + inputName);\r\n    if (fileInput) {\r\n      fileInput.click();\r\n    }\r\n  }\r\n\r\n  // Demo workflow for testing\r\n  loadDemoWorkflow(): void {\r\n    this.pipelineAgents = [\r\n      {\r\n        serial: 1,\r\n        agent: {\r\n          name: 'OCR Agent',\r\n          task: {\r\n            description: 'Extract text from {{image_1}} and {{image_2}}'\r\n          },\r\n          llm: { modelDeploymentName: 'GPT-4' },\r\n          tools: [],\r\n          userTools: []\r\n        }\r\n      },\r\n      {\r\n        serial: 2,\r\n        agent: {\r\n          name: 'Analysis Agent',\r\n          task: {\r\n            description: 'Analyze the extracted text and generate {{report_type}} report'\r\n          },\r\n          llm: { modelDeploymentName: 'Claude-3' },\r\n          tools: [],\r\n          userTools: []\r\n        }\r\n      }\r\n    ];\r\n\r\n    this.workflowName = 'Demo OCR Workflow';\r\n    this.processPipelineAgents();\r\n    this.initializeForm();\r\n    this.initializeAgentStates();\r\n    this.initializeStepper();\r\n\r\n    if (this.userInputList.length === 0) {\r\n      this.disableChat = true;\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAqBC,SAAS,QAAQ,eAAe;AAEvE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;AACzC,SAAiCC,WAAW,EAAEC,mBAAmB,EAAEC,UAAU,QAAQ,gBAAgB;AAErG;AACA,SAASC,sBAAsB,QAAQ,4DAA4D;AAEnG,SACEC,sBAAsB,QACjB,sDAAsD;AAC7D,SACEC,oBAAoB,QAEf,kDAAkD;AACzD,SAEEC,eAAe,EACfC,aAAa,EAEbC,aAAa,QACR,wBAAwB;AAE/B,SAASC,WAAW,QAAQ,kCAAkC;AAC9D,OAAOC,iBAAiB,MAAM,+BAA+B;AAG7D,SAASC,eAAe,QAAqB,gCAAgC;AAsBtE,IAAMC,0BAA0B,GAAhC,MAAMA,0BAA0B;EAmF3BC,KAAA;EACAC,MAAA;EACAC,eAAA;EACAC,YAAA;EACAC,aAAA;EACAC,WAAA;EAvFVC,cAAc,GAAc,CAC1B;IAAEC,EAAE,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAgB,CAAE,EAC3C;IAAED,EAAE,EAAE,cAAc;IAAEC,KAAK,EAAE;EAAc,CAAE,EAC7C;IAAED,EAAE,EAAE,cAAc;IAAEC,KAAK,EAAE,SAAS;IAAEC,QAAQ,EAAE;EAAI,CAAE,CACzD;EACD;EACAC,UAAU,GAAkB,IAAI;EAChCC,YAAY,GAAW,UAAU;EAEjCC,SAAS,GAAGf,iBAAwC;EAGpDgB,iBAAiB;EAEjB;EACAC,YAAY,GAAkB,EAAE;EAChCC,gBAAgB,GAAW,CAAC;EAC5BC,gBAAgB;EAChBC,SAAS,GAAY,KAAK;EAC1BC,MAAM,GAAoBpB,eAAe,CAACqB,UAAU;EAEpD;EACAC,YAAY,GAAkB,EAAE;EAChCC,gBAAgB,GAAY,KAAK;EACjCC,SAAS,GAAG,EAAE;EAEd;EACAC,YAAY,GAAiB,EAAE;EACxBC,YAAY;EACZC,QAAQ,GAAY,MAAM;EAEjC;EACAC,kBAAkB,GAAgB,IAAI;EACtCC,kBAAkB,GAAY,KAAK;EACnCC,WAAW;EAEJC,YAAY,GAAU,EAAE;EAC/BC,kBAAkB,GAAGlC,WAAW,CAACmC,kBAAkB,IAAI,KAAK;EAErDC,mBAAmB,GAAY,KAAK;EAC3CC,gBAAgB;EAEhB;EACQC,QAAQ,GAAG,IAAIjD,OAAO,EAAQ;EACtCkD,WAAW,GAAW,gBAAgB;EACtCC,QAAQ,GAAa,CACnB;IAAE7B,EAAE,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAgB,CAAE,EAC3C;IAAED,EAAE,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAc,CAAE,EACvC;IAAED,EAAE,EAAE,SAAS;IAAEC,KAAK,EAAE,SAAS;IAAEC,QAAQ,EAAE;EAAI,CAAE,CACpD;EACD4B,QAAQ,GAAG,KAAK;EAChBC,UAAU;EACVC,WAAW,GAAG,EAAE;EAChBC,WAAW,GAAG,KAAK;EACnBC,WAAW,GAAa,KAAK;EAC7BC,aAAa,GAAW,EAAE;EAC1BC,cAAc,GAAU,EAAE;EAC1BC,aAAa,GAAU,EAAE;EACzBC,QAAQ,GAAG,CAAC;EACZC,SAAS,GAAG,KAAK;EACjBC,WAAW,GAAW,EAAE;EAExBC,eAAe,GAAa,EAAE;EAC9BC,iBAAiB,GAAW,CAAC;EAC7BC,WAAW,GAAW,UAAU;EAEhC;EACAC,oBAAoB,GAAG,KAAK;EAC5BC,cAAc,GAAc,EAAE;EAC9BC,aAAa,GAA6B,EAAE;EAC5CC,aAAa,GAAc,EAAE;EAE7B;EACAC,YAAY,GAAU,EAAE;EACxBC,kBAAkB,GAAG,CAAC;EAEtB;EACAC,cAAc,GAAU,EAAE;EAC1BC,cAAc,GAAoC,EAAE;EAGpDC,YACU3D,KAAqB,EACrBC,MAAc,EACdC,eAAgC,EAChCC,YAAiC,EACjCC,aAA4B,EAC5BC,WAAwB;IALxB,KAAAL,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;EAClB;EAEHuD,QAAQA,CAAA;IACN,IAAI,CAACxD,aAAa,CAACyD,aAAa,EAAE;IAClC,IAAI,CAAC1B,WAAW,GAAG,gBAAgB;IACnC,IAAI,CAACP,WAAW,GAAGkC,MAAM,CAACC,UAAU,EAAE;IACtC;IACA,IAAI,CAAC/D,KAAK,CAACgE,QAAQ,CAACC,IAAI,CAAC/E,SAAS,CAAC,IAAI,CAACgD,QAAQ,CAAC,CAAC,CAACgC,SAAS,CAAEC,MAAM,IAAI;MACtE,IAAI,CAACzD,UAAU,GAAGyD,MAAM,CAACC,GAAG,CAAC,IAAI,CAAC;MAClC,IAAI,IAAI,CAAC1D,UAAU,EAAE;QACnB,IAAI,CAAC2D,YAAY,CAAC,IAAI,CAAC3D,UAAU,CAAC;MACpC,CAAC,MAAM;QACL;QACA,IAAI,CAACT,MAAM,CAACqE,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;MAC5C;IACF,CAAC,CAAC;IACF;EACF;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACrC,QAAQ,CAACsC,IAAI,EAAE;IACpB,IAAI,CAACtC,QAAQ,CAACuC,QAAQ,EAAE;IACxB,IAAI,CAACrE,aAAa,CAACsE,YAAY,EAAE;EACnC;EACAC,WAAWA,CAACC,KAAoC;IAC9C,IAAI,CAACzC,WAAW,GAAGyC,KAAK,CAACpE,KAAK;IAC9B,IAAI,CAAC0C,WAAW,GAAG0B,KAAK,CAACrE,EAAE;IAC3BsE,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEF,KAAK,CAAC;EACpC;EAEA;EACAP,YAAYA,CAAC9D,EAAU;IACrB;IACAsE,OAAO,CAACC,GAAG,CAAC,6BAA6BvE,EAAE,EAAE,CAAC;IAC9C,IAAI,CAACa,YAAY,GAAG,CAClB;MACE2D,IAAI,EAAE,IAAI;MACVC,IAAI,EAAE;KACQ,CACjB;IACD,IAAI,CAACxD,YAAY,GAAG,IAAI,CAACnB,WAAW,CAAC4E,KAAK,CAAC,EAAE,CAAC;IAE9C,IAAI,CAAC/E,eAAe,CAACgF,eAAe,CAAC3E,EAAE,CAAC,CAAC2D,SAAS,CAAC;MAC/CM,IAAI,EAAGW,GAAG,IAAI;QACd;QACA,IAAIA,GAAG,CAACC,QAAQ,IAAID,GAAG,CAACC,QAAQ,CAACC,cAAc,EAAE;UAC/C,IAAI,CAAC5B,cAAc,GAAG0B,GAAG,CAACC,QAAQ,CAACC,cAAc;UACjD,IAAI,CAAC1E,YAAY,GAAGwE,GAAG,CAACC,QAAQ,CAACE,IAAI;UACrC,IAAI,CAACC,qBAAqB,EAAE;QAC9B,CAAC,MAAM;UACL;UACA,IAAI,CAAC5C,cAAc,GAAGwC,GAAG,CAACxC,cAAc;UACxC,IAAI,CAAChC,YAAY,GAAGwE,GAAG,CAACG,IAAI;UAC5B,IAAI,CAAC1C,aAAa,GAAG,IAAI,CAAC4C,iBAAiB,CAAC,IAAI,CAAC7C,cAAc,CAAC;QAClE;QAEA,IAAG,IAAI,CAACC,aAAa,CAAC6C,MAAM,KAAK,CAAC,EAAC;UACjC,IAAI,CAAChD,WAAW,GAAG,IAAI;QACzB;QAEA,IAAI,CAACiD,cAAc,EAAE;QACrB,IAAI,CAACC,qBAAqB,EAAE;QAC5B,IAAI,CAACC,iBAAiB,EAAE;QACxB,IAAI,CAACC,oBAAoB,EAAE;MAC7B,CAAC;MACCC,KAAK,EAAGC,GAAG,IAAI;QACb;QACA,IAAI,CAACC,gBAAgB,EAAE;QACvBnB,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEiB,GAAG,CAAC;MACzD;KACH,CAAC;EAEJ;EAEOE,YAAYA,CAACC,KAAa;IAC/B,MAAMC,KAAK,GAAGD,KAAK,CAACC,KAAK,CAAC,WAAW,CAAC;IACtC,IAAIA,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,EAAE;MACrB,MAAMC,YAAY,GAAGD,KAAK,CAAC,CAAC,CAAC,CAACE,IAAI,EAAE;MACpC,OAAOD,YAAY,CAACE,UAAU,CAAC,OAAO,CAAC,IAAIF,YAAY,CAACE,UAAU,CAAC,OAAO,CAAC;IAC7E;IACA,OAAO,KAAK;EACd;EAEOZ,cAAcA,CAAA;IACnB,IAAI,CAAC9C,aAAa,CAAC2D,OAAO,CAAE/F,KAAU,IAAI;MACxC,IAAI,CAACgB,YAAY,CAACgF,UAAU,CAAChG,KAAK,CAAC0F,KAAK,EAAE,IAAI,CAAC7F,WAAW,CAACoG,OAAO,CAAC,EAAE,EAAEpH,UAAU,CAACqH,QAAQ,CAAC,CAAC;IAC9F,CAAC,CAAC;EACJ;EAEAC,iBAAiBA,CAAA;IACf,IAAI,CAAC9D,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACZ,gBAAgB,GAAG2E,WAAW,CAAC,MAAK;MACvC,IAAI,IAAI,CAAC/D,QAAQ,GAAG,EAAE,EAAE;QACtB,IAAI,CAACA,QAAQ,IAAI,CAAC,CAAC,CAAC;MACtB;IACF,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EACX;EAEAgE,gBAAgBA,CAAA;IACdC,aAAa,CAAC,IAAI,CAAC7E,gBAAgB,CAAC;IACpC,IAAI,CAACY,QAAQ,GAAG,GAAG;IAEnBkE,UAAU,CAAC,MAAK;MACd,IAAI,CAACjE,SAAS,GAAG,KAAK;IACxB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EACX;EAEA;EACAkE,iBAAiBA,CAACC,OAAe;IAC/B;IACA,IAAI,CAAC5F,gBAAgB,GAAG,IAAI;IAC5B,IAAG4F,OAAO,CAACZ,IAAI,EAAE,KAAK,EAAE,EAAC;MACvB,IAAG,IAAI,CAACrD,eAAe,CAACyC,MAAM,KAAK,CAAC,EAAC;QACnC,IAAI,CAAC5E,iBAAiB,CAACqG,aAAa,CAAC,2BAA2B,CAAC;QACjE,IAAI,CAACC,eAAe,EAAE;MACxB;MACA;IACF;IAEA,IAAG,IAAI,CAACnF,mBAAmB,IAAI,IAAI,CAACiB,iBAAiB,KAAG,IAAI,CAACD,eAAe,CAACyC,MAAM,EAAC;MAChF,IAAI,CAAC5E,iBAAiB,CAACqG,aAAa,CAAC,2BAA2B,CAAC;MACjE,IAAI,CAACC,eAAe,EAAE;MACxB;IACF;IAEA,MAAMC,KAAK,GAAG,IAAI,CAACpE,eAAe,CAAC,IAAI,CAACC,iBAAiB,CAAC;IAC1D,IAAI,IAAI,CAACgD,YAAY,CAACmB,KAAK,CAAC,EAAE;MAC5B;MACA,IAAI,CAACvG,iBAAiB,CAACqG,aAAa,CAAC,mCAAmCE,KAAK,EAAE,CAAC;MAChF;IACF;IAEA,IAAI,CAAC5F,YAAY,CAAC4C,GAAG,CAACgD,KAAK,CAAC,EAAEC,QAAQ,CAACJ,OAAO,CAAC;IAC/C,IAAI,CAAChE,iBAAiB,EAAE;IAExB,IAAI,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAACD,eAAe,CAACyC,MAAM,EAAE;MACxD,IAAI,CAAC6B,qBAAqB,EAAE;IAC9B,CAAC,MAAM;MACL,IAAI,CAACzG,iBAAiB,CAACqG,aAAa,CAAC,oDAAoD,CAAC;MAC1F,IAAI,CAACC,eAAe,EAAE;IACxB;EACF;EAEA;EACAI,QAAQA,CAAA;IACN1C,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IACvC;EACF;EAEA;EACA0C,aAAaA,CAACC,OAA8B;IAC1C5C,OAAO,CAACC,GAAG,CAAC,aAAa2C,OAAO,UAAU,CAAC;IAE3C,IAAIA,OAAO,KAAK,UAAU,EAAE;MAC1B,MAAMC,IAAI,GAAG,IAAI,CAAC5G,YAAY,CAC3B6G,GAAG,CAAE7C,GAAG,IAAK,IAAIA,GAAG,CAAC8C,SAAS,KAAK9C,GAAG,CAACmC,OAAO,EAAE,CAAC,CACjDY,IAAI,CAAC,IAAI,CAAC;MACb,IAAI,CAACC,cAAc,CAACJ,IAAI,EAAE,4BAA4B,EAAE,YAAY,CAAC;IACvE,CAAC,MAAM;MACL,MAAMA,IAAI,GAAGK,IAAI,CAACC,SAAS,CAAC,IAAI,CAACzG,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC;MACvD,IAAI,CAACuG,cAAc,CAACJ,IAAI,EAAE,uBAAuB,EAAE,kBAAkB,CAAC;IACxE;EACF;EAEA;EACQI,cAAcA,CAACJ,IAAY,EAAEO,QAAgB,EAAEC,IAAY;IACjE,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACV,IAAI,CAAC,EAAE;MAAEQ;IAAI,CAAE,CAAC;IACvC,MAAMG,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;IACrC,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAGN,GAAG;IACfG,IAAI,CAACI,QAAQ,GAAGX,QAAQ;IACxBO,IAAI,CAACK,KAAK,EAAE;IACZP,GAAG,CAACQ,eAAe,CAACT,GAAG,CAAC;EAC1B;EAEA;EACAU,mBAAmBA,CAACC,MAAiC;IACnDnE,OAAO,CAACC,GAAG,CAAC,mBAAmBkE,MAAM,EAAE,CAAC;IACxC;IAEA,IAAIA,MAAM,KAAK,MAAM,EAAE;MACrB,IAAI,CAAC/H,SAAS,GAAG,IAAI;IACvB,CAAC,MAAM,IAAI+H,MAAM,KAAK,OAAO,IAAIA,MAAM,KAAK,MAAM,EAAE;MAClD,IAAI,CAAC/H,SAAS,GAAG,KAAK;IACxB;EACF;EAEA;EACAgI,YAAYA,CAAA;IACV,IAAI,CAAChJ,MAAM,CAACqE,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;EAC5C;EAEA;EACA4E,YAAYA,CAAA;IACV,IAAI,IAAI,CAACxI,UAAU,EAAE;MACnB,IAAI,CAACT,MAAM,CAACqE,QAAQ,CAAC,CAAC,uBAAuB,EAAE,IAAI,CAAC5D,UAAU,CAAC,CAAC;IAClE;EACF;EAEOyI,kBAAkBA,CAACC,KAAA,GAAgB,IAAI;IAC5CrC,UAAU,CAAC,MAAK;MACd,IAAI,CAAC,IAAI,CAAC/E,mBAAmB,EAAE;QAC7B6C,OAAO,CAACC,GAAG,CAAC,IAAI,CAAClE,SAAS,CAAC;QAC3BiE,OAAO,CAACC,GAAG,CAAC,IAAI,CAAClE,SAAS,CAAC,QAAQ,CAAC,CAACyI,sBAAsB,CAAC;QAC5D,IAAI,CAACxH,YAAY,CAACyH,IAAI,CAAC;UACrBC,OAAO,EAAE,IAAI,CAAC3I,SAAS,CAAC,QAAQ,CAAC,CAACyI,sBAAsB;UACxDG,KAAK,EAAE;SACR,CAAC;MACJ;IACF,CAAC,EAAEJ,KAAK,CAAC;EACX;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEOK,eAAeA,CAAC7H,WAAmB;IACxC,IAAI,CAAC1B,eAAe,CACjBwJ,kBAAkB,CAAC9H,WAAW,CAAC,CAC/BqC,IAAI,CAAC/E,SAAS,CAAC,IAAI,CAACgD,QAAQ,CAAC,CAAC,CAC9BgC,SAAS,CAAC;MACTM,IAAI,EAAGyC,OAAO,IAAI;QAChBpC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEmC,OAAO,CAAC;QACjC,MAAM;UAAEsC,OAAO;UAAEC;QAAK,CAAE,GAAGvC,OAAO;QAClC,IAAIuC,KAAK,EAAE;UACT,IAAI,CAAC3H,YAAY,CAACyH,IAAI,CAAC;YAAEC,OAAO;YAAEC;UAAK,CAAE,CAAC;QAC5C,CAAC,MAAM,IAAI,IAAI,CAAC1H,kBAAkB,KAAK,KAAK,EAAE;UAC5C;QAAA;MAEJ,CAAC;MACDgE,KAAK,EAAGC,GAAG,IAAI;QACb,IAAI,CAAClE,YAAY,CAACyH,IAAI,CAAC;UACrBC,OAAO,EAAE,IAAI,CAAC3I,SAAS,CAAC,aAAa,CAAC;UACtC4I,KAAK,EAAE;SACR,CAAC;QACF3E,OAAO,CAACiB,KAAK,CAAC,kBAAkB,EAAEC,GAAG,CAAC;MACxC,CAAC;MACDtB,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAAC0E,kBAAkB,EAAE;QACzBtE,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC5C;KACD,CAAC;EACN;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEO6E,YAAYA,CAACC,MAAc;IAChC,IAAI,CAACpH,WAAW,GAAG,KAAK;IACxB,IAAI;MACF,MAAMqH,YAAY,GAAG9B,IAAI,CAAC+B,KAAK,CAACF,MAAM,CAAC;MACvC,IAAI,CAACpH,WAAW,GAAG,IAAI;MACvB,OAAOqH,YAAY;IACrB,CAAC,CAAC,OAAOE,CAAC,EAAE;MACV,OAAO,IAAI;IACb;EACF;EAEO5C,eAAeA,CAAA;IACpB,IAAI6C,OAAO,GAAmC,IAAIC,QAAQ,EAAE;IAC5D,IAAIC,WAAW,GAAG,EAAE;IAGpB,IAAI,CAAChJ,MAAM,GAAGpB,eAAe,CAACqK,OAAO;IACrC,IAAI,IAAI,CAACzH,aAAa,CAAC+C,MAAM,EAAE;MAC7B,IAAI,CAAC/C,aAAa,CAAC6D,OAAO,CAAE6D,IAAI,IAAI;QAClCJ,OAAO,CAACK,MAAM,CAAC,OAAO,EAAED,IAAI,CAAC;MAC/B,CAAC,CAAC;MACFJ,OAAO,CAACK,MAAM,CAAC,YAAY,EAAE,IAAI,CAAC3J,UAAU,CAAC;MAC7CsJ,OAAO,CAACK,MAAM,CAAC,YAAY,EAAEtC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACxG,YAAY,CAAC8I,KAAK,CAAC,CAAC;MACrEN,OAAO,CAACK,MAAM,CAAC,MAAM,EAAE,IAAI,CAAClK,YAAY,CAACoK,aAAa,EAAE,CAAC;MACzDP,OAAO,CAACK,MAAM,CAAC,aAAa,EAAE,IAAI,CAACzI,WAAW,CAAC;MAC/CsI,WAAW,GAAG,QAAQ;IACxB,CAAC,MAAM;MACLF,OAAO,GAAG;QACRQ,UAAU,EAAE,IAAI,CAAC9J,UAAU;QAC3B+J,UAAU,EAAE,IAAI,CAACjJ,YAAY,CAAC8I,KAAK;QACnC1I,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7B8I,IAAI,EAAE,IAAI,CAACvK,YAAY,CAACoK,aAAa;OACtC;IACH;IAEA,IAAI,CAACd,eAAe,CAAC,IAAI,CAAC7H,WAAW,CAAC;IACtC,IAAI,CAAC+E,iBAAiB,EAAE;IAExB,IAAI,CAACzG,eAAe,CACjBiH,eAAe,CAAC6C,OAAO,EAAEE,WAAW,CAAC,CACrCjG,IAAI,CAAC/E,SAAS,CAAC,IAAI,CAACgD,QAAQ,CAAC,CAAC,CAC9BgC,SAAS,CAAC;MACTM,IAAI,EAAGW,GAAG,IAAI;QACZ,IAAI,CAAC9D,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACJ,SAAS,GAAG,KAAK;QACtB,IAAI,CAACJ,iBAAiB,CAACqG,aAAa,CAAC/B,GAAG,EAAE8B,OAAO,IAAI,4CAA4C,CAAC;QAElG,IAAI9B,GAAG,EAAEwF,gBAAgB,EAAEvF,QAAQ,EAAEwE,MAAM,EAAE;UAC3C,IAAI,CAAC5H,mBAAmB,GAAG,IAAI;UAC/B;UACA,IAAI,CAACH,YAAY,CAACyH,IAAI,CAAC;YACrBC,OAAO,EAAE,IAAI,CAAC3I,SAAS,CAAC,QAAQ,CAAC,CAACgK,oBAAoB;YACtDpB,KAAK,EAAE;WACR,CAAC;UACF,IAAI,CAACnH,QAAQ,GAAG,KAAK;UACrB,IAAI,CAACC,UAAU,GAAG6C,GAAG,EAAEwF,gBAAgB,EAAEvF,QAAQ,EAAEwE,MAAM;UACzD,IAAI,CAACrI,YAAY,GAAG4D,GAAG,EAAEwF,gBAAgB,EAAEvF,QAAQ,EAAEyF,YAAY,CAAClD,GAAG,CAAEmD,IAAS,IAAI;YAClF,OAAO;cACLvK,EAAE,EAAEuK,IAAI,EAAEvK,EAAE,IAAI,EAAE;cAClBwK,KAAK,EAAED,IAAI,EAAEC,KAAK,IAAI,EAAE;cACxBxB,OAAO,EAAEuB,IAAI,EAAEvB,OAAO,IAAI,EAAE;cAC5ByB,SAAS,EAAEF,IAAI,EAAEE,SAAS,IAAI,EAAE;cAChCpD,SAAS,EAAEkD,IAAI,EAAElD,SAAS,IAAI,EAAE;cAChCM,IAAI,EAAE4C,IAAI,EAAE5C,IAAI,IAAI,EAAE;cACtB+C,WAAW,EAAEH,IAAI,EAAEG,WAAW,IAAI,EAAE;cACpCC,eAAe,EAAEJ,IAAI,EAAEI,eAAe,IAAI,EAAE;cAC5CC,OAAO,EAAEL,IAAI,EAAEK,OAAO,IAAI,EAAE;cAC5BC,GAAG,EAAEN,IAAI,EAAEM,GAAG,IAAI;aACnB;UACH,CAAC,CAAC;UAEF,IAAI,CAAC7I,WAAW,GAAG4C,GAAG,EAAEwF,gBAAgB,EAAEvF,QAAQ,EAAEyF,YAAY,CAAClD,GAAG,CACjEmD,IAKA,IAAI;YACH,OAAO;cACLG,WAAW,EAAEH,IAAI,CAACG,WAAW;cAC7BE,OAAO,EAAEL,IAAI,CAACK,OAAO;cACrBC,GAAG,EAAEN,IAAI,CAACM,GAAG;cACbF,eAAe,EAAEJ,IAAI,CAACI;aACvB;UACH,CAAC,CACF;UAED;UACA;UACA;UAEA;UACA;UACA;UACA;UACA;UACA;QACF;QACA,IAAI,CAACvB,YAAY,CAAC,IAAI,CAACrH,UAAU,CAAC;QAClC,IAAI,CAACpB,MAAM,GAAGpB,eAAe,CAACuL,SAAS;QACvC,IAAI,CAACxE,gBAAgB,EAAE;QACvB,IAAI,CAACnE,aAAa,GAAG,EAAE;MACzB,CAAC;MACDoD,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC9D,mBAAmB,GAAG,IAAI;QAC/B,IAAI,CAACX,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACgB,QAAQ,GAAG,IAAI;QACpB,IAAI,CAACC,UAAU,GAAGwD,KAAK,EAAEA,KAAK,EAAEwF,MAAM;QACtC,IAAI,CAACpL,eAAe,CAACqL,qBAAqB,EAAE;QAC5C,IAAI,CAAC1J,YAAY,CAACyH,IAAI,CAAC;UACrBC,OAAO,EAAE,IAAI,CAAC3I,SAAS,CAAC,QAAQ,CAAC,CAAC4K,iBAAiB;UACnDhC,KAAK,EAAE;SACR,CAAC;QACF,IAAI,CAAC3I,iBAAiB,CAACqG,aAAa,CAClC,sDAAsD,CACvD;QACD,IAAI,CAACxE,aAAa,GAAG,EAAE;QACvB,IAAI,CAACmE,gBAAgB,EAAE;QACvBhC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEgB,KAAK,CAACmB,OAAO,CAAC;MACxC;KACD,CAAC;EACN;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEAwE,gBAAgBA,CAAA;IACd5G,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;EACjC;EAEA4G,qBAAqBA,CAACC,KAAa;IACjC,IAAG,IAAI,CAAC1I,iBAAiB,KAAG,IAAI,CAACD,eAAe,CAACyC,MAAM,IAAI,IAAI,CAACzC,eAAe,CAACyC,MAAM,KAAG,CAAC,EAAC;MACzF,IAAI,CAAC/C,aAAa,GAAGiJ,KAAK;MAC1B;IACF;IAEA,MAAMvE,KAAK,GAAG,IAAI,CAACpE,eAAe,CAAC,IAAI,CAACC,iBAAiB,CAAC;IAC1D,IAAG,IAAI,CAACgD,YAAY,CAACmB,KAAK,CAAC,EAAC;MAC1B,IAAIuE,KAAK,IAAIA,KAAK,CAAClG,MAAM,GAAG,CAAC,EAAE;QAC7B,IAAI,CAACmG,eAAe,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC;MAChC;IACF,CAAC,MAAM;MACL,IAAI,CAACjJ,aAAa,GAAGiJ,KAAK;IAC5B;EACF;EAEA9F,oBAAoBA,CAAA;IAClB,IAAI,CAAC7C,eAAe,GAAG6I,MAAM,CAACC,IAAI,CAAC,IAAI,CAACtK,YAAY,CAACuK,QAAQ,CAAC;IAC9D,IAAI,CAAC9I,iBAAiB,GAAG,CAAC;IAC1B,IAAI,IAAI,CAACD,eAAe,CAACyC,MAAM,GAAG,CAAC,EAAE;MACnC,IAAI,CAAC6B,qBAAqB,EAAE;IAC9B,CAAC,MACG;MACF,IAAI,CAAC7E,WAAW,GAAG,IAAI;MACvB,IAAI,CAAC5B,iBAAiB,CAACqG,aAAa,CAAC,uFAAuF,CAAC;IAC/H;EACF;EAEAI,qBAAqBA,CAAA;IACnB,MAAMF,KAAK,GAAG,IAAI,CAACpE,eAAe,CAAC,IAAI,CAACC,iBAAiB,CAAC;IAC1D,IAAI,IAAI,CAACgD,YAAY,CAACmB,KAAK,CAAC,EAAE;MAC5B,IAAI,CAAC3F,QAAQ,GAAG,sBAAsB;MACtC,IAAI,CAACZ,iBAAiB,CAACqG,aAAa,CAAC,8BAA8BE,KAAK,EAAE,CAAC;MAC3E;IACF,CAAC,MAAM;MACL,IAAI,CAAC3F,QAAQ,GAAG,MAAM,CAAC,CAAC;MACxB,IAAI,CAACZ,iBAAiB,CAACqG,aAAa,CAAC,6BAA6BE,KAAK,EAAE,CAAC;IAC5E;EACF;EAEAwE,eAAeA,CAACxB,IAAU;IACxB,MAAMhD,KAAK,GAAG,IAAI,CAACpE,eAAe,CAAC,IAAI,CAACC,iBAAiB,CAAC;IAC1D,IAAI,CAAC,IAAI,CAACgD,YAAY,CAACmB,KAAK,CAAC,EAAE;IAE/B,MAAM4E,MAAM,GAAG,IAAIC,UAAU,EAAE;IAC/BD,MAAM,CAACE,MAAM,GAAG,MAAK;MACnB,MAAMC,YAAY,GAAIH,MAAM,CAACI,MAAiB;MAC9C,IAAI,CAAC5K,YAAY,CAAC4C,GAAG,CAACgD,KAAK,CAAC,EAAEC,QAAQ,CAAC8E,YAAY,CAAC;MACpD,IAAI,CAAClJ,iBAAiB,EAAE;MACxB,IAAI,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAACD,eAAe,CAACyC,MAAM,EAAE;QACxD,IAAI,CAAC6B,qBAAqB,EAAE;MAC9B,CAAC,MAAM;QACL,IAAI,CAACzG,iBAAiB,CAACqG,aAAa,CAAC,oCAAoC,CAAC;QAC1E,IAAI,CAACC,eAAe,EAAE;MACxB;IACF,CAAC;IACD6E,MAAM,CAACK,aAAa,CAACjC,IAAI,CAAC;EAC5B;EAEA;EACAkC,eAAeA,CAAA;IACb,IAAI,CAACnJ,oBAAoB,GAAG,CAAC,IAAI,CAACA,oBAAoB;EACxD;EAEAoJ,oBAAoBA,CAACC,KAAa;IAChC,IAAI,CAACpJ,cAAc,CAACoJ,KAAK,CAAC,GAAG,CAAC,IAAI,CAACpJ,cAAc,CAACoJ,KAAK,CAAC;EAC1D;EAEA7G,qBAAqBA,CAAA;IACnB,MAAM8G,UAAU,GAAG,IAAI,CAAChJ,cAAc,CAACgC,MAAM,IAAI,IAAI,CAAC9C,cAAc,CAAC8C,MAAM;IAC3E,IAAI,CAACrC,cAAc,GAAG,IAAIsJ,KAAK,CAACD,UAAU,CAAC,CAACE,IAAI,CAAC,KAAK,CAAC;IACvD,IAAI,CAACtJ,aAAa,GAAG,IAAIqJ,KAAK,CAACD,UAAU,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC,CAAChF,GAAG,CAAC,OAAO;MAAE0D,SAAS,EAAE;IAAK,CAAE,CAAC,CAAC;IACpF,IAAI,CAAC/H,aAAa,GAAG,IAAIoJ,KAAK,CAACD,UAAU,CAAC,CAACE,IAAI,CAAC,KAAK,CAAC;EACxD;EAEA;EACApH,qBAAqBA,CAAA;IACnB,IAAI,CAAC9B,cAAc,CAAC8C,OAAO,CAAC,CAACqG,aAAa,EAAEJ,KAAK,KAAI;MACnD,MAAMK,KAAK,GAAGD,aAAa,CAACC,KAAK;MACjC,MAAMC,WAAW,GAAG,IAAI,CAACC,kBAAkB,CAACF,KAAK,CAAC;MAClD,IAAI,CAACnJ,cAAc,CAAC8I,KAAK,CAAC,GAAGM,WAAW;IAC1C,CAAC,CAAC;IAEF;IACA,IAAI,CAAClK,aAAa,GAAG,EAAE;IACvBiJ,MAAM,CAACmB,MAAM,CAAC,IAAI,CAACtJ,cAAc,CAAC,CAAC6C,OAAO,CAAC0G,MAAM,IAAG;MAClD,IAAI,CAACrK,aAAa,CAAC0G,IAAI,CAAC,GAAG2D,MAAM,CAAC;IACpC,CAAC,CAAC;EACJ;EAEA;EACAF,kBAAkBA,CAACF,KAAU;IAC3B,MAAMK,oBAAoB,GAAG,2BAA2B;IACxD,MAAMjC,WAAW,GAAG4B,KAAK,CAAC/B,IAAI,EAAEG,WAAW,IAAI,EAAE;IACjD,MAAMkC,OAAO,GAAG,CAAC,GAAGlC,WAAW,CAACmC,QAAQ,CAACF,oBAAoB,CAAC,CAAC;IAE/D,OAAOC,OAAO,CAACxF,GAAG,CAACxB,KAAK,KAAK;MAC3BD,KAAK,EAAEC,KAAK,CAAC,CAAC,CAAC;MAAE;MACjBkH,WAAW,EAAElH,KAAK,CAAC,CAAC,CAAC;MAAE;MACvB3F,KAAK,EAAE,IAAI,CAAC8M,gBAAgB,CAACnH,KAAK,CAAC,CAAC,CAAC,CAAC;MACtC6E,SAAS,EAAE6B,KAAK,CAACvH;KAClB,CAAC,CAAC;EACL;EAEA;EACAgI,gBAAgBA,CAACD,WAAmB;IAClC,OAAOA,WAAW,CACfE,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAClBA,OAAO,CAAC,OAAO,EAAEC,CAAC,IAAIA,CAAC,CAACC,WAAW,EAAE,CAAC;EAC3C;EAEA;EACA7H,iBAAiBA,CAAA;IACf,IAAI,CAACrC,YAAY,GAAG,IAAI,CAACE,cAAc,CAACkE,GAAG,CAAC,CAACiF,aAAa,EAAEJ,KAAK,MAAM;MACrEjM,EAAE,EAAEiM,KAAK;MACThM,KAAK,EAAEoM,aAAa,CAACC,KAAK,CAACvH,IAAI;MAC/B+F,SAAS,EAAE,KAAK;MAChBqC,MAAM,EAAElB,KAAK,KAAK;KACnB,CAAC,CAAC;IACH,IAAI,CAAChJ,kBAAkB,GAAG,CAAC;EAC7B;EAEA;EACOgC,iBAAiBA,CAAC7C,cAAqB;IAC5C,MAAMuK,oBAAoB,GAAG,qCAAqC;IAClE,MAAMS,cAAc,GAAoE,EAAE;IAE1FhL,cAAc,CAAC4D,OAAO,CAAEsG,KAAU,IAAI;MACpC,MAAM7B,SAAS,GAAG6B,KAAK,EAAEvH,IAAI,IAAI,SAAS3C,cAAc,CAACiL,OAAO,CAACf,KAAK,CAAC,GAAG,CAAC,EAAE;MAC7E,MAAMgB,gBAAgB,GAAGhB,KAAK,EAAE5B,WAAW,IAAI4B,KAAK,EAAE/B,IAAI,EAAEG,WAAW,IAAI,EAAE;MAC7E,MAAMkC,OAAO,GAAGU,gBAAgB,CAACT,QAAQ,CAACF,oBAAoB,CAAC,IAAI,EAAE;MAErE,KAAK,MAAM/G,KAAK,IAAIgH,OAAO,EAAE;QAC3B,MAAME,WAAW,GAAGlH,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC;QACxC,MAAM2H,gBAAgB,GAAG3H,KAAK,CAAC,CAAC,CAAC;QACjC,IAAI,CAACwH,cAAc,CAACN,WAAW,CAAC,EAAE;UAChCM,cAAc,CAACN,WAAW,CAAC,GAAG;YAAEU,MAAM,EAAE,IAAIC,GAAG,EAAE;YAAEf,MAAM,EAAE,IAAIe,GAAG;UAAE,CAAE;QACxE;QACAL,cAAc,CAACN,WAAW,CAAC,CAACU,MAAM,CAACE,GAAG,CAACjD,SAAS,CAAC;QACjD2C,cAAc,CAACN,WAAW,CAAC,CAACJ,MAAM,CAACgB,GAAG,CAACH,gBAAgB,CAAC;MAC1D;IACF,CAAC,CAAC;IAEF,OAAOjC,MAAM,CAACqC,OAAO,CAACP,cAAc,CAAC,CAAChG,GAAG,CAAC,CAAC,CAAC0F,WAAW,EAAE;MAAEU,MAAM;MAAEd;IAAM,CAAE,CAAC,MAAM;MAChF3H,IAAI,EAAE,CAAC,GAAGyI,MAAM,CAAC,CAACtI,MAAM,GAAG,CAAC,GACxB,GAAG,CAAC,GAAGsI,MAAM,CAAC,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACtG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAGkG,MAAM,CAAC,CAACK,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,GAClE,CAAC,GAAGL,MAAM,CAAC,CAAClG,IAAI,CAAC,OAAO,CAAC;MAC7BwF,WAAW;MACXnH,KAAK,EAAE,CAAC,GAAG+G,MAAM,CAAC,CAAC,CAAC;KACrB,CAAC,CAAC;EACL;EAEA;EACAoB,YAAYA,CAAA;IACV,OAAO,IAAI,CAAC7M,YAAY,CAAC8M,KAAK,IAAI,CAAC,CAAC,IAAI,CAAC5N,UAAU;EACrD;EAEA;EACA6N,eAAeA,CAACC,IAAS;IACvB,IAAI,CAAChL,kBAAkB,GAAGgL,IAAI,CAACjO,EAAE;IACjC,IAAI,CAAC6C,cAAc,CAACoL,IAAI,CAACjO,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;EACvC;EAEA;EACAkO,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAAC/K,cAAc,CAAC,IAAI,CAACF,kBAAkB,CAAC,IAAI,EAAE;EAC3D;EAEA;EACAkL,mBAAmBA,CAAA;IACjB,IAAI,IAAI,CAACjL,cAAc,CAAC,IAAI,CAACD,kBAAkB,CAAC,EAAE;MAChD,OAAO,IAAI,CAACC,cAAc,CAAC,IAAI,CAACD,kBAAkB,CAAC,CAACqJ,KAAK,CAACvH,IAAI;IAChE;IACA,OAAO,SAAS,IAAI,CAAC9B,kBAAkB,GAAG,CAAC,EAAE;EAC/C;EAEA;EACAmL,cAAcA,CAACC,UAAkB;IAC/B,OAAO,IAAI,CAAClL,cAAc,CAACkL,UAAU,CAAC,IAAI,EAAE;EAC9C;EAEA;EACAC,gBAAgBA,CAAChC,KAAU;IACzB,MAAMiC,KAAK,GAAG,CAAC,IAAIjC,KAAK,CAACiC,KAAK,IAAI,EAAE,CAAC,EAAE,IAAIjC,KAAK,CAACkC,SAAS,IAAI,EAAE,CAAC,CAAC;IAClE,OAAOD,KAAK,CAACnH,GAAG,CAACqH,IAAI,IAAIA,IAAI,CAACC,QAAQ,IAAID,IAAI,CAAC,CAACnH,IAAI,CAAC,IAAI,CAAC,IAAI,UAAU;EAC1E;EAEA;EACAqH,mBAAmBA,CAACN,UAAkB;IACpC,IAAI,CAACtL,aAAa,CAACsL,UAAU,CAAC,GAAG,CAAC,IAAI,CAACtL,aAAa,CAACsL,UAAU,CAAC;EAClE;EAEA;EACAO,aAAaA,CAACvK,KAAqB;IACjC,IAAIA,KAAK,IAAIA,KAAK,CAACwK,QAAQ,EAAE;MAC3B,OAAO,CAAC;IACV;IAEA,IAAIxK,KAAK,EAAE;MACTA,KAAK,CAACyK,cAAc,EAAE;IACxB;IAEA;IACA,IAAI,IAAI,CAACC,kBAAkB,EAAE,EAAE;MAC7B,IAAI,CAACC,cAAc,EAAE;IACvB;EACF;EAEA;EACAA,cAAcA,CAAA;IACZ,IAAI,IAAI,CAAC/L,kBAAkB,GAAG,IAAI,CAACC,cAAc,CAACgC,MAAM,GAAG,CAAC,EAAE;MAC5D;MACA,IAAI,CAACpC,aAAa,CAAC,IAAI,CAACG,kBAAkB,CAAC,CAAC6H,SAAS,GAAG,IAAI;MAC5D,IAAI,CAAC9H,YAAY,CAAC,IAAI,CAACC,kBAAkB,CAAC,CAAC6H,SAAS,GAAG,IAAI;MAE3D;MACA,IAAI,CAAC7H,kBAAkB,EAAE;MACzB,IAAI,CAACD,YAAY,CAAC,IAAI,CAACC,kBAAkB,CAAC,CAACkK,MAAM,GAAG,IAAI;MAExD;MACA,IAAI,CAACtK,cAAc,CAAC,IAAI,CAACI,kBAAkB,CAAC,GAAG,IAAI;MAEnD;MACA,MAAMgM,SAAS,GAAG,IAAI,CAAC/L,cAAc,CAAC,IAAI,CAACD,kBAAkB,CAAC;MAC9D,IAAI,CAAC3C,iBAAiB,CAACqG,aAAa,CAAC,aAAasI,SAAS,CAAC3C,KAAK,CAACvH,IAAI,uCAAuC,CAAC;IAChH,CAAC,MAAM;MACL;MACA,IAAI,CAACjC,aAAa,CAAC,IAAI,CAACG,kBAAkB,CAAC,CAAC6H,SAAS,GAAG,IAAI;MAC5D,IAAI,CAAC9H,YAAY,CAAC,IAAI,CAACC,kBAAkB,CAAC,CAAC6H,SAAS,GAAG,IAAI;MAC3D,IAAI,CAACxK,iBAAiB,CAACqG,aAAa,CAAC,sDAAsD,CAAC;IAC9F;EACF;EAEA;EACAoI,kBAAkBA,CAAA;IAChB,MAAMG,aAAa,GAAG,IAAI,CAAChB,oBAAoB,EAAE;IACjD,OAAOgB,aAAa,CAACC,KAAK,CAACxJ,KAAK,IAAG;MACjC,MAAMO,OAAO,GAAG,IAAI,CAACjF,YAAY,CAAC4C,GAAG,CAAC8B,KAAK,CAACA,KAAK,CAAC;MAClD,OAAOO,OAAO,IAAIA,OAAO,CAAC6H,KAAK,IAAI7H,OAAO,CAAC6D,KAAK;IAClD,CAAC,CAAC;EACJ;EAEA;EACAqF,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACL,kBAAkB,EAAE,EAAE;MAC7B,IAAI,CAACC,cAAc,EAAE;IACvB;EACF;EAEA;EACA3D,eAAeA,CAAChH,KAAU,EAAEgL,SAAiB;IAC3C,MAAMxF,IAAI,GAAGxF,KAAK,CAACiL,MAAM,CAAClE,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIvB,IAAI,EAAE;MACR,MAAM4B,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,MAAM,GAAG,MAAK;QACnB,MAAMC,YAAY,GAAGH,MAAM,CAACI,MAAgB;QAC5C,IAAI,CAAC5K,YAAY,CAAC4C,GAAG,CAACwL,SAAS,CAAC,EAAEvI,QAAQ,CAAC8E,YAAY,CAAC;QAExD;QACApF,UAAU,CAAC,MAAK;UACd,IAAI,IAAI,CAACuI,kBAAkB,EAAE,EAAE;YAC7B,IAAI,CAACC,cAAc,EAAE;UACvB;QACF,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACDvD,MAAM,CAACK,aAAa,CAACjC,IAAI,CAAC;IAC5B;EACF;EAEA;EACA0F,gBAAgBA,CAACF,SAAiB;IAChC,MAAMG,SAAS,GAAGtH,QAAQ,CAACuH,cAAc,CAAC,OAAO,GAAGJ,SAAS,CAAC,IAC7CnH,QAAQ,CAACuH,cAAc,CAAC,eAAe,GAAGJ,SAAS,CAAC;IACrE,IAAIG,SAAS,EAAE;MACbA,SAAS,CAAClH,KAAK,EAAE;IACnB;EACF;EAEA;EACA7C,gBAAgBA,CAAA;IACd,IAAI,CAACvC,cAAc,GAAG,CACpB;MACEwM,MAAM,EAAE,CAAC;MACTpD,KAAK,EAAE;QACLvH,IAAI,EAAE,WAAW;QACjBwF,IAAI,EAAE;UACJG,WAAW,EAAE;SACd;QACDiF,GAAG,EAAE;UAAEC,mBAAmB,EAAE;QAAO,CAAE;QACrCrB,KAAK,EAAE,EAAE;QACTC,SAAS,EAAE;;KAEd,EACD;MACEkB,MAAM,EAAE,CAAC;MACTpD,KAAK,EAAE;QACLvH,IAAI,EAAE,gBAAgB;QACtBwF,IAAI,EAAE;UACJG,WAAW,EAAE;SACd;QACDiF,GAAG,EAAE;UAAEC,mBAAmB,EAAE;QAAU,CAAE;QACxCrB,KAAK,EAAE,EAAE;QACTC,SAAS,EAAE;;KAEd,CACF;IAED,IAAI,CAACpO,YAAY,GAAG,mBAAmB;IACvC,IAAI,CAAC4E,qBAAqB,EAAE;IAC5B,IAAI,CAACG,cAAc,EAAE;IACrB,IAAI,CAACC,qBAAqB,EAAE;IAC5B,IAAI,CAACC,iBAAiB,EAAE;IAExB,IAAI,IAAI,CAAChD,aAAa,CAAC6C,MAAM,KAAK,CAAC,EAAE;MACnC,IAAI,CAAChD,WAAW,GAAG,IAAI;IACzB;EACF;CACD;AAtzBC2N,UAAA,EADCrR,SAAS,CAACO,sBAAsB,EAAE;EAAE+Q,MAAM,EAAE;AAAK,CAAE,CAAC,C,oEACV;AAbhCtQ,0BAA0B,GAAAqQ,UAAA,EAlBtCtR,SAAS,CAAC;EACTwR,QAAQ,EAAE,wBAAwB;EAClCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPxR,YAAY,EACZG,WAAW,EACXC,mBAAmB,EACnBE,sBAAsB,EACtBC,sBAAsB,EACtBC,oBAAoB,EACpBG,aAAa,EACbF,eAAe,EACfC,aAAa,EACb+Q,gBAAgB,CACjB;EACDC,WAAW,EAAE,qCAAqC;EAClDC,SAAS,EAAE,CAAC,qCAAqC;CAClD,CAAC,C,EACW5Q,0BAA0B,CAm0BtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}