{"ast": null, "code": "// Built-in tools data for <PERSON><PERSON>e\nexport const TOOLS_DATA = [{\n  id: '1',\n  title: 'File Reader Tool',\n  name: 'FileReaderTool',\n  description: 'Reads and processes various file formats including text, JSON, CSV, and more.',\n  category: 'File Operations',\n  isBuiltIn: true,\n  parameters: [{\n    name: 'file_path',\n    type: 'string',\n    description: 'Path to the file to be read',\n    required: true\n  }]\n}, {\n  id: '2',\n  title: 'File Writer Tool',\n  name: 'FileWriterTool',\n  description: 'Creates and writes content to files in various formats.',\n  category: 'File Operations',\n  isBuiltIn: true,\n  parameters: [{\n    name: 'file_path',\n    type: 'string',\n    description: 'Path where the file should be written',\n    required: true\n  }, {\n    name: 'content',\n    type: 'string',\n    description: 'Content to write to the file',\n    required: true\n  }]\n}, {\n  id: '3',\n  title: 'Web Search Tool',\n  name: 'WebSearchTool',\n  description: 'Performs web searches and retrieves relevant information from search engines.',\n  category: 'Web Operations',\n  isBuiltIn: true,\n  parameters: [{\n    name: 'query',\n    type: 'string',\n    description: 'Search query to execute',\n    required: true\n  }, {\n    name: 'max_results',\n    type: 'number',\n    description: 'Maximum number of search results to return',\n    required: false,\n    default: 10\n  }]\n}, {\n  id: '4',\n  title: 'HTTP Request Tool',\n  name: 'HttpRequestTool',\n  description: 'Makes HTTP requests to APIs and web services.',\n  category: 'Web Operations',\n  isBuiltIn: true,\n  parameters: [{\n    name: 'url',\n    type: 'string',\n    description: 'URL to make the HTTP request to',\n    required: true\n  }, {\n    name: 'method',\n    type: 'string',\n    description: 'HTTP method (GET, POST, PUT, DELETE)',\n    required: false,\n    default: 'GET'\n  }, {\n    name: 'headers',\n    type: 'object',\n    description: 'HTTP headers to include in the request',\n    required: false\n  }]\n}, {\n  id: '5',\n  title: 'JSON Parser Tool',\n  name: 'JsonParserTool',\n  description: 'Parses and manipulates JSON data structures.',\n  category: 'Data Processing',\n  isBuiltIn: true,\n  parameters: [{\n    name: 'json_data',\n    type: 'string',\n    description: 'JSON string to parse',\n    required: true\n  }]\n}, {\n  id: '6',\n  title: 'CSV Processor Tool',\n  name: 'CsvProcessorTool',\n  description: 'Processes and manipulates CSV data files.',\n  category: 'Data Processing',\n  isBuiltIn: true,\n  parameters: [{\n    name: 'csv_data',\n    type: 'string',\n    description: 'CSV data to process',\n    required: true\n  }, {\n    name: 'operation',\n    type: 'string',\n    description: 'Operation to perform (read, filter, transform)',\n    required: true\n  }]\n}, {\n  id: '7',\n  title: 'Email Tool',\n  name: 'EmailTool',\n  description: 'Sends emails with customizable content and attachments.',\n  category: 'Communication',\n  isBuiltIn: true,\n  parameters: [{\n    name: 'recipient',\n    type: 'string',\n    description: 'Email address of the recipient',\n    required: true\n  }, {\n    name: 'subject',\n    type: 'string',\n    description: 'Subject line of the email',\n    required: true\n  }, {\n    name: 'body',\n    type: 'string',\n    description: 'Email body content',\n    required: true\n  }]\n}, {\n  id: '8',\n  title: 'Database Query Tool',\n  name: 'DatabaseQueryTool',\n  description: 'Executes database queries and retrieves data.',\n  category: 'Database Operations',\n  isBuiltIn: true,\n  parameters: [{\n    name: 'connection_string',\n    type: 'string',\n    description: 'Database connection string',\n    required: true\n  }, {\n    name: 'query',\n    type: 'string',\n    description: 'SQL query to execute',\n    required: true\n  }]\n}];\nexport var ToolModes = /*#__PURE__*/function (ToolModes) {\n  ToolModes[\"toolCreation\"] = \"TOOL_CREATION\";\n  return ToolModes;\n}(ToolModes || {});\nexport const TOOL_CREATION_USECASE_IDENTIFIER = 'TOOL_CREATION@ASCENDION@PLATFORM_ENGINEERING@AVA@DIGITAL_ASCENDER';", "map": {"version": 3, "names": ["TOOLS_DATA", "id", "title", "name", "description", "category", "isBuiltIn", "parameters", "type", "required", "default", "ToolModes", "TOOL_CREATION_USECASE_IDENTIFIER"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\libraries\\tools\\constants\\builtInTools.ts"], "sourcesContent": ["// Built-in tools data for <PERSON>e\r\nexport const TOOLS_DATA = [\r\n  {\r\n    id: '1',\r\n    title: 'File Reader Tool',\r\n    name: 'FileReaderTool',\r\n    description:\r\n      'Reads and processes various file formats including text, JSON, CSV, and more.',\r\n    category: 'File Operations',\r\n    isBuiltIn: true,\r\n    parameters: [\r\n      {\r\n        name: 'file_path',\r\n        type: 'string',\r\n        description: 'Path to the file to be read',\r\n        required: true,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    id: '2',\r\n    title: 'File Writer Tool',\r\n    name: 'FileWriterTool',\r\n    description: 'Creates and writes content to files in various formats.',\r\n    category: 'File Operations',\r\n    isBuiltIn: true,\r\n    parameters: [\r\n      {\r\n        name: 'file_path',\r\n        type: 'string',\r\n        description: 'Path where the file should be written',\r\n        required: true,\r\n      },\r\n      {\r\n        name: 'content',\r\n        type: 'string',\r\n        description: 'Content to write to the file',\r\n        required: true,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    id: '3',\r\n    title: 'Web Search Tool',\r\n    name: 'WebSearchTool',\r\n    description:\r\n      'Performs web searches and retrieves relevant information from search engines.',\r\n    category: 'Web Operations',\r\n    isBuiltIn: true,\r\n    parameters: [\r\n      {\r\n        name: 'query',\r\n        type: 'string',\r\n        description: 'Search query to execute',\r\n        required: true,\r\n      },\r\n      {\r\n        name: 'max_results',\r\n        type: 'number',\r\n        description: 'Maximum number of search results to return',\r\n        required: false,\r\n        default: 10,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    id: '4',\r\n    title: 'HTTP Request Tool',\r\n    name: 'HttpRequestTool',\r\n    description: 'Makes HTTP requests to APIs and web services.',\r\n    category: 'Web Operations',\r\n    isBuiltIn: true,\r\n    parameters: [\r\n      {\r\n        name: 'url',\r\n        type: 'string',\r\n        description: 'URL to make the HTTP request to',\r\n        required: true,\r\n      },\r\n      {\r\n        name: 'method',\r\n        type: 'string',\r\n        description: 'HTTP method (GET, POST, PUT, DELETE)',\r\n        required: false,\r\n        default: 'GET',\r\n      },\r\n      {\r\n        name: 'headers',\r\n        type: 'object',\r\n        description: 'HTTP headers to include in the request',\r\n        required: false,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    id: '5',\r\n    title: 'JSON Parser Tool',\r\n    name: 'JsonParserTool',\r\n    description: 'Parses and manipulates JSON data structures.',\r\n    category: 'Data Processing',\r\n    isBuiltIn: true,\r\n    parameters: [\r\n      {\r\n        name: 'json_data',\r\n        type: 'string',\r\n        description: 'JSON string to parse',\r\n        required: true,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    id: '6',\r\n    title: 'CSV Processor Tool',\r\n    name: 'CsvProcessorTool',\r\n    description: 'Processes and manipulates CSV data files.',\r\n    category: 'Data Processing',\r\n    isBuiltIn: true,\r\n    parameters: [\r\n      {\r\n        name: 'csv_data',\r\n        type: 'string',\r\n        description: 'CSV data to process',\r\n        required: true,\r\n      },\r\n      {\r\n        name: 'operation',\r\n        type: 'string',\r\n        description: 'Operation to perform (read, filter, transform)',\r\n        required: true,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    id: '7',\r\n    title: 'Email Tool',\r\n    name: 'EmailTool',\r\n    description: 'Sends emails with customizable content and attachments.',\r\n    category: 'Communication',\r\n    isBuiltIn: true,\r\n    parameters: [\r\n      {\r\n        name: 'recipient',\r\n        type: 'string',\r\n        description: 'Email address of the recipient',\r\n        required: true,\r\n      },\r\n      {\r\n        name: 'subject',\r\n        type: 'string',\r\n        description: 'Subject line of the email',\r\n        required: true,\r\n      },\r\n      {\r\n        name: 'body',\r\n        type: 'string',\r\n        description: 'Email body content',\r\n        required: true,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    id: '8',\r\n    title: 'Database Query Tool',\r\n    name: 'DatabaseQueryTool',\r\n    description: 'Executes database queries and retrieves data.',\r\n    category: 'Database Operations',\r\n    isBuiltIn: true,\r\n    parameters: [\r\n      {\r\n        name: 'connection_string',\r\n        type: 'string',\r\n        description: 'Database connection string',\r\n        required: true,\r\n      },\r\n      {\r\n        name: 'query',\r\n        type: 'string',\r\n        description: 'SQL query to execute',\r\n        required: true,\r\n      },\r\n    ],\r\n  },\r\n];\r\n\r\nexport enum ToolModes {\r\n  toolCreation = 'TOOL_CREATION',\r\n}\r\n\r\n\r\nexport const TOOL_CREATION_USECASE_IDENTIFIER = 'TOOL_CREATION@ASCENDION@PLATFORM_ENGINEERING@AVA@DIGITAL_ASCENDER';"], "mappings": "AAAA;AACA,OAAO,MAAMA,UAAU,GAAG,CACxB;EACEC,EAAE,EAAE,GAAG;EACPC,KAAK,EAAE,kBAAkB;EACzBC,IAAI,EAAE,gBAAgB;EACtBC,WAAW,EACT,+EAA+E;EACjFC,QAAQ,EAAE,iBAAiB;EAC3BC,SAAS,EAAE,IAAI;EACfC,UAAU,EAAE,CACV;IACEJ,IAAI,EAAE,WAAW;IACjBK,IAAI,EAAE,QAAQ;IACdJ,WAAW,EAAE,6BAA6B;IAC1CK,QAAQ,EAAE;GACX;CAEJ,EACD;EACER,EAAE,EAAE,GAAG;EACPC,KAAK,EAAE,kBAAkB;EACzBC,IAAI,EAAE,gBAAgB;EACtBC,WAAW,EAAE,yDAAyD;EACtEC,QAAQ,EAAE,iBAAiB;EAC3BC,SAAS,EAAE,IAAI;EACfC,UAAU,EAAE,CACV;IACEJ,IAAI,EAAE,WAAW;IACjBK,IAAI,EAAE,QAAQ;IACdJ,WAAW,EAAE,uCAAuC;IACpDK,QAAQ,EAAE;GACX,EACD;IACEN,IAAI,EAAE,SAAS;IACfK,IAAI,EAAE,QAAQ;IACdJ,WAAW,EAAE,8BAA8B;IAC3CK,QAAQ,EAAE;GACX;CAEJ,EACD;EACER,EAAE,EAAE,GAAG;EACPC,KAAK,EAAE,iBAAiB;EACxBC,IAAI,EAAE,eAAe;EACrBC,WAAW,EACT,+EAA+E;EACjFC,QAAQ,EAAE,gBAAgB;EAC1BC,SAAS,EAAE,IAAI;EACfC,UAAU,EAAE,CACV;IACEJ,IAAI,EAAE,OAAO;IACbK,IAAI,EAAE,QAAQ;IACdJ,WAAW,EAAE,yBAAyB;IACtCK,QAAQ,EAAE;GACX,EACD;IACEN,IAAI,EAAE,aAAa;IACnBK,IAAI,EAAE,QAAQ;IACdJ,WAAW,EAAE,4CAA4C;IACzDK,QAAQ,EAAE,KAAK;IACfC,OAAO,EAAE;GACV;CAEJ,EACD;EACET,EAAE,EAAE,GAAG;EACPC,KAAK,EAAE,mBAAmB;EAC1BC,IAAI,EAAE,iBAAiB;EACvBC,WAAW,EAAE,+CAA+C;EAC5DC,QAAQ,EAAE,gBAAgB;EAC1BC,SAAS,EAAE,IAAI;EACfC,UAAU,EAAE,CACV;IACEJ,IAAI,EAAE,KAAK;IACXK,IAAI,EAAE,QAAQ;IACdJ,WAAW,EAAE,iCAAiC;IAC9CK,QAAQ,EAAE;GACX,EACD;IACEN,IAAI,EAAE,QAAQ;IACdK,IAAI,EAAE,QAAQ;IACdJ,WAAW,EAAE,sCAAsC;IACnDK,QAAQ,EAAE,KAAK;IACfC,OAAO,EAAE;GACV,EACD;IACEP,IAAI,EAAE,SAAS;IACfK,IAAI,EAAE,QAAQ;IACdJ,WAAW,EAAE,wCAAwC;IACrDK,QAAQ,EAAE;GACX;CAEJ,EACD;EACER,EAAE,EAAE,GAAG;EACPC,KAAK,EAAE,kBAAkB;EACzBC,IAAI,EAAE,gBAAgB;EACtBC,WAAW,EAAE,8CAA8C;EAC3DC,QAAQ,EAAE,iBAAiB;EAC3BC,SAAS,EAAE,IAAI;EACfC,UAAU,EAAE,CACV;IACEJ,IAAI,EAAE,WAAW;IACjBK,IAAI,EAAE,QAAQ;IACdJ,WAAW,EAAE,sBAAsB;IACnCK,QAAQ,EAAE;GACX;CAEJ,EACD;EACER,EAAE,EAAE,GAAG;EACPC,KAAK,EAAE,oBAAoB;EAC3BC,IAAI,EAAE,kBAAkB;EACxBC,WAAW,EAAE,2CAA2C;EACxDC,QAAQ,EAAE,iBAAiB;EAC3BC,SAAS,EAAE,IAAI;EACfC,UAAU,EAAE,CACV;IACEJ,IAAI,EAAE,UAAU;IAChBK,IAAI,EAAE,QAAQ;IACdJ,WAAW,EAAE,qBAAqB;IAClCK,QAAQ,EAAE;GACX,EACD;IACEN,IAAI,EAAE,WAAW;IACjBK,IAAI,EAAE,QAAQ;IACdJ,WAAW,EAAE,gDAAgD;IAC7DK,QAAQ,EAAE;GACX;CAEJ,EACD;EACER,EAAE,EAAE,GAAG;EACPC,KAAK,EAAE,YAAY;EACnBC,IAAI,EAAE,WAAW;EACjBC,WAAW,EAAE,yDAAyD;EACtEC,QAAQ,EAAE,eAAe;EACzBC,SAAS,EAAE,IAAI;EACfC,UAAU,EAAE,CACV;IACEJ,IAAI,EAAE,WAAW;IACjBK,IAAI,EAAE,QAAQ;IACdJ,WAAW,EAAE,gCAAgC;IAC7CK,QAAQ,EAAE;GACX,EACD;IACEN,IAAI,EAAE,SAAS;IACfK,IAAI,EAAE,QAAQ;IACdJ,WAAW,EAAE,2BAA2B;IACxCK,QAAQ,EAAE;GACX,EACD;IACEN,IAAI,EAAE,MAAM;IACZK,IAAI,EAAE,QAAQ;IACdJ,WAAW,EAAE,oBAAoB;IACjCK,QAAQ,EAAE;GACX;CAEJ,EACD;EACER,EAAE,EAAE,GAAG;EACPC,KAAK,EAAE,qBAAqB;EAC5BC,IAAI,EAAE,mBAAmB;EACzBC,WAAW,EAAE,+CAA+C;EAC5DC,QAAQ,EAAE,qBAAqB;EAC/BC,SAAS,EAAE,IAAI;EACfC,UAAU,EAAE,CACV;IACEJ,IAAI,EAAE,mBAAmB;IACzBK,IAAI,EAAE,QAAQ;IACdJ,WAAW,EAAE,4BAA4B;IACzCK,QAAQ,EAAE;GACX,EACD;IACEN,IAAI,EAAE,OAAO;IACbK,IAAI,EAAE,QAAQ;IACdJ,WAAW,EAAE,sBAAsB;IACnCK,QAAQ,EAAE;GACX;CAEJ,CACF;AAED,WAAYE,SAEX,gBAFD,UAAYA,SAAS;EACnBA,SAAA,kCAA8B;EAAA,OADpBA,SAAS;AAErB,CAAC,CAFWA,SAAS,OAEpB;AAGD,OAAO,MAAMC,gCAAgC,GAAG,mEAAmE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}