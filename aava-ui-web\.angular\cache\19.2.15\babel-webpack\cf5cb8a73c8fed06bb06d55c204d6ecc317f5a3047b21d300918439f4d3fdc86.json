{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction SharedNavItemComponent_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 5);\n    i0.ɵɵelement(1, \"img\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"selected\", ctx_r0.selected);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"selected\", ctx_r0.selected);\n    i0.ɵɵproperty(\"src\", ctx_r0.icon, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction SharedNavItemComponent_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 7);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 8);\n    i0.ɵɵelement(2, \"path\", 9);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"open\", ctx_r0.dropdownOpen);\n  }\n}\nexport let SharedNavItemComponent = /*#__PURE__*/(() => {\n  class SharedNavItemComponent {\n    label = '';\n    route = '';\n    selected = false;\n    hasDropdown = false;\n    dropdownOpen = false;\n    dropdownItems = [];\n    icon = '';\n    disabled = false;\n    toggleDropdownEvent = new EventEmitter();\n    navigateEvent = new EventEmitter();\n    selectEvent = new EventEmitter();\n    dropdownItemSelected = new EventEmitter();\n    dropdownPortalOpen = new EventEmitter();\n    toggleDropdown() {\n      this.toggleDropdownEvent.emit();\n    }\n    navigate() {\n      this.navigateEvent.emit(this.route);\n    }\n    select() {\n      this.selectEvent.emit();\n    }\n    onClick(event) {\n      if (this.disabled) {\n        event.preventDefault();\n        event.stopPropagation();\n        return;\n      }\n      if (this.hasDropdown) {\n        event.stopPropagation();\n        const rect = event.currentTarget.getBoundingClientRect();\n        this.dropdownPortalOpen.emit({\n          rect,\n          items: this.dropdownItems,\n          parentLabel: this.label,\n          navItemId: this.label\n        });\n        this.toggleDropdown();\n      } else {\n        this.navigate();\n        this.select();\n      }\n    }\n    onDropdownItemSelected(event) {\n      this.dropdownItemSelected.emit(event);\n    }\n    static ɵfac = function SharedNavItemComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SharedNavItemComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SharedNavItemComponent,\n      selectors: [[\"shared-nav-item\"]],\n      inputs: {\n        label: \"label\",\n        route: \"route\",\n        selected: \"selected\",\n        hasDropdown: \"hasDropdown\",\n        dropdownOpen: \"dropdownOpen\",\n        dropdownItems: \"dropdownItems\",\n        icon: \"icon\",\n        disabled: \"disabled\"\n      },\n      outputs: {\n        toggleDropdownEvent: \"toggleDropdownEvent\",\n        navigateEvent: \"navigateEvent\",\n        selectEvent: \"selectEvent\",\n        dropdownItemSelected: \"dropdownItemSelected\",\n        dropdownPortalOpen: \"dropdownPortalOpen\"\n      },\n      decls: 6,\n      vars: 7,\n      consts: [[1, \"nav-item-container\"], [1, \"nav-item\", 3, \"click\"], [\"class\", \"item-icon\", 3, \"selected\", 4, \"ngIf\"], [1, \"item-label\"], [\"class\", \"dropdown-arrow\", 3, \"open\", 4, \"ngIf\"], [1, \"item-icon\"], [\"alt\", \"\", 1, \"nav-icon\", 3, \"src\"], [1, \"dropdown-arrow\"], [\"width\", \"16\", \"height\", \"16\", \"viewBox\", \"0 0 16 16\", \"fill\", \"none\"], [\"d\", \"M4 6L8 10L12 6\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"]],\n      template: function SharedNavItemComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵlistener(\"click\", function SharedNavItemComponent_Template_div_click_1_listener($event) {\n            return ctx.onClick($event);\n          });\n          i0.ɵɵtemplate(2, SharedNavItemComponent_span_2_Template, 2, 5, \"span\", 2);\n          i0.ɵɵelementStart(3, \"span\", 3);\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(5, SharedNavItemComponent_span_5_Template, 3, 2, \"span\", 4);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"selected\", ctx.selected)(\"disabled\", ctx.disabled);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.icon);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.label);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.hasDropdown);\n        }\n      },\n      dependencies: [CommonModule, i1.NgIf],\n      styles: [\".nav-item-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.nav-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 12px 16px;\\n  border-radius: 999px;\\n  cursor: pointer;\\n  color: #374151;\\n  font-size: 14px;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n  white-space: nowrap;\\n  position: relative;\\n}\\n.nav-item[_ngcontent-%COMP%]:hover {\\n  background: rgba(102, 126, 234, 0.1);\\n  color: #667eea;\\n}\\n.nav-item.selected[_ngcontent-%COMP%] {\\n  background: rgba(102, 126, 234, 0.15);\\n  color: #667eea;\\n  font-weight: 600;\\n}\\n.nav-item.selected[_ngcontent-%COMP%]   .nav-icon[_ngcontent-%COMP%] {\\n  filter: brightness(1.2);\\n}\\n.nav-item.disabled[_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n  pointer-events: none;\\n}\\n.nav-item.disabled[_ngcontent-%COMP%]   .item-icon[_ngcontent-%COMP%], \\n.nav-item.disabled[_ngcontent-%COMP%]   .item-label[_ngcontent-%COMP%], \\n.nav-item.disabled[_ngcontent-%COMP%]   .dropdown-arrow[_ngcontent-%COMP%] {\\n  opacity: 0.6;\\n}\\n.nav-item.disabled[_ngcontent-%COMP%]   .nav-icon[_ngcontent-%COMP%] {\\n  opacity: 0.4;\\n  filter: grayscale(100%);\\n}\\n\\n.item-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 20px;\\n  height: 20px;\\n  transition: all 0.3s ease;\\n}\\n.item-icon.selected[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n\\n.nav-icon[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  object-fit: contain;\\n  transition: all 0.3s ease;\\n}\\n.nav-icon.selected[_ngcontent-%COMP%] {\\n  filter: brightness(1.2) saturate(1.3);\\n}\\n\\n.item-label[_ngcontent-%COMP%] {\\n  font-family: \\\"Mulish\\\", sans-serif;\\n  transition: all 0.3s ease;\\n}\\n\\n.dropdown-arrow[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 16px;\\n  height: 16px;\\n  transition: transform 0.3s ease;\\n}\\n.dropdown-arrow.open[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n.dropdown-arrow[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  color: currentColor;\\n  transition: color 0.3s ease;\\n}\\n\\n@media (max-width: 768px) {\\n  .nav-item[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n    font-size: 13px;\\n  }\\n  .item-icon[_ngcontent-%COMP%] {\\n    width: 18px;\\n    height: 18px;\\n  }\\n  .nav-icon[_ngcontent-%COMP%] {\\n    width: 18px;\\n    height: 18px;\\n  }\\n}\\n@media (prefers-color-scheme: dark) {\\n  .nav-item[_ngcontent-%COMP%] {\\n    color: #e5e7eb;\\n  }\\n  .nav-item[_ngcontent-%COMP%]:hover {\\n    background: rgba(139, 92, 246, 0.15);\\n    color: #a78bfa;\\n  }\\n  .nav-item.selected[_ngcontent-%COMP%] {\\n    background: rgba(139, 92, 246, 0.2);\\n    color: #a78bfa;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return SharedNavItemComponent;\n})();", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵclassProp", "ctx_r0", "selected", "ɵɵadvance", "ɵɵproperty", "icon", "ɵɵsanitizeUrl", "dropdownOpen", "SharedNavItemComponent", "label", "route", "hasDropdown", "dropdownItems", "disabled", "toggleDropdownEvent", "navigateEvent", "selectEvent", "dropdownItemSelected", "dropdownPortalOpen", "toggleDropdown", "emit", "navigate", "select", "onClick", "event", "preventDefault", "stopPropagation", "rect", "currentTarget", "getBoundingClientRect", "items", "parentLabel", "navItemId", "onDropdownItemSelected", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "SharedNavItemComponent_Template", "rf", "ctx", "ɵɵlistener", "SharedNavItemComponent_Template_div_click_1_listener", "$event", "ɵɵtemplate", "SharedNavItemComponent_span_2_Template", "ɵɵtext", "SharedNavItemComponent_span_5_Template", "ɵɵtextInterpolate", "i1", "NgIf", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\components\\nav-item\\nav-item.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\components\\nav-item\\nav-item.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { SharedDropdownItem } from '../app-header/app-header.component';\r\n\r\n@Component({\r\n  selector: 'shared-nav-item',\r\n  standalone: true,\r\n  imports: [CommonModule],\r\n  templateUrl: './nav-item.component.html',\r\n  styleUrl: './nav-item.component.scss',\r\n})\r\nexport class SharedNavItemComponent {\r\n  @Input() label: string = '';\r\n  @Input() route: string = '';\r\n  @Input() selected: boolean = false;\r\n  @Input() hasDropdown: boolean = false;\r\n  @Input() dropdownOpen: boolean = false;\r\n  @Input() dropdownItems: SharedDropdownItem[] = [];\r\n  @Input() icon: string = '';\r\n  @Input() disabled: boolean = false;\r\n\r\n  @Output() toggleDropdownEvent = new EventEmitter<void>();\r\n  @Output() navigateEvent = new EventEmitter<string>();\r\n  @Output() selectEvent = new EventEmitter<void>();\r\n  @Output() dropdownItemSelected = new EventEmitter<{\r\n    route: string;\r\n    label: string;\r\n  }>();\r\n  @Output() dropdownPortalOpen = new EventEmitter<{\r\n    rect: DOMRect;\r\n    items: SharedDropdownItem[];\r\n    parentLabel: string;\r\n    navItemId: string;\r\n  }>();\r\n\r\n  toggleDropdown(): void {\r\n    this.toggleDropdownEvent.emit();\r\n  }\r\n\r\n  navigate(): void {\r\n    this.navigateEvent.emit(this.route);\r\n  }\r\n\r\n  select(): void {\r\n    this.selectEvent.emit();\r\n  }\r\n\r\n  onClick(event: MouseEvent): void {\r\n    if (this.disabled) {\r\n      event.preventDefault();\r\n      event.stopPropagation();\r\n      return;\r\n    }\r\n\r\n    if (this.hasDropdown) {\r\n      event.stopPropagation();\r\n      const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();\r\n      this.dropdownPortalOpen.emit({ \r\n        rect, \r\n        items: this.dropdownItems, \r\n        parentLabel: this.label, \r\n        navItemId: this.label \r\n      });\r\n      this.toggleDropdown();\r\n    } else {\r\n      this.navigate();\r\n      this.select();\r\n    }\r\n  }\r\n\r\n  onDropdownItemSelected(event: { route: string; label: string }): void {\r\n    this.dropdownItemSelected.emit(event);\r\n  }\r\n} ", "<div class=\"nav-item-container\">\r\n  <div \r\n    [class.selected]=\"selected\" \r\n    [class.disabled]=\"disabled\" \r\n    class=\"nav-item\" \r\n    (click)=\"onClick($event)\"\r\n  >\r\n    <span *ngIf=\"icon\" class=\"item-icon\" [class.selected]=\"selected\">\r\n      <img [src]=\"icon\" alt=\"\" class=\"nav-icon\" [class.selected]=\"selected\" />\r\n    </span>\r\n    <span class=\"item-label\">{{ label }}</span>\r\n    <span\r\n      *ngIf=\"hasDropdown\"\r\n      class=\"dropdown-arrow\"\r\n      [class.open]=\"dropdownOpen\"\r\n    >\r\n      <svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" fill=\"none\">\r\n        <path\r\n          d=\"M4 6L8 10L12 6\"\r\n          stroke=\"currentColor\"\r\n          stroke-width=\"2\"\r\n          stroke-linecap=\"round\"\r\n          stroke-linejoin=\"round\"\r\n        />\r\n      </svg>\r\n    </span>\r\n  </div>\r\n</div> "], "mappings": "AAAA,SAAmCA,YAAY,QAAQ,eAAe;AACtE,SAASC,YAAY,QAAQ,iBAAiB;;;;;ICM1CC,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAE,SAAA,aAAwE;IAC1EF,EAAA,CAAAG,YAAA,EAAO;;;;IAF8BH,EAAA,CAAAI,WAAA,aAAAC,MAAA,CAAAC,QAAA,CAA2B;IACpBN,EAAA,CAAAO,SAAA,EAA2B;IAA3BP,EAAA,CAAAI,WAAA,aAAAC,MAAA,CAAAC,QAAA,CAA2B;IAAhEN,EAAA,CAAAQ,UAAA,QAAAH,MAAA,CAAAI,IAAA,EAAAT,EAAA,CAAAU,aAAA,CAAY;;;;;IAGnBV,EAAA,CAAAC,cAAA,cAIC;;IACCD,EAAA,CAAAC,cAAA,aAA4D;IAC1DD,EAAA,CAAAE,SAAA,cAME;IAENF,EADE,CAAAG,YAAA,EAAM,EACD;;;;IAXLH,EAAA,CAAAI,WAAA,SAAAC,MAAA,CAAAM,YAAA,CAA2B;;;ADHjC,WAAaC,sBAAsB;EAA7B,MAAOA,sBAAsB;IACxBC,KAAK,GAAW,EAAE;IAClBC,KAAK,GAAW,EAAE;IAClBR,QAAQ,GAAY,KAAK;IACzBS,WAAW,GAAY,KAAK;IAC5BJ,YAAY,GAAY,KAAK;IAC7BK,aAAa,GAAyB,EAAE;IACxCP,IAAI,GAAW,EAAE;IACjBQ,QAAQ,GAAY,KAAK;IAExBC,mBAAmB,GAAG,IAAIpB,YAAY,EAAQ;IAC9CqB,aAAa,GAAG,IAAIrB,YAAY,EAAU;IAC1CsB,WAAW,GAAG,IAAItB,YAAY,EAAQ;IACtCuB,oBAAoB,GAAG,IAAIvB,YAAY,EAG7C;IACMwB,kBAAkB,GAAG,IAAIxB,YAAY,EAK3C;IAEJyB,cAAcA,CAAA;MACZ,IAAI,CAACL,mBAAmB,CAACM,IAAI,EAAE;IACjC;IAEAC,QAAQA,CAAA;MACN,IAAI,CAACN,aAAa,CAACK,IAAI,CAAC,IAAI,CAACV,KAAK,CAAC;IACrC;IAEAY,MAAMA,CAAA;MACJ,IAAI,CAACN,WAAW,CAACI,IAAI,EAAE;IACzB;IAEAG,OAAOA,CAACC,KAAiB;MACvB,IAAI,IAAI,CAACX,QAAQ,EAAE;QACjBW,KAAK,CAACC,cAAc,EAAE;QACtBD,KAAK,CAACE,eAAe,EAAE;QACvB;MACF;MAEA,IAAI,IAAI,CAACf,WAAW,EAAE;QACpBa,KAAK,CAACE,eAAe,EAAE;QACvB,MAAMC,IAAI,GAAIH,KAAK,CAACI,aAA6B,CAACC,qBAAqB,EAAE;QACzE,IAAI,CAACX,kBAAkB,CAACE,IAAI,CAAC;UAC3BO,IAAI;UACJG,KAAK,EAAE,IAAI,CAAClB,aAAa;UACzBmB,WAAW,EAAE,IAAI,CAACtB,KAAK;UACvBuB,SAAS,EAAE,IAAI,CAACvB;SACjB,CAAC;QACF,IAAI,CAACU,cAAc,EAAE;MACvB,CAAC,MAAM;QACL,IAAI,CAACE,QAAQ,EAAE;QACf,IAAI,CAACC,MAAM,EAAE;MACf;IACF;IAEAW,sBAAsBA,CAACT,KAAuC;MAC5D,IAAI,CAACP,oBAAoB,CAACG,IAAI,CAACI,KAAK,CAAC;IACvC;;uCA7DWhB,sBAAsB;IAAA;;YAAtBA,sBAAsB;MAAA0B,SAAA;MAAAC,MAAA;QAAA1B,KAAA;QAAAC,KAAA;QAAAR,QAAA;QAAAS,WAAA;QAAAJ,YAAA;QAAAK,aAAA;QAAAP,IAAA;QAAAQ,QAAA;MAAA;MAAAuB,OAAA;QAAAtB,mBAAA;QAAAC,aAAA;QAAAC,WAAA;QAAAC,oBAAA;QAAAC,kBAAA;MAAA;MAAAmB,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVjC9C,EADF,CAAAC,cAAA,aAAgC,aAM7B;UADCD,EAAA,CAAAgD,UAAA,mBAAAC,qDAAAC,MAAA;YAAA,OAASH,GAAA,CAAApB,OAAA,CAAAuB,MAAA,CAAe;UAAA,EAAC;UAEzBlD,EAAA,CAAAmD,UAAA,IAAAC,sCAAA,kBAAiE;UAGjEpD,EAAA,CAAAC,cAAA,cAAyB;UAAAD,EAAA,CAAAqD,MAAA,GAAW;UAAArD,EAAA,CAAAG,YAAA,EAAO;UAC3CH,EAAA,CAAAmD,UAAA,IAAAG,sCAAA,kBAIC;UAYLtD,EADE,CAAAG,YAAA,EAAM,EACF;;;UAzBFH,EAAA,CAAAO,SAAA,EAA2B;UAC3BP,EADA,CAAAI,WAAA,aAAA2C,GAAA,CAAAzC,QAAA,CAA2B,aAAAyC,GAAA,CAAA9B,QAAA,CACA;UAIpBjB,EAAA,CAAAO,SAAA,EAAU;UAAVP,EAAA,CAAAQ,UAAA,SAAAuC,GAAA,CAAAtC,IAAA,CAAU;UAGQT,EAAA,CAAAO,SAAA,GAAW;UAAXP,EAAA,CAAAuD,iBAAA,CAAAR,GAAA,CAAAlC,KAAA,CAAW;UAEjCb,EAAA,CAAAO,SAAA,EAAiB;UAAjBP,EAAA,CAAAQ,UAAA,SAAAuC,GAAA,CAAAhC,WAAA,CAAiB;;;qBDLZhB,YAAY,EAAAyD,EAAA,CAAAC,IAAA;MAAAC,MAAA;IAAA;;SAIX9C,sBAAsB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}