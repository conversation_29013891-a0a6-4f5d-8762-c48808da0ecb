{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { HeadingComponent } from '@awe/play-comp-library';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction MarketplaceFooterComponent_div_3_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 14)(1, \"awe-heading\", 15);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((item_r1 == null ? null : item_r1.text) || item_r1);\n  }\n}\nfunction MarketplaceFooterComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"awe-heading\", 11);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\", 12);\n    i0.ɵɵtemplate(4, MarketplaceFooterComponent_div_3_li_4_Template, 3, 1, \"li\", 13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const section_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(section_r2 == null ? null : section_r2.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", section_r2.items);\n  }\n}\nfunction MarketplaceFooterComponent_ng_container_9_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 17);\n    i0.ɵɵtext(1, \"|\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MarketplaceFooterComponent_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"awe-heading\", 15);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, MarketplaceFooterComponent_ng_container_9_span_3_Template, 2, 0, \"span\", 16);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const link_r3 = ctx.$implicit;\n    const last_r4 = ctx.last;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(link_r3 == null ? null : link_r3.text);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !last_r4);\n  }\n}\nexport let MarketplaceFooterComponent = /*#__PURE__*/(() => {\n  class MarketplaceFooterComponent {\n    currentYear = new Date().getFullYear();\n    footerData = {\n      sections: [{\n        title: 'What we do',\n        items: [{\n          text: 'Applied AI'\n        }, {\n          text: 'Data'\n        }, {\n          text: 'Experience'\n        }, {\n          text: 'Platform Engineering'\n        }, {\n          text: 'Product Engineering'\n        }, {\n          text: 'Quality Engineering'\n        }, {\n          text: 'Innovate With Salesforce'\n        }]\n      }, {\n        title: 'Industries',\n        items: [{\n          text: 'Banking and Financial Service'\n        }, {\n          text: 'Communication, Media, & Entertainment'\n        }, {\n          text: 'Healthcare'\n        }, {\n          text: 'High-Tech'\n        }, {\n          text: 'Retail and Consumer Goods'\n        }, {\n          text: 'Travel and Hospitality'\n        }]\n      }, {\n        title: 'Quick Links',\n        items: [{\n          text: 'Home',\n          route: '/home'\n        }, {\n          text: 'Who we are',\n          route: '/about'\n        }, {\n          text: 'Insight and impact',\n          route: '/insights'\n        }, {\n          text: 'Careers India',\n          route: '/careers/india'\n        }, {\n          text: 'Careers North America',\n          route: '/careers/north-america'\n        }, {\n          text: 'Careers Europe',\n          route: '/careers/europe'\n        }, {\n          text: 'Careers Asia Pacific',\n          route: '/careers/asia-pacific'\n        }, {\n          text: 'Global Delivery Hubs',\n          route: '/delivery-hubs'\n        }, {\n          text: 'Contact Us',\n          route: '/contact'\n        }]\n      }],\n      legalLinks: [{\n        text: 'Terms of use',\n        route: '/terms'\n      }, {\n        text: 'Privacy Policy',\n        route: '/privacy'\n      }, {\n        text: 'Accessibility Disclosure',\n        route: '/accessibility'\n      }, {\n        text: 'AO-enhances content notice',\n        route: '/content-notice'\n      }]\n    };\n    static ɵfac = function MarketplaceFooterComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MarketplaceFooterComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MarketplaceFooterComponent,\n      selectors: [[\"app-marketplace-footer\"]],\n      decls: 10,\n      vars: 2,\n      consts: [[1, \"footer\"], [1, \"container-fluid\"], [1, \"row\", \"mb-5\"], [\"class\", \"col-12 col-sm-6 col-lg-3 mb-4 footer-section\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-12\", \"col-sm-6\", \"col-lg-3\", \"mb-4\", \"footer-section\", \"logo-section\"], [\"src\", \"image 338.svg\", \"alt\", \"Acendion Logo\"], [1, \"row\", \"footer-bottom\"], [1, \"col-12\"], [1, \"legal-links\"], [4, \"ngFor\", \"ngForOf\"], [1, \"col-12\", \"col-sm-6\", \"col-lg-3\", \"mb-4\", \"footer-section\"], [\"variant\", \"s1\", \"type\", \"bold\"], [1, \"mt-3\"], [\"class\", \"mb-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"mb-3\"], [\"variant\", \"s2\", \"type\", \"regular\"], [\"class\", \"separator\", 4, \"ngIf\"], [1, \"separator\"]],\n      template: function MarketplaceFooterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"footer\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtemplate(3, MarketplaceFooterComponent_div_3_Template, 5, 2, \"div\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4);\n          i0.ɵɵelement(5, \"img\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 7)(8, \"div\", 8);\n          i0.ɵɵtemplate(9, MarketplaceFooterComponent_ng_container_9_Template, 4, 2, \"ng-container\", 9);\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.footerData.sections);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngForOf\", ctx.footerData.legalLinks);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, HeadingComponent],\n      styles: [\".heading.s2 {\\n  font-size: 16px !important;\\n  cursor: pointer;\\n}\\n\\n.footer[_ngcontent-%COMP%] {\\n  background: #fafafa;\\n  padding: 4rem 0 2rem;\\n  color: var(--Text-Title, #14161f);\\n}\\n@media (max-width: 576px) {\\n  .footer[_ngcontent-%COMP%]   .footer-section[_ngcontent-%COMP%] {\\n    text-align: center;\\n  }\\n}\\n.footer[_ngcontent-%COMP%]   .footer-section[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  list-style: none;\\n  padding: 0;\\n  margin: 0;\\n}\\n@media (max-width: 992px) {\\n  .footer[_ngcontent-%COMP%]   .logo-section[_ngcontent-%COMP%] {\\n    justify-content: center;\\n    margin-top: 1rem;\\n  }\\n}\\n.footer[_ngcontent-%COMP%]   .footer-bottom[_ngcontent-%COMP%]   .legal-links[_ngcontent-%COMP%] {\\n  text-decoration: underline;\\n  display: flex;\\n  justify-content: center;\\n  gap: 1rem;\\n}\\n@media (max-width: 576px) {\\n  .footer[_ngcontent-%COMP%]   .footer-bottom[_ngcontent-%COMP%]   .legal-links[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.75rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3Byb2plY3RzL2VsZGVyLXdhbmQvc3JjL2FwcC9zaGFyZWQvY29tcG9uZW50cy9tYXJrZXRwbGFjZS1mb290ZXIvbWFya2V0cGxhY2UtZm9vdGVyLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsMEJBQUE7RUFDQSxlQUFBO0FBQ0Y7O0FBRUE7RUFDRSxtQkFBQTtFQUNBLG9CQUFBO0VBQ0EsaUNBQUE7QUFDRjtBQUVJO0VBREY7SUFFSSxrQkFBQTtFQUNKO0FBQ0Y7QUFBSTtFQUNFLGdCQUFBO0VBQ0EsVUFBQTtFQUNBLFNBQUE7QUFFTjtBQUdJO0VBREY7SUFFSSx1QkFBQTtJQUNBLGdCQUFBO0VBQUo7QUFDRjtBQUlJO0VBQ0UsMEJBQUE7RUFDQSxhQUFBO0VBQ0EsdUJBQUE7RUFDQSxTQUFBO0FBRk47QUFJTTtFQU5GO0lBT0ksc0JBQUE7SUFDQSxZQUFBO0VBRE47QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIjo6bmctZGVlcCAuaGVhZGluZy5zMiB7XHJcbiAgZm9udC1zaXplOiAxNnB4ICFpbXBvcnRhbnQ7XHJcbiAgY3Vyc29yOiBwb2ludGVyO1xyXG59XHJcblxyXG4uZm9vdGVyIHtcclxuICBiYWNrZ3JvdW5kOiAjZmFmYWZhO1xyXG4gIHBhZGRpbmc6IDRyZW0gMCAycmVtO1xyXG4gIGNvbG9yOiB2YXIoLS1UZXh0LVRpdGxlLCAjMTQxNjFmKTtcclxuXHJcbiAgLmZvb3Rlci1zZWN0aW9uIHtcclxuICAgIEBtZWRpYSAobWF4LXdpZHRoOiA1NzZweCkge1xyXG4gICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcbiAgICB9XHJcbiAgICB1bCB7XHJcbiAgICAgIGxpc3Qtc3R5bGU6IG5vbmU7XHJcbiAgICAgIHBhZGRpbmc6IDA7XHJcbiAgICAgIG1hcmdpbjogMDtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC5sb2dvLXNlY3Rpb24ge1xyXG4gICAgQG1lZGlhIChtYXgtd2lkdGg6IDk5MnB4KSB7XHJcbiAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gICAgICBtYXJnaW4tdG9wOiAxcmVtO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLmZvb3Rlci1ib3R0b20ge1xyXG4gICAgLmxlZ2FsLWxpbmtzIHtcclxuICAgICAgdGV4dC1kZWNvcmF0aW9uOiB1bmRlcmxpbmU7XHJcbiAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gICAgICBnYXA6IDFyZW07XHJcblxyXG4gICAgICBAbWVkaWEgKG1heC13aWR0aDogNTc2cHgpIHtcclxuICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gICAgICAgIGdhcDogMC43NXJlbTtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxufVxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n  return MarketplaceFooterComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "HeadingComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "item_r1", "text", "ɵɵtemplate", "MarketplaceFooterComponent_div_3_li_4_Template", "section_r2", "title", "ɵɵproperty", "items", "ɵɵelementContainerStart", "MarketplaceFooterComponent_ng_container_9_span_3_Template", "link_r3", "last_r4", "MarketplaceFooterComponent", "currentYear", "Date", "getFullYear", "footerData", "sections", "route", "legalLinks", "selectors", "decls", "vars", "consts", "template", "MarketplaceFooterComponent_Template", "rf", "ctx", "MarketplaceFooterComponent_div_3_Template", "ɵɵelement", "MarketplaceFooterComponent_ng_container_9_Template", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\shared\\components\\marketplace-footer\\marketplace-footer.component.ts", "C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\shared\\components\\marketplace-footer\\marketplace-footer.component.html"], "sourcesContent": ["import { CommonModule } from '@angular/common';\r\nimport { Component } from '@angular/core';\r\nimport { HeadingComponent } from '@awe/play-comp-library';\r\n\r\n@Component({\r\n  selector: 'app-marketplace-footer',\r\n  standalone: true,\r\n  imports: [CommonModule, HeadingComponent],\r\n  templateUrl: './marketplace-footer.component.html',\r\n  styleUrl: './marketplace-footer.component.scss',\r\n})\r\nexport class MarketplaceFooterComponent {\r\n  currentYear = new Date().getFullYear();\r\n  footerData = {\r\n    sections: [\r\n      {\r\n        title: 'What we do',\r\n        items: [\r\n          { text: 'Applied AI' },\r\n          { text: 'Data' },\r\n          { text: 'Experience' },\r\n          { text: 'Platform Engineering' },\r\n          { text: 'Product Engineering' },\r\n          { text: 'Quality Engineering' },\r\n          { text: 'Innovate With Salesforce' },\r\n        ],\r\n      },\r\n      {\r\n        title: 'Industries',\r\n        items: [\r\n          { text: 'Banking and Financial Service' },\r\n          { text: 'Communication, Media, & Entertainment' },\r\n          { text: 'Healthcare' },\r\n          { text: 'High-Tech' },\r\n          { text: 'Retail and Consumer Goods' },\r\n          { text: 'Travel and Hospitality' },\r\n        ],\r\n      },\r\n      {\r\n        title: 'Quick Links',\r\n        items: [\r\n          { text: 'Home', route: '/home' },\r\n          { text: 'Who we are', route: '/about' },\r\n          { text: 'Insight and impact', route: '/insights' },\r\n          { text: 'Careers India', route: '/careers/india' },\r\n          { text: 'Careers North America', route: '/careers/north-america' },\r\n          { text: 'Careers Europe', route: '/careers/europe' },\r\n          { text: 'Careers Asia Pacific', route: '/careers/asia-pacific' },\r\n          { text: 'Global Delivery Hubs', route: '/delivery-hubs' },\r\n          { text: 'Contact Us', route: '/contact' },\r\n        ],\r\n      },\r\n    ],\r\n    legalLinks: [\r\n      { text: 'Terms of use', route: '/terms' },\r\n      { text: 'Privacy Policy', route: '/privacy' },\r\n      { text: 'Accessibility Disclosure', route: '/accessibility' },\r\n      { text: 'AO-enhances content notice', route: '/content-notice' },\r\n    ],\r\n  };\r\n}\r\n", "<footer class=\"footer\">\r\n  <div class=\"container-fluid\">\r\n    <div class=\"row mb-5\">\r\n      <div\r\n        class=\"col-12 col-sm-6 col-lg-3 mb-4 footer-section\"\r\n        *ngFor=\"let section of footerData.sections\"\r\n      >\r\n        <awe-heading variant=\"s1\" type=\"bold\">{{ section?.title }}</awe-heading>\r\n        <ul class=\"mt-3\">\r\n          <li *ngFor=\"let item of section.items\" class=\"mb-3\">\r\n            <awe-heading variant=\"s2\" type=\"regular\">{{\r\n              item?.text || item\r\n            }}</awe-heading>\r\n          </li>\r\n        </ul>\r\n      </div>\r\n      <div class=\"col-12 col-sm-6 col-lg-3 mb-4 footer-section logo-section\">\r\n        <img src=\"image 338.svg\" alt=\"Acendion Logo\" />\r\n      </div>\r\n    </div>\r\n    <div class=\"row footer-bottom\">\r\n      <div class=\"col-12\">\r\n        <div class=\"legal-links\">\r\n          <ng-container\r\n            *ngFor=\"let link of footerData.legalLinks; let last = last\"\r\n          >\r\n            <awe-heading variant=\"s2\" type=\"regular\">{{\r\n              link?.text\r\n            }}</awe-heading>\r\n            <span *ngIf=\"!last\" class=\"separator\">|</span>\r\n          </ng-container>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</footer>\r\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,gBAAgB,QAAQ,wBAAwB;;;;;ICQ7CC,EADF,CAAAC,cAAA,aAAoD,sBACT;IAAAD,EAAA,CAAAE,MAAA,GAEvC;IACJF,EADI,CAAAG,YAAA,EAAc,EACb;;;;IAHsCH,EAAA,CAAAI,SAAA,GAEvC;IAFuCJ,EAAA,CAAAK,iBAAA,EAAAC,OAAA,kBAAAA,OAAA,CAAAC,IAAA,KAAAD,OAAA,CAEvC;;;;;IALNN,EAJF,CAAAC,cAAA,cAGC,sBACuC;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAc;IACxEH,EAAA,CAAAC,cAAA,aAAiB;IACfD,EAAA,CAAAQ,UAAA,IAAAC,8CAAA,iBAAoD;IAMxDT,EADE,CAAAG,YAAA,EAAK,EACD;;;;IARkCH,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAK,iBAAA,CAAAK,UAAA,kBAAAA,UAAA,CAAAC,KAAA,CAAoB;IAEnCX,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAY,UAAA,YAAAF,UAAA,CAAAG,KAAA,CAAgB;;;;;IAoBnCb,EAAA,CAAAC,cAAA,eAAsC;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IANhDH,EAAA,CAAAc,uBAAA,GAEC;IACCd,EAAA,CAAAC,cAAA,sBAAyC;IAAAD,EAAA,CAAAE,MAAA,GAEvC;IAAAF,EAAA,CAAAG,YAAA,EAAc;IAChBH,EAAA,CAAAQ,UAAA,IAAAO,yDAAA,mBAAsC;;;;;;IAHGf,EAAA,CAAAI,SAAA,GAEvC;IAFuCJ,EAAA,CAAAK,iBAAA,CAAAW,OAAA,kBAAAA,OAAA,CAAAT,IAAA,CAEvC;IACKP,EAAA,CAAAI,SAAA,EAAW;IAAXJ,EAAA,CAAAY,UAAA,UAAAK,OAAA,CAAW;;;ADlB9B,WAAaC,0BAA0B;EAAjC,MAAOA,0BAA0B;IACrCC,WAAW,GAAG,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;IACtCC,UAAU,GAAG;MACXC,QAAQ,EAAE,CACR;QACEZ,KAAK,EAAE,YAAY;QACnBE,KAAK,EAAE,CACL;UAAEN,IAAI,EAAE;QAAY,CAAE,EACtB;UAAEA,IAAI,EAAE;QAAM,CAAE,EAChB;UAAEA,IAAI,EAAE;QAAY,CAAE,EACtB;UAAEA,IAAI,EAAE;QAAsB,CAAE,EAChC;UAAEA,IAAI,EAAE;QAAqB,CAAE,EAC/B;UAAEA,IAAI,EAAE;QAAqB,CAAE,EAC/B;UAAEA,IAAI,EAAE;QAA0B,CAAE;OAEvC,EACD;QACEI,KAAK,EAAE,YAAY;QACnBE,KAAK,EAAE,CACL;UAAEN,IAAI,EAAE;QAA+B,CAAE,EACzC;UAAEA,IAAI,EAAE;QAAuC,CAAE,EACjD;UAAEA,IAAI,EAAE;QAAY,CAAE,EACtB;UAAEA,IAAI,EAAE;QAAW,CAAE,EACrB;UAAEA,IAAI,EAAE;QAA2B,CAAE,EACrC;UAAEA,IAAI,EAAE;QAAwB,CAAE;OAErC,EACD;QACEI,KAAK,EAAE,aAAa;QACpBE,KAAK,EAAE,CACL;UAAEN,IAAI,EAAE,MAAM;UAAEiB,KAAK,EAAE;QAAO,CAAE,EAChC;UAAEjB,IAAI,EAAE,YAAY;UAAEiB,KAAK,EAAE;QAAQ,CAAE,EACvC;UAAEjB,IAAI,EAAE,oBAAoB;UAAEiB,KAAK,EAAE;QAAW,CAAE,EAClD;UAAEjB,IAAI,EAAE,eAAe;UAAEiB,KAAK,EAAE;QAAgB,CAAE,EAClD;UAAEjB,IAAI,EAAE,uBAAuB;UAAEiB,KAAK,EAAE;QAAwB,CAAE,EAClE;UAAEjB,IAAI,EAAE,gBAAgB;UAAEiB,KAAK,EAAE;QAAiB,CAAE,EACpD;UAAEjB,IAAI,EAAE,sBAAsB;UAAEiB,KAAK,EAAE;QAAuB,CAAE,EAChE;UAAEjB,IAAI,EAAE,sBAAsB;UAAEiB,KAAK,EAAE;QAAgB,CAAE,EACzD;UAAEjB,IAAI,EAAE,YAAY;UAAEiB,KAAK,EAAE;QAAU,CAAE;OAE5C,CACF;MACDC,UAAU,EAAE,CACV;QAAElB,IAAI,EAAE,cAAc;QAAEiB,KAAK,EAAE;MAAQ,CAAE,EACzC;QAAEjB,IAAI,EAAE,gBAAgB;QAAEiB,KAAK,EAAE;MAAU,CAAE,EAC7C;QAAEjB,IAAI,EAAE,0BAA0B;QAAEiB,KAAK,EAAE;MAAgB,CAAE,EAC7D;QAAEjB,IAAI,EAAE,4BAA4B;QAAEiB,KAAK,EAAE;MAAiB,CAAE;KAEnE;;uCAhDUN,0BAA0B;IAAA;;YAA1BA,0BAA0B;MAAAQ,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCTnChC,EAFJ,CAAAC,cAAA,gBAAuB,aACQ,aACL;UACpBD,EAAA,CAAAQ,UAAA,IAAA0B,yCAAA,iBAGC;UAUDlC,EAAA,CAAAC,cAAA,aAAuE;UACrED,EAAA,CAAAmC,SAAA,aAA+C;UAEnDnC,EADE,CAAAG,YAAA,EAAM,EACF;UAGFH,EAFJ,CAAAC,cAAA,aAA+B,aACT,aACO;UACvBD,EAAA,CAAAQ,UAAA,IAAA4B,kDAAA,0BAEC;UAUXpC,EAJQ,CAAAG,YAAA,EAAM,EACF,EACF,EACF,EACC;;;UA9BmBH,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAY,UAAA,YAAAqB,GAAA,CAAAX,UAAA,CAAAC,QAAA,CAAsB;UAmBrBvB,EAAA,CAAAI,SAAA,GAA0B;UAA1BJ,EAAA,CAAAY,UAAA,YAAAqB,GAAA,CAAAX,UAAA,CAAAG,UAAA,CAA0B;;;qBDjB3C3B,YAAY,EAAAuC,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAExC,gBAAgB;MAAAyC,MAAA;IAAA;;SAI7BtB,0BAA0B;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}