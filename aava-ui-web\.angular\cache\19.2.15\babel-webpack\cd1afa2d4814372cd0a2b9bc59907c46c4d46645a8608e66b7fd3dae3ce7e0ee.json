{"ast": null, "code": "export default function (selector) {\n  return function () {\n    return this.matches(selector);\n  };\n}\nexport function childMatcher(selector) {\n  return function (node) {\n    return node.matches(selector);\n  };\n}", "map": {"version": 3, "names": ["selector", "matches", "child<PERSON><PERSON><PERSON>", "node"], "sources": ["C:/console/aava-ui-web/node_modules/d3-selection/src/matcher.js"], "sourcesContent": ["export default function(selector) {\n  return function() {\n    return this.matches(selector);\n  };\n}\n\nexport function childMatcher(selector) {\n  return function(node) {\n    return node.matches(selector);\n  };\n}\n\n"], "mappings": "AAAA,eAAe,UAASA,QAAQ,EAAE;EAChC,OAAO,YAAW;IAChB,OAAO,IAAI,CAACC,OAAO,CAACD,QAAQ,CAAC;EAC/B,CAAC;AACH;AAEA,OAAO,SAASE,YAAYA,CAACF,QAAQ,EAAE;EACrC,OAAO,UAASG,IAAI,EAAE;IACpB,OAAOA,IAAI,CAACF,OAAO,CAACD,QAAQ,CAAC;EAC/B,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}