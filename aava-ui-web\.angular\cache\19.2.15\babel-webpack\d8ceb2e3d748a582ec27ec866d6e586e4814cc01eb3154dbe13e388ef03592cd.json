{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, Input, Output, EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ButtonComponent } from '@ava/play-comp-library';\nlet QuickActionsComponent = class QuickActionsComponent {\n  actions = [{\n    id: 'build-agent',\n    label: 'Build Agent',\n    icon: '/svgs/icons/awe_add_modetor.svg'\n  }, {\n    id: 'build-workflow',\n    label: 'Build Workflow',\n    icon: '/svgs/icons/awe_add_chart.svg'\n  }, {\n    id: 'create-prompt',\n    label: 'Create Prompt',\n    icon: '/svgs/icons/awe_add_bookmark_filled.svg'\n  }, {\n    id: 'create-tool',\n    label: 'Create Tool',\n    icon: '/svgs/icons/awe_build.svg'\n  }, {\n    id: 'create-guardrail',\n    label: 'Create Guardrail',\n    icon: '/svgs/icons/awe_guardrail.svg'\n  }, {\n    id: 'create-knowledge-base',\n    label: 'Create Knowledge Base',\n    icon: '/svgs/icons/awe_knowledge_base.svg'\n  }];\n  actionClick = new EventEmitter();\n  isExpanded = false;\n  toggleExpanded() {\n    this.isExpanded = !this.isExpanded;\n  }\n  onActionClick(action) {\n    if (action.action) {\n      action.action();\n    }\n    this.actionClick.emit(action);\n  }\n  onButtonClick(event) {}\n};\n__decorate([Input()], QuickActionsComponent.prototype, \"actions\", void 0);\n__decorate([Output()], QuickActionsComponent.prototype, \"actionClick\", void 0);\nQuickActionsComponent = __decorate([Component({\n  selector: 'app-quick-actions',\n  standalone: true,\n  imports: [CommonModule, ButtonComponent],\n  templateUrl: './quick-actions.component.html',\n  styleUrls: ['./quick-actions.component.scss']\n})], QuickActionsComponent);\nexport { QuickActionsComponent };", "map": {"version": 3, "names": ["Component", "Input", "Output", "EventEmitter", "CommonModule", "ButtonComponent", "QuickActionsComponent", "actions", "id", "label", "icon", "actionClick", "isExpanded", "toggleExpanded", "onActionClick", "action", "emit", "onButtonClick", "event", "__decorate", "selector", "standalone", "imports", "templateUrl", "styleUrls"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\shared\\components\\quick-actions\\quick-actions.component.ts"], "sourcesContent": ["import { Component, Input, Output, EventEmitter } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ButtonComponent } from '@ava/play-comp-library';\r\n\r\nexport interface QuickAction {\r\n  id: string;\r\n  label: string;\r\n  icon: string;\r\n  action?: () => void;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-quick-actions',\r\n  standalone: true,\r\n  imports: [CommonModule, ButtonComponent],\r\n  templateUrl: './quick-actions.component.html',\r\n  styleUrls: ['./quick-actions.component.scss'],\r\n})\r\nexport class QuickActionsComponent {\r\n  @Input() actions: QuickAction[] = [\r\n    {\r\n      id: 'build-agent',\r\n      label: 'Build Agent',\r\n      icon: '/svgs/icons/awe_add_modetor.svg',\r\n    },\r\n    {\r\n      id: 'build-workflow',\r\n      label: 'Build Workflow',\r\n      icon: '/svgs/icons/awe_add_chart.svg',\r\n    },\r\n    {\r\n      id: 'create-prompt',\r\n      label: 'Create Prompt',\r\n      icon: '/svgs/icons/awe_add_bookmark_filled.svg',\r\n    },\r\n    {\r\n      id: 'create-tool',\r\n      label: 'Create Tool',\r\n      icon: '/svgs/icons/awe_build.svg',\r\n    },\r\n    {\r\n      id: 'create-guardrail',\r\n      label: 'Create Guardrail',\r\n      icon: '/svgs/icons/awe_guardrail.svg',\r\n    },\r\n    {\r\n      id: 'create-knowledge-base',\r\n      label: 'Create Knowledge Base',\r\n      icon: '/svgs/icons/awe_knowledge_base.svg',\r\n    },\r\n  ];\r\n\r\n  @Output() actionClick = new EventEmitter<QuickAction>();\r\n\r\n  isExpanded = false;\r\n\r\n  toggleExpanded(): void {\r\n    this.isExpanded = !this.isExpanded;\r\n  }\r\n\r\n  onActionClick(action: QuickAction): void {\r\n    if (action.action) {\r\n      action.action();\r\n    }\r\n    this.actionClick.emit(action);\r\n  }\r\n\r\n  onButtonClick(event: any) {}\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,YAAY,QAAQ,eAAe;AACtE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,wBAAwB;AAgBjD,IAAMC,qBAAqB,GAA3B,MAAMA,qBAAqB;EACvBC,OAAO,GAAkB,CAChC;IACEC,EAAE,EAAE,aAAa;IACjBC,KAAK,EAAE,aAAa;IACpBC,IAAI,EAAE;GACP,EACD;IACEF,EAAE,EAAE,gBAAgB;IACpBC,KAAK,EAAE,gBAAgB;IACvBC,IAAI,EAAE;GACP,EACD;IACEF,EAAE,EAAE,eAAe;IACnBC,KAAK,EAAE,eAAe;IACtBC,IAAI,EAAE;GACP,EACD;IACEF,EAAE,EAAE,aAAa;IACjBC,KAAK,EAAE,aAAa;IACpBC,IAAI,EAAE;GACP,EACD;IACEF,EAAE,EAAE,kBAAkB;IACtBC,KAAK,EAAE,kBAAkB;IACzBC,IAAI,EAAE;GACP,EACD;IACEF,EAAE,EAAE,uBAAuB;IAC3BC,KAAK,EAAE,uBAAuB;IAC9BC,IAAI,EAAE;GACP,CACF;EAESC,WAAW,GAAG,IAAIR,YAAY,EAAe;EAEvDS,UAAU,GAAG,KAAK;EAElBC,cAAcA,CAAA;IACZ,IAAI,CAACD,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;EACpC;EAEAE,aAAaA,CAACC,MAAmB;IAC/B,IAAIA,MAAM,CAACA,MAAM,EAAE;MACjBA,MAAM,CAACA,MAAM,EAAE;IACjB;IACA,IAAI,CAACJ,WAAW,CAACK,IAAI,CAACD,MAAM,CAAC;EAC/B;EAEAE,aAAaA,CAACC,KAAU,GAAG;CAC5B;AAjDUC,UAAA,EAARlB,KAAK,EAAE,C,qDA+BN;AAEQkB,UAAA,EAATjB,MAAM,EAAE,C,yDAA+C;AAlC7CI,qBAAqB,GAAAa,UAAA,EAPjCnB,SAAS,CAAC;EACToB,QAAQ,EAAE,mBAAmB;EAC7BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAClB,YAAY,EAAEC,eAAe,CAAC;EACxCkB,WAAW,EAAE,gCAAgC;EAC7CC,SAAS,EAAE,CAAC,gCAAgC;CAC7C,CAAC,C,EACWlB,qBAAqB,CAkDjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}