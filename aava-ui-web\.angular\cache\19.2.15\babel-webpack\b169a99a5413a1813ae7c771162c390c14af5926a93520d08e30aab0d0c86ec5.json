{"ast": null, "code": "import { AgentsComponent } from '../../shared/components/agents/agents.component';\nimport { StudiosComponent } from '../../shared/components/studios/studios.component';\nimport { agentsData } from '../../../assets/data/agents.mock';\nimport { salesAgent } from '../../../assets/data/flows.mock';\nimport { projectTeam } from '../../../assets/data/project-team.mock';\nimport { SidebarComponent } from \"../../shared/components/sidebar/sidebar.component\";\nimport Hero from \"../../shared/components/hero/hero.component\";\nimport { AnalyticsCardComponent } from \"../../shared/components/analytics-card/analytics-card.component\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../shared/service/global-store.service\";\nexport let LaunchpadHomeComponent = /*#__PURE__*/(() => {\n  class LaunchpadHomeComponent {\n    globalStoreService;\n    showCount = 8;\n    agentsData = [];\n    selectedUser;\n    constructor(globalStoreService) {\n      this.globalStoreService = globalStoreService;\n    }\n    ngOnInit() {\n      this.globalStoreService.selectedUser.subscribe(user => {\n        this.selectedUser = user;\n        this.updateAgentsList();\n      });\n    }\n    updateAgentsList() {\n      if (this.selectedUser?.type === 'Sales') {\n        this.agentsData = salesAgent.slice(0, this.showCount);\n      } else if (this.selectedUser?.type === 'Project Team') {\n        this.agentsData = projectTeam.slice(0, this.showCount);\n      } else {\n        this.agentsData = agentsData.slice(0, this.showCount);\n      }\n    }\n    static ɵfac = function LaunchpadHomeComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LaunchpadHomeComponent)(i0.ɵɵdirectiveInject(i1.GlobalStoreService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LaunchpadHomeComponent,\n      selectors: [[\"app-launchpad-home\"]],\n      decls: 19,\n      vars: 5,\n      consts: [[1, \"launchpad-container\"], [1, \"sidebar-section\"], [1, \"main-content-section\"], [3, \"searchResultsChange\", \"searchQueryChange\", \"searchLoadingChange\", \"sendClicked\"], [1, \"content-section\"], [1, \"section-text\"], [1, \"two-column-layout\"], [1, \"left-column\"], [1, \"right-column\"], [3, \"agents\", \"showTwoColumns\", \"searchResults\", \"searchQuery\", \"isSearchLoading\"], [1, \"analytics-section\"], [1, \"analytics-header\"]],\n      template: function LaunchpadHomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"app-sidebar\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 2)(4, \"app-hero\", 3);\n          i0.ɵɵlistener(\"searchResultsChange\", function LaunchpadHomeComponent_Template_app_hero_searchResultsChange_4_listener($event) {\n            return ctx.onSearchResultsChange($event);\n          })(\"searchQueryChange\", function LaunchpadHomeComponent_Template_app_hero_searchQueryChange_4_listener($event) {\n            return ctx.onSearchQueryChange($event);\n          })(\"searchLoadingChange\", function LaunchpadHomeComponent_Template_app_hero_searchLoadingChange_4_listener($event) {\n            return ctx.onSearchLoadingChange($event);\n          })(\"sendClicked\", function LaunchpadHomeComponent_Template_app_hero_sendClicked_4_listener($event) {\n            return ctx.onSendClicked($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 4)(6, \"div\", 5)(7, \"p\");\n          i0.ɵɵtext(8, \" Here's a personalized overview of your day, organized to meet your current goals and responsibilities. \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 6)(10, \"div\", 7);\n          i0.ɵɵelement(11, \"app-studios\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"div\", 8);\n          i0.ɵɵelement(13, \"app-agents\", 9);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(14, \"div\", 10)(15, \"div\", 11)(16, \"h2\");\n          i0.ɵɵtext(17, \"Analytics\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(18, \"app-analytics-card\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"agents\", ctx.agentsData)(\"showTwoColumns\", true)(\"searchResults\", ctx.searchResults)(\"searchQuery\", ctx.searchQuery)(\"isSearchLoading\", ctx.isSearchLoading);\n        }\n      },\n      dependencies: [AgentsComponent, StudiosComponent, SidebarComponent, Hero, AnalyticsCardComponent],\n      styles: [\".launchpad-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  margin: 0 20px;\\n  position: relative;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.sidebar-section[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 947px;\\n  flex-shrink: 0;\\n}\\n\\n.main-content-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  max-width: 1431px;\\n  height: 947px;\\n  display: flex;\\n  padding: 72px 10px 12px 10px;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  gap: 48px;\\n  flex-shrink: 0;\\n  border-radius: 24px;\\n  border: 2px solid #FFF;\\n  background: linear-gradient(0deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.2) 100%), linear-gradient(114deg, rgba(240, 235, 248, 0.8) 1.5%, rgba(255, 255, 255, 0.8) 50.17%, rgba(245, 233, 247, 0.8) 98.86%);\\n  box-shadow: 0px 2px 2px -3px #F0F1F2, 0px 0px 6px -2px #D1D3D8;\\n  -webkit-backdrop-filter: blur(8px);\\n          backdrop-filter: blur(8px);\\n}\\n\\n.analytics-section[_ngcontent-%COMP%] {\\n  width: 288px;\\n  height: 947px;\\n  flex-shrink: 0;\\n  display: flex;\\n  padding: 20px 10px 12px 10px;\\n  flex-direction: column;\\n  justify-content: flex-start;\\n  align-items: center;\\n  gap: 24px;\\n  border-radius: 24px;\\n  border: 2px solid #FFF;\\n  background: linear-gradient(0deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.2) 100%), linear-gradient(114deg, rgba(240, 235, 248, 0.8) 1.5%, rgba(255, 255, 255, 0.8) 50.17%, rgba(245, 233, 247, 0.8) 98.86%);\\n  box-shadow: 0px 2px 2px -3px #F0F1F2, 0px 0px 6px -2px #D1D3D8;\\n  -webkit-backdrop-filter: blur(8px);\\n          backdrop-filter: blur(8px);\\n}\\n.analytics-section[_ngcontent-%COMP%]   .analytics-header[_ngcontent-%COMP%] {\\n  width: 100%;\\n  text-align: left;\\n  margin-bottom: 16px;\\n}\\n.analytics-section[_ngcontent-%COMP%]   .analytics-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  color: #000;\\n  font-family: Mulish;\\n  font-size: 20px;\\n  font-weight: 600;\\n  margin: 0;\\n  margin-bottom: 10px;\\n}\\n\\n.content-section[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 24px;\\n}\\n.content-section[_ngcontent-%COMP%]   .section-text[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.content-section[_ngcontent-%COMP%]   .section-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #616874;\\n  font-family: Mulish;\\n  font-size: 20px;\\n  font-weight: 600;\\n  line-height: 24px;\\n  margin: 0;\\n  margin-top: 18px;\\n  width: auto;\\n}\\n.content-section[_ngcontent-%COMP%]   .two-column-layout[_ngcontent-%COMP%] {\\n  display: flex;\\n  width: 100%;\\n  max-width: 1193px;\\n  height: 370px !important;\\n  gap: 8px;\\n}\\n.content-section[_ngcontent-%COMP%]   .two-column-layout[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n  height: 100%;\\n  overflow: hidden;\\n}\\n.content-section[_ngcontent-%COMP%]   .two-column-layout[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%] {\\n  flex: 2;\\n  min-width: 0;\\n  height: 100%;\\n  overflow: hidden;\\n}\\n\\n@media (max-width: 1400px) {\\n  .launchpad-container[_ngcontent-%COMP%] {\\n    gap: 24px;\\n    margin: 0 10px;\\n  }\\n  .main-content-section[_ngcontent-%COMP%] {\\n    max-width: 1000px;\\n  }\\n  .analytics-section[_ngcontent-%COMP%] {\\n    width: 250px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .launchpad-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 16px;\\n    align-items: stretch;\\n  }\\n  .sidebar-section[_ngcontent-%COMP%] {\\n    width: 100%;\\n    height: auto;\\n  }\\n  .main-content-section[_ngcontent-%COMP%], \\n   .analytics-section[_ngcontent-%COMP%] {\\n    width: 100%;\\n    height: auto;\\n    min-height: 400px;\\n    padding: 20px 10px;\\n  }\\n  .content-section[_ngcontent-%COMP%]   .two-column-layout[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 24px;\\n    height: auto;\\n  }\\n  .content-section[_ngcontent-%COMP%]   .section-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .launchpad-container[_ngcontent-%COMP%] {\\n    margin: 0 5px;\\n    gap: 12px;\\n  }\\n  .main-content-section[_ngcontent-%COMP%], \\n   .analytics-section[_ngcontent-%COMP%] {\\n    padding: 15px 8px;\\n  }\\n  .content-section[_ngcontent-%COMP%]   .section-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return LaunchpadHomeComponent;\n})();", "map": {"version": 3, "names": ["AgentsComponent", "StudiosComponent", "agentsData", "salesAgent", "projectTeam", "SidebarComponent", "Hero", "AnalyticsCardComponent", "LaunchpadHomeComponent", "globalStoreService", "showCount", "selected<PERSON>ser", "constructor", "ngOnInit", "subscribe", "user", "updateAgentsList", "type", "slice", "i0", "ɵɵdirectiveInject", "i1", "GlobalStoreService", "selectors", "decls", "vars", "consts", "template", "LaunchpadHomeComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵlistener", "LaunchpadHomeComponent_Template_app_hero_searchResultsChange_4_listener", "$event", "onSearchResultsChange", "LaunchpadHomeComponent_Template_app_hero_searchQueryChange_4_listener", "onSearchQueryChange", "LaunchpadHomeComponent_Template_app_hero_searchLoadingChange_4_listener", "onSearchLoadingChange", "LaunchpadHomeComponent_Template_app_hero_sendClicked_4_listener", "onSendClicked", "ɵɵtext", "ɵɵadvance", "ɵɵproperty", "searchResults", "searchQuery", "isSearchLoading", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\pages\\launchpad-home\\launchpad-home.component.ts", "C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\pages\\launchpad-home\\launchpad-home.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { AgentsComponent } from '../../shared/components/agents/agents.component';\r\n\r\nimport { NewsBlogsComponent } from '../../shared/components/news-blogs/news-blogs.component';\r\nimport { StudiosComponent } from '../../shared/components/studios/studios.component';\r\nimport { TrustedClientsComponent } from '../../shared/components/trusted-clients/trusted-clients.component';\r\nimport { FooterComponent } from '../../shared/components/footer/footer.component';\r\nimport { agentsData } from '../../../assets/data/agents.mock';\r\nimport { salesAgent } from '../../../assets/data/flows.mock';\r\nimport { projectTeam } from '../../../assets/data/project-team.mock';\r\nimport { Agent } from '../../shared/interfaces/agent-list.interface';\r\nimport { GlobalStoreService } from '../../shared/service/global-store.service';\r\nimport { SidebarComponent } from \"../../shared/components/sidebar/sidebar.component\";\r\nimport Hero from \"../../shared/components/hero/hero.component\";\r\nimport { AnalyticsCardComponent } from \"../../shared/components/analytics-card/analytics-card.component\";\r\n\r\n@Component({\r\n  selector: 'app-launchpad-home',\r\n  standalone: true,\r\n  imports: [\r\n    AgentsComponent,\r\n    StudiosComponent,\r\n    SidebarComponent,\r\n    Hero,\r\n    AnalyticsCardComponent\r\n],\r\n  templateUrl: './launchpad-home.component.html',\r\n  styleUrl: './launchpad-home.component.scss',\r\n})\r\nexport class LaunchpadHomeComponent implements OnInit {\r\n  showCount = 8;\r\n  agentsData: Agent[] = [];\r\n  selectedUser: any;\r\n\r\n  constructor(private readonly globalStoreService: GlobalStoreService) {}\r\n\r\n  ngOnInit() {\r\n    this.globalStoreService.selectedUser.subscribe((user) => {\r\n      this.selectedUser = user;\r\n      this.updateAgentsList();\r\n    });\r\n  }\r\n\r\n  private updateAgentsList() {\r\n    if (this.selectedUser?.type === 'Sales') {\r\n      this.agentsData = salesAgent.slice(0, this.showCount);\r\n    } else if (this.selectedUser?.type === 'Project Team') {\r\n      this.agentsData = projectTeam.slice(0, this.showCount);\r\n    } else {\r\n      this.agentsData = agentsData.slice(0, this.showCount);\r\n    }\r\n  }\r\n}\r\n", "<div class=\"launchpad-container\">\r\n  <!-- Sidebar -->\r\n  <div class=\"sidebar-section\">\r\n    <app-sidebar></app-sidebar>\r\n  </div>\r\n\r\n  <!-- Main Content -->\r\n  <div class=\"main-content-section\">\r\n    <app-hero\r\n      (searchResultsChange)=\"onSearchResultsChange($event)\"\r\n      (searchQueryChange)=\"onSearchQueryChange($event)\"\r\n      (searchLoadingChange)=\"onSearchLoadingChange($event)\"\r\n      (sendClicked)=\"onSendClicked($event)\"\r\n    >\r\n    </app-hero>\r\n\r\n    <div class=\"content-section\">\r\n      <div class=\"section-text\">\r\n        <p>\r\n          Here's a personalized overview of your day, organized to meet your\r\n          current goals and responsibilities.\r\n        </p>\r\n      </div>\r\n\r\n      <div class=\"two-column-layout\">\r\n        <!-- Left column: Studios -->\r\n        <div class=\"left-column\">\r\n          <app-studios></app-studios>\r\n        </div>\r\n\r\n        <div class=\"right-column\">\r\n          <app-agents\r\n            [agents]=\"agentsData\"\r\n            [showTwoColumns]=\"true\"\r\n            [searchResults]=\"searchResults\"\r\n            [searchQuery]=\"searchQuery\"\r\n            [isSearchLoading]=\"isSearchLoading\"\r\n          >\r\n          </app-agents>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Analytics Section -->\r\n  <div class=\"analytics-section\">\r\n    <div class=\"analytics-header\">\r\n      <h2>Analytics</h2>\r\n      <app-analytics-card></app-analytics-card>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAASA,eAAe,QAAQ,iDAAiD;AAGjF,SAASC,gBAAgB,QAAQ,mDAAmD;AAGpF,SAASC,UAAU,QAAQ,kCAAkC;AAC7D,SAASC,UAAU,QAAQ,iCAAiC;AAC5D,SAASC,WAAW,QAAQ,wCAAwC;AAGpE,SAASC,gBAAgB,QAAQ,mDAAmD;AACpF,OAAOC,IAAI,MAAM,6CAA6C;AAC9D,SAASC,sBAAsB,QAAQ,iEAAiE;;;AAexG,WAAaC,sBAAsB;EAA7B,MAAOA,sBAAsB;IAKJC,kBAAA;IAJ7BC,SAAS,GAAG,CAAC;IACbR,UAAU,GAAY,EAAE;IACxBS,YAAY;IAEZC,YAA6BH,kBAAsC;MAAtC,KAAAA,kBAAkB,GAAlBA,kBAAkB;IAAuB;IAEtEI,QAAQA,CAAA;MACN,IAAI,CAACJ,kBAAkB,CAACE,YAAY,CAACG,SAAS,CAAEC,IAAI,IAAI;QACtD,IAAI,CAACJ,YAAY,GAAGI,IAAI;QACxB,IAAI,CAACC,gBAAgB,EAAE;MACzB,CAAC,CAAC;IACJ;IAEQA,gBAAgBA,CAAA;MACtB,IAAI,IAAI,CAACL,YAAY,EAAEM,IAAI,KAAK,OAAO,EAAE;QACvC,IAAI,CAACf,UAAU,GAAGC,UAAU,CAACe,KAAK,CAAC,CAAC,EAAE,IAAI,CAACR,SAAS,CAAC;MACvD,CAAC,MAAM,IAAI,IAAI,CAACC,YAAY,EAAEM,IAAI,KAAK,cAAc,EAAE;QACrD,IAAI,CAACf,UAAU,GAAGE,WAAW,CAACc,KAAK,CAAC,CAAC,EAAE,IAAI,CAACR,SAAS,CAAC;MACxD,CAAC,MAAM;QACL,IAAI,CAACR,UAAU,GAAGA,UAAU,CAACgB,KAAK,CAAC,CAAC,EAAE,IAAI,CAACR,SAAS,CAAC;MACvD;IACF;;uCAtBWF,sBAAsB,EAAAW,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,kBAAA;IAAA;;YAAtBd,sBAAsB;MAAAe,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC3BjCV,EAFF,CAAAY,cAAA,aAAiC,aAEF;UAC3BZ,EAAA,CAAAa,SAAA,kBAA2B;UAC7Bb,EAAA,CAAAc,YAAA,EAAM;UAIJd,EADF,CAAAY,cAAA,aAAkC,kBAM/B;UADCZ,EAHA,CAAAe,UAAA,iCAAAC,wEAAAC,MAAA;YAAA,OAAuBN,GAAA,CAAAO,qBAAA,CAAAD,MAAA,CAA6B;UAAA,EAAC,+BAAAE,sEAAAF,MAAA;YAAA,OAChCN,GAAA,CAAAS,mBAAA,CAAAH,MAAA,CAA2B;UAAA,EAAC,iCAAAI,wEAAAJ,MAAA;YAAA,OAC1BN,GAAA,CAAAW,qBAAA,CAAAL,MAAA,CAA6B;UAAA,EAAC,yBAAAM,gEAAAN,MAAA;YAAA,OACtCN,GAAA,CAAAa,aAAA,CAAAP,MAAA,CAAqB;UAAA,EAAC;UAEvCjB,EAAA,CAAAc,YAAA,EAAW;UAIPd,EAFJ,CAAAY,cAAA,aAA6B,aACD,QACrB;UACDZ,EAAA,CAAAyB,MAAA,+GAEF;UACFzB,EADE,CAAAc,YAAA,EAAI,EACA;UAIJd,EAFF,CAAAY,cAAA,aAA+B,cAEJ;UACvBZ,EAAA,CAAAa,SAAA,mBAA2B;UAC7Bb,EAAA,CAAAc,YAAA,EAAM;UAENd,EAAA,CAAAY,cAAA,cAA0B;UACxBZ,EAAA,CAAAa,SAAA,qBAOa;UAIrBb,EAHM,CAAAc,YAAA,EAAM,EACF,EACF,EACF;UAKFd,EAFJ,CAAAY,cAAA,eAA+B,eACC,UACxB;UAAAZ,EAAA,CAAAyB,MAAA,iBAAS;UAAAzB,EAAA,CAAAc,YAAA,EAAK;UAClBd,EAAA,CAAAa,SAAA,0BAAyC;UAG/Cb,EAFI,CAAAc,YAAA,EAAM,EACF,EACF;;;UAnBMd,EAAA,CAAA0B,SAAA,IAAqB;UAIrB1B,EAJA,CAAA2B,UAAA,WAAAhB,GAAA,CAAA5B,UAAA,CAAqB,wBACE,kBAAA4B,GAAA,CAAAiB,aAAA,CACQ,gBAAAjB,GAAA,CAAAkB,WAAA,CACJ,oBAAAlB,GAAA,CAAAmB,eAAA,CACQ;;;qBDhB3CjD,eAAe,EACfC,gBAAgB,EAChBI,gBAAgB,EAChBC,IAAI,EACJC,sBAAsB;MAAA2C,MAAA;IAAA;;SAKb1C,sBAAsB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}