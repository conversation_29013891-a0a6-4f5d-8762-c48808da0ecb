{"ast": null, "code": "import { CommonModule, DatePipe } from '@angular/common';\nimport { AvaTextboxComponent, ButtonComponent, IconComponent, SliderComponent } from '@ava/play-comp-library';\nimport { PreviewPanelComponent } from '../../../../components/preview-panel/preview-panel.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction AgentsPreviewPanelComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵelement(1, \"div\", 13);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading details...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"label\");\n    i0.ɵɵtext(2, \"Model Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 31);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate((ctx_r1.previewData == null ? null : ctx_r1.previewData.title) || ctx_r1.previewData.data.name);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"label\");\n    i0.ɵɵtext(2, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 32);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.modelDescription || ctx_r1.previewData.data.description);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"label\");\n    i0.ɵɵtext(2, \"Added by\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 31);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.createdBy);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"label\");\n    i0.ɵɵtext(2, \"Added on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 31);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 1, ctx_r1.previewData.data.createdOn || ctx_r1.previewData.data.createdDate, \"MM/dd/yyyy\"));\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"label\");\n    i0.ɵɵtext(2, \"Select Model\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 35)(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.modelType);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"label\");\n    i0.ɵɵtext(2, \"Temperature\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"ava-slider\", 36);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"min\", 0)(\"max\", 1)(\"step\", 0.01)(\"value\", ctx_r1.previewData.data.temperature);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_15_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"label\");\n    i0.ɵɵtext(2, \"Max Token\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"ava-textbox\", 40);\n    i0.ɵɵelementStart(4, \"div\", 41);\n    i0.ɵɵtext(5, \"4096 Tokens used\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"value\", ctx_r1.previewData.data.maxTokens);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_15_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"label\");\n    i0.ɵɵtext(2, \"Top P\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"ava-textbox\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"value\", ctx_r1.previewData.data.topP);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵtemplate(1, AgentsPreviewPanelComponent_div_9_div_1_div_15_div_1_Template, 6, 1, \"div\", 38)(2, AgentsPreviewPanelComponent_div_9_div_1_div_15_div_2_Template, 4, 1, \"div\", 38);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.maxTokens);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.topP);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"label\");\n    i0.ɵɵtext(2, \"Max Iteration\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"ava-textbox\", 43);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"value\", ctx_r1.previewData.data.maxIteration);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"label\");\n    i0.ɵɵtext(2, \"AI Engine\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 35);\n    i0.ɵɵelement(4, \"span\", 44);\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.aiEngine);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"label\");\n    i0.ɵɵtext(2, \"Model Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 35)(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.modelType);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"label\");\n    i0.ɵɵtext(2, \"Base URL\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 45);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.baseurl || ctx_r1.previewData.data.baseUrl);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"label\");\n    i0.ɵɵtext(2, \"LLM Deployment Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 45);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.llmDeploymentName);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"label\");\n    i0.ɵɵtext(2, \"API Key Encoded\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 45);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.apiKey);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"label\");\n    i0.ɵɵtext(2, \"API Version\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 45);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.apiVersion);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 20)(2, \"h3\");\n    i0.ɵɵtext(3, \"Model Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, AgentsPreviewPanelComponent_div_9_div_1_div_4_Template, 5, 1, \"div\", 21)(5, AgentsPreviewPanelComponent_div_9_div_1_div_5_Template, 5, 1, \"div\", 21);\n    i0.ɵɵelementStart(6, \"div\", 22)(7, \"div\", 23);\n    i0.ɵɵtemplate(8, AgentsPreviewPanelComponent_div_9_div_1_div_8_Template, 5, 1, \"div\", 24)(9, AgentsPreviewPanelComponent_div_9_div_1_div_9_Template, 6, 4, \"div\", 24);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 20)(11, \"h3\");\n    i0.ɵɵtext(12, \"Model Configuration\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, AgentsPreviewPanelComponent_div_9_div_1_div_13_Template, 6, 1, \"div\", 25)(14, AgentsPreviewPanelComponent_div_9_div_1_div_14_Template, 4, 4, \"div\", 25)(15, AgentsPreviewPanelComponent_div_9_div_1_div_15_Template, 3, 2, \"div\", 26)(16, AgentsPreviewPanelComponent_div_9_div_1_div_16_Template, 4, 1, \"div\", 25);\n    i0.ɵɵelementStart(17, \"div\", 27)(18, \"span\", 28);\n    i0.ɵɵlistener(\"click\", function AgentsPreviewPanelComponent_div_9_div_1_Template_span_click_18_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleConfigDetails());\n    });\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 29);\n    i0.ɵɵtemplate(21, AgentsPreviewPanelComponent_div_9_div_1_div_21_Template, 7, 1, \"div\", 25)(22, AgentsPreviewPanelComponent_div_9_div_1_div_22_Template, 6, 1, \"div\", 25)(23, AgentsPreviewPanelComponent_div_9_div_1_div_23_Template, 5, 1, \"div\", 25)(24, AgentsPreviewPanelComponent_div_9_div_1_div_24_Template, 5, 1, \"div\", 25)(25, AgentsPreviewPanelComponent_div_9_div_1_div_25_Template, 5, 1, \"div\", 25)(26, AgentsPreviewPanelComponent_div_9_div_1_div_26_Template, 5, 1, \"div\", 25);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.previewData == null ? null : ctx_r1.previewData.title) || ctx_r1.previewData.data.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.modelDescription || ctx_r1.previewData.data.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.createdBy);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.createdOn || ctx_r1.previewData.data.createdDate);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.modelType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.temperature !== undefined && ctx_r1.previewData.data.temperature !== null);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.maxTokens || ctx_r1.previewData.data.topP);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.maxIteration);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" View More Configuration Details \", ctx_r1.showMoreConfig ? \"-\" : \"+\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"expanded\", ctx_r1.showMoreConfig);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.aiEngine);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.modelType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.baseurl || ctx_r1.previewData.data.baseUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.llmDeploymentName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.apiKey);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.apiVersion);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_2_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"label\");\n    i0.ɵɵtext(2, \"Tool Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 31);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate((ctx_r1.previewData == null ? null : ctx_r1.previewData.title) || ctx_r1.previewData.data.name);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_2_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"label\");\n    i0.ɵɵtext(2, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 32);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.description || ctx_r1.previewData.data.appDescription);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_2_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"label\");\n    i0.ɵɵtext(2, \"Area of Scope\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 32);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.areaOfScope);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_2_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"label\");\n    i0.ɵɵtext(2, \"Added by\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 31);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.createdBy);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_2_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"label\");\n    i0.ɵɵtext(2, \"Added on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 31);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 1, ctx_r1.previewData.data.createdOn || ctx_r1.previewData.data.createdDate, \"MM/dd/yyyy\"));\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_2_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 47);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.functionality || ctx_r1.previewData.data.content || ctx_r1.previewData.data.toolClassDef || \"No tool definition available\");\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 20)(2, \"h3\");\n    i0.ɵɵtext(3, \"Tool Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, AgentsPreviewPanelComponent_div_9_div_2_div_4_Template, 5, 1, \"div\", 21)(5, AgentsPreviewPanelComponent_div_9_div_2_div_5_Template, 5, 1, \"div\", 21)(6, AgentsPreviewPanelComponent_div_9_div_2_div_6_Template, 5, 1, \"div\", 21);\n    i0.ɵɵelementStart(7, \"div\", 22)(8, \"div\", 23);\n    i0.ɵɵtemplate(9, AgentsPreviewPanelComponent_div_9_div_2_div_9_Template, 5, 1, \"div\", 24)(10, AgentsPreviewPanelComponent_div_9_div_2_div_10_Template, 6, 4, \"div\", 24);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 20)(12, \"h3\");\n    i0.ɵɵtext(13, \"Tool Configuration\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, AgentsPreviewPanelComponent_div_9_div_2_div_14_Template, 3, 1, \"div\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.previewData == null ? null : ctx_r1.previewData.title) || ctx_r1.previewData.data.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.description || ctx_r1.previewData.data.appDescription);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.areaOfScope);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.createdBy);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.createdOn || ctx_r1.previewData.data.createdDate);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.functionality || ctx_r1.previewData.data.content || ctx_r1.previewData.data.toolClassDef);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_3_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"label\");\n    i0.ɵɵtext(2, \"Prompt Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 31);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate((ctx_r1.previewData == null ? null : ctx_r1.previewData.title) || ctx_r1.previewData.data.name);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_3_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"label\");\n    i0.ɵɵtext(2, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 32);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.description);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_3_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"label\");\n    i0.ɵɵtext(2, \"Role\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 31);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.role);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_3_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"label\");\n    i0.ɵɵtext(2, \"Goal\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 32);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.goal);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_3_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"label\");\n    i0.ɵɵtext(2, \"Backstory\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 32);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.backstory);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_3_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"label\");\n    i0.ɵɵtext(2, \"Added by\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 31);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.createdBy);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_3_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"label\");\n    i0.ɵɵtext(2, \"Added on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 31);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 1, ctx_r1.previewData.data.updatedAt || ctx_r1.previewData.data.createdAt, \"MM/dd/yyyy\"));\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_3_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"label\");\n    i0.ɵɵtext(2, \"Freeform Prompt \");\n    i0.ɵɵelementStart(3, \"span\", 49);\n    i0.ɵɵtext(4, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 50);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.promptTask || ctx_r1.previewData.data.template || ctx_r1.previewData.data.content);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_3_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"label\");\n    i0.ɵɵtext(2, \"Expected Output\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 50);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.expectedOutput);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"div\", 20)(2, \"h3\");\n    i0.ɵɵtext(3, \"Prompt Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, AgentsPreviewPanelComponent_div_9_div_3_div_4_Template, 5, 1, \"div\", 21)(5, AgentsPreviewPanelComponent_div_9_div_3_div_5_Template, 5, 1, \"div\", 21)(6, AgentsPreviewPanelComponent_div_9_div_3_div_6_Template, 5, 1, \"div\", 21)(7, AgentsPreviewPanelComponent_div_9_div_3_div_7_Template, 5, 1, \"div\", 21)(8, AgentsPreviewPanelComponent_div_9_div_3_div_8_Template, 5, 1, \"div\", 21);\n    i0.ɵɵelementStart(9, \"div\", 22)(10, \"div\", 23);\n    i0.ɵɵtemplate(11, AgentsPreviewPanelComponent_div_9_div_3_div_11_Template, 5, 1, \"div\", 24)(12, AgentsPreviewPanelComponent_div_9_div_3_div_12_Template, 6, 4, \"div\", 24);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"div\", 20)(14, \"h3\");\n    i0.ɵɵtext(15, \"Prompt Configuration\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, AgentsPreviewPanelComponent_div_9_div_3_div_16_Template, 7, 1, \"div\", 25)(17, AgentsPreviewPanelComponent_div_9_div_3_div_17_Template, 5, 1, \"div\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.previewData == null ? null : ctx_r1.previewData.title) || ctx_r1.previewData.data.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.description);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.role);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.goal);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.backstory);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.createdBy);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.updatedAt || ctx_r1.previewData.data.createdAt);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.promptTask || ctx_r1.previewData.data.template || ctx_r1.previewData.data.content);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.expectedOutput);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_4_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"label\");\n    i0.ɵɵtext(2, \"Knowledge Base Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 31);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.name || (ctx_r1.previewData == null ? null : ctx_r1.previewData.title));\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_4_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"label\");\n    i0.ɵɵtext(2, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 32);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.description);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_4_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"label\");\n    i0.ɵɵtext(2, \"Added by\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 31);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.createdBy);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_4_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"label\");\n    i0.ɵɵtext(2, \"Added on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 31);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 1, ctx_r1.previewData.data.createdDate || ctx_r1.previewData.data.createdOn, \"MM/dd/yyyy\"));\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_4_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"label\");\n    i0.ɵɵtext(2, \"Embedding Model\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 35)(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.embeddingModel);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_4_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"label\");\n    i0.ɵɵtext(2, \"Split Size\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"ava-slider\", 36);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"min\", 0)(\"max\", 1)(\"step\", 0.01)(\"value\", ctx_r1.previewData.data.splitSize);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_4_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"label\");\n    i0.ɵɵtext(2, \"Upload Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 45);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.uploadType);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_4_div_16_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵelement(1, \"ava-icon\", 55);\n    i0.ɵɵelementStart(2, \"span\", 56);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r3 = ctx.$implicit;\n    const i_r4 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"iconColor\", ctx_r1.getFileIconColor(i_r4))(\"iconSize\", 16);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(file_r3.fileName || file_r3.name || \"Knowledge Base Data\");\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_4_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"label\");\n    i0.ɵɵtext(2, \"Files Uploaded\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 52);\n    i0.ɵɵtemplate(4, AgentsPreviewPanelComponent_div_9_div_4_div_16_div_4_Template, 4, 3, \"div\", 53);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.previewData.data.files);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"div\", 20)(2, \"h3\");\n    i0.ɵɵtext(3, \"Knowledge Base Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, AgentsPreviewPanelComponent_div_9_div_4_div_4_Template, 5, 1, \"div\", 21)(5, AgentsPreviewPanelComponent_div_9_div_4_div_5_Template, 5, 1, \"div\", 21);\n    i0.ɵɵelementStart(6, \"div\", 22)(7, \"div\", 23);\n    i0.ɵɵtemplate(8, AgentsPreviewPanelComponent_div_9_div_4_div_8_Template, 5, 1, \"div\", 24)(9, AgentsPreviewPanelComponent_div_9_div_4_div_9_Template, 6, 4, \"div\", 24);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 20)(11, \"h3\");\n    i0.ɵɵtext(12, \"Knowledge Base Configuration\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, AgentsPreviewPanelComponent_div_9_div_4_div_13_Template, 6, 1, \"div\", 25)(14, AgentsPreviewPanelComponent_div_9_div_4_div_14_Template, 4, 4, \"div\", 25)(15, AgentsPreviewPanelComponent_div_9_div_4_div_15_Template, 5, 1, \"div\", 25)(16, AgentsPreviewPanelComponent_div_9_div_4_div_16_Template, 5, 1, \"div\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.name || (ctx_r1.previewData == null ? null : ctx_r1.previewData.title));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.createdBy);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.createdDate || ctx_r1.previewData.data.createdOn);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.embeddingModel);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.splitSize !== undefined && ctx_r1.previewData.data.splitSize !== null);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.uploadType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.files && ctx_r1.previewData.data.files.length > 0);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_5_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"label\");\n    i0.ɵɵtext(2, \"Guardrail Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 31);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate((ctx_r1.previewData == null ? null : ctx_r1.previewData.title) || ctx_r1.previewData.data.name);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_5_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"label\");\n    i0.ɵɵtext(2, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 32);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.description);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_5_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"label\");\n    i0.ɵɵtext(2, \"Added by\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 31);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.createdBy);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_5_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"label\");\n    i0.ɵɵtext(2, \"Added on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 31);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 1, ctx_r1.previewData.data.createdDate || ctx_r1.previewData.data.createdOn, \"MM/dd/yyyy\"));\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_5_div_10_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"label\");\n    i0.ɵɵtext(2, \"Guardrail in Colang\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 47);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.content);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_5_div_10_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"label\");\n    i0.ɵɵtext(2, \"Guardrail in Yml\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 47);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.data.yamlContent);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_5_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"h3\");\n    i0.ɵɵtext(2, \"Guardrail Configuration\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, AgentsPreviewPanelComponent_div_9_div_5_div_10_div_3_Template, 5, 1, \"div\", 25)(4, AgentsPreviewPanelComponent_div_9_div_5_div_10_div_4_Template, 5, 1, \"div\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.content);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.yamlContent);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"div\", 20)(2, \"h3\");\n    i0.ɵɵtext(3, \"Guardrail Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, AgentsPreviewPanelComponent_div_9_div_5_div_4_Template, 5, 1, \"div\", 21)(5, AgentsPreviewPanelComponent_div_9_div_5_div_5_Template, 5, 1, \"div\", 21);\n    i0.ɵɵelementStart(6, \"div\", 22)(7, \"div\", 23);\n    i0.ɵɵtemplate(8, AgentsPreviewPanelComponent_div_9_div_5_div_8_Template, 5, 1, \"div\", 24)(9, AgentsPreviewPanelComponent_div_9_div_5_div_9_Template, 6, 4, \"div\", 24);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(10, AgentsPreviewPanelComponent_div_9_div_5_div_10_Template, 5, 2, \"div\", 58);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.previewData == null ? null : ctx_r1.previewData.title) || ctx_r1.previewData.data.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.createdBy);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.createdDate || ctx_r1.previewData.data.createdOn);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previewData.data.yamlContent || ctx_r1.previewData.data.content);\n  }\n}\nfunction AgentsPreviewPanelComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, AgentsPreviewPanelComponent_div_9_div_1_Template, 27, 17, \"div\", 14)(2, AgentsPreviewPanelComponent_div_9_div_2_Template, 15, 6, \"div\", 15)(3, AgentsPreviewPanelComponent_div_9_div_3_Template, 18, 9, \"div\", 16)(4, AgentsPreviewPanelComponent_div_9_div_4_Template, 17, 8, \"div\", 17)(5, AgentsPreviewPanelComponent_div_9_div_5_Template, 11, 5, \"div\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.previewData == null ? null : ctx_r1.previewData.type) === \"model\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.previewData == null ? null : ctx_r1.previewData.type) === \"tool\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.previewData == null ? null : ctx_r1.previewData.type) === \"prompt\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.previewData == null ? null : ctx_r1.previewData.type) === \"knowledge\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.previewData == null ? null : ctx_r1.previewData.type) === \"guardrail\");\n  }\n}\nfunction AgentsPreviewPanelComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"p\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.previewData.error);\n  }\n}\nexport let AgentsPreviewPanelComponent = /*#__PURE__*/(() => {\n  class AgentsPreviewPanelComponent {\n    previewData = null;\n    closePreview;\n    showMoreConfig = false;\n    toggleConfigDetails() {\n      this.showMoreConfig = !this.showMoreConfig;\n    }\n    onButtonClick(event) {\n      this.closePreview();\n    }\n    getAdditionalFields(data) {\n      const excludeFields = ['id', 'name', 'description', 'labelCode', 'categoryName', 'categoryId'];\n      return Object.keys(data).filter(key => !excludeFields.includes(key) && data[key] != null).map(key => ({\n        key,\n        value: data[key]\n      }));\n    }\n    getFileIconColor(index) {\n      const colors = ['#dc2626', '#2563eb', '#16a34a', '#f59e0b', '#8b5cf6'];\n      return colors[index % colors.length];\n    }\n    getButtonLabel() {\n      const type = this.previewData?.type;\n      switch (type) {\n        case 'model':\n          return 'Edit Model';\n        case 'tool':\n          return 'Edit Tool';\n        case 'prompt':\n          return 'Edit Prompt';\n        case 'knowledge':\n          return 'Edit Knowledge Base';\n        case 'guardrail':\n          return 'Edit Guardrail';\n        default:\n          return 'Edit';\n      }\n    }\n    static ɵfac = function AgentsPreviewPanelComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AgentsPreviewPanelComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AgentsPreviewPanelComponent,\n      selectors: [[\"app-agents-preview-panel\"]],\n      inputs: {\n        previewData: \"previewData\",\n        closePreview: \"closePreview\"\n      },\n      decls: 13,\n      vars: 5,\n      consts: [[1, \"preview-panel\"], [1, \"backdrop\", 3, \"click\"], [1, \"panel-container\", 3, \"click\", \"divider\"], [\"panel-header\", \"\", 1, \"preview-header\"], [1, \"panel-title\"], [\"iconName\", \"x\", \"iconColor\", \"black\", 1, \"close-btn\", 3, \"click\"], [\"panel-content\", \"\", 1, \"preview-content\"], [\"class\", \"preview-loading\", 4, \"ngIf\"], [4, \"ngIf\"], [\"class\", \"preview-error\", 4, \"ngIf\"], [\"panel-footer\", \"\"], [\"variant\", \"info\", \"width\", \"100%\", 3, \"userClick\", \"label\"], [1, \"preview-loading\"], [1, \"loading-spinner\"], [\"class\", \"model-preview\", 4, \"ngIf\"], [\"class\", \"tool-preview\", 4, \"ngIf\"], [\"class\", \"prompt-preview\", 4, \"ngIf\"], [\"class\", \"knowledge-preview\", 4, \"ngIf\"], [\"class\", \"guardrail-preview\", 4, \"ngIf\"], [1, \"model-preview\"], [1, \"model-section\"], [\"class\", \"model-field\", 4, \"ngIf\"], [1, \"model-meta\"], [1, \"meta-row\"], [\"class\", \"meta-item\", 4, \"ngIf\"], [\"class\", \"config-field\", 4, \"ngIf\"], [\"class\", \"config-row\", 4, \"ngIf\"], [1, \"config-toggle\"], [1, \"toggle-text\", 3, \"click\"], [1, \"config-details\"], [1, \"model-field\"], [1, \"field-value\"], [1, \"field-value\", \"description-text\"], [1, \"meta-item\"], [1, \"config-field\"], [1, \"dropdown-display\"], [3, \"min\", \"max\", \"step\", \"value\"], [1, \"config-row\"], [\"class\", \"config-field half-width\", 4, \"ngIf\"], [1, \"config-field\", \"half-width\"], [\"placeholder\", \"4000\", \"type\", \"number\", 3, \"value\"], [1, \"field-hint\"], [\"placeholder\", \"0.95\", \"type\", \"number\", \"step\", \"0.01\", 3, \"value\"], [\"placeholder\", \"1\", \"type\", \"number\", 3, \"value\"], [1, \"engine-icon\"], [1, \"input-display\"], [1, \"tool-preview\"], [1, \"code-content\"], [1, \"prompt-preview\"], [1, \"required\"], [1, \"prompt-content\"], [1, \"knowledge-preview\"], [1, \"files-list\"], [\"class\", \"file-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"file-item\"], [\"iconName\", \"file-text\", 3, \"iconColor\", \"iconSize\"], [1, \"file-name\"], [1, \"guardrail-preview\"], [\"class\", \"model-section\", 4, \"ngIf\"], [1, \"preview-error\"]],\n      template: function AgentsPreviewPanelComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵlistener(\"click\", function AgentsPreviewPanelComponent_Template_div_click_1_listener() {\n            return ctx.closePreview();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"app-preview-panel\", 2);\n          i0.ɵɵlistener(\"click\", function AgentsPreviewPanelComponent_Template_app_preview_panel_click_2_listener($event) {\n            return $event.stopPropagation();\n          });\n          i0.ɵɵelementStart(3, \"div\", 3)(4, \"span\", 4);\n          i0.ɵɵtext(5, \"Metadata Information\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"ava-icon\", 5);\n          i0.ɵɵlistener(\"click\", function AgentsPreviewPanelComponent_Template_ava_icon_click_6_listener() {\n            return ctx.closePreview();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 6);\n          i0.ɵɵtemplate(8, AgentsPreviewPanelComponent_div_8_Template, 4, 0, \"div\", 7)(9, AgentsPreviewPanelComponent_div_9_Template, 6, 5, \"div\", 8)(10, AgentsPreviewPanelComponent_div_10_Template, 3, 1, \"div\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 10)(12, \"ava-button\", 11);\n          i0.ɵɵlistener(\"userClick\", function AgentsPreviewPanelComponent_Template_ava_button_userClick_12_listener($event) {\n            return ctx.onButtonClick($event);\n          });\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"divider\", false);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.previewData == null ? null : ctx.previewData.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (ctx.previewData == null ? null : ctx.previewData.data) && !(ctx.previewData == null ? null : ctx.previewData.loading));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.previewData == null ? null : ctx.previewData.error);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"label\", ctx.getButtonLabel());\n        }\n      },\n      dependencies: [PreviewPanelComponent, IconComponent, ButtonComponent, DatePipe, CommonModule, i1.NgForOf, i1.NgIf, SliderComponent, AvaTextboxComponent],\n      styles: [\".preview-panel[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  z-index: 1000;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.backdrop[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(0, 0, 0, 0.5);\\n  -webkit-backdrop-filter: blur(4px);\\n          backdrop-filter: blur(4px);\\n}\\n\\n.panel-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1000;\\n  width: 480px;\\n  max-height: 90vh;\\n  background: #fff;\\n  border-radius: 12px;\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);\\n  overflow: hidden;\\n  animation: slideIn 0.3s ease-out;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.preview-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  width: 100%;\\n}\\n\\n.panel-title[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin: 0;\\n}\\n\\n.close-btn[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  background: transparent;\\n  border: none;\\n  color: #6b7280;\\n  cursor: pointer;\\n  border-radius: 8px;\\n  transition: all 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.close-btn[_ngcontent-%COMP%]:hover {\\n  background: #f3f4f6;\\n  color: #374151;\\n}\\n\\n.preview-content[_ngcontent-%COMP%] {\\n  position: relative;\\n  flex: 1;\\n  overflow-y: auto;\\n  overflow-x: hidden; \\n\\n  max-height: calc(90vh - 140px);\\n  padding-top: 40px;\\n  border-top: 1px solid #e5e7eb;\\n}\\n.preview-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.preview-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: rgba(0, 0, 0, 0.1);\\n  border-radius: 3px;\\n}\\n.preview-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(0, 0, 0, 0.3);\\n  border-radius: 3px;\\n}\\n.preview-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: rgba(0, 0, 0, 0.5);\\n}\\n\\n.config-field[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.config-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin-bottom: 8px;\\n}\\n.config-field[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #1a1d29;\\n  line-height: 1.5;\\n}\\n.config-field[_ngcontent-%COMP%]   .field-hint[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #6b7280;\\n  margin-top: 4px;\\n}\\n\\n.code-content[_ngcontent-%COMP%], .prompt-content[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  background: #f8f9fa;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  color: #1a1d29;\\n  font-family: \\\"SF Mono\\\", Monaco, \\\"Cascadia Code\\\", monospace;\\n  font-size: 0.875rem;\\n  line-height: 1.6;\\n  min-height: 120px;\\n  white-space: pre-wrap;\\n  overflow-y: auto;\\n  overflow-x: hidden;\\n  scrollbar-width: none;\\n  -ms-overflow-style: none;\\n}\\n.code-content[_ngcontent-%COMP%]::-webkit-scrollbar, .prompt-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none;\\n}\\n.code-content[_ngcontent-%COMP%]:empty::before, .prompt-content[_ngcontent-%COMP%]:empty::before {\\n  content: \\\"No configuration available\\\";\\n  color: #9ca3af;\\n  font-style: italic;\\n}\\n\\n.model-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin: 0 0 24px 0;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  margin-bottom: 20px;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-row[_ngcontent-%COMP%]   .config-field.half-width[_ngcontent-%COMP%] {\\n  flex: 1;\\n  margin-bottom: 0;\\n  min-width: 0;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n  padding-bottom: 24px;\\n  border-bottom: 1px solid #e5e7eb;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10rem;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #1a1d29;\\n  margin-bottom: 4px;\\n  font-weight: 600;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #1a1d29;\\n  font-size: 0.875rem;\\n}\\n\\n.tool-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%], .prompt-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .prompt-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin: 0 0 24px 0;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%], .prompt-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%], .prompt-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin-bottom: 8px;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%], .prompt-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  line-height: 1.5;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value.description-text[_ngcontent-%COMP%], .prompt-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value.description-text[_ngcontent-%COMP%] {\\n  font-weight: 400;\\n  color: #6b7280;\\n  line-height: 1.6;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .input-display[_ngcontent-%COMP%], .prompt-preview[_ngcontent-%COMP%]   .input-display[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 12px 16px;\\n  background: #f8f9fa;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  color: #1a1d29;\\n  min-height: 48px;\\n  font-size: 0.875rem;\\n}\\n\\n  .preview-panel [panel-footer] {\\n  padding: 20px 24px 24px 24px;\\n  border-top: 1px solid #f0f1f2;\\n  background: #fafbfc;\\n}\\n  .preview-panel [panel-footer] ava-button {\\n  width: 100%;\\n}\\n  .preview-panel [panel-footer] ava-button button {\\n  width: 100%;\\n  height: 44px;\\n  font-weight: 600;\\n  border-radius: 8px;\\n  transition: all 0.2s ease;\\n}\\n  .preview-panel [panel-footer] ava-button button:hover {\\n  transform: translateY(-1px);\\n}\\n\\n.preview-loading[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 20px;\\n}\\n.preview-loading[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border: 3px solid #f3f4f6;\\n  border-top: 3px solid #3b82f6;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n  margin-bottom: 16px;\\n}\\n.preview-loading[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #6b7280;\\n  font-size: 0.875rem;\\n}\\n\\n.preview-error[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: #dc2626;\\n  padding: 40px 20px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .panel-container[_ngcontent-%COMP%] {\\n    width: 95vw;\\n    max-width: 400px;\\n  }\\n  .panel-title[_ngcontent-%COMP%] {\\n    font-size: 1.25rem;\\n  }\\n}\\n.model-preview[_ngcontent-%COMP%]   .preview-field[_ngcontent-%COMP%] {\\n  border-left-color: #10b981;\\n}\\n\\n.tool-preview[_ngcontent-%COMP%]   .preview-field[_ngcontent-%COMP%] {\\n  border-left-color: #f59e0b;\\n}\\n\\n.prompt-preview[_ngcontent-%COMP%]   .preview-field[_ngcontent-%COMP%] {\\n  border-left-color: #8b5cf6;\\n}\\n\\n.knowledge-preview[_ngcontent-%COMP%]   .preview-field[_ngcontent-%COMP%] {\\n  border-left-color: #06b6d4;\\n}\\n\\n.guardrail-preview[_ngcontent-%COMP%]   .preview-field[_ngcontent-%COMP%] {\\n  border-left-color: #ef4444;\\n}\\n\\n.model-preview[_ngcontent-%COMP%] {\\n  height: 100%;\\n  overflow-y: auto;\\n  overflow-x: hidden; \\n\\n  padding-bottom: 20px;\\n  width: 100%;\\n  box-sizing: border-box;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin: 0 0 24px 0;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  margin-bottom: 20px;\\n  width: 100%;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-row[_ngcontent-%COMP%]   .config-field.half-width[_ngcontent-%COMP%] {\\n  flex: 1;\\n  margin-bottom: 0;\\n  min-width: 0;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-row[_ngcontent-%COMP%]   .config-field.half-width[_ngcontent-%COMP%]     ava-textbox {\\n  width: 100%;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-row[_ngcontent-%COMP%]   .config-field.half-width[_ngcontent-%COMP%]     ava-textbox .textbox-container {\\n  width: 100%;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-row[_ngcontent-%COMP%]   .config-field.half-width[_ngcontent-%COMP%]     ava-textbox .textbox-container input {\\n  width: 100%;\\n  box-sizing: border-box;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  width: 100%;\\n  box-sizing: border-box;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin-bottom: 8px;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .field-hint[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #6b7280;\\n  margin-top: 4px;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]     ava-textbox {\\n  width: 100%;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]     ava-textbox .textbox-container {\\n  width: 100%;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]     ava-textbox .textbox-container input {\\n  width: 100%;\\n  box-sizing: border-box;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #374151;\\n  margin-bottom: 8px;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  line-height: 1.5;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value.description-text[_ngcontent-%COMP%] {\\n  font-weight: 400;\\n  color: #6b7280;\\n  line-height: 1.6;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n  padding-bottom: 24px;\\n  border-bottom: 1px solid #e5e7eb;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10rem;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  color: #6b7280;\\n  margin-bottom: 4px;\\n  font-weight: 400;\\n}\\n.model-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #1a1d29;\\n  font-size: 0.875rem;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #374151;\\n  margin-bottom: 8px;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .dropdown-display[_ngcontent-%COMP%], \\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .input-display[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 12px 16px;\\n  background: #f8f9fa;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  color: #1a1d29;\\n  min-height: 48px;\\n  box-sizing: border-box;\\n  font-size: 0.875rem;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .dropdown-display[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  width: 0;\\n  height: 0;\\n  border-left: 6px solid transparent;\\n  border-right: 6px solid transparent;\\n  border-top: 8px solid #6b7280;\\n  margin-left: 8px;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .dropdown-display[_ngcontent-%COMP%]   .engine-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 1rem;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]     ava-slider .slider-container {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]     ava-slider .slider-container .slider-track {\\n  flex: 1;\\n  height: 6px;\\n  background: #e5e7eb;\\n  border-radius: 3px;\\n  position: relative;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]     ava-slider .slider-container .slider-track .slider-fill {\\n  height: 100%;\\n  background: #3b82f6;\\n  border-radius: 3px;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]     ava-slider .slider-container .slider-thumb {\\n  width: 20px;\\n  height: 20px;\\n  background: #3b82f6;\\n  border-radius: 50%;\\n  cursor: pointer;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\\n  border: none;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]     ava-slider .slider-container .slider-value {\\n  min-width: 60px;\\n  padding: 8px 12px;\\n  background: #f8f9fa;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 6px;\\n  text-align: center;\\n  font-size: 0.875rem;\\n  color: #374151;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-toggle[_ngcontent-%COMP%] {\\n  margin: 16px 0;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-toggle[_ngcontent-%COMP%]   .toggle-text[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  cursor: pointer;\\n  font-size: 14px;\\n  color: #007bff;\\n  transition: color 0.2s ease;\\n}\\n.model-preview[_ngcontent-%COMP%]   .config-toggle[_ngcontent-%COMP%]   .toggle-text[_ngcontent-%COMP%]:hover {\\n  color: #0056b3;\\n}\\n\\n.config-toggle[_ngcontent-%COMP%] {\\n  margin: 16px 0;\\n}\\n.config-toggle[_ngcontent-%COMP%]   .toggle-text[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  cursor: pointer;\\n  font-size: 14px;\\n  color: #007bff;\\n  transition: color 0.2s ease;\\n}\\n.config-toggle[_ngcontent-%COMP%]   .toggle-text[_ngcontent-%COMP%]:hover {\\n  color: #0056b3;\\n}\\n\\n.config-details[_ngcontent-%COMP%] {\\n  max-height: 0;\\n  overflow: hidden;\\n  transition: max-height 0.3s ease;\\n}\\n.config-details.expanded[_ngcontent-%COMP%] {\\n  max-height: 500px;\\n}\\n\\n.prompt-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.prompt-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin: 0 0 24px 0;\\n}\\n.prompt-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.prompt-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #374151;\\n  margin-bottom: 8px;\\n}\\n.prompt-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  line-height: 1.5;\\n}\\n.prompt-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value.description-text[_ngcontent-%COMP%] {\\n  font-weight: 400;\\n  color: #6b7280;\\n  line-height: 1.6;\\n}\\n.prompt-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n  padding-bottom: 24px;\\n  border-bottom: 1px solid #e5e7eb;\\n}\\n.prompt-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10rem;\\n}\\n.prompt-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  color: #6b7280;\\n  margin-bottom: 4px;\\n  font-weight: 400;\\n}\\n.prompt-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #1a1d29;\\n  font-size: 0.875rem;\\n}\\n.prompt-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.prompt-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #374151;\\n  margin-bottom: 8px;\\n}\\n.prompt-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]   .required[_ngcontent-%COMP%] {\\n  color: #ef4444;\\n  margin-left: 2px;\\n}\\n.prompt-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .prompt-content[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  background: #f8f9fa;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  color: #1a1d29;\\n  font-size: 0.875rem;\\n  line-height: 1.6;\\n  min-height: 120px;\\n  white-space: pre-wrap;\\n}\\n\\n.guardrail-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.guardrail-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin: 0 0 24px 0;\\n}\\n.guardrail-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.guardrail-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin-bottom: 8px;\\n}\\n.guardrail-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 400;\\n  color: #1a1d29;\\n  line-height: 1.5;\\n}\\n.guardrail-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value.description-text[_ngcontent-%COMP%] {\\n  font-weight: 400;\\n  color: #6b7280;\\n  line-height: 1.6;\\n}\\n.guardrail-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n  padding-bottom: 24px;\\n  border-bottom: 1px solid #e5e7eb;\\n}\\n.guardrail-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10rem;\\n}\\n.guardrail-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  color: #1a1d29;\\n  margin-bottom: 4px;\\n  font-weight: 600;\\n}\\n.guardrail-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #1a1d29;\\n  font-size: 0.875rem;\\n}\\n.guardrail-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.guardrail-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin-bottom: 8px;\\n}\\n.guardrail-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .code-content[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  background: #f8f9fa;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  color: #1a1d29;\\n  font-family: \\\"SF Mono\\\", Monaco, \\\"Cascadia Code\\\", monospace;\\n  font-size: 0.875rem;\\n  line-height: 1.6;\\n  min-height: 200px;\\n  white-space: pre-wrap;\\n  overflow-y: auto;\\n  max-height: 400px;\\n}\\n\\n.knowledge-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin: 0 0 24px 0;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin-bottom: 8px;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  line-height: 1.5;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value.description-text[_ngcontent-%COMP%] {\\n  font-weight: 400;\\n  color: #6b7280;\\n  line-height: 1.6;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n  padding-bottom: 24px;\\n  border-bottom: 1px solid #e5e7eb;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10rem;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  color: #1a1d29;\\n  margin-bottom: 4px;\\n  font-weight: 600;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #1a1d29;\\n  font-size: 0.875rem;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin-bottom: 8px;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .dropdown-display[_ngcontent-%COMP%], \\n.knowledge-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .input-display[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 12px 16px;\\n  background: #f8f9fa;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  color: #1a1d29;\\n  min-height: 48px;\\n  box-sizing: border-box;\\n  font-size: 0.875rem;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .dropdown-display[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  width: 0;\\n  height: 0;\\n  border-left: 6px solid transparent;\\n  border-right: 6px solid transparent;\\n  border-top: 8px solid #6b7280;\\n  margin-left: 8px;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .slider-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .slider-container[_ngcontent-%COMP%]   .slider-track[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 6px;\\n  background: #e5e7eb;\\n  border-radius: 3px;\\n  position: relative;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .slider-container[_ngcontent-%COMP%]   .slider-track[_ngcontent-%COMP%]   .slider-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: #3b82f6;\\n  border-radius: 3px;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .slider-container[_ngcontent-%COMP%]   .slider-value[_ngcontent-%COMP%] {\\n  min-width: 60px;\\n  padding: 8px 12px;\\n  background: #f8f9fa;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 6px;\\n  text-align: center;\\n  font-size: 0.875rem;\\n  color: #374151;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .files-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 8px 12px;\\n  background: #f8f9fa;\\n  border-radius: 6px;\\n  margin-bottom: 8px;\\n}\\n.knowledge-preview[_ngcontent-%COMP%]   .files-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-name[_ngcontent-%COMP%] {\\n  color: #3b82f6;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n}\\n\\n.tool-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin: 0 0 24px 0;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin-bottom: 8px;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  line-height: 1.5;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-field[_ngcontent-%COMP%]   .field-value.description-text[_ngcontent-%COMP%] {\\n  font-weight: 400;\\n  color: #6b7280;\\n  line-height: 1.6;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n  padding-bottom: 24px;\\n  border-bottom: 1px solid #e5e7eb;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10rem;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  color: #1a1d29;\\n  margin-bottom: 4px;\\n  font-weight: 600;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .model-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-item[_ngcontent-%COMP%]   .field-value[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #1a1d29;\\n  font-size: 0.875rem;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #1a1d29;\\n  margin-bottom: 8px;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .input-display[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 12px 16px;\\n  background: #f8f9fa;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  color: #1a1d29;\\n  min-height: 48px;\\n  box-sizing: border-box;\\n  font-size: 0.875rem;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .code-content[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  background: #f8f9fa;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  color: #1a1d29;\\n  font-family: \\\"SF Mono\\\", Monaco, \\\"Cascadia Code\\\", monospace;\\n  font-size: 0.875rem;\\n  line-height: 1.6;\\n  min-height: 200px;\\n  white-space: pre-wrap;\\n}\\n.tool-preview[_ngcontent-%COMP%]   .config-field[_ngcontent-%COMP%]   .code-content[_ngcontent-%COMP%]:empty::before {\\n  content: \\\"No configuration available\\\";\\n  color: #9ca3af;\\n  font-style: italic;\\n}\\n\\n[panel-footer][_ngcontent-%COMP%] {\\n  position: sticky;\\n  bottom: 0;\\n  background: white;\\n  padding: 16px;\\n  border-top: 1px solid #f0f1f2;\\n  margin-top: auto;\\n}\\n\\n.code-content[_ngcontent-%COMP%] {\\n  overflow-y: auto;\\n  overflow-x: hidden;\\n  scrollbar-width: none; \\n\\n  -ms-overflow-style: none; \\n\\n}\\n.code-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none; \\n\\n}\\n\\n.config-field[_ngcontent-%COMP%]     ava-textbox .textbox-container {\\n  box-shadow: none !important;\\n}\\n.config-field[_ngcontent-%COMP%]     ava-textbox .textbox-container input {\\n  box-shadow: none !important;\\n}\\n\\n  app-preview-panel .preview-panel > .preview-header {\\n  padding: 24px !important;\\n}\\n\\n  app-preview-panel .preview-panel > .preview-content {\\n  padding: 0 24px !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3Byb2plY3RzL3NoYXJlZC9wYWdlcy9hZ2VudHMvYnVpbGQtYWdlbnRzL2FnZW50cy1wcmV2aWV3LXBhbmVsL2FnZW50cy1wcmV2aWV3LXBhbmVsLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUVBO0VBQ0UsZUFBQTtFQUNBLE1BQUE7RUFDQSxPQUFBO0VBQ0EsUUFBQTtFQUNBLFNBQUE7RUFDQSxhQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7QUFERjs7QUFJQTtFQUNFLGtCQUFBO0VBQ0EsTUFBQTtFQUNBLE9BQUE7RUFDQSxRQUFBO0VBQ0EsU0FBQTtFQUNBLDhCQUFBO0VBQ0Esa0NBQUE7VUFBQSwwQkFBQTtBQURGOztBQUlBO0VBQ0Usa0JBQUE7RUFDQSxhQUFBO0VBQ0EsWUFBQTtFQUNBLGdCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxtQkFBQTtFQUNBLDJDQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQ0FBQTtFQUNBLGFBQUE7RUFDQSxzQkFBQTtBQURGOztBQUlBO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsOEJBQUE7RUFDQSxXQUFBO0FBREY7O0FBSUU7RUFDRSxpQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLFNBQUE7QUFESjs7QUFJRTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0EsdUJBQUE7RUFDQSxZQUFBO0VBQ0EsY0FBQTtFQUNBLGVBQUE7RUFDQSxrQkFBQTtFQUNBLHlCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7QUFESjtBQUdJO0VBQ0UsbUJBQUE7RUFDQSxjQUFBO0FBRE47O0FBS0E7RUFDRSxrQkFBQTtFQUNBLE9BQUE7RUFDQSxnQkFBQTtFQUNBLGtCQUFBLEVBQUEsZ0NBQUE7RUFDQSw4QkFBQTtFQUNBLGlCQUFBO0VBQ0EsNkJBQUE7QUFGRjtBQUdFO0VBQ0UsVUFBQTtBQURKO0FBSUU7RUFDRSw4QkFBQTtFQUNBLGtCQUFBO0FBRko7QUFLRTtFQUNFLDhCQUFBO0VBQ0Esa0JBQUE7QUFISjtBQUtJO0VBQ0UsOEJBQUE7QUFITjs7QUFTQTtFQUNFLG1CQUFBO0FBTkY7QUFRRTtFQUNFLGNBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0FBTko7QUFTRTtFQUNFLG1CQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0FBUEo7QUFVRTtFQUNFLGtCQUFBO0VBQ0EsY0FBQTtFQUNBLGVBQUE7QUFSSjs7QUFhQTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxjQUFBO0VBQ0EsMERBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EsaUJBQUE7RUFDQSxxQkFBQTtFQUNBLGdCQUFBO0VBQ0Esa0JBQUE7RUFDQSxxQkFBQTtFQUNBLHdCQUFBO0FBVkY7QUFZRTtFQUNFLGFBQUE7QUFWSjtBQWFFO0VBQ0UscUNBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7QUFYSjs7QUFpQkU7RUFDRSxtQkFBQTtBQWRKO0FBZ0JJO0VBQ0Usa0JBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxrQkFBQTtBQWROO0FBa0JFO0VBQ0UsYUFBQTtFQUNBLFNBQUE7RUFDQSxtQkFBQTtBQWhCSjtBQWtCSTtFQUNFLE9BQUE7RUFDQSxnQkFBQTtFQUNBLFlBQUE7QUFoQk47QUFvQkU7RUFDRSxtQkFBQTtFQUNBLG9CQUFBO0VBQ0EsZ0NBQUE7QUFsQko7QUFvQkk7RUFDRSxhQUFBO0VBQ0EsVUFBQTtBQWxCTjtBQXFCUTtFQUNFLG1CQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7QUFuQlY7QUFzQlE7RUFDRSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxtQkFBQTtBQXBCVjs7QUE2QkU7RUFDRSxtQkFBQTtBQTFCSjtBQTRCSTtFQUNFLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7QUExQk47QUE4QkU7RUFDRSxtQkFBQTtBQTVCSjtBQThCSTtFQUNFLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7QUE1Qk47QUErQkk7RUFDRSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0FBN0JOO0FBK0JNO0VBQ0UsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EsZ0JBQUE7QUE3QlI7QUFrQ0U7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSw4QkFBQTtFQUNBLGtCQUFBO0VBQ0EsbUJBQUE7RUFDQSx5QkFBQTtFQUNBLGtCQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0VBQ0EsbUJBQUE7QUFoQ0o7O0FBcUNBO0VBQ0UsNEJBQUE7RUFDQSw2QkFBQTtFQUNBLG1CQUFBO0FBbENGO0FBb0NFO0VBQ0UsV0FBQTtBQWxDSjtBQW9DSTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0EsZ0JBQUE7RUFDQSxrQkFBQTtFQUNBLHlCQUFBO0FBbENOO0FBb0NNO0VBQ0UsMkJBQUE7QUFsQ1I7O0FBMENBO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLGtCQUFBO0FBdkNGO0FBeUNFO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSx5QkFBQTtFQUNBLDZCQUFBO0VBQ0Esa0JBQUE7RUFDQSxrQ0FBQTtFQUNBLG1CQUFBO0FBdkNKO0FBMENFO0VBQ0UsY0FBQTtFQUNBLG1CQUFBO0FBeENKOztBQTRDQTtFQUNFLGtCQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0FBekNGOztBQTRDQTtFQUNFO0lBQUssdUJBQUE7RUF4Q0w7RUF5Q0E7SUFBTyx5QkFBQTtFQXRDUDtBQUNGO0FBeUNBO0VBQ0U7SUFDRSxXQUFBO0lBQ0EsZ0JBQUE7RUF2Q0Y7RUEwQ0E7SUFDRSxrQkFBQTtFQXhDRjtBQUNGO0FBOENBO0VBQ0UsMEJBQUE7QUE1Q0Y7O0FBK0NBO0VBQ0UsMEJBQUE7QUE1Q0Y7O0FBK0NBO0VBQ0UsMEJBQUE7QUE1Q0Y7O0FBK0NBO0VBQ0UsMEJBQUE7QUE1Q0Y7O0FBK0NBO0VBQ0UsMEJBQUE7QUE1Q0Y7O0FBK0NBO0VBQ0UsWUFBQTtFQUNBLGdCQUFBO0VBQ0Esa0JBQUEsRUFBQSxnQ0FBQTtFQUNBLG9CQUFBO0VBQ0EsV0FBQTtFQUNBLHNCQUFBO0FBNUNGO0FBOENFO0VBQ0UsbUJBQUE7QUE1Q0o7QUE4Q0k7RUFDRSxrQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0FBNUNOO0FBZ0RFO0VBQ0UsYUFBQTtFQUNBLFNBQUE7RUFDQSxtQkFBQTtFQUNBLFdBQUE7QUE5Q0o7QUFnREk7RUFDRSxPQUFBO0VBQ0EsZ0JBQUE7RUFDQSxZQUFBO0FBOUNOO0FBZ0RNO0VBQ0UsV0FBQTtBQTlDUjtBQWdEUTtFQUNFLFdBQUE7QUE5Q1Y7QUFnRFU7RUFDRSxXQUFBO0VBQ0Esc0JBQUE7QUE5Q1o7QUFxREU7RUFDRSxtQkFBQTtFQUNBLFdBQUE7RUFDQSxzQkFBQTtBQW5ESjtBQXFESTtFQUNFLGNBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0FBbkROO0FBc0RJO0VBQ0Usa0JBQUE7RUFDQSxjQUFBO0VBQ0EsZUFBQTtBQXBETjtBQXVESTtFQUNFLFdBQUE7QUFyRE47QUF1RE07RUFDRSxXQUFBO0FBckRSO0FBdURRO0VBQ0UsV0FBQTtFQUNBLHNCQUFBO0FBckRWO0FBMkRFO0VBQ0UsbUJBQUE7QUF6REo7QUEyREk7RUFDRSxjQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxrQkFBQTtBQXpETjtBQTRESTtFQUNFLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EsZ0JBQUE7QUExRE47QUE0RE07RUFDRSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxnQkFBQTtBQTFEUjtBQStERTtFQUNFLG1CQUFBO0VBQ0Esb0JBQUE7RUFDQSxnQ0FBQTtBQTdESjtBQStESTtFQUNFLGFBQUE7RUFDQSxVQUFBO0FBN0ROO0FBZ0VRO0VBQ0UsY0FBQTtFQUNBLG1CQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7QUE5RFY7QUFpRVE7RUFDRSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxtQkFBQTtBQS9EVjtBQXFFRTtFQUNFLG1CQUFBO0FBbkVKO0FBcUVJO0VBQ0UsY0FBQTtFQUNBLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7QUFuRU47QUFzRUk7O0VBRUUsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsOEJBQUE7RUFDQSxrQkFBQTtFQUNBLG1CQUFBO0VBQ0EseUJBQUE7RUFDQSxrQkFBQTtFQUNBLGNBQUE7RUFDQSxnQkFBQTtFQUNBLHNCQUFBO0VBQ0EsbUJBQUE7QUFwRU47QUF3RU07RUFDRSxXQUFBO0VBQ0EsUUFBQTtFQUNBLFNBQUE7RUFDQSxrQ0FBQTtFQUNBLG1DQUFBO0VBQ0EsNkJBQUE7RUFDQSxnQkFBQTtBQXRFUjtBQXlFTTtFQUNFLGlCQUFBO0VBQ0EsZUFBQTtBQXZFUjtBQTZFTTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFNBQUE7QUEzRVI7QUE2RVE7RUFDRSxPQUFBO0VBQ0EsV0FBQTtFQUNBLG1CQUFBO0VBQ0Esa0JBQUE7RUFDQSxrQkFBQTtBQTNFVjtBQTZFVTtFQUNFLFlBQUE7RUFDQSxtQkFBQTtFQUNBLGtCQUFBO0FBM0VaO0FBK0VRO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxtQkFBQTtFQUNBLGtCQUFBO0VBQ0EsZUFBQTtFQUNBLHdDQUFBO0VBQ0EsWUFBQTtBQTdFVjtBQWdGUTtFQUNFLGVBQUE7RUFDQSxpQkFBQTtFQUNBLG1CQUFBO0VBQ0EseUJBQUE7RUFDQSxrQkFBQTtFQUNBLGtCQUFBO0VBQ0EsbUJBQUE7RUFDQSxjQUFBO0FBOUVWO0FBb0ZFO0VBQ0UsY0FBQTtBQWxGSjtBQW9GSTtFQUNFLHFCQUFBO0VBQ0EsZUFBQTtFQUNBLGVBQUE7RUFDQSxjQUFBO0VBQ0EsMkJBQUE7QUFsRk47QUFvRk07RUFDRSxjQUFBO0FBbEZSOztBQWlHQTtFQUNFLGNBQUE7QUE5RkY7QUFnR0U7RUFDRSxxQkFBQTtFQUNBLGVBQUE7RUFDQSxlQUFBO0VBQ0EsY0FBQTtFQUNBLDJCQUFBO0FBOUZKO0FBZ0dJO0VBQ0UsY0FBQTtBQTlGTjs7QUFtR0E7RUFDRSxhQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQ0FBQTtBQWhHRjtBQWtHRTtFQUNFLGlCQUFBO0FBaEdKOztBQXFHRTtFQUNFLG1CQUFBO0FBbEdKO0FBb0dJO0VBQ0Usa0JBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxrQkFBQTtBQWxHTjtBQXNHRTtFQUNFLG1CQUFBO0FBcEdKO0FBc0dJO0VBQ0UsY0FBQTtFQUNBLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7QUFwR047QUF1R0k7RUFDRSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0FBckdOO0FBdUdNO0VBQ0UsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EsZ0JBQUE7QUFyR1I7QUEwR0U7RUFDRSxtQkFBQTtFQUNBLG9CQUFBO0VBQ0EsZ0NBQUE7QUF4R0o7QUEwR0k7RUFDRSxhQUFBO0VBQ0EsVUFBQTtBQXhHTjtBQTJHUTtFQUNFLGNBQUE7RUFDQSxtQkFBQTtFQUNBLGNBQUE7RUFDQSxrQkFBQTtFQUNBLGdCQUFBO0FBekdWO0FBNEdRO0VBQ0UsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EsbUJBQUE7QUExR1Y7QUFnSEU7RUFDRSxtQkFBQTtBQTlHSjtBQWdISTtFQUNFLGNBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0FBOUdOO0FBZ0hNO0VBQ0UsY0FBQTtFQUNBLGdCQUFBO0FBOUdSO0FBa0hJO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EseUJBQUE7RUFDQSxrQkFBQTtFQUNBLGNBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EsaUJBQUE7RUFDQSxxQkFBQTtBQWhITjs7QUFzSEU7RUFDRSxtQkFBQTtBQW5ISjtBQXFISTtFQUNFLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7QUFuSE47QUF1SEU7RUFDRSxtQkFBQTtBQXJISjtBQXVISTtFQUNFLGNBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0FBckhOO0FBd0hJO0VBQ0UsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxnQkFBQTtBQXRITjtBQXdITTtFQUNFLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0FBdEhSO0FBMkhFO0VBQ0UsbUJBQUE7RUFDQSxvQkFBQTtFQUNBLGdDQUFBO0FBekhKO0FBMkhJO0VBQ0UsYUFBQTtFQUNBLFVBQUE7QUF6SE47QUE0SFE7RUFDRSxjQUFBO0VBQ0EsbUJBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtBQTFIVjtBQTZIUTtFQUNFLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLG1CQUFBO0FBM0hWO0FBaUlFO0VBQ0UsbUJBQUE7QUEvSEo7QUFpSUk7RUFDRSxjQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxrQkFBQTtBQS9ITjtBQWtJSTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxjQUFBO0VBQ0EsMERBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EsaUJBQUE7RUFDQSxxQkFBQTtFQUNBLGdCQUFBO0VBQ0EsaUJBQUE7QUFoSU47O0FBc0lFO0VBQ0UsbUJBQUE7QUFuSUo7QUFxSUk7RUFDRSxrQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0FBbklOO0FBdUlFO0VBQ0UsbUJBQUE7QUFySUo7QUF1SUk7RUFDRSxjQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxrQkFBQTtBQXJJTjtBQXdJSTtFQUNFLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EsZ0JBQUE7QUF0SU47QUF3SU07RUFDRSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxnQkFBQTtBQXRJUjtBQTJJRTtFQUNFLG1CQUFBO0VBQ0Esb0JBQUE7RUFDQSxnQ0FBQTtBQXpJSjtBQTJJSTtFQUNFLGFBQUE7RUFDQSxVQUFBO0FBeklOO0FBNElRO0VBQ0UsY0FBQTtFQUNBLG1CQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7QUExSVY7QUE2SVE7RUFDRSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxtQkFBQTtBQTNJVjtBQWlKRTtFQUNFLG1CQUFBO0FBL0lKO0FBaUpJO0VBQ0UsY0FBQTtFQUNBLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7QUEvSU47QUFrSkk7O0VBRUUsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsOEJBQUE7RUFDQSxrQkFBQTtFQUNBLG1CQUFBO0VBQ0EseUJBQUE7RUFDQSxrQkFBQTtFQUNBLGNBQUE7RUFDQSxnQkFBQTtFQUNBLHNCQUFBO0VBQ0EsbUJBQUE7QUFoSk47QUFvSk07RUFDRSxXQUFBO0VBQ0EsUUFBQTtFQUNBLFNBQUE7RUFDQSxrQ0FBQTtFQUNBLG1DQUFBO0VBQ0EsNkJBQUE7RUFDQSxnQkFBQTtBQWxKUjtBQXNKSTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFNBQUE7QUFwSk47QUFzSk07RUFDRSxPQUFBO0VBQ0EsV0FBQTtFQUNBLG1CQUFBO0VBQ0Esa0JBQUE7RUFDQSxrQkFBQTtBQXBKUjtBQXNKUTtFQUNFLFlBQUE7RUFDQSxtQkFBQTtFQUNBLGtCQUFBO0FBcEpWO0FBd0pNO0VBQ0UsZUFBQTtFQUNBLGlCQUFBO0VBQ0EsbUJBQUE7RUFDQSx5QkFBQTtFQUNBLGtCQUFBO0VBQ0Esa0JBQUE7RUFDQSxtQkFBQTtFQUNBLGNBQUE7QUF0SlI7QUE0Skk7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxRQUFBO0VBQ0EsaUJBQUE7RUFDQSxtQkFBQTtFQUNBLGtCQUFBO0VBQ0Esa0JBQUE7QUExSk47QUE0Sk07RUFDRSxjQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtBQTFKUjs7QUFpS0U7RUFDRSxtQkFBQTtBQTlKSjtBQWdLSTtFQUNFLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7QUE5Sk47QUFrS0U7RUFDRSxtQkFBQTtBQWhLSjtBQWtLSTtFQUNFLGNBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0FBaEtOO0FBbUtJO0VBQ0UsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxnQkFBQTtBQWpLTjtBQW1LTTtFQUNFLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0FBaktSO0FBc0tFO0VBQ0UsbUJBQUE7RUFDQSxvQkFBQTtFQUNBLGdDQUFBO0FBcEtKO0FBc0tJO0VBQ0UsYUFBQTtFQUNBLFVBQUE7QUFwS047QUF1S1E7RUFDRSxjQUFBO0VBQ0EsbUJBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtBQXJLVjtBQXdLUTtFQUNFLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLG1CQUFBO0FBdEtWO0FBNEtFO0VBQ0UsbUJBQUE7QUExS0o7QUE0S0k7RUFDRSxjQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxrQkFBQTtBQTFLTjtBQTZLSTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLDhCQUFBO0VBQ0Esa0JBQUE7RUFDQSxtQkFBQTtFQUNBLHlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxjQUFBO0VBQ0EsZ0JBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQUFBO0FBM0tOO0FBOEtJO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EseUJBQUE7RUFDQSxrQkFBQTtFQUNBLGNBQUE7RUFDQSwwREFBQTtFQUNBLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSxpQkFBQTtFQUNBLHFCQUFBO0FBNUtOO0FBK0tNO0VBQ0UscUNBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7QUE3S1I7O0FBb0xBO0VBQ0UsZ0JBQUE7RUFDQSxTQUFBO0VBQ0EsaUJBQUE7RUFDQSxhQUFBO0VBQ0EsNkJBQUE7RUFDQSxnQkFBQTtBQWpMRjs7QUFvTEE7RUFFRSxnQkFBQTtFQUNBLGtCQUFBO0VBQ0EscUJBQUEsRUFBQSxZQUFBO0VBQ0Esd0JBQUEsRUFBQSwwQkFBQTtBQWxMRjtBQW9MRTtFQUNFLGFBQUEsRUFBQSx5QkFBQTtBQWxMSjs7QUF5TEk7RUFDRSwyQkFBQTtBQXRMTjtBQXdMTTtFQUNFLDJCQUFBO0FBdExSOztBQTJMQTtFQUNFLHdCQUFBO0FBeExGOztBQTJMQTtFQUNFLDBCQUFBO0FBeExGIiwic291cmNlc0NvbnRlbnQiOlsiXHJcbi8vIE1haW4gcGFuZWwgc3RydWN0dXJlXHJcbi5wcmV2aWV3LXBhbmVsIHtcclxuICBwb3NpdGlvbjogZml4ZWQ7XHJcbiAgdG9wOiAwO1xyXG4gIGxlZnQ6IDA7XHJcbiAgcmlnaHQ6IDA7XHJcbiAgYm90dG9tOiAwO1xyXG4gIHotaW5kZXg6IDEwMDA7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG59XHJcblxyXG4uYmFja2Ryb3Age1xyXG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcclxuICB0b3A6IDA7XHJcbiAgbGVmdDogMDtcclxuICByaWdodDogMDtcclxuICBib3R0b206IDA7XHJcbiAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjUpO1xyXG4gIGJhY2tkcm9wLWZpbHRlcjogYmx1cig0cHgpO1xyXG59XHJcblxyXG4ucGFuZWwtY29udGFpbmVyIHtcclxuICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcbiAgei1pbmRleDogMTAwMDtcclxuICB3aWR0aDogNDgwcHg7XHJcbiAgbWF4LWhlaWdodDogOTB2aDtcclxuICBiYWNrZ3JvdW5kOiAjZmZmO1xyXG4gIGJvcmRlci1yYWRpdXM6IDEycHg7XHJcbiAgYm94LXNoYWRvdzogMCAyMHB4IDQwcHggcmdiYSgwLCAwLCAwLCAwLjE1KTtcclxuICBvdmVyZmxvdzogaGlkZGVuO1xyXG4gIGFuaW1hdGlvbjogc2xpZGVJbiAwLjNzIGVhc2Utb3V0O1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxufVxyXG5cclxuLnByZXZpZXctaGVhZGVyIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xyXG4gIHdpZHRoOiAxMDAlO1xyXG59XHJcblxyXG4gIC5wYW5lbC10aXRsZSB7XHJcbiAgICBmb250LXNpemU6IDEuNXJlbTtcclxuICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICBjb2xvcjogIzFhMWQyOTtcclxuICAgIG1hcmdpbjogMDtcclxuICB9XHJcbiAgXHJcbiAgLmNsb3NlLWJ0biB7XHJcbiAgICB3aWR0aDogMzJweDtcclxuICAgIGhlaWdodDogMzJweDtcclxuICAgIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50O1xyXG4gICAgYm9yZGVyOiBub25lO1xyXG4gICAgY29sb3I6ICM2YjcyODA7XHJcbiAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICBib3JkZXItcmFkaXVzOiA4cHg7XHJcbiAgICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlO1xyXG4gICAgZGlzcGxheTogZmxleDtcclxuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuXHJcbiAgICAmOmhvdmVyIHtcclxuICAgICAgYmFja2dyb3VuZDogI2YzZjRmNjtcclxuICAgICAgY29sb3I6ICMzNzQxNTE7XHJcbiAgICB9XHJcbiAgfVxyXG4vLyBDb250ZW50IGFyZWFcclxuLnByZXZpZXctY29udGVudCB7XHJcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gIGZsZXg6IDE7XHJcbiAgb3ZlcmZsb3cteTogYXV0bztcclxuICBvdmVyZmxvdy14OiBoaWRkZW47IC8qIFByZXZlbnQgaG9yaXpvbnRhbCBvdmVyZmxvdyAqL1xyXG4gIG1heC1oZWlnaHQ6IGNhbGMoOTB2aCAtIDE0MHB4KTsgIFxyXG4gIHBhZGRpbmctdG9wOjQwcHg7XHJcbiAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNlNWU3ZWI7XHJcbiAgJjo6LXdlYmtpdC1zY3JvbGxiYXIge1xyXG4gICAgd2lkdGg6IDZweDtcclxuICB9XHJcblxyXG4gICY6Oi13ZWJraXQtc2Nyb2xsYmFyLXRyYWNrIHtcclxuICAgIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC4xKTtcclxuICAgIGJvcmRlci1yYWRpdXM6IDNweDtcclxuICB9XHJcblxyXG4gICY6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHtcclxuICAgIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC4zKTtcclxuICAgIGJvcmRlci1yYWRpdXM6IDNweDtcclxuXHJcbiAgICAmOmhvdmVyIHtcclxuICAgICAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjUpO1xyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLy8gQ29tbW9uIGZpZWxkIHN0eWxpbmdcclxuLmNvbmZpZy1maWVsZCB7XHJcbiAgbWFyZ2luLWJvdHRvbTogMjBweDtcclxuICBcclxuICBsYWJlbCB7XHJcbiAgICBkaXNwbGF5OiBibG9jaztcclxuICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgY29sb3I6ICMxYTFkMjk7XHJcbiAgICBtYXJnaW4tYm90dG9tOiA4cHg7XHJcbiAgfVxyXG4gIFxyXG4gIC5maWVsZC12YWx1ZSB7XHJcbiAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgY29sb3I6ICMxYTFkMjk7XHJcbiAgICBsaW5lLWhlaWdodDogMS41O1xyXG4gIH1cclxuICBcclxuICAuZmllbGQtaGludCB7XHJcbiAgICBmb250LXNpemU6IDAuNzVyZW07XHJcbiAgICBjb2xvcjogIzZiNzI4MDtcclxuICAgIG1hcmdpbi10b3A6IDRweDtcclxuICB9XHJcbn1cclxuXHJcbi8vIENvZGUgY29udGVudCBzdHlsaW5nXHJcbi5jb2RlLWNvbnRlbnQsIC5wcm9tcHQtY29udGVudCB7XHJcbiAgcGFkZGluZzogMTZweDtcclxuICBiYWNrZ3JvdW5kOiAjZjhmOWZhO1xyXG4gIGJvcmRlcjogMXB4IHNvbGlkICNlNWU3ZWI7XHJcbiAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG4gIGNvbG9yOiAjMWExZDI5O1xyXG4gIGZvbnQtZmFtaWx5OiAnU0YgTW9ubycsIE1vbmFjbywgJ0Nhc2NhZGlhIENvZGUnLCBtb25vc3BhY2U7XHJcbiAgZm9udC1zaXplOiAwLjg3NXJlbTtcclxuICBsaW5lLWhlaWdodDogMS42O1xyXG4gIG1pbi1oZWlnaHQ6IDEyMHB4O1xyXG4gIHdoaXRlLXNwYWNlOiBwcmUtd3JhcDtcclxuICBvdmVyZmxvdy15OiBhdXRvO1xyXG4gIG92ZXJmbG93LXg6IGhpZGRlbjtcclxuICBzY3JvbGxiYXItd2lkdGg6IG5vbmU7XHJcbiAgLW1zLW92ZXJmbG93LXN0eWxlOiBub25lO1xyXG4gIFxyXG4gICY6Oi13ZWJraXQtc2Nyb2xsYmFyIHtcclxuICAgIGRpc3BsYXk6IG5vbmU7XHJcbiAgfVxyXG4gIFxyXG4gICY6ZW1wdHk6OmJlZm9yZSB7XHJcbiAgICBjb250ZW50OiAnTm8gY29uZmlndXJhdGlvbiBhdmFpbGFibGUnO1xyXG4gICAgY29sb3I6ICM5Y2EzYWY7XHJcbiAgICBmb250LXN0eWxlOiBpdGFsaWM7XHJcbiAgfVxyXG59XHJcblxyXG4vLyBNb2RlbCBwcmV2aWV3IHNwZWNpZmljXHJcbi5tb2RlbC1wcmV2aWV3IHtcclxuICAubW9kZWwtc2VjdGlvbiB7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAzMnB4O1xyXG4gICAgXHJcbiAgICBoMyB7XHJcbiAgICAgIGZvbnQtc2l6ZTogMS4yNXJlbTtcclxuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgICAgY29sb3I6ICMxYTFkMjk7XHJcbiAgICAgIG1hcmdpbjogMCAwIDI0cHggMDtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC5jb25maWctcm93IHtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBnYXA6IDEycHg7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAyMHB4O1xyXG4gICAgXHJcbiAgICAuY29uZmlnLWZpZWxkLmhhbGYtd2lkdGgge1xyXG4gICAgICBmbGV4OiAxO1xyXG4gICAgICBtYXJnaW4tYm90dG9tOiAwO1xyXG4gICAgICBtaW4td2lkdGg6IDA7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAubW9kZWwtbWV0YSB7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAzMnB4O1xyXG4gICAgcGFkZGluZy1ib3R0b206IDI0cHg7XHJcbiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U1ZTdlYjtcclxuICAgIFxyXG4gICAgLm1ldGEtcm93IHtcclxuICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgZ2FwOiAxMHJlbTtcclxuICAgICAgXHJcbiAgICAgIC5tZXRhLWl0ZW0ge1xyXG4gICAgICAgIGxhYmVsIHtcclxuICAgICAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgICAgICBjb2xvcjogIzFhMWQyOTtcclxuICAgICAgICAgIG1hcmdpbi1ib3R0b206IDRweDtcclxuICAgICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIFxyXG4gICAgICAgIC5maWVsZC12YWx1ZSB7XHJcbiAgICAgICAgICBmb250LXdlaWdodDogNTAwO1xyXG4gICAgICAgICAgY29sb3I6ICMxYTFkMjk7XHJcbiAgICAgICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLy8gVG9vbCBhbmQgcHJvbXB0IHByZXZpZXdcclxuLnRvb2wtcHJldmlldywgLnByb21wdC1wcmV2aWV3IHtcclxuICAubW9kZWwtc2VjdGlvbiB7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAzMnB4O1xyXG4gICAgXHJcbiAgICBoMyB7XHJcbiAgICAgIGZvbnQtc2l6ZTogMS4yNXJlbTtcclxuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgICAgY29sb3I6ICMxYTFkMjk7XHJcbiAgICAgIG1hcmdpbjogMCAwIDI0cHggMDtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC5tb2RlbC1maWVsZCB7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAyNHB4O1xyXG4gICAgXHJcbiAgICBsYWJlbCB7XHJcbiAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgIGNvbG9yOiAjMWExZDI5O1xyXG4gICAgICBtYXJnaW4tYm90dG9tOiA4cHg7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIC5maWVsZC12YWx1ZSB7XHJcbiAgICAgIGZvbnQtc2l6ZTogMS4xMjVyZW07XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgIGNvbG9yOiAjMWExZDI5O1xyXG4gICAgICBsaW5lLWhlaWdodDogMS41O1xyXG4gICAgICBcclxuICAgICAgJi5kZXNjcmlwdGlvbi10ZXh0IHtcclxuICAgICAgICBmb250LXdlaWdodDogNDAwO1xyXG4gICAgICAgIGNvbG9yOiAjNmI3MjgwO1xyXG4gICAgICAgIGxpbmUtaGVpZ2h0OiAxLjY7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcblxyXG4gIC5pbnB1dC1kaXNwbGF5IHtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xyXG4gICAgcGFkZGluZzogMTJweCAxNnB4O1xyXG4gICAgYmFja2dyb3VuZDogI2Y4ZjlmYTtcclxuICAgIGJvcmRlcjogMXB4IHNvbGlkICNlNWU3ZWI7XHJcbiAgICBib3JkZXItcmFkaXVzOiA4cHg7XHJcbiAgICBjb2xvcjogIzFhMWQyOTtcclxuICAgIG1pbi1oZWlnaHQ6IDQ4cHg7XHJcbiAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gIH1cclxufVxyXG5cclxuLy8gRm9vdGVyXHJcbjo6bmctZGVlcCAucHJldmlldy1wYW5lbCBbcGFuZWwtZm9vdGVyXSB7XHJcbiAgcGFkZGluZzogMjBweCAyNHB4IDI0cHggMjRweDtcclxuICBib3JkZXItdG9wOiAxcHggc29saWQgI2YwZjFmMjtcclxuICBiYWNrZ3JvdW5kOiAjZmFmYmZjO1xyXG4gIFxyXG4gIGF2YS1idXR0b24ge1xyXG4gICAgd2lkdGg6IDEwMCU7XHJcbiAgICBcclxuICAgIGJ1dHRvbiB7XHJcbiAgICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgICBoZWlnaHQ6IDQ0cHg7XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICAgICAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZTtcclxuICAgICAgXHJcbiAgICAgICY6aG92ZXIge1xyXG4gICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTtcclxuICAgICAgICAvLyBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMzcsIDk5LCAyMzUsIDAuMyk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi8vIExvYWRpbmcgYW5kIGVycm9yIHN0YXRlc1xyXG4ucHJldmlldy1sb2FkaW5nIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICBwYWRkaW5nOiA0MHB4IDIwcHg7XHJcbiAgXHJcbiAgLmxvYWRpbmctc3Bpbm5lciB7XHJcbiAgICB3aWR0aDogMzJweDtcclxuICAgIGhlaWdodDogMzJweDtcclxuICAgIGJvcmRlcjogM3B4IHNvbGlkICNmM2Y0ZjY7XHJcbiAgICBib3JkZXItdG9wOiAzcHggc29saWQgIzNiODJmNjtcclxuICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcclxuICAgIGFuaW1hdGlvbjogc3BpbiAxcyBsaW5lYXIgaW5maW5pdGU7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAxNnB4O1xyXG4gIH1cclxuICBcclxuICBwIHtcclxuICAgIGNvbG9yOiAjNmI3MjgwO1xyXG4gICAgZm9udC1zaXplOiAwLjg3NXJlbTtcclxuICB9XHJcbn1cclxuXHJcbi5wcmV2aWV3LWVycm9yIHtcclxuICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcbiAgY29sb3I6ICNkYzI2MjY7XHJcbiAgcGFkZGluZzogNDBweCAyMHB4O1xyXG59XHJcblxyXG5Aa2V5ZnJhbWVzIHNwaW4ge1xyXG4gIDAlIHsgdHJhbnNmb3JtOiByb3RhdGUoMGRlZyk7IH1cclxuICAxMDAlIHsgdHJhbnNmb3JtOiByb3RhdGUoMzYwZGVnKTsgfVxyXG59XHJcblxyXG4vLyBSZXNwb25zaXZlIGRlc2lnblxyXG5AbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcclxuICAucGFuZWwtY29udGFpbmVyIHtcclxuICAgIHdpZHRoOiA5NXZ3O1xyXG4gICAgbWF4LXdpZHRoOiA0MDBweDtcclxuICB9XHJcbiAgXHJcbiAgLnBhbmVsLXRpdGxlIHtcclxuICAgIGZvbnQtc2l6ZTogMS4yNXJlbTtcclxuICB9XHJcbiAgXHJcbn1cclxuXHJcblxyXG4vLyBUeXBlLXNwZWNpZmljIHN0eWxpbmdcclxuLm1vZGVsLXByZXZpZXcgLnByZXZpZXctZmllbGQge1xyXG4gIGJvcmRlci1sZWZ0LWNvbG9yOiAjMTBiOTgxO1xyXG59XHJcblxyXG4udG9vbC1wcmV2aWV3IC5wcmV2aWV3LWZpZWxkIHtcclxuICBib3JkZXItbGVmdC1jb2xvcjogI2Y1OWUwYjtcclxufVxyXG5cclxuLnByb21wdC1wcmV2aWV3IC5wcmV2aWV3LWZpZWxkIHtcclxuICBib3JkZXItbGVmdC1jb2xvcjogIzhiNWNmNjtcclxufVxyXG5cclxuLmtub3dsZWRnZS1wcmV2aWV3IC5wcmV2aWV3LWZpZWxkIHtcclxuICBib3JkZXItbGVmdC1jb2xvcjogIzA2YjZkNDtcclxufVxyXG5cclxuLmd1YXJkcmFpbC1wcmV2aWV3IC5wcmV2aWV3LWZpZWxkIHtcclxuICBib3JkZXItbGVmdC1jb2xvcjogI2VmNDQ0NDtcclxufVxyXG5cclxuLm1vZGVsLXByZXZpZXcge1xyXG4gIGhlaWdodDogMTAwJTtcclxuICBvdmVyZmxvdy15OiBhdXRvO1xyXG4gIG92ZXJmbG93LXg6IGhpZGRlbjsgLyogUHJldmVudCBob3Jpem9udGFsIG92ZXJmbG93ICovXHJcbiAgcGFkZGluZy1ib3R0b206IDIwcHg7XHJcbiAgd2lkdGg6IDEwMCU7XHJcbiAgYm94LXNpemluZzogYm9yZGVyLWJveDtcclxuICBcclxuICAubW9kZWwtc2VjdGlvbiB7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAzMnB4O1xyXG4gICAgXHJcbiAgICBoMyB7XHJcbiAgICAgIGZvbnQtc2l6ZTogMS4yNXJlbTtcclxuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgICAgY29sb3I6ICMxYTFkMjk7XHJcbiAgICAgIG1hcmdpbjogMCAwIDI0cHggMDtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC5jb25maWctcm93IHtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBnYXA6IDEycHg7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAyMHB4O1xyXG4gICAgd2lkdGg6IDEwMCU7XHJcbiAgICBcclxuICAgIC5jb25maWctZmllbGQuaGFsZi13aWR0aCB7XHJcbiAgICAgIGZsZXg6IDE7XHJcbiAgICAgIG1hcmdpbi1ib3R0b206IDA7XHJcbiAgICAgIG1pbi13aWR0aDogMDtcclxuICAgICAgXHJcbiAgICAgIDo6bmctZGVlcCBhdmEtdGV4dGJveCB7XHJcbiAgICAgICAgd2lkdGg6IDEwMCU7XHJcbiAgICAgICAgXHJcbiAgICAgICAgLnRleHRib3gtY29udGFpbmVyIHtcclxuICAgICAgICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgICAgICAgXHJcbiAgICAgICAgICBpbnB1dCB7XHJcbiAgICAgICAgICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgICAgICAgICBib3gtc2l6aW5nOiBib3JkZXItYm94O1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLmNvbmZpZy1maWVsZCB7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAyMHB4O1xyXG4gICAgd2lkdGg6IDEwMCU7XHJcbiAgICBib3gtc2l6aW5nOiBib3JkZXItYm94O1xyXG4gICAgXHJcbiAgICBsYWJlbCB7XHJcbiAgICAgIGRpc3BsYXk6IGJsb2NrO1xyXG4gICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICBjb2xvcjogIzFhMWQyOTtcclxuICAgICAgbWFyZ2luLWJvdHRvbTogOHB4O1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICAuZmllbGQtaGludCB7XHJcbiAgICAgIGZvbnQtc2l6ZTogMC43NXJlbTtcclxuICAgICAgY29sb3I6ICM2YjcyODA7XHJcbiAgICAgIG1hcmdpbi10b3A6IDRweDtcclxuICAgIH1cclxuICAgIFxyXG4gICAgOjpuZy1kZWVwIGF2YS10ZXh0Ym94IHtcclxuICAgICAgd2lkdGg6IDEwMCU7XHJcbiAgICAgIFxyXG4gICAgICAudGV4dGJveC1jb250YWluZXIge1xyXG4gICAgICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgICAgIFxyXG4gICAgICAgIGlucHV0IHtcclxuICAgICAgICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgICAgICAgYm94LXNpemluZzogYm9yZGVyLWJveDtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcblxyXG4gIC5tb2RlbC1maWVsZCB7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAyNHB4O1xyXG4gICAgXHJcbiAgICBsYWJlbCB7XHJcbiAgICAgIGRpc3BsYXk6IGJsb2NrO1xyXG4gICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICBmb250LXdlaWdodDogNTAwO1xyXG4gICAgICBjb2xvcjogIzM3NDE1MTtcclxuICAgICAgbWFyZ2luLWJvdHRvbTogOHB4O1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICAuZmllbGQtdmFsdWUge1xyXG4gICAgICBmb250LXNpemU6IDEuMTI1cmVtO1xyXG4gICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICBjb2xvcjogIzFhMWQyOTtcclxuICAgICAgbGluZS1oZWlnaHQ6IDEuNTtcclxuICAgICAgXHJcbiAgICAgICYuZGVzY3JpcHRpb24tdGV4dCB7XHJcbiAgICAgICAgZm9udC13ZWlnaHQ6IDQwMDtcclxuICAgICAgICBjb2xvcjogIzZiNzI4MDtcclxuICAgICAgICBsaW5lLWhlaWdodDogMS42O1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAubW9kZWwtbWV0YSB7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAzMnB4O1xyXG4gICAgcGFkZGluZy1ib3R0b206IDI0cHg7XHJcbiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U1ZTdlYjtcclxuICAgIFxyXG4gICAgLm1ldGEtcm93IHtcclxuICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgZ2FwOiAxMHJlbTtcclxuICAgICAgXHJcbiAgICAgIC5tZXRhLWl0ZW0ge1xyXG4gICAgICAgIGxhYmVsIHtcclxuICAgICAgICAgIGRpc3BsYXk6IGJsb2NrO1xyXG4gICAgICAgICAgZm9udC1zaXplOiAwLjg3NXJlbTtcclxuICAgICAgICAgIGNvbG9yOiAjNmI3MjgwO1xyXG4gICAgICAgICAgbWFyZ2luLWJvdHRvbTogNHB4O1xyXG4gICAgICAgICAgZm9udC13ZWlnaHQ6IDQwMDtcclxuICAgICAgICB9XHJcbiAgICAgICAgXHJcbiAgICAgICAgLmZpZWxkLXZhbHVlIHtcclxuICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICAgICAgICBjb2xvcjogIzFhMWQyOTtcclxuICAgICAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAuY29uZmlnLWZpZWxkIHtcclxuICAgIG1hcmdpbi1ib3R0b206IDIwcHg7XHJcbiAgICBcclxuICAgIGxhYmVsIHtcclxuICAgICAgZGlzcGxheTogYmxvY2s7XHJcbiAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICAgIGNvbG9yOiAjMzc0MTUxO1xyXG4gICAgICBtYXJnaW4tYm90dG9tOiA4cHg7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIC5kcm9wZG93bi1kaXNwbGF5LFxyXG4gICAgLmlucHV0LWRpc3BsYXkge1xyXG4gICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XHJcbiAgICAgIHBhZGRpbmc6IDEycHggMTZweDtcclxuICAgICAgYmFja2dyb3VuZDogI2Y4ZjlmYTtcclxuICAgICAgYm9yZGVyOiAxcHggc29saWQgI2U1ZTdlYjtcclxuICAgICAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG4gICAgICBjb2xvcjogIzFhMWQyOTtcclxuICAgICAgbWluLWhlaWdodDogNDhweDtcclxuICAgICAgYm94LXNpemluZzogYm9yZGVyLWJveDtcclxuICAgICAgZm9udC1zaXplOiAwLjg3NXJlbTtcclxuICAgIH1cclxuICAgIFxyXG4gICAgLmRyb3Bkb3duLWRpc3BsYXkge1xyXG4gICAgICAmOjphZnRlciB7XHJcbiAgICAgICAgY29udGVudDogJyc7XHJcbiAgICAgICAgd2lkdGg6IDA7XHJcbiAgICAgICAgaGVpZ2h0OiAwO1xyXG4gICAgICAgIGJvcmRlci1sZWZ0OiA2cHggc29saWQgdHJhbnNwYXJlbnQ7XHJcbiAgICAgICAgYm9yZGVyLXJpZ2h0OiA2cHggc29saWQgdHJhbnNwYXJlbnQ7XHJcbiAgICAgICAgYm9yZGVyLXRvcDogOHB4IHNvbGlkICM2YjcyODA7XHJcbiAgICAgICAgbWFyZ2luLWxlZnQ6IDhweDtcclxuICAgICAgfVxyXG4gICAgICBcclxuICAgICAgLmVuZ2luZS1pY29uIHtcclxuICAgICAgICBtYXJnaW4tcmlnaHQ6IDhweDtcclxuICAgICAgICBmb250LXNpemU6IDFyZW07XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAvLyBUZW1wZXJhdHVyZSBzbGlkZXIgc3R5bGluZ1xyXG4gICAgOjpuZy1kZWVwIGF2YS1zbGlkZXIge1xyXG4gICAgICAuc2xpZGVyLWNvbnRhaW5lciB7XHJcbiAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICAgIGdhcDogMTZweDtcclxuICAgICAgICBcclxuICAgICAgICAuc2xpZGVyLXRyYWNrIHtcclxuICAgICAgICAgIGZsZXg6IDE7XHJcbiAgICAgICAgICBoZWlnaHQ6IDZweDtcclxuICAgICAgICAgIGJhY2tncm91bmQ6ICNlNWU3ZWI7XHJcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiAzcHg7XHJcbiAgICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcbiAgICAgICAgICBcclxuICAgICAgICAgIC5zbGlkZXItZmlsbCB7XHJcbiAgICAgICAgICAgIGhlaWdodDogMTAwJTtcclxuICAgICAgICAgICAgYmFja2dyb3VuZDogIzNiODJmNjtcclxuICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogM3B4O1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgICBcclxuICAgICAgICAuc2xpZGVyLXRodW1iIHtcclxuICAgICAgICAgIHdpZHRoOiAyMHB4O1xyXG4gICAgICAgICAgaGVpZ2h0OiAyMHB4O1xyXG4gICAgICAgICAgYmFja2dyb3VuZDogIzNiODJmNjtcclxuICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcclxuICAgICAgICAgIGN1cnNvcjogcG9pbnRlcjtcclxuICAgICAgICAgIGJveC1zaGFkb3c6IDAgMnB4IDRweCByZ2JhKDAsIDAsIDAsIDAuMik7XHJcbiAgICAgICAgICBib3JkZXI6IG5vbmU7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIFxyXG4gICAgICAgIC5zbGlkZXItdmFsdWUge1xyXG4gICAgICAgICAgbWluLXdpZHRoOiA2MHB4O1xyXG4gICAgICAgICAgcGFkZGluZzogOHB4IDEycHg7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kOiAjZjhmOWZhO1xyXG4gICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2U1ZTdlYjtcclxuICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDZweDtcclxuICAgICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICAgICAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgICAgICBjb2xvcjogIzM3NDE1MTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcblxyXG4gIC5jb25maWctdG9nZ2xlIHtcclxuICAgIG1hcmdpbjogMTZweCAwO1xyXG4gICAgXHJcbiAgICAudG9nZ2xlLXRleHQge1xyXG4gICAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7XHJcbiAgICAgIGN1cnNvcjogcG9pbnRlcjtcclxuICAgICAgZm9udC1zaXplOiAxNHB4O1xyXG4gICAgICBjb2xvcjogIzAwN2JmZjtcclxuICAgICAgdHJhbnNpdGlvbjogY29sb3IgMC4ycyBlYXNlO1xyXG5cclxuICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgY29sb3I6ICMwMDU2YjM7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8vIC5jb25maWctZGV0YWlscyB7XHJcbiAgLy8gICBtYXgtaGVpZ2h0OiBub25lOyAvLyBSZW1vdmUgaGVpZ2h0IHJlc3RyaWN0aW9uXHJcbiAgLy8gICBvdmVyZmxvdzogdmlzaWJsZTtcclxuICAvLyAgIHRyYW5zaXRpb246IG1heC1oZWlnaHQgMC4zcyBlYXNlO1xyXG5cclxuICAvLyAgICYuZXhwYW5kZWQge1xyXG4gIC8vICAgICBtYXgtaGVpZ2h0OiBub25lOyAvLyBSZW1vdmUgaGVpZ2h0IHJlc3RyaWN0aW9uIHdoZW4gZXhwYW5kZWRcclxuICAvLyAgIH1cclxuICAvLyB9XHJcbn1cclxuLmNvbmZpZy10b2dnbGUge1xyXG4gIG1hcmdpbjogMTZweCAwO1xyXG4gIFxyXG4gIC50b2dnbGUtdGV4dCB7XHJcbiAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7XHJcbiAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICBmb250LXNpemU6IDE0cHg7XHJcbiAgICBjb2xvcjogIzAwN2JmZjtcclxuICAgIHRyYW5zaXRpb246IGNvbG9yIDAuMnMgZWFzZTtcclxuXHJcbiAgICAmOmhvdmVyIHtcclxuICAgICAgY29sb3I6ICMwMDU2YjM7XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4uY29uZmlnLWRldGFpbHMge1xyXG4gIG1heC1oZWlnaHQ6IDA7XHJcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuICB0cmFuc2l0aW9uOiBtYXgtaGVpZ2h0IDAuM3MgZWFzZTtcclxuXHJcbiAgJi5leHBhbmRlZCB7XHJcbiAgICBtYXgtaGVpZ2h0OiA1MDBweDtcclxuICB9XHJcbn1cclxuXHJcbi5wcm9tcHQtcHJldmlldyB7XHJcbiAgLm1vZGVsLXNlY3Rpb24ge1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMzJweDtcclxuICAgIFxyXG4gICAgaDMge1xyXG4gICAgICBmb250LXNpemU6IDEuMjVyZW07XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgIGNvbG9yOiAjMWExZDI5O1xyXG4gICAgICBtYXJnaW46IDAgMCAyNHB4IDA7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAubW9kZWwtZmllbGQge1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMjRweDtcclxuICAgIFxyXG4gICAgbGFiZWwge1xyXG4gICAgICBkaXNwbGF5OiBibG9jaztcclxuICAgICAgZm9udC1zaXplOiAwLjg3NXJlbTtcclxuICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgICAgY29sb3I6ICMzNzQxNTE7XHJcbiAgICAgIG1hcmdpbi1ib3R0b206IDhweDtcclxuICAgIH1cclxuICAgIFxyXG4gICAgLmZpZWxkLXZhbHVlIHtcclxuICAgICAgZm9udC1zaXplOiAxLjEyNXJlbTtcclxuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgICAgY29sb3I6ICMxYTFkMjk7XHJcbiAgICAgIGxpbmUtaGVpZ2h0OiAxLjU7XHJcbiAgICAgIFxyXG4gICAgICAmLmRlc2NyaXB0aW9uLXRleHQge1xyXG4gICAgICAgIGZvbnQtd2VpZ2h0OiA0MDA7XHJcbiAgICAgICAgY29sb3I6ICM2YjcyODA7XHJcbiAgICAgICAgbGluZS1oZWlnaHQ6IDEuNjtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLm1vZGVsLW1ldGEge1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMzJweDtcclxuICAgIHBhZGRpbmctYm90dG9tOiAyNHB4O1xyXG4gICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlNWU3ZWI7XHJcbiAgICBcclxuICAgIC5tZXRhLXJvdyB7XHJcbiAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgIGdhcDogMTByZW07XHJcbiAgICAgIFxyXG4gICAgICAubWV0YS1pdGVtIHtcclxuICAgICAgICBsYWJlbCB7XHJcbiAgICAgICAgICBkaXNwbGF5OiBibG9jaztcclxuICAgICAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgICAgICBjb2xvcjogIzZiNzI4MDtcclxuICAgICAgICAgIG1hcmdpbi1ib3R0b206IDRweDtcclxuICAgICAgICAgIGZvbnQtd2VpZ2h0OiA0MDA7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIFxyXG4gICAgICAgIC5maWVsZC12YWx1ZSB7XHJcbiAgICAgICAgICBmb250LXdlaWdodDogNTAwO1xyXG4gICAgICAgICAgY29sb3I6ICMxYTFkMjk7XHJcbiAgICAgICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLmNvbmZpZy1maWVsZCB7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAyMHB4O1xyXG4gICAgXHJcbiAgICBsYWJlbCB7XHJcbiAgICAgIGRpc3BsYXk6IGJsb2NrO1xyXG4gICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICBmb250LXdlaWdodDogNTAwO1xyXG4gICAgICBjb2xvcjogIzM3NDE1MTtcclxuICAgICAgbWFyZ2luLWJvdHRvbTogOHB4O1xyXG4gICAgICBcclxuICAgICAgLnJlcXVpcmVkIHtcclxuICAgICAgICBjb2xvcjogI2VmNDQ0NDtcclxuICAgICAgICBtYXJnaW4tbGVmdDogMnB4O1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgICBcclxuICAgIC5wcm9tcHQtY29udGVudCB7XHJcbiAgICAgIHBhZGRpbmc6IDE2cHg7XHJcbiAgICAgIGJhY2tncm91bmQ6ICNmOGY5ZmE7XHJcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNlNWU3ZWI7XHJcbiAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICAgICAgY29sb3I6ICMxYTFkMjk7XHJcbiAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgIGxpbmUtaGVpZ2h0OiAxLjY7XHJcbiAgICAgIG1pbi1oZWlnaHQ6IDEyMHB4O1xyXG4gICAgICB3aGl0ZS1zcGFjZTogcHJlLXdyYXA7XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4uZ3VhcmRyYWlsLXByZXZpZXcge1xyXG4gIC5tb2RlbC1zZWN0aW9uIHtcclxuICAgIG1hcmdpbi1ib3R0b206IDMycHg7XHJcbiAgICBcclxuICAgIGgzIHtcclxuICAgICAgZm9udC1zaXplOiAxLjI1cmVtO1xyXG4gICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICBjb2xvcjogIzFhMWQyOTtcclxuICAgICAgbWFyZ2luOiAwIDAgMjRweCAwO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLm1vZGVsLWZpZWxkIHtcclxuICAgIG1hcmdpbi1ib3R0b206IDI0cHg7XHJcbiAgICBcclxuICAgIGxhYmVsIHtcclxuICAgICAgZGlzcGxheTogYmxvY2s7XHJcbiAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgIGNvbG9yOiAjMWExZDI5O1xyXG4gICAgICBtYXJnaW4tYm90dG9tOiA4cHg7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIC5maWVsZC12YWx1ZSB7XHJcbiAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA0MDA7XHJcbiAgICAgIGNvbG9yOiAjMWExZDI5O1xyXG4gICAgICBsaW5lLWhlaWdodDogMS41O1xyXG4gICAgICBcclxuICAgICAgJi5kZXNjcmlwdGlvbi10ZXh0IHtcclxuICAgICAgICBmb250LXdlaWdodDogNDAwO1xyXG4gICAgICAgIGNvbG9yOiAjNmI3MjgwO1xyXG4gICAgICAgIGxpbmUtaGVpZ2h0OiAxLjY7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcblxyXG4gIC5tb2RlbC1tZXRhIHtcclxuICAgIG1hcmdpbi1ib3R0b206IDMycHg7XHJcbiAgICBwYWRkaW5nLWJvdHRvbTogMjRweDtcclxuICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTVlN2ViO1xyXG4gICAgXHJcbiAgICAubWV0YS1yb3cge1xyXG4gICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICBnYXA6IDEwcmVtO1xyXG4gICAgICBcclxuICAgICAgLm1ldGEtaXRlbSB7XHJcbiAgICAgICAgbGFiZWwge1xyXG4gICAgICAgICAgZGlzcGxheTogYmxvY2s7XHJcbiAgICAgICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICAgICAgY29sb3I6ICMxYTFkMjk7XHJcbiAgICAgICAgICBtYXJnaW4tYm90dG9tOiA0cHg7XHJcbiAgICAgICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICAgIH1cclxuICAgICAgICBcclxuICAgICAgICAuZmllbGQtdmFsdWUge1xyXG4gICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgICAgICAgIGNvbG9yOiAjMWExZDI5O1xyXG4gICAgICAgICAgZm9udC1zaXplOiAwLjg3NXJlbTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcblxyXG4gIC5jb25maWctZmllbGQge1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMjBweDtcclxuICAgIFxyXG4gICAgbGFiZWwge1xyXG4gICAgICBkaXNwbGF5OiBibG9jaztcclxuICAgICAgZm9udC1zaXplOiAwLjg3NXJlbTtcclxuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgICAgY29sb3I6ICMxYTFkMjk7XHJcbiAgICAgIG1hcmdpbi1ib3R0b206IDhweDtcclxuICAgIH1cclxuICAgIFxyXG4gICAgLmNvZGUtY29udGVudCB7XHJcbiAgICAgIHBhZGRpbmc6IDE2cHg7XHJcbiAgICAgIGJhY2tncm91bmQ6ICNmOGY5ZmE7XHJcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNlNWU3ZWI7XHJcbiAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICAgICAgY29sb3I6ICMxYTFkMjk7XHJcbiAgICAgIGZvbnQtZmFtaWx5OiAnU0YgTW9ubycsIE1vbmFjbywgJ0Nhc2NhZGlhIENvZGUnLCBtb25vc3BhY2U7XHJcbiAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgIGxpbmUtaGVpZ2h0OiAxLjY7XHJcbiAgICAgIG1pbi1oZWlnaHQ6IDIwMHB4O1xyXG4gICAgICB3aGl0ZS1zcGFjZTogcHJlLXdyYXA7XHJcbiAgICAgIG92ZXJmbG93LXk6IGF1dG87XHJcbiAgICAgIG1heC1oZWlnaHQ6IDQwMHB4O1xyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLmtub3dsZWRnZS1wcmV2aWV3IHtcclxuICAubW9kZWwtc2VjdGlvbiB7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAzMnB4O1xyXG4gICAgXHJcbiAgICBoMyB7XHJcbiAgICAgIGZvbnQtc2l6ZTogMS4yNXJlbTtcclxuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgICAgY29sb3I6ICMxYTFkMjk7XHJcbiAgICAgIG1hcmdpbjogMCAwIDI0cHggMDtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC5tb2RlbC1maWVsZCB7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAyNHB4O1xyXG4gICAgXHJcbiAgICBsYWJlbCB7XHJcbiAgICAgIGRpc3BsYXk6IGJsb2NrO1xyXG4gICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICBjb2xvcjogIzFhMWQyOTtcclxuICAgICAgbWFyZ2luLWJvdHRvbTogOHB4O1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICAuZmllbGQtdmFsdWUge1xyXG4gICAgICBmb250LXNpemU6IDEuMTI1cmVtO1xyXG4gICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICBjb2xvcjogIzFhMWQyOTtcclxuICAgICAgbGluZS1oZWlnaHQ6IDEuNTtcclxuICAgICAgXHJcbiAgICAgICYuZGVzY3JpcHRpb24tdGV4dCB7XHJcbiAgICAgICAgZm9udC13ZWlnaHQ6IDQwMDtcclxuICAgICAgICBjb2xvcjogIzZiNzI4MDtcclxuICAgICAgICBsaW5lLWhlaWdodDogMS42O1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAubW9kZWwtbWV0YSB7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAzMnB4O1xyXG4gICAgcGFkZGluZy1ib3R0b206IDI0cHg7XHJcbiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U1ZTdlYjtcclxuICAgIFxyXG4gICAgLm1ldGEtcm93IHtcclxuICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgZ2FwOiAxMHJlbTtcclxuICAgICAgXHJcbiAgICAgIC5tZXRhLWl0ZW0ge1xyXG4gICAgICAgIGxhYmVsIHtcclxuICAgICAgICAgIGRpc3BsYXk6IGJsb2NrO1xyXG4gICAgICAgICAgZm9udC1zaXplOiAwLjg3NXJlbTtcclxuICAgICAgICAgIGNvbG9yOiAjMWExZDI5O1xyXG4gICAgICAgICAgbWFyZ2luLWJvdHRvbTogNHB4O1xyXG4gICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgICAgICB9XHJcbiAgICAgICAgXHJcbiAgICAgICAgLmZpZWxkLXZhbHVlIHtcclxuICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICAgICAgICBjb2xvcjogIzFhMWQyOTtcclxuICAgICAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAuY29uZmlnLWZpZWxkIHtcclxuICAgIG1hcmdpbi1ib3R0b206IDI0cHg7XHJcbiAgICBcclxuICAgIGxhYmVsIHtcclxuICAgICAgZGlzcGxheTogYmxvY2s7XHJcbiAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgIGNvbG9yOiAjMWExZDI5O1xyXG4gICAgICBtYXJnaW4tYm90dG9tOiA4cHg7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIC5kcm9wZG93bi1kaXNwbGF5LFxyXG4gICAgLmlucHV0LWRpc3BsYXkge1xyXG4gICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XHJcbiAgICAgIHBhZGRpbmc6IDEycHggMTZweDtcclxuICAgICAgYmFja2dyb3VuZDogI2Y4ZjlmYTtcclxuICAgICAgYm9yZGVyOiAxcHggc29saWQgI2U1ZTdlYjtcclxuICAgICAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG4gICAgICBjb2xvcjogIzFhMWQyOTtcclxuICAgICAgbWluLWhlaWdodDogNDhweDtcclxuICAgICAgYm94LXNpemluZzogYm9yZGVyLWJveDtcclxuICAgICAgZm9udC1zaXplOiAwLjg3NXJlbTtcclxuICAgIH1cclxuICAgIFxyXG4gICAgLmRyb3Bkb3duLWRpc3BsYXkge1xyXG4gICAgICAmOjphZnRlciB7XHJcbiAgICAgICAgY29udGVudDogJyc7XHJcbiAgICAgICAgd2lkdGg6IDA7XHJcbiAgICAgICAgaGVpZ2h0OiAwO1xyXG4gICAgICAgIGJvcmRlci1sZWZ0OiA2cHggc29saWQgdHJhbnNwYXJlbnQ7XHJcbiAgICAgICAgYm9yZGVyLXJpZ2h0OiA2cHggc29saWQgdHJhbnNwYXJlbnQ7XHJcbiAgICAgICAgYm9yZGVyLXRvcDogOHB4IHNvbGlkICM2YjcyODA7XHJcbiAgICAgICAgbWFyZ2luLWxlZnQ6IDhweDtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC5zbGlkZXItY29udGFpbmVyIHtcclxuICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgZ2FwOiAxNnB4O1xyXG4gICAgICBcclxuICAgICAgLnNsaWRlci10cmFjayB7XHJcbiAgICAgICAgZmxleDogMTtcclxuICAgICAgICBoZWlnaHQ6IDZweDtcclxuICAgICAgICBiYWNrZ3JvdW5kOiAjZTVlN2ViO1xyXG4gICAgICAgIGJvcmRlci1yYWRpdXM6IDNweDtcclxuICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcbiAgICAgICAgXHJcbiAgICAgICAgLnNsaWRlci1maWxsIHtcclxuICAgICAgICAgIGhlaWdodDogMTAwJTtcclxuICAgICAgICAgIGJhY2tncm91bmQ6ICMzYjgyZjY7XHJcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiAzcHg7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICAgIFxyXG4gICAgICAuc2xpZGVyLXZhbHVlIHtcclxuICAgICAgICBtaW4td2lkdGg6IDYwcHg7XHJcbiAgICAgICAgcGFkZGluZzogOHB4IDEycHg7XHJcbiAgICAgICAgYmFja2dyb3VuZDogI2Y4ZjlmYTtcclxuICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjZTVlN2ViO1xyXG4gICAgICAgIGJvcmRlci1yYWRpdXM6IDZweDtcclxuICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcbiAgICAgICAgZm9udC1zaXplOiAwLjg3NXJlbTtcclxuICAgICAgICBjb2xvcjogIzM3NDE1MTtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLmZpbGVzLWxpc3Qge1xyXG4gICAgLmZpbGUtaXRlbSB7XHJcbiAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgIGdhcDogOHB4O1xyXG4gICAgICBwYWRkaW5nOiA4cHggMTJweDtcclxuICAgICAgYmFja2dyb3VuZDogI2Y4ZjlmYTtcclxuICAgICAgYm9yZGVyLXJhZGl1czogNnB4O1xyXG4gICAgICBtYXJnaW4tYm90dG9tOiA4cHg7XHJcbiAgICAgIFxyXG4gICAgICAuZmlsZS1uYW1lIHtcclxuICAgICAgICBjb2xvcjogIzNiODJmNjtcclxuICAgICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi50b29sLXByZXZpZXcge1xyXG4gIC5tb2RlbC1zZWN0aW9uIHtcclxuICAgIG1hcmdpbi1ib3R0b206IDMycHg7XHJcbiAgICBcclxuICAgIGgzIHtcclxuICAgICAgZm9udC1zaXplOiAxLjI1cmVtO1xyXG4gICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICBjb2xvcjogIzFhMWQyOTtcclxuICAgICAgbWFyZ2luOiAwIDAgMjRweCAwO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLm1vZGVsLWZpZWxkIHtcclxuICAgIG1hcmdpbi1ib3R0b206IDI0cHg7XHJcbiAgICBcclxuICAgIGxhYmVsIHtcclxuICAgICAgZGlzcGxheTogYmxvY2s7XHJcbiAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgIGNvbG9yOiAjMWExZDI5O1xyXG4gICAgICBtYXJnaW4tYm90dG9tOiA4cHg7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIC5maWVsZC12YWx1ZSB7XHJcbiAgICAgIGZvbnQtc2l6ZTogMS4xMjVyZW07XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgIGNvbG9yOiAjMWExZDI5O1xyXG4gICAgICBsaW5lLWhlaWdodDogMS41O1xyXG4gICAgICBcclxuICAgICAgJi5kZXNjcmlwdGlvbi10ZXh0IHtcclxuICAgICAgICBmb250LXdlaWdodDogNDAwO1xyXG4gICAgICAgIGNvbG9yOiAjNmI3MjgwO1xyXG4gICAgICAgIGxpbmUtaGVpZ2h0OiAxLjY7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcblxyXG4gIC5tb2RlbC1tZXRhIHtcclxuICAgIG1hcmdpbi1ib3R0b206IDMycHg7XHJcbiAgICBwYWRkaW5nLWJvdHRvbTogMjRweDtcclxuICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTVlN2ViO1xyXG4gICAgXHJcbiAgICAubWV0YS1yb3cge1xyXG4gICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICBnYXA6IDEwcmVtO1xyXG4gICAgICBcclxuICAgICAgLm1ldGEtaXRlbSB7XHJcbiAgICAgICAgbGFiZWwge1xyXG4gICAgICAgICAgZGlzcGxheTogYmxvY2s7XHJcbiAgICAgICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICAgICAgY29sb3I6ICMxYTFkMjk7XHJcbiAgICAgICAgICBtYXJnaW4tYm90dG9tOiA0cHg7XHJcbiAgICAgICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICAgIH1cclxuICAgICAgICBcclxuICAgICAgICAuZmllbGQtdmFsdWUge1xyXG4gICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgICAgICAgIGNvbG9yOiAjMWExZDI5O1xyXG4gICAgICAgICAgZm9udC1zaXplOiAwLjg3NXJlbTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcblxyXG4gIC5jb25maWctZmllbGQge1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMjBweDtcclxuICAgIFxyXG4gICAgbGFiZWwge1xyXG4gICAgICBkaXNwbGF5OiBibG9jaztcclxuICAgICAgZm9udC1zaXplOiAwLjg3NXJlbTtcclxuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgICAgY29sb3I6ICMxYTFkMjk7XHJcbiAgICAgIG1hcmdpbi1ib3R0b206IDhweDtcclxuICAgIH1cclxuICAgIFxyXG4gICAgLmlucHV0LWRpc3BsYXkge1xyXG4gICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XHJcbiAgICAgIHBhZGRpbmc6IDEycHggMTZweDtcclxuICAgICAgYmFja2dyb3VuZDogI2Y4ZjlmYTtcclxuICAgICAgYm9yZGVyOiAxcHggc29saWQgI2U1ZTdlYjtcclxuICAgICAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG4gICAgICBjb2xvcjogIzFhMWQyOTtcclxuICAgICAgbWluLWhlaWdodDogNDhweDtcclxuICAgICAgYm94LXNpemluZzogYm9yZGVyLWJveDtcclxuICAgICAgZm9udC1zaXplOiAwLjg3NXJlbTtcclxuICAgIH1cclxuICAgIFxyXG4gICAgLmNvZGUtY29udGVudCB7XHJcbiAgICAgIHBhZGRpbmc6IDE2cHg7XHJcbiAgICAgIGJhY2tncm91bmQ6ICNmOGY5ZmE7XHJcbiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNlNWU3ZWI7XHJcbiAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICAgICAgY29sb3I6ICMxYTFkMjk7XHJcbiAgICAgIGZvbnQtZmFtaWx5OiAnU0YgTW9ubycsIE1vbmFjbywgJ0Nhc2NhZGlhIENvZGUnLCBtb25vc3BhY2U7XHJcbiAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgIGxpbmUtaGVpZ2h0OiAxLjY7XHJcbiAgICAgIG1pbi1oZWlnaHQ6IDIwMHB4O1xyXG4gICAgICB3aGl0ZS1zcGFjZTogcHJlLXdyYXA7XHJcbiAgICAgIFxyXG4gICAgICAvLyBBbHdheXMgc2hvdyB0aGUgYm9yZGVyIGFuZCBiYWNrZ3JvdW5kLCBldmVuIHdoZW4gZW1wdHlcclxuICAgICAgJjplbXB0eTo6YmVmb3JlIHtcclxuICAgICAgICBjb250ZW50OiAnTm8gY29uZmlndXJhdGlvbiBhdmFpbGFibGUnO1xyXG4gICAgICAgIGNvbG9yOiAjOWNhM2FmO1xyXG4gICAgICAgIGZvbnQtc3R5bGU6IGl0YWxpYztcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLy8gRW5zdXJlIHRoZSBwYW5lbCBmb290ZXIgaXMgcG9zaXRpb25lZCBjb3JyZWN0bHlcclxuW3BhbmVsLWZvb3Rlcl0ge1xyXG4gIHBvc2l0aW9uOiBzdGlja3k7XHJcbiAgYm90dG9tOiAwO1xyXG4gIGJhY2tncm91bmQ6IHdoaXRlO1xyXG4gIHBhZGRpbmc6IDE2cHg7XHJcbiAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNmMGYxZjI7XHJcbiAgbWFyZ2luLXRvcDogYXV0bztcclxufVxyXG5cclxuLmNvZGUtY29udGVudCB7XHJcbiAgLy8gSGlkZSBzY3JvbGxiYXIgd2hpbGUga2VlcGluZyBzY3JvbGwgZnVuY3Rpb25hbGl0eVxyXG4gIG92ZXJmbG93LXk6IGF1dG87XHJcbiAgb3ZlcmZsb3cteDogaGlkZGVuO1xyXG4gIHNjcm9sbGJhci13aWR0aDogbm9uZTsgLyogRmlyZWZveCAqL1xyXG4gIC1tcy1vdmVyZmxvdy1zdHlsZTogbm9uZTsgLyogSW50ZXJuZXQgRXhwbG9yZXIgMTArICovXHJcbiAgXHJcbiAgJjo6LXdlYmtpdC1zY3JvbGxiYXIge1xyXG4gICAgZGlzcGxheTogbm9uZTsgLyogQ2hyb21lLCBTYWZhcmksIEVkZ2UgKi9cclxuICB9XHJcbn1cclxuXHJcbi8vIFJlbW92ZSBzaGFkb3cgZnJvbSB0ZXh0Ym94ZXNcclxuLmNvbmZpZy1maWVsZCB7XHJcbiAgOjpuZy1kZWVwIGF2YS10ZXh0Ym94IHtcclxuICAgIC50ZXh0Ym94LWNvbnRhaW5lciB7XHJcbiAgICAgIGJveC1zaGFkb3c6IG5vbmUgIWltcG9ydGFudDtcclxuICAgICAgXHJcbiAgICAgIGlucHV0IHtcclxuICAgICAgICBib3gtc2hhZG93OiBub25lICFpbXBvcnRhbnQ7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcbn1cclxuOjpuZy1kZWVwIGFwcC1wcmV2aWV3LXBhbmVsIC5wcmV2aWV3LXBhbmVsID4gLnByZXZpZXctaGVhZGVyIHtcclxuICBwYWRkaW5nOiAyNHB4ICFpbXBvcnRhbnQ7XHJcbn1cclxuXHJcbjo6bmctZGVlcCBhcHAtcHJldmlldy1wYW5lbCAucHJldmlldy1wYW5lbCA+IC5wcmV2aWV3LWNvbnRlbnQge1xyXG4gIHBhZGRpbmc6IDAgMjRweCAhaW1wb3J0YW50O1xyXG59XHJcblxyXG5cclxuXHJcbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n  return AgentsPreviewPanelComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "DatePipe", "AvaTextboxComponent", "ButtonComponent", "IconComponent", "SliderComponent", "PreviewPanelComponent", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "previewData", "title", "data", "name", "modelDescription", "description", "created<PERSON>y", "ɵɵpipeBind2", "createdOn", "createdDate", "modelType", "ɵɵproperty", "temperature", "maxTokens", "topP", "ɵɵtemplate", "AgentsPreviewPanelComponent_div_9_div_1_div_15_div_1_Template", "AgentsPreviewPanelComponent_div_9_div_1_div_15_div_2_Template", "maxIteration", "aiEngine", "baseurl", "baseUrl", "llmDeploymentName", "<PERSON><PERSON><PERSON><PERSON>", "apiVersion", "AgentsPreviewPanelComponent_div_9_div_1_div_4_Template", "AgentsPreviewPanelComponent_div_9_div_1_div_5_Template", "AgentsPreviewPanelComponent_div_9_div_1_div_8_Template", "AgentsPreviewPanelComponent_div_9_div_1_div_9_Template", "AgentsPreviewPanelComponent_div_9_div_1_div_13_Template", "AgentsPreviewPanelComponent_div_9_div_1_div_14_Template", "AgentsPreviewPanelComponent_div_9_div_1_div_15_Template", "AgentsPreviewPanelComponent_div_9_div_1_div_16_Template", "ɵɵlistener", "AgentsPreviewPanelComponent_div_9_div_1_Template_span_click_18_listener", "ɵɵrestoreView", "_r1", "ɵɵnextContext", "ɵɵresetView", "toggleConfigDetails", "AgentsPreviewPanelComponent_div_9_div_1_div_21_Template", "AgentsPreviewPanelComponent_div_9_div_1_div_22_Template", "AgentsPreviewPanelComponent_div_9_div_1_div_23_Template", "AgentsPreviewPanelComponent_div_9_div_1_div_24_Template", "AgentsPreviewPanelComponent_div_9_div_1_div_25_Template", "AgentsPreviewPanelComponent_div_9_div_1_div_26_Template", "undefined", "ɵɵtextInterpolate1", "showMoreConfig", "ɵɵclassProp", "appDescription", "areaOfScope", "functionality", "content", "toolClassDef", "AgentsPreviewPanelComponent_div_9_div_2_div_4_Template", "AgentsPreviewPanelComponent_div_9_div_2_div_5_Template", "AgentsPreviewPanelComponent_div_9_div_2_div_6_Template", "AgentsPreviewPanelComponent_div_9_div_2_div_9_Template", "AgentsPreviewPanelComponent_div_9_div_2_div_10_Template", "AgentsPreviewPanelComponent_div_9_div_2_div_14_Template", "role", "goal", "backstory", "updatedAt", "createdAt", "promptTask", "template", "expectedOutput", "AgentsPreviewPanelComponent_div_9_div_3_div_4_Template", "AgentsPreviewPanelComponent_div_9_div_3_div_5_Template", "AgentsPreviewPanelComponent_div_9_div_3_div_6_Template", "AgentsPreviewPanelComponent_div_9_div_3_div_7_Template", "AgentsPreviewPanelComponent_div_9_div_3_div_8_Template", "AgentsPreviewPanelComponent_div_9_div_3_div_11_Template", "AgentsPreviewPanelComponent_div_9_div_3_div_12_Template", "AgentsPreviewPanelComponent_div_9_div_3_div_16_Template", "AgentsPreviewPanelComponent_div_9_div_3_div_17_Template", "embeddingModel", "splitSize", "uploadType", "getFileIconColor", "i_r4", "file_r3", "fileName", "AgentsPreviewPanelComponent_div_9_div_4_div_16_div_4_Template", "files", "AgentsPreviewPanelComponent_div_9_div_4_div_4_Template", "AgentsPreviewPanelComponent_div_9_div_4_div_5_Template", "AgentsPreviewPanelComponent_div_9_div_4_div_8_Template", "AgentsPreviewPanelComponent_div_9_div_4_div_9_Template", "AgentsPreviewPanelComponent_div_9_div_4_div_13_Template", "AgentsPreviewPanelComponent_div_9_div_4_div_14_Template", "AgentsPreviewPanelComponent_div_9_div_4_div_15_Template", "AgentsPreviewPanelComponent_div_9_div_4_div_16_Template", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AgentsPreviewPanelComponent_div_9_div_5_div_10_div_3_Template", "AgentsPreviewPanelComponent_div_9_div_5_div_10_div_4_Template", "AgentsPreviewPanelComponent_div_9_div_5_div_4_Template", "AgentsPreviewPanelComponent_div_9_div_5_div_5_Template", "AgentsPreviewPanelComponent_div_9_div_5_div_8_Template", "AgentsPreviewPanelComponent_div_9_div_5_div_9_Template", "AgentsPreviewPanelComponent_div_9_div_5_div_10_Template", "AgentsPreviewPanelComponent_div_9_div_1_Template", "AgentsPreviewPanelComponent_div_9_div_2_Template", "AgentsPreviewPanelComponent_div_9_div_3_Template", "AgentsPreviewPanelComponent_div_9_div_4_Template", "AgentsPreviewPanelComponent_div_9_div_5_Template", "type", "error", "AgentsPreviewPanelComponent", "closePreview", "onButtonClick", "event", "getAdditionalFields", "excludeFields", "Object", "keys", "filter", "key", "includes", "map", "value", "index", "colors", "getButtonLabel", "selectors", "inputs", "decls", "vars", "consts", "AgentsPreviewPanelComponent_Template", "rf", "ctx", "AgentsPreviewPanelComponent_Template_div_click_1_listener", "AgentsPreviewPanelComponent_Template_app_preview_panel_click_2_listener", "$event", "stopPropagation", "AgentsPreviewPanelComponent_Template_ava_icon_click_6_listener", "AgentsPreviewPanelComponent_div_8_Template", "AgentsPreviewPanelComponent_div_9_Template", "AgentsPreviewPanelComponent_div_10_Template", "AgentsPreviewPanelComponent_Template_ava_button_userClick_12_listener", "loading", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\agents\\build-agents\\agents-preview-panel\\agents-preview-panel.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\pages\\agents\\build-agents\\agents-preview-panel\\agents-preview-panel.component.html"], "sourcesContent": ["import { CommonModule, DatePipe } from '@angular/common';\r\nimport { Component, Input } from '@angular/core';\r\nimport { AvaTextboxComponent, ButtonComponent, IconComponent, SliderComponent } from '@ava/play-comp-library';\r\nimport { PreviewPanelComponent } from '../../../../components/preview-panel/preview-panel.component';\r\n\r\n@Component({\r\n  selector: 'app-agents-preview-panel',\r\n  imports: [PreviewPanelComponent,IconComponent,ButtonComponent,DatePipe, CommonModule,SliderComponent,AvaTextboxComponent],\r\n  templateUrl: './agents-preview-panel.component.html',\r\n  styleUrl: './agents-preview-panel.component.scss'\r\n})\r\nexport class AgentsPreviewPanelComponent {\r\n  @Input() previewData: any = null;\r\n  @Input() closePreview!: () => void;\r\n  \r\n  showMoreConfig = false;\r\n\r\n  toggleConfigDetails(): void {\r\n    this.showMoreConfig = !this.showMoreConfig;\r\n  }\r\n\r\n  onButtonClick(event: any): void {\r\n    this.closePreview();\r\n  }\r\n\r\n  getAdditionalFields(data: any): { key: string; value: any }[] {\r\n    const excludeFields = ['id', 'name', 'description', 'labelCode', 'categoryName', 'categoryId'];\r\n    return Object.keys(data)\r\n      .filter(key => !excludeFields.includes(key) && data[key] != null)\r\n      .map(key => ({ key, value: data[key] }));\r\n  }\r\n\r\n  getFileIconColor(index: number): string {\r\n    const colors = ['#dc2626', '#2563eb', '#16a34a', '#f59e0b', '#8b5cf6'];\r\n    return colors[index % colors.length];\r\n  }\r\n\r\n  getButtonLabel(): string {\r\n    const type = this.previewData?.type;\r\n    switch (type) {\r\n      case 'model':\r\n        return 'Edit Model';\r\n      case 'tool':\r\n        return 'Edit Tool';\r\n      case 'prompt':\r\n        return 'Edit Prompt';\r\n      case 'knowledge':\r\n        return 'Edit Knowledge Base';\r\n      case 'guardrail':\r\n        return 'Edit Guardrail';\r\n      default:\r\n        return 'Edit';\r\n    }\r\n  }\r\n}\r\n", "<div class=\"preview-panel\">\r\n  <div class=\"backdrop\" (click)=\"closePreview()\"></div>\r\n<app-preview-panel class=\"panel-container\" [divider]=\"false\" (click)=\"$event.stopPropagation()\">\r\n<div panel-header class=\"preview-header\">\r\n  <span class=\"panel-title\">Metadata Information</span>\r\n  <ava-icon iconName=\"x\" iconColor=\"black\" class=\"close-btn\" (click)=\"closePreview()\"></ava-icon>\r\n</div>\r\n<div panel-content class=\"preview-content\">\r\n  <!-- Loading State -->\r\n  <div *ngIf=\"previewData?.loading\" class=\"preview-loading\">\r\n    <div class=\"loading-spinner\"></div>\r\n    <p>Loading details...</p>\r\n  </div>\r\n\r\n  <!-- Content based on preview data -->\r\n  <div *ngIf=\"previewData?.data && !previewData?.loading\">\r\n    <!-- Model Preview -->\r\n    <div *ngIf=\"previewData?.type === 'model'\" class=\"model-preview\">\r\n      <div class=\"model-section\">\r\n        <h3>Model Details</h3>\r\n        \r\n        <div class=\"model-field\" *ngIf=\"previewData?.title || previewData.data.name\">\r\n          <label>Model Name</label>\r\n          <div class=\"field-value\">{{ previewData?.title || previewData.data.name }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-field\" *ngIf=\"previewData.data.modelDescription || previewData.data.description\">\r\n          <label>Description</label>\r\n          <div class=\"field-value description-text\">{{ previewData.data.modelDescription || previewData.data.description }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-meta\">\r\n          <div class=\"meta-row\">\r\n            <div class=\"meta-item\" *ngIf=\"previewData.data.createdBy\">\r\n              <label>Added by</label>\r\n              <div class=\"field-value\">{{ previewData.data.createdBy}}</div>\r\n            </div>\r\n            <div class=\"meta-item\" *ngIf=\"previewData.data.createdOn || previewData.data.createdDate\">\r\n              <label>Added on</label>\r\n              <div class=\"field-value\">{{ previewData.data.createdOn || previewData.data.createdDate | date:'MM/dd/yyyy' }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"model-section\">\r\n        <h3>Model Configuration</h3>\r\n        \r\n        <div class=\"config-field\" *ngIf=\"previewData.data.modelType\">\r\n          <label>Select Model</label>\r\n          <div class=\"dropdown-display\">\r\n            <span>{{ previewData.data.modelType}}</span>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Temperature -->\r\n        <div class=\"config-field\" *ngIf=\"previewData.data.temperature !== undefined && previewData.data.temperature !== null\">\r\n          <label>Temperature</label>\r\n          <ava-slider [min]=\"0\" [max]=\"1\" [step]=\"0.01\"\r\n              [value]=\"previewData.data.temperature\"></ava-slider>\r\n        </div>\r\n\r\n        <!-- Max Tokens and Top P in same row -->\r\n        <div class=\"config-row\" *ngIf=\"previewData.data.maxTokens || previewData.data.topP\">\r\n          <div class=\"config-field half-width\" *ngIf=\"previewData.data.maxTokens\">\r\n            <label>Max Token</label>\r\n            <ava-textbox \r\n              [value]=\"previewData.data.maxTokens\" \r\n              placeholder=\"4000\"\r\n              type=\"number\">\r\n            </ava-textbox>\r\n            <div class=\"field-hint\">4096 Tokens used</div>\r\n          </div>\r\n\r\n          <div class=\"config-field half-width\" *ngIf=\"previewData.data.topP\">\r\n            <label>Top P</label>\r\n            <ava-textbox \r\n              [value]=\"previewData.data.topP\" \r\n              placeholder=\"0.95\"\r\n              type=\"number\"\r\n              step=\"0.01\">\r\n            </ava-textbox>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Max Iteration -->\r\n        <div class=\"config-field\" *ngIf=\"previewData.data.maxIteration\">\r\n          <label>Max Iteration</label>\r\n          <ava-textbox \r\n            [value]=\"previewData.data.maxIteration\" \r\n            placeholder=\"1\"\r\n            type=\"number\">\r\n          </ava-textbox>\r\n        </div>\r\n\r\n        <div class=\"config-toggle\">\r\n          <span class=\"toggle-text\" (click)=\"toggleConfigDetails()\">\r\n            View More Configuration Details {{ showMoreConfig ? '-' : '+' }}\r\n          </span>\r\n        </div>\r\n\r\n        <!-- Collapsible Configuration Fields -->\r\n        <div class=\"config-details\" [class.expanded]=\"showMoreConfig\">\r\n          <div class=\"config-field\" *ngIf=\"previewData.data.aiEngine\">\r\n            <label>AI Engine</label>\r\n            <div class=\"dropdown-display\">\r\n              <span class=\"engine-icon\"></span>\r\n              <span>{{ previewData.data.aiEngine}}</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"config-field\" *ngIf=\"previewData.data.modelType\">\r\n            <label>Model Type</label>\r\n            <div class=\"dropdown-display\">\r\n              <span>{{ previewData.data.modelType}}</span>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"config-field\" *ngIf=\"previewData.data.baseurl || previewData.data.baseUrl\">\r\n            <label>Base URL</label>\r\n            <div class=\"input-display\">{{ previewData.data.baseurl || previewData.data.baseUrl }}</div>\r\n          </div>\r\n\r\n          <div class=\"config-field\" *ngIf=\"previewData.data.llmDeploymentName\">\r\n            <label>LLM Deployment Name</label>\r\n            <div class=\"input-display\">{{ previewData.data.llmDeploymentName }}</div>\r\n          </div>\r\n\r\n          <div class=\"config-field\" *ngIf=\"previewData.data.apiKey\">\r\n            <label>API Key Encoded</label>\r\n            <div class=\"input-display\">{{ previewData.data.apiKey}}</div>\r\n          </div>\r\n\r\n          <div class=\"config-field\" *ngIf=\"previewData.data.apiVersion\">\r\n            <label>API Version</label>\r\n            <div class=\"input-display\">{{ previewData.data.apiVersion }}</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Tool Preview -->\r\n    <div *ngIf=\"previewData?.type === 'tool'\" class=\"tool-preview\">\r\n      <div class=\"model-section\">\r\n        <h3>Tool Details</h3>\r\n        \r\n        <div class=\"model-field\" *ngIf=\"previewData?.title || previewData.data.name\">\r\n          <label>Tool Name</label>\r\n          <div class=\"field-value\">{{ previewData?.title || previewData.data.name }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-field\" *ngIf=\"previewData.data.description || previewData.data.appDescription\">\r\n          <label>Description</label>\r\n          <div class=\"field-value description-text\">{{ previewData.data.description || previewData.data.appDescription }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-field\" *ngIf=\"previewData.data.areaOfScope\">\r\n          <label>Area of Scope</label>\r\n          <div class=\"field-value description-text\">{{ previewData.data.areaOfScope }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-meta\">\r\n          <div class=\"meta-row\">\r\n            <div class=\"meta-item\" *ngIf=\"previewData.data.createdBy\">\r\n              <label>Added by</label>\r\n              <div class=\"field-value\">{{ previewData.data.createdBy }}</div>\r\n            </div>\r\n            <div class=\"meta-item\" *ngIf=\"previewData.data.createdOn || previewData.data.createdDate\">\r\n              <label>Added on</label>\r\n              <div class=\"field-value\">{{ previewData.data.createdOn || previewData.data.createdDate | date:'MM/dd/yyyy' }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"model-section\">\r\n        <h3>Tool Configuration</h3>\r\n        \r\n        <div class=\"config-field\" *ngIf=\"previewData.data.functionality || previewData.data.content || previewData.data.toolClassDef\">\r\n          <div class=\"code-content\">{{ previewData.data.functionality || previewData.data.content || previewData.data.toolClassDef || 'No tool definition available' }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Prompt Preview -->\r\n    <div *ngIf=\"previewData?.type === 'prompt'\" class=\"prompt-preview\">\r\n      <div class=\"model-section\">\r\n        <h3>Prompt Details</h3>\r\n        \r\n        <div class=\"model-field\" *ngIf=\"previewData?.title || previewData.data.name\">\r\n          <label>Prompt Name</label>\r\n          <div class=\"field-value\">{{ previewData?.title || previewData.data.name }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-field\" *ngIf=\"previewData.data.description\">\r\n          <label>Description</label>\r\n          <div class=\"field-value description-text\">{{ previewData.data.description }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-field\" *ngIf=\"previewData.data.role\">\r\n          <label>Role</label>\r\n          <div class=\"field-value\">{{ previewData.data.role }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-field\" *ngIf=\"previewData.data.goal\">\r\n          <label>Goal</label>\r\n          <div class=\"field-value description-text\">{{ previewData.data.goal }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-field\" *ngIf=\"previewData.data.backstory\">\r\n          <label>Backstory</label>\r\n          <div class=\"field-value description-text\">{{ previewData.data.backstory }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-meta\">\r\n          <div class=\"meta-row\">\r\n            <div class=\"meta-item\" *ngIf=\"previewData.data.createdBy\">\r\n              <label>Added by</label>\r\n              <div class=\"field-value\">{{ previewData.data.createdBy }}</div>\r\n            </div>\r\n            <div class=\"meta-item\" *ngIf=\"previewData.data.updatedAt || previewData.data.createdAt\">\r\n              <label>Added on</label>\r\n              <div class=\"field-value\">{{ previewData.data.updatedAt || previewData.data.createdAt | date:'MM/dd/yyyy' }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"model-section\">\r\n        <h3>Prompt Configuration</h3>\r\n        \r\n        <div class=\"config-field\" *ngIf=\"previewData.data.promptTask || previewData.data.template || previewData.data.content\">\r\n          <label>Freeform Prompt <span class=\"required\">*</span></label>\r\n          <div class=\"prompt-content\">{{ previewData.data.promptTask || previewData.data.template || previewData.data.content }}</div>\r\n        </div>\r\n\r\n        <div class=\"config-field\" *ngIf=\"previewData.data.expectedOutput\">\r\n          <label>Expected Output</label>\r\n          <div class=\"prompt-content\">{{ previewData.data.expectedOutput }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Knowledge Base Preview -->\r\n    <div *ngIf=\"previewData?.type === 'knowledge'\" class=\"knowledge-preview\">\r\n      <div class=\"model-section\">\r\n        <h3>Knowledge Base Details</h3>\r\n        \r\n        <div class=\"model-field\" *ngIf=\"previewData.data.name || previewData?.title\">\r\n          <label>Knowledge Base Name</label>\r\n          <div class=\"field-value\">{{ previewData.data.name || previewData?.title }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-field\" *ngIf=\"previewData.data.description\">\r\n          <label>Description</label>\r\n          <div class=\"field-value description-text\">{{ previewData.data.description }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-meta\">\r\n          <div class=\"meta-row\">\r\n            <div class=\"meta-item\" *ngIf=\"previewData.data.createdBy\">\r\n              <label>Added by</label>\r\n              <div class=\"field-value\">{{ previewData.data.createdBy}}</div>\r\n            </div>\r\n            <div class=\"meta-item\" *ngIf=\"previewData.data.createdDate || previewData.data.createdOn\">\r\n              <label>Added on</label>\r\n              <div class=\"field-value\">{{ previewData.data.createdDate || previewData.data.createdOn | date:'MM/dd/yyyy' }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"model-section\">\r\n        <h3>Knowledge Base Configuration</h3>\r\n        \r\n        <div class=\"config-field\" *ngIf=\"previewData.data.embeddingModel\">\r\n          <label>Embedding Model</label>\r\n          <div class=\"dropdown-display\">\r\n            <span>{{ previewData.data.embeddingModel }}</span>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"config-field\" *ngIf=\"previewData.data.splitSize !== undefined && previewData.data.splitSize !== null\">\r\n          <label>Split Size</label>\r\n          <ava-slider [min]=\"0\" [max]=\"1\" [step]=\"0.01\"\r\n              [value]=\"previewData.data.splitSize\"></ava-slider>\r\n        </div>\r\n\r\n        <div class=\"config-field\" *ngIf=\"previewData.data.uploadType\">\r\n          <label>Upload Type</label>\r\n          <div class=\"input-display\">{{ previewData.data.uploadType}}</div>\r\n        </div>\r\n\r\n        <div class=\"config-field\" *ngIf=\"previewData.data.files && previewData.data.files.length > 0\">\r\n          <label>Files Uploaded</label>\r\n          <div class=\"files-list\">\r\n            <div *ngFor=\"let file of previewData.data.files; let i = index\" class=\"file-item\">\r\n              <ava-icon iconName=\"file-text\" [iconColor]=\"getFileIconColor(i)\" [iconSize]=\"16\"></ava-icon>\r\n              <span class=\"file-name\">{{ file.fileName || file.name || \"Knowledge Base Data\" }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Guardrail Preview -->\r\n    <div *ngIf=\"previewData?.type === 'guardrail'\" class=\"guardrail-preview\">\r\n      <div class=\"model-section\">\r\n        <h3>Guardrail Details</h3>\r\n        \r\n        <div class=\"model-field\" *ngIf=\"previewData?.title || previewData.data.name\">\r\n          <label>Guardrail Name</label>\r\n          <div class=\"field-value\">{{ previewData?.title || previewData.data.name }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-field\" *ngIf=\"previewData.data.description\">\r\n          <label>Description</label>\r\n          <div class=\"field-value description-text\">{{ previewData.data.description }}</div>\r\n        </div>\r\n\r\n        <div class=\"model-meta\">\r\n          <div class=\"meta-row\">\r\n            <div class=\"meta-item\" *ngIf=\"previewData.data.createdBy\">\r\n              <label>Added by</label>\r\n              <div class=\"field-value\">{{ previewData.data.createdBy}}</div>\r\n            </div>\r\n            <div class=\"meta-item\" *ngIf=\"previewData.data.createdDate || previewData.data.createdOn\">\r\n              <label>Added on</label>\r\n              <div class=\"field-value\">{{ previewData.data.createdDate || previewData.data.createdOn | date:'MM/dd/yyyy' }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"model-section\"  *ngIf=\"previewData.data.yamlContent || previewData.data.content\" >\r\n        <h3>Guardrail Configuration</h3>\r\n        \r\n        <div class=\"config-field\" *ngIf=\"previewData.data.content\">\r\n          <label>Guardrail in Colang</label>\r\n          <div class=\"code-content\">{{ previewData.data.content }}</div>\r\n        </div>\r\n         <div class=\"config-field\" *ngIf=\"previewData.data.yamlContent\">\r\n          <label>Guardrail in Yml</label>\r\n          <div class=\"code-content\">{{ previewData.data.yamlContent }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Error State -->\r\n  <div *ngIf=\"previewData?.error\" class=\"preview-error\">\r\n    <p>{{ previewData.error }}</p>\r\n  </div>\r\n</div>\r\n <div panel-footer>\r\n    <ava-button [label]=\"getButtonLabel()\" variant=\"info\" width=\"100%\" (userClick)=\"onButtonClick($event)\"></ava-button>\r\n  </div>\r\n</app-preview-panel>\r\n</div>\r\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,QAAQ,QAAQ,iBAAiB;AAExD,SAASC,mBAAmB,EAAEC,eAAe,EAAEC,aAAa,EAAEC,eAAe,QAAQ,wBAAwB;AAC7G,SAASC,qBAAqB,QAAQ,8DAA8D;;;;;ICMlGC,EAAA,CAAAC,cAAA,cAA0D;IACxDD,EAAA,CAAAE,SAAA,cAAmC;IACnCF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,yBAAkB;IACvBH,EADuB,CAAAI,YAAA,EAAI,EACrB;;;;;IAUEJ,EADF,CAAAC,cAAA,cAA6E,YACpE;IAAAD,EAAA,CAAAG,MAAA,iBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACzBJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAiD;IAC5EH,EAD4E,CAAAI,YAAA,EAAM,EAC5E;;;;IADqBJ,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAM,iBAAA,EAAAC,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,KAAA,KAAAF,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAC,IAAA,CAAiD;;;;;IAI1EX,EADF,CAAAC,cAAA,cAAmG,YAC1F;IAAAD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC1BJ,EAAA,CAAAC,cAAA,cAA0C;IAAAD,EAAA,CAAAG,MAAA,GAAuE;IACnHH,EADmH,CAAAI,YAAA,EAAM,EACnH;;;;IADsCJ,EAAA,CAAAK,SAAA,GAAuE;IAAvEL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAE,gBAAA,IAAAL,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAG,WAAA,CAAuE;;;;;IAM7Gb,EADF,CAAAC,cAAA,cAA0D,YACjD;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvBJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAA+B;IAC1DH,EAD0D,CAAAI,YAAA,EAAM,EAC1D;;;;IADqBJ,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAI,SAAA,CAA+B;;;;;IAGxDd,EADF,CAAAC,cAAA,cAA0F,YACjF;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvBJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAoF;;IAC/GH,EAD+G,CAAAI,YAAA,EAAM,EAC/G;;;;IADqBJ,EAAA,CAAAK,SAAA,GAAoF;IAApFL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAAe,WAAA,OAAAR,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAM,SAAA,IAAAT,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAO,WAAA,gBAAoF;;;;;IAUjHjB,EADF,CAAAC,cAAA,cAA6D,YACpD;IAAAD,EAAA,CAAAG,MAAA,mBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAEzBJ,EADF,CAAAC,cAAA,cAA8B,WACtB;IAAAD,EAAA,CAAAG,MAAA,GAA+B;IAEzCH,EAFyC,CAAAI,YAAA,EAAO,EACxC,EACF;;;;IAFIJ,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAQ,SAAA,CAA+B;;;;;IAMvClB,EADF,CAAAC,cAAA,cAAsH,YAC7G;IAAAD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC1BJ,EAAA,CAAAE,SAAA,qBACwD;IAC1DF,EAAA,CAAAI,YAAA,EAAM;;;;IAFQJ,EAAA,CAAAK,SAAA,GAAS;IACjBL,EADQ,CAAAmB,UAAA,UAAS,UAAU,cAAc,UAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAU,WAAA,CACH;;;;;IAMxCpB,EADF,CAAAC,cAAA,cAAwE,YAC/D;IAAAD,EAAA,CAAAG,MAAA,gBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACxBJ,EAAA,CAAAE,SAAA,sBAIc;IACdF,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAG,MAAA,uBAAgB;IAC1CH,EAD0C,CAAAI,YAAA,EAAM,EAC1C;;;;IALFJ,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAmB,UAAA,UAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAW,SAAA,CAAoC;;;;;IAQtCrB,EADF,CAAAC,cAAA,cAAmE,YAC1D;IAAAD,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACpBJ,EAAA,CAAAE,SAAA,sBAKc;IAChBF,EAAA,CAAAI,YAAA,EAAM;;;;IALFJ,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAmB,UAAA,UAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAY,IAAA,CAA+B;;;;;IAdrCtB,EAAA,CAAAC,cAAA,cAAoF;IAWlFD,EAVA,CAAAuB,UAAA,IAAAC,6DAAA,kBAAwE,IAAAC,6DAAA,kBAUL;IASrEzB,EAAA,CAAAI,YAAA,EAAM;;;;IAnBkCJ,EAAA,CAAAK,SAAA,EAAgC;IAAhCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAW,SAAA,CAAgC;IAUhCrB,EAAA,CAAAK,SAAA,EAA2B;IAA3BL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAY,IAAA,CAA2B;;;;;IAajEtB,EADF,CAAAC,cAAA,cAAgE,YACvD;IAAAD,EAAA,CAAAG,MAAA,oBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC5BJ,EAAA,CAAAE,SAAA,sBAIc;IAChBF,EAAA,CAAAI,YAAA,EAAM;;;;IAJFJ,EAAA,CAAAK,SAAA,GAAuC;IAAvCL,EAAA,CAAAmB,UAAA,UAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAgB,YAAA,CAAuC;;;;;IAevC1B,EADF,CAAAC,cAAA,cAA4D,YACnD;IAAAD,EAAA,CAAAG,MAAA,gBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACxBJ,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAE,SAAA,eAAiC;IACjCF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,GAA8B;IAExCH,EAFwC,CAAAI,YAAA,EAAO,EACvC,EACF;;;;IAFIJ,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAiB,QAAA,CAA8B;;;;;IAKtC3B,EADF,CAAAC,cAAA,cAA6D,YACpD;IAAAD,EAAA,CAAAG,MAAA,iBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAEvBJ,EADF,CAAAC,cAAA,cAA8B,WACtB;IAAAD,EAAA,CAAAG,MAAA,GAA+B;IAEzCH,EAFyC,CAAAI,YAAA,EAAO,EACxC,EACF;;;;IAFIJ,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAQ,SAAA,CAA+B;;;;;IAKvClB,EADF,CAAAC,cAAA,cAAuF,YAC9E;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvBJ,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAG,MAAA,GAA0D;IACvFH,EADuF,CAAAI,YAAA,EAAM,EACvF;;;;IADuBJ,EAAA,CAAAK,SAAA,GAA0D;IAA1DL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAkB,OAAA,IAAArB,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAmB,OAAA,CAA0D;;;;;IAIrF7B,EADF,CAAAC,cAAA,cAAqE,YAC5D;IAAAD,EAAA,CAAAG,MAAA,0BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAClCJ,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAG,MAAA,GAAwC;IACrEH,EADqE,CAAAI,YAAA,EAAM,EACrE;;;;IADuBJ,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAoB,iBAAA,CAAwC;;;;;IAInE9B,EADF,CAAAC,cAAA,cAA0D,YACjD;IAAAD,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC9BJ,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAG,MAAA,GAA4B;IACzDH,EADyD,CAAAI,YAAA,EAAM,EACzD;;;;IADuBJ,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAqB,MAAA,CAA4B;;;;;IAIvD/B,EADF,CAAAC,cAAA,cAA8D,YACrD;IAAAD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC1BJ,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAG,MAAA,GAAiC;IAC9DH,EAD8D,CAAAI,YAAA,EAAM,EAC9D;;;;IADuBJ,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAsB,UAAA,CAAiC;;;;;;IApHhEhC,EAFJ,CAAAC,cAAA,cAAiE,cACpC,SACrB;IAAAD,EAAA,CAAAG,MAAA,oBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAOtBJ,EALA,CAAAuB,UAAA,IAAAU,sDAAA,kBAA6E,IAAAC,sDAAA,kBAKsB;IAMjGlC,EADF,CAAAC,cAAA,cAAwB,cACA;IAKpBD,EAJA,CAAAuB,UAAA,IAAAY,sDAAA,kBAA0D,IAAAC,sDAAA,kBAIgC;IAMhGpC,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;IAGJJ,EADF,CAAAC,cAAA,eAA2B,UACrB;IAAAD,EAAA,CAAAG,MAAA,2BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAwC5BJ,EAtCA,CAAAuB,UAAA,KAAAc,uDAAA,kBAA6D,KAAAC,uDAAA,kBAQyD,KAAAC,uDAAA,kBAOlC,KAAAC,uDAAA,kBAuBpB;IAU9DxC,EADF,CAAAC,cAAA,eAA2B,gBACiC;IAAhCD,EAAA,CAAAyC,UAAA,mBAAAC,wEAAA;MAAA1C,EAAA,CAAA2C,aAAA,CAAAC,GAAA;MAAA,MAAArC,MAAA,GAAAP,EAAA,CAAA6C,aAAA;MAAA,OAAA7C,EAAA,CAAA8C,WAAA,CAASvC,MAAA,CAAAwC,mBAAA,EAAqB;IAAA,EAAC;IACvD/C,EAAA,CAAAG,MAAA,IACF;IACFH,EADE,CAAAI,YAAA,EAAO,EACH;IAGNJ,EAAA,CAAAC,cAAA,eAA8D;IA+B5DD,EA9BA,CAAAuB,UAAA,KAAAyB,uDAAA,kBAA4D,KAAAC,uDAAA,kBAQC,KAAAC,uDAAA,kBAO0B,KAAAC,uDAAA,kBAKlB,KAAAC,uDAAA,kBAKX,KAAAC,uDAAA,kBAKI;IAMpErD,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;;;;IAtHwBJ,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAmB,UAAA,UAAAZ,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,KAAA,KAAAF,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAC,IAAA,CAAiD;IAKjDX,EAAA,CAAAK,SAAA,EAAuE;IAAvEL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAE,gBAAA,IAAAL,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAG,WAAA,CAAuE;IAOrEb,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAI,SAAA,CAAgC;IAIhCd,EAAA,CAAAK,SAAA,EAAgE;IAAhEL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAM,SAAA,IAAAT,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAO,WAAA,CAAgE;IAWjEjB,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAQ,SAAA,CAAgC;IAQhClB,EAAA,CAAAK,SAAA,EAAyF;IAAzFL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAU,WAAA,KAAAkC,SAAA,IAAA/C,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAU,WAAA,UAAyF;IAO3FpB,EAAA,CAAAK,SAAA,EAAyD;IAAzDL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAW,SAAA,IAAAd,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAY,IAAA,CAAyD;IAuBvDtB,EAAA,CAAAK,SAAA,EAAmC;IAAnCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAgB,YAAA,CAAmC;IAW1D1B,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAuD,kBAAA,sCAAAhD,MAAA,CAAAiD,cAAA,kBACF;IAI0BxD,EAAA,CAAAK,SAAA,EAAiC;IAAjCL,EAAA,CAAAyD,WAAA,aAAAlD,MAAA,CAAAiD,cAAA,CAAiC;IAChCxD,EAAA,CAAAK,SAAA,EAA+B;IAA/BL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAiB,QAAA,CAA+B;IAQ/B3B,EAAA,CAAAK,SAAA,EAAgC;IAAhCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAQ,SAAA,CAAgC;IAOhClB,EAAA,CAAAK,SAAA,EAA0D;IAA1DL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAkB,OAAA,IAAArB,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAmB,OAAA,CAA0D;IAK1D7B,EAAA,CAAAK,SAAA,EAAwC;IAAxCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAoB,iBAAA,CAAwC;IAKxC9B,EAAA,CAAAK,SAAA,EAA6B;IAA7BL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAqB,MAAA,CAA6B;IAK7B/B,EAAA,CAAAK,SAAA,EAAiC;IAAjCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAsB,UAAA,CAAiC;;;;;IAc5DhC,EADF,CAAAC,cAAA,cAA6E,YACpE;IAAAD,EAAA,CAAAG,MAAA,gBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACxBJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAiD;IAC5EH,EAD4E,CAAAI,YAAA,EAAM,EAC5E;;;;IADqBJ,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAM,iBAAA,EAAAC,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,KAAA,KAAAF,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAC,IAAA,CAAiD;;;;;IAI1EX,EADF,CAAAC,cAAA,cAAiG,YACxF;IAAAD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC1BJ,EAAA,CAAAC,cAAA,cAA0C;IAAAD,EAAA,CAAAG,MAAA,GAAqE;IACjHH,EADiH,CAAAI,YAAA,EAAM,EACjH;;;;IADsCJ,EAAA,CAAAK,SAAA,GAAqE;IAArEL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAG,WAAA,IAAAN,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAgD,cAAA,CAAqE;;;;;IAI/G1D,EADF,CAAAC,cAAA,cAA8D,YACrD;IAAAD,EAAA,CAAAG,MAAA,oBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC5BJ,EAAA,CAAAC,cAAA,cAA0C;IAAAD,EAAA,CAAAG,MAAA,GAAkC;IAC9EH,EAD8E,CAAAI,YAAA,EAAM,EAC9E;;;;IADsCJ,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAiD,WAAA,CAAkC;;;;;IAMxE3D,EADF,CAAAC,cAAA,cAA0D,YACjD;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvBJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAgC;IAC3DH,EAD2D,CAAAI,YAAA,EAAM,EAC3D;;;;IADqBJ,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAI,SAAA,CAAgC;;;;;IAGzDd,EADF,CAAAC,cAAA,cAA0F,YACjF;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvBJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAoF;;IAC/GH,EAD+G,CAAAI,YAAA,EAAM,EAC/G;;;;IADqBJ,EAAA,CAAAK,SAAA,GAAoF;IAApFL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAAe,WAAA,OAAAR,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAM,SAAA,IAAAT,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAO,WAAA,gBAAoF;;;;;IAUjHjB,EADF,CAAAC,cAAA,cAA8H,cAClG;IAAAD,EAAA,CAAAG,MAAA,GAAmI;IAC/JH,EAD+J,CAAAI,YAAA,EAAM,EAC/J;;;;IADsBJ,EAAA,CAAAK,SAAA,GAAmI;IAAnIL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAkD,aAAA,IAAArD,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAmD,OAAA,IAAAtD,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAoD,YAAA,mCAAmI;;;;;IAnC/J9D,EAFJ,CAAAC,cAAA,cAA+D,cAClC,SACrB;IAAAD,EAAA,CAAAG,MAAA,mBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAYrBJ,EAVA,CAAAuB,UAAA,IAAAwC,sDAAA,kBAA6E,IAAAC,sDAAA,kBAKoB,IAAAC,sDAAA,kBAKnC;IAM5DjE,EADF,CAAAC,cAAA,cAAwB,cACA;IAKpBD,EAJA,CAAAuB,UAAA,IAAA2C,sDAAA,kBAA0D,KAAAC,uDAAA,kBAIgC;IAMhGnE,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;IAGJJ,EADF,CAAAC,cAAA,eAA2B,UACrB;IAAAD,EAAA,CAAAG,MAAA,0BAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAE3BJ,EAAA,CAAAuB,UAAA,KAAA6C,uDAAA,kBAA8H;IAIlIpE,EADE,CAAAI,YAAA,EAAM,EACF;;;;IApCwBJ,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAmB,UAAA,UAAAZ,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,KAAA,KAAAF,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAC,IAAA,CAAiD;IAKjDX,EAAA,CAAAK,SAAA,EAAqE;IAArEL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAG,WAAA,IAAAN,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAgD,cAAA,CAAqE;IAKrE1D,EAAA,CAAAK,SAAA,EAAkC;IAAlCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAiD,WAAA,CAAkC;IAOhC3D,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAI,SAAA,CAAgC;IAIhCd,EAAA,CAAAK,SAAA,EAAgE;IAAhEL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAM,SAAA,IAAAT,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAO,WAAA,CAAgE;IAWjEjB,EAAA,CAAAK,SAAA,GAAiG;IAAjGL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAkD,aAAA,IAAArD,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAmD,OAAA,IAAAtD,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAoD,YAAA,CAAiG;;;;;IAY1H9D,EADF,CAAAC,cAAA,cAA6E,YACpE;IAAAD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC1BJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAiD;IAC5EH,EAD4E,CAAAI,YAAA,EAAM,EAC5E;;;;IADqBJ,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAM,iBAAA,EAAAC,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,KAAA,KAAAF,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAC,IAAA,CAAiD;;;;;IAI1EX,EADF,CAAAC,cAAA,cAA8D,YACrD;IAAAD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC1BJ,EAAA,CAAAC,cAAA,cAA0C;IAAAD,EAAA,CAAAG,MAAA,GAAkC;IAC9EH,EAD8E,CAAAI,YAAA,EAAM,EAC9E;;;;IADsCJ,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAG,WAAA,CAAkC;;;;;IAI5Eb,EADF,CAAAC,cAAA,cAAuD,YAC9C;IAAAD,EAAA,CAAAG,MAAA,WAAI;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACnBJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAA2B;IACtDH,EADsD,CAAAI,YAAA,EAAM,EACtD;;;;IADqBJ,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA2D,IAAA,CAA2B;;;;;IAIpDrE,EADF,CAAAC,cAAA,cAAuD,YAC9C;IAAAD,EAAA,CAAAG,MAAA,WAAI;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACnBJ,EAAA,CAAAC,cAAA,cAA0C;IAAAD,EAAA,CAAAG,MAAA,GAA2B;IACvEH,EADuE,CAAAI,YAAA,EAAM,EACvE;;;;IADsCJ,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA4D,IAAA,CAA2B;;;;;IAIrEtE,EADF,CAAAC,cAAA,cAA4D,YACnD;IAAAD,EAAA,CAAAG,MAAA,gBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACxBJ,EAAA,CAAAC,cAAA,cAA0C;IAAAD,EAAA,CAAAG,MAAA,GAAgC;IAC5EH,EAD4E,CAAAI,YAAA,EAAM,EAC5E;;;;IADsCJ,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA6D,SAAA,CAAgC;;;;;IAMtEvE,EADF,CAAAC,cAAA,cAA0D,YACjD;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvBJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAgC;IAC3DH,EAD2D,CAAAI,YAAA,EAAM,EAC3D;;;;IADqBJ,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAI,SAAA,CAAgC;;;;;IAGzDd,EADF,CAAAC,cAAA,cAAwF,YAC/E;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvBJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAkF;;IAC7GH,EAD6G,CAAAI,YAAA,EAAM,EAC7G;;;;IADqBJ,EAAA,CAAAK,SAAA,GAAkF;IAAlFL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAAe,WAAA,OAAAR,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA8D,SAAA,IAAAjE,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA+D,SAAA,gBAAkF;;;;;IAU/GzE,EADF,CAAAC,cAAA,cAAuH,YAC9G;IAAAD,EAAA,CAAAG,MAAA,uBAAgB;IAAAH,EAAA,CAAAC,cAAA,eAAuB;IAAAD,EAAA,CAAAG,MAAA,QAAC;IAAOH,EAAP,CAAAI,YAAA,EAAO,EAAQ;IAC9DJ,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAG,MAAA,GAA0F;IACxHH,EADwH,CAAAI,YAAA,EAAM,EACxH;;;;IADwBJ,EAAA,CAAAK,SAAA,GAA0F;IAA1FL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAgE,UAAA,IAAAnE,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAiE,QAAA,IAAApE,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAmD,OAAA,CAA0F;;;;;IAItH7D,EADF,CAAAC,cAAA,cAAkE,YACzD;IAAAD,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC9BJ,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAG,MAAA,GAAqC;IACnEH,EADmE,CAAAI,YAAA,EAAM,EACnE;;;;IADwBJ,EAAA,CAAAK,SAAA,GAAqC;IAArCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAkE,cAAA,CAAqC;;;;;IAnDnE5E,EAFJ,CAAAC,cAAA,cAAmE,cACtC,SACrB;IAAAD,EAAA,CAAAG,MAAA,qBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAsBvBJ,EApBA,CAAAuB,UAAA,IAAAsD,sDAAA,kBAA6E,IAAAC,sDAAA,kBAKf,IAAAC,sDAAA,kBAKP,IAAAC,sDAAA,kBAKA,IAAAC,sDAAA,kBAKK;IAM1DjF,EADF,CAAAC,cAAA,cAAwB,eACA;IAKpBD,EAJA,CAAAuB,UAAA,KAAA2D,uDAAA,kBAA0D,KAAAC,uDAAA,kBAI8B;IAM9FnF,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;IAGJJ,EADF,CAAAC,cAAA,eAA2B,UACrB;IAAAD,EAAA,CAAAG,MAAA,4BAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAO7BJ,EALA,CAAAuB,UAAA,KAAA6D,uDAAA,kBAAuH,KAAAC,uDAAA,kBAKrD;IAKtErF,EADE,CAAAI,YAAA,EAAM,EACF;;;;IApDwBJ,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAmB,UAAA,UAAAZ,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,KAAA,KAAAF,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAC,IAAA,CAAiD;IAKjDX,EAAA,CAAAK,SAAA,EAAkC;IAAlCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAG,WAAA,CAAkC;IAKlCb,EAAA,CAAAK,SAAA,EAA2B;IAA3BL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA2D,IAAA,CAA2B;IAK3BrE,EAAA,CAAAK,SAAA,EAA2B;IAA3BL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA4D,IAAA,CAA2B;IAK3BtE,EAAA,CAAAK,SAAA,EAAgC;IAAhCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA6D,SAAA,CAAgC;IAO9BvE,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAI,SAAA,CAAgC;IAIhCd,EAAA,CAAAK,SAAA,EAA8D;IAA9DL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA8D,SAAA,IAAAjE,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA+D,SAAA,CAA8D;IAW/DzE,EAAA,CAAAK,SAAA,GAA0F;IAA1FL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAgE,UAAA,IAAAnE,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAiE,QAAA,IAAApE,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAmD,OAAA,CAA0F;IAK1F7D,EAAA,CAAAK,SAAA,EAAqC;IAArCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAkE,cAAA,CAAqC;;;;;IAa9D5E,EADF,CAAAC,cAAA,cAA6E,YACpE;IAAAD,EAAA,CAAAG,MAAA,0BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAClCJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAiD;IAC5EH,EAD4E,CAAAI,YAAA,EAAM,EAC5E;;;;IADqBJ,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAC,IAAA,KAAAJ,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,KAAA,EAAiD;;;;;IAI1ET,EADF,CAAAC,cAAA,cAA8D,YACrD;IAAAD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC1BJ,EAAA,CAAAC,cAAA,cAA0C;IAAAD,EAAA,CAAAG,MAAA,GAAkC;IAC9EH,EAD8E,CAAAI,YAAA,EAAM,EAC9E;;;;IADsCJ,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAG,WAAA,CAAkC;;;;;IAMxEb,EADF,CAAAC,cAAA,cAA0D,YACjD;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvBJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAA+B;IAC1DH,EAD0D,CAAAI,YAAA,EAAM,EAC1D;;;;IADqBJ,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAI,SAAA,CAA+B;;;;;IAGxDd,EADF,CAAAC,cAAA,cAA0F,YACjF;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvBJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAoF;;IAC/GH,EAD+G,CAAAI,YAAA,EAAM,EAC/G;;;;IADqBJ,EAAA,CAAAK,SAAA,GAAoF;IAApFL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAAe,WAAA,OAAAR,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAO,WAAA,IAAAV,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAM,SAAA,gBAAoF;;;;;IAUjHhB,EADF,CAAAC,cAAA,cAAkE,YACzD;IAAAD,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAE5BJ,EADF,CAAAC,cAAA,cAA8B,WACtB;IAAAD,EAAA,CAAAG,MAAA,GAAqC;IAE/CH,EAF+C,CAAAI,YAAA,EAAO,EAC9C,EACF;;;;IAFIJ,EAAA,CAAAK,SAAA,GAAqC;IAArCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA4E,cAAA,CAAqC;;;;;IAK7CtF,EADF,CAAAC,cAAA,cAAkH,YACzG;IAAAD,EAAA,CAAAG,MAAA,iBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACzBJ,EAAA,CAAAE,SAAA,qBACsD;IACxDF,EAAA,CAAAI,YAAA,EAAM;;;;IAFQJ,EAAA,CAAAK,SAAA,GAAS;IACjBL,EADQ,CAAAmB,UAAA,UAAS,UAAU,cAAc,UAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA6E,SAAA,CACL;;;;;IAIxCvF,EADF,CAAAC,cAAA,cAA8D,YACrD;IAAAD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC1BJ,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAG,MAAA,GAAgC;IAC7DH,EAD6D,CAAAI,YAAA,EAAM,EAC7D;;;;IADuBJ,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA8E,UAAA,CAAgC;;;;;IAMzDxF,EAAA,CAAAC,cAAA,cAAkF;IAChFD,EAAA,CAAAE,SAAA,mBAA4F;IAC5FF,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAG,MAAA,GAAyD;IACnFH,EADmF,CAAAI,YAAA,EAAO,EACpF;;;;;;IAF2BJ,EAAA,CAAAK,SAAA,EAAiC;IAACL,EAAlC,CAAAmB,UAAA,cAAAZ,MAAA,CAAAkF,gBAAA,CAAAC,IAAA,EAAiC,gBAAgB;IACxD1F,EAAA,CAAAK,SAAA,GAAyD;IAAzDL,EAAA,CAAAM,iBAAA,CAAAqF,OAAA,CAAAC,QAAA,IAAAD,OAAA,CAAAhF,IAAA,0BAAyD;;;;;IAJrFX,EADF,CAAAC,cAAA,cAA8F,YACrF;IAAAD,EAAA,CAAAG,MAAA,qBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC7BJ,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAuB,UAAA,IAAAsE,6DAAA,kBAAkF;IAKtF7F,EADE,CAAAI,YAAA,EAAM,EACF;;;;IALoBJ,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAAmB,UAAA,YAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAoF,KAAA,CAA2B;;;;;IAlDrD9F,EAFJ,CAAAC,cAAA,cAAyE,cAC5C,SACrB;IAAAD,EAAA,CAAAG,MAAA,6BAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAO/BJ,EALA,CAAAuB,UAAA,IAAAwE,sDAAA,kBAA6E,IAAAC,sDAAA,kBAKf;IAM5DhG,EADF,CAAAC,cAAA,cAAwB,cACA;IAKpBD,EAJA,CAAAuB,UAAA,IAAA0E,sDAAA,kBAA0D,IAAAC,sDAAA,kBAIgC;IAMhGlG,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;IAGJJ,EADF,CAAAC,cAAA,eAA2B,UACrB;IAAAD,EAAA,CAAAG,MAAA,oCAA4B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAoBrCJ,EAlBA,CAAAuB,UAAA,KAAA4E,uDAAA,kBAAkE,KAAAC,uDAAA,kBAOgD,KAAAC,uDAAA,kBAMpD,KAAAC,uDAAA,kBAKgC;IAUlGtG,EADE,CAAAI,YAAA,EAAM,EACF;;;;IAvDwBJ,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAC,IAAA,KAAAJ,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,KAAA,EAAiD;IAKjDT,EAAA,CAAAK,SAAA,EAAkC;IAAlCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAG,WAAA,CAAkC;IAOhCb,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAI,SAAA,CAAgC;IAIhCd,EAAA,CAAAK,SAAA,EAAgE;IAAhEL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAO,WAAA,IAAAV,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAM,SAAA,CAAgE;IAWjEhB,EAAA,CAAAK,SAAA,GAAqC;IAArCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA4E,cAAA,CAAqC;IAOrCtF,EAAA,CAAAK,SAAA,EAAqF;IAArFL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA6E,SAAA,KAAAjC,SAAA,IAAA/C,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA6E,SAAA,UAAqF;IAMrFvF,EAAA,CAAAK,SAAA,EAAiC;IAAjCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA8E,UAAA,CAAiC;IAKjCxF,EAAA,CAAAK,SAAA,EAAiE;IAAjEL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAoF,KAAA,IAAAvF,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAoF,KAAA,CAAAS,MAAA,KAAiE;;;;;IAkB1FvG,EADF,CAAAC,cAAA,cAA6E,YACpE;IAAAD,EAAA,CAAAG,MAAA,qBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC7BJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAiD;IAC5EH,EAD4E,CAAAI,YAAA,EAAM,EAC5E;;;;IADqBJ,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAM,iBAAA,EAAAC,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,KAAA,KAAAF,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAC,IAAA,CAAiD;;;;;IAI1EX,EADF,CAAAC,cAAA,cAA8D,YACrD;IAAAD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC1BJ,EAAA,CAAAC,cAAA,cAA0C;IAAAD,EAAA,CAAAG,MAAA,GAAkC;IAC9EH,EAD8E,CAAAI,YAAA,EAAM,EAC9E;;;;IADsCJ,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAG,WAAA,CAAkC;;;;;IAMxEb,EADF,CAAAC,cAAA,cAA0D,YACjD;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvBJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAA+B;IAC1DH,EAD0D,CAAAI,YAAA,EAAM,EAC1D;;;;IADqBJ,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAI,SAAA,CAA+B;;;;;IAGxDd,EADF,CAAAC,cAAA,cAA0F,YACjF;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvBJ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAoF;;IAC/GH,EAD+G,CAAAI,YAAA,EAAM,EAC/G;;;;IADqBJ,EAAA,CAAAK,SAAA,GAAoF;IAApFL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAAe,WAAA,OAAAR,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAO,WAAA,IAAAV,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAM,SAAA,gBAAoF;;;;;IAUjHhB,EADF,CAAAC,cAAA,cAA2D,YAClD;IAAAD,EAAA,CAAAG,MAAA,0BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAClCJ,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAG,MAAA,GAA8B;IAC1DH,EAD0D,CAAAI,YAAA,EAAM,EAC1D;;;;IADsBJ,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAmD,OAAA,CAA8B;;;;;IAGxD7D,EADD,CAAAC,cAAA,cAA+D,YACvD;IAAAD,EAAA,CAAAG,MAAA,uBAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC/BJ,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAG,MAAA,GAAkC;IAC9DH,EAD8D,CAAAI,YAAA,EAAM,EAC9D;;;;IADsBJ,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA8F,WAAA,CAAkC;;;;;IAR9DxG,EADF,CAAAC,cAAA,cAA8F,SACxF;IAAAD,EAAA,CAAAG,MAAA,8BAAuB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAM/BJ,EAJD,CAAAuB,UAAA,IAAAkF,6DAAA,kBAA2D,IAAAC,6DAAA,kBAIK;IAIlE1G,EAAA,CAAAI,YAAA,EAAM;;;;IARuBJ,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAmD,OAAA,CAA8B;IAI7B7D,EAAA,CAAAK,SAAA,EAAkC;IAAlCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA8F,WAAA,CAAkC;;;;;IAjC9DxG,EAFJ,CAAAC,cAAA,cAAyE,cAC5C,SACrB;IAAAD,EAAA,CAAAG,MAAA,wBAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAO1BJ,EALA,CAAAuB,UAAA,IAAAoF,sDAAA,kBAA6E,IAAAC,sDAAA,kBAKf;IAM5D5G,EADF,CAAAC,cAAA,cAAwB,cACA;IAKpBD,EAJA,CAAAuB,UAAA,IAAAsF,sDAAA,kBAA0D,IAAAC,sDAAA,kBAIgC;IAMhG9G,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;IAENJ,EAAA,CAAAuB,UAAA,KAAAwF,uDAAA,kBAA8F;IAYhG/G,EAAA,CAAAI,YAAA,EAAM;;;;IApCwBJ,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAmB,UAAA,UAAAZ,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,KAAA,KAAAF,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAC,IAAA,CAAiD;IAKjDX,EAAA,CAAAK,SAAA,EAAkC;IAAlCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAG,WAAA,CAAkC;IAOhCb,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAI,SAAA,CAAgC;IAIhCd,EAAA,CAAAK,SAAA,EAAgE;IAAhEL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAO,WAAA,IAAAV,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAM,SAAA,CAAgE;IAQjEhB,EAAA,CAAAK,SAAA,EAA8D;IAA9DL,EAAA,CAAAmB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAA8F,WAAA,IAAAjG,MAAA,CAAAC,WAAA,CAAAE,IAAA,CAAAmD,OAAA,CAA8D;;;;;IA/T/F7D,EAAA,CAAAC,cAAA,UAAwD;IAmStDD,EAjSA,CAAAuB,UAAA,IAAAyF,gDAAA,oBAAiE,IAAAC,gDAAA,mBA6HF,IAAAC,gDAAA,mBA2CI,IAAAC,gDAAA,mBA2DM,IAAAC,gDAAA,mBA8DA;IAyC3EpH,EAAA,CAAAI,YAAA,EAAM;;;;IA1UEJ,EAAA,CAAAK,SAAA,EAAmC;IAAnCL,EAAA,CAAAmB,UAAA,UAAAZ,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAA6G,IAAA,cAAmC;IA6HnCrH,EAAA,CAAAK,SAAA,EAAkC;IAAlCL,EAAA,CAAAmB,UAAA,UAAAZ,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAA6G,IAAA,aAAkC;IA2ClCrH,EAAA,CAAAK,SAAA,EAAoC;IAApCL,EAAA,CAAAmB,UAAA,UAAAZ,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAA6G,IAAA,eAAoC;IA2DpCrH,EAAA,CAAAK,SAAA,EAAuC;IAAvCL,EAAA,CAAAmB,UAAA,UAAAZ,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAA6G,IAAA,kBAAuC;IA8DvCrH,EAAA,CAAAK,SAAA,EAAuC;IAAvCL,EAAA,CAAAmB,UAAA,UAAAZ,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAA6G,IAAA,kBAAuC;;;;;IA6C7CrH,EADF,CAAAC,cAAA,cAAsD,QACjD;IAAAD,EAAA,CAAAG,MAAA,GAAuB;IAC5BH,EAD4B,CAAAI,YAAA,EAAI,EAC1B;;;;IADDJ,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAA8G,KAAA,CAAuB;;;ADpV9B,WAAaC,2BAA2B;EAAlC,MAAOA,2BAA2B;IAC7B/G,WAAW,GAAQ,IAAI;IACvBgH,YAAY;IAErBhE,cAAc,GAAG,KAAK;IAEtBT,mBAAmBA,CAAA;MACjB,IAAI,CAACS,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC5C;IAEAiE,aAAaA,CAACC,KAAU;MACtB,IAAI,CAACF,YAAY,EAAE;IACrB;IAEAG,mBAAmBA,CAACjH,IAAS;MAC3B,MAAMkH,aAAa,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,cAAc,EAAE,YAAY,CAAC;MAC9F,OAAOC,MAAM,CAACC,IAAI,CAACpH,IAAI,CAAC,CACrBqH,MAAM,CAACC,GAAG,IAAI,CAACJ,aAAa,CAACK,QAAQ,CAACD,GAAG,CAAC,IAAItH,IAAI,CAACsH,GAAG,CAAC,IAAI,IAAI,CAAC,CAChEE,GAAG,CAACF,GAAG,KAAK;QAAEA,GAAG;QAAEG,KAAK,EAAEzH,IAAI,CAACsH,GAAG;MAAC,CAAE,CAAC,CAAC;IAC5C;IAEAvC,gBAAgBA,CAAC2C,KAAa;MAC5B,MAAMC,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MACtE,OAAOA,MAAM,CAACD,KAAK,GAAGC,MAAM,CAAC9B,MAAM,CAAC;IACtC;IAEA+B,cAAcA,CAAA;MACZ,MAAMjB,IAAI,GAAG,IAAI,CAAC7G,WAAW,EAAE6G,IAAI;MACnC,QAAQA,IAAI;QACV,KAAK,OAAO;UACV,OAAO,YAAY;QACrB,KAAK,MAAM;UACT,OAAO,WAAW;QACpB,KAAK,QAAQ;UACX,OAAO,aAAa;QACtB,KAAK,WAAW;UACd,OAAO,qBAAqB;QAC9B,KAAK,WAAW;UACd,OAAO,gBAAgB;QACzB;UACE,OAAO,MAAM;MACjB;IACF;;uCA1CWE,2BAA2B;IAAA;;YAA3BA,2BAA2B;MAAAgB,SAAA;MAAAC,MAAA;QAAAhI,WAAA;QAAAgH,YAAA;MAAA;MAAAiB,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAhE,QAAA,WAAAiE,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVtC7I,EADF,CAAAC,cAAA,aAA2B,aACsB;UAAzBD,EAAA,CAAAyC,UAAA,mBAAAsG,0DAAA;YAAA,OAASD,GAAA,CAAAtB,YAAA,EAAc;UAAA,EAAC;UAACxH,EAAA,CAAAI,YAAA,EAAM;UACvDJ,EAAA,CAAAC,cAAA,2BAAgG;UAAnCD,EAAA,CAAAyC,UAAA,mBAAAuG,wEAAAC,MAAA;YAAA,OAASA,MAAA,CAAAC,eAAA,EAAwB;UAAA,EAAC;UAE7FlJ,EADF,CAAAC,cAAA,aAAyC,cACb;UAAAD,EAAA,CAAAG,MAAA,2BAAoB;UAAAH,EAAA,CAAAI,YAAA,EAAO;UACrDJ,EAAA,CAAAC,cAAA,kBAAoF;UAAzBD,EAAA,CAAAyC,UAAA,mBAAA0G,+DAAA;YAAA,OAASL,GAAA,CAAAtB,YAAA,EAAc;UAAA,EAAC;UACrFxH,EADsF,CAAAI,YAAA,EAAW,EAC3F;UACNJ,EAAA,CAAAC,cAAA,aAA2C;UAuVzCD,EArVA,CAAAuB,UAAA,IAAA6H,0CAAA,iBAA0D,IAAAC,0CAAA,iBAMF,KAAAC,2CAAA,iBA+UF;UAGxDtJ,EAAA,CAAAI,YAAA,EAAM;UAEFJ,EADH,CAAAC,cAAA,eAAkB,sBACwF;UAApCD,EAAA,CAAAyC,UAAA,uBAAA8G,sEAAAN,MAAA;YAAA,OAAaH,GAAA,CAAArB,aAAA,CAAAwB,MAAA,CAAqB;UAAA,EAAC;UAG1GjJ,EAH2G,CAAAI,YAAA,EAAa,EAChH,EACY,EACd;;;UApWqCJ,EAAA,CAAAK,SAAA,GAAiB;UAAjBL,EAAA,CAAAmB,UAAA,kBAAiB;UAOpDnB,EAAA,CAAAK,SAAA,GAA0B;UAA1BL,EAAA,CAAAmB,UAAA,SAAA2H,GAAA,CAAAtI,WAAA,kBAAAsI,GAAA,CAAAtI,WAAA,CAAAgJ,OAAA,CAA0B;UAM1BxJ,EAAA,CAAAK,SAAA,EAAgD;UAAhDL,EAAA,CAAAmB,UAAA,UAAA2H,GAAA,CAAAtI,WAAA,kBAAAsI,GAAA,CAAAtI,WAAA,CAAAE,IAAA,OAAAoI,GAAA,CAAAtI,WAAA,kBAAAsI,GAAA,CAAAtI,WAAA,CAAAgJ,OAAA,EAAgD;UA+UhDxJ,EAAA,CAAAK,SAAA,EAAwB;UAAxBL,EAAA,CAAAmB,UAAA,SAAA2H,GAAA,CAAAtI,WAAA,kBAAAsI,GAAA,CAAAtI,WAAA,CAAA8G,KAAA,CAAwB;UAKhBtH,EAAA,CAAAK,SAAA,GAA0B;UAA1BL,EAAA,CAAAmB,UAAA,UAAA2H,GAAA,CAAAR,cAAA,GAA0B;;;qBD5V9BvI,qBAAqB,EAACF,aAAa,EAACD,eAAe,EAACF,QAAQ,EAAED,YAAY,EAAAgK,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAC7J,eAAe,EAACH,mBAAmB;MAAAiK,MAAA;IAAA;;SAI7GrC,2BAA2B;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}