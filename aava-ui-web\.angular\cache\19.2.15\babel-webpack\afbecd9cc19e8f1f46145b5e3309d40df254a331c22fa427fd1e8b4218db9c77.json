{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { DropdownMenuComponent } from '../dropdown-menu/dropdown-menu.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction NavItemComponent_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 6);\n    i0.ɵɵelement(1, \"img\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"selected\", ctx_r0.selected);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"selected\", ctx_r0.selected);\n    i0.ɵɵproperty(\"src\", ctx_r0.icon, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction NavItemComponent_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 8);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 9);\n    i0.ɵɵelement(2, \"path\", 10);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"open\", ctx_r0.dropdownOpen);\n  }\n}\nfunction NavItemComponent_app_dropdown_menu_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-dropdown-menu\", 11);\n    i0.ɵɵlistener(\"itemSelected\", function NavItemComponent_app_dropdown_menu_6_Template_app_dropdown_menu_itemSelected_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onDropdownItemSelected($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"items\", ctx_r0.dropdownItems)(\"visible\", ctx_r0.dropdownOpen);\n  }\n}\nexport let NavItemComponent = /*#__PURE__*/(() => {\n  class NavItemComponent {\n    label = '';\n    route = '';\n    selected = false;\n    hasDropdown = false;\n    dropdownOpen = false;\n    dropdownItems = [];\n    icon = '';\n    toggleDropdownEvent = new EventEmitter();\n    navigateEvent = new EventEmitter();\n    selectEvent = new EventEmitter();\n    dropdownItemSelected = new EventEmitter();\n    dropdownPortalOpen = new EventEmitter();\n    toggleDropdown() {\n      this.toggleDropdownEvent.emit();\n    }\n    navigate() {\n      this.navigateEvent.emit(this.route);\n    }\n    select() {\n      this.selectEvent.emit();\n    }\n    onClick(event) {\n      if (this.hasDropdown) {\n        event.stopPropagation();\n        // Emit bounding rect and dropdown data for portal rendering, use label as navItemId\n        const rect = event.currentTarget.getBoundingClientRect();\n        this.dropdownPortalOpen.emit({\n          rect,\n          items: this.dropdownItems,\n          parentLabel: this.label,\n          navItemId: this.label\n        });\n        this.toggleDropdown();\n      } else {\n        this.navigate();\n        this.select();\n      }\n    }\n    onDropdownItemSelected(event) {\n      this.dropdownItemSelected.emit(event);\n    }\n    static ɵfac = function NavItemComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NavItemComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NavItemComponent,\n      selectors: [[\"app-nav-item\"]],\n      inputs: {\n        label: \"label\",\n        route: \"route\",\n        selected: \"selected\",\n        hasDropdown: \"hasDropdown\",\n        dropdownOpen: \"dropdownOpen\",\n        dropdownItems: \"dropdownItems\",\n        icon: \"icon\"\n      },\n      outputs: {\n        toggleDropdownEvent: \"toggleDropdownEvent\",\n        navigateEvent: \"navigateEvent\",\n        selectEvent: \"selectEvent\",\n        dropdownItemSelected: \"dropdownItemSelected\",\n        dropdownPortalOpen: \"dropdownPortalOpen\"\n      },\n      decls: 7,\n      vars: 6,\n      consts: [[1, \"nav-item-container\"], [1, \"nav-item\", 3, \"click\"], [\"class\", \"item-icon\", 3, \"selected\", 4, \"ngIf\"], [1, \"item-label\"], [\"class\", \"dropdown-arrow\", 3, \"open\", 4, \"ngIf\"], [3, \"items\", \"visible\", \"itemSelected\", 4, \"ngIf\"], [1, \"item-icon\"], [\"alt\", \"\", 1, \"nav-icon\", 3, \"src\"], [1, \"dropdown-arrow\"], [\"width\", \"16\", \"height\", \"16\", \"viewBox\", \"0 0 16 16\", \"fill\", \"none\"], [\"d\", \"M4 6L8 10L12 6\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [3, \"itemSelected\", \"items\", \"visible\"]],\n      template: function NavItemComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵlistener(\"click\", function NavItemComponent_Template_div_click_1_listener($event) {\n            return ctx.onClick($event);\n          });\n          i0.ɵɵtemplate(2, NavItemComponent_span_2_Template, 2, 5, \"span\", 2);\n          i0.ɵɵelementStart(3, \"span\", 3);\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(5, NavItemComponent_span_5_Template, 3, 2, \"span\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(6, NavItemComponent_app_dropdown_menu_6_Template, 1, 2, \"app-dropdown-menu\", 5);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"selected\", ctx.selected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.icon);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.label);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.hasDropdown);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.hasDropdown);\n        }\n      },\n      dependencies: [CommonModule, i1.NgIf, DropdownMenuComponent],\n      styles: [\".nav-item-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1000; \\n\\n}\\n\\n.nav-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 2px;\\n  padding: 8px 12px;\\n  border-radius: 999px;\\n  font-size: 14px;\\n  font-weight: 400;\\n  color: var(--nav-item-color);\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  position: relative;\\n  overflow: hidden;\\n  background-color: transparent;\\n  white-space: nowrap;\\n}\\n.nav-item.selected[_ngcontent-%COMP%] {\\n  color: var(--nav-pill-selected-color);\\n  font-weight: bold;\\n}\\n.nav-item.selected[_ngcontent-%COMP%]   .item-icon[_ngcontent-%COMP%] {\\n  color: var(--nav-pill-selected-color);\\n}\\n.nav-item.selected[_ngcontent-%COMP%]   .nav-icon[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  filter: var(--nav-pill-selected-img-filter, brightness(0) saturate(100%) invert(27%) sepia(98%) saturate(749%) hue-rotate(202deg) brightness(97%) contrast(101%));\\n}\\n@media screen and (min-width: 1400px) {\\n  .nav-item[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n  }\\n}\\n@media screen and (min-width: 1200px) {\\n  .nav-item[_ngcontent-%COMP%] {\\n    padding: 8px 5px;\\n  }\\n}\\n\\n.item-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 20px;\\n  height: 20px;\\n  transition: color 0.3s ease;\\n  color: inherit;\\n}\\n.item-icon.selected[_ngcontent-%COMP%] {\\n  color: var(--nav-pill-selected-color);\\n}\\n\\n.item-label[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  font-weight: inherit;\\n}\\n\\n.nav-icon[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n  object-fit: contain;\\n  transition: filter 0.3s ease, opacity 0.3s ease;\\n  opacity: 0.8;\\n}\\n.nav-icon.selected[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  filter: var(--nav-pill-selected-icon-filter, brightness(0) invert(1)); \\n\\n}\\n\\n.dropdown-arrow[_ngcontent-%COMP%] {\\n  margin-left: 2px;\\n  transition: transform 0.3s ease;\\n  height: 16px;\\n  display: flex;\\n  align-items: center;\\n}\\n.dropdown-arrow.open[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n\\n\\n\\n@media screen and (max-width: 768px) {\\n  .nav-item[_ngcontent-%COMP%] {\\n    padding: 6px 8px;\\n    font-size: 12px;\\n    gap: 4px;\\n  }\\n  .item-icon[_ngcontent-%COMP%] {\\n    width: 16px;\\n    height: 16px;\\n  }\\n  .nav-icon[_ngcontent-%COMP%] {\\n    width: 14px;\\n    height: 14px;\\n  }\\n  .dropdown-arrow[_ngcontent-%COMP%] {\\n    height: 14px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return NavItemComponent;\n})();", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "DropdownMenuComponent", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵclassProp", "ctx_r0", "selected", "ɵɵadvance", "ɵɵproperty", "icon", "ɵɵsanitizeUrl", "dropdownOpen", "ɵɵlistener", "NavItemComponent_app_dropdown_menu_6_Template_app_dropdown_menu_itemSelected_0_listener", "$event", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "onDropdownItemSelected", "dropdownItems", "NavItemComponent", "label", "route", "hasDropdown", "toggleDropdownEvent", "navigateEvent", "selectEvent", "dropdownItemSelected", "dropdownPortalOpen", "toggleDropdown", "emit", "navigate", "select", "onClick", "event", "stopPropagation", "rect", "currentTarget", "getBoundingClientRect", "items", "parentLabel", "navItemId", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "NavItemComponent_Template", "rf", "ctx", "NavItemComponent_Template_div_click_1_listener", "ɵɵtemplate", "NavItemComponent_span_2_Template", "ɵɵtext", "NavItemComponent_span_5_Template", "NavItemComponent_app_dropdown_menu_6_Template", "ɵɵtextInterpolate", "i1", "NgIf", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\shared\\components\\nav-item\\nav-item.component.ts", "C:\\console\\aava-ui-web\\projects\\console\\src\\app\\shared\\components\\nav-item\\nav-item.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { DropdownMenuComponent } from '../dropdown-menu/dropdown-menu.component';\r\n\r\ninterface DropdownItem {\r\n  label: string;\r\n  description: string;\r\n  route: string;\r\n  icon: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-nav-item',\r\n  standalone: true,\r\n  imports: [CommonModule, DropdownMenuComponent],\r\n  templateUrl: './nav-item.component.html',\r\n  styleUrl: './nav-item.component.scss',\r\n})\r\nexport class NavItemComponent {\r\n  @Input() label: string = '';\r\n  @Input() route: string = '';\r\n  @Input() selected: boolean = false;\r\n  @Input() hasDropdown: boolean = false;\r\n  @Input() dropdownOpen: boolean = false;\r\n  @Input() dropdownItems: DropdownItem[] = [];\r\n  @Input() icon: string = '';\r\n\r\n  @Output() toggleDropdownEvent = new EventEmitter<void>();\r\n  @Output() navigateEvent = new EventEmitter<string>();\r\n  @Output() selectEvent = new EventEmitter<void>();\r\n  @Output() dropdownItemSelected = new EventEmitter<{\r\n    route: string;\r\n    label: string;\r\n  }>();\r\n  @Output() dropdownPortalOpen = new EventEmitter<{\r\n    rect: DOMRect;\r\n    items: DropdownItem[];\r\n    parentLabel: string;\r\n    navItemId: string;\r\n  }>();\r\n\r\n  toggleDropdown(): void {\r\n    this.toggleDropdownEvent.emit();\r\n  }\r\n\r\n  navigate(): void {\r\n    this.navigateEvent.emit(this.route);\r\n  }\r\n\r\n  select(): void {\r\n    this.selectEvent.emit();\r\n  }\r\n\r\n  onClick(event: MouseEvent): void {\r\n    if (this.hasDropdown) {\r\n      event.stopPropagation();\r\n      // Emit bounding rect and dropdown data for portal rendering, use label as navItemId\r\n      const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();\r\n      this.dropdownPortalOpen.emit({ rect, items: this.dropdownItems, parentLabel: this.label, navItemId: this.label });\r\n      this.toggleDropdown();\r\n    } else {\r\n      this.navigate();\r\n      this.select();\r\n    }\r\n  }\r\n\r\n  onDropdownItemSelected(event: { route: string; label: string }): void {\r\n    this.dropdownItemSelected.emit(event);\r\n  }\r\n}\r\n", "<div class=\"nav-item-container\">\r\n  <div [class.selected]=\"selected\" class=\"nav-item\" (click)=\"onClick($event)\">\r\n    <span *ngIf=\"icon\" class=\"item-icon\" [class.selected]=\"selected\">\r\n      <!-- SVG background approach for better color control -->\r\n      <img [src]=\"icon\" alt=\"\" class=\"nav-icon\" [class.selected]=\"selected\" />\r\n    </span>\r\n    <span class=\"item-label\">{{ label }}</span>\r\n    <span\r\n      *ngIf=\"hasDropdown\"\r\n      class=\"dropdown-arrow\"\r\n      [class.open]=\"dropdownOpen\"\r\n    >\r\n      <svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" fill=\"none\">\r\n        <path\r\n          d=\"M4 6L8 10L12 6\"\r\n          stroke=\"currentColor\"\r\n          stroke-width=\"2\"\r\n          stroke-linecap=\"round\"\r\n          stroke-linejoin=\"round\"\r\n        />\r\n      </svg>\r\n    </span>\r\n  </div>\r\n\r\n  <!-- Dropdown menu -->\r\n  <app-dropdown-menu\r\n    *ngIf=\"hasDropdown\"\r\n    [items]=\"dropdownItems\"\r\n    [visible]=\"dropdownOpen\"\r\n    (itemSelected)=\"onDropdownItemSelected($event)\"\r\n  >\r\n  </app-dropdown-menu>\r\n</div>\r\n"], "mappings": "AAAA,SAAmCA,YAAY,QAAQ,eAAe;AACtE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,qBAAqB,QAAQ,0CAA0C;;;;;ICA5EC,EAAA,CAAAC,cAAA,cAAiE;IAE/DD,EAAA,CAAAE,SAAA,aAAwE;IAC1EF,EAAA,CAAAG,YAAA,EAAO;;;;IAH8BH,EAAA,CAAAI,WAAA,aAAAC,MAAA,CAAAC,QAAA,CAA2B;IAEpBN,EAAA,CAAAO,SAAA,EAA2B;IAA3BP,EAAA,CAAAI,WAAA,aAAAC,MAAA,CAAAC,QAAA,CAA2B;IAAhEN,EAAA,CAAAQ,UAAA,QAAAH,MAAA,CAAAI,IAAA,EAAAT,EAAA,CAAAU,aAAA,CAAY;;;;;IAGnBV,EAAA,CAAAC,cAAA,cAIC;;IACCD,EAAA,CAAAC,cAAA,aAA4D;IAC1DD,EAAA,CAAAE,SAAA,eAME;IAENF,EADE,CAAAG,YAAA,EAAM,EACD;;;;IAXLH,EAAA,CAAAI,WAAA,SAAAC,MAAA,CAAAM,YAAA,CAA2B;;;;;;IAe/BX,EAAA,CAAAC,cAAA,4BAKC;IADCD,EAAA,CAAAY,UAAA,0BAAAC,wFAAAC,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAX,MAAA,GAAAL,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAgBb,MAAA,CAAAc,sBAAA,CAAAL,MAAA,CAA8B;IAAA,EAAC;IAEjDd,EAAA,CAAAG,YAAA,EAAoB;;;;IAHlBH,EADA,CAAAQ,UAAA,UAAAH,MAAA,CAAAe,aAAA,CAAuB,YAAAf,MAAA,CAAAM,YAAA,CACC;;;ADV5B,WAAaU,gBAAgB;EAAvB,MAAOA,gBAAgB;IAClBC,KAAK,GAAW,EAAE;IAClBC,KAAK,GAAW,EAAE;IAClBjB,QAAQ,GAAY,KAAK;IACzBkB,WAAW,GAAY,KAAK;IAC5Bb,YAAY,GAAY,KAAK;IAC7BS,aAAa,GAAmB,EAAE;IAClCX,IAAI,GAAW,EAAE;IAEhBgB,mBAAmB,GAAG,IAAI5B,YAAY,EAAQ;IAC9C6B,aAAa,GAAG,IAAI7B,YAAY,EAAU;IAC1C8B,WAAW,GAAG,IAAI9B,YAAY,EAAQ;IACtC+B,oBAAoB,GAAG,IAAI/B,YAAY,EAG7C;IACMgC,kBAAkB,GAAG,IAAIhC,YAAY,EAK3C;IAEJiC,cAAcA,CAAA;MACZ,IAAI,CAACL,mBAAmB,CAACM,IAAI,EAAE;IACjC;IAEAC,QAAQA,CAAA;MACN,IAAI,CAACN,aAAa,CAACK,IAAI,CAAC,IAAI,CAACR,KAAK,CAAC;IACrC;IAEAU,MAAMA,CAAA;MACJ,IAAI,CAACN,WAAW,CAACI,IAAI,EAAE;IACzB;IAEAG,OAAOA,CAACC,KAAiB;MACvB,IAAI,IAAI,CAACX,WAAW,EAAE;QACpBW,KAAK,CAACC,eAAe,EAAE;QACvB;QACA,MAAMC,IAAI,GAAIF,KAAK,CAACG,aAA6B,CAACC,qBAAqB,EAAE;QACzE,IAAI,CAACV,kBAAkB,CAACE,IAAI,CAAC;UAAEM,IAAI;UAAEG,KAAK,EAAE,IAAI,CAACpB,aAAa;UAAEqB,WAAW,EAAE,IAAI,CAACnB,KAAK;UAAEoB,SAAS,EAAE,IAAI,CAACpB;QAAK,CAAE,CAAC;QACjH,IAAI,CAACQ,cAAc,EAAE;MACvB,CAAC,MAAM;QACL,IAAI,CAACE,QAAQ,EAAE;QACf,IAAI,CAACC,MAAM,EAAE;MACf;IACF;IAEAd,sBAAsBA,CAACgB,KAAuC;MAC5D,IAAI,CAACP,oBAAoB,CAACG,IAAI,CAACI,KAAK,CAAC;IACvC;;uCAlDWd,gBAAgB;IAAA;;YAAhBA,gBAAgB;MAAAsB,SAAA;MAAAC,MAAA;QAAAtB,KAAA;QAAAC,KAAA;QAAAjB,QAAA;QAAAkB,WAAA;QAAAb,YAAA;QAAAS,aAAA;QAAAX,IAAA;MAAA;MAAAoC,OAAA;QAAApB,mBAAA;QAAAC,aAAA;QAAAC,WAAA;QAAAC,oBAAA;QAAAC,kBAAA;MAAA;MAAAiB,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjB3BnD,EADF,CAAAC,cAAA,aAAgC,aAC8C;UAA1BD,EAAA,CAAAY,UAAA,mBAAAyC,+CAAAvC,MAAA;YAAA,OAASsC,GAAA,CAAAlB,OAAA,CAAApB,MAAA,CAAe;UAAA,EAAC;UACzEd,EAAA,CAAAsD,UAAA,IAAAC,gCAAA,kBAAiE;UAIjEvD,EAAA,CAAAC,cAAA,cAAyB;UAAAD,EAAA,CAAAwD,MAAA,GAAW;UAAAxD,EAAA,CAAAG,YAAA,EAAO;UAC3CH,EAAA,CAAAsD,UAAA,IAAAG,gCAAA,kBAIC;UAWHzD,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAsD,UAAA,IAAAI,6CAAA,+BAKC;UAEH1D,EAAA,CAAAG,YAAA,EAAM;;;UA/BCH,EAAA,CAAAO,SAAA,EAA2B;UAA3BP,EAAA,CAAAI,WAAA,aAAAgD,GAAA,CAAA9C,QAAA,CAA2B;UACvBN,EAAA,CAAAO,SAAA,EAAU;UAAVP,EAAA,CAAAQ,UAAA,SAAA4C,GAAA,CAAA3C,IAAA,CAAU;UAIQT,EAAA,CAAAO,SAAA,GAAW;UAAXP,EAAA,CAAA2D,iBAAA,CAAAP,GAAA,CAAA9B,KAAA,CAAW;UAEjCtB,EAAA,CAAAO,SAAA,EAAiB;UAAjBP,EAAA,CAAAQ,UAAA,SAAA4C,GAAA,CAAA5B,WAAA,CAAiB;UAkBnBxB,EAAA,CAAAO,SAAA,EAAiB;UAAjBP,EAAA,CAAAQ,UAAA,SAAA4C,GAAA,CAAA5B,WAAA,CAAiB;;;qBDZV1B,YAAY,EAAA8D,EAAA,CAAAC,IAAA,EAAE9D,qBAAqB;MAAA+D,MAAA;IAAA;;SAIlCzC,gBAAgB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}