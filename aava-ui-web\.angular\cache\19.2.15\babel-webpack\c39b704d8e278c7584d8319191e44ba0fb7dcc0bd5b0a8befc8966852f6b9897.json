{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { Subject, takeUntil, forkJoin } from 'rxjs';\nimport * as Highcharts from 'highcharts';\nimport { HighchartsChartModule } from 'highcharts-angular';\nimport NoDataToDisplay from 'highcharts/modules/no-data-to-display';\nimport HC_more from 'highcharts/highcharts-more';\nimport HC_Exporting from 'highcharts/modules/exporting';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatOptionModule } from '@angular/material/core';\nimport { MatInputModule } from '@angular/material/input';\nimport { ButtonComponent, CalendarComponent, TextCardComponent, IconComponent } from '@ava/play-comp-library';\nimport { environment } from 'projects/console/src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../shared/services/analytics.service\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"highcharts-angular\";\nfunction AnalyticsComponent_div_8_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function AnalyticsComponent_div_8_div_7_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onPdfDownload());\n    });\n    i0.ɵɵelement(2, \"ava-icon\", 33);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Export PDF\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function AnalyticsComponent_div_8_div_7_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onDataDownload());\n    });\n    i0.ɵɵelement(6, \"ava-icon\", 34);\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8, \"Data Dump\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AnalyticsComponent_div_8_ava_icon_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ava-icon\", 29);\n    i0.ɵɵlistener(\"click\", function AnalyticsComponent_div_8_ava_icon_23_Template_ava_icon_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.downloadExcel(\"userConsumption\"));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AnalyticsComponent_div_8_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"div\", 36);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading chart...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AnalyticsComponent_div_8_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"div\", 38);\n    i0.ɵɵelement(2, \"i\", 39);\n    i0.ɵɵelementStart(3, \"div\", 40);\n    i0.ɵɵtext(4, \"No data available\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AnalyticsComponent_div_8_div_27_div_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵtext(1, \"No Data Available\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AnalyticsComponent_div_8_div_27_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"div\", 44)(2, \"div\", 45)(3, \"span\", 46);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, AnalyticsComponent_div_8_div_27_div_1_div_5_Template, 2, 0, \"div\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 48);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const user_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r1.getBarWidth(user_r5.requestCount), \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r5.userSignature);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !user_r5.requestCount);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r5.requestCount);\n  }\n}\nfunction AnalyticsComponent_div_8_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵtemplate(1, AnalyticsComponent_div_8_div_27_div_1_Template, 8, 5, \"div\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.sortedAnalytics);\n  }\n}\nfunction AnalyticsComponent_div_8_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"div\", 36);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading chart...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AnalyticsComponent_div_8_highcharts_chart_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"highcharts-chart\", 50);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"Highcharts\", ctx_r1.Highcharts)(\"options\", ctx_r1.chartOptions[\"linesOfCodeProcessed\"]);\n  }\n}\nfunction AnalyticsComponent_div_8_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"div\", 36);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading chart...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AnalyticsComponent_div_8_highcharts_chart_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"highcharts-chart\", 50);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"Highcharts\", ctx_r1.Highcharts)(\"options\", ctx_r1.chartOptions[\"topUseCases\"]);\n  }\n}\nfunction AnalyticsComponent_div_8_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"div\", 36);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading chart...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AnalyticsComponent_div_8_highcharts_chart_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"highcharts-chart\", 50);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"Highcharts\", ctx_r1.Highcharts)(\"options\", ctx_r1.chartOptions[\"numberOfRequests\"]);\n  }\n}\nfunction AnalyticsComponent_div_8_div_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"div\", 36);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading chart...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AnalyticsComponent_div_8_highcharts_chart_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"highcharts-chart\", 50);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"Highcharts\", ctx_r1.Highcharts)(\"options\", ctx_r1.chartOptions[\"topLanguages\"]);\n  }\n}\nfunction AnalyticsComponent_div_8_div_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"div\", 36);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading chart...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AnalyticsComponent_div_8_highcharts_chart_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"highcharts-chart\", 50);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"Highcharts\", ctx_r1.Highcharts)(\"options\", ctx_r1.chartOptions[\"userResponse\"]);\n  }\n}\nfunction AnalyticsComponent_div_8_div_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"div\", 36);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading chart...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AnalyticsComponent_div_8_highcharts_chart_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"highcharts-chart\", 50);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"Highcharts\", ctx_r1.Highcharts)(\"options\", ctx_r1.chartOptions[\"userActivity\"]);\n  }\n}\nfunction AnalyticsComponent_div_8_div_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"div\", 36);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading chart...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AnalyticsComponent_div_8_highcharts_chart_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"highcharts-chart\", 50);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"Highcharts\", ctx_r1.Highcharts)(\"options\", ctx_r1.chartOptions[\"responseTime\"]);\n  }\n}\nfunction AnalyticsComponent_div_8_div_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵelement(1, \"i\", 51);\n    i0.ɵɵelementStart(2, \"h3\");\n    i0.ɵɵtext(3, \"No User Consumption Data Available\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"No user consumption data found for the selected date range.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AnalyticsComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"div\", 8)(2, \"div\", 9)(3, \"div\", 10)(4, \"ava-button\", 11);\n    i0.ɵɵlistener(\"userClick\", function AnalyticsComponent_div_8_Template_ava_button_userClick_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goToLangfuse());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"ava-button\", 12);\n    i0.ɵɵlistener(\"userClick\", function AnalyticsComponent_div_8_Template_ava_button_userClick_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goToIclAnalytics());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"ava-button\", 13);\n    i0.ɵɵlistener(\"userClick\", function AnalyticsComponent_div_8_Template_ava_button_userClick_6_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onDownloadToggle($event));\n    })(\"click\", function AnalyticsComponent_div_8_Template_ava_button_click_6_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onDownloadToggle($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, AnalyticsComponent_div_8_div_7_Template, 9, 0, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 15)(9, \"ava-calendar\", 16);\n    i0.ɵɵlistener(\"rangeSelected\", function AnalyticsComponent_div_8_Template_ava_calendar_rangeSelected_9_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRangeSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"ava-button\", 17);\n    i0.ɵɵlistener(\"userClick\", function AnalyticsComponent_div_8_Template_ava_button_userClick_10_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.applyFilter());\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(11, \"div\", 18)(12, \"div\", 19);\n    i0.ɵɵelement(13, \"ava-text-card\", 20)(14, \"ava-text-card\", 20)(15, \"ava-text-card\", 20)(16, \"ava-text-card\", 20)(17, \"ava-text-card\", 20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 21)(19, \"div\", 22)(20, \"div\", 23)(21, \"h3\");\n    i0.ɵɵtext(22, \"User Consumption\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(23, AnalyticsComponent_div_8_ava_icon_23_Template, 1, 0, \"ava-icon\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 25);\n    i0.ɵɵtemplate(25, AnalyticsComponent_div_8_div_25_Template, 4, 0, \"div\", 26)(26, AnalyticsComponent_div_8_div_26_Template, 5, 0, \"div\", 27)(27, AnalyticsComponent_div_8_div_27_Template, 2, 1, \"div\", 28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 22)(29, \"div\", 23)(30, \"h3\");\n    i0.ɵɵtext(31, \"Lines of code Processed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"ava-icon\", 29);\n    i0.ɵɵlistener(\"click\", function AnalyticsComponent_div_8_Template_ava_icon_click_32_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.downloadExcel(\"linesOfCodeProcessed\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(33, AnalyticsComponent_div_8_div_33_Template, 4, 0, \"div\", 26)(34, AnalyticsComponent_div_8_highcharts_chart_34_Template, 1, 2, \"highcharts-chart\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\", 22)(36, \"div\", 23)(37, \"h3\");\n    i0.ɵɵtext(38, \"Top 5 Individual Agents\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"ava-icon\", 29);\n    i0.ɵɵlistener(\"click\", function AnalyticsComponent_div_8_Template_ava_icon_click_39_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.downloadExcel(\"topUseCases\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(40, AnalyticsComponent_div_8_div_40_Template, 4, 0, \"div\", 26)(41, AnalyticsComponent_div_8_highcharts_chart_41_Template, 1, 2, \"highcharts-chart\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"div\", 22)(43, \"div\", 23)(44, \"h3\");\n    i0.ɵɵtext(45, \"Number of Requests\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"ava-icon\", 29);\n    i0.ɵɵlistener(\"click\", function AnalyticsComponent_div_8_Template_ava_icon_click_46_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.downloadExcel(\"numberOfRequests\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(47, AnalyticsComponent_div_8_div_47_Template, 4, 0, \"div\", 26)(48, AnalyticsComponent_div_8_highcharts_chart_48_Template, 1, 2, \"highcharts-chart\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"div\", 22)(50, \"div\", 23)(51, \"h3\");\n    i0.ɵɵtext(52, \"Top 5 Languages / Frameworks\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"ava-icon\", 29);\n    i0.ɵɵlistener(\"click\", function AnalyticsComponent_div_8_Template_ava_icon_click_53_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.downloadExcel(\"topLanguages\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(54, AnalyticsComponent_div_8_div_54_Template, 4, 0, \"div\", 26)(55, AnalyticsComponent_div_8_highcharts_chart_55_Template, 1, 2, \"highcharts-chart\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"div\", 22)(57, \"div\", 23)(58, \"h3\");\n    i0.ɵɵtext(59, \"User Response\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(60, \"ava-icon\", 29);\n    i0.ɵɵlistener(\"click\", function AnalyticsComponent_div_8_Template_ava_icon_click_60_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.downloadExcel(\"userResponse\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(61, AnalyticsComponent_div_8_div_61_Template, 4, 0, \"div\", 26)(62, AnalyticsComponent_div_8_highcharts_chart_62_Template, 1, 2, \"highcharts-chart\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(63, \"div\", 22)(64, \"div\", 23)(65, \"h3\");\n    i0.ɵɵtext(66, \"% Dormant User\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(67, \"ava-icon\", 29);\n    i0.ɵɵlistener(\"click\", function AnalyticsComponent_div_8_Template_ava_icon_click_67_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.downloadExcel(\"dormantUsers\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(68, AnalyticsComponent_div_8_div_68_Template, 4, 0, \"div\", 26)(69, AnalyticsComponent_div_8_highcharts_chart_69_Template, 1, 2, \"highcharts-chart\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(70, \"div\", 22)(71, \"div\", 23)(72, \"h3\");\n    i0.ɵɵtext(73, \"Response Time\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(74, \"ava-icon\", 29);\n    i0.ɵɵlistener(\"click\", function AnalyticsComponent_div_8_Template_ava_icon_click_74_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.downloadExcel(\"responseTime\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(75, AnalyticsComponent_div_8_div_75_Template, 4, 0, \"div\", 26)(76, AnalyticsComponent_div_8_highcharts_chart_76_Template, 1, 2, \"highcharts-chart\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(77, AnalyticsComponent_div_8_div_77_Template, 6, 0, \"div\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.vmFG);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"iconSize\", 20);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showDownloadOptions);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"isRange\", true)(\"dateRange\", ctx_r1.getCalendarDateRange());\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"type\", \"default\")(\"iconName\", \"bug\")(\"title\", \"Bugs Found\")(\"value\", (ctx_r1.useCaseMetrics == null ? null : ctx_r1.useCaseMetrics.totalBugs) || 0)(\"description\", \"Total bugs identified\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"type\", \"default\")(\"iconName\", \"shield-check\")(\"title\", \"Unit Tests\")(\"value\", (ctx_r1.useCaseMetrics == null ? null : ctx_r1.useCaseMetrics.totalUnitTest) || 0)(\"description\", \"Unit tests created\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"type\", \"default\")(\"iconName\", \"library-big\")(\"title\", \"Stories/Epics\")(\"value\", (ctx_r1.useCaseMetrics == null ? null : ctx_r1.useCaseMetrics.totalStory) || 0)(\"description\", \"Stories and epics managed\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"type\", \"default\")(\"iconName\", \"code\")(\"title\", \"Code Optimizations\")(\"value\", (ctx_r1.useCaseMetrics == null ? null : ctx_r1.useCaseMetrics.totalCodeOptimization) || 0)(\"description\", \"Code optimizations performed\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"type\", \"default\")(\"iconName\", \"users\")(\"title\", \"Active Users\")(\"value\", (ctx_r1.userActivity == null ? null : ctx_r1.userActivity.activeUsers) || 0)(\"description\", \"Currently active users\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.noDataAvailable);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.usageLoader);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.usageLoader && ctx_r1.noDataAvailable);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.usageLoader && !ctx_r1.noDataAvailable);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.chartLoadingStates[\"linesOfCodeProcessed\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.chartOptions[\"linesOfCodeProcessed\"] && !ctx_r1.chartLoadingStates[\"linesOfCodeProcessed\"]);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.chartLoadingStates[\"topUseCases\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.chartOptions[\"topUseCases\"] && !ctx_r1.chartLoadingStates[\"topUseCases\"]);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.chartLoadingStates[\"numberOfRequests\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.chartOptions[\"numberOfRequests\"] && !ctx_r1.chartLoadingStates[\"numberOfRequests\"]);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.chartLoadingStates[\"topLanguages\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.chartOptions[\"topLanguages\"] && !ctx_r1.chartLoadingStates[\"topLanguages\"]);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.chartLoadingStates[\"userResponse\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.chartOptions[\"userResponse\"] && !ctx_r1.chartLoadingStates[\"userResponse\"]);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.chartLoadingStates[\"userActivity\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.chartOptions[\"userActivity\"] && !ctx_r1.chartLoadingStates[\"userActivity\"]);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.chartLoadingStates[\"responseTime\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.chartOptions[\"responseTime\"] && !ctx_r1.chartLoadingStates[\"responseTime\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.noDataAvailable && !ctx_r1.usageLoader);\n  }\n}\nfunction AnalyticsComponent_div_9_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function AnalyticsComponent_div_9_div_7_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onPdfDownload());\n    });\n    i0.ɵɵelement(2, \"ava-icon\", 33);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Export PDF\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function AnalyticsComponent_div_9_div_7_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onDataDownload());\n    });\n    i0.ɵɵelement(6, \"ava-icon\", 34);\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8, \"Data Dump\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AnalyticsComponent_div_9_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"div\", 36);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading chart...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AnalyticsComponent_div_9_highcharts_chart_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"highcharts-chart\", 50);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"Highcharts\", ctx_r1.Highcharts)(\"options\", ctx_r1.chartOptions[\"studioUsage\"]);\n  }\n}\nfunction AnalyticsComponent_div_9_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"div\", 36);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading chart...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AnalyticsComponent_div_9_highcharts_chart_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"highcharts-chart\", 50);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"Highcharts\", ctx_r1.Highcharts)(\"options\", ctx_r1.chartOptions[\"topAgents\"]);\n  }\n}\nfunction AnalyticsComponent_div_9_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"div\", 36);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading chart...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AnalyticsComponent_div_9_highcharts_chart_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"highcharts-chart\", 50);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"Highcharts\", ctx_r1.Highcharts)(\"options\", ctx_r1.chartOptions[\"agentCreated\"]);\n  }\n}\nfunction AnalyticsComponent_div_9_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"div\", 36);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading agent metrics...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AnalyticsComponent_div_9_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"div\", 38);\n    i0.ɵɵelement(2, \"i\", 51);\n    i0.ɵɵelementStart(3, \"div\", 40);\n    i0.ɵɵtext(4, \"No agent metrics data available\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AnalyticsComponent_div_9_div_46_ng_container_11_button_3__svg_path_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelement(0, \"path\", 72);\n  }\n}\nfunction AnalyticsComponent_div_9_div_46_ng_container_11_button_3__svg_path_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelement(0, \"path\", 73);\n  }\n}\nfunction AnalyticsComponent_div_9_div_46_ng_container_11_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 68);\n    i0.ɵɵlistener(\"click\", function AnalyticsComponent_div_9_div_46_ng_container_11_button_3_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const i_r9 = i0.ɵɵnextContext().index;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.toggleRowExpansion(i_r9));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 69);\n    i0.ɵɵtemplate(2, AnalyticsComponent_div_9_div_46_ng_container_11_button_3__svg_path_2_Template, 1, 0, \"path\", 70)(3, AnalyticsComponent_div_9_div_46_ng_container_11_button_3__svg_path_3_Template, 1, 0, \"path\", 71);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const i_r9 = i0.ɵɵnextContext().index;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"expanded\", ctx_r1.isRowExpanded(i_r9));\n    i0.ɵɵproperty(\"title\", ctx_r1.isRowExpanded(i_r9) ? \"Collapse workflows\" : \"Expand workflows\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isRowExpanded(i_r9));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isRowExpanded(i_r9));\n  }\n}\nfunction AnalyticsComponent_div_9_div_46_ng_container_11_tr_8_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const workflow_r10 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", workflow_r10, \" \");\n  }\n}\nfunction AnalyticsComponent_div_9_div_46_ng_container_11_tr_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 74)(1, \"td\", 75)(2, \"div\", 76)(3, \"div\", 77);\n    i0.ɵɵtext(4, \"Workflows:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 78);\n    i0.ɵɵtemplate(6, AnalyticsComponent_div_9_div_46_ng_container_11_tr_8_div_6_Template, 2, 1, \"div\", 79);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const agent_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", agent_r11.workflows);\n  }\n}\nfunction AnalyticsComponent_div_9_div_46_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"tr\", 63)(2, \"td\", 61);\n    i0.ɵɵtemplate(3, AnalyticsComponent_div_9_div_46_ng_container_11_button_3_Template, 4, 5, \"button\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\", 65);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\", 66);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, AnalyticsComponent_div_9_div_46_ng_container_11_tr_8_Template, 7, 1, \"tr\", 67);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const agent_r11 = ctx.$implicit;\n    const i_r9 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", agent_r11.workflows && agent_r11.workflows.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(agent_r11.agentName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(agent_r11.workflowCount);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isRowExpanded(i_r9));\n  }\n}\nfunction AnalyticsComponent_div_9_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"div\", 59)(2, \"table\", 60)(3, \"thead\")(4, \"tr\");\n    i0.ɵɵelement(5, \"th\", 61);\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Agent Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"No. of time used\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"tbody\");\n    i0.ɵɵtemplate(11, AnalyticsComponent_div_9_div_46_ng_container_11_Template, 9, 4, \"ng-container\", 62);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.agentMetricsTableData);\n  }\n}\nfunction AnalyticsComponent_div_9_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"div\", 36);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading chart...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AnalyticsComponent_div_9_highcharts_chart_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"highcharts-chart\", 50);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"Highcharts\", ctx_r1.Highcharts)(\"options\", ctx_r1.chartOptions[\"toolAnalytics\"]);\n  }\n}\nfunction AnalyticsComponent_div_9_div_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 81);\n    i0.ɵɵelement(1, \"div\", 36);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading chart...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AnalyticsComponent_div_9_ng_container_57_ava_icon_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ava-icon\", 29);\n    i0.ɵɵlistener(\"click\", function AnalyticsComponent_div_9_ng_container_57_ava_icon_3_Template_ava_icon_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.downloadAgentExcel(\"toolUsage\"));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AnalyticsComponent_div_9_ng_container_57_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"div\", 38);\n    i0.ɵɵelement(2, \"i\", 39);\n    i0.ɵɵelementStart(3, \"div\", 40);\n    i0.ɵɵtext(4, \"No data available\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AnalyticsComponent_div_9_ng_container_57_div_5_div_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵtext(1, \"No Data Available\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AnalyticsComponent_div_9_ng_container_57_div_5_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"div\", 44)(2, \"div\", 83)(3, \"span\", 46);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, AnalyticsComponent_div_9_ng_container_57_div_5_div_1_div_5_Template, 2, 0, \"div\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 48);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tool_r13 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r1.getBarWidth(tool_r13.usageCount, true), \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tool_r13.toolName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !tool_r13.usageCount);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tool_r13.usageCount);\n  }\n}\nfunction AnalyticsComponent_div_9_ng_container_57_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵtemplate(1, AnalyticsComponent_div_9_ng_container_57_div_5_div_1_Template, 8, 5, \"div\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.toolUsage.slice(0, 5));\n  }\n}\nfunction AnalyticsComponent_div_9_ng_container_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 82);\n    i0.ɵɵtext(2, \" Tool Usage \");\n    i0.ɵɵtemplate(3, AnalyticsComponent_div_9_ng_container_57_ava_icon_3_Template, 1, 0, \"ava-icon\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, AnalyticsComponent_div_9_ng_container_57_div_4_Template, 5, 0, \"div\", 27)(5, AnalyticsComponent_div_9_ng_container_57_div_5_Template, 2, 1, \"div\", 28);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.toolUsage && ctx_r1.toolUsage.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.toolUsage || ctx_r1.toolUsage.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.toolUsage && ctx_r1.toolUsage.length > 0);\n  }\n}\nfunction AnalyticsComponent_div_9_div_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"div\", 36);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading chart...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AnalyticsComponent_div_9_highcharts_chart_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"highcharts-chart\", 50);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"Highcharts\", ctx_r1.Highcharts)(\"options\", ctx_r1.chartOptions[\"adoptionRate\"]);\n  }\n}\nfunction AnalyticsComponent_div_9_ava_icon_69_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ava-icon\", 29);\n    i0.ɵɵlistener(\"click\", function AnalyticsComponent_div_9_ava_icon_69_Template_ava_icon_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.downloadAgentExcel(\"userConsumption\"));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AnalyticsComponent_div_9_div_71_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"div\", 36);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading chart...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AnalyticsComponent_div_9_div_72_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"div\", 38);\n    i0.ɵɵelement(2, \"i\", 39);\n    i0.ɵɵelementStart(3, \"div\", 40);\n    i0.ɵɵtext(4, \"No data available\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AnalyticsComponent_div_9_div_73_div_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵtext(1, \"No Data Available\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AnalyticsComponent_div_9_div_73_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"div\", 44)(2, \"div\", 45)(3, \"span\", 46);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, AnalyticsComponent_div_9_div_73_div_1_div_5_Template, 2, 0, \"div\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 48);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const user_r15 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r1.getBarWidth(user_r15.consumptionCount || user_r15.requestCount), \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r15.email || user_r15.userSignature);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !(user_r15.consumptionCount || user_r15.requestCount));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r15.consumptionCount || user_r15.requestCount);\n  }\n}\nfunction AnalyticsComponent_div_9_div_73_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵtemplate(1, AnalyticsComponent_div_9_div_73_div_1_Template, 8, 5, \"div\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.userConsumption);\n  }\n}\nfunction AnalyticsComponent_div_9_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"div\", 36);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading chart...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AnalyticsComponent_div_9_highcharts_chart_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"highcharts-chart\", 50);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"Highcharts\", ctx_r1.Highcharts)(\"options\", ctx_r1.chartOptions[\"userActivity\"]);\n  }\n}\nfunction AnalyticsComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"div\", 8)(2, \"div\", 9)(3, \"div\", 10)(4, \"ava-button\", 11);\n    i0.ɵɵlistener(\"userClick\", function AnalyticsComponent_div_9_Template_ava_button_userClick_4_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goToLangfuse());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"ava-button\", 12);\n    i0.ɵɵlistener(\"userClick\", function AnalyticsComponent_div_9_Template_ava_button_userClick_5_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goToIclAnalytics());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"ava-button\", 13);\n    i0.ɵɵlistener(\"userClick\", function AnalyticsComponent_div_9_Template_ava_button_userClick_6_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onDownloadToggle($event));\n    })(\"click\", function AnalyticsComponent_div_9_Template_ava_button_click_6_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onDownloadToggle($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, AnalyticsComponent_div_9_div_7_Template, 9, 0, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 15)(9, \"ava-calendar\", 16);\n    i0.ɵɵlistener(\"rangeSelected\", function AnalyticsComponent_div_9_Template_ava_calendar_rangeSelected_9_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRangeSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"ava-button\", 17);\n    i0.ɵɵlistener(\"userClick\", function AnalyticsComponent_div_9_Template_ava_button_userClick_10_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.applyFilter());\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(11, \"div\", 18)(12, \"div\", 19);\n    i0.ɵɵelement(13, \"ava-text-card\", 20)(14, \"ava-text-card\", 20)(15, \"ava-text-card\", 20)(16, \"ava-text-card\", 20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 21)(18, \"div\", 22)(19, \"div\", 23)(20, \"h3\");\n    i0.ɵɵtext(21, \"Studio Usage\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"ava-icon\", 29);\n    i0.ɵɵlistener(\"click\", function AnalyticsComponent_div_9_Template_ava_icon_click_22_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.downloadAgentExcel(\"studioUsage\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(23, AnalyticsComponent_div_9_div_23_Template, 4, 0, \"div\", 26)(24, AnalyticsComponent_div_9_highcharts_chart_24_Template, 1, 2, \"highcharts-chart\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 22)(26, \"div\", 23)(27, \"h3\");\n    i0.ɵɵtext(28, \"Top 5 Collaborative Agents\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"ava-icon\", 29);\n    i0.ɵɵlistener(\"click\", function AnalyticsComponent_div_9_Template_ava_icon_click_29_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.downloadAgentExcel(\"topAgents\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(30, AnalyticsComponent_div_9_div_30_Template, 4, 0, \"div\", 26)(31, AnalyticsComponent_div_9_highcharts_chart_31_Template, 1, 2, \"highcharts-chart\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 22)(33, \"div\", 23)(34, \"h3\");\n    i0.ɵɵtext(35, \"Collaborative Agent Created\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"ava-icon\", 29);\n    i0.ɵɵlistener(\"click\", function AnalyticsComponent_div_9_Template_ava_icon_click_36_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.downloadAgentExcel(\"agentCreated\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(37, AnalyticsComponent_div_9_div_37_Template, 4, 0, \"div\", 26)(38, AnalyticsComponent_div_9_highcharts_chart_38_Template, 1, 2, \"highcharts-chart\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 22)(40, \"div\", 23)(41, \"h3\");\n    i0.ɵɵtext(42, \"Collaborative Agent Metrics\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"ava-icon\", 29);\n    i0.ɵɵlistener(\"click\", function AnalyticsComponent_div_9_Template_ava_icon_click_43_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.downloadAgentExcel(\"agentMetrics\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(44, AnalyticsComponent_div_9_div_44_Template, 4, 0, \"div\", 26)(45, AnalyticsComponent_div_9_div_45_Template, 5, 0, \"div\", 27)(46, AnalyticsComponent_div_9_div_46_Template, 12, 1, \"div\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"div\", 22)(48, \"div\", 23)(49, \"h3\");\n    i0.ɵɵtext(50, \"Tool Analytics\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"ava-icon\", 29);\n    i0.ɵɵlistener(\"click\", function AnalyticsComponent_div_9_Template_ava_icon_click_51_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.downloadAgentExcel(\"toolAnalytics\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(52, AnalyticsComponent_div_9_div_52_Template, 4, 0, \"div\", 26)(53, AnalyticsComponent_div_9_highcharts_chart_53_Template, 1, 2, \"highcharts-chart\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"div\", 53)(55, \"div\", 54);\n    i0.ɵɵtemplate(56, AnalyticsComponent_div_9_div_56_Template, 4, 0, \"div\", 55)(57, AnalyticsComponent_div_9_ng_container_57_Template, 6, 3, \"ng-container\", 56);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(58, \"div\", 22)(59, \"div\", 23)(60, \"h3\");\n    i0.ɵɵtext(61, \"Adoption Rate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(62, \"ava-icon\", 29);\n    i0.ɵɵlistener(\"click\", function AnalyticsComponent_div_9_Template_ava_icon_click_62_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.downloadAgentExcel(\"adoptionRate\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(63, AnalyticsComponent_div_9_div_63_Template, 4, 0, \"div\", 26)(64, AnalyticsComponent_div_9_highcharts_chart_64_Template, 1, 2, \"highcharts-chart\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(65, \"div\", 22)(66, \"div\", 23)(67, \"h3\");\n    i0.ɵɵtext(68, \"User Consumption\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(69, AnalyticsComponent_div_9_ava_icon_69_Template, 1, 0, \"ava-icon\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(70, \"div\", 57);\n    i0.ɵɵtemplate(71, AnalyticsComponent_div_9_div_71_Template, 4, 0, \"div\", 26)(72, AnalyticsComponent_div_9_div_72_Template, 5, 0, \"div\", 27)(73, AnalyticsComponent_div_9_div_73_Template, 2, 1, \"div\", 28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(74, \"div\", 22)(75, \"div\", 23)(76, \"h3\");\n    i0.ɵɵtext(77, \"% Dormant User\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(78, \"ava-icon\", 29);\n    i0.ɵɵlistener(\"click\", function AnalyticsComponent_div_9_Template_ava_icon_click_78_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.downloadAgentExcel(\"dormantUsers\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(79, AnalyticsComponent_div_9_div_79_Template, 4, 0, \"div\", 26)(80, AnalyticsComponent_div_9_highcharts_chart_80_Template, 1, 2, \"highcharts-chart\", 30);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.vmFG);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"iconSize\", 20);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showDownloadOptions);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"isRange\", true)(\"dateRange\", ctx_r1.getCalendarDateRange());\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"type\", \"default\")(\"iconName\", \"bot\")(\"title\", \"Collaborative Agents Created\")(\"value\", (ctx_r1.agentMetrics == null ? null : ctx_r1.agentMetrics.totalAgentsCreated) || 0)(\"description\", \"Collaborative agents created\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"type\", \"default\")(\"iconName\", \"rotate-ccw\")(\"title\", \"Collaborative Agents Reused\")(\"value\", (ctx_r1.agentMetrics == null ? null : ctx_r1.agentMetrics.totalAgentsReused) || 0)(\"description\", \"Collaborative agents reused\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"type\", \"default\")(\"iconName\", \"wrench\")(\"title\", \"Total Tools\")(\"value\", (ctx_r1.agentMetrics == null ? null : ctx_r1.agentMetrics.totalTools) || 0)(\"description\", \"Tools available\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"type\", \"default\")(\"iconName\", \"workflow\")(\"title\", \"Workflow Execution Count\")(\"value\", (ctx_r1.agentMetrics == null ? null : ctx_r1.agentMetrics.workflowExecutionCount) || 0)(\"description\", \"Workflow executions\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.chartLoadingStates[\"studioUsage\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.chartOptions[\"studioUsage\"] && !ctx_r1.chartLoadingStates[\"studioUsage\"]);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.chartLoadingStates[\"topAgents\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.chartOptions[\"topAgents\"] && !ctx_r1.chartLoadingStates[\"topAgents\"]);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.chartLoadingStates[\"agentCreated\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.chartOptions[\"agentCreated\"] && !ctx_r1.chartLoadingStates[\"agentCreated\"]);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.agentMetricsLoader);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.agentMetricsNoDataAvailable && !ctx_r1.agentMetricsLoader);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.agentMetricsNoDataAvailable && !ctx_r1.agentMetricsLoader);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.chartLoadingStates[\"toolAnalytics\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.chartOptions[\"toolAnalytics\"] && !ctx_r1.chartLoadingStates[\"toolAnalytics\"]);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.chartLoadingStates[\"toolUsage\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.chartLoadingStates[\"toolUsage\"]);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.chartLoadingStates[\"adoptionRate\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.chartOptions[\"adoptionRate\"] && !ctx_r1.chartLoadingStates[\"adoptionRate\"]);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.userConsumption && ctx_r1.userConsumption.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.chartLoadingStates[\"userConsumption\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.chartLoadingStates[\"userConsumption\"] && (!ctx_r1.userConsumption || ctx_r1.userConsumption.length === 0));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.chartLoadingStates[\"userConsumption\"] && ctx_r1.userConsumption && ctx_r1.userConsumption.length > 0);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.chartLoadingStates[\"userActivity\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.chartOptions[\"userActivity\"] && !ctx_r1.chartLoadingStates[\"userActivity\"]);\n  }\n}\nHC_more(Highcharts);\nNoDataToDisplay(Highcharts);\nHC_Exporting(Highcharts);\nexport let AnalyticsComponent = /*#__PURE__*/(() => {\n  class AnalyticsComponent {\n    analyticsService;\n    fb;\n    destroy$ = new Subject();\n    CHART_COLORS = {\n      primary: '#4F46E5',\n      // Modern Indigo\n      secondary: '#059669',\n      // Emerald Green\n      tertiary: '#DC2626',\n      // Modern Red\n      success: '#10B981',\n      // Success Green\n      warning: '#F59E0B',\n      // Amber Warning\n      danger: '#EF4444' // Error Red\n    };\n    // Professional color palettes for different chart types\n    PROFESSIONAL_COLORS = {\n      userConsumption: '#8B5CF6',\n      // Purple - User related\n      linesOfCode: '#3B82F6',\n      // Blue - Code related\n      topUseCases: '#10B981',\n      // Green - Success/Usage\n      numberOfRequests: '#F59E0B',\n      // Orange - Activity\n      topLanguages: '#EF4444',\n      // Red - Languages\n      userResponse: ['#8B5CF6', '#EC4899', '#F59E0B', '#10B981'],\n      // Multi-color for pie\n      userActivity: ['#10B981', '#EF4444'],\n      // Green/Red for active/inactive\n      responseTime: '#06B6D4',\n      // Cyan - Performance\n      studioUsage: ['#8B5CF6', '#EC4899', '#F59E0B', '#10B981', '#EF4444'],\n      // Multi-color\n      topAgents: '#EC4899',\n      // Pink - Agents\n      agentCreated: '#14B8A6',\n      // Teal - Creation\n      toolAnalytics: ['#F59E0B', '#8B5CF6'],\n      // Orange/Purple for tools\n      toolUsage: '#F97316',\n      // Orange - Tool usage\n      adoptionRate: '#8B5CF6',\n      // Purple - Adoption\n      collaborativeUserConsumption: '#EC4899' // Pink - Collaborative users\n    };\n    CHART_DEFAULTS = {\n      backgroundColor: 'transparent',\n      fontFamily: 'Mulish, sans-serif',\n      height: 320\n    };\n    Highcharts = Highcharts;\n    vmFG;\n    dateRange = {\n      fromDate: this.getDefaultFromDate(),\n      toDate: this.getDefaultToDate()\n    };\n    activeTab = 'usecase';\n    tabs = [{\n      id: 'usecase',\n      label: 'Individual'\n    }, {\n      id: 'agents',\n      label: 'Collaborative'\n    }];\n    tabListStyle = {\n      'background': 'var(--card-bg)',\n      'border': '1px solid var(--card-border)',\n      'border-radius': '8px',\n      'padding': '0.25rem'\n    };\n    isLoading = false;\n    usageLoader = false;\n    metricesLoader = false;\n    consumptionLoader = false;\n    agentMetricsLoader = false;\n    chartLoadingStates = {};\n    chartOptions = {};\n    chartRefs = {};\n    analyticsData = {};\n    useCaseMetrics = {};\n    agentMetrics = {};\n    userActivity = {};\n    userActivityStats = {};\n    userConsumption = [];\n    toolUsage = [];\n    sortedAnalytics = [];\n    agentMetricsTableData = [];\n    expandedRows = new Set();\n    noDataAvailable = false;\n    agentMetricsNoDataAvailable = false;\n    selectedUseCases = [];\n    useCaseList = [];\n    sortedUseCaseList = [];\n    searchText = '';\n    filteringEnabled = false;\n    showDownloadOptions = false;\n    langfuseUrl = environment.consoleLangfuseUrl;\n    ICLAnalyticsUrl = environment.consoleTruelensUrl;\n    constructor(analyticsService, fb) {\n      this.analyticsService = analyticsService;\n      this.fb = fb;\n      this.initializeForm();\n    }\n    ngOnInit() {\n      this.initializeAnalytics();\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    initializeForm() {\n      this.vmFG = this.fb.group({\n        fromDate: [this.dateRange.fromDate],\n        toDate: [this.dateRange.toDate]\n      });\n      this.vmFG.valueChanges.pipe(takeUntil(this.destroy$)).subscribe(values => {\n        if (values.fromDate && values.toDate) {\n          this.dateRange.fromDate = values.fromDate;\n          this.dateRange.toDate = values.toDate;\n        }\n      });\n    }\n    initializeAnalytics() {\n      this.loadAnalyticsData();\n    }\n    onTabChange(tabItem) {\n      this.activeTab = tabItem.id;\n      this.loadTabSpecificData(tabItem.id);\n    }\n    getActiveTab() {\n      return this.tabs.find(tab => tab.id === this.activeTab) || this.tabs[0];\n    }\n    loadTabSpecificData(tabId) {\n      switch (tabId) {\n        case 'usecase':\n          this.loadUseCaseData();\n          break;\n        case 'agents':\n          this.loadAgentData();\n          break;\n      }\n    }\n    onDateRangeChange() {\n      const formValues = this.vmFG.value;\n      if (formValues.fromDate && formValues.toDate) {\n        this.dateRange.fromDate = formValues.fromDate;\n        this.dateRange.toDate = formValues.toDate;\n      }\n    }\n    applyFilter() {\n      this.onDateRangeChange();\n      if (!this.isValidDateRange()) {\n        console.error('Invalid date range selected');\n        return;\n      }\n      this.loadAnalyticsData();\n    }\n    isValidDateRange() {\n      const fromDate = new Date(this.dateRange.fromDate);\n      const toDate = new Date(this.dateRange.toDate);\n      return fromDate <= toDate && fromDate <= new Date();\n    }\n    getDefaultFromDate() {\n      const date = new Date();\n      // Set to the 1st day of the current month\n      date.setDate(1);\n      return date.toISOString().split('T')[0];\n    }\n    getDefaultToDate() {\n      return new Date().toISOString().split('T')[0];\n    }\n    formatDateForAPI(dateString) {\n      const [year, month, day] = dateString.split('-');\n      return `${day}-${month}-${year}`;\n    }\n    getFormattedDateRangeForAPI() {\n      return {\n        fromDate: this.formatDateForAPI(this.dateRange.fromDate),\n        toDate: this.formatDateForAPI(this.dateRange.toDate)\n      };\n    }\n    loadAnalyticsData() {\n      this.isLoading = true;\n      switch (this.activeTab) {\n        case 'usecase':\n          this.loadUseCaseData();\n          break;\n        case 'agents':\n          this.loadAgentData();\n          break;\n        default:\n          this.isLoading = false;\n      }\n    }\n    loadUseCaseData() {\n      this.isLoading = true;\n      this.usageLoader = true;\n      const formattedDateRange = this.getFormattedDateRangeForAPI();\n      forkJoin({\n        mainAnalytics: this.analyticsService.getAllAnalyticsData(formattedDateRange),\n        totalRequestMetrics: this.analyticsService.totalRequestCount(formattedDateRange),\n        userConsumption: this.analyticsService.getUserUsageAnalytics(formattedDateRange),\n        userActivity: this.analyticsService.getUserActivity(formattedDateRange),\n        responseTime: this.analyticsService.getResponseTime(formattedDateRange)\n      }).pipe(takeUntil(this.destroy$)).subscribe({\n        next: responses => {\n          this.parseAndCreateChartsWithSeparateAPIs(responses);\n          this.useCaseMetrics = responses.totalRequestMetrics;\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('Error loading analytics data:', error);\n          this.isLoading = false;\n        }\n      });\n    }\n    parseAndCreateChartsWithSeparateAPIs(responses) {\n      const mainData = responses.mainAnalytics;\n      const userConsumptionData = responses.userConsumption;\n      const userActivityData = responses.userActivity;\n      const responseTimeData = responses.responseTime;\n      Object.keys(this.chartLoadingStates).forEach(key => {\n        this.chartLoadingStates[key] = false;\n      });\n      let userAnalyticsArray = null;\n      if (userConsumptionData) {\n        if (userConsumptionData.userLevelAnalytics && userConsumptionData.userLevelAnalytics.length > 0) {\n          userAnalyticsArray = userConsumptionData.userLevelAnalytics;\n        } else if (userConsumptionData.userAnalytics && userConsumptionData.userAnalytics.length > 0) {\n          userAnalyticsArray = userConsumptionData.userAnalytics;\n        } else if (Array.isArray(userConsumptionData) && userConsumptionData.length > 0) {\n          userAnalyticsArray = userConsumptionData;\n        }\n      }\n      if (userAnalyticsArray && userAnalyticsArray.length > 0) {\n        this.chartOptions['userConsumption'] = this.createUserConsumptionChart(userAnalyticsArray);\n        this.sortedAnalytics = userAnalyticsArray;\n        this.noDataAvailable = false;\n        this.usageLoader = false;\n      } else {\n        this.chartOptions['userConsumption'] = this.createNoDataChart('User Consumption');\n        this.sortedAnalytics = [];\n        this.noDataAvailable = true;\n        this.usageLoader = false;\n      }\n      if (mainData.linesOfCodeAnalytics && mainData.linesOfCodeAnalytics.length > 0) {\n        const categories = mainData.linesOfCodeAnalytics.map(item => item.date);\n        const values = mainData.linesOfCodeAnalytics.map(item => item.count);\n        this.chartOptions['linesOfCodeProcessed'] = this.createLineChart('Lines of code Processed', categories, values, 'Lines of Code');\n      } else {\n        this.chartOptions['linesOfCodeProcessed'] = this.createNoDataChart('Lines of code Processed');\n      }\n      if (mainData.useCaseLevelAnalytics && mainData.useCaseLevelAnalytics.length > 0) {\n        const sortedUseCases = mainData.useCaseLevelAnalytics.sort((a, b) => b.count - a.count).slice(0, 5);\n        const categories = sortedUseCases.map(item => item.useCaseCode);\n        const values = sortedUseCases.map(item => item.count);\n        this.chartOptions['topUseCases'] = this.createColumnChart('Top 5 Individual Agents', categories, values, 'Usage Count', 'agents');\n      } else {\n        this.chartOptions['topUseCases'] = this.createNoDataChart('Top 5 Individual Agents');\n      }\n      let requestData = mainData.numberOfRequestsAnalytics || mainData.numberOfRequestAnalytics;\n      if (requestData && requestData.length > 0) {\n        const categories = requestData.map(item => item.date);\n        const values = requestData.map(item => item.requestCount);\n        this.chartOptions['numberOfRequests'] = this.createLineChart('Number of Requests', categories, values, 'Request Count');\n      } else {\n        this.chartOptions['numberOfRequests'] = this.createNoDataChart('Number of Requests');\n      }\n      if (mainData.programmingLanguageAnalytics && mainData.programmingLanguageAnalytics.length > 0) {\n        const categories = mainData.programmingLanguageAnalytics.map(item => item.programmingLanguage);\n        const values = mainData.programmingLanguageAnalytics.map(item => item.count);\n        this.chartOptions['topLanguages'] = this.createColumnChart('Top 5 Languages / Frameworks', categories, values, 'Usage Count', 'languages');\n      } else {\n        this.chartOptions['topLanguages'] = this.createNoDataChart('Top 5 Languages / Frameworks');\n      }\n      if (mainData.userResponseAnalytics && mainData.userResponseAnalytics.length > 0) {\n        const pieData = mainData.userResponseAnalytics.map(item => [item.response, item.percentage]);\n        this.chartOptions['userResponse'] = this.createPieChart('User Response', pieData, 'userResponse');\n      } else {\n        this.chartOptions['userResponse'] = this.createNoDataChart('User Response');\n      }\n      let activeUsers = 0;\n      let inactiveUsers = 0;\n      let totalUsers = 0;\n      if (userActivityData && userActivityData.userActivity) {\n        const activity = userActivityData.userActivity;\n        activeUsers = activity.activeUsers || 0;\n        inactiveUsers = activity.inactiveUsers || 0;\n        totalUsers = activity.totalUsers || 0;\n      } else if (userActivityData) {\n        activeUsers = userActivityData.activeUsers || 0;\n        inactiveUsers = userActivityData.inactiveUsers || 0;\n        totalUsers = userActivityData.totalUsers || 0;\n      }\n      const total = totalUsers || activeUsers + inactiveUsers;\n      console.log('Active Users:', activeUsers, 'Inactive Users:', inactiveUsers, 'Total:', total);\n      if (total > 0) {\n        const inactivePercentage = inactiveUsers / total * 100;\n        const activePercentage = activeUsers / total * 100;\n        const pieData = [['Active Users', activePercentage], ['Inactive Users', inactivePercentage]];\n        this.chartOptions['userActivity'] = this.createPieChart('% Dormant User', pieData, 'dormantUser');\n        this.userActivity = {\n          activeUsers,\n          dormantUsers: inactiveUsers,\n          totalUsers: total\n        };\n      } else {\n        this.chartOptions['userActivity'] = this.createNoDataChart('% Dormant User');\n      }\n      let categories = [];\n      let values = [];\n      if (responseTimeData) {\n        if (responseTimeData.responseTimes && Array.isArray(responseTimeData.responseTimes)) {\n          categories = responseTimeData.responseTimes.map(item => item.createdAt);\n          values = responseTimeData.responseTimes.map(item => item.responseTime);\n        } else if (responseTimeData.categories && responseTimeData.series) {\n          categories = responseTimeData.categories;\n          values = responseTimeData.series;\n        } else if (responseTimeData.categories && responseTimeData.ySeries) {\n          categories = responseTimeData.categories;\n          values = responseTimeData.ySeries;\n        } else if (Array.isArray(responseTimeData) && responseTimeData.length > 0) {\n          categories = responseTimeData.map(item => item.createdAt || item.date);\n          values = responseTimeData.map(item => item.responseTime || item.time);\n        }\n      }\n      if (categories.length > 0 && values.length > 0) {\n        this.chartOptions['responseTime'] = this.createLineChart('Response Time', categories, values, 'Response Time (ms)');\n      } else {\n        this.chartOptions['responseTime'] = this.createNoDataChart('Response Time');\n      }\n    }\n    loadAgentData() {\n      this.isLoading = true;\n      const formattedDateRange = this.getFormattedDateRangeForAPI();\n      this.analyticsService.getAgenticAIAnalytics(formattedDateRange).pipe(takeUntil(this.destroy$)).subscribe({\n        next: data => {\n          this.processAgentAnalyticsData(data);\n          this.loadAgentCharts(data);\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('Error loading agent data:', error);\n          this.isLoading = false;\n        }\n      });\n    }\n    processAgentAnalyticsData(data) {\n      this.agentMetrics = data;\n      this.userActivityStats = data.userActivityStats;\n      this.toolUsage = data.toolUsage || [];\n      this.userConsumption = data.userConsumption || [];\n    }\n    toggleRowExpansion(index) {\n      if (this.expandedRows.has(index)) {\n        this.expandedRows.delete(index);\n      } else {\n        this.expandedRows.add(index);\n      }\n    }\n    isRowExpanded(index) {\n      return this.expandedRows.has(index);\n    }\n    loadAgentMetricsTable(data) {\n      this.agentMetricsLoader = true;\n      if (data && data.agentMetrics && data.agentMetrics.length > 0) {\n        this.agentMetricsTableData = data.agentMetrics;\n        this.agentMetricsNoDataAvailable = false;\n      } else {\n        this.agentMetricsTableData = [];\n        this.agentMetricsNoDataAvailable = true;\n      }\n      this.agentMetricsLoader = false;\n    }\n    loadAgentCharts(data) {\n      this.agentMetrics = data;\n      if (data.studioUsage && data.studioUsage.length > 0) {\n        const studioUsageData = data.studioUsage.map(item => [item.domainName, item.percentage]);\n        this.chartOptions['studioUsage'] = this.createPieChart('Studio Usage', studioUsageData, 'studioUsage');\n      } else {\n        this.chartOptions['studioUsage'] = this.createNoDataChart('Studio Usage');\n      }\n      if (data.topAgents && data.topAgents.length > 0) {\n        const categories = data.topAgents.slice(0, 5).map(agent => agent.agentName);\n        const series = data.topAgents.slice(0, 5).map(agent => agent.usageCount);\n        this.chartOptions['topAgents'] = this.createColumnChart('Top 5 Collaborative Agents', categories, series, 'Usage Count', 'topAgents');\n      } else {\n        this.chartOptions['topAgents'] = this.createNoDataChart('Top 5 Collaborative Agents');\n      }\n      if (data.agentCreated && data.agentCreated.length > 0) {\n        const categories = data.agentCreated.map(item => item.teamName);\n        const series = data.agentCreated.map(item => item.usageCount);\n        this.chartOptions['agentCreated'] = this.createColumnChart('Collaborative Agent Created', categories, series, 'Usage Count', 'agentCreated');\n      } else {\n        this.chartOptions['agentCreated'] = this.createNoDataChart('Collaborative Agent Created');\n      }\n      this.loadAgentMetricsTable(data);\n      if (data.toolAnalytics && data.toolAnalytics.length > 0) {\n        const toolAnalyticsItem = data.toolAnalytics[0];\n        const toolAnalyticsData = [['User Defined Tools', toolAnalyticsItem.userDefinedPercentage], ['Built-in Tools', toolAnalyticsItem.builtInPercentage]];\n        this.chartOptions['toolAnalytics'] = this.createPieChart('Tool Analytics', toolAnalyticsData, 'default');\n      } else {\n        this.chartOptions['toolAnalytics'] = this.createNoDataChart('Tool Analytics');\n      }\n      if (data.toolUsage && data.toolUsage.length > 0) {\n        const categories = data.toolUsage.slice(0, 5).map(tool => tool.toolName);\n        const series = data.toolUsage.slice(0, 5).map(tool => tool.usageCount);\n        this.chartOptions['toolUsage'] = this.createHorizontalBarChart('Tool Usage', categories, series, 'Usage Count', 'toolUsage');\n      } else {\n        this.chartOptions['toolUsage'] = this.createNoDataChart('Tool Usage');\n      }\n      if (data.adoptionRate && data.adoptionRate.length > 0) {\n        const categories = data.adoptionRate.map(item => item.workflowName);\n        const series = data.adoptionRate.map(item => item.executionCount);\n        this.chartOptions['adoptionRate'] = this.createLineChart('Adoption Rate', categories, series, 'Execution Count');\n      } else {\n        this.chartOptions['adoptionRate'] = this.createNoDataChart('Adoption Rate');\n      }\n      if (data.userConsumption && data.userConsumption.length > 0) {\n        const transformedData = data.userConsumption.map(user => ({\n          userSignature: user.email,\n          requestCount: user.consumptionCount\n        }));\n        this.chartOptions['userConsumption'] = this.createUserConsumptionChart(transformedData);\n      } else {\n        this.chartOptions['userConsumption'] = this.createNoDataChart('User Consumption');\n      }\n      if (data.userActivityStats && Array.isArray(data.userActivityStats) && data.userActivityStats.length > 0) {\n        const userStats = data.userActivityStats[0];\n        const activePercentage = userStats.activeUserPercentage || 0;\n        const inactivePercentage = userStats.inactiveUserPercentage || 0;\n        console.log('User stats:', userStats);\n        console.log('Active percentage:', activePercentage);\n        console.log('Inactive percentage:', inactivePercentage);\n        if (activePercentage + inactivePercentage > 0) {\n          const dormantUserData = [['Active Users', Math.round(activePercentage * 100) / 100], ['Inactive Users', Math.round(inactivePercentage * 100) / 100]];\n          console.log('Dormant User Chart Data:', dormantUserData);\n          this.chartOptions['userActivity'] = this.createPieChart('% Dormant User', dormantUserData, 'dormantUser');\n        } else {\n          this.chartOptions['userActivity'] = this.createNoDataChart('% Dormant User');\n        }\n      } else {\n        console.log('No user activity stats found');\n        this.chartOptions['userActivity'] = this.createNoDataChart('% Dormant User');\n      }\n    }\n    // Chart creation methods\n    createLineChart(title, categories, data, seriesName) {\n      // Get professional color based on chart title\n      let chartColor = this.CHART_COLORS.primary;\n      if (title.includes('Lines of code')) {\n        chartColor = this.PROFESSIONAL_COLORS.linesOfCode;\n      } else if (title.includes('Number of Requests')) {\n        chartColor = this.PROFESSIONAL_COLORS.numberOfRequests;\n      } else if (title.includes('Response Time')) {\n        chartColor = this.PROFESSIONAL_COLORS.responseTime;\n      } else if (title.includes('Adoption Rate')) {\n        chartColor = this.PROFESSIONAL_COLORS.adoptionRate;\n      }\n      const commonConfig = this.getCommonChartConfig();\n      return {\n        ...commonConfig,\n        chart: {\n          ...commonConfig.chart,\n          type: 'line',\n          height: this.CHART_DEFAULTS.height,\n          zoomType: 'x',\n          panning: {\n            enabled: true,\n            type: 'x'\n          },\n          panKey: 'shift'\n        },\n        xAxis: {\n          categories: categories,\n          labels: {\n            rotation: -45,\n            style: {\n              color: '#6B7280',\n              fontSize: '10px',\n              fontFamily: 'Inter, sans-serif'\n            }\n          },\n          lineColor: '#E5E7EB',\n          tickColor: '#E5E7EB'\n        },\n        yAxis: {\n          title: {\n            text: '',\n            style: {\n              color: '#6B7280'\n            }\n          },\n          labels: {\n            style: {\n              color: '#6B7280',\n              fontSize: '10px',\n              fontFamily: 'Inter, sans-serif'\n            }\n          },\n          gridLineColor: '#F3F4F6'\n        },\n        series: [{\n          name: seriesName,\n          type: 'line',\n          data: data,\n          color: chartColor,\n          lineWidth: 3,\n          marker: {\n            fillColor: chartColor,\n            lineColor: '#FFFFFF',\n            lineWidth: 2,\n            radius: 5\n          },\n          shadow: {\n            color: chartColor,\n            opacity: 0.3,\n            width: 3\n          }\n        }],\n        plotOptions: {\n          line: {\n            dataLabels: {\n              enabled: false\n            },\n            marker: {\n              enabled: true\n            }\n          }\n        }\n      };\n    }\n    createColumnChart(title, categories, data, seriesName, chartType = 'default') {\n      // Get professional color based on chart type\n      let color = this.PROFESSIONAL_COLORS.topUseCases; // Default green\n      switch (chartType) {\n        case 'languages':\n          color = this.PROFESSIONAL_COLORS.topLanguages; // Red for languages\n          break;\n        case 'topAgents':\n          color = this.PROFESSIONAL_COLORS.topAgents; // Pink for top collaborative agents\n          break;\n        case 'agentCreated':\n          color = this.PROFESSIONAL_COLORS.agentCreated; // Teal for agent creation\n          break;\n        case 'agents':\n          color = this.PROFESSIONAL_COLORS.topUseCases; // Green for individual agents\n          break;\n        default:\n          color = this.PROFESSIONAL_COLORS.topUseCases;\n        // Default green\n      }\n      const commonConfig = this.getCommonChartConfig();\n      return {\n        ...commonConfig,\n        chart: {\n          ...commonConfig.chart,\n          type: 'column',\n          height: this.CHART_DEFAULTS.height,\n          zoomType: 'x',\n          panning: {\n            enabled: true,\n            type: 'x'\n          },\n          panKey: 'shift'\n        },\n        xAxis: {\n          categories: categories,\n          labels: {\n            style: {\n              color: '#6B7280',\n              fontSize: '10px',\n              fontFamily: 'Inter, sans-serif'\n            }\n          },\n          lineColor: '#E5E7EB',\n          tickColor: '#E5E7EB'\n        },\n        yAxis: {\n          title: {\n            text: '',\n            style: {\n              color: '#6B7280'\n            }\n          },\n          labels: {\n            style: {\n              color: '#6B7280',\n              fontSize: '10px',\n              fontFamily: 'Inter, sans-serif'\n            }\n          },\n          gridLineColor: '#F3F4F6'\n        },\n        series: [{\n          name: seriesName,\n          type: 'column',\n          data: data,\n          color: color,\n          borderWidth: 0,\n          borderRadius: 6,\n          shadow: {\n            color: color,\n            opacity: 0.2,\n            width: 2\n          }\n        }],\n        plotOptions: {\n          column: {\n            dataLabels: {\n              enabled: true,\n              style: {\n                color: '#333333',\n                // Dark text for better visibility\n                fontSize: '12px',\n                fontWeight: '600',\n                textOutline: '1px contrast' // Add text outline for better readability\n              }\n            },\n            borderRadius: 4,\n            pointPadding: 0.2,\n            // Uniform spacing between bars\n            groupPadding: 0.15,\n            // Uniform spacing between groups\n            maxPointWidth: 60 // Maximum bar width for consistency\n          }\n        }\n      };\n    }\n    createPieChart(title, data, chartType = 'default') {\n      // Professional color schemes for different pie chart types\n      let colors = [];\n      if (chartType === 'userResponse') {\n        colors = this.PROFESSIONAL_COLORS.userResponse; // Multi-color palette\n      } else if (chartType === 'dormantUser') {\n        colors = this.PROFESSIONAL_COLORS.userActivity; // Green/Red for active/inactive\n      } else if (chartType === 'studioUsage') {\n        colors = this.PROFESSIONAL_COLORS.studioUsage; // Multi-color for studio usage\n      } else if (chartType === 'default') {\n        colors = this.PROFESSIONAL_COLORS.toolAnalytics; // Orange/Purple for tool analytics\n      } else {\n        colors = [this.CHART_COLORS.primary, this.CHART_COLORS.secondary]; // Fallback\n      }\n      const commonConfig = this.getCommonChartConfig();\n      return {\n        ...commonConfig,\n        chart: {\n          ...commonConfig.chart,\n          type: 'pie',\n          height: this.CHART_DEFAULTS.height\n        },\n        series: [{\n          name: 'Percentage',\n          type: 'pie',\n          data: data,\n          innerSize: '60%',\n          colors: colors,\n          dataLabels: {\n            enabled: false\n          }\n        }],\n        legend: {\n          enabled: true,\n          align: 'right',\n          verticalAlign: 'middle',\n          layout: 'vertical',\n          itemStyle: {\n            color: '#374151',\n            fontSize: '11px',\n            fontFamily: 'Inter, sans-serif',\n            fontWeight: '500'\n          },\n          symbolRadius: 8,\n          itemMarginBottom: 8\n        },\n        plotOptions: {\n          pie: {\n            allowPointSelect: false,\n            cursor: 'pointer',\n            dataLabels: {\n              enabled: false\n            },\n            showInLegend: true,\n            borderWidth: 0\n          }\n        }\n      };\n    }\n    createUserConsumptionChart(data) {\n      const categories = data.map(item => item.userSignature || item.email || item.userName || 'Unknown User');\n      const values = data.map(item => item.requestCount || item.usageCount || item.count || 0);\n      // Professional height for better visual appearance\n      const chartHeight = this.CHART_DEFAULTS.height;\n      const showScrollbar = categories.length > 8; // Show scrollbar if more than 8 users\n      return {\n        chart: {\n          type: 'bar',\n          backgroundColor: this.CHART_DEFAULTS.backgroundColor,\n          height: chartHeight,\n          marginLeft: 200,\n          // Reduced margin for better space utilization\n          marginRight: 80,\n          // Add right margin for value labels\n          scrollablePlotArea: showScrollbar ? {\n            minHeight: categories.length * 30 + 80,\n            // Reduced spacing\n            scrollPositionY: 0\n          } : undefined,\n          style: {\n            fontFamily: this.CHART_DEFAULTS.fontFamily\n          }\n        },\n        title: {\n          text: '',\n          style: {\n            display: 'none'\n          }\n        },\n        xAxis: {\n          categories: categories,\n          labels: {\n            style: {\n              color: '#333333',\n              fontSize: '11px',\n              fontWeight: '500',\n              fontFamily: 'Mulish, sans-serif'\n            },\n            overflow: 'allow',\n            step: 1,\n            useHTML: true,\n            formatter: function () {\n              // Show full email at the start of each bar\n              const email = this.value;\n              return `<div style=\"width: 180px; text-align: left; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;\">${email}</div>`;\n            }\n          },\n          lineColor: 'transparent',\n          tickColor: 'transparent',\n          min: 0,\n          max: showScrollbar ? Math.min(10, categories.length - 1) : undefined // Show 11 items initially if scrollable\n        },\n        yAxis: {\n          title: {\n            text: '',\n            style: {\n              color: '#666666'\n            }\n          },\n          labels: {\n            style: {\n              color: '#666666',\n              fontSize: '10px',\n              fontFamily: 'Mulish, sans-serif'\n            }\n          },\n          gridLineColor: 'transparent',\n          lineColor: 'transparent'\n        },\n        series: [{\n          name: 'Usage',\n          type: 'bar',\n          data: values,\n          color: '#8B7EC8',\n          borderWidth: 0,\n          borderRadius: 2\n        }],\n        legend: {\n          enabled: false\n        },\n        plotOptions: {\n          bar: {\n            dataLabels: {\n              enabled: true,\n              inside: false,\n              align: 'right',\n              x: 5,\n              // Position labels slightly to the right of bars\n              style: {\n                color: '#333333',\n                fontSize: '12px',\n                fontWeight: '600',\n                fontFamily: 'Mulish, sans-serif',\n                textOutline: 'none'\n              },\n              formatter: function () {\n                return this.y;\n              }\n            },\n            color: '#8B7EC8',\n            borderRadius: 4,\n            pointPadding: 0.15,\n            // Reduced padding for better fit\n            groupPadding: 0.05,\n            // Reduced group padding\n            maxPointWidth: 25 // Reduced max width for better proportion\n          }\n        },\n        tooltip: {\n          enabled: true,\n          formatter: function () {\n            return '<b>' + this.x + '</b><br/>Requests: ' + this.y;\n          }\n        },\n        navigation: {\n          buttonOptions: {\n            enabled: true,\n            theme: {\n              stroke: '#8B7EC8',\n              r: 2,\n              states: {\n                hover: {\n                  fill: '#8B7EC8',\n                  stroke: '#8B7EC8'\n                },\n                select: {\n                  fill: '#8B7EC8',\n                  stroke: '#8B7EC8'\n                }\n              }\n            }\n          }\n        },\n        exporting: {\n          enabled: false\n        },\n        credits: {\n          enabled: false\n        }\n      };\n    }\n    createNoDataChart(title) {\n      return {\n        chart: {\n          backgroundColor: 'transparent'\n        },\n        title: {\n          text: title,\n          align: 'left',\n          style: {\n            color: 'var(--text-color)',\n            fontSize: '16px',\n            fontWeight: '600'\n          }\n        },\n        series: [],\n        lang: {\n          noData: 'No data available for the selected period'\n        },\n        noData: {\n          style: {\n            fontWeight: 'bold',\n            fontSize: '15px',\n            color: 'var(--text-secondary)'\n          }\n        },\n        exporting: {\n          enabled: false\n        },\n        credits: {\n          enabled: false\n        }\n      };\n    }\n    // Reusable method to create charts with no-data fallback\n    createChartWithFallback(data, chartCreator, title) {\n      if (data && data.length > 0) {\n        return chartCreator(data);\n      } else {\n        return this.createNoDataChart(title);\n      }\n    }\n    // Common chart styling configuration\n    getCommonChartConfig() {\n      return {\n        chart: {\n          backgroundColor: 'transparent'\n        },\n        title: {\n          text: '',\n          style: {\n            display: 'none'\n          }\n        },\n        exporting: {\n          enabled: false\n        },\n        credits: {\n          enabled: false\n        },\n        legend: {\n          enabled: false\n        }\n      };\n    }\n    // Chart callback functions\n    chartCallback = (chart, chartKey) => {\n      this.chartRefs[chartKey] = chart;\n    };\n    // Export methods\n    downloadExcel(analyticsName) {\n      const formattedDateRange = this.getFormattedDateRangeForAPI();\n      this.analyticsService.downloadChartExcel(formattedDateRange, analyticsName, 'excel');\n    }\n    downloadAgentExcel(analyticsName) {\n      const formattedDateRange = this.getFormattedDateRangeForAPI();\n      this.analyticsService.downloadAgenticAIExcel(formattedDateRange, analyticsName, 'excel');\n    }\n    downloadDump() {\n      const formattedDateRange = this.getFormattedDateRangeForAPI();\n      this.analyticsService.downloadDump(formattedDateRange);\n    }\n    // Filter and dropdown methods\n    onUseCaseSelectionChanged(event) {\n      this.selectedUseCases = event.value;\n      this.filteringEnabled = this.selectedUseCases.length > 0;\n    }\n    setPayload() {\n      // Apply filters and reload data\n      this.applyFilter();\n    }\n    toggleDownloadOptions(event) {\n      event.stopPropagation();\n      this.showDownloadOptions = !this.showDownloadOptions;\n    }\n    downloadChartsAsPDF() {\n      // Implementation for PDF download\n      console.log('Downloading charts as PDF...');\n      this.showDownloadOptions = false;\n    }\n    // Digital Ascender Style Horizontal Bar Chart\n    createHorizontalBarChart(title, categories, data, seriesName, chartType = 'default') {\n      // Professional colors for horizontal bar charts\n      let color = this.PROFESSIONAL_COLORS.toolUsage; // Default orange for tool usage\n      if (chartType === 'toolUsage') {\n        color = this.PROFESSIONAL_COLORS.toolUsage; // Professional orange for tool usage\n      } else if (chartType === 'userConsumption') {\n        color = this.PROFESSIONAL_COLORS.collaborativeUserConsumption; // Pink for collaborative user consumption\n      }\n      // Professional height for better visual appearance\n      const chartHeight = this.CHART_DEFAULTS.height;\n      const showScrollbar = categories.length > 8; // Show scrollbar if more than 8 items\n      const commonConfig = this.getCommonChartConfig();\n      return {\n        ...commonConfig,\n        chart: {\n          ...commonConfig.chart,\n          type: 'bar',\n          height: chartHeight,\n          marginLeft: 180,\n          // Reduced margin for better space utilization\n          marginRight: 80,\n          // Add right margin for value labels\n          scrollablePlotArea: showScrollbar ? {\n            minHeight: categories.length * 30 + 80,\n            // Reduced spacing\n            scrollPositionY: 0\n          } : undefined,\n          zoomType: 'x',\n          panning: {\n            enabled: true,\n            type: 'x'\n          },\n          panKey: 'shift'\n        },\n        xAxis: {\n          categories: categories,\n          title: {\n            text: null\n          },\n          labels: {\n            style: {\n              color: '#333333',\n              fontSize: '11px',\n              fontWeight: '500',\n              fontFamily: 'Mulish, sans-serif'\n            },\n            overflow: 'allow',\n            step: 1,\n            useHTML: true,\n            formatter: function () {\n              // Show full name at the start of each bar\n              const name = this.value;\n              return `<div style=\"width: 160px; text-align: left; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;\">${name}</div>`;\n            }\n          },\n          lineColor: 'transparent',\n          tickColor: 'transparent',\n          min: 0,\n          max: showScrollbar ? Math.min(10, categories.length - 1) : undefined // Show 11 items initially if scrollable\n        },\n        yAxis: {\n          min: 0,\n          title: {\n            text: '',\n            style: {\n              color: '#666666'\n            }\n          },\n          labels: {\n            style: {\n              color: '#666666',\n              fontSize: '10px',\n              fontFamily: 'Mulish, sans-serif'\n            }\n          },\n          gridLineColor: 'transparent',\n          lineColor: 'transparent'\n        },\n        plotOptions: {\n          bar: {\n            dataLabels: {\n              enabled: true,\n              inside: false,\n              align: 'right',\n              x: 5,\n              // Position labels slightly to the right of bars\n              style: {\n                color: '#333333',\n                fontSize: '12px',\n                fontWeight: '600',\n                fontFamily: 'Mulish, sans-serif',\n                textOutline: 'none'\n              },\n              formatter: function () {\n                return this.y;\n              }\n            },\n            color: color,\n            borderRadius: 4,\n            pointPadding: 0.15,\n            // Reduced padding for better fit\n            groupPadding: 0.05,\n            // Reduced group padding\n            maxPointWidth: 25,\n            // Reduced max width for better proportion\n            borderWidth: 0\n          }\n        },\n        series: [{\n          type: 'bar',\n          name: seriesName,\n          data: data,\n          color: color\n        }],\n        legend: {\n          enabled: false\n        },\n        tooltip: {\n          enabled: true,\n          formatter: function () {\n            return '<b>' + this.x + '</b><br/>' + seriesName + ': ' + this.y;\n          }\n        }\n      };\n    }\n    // Common chart configuration helper\n    getBaseChartConfig(type, height = this.CHART_DEFAULTS.height) {\n      return {\n        chart: {\n          type,\n          backgroundColor: this.CHART_DEFAULTS.backgroundColor,\n          height,\n          style: {\n            fontFamily: this.CHART_DEFAULTS.fontFamily\n          }\n        },\n        title: {\n          text: '',\n          style: {\n            display: 'none'\n          }\n        },\n        credits: {\n          enabled: false\n        },\n        legend: {\n          enabled: false\n        }\n      };\n    }\n    // Utility methods for bar charts\n    getBarWidth(value, isToolUsage = false) {\n      if (isToolUsage) {\n        const maxValue = Math.max(...this.toolUsage.map(tool => tool.usageCount));\n        const calculatedWidth = maxValue > 0 ? value / maxValue * 100 : 0;\n        // Ensure minimum width of 25% to show tool names\n        return Math.max(calculatedWidth, 25);\n      } else {\n        // Handle both individual and collaborative user consumption data\n        let maxValue = 0;\n        if (this.activeTab === 'usecase' && this.sortedAnalytics && this.sortedAnalytics.length > 0) {\n          // Individual tab - use sortedAnalytics\n          maxValue = Math.max(...this.sortedAnalytics.map(user => user.requestCount || 0));\n        } else if (this.activeTab === 'agents' && this.userConsumption && this.userConsumption.length > 0) {\n          // Collaborative tab - use userConsumption\n          maxValue = Math.max(...this.userConsumption.map(user => user.consumptionCount || user.requestCount || 0));\n        }\n        const calculatedWidth = maxValue > 0 ? value / maxValue * 100 : 0;\n        // Ensure minimum width of 30% to show email addresses\n        return Math.max(calculatedWidth, 30);\n      }\n    }\n    // Navigation methods\n    goToLangfuse() {\n      window.open(this.langfuseUrl, '_blank');\n    }\n    goToTrulens() {\n      window.open(this.ICLAnalyticsUrl, '_blank');\n    }\n    goToIclAnalytics() {\n      window.open('https://aava-trulens-dev.avateam.io/', '_blank');\n    }\n    onRangeSelected(event) {\n      if (event && event.startDate && event.endDate) {\n        // Convert dates to the format expected by the component\n        const startDate = new Date(event.startDate);\n        const endDate = new Date(event.endDate);\n        this.dateRange.fromDate = startDate.toISOString().split('T')[0];\n        this.dateRange.toDate = endDate.toISOString().split('T')[0];\n        // Update form controls\n        this.vmFG.patchValue({\n          fromDate: this.dateRange.fromDate,\n          toDate: this.dateRange.toDate\n        });\n        // Don't auto-apply filter - let user click the filter button\n        console.log('Date range selected:', this.dateRange);\n      }\n    }\n    getFormattedDateRange() {\n      if (!this.dateRange.fromDate || !this.dateRange.toDate) {\n        return 'No dates selected';\n      }\n      const fromDate = new Date(this.dateRange.fromDate);\n      const toDate = new Date(this.dateRange.toDate);\n      const options = {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n      };\n      return `${fromDate.toLocaleDateString('en-US', options)} to ${toDate.toLocaleDateString('en-US', options)}`;\n    }\n    getCalendarDateRange() {\n      if (!this.dateRange.fromDate || !this.dateRange.toDate) {\n        return {\n          start: null,\n          end: null\n        };\n      }\n      return {\n        start: new Date(this.dateRange.fromDate),\n        end: new Date(this.dateRange.toDate)\n      };\n    }\n    onDocumentClick(event) {\n      const target = event.target;\n      const downloadButton = target.closest('.download-button');\n      const downloadDropdown = target.closest('.download-dropdown');\n      if (!downloadButton && !downloadDropdown) {\n        this.showDownloadOptions = false;\n      }\n    }\n    onDownloadToggle(event) {\n      event.stopPropagation();\n      this.showDownloadOptions = !this.showDownloadOptions;\n      console.log('Download toggle clicked, showDownloadOptions:', this.showDownloadOptions);\n    }\n    onPdfDownload() {\n      this.downloadChartsAsPDF();\n      this.showDownloadOptions = false;\n    }\n    onDataDownload() {\n      if (this.activeTab === 'usecase') {\n        this.downloadDump();\n      } else {\n        this.downloadAgentExcel('agentAnalytics');\n      }\n      this.showDownloadOptions = false;\n    }\n    static ɵfac = function AnalyticsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AnalyticsComponent)(i0.ɵɵdirectiveInject(i1.AnalyticsService), i0.ɵɵdirectiveInject(i2.FormBuilder));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AnalyticsComponent,\n      selectors: [[\"app-analytics\"]],\n      hostBindings: function AnalyticsComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function AnalyticsComponent_click_HostBindingHandler($event) {\n            return ctx.onDocumentClick($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      decls: 10,\n      vars: 6,\n      consts: [[1, \"analytics-container\"], [1, \"analytics-header\"], [1, \"analytics-tabs\"], [\"label\", \"Individual\", \"size\", \"large\", 3, \"userClick\", \"variant\", \"pill\"], [\"label\", \"Collaborative\", \"size\", \"large\", 3, \"userClick\", \"variant\", \"pill\"], [1, \"analytics-content\"], [\"class\", \"tab-content\", 4, \"ngIf\"], [1, \"tab-content\"], [1, \"analytics-controls\", 3, \"formGroup\"], [1, \"controls-row\"], [1, \"left-buttons\"], [\"label\", \"Langfuse\", \"variant\", \"secondary\", \"size\", \"medium\", 3, \"userClick\"], [\"label\", \"ICL Analytics\", \"variant\", \"secondary\", \"size\", \"medium\", 3, \"userClick\"], [\"label\", \"Download\", \"iconPosition\", \"left\", \"size\", \"medium\", \"iconName\", \"Download\", \"variant\", \"secondary\", 1, \"download-button\", 3, \"userClick\", \"click\", \"iconSize\"], [\"class\", \"download-dropdown\", 4, \"ngIf\"], [1, \"right-calendar\"], [3, \"rangeSelected\", \"isRange\", \"dateRange\"], [\"variant\", \"secondary\", \"iconName\", \"Funnel\", \"iconPosition\", \"only\", \"size\", \"medium\", \"title\", \"Apply Filter\", 1, \"filter-button\", 3, \"userClick\"], [1, \"metrics-container\"], [1, \"metrics-row\"], [3, \"type\", \"iconName\", \"title\", \"value\", \"description\"], [1, \"charts-grid\"], [1, \"chart-container\"], [1, \"chart-header\"], [\"iconName\", \"Download\", \"iconSize\", \"20\", \"class\", \"chart-download-icon\", 3, \"click\", 4, \"ngIf\"], [1, \"chart-content1\"], [\"class\", \"chart-loading\", 4, \"ngIf\"], [\"class\", \"no-data-message\", 4, \"ngIf\"], [\"class\", \"bars-container\", 4, \"ngIf\"], [\"iconName\", \"Download\", \"iconSize\", \"20\", 1, \"chart-download-icon\", 3, \"click\"], [\"style\", \"width: 100%; height: 300px;\", 3, \"Highcharts\", \"options\", 4, \"ngIf\"], [1, \"download-dropdown\"], [1, \"dropdown-item\", 3, \"click\"], [\"iconName\", \"FileText\"], [\"iconName\", \"Database\"], [1, \"chart-loading\"], [1, \"loading-spinner\"], [1, \"no-data-message\"], [1, \"error-status\"], [1, \"fas\", \"fa-exclamation-triangle\", \"no-data-icon\"], [1, \"no-data\"], [1, \"bars-container\"], [\"class\", \"bar-container\", 4, \"ngFor\", \"ngForOf\"], [1, \"bar-container\"], [1, \"bar-wrapper\"], [1, \"bar\"], [1, \"bar-label\"], [\"class\", \"no-data-bar\", 4, \"ngIf\"], [1, \"bar-value\"], [1, \"no-data-bar\"], [2, \"width\", \"100%\", \"height\", \"300px\", 3, \"Highcharts\", \"options\"], [1, \"fas\", \"fa-chart-line\"], [\"class\", \"agent-metrics-table-container\", 4, \"ngIf\"], [1, \"tool-usage\"], [1, \"tool-usage-container\"], [\"class\", \"tool-loader\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"chart-content\"], [1, \"agent-metrics-table-container\"], [1, \"table-wrapper\"], [1, \"agent-metrics-table\"], [1, \"expand-column\"], [4, \"ngFor\", \"ngForOf\"], [1, \"agent-row\"], [\"class\", \"expand-btn\", 3, \"expanded\", \"title\", \"click\", 4, \"ngIf\"], [1, \"agent-name\"], [1, \"workflow-count\"], [\"class\", \"workflow-details-row\", 4, \"ngIf\"], [1, \"expand-btn\", 3, \"click\", \"title\"], [\"width\", \"16\", \"height\", \"16\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"d\", \"M9 18L15 12L9 6\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", 4, \"ngIf\"], [\"d\", \"M6 9L12 15L18 9\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", 4, \"ngIf\"], [\"d\", \"M9 18L15 12L9 6\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M6 9L12 15L18 9\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"workflow-details-row\"], [\"colspan\", \"3\"], [1, \"workflow-details\"], [1, \"workflow-header\"], [1, \"workflow-list\"], [\"class\", \"workflow-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"workflow-item\"], [1, \"tool-loader\"], [1, \"tool-usage-header\"], [1, \"bar\", \"tool-bar\"]],\n      template: function AnalyticsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h3\");\n          i0.ɵɵtext(3, \"Agent Analytics\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"div\", 2)(5, \"ava-button\", 3);\n          i0.ɵɵlistener(\"userClick\", function AnalyticsComponent_Template_ava_button_userClick_5_listener() {\n            return ctx.onTabChange({\n              id: \"usecase\",\n              label: \"Individual\"\n            });\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"ava-button\", 4);\n          i0.ɵɵlistener(\"userClick\", function AnalyticsComponent_Template_ava_button_userClick_6_listener() {\n            return ctx.onTabChange({\n              id: \"agents\",\n              label: \"Collaborative\"\n            });\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 5);\n          i0.ɵɵtemplate(8, AnalyticsComponent_div_8_Template, 78, 49, \"div\", 6)(9, AnalyticsComponent_div_9_Template, 81, 46, \"div\", 6);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"variant\", ctx.activeTab === \"usecase\" ? \"primary\" : \"secondary\")(\"pill\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"variant\", ctx.activeTab === \"agents\" ? \"primary\" : \"secondary\")(\"pill\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"usecase\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"agents\");\n        }\n      },\n      dependencies: [CommonModule, i3.NgForOf, i3.NgIf, FormsModule, i2.NgControlStatusGroup, ReactiveFormsModule, i2.FormGroupDirective, HighchartsChartModule, i4.HighchartsChartComponent, MatSelectModule, MatFormFieldModule, MatOptionModule, MatInputModule, ButtonComponent, CalendarComponent, TextCardComponent, IconComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.analytics-container[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n}\\n\\n.analytics-header[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.analytics-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: 600;\\n  color: var(--text-color);\\n  margin-bottom: 0.5rem;\\n}\\n.analytics-header[_ngcontent-%COMP%]   .analytics-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  color: var(--text-secondary);\\n  margin: 0 0 1.5rem 0;\\n}\\n\\n.analytics-tabs[_ngcontent-%COMP%] {\\n  margin-bottom: 10px;\\n}\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 3rem;\\n}\\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n  color: var(--text-secondary);\\n  font-size: 1.1rem;\\n}\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border: 4px solid var(--card-border);\\n  border-top: 4px solid var(--dashboard-primary);\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n.action-bar[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 2rem;\\n  padding: 1rem;\\n  background: var(--card-bg);\\n  border: 1px solid var(--card-border);\\n  border-radius: var(--card-border-radius);\\n  box-shadow: var(--card-box-shadow);\\n}\\n\\n.analytics-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  align-items: center;\\n}\\n\\n.bars-container[_ngcontent-%COMP%], .chart-container[_ngcontent-%COMP%]   .agent-metrics-table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%] {\\n  scrollbar-width: thin;\\n  scrollbar-color: rgba(139, 92, 246, 0.6) transparent;\\n}\\n.bars-container[_ngcontent-%COMP%]::-webkit-scrollbar, .chart-container[_ngcontent-%COMP%]   .agent-metrics-table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 8px;\\n}\\n.bars-container[_ngcontent-%COMP%]::-webkit-scrollbar-track, .chart-container[_ngcontent-%COMP%]   .agent-metrics-table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: rgba(0, 0, 0, 0.05);\\n  border-radius: 4px;\\n}\\n.bars-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb, .chart-container[_ngcontent-%COMP%]   .agent-metrics-table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background-color: rgba(139, 92, 246, 0.6);\\n  border-radius: 4px;\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n}\\n.bars-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover, .chart-container[_ngcontent-%COMP%]   .agent-metrics-table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background-color: rgba(139, 92, 246, 0.8);\\n}\\n\\n.usage-bar[_ngcontent-%COMP%], .user-consumption-section[_ngcontent-%COMP%]   .user-consumption-container[_ngcontent-%COMP%]   .user-consumption-grid[_ngcontent-%COMP%]   .user-consumption-card[_ngcontent-%COMP%]   .usage-bar-container[_ngcontent-%COMP%]   .usage-bar[_ngcontent-%COMP%] {\\n  background: var(--border-color);\\n  border-radius: 3px;\\n  overflow: hidden;\\n}\\n.usage-bar[_ngcontent-%COMP%]   .bar-fill[_ngcontent-%COMP%], .user-consumption-section[_ngcontent-%COMP%]   .user-consumption-container[_ngcontent-%COMP%]   .user-consumption-grid[_ngcontent-%COMP%]   .user-consumption-card[_ngcontent-%COMP%]   .usage-bar-container[_ngcontent-%COMP%]   .usage-bar[_ngcontent-%COMP%]   .bar-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));\\n  border-radius: 3px;\\n  transition: width 0.3s ease;\\n}\\n\\n.stat-card[_ngcontent-%COMP%], .tool-card[_ngcontent-%COMP%], .consumption-card[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  background: var(--agent-chat-column-bg, #f9f9f9);\\n  border: 1px solid var(--card-border);\\n  border-radius: 8px;\\n  transition: all 0.3s ease;\\n}\\n.stat-card[_ngcontent-%COMP%]:hover, .tool-card[_ngcontent-%COMP%]:hover, .consumption-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n\\n.user-activity-stats[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .tool-usage-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.3rem;\\n  font-weight: 600;\\n  color: var(--text-color);\\n  margin: 0 0 1.5rem 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.user-activity-stats[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before, .tool-usage-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  width: 4px;\\n  height: 20px;\\n  border-radius: 2px;\\n}\\n\\n.export-btn[_ngcontent-%COMP%], .filter-btn[_ngcontent-%COMP%], .download-toggle-btn[_ngcontent-%COMP%], .analytics-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.75rem 1.5rem;\\n  background-color: var(--nav-hover);\\n  color: var(--nav-text, #666D99);\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.export-btn[_ngcontent-%COMP%]:hover, .filter-btn[_ngcontent-%COMP%]:hover, .download-toggle-btn[_ngcontent-%COMP%]:hover, .analytics-btn[_ngcontent-%COMP%]:hover {\\n  background-color: var(--nav-hover);\\n  color: var(--nav-item-active-color);\\n}\\n.export-btn[_ngcontent-%COMP%]:hover:after, .filter-btn[_ngcontent-%COMP%]:hover:after, .download-toggle-btn[_ngcontent-%COMP%]:hover:after, .analytics-btn[_ngcontent-%COMP%]:hover:after {\\n  opacity: 0.1;\\n}\\n.export-btn[_ngcontent-%COMP%]:after, .filter-btn[_ngcontent-%COMP%]:after, .download-toggle-btn[_ngcontent-%COMP%]:after, .analytics-btn[_ngcontent-%COMP%]:after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  inset: 0;\\n  background: var(--gradient-primary);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n  z-index: -1;\\n}\\n.export-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .filter-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .download-toggle-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .analytics-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n\\n.analytics-btn[_ngcontent-%COMP%] {\\n  transform: translateY(0);\\n}\\n.analytics-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n}\\n\\n.download-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.download-button[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.download-toggle-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n}\\n\\n.download-dropdown[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 100%;\\n  right: 0;\\n  margin-top: 0.5rem;\\n  background: var(--card-bg);\\n  border: 1px solid var(--card-border);\\n  border-radius: 8px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n  z-index: 1000;\\n  width: 35%;\\n}\\n.download-dropdown[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  width: 100%;\\n  padding: 0.75rem 1rem;\\n  background: none;\\n  border: none;\\n  color: var(--text-color);\\n  font-size: 0.9rem;\\n  cursor: pointer;\\n  transition: background-color 0.2s ease;\\n}\\n.download-dropdown[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  background: var(--dashboard-bg-light);\\n}\\n.download-dropdown[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:first-child {\\n  border-radius: 8px 8px 0 0;\\n}\\n.download-dropdown[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:last-child {\\n  border-radius: 0 0 8px 8px;\\n}\\n\\n.date-filter-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n\\n.date-inputs[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n}\\n\\n.filter-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n}\\n\\n.filter-section[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n  padding: 1rem;\\n  background: var(--card-bg);\\n  border: 1px solid var(--card-border);\\n  border-radius: var(--card-border-radius);\\n  box-shadow: var(--card-box-shadow);\\n}\\n\\n.filter-dropdown[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 400px;\\n}\\n.filter-dropdown[_ngcontent-%COMP%]   .mat-mdc-form-field-wrapper[_ngcontent-%COMP%] {\\n  background: var(--card-bg);\\n}\\n.filter-dropdown[_ngcontent-%COMP%]   .mat-mdc-select-value[_ngcontent-%COMP%] {\\n  color: var(--text-color);\\n}\\n.filter-dropdown[_ngcontent-%COMP%]   .mat-mdc-form-field-outline[_ngcontent-%COMP%] {\\n  color: var(--card-border);\\n}\\n.filter-dropdown[_ngcontent-%COMP%]   .mat-mdc-form-field-label[_ngcontent-%COMP%] {\\n  color: var(--text-secondary);\\n}\\n\\n.user-consumption-section[_ngcontent-%COMP%] {\\n  margin-top: 2rem;\\n  padding: 1.5rem;\\n  background: var(--card-background);\\n  border: 1px solid var(--border-color);\\n  border-radius: 8px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.user-consumption-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.3rem;\\n  font-weight: 600;\\n  color: var(--text-color);\\n  margin: 0 0 1.5rem 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.user-consumption-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  width: 4px;\\n  height: 20px;\\n  background: var(--primary-color);\\n  border-radius: 2px;\\n}\\n.user-consumption-section[_ngcontent-%COMP%]   .user-consumption-container[_ngcontent-%COMP%]   .user-consumption-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 1rem;\\n  padding-bottom: 0.5rem;\\n  border-bottom: 1px solid var(--border-color);\\n}\\n.user-consumption-section[_ngcontent-%COMP%]   .user-consumption-container[_ngcontent-%COMP%]   .user-consumption-header[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: var(--text-color);\\n  font-size: 1rem;\\n}\\n.user-consumption-section[_ngcontent-%COMP%]   .user-consumption-container[_ngcontent-%COMP%]   .user-consumption-header[_ngcontent-%COMP%]   .export-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.5rem 1rem;\\n  background: var(--primary-color);\\n  color: white;\\n  border: none;\\n  border-radius: 4px;\\n  cursor: pointer;\\n  font-size: 0.875rem;\\n  transition: background-color 0.2s ease;\\n}\\n.user-consumption-section[_ngcontent-%COMP%]   .user-consumption-container[_ngcontent-%COMP%]   .user-consumption-header[_ngcontent-%COMP%]   .export-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--primary-hover);\\n}\\n.user-consumption-section[_ngcontent-%COMP%]   .user-consumption-container[_ngcontent-%COMP%]   .user-consumption-header[_ngcontent-%COMP%]   .export-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n.user-consumption-section[_ngcontent-%COMP%]   .user-consumption-container[_ngcontent-%COMP%]   .user-consumption-grid[_ngcontent-%COMP%] {\\n  max-height: 400px;\\n  overflow-y: auto;\\n  padding-right: 8px;\\n  scrollbar-width: thin;\\n  scrollbar-color: var(--border-color) transparent;\\n}\\n.user-consumption-section[_ngcontent-%COMP%]   .user-consumption-container[_ngcontent-%COMP%]   .user-consumption-grid[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.user-consumption-section[_ngcontent-%COMP%]   .user-consumption-container[_ngcontent-%COMP%]   .user-consumption-grid[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: transparent;\\n}\\n.user-consumption-section[_ngcontent-%COMP%]   .user-consumption-container[_ngcontent-%COMP%]   .user-consumption-grid[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background-color: var(--border-color);\\n  border-radius: 3px;\\n}\\n.user-consumption-section[_ngcontent-%COMP%]   .user-consumption-container[_ngcontent-%COMP%]   .user-consumption-grid[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background-color: var(--text-secondary);\\n}\\n.user-consumption-section[_ngcontent-%COMP%]   .user-consumption-container[_ngcontent-%COMP%]   .user-consumption-grid[_ngcontent-%COMP%]   .user-consumption-card[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.75rem;\\n  padding: 1rem;\\n  background: var(--secondary-background);\\n  border: 1px solid var(--border-color);\\n  border-radius: 6px;\\n  margin-bottom: 0.75rem;\\n  transition: all 0.2s ease;\\n}\\n.user-consumption-section[_ngcontent-%COMP%]   .user-consumption-container[_ngcontent-%COMP%]   .user-consumption-grid[_ngcontent-%COMP%]   .user-consumption-card[_ngcontent-%COMP%]:hover {\\n  background: var(--hover-background);\\n  border-color: var(--primary-color);\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n.user-consumption-section[_ngcontent-%COMP%]   .user-consumption-container[_ngcontent-%COMP%]   .user-consumption-grid[_ngcontent-%COMP%]   .user-consumption-card[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.25rem;\\n}\\n.user-consumption-section[_ngcontent-%COMP%]   .user-consumption-container[_ngcontent-%COMP%]   .user-consumption-grid[_ngcontent-%COMP%]   .user-consumption-card[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   .user-email[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: var(--text-color);\\n  font-size: 0.875rem;\\n  word-break: break-word;\\n}\\n.user-consumption-section[_ngcontent-%COMP%]   .user-consumption-container[_ngcontent-%COMP%]   .user-consumption-grid[_ngcontent-%COMP%]   .user-consumption-card[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   .consumption-count[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: var(--text-secondary);\\n  font-weight: 600;\\n}\\n.user-consumption-section[_ngcontent-%COMP%]   .user-consumption-container[_ngcontent-%COMP%]   .user-consumption-grid[_ngcontent-%COMP%]   .user-consumption-card[_ngcontent-%COMP%]   .usage-bar-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n}\\n.user-consumption-section[_ngcontent-%COMP%]   .user-consumption-container[_ngcontent-%COMP%]   .user-consumption-grid[_ngcontent-%COMP%]   .user-consumption-card[_ngcontent-%COMP%]   .usage-bar-container[_ngcontent-%COMP%]   .usage-bar[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 6px;\\n}\\n.user-consumption-section[_ngcontent-%COMP%]   .user-consumption-container[_ngcontent-%COMP%]   .user-consumption-grid[_ngcontent-%COMP%]   .user-consumption-card[_ngcontent-%COMP%]   .usage-bar-container[_ngcontent-%COMP%]   .usage-percentage[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n  color: var(--primary-color);\\n  min-width: 35px;\\n  text-align: right;\\n}\\n\\n.consumption-card[_ngcontent-%COMP%]:hover {\\n  border-color: var(--dashboard-primary);\\n}\\n\\n.user-info[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.user-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: var(--text-color);\\n  margin: 0 0 0.25rem 0;\\n}\\n.user-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  color: var(--text-secondary);\\n  margin: 0;\\n}\\n\\n.usage-stats[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 0.5rem;\\n}\\n.usage-stats[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: var(--text-secondary);\\n  font-weight: 500;\\n}\\n.usage-stats[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: var(--dashboard-primary);\\n}\\n\\n.usage-bar[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 8px;\\n}\\n\\n.no-data-message[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 3rem;\\n  color: var(--text-secondary);\\n}\\n.no-data-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  margin-bottom: 1rem;\\n  opacity: 0.5;\\n}\\n.no-data-message[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 600;\\n  margin: 0 0 0.5rem 0;\\n}\\n.no-data-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  margin: 0;\\n}\\n\\n.tool-usage-section[_ngcontent-%COMP%] {\\n  margin-top: 2rem;\\n  padding: 1.5rem;\\n  background: var(--card-bg);\\n  border: 1px solid var(--card-border);\\n  border-radius: var(--card-border-radius);\\n  box-shadow: var(--card-box-shadow);\\n}\\n.tool-usage-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n  background: var(--success-color);\\n}\\n\\n.tool-usage-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\\n  gap: 1rem;\\n}\\n\\n.tool-card[_ngcontent-%COMP%]:hover {\\n  border-color: var(--success-color);\\n}\\n\\n.tool-info[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.tool-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: var(--text-color);\\n  margin: 0 0 0.5rem 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.tool-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDD27\\\";\\n  font-size: 0.9rem;\\n}\\n.tool-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  color: var(--text-secondary);\\n  margin: 0;\\n}\\n\\n.user-activity-stats[_ngcontent-%COMP%] {\\n  margin-top: 2rem;\\n  padding: 1.5rem;\\n  background: var(--card-bg);\\n  border: 1px solid var(--card-border);\\n  border-radius: var(--card-border-radius);\\n  box-shadow: var(--card-box-shadow);\\n}\\n.user-activity-stats[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n  background: var(--dashboard-accent);\\n}\\n\\n.stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 1rem;\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n.stat-card[_ngcontent-%COMP%]:hover {\\n  border-color: var(--dashboard-accent);\\n}\\n\\n.stat-icon[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: linear-gradient(135deg, var(--dashboard-accent), var(--dashboard-primary));\\n  border-radius: 50%;\\n  color: white;\\n}\\n.stat-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n}\\n\\n.stat-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.stat-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n  color: var(--text-secondary);\\n  margin: 0 0 0.25rem 0;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.stat-info[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  color: var(--text-color);\\n}\\n\\n.analytics-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 2rem;\\n}\\n\\n.tab-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease-in-out;\\n}\\n\\n.metrics-container[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n}\\n.metrics-container[_ngcontent-%COMP%]   .metrics-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n}\\n.metrics-container[_ngcontent-%COMP%]   .metrics-row[_ngcontent-%COMP%]   ava-text-card[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n@media (max-width: 1200px) {\\n  .metrics-container[_ngcontent-%COMP%]   .metrics-row[_ngcontent-%COMP%] {\\n    gap: 0.75rem;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .metrics-container[_ngcontent-%COMP%]   .metrics-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n  }\\n  .metrics-container[_ngcontent-%COMP%]   .metrics-row[_ngcontent-%COMP%]   ava-text-card[_ngcontent-%COMP%] {\\n    flex: none;\\n  }\\n}\\n\\n.analytics-controls[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.analytics-controls[_ngcontent-%COMP%]   .controls-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  gap: 16px;\\n}\\n.analytics-controls[_ngcontent-%COMP%]   .controls-row[_ngcontent-%COMP%]   .left-buttons[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  gap: 12px;\\n  align-items: center;\\n}\\n.analytics-controls[_ngcontent-%COMP%]   .controls-row[_ngcontent-%COMP%]   .left-buttons[_ngcontent-%COMP%]   .download-button[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.analytics-controls[_ngcontent-%COMP%]   .controls-row[_ngcontent-%COMP%]   .left-buttons[_ngcontent-%COMP%]   .download-icon[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  padding: 8px;\\n  border-radius: 6px;\\n  transition: background-color 0.2s;\\n  position: relative;\\n}\\n.analytics-controls[_ngcontent-%COMP%]   .controls-row[_ngcontent-%COMP%]   .left-buttons[_ngcontent-%COMP%]   .download-icon[_ngcontent-%COMP%]:hover {\\n  background-color: var(--hover-bg, #f5f5f5);\\n}\\n.analytics-controls[_ngcontent-%COMP%]   .controls-row[_ngcontent-%COMP%]   .right-calendar[_ngcontent-%COMP%] {\\n  min-width: 300px;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.analytics-controls[_ngcontent-%COMP%]   .controls-row[_ngcontent-%COMP%]   .right-calendar[_ngcontent-%COMP%]   .calendar-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n.analytics-controls[_ngcontent-%COMP%]   .controls-row[_ngcontent-%COMP%]   .right-calendar[_ngcontent-%COMP%]   .calendar-container[_ngcontent-%COMP%]   .calendar-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n}\\n.analytics-controls[_ngcontent-%COMP%]   .controls-row[_ngcontent-%COMP%]   .right-calendar[_ngcontent-%COMP%]   .calendar-container[_ngcontent-%COMP%]   .calendar-header[_ngcontent-%COMP%]   .calendar-title[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 600;\\n  color: var(--text-color);\\n}\\n.analytics-controls[_ngcontent-%COMP%]   .controls-row[_ngcontent-%COMP%]   .right-calendar[_ngcontent-%COMP%]   .calendar-container[_ngcontent-%COMP%]   .calendar-header[_ngcontent-%COMP%]   .selected-range[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  font-size: 11px;\\n  color: var(--text-secondary);\\n  padding: 4px 8px;\\n  background: var(--card-bg);\\n  border-radius: 4px;\\n  border: 1px solid var(--card-border);\\n  white-space: nowrap;\\n}\\n.analytics-controls[_ngcontent-%COMP%]   .controls-row[_ngcontent-%COMP%]   .right-calendar[_ngcontent-%COMP%]   .calendar-container[_ngcontent-%COMP%]   .calendar-header[_ngcontent-%COMP%]   .selected-range[_ngcontent-%COMP%]   ava-icon[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n}\\n.analytics-controls[_ngcontent-%COMP%]   .controls-row[_ngcontent-%COMP%]   .right-calendar[_ngcontent-%COMP%]   .calendar-container[_ngcontent-%COMP%]   .calendar-header[_ngcontent-%COMP%]   .selected-range[_ngcontent-%COMP%]   .range-text[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: var(--text-color);\\n}\\n.analytics-controls[_ngcontent-%COMP%]   .controls-row[_ngcontent-%COMP%]   .right-calendar[_ngcontent-%COMP%]   .filter-button[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  min-width: 44px;\\n  height: 44px;\\n  border-radius: 8px;\\n  transition: all 0.2s ease;\\n}\\n.analytics-controls[_ngcontent-%COMP%]   .controls-row[_ngcontent-%COMP%]   .right-calendar[_ngcontent-%COMP%]   .filter-button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\\n}\\n.analytics-controls[_ngcontent-%COMP%]   .controls-row[_ngcontent-%COMP%]   .right-calendar[_ngcontent-%COMP%]   .filter-button[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n}\\n@media (max-width: 768px) {\\n  .analytics-controls[_ngcontent-%COMP%]   .controls-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 16px;\\n  }\\n  .analytics-controls[_ngcontent-%COMP%]   .controls-row[_ngcontent-%COMP%]   .left-buttons[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: flex-start;\\n  }\\n  .analytics-controls[_ngcontent-%COMP%]   .controls-row[_ngcontent-%COMP%]   .right-calendar[_ngcontent-%COMP%] {\\n    min-width: 100%;\\n    width: 100%;\\n  }\\n}\\n\\n.left-buttons[_ngcontent-%COMP%]   .download-button[_ngcontent-%COMP%]   .download-dropdown[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 100%;\\n  left: 0;\\n  background: white;\\n  border: 1px solid var(--card-border, #e5e7eb);\\n  border-radius: 8px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  z-index: 1000;\\n  width: 100%;\\n  margin-top: 4px;\\n}\\n.left-buttons[_ngcontent-%COMP%]   .download-button[_ngcontent-%COMP%]   .download-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 10px 16px;\\n  border: none;\\n  background: none;\\n  width: 100%;\\n  text-align: left;\\n  cursor: pointer;\\n  transition: background 0.2s;\\n  font-size: 14px;\\n}\\n.left-buttons[_ngcontent-%COMP%]   .download-button[_ngcontent-%COMP%]   .download-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]:first-child {\\n  border-radius: 8px 8px 0 0;\\n}\\n.left-buttons[_ngcontent-%COMP%]   .download-button[_ngcontent-%COMP%]   .download-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]:last-child {\\n  border-radius: 0 0 8px 8px;\\n}\\n.left-buttons[_ngcontent-%COMP%]   .download-button[_ngcontent-%COMP%]   .download-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]:hover {\\n  background: var(--hover-bg, #f5f5f5);\\n}\\n.left-buttons[_ngcontent-%COMP%]   .download-button[_ngcontent-%COMP%]   .download-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   ava-icon[_ngcontent-%COMP%] {\\n  color: var(--text-secondary, #6b7280);\\n  flex-shrink: 0;\\n}\\n.left-buttons[_ngcontent-%COMP%]   .download-button[_ngcontent-%COMP%]   .download-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--text-color, #374151);\\n}\\n\\n.chart-download-icon[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  padding: 6px;\\n  border-radius: 4px;\\n  transition: all 0.2s ease;\\n  color: var(--text-secondary, #6b7280);\\n}\\n.chart-download-icon[_ngcontent-%COMP%]:hover {\\n  background-color: var(--hover-bg, #f5f5f5);\\n  color: var(--text-color, #374151);\\n  transform: scale(1.1);\\n}\\n.chart-download-icon[_ngcontent-%COMP%]:active {\\n  transform: scale(0.95);\\n}\\n\\n.chart-content1[_ngcontent-%COMP%] {\\n  padding: 20px;\\n}\\n\\n.charts-section[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));\\n  gap: 2rem;\\n}\\n\\n.charts-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 1.5rem;\\n  margin-top: 2rem;\\n}\\n.charts-grid[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%] {\\n  background: var(--agent-chat-column-bg, #f9f9f9);\\n  border: 1px solid var(--card-border);\\n  border-radius: var(--card-border-radius);\\n  box-shadow: var(--card-box-shadow);\\n  padding: 1.5rem;\\n  min-height: 400px;\\n  display: flex;\\n  flex-direction: column;\\n  transition: all 0.3s ease;\\n}\\n.charts-grid[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n.charts-grid[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   highcharts-chart[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-height: 320px;\\n  width: 100% !important;\\n}\\n.charts-grid[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   highcharts-chart[_ngcontent-%COMP%]   .highcharts-container[_ngcontent-%COMP%] {\\n  width: 100% !important;\\n  height: 100% !important;\\n}\\n.charts-grid[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   .chart-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 1rem;\\n  padding-bottom: 0.5rem;\\n  border-bottom: 1px solid var(--card-border);\\n}\\n.charts-grid[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   .chart-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: var(--text-color);\\n  margin: 0;\\n}\\n.charts-grid[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   .chart-header[_ngcontent-%COMP%]   .export-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  padding: 0.25rem 0.5rem;\\n  background: none;\\n  border: none;\\n  color: var(--text-secondary);\\n  font-size: 0.8rem;\\n  cursor: pointer;\\n  transition: color 0.2s ease;\\n}\\n.charts-grid[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   .chart-header[_ngcontent-%COMP%]   .export-btn[_ngcontent-%COMP%]:hover {\\n  color: var(--dashboard-primary);\\n}\\n.charts-grid[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]   .chart-header[_ngcontent-%COMP%]   .export-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n}\\n.charts-grid[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]:first-child   .highcharts-container[_ngcontent-%COMP%] {\\n  overflow: visible !important;\\n}\\n.charts-grid[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]:first-child   .highcharts-scrollbar[_ngcontent-%COMP%] {\\n  opacity: 0.7;\\n  transition: opacity 0.3s ease;\\n}\\n.charts-grid[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]:first-child   .highcharts-scrollbar[_ngcontent-%COMP%]:hover {\\n  opacity: 1;\\n}\\n.charts-grid[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]:first-child   .highcharts-scrolling[_ngcontent-%COMP%] {\\n  overflow-y: auto;\\n  overflow-x: hidden;\\n}\\n.charts-grid[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]:first-child   .highcharts-scrolling[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 8px;\\n}\\n.charts-grid[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]:first-child   .highcharts-scrolling[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 4px;\\n}\\n.charts-grid[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]:first-child   .highcharts-scrolling[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #8B7EC8;\\n  border-radius: 4px;\\n}\\n.charts-grid[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%]:first-child   .highcharts-scrolling[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #7A6DB8;\\n}\\n\\n.chart-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 1rem;\\n}\\n.chart-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 600;\\n  color: var(--text-color);\\n  margin: 0;\\n}\\n\\n.export-btn[_ngcontent-%COMP%] {\\n  padding: 0.5rem 1rem;\\n  font-size: 0.85rem;\\n  min-height: 32px;\\n}\\n.export-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n}\\n.export-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(-1px);\\n}\\n.export-btn[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  box-shadow: 0 0 0 3px rgba(102, 102, 204, 0.3);\\n}\\n.export-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  transition: transform 0.2s ease;\\n}\\n.export-btn[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\\n  transform: scale(1.1) rotate(5deg);\\n}\\n.export-btn[_ngcontent-%COMP%]   .download-icon[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n  object-fit: contain;\\n  transition: all 0.3s ease;\\n  opacity: 1;\\n  display: inline-block;\\n  vertical-align: middle;\\n  max-width: 100%;\\n  max-height: 100%;\\n  background-color: transparent;\\n  border: none;\\n}\\n.export-btn[_ngcontent-%COMP%]:hover   .download-icon[_ngcontent-%COMP%] {\\n  transform: scale(1.1) rotate(5deg);\\n  opacity: 0.8;\\n}\\n.export-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n  transform: none;\\n}\\n.export-btn[_ngcontent-%COMP%]:disabled:hover {\\n  transform: none;\\n}\\n.export-btn[_ngcontent-%COMP%]:disabled   .download-icon[_ngcontent-%COMP%] {\\n  opacity: 0.4;\\n}\\n\\n.overview-charts[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));\\n  gap: 2rem;\\n  margin-top: 2rem;\\n}\\n\\n.chart-placeholder[_ngcontent-%COMP%] {\\n  height: 300px;\\n  background: var(--dashboard-bg-light);\\n  border: 2px dashed var(--card-border);\\n  border-radius: 8px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.chart-placeholder[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--text-secondary);\\n  font-style: italic;\\n  margin: 0;\\n}\\n\\n.recent-activity[_ngcontent-%COMP%] {\\n  background: var(--card-bg);\\n  border: 1px solid var(--card-border);\\n  border-radius: var(--card-border-radius);\\n  padding: 1.5rem;\\n  box-shadow: var(--card-box-shadow);\\n}\\n.recent-activity[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 600;\\n  color: var(--text-color);\\n  margin: 0 0 1rem 0;\\n}\\n\\n.activity-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1rem;\\n}\\n\\n.activity-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n  padding: 1rem;\\n  background: var(--dashboard-bg-light);\\n  border-radius: 8px;\\n  transition: background-color 0.2s ease;\\n}\\n.activity-item[_ngcontent-%COMP%]:hover {\\n  background: var(--dashboard-bg-lighter);\\n}\\n\\n.activity-icon[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 8px;\\n  background: var(--dashboard-bg-icon-button);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.activity-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: var(--dashboard-primary);\\n}\\n\\n.activity-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.25rem;\\n}\\n.activity-details[_ngcontent-%COMP%]   .activity-title[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: var(--text-color);\\n}\\n.activity-details[_ngcontent-%COMP%]   .activity-time[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  color: var(--text-secondary);\\n}\\n\\n.reports-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1.5rem;\\n}\\n\\n.report-card[_ngcontent-%COMP%] {\\n  background: var(--card-bg);\\n  border: 1px solid var(--card-border);\\n  border-radius: var(--card-border-radius);\\n  padding: 1.5rem;\\n  box-shadow: var(--card-box-shadow);\\n  transition: all 0.2s ease;\\n}\\n.report-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: var(--card-hover-shadow);\\n}\\n\\n.report-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: between;\\n  align-items: center;\\n  margin-bottom: 1rem;\\n}\\n.report-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 600;\\n  color: var(--text-color);\\n  margin: 0;\\n  flex: 1;\\n}\\n\\n.download-btn[_ngcontent-%COMP%], .generate-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.5rem 1rem;\\n  background: var(--gradient-primary);\\n  color: white;\\n  border: none;\\n  border-radius: 6px;\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: opacity 0.2s ease;\\n}\\n.download-btn[_ngcontent-%COMP%]:hover, .generate-btn[_ngcontent-%COMP%]:hover {\\n  opacity: 0.9;\\n}\\n.download-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .generate-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n}\\n\\n.generate-btn[_ngcontent-%COMP%] {\\n  background: var(--dashboard-bg-icon-button);\\n  color: var(--dashboard-primary);\\n  border: 1px solid var(--card-border);\\n}\\n.generate-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--dashboard-bg-light);\\n}\\n\\n.report-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--text-secondary);\\n  margin-bottom: 1rem;\\n  line-height: 1.5;\\n}\\n\\n.report-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  flex-wrap: wrap;\\n}\\n.report-stats[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  color: var(--text-secondary);\\n  padding: 0.25rem 0.75rem;\\n  background: var(--dashboard-bg-light);\\n  border-radius: 4px;\\n  border: 1px solid var(--card-border);\\n}\\n\\n.agents-table[_ngcontent-%COMP%] {\\n  background: var(--card-bg);\\n  border: 1px solid var(--card-border);\\n  border-radius: var(--card-border-radius);\\n  padding: 1.5rem;\\n  box-shadow: var(--card-box-shadow);\\n  margin-top: 2rem;\\n}\\n.agents-table[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 600;\\n  color: var(--text-color);\\n  margin: 0 0 1rem 0;\\n}\\n\\n.table-container[_ngcontent-%COMP%] {\\n  overflow-x: auto;\\n}\\n\\n.agents-data-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-collapse: collapse;\\n}\\n.agents-data-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .agents-data-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 0.75rem;\\n  text-align: left;\\n  border-bottom: 1px solid var(--card-border);\\n}\\n.agents-data-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background: var(--dashboard-bg-light);\\n  font-weight: 600;\\n  color: var(--text-color);\\n  font-size: 0.9rem;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.agents-data-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  color: var(--text-secondary);\\n  font-size: 0.9rem;\\n}\\n.agents-data-table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  background: var(--dashboard-bg-light);\\n}\\n\\n.performance-badge[_ngcontent-%COMP%] {\\n  padding: 0.25rem 0.75rem;\\n  border-radius: 12px;\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.performance-badge.high[_ngcontent-%COMP%] {\\n  background: rgba(16, 185, 129, 0.1);\\n  color: var(--success-color);\\n  border: 1px solid rgba(16, 185, 129, 0.2);\\n}\\n.performance-badge.medium[_ngcontent-%COMP%] {\\n  background: rgba(245, 158, 11, 0.1);\\n  color: #f59e0b;\\n  border: 1px solid rgba(245, 158, 11, 0.2);\\n}\\n.performance-badge.low[_ngcontent-%COMP%] {\\n  background: rgba(239, 68, 68, 0.1);\\n  color: var(--error-color);\\n  border: 1px solid rgba(239, 68, 68, 0.2);\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .analytics-container[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .analytics-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .analytics-header[_ngcontent-%COMP%]   .analytics-subtitle[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n  .action-bar[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n    align-items: stretch;\\n  }\\n  .analytics-buttons[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.75rem;\\n  }\\n  .analytics-btn[_ngcontent-%COMP%] {\\n    justify-content: center;\\n    padding: 1rem;\\n  }\\n  .date-filter-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.75rem;\\n    align-items: stretch;\\n  }\\n  .date-inputs[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n  }\\n  .filter-btn[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n  .metrics-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n  }\\n  .metrics-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .charts-section[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .charts-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 1rem;\\n  }\\n  .consumption-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .tool-usage-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .stats-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .chart-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 1rem;\\n  }\\n  .export-btn[_ngcontent-%COMP%] {\\n    align-self: flex-end;\\n  }\\n  .filter-dropdown[_ngcontent-%COMP%] {\\n    max-width: 100%;\\n  }\\n  .download-dropdown[_ngcontent-%COMP%] {\\n    right: auto;\\n    left: 0;\\n    width: 100%;\\n  }\\n  .consumption-card[_ngcontent-%COMP%], \\n   .tool-card[_ngcontent-%COMP%], \\n   .stat-card[_ngcontent-%COMP%], \\n   .user-consumption-section[_ngcontent-%COMP%], \\n   .tool-usage-section[_ngcontent-%COMP%], \\n   .user-activity-stats[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n    margin: 1rem 0;\\n  }\\n  .agents-data-table[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .agents-data-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .agents-data-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n    padding: 0.5rem;\\n  }\\n  .table-container[_ngcontent-%COMP%] {\\n    overflow-x: scroll;\\n  }\\n  .report-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 1rem;\\n  }\\n  .report-stats[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n  }\\n}\\n@media (prefers-color-scheme: dark) {\\n  .analytics-btn[_ngcontent-%COMP%], \\n   .download-dropdown[_ngcontent-%COMP%] {\\n    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n  }\\n  .analytics-btn[_ngcontent-%COMP%]:hover, \\n   .download-dropdown[_ngcontent-%COMP%]:hover {\\n    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);\\n  }\\n}\\n.chart-container[_ngcontent-%COMP%]   .agent-metrics-table-container[_ngcontent-%COMP%] {\\n  height: 350px;\\n  overflow: hidden;\\n  border: 1px solid var(--border-color);\\n  border-radius: 8px;\\n  background-color: var(--card-background);\\n}\\n.chart-container[_ngcontent-%COMP%]   .agent-metrics-table-container[_ngcontent-%COMP%]   .table-wrapper[_ngcontent-%COMP%] {\\n  height: 100%;\\n  overflow-y: auto;\\n}\\n.chart-container[_ngcontent-%COMP%]   .agent-metrics-table-container[_ngcontent-%COMP%]   .agent-metrics-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-collapse: collapse;\\n  font-family: \\\"Mulish\\\", sans-serif;\\n}\\n.chart-container[_ngcontent-%COMP%]   .agent-metrics-table-container[_ngcontent-%COMP%]   .agent-metrics-table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 0;\\n  z-index: 10;\\n  background: linear-gradient(135deg, #f8f9fa, #e9ecef);\\n}\\n.chart-container[_ngcontent-%COMP%]   .agent-metrics-table-container[_ngcontent-%COMP%]   .agent-metrics-table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  padding: 12px 16px;\\n  text-align: left;\\n  font-weight: 600;\\n  font-size: 14px;\\n  color: #495057;\\n  border-bottom: 2px solid var(--dashboard-primary);\\n  background: linear-gradient(135deg, #f8f9fa, #e9ecef);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.chart-container[_ngcontent-%COMP%]   .agent-metrics-table-container[_ngcontent-%COMP%]   .agent-metrics-table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th.expand-column[_ngcontent-%COMP%] {\\n  width: 50px;\\n  padding: 12px 8px;\\n  text-align: center;\\n}\\n.chart-container[_ngcontent-%COMP%]   .agent-metrics-table-container[_ngcontent-%COMP%]   .agent-metrics-table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #e9ecef, #dee2e6);\\n}\\n.chart-container[_ngcontent-%COMP%]   .agent-metrics-table-container[_ngcontent-%COMP%]   .agent-metrics-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .agent-row[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid var(--border-color);\\n  transition: background-color 0.2s ease;\\n}\\n.chart-container[_ngcontent-%COMP%]   .agent-metrics-table-container[_ngcontent-%COMP%]   .agent-metrics-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .agent-row[_ngcontent-%COMP%]:hover {\\n  background-color: var(--hover-background);\\n}\\n.chart-container[_ngcontent-%COMP%]   .agent-metrics-table-container[_ngcontent-%COMP%]   .agent-metrics-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .agent-row[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 12px 16px;\\n  font-size: 14px;\\n  color: var(--text-color);\\n  vertical-align: middle;\\n}\\n.chart-container[_ngcontent-%COMP%]   .agent-metrics-table-container[_ngcontent-%COMP%]   .agent-metrics-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .agent-row[_ngcontent-%COMP%]   td.expand-column[_ngcontent-%COMP%] {\\n  width: 50px;\\n  padding: 12px 8px;\\n  text-align: center;\\n  vertical-align: middle;\\n}\\n.chart-container[_ngcontent-%COMP%]   .agent-metrics-table-container[_ngcontent-%COMP%]   .agent-metrics-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .agent-row[_ngcontent-%COMP%]   td.agent-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n}\\n.chart-container[_ngcontent-%COMP%]   .agent-metrics-table-container[_ngcontent-%COMP%]   .agent-metrics-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .agent-row[_ngcontent-%COMP%]   td.workflow-count[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: var(--primary-color);\\n}\\n.chart-container[_ngcontent-%COMP%]   .agent-metrics-table-container[_ngcontent-%COMP%]   .agent-metrics-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .workflow-details-row[_ngcontent-%COMP%] {\\n  background-color: var(--secondary-background);\\n}\\n.chart-container[_ngcontent-%COMP%]   .agent-metrics-table-container[_ngcontent-%COMP%]   .agent-metrics-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .workflow-details-row[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 0;\\n  border-bottom: 1px solid var(--border-color);\\n}\\n.chart-container[_ngcontent-%COMP%]   .agent-metrics-table-container[_ngcontent-%COMP%]   .agent-metrics-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .workflow-details-row[_ngcontent-%COMP%]   .workflow-details[_ngcontent-%COMP%] {\\n  padding: 16px 24px;\\n}\\n.chart-container[_ngcontent-%COMP%]   .agent-metrics-table-container[_ngcontent-%COMP%]   .agent-metrics-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .workflow-details-row[_ngcontent-%COMP%]   .workflow-details[_ngcontent-%COMP%]   .workflow-header[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 13px;\\n  color: var(--text-color);\\n  margin-bottom: 8px;\\n}\\n.chart-container[_ngcontent-%COMP%]   .agent-metrics-table-container[_ngcontent-%COMP%]   .agent-metrics-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .workflow-details-row[_ngcontent-%COMP%]   .workflow-details[_ngcontent-%COMP%]   .workflow-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n}\\n.chart-container[_ngcontent-%COMP%]   .agent-metrics-table-container[_ngcontent-%COMP%]   .agent-metrics-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .workflow-details-row[_ngcontent-%COMP%]   .workflow-details[_ngcontent-%COMP%]   .workflow-list[_ngcontent-%COMP%]   .workflow-item[_ngcontent-%COMP%] {\\n  padding: 4px 8px;\\n  background-color: var(--card-background);\\n  border-radius: 4px;\\n  font-size: 13px;\\n  color: var(--text-secondary);\\n  border-left: 3px solid var(--primary-color);\\n}\\n.chart-container[_ngcontent-%COMP%]   .agent-metrics-table-container[_ngcontent-%COMP%]   .expand-btn[_ngcontent-%COMP%] {\\n  background: var(--card-bg);\\n  border: 1px solid var(--card-border);\\n  cursor: pointer;\\n  padding: 6px;\\n  border-radius: 6px;\\n  color: var(--text-color);\\n  transition: all 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 28px;\\n  height: 28px;\\n}\\n.chart-container[_ngcontent-%COMP%]   .agent-metrics-table-container[_ngcontent-%COMP%]   .expand-btn[_ngcontent-%COMP%]:hover {\\n  background-color: var(--nav-hover);\\n  border-color: var(--dashboard-primary);\\n  color: var(--dashboard-primary);\\n  transform: scale(1.1);\\n}\\n.chart-container[_ngcontent-%COMP%]   .agent-metrics-table-container[_ngcontent-%COMP%]   .expand-btn[_ngcontent-%COMP%]:active {\\n  transform: scale(0.95);\\n}\\n.chart-container[_ngcontent-%COMP%]   .agent-metrics-table-container[_ngcontent-%COMP%]   .expand-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n  transition: transform 0.2s ease;\\n}\\n.chart-container[_ngcontent-%COMP%]   .agent-metrics-table-container[_ngcontent-%COMP%]   .expand-btn.expanded[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n\\n.bars-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  max-height: 250px;\\n  min-height: 200px;\\n  overflow-y: auto;\\n  overflow-x: hidden;\\n  padding-right: 8px;\\n  margin-top: 8px;\\n}\\n.bars-container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 8px !important;\\n  background: rgba(0, 0, 0, 0.1);\\n}\\n.bars-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(139, 92, 246, 0.7) !important;\\n  border-radius: 4px;\\n}\\n.bars-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: rgba(139, 92, 246, 0.9) !important;\\n}\\n\\n.bar-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 12px;\\n  position: relative;\\n  padding: 2px 0;\\n  min-height: 50px;\\n}\\n\\n.bar-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.bar[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #8B5CF6, #A855F7);\\n  height: 3rem;\\n  border-radius: 8px;\\n  display: flex;\\n  align-items: center;\\n  position: relative;\\n  overflow: visible;\\n  min-width: 150px;\\n  box-shadow: 0 2px 6px rgba(139, 92, 246, 0.25);\\n  transition: all 0.3s ease;\\n  margin-bottom: 2px;\\n}\\n.bar[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 12px rgba(139, 92, 246, 0.35);\\n}\\n.bar.tool-bar[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #F97316, #FB923C);\\n  box-shadow: 0 2px 6px rgba(249, 115, 22, 0.25);\\n}\\n.bar.tool-bar[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 6px 12px rgba(249, 115, 22, 0.35);\\n}\\n\\n.bar-label[_ngcontent-%COMP%] {\\n  color: #000;\\n  font-size: 12px;\\n  font-weight: 500;\\n  white-space: nowrap;\\n  position: absolute;\\n  left: 8px;\\n  right: 8px;\\n  overflow: visible;\\n  z-index: 1;\\n  max-width: calc(100% - 16px);\\n}\\n\\n.bar-value[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 600;\\n  white-space: nowrap;\\n  width: 50px;\\n  text-align: right;\\n  margin-left: 10px;\\n  color: var(--text-color);\\n}\\n\\n.no-data-bar[_ngcontent-%COMP%] {\\n  color: var(--text-secondary);\\n  font-size: 12px;\\n  font-style: italic;\\n}\\n\\n.no-data-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  height: 15rem;\\n  color: var(--text-secondary);\\n}\\n.no-data-message[_ngcontent-%COMP%]   .error-status[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n.no-data-message[_ngcontent-%COMP%]   .error-status[_ngcontent-%COMP%]   .no-data-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  color: var(--text-secondary);\\n}\\n.no-data-message[_ngcontent-%COMP%]   .error-status[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  font-weight: 500;\\n}\\n\\n@media (max-width: 768px) {\\n  .user-consumption[_ngcontent-%COMP%], .tool-usage[_ngcontent-%COMP%] {\\n    width: 100%;\\n    height: auto;\\n    min-height: 20rem;\\n  }\\n  .user-consumption-container[_ngcontent-%COMP%], .tool-usage-container[_ngcontent-%COMP%] {\\n    padding: 15px;\\n  }\\n  .bars-container[_ngcontent-%COMP%] {\\n    max-height: 15rem;\\n  }\\n  .bar[_ngcontent-%COMP%] {\\n    height: 2rem;\\n  }\\n  .bar-label[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n    left: 6px;\\n  }\\n  .bar-value[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n    width: 40px;\\n  }\\n  .user-consumption-header[_ngcontent-%COMP%], .tool-usage-header[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n}\\n.gradient-divider[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 2px;\\n  background: linear-gradient(90deg, transparent 0%, #E30A6D 50%, transparent 100%);\\n  margin: 0 16px;\\n  border-radius: 1px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return AnalyticsComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "Subject", "takeUntil", "fork<PERSON><PERSON>n", "Highcharts", "HighchartsChartModule", "NoDataToDisplay", "HC_more", "HC_Exporting", "MatSelectModule", "MatFormFieldModule", "MatOptionModule", "MatInputModule", "ButtonComponent", "CalendarComponent", "TextCardComponent", "IconComponent", "environment", "i0", "ɵɵelementStart", "ɵɵlistener", "AnalyticsComponent_div_8_div_7_Template_button_click_1_listener", "ɵɵrestoreView", "_r3", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onPdfDownload", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "AnalyticsComponent_div_8_div_7_Template_button_click_5_listener", "onDataDownload", "AnalyticsComponent_div_8_ava_icon_23_Template_ava_icon_click_0_listener", "_r4", "downloadExcel", "ɵɵtemplate", "AnalyticsComponent_div_8_div_27_div_1_div_5_Template", "ɵɵadvance", "ɵɵstyleProp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "user_r5", "requestCount", "ɵɵtextInterpolate", "userSignature", "ɵɵproperty", "AnalyticsComponent_div_8_div_27_div_1_Template", "sortedAnalytics", "chartOptions", "AnalyticsComponent_div_8_Template_ava_button_userClick_4_listener", "_r1", "goToLangfuse", "AnalyticsComponent_div_8_Template_ava_button_userClick_5_listener", "goToIclAnalytics", "AnalyticsComponent_div_8_Template_ava_button_userClick_6_listener", "$event", "onDownloadToggle", "AnalyticsComponent_div_8_Template_ava_button_click_6_listener", "AnalyticsComponent_div_8_div_7_Template", "AnalyticsComponent_div_8_Template_ava_calendar_rangeSelected_9_listener", "onRangeSelected", "AnalyticsComponent_div_8_Template_ava_button_userClick_10_listener", "applyFilter", "AnalyticsComponent_div_8_ava_icon_23_Template", "AnalyticsComponent_div_8_div_25_Template", "AnalyticsComponent_div_8_div_26_Template", "AnalyticsComponent_div_8_div_27_Template", "AnalyticsComponent_div_8_Template_ava_icon_click_32_listener", "AnalyticsComponent_div_8_div_33_Template", "AnalyticsComponent_div_8_highcharts_chart_34_Template", "AnalyticsComponent_div_8_Template_ava_icon_click_39_listener", "AnalyticsComponent_div_8_div_40_Template", "AnalyticsComponent_div_8_highcharts_chart_41_Template", "AnalyticsComponent_div_8_Template_ava_icon_click_46_listener", "AnalyticsComponent_div_8_div_47_Template", "AnalyticsComponent_div_8_highcharts_chart_48_Template", "AnalyticsComponent_div_8_Template_ava_icon_click_53_listener", "AnalyticsComponent_div_8_div_54_Template", "AnalyticsComponent_div_8_highcharts_chart_55_Template", "AnalyticsComponent_div_8_Template_ava_icon_click_60_listener", "AnalyticsComponent_div_8_div_61_Template", "AnalyticsComponent_div_8_highcharts_chart_62_Template", "AnalyticsComponent_div_8_Template_ava_icon_click_67_listener", "AnalyticsComponent_div_8_div_68_Template", "AnalyticsComponent_div_8_highcharts_chart_69_Template", "AnalyticsComponent_div_8_Template_ava_icon_click_74_listener", "AnalyticsComponent_div_8_div_75_Template", "AnalyticsComponent_div_8_highcharts_chart_76_Template", "AnalyticsComponent_div_8_div_77_Template", "vmFG", "showDownloadOptions", "getCalendarDateRange", "useCaseMetrics", "totalBugs", "totalUnitTest", "totalStory", "totalCodeOptimization", "userActivity", "activeUsers", "noDataAvailable", "usageLoader", "chartLoadingStates", "AnalyticsComponent_div_9_div_7_Template_button_click_1_listener", "_r7", "AnalyticsComponent_div_9_div_7_Template_button_click_5_listener", "AnalyticsComponent_div_9_div_46_ng_container_11_button_3_Template_button_click_0_listener", "_r8", "i_r9", "index", "toggleRowExpansion", "AnalyticsComponent_div_9_div_46_ng_container_11_button_3__svg_path_2_Template", "AnalyticsComponent_div_9_div_46_ng_container_11_button_3__svg_path_3_Template", "ɵɵclassProp", "isRowExpanded", "ɵɵtextInterpolate1", "workflow_r10", "AnalyticsComponent_div_9_div_46_ng_container_11_tr_8_div_6_Template", "agent_r11", "workflows", "ɵɵelementContainerStart", "AnalyticsComponent_div_9_div_46_ng_container_11_button_3_Template", "AnalyticsComponent_div_9_div_46_ng_container_11_tr_8_Template", "length", "<PERSON><PERSON><PERSON>", "workflowCount", "AnalyticsComponent_div_9_div_46_ng_container_11_Template", "agentMetricsTableData", "AnalyticsComponent_div_9_ng_container_57_ava_icon_3_Template_ava_icon_click_0_listener", "_r12", "downloadAgentExcel", "AnalyticsComponent_div_9_ng_container_57_div_5_div_1_div_5_Template", "tool_r13", "usageCount", "toolName", "AnalyticsComponent_div_9_ng_container_57_div_5_div_1_Template", "toolUsage", "slice", "AnalyticsComponent_div_9_ng_container_57_ava_icon_3_Template", "AnalyticsComponent_div_9_ng_container_57_div_4_Template", "AnalyticsComponent_div_9_ng_container_57_div_5_Template", "AnalyticsComponent_div_9_ava_icon_69_Template_ava_icon_click_0_listener", "_r14", "AnalyticsComponent_div_9_div_73_div_1_div_5_Template", "user_r15", "consumptionCount", "email", "AnalyticsComponent_div_9_div_73_div_1_Template", "userConsumption", "AnalyticsComponent_div_9_Template_ava_button_userClick_4_listener", "_r6", "AnalyticsComponent_div_9_Template_ava_button_userClick_5_listener", "AnalyticsComponent_div_9_Template_ava_button_userClick_6_listener", "AnalyticsComponent_div_9_Template_ava_button_click_6_listener", "AnalyticsComponent_div_9_div_7_Template", "AnalyticsComponent_div_9_Template_ava_calendar_rangeSelected_9_listener", "AnalyticsComponent_div_9_Template_ava_button_userClick_10_listener", "AnalyticsComponent_div_9_Template_ava_icon_click_22_listener", "AnalyticsComponent_div_9_div_23_Template", "AnalyticsComponent_div_9_highcharts_chart_24_Template", "AnalyticsComponent_div_9_Template_ava_icon_click_29_listener", "AnalyticsComponent_div_9_div_30_Template", "AnalyticsComponent_div_9_highcharts_chart_31_Template", "AnalyticsComponent_div_9_Template_ava_icon_click_36_listener", "AnalyticsComponent_div_9_div_37_Template", "AnalyticsComponent_div_9_highcharts_chart_38_Template", "AnalyticsComponent_div_9_Template_ava_icon_click_43_listener", "AnalyticsComponent_div_9_div_44_Template", "AnalyticsComponent_div_9_div_45_Template", "AnalyticsComponent_div_9_div_46_Template", "AnalyticsComponent_div_9_Template_ava_icon_click_51_listener", "AnalyticsComponent_div_9_div_52_Template", "AnalyticsComponent_div_9_highcharts_chart_53_Template", "AnalyticsComponent_div_9_div_56_Template", "AnalyticsComponent_div_9_ng_container_57_Template", "AnalyticsComponent_div_9_Template_ava_icon_click_62_listener", "AnalyticsComponent_div_9_div_63_Template", "AnalyticsComponent_div_9_highcharts_chart_64_Template", "AnalyticsComponent_div_9_ava_icon_69_Template", "AnalyticsComponent_div_9_div_71_Template", "AnalyticsComponent_div_9_div_72_Template", "AnalyticsComponent_div_9_div_73_Template", "AnalyticsComponent_div_9_Template_ava_icon_click_78_listener", "AnalyticsComponent_div_9_div_79_Template", "AnalyticsComponent_div_9_highcharts_chart_80_Template", "agentMetrics", "totalAgentsCreated", "totalAgentsReused", "totalTools", "workflowExecutionCount", "agentMetricsLoader", "agentMetricsNoDataAvailable", "AnalyticsComponent", "analyticsService", "fb", "destroy$", "CHART_COLORS", "primary", "secondary", "tertiary", "success", "warning", "danger", "PROFESSIONAL_COLORS", "linesOfCode", "topUseCases", "numberOfRequests", "topLanguages", "userResponse", "responseTime", "studioUsage", "topAgents", "agentCreated", "toolAnalytics", "adoptionRate", "collaborativeUserConsumption", "CHART_DEFAULTS", "backgroundColor", "fontFamily", "height", "date<PERSON><PERSON><PERSON>", "fromDate", "getDefaultFromDate", "toDate", "getDefaultToDate", "activeTab", "tabs", "id", "label", "tabListStyle", "isLoading", "metricesLoader", "consumptionLoader", "chartRefs", "analyticsData", "userActivityStats", "expandedRows", "Set", "selectedUseCases", "useCaseList", "sortedUseCaseList", "searchText", "filteringEnabled", "langfuseUrl", "consoleLangfuseUrl", "ICLAnalyticsUrl", "consoleTruelensUrl", "constructor", "initializeForm", "ngOnInit", "initializeAnalytics", "ngOnDestroy", "next", "complete", "group", "valueChanges", "pipe", "subscribe", "values", "loadAnalyticsData", "onTabChange", "tabItem", "loadTabSpecificData", "getActiveTab", "find", "tab", "tabId", "loadUseCaseData", "loadAgentData", "onDateRangeChange", "formValues", "value", "isValidDateRange", "console", "error", "Date", "date", "setDate", "toISOString", "split", "formatDateForAPI", "dateString", "year", "month", "day", "getFormattedDateRangeForAPI", "formattedDateRange", "mainAnalytics", "getAllAnalyticsData", "totalRequestMetrics", "totalRequestCount", "getUserUsageAnalytics", "getUserActivity", "getResponseTime", "responses", "parseAndCreateChartsWithSeparateAPIs", "mainData", "userConsumptionData", "userActivityData", "responseTimeData", "Object", "keys", "for<PERSON>ach", "key", "userAnalyticsArray", "userLevelAnalytics", "userAnalytics", "Array", "isArray", "createUserConsumptionChart", "createNoDataChart", "linesOfCodeAnalytics", "categories", "map", "item", "count", "createLineChart", "useCaseLevelAnalytics", "sortedUseCases", "sort", "a", "b", "useCaseCode", "createColumnChart", "requestData", "numberOfRequestsAnalytics", "numberOfRequestAnalytics", "programmingLanguageAnalytics", "programmingLanguage", "userResponseAnalytics", "pieData", "response", "percentage", "create<PERSON>ieChart", "inactiveUsers", "totalUsers", "activity", "total", "log", "inactivePercentage", "activePercentage", "dormantUsers", "responseTimes", "createdAt", "series", "ySeries", "time", "getAgenticAIAnalytics", "data", "processAgentAnalyticsData", "loadAgentCharts", "has", "delete", "add", "loadAgentMetricsTable", "studioUsageData", "domainName", "agent", "teamName", "toolAnalyticsItem", "toolAnalyticsData", "userDefinedPercentage", "builtInPercentage", "tool", "createHorizontalBarChart", "workflowName", "executionCount", "transformedData", "user", "userStats", "activeUserPercentage", "inactiveUserPercentage", "dormantUserData", "Math", "round", "title", "seriesName", "chartColor", "includes", "commonConfig", "getCommonChartConfig", "chart", "type", "zoomType", "panning", "enabled", "panKey", "xAxis", "labels", "rotation", "style", "color", "fontSize", "lineColor", "tickColor", "yAxis", "text", "gridLineColor", "name", "lineWidth", "marker", "fillColor", "radius", "shadow", "opacity", "width", "plotOptions", "line", "dataLabels", "chartType", "borderWidth", "borderRadius", "column", "fontWeight", "textOutline", "pointPadding", "groupPadding", "maxPointWidth", "colors", "innerSize", "legend", "align", "verticalAlign", "layout", "itemStyle", "symbolRadius", "itemMarginBottom", "pie", "allowPointSelect", "cursor", "showInLegend", "userName", "chartHeight", "showScrollbar", "marginLeft", "marginRight", "scrollablePlotArea", "minHeight", "scrollPositionY", "undefined", "display", "overflow", "step", "useHTML", "formatter", "min", "max", "bar", "inside", "x", "y", "tooltip", "navigation", "buttonOptions", "theme", "stroke", "r", "states", "hover", "fill", "select", "exporting", "credits", "lang", "noData", "createChartWithFallback", "chartCreator", "chartCallback", "chart<PERSON>ey", "analyticsName", "downloadChartExcel", "downloadAgenticAIExcel", "downloadDump", "onUseCaseSelectionChanged", "event", "setPayload", "toggleDownloadOptions", "stopPropagation", "downloadChartsAsPDF", "getBaseChartConfig", "isToolUsage", "maxValue", "calculatedWidth", "window", "open", "goToTrulens", "startDate", "endDate", "patchValue", "getFormattedDateRange", "options", "toLocaleDateString", "start", "end", "onDocumentClick", "target", "downloadButton", "closest", "downloadDropdown", "ɵɵdirectiveInject", "i1", "AnalyticsService", "i2", "FormBuilder", "selectors", "hostBindings", "AnalyticsComponent_HostBindings", "rf", "ctx", "AnalyticsComponent_click_HostBindingHandler", "ɵɵresolveDocument", "AnalyticsComponent_Template_ava_button_userClick_5_listener", "AnalyticsComponent_Template_ava_button_userClick_6_listener", "AnalyticsComponent_div_8_Template", "AnalyticsComponent_div_9_Template", "i3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "NgControlStatusGroup", "FormGroupDirective", "i4", "HighchartsChartComponent", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\analytics\\analytics.component.ts", "C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\analytics\\analytics.component.html"], "sourcesContent": ["import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, HostListener } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';\r\nimport { Subject, takeUntil, forkJoin } from 'rxjs';\r\nimport * as Highcharts from 'highcharts';\r\nimport { HighchartsChartModule } from 'highcharts-angular';\r\nimport NoDataToDisplay from 'highcharts/modules/no-data-to-display';\r\nimport HC_more from 'highcharts/highcharts-more';\r\nimport HC_Exporting from 'highcharts/modules/exporting';\r\nimport { MatSelectModule } from '@angular/material/select';\r\nimport { MatFormFieldModule } from '@angular/material/form-field';\r\nimport { MatOptionModule } from '@angular/material/core';\r\nimport { MatInputModule } from '@angular/material/input';\r\nimport { TabItem, ButtonComponent, CalendarComponent, TextCardComponent, IconComponent } from '@ava/play-comp-library';\r\nimport { AnalyticsService } from '../../shared/services/analytics.service';\r\nimport { environment } from 'projects/console/src/environments/environment';\r\n\r\nHC_more(Highcharts);\r\nNoDataToDisplay(Highcharts);\r\nHC_Exporting(Highcharts);\r\n\r\n// Remove AvaTab interface since we'll use TabItem from the library\r\n\r\ninterface DateRange {\r\n  fromDate: string;\r\n  toDate: string;\r\n}\r\n\r\ninterface ChartColors {\r\n  primary: string;\r\n  secondary: string;\r\n  tertiary: string;\r\n  success: string;\r\n  warning: string;\r\n  danger: string;\r\n}\r\n\r\ninterface ChartDefaults {\r\n  backgroundColor: string;\r\n  fontFamily: string;\r\n  height: number;\r\n}\r\n@Component({\r\n  selector: 'app-analytics',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    HighchartsChartModule,\r\n    MatSelectModule,\r\n    MatFormFieldModule,\r\n    MatOptionModule,\r\n    MatInputModule,\r\n    ButtonComponent,\r\n    CalendarComponent,\r\n    TextCardComponent,\r\n    IconComponent\r\n  ],\r\n  templateUrl: './analytics.component.html',\r\n  styleUrls: ['./analytics.component.scss']\r\n})\r\nexport class AnalyticsComponent implements OnInit, OnDestroy {\r\n  private readonly destroy$ = new Subject<void>();\r\n  private readonly CHART_COLORS: ChartColors = {\r\n    primary: '#4F46E5',      // Modern Indigo\r\n    secondary: '#059669',    // Emerald Green\r\n    tertiary: '#DC2626',     // Modern Red\r\n    success: '#10B981',      // Success Green\r\n    warning: '#F59E0B',      // Amber Warning\r\n    danger: '#EF4444'        // Error Red\r\n  };\r\n\r\n  // Professional color palettes for different chart types\r\n  private readonly PROFESSIONAL_COLORS = {\r\n    userConsumption: '#8B5CF6',     // Purple - User related\r\n    linesOfCode: '#3B82F6',         // Blue - Code related\r\n    topUseCases: '#10B981',         // Green - Success/Usage\r\n    numberOfRequests: '#F59E0B',    // Orange - Activity\r\n    topLanguages: '#EF4444',        // Red - Languages\r\n    userResponse: ['#8B5CF6', '#EC4899', '#F59E0B', '#10B981'], // Multi-color for pie\r\n    userActivity: ['#10B981', '#EF4444'], // Green/Red for active/inactive\r\n    responseTime: '#06B6D4',        // Cyan - Performance\r\n    studioUsage: ['#8B5CF6', '#EC4899', '#F59E0B', '#10B981', '#EF4444'], // Multi-color\r\n    topAgents: '#EC4899',           // Pink - Agents\r\n    agentCreated: '#14B8A6',        // Teal - Creation\r\n    toolAnalytics: ['#F59E0B', '#8B5CF6'], // Orange/Purple for tools\r\n    toolUsage: '#F97316',           // Orange - Tool usage\r\n    adoptionRate: '#8B5CF6',        // Purple - Adoption\r\n    collaborativeUserConsumption: '#EC4899' // Pink - Collaborative users\r\n  };\r\n  private readonly CHART_DEFAULTS: ChartDefaults = {\r\n    backgroundColor: 'transparent',\r\n    fontFamily: 'Mulish, sans-serif',\r\n    height: 320\r\n  };\r\n  Highcharts: typeof Highcharts = Highcharts;\r\n  vmFG!: FormGroup;\r\n  dateRange: DateRange = {\r\n    fromDate: this.getDefaultFromDate(),\r\n    toDate: this.getDefaultToDate()\r\n  };\r\n  activeTab: string = 'usecase';\r\n\r\n  tabs: TabItem[] = [\r\n    {\r\n      id: 'usecase',\r\n      label: 'Individual'\r\n    },\r\n    {\r\n      id: 'agents',\r\n      label: 'Collaborative'\r\n    }\r\n  ];\r\n\r\n  tabListStyle = {\r\n    'background': 'var(--card-bg)',\r\n    'border': '1px solid var(--card-border)',\r\n    'border-radius': '8px',\r\n    'padding': '0.25rem'\r\n  };\r\n  isLoading = false;\r\n  usageLoader = false;\r\n  metricesLoader = false;\r\n  consumptionLoader = false;\r\n  agentMetricsLoader = false;\r\n  chartLoadingStates: { [key: string]: boolean } = {};\r\n  chartOptions: { [key: string]: Highcharts.Options } = {};\r\n  chartRefs: { [key: string]: Highcharts.Chart } = {};\r\n  analyticsData: any = {};\r\n  useCaseMetrics: any = {};\r\n  agentMetrics: any = {};\r\n  userActivity: any = {};\r\n  userActivityStats: any = {};\r\n  userConsumption: any[] = [];\r\n  toolUsage: any[] = [];\r\n  sortedAnalytics: any[] = [];\r\n  agentMetricsTableData: any[] = [];\r\n  expandedRows: Set<number> = new Set();\r\n  noDataAvailable = false;\r\n  agentMetricsNoDataAvailable = false;\r\n  selectedUseCases: string[] = [];\r\n  useCaseList: any[] = [];\r\n  sortedUseCaseList: any[] = [];\r\n  searchText = '';\r\n  filteringEnabled = false;\r\n  showDownloadOptions = false;\r\n  public langfuseUrl = environment.consoleLangfuseUrl;\r\n  public ICLAnalyticsUrl = environment.consoleTruelensUrl;\r\n\r\n  constructor(\r\n    private analyticsService: AnalyticsService,\r\n    private fb: FormBuilder\r\n  ) {\r\n    this.initializeForm();\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.initializeAnalytics();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n  }\r\n\r\n  private initializeForm(): void {\r\n    this.vmFG = this.fb.group({\r\n      fromDate: [this.dateRange.fromDate],\r\n      toDate: [this.dateRange.toDate]\r\n    });\r\n    this.vmFG.valueChanges.pipe(takeUntil(this.destroy$)).subscribe(values => {\r\n      if (values.fromDate && values.toDate) {\r\n        this.dateRange.fromDate = values.fromDate;\r\n        this.dateRange.toDate = values.toDate;\r\n      }\r\n    });\r\n  }\r\n\r\n  private initializeAnalytics(): void {\r\n    this.loadAnalyticsData();\r\n  }\r\n\r\n  onTabChange(tabItem: TabItem): void {\r\n    this.activeTab = tabItem.id;\r\n    this.loadTabSpecificData(tabItem.id);\r\n  }\r\n\r\n  getActiveTab(): TabItem {\r\n    return this.tabs.find(tab => tab.id === this.activeTab) || this.tabs[0];\r\n  }\r\n\r\n  private loadTabSpecificData(tabId: string): void {\r\n    switch (tabId) {\r\n      case 'usecase':\r\n        this.loadUseCaseData();\r\n        break;\r\n      case 'agents':\r\n        this.loadAgentData();\r\n        break;\r\n    }\r\n  }\r\n\r\n  onDateRangeChange(): void {\r\n    const formValues = this.vmFG.value;\r\n    if (formValues.fromDate && formValues.toDate) {\r\n      this.dateRange.fromDate = formValues.fromDate;\r\n      this.dateRange.toDate = formValues.toDate;\r\n    }\r\n  }\r\n\r\n  applyFilter(): void {\r\n    this.onDateRangeChange();\r\n    if (!this.isValidDateRange()) {\r\n      console.error('Invalid date range selected');\r\n      return;\r\n    }\r\n    this.loadAnalyticsData();\r\n  }\r\n\r\n  private isValidDateRange(): boolean {\r\n    const fromDate = new Date(this.dateRange.fromDate);\r\n    const toDate = new Date(this.dateRange.toDate);\r\n    return fromDate <= toDate && fromDate <= new Date();\r\n  }\r\n\r\n  private getDefaultFromDate(): string {\r\n    const date = new Date();\r\n    // Set to the 1st day of the current month\r\n    date.setDate(1);\r\n    return date.toISOString().split('T')[0];\r\n  }\r\n\r\n  private getDefaultToDate(): string {\r\n    return new Date().toISOString().split('T')[0];\r\n  }\r\n\r\n  private formatDateForAPI(dateString: string): string {\r\n    const [year, month, day] = dateString.split('-');\r\n    return `${day}-${month}-${year}`;\r\n  }\r\n\r\n  private getFormattedDateRangeForAPI(): DateRange {\r\n    return {\r\n      fromDate: this.formatDateForAPI(this.dateRange.fromDate),\r\n      toDate: this.formatDateForAPI(this.dateRange.toDate)\r\n    };\r\n  }\r\n\r\n  private loadAnalyticsData(): void {\r\n    this.isLoading = true;\r\n    switch (this.activeTab) {\r\n      case 'usecase':\r\n        this.loadUseCaseData();\r\n        break;\r\n      case 'agents':\r\n        this.loadAgentData();\r\n        break;\r\n      default:\r\n        this.isLoading = false;\r\n    }\r\n  }\r\n\r\n  private loadUseCaseData(): void {\r\n    this.isLoading = true;\r\n    this.usageLoader = true;\r\n    const formattedDateRange = this.getFormattedDateRangeForAPI();\r\n    forkJoin({\r\n      mainAnalytics: this.analyticsService.getAllAnalyticsData(formattedDateRange),\r\n      totalRequestMetrics: this.analyticsService.totalRequestCount(formattedDateRange),\r\n      userConsumption: this.analyticsService.getUserUsageAnalytics(formattedDateRange),\r\n      userActivity: this.analyticsService.getUserActivity(formattedDateRange),\r\n      responseTime: this.analyticsService.getResponseTime(formattedDateRange)\r\n    }).pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (responses) => {\r\n          this.parseAndCreateChartsWithSeparateAPIs(responses);\r\n          this.useCaseMetrics = responses.totalRequestMetrics;\r\n          this.isLoading = false;\r\n        },\r\n        error: (error) => {\r\n          console.error('Error loading analytics data:', error);\r\n          this.isLoading = false;\r\n        }\r\n      });\r\n  }\r\n\r\n  private parseAndCreateChartsWithSeparateAPIs(responses: any): void {\r\n    const mainData = responses.mainAnalytics;\r\n    const userConsumptionData = responses.userConsumption;\r\n    const userActivityData = responses.userActivity;\r\n    const responseTimeData = responses.responseTime;\r\n\r\n    Object.keys(this.chartLoadingStates).forEach(key => {\r\n      this.chartLoadingStates[key] = false;\r\n    });\r\n\r\n    let userAnalyticsArray = null;\r\n    if (userConsumptionData) {\r\n      if (userConsumptionData.userLevelAnalytics && userConsumptionData.userLevelAnalytics.length > 0) {\r\n        userAnalyticsArray = userConsumptionData.userLevelAnalytics;\r\n      } else if (userConsumptionData.userAnalytics && userConsumptionData.userAnalytics.length > 0) {\r\n        userAnalyticsArray = userConsumptionData.userAnalytics;\r\n      } else if (Array.isArray(userConsumptionData) && userConsumptionData.length > 0) {\r\n        userAnalyticsArray = userConsumptionData;\r\n      }\r\n    }\r\n\r\n    if (userAnalyticsArray && userAnalyticsArray.length > 0) {\r\n      this.chartOptions['userConsumption'] = this.createUserConsumptionChart(userAnalyticsArray);\r\n      this.sortedAnalytics = userAnalyticsArray;\r\n      this.noDataAvailable = false;\r\n      this.usageLoader = false;\r\n    } else {\r\n      this.chartOptions['userConsumption'] = this.createNoDataChart('User Consumption');\r\n      this.sortedAnalytics = [];\r\n      this.noDataAvailable = true;\r\n      this.usageLoader = false;\r\n    }\r\n\r\n    if (mainData.linesOfCodeAnalytics && mainData.linesOfCodeAnalytics.length > 0) {\r\n      const categories = mainData.linesOfCodeAnalytics.map((item: any) => item.date);\r\n      const values = mainData.linesOfCodeAnalytics.map((item: any) => item.count);\r\n      this.chartOptions['linesOfCodeProcessed'] = this.createLineChart('Lines of code Processed', categories, values, 'Lines of Code');\r\n    } else {\r\n      this.chartOptions['linesOfCodeProcessed'] = this.createNoDataChart('Lines of code Processed');\r\n    }\r\n\r\n    if (mainData.useCaseLevelAnalytics && mainData.useCaseLevelAnalytics.length > 0) {\r\n      const sortedUseCases = mainData.useCaseLevelAnalytics\r\n        .sort((a: any, b: any) => b.count - a.count)\r\n        .slice(0, 5);\r\n      const categories = sortedUseCases.map((item: any) => item.useCaseCode);\r\n      const values = sortedUseCases.map((item: any) => item.count);\r\n      this.chartOptions['topUseCases'] = this.createColumnChart('Top 5 Individual Agents', categories, values, 'Usage Count', 'agents');\r\n    } else {\r\n      this.chartOptions['topUseCases'] = this.createNoDataChart('Top 5 Individual Agents');\r\n    }\r\n\r\n    let requestData = mainData.numberOfRequestsAnalytics || mainData.numberOfRequestAnalytics;\r\n    if (requestData && requestData.length > 0) {\r\n      const categories = requestData.map((item: any) => item.date);\r\n      const values = requestData.map((item: any) => item.requestCount);\r\n      this.chartOptions['numberOfRequests'] = this.createLineChart('Number of Requests', categories, values, 'Request Count');\r\n    } else {\r\n      this.chartOptions['numberOfRequests'] = this.createNoDataChart('Number of Requests');\r\n    }\r\n\r\n    if (mainData.programmingLanguageAnalytics && mainData.programmingLanguageAnalytics.length > 0) {\r\n      const categories = mainData.programmingLanguageAnalytics.map((item: any) => item.programmingLanguage);\r\n      const values = mainData.programmingLanguageAnalytics.map((item: any) => item.count);\r\n      this.chartOptions['topLanguages'] = this.createColumnChart('Top 5 Languages / Frameworks', categories, values, 'Usage Count', 'languages');\r\n    } else {\r\n      this.chartOptions['topLanguages'] = this.createNoDataChart('Top 5 Languages / Frameworks');\r\n    }\r\n\r\n    if (mainData.userResponseAnalytics && mainData.userResponseAnalytics.length > 0) {\r\n      const pieData = mainData.userResponseAnalytics.map((item: any) => [item.response, item.percentage]);\r\n      this.chartOptions['userResponse'] = this.createPieChart('User Response', pieData, 'userResponse');\r\n    } else {\r\n      this.chartOptions['userResponse'] = this.createNoDataChart('User Response');\r\n    }\r\n\r\n    let activeUsers = 0;\r\n    let inactiveUsers = 0;\r\n    let totalUsers = 0;\r\n\r\n    if (userActivityData && userActivityData.userActivity) {\r\n      const activity = userActivityData.userActivity;\r\n      activeUsers = activity.activeUsers || 0;\r\n      inactiveUsers = activity.inactiveUsers || 0;\r\n      totalUsers = activity.totalUsers || 0;\r\n    } else if (userActivityData) {\r\n      activeUsers = userActivityData.activeUsers || 0;\r\n      inactiveUsers = userActivityData.inactiveUsers || 0;\r\n      totalUsers = userActivityData.totalUsers || 0;\r\n    }\r\n\r\n    const total = totalUsers || (activeUsers + inactiveUsers);\r\n    console.log('Active Users:', activeUsers, 'Inactive Users:', inactiveUsers, 'Total:', total);\r\n\r\n    if (total > 0) {\r\n      const inactivePercentage = (inactiveUsers / total) * 100;\r\n      const activePercentage = (activeUsers / total) * 100;\r\n      const pieData = [\r\n        ['Active Users', activePercentage],\r\n        ['Inactive Users', inactivePercentage]\r\n      ];\r\n      this.chartOptions['userActivity'] = this.createPieChart('% Dormant User', pieData, 'dormantUser');\r\n      this.userActivity = { activeUsers, dormantUsers: inactiveUsers, totalUsers: total };\r\n    } else {\r\n      this.chartOptions['userActivity'] = this.createNoDataChart('% Dormant User');\r\n    }\r\n\r\n    let categories: string[] = [];\r\n    let values: number[] = [];\r\n\r\n    if (responseTimeData) {\r\n      if (responseTimeData.responseTimes && Array.isArray(responseTimeData.responseTimes)) {\r\n        categories = responseTimeData.responseTimes.map((item: any) => item.createdAt);\r\n        values = responseTimeData.responseTimes.map((item: any) => item.responseTime);\r\n      } else if (responseTimeData.categories && responseTimeData.series) {\r\n        categories = responseTimeData.categories;\r\n        values = responseTimeData.series;\r\n      } else if (responseTimeData.categories && responseTimeData.ySeries) {\r\n        categories = responseTimeData.categories;\r\n        values = responseTimeData.ySeries;\r\n      } else if (Array.isArray(responseTimeData) && responseTimeData.length > 0) {\r\n        categories = responseTimeData.map((item: any) => item.createdAt || item.date);\r\n        values = responseTimeData.map((item: any) => item.responseTime || item.time);\r\n      }\r\n    }\r\n\r\n    if (categories.length > 0 && values.length > 0) {\r\n      this.chartOptions['responseTime'] = this.createLineChart('Response Time', categories, values, 'Response Time (ms)');\r\n    } else {\r\n      this.chartOptions['responseTime'] = this.createNoDataChart('Response Time');\r\n    }\r\n  }\r\n\r\n  private loadAgentData(): void {\r\n    this.isLoading = true;\r\n    const formattedDateRange = this.getFormattedDateRangeForAPI();\r\n    this.analyticsService.getAgenticAIAnalytics(formattedDateRange)\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (data) => {\r\n          this.processAgentAnalyticsData(data);\r\n          this.loadAgentCharts(data);\r\n          this.isLoading = false;\r\n        },\r\n        error: (error) => {\r\n          console.error('Error loading agent data:', error);\r\n          this.isLoading = false;\r\n        }\r\n      });\r\n  }\r\n\r\n  private processAgentAnalyticsData(data: any): void {\r\n    this.agentMetrics = data;\r\n    this.userActivityStats = data.userActivityStats;\r\n    this.toolUsage = data.toolUsage || [];\r\n    this.userConsumption = data.userConsumption || [];\r\n  }\r\n\r\n\r\n\r\n  toggleRowExpansion(index: number): void {\r\n    if (this.expandedRows.has(index)) {\r\n      this.expandedRows.delete(index);\r\n    } else {\r\n      this.expandedRows.add(index);\r\n    }\r\n  }\r\n\r\n  isRowExpanded(index: number): boolean {\r\n    return this.expandedRows.has(index);\r\n  }\r\n\r\n  private loadAgentMetricsTable(data: any): void {\r\n    this.agentMetricsLoader = true;\r\n    if (data && data.agentMetrics && data.agentMetrics.length > 0) {\r\n      this.agentMetricsTableData = data.agentMetrics;\r\n      this.agentMetricsNoDataAvailable = false;\r\n    } else {\r\n      this.agentMetricsTableData = [];\r\n      this.agentMetricsNoDataAvailable = true;\r\n    }\r\n    this.agentMetricsLoader = false;\r\n  }\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n  private loadAgentCharts(data: any): void {\r\n\r\n    this.agentMetrics = data;\r\n\r\n    if (data.studioUsage && data.studioUsage.length > 0) {\r\n      const studioUsageData = data.studioUsage.map((item: any) => [item.domainName, item.percentage]);\r\n      this.chartOptions['studioUsage'] = this.createPieChart('Studio Usage', studioUsageData, 'studioUsage');\r\n    } else {\r\n      this.chartOptions['studioUsage'] = this.createNoDataChart('Studio Usage');\r\n    }\r\n\r\n    if (data.topAgents && data.topAgents.length > 0) {\r\n      const categories = data.topAgents.slice(0, 5).map((agent: any) => agent.agentName);\r\n      const series = data.topAgents.slice(0, 5).map((agent: any) => agent.usageCount);\r\n      this.chartOptions['topAgents'] = this.createColumnChart('Top 5 Collaborative Agents', categories, series, 'Usage Count', 'topAgents');\r\n    } else {\r\n      this.chartOptions['topAgents'] = this.createNoDataChart('Top 5 Collaborative Agents');\r\n    }\r\n\r\n    if (data.agentCreated && data.agentCreated.length > 0) {\r\n      const categories = data.agentCreated.map((item: any) => item.teamName);\r\n      const series = data.agentCreated.map((item: any) => item.usageCount);\r\n      this.chartOptions['agentCreated'] = this.createColumnChart('Collaborative Agent Created', categories, series, 'Usage Count', 'agentCreated');\r\n    } else {\r\n      this.chartOptions['agentCreated'] = this.createNoDataChart('Collaborative Agent Created');\r\n    }\r\n\r\n    this.loadAgentMetricsTable(data);\r\n\r\n    if (data.toolAnalytics && data.toolAnalytics.length > 0) {\r\n      const toolAnalyticsItem = data.toolAnalytics[0];\r\n      const toolAnalyticsData = [\r\n        ['User Defined Tools', toolAnalyticsItem.userDefinedPercentage],\r\n        ['Built-in Tools', toolAnalyticsItem.builtInPercentage]\r\n      ];\r\n      this.chartOptions['toolAnalytics'] = this.createPieChart('Tool Analytics', toolAnalyticsData, 'default');\r\n    } else {\r\n      this.chartOptions['toolAnalytics'] = this.createNoDataChart('Tool Analytics');\r\n    }\r\n\r\n    if (data.toolUsage && data.toolUsage.length > 0) {\r\n      const categories = data.toolUsage.slice(0, 5).map((tool: any) => tool.toolName);\r\n      const series = data.toolUsage.slice(0, 5).map((tool: any) => tool.usageCount);\r\n      this.chartOptions['toolUsage'] = this.createHorizontalBarChart('Tool Usage', categories, series, 'Usage Count', 'toolUsage');\r\n    } else {\r\n      this.chartOptions['toolUsage'] = this.createNoDataChart('Tool Usage');\r\n    }\r\n\r\n    if (data.adoptionRate && data.adoptionRate.length > 0) {\r\n      const categories = data.adoptionRate.map((item: any) => item.workflowName);\r\n      const series = data.adoptionRate.map((item: any) => item.executionCount);\r\n      this.chartOptions['adoptionRate'] = this.createLineChart('Adoption Rate', categories, series, 'Execution Count');\r\n    } else {\r\n      this.chartOptions['adoptionRate'] = this.createNoDataChart('Adoption Rate');\r\n    }\r\n\r\n    if (data.userConsumption && data.userConsumption.length > 0) {\r\n      const transformedData = data.userConsumption.map((user: any) => ({\r\n        userSignature: user.email,\r\n        requestCount: user.consumptionCount\r\n      }));\r\n      this.chartOptions['userConsumption'] = this.createUserConsumptionChart(transformedData);\r\n    } else {\r\n      this.chartOptions['userConsumption'] = this.createNoDataChart('User Consumption');\r\n    }\r\n\r\n    if (data.userActivityStats && Array.isArray(data.userActivityStats) && data.userActivityStats.length > 0) {\r\n      const userStats = data.userActivityStats[0];\r\n      const activePercentage = userStats.activeUserPercentage || 0;\r\n      const inactivePercentage = userStats.inactiveUserPercentage || 0;\r\n\r\n      console.log('User stats:', userStats);\r\n      console.log('Active percentage:', activePercentage);\r\n      console.log('Inactive percentage:', inactivePercentage);\r\n\r\n      if (activePercentage + inactivePercentage > 0) {\r\n        const dormantUserData = [\r\n          ['Active Users', Math.round(activePercentage * 100) / 100],\r\n          ['Inactive Users', Math.round(inactivePercentage * 100) / 100]\r\n        ];\r\n\r\n        console.log('Dormant User Chart Data:', dormantUserData);\r\n        this.chartOptions['userActivity'] = this.createPieChart('% Dormant User', dormantUserData, 'dormantUser');\r\n      } else {\r\n        this.chartOptions['userActivity'] = this.createNoDataChart('% Dormant User');\r\n      }\r\n    } else {\r\n      console.log('No user activity stats found');\r\n      this.chartOptions['userActivity'] = this.createNoDataChart('% Dormant User');\r\n    }\r\n  }\r\n\r\n  // Chart creation methods\r\n  private createLineChart(title: string, categories: string[], data: number[], seriesName: string): Highcharts.Options {\r\n    // Get professional color based on chart title\r\n    let chartColor = this.CHART_COLORS.primary;\r\n    if (title.includes('Lines of code')) {\r\n      chartColor = this.PROFESSIONAL_COLORS.linesOfCode;\r\n    } else if (title.includes('Number of Requests')) {\r\n      chartColor = this.PROFESSIONAL_COLORS.numberOfRequests;\r\n    } else if (title.includes('Response Time')) {\r\n      chartColor = this.PROFESSIONAL_COLORS.responseTime;\r\n    } else if (title.includes('Adoption Rate')) {\r\n      chartColor = this.PROFESSIONAL_COLORS.adoptionRate;\r\n    }\r\n\r\n    const commonConfig = this.getCommonChartConfig();\r\n\r\n    return {\r\n      ...commonConfig,\r\n      chart: {\r\n        ...commonConfig.chart,\r\n        type: 'line',\r\n        height: this.CHART_DEFAULTS.height,\r\n        zoomType: 'x',\r\n        panning: {\r\n          enabled: true,\r\n          type: 'x'\r\n        },\r\n        panKey: 'shift'\r\n      } as any,\r\n      xAxis: {\r\n        categories: categories,\r\n        labels: {\r\n          rotation: -45,\r\n          style: {\r\n            color: '#6B7280',\r\n            fontSize: '10px',\r\n            fontFamily: 'Inter, sans-serif'\r\n          }\r\n        },\r\n        lineColor: '#E5E7EB',\r\n        tickColor: '#E5E7EB'\r\n      },\r\n      yAxis: {\r\n        title: {\r\n          text: '',\r\n          style: {\r\n            color: '#6B7280'\r\n          }\r\n        },\r\n        labels: {\r\n          style: {\r\n            color: '#6B7280',\r\n            fontSize: '10px',\r\n            fontFamily: 'Inter, sans-serif'\r\n          }\r\n        },\r\n        gridLineColor: '#F3F4F6'\r\n      },\r\n      series: [{\r\n        name: seriesName,\r\n        type: 'line',\r\n        data: data,\r\n        color: chartColor,\r\n        lineWidth: 3,\r\n        marker: {\r\n          fillColor: chartColor,\r\n          lineColor: '#FFFFFF',\r\n          lineWidth: 2,\r\n          radius: 5\r\n        },\r\n        shadow: {\r\n          color: chartColor,\r\n          opacity: 0.3,\r\n          width: 3\r\n        }\r\n      }],\r\n      plotOptions: {\r\n        line: {\r\n          dataLabels: {\r\n            enabled: false\r\n          },\r\n          marker: {\r\n            enabled: true\r\n          }\r\n        }\r\n      }\r\n    };\r\n  }\r\n\r\n  private createColumnChart(title: string, categories: string[], data: number[], seriesName: string, chartType: string = 'default'): Highcharts.Options {\r\n    // Get professional color based on chart type\r\n    let color = this.PROFESSIONAL_COLORS.topUseCases; // Default green\r\n\r\n    switch (chartType) {\r\n      case 'languages':\r\n        color = this.PROFESSIONAL_COLORS.topLanguages; // Red for languages\r\n        break;\r\n      case 'topAgents':\r\n        color = this.PROFESSIONAL_COLORS.topAgents; // Pink for top collaborative agents\r\n        break;\r\n      case 'agentCreated':\r\n        color = this.PROFESSIONAL_COLORS.agentCreated; // Teal for agent creation\r\n        break;\r\n      case 'agents':\r\n        color = this.PROFESSIONAL_COLORS.topUseCases; // Green for individual agents\r\n        break;\r\n      default:\r\n        color = this.PROFESSIONAL_COLORS.topUseCases; // Default green\r\n    }\r\n\r\n    const commonConfig = this.getCommonChartConfig();\r\n\r\n    return {\r\n      ...commonConfig,\r\n      chart: {\r\n        ...commonConfig.chart,\r\n        type: 'column',\r\n        height: this.CHART_DEFAULTS.height,\r\n        zoomType: 'x',\r\n        panning: {\r\n          enabled: true,\r\n          type: 'x'\r\n        },\r\n        panKey: 'shift'\r\n      } as any,\r\n      xAxis: {\r\n        categories: categories,\r\n        labels: {\r\n          style: {\r\n            color: '#6B7280',\r\n            fontSize: '10px',\r\n            fontFamily: 'Inter, sans-serif'\r\n          }\r\n        },\r\n        lineColor: '#E5E7EB',\r\n        tickColor: '#E5E7EB'\r\n      },\r\n      yAxis: {\r\n        title: {\r\n          text: '',\r\n          style: {\r\n            color: '#6B7280'\r\n          }\r\n        },\r\n        labels: {\r\n          style: {\r\n            color: '#6B7280',\r\n            fontSize: '10px',\r\n            fontFamily: 'Inter, sans-serif'\r\n          }\r\n        },\r\n        gridLineColor: '#F3F4F6'\r\n      },\r\n      series: [{\r\n        name: seriesName,\r\n        type: 'column',\r\n        data: data,\r\n        color: color,\r\n        borderWidth: 0,\r\n        borderRadius: 6,\r\n        shadow: {\r\n          color: color,\r\n          opacity: 0.2,\r\n          width: 2\r\n        }\r\n      }],\r\n      plotOptions: {\r\n        column: {\r\n          dataLabels: {\r\n            enabled: true,\r\n            style: {\r\n              color: '#333333',      // Dark text for better visibility\r\n              fontSize: '12px',\r\n              fontWeight: '600',\r\n              textOutline: '1px contrast' // Add text outline for better readability\r\n            }\r\n          },\r\n          borderRadius: 4,\r\n          pointPadding: 0.2,    // Uniform spacing between bars\r\n          groupPadding: 0.15,   // Uniform spacing between groups\r\n          maxPointWidth: 60     // Maximum bar width for consistency\r\n        }\r\n      }\r\n    };\r\n  }\r\n\r\n  private createPieChart(title: string, data: any[], chartType: string = 'default'): Highcharts.Options {\r\n    // Professional color schemes for different pie chart types\r\n    let colors: string[] = [];\r\n\r\n    if (chartType === 'userResponse') {\r\n      colors = this.PROFESSIONAL_COLORS.userResponse; // Multi-color palette\r\n    } else if (chartType === 'dormantUser') {\r\n      colors = this.PROFESSIONAL_COLORS.userActivity; // Green/Red for active/inactive\r\n    } else if (chartType === 'studioUsage') {\r\n      colors = this.PROFESSIONAL_COLORS.studioUsage; // Multi-color for studio usage\r\n    } else if (chartType === 'default') {\r\n      colors = this.PROFESSIONAL_COLORS.toolAnalytics; // Orange/Purple for tool analytics\r\n    } else {\r\n      colors = [this.CHART_COLORS.primary, this.CHART_COLORS.secondary]; // Fallback\r\n    }\r\n\r\n    const commonConfig = this.getCommonChartConfig();\r\n\r\n    return {\r\n      ...commonConfig,\r\n      chart: {\r\n        ...commonConfig.chart,\r\n        type: 'pie',\r\n        height: this.CHART_DEFAULTS.height\r\n      },\r\n      series: [{\r\n        name: 'Percentage',\r\n        type: 'pie',\r\n        data: data,\r\n        innerSize: '60%',\r\n        colors: colors,\r\n        dataLabels: {\r\n          enabled: false\r\n        }\r\n      }],\r\n      legend: {\r\n        enabled: true,\r\n        align: 'right',\r\n        verticalAlign: 'middle',\r\n        layout: 'vertical',\r\n        itemStyle: {\r\n          color: '#374151',\r\n          fontSize: '11px',\r\n          fontFamily: 'Inter, sans-serif',\r\n          fontWeight: '500'\r\n        },\r\n        symbolRadius: 8,\r\n        itemMarginBottom: 8\r\n      },\r\n      plotOptions: {\r\n        pie: {\r\n          allowPointSelect: false,\r\n          cursor: 'pointer',\r\n          dataLabels: {\r\n            enabled: false\r\n          },\r\n          showInLegend: true,\r\n          borderWidth: 0\r\n        }\r\n      }\r\n    };\r\n  }\r\n\r\n  private createUserConsumptionChart(data: any[]): Highcharts.Options {\r\n    const categories = data.map(item => item.userSignature || item.email || item.userName || 'Unknown User');\r\n    const values = data.map(item => item.requestCount || item.usageCount || item.count || 0);\r\n\r\n    // Professional height for better visual appearance\r\n    const chartHeight = this.CHART_DEFAULTS.height;\r\n    const showScrollbar = categories.length > 8; // Show scrollbar if more than 8 users\r\n\r\n    return {\r\n      chart: {\r\n        type: 'bar',\r\n        backgroundColor: this.CHART_DEFAULTS.backgroundColor,\r\n        height: chartHeight,\r\n        marginLeft: 200, // Reduced margin for better space utilization\r\n        marginRight: 80, // Add right margin for value labels\r\n        scrollablePlotArea: showScrollbar ? {\r\n          minHeight: categories.length * 30 + 80, // Reduced spacing\r\n          scrollPositionY: 0\r\n        } : undefined,\r\n        style: {\r\n          fontFamily: this.CHART_DEFAULTS.fontFamily\r\n        }\r\n      } as any,\r\n      title: {\r\n        text: '',\r\n        style: {\r\n          display: 'none'\r\n        }\r\n      },\r\n      xAxis: {\r\n        categories: categories,\r\n        labels: {\r\n          style: {\r\n            color: '#333333',\r\n            fontSize: '11px',\r\n            fontWeight: '500',\r\n            fontFamily: 'Mulish, sans-serif'\r\n          },\r\n          overflow: 'allow',\r\n          step: 1,\r\n          useHTML: true,\r\n          formatter: function() {\r\n            // Show full email at the start of each bar\r\n            const email = this.value as string;\r\n            return `<div style=\"width: 180px; text-align: left; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;\">${email}</div>`;\r\n          }\r\n        },\r\n        lineColor: 'transparent',\r\n        tickColor: 'transparent',\r\n        min: 0,\r\n        max: showScrollbar ? Math.min(10, categories.length - 1) : undefined // Show 11 items initially if scrollable\r\n      },\r\n      yAxis: {\r\n        title: {\r\n          text: '',\r\n          style: {\r\n            color: '#666666'\r\n          }\r\n        },\r\n        labels: {\r\n          style: {\r\n            color: '#666666',\r\n            fontSize: '10px',\r\n            fontFamily: 'Mulish, sans-serif'\r\n          }\r\n        },\r\n        gridLineColor: 'transparent',\r\n        lineColor: 'transparent'\r\n      },\r\n      series: [{\r\n        name: 'Usage',\r\n        type: 'bar',\r\n        data: values,\r\n        color: '#8B7EC8',\r\n        borderWidth: 0,\r\n        borderRadius: 2\r\n      }],\r\n      legend: {\r\n        enabled: false\r\n      },\r\n      plotOptions: {\r\n        bar: {\r\n          dataLabels: {\r\n            enabled: true,\r\n            inside: false,\r\n            align: 'right',\r\n            x: 5, // Position labels slightly to the right of bars\r\n            style: {\r\n              color: '#333333',\r\n              fontSize: '12px',\r\n              fontWeight: '600',\r\n              fontFamily: 'Mulish, sans-serif',\r\n              textOutline: 'none'\r\n            },\r\n            formatter: function() {\r\n              return this.y;\r\n            }\r\n          },\r\n          color: '#8B7EC8',\r\n          borderRadius: 4,\r\n          pointPadding: 0.15, // Reduced padding for better fit\r\n          groupPadding: 0.05, // Reduced group padding\r\n          maxPointWidth: 25   // Reduced max width for better proportion\r\n        }\r\n      },\r\n      tooltip: {\r\n        enabled: true,\r\n        formatter: function() {\r\n          return '<b>' + this.x + '</b><br/>Requests: ' + this.y;\r\n        }\r\n      },\r\n      navigation: {\r\n        buttonOptions: {\r\n          enabled: true,\r\n          theme: {\r\n            stroke: '#8B7EC8',\r\n            r: 2,\r\n            states: {\r\n              hover: {\r\n                fill: '#8B7EC8',\r\n                stroke: '#8B7EC8'\r\n              },\r\n              select: {\r\n                fill: '#8B7EC8',\r\n                stroke: '#8B7EC8'\r\n              }\r\n            }\r\n          } as any\r\n        }\r\n      },\r\n      exporting: {\r\n        enabled: false\r\n      },\r\n      credits: {\r\n        enabled: false\r\n      }\r\n    };\r\n  }\r\n\r\n  private createNoDataChart(title: string): Highcharts.Options {\r\n    return {\r\n      chart: {\r\n        backgroundColor: 'transparent'\r\n      },\r\n      title: {\r\n        text: title,\r\n        align: 'left',\r\n        style: {\r\n          color: 'var(--text-color)',\r\n          fontSize: '16px',\r\n          fontWeight: '600'\r\n        }\r\n      },\r\n      series: [],\r\n      lang: {\r\n        noData: 'No data available for the selected period'\r\n      },\r\n      noData: {\r\n        style: {\r\n          fontWeight: 'bold',\r\n          fontSize: '15px',\r\n          color: 'var(--text-secondary)'\r\n        }\r\n      },\r\n      exporting: {\r\n        enabled: false\r\n      },\r\n      credits: {\r\n        enabled: false\r\n      }\r\n    };\r\n  }\r\n\r\n  // Reusable method to create charts with no-data fallback\r\n  private createChartWithFallback<T>(\r\n    data: T[] | null | undefined,\r\n    chartCreator: (data: T[]) => Highcharts.Options,\r\n    title: string\r\n  ): Highcharts.Options {\r\n    if (data && data.length > 0) {\r\n      return chartCreator(data);\r\n    } else {\r\n      return this.createNoDataChart(title);\r\n    }\r\n  }\r\n\r\n  // Common chart styling configuration\r\n  private getCommonChartConfig(): Partial<Highcharts.Options> {\r\n    return {\r\n      chart: {\r\n        backgroundColor: 'transparent'\r\n      },\r\n      title: {\r\n        text: '',\r\n        style: {\r\n          display: 'none'\r\n        }\r\n      },\r\n      exporting: {\r\n        enabled: false\r\n      },\r\n      credits: {\r\n        enabled: false\r\n      },\r\n      legend: {\r\n        enabled: false\r\n      }\r\n    };\r\n  }\r\n\r\n  // Chart callback functions\r\n  chartCallback = (chart: Highcharts.Chart, chartKey: string): void => {\r\n    this.chartRefs[chartKey] = chart;\r\n  };\r\n\r\n  // Export methods\r\n  downloadExcel(analyticsName: string): void {\r\n    const formattedDateRange = this.getFormattedDateRangeForAPI();\r\n    this.analyticsService.downloadChartExcel(formattedDateRange, analyticsName, 'excel');\r\n  }\r\n\r\n  downloadAgentExcel(analyticsName: string): void {\r\n    const formattedDateRange = this.getFormattedDateRangeForAPI();\r\n    this.analyticsService.downloadAgenticAIExcel(formattedDateRange, analyticsName, 'excel');\r\n  }\r\n\r\n  downloadDump(): void {\r\n    const formattedDateRange = this.getFormattedDateRangeForAPI();\r\n    this.analyticsService.downloadDump(formattedDateRange);\r\n  }\r\n\r\n  // Filter and dropdown methods\r\n  onUseCaseSelectionChanged(event: any): void {\r\n    this.selectedUseCases = event.value;\r\n    this.filteringEnabled = this.selectedUseCases.length > 0;\r\n  }\r\n\r\n  setPayload(): void {\r\n    // Apply filters and reload data\r\n    this.applyFilter();\r\n  }\r\n\r\n  toggleDownloadOptions(event: Event): void {\r\n    event.stopPropagation();\r\n    this.showDownloadOptions = !this.showDownloadOptions;\r\n  }\r\n\r\n  downloadChartsAsPDF(): void {\r\n    // Implementation for PDF download\r\n    console.log('Downloading charts as PDF...');\r\n    this.showDownloadOptions = false;\r\n  }\r\n\r\n  // Digital Ascender Style Horizontal Bar Chart\r\n  private createHorizontalBarChart(title: string, categories: string[], data: number[], seriesName: string, chartType: string = 'default'): Highcharts.Options {\r\n    // Professional colors for horizontal bar charts\r\n    let color = this.PROFESSIONAL_COLORS.toolUsage; // Default orange for tool usage\r\n\r\n    if (chartType === 'toolUsage') {\r\n      color = this.PROFESSIONAL_COLORS.toolUsage; // Professional orange for tool usage\r\n    } else if (chartType === 'userConsumption') {\r\n      color = this.PROFESSIONAL_COLORS.collaborativeUserConsumption; // Pink for collaborative user consumption\r\n    }\r\n\r\n    // Professional height for better visual appearance\r\n    const chartHeight = this.CHART_DEFAULTS.height;\r\n    const showScrollbar = categories.length > 8; // Show scrollbar if more than 8 items\r\n\r\n    const commonConfig = this.getCommonChartConfig();\r\n\r\n    return {\r\n      ...commonConfig,\r\n      chart: {\r\n        ...commonConfig.chart,\r\n        type: 'bar',\r\n        height: chartHeight,\r\n        marginLeft: 180, // Reduced margin for better space utilization\r\n        marginRight: 80, // Add right margin for value labels\r\n        scrollablePlotArea: showScrollbar ? {\r\n          minHeight: categories.length * 30 + 80, // Reduced spacing\r\n          scrollPositionY: 0\r\n        } : undefined,\r\n        zoomType: 'x',\r\n        panning: {\r\n          enabled: true,\r\n          type: 'x'\r\n        },\r\n        panKey: 'shift'\r\n      } as any,\r\n      xAxis: {\r\n        categories: categories,\r\n        title: {\r\n          text: null\r\n        },\r\n        labels: {\r\n          style: {\r\n            color: '#333333',\r\n            fontSize: '11px',\r\n            fontWeight: '500',\r\n            fontFamily: 'Mulish, sans-serif'\r\n          },\r\n          overflow: 'allow',\r\n          step: 1,\r\n          useHTML: true,\r\n          formatter: function() {\r\n            // Show full name at the start of each bar\r\n            const name = this.value as string;\r\n            return `<div style=\"width: 160px; text-align: left; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;\">${name}</div>`;\r\n          }\r\n        },\r\n        lineColor: 'transparent',\r\n        tickColor: 'transparent',\r\n        min: 0,\r\n        max: showScrollbar ? Math.min(10, categories.length - 1) : undefined // Show 11 items initially if scrollable\r\n      },\r\n      yAxis: {\r\n        min: 0,\r\n        title: {\r\n          text: '',\r\n          style: {\r\n            color: '#666666'\r\n          }\r\n        },\r\n        labels: {\r\n          style: {\r\n            color: '#666666',\r\n            fontSize: '10px',\r\n            fontFamily: 'Mulish, sans-serif'\r\n          }\r\n        },\r\n        gridLineColor: 'transparent',\r\n        lineColor: 'transparent'\r\n      },\r\n      plotOptions: {\r\n        bar: {\r\n          dataLabels: {\r\n            enabled: true,\r\n            inside: false,\r\n            align: 'right',\r\n            x: 5, // Position labels slightly to the right of bars\r\n            style: {\r\n              color: '#333333',\r\n              fontSize: '12px',\r\n              fontWeight: '600',\r\n              fontFamily: 'Mulish, sans-serif',\r\n              textOutline: 'none'\r\n            },\r\n            formatter: function() {\r\n              return this.y;\r\n            }\r\n          },\r\n          color: color,\r\n          borderRadius: 4,\r\n          pointPadding: 0.15, // Reduced padding for better fit\r\n          groupPadding: 0.05, // Reduced group padding\r\n          maxPointWidth: 25,   // Reduced max width for better proportion\r\n          borderWidth: 0\r\n        }\r\n      },\r\n      series: [{\r\n        type: 'bar',\r\n        name: seriesName,\r\n        data: data,\r\n        color: color\r\n      }] as any,\r\n      legend: {\r\n        enabled: false\r\n      },\r\n      tooltip: {\r\n        enabled: true,\r\n        formatter: function() {\r\n          return '<b>' + this.x + '</b><br/>' + seriesName + ': ' + this.y;\r\n        }\r\n      }\r\n    };\r\n  }\r\n\r\n  // Common chart configuration helper\r\n  private getBaseChartConfig(type: 'column' | 'bar' | 'pie' | 'line', height: number = this.CHART_DEFAULTS.height): any {\r\n    return {\r\n      chart: {\r\n        type,\r\n        backgroundColor: this.CHART_DEFAULTS.backgroundColor,\r\n        height,\r\n        style: {\r\n          fontFamily: this.CHART_DEFAULTS.fontFamily\r\n        }\r\n      },\r\n      title: {\r\n        text: '',\r\n        style: { display: 'none' }\r\n      },\r\n      credits: { enabled: false },\r\n      legend: { enabled: false }\r\n    };\r\n  }\r\n\r\n  // Utility methods for bar charts\r\n  getBarWidth(value: number, isToolUsage: boolean = false): number {\r\n    if (isToolUsage) {\r\n      const maxValue = Math.max(...this.toolUsage.map(tool => tool.usageCount));\r\n      const calculatedWidth = maxValue > 0 ? (value / maxValue) * 100 : 0;\r\n      // Ensure minimum width of 25% to show tool names\r\n      return Math.max(calculatedWidth, 25);\r\n    } else {\r\n      // Handle both individual and collaborative user consumption data\r\n      let maxValue = 0;\r\n\r\n      if (this.activeTab === 'usecase' && this.sortedAnalytics && this.sortedAnalytics.length > 0) {\r\n        // Individual tab - use sortedAnalytics\r\n        maxValue = Math.max(...this.sortedAnalytics.map(user => user.requestCount || 0));\r\n      } else if (this.activeTab === 'agents' && this.userConsumption && this.userConsumption.length > 0) {\r\n        // Collaborative tab - use userConsumption\r\n        maxValue = Math.max(...this.userConsumption.map(user => user.consumptionCount || user.requestCount || 0));\r\n      }\r\n\r\n      const calculatedWidth = maxValue > 0 ? (value / maxValue) * 100 : 0;\r\n      // Ensure minimum width of 30% to show email addresses\r\n      return Math.max(calculatedWidth, 30);\r\n    }\r\n  }\r\n\r\n\r\n\r\n  // Navigation methods\r\n  public goToLangfuse() {\r\n    window.open(this.langfuseUrl, '_blank');\r\n  }\r\n\r\n  public goToTrulens() {\r\n    window.open(this.ICLAnalyticsUrl, '_blank');\r\n  }\r\n\r\n  public goToIclAnalytics() {\r\n    window.open('https://aava-trulens-dev.avateam.io/', '_blank');\r\n  }\r\n\r\n  public onRangeSelected(event: any) {\r\n    if (event && event.startDate && event.endDate) {\r\n      // Convert dates to the format expected by the component\r\n      const startDate = new Date(event.startDate);\r\n      const endDate = new Date(event.endDate);\r\n\r\n      this.dateRange.fromDate = startDate.toISOString().split('T')[0];\r\n      this.dateRange.toDate = endDate.toISOString().split('T')[0];\r\n\r\n      // Update form controls\r\n      this.vmFG.patchValue({\r\n        fromDate: this.dateRange.fromDate,\r\n        toDate: this.dateRange.toDate\r\n      });\r\n\r\n      // Don't auto-apply filter - let user click the filter button\r\n      console.log('Date range selected:', this.dateRange);\r\n    }\r\n  }\r\n\r\n  getFormattedDateRange(): string {\r\n    if (!this.dateRange.fromDate || !this.dateRange.toDate) {\r\n      return 'No dates selected';\r\n    }\r\n\r\n    const fromDate = new Date(this.dateRange.fromDate);\r\n    const toDate = new Date(this.dateRange.toDate);\r\n\r\n    const options: Intl.DateTimeFormatOptions = {\r\n      year: 'numeric',\r\n      month: 'short',\r\n      day: 'numeric'\r\n    };\r\n\r\n    return `${fromDate.toLocaleDateString('en-US', options)} to ${toDate.toLocaleDateString('en-US', options)}`;\r\n  }\r\n\r\n  getCalendarDateRange(): { start: Date | null; end: Date | null } {\r\n    if (!this.dateRange.fromDate || !this.dateRange.toDate) {\r\n      return { start: null, end: null };\r\n    }\r\n\r\n    return {\r\n      start: new Date(this.dateRange.fromDate),\r\n      end: new Date(this.dateRange.toDate)\r\n    };\r\n  }\r\n\r\n  @HostListener('document:click', ['$event'])\r\n  onDocumentClick(event: Event) {\r\n    const target = event.target as HTMLElement;\r\n    const downloadButton = target.closest('.download-button');\r\n    const downloadDropdown = target.closest('.download-dropdown');\r\n\r\n    if (!downloadButton && !downloadDropdown) {\r\n      this.showDownloadOptions = false;\r\n    }\r\n  }\r\n\r\n  public onDownloadToggle(event: any) {\r\n    event.stopPropagation();\r\n    this.showDownloadOptions = !this.showDownloadOptions;\r\n    console.log('Download toggle clicked, showDownloadOptions:', this.showDownloadOptions);\r\n  }\r\n\r\n  public onPdfDownload() {\r\n    this.downloadChartsAsPDF();\r\n    this.showDownloadOptions = false;\r\n  }\r\n\r\n  public onDataDownload() {\r\n    if (this.activeTab === 'usecase') {\r\n      this.downloadDump();\r\n    } else {\r\n      this.downloadAgentExcel('agentAnalytics');\r\n    }\r\n    this.showDownloadOptions = false;\r\n  }\r\n\r\n}\r\n", "<div class=\"analytics-container\">\r\n    <div class=\"analytics-header\">\r\n        <h3>Agent Analytics</h3>\r\n    </div>\r\n\r\n    <!-- Tab Navigation with Pill Buttons -->\r\n    <div class=\"analytics-tabs\">\r\n        <ava-button label=\"Individual\" [variant]=\"activeTab === 'usecase' ? 'primary' : 'secondary'\" [pill]=\"true\"\r\n            size=\"large\" (userClick)=\"onTabChange({id: 'usecase', label: 'Individual'})\">\r\n        </ava-button>\r\n        <ava-button label=\"Collaborative\" [variant]=\"activeTab === 'agents' ? 'primary' : 'secondary'\" [pill]=\"true\"\r\n            size=\"large\" (userClick)=\"onTabChange({id: 'agents', label: 'Collaborative'})\">\r\n        </ava-button>\r\n    </div>\r\n\r\n\r\n\r\n    <div class=\"analytics-content\">\r\n\r\n\r\n\r\n        <!-- Use Cases Tab Content -->\r\n        <div *ngIf=\"activeTab === 'usecase'\" class=\"tab-content\">\r\n\r\n            <!-- Inline Date Filter -->\r\n            <div class=\"analytics-controls\" [formGroup]=\"vmFG\">\r\n                <div class=\"controls-row\">\r\n                    <!-- Left Side - Action Buttons -->\r\n                    <div class=\"left-buttons\">\r\n                        <ava-button label=\"Langfuse\" variant=\"secondary\" size=\"medium\" (userClick)=\"goToLangfuse()\">\r\n                        </ava-button>\r\n                        <ava-button label=\"ICL Analytics\" variant=\"secondary\" size=\"medium\"\r\n                            (userClick)=\"goToIclAnalytics()\">\r\n                        </ava-button>\r\n\r\n                        <!-- Download Button -->\r\n                        <ava-button label=\"Download\" iconPosition=\"left\" size=\"medium\" iconName=\"Download\"\r\n                            [iconSize]=\"20\" variant=\"secondary\" class=\"download-button\"\r\n                            (userClick)=\"onDownloadToggle($event)\"\r\n                            (click)=\"onDownloadToggle($event)\">\r\n                        </ava-button>\r\n                        <div *ngIf=\"showDownloadOptions\" class=\"download-dropdown\">\r\n                            <button class=\"dropdown-item\" (click)=\"onPdfDownload()\">\r\n                                <ava-icon iconName=\"FileText\"></ava-icon>\r\n                                <span>Export PDF</span>\r\n                            </button>\r\n                            <button class=\"dropdown-item\" (click)=\"onDataDownload()\">\r\n                                <ava-icon iconName=\"Database\"></ava-icon>\r\n                                <span>Data Dump</span>\r\n                            </button>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <!-- Right Side - Date Range Selector and Filter Button -->\r\n                    <div class=\"right-calendar\">\r\n                        <ava-calendar\r\n                            [isRange]=\"true\"\r\n                            [dateRange]=\"getCalendarDateRange()\"\r\n                            (rangeSelected)=\"onRangeSelected($event)\">\r\n                        </ava-calendar>\r\n                        <ava-button variant=\"secondary\" iconName=\"Funnel\" iconPosition=\"only\" size=\"medium\"\r\n                            class=\"filter-button\" (userClick)=\"applyFilter()\" title=\"Apply Filter\">\r\n                        </ava-button>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- Use Case Metrics -->\r\n            <div class=\"metrics-container\">\r\n                <div class=\"metrics-row\">\r\n                    <ava-text-card [type]=\"'default'\" [iconName]=\"'bug'\" [title]=\"'Bugs Found'\"\r\n                        [value]=\"useCaseMetrics?.totalBugs || 0\" [description]=\"'Total bugs identified'\">\r\n                    </ava-text-card>\r\n\r\n                    <ava-text-card [type]=\"'default'\" [iconName]=\"'shield-check'\" [title]=\"'Unit Tests'\"\r\n                        [value]=\"useCaseMetrics?.totalUnitTest || 0\" [description]=\"'Unit tests created'\">\r\n                    </ava-text-card>\r\n\r\n                    <ava-text-card [type]=\"'default'\" [iconName]=\"'library-big'\" [title]=\"'Stories/Epics'\"\r\n                        [value]=\"useCaseMetrics?.totalStory || 0\" [description]=\"'Stories and epics managed'\">\r\n                    </ava-text-card>\r\n\r\n                    <ava-text-card [type]=\"'default'\" [iconName]=\"'code'\" [title]=\"'Code Optimizations'\"\r\n                        [value]=\"useCaseMetrics?.totalCodeOptimization || 0\"\r\n                        [description]=\"'Code optimizations performed'\">\r\n                    </ava-text-card>\r\n\r\n                    <ava-text-card [type]=\"'default'\" [iconName]=\"'users'\" [title]=\"'Active Users'\"\r\n                        [value]=\"userActivity?.activeUsers || 0\" [description]=\"'Currently active users'\">\r\n                    </ava-text-card>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- Charts Section - Matching Digital Ascender Layout -->\r\n            <div class=\"charts-grid\">\r\n                <!-- Row 1: User Consumption and Lines of Code -->\r\n                <div class=\"chart-container\">\r\n                    <div class=\"chart-header\">\r\n                        <h3>User Consumption</h3>\r\n                        <ava-icon *ngIf=\"!noDataAvailable\" iconName=\"Download\" iconSize=\"20\"\r\n                            class=\"chart-download-icon\" (click)=\"downloadExcel('userConsumption')\"></ava-icon>\r\n                    </div>\r\n                    <div class=\"chart-content1\">\r\n                        <div *ngIf=\"usageLoader\" class=\"chart-loading\">\r\n                            <div class=\"loading-spinner\"></div>\r\n                            <p>Loading chart...</p>\r\n                        </div>\r\n                        <div *ngIf=\"!usageLoader && noDataAvailable\" class=\"no-data-message\">\r\n                            <div class=\"error-status\">\r\n                                <i class=\"fas fa-exclamation-triangle no-data-icon\"></i>\r\n                                <div class=\"no-data\">No data available</div>\r\n                            </div>\r\n                        </div>\r\n                        <div *ngIf=\"!usageLoader && !noDataAvailable\" class=\"bars-container\">\r\n                            <div *ngFor=\"let user of sortedAnalytics\" class=\"bar-container\">\r\n                                <div class=\"bar-wrapper\">\r\n                                    <div class=\"bar\" [style.width.%]=\"getBarWidth(user.requestCount)\">\r\n                                        <span class=\"bar-label\">{{ user.userSignature }}</span>\r\n                                    </div>\r\n                                    <div *ngIf=\"!user.requestCount\" class=\"no-data-bar\">No Data Available</div>\r\n                                </div>\r\n                                <span class=\"bar-value\">{{ user.requestCount }}</span>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"chart-container\">\r\n                    <div class=\"chart-header\">\r\n                        <h3>Lines of code Processed</h3>\r\n                        <ava-icon iconName=\"Download\" iconSize=\"20\" class=\"chart-download-icon\"\r\n                            (click)=\"downloadExcel('linesOfCodeProcessed')\"></ava-icon>\r\n                    </div>\r\n                    <div *ngIf=\"chartLoadingStates['linesOfCodeProcessed']\" class=\"chart-loading\">\r\n                        <div class=\"loading-spinner\"></div>\r\n                        <p>Loading chart...</p>\r\n                    </div>\r\n                    <highcharts-chart\r\n                        *ngIf=\"chartOptions['linesOfCodeProcessed'] && !chartLoadingStates['linesOfCodeProcessed']\"\r\n                        [Highcharts]=\"Highcharts\" [options]=\"chartOptions['linesOfCodeProcessed']\"\r\n                        style=\"width: 100%; height: 300px;\">\r\n                    </highcharts-chart>\r\n                </div>\r\n\r\n                <!-- Row 2: Top 5 Individual Agents and Number of Requests -->\r\n                <div class=\"chart-container\">\r\n                    <div class=\"chart-header\">\r\n                        <h3>Top 5 Individual Agents</h3>\r\n                        <ava-icon iconName=\"Download\" iconSize=\"20\" class=\"chart-download-icon\"\r\n                            (click)=\"downloadExcel('topUseCases')\"></ava-icon>\r\n                    </div>\r\n                    <div *ngIf=\"chartLoadingStates['topUseCases']\" class=\"chart-loading\">\r\n                        <div class=\"loading-spinner\"></div>\r\n                        <p>Loading chart...</p>\r\n                    </div>\r\n                    <highcharts-chart *ngIf=\"chartOptions['topUseCases'] && !chartLoadingStates['topUseCases']\"\r\n                        [Highcharts]=\"Highcharts\" [options]=\"chartOptions['topUseCases']\"\r\n                        style=\"width: 100%; height: 300px;\">\r\n                    </highcharts-chart>\r\n                </div>\r\n\r\n                <div class=\"chart-container\">\r\n                    <div class=\"chart-header\">\r\n                        <h3>Number of Requests</h3>\r\n                        <ava-icon iconName=\"Download\" iconSize=\"20\" class=\"chart-download-icon\"\r\n                            (click)=\"downloadExcel('numberOfRequests')\"></ava-icon>\r\n                    </div>\r\n                    <div *ngIf=\"chartLoadingStates['numberOfRequests']\" class=\"chart-loading\">\r\n                        <div class=\"loading-spinner\"></div>\r\n                        <p>Loading chart...</p>\r\n                    </div>\r\n                    <highcharts-chart\r\n                        *ngIf=\"chartOptions['numberOfRequests'] && !chartLoadingStates['numberOfRequests']\"\r\n                        [Highcharts]=\"Highcharts\" [options]=\"chartOptions['numberOfRequests']\"\r\n                        style=\"width: 100%; height: 300px;\">\r\n                    </highcharts-chart>\r\n                </div>\r\n\r\n                <!-- Row 3: Top 5 Languages/Frameworks and User Response -->\r\n                <div class=\"chart-container\">\r\n                    <div class=\"chart-header\">\r\n                        <h3>Top 5 Languages / Frameworks</h3>\r\n                        <ava-icon iconName=\"Download\" iconSize=\"20\" class=\"chart-download-icon\"\r\n                            (click)=\"downloadExcel('topLanguages')\"></ava-icon>\r\n                    </div>\r\n                    <div *ngIf=\"chartLoadingStates['topLanguages']\" class=\"chart-loading\">\r\n                        <div class=\"loading-spinner\"></div>\r\n                        <p>Loading chart...</p>\r\n                    </div>\r\n                    <highcharts-chart *ngIf=\"chartOptions['topLanguages'] && !chartLoadingStates['topLanguages']\"\r\n                        [Highcharts]=\"Highcharts\" [options]=\"chartOptions['topLanguages']\"\r\n                        style=\"width: 100%; height: 300px;\">\r\n                    </highcharts-chart>\r\n                </div>\r\n\r\n                <div class=\"chart-container\">\r\n                    <div class=\"chart-header\">\r\n                        <h3>User Response</h3>\r\n                        <ava-icon iconName=\"Download\" iconSize=\"20\" class=\"chart-download-icon\"\r\n                            (click)=\"downloadExcel('userResponse')\"></ava-icon>\r\n                    </div>\r\n                    <div *ngIf=\"chartLoadingStates['userResponse']\" class=\"chart-loading\">\r\n                        <div class=\"loading-spinner\"></div>\r\n                        <p>Loading chart...</p>\r\n                    </div>\r\n                    <highcharts-chart *ngIf=\"chartOptions['userResponse'] && !chartLoadingStates['userResponse']\"\r\n                        [Highcharts]=\"Highcharts\" [options]=\"chartOptions['userResponse']\"\r\n                        style=\"width: 100%; height: 300px;\">\r\n                    </highcharts-chart>\r\n                </div>\r\n\r\n                <!-- Row 4: % Dormant User and Response Time -->\r\n                <div class=\"chart-container\">\r\n                    <div class=\"chart-header\">\r\n                        <h3>% Dormant User</h3>\r\n                        <ava-icon iconName=\"Download\" iconSize=\"20\" class=\"chart-download-icon\"\r\n                            (click)=\"downloadExcel('dormantUsers')\"></ava-icon>\r\n                    </div>\r\n                    <div *ngIf=\"chartLoadingStates['userActivity']\" class=\"chart-loading\">\r\n                        <div class=\"loading-spinner\"></div>\r\n                        <p>Loading chart...</p>\r\n                    </div>\r\n                    <highcharts-chart *ngIf=\"chartOptions['userActivity'] && !chartLoadingStates['userActivity']\"\r\n                        [Highcharts]=\"Highcharts\" [options]=\"chartOptions['userActivity']\"\r\n                        style=\"width: 100%; height: 300px;\">\r\n                    </highcharts-chart>\r\n                </div>\r\n\r\n                <div class=\"chart-container\">\r\n                    <div class=\"chart-header\">\r\n                        <h3>Response Time</h3>\r\n                        <ava-icon iconName=\"Download\" iconSize=\"20\" class=\"chart-download-icon\"\r\n                            (click)=\"downloadExcel('responseTime')\"></ava-icon>\r\n                    </div>\r\n                    <div *ngIf=\"chartLoadingStates['responseTime']\" class=\"chart-loading\">\r\n                        <div class=\"loading-spinner\"></div>\r\n                        <p>Loading chart...</p>\r\n                    </div>\r\n                    <highcharts-chart *ngIf=\"chartOptions['responseTime'] && !chartLoadingStates['responseTime']\"\r\n                        [Highcharts]=\"Highcharts\" [options]=\"chartOptions['responseTime']\"\r\n                        style=\"width: 100%; height: 300px;\">\r\n                    </highcharts-chart>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- No Data Message -->\r\n            <div *ngIf=\"noDataAvailable && !usageLoader\" class=\"no-data-message\">\r\n                <i class=\"fas fa-chart-line\"></i>\r\n                <h3>No User Consumption Data Available</h3>\r\n                <p>No user consumption data found for the selected date range.</p>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- Agents Tab Content -->\r\n        <div *ngIf=\"activeTab === 'agents'\" class=\"tab-content\">\r\n\r\n            <!-- Inline Date Filter -->\r\n            <div class=\"analytics-controls\" [formGroup]=\"vmFG\">\r\n                <div class=\"controls-row\">\r\n                    <!-- Left Side - Action Buttons -->\r\n                    <div class=\"left-buttons\">\r\n                        <ava-button label=\"Langfuse\" variant=\"secondary\" size=\"medium\" (userClick)=\"goToLangfuse()\">\r\n                        </ava-button>\r\n                        <ava-button label=\"ICL Analytics\" variant=\"secondary\" size=\"medium\"\r\n                            (userClick)=\"goToIclAnalytics()\">\r\n                        </ava-button>\r\n\r\n                        <!-- Download Button -->\r\n                        <ava-button label=\"Download\" iconPosition=\"left\" size=\"medium\" iconName=\"Download\"\r\n                            [iconSize]=\"20\" variant=\"secondary\" class=\"download-button\"\r\n                            (userClick)=\"onDownloadToggle($event)\"\r\n                            (click)=\"onDownloadToggle($event)\">\r\n                        </ava-button>\r\n                        <div *ngIf=\"showDownloadOptions\" class=\"download-dropdown\">\r\n                            <button class=\"dropdown-item\" (click)=\"onPdfDownload()\">\r\n                                <ava-icon iconName=\"FileText\"></ava-icon>\r\n                                <span>Export PDF</span>\r\n                            </button>\r\n                            <button class=\"dropdown-item\" (click)=\"onDataDownload()\">\r\n                                <ava-icon iconName=\"Database\"></ava-icon>\r\n                                <span>Data Dump</span>\r\n                            </button>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <!-- Right Side - Date Range Selector and Filter Button -->\r\n                    <div class=\"right-calendar\">\r\n                        <ava-calendar\r\n                            [isRange]=\"true\"\r\n                            [dateRange]=\"getCalendarDateRange()\"\r\n                            (rangeSelected)=\"onRangeSelected($event)\">\r\n                        </ava-calendar>\r\n                        <ava-button variant=\"secondary\" iconName=\"Funnel\" iconPosition=\"only\" size=\"medium\"\r\n                            class=\"filter-button\" (userClick)=\"applyFilter()\" title=\"Apply Filter\">\r\n                        </ava-button>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- Collaborative Metrics -->\r\n            <div class=\"metrics-container\">\r\n                <div class=\"metrics-row\">\r\n                    <ava-text-card [type]=\"'default'\" [iconName]=\"'bot'\" [title]=\"'Collaborative Agents Created'\"\r\n                        [value]=\"agentMetrics?.totalAgentsCreated || 0\" [description]=\"'Collaborative agents created'\">\r\n                    </ava-text-card>\r\n\r\n                    <ava-text-card [type]=\"'default'\" [iconName]=\"'rotate-ccw'\" [title]=\"'Collaborative Agents Reused'\"\r\n                        [value]=\"agentMetrics?.totalAgentsReused || 0\" [description]=\"'Collaborative agents reused'\">\r\n                    </ava-text-card>\r\n\r\n                    <ava-text-card [type]=\"'default'\" [iconName]=\"'wrench'\" [title]=\"'Total Tools'\"\r\n                        [value]=\"agentMetrics?.totalTools || 0\" [description]=\"'Tools available'\">\r\n                    </ava-text-card>\r\n\r\n                    <ava-text-card [type]=\"'default'\" [iconName]=\"'workflow'\" [title]=\"'Workflow Execution Count'\"\r\n                        [value]=\"agentMetrics?.workflowExecutionCount || 0\" [description]=\"'Workflow executions'\">\r\n                    </ava-text-card>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- Charts Section - Matching Individual Analytics Layout -->\r\n            <div class=\"charts-grid\">\r\n            <!-- Row 1: Studio Usage and Top 5 Collaborative Agents -->\r\n            <div class=\"chart-container\">\r\n                <div class=\"chart-header\">\r\n                    <h3>Studio Usage</h3>\r\n                    <ava-icon iconName=\"Download\" iconSize=\"20\" class=\"chart-download-icon\"\r\n                        (click)=\"downloadAgentExcel('studioUsage')\"></ava-icon>\r\n                </div>\r\n                <div *ngIf=\"chartLoadingStates['studioUsage']\" class=\"chart-loading\">\r\n                    <div class=\"loading-spinner\"></div>\r\n                    <p>Loading chart...</p>\r\n                </div>\r\n                <highcharts-chart *ngIf=\"chartOptions['studioUsage'] && !chartLoadingStates['studioUsage']\"\r\n                    [Highcharts]=\"Highcharts\" [options]=\"chartOptions['studioUsage']\"\r\n                    style=\"width: 100%; height: 300px;\">\r\n                </highcharts-chart>\r\n            </div>\r\n\r\n            <div class=\"chart-container\">\r\n                <div class=\"chart-header\">\r\n                    <h3>Top 5 Collaborative Agents</h3>\r\n                    <ava-icon iconName=\"Download\" iconSize=\"20\" class=\"chart-download-icon\"\r\n                        (click)=\"downloadAgentExcel('topAgents')\"></ava-icon>\r\n                </div>\r\n                <div *ngIf=\"chartLoadingStates['topAgents']\" class=\"chart-loading\">\r\n                    <div class=\"loading-spinner\"></div>\r\n                    <p>Loading chart...</p>\r\n                </div>\r\n                <highcharts-chart *ngIf=\"chartOptions['topAgents'] && !chartLoadingStates['topAgents']\"\r\n                    [Highcharts]=\"Highcharts\" [options]=\"chartOptions['topAgents']\" style=\"width: 100%; height: 300px;\">\r\n                </highcharts-chart>\r\n            </div>\r\n\r\n            <!-- Row 2: Collaborative Agent Created and Collaborative Agent Metrics -->\r\n            <div class=\"chart-container\">\r\n                <div class=\"chart-header\">\r\n                    <h3>Collaborative Agent Created</h3>\r\n                    <ava-icon iconName=\"Download\" iconSize=\"20\" class=\"chart-download-icon\"\r\n                        (click)=\"downloadAgentExcel('agentCreated')\"></ava-icon>\r\n                </div>\r\n                <div *ngIf=\"chartLoadingStates['agentCreated']\" class=\"chart-loading\">\r\n                    <div class=\"loading-spinner\"></div>\r\n                    <p>Loading chart...</p>\r\n                </div>\r\n                <highcharts-chart *ngIf=\"chartOptions['agentCreated'] && !chartLoadingStates['agentCreated']\"\r\n                    [Highcharts]=\"Highcharts\" [options]=\"chartOptions['agentCreated']\"\r\n                    style=\"width: 100%; height: 300px;\">\r\n                </highcharts-chart>\r\n            </div>\r\n\r\n            <div class=\"chart-container\">\r\n                <div class=\"chart-header\">\r\n                    <h3>Collaborative Agent Metrics</h3>\r\n                    <ava-icon iconName=\"Download\" iconSize=\"20\" class=\"chart-download-icon\"\r\n                        (click)=\"downloadAgentExcel('agentMetrics')\"></ava-icon>\r\n                </div>\r\n\r\n                <!-- Loading State -->\r\n                <div *ngIf=\"agentMetricsLoader\" class=\"chart-loading\">\r\n                    <div class=\"loading-spinner\"></div>\r\n                    <p>Loading agent metrics...</p>\r\n                </div>\r\n\r\n                <!-- No Data State -->\r\n                <div *ngIf=\"agentMetricsNoDataAvailable && !agentMetricsLoader\" class=\"no-data-message\">\r\n                    <div class=\"error-status\">\r\n                        <i class=\"fas fa-chart-line\"></i>\r\n                        <div class=\"no-data\">No agent metrics data available</div>\r\n                    </div>\r\n                </div>\r\n\r\n                <!-- Simple Table -->\r\n                <div *ngIf=\"!agentMetricsNoDataAvailable && !agentMetricsLoader\" class=\"agent-metrics-table-container\">\r\n                    <div class=\"table-wrapper\">\r\n                        <table class=\"agent-metrics-table\">\r\n                            <thead>\r\n                                <tr>\r\n                                    <th class=\"expand-column\"></th>\r\n                                    <th>Agent Name</th>\r\n                                    <th>No. of time used</th>\r\n                                </tr>\r\n                            </thead>\r\n                            <tbody>\r\n                                <ng-container *ngFor=\"let agent of agentMetricsTableData; let i = index\">\r\n                                    <tr class=\"agent-row\">\r\n                                        <td class=\"expand-column\">\r\n                                            <button *ngIf=\"agent.workflows && agent.workflows.length > 0\"\r\n                                                class=\"expand-btn\" (click)=\"toggleRowExpansion(i)\"\r\n                                                [class.expanded]=\"isRowExpanded(i)\"\r\n                                                [title]=\"isRowExpanded(i) ? 'Collapse workflows' : 'Expand workflows'\">\r\n                                                <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\"\r\n                                                    xmlns=\"http://www.w3.org/2000/svg\">\r\n                                                    <path *ngIf=\"!isRowExpanded(i)\" d=\"M9 18L15 12L9 6\"\r\n                                                        stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\"\r\n                                                        stroke-linejoin=\"round\" />\r\n                                                    <path *ngIf=\"isRowExpanded(i)\" d=\"M6 9L12 15L18 9\"\r\n                                                        stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\"\r\n                                                        stroke-linejoin=\"round\" />\r\n                                                </svg>\r\n                                            </button>\r\n                                        </td>\r\n                                        <td class=\"agent-name\">{{ agent.agentName }}</td>\r\n                                        <td class=\"workflow-count\">{{ agent.workflowCount }}</td>\r\n                                    </tr>\r\n                                    <!-- Expanded workflow details -->\r\n                                    <tr *ngIf=\"isRowExpanded(i)\" class=\"workflow-details-row\">\r\n                                        <td colspan=\"3\">\r\n                                            <div class=\"workflow-details\">\r\n                                                <div class=\"workflow-header\">Workflows:</div>\r\n                                                <div class=\"workflow-list\">\r\n                                                    <div *ngFor=\"let workflow of agent.workflows\" class=\"workflow-item\">\r\n                                                        {{ workflow }}\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                        </td>\r\n                                    </tr>\r\n                                </ng-container>\r\n                            </tbody>\r\n                        </table>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- Row 3: Tool Analytics and Tool Usage -->\r\n            <div class=\"chart-container\">\r\n                <div class=\"chart-header\">\r\n                    <h3>Tool Analytics</h3>\r\n                    <ava-icon iconName=\"Download\" iconSize=\"20\" class=\"chart-download-icon\"\r\n                        (click)=\"downloadAgentExcel('toolAnalytics')\"></ava-icon>\r\n                </div>\r\n                <div *ngIf=\"chartLoadingStates['toolAnalytics']\" class=\"chart-loading\">\r\n                    <div class=\"loading-spinner\"></div>\r\n                    <p>Loading chart...</p>\r\n                </div>\r\n                <highcharts-chart *ngIf=\"chartOptions['toolAnalytics'] && !chartLoadingStates['toolAnalytics']\"\r\n                    [Highcharts]=\"Highcharts\" [options]=\"chartOptions['toolAnalytics']\"\r\n                    style=\"width: 100%; height: 300px;\">\r\n                </highcharts-chart>\r\n            </div>\r\n\r\n            <div class=\"tool-usage\">\r\n                <div class=\"tool-usage-container\">\r\n                    <div *ngIf=\"chartLoadingStates['toolUsage']\" class=\"tool-loader\">\r\n                        <div class=\"loading-spinner\"></div>\r\n                        <p>Loading chart...</p>\r\n                    </div>\r\n                    <ng-container *ngIf=\"!chartLoadingStates['toolUsage']\">\r\n                        <div class=\"tool-usage-header\">\r\n                            Tool Usage\r\n                            <ava-icon *ngIf=\"toolUsage && toolUsage.length > 0\" iconName=\"Download\" iconSize=\"20\"\r\n                                class=\"chart-download-icon\" (click)=\"downloadAgentExcel('toolUsage')\"></ava-icon>\r\n                        </div>\r\n                        <div class=\"no-data-message\" *ngIf=\"!toolUsage || toolUsage.length === 0\">\r\n                            <div class=\"error-status\">\r\n                                <i class=\"fas fa-exclamation-triangle no-data-icon\"></i>\r\n                                <div class=\"no-data\">No data available</div>\r\n                            </div>\r\n                        </div>\r\n                        <div *ngIf=\"toolUsage && toolUsage.length > 0\" class=\"bars-container\">\r\n                            <div *ngFor=\"let tool of toolUsage.slice(0, 5)\" class=\"bar-container\">\r\n                                <div class=\"bar-wrapper\">\r\n                                    <div class=\"bar tool-bar\" [style.width.%]=\"getBarWidth(tool.usageCount, true)\">\r\n                                        <span class=\"bar-label\">{{ tool.toolName }}</span>\r\n                                    </div>\r\n                                    <div *ngIf=\"!tool.usageCount\" class=\"no-data-bar\">No Data Available</div>\r\n                                </div>\r\n                                <span class=\"bar-value\">{{ tool.usageCount }}</span>\r\n                            </div>\r\n                        </div>\r\n                    </ng-container>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- Row 4: Adoption Rate and User Consumption -->\r\n            <div class=\"chart-container\">\r\n                <div class=\"chart-header\">\r\n                    <h3>Adoption Rate</h3>\r\n                    <ava-icon iconName=\"Download\" iconSize=\"20\" class=\"chart-download-icon\"\r\n                        (click)=\"downloadAgentExcel('adoptionRate')\"></ava-icon>\r\n                </div>\r\n                <div *ngIf=\"chartLoadingStates['adoptionRate']\" class=\"chart-loading\">\r\n                    <div class=\"loading-spinner\"></div>\r\n                    <p>Loading chart...</p>\r\n                </div>\r\n                <highcharts-chart *ngIf=\"chartOptions['adoptionRate'] && !chartLoadingStates['adoptionRate']\"\r\n                    [Highcharts]=\"Highcharts\" [options]=\"chartOptions['adoptionRate']\"\r\n                    style=\"width: 100%; height: 300px;\">\r\n                </highcharts-chart>\r\n            </div>\r\n\r\n            <div class=\"chart-container\">\r\n                <div class=\"chart-header\">\r\n                    <h3>User Consumption</h3>\r\n                    <ava-icon *ngIf=\"userConsumption && userConsumption.length > 0\" iconName=\"Download\" iconSize=\"20\"\r\n                        class=\"chart-download-icon\" (click)=\"downloadAgentExcel('userConsumption')\"></ava-icon>\r\n                </div>\r\n                <div class=\"chart-content\">\r\n                    <div *ngIf=\"chartLoadingStates['userConsumption']\" class=\"chart-loading\">\r\n                        <div class=\"loading-spinner\"></div>\r\n                        <p>Loading chart...</p>\r\n                    </div>\r\n                    <div *ngIf=\"!chartLoadingStates['userConsumption'] && (!userConsumption || userConsumption.length === 0)\" class=\"no-data-message\">\r\n                        <div class=\"error-status\">\r\n                            <i class=\"fas fa-exclamation-triangle no-data-icon\"></i>\r\n                            <div class=\"no-data\">No data available</div>\r\n                        </div>\r\n                    </div>\r\n                    <div *ngIf=\"!chartLoadingStates['userConsumption'] && userConsumption && userConsumption.length > 0\" class=\"bars-container\">\r\n                        <div *ngFor=\"let user of userConsumption\" class=\"bar-container\">\r\n                            <div class=\"bar-wrapper\">\r\n                                <div class=\"bar\" [style.width.%]=\"getBarWidth(user.consumptionCount || user.requestCount)\">\r\n                                    <span class=\"bar-label\">{{ user.email || user.userSignature }}</span>\r\n                                </div>\r\n                                <div *ngIf=\"!(user.consumptionCount || user.requestCount)\" class=\"no-data-bar\">No Data Available</div>\r\n                            </div>\r\n                            <span class=\"bar-value\">{{ user.consumptionCount || user.requestCount }}</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- Row 5: % Dormant User -->\r\n            <div class=\"chart-container\">\r\n                <div class=\"chart-header\">\r\n                    <h3>% Dormant User</h3>\r\n                    <ava-icon iconName=\"Download\" iconSize=\"20\" class=\"chart-download-icon\"\r\n                        (click)=\"downloadAgentExcel('dormantUsers')\"></ava-icon>\r\n                </div>\r\n                <div *ngIf=\"chartLoadingStates['userActivity']\" class=\"chart-loading\">\r\n                    <div class=\"loading-spinner\"></div>\r\n                    <p>Loading chart...</p>\r\n                </div>\r\n                <highcharts-chart *ngIf=\"chartOptions['userActivity'] && !chartLoadingStates['userActivity']\"\r\n                    [Highcharts]=\"Highcharts\" [options]=\"chartOptions['userActivity']\"\r\n                    style=\"width: 100%; height: 300px;\">\r\n                </highcharts-chart>\r\n            </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAgC,gBAAgB;AACzF,SAASC,OAAO,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,MAAM;AACnD,OAAO,KAAKC,UAAU,MAAM,YAAY;AACxC,SAASC,qBAAqB,QAAQ,oBAAoB;AAC1D,OAAOC,eAAe,MAAM,uCAAuC;AACnE,OAAOC,OAAO,MAAM,4BAA4B;AAChD,OAAOC,YAAY,MAAM,8BAA8B;AACvD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAAkBC,eAAe,EAAEC,iBAAiB,EAAEC,iBAAiB,EAAEC,aAAa,QAAQ,wBAAwB;AAEtH,SAASC,WAAW,QAAQ,+CAA+C;;;;;;;;;IC2B/CC,EADJ,CAAAC,cAAA,cAA2D,iBACC;IAA1BD,EAAA,CAAAE,UAAA,mBAAAC,gEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,aAAA,EAAe;IAAA,EAAC;IACnDT,EAAA,CAAAU,SAAA,mBAAyC;IACzCV,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAW,MAAA,iBAAU;IACpBX,EADoB,CAAAY,YAAA,EAAO,EAClB;IACTZ,EAAA,CAAAC,cAAA,iBAAyD;IAA3BD,EAAA,CAAAE,UAAA,mBAAAW,gEAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAQ,cAAA,EAAgB;IAAA,EAAC;IACpDd,EAAA,CAAAU,SAAA,mBAAyC;IACzCV,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAW,MAAA,gBAAS;IAEvBX,EAFuB,CAAAY,YAAA,EAAO,EACjB,EACP;;;;;;IAiDNZ,EAAA,CAAAC,cAAA,mBAC2E;IAA3CD,EAAA,CAAAE,UAAA,mBAAAa,wEAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAY,GAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAW,aAAA,CAAc,iBAAiB,CAAC;IAAA,EAAC;IAACjB,EAAA,CAAAY,YAAA,EAAW;;;;;IAGtFZ,EAAA,CAAAC,cAAA,cAA+C;IAC3CD,EAAA,CAAAU,SAAA,cAAmC;IACnCV,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAW,MAAA,uBAAgB;IACvBX,EADuB,CAAAY,YAAA,EAAI,EACrB;;;;;IAEFZ,EADJ,CAAAC,cAAA,cAAqE,cACvC;IACtBD,EAAA,CAAAU,SAAA,YAAwD;IACxDV,EAAA,CAAAC,cAAA,cAAqB;IAAAD,EAAA,CAAAW,MAAA,wBAAiB;IAE9CX,EAF8C,CAAAY,YAAA,EAAM,EAC1C,EACJ;;;;;IAOMZ,EAAA,CAAAC,cAAA,cAAoD;IAAAD,EAAA,CAAAW,MAAA,wBAAiB;IAAAX,EAAA,CAAAY,YAAA,EAAM;;;;;IAFvEZ,EAHZ,CAAAC,cAAA,cAAgE,cACnC,cAC6C,eACtC;IAAAD,EAAA,CAAAW,MAAA,GAAwB;IACpDX,EADoD,CAAAY,YAAA,EAAO,EACrD;IACNZ,EAAA,CAAAkB,UAAA,IAAAC,oDAAA,kBAAoD;IACxDnB,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAW,MAAA,GAAuB;IACnDX,EADmD,CAAAY,YAAA,EAAO,EACpD;;;;;IANmBZ,EAAA,CAAAoB,SAAA,GAAgD;IAAhDpB,EAAA,CAAAqB,WAAA,UAAAf,MAAA,CAAAgB,WAAA,CAAAC,OAAA,CAAAC,YAAA,OAAgD;IACrCxB,EAAA,CAAAoB,SAAA,GAAwB;IAAxBpB,EAAA,CAAAyB,iBAAA,CAAAF,OAAA,CAAAG,aAAA,CAAwB;IAE9C1B,EAAA,CAAAoB,SAAA,EAAwB;IAAxBpB,EAAA,CAAA2B,UAAA,UAAAJ,OAAA,CAAAC,YAAA,CAAwB;IAEVxB,EAAA,CAAAoB,SAAA,GAAuB;IAAvBpB,EAAA,CAAAyB,iBAAA,CAAAF,OAAA,CAAAC,YAAA,CAAuB;;;;;IARvDxB,EAAA,CAAAC,cAAA,cAAqE;IACjED,EAAA,CAAAkB,UAAA,IAAAU,8CAAA,kBAAgE;IASpE5B,EAAA,CAAAY,YAAA,EAAM;;;;IAToBZ,EAAA,CAAAoB,SAAA,EAAkB;IAAlBpB,EAAA,CAAA2B,UAAA,YAAArB,MAAA,CAAAuB,eAAA,CAAkB;;;;;IAmBhD7B,EAAA,CAAAC,cAAA,cAA8E;IAC1ED,EAAA,CAAAU,SAAA,cAAmC;IACnCV,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAW,MAAA,uBAAgB;IACvBX,EADuB,CAAAY,YAAA,EAAI,EACrB;;;;;IACNZ,EAAA,CAAAU,SAAA,2BAImB;;;;IAFWV,EAA1B,CAAA2B,UAAA,eAAArB,MAAA,CAAApB,UAAA,CAAyB,YAAAoB,MAAA,CAAAwB,YAAA,yBAAiD;;;;;IAY9E9B,EAAA,CAAAC,cAAA,cAAqE;IACjED,EAAA,CAAAU,SAAA,cAAmC;IACnCV,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAW,MAAA,uBAAgB;IACvBX,EADuB,CAAAY,YAAA,EAAI,EACrB;;;;;IACNZ,EAAA,CAAAU,SAAA,2BAGmB;;;;IAFWV,EAA1B,CAAA2B,UAAA,eAAArB,MAAA,CAAApB,UAAA,CAAyB,YAAAoB,MAAA,CAAAwB,YAAA,gBAAwC;;;;;IAWrE9B,EAAA,CAAAC,cAAA,cAA0E;IACtED,EAAA,CAAAU,SAAA,cAAmC;IACnCV,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAW,MAAA,uBAAgB;IACvBX,EADuB,CAAAY,YAAA,EAAI,EACrB;;;;;IACNZ,EAAA,CAAAU,SAAA,2BAImB;;;;IAFWV,EAA1B,CAAA2B,UAAA,eAAArB,MAAA,CAAApB,UAAA,CAAyB,YAAAoB,MAAA,CAAAwB,YAAA,qBAA6C;;;;;IAY1E9B,EAAA,CAAAC,cAAA,cAAsE;IAClED,EAAA,CAAAU,SAAA,cAAmC;IACnCV,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAW,MAAA,uBAAgB;IACvBX,EADuB,CAAAY,YAAA,EAAI,EACrB;;;;;IACNZ,EAAA,CAAAU,SAAA,2BAGmB;;;;IAFWV,EAA1B,CAAA2B,UAAA,eAAArB,MAAA,CAAApB,UAAA,CAAyB,YAAAoB,MAAA,CAAAwB,YAAA,iBAAyC;;;;;IAWtE9B,EAAA,CAAAC,cAAA,cAAsE;IAClED,EAAA,CAAAU,SAAA,cAAmC;IACnCV,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAW,MAAA,uBAAgB;IACvBX,EADuB,CAAAY,YAAA,EAAI,EACrB;;;;;IACNZ,EAAA,CAAAU,SAAA,2BAGmB;;;;IAFWV,EAA1B,CAAA2B,UAAA,eAAArB,MAAA,CAAApB,UAAA,CAAyB,YAAAoB,MAAA,CAAAwB,YAAA,iBAAyC;;;;;IAYtE9B,EAAA,CAAAC,cAAA,cAAsE;IAClED,EAAA,CAAAU,SAAA,cAAmC;IACnCV,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAW,MAAA,uBAAgB;IACvBX,EADuB,CAAAY,YAAA,EAAI,EACrB;;;;;IACNZ,EAAA,CAAAU,SAAA,2BAGmB;;;;IAFWV,EAA1B,CAAA2B,UAAA,eAAArB,MAAA,CAAApB,UAAA,CAAyB,YAAAoB,MAAA,CAAAwB,YAAA,iBAAyC;;;;;IAWtE9B,EAAA,CAAAC,cAAA,cAAsE;IAClED,EAAA,CAAAU,SAAA,cAAmC;IACnCV,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAW,MAAA,uBAAgB;IACvBX,EADuB,CAAAY,YAAA,EAAI,EACrB;;;;;IACNZ,EAAA,CAAAU,SAAA,2BAGmB;;;;IAFWV,EAA1B,CAAA2B,UAAA,eAAArB,MAAA,CAAApB,UAAA,CAAyB,YAAAoB,MAAA,CAAAwB,YAAA,iBAAyC;;;;;IAO9E9B,EAAA,CAAAC,cAAA,cAAqE;IACjED,EAAA,CAAAU,SAAA,YAAiC;IACjCV,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAW,MAAA,yCAAkC;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAC3CZ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAW,MAAA,kEAA2D;IAClEX,EADkE,CAAAY,YAAA,EAAI,EAChE;;;;;;IA7NMZ,EAPhB,CAAAC,cAAA,aAAyD,aAGF,aACrB,cAEI,qBACsE;IAA7BD,EAAA,CAAAE,UAAA,uBAAA6B,kEAAA;MAAA/B,EAAA,CAAAI,aAAA,CAAA4B,GAAA;MAAA,MAAA1B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAaF,MAAA,CAAA2B,YAAA,EAAc;IAAA,EAAC;IAC3FjC,EAAA,CAAAY,YAAA,EAAa;IACbZ,EAAA,CAAAC,cAAA,qBACqC;IAAjCD,EAAA,CAAAE,UAAA,uBAAAgC,kEAAA;MAAAlC,EAAA,CAAAI,aAAA,CAAA4B,GAAA;MAAA,MAAA1B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAaF,MAAA,CAAA6B,gBAAA,EAAkB;IAAA,EAAC;IACpCnC,EAAA,CAAAY,YAAA,EAAa;IAGbZ,EAAA,CAAAC,cAAA,qBAGuC;IAAnCD,EADA,CAAAE,UAAA,uBAAAkC,kEAAAC,MAAA;MAAArC,EAAA,CAAAI,aAAA,CAAA4B,GAAA;MAAA,MAAA1B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAaF,MAAA,CAAAgC,gBAAA,CAAAD,MAAA,CAAwB;IAAA,EAAC,mBAAAE,8DAAAF,MAAA;MAAArC,EAAA,CAAAI,aAAA,CAAA4B,GAAA;MAAA,MAAA1B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAC7BF,MAAA,CAAAgC,gBAAA,CAAAD,MAAA,CAAwB;IAAA,EAAC;IACtCrC,EAAA,CAAAY,YAAA,EAAa;IACbZ,EAAA,CAAAkB,UAAA,IAAAsB,uCAAA,kBAA2D;IAU/DxC,EAAA,CAAAY,YAAA,EAAM;IAIFZ,EADJ,CAAAC,cAAA,cAA4B,uBAIsB;IAA1CD,EAAA,CAAAE,UAAA,2BAAAuC,wEAAAJ,MAAA;MAAArC,EAAA,CAAAI,aAAA,CAAA4B,GAAA;MAAA,MAAA1B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAiBF,MAAA,CAAAoC,eAAA,CAAAL,MAAA,CAAuB;IAAA,EAAC;IAC7CrC,EAAA,CAAAY,YAAA,EAAe;IACfZ,EAAA,CAAAC,cAAA,sBAC2E;IAAjDD,EAAA,CAAAE,UAAA,uBAAAyC,mEAAA;MAAA3C,EAAA,CAAAI,aAAA,CAAA4B,GAAA;MAAA,MAAA1B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAaF,MAAA,CAAAsC,WAAA,EAAa;IAAA,EAAC;IAIjE5C,EAHY,CAAAY,YAAA,EAAa,EACX,EACJ,EACJ;IAIFZ,EADJ,CAAAC,cAAA,eAA+B,eACF;IAkBrBD,EAjBA,CAAAU,SAAA,yBAEgB,yBAIA,yBAIA,yBAKA,yBAIA;IAExBV,EADI,CAAAY,YAAA,EAAM,EACJ;IAOMZ,EAJZ,CAAAC,cAAA,eAAyB,eAEQ,eACC,UAClB;IAAAD,EAAA,CAAAW,MAAA,wBAAgB;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACzBZ,EAAA,CAAAkB,UAAA,KAAA2B,6CAAA,uBAC2E;IAC/E7C,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAC,cAAA,eAA4B;IAWxBD,EAVA,CAAAkB,UAAA,KAAA4B,wCAAA,kBAA+C,KAAAC,wCAAA,kBAIsB,KAAAC,wCAAA,kBAMA;IAY7EhD,EADI,CAAAY,YAAA,EAAM,EACJ;IAIEZ,EAFR,CAAAC,cAAA,eAA6B,eACC,UAClB;IAAAD,EAAA,CAAAW,MAAA,+BAAuB;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAChCZ,EAAA,CAAAC,cAAA,oBACoD;IAAhDD,EAAA,CAAAE,UAAA,mBAAA+C,6DAAA;MAAAjD,EAAA,CAAAI,aAAA,CAAA4B,GAAA;MAAA,MAAA1B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAW,aAAA,CAAc,sBAAsB,CAAC;IAAA,EAAC;IACvDjB,EADwD,CAAAY,YAAA,EAAW,EAC7D;IAKNZ,EAJA,CAAAkB,UAAA,KAAAgC,wCAAA,kBAA8E,KAAAC,qDAAA,+BAOtC;IAE5CnD,EAAA,CAAAY,YAAA,EAAM;IAKEZ,EAFR,CAAAC,cAAA,eAA6B,eACC,UAClB;IAAAD,EAAA,CAAAW,MAAA,+BAAuB;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAChCZ,EAAA,CAAAC,cAAA,oBAC2C;IAAvCD,EAAA,CAAAE,UAAA,mBAAAkD,6DAAA;MAAApD,EAAA,CAAAI,aAAA,CAAA4B,GAAA;MAAA,MAAA1B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAW,aAAA,CAAc,aAAa,CAAC;IAAA,EAAC;IAC9CjB,EAD+C,CAAAY,YAAA,EAAW,EACpD;IAKNZ,EAJA,CAAAkB,UAAA,KAAAmC,wCAAA,kBAAqE,KAAAC,qDAAA,+BAM7B;IAE5CtD,EAAA,CAAAY,YAAA,EAAM;IAIEZ,EAFR,CAAAC,cAAA,eAA6B,eACC,UAClB;IAAAD,EAAA,CAAAW,MAAA,0BAAkB;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAC3BZ,EAAA,CAAAC,cAAA,oBACgD;IAA5CD,EAAA,CAAAE,UAAA,mBAAAqD,6DAAA;MAAAvD,EAAA,CAAAI,aAAA,CAAA4B,GAAA;MAAA,MAAA1B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAW,aAAA,CAAc,kBAAkB,CAAC;IAAA,EAAC;IACnDjB,EADoD,CAAAY,YAAA,EAAW,EACzD;IAKNZ,EAJA,CAAAkB,UAAA,KAAAsC,wCAAA,kBAA0E,KAAAC,qDAAA,+BAOlC;IAE5CzD,EAAA,CAAAY,YAAA,EAAM;IAKEZ,EAFR,CAAAC,cAAA,eAA6B,eACC,UAClB;IAAAD,EAAA,CAAAW,MAAA,oCAA4B;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACrCZ,EAAA,CAAAC,cAAA,oBAC4C;IAAxCD,EAAA,CAAAE,UAAA,mBAAAwD,6DAAA;MAAA1D,EAAA,CAAAI,aAAA,CAAA4B,GAAA;MAAA,MAAA1B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAW,aAAA,CAAc,cAAc,CAAC;IAAA,EAAC;IAC/CjB,EADgD,CAAAY,YAAA,EAAW,EACrD;IAKNZ,EAJA,CAAAkB,UAAA,KAAAyC,wCAAA,kBAAsE,KAAAC,qDAAA,+BAM9B;IAE5C5D,EAAA,CAAAY,YAAA,EAAM;IAIEZ,EAFR,CAAAC,cAAA,eAA6B,eACC,UAClB;IAAAD,EAAA,CAAAW,MAAA,qBAAa;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACtBZ,EAAA,CAAAC,cAAA,oBAC4C;IAAxCD,EAAA,CAAAE,UAAA,mBAAA2D,6DAAA;MAAA7D,EAAA,CAAAI,aAAA,CAAA4B,GAAA;MAAA,MAAA1B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAW,aAAA,CAAc,cAAc,CAAC;IAAA,EAAC;IAC/CjB,EADgD,CAAAY,YAAA,EAAW,EACrD;IAKNZ,EAJA,CAAAkB,UAAA,KAAA4C,wCAAA,kBAAsE,KAAAC,qDAAA,+BAM9B;IAE5C/D,EAAA,CAAAY,YAAA,EAAM;IAKEZ,EAFR,CAAAC,cAAA,eAA6B,eACC,UAClB;IAAAD,EAAA,CAAAW,MAAA,sBAAc;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACvBZ,EAAA,CAAAC,cAAA,oBAC4C;IAAxCD,EAAA,CAAAE,UAAA,mBAAA8D,6DAAA;MAAAhE,EAAA,CAAAI,aAAA,CAAA4B,GAAA;MAAA,MAAA1B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAW,aAAA,CAAc,cAAc,CAAC;IAAA,EAAC;IAC/CjB,EADgD,CAAAY,YAAA,EAAW,EACrD;IAKNZ,EAJA,CAAAkB,UAAA,KAAA+C,wCAAA,kBAAsE,KAAAC,qDAAA,+BAM9B;IAE5ClE,EAAA,CAAAY,YAAA,EAAM;IAIEZ,EAFR,CAAAC,cAAA,eAA6B,eACC,UAClB;IAAAD,EAAA,CAAAW,MAAA,qBAAa;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACtBZ,EAAA,CAAAC,cAAA,oBAC4C;IAAxCD,EAAA,CAAAE,UAAA,mBAAAiE,6DAAA;MAAAnE,EAAA,CAAAI,aAAA,CAAA4B,GAAA;MAAA,MAAA1B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAW,aAAA,CAAc,cAAc,CAAC;IAAA,EAAC;IAC/CjB,EADgD,CAAAY,YAAA,EAAW,EACrD;IAKNZ,EAJA,CAAAkB,UAAA,KAAAkD,wCAAA,kBAAsE,KAAAC,qDAAA,+BAM9B;IAGhDrE,EADI,CAAAY,YAAA,EAAM,EACJ;IAGNZ,EAAA,CAAAkB,UAAA,KAAAoD,wCAAA,kBAAqE;IAKzEtE,EAAA,CAAAY,YAAA,EAAM;;;;IAlO8BZ,EAAA,CAAAoB,SAAA,EAAkB;IAAlBpB,EAAA,CAAA2B,UAAA,cAAArB,MAAA,CAAAiE,IAAA,CAAkB;IAYlCvE,EAAA,CAAAoB,SAAA,GAAe;IAAfpB,EAAA,CAAA2B,UAAA,gBAAe;IAIb3B,EAAA,CAAAoB,SAAA,EAAyB;IAAzBpB,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAAkE,mBAAA,CAAyB;IAe3BxE,EAAA,CAAAoB,SAAA,GAAgB;IAChBpB,EADA,CAAA2B,UAAA,iBAAgB,cAAArB,MAAA,CAAAmE,oBAAA,GACoB;IAa7BzE,EAAA,CAAAoB,SAAA,GAAkB;IACYpB,EAD9B,CAAA2B,UAAA,mBAAkB,mBAAmB,uBAAuB,WAAArB,MAAA,CAAAoE,cAAA,kBAAApE,MAAA,CAAAoE,cAAA,CAAAC,SAAA,OAC/B,wCAAwC;IAGrE3E,EAAA,CAAAoB,SAAA,EAAkB;IACgBpB,EADlC,CAAA2B,UAAA,mBAAkB,4BAA4B,uBAAuB,WAAArB,MAAA,CAAAoE,cAAA,kBAAApE,MAAA,CAAAoE,cAAA,CAAAE,aAAA,OACpC,qCAAqC;IAGtE5E,EAAA,CAAAoB,SAAA,EAAkB;IACapB,EAD/B,CAAA2B,UAAA,mBAAkB,2BAA2B,0BAA0B,WAAArB,MAAA,CAAAoE,cAAA,kBAAApE,MAAA,CAAAoE,cAAA,CAAAG,UAAA,OACzC,4CAA4C;IAG1E7E,EAAA,CAAAoB,SAAA,EAAkB;IAE7BpB,EAFW,CAAA2B,UAAA,mBAAkB,oBAAoB,+BAA+B,WAAArB,MAAA,CAAAoE,cAAA,kBAAApE,MAAA,CAAAoE,cAAA,CAAAI,qBAAA,OAC5B,+CACN;IAGnC9E,EAAA,CAAAoB,SAAA,EAAkB;IACYpB,EAD9B,CAAA2B,UAAA,mBAAkB,qBAAqB,yBAAyB,WAAArB,MAAA,CAAAyE,YAAA,kBAAAzE,MAAA,CAAAyE,YAAA,CAAAC,WAAA,OACnC,yCAAyC;IAWtEhF,EAAA,CAAAoB,SAAA,GAAsB;IAAtBpB,EAAA,CAAA2B,UAAA,UAAArB,MAAA,CAAA2E,eAAA,CAAsB;IAI3BjF,EAAA,CAAAoB,SAAA,GAAiB;IAAjBpB,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAA4E,WAAA,CAAiB;IAIjBlF,EAAA,CAAAoB,SAAA,EAAqC;IAArCpB,EAAA,CAAA2B,UAAA,UAAArB,MAAA,CAAA4E,WAAA,IAAA5E,MAAA,CAAA2E,eAAA,CAAqC;IAMrCjF,EAAA,CAAAoB,SAAA,EAAsC;IAAtCpB,EAAA,CAAA2B,UAAA,UAAArB,MAAA,CAAA4E,WAAA,KAAA5E,MAAA,CAAA2E,eAAA,CAAsC;IAoB1CjF,EAAA,CAAAoB,SAAA,GAAgD;IAAhDpB,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAA6E,kBAAA,yBAAgD;IAKjDnF,EAAA,CAAAoB,SAAA,EAAyF;IAAzFpB,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAAwB,YAAA,6BAAAxB,MAAA,CAAA6E,kBAAA,yBAAyF;IAaxFnF,EAAA,CAAAoB,SAAA,GAAuC;IAAvCpB,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAA6E,kBAAA,gBAAuC;IAI1BnF,EAAA,CAAAoB,SAAA,EAAuE;IAAvEpB,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAAwB,YAAA,oBAAAxB,MAAA,CAAA6E,kBAAA,gBAAuE;IAYpFnF,EAAA,CAAAoB,SAAA,GAA4C;IAA5CpB,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAA6E,kBAAA,qBAA4C;IAK7CnF,EAAA,CAAAoB,SAAA,EAAiF;IAAjFpB,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAAwB,YAAA,yBAAAxB,MAAA,CAAA6E,kBAAA,qBAAiF;IAahFnF,EAAA,CAAAoB,SAAA,GAAwC;IAAxCpB,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAA6E,kBAAA,iBAAwC;IAI3BnF,EAAA,CAAAoB,SAAA,EAAyE;IAAzEpB,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAAwB,YAAA,qBAAAxB,MAAA,CAAA6E,kBAAA,iBAAyE;IAYtFnF,EAAA,CAAAoB,SAAA,GAAwC;IAAxCpB,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAA6E,kBAAA,iBAAwC;IAI3BnF,EAAA,CAAAoB,SAAA,EAAyE;IAAzEpB,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAAwB,YAAA,qBAAAxB,MAAA,CAAA6E,kBAAA,iBAAyE;IAatFnF,EAAA,CAAAoB,SAAA,GAAwC;IAAxCpB,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAA6E,kBAAA,iBAAwC;IAI3BnF,EAAA,CAAAoB,SAAA,EAAyE;IAAzEpB,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAAwB,YAAA,qBAAAxB,MAAA,CAAA6E,kBAAA,iBAAyE;IAYtFnF,EAAA,CAAAoB,SAAA,GAAwC;IAAxCpB,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAA6E,kBAAA,iBAAwC;IAI3BnF,EAAA,CAAAoB,SAAA,EAAyE;IAAzEpB,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAAwB,YAAA,qBAAAxB,MAAA,CAAA6E,kBAAA,iBAAyE;IAQ9FnF,EAAA,CAAAoB,SAAA,EAAqC;IAArCpB,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAA2E,eAAA,KAAA3E,MAAA,CAAA4E,WAAA,CAAqC;;;;;;IA4B3BlF,EADJ,CAAAC,cAAA,cAA2D,iBACC;IAA1BD,EAAA,CAAAE,UAAA,mBAAAkF,gEAAA;MAAApF,EAAA,CAAAI,aAAA,CAAAiF,GAAA;MAAA,MAAA/E,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,aAAA,EAAe;IAAA,EAAC;IACnDT,EAAA,CAAAU,SAAA,mBAAyC;IACzCV,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAW,MAAA,iBAAU;IACpBX,EADoB,CAAAY,YAAA,EAAO,EAClB;IACTZ,EAAA,CAAAC,cAAA,iBAAyD;IAA3BD,EAAA,CAAAE,UAAA,mBAAAoF,gEAAA;MAAAtF,EAAA,CAAAI,aAAA,CAAAiF,GAAA;MAAA,MAAA/E,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAQ,cAAA,EAAgB;IAAA,EAAC;IACpDd,EAAA,CAAAU,SAAA,mBAAyC;IACzCV,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAW,MAAA,gBAAS;IAEvBX,EAFuB,CAAAY,YAAA,EAAO,EACjB,EACP;;;;;IA+CdZ,EAAA,CAAAC,cAAA,cAAqE;IACjED,EAAA,CAAAU,SAAA,cAAmC;IACnCV,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAW,MAAA,uBAAgB;IACvBX,EADuB,CAAAY,YAAA,EAAI,EACrB;;;;;IACNZ,EAAA,CAAAU,SAAA,2BAGmB;;;;IAFWV,EAA1B,CAAA2B,UAAA,eAAArB,MAAA,CAAApB,UAAA,CAAyB,YAAAoB,MAAA,CAAAwB,YAAA,gBAAwC;;;;;IAWrE9B,EAAA,CAAAC,cAAA,cAAmE;IAC/DD,EAAA,CAAAU,SAAA,cAAmC;IACnCV,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAW,MAAA,uBAAgB;IACvBX,EADuB,CAAAY,YAAA,EAAI,EACrB;;;;;IACNZ,EAAA,CAAAU,SAAA,2BAEmB;;;;IADWV,EAA1B,CAAA2B,UAAA,eAAArB,MAAA,CAAApB,UAAA,CAAyB,YAAAoB,MAAA,CAAAwB,YAAA,cAAsC;;;;;IAWnE9B,EAAA,CAAAC,cAAA,cAAsE;IAClED,EAAA,CAAAU,SAAA,cAAmC;IACnCV,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAW,MAAA,uBAAgB;IACvBX,EADuB,CAAAY,YAAA,EAAI,EACrB;;;;;IACNZ,EAAA,CAAAU,SAAA,2BAGmB;;;;IAFWV,EAA1B,CAAA2B,UAAA,eAAArB,MAAA,CAAApB,UAAA,CAAyB,YAAAoB,MAAA,CAAAwB,YAAA,iBAAyC;;;;;IAatE9B,EAAA,CAAAC,cAAA,cAAsD;IAClDD,EAAA,CAAAU,SAAA,cAAmC;IACnCV,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAW,MAAA,+BAAwB;IAC/BX,EAD+B,CAAAY,YAAA,EAAI,EAC7B;;;;;IAIFZ,EADJ,CAAAC,cAAA,cAAwF,cAC1D;IACtBD,EAAA,CAAAU,SAAA,YAAiC;IACjCV,EAAA,CAAAC,cAAA,cAAqB;IAAAD,EAAA,CAAAW,MAAA,sCAA+B;IAE5DX,EAF4D,CAAAY,YAAA,EAAM,EACxD,EACJ;;;;;;IAuB8BZ,EAAA,CAAAU,SAAA,eAE8B;;;;;;IAC9BV,EAAA,CAAAU,SAAA,eAE8B;;;;;;IAXtCV,EAAA,CAAAC,cAAA,iBAG2E;IAFpDD,EAAA,CAAAE,UAAA,mBAAAqF,0FAAA;MAAAvF,EAAA,CAAAI,aAAA,CAAAoF,GAAA;MAAA,MAAAC,IAAA,GAAAzF,EAAA,CAAAO,aAAA,GAAAmF,KAAA;MAAA,MAAApF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqF,kBAAA,CAAAF,IAAA,CAAqB;IAAA,EAAC;;IAGlDzF,EAAA,CAAAC,cAAA,cACuC;IAInCD,EAHA,CAAAkB,UAAA,IAAA0E,6EAAA,mBAE8B,IAAAC,6EAAA,mBAGA;IAEtC7F,EADI,CAAAY,YAAA,EAAM,EACD;;;;;IAXLZ,EAAA,CAAA8F,WAAA,aAAAxF,MAAA,CAAAyF,aAAA,CAAAN,IAAA,EAAmC;IACnCzF,EAAA,CAAA2B,UAAA,UAAArB,MAAA,CAAAyF,aAAA,CAAAN,IAAA,8CAAsE;IAG3DzF,EAAA,CAAAoB,SAAA,GAAuB;IAAvBpB,EAAA,CAAA2B,UAAA,UAAArB,MAAA,CAAAyF,aAAA,CAAAN,IAAA,EAAuB;IAGvBzF,EAAA,CAAAoB,SAAA,EAAsB;IAAtBpB,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAAyF,aAAA,CAAAN,IAAA,EAAsB;;;;;IAe7BzF,EAAA,CAAAC,cAAA,cAAoE;IAChED,EAAA,CAAAW,MAAA,GACJ;IAAAX,EAAA,CAAAY,YAAA,EAAM;;;;IADFZ,EAAA,CAAAoB,SAAA,EACJ;IADIpB,EAAA,CAAAgG,kBAAA,MAAAC,YAAA,MACJ;;;;;IAJJjG,EAHZ,CAAAC,cAAA,aAA0D,aACtC,cACkB,cACG;IAAAD,EAAA,CAAAW,MAAA,iBAAU;IAAAX,EAAA,CAAAY,YAAA,EAAM;IAC7CZ,EAAA,CAAAC,cAAA,cAA2B;IACvBD,EAAA,CAAAkB,UAAA,IAAAgF,mEAAA,kBAAoE;IAMpFlG,EAHY,CAAAY,YAAA,EAAM,EACJ,EACL,EACJ;;;;IANqCZ,EAAA,CAAAoB,SAAA,GAAkB;IAAlBpB,EAAA,CAAA2B,UAAA,YAAAwE,SAAA,CAAAC,SAAA,CAAkB;;;;;IA3BhEpG,EAAA,CAAAqG,uBAAA,GAAyE;IAEjErG,EADJ,CAAAC,cAAA,aAAsB,aACQ;IACtBD,EAAA,CAAAkB,UAAA,IAAAoF,iEAAA,qBAG2E;IAW/EtG,EAAA,CAAAY,YAAA,EAAK;IACLZ,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAW,MAAA,GAAqB;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACjDZ,EAAA,CAAAC,cAAA,aAA2B;IAAAD,EAAA,CAAAW,MAAA,GAAyB;IACxDX,EADwD,CAAAY,YAAA,EAAK,EACxD;IAELZ,EAAA,CAAAkB,UAAA,IAAAqF,6DAAA,iBAA0D;;;;;;;IAnBzCvG,EAAA,CAAAoB,SAAA,GAAmD;IAAnDpB,EAAA,CAAA2B,UAAA,SAAAwE,SAAA,CAAAC,SAAA,IAAAD,SAAA,CAAAC,SAAA,CAAAI,MAAA,KAAmD;IAezCxG,EAAA,CAAAoB,SAAA,GAAqB;IAArBpB,EAAA,CAAAyB,iBAAA,CAAA0E,SAAA,CAAAM,SAAA,CAAqB;IACjBzG,EAAA,CAAAoB,SAAA,GAAyB;IAAzBpB,EAAA,CAAAyB,iBAAA,CAAA0E,SAAA,CAAAO,aAAA,CAAyB;IAGnD1G,EAAA,CAAAoB,SAAA,EAAsB;IAAtBpB,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAAyF,aAAA,CAAAN,IAAA,EAAsB;;;;;IA7B/BzF,EAJhB,CAAAC,cAAA,cAAuG,cACxE,gBACY,YACxB,SACC;IACAD,EAAA,CAAAU,SAAA,aAA+B;IAC/BV,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAW,MAAA,iBAAU;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACnBZ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAW,MAAA,uBAAgB;IAE5BX,EAF4B,CAAAY,YAAA,EAAK,EACxB,EACD;IACRZ,EAAA,CAAAC,cAAA,aAAO;IACHD,EAAA,CAAAkB,UAAA,KAAAyF,wDAAA,2BAAyE;IAsCzF3G,EAHY,CAAAY,YAAA,EAAQ,EACJ,EACN,EACJ;;;;IAtC0CZ,EAAA,CAAAoB,SAAA,IAA0B;IAA1BpB,EAAA,CAAA2B,UAAA,YAAArB,MAAA,CAAAsG,qBAAA,CAA0B;;;;;IAgD1E5G,EAAA,CAAAC,cAAA,cAAuE;IACnED,EAAA,CAAAU,SAAA,cAAmC;IACnCV,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAW,MAAA,uBAAgB;IACvBX,EADuB,CAAAY,YAAA,EAAI,EACrB;;;;;IACNZ,EAAA,CAAAU,SAAA,2BAGmB;;;;IAFWV,EAA1B,CAAA2B,UAAA,eAAArB,MAAA,CAAApB,UAAA,CAAyB,YAAAoB,MAAA,CAAAwB,YAAA,kBAA0C;;;;;IAOnE9B,EAAA,CAAAC,cAAA,cAAiE;IAC7DD,EAAA,CAAAU,SAAA,cAAmC;IACnCV,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAW,MAAA,uBAAgB;IACvBX,EADuB,CAAAY,YAAA,EAAI,EACrB;;;;;;IAIEZ,EAAA,CAAAC,cAAA,mBAC0E;IAA1CD,EAAA,CAAAE,UAAA,mBAAA2G,uFAAA;MAAA7G,EAAA,CAAAI,aAAA,CAAA0G,IAAA;MAAA,MAAAxG,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAyG,kBAAA,CAAmB,WAAW,CAAC;IAAA,EAAC;IAAC/G,EAAA,CAAAY,YAAA,EAAW;;;;;IAGrFZ,EADJ,CAAAC,cAAA,cAA0E,cAC5C;IACtBD,EAAA,CAAAU,SAAA,YAAwD;IACxDV,EAAA,CAAAC,cAAA,cAAqB;IAAAD,EAAA,CAAAW,MAAA,wBAAiB;IAE9CX,EAF8C,CAAAY,YAAA,EAAM,EAC1C,EACJ;;;;;IAOMZ,EAAA,CAAAC,cAAA,cAAkD;IAAAD,EAAA,CAAAW,MAAA,wBAAiB;IAAAX,EAAA,CAAAY,YAAA,EAAM;;;;;IAFrEZ,EAHZ,CAAAC,cAAA,cAAsE,cACzC,cAC0D,eACnD;IAAAD,EAAA,CAAAW,MAAA,GAAmB;IAC/CX,EAD+C,CAAAY,YAAA,EAAO,EAChD;IACNZ,EAAA,CAAAkB,UAAA,IAAA8F,mEAAA,kBAAkD;IACtDhH,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAW,MAAA,GAAqB;IACjDX,EADiD,CAAAY,YAAA,EAAO,EAClD;;;;;IAN4BZ,EAAA,CAAAoB,SAAA,GAAoD;IAApDpB,EAAA,CAAAqB,WAAA,UAAAf,MAAA,CAAAgB,WAAA,CAAA2F,QAAA,CAAAC,UAAA,aAAoD;IAClDlH,EAAA,CAAAoB,SAAA,GAAmB;IAAnBpB,EAAA,CAAAyB,iBAAA,CAAAwF,QAAA,CAAAE,QAAA,CAAmB;IAEzCnH,EAAA,CAAAoB,SAAA,EAAsB;IAAtBpB,EAAA,CAAA2B,UAAA,UAAAsF,QAAA,CAAAC,UAAA,CAAsB;IAERlH,EAAA,CAAAoB,SAAA,GAAqB;IAArBpB,EAAA,CAAAyB,iBAAA,CAAAwF,QAAA,CAAAC,UAAA,CAAqB;;;;;IARrDlH,EAAA,CAAAC,cAAA,cAAsE;IAClED,EAAA,CAAAkB,UAAA,IAAAkG,6DAAA,kBAAsE;IAS1EpH,EAAA,CAAAY,YAAA,EAAM;;;;IAToBZ,EAAA,CAAAoB,SAAA,EAAwB;IAAxBpB,EAAA,CAAA2B,UAAA,YAAArB,MAAA,CAAA+G,SAAA,CAAAC,KAAA,OAAwB;;;;;IAbtDtH,EAAA,CAAAqG,uBAAA,GAAuD;IACnDrG,EAAA,CAAAC,cAAA,cAA+B;IAC3BD,EAAA,CAAAW,MAAA,mBACA;IAAAX,EAAA,CAAAkB,UAAA,IAAAqG,4DAAA,uBAC0E;IAC9EvH,EAAA,CAAAY,YAAA,EAAM;IAONZ,EANA,CAAAkB,UAAA,IAAAsG,uDAAA,kBAA0E,IAAAC,uDAAA,kBAMJ;;;;;IATvDzH,EAAA,CAAAoB,SAAA,GAAuC;IAAvCpB,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAA+G,SAAA,IAAA/G,MAAA,CAAA+G,SAAA,CAAAb,MAAA,KAAuC;IAGxBxG,EAAA,CAAAoB,SAAA,EAA0C;IAA1CpB,EAAA,CAAA2B,UAAA,UAAArB,MAAA,CAAA+G,SAAA,IAAA/G,MAAA,CAAA+G,SAAA,CAAAb,MAAA,OAA0C;IAMlExG,EAAA,CAAAoB,SAAA,EAAuC;IAAvCpB,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAA+G,SAAA,IAAA/G,MAAA,CAAA+G,SAAA,CAAAb,MAAA,KAAuC;;;;;IAsBrDxG,EAAA,CAAAC,cAAA,cAAsE;IAClED,EAAA,CAAAU,SAAA,cAAmC;IACnCV,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAW,MAAA,uBAAgB;IACvBX,EADuB,CAAAY,YAAA,EAAI,EACrB;;;;;IACNZ,EAAA,CAAAU,SAAA,2BAGmB;;;;IAFWV,EAA1B,CAAA2B,UAAA,eAAArB,MAAA,CAAApB,UAAA,CAAyB,YAAAoB,MAAA,CAAAwB,YAAA,iBAAyC;;;;;;IAQlE9B,EAAA,CAAAC,cAAA,mBACgF;IAAhDD,EAAA,CAAAE,UAAA,mBAAAwH,wEAAA;MAAA1H,EAAA,CAAAI,aAAA,CAAAuH,IAAA;MAAA,MAAArH,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAyG,kBAAA,CAAmB,iBAAiB,CAAC;IAAA,EAAC;IAAC/G,EAAA,CAAAY,YAAA,EAAW;;;;;IAG3FZ,EAAA,CAAAC,cAAA,cAAyE;IACrED,EAAA,CAAAU,SAAA,cAAmC;IACnCV,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAW,MAAA,uBAAgB;IACvBX,EADuB,CAAAY,YAAA,EAAI,EACrB;;;;;IAEFZ,EADJ,CAAAC,cAAA,cAAkI,cACpG;IACtBD,EAAA,CAAAU,SAAA,YAAwD;IACxDV,EAAA,CAAAC,cAAA,cAAqB;IAAAD,EAAA,CAAAW,MAAA,wBAAiB;IAE9CX,EAF8C,CAAAY,YAAA,EAAM,EAC1C,EACJ;;;;;IAOMZ,EAAA,CAAAC,cAAA,cAA+E;IAAAD,EAAA,CAAAW,MAAA,wBAAiB;IAAAX,EAAA,CAAAY,YAAA,EAAM;;;;;IAFlGZ,EAHZ,CAAAC,cAAA,cAAgE,cACnC,cACsE,eAC/D;IAAAD,EAAA,CAAAW,MAAA,GAAsC;IAClEX,EADkE,CAAAY,YAAA,EAAO,EACnE;IACNZ,EAAA,CAAAkB,UAAA,IAAA0G,oDAAA,kBAA+E;IACnF5H,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAW,MAAA,GAAgD;IAC5EX,EAD4E,CAAAY,YAAA,EAAO,EAC7E;;;;;IANmBZ,EAAA,CAAAoB,SAAA,GAAyE;IAAzEpB,EAAA,CAAAqB,WAAA,UAAAf,MAAA,CAAAgB,WAAA,CAAAuG,QAAA,CAAAC,gBAAA,IAAAD,QAAA,CAAArG,YAAA,OAAyE;IAC9DxB,EAAA,CAAAoB,SAAA,GAAsC;IAAtCpB,EAAA,CAAAyB,iBAAA,CAAAoG,QAAA,CAAAE,KAAA,IAAAF,QAAA,CAAAnG,aAAA,CAAsC;IAE5D1B,EAAA,CAAAoB,SAAA,EAAmD;IAAnDpB,EAAA,CAAA2B,UAAA,WAAAkG,QAAA,CAAAC,gBAAA,IAAAD,QAAA,CAAArG,YAAA,EAAmD;IAErCxB,EAAA,CAAAoB,SAAA,GAAgD;IAAhDpB,EAAA,CAAAyB,iBAAA,CAAAoG,QAAA,CAAAC,gBAAA,IAAAD,QAAA,CAAArG,YAAA,CAAgD;;;;;IARhFxB,EAAA,CAAAC,cAAA,cAA4H;IACxHD,EAAA,CAAAkB,UAAA,IAAA8G,8CAAA,kBAAgE;IASpEhI,EAAA,CAAAY,YAAA,EAAM;;;;IAToBZ,EAAA,CAAAoB,SAAA,EAAkB;IAAlBpB,EAAA,CAAA2B,UAAA,YAAArB,MAAA,CAAA2H,eAAA,CAAkB;;;;;IAoBhDjI,EAAA,CAAAC,cAAA,cAAsE;IAClED,EAAA,CAAAU,SAAA,cAAmC;IACnCV,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAW,MAAA,uBAAgB;IACvBX,EADuB,CAAAY,YAAA,EAAI,EACrB;;;;;IACNZ,EAAA,CAAAU,SAAA,2BAGmB;;;;IAFWV,EAA1B,CAAA2B,UAAA,eAAArB,MAAA,CAAApB,UAAA,CAAyB,YAAAoB,MAAA,CAAAwB,YAAA,iBAAyC;;;;;;IAtS9D9B,EAPhB,CAAAC,cAAA,aAAwD,aAGD,aACrB,cAEI,qBACsE;IAA7BD,EAAA,CAAAE,UAAA,uBAAAgI,kEAAA;MAAAlI,EAAA,CAAAI,aAAA,CAAA+H,GAAA;MAAA,MAAA7H,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAaF,MAAA,CAAA2B,YAAA,EAAc;IAAA,EAAC;IAC3FjC,EAAA,CAAAY,YAAA,EAAa;IACbZ,EAAA,CAAAC,cAAA,qBACqC;IAAjCD,EAAA,CAAAE,UAAA,uBAAAkI,kEAAA;MAAApI,EAAA,CAAAI,aAAA,CAAA+H,GAAA;MAAA,MAAA7H,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAaF,MAAA,CAAA6B,gBAAA,EAAkB;IAAA,EAAC;IACpCnC,EAAA,CAAAY,YAAA,EAAa;IAGbZ,EAAA,CAAAC,cAAA,qBAGuC;IAAnCD,EADA,CAAAE,UAAA,uBAAAmI,kEAAAhG,MAAA;MAAArC,EAAA,CAAAI,aAAA,CAAA+H,GAAA;MAAA,MAAA7H,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAaF,MAAA,CAAAgC,gBAAA,CAAAD,MAAA,CAAwB;IAAA,EAAC,mBAAAiG,8DAAAjG,MAAA;MAAArC,EAAA,CAAAI,aAAA,CAAA+H,GAAA;MAAA,MAAA7H,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAC7BF,MAAA,CAAAgC,gBAAA,CAAAD,MAAA,CAAwB;IAAA,EAAC;IACtCrC,EAAA,CAAAY,YAAA,EAAa;IACbZ,EAAA,CAAAkB,UAAA,IAAAqH,uCAAA,kBAA2D;IAU/DvI,EAAA,CAAAY,YAAA,EAAM;IAIFZ,EADJ,CAAAC,cAAA,cAA4B,uBAIsB;IAA1CD,EAAA,CAAAE,UAAA,2BAAAsI,wEAAAnG,MAAA;MAAArC,EAAA,CAAAI,aAAA,CAAA+H,GAAA;MAAA,MAAA7H,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAiBF,MAAA,CAAAoC,eAAA,CAAAL,MAAA,CAAuB;IAAA,EAAC;IAC7CrC,EAAA,CAAAY,YAAA,EAAe;IACfZ,EAAA,CAAAC,cAAA,sBAC2E;IAAjDD,EAAA,CAAAE,UAAA,uBAAAuI,mEAAA;MAAAzI,EAAA,CAAAI,aAAA,CAAA+H,GAAA;MAAA,MAAA7H,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAaF,MAAA,CAAAsC,WAAA,EAAa;IAAA,EAAC;IAIjE5C,EAHY,CAAAY,YAAA,EAAa,EACX,EACJ,EACJ;IAIFZ,EADJ,CAAAC,cAAA,eAA+B,eACF;IAarBD,EAZA,CAAAU,SAAA,yBAEgB,yBAIA,yBAIA,yBAIA;IAExBV,EADI,CAAAY,YAAA,EAAM,EACJ;IAOEZ,EAJR,CAAAC,cAAA,eAAyB,eAEI,eACC,UAClB;IAAAD,EAAA,CAAAW,MAAA,oBAAY;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACrBZ,EAAA,CAAAC,cAAA,oBACgD;IAA5CD,EAAA,CAAAE,UAAA,mBAAAwI,6DAAA;MAAA1I,EAAA,CAAAI,aAAA,CAAA+H,GAAA;MAAA,MAAA7H,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAyG,kBAAA,CAAmB,aAAa,CAAC;IAAA,EAAC;IACnD/G,EADoD,CAAAY,YAAA,EAAW,EACzD;IAKNZ,EAJA,CAAAkB,UAAA,KAAAyH,wCAAA,kBAAqE,KAAAC,qDAAA,+BAM7B;IAE5C5I,EAAA,CAAAY,YAAA,EAAM;IAIEZ,EAFR,CAAAC,cAAA,eAA6B,eACC,UAClB;IAAAD,EAAA,CAAAW,MAAA,kCAA0B;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACnCZ,EAAA,CAAAC,cAAA,oBAC8C;IAA1CD,EAAA,CAAAE,UAAA,mBAAA2I,6DAAA;MAAA7I,EAAA,CAAAI,aAAA,CAAA+H,GAAA;MAAA,MAAA7H,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAyG,kBAAA,CAAmB,WAAW,CAAC;IAAA,EAAC;IACjD/G,EADkD,CAAAY,YAAA,EAAW,EACvD;IAKNZ,EAJA,CAAAkB,UAAA,KAAA4H,wCAAA,kBAAmE,KAAAC,qDAAA,+BAKqC;IAE5G/I,EAAA,CAAAY,YAAA,EAAM;IAKEZ,EAFR,CAAAC,cAAA,eAA6B,eACC,UAClB;IAAAD,EAAA,CAAAW,MAAA,mCAA2B;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACpCZ,EAAA,CAAAC,cAAA,oBACiD;IAA7CD,EAAA,CAAAE,UAAA,mBAAA8I,6DAAA;MAAAhJ,EAAA,CAAAI,aAAA,CAAA+H,GAAA;MAAA,MAAA7H,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAyG,kBAAA,CAAmB,cAAc,CAAC;IAAA,EAAC;IACpD/G,EADqD,CAAAY,YAAA,EAAW,EAC1D;IAKNZ,EAJA,CAAAkB,UAAA,KAAA+H,wCAAA,kBAAsE,KAAAC,qDAAA,+BAM9B;IAE5ClJ,EAAA,CAAAY,YAAA,EAAM;IAIEZ,EAFR,CAAAC,cAAA,eAA6B,eACC,UAClB;IAAAD,EAAA,CAAAW,MAAA,mCAA2B;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACpCZ,EAAA,CAAAC,cAAA,oBACiD;IAA7CD,EAAA,CAAAE,UAAA,mBAAAiJ,6DAAA;MAAAnJ,EAAA,CAAAI,aAAA,CAAA+H,GAAA;MAAA,MAAA7H,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAyG,kBAAA,CAAmB,cAAc,CAAC;IAAA,EAAC;IACpD/G,EADqD,CAAAY,YAAA,EAAW,EAC1D;IAiBNZ,EAdA,CAAAkB,UAAA,KAAAkI,wCAAA,kBAAsD,KAAAC,wCAAA,kBAMkC,KAAAC,wCAAA,mBAQe;IAkD3GtJ,EAAA,CAAAY,YAAA,EAAM;IAKEZ,EAFR,CAAAC,cAAA,eAA6B,eACC,UAClB;IAAAD,EAAA,CAAAW,MAAA,sBAAc;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACvBZ,EAAA,CAAAC,cAAA,oBACkD;IAA9CD,EAAA,CAAAE,UAAA,mBAAAqJ,6DAAA;MAAAvJ,EAAA,CAAAI,aAAA,CAAA+H,GAAA;MAAA,MAAA7H,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAyG,kBAAA,CAAmB,eAAe,CAAC;IAAA,EAAC;IACrD/G,EADsD,CAAAY,YAAA,EAAW,EAC3D;IAKNZ,EAJA,CAAAkB,UAAA,KAAAsI,wCAAA,kBAAuE,KAAAC,qDAAA,+BAM/B;IAE5CzJ,EAAA,CAAAY,YAAA,EAAM;IAGFZ,EADJ,CAAAC,cAAA,eAAwB,eACc;IAK9BD,EAJA,CAAAkB,UAAA,KAAAwI,wCAAA,kBAAiE,KAAAC,iDAAA,2BAIV;IAyB/D3J,EADI,CAAAY,YAAA,EAAM,EACJ;IAKEZ,EAFR,CAAAC,cAAA,eAA6B,eACC,UAClB;IAAAD,EAAA,CAAAW,MAAA,qBAAa;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACtBZ,EAAA,CAAAC,cAAA,oBACiD;IAA7CD,EAAA,CAAAE,UAAA,mBAAA0J,6DAAA;MAAA5J,EAAA,CAAAI,aAAA,CAAA+H,GAAA;MAAA,MAAA7H,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAyG,kBAAA,CAAmB,cAAc,CAAC;IAAA,EAAC;IACpD/G,EADqD,CAAAY,YAAA,EAAW,EAC1D;IAKNZ,EAJA,CAAAkB,UAAA,KAAA2I,wCAAA,kBAAsE,KAAAC,qDAAA,+BAM9B;IAE5C9J,EAAA,CAAAY,YAAA,EAAM;IAIEZ,EAFR,CAAAC,cAAA,eAA6B,eACC,UAClB;IAAAD,EAAA,CAAAW,MAAA,wBAAgB;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACzBZ,EAAA,CAAAkB,UAAA,KAAA6I,6CAAA,uBACgF;IACpF/J,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAC,cAAA,eAA2B;IAWvBD,EAVA,CAAAkB,UAAA,KAAA8I,wCAAA,kBAAyE,KAAAC,wCAAA,kBAIyD,KAAAC,wCAAA,kBAMN;IAYpIlK,EADI,CAAAY,YAAA,EAAM,EACJ;IAKEZ,EAFR,CAAAC,cAAA,eAA6B,eACC,UAClB;IAAAD,EAAA,CAAAW,MAAA,sBAAc;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACvBZ,EAAA,CAAAC,cAAA,oBACiD;IAA7CD,EAAA,CAAAE,UAAA,mBAAAiK,6DAAA;MAAAnK,EAAA,CAAAI,aAAA,CAAA+H,GAAA;MAAA,MAAA7H,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAyG,kBAAA,CAAmB,cAAc,CAAC;IAAA,EAAC;IACpD/G,EADqD,CAAAY,YAAA,EAAW,EAC1D;IAKNZ,EAJA,CAAAkB,UAAA,KAAAkJ,wCAAA,kBAAsE,KAAAC,qDAAA,+BAM9B;IAIhDrK,EAFI,CAAAY,YAAA,EAAM,EACA,EACJ;;;;IA/S8BZ,EAAA,CAAAoB,SAAA,EAAkB;IAAlBpB,EAAA,CAAA2B,UAAA,cAAArB,MAAA,CAAAiE,IAAA,CAAkB;IAYlCvE,EAAA,CAAAoB,SAAA,GAAe;IAAfpB,EAAA,CAAA2B,UAAA,gBAAe;IAIb3B,EAAA,CAAAoB,SAAA,EAAyB;IAAzBpB,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAAkE,mBAAA,CAAyB;IAe3BxE,EAAA,CAAAoB,SAAA,GAAgB;IAChBpB,EADA,CAAA2B,UAAA,iBAAgB,cAAArB,MAAA,CAAAmE,oBAAA,GACoB;IAa7BzE,EAAA,CAAAoB,SAAA,GAAkB;IACmBpB,EADrC,CAAA2B,UAAA,mBAAkB,mBAAmB,yCAAyC,WAAArB,MAAA,CAAAgK,YAAA,kBAAAhK,MAAA,CAAAgK,YAAA,CAAAC,kBAAA,OAC1C,+CAA+C;IAGnFvK,EAAA,CAAAoB,SAAA,EAAkB;IACkBpB,EADpC,CAAA2B,UAAA,mBAAkB,0BAA0B,wCAAwC,WAAArB,MAAA,CAAAgK,YAAA,kBAAAhK,MAAA,CAAAgK,YAAA,CAAAE,iBAAA,OACjD,8CAA8C;IAGjFxK,EAAA,CAAAoB,SAAA,EAAkB;IACWpB,EAD7B,CAAA2B,UAAA,mBAAkB,sBAAsB,wBAAwB,WAAArB,MAAA,CAAAgK,YAAA,kBAAAhK,MAAA,CAAAgK,YAAA,CAAAG,UAAA,OACpC,kCAAkC;IAG9DzK,EAAA,CAAAoB,SAAA,EAAkB;IACuBpB,EADzC,CAAA2B,UAAA,mBAAkB,wBAAwB,qCAAqC,WAAArB,MAAA,CAAAgK,YAAA,kBAAAhK,MAAA,CAAAgK,YAAA,CAAAI,sBAAA,OACvC,sCAAsC;IAc3F1K,EAAA,CAAAoB,SAAA,GAAuC;IAAvCpB,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAA6E,kBAAA,gBAAuC;IAI1BnF,EAAA,CAAAoB,SAAA,EAAuE;IAAvEpB,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAAwB,YAAA,oBAAAxB,MAAA,CAAA6E,kBAAA,gBAAuE;IAYpFnF,EAAA,CAAAoB,SAAA,GAAqC;IAArCpB,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAA6E,kBAAA,cAAqC;IAIxBnF,EAAA,CAAAoB,SAAA,EAAmE;IAAnEpB,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAAwB,YAAA,kBAAAxB,MAAA,CAAA6E,kBAAA,cAAmE;IAYhFnF,EAAA,CAAAoB,SAAA,GAAwC;IAAxCpB,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAA6E,kBAAA,iBAAwC;IAI3BnF,EAAA,CAAAoB,SAAA,EAAyE;IAAzEpB,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAAwB,YAAA,qBAAAxB,MAAA,CAAA6E,kBAAA,iBAAyE;IActFnF,EAAA,CAAAoB,SAAA,GAAwB;IAAxBpB,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAAqK,kBAAA,CAAwB;IAMxB3K,EAAA,CAAAoB,SAAA,EAAwD;IAAxDpB,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAAsK,2BAAA,KAAAtK,MAAA,CAAAqK,kBAAA,CAAwD;IAQxD3K,EAAA,CAAAoB,SAAA,EAAyD;IAAzDpB,EAAA,CAAA2B,UAAA,UAAArB,MAAA,CAAAsK,2BAAA,KAAAtK,MAAA,CAAAqK,kBAAA,CAAyD;IA2DzD3K,EAAA,CAAAoB,SAAA,GAAyC;IAAzCpB,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAA6E,kBAAA,kBAAyC;IAI5BnF,EAAA,CAAAoB,SAAA,EAA2E;IAA3EpB,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAAwB,YAAA,sBAAAxB,MAAA,CAAA6E,kBAAA,kBAA2E;IAQpFnF,EAAA,CAAAoB,SAAA,GAAqC;IAArCpB,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAA6E,kBAAA,cAAqC;IAI5BnF,EAAA,CAAAoB,SAAA,EAAsC;IAAtCpB,EAAA,CAAA2B,UAAA,UAAArB,MAAA,CAAA6E,kBAAA,cAAsC;IAkCnDnF,EAAA,CAAAoB,SAAA,GAAwC;IAAxCpB,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAA6E,kBAAA,iBAAwC;IAI3BnF,EAAA,CAAAoB,SAAA,EAAyE;IAAzEpB,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAAwB,YAAA,qBAAAxB,MAAA,CAAA6E,kBAAA,iBAAyE;IAS7EnF,EAAA,CAAAoB,SAAA,GAAmD;IAAnDpB,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAA2H,eAAA,IAAA3H,MAAA,CAAA2H,eAAA,CAAAzB,MAAA,KAAmD;IAIxDxG,EAAA,CAAAoB,SAAA,GAA2C;IAA3CpB,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAA6E,kBAAA,oBAA2C;IAI3CnF,EAAA,CAAAoB,SAAA,EAAkG;IAAlGpB,EAAA,CAAA2B,UAAA,UAAArB,MAAA,CAAA6E,kBAAA,yBAAA7E,MAAA,CAAA2H,eAAA,IAAA3H,MAAA,CAAA2H,eAAA,CAAAzB,MAAA,QAAkG;IAMlGxG,EAAA,CAAAoB,SAAA,EAA6F;IAA7FpB,EAAA,CAAA2B,UAAA,UAAArB,MAAA,CAAA6E,kBAAA,uBAAA7E,MAAA,CAAA2H,eAAA,IAAA3H,MAAA,CAAA2H,eAAA,CAAAzB,MAAA,KAA6F;IAqBjGxG,EAAA,CAAAoB,SAAA,GAAwC;IAAxCpB,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAA6E,kBAAA,iBAAwC;IAI3BnF,EAAA,CAAAoB,SAAA,EAAyE;IAAzEpB,EAAA,CAAA2B,UAAA,SAAArB,MAAA,CAAAwB,YAAA,qBAAAxB,MAAA,CAAA6E,kBAAA,iBAAyE;;;ADzhB5G9F,OAAO,CAACH,UAAU,CAAC;AACnBE,eAAe,CAACF,UAAU,CAAC;AAC3BI,YAAY,CAACJ,UAAU,CAAC;AA2CxB,WAAa2L,kBAAkB;EAAzB,MAAOA,kBAAkB;IAyFnBC,gBAAA;IACAC,EAAA;IAzFOC,QAAQ,GAAG,IAAIjM,OAAO,EAAQ;IAC9BkM,YAAY,GAAgB;MAC3CC,OAAO,EAAE,SAAS;MAAO;MACzBC,SAAS,EAAE,SAAS;MAAK;MACzBC,QAAQ,EAAE,SAAS;MAAM;MACzBC,OAAO,EAAE,SAAS;MAAO;MACzBC,OAAO,EAAE,SAAS;MAAO;MACzBC,MAAM,EAAE,SAAS,CAAQ;KAC1B;IAED;IACiBC,mBAAmB,GAAG;MACrCvD,eAAe,EAAE,SAAS;MAAM;MAChCwD,WAAW,EAAE,SAAS;MAAU;MAChCC,WAAW,EAAE,SAAS;MAAU;MAChCC,gBAAgB,EAAE,SAAS;MAAK;MAChCC,YAAY,EAAE,SAAS;MAAS;MAChCC,YAAY,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MAAE;MAC5D9G,YAAY,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;MAAE;MACtC+G,YAAY,EAAE,SAAS;MAAS;MAChCC,WAAW,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MAAE;MACtEC,SAAS,EAAE,SAAS;MAAY;MAChCC,YAAY,EAAE,SAAS;MAAS;MAChCC,aAAa,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;MAAE;MACvC7E,SAAS,EAAE,SAAS;MAAY;MAChC8E,YAAY,EAAE,SAAS;MAAS;MAChCC,4BAA4B,EAAE,SAAS,CAAC;KACzC;IACgBC,cAAc,GAAkB;MAC/CC,eAAe,EAAE,aAAa;MAC9BC,UAAU,EAAE,oBAAoB;MAChCC,MAAM,EAAE;KACT;IACDtN,UAAU,GAAsBA,UAAU;IAC1CqF,IAAI;IACJkI,SAAS,GAAc;MACrBC,QAAQ,EAAE,IAAI,CAACC,kBAAkB,EAAE;MACnCC,MAAM,EAAE,IAAI,CAACC,gBAAgB;KAC9B;IACDC,SAAS,GAAW,SAAS;IAE7BC,IAAI,GAAc,CAChB;MACEC,EAAE,EAAE,SAAS;MACbC,KAAK,EAAE;KACR,EACD;MACED,EAAE,EAAE,QAAQ;MACZC,KAAK,EAAE;KACR,CACF;IAEDC,YAAY,GAAG;MACb,YAAY,EAAE,gBAAgB;MAC9B,QAAQ,EAAE,8BAA8B;MACxC,eAAe,EAAE,KAAK;MACtB,SAAS,EAAE;KACZ;IACDC,SAAS,GAAG,KAAK;IACjBjI,WAAW,GAAG,KAAK;IACnBkI,cAAc,GAAG,KAAK;IACtBC,iBAAiB,GAAG,KAAK;IACzB1C,kBAAkB,GAAG,KAAK;IAC1BxF,kBAAkB,GAA+B,EAAE;IACnDrD,YAAY,GAA0C,EAAE;IACxDwL,SAAS,GAAwC,EAAE;IACnDC,aAAa,GAAQ,EAAE;IACvB7I,cAAc,GAAQ,EAAE;IACxB4F,YAAY,GAAQ,EAAE;IACtBvF,YAAY,GAAQ,EAAE;IACtByI,iBAAiB,GAAQ,EAAE;IAC3BvF,eAAe,GAAU,EAAE;IAC3BZ,SAAS,GAAU,EAAE;IACrBxF,eAAe,GAAU,EAAE;IAC3B+E,qBAAqB,GAAU,EAAE;IACjC6G,YAAY,GAAgB,IAAIC,GAAG,EAAE;IACrCzI,eAAe,GAAG,KAAK;IACvB2F,2BAA2B,GAAG,KAAK;IACnC+C,gBAAgB,GAAa,EAAE;IAC/BC,WAAW,GAAU,EAAE;IACvBC,iBAAiB,GAAU,EAAE;IAC7BC,UAAU,GAAG,EAAE;IACfC,gBAAgB,GAAG,KAAK;IACxBvJ,mBAAmB,GAAG,KAAK;IACpBwJ,WAAW,GAAGjO,WAAW,CAACkO,kBAAkB;IAC5CC,eAAe,GAAGnO,WAAW,CAACoO,kBAAkB;IAEvDC,YACUtD,gBAAkC,EAClCC,EAAe;MADf,KAAAD,gBAAgB,GAAhBA,gBAAgB;MAChB,KAAAC,EAAE,GAAFA,EAAE;MAEV,IAAI,CAACsD,cAAc,EAAE;IACvB;IAEAC,QAAQA,CAAA;MACN,IAAI,CAACC,mBAAmB,EAAE;IAC5B;IAEAC,WAAWA,CAAA;MACT,IAAI,CAACxD,QAAQ,CAACyD,IAAI,EAAE;MACpB,IAAI,CAACzD,QAAQ,CAAC0D,QAAQ,EAAE;IAC1B;IAEQL,cAAcA,CAAA;MACpB,IAAI,CAAC9J,IAAI,GAAG,IAAI,CAACwG,EAAE,CAAC4D,KAAK,CAAC;QACxBjC,QAAQ,EAAE,CAAC,IAAI,CAACD,SAAS,CAACC,QAAQ,CAAC;QACnCE,MAAM,EAAE,CAAC,IAAI,CAACH,SAAS,CAACG,MAAM;OAC/B,CAAC;MACF,IAAI,CAACrI,IAAI,CAACqK,YAAY,CAACC,IAAI,CAAC7P,SAAS,CAAC,IAAI,CAACgM,QAAQ,CAAC,CAAC,CAAC8D,SAAS,CAACC,MAAM,IAAG;QACvE,IAAIA,MAAM,CAACrC,QAAQ,IAAIqC,MAAM,CAACnC,MAAM,EAAE;UACpC,IAAI,CAACH,SAAS,CAACC,QAAQ,GAAGqC,MAAM,CAACrC,QAAQ;UACzC,IAAI,CAACD,SAAS,CAACG,MAAM,GAAGmC,MAAM,CAACnC,MAAM;QACvC;MACF,CAAC,CAAC;IACJ;IAEQ2B,mBAAmBA,CAAA;MACzB,IAAI,CAACS,iBAAiB,EAAE;IAC1B;IAEAC,WAAWA,CAACC,OAAgB;MAC1B,IAAI,CAACpC,SAAS,GAAGoC,OAAO,CAAClC,EAAE;MAC3B,IAAI,CAACmC,mBAAmB,CAACD,OAAO,CAAClC,EAAE,CAAC;IACtC;IAEAoC,YAAYA,CAAA;MACV,OAAO,IAAI,CAACrC,IAAI,CAACsC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACtC,EAAE,KAAK,IAAI,CAACF,SAAS,CAAC,IAAI,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;IACzE;IAEQoC,mBAAmBA,CAACI,KAAa;MACvC,QAAQA,KAAK;QACX,KAAK,SAAS;UACZ,IAAI,CAACC,eAAe,EAAE;UACtB;QACF,KAAK,QAAQ;UACX,IAAI,CAACC,aAAa,EAAE;UACpB;MACJ;IACF;IAEAC,iBAAiBA,CAAA;MACf,MAAMC,UAAU,GAAG,IAAI,CAACpL,IAAI,CAACqL,KAAK;MAClC,IAAID,UAAU,CAACjD,QAAQ,IAAIiD,UAAU,CAAC/C,MAAM,EAAE;QAC5C,IAAI,CAACH,SAAS,CAACC,QAAQ,GAAGiD,UAAU,CAACjD,QAAQ;QAC7C,IAAI,CAACD,SAAS,CAACG,MAAM,GAAG+C,UAAU,CAAC/C,MAAM;MAC3C;IACF;IAEAhK,WAAWA,CAAA;MACT,IAAI,CAAC8M,iBAAiB,EAAE;MACxB,IAAI,CAAC,IAAI,CAACG,gBAAgB,EAAE,EAAE;QAC5BC,OAAO,CAACC,KAAK,CAAC,6BAA6B,CAAC;QAC5C;MACF;MACA,IAAI,CAACf,iBAAiB,EAAE;IAC1B;IAEQa,gBAAgBA,CAAA;MACtB,MAAMnD,QAAQ,GAAG,IAAIsD,IAAI,CAAC,IAAI,CAACvD,SAAS,CAACC,QAAQ,CAAC;MAClD,MAAME,MAAM,GAAG,IAAIoD,IAAI,CAAC,IAAI,CAACvD,SAAS,CAACG,MAAM,CAAC;MAC9C,OAAOF,QAAQ,IAAIE,MAAM,IAAIF,QAAQ,IAAI,IAAIsD,IAAI,EAAE;IACrD;IAEQrD,kBAAkBA,CAAA;MACxB,MAAMsD,IAAI,GAAG,IAAID,IAAI,EAAE;MACvB;MACAC,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC;MACf,OAAOD,IAAI,CAACE,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACzC;IAEQvD,gBAAgBA,CAAA;MACtB,OAAO,IAAImD,IAAI,EAAE,CAACG,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC/C;IAEQC,gBAAgBA,CAACC,UAAkB;MACzC,MAAM,CAACC,IAAI,EAAEC,KAAK,EAAEC,GAAG,CAAC,GAAGH,UAAU,CAACF,KAAK,CAAC,GAAG,CAAC;MAChD,OAAO,GAAGK,GAAG,IAAID,KAAK,IAAID,IAAI,EAAE;IAClC;IAEQG,2BAA2BA,CAAA;MACjC,OAAO;QACLhE,QAAQ,EAAE,IAAI,CAAC2D,gBAAgB,CAAC,IAAI,CAAC5D,SAAS,CAACC,QAAQ,CAAC;QACxDE,MAAM,EAAE,IAAI,CAACyD,gBAAgB,CAAC,IAAI,CAAC5D,SAAS,CAACG,MAAM;OACpD;IACH;IAEQoC,iBAAiBA,CAAA;MACvB,IAAI,CAAC7B,SAAS,GAAG,IAAI;MACrB,QAAQ,IAAI,CAACL,SAAS;QACpB,KAAK,SAAS;UACZ,IAAI,CAAC0C,eAAe,EAAE;UACtB;QACF,KAAK,QAAQ;UACX,IAAI,CAACC,aAAa,EAAE;UACpB;QACF;UACE,IAAI,CAACtC,SAAS,GAAG,KAAK;MAC1B;IACF;IAEQqC,eAAeA,CAAA;MACrB,IAAI,CAACrC,SAAS,GAAG,IAAI;MACrB,IAAI,CAACjI,WAAW,GAAG,IAAI;MACvB,MAAMyL,kBAAkB,GAAG,IAAI,CAACD,2BAA2B,EAAE;MAC7DzR,QAAQ,CAAC;QACP2R,aAAa,EAAE,IAAI,CAAC9F,gBAAgB,CAAC+F,mBAAmB,CAACF,kBAAkB,CAAC;QAC5EG,mBAAmB,EAAE,IAAI,CAAChG,gBAAgB,CAACiG,iBAAiB,CAACJ,kBAAkB,CAAC;QAChF1I,eAAe,EAAE,IAAI,CAAC6C,gBAAgB,CAACkG,qBAAqB,CAACL,kBAAkB,CAAC;QAChF5L,YAAY,EAAE,IAAI,CAAC+F,gBAAgB,CAACmG,eAAe,CAACN,kBAAkB,CAAC;QACvE7E,YAAY,EAAE,IAAI,CAAChB,gBAAgB,CAACoG,eAAe,CAACP,kBAAkB;OACvE,CAAC,CAAC9B,IAAI,CAAC7P,SAAS,CAAC,IAAI,CAACgM,QAAQ,CAAC,CAAC,CAC9B8D,SAAS,CAAC;QACTL,IAAI,EAAG0C,SAAS,IAAI;UAClB,IAAI,CAACC,oCAAoC,CAACD,SAAS,CAAC;UACpD,IAAI,CAACzM,cAAc,GAAGyM,SAAS,CAACL,mBAAmB;UACnD,IAAI,CAAC3D,SAAS,GAAG,KAAK;QACxB,CAAC;QACD4C,KAAK,EAAGA,KAAK,IAAI;UACfD,OAAO,CAACC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;UACrD,IAAI,CAAC5C,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;IACN;IAEQiE,oCAAoCA,CAACD,SAAc;MACzD,MAAME,QAAQ,GAAGF,SAAS,CAACP,aAAa;MACxC,MAAMU,mBAAmB,GAAGH,SAAS,CAAClJ,eAAe;MACrD,MAAMsJ,gBAAgB,GAAGJ,SAAS,CAACpM,YAAY;MAC/C,MAAMyM,gBAAgB,GAAGL,SAAS,CAACrF,YAAY;MAE/C2F,MAAM,CAACC,IAAI,CAAC,IAAI,CAACvM,kBAAkB,CAAC,CAACwM,OAAO,CAACC,GAAG,IAAG;QACjD,IAAI,CAACzM,kBAAkB,CAACyM,GAAG,CAAC,GAAG,KAAK;MACtC,CAAC,CAAC;MAEF,IAAIC,kBAAkB,GAAG,IAAI;MAC7B,IAAIP,mBAAmB,EAAE;QACvB,IAAIA,mBAAmB,CAACQ,kBAAkB,IAAIR,mBAAmB,CAACQ,kBAAkB,CAACtL,MAAM,GAAG,CAAC,EAAE;UAC/FqL,kBAAkB,GAAGP,mBAAmB,CAACQ,kBAAkB;QAC7D,CAAC,MAAM,IAAIR,mBAAmB,CAACS,aAAa,IAAIT,mBAAmB,CAACS,aAAa,CAACvL,MAAM,GAAG,CAAC,EAAE;UAC5FqL,kBAAkB,GAAGP,mBAAmB,CAACS,aAAa;QACxD,CAAC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACX,mBAAmB,CAAC,IAAIA,mBAAmB,CAAC9K,MAAM,GAAG,CAAC,EAAE;UAC/EqL,kBAAkB,GAAGP,mBAAmB;QAC1C;MACF;MAEA,IAAIO,kBAAkB,IAAIA,kBAAkB,CAACrL,MAAM,GAAG,CAAC,EAAE;QACvD,IAAI,CAAC1E,YAAY,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAACoQ,0BAA0B,CAACL,kBAAkB,CAAC;QAC1F,IAAI,CAAChQ,eAAe,GAAGgQ,kBAAkB;QACzC,IAAI,CAAC5M,eAAe,GAAG,KAAK;QAC5B,IAAI,CAACC,WAAW,GAAG,KAAK;MAC1B,CAAC,MAAM;QACL,IAAI,CAACpD,YAAY,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAACqQ,iBAAiB,CAAC,kBAAkB,CAAC;QACjF,IAAI,CAACtQ,eAAe,GAAG,EAAE;QACzB,IAAI,CAACoD,eAAe,GAAG,IAAI;QAC3B,IAAI,CAACC,WAAW,GAAG,KAAK;MAC1B;MAEA,IAAImM,QAAQ,CAACe,oBAAoB,IAAIf,QAAQ,CAACe,oBAAoB,CAAC5L,MAAM,GAAG,CAAC,EAAE;QAC7E,MAAM6L,UAAU,GAAGhB,QAAQ,CAACe,oBAAoB,CAACE,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAACtC,IAAI,CAAC;QAC9E,MAAMlB,MAAM,GAAGsC,QAAQ,CAACe,oBAAoB,CAACE,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAACC,KAAK,CAAC;QAC3E,IAAI,CAAC1Q,YAAY,CAAC,sBAAsB,CAAC,GAAG,IAAI,CAAC2Q,eAAe,CAAC,yBAAyB,EAAEJ,UAAU,EAAEtD,MAAM,EAAE,eAAe,CAAC;MAClI,CAAC,MAAM;QACL,IAAI,CAACjN,YAAY,CAAC,sBAAsB,CAAC,GAAG,IAAI,CAACqQ,iBAAiB,CAAC,yBAAyB,CAAC;MAC/F;MAEA,IAAId,QAAQ,CAACqB,qBAAqB,IAAIrB,QAAQ,CAACqB,qBAAqB,CAAClM,MAAM,GAAG,CAAC,EAAE;QAC/E,MAAMmM,cAAc,GAAGtB,QAAQ,CAACqB,qBAAqB,CAClDE,IAAI,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAKA,CAAC,CAACN,KAAK,GAAGK,CAAC,CAACL,KAAK,CAAC,CAC3ClL,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QACd,MAAM+K,UAAU,GAAGM,cAAc,CAACL,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAACQ,WAAW,CAAC;QACtE,MAAMhE,MAAM,GAAG4D,cAAc,CAACL,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAACC,KAAK,CAAC;QAC5D,IAAI,CAAC1Q,YAAY,CAAC,aAAa,CAAC,GAAG,IAAI,CAACkR,iBAAiB,CAAC,yBAAyB,EAAEX,UAAU,EAAEtD,MAAM,EAAE,aAAa,EAAE,QAAQ,CAAC;MACnI,CAAC,MAAM;QACL,IAAI,CAACjN,YAAY,CAAC,aAAa,CAAC,GAAG,IAAI,CAACqQ,iBAAiB,CAAC,yBAAyB,CAAC;MACtF;MAEA,IAAIc,WAAW,GAAG5B,QAAQ,CAAC6B,yBAAyB,IAAI7B,QAAQ,CAAC8B,wBAAwB;MACzF,IAAIF,WAAW,IAAIA,WAAW,CAACzM,MAAM,GAAG,CAAC,EAAE;QACzC,MAAM6L,UAAU,GAAGY,WAAW,CAACX,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAACtC,IAAI,CAAC;QAC5D,MAAMlB,MAAM,GAAGkE,WAAW,CAACX,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAAC/Q,YAAY,CAAC;QAChE,IAAI,CAACM,YAAY,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAAC2Q,eAAe,CAAC,oBAAoB,EAAEJ,UAAU,EAAEtD,MAAM,EAAE,eAAe,CAAC;MACzH,CAAC,MAAM;QACL,IAAI,CAACjN,YAAY,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAACqQ,iBAAiB,CAAC,oBAAoB,CAAC;MACtF;MAEA,IAAId,QAAQ,CAAC+B,4BAA4B,IAAI/B,QAAQ,CAAC+B,4BAA4B,CAAC5M,MAAM,GAAG,CAAC,EAAE;QAC7F,MAAM6L,UAAU,GAAGhB,QAAQ,CAAC+B,4BAA4B,CAACd,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAACc,mBAAmB,CAAC;QACrG,MAAMtE,MAAM,GAAGsC,QAAQ,CAAC+B,4BAA4B,CAACd,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAACC,KAAK,CAAC;QACnF,IAAI,CAAC1Q,YAAY,CAAC,cAAc,CAAC,GAAG,IAAI,CAACkR,iBAAiB,CAAC,8BAA8B,EAAEX,UAAU,EAAEtD,MAAM,EAAE,aAAa,EAAE,WAAW,CAAC;MAC5I,CAAC,MAAM;QACL,IAAI,CAACjN,YAAY,CAAC,cAAc,CAAC,GAAG,IAAI,CAACqQ,iBAAiB,CAAC,8BAA8B,CAAC;MAC5F;MAEA,IAAId,QAAQ,CAACiC,qBAAqB,IAAIjC,QAAQ,CAACiC,qBAAqB,CAAC9M,MAAM,GAAG,CAAC,EAAE;QAC/E,MAAM+M,OAAO,GAAGlC,QAAQ,CAACiC,qBAAqB,CAAChB,GAAG,CAAEC,IAAS,IAAK,CAACA,IAAI,CAACiB,QAAQ,EAAEjB,IAAI,CAACkB,UAAU,CAAC,CAAC;QACnG,IAAI,CAAC3R,YAAY,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC4R,cAAc,CAAC,eAAe,EAAEH,OAAO,EAAE,cAAc,CAAC;MACnG,CAAC,MAAM;QACL,IAAI,CAACzR,YAAY,CAAC,cAAc,CAAC,GAAG,IAAI,CAACqQ,iBAAiB,CAAC,eAAe,CAAC;MAC7E;MAEA,IAAInN,WAAW,GAAG,CAAC;MACnB,IAAI2O,aAAa,GAAG,CAAC;MACrB,IAAIC,UAAU,GAAG,CAAC;MAElB,IAAIrC,gBAAgB,IAAIA,gBAAgB,CAACxM,YAAY,EAAE;QACrD,MAAM8O,QAAQ,GAAGtC,gBAAgB,CAACxM,YAAY;QAC9CC,WAAW,GAAG6O,QAAQ,CAAC7O,WAAW,IAAI,CAAC;QACvC2O,aAAa,GAAGE,QAAQ,CAACF,aAAa,IAAI,CAAC;QAC3CC,UAAU,GAAGC,QAAQ,CAACD,UAAU,IAAI,CAAC;MACvC,CAAC,MAAM,IAAIrC,gBAAgB,EAAE;QAC3BvM,WAAW,GAAGuM,gBAAgB,CAACvM,WAAW,IAAI,CAAC;QAC/C2O,aAAa,GAAGpC,gBAAgB,CAACoC,aAAa,IAAI,CAAC;QACnDC,UAAU,GAAGrC,gBAAgB,CAACqC,UAAU,IAAI,CAAC;MAC/C;MAEA,MAAME,KAAK,GAAGF,UAAU,IAAK5O,WAAW,GAAG2O,aAAc;MACzD7D,OAAO,CAACiE,GAAG,CAAC,eAAe,EAAE/O,WAAW,EAAE,iBAAiB,EAAE2O,aAAa,EAAE,QAAQ,EAAEG,KAAK,CAAC;MAE5F,IAAIA,KAAK,GAAG,CAAC,EAAE;QACb,MAAME,kBAAkB,GAAIL,aAAa,GAAGG,KAAK,GAAI,GAAG;QACxD,MAAMG,gBAAgB,GAAIjP,WAAW,GAAG8O,KAAK,GAAI,GAAG;QACpD,MAAMP,OAAO,GAAG,CACd,CAAC,cAAc,EAAEU,gBAAgB,CAAC,EAClC,CAAC,gBAAgB,EAAED,kBAAkB,CAAC,CACvC;QACD,IAAI,CAAClS,YAAY,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC4R,cAAc,CAAC,gBAAgB,EAAEH,OAAO,EAAE,aAAa,CAAC;QACjG,IAAI,CAACxO,YAAY,GAAG;UAAEC,WAAW;UAAEkP,YAAY,EAAEP,aAAa;UAAEC,UAAU,EAAEE;QAAK,CAAE;MACrF,CAAC,MAAM;QACL,IAAI,CAAChS,YAAY,CAAC,cAAc,CAAC,GAAG,IAAI,CAACqQ,iBAAiB,CAAC,gBAAgB,CAAC;MAC9E;MAEA,IAAIE,UAAU,GAAa,EAAE;MAC7B,IAAItD,MAAM,GAAa,EAAE;MAEzB,IAAIyC,gBAAgB,EAAE;QACpB,IAAIA,gBAAgB,CAAC2C,aAAa,IAAInC,KAAK,CAACC,OAAO,CAACT,gBAAgB,CAAC2C,aAAa,CAAC,EAAE;UACnF9B,UAAU,GAAGb,gBAAgB,CAAC2C,aAAa,CAAC7B,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAAC6B,SAAS,CAAC;UAC9ErF,MAAM,GAAGyC,gBAAgB,CAAC2C,aAAa,CAAC7B,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAACzG,YAAY,CAAC;QAC/E,CAAC,MAAM,IAAI0F,gBAAgB,CAACa,UAAU,IAAIb,gBAAgB,CAAC6C,MAAM,EAAE;UACjEhC,UAAU,GAAGb,gBAAgB,CAACa,UAAU;UACxCtD,MAAM,GAAGyC,gBAAgB,CAAC6C,MAAM;QAClC,CAAC,MAAM,IAAI7C,gBAAgB,CAACa,UAAU,IAAIb,gBAAgB,CAAC8C,OAAO,EAAE;UAClEjC,UAAU,GAAGb,gBAAgB,CAACa,UAAU;UACxCtD,MAAM,GAAGyC,gBAAgB,CAAC8C,OAAO;QACnC,CAAC,MAAM,IAAItC,KAAK,CAACC,OAAO,CAACT,gBAAgB,CAAC,IAAIA,gBAAgB,CAAChL,MAAM,GAAG,CAAC,EAAE;UACzE6L,UAAU,GAAGb,gBAAgB,CAACc,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAAC6B,SAAS,IAAI7B,IAAI,CAACtC,IAAI,CAAC;UAC7ElB,MAAM,GAAGyC,gBAAgB,CAACc,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAACzG,YAAY,IAAIyG,IAAI,CAACgC,IAAI,CAAC;QAC9E;MACF;MAEA,IAAIlC,UAAU,CAAC7L,MAAM,GAAG,CAAC,IAAIuI,MAAM,CAACvI,MAAM,GAAG,CAAC,EAAE;QAC9C,IAAI,CAAC1E,YAAY,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC2Q,eAAe,CAAC,eAAe,EAAEJ,UAAU,EAAEtD,MAAM,EAAE,oBAAoB,CAAC;MACrH,CAAC,MAAM;QACL,IAAI,CAACjN,YAAY,CAAC,cAAc,CAAC,GAAG,IAAI,CAACqQ,iBAAiB,CAAC,eAAe,CAAC;MAC7E;IACF;IAEQ1C,aAAaA,CAAA;MACnB,IAAI,CAACtC,SAAS,GAAG,IAAI;MACrB,MAAMwD,kBAAkB,GAAG,IAAI,CAACD,2BAA2B,EAAE;MAC7D,IAAI,CAAC5F,gBAAgB,CAAC0J,qBAAqB,CAAC7D,kBAAkB,CAAC,CAC5D9B,IAAI,CAAC7P,SAAS,CAAC,IAAI,CAACgM,QAAQ,CAAC,CAAC,CAC9B8D,SAAS,CAAC;QACTL,IAAI,EAAGgG,IAAI,IAAI;UACb,IAAI,CAACC,yBAAyB,CAACD,IAAI,CAAC;UACpC,IAAI,CAACE,eAAe,CAACF,IAAI,CAAC;UAC1B,IAAI,CAACtH,SAAS,GAAG,KAAK;QACxB,CAAC;QACD4C,KAAK,EAAGA,KAAK,IAAI;UACfD,OAAO,CAACC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;UACjD,IAAI,CAAC5C,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;IACN;IAEQuH,yBAAyBA,CAACD,IAAS;MACzC,IAAI,CAACnK,YAAY,GAAGmK,IAAI;MACxB,IAAI,CAACjH,iBAAiB,GAAGiH,IAAI,CAACjH,iBAAiB;MAC/C,IAAI,CAACnG,SAAS,GAAGoN,IAAI,CAACpN,SAAS,IAAI,EAAE;MACrC,IAAI,CAACY,eAAe,GAAGwM,IAAI,CAACxM,eAAe,IAAI,EAAE;IACnD;IAIAtC,kBAAkBA,CAACD,KAAa;MAC9B,IAAI,IAAI,CAAC+H,YAAY,CAACmH,GAAG,CAAClP,KAAK,CAAC,EAAE;QAChC,IAAI,CAAC+H,YAAY,CAACoH,MAAM,CAACnP,KAAK,CAAC;MACjC,CAAC,MAAM;QACL,IAAI,CAAC+H,YAAY,CAACqH,GAAG,CAACpP,KAAK,CAAC;MAC9B;IACF;IAEAK,aAAaA,CAACL,KAAa;MACzB,OAAO,IAAI,CAAC+H,YAAY,CAACmH,GAAG,CAAClP,KAAK,CAAC;IACrC;IAEQqP,qBAAqBA,CAACN,IAAS;MACrC,IAAI,CAAC9J,kBAAkB,GAAG,IAAI;MAC9B,IAAI8J,IAAI,IAAIA,IAAI,CAACnK,YAAY,IAAImK,IAAI,CAACnK,YAAY,CAAC9D,MAAM,GAAG,CAAC,EAAE;QAC7D,IAAI,CAACI,qBAAqB,GAAG6N,IAAI,CAACnK,YAAY;QAC9C,IAAI,CAACM,2BAA2B,GAAG,KAAK;MAC1C,CAAC,MAAM;QACL,IAAI,CAAChE,qBAAqB,GAAG,EAAE;QAC/B,IAAI,CAACgE,2BAA2B,GAAG,IAAI;MACzC;MACA,IAAI,CAACD,kBAAkB,GAAG,KAAK;IACjC;IAQQgK,eAAeA,CAACF,IAAS;MAE/B,IAAI,CAACnK,YAAY,GAAGmK,IAAI;MAExB,IAAIA,IAAI,CAAC1I,WAAW,IAAI0I,IAAI,CAAC1I,WAAW,CAACvF,MAAM,GAAG,CAAC,EAAE;QACnD,MAAMwO,eAAe,GAAGP,IAAI,CAAC1I,WAAW,CAACuG,GAAG,CAAEC,IAAS,IAAK,CAACA,IAAI,CAAC0C,UAAU,EAAE1C,IAAI,CAACkB,UAAU,CAAC,CAAC;QAC/F,IAAI,CAAC3R,YAAY,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC4R,cAAc,CAAC,cAAc,EAAEsB,eAAe,EAAE,aAAa,CAAC;MACxG,CAAC,MAAM;QACL,IAAI,CAAClT,YAAY,CAAC,aAAa,CAAC,GAAG,IAAI,CAACqQ,iBAAiB,CAAC,cAAc,CAAC;MAC3E;MAEA,IAAIsC,IAAI,CAACzI,SAAS,IAAIyI,IAAI,CAACzI,SAAS,CAACxF,MAAM,GAAG,CAAC,EAAE;QAC/C,MAAM6L,UAAU,GAAGoC,IAAI,CAACzI,SAAS,CAAC1E,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACgL,GAAG,CAAE4C,KAAU,IAAKA,KAAK,CAACzO,SAAS,CAAC;QAClF,MAAM4N,MAAM,GAAGI,IAAI,CAACzI,SAAS,CAAC1E,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACgL,GAAG,CAAE4C,KAAU,IAAKA,KAAK,CAAChO,UAAU,CAAC;QAC/E,IAAI,CAACpF,YAAY,CAAC,WAAW,CAAC,GAAG,IAAI,CAACkR,iBAAiB,CAAC,4BAA4B,EAAEX,UAAU,EAAEgC,MAAM,EAAE,aAAa,EAAE,WAAW,CAAC;MACvI,CAAC,MAAM;QACL,IAAI,CAACvS,YAAY,CAAC,WAAW,CAAC,GAAG,IAAI,CAACqQ,iBAAiB,CAAC,4BAA4B,CAAC;MACvF;MAEA,IAAIsC,IAAI,CAACxI,YAAY,IAAIwI,IAAI,CAACxI,YAAY,CAACzF,MAAM,GAAG,CAAC,EAAE;QACrD,MAAM6L,UAAU,GAAGoC,IAAI,CAACxI,YAAY,CAACqG,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAAC4C,QAAQ,CAAC;QACtE,MAAMd,MAAM,GAAGI,IAAI,CAACxI,YAAY,CAACqG,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAACrL,UAAU,CAAC;QACpE,IAAI,CAACpF,YAAY,CAAC,cAAc,CAAC,GAAG,IAAI,CAACkR,iBAAiB,CAAC,6BAA6B,EAAEX,UAAU,EAAEgC,MAAM,EAAE,aAAa,EAAE,cAAc,CAAC;MAC9I,CAAC,MAAM;QACL,IAAI,CAACvS,YAAY,CAAC,cAAc,CAAC,GAAG,IAAI,CAACqQ,iBAAiB,CAAC,6BAA6B,CAAC;MAC3F;MAEA,IAAI,CAAC4C,qBAAqB,CAACN,IAAI,CAAC;MAEhC,IAAIA,IAAI,CAACvI,aAAa,IAAIuI,IAAI,CAACvI,aAAa,CAAC1F,MAAM,GAAG,CAAC,EAAE;QACvD,MAAM4O,iBAAiB,GAAGX,IAAI,CAACvI,aAAa,CAAC,CAAC,CAAC;QAC/C,MAAMmJ,iBAAiB,GAAG,CACxB,CAAC,oBAAoB,EAAED,iBAAiB,CAACE,qBAAqB,CAAC,EAC/D,CAAC,gBAAgB,EAAEF,iBAAiB,CAACG,iBAAiB,CAAC,CACxD;QACD,IAAI,CAACzT,YAAY,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC4R,cAAc,CAAC,gBAAgB,EAAE2B,iBAAiB,EAAE,SAAS,CAAC;MAC1G,CAAC,MAAM;QACL,IAAI,CAACvT,YAAY,CAAC,eAAe,CAAC,GAAG,IAAI,CAACqQ,iBAAiB,CAAC,gBAAgB,CAAC;MAC/E;MAEA,IAAIsC,IAAI,CAACpN,SAAS,IAAIoN,IAAI,CAACpN,SAAS,CAACb,MAAM,GAAG,CAAC,EAAE;QAC/C,MAAM6L,UAAU,GAAGoC,IAAI,CAACpN,SAAS,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACgL,GAAG,CAAEkD,IAAS,IAAKA,IAAI,CAACrO,QAAQ,CAAC;QAC/E,MAAMkN,MAAM,GAAGI,IAAI,CAACpN,SAAS,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACgL,GAAG,CAAEkD,IAAS,IAAKA,IAAI,CAACtO,UAAU,CAAC;QAC7E,IAAI,CAACpF,YAAY,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC2T,wBAAwB,CAAC,YAAY,EAAEpD,UAAU,EAAEgC,MAAM,EAAE,aAAa,EAAE,WAAW,CAAC;MAC9H,CAAC,MAAM;QACL,IAAI,CAACvS,YAAY,CAAC,WAAW,CAAC,GAAG,IAAI,CAACqQ,iBAAiB,CAAC,YAAY,CAAC;MACvE;MAEA,IAAIsC,IAAI,CAACtI,YAAY,IAAIsI,IAAI,CAACtI,YAAY,CAAC3F,MAAM,GAAG,CAAC,EAAE;QACrD,MAAM6L,UAAU,GAAGoC,IAAI,CAACtI,YAAY,CAACmG,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAACmD,YAAY,CAAC;QAC1E,MAAMrB,MAAM,GAAGI,IAAI,CAACtI,YAAY,CAACmG,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAACoD,cAAc,CAAC;QACxE,IAAI,CAAC7T,YAAY,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC2Q,eAAe,CAAC,eAAe,EAAEJ,UAAU,EAAEgC,MAAM,EAAE,iBAAiB,CAAC;MAClH,CAAC,MAAM;QACL,IAAI,CAACvS,YAAY,CAAC,cAAc,CAAC,GAAG,IAAI,CAACqQ,iBAAiB,CAAC,eAAe,CAAC;MAC7E;MAEA,IAAIsC,IAAI,CAACxM,eAAe,IAAIwM,IAAI,CAACxM,eAAe,CAACzB,MAAM,GAAG,CAAC,EAAE;QAC3D,MAAMoP,eAAe,GAAGnB,IAAI,CAACxM,eAAe,CAACqK,GAAG,CAAEuD,IAAS,KAAM;UAC/DnU,aAAa,EAAEmU,IAAI,CAAC9N,KAAK;UACzBvG,YAAY,EAAEqU,IAAI,CAAC/N;SACpB,CAAC,CAAC;QACH,IAAI,CAAChG,YAAY,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAACoQ,0BAA0B,CAAC0D,eAAe,CAAC;MACzF,CAAC,MAAM;QACL,IAAI,CAAC9T,YAAY,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAACqQ,iBAAiB,CAAC,kBAAkB,CAAC;MACnF;MAEA,IAAIsC,IAAI,CAACjH,iBAAiB,IAAIwE,KAAK,CAACC,OAAO,CAACwC,IAAI,CAACjH,iBAAiB,CAAC,IAAIiH,IAAI,CAACjH,iBAAiB,CAAChH,MAAM,GAAG,CAAC,EAAE;QACxG,MAAMsP,SAAS,GAAGrB,IAAI,CAACjH,iBAAiB,CAAC,CAAC,CAAC;QAC3C,MAAMyG,gBAAgB,GAAG6B,SAAS,CAACC,oBAAoB,IAAI,CAAC;QAC5D,MAAM/B,kBAAkB,GAAG8B,SAAS,CAACE,sBAAsB,IAAI,CAAC;QAEhElG,OAAO,CAACiE,GAAG,CAAC,aAAa,EAAE+B,SAAS,CAAC;QACrChG,OAAO,CAACiE,GAAG,CAAC,oBAAoB,EAAEE,gBAAgB,CAAC;QACnDnE,OAAO,CAACiE,GAAG,CAAC,sBAAsB,EAAEC,kBAAkB,CAAC;QAEvD,IAAIC,gBAAgB,GAAGD,kBAAkB,GAAG,CAAC,EAAE;UAC7C,MAAMiC,eAAe,GAAG,CACtB,CAAC,cAAc,EAAEC,IAAI,CAACC,KAAK,CAAClC,gBAAgB,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,EAC1D,CAAC,gBAAgB,EAAEiC,IAAI,CAACC,KAAK,CAACnC,kBAAkB,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAC/D;UAEDlE,OAAO,CAACiE,GAAG,CAAC,0BAA0B,EAAEkC,eAAe,CAAC;UACxD,IAAI,CAACnU,YAAY,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC4R,cAAc,CAAC,gBAAgB,EAAEuC,eAAe,EAAE,aAAa,CAAC;QAC3G,CAAC,MAAM;UACL,IAAI,CAACnU,YAAY,CAAC,cAAc,CAAC,GAAG,IAAI,CAACqQ,iBAAiB,CAAC,gBAAgB,CAAC;QAC9E;MACF,CAAC,MAAM;QACLrC,OAAO,CAACiE,GAAG,CAAC,8BAA8B,CAAC;QAC3C,IAAI,CAACjS,YAAY,CAAC,cAAc,CAAC,GAAG,IAAI,CAACqQ,iBAAiB,CAAC,gBAAgB,CAAC;MAC9E;IACF;IAEA;IACQM,eAAeA,CAAC2D,KAAa,EAAE/D,UAAoB,EAAEoC,IAAc,EAAE4B,UAAkB;MAC7F;MACA,IAAIC,UAAU,GAAG,IAAI,CAACrL,YAAY,CAACC,OAAO;MAC1C,IAAIkL,KAAK,CAACG,QAAQ,CAAC,eAAe,CAAC,EAAE;QACnCD,UAAU,GAAG,IAAI,CAAC9K,mBAAmB,CAACC,WAAW;MACnD,CAAC,MAAM,IAAI2K,KAAK,CAACG,QAAQ,CAAC,oBAAoB,CAAC,EAAE;QAC/CD,UAAU,GAAG,IAAI,CAAC9K,mBAAmB,CAACG,gBAAgB;MACxD,CAAC,MAAM,IAAIyK,KAAK,CAACG,QAAQ,CAAC,eAAe,CAAC,EAAE;QAC1CD,UAAU,GAAG,IAAI,CAAC9K,mBAAmB,CAACM,YAAY;MACpD,CAAC,MAAM,IAAIsK,KAAK,CAACG,QAAQ,CAAC,eAAe,CAAC,EAAE;QAC1CD,UAAU,GAAG,IAAI,CAAC9K,mBAAmB,CAACW,YAAY;MACpD;MAEA,MAAMqK,YAAY,GAAG,IAAI,CAACC,oBAAoB,EAAE;MAEhD,OAAO;QACL,GAAGD,YAAY;QACfE,KAAK,EAAE;UACL,GAAGF,YAAY,CAACE,KAAK;UACrBC,IAAI,EAAE,MAAM;UACZnK,MAAM,EAAE,IAAI,CAACH,cAAc,CAACG,MAAM;UAClCoK,QAAQ,EAAE,GAAG;UACbC,OAAO,EAAE;YACPC,OAAO,EAAE,IAAI;YACbH,IAAI,EAAE;WACP;UACDI,MAAM,EAAE;SACF;QACRC,KAAK,EAAE;UACL3E,UAAU,EAAEA,UAAU;UACtB4E,MAAM,EAAE;YACNC,QAAQ,EAAE,CAAC,EAAE;YACbC,KAAK,EAAE;cACLC,KAAK,EAAE,SAAS;cAChBC,QAAQ,EAAE,MAAM;cAChB9K,UAAU,EAAE;;WAEf;UACD+K,SAAS,EAAE,SAAS;UACpBC,SAAS,EAAE;SACZ;QACDC,KAAK,EAAE;UACLpB,KAAK,EAAE;YACLqB,IAAI,EAAE,EAAE;YACRN,KAAK,EAAE;cACLC,KAAK,EAAE;;WAEV;UACDH,MAAM,EAAE;YACNE,KAAK,EAAE;cACLC,KAAK,EAAE,SAAS;cAChBC,QAAQ,EAAE,MAAM;cAChB9K,UAAU,EAAE;;WAEf;UACDmL,aAAa,EAAE;SAChB;QACDrD,MAAM,EAAE,CAAC;UACPsD,IAAI,EAAEtB,UAAU;UAChBM,IAAI,EAAE,MAAM;UACZlC,IAAI,EAAEA,IAAI;UACV2C,KAAK,EAAEd,UAAU;UACjBsB,SAAS,EAAE,CAAC;UACZC,MAAM,EAAE;YACNC,SAAS,EAAExB,UAAU;YACrBgB,SAAS,EAAE,SAAS;YACpBM,SAAS,EAAE,CAAC;YACZG,MAAM,EAAE;WACT;UACDC,MAAM,EAAE;YACNZ,KAAK,EAAEd,UAAU;YACjB2B,OAAO,EAAE,GAAG;YACZC,KAAK,EAAE;;SAEV,CAAC;QACFC,WAAW,EAAE;UACXC,IAAI,EAAE;YACJC,UAAU,EAAE;cACVvB,OAAO,EAAE;aACV;YACDe,MAAM,EAAE;cACNf,OAAO,EAAE;;;;OAIhB;IACH;IAEQ9D,iBAAiBA,CAACoD,KAAa,EAAE/D,UAAoB,EAAEoC,IAAc,EAAE4B,UAAkB,EAAEiC,SAAA,GAAoB,SAAS;MAC9H;MACA,IAAIlB,KAAK,GAAG,IAAI,CAAC5L,mBAAmB,CAACE,WAAW,CAAC,CAAC;MAElD,QAAQ4M,SAAS;QACf,KAAK,WAAW;UACdlB,KAAK,GAAG,IAAI,CAAC5L,mBAAmB,CAACI,YAAY,CAAC,CAAC;UAC/C;QACF,KAAK,WAAW;UACdwL,KAAK,GAAG,IAAI,CAAC5L,mBAAmB,CAACQ,SAAS,CAAC,CAAC;UAC5C;QACF,KAAK,cAAc;UACjBoL,KAAK,GAAG,IAAI,CAAC5L,mBAAmB,CAACS,YAAY,CAAC,CAAC;UAC/C;QACF,KAAK,QAAQ;UACXmL,KAAK,GAAG,IAAI,CAAC5L,mBAAmB,CAACE,WAAW,CAAC,CAAC;UAC9C;QACF;UACE0L,KAAK,GAAG,IAAI,CAAC5L,mBAAmB,CAACE,WAAW;QAAE;MAClD;MAEA,MAAM8K,YAAY,GAAG,IAAI,CAACC,oBAAoB,EAAE;MAEhD,OAAO;QACL,GAAGD,YAAY;QACfE,KAAK,EAAE;UACL,GAAGF,YAAY,CAACE,KAAK;UACrBC,IAAI,EAAE,QAAQ;UACdnK,MAAM,EAAE,IAAI,CAACH,cAAc,CAACG,MAAM;UAClCoK,QAAQ,EAAE,GAAG;UACbC,OAAO,EAAE;YACPC,OAAO,EAAE,IAAI;YACbH,IAAI,EAAE;WACP;UACDI,MAAM,EAAE;SACF;QACRC,KAAK,EAAE;UACL3E,UAAU,EAAEA,UAAU;UACtB4E,MAAM,EAAE;YACNE,KAAK,EAAE;cACLC,KAAK,EAAE,SAAS;cAChBC,QAAQ,EAAE,MAAM;cAChB9K,UAAU,EAAE;;WAEf;UACD+K,SAAS,EAAE,SAAS;UACpBC,SAAS,EAAE;SACZ;QACDC,KAAK,EAAE;UACLpB,KAAK,EAAE;YACLqB,IAAI,EAAE,EAAE;YACRN,KAAK,EAAE;cACLC,KAAK,EAAE;;WAEV;UACDH,MAAM,EAAE;YACNE,KAAK,EAAE;cACLC,KAAK,EAAE,SAAS;cAChBC,QAAQ,EAAE,MAAM;cAChB9K,UAAU,EAAE;;WAEf;UACDmL,aAAa,EAAE;SAChB;QACDrD,MAAM,EAAE,CAAC;UACPsD,IAAI,EAAEtB,UAAU;UAChBM,IAAI,EAAE,QAAQ;UACdlC,IAAI,EAAEA,IAAI;UACV2C,KAAK,EAAEA,KAAK;UACZmB,WAAW,EAAE,CAAC;UACdC,YAAY,EAAE,CAAC;UACfR,MAAM,EAAE;YACNZ,KAAK,EAAEA,KAAK;YACZa,OAAO,EAAE,GAAG;YACZC,KAAK,EAAE;;SAEV,CAAC;QACFC,WAAW,EAAE;UACXM,MAAM,EAAE;YACNJ,UAAU,EAAE;cACVvB,OAAO,EAAE,IAAI;cACbK,KAAK,EAAE;gBACLC,KAAK,EAAE,SAAS;gBAAO;gBACvBC,QAAQ,EAAE,MAAM;gBAChBqB,UAAU,EAAE,KAAK;gBACjBC,WAAW,EAAE,cAAc,CAAC;;aAE/B;YACDH,YAAY,EAAE,CAAC;YACfI,YAAY,EAAE,GAAG;YAAK;YACtBC,YAAY,EAAE,IAAI;YAAI;YACtBC,aAAa,EAAE,EAAE,CAAK;;;OAG3B;IACH;IAEQpF,cAAcA,CAAC0C,KAAa,EAAE3B,IAAW,EAAE6D,SAAA,GAAoB,SAAS;MAC9E;MACA,IAAIS,MAAM,GAAa,EAAE;MAEzB,IAAIT,SAAS,KAAK,cAAc,EAAE;QAChCS,MAAM,GAAG,IAAI,CAACvN,mBAAmB,CAACK,YAAY,CAAC,CAAC;MAClD,CAAC,MAAM,IAAIyM,SAAS,KAAK,aAAa,EAAE;QACtCS,MAAM,GAAG,IAAI,CAACvN,mBAAmB,CAACzG,YAAY,CAAC,CAAC;MAClD,CAAC,MAAM,IAAIuT,SAAS,KAAK,aAAa,EAAE;QACtCS,MAAM,GAAG,IAAI,CAACvN,mBAAmB,CAACO,WAAW,CAAC,CAAC;MACjD,CAAC,MAAM,IAAIuM,SAAS,KAAK,SAAS,EAAE;QAClCS,MAAM,GAAG,IAAI,CAACvN,mBAAmB,CAACU,aAAa,CAAC,CAAC;MACnD,CAAC,MAAM;QACL6M,MAAM,GAAG,CAAC,IAAI,CAAC9N,YAAY,CAACC,OAAO,EAAE,IAAI,CAACD,YAAY,CAACE,SAAS,CAAC,CAAC,CAAC;MACrE;MAEA,MAAMqL,YAAY,GAAG,IAAI,CAACC,oBAAoB,EAAE;MAEhD,OAAO;QACL,GAAGD,YAAY;QACfE,KAAK,EAAE;UACL,GAAGF,YAAY,CAACE,KAAK;UACrBC,IAAI,EAAE,KAAK;UACXnK,MAAM,EAAE,IAAI,CAACH,cAAc,CAACG;SAC7B;QACD6H,MAAM,EAAE,CAAC;UACPsD,IAAI,EAAE,YAAY;UAClBhB,IAAI,EAAE,KAAK;UACXlC,IAAI,EAAEA,IAAI;UACVuE,SAAS,EAAE,KAAK;UAChBD,MAAM,EAAEA,MAAM;UACdV,UAAU,EAAE;YACVvB,OAAO,EAAE;;SAEZ,CAAC;QACFmC,MAAM,EAAE;UACNnC,OAAO,EAAE,IAAI;UACboC,KAAK,EAAE,OAAO;UACdC,aAAa,EAAE,QAAQ;UACvBC,MAAM,EAAE,UAAU;UAClBC,SAAS,EAAE;YACTjC,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE,MAAM;YAChB9K,UAAU,EAAE,mBAAmB;YAC/BmM,UAAU,EAAE;WACb;UACDY,YAAY,EAAE,CAAC;UACfC,gBAAgB,EAAE;SACnB;QACDpB,WAAW,EAAE;UACXqB,GAAG,EAAE;YACHC,gBAAgB,EAAE,KAAK;YACvBC,MAAM,EAAE,SAAS;YACjBrB,UAAU,EAAE;cACVvB,OAAO,EAAE;aACV;YACD6C,YAAY,EAAE,IAAI;YAClBpB,WAAW,EAAE;;;OAGlB;IACH;IAEQrG,0BAA0BA,CAACuC,IAAW;MAC5C,MAAMpC,UAAU,GAAGoC,IAAI,CAACnC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAAC7Q,aAAa,IAAI6Q,IAAI,CAACxK,KAAK,IAAIwK,IAAI,CAACqH,QAAQ,IAAI,cAAc,CAAC;MACxG,MAAM7K,MAAM,GAAG0F,IAAI,CAACnC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAAC/Q,YAAY,IAAI+Q,IAAI,CAACrL,UAAU,IAAIqL,IAAI,CAACC,KAAK,IAAI,CAAC,CAAC;MAExF;MACA,MAAMqH,WAAW,GAAG,IAAI,CAACxN,cAAc,CAACG,MAAM;MAC9C,MAAMsN,aAAa,GAAGzH,UAAU,CAAC7L,MAAM,GAAG,CAAC,CAAC,CAAC;MAE7C,OAAO;QACLkQ,KAAK,EAAE;UACLC,IAAI,EAAE,KAAK;UACXrK,eAAe,EAAE,IAAI,CAACD,cAAc,CAACC,eAAe;UACpDE,MAAM,EAAEqN,WAAW;UACnBE,UAAU,EAAE,GAAG;UAAE;UACjBC,WAAW,EAAE,EAAE;UAAE;UACjBC,kBAAkB,EAAEH,aAAa,GAAG;YAClCI,SAAS,EAAE7H,UAAU,CAAC7L,MAAM,GAAG,EAAE,GAAG,EAAE;YAAE;YACxC2T,eAAe,EAAE;WAClB,GAAGC,SAAS;UACbjD,KAAK,EAAE;YACL5K,UAAU,EAAE,IAAI,CAACF,cAAc,CAACE;;SAE5B;QACR6J,KAAK,EAAE;UACLqB,IAAI,EAAE,EAAE;UACRN,KAAK,EAAE;YACLkD,OAAO,EAAE;;SAEZ;QACDrD,KAAK,EAAE;UACL3E,UAAU,EAAEA,UAAU;UACtB4E,MAAM,EAAE;YACNE,KAAK,EAAE;cACLC,KAAK,EAAE,SAAS;cAChBC,QAAQ,EAAE,MAAM;cAChBqB,UAAU,EAAE,KAAK;cACjBnM,UAAU,EAAE;aACb;YACD+N,QAAQ,EAAE,OAAO;YACjBC,IAAI,EAAE,CAAC;YACPC,OAAO,EAAE,IAAI;YACbC,SAAS,EAAE,SAAAA,CAAA;cACT;cACA,MAAM1S,KAAK,GAAG,IAAI,CAAC6H,KAAe;cAClC,OAAO,gHAAgH7H,KAAK,QAAQ;YACtI;WACD;UACDuP,SAAS,EAAE,aAAa;UACxBC,SAAS,EAAE,aAAa;UACxBmD,GAAG,EAAE,CAAC;UACNC,GAAG,EAAEb,aAAa,GAAG5D,IAAI,CAACwE,GAAG,CAAC,EAAE,EAAErI,UAAU,CAAC7L,MAAM,GAAG,CAAC,CAAC,GAAG4T,SAAS,CAAC;SACtE;QACD5C,KAAK,EAAE;UACLpB,KAAK,EAAE;YACLqB,IAAI,EAAE,EAAE;YACRN,KAAK,EAAE;cACLC,KAAK,EAAE;;WAEV;UACDH,MAAM,EAAE;YACNE,KAAK,EAAE;cACLC,KAAK,EAAE,SAAS;cAChBC,QAAQ,EAAE,MAAM;cAChB9K,UAAU,EAAE;;WAEf;UACDmL,aAAa,EAAE,aAAa;UAC5BJ,SAAS,EAAE;SACZ;QACDjD,MAAM,EAAE,CAAC;UACPsD,IAAI,EAAE,OAAO;UACbhB,IAAI,EAAE,KAAK;UACXlC,IAAI,EAAE1F,MAAM;UACZqI,KAAK,EAAE,SAAS;UAChBmB,WAAW,EAAE,CAAC;UACdC,YAAY,EAAE;SACf,CAAC;QACFS,MAAM,EAAE;UACNnC,OAAO,EAAE;SACV;QACDqB,WAAW,EAAE;UACXyC,GAAG,EAAE;YACHvC,UAAU,EAAE;cACVvB,OAAO,EAAE,IAAI;cACb+D,MAAM,EAAE,KAAK;cACb3B,KAAK,EAAE,OAAO;cACd4B,CAAC,EAAE,CAAC;cAAE;cACN3D,KAAK,EAAE;gBACLC,KAAK,EAAE,SAAS;gBAChBC,QAAQ,EAAE,MAAM;gBAChBqB,UAAU,EAAE,KAAK;gBACjBnM,UAAU,EAAE,oBAAoB;gBAChCoM,WAAW,EAAE;eACd;cACD8B,SAAS,EAAE,SAAAA,CAAA;gBACT,OAAO,IAAI,CAACM,CAAC;cACf;aACD;YACD3D,KAAK,EAAE,SAAS;YAChBoB,YAAY,EAAE,CAAC;YACfI,YAAY,EAAE,IAAI;YAAE;YACpBC,YAAY,EAAE,IAAI;YAAE;YACpBC,aAAa,EAAE,EAAE,CAAG;;SAEvB;QACDkC,OAAO,EAAE;UACPlE,OAAO,EAAE,IAAI;UACb2D,SAAS,EAAE,SAAAA,CAAA;YACT,OAAO,KAAK,GAAG,IAAI,CAACK,CAAC,GAAG,qBAAqB,GAAG,IAAI,CAACC,CAAC;UACxD;SACD;QACDE,UAAU,EAAE;UACVC,aAAa,EAAE;YACbpE,OAAO,EAAE,IAAI;YACbqE,KAAK,EAAE;cACLC,MAAM,EAAE,SAAS;cACjBC,CAAC,EAAE,CAAC;cACJC,MAAM,EAAE;gBACNC,KAAK,EAAE;kBACLC,IAAI,EAAE,SAAS;kBACfJ,MAAM,EAAE;iBACT;gBACDK,MAAM,EAAE;kBACND,IAAI,EAAE,SAAS;kBACfJ,MAAM,EAAE;;;;;SAKjB;QACDM,SAAS,EAAE;UACT5E,OAAO,EAAE;SACV;QACD6E,OAAO,EAAE;UACP7E,OAAO,EAAE;;OAEZ;IACH;IAEQ3E,iBAAiBA,CAACiE,KAAa;MACrC,OAAO;QACLM,KAAK,EAAE;UACLpK,eAAe,EAAE;SAClB;QACD8J,KAAK,EAAE;UACLqB,IAAI,EAAErB,KAAK;UACX8C,KAAK,EAAE,MAAM;UACb/B,KAAK,EAAE;YACLC,KAAK,EAAE,mBAAmB;YAC1BC,QAAQ,EAAE,MAAM;YAChBqB,UAAU,EAAE;;SAEf;QACDrE,MAAM,EAAE,EAAE;QACVuH,IAAI,EAAE;UACJC,MAAM,EAAE;SACT;QACDA,MAAM,EAAE;UACN1E,KAAK,EAAE;YACLuB,UAAU,EAAE,MAAM;YAClBrB,QAAQ,EAAE,MAAM;YAChBD,KAAK,EAAE;;SAEV;QACDsE,SAAS,EAAE;UACT5E,OAAO,EAAE;SACV;QACD6E,OAAO,EAAE;UACP7E,OAAO,EAAE;;OAEZ;IACH;IAEA;IACQgF,uBAAuBA,CAC7BrH,IAA4B,EAC5BsH,YAA+C,EAC/C3F,KAAa;MAEb,IAAI3B,IAAI,IAAIA,IAAI,CAACjO,MAAM,GAAG,CAAC,EAAE;QAC3B,OAAOuV,YAAY,CAACtH,IAAI,CAAC;MAC3B,CAAC,MAAM;QACL,OAAO,IAAI,CAACtC,iBAAiB,CAACiE,KAAK,CAAC;MACtC;IACF;IAEA;IACQK,oBAAoBA,CAAA;MAC1B,OAAO;QACLC,KAAK,EAAE;UACLpK,eAAe,EAAE;SAClB;QACD8J,KAAK,EAAE;UACLqB,IAAI,EAAE,EAAE;UACRN,KAAK,EAAE;YACLkD,OAAO,EAAE;;SAEZ;QACDqB,SAAS,EAAE;UACT5E,OAAO,EAAE;SACV;QACD6E,OAAO,EAAE;UACP7E,OAAO,EAAE;SACV;QACDmC,MAAM,EAAE;UACNnC,OAAO,EAAE;;OAEZ;IACH;IAEA;IACAkF,aAAa,GAAGA,CAACtF,KAAuB,EAAEuF,QAAgB,KAAU;MAClE,IAAI,CAAC3O,SAAS,CAAC2O,QAAQ,CAAC,GAAGvF,KAAK;IAClC,CAAC;IAED;IACAzV,aAAaA,CAACib,aAAqB;MACjC,MAAMvL,kBAAkB,GAAG,IAAI,CAACD,2BAA2B,EAAE;MAC7D,IAAI,CAAC5F,gBAAgB,CAACqR,kBAAkB,CAACxL,kBAAkB,EAAEuL,aAAa,EAAE,OAAO,CAAC;IACtF;IAEAnV,kBAAkBA,CAACmV,aAAqB;MACtC,MAAMvL,kBAAkB,GAAG,IAAI,CAACD,2BAA2B,EAAE;MAC7D,IAAI,CAAC5F,gBAAgB,CAACsR,sBAAsB,CAACzL,kBAAkB,EAAEuL,aAAa,EAAE,OAAO,CAAC;IAC1F;IAEAG,YAAYA,CAAA;MACV,MAAM1L,kBAAkB,GAAG,IAAI,CAACD,2BAA2B,EAAE;MAC7D,IAAI,CAAC5F,gBAAgB,CAACuR,YAAY,CAAC1L,kBAAkB,CAAC;IACxD;IAEA;IACA2L,yBAAyBA,CAACC,KAAU;MAClC,IAAI,CAAC5O,gBAAgB,GAAG4O,KAAK,CAAC3M,KAAK;MACnC,IAAI,CAAC7B,gBAAgB,GAAG,IAAI,CAACJ,gBAAgB,CAACnH,MAAM,GAAG,CAAC;IAC1D;IAEAgW,UAAUA,CAAA;MACR;MACA,IAAI,CAAC5Z,WAAW,EAAE;IACpB;IAEA6Z,qBAAqBA,CAACF,KAAY;MAChCA,KAAK,CAACG,eAAe,EAAE;MACvB,IAAI,CAAClY,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;IACtD;IAEAmY,mBAAmBA,CAAA;MACjB;MACA7M,OAAO,CAACiE,GAAG,CAAC,8BAA8B,CAAC;MAC3C,IAAI,CAACvP,mBAAmB,GAAG,KAAK;IAClC;IAEA;IACQiR,wBAAwBA,CAACW,KAAa,EAAE/D,UAAoB,EAAEoC,IAAc,EAAE4B,UAAkB,EAAEiC,SAAA,GAAoB,SAAS;MACrI;MACA,IAAIlB,KAAK,GAAG,IAAI,CAAC5L,mBAAmB,CAACnE,SAAS,CAAC,CAAC;MAEhD,IAAIiR,SAAS,KAAK,WAAW,EAAE;QAC7BlB,KAAK,GAAG,IAAI,CAAC5L,mBAAmB,CAACnE,SAAS,CAAC,CAAC;MAC9C,CAAC,MAAM,IAAIiR,SAAS,KAAK,iBAAiB,EAAE;QAC1ClB,KAAK,GAAG,IAAI,CAAC5L,mBAAmB,CAACY,4BAA4B,CAAC,CAAC;MACjE;MAEA;MACA,MAAMyN,WAAW,GAAG,IAAI,CAACxN,cAAc,CAACG,MAAM;MAC9C,MAAMsN,aAAa,GAAGzH,UAAU,CAAC7L,MAAM,GAAG,CAAC,CAAC,CAAC;MAE7C,MAAMgQ,YAAY,GAAG,IAAI,CAACC,oBAAoB,EAAE;MAEhD,OAAO;QACL,GAAGD,YAAY;QACfE,KAAK,EAAE;UACL,GAAGF,YAAY,CAACE,KAAK;UACrBC,IAAI,EAAE,KAAK;UACXnK,MAAM,EAAEqN,WAAW;UACnBE,UAAU,EAAE,GAAG;UAAE;UACjBC,WAAW,EAAE,EAAE;UAAE;UACjBC,kBAAkB,EAAEH,aAAa,GAAG;YAClCI,SAAS,EAAE7H,UAAU,CAAC7L,MAAM,GAAG,EAAE,GAAG,EAAE;YAAE;YACxC2T,eAAe,EAAE;WAClB,GAAGC,SAAS;UACbxD,QAAQ,EAAE,GAAG;UACbC,OAAO,EAAE;YACPC,OAAO,EAAE,IAAI;YACbH,IAAI,EAAE;WACP;UACDI,MAAM,EAAE;SACF;QACRC,KAAK,EAAE;UACL3E,UAAU,EAAEA,UAAU;UACtB+D,KAAK,EAAE;YACLqB,IAAI,EAAE;WACP;UACDR,MAAM,EAAE;YACNE,KAAK,EAAE;cACLC,KAAK,EAAE,SAAS;cAChBC,QAAQ,EAAE,MAAM;cAChBqB,UAAU,EAAE,KAAK;cACjBnM,UAAU,EAAE;aACb;YACD+N,QAAQ,EAAE,OAAO;YACjBC,IAAI,EAAE,CAAC;YACPC,OAAO,EAAE,IAAI;YACbC,SAAS,EAAE,SAAAA,CAAA;cACT;cACA,MAAM9C,IAAI,GAAG,IAAI,CAAC/H,KAAe;cACjC,OAAO,gHAAgH+H,IAAI,QAAQ;YACrI;WACD;UACDL,SAAS,EAAE,aAAa;UACxBC,SAAS,EAAE,aAAa;UACxBmD,GAAG,EAAE,CAAC;UACNC,GAAG,EAAEb,aAAa,GAAG5D,IAAI,CAACwE,GAAG,CAAC,EAAE,EAAErI,UAAU,CAAC7L,MAAM,GAAG,CAAC,CAAC,GAAG4T,SAAS,CAAC;SACtE;QACD5C,KAAK,EAAE;UACLkD,GAAG,EAAE,CAAC;UACNtE,KAAK,EAAE;YACLqB,IAAI,EAAE,EAAE;YACRN,KAAK,EAAE;cACLC,KAAK,EAAE;;WAEV;UACDH,MAAM,EAAE;YACNE,KAAK,EAAE;cACLC,KAAK,EAAE,SAAS;cAChBC,QAAQ,EAAE,MAAM;cAChB9K,UAAU,EAAE;;WAEf;UACDmL,aAAa,EAAE,aAAa;UAC5BJ,SAAS,EAAE;SACZ;QACDa,WAAW,EAAE;UACXyC,GAAG,EAAE;YACHvC,UAAU,EAAE;cACVvB,OAAO,EAAE,IAAI;cACb+D,MAAM,EAAE,KAAK;cACb3B,KAAK,EAAE,OAAO;cACd4B,CAAC,EAAE,CAAC;cAAE;cACN3D,KAAK,EAAE;gBACLC,KAAK,EAAE,SAAS;gBAChBC,QAAQ,EAAE,MAAM;gBAChBqB,UAAU,EAAE,KAAK;gBACjBnM,UAAU,EAAE,oBAAoB;gBAChCoM,WAAW,EAAE;eACd;cACD8B,SAAS,EAAE,SAAAA,CAAA;gBACT,OAAO,IAAI,CAACM,CAAC;cACf;aACD;YACD3D,KAAK,EAAEA,KAAK;YACZoB,YAAY,EAAE,CAAC;YACfI,YAAY,EAAE,IAAI;YAAE;YACpBC,YAAY,EAAE,IAAI;YAAE;YACpBC,aAAa,EAAE,EAAE;YAAI;YACrBP,WAAW,EAAE;;SAEhB;QACDlE,MAAM,EAAE,CAAC;UACPsC,IAAI,EAAE,KAAK;UACXgB,IAAI,EAAEtB,UAAU;UAChB5B,IAAI,EAAEA,IAAI;UACV2C,KAAK,EAAEA;SACR,CAAQ;QACT6B,MAAM,EAAE;UACNnC,OAAO,EAAE;SACV;QACDkE,OAAO,EAAE;UACPlE,OAAO,EAAE,IAAI;UACb2D,SAAS,EAAE,SAAAA,CAAA;YACT,OAAO,KAAK,GAAG,IAAI,CAACK,CAAC,GAAG,WAAW,GAAGzE,UAAU,GAAG,IAAI,GAAG,IAAI,CAAC0E,CAAC;UAClE;;OAEH;IACH;IAEA;IACQ6B,kBAAkBA,CAACjG,IAAuC,EAAEnK,MAAA,GAAiB,IAAI,CAACH,cAAc,CAACG,MAAM;MAC7G,OAAO;QACLkK,KAAK,EAAE;UACLC,IAAI;UACJrK,eAAe,EAAE,IAAI,CAACD,cAAc,CAACC,eAAe;UACpDE,MAAM;UACN2K,KAAK,EAAE;YACL5K,UAAU,EAAE,IAAI,CAACF,cAAc,CAACE;;SAEnC;QACD6J,KAAK,EAAE;UACLqB,IAAI,EAAE,EAAE;UACRN,KAAK,EAAE;YAAEkD,OAAO,EAAE;UAAM;SACzB;QACDsB,OAAO,EAAE;UAAE7E,OAAO,EAAE;QAAK,CAAE;QAC3BmC,MAAM,EAAE;UAAEnC,OAAO,EAAE;QAAK;OACzB;IACH;IAEA;IACAxV,WAAWA,CAACsO,KAAa,EAAEiN,WAAA,GAAuB,KAAK;MACrD,IAAIA,WAAW,EAAE;QACf,MAAMC,QAAQ,GAAG5G,IAAI,CAACyE,GAAG,CAAC,GAAG,IAAI,CAACtT,SAAS,CAACiL,GAAG,CAACkD,IAAI,IAAIA,IAAI,CAACtO,UAAU,CAAC,CAAC;QACzE,MAAM6V,eAAe,GAAGD,QAAQ,GAAG,CAAC,GAAIlN,KAAK,GAAGkN,QAAQ,GAAI,GAAG,GAAG,CAAC;QACnE;QACA,OAAO5G,IAAI,CAACyE,GAAG,CAACoC,eAAe,EAAE,EAAE,CAAC;MACtC,CAAC,MAAM;QACL;QACA,IAAID,QAAQ,GAAG,CAAC;QAEhB,IAAI,IAAI,CAAChQ,SAAS,KAAK,SAAS,IAAI,IAAI,CAACjL,eAAe,IAAI,IAAI,CAACA,eAAe,CAAC2E,MAAM,GAAG,CAAC,EAAE;UAC3F;UACAsW,QAAQ,GAAG5G,IAAI,CAACyE,GAAG,CAAC,GAAG,IAAI,CAAC9Y,eAAe,CAACyQ,GAAG,CAACuD,IAAI,IAAIA,IAAI,CAACrU,YAAY,IAAI,CAAC,CAAC,CAAC;QAClF,CAAC,MAAM,IAAI,IAAI,CAACsL,SAAS,KAAK,QAAQ,IAAI,IAAI,CAAC7E,eAAe,IAAI,IAAI,CAACA,eAAe,CAACzB,MAAM,GAAG,CAAC,EAAE;UACjG;UACAsW,QAAQ,GAAG5G,IAAI,CAACyE,GAAG,CAAC,GAAG,IAAI,CAAC1S,eAAe,CAACqK,GAAG,CAACuD,IAAI,IAAIA,IAAI,CAAC/N,gBAAgB,IAAI+N,IAAI,CAACrU,YAAY,IAAI,CAAC,CAAC,CAAC;QAC3G;QAEA,MAAMub,eAAe,GAAGD,QAAQ,GAAG,CAAC,GAAIlN,KAAK,GAAGkN,QAAQ,GAAI,GAAG,GAAG,CAAC;QACnE;QACA,OAAO5G,IAAI,CAACyE,GAAG,CAACoC,eAAe,EAAE,EAAE,CAAC;MACtC;IACF;IAIA;IACO9a,YAAYA,CAAA;MACjB+a,MAAM,CAACC,IAAI,CAAC,IAAI,CAACjP,WAAW,EAAE,QAAQ,CAAC;IACzC;IAEOkP,WAAWA,CAAA;MAChBF,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC/O,eAAe,EAAE,QAAQ,CAAC;IAC7C;IAEO/L,gBAAgBA,CAAA;MACrB6a,MAAM,CAACC,IAAI,CAAC,sCAAsC,EAAE,QAAQ,CAAC;IAC/D;IAEOva,eAAeA,CAAC6Z,KAAU;MAC/B,IAAIA,KAAK,IAAIA,KAAK,CAACY,SAAS,IAAIZ,KAAK,CAACa,OAAO,EAAE;QAC7C;QACA,MAAMD,SAAS,GAAG,IAAInN,IAAI,CAACuM,KAAK,CAACY,SAAS,CAAC;QAC3C,MAAMC,OAAO,GAAG,IAAIpN,IAAI,CAACuM,KAAK,CAACa,OAAO,CAAC;QAEvC,IAAI,CAAC3Q,SAAS,CAACC,QAAQ,GAAGyQ,SAAS,CAAChN,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC/D,IAAI,CAAC3D,SAAS,CAACG,MAAM,GAAGwQ,OAAO,CAACjN,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAE3D;QACA,IAAI,CAAC7L,IAAI,CAAC8Y,UAAU,CAAC;UACnB3Q,QAAQ,EAAE,IAAI,CAACD,SAAS,CAACC,QAAQ;UACjCE,MAAM,EAAE,IAAI,CAACH,SAAS,CAACG;SACxB,CAAC;QAEF;QACAkD,OAAO,CAACiE,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACtH,SAAS,CAAC;MACrD;IACF;IAEA6Q,qBAAqBA,CAAA;MACnB,IAAI,CAAC,IAAI,CAAC7Q,SAAS,CAACC,QAAQ,IAAI,CAAC,IAAI,CAACD,SAAS,CAACG,MAAM,EAAE;QACtD,OAAO,mBAAmB;MAC5B;MAEA,MAAMF,QAAQ,GAAG,IAAIsD,IAAI,CAAC,IAAI,CAACvD,SAAS,CAACC,QAAQ,CAAC;MAClD,MAAME,MAAM,GAAG,IAAIoD,IAAI,CAAC,IAAI,CAACvD,SAAS,CAACG,MAAM,CAAC;MAE9C,MAAM2Q,OAAO,GAA+B;QAC1ChN,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE;OACN;MAED,OAAO,GAAG/D,QAAQ,CAAC8Q,kBAAkB,CAAC,OAAO,EAAED,OAAO,CAAC,OAAO3Q,MAAM,CAAC4Q,kBAAkB,CAAC,OAAO,EAAED,OAAO,CAAC,EAAE;IAC7G;IAEA9Y,oBAAoBA,CAAA;MAClB,IAAI,CAAC,IAAI,CAACgI,SAAS,CAACC,QAAQ,IAAI,CAAC,IAAI,CAACD,SAAS,CAACG,MAAM,EAAE;QACtD,OAAO;UAAE6Q,KAAK,EAAE,IAAI;UAAEC,GAAG,EAAE;QAAI,CAAE;MACnC;MAEA,OAAO;QACLD,KAAK,EAAE,IAAIzN,IAAI,CAAC,IAAI,CAACvD,SAAS,CAACC,QAAQ,CAAC;QACxCgR,GAAG,EAAE,IAAI1N,IAAI,CAAC,IAAI,CAACvD,SAAS,CAACG,MAAM;OACpC;IACH;IAGA+Q,eAAeA,CAACpB,KAAY;MAC1B,MAAMqB,MAAM,GAAGrB,KAAK,CAACqB,MAAqB;MAC1C,MAAMC,cAAc,GAAGD,MAAM,CAACE,OAAO,CAAC,kBAAkB,CAAC;MACzD,MAAMC,gBAAgB,GAAGH,MAAM,CAACE,OAAO,CAAC,oBAAoB,CAAC;MAE7D,IAAI,CAACD,cAAc,IAAI,CAACE,gBAAgB,EAAE;QACxC,IAAI,CAACvZ,mBAAmB,GAAG,KAAK;MAClC;IACF;IAEOlC,gBAAgBA,CAACia,KAAU;MAChCA,KAAK,CAACG,eAAe,EAAE;MACvB,IAAI,CAAClY,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;MACpDsL,OAAO,CAACiE,GAAG,CAAC,+CAA+C,EAAE,IAAI,CAACvP,mBAAmB,CAAC;IACxF;IAEO/D,aAAaA,CAAA;MAClB,IAAI,CAACkc,mBAAmB,EAAE;MAC1B,IAAI,CAACnY,mBAAmB,GAAG,KAAK;IAClC;IAEO1D,cAAcA,CAAA;MACnB,IAAI,IAAI,CAACgM,SAAS,KAAK,SAAS,EAAE;QAChC,IAAI,CAACuP,YAAY,EAAE;MACrB,CAAC,MAAM;QACL,IAAI,CAACtV,kBAAkB,CAAC,gBAAgB,CAAC;MAC3C;MACA,IAAI,CAACvC,mBAAmB,GAAG,KAAK;IAClC;;uCAtvCWqG,kBAAkB,EAAA7K,EAAA,CAAAge,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAle,EAAA,CAAAge,iBAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;;YAAlBvT,kBAAkB;MAAAwT,SAAA;MAAAC,YAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAlBxe,EAAA,CAAAE,UAAA,mBAAAwe,4CAAArc,MAAA;YAAA,OAAAoc,GAAA,CAAAd,eAAA,CAAAtb,MAAA,CAAuB;UAAA,UAAArC,EAAA,CAAA2e,iBAAA,CAAL;;;;;;;;UC5DvB3e,EAFR,CAAAC,cAAA,aAAiC,aACC,SACtB;UAAAD,EAAA,CAAAW,MAAA,sBAAe;UACvBX,EADuB,CAAAY,YAAA,EAAK,EACtB;UAIFZ,EADJ,CAAAC,cAAA,aAA4B,oBAEyD;UAAhED,EAAA,CAAAE,UAAA,uBAAA0e,4DAAA;YAAA,OAAaH,GAAA,CAAAxP,WAAA,CAAY;cAAAjC,EAAA,EAAK,SAAS;cAAAC,KAAA,EAAS;YAAY,CAAC,CAAC;UAAA,EAAC;UAChFjN,EAAA,CAAAY,YAAA,EAAa;UACbZ,EAAA,CAAAC,cAAA,oBACmF;UAAlED,EAAA,CAAAE,UAAA,uBAAA2e,4DAAA;YAAA,OAAaJ,GAAA,CAAAxP,WAAA,CAAY;cAAAjC,EAAA,EAAK,QAAQ;cAAAC,KAAA,EAAS;YAAe,CAAC,CAAC;UAAA,EAAC;UAEtFjN,EADI,CAAAY,YAAA,EAAa,EACX;UAINZ,EAAA,CAAAC,cAAA,aAA+B;UA6O3BD,EAxOA,CAAAkB,UAAA,IAAA4d,iCAAA,mBAAyD,IAAAC,iCAAA,mBAwOD;UAoThE/e,EADI,CAAAY,YAAA,EAAM,EACJ;;;UA3iBiCZ,EAAA,CAAAoB,SAAA,GAA6D;UAACpB,EAA9D,CAAA2B,UAAA,YAAA8c,GAAA,CAAA3R,SAAA,yCAA6D,cAAc;UAGxE9M,EAAA,CAAAoB,SAAA,EAA4D;UAACpB,EAA7D,CAAA2B,UAAA,YAAA8c,GAAA,CAAA3R,SAAA,wCAA4D,cAAc;UAYtG9M,EAAA,CAAAoB,SAAA,GAA6B;UAA7BpB,EAAA,CAAA2B,UAAA,SAAA8c,GAAA,CAAA3R,SAAA,eAA6B;UAwO7B9M,EAAA,CAAAoB,SAAA,EAA4B;UAA5BpB,EAAA,CAAA2B,UAAA,SAAA8c,GAAA,CAAA3R,SAAA,cAA4B;;;qBDhNtClO,YAAY,EAAAogB,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZrgB,WAAW,EAAAsf,EAAA,CAAAgB,oBAAA,EACXrgB,mBAAmB,EAAAqf,EAAA,CAAAiB,kBAAA,EACnBjgB,qBAAqB,EAAAkgB,EAAA,CAAAC,wBAAA,EACrB/f,eAAe,EACfC,kBAAkB,EAClBC,eAAe,EACfC,cAAc,EACdC,eAAe,EACfC,iBAAiB,EACjBC,iBAAiB,EACjBC,aAAa;MAAAyf,MAAA;IAAA;;SAKJ1U,kBAAkB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}