{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { IconComponent, ButtonComponent } from \"@ava/play-comp-library\";\nimport { FilterTabsComponent } from '../filter-tabs/filter-tabs.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@shared/auth/services/token-storage.service\";\nimport * as i2 from \"../../service/global-store.service\";\nimport * as i3 from \"@angular/common\";\nfunction MyAgentHomeComponent_div_17_ava_icon_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ava-icon\", 30);\n  }\n}\nfunction MyAgentHomeComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"div\", 21)(2, \"div\", 22)(3, \"div\", 23);\n    i0.ɵɵelement(4, \"ava-icon\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 25)(6, \"h3\", 26);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 27);\n    i0.ɵɵtemplate(9, MyAgentHomeComponent_div_17_ava_icon_9_Template, 1, 0, \"ava-icon\", 28);\n    i0.ɵɵelementStart(10, \"p\", 29);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const card_r1 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"iconName\", card_r1.icon)(\"iconColor\", card_r1.iconColor);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(card_r1.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", card_r1.title === \"Average Agents Rating\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(card_r1.value);\n  }\n}\nfunction MyAgentHomeComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 32)(2, \"div\", 33)(3, \"div\", 34);\n    i0.ɵɵelement(4, \"ava-icon\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 36)(6, \"div\", 37)(7, \"h3\", 38);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 39);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"p\", 40);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 41)(14, \"span\", 42);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 43)(17, \"ava-button\", 44);\n    i0.ɵɵlistener(\"click\", function MyAgentHomeComponent_div_24_Template_ava_button_click_17_listener() {\n      const agent_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onEditAgent(agent_r3));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"ava-button\", 45);\n    i0.ɵɵlistener(\"click\", function MyAgentHomeComponent_div_24_Template_ava_button_click_18_listener() {\n      const agent_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onViewAgent(agent_r3));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"ava-button\", 46);\n    i0.ɵɵlistener(\"click\", function MyAgentHomeComponent_div_24_Template_ava_button_click_19_listener() {\n      const agent_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onDeleteAgent(agent_r3));\n    });\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const agent_r3 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(agent_r3.title);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.getStatusClass(agent_r3.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", agent_r3.status, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(agent_r3.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Created on - \", agent_r3.createdDate, \"\");\n  }\n}\nexport let MyAgentHomeComponent = /*#__PURE__*/(() => {\n  class MyAgentHomeComponent {\n    tokenStorageService;\n    globalStoreService;\n    userName = '';\n    userEmail = '';\n    userRole = 'Lead Backend Engineer';\n    userAvatar = 'assets/icons/user-avatar.svg';\n    selectedUser = null;\n    // Statistics cards data\n    statCards = [{\n      icon: 'bot',\n      title: 'Total Agents created',\n      value: '32',\n      iconColor: '#000'\n    }, {\n      icon: 'circle-check',\n      title: 'Approved Agents',\n      value: '126',\n      iconColor: '#000'\n    }, {\n      icon: 'star',\n      title: 'Average Agents Rating',\n      value: '4.8',\n      iconColor: '#000'\n    }, {\n      icon: 'chart-spline',\n      title: 'Average Accuracy',\n      value: '97%',\n      iconColor: '#000'\n    }];\n    // Tabs data for My Agents section\n    activeTab = 'all';\n    agentTabs = [{\n      id: 'all',\n      label: 'All',\n      priority: 100\n    }, {\n      id: 'approved',\n      label: 'Approved (126)',\n      icon: 'circle-check',\n      iconColor: '#059669',\n      priority: 90\n    }, {\n      id: 'pending',\n      label: 'Pending (10)',\n      icon: 'clock',\n      iconColor: '#D97706',\n      priority: 80\n    }, {\n      id: 'denied',\n      label: 'Denied (4)',\n      icon: 'x',\n      iconColor: '#DC2626',\n      priority: 70\n    }, {\n      id: 'draft',\n      label: 'Draft (1)',\n      icon: 'pencil',\n      priority: 60\n    }];\n    // Agent cards data\n    allAgentCards = [{\n      id: 1,\n      title: 'Java to Ruby Migration',\n      description: 'AI-powered migration assistant designed to automate and optimize the transition of backend applications from Java to Ruby.',\n      status: 'Approved',\n      createdDate: '12/06/2025'\n    }, {\n      id: 2,\n      title: 'Java to Ruby Migration',\n      description: 'AI-powered migration assistant designed to automate and optimize the transition of backend applications from Java to Ruby.',\n      status: 'Pending',\n      createdDate: '10/06/2025'\n    }, {\n      id: 3,\n      title: 'Java to Ruby Migration',\n      description: 'AI-powered migration assistant designed to automate and optimize the transition of backend applications from Java to Ruby.',\n      status: 'Approved',\n      createdDate: '11/06/2025'\n    }, {\n      id: 4,\n      title: 'Java to Ruby Migration',\n      description: 'AI-powered migration assistant designed to automate and optimize the transition of backend applications from Java to Ruby.',\n      status: 'Approved',\n      createdDate: '08/06/2025'\n    }, {\n      id: 5,\n      title: 'Java to Ruby Migration',\n      description: 'AI-powered migration assistant designed to automate and optimize the transition of backend applications from Java to Ruby.',\n      status: 'Approved',\n      createdDate: '05/06/2025'\n    }, {\n      id: 6,\n      title: 'Java to Ruby Migration',\n      description: 'AI-powered migration assistant designed to automate and optimize the transition of backend applications from Java to Ruby.',\n      status: 'Pending',\n      createdDate: '01/06/2025'\n    }];\n    // Filtered agent cards based on active tab\n    get filteredAgentCards() {\n      if (this.activeTab === 'all') {\n        return this.allAgentCards;\n      }\n      const statusMap = {\n        'approved': 'Approved',\n        'pending': 'Pending',\n        'denied': 'Denied',\n        'draft': 'Draft'\n      };\n      const targetStatus = statusMap[this.activeTab];\n      return this.allAgentCards.filter(card => card.status === targetStatus);\n    }\n    constructor(tokenStorageService, globalStoreService) {\n      this.tokenStorageService = tokenStorageService;\n      this.globalStoreService = globalStoreService;\n    }\n    ngOnInit() {\n      this.loadUserData();\n      this.loadSelectedUser();\n    }\n    loadUserData() {\n      this.userName = this.tokenStorageService.getDaName() || 'Akash Kumar';\n      this.userEmail = this.tokenStorageService.getDaUsername() || '<EMAIL>';\n    }\n    loadSelectedUser() {\n      this.globalStoreService.selectedUser.subscribe(user => {\n        this.selectedUser = user;\n        if (user && user.role) {\n          this.userRole = user.role;\n        }\n      });\n    }\n    onCreateAgent() {\n      console.log('Create Agent clicked');\n      // TODO: Implement navigation to agent creation interface\n    }\n    onViewAnalytics() {\n      console.log('View Analytics clicked');\n      // TODO: Implement navigation to analytics page\n    }\n    onTabChange(tabId) {\n      this.activeTab = tabId;\n    }\n    onEditAgent(agent) {\n      console.log('Edit agent:', agent);\n      // TODO: Implement agent editing functionality\n    }\n    onViewAgent(agent) {\n      console.log('View agent:', agent);\n      // TODO: Implement agent viewing functionality\n    }\n    onDeleteAgent(agent) {\n      console.log('Delete agent:', agent);\n      // TODO: Implement agent deletion functionality\n    }\n    getStatusClass(status) {\n      const statusClasses = {\n        'Approved': 'status-approved',\n        'Pending': 'status-pending',\n        'Denied': 'status-denied',\n        'Draft': 'status-draft'\n      };\n      return statusClasses[status] || 'status-default';\n    }\n    static ɵfac = function MyAgentHomeComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MyAgentHomeComponent)(i0.ɵɵdirectiveInject(i1.TokenStorageService), i0.ɵɵdirectiveInject(i2.GlobalStoreService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MyAgentHomeComponent,\n      selectors: [[\"app-my-agent-home\"]],\n      decls: 25,\n      vars: 9,\n      consts: [[1, \"my-agent-home-container\"], [1, \"header-section\"], [1, \"user-profile-section\"], [1, \"user-avatar\"], [1, \"avatar-image\", 3, \"src\", \"alt\"], [1, \"user-details\"], [1, \"user-name\"], [1, \"user-email\"], [1, \"user-role\"], [1, \"action-buttons\"], [\"label\", \"Create Agent\", \"iconName\", \"plus\", \"iconPosition\", \"right\", \"variant\", \"primary\", \"iconColor\", \"#fff\", \"width\", \"270px\", \"height\", \"48px\", 1, \"create-agent-btn\", 3, \"userClick\"], [\"label\", \"View Analytics\", \"iconName\", \"chart-spline\", \"iconPosition\", \"right\", \"variant\", \"secondary\", \"iconColor\", \"#E91E63\", \"width\", \"270px\", \"height\", \"48px\", 3, \"userClick\"], [1, \"stats-section\"], [1, \"row\"], [\"class\", \"col-12 col-sm-6 col-lg-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"my-agents-section\"], [1, \"section-title\"], [3, \"tabChange\", \"tabs\", \"activeTab\"], [1, \"agent-cards-grid\"], [\"class\", \"col-12 col-md-6 col-lg-4\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-12\", \"col-sm-6\", \"col-lg-3\"], [1, \"stat-card\"], [1, \"stat-header\"], [1, \"stat-icon-container\"], [\"iconSize\", \"28px\", 1, \"stat-icon\", 3, \"iconName\", \"iconColor\"], [1, \"stat-content\"], [1, \"stat-title\"], [1, \"stat-value-container\"], [\"iconName\", \"star\", \"iconColor\", \"#FFD700\", \"iconSize\", \"24px\", \"class\", \"rating-star\", 4, \"ngIf\"], [1, \"stat-value\"], [\"iconName\", \"star\", \"iconColor\", \"#FFD700\", \"iconSize\", \"24px\", 1, \"rating-star\"], [1, \"col-12\", \"col-md-6\", \"col-lg-4\"], [1, \"agent-card\"], [1, \"card-layout\"], [1, \"agent-icon\"], [\"iconName\", \"code\", \"iconSize\", \"20px\", \"iconColor\", \"#fff\"], [1, \"card-content\"], [1, \"card-header\"], [1, \"agent-title\"], [1, \"status-badge\", 3, \"ngClass\"], [1, \"agent-description\"], [1, \"agent-meta\"], [1, \"created-date\"], [1, \"card-actions\"], [\"label\", \"Edit\", \"iconName\", \"pencil\", \"iconPosition\", \"right\", \"height\", \"40px\", \"width\", \"218px\", \"variant\", \"secondary\", \"iconColor\", \"#E91E63\", 3, \"click\"], [\"label\", \"View\", \"iconName\", \"eye\", \"iconPosition\", \"right\", \"height\", \"40px\", \"width\", \"218px\", \"variant\", \"secondary\", \"iconColor\", \"#E91E63\", 3, \"click\"], [\"iconName\", \"trash-2\", \"iconPosition\", \"only\", \"height\", \"40px\", \"width\", \"40px\", \"variant\", \"secondary\", \"iconColor\", \"#E91E63\", 3, \"click\"]],\n      template: function MyAgentHomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelement(4, \"img\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 5)(6, \"h2\", 6);\n          i0.ɵɵtext(7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"p\", 7);\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"p\", 8);\n          i0.ɵɵtext(11);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(12, \"div\", 9)(13, \"ava-button\", 10);\n          i0.ɵɵlistener(\"userClick\", function MyAgentHomeComponent_Template_ava_button_userClick_13_listener() {\n            return ctx.onCreateAgent();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"ava-button\", 11);\n          i0.ɵɵlistener(\"userClick\", function MyAgentHomeComponent_Template_ava_button_userClick_14_listener() {\n            return ctx.onViewAnalytics();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"div\", 12)(16, \"div\", 13);\n          i0.ɵɵtemplate(17, MyAgentHomeComponent_div_17_Template, 12, 5, \"div\", 14);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\", 15)(19, \"h2\", 16);\n          i0.ɵɵtext(20, \"My Agents\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"app-filter-tabs\", 17);\n          i0.ɵɵlistener(\"tabChange\", function MyAgentHomeComponent_Template_app_filter_tabs_tabChange_21_listener($event) {\n            return ctx.onTabChange($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"div\", 18)(23, \"div\", 13);\n          i0.ɵɵtemplate(24, MyAgentHomeComponent_div_24_Template, 20, 5, \"div\", 19);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"src\", ctx.userAvatar, i0.ɵɵsanitizeUrl)(\"alt\", ctx.userName);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.userName);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.userEmail);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.userRole);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngForOf\", ctx.statCards);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"tabs\", ctx.agentTabs)(\"activeTab\", ctx.activeTab);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredAgentCards);\n        }\n      },\n      dependencies: [CommonModule, i3.NgClass, i3.NgForOf, i3.NgIf, IconComponent, FilterTabsComponent, ButtonComponent],\n      styles: [\".my-agent-home-container {\\n  margin: 0 auto;\\n}\\n\\n.header-section {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  border-radius: 12px;\\n  padding: 2rem;\\n  margin-bottom: 1rem;\\n}\\n\\n.user-profile-section {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n\\n.user-avatar .avatar-image {\\n  height: 50px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  box-shadow: 0 2px 8px rgba(20, 70, 146, 0.08);\\n  margin-bottom: 26px;\\n}\\n\\n.user-details .user-name {\\n  color: #000;\\n  font-family: Mulish;\\n  font-size: 32px;\\n  font-weight: 700;\\n  margin: 0;\\n}\\n.user-details .user-email,\\n.user-details .user-role {\\n  color: #616874;\\n  font-family: Mulish;\\n  font-size: 20px;\\n  font-weight: 600;\\n  margin: 0;\\n}\\n\\n.action-buttons {\\n  display: flex;\\n  gap: 1rem;\\n  align-items: center;\\n}\\n\\n.create-agent-btn .ava-button .button-label {\\n  color: #fff;\\n}\\n\\n.stats-section {\\n  margin-top: 2rem;\\n}\\n.stats-section .row {\\n  margin: 0 -0.5rem;\\n}\\n.stats-section .col-12,\\n.stats-section .col-sm-6,\\n.stats-section .col-lg-3 {\\n  padding: 0 0.5rem;\\n  margin-bottom: 1rem;\\n}\\n\\n.stat-card {\\n  background: #fff;\\n  border-radius: 12px;\\n  padding: 1.5rem;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  border: 1px solid #f0f0f0;\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1rem;\\n}\\n.stat-card:hover {\\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\\n}\\n\\n.stat-header {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 12px;\\n}\\n\\n.stat-icon-container {\\n  flex-shrink: 0;\\n}\\n.stat-icon-container .stat-icon {\\n  width: 42px;\\n  height: 42px;\\n  border-radius: 8px;\\n  background: linear-gradient(180deg, var(--Brand-Primary-50, #FDE9EF) 0%, var(--Brand-Tertiary-50, #F0EBF8) 100%);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 0;\\n}\\n\\n.stat-content {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n  flex: 1;\\n}\\n\\n.stat-title {\\n  color: #000;\\n  text-align: left;\\n  font-family: Mulish;\\n  font-size: 24px;\\n  font-weight: 600;\\n  margin: 0;\\n}\\n\\n.stat-value-container {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.rating-star {\\n  flex-shrink: 0;\\n}\\n\\n.stat-value {\\n  color: #3B3F46;\\n  font-family: Mulish;\\n  font-size: 32px;\\n  font-weight: 700;\\n  margin: 0;\\n}\\n\\n@media (max-width: 768px) {\\n  .header-section {\\n    flex-direction: column;\\n    gap: 1.5rem;\\n    text-align: center;\\n  }\\n  .action-buttons {\\n    flex-direction: column;\\n    width: 100%;\\n  }\\n  .action-buttons ava-button {\\n    width: 100%;\\n    min-width: unset;\\n  }\\n  .stat-card {\\n    padding: 1rem;\\n  }\\n  .stat-value {\\n    font-size: 1.5rem;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .stat-card {\\n    gap: 0.75rem;\\n  }\\n  .stat-title {\\n    font-size: 0.8rem;\\n  }\\n  .stat-value {\\n    font-size: 1.25rem;\\n  }\\n}\\n.my-agents-section .section-title {\\n  color: #000;\\n  font-family: Mulish;\\n  font-size: 32px;\\n  font-weight: 700;\\n  margin: 0.5rem 0;\\n}\\n\\n.agent-cards-grid {\\n  margin-top: 1rem;\\n}\\n.agent-cards-grid .row {\\n  margin: 0 -0.5rem;\\n}\\n.agent-cards-grid .col-12,\\n.agent-cards-grid .col-md-6,\\n.agent-cards-grid .col-lg-4 {\\n  padding: 0 0.5rem;\\n  margin-bottom: 1.5rem;\\n}\\n\\n.agent-card {\\n  background: #fff;\\n  border-radius: 12px;\\n  padding: 1.5rem;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  border: 1px solid rgba(67, 131, 230, 0.1);\\n  height: 100%;\\n}\\n.agent-card:hover {\\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\\n}\\n.agent-card .card-layout {\\n  display: flex;\\n  gap: 1rem;\\n  height: 100%;\\n}\\n.agent-card .agent-icon {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 12px;\\n  border: 2px solid #fff;\\n  background: linear-gradient(180deg, #F06896 0%, #997BCF 100%);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n  box-shadow: 0 0 0 1px #e0e0e0;\\n}\\n.agent-card .card-content {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1rem;\\n  flex: 1;\\n}\\n.agent-card .card-header {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n}\\n.agent-card .card-header .agent-title {\\n  color: #000;\\n  font-family: Mulish;\\n  font-size: 24px;\\n  font-weight: 600;\\n  margin: 0;\\n  flex: 1;\\n}\\n.agent-card .card-header .status-badge {\\n  color: #FFF;\\n  font-family: Inter;\\n  font-size: 12px;\\n  font-style: normal;\\n  font-weight: 500;\\n  padding: 5px 10px;\\n  border-radius: 20px;\\n}\\n.agent-card .card-header .status-badge.status-approved {\\n  background: #059669;\\n  color: #fff;\\n}\\n.agent-card .card-header .status-badge.status-pending {\\n  background: #6B7280;\\n  color: #fff;\\n}\\n.agent-card .card-header .status-badge.status-denied {\\n  background: #DC2626;\\n  color: #fff;\\n}\\n.agent-card .agent-description {\\n  color: #595959;\\n  font-family: Inter;\\n  font-size: 16px;\\n  font-weight: 400;\\n  margin: 0;\\n}\\n.agent-card .agent-meta .created-date {\\n  font-size: 16px;\\n  color: #595959;\\n  margin: 0;\\n}\\n.agent-card .card-actions {\\n  display: flex;\\n  gap: 0.5rem;\\n  align-items: center;\\n  margin-top: 1rem;\\n  flex-wrap: wrap;\\n}\\n.agent-card .card-actions ava-button {\\n  align-items: center;\\n  flex: 1;\\n  min-width: 0;\\n}\\n.agent-card .card-actions ava-button:last-child {\\n  flex: none;\\n  width: 48px;\\n}\\n\\n@media (max-width: 768px) {\\n  .agent-cards-grid .col-12,\\n  .agent-cards-grid .col-md-6,\\n  .agent-cards-grid .col-lg-4 {\\n    margin-bottom: 1rem;\\n  }\\n  .agent-card {\\n    padding: 1rem;\\n  }\\n  .agent-card .card-layout {\\n    gap: 0.75rem;\\n  }\\n  .agent-card .agent-icon {\\n    width: 40px;\\n    height: 40px;\\n  }\\n  .agent-card .agent-title {\\n    font-size: 18px;\\n  }\\n  .agent-card .agent-description {\\n    font-size: 14px;\\n  }\\n  .agent-card .agent-meta .created-date {\\n    font-size: 14px;\\n  }\\n  .agent-card .status-badge {\\n    padding: 0.2rem 0.5rem;\\n    font-size: 0.7rem;\\n  }\\n  .agent-card .card-actions {\\n    gap: 0.4rem;\\n    flex-direction: column;\\n  }\\n  .agent-card .card-actions ava-button {\\n    width: 100%;\\n  }\\n  .agent-card .card-actions ava-button:last-child {\\n    width: 40px;\\n    align-self: flex-end;\\n  }\\n}\\n@media (max-width: 992px) {\\n  .agent-card .card-actions ava-button {\\n    width: 100%;\\n  }\\n  .agent-card .card-actions ava-button:last-child {\\n    width: 44px;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .agent-card {\\n    padding: 0.75rem;\\n  }\\n  .agent-card .card-layout {\\n    gap: 0.5rem;\\n  }\\n  .agent-card .agent-icon {\\n    width: 36px;\\n    height: 36px;\\n  }\\n  .agent-card .agent-title {\\n    font-size: 16px;\\n  }\\n  .agent-card .agent-description {\\n    font-size: 13px;\\n  }\\n  .agent-card .agent-meta .created-date {\\n    font-size: 12px;\\n  }\\n  .agent-card .card-actions {\\n    gap: 0.3rem;\\n  }\\n  .agent-card .card-actions ava-button {\\n    font-size: 12px;\\n    height: 36px;\\n  }\\n  .agent-card .card-actions ava-button:last-child {\\n    width: 36px;\\n    height: 36px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      encapsulation: 2\n    });\n  }\n  return MyAgentHomeComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "IconComponent", "ButtonComponent", "FilterTabsComponent", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵtext", "ɵɵtemplate", "MyAgentHomeComponent_div_17_ava_icon_9_Template", "ɵɵadvance", "ɵɵproperty", "card_r1", "icon", "iconColor", "ɵɵtextInterpolate", "title", "value", "ɵɵlistener", "MyAgentHomeComponent_div_24_Template_ava_button_click_17_listener", "agent_r3", "ɵɵrestoreView", "_r2", "$implicit", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "onEditAgent", "MyAgentHomeComponent_div_24_Template_ava_button_click_18_listener", "onViewAgent", "MyAgentHomeComponent_div_24_Template_ava_button_click_19_listener", "onDeleteAgent", "getStatusClass", "status", "ɵɵtextInterpolate1", "description", "createdDate", "MyAgentHomeComponent", "tokenStorageService", "globalStoreService", "userName", "userEmail", "userRole", "userAvatar", "selected<PERSON>ser", "statCards", "activeTab", "agentTabs", "id", "label", "priority", "allAgentCards", "filteredAgentCards", "statusMap", "targetStatus", "filter", "card", "constructor", "ngOnInit", "loadUserData", "loadSelectedUser", "getDaName", "getDaUsername", "subscribe", "user", "role", "onCreateAgent", "console", "log", "onViewAnalytics", "onTabChange", "tabId", "agent", "statusClasses", "ɵɵdirectiveInject", "i1", "TokenStorageService", "i2", "GlobalStoreService", "selectors", "decls", "vars", "consts", "template", "MyAgentHomeComponent_Template", "rf", "ctx", "MyAgentHomeComponent_Template_ava_button_userClick_13_listener", "MyAgentHomeComponent_Template_ava_button_userClick_14_listener", "MyAgentHomeComponent_div_17_Template", "MyAgentHomeComponent_Template_app_filter_tabs_tabChange_21_listener", "$event", "MyAgentHomeComponent_div_24_Template", "ɵɵsanitizeUrl", "i3", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles", "encapsulation"], "sources": ["C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\shared\\components\\my-agent-home\\my-agent-home.component.ts", "C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\shared\\components\\my-agent-home\\my-agent-home.component.html"], "sourcesContent": ["import { Component, OnInit, ViewEncapsulation } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { TokenStorageService } from '@shared/auth/services/token-storage.service';\r\nimport { GlobalStoreService } from '../../service/global-store.service';\r\nimport { IconComponent, ButtonComponent } from \"@ava/play-comp-library\";\r\nimport { FilterTabsComponent, FilterTab } from '../filter-tabs/filter-tabs.component';\r\n\r\ninterface User {\r\n  name: string;\r\n  role: string;\r\n  type: string;\r\n}\r\n\r\ninterface StatCard {\r\n  icon: string;\r\n  title: string;\r\n  value: string;\r\n  iconColor: string;\r\n}\r\n\r\ninterface AgentCard {\r\n  id: number;\r\n  title: string;\r\n  description: string;\r\n  status: 'Approved' | 'Pending' | 'Denied' | 'Draft';\r\n  createdDate: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-my-agent-home',\r\n  imports: [CommonModule, IconComponent, FilterTabsComponent, ButtonComponent],\r\n  templateUrl: './my-agent-home.component.html',\r\n  styleUrl: './my-agent-home.component.scss',\r\n  encapsulation: ViewEncapsulation.None\r\n})\r\nexport class MyAgentHomeComponent implements OnInit {\r\n  userName: string = '';\r\n  userEmail: string = '';\r\n  userRole: string = 'Lead Backend Engineer';\r\n  userAvatar: string = 'assets/icons/user-avatar.svg';\r\n  selectedUser: User | null = null;\r\n\r\n  // Statistics cards data\r\n  statCards: StatCard[] = [\r\n    {\r\n      icon: 'bot',\r\n      title: 'Total Agents created',\r\n      value: '32',\r\n      iconColor: '#000'\r\n    },\r\n    {\r\n      icon: 'circle-check',\r\n      title: 'Approved Agents',\r\n      value: '126',\r\n      iconColor: '#000'\r\n    },\r\n    {\r\n      icon: 'star',\r\n      title: 'Average Agents Rating',\r\n      value: '4.8',\r\n      iconColor: '#000'\r\n    },\r\n    {\r\n      icon: 'chart-spline',\r\n      title: 'Average Accuracy',\r\n      value: '97%',\r\n      iconColor: '#000'\r\n    }\r\n  ];\r\n\r\n  // Tabs data for My Agents section\r\n  activeTab = 'all';\r\n  agentTabs: FilterTab[] = [\r\n    { id: 'all', label: 'All', priority: 100 },\r\n    { id: 'approved', label: 'Approved (126)', icon: 'circle-check', iconColor: '#059669', priority: 90 },\r\n    { id: 'pending', label: 'Pending (10)', icon: 'clock', iconColor: '#D97706', priority: 80 },\r\n    { id: 'denied', label: 'Denied (4)', icon: 'x', iconColor: '#DC2626', priority: 70 },\r\n    { id: 'draft', label: 'Draft (1)', icon: 'pencil', priority: 60 }\r\n  ];\r\n\r\n  // Agent cards data\r\n  allAgentCards: AgentCard[] = [\r\n    {\r\n      id: 1,\r\n      title: 'Java to Ruby Migration',\r\n      description: 'AI-powered migration assistant designed to automate and optimize the transition of backend applications from Java to Ruby.',\r\n      status: 'Approved',\r\n      createdDate: '12/06/2025'\r\n    },\r\n    {\r\n      id: 2,\r\n      title: 'Java to Ruby Migration',\r\n      description: 'AI-powered migration assistant designed to automate and optimize the transition of backend applications from Java to Ruby.',\r\n      status: 'Pending',\r\n      createdDate: '10/06/2025'\r\n    },\r\n    {\r\n      id: 3,\r\n      title: 'Java to Ruby Migration',\r\n      description: 'AI-powered migration assistant designed to automate and optimize the transition of backend applications from Java to Ruby.',\r\n      status: 'Approved',\r\n      createdDate: '11/06/2025'\r\n    },\r\n    {\r\n      id: 4,\r\n      title: 'Java to Ruby Migration',\r\n      description: 'AI-powered migration assistant designed to automate and optimize the transition of backend applications from Java to Ruby.',\r\n      status: 'Approved',\r\n      createdDate: '08/06/2025'\r\n    },\r\n    {\r\n      id: 5,\r\n      title: 'Java to Ruby Migration',\r\n      description: 'AI-powered migration assistant designed to automate and optimize the transition of backend applications from Java to Ruby.',\r\n      status: 'Approved',\r\n      createdDate: '05/06/2025'\r\n    },\r\n    {\r\n      id: 6,\r\n      title: 'Java to Ruby Migration',\r\n      description: 'AI-powered migration assistant designed to automate and optimize the transition of backend applications from Java to Ruby.',\r\n      status: 'Pending',\r\n      createdDate: '01/06/2025'\r\n    }\r\n  ];\r\n\r\n  // Filtered agent cards based on active tab\r\n  get filteredAgentCards(): AgentCard[] {\r\n    if (this.activeTab === 'all') {\r\n      return this.allAgentCards;\r\n    }\r\n\r\n    const statusMap: { [key: string]: string } = {\r\n      'approved': 'Approved',\r\n      'pending': 'Pending',\r\n      'denied': 'Denied',\r\n      'draft': 'Draft'\r\n    };\r\n\r\n    const targetStatus = statusMap[this.activeTab];\r\n    return this.allAgentCards.filter(card => card.status === targetStatus);\r\n  }\r\n\r\n  constructor(\r\n    private tokenStorageService: TokenStorageService,\r\n    private globalStoreService: GlobalStoreService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loadUserData();\r\n    this.loadSelectedUser();\r\n  }\r\n\r\n  private loadUserData(): void {\r\n    this.userName = this.tokenStorageService.getDaName() || 'Akash Kumar';\r\n    this.userEmail = this.tokenStorageService.getDaUsername() || '<EMAIL>';\r\n  }\r\n\r\n  private loadSelectedUser(): void {\r\n    this.globalStoreService.selectedUser.subscribe((user) => {\r\n      this.selectedUser = user;\r\n      if (user && user.role) {\r\n        this.userRole = user.role;\r\n      }\r\n    });\r\n  }\r\n\r\n  onCreateAgent(): void {\r\n    console.log('Create Agent clicked');\r\n    // TODO: Implement navigation to agent creation interface\r\n  }\r\n\r\n  onViewAnalytics(): void {\r\n    console.log('View Analytics clicked');\r\n    // TODO: Implement navigation to analytics page\r\n  }\r\n\r\n  onTabChange(tabId: string): void {\r\n    this.activeTab = tabId;\r\n  }\r\n\r\n  onEditAgent(agent: AgentCard): void {\r\n    console.log('Edit agent:', agent);\r\n    // TODO: Implement agent editing functionality\r\n  }\r\n\r\n  onViewAgent(agent: AgentCard): void {\r\n    console.log('View agent:', agent);\r\n    // TODO: Implement agent viewing functionality\r\n  }\r\n\r\n  onDeleteAgent(agent: AgentCard): void {\r\n    console.log('Delete agent:', agent);\r\n    // TODO: Implement agent deletion functionality\r\n  }\r\n\r\n  getStatusClass(status: string): string {\r\n    const statusClasses: { [key: string]: string } = {\r\n      'Approved': 'status-approved',\r\n      'Pending': 'status-pending',\r\n      'Denied': 'status-denied',\r\n      'Draft': 'status-draft'\r\n    };\r\n    return statusClasses[status] || 'status-default';\r\n  }\r\n}\r\n", "<div class=\"my-agent-home-container\">\r\n  <div class=\"header-section\">\r\n    <!-- Left side - User Profile -->\r\n    <div class=\"user-profile-section\">\r\n      <div class=\"user-avatar\">\r\n        <img [src]=\"userAvatar\" [alt]=\"userName\" class=\"avatar-image\" />\r\n      </div>\r\n      <div class=\"user-details\">\r\n        <h2 class=\"user-name\">{{ userName }}</h2>\r\n        <p class=\"user-email\">{{ userEmail }}</p>\r\n        <p class=\"user-role\">{{ userRole }}</p>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Right side - Action Buttons -->\r\n    <div class=\"action-buttons\">\r\n        <ava-button\r\n        class=\"create-agent-btn\"\r\n          label=\"Create Agent\"\r\n          iconName=\"plus\"\r\n          iconPosition=\"right\"\r\n          variant=\"primary\"\r\n          iconColor=\"#fff\"\r\n          width=\"270px\"\r\n          height=\"48px\"\r\n          (userClick)=\"onCreateAgent()\"\r\n        ></ava-button>\r\n              <ava-button\r\n          label=\"View Analytics\"\r\n          iconName=\"chart-spline\"\r\n          iconPosition=\"right\"\r\n          variant=\"secondary\"\r\n          iconColor=\"#E91E63\"\r\n          width=\"270px\"\r\n          height=\"48px\"\r\n          (userClick)=\"onViewAnalytics()\"\r\n        ></ava-button>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Statistics Cards Section -->\r\n  <div class=\"stats-section\">\r\n    <div class=\"row\">\r\n      <div class=\"col-12 col-sm-6 col-lg-3\" *ngFor=\"let card of statCards\">\r\n        <div class=\"stat-card\">\r\n          <div class=\"stat-header\">\r\n            <div class=\"stat-icon-container\">\r\n              <ava-icon [iconName]=\"card.icon\" [iconColor]=\"card.iconColor\" iconSize=\"28px\" class=\"stat-icon\"></ava-icon>\r\n            </div>\r\n            <div class=\"stat-content\">\r\n              <h3 class=\"stat-title\">{{ card.title }}</h3>\r\n              <div class=\"stat-value-container\">\r\n                <ava-icon\r\n                  *ngIf=\"card.title === 'Average Agents Rating'\"\r\n                  iconName=\"star\"\r\n                  iconColor=\"#FFD700\"\r\n                  iconSize=\"24px\"\r\n                  class=\"rating-star\">\r\n                </ava-icon>\r\n                <p class=\"stat-value\">{{ card.value }}</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- My Agents Section with Tabs -->\r\n  <div class=\"my-agents-section\">\r\n    <h2 class=\"section-title\">My Agents</h2>\r\n    <app-filter-tabs\r\n      [tabs]=\"agentTabs\"\r\n      [activeTab]=\"activeTab\"\r\n      (tabChange)=\"onTabChange($event)\">\r\n    </app-filter-tabs>\r\n\r\n    <!-- Agent Cards Grid -->\r\n    <div class=\"agent-cards-grid\">\r\n      <div class=\"row\">\r\n        <div class=\"col-12 col-md-6 col-lg-4\" *ngFor=\"let agent of filteredAgentCards\">\r\n          <div class=\"agent-card\">\r\n            <div class=\"card-layout\">\r\n              <!-- Agent Icon -->\r\n              <div class=\"agent-icon\">\r\n                <ava-icon iconName=\"code\" iconSize=\"20px\" iconColor=\"#fff\"></ava-icon>\r\n              </div>\r\n\r\n              <!-- Card Content -->\r\n              <div class=\"card-content\">\r\n                <!-- Card Header with Title and Status -->\r\n                <div class=\"card-header\">\r\n                  <h3 class=\"agent-title\">{{ agent.title }}</h3>\r\n                  <div class=\"status-badge\" [ngClass]=\"getStatusClass(agent.status)\">\r\n                    {{ agent.status }}\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Description and Meta -->\r\n                <p class=\"agent-description\">{{ agent.description }}</p>\r\n                <div class=\"agent-meta\">\r\n                  <span class=\"created-date\">Created on - {{ agent.createdDate }}</span>\r\n                </div>\r\n\r\n                <!-- Card Actions -->\r\n                <div class=\"card-actions\">\r\n              <ava-button\r\n              label=\"Edit\"\r\n                iconName=\"pencil\"\r\n                iconPosition=\"right\"\r\n                height=\"40px\"\r\n                width=\"218px\"\r\n                variant=\"secondary\"\r\n                iconColor=\"#E91E63\"\r\n                (click)=\"onEditAgent(agent)\">\r\n                \r\n              </ava-button>\r\n              <ava-button\r\n                label=\"View\"\r\n                iconName=\"eye\"\r\n                iconPosition=\"right\"\r\n                height=\"40px\"\r\n                width=\"218px\"\r\n                variant=\"secondary\"\r\n                iconColor=\"#E91E63\"\r\n                (click)=\"onViewAgent(agent)\">                \r\n              </ava-button>\r\n              <ava-button\r\n                iconName=\"trash-2\"\r\n                iconPosition=\"only\"\r\n                height=\"40px\"\r\n                width=\"40px\"\r\n                variant=\"secondary\"\r\n                iconColor=\"#E91E63\"\r\n                (click)=\"onDeleteAgent(agent)\">\r\n              </ava-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAG9C,SAASC,aAAa,EAAEC,eAAe,QAAQ,wBAAwB;AACvE,SAASC,mBAAmB,QAAmB,sCAAsC;;;;;;;IC+CrEC,EAAA,CAAAC,SAAA,mBAMW;;;;;IAZfD,EAHN,CAAAE,cAAA,cAAqE,cAC5C,cACI,cACU;IAC/BF,EAAA,CAAAC,SAAA,mBAA2G;IAC7GD,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAE,cAAA,cAA0B,aACD;IAAAF,EAAA,CAAAI,MAAA,GAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC5CH,EAAA,CAAAE,cAAA,cAAkC;IAChCF,EAAA,CAAAK,UAAA,IAAAC,+CAAA,uBAKsB;IAEtBN,EAAA,CAAAE,cAAA,aAAsB;IAAAF,EAAA,CAAAI,MAAA,IAAgB;IAKhDJ,EALgD,CAAAG,YAAA,EAAI,EACtC,EACF,EACF,EACF,EACF;;;;IAjBYH,EAAA,CAAAO,SAAA,GAAsB;IAACP,EAAvB,CAAAQ,UAAA,aAAAC,OAAA,CAAAC,IAAA,CAAsB,cAAAD,OAAA,CAAAE,SAAA,CAA6B;IAGtCX,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAY,iBAAA,CAAAH,OAAA,CAAAI,KAAA,CAAgB;IAGlCb,EAAA,CAAAO,SAAA,GAA4C;IAA5CP,EAAA,CAAAQ,UAAA,SAAAC,OAAA,CAAAI,KAAA,6BAA4C;IAMzBb,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAY,iBAAA,CAAAH,OAAA,CAAAK,KAAA,CAAgB;;;;;;IAyBxCd,EAJN,CAAAE,cAAA,cAA+E,cACrD,cACG,cAEC;IACtBF,EAAA,CAAAC,SAAA,mBAAsE;IACxED,EAAA,CAAAG,YAAA,EAAM;IAMFH,EAHJ,CAAAE,cAAA,cAA0B,cAEC,aACC;IAAAF,EAAA,CAAAI,MAAA,GAAiB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC9CH,EAAA,CAAAE,cAAA,cAAmE;IACjEF,EAAA,CAAAI,MAAA,IACF;IACFJ,EADE,CAAAG,YAAA,EAAM,EACF;IAGNH,EAAA,CAAAE,cAAA,aAA6B;IAAAF,EAAA,CAAAI,MAAA,IAAuB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAEtDH,EADF,CAAAE,cAAA,eAAwB,gBACK;IAAAF,EAAA,CAAAI,MAAA,IAAoC;IACjEJ,EADiE,CAAAG,YAAA,EAAO,EAClE;IAIRH,EADE,CAAAE,cAAA,eAA0B,sBASG;IAA7BF,EAAA,CAAAe,UAAA,mBAAAC,kEAAA;MAAA,MAAAC,QAAA,GAAAjB,EAAA,CAAAkB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASF,MAAA,CAAAG,WAAA,CAAAP,QAAA,CAAkB;IAAA,EAAC;IAE9BjB,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAE,cAAA,sBAQ+B;IAA7BF,EAAA,CAAAe,UAAA,mBAAAU,kEAAA;MAAA,MAAAR,QAAA,GAAAjB,EAAA,CAAAkB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASF,MAAA,CAAAK,WAAA,CAAAT,QAAA,CAAkB;IAAA,EAAC;IAC9BjB,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAE,cAAA,sBAOiC;IAA/BF,EAAA,CAAAe,UAAA,mBAAAY,kEAAA;MAAA,MAAAV,QAAA,GAAAjB,EAAA,CAAAkB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASF,MAAA,CAAAO,aAAA,CAAAX,QAAA,CAAoB;IAAA,EAAC;IAMtCjB,EALM,CAAAG,YAAA,EAAa,EACL,EACF,EACF,EACF,EACF;;;;;IAhD4BH,EAAA,CAAAO,SAAA,GAAiB;IAAjBP,EAAA,CAAAY,iBAAA,CAAAK,QAAA,CAAAJ,KAAA,CAAiB;IACfb,EAAA,CAAAO,SAAA,EAAwC;IAAxCP,EAAA,CAAAQ,UAAA,YAAAa,MAAA,CAAAQ,cAAA,CAAAZ,QAAA,CAAAa,MAAA,EAAwC;IAChE9B,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAA+B,kBAAA,MAAAd,QAAA,CAAAa,MAAA,MACF;IAI2B9B,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAY,iBAAA,CAAAK,QAAA,CAAAe,WAAA,CAAuB;IAEvBhC,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAA+B,kBAAA,kBAAAd,QAAA,CAAAgB,WAAA,KAAoC;;;ADlEjF,WAAaC,oBAAoB;EAA3B,MAAOA,oBAAoB;IA6GrBC,mBAAA;IACAC,kBAAA;IA7GVC,QAAQ,GAAW,EAAE;IACrBC,SAAS,GAAW,EAAE;IACtBC,QAAQ,GAAW,uBAAuB;IAC1CC,UAAU,GAAW,8BAA8B;IACnDC,YAAY,GAAgB,IAAI;IAEhC;IACAC,SAAS,GAAe,CACtB;MACEhC,IAAI,EAAE,KAAK;MACXG,KAAK,EAAE,sBAAsB;MAC7BC,KAAK,EAAE,IAAI;MACXH,SAAS,EAAE;KACZ,EACD;MACED,IAAI,EAAE,cAAc;MACpBG,KAAK,EAAE,iBAAiB;MACxBC,KAAK,EAAE,KAAK;MACZH,SAAS,EAAE;KACZ,EACD;MACED,IAAI,EAAE,MAAM;MACZG,KAAK,EAAE,uBAAuB;MAC9BC,KAAK,EAAE,KAAK;MACZH,SAAS,EAAE;KACZ,EACD;MACED,IAAI,EAAE,cAAc;MACpBG,KAAK,EAAE,kBAAkB;MACzBC,KAAK,EAAE,KAAK;MACZH,SAAS,EAAE;KACZ,CACF;IAED;IACAgC,SAAS,GAAG,KAAK;IACjBC,SAAS,GAAgB,CACvB;MAAEC,EAAE,EAAE,KAAK;MAAEC,KAAK,EAAE,KAAK;MAAEC,QAAQ,EAAE;IAAG,CAAE,EAC1C;MAAEF,EAAE,EAAE,UAAU;MAAEC,KAAK,EAAE,gBAAgB;MAAEpC,IAAI,EAAE,cAAc;MAAEC,SAAS,EAAE,SAAS;MAAEoC,QAAQ,EAAE;IAAE,CAAE,EACrG;MAAEF,EAAE,EAAE,SAAS;MAAEC,KAAK,EAAE,cAAc;MAAEpC,IAAI,EAAE,OAAO;MAAEC,SAAS,EAAE,SAAS;MAAEoC,QAAQ,EAAE;IAAE,CAAE,EAC3F;MAAEF,EAAE,EAAE,QAAQ;MAAEC,KAAK,EAAE,YAAY;MAAEpC,IAAI,EAAE,GAAG;MAAEC,SAAS,EAAE,SAAS;MAAEoC,QAAQ,EAAE;IAAE,CAAE,EACpF;MAAEF,EAAE,EAAE,OAAO;MAAEC,KAAK,EAAE,WAAW;MAAEpC,IAAI,EAAE,QAAQ;MAAEqC,QAAQ,EAAE;IAAE,CAAE,CAClE;IAED;IACAC,aAAa,GAAgB,CAC3B;MACEH,EAAE,EAAE,CAAC;MACLhC,KAAK,EAAE,wBAAwB;MAC/BmB,WAAW,EAAE,4HAA4H;MACzIF,MAAM,EAAE,UAAU;MAClBG,WAAW,EAAE;KACd,EACD;MACEY,EAAE,EAAE,CAAC;MACLhC,KAAK,EAAE,wBAAwB;MAC/BmB,WAAW,EAAE,4HAA4H;MACzIF,MAAM,EAAE,SAAS;MACjBG,WAAW,EAAE;KACd,EACD;MACEY,EAAE,EAAE,CAAC;MACLhC,KAAK,EAAE,wBAAwB;MAC/BmB,WAAW,EAAE,4HAA4H;MACzIF,MAAM,EAAE,UAAU;MAClBG,WAAW,EAAE;KACd,EACD;MACEY,EAAE,EAAE,CAAC;MACLhC,KAAK,EAAE,wBAAwB;MAC/BmB,WAAW,EAAE,4HAA4H;MACzIF,MAAM,EAAE,UAAU;MAClBG,WAAW,EAAE;KACd,EACD;MACEY,EAAE,EAAE,CAAC;MACLhC,KAAK,EAAE,wBAAwB;MAC/BmB,WAAW,EAAE,4HAA4H;MACzIF,MAAM,EAAE,UAAU;MAClBG,WAAW,EAAE;KACd,EACD;MACEY,EAAE,EAAE,CAAC;MACLhC,KAAK,EAAE,wBAAwB;MAC/BmB,WAAW,EAAE,4HAA4H;MACzIF,MAAM,EAAE,SAAS;MACjBG,WAAW,EAAE;KACd,CACF;IAED;IACA,IAAIgB,kBAAkBA,CAAA;MACpB,IAAI,IAAI,CAACN,SAAS,KAAK,KAAK,EAAE;QAC5B,OAAO,IAAI,CAACK,aAAa;MAC3B;MAEA,MAAME,SAAS,GAA8B;QAC3C,UAAU,EAAE,UAAU;QACtB,SAAS,EAAE,SAAS;QACpB,QAAQ,EAAE,QAAQ;QAClB,OAAO,EAAE;OACV;MAED,MAAMC,YAAY,GAAGD,SAAS,CAAC,IAAI,CAACP,SAAS,CAAC;MAC9C,OAAO,IAAI,CAACK,aAAa,CAACI,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACvB,MAAM,KAAKqB,YAAY,CAAC;IACxE;IAEAG,YACUnB,mBAAwC,EACxCC,kBAAsC;MADtC,KAAAD,mBAAmB,GAAnBA,mBAAmB;MACnB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IACzB;IAEHmB,QAAQA,CAAA;MACN,IAAI,CAACC,YAAY,EAAE;MACnB,IAAI,CAACC,gBAAgB,EAAE;IACzB;IAEQD,YAAYA,CAAA;MAClB,IAAI,CAACnB,QAAQ,GAAG,IAAI,CAACF,mBAAmB,CAACuB,SAAS,EAAE,IAAI,aAAa;MACrE,IAAI,CAACpB,SAAS,GAAG,IAAI,CAACH,mBAAmB,CAACwB,aAAa,EAAE,IAAI,sBAAsB;IACrF;IAEQF,gBAAgBA,CAAA;MACtB,IAAI,CAACrB,kBAAkB,CAACK,YAAY,CAACmB,SAAS,CAAEC,IAAI,IAAI;QACtD,IAAI,CAACpB,YAAY,GAAGoB,IAAI;QACxB,IAAIA,IAAI,IAAIA,IAAI,CAACC,IAAI,EAAE;UACrB,IAAI,CAACvB,QAAQ,GAAGsB,IAAI,CAACC,IAAI;QAC3B;MACF,CAAC,CAAC;IACJ;IAEAC,aAAaA,CAAA;MACXC,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;MACnC;IACF;IAEAC,eAAeA,CAAA;MACbF,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrC;IACF;IAEAE,WAAWA,CAACC,KAAa;MACvB,IAAI,CAACzB,SAAS,GAAGyB,KAAK;IACxB;IAEA5C,WAAWA,CAAC6C,KAAgB;MAC1BL,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEI,KAAK,CAAC;MACjC;IACF;IAEA3C,WAAWA,CAAC2C,KAAgB;MAC1BL,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEI,KAAK,CAAC;MACjC;IACF;IAEAzC,aAAaA,CAACyC,KAAgB;MAC5BL,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEI,KAAK,CAAC;MACnC;IACF;IAEAxC,cAAcA,CAACC,MAAc;MAC3B,MAAMwC,aAAa,GAA8B;QAC/C,UAAU,EAAE,iBAAiB;QAC7B,SAAS,EAAE,gBAAgB;QAC3B,QAAQ,EAAE,eAAe;QACzB,OAAO,EAAE;OACV;MACD,OAAOA,aAAa,CAACxC,MAAM,CAAC,IAAI,gBAAgB;IAClD;;uCAzKWI,oBAAoB,EAAAlC,EAAA,CAAAuE,iBAAA,CAAAC,EAAA,CAAAC,mBAAA,GAAAzE,EAAA,CAAAuE,iBAAA,CAAAG,EAAA,CAAAC,kBAAA;IAAA;;YAApBzC,oBAAoB;MAAA0C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC/B3BlF,EAJN,CAAAE,cAAA,aAAqC,aACP,aAEQ,aACP;UACvBF,EAAA,CAAAC,SAAA,aAAgE;UAClED,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAE,cAAA,aAA0B,YACF;UAAAF,EAAA,CAAAI,MAAA,GAAc;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACzCH,EAAA,CAAAE,cAAA,WAAsB;UAAAF,EAAA,CAAAI,MAAA,GAAe;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UACzCH,EAAA,CAAAE,cAAA,YAAqB;UAAAF,EAAA,CAAAI,MAAA,IAAc;UAEvCJ,EAFuC,CAAAG,YAAA,EAAI,EACnC,EACF;UAIFH,EADJ,CAAAE,cAAA,cAA4B,sBAWvB;UADCF,EAAA,CAAAe,UAAA,uBAAAqE,+DAAA;YAAA,OAAaD,GAAA,CAAApB,aAAA,EAAe;UAAA,EAAC;UAC9B/D,EAAA,CAAAG,YAAA,EAAa;UACRH,EAAA,CAAAE,cAAA,sBASL;UADCF,EAAA,CAAAe,UAAA,uBAAAsE,+DAAA;YAAA,OAAaF,GAAA,CAAAjB,eAAA,EAAiB;UAAA,EAAC;UAGvClE,EAFO,CAAAG,YAAA,EAAa,EACZ,EACF;UAIJH,EADF,CAAAE,cAAA,eAA2B,eACR;UACfF,EAAA,CAAAK,UAAA,KAAAiF,oCAAA,mBAAqE;UAuBzEtF,EADE,CAAAG,YAAA,EAAM,EACF;UAIJH,EADF,CAAAE,cAAA,eAA+B,cACH;UAAAF,EAAA,CAAAI,MAAA,iBAAS;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACxCH,EAAA,CAAAE,cAAA,2BAGoC;UAAlCF,EAAA,CAAAe,UAAA,uBAAAwE,oEAAAC,MAAA;YAAA,OAAaL,GAAA,CAAAhB,WAAA,CAAAqB,MAAA,CAAmB;UAAA,EAAC;UACnCxF,EAAA,CAAAG,YAAA,EAAkB;UAIhBH,EADF,CAAAE,cAAA,eAA8B,eACX;UACfF,EAAA,CAAAK,UAAA,KAAAoF,oCAAA,mBAA+E;UAgEvFzF,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACF;;;UA3IOH,EAAA,CAAAO,SAAA,GAAkB;UAACP,EAAnB,CAAAQ,UAAA,QAAA2E,GAAA,CAAA3C,UAAA,EAAAxC,EAAA,CAAA0F,aAAA,CAAkB,QAAAP,GAAA,CAAA9C,QAAA,CAAiB;UAGlBrC,EAAA,CAAAO,SAAA,GAAc;UAAdP,EAAA,CAAAY,iBAAA,CAAAuE,GAAA,CAAA9C,QAAA,CAAc;UACdrC,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAY,iBAAA,CAAAuE,GAAA,CAAA7C,SAAA,CAAe;UAChBtC,EAAA,CAAAO,SAAA,GAAc;UAAdP,EAAA,CAAAY,iBAAA,CAAAuE,GAAA,CAAA5C,QAAA,CAAc;UAiCkBvC,EAAA,CAAAO,SAAA,GAAY;UAAZP,EAAA,CAAAQ,UAAA,YAAA2E,GAAA,CAAAzC,SAAA,CAAY;UA6BnE1C,EAAA,CAAAO,SAAA,GAAkB;UAClBP,EADA,CAAAQ,UAAA,SAAA2E,GAAA,CAAAvC,SAAA,CAAkB,cAAAuC,GAAA,CAAAxC,SAAA,CACK;UAOmC3C,EAAA,CAAAO,SAAA,GAAqB;UAArBP,EAAA,CAAAQ,UAAA,YAAA2E,GAAA,CAAAlC,kBAAA,CAAqB;;;qBDlDzErD,YAAY,EAAA+F,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAEjG,aAAa,EAAEE,mBAAmB,EAAED,eAAe;MAAAiG,MAAA;MAAAC,aAAA;IAAA;;SAKhE9D,oBAAoB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}