{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { PopupComponent } from '@ava/play-comp-library';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nconst _c0 = [\"messagesContainer\"];\nfunction ChatInterfaceComponent_div_4_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 23);\n    i0.ɵɵelement(2, \"div\", 24)(3, \"div\", 24)(4, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction ChatInterfaceComponent_div_4_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const message_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", message_r2.text, \" \");\n  }\n}\nfunction ChatInterfaceComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"div\", 21);\n    i0.ɵɵtemplate(2, ChatInterfaceComponent_div_4_ng_container_2_Template, 5, 0, \"ng-container\", 22)(3, ChatInterfaceComponent_div_4_ng_container_3_Template, 2, 1, \"ng-container\", 22);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", message_r2.from);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", message_r2.text === \"...\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", message_r2.text !== \"...\");\n  }\n}\nfunction ChatInterfaceComponent_div_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 28);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 29)(4, \"div\", 30);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function ChatInterfaceComponent_div_8_div_1_Template_button_click_6_listener() {\n      const file_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.deleteFile(file_r4.name));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(7, \"svg\", 32);\n    i0.ɵɵelement(8, \"line\", 33)(9, \"line\", 34);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const file_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", file_r4.type.includes(\"zip\") && \"ZIP\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", file_r4.name, \" \");\n  }\n}\nfunction ChatInterfaceComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵtemplate(1, ChatInterfaceComponent_div_8_div_1_Template, 10, 2, \"div\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.files);\n  }\n}\nexport let ChatInterfaceComponent = /*#__PURE__*/(() => {\n  class ChatInterfaceComponent {\n    theme = 'light';\n    placeholder = 'Ask here';\n    messages = [];\n    isLoading = false;\n    isDisabled = false;\n    isOptionalInput = false;\n    handleFileUpload = false;\n    fileType = '';\n    maxFileSize = null; //Setting 100MB as default for now.\n    isMultiple = false;\n    messageSent = new EventEmitter();\n    attachmentClicked = new EventEmitter();\n    attachmentSelected = new EventEmitter();\n    fileSize = '';\n    messagesContainer;\n    files = [];\n    showWarningPopup = false;\n    inputText = '';\n    shouldScrollToBottom = false;\n    previousMessagesLength = 0;\n    // Icon configuration for the chat input\n    inputIcons = [{\n      name: 'attachment',\n      tooltip: 'Add attachment'\n    }, {\n      name: 'send',\n      tooltip: 'Send message'\n    }];\n    constructor() {}\n    ngOnInit() {\n      // Initialize with a welcome message if no messages exist\n      if (this.maxFileSize) {\n        this.fileSize = `${this.maxFileSize / (1024 * 1024)}MB`;\n      }\n      if (this.messages.length === 0) {\n        this.messages = [{\n          from: 'ai',\n          text: 'Hi there, how can I help you today?'\n        }];\n      }\n      // Initial scroll\n      this.shouldScrollToBottom = true;\n      setTimeout(() => this.scrollToBottom(), 100);\n    }\n    ngOnChanges(changes) {\n      // Monitor messages array for changes\n      if (changes['messages'] && !changes['messages'].firstChange && this.messages.length !== this.previousMessagesLength) {\n        this.shouldScrollToBottom = true;\n        this.previousMessagesLength = this.messages.length;\n      }\n    }\n    ngAfterViewChecked() {\n      if (this.shouldScrollToBottom) {\n        this.scrollToBottom();\n        this.shouldScrollToBottom = false;\n      }\n    }\n    scrollToBottom() {\n      try {\n        if (this.messagesContainer && this.messagesContainer.nativeElement) {\n          this.messagesContainer.nativeElement.scrollTop = this.messagesContainer.nativeElement.scrollHeight;\n        }\n      } catch (err) {\n        console.error('Error scrolling to bottom:', err);\n      }\n    }\n    onEnterKeydown(event) {\n      // Only prevent default and send if Shift key is not pressed\n      if (!event.shiftKey) {\n        event.preventDefault();\n        this.handleSendMessage();\n      }\n    }\n    handleSendMessage() {\n      setTimeout(() => {\n        this.files = [];\n      });\n      if (!this.inputText.trim() || this.isDisabled) {\n        this.messageSent.emit('');\n        return;\n      }\n      // Add user message to the chat\n      this.messages = [...this.messages, {\n        from: 'user',\n        text: this.inputText\n      }];\n      this.shouldScrollToBottom = true;\n      this.previousMessagesLength = this.messages.length;\n      // Emit the message to parent component\n      const messageText = this.inputText;\n      this.inputText = '';\n      this.messageSent.emit(messageText);\n      // If parent doesn't handle loading state, show a loading indicator\n      if (!this.isLoading) {\n        setTimeout(() => {\n          // Add a temporary loading message if parent doesn't handle responses\n          this.addLoadingIndicator();\n        }, 100);\n      }\n    }\n    handleIconClick(event) {\n      if (event.name === 'send') {\n        this.handleSendMessage();\n      } else if (event.name === 'attachment') {\n        this.attachmentClicked.emit();\n      }\n    }\n    addLoadingIndicator(showAlways = false) {\n      // Only add loading indicator if no parent handling\n      const messages = [...this.messages, {\n        from: 'ai',\n        text: '...'\n      }];\n      if (showAlways) {\n        this.messages = messages;\n      }\n      if (!this.isLoading && this.messages[this.messages.length - 1].from === 'user') {\n        this.messages = messages;\n        this.shouldScrollToBottom = true;\n        this.previousMessagesLength = this.messages.length;\n      }\n    }\n    // Public method to add an AI response\n    addAiResponse(text) {\n      // Remove any loading indicators\n      this.messages = this.messages.filter(m => m.text !== '...');\n      // Add the actual response\n      this.messages = [...this.messages, {\n        from: 'ai',\n        text: text\n      }];\n      this.shouldScrollToBottom = true;\n      this.previousMessagesLength = this.messages.length;\n    }\n    // This can be called by parent components to clear the chat\n    clearChat() {\n      this.messages = [];\n      this.inputText = '';\n      this.previousMessagesLength = 0;\n    }\n    uploadFile(file) {\n      const uploadedFiles = [...this.files, ...file];\n      const fileSise = uploadedFiles.reduce((a, b) => a + b.size, 0);\n      if (this.maxFileSize && fileSise > this.maxFileSize) {\n        this.showWarningPopup = true;\n        return;\n      }\n      this.files = [...uploadedFiles];\n      this.attachmentSelected.emit(this.files);\n    }\n    deleteFile(name) {\n      this.files = this.files.filter(file => file.name !== name);\n      this.attachmentSelected.emit(this.files);\n    }\n    static ɵfac = function ChatInterfaceComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ChatInterfaceComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ChatInterfaceComponent,\n      selectors: [[\"app-chat-interface\"]],\n      viewQuery: function ChatInterfaceComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messagesContainer = _t.first);\n        }\n      },\n      hostBindings: function ChatInterfaceComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown.enter\", function ChatInterfaceComponent_keydown_enter_HostBindingHandler($event) {\n            return ctx.onEnterKeydown($event);\n          });\n        }\n      },\n      inputs: {\n        theme: \"theme\",\n        placeholder: \"placeholder\",\n        messages: \"messages\",\n        isLoading: \"isLoading\",\n        isDisabled: \"isDisabled\",\n        isOptionalInput: \"isOptionalInput\",\n        handleFileUpload: \"handleFileUpload\",\n        fileType: \"fileType\",\n        maxFileSize: \"maxFileSize\",\n        isMultiple: \"isMultiple\",\n        inputText: \"inputText\"\n      },\n      outputs: {\n        messageSent: \"messageSent\",\n        attachmentClicked: \"attachmentClicked\",\n        attachmentSelected: \"attachmentSelected\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 21,\n      vars: 22,\n      consts: [[\"messagesContainer\", \"\"], [\"fileInput\", \"\"], [1, \"chat-interface-container\"], [1, \"messages-scroll-container\"], [1, \"messages-container\"], [\"class\", \"message-wrapper\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"message-spacer\"], [1, \"prompt-container\"], [1, \"prompt-input-wrapper\"], [\"class\", \"files-container\", 4, \"ngIf\"], [1, \"prompt-input\", 3, \"ngModelChange\", \"keydown.enter\", \"placeholder\", \"ngModel\", \"disabled\"], [1, \"prompt-actions\"], [\"type\", \"file\", \"hidden\", \"\", 3, \"change\"], [1, \"action-icon\", \"attachment-icon\", 3, \"click\"], [\"width\", \"24\", \"height\", \"24\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"d\", \"M21.4383 11.6622L12.2483 20.8522C11.1225 21.9781 9.59552 22.6106 8.00334 22.6106C6.41115 22.6106 4.88418 21.9781 3.75834 20.8522C2.63249 19.7264 2 18.1994 2 16.6072C2 15.015 2.63249 13.4881 3.75834 12.3622L12.9483 3.17224C13.697 2.42348 14.7267 2 15.8033 2C16.88 2 17.9097 2.42348 18.6583 3.17224C19.4071 3.92101 19.8306 4.9507 19.8306 6.02735C19.8306 7.10399 19.4071 8.13368 18.6583 8.88245L9.47834 18.0722C9.10396 18.4466 8.58911 18.6584 8.05084 18.6584C7.51257 18.6584 6.99773 18.4466 6.62334 18.0722C6.24896 17.6978 6.03711 17.183 6.03711 16.6447C6.03711 16.1064 6.24896 15.5916 6.62334 15.2172L15.0683 6.78224\", \"stroke\", \"#6566CD\", \"stroke-width\", \"1.5\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"action-icon\", \"send-icon\", 3, \"click\", \"disabled\"], [\"d\", \"M22 2L11 13\", \"stroke\", \"#6566CD\", \"stroke-width\", \"1.5\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M22 2L15 22L11 13L2 9L22 2Z\", \"stroke\", \"#6566CD\", \"stroke-width\", \"1.5\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [3, \"closed\", \"show\", \"showTitle\", \"showHeaderIcon\", \"showInlineMessage\", \"inlineIconName\", \"inlineIconSize\", \"inlineIconColor\", \"inlineMessage\", \"message\", \"showClose\", \"showCancel\", \"cancelButtonLabel\", \"cancelButtonVariant\", \"popupWidth\"], [1, \"message-wrapper\", 3, \"ngClass\"], [1, \"message-bubble\"], [4, \"ngIf\"], [1, \"dot-typing\"], [1, \"dot\"], [1, \"files-container\"], [\"class\", \"file-bubble\", 4, \"ngFor\", \"ngForOf\"], [1, \"file-bubble\"], [1, \"file-icon\"], [1, \"file-details\"], [1, \"file-name\"], [\"aria-label\", \"Delete node\", 1, \"delete-btn\", 3, \"click\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"width\", \"12\", \"height\", \"12\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"aria-hidden\", \"true\"], [\"x1\", \"18\", \"y1\", \"6\", \"x2\", \"6\", \"y2\", \"18\"], [\"x1\", \"6\", \"y1\", \"6\", \"x2\", \"18\", \"y2\", \"18\"]],\n      template: function ChatInterfaceComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4, 0);\n          i0.ɵɵtemplate(4, ChatInterfaceComponent_div_4_Template, 4, 3, \"div\", 5);\n          i0.ɵɵelement(5, \"div\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 7)(7, \"div\", 8);\n          i0.ɵɵtemplate(8, ChatInterfaceComponent_div_8_Template, 2, 1, \"div\", 9);\n          i0.ɵɵelementStart(9, \"textarea\", 10);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ChatInterfaceComponent_Template_textarea_ngModelChange_9_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.inputText, $event) || (ctx.inputText = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"keydown.enter\", function ChatInterfaceComponent_Template_textarea_keydown_enter_9_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            $event.preventDefault();\n            return i0.ɵɵresetView(ctx.handleSendMessage());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 11)(11, \"input\", 12, 1);\n          i0.ɵɵlistener(\"change\", function ChatInterfaceComponent_Template_input_change_11_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const fileInput_r6 = i0.ɵɵreference(12);\n            return i0.ɵɵresetView(fileInput_r6.files && ctx.uploadFile(fileInput_r6.files));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"button\", 13);\n          i0.ɵɵlistener(\"click\", function ChatInterfaceComponent_Template_button_click_13_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const fileInput_r6 = i0.ɵɵreference(12);\n            ctx.handleIconClick({\n              name: \"attachment\",\n              index: 0\n            });\n            return i0.ɵɵresetView(ctx.handleFileUpload && (fileInput_r6 == null ? null : fileInput_r6.click()));\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(14, \"svg\", 14);\n          i0.ɵɵelement(15, \"path\", 15);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(16, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function ChatInterfaceComponent_Template_button_click_16_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.handleSendMessage());\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(17, \"svg\", 14);\n          i0.ɵɵelement(18, \"path\", 17)(19, \"path\", 18);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(20, \"ava-popup\", 19);\n          i0.ɵɵlistener(\"closed\", function ChatInterfaceComponent_Template_ava_popup_closed_20_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.showWarningPopup = false);\n          });\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.messages);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.files.length);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"placeholder\", ctx.placeholder);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.inputText);\n          i0.ɵɵproperty(\"disabled\", ctx.isDisabled);\n          i0.ɵɵadvance(2);\n          i0.ɵɵattribute(\"accept\", ctx.fileType)(\"multiple\", ctx.isMultiple);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading || !ctx.isOptionalInput && !ctx.inputText.trim());\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"show\", ctx.showWarningPopup)(\"showTitle\", false)(\"showHeaderIcon\", false)(\"showInlineMessage\", true)(\"inlineIconName\", \"info\")(\"inlineIconSize\", 48)(\"inlineIconColor\", \"#007bff\")(\"inlineMessage\", \"Warning!\")(\"message\", \"The total size of selected files exceeds {{fileSize}}. Please select files totaling less than {{fileSize}}.\")(\"showClose\", true)(\"showCancel\", true)(\"cancelButtonLabel\", \"Okay\")(\"cancelButtonVariant\", \"primary\")(\"popupWidth\", \"400px\");\n        }\n      },\n      dependencies: [CommonModule, i1.NgClass, i1.NgForOf, i1.NgIf, FormsModule, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgModel, PopupComponent],\n      styles: [\"@import url(https://fonts.googleapis.com/css2?family=Mulish:wght@300;400[_ngcontent-%COMP%];500[_ngcontent-%COMP%];600[_ngcontent-%COMP%];700&display=swap)[_ngcontent-%COMP%];.chat-interface-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  width: 100%;\\n  background-color: var(--agent-card-bg, #ffffff);\\n  border-radius: 8px;\\n  overflow: hidden;\\n  font-family: \\\"Mulish\\\", sans-serif;\\n}\\n\\n.messages-scroll-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow: hidden;\\n  position: relative;\\n  display: flex;\\n  flex-direction: column;\\n  max-height: calc(100% - 150px);\\n}\\n\\n.messages-container[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  overflow-y: auto;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n  scroll-behavior: smooth;\\n}\\n\\n.message-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  width: 100%;\\n}\\n.message-wrapper.user[_ngcontent-%COMP%] {\\n  justify-content: flex-end;\\n}\\n.message-wrapper.user[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%] {\\n  background-color: var(--chat-user-message-bg, #f8d7e3);\\n  color: var(--chat-user-message-text, #333333);\\n  border-radius: 8px;\\n  max-width: 100%;\\n}\\n.message-wrapper.ai[_ngcontent-%COMP%] {\\n  justify-content: flex-start;\\n}\\n.message-wrapper.ai[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%] {\\n  background-color: var(--chat-ai-message-bg, #f0f2f5);\\n  color: var(--chat-ai-message-text, #333333);\\n  border-radius: 18px 18px 18px 4px;\\n  max-width: 100%;\\n}\\n\\n.message-bubble[_ngcontent-%COMP%] {\\n  padding: 12px 16px;\\n  max-width: 70%;\\n  word-break: break-word;\\n  font-size: 15px;\\n  line-height: 1.5;\\n  position: relative;\\n  box-shadow: 0 1px 2px var(--chat-message-shadow, rgba(0, 0, 0, 0.05));\\n}\\n\\n.message-spacer[_ngcontent-%COMP%] {\\n  height: 12px;\\n  width: 100%;\\n}\\n\\n.prompt-container[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n}\\n\\n.prompt-input-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  border-radius: 16px;\\n  flex-direction: column;\\n  position: relative;\\n  min-height: 100px;\\n  max-height: 150px;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  background-color: white;\\n  box-shadow: 0 2px 6px var(--chat-input-shadow, rgba(0, 0, 0, 0.03));\\n}\\n\\n.prompt-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  width: 100%;\\n  padding: 16px 20px 56px 20px;\\n  border: none;\\n  outline: none;\\n  font-size: 15px;\\n  color: var(--input-text, #333333);\\n  background-color: var(--chat-input-bg, #f9fafb);\\n  font-family: \\\"Mulish\\\", sans-serif;\\n  resize: none;\\n  border: 2px solid; \\n\\n  border-image-source: linear-gradient(91.99deg, #03acc1 1.36%, #1e65e1 100%);\\n  border-image-slice: 1; \\n\\n}\\n.prompt-input[_ngcontent-%COMP%]:disabled {\\n  background-color: var(--chat-input-disabled-bg, #f7f7f7);\\n  cursor: not-allowed;\\n}\\n.prompt-input[_ngcontent-%COMP%]::placeholder {\\n  color: var(--input-placeholder, #9ca3af);\\n  font-family: \\\"Mulish\\\", sans-serif;\\n}\\n\\n.prompt-actions[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 20px;\\n  bottom: 18px;\\n  display: flex;\\n  gap: 20px;\\n  align-items: center;\\n}\\n\\n.action-icon[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  padding: 0;\\n  width: 28px;\\n  height: 28px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  opacity: 0.7;\\n  transition: opacity 0.2s ease;\\n}\\n.action-icon[_ngcontent-%COMP%]:hover {\\n  opacity: 1;\\n}\\n.action-icon[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.action-icon[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n}\\n.action-icon[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n}\\n.action-icon[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]   path[_ngcontent-%COMP%] {\\n  stroke: var(--button-start-gradient, #6566cd);\\n}\\n\\n.dot-typing[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n.dot-typing[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 50%;\\n  background-color: var(--chat-typing-dot, #9ca3af);\\n  animation: _ngcontent-%COMP%_dot-pulse 1.5s infinite ease-in-out;\\n}\\n.dot-typing[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%]:nth-child(1) {\\n  animation-delay: 0s;\\n}\\n.dot-typing[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%]:nth-child(2) {\\n  animation-delay: 0.3s;\\n}\\n.dot-typing[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%]:nth-child(3) {\\n  animation-delay: 0.6s;\\n}\\n\\n@keyframes _ngcontent-%COMP%_dot-pulse {\\n  0%, 80%, 100% {\\n    transform: scale(0.8);\\n    opacity: 0.6;\\n  }\\n  40% {\\n    transform: scale(1.2);\\n    opacity: 1;\\n  }\\n}\\n.files-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 200px;\\n  padding: 1rem 1rem 0 1rem;\\n  display: flex;\\n  gap: 12px;\\n  width: 100%;\\n  overflow-x: auto;\\n  overflow-y: hidden;\\n}\\n\\n.file-bubble[_ngcontent-%COMP%] {\\n  border: 1px solid #c5c5c5;\\n  padding: 10px 12px;\\n  border-radius: 8px;\\n  max-width: 450px;\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.file-icon[_ngcontent-%COMP%] {\\n  width: 30px;\\n  height: 30px;\\n  background: #90a4ae;\\n  color: #fff;\\n  font-weight: bold;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 5px;\\n}\\n\\n.file-details[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n  overflow: hidden;\\n}\\n\\n.file-name[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  padding-right: 0.5rem;\\n  white-space: nowrap;\\n  text-overflow: ellipsis;\\n  overflow: hidden;\\n}\\n\\n.delete-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 0;\\n  top: 0;\\n  width: 20px;\\n  height: 20px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background-color: #ef4444;\\n  border: none;\\n  border-radius: 50%;\\n  padding: 0;\\n  color: white;\\n  cursor: pointer;\\n  z-index: 999;\\n  transition: all 0.2s ease;\\n}\\n\\n.delete-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #dc2626;\\n}\\n\\n  .prompt-actions svg path {\\n  stroke: #143681 !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return ChatInterfaceComponent;\n})();", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "PopupComponent", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "message_r2", "text", "ɵɵtemplate", "ChatInterfaceComponent_div_4_ng_container_2_Template", "ChatInterfaceComponent_div_4_ng_container_3_Template", "ɵɵproperty", "from", "ɵɵlistener", "ChatInterfaceComponent_div_8_div_1_Template_button_click_6_listener", "file_r4", "ɵɵrestoreView", "_r3", "$implicit", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "deleteFile", "name", "type", "includes", "ChatInterfaceComponent_div_8_div_1_Template", "files", "ChatInterfaceComponent", "theme", "placeholder", "messages", "isLoading", "isDisabled", "isOptionalInput", "handleFileUpload", "fileType", "maxFileSize", "isMultiple", "messageSent", "attachmentClicked", "attachmentSelected", "fileSize", "messagesContainer", "showWarningPopup", "inputText", "shouldScrollToBottom", "previousMessages<PERSON><PERSON><PERSON>", "inputIcons", "tooltip", "constructor", "ngOnInit", "length", "setTimeout", "scrollToBottom", "ngOnChanges", "changes", "firstChange", "ngAfterViewChecked", "nativeElement", "scrollTop", "scrollHeight", "err", "console", "error", "onEnterKeydown", "event", "shift<PERSON>ey", "preventDefault", "handleSendMessage", "trim", "emit", "messageText", "addLoadingIndicator", "handleIconClick", "showAlways", "addAiResponse", "filter", "m", "clearChat", "uploadFile", "file", "uploadedFiles", "fileSise", "reduce", "a", "b", "size", "selectors", "viewQuery", "ChatInterfaceComponent_Query", "rf", "ctx", "ChatInterfaceComponent_keydown_enter_HostBindingHandler", "$event", "ChatInterfaceComponent_div_4_Template", "ChatInterfaceComponent_div_8_Template", "ɵɵtwoWayListener", "ChatInterfaceComponent_Template_textarea_ngModelChange_9_listener", "_r1", "ɵɵtwoWayBindingSet", "ChatInterfaceComponent_Template_textarea_keydown_enter_9_listener", "ChatInterfaceComponent_Template_input_change_11_listener", "fileInput_r6", "ɵɵreference", "ChatInterfaceComponent_Template_button_click_13_listener", "index", "click", "ChatInterfaceComponent_Template_button_click_16_listener", "ChatInterfaceComponent_Template_ava_popup_closed_20_listener", "ɵɵtwoWayProperty", "i1", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\shared\\components\\chat-interface\\chat-interface.component.ts", "C:\\console\\aava-ui-web\\projects\\console\\src\\app\\shared\\components\\chat-interface\\chat-interface.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  Input,\r\n  Output,\r\n  EventEmitter,\r\n  ViewChild,\r\n  ElementRef,\r\n  OnInit,\r\n  HostListener,\r\n  AfterViewChecked,\r\n  OnChanges,\r\n  SimpleChanges,\r\n} from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { ChatMessage } from '../chat-window';\r\nimport { PopupComponent } from '@ava/play-comp-library';\r\n\r\n@Component({\r\n  selector: 'app-chat-interface',\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule, PopupComponent],\r\n  templateUrl: './chat-interface.component.html',\r\n  styleUrls: ['./chat-interface.component.scss'],\r\n})\r\nexport class ChatInterfaceComponent\r\n  implements OnInit, AfterViewChecked, OnChanges\r\n{\r\n  @Input() theme: 'light' | 'dark' = 'light';\r\n  @Input() placeholder: string = 'Ask here';\r\n  @Input() messages: ChatMessage[] = [];\r\n  @Input() isLoading: boolean = false;\r\n  @Input() isDisabled: boolean = false;\r\n  @Input() isOptionalInput = false;\r\n  @Input() handleFileUpload = false;\r\n  @Input() fileType = '';\r\n  @Input() maxFileSize: number | null = null; //Setting 100MB as default for now.\r\n  @Input() isMultiple = false;\r\n\r\n  @Output() messageSent = new EventEmitter<string>();\r\n  @Output() attachmentClicked = new EventEmitter<void>();\r\n  @Output() attachmentSelected = new EventEmitter<File[]>();\r\n\r\n  fileSize = '';\r\n\r\n  @ViewChild('messagesContainer') messagesContainer!: ElementRef;\r\n\r\n  files: File[] = [];\r\n  showWarningPopup = false;\r\n\r\n  @Input() inputText: string = '';\r\n  private shouldScrollToBottom: boolean = false;\r\n  private previousMessagesLength = 0;\r\n\r\n  // Icon configuration for the chat input\r\n  inputIcons = [\r\n    { name: 'attachment', tooltip: 'Add attachment' },\r\n    { name: 'send', tooltip: 'Send message' },\r\n  ];\r\n\r\n  constructor() {}\r\n\r\n  ngOnInit(): void {\r\n    // Initialize with a welcome message if no messages exist\r\n    if (this.maxFileSize) {\r\n      this.fileSize = `${this.maxFileSize / (1024 * 1024)}MB`;\r\n    }\r\n    if (this.messages.length === 0) {\r\n      this.messages = [\r\n        {\r\n          from: 'ai',\r\n          text: 'Hi there, how can I help you today?',\r\n        },\r\n      ];\r\n    }\r\n\r\n    // Initial scroll\r\n    this.shouldScrollToBottom = true;\r\n    setTimeout(() => this.scrollToBottom(), 100);\r\n  }\r\n\r\n  ngOnChanges(changes: SimpleChanges): void {\r\n    // Monitor messages array for changes\r\n    if (\r\n      changes['messages'] &&\r\n      !changes['messages'].firstChange &&\r\n      this.messages.length !== this.previousMessagesLength\r\n    ) {\r\n      this.shouldScrollToBottom = true;\r\n      this.previousMessagesLength = this.messages.length;\r\n    }\r\n  }\r\n\r\n  ngAfterViewChecked() {\r\n    if (this.shouldScrollToBottom) {\r\n      this.scrollToBottom();\r\n      this.shouldScrollToBottom = false;\r\n    }\r\n  }\r\n\r\n  scrollToBottom(): void {\r\n    try {\r\n      if (this.messagesContainer && this.messagesContainer.nativeElement) {\r\n        this.messagesContainer.nativeElement.scrollTop =\r\n          this.messagesContainer.nativeElement.scrollHeight;\r\n      }\r\n    } catch (err) {\r\n      console.error('Error scrolling to bottom:', err);\r\n    }\r\n  }\r\n\r\n  @HostListener('keydown.enter', ['$event'])\r\n  onEnterKeydown(event: KeyboardEvent): void {\r\n    // Only prevent default and send if Shift key is not pressed\r\n    if (!event.shiftKey) {\r\n      event.preventDefault();\r\n      this.handleSendMessage();\r\n    }\r\n  }\r\n\r\n  handleSendMessage(): void {\r\n    setTimeout(() => {\r\n      this.files = [];\r\n    });\r\n    if (!this.inputText.trim() || this.isDisabled) {\r\n      this.messageSent.emit('');\r\n      return;\r\n    }\r\n\r\n    // Add user message to the chat\r\n    this.messages = [\r\n      ...this.messages,\r\n      {\r\n        from: 'user',\r\n        text: this.inputText,\r\n      },\r\n    ];\r\n\r\n    this.shouldScrollToBottom = true;\r\n    this.previousMessagesLength = this.messages.length;\r\n\r\n    // Emit the message to parent component\r\n    const messageText = this.inputText;\r\n    this.inputText = '';\r\n    this.messageSent.emit(messageText);\r\n\r\n    // If parent doesn't handle loading state, show a loading indicator\r\n    if (!this.isLoading) {\r\n      setTimeout(() => {\r\n        // Add a temporary loading message if parent doesn't handle responses\r\n        this.addLoadingIndicator();\r\n      }, 100);\r\n    }\r\n  }\r\n\r\n  handleIconClick(event: { name: string; index: number }): void {\r\n    if (event.name === 'send') {\r\n      this.handleSendMessage();\r\n    } else if (event.name === 'attachment') {\r\n      this.attachmentClicked.emit();\r\n    }\r\n  }\r\n\r\n  addLoadingIndicator(showAlways = false): void {\r\n    // Only add loading indicator if no parent handling\r\n\r\n    const messages: any[] = [\r\n      ...this.messages,\r\n      {\r\n        from: 'ai',\r\n        text: '...',\r\n      },\r\n    ];\r\n\r\n    if (showAlways) {\r\n      this.messages = messages;\r\n    }\r\n\r\n    if (\r\n      !this.isLoading &&\r\n      this.messages[this.messages.length - 1].from === 'user'\r\n    ) {\r\n      this.messages = messages;\r\n      this.shouldScrollToBottom = true;\r\n      this.previousMessagesLength = this.messages.length;\r\n    }\r\n  }\r\n\r\n  // Public method to add an AI response\r\n  addAiResponse(text: string): void {\r\n    // Remove any loading indicators\r\n    this.messages = this.messages.filter((m) => m.text !== '...');\r\n\r\n    // Add the actual response\r\n    this.messages = [\r\n      ...this.messages,\r\n      {\r\n        from: 'ai',\r\n        text: text,\r\n      },\r\n    ];\r\n\r\n    this.shouldScrollToBottom = true;\r\n    this.previousMessagesLength = this.messages.length;\r\n  }\r\n\r\n  // This can be called by parent components to clear the chat\r\n  clearChat(): void {\r\n    this.messages = [];\r\n    this.inputText = '';\r\n    this.previousMessagesLength = 0;\r\n  }\r\n\r\n  uploadFile(file: FileList) {\r\n    const uploadedFiles = [...this.files, ...file];\r\n    const fileSise = uploadedFiles.reduce((a, b) => a + b.size, 0);\r\n\r\n    if (this.maxFileSize && fileSise > this.maxFileSize) {\r\n      this.showWarningPopup = true;\r\n      return;\r\n    }\r\n    this.files = [...uploadedFiles];\r\n    this.attachmentSelected.emit(this.files);\r\n  }\r\n\r\n  deleteFile(name: string) {\r\n    this.files = this.files.filter((file) => file.name !== name);\r\n    this.attachmentSelected.emit(this.files);\r\n  }\r\n}\r\n", "<div class=\"chat-interface-container\">\r\n  <div class=\"messages-scroll-container\">\r\n    <div class=\"messages-container\" #messagesContainer>\r\n      <div\r\n        *ngFor=\"let message of messages\"\r\n        class=\"message-wrapper\"\r\n        [ngClass]=\"message.from\"\r\n      >\r\n        <div class=\"message-bubble\">\r\n          <ng-container *ngIf=\"message.text === '...'\">\r\n            <div class=\"dot-typing\">\r\n              <div class=\"dot\"></div>\r\n              <div class=\"dot\"></div>\r\n              <div class=\"dot\"></div>\r\n            </div>\r\n          </ng-container>\r\n          <ng-container *ngIf=\"message.text !== '...'\">\r\n            {{ message.text }}\r\n          </ng-container>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Space to ensure scrolling reaches the bottom -->\r\n      <div class=\"message-spacer\"></div>\r\n    </div>\r\n  </div>\r\n\r\n  <div class=\"prompt-container\">\r\n    <div class=\"prompt-input-wrapper\">\r\n      <div *ngIf=\"files.length\" class=\"files-container\">\r\n        <div class=\"file-bubble\" *ngFor=\"let file of files\">\r\n          <div class=\"file-icon\">\r\n            {{ file.type.includes(\"zip\") && \"ZIP\" }}\r\n          </div>\r\n          <div class=\"file-details\">\r\n            <div class=\"file-name\">\r\n              {{ file.name }}\r\n            </div>\r\n          </div>\r\n          <button\r\n            class=\"delete-btn\"\r\n            (click)=\"deleteFile(file.name)\"\r\n            aria-label=\"Delete node\"\r\n          >\r\n            <svg\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              width=\"12\"\r\n              height=\"12\"\r\n              viewBox=\"0 0 24 24\"\r\n              fill=\"none\"\r\n              stroke=\"currentColor\"\r\n              stroke-width=\"2\"\r\n              stroke-linecap=\"round\"\r\n              stroke-linejoin=\"round\"\r\n              aria-hidden=\"true\"\r\n            >\r\n              <line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\"></line>\r\n              <line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\"></line>\r\n            </svg>\r\n          </button>\r\n        </div>\r\n      </div>\r\n      <textarea\r\n        class=\"prompt-input\"\r\n        [placeholder]=\"placeholder\"\r\n        [(ngModel)]=\"inputText\"\r\n        [disabled]=\"isDisabled\"\r\n        (keydown.enter)=\"$event.preventDefault(); handleSendMessage()\"\r\n      ></textarea>\r\n\r\n      <div class=\"prompt-actions\">\r\n        <input\r\n          [attr.accept]=\"fileType\"\r\n          [attr.multiple]=\"isMultiple\"\r\n          type=\"file\"\r\n          #fileInput\r\n          hidden\r\n          (change)=\"fileInput.files && uploadFile(fileInput.files)\"\r\n        />\r\n        <button\r\n          class=\"action-icon attachment-icon\"\r\n          (click)=\"\r\n            handleIconClick({ name: 'attachment', index: 0 });\r\n            handleFileUpload && fileInput?.click()\r\n          \"\r\n        >\r\n          <svg\r\n            width=\"24\"\r\n            height=\"24\"\r\n            viewBox=\"0 0 24 24\"\r\n            fill=\"none\"\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n          >\r\n            <path\r\n              d=\"M21.4383 11.6622L12.2483 20.8522C11.1225 21.9781 9.59552 22.6106 8.00334 22.6106C6.41115 22.6106 4.88418 21.9781 3.75834 20.8522C2.63249 19.7264 2 18.1994 2 16.6072C2 15.015 2.63249 13.4881 3.75834 12.3622L12.9483 3.17224C13.697 2.42348 14.7267 2 15.8033 2C16.88 2 17.9097 2.42348 18.6583 3.17224C19.4071 3.92101 19.8306 4.9507 19.8306 6.02735C19.8306 7.10399 19.4071 8.13368 18.6583 8.88245L9.47834 18.0722C9.10396 18.4466 8.58911 18.6584 8.05084 18.6584C7.51257 18.6584 6.99773 18.4466 6.62334 18.0722C6.24896 17.6978 6.03711 17.183 6.03711 16.6447C6.03711 16.1064 6.24896 15.5916 6.62334 15.2172L15.0683 6.78224\"\r\n              stroke=\"#6566CD\"\r\n              stroke-width=\"1.5\"\r\n              stroke-linecap=\"round\"\r\n              stroke-linejoin=\"round\"\r\n            />\r\n          </svg>\r\n        </button>\r\n\r\n        <button\r\n          class=\"action-icon send-icon\"\r\n          (click)=\"handleSendMessage()\"\r\n          [disabled]=\"isLoading || (!isOptionalInput && !inputText.trim())\"\r\n        >\r\n          <svg\r\n            width=\"24\"\r\n            height=\"24\"\r\n            viewBox=\"0 0 24 24\"\r\n            fill=\"none\"\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n          >\r\n            <path\r\n              d=\"M22 2L11 13\"\r\n              stroke=\"#6566CD\"\r\n              stroke-width=\"1.5\"\r\n              stroke-linecap=\"round\"\r\n              stroke-linejoin=\"round\"\r\n            />\r\n            <path\r\n              d=\"M22 2L15 22L11 13L2 9L22 2Z\"\r\n              stroke=\"#6566CD\"\r\n              stroke-width=\"1.5\"\r\n              stroke-linecap=\"round\"\r\n              stroke-linejoin=\"round\"\r\n            />\r\n          </svg>\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<!-- Warning Popup -->\r\n<!-- this popup will be displayed if the files exceeds give maxFIleSize limit -->\r\n<ava-popup\r\n  [show]=\"showWarningPopup\"\r\n  [showTitle]=\"false\"\r\n  [showHeaderIcon]=\"false\"\r\n  [showInlineMessage]=\"true\"\r\n  [inlineIconName]=\"'info'\"\r\n  [inlineIconSize]=\"48\"\r\n  [inlineIconColor]=\"'#007bff'\"\r\n  [inlineMessage]=\"'Warning!'\"\r\n  [message]=\"\r\n    'The total size of selected files exceeds {{fileSize}}. Please select files totaling less than {{fileSize}}.'\r\n  \"\r\n  [showClose]=\"true\"\r\n  [showCancel]=\"true\"\r\n  [cancelButtonLabel]=\"'Okay'\"\r\n  [cancelButtonVariant]=\"'primary'\"\r\n  [popupWidth]=\"'400px'\"\r\n  (closed)=\"showWarningPopup = false\"\r\n>\r\n</ava-popup>\r\n<!--  -->\r\n"], "mappings": "AAAA,SAIEA,YAAY,QAQP,eAAe;AACtB,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,wBAAwB;;;;;;;ICP7CC,EAAA,CAAAC,uBAAA,GAA6C;IAC3CD,EAAA,CAAAE,cAAA,cAAwB;IAGtBF,EAFA,CAAAG,SAAA,cAAuB,cACA,cACA;IACzBH,EAAA,CAAAI,YAAA,EAAM;;;;;;IAERJ,EAAA,CAAAC,uBAAA,GAA6C;IAC3CD,EAAA,CAAAK,MAAA,GACF;;;;;IADEL,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAC,UAAA,CAAAC,IAAA,MACF;;;;;IAVFT,EALF,CAAAE,cAAA,cAIC,cAC6B;IAQ1BF,EAPA,CAAAU,UAAA,IAAAC,oDAAA,2BAA6C,IAAAC,oDAAA,2BAOA;IAIjDZ,EADE,CAAAI,YAAA,EAAM,EACF;;;;IAdJJ,EAAA,CAAAa,UAAA,YAAAL,UAAA,CAAAM,IAAA,CAAwB;IAGPd,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAAa,UAAA,SAAAL,UAAA,CAAAC,IAAA,WAA4B;IAO5BT,EAAA,CAAAM,SAAA,EAA4B;IAA5BN,EAAA,CAAAa,UAAA,SAAAL,UAAA,CAAAC,IAAA,WAA4B;;;;;;IAe3CT,EADF,CAAAE,cAAA,cAAoD,cAC3B;IACrBF,EAAA,CAAAK,MAAA,GACF;IAAAL,EAAA,CAAAI,YAAA,EAAM;IAEJJ,EADF,CAAAE,cAAA,cAA0B,cACD;IACrBF,EAAA,CAAAK,MAAA,GACF;IACFL,EADE,CAAAI,YAAA,EAAM,EACF;IACNJ,EAAA,CAAAE,cAAA,iBAIC;IAFCF,EAAA,CAAAe,UAAA,mBAAAC,oEAAA;MAAA,MAAAC,OAAA,GAAAjB,EAAA,CAAAkB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASF,MAAA,CAAAG,UAAA,CAAAP,OAAA,CAAAQ,IAAA,CAAqB;IAAA,EAAC;;IAG/BzB,EAAA,CAAAE,cAAA,cAWC;IAECF,EADA,CAAAG,SAAA,eAA2C,eACA;IAGjDH,EAFI,CAAAI,YAAA,EAAM,EACC,EACL;;;;IA5BFJ,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAU,OAAA,CAAAS,IAAA,CAAAC,QAAA,sBACF;IAGI3B,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAU,OAAA,CAAAQ,IAAA,MACF;;;;;IARNzB,EAAA,CAAAE,cAAA,cAAkD;IAChDF,EAAA,CAAAU,UAAA,IAAAkB,2CAAA,mBAAoD;IA+BtD5B,EAAA,CAAAI,YAAA,EAAM;;;;IA/BsCJ,EAAA,CAAAM,SAAA,EAAQ;IAARN,EAAA,CAAAa,UAAA,YAAAQ,MAAA,CAAAQ,KAAA,CAAQ;;;ADL1D,WAAaC,sBAAsB;EAA7B,MAAOA,sBAAsB;IAGxBC,KAAK,GAAqB,OAAO;IACjCC,WAAW,GAAW,UAAU;IAChCC,QAAQ,GAAkB,EAAE;IAC5BC,SAAS,GAAY,KAAK;IAC1BC,UAAU,GAAY,KAAK;IAC3BC,eAAe,GAAG,KAAK;IACvBC,gBAAgB,GAAG,KAAK;IACxBC,QAAQ,GAAG,EAAE;IACbC,WAAW,GAAkB,IAAI,CAAC,CAAC;IACnCC,UAAU,GAAG,KAAK;IAEjBC,WAAW,GAAG,IAAI7C,YAAY,EAAU;IACxC8C,iBAAiB,GAAG,IAAI9C,YAAY,EAAQ;IAC5C+C,kBAAkB,GAAG,IAAI/C,YAAY,EAAU;IAEzDgD,QAAQ,GAAG,EAAE;IAEmBC,iBAAiB;IAEjDhB,KAAK,GAAW,EAAE;IAClBiB,gBAAgB,GAAG,KAAK;IAEfC,SAAS,GAAW,EAAE;IACvBC,oBAAoB,GAAY,KAAK;IACrCC,sBAAsB,GAAG,CAAC;IAElC;IACAC,UAAU,GAAG,CACX;MAAEzB,IAAI,EAAE,YAAY;MAAE0B,OAAO,EAAE;IAAgB,CAAE,EACjD;MAAE1B,IAAI,EAAE,MAAM;MAAE0B,OAAO,EAAE;IAAc,CAAE,CAC1C;IAEDC,YAAA,GAAe;IAEfC,QAAQA,CAAA;MACN;MACA,IAAI,IAAI,CAACd,WAAW,EAAE;QACpB,IAAI,CAACK,QAAQ,GAAG,GAAG,IAAI,CAACL,WAAW,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI;MACzD;MACA,IAAI,IAAI,CAACN,QAAQ,CAACqB,MAAM,KAAK,CAAC,EAAE;QAC9B,IAAI,CAACrB,QAAQ,GAAG,CACd;UACEnB,IAAI,EAAE,IAAI;UACVL,IAAI,EAAE;SACP,CACF;MACH;MAEA;MACA,IAAI,CAACuC,oBAAoB,GAAG,IAAI;MAChCO,UAAU,CAAC,MAAM,IAAI,CAACC,cAAc,EAAE,EAAE,GAAG,CAAC;IAC9C;IAEAC,WAAWA,CAACC,OAAsB;MAChC;MACA,IACEA,OAAO,CAAC,UAAU,CAAC,IACnB,CAACA,OAAO,CAAC,UAAU,CAAC,CAACC,WAAW,IAChC,IAAI,CAAC1B,QAAQ,CAACqB,MAAM,KAAK,IAAI,CAACL,sBAAsB,EACpD;QACA,IAAI,CAACD,oBAAoB,GAAG,IAAI;QAChC,IAAI,CAACC,sBAAsB,GAAG,IAAI,CAAChB,QAAQ,CAACqB,MAAM;MACpD;IACF;IAEAM,kBAAkBA,CAAA;MAChB,IAAI,IAAI,CAACZ,oBAAoB,EAAE;QAC7B,IAAI,CAACQ,cAAc,EAAE;QACrB,IAAI,CAACR,oBAAoB,GAAG,KAAK;MACnC;IACF;IAEAQ,cAAcA,CAAA;MACZ,IAAI;QACF,IAAI,IAAI,CAACX,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,CAACgB,aAAa,EAAE;UAClE,IAAI,CAAChB,iBAAiB,CAACgB,aAAa,CAACC,SAAS,GAC5C,IAAI,CAACjB,iBAAiB,CAACgB,aAAa,CAACE,YAAY;QACrD;MACF,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZC,OAAO,CAACC,KAAK,CAAC,4BAA4B,EAAEF,GAAG,CAAC;MAClD;IACF;IAGAG,cAAcA,CAACC,KAAoB;MACjC;MACA,IAAI,CAACA,KAAK,CAACC,QAAQ,EAAE;QACnBD,KAAK,CAACE,cAAc,EAAE;QACtB,IAAI,CAACC,iBAAiB,EAAE;MAC1B;IACF;IAEAA,iBAAiBA,CAAA;MACfhB,UAAU,CAAC,MAAK;QACd,IAAI,CAAC1B,KAAK,GAAG,EAAE;MACjB,CAAC,CAAC;MACF,IAAI,CAAC,IAAI,CAACkB,SAAS,CAACyB,IAAI,EAAE,IAAI,IAAI,CAACrC,UAAU,EAAE;QAC7C,IAAI,CAACM,WAAW,CAACgC,IAAI,CAAC,EAAE,CAAC;QACzB;MACF;MAEA;MACA,IAAI,CAACxC,QAAQ,GAAG,CACd,GAAG,IAAI,CAACA,QAAQ,EAChB;QACEnB,IAAI,EAAE,MAAM;QACZL,IAAI,EAAE,IAAI,CAACsC;OACZ,CACF;MAED,IAAI,CAACC,oBAAoB,GAAG,IAAI;MAChC,IAAI,CAACC,sBAAsB,GAAG,IAAI,CAAChB,QAAQ,CAACqB,MAAM;MAElD;MACA,MAAMoB,WAAW,GAAG,IAAI,CAAC3B,SAAS;MAClC,IAAI,CAACA,SAAS,GAAG,EAAE;MACnB,IAAI,CAACN,WAAW,CAACgC,IAAI,CAACC,WAAW,CAAC;MAElC;MACA,IAAI,CAAC,IAAI,CAACxC,SAAS,EAAE;QACnBqB,UAAU,CAAC,MAAK;UACd;UACA,IAAI,CAACoB,mBAAmB,EAAE;QAC5B,CAAC,EAAE,GAAG,CAAC;MACT;IACF;IAEAC,eAAeA,CAACR,KAAsC;MACpD,IAAIA,KAAK,CAAC3C,IAAI,KAAK,MAAM,EAAE;QACzB,IAAI,CAAC8C,iBAAiB,EAAE;MAC1B,CAAC,MAAM,IAAIH,KAAK,CAAC3C,IAAI,KAAK,YAAY,EAAE;QACtC,IAAI,CAACiB,iBAAiB,CAAC+B,IAAI,EAAE;MAC/B;IACF;IAEAE,mBAAmBA,CAACE,UAAU,GAAG,KAAK;MACpC;MAEA,MAAM5C,QAAQ,GAAU,CACtB,GAAG,IAAI,CAACA,QAAQ,EAChB;QACEnB,IAAI,EAAE,IAAI;QACVL,IAAI,EAAE;OACP,CACF;MAED,IAAIoE,UAAU,EAAE;QACd,IAAI,CAAC5C,QAAQ,GAAGA,QAAQ;MAC1B;MAEA,IACE,CAAC,IAAI,CAACC,SAAS,IACf,IAAI,CAACD,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAACqB,MAAM,GAAG,CAAC,CAAC,CAACxC,IAAI,KAAK,MAAM,EACvD;QACA,IAAI,CAACmB,QAAQ,GAAGA,QAAQ;QACxB,IAAI,CAACe,oBAAoB,GAAG,IAAI;QAChC,IAAI,CAACC,sBAAsB,GAAG,IAAI,CAAChB,QAAQ,CAACqB,MAAM;MACpD;IACF;IAEA;IACAwB,aAAaA,CAACrE,IAAY;MACxB;MACA,IAAI,CAACwB,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC8C,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACvE,IAAI,KAAK,KAAK,CAAC;MAE7D;MACA,IAAI,CAACwB,QAAQ,GAAG,CACd,GAAG,IAAI,CAACA,QAAQ,EAChB;QACEnB,IAAI,EAAE,IAAI;QACVL,IAAI,EAAEA;OACP,CACF;MAED,IAAI,CAACuC,oBAAoB,GAAG,IAAI;MAChC,IAAI,CAACC,sBAAsB,GAAG,IAAI,CAAChB,QAAQ,CAACqB,MAAM;IACpD;IAEA;IACA2B,SAASA,CAAA;MACP,IAAI,CAAChD,QAAQ,GAAG,EAAE;MAClB,IAAI,CAACc,SAAS,GAAG,EAAE;MACnB,IAAI,CAACE,sBAAsB,GAAG,CAAC;IACjC;IAEAiC,UAAUA,CAACC,IAAc;MACvB,MAAMC,aAAa,GAAG,CAAC,GAAG,IAAI,CAACvD,KAAK,EAAE,GAAGsD,IAAI,CAAC;MAC9C,MAAME,QAAQ,GAAGD,aAAa,CAACE,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAACC,IAAI,EAAE,CAAC,CAAC;MAE9D,IAAI,IAAI,CAAClD,WAAW,IAAI8C,QAAQ,GAAG,IAAI,CAAC9C,WAAW,EAAE;QACnD,IAAI,CAACO,gBAAgB,GAAG,IAAI;QAC5B;MACF;MACA,IAAI,CAACjB,KAAK,GAAG,CAAC,GAAGuD,aAAa,CAAC;MAC/B,IAAI,CAACzC,kBAAkB,CAAC8B,IAAI,CAAC,IAAI,CAAC5C,KAAK,CAAC;IAC1C;IAEAL,UAAUA,CAACC,IAAY;MACrB,IAAI,CAACI,KAAK,GAAG,IAAI,CAACA,KAAK,CAACkD,MAAM,CAAEI,IAAI,IAAKA,IAAI,CAAC1D,IAAI,KAAKA,IAAI,CAAC;MAC5D,IAAI,CAACkB,kBAAkB,CAAC8B,IAAI,CAAC,IAAI,CAAC5C,KAAK,CAAC;IAC1C;;uCA3MWC,sBAAsB;IAAA;;YAAtBA,sBAAsB;MAAA4D,SAAA;MAAAC,SAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;UAAtB7F,EAAA,CAAAe,UAAA,2BAAAgF,wDAAAC,MAAA;YAAA,OAAAF,GAAA,CAAA3B,cAAA,CAAA6B,MAAA,CAAsB;UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;UCvB/BhG,EAFJ,CAAAE,cAAA,aAAsC,aACG,gBACc;UACjDF,EAAA,CAAAU,UAAA,IAAAuF,qCAAA,iBAIC;UAgBDjG,EAAA,CAAAG,SAAA,aAAkC;UAEtCH,EADE,CAAAI,YAAA,EAAM,EACF;UAGJJ,EADF,CAAAE,cAAA,aAA8B,aACM;UAChCF,EAAA,CAAAU,UAAA,IAAAwF,qCAAA,iBAAkD;UAiClDlG,EAAA,CAAAE,cAAA,mBAMC;UAHCF,EAAA,CAAAmG,gBAAA,2BAAAC,kEAAAJ,MAAA;YAAAhG,EAAA,CAAAkB,aAAA,CAAAmF,GAAA;YAAArG,EAAA,CAAAsG,kBAAA,CAAAR,GAAA,CAAA/C,SAAA,EAAAiD,MAAA,MAAAF,GAAA,CAAA/C,SAAA,GAAAiD,MAAA;YAAA,OAAAhG,EAAA,CAAAuB,WAAA,CAAAyE,MAAA;UAAA,EAAuB;UAEvBhG,EAAA,CAAAe,UAAA,2BAAAwF,kEAAAP,MAAA;YAAAhG,EAAA,CAAAkB,aAAA,CAAAmF,GAAA;YAAiBL,MAAA,CAAA1B,cAAA,EAAuB;YAAA,OAAAtE,EAAA,CAAAuB,WAAA,CAAEuE,GAAA,CAAAvB,iBAAA,EAAmB;UAAA,EAAC;UAC/DvE,EAAA,CAAAI,YAAA,EAAW;UAGVJ,EADF,CAAAE,cAAA,eAA4B,oBAQxB;UADAF,EAAA,CAAAe,UAAA,oBAAAyF,yDAAA;YAAAxG,EAAA,CAAAkB,aAAA,CAAAmF,GAAA;YAAA,MAAAI,YAAA,GAAAzG,EAAA,CAAA0G,WAAA;YAAA,OAAA1G,EAAA,CAAAuB,WAAA,CAAAkF,YAAA,CAAA5E,KAAA,IAA6BiE,GAAA,CAAAZ,UAAA,CAAAuB,YAAA,CAAA5E,KAAA,CAA2B;UAAA,EAAC;UAN3D7B,EAAA,CAAAI,YAAA,EAOE;UACFJ,EAAA,CAAAE,cAAA,kBAMC;UAJCF,EAAA,CAAAe,UAAA,mBAAA4F,yDAAA;YAAA3G,EAAA,CAAAkB,aAAA,CAAAmF,GAAA;YAAA,MAAAI,YAAA,GAAAzG,EAAA,CAAA0G,WAAA;YACeZ,GAAA,CAAAlB,eAAA,CAAgB;cAAAnD,IAAA,EAAQ,YAAY;cAAAmF,KAAA,EACvD;YAAC,CAAE,CAAC;YAAA,OAAA5G,EAAA,CAAAuB,WAAA,CAAAuE,GAAA,CAAAzD,gBAAA,KAAAoE,YAAA,kBAAAA,YAAA,CAAAI,KAAA;UAAA,EACC;;UAED7G,EAAA,CAAAE,cAAA,eAMC;UACCF,EAAA,CAAAG,SAAA,gBAME;UAENH,EADE,CAAAI,YAAA,EAAM,EACC;;UAETJ,EAAA,CAAAE,cAAA,kBAIC;UAFCF,EAAA,CAAAe,UAAA,mBAAA+F,yDAAA;YAAA9G,EAAA,CAAAkB,aAAA,CAAAmF,GAAA;YAAA,OAAArG,EAAA,CAAAuB,WAAA,CAASuE,GAAA,CAAAvB,iBAAA,EAAmB;UAAA,EAAC;;UAG7BvE,EAAA,CAAAE,cAAA,eAMC;UAQCF,EAPA,CAAAG,SAAA,gBAME,gBAOA;UAMdH,EALU,CAAAI,YAAA,EAAM,EACC,EACL,EACF,EACF,EACF;;UAINJ,EAAA,CAAAE,cAAA,qBAkBC;UADCF,EAAA,CAAAe,UAAA,oBAAAgG,6DAAA;YAAA/G,EAAA,CAAAkB,aAAA,CAAAmF,GAAA;YAAA,OAAArG,EAAA,CAAAuB,WAAA,CAAAuE,GAAA,CAAAhD,gBAAA,GAA6B,KAAK;UAAA,EAAC;UAErC9C,EAAA,CAAAI,YAAA,EAAY;;;UAzJgBJ,EAAA,CAAAM,SAAA,GAAW;UAAXN,EAAA,CAAAa,UAAA,YAAAiF,GAAA,CAAA7D,QAAA,CAAW;UAyB3BjC,EAAA,CAAAM,SAAA,GAAkB;UAAlBN,EAAA,CAAAa,UAAA,SAAAiF,GAAA,CAAAjE,KAAA,CAAAyB,MAAA,CAAkB;UAmCtBtD,EAAA,CAAAM,SAAA,EAA2B;UAA3BN,EAAA,CAAAa,UAAA,gBAAAiF,GAAA,CAAA9D,WAAA,CAA2B;UAC3BhC,EAAA,CAAAgH,gBAAA,YAAAlB,GAAA,CAAA/C,SAAA,CAAuB;UACvB/C,EAAA,CAAAa,UAAA,aAAAiF,GAAA,CAAA3D,UAAA,CAAuB;UAMrBnC,EAAA,CAAAM,SAAA,GAAwB;;UAkCxBN,EAAA,CAAAM,SAAA,GAAiE;UAAjEN,EAAA,CAAAa,UAAA,aAAAiF,GAAA,CAAA5D,SAAA,KAAA4D,GAAA,CAAA1D,eAAA,KAAA0D,GAAA,CAAA/C,SAAA,CAAAyB,IAAA,GAAiE;UAiCzExE,EAAA,CAAAM,SAAA,GAAyB;UAezBN,EAfA,CAAAa,UAAA,SAAAiF,GAAA,CAAAhD,gBAAA,CAAyB,oBACN,yBACK,2BACE,0BACD,sBACJ,8BACQ,6BACD,0HAG3B,mBACiB,oBACC,6BACS,kCACK,uBACX;;;qBDrIZjD,YAAY,EAAAoH,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAEtH,WAAW,EAAAuH,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EAAEzH,cAAc;MAAA0H,MAAA;IAAA;;SAIxC3F,sBAAsB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}