{"ast": null, "code": "import { abs, atan2, cos, epsilon, pi, sign, sin, sqrt } from \"../math.js\";\nimport { conicProjection } from \"./conic.js\";\nimport { equirectangularRaw } from \"./equirectangular.js\";\nexport function conicEquidistantRaw(y0, y1) {\n  var cy0 = cos(y0),\n    n = y0 === y1 ? sin(y0) : (cy0 - cos(y1)) / (y1 - y0),\n    g = cy0 / n + y0;\n  if (abs(n) < epsilon) return equirectangularRaw;\n  function project(x, y) {\n    var gy = g - y,\n      nx = n * x;\n    return [gy * sin(nx), g - gy * cos(nx)];\n  }\n  project.invert = function (x, y) {\n    var gy = g - y,\n      l = atan2(x, abs(gy)) * sign(gy);\n    if (gy * n < 0) l -= pi * sign(x) * sign(gy);\n    return [l / n, g - sign(n) * sqrt(x * x + gy * gy)];\n  };\n  return project;\n}\nexport default function () {\n  return conicProjection(conicEquidistantRaw).scale(131.154).center([0, 13.9389]);\n}", "map": {"version": 3, "names": ["abs", "atan2", "cos", "epsilon", "pi", "sign", "sin", "sqrt", "conicProjection", "equirectangularRaw", "conicEquidistantRaw", "y0", "y1", "cy0", "n", "g", "project", "x", "y", "gy", "nx", "invert", "l", "scale", "center"], "sources": ["C:/console/aava-ui-web/node_modules/d3-geo/src/projection/conicEquidistant.js"], "sourcesContent": ["import {abs, atan2, cos, epsilon, pi, sign, sin, sqrt} from \"../math.js\";\nimport {conicProjection} from \"./conic.js\";\nimport {equirectangularRaw} from \"./equirectangular.js\";\n\nexport function conicEquidistantRaw(y0, y1) {\n  var cy0 = cos(y0),\n      n = y0 === y1 ? sin(y0) : (cy0 - cos(y1)) / (y1 - y0),\n      g = cy0 / n + y0;\n\n  if (abs(n) < epsilon) return equirectangularRaw;\n\n  function project(x, y) {\n    var gy = g - y, nx = n * x;\n    return [gy * sin(nx), g - gy * cos(nx)];\n  }\n\n  project.invert = function(x, y) {\n    var gy = g - y,\n        l = atan2(x, abs(gy)) * sign(gy);\n    if (gy * n < 0)\n      l -= pi * sign(x) * sign(gy);\n    return [l / n, g - sign(n) * sqrt(x * x + gy * gy)];\n  };\n\n  return project;\n}\n\nexport default function() {\n  return conicProjection(conicEquidistantRaw)\n      .scale(131.154)\n      .center([0, 13.9389]);\n}\n"], "mappings": "AAAA,SAAQA,GAAG,EAAEC,KAAK,EAAEC,GAAG,EAAEC,OAAO,EAAEC,EAAE,EAAEC,IAAI,EAAEC,GAAG,EAAEC,IAAI,QAAO,YAAY;AACxE,SAAQC,eAAe,QAAO,YAAY;AAC1C,SAAQC,kBAAkB,QAAO,sBAAsB;AAEvD,OAAO,SAASC,mBAAmBA,CAACC,EAAE,EAAEC,EAAE,EAAE;EAC1C,IAAIC,GAAG,GAAGX,GAAG,CAACS,EAAE,CAAC;IACbG,CAAC,GAAGH,EAAE,KAAKC,EAAE,GAAGN,GAAG,CAACK,EAAE,CAAC,GAAG,CAACE,GAAG,GAAGX,GAAG,CAACU,EAAE,CAAC,KAAKA,EAAE,GAAGD,EAAE,CAAC;IACrDI,CAAC,GAAGF,GAAG,GAAGC,CAAC,GAAGH,EAAE;EAEpB,IAAIX,GAAG,CAACc,CAAC,CAAC,GAAGX,OAAO,EAAE,OAAOM,kBAAkB;EAE/C,SAASO,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;IACrB,IAAIC,EAAE,GAAGJ,CAAC,GAAGG,CAAC;MAAEE,EAAE,GAAGN,CAAC,GAAGG,CAAC;IAC1B,OAAO,CAACE,EAAE,GAAGb,GAAG,CAACc,EAAE,CAAC,EAAEL,CAAC,GAAGI,EAAE,GAAGjB,GAAG,CAACkB,EAAE,CAAC,CAAC;EACzC;EAEAJ,OAAO,CAACK,MAAM,GAAG,UAASJ,CAAC,EAAEC,CAAC,EAAE;IAC9B,IAAIC,EAAE,GAAGJ,CAAC,GAAGG,CAAC;MACVI,CAAC,GAAGrB,KAAK,CAACgB,CAAC,EAAEjB,GAAG,CAACmB,EAAE,CAAC,CAAC,GAAGd,IAAI,CAACc,EAAE,CAAC;IACpC,IAAIA,EAAE,GAAGL,CAAC,GAAG,CAAC,EACZQ,CAAC,IAAIlB,EAAE,GAAGC,IAAI,CAACY,CAAC,CAAC,GAAGZ,IAAI,CAACc,EAAE,CAAC;IAC9B,OAAO,CAACG,CAAC,GAAGR,CAAC,EAAEC,CAAC,GAAGV,IAAI,CAACS,CAAC,CAAC,GAAGP,IAAI,CAACU,CAAC,GAAGA,CAAC,GAAGE,EAAE,GAAGA,EAAE,CAAC,CAAC;EACrD,CAAC;EAED,OAAOH,OAAO;AAChB;AAEA,eAAe,YAAW;EACxB,OAAOR,eAAe,CAACE,mBAAmB,CAAC,CACtCa,KAAK,CAAC,OAAO,CAAC,CACdC,MAAM,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;AAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}