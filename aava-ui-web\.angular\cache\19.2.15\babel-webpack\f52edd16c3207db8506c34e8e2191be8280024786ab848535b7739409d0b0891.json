{"ast": null, "code": "function responseArrayBuffer(response) {\n  if (!response.ok) throw new Error(response.status + \" \" + response.statusText);\n  return response.arrayBuffer();\n}\nexport default function (input, init) {\n  return fetch(input, init).then(responseArrayBuffer);\n}", "map": {"version": 3, "names": ["responseA<PERSON>y<PERSON><PERSON><PERSON>", "response", "ok", "Error", "status", "statusText", "arrayBuffer", "input", "init", "fetch", "then"], "sources": ["C:/console/aava-ui-web/node_modules/d3-fetch/src/buffer.js"], "sourcesContent": ["function responseArrayBuffer(response) {\n  if (!response.ok) throw new Error(response.status + \" \" + response.statusText);\n  return response.arrayBuffer();\n}\n\nexport default function(input, init) {\n  return fetch(input, init).then(responseArrayBuffer);\n}\n"], "mappings": "AAAA,SAASA,mBAAmBA,CAACC,QAAQ,EAAE;EACrC,IAAI,CAACA,QAAQ,CAACC,EAAE,EAAE,MAAM,IAAIC,KAAK,CAACF,QAAQ,CAACG,MAAM,GAAG,GAAG,GAAGH,QAAQ,CAACI,UAAU,CAAC;EAC9E,OAAOJ,QAAQ,CAACK,WAAW,CAAC,CAAC;AAC/B;AAEA,eAAe,UAASC,KAAK,EAAEC,IAAI,EAAE;EACnC,OAAOC,KAAK,CAACF,KAAK,EAAEC,IAAI,CAAC,CAACE,IAAI,CAACV,mBAAmB,CAAC;AACrD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}