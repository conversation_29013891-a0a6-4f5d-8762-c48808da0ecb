{"ast": null, "code": "import { monthsInQuarter } from \"./constants.js\";\n\n/**\n * @name quartersToMonths\n * @category Conversion Helpers\n * @summary Convert number of quarters to months.\n *\n * @description\n * Convert a number of quarters to a full number of months.\n *\n * @param quarters - The number of quarters to be converted\n *\n * @returns The number of quarters converted in months\n *\n * @example\n * // Convert 2 quarters to months\n * const result = quartersToMonths(2)\n * //=> 6\n */\nexport function quartersToMonths(quarters) {\n  return Math.trunc(quarters * monthsInQuarter);\n}\n\n// Fallback for modularized imports:\nexport default quartersToMonths;", "map": {"version": 3, "names": ["monthsInQuarter", "quartersToMonths", "quarters", "Math", "trunc"], "sources": ["C:/console/aava-ui-web/node_modules/date-fns/quartersToMonths.js"], "sourcesContent": ["import { monthsInQuarter } from \"./constants.js\";\n\n/**\n * @name quartersToMonths\n * @category Conversion Helpers\n * @summary Convert number of quarters to months.\n *\n * @description\n * Convert a number of quarters to a full number of months.\n *\n * @param quarters - The number of quarters to be converted\n *\n * @returns The number of quarters converted in months\n *\n * @example\n * // Convert 2 quarters to months\n * const result = quartersToMonths(2)\n * //=> 6\n */\nexport function quartersToMonths(quarters) {\n  return Math.trunc(quarters * monthsInQuarter);\n}\n\n// Fallback for modularized imports:\nexport default quartersToMonths;\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,gBAAgB;;AAEhD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAACC,QAAQ,EAAE;EACzC,OAAOC,IAAI,CAACC,KAAK,CAACF,QAAQ,GAAGF,eAAe,CAAC;AAC/C;;AAEA;AACA,eAAeC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}