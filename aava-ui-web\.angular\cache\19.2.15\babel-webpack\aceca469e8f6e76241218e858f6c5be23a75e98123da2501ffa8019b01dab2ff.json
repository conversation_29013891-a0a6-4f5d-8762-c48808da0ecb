{"ast": null, "code": "import { TxtCardComponent, CardContentComponent, IconComponent } from \"@ava/play-comp-library\";\nimport * as i0 from \"@angular/core\";\nexport let ApprovalTxtCardComponent = /*#__PURE__*/(() => {\n  class ApprovalTxtCardComponent {\n    title = \"\";\n    value = 0;\n    subtitle = \"\";\n    iconName = \"\";\n    static ɵfac = function ApprovalTxtCardComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ApprovalTxtCardComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ApprovalTxtCardComponent,\n      selectors: [[\"app-approval-txt-card\"]],\n      inputs: {\n        title: \"title\",\n        value: \"value\",\n        subtitle: \"subtitle\",\n        iconName: \"iconName\"\n      },\n      decls: 12,\n      vars: 4,\n      consts: [[\"id\", \"dashboard-txt-card\"], [1, \"dashboard-txt-card-1\"], [1, \"d-flex\", \"flex-column\", \"gap-3\"], [1, \"title-wrapper\"], [\"iconColor\", \"#3B3F46\", 3, \"iconName\"], [1, \"value-wrapper\"]],\n      template: function ApprovalTxtCardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"ava-txt-card\", 1)(2, \"ava-card-content\", 2)(3, \"div\", 3)(4, \"div\");\n          i0.ɵɵtext(5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(6, \"ava-icon\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 5)(8, \"h1\");\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"p\");\n          i0.ɵɵtext(11);\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.title);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"iconName\", ctx.iconName);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.value);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.subtitle);\n        }\n      },\n      dependencies: [TxtCardComponent, CardContentComponent, IconComponent],\n      styles: [\"#dashboard-txt-card .dashboard-txt-card-1 .ava-text-card-wrapper .ava-default-card-container .ava-default-card.default-card.card {\\n  border: none;\\n  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);\\n  height: 300px;\\n  width: 420px;\\n  padding: 24px;\\n  background: linear-gradient(to left, #ffffff, #f0f5ff);\\n}\\n  #dashboard-txt-card .dashboard-txt-card-1 .ava-text-card-wrapper .ava-default-card-container .ava-default-card.default-card.card ava-card-content {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: space-between;\\n  height: 100%;\\n}\\n  #dashboard-txt-card .dashboard-txt-card-1 .ava-text-card-wrapper .ava-default-card-container .ava-default-card.default-card.card ava-card-content .title-wrapper {\\n  display: flex;\\n  align-items: center;\\n  column-gap: 20px;\\n}\\n  #dashboard-txt-card .dashboard-txt-card-1 .ava-text-card-wrapper .ava-default-card-container .ava-default-card.default-card.card ava-card-content .title-wrapper div {\\n  margin: 0px;\\n  font-weight: 900;\\n  font-style: bold;\\n  font-size: 20px;\\n  color: #3B3F46;\\n}\\n  #dashboard-txt-card .dashboard-txt-card-1 .ava-text-card-wrapper .ava-default-card-container .ava-default-card.default-card.card ava-card-content .value-wrapper {\\n  display: flex;\\n  flex-direction: column;\\n  row-gap: 10px;\\n}\\n  #dashboard-txt-card .dashboard-txt-card-1 .ava-text-card-wrapper .ava-default-card-container .ava-default-card.default-card.card ava-card-content .value-wrapper h1 {\\n  margin: 0px;\\n  font-weight: 500;\\n  font-style: Medium;\\n  font-size: 48px;\\n  color: #3B3F46;\\n}\\n  #dashboard-txt-card .dashboard-txt-card-1 .ava-text-card-wrapper .ava-default-card-container .ava-default-card.default-card.card ava-card-content .value-wrapper p {\\n  margin: 0px;\\n  font-weight: 400;\\n  font-style: Regular;\\n  font-size: 16px;\\n  color: #3B3F46;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return ApprovalTxtCardComponent;\n})();", "map": {"version": 3, "names": ["TxtCardComponent", "CardContentComponent", "IconComponent", "ApprovalTxtCardComponent", "title", "value", "subtitle", "iconName", "selectors", "inputs", "decls", "vars", "consts", "template", "ApprovalTxtCardComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵproperty", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\approval\\approval-text-card\\approval-text-card.component.ts", "C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\approval\\approval-text-card\\approval-text-card.component.html"], "sourcesContent": ["import { Component, Input } from '@angular/core';\r\nimport { TxtCardComponent, CardContentComponent, IconComponent } from \"@ava/play-comp-library\";\r\n\r\n@Component({\r\n  selector: 'app-approval-txt-card',\r\n  imports: [TxtCardComponent, CardContentComponent, IconComponent],\r\n  templateUrl: './approval-text-card.component.html',\r\n  styleUrl: './approval-text-card.component.scss'\r\n})\r\nexport class ApprovalTxtCardComponent {\r\n  @Input() title = \"\"\r\n  @Input() value = 0\r\n  @Input() subtitle = \"\"\r\n  @Input() iconName = \"\"\r\n\r\n}", "<div id=\"dashboard-txt-card\">\r\n    <ava-txt-card class=\"dashboard-txt-card-1\">\r\n        <ava-card-content class=\"d-flex flex-column\tgap-3\">\r\n            <div class=\"title-wrapper\">\r\n                <div>{{title}}</div>\r\n                <ava-icon [iconName]=\"iconName\" iconColor=\"#3B3F46\"></ava-icon>\r\n            </div>\r\n            <div class=\"value-wrapper\">\r\n                <h1>{{value}}</h1>\r\n                <p>{{subtitle}}</p>\r\n            </div>\r\n        </ava-card-content>\r\n    </ava-txt-card>\r\n</div>"], "mappings": "AACA,SAASA,gBAAgB,EAAEC,oBAAoB,EAAEC,aAAa,QAAQ,wBAAwB;;AAQ9F,WAAaC,wBAAwB;EAA/B,MAAOA,wBAAwB;IAC1BC,KAAK,GAAG,EAAE;IACVC,KAAK,GAAG,CAAC;IACTC,QAAQ,GAAG,EAAE;IACbC,QAAQ,GAAG,EAAE;;uCAJXJ,wBAAwB;IAAA;;YAAxBA,wBAAwB;MAAAK,SAAA;MAAAC,MAAA;QAAAL,KAAA;QAAAC,KAAA;QAAAC,QAAA;QAAAC,QAAA;MAAA;MAAAG,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCLrBE,EAJhB,CAAAC,cAAA,aAA6B,sBACkB,0BACY,aACpB,UAClB;UAAAD,EAAA,CAAAE,MAAA,GAAS;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACpBH,EAAA,CAAAI,SAAA,kBAA+D;UACnEJ,EAAA,CAAAG,YAAA,EAAM;UAEFH,EADJ,CAAAC,cAAA,aAA2B,SACnB;UAAAD,EAAA,CAAAE,MAAA,GAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,IAAY;UAI/BF,EAJ+B,CAAAG,YAAA,EAAI,EACjB,EACS,EACR,EACb;;;UATeH,EAAA,CAAAK,SAAA,GAAS;UAATL,EAAA,CAAAM,iBAAA,CAAAP,GAAA,CAAAZ,KAAA,CAAS;UACJa,EAAA,CAAAK,SAAA,EAAqB;UAArBL,EAAA,CAAAO,UAAA,aAAAR,GAAA,CAAAT,QAAA,CAAqB;UAG3BU,EAAA,CAAAK,SAAA,GAAS;UAATL,EAAA,CAAAM,iBAAA,CAAAP,GAAA,CAAAX,KAAA,CAAS;UACVY,EAAA,CAAAK,SAAA,GAAY;UAAZL,EAAA,CAAAM,iBAAA,CAAAP,GAAA,CAAAV,QAAA,CAAY;;;qBDJnBN,gBAAgB,EAAEC,oBAAoB,EAAEC,aAAa;MAAAuB,MAAA;IAAA;;SAIpDtB,wBAAwB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}