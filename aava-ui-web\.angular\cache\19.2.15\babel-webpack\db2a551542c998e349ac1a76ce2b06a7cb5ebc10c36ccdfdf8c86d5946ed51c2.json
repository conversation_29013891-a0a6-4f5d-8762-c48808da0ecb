{"ast": null, "code": "import { EventEmitter } from \"@angular/core\";\nimport { CommonModule } from \"@angular/common\";\nimport { FormsModule } from \"@angular/forms\";\nimport { IconComponent } from \"@ava/play-comp-library\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nfunction SearchBar_div_5_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const text_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(text_r1);\n  }\n}\nfunction SearchBar_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9);\n    i0.ɵɵtemplate(2, SearchBar_div_5_span_2_Template, 2, 1, \"span\", 10);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.placeholderTexts);\n  }\n}\nlet SearchBar = /*#__PURE__*/(() => {\n  class SearchBar {\n    placeholderTexts = [\"What you want to do today\", \"How can I make your day productive?\"];\n    searchValue = '';\n    sendClicked = new EventEmitter();\n    constructor() {}\n    onSend() {\n      if (this.searchValue.trim()) {\n        this.sendClicked.emit(this.searchValue);\n      }\n    }\n    static ɵfac = function SearchBar_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SearchBar)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SearchBar,\n      selectors: [[\"app-search-bar\"]],\n      outputs: {\n        sendClicked: \"sendClicked\"\n      },\n      decls: 8,\n      vars: 2,\n      consts: [[1, \"search-container\"], [1, \"search-wrapper\"], [1, \"search-icon\"], [\"iconName\", \"search\", \"iconSize\", \"20px\", \"iconColor\", \"#666D99\", 1, \"search-svg-icon\"], [\"type\", \"text\", \"aria-label\", \"Search\", 1, \"search-input\", 3, \"ngModelChange\", \"ngModel\"], [\"class\", \"animated-placeholder-wrapper\", 4, \"ngIf\"], [1, \"send-button\", 3, \"click\"], [\"iconName\", \"send-horizontal\", \"iconSize\", \"24px\", \"iconColor\", \"black\"], [1, \"animated-placeholder-wrapper\"], [1, \"animated-placeholder\"], [4, \"ngFor\", \"ngForOf\"]],\n      template: function SearchBar_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"ava-icon\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"input\", 4);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SearchBar_Template_input_ngModelChange_4_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchValue, $event) || (ctx.searchValue = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(5, SearchBar_div_5_Template, 3, 1, \"div\", 5);\n          i0.ɵɵelementStart(6, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function SearchBar_Template_button_click_6_listener() {\n            return ctx.onSend();\n          });\n          i0.ɵɵelement(7, \"ava-icon\", 7);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchValue);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.searchValue);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, FormsModule, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgModel, IconComponent],\n      styles: [\".search-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  width: 100%;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: flex-start;\\n  gap: 10px;\\n  border-radius: 16px;\\n  box-sizing: border-box;\\n  margin: 0 auto;\\n  position: relative;\\n  z-index: 2;\\n  overflow: visible;\\n}\\n\\n.search-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  display: flex;\\n  align-items: center;\\n  z-index: 2;\\n}\\n\\n.search-icon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 16px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 1;\\n}\\n\\n.search-svg-icon[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  display: block;\\n}\\n\\n.search-input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 16px 60px 16px 48px;\\n  border-radius: 16px;\\n  background-color: #fff;\\n  color: #666D99;\\n  font-family: Mulish;\\n  font-size: 24px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: normal;\\n  outline: none;\\n  border: double 1px transparent;\\n  background-image: linear-gradient(white, white), linear-gradient(180deg, var(--Brand-Primary-300, #F06896) 0%, var(--Brand-Tertiary-300, #997BCF) 100%);\\n  background-origin: border-box;\\n  background-clip: padding-box, border-box;\\n}\\n\\n.animated-placeholder-wrapper[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 48px;\\n  \\n\\n  top: 50%;\\n  transform: translateY(-50%);\\n  overflow: hidden;\\n  \\n\\n  height: 24px;\\n  \\n\\n  display: flex;\\n  align-items: center;\\n  pointer-events: none;\\n  \\n\\n  width: calc(100% - 48px - 16px);\\n  \\n\\n}\\n\\n.animated-placeholder[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  animation: _ngcontent-%COMP%_slide-up-down 10s ease-in-out infinite;\\n  line-height: 1.2;\\n}\\n\\n.animated-placeholder[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #666D99;\\n  font-family: \\\"Mulish\\\", -apple-system, \\\"Roboto\\\", \\\"Helvetica\\\", sans-serif;\\n  font-size: 20px;\\n  font-weight: 400;\\n  white-space: nowrap;\\n  \\n\\n  \\n\\n  height: 100%;\\n  display: flex;\\n  align-items: center;\\n  \\n\\n  flex-shrink: 0;\\n  \\n\\n  flex-grow: 0;\\n  \\n\\n}\\n\\n@keyframes _ngcontent-%COMP%_slide-up-down {\\n  0%, 40% {\\n    transform: translateY(0%);\\n  }\\n  50%, 90% {\\n    transform: translateY(-100%);\\n  }\\n  100% {\\n    transform: translateY(0%);\\n  }\\n}\\n\\n\\n@media (max-width: 1200px) {\\n  .search-container[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .search-wrapper[_ngcontent-%COMP%] {\\n    max-width: 100%;\\n  }\\n  .search-container[_ngcontent-%COMP%] {\\n    height: auto;\\n  }\\n  .search-input[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n  .animated-placeholder[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n  .animated-placeholder-wrapper[_ngcontent-%COMP%] {\\n    height: 22px;\\n    \\n\\n  }\\n  .search-ball-left[_ngcontent-%COMP%], .search-ball-right[_ngcontent-%COMP%] {\\n    width: 60px;\\n  }\\n}\\nawe-icons[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  margin-top: 10px;\\n}\\n\\n.send-button[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 16px;\\n  top: 49%;\\n  transform: translateY(-50%);\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 10;\\n  padding: 8px;\\n  border-radius: 50%;\\n  transition: background-color 0.2s ease;\\n}\\n.send-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 0, 0, 0.05);\\n}\\n.send-button[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  background-color: rgba(0, 0, 0, 0.1);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return SearchBar;\n})();\nexport { SearchBar as default };", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "IconComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "text_r1", "ɵɵtemplate", "SearchBar_div_5_span_2_Template", "ɵɵproperty", "ctx_r1", "placeholderTexts", "SearchBar", "searchValue", "sendClicked", "constructor", "onSend", "trim", "emit", "selectors", "outputs", "decls", "vars", "consts", "template", "SearchBar_Template", "rf", "ctx", "ɵɵelement", "ɵɵtwoWayListener", "SearchBar_Template_input_ngModelChange_4_listener", "$event", "ɵɵtwoWayBindingSet", "SearchBar_div_5_Template", "ɵɵlistener", "SearchBar_Template_button_click_6_listener", "ɵɵtwoWayProperty", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles", "default"], "sources": ["C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\shared\\components\\search-bar\\search-bar.component.ts", "C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\shared\\components\\search-bar\\search-bar.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy, Output, EventEmitter } from \"@angular/core\";\r\nimport { CommonModule } from \"@angular/common\";\r\nimport { FormsModule } from \"@angular/forms\";\r\nimport { IconComponent } from \"@ava/play-comp-library\";\r\n\r\n@Component({\r\n  selector: 'app-search-bar',\r\n  templateUrl: './search-bar.component.html',\r\n  styleUrls: ['./search-bar.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule, IconComponent],\r\n})\r\nexport default class SearchBar {\r\n  placeholderTexts: string[] = [\r\n    \"What you want to do today\",\r\n    \"How can I make your day productive?\"\r\n  ];\r\n  searchValue: string = '';\r\n  \r\n  @Output() sendClicked = new EventEmitter<string>();\r\n\r\n  constructor() {}\r\n\r\n  onSend() {\r\n    if (this.searchValue.trim()) {\r\n      this.sendClicked.emit(this.searchValue);\r\n    }\r\n  }\r\n}", "<div class=\"search-container\">\r\n  <div class=\"search-wrapper\">\r\n    <div class=\"search-icon\">\r\n      <ava-icon iconName=\"search\" iconSize=\"20px\" iconColor=\"#666D99\" class=\"search-svg-icon\"></ava-icon>\r\n    </div>\r\n\r\n    <input type=\"text\" class=\"search-input\" aria-label=\"Search\" [(ngModel)]=\"searchValue\">\r\n    <div class=\"animated-placeholder-wrapper\" *ngIf=\"!searchValue\">\r\n      <div class=\"animated-placeholder\">\r\n        <span *ngFor=\"let text of placeholderTexts\">{{ text }}</span>\r\n      </div>\r\n    </div>\r\n    \r\n    <!-- Send Button -->\r\n    <button class=\"send-button\" (click)=\"onSend()\">\r\n      <ava-icon iconName=\"send-horizontal\" iconSize=\"24px\" iconColor=\"black\" ></ava-icon>\r\n    </button>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAAA,SAA+CA,YAAY,QAAQ,eAAe;AAClF,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,aAAa,QAAQ,wBAAwB;;;;;;ICM9CC,EAAA,CAAAC,cAAA,WAA4C;IAAAD,EAAA,CAAAE,MAAA,GAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAjBH,EAAA,CAAAI,SAAA,EAAU;IAAVJ,EAAA,CAAAK,iBAAA,CAAAC,OAAA,CAAU;;;;;IADxDN,EADF,CAAAC,cAAA,aAA+D,aAC3B;IAChCD,EAAA,CAAAO,UAAA,IAAAC,+BAAA,mBAA4C;IAEhDR,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAFqBH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAS,UAAA,YAAAC,MAAA,CAAAC,gBAAA,CAAmB;;;IDG7BC,SAAS;EAAhB,MAAOA,SAAS;IAC5BD,gBAAgB,GAAa,CAC3B,2BAA2B,EAC3B,qCAAqC,CACtC;IACDE,WAAW,GAAW,EAAE;IAEdC,WAAW,GAAG,IAAIlB,YAAY,EAAU;IAElDmB,YAAA,GAAe;IAEfC,MAAMA,CAAA;MACJ,IAAI,IAAI,CAACH,WAAW,CAACI,IAAI,EAAE,EAAE;QAC3B,IAAI,CAACH,WAAW,CAACI,IAAI,CAAC,IAAI,CAACL,WAAW,CAAC;MACzC;IACF;;uCAfmBD,SAAS;IAAA;;YAATA,SAAS;MAAAO,SAAA;MAAAC,OAAA;QAAAN,WAAA;MAAA;MAAAO,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCV1B1B,EAFJ,CAAAC,cAAA,aAA8B,aACA,aACD;UACvBD,EAAA,CAAA4B,SAAA,kBAAmG;UACrG5B,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAAsF;UAA1BD,EAAA,CAAA6B,gBAAA,2BAAAC,kDAAAC,MAAA;YAAA/B,EAAA,CAAAgC,kBAAA,CAAAL,GAAA,CAAAd,WAAA,EAAAkB,MAAA,MAAAJ,GAAA,CAAAd,WAAA,GAAAkB,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAyB;UAArF/B,EAAA,CAAAG,YAAA,EAAsF;UACtFH,EAAA,CAAAO,UAAA,IAAA0B,wBAAA,iBAA+D;UAO/DjC,EAAA,CAAAC,cAAA,gBAA+C;UAAnBD,EAAA,CAAAkC,UAAA,mBAAAC,2CAAA;YAAA,OAASR,GAAA,CAAAX,MAAA,EAAQ;UAAA,EAAC;UAC5ChB,EAAA,CAAA4B,SAAA,kBAAmF;UAGzF5B,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;UAZ0DH,EAAA,CAAAI,SAAA,GAAyB;UAAzBJ,EAAA,CAAAoC,gBAAA,YAAAT,GAAA,CAAAd,WAAA,CAAyB;UAC1Cb,EAAA,CAAAI,SAAA,EAAkB;UAAlBJ,EAAA,CAAAS,UAAA,UAAAkB,GAAA,CAAAd,WAAA,CAAkB;;;qBDGrDhB,YAAY,EAAAwC,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEzC,WAAW,EAAA0C,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EAAE5C,aAAa;MAAA6C,MAAA;IAAA;;SAE/BhC,SAAS;AAAA;AAAA,SAATA,SAAS,IAAAiC,OAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}