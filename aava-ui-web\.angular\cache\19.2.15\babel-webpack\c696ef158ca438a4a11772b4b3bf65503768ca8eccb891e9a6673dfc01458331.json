{"ast": null, "code": "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\nexport var scheme = new Array(3).concat(\"efedf5bcbddc756bb1\", \"f2f0f7cbc9e29e9ac86a51a3\", \"f2f0f7cbc9e29e9ac8756bb154278f\", \"f2f0f7dadaebbcbddc9e9ac8756bb154278f\", \"f2f0f7dadaebbcbddc9e9ac8807dba6a51a34a1486\", \"fcfbfdefedf5dadaebbcbddc9e9ac8807dba6a51a34a1486\", \"fcfbfdefedf5dadaebbcbddc9e9ac8807dba6a51a354278f3f007d\").map(colors);\nexport default ramp(scheme);", "map": {"version": 3, "names": ["colors", "ramp", "scheme", "Array", "concat", "map"], "sources": ["C:/console/aava-ui-web/node_modules/d3-scale-chromatic/src/sequential-single/Purples.js"], "sourcesContent": ["import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"efedf5bcbddc756bb1\",\n  \"f2f0f7cbc9e29e9ac86a51a3\",\n  \"f2f0f7cbc9e29e9ac8756bb154278f\",\n  \"f2f0f7dadaebbcbddc9e9ac8756bb154278f\",\n  \"f2f0f7dadaebbcbddc9e9ac8807dba6a51a34a1486\",\n  \"fcfbfdefedf5dadaebbcbddc9e9ac8807dba6a51a34a1486\",\n  \"fcfbfdefedf5dadaebbcbddc9e9ac8807dba6a51a354278f3f007d\"\n).map(colors);\n\nexport default ramp(scheme);\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,cAAc;AACjC,OAAOC,IAAI,MAAM,YAAY;AAE7B,OAAO,IAAIC,MAAM,GAAG,IAAIC,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,CACrC,oBAAoB,EACpB,0BAA0B,EAC1B,gCAAgC,EAChC,sCAAsC,EACtC,4CAA4C,EAC5C,kDAAkD,EAClD,wDACF,CAAC,CAACC,GAAG,CAACL,MAAM,CAAC;AAEb,eAAeC,IAAI,CAACC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}