{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { Subject, takeUntil, switchMap, finalize, catchError, of } from 'rxjs';\nimport { FormsModule } from '@angular/forms';\n// Import child components\nimport { AgentExecutionPlaygroundComponent } from './components/agent-execution-playground/agent-execution-playground.component';\nimport { IconComponent } from '@ava/play-comp-library';\nimport { environment } from '@shared/environments/environment';\nimport { ExecutionStatus } from '@shared/models/execution.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../services/agent-service.service\";\nimport * as i3 from \"../build-agents/services/agent-playground.service\";\nimport * as i4 from \"@shared/auth/services/token-storage.service\";\nimport * as i5 from \"@shared/services/loader/loader.service\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"@shared/services/tool-execution/tool-execution.service\";\nimport * as i8 from \"@angular/common\";\nfunction AgentExecutionComponent_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 19);\n    i0.ɵɵtext(1, \" History \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"app-agent-execution-playground\", 20);\n    i0.ɵɵlistener(\"promptChange\", function AgentExecutionComponent_div_13_Template_app_agent_execution_playground_promptChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPromptChanged($event));\n    })(\"messageSent\", function AgentExecutionComponent_div_13_Template_app_agent_execution_playground_messageSent_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.handleChatMessage($event));\n    })(\"conversationalToggle\", function AgentExecutionComponent_div_13_Template_app_agent_execution_playground_conversationalToggle_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPlaygroundConversationalToggle($event));\n    })(\"templateToggle\", function AgentExecutionComponent_div_13_Template_app_agent_execution_playground_templateToggle_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPlaygroundTemplateToggle($event));\n    })(\"filesSelected\", function AgentExecutionComponent_div_13_Template_app_agent_execution_playground_filesSelected_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFilesSelected($event));\n    })(\"approvalRequested\", function AgentExecutionComponent_div_13_Template_app_agent_execution_playground_approvalRequested_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onApprovalRequested());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"messages\", ctx_r1.chatMessages)(\"isLoading\", ctx_r1.isProcessingChat)(\"agentType\", ctx_r1.agentType)(\"showChatInteractionToggles\", ctx_r1.agentType === \"individual\")(\"showAiPrincipleToggle\", false)(\"showApprovalButton\", false)(\"showDropdown\", false)(\"showAgentNameInput\", true)(\"showFileUploadButton\", true)(\"showStatusOnly\", true)(\"displayedAgentName\", ctx_r1.agentName)(\"agentNamePlaceholder\", \"Current Agent Name\");\n  }\n}\nfunction AgentExecutionComponent_div_22_div_40_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵtext(1, \" No prompt configured \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_22_div_40_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"span\", 77);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(node_r4.name || \"Prompt\");\n  }\n}\nfunction AgentExecutionComponent_div_22_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_22_div_40_div_1_Template, 2, 0, \"div\", 72);\n    i0.ɵɵelementStart(2, \"div\", 73);\n    i0.ɵɵtemplate(3, AgentExecutionComponent_div_22_div_40_div_3_Template, 3, 1, \"div\", 74);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.blueprintPromptNodes.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.blueprintPromptNodes);\n  }\n}\nfunction AgentExecutionComponent_div_22_div_61_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵtext(1, \" No knowledge base configured \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_22_div_61_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"span\", 77);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(node_r5.name || \"Knowledge Base\");\n  }\n}\nfunction AgentExecutionComponent_div_22_div_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_22_div_61_div_1_Template, 2, 0, \"div\", 72);\n    i0.ɵɵelementStart(2, \"div\", 73);\n    i0.ɵɵtemplate(3, AgentExecutionComponent_div_22_div_61_div_3_Template, 3, 1, \"div\", 74);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.blueprintKnowledgeNodes.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.blueprintKnowledgeNodes);\n  }\n}\nfunction AgentExecutionComponent_div_22_div_79_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵtext(1, \" No model configured \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_22_div_79_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"span\", 77);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(node_r6.name || \"Model\");\n  }\n}\nfunction AgentExecutionComponent_div_22_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_22_div_79_div_1_Template, 2, 0, \"div\", 72);\n    i0.ɵɵelementStart(2, \"div\", 73);\n    i0.ɵɵtemplate(3, AgentExecutionComponent_div_22_div_79_div_3_Template, 3, 1, \"div\", 74);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.blueprintModelNodes.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.blueprintModelNodes);\n  }\n}\nfunction AgentExecutionComponent_div_22_div_80__svg_svg_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 45);\n    i0.ɵɵelement(1, \"rect\", 80)(2, \"path\", 81);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_22_div_80_div_13_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵtext(1, \" No tools configured \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_22_div_80_div_13_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"span\", 77);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r8 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(node_r8.name || \"Tool\");\n  }\n}\nfunction AgentExecutionComponent_div_22_div_80_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_22_div_80_div_13_div_1_Template, 2, 0, \"div\", 72);\n    i0.ɵɵelementStart(2, \"div\", 73);\n    i0.ɵɵtemplate(3, AgentExecutionComponent_div_22_div_80_div_13_div_3_Template, 3, 1, \"div\", 74);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.blueprintToolNodes.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.blueprintToolNodes);\n  }\n}\nfunction AgentExecutionComponent_div_22_div_80_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"div\", 42);\n    i0.ɵɵlistener(\"click\", function AgentExecutionComponent_div_22_div_80_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleBlueprintZone(\"tool\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 43)(3, \"div\", 44);\n    i0.ɵɵtemplate(4, AgentExecutionComponent_div_22_div_80__svg_svg_4_Template, 3, 0, \"svg\", 79);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h3\", 48);\n    i0.ɵɵtext(6, \"Tools\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 49)(8, \"span\", 63);\n    i0.ɵɵtext(9, \"Optional\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 51);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(11, \"svg\", 52);\n    i0.ɵɵelement(12, \"path\", 53);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(13, AgentExecutionComponent_div_22_div_80_div_13_Template, 4, 2, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"has-nodes\", ctx_r1.blueprintToolNodes.length > 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.agentType === \"collaborative\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵstyleProp(\"transform\", ctx_r1.isBlueprintZoneExpanded(\"tool\") ? \"rotate(180deg)\" : \"rotate(0deg)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isBlueprintZoneExpanded(\"tool\"));\n  }\n}\nfunction AgentExecutionComponent_div_22_div_81_div_15_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵtext(1, \" No guardrails configured \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_22_div_81_div_15_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"span\", 77);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r10 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(node_r10.name || \"Guardrail\");\n  }\n}\nfunction AgentExecutionComponent_div_22_div_81_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_22_div_81_div_15_div_1_Template, 2, 0, \"div\", 72);\n    i0.ɵɵelementStart(2, \"div\", 73);\n    i0.ɵɵtemplate(3, AgentExecutionComponent_div_22_div_81_div_15_div_3_Template, 3, 1, \"div\", 74);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.blueprintGuardrailNodes.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.blueprintGuardrailNodes);\n  }\n}\nfunction AgentExecutionComponent_div_22_div_81_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 82)(1, \"div\", 42);\n    i0.ɵɵlistener(\"click\", function AgentExecutionComponent_div_22_div_81_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleBlueprintZone(\"guardrail\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 43)(3, \"div\", 44);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(4, \"svg\", 45);\n    i0.ɵɵelement(5, \"rect\", 83)(6, \"path\", 84);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(7, \"h3\", 48);\n    i0.ɵɵtext(8, \"Guardrails\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 49)(10, \"span\", 63);\n    i0.ɵɵtext(11, \"Optional\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 51);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(13, \"svg\", 52);\n    i0.ɵɵelement(14, \"path\", 53);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(15, AgentExecutionComponent_div_22_div_81_div_15_Template, 4, 2, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"has-nodes\", ctx_r1.blueprintGuardrailNodes.length > 0);\n    i0.ɵɵadvance(13);\n    i0.ɵɵstyleProp(\"transform\", ctx_r1.isBlueprintZoneExpanded(\"guardrail\") ? \"rotate(180deg)\" : \"rotate(0deg)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isBlueprintZoneExpanded(\"guardrail\"));\n  }\n}\nfunction AgentExecutionComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22)(2, \"h3\");\n    i0.ɵɵtext(3, \"Agent Blueprint\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 23);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(5, \"svg\", 24);\n    i0.ɵɵelement(6, \"line\", 25)(7, \"line\", 26)(8, \"line\", 27)(9, \"line\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(10, \"div\", 29)(11, \"div\", 30);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(12, \"svg\", 31)(13, \"defs\")(14, \"linearGradient\", 32);\n    i0.ɵɵelement(15, \"stop\", 33)(16, \"stop\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(17, \"circle\", 35)(18, \"circle\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(19, \"div\", 37)(20, \"div\", 38);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 39);\n    i0.ɵɵtext(23, \"Complete\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(24, \"div\", 40)(25, \"div\", 41)(26, \"div\", 42);\n    i0.ɵɵlistener(\"click\", function AgentExecutionComponent_div_22_Template_div_click_26_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleBlueprintZone(\"prompt\"));\n    });\n    i0.ɵɵelementStart(27, \"div\", 43)(28, \"div\", 44);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(29, \"svg\", 45);\n    i0.ɵɵelement(30, \"rect\", 46)(31, \"path\", 47);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(32, \"h3\", 48);\n    i0.ɵɵtext(33, \"System Prompt\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 49)(35, \"span\", 50);\n    i0.ɵɵtext(36, \"Required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"button\", 51);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(38, \"svg\", 52);\n    i0.ɵɵelement(39, \"path\", 53);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(40, AgentExecutionComponent_div_22_div_40_Template, 4, 2, \"div\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(41, \"div\", 55)(42, \"div\", 42);\n    i0.ɵɵlistener(\"click\", function AgentExecutionComponent_div_22_Template_div_click_42_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleBlueprintZone(\"knowledge\"));\n    });\n    i0.ɵɵelementStart(43, \"div\", 43)(44, \"div\", 44);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(45, \"svg\", 45);\n    i0.ɵɵelement(46, \"rect\", 56)(47, \"path\", 57)(48, \"path\", 58)(49, \"path\", 59)(50, \"path\", 60)(51, \"path\", 61)(52, \"path\", 62);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(53, \"h3\", 48);\n    i0.ɵɵtext(54, \"Knowledgebase\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(55, \"div\", 49)(56, \"span\", 63);\n    i0.ɵɵtext(57, \"Optional\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"button\", 51);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(59, \"svg\", 52);\n    i0.ɵɵelement(60, \"path\", 53);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(61, AgentExecutionComponent_div_22_div_61_Template, 4, 2, \"div\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(62, \"div\", 64)(63, \"div\", 42);\n    i0.ɵɵlistener(\"click\", function AgentExecutionComponent_div_22_Template_div_click_63_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleBlueprintZone(\"model\"));\n    });\n    i0.ɵɵelementStart(64, \"div\", 43)(65, \"div\", 44);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(66, \"svg\", 45);\n    i0.ɵɵelement(67, \"rect\", 65)(68, \"path\", 66)(69, \"path\", 67)(70, \"path\", 68);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(71, \"h3\", 48);\n    i0.ɵɵtext(72, \"AI Model\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(73, \"div\", 49)(74, \"span\", 50);\n    i0.ɵɵtext(75, \"Required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(76, \"button\", 51);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(77, \"svg\", 52);\n    i0.ɵɵelement(78, \"path\", 53);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(79, AgentExecutionComponent_div_22_div_79_Template, 4, 2, \"div\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(80, AgentExecutionComponent_div_22_div_80_Template, 14, 6, \"div\", 69)(81, AgentExecutionComponent_div_22_div_81_Template, 16, 5, \"div\", 70);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(18);\n    i0.ɵɵstyleProp(\"stroke-dasharray\", 314)(\"stroke-dashoffset\", 314 - 314 * ctx_r1.blueprintCompletionPercentage / 100);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.blueprintCompletionPercentage, \"% \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"has-nodes\", ctx_r1.blueprintPromptNodes.length > 0);\n    i0.ɵɵadvance(13);\n    i0.ɵɵstyleProp(\"transform\", ctx_r1.isBlueprintZoneExpanded(\"prompt\") ? \"rotate(180deg)\" : \"rotate(0deg)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isBlueprintZoneExpanded(\"prompt\"));\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"has-nodes\", ctx_r1.blueprintKnowledgeNodes.length > 0);\n    i0.ɵɵadvance(18);\n    i0.ɵɵstyleProp(\"transform\", ctx_r1.isBlueprintZoneExpanded(\"knowledge\") ? \"rotate(180deg)\" : \"rotate(0deg)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isBlueprintZoneExpanded(\"knowledge\"));\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"has-nodes\", ctx_r1.blueprintModelNodes.length > 0);\n    i0.ɵɵadvance(15);\n    i0.ɵɵstyleProp(\"transform\", ctx_r1.isBlueprintZoneExpanded(\"model\") ? \"rotate(180deg)\" : \"rotate(0deg)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isBlueprintZoneExpanded(\"model\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.agentType === \"collaborative\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.agentType === \"individual\");\n  }\n}\nfunction AgentExecutionComponent_div_23_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"p\");\n    i0.ɵɵtext(2, \"No agent outputs available yet. Send a message to see the response.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AgentExecutionComponent_div_23_div_5_div_1_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 100)(1, \"div\", 101)(2, \"h4\");\n    i0.ɵɵtext(3, \"Response\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 102);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const execution_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (execution_r11.response == null ? null : execution_r11.response.response == null ? null : execution_r11.response.response.choices == null ? null : execution_r11.response.response.choices[0] == null ? null : execution_r11.response.response.choices[0].text) || (execution_r11.response == null ? null : execution_r11.response.agentResponse == null ? null : execution_r11.response.agentResponse.detail) || \"No response content available.\", \" \");\n  }\n}\nfunction AgentExecutionComponent_div_23_div_5_div_1_div_15_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 109)(1, \"strong\");\n    i0.ɵɵtext(2, \"Description:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const taskOutput_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", taskOutput_r12.description, \" \");\n  }\n}\nfunction AgentExecutionComponent_div_23_div_5_div_1_div_15_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 110)(1, \"strong\");\n    i0.ɵɵtext(2, \"Expected Output:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const taskOutput_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", taskOutput_r12.expected_output, \" \");\n  }\n}\nfunction AgentExecutionComponent_div_23_div_5_div_1_div_15_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 104);\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_23_div_5_div_1_div_15_div_1_div_1_Template, 4, 1, \"div\", 105)(2, AgentExecutionComponent_div_23_div_5_div_1_div_15_div_1_div_2_Template, 4, 1, \"div\", 106);\n    i0.ɵɵelementStart(3, \"div\", 107);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 108)(6, \"h4\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const taskOutput_r12 = ctx.$implicit;\n    const j_r13 = ctx.index;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", taskOutput_r12.description);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", taskOutput_r12.expected_output);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", taskOutput_r12.raw, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Summary: \", taskOutput_r12.summary || \"Task \" + (j_r13 + 1), \"\");\n  }\n}\nfunction AgentExecutionComponent_div_23_div_5_div_1_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 100);\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_23_div_5_div_1_div_15_div_1_Template, 8, 4, \"div\", 103);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const execution_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", execution_r11.response == null ? null : execution_r11.response.agentResponse == null ? null : execution_r11.response.agentResponse.agent == null ? null : execution_r11.response.agentResponse.agent.tasksOutputs);\n  }\n}\nfunction AgentExecutionComponent_div_23_div_5_div_1_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 111)(1, \"div\", 112)(2, \"strong\");\n    i0.ɵɵtext(3, \"Error:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" The agent execution failed. Please try again. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AgentExecutionComponent_div_23_div_5_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 92)(1, \"div\", 93)(2, \"div\", 5);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 94);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 95)(7, \"div\", 96)(8, \"strong\");\n    i0.ɵɵtext(9, \"Query:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 97);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(14, AgentExecutionComponent_div_23_div_5_div_1_div_14_Template, 6, 1, \"div\", 98)(15, AgentExecutionComponent_div_23_div_5_div_1_div_15_Template, 2, 1, \"div\", 98)(16, AgentExecutionComponent_div_23_div_5_div_1_div_16_Template, 5, 0, \"div\", 99);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const execution_r11 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"success\", execution_r11.status === \"success\")(\"failed\", execution_r11.status === \"failed\")(\"pending\", execution_r11.status === \"pending\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(execution_r11.agentName);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(execution_r11.status);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", execution_r11.status === \"success\" ? \"Success\" : execution_r11.status === \"failed\" ? \"Failed\" : \"Pending\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", execution_r11.userMessage, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(13, 15, execution_r11.timestamp, \"medium\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", execution_r11.status === \"success\" && execution_r11.response && ctx_r1.agentType === \"individual\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", execution_r11.status === \"success\" && execution_r11.response && ctx_r1.agentType === \"collaborative\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", execution_r11.status === \"failed\");\n  }\n}\nfunction AgentExecutionComponent_div_23_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 90);\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_23_div_5_div_1_Template, 17, 18, \"div\", 91);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.executionHistory);\n  }\n}\nfunction AgentExecutionComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 85)(1, \"h3\");\n    i0.ɵɵtext(2, \"Agent Output\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 86);\n    i0.ɵɵtemplate(4, AgentExecutionComponent_div_23_div_4_Template, 3, 0, \"div\", 87)(5, AgentExecutionComponent_div_23_div_5_Template, 2, 1, \"div\", 88);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.executionHistory.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.executionHistory.length > 0);\n  }\n}\n// Remove duplicate definitions - they're now in shared models\nexport let AgentExecutionComponent = /*#__PURE__*/(() => {\n  class AgentExecutionComponent {\n    route;\n    router;\n    agentService;\n    agentPlaygroundService;\n    tokenStorage;\n    loaderService;\n    formBuilder;\n    toolExecutionService;\n    navigationTabs = [{\n      id: 'nav-home',\n      label: 'Agent Activity'\n    }, {\n      id: 'nav-products',\n      label: 'Agent Output'\n    }, {\n      id: 'nav-services',\n      label: 'Preview',\n      disabled: true\n    }];\n    // Agent details\n    agentId = null;\n    agentType = 'individual';\n    agentName = 'Agent';\n    agentDetail = '';\n    playgroundComp;\n    // Activity logs\n    activityLogs = [];\n    activityProgress = 0;\n    executionDetails;\n    isRunning = false;\n    status = ExecutionStatus.notStarted;\n    // Chat messages\n    chatMessages = [];\n    isProcessingChat = false;\n    inputText = '';\n    // Agent outputs\n    agentOutputs = [];\n    latestAgentResponse = null; // Store the latest agent response for display\n    // New properties for execution history\n    executionHistory = [];\n    agentForm;\n    // Execution state\n    executionStartTime = null;\n    executionCompleted = false;\n    executionId;\n    enableStreamingLog = environment.enableLogStreaming || 'all';\n    isExecutionComplete = false;\n    progressInterval;\n    destroy$ = new Subject();\n    selectedTab = 'Agent Activity';\n    demoTabs = [{\n      id: 'tab1',\n      label: 'History'\n    }, {\n      id: 'tab2',\n      label: 'Blueprint'\n    }, {\n      id: 'tab3',\n      label: 'Agent Output'\n    }];\n    errorMsg = false;\n    resMessage;\n    taskMessage = [];\n    isJsonValid = false;\n    disableChat = false;\n    selectedFiles = [];\n    agentNodes = [];\n    userInputList = [];\n    progress = 0;\n    isLoading = false;\n    loaderColor = '';\n    inputFieldOrder = [];\n    currentInputIndex = 0;\n    activeTabId = 'nav-home';\n    // Panel state properties\n    isLeftPanelCollapsed = false;\n    activeRightTab = 'blueprint';\n    // Agent-specific properties\n    currentAgentDetails = null;\n    buildAgentNodes = [];\n    canvasNodes = [];\n    canvasEdges = [];\n    selectedPrompt = '';\n    selectedAgentMode = '';\n    selectedUseCaseIdentifier = '';\n    agentFilesUploadedData = [];\n    agentAttachment = [];\n    isAgentPlaygroundLoading = false;\n    agentPlaygroundDestroy = new Subject();\n    agentChatPayload = [];\n    agentCode = '';\n    promptOptions = [];\n    // Custom Blueprint Display Properties\n    blueprintCompletionPercentage = 0;\n    blueprintPromptNodes = [];\n    blueprintModelNodes = [];\n    blueprintKnowledgeNodes = [];\n    blueprintGuardrailNodes = [];\n    blueprintToolNodes = [];\n    // Blueprint zone expansion state\n    blueprintZonesExpanded = {\n      prompt: true,\n      model: true,\n      knowledge: true,\n      guardrail: true,\n      tool: true\n    };\n    // Blueprint panel properties (using existing arrays above)\n    constructor(route, router, agentService, agentPlaygroundService, tokenStorage, loaderService, formBuilder, toolExecutionService) {\n      this.route = route;\n      this.router = router;\n      this.agentService = agentService;\n      this.agentPlaygroundService = agentPlaygroundService;\n      this.tokenStorage = tokenStorage;\n      this.loaderService = loaderService;\n      this.formBuilder = formBuilder;\n      this.toolExecutionService = toolExecutionService;\n      this.agentForm = this.formBuilder.group({\n        isConversational: [true],\n        isUseTemplate: [false]\n      });\n    }\n    ngOnInit() {\n      console.log('🌟 SHARED COMPONENT INITIALIZED! 🌟');\n      this.executionId = crypto.randomUUID();\n      this.route.params.subscribe(params => {\n        this.agentType = params['type'] || 'individual';\n        console.log('🌟 SHARED: Agent type set to:', this.agentType);\n      });\n      this.route.queryParams.subscribe(params => {\n        if (params['id']) {\n          this.agentId = params['id'];\n          this.loadAgentData(params['id']);\n        }\n      });\n      // Initialize chat messages\n      this.chatMessages = [{\n        from: 'ai',\n        text: `Hi there! I am ${this.agentName || 'your agent'}. How can I help you today?`\n      }];\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n      if (this.progressInterval) {\n        clearInterval(this.progressInterval);\n      }\n    }\n    onTabChange(event) {\n      this.activeTabId = event.id;\n      this.selectedTab = event.label;\n    }\n    loadAgentData(agentId) {\n      this.isLoading = true;\n      // Load agent data based on type\n      if (this.agentType === 'collaborative') {\n        this.agentService.getCollaborativeAgentDetailsById(agentId).subscribe({\n          next: response => {\n            this.handleAgentDataResponse(response);\n          },\n          error: error => {\n            console.error('Error loading collaborative agent:', error);\n            this.isLoading = false;\n          }\n        });\n      } else {\n        this.agentService.getAgentById(agentId).subscribe({\n          next: response => {\n            this.handleAgentDataResponse(response);\n          },\n          error: error => {\n            console.error('Error loading individual agent:', error);\n            this.isLoading = false;\n          }\n        });\n      }\n    }\n    handleAgentDataResponse(response) {\n      this.isLoading = false;\n      // Extract agent details\n      let agentData;\n      if (response.agentDetails && Array.isArray(response.agentDetails) && response.agentDetails.length > 0) {\n        agentData = response.agentDetails[0];\n      } else if (response.agentDetail) {\n        agentData = response.agentDetail;\n      } else if (response.data) {\n        agentData = response.data;\n      } else {\n        agentData = response;\n      }\n      if (agentData) {\n        this.currentAgentDetails = agentData;\n        this.agentName = agentData.name || agentData.agentName || 'Agent';\n        this.agentDetail = agentData.description || agentData.agentDetail || '';\n        // For individual agents, set up the required properties for playground functionality\n        if (this.agentType === 'individual') {\n          // Set selectedPrompt to agent name or useCaseName to bypass dropdown requirement\n          this.selectedPrompt = agentData.useCaseName || agentData.name || 'loaded-agent';\n          // Set selectedAgentMode for API calls - use useCaseCode if available\n          this.selectedAgentMode = agentData.useCaseCode || agentData.useCaseName || agentData.name || '';\n          // Set useCaseIdentifier - use organizationPath if available\n          if (agentData.organizationPath) {\n            this.selectedUseCaseIdentifier = agentData.organizationPath;\n          } else if (agentData.useCaseCode) {\n            this.selectedUseCaseIdentifier = agentData.useCaseCode;\n          } else if (agentData.useCaseName) {\n            this.selectedUseCaseIdentifier = agentData.useCaseName;\n          }\n        }\n        // Update chat message with agent name\n        if (this.chatMessages.length > 0) {\n          this.chatMessages[0].text = `Hi there! I am ${this.agentName}. How can I help you today?`;\n        }\n        // Load agent nodes and configuration\n        this.loadAgentNodes(agentData);\n      }\n    }\n    loadAgentNodes(agentData) {\n      // Map agent configuration to blueprint panel\n      this.mapAgentConfigurationToBlueprint(agentData);\n    }\n    handleChatMessage(message) {\n      if (this.agentType === 'individual') {\n        // For individual agents, use the loaded agent details instead of requiring dropdown selection\n        if (!this.currentAgentDetails && (!this.selectedPrompt || this.selectedPrompt === 'default')) {\n          this.showAgentError('Agent details are not loaded. Please try refreshing the page.');\n          return;\n        }\n        let displayMessage = message;\n        if (this.agentFilesUploadedData.length > 0) {\n          const fileNames = this.agentFilesUploadedData.map(file => file.documentName).join(', ');\n          displayMessage = `${message}\\n\\n📎 Attached files: ${fileNames}`;\n        }\n        // Add to execution history\n        const executionId = crypto.randomUUID();\n        this.executionHistory.push({\n          id: executionId,\n          agentName: this.agentName,\n          userMessage: message,\n          status: 'pending',\n          // Will be updated based on API response\n          timestamp: new Date()\n        });\n        console.log('Added new execution to history:', this.executionHistory);\n        this.chatMessages = [...this.chatMessages, {\n          from: 'user',\n          text: displayMessage\n        }, {\n          from: 'ai',\n          text: `${this.agentName} - Status: Pending`\n        }];\n        this.isProcessingChat = true;\n        const isConversational = this.agentForm.get('isConversational')?.value || false;\n        const isUseTemplate = this.agentForm.get('isUseTemplate')?.value || false;\n        console.log('Chat message handling - isConversational:', isConversational, 'isUseTemplate:', isUseTemplate);\n        // Use agent details from the loaded agent data instead of dropdown selection\n        // Mode should be the useCaseCode, not useCaseName\n        const agentMode = this.agentCode || this.selectedAgentMode || this.currentAgentDetails?.useCaseCode || this.currentAgentDetails?.useCaseName || this.currentAgentDetails?.name || this.selectedPrompt;\n        let useCaseIdentifier = this.selectedUseCaseIdentifier;\n        if (!useCaseIdentifier) {\n          // Use organizationPath if available, otherwise build from agent details\n          if (this.currentAgentDetails?.organizationPath) {\n            useCaseIdentifier = this.currentAgentDetails.organizationPath;\n          } else {\n            const orgPath = this.buildOrganizationPath();\n            const agentIdentifier = this.currentAgentDetails?.useCaseCode || this.currentAgentDetails?.useCaseName || this.currentAgentDetails?.name || agentMode;\n            useCaseIdentifier = `${agentIdentifier}${orgPath}`;\n          }\n        }\n        if (this.agentFilesUploadedData.length > 0) {\n          this.processAgentFilesAndSendMessage(message, agentMode, useCaseIdentifier, isConversational, isUseTemplate);\n          return;\n        }\n        this.sendAgentMessageToAPI(message, agentMode, useCaseIdentifier, isConversational, isUseTemplate);\n      } else if (this.agentType === 'collaborative') {\n        // Add to execution history for collaborative agents too\n        const executionId = crypto.randomUUID();\n        this.executionHistory.push({\n          id: executionId,\n          agentName: this.agentName,\n          userMessage: message,\n          status: 'pending',\n          // Will be updated based on API response\n          timestamp: new Date()\n        });\n        console.log('Added new collaborative execution to history:', this.executionHistory);\n        this.isProcessingChat = true;\n        let payload = {\n          executionId: this.executionId,\n          agentId: Number(this.agentId),\n          user: this.tokenStorage.getDaUsername() || '<EMAIL>',\n          userInputs: {\n            question: message\n          }\n        };\n        // Add pending status to chat for collaborative agents\n        this.chatMessages = [...this.chatMessages, {\n          from: 'user',\n          text: message\n        }, {\n          from: 'ai',\n          text: `${this.agentName} - Status: Pending`\n        }];\n        if (this.agentFilesUploadedData.length > 0) {\n          const fileWrapper = this.agentFilesUploadedData[0];\n          let displayMessage;\n          if (this.agentFilesUploadedData.length > 0) {\n            const fileNames = this.agentFilesUploadedData.map(file => file.documentName).join(', ');\n            displayMessage = `📎 Attached files: ${fileNames}`;\n            this.chatMessages = [{\n              from: 'user',\n              text: displayMessage\n            }];\n          }\n          this.agentPlaygroundService.submitAgentExecuteWithFile(payload, fileWrapper).pipe(finalize(() => {\n            this.isProcessingChat = false;\n            this.isAgentPlaygroundLoading = false;\n          }), takeUntil(this.agentPlaygroundDestroy)).subscribe({\n            next: res => this.handleAgentExecuteResponse(res, message),\n            error: err => {\n              // Update execution history with failed status\n              if (this.executionHistory.length > 0) {\n                this.executionHistory[this.executionHistory.length - 1].status = 'failed';\n              }\n              // Update the last chat message with the final status\n              if (this.chatMessages.length > 0) {\n                const lastMessage = this.chatMessages[this.chatMessages.length - 1];\n                if (lastMessage.from === 'ai' && lastMessage.text.includes('Status: Pending')) {\n                  lastMessage.text = `${this.agentName} - Status: Failed`;\n                }\n              }\n              // Switch to output tab even on failure\n              this.setActiveRightTab('output');\n            }\n          });\n        } else {\n          this.agentPlaygroundService.submitAgentExecute(payload).pipe(finalize(() => {\n            this.isProcessingChat = false;\n            this.isAgentPlaygroundLoading = false;\n          }), takeUntil(this.agentPlaygroundDestroy)).subscribe({\n            next: res => this.handleAgentExecuteResponse(res, message),\n            error: err => {\n              // Update execution history with failed status\n              if (this.executionHistory.length > 0) {\n                this.executionHistory[this.executionHistory.length - 1].status = 'failed';\n              }\n              // Update the last chat message with the final status\n              if (this.chatMessages.length > 0) {\n                const lastMessage = this.chatMessages[this.chatMessages.length - 1];\n                if (lastMessage.from === 'ai' && lastMessage.text.includes('Status: Pending')) {\n                  lastMessage.text = `${this.agentName} - Status: Failed`;\n                }\n              }\n              // Switch to output tab even on failure\n              this.setActiveRightTab('output');\n            }\n          });\n        }\n      }\n    }\n    onPromptChanged(prompt) {\n      this.inputText = prompt.name || String(prompt.value) || '';\n    }\n    onPlaygroundConversationalToggle(value) {\n      // Update the form control\n      this.agentForm.get('isConversational')?.setValue(value);\n      // When conversational mode is turned off, clear the conversation history\n      // This ensures that the next message will be treated as a fresh start\n      if (!value) {\n        this.agentChatPayload = [];\n        console.log('Conversational mode disabled - cleared chat payload history');\n      } else {\n        console.log('Conversational mode enabled - will maintain chat history');\n      }\n    }\n    onPlaygroundTemplateToggle(value) {\n      // Update the form control\n      this.agentForm.get('isUseTemplate')?.setValue(value);\n      console.log('Template mode toggled:', value);\n    }\n    onFilesSelected(files) {\n      this.selectedFiles = files;\n      // Update agentFilesUploadedData for agent execution\n      this.agentFilesUploadedData = files;\n    }\n    onApprovalRequested() {\n      // Handle approval request\n    }\n    saveLogs() {\n      // Save execution logs\n    }\n    exportResults(section) {\n      // Export results\n    }\n    handleControlAction(action) {\n      // Handle execution control actions\n    }\n    navigateBack() {\n      this.router.navigate(['/build/agents', this.agentType], {\n        queryParams: {\n          id: this.agentId,\n          mode: 'view'\n        }\n      });\n    }\n    editAgent() {\n      this.router.navigate(['/build/agents', this.agentType], {\n        queryParams: {\n          id: this.agentId,\n          mode: 'edit'\n        }\n      });\n    }\n    navigateToAgentsList() {\n      this.router.navigate(['/build/agents']);\n    }\n    toggleLeftPanel() {\n      this.isLeftPanelCollapsed = !this.isLeftPanelCollapsed;\n    }\n    setActiveRightTab(tab) {\n      this.activeRightTab = tab;\n    }\n    // Blueprint zone management methods\n    toggleBlueprintZone(zoneType) {\n      this.blueprintZonesExpanded[zoneType] = !this.blueprintZonesExpanded[zoneType];\n    }\n    isBlueprintZoneExpanded(zoneType) {\n      return this.blueprintZonesExpanded[zoneType] || false;\n    }\n    // API and helper methods from build-agents component\n    showAgentError(message) {\n      this.chatMessages = [...this.chatMessages, {\n        from: 'ai',\n        text: message\n      }];\n    }\n    buildOrganizationPath() {\n      // Simple implementation - in real scenario this would be from navbar/metadata\n      return '';\n    }\n    getMetadataFromNavbar() {\n      // Simple implementation - in real scenario this would get org level mapping\n      return {};\n    }\n    handleAgentExecuteResponse(response, message) {\n      try {\n        // Store the latest response for display in the output panel\n        this.latestAgentResponse = response;\n        // Update the latest execution history entry with success status and response\n        if (this.executionHistory.length > 0) {\n          this.executionHistory[this.executionHistory.length - 1].status = 'success';\n          this.executionHistory[this.executionHistory.length - 1].response = response;\n          console.log('Updated execution history:', this.executionHistory);\n        }\n        const outputRaw = response?.agentResponse?.agent?.output;\n        let formattedOutput = '';\n        if (outputRaw) {\n          // Directly replace escaped \\n with real newlines\n          formattedOutput = outputRaw.replace(/\\\\n/g, '\\n');\n        } else {\n          formattedOutput = response?.agentResponse?.detail;\n        }\n        // In playground, only show agent name and status, not the full response\n        this.chatMessages = [...this.chatMessages, {\n          from: 'user',\n          text: message\n        }, {\n          from: 'ai',\n          text: `${this.agentName} - Status: Success`\n        }];\n        // Update the last status message from 'Pending' to 'Success'\n        if (this.chatMessages.length > 0) {\n          for (let i = this.chatMessages.length - 1; i >= 0; i--) {\n            if (this.chatMessages[i].from === 'ai' && this.chatMessages[i].text.includes('Status: Pending')) {\n              this.chatMessages[i].text = `${this.agentName} - Status: Success`;\n              break;\n            }\n          }\n        }\n        // Automatically switch to output tab after successful execution\n        this.setActiveRightTab('output');\n      } catch (err) {\n        // Update execution history with failed status\n        if (this.executionHistory.length > 0) {\n          this.executionHistory[this.executionHistory.length - 1].status = 'failed';\n          console.log('Updated execution history (failed):', this.executionHistory);\n        }\n        this.chatMessages = [...this.chatMessages, {\n          from: 'user',\n          text: message\n        }, {\n          from: 'ai',\n          text: `${this.agentName} - Status: Failed`\n        }];\n        // Update the last status message from 'Pending' to 'Failed'\n        if (this.chatMessages.length > 0) {\n          for (let i = this.chatMessages.length - 1; i >= 0; i--) {\n            if (this.chatMessages[i].from === 'ai' && this.chatMessages[i].text.includes('Status: Pending')) {\n              this.chatMessages[i].text = `${this.agentName} - Status: Failed`;\n              break;\n            }\n          }\n        }\n        // Switch to output tab even on failure\n        this.setActiveRightTab('output');\n      }\n    }\n    processAgentFilesAndSendMessage(message, mode, useCaseIdentifier, isConversational, isUseTemplate) {\n      const formData = new FormData();\n      this.agentFilesUploadedData.forEach(fileData => {\n        if (fileData.file) {\n          formData.append('files', fileData.file);\n        }\n      });\n      if (formData.has('files')) {\n        this.agentPlaygroundService.getFileToContent(formData).pipe(switchMap(fileResponse => {\n          const fileContent = fileResponse?.fileResponses?.map(response => response.fileContent)?.join('\\n') || '';\n          this.sendAgentMessageToAPIWithFiles(message, mode, useCaseIdentifier, isConversational, isUseTemplate, fileContent);\n          return of(null);\n        }), catchError(error => {\n          console.error('Error parsing files:', error);\n          this.sendAgentMessageToAPI(message, mode, useCaseIdentifier, isConversational, isUseTemplate);\n          return of(null);\n        })).subscribe();\n      } else {\n        this.sendAgentMessageToAPI(message, mode, useCaseIdentifier, isConversational, isUseTemplate);\n      }\n    }\n    sendAgentMessageToAPI(message, mode, useCaseIdentifier, isConversational, isUseTemplate) {\n      console.log('API Call Parameters:', {\n        message,\n        mode,\n        useCaseIdentifier,\n        isConversational,\n        isUseTemplate,\n        currentChatPayloadLength: this.agentChatPayload.length\n      });\n      if (isConversational) {\n        this.agentChatPayload.push({\n          content: message,\n          role: 'user'\n        });\n      }\n      const payload = isConversational ? this.agentChatPayload : message;\n      const {\n        levelId\n      } = this.getMetadataFromNavbar();\n      console.log('Final payload being sent:', payload);\n      this.agentPlaygroundService.generatePrompt(payload, mode, isConversational, isUseTemplate, this.agentAttachment, useCaseIdentifier, '', levelId).pipe(finalize(() => {\n        this.isProcessingChat = false;\n        this.isAgentPlaygroundLoading = false;\n      }), takeUntil(this.agentPlaygroundDestroy)).subscribe({\n        next: generatedResponse => {\n          // Call handleAgentExecuteResponse to properly handle the response\n          this.handleAgentExecuteResponse(generatedResponse, message);\n        },\n        error: error => {\n          console.error('API Error:', error);\n          // Update execution history with failed status\n          if (this.executionHistory.length > 0) {\n            this.executionHistory[this.executionHistory.length - 1].status = 'failed';\n          }\n          const errorMessage = error?.error?.message || 'An error occurred while processing your request.';\n          // Show error in chat and switch to output tab\n          this.chatMessages = [...this.chatMessages, {\n            from: 'user',\n            text: message\n          }, {\n            from: 'ai',\n            text: `${this.agentName} - Status: Failed`\n          }];\n          // Switch to output tab even on failure\n          this.setActiveRightTab('output');\n          if (isConversational && this.agentChatPayload.length > 0) {\n            this.agentChatPayload.pop();\n          }\n        }\n      });\n    }\n    sendAgentMessageToAPIWithFiles(message, mode, useCaseIdentifier, isConversational, isUseTemplate, fileContents) {\n      if (isConversational) {\n        this.agentChatPayload.push({\n          content: message,\n          role: 'user'\n        });\n      }\n      const payload = isConversational ? this.agentChatPayload : message;\n      const {\n        levelId\n      } = this.getMetadataFromNavbar();\n      this.agentPlaygroundService.generatePrompt(payload, mode, isConversational, isUseTemplate, this.agentAttachment, useCaseIdentifier, fileContents, levelId).pipe(finalize(() => {\n        this.isProcessingChat = false;\n        this.isAgentPlaygroundLoading = false;\n      }), takeUntil(this.agentPlaygroundDestroy)).subscribe({\n        next: generatedResponse => {\n          // Call handleAgentExecuteResponse to properly handle the response\n          this.handleAgentExecuteResponse(generatedResponse, message);\n        },\n        error: error => {\n          console.error('API Error:', error);\n          const errorMessage = error?.error?.message || 'An error occurred while processing your request.';\n          this.showAgentError(errorMessage);\n          if (isConversational && this.agentChatPayload.length > 0) {\n            this.agentChatPayload.pop();\n          }\n        }\n      });\n    }\n    // Blueprint panel methods\n    mapAgentConfigurationToBlueprint(agentData) {\n      if (!agentData) {\n        console.warn('No agent data provided for blueprint');\n        return;\n      }\n      console.log('🔍 DEBUG: Full agent data received:', agentData);\n      console.log('🔍 DEBUG: Agent type:', this.agentType);\n      console.log('🔍 DEBUG: Agent data keys:', Object.keys(agentData));\n      // Clear existing nodes\n      this.buildAgentNodes = [];\n      this.canvasNodes = [];\n      let nodeCounter = 1;\n      // Map agent configuration to nodes based on agent type\n      if (this.agentType === 'individual') {\n        this.mapIndividualAgentToBlueprint(agentData, nodeCounter);\n      } else if (this.agentType === 'collaborative') {\n        this.mapCollaborativeAgentToBlueprint(agentData, nodeCounter);\n      }\n      console.log('🎯 Blueprint nodes mapped:', {\n        buildAgentNodes: this.buildAgentNodes,\n        canvasNodes: this.canvasNodes,\n        totalNodes: this.buildAgentNodes.length\n      });\n    }\n    mapIndividualAgentToBlueprint(agentData, nodeCounter) {\n      console.log('🔍 Individual agent mapping - checking fields:', {\n        config: agentData.config,\n        configLength: agentData.config?.length,\n        useCaseName: agentData.useCaseName,\n        prompt: agentData.prompt,\n        useCaseDetails: agentData.useCaseDetails\n      });\n      // Clear existing blueprint nodes\n      this.blueprintPromptNodes = [];\n      this.blueprintModelNodes = [];\n      this.blueprintKnowledgeNodes = [];\n      this.blueprintGuardrailNodes = [];\n      // Add prompt node from \"prompt\" field\n      if (agentData.prompt) {\n        this.blueprintPromptNodes.push({\n          id: `prompt-${nodeCounter++}`,\n          name: agentData.prompt,\n          type: 'prompt'\n        });\n        console.log('✅ Added prompt node:', agentData.prompt);\n      }\n      // Process the config array to extract model, knowledge bases, and guardrails\n      if (agentData.config && Array.isArray(agentData.config)) {\n        console.log('🔍 Processing config array with length:', agentData.config.length);\n        agentData.config.forEach((category, categoryIndex) => {\n          console.log(`🔍 Category ${categoryIndex} (ID: ${category.categoryId}):`, category.categoryName);\n          if (category.config && Array.isArray(category.config)) {\n            console.log(`🔍 Category ${categoryIndex} has ${category.config.length} config items`);\n            category.config.forEach((configItem, itemIndex) => {\n              console.log(`🔍 Config item ${categoryIndex}-${itemIndex}:`, {\n                configKey: configItem.configKey,\n                configValue: configItem.configValue,\n                categoryId: configItem.categoryId\n              });\n              // Handle AI Model from categoryId 1\n              if (configItem.categoryId === 1 && configItem.configKey === 'MODEL' && configItem.configValue) {\n                console.log('✅ Adding AI model node from categoryId 1:', configItem.configValue);\n                this.blueprintModelNodes.push({\n                  id: `model-${nodeCounter++}`,\n                  name: `${configItem.configKey}`,\n                  type: 'model'\n                });\n              }\n              // Handle Knowledge Base from categoryId 2\n              if (configItem.categoryId === 2 && configItem.configKey === 'RAG_KNOWLEDGEBASE_NAME' && configItem.configValue) {\n                console.log('✅ Adding knowledge base nodes from categoryId 2:', configItem.configValue);\n                const kbValue = configItem.configValue.toString();\n                const kbIds = kbValue.split(',').map(id => id.trim()).filter(id => id);\n                kbIds.forEach(kbId => {\n                  this.blueprintKnowledgeNodes.push({\n                    id: `knowledge-${nodeCounter++}`,\n                    name: `Knowledge Base: ${kbId}`,\n                    type: 'knowledge'\n                  });\n                });\n              }\n              // Handle Guardrails from categoryId 3 where configValue is true\n              if (configItem.categoryId === 3 && configItem.configValue === 'true') {\n                console.log('✅ Found enabled guardrail from categoryId 3:', {\n                  key: configItem.configKey,\n                  value: configItem.configValue\n                });\n                if (configItem.configKey === 'ENABLE_GUARDRAILS') {\n                  // Only add one general guardrail node if not already added\n                  if (this.blueprintGuardrailNodes.length === 0) {\n                    this.blueprintGuardrailNodes.push({\n                      id: `guardrail-${nodeCounter++}`,\n                      name: 'Guardrails Enabled',\n                      type: 'guardrail'\n                    });\n                  }\n                } else if (configItem.configKey.startsWith('GUARDRAIL_')) {\n                  // Add specific guardrail nodes for enabled guardrails\n                  let guardrailName = configItem.configKey;\n                  if (guardrailName.startsWith('GUARDRAIL_')) {\n                    guardrailName = guardrailName.replace('GUARDRAIL_', '').replace(/_/g, ' ');\n                  }\n                  this.blueprintGuardrailNodes.push({\n                    id: `guardrail-${nodeCounter++}`,\n                    name: `${guardrailName}`,\n                    type: 'guardrail'\n                  });\n                }\n              }\n            });\n          }\n        });\n      }\n      console.log('🎯 Final blueprint nodes:', {\n        promptNodes: this.blueprintPromptNodes,\n        modelNodes: this.blueprintModelNodes,\n        knowledgeNodes: this.blueprintKnowledgeNodes,\n        guardrailNodes: this.blueprintGuardrailNodes\n      });\n      // Calculate completion percentage\n      const totalRequired = 2; // Prompt + Model are required\n      const currentRequired = this.blueprintPromptNodes.length + this.blueprintModelNodes.length;\n      this.blueprintCompletionPercentage = Math.round(currentRequired / totalRequired * 100);\n    }\n    mapCollaborativeAgentToBlueprint(agentData, nodeCounter) {\n      console.log('🚀 SHARED COMPONENT: mapCollaborativeAgentToBlueprint called!');\n      console.log('🔍 DEBUG: Collaborative agent data received:', agentData);\n      console.log('🔍 DEBUG: Collaborative agent data keys:', Object.keys(agentData));\n      console.log('🔍 DEBUG: Agent type in component:', this.agentType);\n      console.log('🔍 DEBUG: Current nodeCounter:', nodeCounter);\n      // Clear existing blueprint nodes\n      this.blueprintPromptNodes = [];\n      this.blueprintModelNodes = [];\n      this.blueprintKnowledgeNodes = [];\n      this.blueprintToolNodes = [];\n      this.blueprintGuardrailNodes = [];\n      console.log('🔍 DEBUG: Cleared all blueprint node arrays');\n      // Add prompt node - handle different prompt structures for collaborative agents\n      const shouldCreatePromptNode = agentData.goal || agentData.role || agentData.description;\n      console.log('🔍 DEBUG: Checking prompt node creation:', {\n        goal: agentData.goal,\n        role: agentData.role,\n        description: agentData.description,\n        shouldCreatePromptNode\n      });\n      if (shouldCreatePromptNode) {\n        let promptNodeName = agentData.goal || agentData.role || agentData.description || 'Collaborative Agent Prompt';\n        // Truncate prompt if too long for display\n        if (promptNodeName.length > 150) {\n          promptNodeName = promptNodeName.substring(0, 150) + '...';\n        }\n        this.blueprintPromptNodes.push({\n          id: `prompt-${nodeCounter++}`,\n          name: promptNodeName,\n          type: 'prompt'\n        });\n        console.log('✅ Added collaborative prompt node:', promptNodeName);\n      }\n      // Add model nodes - handle both old and new API formats like build-agents\n      let modelReferences = [];\n      console.log('🔍 DEBUG: Checking model data:', {\n        hasAgentConfigs: !!agentData.agentConfigs,\n        agentConfigs: agentData.agentConfigs,\n        model: agentData.model,\n        modelName: agentData.modelName,\n        modelDetails: agentData.modelDetails\n      });\n      // New API format: agentConfigs.modelRef (array of model IDs or objects)\n      if (agentData.agentConfigs && agentData.agentConfigs.modelRef) {\n        const modelRefs = Array.isArray(agentData.agentConfigs.modelRef) ? agentData.agentConfigs.modelRef : [agentData.agentConfigs.modelRef];\n        modelReferences = modelRefs.map(ref => {\n          if (typeof ref === 'number' || typeof ref === 'string') {\n            return {\n              modelId: ref\n            };\n          }\n          return ref;\n        });\n      }\n      // Old API format: modelDetails\n      else if (agentData.modelDetails) {\n        modelReferences = [agentData.modelDetails];\n      }\n      // Fallback: check for model or modelName directly\n      else if (agentData.model || agentData.modelName) {\n        modelReferences = [{\n          modelId: agentData.model || agentData.modelName\n        }];\n      }\n      modelReferences.forEach(modelRef => {\n        const modelId = modelRef.modelId || modelRef.id;\n        const modelName = modelRef.model || modelRef.modelDeploymentName || `Model ID: ${modelId}`;\n        this.blueprintModelNodes.push({\n          id: `model-${nodeCounter++}`,\n          name: modelName,\n          type: 'model'\n        });\n        console.log('✅ Added collaborative model node:', modelName);\n      });\n      // Add knowledge base nodes - handle both old and new API formats\n      let knowledgeReferences = [];\n      // New API format: agentConfigs.knowledgeBaseRef (array of KB IDs or objects)\n      if (agentData.agentConfigs && agentData.agentConfigs.knowledgeBaseRef) {\n        const kbRefs = Array.isArray(agentData.agentConfigs.knowledgeBaseRef) ? agentData.agentConfigs.knowledgeBaseRef : [agentData.agentConfigs.knowledgeBaseRef];\n        knowledgeReferences = kbRefs.map(ref => {\n          if (typeof ref === 'number' || typeof ref === 'string') {\n            return {\n              knowledgeBaseId: ref\n            };\n          }\n          return ref;\n        });\n      }\n      // Old API format: knowledgeBase\n      else if (agentData.knowledgeBase && Array.isArray(agentData.knowledgeBase)) {\n        knowledgeReferences = agentData.knowledgeBase;\n      }\n      knowledgeReferences.forEach(kbRef => {\n        const kbId = kbRef.knowledgeBaseId || kbRef.id;\n        const collectionName = kbRef.indexCollectionName || kbRef.name;\n        const kbName = collectionName || `Knowledge Base ID: ${kbId}`;\n        this.blueprintKnowledgeNodes.push({\n          id: `knowledge-${nodeCounter++}`,\n          name: kbName,\n          type: 'knowledge'\n        });\n        console.log('✅ Added collaborative knowledge node:', kbName);\n      });\n      // Add tool nodes - handle both old and new API formats like build-agents\n      let toolReferences = [];\n      let userToolReferences = [];\n      console.log('🔍 DEBUG: Checking tool data:', {\n        hasAgentConfigs: !!agentData.agentConfigs,\n        agentConfigsContent: agentData.agentConfigs,\n        hasTools: agentData.tools,\n        toolsContent: agentData.tools,\n        hasUserTools: agentData.userTools,\n        userToolsContent: agentData.userTools\n      });\n      // New API format: agentConfigs.toolRef and agentConfigs.userToolRef\n      if (agentData.agentConfigs) {\n        if (agentData.agentConfigs.toolRef) {\n          const toolRefs = Array.isArray(agentData.agentConfigs.toolRef) ? agentData.agentConfigs.toolRef : [agentData.agentConfigs.toolRef];\n          toolReferences = toolRefs.map(ref => {\n            if (typeof ref === 'number' || typeof ref === 'string') {\n              return {\n                toolId: ref\n              };\n            }\n            return ref;\n          });\n        }\n        if (agentData.agentConfigs.userToolRef) {\n          const userToolRefs = Array.isArray(agentData.agentConfigs.userToolRef) ? agentData.agentConfigs.userToolRef : [agentData.agentConfigs.userToolRef];\n          userToolReferences = userToolRefs.map(ref => {\n            if (typeof ref === 'number' || typeof ref === 'string') {\n              return {\n                toolId: ref\n              };\n            }\n            return ref;\n          });\n        }\n      }\n      // Old API format: tools and userTools\n      else {\n        if (agentData.tools && Array.isArray(agentData.tools)) {\n          toolReferences = agentData.tools;\n        }\n        if (agentData.userTools && Array.isArray(agentData.userTools)) {\n          userToolReferences = agentData.userTools;\n        }\n      }\n      // Process built-in tools\n      toolReferences.forEach(tool => {\n        const toolId = tool.toolId || tool.id;\n        const toolName = tool.toolName || tool.name || `Tool ID: ${toolId}`;\n        this.blueprintToolNodes.push({\n          id: `tool-${nodeCounter++}`,\n          name: toolName,\n          type: 'tool'\n        });\n        console.log('✅ Added collaborative builtin tool node:', toolName);\n      });\n      // Process user tools\n      userToolReferences.forEach(userTool => {\n        const userToolId = userTool.toolId || userTool.id;\n        const userToolName = userTool.toolName || userTool.name || `User Tool ID: ${userToolId}`;\n        this.blueprintToolNodes.push({\n          id: `tool-${nodeCounter++}`,\n          name: userToolName,\n          type: 'tool'\n        });\n        console.log('✅ Added collaborative user tool node:', userToolName);\n      });\n      console.log('🎯 Final collaborative blueprint nodes:', {\n        promptNodes: this.blueprintPromptNodes,\n        modelNodes: this.blueprintModelNodes,\n        knowledgeNodes: this.blueprintKnowledgeNodes,\n        toolNodes: this.blueprintToolNodes,\n        guardrailNodes: this.blueprintGuardrailNodes\n      });\n      // Debug: Check blueprint node arrays lengths\n      console.log('📊 Blueprint node counts:', {\n        promptCount: this.blueprintPromptNodes.length,\n        modelCount: this.blueprintModelNodes.length,\n        knowledgeCount: this.blueprintKnowledgeNodes.length,\n        toolCount: this.blueprintToolNodes.length,\n        guardrailCount: this.blueprintGuardrailNodes.length\n      });\n      // Debug: Check if tools zone will be visible\n      console.log('🔧 Tools zone debug:', {\n        agentType: this.agentType,\n        isCollaborative: this.agentType === 'collaborative',\n        hasToolNodes: this.blueprintToolNodes.length > 0,\n        toolNodeNames: this.blueprintToolNodes.map(t => t.name)\n      });\n      // Calculate completion percentage\n      const totalRequired = 2; // Prompt + Model are required\n      const currentRequired = this.blueprintPromptNodes.length + this.blueprintModelNodes.length;\n      this.blueprintCompletionPercentage = Math.round(currentRequired / totalRequired * 100);\n    }\n    static ɵfac = function AgentExecutionComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AgentExecutionComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AgentServiceService), i0.ɵɵdirectiveInject(i3.AgentPlaygroundService), i0.ɵɵdirectiveInject(i4.TokenStorageService), i0.ɵɵdirectiveInject(i5.LoaderService), i0.ɵɵdirectiveInject(i6.FormBuilder), i0.ɵɵdirectiveInject(i7.ToolExecutionService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AgentExecutionComponent,\n      selectors: [[\"app-agent-execution\"]],\n      viewQuery: function AgentExecutionComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(AgentExecutionPlaygroundComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.playgroundComp = _t.first);\n        }\n      },\n      decls: 24,\n      vars: 12,\n      consts: [[1, \"agent-execution-container\"], [1, \"top-nav-bar\"], [1, \"nav-left\"], [\"type\", \"button\", 1, \"back-button\", 3, \"click\"], [\"iconName\", \"ArrowLeft\", \"iconSize\", \"16\", \"iconColor\", \"#374151\"], [1, \"agent-name\"], [1, \"main-content\"], [1, \"left-panel\"], [1, \"panel-header\"], [\"type\", \"button\", 1, \"collapse-btn\", 3, \"click\"], [\"iconSize\", \"16\", \"iconColor\", \"#6B7280\", 3, \"iconName\"], [\"class\", \"history-btn\", \"type\", \"button\", \"disabled\", \"\", 4, \"ngIf\"], [\"class\", \"panel-content\", 4, \"ngIf\"], [1, \"right-panel\"], [1, \"tabs-container\"], [1, \"tab-btn\", 3, \"click\"], [1, \"panel-content\"], [\"class\", \"blueprint-content\", 4, \"ngIf\"], [\"class\", \"output-content\", 4, \"ngIf\"], [\"type\", \"button\", \"disabled\", \"\", 1, \"history-btn\"], [3, \"promptChange\", \"messageSent\", \"conversationalToggle\", \"templateToggle\", \"filesSelected\", \"approvalRequested\", \"messages\", \"isLoading\", \"agentType\", \"showChatInteractionToggles\", \"showAiPrincipleToggle\", \"showApprovalButton\", \"showDropdown\", \"showAgentNameInput\", \"showFileUploadButton\", \"showStatusOnly\", \"displayedAgentName\", \"agentNamePlaceholder\"], [1, \"blueprint-content\"], [1, \"blueprint-header\"], [1, \"custom-blueprint-container\"], [\"viewBox\", \"0 0 100 100\", \"preserveAspectRatio\", \"none\", 1, \"connection-lines\"], [\"x1\", \"25\", \"y1\", \"25\", \"x2\", \"50\", \"y2\", \"50\", \"stroke\", \"#BBBEC5\", \"stroke-width\", \"0.3\"], [\"x1\", \"75\", \"y1\", \"25\", \"x2\", \"50\", \"y2\", \"50\", \"stroke\", \"#BBBEC5\", \"stroke-width\", \"0.3\"], [\"x1\", \"25\", \"y1\", \"75\", \"x2\", \"50\", \"y2\", \"50\", \"stroke\", \"#BBBEC5\", \"stroke-width\", \"0.3\"], [\"x1\", \"75\", \"y1\", \"75\", \"x2\", \"50\", \"y2\", \"50\", \"stroke\", \"#BBBEC5\", \"stroke-width\", \"0.3\"], [1, \"central-progress\"], [1, \"progress-ring\"], [\"width\", \"120\", \"height\", \"120\", 1, \"progress-circle\"], [\"id\", \"progressGradient\", \"x1\", \"0%\", \"y1\", \"0%\", \"x2\", \"100%\", \"y2\", \"100%\"], [\"offset\", \"33.91%\", 2, \"stop-color\", \"#0084ff\"], [\"offset\", \"100%\", 2, \"stop-color\", \"#03bdd4\"], [\"cx\", \"60\", \"cy\", \"60\", \"r\", \"50\", \"fill\", \"none\", \"stroke\", \"#e5e7eb\", \"stroke-width\", \"8\", 1, \"progress-background\"], [\"cx\", \"60\", \"cy\", \"60\", \"r\", \"50\", \"fill\", \"none\", \"stroke\", \"url(#progressGradient)\", \"stroke-width\", \"8\", \"stroke-linecap\", \"round\", \"transform\", \"rotate(180 60 60)\", 1, \"progress-bar\"], [1, \"progress-content\"], [1, \"progress-percentage\"], [1, \"progress-label\"], [\"id\", \"parent-box\"], [1, \"blueprint-zone\", \"north-zone\", \"prompts-zone\"], [1, \"zone-header\", 3, \"click\"], [1, \"header-content\"], [1, \"header-icon\"], [\"width\", \"45\", \"height\", \"44\", \"viewBox\", \"0 0 45 44\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"x\", \"0.679688\", \"width\", \"44\", \"height\", \"44\", \"rx\", \"8\", \"fill\", \"#5082EF\"], [\"d\", \"M20.6166 25.5C20.5273 25.1539 20.347 24.8381 20.0942 24.5854C19.8415 24.3327 19.5257 24.1523 19.1796 24.063L13.0446 22.481C12.94 22.4513 12.8478 22.3883 12.7822 22.3014C12.7166 22.2146 12.6812 22.1088 12.6812 22C12.6812 21.8912 12.7166 21.7854 12.7822 21.6986C12.8478 21.6118 12.94 21.5487 13.0446 21.519L19.1796 19.936C19.5256 19.8468 19.8413 19.6666 20.094 19.414C20.3467 19.1615 20.5272 18.8459 20.6166 18.5L22.1986 12.365C22.228 12.2599 22.291 12.1673 22.3779 12.1014C22.4649 12.0355 22.571 11.9998 22.6801 11.9998C22.7892 11.9998 22.8954 12.0355 22.9823 12.1014C23.0692 12.1673 23.1322 12.2599 23.1616 12.365L24.7426 18.5C24.8319 18.8461 25.0123 19.1619 25.265 19.4146C25.5177 19.6673 25.8336 19.8477 26.1796 19.937L32.3146 21.518C32.4201 21.5471 32.5132 21.61 32.5795 21.6971C32.6458 21.7841 32.6817 21.8906 32.6817 22C32.6817 22.1094 32.6458 22.2159 32.5795 22.3029C32.5132 22.39 32.4201 22.4529 32.3146 22.482L26.1796 24.063C25.8336 24.1523 25.5177 24.3327 25.265 24.5854C25.0123 24.8381 24.8319 25.1539 24.7426 25.5L23.1606 31.635C23.1312 31.7401 23.0682 31.8327 22.9813 31.8986C22.8944 31.9646 22.7882 32.0003 22.6791 32.0003C22.57 32.0003 22.4639 31.9646 22.3769 31.8986C22.29 31.8327 22.227 31.7401 22.1976 31.635L20.6166 25.5Z\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"zone-title\"], [1, \"header-actions\"], [1, \"required-badge\"], [\"type\", \"button\", 1, \"accordion-toggle\"], [\"width\", \"16\", \"height\", \"16\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\"], [\"d\", \"M6 9L12 15L18 9\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"class\", \"zone-content\", 4, \"ngIf\"], [1, \"blueprint-zone\", \"west-zone\", \"knowledge-zone\"], [\"x\", \"0.679688\", \"width\", \"44\", \"height\", \"44\", \"rx\", \"8\", \"fill\", \"#308666\"], [\"d\", \"M22.6797 17V31\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M26.6797 22H28.6797\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M26.6797 18H28.6797\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M13.6797 28C13.4145 28 13.1601 27.8946 12.9726 27.7071C12.785 27.5196 12.6797 27.2652 12.6797 27V14C12.6797 13.7348 12.785 13.4804 12.9726 13.2929C13.1601 13.1054 13.4145 13 13.6797 13H18.6797C19.7406 13 20.758 13.4214 21.5081 14.1716C22.2583 14.9217 22.6797 15.9391 22.6797 17C22.6797 15.9391 23.1011 14.9217 23.8513 14.1716C24.6014 13.4214 25.6188 13 26.6797 13H31.6797C31.9449 13 32.1993 13.1054 32.3868 13.2929C32.5743 13.4804 32.6797 13.7348 32.6797 14V27C32.6797 27.2652 32.5743 27.5196 32.3868 27.7071C32.1993 27.8946 31.9449 28 31.6797 28H25.6797C24.884 28 24.121 28.3161 23.5584 28.8787C22.9958 29.4413 22.6797 30.2044 22.6797 31C22.6797 30.2044 22.3636 29.4413 21.801 28.8787C21.2384 28.3161 20.4753 28 19.6797 28H13.6797Z\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M16.6797 22H18.6797\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M16.6797 18H18.6797\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"optional-badge\"], [1, \"blueprint-zone\", \"east-zone\", \"models-zone\"], [\"x\", \"0.679688\", \"width\", \"44\", \"height\", \"44\", \"rx\", \"8\", \"fill\", \"#997BCF\"], [\"d\", \"M31.6797 18C31.6793 17.6493 31.5867 17.3048 31.4112 17.0012C31.2357 16.6975 30.9834 16.4454 30.6797 16.27L23.6797 12.27C23.3756 12.0945 23.0308 12.0021 22.6797 12.0021C22.3286 12.0021 21.9837 12.0945 21.6797 12.27L14.6797 16.27C14.376 16.4454 14.1237 16.6975 13.9481 17.0012C13.7726 17.3048 13.68 17.6493 13.6797 18V26C13.68 26.3508 13.7726 26.6952 13.9481 26.9989C14.1237 27.3025 14.376 27.5547 14.6797 27.73L21.6797 31.73C21.9837 31.9056 22.3286 31.998 22.6797 31.998C23.0308 31.998 23.3756 31.9056 23.6797 31.73L30.6797 27.73C30.9834 27.5547 31.2357 27.3025 31.4112 26.9989C31.5867 26.6952 31.6793 26.3508 31.6797 26V18Z\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M13.9795 17L22.6795 22L31.3795 17\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M22.6797 32V22\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"class\", \"blueprint-zone south-zone tools-zone\", 3, \"has-nodes\", 4, \"ngIf\"], [\"class\", \"blueprint-zone south-zone guardrails-zone\", 3, \"has-nodes\", 4, \"ngIf\"], [1, \"zone-content\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [1, \"nodes-list\"], [\"class\", \"kanban-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"empty-state\"], [1, \"kanban-card\"], [1, \"card-title\"], [1, \"blueprint-zone\", \"south-zone\", \"tools-zone\"], [\"width\", \"45\", \"height\", \"44\", \"viewBox\", \"0 0 45 44\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\", 4, \"ngIf\"], [\"x\", \"0.679688\", \"width\", \"44\", \"height\", \"44\", \"rx\", \"8\", \"fill\", \"#D97706\"], [\"d\", \"M25.3795 16.3C25.1963 16.4869 25.0937 16.7382 25.0937 17C25.0937 17.2617 25.1963 17.5131 25.3795 17.7L26.9795 19.3C27.1665 19.4832 27.4178 19.5858 27.6795 19.5858C27.9413 19.5858 28.1926 19.4832 28.3795 19.3L32.1495 15.53C32.6524 16.6412 32.8046 17.8792 32.586 19.0791C32.3674 20.279 31.7883 21.3838 30.9258 22.2463C30.0634 23.1087 28.9586 23.6878 27.7587 23.9064C26.5588 24.1251 25.3207 23.9728 24.2095 23.47L17.2995 30.38C16.9017 30.7778 16.3622 31.0013 15.7995 31.0013C15.2369 31.0013 14.6974 30.7778 14.2995 30.38C13.9017 29.9822 13.6782 29.4426 13.6782 28.88C13.6782 28.3174 13.9017 27.7778 14.2995 27.38L21.2095 20.47C20.7067 19.3588 20.5545 18.1207 20.7731 16.9208C20.9917 15.7209 21.5708 14.6161 22.4333 13.7537C23.2957 12.8913 24.4005 12.3121 25.6004 12.0935C26.8003 11.8749 28.0384 12.0271 29.1495 12.53L25.3895 16.29L25.3795 16.3Z\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"blueprint-zone\", \"south-zone\", \"guardrails-zone\"], [\"x\", \"0.679688\", \"width\", \"44\", \"height\", \"44\", \"rx\", \"8\", \"fill\", \"#DC2626\"], [\"d\", \"M30.6797 23C30.6797 28 27.1797 30.5 23.0197 31.95C22.8019 32.0238 22.5652 32.0202 22.3497 31.94C18.1797 30.5 14.6797 28 14.6797 23V16C14.6797 15.7347 14.785 15.4804 14.9726 15.2929C15.1601 15.1053 15.4145 15 15.6797 15C17.6797 15 20.1797 13.8 21.9197 12.28C22.1315 12.099 22.401 11.9995 22.6797 11.9995C22.9583 11.9995 23.2278 12.099 23.4397 12.28C25.1897 13.81 27.6797 15 29.6797 15C29.9449 15 30.1993 15.1053 30.3868 15.2929C30.5743 15.4804 30.6797 15.7347 30.6797 16V23Z\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"output-content\"], [1, \"execution-history-container\"], [\"class\", \"no-output-message\", 4, \"ngIf\"], [\"class\", \"execution-history-list\", 4, \"ngIf\"], [1, \"no-output-message\"], [1, \"execution-history-list\"], [\"class\", \"execution-item\", 3, \"success\", \"failed\", \"pending\", 4, \"ngFor\", \"ngForOf\"], [1, \"execution-item\"], [1, \"execution-header\"], [1, \"execution-status\"], [1, \"execution-details\"], [1, \"user-message\"], [1, \"execution-timestamp\"], [\"class\", \"execution-response\", 4, \"ngIf\"], [\"class\", \"execution-error\", 4, \"ngIf\"], [1, \"execution-response\"], [1, \"response-section\"], [1, \"response-text\"], [\"class\", \"task-output\", 4, \"ngFor\", \"ngForOf\"], [1, \"task-output\"], [\"class\", \"task-description\", 4, \"ngIf\"], [\"class\", \"task-expected\", 4, \"ngIf\"], [1, \"task-content\"], [1, \"task-header\"], [1, \"task-description\"], [1, \"task-expected\"], [1, \"execution-error\"], [1, \"error-message\"]],\n      template: function AgentExecutionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function AgentExecutionComponent_Template_button_click_3_listener() {\n            return ctx.navigateBack();\n          });\n          i0.ɵɵelement(4, \"ava-icon\", 4);\n          i0.ɵɵelementStart(5, \"span\", 5);\n          i0.ɵɵtext(6);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"div\", 7)(9, \"div\", 8)(10, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function AgentExecutionComponent_Template_button_click_10_listener() {\n            return ctx.toggleLeftPanel();\n          });\n          i0.ɵɵelement(11, \"ava-icon\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(12, AgentExecutionComponent_button_12_Template, 2, 0, \"button\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(13, AgentExecutionComponent_div_13_Template, 2, 12, \"div\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"div\", 13)(15, \"div\", 8)(16, \"div\", 14)(17, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function AgentExecutionComponent_Template_button_click_17_listener() {\n            return ctx.setActiveRightTab(\"blueprint\");\n          });\n          i0.ɵɵtext(18, \" Blueprint \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function AgentExecutionComponent_Template_button_click_19_listener() {\n            return ctx.setActiveRightTab(\"output\");\n          });\n          i0.ɵɵtext(20, \" Agent Output \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"div\", 16);\n          i0.ɵɵtemplate(22, AgentExecutionComponent_div_22_Template, 82, 22, \"div\", 17)(23, AgentExecutionComponent_div_23_Template, 6, 2, \"div\", 18);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.agentName);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"collapsed\", ctx.isLeftPanelCollapsed);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"iconName\", ctx.isLeftPanelCollapsed ? \"ChevronRight\" : \"PanelLeft\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLeftPanelCollapsed);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLeftPanelCollapsed);\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"active\", ctx.activeRightTab === \"blueprint\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"active\", ctx.activeRightTab === \"output\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.activeRightTab === \"blueprint\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.activeRightTab === \"output\");\n        }\n      },\n      dependencies: [CommonModule, i8.NgForOf, i8.NgIf, i8.DatePipe, FormsModule, AgentExecutionPlaygroundComponent, IconComponent],\n      styles: [\".agent-execution-container[_ngcontent-%COMP%] {\\n  height: calc(100vh - 84px);\\n  display: flex;\\n  flex-direction: column;\\n  color: var(--color-text-primary);\\n  overflow: hidden;\\n}\\n\\n.top-nav-bar[_ngcontent-%COMP%] {\\n  padding: 16px 24px;\\n  padding-bottom: 0px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  min-height: 64px;\\n}\\n\\n.nav-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.back-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 8px 12px;\\n  border-radius: 6px;\\n  transition: background-color 0.2s ease;\\n}\\n.back-button[_ngcontent-%COMP%]:hover {\\n  background: var(--color-background-tertiary);\\n}\\n.back-button[_ngcontent-%COMP%]   .agent-name[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #000000;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  gap: 16px;\\n  padding: 16px;\\n  padding-top: 0px;\\n  height: calc(100vh - 96px);\\n  overflow: hidden;\\n}\\n\\n.left-panel[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 400px;\\n  max-width: 600px;\\n  background: white;\\n  border-radius: 12px;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n  transition: all 0.3s ease;\\n}\\n.left-panel.collapsed[_ngcontent-%COMP%] {\\n  flex: 0 0 48px;\\n  min-width: 48px;\\n  max-width: 48px;\\n}\\n\\n.right-panel[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 400px;\\n  background: white;\\n  border-radius: 12px;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n\\n.panel-header[_ngcontent-%COMP%] {\\n  padding: 16px 20px;\\n  background: #e9effd;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  max-height: 45px;\\n  border-radius: 12px 12px 0 0;\\n}\\n\\n.collapse-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 6px;\\n  border-radius: 4px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: background-color 0.2s ease;\\n  color: #1a46a7;\\n}\\n.collapse-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--color-background-quaternary);\\n}\\n\\n.history-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 8px 12px;\\n  border-radius: 6px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #1a46a7;\\n  transition: all 0.2s ease;\\n}\\n.history-btn[_ngcontent-%COMP%]:disabled {\\n  cursor: not-allowed;\\n}\\n.history-btn[_ngcontent-%COMP%]:hover {\\n  color: var(--color-text-primary);\\n  background: var(--color-background-quaternary);\\n}\\n\\n.tabs-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  padding: 4px;\\n}\\n\\n.tab-btn[_ngcontent-%COMP%] {\\n  padding: 8px 20px;\\n  border: none;\\n  background: transparent;\\n  color: var(--text-secondary);\\n  border-radius: 10px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  white-space: nowrap;\\n  color: #1a46a7;\\n}\\n.tab-btn.active[_ngcontent-%COMP%] {\\n  background: white;\\n  color: var(--nav-pill-selected-color);\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n  font-weight: 600;\\n}\\n\\n.panel-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.mock-content[_ngcontent-%COMP%] {\\n  padding: 24px;\\n  height: 100%;\\n  overflow-y: auto;\\n}\\n.mock-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: var(--color-text-primary);\\n}\\n.mock-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 24px 0;\\n  color: var(--color-text-secondary);\\n  font-size: 14px;\\n  line-height: 1.5;\\n}\\n\\n.blueprint-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n}\\n\\n.blueprint-header[_ngcontent-%COMP%] {\\n  padding: 10px;\\n}\\n.blueprint-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: black;\\n  margin: 0 0 5px 0;\\n  font-size: 18px;\\n  font-weight: 600;\\n  text-align: center;\\n}\\n.blueprint-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--color-text-secondary);\\n  margin: 0;\\n  font-size: 14px;\\n}\\n\\n.blueprint-canvas-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow: auto;\\n  padding: 10px;\\n  background: var(--color-background-primary);\\n}\\n\\n.custom-blueprint-container[_ngcontent-%COMP%] {\\n  height: 100%;\\n  width: 100%;\\n  min-height: 500px;\\n  background-color: #f8f9fa;\\n  background-image: radial-gradient(circle, #d1d5db 1px, transparent 1px);\\n  background-size: 20px 20px;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  border: 1px solid #d1d3d8;\\n  position: relative;\\n  padding: 5%;\\n  border-radius: 10px;\\n}\\n\\n.central-progress[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  z-index: 10;\\n  background-color: white;\\n  border-radius: 50%;\\n  padding: 8px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-ring[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-circle[_ngcontent-%COMP%] {\\n  transform: rotate(-90deg);\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-background[_ngcontent-%COMP%] {\\n  opacity: 0.15;\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  transition: stroke-dashoffset 0.3s ease;\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-content[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  text-align: center;\\n  color: #374151;\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-percentage[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  line-height: 1;\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-label[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #6b7280;\\n  margin-top: 2px;\\n}\\n\\n#parent-box[_ngcontent-%COMP%] {\\n  height: 100%;\\n  width: 100%;\\n  gap: 2rem;\\n  position: relative;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.blueprint-zones-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 80%;\\n  height: 80%;\\n  max-width: 800px;\\n  max-height: 600px;\\n}\\n\\n#box1[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: space-between;\\n  height: 300px;\\n  position: relative;\\n  border: 1px solid #bbbec5;\\n  position: fixed;\\n  top: 32%;\\n  left: 40%;\\n  border-left: none;\\n}\\n\\n#box2[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: space-between;\\n  height: 300px;\\n  position: relative;\\n  border: 1px solid #bbbec5;\\n  position: fixed;\\n  top: 32%;\\n  left: 60%;\\n  border-right: none;\\n}\\n\\n.blueprint-zone[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  padding: 16px;\\n  transition: all 0.3s ease;\\n  position: absolute;\\n  width: 280px;\\n  z-index: 5;\\n}\\n.blueprint-zone.has-nodes[_ngcontent-%COMP%] {\\n  border-style: solid;\\n}\\n.blueprint-zone.collapsed[_ngcontent-%COMP%] {\\n  min-height: 40px;\\n}\\n.blueprint-zone.collapsed[_ngcontent-%COMP%]   .zone-header[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n\\n.north-zone[_ngcontent-%COMP%] {\\n  background-color: #ecf0fa;\\n  color: #005eb5;\\n  border: 2px solid #9ab7f6;\\n  top: 0;\\n  left: 0;\\n  overflow: hidden;\\n}\\n\\n.east-zone[_ngcontent-%COMP%] {\\n  background-color: #f2ebfd;\\n  border: 2px solid #d6c2f9;\\n  color: #d6c2f9;\\n  top: 0;\\n  right: 0;\\n  overflow: hidden;\\n}\\n\\n.south-zone[_ngcontent-%COMP%] {\\n  background-color: #fbf6f7;\\n  border: 2px solid #fecaca;\\n  color: #dc2626 !important;\\n  bottom: 0;\\n  right: 0;\\n  overflow: hidden;\\n}\\n.south-zone.has-nodes[_ngcontent-%COMP%] {\\n  height: auto;\\n  min-height: 100px;\\n  max-height: 200px;\\n}\\n.south-zone.has-nodes[_ngcontent-%COMP%]:hover {\\n  overflow-y: auto;\\n  scrollbar-width: none !important;\\n}\\n\\n.west-zone[_ngcontent-%COMP%] {\\n  background-color: #ecf8f4;\\n  border: 2px solid #a9e1cc;\\n  color: #25684f;\\n  bottom: 0;\\n  left: 0;\\n  overflow: hidden;\\n}\\n.west-zone.has-nodes[_ngcontent-%COMP%] {\\n  height: auto;\\n  min-height: 100px;\\n  max-height: 200px;\\n}\\n.west-zone.has-nodes[_ngcontent-%COMP%]:hover {\\n  overflow-y: auto;\\n  scrollbar-width: none !important;\\n}\\n\\n.connection-lines[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  pointer-events: none;\\n  z-index: 1;\\n}\\n.connection-lines[_ngcontent-%COMP%]   line[_ngcontent-%COMP%] {\\n  opacity: 0.7;\\n  transition: opacity 0.3s ease;\\n}\\n.connection-lines[_ngcontent-%COMP%]   line[_ngcontent-%COMP%]:hover {\\n  opacity: 1;\\n}\\n\\n.central-progress[_ngcontent-%COMP%] {\\n  z-index: 10;\\n}\\n\\n.blueprint-zone[_ngcontent-%COMP%] {\\n  z-index: 5;\\n}\\n\\n.zone-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin-bottom: 16px;\\n  padding-bottom: 12px;\\n  cursor: pointer;\\n  -webkit-user-select: none;\\n          user-select: none;\\n  transition: all 0.2s ease;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 8px;\\n  background-color: rgba(59, 130, 246, 0.1);\\n  color: #3b82f6;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .zone-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #374151;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .accordion-toggle[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 4px;\\n  border-radius: 4px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.2s ease;\\n  color: #6b7280;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .accordion-toggle[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 0, 0, 0.05);\\n  color: #374151;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .accordion-toggle[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  transition: transform 0.3s ease;\\n}\\n\\n.required-badge[_ngcontent-%COMP%] {\\n  background-color: var(--status-error);\\n  color: white;\\n  font-size: 10px;\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n  font-weight: 500;\\n}\\n\\n.optional-badge[_ngcontent-%COMP%] {\\n  background-color: var(--status-warning);\\n  color: white;\\n  font-size: 10px;\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n  font-weight: 500;\\n}\\n\\n.zone-content[_ngcontent-%COMP%] {\\n  min-height: 60px;\\n  flex: 1;\\n  transition: all 0.3s ease-in-out;\\n  opacity: 1;\\n  overflow: hidden;\\n}\\n\\n.empty-state[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: var(--text-secondary);\\n  font-size: 12px;\\n  padding: 20px 0;\\n  font-style: italic;\\n}\\n\\n.nodes-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n}\\n\\n.kanban-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  padding: 12px 16px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n  transition: all 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n}\\n.kanban-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\\n  transform: translateY(-1px);\\n}\\n.kanban-card[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #374151;\\n  flex: 1;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n.blueprint-placeholder[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n\\n.blueprint-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 16px;\\n  background: var(--color-background-primary);\\n  border: 1px solid var(--color-border-primary);\\n  border-radius: 8px;\\n  transition: all 0.2s ease;\\n}\\n.blueprint-item[_ngcontent-%COMP%]:hover {\\n  border-color: var(--color-border-secondary);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.blueprint-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  width: 32px;\\n  height: 32px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.blueprint-text[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: var(--color-text-primary);\\n}\\n\\n.output-placeholder[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 24px;\\n}\\n\\n.output-section[_ngcontent-%COMP%] {\\n  background: var(--color-background-primary);\\n  border: 1px solid var(--color-border-primary);\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n.output-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 16px 16px 8px 16px;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: var(--color-text-primary);\\n}\\n.output-section[_ngcontent-%COMP%]   .output-meta[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 0 16px 16px 16px;\\n  font-size: 12px;\\n  color: var(--color-text-tertiary);\\n}\\n\\n.output-preview[_ngcontent-%COMP%] {\\n  border-top: 1px solid var(--color-border-primary);\\n}\\n.output-preview[_ngcontent-%COMP%]   .code-block[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  background: var(--color-background-secondary);\\n  font-family: \\\"Monaco\\\", \\\"Menlo\\\", \\\"Ubuntu Mono\\\", monospace;\\n  font-size: 12px;\\n  line-height: 1.6;\\n  color: var(--color-text-primary);\\n  border-bottom: 1px solid var(--color-border-primary);\\n}\\n.output-preview[_ngcontent-%COMP%]   .preview-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 12px 16px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: var(--color-brand-primary);\\n  transition: all 0.2s ease;\\n  width: 100%;\\n  text-align: left;\\n}\\n.output-preview[_ngcontent-%COMP%]   .preview-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--color-background-tertiary);\\n}\\n\\n@media (max-width: 768px) {\\n  .execution-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .playground-column[_ngcontent-%COMP%], \\n   .output-column[_ngcontent-%COMP%] {\\n    flex: none;\\n    height: 50%;\\n  }\\n  .playground-column[_ngcontent-%COMP%] {\\n    border-right: none;\\n    border-bottom: 1px solid var(--color-border-primary);\\n  }\\n}\\n.row[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  margin: 0;\\n}\\n\\n.col-7[_ngcontent-%COMP%] {\\n  flex: 0 0 58.333333%;\\n  max-width: 58.333333%;\\n}\\n\\n.col-5[_ngcontent-%COMP%] {\\n  flex: 0 0 41.666667%;\\n  max-width: 41.666667%;\\n}\\n\\n*[_ngcontent-%COMP%] {\\n  box-sizing: border-box;\\n}\\n\\n[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n  height: 6px;\\n}\\n\\n[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: var(--color-background-secondary);\\n}\\n\\n[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: var(--color-border-primary);\\n  border-radius: 3px;\\n}\\n\\n[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: var(--color-border-secondary);\\n}\\n\\n.activity-placeholder[_ngcontent-%COMP%], \\n.output-placeholder[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n.activity-placeholder[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], \\n.output-placeholder[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: var(--color-text-primary);\\n}\\n.activity-placeholder[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], \\n.output-placeholder[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: var(--color-text-secondary);\\n  font-size: 14px;\\n}\\n\\n.activity-list[_ngcontent-%COMP%], \\n.output-list[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  border: 1px solid var(--color-border-primary);\\n  border-radius: 8px;\\n  padding: 16px;\\n  background: var(--color-background-secondary);\\n}\\n\\n.activity-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n  padding: 12px 0;\\n  border-bottom: 1px solid var(--color-border-primary);\\n}\\n.activity-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.activity-item[_ngcontent-%COMP%]   .timestamp[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: var(--color-text-tertiary);\\n  font-weight: 500;\\n}\\n.activity-item[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--color-text-primary);\\n}\\n\\n.output-item[_ngcontent-%COMP%] {\\n  padding: 12px 0;\\n  border-bottom: 1px solid var(--color-border-primary);\\n}\\n.output-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.output-item[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--color-text-primary);\\n  line-height: 1.5;\\n}\\n\\n.individual-output[_ngcontent-%COMP%], \\n.collaborative-output[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n.individual-output[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], \\n.collaborative-output[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: var(--color-text-primary);\\n}\\n.individual-output[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], \\n.collaborative-output[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--color-text-secondary);\\n  font-size: 14px;\\n  margin: 0;\\n}\\n\\n.output-content[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.output-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: var(--color-text-primary);\\n}\\n\\n.execution-history-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.no-output-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  height: 100%;\\n}\\n.no-output-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--color-text-secondary);\\n  font-size: 14px;\\n  text-align: center;\\n}\\n\\n.execution-history-list[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding-right: 8px;\\n}\\n.execution-history-list[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.execution-history-list[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: var(--color-background-secondary);\\n  border-radius: 3px;\\n}\\n.execution-history-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: var(--color-border-primary);\\n  border-radius: 3px;\\n}\\n.execution-history-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: var(--color-border-secondary);\\n}\\n\\n.execution-item[_ngcontent-%COMP%] {\\n  background: var(--color-background-secondary);\\n  border: 1px solid var(--color-border-primary);\\n  border-radius: 8px;\\n  margin-bottom: 16px;\\n  overflow: hidden;\\n  transition: all 0.2s ease;\\n}\\n.execution-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.execution-item.success[_ngcontent-%COMP%] {\\n  border-left: 4px solid #10b981;\\n}\\n.execution-item.failed[_ngcontent-%COMP%] {\\n  border-left: 4px solid #ef4444;\\n}\\n.execution-item.pending[_ngcontent-%COMP%] {\\n  border-left: 4px solid #f59e0b;\\n}\\n\\n.execution-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 12px 16px;\\n  background: var(--color-background-primary);\\n  border-bottom: 1px solid var(--color-border-primary);\\n}\\n\\n.agent-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 14px;\\n  color: var(--color-text-primary);\\n}\\n\\n.execution-status[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 600;\\n  padding: 4px 8px;\\n  border-radius: 4px;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.execution-status.success[_ngcontent-%COMP%] {\\n  background: rgba(16, 185, 129, 0.1);\\n  color: #10b981;\\n}\\n.execution-status.failed[_ngcontent-%COMP%] {\\n  background: rgba(239, 68, 68, 0.1);\\n  color: #ef4444;\\n}\\n.execution-status.pending[_ngcontent-%COMP%] {\\n  background: rgba(245, 158, 11, 0.1);\\n  color: #f59e0b;\\n}\\n\\n.execution-details[_ngcontent-%COMP%] {\\n  padding: 12px 16px;\\n  border-bottom: 1px solid var(--color-border-primary);\\n}\\n\\n.user-message[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--color-text-primary);\\n  margin-bottom: 8px;\\n  line-height: 1.4;\\n}\\n.user-message[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: var(--color-text-primary);\\n}\\n\\n.execution-timestamp[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: var(--color-text-tertiary);\\n}\\n\\n.execution-response[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n\\n.response-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 12px 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: var(--color-text-primary);\\n}\\n\\n.response-text[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  line-height: 1.6;\\n  color: var(--color-text-primary);\\n  white-space: pre-wrap;\\n  word-wrap: break-word;\\n  background: var(--color-background-primary);\\n  padding: 12px;\\n  border-radius: 6px;\\n  border: 1px solid var(--color-border-secondary);\\n  max-height: 300px;\\n  overflow-y: auto;\\n}\\n\\n.task-output[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.task-output[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n\\n.task-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 12px 0 12px 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: var(--color-text-primary);\\n}\\n\\n.task-description[_ngcontent-%COMP%], \\n.task-expected[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n  font-size: 14px;\\n  color: var(--color-text-secondary);\\n}\\n.task-description[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%], \\n.task-expected[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: var(--color-text-primary);\\n}\\n\\n.task-content[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  line-height: 1.6;\\n  color: var(--color-text-primary);\\n  white-space: pre-wrap;\\n  word-wrap: break-word;\\n  background: var(--color-background-primary);\\n  padding: 12px;\\n  border-radius: 6px;\\n  border: 1px solid var(--color-border-secondary);\\n  max-height: 300px;\\n  overflow-y: auto;\\n}\\n\\n.execution-error[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #ef4444;\\n  background: rgba(239, 68, 68, 0.1);\\n  padding: 12px;\\n  border-radius: 6px;\\n  border: 1px solid rgba(239, 68, 68, 0.2);\\n}\\n.error-message[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #ef4444;\\n}\\n\\n.output-box[_ngcontent-%COMP%] {\\n  background: var(--color-background-secondary);\\n  border: 1px solid var(--color-border-primary);\\n  border-radius: 8px;\\n  margin-bottom: 16px;\\n}\\n.output-box[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n\\n.output-section[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n.output-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 12px 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: var(--color-text-primary);\\n}\\n.output-section[_ngcontent-%COMP%]   .task-description[_ngcontent-%COMP%], \\n.output-section[_ngcontent-%COMP%]   .task-expected[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n  font-size: 14px;\\n  color: var(--color-text-secondary);\\n}\\n.output-section[_ngcontent-%COMP%]   .task-description[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%], \\n.output-section[_ngcontent-%COMP%]   .task-expected[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: var(--color-text-primary);\\n}\\n\\n.output-text[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  line-height: 1.6;\\n  color: var(--color-text-primary);\\n  white-space: pre-wrap;\\n  word-wrap: break-word;\\n  background: var(--color-background-primary);\\n  padding: 12px;\\n  border-radius: 6px;\\n  border: 1px solid var(--color-border-secondary);\\n  max-height: 400px;\\n  overflow-y: auto;\\n}\\n\\n.tools-zone[_ngcontent-%COMP%] {\\n  border-color: #ff8c00;\\n}\\n.tools-zone[_ngcontent-%COMP%]   .zone-header[_ngcontent-%COMP%] {\\n  border-bottom-color: rgba(255, 140, 0, 0.2);\\n}\\n\\n.guardrails-zone[_ngcontent-%COMP%] {\\n  border-color: #dc2626;\\n}\\n.guardrails-zone[_ngcontent-%COMP%]   .zone-header[_ngcontent-%COMP%] {\\n  border-bottom-color: rgba(220, 38, 38, 0.2);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return AgentExecutionComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "Subject", "takeUntil", "switchMap", "finalize", "catchError", "of", "FormsModule", "AgentExecutionPlaygroundComponent", "IconComponent", "environment", "ExecutionStatus", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "AgentExecutionComponent_div_13_Template_app_agent_execution_playground_promptChange_1_listener", "$event", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onPromptChanged", "AgentExecutionComponent_div_13_Template_app_agent_execution_playground_messageSent_1_listener", "handleChatMessage", "AgentExecutionComponent_div_13_Template_app_agent_execution_playground_conversationalToggle_1_listener", "onPlaygroundConversationalToggle", "AgentExecutionComponent_div_13_Template_app_agent_execution_playground_templateToggle_1_listener", "onPlaygroundTemplateToggle", "AgentExecutionComponent_div_13_Template_app_agent_execution_playground_filesSelected_1_listener", "onFilesSelected", "AgentExecutionComponent_div_13_Template_app_agent_execution_playground_approvalRequested_1_listener", "onApprovalRequested", "ɵɵadvance", "ɵɵproperty", "chatMessages", "isProcessingChat", "agentType", "<PERSON><PERSON><PERSON>", "ɵɵtextInterpolate", "node_r4", "name", "ɵɵtemplate", "AgentExecutionComponent_div_22_div_40_div_1_Template", "AgentExecutionComponent_div_22_div_40_div_3_Template", "blueprintPromptNodes", "length", "node_r5", "AgentExecutionComponent_div_22_div_61_div_1_Template", "AgentExecutionComponent_div_22_div_61_div_3_Template", "blueprintKnowledgeNodes", "node_r6", "AgentExecutionComponent_div_22_div_79_div_1_Template", "AgentExecutionComponent_div_22_div_79_div_3_Template", "blueprintModelNodes", "ɵɵelement", "node_r8", "AgentExecutionComponent_div_22_div_80_div_13_div_1_Template", "AgentExecutionComponent_div_22_div_80_div_13_div_3_Template", "blueprintToolNodes", "AgentExecutionComponent_div_22_div_80_Template_div_click_1_listener", "_r7", "toggleBlueprintZone", "AgentExecutionComponent_div_22_div_80__svg_svg_4_Template", "AgentExecutionComponent_div_22_div_80_div_13_Template", "ɵɵclassProp", "ɵɵstyleProp", "isBlueprintZoneExpanded", "node_r10", "AgentExecutionComponent_div_22_div_81_div_15_div_1_Template", "AgentExecutionComponent_div_22_div_81_div_15_div_3_Template", "blueprintGuardrailNodes", "AgentExecutionComponent_div_22_div_81_Template_div_click_1_listener", "_r9", "AgentExecutionComponent_div_22_div_81_div_15_Template", "AgentExecutionComponent_div_22_Template_div_click_26_listener", "_r3", "AgentExecutionComponent_div_22_div_40_Template", "AgentExecutionComponent_div_22_Template_div_click_42_listener", "AgentExecutionComponent_div_22_div_61_Template", "AgentExecutionComponent_div_22_Template_div_click_63_listener", "AgentExecutionComponent_div_22_div_79_Template", "AgentExecutionComponent_div_22_div_80_Template", "AgentExecutionComponent_div_22_div_81_Template", "blueprintCompletionPercentage", "ɵɵtextInterpolate1", "execution_r11", "response", "choices", "text", "agentResponse", "detail", "taskOutput_r12", "description", "expected_output", "AgentExecutionComponent_div_23_div_5_div_1_div_15_div_1_div_1_Template", "AgentExecutionComponent_div_23_div_5_div_1_div_15_div_1_div_2_Template", "raw", "summary", "j_r13", "AgentExecutionComponent_div_23_div_5_div_1_div_15_div_1_Template", "agent", "tasksOutputs", "AgentExecutionComponent_div_23_div_5_div_1_div_14_Template", "AgentExecutionComponent_div_23_div_5_div_1_div_15_Template", "AgentExecutionComponent_div_23_div_5_div_1_div_16_Template", "status", "ɵɵclassMap", "userMessage", "ɵɵpipeBind2", "timestamp", "AgentExecutionComponent_div_23_div_5_div_1_Template", "executionHistory", "AgentExecutionComponent_div_23_div_4_Template", "AgentExecutionComponent_div_23_div_5_Template", "AgentExecutionComponent", "route", "router", "agentService", "agentPlaygroundService", "tokenStorage", "loaderService", "formBuilder", "toolExecutionService", "navigationTabs", "id", "label", "disabled", "agentId", "agentDetail", "playgroundComp", "activityLogs", "activityProgress", "executionDetails", "isRunning", "notStarted", "inputText", "agentOutputs", "latestAgentResponse", "agentForm", "executionStartTime", "executionCompleted", "executionId", "enableStreamingLog", "enableLogStreaming", "isExecutionComplete", "progressInterval", "destroy$", "selectedTab", "demoTabs", "errorMsg", "resMessage", "taskMessage", "isJsonValid", "disable<PERSON>hat", "selectedFiles", "agentNodes", "userInputList", "progress", "isLoading", "loaderColor", "inputFieldOrder", "currentInputIndex", "activeTabId", "isLeftPanelCollapsed", "activeRightTab", "currentAgentDetails", "buildAgentNodes", "canvasNodes", "canvasEdges", "selected<PERSON><PERSON><PERSON>", "selectedAgentMode", "selectedUseCaseIdentifier", "agentFilesUploadedData", "agentAttachment", "isAgentPlaygroundLoading", "agentPlaygroundDestroy", "agentChatPayload", "agentCode", "promptOptions", "blueprintZonesExpanded", "prompt", "model", "knowledge", "guardrail", "tool", "constructor", "group", "isConversational", "isUseTemplate", "ngOnInit", "console", "log", "crypto", "randomUUID", "params", "subscribe", "queryParams", "loadAgentData", "from", "ngOnDestroy", "next", "complete", "clearInterval", "onTabChange", "event", "getCollaborativeAgentDetailsById", "handleAgentDataResponse", "error", "getAgentById", "agentData", "agentDetails", "Array", "isArray", "data", "useCaseName", "useCaseCode", "organizationPath", "loadAgentNodes", "mapAgentConfigurationToBlueprint", "message", "showAgentError", "displayMessage", "fileNames", "map", "file", "documentName", "join", "push", "Date", "get", "value", "agentMode", "useCaseIdentifier", "orgPath", "buildOrganizationPath", "agentIdentifier", "processAgentFilesAndSendMessage", "sendAgentMessageToAPI", "payload", "Number", "user", "getDaUsername", "userInputs", "question", "fileWrapper", "submitAgentExecuteWithFile", "pipe", "res", "handleAgentExecuteResponse", "err", "lastMessage", "includes", "setActiveRightTab", "submitAgentExecute", "String", "setValue", "files", "saveLogs", "exportResults", "section", "handleControlAction", "action", "navigateBack", "navigate", "mode", "editAgent", "navigateToAgentsList", "toggleLeftPanel", "tab", "zoneType", "getMetadataFromNavbar", "outputRaw", "output", "formattedOutput", "replace", "i", "formData", "FormData", "for<PERSON>ach", "fileData", "append", "has", "getFileToContent", "fileResponse", "fileContent", "fileResponses", "sendAgentMessageToAPIWithFiles", "currentChatPayloadLength", "content", "role", "levelId", "generatePrompt", "generatedResponse", "errorMessage", "pop", "fileContents", "warn", "Object", "keys", "nodeCounter", "mapIndividualAgentToBlueprint", "mapCollaborativeAgentToBlueprint", "totalNodes", "config", "config<PERSON><PERSON><PERSON>", "useCaseDetails", "type", "category", "categoryIndex", "categoryId", "categoryName", "configItem", "itemIndex", "config<PERSON><PERSON>", "config<PERSON><PERSON><PERSON>", "kbValue", "toString", "kbIds", "split", "trim", "filter", "kbId", "key", "startsWith", "guardrailName", "promptNodes", "modelNodes", "knowledgeNodes", "guardrailNodes", "totalRequired", "currentRequired", "Math", "round", "shouldCreatePromptNode", "goal", "promptNodeName", "substring", "modelReferences", "hasAgentConfigs", "agentConfigs", "modelName", "modelDetails", "modelRef", "modelRefs", "ref", "modelId", "modelDeploymentName", "knowledgeReferences", "knowledgeBaseRef", "kbRefs", "knowledgeBaseId", "knowledgeBase", "kbRef", "collectionName", "indexCollectionName", "kbName", "toolReferences", "userToolReferences", "agentConfigsContent", "hasTools", "tools", "toolsContent", "hasUserTools", "userTools", "userToolsContent", "toolRef", "toolRefs", "toolId", "userToolRef", "userToolRefs", "toolName", "userTool", "userToolId", "userToolName", "toolNodes", "promptCount", "modelCount", "knowledgeCount", "toolCount", "guardrailCount", "isCollaborative", "hasToolNodes", "toolNodeNames", "t", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "AgentServiceService", "i3", "AgentPlaygroundService", "i4", "TokenStorageService", "i5", "LoaderService", "i6", "FormBuilder", "i7", "ToolExecutionService", "selectors", "viewQuery", "AgentExecutionComponent_Query", "rf", "ctx", "AgentExecutionComponent_Template_button_click_3_listener", "AgentExecutionComponent_Template_button_click_10_listener", "AgentExecutionComponent_button_12_Template", "AgentExecutionComponent_div_13_Template", "AgentExecutionComponent_Template_button_click_17_listener", "AgentExecutionComponent_Template_button_click_19_listener", "AgentExecutionComponent_div_22_Template", "AgentExecutionComponent_div_23_Template", "i8", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\agents\\agent-execution\\agent-execution.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\pages\\agents\\agent-execution\\agent-execution.component.html"], "sourcesContent": ["import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { CommonModule } from '@angular/common';\nimport { Subject, takeUntil, switchMap, finalize, catchError, of } from 'rxjs';\nimport { FormBuilder, FormGroup, FormsModule } from '@angular/forms';\n\n// Import child components\nimport { AgentExecutionPlaygroundComponent } from './components/agent-execution-playground/agent-execution-playground.component';\nimport { ChatMessage } from '@shared/components/chat-window/chat-window.component';\nimport { IconComponent, TabItem, DropdownOption } from '@ava/play-comp-library';\nimport { AgentServiceService } from '../services/agent-service.service';\nimport { AgentPlaygroundService } from '../build-agents/services/agent-playground.service';\nimport { environment } from '@shared/environments/environment';\nimport { TokenStorageService } from '@shared/auth/services/token-storage.service';\nimport { LoaderService } from '@shared/services/loader/loader.service';\nimport { ToolExecutionService } from '@shared/services/tool-execution/tool-execution.service';\nimport { AvaTab } from '@shared/models/tab.model';\nimport { ExecutionStatus, ActivityLog, ExecutionDetails, OutputItem } from '@shared/models/execution.model';\n\n// Remove duplicate definitions - they're now in shared models\n\n@Component({\n  selector: 'app-agent-execution',\n  standalone: true,\n  imports: [\n    CommonModule,\n    FormsModule,\n    AgentExecutionPlaygroundComponent,\n    IconComponent,\n  ],\n  templateUrl: './agent-execution.component.html',\n  styleUrls: ['./agent-execution.component.scss'],\n})\nexport class AgentExecutionComponent implements OnInit, OnDestroy {\n  navigationTabs: TabItem[] = [\n    { id: 'nav-home', label: 'Agent Activity' },\n    { id: 'nav-products', label: 'Agent Output' },\n    { id: 'nav-services', label: 'Preview', disabled: true },\n  ];\n\n  // Agent details\n  agentId: string | null = null;\n  agentType: string = 'individual';\n  agentName: string = 'Agent';\n  agentDetail: string = '';\n\n  @ViewChild(AgentExecutionPlaygroundComponent, { static: false })\n  playgroundComp!: AgentExecutionPlaygroundComponent;\n\n  // Activity logs\n  activityLogs: ActivityLog[] = [];\n  activityProgress: number = 0;\n  executionDetails?: ExecutionDetails;\n  isRunning: boolean = false;\n  status: ExecutionStatus = ExecutionStatus.notStarted;\n\n  // Chat messages\n  chatMessages: ChatMessage[] = [];\n  isProcessingChat: boolean = false;\n  inputText = '';\n\n  // Agent outputs\n  agentOutputs: OutputItem[] = [];\n  latestAgentResponse: any = null; // Store the latest agent response for display\n  \n  // New properties for execution history\n  executionHistory: Array<{\n    id: string;\n    agentName: string;\n    userMessage: string;\n    status: 'pending' | 'success' | 'failed';\n    response?: any;\n    timestamp: Date;\n  }> = [];\n\n  public agentForm!: FormGroup;\n\n  // Execution state\n  executionStartTime: Date | null = null;\n  executionCompleted: boolean = false;\n  executionId!: string;\n\n  enableStreamingLog = environment.enableLogStreaming || 'all';\n\n  public isExecutionComplete: boolean = false;\n  progressInterval: any;\n\n  private destroy$ = new Subject<void>();\n  selectedTab: string = 'Agent Activity';\n  demoTabs: AvaTab[] = [\n    { id: 'tab1', label: 'History' },\n    { id: 'tab2', label: 'Blueprint' },\n    { id: 'tab3', label: 'Agent Output' },\n  ];\n\n  errorMsg = false;\n  resMessage: any;\n  taskMessage: any[] = [];\n  isJsonValid = false;\n  disableChat: boolean = false;\n  selectedFiles: File[] = [];\n  agentNodes: any[] = [];\n  userInputList: any[] = [];\n  progress = 0;\n  isLoading = false;\n  loaderColor: string = '';\n\n  inputFieldOrder: string[] = [];\n  currentInputIndex: number = 0;\n  activeTabId: string = 'nav-home';\n\n  // Panel state properties\n  isLeftPanelCollapsed: boolean = false;\n  activeRightTab: string = 'blueprint';\n\n  // Agent-specific properties\n  currentAgentDetails: any = null;\n  buildAgentNodes: any[] = [];\n  canvasNodes: any[] = [];\n  canvasEdges: any[] = [];\n  selectedPrompt: string = '';\n  selectedAgentMode: string = '';\n  selectedUseCaseIdentifier: string = '';\n  agentFilesUploadedData: any[] = [];\n  agentAttachment: string[] = [];\n  isAgentPlaygroundLoading = false;\n  agentPlaygroundDestroy = new Subject<boolean>();\n  agentChatPayload: any[] = [];\n  agentCode: string = '';\n  promptOptions: DropdownOption[] = [];\n\n  // Custom Blueprint Display Properties\n  blueprintCompletionPercentage: number = 0;\n  blueprintPromptNodes: any[] = [];\n  blueprintModelNodes: any[] = [];\n  blueprintKnowledgeNodes: any[] = [];\n  blueprintGuardrailNodes: any[] = [];\n  blueprintToolNodes: any[] = [];\n\n  // Blueprint zone expansion state\n  private blueprintZonesExpanded: { [key: string]: boolean } = {\n    prompt: true,\n    model: true,\n    knowledge: true,\n    guardrail: true,\n    tool: true,\n  };\n\n  // Blueprint panel properties (using existing arrays above)\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private agentService: AgentServiceService,\n    private agentPlaygroundService: AgentPlaygroundService,\n    private tokenStorage: TokenStorageService,\n    private loaderService: LoaderService,\n    private formBuilder: FormBuilder,\n    private toolExecutionService: ToolExecutionService,\n  ) {\n    this.agentForm = this.formBuilder.group({\n      isConversational: [true],\n      isUseTemplate: [false],\n    });\n  }\n\n  ngOnInit(): void {\n    console.log('🌟 SHARED COMPONENT INITIALIZED! 🌟');\n    this.executionId = crypto.randomUUID();\n\n    this.route.params.subscribe((params) => {\n      this.agentType = params['type'] || 'individual';\n      console.log('🌟 SHARED: Agent type set to:', this.agentType);\n    });\n\n    this.route.queryParams.subscribe((params) => {\n      if (params['id']) {\n        this.agentId = params['id'];\n        this.loadAgentData(params['id']);\n      }\n    });\n\n    // Initialize chat messages\n    this.chatMessages = [\n      {\n        from: 'ai',\n        text: `Hi there! I am ${this.agentName || 'your agent'}. How can I help you today?`,\n      },\n    ];\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n    if (this.progressInterval) {\n      clearInterval(this.progressInterval);\n    }\n  }\n\n  onTabChange(event: { id: string; label: string }) {\n    this.activeTabId = event.id;\n    this.selectedTab = event.label;\n  }\n\n  loadAgentData(agentId: string): void {\n    this.isLoading = true;\n\n    // Load agent data based on type\n    if (this.agentType === 'collaborative') {\n      this.agentService.getCollaborativeAgentDetailsById(agentId).subscribe({\n        next: (response: any) => {\n          this.handleAgentDataResponse(response);\n        },\n        error: (error: any) => {\n          console.error('Error loading collaborative agent:', error);\n          this.isLoading = false;\n        },\n      });\n    } else {\n      this.agentService.getAgentById(agentId).subscribe({\n        next: (response: any) => {\n          this.handleAgentDataResponse(response);\n        },\n        error: (error: any) => {\n          console.error('Error loading individual agent:', error);\n          this.isLoading = false;\n        },\n      });\n    }\n  }\n\n  private handleAgentDataResponse(response: any): void {\n    this.isLoading = false;\n\n    // Extract agent details\n    let agentData;\n    if (\n      response.agentDetails &&\n      Array.isArray(response.agentDetails) &&\n      response.agentDetails.length > 0\n    ) {\n      agentData = response.agentDetails[0];\n    } else if (response.agentDetail) {\n      agentData = response.agentDetail;\n    } else if (response.data) {\n      agentData = response.data;\n    } else {\n      agentData = response;\n    }\n\n    if (agentData) {\n      this.currentAgentDetails = agentData;\n      this.agentName = agentData.name || agentData.agentName || 'Agent';\n      this.agentDetail = agentData.description || agentData.agentDetail || '';\n\n      // For individual agents, set up the required properties for playground functionality\n      if (this.agentType === 'individual') {\n        // Set selectedPrompt to agent name or useCaseName to bypass dropdown requirement\n        this.selectedPrompt =\n          agentData.useCaseName || agentData.name || 'loaded-agent';\n\n        // Set selectedAgentMode for API calls - use useCaseCode if available\n        this.selectedAgentMode =\n          agentData.useCaseCode ||\n          agentData.useCaseName ||\n          agentData.name ||\n          '';\n\n        // Set useCaseIdentifier - use organizationPath if available\n        if (agentData.organizationPath) {\n          this.selectedUseCaseIdentifier = agentData.organizationPath;\n        } else if (agentData.useCaseCode) {\n          this.selectedUseCaseIdentifier = agentData.useCaseCode;\n        } else if (agentData.useCaseName) {\n          this.selectedUseCaseIdentifier = agentData.useCaseName;\n        }\n      }\n\n      // Update chat message with agent name\n      if (this.chatMessages.length > 0) {\n        this.chatMessages[0].text = `Hi there! I am ${this.agentName}. How can I help you today?`;\n      }\n\n      // Load agent nodes and configuration\n      this.loadAgentNodes(agentData);\n    }\n  }\n\n  private loadAgentNodes(agentData: any): void {\n    // Map agent configuration to blueprint panel\n    this.mapAgentConfigurationToBlueprint(agentData);\n  }\n\n  handleChatMessage(message: string): void {\n    if (this.agentType === 'individual') {\n      // For individual agents, use the loaded agent details instead of requiring dropdown selection\n      if (\n        !this.currentAgentDetails &&\n        (!this.selectedPrompt || this.selectedPrompt === 'default')\n      ) {\n        this.showAgentError(\n          'Agent details are not loaded. Please try refreshing the page.',\n        );\n        return;\n      }\n\n      let displayMessage = message;\n      if (this.agentFilesUploadedData.length > 0) {\n        const fileNames = this.agentFilesUploadedData\n          .map((file) => file.documentName)\n          .join(', ');\n        displayMessage = `${message}\\n\\n📎 Attached files: ${fileNames}`;\n      }\n\n      // Add to execution history\n      const executionId = crypto.randomUUID();\n      this.executionHistory.push({\n        id: executionId,\n        agentName: this.agentName,\n        userMessage: message,\n        status: 'pending', // Will be updated based on API response\n        timestamp: new Date()\n      });\n      console.log('Added new execution to history:', this.executionHistory);\n\n      this.chatMessages = [\n        ...this.chatMessages,\n        { from: 'user', text: displayMessage },\n        { from: 'ai', text: `${this.agentName} - Status: Pending` },\n      ];\n      this.isProcessingChat = true;\n\n      const isConversational =\n        this.agentForm.get('isConversational')?.value || false;\n      const isUseTemplate = this.agentForm.get('isUseTemplate')?.value || false;\n\n      console.log(\n        'Chat message handling - isConversational:',\n        isConversational,\n        'isUseTemplate:',\n        isUseTemplate,\n      );\n\n      // Use agent details from the loaded agent data instead of dropdown selection\n      // Mode should be the useCaseCode, not useCaseName\n      const agentMode =\n        this.agentCode ||\n        this.selectedAgentMode ||\n        this.currentAgentDetails?.useCaseCode ||\n        this.currentAgentDetails?.useCaseName ||\n        this.currentAgentDetails?.name ||\n        this.selectedPrompt;\n\n      let useCaseIdentifier = this.selectedUseCaseIdentifier;\n      if (!useCaseIdentifier) {\n        // Use organizationPath if available, otherwise build from agent details\n        if (this.currentAgentDetails?.organizationPath) {\n          useCaseIdentifier = this.currentAgentDetails.organizationPath;\n        } else {\n          const orgPath = this.buildOrganizationPath();\n          const agentIdentifier =\n            this.currentAgentDetails?.useCaseCode ||\n            this.currentAgentDetails?.useCaseName ||\n            this.currentAgentDetails?.name ||\n            agentMode;\n          useCaseIdentifier = `${agentIdentifier}${orgPath}`;\n        }\n      }\n\n      if (this.agentFilesUploadedData.length > 0) {\n        this.processAgentFilesAndSendMessage(\n          message,\n          agentMode,\n          useCaseIdentifier,\n          isConversational,\n          isUseTemplate,\n        );\n        return;\n      }\n\n      this.sendAgentMessageToAPI(\n        message,\n        agentMode,\n        useCaseIdentifier,\n        isConversational,\n        isUseTemplate,\n      );\n    } else if (this.agentType === 'collaborative') {\n      // Add to execution history for collaborative agents too\n      const executionId = crypto.randomUUID();\n      this.executionHistory.push({\n        id: executionId,\n        agentName: this.agentName,\n        userMessage: message,\n        status: 'pending', // Will be updated based on API response\n        timestamp: new Date()\n      });\n      console.log('Added new collaborative execution to history:', this.executionHistory);\n\n      this.isProcessingChat = true;\n      let payload = {\n        executionId: this.executionId,\n        agentId: Number(this.agentId),\n        user: this.tokenStorage.getDaUsername() || '<EMAIL>',\n        userInputs: { question: message },\n      };\n\n      // Add pending status to chat for collaborative agents\n      this.chatMessages = [\n        ...this.chatMessages,\n        { from: 'user', text: message },\n        { from: 'ai', text: `${this.agentName} - Status: Pending` },\n      ];\n\n      if (this.agentFilesUploadedData.length > 0) {\n        const fileWrapper = this.agentFilesUploadedData[0];\n        let displayMessage: string;\n        if (this.agentFilesUploadedData.length > 0) {\n          const fileNames = this.agentFilesUploadedData\n            .map((file) => file.documentName)\n            .join(', ');\n          displayMessage = `📎 Attached files: ${fileNames}`;\n\n          this.chatMessages = [{ from: 'user', text: displayMessage }];\n        }\n        this.agentPlaygroundService\n          .submitAgentExecuteWithFile(payload, fileWrapper)\n          .pipe(\n            finalize(() => {\n              this.isProcessingChat = false;\n              this.isAgentPlaygroundLoading = false;\n            }),\n            takeUntil(this.agentPlaygroundDestroy),\n          )\n          .subscribe({\n            next: (res) => this.handleAgentExecuteResponse(res, message),\n            error: (err: any) => {\n              // Update execution history with failed status\n              if (this.executionHistory.length > 0) {\n                this.executionHistory[this.executionHistory.length - 1].status = 'failed';\n              }\n              \n              // Update the last chat message with the final status\n              if (this.chatMessages.length > 0) {\n                const lastMessage = this.chatMessages[this.chatMessages.length - 1];\n                if (lastMessage.from === 'ai' && lastMessage.text.includes('Status: Pending')) {\n                  lastMessage.text = `${this.agentName} - Status: Failed`;\n                }\n              }\n              \n              // Switch to output tab even on failure\n              this.setActiveRightTab('output');\n            },\n          });\n      } else {\n        this.agentPlaygroundService\n          .submitAgentExecute(payload)\n          .pipe(\n            finalize(() => {\n              this.isProcessingChat = false;\n              this.isAgentPlaygroundLoading = false;\n            }),\n            takeUntil(this.agentPlaygroundDestroy),\n          )\n          .subscribe({\n            next: (res) => this.handleAgentExecuteResponse(res, message),\n            error: (err: any) => {\n              // Update execution history with failed status\n              if (this.executionHistory.length > 0) {\n                this.executionHistory[this.executionHistory.length - 1].status = 'failed';\n              }\n              \n              // Update the last chat message with the final status\n              if (this.chatMessages.length > 0) {\n                const lastMessage = this.chatMessages[this.chatMessages.length - 1];\n                if (lastMessage.from === 'ai' && lastMessage.text.includes('Status: Pending')) {\n                  lastMessage.text = `${this.agentName} - Status: Failed`;\n                }\n              }\n              \n              // Switch to output tab even on failure\n              this.setActiveRightTab('output');\n            },\n          });\n      }\n    }\n  }\n\n  onPromptChanged(prompt: DropdownOption): void {\n    this.inputText = prompt.name || String(prompt.value) || '';\n  }\n\n  onPlaygroundConversationalToggle(value: boolean): void {\n    // Update the form control\n    this.agentForm.get('isConversational')?.setValue(value);\n\n    // When conversational mode is turned off, clear the conversation history\n    // This ensures that the next message will be treated as a fresh start\n    if (!value) {\n      this.agentChatPayload = [];\n      console.log(\n        'Conversational mode disabled - cleared chat payload history',\n      );\n    } else {\n      console.log('Conversational mode enabled - will maintain chat history');\n    }\n  }\n\n  onPlaygroundTemplateToggle(value: boolean): void {\n    // Update the form control\n    this.agentForm.get('isUseTemplate')?.setValue(value);\n    console.log('Template mode toggled:', value);\n  }\n\n  onFilesSelected(files: any[]): void {\n    this.selectedFiles = files;\n    // Update agentFilesUploadedData for agent execution\n    this.agentFilesUploadedData = files;\n  }\n\n  onApprovalRequested(): void {\n    // Handle approval request\n  }\n\n  saveLogs(): void {\n    // Save execution logs\n  }\n\n  exportResults(section: 'activity' | 'output'): void {\n    // Export results\n  }\n\n  handleControlAction(action: 'play' | 'pause' | 'stop'): void {\n    // Handle execution control actions\n  }\n\n  navigateBack(): void {\n    this.router.navigate(['/build/agents', this.agentType], {\n      queryParams: { id: this.agentId, mode: 'view' },\n    });\n  }\n\n  editAgent(): void {\n    this.router.navigate(['/build/agents', this.agentType], {\n      queryParams: { id: this.agentId, mode: 'edit' },\n    });\n  }\n\n  navigateToAgentsList(): void {\n    this.router.navigate(['/build/agents']);\n  }\n\n  toggleLeftPanel(): void {\n    this.isLeftPanelCollapsed = !this.isLeftPanelCollapsed;\n  }\n\n  setActiveRightTab(tab: string): void {\n    this.activeRightTab = tab;\n  }\n\n  // Blueprint zone management methods\n  toggleBlueprintZone(zoneType: string): void {\n    this.blueprintZonesExpanded[zoneType] =\n      !this.blueprintZonesExpanded[zoneType];\n  }\n\n  isBlueprintZoneExpanded(zoneType: string): boolean {\n    return this.blueprintZonesExpanded[zoneType] || false;\n  }\n\n  // API and helper methods from build-agents component\n  private showAgentError(message: string): void {\n    this.chatMessages = [...this.chatMessages, { from: 'ai', text: message }];\n  }\n\n  private buildOrganizationPath(): string {\n    // Simple implementation - in real scenario this would be from navbar/metadata\n    return '';\n  }\n\n  private getMetadataFromNavbar(): { levelId?: number } {\n    // Simple implementation - in real scenario this would get org level mapping\n    return {};\n  }\n\n  handleAgentExecuteResponse(response: any, message: string): void {\n    try {\n      // Store the latest response for display in the output panel\n      this.latestAgentResponse = response;\n\n      // Update the latest execution history entry with success status and response\n      if (this.executionHistory.length > 0) {\n        this.executionHistory[this.executionHistory.length - 1].status = 'success';\n        this.executionHistory[this.executionHistory.length - 1].response = response;\n        console.log('Updated execution history:', this.executionHistory);\n      }\n\n      const outputRaw = response?.agentResponse?.agent?.output;\n      let formattedOutput = '';\n\n      if (outputRaw) {\n        // Directly replace escaped \\n with real newlines\n        formattedOutput = outputRaw.replace(/\\\\n/g, '\\n');\n      } else {\n        formattedOutput = response?.agentResponse?.detail;\n      }\n\n      // In playground, only show agent name and status, not the full response\n      this.chatMessages = [\n        ...this.chatMessages,\n        { from: 'user', text: message },\n        { from: 'ai', text: `${this.agentName} - Status: Success` },\n      ];\n\n      // Update the last status message from 'Pending' to 'Success'\n      if (this.chatMessages.length > 0) {\n        for (let i = this.chatMessages.length - 1; i >= 0; i--) {\n          if (this.chatMessages[i].from === 'ai' && this.chatMessages[i].text.includes('Status: Pending')) {\n            this.chatMessages[i].text = `${this.agentName} - Status: Success`;\n            break;\n          }\n        }\n      }\n\n      // Automatically switch to output tab after successful execution\n      this.setActiveRightTab('output');\n    } catch (err: any) {\n      // Update execution history with failed status\n      if (this.executionHistory.length > 0) {\n        this.executionHistory[this.executionHistory.length - 1].status = 'failed';\n        console.log('Updated execution history (failed):', this.executionHistory);\n      }\n\n      this.chatMessages = [\n        ...this.chatMessages,\n        { from: 'user', text: message },\n        { from: 'ai', text: `${this.agentName} - Status: Failed` },\n      ];\n      \n      // Update the last status message from 'Pending' to 'Failed'\n      if (this.chatMessages.length > 0) {\n        for (let i = this.chatMessages.length - 1; i >= 0; i--) {\n          if (this.chatMessages[i].from === 'ai' && this.chatMessages[i].text.includes('Status: Pending')) {\n            this.chatMessages[i].text = `${this.agentName} - Status: Failed`;\n            break;\n          }\n        }\n      }\n      \n      // Switch to output tab even on failure\n      this.setActiveRightTab('output');\n    }\n  }\n\n  private processAgentFilesAndSendMessage(\n    message: string,\n    mode: string,\n    useCaseIdentifier: string,\n    isConversational: boolean,\n    isUseTemplate: boolean,\n  ): void {\n    const formData = new FormData();\n    this.agentFilesUploadedData.forEach((fileData) => {\n      if (fileData.file) {\n        formData.append('files', fileData.file);\n      }\n    });\n\n    if (formData.has('files')) {\n      this.agentPlaygroundService\n        .getFileToContent(formData)\n        .pipe(\n          switchMap((fileResponse) => {\n            const fileContent =\n              fileResponse?.fileResponses\n                ?.map((response: any) => response.fileContent)\n                ?.join('\\n') || '';\n            this.sendAgentMessageToAPIWithFiles(\n              message,\n              mode,\n              useCaseIdentifier,\n              isConversational,\n              isUseTemplate,\n              fileContent,\n            );\n            return of(null);\n          }),\n          catchError((error) => {\n            console.error('Error parsing files:', error);\n            this.sendAgentMessageToAPI(\n              message,\n              mode,\n              useCaseIdentifier,\n              isConversational,\n              isUseTemplate,\n            );\n            return of(null);\n          }),\n        )\n        .subscribe();\n    } else {\n      this.sendAgentMessageToAPI(\n        message,\n        mode,\n        useCaseIdentifier,\n        isConversational,\n        isUseTemplate,\n      );\n    }\n  }\n\n  private sendAgentMessageToAPI(\n    message: string,\n    mode: string,\n    useCaseIdentifier: string,\n    isConversational: boolean,\n    isUseTemplate: boolean,\n  ): void {\n    console.log('API Call Parameters:', {\n      message,\n      mode,\n      useCaseIdentifier,\n      isConversational,\n      isUseTemplate,\n      currentChatPayloadLength: this.agentChatPayload.length,\n    });\n\n    if (isConversational) {\n      this.agentChatPayload.push({ content: message, role: 'user' });\n    }\n\n    const payload = isConversational ? this.agentChatPayload : message;\n    const { levelId } = this.getMetadataFromNavbar();\n\n    console.log('Final payload being sent:', payload);\n\n    this.agentPlaygroundService\n      .generatePrompt(\n        payload,\n        mode,\n        isConversational,\n        isUseTemplate,\n        this.agentAttachment,\n        useCaseIdentifier,\n        '',\n        levelId,\n      )\n      .pipe(\n        finalize(() => {\n          this.isProcessingChat = false;\n          this.isAgentPlaygroundLoading = false;\n        }),\n        takeUntil(this.agentPlaygroundDestroy),\n      )\n      .subscribe({\n        next: (generatedResponse: any) => {\n          // Call handleAgentExecuteResponse to properly handle the response\n          this.handleAgentExecuteResponse(generatedResponse, message);\n        },\n        error: (error: any) => {\n          console.error('API Error:', error);\n          \n          // Update execution history with failed status\n          if (this.executionHistory.length > 0) {\n            this.executionHistory[this.executionHistory.length - 1].status = 'failed';\n          }\n          \n          const errorMessage =\n            error?.error?.message ||\n            'An error occurred while processing your request.';\n          \n          // Show error in chat and switch to output tab\n          this.chatMessages = [\n            ...this.chatMessages,\n            { from: 'user', text: message },\n            { from: 'ai', text: `${this.agentName} - Status: Failed` },\n          ];\n          \n          // Switch to output tab even on failure\n          this.setActiveRightTab('output');\n          \n          if (isConversational && this.agentChatPayload.length > 0) {\n            this.agentChatPayload.pop();\n          }\n        },\n      });\n  }\n\n  private sendAgentMessageToAPIWithFiles(\n    message: string,\n    mode: string,\n    useCaseIdentifier: string,\n    isConversational: boolean,\n    isUseTemplate: boolean,\n    fileContents: string,\n  ): void {\n    if (isConversational) {\n      this.agentChatPayload.push({ content: message, role: 'user' });\n    }\n    const payload = isConversational ? this.agentChatPayload : message;\n    const { levelId } = this.getMetadataFromNavbar();\n\n    this.agentPlaygroundService\n      .generatePrompt(\n        payload,\n        mode,\n        isConversational,\n        isUseTemplate,\n        this.agentAttachment,\n        useCaseIdentifier,\n        fileContents,\n        levelId,\n      )\n      .pipe(\n        finalize(() => {\n          this.isProcessingChat = false;\n          this.isAgentPlaygroundLoading = false;\n        }),\n        takeUntil(this.agentPlaygroundDestroy),\n      )\n      .subscribe({\n        next: (generatedResponse: any) => {\n          // Call handleAgentExecuteResponse to properly handle the response\n          this.handleAgentExecuteResponse(generatedResponse, message);\n        },\n        error: (error: any) => {\n          console.error('API Error:', error);\n          const errorMessage =\n            error?.error?.message ||\n            'An error occurred while processing your request.';\n          this.showAgentError(errorMessage);\n          if (isConversational && this.agentChatPayload.length > 0) {\n            this.agentChatPayload.pop();\n          }\n        },\n      });\n  }\n\n  // Blueprint panel methods\n  private mapAgentConfigurationToBlueprint(agentData: any): void {\n    if (!agentData) {\n      console.warn('No agent data provided for blueprint');\n      return;\n    }\n\n    console.log('🔍 DEBUG: Full agent data received:', agentData);\n    console.log('🔍 DEBUG: Agent type:', this.agentType);\n    console.log('🔍 DEBUG: Agent data keys:', Object.keys(agentData));\n\n    // Clear existing nodes\n    this.buildAgentNodes = [];\n    this.canvasNodes = [];\n\n    let nodeCounter = 1;\n\n    // Map agent configuration to nodes based on agent type\n    if (this.agentType === 'individual') {\n      this.mapIndividualAgentToBlueprint(agentData, nodeCounter);\n    } else if (this.agentType === 'collaborative') {\n      this.mapCollaborativeAgentToBlueprint(agentData, nodeCounter);\n    }\n\n    console.log('🎯 Blueprint nodes mapped:', {\n      buildAgentNodes: this.buildAgentNodes,\n      canvasNodes: this.canvasNodes,\n      totalNodes: this.buildAgentNodes.length,\n    });\n  }\n\n  private mapIndividualAgentToBlueprint(\n    agentData: any,\n    nodeCounter: number,\n  ): void {\n    console.log('🔍 Individual agent mapping - checking fields:', {\n      config: agentData.config,\n      configLength: agentData.config?.length,\n      useCaseName: agentData.useCaseName,\n      prompt: agentData.prompt,\n      useCaseDetails: agentData.useCaseDetails,\n    });\n\n    // Clear existing blueprint nodes\n    this.blueprintPromptNodes = [];\n    this.blueprintModelNodes = [];\n    this.blueprintKnowledgeNodes = [];\n    this.blueprintGuardrailNodes = [];\n\n    // Add prompt node from \"prompt\" field\n    if (agentData.prompt) {\n      this.blueprintPromptNodes.push({\n        id: `prompt-${nodeCounter++}`,\n        name: agentData.prompt,\n        type: 'prompt',\n      });\n      console.log('✅ Added prompt node:', agentData.prompt);\n    }\n\n    // Process the config array to extract model, knowledge bases, and guardrails\n    if (agentData.config && Array.isArray(agentData.config)) {\n      console.log(\n        '🔍 Processing config array with length:',\n        agentData.config.length,\n      );\n\n      agentData.config.forEach((category: any, categoryIndex: number) => {\n        console.log(\n          `🔍 Category ${categoryIndex} (ID: ${category.categoryId}):`,\n          category.categoryName,\n        );\n\n        if (category.config && Array.isArray(category.config)) {\n          console.log(\n            `🔍 Category ${categoryIndex} has ${category.config.length} config items`,\n          );\n\n          category.config.forEach((configItem: any, itemIndex: number) => {\n            console.log(`🔍 Config item ${categoryIndex}-${itemIndex}:`, {\n              configKey: configItem.configKey,\n              configValue: configItem.configValue,\n              categoryId: configItem.categoryId,\n            });\n\n            // Handle AI Model from categoryId 1\n            if (\n              configItem.categoryId === 1 &&\n              configItem.configKey === 'MODEL' &&\n              configItem.configValue\n            ) {\n              console.log(\n                '✅ Adding AI model node from categoryId 1:',\n                configItem.configValue,\n              );\n              this.blueprintModelNodes.push({\n                id: `model-${nodeCounter++}`,\n                name: `${configItem.configKey}`,\n                type: 'model',\n              });\n            }\n\n            // Handle Knowledge Base from categoryId 2\n            if (\n              configItem.categoryId === 2 &&\n              configItem.configKey === 'RAG_KNOWLEDGEBASE_NAME' &&\n              configItem.configValue\n            ) {\n              console.log(\n                '✅ Adding knowledge base nodes from categoryId 2:',\n                configItem.configValue,\n              );\n              const kbValue = configItem.configValue.toString();\n              const kbIds = kbValue\n                .split(',')\n                .map((id: string) => id.trim())\n                .filter((id: string) => id);\n\n              kbIds.forEach((kbId: string) => {\n                this.blueprintKnowledgeNodes.push({\n                  id: `knowledge-${nodeCounter++}`,\n                  name: `Knowledge Base: ${kbId}`,\n                  type: 'knowledge',\n                });\n              });\n            }\n\n            // Handle Guardrails from categoryId 3 where configValue is true\n            if (\n              configItem.categoryId === 3 &&\n              configItem.configValue === 'true'\n            ) {\n              console.log('✅ Found enabled guardrail from categoryId 3:', {\n                key: configItem.configKey,\n                value: configItem.configValue,\n              });\n\n              if (configItem.configKey === 'ENABLE_GUARDRAILS') {\n                // Only add one general guardrail node if not already added\n                if (this.blueprintGuardrailNodes.length === 0) {\n                  this.blueprintGuardrailNodes.push({\n                    id: `guardrail-${nodeCounter++}`,\n                    name: 'Guardrails Enabled',\n                    type: 'guardrail',\n                  });\n                }\n              } else if (configItem.configKey.startsWith('GUARDRAIL_')) {\n                // Add specific guardrail nodes for enabled guardrails\n                let guardrailName = configItem.configKey;\n                if (guardrailName.startsWith('GUARDRAIL_')) {\n                  guardrailName = guardrailName\n                    .replace('GUARDRAIL_', '')\n                    .replace(/_/g, ' ');\n                }\n\n                this.blueprintGuardrailNodes.push({\n                  id: `guardrail-${nodeCounter++}`,\n                  name: `${guardrailName}`,\n                  type: 'guardrail',\n                });\n              }\n            }\n          });\n        }\n      });\n    }\n\n    console.log('🎯 Final blueprint nodes:', {\n      promptNodes: this.blueprintPromptNodes,\n      modelNodes: this.blueprintModelNodes,\n      knowledgeNodes: this.blueprintKnowledgeNodes,\n      guardrailNodes: this.blueprintGuardrailNodes,\n    });\n\n    // Calculate completion percentage\n    const totalRequired = 2; // Prompt + Model are required\n    const currentRequired =\n      this.blueprintPromptNodes.length + this.blueprintModelNodes.length;\n    this.blueprintCompletionPercentage = Math.round(\n      (currentRequired / totalRequired) * 100,\n    );\n  }\n\n  private mapCollaborativeAgentToBlueprint(\n    agentData: any,\n    nodeCounter: number,\n  ): void {\n    console.log(\n      '🚀 SHARED COMPONENT: mapCollaborativeAgentToBlueprint called!',\n    );\n    console.log('🔍 DEBUG: Collaborative agent data received:', agentData);\n    console.log(\n      '🔍 DEBUG: Collaborative agent data keys:',\n      Object.keys(agentData),\n    );\n    console.log('🔍 DEBUG: Agent type in component:', this.agentType);\n    console.log('🔍 DEBUG: Current nodeCounter:', nodeCounter);\n\n    // Clear existing blueprint nodes\n    this.blueprintPromptNodes = [];\n    this.blueprintModelNodes = [];\n    this.blueprintKnowledgeNodes = [];\n    this.blueprintToolNodes = [];\n    this.blueprintGuardrailNodes = [];\n\n    console.log('🔍 DEBUG: Cleared all blueprint node arrays');\n\n    // Add prompt node - handle different prompt structures for collaborative agents\n    const shouldCreatePromptNode =\n      agentData.goal || agentData.role || agentData.description;\n\n    console.log('🔍 DEBUG: Checking prompt node creation:', {\n      goal: agentData.goal,\n      role: agentData.role,\n      description: agentData.description,\n      shouldCreatePromptNode,\n    });\n\n    if (shouldCreatePromptNode) {\n      let promptNodeName =\n        agentData.goal ||\n        agentData.role ||\n        agentData.description ||\n        'Collaborative Agent Prompt';\n\n      // Truncate prompt if too long for display\n      if (promptNodeName.length > 150) {\n        promptNodeName = promptNodeName.substring(0, 150) + '...';\n      }\n\n      this.blueprintPromptNodes.push({\n        id: `prompt-${nodeCounter++}`,\n        name: promptNodeName,\n        type: 'prompt',\n      });\n      console.log('✅ Added collaborative prompt node:', promptNodeName);\n    }\n\n    // Add model nodes - handle both old and new API formats like build-agents\n    let modelReferences = [];\n\n    console.log('🔍 DEBUG: Checking model data:', {\n      hasAgentConfigs: !!agentData.agentConfigs,\n      agentConfigs: agentData.agentConfigs,\n      model: agentData.model,\n      modelName: agentData.modelName,\n      modelDetails: agentData.modelDetails,\n    });\n\n    // New API format: agentConfigs.modelRef (array of model IDs or objects)\n    if (agentData.agentConfigs && agentData.agentConfigs.modelRef) {\n      const modelRefs = Array.isArray(agentData.agentConfigs.modelRef)\n        ? agentData.agentConfigs.modelRef\n        : [agentData.agentConfigs.modelRef];\n\n      modelReferences = modelRefs.map((ref: any) => {\n        if (typeof ref === 'number' || typeof ref === 'string') {\n          return { modelId: ref };\n        }\n        return ref;\n      });\n    }\n    // Old API format: modelDetails\n    else if (agentData.modelDetails) {\n      modelReferences = [agentData.modelDetails];\n    }\n    // Fallback: check for model or modelName directly\n    else if (agentData.model || agentData.modelName) {\n      modelReferences = [{ modelId: agentData.model || agentData.modelName }];\n    }\n\n    modelReferences.forEach((modelRef: any) => {\n      const modelId = modelRef.modelId || modelRef.id;\n      const modelName =\n        modelRef.model ||\n        modelRef.modelDeploymentName ||\n        `Model ID: ${modelId}`;\n\n      this.blueprintModelNodes.push({\n        id: `model-${nodeCounter++}`,\n        name: modelName,\n        type: 'model',\n      });\n      console.log('✅ Added collaborative model node:', modelName);\n    });\n\n    // Add knowledge base nodes - handle both old and new API formats\n    let knowledgeReferences = [];\n\n    // New API format: agentConfigs.knowledgeBaseRef (array of KB IDs or objects)\n    if (agentData.agentConfigs && agentData.agentConfigs.knowledgeBaseRef) {\n      const kbRefs = Array.isArray(agentData.agentConfigs.knowledgeBaseRef)\n        ? agentData.agentConfigs.knowledgeBaseRef\n        : [agentData.agentConfigs.knowledgeBaseRef];\n\n      knowledgeReferences = kbRefs.map((ref: any) => {\n        if (typeof ref === 'number' || typeof ref === 'string') {\n          return { knowledgeBaseId: ref };\n        }\n        return ref;\n      });\n    }\n    // Old API format: knowledgeBase\n    else if (\n      agentData.knowledgeBase &&\n      Array.isArray(agentData.knowledgeBase)\n    ) {\n      knowledgeReferences = agentData.knowledgeBase;\n    }\n\n    knowledgeReferences.forEach((kbRef: any) => {\n      const kbId = kbRef.knowledgeBaseId || kbRef.id;\n      const collectionName = kbRef.indexCollectionName || kbRef.name;\n      const kbName = collectionName || `Knowledge Base ID: ${kbId}`;\n\n      this.blueprintKnowledgeNodes.push({\n        id: `knowledge-${nodeCounter++}`,\n        name: kbName,\n        type: 'knowledge',\n      });\n      console.log('✅ Added collaborative knowledge node:', kbName);\n    });\n\n    // Add tool nodes - handle both old and new API formats like build-agents\n    let toolReferences = [];\n    let userToolReferences = [];\n\n    console.log('🔍 DEBUG: Checking tool data:', {\n      hasAgentConfigs: !!agentData.agentConfigs,\n      agentConfigsContent: agentData.agentConfigs,\n      hasTools: agentData.tools,\n      toolsContent: agentData.tools,\n      hasUserTools: agentData.userTools,\n      userToolsContent: agentData.userTools,\n    });\n\n    // New API format: agentConfigs.toolRef and agentConfigs.userToolRef\n    if (agentData.agentConfigs) {\n      if (agentData.agentConfigs.toolRef) {\n        const toolRefs = Array.isArray(agentData.agentConfigs.toolRef)\n          ? agentData.agentConfigs.toolRef\n          : [agentData.agentConfigs.toolRef];\n\n        toolReferences = toolRefs.map((ref: any) => {\n          if (typeof ref === 'number' || typeof ref === 'string') {\n            return { toolId: ref };\n          }\n          return ref;\n        });\n      }\n      if (agentData.agentConfigs.userToolRef) {\n        const userToolRefs = Array.isArray(agentData.agentConfigs.userToolRef)\n          ? agentData.agentConfigs.userToolRef\n          : [agentData.agentConfigs.userToolRef];\n\n        userToolReferences = userToolRefs.map((ref: any) => {\n          if (typeof ref === 'number' || typeof ref === 'string') {\n            return { toolId: ref };\n          }\n          return ref;\n        });\n      }\n    }\n    // Old API format: tools and userTools\n    else {\n      if (agentData.tools && Array.isArray(agentData.tools)) {\n        toolReferences = agentData.tools;\n      }\n      if (agentData.userTools && Array.isArray(agentData.userTools)) {\n        userToolReferences = agentData.userTools;\n      }\n    }\n\n    // Process built-in tools\n    toolReferences.forEach((tool: any) => {\n      const toolId = tool.toolId || tool.id;\n      const toolName = tool.toolName || tool.name || `Tool ID: ${toolId}`;\n\n      this.blueprintToolNodes.push({\n        id: `tool-${nodeCounter++}`,\n        name: toolName,\n        type: 'tool',\n      });\n      console.log('✅ Added collaborative builtin tool node:', toolName);\n    });\n\n    // Process user tools\n    userToolReferences.forEach((userTool: any) => {\n      const userToolId = userTool.toolId || userTool.id;\n      const userToolName =\n        userTool.toolName || userTool.name || `User Tool ID: ${userToolId}`;\n\n      this.blueprintToolNodes.push({\n        id: `tool-${nodeCounter++}`,\n        name: userToolName,\n        type: 'tool',\n      });\n      console.log('✅ Added collaborative user tool node:', userToolName);\n    });\n\n    console.log('🎯 Final collaborative blueprint nodes:', {\n      promptNodes: this.blueprintPromptNodes,\n      modelNodes: this.blueprintModelNodes,\n      knowledgeNodes: this.blueprintKnowledgeNodes,\n      toolNodes: this.blueprintToolNodes,\n      guardrailNodes: this.blueprintGuardrailNodes,\n    });\n\n    // Debug: Check blueprint node arrays lengths\n    console.log('📊 Blueprint node counts:', {\n      promptCount: this.blueprintPromptNodes.length,\n      modelCount: this.blueprintModelNodes.length,\n      knowledgeCount: this.blueprintKnowledgeNodes.length,\n      toolCount: this.blueprintToolNodes.length,\n      guardrailCount: this.blueprintGuardrailNodes.length,\n    });\n\n    // Debug: Check if tools zone will be visible\n    console.log('🔧 Tools zone debug:', {\n      agentType: this.agentType,\n      isCollaborative: this.agentType === 'collaborative',\n      hasToolNodes: this.blueprintToolNodes.length > 0,\n      toolNodeNames: this.blueprintToolNodes.map((t) => t.name),\n    });\n\n    // Calculate completion percentage\n    const totalRequired = 2; // Prompt + Model are required\n    const currentRequired =\n      this.blueprintPromptNodes.length + this.blueprintModelNodes.length;\n    this.blueprintCompletionPercentage = Math.round(\n      (currentRequired / totalRequired) * 100,\n    );\n  }\n}\n", "<div class=\"agent-execution-container\">\n  <!-- Top Navigation Bar -->\n  <div class=\"top-nav-bar\">\n    <div class=\"nav-left\">\n      <button class=\"back-button\" (click)=\"navigateBack()\" type=\"button\">\n        <ava-icon\n          iconName=\"ArrowLeft\"\n          iconSize=\"16\"\n          iconColor=\"#374151\"\n        ></ava-icon>\n        <span class=\"agent-name\">{{ agentName }}</span>\n      </button>\n    </div>\n  </div>\n\n  <!-- Main Content with Two Panels -->\n  <div class=\"main-content\">\n    <!-- Left Panel: Playground -->\n    <div class=\"left-panel\" [class.collapsed]=\"isLeftPanelCollapsed\">\n      <div class=\"panel-header\">\n        <button class=\"collapse-btn\" (click)=\"toggleLeftPanel()\" type=\"button\">\n          <ava-icon\n            [iconName]=\"isLeftPanelCollapsed ? 'ChevronRight' : 'PanelLeft'\"\n            iconSize=\"16\"\n            iconColor=\"#6B7280\"\n          >\n          </ava-icon>\n        </button>\n        <button\n          class=\"history-btn\"\n          *ngIf=\"!isLeftPanelCollapsed\"\n          type=\"button\"\n          disabled\n        >\n          History\n        </button>\n      </div>\n\n      <div class=\"panel-content\" *ngIf=\"!isLeftPanelCollapsed\">\n        <app-agent-execution-playground\n          [messages]=\"chatMessages\"\n          [isLoading]=\"isProcessingChat\"\n          [agentType]=\"agentType\"\n          [showChatInteractionToggles]=\"agentType === 'individual'\"\n          [showAiPrincipleToggle]=\"false\"\n          [showApprovalButton]=\"false\"\n          [showDropdown]=\"false\"\n          [showAgentNameInput]=\"true\"\n          [showFileUploadButton]=\"true\"\n          [showStatusOnly]=\"true\"\n          [displayedAgentName]=\"agentName\"\n          [agentNamePlaceholder]=\"'Current Agent Name'\"\n          (promptChange)=\"onPromptChanged($event)\"\n          (messageSent)=\"handleChatMessage($event)\"\n          (conversationalToggle)=\"onPlaygroundConversationalToggle($event)\"\n          (templateToggle)=\"onPlaygroundTemplateToggle($event)\"\n          (filesSelected)=\"onFilesSelected($event)\"\n          (approvalRequested)=\"onApprovalRequested()\"\n        >\n        </app-agent-execution-playground>\n      </div>\n    </div>\n\n    <!-- Right Panel: Blueprint/Output -->\n    <div class=\"right-panel\">\n      <!-- Right Panel Header -->\n      <div class=\"panel-header\">\n        <div class=\"tabs-container\">\n          <button\n            class=\"tab-btn\"\n            [class.active]=\"activeRightTab === 'blueprint'\"\n            (click)=\"setActiveRightTab('blueprint')\"\n          >\n            Blueprint\n          </button>\n          <button\n            class=\"tab-btn\"\n            [class.active]=\"activeRightTab === 'output'\"\n            (click)=\"setActiveRightTab('output')\"\n          >\n            Agent Output\n          </button>\n        </div>\n      </div>\n\n      <div class=\"panel-content\">\n        <!-- Blueprint Content -->\n        <div *ngIf=\"activeRightTab === 'blueprint'\" class=\"blueprint-content\">\n          <div class=\"blueprint-header\">\n            <h3>Agent Blueprint</h3>\n          </div>\n\n          <!-- Custom Blueprint Display -->\n          <div class=\"custom-blueprint-container\">\n            <!-- SVG Connecting Lines -->\n            <svg\n              class=\"connection-lines\"\n              viewBox=\"0 0 100 100\"\n              preserveAspectRatio=\"none\"\n            >\n              <!-- Line from System Prompt (top-left) to center -->\n              <line\n                x1=\"25\"\n                y1=\"25\"\n                x2=\"50\"\n                y2=\"50\"\n                stroke=\"#BBBEC5\"\n                stroke-width=\"0.3\"\n              />\n\n              <!-- Line from AI Model (top-right) to center -->\n              <line\n                x1=\"75\"\n                y1=\"25\"\n                x2=\"50\"\n                y2=\"50\"\n                stroke=\"#BBBEC5\"\n                stroke-width=\"0.3\"\n              />\n\n              <!-- Line from Knowledge Base (bottom-left) to center -->\n              <line\n                x1=\"25\"\n                y1=\"75\"\n                x2=\"50\"\n                y2=\"50\"\n                stroke=\"#BBBEC5\"\n                stroke-width=\"0.3\"\n              />\n\n              <!-- Line from Guardrails (bottom-right) to center -->\n              <line\n                x1=\"75\"\n                y1=\"75\"\n                x2=\"50\"\n                y2=\"50\"\n                stroke=\"#BBBEC5\"\n                stroke-width=\"0.3\"\n              />\n            </svg>\n\n            <!-- Central Progress Bar -->\n            <div class=\"central-progress\">\n              <div class=\"progress-ring\">\n                <svg class=\"progress-circle\" width=\"120\" height=\"120\">\n                  <defs>\n                    <linearGradient\n                      id=\"progressGradient\"\n                      x1=\"0%\"\n                      y1=\"0%\"\n                      x2=\"100%\"\n                      y2=\"100%\"\n                    >\n                      <stop offset=\"33.91%\" style=\"stop-color: #0084ff\" />\n                      <stop offset=\"100%\" style=\"stop-color: #03bdd4\" />\n                    </linearGradient>\n                  </defs>\n                  <circle\n                    class=\"progress-background\"\n                    cx=\"60\"\n                    cy=\"60\"\n                    r=\"50\"\n                    fill=\"none\"\n                    stroke=\"#e5e7eb\"\n                    stroke-width=\"8\"\n                  />\n                  <circle\n                    class=\"progress-bar\"\n                    cx=\"60\"\n                    cy=\"60\"\n                    r=\"50\"\n                    fill=\"none\"\n                    stroke=\"url(#progressGradient)\"\n                    stroke-width=\"8\"\n                    stroke-linecap=\"round\"\n                    [style.stroke-dasharray]=\"314\"\n                    [style.stroke-dashoffset]=\"\n                      314 - (314 * blueprintCompletionPercentage) / 100\n                    \"\n                    transform=\"rotate(180 60 60)\"\n                  />\n                </svg>\n                <div class=\"progress-content\">\n                  <div class=\"progress-percentage\">\n                    {{ blueprintCompletionPercentage }}%\n                  </div>\n                  <div class=\"progress-label\">Complete</div>\n                </div>\n              </div>\n            </div>\n\n            <!-- Blueprint Zones Layout -->\n            <div id=\"parent-box\">\n              <!-- <div id=\"box1\"> -->\n              <!-- System Prompt Zone -->\n              <div\n                class=\"blueprint-zone north-zone prompts-zone\"\n                [class.has-nodes]=\"blueprintPromptNodes.length > 0\"\n              >\n                <div\n                  class=\"zone-header\"\n                  (click)=\"toggleBlueprintZone('prompt')\"\n                >\n                  <div class=\"header-content\">\n                    <div class=\"header-icon\">\n                      <svg\n                        width=\"45\"\n                        height=\"44\"\n                        viewBox=\"0 0 45 44\"\n                        fill=\"none\"\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                      >\n                        <rect\n                          x=\"0.679688\"\n                          width=\"44\"\n                          height=\"44\"\n                          rx=\"8\"\n                          fill=\"#5082EF\"\n                        />\n                        <path\n                          d=\"M20.6166 25.5C20.5273 25.1539 20.347 24.8381 20.0942 24.5854C19.8415 24.3327 19.5257 24.1523 19.1796 24.063L13.0446 22.481C12.94 22.4513 12.8478 22.3883 12.7822 22.3014C12.7166 22.2146 12.6812 22.1088 12.6812 22C12.6812 21.8912 12.7166 21.7854 12.7822 21.6986C12.8478 21.6118 12.94 21.5487 13.0446 21.519L19.1796 19.936C19.5256 19.8468 19.8413 19.6666 20.094 19.414C20.3467 19.1615 20.5272 18.8459 20.6166 18.5L22.1986 12.365C22.228 12.2599 22.291 12.1673 22.3779 12.1014C22.4649 12.0355 22.571 11.9998 22.6801 11.9998C22.7892 11.9998 22.8954 12.0355 22.9823 12.1014C23.0692 12.1673 23.1322 12.2599 23.1616 12.365L24.7426 18.5C24.8319 18.8461 25.0123 19.1619 25.265 19.4146C25.5177 19.6673 25.8336 19.8477 26.1796 19.937L32.3146 21.518C32.4201 21.5471 32.5132 21.61 32.5795 21.6971C32.6458 21.7841 32.6817 21.8906 32.6817 22C32.6817 22.1094 32.6458 22.2159 32.5795 22.3029C32.5132 22.39 32.4201 22.4529 32.3146 22.482L26.1796 24.063C25.8336 24.1523 25.5177 24.3327 25.265 24.5854C25.0123 24.8381 24.8319 25.1539 24.7426 25.5L23.1606 31.635C23.1312 31.7401 23.0682 31.8327 22.9813 31.8986C22.8944 31.9646 22.7882 32.0003 22.6791 32.0003C22.57 32.0003 22.4639 31.9646 22.3769 31.8986C22.29 31.8327 22.227 31.7401 22.1976 31.635L20.6166 25.5Z\"\n                          stroke=\"white\"\n                          stroke-width=\"2\"\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                        />\n                      </svg>\n                    </div>\n                    <h3 class=\"zone-title\">System Prompt</h3>\n                  </div>\n                  <div class=\"header-actions\">\n                    <span class=\"required-badge\">Required</span>\n                    <button class=\"accordion-toggle\" type=\"button\">\n                      <svg\n                        width=\"16\"\n                        height=\"16\"\n                        viewBox=\"0 0 24 24\"\n                        fill=\"none\"\n                        [style.transform]=\"\n                          isBlueprintZoneExpanded('prompt')\n                            ? 'rotate(180deg)'\n                            : 'rotate(0deg)'\n                        \"\n                      >\n                        <path\n                          d=\"M6 9L12 15L18 9\"\n                          stroke=\"currentColor\"\n                          stroke-width=\"2\"\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                        />\n                      </svg>\n                    </button>\n                  </div>\n                </div>\n                <div\n                  class=\"zone-content\"\n                  *ngIf=\"isBlueprintZoneExpanded('prompt')\"\n                >\n                  <div\n                    *ngIf=\"blueprintPromptNodes.length === 0\"\n                    class=\"empty-state\"\n                  >\n                    No prompt configured\n                  </div>\n                  <div class=\"nodes-list\">\n                    <div\n                      *ngFor=\"let node of blueprintPromptNodes\"\n                      class=\"kanban-card\"\n                    >\n                      <span class=\"card-title\">{{\n                        node.name || \"Prompt\"\n                      }}</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Knowledge Base Zone -->\n              <div\n                class=\"blueprint-zone west-zone knowledge-zone\"\n                [class.has-nodes]=\"blueprintKnowledgeNodes.length > 0\"\n              >\n                <div\n                  class=\"zone-header\"\n                  (click)=\"toggleBlueprintZone('knowledge')\"\n                >\n                  <div class=\"header-content\">\n                    <div class=\"header-icon\">\n                      <svg\n                        width=\"45\"\n                        height=\"44\"\n                        viewBox=\"0 0 45 44\"\n                        fill=\"none\"\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                      >\n                        <rect\n                          x=\"0.679688\"\n                          width=\"44\"\n                          height=\"44\"\n                          rx=\"8\"\n                          fill=\"#308666\"\n                        />\n                        <path\n                          d=\"M22.6797 17V31\"\n                          stroke=\"white\"\n                          stroke-width=\"2\"\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                        />\n                        <path\n                          d=\"M26.6797 22H28.6797\"\n                          stroke=\"white\"\n                          stroke-width=\"2\"\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                        />\n                        <path\n                          d=\"M26.6797 18H28.6797\"\n                          stroke=\"white\"\n                          stroke-width=\"2\"\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                        />\n                        <path\n                          d=\"M13.6797 28C13.4145 28 13.1601 27.8946 12.9726 27.7071C12.785 27.5196 12.6797 27.2652 12.6797 27V14C12.6797 13.7348 12.785 13.4804 12.9726 13.2929C13.1601 13.1054 13.4145 13 13.6797 13H18.6797C19.7406 13 20.758 13.4214 21.5081 14.1716C22.2583 14.9217 22.6797 15.9391 22.6797 17C22.6797 15.9391 23.1011 14.9217 23.8513 14.1716C24.6014 13.4214 25.6188 13 26.6797 13H31.6797C31.9449 13 32.1993 13.1054 32.3868 13.2929C32.5743 13.4804 32.6797 13.7348 32.6797 14V27C32.6797 27.2652 32.5743 27.5196 32.3868 27.7071C32.1993 27.8946 31.9449 28 31.6797 28H25.6797C24.884 28 24.121 28.3161 23.5584 28.8787C22.9958 29.4413 22.6797 30.2044 22.6797 31C22.6797 30.2044 22.3636 29.4413 21.801 28.8787C21.2384 28.3161 20.4753 28 19.6797 28H13.6797Z\"\n                          stroke=\"white\"\n                          stroke-width=\"2\"\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                        />\n                        <path\n                          d=\"M16.6797 22H18.6797\"\n                          stroke=\"white\"\n                          stroke-width=\"2\"\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                        />\n                        <path\n                          d=\"M16.6797 18H18.6797\"\n                          stroke=\"white\"\n                          stroke-width=\"2\"\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                        />\n                      </svg>\n                    </div>\n                    <h3 class=\"zone-title\">Knowledgebase</h3>\n                  </div>\n                  <div class=\"header-actions\">\n                    <span class=\"optional-badge\">Optional</span>\n                    <button class=\"accordion-toggle\" type=\"button\">\n                      <svg\n                        width=\"16\"\n                        height=\"16\"\n                        viewBox=\"0 0 24 24\"\n                        fill=\"none\"\n                        [style.transform]=\"\n                          isBlueprintZoneExpanded('knowledge')\n                            ? 'rotate(180deg)'\n                            : 'rotate(0deg)'\n                        \"\n                      >\n                        <path\n                          d=\"M6 9L12 15L18 9\"\n                          stroke=\"currentColor\"\n                          stroke-width=\"2\"\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                        />\n                      </svg>\n                    </button>\n                  </div>\n                </div>\n                <div\n                  class=\"zone-content\"\n                  *ngIf=\"isBlueprintZoneExpanded('knowledge')\"\n                >\n                  <div\n                    *ngIf=\"blueprintKnowledgeNodes.length === 0\"\n                    class=\"empty-state\"\n                  >\n                    No knowledge base configured\n                  </div>\n                  <div class=\"nodes-list\">\n                    <div\n                      *ngFor=\"let node of blueprintKnowledgeNodes\"\n                      class=\"kanban-card\"\n                    >\n                      <span class=\"card-title\">{{\n                        node.name || \"Knowledge Base\"\n                      }}</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n              <!-- </div> -->\n\n              <!-- <div id=\"box2\"> -->\n              <!-- AI Model Zone -->\n              <div\n                class=\"blueprint-zone east-zone models-zone\"\n                [class.has-nodes]=\"blueprintModelNodes.length > 0\"\n              >\n                <div class=\"zone-header\" (click)=\"toggleBlueprintZone('model')\">\n                  <div class=\"header-content\">\n                    <div class=\"header-icon\">\n                      <svg\n                        width=\"45\"\n                        height=\"44\"\n                        viewBox=\"0 0 45 44\"\n                        fill=\"none\"\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                      >\n                        <rect\n                          x=\"0.679688\"\n                          width=\"44\"\n                          height=\"44\"\n                          rx=\"8\"\n                          fill=\"#997BCF\"\n                        />\n                        <path\n                          d=\"M31.6797 18C31.6793 17.6493 31.5867 17.3048 31.4112 17.0012C31.2357 16.6975 30.9834 16.4454 30.6797 16.27L23.6797 12.27C23.3756 12.0945 23.0308 12.0021 22.6797 12.0021C22.3286 12.0021 21.9837 12.0945 21.6797 12.27L14.6797 16.27C14.376 16.4454 14.1237 16.6975 13.9481 17.0012C13.7726 17.3048 13.68 17.6493 13.6797 18V26C13.68 26.3508 13.7726 26.6952 13.9481 26.9989C14.1237 27.3025 14.376 27.5547 14.6797 27.73L21.6797 31.73C21.9837 31.9056 22.3286 31.998 22.6797 31.998C23.0308 31.998 23.3756 31.9056 23.6797 31.73L30.6797 27.73C30.9834 27.5547 31.2357 27.3025 31.4112 26.9989C31.5867 26.6952 31.6793 26.3508 31.6797 26V18Z\"\n                          stroke=\"white\"\n                          stroke-width=\"2\"\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                        />\n                        <path\n                          d=\"M13.9795 17L22.6795 22L31.3795 17\"\n                          stroke=\"white\"\n                          stroke-width=\"2\"\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                        />\n                        <path\n                          d=\"M22.6797 32V22\"\n                          stroke=\"white\"\n                          stroke-width=\"2\"\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                        />\n                      </svg>\n                    </div>\n                    <h3 class=\"zone-title\">AI Model</h3>\n                  </div>\n                  <div class=\"header-actions\">\n                    <span class=\"required-badge\">Required</span>\n                    <button class=\"accordion-toggle\" type=\"button\">\n                      <svg\n                        width=\"16\"\n                        height=\"16\"\n                        viewBox=\"0 0 24 24\"\n                        fill=\"none\"\n                        [style.transform]=\"\n                          isBlueprintZoneExpanded('model')\n                            ? 'rotate(180deg)'\n                            : 'rotate(0deg)'\n                        \"\n                      >\n                        <path\n                          d=\"M6 9L12 15L18 9\"\n                          stroke=\"currentColor\"\n                          stroke-width=\"2\"\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                        />\n                      </svg>\n                    </button>\n                  </div>\n                </div>\n                <div\n                  class=\"zone-content\"\n                  *ngIf=\"isBlueprintZoneExpanded('model')\"\n                >\n                  <div\n                    *ngIf=\"blueprintModelNodes.length === 0\"\n                    class=\"empty-state\"\n                  >\n                    No model configured\n                  </div>\n                  <div class=\"nodes-list\">\n                    <div\n                      *ngFor=\"let node of blueprintModelNodes\"\n                      class=\"kanban-card\"\n                    >\n                      <span class=\"card-title\">{{ node.name || \"Model\" }}</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Tools Zone (for Collaborative Agents) -->\n              <div\n                *ngIf=\"agentType === 'collaborative'\"\n                class=\"blueprint-zone south-zone tools-zone\"\n                [class.has-nodes]=\"blueprintToolNodes.length > 0\"\n              >\n                <div class=\"zone-header\" (click)=\"toggleBlueprintZone('tool')\">\n                  <div class=\"header-content\">\n                    <div class=\"header-icon\">\n                      <svg\n                        width=\"45\"\n                        height=\"44\"\n                        viewBox=\"0 0 45 44\"\n                        fill=\"none\"\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        *ngIf=\"agentType === 'collaborative'\"\n                      >\n                        <rect\n                          x=\"0.679688\"\n                          width=\"44\"\n                          height=\"44\"\n                          rx=\"8\"\n                          fill=\"#D97706\"\n                        />\n                        <path\n                          d=\"M25.3795 16.3C25.1963 16.4869 25.0937 16.7382 25.0937 17C25.0937 17.2617 25.1963 17.5131 25.3795 17.7L26.9795 19.3C27.1665 19.4832 27.4178 19.5858 27.6795 19.5858C27.9413 19.5858 28.1926 19.4832 28.3795 19.3L32.1495 15.53C32.6524 16.6412 32.8046 17.8792 32.586 19.0791C32.3674 20.279 31.7883 21.3838 30.9258 22.2463C30.0634 23.1087 28.9586 23.6878 27.7587 23.9064C26.5588 24.1251 25.3207 23.9728 24.2095 23.47L17.2995 30.38C16.9017 30.7778 16.3622 31.0013 15.7995 31.0013C15.2369 31.0013 14.6974 30.7778 14.2995 30.38C13.9017 29.9822 13.6782 29.4426 13.6782 28.88C13.6782 28.3174 13.9017 27.7778 14.2995 27.38L21.2095 20.47C20.7067 19.3588 20.5545 18.1207 20.7731 16.9208C20.9917 15.7209 21.5708 14.6161 22.4333 13.7537C23.2957 12.8913 24.4005 12.3121 25.6004 12.0935C26.8003 11.8749 28.0384 12.0271 29.1495 12.53L25.3895 16.29L25.3795 16.3Z\"\n                          stroke=\"white\"\n                          stroke-width=\"2\"\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                        />\n                      </svg>\n                    </div>\n                    <h3 class=\"zone-title\">Tools</h3>\n                  </div>\n                  <div class=\"header-actions\">\n                    <span class=\"optional-badge\">Optional</span>\n                    <button class=\"accordion-toggle\" type=\"button\">\n                      <svg\n                        width=\"16\"\n                        height=\"16\"\n                        viewBox=\"0 0 24 24\"\n                        fill=\"none\"\n                        [style.transform]=\"\n                          isBlueprintZoneExpanded('tool')\n                            ? 'rotate(180deg)'\n                            : 'rotate(0deg)'\n                        \"\n                      >\n                        <path\n                          d=\"M6 9L12 15L18 9\"\n                          stroke=\"currentColor\"\n                          stroke-width=\"2\"\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                        />\n                      </svg>\n                    </button>\n                  </div>\n                </div>\n                <div\n                  class=\"zone-content\"\n                  *ngIf=\"isBlueprintZoneExpanded('tool')\"\n                >\n                  <div\n                    *ngIf=\"blueprintToolNodes.length === 0\"\n                    class=\"empty-state\"\n                  >\n                    No tools configured\n                  </div>\n                  <div class=\"nodes-list\">\n                    <div\n                      *ngFor=\"let node of blueprintToolNodes\"\n                      class=\"kanban-card\"\n                    >\n                      <span class=\"card-title\">{{ node.name || \"Tool\" }}</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Guardrails Zone (for Individual Agents) -->\n              <div\n                *ngIf=\"agentType === 'individual'\"\n                class=\"blueprint-zone south-zone guardrails-zone\"\n                [class.has-nodes]=\"blueprintGuardrailNodes.length > 0\"\n              >\n                <div\n                  class=\"zone-header\"\n                  (click)=\"toggleBlueprintZone('guardrail')\"\n                >\n                  <div class=\"header-content\">\n                    <div class=\"header-icon\">\n                      <svg\n                        width=\"45\"\n                        height=\"44\"\n                        viewBox=\"0 0 45 44\"\n                        fill=\"none\"\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                      >\n                        <rect\n                          x=\"0.679688\"\n                          width=\"44\"\n                          height=\"44\"\n                          rx=\"8\"\n                          fill=\"#DC2626\"\n                        />\n                        <path\n                          d=\"M30.6797 23C30.6797 28 27.1797 30.5 23.0197 31.95C22.8019 32.0238 22.5652 32.0202 22.3497 31.94C18.1797 30.5 14.6797 28 14.6797 23V16C14.6797 15.7347 14.785 15.4804 14.9726 15.2929C15.1601 15.1053 15.4145 15 15.6797 15C17.6797 15 20.1797 13.8 21.9197 12.28C22.1315 12.099 22.401 11.9995 22.6797 11.9995C22.9583 11.9995 23.2278 12.099 23.4397 12.28C25.1897 13.81 27.6797 15 29.6797 15C29.9449 15 30.1993 15.1053 30.3868 15.2929C30.5743 15.4804 30.6797 15.7347 30.6797 16V23Z\"\n                          stroke=\"white\"\n                          stroke-width=\"2\"\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                        />\n                      </svg>\n                    </div>\n                    <h3 class=\"zone-title\">Guardrails</h3>\n                  </div>\n                  <div class=\"header-actions\">\n                    <span class=\"optional-badge\">Optional</span>\n                    <button class=\"accordion-toggle\" type=\"button\">\n                      <svg\n                        width=\"16\"\n                        height=\"16\"\n                        viewBox=\"0 0 24 24\"\n                        fill=\"none\"\n                        [style.transform]=\"\n                          isBlueprintZoneExpanded('guardrail')\n                            ? 'rotate(180deg)'\n                            : 'rotate(0deg)'\n                        \"\n                      >\n                        <path\n                          d=\"M6 9L12 15L18 9\"\n                          stroke=\"currentColor\"\n                          stroke-width=\"2\"\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                        />\n                      </svg>\n                    </button>\n                  </div>\n                </div>\n                <div\n                  class=\"zone-content\"\n                  *ngIf=\"isBlueprintZoneExpanded('guardrail')\"\n                >\n                  <div\n                    *ngIf=\"blueprintGuardrailNodes.length === 0\"\n                    class=\"empty-state\"\n                  >\n                    No guardrails configured\n                  </div>\n                  <div class=\"nodes-list\">\n                    <div\n                      *ngFor=\"let node of blueprintGuardrailNodes\"\n                      class=\"kanban-card\"\n                    >\n                      <span class=\"card-title\">{{\n                        node.name || \"Guardrail\"\n                      }}</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n              <!-- </div> -->\n            </div>\n          </div>\n        </div>\n\n        <!-- Agent Output Content -->\n        <div *ngIf=\"activeRightTab === 'output'\" class=\"output-content\">\n          <h3>Agent Output</h3>\n          \n          <!-- Execution History with Scroll -->\n          <div class=\"execution-history-container\">\n            <div *ngIf=\"executionHistory.length === 0\" class=\"no-output-message\">\n              <p>No agent outputs available yet. Send a message to see the response.</p>\n            </div>\n            \n            <div *ngIf=\"executionHistory.length > 0\" class=\"execution-history-list\">\n              <div \n                *ngFor=\"let execution of executionHistory; let i = index\" \n                class=\"execution-item\"\n                [class.success]=\"execution.status === 'success'\"\n                [class.failed]=\"execution.status === 'failed'\"\n                [class.pending]=\"execution.status === 'pending'\"\n              >\n                <div class=\"execution-header\">\n                  <div class=\"agent-name\">{{ execution.agentName }}</div>\n                  <div class=\"execution-status\" [class]=\"execution.status\">\n                    {{ execution.status === 'success' ? 'Success' : execution.status === 'failed' ? 'Failed' : 'Pending' }}\n                  </div>\n                </div>\n                \n                <div class=\"execution-details\">\n                  <div class=\"user-message\">\n                    <strong>Query:</strong> {{ execution.userMessage }}\n                  </div>\n                  \n                  <div class=\"execution-timestamp\">\n                    {{ execution.timestamp | date:'medium' }}\n                  </div>\n                </div>\n                \n                <!-- Individual Agent Response -->\n                <div *ngIf=\"execution.status === 'success' && execution.response && agentType === 'individual'\" class=\"execution-response\">\n                  <div class=\"response-section\">\n                    <h4>Response</h4>\n                    <div class=\"response-text\">\n                      {{ execution.response?.response?.choices?.[0]?.text || execution.response?.agentResponse?.detail || 'No response content available.' }}\n                    </div>\n                  </div>\n                </div>\n                \n                <!-- Collaborative Agent Response -->\n                <div *ngIf=\"execution.status === 'success' && execution.response && agentType === 'collaborative'\" class=\"execution-response\">\n                  <div \n                    *ngFor=\"let taskOutput of execution.response?.agentResponse?.agent?.tasksOutputs; let j = index\"\n                    class=\"task-output\"\n                  >\n                    <div *ngIf=\"taskOutput.description\" class=\"task-description\">\n                      <strong>Description:</strong> {{ taskOutput.description }}\n                    </div>\n                    <div *ngIf=\"taskOutput.expected_output\" class=\"task-expected\">\n                      <strong>Expected Output:</strong> {{ taskOutput.expected_output }}\n                    </div>\n                    <div class=\"task-content\">\n                      {{ taskOutput.raw }}\n                    </div>\n                    <div class=\"task-header\">\n                      <h4>Summary: {{ taskOutput.summary || 'Task ' + (j + 1) }}</h4>\n                    </div>\n                  </div>\n                </div>\n                \n                <!-- Failed Response -->\n                <div *ngIf=\"execution.status === 'failed'\" class=\"execution-error\">\n                  <div class=\"error-message\">\n                    <strong>Error:</strong> The agent execution failed. Please try again.\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,OAAO,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,EAAE,QAAQ,MAAM;AAC9E,SAAiCC,WAAW,QAAQ,gBAAgB;AAEpE;AACA,SAASC,iCAAiC,QAAQ,8EAA8E;AAEhI,SAASC,aAAa,QAAiC,wBAAwB;AAG/E,SAASC,WAAW,QAAQ,kCAAkC;AAK9D,SAASC,eAAe,QAAmD,gCAAgC;;;;;;;;;;;;ICWnGC,EAAA,CAAAC,cAAA,iBAKC;IACCD,EAAA,CAAAE,MAAA,gBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAITH,EADF,CAAAC,cAAA,cAAyD,yCAoBtD;IADCD,EALA,CAAAI,UAAA,0BAAAC,+FAAAC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAgBF,MAAA,CAAAG,eAAA,CAAAN,MAAA,CAAuB;IAAA,EAAC,yBAAAO,8FAAAP,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CACzBF,MAAA,CAAAK,iBAAA,CAAAR,MAAA,CAAyB;IAAA,EAAC,kCAAAS,uGAAAT,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CACjBF,MAAA,CAAAO,gCAAA,CAAAV,MAAA,CAAwC;IAAA,EAAC,4BAAAW,iGAAAX,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAC/CF,MAAA,CAAAS,0BAAA,CAAAZ,MAAA,CAAkC;IAAA,EAAC,2BAAAa,gGAAAb,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CACpCF,MAAA,CAAAW,eAAA,CAAAd,MAAA,CAAuB;IAAA,EAAC,+BAAAe,oGAAA;MAAArB,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CACpBF,MAAA,CAAAa,mBAAA,EAAqB;IAAA,EAAC;IAG/CtB,EADE,CAAAG,YAAA,EAAiC,EAC7B;;;;IApBFH,EAAA,CAAAuB,SAAA,EAAyB;IAWzBvB,EAXA,CAAAwB,UAAA,aAAAf,MAAA,CAAAgB,YAAA,CAAyB,cAAAhB,MAAA,CAAAiB,gBAAA,CACK,cAAAjB,MAAA,CAAAkB,SAAA,CACP,+BAAAlB,MAAA,CAAAkB,SAAA,kBACkC,gCAC1B,6BACH,uBACN,4BACK,8BACE,wBACN,uBAAAlB,MAAA,CAAAmB,SAAA,CACS,8CACa;;;;;IAgNrC5B,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAMFH,EAJF,CAAAC,cAAA,cAGC,eAC0B;IAAAD,EAAA,CAAAE,MAAA,GAEvB;IACJF,EADI,CAAAG,YAAA,EAAO,EACL;;;;IAHqBH,EAAA,CAAAuB,SAAA,GAEvB;IAFuBvB,EAAA,CAAA6B,iBAAA,CAAAC,OAAA,CAAAC,IAAA,aAEvB;;;;;IAjBR/B,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAgC,UAAA,IAAAC,oDAAA,kBAGC;IAGDjC,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAgC,UAAA,IAAAE,oDAAA,kBAGC;IAMLlC,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAfDH,EAAA,CAAAuB,SAAA,EAAuC;IAAvCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAA0B,oBAAA,CAAAC,MAAA,OAAuC;IAOrBpC,EAAA,CAAAuB,SAAA,GAAuB;IAAvBvB,EAAA,CAAAwB,UAAA,YAAAf,MAAA,CAAA0B,oBAAA,CAAuB;;;;;IA+G5CnC,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,MAAA,qCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAMFH,EAJF,CAAAC,cAAA,cAGC,eAC0B;IAAAD,EAAA,CAAAE,MAAA,GAEvB;IACJF,EADI,CAAAG,YAAA,EAAO,EACL;;;;IAHqBH,EAAA,CAAAuB,SAAA,GAEvB;IAFuBvB,EAAA,CAAA6B,iBAAA,CAAAQ,OAAA,CAAAN,IAAA,qBAEvB;;;;;IAjBR/B,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAgC,UAAA,IAAAM,oDAAA,kBAGC;IAGDtC,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAgC,UAAA,IAAAO,oDAAA,kBAGC;IAMLvC,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAfDH,EAAA,CAAAuB,SAAA,EAA0C;IAA1CvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAA+B,uBAAA,CAAAJ,MAAA,OAA0C;IAOxBpC,EAAA,CAAAuB,SAAA,GAA0B;IAA1BvB,EAAA,CAAAwB,UAAA,YAAAf,MAAA,CAAA+B,uBAAA,CAA0B;;;;;IAyF/CxC,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAMFH,EAJF,CAAAC,cAAA,cAGC,eAC0B;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IACrDF,EADqD,CAAAG,YAAA,EAAO,EACtD;;;;IADqBH,EAAA,CAAAuB,SAAA,GAA0B;IAA1BvB,EAAA,CAAA6B,iBAAA,CAAAY,OAAA,CAAAV,IAAA,YAA0B;;;;;IAfzD/B,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAgC,UAAA,IAAAU,oDAAA,kBAGC;IAGD1C,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAgC,UAAA,IAAAW,oDAAA,kBAGC;IAIL3C,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAbDH,EAAA,CAAAuB,SAAA,EAAsC;IAAtCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAmC,mBAAA,CAAAR,MAAA,OAAsC;IAOpBpC,EAAA,CAAAuB,SAAA,GAAsB;IAAtBvB,EAAA,CAAAwB,UAAA,YAAAf,MAAA,CAAAmC,mBAAA,CAAsB;;;;;;IAkBvC5C,EAAA,CAAAC,cAAA,cAOC;IAQCD,EAPA,CAAA6C,SAAA,eAME,eAOA;IACJ7C,EAAA,CAAAG,YAAA,EAAM;;;;;IAiCVH,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAMFH,EAJF,CAAAC,cAAA,cAGC,eAC0B;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IACpDF,EADoD,CAAAG,YAAA,EAAO,EACrD;;;;IADqBH,EAAA,CAAAuB,SAAA,GAAyB;IAAzBvB,EAAA,CAAA6B,iBAAA,CAAAiB,OAAA,CAAAf,IAAA,WAAyB;;;;;IAfxD/B,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAgC,UAAA,IAAAe,2DAAA,kBAGC;IAGD/C,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAgC,UAAA,IAAAgB,2DAAA,kBAGC;IAILhD,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAbDH,EAAA,CAAAuB,SAAA,EAAqC;IAArCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAwC,kBAAA,CAAAb,MAAA,OAAqC;IAOnBpC,EAAA,CAAAuB,SAAA,GAAqB;IAArBvB,EAAA,CAAAwB,UAAA,YAAAf,MAAA,CAAAwC,kBAAA,CAAqB;;;;;;IAlE5CjD,EALF,CAAAC,cAAA,cAIC,cACgE;IAAtCD,EAAA,CAAAI,UAAA,mBAAA8C,oEAAA;MAAAlD,EAAA,CAAAO,aAAA,CAAA4C,GAAA;MAAA,MAAA1C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA2C,mBAAA,CAAoB,MAAM,CAAC;IAAA,EAAC;IAE1DpD,EADF,CAAAC,cAAA,cAA4B,cACD;IACvBD,EAAA,CAAAgC,UAAA,IAAAqB,yDAAA,kBAOC;IAgBHrD,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAC9BF,EAD8B,CAAAG,YAAA,EAAK,EAC7B;IAEJH,EADF,CAAAC,cAAA,cAA4B,eACG;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,kBAA+C;;IAC7CD,EAAA,CAAAC,cAAA,eAUC;IACCD,EAAA,CAAA6C,SAAA,gBAME;IAIV7C,EAHM,CAAAG,YAAA,EAAM,EACC,EACL,EACF;IACNH,EAAA,CAAAgC,UAAA,KAAAsB,qDAAA,kBAGC;IAgBHtD,EAAA,CAAAG,YAAA,EAAM;;;;IA3EJH,EAAA,CAAAuD,WAAA,cAAA9C,MAAA,CAAAwC,kBAAA,CAAAb,MAAA,KAAiD;IAWxCpC,EAAA,CAAAuB,SAAA,GAAmC;IAAnCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAkB,SAAA,qBAAmC;IA4BpC3B,EAAA,CAAAuB,SAAA,GAIC;IAJDvB,EAAA,CAAAwD,WAAA,cAAA/C,MAAA,CAAAgD,uBAAA,6CAIC;IAeNzD,EAAA,CAAAuB,SAAA,GAAqC;IAArCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAgD,uBAAA,SAAqC;;;;;IAqFtCzD,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAMFH,EAJF,CAAAC,cAAA,cAGC,eAC0B;IAAAD,EAAA,CAAAE,MAAA,GAEvB;IACJF,EADI,CAAAG,YAAA,EAAO,EACL;;;;IAHqBH,EAAA,CAAAuB,SAAA,GAEvB;IAFuBvB,EAAA,CAAA6B,iBAAA,CAAA6B,QAAA,CAAA3B,IAAA,gBAEvB;;;;;IAjBR/B,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAgC,UAAA,IAAA2B,2DAAA,kBAGC;IAGD3D,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAgC,UAAA,IAAA4B,2DAAA,kBAGC;IAML5D,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAfDH,EAAA,CAAAuB,SAAA,EAA0C;IAA1CvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAoD,uBAAA,CAAAzB,MAAA,OAA0C;IAOxBpC,EAAA,CAAAuB,SAAA,GAA0B;IAA1BvB,EAAA,CAAAwB,UAAA,YAAAf,MAAA,CAAAoD,uBAAA,CAA0B;;;;;;IApEjD7D,EALF,CAAAC,cAAA,cAIC,cAIE;IADCD,EAAA,CAAAI,UAAA,mBAAA0D,oEAAA;MAAA9D,EAAA,CAAAO,aAAA,CAAAwD,GAAA;MAAA,MAAAtD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA2C,mBAAA,CAAoB,WAAW,CAAC;IAAA,EAAC;IAGxCpD,EADF,CAAAC,cAAA,cAA4B,cACD;;IACvBD,EAAA,CAAAC,cAAA,cAMC;IAQCD,EAPA,CAAA6C,SAAA,eAME,eAOA;IAEN7C,EADE,CAAAG,YAAA,EAAM,EACF;;IACNH,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IACnCF,EADmC,CAAAG,YAAA,EAAK,EAClC;IAEJH,EADF,CAAAC,cAAA,cAA4B,gBACG;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,kBAA+C;;IAC7CD,EAAA,CAAAC,cAAA,eAUC;IACCD,EAAA,CAAA6C,SAAA,gBAME;IAIV7C,EAHM,CAAAG,YAAA,EAAM,EACC,EACL,EACF;IACNH,EAAA,CAAAgC,UAAA,KAAAgC,qDAAA,kBAGC;IAkBHhE,EAAA,CAAAG,YAAA,EAAM;;;;IA/EJH,EAAA,CAAAuD,WAAA,cAAA9C,MAAA,CAAAoD,uBAAA,CAAAzB,MAAA,KAAsD;IAyC9CpC,EAAA,CAAAuB,SAAA,IAIC;IAJDvB,EAAA,CAAAwD,WAAA,cAAA/C,MAAA,CAAAgD,uBAAA,kDAIC;IAeNzD,EAAA,CAAAuB,SAAA,GAA0C;IAA1CvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAgD,uBAAA,cAA0C;;;;;;IApiBjDzD,EAFJ,CAAAC,cAAA,cAAsE,cACtC,SACxB;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IACrBF,EADqB,CAAAG,YAAA,EAAK,EACpB;IAGNH,EAAA,CAAAC,cAAA,cAAwC;;IAEtCD,EAAA,CAAAC,cAAA,cAIC;IAgCCD,EA9BA,CAAA6C,SAAA,eAOE,eAUA,eAUA,eAUA;IACJ7C,EAAA,CAAAG,YAAA,EAAM;;IAIJH,EADF,CAAAC,cAAA,eAA8B,eACD;;IAGrBD,EAFJ,CAAAC,cAAA,eAAsD,YAC9C,0BAOH;IAECD,EADA,CAAA6C,SAAA,gBAAoD,gBACF;IAEtD7C,EADE,CAAAG,YAAA,EAAiB,EACZ;IAUPH,EATA,CAAA6C,SAAA,kBAQE,kBAeA;IACJ7C,EAAA,CAAAG,YAAA,EAAM;;IAEJH,EADF,CAAAC,cAAA,eAA8B,eACK;IAC/BD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAG1CF,EAH0C,CAAAG,YAAA,EAAM,EACtC,EACF,EACF;IAUFH,EAPJ,CAAAC,cAAA,eAAqB,eAMlB,eAIE;IADCD,EAAA,CAAAI,UAAA,mBAAA6D,8DAAA;MAAAjE,EAAA,CAAAO,aAAA,CAAA2D,GAAA;MAAA,MAAAzD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA2C,mBAAA,CAAoB,QAAQ,CAAC;IAAA,EAAC;IAGrCpD,EADF,CAAAC,cAAA,eAA4B,eACD;;IACvBD,EAAA,CAAAC,cAAA,eAMC;IAQCD,EAPA,CAAA6C,SAAA,gBAME,gBAOA;IAEN7C,EADE,CAAAG,YAAA,EAAM,EACF;;IACNH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IACtCF,EADsC,CAAAG,YAAA,EAAK,EACrC;IAEJH,EADF,CAAAC,cAAA,eAA4B,gBACG;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,kBAA+C;;IAC7CD,EAAA,CAAAC,cAAA,eAUC;IACCD,EAAA,CAAA6C,SAAA,gBAME;IAIV7C,EAHM,CAAAG,YAAA,EAAM,EACC,EACL,EACF;IACNH,EAAA,CAAAgC,UAAA,KAAAmC,8CAAA,kBAGC;IAkBHnE,EAAA,CAAAG,YAAA,EAAM;;IAOJH,EAJF,CAAAC,cAAA,eAGC,eAIE;IADCD,EAAA,CAAAI,UAAA,mBAAAgE,8DAAA;MAAApE,EAAA,CAAAO,aAAA,CAAA2D,GAAA;MAAA,MAAAzD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA2C,mBAAA,CAAoB,WAAW,CAAC;IAAA,EAAC;IAGxCpD,EADF,CAAAC,cAAA,eAA4B,eACD;;IACvBD,EAAA,CAAAC,cAAA,eAMC;IA2CCD,EA1CA,CAAA6C,SAAA,gBAME,gBAOA,gBAOA,gBAOA,gBAOA,gBAOA,gBAOA;IAEN7C,EADE,CAAAG,YAAA,EAAM,EACF;;IACNH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IACtCF,EADsC,CAAAG,YAAA,EAAK,EACrC;IAEJH,EADF,CAAAC,cAAA,eAA4B,gBACG;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,kBAA+C;;IAC7CD,EAAA,CAAAC,cAAA,eAUC;IACCD,EAAA,CAAA6C,SAAA,gBAME;IAIV7C,EAHM,CAAAG,YAAA,EAAM,EACC,EACL,EACF;IACNH,EAAA,CAAAgC,UAAA,KAAAqC,8CAAA,kBAGC;IAkBHrE,EAAA,CAAAG,YAAA,EAAM;;IASJH,EAJF,CAAAC,cAAA,eAGC,eACiE;IAAvCD,EAAA,CAAAI,UAAA,mBAAAkE,8DAAA;MAAAtE,EAAA,CAAAO,aAAA,CAAA2D,GAAA;MAAA,MAAAzD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA2C,mBAAA,CAAoB,OAAO,CAAC;IAAA,EAAC;IAE3DpD,EADF,CAAAC,cAAA,eAA4B,eACD;;IACvBD,EAAA,CAAAC,cAAA,eAMC;IAsBCD,EArBA,CAAA6C,SAAA,gBAME,gBAOA,gBAOA,gBAOA;IAEN7C,EADE,CAAAG,YAAA,EAAM,EACF;;IACNH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IACjCF,EADiC,CAAAG,YAAA,EAAK,EAChC;IAEJH,EADF,CAAAC,cAAA,eAA4B,gBACG;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,kBAA+C;;IAC7CD,EAAA,CAAAC,cAAA,eAUC;IACCD,EAAA,CAAA6C,SAAA,gBAME;IAIV7C,EAHM,CAAAG,YAAA,EAAM,EACC,EACL,EACF;IACNH,EAAA,CAAAgC,UAAA,KAAAuC,8CAAA,kBAGC;IAgBHvE,EAAA,CAAAG,YAAA,EAAM;IAoFNH,EAjFA,CAAAgC,UAAA,KAAAwC,8CAAA,mBAIC,KAAAC,8CAAA,mBAiFA;IAkFPzE,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IAreMH,EAAA,CAAAuB,SAAA,IAA8B;IAC9BvB,EADA,CAAAwD,WAAA,yBAA8B,kCAAA/C,MAAA,CAAAiE,6BAAA,OAG7B;IAMD1E,EAAA,CAAAuB,SAAA,GACF;IADEvB,EAAA,CAAA2E,kBAAA,MAAAlE,MAAA,CAAAiE,6BAAA,OACF;IAYF1E,EAAA,CAAAuB,SAAA,GAAmD;IAAnDvB,EAAA,CAAAuD,WAAA,cAAA9C,MAAA,CAAA0B,oBAAA,CAAAC,MAAA,KAAmD;IAyC3CpC,EAAA,CAAAuB,SAAA,IAIC;IAJDvB,EAAA,CAAAwD,WAAA,cAAA/C,MAAA,CAAAgD,uBAAA,+CAIC;IAeNzD,EAAA,CAAAuB,SAAA,GAAuC;IAAvCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAgD,uBAAA,WAAuC;IAwB1CzD,EAAA,CAAAuB,SAAA,EAAsD;IAAtDvB,EAAA,CAAAuD,WAAA,cAAA9C,MAAA,CAAA+B,uBAAA,CAAAJ,MAAA,KAAsD;IA4E9CpC,EAAA,CAAAuB,SAAA,IAIC;IAJDvB,EAAA,CAAAwD,WAAA,cAAA/C,MAAA,CAAAgD,uBAAA,kDAIC;IAeNzD,EAAA,CAAAuB,SAAA,GAA0C;IAA1CvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAgD,uBAAA,cAA0C;IA0B7CzD,EAAA,CAAAuB,SAAA,EAAkD;IAAlDvB,EAAA,CAAAuD,WAAA,cAAA9C,MAAA,CAAAmC,mBAAA,CAAAR,MAAA,KAAkD;IAoD1CpC,EAAA,CAAAuB,SAAA,IAIC;IAJDvB,EAAA,CAAAwD,WAAA,cAAA/C,MAAA,CAAAgD,uBAAA,8CAIC;IAeNzD,EAAA,CAAAuB,SAAA,GAAsC;IAAtCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAgD,uBAAA,UAAsC;IAqBxCzD,EAAA,CAAAuB,SAAA,EAAmC;IAAnCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAkB,SAAA,qBAAmC;IAiFnC3B,EAAA,CAAAuB,SAAA,EAAgC;IAAhCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAkB,SAAA,kBAAgC;;;;;IA8FnC3B,EADF,CAAAC,cAAA,cAAqE,QAChE;IAAAD,EAAA,CAAAE,MAAA,0EAAmE;IACxEF,EADwE,CAAAG,YAAA,EAAI,EACtE;;;;;IA8BEH,EAFJ,CAAAC,cAAA,eAA2H,eAC3F,SACxB;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAAE,MAAA,GACF;IAEJF,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IAHAH,EAAA,CAAAuB,SAAA,GACF;IADEvB,EAAA,CAAA2E,kBAAA,OAAAC,aAAA,CAAAC,QAAA,kBAAAD,aAAA,CAAAC,QAAA,CAAAA,QAAA,kBAAAD,aAAA,CAAAC,QAAA,CAAAA,QAAA,CAAAC,OAAA,kBAAAF,aAAA,CAAAC,QAAA,CAAAA,QAAA,CAAAC,OAAA,qBAAAF,aAAA,CAAAC,QAAA,CAAAA,QAAA,CAAAC,OAAA,IAAAC,IAAA,MAAAH,aAAA,CAAAC,QAAA,kBAAAD,aAAA,CAAAC,QAAA,CAAAG,aAAA,kBAAAJ,aAAA,CAAAC,QAAA,CAAAG,aAAA,CAAAC,MAAA,2CACF;;;;;IAWEjF,EADF,CAAAC,cAAA,eAA6D,aACnD;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,GAChC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAD0BH,EAAA,CAAAuB,SAAA,GAChC;IADgCvB,EAAA,CAAA2E,kBAAA,MAAAO,cAAA,CAAAC,WAAA,MAChC;;;;;IAEEnF,EADF,CAAAC,cAAA,eAA8D,aACpD;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,GACpC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAD8BH,EAAA,CAAAuB,SAAA,GACpC;IADoCvB,EAAA,CAAA2E,kBAAA,MAAAO,cAAA,CAAAE,eAAA,MACpC;;;;;IATFpF,EAAA,CAAAC,cAAA,eAGC;IAICD,EAHA,CAAAgC,UAAA,IAAAqD,sEAAA,mBAA6D,IAAAC,sEAAA,mBAGC;IAG9DtF,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAyB,SACnB;IAAAD,EAAA,CAAAE,MAAA,GAAsD;IAE9DF,EAF8D,CAAAG,YAAA,EAAK,EAC3D,EACF;;;;;IAZEH,EAAA,CAAAuB,SAAA,EAA4B;IAA5BvB,EAAA,CAAAwB,UAAA,SAAA0D,cAAA,CAAAC,WAAA,CAA4B;IAG5BnF,EAAA,CAAAuB,SAAA,EAAgC;IAAhCvB,EAAA,CAAAwB,UAAA,SAAA0D,cAAA,CAAAE,eAAA,CAAgC;IAIpCpF,EAAA,CAAAuB,SAAA,GACF;IADEvB,EAAA,CAAA2E,kBAAA,MAAAO,cAAA,CAAAK,GAAA,MACF;IAEMvF,EAAA,CAAAuB,SAAA,GAAsD;IAAtDvB,EAAA,CAAA2E,kBAAA,cAAAO,cAAA,CAAAM,OAAA,eAAAC,KAAA,UAAsD;;;;;IAfhEzF,EAAA,CAAAC,cAAA,eAA8H;IAC5HD,EAAA,CAAAgC,UAAA,IAAA0D,gEAAA,mBAGC;IAcH1F,EAAA,CAAAG,YAAA,EAAM;;;;IAhBqBH,EAAA,CAAAuB,SAAA,EAA2D;IAA3DvB,EAAA,CAAAwB,UAAA,YAAAoD,aAAA,CAAAC,QAAA,kBAAAD,aAAA,CAAAC,QAAA,CAAAG,aAAA,kBAAAJ,aAAA,CAAAC,QAAA,CAAAG,aAAA,CAAAW,KAAA,kBAAAf,aAAA,CAAAC,QAAA,CAAAG,aAAA,CAAAW,KAAA,CAAAC,YAAA,CAA2D;;;;;IAqBlF5F,EAFJ,CAAAC,cAAA,eAAmE,eACtC,aACjB;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,sDAC1B;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IApDJH,EARJ,CAAAC,cAAA,cAMC,cAC+B,aACJ;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACvDH,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAIFH,EAFJ,CAAAC,cAAA,cAA+B,cACH,aAChB;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IAC1B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAAiC;IAC/BD,EAAA,CAAAE,MAAA,IACF;;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAkCNH,EA/BA,CAAAgC,UAAA,KAAA6D,0DAAA,kBAA2H,KAAAC,0DAAA,kBAUG,KAAAC,0DAAA,kBAqB3D;IAKrE/F,EAAA,CAAAG,YAAA,EAAM;;;;;IAxDJH,EAFA,CAAAuD,WAAA,YAAAqB,aAAA,CAAAoB,MAAA,eAAgD,WAAApB,aAAA,CAAAoB,MAAA,cACF,YAAApB,aAAA,CAAAoB,MAAA,eACE;IAGtBhG,EAAA,CAAAuB,SAAA,GAAyB;IAAzBvB,EAAA,CAAA6B,iBAAA,CAAA+C,aAAA,CAAAhD,SAAA,CAAyB;IACnB5B,EAAA,CAAAuB,SAAA,EAA0B;IAA1BvB,EAAA,CAAAiG,UAAA,CAAArB,aAAA,CAAAoB,MAAA,CAA0B;IACtDhG,EAAA,CAAAuB,SAAA,EACF;IADEvB,EAAA,CAAA2E,kBAAA,MAAAC,aAAA,CAAAoB,MAAA,6BAAApB,aAAA,CAAAoB,MAAA,0CACF;IAK0BhG,EAAA,CAAAuB,SAAA,GAC1B;IAD0BvB,EAAA,CAAA2E,kBAAA,MAAAC,aAAA,CAAAsB,WAAA,MAC1B;IAGElG,EAAA,CAAAuB,SAAA,GACF;IADEvB,EAAA,CAAA2E,kBAAA,MAAA3E,EAAA,CAAAmG,WAAA,SAAAvB,aAAA,CAAAwB,SAAA,iBACF;IAIIpG,EAAA,CAAAuB,SAAA,GAAwF;IAAxFvB,EAAA,CAAAwB,UAAA,SAAAoD,aAAA,CAAAoB,MAAA,kBAAApB,aAAA,CAAAC,QAAA,IAAApE,MAAA,CAAAkB,SAAA,kBAAwF;IAUxF3B,EAAA,CAAAuB,SAAA,EAA2F;IAA3FvB,EAAA,CAAAwB,UAAA,SAAAoD,aAAA,CAAAoB,MAAA,kBAAApB,aAAA,CAAAC,QAAA,IAAApE,MAAA,CAAAkB,SAAA,qBAA2F;IAqB3F3B,EAAA,CAAAuB,SAAA,EAAmC;IAAnCvB,EAAA,CAAAwB,UAAA,SAAAoD,aAAA,CAAAoB,MAAA,cAAmC;;;;;IAzD7ChG,EAAA,CAAAC,cAAA,cAAwE;IACtED,EAAA,CAAAgC,UAAA,IAAAqE,mDAAA,oBAMC;IAwDHrG,EAAA,CAAAG,YAAA,EAAM;;;;IA7DoBH,EAAA,CAAAuB,SAAA,EAAqB;IAArBvB,EAAA,CAAAwB,UAAA,YAAAf,MAAA,CAAA6F,gBAAA,CAAqB;;;;;IAVjDtG,EADF,CAAAC,cAAA,cAAgE,SAC1D;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGrBH,EAAA,CAAAC,cAAA,cAAyC;IAKvCD,EAJA,CAAAgC,UAAA,IAAAuE,6CAAA,kBAAqE,IAAAC,6CAAA,kBAIG;IAiE5ExG,EADE,CAAAG,YAAA,EAAM,EACF;;;;IArEIH,EAAA,CAAAuB,SAAA,GAAmC;IAAnCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAA6F,gBAAA,CAAAlE,MAAA,OAAmC;IAInCpC,EAAA,CAAAuB,SAAA,EAAiC;IAAjCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAA6F,gBAAA,CAAAlE,MAAA,KAAiC;;;AD7oBnD;AAcA,WAAaqE,uBAAuB;EAA9B,MAAOA,uBAAuB;IAsHxBC,KAAA;IACAC,MAAA;IACAC,YAAA;IACAC,sBAAA;IACAC,YAAA;IACAC,aAAA;IACAC,WAAA;IACAC,oBAAA;IA5HVC,cAAc,GAAc,CAC1B;MAAEC,EAAE,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAgB,CAAE,EAC3C;MAAED,EAAE,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAc,CAAE,EAC7C;MAAED,EAAE,EAAE,cAAc;MAAEC,KAAK,EAAE,SAAS;MAAEC,QAAQ,EAAE;IAAI,CAAE,CACzD;IAED;IACAC,OAAO,GAAkB,IAAI;IAC7B3F,SAAS,GAAW,YAAY;IAChCC,SAAS,GAAW,OAAO;IAC3B2F,WAAW,GAAW,EAAE;IAGxBC,cAAc;IAEd;IACAC,YAAY,GAAkB,EAAE;IAChCC,gBAAgB,GAAW,CAAC;IAC5BC,gBAAgB;IAChBC,SAAS,GAAY,KAAK;IAC1B5B,MAAM,GAAoBjG,eAAe,CAAC8H,UAAU;IAEpD;IACApG,YAAY,GAAkB,EAAE;IAChCC,gBAAgB,GAAY,KAAK;IACjCoG,SAAS,GAAG,EAAE;IAEd;IACAC,YAAY,GAAiB,EAAE;IAC/BC,mBAAmB,GAAQ,IAAI,CAAC,CAAC;IAEjC;IACA1B,gBAAgB,GAOX,EAAE;IAEA2B,SAAS;IAEhB;IACAC,kBAAkB,GAAgB,IAAI;IACtCC,kBAAkB,GAAY,KAAK;IACnCC,WAAW;IAEXC,kBAAkB,GAAGvI,WAAW,CAACwI,kBAAkB,IAAI,KAAK;IAErDC,mBAAmB,GAAY,KAAK;IAC3CC,gBAAgB;IAERC,QAAQ,GAAG,IAAIpJ,OAAO,EAAQ;IACtCqJ,WAAW,GAAW,gBAAgB;IACtCC,QAAQ,GAAa,CACnB;MAAExB,EAAE,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAS,CAAE,EAChC;MAAED,EAAE,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAW,CAAE,EAClC;MAAED,EAAE,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAc,CAAE,CACtC;IAEDwB,QAAQ,GAAG,KAAK;IAChBC,UAAU;IACVC,WAAW,GAAU,EAAE;IACvBC,WAAW,GAAG,KAAK;IACnBC,WAAW,GAAY,KAAK;IAC5BC,aAAa,GAAW,EAAE;IAC1BC,UAAU,GAAU,EAAE;IACtBC,aAAa,GAAU,EAAE;IACzBC,QAAQ,GAAG,CAAC;IACZC,SAAS,GAAG,KAAK;IACjBC,WAAW,GAAW,EAAE;IAExBC,eAAe,GAAa,EAAE;IAC9BC,iBAAiB,GAAW,CAAC;IAC7BC,WAAW,GAAW,UAAU;IAEhC;IACAC,oBAAoB,GAAY,KAAK;IACrCC,cAAc,GAAW,WAAW;IAEpC;IACAC,mBAAmB,GAAQ,IAAI;IAC/BC,eAAe,GAAU,EAAE;IAC3BC,WAAW,GAAU,EAAE;IACvBC,WAAW,GAAU,EAAE;IACvBC,cAAc,GAAW,EAAE;IAC3BC,iBAAiB,GAAW,EAAE;IAC9BC,yBAAyB,GAAW,EAAE;IACtCC,sBAAsB,GAAU,EAAE;IAClCC,eAAe,GAAa,EAAE;IAC9BC,wBAAwB,GAAG,KAAK;IAChCC,sBAAsB,GAAG,IAAIjL,OAAO,EAAW;IAC/CkL,gBAAgB,GAAU,EAAE;IAC5BC,SAAS,GAAW,EAAE;IACtBC,aAAa,GAAqB,EAAE;IAEpC;IACA/F,6BAA6B,GAAW,CAAC;IACzCvC,oBAAoB,GAAU,EAAE;IAChCS,mBAAmB,GAAU,EAAE;IAC/BJ,uBAAuB,GAAU,EAAE;IACnCqB,uBAAuB,GAAU,EAAE;IACnCZ,kBAAkB,GAAU,EAAE;IAE9B;IACQyH,sBAAsB,GAA+B;MAC3DC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE,IAAI;MACXC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE,IAAI;MACfC,IAAI,EAAE;KACP;IAED;IAEAC,YACUtE,KAAqB,EACrBC,MAAc,EACdC,YAAiC,EACjCC,sBAA8C,EAC9CC,YAAiC,EACjCC,aAA4B,EAC5BC,WAAwB,EACxBC,oBAA0C;MAP1C,KAAAP,KAAK,GAALA,KAAK;MACL,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,YAAY,GAAZA,YAAY;MACZ,KAAAC,sBAAsB,GAAtBA,sBAAsB;MACtB,KAAAC,YAAY,GAAZA,YAAY;MACZ,KAAAC,aAAa,GAAbA,aAAa;MACb,KAAAC,WAAW,GAAXA,WAAW;MACX,KAAAC,oBAAoB,GAApBA,oBAAoB;MAE5B,IAAI,CAACgB,SAAS,GAAG,IAAI,CAACjB,WAAW,CAACiE,KAAK,CAAC;QACtCC,gBAAgB,EAAE,CAAC,IAAI,CAAC;QACxBC,aAAa,EAAE,CAAC,KAAK;OACtB,CAAC;IACJ;IAEAC,QAAQA,CAAA;MACNC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MAClD,IAAI,CAAClD,WAAW,GAAGmD,MAAM,CAACC,UAAU,EAAE;MAEtC,IAAI,CAAC9E,KAAK,CAAC+E,MAAM,CAACC,SAAS,CAAED,MAAM,IAAI;QACrC,IAAI,CAAC9J,SAAS,GAAG8J,MAAM,CAAC,MAAM,CAAC,IAAI,YAAY;QAC/CJ,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAAC3J,SAAS,CAAC;MAC9D,CAAC,CAAC;MAEF,IAAI,CAAC+E,KAAK,CAACiF,WAAW,CAACD,SAAS,CAAED,MAAM,IAAI;QAC1C,IAAIA,MAAM,CAAC,IAAI,CAAC,EAAE;UAChB,IAAI,CAACnE,OAAO,GAAGmE,MAAM,CAAC,IAAI,CAAC;UAC3B,IAAI,CAACG,aAAa,CAACH,MAAM,CAAC,IAAI,CAAC,CAAC;QAClC;MACF,CAAC,CAAC;MAEF;MACA,IAAI,CAAChK,YAAY,GAAG,CAClB;QACEoK,IAAI,EAAE,IAAI;QACV9G,IAAI,EAAE,kBAAkB,IAAI,CAACnD,SAAS,IAAI,YAAY;OACvD,CACF;IACH;IAEAkK,WAAWA,CAAA;MACT,IAAI,CAACrD,QAAQ,CAACsD,IAAI,EAAE;MACpB,IAAI,CAACtD,QAAQ,CAACuD,QAAQ,EAAE;MACxB,IAAI,IAAI,CAACxD,gBAAgB,EAAE;QACzByD,aAAa,CAAC,IAAI,CAACzD,gBAAgB,CAAC;MACtC;IACF;IAEA0D,WAAWA,CAACC,KAAoC;MAC9C,IAAI,CAAC1C,WAAW,GAAG0C,KAAK,CAAChF,EAAE;MAC3B,IAAI,CAACuB,WAAW,GAAGyD,KAAK,CAAC/E,KAAK;IAChC;IAEAwE,aAAaA,CAACtE,OAAe;MAC3B,IAAI,CAAC+B,SAAS,GAAG,IAAI;MAErB;MACA,IAAI,IAAI,CAAC1H,SAAS,KAAK,eAAe,EAAE;QACtC,IAAI,CAACiF,YAAY,CAACwF,gCAAgC,CAAC9E,OAAO,CAAC,CAACoE,SAAS,CAAC;UACpEK,IAAI,EAAGlH,QAAa,IAAI;YACtB,IAAI,CAACwH,uBAAuB,CAACxH,QAAQ,CAAC;UACxC,CAAC;UACDyH,KAAK,EAAGA,KAAU,IAAI;YACpBjB,OAAO,CAACiB,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;YAC1D,IAAI,CAACjD,SAAS,GAAG,KAAK;UACxB;SACD,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAACzC,YAAY,CAAC2F,YAAY,CAACjF,OAAO,CAAC,CAACoE,SAAS,CAAC;UAChDK,IAAI,EAAGlH,QAAa,IAAI;YACtB,IAAI,CAACwH,uBAAuB,CAACxH,QAAQ,CAAC;UACxC,CAAC;UACDyH,KAAK,EAAGA,KAAU,IAAI;YACpBjB,OAAO,CAACiB,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;YACvD,IAAI,CAACjD,SAAS,GAAG,KAAK;UACxB;SACD,CAAC;MACJ;IACF;IAEQgD,uBAAuBA,CAACxH,QAAa;MAC3C,IAAI,CAACwE,SAAS,GAAG,KAAK;MAEtB;MACA,IAAImD,SAAS;MACb,IACE3H,QAAQ,CAAC4H,YAAY,IACrBC,KAAK,CAACC,OAAO,CAAC9H,QAAQ,CAAC4H,YAAY,CAAC,IACpC5H,QAAQ,CAAC4H,YAAY,CAACrK,MAAM,GAAG,CAAC,EAChC;QACAoK,SAAS,GAAG3H,QAAQ,CAAC4H,YAAY,CAAC,CAAC,CAAC;MACtC,CAAC,MAAM,IAAI5H,QAAQ,CAAC0C,WAAW,EAAE;QAC/BiF,SAAS,GAAG3H,QAAQ,CAAC0C,WAAW;MAClC,CAAC,MAAM,IAAI1C,QAAQ,CAAC+H,IAAI,EAAE;QACxBJ,SAAS,GAAG3H,QAAQ,CAAC+H,IAAI;MAC3B,CAAC,MAAM;QACLJ,SAAS,GAAG3H,QAAQ;MACtB;MAEA,IAAI2H,SAAS,EAAE;QACb,IAAI,CAAC5C,mBAAmB,GAAG4C,SAAS;QACpC,IAAI,CAAC5K,SAAS,GAAG4K,SAAS,CAACzK,IAAI,IAAIyK,SAAS,CAAC5K,SAAS,IAAI,OAAO;QACjE,IAAI,CAAC2F,WAAW,GAAGiF,SAAS,CAACrH,WAAW,IAAIqH,SAAS,CAACjF,WAAW,IAAI,EAAE;QAEvE;QACA,IAAI,IAAI,CAAC5F,SAAS,KAAK,YAAY,EAAE;UACnC;UACA,IAAI,CAACqI,cAAc,GACjBwC,SAAS,CAACK,WAAW,IAAIL,SAAS,CAACzK,IAAI,IAAI,cAAc;UAE3D;UACA,IAAI,CAACkI,iBAAiB,GACpBuC,SAAS,CAACM,WAAW,IACrBN,SAAS,CAACK,WAAW,IACrBL,SAAS,CAACzK,IAAI,IACd,EAAE;UAEJ;UACA,IAAIyK,SAAS,CAACO,gBAAgB,EAAE;YAC9B,IAAI,CAAC7C,yBAAyB,GAAGsC,SAAS,CAACO,gBAAgB;UAC7D,CAAC,MAAM,IAAIP,SAAS,CAACM,WAAW,EAAE;YAChC,IAAI,CAAC5C,yBAAyB,GAAGsC,SAAS,CAACM,WAAW;UACxD,CAAC,MAAM,IAAIN,SAAS,CAACK,WAAW,EAAE;YAChC,IAAI,CAAC3C,yBAAyB,GAAGsC,SAAS,CAACK,WAAW;UACxD;QACF;QAEA;QACA,IAAI,IAAI,CAACpL,YAAY,CAACW,MAAM,GAAG,CAAC,EAAE;UAChC,IAAI,CAACX,YAAY,CAAC,CAAC,CAAC,CAACsD,IAAI,GAAG,kBAAkB,IAAI,CAACnD,SAAS,6BAA6B;QAC3F;QAEA;QACA,IAAI,CAACoL,cAAc,CAACR,SAAS,CAAC;MAChC;IACF;IAEQQ,cAAcA,CAACR,SAAc;MACnC;MACA,IAAI,CAACS,gCAAgC,CAACT,SAAS,CAAC;IAClD;IAEA1L,iBAAiBA,CAACoM,OAAe;MAC/B,IAAI,IAAI,CAACvL,SAAS,KAAK,YAAY,EAAE;QACnC;QACA,IACE,CAAC,IAAI,CAACiI,mBAAmB,KACxB,CAAC,IAAI,CAACI,cAAc,IAAI,IAAI,CAACA,cAAc,KAAK,SAAS,CAAC,EAC3D;UACA,IAAI,CAACmD,cAAc,CACjB,+DAA+D,CAChE;UACD;QACF;QAEA,IAAIC,cAAc,GAAGF,OAAO;QAC5B,IAAI,IAAI,CAAC/C,sBAAsB,CAAC/H,MAAM,GAAG,CAAC,EAAE;UAC1C,MAAMiL,SAAS,GAAG,IAAI,CAAClD,sBAAsB,CAC1CmD,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACC,YAAY,CAAC,CAChCC,IAAI,CAAC,IAAI,CAAC;UACbL,cAAc,GAAG,GAAGF,OAAO,0BAA0BG,SAAS,EAAE;QAClE;QAEA;QACA,MAAMjF,WAAW,GAAGmD,MAAM,CAACC,UAAU,EAAE;QACvC,IAAI,CAAClF,gBAAgB,CAACoH,IAAI,CAAC;UACzBvG,EAAE,EAAEiB,WAAW;UACfxG,SAAS,EAAE,IAAI,CAACA,SAAS;UACzBsE,WAAW,EAAEgH,OAAO;UACpBlH,MAAM,EAAE,SAAS;UAAE;UACnBI,SAAS,EAAE,IAAIuH,IAAI;SACpB,CAAC;QACFtC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,IAAI,CAAChF,gBAAgB,CAAC;QAErE,IAAI,CAAC7E,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;UAAEoK,IAAI,EAAE,MAAM;UAAE9G,IAAI,EAAEqI;QAAc,CAAE,EACtC;UAAEvB,IAAI,EAAE,IAAI;UAAE9G,IAAI,EAAE,GAAG,IAAI,CAACnD,SAAS;QAAoB,CAAE,CAC5D;QACD,IAAI,CAACF,gBAAgB,GAAG,IAAI;QAE5B,MAAMwJ,gBAAgB,GACpB,IAAI,CAACjD,SAAS,CAAC2F,GAAG,CAAC,kBAAkB,CAAC,EAAEC,KAAK,IAAI,KAAK;QACxD,MAAM1C,aAAa,GAAG,IAAI,CAAClD,SAAS,CAAC2F,GAAG,CAAC,eAAe,CAAC,EAAEC,KAAK,IAAI,KAAK;QAEzExC,OAAO,CAACC,GAAG,CACT,2CAA2C,EAC3CJ,gBAAgB,EAChB,gBAAgB,EAChBC,aAAa,CACd;QAED;QACA;QACA,MAAM2C,SAAS,GACb,IAAI,CAACtD,SAAS,IACd,IAAI,CAACP,iBAAiB,IACtB,IAAI,CAACL,mBAAmB,EAAEkD,WAAW,IACrC,IAAI,CAAClD,mBAAmB,EAAEiD,WAAW,IACrC,IAAI,CAACjD,mBAAmB,EAAE7H,IAAI,IAC9B,IAAI,CAACiI,cAAc;QAErB,IAAI+D,iBAAiB,GAAG,IAAI,CAAC7D,yBAAyB;QACtD,IAAI,CAAC6D,iBAAiB,EAAE;UACtB;UACA,IAAI,IAAI,CAACnE,mBAAmB,EAAEmD,gBAAgB,EAAE;YAC9CgB,iBAAiB,GAAG,IAAI,CAACnE,mBAAmB,CAACmD,gBAAgB;UAC/D,CAAC,MAAM;YACL,MAAMiB,OAAO,GAAG,IAAI,CAACC,qBAAqB,EAAE;YAC5C,MAAMC,eAAe,GACnB,IAAI,CAACtE,mBAAmB,EAAEkD,WAAW,IACrC,IAAI,CAAClD,mBAAmB,EAAEiD,WAAW,IACrC,IAAI,CAACjD,mBAAmB,EAAE7H,IAAI,IAC9B+L,SAAS;YACXC,iBAAiB,GAAG,GAAGG,eAAe,GAAGF,OAAO,EAAE;UACpD;QACF;QAEA,IAAI,IAAI,CAAC7D,sBAAsB,CAAC/H,MAAM,GAAG,CAAC,EAAE;UAC1C,IAAI,CAAC+L,+BAA+B,CAClCjB,OAAO,EACPY,SAAS,EACTC,iBAAiB,EACjB7C,gBAAgB,EAChBC,aAAa,CACd;UACD;QACF;QAEA,IAAI,CAACiD,qBAAqB,CACxBlB,OAAO,EACPY,SAAS,EACTC,iBAAiB,EACjB7C,gBAAgB,EAChBC,aAAa,CACd;MACH,CAAC,MAAM,IAAI,IAAI,CAACxJ,SAAS,KAAK,eAAe,EAAE;QAC7C;QACA,MAAMyG,WAAW,GAAGmD,MAAM,CAACC,UAAU,EAAE;QACvC,IAAI,CAAClF,gBAAgB,CAACoH,IAAI,CAAC;UACzBvG,EAAE,EAAEiB,WAAW;UACfxG,SAAS,EAAE,IAAI,CAACA,SAAS;UACzBsE,WAAW,EAAEgH,OAAO;UACpBlH,MAAM,EAAE,SAAS;UAAE;UACnBI,SAAS,EAAE,IAAIuH,IAAI;SACpB,CAAC;QACFtC,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAE,IAAI,CAAChF,gBAAgB,CAAC;QAEnF,IAAI,CAAC5E,gBAAgB,GAAG,IAAI;QAC5B,IAAI2M,OAAO,GAAG;UACZjG,WAAW,EAAE,IAAI,CAACA,WAAW;UAC7Bd,OAAO,EAAEgH,MAAM,CAAC,IAAI,CAAChH,OAAO,CAAC;UAC7BiH,IAAI,EAAE,IAAI,CAACzH,YAAY,CAAC0H,aAAa,EAAE,IAAI,uBAAuB;UAClEC,UAAU,EAAE;YAAEC,QAAQ,EAAExB;UAAO;SAChC;QAED;QACA,IAAI,CAACzL,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;UAAEoK,IAAI,EAAE,MAAM;UAAE9G,IAAI,EAAEmI;QAAO,CAAE,EAC/B;UAAErB,IAAI,EAAE,IAAI;UAAE9G,IAAI,EAAE,GAAG,IAAI,CAACnD,SAAS;QAAoB,CAAE,CAC5D;QAED,IAAI,IAAI,CAACuI,sBAAsB,CAAC/H,MAAM,GAAG,CAAC,EAAE;UAC1C,MAAMuM,WAAW,GAAG,IAAI,CAACxE,sBAAsB,CAAC,CAAC,CAAC;UAClD,IAAIiD,cAAsB;UAC1B,IAAI,IAAI,CAACjD,sBAAsB,CAAC/H,MAAM,GAAG,CAAC,EAAE;YAC1C,MAAMiL,SAAS,GAAG,IAAI,CAAClD,sBAAsB,CAC1CmD,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACC,YAAY,CAAC,CAChCC,IAAI,CAAC,IAAI,CAAC;YACbL,cAAc,GAAG,sBAAsBC,SAAS,EAAE;YAElD,IAAI,CAAC5L,YAAY,GAAG,CAAC;cAAEoK,IAAI,EAAE,MAAM;cAAE9G,IAAI,EAAEqI;YAAc,CAAE,CAAC;UAC9D;UACA,IAAI,CAACvG,sBAAsB,CACxB+H,0BAA0B,CAACP,OAAO,EAAEM,WAAW,CAAC,CAChDE,IAAI,CACHrP,QAAQ,CAAC,MAAK;YACZ,IAAI,CAACkC,gBAAgB,GAAG,KAAK;YAC7B,IAAI,CAAC2I,wBAAwB,GAAG,KAAK;UACvC,CAAC,CAAC,EACF/K,SAAS,CAAC,IAAI,CAACgL,sBAAsB,CAAC,CACvC,CACAoB,SAAS,CAAC;YACTK,IAAI,EAAG+C,GAAG,IAAK,IAAI,CAACC,0BAA0B,CAACD,GAAG,EAAE5B,OAAO,CAAC;YAC5DZ,KAAK,EAAG0C,GAAQ,IAAI;cAClB;cACA,IAAI,IAAI,CAAC1I,gBAAgB,CAAClE,MAAM,GAAG,CAAC,EAAE;gBACpC,IAAI,CAACkE,gBAAgB,CAAC,IAAI,CAACA,gBAAgB,CAAClE,MAAM,GAAG,CAAC,CAAC,CAAC4D,MAAM,GAAG,QAAQ;cAC3E;cAEA;cACA,IAAI,IAAI,CAACvE,YAAY,CAACW,MAAM,GAAG,CAAC,EAAE;gBAChC,MAAM6M,WAAW,GAAG,IAAI,CAACxN,YAAY,CAAC,IAAI,CAACA,YAAY,CAACW,MAAM,GAAG,CAAC,CAAC;gBACnE,IAAI6M,WAAW,CAACpD,IAAI,KAAK,IAAI,IAAIoD,WAAW,CAAClK,IAAI,CAACmK,QAAQ,CAAC,iBAAiB,CAAC,EAAE;kBAC7ED,WAAW,CAAClK,IAAI,GAAG,GAAG,IAAI,CAACnD,SAAS,mBAAmB;gBACzD;cACF;cAEA;cACA,IAAI,CAACuN,iBAAiB,CAAC,QAAQ,CAAC;YAClC;WACD,CAAC;QACN,CAAC,MAAM;UACL,IAAI,CAACtI,sBAAsB,CACxBuI,kBAAkB,CAACf,OAAO,CAAC,CAC3BQ,IAAI,CACHrP,QAAQ,CAAC,MAAK;YACZ,IAAI,CAACkC,gBAAgB,GAAG,KAAK;YAC7B,IAAI,CAAC2I,wBAAwB,GAAG,KAAK;UACvC,CAAC,CAAC,EACF/K,SAAS,CAAC,IAAI,CAACgL,sBAAsB,CAAC,CACvC,CACAoB,SAAS,CAAC;YACTK,IAAI,EAAG+C,GAAG,IAAK,IAAI,CAACC,0BAA0B,CAACD,GAAG,EAAE5B,OAAO,CAAC;YAC5DZ,KAAK,EAAG0C,GAAQ,IAAI;cAClB;cACA,IAAI,IAAI,CAAC1I,gBAAgB,CAAClE,MAAM,GAAG,CAAC,EAAE;gBACpC,IAAI,CAACkE,gBAAgB,CAAC,IAAI,CAACA,gBAAgB,CAAClE,MAAM,GAAG,CAAC,CAAC,CAAC4D,MAAM,GAAG,QAAQ;cAC3E;cAEA;cACA,IAAI,IAAI,CAACvE,YAAY,CAACW,MAAM,GAAG,CAAC,EAAE;gBAChC,MAAM6M,WAAW,GAAG,IAAI,CAACxN,YAAY,CAAC,IAAI,CAACA,YAAY,CAACW,MAAM,GAAG,CAAC,CAAC;gBACnE,IAAI6M,WAAW,CAACpD,IAAI,KAAK,IAAI,IAAIoD,WAAW,CAAClK,IAAI,CAACmK,QAAQ,CAAC,iBAAiB,CAAC,EAAE;kBAC7ED,WAAW,CAAClK,IAAI,GAAG,GAAG,IAAI,CAACnD,SAAS,mBAAmB;gBACzD;cACF;cAEA;cACA,IAAI,CAACuN,iBAAiB,CAAC,QAAQ,CAAC;YAClC;WACD,CAAC;QACN;MACF;IACF;IAEAvO,eAAeA,CAAC+J,MAAsB;MACpC,IAAI,CAAC7C,SAAS,GAAG6C,MAAM,CAAC5I,IAAI,IAAIsN,MAAM,CAAC1E,MAAM,CAACkD,KAAK,CAAC,IAAI,EAAE;IAC5D;IAEA7M,gCAAgCA,CAAC6M,KAAc;MAC7C;MACA,IAAI,CAAC5F,SAAS,CAAC2F,GAAG,CAAC,kBAAkB,CAAC,EAAE0B,QAAQ,CAACzB,KAAK,CAAC;MAEvD;MACA;MACA,IAAI,CAACA,KAAK,EAAE;QACV,IAAI,CAACtD,gBAAgB,GAAG,EAAE;QAC1Bc,OAAO,CAACC,GAAG,CACT,6DAA6D,CAC9D;MACH,CAAC,MAAM;QACLD,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;MACzE;IACF;IAEApK,0BAA0BA,CAAC2M,KAAc;MACvC;MACA,IAAI,CAAC5F,SAAS,CAAC2F,GAAG,CAAC,eAAe,CAAC,EAAE0B,QAAQ,CAACzB,KAAK,CAAC;MACpDxC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEuC,KAAK,CAAC;IAC9C;IAEAzM,eAAeA,CAACmO,KAAY;MAC1B,IAAI,CAACtG,aAAa,GAAGsG,KAAK;MAC1B;MACA,IAAI,CAACpF,sBAAsB,GAAGoF,KAAK;IACrC;IAEAjO,mBAAmBA,CAAA;MACjB;IAAA;IAGFkO,QAAQA,CAAA;MACN;IAAA;IAGFC,aAAaA,CAACC,OAA8B;MAC1C;IAAA;IAGFC,mBAAmBA,CAACC,MAAiC;MACnD;IAAA;IAGFC,YAAYA,CAAA;MACV,IAAI,CAAClJ,MAAM,CAACmJ,QAAQ,CAAC,CAAC,eAAe,EAAE,IAAI,CAACnO,SAAS,CAAC,EAAE;QACtDgK,WAAW,EAAE;UAAExE,EAAE,EAAE,IAAI,CAACG,OAAO;UAAEyI,IAAI,EAAE;QAAM;OAC9C,CAAC;IACJ;IAEAC,SAASA,CAAA;MACP,IAAI,CAACrJ,MAAM,CAACmJ,QAAQ,CAAC,CAAC,eAAe,EAAE,IAAI,CAACnO,SAAS,CAAC,EAAE;QACtDgK,WAAW,EAAE;UAAExE,EAAE,EAAE,IAAI,CAACG,OAAO;UAAEyI,IAAI,EAAE;QAAM;OAC9C,CAAC;IACJ;IAEAE,oBAAoBA,CAAA;MAClB,IAAI,CAACtJ,MAAM,CAACmJ,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;IACzC;IAEAI,eAAeA,CAAA;MACb,IAAI,CAACxG,oBAAoB,GAAG,CAAC,IAAI,CAACA,oBAAoB;IACxD;IAEAyF,iBAAiBA,CAACgB,GAAW;MAC3B,IAAI,CAACxG,cAAc,GAAGwG,GAAG;IAC3B;IAEA;IACA/M,mBAAmBA,CAACgN,QAAgB;MAClC,IAAI,CAAC1F,sBAAsB,CAAC0F,QAAQ,CAAC,GACnC,CAAC,IAAI,CAAC1F,sBAAsB,CAAC0F,QAAQ,CAAC;IAC1C;IAEA3M,uBAAuBA,CAAC2M,QAAgB;MACtC,OAAO,IAAI,CAAC1F,sBAAsB,CAAC0F,QAAQ,CAAC,IAAI,KAAK;IACvD;IAEA;IACQjD,cAAcA,CAACD,OAAe;MACpC,IAAI,CAACzL,YAAY,GAAG,CAAC,GAAG,IAAI,CAACA,YAAY,EAAE;QAAEoK,IAAI,EAAE,IAAI;QAAE9G,IAAI,EAAEmI;MAAO,CAAE,CAAC;IAC3E;IAEQe,qBAAqBA,CAAA;MAC3B;MACA,OAAO,EAAE;IACX;IAEQoC,qBAAqBA,CAAA;MAC3B;MACA,OAAO,EAAE;IACX;IAEAtB,0BAA0BA,CAAClK,QAAa,EAAEqI,OAAe;MACvD,IAAI;QACF;QACA,IAAI,CAAClF,mBAAmB,GAAGnD,QAAQ;QAEnC;QACA,IAAI,IAAI,CAACyB,gBAAgB,CAAClE,MAAM,GAAG,CAAC,EAAE;UACpC,IAAI,CAACkE,gBAAgB,CAAC,IAAI,CAACA,gBAAgB,CAAClE,MAAM,GAAG,CAAC,CAAC,CAAC4D,MAAM,GAAG,SAAS;UAC1E,IAAI,CAACM,gBAAgB,CAAC,IAAI,CAACA,gBAAgB,CAAClE,MAAM,GAAG,CAAC,CAAC,CAACyC,QAAQ,GAAGA,QAAQ;UAC3EwG,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAAChF,gBAAgB,CAAC;QAClE;QAEA,MAAMgK,SAAS,GAAGzL,QAAQ,EAAEG,aAAa,EAAEW,KAAK,EAAE4K,MAAM;QACxD,IAAIC,eAAe,GAAG,EAAE;QAExB,IAAIF,SAAS,EAAE;UACb;UACAE,eAAe,GAAGF,SAAS,CAACG,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC;QACnD,CAAC,MAAM;UACLD,eAAe,GAAG3L,QAAQ,EAAEG,aAAa,EAAEC,MAAM;QACnD;QAEA;QACA,IAAI,CAACxD,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;UAAEoK,IAAI,EAAE,MAAM;UAAE9G,IAAI,EAAEmI;QAAO,CAAE,EAC/B;UAAErB,IAAI,EAAE,IAAI;UAAE9G,IAAI,EAAE,GAAG,IAAI,CAACnD,SAAS;QAAoB,CAAE,CAC5D;QAED;QACA,IAAI,IAAI,CAACH,YAAY,CAACW,MAAM,GAAG,CAAC,EAAE;UAChC,KAAK,IAAIsO,CAAC,GAAG,IAAI,CAACjP,YAAY,CAACW,MAAM,GAAG,CAAC,EAAEsO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;YACtD,IAAI,IAAI,CAACjP,YAAY,CAACiP,CAAC,CAAC,CAAC7E,IAAI,KAAK,IAAI,IAAI,IAAI,CAACpK,YAAY,CAACiP,CAAC,CAAC,CAAC3L,IAAI,CAACmK,QAAQ,CAAC,iBAAiB,CAAC,EAAE;cAC/F,IAAI,CAACzN,YAAY,CAACiP,CAAC,CAAC,CAAC3L,IAAI,GAAG,GAAG,IAAI,CAACnD,SAAS,oBAAoB;cACjE;YACF;UACF;QACF;QAEA;QACA,IAAI,CAACuN,iBAAiB,CAAC,QAAQ,CAAC;MAClC,CAAC,CAAC,OAAOH,GAAQ,EAAE;QACjB;QACA,IAAI,IAAI,CAAC1I,gBAAgB,CAAClE,MAAM,GAAG,CAAC,EAAE;UACpC,IAAI,CAACkE,gBAAgB,CAAC,IAAI,CAACA,gBAAgB,CAAClE,MAAM,GAAG,CAAC,CAAC,CAAC4D,MAAM,GAAG,QAAQ;UACzEqF,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE,IAAI,CAAChF,gBAAgB,CAAC;QAC3E;QAEA,IAAI,CAAC7E,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;UAAEoK,IAAI,EAAE,MAAM;UAAE9G,IAAI,EAAEmI;QAAO,CAAE,EAC/B;UAAErB,IAAI,EAAE,IAAI;UAAE9G,IAAI,EAAE,GAAG,IAAI,CAACnD,SAAS;QAAmB,CAAE,CAC3D;QAED;QACA,IAAI,IAAI,CAACH,YAAY,CAACW,MAAM,GAAG,CAAC,EAAE;UAChC,KAAK,IAAIsO,CAAC,GAAG,IAAI,CAACjP,YAAY,CAACW,MAAM,GAAG,CAAC,EAAEsO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;YACtD,IAAI,IAAI,CAACjP,YAAY,CAACiP,CAAC,CAAC,CAAC7E,IAAI,KAAK,IAAI,IAAI,IAAI,CAACpK,YAAY,CAACiP,CAAC,CAAC,CAAC3L,IAAI,CAACmK,QAAQ,CAAC,iBAAiB,CAAC,EAAE;cAC/F,IAAI,CAACzN,YAAY,CAACiP,CAAC,CAAC,CAAC3L,IAAI,GAAG,GAAG,IAAI,CAACnD,SAAS,mBAAmB;cAChE;YACF;UACF;QACF;QAEA;QACA,IAAI,CAACuN,iBAAiB,CAAC,QAAQ,CAAC;MAClC;IACF;IAEQhB,+BAA+BA,CACrCjB,OAAe,EACf6C,IAAY,EACZhC,iBAAyB,EACzB7C,gBAAyB,EACzBC,aAAsB;MAEtB,MAAMwF,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/B,IAAI,CAACzG,sBAAsB,CAAC0G,OAAO,CAAEC,QAAQ,IAAI;QAC/C,IAAIA,QAAQ,CAACvD,IAAI,EAAE;UACjBoD,QAAQ,CAACI,MAAM,CAAC,OAAO,EAAED,QAAQ,CAACvD,IAAI,CAAC;QACzC;MACF,CAAC,CAAC;MAEF,IAAIoD,QAAQ,CAACK,GAAG,CAAC,OAAO,CAAC,EAAE;QACzB,IAAI,CAACnK,sBAAsB,CACxBoK,gBAAgB,CAACN,QAAQ,CAAC,CAC1B9B,IAAI,CACHtP,SAAS,CAAE2R,YAAY,IAAI;UACzB,MAAMC,WAAW,GACfD,YAAY,EAAEE,aAAa,EACvB9D,GAAG,CAAEzI,QAAa,IAAKA,QAAQ,CAACsM,WAAW,CAAC,EAC5C1D,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;UACtB,IAAI,CAAC4D,8BAA8B,CACjCnE,OAAO,EACP6C,IAAI,EACJhC,iBAAiB,EACjB7C,gBAAgB,EAChBC,aAAa,EACbgG,WAAW,CACZ;UACD,OAAOzR,EAAE,CAAC,IAAI,CAAC;QACjB,CAAC,CAAC,EACFD,UAAU,CAAE6M,KAAK,IAAI;UACnBjB,OAAO,CAACiB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,IAAI,CAAC8B,qBAAqB,CACxBlB,OAAO,EACP6C,IAAI,EACJhC,iBAAiB,EACjB7C,gBAAgB,EAChBC,aAAa,CACd;UACD,OAAOzL,EAAE,CAAC,IAAI,CAAC;QACjB,CAAC,CAAC,CACH,CACAgM,SAAS,EAAE;MAChB,CAAC,MAAM;QACL,IAAI,CAAC0C,qBAAqB,CACxBlB,OAAO,EACP6C,IAAI,EACJhC,iBAAiB,EACjB7C,gBAAgB,EAChBC,aAAa,CACd;MACH;IACF;IAEQiD,qBAAqBA,CAC3BlB,OAAe,EACf6C,IAAY,EACZhC,iBAAyB,EACzB7C,gBAAyB,EACzBC,aAAsB;MAEtBE,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;QAClC4B,OAAO;QACP6C,IAAI;QACJhC,iBAAiB;QACjB7C,gBAAgB;QAChBC,aAAa;QACbmG,wBAAwB,EAAE,IAAI,CAAC/G,gBAAgB,CAACnI;OACjD,CAAC;MAEF,IAAI8I,gBAAgB,EAAE;QACpB,IAAI,CAACX,gBAAgB,CAACmD,IAAI,CAAC;UAAE6D,OAAO,EAAErE,OAAO;UAAEsE,IAAI,EAAE;QAAM,CAAE,CAAC;MAChE;MAEA,MAAMnD,OAAO,GAAGnD,gBAAgB,GAAG,IAAI,CAACX,gBAAgB,GAAG2C,OAAO;MAClE,MAAM;QAAEuE;MAAO,CAAE,GAAG,IAAI,CAACpB,qBAAqB,EAAE;MAEhDhF,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE+C,OAAO,CAAC;MAEjD,IAAI,CAACxH,sBAAsB,CACxB6K,cAAc,CACbrD,OAAO,EACP0B,IAAI,EACJ7E,gBAAgB,EAChBC,aAAa,EACb,IAAI,CAACf,eAAe,EACpB2D,iBAAiB,EACjB,EAAE,EACF0D,OAAO,CACR,CACA5C,IAAI,CACHrP,QAAQ,CAAC,MAAK;QACZ,IAAI,CAACkC,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAAC2I,wBAAwB,GAAG,KAAK;MACvC,CAAC,CAAC,EACF/K,SAAS,CAAC,IAAI,CAACgL,sBAAsB,CAAC,CACvC,CACAoB,SAAS,CAAC;QACTK,IAAI,EAAG4F,iBAAsB,IAAI;UAC/B;UACA,IAAI,CAAC5C,0BAA0B,CAAC4C,iBAAiB,EAAEzE,OAAO,CAAC;QAC7D,CAAC;QACDZ,KAAK,EAAGA,KAAU,IAAI;UACpBjB,OAAO,CAACiB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;UAElC;UACA,IAAI,IAAI,CAAChG,gBAAgB,CAAClE,MAAM,GAAG,CAAC,EAAE;YACpC,IAAI,CAACkE,gBAAgB,CAAC,IAAI,CAACA,gBAAgB,CAAClE,MAAM,GAAG,CAAC,CAAC,CAAC4D,MAAM,GAAG,QAAQ;UAC3E;UAEA,MAAM4L,YAAY,GAChBtF,KAAK,EAAEA,KAAK,EAAEY,OAAO,IACrB,kDAAkD;UAEpD;UACA,IAAI,CAACzL,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;YAAEoK,IAAI,EAAE,MAAM;YAAE9G,IAAI,EAAEmI;UAAO,CAAE,EAC/B;YAAErB,IAAI,EAAE,IAAI;YAAE9G,IAAI,EAAE,GAAG,IAAI,CAACnD,SAAS;UAAmB,CAAE,CAC3D;UAED;UACA,IAAI,CAACuN,iBAAiB,CAAC,QAAQ,CAAC;UAEhC,IAAIjE,gBAAgB,IAAI,IAAI,CAACX,gBAAgB,CAACnI,MAAM,GAAG,CAAC,EAAE;YACxD,IAAI,CAACmI,gBAAgB,CAACsH,GAAG,EAAE;UAC7B;QACF;OACD,CAAC;IACN;IAEQR,8BAA8BA,CACpCnE,OAAe,EACf6C,IAAY,EACZhC,iBAAyB,EACzB7C,gBAAyB,EACzBC,aAAsB,EACtB2G,YAAoB;MAEpB,IAAI5G,gBAAgB,EAAE;QACpB,IAAI,CAACX,gBAAgB,CAACmD,IAAI,CAAC;UAAE6D,OAAO,EAAErE,OAAO;UAAEsE,IAAI,EAAE;QAAM,CAAE,CAAC;MAChE;MACA,MAAMnD,OAAO,GAAGnD,gBAAgB,GAAG,IAAI,CAACX,gBAAgB,GAAG2C,OAAO;MAClE,MAAM;QAAEuE;MAAO,CAAE,GAAG,IAAI,CAACpB,qBAAqB,EAAE;MAEhD,IAAI,CAACxJ,sBAAsB,CACxB6K,cAAc,CACbrD,OAAO,EACP0B,IAAI,EACJ7E,gBAAgB,EAChBC,aAAa,EACb,IAAI,CAACf,eAAe,EACpB2D,iBAAiB,EACjB+D,YAAY,EACZL,OAAO,CACR,CACA5C,IAAI,CACHrP,QAAQ,CAAC,MAAK;QACZ,IAAI,CAACkC,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAAC2I,wBAAwB,GAAG,KAAK;MACvC,CAAC,CAAC,EACF/K,SAAS,CAAC,IAAI,CAACgL,sBAAsB,CAAC,CACvC,CACAoB,SAAS,CAAC;QACTK,IAAI,EAAG4F,iBAAsB,IAAI;UAC/B;UACA,IAAI,CAAC5C,0BAA0B,CAAC4C,iBAAiB,EAAEzE,OAAO,CAAC;QAC7D,CAAC;QACDZ,KAAK,EAAGA,KAAU,IAAI;UACpBjB,OAAO,CAACiB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;UAClC,MAAMsF,YAAY,GAChBtF,KAAK,EAAEA,KAAK,EAAEY,OAAO,IACrB,kDAAkD;UACpD,IAAI,CAACC,cAAc,CAACyE,YAAY,CAAC;UACjC,IAAI1G,gBAAgB,IAAI,IAAI,CAACX,gBAAgB,CAACnI,MAAM,GAAG,CAAC,EAAE;YACxD,IAAI,CAACmI,gBAAgB,CAACsH,GAAG,EAAE;UAC7B;QACF;OACD,CAAC;IACN;IAEA;IACQ5E,gCAAgCA,CAACT,SAAc;MACrD,IAAI,CAACA,SAAS,EAAE;QACdnB,OAAO,CAAC0G,IAAI,CAAC,sCAAsC,CAAC;QACpD;MACF;MAEA1G,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEkB,SAAS,CAAC;MAC7DnB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC3J,SAAS,CAAC;MACpD0J,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE0G,MAAM,CAACC,IAAI,CAACzF,SAAS,CAAC,CAAC;MAEjE;MACA,IAAI,CAAC3C,eAAe,GAAG,EAAE;MACzB,IAAI,CAACC,WAAW,GAAG,EAAE;MAErB,IAAIoI,WAAW,GAAG,CAAC;MAEnB;MACA,IAAI,IAAI,CAACvQ,SAAS,KAAK,YAAY,EAAE;QACnC,IAAI,CAACwQ,6BAA6B,CAAC3F,SAAS,EAAE0F,WAAW,CAAC;MAC5D,CAAC,MAAM,IAAI,IAAI,CAACvQ,SAAS,KAAK,eAAe,EAAE;QAC7C,IAAI,CAACyQ,gCAAgC,CAAC5F,SAAS,EAAE0F,WAAW,CAAC;MAC/D;MAEA7G,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE;QACxCzB,eAAe,EAAE,IAAI,CAACA,eAAe;QACrCC,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7BuI,UAAU,EAAE,IAAI,CAACxI,eAAe,CAACzH;OAClC,CAAC;IACJ;IAEQ+P,6BAA6BA,CACnC3F,SAAc,EACd0F,WAAmB;MAEnB7G,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAE;QAC5DgH,MAAM,EAAE9F,SAAS,CAAC8F,MAAM;QACxBC,YAAY,EAAE/F,SAAS,CAAC8F,MAAM,EAAElQ,MAAM;QACtCyK,WAAW,EAAEL,SAAS,CAACK,WAAW;QAClClC,MAAM,EAAE6B,SAAS,CAAC7B,MAAM;QACxB6H,cAAc,EAAEhG,SAAS,CAACgG;OAC3B,CAAC;MAEF;MACA,IAAI,CAACrQ,oBAAoB,GAAG,EAAE;MAC9B,IAAI,CAACS,mBAAmB,GAAG,EAAE;MAC7B,IAAI,CAACJ,uBAAuB,GAAG,EAAE;MACjC,IAAI,CAACqB,uBAAuB,GAAG,EAAE;MAEjC;MACA,IAAI2I,SAAS,CAAC7B,MAAM,EAAE;QACpB,IAAI,CAACxI,oBAAoB,CAACuL,IAAI,CAAC;UAC7BvG,EAAE,EAAE,UAAU+K,WAAW,EAAE,EAAE;UAC7BnQ,IAAI,EAAEyK,SAAS,CAAC7B,MAAM;UACtB8H,IAAI,EAAE;SACP,CAAC;QACFpH,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEkB,SAAS,CAAC7B,MAAM,CAAC;MACvD;MAEA;MACA,IAAI6B,SAAS,CAAC8F,MAAM,IAAI5F,KAAK,CAACC,OAAO,CAACH,SAAS,CAAC8F,MAAM,CAAC,EAAE;QACvDjH,OAAO,CAACC,GAAG,CACT,yCAAyC,EACzCkB,SAAS,CAAC8F,MAAM,CAAClQ,MAAM,CACxB;QAEDoK,SAAS,CAAC8F,MAAM,CAACzB,OAAO,CAAC,CAAC6B,QAAa,EAAEC,aAAqB,KAAI;UAChEtH,OAAO,CAACC,GAAG,CACT,eAAeqH,aAAa,SAASD,QAAQ,CAACE,UAAU,IAAI,EAC5DF,QAAQ,CAACG,YAAY,CACtB;UAED,IAAIH,QAAQ,CAACJ,MAAM,IAAI5F,KAAK,CAACC,OAAO,CAAC+F,QAAQ,CAACJ,MAAM,CAAC,EAAE;YACrDjH,OAAO,CAACC,GAAG,CACT,eAAeqH,aAAa,QAAQD,QAAQ,CAACJ,MAAM,CAAClQ,MAAM,eAAe,CAC1E;YAEDsQ,QAAQ,CAACJ,MAAM,CAACzB,OAAO,CAAC,CAACiC,UAAe,EAAEC,SAAiB,KAAI;cAC7D1H,OAAO,CAACC,GAAG,CAAC,kBAAkBqH,aAAa,IAAII,SAAS,GAAG,EAAE;gBAC3DC,SAAS,EAAEF,UAAU,CAACE,SAAS;gBAC/BC,WAAW,EAAEH,UAAU,CAACG,WAAW;gBACnCL,UAAU,EAAEE,UAAU,CAACF;eACxB,CAAC;cAEF;cACA,IACEE,UAAU,CAACF,UAAU,KAAK,CAAC,IAC3BE,UAAU,CAACE,SAAS,KAAK,OAAO,IAChCF,UAAU,CAACG,WAAW,EACtB;gBACA5H,OAAO,CAACC,GAAG,CACT,2CAA2C,EAC3CwH,UAAU,CAACG,WAAW,CACvB;gBACD,IAAI,CAACrQ,mBAAmB,CAAC8K,IAAI,CAAC;kBAC5BvG,EAAE,EAAE,SAAS+K,WAAW,EAAE,EAAE;kBAC5BnQ,IAAI,EAAE,GAAG+Q,UAAU,CAACE,SAAS,EAAE;kBAC/BP,IAAI,EAAE;iBACP,CAAC;cACJ;cAEA;cACA,IACEK,UAAU,CAACF,UAAU,KAAK,CAAC,IAC3BE,UAAU,CAACE,SAAS,KAAK,wBAAwB,IACjDF,UAAU,CAACG,WAAW,EACtB;gBACA5H,OAAO,CAACC,GAAG,CACT,kDAAkD,EAClDwH,UAAU,CAACG,WAAW,CACvB;gBACD,MAAMC,OAAO,GAAGJ,UAAU,CAACG,WAAW,CAACE,QAAQ,EAAE;gBACjD,MAAMC,KAAK,GAAGF,OAAO,CAClBG,KAAK,CAAC,GAAG,CAAC,CACV/F,GAAG,CAAEnG,EAAU,IAAKA,EAAE,CAACmM,IAAI,EAAE,CAAC,CAC9BC,MAAM,CAAEpM,EAAU,IAAKA,EAAE,CAAC;gBAE7BiM,KAAK,CAACvC,OAAO,CAAE2C,IAAY,IAAI;kBAC7B,IAAI,CAAChR,uBAAuB,CAACkL,IAAI,CAAC;oBAChCvG,EAAE,EAAE,aAAa+K,WAAW,EAAE,EAAE;oBAChCnQ,IAAI,EAAE,mBAAmByR,IAAI,EAAE;oBAC/Bf,IAAI,EAAE;mBACP,CAAC;gBACJ,CAAC,CAAC;cACJ;cAEA;cACA,IACEK,UAAU,CAACF,UAAU,KAAK,CAAC,IAC3BE,UAAU,CAACG,WAAW,KAAK,MAAM,EACjC;gBACA5H,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE;kBAC1DmI,GAAG,EAAEX,UAAU,CAACE,SAAS;kBACzBnF,KAAK,EAAEiF,UAAU,CAACG;iBACnB,CAAC;gBAEF,IAAIH,UAAU,CAACE,SAAS,KAAK,mBAAmB,EAAE;kBAChD;kBACA,IAAI,IAAI,CAACnP,uBAAuB,CAACzB,MAAM,KAAK,CAAC,EAAE;oBAC7C,IAAI,CAACyB,uBAAuB,CAAC6J,IAAI,CAAC;sBAChCvG,EAAE,EAAE,aAAa+K,WAAW,EAAE,EAAE;sBAChCnQ,IAAI,EAAE,oBAAoB;sBAC1B0Q,IAAI,EAAE;qBACP,CAAC;kBACJ;gBACF,CAAC,MAAM,IAAIK,UAAU,CAACE,SAAS,CAACU,UAAU,CAAC,YAAY,CAAC,EAAE;kBACxD;kBACA,IAAIC,aAAa,GAAGb,UAAU,CAACE,SAAS;kBACxC,IAAIW,aAAa,CAACD,UAAU,CAAC,YAAY,CAAC,EAAE;oBAC1CC,aAAa,GAAGA,aAAa,CAC1BlD,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CACzBA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;kBACvB;kBAEA,IAAI,CAAC5M,uBAAuB,CAAC6J,IAAI,CAAC;oBAChCvG,EAAE,EAAE,aAAa+K,WAAW,EAAE,EAAE;oBAChCnQ,IAAI,EAAE,GAAG4R,aAAa,EAAE;oBACxBlB,IAAI,EAAE;mBACP,CAAC;gBACJ;cACF;YACF,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ;MAEApH,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;QACvCsI,WAAW,EAAE,IAAI,CAACzR,oBAAoB;QACtC0R,UAAU,EAAE,IAAI,CAACjR,mBAAmB;QACpCkR,cAAc,EAAE,IAAI,CAACtR,uBAAuB;QAC5CuR,cAAc,EAAE,IAAI,CAAClQ;OACtB,CAAC;MAEF;MACA,MAAMmQ,aAAa,GAAG,CAAC,CAAC,CAAC;MACzB,MAAMC,eAAe,GACnB,IAAI,CAAC9R,oBAAoB,CAACC,MAAM,GAAG,IAAI,CAACQ,mBAAmB,CAACR,MAAM;MACpE,IAAI,CAACsC,6BAA6B,GAAGwP,IAAI,CAACC,KAAK,CAC5CF,eAAe,GAAGD,aAAa,GAAI,GAAG,CACxC;IACH;IAEQ5B,gCAAgCA,CACtC5F,SAAc,EACd0F,WAAmB;MAEnB7G,OAAO,CAACC,GAAG,CACT,+DAA+D,CAChE;MACDD,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEkB,SAAS,CAAC;MACtEnB,OAAO,CAACC,GAAG,CACT,0CAA0C,EAC1C0G,MAAM,CAACC,IAAI,CAACzF,SAAS,CAAC,CACvB;MACDnB,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE,IAAI,CAAC3J,SAAS,CAAC;MACjE0J,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE4G,WAAW,CAAC;MAE1D;MACA,IAAI,CAAC/P,oBAAoB,GAAG,EAAE;MAC9B,IAAI,CAACS,mBAAmB,GAAG,EAAE;MAC7B,IAAI,CAACJ,uBAAuB,GAAG,EAAE;MACjC,IAAI,CAACS,kBAAkB,GAAG,EAAE;MAC5B,IAAI,CAACY,uBAAuB,GAAG,EAAE;MAEjCwH,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;MAE1D;MACA,MAAM8I,sBAAsB,GAC1B5H,SAAS,CAAC6H,IAAI,IAAI7H,SAAS,CAACgF,IAAI,IAAIhF,SAAS,CAACrH,WAAW;MAE3DkG,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE;QACtD+I,IAAI,EAAE7H,SAAS,CAAC6H,IAAI;QACpB7C,IAAI,EAAEhF,SAAS,CAACgF,IAAI;QACpBrM,WAAW,EAAEqH,SAAS,CAACrH,WAAW;QAClCiP;OACD,CAAC;MAEF,IAAIA,sBAAsB,EAAE;QAC1B,IAAIE,cAAc,GAChB9H,SAAS,CAAC6H,IAAI,IACd7H,SAAS,CAACgF,IAAI,IACdhF,SAAS,CAACrH,WAAW,IACrB,4BAA4B;QAE9B;QACA,IAAImP,cAAc,CAAClS,MAAM,GAAG,GAAG,EAAE;UAC/BkS,cAAc,GAAGA,cAAc,CAACC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK;QAC3D;QAEA,IAAI,CAACpS,oBAAoB,CAACuL,IAAI,CAAC;UAC7BvG,EAAE,EAAE,UAAU+K,WAAW,EAAE,EAAE;UAC7BnQ,IAAI,EAAEuS,cAAc;UACpB7B,IAAI,EAAE;SACP,CAAC;QACFpH,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEgJ,cAAc,CAAC;MACnE;MAEA;MACA,IAAIE,eAAe,GAAG,EAAE;MAExBnJ,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;QAC5CmJ,eAAe,EAAE,CAAC,CAACjI,SAAS,CAACkI,YAAY;QACzCA,YAAY,EAAElI,SAAS,CAACkI,YAAY;QACpC9J,KAAK,EAAE4B,SAAS,CAAC5B,KAAK;QACtB+J,SAAS,EAAEnI,SAAS,CAACmI,SAAS;QAC9BC,YAAY,EAAEpI,SAAS,CAACoI;OACzB,CAAC;MAEF;MACA,IAAIpI,SAAS,CAACkI,YAAY,IAAIlI,SAAS,CAACkI,YAAY,CAACG,QAAQ,EAAE;QAC7D,MAAMC,SAAS,GAAGpI,KAAK,CAACC,OAAO,CAACH,SAAS,CAACkI,YAAY,CAACG,QAAQ,CAAC,GAC5DrI,SAAS,CAACkI,YAAY,CAACG,QAAQ,GAC/B,CAACrI,SAAS,CAACkI,YAAY,CAACG,QAAQ,CAAC;QAErCL,eAAe,GAAGM,SAAS,CAACxH,GAAG,CAAEyH,GAAQ,IAAI;UAC3C,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;YACtD,OAAO;cAAEC,OAAO,EAAED;YAAG,CAAE;UACzB;UACA,OAAOA,GAAG;QACZ,CAAC,CAAC;MACJ;MACA;MAAA,KACK,IAAIvI,SAAS,CAACoI,YAAY,EAAE;QAC/BJ,eAAe,GAAG,CAAChI,SAAS,CAACoI,YAAY,CAAC;MAC5C;MACA;MAAA,KACK,IAAIpI,SAAS,CAAC5B,KAAK,IAAI4B,SAAS,CAACmI,SAAS,EAAE;QAC/CH,eAAe,GAAG,CAAC;UAAEQ,OAAO,EAAExI,SAAS,CAAC5B,KAAK,IAAI4B,SAAS,CAACmI;QAAS,CAAE,CAAC;MACzE;MAEAH,eAAe,CAAC3D,OAAO,CAAEgE,QAAa,IAAI;QACxC,MAAMG,OAAO,GAAGH,QAAQ,CAACG,OAAO,IAAIH,QAAQ,CAAC1N,EAAE;QAC/C,MAAMwN,SAAS,GACbE,QAAQ,CAACjK,KAAK,IACdiK,QAAQ,CAACI,mBAAmB,IAC5B,aAAaD,OAAO,EAAE;QAExB,IAAI,CAACpS,mBAAmB,CAAC8K,IAAI,CAAC;UAC5BvG,EAAE,EAAE,SAAS+K,WAAW,EAAE,EAAE;UAC5BnQ,IAAI,EAAE4S,SAAS;UACflC,IAAI,EAAE;SACP,CAAC;QACFpH,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEqJ,SAAS,CAAC;MAC7D,CAAC,CAAC;MAEF;MACA,IAAIO,mBAAmB,GAAG,EAAE;MAE5B;MACA,IAAI1I,SAAS,CAACkI,YAAY,IAAIlI,SAAS,CAACkI,YAAY,CAACS,gBAAgB,EAAE;QACrE,MAAMC,MAAM,GAAG1I,KAAK,CAACC,OAAO,CAACH,SAAS,CAACkI,YAAY,CAACS,gBAAgB,CAAC,GACjE3I,SAAS,CAACkI,YAAY,CAACS,gBAAgB,GACvC,CAAC3I,SAAS,CAACkI,YAAY,CAACS,gBAAgB,CAAC;QAE7CD,mBAAmB,GAAGE,MAAM,CAAC9H,GAAG,CAAEyH,GAAQ,IAAI;UAC5C,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;YACtD,OAAO;cAAEM,eAAe,EAAEN;YAAG,CAAE;UACjC;UACA,OAAOA,GAAG;QACZ,CAAC,CAAC;MACJ;MACA;MAAA,KACK,IACHvI,SAAS,CAAC8I,aAAa,IACvB5I,KAAK,CAACC,OAAO,CAACH,SAAS,CAAC8I,aAAa,CAAC,EACtC;QACAJ,mBAAmB,GAAG1I,SAAS,CAAC8I,aAAa;MAC/C;MAEAJ,mBAAmB,CAACrE,OAAO,CAAE0E,KAAU,IAAI;QACzC,MAAM/B,IAAI,GAAG+B,KAAK,CAACF,eAAe,IAAIE,KAAK,CAACpO,EAAE;QAC9C,MAAMqO,cAAc,GAAGD,KAAK,CAACE,mBAAmB,IAAIF,KAAK,CAACxT,IAAI;QAC9D,MAAM2T,MAAM,GAAGF,cAAc,IAAI,sBAAsBhC,IAAI,EAAE;QAE7D,IAAI,CAAChR,uBAAuB,CAACkL,IAAI,CAAC;UAChCvG,EAAE,EAAE,aAAa+K,WAAW,EAAE,EAAE;UAChCnQ,IAAI,EAAE2T,MAAM;UACZjD,IAAI,EAAE;SACP,CAAC;QACFpH,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEoK,MAAM,CAAC;MAC9D,CAAC,CAAC;MAEF;MACA,IAAIC,cAAc,GAAG,EAAE;MACvB,IAAIC,kBAAkB,GAAG,EAAE;MAE3BvK,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;QAC3CmJ,eAAe,EAAE,CAAC,CAACjI,SAAS,CAACkI,YAAY;QACzCmB,mBAAmB,EAAErJ,SAAS,CAACkI,YAAY;QAC3CoB,QAAQ,EAAEtJ,SAAS,CAACuJ,KAAK;QACzBC,YAAY,EAAExJ,SAAS,CAACuJ,KAAK;QAC7BE,YAAY,EAAEzJ,SAAS,CAAC0J,SAAS;QACjCC,gBAAgB,EAAE3J,SAAS,CAAC0J;OAC7B,CAAC;MAEF;MACA,IAAI1J,SAAS,CAACkI,YAAY,EAAE;QAC1B,IAAIlI,SAAS,CAACkI,YAAY,CAAC0B,OAAO,EAAE;UAClC,MAAMC,QAAQ,GAAG3J,KAAK,CAACC,OAAO,CAACH,SAAS,CAACkI,YAAY,CAAC0B,OAAO,CAAC,GAC1D5J,SAAS,CAACkI,YAAY,CAAC0B,OAAO,GAC9B,CAAC5J,SAAS,CAACkI,YAAY,CAAC0B,OAAO,CAAC;UAEpCT,cAAc,GAAGU,QAAQ,CAAC/I,GAAG,CAAEyH,GAAQ,IAAI;YACzC,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;cACtD,OAAO;gBAAEuB,MAAM,EAAEvB;cAAG,CAAE;YACxB;YACA,OAAOA,GAAG;UACZ,CAAC,CAAC;QACJ;QACA,IAAIvI,SAAS,CAACkI,YAAY,CAAC6B,WAAW,EAAE;UACtC,MAAMC,YAAY,GAAG9J,KAAK,CAACC,OAAO,CAACH,SAAS,CAACkI,YAAY,CAAC6B,WAAW,CAAC,GAClE/J,SAAS,CAACkI,YAAY,CAAC6B,WAAW,GAClC,CAAC/J,SAAS,CAACkI,YAAY,CAAC6B,WAAW,CAAC;UAExCX,kBAAkB,GAAGY,YAAY,CAAClJ,GAAG,CAAEyH,GAAQ,IAAI;YACjD,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;cACtD,OAAO;gBAAEuB,MAAM,EAAEvB;cAAG,CAAE;YACxB;YACA,OAAOA,GAAG;UACZ,CAAC,CAAC;QACJ;MACF;MACA;MAAA,KACK;QACH,IAAIvI,SAAS,CAACuJ,KAAK,IAAIrJ,KAAK,CAACC,OAAO,CAACH,SAAS,CAACuJ,KAAK,CAAC,EAAE;UACrDJ,cAAc,GAAGnJ,SAAS,CAACuJ,KAAK;QAClC;QACA,IAAIvJ,SAAS,CAAC0J,SAAS,IAAIxJ,KAAK,CAACC,OAAO,CAACH,SAAS,CAAC0J,SAAS,CAAC,EAAE;UAC7DN,kBAAkB,GAAGpJ,SAAS,CAAC0J,SAAS;QAC1C;MACF;MAEA;MACAP,cAAc,CAAC9E,OAAO,CAAE9F,IAAS,IAAI;QACnC,MAAMuL,MAAM,GAAGvL,IAAI,CAACuL,MAAM,IAAIvL,IAAI,CAAC5D,EAAE;QACrC,MAAMsP,QAAQ,GAAG1L,IAAI,CAAC0L,QAAQ,IAAI1L,IAAI,CAAChJ,IAAI,IAAI,YAAYuU,MAAM,EAAE;QAEnE,IAAI,CAACrT,kBAAkB,CAACyK,IAAI,CAAC;UAC3BvG,EAAE,EAAE,QAAQ+K,WAAW,EAAE,EAAE;UAC3BnQ,IAAI,EAAE0U,QAAQ;UACdhE,IAAI,EAAE;SACP,CAAC;QACFpH,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEmL,QAAQ,CAAC;MACnE,CAAC,CAAC;MAEF;MACAb,kBAAkB,CAAC/E,OAAO,CAAE6F,QAAa,IAAI;QAC3C,MAAMC,UAAU,GAAGD,QAAQ,CAACJ,MAAM,IAAII,QAAQ,CAACvP,EAAE;QACjD,MAAMyP,YAAY,GAChBF,QAAQ,CAACD,QAAQ,IAAIC,QAAQ,CAAC3U,IAAI,IAAI,iBAAiB4U,UAAU,EAAE;QAErE,IAAI,CAAC1T,kBAAkB,CAACyK,IAAI,CAAC;UAC3BvG,EAAE,EAAE,QAAQ+K,WAAW,EAAE,EAAE;UAC3BnQ,IAAI,EAAE6U,YAAY;UAClBnE,IAAI,EAAE;SACP,CAAC;QACFpH,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEsL,YAAY,CAAC;MACpE,CAAC,CAAC;MAEFvL,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE;QACrDsI,WAAW,EAAE,IAAI,CAACzR,oBAAoB;QACtC0R,UAAU,EAAE,IAAI,CAACjR,mBAAmB;QACpCkR,cAAc,EAAE,IAAI,CAACtR,uBAAuB;QAC5CqU,SAAS,EAAE,IAAI,CAAC5T,kBAAkB;QAClC8Q,cAAc,EAAE,IAAI,CAAClQ;OACtB,CAAC;MAEF;MACAwH,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;QACvCwL,WAAW,EAAE,IAAI,CAAC3U,oBAAoB,CAACC,MAAM;QAC7C2U,UAAU,EAAE,IAAI,CAACnU,mBAAmB,CAACR,MAAM;QAC3C4U,cAAc,EAAE,IAAI,CAACxU,uBAAuB,CAACJ,MAAM;QACnD6U,SAAS,EAAE,IAAI,CAAChU,kBAAkB,CAACb,MAAM;QACzC8U,cAAc,EAAE,IAAI,CAACrT,uBAAuB,CAACzB;OAC9C,CAAC;MAEF;MACAiJ,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;QAClC3J,SAAS,EAAE,IAAI,CAACA,SAAS;QACzBwV,eAAe,EAAE,IAAI,CAACxV,SAAS,KAAK,eAAe;QACnDyV,YAAY,EAAE,IAAI,CAACnU,kBAAkB,CAACb,MAAM,GAAG,CAAC;QAChDiV,aAAa,EAAE,IAAI,CAACpU,kBAAkB,CAACqK,GAAG,CAAEgK,CAAC,IAAKA,CAAC,CAACvV,IAAI;OACzD,CAAC;MAEF;MACA,MAAMiS,aAAa,GAAG,CAAC,CAAC,CAAC;MACzB,MAAMC,eAAe,GACnB,IAAI,CAAC9R,oBAAoB,CAACC,MAAM,GAAG,IAAI,CAACQ,mBAAmB,CAACR,MAAM;MACpE,IAAI,CAACsC,6BAA6B,GAAGwP,IAAI,CAACC,KAAK,CAC5CF,eAAe,GAAGD,aAAa,GAAI,GAAG,CACxC;IACH;;uCAptCWvN,uBAAuB,EAAAzG,EAAA,CAAAuX,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAzX,EAAA,CAAAuX,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA1X,EAAA,CAAAuX,iBAAA,CAAAI,EAAA,CAAAC,mBAAA,GAAA5X,EAAA,CAAAuX,iBAAA,CAAAM,EAAA,CAAAC,sBAAA,GAAA9X,EAAA,CAAAuX,iBAAA,CAAAQ,EAAA,CAAAC,mBAAA,GAAAhY,EAAA,CAAAuX,iBAAA,CAAAU,EAAA,CAAAC,aAAA,GAAAlY,EAAA,CAAAuX,iBAAA,CAAAY,EAAA,CAAAC,WAAA,GAAApY,EAAA,CAAAuX,iBAAA,CAAAc,EAAA,CAAAC,oBAAA;IAAA;;YAAvB7R,uBAAuB;MAAA8R,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBAavB9Y,iCAAiC;;;;;;;;;;;;UC1CxCI,EAJN,CAAAC,cAAA,aAAuC,aAEZ,aACD,gBAC+C;UAAvCD,EAAA,CAAAI,UAAA,mBAAAwY,yDAAA;YAAA,OAASD,GAAA,CAAA9I,YAAA,EAAc;UAAA,EAAC;UAClD7P,EAAA,CAAA6C,SAAA,kBAIY;UACZ7C,EAAA,CAAAC,cAAA,cAAyB;UAAAD,EAAA,CAAAE,MAAA,GAAe;UAG9CF,EAH8C,CAAAG,YAAA,EAAO,EACxC,EACL,EACF;UAOAH,EAJN,CAAAC,cAAA,aAA0B,aAEyC,aACrC,iBAC+C;UAA1CD,EAAA,CAAAI,UAAA,mBAAAyY,0DAAA;YAAA,OAASF,GAAA,CAAAzI,eAAA,EAAiB;UAAA,EAAC;UACtDlQ,EAAA,CAAA6C,SAAA,oBAKW;UACb7C,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAgC,UAAA,KAAA8W,0CAAA,qBAKC;UAGH9Y,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAgC,UAAA,KAAA+W,uCAAA,mBAAyD;UAuB3D/Y,EAAA,CAAAG,YAAA,EAAM;UAOAH,EAJN,CAAAC,cAAA,eAAyB,cAEG,eACI,kBAKzB;UADCD,EAAA,CAAAI,UAAA,mBAAA4Y,0DAAA;YAAA,OAASL,GAAA,CAAAxJ,iBAAA,CAAkB,WAAW,CAAC;UAAA,EAAC;UAExCnP,EAAA,CAAAE,MAAA,mBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAIC;UADCD,EAAA,CAAAI,UAAA,mBAAA6Y,0DAAA;YAAA,OAASN,GAAA,CAAAxJ,iBAAA,CAAkB,QAAQ,CAAC;UAAA,EAAC;UAErCnP,EAAA,CAAAE,MAAA,sBACF;UAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;UAENH,EAAA,CAAAC,cAAA,eAA2B;UAkkBzBD,EAhkBA,CAAAgC,UAAA,KAAAkX,uCAAA,oBAAsE,KAAAC,uCAAA,kBAgkBN;UA8ExEnZ,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACF;;;UA3tB2BH,EAAA,CAAAuB,SAAA,GAAe;UAAfvB,EAAA,CAAA6B,iBAAA,CAAA8W,GAAA,CAAA/W,SAAA,CAAe;UAQpB5B,EAAA,CAAAuB,SAAA,GAAwC;UAAxCvB,EAAA,CAAAuD,WAAA,cAAAoV,GAAA,CAAAjP,oBAAA,CAAwC;UAIxD1J,EAAA,CAAAuB,SAAA,GAAgE;UAAhEvB,EAAA,CAAAwB,UAAA,aAAAmX,GAAA,CAAAjP,oBAAA,gCAAgE;UAQjE1J,EAAA,CAAAuB,SAAA,EAA2B;UAA3BvB,EAAA,CAAAwB,UAAA,UAAAmX,GAAA,CAAAjP,oBAAA,CAA2B;UAQJ1J,EAAA,CAAAuB,SAAA,EAA2B;UAA3BvB,EAAA,CAAAwB,UAAA,UAAAmX,GAAA,CAAAjP,oBAAA,CAA2B;UAgCjD1J,EAAA,CAAAuB,SAAA,GAA+C;UAA/CvB,EAAA,CAAAuD,WAAA,WAAAoV,GAAA,CAAAhP,cAAA,iBAA+C;UAO/C3J,EAAA,CAAAuB,SAAA,GAA4C;UAA5CvB,EAAA,CAAAuD,WAAA,WAAAoV,GAAA,CAAAhP,cAAA,cAA4C;UAU1C3J,EAAA,CAAAuB,SAAA,GAAoC;UAApCvB,EAAA,CAAAwB,UAAA,SAAAmX,GAAA,CAAAhP,cAAA,iBAAoC;UAgkBpC3J,EAAA,CAAAuB,SAAA,EAAiC;UAAjCvB,EAAA,CAAAwB,UAAA,SAAAmX,GAAA,CAAAhP,cAAA,cAAiC;;;qBD9nB3CvK,YAAY,EAAAga,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,QAAA,EACZ5Z,WAAW,EACXC,iCAAiC,EACjCC,aAAa;MAAA2Z,MAAA;IAAA;;SAKJ/S,uBAAuB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}