{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { EventEmitter } from '@angular/core';\nimport { FormsModule, FormControl, ReactiveFormsModule } from '@angular/forms';\nimport { IconComponent, ToggleComponent } from '@ava/play-comp-library';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nconst _c0 = [\"messagesContainer\"];\nconst _c1 = [\"fileInput\"];\nconst _c2 = (a0, a1, a2, a3, a4, a5) => ({\n  \"bot-response\": a0,\n  \"user-message\": a1,\n  \"status-message\": a2,\n  \"success\": a3,\n  \"failed\": a4,\n  \"pending\": a5\n});\nfunction AgentExecutionPlaygroundComponent_div_3_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function AgentExecutionPlaygroundComponent_div_3_button_3_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const msg_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.copyToClipboard(msg_r3.text));\n    });\n    i0.ɵɵelement(1, \"ava-icon\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"iconSize\", 16);\n  }\n}\nfunction AgentExecutionPlaygroundComponent_div_3_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtext(1, \"Copied!\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionPlaygroundComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 16);\n    i0.ɵɵtext(2);\n    i0.ɵɵtemplate(3, AgentExecutionPlaygroundComponent_div_3_button_3_Template, 2, 1, \"button\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, AgentExecutionPlaygroundComponent_div_3_div_4_Template, 2, 0, \"div\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const msg_r3 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", msg_r3.from);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction6(5, _c2, msg_r3.from === \"ai\" && !(msg_r3.text.includes(\"Status: Pending\") || msg_r3.text.includes(\"Status: Success\") || msg_r3.text.includes(\"Status: Failed\")), msg_r3.from === \"user\", msg_r3.from === \"ai\" && ctx_r3.showStatusOnly && (msg_r3.text.includes(\"Status: Pending\") || msg_r3.text.includes(\"Status: Success\") || msg_r3.text.includes(\"Status: Failed\")), msg_r3.from === \"ai\" && ctx_r3.showStatusOnly && msg_r3.text.includes(\"Status: Success\"), msg_r3.from === \"ai\" && ctx_r3.showStatusOnly && msg_r3.text.includes(\"Status: Failed\"), msg_r3.from === \"ai\" && ctx_r3.showStatusOnly && msg_r3.text.includes(\"Status: Pending\")));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", msg_r3.text, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", msg_r3.from === \"ai\" && !ctx_r3.showStatusOnly && !(msg_r3.text.includes(\"Status: Pending\") || msg_r3.text.includes(\"Status: Success\") || msg_r3.text.includes(\"Status: Failed\")));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.showCopiedToast);\n  }\n}\nfunction AgentExecutionPlaygroundComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23)(2, \"div\", 24);\n    i0.ɵɵelement(3, \"div\", 25)(4, \"div\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 27);\n    i0.ɵɵtext(6, \"Generating...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AgentExecutionPlaygroundComponent_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function AgentExecutionPlaygroundComponent_button_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      i0.ɵɵnextContext();\n      const fileInput_r6 = i0.ɵɵreference(9);\n      return i0.ɵɵresetView(fileInput_r6.click());\n    });\n    i0.ɵɵelement(1, \"ava-icon\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"iconSize\", 16);\n  }\n}\nfunction AgentExecutionPlaygroundComponent_div_10_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"span\", 33);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function AgentExecutionPlaygroundComponent_div_10_div_1_Template_button_click_3_listener() {\n      const i_r8 = i0.ɵɵrestoreView(_r7).index;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.removeFile(i_r8));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r9 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(file_r9.documentName);\n  }\n}\nfunction AgentExecutionPlaygroundComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵtemplate(1, AgentExecutionPlaygroundComponent_div_10_div_1_Template, 4, 1, \"div\", 31);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.filesUploadedData);\n  }\n}\nfunction AgentExecutionPlaygroundComponent_div_14_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"ava-toggle\", 38);\n    i0.ɵɵlistener(\"checkedChange\", function AgentExecutionPlaygroundComponent_div_14_div_1_Template_ava_toggle_checkedChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onConversationalToggle($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"checked\", ctx_r3.isConvChecked)(\"title\", \"Conversational\")(\"position\", \"left\");\n  }\n}\nfunction AgentExecutionPlaygroundComponent_div_14_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"ava-toggle\", 38);\n    i0.ɵɵlistener(\"checkedChange\", function AgentExecutionPlaygroundComponent_div_14_div_2_Template_ava_toggle_checkedChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onTemplateToggle($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"checked\", ctx_r3.isUseTemplate)(\"title\", \"Use Template\")(\"position\", \"left\");\n  }\n}\nfunction AgentExecutionPlaygroundComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtemplate(1, AgentExecutionPlaygroundComponent_div_14_div_1_Template, 2, 3, \"div\", 36)(2, AgentExecutionPlaygroundComponent_div_14_div_2_Template, 2, 3, \"div\", 36);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.showChatInteractionToggles);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.showChatInteractionToggles);\n  }\n}\nexport let AgentExecutionPlaygroundComponent = /*#__PURE__*/(() => {\n  class AgentExecutionPlaygroundComponent {\n    isMenuOpen = false;\n    isToolMenuOpen = false;\n    promptChange = new EventEmitter();\n    promptOptions = [];\n    selectedValue = 'default'; // Input for pre-selected value\n    agentType = 'individual'; // Input for agent type ('individual' or 'collaborative')\n    showChatInteractionToggles = false; // Input to show conversational and template toggles\n    showAiPrincipleToggle = false; // Input to show AI principle toggle\n    showDropdown = true; // Input to control dropdown visibility\n    showAgentNameInput = false; // Input to show disabled agent name input field\n    agentNamePlaceholder = 'Agent Name'; // Placeholder for agent name input\n    displayedAgentName = ''; // Agent name to display in disabled input\n    showFileUploadButton = false; // Controls visibility of attach file button\n    showStatusOnly = false; // Controls whether to show only status instead of full response\n    selectedPrompt = 'default';\n    // Form control for agent name display\n    agentNameDisplayControl = new FormControl({\n      value: '',\n      disabled: true\n    });\n    // Chat data\n    showCopiedToast = false;\n    inputText = '';\n    previousMessagesLength = 0;\n    shouldScrollToBottom = false;\n    messages = [];\n    isLoading = false;\n    isDisabled = false;\n    showLoader = true;\n    messageSent = new EventEmitter();\n    conversationalToggle = new EventEmitter();\n    templateToggle = new EventEmitter();\n    filesSelected = new EventEmitter();\n    messagesContainer;\n    fileInput;\n    showApprovalButton = true;\n    approvalRequested = new EventEmitter();\n    isMinimalView = false;\n    // Simple toggle properties for display only\n    isConvChecked = true;\n    isUseTemplate = false;\n    // File upload properties\n    filesUploadedData = [];\n    acceptedFileType = '';\n    ngOnInit() {\n      this.messages = [{\n        from: 'ai',\n        text: 'Hi there, how can I help you today?'\n      }];\n      this.shouldScrollToBottom = true;\n      // Set accepted file type based on agent type\n      if (this.agentType === 'collaborative') {\n        this.acceptedFileType = '.zip';\n      } else {\n        this.acceptedFileType = '.pdf,.doc,.docx,.txt,.csv,.xlsx,.xls,.ppt,.pptx,.png,.jpg,.jpeg,.gif,.bmp,.svg';\n      }\n      // Set selected prompt from input\n      if (this.selectedValue) {\n        this.selectedPrompt = this.selectedValue;\n        // Update displayed agent name if showing agent name input\n        if (this.showAgentNameInput && !this.displayedAgentName) {\n          this.displayedAgentName = this.selectedValue;\n          this.agentNameDisplayControl.setValue(this.selectedValue);\n        }\n      }\n      // Initialize agent name display control\n      if (this.displayedAgentName) {\n        this.agentNameDisplayControl.setValue(this.displayedAgentName);\n      }\n    }\n    ngOnChanges(changes) {\n      // Update selectedPrompt when selectedValue input changes\n      if (changes['selectedValue'] && changes['selectedValue'].currentValue) {\n        // The selectedValue from parent should be the name (for dropdown display)\n        this.selectedPrompt = changes['selectedValue'].currentValue;\n      }\n      // Update agent name display control when displayedAgentName input changes\n      if (changes['displayedAgentName'] && changes['displayedAgentName'].currentValue !== undefined) {\n        this.agentNameDisplayControl.setValue(changes['displayedAgentName'].currentValue);\n      }\n      // Update accepted file type when agentType changes\n      if (changes['agentType'] && changes['agentType'].currentValue) {\n        if (changes['agentType'].currentValue === 'collaborative') {\n          this.acceptedFileType = '.zip';\n        } else {\n          this.acceptedFileType = '.pdf,.doc,.docx,.txt,.csv,.xlsx,.xls,.ppt,.pptx,.png,.jpg,.jpeg,.gif,.bmp,.svg';\n        }\n      }\n    }\n    ngAfterViewChecked() {\n      if (this.shouldScrollToBottom) {\n        this.scrollToBottom();\n        this.shouldScrollToBottom = false;\n      }\n    }\n    scrollToBottom() {\n      try {\n        if (this.messagesContainer && this.messagesContainer.nativeElement) {\n          // Scroll to bottom to show latest messages\n          this.messagesContainer.nativeElement.scrollTop = this.messagesContainer.nativeElement.scrollHeight;\n        }\n      } catch (err) {\n        console.error('Error scrolling to bottom:', err);\n      }\n    }\n    handleSendMessage() {\n      if (!this.inputText.trim() || this.isDisabled) {\n        return;\n      }\n      // Add user message to the chat\n      this.messages = [...this.messages, {\n        from: 'user',\n        text: this.inputText\n      }];\n      this.shouldScrollToBottom = true;\n      // Emit the message to parent component\n      const messageText = this.inputText;\n      this.inputText = '';\n      this.messageSent.emit(messageText);\n      // Clear uploaded files after sending message\n      this.clearFiles();\n    }\n    clearFiles() {\n      this.filesUploadedData = [];\n      this.filesSelected.emit(this.filesUploadedData);\n    }\n    toggleMenu() {\n      this.isMenuOpen = !this.isMenuOpen;\n    }\n    onAiPrincipleToggle(event) {\n      console.log('AI Principles toggle:', event);\n    }\n    onConversationalToggle(event) {\n      this.isConvChecked = event;\n      // If conversational is enabled, disable template\n      if (event && this.isUseTemplate) {\n        this.isUseTemplate = false;\n        this.templateToggle.emit(false);\n      }\n      console.log('Conversational mode:', event);\n      this.conversationalToggle.emit(event);\n    }\n    onTemplateToggle(event) {\n      this.isUseTemplate = event;\n      // If template is enabled, disable conversational\n      if (event && this.isConvChecked) {\n        this.isConvChecked = false;\n        this.conversationalToggle.emit(false);\n      }\n      console.log('Use template:', event);\n      this.templateToggle.emit(event);\n    }\n    onPromptChange(selectionData) {\n      // The dropdown component emits an object with selectedOptions and selectedValue\n      // selectedValue contains the name of the selected option\n      let selectedName;\n      if (typeof selectionData === 'string') {\n        selectedName = selectionData;\n      } else if (selectionData && selectionData.selectedValue) {\n        selectedName = selectionData.selectedValue;\n      } else if (selectionData && selectionData.selectedOptions && selectionData.selectedOptions.length > 0) {\n        selectedName = selectionData.selectedOptions[0].name;\n      } else {\n        return;\n      }\n      this.selectedPrompt = selectedName;\n      // Update displayed agent name if showing agent name input\n      if (this.showAgentNameInput) {\n        this.displayedAgentName = selectedName;\n        this.agentNameDisplayControl.setValue(selectedName);\n      }\n      // Find the option by name\n      const selectedOption = this.promptOptions.find(opt => opt.name === selectedName);\n      if (selectedOption) {\n        this.promptChange.emit(selectedOption);\n      }\n    }\n    copyToClipboard(text) {\n      navigator.clipboard.writeText(text).then(() => {\n        this.showCopiedToast = true;\n        setTimeout(() => {\n          this.showCopiedToast = false;\n        }, 2000);\n      });\n    }\n    save() {\n      this.isMenuOpen = false;\n      console.log('Save clicked');\n      // your save logic here\n    }\n    export() {\n      this.isMenuOpen = false;\n      console.log('Export clicked');\n      // your export logic here\n    }\n    // Hide menu when clicking outside\n    onClickOutside(event) {\n      const target = event.target;\n      if (!target.closest('.btn-menu')) {\n        this.isMenuOpen = false;\n      }\n    }\n    onEnterKeydown(event) {\n      // Only prevent default and send if Shift key is not pressed\n      if (!event.shiftKey) {\n        event.preventDefault();\n        this.handleSendMessage();\n      }\n    }\n    // File upload methods\n    onFileSelected(event) {\n      const files = event.target.files;\n      if (files && files.length > 0) {\n        this.filesUploadedData = [];\n        for (let i = 0; i < files.length; i++) {\n          const file = files[i];\n          // Validate file type for collaborative agents\n          if (this.agentType === 'collaborative') {\n            if (!file.name.toLowerCase().endsWith('.zip')) {\n              alert('Only ZIP files are accepted for collaborative agents.');\n              continue; // Skip this file\n            }\n          }\n          this.filesUploadedData.push({\n            id: `file_${Date.now()}_${i}`,\n            documentName: file.name,\n            isImage: file.type.startsWith('image/'),\n            file: file\n          });\n        }\n        console.log('Files selected:', this.filesUploadedData);\n        this.filesSelected.emit(this.filesUploadedData);\n      }\n    }\n    removeFile(index) {\n      this.filesUploadedData.splice(index, 1);\n      this.filesSelected.emit(this.filesUploadedData);\n    }\n    // Track by function for ngFor performance\n    trackByIndex(index, _item) {\n      return index;\n    }\n    onApprovalClick() {\n      this.approvalRequested.emit();\n    }\n    static ɵfac = function AgentExecutionPlaygroundComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AgentExecutionPlaygroundComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AgentExecutionPlaygroundComponent,\n      selectors: [[\"app-agent-execution-playground\"]],\n      viewQuery: function AgentExecutionPlaygroundComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messagesContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      hostBindings: function AgentExecutionPlaygroundComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function AgentExecutionPlaygroundComponent_click_HostBindingHandler($event) {\n            return ctx.onClickOutside($event);\n          }, false, i0.ɵɵresolveDocument)(\"keydown.enter\", function AgentExecutionPlaygroundComponent_keydown_enter_HostBindingHandler($event) {\n            return ctx.onEnterKeydown($event);\n          });\n        }\n      },\n      inputs: {\n        promptOptions: \"promptOptions\",\n        selectedValue: \"selectedValue\",\n        agentType: \"agentType\",\n        showChatInteractionToggles: \"showChatInteractionToggles\",\n        showAiPrincipleToggle: \"showAiPrincipleToggle\",\n        showDropdown: \"showDropdown\",\n        showAgentNameInput: \"showAgentNameInput\",\n        agentNamePlaceholder: \"agentNamePlaceholder\",\n        displayedAgentName: \"displayedAgentName\",\n        showFileUploadButton: \"showFileUploadButton\",\n        showStatusOnly: \"showStatusOnly\",\n        messages: \"messages\",\n        isLoading: \"isLoading\",\n        isDisabled: \"isDisabled\",\n        showLoader: \"showLoader\",\n        showApprovalButton: \"showApprovalButton\",\n        isMinimalView: \"isMinimalView\",\n        acceptedFileType: \"acceptedFileType\"\n      },\n      outputs: {\n        promptChange: \"promptChange\",\n        messageSent: \"messageSent\",\n        conversationalToggle: \"conversationalToggle\",\n        templateToggle: \"templateToggle\",\n        filesSelected: \"filesSelected\",\n        approvalRequested: \"approvalRequested\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 15,\n      vars: 11,\n      consts: [[\"messagesContainer\", \"\"], [\"fileInput\", \"\"], [1, \"playground-container\"], [1, \"layout\"], [\"class\", \"message-row\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"generating-indicator\", 4, \"ngIf\"], [1, \"input-container\"], [\"placeholder\", \"Enter something to test\", 3, \"ngModelChange\", \"keydown.enter\", \"ngModel\", \"disabled\"], [\"class\", \"attach-btn\", \"title\", \"Attach File\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"file\", \"multiple\", \"\", 2, \"display\", \"none\", 3, \"change\", \"accept\"], [\"class\", \"uploaded-files\", 4, \"ngIf\"], [1, \"right-icons\"], [\"title\", \"Send\", 1, \"send-btn\", 3, \"click\", \"disabled\"], [\"slot\", \"icon-start\", \"iconName\", \"send-horizontal\", \"iconColor\", \"var(--color-brand-primary)\", 3, \"iconSize\"], [\"class\", \"toggle-container\", 4, \"ngIf\"], [1, \"message-row\", 3, \"ngClass\"], [3, \"ngClass\"], [\"class\", \"copy-btn\", \"title\", \"Copy\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"copied-toast\", 4, \"ngIf\"], [\"title\", \"Copy\", 1, \"copy-btn\", 3, \"click\"], [\"slot\", \"icon-start\", \"iconName\", \"copy\", \"iconColor\", \"var(--color-brand-primary)\", 3, \"iconSize\"], [1, \"copied-toast\"], [1, \"generating-indicator\"], [1, \"generating-stepper\"], [1, \"modern-loading-spinner\"], [1, \"spinner-ring\"], [1, \"spinner-core\"], [1, \"generating-text\"], [\"title\", \"Attach File\", 1, \"attach-btn\", 3, \"click\"], [\"slot\", \"icon-start\", \"iconName\", \"paperclip\", \"iconColor\", \"var(--color-brand-primary)\", 3, \"iconSize\"], [1, \"uploaded-files\"], [\"class\", \"file-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"file-item\"], [1, \"file-name\"], [\"title\", \"Remove file\", 1, \"remove-file\", 3, \"click\"], [1, \"toggle-container\"], [\"class\", \"toggle-row\", 4, \"ngIf\"], [1, \"toggle-row\"], [\"size\", \"small\", 3, \"checkedChange\", \"checked\", \"title\", \"position\"]],\n      template: function AgentExecutionPlaygroundComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3, 0);\n          i0.ɵɵtemplate(3, AgentExecutionPlaygroundComponent_div_3_Template, 5, 12, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, AgentExecutionPlaygroundComponent_div_4_Template, 7, 0, \"div\", 5);\n          i0.ɵɵelementStart(5, \"div\", 6)(6, \"textarea\", 7);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function AgentExecutionPlaygroundComponent_Template_textarea_ngModelChange_6_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.inputText, $event) || (ctx.inputText = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"keydown.enter\", function AgentExecutionPlaygroundComponent_Template_textarea_keydown_enter_6_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            ctx.handleSendMessage();\n            return i0.ɵɵresetView($event.preventDefault());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, AgentExecutionPlaygroundComponent_button_7_Template, 2, 1, \"button\", 8);\n          i0.ɵɵelementStart(8, \"input\", 9, 1);\n          i0.ɵɵlistener(\"change\", function AgentExecutionPlaygroundComponent_Template_input_change_8_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onFileSelected($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(10, AgentExecutionPlaygroundComponent_div_10_Template, 2, 1, \"div\", 10);\n          i0.ɵɵelementStart(11, \"div\", 11)(12, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function AgentExecutionPlaygroundComponent_Template_button_click_12_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.handleSendMessage());\n          });\n          i0.ɵɵelement(13, \"ava-icon\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(14, AgentExecutionPlaygroundComponent_div_14_Template, 3, 2, \"div\", 14);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.messages)(\"ngForTrackBy\", ctx.trackByIndex);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.inputText);\n          i0.ɵɵproperty(\"disabled\", ctx.isDisabled || ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showFileUploadButton);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"accept\", ctx.acceptedFileType);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.filesUploadedData.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading || ctx.isDisabled);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"iconSize\", 16);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showChatInteractionToggles || ctx.showAiPrincipleToggle);\n        }\n      },\n      dependencies: [CommonModule, i1.NgClass, i1.NgForOf, i1.NgIf, FormsModule, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgModel, ReactiveFormsModule, ToggleComponent, IconComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.playground-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  font-family: \\\"Inter\\\", sans-serif;\\n  width: 100%;\\n  height: 78vh; \\n\\n  max-height: 100%; \\n\\n  overflow: hidden; \\n\\n}\\n@media (max-width: 1400px) {\\n  .playground-container[_ngcontent-%COMP%] {\\n    width: 60%;\\n  }\\n}\\n@media (max-width: 1200px) {\\n  .playground-container[_ngcontent-%COMP%] {\\n    width: 60%;\\n  }\\n}\\n\\n\\n\\n.button-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 8px 12px;\\n  height: 48px;\\n  flex-shrink: 0;\\n  border-bottom: 1px solid #eee;\\n  background-color: #ffffff;\\n}\\n\\n\\n\\n.dropdown-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 8px 12px;\\n  border: 1px solid #ccc;\\n  border-radius: 6px;\\n  background: white;\\n  color: #333;\\n  font-size: 14px;\\n  cursor: pointer;\\n  min-width: 100px;\\n}\\n\\n.dropdown-btn[_ngcontent-%COMP%]   .arrow-down[_ngcontent-%COMP%] {\\n  width: 10px;\\n  height: 6px;\\n  margin-left: 8px;\\n}\\n\\n\\n\\n.btn-menu[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n\\n\\n.menu-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  gap: 3px;\\n  cursor: pointer;\\n}\\n\\n.menu-icon[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  display: block;\\n  width: 3px;\\n  height: 3px;\\n  background: black;\\n  border-radius: 50%;\\n}\\n\\n\\n\\n.layout[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n  flex: 1; \\n\\n  padding: 16px;\\n  overflow-y: auto; \\n\\n  overflow-x: hidden; \\n\\n  background: #fff;\\n  align-items: stretch;\\n  justify-content: flex-start;\\n  min-height: 300px; \\n\\n  max-height: none; \\n\\n}\\n\\n\\n\\n.message[_ngcontent-%COMP%] {\\n  max-width: 60%;\\n  font-size: 14px;\\n  border-radius: 8px;\\n  padding: 16px 24px;\\n}\\n\\n.message-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  width: 100%;\\n}\\n\\n.message-row.ai[_ngcontent-%COMP%] {\\n  justify-content: flex-start;\\n}\\n\\n.message-row.user[_ngcontent-%COMP%] {\\n  justify-content: flex-end;\\n}\\n\\n\\n\\n.user-message[_ngcontent-%COMP%] {\\n  display: inline-flex; \\n\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: flex-start; \\n\\n  align-self: flex-end; \\n\\n  background: #c2c4cd;\\n  color: #333;\\n  border-radius: 8px;\\n  padding: 16px 24px;\\n  max-width: 60%;\\n  word-wrap: break-word;\\n  text-align: left;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.user-message[_ngcontent-%COMP%]:empty {\\n  background: none;\\n  padding: 0;\\n}\\n\\n\\n\\n.bot-response[_ngcontent-%COMP%] {\\n  align-self: flex-start;\\n  background: #f1f1f1; \\n\\n  color: #333;\\n  display: flex;\\n  padding: 16px 24px;\\n  border-radius: 8px;\\n  flex-direction: column;\\n  justify-content: center;\\n  gap: 10px;\\n  margin-bottom: 0.5rem;\\n  position: relative;\\n  padding-right: 40px;\\n}\\n\\n\\n\\n.status-message[_ngcontent-%COMP%] {\\n  align-self: flex-start;\\n  background: #f8fafc;\\n  color: #374151;\\n  display: flex;\\n  padding: 12px 16px;\\n  border-radius: 6px;\\n  flex-direction: column;\\n  justify-content: center;\\n  gap: 8px;\\n  margin-bottom: 0.5rem;\\n  position: relative;\\n  border: 1px solid #e5e7eb;\\n  font-size: 14px;\\n  font-weight: 500;\\n}\\n.status-message.success[_ngcontent-%COMP%] {\\n  background: rgba(16, 185, 129, 0.1);\\n  border-color: rgba(16, 185, 129, 0.2);\\n  color: #10b981;\\n}\\n.status-message.failed[_ngcontent-%COMP%] {\\n  background: rgba(239, 68, 68, 0.1);\\n  border-color: rgba(239, 68, 68, 0.2);\\n  color: #ef4444;\\n}\\n\\n.copy-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 16px;\\n  right: 16px;\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 4px;\\n  display: flex;\\n  align-items: center;\\n}\\n.copy-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  fill: var(--color-brand-primary, #144692);\\n  transition: fill 0.2s ease;\\n}\\n.copy-btn[_ngcontent-%COMP%]:hover   svg[_ngcontent-%COMP%] {\\n  fill: #1d4ed8;\\n}\\n\\n\\n\\n.loading-message[_ngcontent-%COMP%] {\\n  min-height: 20px;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n\\n\\n.typing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 50%;\\n  background-color: #666;\\n  animation: _ngcontent-%COMP%_typing 1.4s infinite ease-in-out;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%]:nth-child(1) {\\n  animation-delay: 0s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%]:nth-child(2) {\\n  animation-delay: 0.2s;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%]:nth-child(3) {\\n  animation-delay: 0.4s;\\n}\\n\\n@keyframes _ngcontent-%COMP%_typing {\\n  0%, 60%, 100% {\\n    transform: scale(1);\\n    opacity: 0.5;\\n  }\\n  30% {\\n    transform: scale(1.2);\\n    opacity: 1;\\n  }\\n}\\n\\n\\n.result-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  width: 186px;\\n  padding: 8px 12px;\\n  justify-content: center;\\n  align-items: center;\\n  border-radius: 8px;\\n  background: #f1f1f1;\\n  font-size: 14px;\\n  color: #333;\\n  margin-bottom: 0.5rem;\\n}\\n\\n\\n\\n\\n\\n.toggle-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 14px;\\n  padding: 8px 24px;\\n  font-family: \\\"Inter\\\", sans-serif;\\n  flex-shrink: 0; \\n\\n  height: 40px; \\n\\n  min-height: 40px; \\n\\n}\\n\\n\\n\\n.toggle-switch[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: inline-block;\\n  width: 40px;\\n  height: 20px;\\n}\\n\\n.toggle-switch[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  opacity: 0;\\n  width: 0;\\n  height: 0;\\n}\\n\\n.slider[_ngcontent-%COMP%] {\\n  position: absolute;\\n  cursor: pointer;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background-color: #ccc;\\n  transition: 0.4s;\\n  border-radius: 20px;\\n}\\n\\n.slider[_ngcontent-%COMP%]:before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  height: 14px;\\n  width: 14px;\\n  left: 3px;\\n  bottom: 3px;\\n  background-color: white;\\n  transition: 0.4s;\\n  border-radius: 50%;\\n}\\n\\n.toggle-switch[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked    + .slider[_ngcontent-%COMP%] {\\n  background-color: #4caf50;\\n}\\n\\n.toggle-switch[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked    + .slider[_ngcontent-%COMP%]:before {\\n  transform: translateX(20px);\\n}\\n\\n.toggle-label[_ngcontent-%COMP%] {\\n  color: #333;\\n}\\n\\n.dot-dropdown-menu[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 12px;\\n  top: 120px;\\n  background: white;\\n  border: 1px solid #ccc;\\n  border-radius: 6px;\\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);\\n  z-index: 10;\\n  display: flex;\\n  flex-direction: column;\\n  min-width: 120px;\\n}\\n\\n.dot-dropdown-menu[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  background: #f0f0f0;\\n}\\n\\n.dot-dropdown-menu[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-start;\\n  gap: 8px;\\n  padding: 10px 16px;\\n  border: none;\\n  background: none;\\n  text-align: left;\\n  cursor: pointer;\\n  font-size: 14px;\\n  color: #333;\\n  width: 100%;\\n  line-height: 1.2;\\n}\\n\\n.dot-dropdown-menu[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  display: block;\\n  height: 16px;\\n  width: 16px;\\n  vertical-align: middle;\\n}\\n\\n.dropdown-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: inline-block;\\n  width: 30%;\\n}\\n\\n\\n\\n.agent-name-display[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: inline-block;\\n  width: 30%;\\n}\\n.agent-name-display[_ngcontent-%COMP%]   .disabled-agent-name-input[_ngcontent-%COMP%]     .ava-textbox input {\\n  background-color: #f8f9fa !important;\\n  color: #6c757d !important;\\n  cursor: not-allowed !important;\\n  border-color: #e9ecef !important;\\n}\\n\\n.tool-dropdown-menu[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 100%; \\n\\n  left: 0;\\n  display: flex;\\n  flex-direction: column;\\n  background: white;\\n  border: 1px solid #ccc;\\n  border-radius: 6px;\\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);\\n  z-index: 10;\\n  min-width: 140px;\\n}\\n\\n\\n\\n.toggle-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  gap: 16px;\\n  align-items: center;\\n  padding: 12px 16px;\\n  border-top: 1px solid #e9ecef;\\n  background: #f8f9fa;\\n  min-height: 56px;\\n  flex-shrink: 0; \\n\\n  border-bottom-left-radius: 8px;\\n  border-bottom-right-radius: 8px;\\n}\\n\\n.toggle-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-start;\\n  min-height: 32px;\\n  white-space: nowrap;\\n}\\n\\n\\n\\n.uploaded-files[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 100%;\\n  left: 0;\\n  right: 0;\\n  background: white;\\n  border: 1px solid #e0e0e0;\\n  border-bottom: none;\\n  border-radius: 8px 8px 0 0;\\n  padding: 12px;\\n  max-height: 120px;\\n  overflow-y: auto;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n.file-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background: #f8f9fa;\\n  border: 1px solid #e9ecef;\\n  border-radius: 8px;\\n  padding: 12px 16px;\\n  font-size: 14px;\\n  min-height: 48px;\\n  transition: all 0.2s ease;\\n  position: relative;\\n}\\n.file-item[_ngcontent-%COMP%]:hover {\\n  background: #e9ecef;\\n  border-color: #ced4da;\\n}\\n\\n.file-name[_ngcontent-%COMP%] {\\n  flex: 1;\\n  font-weight: 500;\\n  color: #495057;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n  margin-right: 12px;\\n}\\n.file-name[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDCC4\\\";\\n  margin-right: 8px;\\n  font-size: 16px;\\n}\\n\\n.remove-file[_ngcontent-%COMP%] {\\n  background: #dc3545;\\n  border: none;\\n  color: white;\\n  cursor: pointer;\\n  font-size: 12px;\\n  line-height: 1;\\n  padding: 4px 8px;\\n  border-radius: 4px;\\n  font-weight: 500;\\n  transition: background-color 0.2s ease;\\n}\\n.remove-file[_ngcontent-%COMP%]:hover {\\n  background: #c82333;\\n}\\n.remove-file[_ngcontent-%COMP%]::after {\\n  content: \\\"Remove\\\";\\n}\\n\\n\\n\\n[_nghost-%COMP%]     .message-content .file-attachment-info {\\n  background: #f8f9fa;\\n  border: 1px solid #e9ecef;\\n  border-radius: 6px;\\n  padding: 8px 12px;\\n  margin-top: 8px;\\n  font-size: 12px;\\n  color: #6c757d;\\n}\\n[_nghost-%COMP%]     .message-content .file-attachment-info::before {\\n  content: \\\"\\uD83D\\uDCCE\\\";\\n  margin-right: 6px;\\n}\\n\\n.copied-toast[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: 24px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  background-color: #333;\\n  color: #fff;\\n  padding: 8px 16px;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  z-index: 1000;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25);\\n  animation: _ngcontent-%COMP%_fadeInOut 2s ease-in-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeInOut {\\n  0% {\\n    opacity: 0;\\n    transform: translateX(-50%) translateY(10px);\\n  }\\n  10%, 90% {\\n    opacity: 1;\\n    transform: translateX(-50%) translateY(0);\\n  }\\n  100% {\\n    opacity: 0;\\n    transform: translateX(-50%) translateY(-10px);\\n  }\\n}\\n.generating-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-start;\\n  align-items: center;\\n  padding: 5px 20px;\\n  background: var(--color-background-primary);\\n}\\n.generating-indicator[_ngcontent-%COMP%]   .generating-stepper[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.generating-indicator[_ngcontent-%COMP%]   .generating-stepper[_ngcontent-%COMP%]   .modern-loading-spinner[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n}\\n.generating-indicator[_ngcontent-%COMP%]   .generating-stepper[_ngcontent-%COMP%]   .modern-loading-spinner[_ngcontent-%COMP%]   .spinner-ring[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 20px;\\n  height: 20px;\\n  border-radius: 50%;\\n  border: 2.5px solid transparent;\\n  border-top-color: #6566cd;\\n  border-bottom-color: #0a0ee3;\\n  filter: drop-shadow(0 0 1px rgba(101, 102, 205, 0.3));\\n  animation: _ngcontent-%COMP%_spin-ring 1.5s ease-in-out infinite;\\n}\\n.generating-indicator[_ngcontent-%COMP%]   .generating-stepper[_ngcontent-%COMP%]   .modern-loading-spinner[_ngcontent-%COMP%]   .spinner-core[_ngcontent-%COMP%] {\\n  width: 10px;\\n  height: 10px;\\n  border-radius: 50%;\\n  background: linear-gradient(45deg, #4facfe 0%, #00f2fe 100%);\\n  box-shadow: 0 0 8px rgba(229, 10, 109, 0.5);\\n  animation: _ngcontent-%COMP%_pulse 1.5s ease-in-out infinite alternate;\\n}\\n.generating-indicator[_ngcontent-%COMP%]   .generating-stepper[_ngcontent-%COMP%]   .generating-text[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: black;\\n  font-weight: 500;\\n  position: relative;\\n  overflow: hidden;\\n  animation: _ngcontent-%COMP%_minimal-text-shine 1.5s ease-in-out infinite;\\n}\\n.generating-indicator.dark[_ngcontent-%COMP%]   .generating-stepper[_ngcontent-%COMP%]   .generating-text[_ngcontent-%COMP%] {\\n  color: var(--color-text-secondary-dark, #ccc);\\n  background: linear-gradient(90deg, var(--color-text-secondary-dark, #ccc) 25%, var(--code-viewer-bg) 50%, var(--color-text-secondary-dark, #ccc) 75%);\\n  background-size: 200% 100%;\\n  background-clip: text;\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin-ring {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    transform: scale(0.8);\\n    opacity: 0.7;\\n  }\\n  100% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_minimal-text-shine {\\n  0% {\\n    background-position: 100% 0;\\n  }\\n  100% {\\n    background-position: -100% 0;\\n  }\\n}\\n.input-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  background: #fff;\\n  border: 2px solid #03acc1;\\n  border-radius: 16px;\\n  padding: 12px;\\n  margin: 1rem;\\n  box-sizing: border-box;\\n  min-height: 80px;\\n}\\n\\n\\n\\n.input-container[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border: none;\\n  resize: none;\\n  background: transparent;\\n  font-size: 14px;\\n  font-family: \\\"Inter\\\", sans-serif;\\n  line-height: 1.4;\\n  outline: none;\\n  padding: 0;\\n  padding-right: 48px; \\n\\n  box-sizing: border-box;\\n  min-height: 3em; \\n\\n  max-height: 4.2em; \\n\\n  overflow-y: auto;\\n}\\n\\n.input-container[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]::placeholder {\\n  color: #999;\\n}\\n\\n\\n\\n.attach-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 8px;\\n  left: 12px;\\n  background: none;\\n  border: none;\\n  padding: 4px;\\n  cursor: pointer;\\n}\\n\\n.attach-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 18px;\\n  height: 18px;\\n  fill: #e91e63;\\n}\\n\\n\\n\\n.right-icons[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 8px;\\n  right: 12px;\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.right-icons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 4px;\\n}\\n\\n.right-icons[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 18px;\\n  height: 18px;\\n  fill: #e91e63;\\n}\\n\\n\\n\\n.agent-details-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  margin-right: 16px;\\n  padding: 12px 16px;\\n  background: var(--color-surface-secondary, #f8f9fa);\\n  border: 1px solid var(--color-border-primary, #e1e5e9);\\n  border-radius: 8px;\\n}\\n.agent-details-container[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%]   .agent-name[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: var(--color-text-primary, #1a1d21);\\n  margin: 0 0 4px 0;\\n}\\n.agent-details-container[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%]   .agent-description[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--color-text-secondary, #6c757d);\\n  margin: 0 0 8px 0;\\n  line-height: 1.4;\\n}\\n.agent-details-container[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%]   .agent-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n}\\n.agent-details-container[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%]   .agent-meta[_ngcontent-%COMP%]   .agent-role[_ngcontent-%COMP%], \\n.agent-details-container[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%]   .agent-meta[_ngcontent-%COMP%]   .agent-goal[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: var(--color-text-tertiary, #8e9297);\\n  font-weight: 500;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return AgentExecutionPlaygroundComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "EventEmitter", "FormsModule", "FormControl", "ReactiveFormsModule", "IconComponent", "ToggleComponent", "i0", "ɵɵelementStart", "ɵɵlistener", "AgentExecutionPlaygroundComponent_div_3_button_3_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "msg_r3", "ɵɵnextContext", "$implicit", "ctx_r3", "ɵɵresetView", "copyToClipboard", "text", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ɵɵtext", "ɵɵtemplate", "AgentExecutionPlaygroundComponent_div_3_button_3_Template", "AgentExecutionPlaygroundComponent_div_3_div_4_Template", "from", "ɵɵpureFunction6", "_c2", "includes", "showStatusOnly", "ɵɵtextInterpolate1", "showCopiedToast", "AgentExecutionPlaygroundComponent_button_7_Template_button_click_0_listener", "_r5", "fileInput_r6", "ɵɵreference", "click", "AgentExecutionPlaygroundComponent_div_10_div_1_Template_button_click_3_listener", "i_r8", "_r7", "index", "removeFile", "ɵɵtextInterpolate", "file_r9", "documentName", "AgentExecutionPlaygroundComponent_div_10_div_1_Template", "filesUploadedData", "AgentExecutionPlaygroundComponent_div_14_div_1_Template_ava_toggle_checkedChange_1_listener", "$event", "_r10", "onConversationalToggle", "isConvChecked", "AgentExecutionPlaygroundComponent_div_14_div_2_Template_ava_toggle_checkedChange_1_listener", "_r11", "onTemplateToggle", "isUseTemplate", "AgentExecutionPlaygroundComponent_div_14_div_1_Template", "AgentExecutionPlaygroundComponent_div_14_div_2_Template", "showChatInteractionToggles", "AgentExecutionPlaygroundComponent", "isMenuOpen", "isToolMenuOpen", "prompt<PERSON><PERSON>e", "promptOptions", "selected<PERSON><PERSON><PERSON>", "agentType", "showAiPrincipleToggle", "showDropdown", "showAgentNameInput", "agentNamePlaceholder", "displayedAgentName", "showFileUploadButton", "selected<PERSON><PERSON><PERSON>", "agentNameDisplayControl", "value", "disabled", "inputText", "previousMessages<PERSON><PERSON><PERSON>", "shouldScrollToBottom", "messages", "isLoading", "isDisabled", "<PERSON><PERSON><PERSON><PERSON>", "messageSent", "conversationalToggle", "templateToggle", "filesSelected", "messagesContainer", "fileInput", "showApprovalButton", "approvalRequested", "isMinimalView", "acceptedFileType", "ngOnInit", "setValue", "ngOnChanges", "changes", "currentValue", "undefined", "ngAfterViewChecked", "scrollToBottom", "nativeElement", "scrollTop", "scrollHeight", "err", "console", "error", "handleSendMessage", "trim", "messageText", "emit", "clearFiles", "toggleMenu", "onAiPrincipleToggle", "event", "log", "onPromptChange", "selectionData", "<PERSON><PERSON><PERSON>", "selectedOptions", "length", "name", "selectedOption", "find", "opt", "navigator", "clipboard", "writeText", "then", "setTimeout", "save", "export", "onClickOutside", "target", "closest", "onEnterKeydown", "shift<PERSON>ey", "preventDefault", "onFileSelected", "files", "i", "file", "toLowerCase", "endsWith", "alert", "push", "id", "Date", "now", "isImage", "type", "startsWith", "splice", "trackByIndex", "_item", "onApprovalClick", "selectors", "viewQuery", "AgentExecutionPlaygroundComponent_Query", "rf", "ctx", "AgentExecutionPlaygroundComponent_click_HostBindingHandler", "ɵɵresolveDocument", "AgentExecutionPlaygroundComponent_keydown_enter_HostBindingHandler", "AgentExecutionPlaygroundComponent_div_3_Template", "AgentExecutionPlaygroundComponent_div_4_Template", "ɵɵtwoWayListener", "AgentExecutionPlaygroundComponent_Template_textarea_ngModelChange_6_listener", "_r1", "ɵɵtwoWayBindingSet", "AgentExecutionPlaygroundComponent_Template_textarea_keydown_enter_6_listener", "AgentExecutionPlaygroundComponent_button_7_Template", "AgentExecutionPlaygroundComponent_Template_input_change_8_listener", "AgentExecutionPlaygroundComponent_div_10_Template", "AgentExecutionPlaygroundComponent_Template_button_click_12_listener", "AgentExecutionPlaygroundComponent_div_14_Template", "ɵɵtwoWayProperty", "i1", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\agents\\agent-execution\\components\\agent-execution-playground\\agent-execution-playground.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\pages\\agents\\agent-execution\\components\\agent-execution-playground\\agent-execution-playground.component.html"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport {\n  AfterViewChecked,\n  Component,\n  ElementRef,\n  EventEmitter,\n  HostListener,\n  Input,\n  OnChanges,\n  OnInit,\n  Output,\n  ViewChild,\n} from '@angular/core';\nimport { ChatMessage } from '../../../../../components/chat-window';\nimport { FormsModule, FormControl, ReactiveFormsModule } from '@angular/forms';\nimport {\n  IconComponent,\n  ToggleComponent,\n  DropdownOption,\n} from '@ava/play-comp-library';\n\ninterface Tool {\n  id: number;\n  name: string;\n}\n\n@Component({\n  selector: 'app-agent-execution-playground',\n  standalone: true,\n  imports: [\n    CommonModule,\n    FormsModule,\n    ReactiveFormsModule,\n    ToggleComponent,\n    IconComponent,\n  ],\n  templateUrl: './agent-execution-playground.component.html',\n  styleUrl: './agent-execution-playground.component.scss',\n})\nexport class AgentExecutionPlaygroundComponent\n  implements OnInit, OnChanges, AfterViewChecked\n{\n  isMenuOpen = false;\n  isToolMenuOpen = false;\n  @Output() promptChange = new EventEmitter<DropdownOption>();\n  @Input() promptOptions: DropdownOption[] = [];\n  @Input() selectedValue: string = 'default'; // Input for pre-selected value\n  @Input() agentType: string = 'individual'; // Input for agent type ('individual' or 'collaborative')\n  @Input() showChatInteractionToggles: boolean = false; // Input to show conversational and template toggles\n  @Input() showAiPrincipleToggle: boolean = false; // Input to show AI principle toggle\n  @Input() showDropdown: boolean = true; // Input to control dropdown visibility\n  @Input() showAgentNameInput: boolean = false; // Input to show disabled agent name input field\n  @Input() agentNamePlaceholder: string = 'Agent Name'; // Placeholder for agent name input\n  @Input() displayedAgentName: string = ''; // Agent name to display in disabled input\n  @Input() showFileUploadButton: boolean = false; // Controls visibility of attach file button\n  @Input() showStatusOnly: boolean = false; // Controls whether to show only status instead of full response\n  selectedPrompt: string = 'default';\n\n  // Form control for agent name display\n  agentNameDisplayControl = new FormControl({ value: '', disabled: true });\n\n  // Chat data\n  showCopiedToast = false;\n  inputText: string = '';\n  previousMessagesLength = 0;\n  shouldScrollToBottom = false;\n  @Input() messages: ChatMessage[] = [];\n  @Input() isLoading: boolean = false;\n  @Input() isDisabled: boolean = false;\n  @Input() showLoader: boolean = true;\n  @Output() messageSent = new EventEmitter<string>();\n  @Output() conversationalToggle = new EventEmitter<boolean>();\n  @Output() templateToggle = new EventEmitter<boolean>();\n  @Output() filesSelected = new EventEmitter<any[]>();\n  @ViewChild('messagesContainer') messagesContainer!: ElementRef;\n  @ViewChild('fileInput') fileInput!: ElementRef;\n  @Input() showApprovalButton: boolean = true;\n  @Output() approvalRequested = new EventEmitter<void>();\n  @Input() isMinimalView: boolean = false;\n\n  // Simple toggle properties for display only\n  public isConvChecked: boolean = true;\n  public isUseTemplate: boolean = false;\n\n  // File upload properties\n  public filesUploadedData: any[] = [];\n  @Input() acceptedFileType: string = '';\n\n  ngOnInit(): void {\n    this.messages = [\n      {\n        from: 'ai',\n        text: 'Hi there, how can I help you today?',\n      },\n    ];\n    this.shouldScrollToBottom = true;\n\n    // Set accepted file type based on agent type\n    if (this.agentType === 'collaborative') {\n      this.acceptedFileType = '.zip';\n    } else {\n      this.acceptedFileType = '.pdf,.doc,.docx,.txt,.csv,.xlsx,.xls,.ppt,.pptx,.png,.jpg,.jpeg,.gif,.bmp,.svg';\n    }\n\n    // Set selected prompt from input\n    if (this.selectedValue) {\n      this.selectedPrompt = this.selectedValue;\n      // Update displayed agent name if showing agent name input\n      if (this.showAgentNameInput && !this.displayedAgentName) {\n        this.displayedAgentName = this.selectedValue;\n        this.agentNameDisplayControl.setValue(this.selectedValue);\n      }\n    }\n\n    // Initialize agent name display control\n    if (this.displayedAgentName) {\n      this.agentNameDisplayControl.setValue(this.displayedAgentName);\n    }\n  }\n\n  ngOnChanges(changes: any): void {\n    // Update selectedPrompt when selectedValue input changes\n    if (changes['selectedValue'] && changes['selectedValue'].currentValue) {\n      // The selectedValue from parent should be the name (for dropdown display)\n      this.selectedPrompt = changes['selectedValue'].currentValue;\n    }\n\n    // Update agent name display control when displayedAgentName input changes\n    if (\n      changes['displayedAgentName'] &&\n      changes['displayedAgentName'].currentValue !== undefined\n    ) {\n      this.agentNameDisplayControl.setValue(\n        changes['displayedAgentName'].currentValue,\n      );\n    }\n\n    // Update accepted file type when agentType changes\n    if (changes['agentType'] && changes['agentType'].currentValue) {\n      if (changes['agentType'].currentValue === 'collaborative') {\n        this.acceptedFileType = '.zip';\n      } else {\n        this.acceptedFileType = '.pdf,.doc,.docx,.txt,.csv,.xlsx,.xls,.ppt,.pptx,.png,.jpg,.jpeg,.gif,.bmp,.svg';\n      }\n    }\n  }\n\n  ngAfterViewChecked() {\n    if (this.shouldScrollToBottom) {\n      this.scrollToBottom();\n      this.shouldScrollToBottom = false;\n    }\n  }\n\n  scrollToBottom(): void {\n    try {\n      if (this.messagesContainer && this.messagesContainer.nativeElement) {\n        // Scroll to bottom to show latest messages\n        this.messagesContainer.nativeElement.scrollTop =\n          this.messagesContainer.nativeElement.scrollHeight;\n      }\n    } catch (err) {\n      console.error('Error scrolling to bottom:', err);\n    }\n  }\n\n  handleSendMessage(): void {\n    if (!this.inputText.trim() || this.isDisabled) {\n      return;\n    }\n\n    // Add user message to the chat\n    this.messages = [\n      ...this.messages,\n      {\n        from: 'user',\n        text: this.inputText,\n      },\n    ];\n    this.shouldScrollToBottom = true;\n\n    // Emit the message to parent component\n    const messageText = this.inputText;\n    this.inputText = '';\n    this.messageSent.emit(messageText);\n\n    // Clear uploaded files after sending message\n    this.clearFiles();\n  }\n\n  private clearFiles(): void {\n    this.filesUploadedData = [];\n    this.filesSelected.emit(this.filesUploadedData);\n  }\n\n  toggleMenu() {\n    this.isMenuOpen = !this.isMenuOpen;\n  }\n\n  onAiPrincipleToggle(event: any) {\n    console.log('AI Principles toggle:', event);\n  }\n\n  onConversationalToggle(event: any) {\n    this.isConvChecked = event;\n\n    // If conversational is enabled, disable template\n    if (event && this.isUseTemplate) {\n      this.isUseTemplate = false;\n      this.templateToggle.emit(false);\n    }\n\n    console.log('Conversational mode:', event);\n    this.conversationalToggle.emit(event);\n  }\n\n  onTemplateToggle(event: any) {\n    this.isUseTemplate = event;\n\n    // If template is enabled, disable conversational\n    if (event && this.isConvChecked) {\n      this.isConvChecked = false;\n      this.conversationalToggle.emit(false);\n    }\n\n    console.log('Use template:', event);\n    this.templateToggle.emit(event);\n  }\n\n  onPromptChange(selectionData: any): void {\n    // The dropdown component emits an object with selectedOptions and selectedValue\n    // selectedValue contains the name of the selected option\n    let selectedName: string;\n\n    if (typeof selectionData === 'string') {\n      selectedName = selectionData;\n    } else if (selectionData && selectionData.selectedValue) {\n      selectedName = selectionData.selectedValue;\n    } else if (\n      selectionData &&\n      selectionData.selectedOptions &&\n      selectionData.selectedOptions.length > 0\n    ) {\n      selectedName = selectionData.selectedOptions[0].name;\n    } else {\n      return;\n    }\n\n    this.selectedPrompt = selectedName;\n\n    // Update displayed agent name if showing agent name input\n    if (this.showAgentNameInput) {\n      this.displayedAgentName = selectedName;\n      this.agentNameDisplayControl.setValue(selectedName);\n    }\n\n    // Find the option by name\n    const selectedOption = this.promptOptions.find(\n      (opt) => opt.name === selectedName,\n    );\n\n    if (selectedOption) {\n      this.promptChange.emit(selectedOption);\n    }\n  }\n\n  copyToClipboard(text: string): void {\n    navigator.clipboard.writeText(text).then(() => {\n      this.showCopiedToast = true;\n      setTimeout(() => {\n        this.showCopiedToast = false;\n      }, 2000);\n    });\n  }\n  save() {\n    this.isMenuOpen = false;\n    console.log('Save clicked');\n    // your save logic here\n  }\n\n  export() {\n    this.isMenuOpen = false;\n    console.log('Export clicked');\n    // your export logic here\n  }\n\n  // Hide menu when clicking outside\n  @HostListener('document:click', ['$event'])\n  onClickOutside(event: Event) {\n    const target = event.target as HTMLElement;\n    if (!target.closest('.btn-menu')) {\n      this.isMenuOpen = false;\n    }\n  }\n\n  @HostListener('keydown.enter', ['$event'])\n  onEnterKeydown(event: KeyboardEvent): void {\n    // Only prevent default and send if Shift key is not pressed\n    if (!event.shiftKey) {\n      event.preventDefault();\n      this.handleSendMessage();\n    }\n  }\n\n  // File upload methods\n  onFileSelected(event: any): void {\n    const files = event.target.files;\n    if (files && files.length > 0) {\n      this.filesUploadedData = [];\n\n      for (let i = 0; i < files.length; i++) {\n        const file = files[i];\n        \n        // Validate file type for collaborative agents\n        if (this.agentType === 'collaborative') {\n          if (!file.name.toLowerCase().endsWith('.zip')) {\n            alert('Only ZIP files are accepted for collaborative agents.');\n            continue; // Skip this file\n          }\n        }\n        \n        this.filesUploadedData.push({\n          id: `file_${Date.now()}_${i}`,\n          documentName: file.name,\n          isImage: file.type.startsWith('image/'),\n          file: file,\n        });\n      }\n\n      console.log('Files selected:', this.filesUploadedData);\n      this.filesSelected.emit(this.filesUploadedData);\n    }\n  }\n\n  removeFile(index: number): void {\n    this.filesUploadedData.splice(index, 1);\n    this.filesSelected.emit(this.filesUploadedData);\n  }\n\n  // Track by function for ngFor performance\n  trackByIndex(index: number, _item: any): number {\n    return index;\n  }\n\n  onApprovalClick() {\n    this.approvalRequested.emit();\n  }\n}\n", "<div class=\"playground-container\">\n  <!-- <div class=\"button-container\">\n    <!-- Agent Selection Dropdown -->\n  <!-- <div class=\"dropdown-container\" *ngIf=\"showDropdown\">\n      <ava-dropdown\n        *ngIf=\"!isMinimalView\"\n        dropdownTitle=\"Choose Agent\"\n        [options]=\"promptOptions\"\n        [selectedValue]=\"selectedPrompt\"\n        [enableSearch]=\"true\"\n        [singleSelect]=\"true\"\n        (selectionChange)=\"onPromptChange($event)\"\n      >\n      </ava-dropdown>\n    </div> -->\n\n  <!-- Disabled Agent Name Input Field -->\n  <!-- <div class=\"agent-name-display\" *ngIf=\"showAgentNameInput\">\n      <ava-textbox\n        [placeholder]=\"agentNamePlaceholder\"\n        [formControl]=\"agentNameDisplayControl\"\n        variant=\"default\"\n        size=\"md\"\n        class=\"disabled-agent-name-input\"\n      >\n      </ava-textbox>\n    </div> -->\n\n  <!-- <div class=\"btn-menu\">\n      <ava-button\n        *ngIf=\"showApprovalButton\"\n        label=\"Send for Approval\"\n        size=\"small\"\n        state=\"active\"\n        variant=\"primary\"\n        [customStyles]=\"{\n          background:\n            'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\n          '--button-effect-color': '33, 90, 214',\n        }\"\n        (userClick)=\"onApprovalClick()\"\n      ></ava-button>\n      <div class=\"menu-icon\" (click)=\"toggleMenu()\" #menuIcon>\n        <span></span>\n        <span></span>\n        <span></span>\n      </div>\n\n      <div class=\"dot-dropdown-menu\" *ngIf=\"isMenuOpen\">\n        <button (click)=\"save()\">\n          <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            width=\"24\"\n            height=\"24\"\n            fill=\"currentColor\"\n            viewBox=\"0 0 24 24\"\n          >\n            <path\n              d=\"M17 3H5a2 2 0 0 0-2 2v14l4-4h10a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2z\"\n            />\n          </svg>\n          Save\n        </button>\n\n        <button (click)=\"export()\">\n          <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            width=\"24\"\n            height=\"24\"\n            fill=\"currentColor\"\n            viewBox=\"0 0 24 24\"\n          >\n            <path d=\"M5 20h14v-2H5m14-9h-4V3H9v6H5l7 7 7-7z\" />\n          </svg>\n          Export\n        </button>\n      </div>\n    </div> -->\n  <!-- </div> -->\n  <div class=\"layout\" #messagesContainer>\n    <!-- Messages in normal order -->\n    <div\n      *ngFor=\"let msg of messages; trackBy: trackByIndex\"\n      class=\"message-row\"\n      [ngClass]=\"msg.from\"\n    >\n      <div\n        [ngClass]=\"{\n          'bot-response': msg.from === 'ai' && !(msg.text.includes('Status: Pending') || msg.text.includes('Status: Success') || msg.text.includes('Status: Failed')),\n          'user-message': msg.from === 'user',\n          'status-message': msg.from === 'ai' && showStatusOnly && (msg.text.includes('Status: Pending') || msg.text.includes('Status: Success') || msg.text.includes('Status: Failed')),\n          'success': msg.from === 'ai' && showStatusOnly && msg.text.includes('Status: Success'),\n          'failed': msg.from === 'ai' && showStatusOnly && msg.text.includes('Status: Failed'),\n          'pending': msg.from === 'ai' && showStatusOnly && msg.text.includes('Status: Pending')\n        }\"\n      >\n        {{ msg.text }}\n        <button\n          *ngIf=\"msg.from === 'ai' && !showStatusOnly && !(msg.text.includes('Status: Pending') || msg.text.includes('Status: Success') || msg.text.includes('Status: Failed'))\"\n          class=\"copy-btn\"\n          (click)=\"copyToClipboard(msg.text)\"\n          title=\"Copy\"\n        >\n          <ava-icon\n            slot=\"icon-start\"\n            iconName=\"copy\"\n            [iconSize]=\"16\"\n            iconColor=\"var(--color-brand-primary)\"\n          >\n          </ava-icon>\n        </button>\n      </div>\n      <div *ngIf=\"showCopiedToast\" class=\"copied-toast\">Copied!</div>\n    </div>\n\n    <!-- <div *ngIf=\"isLoading && showLoader && !messages[messages.length-1]?.text?.includes('Status:')\" class=\"message-row ai\">\n      <div class=\"bot-response loading-message\">\n        <div class=\"typing-indicator\">\n          <span class=\"dot\"></span>\n          <span class=\"dot\"></span>\n          <span class=\"dot\"></span>\n        </div>\n      </div>\n    </div> -->\n\n    <!-- Simple text loading indicator for tool testing -->\n    <!-- Removed as per user request -->\n  </div>\n\n  <!-- Generating text above prompt bar -->\n  <div *ngIf=\"isLoading\" class=\"generating-indicator\">\n    <div class=\"generating-stepper\">\n      <!-- Exact same spinner from stepper component -->\n      <div class=\"modern-loading-spinner\">\n        <div class=\"spinner-ring\"></div>\n        <div class=\"spinner-core\"></div>\n      </div>\n      <span class=\"generating-text\">Generating...</span>\n    </div>\n  </div>\n\n  <div class=\"input-container\">\n    <textarea\n      [(ngModel)]=\"inputText\"\n      [disabled]=\"isDisabled || isLoading\"\n      (keydown.enter)=\"handleSendMessage(); $event.preventDefault()\"\n      placeholder=\"Enter something to test\"\n    ></textarea>\n\n    <button\n      *ngIf=\"showFileUploadButton\"\n      class=\"attach-btn\"\n      title=\"Attach File\"\n      (click)=\"fileInput.click()\"\n    >\n      <ava-icon\n        slot=\"icon-start\"\n        iconName=\"paperclip\"\n        [iconSize]=\"16\"\n        iconColor=\"var(--color-brand-primary)\"\n      >\n      </ava-icon>\n    </button>\n\n    <!-- Hidden file input -->\n    <input\n      #fileInput\n      type=\"file\"\n      [accept]=\"acceptedFileType\"\n      multiple\n      style=\"display: none\"\n      (change)=\"onFileSelected($event)\"\n    />\n\n    <!-- Display uploaded files -->\n    <div class=\"uploaded-files\" *ngIf=\"filesUploadedData.length > 0\">\n      <div\n        class=\"file-item\"\n        *ngFor=\"let file of filesUploadedData; let i = index\"\n      >\n        <span class=\"file-name\">{{ file.documentName }}</span>\n        <button\n          class=\"remove-file\"\n          (click)=\"removeFile(i)\"\n          title=\"Remove file\"\n        ></button>\n      </div>\n    </div>\n\n    <div class=\"right-icons\">\n      <!-- <button class=\"edit-btn\" title=\"Edit\">\n     <ava-icon slot=\"icon-start\" iconName=\"wand-sparkles\" [iconSize]=\"16\" iconColor=\"var(--color-brand-primary)\">\n        </ava-icon>\n    </button> -->\n      <button\n        class=\"send-btn\"\n        title=\"Send\"\n        (click)=\"handleSendMessage()\"\n        [disabled]=\"isLoading || isDisabled\"\n      >\n        <ava-icon\n          slot=\"icon-start\"\n          iconName=\"send-horizontal\"\n          [iconSize]=\"16\"\n          iconColor=\"var(--color-brand-primary)\"\n        >\n        </ava-icon>\n      </button>\n    </div>\n  </div>\n\n  <!-- Toggles Container - All toggles in same line when present -->\n  <div\n    class=\"toggle-container\"\n    *ngIf=\"showChatInteractionToggles || showAiPrincipleToggle\"\n  >\n    <!-- Conversational Toggle -->\n    <div class=\"toggle-row\" *ngIf=\"showChatInteractionToggles\">\n      <ava-toggle\n        [checked]=\"isConvChecked\"\n        [title]=\"'Conversational'\"\n        [position]=\"'left'\"\n        size=\"small\"\n        (checkedChange)=\"onConversationalToggle($event)\"\n      >\n      </ava-toggle>\n    </div>\n\n    <!-- Use Template Toggle -->\n    <div class=\"toggle-row\" *ngIf=\"showChatInteractionToggles\">\n      <ava-toggle\n        [checked]=\"isUseTemplate\"\n        [title]=\"'Use Template'\"\n        [position]=\"'left'\"\n        size=\"small\"\n        (checkedChange)=\"onTemplateToggle($event)\"\n      >\n      </ava-toggle>\n    </div>\n\n    <!-- AI Principles Toggle -->\n    <!-- <div class=\"toggle-row\" *ngIf=\"showAiPrincipleToggle\">\n      <ava-toggle\n        [checked]=\"true\"\n        [title]=\"'AI Principles'\"\n        [position]=\"'left'\"\n        size=\"small\"\n        (checkedChange)=\"onAiPrincipleToggle($event)\">\n      </ava-toggle>\n    </div> -->\n  </div>\n</div>\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAIEC,YAAY,QAOP,eAAe;AAEtB,SAASC,WAAW,EAAEC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AAC9E,SACEC,aAAa,EACbC,eAAe,QAEV,wBAAwB;;;;;;;;;;;;;;;;;IC8EvBC,EAAA,CAAAC,cAAA,iBAKC;IAFCD,EAAA,CAAAE,UAAA,mBAAAC,kFAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASD,MAAA,CAAAE,eAAA,CAAAL,MAAA,CAAAM,IAAA,CAAyB;IAAA,EAAC;IAGnCZ,EAAA,CAAAa,SAAA,mBAMW;IACbb,EAAA,CAAAc,YAAA,EAAS;;;IAJLd,EAAA,CAAAe,SAAA,EAAe;IAAff,EAAA,CAAAgB,UAAA,gBAAe;;;;;IAMrBhB,EAAA,CAAAC,cAAA,cAAkD;IAAAD,EAAA,CAAAiB,MAAA,cAAO;IAAAjB,EAAA,CAAAc,YAAA,EAAM;;;;;IA1B/Dd,EALF,CAAAC,cAAA,cAIC,cAUE;IACCD,EAAA,CAAAiB,MAAA,GACA;IAAAjB,EAAA,CAAAkB,UAAA,IAAAC,yDAAA,qBAKC;IASHnB,EAAA,CAAAc,YAAA,EAAM;IACNd,EAAA,CAAAkB,UAAA,IAAAE,sDAAA,kBAAkD;IACpDpB,EAAA,CAAAc,YAAA,EAAM;;;;;IA7BJd,EAAA,CAAAgB,UAAA,YAAAV,MAAA,CAAAe,IAAA,CAAoB;IAGlBrB,EAAA,CAAAe,SAAA,EAOE;IAPFf,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAAsB,eAAA,IAAAC,GAAA,EAAAjB,MAAA,CAAAe,IAAA,eAAAf,MAAA,CAAAM,IAAA,CAAAY,QAAA,uBAAAlB,MAAA,CAAAM,IAAA,CAAAY,QAAA,uBAAAlB,MAAA,CAAAM,IAAA,CAAAY,QAAA,qBAAAlB,MAAA,CAAAe,IAAA,aAAAf,MAAA,CAAAe,IAAA,aAAAZ,MAAA,CAAAgB,cAAA,KAAAnB,MAAA,CAAAM,IAAA,CAAAY,QAAA,uBAAAlB,MAAA,CAAAM,IAAA,CAAAY,QAAA,uBAAAlB,MAAA,CAAAM,IAAA,CAAAY,QAAA,qBAAAlB,MAAA,CAAAe,IAAA,aAAAZ,MAAA,CAAAgB,cAAA,IAAAnB,MAAA,CAAAM,IAAA,CAAAY,QAAA,qBAAAlB,MAAA,CAAAe,IAAA,aAAAZ,MAAA,CAAAgB,cAAA,IAAAnB,MAAA,CAAAM,IAAA,CAAAY,QAAA,oBAAAlB,MAAA,CAAAe,IAAA,aAAAZ,MAAA,CAAAgB,cAAA,IAAAnB,MAAA,CAAAM,IAAA,CAAAY,QAAA,qBAOE;IAEFxB,EAAA,CAAAe,SAAA,EACA;IADAf,EAAA,CAAA0B,kBAAA,MAAApB,MAAA,CAAAM,IAAA,MACA;IACGZ,EAAA,CAAAe,SAAA,EAAoK;IAApKf,EAAA,CAAAgB,UAAA,SAAAV,MAAA,CAAAe,IAAA,cAAAZ,MAAA,CAAAgB,cAAA,MAAAnB,MAAA,CAAAM,IAAA,CAAAY,QAAA,uBAAAlB,MAAA,CAAAM,IAAA,CAAAY,QAAA,uBAAAlB,MAAA,CAAAM,IAAA,CAAAY,QAAA,oBAAoK;IAcnKxB,EAAA,CAAAe,SAAA,EAAqB;IAArBf,EAAA,CAAAgB,UAAA,SAAAP,MAAA,CAAAkB,eAAA,CAAqB;;;;;IAqB3B3B,EAHJ,CAAAC,cAAA,cAAoD,cAClB,cAEM;IAElCD,EADA,CAAAa,SAAA,cAAgC,cACA;IAClCb,EAAA,CAAAc,YAAA,EAAM;IACNd,EAAA,CAAAC,cAAA,eAA8B;IAAAD,EAAA,CAAAiB,MAAA,oBAAa;IAE/CjB,EAF+C,CAAAc,YAAA,EAAO,EAC9C,EACF;;;;;;IAUJd,EAAA,CAAAC,cAAA,iBAKC;IADCD,EAAA,CAAAE,UAAA,mBAAA0B,4EAAA;MAAA5B,EAAA,CAAAI,aAAA,CAAAyB,GAAA;MAAA7B,EAAA,CAAAO,aAAA;MAAA,MAAAuB,YAAA,GAAA9B,EAAA,CAAA+B,WAAA;MAAA,OAAA/B,EAAA,CAAAU,WAAA,CAASoB,YAAA,CAAAE,KAAA,EAAiB;IAAA,EAAC;IAE3BhC,EAAA,CAAAa,SAAA,mBAMW;IACbb,EAAA,CAAAc,YAAA,EAAS;;;IAJLd,EAAA,CAAAe,SAAA,EAAe;IAAff,EAAA,CAAAgB,UAAA,gBAAe;;;;;;IAsBfhB,EAJF,CAAAC,cAAA,cAGC,eACyB;IAAAD,EAAA,CAAAiB,MAAA,GAAuB;IAAAjB,EAAA,CAAAc,YAAA,EAAO;IACtDd,EAAA,CAAAC,cAAA,iBAIC;IAFCD,EAAA,CAAAE,UAAA,mBAAA+B,gFAAA;MAAA,MAAAC,IAAA,GAAAlC,EAAA,CAAAI,aAAA,CAAA+B,GAAA,EAAAC,KAAA;MAAA,MAAA3B,MAAA,GAAAT,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASD,MAAA,CAAA4B,UAAA,CAAAH,IAAA,CAAa;IAAA,EAAC;IAG3BlC,EADG,CAAAc,YAAA,EAAS,EACN;;;;IANoBd,EAAA,CAAAe,SAAA,GAAuB;IAAvBf,EAAA,CAAAsC,iBAAA,CAAAC,OAAA,CAAAC,YAAA,CAAuB;;;;;IALnDxC,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAkB,UAAA,IAAAuB,uDAAA,kBAGC;IAQHzC,EAAA,CAAAc,YAAA,EAAM;;;;IATed,EAAA,CAAAe,SAAA,EAAsB;IAAtBf,EAAA,CAAAgB,UAAA,YAAAP,MAAA,CAAAiC,iBAAA,CAAsB;;;;;;IAwCzC1C,EADF,CAAAC,cAAA,cAA2D,qBAOxD;IADCD,EAAA,CAAAE,UAAA,2BAAAyC,4FAAAC,MAAA;MAAA5C,EAAA,CAAAI,aAAA,CAAAyC,IAAA;MAAA,MAAApC,MAAA,GAAAT,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAiBD,MAAA,CAAAqC,sBAAA,CAAAF,MAAA,CAA8B;IAAA,EAAC;IAGpD5C,EADE,CAAAc,YAAA,EAAa,EACT;;;;IAPFd,EAAA,CAAAe,SAAA,EAAyB;IAEzBf,EAFA,CAAAgB,UAAA,YAAAP,MAAA,CAAAsC,aAAA,CAAyB,2BACC,oBACP;;;;;;IASrB/C,EADF,CAAAC,cAAA,cAA2D,qBAOxD;IADCD,EAAA,CAAAE,UAAA,2BAAA8C,4FAAAJ,MAAA;MAAA5C,EAAA,CAAAI,aAAA,CAAA6C,IAAA;MAAA,MAAAxC,MAAA,GAAAT,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAiBD,MAAA,CAAAyC,gBAAA,CAAAN,MAAA,CAAwB;IAAA,EAAC;IAG9C5C,EADE,CAAAc,YAAA,EAAa,EACT;;;;IAPFd,EAAA,CAAAe,SAAA,EAAyB;IAEzBf,EAFA,CAAAgB,UAAA,YAAAP,MAAA,CAAA0C,aAAA,CAAyB,yBACD,oBACL;;;;;IArBzBnD,EAAA,CAAAC,cAAA,cAGC;IAcCD,EAZA,CAAAkB,UAAA,IAAAkC,uDAAA,kBAA2D,IAAAC,uDAAA,kBAYA;IAqB7DrD,EAAA,CAAAc,YAAA,EAAM;;;;IAjCqBd,EAAA,CAAAe,SAAA,EAAgC;IAAhCf,EAAA,CAAAgB,UAAA,SAAAP,MAAA,CAAA6C,0BAAA,CAAgC;IAYhCtD,EAAA,CAAAe,SAAA,EAAgC;IAAhCf,EAAA,CAAAgB,UAAA,SAAAP,MAAA,CAAA6C,0BAAA,CAAgC;;;AD9L7D,WAAaC,iCAAiC;EAAxC,MAAOA,iCAAiC;IAG5CC,UAAU,GAAG,KAAK;IAClBC,cAAc,GAAG,KAAK;IACZC,YAAY,GAAG,IAAIhE,YAAY,EAAkB;IAClDiE,aAAa,GAAqB,EAAE;IACpCC,aAAa,GAAW,SAAS,CAAC,CAAC;IACnCC,SAAS,GAAW,YAAY,CAAC,CAAC;IAClCP,0BAA0B,GAAY,KAAK,CAAC,CAAC;IAC7CQ,qBAAqB,GAAY,KAAK,CAAC,CAAC;IACxCC,YAAY,GAAY,IAAI,CAAC,CAAC;IAC9BC,kBAAkB,GAAY,KAAK,CAAC,CAAC;IACrCC,oBAAoB,GAAW,YAAY,CAAC,CAAC;IAC7CC,kBAAkB,GAAW,EAAE,CAAC,CAAC;IACjCC,oBAAoB,GAAY,KAAK,CAAC,CAAC;IACvC1C,cAAc,GAAY,KAAK,CAAC,CAAC;IAC1C2C,cAAc,GAAW,SAAS;IAElC;IACAC,uBAAuB,GAAG,IAAIzE,WAAW,CAAC;MAAE0E,KAAK,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE,CAAC;IAExE;IACA5C,eAAe,GAAG,KAAK;IACvB6C,SAAS,GAAW,EAAE;IACtBC,sBAAsB,GAAG,CAAC;IAC1BC,oBAAoB,GAAG,KAAK;IACnBC,QAAQ,GAAkB,EAAE;IAC5BC,SAAS,GAAY,KAAK;IAC1BC,UAAU,GAAY,KAAK;IAC3BC,UAAU,GAAY,IAAI;IACzBC,WAAW,GAAG,IAAIrF,YAAY,EAAU;IACxCsF,oBAAoB,GAAG,IAAItF,YAAY,EAAW;IAClDuF,cAAc,GAAG,IAAIvF,YAAY,EAAW;IAC5CwF,aAAa,GAAG,IAAIxF,YAAY,EAAS;IACnByF,iBAAiB;IACzBC,SAAS;IACxBC,kBAAkB,GAAY,IAAI;IACjCC,iBAAiB,GAAG,IAAI5F,YAAY,EAAQ;IAC7C6F,aAAa,GAAY,KAAK;IAEvC;IACOxC,aAAa,GAAY,IAAI;IAC7BI,aAAa,GAAY,KAAK;IAErC;IACOT,iBAAiB,GAAU,EAAE;IAC3B8C,gBAAgB,GAAW,EAAE;IAEtCC,QAAQA,CAAA;MACN,IAAI,CAACd,QAAQ,GAAG,CACd;QACEtD,IAAI,EAAE,IAAI;QACVT,IAAI,EAAE;OACP,CACF;MACD,IAAI,CAAC8D,oBAAoB,GAAG,IAAI;MAEhC;MACA,IAAI,IAAI,CAACb,SAAS,KAAK,eAAe,EAAE;QACtC,IAAI,CAAC2B,gBAAgB,GAAG,MAAM;MAChC,CAAC,MAAM;QACL,IAAI,CAACA,gBAAgB,GAAG,gFAAgF;MAC1G;MAEA;MACA,IAAI,IAAI,CAAC5B,aAAa,EAAE;QACtB,IAAI,CAACQ,cAAc,GAAG,IAAI,CAACR,aAAa;QACxC;QACA,IAAI,IAAI,CAACI,kBAAkB,IAAI,CAAC,IAAI,CAACE,kBAAkB,EAAE;UACvD,IAAI,CAACA,kBAAkB,GAAG,IAAI,CAACN,aAAa;UAC5C,IAAI,CAACS,uBAAuB,CAACqB,QAAQ,CAAC,IAAI,CAAC9B,aAAa,CAAC;QAC3D;MACF;MAEA;MACA,IAAI,IAAI,CAACM,kBAAkB,EAAE;QAC3B,IAAI,CAACG,uBAAuB,CAACqB,QAAQ,CAAC,IAAI,CAACxB,kBAAkB,CAAC;MAChE;IACF;IAEAyB,WAAWA,CAACC,OAAY;MACtB;MACA,IAAIA,OAAO,CAAC,eAAe,CAAC,IAAIA,OAAO,CAAC,eAAe,CAAC,CAACC,YAAY,EAAE;QACrE;QACA,IAAI,CAACzB,cAAc,GAAGwB,OAAO,CAAC,eAAe,CAAC,CAACC,YAAY;MAC7D;MAEA;MACA,IACED,OAAO,CAAC,oBAAoB,CAAC,IAC7BA,OAAO,CAAC,oBAAoB,CAAC,CAACC,YAAY,KAAKC,SAAS,EACxD;QACA,IAAI,CAACzB,uBAAuB,CAACqB,QAAQ,CACnCE,OAAO,CAAC,oBAAoB,CAAC,CAACC,YAAY,CAC3C;MACH;MAEA;MACA,IAAID,OAAO,CAAC,WAAW,CAAC,IAAIA,OAAO,CAAC,WAAW,CAAC,CAACC,YAAY,EAAE;QAC7D,IAAID,OAAO,CAAC,WAAW,CAAC,CAACC,YAAY,KAAK,eAAe,EAAE;UACzD,IAAI,CAACL,gBAAgB,GAAG,MAAM;QAChC,CAAC,MAAM;UACL,IAAI,CAACA,gBAAgB,GAAG,gFAAgF;QAC1G;MACF;IACF;IAEAO,kBAAkBA,CAAA;MAChB,IAAI,IAAI,CAACrB,oBAAoB,EAAE;QAC7B,IAAI,CAACsB,cAAc,EAAE;QACrB,IAAI,CAACtB,oBAAoB,GAAG,KAAK;MACnC;IACF;IAEAsB,cAAcA,CAAA;MACZ,IAAI;QACF,IAAI,IAAI,CAACb,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,CAACc,aAAa,EAAE;UAClE;UACA,IAAI,CAACd,iBAAiB,CAACc,aAAa,CAACC,SAAS,GAC5C,IAAI,CAACf,iBAAiB,CAACc,aAAa,CAACE,YAAY;QACrD;MACF,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZC,OAAO,CAACC,KAAK,CAAC,4BAA4B,EAAEF,GAAG,CAAC;MAClD;IACF;IAEAG,iBAAiBA,CAAA;MACf,IAAI,CAAC,IAAI,CAAC/B,SAAS,CAACgC,IAAI,EAAE,IAAI,IAAI,CAAC3B,UAAU,EAAE;QAC7C;MACF;MAEA;MACA,IAAI,CAACF,QAAQ,GAAG,CACd,GAAG,IAAI,CAACA,QAAQ,EAChB;QACEtD,IAAI,EAAE,MAAM;QACZT,IAAI,EAAE,IAAI,CAAC4D;OACZ,CACF;MACD,IAAI,CAACE,oBAAoB,GAAG,IAAI;MAEhC;MACA,MAAM+B,WAAW,GAAG,IAAI,CAACjC,SAAS;MAClC,IAAI,CAACA,SAAS,GAAG,EAAE;MACnB,IAAI,CAACO,WAAW,CAAC2B,IAAI,CAACD,WAAW,CAAC;MAElC;MACA,IAAI,CAACE,UAAU,EAAE;IACnB;IAEQA,UAAUA,CAAA;MAChB,IAAI,CAACjE,iBAAiB,GAAG,EAAE;MAC3B,IAAI,CAACwC,aAAa,CAACwB,IAAI,CAAC,IAAI,CAAChE,iBAAiB,CAAC;IACjD;IAEAkE,UAAUA,CAAA;MACR,IAAI,CAACpD,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IACpC;IAEAqD,mBAAmBA,CAACC,KAAU;MAC5BT,OAAO,CAACU,GAAG,CAAC,uBAAuB,EAAED,KAAK,CAAC;IAC7C;IAEAhE,sBAAsBA,CAACgE,KAAU;MAC/B,IAAI,CAAC/D,aAAa,GAAG+D,KAAK;MAE1B;MACA,IAAIA,KAAK,IAAI,IAAI,CAAC3D,aAAa,EAAE;QAC/B,IAAI,CAACA,aAAa,GAAG,KAAK;QAC1B,IAAI,CAAC8B,cAAc,CAACyB,IAAI,CAAC,KAAK,CAAC;MACjC;MAEAL,OAAO,CAACU,GAAG,CAAC,sBAAsB,EAAED,KAAK,CAAC;MAC1C,IAAI,CAAC9B,oBAAoB,CAAC0B,IAAI,CAACI,KAAK,CAAC;IACvC;IAEA5D,gBAAgBA,CAAC4D,KAAU;MACzB,IAAI,CAAC3D,aAAa,GAAG2D,KAAK;MAE1B;MACA,IAAIA,KAAK,IAAI,IAAI,CAAC/D,aAAa,EAAE;QAC/B,IAAI,CAACA,aAAa,GAAG,KAAK;QAC1B,IAAI,CAACiC,oBAAoB,CAAC0B,IAAI,CAAC,KAAK,CAAC;MACvC;MAEAL,OAAO,CAACU,GAAG,CAAC,eAAe,EAAED,KAAK,CAAC;MACnC,IAAI,CAAC7B,cAAc,CAACyB,IAAI,CAACI,KAAK,CAAC;IACjC;IAEAE,cAAcA,CAACC,aAAkB;MAC/B;MACA;MACA,IAAIC,YAAoB;MAExB,IAAI,OAAOD,aAAa,KAAK,QAAQ,EAAE;QACrCC,YAAY,GAAGD,aAAa;MAC9B,CAAC,MAAM,IAAIA,aAAa,IAAIA,aAAa,CAACrD,aAAa,EAAE;QACvDsD,YAAY,GAAGD,aAAa,CAACrD,aAAa;MAC5C,CAAC,MAAM,IACLqD,aAAa,IACbA,aAAa,CAACE,eAAe,IAC7BF,aAAa,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EACxC;QACAF,YAAY,GAAGD,aAAa,CAACE,eAAe,CAAC,CAAC,CAAC,CAACE,IAAI;MACtD,CAAC,MAAM;QACL;MACF;MAEA,IAAI,CAACjD,cAAc,GAAG8C,YAAY;MAElC;MACA,IAAI,IAAI,CAAClD,kBAAkB,EAAE;QAC3B,IAAI,CAACE,kBAAkB,GAAGgD,YAAY;QACtC,IAAI,CAAC7C,uBAAuB,CAACqB,QAAQ,CAACwB,YAAY,CAAC;MACrD;MAEA;MACA,MAAMI,cAAc,GAAG,IAAI,CAAC3D,aAAa,CAAC4D,IAAI,CAC3CC,GAAG,IAAKA,GAAG,CAACH,IAAI,KAAKH,YAAY,CACnC;MAED,IAAII,cAAc,EAAE;QAClB,IAAI,CAAC5D,YAAY,CAACgD,IAAI,CAACY,cAAc,CAAC;MACxC;IACF;IAEA3G,eAAeA,CAACC,IAAY;MAC1B6G,SAAS,CAACC,SAAS,CAACC,SAAS,CAAC/G,IAAI,CAAC,CAACgH,IAAI,CAAC,MAAK;QAC5C,IAAI,CAACjG,eAAe,GAAG,IAAI;QAC3BkG,UAAU,CAAC,MAAK;UACd,IAAI,CAAClG,eAAe,GAAG,KAAK;QAC9B,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,CAAC;IACJ;IACAmG,IAAIA,CAAA;MACF,IAAI,CAACtE,UAAU,GAAG,KAAK;MACvB6C,OAAO,CAACU,GAAG,CAAC,cAAc,CAAC;MAC3B;IACF;IAEAgB,MAAMA,CAAA;MACJ,IAAI,CAACvE,UAAU,GAAG,KAAK;MACvB6C,OAAO,CAACU,GAAG,CAAC,gBAAgB,CAAC;MAC7B;IACF;IAEA;IAEAiB,cAAcA,CAAClB,KAAY;MACzB,MAAMmB,MAAM,GAAGnB,KAAK,CAACmB,MAAqB;MAC1C,IAAI,CAACA,MAAM,CAACC,OAAO,CAAC,WAAW,CAAC,EAAE;QAChC,IAAI,CAAC1E,UAAU,GAAG,KAAK;MACzB;IACF;IAGA2E,cAAcA,CAACrB,KAAoB;MACjC;MACA,IAAI,CAACA,KAAK,CAACsB,QAAQ,EAAE;QACnBtB,KAAK,CAACuB,cAAc,EAAE;QACtB,IAAI,CAAC9B,iBAAiB,EAAE;MAC1B;IACF;IAEA;IACA+B,cAAcA,CAACxB,KAAU;MACvB,MAAMyB,KAAK,GAAGzB,KAAK,CAACmB,MAAM,CAACM,KAAK;MAChC,IAAIA,KAAK,IAAIA,KAAK,CAACnB,MAAM,GAAG,CAAC,EAAE;QAC7B,IAAI,CAAC1E,iBAAiB,GAAG,EAAE;QAE3B,KAAK,IAAI8F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACnB,MAAM,EAAEoB,CAAC,EAAE,EAAE;UACrC,MAAMC,IAAI,GAAGF,KAAK,CAACC,CAAC,CAAC;UAErB;UACA,IAAI,IAAI,CAAC3E,SAAS,KAAK,eAAe,EAAE;YACtC,IAAI,CAAC4E,IAAI,CAACpB,IAAI,CAACqB,WAAW,EAAE,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;cAC7CC,KAAK,CAAC,uDAAuD,CAAC;cAC9D,SAAS,CAAC;YACZ;UACF;UAEA,IAAI,CAAClG,iBAAiB,CAACmG,IAAI,CAAC;YAC1BC,EAAE,EAAE,QAAQC,IAAI,CAACC,GAAG,EAAE,IAAIR,CAAC,EAAE;YAC7BhG,YAAY,EAAEiG,IAAI,CAACpB,IAAI;YACvB4B,OAAO,EAAER,IAAI,CAACS,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC;YACvCV,IAAI,EAAEA;WACP,CAAC;QACJ;QAEApC,OAAO,CAACU,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAACrE,iBAAiB,CAAC;QACtD,IAAI,CAACwC,aAAa,CAACwB,IAAI,CAAC,IAAI,CAAChE,iBAAiB,CAAC;MACjD;IACF;IAEAL,UAAUA,CAACD,KAAa;MACtB,IAAI,CAACM,iBAAiB,CAAC0G,MAAM,CAAChH,KAAK,EAAE,CAAC,CAAC;MACvC,IAAI,CAAC8C,aAAa,CAACwB,IAAI,CAAC,IAAI,CAAChE,iBAAiB,CAAC;IACjD;IAEA;IACA2G,YAAYA,CAACjH,KAAa,EAAEkH,KAAU;MACpC,OAAOlH,KAAK;IACd;IAEAmH,eAAeA,CAAA;MACb,IAAI,CAACjE,iBAAiB,CAACoB,IAAI,EAAE;IAC/B;;uCAnTWnD,iCAAiC;IAAA;;YAAjCA,iCAAiC;MAAAiG,SAAA;MAAAC,SAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;UAAjC3J,EAAA,CAAAE,UAAA,mBAAA2J,2DAAAjH,MAAA;YAAA,OAAAgH,GAAA,CAAA5B,cAAA,CAAApF,MAAA,CAAsB;UAAA,UAAA5C,EAAA,CAAA8J,iBAAA,CAAW,2BAAAC,mEAAAnH,MAAA;YAAA,OAAjCgH,GAAA,CAAAzB,cAAA,CAAAvF,MAAA,CAAsB;UAAA,EAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UCwC5C5C,EA/EF,CAAAC,cAAA,aAAkC,gBA+EO;UAErCD,EAAA,CAAAkB,UAAA,IAAA8I,gDAAA,kBAIC;UA0CHhK,EAAA,CAAAc,YAAA,EAAM;UAGNd,EAAA,CAAAkB,UAAA,IAAA+I,gDAAA,iBAAoD;UAYlDjK,EADF,CAAAC,cAAA,aAA6B,kBAM1B;UAJCD,EAAA,CAAAkK,gBAAA,2BAAAC,6EAAAvH,MAAA;YAAA5C,EAAA,CAAAI,aAAA,CAAAgK,GAAA;YAAApK,EAAA,CAAAqK,kBAAA,CAAAT,GAAA,CAAApF,SAAA,EAAA5B,MAAA,MAAAgH,GAAA,CAAApF,SAAA,GAAA5B,MAAA;YAAA,OAAA5C,EAAA,CAAAU,WAAA,CAAAkC,MAAA;UAAA,EAAuB;UAEvB5C,EAAA,CAAAE,UAAA,2BAAAoK,6EAAA1H,MAAA;YAAA5C,EAAA,CAAAI,aAAA,CAAAgK,GAAA;YAAiBR,GAAA,CAAArD,iBAAA,EAAmB;YAAA,OAAAvG,EAAA,CAAAU,WAAA,CAAEkC,MAAA,CAAAyF,cAAA,EAAuB;UAAA,EAAC;UAE/DrI,EAAA,CAAAc,YAAA,EAAW;UAEZd,EAAA,CAAAkB,UAAA,IAAAqJ,mDAAA,oBAKC;UAWDvK,EAAA,CAAAC,cAAA,kBAOE;UADAD,EAAA,CAAAE,UAAA,oBAAAsK,mEAAA5H,MAAA;YAAA5C,EAAA,CAAAI,aAAA,CAAAgK,GAAA;YAAA,OAAApK,EAAA,CAAAU,WAAA,CAAUkJ,GAAA,CAAAtB,cAAA,CAAA1F,MAAA,CAAsB;UAAA,EAAC;UANnC5C,EAAA,CAAAc,YAAA,EAOE;UAGFd,EAAA,CAAAkB,UAAA,KAAAuJ,iDAAA,kBAAiE;UAmB/DzK,EALF,CAAAC,cAAA,eAAyB,kBAUtB;UAFCD,EAAA,CAAAE,UAAA,mBAAAwK,oEAAA;YAAA1K,EAAA,CAAAI,aAAA,CAAAgK,GAAA;YAAA,OAAApK,EAAA,CAAAU,WAAA,CAASkJ,GAAA,CAAArD,iBAAA,EAAmB;UAAA,EAAC;UAG7BvG,EAAA,CAAAa,SAAA,oBAMW;UAGjBb,EAFI,CAAAc,YAAA,EAAS,EACL,EACF;UAGNd,EAAA,CAAAkB,UAAA,KAAAyJ,iDAAA,kBAGC;UAoCH3K,EAAA,CAAAc,YAAA,EAAM;;;UAzKgBd,EAAA,CAAAe,SAAA,GAAa;UAAAf,EAAb,CAAAgB,UAAA,YAAA4I,GAAA,CAAAjF,QAAA,CAAa,iBAAAiF,GAAA,CAAAP,YAAA,CAAqB;UAgDhDrJ,EAAA,CAAAe,SAAA,EAAe;UAAff,EAAA,CAAAgB,UAAA,SAAA4I,GAAA,CAAAhF,SAAA,CAAe;UAajB5E,EAAA,CAAAe,SAAA,GAAuB;UAAvBf,EAAA,CAAA4K,gBAAA,YAAAhB,GAAA,CAAApF,SAAA,CAAuB;UACvBxE,EAAA,CAAAgB,UAAA,aAAA4I,GAAA,CAAA/E,UAAA,IAAA+E,GAAA,CAAAhF,SAAA,CAAoC;UAMnC5E,EAAA,CAAAe,SAAA,EAA0B;UAA1Bf,EAAA,CAAAgB,UAAA,SAAA4I,GAAA,CAAAzF,oBAAA,CAA0B;UAkB3BnE,EAAA,CAAAe,SAAA,EAA2B;UAA3Bf,EAAA,CAAAgB,UAAA,WAAA4I,GAAA,CAAApE,gBAAA,CAA2B;UAOAxF,EAAA,CAAAe,SAAA,GAAkC;UAAlCf,EAAA,CAAAgB,UAAA,SAAA4I,GAAA,CAAAlH,iBAAA,CAAA0E,MAAA,KAAkC;UAuB3DpH,EAAA,CAAAe,SAAA,GAAoC;UAApCf,EAAA,CAAAgB,UAAA,aAAA4I,GAAA,CAAAhF,SAAA,IAAAgF,GAAA,CAAA/E,UAAA,CAAoC;UAKlC7E,EAAA,CAAAe,SAAA,EAAe;UAAff,EAAA,CAAAgB,UAAA,gBAAe;UAWpBhB,EAAA,CAAAe,SAAA,EAAyD;UAAzDf,EAAA,CAAAgB,UAAA,SAAA4I,GAAA,CAAAtG,0BAAA,IAAAsG,GAAA,CAAA9F,qBAAA,CAAyD;;;qBDxL1DrE,YAAY,EAAAoL,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EACZrL,WAAW,EAAAsL,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EACXvL,mBAAmB,EACnBE,eAAe,EACfD,aAAa;MAAAuL,MAAA;IAAA;;SAKJ9H,iCAAiC;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}