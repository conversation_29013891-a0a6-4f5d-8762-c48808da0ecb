{"ast": null, "code": "import defaultSource from \"./defaultSource.js\";\nimport gamma from \"./gamma.js\";\nexport default (function sourceRandomBeta(source) {\n  var G = gamma.source(source);\n  function randomBeta(alpha, beta) {\n    var X = G(alpha),\n      Y = G(beta);\n    return function () {\n      var x = X();\n      return x === 0 ? 0 : x / (x + Y());\n    };\n  }\n  randomBeta.source = sourceRandomBeta;\n  return randomBeta;\n})(defaultSource);", "map": {"version": 3, "names": ["defaultSource", "gamma", "sourceRandomBeta", "source", "G", "randomBeta", "alpha", "beta", "X", "Y", "x"], "sources": ["C:/console/aava-ui-web/node_modules/d3-random/src/beta.js"], "sourcesContent": ["import defaultSource from \"./defaultSource.js\";\nimport gamma from \"./gamma.js\";\n\nexport default (function sourceRandomBeta(source) {\n  var G = gamma.source(source);\n\n  function randomBeta(alpha, beta) {\n    var X = G(alpha),\n        Y = G(beta);\n    return function() {\n      var x = X();\n      return x === 0 ? 0 : x / (x + Y());\n    };\n  }\n\n  randomBeta.source = sourceRandomBeta;\n\n  return randomBeta;\n})(defaultSource);\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,KAAK,MAAM,YAAY;AAE9B,eAAe,CAAC,SAASC,gBAAgBA,CAACC,MAAM,EAAE;EAChD,IAAIC,CAAC,GAAGH,KAAK,CAACE,MAAM,CAACA,MAAM,CAAC;EAE5B,SAASE,UAAUA,CAACC,KAAK,EAAEC,IAAI,EAAE;IAC/B,IAAIC,CAAC,GAAGJ,CAAC,CAACE,KAAK,CAAC;MACZG,CAAC,GAAGL,CAAC,CAACG,IAAI,CAAC;IACf,OAAO,YAAW;MAChB,IAAIG,CAAC,GAAGF,CAAC,CAAC,CAAC;MACX,OAAOE,CAAC,KAAK,CAAC,GAAG,CAAC,GAAGA,CAAC,IAAIA,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC;EACH;EAEAJ,UAAU,CAACF,MAAM,GAAGD,gBAAgB;EAEpC,OAAOG,UAAU;AACnB,CAAC,EAAEL,aAAa,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}