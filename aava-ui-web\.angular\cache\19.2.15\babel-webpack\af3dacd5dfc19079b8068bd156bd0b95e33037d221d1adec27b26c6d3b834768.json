{"ast": null, "code": "import defaultSource from \"./defaultSource.js\";\nexport default (function sourceRandomInt(source) {\n  function randomInt(min, max) {\n    if (arguments.length < 2) max = min, min = 0;\n    min = Math.floor(min);\n    max = Math.floor(max) - min;\n    return function () {\n      return Math.floor(source() * max + min);\n    };\n  }\n  randomInt.source = sourceRandomInt;\n  return randomInt;\n})(defaultSource);", "map": {"version": 3, "names": ["defaultSource", "sourceRandomInt", "source", "randomInt", "min", "max", "arguments", "length", "Math", "floor"], "sources": ["C:/console/aava-ui-web/node_modules/d3-random/src/int.js"], "sourcesContent": ["import defaultSource from \"./defaultSource.js\";\n\nexport default (function sourceRandomInt(source) {\n  function randomInt(min, max) {\n    if (arguments.length < 2) max = min, min = 0;\n    min = Math.floor(min);\n    max = Math.floor(max) - min;\n    return function() {\n      return Math.floor(source() * max + min);\n    };\n  }\n\n  randomInt.source = sourceRandomInt;\n\n  return randomInt;\n})(defaultSource);\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,oBAAoB;AAE9C,eAAe,CAAC,SAASC,eAAeA,CAACC,MAAM,EAAE;EAC/C,SAASC,SAASA,CAACC,GAAG,EAAEC,GAAG,EAAE;IAC3B,IAAIC,SAAS,CAACC,MAAM,GAAG,CAAC,EAAEF,GAAG,GAAGD,GAAG,EAAEA,GAAG,GAAG,CAAC;IAC5CA,GAAG,GAAGI,IAAI,CAACC,KAAK,CAACL,GAAG,CAAC;IACrBC,GAAG,GAAGG,IAAI,CAACC,KAAK,CAACJ,GAAG,CAAC,GAAGD,GAAG;IAC3B,OAAO,YAAW;MAChB,OAAOI,IAAI,CAACC,KAAK,CAACP,MAAM,CAAC,CAAC,GAAGG,GAAG,GAAGD,GAAG,CAAC;IACzC,CAAC;EACH;EAEAD,SAAS,CAACD,MAAM,GAAGD,eAAe;EAElC,OAAOE,SAAS;AAClB,CAAC,EAAEH,aAAa,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}