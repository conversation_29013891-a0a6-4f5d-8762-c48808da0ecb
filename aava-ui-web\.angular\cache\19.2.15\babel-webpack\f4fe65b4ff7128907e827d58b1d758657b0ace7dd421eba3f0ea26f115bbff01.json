{"ast": null, "code": "import { dispatch } from \"d3-dispatch\";\nimport { dragDisable, dragEnable } from \"d3-drag\";\nimport { interpolate } from \"d3-interpolate\";\nimport { pointer, select } from \"d3-selection\";\nimport { interrupt } from \"d3-transition\";\nimport constant from \"./constant.js\";\nimport BrushEvent from \"./event.js\";\nimport noevent, { nopropagation } from \"./noevent.js\";\nvar MODE_DRAG = {\n    name: \"drag\"\n  },\n  MODE_SPACE = {\n    name: \"space\"\n  },\n  MODE_HANDLE = {\n    name: \"handle\"\n  },\n  MODE_CENTER = {\n    name: \"center\"\n  };\nconst {\n  abs,\n  max,\n  min\n} = Math;\nfunction number1(e) {\n  return [+e[0], +e[1]];\n}\nfunction number2(e) {\n  return [number1(e[0]), number1(e[1])];\n}\nvar X = {\n  name: \"x\",\n  handles: [\"w\", \"e\"].map(type),\n  input: function (x, e) {\n    return x == null ? null : [[+x[0], e[0][1]], [+x[1], e[1][1]]];\n  },\n  output: function (xy) {\n    return xy && [xy[0][0], xy[1][0]];\n  }\n};\nvar Y = {\n  name: \"y\",\n  handles: [\"n\", \"s\"].map(type),\n  input: function (y, e) {\n    return y == null ? null : [[e[0][0], +y[0]], [e[1][0], +y[1]]];\n  },\n  output: function (xy) {\n    return xy && [xy[0][1], xy[1][1]];\n  }\n};\nvar XY = {\n  name: \"xy\",\n  handles: [\"n\", \"w\", \"e\", \"s\", \"nw\", \"ne\", \"sw\", \"se\"].map(type),\n  input: function (xy) {\n    return xy == null ? null : number2(xy);\n  },\n  output: function (xy) {\n    return xy;\n  }\n};\nvar cursors = {\n  overlay: \"crosshair\",\n  selection: \"move\",\n  n: \"ns-resize\",\n  e: \"ew-resize\",\n  s: \"ns-resize\",\n  w: \"ew-resize\",\n  nw: \"nwse-resize\",\n  ne: \"nesw-resize\",\n  se: \"nwse-resize\",\n  sw: \"nesw-resize\"\n};\nvar flipX = {\n  e: \"w\",\n  w: \"e\",\n  nw: \"ne\",\n  ne: \"nw\",\n  se: \"sw\",\n  sw: \"se\"\n};\nvar flipY = {\n  n: \"s\",\n  s: \"n\",\n  nw: \"sw\",\n  ne: \"se\",\n  se: \"ne\",\n  sw: \"nw\"\n};\nvar signsX = {\n  overlay: +1,\n  selection: +1,\n  n: null,\n  e: +1,\n  s: null,\n  w: -1,\n  nw: -1,\n  ne: +1,\n  se: +1,\n  sw: -1\n};\nvar signsY = {\n  overlay: +1,\n  selection: +1,\n  n: -1,\n  e: null,\n  s: +1,\n  w: null,\n  nw: -1,\n  ne: -1,\n  se: +1,\n  sw: +1\n};\nfunction type(t) {\n  return {\n    type: t\n  };\n}\n\n// Ignore right-click, since that should open the context menu.\nfunction defaultFilter(event) {\n  return !event.ctrlKey && !event.button;\n}\nfunction defaultExtent() {\n  var svg = this.ownerSVGElement || this;\n  if (svg.hasAttribute(\"viewBox\")) {\n    svg = svg.viewBox.baseVal;\n    return [[svg.x, svg.y], [svg.x + svg.width, svg.y + svg.height]];\n  }\n  return [[0, 0], [svg.width.baseVal.value, svg.height.baseVal.value]];\n}\nfunction defaultTouchable() {\n  return navigator.maxTouchPoints || \"ontouchstart\" in this;\n}\n\n// Like d3.local, but with the name “__brush” rather than auto-generated.\nfunction local(node) {\n  while (!node.__brush) if (!(node = node.parentNode)) return;\n  return node.__brush;\n}\nfunction empty(extent) {\n  return extent[0][0] === extent[1][0] || extent[0][1] === extent[1][1];\n}\nexport function brushSelection(node) {\n  var state = node.__brush;\n  return state ? state.dim.output(state.selection) : null;\n}\nexport function brushX() {\n  return brush(X);\n}\nexport function brushY() {\n  return brush(Y);\n}\nexport default function () {\n  return brush(XY);\n}\nfunction brush(dim) {\n  var extent = defaultExtent,\n    filter = defaultFilter,\n    touchable = defaultTouchable,\n    keys = true,\n    listeners = dispatch(\"start\", \"brush\", \"end\"),\n    handleSize = 6,\n    touchending;\n  function brush(group) {\n    var overlay = group.property(\"__brush\", initialize).selectAll(\".overlay\").data([type(\"overlay\")]);\n    overlay.enter().append(\"rect\").attr(\"class\", \"overlay\").attr(\"pointer-events\", \"all\").attr(\"cursor\", cursors.overlay).merge(overlay).each(function () {\n      var extent = local(this).extent;\n      select(this).attr(\"x\", extent[0][0]).attr(\"y\", extent[0][1]).attr(\"width\", extent[1][0] - extent[0][0]).attr(\"height\", extent[1][1] - extent[0][1]);\n    });\n    group.selectAll(\".selection\").data([type(\"selection\")]).enter().append(\"rect\").attr(\"class\", \"selection\").attr(\"cursor\", cursors.selection).attr(\"fill\", \"#777\").attr(\"fill-opacity\", 0.3).attr(\"stroke\", \"#fff\").attr(\"shape-rendering\", \"crispEdges\");\n    var handle = group.selectAll(\".handle\").data(dim.handles, function (d) {\n      return d.type;\n    });\n    handle.exit().remove();\n    handle.enter().append(\"rect\").attr(\"class\", function (d) {\n      return \"handle handle--\" + d.type;\n    }).attr(\"cursor\", function (d) {\n      return cursors[d.type];\n    });\n    group.each(redraw).attr(\"fill\", \"none\").attr(\"pointer-events\", \"all\").on(\"mousedown.brush\", started).filter(touchable).on(\"touchstart.brush\", started).on(\"touchmove.brush\", touchmoved).on(\"touchend.brush touchcancel.brush\", touchended).style(\"touch-action\", \"none\").style(\"-webkit-tap-highlight-color\", \"rgba(0,0,0,0)\");\n  }\n  brush.move = function (group, selection, event) {\n    if (group.tween) {\n      group.on(\"start.brush\", function (event) {\n        emitter(this, arguments).beforestart().start(event);\n      }).on(\"interrupt.brush end.brush\", function (event) {\n        emitter(this, arguments).end(event);\n      }).tween(\"brush\", function () {\n        var that = this,\n          state = that.__brush,\n          emit = emitter(that, arguments),\n          selection0 = state.selection,\n          selection1 = dim.input(typeof selection === \"function\" ? selection.apply(this, arguments) : selection, state.extent),\n          i = interpolate(selection0, selection1);\n        function tween(t) {\n          state.selection = t === 1 && selection1 === null ? null : i(t);\n          redraw.call(that);\n          emit.brush();\n        }\n        return selection0 !== null && selection1 !== null ? tween : tween(1);\n      });\n    } else {\n      group.each(function () {\n        var that = this,\n          args = arguments,\n          state = that.__brush,\n          selection1 = dim.input(typeof selection === \"function\" ? selection.apply(that, args) : selection, state.extent),\n          emit = emitter(that, args).beforestart();\n        interrupt(that);\n        state.selection = selection1 === null ? null : selection1;\n        redraw.call(that);\n        emit.start(event).brush(event).end(event);\n      });\n    }\n  };\n  brush.clear = function (group, event) {\n    brush.move(group, null, event);\n  };\n  function redraw() {\n    var group = select(this),\n      selection = local(this).selection;\n    if (selection) {\n      group.selectAll(\".selection\").style(\"display\", null).attr(\"x\", selection[0][0]).attr(\"y\", selection[0][1]).attr(\"width\", selection[1][0] - selection[0][0]).attr(\"height\", selection[1][1] - selection[0][1]);\n      group.selectAll(\".handle\").style(\"display\", null).attr(\"x\", function (d) {\n        return d.type[d.type.length - 1] === \"e\" ? selection[1][0] - handleSize / 2 : selection[0][0] - handleSize / 2;\n      }).attr(\"y\", function (d) {\n        return d.type[0] === \"s\" ? selection[1][1] - handleSize / 2 : selection[0][1] - handleSize / 2;\n      }).attr(\"width\", function (d) {\n        return d.type === \"n\" || d.type === \"s\" ? selection[1][0] - selection[0][0] + handleSize : handleSize;\n      }).attr(\"height\", function (d) {\n        return d.type === \"e\" || d.type === \"w\" ? selection[1][1] - selection[0][1] + handleSize : handleSize;\n      });\n    } else {\n      group.selectAll(\".selection,.handle\").style(\"display\", \"none\").attr(\"x\", null).attr(\"y\", null).attr(\"width\", null).attr(\"height\", null);\n    }\n  }\n  function emitter(that, args, clean) {\n    var emit = that.__brush.emitter;\n    return emit && (!clean || !emit.clean) ? emit : new Emitter(that, args, clean);\n  }\n  function Emitter(that, args, clean) {\n    this.that = that;\n    this.args = args;\n    this.state = that.__brush;\n    this.active = 0;\n    this.clean = clean;\n  }\n  Emitter.prototype = {\n    beforestart: function () {\n      if (++this.active === 1) this.state.emitter = this, this.starting = true;\n      return this;\n    },\n    start: function (event, mode) {\n      if (this.starting) this.starting = false, this.emit(\"start\", event, mode);else this.emit(\"brush\", event);\n      return this;\n    },\n    brush: function (event, mode) {\n      this.emit(\"brush\", event, mode);\n      return this;\n    },\n    end: function (event, mode) {\n      if (--this.active === 0) delete this.state.emitter, this.emit(\"end\", event, mode);\n      return this;\n    },\n    emit: function (type, event, mode) {\n      var d = select(this.that).datum();\n      listeners.call(type, this.that, new BrushEvent(type, {\n        sourceEvent: event,\n        target: brush,\n        selection: dim.output(this.state.selection),\n        mode,\n        dispatch: listeners\n      }), d);\n    }\n  };\n  function started(event) {\n    if (touchending && !event.touches) return;\n    if (!filter.apply(this, arguments)) return;\n    var that = this,\n      type = event.target.__data__.type,\n      mode = (keys && event.metaKey ? type = \"overlay\" : type) === \"selection\" ? MODE_DRAG : keys && event.altKey ? MODE_CENTER : MODE_HANDLE,\n      signX = dim === Y ? null : signsX[type],\n      signY = dim === X ? null : signsY[type],\n      state = local(that),\n      extent = state.extent,\n      selection = state.selection,\n      W = extent[0][0],\n      w0,\n      w1,\n      N = extent[0][1],\n      n0,\n      n1,\n      E = extent[1][0],\n      e0,\n      e1,\n      S = extent[1][1],\n      s0,\n      s1,\n      dx = 0,\n      dy = 0,\n      moving,\n      shifting = signX && signY && keys && event.shiftKey,\n      lockX,\n      lockY,\n      points = Array.from(event.touches || [event], t => {\n        const i = t.identifier;\n        t = pointer(t, that);\n        t.point0 = t.slice();\n        t.identifier = i;\n        return t;\n      });\n    interrupt(that);\n    var emit = emitter(that, arguments, true).beforestart();\n    if (type === \"overlay\") {\n      if (selection) moving = true;\n      const pts = [points[0], points[1] || points[0]];\n      state.selection = selection = [[w0 = dim === Y ? W : min(pts[0][0], pts[1][0]), n0 = dim === X ? N : min(pts[0][1], pts[1][1])], [e0 = dim === Y ? E : max(pts[0][0], pts[1][0]), s0 = dim === X ? S : max(pts[0][1], pts[1][1])]];\n      if (points.length > 1) move(event);\n    } else {\n      w0 = selection[0][0];\n      n0 = selection[0][1];\n      e0 = selection[1][0];\n      s0 = selection[1][1];\n    }\n    w1 = w0;\n    n1 = n0;\n    e1 = e0;\n    s1 = s0;\n    var group = select(that).attr(\"pointer-events\", \"none\");\n    var overlay = group.selectAll(\".overlay\").attr(\"cursor\", cursors[type]);\n    if (event.touches) {\n      emit.moved = moved;\n      emit.ended = ended;\n    } else {\n      var view = select(event.view).on(\"mousemove.brush\", moved, true).on(\"mouseup.brush\", ended, true);\n      if (keys) view.on(\"keydown.brush\", keydowned, true).on(\"keyup.brush\", keyupped, true);\n      dragDisable(event.view);\n    }\n    redraw.call(that);\n    emit.start(event, mode.name);\n    function moved(event) {\n      for (const p of event.changedTouches || [event]) {\n        for (const d of points) if (d.identifier === p.identifier) d.cur = pointer(p, that);\n      }\n      if (shifting && !lockX && !lockY && points.length === 1) {\n        const point = points[0];\n        if (abs(point.cur[0] - point[0]) > abs(point.cur[1] - point[1])) lockY = true;else lockX = true;\n      }\n      for (const point of points) if (point.cur) point[0] = point.cur[0], point[1] = point.cur[1];\n      moving = true;\n      noevent(event);\n      move(event);\n    }\n    function move(event) {\n      const point = points[0],\n        point0 = point.point0;\n      var t;\n      dx = point[0] - point0[0];\n      dy = point[1] - point0[1];\n      switch (mode) {\n        case MODE_SPACE:\n        case MODE_DRAG:\n          {\n            if (signX) dx = max(W - w0, min(E - e0, dx)), w1 = w0 + dx, e1 = e0 + dx;\n            if (signY) dy = max(N - n0, min(S - s0, dy)), n1 = n0 + dy, s1 = s0 + dy;\n            break;\n          }\n        case MODE_HANDLE:\n          {\n            if (points[1]) {\n              if (signX) w1 = max(W, min(E, points[0][0])), e1 = max(W, min(E, points[1][0])), signX = 1;\n              if (signY) n1 = max(N, min(S, points[0][1])), s1 = max(N, min(S, points[1][1])), signY = 1;\n            } else {\n              if (signX < 0) dx = max(W - w0, min(E - w0, dx)), w1 = w0 + dx, e1 = e0;else if (signX > 0) dx = max(W - e0, min(E - e0, dx)), w1 = w0, e1 = e0 + dx;\n              if (signY < 0) dy = max(N - n0, min(S - n0, dy)), n1 = n0 + dy, s1 = s0;else if (signY > 0) dy = max(N - s0, min(S - s0, dy)), n1 = n0, s1 = s0 + dy;\n            }\n            break;\n          }\n        case MODE_CENTER:\n          {\n            if (signX) w1 = max(W, min(E, w0 - dx * signX)), e1 = max(W, min(E, e0 + dx * signX));\n            if (signY) n1 = max(N, min(S, n0 - dy * signY)), s1 = max(N, min(S, s0 + dy * signY));\n            break;\n          }\n      }\n      if (e1 < w1) {\n        signX *= -1;\n        t = w0, w0 = e0, e0 = t;\n        t = w1, w1 = e1, e1 = t;\n        if (type in flipX) overlay.attr(\"cursor\", cursors[type = flipX[type]]);\n      }\n      if (s1 < n1) {\n        signY *= -1;\n        t = n0, n0 = s0, s0 = t;\n        t = n1, n1 = s1, s1 = t;\n        if (type in flipY) overlay.attr(\"cursor\", cursors[type = flipY[type]]);\n      }\n      if (state.selection) selection = state.selection; // May be set by brush.move!\n      if (lockX) w1 = selection[0][0], e1 = selection[1][0];\n      if (lockY) n1 = selection[0][1], s1 = selection[1][1];\n      if (selection[0][0] !== w1 || selection[0][1] !== n1 || selection[1][0] !== e1 || selection[1][1] !== s1) {\n        state.selection = [[w1, n1], [e1, s1]];\n        redraw.call(that);\n        emit.brush(event, mode.name);\n      }\n    }\n    function ended(event) {\n      nopropagation(event);\n      if (event.touches) {\n        if (event.touches.length) return;\n        if (touchending) clearTimeout(touchending);\n        touchending = setTimeout(function () {\n          touchending = null;\n        }, 500); // Ghost clicks are delayed!\n      } else {\n        dragEnable(event.view, moving);\n        view.on(\"keydown.brush keyup.brush mousemove.brush mouseup.brush\", null);\n      }\n      group.attr(\"pointer-events\", \"all\");\n      overlay.attr(\"cursor\", cursors.overlay);\n      if (state.selection) selection = state.selection; // May be set by brush.move (on start)!\n      if (empty(selection)) state.selection = null, redraw.call(that);\n      emit.end(event, mode.name);\n    }\n    function keydowned(event) {\n      switch (event.keyCode) {\n        case 16:\n          {\n            // SHIFT\n            shifting = signX && signY;\n            break;\n          }\n        case 18:\n          {\n            // ALT\n            if (mode === MODE_HANDLE) {\n              if (signX) e0 = e1 - dx * signX, w0 = w1 + dx * signX;\n              if (signY) s0 = s1 - dy * signY, n0 = n1 + dy * signY;\n              mode = MODE_CENTER;\n              move(event);\n            }\n            break;\n          }\n        case 32:\n          {\n            // SPACE; takes priority over ALT\n            if (mode === MODE_HANDLE || mode === MODE_CENTER) {\n              if (signX < 0) e0 = e1 - dx;else if (signX > 0) w0 = w1 - dx;\n              if (signY < 0) s0 = s1 - dy;else if (signY > 0) n0 = n1 - dy;\n              mode = MODE_SPACE;\n              overlay.attr(\"cursor\", cursors.selection);\n              move(event);\n            }\n            break;\n          }\n        default:\n          return;\n      }\n      noevent(event);\n    }\n    function keyupped(event) {\n      switch (event.keyCode) {\n        case 16:\n          {\n            // SHIFT\n            if (shifting) {\n              lockX = lockY = shifting = false;\n              move(event);\n            }\n            break;\n          }\n        case 18:\n          {\n            // ALT\n            if (mode === MODE_CENTER) {\n              if (signX < 0) e0 = e1;else if (signX > 0) w0 = w1;\n              if (signY < 0) s0 = s1;else if (signY > 0) n0 = n1;\n              mode = MODE_HANDLE;\n              move(event);\n            }\n            break;\n          }\n        case 32:\n          {\n            // SPACE\n            if (mode === MODE_SPACE) {\n              if (event.altKey) {\n                if (signX) e0 = e1 - dx * signX, w0 = w1 + dx * signX;\n                if (signY) s0 = s1 - dy * signY, n0 = n1 + dy * signY;\n                mode = MODE_CENTER;\n              } else {\n                if (signX < 0) e0 = e1;else if (signX > 0) w0 = w1;\n                if (signY < 0) s0 = s1;else if (signY > 0) n0 = n1;\n                mode = MODE_HANDLE;\n              }\n              overlay.attr(\"cursor\", cursors[type]);\n              move(event);\n            }\n            break;\n          }\n        default:\n          return;\n      }\n      noevent(event);\n    }\n  }\n  function touchmoved(event) {\n    emitter(this, arguments).moved(event);\n  }\n  function touchended(event) {\n    emitter(this, arguments).ended(event);\n  }\n  function initialize() {\n    var state = this.__brush || {\n      selection: null\n    };\n    state.extent = number2(extent.apply(this, arguments));\n    state.dim = dim;\n    return state;\n  }\n  brush.extent = function (_) {\n    return arguments.length ? (extent = typeof _ === \"function\" ? _ : constant(number2(_)), brush) : extent;\n  };\n  brush.filter = function (_) {\n    return arguments.length ? (filter = typeof _ === \"function\" ? _ : constant(!!_), brush) : filter;\n  };\n  brush.touchable = function (_) {\n    return arguments.length ? (touchable = typeof _ === \"function\" ? _ : constant(!!_), brush) : touchable;\n  };\n  brush.handleSize = function (_) {\n    return arguments.length ? (handleSize = +_, brush) : handleSize;\n  };\n  brush.keyModifiers = function (_) {\n    return arguments.length ? (keys = !!_, brush) : keys;\n  };\n  brush.on = function () {\n    var value = listeners.on.apply(listeners, arguments);\n    return value === listeners ? brush : value;\n  };\n  return brush;\n}", "map": {"version": 3, "names": ["dispatch", "dragDisable", "dragEnable", "interpolate", "pointer", "select", "interrupt", "constant", "BrushEvent", "noevent", "nopropagation", "MODE_DRAG", "name", "MODE_SPACE", "MODE_HANDLE", "MODE_CENTER", "abs", "max", "min", "Math", "number1", "e", "number2", "X", "handles", "map", "type", "input", "x", "output", "xy", "Y", "y", "XY", "cursors", "overlay", "selection", "n", "s", "w", "nw", "ne", "se", "sw", "flipX", "flipY", "signsX", "signsY", "t", "defaultFilter", "event", "ctrl<PERSON>ey", "button", "defaultExtent", "svg", "ownerSVGElement", "hasAttribute", "viewBox", "baseVal", "width", "height", "value", "defaultTouchable", "navigator", "maxTouchPoints", "local", "node", "__brush", "parentNode", "empty", "extent", "brushSelection", "state", "dim", "brushX", "brush", "brushY", "filter", "touchable", "keys", "listeners", "handleSize", "touchending", "group", "property", "initialize", "selectAll", "data", "enter", "append", "attr", "merge", "each", "handle", "d", "exit", "remove", "redraw", "on", "started", "touchmoved", "touchended", "style", "move", "tween", "emitter", "arguments", "beforestart", "start", "end", "that", "emit", "selection0", "selection1", "apply", "i", "call", "args", "clear", "length", "clean", "Emitter", "active", "prototype", "starting", "mode", "datum", "sourceEvent", "target", "touches", "__data__", "metaKey", "altKey", "signX", "signY", "W", "w0", "w1", "N", "n0", "n1", "E", "e0", "e1", "S", "s0", "s1", "dx", "dy", "moving", "shifting", "shift<PERSON>ey", "lockX", "lockY", "points", "Array", "from", "identifier", "point0", "slice", "pts", "moved", "ended", "view", "keydowned", "keyupped", "p", "changedTouches", "cur", "point", "clearTimeout", "setTimeout", "keyCode", "_", "keyModifiers"], "sources": ["C:/console/aava-ui-web/node_modules/d3-brush/src/brush.js"], "sourcesContent": ["import {dispatch} from \"d3-dispatch\";\nimport {dragDisable, dragEnable} from \"d3-drag\";\nimport {interpolate} from \"d3-interpolate\";\nimport {pointer, select} from \"d3-selection\";\nimport {interrupt} from \"d3-transition\";\nimport constant from \"./constant.js\";\nimport BrushEvent from \"./event.js\";\nimport noevent, {nopropagation} from \"./noevent.js\";\n\nvar MODE_DRAG = {name: \"drag\"},\n    MODE_SPACE = {name: \"space\"},\n    MODE_HANDLE = {name: \"handle\"},\n    MODE_CENTER = {name: \"center\"};\n\nconst {abs, max, min} = Math;\n\nfunction number1(e) {\n  return [+e[0], +e[1]];\n}\n\nfunction number2(e) {\n  return [number1(e[0]), number1(e[1])];\n}\n\nvar X = {\n  name: \"x\",\n  handles: [\"w\", \"e\"].map(type),\n  input: function(x, e) { return x == null ? null : [[+x[0], e[0][1]], [+x[1], e[1][1]]]; },\n  output: function(xy) { return xy && [xy[0][0], xy[1][0]]; }\n};\n\nvar Y = {\n  name: \"y\",\n  handles: [\"n\", \"s\"].map(type),\n  input: function(y, e) { return y == null ? null : [[e[0][0], +y[0]], [e[1][0], +y[1]]]; },\n  output: function(xy) { return xy && [xy[0][1], xy[1][1]]; }\n};\n\nvar XY = {\n  name: \"xy\",\n  handles: [\"n\", \"w\", \"e\", \"s\", \"nw\", \"ne\", \"sw\", \"se\"].map(type),\n  input: function(xy) { return xy == null ? null : number2(xy); },\n  output: function(xy) { return xy; }\n};\n\nvar cursors = {\n  overlay: \"crosshair\",\n  selection: \"move\",\n  n: \"ns-resize\",\n  e: \"ew-resize\",\n  s: \"ns-resize\",\n  w: \"ew-resize\",\n  nw: \"nwse-resize\",\n  ne: \"nesw-resize\",\n  se: \"nwse-resize\",\n  sw: \"nesw-resize\"\n};\n\nvar flipX = {\n  e: \"w\",\n  w: \"e\",\n  nw: \"ne\",\n  ne: \"nw\",\n  se: \"sw\",\n  sw: \"se\"\n};\n\nvar flipY = {\n  n: \"s\",\n  s: \"n\",\n  nw: \"sw\",\n  ne: \"se\",\n  se: \"ne\",\n  sw: \"nw\"\n};\n\nvar signsX = {\n  overlay: +1,\n  selection: +1,\n  n: null,\n  e: +1,\n  s: null,\n  w: -1,\n  nw: -1,\n  ne: +1,\n  se: +1,\n  sw: -1\n};\n\nvar signsY = {\n  overlay: +1,\n  selection: +1,\n  n: -1,\n  e: null,\n  s: +1,\n  w: null,\n  nw: -1,\n  ne: -1,\n  se: +1,\n  sw: +1\n};\n\nfunction type(t) {\n  return {type: t};\n}\n\n// Ignore right-click, since that should open the context menu.\nfunction defaultFilter(event) {\n  return !event.ctrlKey && !event.button;\n}\n\nfunction defaultExtent() {\n  var svg = this.ownerSVGElement || this;\n  if (svg.hasAttribute(\"viewBox\")) {\n    svg = svg.viewBox.baseVal;\n    return [[svg.x, svg.y], [svg.x + svg.width, svg.y + svg.height]];\n  }\n  return [[0, 0], [svg.width.baseVal.value, svg.height.baseVal.value]];\n}\n\nfunction defaultTouchable() {\n  return navigator.maxTouchPoints || (\"ontouchstart\" in this);\n}\n\n// Like d3.local, but with the name “__brush” rather than auto-generated.\nfunction local(node) {\n  while (!node.__brush) if (!(node = node.parentNode)) return;\n  return node.__brush;\n}\n\nfunction empty(extent) {\n  return extent[0][0] === extent[1][0]\n      || extent[0][1] === extent[1][1];\n}\n\nexport function brushSelection(node) {\n  var state = node.__brush;\n  return state ? state.dim.output(state.selection) : null;\n}\n\nexport function brushX() {\n  return brush(X);\n}\n\nexport function brushY() {\n  return brush(Y);\n}\n\nexport default function() {\n  return brush(XY);\n}\n\nfunction brush(dim) {\n  var extent = defaultExtent,\n      filter = defaultFilter,\n      touchable = defaultTouchable,\n      keys = true,\n      listeners = dispatch(\"start\", \"brush\", \"end\"),\n      handleSize = 6,\n      touchending;\n\n  function brush(group) {\n    var overlay = group\n        .property(\"__brush\", initialize)\n      .selectAll(\".overlay\")\n      .data([type(\"overlay\")]);\n\n    overlay.enter().append(\"rect\")\n        .attr(\"class\", \"overlay\")\n        .attr(\"pointer-events\", \"all\")\n        .attr(\"cursor\", cursors.overlay)\n      .merge(overlay)\n        .each(function() {\n          var extent = local(this).extent;\n          select(this)\n              .attr(\"x\", extent[0][0])\n              .attr(\"y\", extent[0][1])\n              .attr(\"width\", extent[1][0] - extent[0][0])\n              .attr(\"height\", extent[1][1] - extent[0][1]);\n        });\n\n    group.selectAll(\".selection\")\n      .data([type(\"selection\")])\n      .enter().append(\"rect\")\n        .attr(\"class\", \"selection\")\n        .attr(\"cursor\", cursors.selection)\n        .attr(\"fill\", \"#777\")\n        .attr(\"fill-opacity\", 0.3)\n        .attr(\"stroke\", \"#fff\")\n        .attr(\"shape-rendering\", \"crispEdges\");\n\n    var handle = group.selectAll(\".handle\")\n      .data(dim.handles, function(d) { return d.type; });\n\n    handle.exit().remove();\n\n    handle.enter().append(\"rect\")\n        .attr(\"class\", function(d) { return \"handle handle--\" + d.type; })\n        .attr(\"cursor\", function(d) { return cursors[d.type]; });\n\n    group\n        .each(redraw)\n        .attr(\"fill\", \"none\")\n        .attr(\"pointer-events\", \"all\")\n        .on(\"mousedown.brush\", started)\n      .filter(touchable)\n        .on(\"touchstart.brush\", started)\n        .on(\"touchmove.brush\", touchmoved)\n        .on(\"touchend.brush touchcancel.brush\", touchended)\n        .style(\"touch-action\", \"none\")\n        .style(\"-webkit-tap-highlight-color\", \"rgba(0,0,0,0)\");\n  }\n\n  brush.move = function(group, selection, event) {\n    if (group.tween) {\n      group\n          .on(\"start.brush\", function(event) { emitter(this, arguments).beforestart().start(event); })\n          .on(\"interrupt.brush end.brush\", function(event) { emitter(this, arguments).end(event); })\n          .tween(\"brush\", function() {\n            var that = this,\n                state = that.__brush,\n                emit = emitter(that, arguments),\n                selection0 = state.selection,\n                selection1 = dim.input(typeof selection === \"function\" ? selection.apply(this, arguments) : selection, state.extent),\n                i = interpolate(selection0, selection1);\n\n            function tween(t) {\n              state.selection = t === 1 && selection1 === null ? null : i(t);\n              redraw.call(that);\n              emit.brush();\n            }\n\n            return selection0 !== null && selection1 !== null ? tween : tween(1);\n          });\n    } else {\n      group\n          .each(function() {\n            var that = this,\n                args = arguments,\n                state = that.__brush,\n                selection1 = dim.input(typeof selection === \"function\" ? selection.apply(that, args) : selection, state.extent),\n                emit = emitter(that, args).beforestart();\n\n            interrupt(that);\n            state.selection = selection1 === null ? null : selection1;\n            redraw.call(that);\n            emit.start(event).brush(event).end(event);\n          });\n    }\n  };\n\n  brush.clear = function(group, event) {\n    brush.move(group, null, event);\n  };\n\n  function redraw() {\n    var group = select(this),\n        selection = local(this).selection;\n\n    if (selection) {\n      group.selectAll(\".selection\")\n          .style(\"display\", null)\n          .attr(\"x\", selection[0][0])\n          .attr(\"y\", selection[0][1])\n          .attr(\"width\", selection[1][0] - selection[0][0])\n          .attr(\"height\", selection[1][1] - selection[0][1]);\n\n      group.selectAll(\".handle\")\n          .style(\"display\", null)\n          .attr(\"x\", function(d) { return d.type[d.type.length - 1] === \"e\" ? selection[1][0] - handleSize / 2 : selection[0][0] - handleSize / 2; })\n          .attr(\"y\", function(d) { return d.type[0] === \"s\" ? selection[1][1] - handleSize / 2 : selection[0][1] - handleSize / 2; })\n          .attr(\"width\", function(d) { return d.type === \"n\" || d.type === \"s\" ? selection[1][0] - selection[0][0] + handleSize : handleSize; })\n          .attr(\"height\", function(d) { return d.type === \"e\" || d.type === \"w\" ? selection[1][1] - selection[0][1] + handleSize : handleSize; });\n    }\n\n    else {\n      group.selectAll(\".selection,.handle\")\n          .style(\"display\", \"none\")\n          .attr(\"x\", null)\n          .attr(\"y\", null)\n          .attr(\"width\", null)\n          .attr(\"height\", null);\n    }\n  }\n\n  function emitter(that, args, clean) {\n    var emit = that.__brush.emitter;\n    return emit && (!clean || !emit.clean) ? emit : new Emitter(that, args, clean);\n  }\n\n  function Emitter(that, args, clean) {\n    this.that = that;\n    this.args = args;\n    this.state = that.__brush;\n    this.active = 0;\n    this.clean = clean;\n  }\n\n  Emitter.prototype = {\n    beforestart: function() {\n      if (++this.active === 1) this.state.emitter = this, this.starting = true;\n      return this;\n    },\n    start: function(event, mode) {\n      if (this.starting) this.starting = false, this.emit(\"start\", event, mode);\n      else this.emit(\"brush\", event);\n      return this;\n    },\n    brush: function(event, mode) {\n      this.emit(\"brush\", event, mode);\n      return this;\n    },\n    end: function(event, mode) {\n      if (--this.active === 0) delete this.state.emitter, this.emit(\"end\", event, mode);\n      return this;\n    },\n    emit: function(type, event, mode) {\n      var d = select(this.that).datum();\n      listeners.call(\n        type,\n        this.that,\n        new BrushEvent(type, {\n          sourceEvent: event,\n          target: brush,\n          selection: dim.output(this.state.selection),\n          mode,\n          dispatch: listeners\n        }),\n        d\n      );\n    }\n  };\n\n  function started(event) {\n    if (touchending && !event.touches) return;\n    if (!filter.apply(this, arguments)) return;\n\n    var that = this,\n        type = event.target.__data__.type,\n        mode = (keys && event.metaKey ? type = \"overlay\" : type) === \"selection\" ? MODE_DRAG : (keys && event.altKey ? MODE_CENTER : MODE_HANDLE),\n        signX = dim === Y ? null : signsX[type],\n        signY = dim === X ? null : signsY[type],\n        state = local(that),\n        extent = state.extent,\n        selection = state.selection,\n        W = extent[0][0], w0, w1,\n        N = extent[0][1], n0, n1,\n        E = extent[1][0], e0, e1,\n        S = extent[1][1], s0, s1,\n        dx = 0,\n        dy = 0,\n        moving,\n        shifting = signX && signY && keys && event.shiftKey,\n        lockX,\n        lockY,\n        points = Array.from(event.touches || [event], t => {\n          const i = t.identifier;\n          t = pointer(t, that);\n          t.point0 = t.slice();\n          t.identifier = i;\n          return t;\n        });\n\n    interrupt(that);\n    var emit = emitter(that, arguments, true).beforestart();\n\n    if (type === \"overlay\") {\n      if (selection) moving = true;\n      const pts = [points[0], points[1] || points[0]];\n      state.selection = selection = [[\n          w0 = dim === Y ? W : min(pts[0][0], pts[1][0]),\n          n0 = dim === X ? N : min(pts[0][1], pts[1][1])\n        ], [\n          e0 = dim === Y ? E : max(pts[0][0], pts[1][0]),\n          s0 = dim === X ? S : max(pts[0][1], pts[1][1])\n        ]];\n      if (points.length > 1) move(event);\n    } else {\n      w0 = selection[0][0];\n      n0 = selection[0][1];\n      e0 = selection[1][0];\n      s0 = selection[1][1];\n    }\n\n    w1 = w0;\n    n1 = n0;\n    e1 = e0;\n    s1 = s0;\n\n    var group = select(that)\n        .attr(\"pointer-events\", \"none\");\n\n    var overlay = group.selectAll(\".overlay\")\n        .attr(\"cursor\", cursors[type]);\n\n    if (event.touches) {\n      emit.moved = moved;\n      emit.ended = ended;\n    } else {\n      var view = select(event.view)\n          .on(\"mousemove.brush\", moved, true)\n          .on(\"mouseup.brush\", ended, true);\n      if (keys) view\n          .on(\"keydown.brush\", keydowned, true)\n          .on(\"keyup.brush\", keyupped, true)\n\n      dragDisable(event.view);\n    }\n\n    redraw.call(that);\n    emit.start(event, mode.name);\n\n    function moved(event) {\n      for (const p of event.changedTouches || [event]) {\n        for (const d of points)\n          if (d.identifier === p.identifier) d.cur = pointer(p, that);\n      }\n      if (shifting && !lockX && !lockY && points.length === 1) {\n        const point = points[0];\n        if (abs(point.cur[0] - point[0]) > abs(point.cur[1] - point[1]))\n          lockY = true;\n        else\n          lockX = true;\n      }\n      for (const point of points)\n        if (point.cur) point[0] = point.cur[0], point[1] = point.cur[1];\n      moving = true;\n      noevent(event);\n      move(event);\n    }\n\n    function move(event) {\n      const point = points[0], point0 = point.point0;\n      var t;\n\n      dx = point[0] - point0[0];\n      dy = point[1] - point0[1];\n\n      switch (mode) {\n        case MODE_SPACE:\n        case MODE_DRAG: {\n          if (signX) dx = max(W - w0, min(E - e0, dx)), w1 = w0 + dx, e1 = e0 + dx;\n          if (signY) dy = max(N - n0, min(S - s0, dy)), n1 = n0 + dy, s1 = s0 + dy;\n          break;\n        }\n        case MODE_HANDLE: {\n          if (points[1]) {\n            if (signX) w1 = max(W, min(E, points[0][0])), e1 = max(W, min(E, points[1][0])), signX = 1;\n            if (signY) n1 = max(N, min(S, points[0][1])), s1 = max(N, min(S, points[1][1])), signY = 1;\n          } else {\n            if (signX < 0) dx = max(W - w0, min(E - w0, dx)), w1 = w0 + dx, e1 = e0;\n            else if (signX > 0) dx = max(W - e0, min(E - e0, dx)), w1 = w0, e1 = e0 + dx;\n            if (signY < 0) dy = max(N - n0, min(S - n0, dy)), n1 = n0 + dy, s1 = s0;\n            else if (signY > 0) dy = max(N - s0, min(S - s0, dy)), n1 = n0, s1 = s0 + dy;\n          }\n          break;\n        }\n        case MODE_CENTER: {\n          if (signX) w1 = max(W, min(E, w0 - dx * signX)), e1 = max(W, min(E, e0 + dx * signX));\n          if (signY) n1 = max(N, min(S, n0 - dy * signY)), s1 = max(N, min(S, s0 + dy * signY));\n          break;\n        }\n      }\n\n      if (e1 < w1) {\n        signX *= -1;\n        t = w0, w0 = e0, e0 = t;\n        t = w1, w1 = e1, e1 = t;\n        if (type in flipX) overlay.attr(\"cursor\", cursors[type = flipX[type]]);\n      }\n\n      if (s1 < n1) {\n        signY *= -1;\n        t = n0, n0 = s0, s0 = t;\n        t = n1, n1 = s1, s1 = t;\n        if (type in flipY) overlay.attr(\"cursor\", cursors[type = flipY[type]]);\n      }\n\n      if (state.selection) selection = state.selection; // May be set by brush.move!\n      if (lockX) w1 = selection[0][0], e1 = selection[1][0];\n      if (lockY) n1 = selection[0][1], s1 = selection[1][1];\n\n      if (selection[0][0] !== w1\n          || selection[0][1] !== n1\n          || selection[1][0] !== e1\n          || selection[1][1] !== s1) {\n        state.selection = [[w1, n1], [e1, s1]];\n        redraw.call(that);\n        emit.brush(event, mode.name);\n      }\n    }\n\n    function ended(event) {\n      nopropagation(event);\n      if (event.touches) {\n        if (event.touches.length) return;\n        if (touchending) clearTimeout(touchending);\n        touchending = setTimeout(function() { touchending = null; }, 500); // Ghost clicks are delayed!\n      } else {\n        dragEnable(event.view, moving);\n        view.on(\"keydown.brush keyup.brush mousemove.brush mouseup.brush\", null);\n      }\n      group.attr(\"pointer-events\", \"all\");\n      overlay.attr(\"cursor\", cursors.overlay);\n      if (state.selection) selection = state.selection; // May be set by brush.move (on start)!\n      if (empty(selection)) state.selection = null, redraw.call(that);\n      emit.end(event, mode.name);\n    }\n\n    function keydowned(event) {\n      switch (event.keyCode) {\n        case 16: { // SHIFT\n          shifting = signX && signY;\n          break;\n        }\n        case 18: { // ALT\n          if (mode === MODE_HANDLE) {\n            if (signX) e0 = e1 - dx * signX, w0 = w1 + dx * signX;\n            if (signY) s0 = s1 - dy * signY, n0 = n1 + dy * signY;\n            mode = MODE_CENTER;\n            move(event);\n          }\n          break;\n        }\n        case 32: { // SPACE; takes priority over ALT\n          if (mode === MODE_HANDLE || mode === MODE_CENTER) {\n            if (signX < 0) e0 = e1 - dx; else if (signX > 0) w0 = w1 - dx;\n            if (signY < 0) s0 = s1 - dy; else if (signY > 0) n0 = n1 - dy;\n            mode = MODE_SPACE;\n            overlay.attr(\"cursor\", cursors.selection);\n            move(event);\n          }\n          break;\n        }\n        default: return;\n      }\n      noevent(event);\n    }\n\n    function keyupped(event) {\n      switch (event.keyCode) {\n        case 16: { // SHIFT\n          if (shifting) {\n            lockX = lockY = shifting = false;\n            move(event);\n          }\n          break;\n        }\n        case 18: { // ALT\n          if (mode === MODE_CENTER) {\n            if (signX < 0) e0 = e1; else if (signX > 0) w0 = w1;\n            if (signY < 0) s0 = s1; else if (signY > 0) n0 = n1;\n            mode = MODE_HANDLE;\n            move(event);\n          }\n          break;\n        }\n        case 32: { // SPACE\n          if (mode === MODE_SPACE) {\n            if (event.altKey) {\n              if (signX) e0 = e1 - dx * signX, w0 = w1 + dx * signX;\n              if (signY) s0 = s1 - dy * signY, n0 = n1 + dy * signY;\n              mode = MODE_CENTER;\n            } else {\n              if (signX < 0) e0 = e1; else if (signX > 0) w0 = w1;\n              if (signY < 0) s0 = s1; else if (signY > 0) n0 = n1;\n              mode = MODE_HANDLE;\n            }\n            overlay.attr(\"cursor\", cursors[type]);\n            move(event);\n          }\n          break;\n        }\n        default: return;\n      }\n      noevent(event);\n    }\n  }\n\n  function touchmoved(event) {\n    emitter(this, arguments).moved(event);\n  }\n\n  function touchended(event) {\n    emitter(this, arguments).ended(event);\n  }\n\n  function initialize() {\n    var state = this.__brush || {selection: null};\n    state.extent = number2(extent.apply(this, arguments));\n    state.dim = dim;\n    return state;\n  }\n\n  brush.extent = function(_) {\n    return arguments.length ? (extent = typeof _ === \"function\" ? _ : constant(number2(_)), brush) : extent;\n  };\n\n  brush.filter = function(_) {\n    return arguments.length ? (filter = typeof _ === \"function\" ? _ : constant(!!_), brush) : filter;\n  };\n\n  brush.touchable = function(_) {\n    return arguments.length ? (touchable = typeof _ === \"function\" ? _ : constant(!!_), brush) : touchable;\n  };\n\n  brush.handleSize = function(_) {\n    return arguments.length ? (handleSize = +_, brush) : handleSize;\n  };\n\n  brush.keyModifiers = function(_) {\n    return arguments.length ? (keys = !!_, brush) : keys;\n  };\n\n  brush.on = function() {\n    var value = listeners.on.apply(listeners, arguments);\n    return value === listeners ? brush : value;\n  };\n\n  return brush;\n}\n"], "mappings": "AAAA,SAAQA,QAAQ,QAAO,aAAa;AACpC,SAAQC,WAAW,EAAEC,UAAU,QAAO,SAAS;AAC/C,SAAQC,WAAW,QAAO,gBAAgB;AAC1C,SAAQC,OAAO,EAAEC,MAAM,QAAO,cAAc;AAC5C,SAAQC,SAAS,QAAO,eAAe;AACvC,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,IAAGC,aAAa,QAAO,cAAc;AAEnD,IAAIC,SAAS,GAAG;IAACC,IAAI,EAAE;EAAM,CAAC;EAC1BC,UAAU,GAAG;IAACD,IAAI,EAAE;EAAO,CAAC;EAC5BE,WAAW,GAAG;IAACF,IAAI,EAAE;EAAQ,CAAC;EAC9BG,WAAW,GAAG;IAACH,IAAI,EAAE;EAAQ,CAAC;AAElC,MAAM;EAACI,GAAG;EAAEC,GAAG;EAAEC;AAAG,CAAC,GAAGC,IAAI;AAE5B,SAASC,OAAOA,CAACC,CAAC,EAAE;EAClB,OAAO,CAAC,CAACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAACA,CAAC,CAAC,CAAC,CAAC,CAAC;AACvB;AAEA,SAASC,OAAOA,CAACD,CAAC,EAAE;EAClB,OAAO,CAACD,OAAO,CAACC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvC;AAEA,IAAIE,CAAC,GAAG;EACNX,IAAI,EAAE,GAAG;EACTY,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAACC,GAAG,CAACC,IAAI,CAAC;EAC7BC,KAAK,EAAE,SAAAA,CAASC,CAAC,EAAEP,CAAC,EAAE;IAAE,OAAOO,CAAC,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,CAACA,CAAC,CAAC,CAAC,CAAC,EAAEP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAACO,CAAC,CAAC,CAAC,CAAC,EAAEP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAAE,CAAC;EACzFQ,MAAM,EAAE,SAAAA,CAASC,EAAE,EAAE;IAAE,OAAOA,EAAE,IAAI,CAACA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAAE;AAC5D,CAAC;AAED,IAAIC,CAAC,GAAG;EACNnB,IAAI,EAAE,GAAG;EACTY,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAACC,GAAG,CAACC,IAAI,CAAC;EAC7BC,KAAK,EAAE,SAAAA,CAASK,CAAC,EAAEX,CAAC,EAAE;IAAE,OAAOW,CAAC,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,CAACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAACW,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAACW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAAE,CAAC;EACzFH,MAAM,EAAE,SAAAA,CAASC,EAAE,EAAE;IAAE,OAAOA,EAAE,IAAI,CAACA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAAE;AAC5D,CAAC;AAED,IAAIG,EAAE,GAAG;EACPrB,IAAI,EAAE,IAAI;EACVY,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAACC,GAAG,CAACC,IAAI,CAAC;EAC/DC,KAAK,EAAE,SAAAA,CAASG,EAAE,EAAE;IAAE,OAAOA,EAAE,IAAI,IAAI,GAAG,IAAI,GAAGR,OAAO,CAACQ,EAAE,CAAC;EAAE,CAAC;EAC/DD,MAAM,EAAE,SAAAA,CAASC,EAAE,EAAE;IAAE,OAAOA,EAAE;EAAE;AACpC,CAAC;AAED,IAAII,OAAO,GAAG;EACZC,OAAO,EAAE,WAAW;EACpBC,SAAS,EAAE,MAAM;EACjBC,CAAC,EAAE,WAAW;EACdhB,CAAC,EAAE,WAAW;EACdiB,CAAC,EAAE,WAAW;EACdC,CAAC,EAAE,WAAW;EACdC,EAAE,EAAE,aAAa;EACjBC,EAAE,EAAE,aAAa;EACjBC,EAAE,EAAE,aAAa;EACjBC,EAAE,EAAE;AACN,CAAC;AAED,IAAIC,KAAK,GAAG;EACVvB,CAAC,EAAE,GAAG;EACNkB,CAAC,EAAE,GAAG;EACNC,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE;AACN,CAAC;AAED,IAAIE,KAAK,GAAG;EACVR,CAAC,EAAE,GAAG;EACNC,CAAC,EAAE,GAAG;EACNE,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE;AACN,CAAC;AAED,IAAIG,MAAM,GAAG;EACXX,OAAO,EAAE,CAAC,CAAC;EACXC,SAAS,EAAE,CAAC,CAAC;EACbC,CAAC,EAAE,IAAI;EACPhB,CAAC,EAAE,CAAC,CAAC;EACLiB,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,CAAC,CAAC;EACLC,EAAE,EAAE,CAAC,CAAC;EACNC,EAAE,EAAE,CAAC,CAAC;EACNC,EAAE,EAAE,CAAC,CAAC;EACNC,EAAE,EAAE,CAAC;AACP,CAAC;AAED,IAAII,MAAM,GAAG;EACXZ,OAAO,EAAE,CAAC,CAAC;EACXC,SAAS,EAAE,CAAC,CAAC;EACbC,CAAC,EAAE,CAAC,CAAC;EACLhB,CAAC,EAAE,IAAI;EACPiB,CAAC,EAAE,CAAC,CAAC;EACLC,CAAC,EAAE,IAAI;EACPC,EAAE,EAAE,CAAC,CAAC;EACNC,EAAE,EAAE,CAAC,CAAC;EACNC,EAAE,EAAE,CAAC,CAAC;EACNC,EAAE,EAAE,CAAC;AACP,CAAC;AAED,SAASjB,IAAIA,CAACsB,CAAC,EAAE;EACf,OAAO;IAACtB,IAAI,EAAEsB;EAAC,CAAC;AAClB;;AAEA;AACA,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC5B,OAAO,CAACA,KAAK,CAACC,OAAO,IAAI,CAACD,KAAK,CAACE,MAAM;AACxC;AAEA,SAASC,aAAaA,CAAA,EAAG;EACvB,IAAIC,GAAG,GAAG,IAAI,CAACC,eAAe,IAAI,IAAI;EACtC,IAAID,GAAG,CAACE,YAAY,CAAC,SAAS,CAAC,EAAE;IAC/BF,GAAG,GAAGA,GAAG,CAACG,OAAO,CAACC,OAAO;IACzB,OAAO,CAAC,CAACJ,GAAG,CAAC1B,CAAC,EAAE0B,GAAG,CAACtB,CAAC,CAAC,EAAE,CAACsB,GAAG,CAAC1B,CAAC,GAAG0B,GAAG,CAACK,KAAK,EAAEL,GAAG,CAACtB,CAAC,GAAGsB,GAAG,CAACM,MAAM,CAAC,CAAC;EAClE;EACA,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAACN,GAAG,CAACK,KAAK,CAACD,OAAO,CAACG,KAAK,EAAEP,GAAG,CAACM,MAAM,CAACF,OAAO,CAACG,KAAK,CAAC,CAAC;AACtE;AAEA,SAASC,gBAAgBA,CAAA,EAAG;EAC1B,OAAOC,SAAS,CAACC,cAAc,IAAK,cAAc,IAAI,IAAK;AAC7D;;AAEA;AACA,SAASC,KAAKA,CAACC,IAAI,EAAE;EACnB,OAAO,CAACA,IAAI,CAACC,OAAO,EAAE,IAAI,EAAED,IAAI,GAAGA,IAAI,CAACE,UAAU,CAAC,EAAE;EACrD,OAAOF,IAAI,CAACC,OAAO;AACrB;AAEA,SAASE,KAAKA,CAACC,MAAM,EAAE;EACrB,OAAOA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAC7BA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtC;AAEA,OAAO,SAASC,cAAcA,CAACL,IAAI,EAAE;EACnC,IAAIM,KAAK,GAAGN,IAAI,CAACC,OAAO;EACxB,OAAOK,KAAK,GAAGA,KAAK,CAACC,GAAG,CAAC5C,MAAM,CAAC2C,KAAK,CAACpC,SAAS,CAAC,GAAG,IAAI;AACzD;AAEA,OAAO,SAASsC,MAAMA,CAAA,EAAG;EACvB,OAAOC,KAAK,CAACpD,CAAC,CAAC;AACjB;AAEA,OAAO,SAASqD,MAAMA,CAAA,EAAG;EACvB,OAAOD,KAAK,CAAC5C,CAAC,CAAC;AACjB;AAEA,eAAe,YAAW;EACxB,OAAO4C,KAAK,CAAC1C,EAAE,CAAC;AAClB;AAEA,SAAS0C,KAAKA,CAACF,GAAG,EAAE;EAClB,IAAIH,MAAM,GAAGjB,aAAa;IACtBwB,MAAM,GAAG5B,aAAa;IACtB6B,SAAS,GAAGhB,gBAAgB;IAC5BiB,IAAI,GAAG,IAAI;IACXC,SAAS,GAAGhF,QAAQ,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC;IAC7CiF,UAAU,GAAG,CAAC;IACdC,WAAW;EAEf,SAASP,KAAKA,CAACQ,KAAK,EAAE;IACpB,IAAIhD,OAAO,GAAGgD,KAAK,CACdC,QAAQ,CAAC,SAAS,EAAEC,UAAU,CAAC,CACjCC,SAAS,CAAC,UAAU,CAAC,CACrBC,IAAI,CAAC,CAAC7D,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IAE1BS,OAAO,CAACqD,KAAK,CAAC,CAAC,CAACC,MAAM,CAAC,MAAM,CAAC,CACzBC,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC,CACxBA,IAAI,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAC7BA,IAAI,CAAC,QAAQ,EAAExD,OAAO,CAACC,OAAO,CAAC,CACjCwD,KAAK,CAACxD,OAAO,CAAC,CACZyD,IAAI,CAAC,YAAW;MACf,IAAItB,MAAM,GAAGL,KAAK,CAAC,IAAI,CAAC,CAACK,MAAM;MAC/BjE,MAAM,CAAC,IAAI,CAAC,CACPqF,IAAI,CAAC,GAAG,EAAEpB,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACvBoB,IAAI,CAAC,GAAG,EAAEpB,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACvBoB,IAAI,CAAC,OAAO,EAAEpB,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAC1CoB,IAAI,CAAC,QAAQ,EAAEpB,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClD,CAAC,CAAC;IAENa,KAAK,CAACG,SAAS,CAAC,YAAY,CAAC,CAC1BC,IAAI,CAAC,CAAC7D,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CACzB8D,KAAK,CAAC,CAAC,CAACC,MAAM,CAAC,MAAM,CAAC,CACpBC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,CAC1BA,IAAI,CAAC,QAAQ,EAAExD,OAAO,CAACE,SAAS,CAAC,CACjCsD,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CACpBA,IAAI,CAAC,cAAc,EAAE,GAAG,CAAC,CACzBA,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CACtBA,IAAI,CAAC,iBAAiB,EAAE,YAAY,CAAC;IAE1C,IAAIG,MAAM,GAAGV,KAAK,CAACG,SAAS,CAAC,SAAS,CAAC,CACpCC,IAAI,CAACd,GAAG,CAACjD,OAAO,EAAE,UAASsE,CAAC,EAAE;MAAE,OAAOA,CAAC,CAACpE,IAAI;IAAE,CAAC,CAAC;IAEpDmE,MAAM,CAACE,IAAI,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;IAEtBH,MAAM,CAACL,KAAK,CAAC,CAAC,CAACC,MAAM,CAAC,MAAM,CAAC,CACxBC,IAAI,CAAC,OAAO,EAAE,UAASI,CAAC,EAAE;MAAE,OAAO,iBAAiB,GAAGA,CAAC,CAACpE,IAAI;IAAE,CAAC,CAAC,CACjEgE,IAAI,CAAC,QAAQ,EAAE,UAASI,CAAC,EAAE;MAAE,OAAO5D,OAAO,CAAC4D,CAAC,CAACpE,IAAI,CAAC;IAAE,CAAC,CAAC;IAE5DyD,KAAK,CACAS,IAAI,CAACK,MAAM,CAAC,CACZP,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CACpBA,IAAI,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAC7BQ,EAAE,CAAC,iBAAiB,EAAEC,OAAO,CAAC,CAChCtB,MAAM,CAACC,SAAS,CAAC,CACfoB,EAAE,CAAC,kBAAkB,EAAEC,OAAO,CAAC,CAC/BD,EAAE,CAAC,iBAAiB,EAAEE,UAAU,CAAC,CACjCF,EAAE,CAAC,kCAAkC,EAAEG,UAAU,CAAC,CAClDC,KAAK,CAAC,cAAc,EAAE,MAAM,CAAC,CAC7BA,KAAK,CAAC,6BAA6B,EAAE,eAAe,CAAC;EAC5D;EAEA3B,KAAK,CAAC4B,IAAI,GAAG,UAASpB,KAAK,EAAE/C,SAAS,EAAEc,KAAK,EAAE;IAC7C,IAAIiC,KAAK,CAACqB,KAAK,EAAE;MACfrB,KAAK,CACAe,EAAE,CAAC,aAAa,EAAE,UAAShD,KAAK,EAAE;QAAEuD,OAAO,CAAC,IAAI,EAAEC,SAAS,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC1D,KAAK,CAAC;MAAE,CAAC,CAAC,CAC3FgD,EAAE,CAAC,2BAA2B,EAAE,UAAShD,KAAK,EAAE;QAAEuD,OAAO,CAAC,IAAI,EAAEC,SAAS,CAAC,CAACG,GAAG,CAAC3D,KAAK,CAAC;MAAE,CAAC,CAAC,CACzFsD,KAAK,CAAC,OAAO,EAAE,YAAW;QACzB,IAAIM,IAAI,GAAG,IAAI;UACXtC,KAAK,GAAGsC,IAAI,CAAC3C,OAAO;UACpB4C,IAAI,GAAGN,OAAO,CAACK,IAAI,EAAEJ,SAAS,CAAC;UAC/BM,UAAU,GAAGxC,KAAK,CAACpC,SAAS;UAC5B6E,UAAU,GAAGxC,GAAG,CAAC9C,KAAK,CAAC,OAAOS,SAAS,KAAK,UAAU,GAAGA,SAAS,CAAC8E,KAAK,CAAC,IAAI,EAAER,SAAS,CAAC,GAAGtE,SAAS,EAAEoC,KAAK,CAACF,MAAM,CAAC;UACpH6C,CAAC,GAAGhH,WAAW,CAAC6G,UAAU,EAAEC,UAAU,CAAC;QAE3C,SAAST,KAAKA,CAACxD,CAAC,EAAE;UAChBwB,KAAK,CAACpC,SAAS,GAAGY,CAAC,KAAK,CAAC,IAAIiE,UAAU,KAAK,IAAI,GAAG,IAAI,GAAGE,CAAC,CAACnE,CAAC,CAAC;UAC9DiD,MAAM,CAACmB,IAAI,CAACN,IAAI,CAAC;UACjBC,IAAI,CAACpC,KAAK,CAAC,CAAC;QACd;QAEA,OAAOqC,UAAU,KAAK,IAAI,IAAIC,UAAU,KAAK,IAAI,GAAGT,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC;MACtE,CAAC,CAAC;IACR,CAAC,MAAM;MACLrB,KAAK,CACAS,IAAI,CAAC,YAAW;QACf,IAAIkB,IAAI,GAAG,IAAI;UACXO,IAAI,GAAGX,SAAS;UAChBlC,KAAK,GAAGsC,IAAI,CAAC3C,OAAO;UACpB8C,UAAU,GAAGxC,GAAG,CAAC9C,KAAK,CAAC,OAAOS,SAAS,KAAK,UAAU,GAAGA,SAAS,CAAC8E,KAAK,CAACJ,IAAI,EAAEO,IAAI,CAAC,GAAGjF,SAAS,EAAEoC,KAAK,CAACF,MAAM,CAAC;UAC/GyC,IAAI,GAAGN,OAAO,CAACK,IAAI,EAAEO,IAAI,CAAC,CAACV,WAAW,CAAC,CAAC;QAE5CrG,SAAS,CAACwG,IAAI,CAAC;QACftC,KAAK,CAACpC,SAAS,GAAG6E,UAAU,KAAK,IAAI,GAAG,IAAI,GAAGA,UAAU;QACzDhB,MAAM,CAACmB,IAAI,CAACN,IAAI,CAAC;QACjBC,IAAI,CAACH,KAAK,CAAC1D,KAAK,CAAC,CAACyB,KAAK,CAACzB,KAAK,CAAC,CAAC2D,GAAG,CAAC3D,KAAK,CAAC;MAC3C,CAAC,CAAC;IACR;EACF,CAAC;EAEDyB,KAAK,CAAC2C,KAAK,GAAG,UAASnC,KAAK,EAAEjC,KAAK,EAAE;IACnCyB,KAAK,CAAC4B,IAAI,CAACpB,KAAK,EAAE,IAAI,EAAEjC,KAAK,CAAC;EAChC,CAAC;EAED,SAAS+C,MAAMA,CAAA,EAAG;IAChB,IAAId,KAAK,GAAG9E,MAAM,CAAC,IAAI,CAAC;MACpB+B,SAAS,GAAG6B,KAAK,CAAC,IAAI,CAAC,CAAC7B,SAAS;IAErC,IAAIA,SAAS,EAAE;MACb+C,KAAK,CAACG,SAAS,CAAC,YAAY,CAAC,CACxBgB,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC,CACtBZ,IAAI,CAAC,GAAG,EAAEtD,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAC1BsD,IAAI,CAAC,GAAG,EAAEtD,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAC1BsD,IAAI,CAAC,OAAO,EAAEtD,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAChDsD,IAAI,CAAC,QAAQ,EAAEtD,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAEtD+C,KAAK,CAACG,SAAS,CAAC,SAAS,CAAC,CACrBgB,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC,CACtBZ,IAAI,CAAC,GAAG,EAAE,UAASI,CAAC,EAAE;QAAE,OAAOA,CAAC,CAACpE,IAAI,CAACoE,CAAC,CAACpE,IAAI,CAAC6F,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,GAAGnF,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG6C,UAAU,GAAG,CAAC,GAAG7C,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG6C,UAAU,GAAG,CAAC;MAAE,CAAC,CAAC,CAC1IS,IAAI,CAAC,GAAG,EAAE,UAASI,CAAC,EAAE;QAAE,OAAOA,CAAC,CAACpE,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,GAAGU,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG6C,UAAU,GAAG,CAAC,GAAG7C,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG6C,UAAU,GAAG,CAAC;MAAE,CAAC,CAAC,CAC1HS,IAAI,CAAC,OAAO,EAAE,UAASI,CAAC,EAAE;QAAE,OAAOA,CAAC,CAACpE,IAAI,KAAK,GAAG,IAAIoE,CAAC,CAACpE,IAAI,KAAK,GAAG,GAAGU,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG6C,UAAU,GAAGA,UAAU;MAAE,CAAC,CAAC,CACrIS,IAAI,CAAC,QAAQ,EAAE,UAASI,CAAC,EAAE;QAAE,OAAOA,CAAC,CAACpE,IAAI,KAAK,GAAG,IAAIoE,CAAC,CAACpE,IAAI,KAAK,GAAG,GAAGU,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG6C,UAAU,GAAGA,UAAU;MAAE,CAAC,CAAC;IAC7I,CAAC,MAEI;MACHE,KAAK,CAACG,SAAS,CAAC,oBAAoB,CAAC,CAChCgB,KAAK,CAAC,SAAS,EAAE,MAAM,CAAC,CACxBZ,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CACfA,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CACfA,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CACnBA,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC;IAC3B;EACF;EAEA,SAASe,OAAOA,CAACK,IAAI,EAAEO,IAAI,EAAEG,KAAK,EAAE;IAClC,IAAIT,IAAI,GAAGD,IAAI,CAAC3C,OAAO,CAACsC,OAAO;IAC/B,OAAOM,IAAI,KAAK,CAACS,KAAK,IAAI,CAACT,IAAI,CAACS,KAAK,CAAC,GAAGT,IAAI,GAAG,IAAIU,OAAO,CAACX,IAAI,EAAEO,IAAI,EAAEG,KAAK,CAAC;EAChF;EAEA,SAASC,OAAOA,CAACX,IAAI,EAAEO,IAAI,EAAEG,KAAK,EAAE;IAClC,IAAI,CAACV,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACO,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC7C,KAAK,GAAGsC,IAAI,CAAC3C,OAAO;IACzB,IAAI,CAACuD,MAAM,GAAG,CAAC;IACf,IAAI,CAACF,KAAK,GAAGA,KAAK;EACpB;EAEAC,OAAO,CAACE,SAAS,GAAG;IAClBhB,WAAW,EAAE,SAAAA,CAAA,EAAW;MACtB,IAAI,EAAE,IAAI,CAACe,MAAM,KAAK,CAAC,EAAE,IAAI,CAAClD,KAAK,CAACiC,OAAO,GAAG,IAAI,EAAE,IAAI,CAACmB,QAAQ,GAAG,IAAI;MACxE,OAAO,IAAI;IACb,CAAC;IACDhB,KAAK,EAAE,SAAAA,CAAS1D,KAAK,EAAE2E,IAAI,EAAE;MAC3B,IAAI,IAAI,CAACD,QAAQ,EAAE,IAAI,CAACA,QAAQ,GAAG,KAAK,EAAE,IAAI,CAACb,IAAI,CAAC,OAAO,EAAE7D,KAAK,EAAE2E,IAAI,CAAC,CAAC,KACrE,IAAI,CAACd,IAAI,CAAC,OAAO,EAAE7D,KAAK,CAAC;MAC9B,OAAO,IAAI;IACb,CAAC;IACDyB,KAAK,EAAE,SAAAA,CAASzB,KAAK,EAAE2E,IAAI,EAAE;MAC3B,IAAI,CAACd,IAAI,CAAC,OAAO,EAAE7D,KAAK,EAAE2E,IAAI,CAAC;MAC/B,OAAO,IAAI;IACb,CAAC;IACDhB,GAAG,EAAE,SAAAA,CAAS3D,KAAK,EAAE2E,IAAI,EAAE;MACzB,IAAI,EAAE,IAAI,CAACH,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI,CAAClD,KAAK,CAACiC,OAAO,EAAE,IAAI,CAACM,IAAI,CAAC,KAAK,EAAE7D,KAAK,EAAE2E,IAAI,CAAC;MACjF,OAAO,IAAI;IACb,CAAC;IACDd,IAAI,EAAE,SAAAA,CAASrF,IAAI,EAAEwB,KAAK,EAAE2E,IAAI,EAAE;MAChC,IAAI/B,CAAC,GAAGzF,MAAM,CAAC,IAAI,CAACyG,IAAI,CAAC,CAACgB,KAAK,CAAC,CAAC;MACjC9C,SAAS,CAACoC,IAAI,CACZ1F,IAAI,EACJ,IAAI,CAACoF,IAAI,EACT,IAAItG,UAAU,CAACkB,IAAI,EAAE;QACnBqG,WAAW,EAAE7E,KAAK;QAClB8E,MAAM,EAAErD,KAAK;QACbvC,SAAS,EAAEqC,GAAG,CAAC5C,MAAM,CAAC,IAAI,CAAC2C,KAAK,CAACpC,SAAS,CAAC;QAC3CyF,IAAI;QACJ7H,QAAQ,EAAEgF;MACZ,CAAC,CAAC,EACFc,CACF,CAAC;IACH;EACF,CAAC;EAED,SAASK,OAAOA,CAACjD,KAAK,EAAE;IACtB,IAAIgC,WAAW,IAAI,CAAChC,KAAK,CAAC+E,OAAO,EAAE;IACnC,IAAI,CAACpD,MAAM,CAACqC,KAAK,CAAC,IAAI,EAAER,SAAS,CAAC,EAAE;IAEpC,IAAII,IAAI,GAAG,IAAI;MACXpF,IAAI,GAAGwB,KAAK,CAAC8E,MAAM,CAACE,QAAQ,CAACxG,IAAI;MACjCmG,IAAI,GAAG,CAAC9C,IAAI,IAAI7B,KAAK,CAACiF,OAAO,GAAGzG,IAAI,GAAG,SAAS,GAAGA,IAAI,MAAM,WAAW,GAAGf,SAAS,GAAIoE,IAAI,IAAI7B,KAAK,CAACkF,MAAM,GAAGrH,WAAW,GAAGD,WAAY;MACzIuH,KAAK,GAAG5D,GAAG,KAAK1C,CAAC,GAAG,IAAI,GAAGe,MAAM,CAACpB,IAAI,CAAC;MACvC4G,KAAK,GAAG7D,GAAG,KAAKlD,CAAC,GAAG,IAAI,GAAGwB,MAAM,CAACrB,IAAI,CAAC;MACvC8C,KAAK,GAAGP,KAAK,CAAC6C,IAAI,CAAC;MACnBxC,MAAM,GAAGE,KAAK,CAACF,MAAM;MACrBlC,SAAS,GAAGoC,KAAK,CAACpC,SAAS;MAC3BmG,CAAC,GAAGjE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAAEkE,EAAE;MAAEC,EAAE;MACxBC,CAAC,GAAGpE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAAEqE,EAAE;MAAEC,EAAE;MACxBC,CAAC,GAAGvE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAAEwE,EAAE;MAAEC,EAAE;MACxBC,CAAC,GAAG1E,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAAE2E,EAAE;MAAEC,EAAE;MACxBC,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACNC,MAAM;MACNC,QAAQ,GAAGjB,KAAK,IAAIC,KAAK,IAAIvD,IAAI,IAAI7B,KAAK,CAACqG,QAAQ;MACnDC,KAAK;MACLC,KAAK;MACLC,MAAM,GAAGC,KAAK,CAACC,IAAI,CAAC1G,KAAK,CAAC+E,OAAO,IAAI,CAAC/E,KAAK,CAAC,EAAEF,CAAC,IAAI;QACjD,MAAMmE,CAAC,GAAGnE,CAAC,CAAC6G,UAAU;QACtB7G,CAAC,GAAG5C,OAAO,CAAC4C,CAAC,EAAE8D,IAAI,CAAC;QACpB9D,CAAC,CAAC8G,MAAM,GAAG9G,CAAC,CAAC+G,KAAK,CAAC,CAAC;QACpB/G,CAAC,CAAC6G,UAAU,GAAG1C,CAAC;QAChB,OAAOnE,CAAC;MACV,CAAC,CAAC;IAEN1C,SAAS,CAACwG,IAAI,CAAC;IACf,IAAIC,IAAI,GAAGN,OAAO,CAACK,IAAI,EAAEJ,SAAS,EAAE,IAAI,CAAC,CAACC,WAAW,CAAC,CAAC;IAEvD,IAAIjF,IAAI,KAAK,SAAS,EAAE;MACtB,IAAIU,SAAS,EAAEiH,MAAM,GAAG,IAAI;MAC5B,MAAMW,GAAG,GAAG,CAACN,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,CAAC;MAC/ClF,KAAK,CAACpC,SAAS,GAAGA,SAAS,GAAG,CAAC,CAC3BoG,EAAE,GAAG/D,GAAG,KAAK1C,CAAC,GAAGwG,CAAC,GAAGrH,GAAG,CAAC8I,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAC9CrB,EAAE,GAAGlE,GAAG,KAAKlD,CAAC,GAAGmH,CAAC,GAAGxH,GAAG,CAAC8I,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAC/C,EAAE,CACDlB,EAAE,GAAGrE,GAAG,KAAK1C,CAAC,GAAG8G,CAAC,GAAG5H,GAAG,CAAC+I,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAC9Cf,EAAE,GAAGxE,GAAG,KAAKlD,CAAC,GAAGyH,CAAC,GAAG/H,GAAG,CAAC+I,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAC/C,CAAC;MACJ,IAAIN,MAAM,CAACnC,MAAM,GAAG,CAAC,EAAEhB,IAAI,CAACrD,KAAK,CAAC;IACpC,CAAC,MAAM;MACLsF,EAAE,GAAGpG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpBuG,EAAE,GAAGvG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB0G,EAAE,GAAG1G,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB6G,EAAE,GAAG7G,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB;IAEAqG,EAAE,GAAGD,EAAE;IACPI,EAAE,GAAGD,EAAE;IACPI,EAAE,GAAGD,EAAE;IACPI,EAAE,GAAGD,EAAE;IAEP,IAAI9D,KAAK,GAAG9E,MAAM,CAACyG,IAAI,CAAC,CACnBpB,IAAI,CAAC,gBAAgB,EAAE,MAAM,CAAC;IAEnC,IAAIvD,OAAO,GAAGgD,KAAK,CAACG,SAAS,CAAC,UAAU,CAAC,CACpCI,IAAI,CAAC,QAAQ,EAAExD,OAAO,CAACR,IAAI,CAAC,CAAC;IAElC,IAAIwB,KAAK,CAAC+E,OAAO,EAAE;MACjBlB,IAAI,CAACkD,KAAK,GAAGA,KAAK;MAClBlD,IAAI,CAACmD,KAAK,GAAGA,KAAK;IACpB,CAAC,MAAM;MACL,IAAIC,IAAI,GAAG9J,MAAM,CAAC6C,KAAK,CAACiH,IAAI,CAAC,CACxBjE,EAAE,CAAC,iBAAiB,EAAE+D,KAAK,EAAE,IAAI,CAAC,CAClC/D,EAAE,CAAC,eAAe,EAAEgE,KAAK,EAAE,IAAI,CAAC;MACrC,IAAInF,IAAI,EAAEoF,IAAI,CACTjE,EAAE,CAAC,eAAe,EAAEkE,SAAS,EAAE,IAAI,CAAC,CACpClE,EAAE,CAAC,aAAa,EAAEmE,QAAQ,EAAE,IAAI,CAAC;MAEtCpK,WAAW,CAACiD,KAAK,CAACiH,IAAI,CAAC;IACzB;IAEAlE,MAAM,CAACmB,IAAI,CAACN,IAAI,CAAC;IACjBC,IAAI,CAACH,KAAK,CAAC1D,KAAK,EAAE2E,IAAI,CAACjH,IAAI,CAAC;IAE5B,SAASqJ,KAAKA,CAAC/G,KAAK,EAAE;MACpB,KAAK,MAAMoH,CAAC,IAAIpH,KAAK,CAACqH,cAAc,IAAI,CAACrH,KAAK,CAAC,EAAE;QAC/C,KAAK,MAAM4C,CAAC,IAAI4D,MAAM,EACpB,IAAI5D,CAAC,CAAC+D,UAAU,KAAKS,CAAC,CAACT,UAAU,EAAE/D,CAAC,CAAC0E,GAAG,GAAGpK,OAAO,CAACkK,CAAC,EAAExD,IAAI,CAAC;MAC/D;MACA,IAAIwC,QAAQ,IAAI,CAACE,KAAK,IAAI,CAACC,KAAK,IAAIC,MAAM,CAACnC,MAAM,KAAK,CAAC,EAAE;QACvD,MAAMkD,KAAK,GAAGf,MAAM,CAAC,CAAC,CAAC;QACvB,IAAI1I,GAAG,CAACyJ,KAAK,CAACD,GAAG,CAAC,CAAC,CAAC,GAAGC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGzJ,GAAG,CAACyJ,KAAK,CAACD,GAAG,CAAC,CAAC,CAAC,GAAGC,KAAK,CAAC,CAAC,CAAC,CAAC,EAC7DhB,KAAK,GAAG,IAAI,CAAC,KAEbD,KAAK,GAAG,IAAI;MAChB;MACA,KAAK,MAAMiB,KAAK,IAAIf,MAAM,EACxB,IAAIe,KAAK,CAACD,GAAG,EAAEC,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAACD,GAAG,CAAC,CAAC,CAAC,EAAEC,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAACD,GAAG,CAAC,CAAC,CAAC;MACjEnB,MAAM,GAAG,IAAI;MACb5I,OAAO,CAACyC,KAAK,CAAC;MACdqD,IAAI,CAACrD,KAAK,CAAC;IACb;IAEA,SAASqD,IAAIA,CAACrD,KAAK,EAAE;MACnB,MAAMuH,KAAK,GAAGf,MAAM,CAAC,CAAC,CAAC;QAAEI,MAAM,GAAGW,KAAK,CAACX,MAAM;MAC9C,IAAI9G,CAAC;MAELmG,EAAE,GAAGsB,KAAK,CAAC,CAAC,CAAC,GAAGX,MAAM,CAAC,CAAC,CAAC;MACzBV,EAAE,GAAGqB,KAAK,CAAC,CAAC,CAAC,GAAGX,MAAM,CAAC,CAAC,CAAC;MAEzB,QAAQjC,IAAI;QACV,KAAKhH,UAAU;QACf,KAAKF,SAAS;UAAE;YACd,IAAI0H,KAAK,EAAEc,EAAE,GAAGlI,GAAG,CAACsH,CAAC,GAAGC,EAAE,EAAEtH,GAAG,CAAC2H,CAAC,GAAGC,EAAE,EAAEK,EAAE,CAAC,CAAC,EAAEV,EAAE,GAAGD,EAAE,GAAGW,EAAE,EAAEJ,EAAE,GAAGD,EAAE,GAAGK,EAAE;YACxE,IAAIb,KAAK,EAAEc,EAAE,GAAGnI,GAAG,CAACyH,CAAC,GAAGC,EAAE,EAAEzH,GAAG,CAAC8H,CAAC,GAAGC,EAAE,EAAEG,EAAE,CAAC,CAAC,EAAER,EAAE,GAAGD,EAAE,GAAGS,EAAE,EAAEF,EAAE,GAAGD,EAAE,GAAGG,EAAE;YACxE;UACF;QACA,KAAKtI,WAAW;UAAE;YAChB,IAAI4I,MAAM,CAAC,CAAC,CAAC,EAAE;cACb,IAAIrB,KAAK,EAAEI,EAAE,GAAGxH,GAAG,CAACsH,CAAC,EAAErH,GAAG,CAAC2H,CAAC,EAAEa,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEX,EAAE,GAAG9H,GAAG,CAACsH,CAAC,EAAErH,GAAG,CAAC2H,CAAC,EAAEa,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAErB,KAAK,GAAG,CAAC;cAC1F,IAAIC,KAAK,EAAEM,EAAE,GAAG3H,GAAG,CAACyH,CAAC,EAAExH,GAAG,CAAC8H,CAAC,EAAEU,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAER,EAAE,GAAGjI,GAAG,CAACyH,CAAC,EAAExH,GAAG,CAAC8H,CAAC,EAAEU,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEpB,KAAK,GAAG,CAAC;YAC5F,CAAC,MAAM;cACL,IAAID,KAAK,GAAG,CAAC,EAAEc,EAAE,GAAGlI,GAAG,CAACsH,CAAC,GAAGC,EAAE,EAAEtH,GAAG,CAAC2H,CAAC,GAAGL,EAAE,EAAEW,EAAE,CAAC,CAAC,EAAEV,EAAE,GAAGD,EAAE,GAAGW,EAAE,EAAEJ,EAAE,GAAGD,EAAE,CAAC,KACnE,IAAIT,KAAK,GAAG,CAAC,EAAEc,EAAE,GAAGlI,GAAG,CAACsH,CAAC,GAAGO,EAAE,EAAE5H,GAAG,CAAC2H,CAAC,GAAGC,EAAE,EAAEK,EAAE,CAAC,CAAC,EAAEV,EAAE,GAAGD,EAAE,EAAEO,EAAE,GAAGD,EAAE,GAAGK,EAAE;cAC5E,IAAIb,KAAK,GAAG,CAAC,EAAEc,EAAE,GAAGnI,GAAG,CAACyH,CAAC,GAAGC,EAAE,EAAEzH,GAAG,CAAC8H,CAAC,GAAGL,EAAE,EAAES,EAAE,CAAC,CAAC,EAAER,EAAE,GAAGD,EAAE,GAAGS,EAAE,EAAEF,EAAE,GAAGD,EAAE,CAAC,KACnE,IAAIX,KAAK,GAAG,CAAC,EAAEc,EAAE,GAAGnI,GAAG,CAACyH,CAAC,GAAGO,EAAE,EAAE/H,GAAG,CAAC8H,CAAC,GAAGC,EAAE,EAAEG,EAAE,CAAC,CAAC,EAAER,EAAE,GAAGD,EAAE,EAAEO,EAAE,GAAGD,EAAE,GAAGG,EAAE;YAC9E;YACA;UACF;QACA,KAAKrI,WAAW;UAAE;YAChB,IAAIsH,KAAK,EAAEI,EAAE,GAAGxH,GAAG,CAACsH,CAAC,EAAErH,GAAG,CAAC2H,CAAC,EAAEL,EAAE,GAAGW,EAAE,GAAGd,KAAK,CAAC,CAAC,EAAEU,EAAE,GAAG9H,GAAG,CAACsH,CAAC,EAAErH,GAAG,CAAC2H,CAAC,EAAEC,EAAE,GAAGK,EAAE,GAAGd,KAAK,CAAC,CAAC;YACrF,IAAIC,KAAK,EAAEM,EAAE,GAAG3H,GAAG,CAACyH,CAAC,EAAExH,GAAG,CAAC8H,CAAC,EAAEL,EAAE,GAAGS,EAAE,GAAGd,KAAK,CAAC,CAAC,EAAEY,EAAE,GAAGjI,GAAG,CAACyH,CAAC,EAAExH,GAAG,CAAC8H,CAAC,EAAEC,EAAE,GAAGG,EAAE,GAAGd,KAAK,CAAC,CAAC;YACrF;UACF;MACF;MAEA,IAAIS,EAAE,GAAGN,EAAE,EAAE;QACXJ,KAAK,IAAI,CAAC,CAAC;QACXrF,CAAC,GAAGwF,EAAE,EAAEA,EAAE,GAAGM,EAAE,EAAEA,EAAE,GAAG9F,CAAC;QACvBA,CAAC,GAAGyF,EAAE,EAAEA,EAAE,GAAGM,EAAE,EAAEA,EAAE,GAAG/F,CAAC;QACvB,IAAItB,IAAI,IAAIkB,KAAK,EAAET,OAAO,CAACuD,IAAI,CAAC,QAAQ,EAAExD,OAAO,CAACR,IAAI,GAAGkB,KAAK,CAAClB,IAAI,CAAC,CAAC,CAAC;MACxE;MAEA,IAAIwH,EAAE,GAAGN,EAAE,EAAE;QACXN,KAAK,IAAI,CAAC,CAAC;QACXtF,CAAC,GAAG2F,EAAE,EAAEA,EAAE,GAAGM,EAAE,EAAEA,EAAE,GAAGjG,CAAC;QACvBA,CAAC,GAAG4F,EAAE,EAAEA,EAAE,GAAGM,EAAE,EAAEA,EAAE,GAAGlG,CAAC;QACvB,IAAItB,IAAI,IAAImB,KAAK,EAAEV,OAAO,CAACuD,IAAI,CAAC,QAAQ,EAAExD,OAAO,CAACR,IAAI,GAAGmB,KAAK,CAACnB,IAAI,CAAC,CAAC,CAAC;MACxE;MAEA,IAAI8C,KAAK,CAACpC,SAAS,EAAEA,SAAS,GAAGoC,KAAK,CAACpC,SAAS,CAAC,CAAC;MAClD,IAAIoH,KAAK,EAAEf,EAAE,GAAGrG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE2G,EAAE,GAAG3G,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrD,IAAIqH,KAAK,EAAEb,EAAE,GAAGxG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE8G,EAAE,GAAG9G,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAErD,IAAIA,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKqG,EAAE,IACnBrG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKwG,EAAE,IACtBxG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK2G,EAAE,IACtB3G,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK8G,EAAE,EAAE;QAC7B1E,KAAK,CAACpC,SAAS,GAAG,CAAC,CAACqG,EAAE,EAAEG,EAAE,CAAC,EAAE,CAACG,EAAE,EAAEG,EAAE,CAAC,CAAC;QACtCjD,MAAM,CAACmB,IAAI,CAACN,IAAI,CAAC;QACjBC,IAAI,CAACpC,KAAK,CAACzB,KAAK,EAAE2E,IAAI,CAACjH,IAAI,CAAC;MAC9B;IACF;IAEA,SAASsJ,KAAKA,CAAChH,KAAK,EAAE;MACpBxC,aAAa,CAACwC,KAAK,CAAC;MACpB,IAAIA,KAAK,CAAC+E,OAAO,EAAE;QACjB,IAAI/E,KAAK,CAAC+E,OAAO,CAACV,MAAM,EAAE;QAC1B,IAAIrC,WAAW,EAAEwF,YAAY,CAACxF,WAAW,CAAC;QAC1CA,WAAW,GAAGyF,UAAU,CAAC,YAAW;UAAEzF,WAAW,GAAG,IAAI;QAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;MACrE,CAAC,MAAM;QACLhF,UAAU,CAACgD,KAAK,CAACiH,IAAI,EAAEd,MAAM,CAAC;QAC9Bc,IAAI,CAACjE,EAAE,CAAC,yDAAyD,EAAE,IAAI,CAAC;MAC1E;MACAf,KAAK,CAACO,IAAI,CAAC,gBAAgB,EAAE,KAAK,CAAC;MACnCvD,OAAO,CAACuD,IAAI,CAAC,QAAQ,EAAExD,OAAO,CAACC,OAAO,CAAC;MACvC,IAAIqC,KAAK,CAACpC,SAAS,EAAEA,SAAS,GAAGoC,KAAK,CAACpC,SAAS,CAAC,CAAC;MAClD,IAAIiC,KAAK,CAACjC,SAAS,CAAC,EAAEoC,KAAK,CAACpC,SAAS,GAAG,IAAI,EAAE6D,MAAM,CAACmB,IAAI,CAACN,IAAI,CAAC;MAC/DC,IAAI,CAACF,GAAG,CAAC3D,KAAK,EAAE2E,IAAI,CAACjH,IAAI,CAAC;IAC5B;IAEA,SAASwJ,SAASA,CAAClH,KAAK,EAAE;MACxB,QAAQA,KAAK,CAAC0H,OAAO;QACnB,KAAK,EAAE;UAAE;YAAE;YACTtB,QAAQ,GAAGjB,KAAK,IAAIC,KAAK;YACzB;UACF;QACA,KAAK,EAAE;UAAE;YAAE;YACT,IAAIT,IAAI,KAAK/G,WAAW,EAAE;cACxB,IAAIuH,KAAK,EAAES,EAAE,GAAGC,EAAE,GAAGI,EAAE,GAAGd,KAAK,EAAEG,EAAE,GAAGC,EAAE,GAAGU,EAAE,GAAGd,KAAK;cACrD,IAAIC,KAAK,EAAEW,EAAE,GAAGC,EAAE,GAAGE,EAAE,GAAGd,KAAK,EAAEK,EAAE,GAAGC,EAAE,GAAGQ,EAAE,GAAGd,KAAK;cACrDT,IAAI,GAAG9G,WAAW;cAClBwF,IAAI,CAACrD,KAAK,CAAC;YACb;YACA;UACF;QACA,KAAK,EAAE;UAAE;YAAE;YACT,IAAI2E,IAAI,KAAK/G,WAAW,IAAI+G,IAAI,KAAK9G,WAAW,EAAE;cAChD,IAAIsH,KAAK,GAAG,CAAC,EAAES,EAAE,GAAGC,EAAE,GAAGI,EAAE,CAAC,KAAM,IAAId,KAAK,GAAG,CAAC,EAAEG,EAAE,GAAGC,EAAE,GAAGU,EAAE;cAC7D,IAAIb,KAAK,GAAG,CAAC,EAAEW,EAAE,GAAGC,EAAE,GAAGE,EAAE,CAAC,KAAM,IAAId,KAAK,GAAG,CAAC,EAAEK,EAAE,GAAGC,EAAE,GAAGQ,EAAE;cAC7DvB,IAAI,GAAGhH,UAAU;cACjBsB,OAAO,CAACuD,IAAI,CAAC,QAAQ,EAAExD,OAAO,CAACE,SAAS,CAAC;cACzCmE,IAAI,CAACrD,KAAK,CAAC;YACb;YACA;UACF;QACA;UAAS;MACX;MACAzC,OAAO,CAACyC,KAAK,CAAC;IAChB;IAEA,SAASmH,QAAQA,CAACnH,KAAK,EAAE;MACvB,QAAQA,KAAK,CAAC0H,OAAO;QACnB,KAAK,EAAE;UAAE;YAAE;YACT,IAAItB,QAAQ,EAAE;cACZE,KAAK,GAAGC,KAAK,GAAGH,QAAQ,GAAG,KAAK;cAChC/C,IAAI,CAACrD,KAAK,CAAC;YACb;YACA;UACF;QACA,KAAK,EAAE;UAAE;YAAE;YACT,IAAI2E,IAAI,KAAK9G,WAAW,EAAE;cACxB,IAAIsH,KAAK,GAAG,CAAC,EAAES,EAAE,GAAGC,EAAE,CAAC,KAAM,IAAIV,KAAK,GAAG,CAAC,EAAEG,EAAE,GAAGC,EAAE;cACnD,IAAIH,KAAK,GAAG,CAAC,EAAEW,EAAE,GAAGC,EAAE,CAAC,KAAM,IAAIZ,KAAK,GAAG,CAAC,EAAEK,EAAE,GAAGC,EAAE;cACnDf,IAAI,GAAG/G,WAAW;cAClByF,IAAI,CAACrD,KAAK,CAAC;YACb;YACA;UACF;QACA,KAAK,EAAE;UAAE;YAAE;YACT,IAAI2E,IAAI,KAAKhH,UAAU,EAAE;cACvB,IAAIqC,KAAK,CAACkF,MAAM,EAAE;gBAChB,IAAIC,KAAK,EAAES,EAAE,GAAGC,EAAE,GAAGI,EAAE,GAAGd,KAAK,EAAEG,EAAE,GAAGC,EAAE,GAAGU,EAAE,GAAGd,KAAK;gBACrD,IAAIC,KAAK,EAAEW,EAAE,GAAGC,EAAE,GAAGE,EAAE,GAAGd,KAAK,EAAEK,EAAE,GAAGC,EAAE,GAAGQ,EAAE,GAAGd,KAAK;gBACrDT,IAAI,GAAG9G,WAAW;cACpB,CAAC,MAAM;gBACL,IAAIsH,KAAK,GAAG,CAAC,EAAES,EAAE,GAAGC,EAAE,CAAC,KAAM,IAAIV,KAAK,GAAG,CAAC,EAAEG,EAAE,GAAGC,EAAE;gBACnD,IAAIH,KAAK,GAAG,CAAC,EAAEW,EAAE,GAAGC,EAAE,CAAC,KAAM,IAAIZ,KAAK,GAAG,CAAC,EAAEK,EAAE,GAAGC,EAAE;gBACnDf,IAAI,GAAG/G,WAAW;cACpB;cACAqB,OAAO,CAACuD,IAAI,CAAC,QAAQ,EAAExD,OAAO,CAACR,IAAI,CAAC,CAAC;cACrC6E,IAAI,CAACrD,KAAK,CAAC;YACb;YACA;UACF;QACA;UAAS;MACX;MACAzC,OAAO,CAACyC,KAAK,CAAC;IAChB;EACF;EAEA,SAASkD,UAAUA,CAAClD,KAAK,EAAE;IACzBuD,OAAO,CAAC,IAAI,EAAEC,SAAS,CAAC,CAACuD,KAAK,CAAC/G,KAAK,CAAC;EACvC;EAEA,SAASmD,UAAUA,CAACnD,KAAK,EAAE;IACzBuD,OAAO,CAAC,IAAI,EAAEC,SAAS,CAAC,CAACwD,KAAK,CAAChH,KAAK,CAAC;EACvC;EAEA,SAASmC,UAAUA,CAAA,EAAG;IACpB,IAAIb,KAAK,GAAG,IAAI,CAACL,OAAO,IAAI;MAAC/B,SAAS,EAAE;IAAI,CAAC;IAC7CoC,KAAK,CAACF,MAAM,GAAGhD,OAAO,CAACgD,MAAM,CAAC4C,KAAK,CAAC,IAAI,EAAER,SAAS,CAAC,CAAC;IACrDlC,KAAK,CAACC,GAAG,GAAGA,GAAG;IACf,OAAOD,KAAK;EACd;EAEAG,KAAK,CAACL,MAAM,GAAG,UAASuG,CAAC,EAAE;IACzB,OAAOnE,SAAS,CAACa,MAAM,IAAIjD,MAAM,GAAG,OAAOuG,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGtK,QAAQ,CAACe,OAAO,CAACuJ,CAAC,CAAC,CAAC,EAAElG,KAAK,IAAIL,MAAM;EACzG,CAAC;EAEDK,KAAK,CAACE,MAAM,GAAG,UAASgG,CAAC,EAAE;IACzB,OAAOnE,SAAS,CAACa,MAAM,IAAI1C,MAAM,GAAG,OAAOgG,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGtK,QAAQ,CAAC,CAAC,CAACsK,CAAC,CAAC,EAAElG,KAAK,IAAIE,MAAM;EAClG,CAAC;EAEDF,KAAK,CAACG,SAAS,GAAG,UAAS+F,CAAC,EAAE;IAC5B,OAAOnE,SAAS,CAACa,MAAM,IAAIzC,SAAS,GAAG,OAAO+F,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGtK,QAAQ,CAAC,CAAC,CAACsK,CAAC,CAAC,EAAElG,KAAK,IAAIG,SAAS;EACxG,CAAC;EAEDH,KAAK,CAACM,UAAU,GAAG,UAAS4F,CAAC,EAAE;IAC7B,OAAOnE,SAAS,CAACa,MAAM,IAAItC,UAAU,GAAG,CAAC4F,CAAC,EAAElG,KAAK,IAAIM,UAAU;EACjE,CAAC;EAEDN,KAAK,CAACmG,YAAY,GAAG,UAASD,CAAC,EAAE;IAC/B,OAAOnE,SAAS,CAACa,MAAM,IAAIxC,IAAI,GAAG,CAAC,CAAC8F,CAAC,EAAElG,KAAK,IAAII,IAAI;EACtD,CAAC;EAEDJ,KAAK,CAACuB,EAAE,GAAG,YAAW;IACpB,IAAIrC,KAAK,GAAGmB,SAAS,CAACkB,EAAE,CAACgB,KAAK,CAAClC,SAAS,EAAE0B,SAAS,CAAC;IACpD,OAAO7C,KAAK,KAAKmB,SAAS,GAAGL,KAAK,GAAGd,KAAK;EAC5C,CAAC;EAED,OAAOc,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}