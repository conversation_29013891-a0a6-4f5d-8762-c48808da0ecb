{"ast": null, "code": "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\nexport var scheme = new Array(3).concat(\"d8b365f5f5f55ab4ac\", \"a6611adfc27d80cdc1018571\", \"a6611adfc27df5f5f580cdc1018571\", \"8c510ad8b365f6e8c3c7eae55ab4ac01665e\", \"8c510ad8b365f6e8c3f5f5f5c7eae55ab4ac01665e\", \"8c510abf812ddfc27df6e8c3c7eae580cdc135978f01665e\", \"8c510abf812ddfc27df6e8c3f5f5f5c7eae580cdc135978f01665e\", \"5430058c510abf812ddfc27df6e8c3c7eae580cdc135978f01665e003c30\", \"5430058c510abf812ddfc27df6e8c3f5f5f5c7eae580cdc135978f01665e003c30\").map(colors);\nexport default ramp(scheme);", "map": {"version": 3, "names": ["colors", "ramp", "scheme", "Array", "concat", "map"], "sources": ["C:/console/aava-ui-web/node_modules/d3-scale-chromatic/src/diverging/BrBG.js"], "sourcesContent": ["import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"d8b365f5f5f55ab4ac\",\n  \"a6611adfc27d80cdc1018571\",\n  \"a6611adfc27df5f5f580cdc1018571\",\n  \"8c510ad8b365f6e8c3c7eae55ab4ac01665e\",\n  \"8c510ad8b365f6e8c3f5f5f5c7eae55ab4ac01665e\",\n  \"8c510abf812ddfc27df6e8c3c7eae580cdc135978f01665e\",\n  \"8c510abf812ddfc27df6e8c3f5f5f5c7eae580cdc135978f01665e\",\n  \"5430058c510abf812ddfc27df6e8c3c7eae580cdc135978f01665e003c30\",\n  \"5430058c510abf812ddfc27df6e8c3f5f5f5c7eae580cdc135978f01665e003c30\"\n).map(colors);\n\nexport default ramp(scheme);\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,cAAc;AACjC,OAAOC,IAAI,MAAM,YAAY;AAE7B,OAAO,IAAIC,MAAM,GAAG,IAAIC,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,CACrC,oBAAoB,EACpB,0BAA0B,EAC1B,gCAAgC,EAChC,sCAAsC,EACtC,4CAA4C,EAC5C,kDAAkD,EAClD,wDAAwD,EACxD,8DAA8D,EAC9D,oEACF,CAAC,CAACC,GAAG,CAACL,MAAM,CAAC;AAEb,eAAeC,IAAI,CAACC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}