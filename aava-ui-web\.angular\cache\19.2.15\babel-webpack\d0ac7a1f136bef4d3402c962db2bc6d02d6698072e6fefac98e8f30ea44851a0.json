{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';\nimport { CommonModule, formatDate } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { PopupComponent, ConfirmationPopupComponent } from '@ava/play-comp-library';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { startWith, debounceTime, distinctUntilChanged, map } from 'rxjs';\nimport approvalText from './constants/approval.json';\nlet ApprovalComponent = class ApprovalComponent {\n  router;\n  route;\n  apiService;\n  approvalService;\n  fb;\n  // Labels from constants file\n  appLabels = approvalText.labels;\n  searchValue = '';\n  totalApprovedApprovals = 20;\n  totalPendingApprovals = 15;\n  totalApprovals = 60;\n  isBasicCollapsed = false;\n  quickActionsExpanded = true;\n  consoleApproval = {};\n  options = [];\n  basicSidebarItems = [];\n  quickActions = [];\n  toolReviews = [];\n  workflowReviews = [];\n  agentsReviews = [];\n  currentToolsPage = 1;\n  currentAgentsPage = 1;\n  currentWorkflowsPage = 1;\n  pageSize = 50;\n  totalRecords = 0;\n  isDeleted = false;\n  currentTab = 'Agents';\n  showToolApprovalPopup = false;\n  showInfoPopup = false;\n  showErrorPopup = false;\n  infoMessage = '';\n  selectedIndex = 0;\n  showFeedbackPopup = false;\n  searchForm;\n  labels = approvalText.labels;\n  approvedAgentId = null;\n  constructor(router, route, apiService, approvalService, fb) {\n    this.router = router;\n    this.route = route;\n    this.apiService = apiService;\n    this.approvalService = approvalService;\n    this.fb = fb;\n    this.labels = approvalText.labels;\n    this.options = [{\n      name: this.labels.electronics,\n      value: 'electronics'\n    }, {\n      name: this.labels.clothing,\n      value: 'clothing'\n    }, {\n      name: this.labels.books,\n      value: 'books'\n    }];\n    this.basicSidebarItems = [{\n      id: '1',\n      icon: 'hammer',\n      text: this.labels.agents,\n      route: '',\n      active: true\n    }, {\n      id: '2',\n      icon: 'circle-check',\n      text: this.labels.workflows,\n      route: ''\n    }, {\n      id: '3',\n      icon: 'bot',\n      text: this.labels.tools,\n      route: ''\n    }];\n    this.quickActions = [{\n      icon: 'awe_agents',\n      label: this.labels.agents,\n      route: ''\n    }, {\n      icon: 'awe_workflows',\n      label: this.labels.workflows,\n      route: ''\n    }, {\n      icon: 'awe_tools',\n      label: this.labels.tools,\n      route: ''\n    }];\n    this.searchForm = this.fb.group({\n      search: ['']\n    });\n  }\n  ngOnInit() {\n    this.searchList();\n    this.totalApprovals = 60;\n  }\n  searchList() {\n    console.log(this.searchForm.get('search')?.value);\n    this.searchForm.get('search').valueChanges.pipe(startWith(''), debounceTime(300), distinctUntilChanged(), map(value => value?.toLowerCase() ?? '')).subscribe(searchText => {\n      this.applyFilter(searchText);\n    });\n  }\n  applyFilter(text) {\n    const lower = text;\n    if (!this.searchValue) {\n      if (this.currentTab === 'Agents') {\n        this.updateConsoleApproval(this.agentsReviews, 'agent');\n      } else if (this.currentTab === 'Tools') {\n        this.updateConsoleApproval(this.toolReviews, 'tool');\n      } else {\n        this.updateConsoleApproval(this.workflowReviews, 'workflow');\n      }\n      return;\n    }\n    if (this.currentTab === 'Agents') {\n      const filtered = this.agentsReviews.filter(item => item.agentName?.toLowerCase().includes(lower));\n      this.updateConsoleApproval(filtered, 'agent');\n    } else if (this.currentTab === 'Tools') {\n      const filtered = this.toolReviews.filter(item => item.toolName?.toLowerCase().includes(lower));\n      this.updateConsoleApproval(filtered, 'tool');\n    } else {\n      const filtered = this.workflowReviews.filter(item => item.name?.toLowerCase().includes(lower));\n      this.updateConsoleApproval(filtered, 'workflow');\n    }\n  }\n  onSelectionChange(data) {\n    console.log('Selection changed:', data);\n  }\n  uClick(i) {\n    console.log('log' + i);\n  }\n  toggleQuickActions() {\n    this.quickActionsExpanded = !this.quickActionsExpanded;\n  }\n  onBasicCollapseToggle(isCollapsed) {\n    this.isBasicCollapsed = isCollapsed;\n    console.log('Basic sidebar collapsed:', isCollapsed);\n  }\n  onBasicItemClick(item) {\n    this.basicSidebarItems.forEach(i => i.active = false);\n    item.active = true;\n    console.log(item);\n  }\n  toRequestStatus(value) {\n    return value === 'approved' || value === 'rejected' || value === 'review' ? value : 'review';\n  }\n  loadToolReviews() {\n    this.approvalService.getAllReviewTools(this.currentToolsPage, this.pageSize, this.isDeleted).subscribe(response => {\n      if (this.currentToolsPage > 1) {\n        this.toolReviews = [...this.toolReviews, ...response.userToolReviewDetails];\n      } else {\n        this.toolReviews = response?.userToolReviewDetails;\n      }\n      this.toolReviews = this.toolReviews.filter(r => r.status !== 'approved');\n      this.totalRecords = this.toolReviews.length;\n      this.updateConsoleApproval(this.toolReviews, 'tool');\n    });\n  }\n  loadWorkflowReviews() {\n    this.approvalService.getAllReviewWorkflows(this.currentWorkflowsPage, this.pageSize, this.isDeleted).subscribe(response => {\n      if (this.currentWorkflowsPage > 1) {\n        this.workflowReviews = [...this.workflowReviews, ...response.workflowReviewDetails];\n      } else {\n        this.workflowReviews = response?.workflowReviewDetails;\n      }\n      this.workflowReviews = this.workflowReviews.filter(r => r.status !== 'approved');\n      this.totalRecords = this.workflowReviews.length;\n      console.log('reviews ', this.workflowReviews);\n      this.updateConsoleApproval(this.workflowReviews, 'workflow');\n    });\n  }\n  loadAgentsReviews() {\n    this.approvalService.getAllReviewAgents(this.currentAgentsPage, this.pageSize, this.isDeleted).subscribe(response => {\n      if (this.currentAgentsPage > 1) {\n        this.agentsReviews = [...this.agentsReviews, ...response.agentReviewDetails];\n      } else {\n        this.agentsReviews = response?.agentReviewDetails;\n      }\n      this.agentsReviews = this.agentsReviews.filter(r => r.status !== 'approved');\n      this.totalRecords = this.agentsReviews.length;\n      this.updateConsoleApproval(this.agentsReviews, 'agent');\n    });\n  }\n  loadMoreTools(page) {\n    this.currentToolsPage = page;\n    this.loadToolReviews();\n  }\n  loadMoreAgents(page) {\n    this.currentAgentsPage = page;\n    this.loadAgentsReviews();\n  }\n  loadMoreWorkflows(page) {\n    this.currentWorkflowsPage = page;\n    this.loadWorkflowReviews();\n  }\n  loadReviews(name) {\n    this.currentTab = name;\n    if (name == 'Tools') {\n      this.loadToolReviews();\n    } else if (name == 'Agents') {\n      this.loadAgentsReviews();\n    } else {\n      this.loadWorkflowReviews();\n    }\n  }\n  redirectToListOfApproval(name) {\n    this.currentTab = name;\n    if (name === this.labels.tools) {\n      this.router.navigate(['approval-tools'], {\n        relativeTo: this.route\n      });\n    } else if (name === this.labels.agents) {\n      this.router.navigate(['approval-agents'], {\n        relativeTo: this.route\n      });\n    } else if (name === this.labels.workflows) {\n      this.router.navigate(['approval-workflows'], {\n        relativeTo: this.route\n      });\n    }\n  }\n  rejectApproval(idx) {\n    console.log(idx);\n    this.selectedIndex = idx;\n    this.showFeedbackPopup = true;\n  }\n  approveApproval(idx) {\n    console.log(idx);\n    this.selectedIndex = idx;\n    this.showToolApprovalPopup = true;\n  }\n  handleTesting(index) {\n    console.log(index);\n    if (this.currentTab == 'Tools') {\n      const toolId = this.toolReviews[index].toolId;\n      this.redirectToToolPlayground(toolId);\n    } else if (this.currentTab == 'Agents') {\n      const agentId = this.agentsReviews[index].agentId;\n      this.redirectToAgentsPlayground(agentId, 'collaborative');\n    } else {\n      const workflowId = this.workflowReviews[index].workflowId;\n      this.redirectToWorkflowPlayground(workflowId);\n    }\n  }\n  redirectToToolPlayground(id) {\n    this.router.navigate(['/libraries/tools/execute', id]);\n  }\n  redirectToWorkflowPlayground(id) {\n    this.router.navigate(['/build/workflows/execute', id]);\n  }\n  redirectToAgentsPlayground(id, type) {\n    this.router.navigate(['/build/agents', type, 'execute'], {\n      queryParams: {\n        id: id\n      }\n    });\n  }\n  handleApproval() {\n    if (this.currentTab == 'Tools') {\n      this.handleToolApproval();\n    } else if (this.currentTab == 'Agents') {\n      this.handleAgentApproval();\n    } else {\n      this.handleWorkflowApproval();\n    }\n  }\n  handleRejection(feedback) {\n    if (this.currentTab == 'Tools') {\n      this.handleToolRejection(feedback);\n    } else if (this.currentTab == 'Agents') {\n      this.handleAgentRejection(feedback);\n    } else {\n      this.handleWorkflowRejection(feedback);\n    }\n  }\n  handleToolApproval() {\n    const toolDetails = this.toolReviews[this.selectedIndex];\n    const id = toolDetails.id;\n    const toolId = toolDetails.toolId;\n    const status = 'approved';\n    const reviewedBy = toolDetails.reviewedBy;\n    this.approvalService.approveTool(id, toolId, status, reviewedBy).subscribe({\n      next: response => {\n        this.infoMessage = response?.message || this.labels.toolSuccessApproveMessage;\n        this.showInfoPopup = true;\n      },\n      error: error => {\n        this.showErrorPopup = true;\n        this.infoMessage = error?.error?.message || this.labels.defaultErrorMessage;\n        console.error('Error:', error);\n      }\n    });\n  }\n  handleToolRejection(feedback) {\n    const toolDetails = this.toolReviews[this.selectedIndex];\n    const id = toolDetails.id;\n    const toolId = toolDetails.toolId;\n    const status = 'rejected';\n    const reviewedBy = toolDetails.reviewedBy;\n    const message = feedback;\n    this.approvalService.rejectTool(id, toolId, status, reviewedBy, message).subscribe({\n      next: response => {\n        this.infoMessage = response?.message || this.labels.toolSuccessRejectMessage;\n        this.showInfoPopup = true;\n      },\n      error: error => {\n        this.showErrorPopup = true;\n        this.infoMessage = error?.error?.message || this.labels.defaultErrorMessage;\n        console.error('Error:', error);\n      }\n    });\n  }\n  handleAgentApproval() {\n    const agentDetails = this.agentsReviews[this.selectedIndex];\n    const id = agentDetails.id;\n    const agentId = agentDetails.agentId;\n    const status = 'approved';\n    const reviewedBy = agentDetails.reviewedBy;\n    this.approvalService.approveAgent(id, agentId, status, reviewedBy).subscribe({\n      next: response => {\n        this.infoMessage = response?.message || this.labels.agentSuccessApproveMessage;\n        this.showInfoPopup = true;\n        // Store agent ID for navigation after popup confirmation\n        this.approvedAgentId = agentId;\n      },\n      error: error => {\n        this.showErrorPopup = true;\n        this.infoMessage = error?.error?.message || this.labels.defaultErrorMessage;\n        console.error('Error:', error);\n      }\n    });\n  }\n  handleAgentRejection(feedback) {\n    const agentDetails = this.agentsReviews[this.selectedIndex];\n    const id = agentDetails.id;\n    const agentId = agentDetails.agentId;\n    const status = 'rejected';\n    const reviewedBy = agentDetails.reviewedBy;\n    const message = feedback;\n    this.approvalService.rejectAgent(id, agentId, status, reviewedBy, message).subscribe({\n      next: response => {\n        this.infoMessage = response?.message || this.labels.agentSuccessRejectMessage;\n        this.showInfoPopup = true;\n      },\n      error: error => {\n        this.showErrorPopup = true;\n        this.infoMessage = error?.error?.message || this.labels.defaultErrorMessage;\n        console.error('Error:', error);\n      }\n    });\n  }\n  handleWorkflowApproval() {\n    const workflowDetails = this.workflowReviews[this.selectedIndex];\n    const id = workflowDetails?.id;\n    const workflowId = workflowDetails?.workflowId;\n    const status = 'approved';\n    const reviewedBy = workflowDetails?.reviewedBy;\n    console.log(id, workflowId, status, reviewedBy);\n    this.approvalService.approveWorkflow(id, workflowId, status, reviewedBy).subscribe({\n      next: response => {\n        this.infoMessage = response?.message || this.labels.workflowSuccessApproveMessage;\n        this.showInfoPopup = true;\n      },\n      error: error => {\n        this.showErrorPopup = true;\n        this.infoMessage = error?.error?.message || this.labels.defaultErrorMessage;\n        console.error('Error:', error);\n      }\n    });\n  }\n  handleWorkflowRejection(feedback) {\n    const workflowDetails = this.workflowReviews[this.selectedIndex];\n    const id = workflowDetails?.id;\n    const workflowId = workflowDetails?.workflowId;\n    const status = 'rejected';\n    const reviewedBy = workflowDetails?.reviewedBy;\n    const message = feedback;\n    console.log(id, workflowId, status, reviewedBy, message);\n    this.approvalService.rejectWorkflow(id, workflowId, status, reviewedBy, message).subscribe({\n      next: response => {\n        this.infoMessage = response?.message || this.labels.workflowSuccessRejectMessage;\n        this.showInfoPopup = true;\n      },\n      error: error => {\n        this.showErrorPopup = true;\n        this.infoMessage = error?.error?.message || this.labels.defaultErrorMessage;\n        console.error('Error:', error);\n      }\n    });\n  }\n  handleInfoPopup() {\n    this.showInfoPopup = false;\n    // Check if we need to navigate to build agent screen after approval\n    if (this.currentTab == 'Agents' && this.approvedAgentId) {\n      // Navigate to build agent screen with the approved agent ID\n      // Route structure: /build/agents/collaborative with query params\n      this.router.navigate(['/build/agents/collaborative'], {\n        queryParams: {\n          id: this.approvedAgentId,\n          mode: 'edit'\n        }\n      });\n      // Reset the approved agent ID\n      this.approvedAgentId = null;\n      return;\n    }\n    if (this.currentTab == 'Tools') {\n      this.loadToolReviews();\n    } else if (this.currentTab == 'Agents') {\n      this.loadAgentsReviews();\n    } else {\n      this.loadWorkflowReviews();\n    }\n  }\n  updateConsoleApproval(data, type) {\n    this.consoleApproval = {\n      contents: data?.map(req => {\n        const statusIcons = {\n          approved: 'circle-check-big',\n          rejected: 'circle-x',\n          review: 'clock'\n        };\n        const statusTexts = {\n          approved: this.labels.approved,\n          rejected: this.labels.rejected,\n          review: this.labels.review\n        };\n        const statusKey = this.toRequestStatus(req?.status);\n        let specificId = 0;\n        let title = '';\n        if (type === 'tool') {\n          specificId = req.toolId;\n          title = req.toolName;\n        } else if (type === 'agent') {\n          specificId = req.agentId;\n          title = req.agentName;\n        } else {\n          specificId = req.workflowId;\n          title = req.workflowName;\n        }\n        return {\n          id: req.id,\n          refId: specificId,\n          type: type,\n          session1: {\n            title: title,\n            labels: [{\n              name: type,\n              color: 'success',\n              background: 'red',\n              type: 'normal'\n            }, {\n              name: req.changeRequestType,\n              color: req.changeRequestType === 'update' ? 'error' : 'info',\n              background: 'red',\n              type: 'pill'\n            }]\n          },\n          session2: [{\n            name: type,\n            color: 'default',\n            background: 'red',\n            type: 'normal'\n          }, {\n            name: req.status,\n            color: 'default',\n            background: 'red',\n            type: 'normal'\n          }],\n          session3: [{\n            iconName: 'user',\n            label: req.requestedBy\n          }, {\n            iconName: 'calendar-days',\n            label: formatDate(req?.requestedAt, 'dd MMM yyyy', 'en-IN')\n          }],\n          session4: {\n            status: statusTexts[statusKey],\n            iconName: statusIcons[statusKey]\n          }\n        };\n      }),\n      footer: {}\n    };\n  }\n};\nApprovalComponent = __decorate([Component({\n  selector: 'app-approval',\n  standalone: true,\n  imports: [CommonModule, RouterModule, PopupComponent, ConfirmationPopupComponent, ReactiveFormsModule],\n  schemas: [CUSTOM_ELEMENTS_SCHEMA],\n  templateUrl: './approval.component.html',\n  styleUrl: './approval.component.scss'\n})], ApprovalComponent);\nexport { ApprovalComponent };", "map": {"version": 3, "names": ["Component", "CUSTOM_ELEMENTS_SCHEMA", "CommonModule", "formatDate", "RouterModule", "PopupComponent", "ConfirmationPopupComponent", "ReactiveFormsModule", "startWith", "debounceTime", "distinctUntilChanged", "map", "approvalText", "ApprovalComponent", "router", "route", "apiService", "approvalService", "fb", "appLabels", "labels", "searchValue", "totalApprovedApprovals", "totalPendingApprovals", "totalApprovals", "isBasicCollapsed", "quickActionsExpanded", "consoleApproval", "options", "basicSidebarItems", "quickActions", "toolReviews", "workflowReviews", "agentsReviews", "currentToolsPage", "currentAgentsPage", "currentWorkflowsPage", "pageSize", "totalRecords", "isDeleted", "currentTab", "showToolApprovalPopup", "showInfoPopup", "showErrorPopup", "infoMessage", "selectedIndex", "showFeedbackPopup", "searchForm", "approvedAgentId", "constructor", "name", "electronics", "value", "clothing", "books", "id", "icon", "text", "agents", "active", "workflows", "tools", "label", "group", "search", "ngOnInit", "searchList", "console", "log", "get", "valueChanges", "pipe", "toLowerCase", "subscribe", "searchText", "applyFilter", "lower", "updateConsoleApproval", "filtered", "filter", "item", "<PERSON><PERSON><PERSON>", "includes", "toolName", "onSelectionChange", "data", "uClick", "i", "toggleQuickActions", "onBasicCollapseToggle", "isCollapsed", "onBasicItemClick", "for<PERSON>ach", "toRequestStatus", "loadToolReviews", "getAllReviewTools", "response", "userToolReviewDetails", "r", "status", "length", "loadWorkflowReviews", "getAllReviewWorkflows", "workflowReviewDetails", "loadAgentsReviews", "getAllReviewAgents", "agentReviewDetails", "loadMoreTools", "page", "loadMoreAgents", "loadMoreWorkflows", "loadReviews", "redirectToListOfApproval", "navigate", "relativeTo", "rejectApproval", "idx", "approveApproval", "handleTesting", "index", "toolId", "redirectToToolPlayground", "agentId", "redirectToAgentsPlayground", "workflowId", "redirectToWorkflowPlayground", "type", "queryParams", "handleApproval", "handleToolApproval", "handleAgentApproval", "handleWorkflowApproval", "handleRejection", "feedback", "handleToolRejection", "handleAgentRejection", "handleWorkflowRejection", "toolDetails", "reviewedBy", "approveTool", "next", "message", "toolSuccessApproveMessage", "error", "defaultErrorMessage", "rejectTool", "toolSuccessRejectMessage", "agentDetails", "approveAgent", "agentSuccessApproveMessage", "rejectAgent", "agentSuccessRejectMessage", "workflowDetails", "approveWorkflow", "workflowSuccessApproveMessage", "rejectWorkflow", "workflowSuccessRejectMessage", "handleInfoPopup", "mode", "contents", "req", "statusIcons", "approved", "rejected", "review", "statusTexts", "statusKey", "specificId", "title", "workflowName", "refId", "session1", "color", "background", "changeRequestType", "session2", "session3", "iconName", "requestedBy", "requestedAt", "session4", "footer", "__decorate", "selector", "standalone", "imports", "schemas", "templateUrl", "styleUrl"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\approval\\approval.component.ts"], "sourcesContent": ["import { Component, CUSTOM_ELEMENTS_SCHEMA, OnInit } from '@angular/core';\r\nimport { CommonModule, formatDate } from '@angular/common';\r\nimport {\r\n  ActivatedRoute,\r\n  NavigationEnd,\r\n  Router,\r\n  RouterModule,\r\n} from '@angular/router';\r\nimport {\r\n  ApprovalCardComponent,\r\n  AvaTextboxComponent,\r\n  DropdownComponent,\r\n  DropdownOption,\r\n  IconComponent,\r\n  TextCardComponent,\r\n  ButtonComponent,\r\n  PopupComponent,\r\n  ConfirmationPopupComponent,\r\n  AvaTagComponent,\r\n} from '@ava/play-comp-library';\r\nimport { ApprovalService } from '../../shared/services/approval.service';\r\nimport { SharedApiServiceService } from '../../shared/services/shared-api-service.service';\r\nimport { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';\r\nimport {\r\n  startWith,\r\n  debounceTime,\r\n  distinctUntilChanged,\r\n  map,\r\n  filter,\r\n} from 'rxjs';\r\nimport approvalText from './constants/approval.json';\r\n\r\nexport type RequestStatus = 'approved' | 'rejected' | 'review';\r\n@Component({\r\n  selector: 'app-approval',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    RouterModule,\r\n    PopupComponent,\r\n    ConfirmationPopupComponent,\r\n    ReactiveFormsModule,\r\n  ],\r\n  schemas: [CUSTOM_ELEMENTS_SCHEMA],\r\n  templateUrl: './approval.component.html',\r\n  styleUrl: './approval.component.scss',\r\n})\r\nexport class ApprovalComponent implements OnInit {\r\n  // Labels from constants file\r\n  appLabels = approvalText.labels;\r\n\r\n  public searchValue: string = '';\r\n  public totalApprovedApprovals: number = 20;\r\n  public totalPendingApprovals: number = 15;\r\n  public totalApprovals: number = 60;\r\n  public isBasicCollapsed: boolean = false;\r\n  public quickActionsExpanded: boolean = true;\r\n  public consoleApproval: any = {};\r\n  public options: DropdownOption[] = [];\r\n  public basicSidebarItems: any[] = [];\r\n  public quickActions: any[] = [];\r\n  public toolReviews: any[] = [];\r\n  public workflowReviews: any[] = [];\r\n  public agentsReviews: any[] = [];\r\n  public currentToolsPage = 1;\r\n  public currentAgentsPage = 1;\r\n  public currentWorkflowsPage = 1;\r\n  public pageSize = 50;\r\n  public totalRecords = 0;\r\n  public isDeleted = false;\r\n  public currentTab = 'Agents';\r\n  public showToolApprovalPopup = false;\r\n  public showInfoPopup = false;\r\n  public showErrorPopup = false;\r\n  public infoMessage = '';\r\n  public selectedIndex = 0;\r\n  public showFeedbackPopup = false;\r\n  public searchForm!: FormGroup;\r\n  public labels: any = approvalText.labels;\r\n  public approvedAgentId: number | null = null;\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private apiService: SharedApiServiceService,\r\n    private approvalService: ApprovalService,\r\n    private fb: FormBuilder,\r\n  ) {\r\n    this.labels = approvalText.labels;\r\n    this.options = [\r\n      { name: this.labels.electronics, value: 'electronics' },\r\n      { name: this.labels.clothing, value: 'clothing' },\r\n      { name: this.labels.books, value: 'books' },\r\n    ];\r\n    this.basicSidebarItems = [\r\n      {\r\n        id: '1',\r\n        icon: 'hammer',\r\n        text: this.labels.agents,\r\n        route: '',\r\n        active: true,\r\n      },\r\n      { id: '2', icon: 'circle-check', text: this.labels.workflows, route: '' },\r\n      { id: '3', icon: 'bot', text: this.labels.tools, route: '' },\r\n    ];\r\n    this.quickActions = [\r\n      {\r\n        icon: 'awe_agents',\r\n        label: this.labels.agents,\r\n        route: '',\r\n      },\r\n      {\r\n        icon: 'awe_workflows',\r\n        label: this.labels.workflows,\r\n        route: '',\r\n      },\r\n      {\r\n        icon: 'awe_tools',\r\n        label: this.labels.tools,\r\n        route: '',\r\n      },\r\n    ];\r\n    this.searchForm = this.fb.group({\r\n      search: [''],\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.searchList();\r\n    this.totalApprovals = 60;\r\n  }\r\n\r\n  public searchList() {\r\n    console.log(this.searchForm.get('search')?.value);\r\n    this.searchForm\r\n      .get('search')!\r\n      .valueChanges.pipe(\r\n        startWith(''),\r\n        debounceTime(300),\r\n        distinctUntilChanged(),\r\n        map((value) => value?.toLowerCase() ?? ''),\r\n      )\r\n      .subscribe((searchText) => {\r\n        this.applyFilter(searchText);\r\n      });\r\n  }\r\n\r\n  public applyFilter(text: string) {\r\n    const lower = text;\r\n\r\n    if (!this.searchValue) {\r\n      if (this.currentTab === 'Agents') {\r\n        this.updateConsoleApproval(this.agentsReviews, 'agent');\r\n      } else if (this.currentTab === 'Tools') {\r\n        this.updateConsoleApproval(this.toolReviews, 'tool');\r\n      } else {\r\n        this.updateConsoleApproval(this.workflowReviews, 'workflow');\r\n      }\r\n      return;\r\n    }\r\n\r\n    if (this.currentTab === 'Agents') {\r\n      const filtered = this.agentsReviews.filter((item) =>\r\n        item.agentName?.toLowerCase().includes(lower),\r\n      );\r\n      this.updateConsoleApproval(filtered, 'agent');\r\n    } else if (this.currentTab === 'Tools') {\r\n      const filtered = this.toolReviews.filter((item) =>\r\n        item.toolName?.toLowerCase().includes(lower),\r\n      );\r\n      this.updateConsoleApproval(filtered, 'tool');\r\n    } else {\r\n      const filtered = this.workflowReviews.filter((item) =>\r\n        item.name?.toLowerCase().includes(lower),\r\n      );\r\n      this.updateConsoleApproval(filtered, 'workflow');\r\n    }\r\n  }\r\n\r\n  public onSelectionChange(data: any) {\r\n    console.log('Selection changed:', data);\r\n  }\r\n\r\n  public uClick(i: any) {\r\n    console.log('log' + i);\r\n  }\r\n\r\n  public toggleQuickActions(): void {\r\n    this.quickActionsExpanded = !this.quickActionsExpanded;\r\n  }\r\n\r\n  public onBasicCollapseToggle(isCollapsed: boolean): void {\r\n    this.isBasicCollapsed = isCollapsed;\r\n    console.log('Basic sidebar collapsed:', isCollapsed);\r\n  }\r\n\r\n  public onBasicItemClick(item: any): void {\r\n    this.basicSidebarItems.forEach((i) => (i.active = false));\r\n    item.active = true;\r\n    console.log(item);\r\n  }\r\n\r\n  public toRequestStatus(value: string | null | undefined): RequestStatus {\r\n    return value === 'approved' || value === 'rejected' || value === 'review'\r\n      ? value\r\n      : 'review';\r\n  }\r\n\r\n  public loadToolReviews() {\r\n    this.approvalService\r\n      .getAllReviewTools(this.currentToolsPage, this.pageSize, this.isDeleted)\r\n      .subscribe((response) => {\r\n        if (this.currentToolsPage > 1) {\r\n          this.toolReviews = [\r\n            ...this.toolReviews,\r\n            ...response.userToolReviewDetails,\r\n          ];\r\n        } else {\r\n          this.toolReviews = response?.userToolReviewDetails;\r\n        }\r\n        this.toolReviews = this.toolReviews.filter(\r\n          (r) => r.status !== 'approved',\r\n        );\r\n        this.totalRecords = this.toolReviews.length;\r\n        this.updateConsoleApproval(this.toolReviews, 'tool');\r\n      });\r\n  }\r\n\r\n  public loadWorkflowReviews() {\r\n    this.approvalService\r\n      .getAllReviewWorkflows(\r\n        this.currentWorkflowsPage,\r\n        this.pageSize,\r\n        this.isDeleted,\r\n      )\r\n      .subscribe((response) => {\r\n        if (this.currentWorkflowsPage > 1) {\r\n          this.workflowReviews = [\r\n            ...this.workflowReviews,\r\n            ...response.workflowReviewDetails,\r\n          ];\r\n        } else {\r\n          this.workflowReviews = response?.workflowReviewDetails;\r\n        }\r\n        this.workflowReviews = this.workflowReviews.filter(\r\n          (r) => r.status !== 'approved',\r\n        );\r\n        this.totalRecords = this.workflowReviews.length;\r\n        console.log('reviews ', this.workflowReviews);\r\n        this.updateConsoleApproval(this.workflowReviews, 'workflow');\r\n      });\r\n  }\r\n\r\n  public loadAgentsReviews() {\r\n    this.approvalService\r\n      .getAllReviewAgents(this.currentAgentsPage, this.pageSize, this.isDeleted)\r\n      .subscribe((response) => {\r\n        if (this.currentAgentsPage > 1) {\r\n          this.agentsReviews = [\r\n            ...this.agentsReviews,\r\n            ...response.agentReviewDetails,\r\n          ];\r\n        } else {\r\n          this.agentsReviews = response?.agentReviewDetails;\r\n        }\r\n        this.agentsReviews = this.agentsReviews.filter(\r\n          (r) => r.status !== 'approved',\r\n        );\r\n        this.totalRecords = this.agentsReviews.length;\r\n        this.updateConsoleApproval(this.agentsReviews, 'agent');\r\n      });\r\n  }\r\n\r\n  public loadMoreTools(page: number) {\r\n    this.currentToolsPage = page;\r\n    this.loadToolReviews();\r\n  }\r\n\r\n  public loadMoreAgents(page: number) {\r\n    this.currentAgentsPage = page;\r\n    this.loadAgentsReviews();\r\n  }\r\n\r\n  public loadMoreWorkflows(page: number) {\r\n    this.currentWorkflowsPage = page;\r\n    this.loadWorkflowReviews();\r\n  }\r\n\r\n  public loadReviews(name: string) {\r\n    this.currentTab = name;\r\n    if (name == 'Tools') {\r\n      this.loadToolReviews();\r\n    } else if (name == 'Agents') {\r\n      this.loadAgentsReviews();\r\n    } else {\r\n      this.loadWorkflowReviews();\r\n    }\r\n  }\r\n\r\n  public redirectToListOfApproval(name: string) {\r\n    this.currentTab = name;\r\n    if (name === this.labels.tools) {\r\n      this.router.navigate(['approval-tools'], { relativeTo: this.route });\r\n    } else if (name === this.labels.agents) {\r\n      this.router.navigate(['approval-agents'], { relativeTo: this.route });\r\n    } else if (name === this.labels.workflows) {\r\n      this.router.navigate(['approval-workflows'], { relativeTo: this.route });\r\n    }\r\n  }\r\n\r\n  public rejectApproval(idx: any) {\r\n    console.log(idx);\r\n    this.selectedIndex = idx;\r\n    this.showFeedbackPopup = true;\r\n  }\r\n\r\n  public approveApproval(idx: any) {\r\n    console.log(idx);\r\n    this.selectedIndex = idx;\r\n    this.showToolApprovalPopup = true;\r\n  }\r\n\r\n  public handleTesting(index: any) {\r\n    console.log(index);\r\n    if (this.currentTab == 'Tools') {\r\n      const toolId = this.toolReviews[index].toolId;\r\n      this.redirectToToolPlayground(toolId);\r\n    } else if (this.currentTab == 'Agents') {\r\n      const agentId = this.agentsReviews[index].agentId;\r\n      this.redirectToAgentsPlayground(agentId, 'collaborative');\r\n    } else {\r\n      const workflowId = this.workflowReviews[index].workflowId;\r\n      this.redirectToWorkflowPlayground(workflowId);\r\n    }\r\n  }\r\n\r\n  public redirectToToolPlayground(id: number): void {\r\n    this.router.navigate(['/libraries/tools/execute', id]);\r\n  }\r\n\r\n  public redirectToWorkflowPlayground(id: number): void {\r\n    this.router.navigate(['/build/workflows/execute', id]);\r\n  }\r\n\r\n  public redirectToAgentsPlayground(id: number, type: string): void {\r\n    this.router.navigate(['/build/agents', type, 'execute'], {\r\n      queryParams: { id: id },\r\n    });\r\n  }\r\n\r\n  public handleApproval() {\r\n    if (this.currentTab == 'Tools') {\r\n      this.handleToolApproval();\r\n    } else if (this.currentTab == 'Agents') {\r\n      this.handleAgentApproval();\r\n    } else {\r\n      this.handleWorkflowApproval();\r\n    }\r\n  }\r\n\r\n  public handleRejection(feedback: any) {\r\n    if (this.currentTab == 'Tools') {\r\n      this.handleToolRejection(feedback);\r\n    } else if (this.currentTab == 'Agents') {\r\n      this.handleAgentRejection(feedback);\r\n    } else {\r\n      this.handleWorkflowRejection(feedback);\r\n    }\r\n  }\r\n\r\n  public handleToolApproval() {\r\n    const toolDetails = this.toolReviews[this.selectedIndex];\r\n    const id = toolDetails.id;\r\n    const toolId = toolDetails.toolId;\r\n    const status = 'approved';\r\n    const reviewedBy = toolDetails.reviewedBy;\r\n\r\n    this.approvalService.approveTool(id, toolId, status, reviewedBy).subscribe({\r\n      next: (response: any) => {\r\n        this.infoMessage =\r\n          response?.message || this.labels.toolSuccessApproveMessage;\r\n        this.showInfoPopup = true;\r\n      },\r\n      error: (error) => {\r\n        this.showErrorPopup = true;\r\n        this.infoMessage =\r\n          error?.error?.message || this.labels.defaultErrorMessage;\r\n        console.error('Error:', error);\r\n      },\r\n    });\r\n  }\r\n\r\n  public handleToolRejection(feedback: any) {\r\n    const toolDetails = this.toolReviews[this.selectedIndex];\r\n    const id = toolDetails.id;\r\n    const toolId = toolDetails.toolId;\r\n    const status = 'rejected';\r\n    const reviewedBy = toolDetails.reviewedBy;\r\n    const message = feedback;\r\n\r\n    this.approvalService\r\n      .rejectTool(id, toolId, status, reviewedBy, message)\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.infoMessage =\r\n            response?.message || this.labels.toolSuccessRejectMessage;\r\n          this.showInfoPopup = true;\r\n        },\r\n        error: (error) => {\r\n          this.showErrorPopup = true;\r\n          this.infoMessage =\r\n            error?.error?.message || this.labels.defaultErrorMessage;\r\n          console.error('Error:', error);\r\n        },\r\n      });\r\n  }\r\n\r\n  public handleAgentApproval() {\r\n    const agentDetails = this.agentsReviews[this.selectedIndex];\r\n    const id = agentDetails.id;\r\n    const agentId = agentDetails.agentId;\r\n    const status = 'approved';\r\n    const reviewedBy = agentDetails.reviewedBy;\r\n\r\n    this.approvalService\r\n      .approveAgent(id, agentId, status, reviewedBy)\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.infoMessage =\r\n            response?.message || this.labels.agentSuccessApproveMessage;\r\n          this.showInfoPopup = true;\r\n\r\n          // Store agent ID for navigation after popup confirmation\r\n          this.approvedAgentId = agentId;\r\n        },\r\n        error: (error) => {\r\n          this.showErrorPopup = true;\r\n          this.infoMessage =\r\n            error?.error?.message || this.labels.defaultErrorMessage;\r\n          console.error('Error:', error);\r\n        },\r\n      });\r\n  }\r\n\r\n  public handleAgentRejection(feedback: any) {\r\n    const agentDetails = this.agentsReviews[this.selectedIndex];\r\n    const id = agentDetails.id;\r\n    const agentId = agentDetails.agentId;\r\n    const status = 'rejected';\r\n    const reviewedBy = agentDetails.reviewedBy;\r\n    const message = feedback;\r\n\r\n    this.approvalService\r\n      .rejectAgent(id, agentId, status, reviewedBy, message)\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.infoMessage =\r\n            response?.message || this.labels.agentSuccessRejectMessage;\r\n          this.showInfoPopup = true;\r\n        },\r\n        error: (error) => {\r\n          this.showErrorPopup = true;\r\n          this.infoMessage =\r\n            error?.error?.message || this.labels.defaultErrorMessage;\r\n          console.error('Error:', error);\r\n        },\r\n      });\r\n  }\r\n\r\n  public handleWorkflowApproval() {\r\n    const workflowDetails = this.workflowReviews[this.selectedIndex];\r\n    const id = workflowDetails?.id;\r\n    const workflowId = workflowDetails?.workflowId;\r\n    const status = 'approved';\r\n    const reviewedBy = workflowDetails?.reviewedBy;\r\n    console.log(id, workflowId, status, reviewedBy);\r\n    this.approvalService\r\n      .approveWorkflow(id, workflowId, status, reviewedBy)\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.infoMessage =\r\n            response?.message || this.labels.workflowSuccessApproveMessage;\r\n          this.showInfoPopup = true;\r\n        },\r\n        error: (error) => {\r\n          this.showErrorPopup = true;\r\n          this.infoMessage =\r\n            error?.error?.message || this.labels.defaultErrorMessage;\r\n          console.error('Error:', error);\r\n        },\r\n      });\r\n  }\r\n\r\n  public handleWorkflowRejection(feedback: any) {\r\n    const workflowDetails = this.workflowReviews[this.selectedIndex];\r\n    const id = workflowDetails?.id;\r\n    const workflowId = workflowDetails?.workflowId;\r\n    const status = 'rejected';\r\n    const reviewedBy = workflowDetails?.reviewedBy;\r\n    const message = feedback;\r\n    console.log(id, workflowId, status, reviewedBy, message);\r\n    this.approvalService\r\n      .rejectWorkflow(id, workflowId, status, reviewedBy, message)\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.infoMessage =\r\n            response?.message || this.labels.workflowSuccessRejectMessage;\r\n          this.showInfoPopup = true;\r\n        },\r\n        error: (error) => {\r\n          this.showErrorPopup = true;\r\n          this.infoMessage =\r\n            error?.error?.message || this.labels.defaultErrorMessage;\r\n          console.error('Error:', error);\r\n        },\r\n      });\r\n  }\r\n\r\n  public handleInfoPopup() {\r\n    this.showInfoPopup = false;\r\n\r\n    // Check if we need to navigate to build agent screen after approval\r\n    if (this.currentTab == 'Agents' && this.approvedAgentId) {\r\n      // Navigate to build agent screen with the approved agent ID\r\n      // Route structure: /build/agents/collaborative with query params\r\n      this.router.navigate(['/build/agents/collaborative'], {\r\n        queryParams: {\r\n          id: this.approvedAgentId,\r\n          mode: 'edit',\r\n        },\r\n      });\r\n      // Reset the approved agent ID\r\n      this.approvedAgentId = null;\r\n      return;\r\n    }\r\n\r\n    if (this.currentTab == 'Tools') {\r\n      this.loadToolReviews();\r\n    } else if (this.currentTab == 'Agents') {\r\n      this.loadAgentsReviews();\r\n    } else {\r\n      this.loadWorkflowReviews();\r\n    }\r\n  }\r\n\r\n  public updateConsoleApproval(data: any[], type: string) {\r\n    this.consoleApproval = {\r\n      contents: data?.map((req: any) => {\r\n        const statusIcons: Record<RequestStatus, string> = {\r\n          approved: 'circle-check-big',\r\n          rejected: 'circle-x',\r\n          review: 'clock',\r\n        };\r\n        const statusTexts: Record<RequestStatus, string> = {\r\n          approved: this.labels.approved,\r\n          rejected: this.labels.rejected,\r\n          review: this.labels.review,\r\n        };\r\n        const statusKey = this.toRequestStatus(req?.status);\r\n        let specificId = 0;\r\n        let title = '';\r\n\r\n        if (type === 'tool') {\r\n          specificId = req.toolId;\r\n          title = req.toolName;\r\n        } else if (type === 'agent') {\r\n          specificId = req.agentId;\r\n          title = req.agentName;\r\n        } else {\r\n          specificId = req.workflowId;\r\n          title = req.workflowName;\r\n        }\r\n\r\n        return {\r\n          id: req.id,\r\n          refId: specificId,\r\n          type: type,\r\n          session1: {\r\n            title: title,\r\n            labels: [\r\n              {\r\n                name: type,\r\n                color: 'success',\r\n                background: 'red',\r\n                type: 'normal',\r\n              },\r\n              {\r\n                name: req.changeRequestType,\r\n                color: req.changeRequestType === 'update' ? 'error' : 'info',\r\n                background: 'red',\r\n                type: 'pill',\r\n              },\r\n            ],\r\n          },\r\n          session2: [\r\n            {\r\n              name: type,\r\n              color: 'default',\r\n              background: 'red',\r\n              type: 'normal',\r\n            },\r\n            {\r\n              name: req.status,\r\n              color: 'default',\r\n              background: 'red',\r\n              type: 'normal',\r\n            },\r\n          ],\r\n          session3: [\r\n            {\r\n              iconName: 'user',\r\n              label: req.requestedBy,\r\n            },\r\n            {\r\n              iconName: 'calendar-days',\r\n              label: formatDate(req?.requestedAt, 'dd MMM yyyy', 'en-IN'),\r\n            },\r\n          ],\r\n          session4: {\r\n            status: statusTexts[statusKey],\r\n            iconName: statusIcons[statusKey],\r\n          },\r\n        };\r\n      }),\r\n      footer: {},\r\n    };\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,sBAAsB,QAAgB,eAAe;AACzE,SAASC,YAAY,EAAEC,UAAU,QAAQ,iBAAiB;AAC1D,SAIEC,YAAY,QACP,iBAAiB;AACxB,SAQEC,cAAc,EACdC,0BAA0B,QAErB,wBAAwB;AAG/B,SAAiCC,mBAAmB,QAAQ,gBAAgB;AAC5E,SACEC,SAAS,EACTC,YAAY,EACZC,oBAAoB,EACpBC,GAAG,QAEE,MAAM;AACb,OAAOC,YAAY,MAAM,2BAA2B;AAiB7C,IAAMC,iBAAiB,GAAvB,MAAMA,iBAAiB;EAmClBC,MAAA;EACAC,KAAA;EACAC,UAAA;EACAC,eAAA;EACAC,EAAA;EAtCV;EACAC,SAAS,GAAGP,YAAY,CAACQ,MAAM;EAExBC,WAAW,GAAW,EAAE;EACxBC,sBAAsB,GAAW,EAAE;EACnCC,qBAAqB,GAAW,EAAE;EAClCC,cAAc,GAAW,EAAE;EAC3BC,gBAAgB,GAAY,KAAK;EACjCC,oBAAoB,GAAY,IAAI;EACpCC,eAAe,GAAQ,EAAE;EACzBC,OAAO,GAAqB,EAAE;EAC9BC,iBAAiB,GAAU,EAAE;EAC7BC,YAAY,GAAU,EAAE;EACxBC,WAAW,GAAU,EAAE;EACvBC,eAAe,GAAU,EAAE;EAC3BC,aAAa,GAAU,EAAE;EACzBC,gBAAgB,GAAG,CAAC;EACpBC,iBAAiB,GAAG,CAAC;EACrBC,oBAAoB,GAAG,CAAC;EACxBC,QAAQ,GAAG,EAAE;EACbC,YAAY,GAAG,CAAC;EAChBC,SAAS,GAAG,KAAK;EACjBC,UAAU,GAAG,QAAQ;EACrBC,qBAAqB,GAAG,KAAK;EAC7BC,aAAa,GAAG,KAAK;EACrBC,cAAc,GAAG,KAAK;EACtBC,WAAW,GAAG,EAAE;EAChBC,aAAa,GAAG,CAAC;EACjBC,iBAAiB,GAAG,KAAK;EACzBC,UAAU;EACV3B,MAAM,GAAQR,YAAY,CAACQ,MAAM;EACjC4B,eAAe,GAAkB,IAAI;EAE5CC,YACUnC,MAAc,EACdC,KAAqB,EACrBC,UAAmC,EACnCC,eAAgC,EAChCC,EAAe;IAJf,KAAAJ,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,EAAE,GAAFA,EAAE;IAEV,IAAI,CAACE,MAAM,GAAGR,YAAY,CAACQ,MAAM;IACjC,IAAI,CAACQ,OAAO,GAAG,CACb;MAAEsB,IAAI,EAAE,IAAI,CAAC9B,MAAM,CAAC+B,WAAW;MAAEC,KAAK,EAAE;IAAa,CAAE,EACvD;MAAEF,IAAI,EAAE,IAAI,CAAC9B,MAAM,CAACiC,QAAQ;MAAED,KAAK,EAAE;IAAU,CAAE,EACjD;MAAEF,IAAI,EAAE,IAAI,CAAC9B,MAAM,CAACkC,KAAK;MAAEF,KAAK,EAAE;IAAO,CAAE,CAC5C;IACD,IAAI,CAACvB,iBAAiB,GAAG,CACvB;MACE0B,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,IAAI,CAACrC,MAAM,CAACsC,MAAM;MACxB3C,KAAK,EAAE,EAAE;MACT4C,MAAM,EAAE;KACT,EACD;MAAEJ,EAAE,EAAE,GAAG;MAAEC,IAAI,EAAE,cAAc;MAAEC,IAAI,EAAE,IAAI,CAACrC,MAAM,CAACwC,SAAS;MAAE7C,KAAK,EAAE;IAAE,CAAE,EACzE;MAAEwC,EAAE,EAAE,GAAG;MAAEC,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE,IAAI,CAACrC,MAAM,CAACyC,KAAK;MAAE9C,KAAK,EAAE;IAAE,CAAE,CAC7D;IACD,IAAI,CAACe,YAAY,GAAG,CAClB;MACE0B,IAAI,EAAE,YAAY;MAClBM,KAAK,EAAE,IAAI,CAAC1C,MAAM,CAACsC,MAAM;MACzB3C,KAAK,EAAE;KACR,EACD;MACEyC,IAAI,EAAE,eAAe;MACrBM,KAAK,EAAE,IAAI,CAAC1C,MAAM,CAACwC,SAAS;MAC5B7C,KAAK,EAAE;KACR,EACD;MACEyC,IAAI,EAAE,WAAW;MACjBM,KAAK,EAAE,IAAI,CAAC1C,MAAM,CAACyC,KAAK;MACxB9C,KAAK,EAAE;KACR,CACF;IACD,IAAI,CAACgC,UAAU,GAAG,IAAI,CAAC7B,EAAE,CAAC6C,KAAK,CAAC;MAC9BC,MAAM,EAAE,CAAC,EAAE;KACZ,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,UAAU,EAAE;IACjB,IAAI,CAAC1C,cAAc,GAAG,EAAE;EAC1B;EAEO0C,UAAUA,CAAA;IACfC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACrB,UAAU,CAACsB,GAAG,CAAC,QAAQ,CAAC,EAAEjB,KAAK,CAAC;IACjD,IAAI,CAACL,UAAU,CACZsB,GAAG,CAAC,QAAQ,CAAE,CACdC,YAAY,CAACC,IAAI,CAChB/D,SAAS,CAAC,EAAE,CAAC,EACbC,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,GAAG,CAAEyC,KAAK,IAAKA,KAAK,EAAEoB,WAAW,EAAE,IAAI,EAAE,CAAC,CAC3C,CACAC,SAAS,CAAEC,UAAU,IAAI;MACxB,IAAI,CAACC,WAAW,CAACD,UAAU,CAAC;IAC9B,CAAC,CAAC;EACN;EAEOC,WAAWA,CAAClB,IAAY;IAC7B,MAAMmB,KAAK,GAAGnB,IAAI;IAElB,IAAI,CAAC,IAAI,CAACpC,WAAW,EAAE;MACrB,IAAI,IAAI,CAACmB,UAAU,KAAK,QAAQ,EAAE;QAChC,IAAI,CAACqC,qBAAqB,CAAC,IAAI,CAAC5C,aAAa,EAAE,OAAO,CAAC;MACzD,CAAC,MAAM,IAAI,IAAI,CAACO,UAAU,KAAK,OAAO,EAAE;QACtC,IAAI,CAACqC,qBAAqB,CAAC,IAAI,CAAC9C,WAAW,EAAE,MAAM,CAAC;MACtD,CAAC,MAAM;QACL,IAAI,CAAC8C,qBAAqB,CAAC,IAAI,CAAC7C,eAAe,EAAE,UAAU,CAAC;MAC9D;MACA;IACF;IAEA,IAAI,IAAI,CAACQ,UAAU,KAAK,QAAQ,EAAE;MAChC,MAAMsC,QAAQ,GAAG,IAAI,CAAC7C,aAAa,CAAC8C,MAAM,CAAEC,IAAI,IAC9CA,IAAI,CAACC,SAAS,EAAET,WAAW,EAAE,CAACU,QAAQ,CAACN,KAAK,CAAC,CAC9C;MACD,IAAI,CAACC,qBAAqB,CAACC,QAAQ,EAAE,OAAO,CAAC;IAC/C,CAAC,MAAM,IAAI,IAAI,CAACtC,UAAU,KAAK,OAAO,EAAE;MACtC,MAAMsC,QAAQ,GAAG,IAAI,CAAC/C,WAAW,CAACgD,MAAM,CAAEC,IAAI,IAC5CA,IAAI,CAACG,QAAQ,EAAEX,WAAW,EAAE,CAACU,QAAQ,CAACN,KAAK,CAAC,CAC7C;MACD,IAAI,CAACC,qBAAqB,CAACC,QAAQ,EAAE,MAAM,CAAC;IAC9C,CAAC,MAAM;MACL,MAAMA,QAAQ,GAAG,IAAI,CAAC9C,eAAe,CAAC+C,MAAM,CAAEC,IAAI,IAChDA,IAAI,CAAC9B,IAAI,EAAEsB,WAAW,EAAE,CAACU,QAAQ,CAACN,KAAK,CAAC,CACzC;MACD,IAAI,CAACC,qBAAqB,CAACC,QAAQ,EAAE,UAAU,CAAC;IAClD;EACF;EAEOM,iBAAiBA,CAACC,IAAS;IAChClB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEiB,IAAI,CAAC;EACzC;EAEOC,MAAMA,CAACC,CAAM;IAClBpB,OAAO,CAACC,GAAG,CAAC,KAAK,GAAGmB,CAAC,CAAC;EACxB;EAEOC,kBAAkBA,CAAA;IACvB,IAAI,CAAC9D,oBAAoB,GAAG,CAAC,IAAI,CAACA,oBAAoB;EACxD;EAEO+D,qBAAqBA,CAACC,WAAoB;IAC/C,IAAI,CAACjE,gBAAgB,GAAGiE,WAAW;IACnCvB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEsB,WAAW,CAAC;EACtD;EAEOC,gBAAgBA,CAACX,IAAS;IAC/B,IAAI,CAACnD,iBAAiB,CAAC+D,OAAO,CAAEL,CAAC,IAAMA,CAAC,CAAC5B,MAAM,GAAG,KAAM,CAAC;IACzDqB,IAAI,CAACrB,MAAM,GAAG,IAAI;IAClBQ,OAAO,CAACC,GAAG,CAACY,IAAI,CAAC;EACnB;EAEOa,eAAeA,CAACzC,KAAgC;IACrD,OAAOA,KAAK,KAAK,UAAU,IAAIA,KAAK,KAAK,UAAU,IAAIA,KAAK,KAAK,QAAQ,GACrEA,KAAK,GACL,QAAQ;EACd;EAEO0C,eAAeA,CAAA;IACpB,IAAI,CAAC7E,eAAe,CACjB8E,iBAAiB,CAAC,IAAI,CAAC7D,gBAAgB,EAAE,IAAI,CAACG,QAAQ,EAAE,IAAI,CAACE,SAAS,CAAC,CACvEkC,SAAS,CAAEuB,QAAQ,IAAI;MACtB,IAAI,IAAI,CAAC9D,gBAAgB,GAAG,CAAC,EAAE;QAC7B,IAAI,CAACH,WAAW,GAAG,CACjB,GAAG,IAAI,CAACA,WAAW,EACnB,GAAGiE,QAAQ,CAACC,qBAAqB,CAClC;MACH,CAAC,MAAM;QACL,IAAI,CAAClE,WAAW,GAAGiE,QAAQ,EAAEC,qBAAqB;MACpD;MACA,IAAI,CAAClE,WAAW,GAAG,IAAI,CAACA,WAAW,CAACgD,MAAM,CACvCmB,CAAC,IAAKA,CAAC,CAACC,MAAM,KAAK,UAAU,CAC/B;MACD,IAAI,CAAC7D,YAAY,GAAG,IAAI,CAACP,WAAW,CAACqE,MAAM;MAC3C,IAAI,CAACvB,qBAAqB,CAAC,IAAI,CAAC9C,WAAW,EAAE,MAAM,CAAC;IACtD,CAAC,CAAC;EACN;EAEOsE,mBAAmBA,CAAA;IACxB,IAAI,CAACpF,eAAe,CACjBqF,qBAAqB,CACpB,IAAI,CAAClE,oBAAoB,EACzB,IAAI,CAACC,QAAQ,EACb,IAAI,CAACE,SAAS,CACf,CACAkC,SAAS,CAAEuB,QAAQ,IAAI;MACtB,IAAI,IAAI,CAAC5D,oBAAoB,GAAG,CAAC,EAAE;QACjC,IAAI,CAACJ,eAAe,GAAG,CACrB,GAAG,IAAI,CAACA,eAAe,EACvB,GAAGgE,QAAQ,CAACO,qBAAqB,CAClC;MACH,CAAC,MAAM;QACL,IAAI,CAACvE,eAAe,GAAGgE,QAAQ,EAAEO,qBAAqB;MACxD;MACA,IAAI,CAACvE,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC+C,MAAM,CAC/CmB,CAAC,IAAKA,CAAC,CAACC,MAAM,KAAK,UAAU,CAC/B;MACD,IAAI,CAAC7D,YAAY,GAAG,IAAI,CAACN,eAAe,CAACoE,MAAM;MAC/CjC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,IAAI,CAACpC,eAAe,CAAC;MAC7C,IAAI,CAAC6C,qBAAqB,CAAC,IAAI,CAAC7C,eAAe,EAAE,UAAU,CAAC;IAC9D,CAAC,CAAC;EACN;EAEOwE,iBAAiBA,CAAA;IACtB,IAAI,CAACvF,eAAe,CACjBwF,kBAAkB,CAAC,IAAI,CAACtE,iBAAiB,EAAE,IAAI,CAACE,QAAQ,EAAE,IAAI,CAACE,SAAS,CAAC,CACzEkC,SAAS,CAAEuB,QAAQ,IAAI;MACtB,IAAI,IAAI,CAAC7D,iBAAiB,GAAG,CAAC,EAAE;QAC9B,IAAI,CAACF,aAAa,GAAG,CACnB,GAAG,IAAI,CAACA,aAAa,EACrB,GAAG+D,QAAQ,CAACU,kBAAkB,CAC/B;MACH,CAAC,MAAM;QACL,IAAI,CAACzE,aAAa,GAAG+D,QAAQ,EAAEU,kBAAkB;MACnD;MACA,IAAI,CAACzE,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC8C,MAAM,CAC3CmB,CAAC,IAAKA,CAAC,CAACC,MAAM,KAAK,UAAU,CAC/B;MACD,IAAI,CAAC7D,YAAY,GAAG,IAAI,CAACL,aAAa,CAACmE,MAAM;MAC7C,IAAI,CAACvB,qBAAqB,CAAC,IAAI,CAAC5C,aAAa,EAAE,OAAO,CAAC;IACzD,CAAC,CAAC;EACN;EAEO0E,aAAaA,CAACC,IAAY;IAC/B,IAAI,CAAC1E,gBAAgB,GAAG0E,IAAI;IAC5B,IAAI,CAACd,eAAe,EAAE;EACxB;EAEOe,cAAcA,CAACD,IAAY;IAChC,IAAI,CAACzE,iBAAiB,GAAGyE,IAAI;IAC7B,IAAI,CAACJ,iBAAiB,EAAE;EAC1B;EAEOM,iBAAiBA,CAACF,IAAY;IACnC,IAAI,CAACxE,oBAAoB,GAAGwE,IAAI;IAChC,IAAI,CAACP,mBAAmB,EAAE;EAC5B;EAEOU,WAAWA,CAAC7D,IAAY;IAC7B,IAAI,CAACV,UAAU,GAAGU,IAAI;IACtB,IAAIA,IAAI,IAAI,OAAO,EAAE;MACnB,IAAI,CAAC4C,eAAe,EAAE;IACxB,CAAC,MAAM,IAAI5C,IAAI,IAAI,QAAQ,EAAE;MAC3B,IAAI,CAACsD,iBAAiB,EAAE;IAC1B,CAAC,MAAM;MACL,IAAI,CAACH,mBAAmB,EAAE;IAC5B;EACF;EAEOW,wBAAwBA,CAAC9D,IAAY;IAC1C,IAAI,CAACV,UAAU,GAAGU,IAAI;IACtB,IAAIA,IAAI,KAAK,IAAI,CAAC9B,MAAM,CAACyC,KAAK,EAAE;MAC9B,IAAI,CAAC/C,MAAM,CAACmG,QAAQ,CAAC,CAAC,gBAAgB,CAAC,EAAE;QAAEC,UAAU,EAAE,IAAI,CAACnG;MAAK,CAAE,CAAC;IACtE,CAAC,MAAM,IAAImC,IAAI,KAAK,IAAI,CAAC9B,MAAM,CAACsC,MAAM,EAAE;MACtC,IAAI,CAAC5C,MAAM,CAACmG,QAAQ,CAAC,CAAC,iBAAiB,CAAC,EAAE;QAAEC,UAAU,EAAE,IAAI,CAACnG;MAAK,CAAE,CAAC;IACvE,CAAC,MAAM,IAAImC,IAAI,KAAK,IAAI,CAAC9B,MAAM,CAACwC,SAAS,EAAE;MACzC,IAAI,CAAC9C,MAAM,CAACmG,QAAQ,CAAC,CAAC,oBAAoB,CAAC,EAAE;QAAEC,UAAU,EAAE,IAAI,CAACnG;MAAK,CAAE,CAAC;IAC1E;EACF;EAEOoG,cAAcA,CAACC,GAAQ;IAC5BjD,OAAO,CAACC,GAAG,CAACgD,GAAG,CAAC;IAChB,IAAI,CAACvE,aAAa,GAAGuE,GAAG;IACxB,IAAI,CAACtE,iBAAiB,GAAG,IAAI;EAC/B;EAEOuE,eAAeA,CAACD,GAAQ;IAC7BjD,OAAO,CAACC,GAAG,CAACgD,GAAG,CAAC;IAChB,IAAI,CAACvE,aAAa,GAAGuE,GAAG;IACxB,IAAI,CAAC3E,qBAAqB,GAAG,IAAI;EACnC;EAEO6E,aAAaA,CAACC,KAAU;IAC7BpD,OAAO,CAACC,GAAG,CAACmD,KAAK,CAAC;IAClB,IAAI,IAAI,CAAC/E,UAAU,IAAI,OAAO,EAAE;MAC9B,MAAMgF,MAAM,GAAG,IAAI,CAACzF,WAAW,CAACwF,KAAK,CAAC,CAACC,MAAM;MAC7C,IAAI,CAACC,wBAAwB,CAACD,MAAM,CAAC;IACvC,CAAC,MAAM,IAAI,IAAI,CAAChF,UAAU,IAAI,QAAQ,EAAE;MACtC,MAAMkF,OAAO,GAAG,IAAI,CAACzF,aAAa,CAACsF,KAAK,CAAC,CAACG,OAAO;MACjD,IAAI,CAACC,0BAA0B,CAACD,OAAO,EAAE,eAAe,CAAC;IAC3D,CAAC,MAAM;MACL,MAAME,UAAU,GAAG,IAAI,CAAC5F,eAAe,CAACuF,KAAK,CAAC,CAACK,UAAU;MACzD,IAAI,CAACC,4BAA4B,CAACD,UAAU,CAAC;IAC/C;EACF;EAEOH,wBAAwBA,CAAClE,EAAU;IACxC,IAAI,CAACzC,MAAM,CAACmG,QAAQ,CAAC,CAAC,0BAA0B,EAAE1D,EAAE,CAAC,CAAC;EACxD;EAEOsE,4BAA4BA,CAACtE,EAAU;IAC5C,IAAI,CAACzC,MAAM,CAACmG,QAAQ,CAAC,CAAC,0BAA0B,EAAE1D,EAAE,CAAC,CAAC;EACxD;EAEOoE,0BAA0BA,CAACpE,EAAU,EAAEuE,IAAY;IACxD,IAAI,CAAChH,MAAM,CAACmG,QAAQ,CAAC,CAAC,eAAe,EAAEa,IAAI,EAAE,SAAS,CAAC,EAAE;MACvDC,WAAW,EAAE;QAAExE,EAAE,EAAEA;MAAE;KACtB,CAAC;EACJ;EAEOyE,cAAcA,CAAA;IACnB,IAAI,IAAI,CAACxF,UAAU,IAAI,OAAO,EAAE;MAC9B,IAAI,CAACyF,kBAAkB,EAAE;IAC3B,CAAC,MAAM,IAAI,IAAI,CAACzF,UAAU,IAAI,QAAQ,EAAE;MACtC,IAAI,CAAC0F,mBAAmB,EAAE;IAC5B,CAAC,MAAM;MACL,IAAI,CAACC,sBAAsB,EAAE;IAC/B;EACF;EAEOC,eAAeA,CAACC,QAAa;IAClC,IAAI,IAAI,CAAC7F,UAAU,IAAI,OAAO,EAAE;MAC9B,IAAI,CAAC8F,mBAAmB,CAACD,QAAQ,CAAC;IACpC,CAAC,MAAM,IAAI,IAAI,CAAC7F,UAAU,IAAI,QAAQ,EAAE;MACtC,IAAI,CAAC+F,oBAAoB,CAACF,QAAQ,CAAC;IACrC,CAAC,MAAM;MACL,IAAI,CAACG,uBAAuB,CAACH,QAAQ,CAAC;IACxC;EACF;EAEOJ,kBAAkBA,CAAA;IACvB,MAAMQ,WAAW,GAAG,IAAI,CAAC1G,WAAW,CAAC,IAAI,CAACc,aAAa,CAAC;IACxD,MAAMU,EAAE,GAAGkF,WAAW,CAAClF,EAAE;IACzB,MAAMiE,MAAM,GAAGiB,WAAW,CAACjB,MAAM;IACjC,MAAMrB,MAAM,GAAG,UAAU;IACzB,MAAMuC,UAAU,GAAGD,WAAW,CAACC,UAAU;IAEzC,IAAI,CAACzH,eAAe,CAAC0H,WAAW,CAACpF,EAAE,EAAEiE,MAAM,EAAErB,MAAM,EAAEuC,UAAU,CAAC,CAACjE,SAAS,CAAC;MACzEmE,IAAI,EAAG5C,QAAa,IAAI;QACtB,IAAI,CAACpD,WAAW,GACdoD,QAAQ,EAAE6C,OAAO,IAAI,IAAI,CAACzH,MAAM,CAAC0H,yBAAyB;QAC5D,IAAI,CAACpG,aAAa,GAAG,IAAI;MAC3B,CAAC;MACDqG,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACpG,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACC,WAAW,GACdmG,KAAK,EAAEA,KAAK,EAAEF,OAAO,IAAI,IAAI,CAACzH,MAAM,CAAC4H,mBAAmB;QAC1D7E,OAAO,CAAC4E,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;MAChC;KACD,CAAC;EACJ;EAEOT,mBAAmBA,CAACD,QAAa;IACtC,MAAMI,WAAW,GAAG,IAAI,CAAC1G,WAAW,CAAC,IAAI,CAACc,aAAa,CAAC;IACxD,MAAMU,EAAE,GAAGkF,WAAW,CAAClF,EAAE;IACzB,MAAMiE,MAAM,GAAGiB,WAAW,CAACjB,MAAM;IACjC,MAAMrB,MAAM,GAAG,UAAU;IACzB,MAAMuC,UAAU,GAAGD,WAAW,CAACC,UAAU;IACzC,MAAMG,OAAO,GAAGR,QAAQ;IAExB,IAAI,CAACpH,eAAe,CACjBgI,UAAU,CAAC1F,EAAE,EAAEiE,MAAM,EAAErB,MAAM,EAAEuC,UAAU,EAAEG,OAAO,CAAC,CACnDpE,SAAS,CAAC;MACTmE,IAAI,EAAG5C,QAAa,IAAI;QACtB,IAAI,CAACpD,WAAW,GACdoD,QAAQ,EAAE6C,OAAO,IAAI,IAAI,CAACzH,MAAM,CAAC8H,wBAAwB;QAC3D,IAAI,CAACxG,aAAa,GAAG,IAAI;MAC3B,CAAC;MACDqG,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACpG,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACC,WAAW,GACdmG,KAAK,EAAEA,KAAK,EAAEF,OAAO,IAAI,IAAI,CAACzH,MAAM,CAAC4H,mBAAmB;QAC1D7E,OAAO,CAAC4E,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;MAChC;KACD,CAAC;EACN;EAEOb,mBAAmBA,CAAA;IACxB,MAAMiB,YAAY,GAAG,IAAI,CAAClH,aAAa,CAAC,IAAI,CAACY,aAAa,CAAC;IAC3D,MAAMU,EAAE,GAAG4F,YAAY,CAAC5F,EAAE;IAC1B,MAAMmE,OAAO,GAAGyB,YAAY,CAACzB,OAAO;IACpC,MAAMvB,MAAM,GAAG,UAAU;IACzB,MAAMuC,UAAU,GAAGS,YAAY,CAACT,UAAU;IAE1C,IAAI,CAACzH,eAAe,CACjBmI,YAAY,CAAC7F,EAAE,EAAEmE,OAAO,EAAEvB,MAAM,EAAEuC,UAAU,CAAC,CAC7CjE,SAAS,CAAC;MACTmE,IAAI,EAAG5C,QAAa,IAAI;QACtB,IAAI,CAACpD,WAAW,GACdoD,QAAQ,EAAE6C,OAAO,IAAI,IAAI,CAACzH,MAAM,CAACiI,0BAA0B;QAC7D,IAAI,CAAC3G,aAAa,GAAG,IAAI;QAEzB;QACA,IAAI,CAACM,eAAe,GAAG0E,OAAO;MAChC,CAAC;MACDqB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACpG,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACC,WAAW,GACdmG,KAAK,EAAEA,KAAK,EAAEF,OAAO,IAAI,IAAI,CAACzH,MAAM,CAAC4H,mBAAmB;QAC1D7E,OAAO,CAAC4E,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;MAChC;KACD,CAAC;EACN;EAEOR,oBAAoBA,CAACF,QAAa;IACvC,MAAMc,YAAY,GAAG,IAAI,CAAClH,aAAa,CAAC,IAAI,CAACY,aAAa,CAAC;IAC3D,MAAMU,EAAE,GAAG4F,YAAY,CAAC5F,EAAE;IAC1B,MAAMmE,OAAO,GAAGyB,YAAY,CAACzB,OAAO;IACpC,MAAMvB,MAAM,GAAG,UAAU;IACzB,MAAMuC,UAAU,GAAGS,YAAY,CAACT,UAAU;IAC1C,MAAMG,OAAO,GAAGR,QAAQ;IAExB,IAAI,CAACpH,eAAe,CACjBqI,WAAW,CAAC/F,EAAE,EAAEmE,OAAO,EAAEvB,MAAM,EAAEuC,UAAU,EAAEG,OAAO,CAAC,CACrDpE,SAAS,CAAC;MACTmE,IAAI,EAAG5C,QAAa,IAAI;QACtB,IAAI,CAACpD,WAAW,GACdoD,QAAQ,EAAE6C,OAAO,IAAI,IAAI,CAACzH,MAAM,CAACmI,yBAAyB;QAC5D,IAAI,CAAC7G,aAAa,GAAG,IAAI;MAC3B,CAAC;MACDqG,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACpG,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACC,WAAW,GACdmG,KAAK,EAAEA,KAAK,EAAEF,OAAO,IAAI,IAAI,CAACzH,MAAM,CAAC4H,mBAAmB;QAC1D7E,OAAO,CAAC4E,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;MAChC;KACD,CAAC;EACN;EAEOZ,sBAAsBA,CAAA;IAC3B,MAAMqB,eAAe,GAAG,IAAI,CAACxH,eAAe,CAAC,IAAI,CAACa,aAAa,CAAC;IAChE,MAAMU,EAAE,GAAGiG,eAAe,EAAEjG,EAAE;IAC9B,MAAMqE,UAAU,GAAG4B,eAAe,EAAE5B,UAAU;IAC9C,MAAMzB,MAAM,GAAG,UAAU;IACzB,MAAMuC,UAAU,GAAGc,eAAe,EAAEd,UAAU;IAC9CvE,OAAO,CAACC,GAAG,CAACb,EAAE,EAAEqE,UAAU,EAAEzB,MAAM,EAAEuC,UAAU,CAAC;IAC/C,IAAI,CAACzH,eAAe,CACjBwI,eAAe,CAAClG,EAAE,EAAEqE,UAAU,EAAEzB,MAAM,EAAEuC,UAAU,CAAC,CACnDjE,SAAS,CAAC;MACTmE,IAAI,EAAG5C,QAAa,IAAI;QACtB,IAAI,CAACpD,WAAW,GACdoD,QAAQ,EAAE6C,OAAO,IAAI,IAAI,CAACzH,MAAM,CAACsI,6BAA6B;QAChE,IAAI,CAAChH,aAAa,GAAG,IAAI;MAC3B,CAAC;MACDqG,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACpG,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACC,WAAW,GACdmG,KAAK,EAAEA,KAAK,EAAEF,OAAO,IAAI,IAAI,CAACzH,MAAM,CAAC4H,mBAAmB;QAC1D7E,OAAO,CAAC4E,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;MAChC;KACD,CAAC;EACN;EAEOP,uBAAuBA,CAACH,QAAa;IAC1C,MAAMmB,eAAe,GAAG,IAAI,CAACxH,eAAe,CAAC,IAAI,CAACa,aAAa,CAAC;IAChE,MAAMU,EAAE,GAAGiG,eAAe,EAAEjG,EAAE;IAC9B,MAAMqE,UAAU,GAAG4B,eAAe,EAAE5B,UAAU;IAC9C,MAAMzB,MAAM,GAAG,UAAU;IACzB,MAAMuC,UAAU,GAAGc,eAAe,EAAEd,UAAU;IAC9C,MAAMG,OAAO,GAAGR,QAAQ;IACxBlE,OAAO,CAACC,GAAG,CAACb,EAAE,EAAEqE,UAAU,EAAEzB,MAAM,EAAEuC,UAAU,EAAEG,OAAO,CAAC;IACxD,IAAI,CAAC5H,eAAe,CACjB0I,cAAc,CAACpG,EAAE,EAAEqE,UAAU,EAAEzB,MAAM,EAAEuC,UAAU,EAAEG,OAAO,CAAC,CAC3DpE,SAAS,CAAC;MACTmE,IAAI,EAAG5C,QAAa,IAAI;QACtB,IAAI,CAACpD,WAAW,GACdoD,QAAQ,EAAE6C,OAAO,IAAI,IAAI,CAACzH,MAAM,CAACwI,4BAA4B;QAC/D,IAAI,CAAClH,aAAa,GAAG,IAAI;MAC3B,CAAC;MACDqG,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACpG,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACC,WAAW,GACdmG,KAAK,EAAEA,KAAK,EAAEF,OAAO,IAAI,IAAI,CAACzH,MAAM,CAAC4H,mBAAmB;QAC1D7E,OAAO,CAAC4E,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;MAChC;KACD,CAAC;EACN;EAEOc,eAAeA,CAAA;IACpB,IAAI,CAACnH,aAAa,GAAG,KAAK;IAE1B;IACA,IAAI,IAAI,CAACF,UAAU,IAAI,QAAQ,IAAI,IAAI,CAACQ,eAAe,EAAE;MACvD;MACA;MACA,IAAI,CAAClC,MAAM,CAACmG,QAAQ,CAAC,CAAC,6BAA6B,CAAC,EAAE;QACpDc,WAAW,EAAE;UACXxE,EAAE,EAAE,IAAI,CAACP,eAAe;UACxB8G,IAAI,EAAE;;OAET,CAAC;MACF;MACA,IAAI,CAAC9G,eAAe,GAAG,IAAI;MAC3B;IACF;IAEA,IAAI,IAAI,CAACR,UAAU,IAAI,OAAO,EAAE;MAC9B,IAAI,CAACsD,eAAe,EAAE;IACxB,CAAC,MAAM,IAAI,IAAI,CAACtD,UAAU,IAAI,QAAQ,EAAE;MACtC,IAAI,CAACgE,iBAAiB,EAAE;IAC1B,CAAC,MAAM;MACL,IAAI,CAACH,mBAAmB,EAAE;IAC5B;EACF;EAEOxB,qBAAqBA,CAACQ,IAAW,EAAEyC,IAAY;IACpD,IAAI,CAACnG,eAAe,GAAG;MACrBoI,QAAQ,EAAE1E,IAAI,EAAE1E,GAAG,CAAEqJ,GAAQ,IAAI;QAC/B,MAAMC,WAAW,GAAkC;UACjDC,QAAQ,EAAE,kBAAkB;UAC5BC,QAAQ,EAAE,UAAU;UACpBC,MAAM,EAAE;SACT;QACD,MAAMC,WAAW,GAAkC;UACjDH,QAAQ,EAAE,IAAI,CAAC9I,MAAM,CAAC8I,QAAQ;UAC9BC,QAAQ,EAAE,IAAI,CAAC/I,MAAM,CAAC+I,QAAQ;UAC9BC,MAAM,EAAE,IAAI,CAAChJ,MAAM,CAACgJ;SACrB;QACD,MAAME,SAAS,GAAG,IAAI,CAACzE,eAAe,CAACmE,GAAG,EAAE7D,MAAM,CAAC;QACnD,IAAIoE,UAAU,GAAG,CAAC;QAClB,IAAIC,KAAK,GAAG,EAAE;QAEd,IAAI1C,IAAI,KAAK,MAAM,EAAE;UACnByC,UAAU,GAAGP,GAAG,CAACxC,MAAM;UACvBgD,KAAK,GAAGR,GAAG,CAAC7E,QAAQ;QACtB,CAAC,MAAM,IAAI2C,IAAI,KAAK,OAAO,EAAE;UAC3ByC,UAAU,GAAGP,GAAG,CAACtC,OAAO;UACxB8C,KAAK,GAAGR,GAAG,CAAC/E,SAAS;QACvB,CAAC,MAAM;UACLsF,UAAU,GAAGP,GAAG,CAACpC,UAAU;UAC3B4C,KAAK,GAAGR,GAAG,CAACS,YAAY;QAC1B;QAEA,OAAO;UACLlH,EAAE,EAAEyG,GAAG,CAACzG,EAAE;UACVmH,KAAK,EAAEH,UAAU;UACjBzC,IAAI,EAAEA,IAAI;UACV6C,QAAQ,EAAE;YACRH,KAAK,EAAEA,KAAK;YACZpJ,MAAM,EAAE,CACN;cACE8B,IAAI,EAAE4E,IAAI;cACV8C,KAAK,EAAE,SAAS;cAChBC,UAAU,EAAE,KAAK;cACjB/C,IAAI,EAAE;aACP,EACD;cACE5E,IAAI,EAAE8G,GAAG,CAACc,iBAAiB;cAC3BF,KAAK,EAAEZ,GAAG,CAACc,iBAAiB,KAAK,QAAQ,GAAG,OAAO,GAAG,MAAM;cAC5DD,UAAU,EAAE,KAAK;cACjB/C,IAAI,EAAE;aACP;WAEJ;UACDiD,QAAQ,EAAE,CACR;YACE7H,IAAI,EAAE4E,IAAI;YACV8C,KAAK,EAAE,SAAS;YAChBC,UAAU,EAAE,KAAK;YACjB/C,IAAI,EAAE;WACP,EACD;YACE5E,IAAI,EAAE8G,GAAG,CAAC7D,MAAM;YAChByE,KAAK,EAAE,SAAS;YAChBC,UAAU,EAAE,KAAK;YACjB/C,IAAI,EAAE;WACP,CACF;UACDkD,QAAQ,EAAE,CACR;YACEC,QAAQ,EAAE,MAAM;YAChBnH,KAAK,EAAEkG,GAAG,CAACkB;WACZ,EACD;YACED,QAAQ,EAAE,eAAe;YACzBnH,KAAK,EAAE3D,UAAU,CAAC6J,GAAG,EAAEmB,WAAW,EAAE,aAAa,EAAE,OAAO;WAC3D,CACF;UACDC,QAAQ,EAAE;YACRjF,MAAM,EAAEkE,WAAW,CAACC,SAAS,CAAC;YAC9BW,QAAQ,EAAEhB,WAAW,CAACK,SAAS;;SAElC;MACH,CAAC,CAAC;MACFe,MAAM,EAAE;KACT;EACH;CACD;AApkBYxK,iBAAiB,GAAAyK,UAAA,EAd7BtL,SAAS,CAAC;EACTuL,QAAQ,EAAE,cAAc;EACxBC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPvL,YAAY,EACZE,YAAY,EACZC,cAAc,EACdC,0BAA0B,EAC1BC,mBAAmB,CACpB;EACDmL,OAAO,EAAE,CAACzL,sBAAsB,CAAC;EACjC0L,WAAW,EAAE,2BAA2B;EACxCC,QAAQ,EAAE;CACX,CAAC,C,EACW/K,iBAAiB,CAokB7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}