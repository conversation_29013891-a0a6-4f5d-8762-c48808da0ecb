{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { IconComponent } from \"@ava/play-comp-library\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction FilterTabsComponent_button_3_ava_icon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ava-icon\", 6);\n  }\n  if (rf & 2) {\n    const tab_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"iconName\", tab_r2.icon)(\"iconColor\", tab_r2.iconColor || \"#000\");\n  }\n}\nfunction FilterTabsComponent_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 4);\n    i0.ɵɵlistener(\"click\", function FilterTabsComponent_button_3_Template_button_click_0_listener() {\n      const tab_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onTabClick(tab_r2.id));\n    });\n    i0.ɵɵtemplate(1, FilterTabsComponent_button_3_ava_icon_1_Template, 1, 2, \"ava-icon\", 5);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tab_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r2.isActiveTab(tab_r2.id))(\"disabled\", tab_r2.disabled);\n    i0.ɵɵproperty(\"disabled\", tab_r2.disabled);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", tab_r2.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tab_r2.label);\n  }\n}\nexport let FilterTabsComponent = /*#__PURE__*/(() => {\n  class FilterTabsComponent {\n    tabs = [];\n    activeTab = 'all';\n    tabChange = new EventEmitter();\n    visibleTabs = [];\n    dropdownTabs = [];\n    showDropdown = false;\n    constructor() {}\n    ngOnInit() {\n      // Sort tabs by priority if provided\n      this.tabs = this.tabs.map((tab, index) => ({\n        ...tab,\n        priority: tab.priority ?? this.tabs.length - index // Default priority based on order\n      })).sort((a, b) => (b.priority || 0) - (a.priority || 0));\n      this.updateTabsVisibility();\n    }\n    ngAfterViewInit() {\n      setTimeout(() => {\n        this.updateTabsVisibility();\n      });\n    }\n    onResize() {\n      this.updateTabsVisibility();\n    }\n    onDocumentClick(event) {\n      const filterButton = document.querySelector('.filter-dropdown-btn');\n      const dropdown = document.querySelector('.filter-dropdown');\n      if (!filterButton?.contains(event.target) && !dropdown?.contains(event.target)) {\n        this.showDropdown = false;\n      }\n    }\n    updateTabsVisibility() {\n      const container = document.querySelector('.tabs-container');\n      if (!container) return;\n      const containerWidth = container.clientWidth;\n      const filterButtonWidth = 100; // Width reserved for filter button\n      const availableWidth = containerWidth - filterButtonWidth;\n      // Reset tabs\n      this.visibleTabs = [];\n      this.dropdownTabs = [];\n      let currentWidth = 0;\n      const averageTabWidth = availableWidth / this.tabs.length;\n      // Distribute tabs based on available space\n      for (const tab of this.tabs) {\n        const estimatedWidth = this.calculateTabWidth(tab);\n        if (currentWidth + estimatedWidth <= availableWidth && estimatedWidth <= averageTabWidth * 1.5) {\n          this.visibleTabs.push(tab);\n          currentWidth += estimatedWidth;\n        } else {\n          this.dropdownTabs.push(tab);\n        }\n      }\n    }\n    calculateTabWidth(tab) {\n      // Approximate width calculation based on text length and padding\n      const textWidth = tab.label.length * 8; // Approximate 8px per character\n      const padding = 32; // Left and right padding\n      const iconWidth = tab.icon ? 24 : 0; // Icon width if present\n      const gap = 8; // Gap between icon and text\n      return textWidth + padding + iconWidth + gap;\n    }\n    toggleDropdown(event) {\n      event.stopPropagation();\n      this.showDropdown = !this.showDropdown;\n    }\n    onTabClick(tabId) {\n      const tab = this.tabs.find(t => t.id === tabId);\n      if (tab?.disabled) {\n        return; // Don't allow clicking disabled tabs\n      }\n      if (this.activeTab !== tabId) {\n        this.activeTab = tabId;\n        this.tabChange.emit(tabId);\n      }\n      this.showDropdown = false;\n    }\n    isActiveTab(tabId) {\n      return this.activeTab === tabId;\n    }\n    static ɵfac = function FilterTabsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FilterTabsComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FilterTabsComponent,\n      selectors: [[\"app-filter-tabs\"]],\n      hostBindings: function FilterTabsComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"resize\", function FilterTabsComponent_resize_HostBindingHandler() {\n            return ctx.onResize();\n          }, false, i0.ɵɵresolveWindow)(\"click\", function FilterTabsComponent_click_HostBindingHandler($event) {\n            return ctx.onDocumentClick($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      inputs: {\n        tabs: \"tabs\",\n        activeTab: \"activeTab\"\n      },\n      outputs: {\n        tabChange: \"tabChange\"\n      },\n      decls: 4,\n      vars: 1,\n      consts: [[1, \"filter-tabs\"], [1, \"tabs-wrapper\"], [1, \"tabs-container\"], [\"class\", \"tab-item\", 3, \"active\", \"disabled\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"tab-item\", 3, \"click\", \"disabled\"], [\"iconSize\", \"24px\", 3, \"iconName\", \"iconColor\", 4, \"ngIf\"], [\"iconSize\", \"24px\", 3, \"iconName\", \"iconColor\"]],\n      template: function FilterTabsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtemplate(3, FilterTabsComponent_button_3_Template, 4, 7, \"button\", 3);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.visibleTabs);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, IconComponent],\n      styles: [\".filter-tabs[_ngcontent-%COMP%] {\\n  background-color: rgba(237, 237, 243, 0.5);\\n  border-radius: 12px;\\n  padding: 8px;\\n  margin: 48px 0 24px 0;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\\n  border: 1px solid var(--Border-Color, #E5E7EB);\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .tabs-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 4px;\\n  position: relative;\\n  width: 100%;\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .tabs-wrapper[_ngcontent-%COMP%]   .tabs-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex: 1;\\n  gap: 8px;\\n  overflow-x: auto;\\n  scrollbar-width: none;\\n  \\n\\n  -ms-overflow-style: none;\\n  \\n\\n  margin-right: 8px;\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .tabs-wrapper[_ngcontent-%COMP%]   .tabs-container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none;\\n  \\n\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .tabs-wrapper[_ngcontent-%COMP%]   .tabs-container[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: fit-content;\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n  padding: 8px 0;\\n  border-radius: 8px;\\n  border: none;\\n  background: transparent;\\n  color: var(--Text-Body, #000);\\n  font-family: Mulish;\\n  font-size: 18px;\\n  font-style: normal;\\n  font-weight: 400;\\n  line-height: normal;\\n  cursor: pointer;\\n  transition: all 0.2s ease-in-out;\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .tab-item.disabled[_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n  pointer-events: auto;\\n  color: var(--Text-Disabled, #9CA3AF);\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .tab-item.disabled[_ngcontent-%COMP%]:hover {\\n  background: transparent;\\n  transform: none;\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]:hover {\\n  background-color: white;\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .tab-item.active[_ngcontent-%COMP%] {\\n  background-color: white;\\n  background-image: linear-gradient(90deg, rgb(247, 145, 28) 40%, rgb(67, 131, 230) 70%);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n  color: transparent;\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .tab-item.active[_ngcontent-%COMP%]   ava-icon[_ngcontent-%COMP%] {\\n  background: none !important;\\n  -webkit-background-clip: initial !important;\\n  -webkit-text-fill-color: initial !important;\\n  background-clip: initial !important;\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .tab-item.active[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  background: inherit;\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .filter-dropdown-container[_ngcontent-%COMP%] {\\n  position: sticky;\\n  right: 4px;\\n  top: 4px;\\n  margin-left: 8px;\\n  z-index: 10;\\n  background-color: var(--background-color);\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .filter-dropdown-container[_ngcontent-%COMP%]   .filter-dropdown-btn[_ngcontent-%COMP%] {\\n  border: 1px solid var(--Border-Color, #E5E7EB);\\n  padding-right: 12px;\\n  white-space: nowrap;\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .filter-dropdown-container[_ngcontent-%COMP%]   .filter-dropdown-btn[_ngcontent-%COMP%]   i.fa-chevron-down[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  transition: transform 0.2s ease-in-out;\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .filter-dropdown-container[_ngcontent-%COMP%]   .filter-dropdown-btn.active[_ngcontent-%COMP%]   i.fa-chevron-down[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .filter-dropdown-container[_ngcontent-%COMP%]   .filter-dropdown[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: calc(100% + 8px);\\n  right: 0;\\n  background-color: var(--background-color);\\n  border-radius: 12px;\\n  border: 1px solid var(--Border-Color, #E5E7EB);\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\\n  min-width: 200px;\\n  z-index: 1000;\\n  padding: 8px;\\n  animation: _ngcontent-%COMP%_fadeIn 0.2s ease-in-out;\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .filter-dropdown-container[_ngcontent-%COMP%]   .filter-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  width: 100%;\\n  padding: 8px 16px;\\n  border: none;\\n  background: transparent;\\n  color: var(--Text-Body, #666D99);\\n  font-size: 14px;\\n  font-weight: 500;\\n  text-align: left;\\n  cursor: pointer;\\n  transition: all 0.2s ease-in-out;\\n  border-radius: 6px;\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .filter-dropdown-container[_ngcontent-%COMP%]   .filter-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 20px;\\n  text-align: center;\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .filter-dropdown-container[_ngcontent-%COMP%]   .filter-dropdown[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]:hover {\\n  background-color: var(--Hover-Color, rgba(0, 0, 0, 0.05));\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .filter-dropdown-container[_ngcontent-%COMP%]   .filter-dropdown[_ngcontent-%COMP%]   .dropdown-item.active[_ngcontent-%COMP%] {\\n  background-color: white;\\n  background-image: linear-gradient(90deg, #8B8DDA 0%, #F63B8F 100%);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n  color: transparent;\\n}\\n.filter-tabs[_ngcontent-%COMP%]   .filter-dropdown-container[_ngcontent-%COMP%]   .filter-dropdown[_ngcontent-%COMP%]   .dropdown-item.active[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  background: inherit;\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .filter-tabs[_ngcontent-%COMP%] {\\n    padding: 6px;\\n  }\\n  .filter-tabs[_ngcontent-%COMP%]   .tabs-wrapper[_ngcontent-%COMP%] {\\n    gap: 6px;\\n  }\\n  .filter-tabs[_ngcontent-%COMP%]   .tabs-wrapper[_ngcontent-%COMP%]   .tabs-container[_ngcontent-%COMP%] {\\n    gap: 6px;\\n  }\\n  .filter-tabs[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%] {\\n    padding: 6px 12px;\\n    font-size: 13px;\\n  }\\n  .filter-tabs[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .filter-tabs[_ngcontent-%COMP%]   .filter-dropdown-container[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%] {\\n    padding: 6px 12px;\\n    font-size: 13px;\\n  }\\n  .filter-tabs[_ngcontent-%COMP%]   .filter-dropdown-container[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return FilterTabsComponent;\n})();", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "IconComponent", "i0", "ɵɵelement", "ɵɵproperty", "tab_r2", "icon", "iconColor", "ɵɵelementStart", "ɵɵlistener", "FilterTabsComponent_button_3_Template_button_click_0_listener", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "onTabClick", "id", "ɵɵtemplate", "FilterTabsComponent_button_3_ava_icon_1_Template", "ɵɵtext", "ɵɵelementEnd", "ɵɵclassProp", "isActiveTab", "disabled", "ɵɵadvance", "ɵɵtextInterpolate", "label", "FilterTabsComponent", "tabs", "activeTab", "tabChange", "visibleTabs", "dropdownTabs", "showDropdown", "constructor", "ngOnInit", "map", "tab", "index", "priority", "length", "sort", "a", "b", "updateTabsVisibility", "ngAfterViewInit", "setTimeout", "onResize", "onDocumentClick", "event", "filterButton", "document", "querySelector", "dropdown", "contains", "target", "container", "containerWidth", "clientWidth", "filter<PERSON>utton<PERSON><PERSON><PERSON>", "availableWidth", "currentWidth", "averageTabWidth", "estimatedWidth", "calculateTabWidth", "push", "textWidth", "padding", "iconWidth", "gap", "toggleDropdown", "stopPropagation", "tabId", "find", "t", "emit", "selectors", "hostBindings", "FilterTabsComponent_HostBindings", "rf", "ctx", "FilterTabsComponent_resize_HostBindingHandler", "ɵɵresolveWindow", "FilterTabsComponent_click_HostBindingHandler", "$event", "ɵɵresolveDocument", "FilterTabsComponent_button_3_Template", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\shared\\components\\filter-tabs\\filter-tabs.component.ts", "C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\shared\\components\\filter-tabs\\filter-tabs.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  Input,\r\n  Output,\r\n  EventEmitter,\r\n  HostListener,\r\n  OnInit,\r\n  AfterViewInit,\r\n} from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { IconComponent } from \"@ava/play-comp-library\";\r\n\r\nexport interface FilterTab {\r\n  id: string;\r\n  label: string;\r\n  icon?: string;\r\n  iconColor?: string;\r\n  priority?: number;\r\n  disabled?: boolean;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-filter-tabs',\r\n  templateUrl: './filter-tabs.component.html',\r\n  styleUrls: ['./filter-tabs.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, IconComponent],\r\n})\r\nexport class FilterTabsComponent implements OnInit, AfterViewInit {\r\n  @Input() tabs: FilterTab[] = [];\r\n  @Input() activeTab: string = 'all';\r\n  @Output() tabChange = new EventEmitter<string>();\r\n\r\n  visibleTabs: FilterTab[] = [];\r\n  dropdownTabs: FilterTab[] = [];\r\n  showDropdown = false;\r\n\r\n  constructor() { }\r\n\r\n  ngOnInit(): void {\r\n    // Sort tabs by priority if provided\r\n    this.tabs = this.tabs\r\n      .map((tab, index) => ({\r\n        ...tab,\r\n        priority: tab.priority ?? this.tabs.length - index, // Default priority based on order\r\n      }))\r\n      .sort((a, b) => (b.priority || 0) - (a.priority || 0));\r\n\r\n    this.updateTabsVisibility();\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n    setTimeout(() => {\r\n      this.updateTabsVisibility();\r\n    });\r\n  }\r\n\r\n  @HostListener('window:resize')\r\n  onResize() {\r\n    this.updateTabsVisibility();\r\n  }\r\n\r\n  @HostListener('document:click', ['$event'])\r\n  onDocumentClick(event: MouseEvent) {\r\n    const filterButton = document.querySelector('.filter-dropdown-btn');\r\n    const dropdown = document.querySelector('.filter-dropdown');\r\n    if (\r\n      !filterButton?.contains(event.target as Node) &&\r\n      !dropdown?.contains(event.target as Node)\r\n    ) {\r\n      this.showDropdown = false;\r\n    }\r\n  }\r\n\r\n  updateTabsVisibility() {\r\n    const container = document.querySelector('.tabs-container');\r\n    if (!container) return;\r\n\r\n    const containerWidth = container.clientWidth;\r\n    const filterButtonWidth = 100; // Width reserved for filter button\r\n    const availableWidth = containerWidth - filterButtonWidth;\r\n\r\n    // Reset tabs\r\n    this.visibleTabs = [];\r\n    this.dropdownTabs = [];\r\n\r\n    let currentWidth = 0;\r\n    const averageTabWidth = availableWidth / this.tabs.length;\r\n\r\n    // Distribute tabs based on available space\r\n    for (const tab of this.tabs) {\r\n      const estimatedWidth = this.calculateTabWidth(tab);\r\n      if (currentWidth + estimatedWidth <= availableWidth && estimatedWidth <= averageTabWidth * 1.5) {\r\n        this.visibleTabs.push(tab);\r\n        currentWidth += estimatedWidth;\r\n      } else {\r\n        this.dropdownTabs.push(tab);\r\n      }\r\n    }\r\n  }\r\n\r\n  private calculateTabWidth(tab: FilterTab): number {\r\n    // Approximate width calculation based on text length and padding\r\n    const textWidth = tab.label.length * 8; // Approximate 8px per character\r\n    const padding = 32; // Left and right padding\r\n    const iconWidth = tab.icon ? 24 : 0; // Icon width if present\r\n    const gap = 8; // Gap between icon and text\r\n    return textWidth + padding + iconWidth + gap;\r\n  }\r\n\r\n  toggleDropdown(event: Event) {\r\n    event.stopPropagation();\r\n    this.showDropdown = !this.showDropdown;\r\n  }\r\n\r\n  onTabClick(tabId: string): void {\r\n    const tab = this.tabs.find(t => t.id === tabId);\r\n    if (tab?.disabled) {\r\n      return; // Don't allow clicking disabled tabs\r\n    }\r\n    \r\n    if (this.activeTab !== tabId) {\r\n      this.activeTab = tabId;\r\n      this.tabChange.emit(tabId);\r\n    }\r\n    this.showDropdown = false;\r\n  }\r\n\r\n  isActiveTab(tabId: string): boolean {\r\n    return this.activeTab === tabId;\r\n  }\r\n}\r\n ", "<div class=\"filter-tabs\">\r\n  <div class=\"tabs-wrapper\">\r\n    <!-- Visible Tabs Container -->\r\n    <div class=\"tabs-container\">\r\n      <button *ngFor=\"let tab of visibleTabs\" \r\n              class=\"tab-item\" \r\n              [class.active]=\"isActiveTab(tab.id)\"\r\n              [class.disabled]=\"tab.disabled\"\r\n              [disabled]=\"tab.disabled\"\r\n              (click)=\"onTabClick(tab.id)\">\r\n        <ava-icon *ngIf=\"tab.icon\" [iconName]=\"tab.icon\" iconSize=\"24px\" [iconColor]=\"tab.iconColor || '#000'\"></ava-icon>\r\n        <span>{{ tab.label }}</span>\r\n      </button>\r\n    </div>\r\n  </div>\r\n</div>\r\n "], "mappings": "AAAA,SAIEA,YAAY,QAIP,eAAe;AACtB,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;;;;;ICA9CC,EAAA,CAAAC,SAAA,kBAAkH;;;;IAAjDD,EAAtC,CAAAE,UAAA,aAAAC,MAAA,CAAAC,IAAA,CAAqB,cAAAD,MAAA,CAAAE,SAAA,WAAsD;;;;;;IANxGL,EAAA,CAAAM,cAAA,gBAKqC;IAA7BN,EAAA,CAAAO,UAAA,mBAAAC,8DAAA;MAAA,MAAAL,MAAA,GAAAH,EAAA,CAAAS,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAZ,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASF,MAAA,CAAAG,UAAA,CAAAZ,MAAA,CAAAa,EAAA,CAAkB;IAAA,EAAC;IAClChB,EAAA,CAAAiB,UAAA,IAAAC,gDAAA,sBAAuG;IACvGlB,EAAA,CAAAM,cAAA,WAAM;IAAAN,EAAA,CAAAmB,MAAA,GAAe;IACvBnB,EADuB,CAAAoB,YAAA,EAAO,EACrB;;;;;IALDpB,EADA,CAAAqB,WAAA,WAAAT,MAAA,CAAAU,WAAA,CAAAnB,MAAA,CAAAa,EAAA,EAAoC,aAAAb,MAAA,CAAAoB,QAAA,CACL;IAC/BvB,EAAA,CAAAE,UAAA,aAAAC,MAAA,CAAAoB,QAAA,CAAyB;IAEpBvB,EAAA,CAAAwB,SAAA,EAAc;IAAdxB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAC,IAAA,CAAc;IACnBJ,EAAA,CAAAwB,SAAA,GAAe;IAAfxB,EAAA,CAAAyB,iBAAA,CAAAtB,MAAA,CAAAuB,KAAA,CAAe;;;ADiB7B,WAAaC,mBAAmB;EAA1B,MAAOA,mBAAmB;IACrBC,IAAI,GAAgB,EAAE;IACtBC,SAAS,GAAW,KAAK;IACxBC,SAAS,GAAG,IAAIjC,YAAY,EAAU;IAEhDkC,WAAW,GAAgB,EAAE;IAC7BC,YAAY,GAAgB,EAAE;IAC9BC,YAAY,GAAG,KAAK;IAEpBC,YAAA,GAAgB;IAEhBC,QAAQA,CAAA;MACN;MACA,IAAI,CAACP,IAAI,GAAG,IAAI,CAACA,IAAI,CAClBQ,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,MAAM;QACpB,GAAGD,GAAG;QACNE,QAAQ,EAAEF,GAAG,CAACE,QAAQ,IAAI,IAAI,CAACX,IAAI,CAACY,MAAM,GAAGF,KAAK,CAAE;OACrD,CAAC,CAAC,CACFG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACA,CAAC,CAACJ,QAAQ,IAAI,CAAC,KAAKG,CAAC,CAACH,QAAQ,IAAI,CAAC,CAAC,CAAC;MAExD,IAAI,CAACK,oBAAoB,EAAE;IAC7B;IAEAC,eAAeA,CAAA;MACbC,UAAU,CAAC,MAAK;QACd,IAAI,CAACF,oBAAoB,EAAE;MAC7B,CAAC,CAAC;IACJ;IAGAG,QAAQA,CAAA;MACN,IAAI,CAACH,oBAAoB,EAAE;IAC7B;IAGAI,eAAeA,CAACC,KAAiB;MAC/B,MAAMC,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,sBAAsB,CAAC;MACnE,MAAMC,QAAQ,GAAGF,QAAQ,CAACC,aAAa,CAAC,kBAAkB,CAAC;MAC3D,IACE,CAACF,YAAY,EAAEI,QAAQ,CAACL,KAAK,CAACM,MAAc,CAAC,IAC7C,CAACF,QAAQ,EAAEC,QAAQ,CAACL,KAAK,CAACM,MAAc,CAAC,EACzC;QACA,IAAI,CAACtB,YAAY,GAAG,KAAK;MAC3B;IACF;IAEAW,oBAAoBA,CAAA;MAClB,MAAMY,SAAS,GAAGL,QAAQ,CAACC,aAAa,CAAC,iBAAiB,CAAC;MAC3D,IAAI,CAACI,SAAS,EAAE;MAEhB,MAAMC,cAAc,GAAGD,SAAS,CAACE,WAAW;MAC5C,MAAMC,iBAAiB,GAAG,GAAG,CAAC,CAAC;MAC/B,MAAMC,cAAc,GAAGH,cAAc,GAAGE,iBAAiB;MAEzD;MACA,IAAI,CAAC5B,WAAW,GAAG,EAAE;MACrB,IAAI,CAACC,YAAY,GAAG,EAAE;MAEtB,IAAI6B,YAAY,GAAG,CAAC;MACpB,MAAMC,eAAe,GAAGF,cAAc,GAAG,IAAI,CAAChC,IAAI,CAACY,MAAM;MAEzD;MACA,KAAK,MAAMH,GAAG,IAAI,IAAI,CAACT,IAAI,EAAE;QAC3B,MAAMmC,cAAc,GAAG,IAAI,CAACC,iBAAiB,CAAC3B,GAAG,CAAC;QAClD,IAAIwB,YAAY,GAAGE,cAAc,IAAIH,cAAc,IAAIG,cAAc,IAAID,eAAe,GAAG,GAAG,EAAE;UAC9F,IAAI,CAAC/B,WAAW,CAACkC,IAAI,CAAC5B,GAAG,CAAC;UAC1BwB,YAAY,IAAIE,cAAc;QAChC,CAAC,MAAM;UACL,IAAI,CAAC/B,YAAY,CAACiC,IAAI,CAAC5B,GAAG,CAAC;QAC7B;MACF;IACF;IAEQ2B,iBAAiBA,CAAC3B,GAAc;MACtC;MACA,MAAM6B,SAAS,GAAG7B,GAAG,CAACX,KAAK,CAACc,MAAM,GAAG,CAAC,CAAC,CAAC;MACxC,MAAM2B,OAAO,GAAG,EAAE,CAAC,CAAC;MACpB,MAAMC,SAAS,GAAG/B,GAAG,CAACjC,IAAI,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;MACrC,MAAMiE,GAAG,GAAG,CAAC,CAAC,CAAC;MACf,OAAOH,SAAS,GAAGC,OAAO,GAAGC,SAAS,GAAGC,GAAG;IAC9C;IAEAC,cAAcA,CAACrB,KAAY;MACzBA,KAAK,CAACsB,eAAe,EAAE;MACvB,IAAI,CAACtC,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;IACxC;IAEAlB,UAAUA,CAACyD,KAAa;MACtB,MAAMnC,GAAG,GAAG,IAAI,CAACT,IAAI,CAAC6C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC1D,EAAE,KAAKwD,KAAK,CAAC;MAC/C,IAAInC,GAAG,EAAEd,QAAQ,EAAE;QACjB,OAAO,CAAC;MACV;MAEA,IAAI,IAAI,CAACM,SAAS,KAAK2C,KAAK,EAAE;QAC5B,IAAI,CAAC3C,SAAS,GAAG2C,KAAK;QACtB,IAAI,CAAC1C,SAAS,CAAC6C,IAAI,CAACH,KAAK,CAAC;MAC5B;MACA,IAAI,CAACvC,YAAY,GAAG,KAAK;IAC3B;IAEAX,WAAWA,CAACkD,KAAa;MACvB,OAAO,IAAI,CAAC3C,SAAS,KAAK2C,KAAK;IACjC;;uCAtGW7C,mBAAmB;IAAA;;YAAnBA,mBAAmB;MAAAiD,SAAA;MAAAC,YAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAnB/E,EAAA,CAAAO,UAAA,oBAAA0E,8CAAA;YAAA,OAAAD,GAAA,CAAAjC,QAAA,EAAU;UAAA,UAAA/C,EAAA,CAAAkF,eAAA,CAAS,mBAAAC,6CAAAC,MAAA;YAAA,OAAnBJ,GAAA,CAAAhC,eAAA,CAAAoC,MAAA,CAAuB;UAAA,UAAApF,EAAA,CAAAqF,iBAAA,CAAJ;;;;;;;;;;;;;;;UCzB5BrF,EAHJ,CAAAM,cAAA,aAAyB,aACG,aAEI;UAC1BN,EAAA,CAAAiB,UAAA,IAAAqE,qCAAA,oBAKqC;UAM3CtF,EAFI,CAAAoB,YAAA,EAAM,EACF,EACF;;;UAXwBpB,EAAA,CAAAwB,SAAA,GAAc;UAAdxB,EAAA,CAAAE,UAAA,YAAA8E,GAAA,CAAAjD,WAAA,CAAc;;;qBDsBhCjC,YAAY,EAAAyF,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE1F,aAAa;MAAA2F,MAAA;IAAA;;SAE1B/D,mBAAmB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}