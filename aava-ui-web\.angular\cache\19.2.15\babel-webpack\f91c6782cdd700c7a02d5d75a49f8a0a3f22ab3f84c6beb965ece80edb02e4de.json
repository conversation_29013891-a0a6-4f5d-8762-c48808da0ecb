{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, Validators } from '@angular/forms';\nimport { AvaTextboxComponent, AvaTextareaComponent, ButtonComponent, DropdownComponent, ListComponent, PopupComponent } from '@ava/play-comp-library';\nimport modelText from '../constants/models.json';\nimport { LucideAngularModule } from 'lucide-angular';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common/http\";\nimport * as i4 from \"@shared/services/model.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"lucide-angular\";\nconst _c0 = a0 => ({\n  \"ava-list-disabled\": a0\n});\nconst _c1 = () => ({\n  background: \"linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)\",\n  \"--button-effect-color\": \"33, 90, 214\"\n});\nfunction CreateModelsComponent_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 30);\n    i0.ɵɵtext(1, \"Model Description\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreateModelsComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵelement(1, \"ava-textbox\", 31)(2, \"ava-textarea\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", ctx_r0.labels.name)(\"id\", \"modelName\")(\"placeholder\", ctx_r0.labels.placeholderModelName)(\"formControl\", ctx_r0.getControl(\"modelDeploymentName\"))(\"error\", ctx_r0.getFieldError(\"modelDeploymentName\"))(\"required\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", ctx_r0.labels.description)(\"placeholder\", ctx_r0.labels.placeholderModelDescription)(\"formControl\", ctx_r0.getControl(\"modelDescription\"))(\"required\", true)(\"error\", ctx_r0.getFieldError(\"modelDescription\"));\n  }\n}\nfunction CreateModelsComponent_ava_button_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ava-button\", 33);\n    i0.ɵɵlistener(\"userClick\", function CreateModelsComponent_ava_button_18_Template_ava_button_userClick_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onCancel());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreateModelsComponent_ava_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ava-button\", 34);\n    i0.ɵɵlistener(\"userClick\", function CreateModelsComponent_ava_button_19_Template_ava_button_userClick_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onSave());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", !(ctx_r0.modelForm.valid && ctx_r0.selectedAiEngineId && ctx_r0.selectedModelId))(\"customStyles\", i0.ɵɵpureFunction0(2, _c1));\n  }\n}\nfunction CreateModelsComponent_ava_button_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ava-button\", 35);\n    i0.ɵɵlistener(\"userClick\", function CreateModelsComponent_ava_button_20_Template_ava_button_userClick_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onCancel());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreateModelsComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtext(1, \" No models available for the selected type and engine. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreateModelsComponent_p_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.labels.pleaseSelectMessage, \" \");\n  }\n}\nfunction CreateModelsComponent_div_37_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"div\", 42);\n    i0.ɵɵelement(2, \"ava-textbox\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 42);\n    i0.ɵɵelement(4, \"ava-textbox\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 42);\n    i0.ɵɵelement(6, \"ava-textbox\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 42);\n    i0.ɵɵelement(8, \"ava-dropdown\", 45);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_29_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", ctx_r0.labels.baseurl)(\"id\", \"baseurl\")(\"placeholder\", ctx_r0.labels.placeholderBaseUrl)(\"formControl\", ctx_r0.getControl(\"baseurl\"))(\"error\", ctx_r0.getFieldError(\"baseurl\"))(\"required\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", ctx_r0.labels.llmDeploymentName)(\"id\", \"llmDeploymentName\")(\"placeholder\", ctx_r0.labels.placeholderLlmDeploymentName)(\"error\", ctx_r0.getFieldError(\"llmDeploymentName\"))(\"type\", \"text\")(\"formControl\", ctx_r0.getControl(\"llmDeploymentName\"))(\"required\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", ctx_r0.labels.apiKey)(\"id\", \"apiKey\")(\"placeholder\", ctx_r0.labels.placeholderApiKey)(\"error\", ctx_r0.getFieldError(\"apiKey\"))(\"formControl\", ctx_r0.getControl(\"apiKey\"))(\"required\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"dropdownTitle\", ctx_r0.labels.dropdownTitleApiVersion);\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r0.labels.apiVersion);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.isEditMode)(\"disabled\", ctx_r0.isEditMode)(\"options\", ctx_r0.apiVersionOptions)(\"formControl\", ctx_r0.getControl(\"apiVersion\"))(\"error\", ctx_r0.getFieldError(\"apiVersion\"))(\"required\", true)(\"selectedValue\", (tmp_29_0 = ctx_r0.modelForm.get(\"apiVersion\")) == null ? null : tmp_29_0.value);\n  }\n}\nfunction CreateModelsComponent_div_37_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"div\", 42);\n    i0.ɵɵelement(2, \"ava-textbox\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 42);\n    i0.ɵɵelement(4, \"ava-textbox\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 42);\n    i0.ɵɵelement(6, \"ava-textbox\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 42);\n    i0.ɵɵelement(8, \"ava-textbox\", 46);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", ctx_r0.labels.awsAccessKey)(\"id\", \"awsAccessKey\")(\"placeholder\", ctx_r0.labels.placeholderAwsAccessKey)(\"type\", \"text\")(\"formControl\", ctx_r0.getControl(\"awsAccessKey\"))(\"error\", ctx_r0.getFieldError(\"awsAccessKey\"))(\"required\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", ctx_r0.labels.awsSecretKey)(\"id\", \"awsSecretKey\")(\"placeholder\", ctx_r0.labels.placeholderAwsSecretKey)(\"formControl\", ctx_r0.getControl(\"awsSecretKey\"))(\"error\", ctx_r0.getFieldError(\"awsSecretKey\"))(\"required\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", ctx_r0.labels.awsRegion)(\"id\", \"awsRegion\")(\"placeholder\", ctx_r0.labels.placeholderAwsRegion)(\"type\", \"text\")(\"formControl\", ctx_r0.getControl(\"awsRegion\"))(\"error\", ctx_r0.getFieldError(\"awsRegion\"))(\"required\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", ctx_r0.labels.bedrockModelId)(\"id\", \"bedrockModelId\")(\"placeholder\", ctx_r0.labels.placeholderBedRockModel)(\"type\", \"text\")(\"formControl\", ctx_r0.getControl(\"bedrockModelId\"))(\"error\", ctx_r0.getFieldError(\"bedrockModelId\"))(\"required\", true);\n  }\n}\nfunction CreateModelsComponent_div_37_div_4_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵelement(1, \"ava-textbox\", 46);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", ctx_r0.labels.vertexAIEndpoint)(\"id\", \"vertexAIEndpoint\")(\"placeholder\", ctx_r0.labels.placeholderVertextAIEndPoint)(\"type\", \"url\")(\"formControl\", ctx_r0.getControl(\"vertexAIEndpoint\"))(\"error\", ctx_r0.getFieldError(\"vertexAIEndpoint\"))(\"required\", true);\n  }\n}\nfunction CreateModelsComponent_div_37_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"div\", 42);\n    i0.ɵɵelement(2, \"ava-textbox\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 42);\n    i0.ɵɵelement(4, \"ava-textbox\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, CreateModelsComponent_div_37_div_4_div_5_Template, 2, 7, \"div\", 48);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", ctx_r0.labels.gcpProjectId)(\"id\", \"gcpProjectId\")(\"placeholder\", ctx_r0.labels.placeholderGcpProjectId)(\"type\", \"text\")(\"formControl\", ctx_r0.getControl(\"gcpProjectId\"))(\"error\", ctx_r0.getFieldError(\"gcpProjectId\"))(\"required\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", ctx_r0.labels.gcpLocation)(\"id\", \"gcpLocation\")(\"placeholder\", ctx_r0.labels.placeholderGcpLocation)(\"type\", \"text\")(\"formControl\", ctx_r0.getControl(\"gcpLocation\"))(\"required\", true)(\"error\", ctx_r0.getFieldError(\"gcpLocation\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.selectedAiEngineId === \"GoogleAI\" && ctx_r0.selectedModelTypeId === \"Embedding\");\n  }\n}\nfunction CreateModelsComponent_div_37_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"div\", 42);\n    i0.ɵɵelement(2, \"ava-textbox\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 42);\n    i0.ɵɵelement(4, \"ava-textbox\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 42);\n    i0.ɵɵelement(6, \"ava-textbox\", 31);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", ctx_r0.labels.serviceUrl)(\"id\", \"serviceUrl\")(\"placeholder\", ctx_r0.labels.placeholderServiceUrl)(\"type\", \"url\")(\"formControl\", ctx_r0.getControl(\"serviceUrl\"))(\"error\", ctx_r0.getFieldError(\"serviceUrl\"))(\"required\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", ctx_r0.labels.apiKeyEncoded)(\"id\", \"apiKeyEncoded\")(\"placeholder\", ctx_r0.labels.placeholderEncodedApiKey)(\"formControl\", ctx_r0.getControl(\"apiKeyEncoded\"))(\"error\", ctx_r0.getFieldError(\"apiKeyEncoded\"))(\"required\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", ctx_r0.labels.headerName)(\"id\", \"headerName\")(\"placeholder\", ctx_r0.labels.placeholderHeaderName)(\"formControl\", ctx_r0.getControl(\"headerName\"))(\"error\", ctx_r0.getFieldError(\"headerName\"))(\"required\", true);\n  }\n}\nfunction CreateModelsComponent_div_37_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 41);\n  }\n}\nfunction CreateModelsComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"div\", 39);\n    i0.ɵɵtemplate(2, CreateModelsComponent_div_37_div_2_Template, 9, 28, \"div\", 40)(3, CreateModelsComponent_div_37_div_3_Template, 9, 27, \"div\", 40)(4, CreateModelsComponent_div_37_div_4_Template, 6, 15, \"div\", 40)(5, CreateModelsComponent_div_37_div_5_Template, 7, 19, \"div\", 40)(6, CreateModelsComponent_div_37_div_6_Template, 1, 0, \"div\", 40);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.inputVisibility.AzureOpenAI && ctx_r0.selectedModelTypeId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.inputVisibility.AmazonBedrock && ctx_r0.selectedModelTypeId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.inputVisibility.GoogleAI && ctx_r0.selectedModelTypeId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.inputVisibility.DaOpenSourceAI && ctx_r0.selectedModelTypeId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.inputVisibility.BNY && ctx_r0.selectedModelTypeId);\n  }\n}\nexport let CreateModelsComponent = /*#__PURE__*/(() => {\n  class CreateModelsComponent {\n    fb;\n    router;\n    route;\n    http;\n    modelService;\n    cdr;\n    iconName = 'info';\n    showSuccessPopup = false;\n    submissionSuccess = false;\n    popupTitle = '';\n    popupMessage = '';\n    modelId = null;\n    isEditMode = false;\n    modelForm;\n    aiEngines = [];\n    modelDropdownOptions = [];\n    amazonDropdownOptions = [];\n    googleDropdownOptions = [];\n    daDropdownOptions = [];\n    bnyDropdownOptions = [];\n    modelConfigurationOptions = [];\n    apiVersionOptions = [];\n    modelNames = [];\n    selectedAiEngineId = null;\n    selectedModelId = null;\n    modelTypeList = [];\n    selectedModelTypeId = null;\n    inputVisibility = {\n      AzureOpenAI: false,\n      AmazonBedrock: false,\n      GoogleAI: false,\n      DaOpenSourceAI: false,\n      BNY: false\n    };\n    mode = 'add';\n    labels = modelText.labels;\n    dropdownConfigs = [{\n      label: 'AI Engine',\n      targetKey: 'aiEngines',\n      mapToListItem: true\n    }, {\n      label: 'AzureOpenAI Model',\n      targetKey: 'modelDropdownOptions'\n    }, {\n      label: 'AmazonBedrock Model',\n      targetKey: 'amazonDropdownOptions'\n    }, {\n      label: 'GoogleAI Model',\n      targetKey: 'googleDropdownOptions'\n    }, {\n      label: 'DaOpenSourceAI Model',\n      targetKey: 'daDropdownOptions'\n    }, {\n      label: 'BNY Model',\n      targetKey: 'bnyDropdownOptions'\n    }, {\n      label: 'Model Type',\n      targetKey: 'modelTypeList',\n      mapToListItem: true\n    }, {\n      label: 'Api Version',\n      targetKey: 'apiVersionOptions',\n      includeInactive: true\n    }];\n    isLeftCollapsed = false;\n    toggleLeftPanel() {\n      this.isLeftCollapsed = !this.isLeftCollapsed;\n    }\n    constructor(fb, router, route, http, modelService, cdr) {\n      this.fb = fb;\n      this.router = router;\n      this.route = route;\n      this.http = http;\n      this.modelService = modelService;\n      this.cdr = cdr;\n      this.modelForm = this.fb.group({\n        modelDeploymentName: ['', [Validators.required, this.noSpacesValidator]],\n        modelDescription: ['', [Validators.required, this.noLeadingSpaceValidator]],\n        description: ['', this.noSpacesValidator],\n        modelType: ['', Validators.required],\n        model: [''],\n        organization: ['', this.noSpacesValidator],\n        domain: ['', this.noSpacesValidator],\n        project: ['', this.noSpacesValidator],\n        team: ['', this.noSpacesValidator],\n        baseurl: ['', [Validators.required, this.noSpacesValidator]],\n        llmDeploymentName: ['', this.noSpacesValidator],\n        apiKey: ['', this.noSpacesValidator],\n        apiVersion: ['', this.noSpacesValidator],\n        awsAccessKey: ['', this.noSpacesValidator],\n        awsSecretKey: ['', this.noSpacesValidator],\n        awsRegion: ['', this.noSpacesValidator],\n        bedrockModelId: ['', this.noSpacesValidator],\n        gcpProjectId: ['', this.noSpacesValidator],\n        gcpLocation: ['', this.noSpacesValidator],\n        vertexAIEndpoint: ['', this.noSpacesValidator],\n        serviceUrl: ['', this.noSpacesValidator],\n        apiKeyEncoded: ['', this.noSpacesValidator],\n        headerName: ['', this.noSpacesValidator],\n        aiEngine: ['', Validators.required]\n      });\n    }\n    noSpacesValidator = control => {\n      if (control.value && control.value.toString().includes(' ')) {\n        return {\n          containsSpaces: true\n        };\n      }\n      return null;\n    };\n    urlValidator(control) {\n      if (control.value && !/^(https?|ftp):\\/\\/[^\\s/$.?#].[^\\s]*$/.test(control.value)) {\n        return {\n          invalidUrl: true\n        };\n      }\n      return null;\n    }\n    noLeadingSpaceValidator(control) {\n      const value = control.value;\n      if (typeof value === 'string' && value.length > 0 && value.startsWith(' ')) {\n        return {\n          leadingSpace: true\n        };\n      }\n      return null;\n    }\n    // Lifecycle hook to initialize form and fetch dropdown values\n    ngOnInit() {\n      this.modelId = this.route.snapshot.paramMap.get('id');\n      this.isEditMode = !!this.modelId;\n      this.dropdownConfigs.forEach(({\n        label,\n        targetKey,\n        mapToListItem,\n        includeInactive\n      }) => {\n        this.modelService.getDropdownOptions(label, includeInactive).subscribe({\n          next: options => {\n            const mapped = mapToListItem ? options.map(o => ({\n              id: o.value,\n              title: o.label\n            })) : options.map(o => ({\n              name: o.label,\n              value: o.value\n            }));\n            this[targetKey] = mapped;\n            this.cdr.detectChanges();\n          },\n          error: error => console.error(`Error fetching ${label}:`, error)\n        });\n      });\n      if (this.isEditMode && this.modelId) {\n        this.loadModelData(this.modelId);\n        this.modelForm.disable();\n      }\n    }\n    // Load model data by ID and patch form values\n    loadModelData(modelId) {\n      this.mode = 'view';\n      this.modelService.getOneModeById(modelId).subscribe({\n        next: modelData => {\n          this.selectedAiEngineId = modelData.aiEngine;\n          this.selectedModelId = modelData.model;\n          this.selectedModelTypeId = modelData.modelType;\n          this.onAiEngineSelected({\n            id: modelData.aiEngine,\n            title: ''\n          });\n          setTimeout(() => {\n            this.modelForm.patchValue({\n              modelDeploymentName: modelData.modelDeploymentName,\n              description: modelData.modelDescription || modelData.description,\n              modelType: modelData.modelType,\n              modelDescription: modelData.modelDescription,\n              aiEngine: modelData.aiEngine,\n              model: modelData.model,\n              baseurl: modelData.baseurl,\n              llmDeploymentName: modelData.llmDeploymentName,\n              apiKey: modelData.apiKey,\n              apiVersion: modelData.apiVersion,\n              awsAccessKey: modelData.awsAccessKey,\n              awsSecretKey: modelData.awsSecretKey,\n              awsRegion: modelData.awsRegion,\n              bedrockModelId: modelData.bedrockModelId,\n              gcpProjectId: modelData.gcpProjectId,\n              gcpLocation: modelData.gcpLocation,\n              vertexAIEndpoint: modelData.vertexAIEndpoint,\n              serviceUrl: modelData.serviceUrl,\n              apiKeyEncoded: modelData.apiKeyEncoded,\n              headerName: modelData.headerName,\n              organization: modelData.organization,\n              domain: modelData.domain,\n              project: modelData.project,\n              team: modelData.team\n            });\n            this.cdr.detectChanges();\n            if (this.mode === 'view') {\n              this.modelForm.disable();\n            }\n          }, 100);\n        },\n        error: error => {\n          console.error('Error loading model data:', error);\n        }\n      });\n    }\n    areSelectionsComplete() {\n      return !!this.selectedAiEngineId && !!this.selectedModelTypeId && !!this.selectedModelId;\n    }\n    // Handle AI engine selection and update related fields and dropdowns\n    onAiEngineSelected(selectedItem) {\n      this.selectedAiEngineId = selectedItem.id;\n      const selectedEngine = selectedItem.id;\n      if (this.mode !== 'view') {\n        this.selectedModelId = null;\n        this.modelForm.patchValue({\n          aiEngine: selectedEngine,\n          model: null\n        }, {\n          emitEvent: false\n        });\n      }\n      if (selectedEngine) {\n        this.displayInputField(selectedEngine);\n        this.updateEngineFieldValidators(selectedEngine);\n        if (this.selectedModelTypeId) {\n          this.fetchModelsForTypeAndEngine(this.selectedModelTypeId, selectedEngine);\n        } else {\n          this.modelNames = [];\n        }\n      }\n    }\n    // Handle Model Type selection\n    onModelTypeSelected(selectedItem) {\n      this.selectedModelTypeId = selectedItem.id;\n      if (this.mode !== 'view') {\n        this.selectedModelId = null;\n        this.modelForm.patchValue({\n          modelType: selectedItem.id,\n          model: null\n        }, {\n          emitEvent: false\n        });\n      }\n      if (this.selectedAiEngineId) {\n        this.fetchModelsForTypeAndEngine(selectedItem.id, this.selectedAiEngineId);\n      } else {\n        this.modelNames = [];\n      }\n    }\n    // Handle Model selection\n    onModelSelected(selectedItem) {\n      this.selectedModelId = selectedItem.id;\n      this.modelForm.patchValue({\n        model: selectedItem.id\n      }, {\n        emitEvent: false\n      });\n    }\n    // Fetch models using the API refdata endpoint for the selected engine\n    fetchModelsForTypeAndEngine(modelType, aiEngine) {\n      // Map engine to ref_key\n      const engineRefKeyMap = {\n        AzureOpenAI: 'AzureOpenAI Model',\n        AmazonBedrock: 'AmazonBedrock Model',\n        GoogleAI: 'GoogleAI Model',\n        DaOpenSourceAI: 'DaOpenSourceAI Model',\n        BNY: 'BNY Model',\n        DatabricksAI: 'DatabricksAI Model'\n      };\n      const refKey = engineRefKeyMap[aiEngine];\n      if (!refKey) {\n        this.modelNames = [];\n        return;\n      }\n      this.modelService.getDropdownOptions(refKey, false, true).subscribe({\n        next: modelGroups => {\n          const group = modelGroups.find(g => g.type === modelType);\n          this.modelNames = group && group.models ? group.models.map(m => ({\n            id: m.id,\n            title: m.name\n          })) : [];\n          this.cdr.detectChanges();\n        },\n        error: error => {\n          this.modelNames = [];\n          console.error('Error fetching models:', error);\n        }\n      });\n    }\n    // Get error message for a specific form field\n    getFieldError(fieldName) {\n      const field = this.modelForm.get(fieldName);\n      const customLabels = {\n        modelDeploymentName: 'Model Name',\n        modelDescription: 'Description',\n        baseurl: 'Base URL',\n        llmDeploymentName: 'LLM Deployment Name',\n        apiKey: 'API Key Encoded',\n        apiVersion: 'API Version',\n        awsAccessKey: 'AWS Access Key',\n        awsSecretKey: 'AWS Secret Key',\n        awsRegion: 'AWS Region',\n        bedrockModelId: 'Bedrock Model Id',\n        gcpProjectId: 'GCP Project Id',\n        gcpLocation: 'GCP Location',\n        vertexAIEndpoint: 'VertexAI Endpoint',\n        serviceUrl: 'Service URL',\n        apiKeyEncoded: 'API Key Encoded',\n        headerName: 'Header Name',\n        modelType: 'Model Type'\n      };\n      const formattedFieldName = customLabels[fieldName] || fieldName;\n      if (field && field.invalid && (field.touched || field.dirty)) {\n        if (field.errors?.['required']) {\n          return `${formattedFieldName} is required`;\n        }\n        if (field.errors?.['minlength']) {\n          return `${formattedFieldName} must be at least ${field.errors['minlength'].requiredLength} characters long`;\n        }\n        if (field.errors?.['invalidUrl']) {\n          return `Please enter a valid URL`;\n        }\n        if (field.errors?.['containsSpaces']) {\n          return `${formattedFieldName} cannot contain spaces`;\n        }\n        if (field.errors?.['leadingSpace']) {\n          return `${formattedFieldName} cannot contain spaces`;\n        }\n      }\n      return '';\n    }\n    // Update form control when model type changes\n    onModelTypeChange(event) {\n      const selectedType = event.selectedValue;\n      if (selectedType) {\n        const selectedOption = this.modelConfigurationOptions.find(option => option.name === selectedType);\n        const typeValue = selectedOption ? selectedOption.value : selectedType;\n        this.modelForm.patchValue({\n          modelType: typeValue\n        }, {\n          emitEvent: false\n        });\n      }\n    }\n    // Show or hide engine-specific input fields and reset values\n    displayInputField(data) {\n      // Reset visibility first\n      Object.keys(this.inputVisibility).forEach(key => {\n        this.inputVisibility[key] = false;\n      });\n      // Show only the selected engine's fields\n      if (this.inputVisibility.hasOwnProperty(data)) {\n        this.inputVisibility[data] = true;\n      }\n      // Reset form fields specific to other engines to avoid cross-engine data leakage\n      const engineFieldsMap = {\n        AzureOpenAI: ['baseurl', 'llmDeploymentName', 'apiKey', 'apiVersion'],\n        AmazonBedrock: ['awsAccessKey', 'awsSecretKey', 'awsRegion', 'bedrockModelId'],\n        GoogleAI: ['gcpProjectId', 'gcpLocation', 'vertexAIEndpoint'],\n        DaOpenSourceAI: ['serviceUrl', 'apiKeyEncoded', 'headerName'],\n        BNY: [] // Add fields if needed\n      };\n      // Clear all engine-specific fields\n      Object.values(engineFieldsMap).flat().forEach(field => {\n        this.modelForm.get(field)?.reset();\n      });\n      // Mark required fields as untouched (optional, for cleaner UX)\n      this.modelForm.markAsUntouched();\n    }\n    // Update the model name dropdown based on selected AI engine\n    updateModelNameOptions(engine) {\n      // No longer needed with new API response, keep empty or remove\n      this.modelNames = [];\n    }\n    iconColor = '#28a745';\n    // Save model if form is valid and prepare API payload\n    onSave() {\n      if (this.modelForm.valid) {\n        const formValues = this.modelForm.value;\n        const payload = Object.keys(formValues).reduce((acc, key) => {\n          const value = formValues[key];\n          if (value !== null && value !== undefined && value !== '') {\n            acc[key] = value;\n          }\n          return acc;\n        }, {});\n        if (!(this.selectedAiEngineId === 'GoogleAI' && this.selectedModelTypeId === 'Embedding')) {\n          delete payload.vertexAIEndpoint;\n        }\n        if (this.isEditMode && this.modelId) {} else {\n          this.modelService.saveModel(payload).subscribe({\n            next: info => {\n              this.iconName = \"circle-check\";\n              this.popupMessage = info?.info?.message || info.message;\n              this.showSuccessPopup = true;\n              this.submissionSuccess = true;\n              this.iconColor = \"#28a745\";\n            },\n            error: error => {\n              this.iconName = \"info\";\n              this.popupMessage = error?.error?.message || error.message;\n              this.showSuccessPopup = true;\n              this.submissionSuccess = false;\n              this.iconColor = \"#dc3545\";\n            }\n          });\n        }\n      } else {\n        this.markFormGroupTouched();\n      }\n    }\n    // Mark all form fields as touched to trigger validation\n    markFormGroupTouched() {\n      Object.keys(this.modelForm.controls).forEach(key => {\n        const control = this.modelForm.get(key);\n        if (control) {\n          control.markAsTouched();\n        }\n      });\n    }\n    // Reset form and navigate on cancel\n    onCancel() {\n      if (this.mode === 'add') {\n        this.modelForm.reset({\n          aiEngine: '',\n          modelType: 'Generative'\n        });\n        Object.keys(this.inputVisibility).forEach(key => {\n          this.inputVisibility[key] = false;\n        });\n      }\n      this.router.navigate(['/libraries/models']);\n    }\n    // Return specific form control by name\n    getControl(name) {\n      return this.modelForm.get(name);\n    }\n    // Getter for all form controls\n    get formControls() {\n      return this.modelForm.controls;\n    }\n    // Getter for AI engine control\n    get aiEngine() {\n      return this.formControls['aiEngine'];\n    }\n    // Check if any input section should be shown based on engine\n    get shouldDisplayInput() {\n      return this.aiEngine.value === 'AzureOpenAI' || this.aiEngine.value === 'AmazonBedrock' || this.aiEngine.value === 'GoogleAI' || this.aiEngine.value === 'DaOpenSourceAI' || this.aiEngine.value === 'BNY' || this.mode === 'view';\n    }\n    // Handle success popup confirmation\n    onSuccessConfirm() {\n      this.closeSuccessPopup();\n    }\n    // Hide success popup and navigate if successful\n    closeSuccessPopup() {\n      this.showSuccessPopup = false;\n      this.popupTitle = '';\n      this.popupMessage = '';\n      if (this.submissionSuccess) {\n        this.router.navigate(['/libraries/models']);\n      }\n    }\n    // Set validators dynamically for selected engine fields\n    updateEngineFieldValidators(selectedEngine) {\n      const engineFieldMap = {\n        AzureOpenAI: ['baseurl', 'llmDeploymentName', 'apiKey', 'apiVersion'],\n        AmazonBedrock: ['awsAccessKey', 'awsSecretKey', 'awsRegion', 'bedrockModelId'],\n        GoogleAI: ['gcpProjectId', 'gcpLocation', 'vertexAIEndpoint'],\n        DaOpenSourceAI: ['serviceUrl', 'apiKeyEncoded', 'headerName'],\n        BNY: []\n      };\n      const fieldsToUpdate = engineFieldMap[selectedEngine] || [];\n      const allEngineFields = Object.values(engineFieldMap).flat();\n      // Clear validators for all fields\n      allEngineFields.forEach(field => {\n        const control = this.modelForm.get(field);\n        if (control) {\n          control.clearValidators();\n          control.updateValueAndValidity();\n        }\n      });\n      // Apply only required validators to selected engine fields\n      fieldsToUpdate.forEach(field => {\n        const control = this.modelForm.get(field);\n        if (control) {\n          if (field === 'baseurl' || field === 'serviceUrl') {\n            control.setValidators([Validators.required, this.noSpacesValidator, this.urlValidator]);\n            control.updateValueAndValidity();\n          } else {\n            control.setValidators([Validators.required, this.noSpacesValidator]);\n            control.updateValueAndValidity();\n          }\n        }\n      });\n    }\n    static ɵfac = function CreateModelsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CreateModelsComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.HttpClient), i0.ɵɵdirectiveInject(i4.ModelService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CreateModelsComponent,\n      selectors: [[\"app-create-models\"]],\n      decls: 39,\n      vars: 42,\n      consts: [[1, \"page-title\"], [1, \"create-models-container\"], [3, \"formGroup\"], [1, \"form-layout\"], [1, \"left-column\"], [1, \"left-header\"], [\"class\", \"left-title\", 4, \"ngIf\"], [\"name\", \"panel-left\", 1, \"collapse-icon\", 3, \"click\"], [\"class\", \"card-content\", 4, \"ngIf\"], [1, \"right-column\"], [1, \"right-column-content\"], [1, \"solid-card\"], [1, \"card-content\"], [1, \"header-with-save\"], [1, \"button-container\"], [\"label\", \"Cancel\", \"variant\", \"secondary\", 3, \"userClick\", 4, \"ngIf\"], [\"label\", \"Save\", \"variant\", \"primary\", 3, \"disabled\", \"customStyles\", \"userClick\", 4, \"ngIf\"], [\"label\", \"Back\", \"variant\", \"secondary\", 3, \"userClick\", 4, \"ngIf\"], [1, \"lists-container\"], [1, \"engine-selection\"], [1, \"list-container\", 3, \"ngClass\"], [3, \"onOptionSelected\", \"title\", \"items\", \"selectedItemId\"], [1, \"model-type-selection\"], [1, \"model-selection\"], [3, \"onOptionSelected\", \"emptyLabel\", \"title\", \"items\", \"selectedItemId\"], [\"class\", \"no-model-message\", 4, \"ngIf\"], [1, \"message-wrapper\"], [\"class\", \"configuration-message\", 4, \"ngIf\"], [\"class\", \"parameters-container\", 4, \"ngIf\"], [3, \"confirm\", \"closed\", \"show\", \"title\", \"message\", \"showHeaderIcon\", \"headerIconName\", \"iconColor\", \"showClose\", \"showCancel\", \"confirmButtonVariant\", \"confirmButtonBackground\"], [1, \"left-title\"], [3, \"label\", \"id\", \"placeholder\", \"formControl\", \"error\", \"required\"], [\"formControlName\", \"modelDescription\", 3, \"label\", \"placeholder\", \"formControl\", \"required\", \"error\"], [\"label\", \"Cancel\", \"variant\", \"secondary\", 3, \"userClick\"], [\"label\", \"Save\", \"variant\", \"primary\", 3, \"userClick\", \"disabled\", \"customStyles\"], [\"label\", \"Back\", \"variant\", \"secondary\", 3, \"userClick\"], [1, \"no-model-message\"], [1, \"configuration-message\"], [1, \"parameters-container\"], [1, \"parameter-form\"], [\"class\", \"param-row\", 4, \"ngIf\"], [1, \"param-row\"], [1, \"param-field\"], [3, \"label\", \"id\", \"placeholder\", \"error\", \"type\", \"formControl\", \"required\"], [3, \"label\", \"id\", \"placeholder\", \"error\", \"formControl\", \"required\"], [1, \"version-top\", 3, \"disabled\", \"dropdownTitle\", \"options\", \"formControl\", \"error\", \"required\", \"label\", \"selectedValue\"], [3, \"label\", \"id\", \"placeholder\", \"type\", \"formControl\", \"error\", \"required\"], [3, \"label\", \"id\", \"placeholder\", \"type\", \"formControl\", \"required\", \"error\"], [\"class\", \"param-field\", 4, \"ngIf\"]],\n      template: function CreateModelsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p\", 0);\n          i0.ɵɵtext(1, \"Model Configuration\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"div\", 1)(3, \"form\", 2)(4, \"div\", 3)(5, \"div\", 4)(6, \"div\", 5);\n          i0.ɵɵtemplate(7, CreateModelsComponent_span_7_Template, 2, 0, \"span\", 6);\n          i0.ɵɵelementStart(8, \"lucide-icon\", 7);\n          i0.ɵɵlistener(\"click\", function CreateModelsComponent_Template_lucide_icon_click_8_listener() {\n            return ctx.toggleLeftPanel();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(9, CreateModelsComponent_div_9_Template, 3, 11, \"div\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 9)(11, \"div\", 10)(12, \"div\", 11)(13, \"div\", 12)(14, \"div\", 13)(15, \"h4\");\n          i0.ɵɵtext(16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\", 14);\n          i0.ɵɵtemplate(18, CreateModelsComponent_ava_button_18_Template, 1, 0, \"ava-button\", 15)(19, CreateModelsComponent_ava_button_19_Template, 1, 3, \"ava-button\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(20, CreateModelsComponent_ava_button_20_Template, 1, 0, \"ava-button\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"div\", 18)(22, \"div\", 19)(23, \"div\", 20)(24, \"ava-list\", 21);\n          i0.ɵɵlistener(\"onOptionSelected\", function CreateModelsComponent_Template_ava_list_onOptionSelected_24_listener($event) {\n            return ctx.onAiEngineSelected($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(25, \"div\", 22)(26, \"div\", 20)(27, \"ava-list\", 21);\n          i0.ɵɵlistener(\"onOptionSelected\", function CreateModelsComponent_Template_ava_list_onOptionSelected_27_listener($event) {\n            return ctx.onModelTypeSelected($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(28, \"div\", 23)(29, \"div\", 20)(30, \"ava-list\", 24);\n          i0.ɵɵlistener(\"onOptionSelected\", function CreateModelsComponent_Template_ava_list_onOptionSelected_30_listener($event) {\n            return ctx.onModelSelected($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(31, CreateModelsComponent_div_31_Template, 2, 0, \"div\", 25);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(32, \"hr\");\n          i0.ɵɵelementStart(33, \"h3\");\n          i0.ɵɵtext(34);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"div\", 26);\n          i0.ɵɵtemplate(36, CreateModelsComponent_p_36_Template, 2, 1, \"p\", 27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(37, CreateModelsComponent_div_37_Template, 7, 5, \"div\", 28);\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelementStart(38, \"ava-popup\", 29);\n          i0.ɵɵlistener(\"confirm\", function CreateModelsComponent_Template_ava_popup_confirm_38_listener() {\n            return ctx.onSuccessConfirm();\n          })(\"closed\", function CreateModelsComponent_Template_ava_popup_closed_38_listener() {\n            return ctx.closeSuccessPopup();\n          });\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"formGroup\", ctx.modelForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"collapsed\", ctx.isLeftCollapsed);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLeftCollapsed);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLeftCollapsed);\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate(ctx.labels.chooseEngineAndModel);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isEditMode);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isEditMode);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isEditMode);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(36, _c0, ctx.isEditMode || ctx.mode === \"view\"));\n          i0.ɵɵadvance();\n          i0.ɵɵpropertyInterpolate(\"title\", ctx.labels.chooseAiEngine);\n          i0.ɵɵproperty(\"items\", ctx.aiEngines)(\"selectedItemId\", ctx.selectedAiEngineId);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(38, _c0, ctx.isEditMode || ctx.mode === \"view\"));\n          i0.ɵɵadvance();\n          i0.ɵɵpropertyInterpolate(\"title\", ctx.labels.chooseModelType);\n          i0.ɵɵproperty(\"items\", ctx.modelTypeList)(\"selectedItemId\", ctx.selectedModelTypeId);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(40, _c0, ctx.isEditMode || ctx.mode === \"view\"));\n          i0.ɵɵadvance();\n          i0.ɵɵpropertyInterpolate(\"title\", ctx.labels.chooseModel);\n          i0.ɵɵproperty(\"emptyLabel\", ctx.modelNames.length === 0 && ctx.selectedModelTypeId && ctx.selectedAiEngineId ? \"No models available for the selected type and engine.\" : ctx.labels.choosePreferredAIEngine)(\"items\", ctx.modelNames)(\"selectedItemId\", ctx.selectedModelId);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.modelNames.length === 0 && ctx.selectedModelTypeId && ctx.selectedAiEngineId);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.labels.modelConfiguration);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.areSelectionsComplete());\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.areSelectionsComplete());\n          i0.ɵɵadvance();\n          i0.ɵɵpropertyInterpolate(\"headerIconName\", ctx.iconName);\n          i0.ɵɵproperty(\"show\", ctx.showSuccessPopup)(\"title\", ctx.popupTitle)(\"message\", ctx.popupMessage)(\"showHeaderIcon\", true)(\"iconColor\", ctx.iconColor)(\"showClose\", true)(\"showCancel\", false)(\"confirmButtonVariant\", \"primary\")(\"confirmButtonBackground\", \"#28a745\");\n        }\n      },\n      dependencies: [CommonModule, i5.NgClass, i5.NgIf, ReactiveFormsModule, i1.ɵNgNoValidate, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.FormControlDirective, i1.FormGroupDirective, i1.FormControlName, LucideAngularModule, i6.LucideAngularComponent, ListComponent, AvaTextboxComponent, ButtonComponent, AvaTextareaComponent, DropdownComponent, PopupComponent],\n      styles: [\".create-models-container {\\n  width: 100%;\\n  height: 100vh;\\n  min-height: 0;\\n  display: flex;\\n  flex-direction: column;\\n  background-color: transparent;\\n}\\n\\nform {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  min-height: 0;\\n}\\n\\n.form-layout {\\n  display: flex;\\n  flex-direction: row;\\n  gap: 0;\\n  padding: 0;\\n  flex: 1 1 0;\\n  min-height: 0;\\n  height: 100%;\\n  border: 1px solid #e1e4e8;\\n  background: #ffffff;\\n}\\n@media (max-width: 1400px) {\\n  .form-layout {\\n    gap: 16px;\\n    padding: 16px;\\n  }\\n}\\n@media (max-width: 1200px) {\\n  .form-layout {\\n    flex-wrap: wrap;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .form-layout {\\n    gap: 12px;\\n    padding: 12px;\\n    flex-direction: column;\\n  }\\n}\\n\\n.left-column, .right-column {\\n  display: flex;\\n  flex-direction: column;\\n  min-height: 0;\\n  height: 100%;\\n  background: #ffffff;\\n  border-right: 1px solid #e1e4e8;\\n}\\n\\n.left-column {\\n  width: 340px;\\n  min-width: 60px;\\n  max-width: 340px;\\n  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  background: #f8f9fa;\\n}\\n\\n.right-column {\\n  flex: 1 1 0;\\n  min-width: 0;\\n  min-height: 0;\\n  border-right: none;\\n  background: #fff;\\n}\\n\\n.right-column-content {\\n  flex: 1 1 0;\\n  min-height: 0;\\n  display: flex;\\n  flex-direction: column;\\n  overflow-y: auto;\\n  scrollbar-width: none;\\n  -ms-overflow-style: none;\\n}\\n\\n.right-column-content::-webkit-scrollbar {\\n  display: none;\\n}\\n\\n.solid-card {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  border: none !important;\\n}\\n\\n.card-content {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n  padding: 16px;\\n}\\n@media (max-width: 576px) {\\n  .card-content {\\n    gap: 12px;\\n    padding: 12px;\\n  }\\n}\\n\\n.right-column .card-content {\\n  flex: 1;\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.section-title, .selection-title {\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin: 0 0 8px;\\n  color: var(--text-color);\\n}\\n@media (max-width: 576px) {\\n  .section-title, .selection-title {\\n    font-size: 14px;\\n    margin-bottom: 6px;\\n  }\\n}\\n\\n.selection-description {\\n  font-size: 14px;\\n  color: var(--text-secondary);\\n  margin-bottom: 8px;\\n}\\n\\n.model-selection-container {\\n  display: flex;\\n  flex-direction: row;\\n  gap: 24px;\\n  margin-bottom: 24px;\\n}\\n@media (max-width: 992px) {\\n  .model-selection-container {\\n    flex-direction: column;\\n    gap: 16px;\\n  }\\n}\\n\\n.engine-selection,\\n.model-selection {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.dropdown-container {\\n  margin-top: 8px;\\n}\\n\\n.dropdown-select {\\n  width: 100%;\\n  padding: 12px 16px;\\n  border: 1px solid var(--form-input-border);\\n  border-radius: 8px;\\n  background-color: var(--form-input-bg);\\n  font-size: 14px;\\n  color: var(--form-input-color);\\n  appearance: none;\\n  background-image: url(\\\"data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e\\\");\\n  background-repeat: no-repeat;\\n  background-position: right 16px center;\\n  background-size: 16px;\\n}\\n.dropdown-select:focus {\\n  outline: none;\\n  border-color: var(--form-input-focus-border);\\n}\\n\\n.parameters-container {\\n  margin-top: 16px;\\n}\\n\\n.parameter-form {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 24px;\\n  margin-top: 16px;\\n}\\n\\n.param-row {\\n  display: grid;\\n  grid-template-columns: repeat(2, 1fr);\\n  gap: 10px;\\n}\\n\\n.param-field {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 10px;\\n  width: 100%;\\n}\\n\\n.param-label {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: var(--form-label-color);\\n}\\n\\n.param-input {\\n  padding: 10px 12px;\\n  border: 1px solid var(--form-input-border);\\n  border-radius: 6px;\\n  font-size: 14px;\\n  background-color: var(--form-input-bg);\\n  color: var(--form-input-color);\\n}\\n.param-input:focus {\\n  outline: none;\\n  border-color: var(--form-input-focus-border);\\n}\\n\\n.dropdown, .dropdown-selected, .dropdown-icon, .dropdown-arrow {\\n  display: none;\\n}\\n\\n.dropdown-medium {\\n  width: 250px;\\n}\\n\\n.version-top {\\n  margin-top: 8px;\\n}\\n\\n.ava-list-disabled .ava-list-item {\\n  pointer-events: none;\\n}\\n\\n.radio-group {\\n  display: flex;\\n  gap: 20px;\\n  margin-top: 10px;\\n}\\n\\n.radio-option {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  cursor: pointer;\\n}\\n\\n.radio-label {\\n  font-size: 14px;\\n  margin-left: 4px;\\n  color: var(--form-label-color);\\n}\\n\\n.right-column-buttons {\\n  justify-content: flex-end;\\n  gap: 16px;\\n  padding: 33px 13px 4px;\\n  margin-top: auto;\\n  margin-bottom: 0;\\n  flex-shrink: 0;\\n}\\n@media (max-width: 576px) {\\n  .right-column-buttons {\\n    gap: 12px;\\n    padding: 12px 0 0;\\n  }\\n}\\n\\n.exit-button, .save-button {\\n  padding: 10px 24px;\\n  border-radius: 6px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n@media (max-width: 576px) {\\n  .exit-button, .save-button {\\n    padding: 8px 16px;\\n    font-size: 13px;\\n  }\\n}\\n\\n.exit-button {\\n  background-color: transparent;\\n  border: 1px solid var(--button-secondary-border);\\n  color: var(--button-secondary-text);\\n}\\n.exit-button:hover {\\n  background-color: var(--button-secondary-hover-bg);\\n}\\n\\n.save-button {\\n  background: var(--button-gradient);\\n  border: none;\\n  color: var(--button-primary-text);\\n}\\n.save-button:hover {\\n  opacity: var(--button-hover-opacity);\\n}\\n\\n.empty-state {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  height: 140px;\\n  color: var(--text-secondary);\\n  font-size: 14px;\\n  font-style: italic;\\n  text-align: center;\\n  background-color: var(--agent-tools-empty-bg);\\n  border-radius: 6px;\\n  border: 1px dashed var(--agent-tools-empty-border);\\n}\\n\\n/* CSS to make heading and Save button inline */\\n.header-with-save {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 16px;\\n}\\n\\n.header-with-save h3 {\\n  margin: 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: var(--text-color);\\n}\\n\\n/* Remove the old right-column-buttons styles since button is now inline */\\n.right-column-buttons {\\n  display: none; /* Hide the old button container */\\n}\\n\\n/* Responsive adjustments */\\n@media (max-width: 576px) {\\n  .header-with-save {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 12px;\\n  }\\n  .header-with-save h3 {\\n    font-size: 14px;\\n  }\\n}\\n.lists-container {\\n  display: flex;\\n  gap: 16px;\\n}\\n\\n.engine-selection, .model-type-selection, .model-selection {\\n  flex: 1;\\n}\\n\\n.message-wrapper {\\n  display: flex;\\n  justify-content: center; /* Center horizontally */\\n  align-items: center; /* Center vertically */\\n}\\n\\n.page-title {\\n  font-weight: 700;\\n  font-size: 24px;\\n  color: #23272E;\\n  margin-bottom: 0;\\n  padding: 0 0 12px 0;\\n}\\n\\n.left-header {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  height: 48px;\\n  padding: 0 16px;\\n  background: #fff;\\n  border-bottom: 1px solid #e1e4e8;\\n  z-index: 2;\\n  font-weight: 600;\\n  font-size: 16px;\\n  color: #23272E;\\n}\\n\\n.header-with-save {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n}\\n\\n.header-with-save h4 {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #23272E;\\n  margin: 0;\\n}\\n\\n.left-title.active, .header-with-save .active {\\n  color: #215AD6;\\n  border-bottom: 2px solid #215AD6;\\n  padding-bottom: 6px;\\n}\\n\\n.collapse-icon {\\n  width: 24px;\\n  height: 24px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: #fff;\\n  border-radius: 50%;\\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\\n  cursor: pointer;\\n  z-index: 2;\\n  font-size: 18px;\\n  border: 1px solid #e1e4e8;\\n}\\n\\n.left-title {\\n  font-weight: 600;\\n  font-size: 16px;\\n}\\n\\n.left-column.collapsed {\\n  width: 48px;\\n  min-width: 48px;\\n  max-width: 48px;\\n}\\n\\n.left-column.collapsed .card-content {\\n  display: none;\\n}\\n\\n.configuration-message {\\n  margin-top: 10%;\\n}\\n\\n.button-container {\\n  display: flex;\\n  gap: 10px;\\n}\\n\\n.ava-dropdown .dropdown-toggle {\\n  border: 1px solid black !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      encapsulation: 2\n    });\n  }\n  return CreateModelsComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "Validators", "AvaTextboxComponent", "AvaTextareaComponent", "ButtonComponent", "DropdownComponent", "ListComponent", "PopupComponent", "modelText", "LucideAngularModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "labels", "name", "placeholder<PERSON><PERSON><PERSON><PERSON><PERSON>", "getControl", "getFieldError", "description", "placeholderModelDescription", "ɵɵlistener", "CreateModelsComponent_ava_button_18_Template_ava_button_userClick_0_listener", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "onCancel", "CreateModelsComponent_ava_button_19_Template_ava_button_userClick_0_listener", "_r3", "onSave", "modelForm", "valid", "selectedAiEngineId", "selectedModelId", "ɵɵpureFunction0", "_c1", "CreateModelsComponent_ava_button_20_Template_ava_button_userClick_0_listener", "_r4", "ɵɵtextInterpolate1", "pleaseSelectMessage", "baseurl", "placeholderBaseUrl", "llmDeploymentName", "placeholderLlmDeploymentName", "<PERSON><PERSON><PERSON><PERSON>", "placeholder<PERSON><PERSON><PERSON><PERSON>", "ɵɵpropertyInterpolate", "dropdownTitleApiVersion", "apiVersion", "isEditMode", "apiVersionOptions", "tmp_29_0", "get", "value", "awsAccessKey", "placeholderAwsAccessKey", "awsSecretKey", "placeholderAwsSecret<PERSON>ey", "awsRegion", "placeholderAwsRegion", "bedrockModelId", "placeholderBedRockModel", "vertexAIEndpoint", "placeholderVertextAIEndPoint", "ɵɵtemplate", "CreateModelsComponent_div_37_div_4_div_5_Template", "gcpProjectId", "placeholderGcpProjectId", "gcpLocation", "placeholderGcpLocation", "selectedModelTypeId", "serviceUrl", "placeholderServiceUrl", "apiKeyEncoded", "placeholder<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "headerName", "placeholder<PERSON><PERSON><PERSON><PERSON><PERSON>", "CreateModelsComponent_div_37_div_2_Template", "CreateModelsComponent_div_37_div_3_Template", "CreateModelsComponent_div_37_div_4_Template", "CreateModelsComponent_div_37_div_5_Template", "CreateModelsComponent_div_37_div_6_Template", "inputVisibility", "AzureOpenAI", "AmazonBedrock", "GoogleAI", "DaOpenSourceAI", "BNY", "CreateModelsComponent", "fb", "router", "route", "http", "modelService", "cdr", "iconName", "showSuccessPopup", "submissionSuccess", "popupTitle", "popupMessage", "modelId", "aiEngines", "modelDropdownOptions", "amazonDropdownOptions", "googleDropdownOptions", "daDropdownOptions", "bnyDropdownOptions", "modelConfigurationOptions", "modelNames", "modelTypeList", "mode", "dropdownConfigs", "label", "<PERSON><PERSON><PERSON>", "mapToListItem", "includeInactive", "isLeftCollapsed", "toggleLeftPanel", "constructor", "group", "modelDeploymentName", "required", "noSpacesValidator", "modelDescription", "noLeadingSpaceValidator", "modelType", "model", "organization", "domain", "project", "team", "aiEngine", "control", "toString", "includes", "containsSpaces", "urlValidator", "test", "invalidUrl", "length", "startsWith", "leadingSpace", "ngOnInit", "snapshot", "paramMap", "for<PERSON>ach", "getDropdownOptions", "subscribe", "next", "options", "mapped", "map", "o", "id", "title", "detectChanges", "error", "console", "loadModelData", "disable", "getOneModeById", "modelData", "onAiEngineSelected", "setTimeout", "patchValue", "areSelectionsComplete", "selectedItem", "<PERSON><PERSON><PERSON><PERSON>", "emitEvent", "displayInputField", "updateEngineFieldValidators", "fetchModelsForTypeAndEngine", "onModelTypeSelected", "onModelSelected", "engineRefKeyMap", "DatabricksAI", "refKey", "modelGroups", "find", "g", "type", "models", "m", "fieldName", "field", "customLabels", "formattedFieldName", "invalid", "touched", "dirty", "errors", "<PERSON><PERSON><PERSON><PERSON>", "onModelTypeChange", "event", "selectedType", "selected<PERSON><PERSON><PERSON>", "selectedOption", "option", "typeValue", "data", "Object", "keys", "key", "hasOwnProperty", "engineFieldsMap", "values", "flat", "reset", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "updateModelNameOptions", "engine", "iconColor", "formValues", "payload", "reduce", "acc", "undefined", "saveModel", "info", "message", "markFormGroupTouched", "controls", "<PERSON><PERSON><PERSON><PERSON>ched", "navigate", "formControls", "shouldDisplayInput", "onSuccessConfirm", "closeSuccessPopup", "engineFieldMap", "fieldsToUpdate", "allEngineFields", "clearValidators", "updateValueAndValidity", "setValidators", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "Router", "ActivatedRoute", "i3", "HttpClient", "i4", "ModelService", "ChangeDetectorRef", "selectors", "decls", "vars", "consts", "template", "CreateModelsComponent_Template", "rf", "ctx", "CreateModelsComponent_span_7_Template", "CreateModelsComponent_Template_lucide_icon_click_8_listener", "CreateModelsComponent_div_9_Template", "CreateModelsComponent_ava_button_18_Template", "CreateModelsComponent_ava_button_19_Template", "CreateModelsComponent_ava_button_20_Template", "CreateModelsComponent_Template_ava_list_onOptionSelected_24_listener", "$event", "CreateModelsComponent_Template_ava_list_onOptionSelected_27_listener", "CreateModelsComponent_Template_ava_list_onOptionSelected_30_listener", "CreateModelsComponent_div_31_Template", "CreateModelsComponent_p_36_Template", "CreateModelsComponent_div_37_Template", "CreateModelsComponent_Template_ava_popup_confirm_38_listener", "CreateModelsComponent_Template_ava_popup_closed_38_listener", "ɵɵclassProp", "ɵɵtextInterpolate", "chooseEngineAndModel", "ɵɵpureFunction1", "_c0", "chooseAiEngine", "chooseModelType", "chooseModel", "choosePreferredAIEngine", "modelConfiguration", "i5", "Ng<PERSON><PERSON>", "NgIf", "ɵNgNoValidate", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "FormControlDirective", "FormGroupDirective", "FormControlName", "i6", "LucideAngularComponent", "styles", "encapsulation"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\libraries\\models\\create-models\\create-models.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\pages\\libraries\\models\\create-models\\create-models.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  OnInit,\r\n  ViewEncapsulation,\r\n  ChangeDetectorRef,\r\n} from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormBuilder, FormGroup, ReactiveFormsModule, FormControl, Validators, ValidatorFn, AbstractControl, ValidationErrors } from '@angular/forms';\r\nimport { Router, ActivatedRoute } from '@angular/router';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { ModelService } from '@shared/services/model.service';\r\nimport { AvaTextboxComponent, AvaTextareaComponent, ButtonComponent, DropdownComponent, DropdownOption, ListComponent, ListItem, PopupComponent } from '@ava/play-comp-library';\r\nimport modelText from '../constants/models.json';\r\nimport { LucideAngularModule } from 'lucide-angular';\r\n@Component({\r\n  selector: 'app-create-models',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    ReactiveFormsModule,\r\n    LucideAngularModule,\r\n    ListComponent,\r\n    AvaTextboxComponent,\r\n    ButtonComponent,\r\n    AvaTextareaComponent,\r\n    DropdownComponent,\r\n    PopupComponent,\r\n  ],\r\n  templateUrl: './create-models.component.html',\r\n  styleUrls: ['./create-models.component.scss'],\r\n  encapsulation: ViewEncapsulation.None,\r\n})\r\nexport class CreateModelsComponent implements OnInit {\r\n  iconName = 'info';\r\n  showSuccessPopup = false;\r\n  submissionSuccess = false;\r\n  popupTitle: string = '';\r\n  popupMessage: string = ''\r\n  modelId: string | null = null;\r\n  isEditMode: boolean = false;\r\n  modelForm: FormGroup;\r\n  aiEngines: ListItem[] = [];\r\n  modelDropdownOptions: DropdownOption[] = [];\r\n  amazonDropdownOptions: DropdownOption[] = [];\r\n  googleDropdownOptions: DropdownOption[] = [];\r\n  daDropdownOptions: DropdownOption[] = [];\r\n  bnyDropdownOptions: DropdownOption[] = [];\r\n  modelConfigurationOptions: DropdownOption[] = [];\r\n  apiVersionOptions: DropdownOption[] = [];\r\n  modelNames: ListItem[] = [];\r\n  selectedAiEngineId: string | null = null;\r\n  selectedModelId: string | null = null;\r\n  modelTypeList: ListItem[] = [];\r\n  selectedModelTypeId: string | null = null;\r\n\r\n  public inputVisibility = {\r\n    AzureOpenAI: false,\r\n    AmazonBedrock: false,\r\n    GoogleAI: false,\r\n    DaOpenSourceAI: false,\r\n    BNY: false,\r\n  };\r\n  public mode: 'view' | 'add' = 'add';\r\n  public labels: any = modelText.labels;\r\n  dropdownConfigs: {\r\n    label: string;\r\n    targetKey: keyof CreateModelsComponent;\r\n    mapToListItem?: boolean;\r\n    includeInactive?: boolean;\r\n  }[] = [\r\n      { label: 'AI Engine', targetKey: 'aiEngines', mapToListItem: true },\r\n      { label: 'AzureOpenAI Model', targetKey: 'modelDropdownOptions' },\r\n      { label: 'AmazonBedrock Model', targetKey: 'amazonDropdownOptions' },\r\n      { label: 'GoogleAI Model', targetKey: 'googleDropdownOptions' },\r\n      { label: 'DaOpenSourceAI Model', targetKey: 'daDropdownOptions' },\r\n      { label: 'BNY Model', targetKey: 'bnyDropdownOptions' },\r\n\r\n      { label: 'Model Type', targetKey: 'modelTypeList', mapToListItem: true },\r\n      { label: 'Api Version', targetKey: 'apiVersionOptions', includeInactive: true },\r\n    ];\r\n  isLeftCollapsed = false;\r\n\r\n  toggleLeftPanel() {\r\n    this.isLeftCollapsed = !this.isLeftCollapsed;\r\n  }\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private http: HttpClient,\r\n    private modelService: ModelService,\r\n    private cdr: ChangeDetectorRef,\r\n  ) {\r\n    this.modelForm = this.fb.group({\r\n      modelDeploymentName: ['', [Validators.required, this.noSpacesValidator]],\r\n      modelDescription: ['', [Validators.required, this.noLeadingSpaceValidator]],\r\n      description: ['', this.noSpacesValidator],\r\n      modelType: ['', Validators.required],\r\n      model: [''],\r\n      organization: ['', this.noSpacesValidator],\r\n      domain: ['', this.noSpacesValidator],\r\n      project: ['', this.noSpacesValidator],\r\n      team: ['', this.noSpacesValidator],\r\n      baseurl: ['', [Validators.required, this.noSpacesValidator]],\r\n      llmDeploymentName: ['', this.noSpacesValidator],\r\n      apiKey: ['', this.noSpacesValidator],\r\n      apiVersion: ['', this.noSpacesValidator],\r\n      awsAccessKey: ['', this.noSpacesValidator],\r\n      awsSecretKey: ['', this.noSpacesValidator],\r\n      awsRegion: ['', this.noSpacesValidator],\r\n      bedrockModelId: ['', this.noSpacesValidator],\r\n      gcpProjectId: ['', this.noSpacesValidator],\r\n      gcpLocation: ['', this.noSpacesValidator],\r\n      vertexAIEndpoint: ['', this.noSpacesValidator],\r\n      serviceUrl: ['', this.noSpacesValidator],\r\n      apiKeyEncoded: ['', this.noSpacesValidator],\r\n      headerName: ['', this.noSpacesValidator],\r\n      aiEngine: ['', Validators.required],\r\n    });\r\n\r\n  }\r\n  noSpacesValidator = (control: AbstractControl): ValidationErrors | null => {\r\n    if (control.value && control.value.toString().includes(' ')) {\r\n      return { containsSpaces: true };\r\n    }\r\n    return null;\r\n  };\r\n  urlValidator(control: AbstractControl): ValidationErrors | null {\r\n    if (control.value && !/^(https?|ftp):\\/\\/[^\\s/$.?#].[^\\s]*$/.test(control.value)) {\r\n      return { invalidUrl: true };\r\n    }\r\n    return null;\r\n  }\r\n  noLeadingSpaceValidator(control: AbstractControl): ValidationErrors | null {\r\n    const value = control.value;\r\n\r\n    if (typeof value === 'string' && value.length > 0 && value.startsWith(' ')) {\r\n      return { leadingSpace: true };\r\n    }\r\n    return null;\r\n  }\r\n  // Lifecycle hook to initialize form and fetch dropdown values\r\n  ngOnInit(): void {\r\n    this.modelId = this.route.snapshot.paramMap.get('id');\r\n    this.isEditMode = !!this.modelId;\r\n    this.dropdownConfigs.forEach(({ label, targetKey, mapToListItem, includeInactive }) => {\r\n      this.modelService.getDropdownOptions(label, includeInactive).subscribe({\r\n        next: (options) => {\r\n          const mapped = mapToListItem\r\n            ? options.map((o) => ({ id: o.value, title: o.label }))\r\n            : options.map((o) => ({ name: o.label, value: o.value }));\r\n          (this[targetKey] as any) = mapped;\r\n          this.cdr.detectChanges();\r\n        },\r\n        error: (error) => console.error(`Error fetching ${label}:`, error),\r\n      });\r\n    });\r\n    if (this.isEditMode && this.modelId) {\r\n      this.loadModelData(this.modelId);\r\n      this.modelForm.disable();\r\n    }\r\n  }\r\n  // Load model data by ID and patch form values\r\n  loadModelData(modelId: string): void {\r\n    this.mode = 'view';\r\n    this.modelService.getOneModeById(modelId).subscribe({\r\n      next: (modelData: any) => {\r\n        this.selectedAiEngineId = modelData.aiEngine;\r\n        this.selectedModelId = modelData.model;\r\n        this.selectedModelTypeId = modelData.modelType;\r\n        this.onAiEngineSelected({ id: modelData.aiEngine, title: '' });\r\n        setTimeout(() => {\r\n          this.modelForm.patchValue({\r\n            modelDeploymentName: modelData.modelDeploymentName,\r\n            description: modelData.modelDescription || modelData.description,\r\n            modelType: modelData.modelType,\r\n            modelDescription: modelData.modelDescription,\r\n            aiEngine: modelData.aiEngine,\r\n            model: modelData.model,\r\n            baseurl: modelData.baseurl,\r\n            llmDeploymentName: modelData.llmDeploymentName,\r\n            apiKey: modelData.apiKey,\r\n            apiVersion: modelData.apiVersion,\r\n            awsAccessKey: modelData.awsAccessKey,\r\n            awsSecretKey: modelData.awsSecretKey,\r\n            awsRegion: modelData.awsRegion,\r\n            bedrockModelId: modelData.bedrockModelId,\r\n            gcpProjectId: modelData.gcpProjectId,\r\n            gcpLocation: modelData.gcpLocation,\r\n            vertexAIEndpoint: modelData.vertexAIEndpoint,\r\n            serviceUrl: modelData.serviceUrl,\r\n            apiKeyEncoded: modelData.apiKeyEncoded,\r\n            headerName: modelData.headerName,\r\n            organization: modelData.organization,\r\n            domain: modelData.domain,\r\n            project: modelData.project,\r\n            team: modelData.team,\r\n          });\r\n          this.cdr.detectChanges();\r\n          if (this.mode === 'view') {\r\n            this.modelForm.disable();\r\n          }\r\n        }, 100);\r\n      },\r\n      error: (error: any) => {\r\n        console.error('Error loading model data:', error);\r\n      },\r\n    });\r\n  }\r\n  areSelectionsComplete(): boolean {\r\n    return !!this.selectedAiEngineId && !!this.selectedModelTypeId && !!this.selectedModelId;\r\n  }\r\n  // Handle AI engine selection and update related fields and dropdowns\r\n  onAiEngineSelected(selectedItem: ListItem): void {\r\n    this.selectedAiEngineId = selectedItem.id;\r\n    const selectedEngine = selectedItem.id;\r\n    if (this.mode !== 'view') {\r\n      this.selectedModelId = null;\r\n      this.modelForm.patchValue({\r\n        aiEngine: selectedEngine,\r\n        model: null\r\n      }, { emitEvent: false });\r\n    }\r\n    if (selectedEngine) {\r\n      this.displayInputField(selectedEngine);\r\n      this.updateEngineFieldValidators(selectedEngine);\r\n      if (this.selectedModelTypeId) {\r\n        this.fetchModelsForTypeAndEngine(this.selectedModelTypeId, selectedEngine);\r\n      } else {\r\n        this.modelNames = [];\r\n      }\r\n    }\r\n  }\r\n  // Handle Model Type selection\r\n  onModelTypeSelected(selectedItem: ListItem): void {\r\n    this.selectedModelTypeId = selectedItem.id;\r\n    if (this.mode !== 'view') {\r\n      this.selectedModelId = null;\r\n      this.modelForm.patchValue({\r\n        modelType: selectedItem.id,\r\n        model: null\r\n      }, { emitEvent: false });\r\n    }\r\n    if (this.selectedAiEngineId) {\r\n      this.fetchModelsForTypeAndEngine(selectedItem.id, this.selectedAiEngineId);\r\n    } else {\r\n      this.modelNames = [];\r\n    }\r\n  }\r\n  // Handle Model selection\r\n  onModelSelected(selectedItem: ListItem): void {\r\n    this.selectedModelId = selectedItem.id;\r\n    this.modelForm.patchValue({ model: selectedItem.id }, { emitEvent: false });\r\n  }\r\n  // Fetch models using the API refdata endpoint for the selected engine\r\n  fetchModelsForTypeAndEngine(modelType: string, aiEngine: string): void {\r\n    // Map engine to ref_key\r\n    const engineRefKeyMap: { [key: string]: string } = {\r\n      AzureOpenAI: 'AzureOpenAI Model',\r\n      AmazonBedrock: 'AmazonBedrock Model',\r\n      GoogleAI: 'GoogleAI Model',\r\n      DaOpenSourceAI: 'DaOpenSourceAI Model',\r\n      BNY: 'BNY Model',\r\n      DatabricksAI: 'DatabricksAI Model',\r\n    };\r\n    const refKey = engineRefKeyMap[aiEngine];\r\n    if (!refKey) {\r\n      this.modelNames = [];\r\n      return;\r\n    }\r\n    this.modelService.getDropdownOptions(refKey, false, true).subscribe({\r\n      next: (modelGroups: any[]) => {\r\n        const group = modelGroups.find(g => g.type === modelType);\r\n        this.modelNames = group && group.models\r\n          ? group.models.map((m: any) => ({ id: m.id, title: m.name }))\r\n          : [];\r\n        this.cdr.detectChanges();\r\n      },\r\n      error: (error) => {\r\n        this.modelNames = [];\r\n        console.error('Error fetching models:', error);\r\n      }\r\n    });\r\n  }\r\n  // Get error message for a specific form field\r\n  getFieldError(fieldName: string): string {\r\n    const field = this.modelForm.get(fieldName);\r\n  \r\n    const customLabels: Record<string, string> = {\r\n      modelDeploymentName: 'Model Name',\r\n      modelDescription: 'Description',\r\n      baseurl: 'Base URL',\r\n      llmDeploymentName: 'LLM Deployment Name',\r\n      apiKey: 'API Key Encoded',\r\n      apiVersion: 'API Version',\r\n      awsAccessKey: 'AWS Access Key',\r\n      awsSecretKey: 'AWS Secret Key',\r\n      awsRegion: 'AWS Region',\r\n      bedrockModelId: 'Bedrock Model Id',\r\n      gcpProjectId: 'GCP Project Id',\r\n      gcpLocation: 'GCP Location',\r\n      vertexAIEndpoint: 'VertexAI Endpoint',\r\n      serviceUrl: 'Service URL',\r\n      apiKeyEncoded: 'API Key Encoded',\r\n      headerName: 'Header Name',\r\n      modelType: 'Model Type'\r\n    };\r\n    const formattedFieldName = customLabels[fieldName] || fieldName;\r\n    if (field && field.invalid && (field.touched || field.dirty)) {\r\n      if (field.errors?.['required']) {\r\n        return `${formattedFieldName} is required`;\r\n      }\r\n      if (field.errors?.['minlength']) {\r\n        return `${formattedFieldName} must be at least ${field.errors['minlength'].requiredLength} characters long`;\r\n      }\r\n      if (field.errors?.['invalidUrl']) {\r\n        return `Please enter a valid URL`;\r\n      }\r\n      if (field.errors?.['containsSpaces']) {\r\n        return `${formattedFieldName} cannot contain spaces`;\r\n      }\r\n      if (field.errors?.['leadingSpace']) {\r\n        return `${formattedFieldName} cannot contain spaces`;\r\n      }\r\n    }\r\n    return '';\r\n  }\r\n  \r\n  // Update form control when model type changes\r\n  onModelTypeChange(event: any) {\r\n    const selectedType = event.selectedValue;\r\n    if (selectedType) {\r\n      const selectedOption = this.modelConfigurationOptions.find(option => option.name === selectedType);\r\n      const typeValue = selectedOption ? selectedOption.value : selectedType;\r\n      this.modelForm.patchValue({\r\n        modelType: typeValue\r\n      }, { emitEvent: false });\r\n    }\r\n  }\r\n  // Show or hide engine-specific input fields and reset values\r\n  displayInputField(data: string) {\r\n    // Reset visibility first\r\n    Object.keys(this.inputVisibility).forEach((key) => {\r\n      this.inputVisibility[key as keyof typeof this.inputVisibility] = false;\r\n    });\r\n    // Show only the selected engine's fields\r\n    if (this.inputVisibility.hasOwnProperty(data)) {\r\n      this.inputVisibility[data as keyof typeof this.inputVisibility] = true;\r\n    }\r\n    // Reset form fields specific to other engines to avoid cross-engine data leakage\r\n    const engineFieldsMap: Record<string, string[]> = {\r\n      AzureOpenAI: ['baseurl', 'llmDeploymentName', 'apiKey', 'apiVersion'],\r\n      AmazonBedrock: ['awsAccessKey', 'awsSecretKey', 'awsRegion', 'bedrockModelId'],\r\n      GoogleAI: ['gcpProjectId', 'gcpLocation', 'vertexAIEndpoint'],\r\n      DaOpenSourceAI: ['serviceUrl', 'apiKeyEncoded', 'headerName'],\r\n      BNY: [] // Add fields if needed\r\n    };\r\n    // Clear all engine-specific fields\r\n    Object.values(engineFieldsMap).flat().forEach(field => {\r\n      this.modelForm.get(field)?.reset();\r\n    });\r\n    // Mark required fields as untouched (optional, for cleaner UX)\r\n    this.modelForm.markAsUntouched();\r\n  }\r\n\r\n  // Update the model name dropdown based on selected AI engine\r\n  updateModelNameOptions(engine: string): void {\r\n    // No longer needed with new API response, keep empty or remove\r\n    this.modelNames = [];\r\n  }\r\n   iconColor: string = '#28a745'\r\n  // Save model if form is valid and prepare API payload\r\n  onSave(): void {\r\n    if (this.modelForm.valid) {\r\n      const formValues = this.modelForm.value;\r\n      const payload = Object.keys(formValues).reduce((acc, key) => {\r\n        const value = formValues[key];\r\n        if (value !== null && value !== undefined && value !== '') {\r\n          acc[key] = value;\r\n        }\r\n        return acc;\r\n      }, {} as any);\r\n      if (!(this.selectedAiEngineId === 'GoogleAI' && this.selectedModelTypeId === 'Embedding')) {\r\n      delete payload.vertexAIEndpoint;\r\n    }\r\n      if (this.isEditMode && this.modelId) {\r\n      } else {\r\n        this.modelService.saveModel(payload).subscribe({\r\n          next: (info) => {\r\n            this.iconName = \"circle-check\";\r\n            this.popupMessage = info?.info?.message || info.message;\r\n            this.showSuccessPopup = true;\r\n            this.submissionSuccess = true;\r\n            this.iconColor = \"#28a745\";\r\n          },\r\n          error: (error) => {\r\n            this.iconName = \"info\";\r\n            this.popupMessage = error?.error?.message || error.message;\r\n            this.showSuccessPopup = true;\r\n            this.submissionSuccess = false;\r\n            this.iconColor = \"#dc3545\";\r\n          },\r\n        });\r\n      }\r\n    } else {\r\n      this.markFormGroupTouched();\r\n    }\r\n  }\r\n  // Mark all form fields as touched to trigger validation\r\n  markFormGroupTouched() {\r\n    Object.keys(this.modelForm.controls).forEach((key) => {\r\n      const control = this.modelForm.get(key);\r\n      if (control) {\r\n        control.markAsTouched();\r\n      }\r\n    });\r\n  }\r\n  // Reset form and navigate on cancel\r\n  onCancel(): void {\r\n    if (this.mode === 'add') {\r\n      this.modelForm.reset({\r\n        aiEngine: '',\r\n        modelType: 'Generative',\r\n      });\r\n      Object.keys(this.inputVisibility).forEach((key) => {\r\n        this.inputVisibility[key as keyof typeof this.inputVisibility] = false;\r\n      });\r\n    }\r\n    this.router.navigate(['/libraries/models']);\r\n  }\r\n  // Return specific form control by name\r\n  getControl(name: string): FormControl {\r\n    return this.modelForm.get(name) as FormControl;\r\n  }\r\n  // Getter for all form controls\r\n  get formControls() {\r\n    return this.modelForm.controls;\r\n  }\r\n  // Getter for AI engine control\r\n  get aiEngine() {\r\n    return this.formControls['aiEngine'];\r\n  }\r\n  // Check if any input section should be shown based on engine\r\n  get shouldDisplayInput(): boolean {\r\n    return (\r\n      this.aiEngine.value === 'AzureOpenAI' ||\r\n      this.aiEngine.value === 'AmazonBedrock' ||\r\n      this.aiEngine.value === 'GoogleAI' ||\r\n      this.aiEngine.value === 'DaOpenSourceAI' ||\r\n      this.aiEngine.value === 'BNY' ||\r\n      this.mode === 'view'\r\n    );\r\n  }\r\n  // Handle success popup confirmation\r\n  onSuccessConfirm(): void {\r\n    this.closeSuccessPopup();\r\n  }\r\n  // Hide success popup and navigate if successful\r\n  closeSuccessPopup(): void {\r\n    this.showSuccessPopup = false;\r\n    this.popupTitle = '';\r\n    this.popupMessage = '';\r\n    if (this.submissionSuccess) {\r\n      this.router.navigate(['/libraries/models']);\r\n    }\r\n  }\r\n  // Set validators dynamically for selected engine fields\r\n  updateEngineFieldValidators(selectedEngine: string): void {\r\n    const engineFieldMap: Record<string, string[]> = {\r\n      AzureOpenAI: ['baseurl', 'llmDeploymentName', 'apiKey', 'apiVersion'],\r\n      AmazonBedrock: ['awsAccessKey', 'awsSecretKey', 'awsRegion', 'bedrockModelId'],\r\n      GoogleAI: ['gcpProjectId', 'gcpLocation', 'vertexAIEndpoint'],\r\n      DaOpenSourceAI: ['serviceUrl', 'apiKeyEncoded', 'headerName'],\r\n      BNY: []\r\n    };\r\n    const fieldsToUpdate = engineFieldMap[selectedEngine] || [];\r\n    const allEngineFields = Object.values(engineFieldMap).flat();\r\n    // Clear validators for all fields\r\n    allEngineFields.forEach(field => {\r\n      const control = this.modelForm.get(field);\r\n      if (control) {\r\n        control.clearValidators();\r\n        control.updateValueAndValidity();\r\n      }\r\n    });\r\n    // Apply only required validators to selected engine fields\r\n    fieldsToUpdate.forEach(field => {\r\n      const control = this.modelForm.get(field);\r\n      if (control) {\r\n        if (field === 'baseurl' || field === 'serviceUrl') {\r\n          control.setValidators([Validators.required, this.noSpacesValidator, this.urlValidator]);\r\n          control.updateValueAndValidity();\r\n        }\r\n        else {\r\n          control.setValidators([Validators.required, this.noSpacesValidator]);\r\n          control.updateValueAndValidity();\r\n        }\r\n      }\r\n    });\r\n  }\r\n}", "<p class=\"page-title\">Model Configuration</p>\r\n<div class=\"create-models-container\">\r\n  <form [formGroup]=\"modelForm\">\r\n    <div class=\"form-layout\">\r\n      <!-- Left Column -->\r\n      <div class=\"left-column\" [class.collapsed]=\"isLeftCollapsed\">\r\n        <div class=\"left-header\">\r\n          <span class=\"left-title\" *ngIf=\"!isLeftCollapsed\">Model Description</span>\r\n          <lucide-icon name=\"panel-left\" class=\"collapse-icon\" (click)=\"toggleLeftPanel()\"></lucide-icon>\r\n        </div>\r\n        <div class=\"card-content\" *ngIf=\"!isLeftCollapsed\">\r\n          <ava-textbox [label]=\"labels.name\" [id]=\"'modelName'\" [placeholder]=\"labels.placeholderModelName\"\r\n            [formControl]=\"getControl('modelDeploymentName')\" [error]=\"getFieldError('modelDeploymentName')\"\r\n            [required]=\"true\">\r\n          </ava-textbox>\r\n          <ava-textarea [label]=\"labels.description\" [placeholder]=\"labels.placeholderModelDescription\"\r\n            [formControl]=\"getControl('modelDescription')\" formControlName=\"modelDescription\" [required]=\"true\"\r\n            [error]=\"getFieldError('modelDescription')\">\r\n          </ava-textarea>\r\n        </div>\r\n      </div>\r\n      <!-- Right Column -->\r\n      <div class=\"right-column\">\r\n        <!-- Content wrapper to match left column height -->\r\n        <div class=\"right-column-content\">\r\n          <div class=\"solid-card\">\r\n            <div class=\"card-content\">\r\n              <div class=\"header-with-save\">\r\n                <h4>{{labels.chooseEngineAndModel}}</h4>\r\n                <div class=\"button-container\">\r\n                  <ava-button\r\n                    *ngIf=\"!isEditMode\"\r\n                    label=\"Cancel\"\r\n                    variant=\"secondary\"\r\n                    (userClick)=\"onCancel()\">\r\n                  </ava-button>\r\n                  <ava-button\r\n                    [disabled]=\"!(modelForm.valid && selectedAiEngineId && selectedModelId)\"\r\n                    *ngIf=\"!isEditMode\"\r\n                    label=\"Save\"\r\n                    variant=\"primary\"\r\n                    [customStyles]=\"{\r\n                      background: 'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n                      '--button-effect-color': '33, 90, 214',\r\n                    }\"\r\n                    (userClick)=\"onSave()\">\r\n                  </ava-button>\r\n                </div>\r\n                <ava-button *ngIf=\"isEditMode\" label=\"Back\" variant=\"secondary\" (userClick)=\"onCancel()\">\r\n                </ava-button>\r\n              </div>\r\n              <div class=\"lists-container\">\r\n                <div class=\"engine-selection\">\r\n \r\n                  <div class=\"list-container\" [ngClass]=\"{ 'ava-list-disabled': isEditMode || mode === 'view' }\">\r\n                    <ava-list title=\"{{labels.chooseAiEngine}}\" [items]=\"aiEngines\"\r\n                      [selectedItemId]=\"selectedAiEngineId\" (onOptionSelected)=\"onAiEngineSelected($event)\">\r\n                    </ava-list>\r\n                  </div>\r\n                </div>\r\n                <div class=\"model-type-selection\">\r\n \r\n                  <div class=\"list-container\" [ngClass]=\"{ 'ava-list-disabled': isEditMode || mode === 'view' }\">\r\n                    <ava-list title=\"{{labels.chooseModelType}}\" [items]=\"modelTypeList\"\r\n                      [selectedItemId]=\"selectedModelTypeId\" (onOptionSelected)=\"onModelTypeSelected($event)\">\r\n                    </ava-list>\r\n                  </div>\r\n                </div>\r\n \r\n                <div class=\"model-selection\">\r\n                  <div class=\"list-container\" [ngClass]=\"{ 'ava-list-disabled': isEditMode || mode === 'view' }\">\r\n                    <ava-list\r\n                      [emptyLabel]=\"(modelNames.length === 0 && selectedModelTypeId && selectedAiEngineId) ? 'No models available for the selected type and engine.' : labels.choosePreferredAIEngine\"\r\n                      title=\"{{labels.chooseModel}}\" [items]=\"modelNames\" [selectedItemId]=\"selectedModelId\"\r\n                      (onOptionSelected)=\"onModelSelected($event)\">\r\n                    </ava-list>\r\n                    <div *ngIf=\"modelNames.length === 0 && selectedModelTypeId && selectedAiEngineId\"\r\n                      class=\"no-model-message\">\r\n                      No models available for the selected type and engine.\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <hr>\r\n              <h3>{{labels.modelConfiguration}}</h3>\r\n              <div class=\"message-wrapper\">\r\n                <p class=\"configuration-message\" *ngIf=\"!areSelectionsComplete()\">\r\n                  {{labels.pleaseSelectMessage}}\r\n                </p>\r\n              </div>\r\n              <div class=\"parameters-container\" *ngIf=\"areSelectionsComplete()\">\r\n                <div class=\"parameter-form\">\r\n                  <!-- Azure OpenAI Fields -->\r\n                  <div *ngIf=\"inputVisibility.AzureOpenAI && selectedModelTypeId\" class=\"param-row\">\r\n                    <div class=\"param-field\">\r\n                      <ava-textbox [label]=\"labels.baseurl\" [id]=\"'baseurl'\" [placeholder]=\"labels.placeholderBaseUrl\"\r\n                        [formControl]=\"getControl('baseurl')\" [error]=\"getFieldError('baseurl')\" [required]=\"true\">\r\n                      </ava-textbox>\r\n                    </div>\r\n                    <div class=\"param-field\">\r\n                      <ava-textbox [label]=\"labels.llmDeploymentName\" [id]=\"'llmDeploymentName'\"\r\n                        [placeholder]=\"labels.placeholderLlmDeploymentName\" [error]=\"getFieldError('llmDeploymentName')\"\r\n                        [type]=\"'text'\" [formControl]=\"getControl('llmDeploymentName')\" [required]=\"true\">\r\n                      </ava-textbox>\r\n                    </div>\r\n                    <div class=\"param-field\">\r\n                      <ava-textbox [label]=\"labels.apiKey\" [id]=\"'apiKey'\" [placeholder]=\"labels.placeholderApiKey\"\r\n                        [error]=\"getFieldError('apiKey')\" [formControl]=\"getControl('apiKey')\" [required]=\"true\">\r\n                      </ava-textbox>\r\n                    </div>\r\n                    <div class=\"param-field\">\r\n                      <ava-dropdown [disabled]=\"isEditMode\" class=\"version-top\" dropdownTitle=\"{{labels.dropdownTitleApiVersion}}\"\r\n                        [disabled]=\"isEditMode\" [options]=\"apiVersionOptions\" [formControl]=\"getControl('apiVersion')\"\r\n                        [error]=\"getFieldError('apiVersion')\" [required]=\"true\" label=\"{{labels.apiVersion}}\"\r\n                        [selectedValue]=\"modelForm.get('apiVersion')?.value\">\r\n                      </ava-dropdown>\r\n                    </div>\r\n                  </div>\r\n                  <!-- Amazon Bedrock Fields -->\r\n                  <div *ngIf=\"inputVisibility.AmazonBedrock && selectedModelTypeId\" class=\"param-row\">\r\n                    <div class=\"param-field\">\r\n                      <ava-textbox [label]=\"labels.awsAccessKey\" [id]=\"'awsAccessKey'\"\r\n                        [placeholder]=\"labels.placeholderAwsAccessKey\" [type]=\"'text'\"\r\n                        [formControl]=\"getControl('awsAccessKey')\" [error]=\"getFieldError('awsAccessKey')\"\r\n                        [required]=\"true\">\r\n                      </ava-textbox>\r\n                    </div>\r\n                    <div class=\"param-field\">\r\n                      <ava-textbox [label]=\"labels.awsSecretKey\" [id]=\"'awsSecretKey'\"\r\n                        [placeholder]=\"labels.placeholderAwsSecretKey\" [formControl]=\"getControl('awsSecretKey')\"\r\n                        [error]=\"getFieldError('awsSecretKey')\" [required]=\"true\">\r\n                      </ava-textbox>\r\n                    </div>\r\n                    <div class=\"param-field\">\r\n                      <ava-textbox [label]=\"labels.awsRegion\" [id]=\"'awsRegion'\"\r\n                        [placeholder]=\"labels.placeholderAwsRegion\" [type]=\"'text'\"\r\n                        [formControl]=\"getControl('awsRegion')\" [error]=\"getFieldError('awsRegion')\" [required]=\"true\">\r\n                      </ava-textbox>\r\n                    </div>\r\n                    <div class=\"param-field\">\r\n                      <ava-textbox [label]=\"labels.bedrockModelId\" [id]=\"'bedrockModelId'\"\r\n                        [placeholder]=\"labels.placeholderBedRockModel\" [type]=\"'text'\"\r\n                        [formControl]=\"getControl('bedrockModelId')\" [error]=\"getFieldError('bedrockModelId')\"\r\n                        [required]=\"true\">\r\n                      </ava-textbox>\r\n                    </div>\r\n                  </div>\r\n                  <!-- Google AI Fields -->\r\n                  <div *ngIf=\"inputVisibility.GoogleAI && selectedModelTypeId\" class=\"param-row\">\r\n                    <div class=\"param-field\">\r\n                      <ava-textbox [label]=\"labels.gcpProjectId\" [id]=\"'gcpProjectId'\"\r\n                        [placeholder]=\"labels.placeholderGcpProjectId\" [type]=\"'text'\"\r\n                        [formControl]=\"getControl('gcpProjectId')\" [error]=\"getFieldError('gcpProjectId')\"\r\n                        [required]=\"true\">\r\n                      </ava-textbox>\r\n                    </div>\r\n                    <div class=\"param-field\">\r\n                      <ava-textbox [label]=\"labels.gcpLocation\" [id]=\"'gcpLocation'\"\r\n                        [placeholder]=\"labels.placeholderGcpLocation\" [type]=\"'text'\"\r\n                        [formControl]=\"getControl('gcpLocation')\" [required]=\"true\"\r\n                        [error]=\"getFieldError('gcpLocation')\">\r\n                      </ava-textbox>\r\n                    </div>\r\n                    <div class=\"param-field\" *ngIf=\"selectedAiEngineId === 'GoogleAI' && selectedModelTypeId === 'Embedding' \">\r\n                      <ava-textbox [label]=\"labels.vertexAIEndpoint\" [id]=\"'vertexAIEndpoint'\"\r\n                        [placeholder]=\"labels.placeholderVertextAIEndPoint\" [type]=\"'url'\"\r\n                        [formControl]=\"getControl('vertexAIEndpoint')\" [error]=\"getFieldError('vertexAIEndpoint')\"\r\n                        [required]=\"true\">\r\n                      </ava-textbox>\r\n                    </div>\r\n                  </div>\r\n                  <!-- DaOpenSource AI Fields -->\r\n                  <div *ngIf=\"inputVisibility.DaOpenSourceAI && selectedModelTypeId\" class=\"param-row\">\r\n                    <div class=\"param-field\">\r\n                      <ava-textbox [label]=\"labels.serviceUrl\" [id]=\"'serviceUrl'\"\r\n                        [placeholder]=\"labels.placeholderServiceUrl\" [type]=\"'url'\"\r\n                        [formControl]=\"getControl('serviceUrl')\" [error]=\"getFieldError('serviceUrl')\"\r\n                        [required]=\"true\">\r\n                      </ava-textbox>\r\n                    </div>\r\n                    <div class=\"param-field\">\r\n                      <ava-textbox [label]=\"labels.apiKeyEncoded\" [id]=\"'apiKeyEncoded'\"\r\n                        [placeholder]=\"labels.placeholderEncodedApiKey\" [formControl]=\"getControl('apiKeyEncoded')\"\r\n                        [error]=\"getFieldError('apiKeyEncoded')\" [required]=\"true\">\r\n                      </ava-textbox>\r\n                    </div>\r\n                    <div class=\"param-field\">\r\n                      <ava-textbox [label]=\"labels.headerName\" [id]=\"'headerName'\"\r\n                        [placeholder]=\"labels.placeholderHeaderName\" [formControl]=\"getControl('headerName')\"\r\n                        [error]=\"getFieldError('headerName')\" [required]=\"true\">\r\n                      </ava-textbox>\r\n                    </div>\r\n                  </div>\r\n                  <!-- BNY Fields -->\r\n                  <div *ngIf=\"inputVisibility.BNY && selectedModelTypeId\" class=\"param-row\">\r\n                    <!-- Add BNY specific fields here when needed -->\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </form>\r\n</div>\r\n<!-- Success Popup -->\r\n<ava-popup [show]=\"showSuccessPopup\" [title]=\"popupTitle\" [message]=\"popupMessage\" [showHeaderIcon]=\"true\"\r\n  headerIconName=\"{{iconName}}\" [iconColor]=\"iconColor\"[showClose]=\"true\" [showCancel]=\"false\"\r\n  [confirmButtonVariant]=\"'primary'\" [confirmButtonBackground]=\"'#28a745'\" (confirm)=\"onSuccessConfirm()\"\r\n  (closed)=\"closeSuccessPopup()\">\r\n</ava-popup>\r\n "], "mappings": "AAMA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAiCC,mBAAmB,EAAeC,UAAU,QAAwD,gBAAgB;AAIrJ,SAASC,mBAAmB,EAAEC,oBAAoB,EAAEC,eAAe,EAAEC,iBAAiB,EAAkBC,aAAa,EAAYC,cAAc,QAAQ,wBAAwB;AAC/K,OAAOC,SAAS,MAAM,0BAA0B;AAChD,SAASC,mBAAmB,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;ICN1CC,EAAA,CAAAC,cAAA,eAAkD;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAG5EH,EAAA,CAAAC,cAAA,cAAmD;IAKjDD,EAJA,CAAAI,SAAA,sBAGc,uBAIC;IACjBJ,EAAA,CAAAG,YAAA,EAAM;;;;IARSH,EAAA,CAAAK,SAAA,EAAqB;IAEhCL,EAFW,CAAAM,UAAA,UAAAC,MAAA,CAAAC,MAAA,CAAAC,IAAA,CAAqB,mBAAmB,gBAAAF,MAAA,CAAAC,MAAA,CAAAE,oBAAA,CAA4C,gBAAAH,MAAA,CAAAI,UAAA,wBAC9C,UAAAJ,MAAA,CAAAK,aAAA,wBAA+C,kBAC/E;IAELZ,EAAA,CAAAK,SAAA,EAA4B;IAExCL,EAFY,CAAAM,UAAA,UAAAC,MAAA,CAAAC,MAAA,CAAAK,WAAA,CAA4B,gBAAAN,MAAA,CAAAC,MAAA,CAAAM,2BAAA,CAAmD,gBAAAP,MAAA,CAAAI,UAAA,qBAC7C,kBAAqD,UAAAJ,MAAA,CAAAK,aAAA,qBACxD;;;;;;IAarCZ,EAAA,CAAAC,cAAA,qBAI2B;IAAzBD,EAAA,CAAAe,UAAA,uBAAAC,6EAAA;MAAAhB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAX,MAAA,GAAAP,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAab,MAAA,CAAAc,QAAA,EAAU;IAAA,EAAC;IAC1BrB,EAAA,CAAAG,YAAA,EAAa;;;;;;IACbH,EAAA,CAAAC,cAAA,qBASyB;IAAvBD,EAAA,CAAAe,UAAA,uBAAAO,6EAAA;MAAAtB,EAAA,CAAAiB,aAAA,CAAAM,GAAA;MAAA,MAAAhB,MAAA,GAAAP,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAab,MAAA,CAAAiB,MAAA,EAAQ;IAAA,EAAC;IACxBxB,EAAA,CAAAG,YAAA,EAAa;;;;IALXH,EAJA,CAAAM,UAAA,eAAAC,MAAA,CAAAkB,SAAA,CAAAC,KAAA,IAAAnB,MAAA,CAAAoB,kBAAA,IAAApB,MAAA,CAAAqB,eAAA,EAAwE,iBAAA5B,EAAA,CAAA6B,eAAA,IAAAC,GAAA,EAOtE;;;;;;IAIN9B,EAAA,CAAAC,cAAA,qBAAyF;IAAzBD,EAAA,CAAAe,UAAA,uBAAAgB,6EAAA;MAAA/B,EAAA,CAAAiB,aAAA,CAAAe,GAAA;MAAA,MAAAzB,MAAA,GAAAP,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAab,MAAA,CAAAc,QAAA,EAAU;IAAA,EAAC;IACxFrB,EAAA,CAAAG,YAAA,EAAa;;;;;IA2BTH,EAAA,CAAAC,cAAA,cAC2B;IACzBD,EAAA,CAAAE,MAAA,8DACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAOVH,EAAA,CAAAC,cAAA,YAAkE;IAChED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAiC,kBAAA,MAAA1B,MAAA,CAAAC,MAAA,CAAA0B,mBAAA,MACF;;;;;IAMIlC,EADF,CAAAC,cAAA,cAAkF,cACvD;IACvBD,EAAA,CAAAI,SAAA,sBAEc;IAChBJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAI,SAAA,sBAGc;IAChBJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAI,SAAA,sBAEc;IAChBJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAI,SAAA,uBAIe;IAEnBJ,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IAtBWH,EAAA,CAAAK,SAAA,GAAwB;IACsCL,EAD9D,CAAAM,UAAA,UAAAC,MAAA,CAAAC,MAAA,CAAA2B,OAAA,CAAwB,iBAAiB,gBAAA5B,MAAA,CAAAC,MAAA,CAAA4B,kBAAA,CAA0C,gBAAA7B,MAAA,CAAAI,UAAA,YACzD,UAAAJ,MAAA,CAAAK,aAAA,YAAmC,kBAAkB;IAI/EZ,EAAA,CAAAK,SAAA,GAAkC;IAEmBL,EAFrD,CAAAM,UAAA,UAAAC,MAAA,CAAAC,MAAA,CAAA6B,iBAAA,CAAkC,2BAA2B,gBAAA9B,MAAA,CAAAC,MAAA,CAAA8B,4BAAA,CACrB,UAAA/B,MAAA,CAAAK,aAAA,sBAA6C,gBACjF,gBAAAL,MAAA,CAAAI,UAAA,sBAAgD,kBAAkB;IAItEX,EAAA,CAAAK,SAAA,GAAuB;IACqCL,EAD5D,CAAAM,UAAA,UAAAC,MAAA,CAAAC,MAAA,CAAA+B,MAAA,CAAuB,gBAAgB,gBAAAhC,MAAA,CAAAC,MAAA,CAAAgC,iBAAA,CAAyC,UAAAjC,MAAA,CAAAK,aAAA,WAC1D,gBAAAL,MAAA,CAAAI,UAAA,WAAqC,kBAAkB;IAIhCX,EAAA,CAAAK,SAAA,GAAkD;IAAlDL,EAAA,CAAAyC,qBAAA,kBAAAlC,MAAA,CAAAC,MAAA,CAAAkC,uBAAA,CAAkD;IAElD1C,EAAA,CAAAyC,qBAAA,UAAAlC,MAAA,CAAAC,MAAA,CAAAmC,UAAA,CAA6B;IACrF3C,EAHY,CAAAM,UAAA,aAAAC,MAAA,CAAAqC,UAAA,CAAuB,aAAArC,MAAA,CAAAqC,UAAA,CACZ,YAAArC,MAAA,CAAAsC,iBAAA,CAA8B,gBAAAtC,MAAA,CAAAI,UAAA,eAAyC,UAAAJ,MAAA,CAAAK,aAAA,eACzD,kBAAkB,mBAAAkC,QAAA,GAAAvC,MAAA,CAAAkB,SAAA,CAAAsB,GAAA,iCAAAD,QAAA,CAAAE,KAAA,CACH;;;;;IAMxDhD,EADF,CAAAC,cAAA,cAAoF,cACzD;IACvBD,EAAA,CAAAI,SAAA,sBAIc;IAChBJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAI,SAAA,sBAGc;IAChBJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAI,SAAA,sBAGc;IAChBJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAI,SAAA,sBAIc;IAElBJ,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAzBWH,EAAA,CAAAK,SAAA,GAA6B;IAGxCL,EAHW,CAAAM,UAAA,UAAAC,MAAA,CAAAC,MAAA,CAAAyC,YAAA,CAA6B,sBAAsB,gBAAA1C,MAAA,CAAAC,MAAA,CAAA0C,uBAAA,CAChB,gBAAgB,gBAAA3C,MAAA,CAAAI,UAAA,iBACpB,UAAAJ,MAAA,CAAAK,aAAA,iBAAwC,kBACjE;IAINZ,EAAA,CAAAK,SAAA,GAA6B;IAEAL,EAF7B,CAAAM,UAAA,UAAAC,MAAA,CAAAC,MAAA,CAAA2C,YAAA,CAA6B,sBAAsB,gBAAA5C,MAAA,CAAAC,MAAA,CAAA4C,uBAAA,CAChB,gBAAA7C,MAAA,CAAAI,UAAA,iBAA2C,UAAAJ,MAAA,CAAAK,aAAA,iBAClD,kBAAkB;IAI9CZ,EAAA,CAAAK,SAAA,GAA0B;IAEwCL,EAFlE,CAAAM,UAAA,UAAAC,MAAA,CAAAC,MAAA,CAAA6C,SAAA,CAA0B,mBAAmB,gBAAA9C,MAAA,CAAAC,MAAA,CAAA8C,oBAAA,CACb,gBAAgB,gBAAA/C,MAAA,CAAAI,UAAA,cACpB,UAAAJ,MAAA,CAAAK,aAAA,cAAqC,kBAAkB;IAInFZ,EAAA,CAAAK,SAAA,GAA+B;IAG1CL,EAHW,CAAAM,UAAA,UAAAC,MAAA,CAAAC,MAAA,CAAA+C,cAAA,CAA+B,wBAAwB,gBAAAhD,MAAA,CAAAC,MAAA,CAAAgD,uBAAA,CACpB,gBAAgB,gBAAAjD,MAAA,CAAAI,UAAA,mBAClB,UAAAJ,MAAA,CAAAK,aAAA,mBAA0C,kBACrE;;;;;IAoBrBZ,EAAA,CAAAC,cAAA,cAA2G;IACzGD,EAAA,CAAAI,SAAA,sBAIc;IAChBJ,EAAA,CAAAG,YAAA,EAAM;;;;IALSH,EAAA,CAAAK,SAAA,EAAiC;IAG5CL,EAHW,CAAAM,UAAA,UAAAC,MAAA,CAAAC,MAAA,CAAAiD,gBAAA,CAAiC,0BAA0B,gBAAAlD,MAAA,CAAAC,MAAA,CAAAkD,4BAAA,CACnB,eAAe,gBAAAnD,MAAA,CAAAI,UAAA,qBACpB,UAAAJ,MAAA,CAAAK,aAAA,qBAA4C,kBACzE;;;;;IAlBrBZ,EADF,CAAAC,cAAA,cAA+E,cACpD;IACvBD,EAAA,CAAAI,SAAA,sBAIc;IAChBJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAI,SAAA,sBAIc;IAChBJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAA2D,UAAA,IAAAC,iDAAA,kBAA2G;IAO7G5D,EAAA,CAAAG,YAAA,EAAM;;;;IApBWH,EAAA,CAAAK,SAAA,GAA6B;IAGxCL,EAHW,CAAAM,UAAA,UAAAC,MAAA,CAAAC,MAAA,CAAAqD,YAAA,CAA6B,sBAAsB,gBAAAtD,MAAA,CAAAC,MAAA,CAAAsD,uBAAA,CAChB,gBAAgB,gBAAAvD,MAAA,CAAAI,UAAA,iBACpB,UAAAJ,MAAA,CAAAK,aAAA,iBAAwC,kBACjE;IAINZ,EAAA,CAAAK,SAAA,GAA4B;IAGvCL,EAHW,CAAAM,UAAA,UAAAC,MAAA,CAAAC,MAAA,CAAAuD,WAAA,CAA4B,qBAAqB,gBAAAxD,MAAA,CAAAC,MAAA,CAAAwD,sBAAA,CACf,gBAAgB,gBAAAzD,MAAA,CAAAI,UAAA,gBACpB,kBAAkB,UAAAJ,MAAA,CAAAK,aAAA,gBACrB;IAGhBZ,EAAA,CAAAK,SAAA,EAA8E;IAA9EL,EAAA,CAAAM,UAAA,SAAAC,MAAA,CAAAoB,kBAAA,mBAAApB,MAAA,CAAA0D,mBAAA,iBAA8E;;;;;IAUxGjE,EADF,CAAAC,cAAA,cAAqF,cAC1D;IACvBD,EAAA,CAAAI,SAAA,sBAIc;IAChBJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAI,SAAA,sBAGc;IAChBJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAI,SAAA,sBAGc;IAElBJ,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAlBWH,EAAA,CAAAK,SAAA,GAA2B;IAGtCL,EAHW,CAAAM,UAAA,UAAAC,MAAA,CAAAC,MAAA,CAAA0D,UAAA,CAA2B,oBAAoB,gBAAA3D,MAAA,CAAAC,MAAA,CAAA2D,qBAAA,CACd,eAAe,gBAAA5D,MAAA,CAAAI,UAAA,eACnB,UAAAJ,MAAA,CAAAK,aAAA,eAAsC,kBAC7D;IAINZ,EAAA,CAAAK,SAAA,GAA8B;IAEAL,EAF9B,CAAAM,UAAA,UAAAC,MAAA,CAAAC,MAAA,CAAA4D,aAAA,CAA8B,uBAAuB,gBAAA7D,MAAA,CAAAC,MAAA,CAAA6D,wBAAA,CACjB,gBAAA9D,MAAA,CAAAI,UAAA,kBAA4C,UAAAJ,MAAA,CAAAK,aAAA,kBACnD,kBAAkB;IAI/CZ,EAAA,CAAAK,SAAA,GAA2B;IAEAL,EAF3B,CAAAM,UAAA,UAAAC,MAAA,CAAAC,MAAA,CAAA8D,UAAA,CAA2B,oBAAoB,gBAAA/D,MAAA,CAAAC,MAAA,CAAA+D,qBAAA,CACd,gBAAAhE,MAAA,CAAAI,UAAA,eAAyC,UAAAJ,MAAA,CAAAK,aAAA,eAChD,kBAAkB;;;;;IAK7DZ,EAAA,CAAAI,SAAA,cAEM;;;;;IAzGRJ,EADF,CAAAC,cAAA,cAAkE,cACpC;IAuG1BD,EArGA,CAAA2D,UAAA,IAAAa,2CAAA,mBAAkF,IAAAC,2CAAA,mBA0BE,IAAAC,2CAAA,mBA6BL,IAAAC,2CAAA,mBAwBM,IAAAC,2CAAA,kBAsBX;IAI9E5E,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAzGIH,EAAA,CAAAK,SAAA,GAAwD;IAAxDL,EAAA,CAAAM,UAAA,SAAAC,MAAA,CAAAsE,eAAA,CAAAC,WAAA,IAAAvE,MAAA,CAAA0D,mBAAA,CAAwD;IA0BxDjE,EAAA,CAAAK,SAAA,EAA0D;IAA1DL,EAAA,CAAAM,UAAA,SAAAC,MAAA,CAAAsE,eAAA,CAAAE,aAAA,IAAAxE,MAAA,CAAA0D,mBAAA,CAA0D;IA6B1DjE,EAAA,CAAAK,SAAA,EAAqD;IAArDL,EAAA,CAAAM,UAAA,SAAAC,MAAA,CAAAsE,eAAA,CAAAG,QAAA,IAAAzE,MAAA,CAAA0D,mBAAA,CAAqD;IAwBrDjE,EAAA,CAAAK,SAAA,EAA2D;IAA3DL,EAAA,CAAAM,UAAA,SAAAC,MAAA,CAAAsE,eAAA,CAAAI,cAAA,IAAA1E,MAAA,CAAA0D,mBAAA,CAA2D;IAsB3DjE,EAAA,CAAAK,SAAA,EAAgD;IAAhDL,EAAA,CAAAM,UAAA,SAAAC,MAAA,CAAAsE,eAAA,CAAAK,GAAA,IAAA3E,MAAA,CAAA0D,mBAAA,CAAgD;;;ADlKxE,WAAakB,qBAAqB;EAA5B,MAAOA,qBAAqB;IAsDtBC,EAAA;IACAC,MAAA;IACAC,KAAA;IACAC,IAAA;IACAC,YAAA;IACAC,GAAA;IA1DVC,QAAQ,GAAG,MAAM;IACjBC,gBAAgB,GAAG,KAAK;IACxBC,iBAAiB,GAAG,KAAK;IACzBC,UAAU,GAAW,EAAE;IACvBC,YAAY,GAAW,EAAE;IACzBC,OAAO,GAAkB,IAAI;IAC7BnD,UAAU,GAAY,KAAK;IAC3BnB,SAAS;IACTuE,SAAS,GAAe,EAAE;IAC1BC,oBAAoB,GAAqB,EAAE;IAC3CC,qBAAqB,GAAqB,EAAE;IAC5CC,qBAAqB,GAAqB,EAAE;IAC5CC,iBAAiB,GAAqB,EAAE;IACxCC,kBAAkB,GAAqB,EAAE;IACzCC,yBAAyB,GAAqB,EAAE;IAChDzD,iBAAiB,GAAqB,EAAE;IACxC0D,UAAU,GAAe,EAAE;IAC3B5E,kBAAkB,GAAkB,IAAI;IACxCC,eAAe,GAAkB,IAAI;IACrC4E,aAAa,GAAe,EAAE;IAC9BvC,mBAAmB,GAAkB,IAAI;IAElCY,eAAe,GAAG;MACvBC,WAAW,EAAE,KAAK;MAClBC,aAAa,EAAE,KAAK;MACpBC,QAAQ,EAAE,KAAK;MACfC,cAAc,EAAE,KAAK;MACrBC,GAAG,EAAE;KACN;IACMuB,IAAI,GAAmB,KAAK;IAC5BjG,MAAM,GAAQV,SAAS,CAACU,MAAM;IACrCkG,eAAe,GAKT,CACF;MAAEC,KAAK,EAAE,WAAW;MAAEC,SAAS,EAAE,WAAW;MAAEC,aAAa,EAAE;IAAI,CAAE,EACnE;MAAEF,KAAK,EAAE,mBAAmB;MAAEC,SAAS,EAAE;IAAsB,CAAE,EACjE;MAAED,KAAK,EAAE,qBAAqB;MAAEC,SAAS,EAAE;IAAuB,CAAE,EACpE;MAAED,KAAK,EAAE,gBAAgB;MAAEC,SAAS,EAAE;IAAuB,CAAE,EAC/D;MAAED,KAAK,EAAE,sBAAsB;MAAEC,SAAS,EAAE;IAAmB,CAAE,EACjE;MAAED,KAAK,EAAE,WAAW;MAAEC,SAAS,EAAE;IAAoB,CAAE,EAEvD;MAAED,KAAK,EAAE,YAAY;MAAEC,SAAS,EAAE,eAAe;MAAEC,aAAa,EAAE;IAAI,CAAE,EACxE;MAAEF,KAAK,EAAE,aAAa;MAAEC,SAAS,EAAE,mBAAmB;MAAEE,eAAe,EAAE;IAAI,CAAE,CAChF;IACHC,eAAe,GAAG,KAAK;IAEvBC,eAAeA,CAAA;MACb,IAAI,CAACD,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;IAC9C;IACAE,YACU7B,EAAe,EACfC,MAAc,EACdC,KAAqB,EACrBC,IAAgB,EAChBC,YAA0B,EAC1BC,GAAsB;MALtB,KAAAL,EAAE,GAAFA,EAAE;MACF,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,KAAK,GAALA,KAAK;MACL,KAAAC,IAAI,GAAJA,IAAI;MACJ,KAAAC,YAAY,GAAZA,YAAY;MACZ,KAAAC,GAAG,GAAHA,GAAG;MAEX,IAAI,CAAChE,SAAS,GAAG,IAAI,CAAC2D,EAAE,CAAC8B,KAAK,CAAC;QAC7BC,mBAAmB,EAAE,CAAC,EAAE,EAAE,CAAC5H,UAAU,CAAC6H,QAAQ,EAAE,IAAI,CAACC,iBAAiB,CAAC,CAAC;QACxEC,gBAAgB,EAAE,CAAC,EAAE,EAAE,CAAC/H,UAAU,CAAC6H,QAAQ,EAAE,IAAI,CAACG,uBAAuB,CAAC,CAAC;QAC3E1G,WAAW,EAAE,CAAC,EAAE,EAAE,IAAI,CAACwG,iBAAiB,CAAC;QACzCG,SAAS,EAAE,CAAC,EAAE,EAAEjI,UAAU,CAAC6H,QAAQ,CAAC;QACpCK,KAAK,EAAE,CAAC,EAAE,CAAC;QACXC,YAAY,EAAE,CAAC,EAAE,EAAE,IAAI,CAACL,iBAAiB,CAAC;QAC1CM,MAAM,EAAE,CAAC,EAAE,EAAE,IAAI,CAACN,iBAAiB,CAAC;QACpCO,OAAO,EAAE,CAAC,EAAE,EAAE,IAAI,CAACP,iBAAiB,CAAC;QACrCQ,IAAI,EAAE,CAAC,EAAE,EAAE,IAAI,CAACR,iBAAiB,CAAC;QAClClF,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC5C,UAAU,CAAC6H,QAAQ,EAAE,IAAI,CAACC,iBAAiB,CAAC,CAAC;QAC5DhF,iBAAiB,EAAE,CAAC,EAAE,EAAE,IAAI,CAACgF,iBAAiB,CAAC;QAC/C9E,MAAM,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC8E,iBAAiB,CAAC;QACpC1E,UAAU,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC0E,iBAAiB,CAAC;QACxCpE,YAAY,EAAE,CAAC,EAAE,EAAE,IAAI,CAACoE,iBAAiB,CAAC;QAC1ClE,YAAY,EAAE,CAAC,EAAE,EAAE,IAAI,CAACkE,iBAAiB,CAAC;QAC1ChE,SAAS,EAAE,CAAC,EAAE,EAAE,IAAI,CAACgE,iBAAiB,CAAC;QACvC9D,cAAc,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC8D,iBAAiB,CAAC;QAC5CxD,YAAY,EAAE,CAAC,EAAE,EAAE,IAAI,CAACwD,iBAAiB,CAAC;QAC1CtD,WAAW,EAAE,CAAC,EAAE,EAAE,IAAI,CAACsD,iBAAiB,CAAC;QACzC5D,gBAAgB,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC4D,iBAAiB,CAAC;QAC9CnD,UAAU,EAAE,CAAC,EAAE,EAAE,IAAI,CAACmD,iBAAiB,CAAC;QACxCjD,aAAa,EAAE,CAAC,EAAE,EAAE,IAAI,CAACiD,iBAAiB,CAAC;QAC3C/C,UAAU,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC+C,iBAAiB,CAAC;QACxCS,QAAQ,EAAE,CAAC,EAAE,EAAEvI,UAAU,CAAC6H,QAAQ;OACnC,CAAC;IAEJ;IACAC,iBAAiB,GAAIU,OAAwB,IAA6B;MACxE,IAAIA,OAAO,CAAC/E,KAAK,IAAI+E,OAAO,CAAC/E,KAAK,CAACgF,QAAQ,EAAE,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;QAC3D,OAAO;UAAEC,cAAc,EAAE;QAAI,CAAE;MACjC;MACA,OAAO,IAAI;IACb,CAAC;IACDC,YAAYA,CAACJ,OAAwB;MACnC,IAAIA,OAAO,CAAC/E,KAAK,IAAI,CAAC,sCAAsC,CAACoF,IAAI,CAACL,OAAO,CAAC/E,KAAK,CAAC,EAAE;QAChF,OAAO;UAAEqF,UAAU,EAAE;QAAI,CAAE;MAC7B;MACA,OAAO,IAAI;IACb;IACAd,uBAAuBA,CAACQ,OAAwB;MAC9C,MAAM/E,KAAK,GAAG+E,OAAO,CAAC/E,KAAK;MAE3B,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACsF,MAAM,GAAG,CAAC,IAAItF,KAAK,CAACuF,UAAU,CAAC,GAAG,CAAC,EAAE;QAC1E,OAAO;UAAEC,YAAY,EAAE;QAAI,CAAE;MAC/B;MACA,OAAO,IAAI;IACb;IACA;IACAC,QAAQA,CAAA;MACN,IAAI,CAAC1C,OAAO,GAAG,IAAI,CAACT,KAAK,CAACoD,QAAQ,CAACC,QAAQ,CAAC5F,GAAG,CAAC,IAAI,CAAC;MACrD,IAAI,CAACH,UAAU,GAAG,CAAC,CAAC,IAAI,CAACmD,OAAO;MAChC,IAAI,CAACW,eAAe,CAACkC,OAAO,CAAC,CAAC;QAAEjC,KAAK;QAAEC,SAAS;QAAEC,aAAa;QAAEC;MAAe,CAAE,KAAI;QACpF,IAAI,CAACtB,YAAY,CAACqD,kBAAkB,CAAClC,KAAK,EAAEG,eAAe,CAAC,CAACgC,SAAS,CAAC;UACrEC,IAAI,EAAGC,OAAO,IAAI;YAChB,MAAMC,MAAM,GAAGpC,aAAa,GACxBmC,OAAO,CAACE,GAAG,CAAEC,CAAC,KAAM;cAAEC,EAAE,EAAED,CAAC,CAACnG,KAAK;cAAEqG,KAAK,EAAEF,CAAC,CAACxC;YAAK,CAAE,CAAC,CAAC,GACrDqC,OAAO,CAACE,GAAG,CAAEC,CAAC,KAAM;cAAE1I,IAAI,EAAE0I,CAAC,CAACxC,KAAK;cAAE3D,KAAK,EAAEmG,CAAC,CAACnG;YAAK,CAAE,CAAC,CAAC;YAC1D,IAAI,CAAC4D,SAAS,CAAS,GAAGqC,MAAM;YACjC,IAAI,CAACxD,GAAG,CAAC6D,aAAa,EAAE;UAC1B,CAAC;UACDC,KAAK,EAAGA,KAAK,IAAKC,OAAO,CAACD,KAAK,CAAC,kBAAkB5C,KAAK,GAAG,EAAE4C,KAAK;SAClE,CAAC;MACJ,CAAC,CAAC;MACF,IAAI,IAAI,CAAC3G,UAAU,IAAI,IAAI,CAACmD,OAAO,EAAE;QACnC,IAAI,CAAC0D,aAAa,CAAC,IAAI,CAAC1D,OAAO,CAAC;QAChC,IAAI,CAACtE,SAAS,CAACiI,OAAO,EAAE;MAC1B;IACF;IACA;IACAD,aAAaA,CAAC1D,OAAe;MAC3B,IAAI,CAACU,IAAI,GAAG,MAAM;MAClB,IAAI,CAACjB,YAAY,CAACmE,cAAc,CAAC5D,OAAO,CAAC,CAAC+C,SAAS,CAAC;QAClDC,IAAI,EAAGa,SAAc,IAAI;UACvB,IAAI,CAACjI,kBAAkB,GAAGiI,SAAS,CAAC9B,QAAQ;UAC5C,IAAI,CAAClG,eAAe,GAAGgI,SAAS,CAACnC,KAAK;UACtC,IAAI,CAACxD,mBAAmB,GAAG2F,SAAS,CAACpC,SAAS;UAC9C,IAAI,CAACqC,kBAAkB,CAAC;YAAET,EAAE,EAAEQ,SAAS,CAAC9B,QAAQ;YAAEuB,KAAK,EAAE;UAAE,CAAE,CAAC;UAC9DS,UAAU,CAAC,MAAK;YACd,IAAI,CAACrI,SAAS,CAACsI,UAAU,CAAC;cACxB5C,mBAAmB,EAAEyC,SAAS,CAACzC,mBAAmB;cAClDtG,WAAW,EAAE+I,SAAS,CAACtC,gBAAgB,IAAIsC,SAAS,CAAC/I,WAAW;cAChE2G,SAAS,EAAEoC,SAAS,CAACpC,SAAS;cAC9BF,gBAAgB,EAAEsC,SAAS,CAACtC,gBAAgB;cAC5CQ,QAAQ,EAAE8B,SAAS,CAAC9B,QAAQ;cAC5BL,KAAK,EAAEmC,SAAS,CAACnC,KAAK;cACtBtF,OAAO,EAAEyH,SAAS,CAACzH,OAAO;cAC1BE,iBAAiB,EAAEuH,SAAS,CAACvH,iBAAiB;cAC9CE,MAAM,EAAEqH,SAAS,CAACrH,MAAM;cACxBI,UAAU,EAAEiH,SAAS,CAACjH,UAAU;cAChCM,YAAY,EAAE2G,SAAS,CAAC3G,YAAY;cACpCE,YAAY,EAAEyG,SAAS,CAACzG,YAAY;cACpCE,SAAS,EAAEuG,SAAS,CAACvG,SAAS;cAC9BE,cAAc,EAAEqG,SAAS,CAACrG,cAAc;cACxCM,YAAY,EAAE+F,SAAS,CAAC/F,YAAY;cACpCE,WAAW,EAAE6F,SAAS,CAAC7F,WAAW;cAClCN,gBAAgB,EAAEmG,SAAS,CAACnG,gBAAgB;cAC5CS,UAAU,EAAE0F,SAAS,CAAC1F,UAAU;cAChCE,aAAa,EAAEwF,SAAS,CAACxF,aAAa;cACtCE,UAAU,EAAEsF,SAAS,CAACtF,UAAU;cAChCoD,YAAY,EAAEkC,SAAS,CAAClC,YAAY;cACpCC,MAAM,EAAEiC,SAAS,CAACjC,MAAM;cACxBC,OAAO,EAAEgC,SAAS,CAAChC,OAAO;cAC1BC,IAAI,EAAE+B,SAAS,CAAC/B;aACjB,CAAC;YACF,IAAI,CAACpC,GAAG,CAAC6D,aAAa,EAAE;YACxB,IAAI,IAAI,CAAC7C,IAAI,KAAK,MAAM,EAAE;cACxB,IAAI,CAAChF,SAAS,CAACiI,OAAO,EAAE;YAC1B;UACF,CAAC,EAAE,GAAG,CAAC;QACT,CAAC;QACDH,KAAK,EAAGA,KAAU,IAAI;UACpBC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACnD;OACD,CAAC;IACJ;IACAS,qBAAqBA,CAAA;MACnB,OAAO,CAAC,CAAC,IAAI,CAACrI,kBAAkB,IAAI,CAAC,CAAC,IAAI,CAACsC,mBAAmB,IAAI,CAAC,CAAC,IAAI,CAACrC,eAAe;IAC1F;IACA;IACAiI,kBAAkBA,CAACI,YAAsB;MACvC,IAAI,CAACtI,kBAAkB,GAAGsI,YAAY,CAACb,EAAE;MACzC,MAAMc,cAAc,GAAGD,YAAY,CAACb,EAAE;MACtC,IAAI,IAAI,CAAC3C,IAAI,KAAK,MAAM,EAAE;QACxB,IAAI,CAAC7E,eAAe,GAAG,IAAI;QAC3B,IAAI,CAACH,SAAS,CAACsI,UAAU,CAAC;UACxBjC,QAAQ,EAAEoC,cAAc;UACxBzC,KAAK,EAAE;SACR,EAAE;UAAE0C,SAAS,EAAE;QAAK,CAAE,CAAC;MAC1B;MACA,IAAID,cAAc,EAAE;QAClB,IAAI,CAACE,iBAAiB,CAACF,cAAc,CAAC;QACtC,IAAI,CAACG,2BAA2B,CAACH,cAAc,CAAC;QAChD,IAAI,IAAI,CAACjG,mBAAmB,EAAE;UAC5B,IAAI,CAACqG,2BAA2B,CAAC,IAAI,CAACrG,mBAAmB,EAAEiG,cAAc,CAAC;QAC5E,CAAC,MAAM;UACL,IAAI,CAAC3D,UAAU,GAAG,EAAE;QACtB;MACF;IACF;IACA;IACAgE,mBAAmBA,CAACN,YAAsB;MACxC,IAAI,CAAChG,mBAAmB,GAAGgG,YAAY,CAACb,EAAE;MAC1C,IAAI,IAAI,CAAC3C,IAAI,KAAK,MAAM,EAAE;QACxB,IAAI,CAAC7E,eAAe,GAAG,IAAI;QAC3B,IAAI,CAACH,SAAS,CAACsI,UAAU,CAAC;UACxBvC,SAAS,EAAEyC,YAAY,CAACb,EAAE;UAC1B3B,KAAK,EAAE;SACR,EAAE;UAAE0C,SAAS,EAAE;QAAK,CAAE,CAAC;MAC1B;MACA,IAAI,IAAI,CAACxI,kBAAkB,EAAE;QAC3B,IAAI,CAAC2I,2BAA2B,CAACL,YAAY,CAACb,EAAE,EAAE,IAAI,CAACzH,kBAAkB,CAAC;MAC5E,CAAC,MAAM;QACL,IAAI,CAAC4E,UAAU,GAAG,EAAE;MACtB;IACF;IACA;IACAiE,eAAeA,CAACP,YAAsB;MACpC,IAAI,CAACrI,eAAe,GAAGqI,YAAY,CAACb,EAAE;MACtC,IAAI,CAAC3H,SAAS,CAACsI,UAAU,CAAC;QAAEtC,KAAK,EAAEwC,YAAY,CAACb;MAAE,CAAE,EAAE;QAAEe,SAAS,EAAE;MAAK,CAAE,CAAC;IAC7E;IACA;IACAG,2BAA2BA,CAAC9C,SAAiB,EAAEM,QAAgB;MAC7D;MACA,MAAM2C,eAAe,GAA8B;QACjD3F,WAAW,EAAE,mBAAmB;QAChCC,aAAa,EAAE,qBAAqB;QACpCC,QAAQ,EAAE,gBAAgB;QAC1BC,cAAc,EAAE,sBAAsB;QACtCC,GAAG,EAAE,WAAW;QAChBwF,YAAY,EAAE;OACf;MACD,MAAMC,MAAM,GAAGF,eAAe,CAAC3C,QAAQ,CAAC;MACxC,IAAI,CAAC6C,MAAM,EAAE;QACX,IAAI,CAACpE,UAAU,GAAG,EAAE;QACpB;MACF;MACA,IAAI,CAACf,YAAY,CAACqD,kBAAkB,CAAC8B,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC7B,SAAS,CAAC;QAClEC,IAAI,EAAG6B,WAAkB,IAAI;UAC3B,MAAM1D,KAAK,GAAG0D,WAAW,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKvD,SAAS,CAAC;UACzD,IAAI,CAACjB,UAAU,GAAGW,KAAK,IAAIA,KAAK,CAAC8D,MAAM,GACnC9D,KAAK,CAAC8D,MAAM,CAAC9B,GAAG,CAAE+B,CAAM,KAAM;YAAE7B,EAAE,EAAE6B,CAAC,CAAC7B,EAAE;YAAEC,KAAK,EAAE4B,CAAC,CAACxK;UAAI,CAAE,CAAC,CAAC,GAC3D,EAAE;UACN,IAAI,CAACgF,GAAG,CAAC6D,aAAa,EAAE;QAC1B,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAAChD,UAAU,GAAG,EAAE;UACpBiD,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAChD;OACD,CAAC;IACJ;IACA;IACA3I,aAAaA,CAACsK,SAAiB;MAC7B,MAAMC,KAAK,GAAG,IAAI,CAAC1J,SAAS,CAACsB,GAAG,CAACmI,SAAS,CAAC;MAE3C,MAAME,YAAY,GAA2B;QAC3CjE,mBAAmB,EAAE,YAAY;QACjCG,gBAAgB,EAAE,aAAa;QAC/BnF,OAAO,EAAE,UAAU;QACnBE,iBAAiB,EAAE,qBAAqB;QACxCE,MAAM,EAAE,iBAAiB;QACzBI,UAAU,EAAE,aAAa;QACzBM,YAAY,EAAE,gBAAgB;QAC9BE,YAAY,EAAE,gBAAgB;QAC9BE,SAAS,EAAE,YAAY;QACvBE,cAAc,EAAE,kBAAkB;QAClCM,YAAY,EAAE,gBAAgB;QAC9BE,WAAW,EAAE,cAAc;QAC3BN,gBAAgB,EAAE,mBAAmB;QACrCS,UAAU,EAAE,aAAa;QACzBE,aAAa,EAAE,iBAAiB;QAChCE,UAAU,EAAE,aAAa;QACzBkD,SAAS,EAAE;OACZ;MACD,MAAM6D,kBAAkB,GAAGD,YAAY,CAACF,SAAS,CAAC,IAAIA,SAAS;MAC/D,IAAIC,KAAK,IAAIA,KAAK,CAACG,OAAO,KAAKH,KAAK,CAACI,OAAO,IAAIJ,KAAK,CAACK,KAAK,CAAC,EAAE;QAC5D,IAAIL,KAAK,CAACM,MAAM,GAAG,UAAU,CAAC,EAAE;UAC9B,OAAO,GAAGJ,kBAAkB,cAAc;QAC5C;QACA,IAAIF,KAAK,CAACM,MAAM,GAAG,WAAW,CAAC,EAAE;UAC/B,OAAO,GAAGJ,kBAAkB,qBAAqBF,KAAK,CAACM,MAAM,CAAC,WAAW,CAAC,CAACC,cAAc,kBAAkB;QAC7G;QACA,IAAIP,KAAK,CAACM,MAAM,GAAG,YAAY,CAAC,EAAE;UAChC,OAAO,0BAA0B;QACnC;QACA,IAAIN,KAAK,CAACM,MAAM,GAAG,gBAAgB,CAAC,EAAE;UACpC,OAAO,GAAGJ,kBAAkB,wBAAwB;QACtD;QACA,IAAIF,KAAK,CAACM,MAAM,GAAG,cAAc,CAAC,EAAE;UAClC,OAAO,GAAGJ,kBAAkB,wBAAwB;QACtD;MACF;MACA,OAAO,EAAE;IACX;IAEA;IACAM,iBAAiBA,CAACC,KAAU;MAC1B,MAAMC,YAAY,GAAGD,KAAK,CAACE,aAAa;MACxC,IAAID,YAAY,EAAE;QAChB,MAAME,cAAc,GAAG,IAAI,CAACzF,yBAAyB,CAACuE,IAAI,CAACmB,MAAM,IAAIA,MAAM,CAACvL,IAAI,KAAKoL,YAAY,CAAC;QAClG,MAAMI,SAAS,GAAGF,cAAc,GAAGA,cAAc,CAAC/I,KAAK,GAAG6I,YAAY;QACtE,IAAI,CAACpK,SAAS,CAACsI,UAAU,CAAC;UACxBvC,SAAS,EAAEyE;SACZ,EAAE;UAAE9B,SAAS,EAAE;QAAK,CAAE,CAAC;MAC1B;IACF;IACA;IACAC,iBAAiBA,CAAC8B,IAAY;MAC5B;MACAC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACvH,eAAe,CAAC,CAAC+D,OAAO,CAAEyD,GAAG,IAAI;QAChD,IAAI,CAACxH,eAAe,CAACwH,GAAwC,CAAC,GAAG,KAAK;MACxE,CAAC,CAAC;MACF;MACA,IAAI,IAAI,CAACxH,eAAe,CAACyH,cAAc,CAACJ,IAAI,CAAC,EAAE;QAC7C,IAAI,CAACrH,eAAe,CAACqH,IAAyC,CAAC,GAAG,IAAI;MACxE;MACA;MACA,MAAMK,eAAe,GAA6B;QAChDzH,WAAW,EAAE,CAAC,SAAS,EAAE,mBAAmB,EAAE,QAAQ,EAAE,YAAY,CAAC;QACrEC,aAAa,EAAE,CAAC,cAAc,EAAE,cAAc,EAAE,WAAW,EAAE,gBAAgB,CAAC;QAC9EC,QAAQ,EAAE,CAAC,cAAc,EAAE,aAAa,EAAE,kBAAkB,CAAC;QAC7DC,cAAc,EAAE,CAAC,YAAY,EAAE,eAAe,EAAE,YAAY,CAAC;QAC7DC,GAAG,EAAE,EAAE,CAAC;OACT;MACD;MACAiH,MAAM,CAACK,MAAM,CAACD,eAAe,CAAC,CAACE,IAAI,EAAE,CAAC7D,OAAO,CAACuC,KAAK,IAAG;QACpD,IAAI,CAAC1J,SAAS,CAACsB,GAAG,CAACoI,KAAK,CAAC,EAAEuB,KAAK,EAAE;MACpC,CAAC,CAAC;MACF;MACA,IAAI,CAACjL,SAAS,CAACkL,eAAe,EAAE;IAClC;IAEA;IACAC,sBAAsBA,CAACC,MAAc;MACnC;MACA,IAAI,CAACtG,UAAU,GAAG,EAAE;IACtB;IACCuG,SAAS,GAAW,SAAS;IAC9B;IACAtL,MAAMA,CAAA;MACJ,IAAI,IAAI,CAACC,SAAS,CAACC,KAAK,EAAE;QACxB,MAAMqL,UAAU,GAAG,IAAI,CAACtL,SAAS,CAACuB,KAAK;QACvC,MAAMgK,OAAO,GAAGb,MAAM,CAACC,IAAI,CAACW,UAAU,CAAC,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEb,GAAG,KAAI;UAC1D,MAAMrJ,KAAK,GAAG+J,UAAU,CAACV,GAAG,CAAC;UAC7B,IAAIrJ,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKmK,SAAS,IAAInK,KAAK,KAAK,EAAE,EAAE;YACzDkK,GAAG,CAACb,GAAG,CAAC,GAAGrJ,KAAK;UAClB;UACA,OAAOkK,GAAG;QACZ,CAAC,EAAE,EAAS,CAAC;QACb,IAAI,EAAE,IAAI,CAACvL,kBAAkB,KAAK,UAAU,IAAI,IAAI,CAACsC,mBAAmB,KAAK,WAAW,CAAC,EAAE;UAC3F,OAAO+I,OAAO,CAACvJ,gBAAgB;QACjC;QACE,IAAI,IAAI,CAACb,UAAU,IAAI,IAAI,CAACmD,OAAO,EAAE,CACrC,CAAC,MAAM;UACL,IAAI,CAACP,YAAY,CAAC4H,SAAS,CAACJ,OAAO,CAAC,CAAClE,SAAS,CAAC;YAC7CC,IAAI,EAAGsE,IAAI,IAAI;cACb,IAAI,CAAC3H,QAAQ,GAAG,cAAc;cAC9B,IAAI,CAACI,YAAY,GAAGuH,IAAI,EAAEA,IAAI,EAAEC,OAAO,IAAID,IAAI,CAACC,OAAO;cACvD,IAAI,CAAC3H,gBAAgB,GAAG,IAAI;cAC5B,IAAI,CAACC,iBAAiB,GAAG,IAAI;cAC7B,IAAI,CAACkH,SAAS,GAAG,SAAS;YAC5B,CAAC;YACDvD,KAAK,EAAGA,KAAK,IAAI;cACf,IAAI,CAAC7D,QAAQ,GAAG,MAAM;cACtB,IAAI,CAACI,YAAY,GAAGyD,KAAK,EAAEA,KAAK,EAAE+D,OAAO,IAAI/D,KAAK,CAAC+D,OAAO;cAC1D,IAAI,CAAC3H,gBAAgB,GAAG,IAAI;cAC5B,IAAI,CAACC,iBAAiB,GAAG,KAAK;cAC9B,IAAI,CAACkH,SAAS,GAAG,SAAS;YAC5B;WACD,CAAC;QACJ;MACF,CAAC,MAAM;QACL,IAAI,CAACS,oBAAoB,EAAE;MAC7B;IACF;IACA;IACAA,oBAAoBA,CAAA;MAClBpB,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC3K,SAAS,CAAC+L,QAAQ,CAAC,CAAC5E,OAAO,CAAEyD,GAAG,IAAI;QACnD,MAAMtE,OAAO,GAAG,IAAI,CAACtG,SAAS,CAACsB,GAAG,CAACsJ,GAAG,CAAC;QACvC,IAAItE,OAAO,EAAE;UACXA,OAAO,CAAC0F,aAAa,EAAE;QACzB;MACF,CAAC,CAAC;IACJ;IACA;IACApM,QAAQA,CAAA;MACN,IAAI,IAAI,CAACoF,IAAI,KAAK,KAAK,EAAE;QACvB,IAAI,CAAChF,SAAS,CAACiL,KAAK,CAAC;UACnB5E,QAAQ,EAAE,EAAE;UACZN,SAAS,EAAE;SACZ,CAAC;QACF2E,MAAM,CAACC,IAAI,CAAC,IAAI,CAACvH,eAAe,CAAC,CAAC+D,OAAO,CAAEyD,GAAG,IAAI;UAChD,IAAI,CAACxH,eAAe,CAACwH,GAAwC,CAAC,GAAG,KAAK;QACxE,CAAC,CAAC;MACJ;MACA,IAAI,CAAChH,MAAM,CAACqI,QAAQ,CAAC,CAAC,mBAAmB,CAAC,CAAC;IAC7C;IACA;IACA/M,UAAUA,CAACF,IAAY;MACrB,OAAO,IAAI,CAACgB,SAAS,CAACsB,GAAG,CAACtC,IAAI,CAAgB;IAChD;IACA;IACA,IAAIkN,YAAYA,CAAA;MACd,OAAO,IAAI,CAAClM,SAAS,CAAC+L,QAAQ;IAChC;IACA;IACA,IAAI1F,QAAQA,CAAA;MACV,OAAO,IAAI,CAAC6F,YAAY,CAAC,UAAU,CAAC;IACtC;IACA;IACA,IAAIC,kBAAkBA,CAAA;MACpB,OACE,IAAI,CAAC9F,QAAQ,CAAC9E,KAAK,KAAK,aAAa,IACrC,IAAI,CAAC8E,QAAQ,CAAC9E,KAAK,KAAK,eAAe,IACvC,IAAI,CAAC8E,QAAQ,CAAC9E,KAAK,KAAK,UAAU,IAClC,IAAI,CAAC8E,QAAQ,CAAC9E,KAAK,KAAK,gBAAgB,IACxC,IAAI,CAAC8E,QAAQ,CAAC9E,KAAK,KAAK,KAAK,IAC7B,IAAI,CAACyD,IAAI,KAAK,MAAM;IAExB;IACA;IACAoH,gBAAgBA,CAAA;MACd,IAAI,CAACC,iBAAiB,EAAE;IAC1B;IACA;IACAA,iBAAiBA,CAAA;MACf,IAAI,CAACnI,gBAAgB,GAAG,KAAK;MAC7B,IAAI,CAACE,UAAU,GAAG,EAAE;MACpB,IAAI,CAACC,YAAY,GAAG,EAAE;MACtB,IAAI,IAAI,CAACF,iBAAiB,EAAE;QAC1B,IAAI,CAACP,MAAM,CAACqI,QAAQ,CAAC,CAAC,mBAAmB,CAAC,CAAC;MAC7C;IACF;IACA;IACArD,2BAA2BA,CAACH,cAAsB;MAChD,MAAM6D,cAAc,GAA6B;QAC/CjJ,WAAW,EAAE,CAAC,SAAS,EAAE,mBAAmB,EAAE,QAAQ,EAAE,YAAY,CAAC;QACrEC,aAAa,EAAE,CAAC,cAAc,EAAE,cAAc,EAAE,WAAW,EAAE,gBAAgB,CAAC;QAC9EC,QAAQ,EAAE,CAAC,cAAc,EAAE,aAAa,EAAE,kBAAkB,CAAC;QAC7DC,cAAc,EAAE,CAAC,YAAY,EAAE,eAAe,EAAE,YAAY,CAAC;QAC7DC,GAAG,EAAE;OACN;MACD,MAAM8I,cAAc,GAAGD,cAAc,CAAC7D,cAAc,CAAC,IAAI,EAAE;MAC3D,MAAM+D,eAAe,GAAG9B,MAAM,CAACK,MAAM,CAACuB,cAAc,CAAC,CAACtB,IAAI,EAAE;MAC5D;MACAwB,eAAe,CAACrF,OAAO,CAACuC,KAAK,IAAG;QAC9B,MAAMpD,OAAO,GAAG,IAAI,CAACtG,SAAS,CAACsB,GAAG,CAACoI,KAAK,CAAC;QACzC,IAAIpD,OAAO,EAAE;UACXA,OAAO,CAACmG,eAAe,EAAE;UACzBnG,OAAO,CAACoG,sBAAsB,EAAE;QAClC;MACF,CAAC,CAAC;MACF;MACAH,cAAc,CAACpF,OAAO,CAACuC,KAAK,IAAG;QAC7B,MAAMpD,OAAO,GAAG,IAAI,CAACtG,SAAS,CAACsB,GAAG,CAACoI,KAAK,CAAC;QACzC,IAAIpD,OAAO,EAAE;UACX,IAAIoD,KAAK,KAAK,SAAS,IAAIA,KAAK,KAAK,YAAY,EAAE;YACjDpD,OAAO,CAACqG,aAAa,CAAC,CAAC7O,UAAU,CAAC6H,QAAQ,EAAE,IAAI,CAACC,iBAAiB,EAAE,IAAI,CAACc,YAAY,CAAC,CAAC;YACvFJ,OAAO,CAACoG,sBAAsB,EAAE;UAClC,CAAC,MACI;YACHpG,OAAO,CAACqG,aAAa,CAAC,CAAC7O,UAAU,CAAC6H,QAAQ,EAAE,IAAI,CAACC,iBAAiB,CAAC,CAAC;YACpEU,OAAO,CAACoG,sBAAsB,EAAE;UAClC;QACF;MACF,CAAC,CAAC;IACJ;;uCAndWhJ,qBAAqB,EAAAnF,EAAA,CAAAqO,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAvO,EAAA,CAAAqO,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAzO,EAAA,CAAAqO,iBAAA,CAAAG,EAAA,CAAAE,cAAA,GAAA1O,EAAA,CAAAqO,iBAAA,CAAAM,EAAA,CAAAC,UAAA,GAAA5O,EAAA,CAAAqO,iBAAA,CAAAQ,EAAA,CAAAC,YAAA,GAAA9O,EAAA,CAAAqO,iBAAA,CAAArO,EAAA,CAAA+O,iBAAA;IAAA;;YAArB5J,qBAAqB;MAAA6J,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChClCtP,EAAA,CAAAC,cAAA,WAAsB;UAAAD,EAAA,CAAAE,MAAA,0BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAMrCH,EALR,CAAAC,cAAA,aAAqC,cACL,aACH,aAEsC,aAClC;UACvBD,EAAA,CAAA2D,UAAA,IAAA6L,qCAAA,kBAAkD;UAClDxP,EAAA,CAAAC,cAAA,qBAAiF;UAA5BD,EAAA,CAAAe,UAAA,mBAAA0O,4DAAA;YAAA,OAASF,GAAA,CAAAvI,eAAA,EAAiB;UAAA,EAAC;UAClFhH,EADmF,CAAAG,YAAA,EAAc,EAC3F;UACNH,EAAA,CAAA2D,UAAA,IAAA+L,oCAAA,kBAAmD;UAUrD1P,EAAA,CAAAG,YAAA,EAAM;UAQIH,EANV,CAAAC,cAAA,cAA0B,eAEU,eACR,eACI,eACM,UACxB;UAAAD,EAAA,CAAAE,MAAA,IAA+B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxCH,EAAA,CAAAC,cAAA,eAA8B;UAO5BD,EANA,CAAA2D,UAAA,KAAAgM,4CAAA,yBAI2B,KAAAC,4CAAA,yBAWF;UAE3B5P,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAA2D,UAAA,KAAAkM,4CAAA,yBAAyF;UAE3F7P,EAAA,CAAAG,YAAA,EAAM;UAKAH,EAJN,CAAAC,cAAA,eAA6B,eACG,eAEmE,oBAEL;UAAhDD,EAAA,CAAAe,UAAA,8BAAA+O,qEAAAC,MAAA;YAAA,OAAoBR,GAAA,CAAA1F,kBAAA,CAAAkG,MAAA,CAA0B;UAAA,EAAC;UAG3F/P,EAFI,CAAAG,YAAA,EAAW,EACP,EACF;UAIFH,EAHJ,CAAAC,cAAA,eAAkC,eAE+D,oBAEH;UAAjDD,EAAA,CAAAe,UAAA,8BAAAiP,qEAAAD,MAAA;YAAA,OAAoBR,GAAA,CAAAhF,mBAAA,CAAAwF,MAAA,CAA2B;UAAA,EAAC;UAG7F/P,EAFI,CAAAG,YAAA,EAAW,EACP,EACF;UAIFH,EAFJ,CAAAC,cAAA,eAA6B,eACoE,oBAI9C;UAA7CD,EAAA,CAAAe,UAAA,8BAAAkP,qEAAAF,MAAA;YAAA,OAAoBR,GAAA,CAAA/E,eAAA,CAAAuF,MAAA,CAAuB;UAAA,EAAC;UAC9C/P,EAAA,CAAAG,YAAA,EAAW;UACXH,EAAA,CAAA2D,UAAA,KAAAuM,qCAAA,kBAC2B;UAKjClQ,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;UACNH,EAAA,CAAAI,SAAA,UAAI;UACJJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,IAA6B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACtCH,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAA2D,UAAA,KAAAwM,mCAAA,gBAAkE;UAGpEnQ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAA2D,UAAA,KAAAyM,qCAAA,kBAAkE;UAmHhFpQ,EANY,CAAAG,YAAA,EAAM,EACF,EACF,EACF,EACF,EACD,EACH;UAENH,EAAA,CAAAC,cAAA,qBAGiC;UAA/BD,EADyE,CAAAe,UAAA,qBAAAsP,6DAAA;YAAA,OAAWd,GAAA,CAAA1B,gBAAA,EAAkB;UAAA,EAAC,oBAAAyC,4DAAA;YAAA,OAC7Ff,GAAA,CAAAzB,iBAAA,EAAmB;UAAA,EAAC;UAChC9N,EAAA,CAAAG,YAAA,EAAY;;;UAjNJH,EAAA,CAAAK,SAAA,GAAuB;UAAvBL,EAAA,CAAAM,UAAA,cAAAiP,GAAA,CAAA9N,SAAA,CAAuB;UAGAzB,EAAA,CAAAK,SAAA,GAAmC;UAAnCL,EAAA,CAAAuQ,WAAA,cAAAhB,GAAA,CAAAxI,eAAA,CAAmC;UAE9B/G,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAM,UAAA,UAAAiP,GAAA,CAAAxI,eAAA,CAAsB;UAGvB/G,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAM,UAAA,UAAAiP,GAAA,CAAAxI,eAAA,CAAsB;UAkBrC/G,EAAA,CAAAK,SAAA,GAA+B;UAA/BL,EAAA,CAAAwQ,iBAAA,CAAAjB,GAAA,CAAA/O,MAAA,CAAAiQ,oBAAA,CAA+B;UAG9BzQ,EAAA,CAAAK,SAAA,GAAiB;UAAjBL,EAAA,CAAAM,UAAA,UAAAiP,GAAA,CAAA3M,UAAA,CAAiB;UAOjB5C,EAAA,CAAAK,SAAA,EAAiB;UAAjBL,EAAA,CAAAM,UAAA,UAAAiP,GAAA,CAAA3M,UAAA,CAAiB;UAUT5C,EAAA,CAAAK,SAAA,EAAgB;UAAhBL,EAAA,CAAAM,UAAA,SAAAiP,GAAA,CAAA3M,UAAA,CAAgB;UAMC5C,EAAA,CAAAK,SAAA,GAAkE;UAAlEL,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAA0Q,eAAA,KAAAC,GAAA,EAAApB,GAAA,CAAA3M,UAAA,IAAA2M,GAAA,CAAA9I,IAAA,aAAkE;UAClFzG,EAAA,CAAAK,SAAA,EAAiC;UAAjCL,EAAA,CAAAyC,qBAAA,UAAA8M,GAAA,CAAA/O,MAAA,CAAAoQ,cAAA,CAAiC;UACzC5Q,EAD0C,CAAAM,UAAA,UAAAiP,GAAA,CAAAvJ,SAAA,CAAmB,mBAAAuJ,GAAA,CAAA5N,kBAAA,CACxB;UAMb3B,EAAA,CAAAK,SAAA,GAAkE;UAAlEL,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAA0Q,eAAA,KAAAC,GAAA,EAAApB,GAAA,CAAA3M,UAAA,IAAA2M,GAAA,CAAA9I,IAAA,aAAkE;UAClFzG,EAAA,CAAAK,SAAA,EAAkC;UAAlCL,EAAA,CAAAyC,qBAAA,UAAA8M,GAAA,CAAA/O,MAAA,CAAAqQ,eAAA,CAAkC;UAC1C7Q,EAD2C,CAAAM,UAAA,UAAAiP,GAAA,CAAA/I,aAAA,CAAuB,mBAAA+I,GAAA,CAAAtL,mBAAA,CAC5B;UAMdjE,EAAA,CAAAK,SAAA,GAAkE;UAAlEL,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAA0Q,eAAA,KAAAC,GAAA,EAAApB,GAAA,CAAA3M,UAAA,IAAA2M,GAAA,CAAA9I,IAAA,aAAkE;UAG1FzG,EAAA,CAAAK,SAAA,EAA8B;UAA9BL,EAAA,CAAAyC,qBAAA,UAAA8M,GAAA,CAAA/O,MAAA,CAAAsQ,WAAA,CAA8B;UAAsB9Q,EADpD,CAAAM,UAAA,eAAAiP,GAAA,CAAAhJ,UAAA,CAAA+B,MAAA,UAAAiH,GAAA,CAAAtL,mBAAA,IAAAsL,GAAA,CAAA5N,kBAAA,6DAAA4N,GAAA,CAAA/O,MAAA,CAAAuQ,uBAAA,CAAgL,UAAAxB,GAAA,CAAAhJ,UAAA,CAC7H,mBAAAgJ,GAAA,CAAA3N,eAAA,CAAmC;UAGlF5B,EAAA,CAAAK,SAAA,EAA0E;UAA1EL,EAAA,CAAAM,UAAA,SAAAiP,GAAA,CAAAhJ,UAAA,CAAA+B,MAAA,UAAAiH,GAAA,CAAAtL,mBAAA,IAAAsL,GAAA,CAAA5N,kBAAA,CAA0E;UAQlF3B,EAAA,CAAAK,SAAA,GAA6B;UAA7BL,EAAA,CAAAwQ,iBAAA,CAAAjB,GAAA,CAAA/O,MAAA,CAAAwQ,kBAAA,CAA6B;UAEGhR,EAAA,CAAAK,SAAA,GAA8B;UAA9BL,EAAA,CAAAM,UAAA,UAAAiP,GAAA,CAAAvF,qBAAA,GAA8B;UAI/BhK,EAAA,CAAAK,SAAA,EAA6B;UAA7BL,EAAA,CAAAM,UAAA,SAAAiP,GAAA,CAAAvF,qBAAA,GAA6B;UAsH5EhK,EAAA,CAAAK,SAAA,EAA6B;UAA7BL,EAAA,CAAAyC,qBAAA,mBAAA8M,GAAA,CAAA7J,QAAA,CAA6B;UACM1F,EAF1B,CAAAM,UAAA,SAAAiP,GAAA,CAAA5J,gBAAA,CAAyB,UAAA4J,GAAA,CAAA1J,UAAA,CAAqB,YAAA0J,GAAA,CAAAzJ,YAAA,CAAyB,wBAAwB,cAAAyJ,GAAA,CAAAzC,SAAA,CACnD,mBAAkB,qBAAqB,mCAC1D,sCAAsC;;;qBD/LtEzN,YAAY,EAAA4R,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZ7R,mBAAmB,EAAAgP,EAAA,CAAA8C,aAAA,EAAA9C,EAAA,CAAA+C,eAAA,EAAA/C,EAAA,CAAAgD,oBAAA,EAAAhD,EAAA,CAAAiD,iBAAA,EAAAjD,EAAA,CAAAkD,oBAAA,EAAAlD,EAAA,CAAAmD,kBAAA,EAAAnD,EAAA,CAAAoD,eAAA,EACnB3R,mBAAmB,EAAA4R,EAAA,CAAAC,sBAAA,EACnBhS,aAAa,EACbJ,mBAAmB,EACnBE,eAAe,EACfD,oBAAoB,EACpBE,iBAAiB,EACjBE,cAAc;MAAAgS,MAAA;MAAAC,aAAA;IAAA;;SAML3M,qBAAqB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}