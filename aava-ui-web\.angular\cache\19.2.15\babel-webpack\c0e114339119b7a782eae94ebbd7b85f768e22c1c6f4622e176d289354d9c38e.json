{"ast": null, "code": "import Quad from \"./quad.js\";\nexport default function (x, y, radius) {\n  var data,\n    x0 = this._x0,\n    y0 = this._y0,\n    x1,\n    y1,\n    x2,\n    y2,\n    x3 = this._x1,\n    y3 = this._y1,\n    quads = [],\n    node = this._root,\n    q,\n    i;\n  if (node) quads.push(new Quad(node, x0, y0, x3, y3));\n  if (radius == null) radius = Infinity;else {\n    x0 = x - radius, y0 = y - radius;\n    x3 = x + radius, y3 = y + radius;\n    radius *= radius;\n  }\n  while (q = quads.pop()) {\n    // Stop searching if this quadrant can’t contain a closer node.\n    if (!(node = q.node) || (x1 = q.x0) > x3 || (y1 = q.y0) > y3 || (x2 = q.x1) < x0 || (y2 = q.y1) < y0) continue;\n\n    // Bisect the current quadrant.\n    if (node.length) {\n      var xm = (x1 + x2) / 2,\n        ym = (y1 + y2) / 2;\n      quads.push(new Quad(node[3], xm, ym, x2, y2), new Quad(node[2], x1, ym, xm, y2), new Quad(node[1], xm, y1, x2, ym), new Quad(node[0], x1, y1, xm, ym));\n\n      // Visit the closest quadrant first.\n      if (i = (y >= ym) << 1 | x >= xm) {\n        q = quads[quads.length - 1];\n        quads[quads.length - 1] = quads[quads.length - 1 - i];\n        quads[quads.length - 1 - i] = q;\n      }\n    }\n\n    // Visit this point. (Visiting coincident points isn’t necessary!)\n    else {\n      var dx = x - +this._x.call(null, node.data),\n        dy = y - +this._y.call(null, node.data),\n        d2 = dx * dx + dy * dy;\n      if (d2 < radius) {\n        var d = Math.sqrt(radius = d2);\n        x0 = x - d, y0 = y - d;\n        x3 = x + d, y3 = y + d;\n        data = node.data;\n      }\n    }\n  }\n  return data;\n}", "map": {"version": 3, "names": ["Quad", "x", "y", "radius", "data", "x0", "_x0", "y0", "_y0", "x1", "y1", "x2", "y2", "x3", "_x1", "y3", "_y1", "quads", "node", "_root", "q", "i", "push", "Infinity", "pop", "length", "xm", "ym", "dx", "_x", "call", "dy", "_y", "d2", "d", "Math", "sqrt"], "sources": ["C:/console/aava-ui-web/node_modules/d3-quadtree/src/find.js"], "sourcesContent": ["import Quad from \"./quad.js\";\n\nexport default function(x, y, radius) {\n  var data,\n      x0 = this._x0,\n      y0 = this._y0,\n      x1,\n      y1,\n      x2,\n      y2,\n      x3 = this._x1,\n      y3 = this._y1,\n      quads = [],\n      node = this._root,\n      q,\n      i;\n\n  if (node) quads.push(new Quad(node, x0, y0, x3, y3));\n  if (radius == null) radius = Infinity;\n  else {\n    x0 = x - radius, y0 = y - radius;\n    x3 = x + radius, y3 = y + radius;\n    radius *= radius;\n  }\n\n  while (q = quads.pop()) {\n\n    // Stop searching if this quadrant can’t contain a closer node.\n    if (!(node = q.node)\n        || (x1 = q.x0) > x3\n        || (y1 = q.y0) > y3\n        || (x2 = q.x1) < x0\n        || (y2 = q.y1) < y0) continue;\n\n    // Bisect the current quadrant.\n    if (node.length) {\n      var xm = (x1 + x2) / 2,\n          ym = (y1 + y2) / 2;\n\n      quads.push(\n        new Quad(node[3], xm, ym, x2, y2),\n        new Quad(node[2], x1, ym, xm, y2),\n        new Quad(node[1], xm, y1, x2, ym),\n        new Quad(node[0], x1, y1, xm, ym)\n      );\n\n      // Visit the closest quadrant first.\n      if (i = (y >= ym) << 1 | (x >= xm)) {\n        q = quads[quads.length - 1];\n        quads[quads.length - 1] = quads[quads.length - 1 - i];\n        quads[quads.length - 1 - i] = q;\n      }\n    }\n\n    // Visit this point. (Visiting coincident points isn’t necessary!)\n    else {\n      var dx = x - +this._x.call(null, node.data),\n          dy = y - +this._y.call(null, node.data),\n          d2 = dx * dx + dy * dy;\n      if (d2 < radius) {\n        var d = Math.sqrt(radius = d2);\n        x0 = x - d, y0 = y - d;\n        x3 = x + d, y3 = y + d;\n        data = node.data;\n      }\n    }\n  }\n\n  return data;\n}\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,WAAW;AAE5B,eAAe,UAASC,CAAC,EAAEC,CAAC,EAAEC,MAAM,EAAE;EACpC,IAAIC,IAAI;IACJC,EAAE,GAAG,IAAI,CAACC,GAAG;IACbC,EAAE,GAAG,IAAI,CAACC,GAAG;IACbC,EAAE;IACFC,EAAE;IACFC,EAAE;IACFC,EAAE;IACFC,EAAE,GAAG,IAAI,CAACC,GAAG;IACbC,EAAE,GAAG,IAAI,CAACC,GAAG;IACbC,KAAK,GAAG,EAAE;IACVC,IAAI,GAAG,IAAI,CAACC,KAAK;IACjBC,CAAC;IACDC,CAAC;EAEL,IAAIH,IAAI,EAAED,KAAK,CAACK,IAAI,CAAC,IAAItB,IAAI,CAACkB,IAAI,EAAEb,EAAE,EAAEE,EAAE,EAAEM,EAAE,EAAEE,EAAE,CAAC,CAAC;EACpD,IAAIZ,MAAM,IAAI,IAAI,EAAEA,MAAM,GAAGoB,QAAQ,CAAC,KACjC;IACHlB,EAAE,GAAGJ,CAAC,GAAGE,MAAM,EAAEI,EAAE,GAAGL,CAAC,GAAGC,MAAM;IAChCU,EAAE,GAAGZ,CAAC,GAAGE,MAAM,EAAEY,EAAE,GAAGb,CAAC,GAAGC,MAAM;IAChCA,MAAM,IAAIA,MAAM;EAClB;EAEA,OAAOiB,CAAC,GAAGH,KAAK,CAACO,GAAG,CAAC,CAAC,EAAE;IAEtB;IACA,IAAI,EAAEN,IAAI,GAAGE,CAAC,CAACF,IAAI,CAAC,IACb,CAACT,EAAE,GAAGW,CAAC,CAACf,EAAE,IAAIQ,EAAE,IAChB,CAACH,EAAE,GAAGU,CAAC,CAACb,EAAE,IAAIQ,EAAE,IAChB,CAACJ,EAAE,GAAGS,CAAC,CAACX,EAAE,IAAIJ,EAAE,IAChB,CAACO,EAAE,GAAGQ,CAAC,CAACV,EAAE,IAAIH,EAAE,EAAE;;IAEzB;IACA,IAAIW,IAAI,CAACO,MAAM,EAAE;MACf,IAAIC,EAAE,GAAG,CAACjB,EAAE,GAAGE,EAAE,IAAI,CAAC;QAClBgB,EAAE,GAAG,CAACjB,EAAE,GAAGE,EAAE,IAAI,CAAC;MAEtBK,KAAK,CAACK,IAAI,CACR,IAAItB,IAAI,CAACkB,IAAI,CAAC,CAAC,CAAC,EAAEQ,EAAE,EAAEC,EAAE,EAAEhB,EAAE,EAAEC,EAAE,CAAC,EACjC,IAAIZ,IAAI,CAACkB,IAAI,CAAC,CAAC,CAAC,EAAET,EAAE,EAAEkB,EAAE,EAAED,EAAE,EAAEd,EAAE,CAAC,EACjC,IAAIZ,IAAI,CAACkB,IAAI,CAAC,CAAC,CAAC,EAAEQ,EAAE,EAAEhB,EAAE,EAAEC,EAAE,EAAEgB,EAAE,CAAC,EACjC,IAAI3B,IAAI,CAACkB,IAAI,CAAC,CAAC,CAAC,EAAET,EAAE,EAAEC,EAAE,EAAEgB,EAAE,EAAEC,EAAE,CAClC,CAAC;;MAED;MACA,IAAIN,CAAC,GAAG,CAACnB,CAAC,IAAIyB,EAAE,KAAK,CAAC,GAAI1B,CAAC,IAAIyB,EAAG,EAAE;QAClCN,CAAC,GAAGH,KAAK,CAACA,KAAK,CAACQ,MAAM,GAAG,CAAC,CAAC;QAC3BR,KAAK,CAACA,KAAK,CAACQ,MAAM,GAAG,CAAC,CAAC,GAAGR,KAAK,CAACA,KAAK,CAACQ,MAAM,GAAG,CAAC,GAAGJ,CAAC,CAAC;QACrDJ,KAAK,CAACA,KAAK,CAACQ,MAAM,GAAG,CAAC,GAAGJ,CAAC,CAAC,GAAGD,CAAC;MACjC;IACF;;IAEA;IAAA,KACK;MACH,IAAIQ,EAAE,GAAG3B,CAAC,GAAG,CAAC,IAAI,CAAC4B,EAAE,CAACC,IAAI,CAAC,IAAI,EAAEZ,IAAI,CAACd,IAAI,CAAC;QACvC2B,EAAE,GAAG7B,CAAC,GAAG,CAAC,IAAI,CAAC8B,EAAE,CAACF,IAAI,CAAC,IAAI,EAAEZ,IAAI,CAACd,IAAI,CAAC;QACvC6B,EAAE,GAAGL,EAAE,GAAGA,EAAE,GAAGG,EAAE,GAAGA,EAAE;MAC1B,IAAIE,EAAE,GAAG9B,MAAM,EAAE;QACf,IAAI+B,CAAC,GAAGC,IAAI,CAACC,IAAI,CAACjC,MAAM,GAAG8B,EAAE,CAAC;QAC9B5B,EAAE,GAAGJ,CAAC,GAAGiC,CAAC,EAAE3B,EAAE,GAAGL,CAAC,GAAGgC,CAAC;QACtBrB,EAAE,GAAGZ,CAAC,GAAGiC,CAAC,EAAEnB,EAAE,GAAGb,CAAC,GAAGgC,CAAC;QACtB9B,IAAI,GAAGc,IAAI,CAACd,IAAI;MAClB;IACF;EACF;EAEA,OAAOA,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}