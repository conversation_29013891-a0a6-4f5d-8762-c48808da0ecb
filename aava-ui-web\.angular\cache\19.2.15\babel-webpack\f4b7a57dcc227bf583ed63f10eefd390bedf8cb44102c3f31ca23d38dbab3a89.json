{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction PaginationComponent_div_0_ng_container_5_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 12);\n    i0.ɵɵtext(1, \"...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PaginationComponent_div_0_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function PaginationComponent_div_0_ng_container_5_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.changePage(ctx_r1.pages[0]));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, PaginationComponent_div_0_ng_container_5_span_3_Template, 2, 0, \"span\", 11);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r1.pages[0] === ctx_r1.currentPage);\n    i0.ɵɵattribute(\"aria-current\", ctx_r1.pages[0] === ctx_r1.currentPage ? \"page\" : null);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.pages[0], \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showStartEllipsis);\n  }\n}\nfunction PaginationComponent_div_0_ng_container_6_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function PaginationComponent_div_0_ng_container_6_ng_container_1_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const page_r5 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.changePage(page_r5));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const page_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", page_r5 === ctx_r1.currentPage);\n    i0.ɵɵattribute(\"aria-current\", page_r5 === ctx_r1.currentPage ? \"page\" : null);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", page_r5, \" \");\n  }\n}\nfunction PaginationComponent_div_0_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PaginationComponent_div_0_ng_container_6_ng_container_1_Template, 3, 4, \"ng-container\", 6);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const i_r6 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r6 > 0 && i_r6 < ctx_r1.pages.length - 1);\n  }\n}\nfunction PaginationComponent_div_0_ng_container_7_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 12);\n    i0.ɵɵtext(1, \"...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PaginationComponent_div_0_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PaginationComponent_div_0_ng_container_7_span_1_Template, 2, 0, \"span\", 11);\n    i0.ɵɵelementStart(2, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function PaginationComponent_div_0_ng_container_7_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.changePage(ctx_r1.pages[ctx_r1.pages.length - 1]));\n    });\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showEllipsis);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r1.pages[ctx_r1.pages.length - 1] === ctx_r1.currentPage);\n    i0.ɵɵattribute(\"aria-current\", ctx_r1.pages[ctx_r1.pages.length - 1] === ctx_r1.currentPage ? \"page\" : null);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.pages[ctx_r1.pages.length - 1], \" \");\n  }\n}\nfunction PaginationComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"button\", 2);\n    i0.ɵɵlistener(\"click\", function PaginationComponent_div_0_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.previousPage());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 3);\n    i0.ɵɵelement(3, \"path\", 4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"div\", 5);\n    i0.ɵɵtemplate(5, PaginationComponent_div_0_ng_container_5_Template, 4, 5, \"ng-container\", 6)(6, PaginationComponent_div_0_ng_container_6_Template, 2, 1, \"ng-container\", 7)(7, PaginationComponent_div_0_ng_container_7_Template, 4, 5, \"ng-container\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function PaginationComponent_div_0_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextPage());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(9, \"svg\", 3);\n    i0.ɵɵelement(10, \"path\", 9);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.currentPage === 1);\n    i0.ɵɵattribute(\"aria-disabled\", ctx_r1.currentPage === 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.pages.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.pages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.pages.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", ctx_r1.currentPage === ctx_r1.totalPages);\n    i0.ɵɵattribute(\"aria-disabled\", ctx_r1.currentPage === ctx_r1.totalPages);\n  }\n}\nexport let PaginationComponent = /*#__PURE__*/(() => {\n  class PaginationComponent {\n    totalItems = 0;\n    currentPage = 1;\n    itemsPerPage = 12;\n    visiblePageCount = 5;\n    pageChange = new EventEmitter();\n    totalPages = 0;\n    pages = [];\n    showEllipsis = false;\n    showStartEllipsis = false;\n    ngOnChanges(changes) {\n      this.calculatePages();\n    }\n    calculatePages() {\n      this.totalPages = Math.ceil(this.totalItems / this.itemsPerPage);\n      if (this.totalPages <= this.visiblePageCount) {\n        // If we have fewer pages than the visible count, show all pages\n        this.pages = Array.from({\n          length: this.totalPages\n        }, (_, i) => i + 1);\n        this.showEllipsis = false;\n        this.showStartEllipsis = false;\n      } else {\n        const maxPagesBeforeCurrentPage = Math.floor((this.visiblePageCount - 1) / 2);\n        const maxPagesAfterCurrentPage = Math.ceil((this.visiblePageCount - 1) / 2);\n        let startPage = Math.max(1, this.currentPage - maxPagesBeforeCurrentPage);\n        let endPage = Math.min(this.totalPages, startPage + this.visiblePageCount - 1);\n        // Adjust if we're near the end of the page range\n        if (endPage >= this.totalPages) {\n          startPage = Math.max(1, this.totalPages - this.visiblePageCount + 1);\n          endPage = this.totalPages;\n        }\n        // Generate page numbers\n        this.pages = Array.from({\n          length: endPage - startPage + 1\n        }, (_, i) => startPage + i);\n        // Add indicators for ellipses\n        this.showStartEllipsis = startPage > 1;\n        this.showEllipsis = endPage < this.totalPages;\n        // Always include first and last page if needed\n        if (startPage > 1) {\n          this.pages.unshift(1);\n        }\n        if (endPage < this.totalPages) {\n          this.pages.push(this.totalPages);\n        }\n      }\n    }\n    changePage(page) {\n      if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {\n        this.currentPage = page;\n        this.pageChange.emit(this.currentPage);\n        this.calculatePages();\n      }\n    }\n    previousPage() {\n      this.changePage(this.currentPage - 1);\n    }\n    nextPage() {\n      this.changePage(this.currentPage + 1);\n    }\n    static ɵfac = function PaginationComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PaginationComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PaginationComponent,\n      selectors: [[\"app-pagination\"]],\n      inputs: {\n        totalItems: \"totalItems\",\n        currentPage: \"currentPage\",\n        itemsPerPage: \"itemsPerPage\",\n        visiblePageCount: \"visiblePageCount\"\n      },\n      outputs: {\n        pageChange: \"pageChange\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 1,\n      vars: 1,\n      consts: [[\"class\", \"pagination-container\", 4, \"ngIf\"], [1, \"pagination-container\"], [\"aria-label\", \"Previous page\", 1, \"page-nav\", \"prev\", 3, \"click\"], [\"width\", \"20\", \"height\", \"20\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"d\", \"M15 18L9 12L15 6\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"page-numbers\"], [4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [\"aria-label\", \"Next page\", 1, \"page-nav\", \"next\", 3, \"click\"], [\"d\", \"M9 18L15 12L9 6\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"page-number\", 3, \"click\"], [\"class\", \"ellipsis\", 4, \"ngIf\"], [1, \"ellipsis\"]],\n      template: function PaginationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, PaginationComponent_div_0_Template, 11, 9, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.totalPages > 0);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf],\n      styles: [\".pagination-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n  padding: 16px 0;\\n  width: 100%;\\n}\\n\\n.page-nav[_ngcontent-%COMP%] {\\n  color: var(--pagination-arrow-color);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 0;\\n  background-color: transparent;\\n  border: none;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  font-size: 18px;\\n  font-weight: 500;\\n}\\n.page-nav[_ngcontent-%COMP%]:hover:not(.disabled) {\\n  color: var(--pagination-arrow-hover-color);\\n  background: transparent;\\n  border-color: transparent;\\n}\\n.page-nav[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n}\\n.page-nav.disabled[_ngcontent-%COMP%] {\\n  opacity: var(--pagination-disabled-opacity);\\n  cursor: not-allowed;\\n  pointer-events: none;\\n  color: var(--pagination-disabled-text);\\n}\\n.page-nav[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n.page-numbers[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.page-number[_ngcontent-%COMP%] {\\n  background-color: var(--pagination-button-bg);\\n  color: var(--pagination-button-text);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  border: none;\\n  cursor: pointer;\\n  font-size: 16px;\\n  font-weight: 500;\\n  transition: all 0.2s ease;\\n}\\n.page-number[_ngcontent-%COMP%]:hover:not(.active) {\\n  color: var(--pagination-button-text-hover);\\n  background: var(--pagination-button-bg-hover);\\n}\\n.page-number[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n}\\n.page-number.active[_ngcontent-%COMP%] {\\n  color: var(--pagination-active-text);\\n  background: var(--pagination-active-bg);\\n  border: none;\\n}\\n\\n.ellipsis[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 40px;\\n  height: 40px;\\n  font-size: 16px;\\n  font-weight: 500;\\n  color: var(--pagination-ellipsis);\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .pagination-container[_ngcontent-%COMP%] {\\n    padding: 12px 0;\\n  }\\n  .page-nav[_ngcontent-%COMP%], \\n   .page-number[_ngcontent-%COMP%] {\\n    width: 36px;\\n    height: 36px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .page-nav[_ngcontent-%COMP%], \\n   .page-number[_ngcontent-%COMP%] {\\n    width: 32px;\\n    height: 32px;\\n  }\\n  .page-numbers[_ngcontent-%COMP%] {\\n    gap: 4px;\\n  }\\n  .ellipsis[_ngcontent-%COMP%] {\\n    width: 20px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return PaginationComponent;\n})();", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelementContainerStart", "ɵɵlistener", "PaginationComponent_div_0_ng_container_5_Template_button_click_1_listener", "ɵɵrestoreView", "_r3", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "changePage", "pages", "ɵɵtemplate", "PaginationComponent_div_0_ng_container_5_span_3_Template", "ɵɵadvance", "ɵɵclassProp", "currentPage", "ɵɵtextInterpolate1", "ɵɵproperty", "showStartEllipsis", "PaginationComponent_div_0_ng_container_6_ng_container_1_Template_button_click_1_listener", "_r4", "page_r5", "$implicit", "PaginationComponent_div_0_ng_container_6_ng_container_1_Template", "i_r6", "length", "PaginationComponent_div_0_ng_container_7_span_1_Template", "PaginationComponent_div_0_ng_container_7_Template_button_click_2_listener", "_r7", "showEllipsis", "PaginationComponent_div_0_Template_button_click_1_listener", "_r1", "previousPage", "ɵɵelement", "PaginationComponent_div_0_ng_container_5_Template", "PaginationComponent_div_0_ng_container_6_Template", "PaginationComponent_div_0_ng_container_7_Template", "PaginationComponent_div_0_Template_button_click_8_listener", "nextPage", "totalPages", "PaginationComponent", "totalItems", "itemsPerPage", "visiblePageCount", "pageChange", "ngOnChanges", "changes", "calculatePages", "Math", "ceil", "Array", "from", "_", "i", "maxPagesBeforeCurrentPage", "floor", "maxPagesAfterCurrentPage", "startPage", "max", "endPage", "min", "unshift", "push", "page", "emit", "selectors", "inputs", "outputs", "features", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "PaginationComponent_Template", "rf", "ctx", "PaginationComponent_div_0_Template", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\components\\pagination\\pagination.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\components\\pagination\\pagination.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\n@Component({\r\n  selector: 'app-pagination',\r\n  standalone: true,\r\n  imports: [CommonModule],\r\n  templateUrl: './pagination.component.html',\r\n  styleUrls: ['./pagination.component.scss']\r\n})\r\nexport class PaginationComponent implements OnChanges {\r\n  @Input() totalItems: number = 0;\r\n  @Input() currentPage: number = 1;\r\n  @Input() itemsPerPage: number = 12;\r\n  @Input() visiblePageCount: number = 5;\r\n  @Output() pageChange = new EventEmitter<number>();\r\n  \r\n  totalPages: number = 0;\r\n  pages: number[] = [];\r\n  showEllipsis: boolean = false;\r\n  showStartEllipsis: boolean = false;\r\n\r\n  ngOnChanges(changes: SimpleChanges): void {\r\n    this.calculatePages();\r\n  }\r\n\r\n  calculatePages(): void {\r\n    this.totalPages = Math.ceil(this.totalItems / this.itemsPerPage);\r\n    \r\n    if (this.totalPages <= this.visiblePageCount) {\r\n      // If we have fewer pages than the visible count, show all pages\r\n      this.pages = Array.from({ length: this.totalPages }, (_, i) => i + 1);\r\n      this.showEllipsis = false;\r\n      this.showStartEllipsis = false;\r\n    } else {\r\n      const maxPagesBeforeCurrentPage = Math.floor((this.visiblePageCount - 1) / 2);\r\n      const maxPagesAfterCurrentPage = Math.ceil((this.visiblePageCount - 1) / 2);\r\n      \r\n      let startPage = Math.max(1, this.currentPage - maxPagesBeforeCurrentPage);\r\n      let endPage = Math.min(this.totalPages, startPage + this.visiblePageCount - 1);\r\n      \r\n      // Adjust if we're near the end of the page range\r\n      if (endPage >= this.totalPages) {\r\n        startPage = Math.max(1, this.totalPages - this.visiblePageCount + 1);\r\n        endPage = this.totalPages;\r\n      }\r\n      \r\n      // Generate page numbers\r\n      this.pages = Array.from(\r\n        { length: endPage - startPage + 1 }, \r\n        (_, i) => startPage + i\r\n      );\r\n      \r\n      // Add indicators for ellipses\r\n      this.showStartEllipsis = startPage > 1;\r\n      this.showEllipsis = endPage < this.totalPages;\r\n      \r\n      // Always include first and last page if needed\r\n      if (startPage > 1) {\r\n        this.pages.unshift(1);\r\n      }\r\n      \r\n      if (endPage < this.totalPages) {\r\n        this.pages.push(this.totalPages);\r\n      }\r\n    }\r\n  }\r\n\r\n  changePage(page: number): void {\r\n    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {\r\n      this.currentPage = page;\r\n      this.pageChange.emit(this.currentPage);\r\n      this.calculatePages();\r\n    }\r\n  }\r\n\r\n  previousPage(): void {\r\n    this.changePage(this.currentPage - 1);\r\n  }\r\n\r\n  nextPage(): void {\r\n    this.changePage(this.currentPage + 1);\r\n  }\r\n} ", "<div class=\"pagination-container\" *ngIf=\"totalPages > 0\">\r\n  <!-- Previous Page Button -->\r\n  <button \r\n    class=\"page-nav prev\" \r\n    [class.disabled]=\"currentPage === 1\"\r\n    (click)=\"previousPage()\"\r\n    [attr.aria-disabled]=\"currentPage === 1\"\r\n    aria-label=\"Previous page\"\r\n  >\r\n    <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n      <path d=\"M15 18L9 12L15 6\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n    </svg>\r\n  </button>\r\n  \r\n  <!-- Page Numbers -->\r\n  <div class=\"page-numbers\">\r\n    <!-- First page -->\r\n    <ng-container *ngIf=\"pages.length > 0\">\r\n      <button \r\n        class=\"page-number\" \r\n        [class.active]=\"pages[0] === currentPage\"\r\n        (click)=\"changePage(pages[0])\"\r\n        [attr.aria-current]=\"pages[0] === currentPage ? 'page' : null\"\r\n      >\r\n        {{ pages[0] }}\r\n      </button>\r\n      \r\n      <!-- Start ellipsis -->\r\n      <span class=\"ellipsis\" *ngIf=\"showStartEllipsis\">...</span>\r\n    </ng-container>\r\n    \r\n    <!-- Middle pages -->\r\n    <ng-container *ngFor=\"let page of pages; let i = index\">\r\n      <ng-container *ngIf=\"i > 0 && i < pages.length - 1\">\r\n        <button \r\n          class=\"page-number\" \r\n          [class.active]=\"page === currentPage\"\r\n          (click)=\"changePage(page)\"\r\n          [attr.aria-current]=\"page === currentPage ? 'page' : null\"\r\n        >\r\n          {{ page }}\r\n        </button>\r\n      </ng-container>\r\n    </ng-container>\r\n    \r\n    <!-- Last page -->\r\n    <ng-container *ngIf=\"pages.length > 1\">\r\n      <!-- End ellipsis -->\r\n      <span class=\"ellipsis\" *ngIf=\"showEllipsis\">...</span>\r\n      \r\n      <button \r\n        class=\"page-number\" \r\n        [class.active]=\"pages[pages.length-1] === currentPage\"\r\n        (click)=\"changePage(pages[pages.length-1])\"\r\n        [attr.aria-current]=\"pages[pages.length-1] === currentPage ? 'page' : null\"\r\n      >\r\n        {{ pages[pages.length-1] }}\r\n      </button>\r\n    </ng-container>\r\n  </div>\r\n  \r\n  <!-- Next Page Button -->\r\n  <button \r\n    class=\"page-nav next\" \r\n    [class.disabled]=\"currentPage === totalPages\"\r\n    (click)=\"nextPage()\"\r\n    [attr.aria-disabled]=\"currentPage === totalPages\"\r\n    aria-label=\"Next page\"\r\n  >\r\n    <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n      <path d=\"M9 18L15 12L9 6\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n    </svg>\r\n  </button>\r\n</div>"], "mappings": "AAAA,SAAoBA,YAAY,QAAiD,eAAe;AAChG,SAASC,YAAY,QAAQ,iBAAiB;;;;;IC2BxCC,EAAA,CAAAC,cAAA,eAAiD;IAAAD,EAAA,CAAAE,MAAA,UAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAX7DH,EAAA,CAAAI,uBAAA,GAAuC;IACrCJ,EAAA,CAAAC,cAAA,iBAKC;IAFCD,EAAA,CAAAK,UAAA,mBAAAC,0EAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,UAAA,CAAAH,MAAA,CAAAI,KAAA,CAAiB,CAAC,EAAE;IAAA,EAAC;IAG9Bb,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAGTH,EAAA,CAAAc,UAAA,IAAAC,wDAAA,mBAAiD;;;;;IAR/Cf,EAAA,CAAAgB,SAAA,EAAyC;IAAzChB,EAAA,CAAAiB,WAAA,WAAAR,MAAA,CAAAI,KAAA,QAAAJ,MAAA,CAAAS,WAAA,CAAyC;;IAIzClB,EAAA,CAAAgB,SAAA,EACF;IADEhB,EAAA,CAAAmB,kBAAA,MAAAV,MAAA,CAAAI,KAAA,SACF;IAGwBb,EAAA,CAAAgB,SAAA,EAAuB;IAAvBhB,EAAA,CAAAoB,UAAA,SAAAX,MAAA,CAAAY,iBAAA,CAAuB;;;;;;IAK/CrB,EAAA,CAAAI,uBAAA,GAAoD;IAClDJ,EAAA,CAAAC,cAAA,iBAKC;IAFCD,EAAA,CAAAK,UAAA,mBAAAiB,yFAAA;MAAAtB,EAAA,CAAAO,aAAA,CAAAgB,GAAA;MAAA,MAAAC,OAAA,GAAAxB,EAAA,CAAAU,aAAA,GAAAe,SAAA;MAAA,MAAAhB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,UAAA,CAAAY,OAAA,CAAgB;IAAA,EAAC;IAG1BxB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IALPH,EAAA,CAAAgB,SAAA,EAAqC;IAArChB,EAAA,CAAAiB,WAAA,WAAAO,OAAA,KAAAf,MAAA,CAAAS,WAAA,CAAqC;;IAIrClB,EAAA,CAAAgB,SAAA,EACF;IADEhB,EAAA,CAAAmB,kBAAA,MAAAK,OAAA,MACF;;;;;IATJxB,EAAA,CAAAI,uBAAA,GAAwD;IACtDJ,EAAA,CAAAc,UAAA,IAAAY,gEAAA,0BAAoD;;;;;;IAArC1B,EAAA,CAAAgB,SAAA,EAAmC;IAAnChB,EAAA,CAAAoB,UAAA,SAAAO,IAAA,QAAAA,IAAA,GAAAlB,MAAA,CAAAI,KAAA,CAAAe,MAAA,KAAmC;;;;;IAelD5B,EAAA,CAAAC,cAAA,eAA4C;IAAAD,EAAA,CAAAE,MAAA,UAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAFxDH,EAAA,CAAAI,uBAAA,GAAuC;IAErCJ,EAAA,CAAAc,UAAA,IAAAe,wDAAA,mBAA4C;IAE5C7B,EAAA,CAAAC,cAAA,iBAKC;IAFCD,EAAA,CAAAK,UAAA,mBAAAyB,0EAAA;MAAA9B,EAAA,CAAAO,aAAA,CAAAwB,GAAA;MAAA,MAAAtB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,UAAA,CAAAH,MAAA,CAAAI,KAAA,CAAAJ,MAAA,CAAAI,KAAA,CAAAe,MAAA,GAA8B,CAAC,EAAE;IAAA,EAAC;IAG3C5B,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IATeH,EAAA,CAAAgB,SAAA,EAAkB;IAAlBhB,EAAA,CAAAoB,UAAA,SAAAX,MAAA,CAAAuB,YAAA,CAAkB;IAIxChC,EAAA,CAAAgB,SAAA,EAAsD;IAAtDhB,EAAA,CAAAiB,WAAA,WAAAR,MAAA,CAAAI,KAAA,CAAAJ,MAAA,CAAAI,KAAA,CAAAe,MAAA,UAAAnB,MAAA,CAAAS,WAAA,CAAsD;;IAItDlB,EAAA,CAAAgB,SAAA,EACF;IADEhB,EAAA,CAAAmB,kBAAA,MAAAV,MAAA,CAAAI,KAAA,CAAAJ,MAAA,CAAAI,KAAA,CAAAe,MAAA,WACF;;;;;;IAvDJ5B,EAFF,CAAAC,cAAA,aAAyD,gBAQtD;IAHCD,EAAA,CAAAK,UAAA,mBAAA4B,2DAAA;MAAAjC,EAAA,CAAAO,aAAA,CAAA2B,GAAA;MAAA,MAAAzB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA0B,YAAA,EAAc;IAAA,EAAC;;IAIxBnC,EAAA,CAAAC,cAAA,aAA+F;IAC7FD,EAAA,CAAAoC,SAAA,cAAkH;IAEtHpC,EADE,CAAAG,YAAA,EAAM,EACC;;IAGTH,EAAA,CAAAC,cAAA,aAA0B;IA+BxBD,EA7BA,CAAAc,UAAA,IAAAuB,iDAAA,0BAAuC,IAAAC,iDAAA,0BAeiB,IAAAC,iDAAA,0BAcjB;IAazCvC,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,gBAMC;IAHCD,EAAA,CAAAK,UAAA,mBAAAmC,2DAAA;MAAAxC,EAAA,CAAAO,aAAA,CAAA2B,GAAA;MAAA,MAAAzB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAgC,QAAA,EAAU;IAAA,EAAC;;IAIpBzC,EAAA,CAAAC,cAAA,aAA+F;IAC7FD,EAAA,CAAAoC,SAAA,eAAiH;IAGvHpC,EAFI,CAAAG,YAAA,EAAM,EACC,EACL;;;;IArEFH,EAAA,CAAAgB,SAAA,EAAoC;IAApChB,EAAA,CAAAiB,WAAA,aAAAR,MAAA,CAAAS,WAAA,OAAoC;;IAarBlB,EAAA,CAAAgB,SAAA,GAAsB;IAAtBhB,EAAA,CAAAoB,UAAA,SAAAX,MAAA,CAAAI,KAAA,CAAAe,MAAA,KAAsB;IAeN5B,EAAA,CAAAgB,SAAA,EAAU;IAAVhB,EAAA,CAAAoB,UAAA,YAAAX,MAAA,CAAAI,KAAA,CAAU;IAc1Bb,EAAA,CAAAgB,SAAA,EAAsB;IAAtBhB,EAAA,CAAAoB,UAAA,SAAAX,MAAA,CAAAI,KAAA,CAAAe,MAAA,KAAsB;IAkBrC5B,EAAA,CAAAgB,SAAA,EAA6C;IAA7ChB,EAAA,CAAAiB,WAAA,aAAAR,MAAA,CAAAS,WAAA,KAAAT,MAAA,CAAAiC,UAAA,CAA6C;;;;ADtDjD,WAAaC,mBAAmB;EAA1B,MAAOA,mBAAmB;IACrBC,UAAU,GAAW,CAAC;IACtB1B,WAAW,GAAW,CAAC;IACvB2B,YAAY,GAAW,EAAE;IACzBC,gBAAgB,GAAW,CAAC;IAC3BC,UAAU,GAAG,IAAIjD,YAAY,EAAU;IAEjD4C,UAAU,GAAW,CAAC;IACtB7B,KAAK,GAAa,EAAE;IACpBmB,YAAY,GAAY,KAAK;IAC7BX,iBAAiB,GAAY,KAAK;IAElC2B,WAAWA,CAACC,OAAsB;MAChC,IAAI,CAACC,cAAc,EAAE;IACvB;IAEAA,cAAcA,CAAA;MACZ,IAAI,CAACR,UAAU,GAAGS,IAAI,CAACC,IAAI,CAAC,IAAI,CAACR,UAAU,GAAG,IAAI,CAACC,YAAY,CAAC;MAEhE,IAAI,IAAI,CAACH,UAAU,IAAI,IAAI,CAACI,gBAAgB,EAAE;QAC5C;QACA,IAAI,CAACjC,KAAK,GAAGwC,KAAK,CAACC,IAAI,CAAC;UAAE1B,MAAM,EAAE,IAAI,CAACc;QAAU,CAAE,EAAE,CAACa,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC;QACrE,IAAI,CAACxB,YAAY,GAAG,KAAK;QACzB,IAAI,CAACX,iBAAiB,GAAG,KAAK;MAChC,CAAC,MAAM;QACL,MAAMoC,yBAAyB,GAAGN,IAAI,CAACO,KAAK,CAAC,CAAC,IAAI,CAACZ,gBAAgB,GAAG,CAAC,IAAI,CAAC,CAAC;QAC7E,MAAMa,wBAAwB,GAAGR,IAAI,CAACC,IAAI,CAAC,CAAC,IAAI,CAACN,gBAAgB,GAAG,CAAC,IAAI,CAAC,CAAC;QAE3E,IAAIc,SAAS,GAAGT,IAAI,CAACU,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC3C,WAAW,GAAGuC,yBAAyB,CAAC;QACzE,IAAIK,OAAO,GAAGX,IAAI,CAACY,GAAG,CAAC,IAAI,CAACrB,UAAU,EAAEkB,SAAS,GAAG,IAAI,CAACd,gBAAgB,GAAG,CAAC,CAAC;QAE9E;QACA,IAAIgB,OAAO,IAAI,IAAI,CAACpB,UAAU,EAAE;UAC9BkB,SAAS,GAAGT,IAAI,CAACU,GAAG,CAAC,CAAC,EAAE,IAAI,CAACnB,UAAU,GAAG,IAAI,CAACI,gBAAgB,GAAG,CAAC,CAAC;UACpEgB,OAAO,GAAG,IAAI,CAACpB,UAAU;QAC3B;QAEA;QACA,IAAI,CAAC7B,KAAK,GAAGwC,KAAK,CAACC,IAAI,CACrB;UAAE1B,MAAM,EAAEkC,OAAO,GAAGF,SAAS,GAAG;QAAC,CAAE,EACnC,CAACL,CAAC,EAAEC,CAAC,KAAKI,SAAS,GAAGJ,CAAC,CACxB;QAED;QACA,IAAI,CAACnC,iBAAiB,GAAGuC,SAAS,GAAG,CAAC;QACtC,IAAI,CAAC5B,YAAY,GAAG8B,OAAO,GAAG,IAAI,CAACpB,UAAU;QAE7C;QACA,IAAIkB,SAAS,GAAG,CAAC,EAAE;UACjB,IAAI,CAAC/C,KAAK,CAACmD,OAAO,CAAC,CAAC,CAAC;QACvB;QAEA,IAAIF,OAAO,GAAG,IAAI,CAACpB,UAAU,EAAE;UAC7B,IAAI,CAAC7B,KAAK,CAACoD,IAAI,CAAC,IAAI,CAACvB,UAAU,CAAC;QAClC;MACF;IACF;IAEA9B,UAAUA,CAACsD,IAAY;MACrB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAACxB,UAAU,IAAIwB,IAAI,KAAK,IAAI,CAAChD,WAAW,EAAE;QACrE,IAAI,CAACA,WAAW,GAAGgD,IAAI;QACvB,IAAI,CAACnB,UAAU,CAACoB,IAAI,CAAC,IAAI,CAACjD,WAAW,CAAC;QACtC,IAAI,CAACgC,cAAc,EAAE;MACvB;IACF;IAEAf,YAAYA,CAAA;MACV,IAAI,CAACvB,UAAU,CAAC,IAAI,CAACM,WAAW,GAAG,CAAC,CAAC;IACvC;IAEAuB,QAAQA,CAAA;MACN,IAAI,CAAC7B,UAAU,CAAC,IAAI,CAACM,WAAW,GAAG,CAAC,CAAC;IACvC;;uCAxEWyB,mBAAmB;IAAA;;YAAnBA,mBAAmB;MAAAyB,SAAA;MAAAC,MAAA;QAAAzB,UAAA;QAAA1B,WAAA;QAAA2B,YAAA;QAAAC,gBAAA;MAAA;MAAAwB,OAAA;QAAAvB,UAAA;MAAA;MAAAwB,QAAA,GAAAvE,EAAA,CAAAwE,oBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVhC9E,EAAA,CAAAc,UAAA,IAAAkE,kCAAA,kBAAyD;;;UAAtBhF,EAAA,CAAAoB,UAAA,SAAA2D,GAAA,CAAArC,UAAA,KAAoB;;;qBDM3C3C,YAAY,EAAAkF,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA;MAAAC,MAAA;IAAA;;SAIXzC,mBAAmB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}