{"ast": null, "code": "import { switchMap } from './switchMap';\nimport { isFunction } from '../util/isFunction';\nexport function switchMapTo(innerObservable, resultSelector) {\n  return isFunction(resultSelector) ? switchMap(() => innerObservable, resultSelector) : switchMap(() => innerObservable);\n}", "map": {"version": 3, "names": ["switchMap", "isFunction", "switchMapTo", "innerObservable", "resultSelector"], "sources": ["C:/console/aava-ui-web/node_modules/rxjs/dist/esm/internal/operators/switchMapTo.js"], "sourcesContent": ["import { switchMap } from './switchMap';\nimport { isFunction } from '../util/isFunction';\nexport function switchMapTo(innerObservable, resultSelector) {\n    return isFunction(resultSelector) ? switchMap(() => innerObservable, resultSelector) : switchMap(() => innerObservable);\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,aAAa;AACvC,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,OAAO,SAASC,WAAWA,CAACC,eAAe,EAAEC,cAAc,EAAE;EACzD,OAAOH,UAAU,CAACG,cAAc,CAAC,GAAGJ,SAAS,CAAC,MAAMG,eAAe,EAAEC,cAAc,CAAC,GAAGJ,SAAS,CAAC,MAAMG,eAAe,CAAC;AAC3H", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}