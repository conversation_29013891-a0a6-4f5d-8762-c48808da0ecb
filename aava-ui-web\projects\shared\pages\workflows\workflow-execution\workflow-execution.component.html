<div class="workflow-execution-container">
  <!-- SVG Gradient Definitions for Icons -->
  <svg width="0" height="0" style="position: absolute">
    <defs>
      <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
        <stop offset="0%" stop-color="#6566CD" />
        <stop offset="100%" stop-color="#F96CAB" />
      </linearGradient>
    </defs>
  </svg>
  <div class="execution-content" role="main">
    <!-- Left Panel: Workflow Execution Panel -->
    <div
      class="column execution-panel-column"
      [class.collapsed]="isLeftPanelCollapsed"
      role="region"
      aria-label="Workflow Execution Panel"
    >
      <div class="column-content execution-panel-content">
        <div class="column-header">
          <div class="header-left">
            <ava-icon
              iconName="arrowLeft"
              iconColor="#1A46A7"
              class="back-icon"
              (click)="navigateBack()"
            ></ava-icon>
            <ava-icon
              iconName="panelLeft"
              iconColor="#1A46A7"
              class="panel-icon"
              (click)="toggleLeftPanel()"
            ></ava-icon>
          </div>
        </div>

        <div class="panel-content" [class.hidden]="isLeftPanelCollapsed">
          <!-- Stepper Section -->
          <div class="stepper-section">
            <ava-stepper
              [steps]="stepperSteps"
              [currentStep]="currentStepperStep"
              [iconColor]="'#ffff'"
              [iconSize]="'16'"
              orientation="vertical"
              size="small"
              [showNavigation]="false"
              (stepChange)="onStepperChange($event)">
            </ava-stepper>
          </div>

          <!-- Agent Cards -->
          <div class="agents-section">
            <ng-container *ngFor="let agent of pipelineAgents; let i = index">
              <div class="agent-card"
                   [class.completed]="agentStatuses[i]?.completed"
                   [class.active]="currentStepperStep === i"
                   [class.expanded]="expandedAgents[i]">
                <div class="agent-header" (click)="toggleAgentExpansion(i)">
                  <div class="status-icon">
                    <ava-icon
                      [iconName]="agentStatuses[i]?.completed ? 'check' : 'folder'"
                      [iconColor]="agentStatuses[i]?.completed ? '#22C55E' : '#1A46A7'"
                    ></ava-icon>
                  </div>
                  <div class="agent-info">
                    <h3 class="agent-name">{{ agent?.agent?.name || 'Agent ' + (i + 1) }}</h3>
                    <ava-icon
                      iconName="chevronDown"
                      iconColor="#6B7280"
                      class="expand-icon"
                      [class.expanded]="expandedAgents[i]"
                    ></ava-icon>
                  </div>
                </div>

                <!-- Agent Details (Expandable) -->
                <div class="agent-details" [class.expanded]="expandedAgents[i]">
                  <!-- Agent Model and Tools Info -->
                  <div class="agent-detail-item" *ngIf="agent?.agent?.llm?.modelDeploymentName">
                    <span class="detail-label">Model:</span>
                    <span class="detail-value">{{ agent.agent.llm.modelDeploymentName }}</span>
                  </div>
                  <div class="agent-detail-item" *ngIf="agent?.agent?.tools?.length || agent?.agent?.userTools?.length">
                    <span class="detail-label">Tools:</span>
                    <span class="detail-value">{{ getCombinedTools(agent.agent) }}</span>
                  </div>

                  <!-- Agent Input Fields -->
                  <div class="agent-inputs" *ngIf="getAgentInputs(i).length > 0">
                    <div class="inputs-container" [class.scrollable]="getAgentInputs(i).length > 2">
                      <ng-container *ngFor="let input of getAgentInputs(i); let inputIndex = index">
                        <div class="input-field"
                             [class.hidden]="inputIndex > 1 && !showAllInputs[i]"
                             [formGroup]="workflowForm">
                          <label class="input-label">{{ input.label }}</label>

                          <!-- Text Input -->
                          <div *ngIf="!isImageInput(input.input)" class="text-input-container">
                            <textarea
                              [formControlName]="input.input"
                              [placeholder]="'Enter ' + input.label"
                              class="input-textarea"
                              [disabled]="currentStepperStep !== i"
                            ></textarea>
                          </div>

                          <!-- Image Input -->
                          <div *ngIf="isImageInput(input.input)" class="image-input-container">
                            <div class="file-upload-area"
                                 [class.disabled]="currentStepperStep !== i"
                                 (click)="triggerFileInput(input.input)">
                              <ava-icon iconName="upload" iconColor="#6B7280"></ava-icon>
                              <span>Upload Image</span>
                            </div>
                            <input
                              type="file"
                              [id]="'file-' + input.input"
                              accept="image/*"
                              (change)="onImageSelected($event, input.input)"
                              style="display: none;">
                          </div>
                        </div>
                      </ng-container>

                      <!-- Show More/Less Toggle -->
                      <div class="show-more-toggle" *ngIf="getAgentInputs(i).length > 2">
                        <button type="button"
                                class="toggle-btn"
                                (click)="toggleShowAllInputs(i)">
                          {{ showAllInputs[i] ? 'Show Less' : 'Show More (' + (getAgentInputs(i).length - 2) + ' more)' }}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </ng-container>
          </div>

          <!-- Current Input Section -->
          <div class="current-input-section" *ngIf="getCurrentStepInputs().length > 0">
            <div class="input-header">
              <div class="status-icon">
                <ava-icon
                  iconName="edit"
                  iconColor="#1A46A7"
                ></ava-icon>
              </div>
              <h3 class="input-title">Enter input for {{ getCurrentAgentName() }}</h3>
            </div>

            <div class="current-input-container" [formGroup]="workflowForm">
              <ng-container *ngFor="let input of getCurrentStepInputs()">
                <div class="input-field">
                  <label class="input-label">{{ input.label }}</label>

                  <!-- Text Input -->
                  <div *ngIf="!isImageInput(input.input)" class="input-container">
                    <textarea
                      [formControlName]="input.input"
                      [placeholder]="'Enter ' + input.label"
                      class="input-textarea"
                      (keydown.enter)="onInputSubmit($event)"
                    ></textarea>
                    <div class="input-actions">
                      <ava-icon iconName="send" iconColor="#1A46A7" class="send-icon" (click)="onInputSubmit()"></ava-icon>
                    </div>
                  </div>

                  <!-- Image Input -->
                  <div *ngIf="isImageInput(input.input)" class="image-input-container">
                    <div class="file-upload-area" (click)="triggerFileInput(input.input)">
                      <ava-icon iconName="upload" iconColor="#6B7280"></ava-icon>
                      <span>Upload Image for {{ input.label }}</span>
                    </div>
                    <input
                      type="file"
                      [id]="'current-file-' + input.input"
                      accept="image/*"
                      (change)="onImageSelected($event, input.input)"
                      style="display: none;">
                  </div>
                </div>
              </ng-container>
            </div>
          </div>

          <!-- Info Message -->
          <div class="info-message">
            <p>Agent input needed in order to execute Workflow</p>
          </div>

          <!-- Execute Button -->
          <div class="execute-section">
            <ava-button
              label="▶ Execute Agent"
              variant="primary"
              size="large"
              [disabled]="!isCurrentStepValid()"
              (click)="executeCurrentStep()"
              [customStyles]="{
                'background': '#1A46A7',
                'width': '100%',
                'border-radius': '8px'
              }"
            ></ava-button>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Column: Agent Output -->
    <div class="column output-column" role="region" aria-label="Agent Output">
      <div class="column-content">
        <div class="column-header row">
          <div class="col-7">
            <ava-tabs
              [tabs]="navigationTabs"
              [activeTabId]="activeTabId"
              variant="button"
              buttonShape="pill"
              [showContentPanels]="false"
              (tabChange)="onTabChange($event)"
              ariaLabel="Pill navigation tabs"
            ></ava-tabs>
          </div>
          <div class="col-5 right-section-header">
            <ava-button
              label="Send for Approval"
              variant="primary"
              [customStyles]="{
                background:
                  'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
                '--button-effect-color': '33, 90, 214',
                'border-radius': '8px',
                'box-shadow': 'none',
              }"
              size="medium"
            >
            </ava-button>
            <!-- <ava-button
              label="Export"
              variant="secondary"
              size="medium"
              class="ms-2"
            >
            </ava-button> -->
          </div>
        </div>
        <!-- activity content -->
        <div *ngIf="selectedTab === 'Agent Activity'" style="height: 100%">
          <app-agent-activity
            [activityLogs]="workflowLogs"
            [executionDetails]="executionDetails"
            [progress]="progress"
            [isRunning]="isRunning"
            (saveLogs)="saveLogs()"
            (controlAction)="handleControlAction($event)"
            [status]="status"
            (onOutPutBtnClick)="onTabChange({id: 'nav-products', label: 'Agent Output'})"
          ></app-agent-activity>
        </div>
        <!-- Agent Output Component -->
        <div *ngIf="selectedTab === 'Agent Output'" style="height: 100%">
          <app-agent-output
            [outputs]="taskMessage"
            (export)="exportResults('output')"
          ></app-agent-output>
        </div>
      </div>
    </div>
  </div>
</div>
