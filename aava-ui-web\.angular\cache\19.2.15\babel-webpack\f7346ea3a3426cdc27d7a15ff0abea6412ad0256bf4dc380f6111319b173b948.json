{"ast": null, "code": "// These are typically used in conjunction with noevent to ensure that we can\n// preventDefault on the event.\nexport const nonpassive = {\n  passive: false\n};\nexport const nonpassivecapture = {\n  capture: true,\n  passive: false\n};\nexport function nopropagation(event) {\n  event.stopImmediatePropagation();\n}\nexport default function (event) {\n  event.preventDefault();\n  event.stopImmediatePropagation();\n}", "map": {"version": 3, "names": ["nonpassive", "passive", "nonpassivecapture", "capture", "nopropagation", "event", "stopImmediatePropagation", "preventDefault"], "sources": ["C:/console/aava-ui-web/node_modules/d3-drag/src/noevent.js"], "sourcesContent": ["// These are typically used in conjunction with noevent to ensure that we can\n// preventDefault on the event.\nexport const nonpassive = {passive: false};\nexport const nonpassivecapture = {capture: true, passive: false};\n\nexport function nopropagation(event) {\n  event.stopImmediatePropagation();\n}\n\nexport default function(event) {\n  event.preventDefault();\n  event.stopImmediatePropagation();\n}\n"], "mappings": "AAAA;AACA;AACA,OAAO,MAAMA,UAAU,GAAG;EAACC,OAAO,EAAE;AAAK,CAAC;AAC1C,OAAO,MAAMC,iBAAiB,GAAG;EAACC,OAAO,EAAE,IAAI;EAAEF,OAAO,EAAE;AAAK,CAAC;AAEhE,OAAO,SAASG,aAAaA,CAACC,KAAK,EAAE;EACnCA,KAAK,CAACC,wBAAwB,CAAC,CAAC;AAClC;AAEA,eAAe,UAASD,KAAK,EAAE;EAC7BA,KAAK,CAACE,cAAc,CAAC,CAAC;EACtBF,KAAK,CAACC,wBAAwB,CAAC,CAAC;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}