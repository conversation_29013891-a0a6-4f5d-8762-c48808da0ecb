{"ast": null, "code": "import { CommonModule, DatePipe } from '@angular/common';\nimport { PageFooterComponent } from '../../../shared/components/page-footer/page-footer.component';\nimport { AvaTextboxComponent, DropdownComponent, IconComponent, TextCardComponent, PopupComponent } from '@ava/play-comp-library';\nimport { LucideAngularModule } from 'lucide-angular';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { startWith, debounceTime, distinctUntilChanged, map } from 'rxjs';\nimport guardrailsLabels from './constants/guardrails-base.json';\nimport { ConsoleCardComponent } from \"../../../shared/components/console-card/console-card.component\";\nimport { TimeAgoPipe } from '../../../shared/pipes/time-ago.pipe';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../shared/services/pagination.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"../../../shared/services/guardrails.service\";\nimport * as i5 from \"@angular/forms\";\nfunction GuardrailsComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 16)(2, \"h5\", 17);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.grLabels.text);\n  }\n}\nfunction GuardrailsComponent_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"ava-console-card\", 18);\n    i0.ɵɵpipe(2, \"timeAgo\");\n    i0.ɵɵlistener(\"actionClick\", function GuardrailsComponent_ng_container_11_Template_ava_console_card_actionClick_1_listener($event) {\n      const guardrail_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onActionClick($event, guardrail_r3.id));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const guardrail_r3 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"title\", guardrail_r3 == null ? null : guardrail_r3.title)(\"description\", guardrail_r3 == null ? null : guardrail_r3.description)(\"author\", (guardrail_r3 == null ? null : guardrail_r3.owner) || \"AAVA\")(\"date\", i0.ɵɵpipeBind1(2, 6, guardrail_r3 == null ? null : guardrail_r3.createdDate))(\"actions\", ctx_r0.defaultActions)(\"skeleton\", ctx_r0.isLoading);\n  }\n}\nfunction GuardrailsComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 20)(2, \"app-page-footer\", 21);\n    i0.ɵɵlistener(\"pageChange\", function GuardrailsComponent_div_12_Template_app_page_footer_pageChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onPageChange($event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"totalItems\", ctx_r0.filteredGuardrails.length + 1)(\"currentPage\", ctx_r0.currentPage)(\"itemsPerPage\", ctx_r0.itemsPerPage);\n  }\n}\nexport let GuardrailsComponent = /*#__PURE__*/(() => {\n  class GuardrailsComponent {\n    paginationService;\n    router;\n    route;\n    datePipe;\n    guardrailsService;\n    fb;\n    cdr;\n    // Labels from constants file\n    grLabels = guardrailsLabels.labels;\n    searchForm;\n    search;\n    onSearchClick() {\n      throw new Error('Method not implemented.');\n    }\n    allGuardrails = [];\n    filteredGuardrails = [];\n    displayedGuardrails = [];\n    isLoading = false;\n    // Delete popup properties\n    showDeletePopup = false;\n    guardrailToDelete = null;\n    error = null;\n    currentPage = 1;\n    itemsPerPage = 12;\n    totalPages = 1;\n    guardrailsOptions = [{\n      name: 'All',\n      value: 'all'\n    }, {\n      name: 'Type A',\n      value: 'typeA'\n    }, {\n      name: 'Type B',\n      value: 'typeB'\n    }];\n    selectedData = null;\n    defaultActions = [{\n      id: 'edit',\n      icon: 'edit',\n      label: 'Edit item',\n      tooltip: 'Edit'\n    }, {\n      id: 'delete',\n      icon: 'trash',\n      label: 'Delete item',\n      tooltip: 'Delete'\n    }, {\n      id: 'run',\n      icon: 'play',\n      label: 'Run application',\n      tooltip: 'Run',\n      isPrimary: true\n    }];\n    showInfoPopup = false;\n    infoMessage = '';\n    message = \"\";\n    cardSkeletonPlaceholders = Array(11);\n    constructor(paginationService, router, route, datePipe, guardrailsService, fb, cdr) {\n      this.paginationService = paginationService;\n      this.router = router;\n      this.route = route;\n      this.datePipe = datePipe;\n      this.guardrailsService = guardrailsService;\n      this.fb = fb;\n      this.cdr = cdr;\n    }\n    ngOnInit() {\n      this.searchForm = this.fb.group({\n        search: ['']\n      });\n      this.filteredGuardrails = [...this.allGuardrails];\n      const pageParam = this.route.snapshot.queryParamMap.get('page');\n      if (pageParam) {\n        this.currentPage = parseInt(pageParam, 10);\n      }\n      this.filteredGuardrails = this.allGuardrails.map(item => {\n        const formattedDate = this.datePipe.transform(item.createdDate, 'MM/dd/yyyy') || '';\n        return {\n          ...item,\n          createdDate: formattedDate\n        };\n      });\n      // initialize the search listener ONCE\n      this.searchForm.get('search').valueChanges.pipe(startWith(''), debounceTime(300), distinctUntilChanged(), map(value => value?.toLowerCase() ?? '')).subscribe(searchText => {\n        this.filterGuardrails(searchText);\n      });\n      this.loadGuardrails();\n    }\n    loadGuardrails() {\n      this.isLoading = true;\n      this.guardrailsService.fetchAllGuardrails().subscribe(data => {\n        this.allGuardrails = data.map(item => ({\n          id: String(item.id),\n          title: item.name,\n          description: item.description || 'No description',\n          tags: [{\n            label: item.configKey,\n            type: 'primary'\n          }, {\n            label: `ChatBot: ${item.chatBot ? 'Yes' : 'No'}`,\n            type: 'secondary'\n          }],\n          actions: [{\n            action: 'execute',\n            icon: 'play_circle',\n            tooltip: 'Run guardrail'\n          }, {\n            action: 'clone',\n            icon: 'content_copy',\n            tooltip: 'Clone guardrail'\n          }, {\n            action: 'delete',\n            icon: 'delete',\n            tooltip: 'Delete guardrail'\n          }],\n          createdDate: new Date().toLocaleDateString() // Optional: Use actual created date if available\n        }));\n        // after data is loaded, filter based on current search text\n        const currentSearch = this.searchForm.get('search')?.value?.toLowerCase() || '';\n        this.filterGuardrails(currentSearch);\n        this.filteredGuardrails = [...this.allGuardrails];\n        this.updateDisplayedGuardrails();\n        this.isLoading = false;\n      });\n    }\n    filterGuardrails(searchText) {\n      this.filteredGuardrails = this.allGuardrails.filter(gr => {\n        const inTitle = gr.title?.toLowerCase().includes(searchText);\n        const inDescription = gr.description?.toLowerCase().includes(searchText);\n        const inTags = Array.isArray(gr.tags) && gr.tags?.some(tag => tag.label?.toLowerCase().includes(searchText));\n        return inTitle || inDescription || inTags;\n      });\n      this.updateDisplayedGuardrails();\n    }\n    updateDisplayedGuardrails() {\n      const result = this.paginationService.getPaginatedItems(this.filteredGuardrails, this.currentPage, this.itemsPerPage);\n      this.displayedGuardrails = result.displayedItems;\n      this.totalPages = result.totalPages;\n    }\n    onCreateGuardrail() {\n      this.router.navigate(['/libraries/guardrails/create']);\n    }\n    // onCardClicked(guardrailId: string): void {\n    //   this.router.navigate(['/libraries/guardrails/edit', guardrailId], {\n    //     queryParams: { returnPage: this.currentPage },\n    //   });\n    // }\n    getHeaderIcons(guardrail) {\n      return [{\n        iconName: 'swords',\n        title: guardrail.toolType || 'Guardrails'\n      }, {\n        iconName: 'users',\n        title: `${guardrail.userCount || 10}`\n      }];\n    }\n    getFooterIcons(guardrail) {\n      return [{\n        iconName: 'user',\n        title: guardrail.owner || 'AAVA'\n      }, {\n        iconName: 'calendar-days',\n        title: guardrail.createdDate\n      }];\n    }\n    onActionClick(event, guardrailId) {\n      switch (event.actionId) {\n        case 'edit':\n          this.router.navigate([`/libraries/guardrails/edit/${guardrailId}`]);\n          break;\n        case 'delete':\n          this.confirmDeleteGuardrail(guardrailId);\n          break;\n        case 'run':\n          this.router.navigate([`/libraries/guardrails/edit/${guardrailId}`]);\n          break;\n        case 'copy':\n          this.duplicateGuardrail(guardrailId);\n          break;\n        default:\n          break;\n      }\n    }\n    confirmDeleteGuardrail(guardrailId) {\n      this.guardrailToDelete = this.allGuardrails.find(item => item.id === guardrailId);\n      this.message = `Are you sure you want to delete ${this.guardrailToDelete.title} ?`;\n      this.showDeletePopup = true;\n    }\n    onConfirmDelete() {\n      if (this.guardrailToDelete) {\n        const successMessage = `Guardrail \"${this.guardrailToDelete.title}\" has been successfully deleted.`;\n        this.guardrailsService.deleteGuardrail(Number(this.guardrailToDelete.id)).subscribe({\n          next: () => {\n            console.log('Guardrail deleted');\n            this.closeDeletePopup();\n            setTimeout(() => {\n              this.showInfoPopup = true;\n              this.infoMessage = successMessage;\n              this.cdr.detectChanges();\n            }, 0);\n            this.loadGuardrails(); // Refresh list\n          },\n          error: err => {\n            console.error('Failed to delete guardrail:', err);\n            this.closeDeletePopup();\n          }\n        });\n      }\n    }\n    handleInfoPopupClose() {\n      this.showInfoPopup = false;\n    }\n    closeDeletePopup() {\n      this.showDeletePopup = false;\n      this.guardrailToDelete = null;\n    }\n    duplicateGuardrail(guardrailId) {\n      // Implement duplicate logic\n    }\n    getTagsLine(guardrail) {\n      if (!guardrail.tags || !Array.isArray(guardrail.tags)) return '';\n      return guardrail.tags.map(tag => tag.label?.trim()).filter(label => !!label).join(' | ');\n    }\n    onSelectionChange(data) {\n      this.selectedData = data;\n      // Implement filter logic if needed\n    }\n    onPageChange(page) {\n      this.currentPage = page;\n      this.updateDisplayedGuardrails();\n    }\n    get showCreateCard() {\n      return this.currentPage === 1 && !this.isLoading && !this.error;\n    }\n    static ɵfac = function GuardrailsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || GuardrailsComponent)(i0.ɵɵdirectiveInject(i1.PaginationService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.DatePipe), i0.ɵɵdirectiveInject(i4.GuardrailsService), i0.ɵɵdirectiveInject(i5.FormBuilder), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: GuardrailsComponent,\n      selectors: [[\"app-guardrails\"]],\n      features: [i0.ɵɵProvidersFeature([DatePipe])],\n      decls: 15,\n      vars: 26,\n      consts: [[\"id\", \"guardrails-container\", 1, \"container-fluid\"], [\"id\", \"search-filter-container\", 1, \"row\", \"g-3\"], [1, \"col-12\", \"col-md-8\", \"col-lg-9\", \"col-xl-10\", \"search-section\"], [3, \"formGroup\"], [\"hoverEffect\", \"glow\", \"pressedEffect\", \"solid\", \"formControlName\", \"search\", 3, \"placeholder\"], [\"slot\", \"icon-start\", \"iconName\", \"search\", \"iconColor\", \"var(--color-brand-primary)\", 3, \"iconSize\"], [1, \"col-12\", \"col-md-4\", \"col-lg-3\", \"col-xl-2\", \"action-buttons\"], [3, \"selectionChange\", \"dropdownTitle\", \"options\"], [\"id\", \"prompts-card-container\", 1, \"row\", \"g-3\"], [\"iconColor\", \"#144692\", 1, \"col-12\", \"col-sm-6\", \"col-md-4\", \"col-lg-3\", \"col-xl-3\", \"col-xxl-2\", \"mt-5\", 3, \"cardClick\", \"type\", \"iconName\", \"title\", \"isLoading\"], [\"class\", \"col-12 d-flex justify-content-center align-items-center py-5\", 4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"row\", 4, \"ngIf\"], [\"headerIconName\", \"trash\", \"iconColor\", \"#dc3545\", 3, \"confirm\", \"cancel\", \"closed\", \"show\", \"title\", \"message\", \"showHeaderIcon\", \"showClose\", \"showCancel\", \"showConfirm\", \"confirmButtonLabel\", \"confirmButtonVariant\", \"confirmButtonBackground\"], [\"messageAlignment\", \"center\", \"title\", \"SUCCESS!\", \"headerIconName\", \"circle-check\", \"iconColor\", \"green\", 3, \"closed\", \"show\", \"message\", \"showHeaderIcon\", \"showClose\"], [1, \"col-12\", \"d-flex\", \"justify-content-center\", \"align-items-center\", \"py-5\"], [1, \"text-center\"], [1, \"text-muted\"], [\"categoryIcon\", \"swords\", \"categoryTitle\", \"Guardrail\", \"categoryValue\", \"42\", 1, \"col-12\", \"col-sm-6\", \"col-md-4\", \"col-lg-3\", \"col-xl-3\", \"col-xxl-2\", \"mt-5\", 3, \"actionClick\", \"title\", \"description\", \"author\", \"date\", \"actions\", \"skeleton\"], [1, \"row\"], [1, \"col-12\", \"d-flex\", \"justify-content-center\", \"mt-4\"], [3, \"pageChange\", \"totalItems\", \"currentPage\", \"itemsPerPage\"]],\n      template: function GuardrailsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"form\", 3)(4, \"ava-textbox\", 4);\n          i0.ɵɵelement(5, \"ava-icon\", 5);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"ava-dropdown\", 7);\n          i0.ɵɵlistener(\"selectionChange\", function GuardrailsComponent_Template_ava_dropdown_selectionChange_7_listener($event) {\n            return ctx.onSelectionChange($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(8, \"div\", 8)(9, \"ava-text-card\", 9);\n          i0.ɵɵlistener(\"cardClick\", function GuardrailsComponent_Template_ava_text_card_cardClick_9_listener() {\n            return ctx.onCreateGuardrail();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(10, GuardrailsComponent_div_10_Template, 4, 1, \"div\", 10)(11, GuardrailsComponent_ng_container_11_Template, 3, 8, \"ng-container\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(12, GuardrailsComponent_div_12_Template, 3, 3, \"div\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"ava-popup\", 13);\n          i0.ɵɵlistener(\"confirm\", function GuardrailsComponent_Template_ava_popup_confirm_13_listener() {\n            return ctx.onConfirmDelete();\n          })(\"cancel\", function GuardrailsComponent_Template_ava_popup_cancel_13_listener() {\n            return ctx.closeDeletePopup();\n          })(\"closed\", function GuardrailsComponent_Template_ava_popup_closed_13_listener() {\n            return ctx.closeDeletePopup();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"ava-popup\", 14);\n          i0.ɵɵlistener(\"closed\", function GuardrailsComponent_Template_ava_popup_closed_14_listener() {\n            return ctx.handleInfoPopupClose();\n          });\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"formGroup\", ctx.searchForm);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"placeholder\", ctx.grLabels.searchPlaceholder);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"iconSize\", 16);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"dropdownTitle\", ctx.grLabels.dropdownTitle)(\"options\", ctx.guardrailsOptions);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"type\", \"create\")(\"iconName\", \"plus\")(\"title\", ctx.grLabels.title)(\"isLoading\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.filteredGuardrails.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.isLoading && ctx.displayedGuardrails.length === 0 ? ctx.cardSkeletonPlaceholders : ctx.displayedGuardrails);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.filteredGuardrails.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"show\", ctx.showDeletePopup)(\"title\", ctx.grLabels.deleteTitle)(\"message\", ctx.message)(\"showHeaderIcon\", true)(\"showClose\", true)(\"showCancel\", true)(\"showConfirm\", true)(\"confirmButtonLabel\", \"Delete\")(\"confirmButtonVariant\", \"primary\")(\"confirmButtonBackground\", \"#dc3545\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"show\", ctx.showInfoPopup)(\"message\", ctx.infoMessage)(\"showHeaderIcon\", true)(\"showClose\", true);\n        }\n      },\n      dependencies: [CommonModule, i3.NgForOf, i3.NgIf, ReactiveFormsModule, i5.ɵNgNoValidate, i5.NgControlStatus, i5.NgControlStatusGroup, i5.FormGroupDirective, i5.FormControlName, PageFooterComponent, TextCardComponent, AvaTextboxComponent, IconComponent, DropdownComponent, PopupComponent, LucideAngularModule, ConsoleCardComponent, TimeAgoPipe],\n      styles: [\".ava-dropdown {\\n  width: 100% !important;\\n}\\n\\n.mt-5[_ngcontent-%COMP%] {\\n  margin-top: 2rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3Byb2plY3RzL2NvbnNvbGUvc3JjL2FwcC9wYWdlcy9saWJyYXJpZXMvZ3VhcmRyYWlscy9ndWFyZHJhaWxzLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0Usc0JBQUE7QUFDRjs7QUFFQTtFQUNFLGdCQUFBO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyI6Om5nLWRlZXAgLmF2YS1kcm9wZG93biB7XHJcbiAgd2lkdGg6IDEwMCUgIWltcG9ydGFudDtcclxufVxyXG5cclxuLm10LTUge1xyXG4gIG1hcmdpbi10b3A6IDJyZW07XHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n  return GuardrailsComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "DatePipe", "PageFooterComponent", "AvaTextboxComponent", "DropdownComponent", "IconComponent", "TextCardComponent", "PopupComponent", "LucideAngularModule", "ReactiveFormsModule", "startWith", "debounceTime", "distinctUntilChanged", "map", "guardrailsLabels", "ConsoleCardComponent", "TimeAgoPipe", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "g<PERSON><PERSON><PERSON><PERSON>", "text", "ɵɵelementContainerStart", "ɵɵlistener", "GuardrailsComponent_ng_container_11_Template_ava_console_card_actionClick_1_listener", "$event", "guardrail_r3", "ɵɵrestoreView", "_r2", "$implicit", "ɵɵnextContext", "ɵɵresetView", "onActionClick", "id", "ɵɵproperty", "title", "description", "owner", "ɵɵpipeBind1", "createdDate", "defaultActions", "isLoading", "GuardrailsComponent_div_12_Template_app_page_footer_pageChange_2_listener", "_r4", "onPageChange", "filteredGuardrails", "length", "currentPage", "itemsPerPage", "GuardrailsComponent", "paginationService", "router", "route", "datePipe", "guardrailsService", "fb", "cdr", "labels", "searchForm", "search", "onSearchClick", "Error", "allGuardrails", "displayedGuardrails", "showDeletePopup", "guardrailToDelete", "error", "totalPages", "guardrailsOptions", "name", "value", "selectedData", "icon", "label", "tooltip", "isPrimary", "showInfoPopup", "infoMessage", "message", "cardSkeletonPlaceholders", "Array", "constructor", "ngOnInit", "group", "pageParam", "snapshot", "queryParamMap", "get", "parseInt", "item", "formattedDate", "transform", "valueChanges", "pipe", "toLowerCase", "subscribe", "searchText", "filterGuardrails", "loadGuardrails", "fetchAllGuardrails", "data", "String", "tags", "config<PERSON><PERSON>", "type", "chatBot", "actions", "action", "Date", "toLocaleDateString", "currentSearch", "updateDisplayedGuardrails", "filter", "gr", "inTitle", "includes", "inDescription", "inTags", "isArray", "some", "tag", "result", "getPaginatedItems", "displayedItems", "onCreateGuardrail", "navigate", "getHeaderIcons", "guardrail", "iconName", "toolType", "userCount", "getFooterIcons", "event", "guardrailId", "actionId", "confirmDeleteGuardrail", "duplicateGuardrail", "find", "onConfirmDelete", "successMessage", "deleteGuardrail", "Number", "next", "console", "log", "closeDeletePopup", "setTimeout", "detectChanges", "err", "handleInfoPopupClose", "getTagsLine", "trim", "join", "onSelectionChange", "page", "showCreateCard", "ɵɵdirectiveInject", "i1", "PaginationService", "i2", "Router", "ActivatedRoute", "i3", "i4", "GuardrailsService", "i5", "FormBuilder", "ChangeDetectorRef", "selectors", "features", "ɵɵProvidersFeature", "decls", "vars", "consts", "template", "GuardrailsComponent_Template", "rf", "ctx", "ɵɵelement", "GuardrailsComponent_Template_ava_dropdown_selectionChange_7_listener", "GuardrailsComponent_Template_ava_text_card_cardClick_9_listener", "ɵɵtemplate", "GuardrailsComponent_div_10_Template", "GuardrailsComponent_ng_container_11_Template", "GuardrailsComponent_div_12_Template", "GuardrailsComponent_Template_ava_popup_confirm_13_listener", "GuardrailsComponent_Template_ava_popup_cancel_13_listener", "GuardrailsComponent_Template_ava_popup_closed_13_listener", "GuardrailsComponent_Template_ava_popup_closed_14_listener", "searchPlaceholder", "dropdownTitle", "deleteTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "ɵNgNoValidate", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\libraries\\guardrails\\guardrails.component.ts", "C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\libraries\\guardrails\\guardrails.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, OnInit } from '@angular/core';\r\nimport { CommonModule, DatePipe } from '@angular/common';\r\nimport { Router, ActivatedRoute } from '@angular/router';\r\nimport { PageFooterComponent } from '../../../shared/components/page-footer/page-footer.component';\r\nimport { PaginationService } from '../../../shared/services/pagination.service';\r\nimport {\r\n  AvaTextboxComponent,\r\n  DropdownComponent,\r\n  DropdownOption,\r\n  IconComponent,\r\n  TextCardComponent,\r\n  PopupComponent,\r\n} from '@ava/play-comp-library';\r\nimport { LucideAngularModule } from 'lucide-angular';\r\nimport { Guardrail } from '../../../shared/models/card.model';\r\nimport { GuardrailsService } from '../../../shared/services/guardrails.service';\r\nimport { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';\r\nimport { startWith, debounceTime, distinctUntilChanged, map } from 'rxjs';\r\nimport guardrailsLabels from './constants/guardrails-base.json';\r\nimport { ConsoleCardAction, ConsoleCardComponent } from \"../../../shared/components/console-card/console-card.component\";\r\nimport { TimeAgoPipe } from '../../../shared/pipes/time-ago.pipe';\r\n\r\n@Component({\r\n  selector: 'app-guardrails',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    ReactiveFormsModule,\r\n    PageFooterComponent,\r\n    TextCardComponent,\r\n    AvaTextboxComponent,\r\n    IconComponent,\r\n    DropdownComponent,\r\n    PopupComponent,\r\n    LucideAngularModule,\r\n    ConsoleCardComponent,\r\n    TimeAgoPipe,\r\n],\r\n  providers: [DatePipe],\r\n  templateUrl: './guardrails.component.html',\r\n  styleUrl: './guardrails.component.scss',\r\n})\r\nexport class GuardrailsComponent implements OnInit {\r\n  // Labels from constants file\r\n  grLabels = guardrailsLabels.labels;\r\n\r\n  searchForm!: FormGroup;\r\n  search: any;\r\n  onSearchClick() {\r\n    throw new Error('Method not implemented.');\r\n  }\r\n  allGuardrails: any = [];\r\n  filteredGuardrails: any[] = [];\r\n  displayedGuardrails: any[] = [];\r\n  isLoading: boolean = false;\r\n\r\n  // Delete popup properties\r\n  showDeletePopup: boolean = false;\r\n  guardrailToDelete: any = null;\r\n  error: string | null = null;\r\n  currentPage: number = 1;\r\n  itemsPerPage: number = 12;\r\n  totalPages: number = 1;\r\n  guardrailsOptions: DropdownOption[] = [\r\n    { name: 'All', value: 'all' },\r\n    { name: 'Type A', value: 'typeA' },\r\n    { name: 'Type B', value: 'typeB' },\r\n  ];\r\n  selectedData: any = null;\r\ndefaultActions: ConsoleCardAction[] = [\r\n    {\r\n      id: 'edit',\r\n      icon: 'edit',\r\n      label: 'Edit item',\r\n      tooltip: 'Edit',\r\n    },\r\n    {\r\n      id: 'delete',\r\n      icon: 'trash',\r\n      label: 'Delete item',\r\n      tooltip: 'Delete',\r\n    },\r\n    {\r\n      id: 'run',\r\n      icon: 'play',\r\n      label: 'Run application',\r\n      tooltip: 'Run',\r\n      isPrimary: true,\r\n    },\r\n  ];\r\n  showInfoPopup: boolean = false;\r\n  infoMessage: string = '';\r\n  message: string =\"\";\r\n  cardSkeletonPlaceholders = Array(11);\r\n\r\n  constructor(\r\n    private paginationService: PaginationService,\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private datePipe: DatePipe,\r\n    private guardrailsService: GuardrailsService,\r\n    private fb: FormBuilder,\r\n    private cdr:ChangeDetectorRef\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.searchForm = this.fb.group({\r\n      search: [''],\r\n    });\r\n    this.filteredGuardrails = [...this.allGuardrails];\r\n    const pageParam = this.route.snapshot.queryParamMap.get('page');\r\n    if (pageParam) {\r\n      this.currentPage = parseInt(pageParam, 10);\r\n    }\r\n    this.filteredGuardrails = this.allGuardrails.map((item: any) => {\r\n      const formattedDate =\r\n        this.datePipe.transform(item.createdDate, 'MM/dd/yyyy') || '';\r\n      return {\r\n        ...item,\r\n        createdDate: formattedDate,\r\n      };\r\n    });\r\n\r\n    // initialize the search listener ONCE\r\n    this.searchForm\r\n      .get('search')!\r\n      .valueChanges.pipe(\r\n        startWith(''),\r\n        debounceTime(300),\r\n        distinctUntilChanged(),\r\n        map((value) => value?.toLowerCase() ?? ''),\r\n      )\r\n      .subscribe((searchText) => {\r\n        this.filterGuardrails(searchText);\r\n      });\r\n    \r\n    this.loadGuardrails();\r\n  }\r\n\r\n  loadGuardrails(): void {\r\n    this.isLoading= true;\r\n    this.guardrailsService\r\n      .fetchAllGuardrails()\r\n      .subscribe((data: Guardrail[]) => {\r\n        this.allGuardrails = data.map((item: Guardrail) => ({\r\n          id: String(item.id),\r\n          title: item.name,\r\n          description: item.description || 'No description',\r\n          tags: [\r\n            { label: item.configKey, type: 'primary' },\r\n            {\r\n              label: `ChatBot: ${item.chatBot ? 'Yes' : 'No'}`,\r\n              type: 'secondary',\r\n            },\r\n          ],\r\n          actions: [\r\n            {\r\n              action: 'execute',\r\n              icon: 'play_circle',\r\n              tooltip: 'Run guardrail',\r\n            },\r\n            {\r\n              action: 'clone',\r\n              icon: 'content_copy',\r\n              tooltip: 'Clone guardrail',\r\n            },\r\n            { action: 'delete', icon: 'delete', tooltip: 'Delete guardrail' },\r\n          ],\r\n          createdDate: new Date().toLocaleDateString(), // Optional: Use actual created date if available\r\n        }));\r\n\r\n        // after data is loaded, filter based on current search text\r\n        const currentSearch =\r\n          this.searchForm.get('search')?.value?.toLowerCase() || '';\r\n        this.filterGuardrails(currentSearch);\r\n\r\n        this.filteredGuardrails = [...this.allGuardrails];\r\n        this.updateDisplayedGuardrails();\r\n        this.isLoading= false;\r\n      });\r\n  }\r\n\r\n  filterGuardrails(searchText: string): void {\r\n    this.filteredGuardrails = this.allGuardrails.filter((gr: any) => {\r\n      const inTitle = gr.title?.toLowerCase().includes(searchText);\r\n      const inDescription = gr.description?.toLowerCase().includes(searchText);\r\n      const inTags =\r\n        Array.isArray(gr.tags) &&\r\n        gr.tags?.some((tag: any) =>\r\n          tag.label?.toLowerCase().includes(searchText),\r\n        );\r\n\r\n      return inTitle || inDescription || inTags;\r\n    });\r\n\r\n    this.updateDisplayedGuardrails();\r\n  }\r\n\r\n  updateDisplayedGuardrails(): void {\r\n    const result = this.paginationService.getPaginatedItems(\r\n      this.filteredGuardrails,\r\n      this.currentPage,\r\n      this.itemsPerPage,\r\n    );\r\n    this.displayedGuardrails = result.displayedItems;\r\n    this.totalPages = result.totalPages;\r\n  }\r\n\r\n  onCreateGuardrail(): void {\r\n    this.router.navigate(['/libraries/guardrails/create']);\r\n  }\r\n\r\n  // onCardClicked(guardrailId: string): void {\r\n  //   this.router.navigate(['/libraries/guardrails/edit', guardrailId], {\r\n  //     queryParams: { returnPage: this.currentPage },\r\n  //   });\r\n  // }\r\n\r\n  getHeaderIcons(guardrail: any): { iconName: string; title: string }[] {\r\n    return [\r\n      { iconName: 'swords', title: guardrail.toolType || 'Guardrails' },\r\n      { iconName: 'users', title: `${guardrail.userCount || 10}` },\r\n    ];\r\n  }\r\n\r\n  getFooterIcons(guardrail: any): { iconName: string; title: string }[] {\r\n    return [\r\n      { iconName: 'user', title: guardrail.owner || 'AAVA' },\r\n      { iconName: 'calendar-days', title: guardrail.createdDate },\r\n    ];\r\n  }\r\n\r\n  onActionClick(\r\n    event: { actionId: string; action: ConsoleCardAction },\r\n    guardrailId: string,\r\n  ): void {\r\n    switch (event.actionId) {\r\n      case 'edit':\r\n        this.router.navigate([`/libraries/guardrails/edit/${guardrailId}`]);\r\n        break;\r\n      case 'delete':\r\n        this.confirmDeleteGuardrail(guardrailId);\r\n        break;\r\n      case 'run':\r\n        this.router.navigate([`/libraries/guardrails/edit/${guardrailId}`]);\r\n        break;\r\n      case 'copy':\r\n        this.duplicateGuardrail(guardrailId);\r\n        break;\r\n      default:\r\n        break;\r\n    }\r\n  }\r\n\r\n  confirmDeleteGuardrail(guardrailId: string): void {\r\n    this.guardrailToDelete = this.allGuardrails.find(\r\n      (item: any) => item.id === guardrailId,\r\n    );\r\n    this.message =`Are you sure you want to delete ${this.guardrailToDelete.title} ?`\r\n    this.showDeletePopup = true;\r\n  }\r\n\r\n  onConfirmDelete(): void {\r\n    if (this.guardrailToDelete) {\r\n    const successMessage = `Guardrail \"${this.guardrailToDelete.title}\" has been successfully deleted.`;\r\n      this.guardrailsService\r\n        .deleteGuardrail(Number(this.guardrailToDelete.id))\r\n        .subscribe({\r\n          next: () => {\r\n            console.log('Guardrail deleted');\r\n            this.closeDeletePopup();\r\n         setTimeout(() => {\r\n          this.showInfoPopup = true;\r\n            this.infoMessage = successMessage;\r\n            this.cdr.detectChanges(); \r\n          }, 0);\r\n          this.loadGuardrails(); // Refresh list\r\n        },\r\n          error: (err) => {\r\n            console.error('Failed to delete guardrail:', err);\r\n            this.closeDeletePopup();\r\n          },\r\n        });\r\n    }\r\n  }\r\n  handleInfoPopupClose(): void {\r\n  this.showInfoPopup = false;\r\n}\r\n\r\n  closeDeletePopup(): void {\r\n    this.showDeletePopup = false;\r\n    this.guardrailToDelete = null;\r\n  }\r\n\r\n  duplicateGuardrail(guardrailId: string): void {\r\n    // Implement duplicate logic\r\n  }\r\n\r\n  getTagsLine(guardrail: any): string {\r\n    if (!guardrail.tags || !Array.isArray(guardrail.tags)) return '';\r\n    return guardrail.tags\r\n      .map((tag: any) => tag.label?.trim())\r\n      .filter((label: string | undefined) => !!label)\r\n      .join(' | ');\r\n  }\r\n\r\n  onSelectionChange(data: any) {\r\n    this.selectedData = data;\r\n    // Implement filter logic if needed\r\n  }\r\n\r\n  onPageChange(page: number): void {\r\n    this.currentPage = page;\r\n    this.updateDisplayedGuardrails();\r\n  }\r\n\r\n  get showCreateCard(): boolean {\r\n    return this.currentPage === 1 && !this.isLoading && !this.error;\r\n  }\r\n}\r\n", "<div id=\"guardrails-container\" class=\"container-fluid\">\r\n  <div id=\"search-filter-container\" class=\"row g-3\">\r\n    <div class=\"col-12 col-md-8 col-lg-9 col-xl-10 search-section\">\r\n      <form [formGroup]=\"searchForm\">\r\n        <ava-textbox\r\n          [placeholder]=\"grLabels.searchPlaceholder\"\r\n          hoverEffect=\"glow\"\r\n          pressedEffect=\"solid\"\r\n          formControlName=\"search\"\r\n        >\r\n          <ava-icon\r\n            slot=\"icon-start\"\r\n            iconName=\"search\"\r\n            [iconSize]=\"16\"\r\n            iconColor=\"var(--color-brand-primary)\"\r\n          >\r\n          </ava-icon>\r\n        </ava-textbox>\r\n      </form>\r\n    </div>\r\n    <div class=\"col-12 col-md-4 col-lg-3 col-xl-2 action-buttons\">\r\n      <ava-dropdown\r\n        [dropdownTitle]=\"grLabels.dropdownTitle\"\r\n        [options]=\"guardrailsOptions\"\r\n        (selectionChange)=\"onSelectionChange($event)\"\r\n      >\r\n      </ava-dropdown>\r\n    </div>\r\n  </div>\r\n\r\n  <div id=\"prompts-card-container\" class=\"row g-3\">\r\n    <ava-text-card\r\n      class=\"col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3 col-xxl-2 mt-5\"\r\n      [type]=\"'create'\"\r\n      [iconName]=\"'plus'\"\r\n      iconColor=\"#144692\"\r\n      [title]=\"grLabels.title\"\r\n      (cardClick)=\"onCreateGuardrail()\"\r\n      [isLoading]=\"isLoading\"\r\n    >\r\n    </ava-text-card>\r\n\r\n    <!-- No Results Message -->\r\n    <div\r\n      class=\"col-12 d-flex justify-content-center align-items-center py-5\"\r\n      *ngIf=\"!isLoading && filteredGuardrails.length === 0\"\r\n    >\r\n      <div class=\"text-center\">\r\n        <h5 class=\"text-muted\">{{grLabels.text}}</h5>\r\n      </div>\r\n    </div>\r\n\r\n    <ng-container *ngFor=\"let guardrail of isLoading && displayedGuardrails.length === 0 ? cardSkeletonPlaceholders : displayedGuardrails\">\r\n      <ava-console-card\r\n        class=\"col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3 col-xxl-2 mt-5\"\r\n        [title]=\"guardrail?.title\"\r\n        [description]=\"guardrail?.description\"\r\n        categoryIcon=\"swords\"\r\n        categoryTitle=\"Guardrail\"\r\n        categoryValue=\"42\"\r\n        [author]=\"guardrail?.owner || 'AAVA'\"\r\n        [date]=\"guardrail?.createdDate | timeAgo\"\r\n        [actions]=\"defaultActions\"\r\n        (actionClick)=\"onActionClick($event, guardrail.id)\"\r\n        [skeleton]=\"isLoading\"\r\n      >\r\n      </ava-console-card>\r\n    </ng-container>\r\n  </div>\r\n\r\n  <!-- Pagination Footer -->\r\n  <div class=\"row\" *ngIf=\"filteredGuardrails.length > 0\">\r\n    <div class=\"col-12 d-flex justify-content-center mt-4\">\r\n      <app-page-footer\r\n        [totalItems]=\"filteredGuardrails.length + 1\"\r\n        [currentPage]=\"currentPage\"\r\n        [itemsPerPage]=\"itemsPerPage\"\r\n        (pageChange)=\"onPageChange($event)\"\r\n      ></app-page-footer>\r\n    </div>\r\n  </div>\r\n</div>\r\n<ava-popup\r\n  [show]=\"showDeletePopup\"\r\n  [title]=\"grLabels.deleteTitle\"\r\n  [message]=\"message\"\r\n  [showHeaderIcon]=\"true\"\r\n  headerIconName=\"trash\"\r\n  iconColor=\"#dc3545\"\r\n  [showClose]=\"true\"\r\n  [showCancel]=\"true\"\r\n  [showConfirm]=\"true\"\r\n  [confirmButtonLabel]=\"'Delete'\"\r\n  [confirmButtonVariant]=\"'primary'\"\r\n  [confirmButtonBackground]=\"'#dc3545'\"\r\n  (confirm)=\"onConfirmDelete()\"\r\n  (cancel)=\"closeDeletePopup()\"\r\n  (closed)=\"closeDeletePopup()\"\r\n>\r\n</ava-popup>\r\n\r\n<ava-popup\r\n  messageAlignment=\"center\"\r\n  [show]=\"showInfoPopup\"\r\n  title=\"SUCCESS!\"\r\n  [message]=\"infoMessage\"\r\n  [showHeaderIcon]=\"true\"\r\n  headerIconName=\"circle-check\"\r\n  iconColor=\"green\"\r\n  [showClose]=\"true\"\r\n  (closed)=\"handleInfoPopupClose()\"\r\n></ava-popup>\r\n"], "mappings": "AACA,SAASA,YAAY,EAAEC,QAAQ,QAAQ,iBAAiB;AAExD,SAASC,mBAAmB,QAAQ,8DAA8D;AAElG,SACEC,mBAAmB,EACnBC,iBAAiB,EAEjBC,aAAa,EACbC,iBAAiB,EACjBC,cAAc,QACT,wBAAwB;AAC/B,SAASC,mBAAmB,QAAQ,gBAAgB;AAGpD,SAAiCC,mBAAmB,QAAQ,gBAAgB;AAC5E,SAASC,SAAS,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,GAAG,QAAQ,MAAM;AACzE,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,SAA4BC,oBAAoB,QAAQ,gEAAgE;AACxH,SAASC,WAAW,QAAQ,qCAAqC;;;;;;;;;IC4BzDC,EALJ,CAAAC,cAAA,cAGC,cAC0B,aACA;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAE5CF,EAF4C,CAAAG,YAAA,EAAK,EACzC,EACF;;;;IAFqBH,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,QAAA,CAAAC,IAAA,CAAiB;;;;;;IAI5CR,EAAA,CAAAS,uBAAA,GAAuI;IACrIT,EAAA,CAAAC,cAAA,2BAYC;;IAFCD,EAAA,CAAAU,UAAA,yBAAAC,qFAAAC,MAAA;MAAA,MAAAC,YAAA,GAAAb,EAAA,CAAAc,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAeZ,MAAA,CAAAa,aAAA,CAAAP,MAAA,EAAAC,YAAA,CAAAO,EAAA,CAAmC;IAAA,EAAC;IAGrDpB,EAAA,CAAAG,YAAA,EAAmB;;;;;;IAXjBH,EAAA,CAAAI,SAAA,EAA0B;IAS1BJ,EATA,CAAAqB,UAAA,UAAAR,YAAA,kBAAAA,YAAA,CAAAS,KAAA,CAA0B,gBAAAT,YAAA,kBAAAA,YAAA,CAAAU,WAAA,CACY,YAAAV,YAAA,kBAAAA,YAAA,CAAAW,KAAA,YAID,SAAAxB,EAAA,CAAAyB,WAAA,OAAAZ,YAAA,kBAAAA,YAAA,CAAAa,WAAA,EACI,YAAApB,MAAA,CAAAqB,cAAA,CACf,aAAArB,MAAA,CAAAsB,SAAA,CAEJ;;;;;;IASxB5B,EAFJ,CAAAC,cAAA,cAAuD,cACE,0BAMpD;IADCD,EAAA,CAAAU,UAAA,wBAAAmB,0EAAAjB,MAAA;MAAAZ,EAAA,CAAAc,aAAA,CAAAgB,GAAA;MAAA,MAAAxB,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAcZ,MAAA,CAAAyB,YAAA,CAAAnB,MAAA,CAAoB;IAAA,EAAC;IAGzCZ,EAFK,CAAAG,YAAA,EAAkB,EACf,EACF;;;;IANAH,EAAA,CAAAI,SAAA,GAA4C;IAE5CJ,EAFA,CAAAqB,UAAA,eAAAf,MAAA,CAAA0B,kBAAA,CAAAC,MAAA,KAA4C,gBAAA3B,MAAA,CAAA4B,WAAA,CACjB,iBAAA5B,MAAA,CAAA6B,YAAA,CACE;;;ADlCrC,WAAaC,mBAAmB;EAA1B,MAAOA,mBAAmB;IAsDpBC,iBAAA;IACAC,MAAA;IACAC,KAAA;IACAC,QAAA;IACAC,iBAAA;IACAC,EAAA;IACAC,GAAA;IA3DV;IACApC,QAAQ,GAAGV,gBAAgB,CAAC+C,MAAM;IAElCC,UAAU;IACVC,MAAM;IACNC,aAAaA,CAAA;MACX,MAAM,IAAIC,KAAK,CAAC,yBAAyB,CAAC;IAC5C;IACAC,aAAa,GAAQ,EAAE;IACvBjB,kBAAkB,GAAU,EAAE;IAC9BkB,mBAAmB,GAAU,EAAE;IAC/BtB,SAAS,GAAY,KAAK;IAE1B;IACAuB,eAAe,GAAY,KAAK;IAChCC,iBAAiB,GAAQ,IAAI;IAC7BC,KAAK,GAAkB,IAAI;IAC3BnB,WAAW,GAAW,CAAC;IACvBC,YAAY,GAAW,EAAE;IACzBmB,UAAU,GAAW,CAAC;IACtBC,iBAAiB,GAAqB,CACpC;MAAEC,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAK,CAAE,EAC7B;MAAED,IAAI,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAO,CAAE,EAClC;MAAED,IAAI,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAO,CAAE,CACnC;IACDC,YAAY,GAAQ,IAAI;IAC1B/B,cAAc,GAAwB,CAClC;MACEP,EAAE,EAAE,MAAM;MACVuC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,WAAW;MAClBC,OAAO,EAAE;KACV,EACD;MACEzC,EAAE,EAAE,QAAQ;MACZuC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,aAAa;MACpBC,OAAO,EAAE;KACV,EACD;MACEzC,EAAE,EAAE,KAAK;MACTuC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,iBAAiB;MACxBC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE;KACZ,CACF;IACDC,aAAa,GAAY,KAAK;IAC9BC,WAAW,GAAW,EAAE;IACxBC,OAAO,GAAU,EAAE;IACnBC,wBAAwB,GAAGC,KAAK,CAAC,EAAE,CAAC;IAEpCC,YACU/B,iBAAoC,EACpCC,MAAc,EACdC,KAAqB,EACrBC,QAAkB,EAClBC,iBAAoC,EACpCC,EAAe,EACfC,GAAqB;MANrB,KAAAN,iBAAiB,GAAjBA,iBAAiB;MACjB,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,KAAK,GAALA,KAAK;MACL,KAAAC,QAAQ,GAARA,QAAQ;MACR,KAAAC,iBAAiB,GAAjBA,iBAAiB;MACjB,KAAAC,EAAE,GAAFA,EAAE;MACF,KAAAC,GAAG,GAAHA,GAAG;IACV;IAEH0B,QAAQA,CAAA;MACN,IAAI,CAACxB,UAAU,GAAG,IAAI,CAACH,EAAE,CAAC4B,KAAK,CAAC;QAC9BxB,MAAM,EAAE,CAAC,EAAE;OACZ,CAAC;MACF,IAAI,CAACd,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACiB,aAAa,CAAC;MACjD,MAAMsB,SAAS,GAAG,IAAI,CAAChC,KAAK,CAACiC,QAAQ,CAACC,aAAa,CAACC,GAAG,CAAC,MAAM,CAAC;MAC/D,IAAIH,SAAS,EAAE;QACb,IAAI,CAACrC,WAAW,GAAGyC,QAAQ,CAACJ,SAAS,EAAE,EAAE,CAAC;MAC5C;MACA,IAAI,CAACvC,kBAAkB,GAAG,IAAI,CAACiB,aAAa,CAACrD,GAAG,CAAEgF,IAAS,IAAI;QAC7D,MAAMC,aAAa,GACjB,IAAI,CAACrC,QAAQ,CAACsC,SAAS,CAACF,IAAI,CAAClD,WAAW,EAAE,YAAY,CAAC,IAAI,EAAE;QAC/D,OAAO;UACL,GAAGkD,IAAI;UACPlD,WAAW,EAAEmD;SACd;MACH,CAAC,CAAC;MAEF;MACA,IAAI,CAAChC,UAAU,CACZ6B,GAAG,CAAC,QAAQ,CAAE,CACdK,YAAY,CAACC,IAAI,CAChBvF,SAAS,CAAC,EAAE,CAAC,EACbC,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,GAAG,CAAE6D,KAAK,IAAKA,KAAK,EAAEwB,WAAW,EAAE,IAAI,EAAE,CAAC,CAC3C,CACAC,SAAS,CAAEC,UAAU,IAAI;QACxB,IAAI,CAACC,gBAAgB,CAACD,UAAU,CAAC;MACnC,CAAC,CAAC;MAEJ,IAAI,CAACE,cAAc,EAAE;IACvB;IAEAA,cAAcA,CAAA;MACZ,IAAI,CAACzD,SAAS,GAAE,IAAI;MACpB,IAAI,CAACa,iBAAiB,CACnB6C,kBAAkB,EAAE,CACpBJ,SAAS,CAAEK,IAAiB,IAAI;QAC/B,IAAI,CAACtC,aAAa,GAAGsC,IAAI,CAAC3F,GAAG,CAAEgF,IAAe,KAAM;UAClDxD,EAAE,EAAEoE,MAAM,CAACZ,IAAI,CAACxD,EAAE,CAAC;UACnBE,KAAK,EAAEsD,IAAI,CAACpB,IAAI;UAChBjC,WAAW,EAAEqD,IAAI,CAACrD,WAAW,IAAI,gBAAgB;UACjDkE,IAAI,EAAE,CACJ;YAAE7B,KAAK,EAAEgB,IAAI,CAACc,SAAS;YAAEC,IAAI,EAAE;UAAS,CAAE,EAC1C;YACE/B,KAAK,EAAE,YAAYgB,IAAI,CAACgB,OAAO,GAAG,KAAK,GAAG,IAAI,EAAE;YAChDD,IAAI,EAAE;WACP,CACF;UACDE,OAAO,EAAE,CACP;YACEC,MAAM,EAAE,SAAS;YACjBnC,IAAI,EAAE,aAAa;YACnBE,OAAO,EAAE;WACV,EACD;YACEiC,MAAM,EAAE,OAAO;YACfnC,IAAI,EAAE,cAAc;YACpBE,OAAO,EAAE;WACV,EACD;YAAEiC,MAAM,EAAE,QAAQ;YAAEnC,IAAI,EAAE,QAAQ;YAAEE,OAAO,EAAE;UAAkB,CAAE,CAClE;UACDnC,WAAW,EAAE,IAAIqE,IAAI,EAAE,CAACC,kBAAkB,EAAE,CAAE;SAC/C,CAAC,CAAC;QAEH;QACA,MAAMC,aAAa,GACjB,IAAI,CAACpD,UAAU,CAAC6B,GAAG,CAAC,QAAQ,CAAC,EAAEjB,KAAK,EAAEwB,WAAW,EAAE,IAAI,EAAE;QAC3D,IAAI,CAACG,gBAAgB,CAACa,aAAa,CAAC;QAEpC,IAAI,CAACjE,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACiB,aAAa,CAAC;QACjD,IAAI,CAACiD,yBAAyB,EAAE;QAChC,IAAI,CAACtE,SAAS,GAAE,KAAK;MACvB,CAAC,CAAC;IACN;IAEAwD,gBAAgBA,CAACD,UAAkB;MACjC,IAAI,CAACnD,kBAAkB,GAAG,IAAI,CAACiB,aAAa,CAACkD,MAAM,CAAEC,EAAO,IAAI;QAC9D,MAAMC,OAAO,GAAGD,EAAE,CAAC9E,KAAK,EAAE2D,WAAW,EAAE,CAACqB,QAAQ,CAACnB,UAAU,CAAC;QAC5D,MAAMoB,aAAa,GAAGH,EAAE,CAAC7E,WAAW,EAAE0D,WAAW,EAAE,CAACqB,QAAQ,CAACnB,UAAU,CAAC;QACxE,MAAMqB,MAAM,GACVrC,KAAK,CAACsC,OAAO,CAACL,EAAE,CAACX,IAAI,CAAC,IACtBW,EAAE,CAACX,IAAI,EAAEiB,IAAI,CAAEC,GAAQ,IACrBA,GAAG,CAAC/C,KAAK,EAAEqB,WAAW,EAAE,CAACqB,QAAQ,CAACnB,UAAU,CAAC,CAC9C;QAEH,OAAOkB,OAAO,IAAIE,aAAa,IAAIC,MAAM;MAC3C,CAAC,CAAC;MAEF,IAAI,CAACN,yBAAyB,EAAE;IAClC;IAEAA,yBAAyBA,CAAA;MACvB,MAAMU,MAAM,GAAG,IAAI,CAACvE,iBAAiB,CAACwE,iBAAiB,CACrD,IAAI,CAAC7E,kBAAkB,EACvB,IAAI,CAACE,WAAW,EAChB,IAAI,CAACC,YAAY,CAClB;MACD,IAAI,CAACe,mBAAmB,GAAG0D,MAAM,CAACE,cAAc;MAChD,IAAI,CAACxD,UAAU,GAAGsD,MAAM,CAACtD,UAAU;IACrC;IAEAyD,iBAAiBA,CAAA;MACf,IAAI,CAACzE,MAAM,CAAC0E,QAAQ,CAAC,CAAC,8BAA8B,CAAC,CAAC;IACxD;IAEA;IACA;IACA;IACA;IACA;IAEAC,cAAcA,CAACC,SAAc;MAC3B,OAAO,CACL;QAAEC,QAAQ,EAAE,QAAQ;QAAE7F,KAAK,EAAE4F,SAAS,CAACE,QAAQ,IAAI;MAAY,CAAE,EACjE;QAAED,QAAQ,EAAE,OAAO;QAAE7F,KAAK,EAAE,GAAG4F,SAAS,CAACG,SAAS,IAAI,EAAE;MAAE,CAAE,CAC7D;IACH;IAEAC,cAAcA,CAACJ,SAAc;MAC3B,OAAO,CACL;QAAEC,QAAQ,EAAE,MAAM;QAAE7F,KAAK,EAAE4F,SAAS,CAAC1F,KAAK,IAAI;MAAM,CAAE,EACtD;QAAE2F,QAAQ,EAAE,eAAe;QAAE7F,KAAK,EAAE4F,SAAS,CAACxF;MAAW,CAAE,CAC5D;IACH;IAEAP,aAAaA,CACXoG,KAAsD,EACtDC,WAAmB;MAEnB,QAAQD,KAAK,CAACE,QAAQ;QACpB,KAAK,MAAM;UACT,IAAI,CAACnF,MAAM,CAAC0E,QAAQ,CAAC,CAAC,8BAA8BQ,WAAW,EAAE,CAAC,CAAC;UACnE;QACF,KAAK,QAAQ;UACX,IAAI,CAACE,sBAAsB,CAACF,WAAW,CAAC;UACxC;QACF,KAAK,KAAK;UACR,IAAI,CAAClF,MAAM,CAAC0E,QAAQ,CAAC,CAAC,8BAA8BQ,WAAW,EAAE,CAAC,CAAC;UACnE;QACF,KAAK,MAAM;UACT,IAAI,CAACG,kBAAkB,CAACH,WAAW,CAAC;UACpC;QACF;UACE;MACJ;IACF;IAEAE,sBAAsBA,CAACF,WAAmB;MACxC,IAAI,CAACpE,iBAAiB,GAAG,IAAI,CAACH,aAAa,CAAC2E,IAAI,CAC7ChD,IAAS,IAAKA,IAAI,CAACxD,EAAE,KAAKoG,WAAW,CACvC;MACD,IAAI,CAACvD,OAAO,GAAE,mCAAmC,IAAI,CAACb,iBAAiB,CAAC9B,KAAK,IAAI;MACjF,IAAI,CAAC6B,eAAe,GAAG,IAAI;IAC7B;IAEA0E,eAAeA,CAAA;MACb,IAAI,IAAI,CAACzE,iBAAiB,EAAE;QAC5B,MAAM0E,cAAc,GAAG,cAAc,IAAI,CAAC1E,iBAAiB,CAAC9B,KAAK,kCAAkC;QACjG,IAAI,CAACmB,iBAAiB,CACnBsF,eAAe,CAACC,MAAM,CAAC,IAAI,CAAC5E,iBAAiB,CAAChC,EAAE,CAAC,CAAC,CAClD8D,SAAS,CAAC;UACT+C,IAAI,EAAEA,CAAA,KAAK;YACTC,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;YAChC,IAAI,CAACC,gBAAgB,EAAE;YAC1BC,UAAU,CAAC,MAAK;cACf,IAAI,CAACtE,aAAa,GAAG,IAAI;cACvB,IAAI,CAACC,WAAW,GAAG8D,cAAc;cACjC,IAAI,CAACnF,GAAG,CAAC2F,aAAa,EAAE;YAC1B,CAAC,EAAE,CAAC,CAAC;YACL,IAAI,CAACjD,cAAc,EAAE,CAAC,CAAC;UACzB,CAAC;UACChC,KAAK,EAAGkF,GAAG,IAAI;YACbL,OAAO,CAAC7E,KAAK,CAAC,6BAA6B,EAAEkF,GAAG,CAAC;YACjD,IAAI,CAACH,gBAAgB,EAAE;UACzB;SACD,CAAC;MACN;IACF;IACAI,oBAAoBA,CAAA;MACpB,IAAI,CAACzE,aAAa,GAAG,KAAK;IAC5B;IAEEqE,gBAAgBA,CAAA;MACd,IAAI,CAACjF,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACC,iBAAiB,GAAG,IAAI;IAC/B;IAEAuE,kBAAkBA,CAACH,WAAmB;MACpC;IAAA;IAGFiB,WAAWA,CAACvB,SAAc;MACxB,IAAI,CAACA,SAAS,CAACzB,IAAI,IAAI,CAACtB,KAAK,CAACsC,OAAO,CAACS,SAAS,CAACzB,IAAI,CAAC,EAAE,OAAO,EAAE;MAChE,OAAOyB,SAAS,CAACzB,IAAI,CAClB7F,GAAG,CAAE+G,GAAQ,IAAKA,GAAG,CAAC/C,KAAK,EAAE8E,IAAI,EAAE,CAAC,CACpCvC,MAAM,CAAEvC,KAAyB,IAAK,CAAC,CAACA,KAAK,CAAC,CAC9C+E,IAAI,CAAC,KAAK,CAAC;IAChB;IAEAC,iBAAiBA,CAACrD,IAAS;MACzB,IAAI,CAAC7B,YAAY,GAAG6B,IAAI;MACxB;IACF;IAEAxD,YAAYA,CAAC8G,IAAY;MACvB,IAAI,CAAC3G,WAAW,GAAG2G,IAAI;MACvB,IAAI,CAAC3C,yBAAyB,EAAE;IAClC;IAEA,IAAI4C,cAAcA,CAAA;MAChB,OAAO,IAAI,CAAC5G,WAAW,KAAK,CAAC,IAAI,CAAC,IAAI,CAACN,SAAS,IAAI,CAAC,IAAI,CAACyB,KAAK;IACjE;;uCApRWjB,mBAAmB,EAAApC,EAAA,CAAA+I,iBAAA,CAAAC,EAAA,CAAAC,iBAAA,GAAAjJ,EAAA,CAAA+I,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAnJ,EAAA,CAAA+I,iBAAA,CAAAG,EAAA,CAAAE,cAAA,GAAApJ,EAAA,CAAA+I,iBAAA,CAAAM,EAAA,CAAArK,QAAA,GAAAgB,EAAA,CAAA+I,iBAAA,CAAAO,EAAA,CAAAC,iBAAA,GAAAvJ,EAAA,CAAA+I,iBAAA,CAAAS,EAAA,CAAAC,WAAA,GAAAzJ,EAAA,CAAA+I,iBAAA,CAAA/I,EAAA,CAAA0J,iBAAA;IAAA;;YAAnBtH,mBAAmB;MAAAuH,SAAA;MAAAC,QAAA,GAAA5J,EAAA,CAAA6J,kBAAA,CAJnB,CAAC7K,QAAQ,CAAC;MAAA8K,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UClCfnK,EAJR,CAAAC,cAAA,aAAuD,aACH,aACe,cAC9B,qBAM5B;UACCD,EAAA,CAAAqK,SAAA,kBAMW;UAGjBrK,EAFI,CAAAG,YAAA,EAAc,EACT,EACH;UAEJH,EADF,CAAAC,cAAA,aAA8D,sBAK3D;UADCD,EAAA,CAAAU,UAAA,6BAAA4J,qEAAA1J,MAAA;YAAA,OAAmBwJ,GAAA,CAAAxB,iBAAA,CAAAhI,MAAA,CAAyB;UAAA,EAAC;UAInDZ,EAFI,CAAAG,YAAA,EAAe,EACX,EACF;UAGJH,EADF,CAAAC,cAAA,aAAiD,uBAS9C;UAFCD,EAAA,CAAAU,UAAA,uBAAA6J,gEAAA;YAAA,OAAaH,GAAA,CAAArD,iBAAA,EAAmB;UAAA,EAAC;UAGnC/G,EAAA,CAAAG,YAAA,EAAgB;UAYhBH,EATA,CAAAwK,UAAA,KAAAC,mCAAA,kBAGC,KAAAC,4CAAA,2BAMsI;UAgBzI1K,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAwK,UAAA,KAAAG,mCAAA,kBAAuD;UAUzD3K,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,qBAgBC;UADCD,EAFA,CAAAU,UAAA,qBAAAkK,2DAAA;YAAA,OAAWR,GAAA,CAAAvC,eAAA,EAAiB;UAAA,EAAC,oBAAAgD,0DAAA;YAAA,OACnBT,GAAA,CAAAhC,gBAAA,EAAkB;UAAA,EAAC,oBAAA0C,0DAAA;YAAA,OACnBV,GAAA,CAAAhC,gBAAA,EAAkB;UAAA,EAAC;UAE/BpI,EAAA,CAAAG,YAAA,EAAY;UAEZH,EAAA,CAAAC,cAAA,qBAUC;UADCD,EAAA,CAAAU,UAAA,oBAAAqK,0DAAA;YAAA,OAAUX,GAAA,CAAA5B,oBAAA,EAAsB;UAAA,EAAC;UAClCxI,EAAA,CAAAG,YAAA,EAAY;;;UA5GDH,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAAqB,UAAA,cAAA+I,GAAA,CAAAvH,UAAA,CAAwB;UAE1B7C,EAAA,CAAAI,SAAA,EAA0C;UAA1CJ,EAAA,CAAAqB,UAAA,gBAAA+I,GAAA,CAAA7J,QAAA,CAAAyK,iBAAA,CAA0C;UAQxChL,EAAA,CAAAI,SAAA,EAAe;UAAfJ,EAAA,CAAAqB,UAAA,gBAAe;UASnBrB,EAAA,CAAAI,SAAA,GAAwC;UACxCJ,EADA,CAAAqB,UAAA,kBAAA+I,GAAA,CAAA7J,QAAA,CAAA0K,aAAA,CAAwC,YAAAb,GAAA,CAAA7G,iBAAA,CACX;UAU/BvD,EAAA,CAAAI,SAAA,GAAiB;UAKjBJ,EALA,CAAAqB,UAAA,kBAAiB,oBACE,UAAA+I,GAAA,CAAA7J,QAAA,CAAAe,KAAA,CAEK,cAAA8I,GAAA,CAAAxI,SAAA,CAED;UAOtB5B,EAAA,CAAAI,SAAA,EAAmD;UAAnDJ,EAAA,CAAAqB,UAAA,UAAA+I,GAAA,CAAAxI,SAAA,IAAAwI,GAAA,CAAApI,kBAAA,CAAAC,MAAA,OAAmD;UAOlBjC,EAAA,CAAAI,SAAA,EAAiG;UAAjGJ,EAAA,CAAAqB,UAAA,YAAA+I,GAAA,CAAAxI,SAAA,IAAAwI,GAAA,CAAAlH,mBAAA,CAAAjB,MAAA,SAAAmI,GAAA,CAAAlG,wBAAA,GAAAkG,GAAA,CAAAlH,mBAAA,CAAiG;UAmBrHlD,EAAA,CAAAI,SAAA,EAAmC;UAAnCJ,EAAA,CAAAqB,UAAA,SAAA+I,GAAA,CAAApI,kBAAA,CAAAC,MAAA,KAAmC;UAYrDjC,EAAA,CAAAI,SAAA,EAAwB;UAWxBJ,EAXA,CAAAqB,UAAA,SAAA+I,GAAA,CAAAjH,eAAA,CAAwB,UAAAiH,GAAA,CAAA7J,QAAA,CAAA2K,WAAA,CACM,YAAAd,GAAA,CAAAnG,OAAA,CACX,wBACI,mBAGL,oBACC,qBACC,gCACW,mCACG,sCACG;UASrCjE,EAAA,CAAAI,SAAA,EAAsB;UAMtBJ,EANA,CAAAqB,UAAA,SAAA+I,GAAA,CAAArG,aAAA,CAAsB,YAAAqG,GAAA,CAAApG,WAAA,CAEC,wBACA,mBAGL;;;qBDnFhBjF,YAAY,EAAAsK,EAAA,CAAA8B,OAAA,EAAA9B,EAAA,CAAA+B,IAAA,EACZ5L,mBAAmB,EAAAgK,EAAA,CAAA6B,aAAA,EAAA7B,EAAA,CAAA8B,eAAA,EAAA9B,EAAA,CAAA+B,oBAAA,EAAA/B,EAAA,CAAAgC,kBAAA,EAAAhC,EAAA,CAAAiC,eAAA,EACnBxM,mBAAmB,EACnBI,iBAAiB,EACjBH,mBAAmB,EACnBE,aAAa,EACbD,iBAAiB,EACjBG,cAAc,EACdC,mBAAmB,EACnBO,oBAAoB,EACpBC,WAAW;MAAA2L,MAAA;IAAA;;SAMFtJ,mBAAmB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}