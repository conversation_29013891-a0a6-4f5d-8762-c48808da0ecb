{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { HeadingComponent } from '@awe/play-comp-library';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction MarketplaceFooterComponent_div_3_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 17)(1, \"awe-heading\", 12);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((item_r1 == null ? null : item_r1.text) || item_r1);\n  }\n}\nfunction MarketplaceFooterComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"awe-heading\", 14);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\", 15);\n    i0.ɵɵtemplate(4, MarketplaceFooterComponent_div_3_li_4_Template, 3, 1, \"li\", 16);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const section_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(section_r2 == null ? null : section_r2.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", section_r2.items);\n  }\n}\nfunction MarketplaceFooterComponent_ng_container_10_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 19);\n    i0.ɵɵtext(1, \"|\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MarketplaceFooterComponent_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"awe-heading\", 12);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, MarketplaceFooterComponent_ng_container_10_span_3_Template, 2, 0, \"span\", 18);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const link_r3 = ctx.$implicit;\n    const last_r4 = ctx.last;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(link_r3 == null ? null : link_r3.text);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !last_r4);\n  }\n}\nexport let MarketplaceFooterComponent = /*#__PURE__*/(() => {\n  class MarketplaceFooterComponent {\n    currentYear = new Date().getFullYear();\n    footerData = {\n      sections: [{\n        title: 'What we do',\n        items: [{\n          text: 'Applied AI'\n        }, {\n          text: 'Data'\n        }, {\n          text: 'Experience'\n        }, {\n          text: 'Platform Engineering'\n        }, {\n          text: 'Product Engineering'\n        }, {\n          text: 'Quality Engineering'\n        }, {\n          text: 'Innovate With Salesforce'\n        }]\n      }, {\n        title: 'Industries',\n        items: [{\n          text: 'Banking and Financial Service'\n        }, {\n          text: 'Communication, Media, & Entertainment'\n        }, {\n          text: 'Healthcare'\n        }, {\n          text: 'High-Tech'\n        }, {\n          text: 'Retail and Consumer Goods'\n        }, {\n          text: 'Travel and Hospitality'\n        }]\n      }, {\n        title: 'Quick Links',\n        items: [{\n          text: 'Home',\n          route: '/home'\n        }, {\n          text: 'Who we are',\n          route: '/about'\n        }, {\n          text: 'Insight and impact',\n          route: '/insights'\n        }, {\n          text: 'Careers India',\n          route: '/careers/india'\n        }, {\n          text: 'Careers North America',\n          route: '/careers/north-america'\n        }, {\n          text: 'Careers Europe',\n          route: '/careers/europe'\n        }, {\n          text: 'Careers Asia Pacific',\n          route: '/careers/asia-pacific'\n        }, {\n          text: 'Global Delivery Hubs',\n          route: '/delivery-hubs'\n        }, {\n          text: 'Contact Us',\n          route: '/contact'\n        }]\n      }],\n      legalLinks: [{\n        text: 'Terms of use',\n        route: '/terms'\n      }, {\n        text: 'Privacy Policy',\n        route: '/privacy'\n      }, {\n        text: 'Accessibility Disclosure',\n        route: '/accessibility'\n      }, {\n        text: 'AO-enhances content notice',\n        route: '/content-notice'\n      }]\n    };\n    static ɵfac = function MarketplaceFooterComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MarketplaceFooterComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MarketplaceFooterComponent,\n      selectors: [[\"app-marketplace-footer\"]],\n      decls: 14,\n      vars: 2,\n      consts: [[1, \"footer\"], [1, \"container-fluid\"], [1, \"row\", \"mb-5\"], [\"class\", \"col-12 col-sm-6 col-lg-3 mb-4 footer-section\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-12\", \"col-sm-6\", \"col-lg-3\", \"mb-4\", \"footer-section\", \"logo-section\", \"text-center\"], [\"src\", \"assets/icons/image 338.svg\", \"alt\", \"Acendion Logo\"], [1, \"row\", \"footer-bottom\"], [1, \"col-12\"], [1, \"footer-content\", \"text-center\"], [1, \"legal-links\"], [4, \"ngFor\", \"ngForOf\"], [1, \"copyright\"], [\"variant\", \"s2\", \"type\", \"regular\"], [1, \"col-12\", \"col-sm-6\", \"col-lg-3\", \"mb-4\", \"footer-section\"], [\"variant\", \"s1\", \"type\", \"bold\"], [1, \"mt-3\"], [\"class\", \"mb-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"mb-3\"], [\"class\", \"separator\", 4, \"ngIf\"], [1, \"separator\"]],\n      template: function MarketplaceFooterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"footer\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtemplate(3, MarketplaceFooterComponent_div_3_Template, 5, 2, \"div\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4);\n          i0.ɵɵelement(5, \"img\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 7)(8, \"div\", 8)(9, \"div\", 9);\n          i0.ɵɵtemplate(10, MarketplaceFooterComponent_ng_container_10_Template, 4, 2, \"ng-container\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 11)(12, \"awe-heading\", 12);\n          i0.ɵɵtext(13, \"\\u00A9 2025 Ascendion. All Rights Reserved.\");\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.footerData.sections);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngForOf\", ctx.footerData.legalLinks);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, HeadingComponent],\n      styles: [\".heading.s2 {\\n  font-size: 16px !important;\\n  cursor: pointer;\\n}\\n\\n.footer[_ngcontent-%COMP%] {\\n  background: #fafafa;\\n  padding: 4rem 0 2rem;\\n  color: var(--Text-Title, #14161f);\\n}\\n@media (max-width: 576px) {\\n  .footer[_ngcontent-%COMP%]   .footer-section[_ngcontent-%COMP%] {\\n    text-align: center;\\n  }\\n}\\n.footer[_ngcontent-%COMP%]   .footer-section[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  list-style: none;\\n  padding: 0;\\n  margin: 0;\\n}\\n@media (max-width: 992px) {\\n  .footer[_ngcontent-%COMP%]   .logo-section[_ngcontent-%COMP%] {\\n    justify-content: center;\\n    margin-top: 1rem;\\n  }\\n}\\n.footer[_ngcontent-%COMP%]   .footer-bottom[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 1728px;\\n  height: 60px;\\n  margin: 0 auto;\\n  padding: 10px;\\n  opacity: 1;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 10px;\\n}\\n.footer[_ngcontent-%COMP%]   .footer-bottom[_ngcontent-%COMP%]   .legal-links[_ngcontent-%COMP%] {\\n  text-decoration: underline;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  gap: 10px;\\n}\\n@media (max-width: 576px) {\\n  .footer[_ngcontent-%COMP%]   .footer-bottom[_ngcontent-%COMP%]   .legal-links[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.75rem;\\n  }\\n}\\n.footer[_ngcontent-%COMP%]   .footer-bottom[_ngcontent-%COMP%]   .copyright[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin: 0;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3Byb2plY3RzL2VsZGVyLXdhbmQvc3JjL2FwcC9zaGFyZWQvY29tcG9uZW50cy9tYXJrZXRwbGFjZS1mb290ZXIvbWFya2V0cGxhY2UtZm9vdGVyLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsMEJBQUE7RUFDQSxlQUFBO0FBQ0Y7O0FBRUE7RUFDRSxtQkFBQTtFQUNBLG9CQUFBO0VBQ0EsaUNBQUE7QUFDRjtBQUVJO0VBREY7SUFFSSxrQkFBQTtFQUNKO0FBQ0Y7QUFBSTtFQUNFLGdCQUFBO0VBQ0EsVUFBQTtFQUNBLFNBQUE7QUFFTjtBQUdJO0VBREY7SUFFSSx1QkFBQTtJQUNBLGdCQUFBO0VBQUo7QUFDRjtBQUlJO0VBQ0UsV0FBQTtFQUNBLGlCQUFBO0VBQ0EsWUFBQTtFQUNBLGNBQUE7RUFDQSxhQUFBO0VBQ0EsVUFBQTtFQUNBLGFBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxTQUFBO0FBRk47QUFLSTtFQUNFLDBCQUFBO0VBQ0EsYUFBQTtFQUNBLHVCQUFBO0VBQ0EsbUJBQUE7RUFDQSxTQUFBO0FBSE47QUFLTTtFQVBGO0lBUUksc0JBQUE7SUFDQSxZQUFBO0VBRk47QUFDRjtBQUtJO0VBQ0Usa0JBQUE7RUFDQSxTQUFBO0FBSE4iLCJzb3VyY2VzQ29udGVudCI6WyI6Om5nLWRlZXAgLmhlYWRpbmcuczIge1xyXG4gIGZvbnQtc2l6ZTogMTZweCAhaW1wb3J0YW50O1xyXG4gIGN1cnNvcjogcG9pbnRlcjtcclxufVxyXG5cclxuLmZvb3RlciB7XHJcbiAgYmFja2dyb3VuZDogI2ZhZmFmYTtcclxuICBwYWRkaW5nOiA0cmVtIDAgMnJlbTtcclxuICBjb2xvcjogdmFyKC0tVGV4dC1UaXRsZSwgIzE0MTYxZik7XHJcblxyXG4gIC5mb290ZXItc2VjdGlvbiB7XHJcbiAgICBAbWVkaWEgKG1heC13aWR0aDogNTc2cHgpIHtcclxuICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG4gICAgfVxyXG4gICAgdWwge1xyXG4gICAgICBsaXN0LXN0eWxlOiBub25lO1xyXG4gICAgICBwYWRkaW5nOiAwO1xyXG4gICAgICBtYXJnaW46IDA7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAubG9nby1zZWN0aW9uIHtcclxuICAgIEBtZWRpYSAobWF4LXdpZHRoOiA5OTJweCkge1xyXG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICAgICAgbWFyZ2luLXRvcDogMXJlbTtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC5mb290ZXItYm90dG9tIHtcclxuICAgIC5mb290ZXItY29udGVudCB7XHJcbiAgICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgICBtYXgtd2lkdGg6IDE3MjhweDtcclxuICAgICAgaGVpZ2h0OiA2MHB4O1xyXG4gICAgICBtYXJnaW46IDAgYXV0bztcclxuICAgICAgcGFkZGluZzogMTBweDtcclxuICAgICAgb3BhY2l0eTogMTtcclxuICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgICAgIGdhcDogMTBweDtcclxuICAgIH1cclxuXHJcbiAgICAubGVnYWwtbGlua3Mge1xyXG4gICAgICB0ZXh0LWRlY29yYXRpb246IHVuZGVybGluZTtcclxuICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgIGdhcDogMTBweDtcclxuXHJcbiAgICAgIEBtZWRpYSAobWF4LXdpZHRoOiA1NzZweCkge1xyXG4gICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgICAgICAgZ2FwOiAwLjc1cmVtO1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLmNvcHlyaWdodCB7XHJcbiAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICAgICAgbWFyZ2luOiAwO1xyXG4gICAgfVxyXG4gIH1cclxufVxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n  return MarketplaceFooterComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "HeadingComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "item_r1", "text", "ɵɵtemplate", "MarketplaceFooterComponent_div_3_li_4_Template", "section_r2", "title", "ɵɵproperty", "items", "ɵɵelementContainerStart", "MarketplaceFooterComponent_ng_container_10_span_3_Template", "link_r3", "last_r4", "MarketplaceFooterComponent", "currentYear", "Date", "getFullYear", "footerData", "sections", "route", "legalLinks", "selectors", "decls", "vars", "consts", "template", "MarketplaceFooterComponent_Template", "rf", "ctx", "MarketplaceFooterComponent_div_3_Template", "ɵɵelement", "MarketplaceFooterComponent_ng_container_10_Template", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\shared\\components\\marketplace-footer\\marketplace-footer.component.ts", "C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\shared\\components\\marketplace-footer\\marketplace-footer.component.html"], "sourcesContent": ["import { CommonModule } from '@angular/common';\r\nimport { Component } from '@angular/core';\r\nimport { HeadingComponent } from '@awe/play-comp-library';\r\n\r\n@Component({\r\n  selector: 'app-marketplace-footer',\r\n  standalone: true,\r\n  imports: [CommonModule, HeadingComponent],\r\n  templateUrl: './marketplace-footer.component.html',\r\n  styleUrl: './marketplace-footer.component.scss',\r\n})\r\nexport class MarketplaceFooterComponent {\r\n  currentYear = new Date().getFullYear();\r\n  footerData = {\r\n    sections: [\r\n      {\r\n        title: 'What we do',\r\n        items: [\r\n          { text: 'Applied AI' },\r\n          { text: 'Data' },\r\n          { text: 'Experience' },\r\n          { text: 'Platform Engineering' },\r\n          { text: 'Product Engineering' },\r\n          { text: 'Quality Engineering' },\r\n          { text: 'Innovate With Salesforce' },\r\n        ],\r\n      },\r\n      {\r\n        title: 'Industries',\r\n        items: [\r\n          { text: 'Banking and Financial Service' },\r\n          { text: 'Communication, Media, & Entertainment' },\r\n          { text: 'Healthcare' },\r\n          { text: 'High-Tech' },\r\n          { text: 'Retail and Consumer Goods' },\r\n          { text: 'Travel and Hospitality' },\r\n        ],\r\n      },\r\n      {\r\n        title: 'Quick Links',\r\n        items: [\r\n          { text: 'Home', route: '/home' },\r\n          { text: 'Who we are', route: '/about' },\r\n          { text: 'Insight and impact', route: '/insights' },\r\n          { text: 'Careers India', route: '/careers/india' },\r\n          { text: 'Careers North America', route: '/careers/north-america' },\r\n          { text: 'Careers Europe', route: '/careers/europe' },\r\n          { text: 'Careers Asia Pacific', route: '/careers/asia-pacific' },\r\n          { text: 'Global Delivery Hubs', route: '/delivery-hubs' },\r\n          { text: 'Contact Us', route: '/contact' },\r\n        ],\r\n      },\r\n    ],\r\n    legalLinks: [\r\n      { text: 'Terms of use', route: '/terms' },\r\n      { text: 'Privacy Policy', route: '/privacy' },\r\n      { text: 'Accessibility Disclosure', route: '/accessibility' },\r\n      { text: 'AO-enhances content notice', route: '/content-notice' },\r\n    ],\r\n  };\r\n}\r\n", "<footer class=\"footer\">\n  <div class=\"container-fluid\">\n    <div class=\"row mb-5\">\n      <div\n        class=\"col-12 col-sm-6 col-lg-3 mb-4 footer-section\"\n        *ngFor=\"let section of footerData.sections\"\n      >\n        <awe-heading variant=\"s1\" type=\"bold\">{{ section?.title }}</awe-heading>\n        <ul class=\"mt-3\">\n          <li *ngFor=\"let item of section.items\" class=\"mb-3\">\n            <awe-heading variant=\"s2\" type=\"regular\">{{\n              item?.text || item\n            }}</awe-heading>\n          </li>\n        </ul>\n      </div>\n      <div class=\"col-12 col-sm-6 col-lg-3 mb-4 footer-section logo-section text-center\">\n        <img src=\"assets/icons/image 338.svg\" alt=\"Acendion Logo\" />\n      </div>\n    </div>\n    <div class=\"row footer-bottom\">\n      <div class=\"col-12\">\n        <div class=\"footer-content text-center\">\n          <div class=\"legal-links\">\n            <ng-container\n              *ngFor=\"let link of footerData.legalLinks; let last = last\"\n            >\n              <awe-heading variant=\"s2\" type=\"regular\">{{\n                link?.text\n              }}</awe-heading>\n              <span *ngIf=\"!last\" class=\"separator\">|</span>\n            </ng-container>\n          </div>\n          <div class=\"copyright\">\n            <awe-heading variant=\"s2\" type=\"regular\">© 2025 Ascendion. All Rights Reserved.</awe-heading>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</footer>\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,gBAAgB,QAAQ,wBAAwB;;;;;ICQ7CC,EADF,CAAAC,cAAA,aAAoD,sBACT;IAAAD,EAAA,CAAAE,MAAA,GAEvC;IACJF,EADI,CAAAG,YAAA,EAAc,EACb;;;;IAHsCH,EAAA,CAAAI,SAAA,GAEvC;IAFuCJ,EAAA,CAAAK,iBAAA,EAAAC,OAAA,kBAAAA,OAAA,CAAAC,IAAA,KAAAD,OAAA,CAEvC;;;;;IALNN,EAJF,CAAAC,cAAA,cAGC,sBACuC;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAc;IACxEH,EAAA,CAAAC,cAAA,aAAiB;IACfD,EAAA,CAAAQ,UAAA,IAAAC,8CAAA,iBAAoD;IAMxDT,EADE,CAAAG,YAAA,EAAK,EACD;;;;IARkCH,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAK,iBAAA,CAAAK,UAAA,kBAAAA,UAAA,CAAAC,KAAA,CAAoB;IAEnCX,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAY,UAAA,YAAAF,UAAA,CAAAG,KAAA,CAAgB;;;;;IAqBjCb,EAAA,CAAAC,cAAA,eAAsC;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IANhDH,EAAA,CAAAc,uBAAA,GAEC;IACCd,EAAA,CAAAC,cAAA,sBAAyC;IAAAD,EAAA,CAAAE,MAAA,GAEvC;IAAAF,EAAA,CAAAG,YAAA,EAAc;IAChBH,EAAA,CAAAQ,UAAA,IAAAO,0DAAA,mBAAsC;;;;;;IAHGf,EAAA,CAAAI,SAAA,GAEvC;IAFuCJ,EAAA,CAAAK,iBAAA,CAAAW,OAAA,kBAAAA,OAAA,CAAAT,IAAA,CAEvC;IACKP,EAAA,CAAAI,SAAA,EAAW;IAAXJ,EAAA,CAAAY,UAAA,UAAAK,OAAA,CAAW;;;ADnBhC,WAAaC,0BAA0B;EAAjC,MAAOA,0BAA0B;IACrCC,WAAW,GAAG,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;IACtCC,UAAU,GAAG;MACXC,QAAQ,EAAE,CACR;QACEZ,KAAK,EAAE,YAAY;QACnBE,KAAK,EAAE,CACL;UAAEN,IAAI,EAAE;QAAY,CAAE,EACtB;UAAEA,IAAI,EAAE;QAAM,CAAE,EAChB;UAAEA,IAAI,EAAE;QAAY,CAAE,EACtB;UAAEA,IAAI,EAAE;QAAsB,CAAE,EAChC;UAAEA,IAAI,EAAE;QAAqB,CAAE,EAC/B;UAAEA,IAAI,EAAE;QAAqB,CAAE,EAC/B;UAAEA,IAAI,EAAE;QAA0B,CAAE;OAEvC,EACD;QACEI,KAAK,EAAE,YAAY;QACnBE,KAAK,EAAE,CACL;UAAEN,IAAI,EAAE;QAA+B,CAAE,EACzC;UAAEA,IAAI,EAAE;QAAuC,CAAE,EACjD;UAAEA,IAAI,EAAE;QAAY,CAAE,EACtB;UAAEA,IAAI,EAAE;QAAW,CAAE,EACrB;UAAEA,IAAI,EAAE;QAA2B,CAAE,EACrC;UAAEA,IAAI,EAAE;QAAwB,CAAE;OAErC,EACD;QACEI,KAAK,EAAE,aAAa;QACpBE,KAAK,EAAE,CACL;UAAEN,IAAI,EAAE,MAAM;UAAEiB,KAAK,EAAE;QAAO,CAAE,EAChC;UAAEjB,IAAI,EAAE,YAAY;UAAEiB,KAAK,EAAE;QAAQ,CAAE,EACvC;UAAEjB,IAAI,EAAE,oBAAoB;UAAEiB,KAAK,EAAE;QAAW,CAAE,EAClD;UAAEjB,IAAI,EAAE,eAAe;UAAEiB,KAAK,EAAE;QAAgB,CAAE,EAClD;UAAEjB,IAAI,EAAE,uBAAuB;UAAEiB,KAAK,EAAE;QAAwB,CAAE,EAClE;UAAEjB,IAAI,EAAE,gBAAgB;UAAEiB,KAAK,EAAE;QAAiB,CAAE,EACpD;UAAEjB,IAAI,EAAE,sBAAsB;UAAEiB,KAAK,EAAE;QAAuB,CAAE,EAChE;UAAEjB,IAAI,EAAE,sBAAsB;UAAEiB,KAAK,EAAE;QAAgB,CAAE,EACzD;UAAEjB,IAAI,EAAE,YAAY;UAAEiB,KAAK,EAAE;QAAU,CAAE;OAE5C,CACF;MACDC,UAAU,EAAE,CACV;QAAElB,IAAI,EAAE,cAAc;QAAEiB,KAAK,EAAE;MAAQ,CAAE,EACzC;QAAEjB,IAAI,EAAE,gBAAgB;QAAEiB,KAAK,EAAE;MAAU,CAAE,EAC7C;QAAEjB,IAAI,EAAE,0BAA0B;QAAEiB,KAAK,EAAE;MAAgB,CAAE,EAC7D;QAAEjB,IAAI,EAAE,4BAA4B;QAAEiB,KAAK,EAAE;MAAiB,CAAE;KAEnE;;uCAhDUN,0BAA0B;IAAA;;YAA1BA,0BAA0B;MAAAQ,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCTnChC,EAFJ,CAAAC,cAAA,gBAAuB,aACQ,aACL;UACpBD,EAAA,CAAAQ,UAAA,IAAA0B,yCAAA,iBAGC;UAUDlC,EAAA,CAAAC,cAAA,aAAmF;UACjFD,EAAA,CAAAmC,SAAA,aAA4D;UAEhEnC,EADE,CAAAG,YAAA,EAAM,EACF;UAIAH,EAHN,CAAAC,cAAA,aAA+B,aACT,aACsB,aACb;UACvBD,EAAA,CAAAQ,UAAA,KAAA4B,mDAAA,2BAEC;UAMHpC,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAAuB,uBACoB;UAAAD,EAAA,CAAAE,MAAA,mDAAsC;UAM3FF,EAN2F,CAAAG,YAAA,EAAc,EACzF,EACF,EACF,EACF,EACF,EACC;;;UAnCmBH,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAY,UAAA,YAAAqB,GAAA,CAAAX,UAAA,CAAAC,QAAA,CAAsB;UAoBnBvB,EAAA,CAAAI,SAAA,GAA0B;UAA1BJ,EAAA,CAAAY,UAAA,YAAAqB,GAAA,CAAAX,UAAA,CAAAG,UAAA,CAA0B;;;qBDlB7C3B,YAAY,EAAAuC,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAExC,gBAAgB;MAAAyC,MAAA;IAAA;;SAI7BtB,0BAA0B;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}