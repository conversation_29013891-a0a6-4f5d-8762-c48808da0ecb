{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule, formatDate } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { ApprovalCardComponent, AvaTagComponent, ButtonComponent, DropdownComponent, IconComponent, LinkComponent, TextCardComponent } from '@ava/play-comp-library';\nimport { QuickActionsComponent } from '../../shared/components/quick-actions/quick-actions.component';\nimport { ACTIVE_MONITORING_OPTIONS, ActiveMonitorings, APIKeys, DASHBOARD_CARD_DETAILS } from './constants/dashoard.constant';\nimport approvalText from '../approval/constants/approval.json';\nlet DashboardComponent = class DashboardComponent {\n  router;\n  apiService;\n  approvalService;\n  options = ACTIVE_MONITORING_OPTIONS;\n  labels = approvalText.labels;\n  quickActions = [{\n    id: 'build-agent',\n    label: 'Build Agent',\n    icon: 'svgs/agents.svg'\n  }, {\n    id: 'build-workflow',\n    label: 'Build Workflow',\n    icon: 'svgs/Workflows.svg'\n  }, {\n    id: 'create-prompt',\n    label: 'Create Prompt',\n    icon: 'svgs/Prompts.svg'\n  }, {\n    id: 'create-tool',\n    label: 'Create Tool',\n    icon: 'svgs/Tools.svg'\n  }, {\n    id: 'create-guardrail',\n    label: 'Create Guardrail',\n    icon: 'svgs/Guardrails.svg'\n  }, {\n    id: 'create-knowledge-base',\n    label: 'Create Knowledge Base',\n    icon: 'svgs/Knowledgebase.svg'\n  }];\n  approvalCardData = {\n    header: {\n      iconName: '',\n      // Optional, if you want an icon\n      title: 'High Priority Approvals',\n      viewAll: true\n    },\n    contents: [{\n      session1: {\n        title: 'Autonomous Systems..... Agent',\n        labels: [{\n          name: 'Agent',\n          color: 'info',\n          background: '#B6F0FA',\n          type: 'pill'\n        }, {\n          name: 'High',\n          color: 'error',\n          background: '#E53935',\n          type: 'pill'\n        }]\n      },\n      session2: [{\n        name: 'Individual',\n        color: 'default',\n        background: '#ECEEF1',\n        type: 'normal'\n      }, {\n        name: 'Ascendion',\n        color: 'default',\n        background: '#ECEEF1',\n        type: 'normal'\n      }, {\n        name: 'Platform Engineering',\n        color: 'default',\n        background: '#ECEEF1',\n        type: 'normal'\n      }, {\n        name: 'Digital Ascender',\n        color: 'default',\n        background: '#ECEEF1',\n        type: 'normal'\n      }, {\n        name: 'Revamp Demo',\n        color: 'default',\n        background: '#ECEEF1',\n        type: 'normal'\n      }],\n      session3: [{\n        iconName: 'user',\n        label: '<EMAIL>'\n      }, {\n        iconName: 'calendar-days',\n        label: '12 May 2025'\n      }],\n      session4: {\n        status: 'Pending Approval',\n        iconName: 'circle-check'\n      }\n    },\n    // Additional cards\n    {\n      session1: {\n        title: 'Invoice Processing & Approval',\n        labels: [{\n          name: 'Workflow',\n          color: 'info',\n          background: '#B6F0FA',\n          type: 'pill'\n        }, {\n          name: 'Critical',\n          color: 'error',\n          background: '#E53935',\n          type: 'pill'\n        }]\n      },\n      session2: [{\n        name: 'Finance',\n        color: 'default',\n        background: '#ECEEF1',\n        type: 'normal'\n      }, {\n        name: 'Ascendion',\n        color: 'default',\n        background: '#ECEEF1',\n        type: 'normal'\n      }],\n      session3: [{\n        iconName: 'user',\n        label: '<EMAIL>'\n      }, {\n        iconName: 'calendar-days',\n        label: '15 May 2025'\n      }],\n      session4: {\n        status: 'Pending Approval',\n        iconName: 'circle-check'\n      }\n    }, {\n      session1: {\n        title: 'Customer Support Chatbot',\n        labels: [{\n          name: 'Agent',\n          color: 'info',\n          background: '#B6F0FA',\n          type: 'pill'\n        }, {\n          name: 'Medium',\n          color: 'warning',\n          background: '#FFD600',\n          type: 'pill'\n        }]\n      },\n      session2: [{\n        name: 'Support',\n        color: 'default',\n        background: '#ECEEF1',\n        type: 'normal'\n      }, {\n        name: 'Ascendion',\n        color: 'default',\n        background: '#ECEEF1',\n        type: 'normal'\n      }],\n      session3: [{\n        iconName: 'user',\n        label: '<EMAIL>'\n      }, {\n        iconName: 'calendar-days',\n        label: '18 May 2025'\n      }],\n      session4: {\n        status: 'Awaiting Review',\n        iconName: 'circle-check'\n      }\n    }, {\n      session1: {\n        title: 'AI-Powered Code Review Assistant',\n        labels: [{\n          name: 'Agent',\n          color: 'info',\n          background: '#B6F0FA',\n          type: 'pill'\n        }, {\n          name: 'Low',\n          color: 'success',\n          background: '#4CAF50',\n          type: 'pill'\n        }]\n      },\n      session2: [{\n        name: 'Engineering',\n        color: 'default',\n        background: '#ECEEF1',\n        type: 'normal'\n      }, {\n        name: 'Ascendion',\n        color: 'default',\n        background: '#ECEEF1',\n        type: 'normal'\n      }],\n      session3: [{\n        iconName: 'user',\n        label: '<EMAIL>'\n      }, {\n        iconName: 'calendar-days',\n        label: '20 May 2025'\n      }],\n      session4: {\n        status: 'Completed',\n        iconName: 'circle-check'\n      }\n    }],\n    footer: {}\n  };\n  dashboardDetails = [\n    // {\n    //   icon: '',\n    //   title: '',\n    //   value: 0,\n    //   subtitle: '',\n    //   badge: '',\n    // },\n    // {\n    //   icon: 'trending-up',\n    //   title: 'Active Workflows',\n    //   value: 80,\n    //   subtitle: 'Agents actively running',\n    //   badge: 'S',\n    // },\n    // {\n    //   icon: 'trending-up',\n    //   title: 'Active Agents',\n    //   value: 1240,\n    //   subtitle: 'Agents active',\n    //   badge: 'S',\n    // },\n    // {\n    //   icon: 'trending-up',\n    //   title: 'Agents Approval',\n    //   value: 120,\n    //   subtitle: 'Agents actively running',\n    //   badge: 'S',\n    // },\n    // {\n    //   icon: 'trending-up',\n    //   title: 'Active Workflows',\n    //   value: 80,\n    //   subtitle: 'Agents actively running',\n    //   badge: 'S',\n    // },\n  ];\n  // Dashboard metrics\n  totalAgents = 0;\n  newAgentsCreated = 50;\n  totalWorkflows = 124;\n  newWorkflowsCreated = 50;\n  totalUsers = 300;\n  newUsersAdded = 50;\n  selectedActiveMonitoring = ActiveMonitorings.tools;\n  activityMonitoringCount = 5;\n  // Current user info (would come from auth service in real app)\n  currentUser = {\n    name: 'Akash Raj'\n  };\n  // Footer info\n  currentYear = new Date().getFullYear();\n  // User logs data\n  userLogs = [{\n    id: '1',\n    username: 'Michael Scott',\n    avatar: 'assets/images/avatars/michael-scott.jpg',\n    securityToken: 'X9D7K2B4MQ',\n    status: 'Active'\n  }, {\n    id: '2',\n    username: 'Jim Halpert',\n    avatar: 'assets/images/avatars/jim-halpert.jpg',\n    securityToken: 'QWE2349SDF',\n    status: 'Active'\n  }, {\n    id: '3',\n    username: 'Dwight Schrute',\n    avatar: 'assets/images/avatars/dwight-schrute.jpg',\n    securityToken: 'OWDF1230JS',\n    status: 'Active'\n  }, {\n    id: '4',\n    username: 'Kevin Malone',\n    avatar: 'assets/images/avatars/kevin-malone.jpg',\n    securityToken: 'SDVP9I23EJ',\n    status: 'Active'\n  }];\n  // Model usage data\n  modelUsage = [{\n    id: '1',\n    name: 'GPT 3',\n    publisher: {\n      name: 'Open AI',\n      logo: 'assets/images/logos/openai-logo.png'\n    },\n    agentsCount: 48\n  }, {\n    id: '2',\n    name: 'Claude 2',\n    publisher: {\n      name: 'Anthropic',\n      logo: 'assets/images/logos/anthropic-logo.png'\n    },\n    agentsCount: 24\n  }, {\n    id: '3',\n    name: 'Gemini',\n    publisher: {\n      name: 'Google',\n      logo: 'assets/images/logos/google-logo.png'\n    },\n    agentsCount: 20\n  }, {\n    id: '4',\n    name: 'GPT-4',\n    publisher: {\n      name: 'Open AI',\n      logo: 'assets/images/logos/openai-logo.png'\n    },\n    agentsCount: 8\n  }];\n  // Pending approvals\n  pendingApprovals = [{\n    id: '1',\n    name: 'Test Ruby to Springboot',\n    type: 'migration'\n  }, {\n    id: '2',\n    name: 'Customer Support Chatbot',\n    type: 'agent'\n  }, {\n    id: '3',\n    name: 'Invoice Processing & Approval',\n    type: 'workflow'\n  }, {\n    id: '4',\n    name: 'Invoice Processing & Approval',\n    type: 'workflow'\n  }, {\n    id: '5',\n    name: 'AI-Powered Code Review Assistant',\n    type: 'agent'\n  }, {\n    id: '6',\n    name: 'AI-Powered Code Review Assistant',\n    type: 'agent'\n  }, {\n    id: '7',\n    name: 'AI-Powered Code Review Assistant',\n    type: 'agent'\n  }, {\n    id: '8',\n    name: 'AI-Powered Code Review Assistant',\n    type: 'agent'\n  }];\n  activityMonitoring = [\n    // {\n    //   agentName: 'Agent Name will be here',\n    //   status: 'Active',\n    //   user: 'Michael Scott',\n    //   date: '12 June 2025',\n    //   totalRuns: 264,\n    // },\n    // {\n    //   agentName: 'Agent Name will be here',\n    //   status: 'Active',\n    //   user: 'Michael Scott',\n    //   date: '12 June 2025',\n    //   totalRuns: 264,\n    // },\n    // {\n    //   agentName: 'Agent Name will be here',\n    //   status: 'Active',\n    //   user: 'Michael Scott',\n    //   date: '12 June 2025',\n    //   totalRuns: 264,\n    // },\n    // {\n    //   agentName: 'Agent Name will be here',\n    //   status: 'Active',\n    //   user: 'Michael Scott',\n    //   date: '12 June 2025',\n    //   totalRuns: 264,\n    // },\n    // {\n    //   agentName: 'Agent Name will be here',\n    //   status: 'Active',\n    //   user: 'Michael Scott',\n    //   date: '12 June 2025',\n    //   totalRuns: 264,\n    // },\n  ];\n  workflowApprovals = [];\n  constructor(router, apiService, approvalService) {\n    this.router = router;\n    this.apiService = apiService;\n    this.approvalService = approvalService;\n  }\n  // seprating the tool and agents.\n  activityMonitoringTools = [];\n  activityMonitoringAgents = [];\n  setActiveMonitoringData() {\n    // INsted of re-formating, just switch the values\n    if (this.selectedActiveMonitoring === ActiveMonitorings.tools) {\n      this.activityMonitoring = this.activityMonitoringTools;\n    } else {\n      this.activityMonitoring = this.activityMonitoringAgents;\n    }\n  }\n  mapToActivityMonitoringItem = (item, nameKey, usageKey) => ({\n    agentName: item[nameKey] || '',\n    totalRuns: Number(item[usageKey]) || 0,\n    status: '',\n    user: '',\n    date: ''\n  });\n  toRequestStatus(value) {\n    return value === 'approved' || value === 'rejected' || value === 'review' ? value : 'review';\n  }\n  initApiCalls() {\n    const date = new Date();\n    const dateEnd = this.apiService.formatDate(date);\n    date.setDate(1);\n    const dateStart = this.apiService.formatDate(date);\n    this.apiService.getCollabrativeAgentAnalytics(dateStart, dateEnd).subscribe(response => {\n      // setting dashboard card values\n      this.dashboardDetails = DASHBOARD_CARD_DETAILS.map(cardDetail => {\n        cardDetail.value = response[cardDetail.field] || this.totalAgents;\n        return cardDetail;\n      });\n      // Active Monitoring\n      // Extracting tools and agents to seprate varibales to reduce frequent re-formatig as the dropdown value changes\n      this.activityMonitoringTools = response[APIKeys.toolUsage].slice(0, this.activityMonitoringCount).map(toolUsage => this.mapToActivityMonitoringItem(toolUsage, APIKeys.toolName, APIKeys.usageCount));\n      this.activityMonitoringAgents = response[APIKeys.agentMetrics].slice(0, this.activityMonitoringCount).map(agentMetric => this.mapToActivityMonitoringItem(agentMetric, APIKeys.agentName, APIKeys.workflowCount));\n      this.setActiveMonitoringData();\n    });\n    this.approvalService.getAllReviewAgents(1, 100, false).subscribe(res => {\n      const agentReviewDetails = res?.agentReviewDetails || [];\n      // Extracting agents which are under review\n      this.totalAgents = agentReviewDetails.filter(agentReviewDetail => agentReviewDetail.status !== 'approved')?.length || 0;\n      // If this API call is late then will set approval count here.\n      const toolCardDetial = this.dashboardDetails.at(-1);\n      if (toolCardDetial) {\n        toolCardDetial.value = this.totalAgents;\n      }\n    });\n    this.approvalService.getAllReviewWorkflows(1, 5, false).subscribe(response => {\n      const type = 'workflow';\n      this.workflowApprovals = response.workflowReviewDetails?.map(req => {\n        const statusIcons = {\n          approved: 'circle-check-big',\n          rejected: 'circle-x',\n          review: 'clock'\n        };\n        const statusTexts = {\n          approved: this.labels.approved,\n          rejected: this.labels.rejected,\n          review: this.labels.review\n        };\n        const statusKey = this.toRequestStatus(req?.status);\n        let specificId = 0;\n        let title = '';\n        specificId = req.workflowId;\n        title = req.workflowName;\n        return {\n          id: req.id,\n          refId: specificId,\n          type: type,\n          session1: {\n            title: title,\n            labels: [{\n              name: type,\n              color: 'success',\n              background: 'red',\n              type: 'normal'\n            }, {\n              name: req.changeRequestType,\n              color: req.changeRequestType === 'update' ? 'error' : 'info',\n              background: 'red',\n              type: 'pill'\n            }]\n          },\n          session2: [{\n            name: type,\n            color: 'default',\n            background: 'red',\n            type: 'normal'\n          }, {\n            name: req.status,\n            color: 'default',\n            background: 'red',\n            type: 'normal'\n          }],\n          session3: [{\n            iconName: 'user',\n            label: req.requestedBy\n          }, {\n            iconName: 'calendar-days',\n            label: formatDate(req?.requestedAt, 'dd MMM yyyy', 'en-IN')\n          }],\n          session4: {\n            status: statusTexts[statusKey],\n            iconName: statusIcons[statusKey]\n          }\n        };\n      });\n    });\n  }\n  ngOnInit() {\n    // The layout is now managed with fixed heights in CSS\n    // No need for recalculateLayout\n    this.initApiCalls();\n  }\n  // Navigate to a route\n  navigateTo(route) {\n    this.router.navigate([route]);\n  }\n  // Approve a pending item\n  approveItem(id) {\n    // In a real app, you would call a service to approve the item\n    // Then remove it from the pendingApprovals array or refresh the list\n  }\n  // Test method to demonstrate loader functionality\n  testLoader() {\n    this.apiService.getConfigLabels().subscribe({\n      next: response => {},\n      error: error => {}\n    });\n  }\n  uClick(index) {\n    this.router.navigate(['/approval']);\n  }\n  onSelectionChange(data) {\n    this.selectedActiveMonitoring = data.selectedValue;\n    this.setActiveMonitoringData();\n  }\n  onQuickActionClick(action) {\n    console.log('Quick action clicked:', action);\n    // Handle navigation or action based on action.id\n    switch (action.id) {\n      case 'build-agent':\n        this.router.navigate(['/build/agents/create']);\n        break;\n      case 'build-workflow':\n        this.router.navigate(['/build/workflows/create']);\n        break;\n      case 'create-prompt':\n        this.router.navigate(['/libraries/prompts/create']);\n        break;\n      case 'create-tool':\n        this.router.navigate(['/libraries/tools/create']);\n        break;\n      case 'create-guardrail':\n        this.router.navigate(['/libraries/guardrails/create']);\n        break;\n      case 'create-knowledge-base':\n        this.router.navigate(['/libraries/knowledge-base/create']);\n        break;\n      default:\n        console.log('Unknown action:', action.id);\n    }\n  }\n};\nDashboardComponent = __decorate([Component({\n  selector: 'app-dashboard',\n  standalone: true,\n  imports: [CommonModule, RouterModule, TextCardComponent, ButtonComponent, ApprovalCardComponent, IconComponent, DropdownComponent, QuickActionsComponent, AvaTagComponent, LinkComponent],\n  templateUrl: './dashboard.component.html',\n  styleUrl: './dashboard.component.scss'\n})], DashboardComponent);\nexport { DashboardComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "formatDate", "RouterModule", "ApprovalCardComponent", "AvaTagComponent", "ButtonComponent", "DropdownComponent", "IconComponent", "LinkComponent", "TextCardComponent", "QuickActionsComponent", "ACTIVE_MONITORING_OPTIONS", "ActiveMonitorings", "APIKeys", "DASHBOARD_CARD_DETAILS", "approvalText", "DashboardComponent", "router", "apiService", "approvalService", "options", "labels", "quickActions", "id", "label", "icon", "approvalCardData", "header", "iconName", "title", "viewAll", "contents", "session1", "name", "color", "background", "type", "session2", "session3", "session4", "status", "footer", "dashboardDetails", "totalAgents", "newAgentsCreated", "totalWorkflows", "newWorkflowsCreated", "totalUsers", "newUsersAdded", "selectedActiveMonitoring", "tools", "activityMonitoringCount", "currentUser", "currentYear", "Date", "getFullYear", "userLogs", "username", "avatar", "securityToken", "modelUsage", "publisher", "logo", "agentsCount", "pendingApprovals", "activityMonitoring", "workflowApprovals", "constructor", "activityMonitoringTools", "activityMonitoringAgents", "setActiveMonitoringData", "mapToActivityMonitoringItem", "item", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "totalRuns", "Number", "user", "date", "toRequestStatus", "value", "initApiCalls", "dateEnd", "setDate", "dateStart", "getCollabrativeAgentAnalytics", "subscribe", "response", "map", "cardDetail", "field", "toolUsage", "slice", "toolName", "usageCount", "agentMetrics", "agentMetric", "workflowCount", "getAllReviewAgents", "res", "agentReviewDetails", "filter", "agentReviewDetail", "length", "toolCardDetial", "at", "getAllReviewWorkflows", "workflowReviewDetails", "req", "statusIcons", "approved", "rejected", "review", "statusTexts", "statusKey", "specificId", "workflowId", "workflowName", "refId", "changeRequestType", "requestedBy", "requestedAt", "ngOnInit", "navigateTo", "route", "navigate", "approveItem", "<PERSON><PERSON><PERSON><PERSON>", "getConfigLabels", "next", "error", "uClick", "index", "onSelectionChange", "data", "selected<PERSON><PERSON><PERSON>", "onQuickActionClick", "action", "console", "log", "__decorate", "selector", "standalone", "imports", "templateUrl", "styleUrl"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\dashboard\\dashboard.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule, formatDate } from '@angular/common';\r\nimport { RouterModule } from '@angular/router';\r\nimport { Router } from '@angular/router';\r\nimport { SharedApiServiceService } from '../../shared/services/shared-api-service.service';\r\nimport {\r\n  ApprovalCardComponent,\r\n  AvaTagComponent,\r\n  ButtonComponent,\r\n  DropdownComponent,\r\n  DropdownOption,\r\n  IconComponent,\r\n  LinkComponent,\r\n  TextCardComponent,\r\n} from '@ava/play-comp-library';\r\nimport { QuickActionsComponent } from '../../shared/components/quick-actions/quick-actions.component';\r\nimport {\r\n  ActivityMonitoringI,\r\n  DashboardDetailI,\r\n} from './models/dashboard.model';\r\nimport { ApprovalService } from '../../shared/services/approval.service';\r\nimport {\r\n  ACTIVE_MONITORING_OPTIONS,\r\n  ActiveMonitorings,\r\n  APIKeys,\r\n  DASHBOARD_CARD_DETAILS,\r\n} from './constants/dashoard.constant';\r\nimport { RequestStatus } from '../approval/approval.component';\r\nimport approvalText from '../approval/constants/approval.json';\r\n\r\n// Interfaces\r\ninterface UserLog {\r\n  id: string;\r\n  username: string;\r\n  avatar: string;\r\n  securityToken: string;\r\n  status: 'Active' | 'Inactive' | 'Pending';\r\n}\r\n\r\ninterface ModelUsage {\r\n  id: string;\r\n  name: string;\r\n  publisher: {\r\n    name: string;\r\n    logo: string;\r\n  };\r\n  agentsCount: number;\r\n}\r\n\r\ninterface PendingApproval {\r\n  id: string;\r\n  name: string;\r\n  type: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-dashboard',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    RouterModule,\r\n    TextCardComponent,\r\n    ButtonComponent,\r\n    ApprovalCardComponent,\r\n    IconComponent,\r\n    DropdownComponent,\r\n    QuickActionsComponent,\r\n    AvaTagComponent,\r\n    LinkComponent,\r\n  ],\r\n  templateUrl: './dashboard.component.html',\r\n  styleUrl: './dashboard.component.scss',\r\n})\r\nexport class DashboardComponent implements OnInit {\r\n  options: DropdownOption[] = ACTIVE_MONITORING_OPTIONS;\r\n  public labels: any = approvalText.labels;\r\n\r\n  quickActions: any[] = [\r\n    { id: 'build-agent', label: 'Build Agent', icon: 'svgs/agents.svg' },\r\n    {\r\n      id: 'build-workflow',\r\n      label: 'Build Workflow',\r\n      icon: 'svgs/Workflows.svg',\r\n    },\r\n    { id: 'create-prompt', label: 'Create Prompt', icon: 'svgs/Prompts.svg' },\r\n    { id: 'create-tool', label: 'Create Tool', icon: 'svgs/Tools.svg' },\r\n    {\r\n      id: 'create-guardrail',\r\n      label: 'Create Guardrail',\r\n      icon: 'svgs/Guardrails.svg',\r\n    },\r\n    {\r\n      id: 'create-knowledge-base',\r\n      label: 'Create Knowledge Base',\r\n      icon: 'svgs/Knowledgebase.svg',\r\n    },\r\n  ];\r\n\r\n  approvalCardData = {\r\n    header: {\r\n      iconName: '', // Optional, if you want an icon\r\n      title: 'High Priority Approvals',\r\n      viewAll: true,\r\n    },\r\n    contents: [\r\n      {\r\n        session1: {\r\n          title: 'Autonomous Systems..... Agent',\r\n          labels: [\r\n            {\r\n              name: 'Agent',\r\n              color: 'info',\r\n              background: '#B6F0FA',\r\n              type: 'pill',\r\n            },\r\n            {\r\n              name: 'High',\r\n              color: 'error',\r\n              background: '#E53935',\r\n              type: 'pill',\r\n            },\r\n          ],\r\n        },\r\n        session2: [\r\n          {\r\n            name: 'Individual',\r\n            color: 'default',\r\n            background: '#ECEEF1',\r\n            type: 'normal',\r\n          },\r\n          {\r\n            name: 'Ascendion',\r\n            color: 'default',\r\n            background: '#ECEEF1',\r\n            type: 'normal',\r\n          },\r\n          {\r\n            name: 'Platform Engineering',\r\n            color: 'default',\r\n            background: '#ECEEF1',\r\n            type: 'normal',\r\n          },\r\n          {\r\n            name: 'Digital Ascender',\r\n            color: 'default',\r\n            background: '#ECEEF1',\r\n            type: 'normal',\r\n          },\r\n          {\r\n            name: 'Revamp Demo',\r\n            color: 'default',\r\n            background: '#ECEEF1',\r\n            type: 'normal',\r\n          },\r\n        ],\r\n        session3: [\r\n          { iconName: 'user', label: '<EMAIL>' },\r\n          { iconName: 'calendar-days', label: '12 May 2025' },\r\n        ],\r\n        session4: {\r\n          status: 'Pending Approval',\r\n          iconName: 'circle-check',\r\n        },\r\n      },\r\n      // Additional cards\r\n      {\r\n        session1: {\r\n          title: 'Invoice Processing & Approval',\r\n          labels: [\r\n            {\r\n              name: 'Workflow',\r\n              color: 'info',\r\n              background: '#B6F0FA',\r\n              type: 'pill',\r\n            },\r\n            {\r\n              name: 'Critical',\r\n              color: 'error',\r\n              background: '#E53935',\r\n              type: 'pill',\r\n            },\r\n          ],\r\n        },\r\n        session2: [\r\n          {\r\n            name: 'Finance',\r\n            color: 'default',\r\n            background: '#ECEEF1',\r\n            type: 'normal',\r\n          },\r\n          {\r\n            name: 'Ascendion',\r\n            color: 'default',\r\n            background: '#ECEEF1',\r\n            type: 'normal',\r\n          },\r\n        ],\r\n        session3: [\r\n          { iconName: 'user', label: '<EMAIL>' },\r\n          { iconName: 'calendar-days', label: '15 May 2025' },\r\n        ],\r\n        session4: {\r\n          status: 'Pending Approval',\r\n          iconName: 'circle-check',\r\n        },\r\n      },\r\n      {\r\n        session1: {\r\n          title: 'Customer Support Chatbot',\r\n          labels: [\r\n            {\r\n              name: 'Agent',\r\n              color: 'info',\r\n              background: '#B6F0FA',\r\n              type: 'pill',\r\n            },\r\n            {\r\n              name: 'Medium',\r\n              color: 'warning',\r\n              background: '#FFD600',\r\n              type: 'pill',\r\n            },\r\n          ],\r\n        },\r\n        session2: [\r\n          {\r\n            name: 'Support',\r\n            color: 'default',\r\n            background: '#ECEEF1',\r\n            type: 'normal',\r\n          },\r\n          {\r\n            name: 'Ascendion',\r\n            color: 'default',\r\n            background: '#ECEEF1',\r\n            type: 'normal',\r\n          },\r\n        ],\r\n        session3: [\r\n          { iconName: 'user', label: '<EMAIL>' },\r\n          { iconName: 'calendar-days', label: '18 May 2025' },\r\n        ],\r\n        session4: {\r\n          status: 'Awaiting Review',\r\n          iconName: 'circle-check',\r\n        },\r\n      },\r\n      {\r\n        session1: {\r\n          title: 'AI-Powered Code Review Assistant',\r\n          labels: [\r\n            {\r\n              name: 'Agent',\r\n              color: 'info',\r\n              background: '#B6F0FA',\r\n              type: 'pill',\r\n            },\r\n            {\r\n              name: 'Low',\r\n              color: 'success',\r\n              background: '#4CAF50',\r\n              type: 'pill',\r\n            },\r\n          ],\r\n        },\r\n        session2: [\r\n          {\r\n            name: 'Engineering',\r\n            color: 'default',\r\n            background: '#ECEEF1',\r\n            type: 'normal',\r\n          },\r\n          {\r\n            name: 'Ascendion',\r\n            color: 'default',\r\n            background: '#ECEEF1',\r\n            type: 'normal',\r\n          },\r\n        ],\r\n        session3: [\r\n          { iconName: 'user', label: '<EMAIL>' },\r\n          { iconName: 'calendar-days', label: '20 May 2025' },\r\n        ],\r\n        session4: {\r\n          status: 'Completed',\r\n          iconName: 'circle-check',\r\n        },\r\n      },\r\n    ],\r\n    footer: {},\r\n  };\r\n  dashboardDetails: DashboardDetailI[] = [\r\n    // {\r\n    //   icon: '',\r\n    //   title: '',\r\n    //   value: 0,\r\n    //   subtitle: '',\r\n    //   badge: '',\r\n    // },\r\n    // {\r\n    //   icon: 'trending-up',\r\n    //   title: 'Active Workflows',\r\n    //   value: 80,\r\n    //   subtitle: 'Agents actively running',\r\n    //   badge: 'S',\r\n    // },\r\n    // {\r\n    //   icon: 'trending-up',\r\n    //   title: 'Active Agents',\r\n    //   value: 1240,\r\n    //   subtitle: 'Agents active',\r\n    //   badge: 'S',\r\n    // },\r\n    // {\r\n    //   icon: 'trending-up',\r\n    //   title: 'Agents Approval',\r\n    //   value: 120,\r\n    //   subtitle: 'Agents actively running',\r\n    //   badge: 'S',\r\n    // },\r\n    // {\r\n    //   icon: 'trending-up',\r\n    //   title: 'Active Workflows',\r\n    //   value: 80,\r\n    //   subtitle: 'Agents actively running',\r\n    //   badge: 'S',\r\n    // },\r\n  ];\r\n  // Dashboard metrics\r\n  totalAgents: number = 0;\r\n  newAgentsCreated: number = 50;\r\n\r\n  totalWorkflows: number = 124;\r\n  newWorkflowsCreated: number = 50;\r\n\r\n  totalUsers: number = 300;\r\n  newUsersAdded: number = 50;\r\n\r\n  selectedActiveMonitoring = ActiveMonitorings.tools;\r\n\r\n  activityMonitoringCount = 5;\r\n\r\n  // Current user info (would come from auth service in real app)\r\n  currentUser: { name: string } = { name: 'Akash Raj' };\r\n\r\n  // Footer info\r\n  currentYear: number = new Date().getFullYear();\r\n\r\n  // User logs data\r\n  userLogs: UserLog[] = [\r\n    {\r\n      id: '1',\r\n      username: 'Michael Scott',\r\n      avatar: 'assets/images/avatars/michael-scott.jpg',\r\n      securityToken: 'X9D7K2B4MQ',\r\n      status: 'Active',\r\n    },\r\n    {\r\n      id: '2',\r\n      username: 'Jim Halpert',\r\n      avatar: 'assets/images/avatars/jim-halpert.jpg',\r\n      securityToken: 'QWE2349SDF',\r\n      status: 'Active',\r\n    },\r\n    {\r\n      id: '3',\r\n      username: 'Dwight Schrute',\r\n      avatar: 'assets/images/avatars/dwight-schrute.jpg',\r\n      securityToken: 'OWDF1230JS',\r\n      status: 'Active',\r\n    },\r\n    {\r\n      id: '4',\r\n      username: 'Kevin Malone',\r\n      avatar: 'assets/images/avatars/kevin-malone.jpg',\r\n      securityToken: 'SDVP9I23EJ',\r\n      status: 'Active',\r\n    },\r\n  ];\r\n\r\n  // Model usage data\r\n  modelUsage: ModelUsage[] = [\r\n    {\r\n      id: '1',\r\n      name: 'GPT 3',\r\n      publisher: {\r\n        name: 'Open AI',\r\n        logo: 'assets/images/logos/openai-logo.png',\r\n      },\r\n      agentsCount: 48,\r\n    },\r\n    {\r\n      id: '2',\r\n      name: 'Claude 2',\r\n      publisher: {\r\n        name: 'Anthropic',\r\n        logo: 'assets/images/logos/anthropic-logo.png',\r\n      },\r\n      agentsCount: 24,\r\n    },\r\n    {\r\n      id: '3',\r\n      name: 'Gemini',\r\n      publisher: {\r\n        name: 'Google',\r\n        logo: 'assets/images/logos/google-logo.png',\r\n      },\r\n      agentsCount: 20,\r\n    },\r\n    {\r\n      id: '4',\r\n      name: 'GPT-4',\r\n      publisher: {\r\n        name: 'Open AI',\r\n        logo: 'assets/images/logos/openai-logo.png',\r\n      },\r\n      agentsCount: 8,\r\n    },\r\n  ];\r\n\r\n  // Pending approvals\r\n  pendingApprovals: PendingApproval[] = [\r\n    {\r\n      id: '1',\r\n      name: 'Test Ruby to Springboot',\r\n      type: 'migration',\r\n    },\r\n    {\r\n      id: '2',\r\n      name: 'Customer Support Chatbot',\r\n      type: 'agent',\r\n    },\r\n    {\r\n      id: '3',\r\n      name: 'Invoice Processing & Approval',\r\n      type: 'workflow',\r\n    },\r\n    {\r\n      id: '4',\r\n      name: 'Invoice Processing & Approval',\r\n      type: 'workflow',\r\n    },\r\n    {\r\n      id: '5',\r\n      name: 'AI-Powered Code Review Assistant',\r\n      type: 'agent',\r\n    },\r\n    {\r\n      id: '6',\r\n      name: 'AI-Powered Code Review Assistant',\r\n      type: 'agent',\r\n    },\r\n    {\r\n      id: '7',\r\n      name: 'AI-Powered Code Review Assistant',\r\n      type: 'agent',\r\n    },\r\n    {\r\n      id: '8',\r\n      name: 'AI-Powered Code Review Assistant',\r\n      type: 'agent',\r\n    },\r\n  ];\r\n\r\n  activityMonitoring: ActivityMonitoringI[] = [\r\n    // {\r\n    //   agentName: 'Agent Name will be here',\r\n    //   status: 'Active',\r\n    //   user: 'Michael Scott',\r\n    //   date: '12 June 2025',\r\n    //   totalRuns: 264,\r\n    // },\r\n    // {\r\n    //   agentName: 'Agent Name will be here',\r\n    //   status: 'Active',\r\n    //   user: 'Michael Scott',\r\n    //   date: '12 June 2025',\r\n    //   totalRuns: 264,\r\n    // },\r\n    // {\r\n    //   agentName: 'Agent Name will be here',\r\n    //   status: 'Active',\r\n    //   user: 'Michael Scott',\r\n    //   date: '12 June 2025',\r\n    //   totalRuns: 264,\r\n    // },\r\n    // {\r\n    //   agentName: 'Agent Name will be here',\r\n    //   status: 'Active',\r\n    //   user: 'Michael Scott',\r\n    //   date: '12 June 2025',\r\n    //   totalRuns: 264,\r\n    // },\r\n    // {\r\n    //   agentName: 'Agent Name will be here',\r\n    //   status: 'Active',\r\n    //   user: 'Michael Scott',\r\n    //   date: '12 June 2025',\r\n    //   totalRuns: 264,\r\n    // },\r\n  ];\r\n  workflowApprovals: any[] = [];\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private apiService: SharedApiServiceService,\r\n    private approvalService: ApprovalService,\r\n  ) {}\r\n\r\n  // seprating the tool and agents.\r\n  activityMonitoringTools: ActivityMonitoringI[] = [];\r\n  activityMonitoringAgents: ActivityMonitoringI[] = [];\r\n\r\n  setActiveMonitoringData() {\r\n    // INsted of re-formating, just switch the values\r\n    if (this.selectedActiveMonitoring === ActiveMonitorings.tools) {\r\n      this.activityMonitoring = this.activityMonitoringTools;\r\n    } else {\r\n      this.activityMonitoring = this.activityMonitoringAgents;\r\n    }\r\n  }\r\n\r\n  mapToActivityMonitoringItem = (\r\n    item: any,\r\n    nameKey: APIKeys,\r\n    usageKey: APIKeys,\r\n  ): ActivityMonitoringI => ({\r\n    agentName: item[nameKey] || '',\r\n    totalRuns: Number(item[usageKey]) || 0,\r\n    status: '',\r\n    user: '',\r\n    date: '',\r\n  });\r\n\r\n  public toRequestStatus(value: string | null | undefined): RequestStatus {\r\n    return value === 'approved' || value === 'rejected' || value === 'review'\r\n      ? value\r\n      : 'review';\r\n  }\r\n\r\n  initApiCalls() {\r\n    const date = new Date();\r\n    const dateEnd = this.apiService.formatDate(date);\r\n    date.setDate(1);\r\n    const dateStart = this.apiService.formatDate(date);\r\n    this.apiService\r\n      .getCollabrativeAgentAnalytics(dateStart, dateEnd)\r\n      .subscribe((response: Record<string, any>) => {\r\n        // setting dashboard card values\r\n        this.dashboardDetails = DASHBOARD_CARD_DETAILS.map((cardDetail) => {\r\n          cardDetail.value =\r\n            (response[cardDetail.field] as number) || this.totalAgents;\r\n          return cardDetail as DashboardDetailI;\r\n        });\r\n\r\n        // Active Monitoring\r\n        // Extracting tools and agents to seprate varibales to reduce frequent re-formatig as the dropdown value changes\r\n        this.activityMonitoringTools = (response[APIKeys.toolUsage] as any[])\r\n          .slice(0, this.activityMonitoringCount)\r\n          .map((toolUsage) =>\r\n            this.mapToActivityMonitoringItem(\r\n              toolUsage,\r\n              APIKeys.toolName,\r\n              APIKeys.usageCount,\r\n            ),\r\n          );\r\n\r\n        this.activityMonitoringAgents = (\r\n          response[APIKeys.agentMetrics] as any[]\r\n        )\r\n          .slice(0, this.activityMonitoringCount)\r\n          .map((agentMetric) =>\r\n            this.mapToActivityMonitoringItem(\r\n              agentMetric,\r\n              APIKeys.agentName,\r\n              APIKeys.workflowCount,\r\n            ),\r\n          );\r\n\r\n        this.setActiveMonitoringData();\r\n      });\r\n\r\n    this.approvalService.getAllReviewAgents(1, 100, false).subscribe((res) => {\r\n      const agentReviewDetails = res?.agentReviewDetails || [];\r\n      // Extracting agents which are under review\r\n      this.totalAgents =\r\n        agentReviewDetails.filter(\r\n          (agentReviewDetail: any) => agentReviewDetail.status !== 'approved',\r\n        )?.length || 0;\r\n\r\n      // If this API call is late then will set approval count here.\r\n      const toolCardDetial = this.dashboardDetails.at(-1);\r\n      if (toolCardDetial) {\r\n        toolCardDetial.value = this.totalAgents;\r\n      }\r\n    });\r\n\r\n    this.approvalService\r\n      .getAllReviewWorkflows(1, 5, false)\r\n      .subscribe((response) => {\r\n        const type = 'workflow';\r\n\r\n        this.workflowApprovals = response.workflowReviewDetails?.map(\r\n          (req: any) => {\r\n            const statusIcons: Record<RequestStatus, string> = {\r\n              approved: 'circle-check-big',\r\n              rejected: 'circle-x',\r\n              review: 'clock',\r\n            };\r\n            const statusTexts: Record<RequestStatus, string> = {\r\n              approved: this.labels.approved,\r\n              rejected: this.labels.rejected,\r\n              review: this.labels.review,\r\n            };\r\n            const statusKey = this.toRequestStatus(req?.status);\r\n            let specificId = 0;\r\n            let title = '';\r\n\r\n            specificId = req.workflowId;\r\n            title = req.workflowName;\r\n\r\n            return {\r\n              id: req.id,\r\n              refId: specificId,\r\n              type: type,\r\n              session1: {\r\n                title: title,\r\n                labels: [\r\n                  {\r\n                    name: type,\r\n                    color: 'success',\r\n                    background: 'red',\r\n                    type: 'normal',\r\n                  },\r\n                  {\r\n                    name: req.changeRequestType,\r\n                    color:\r\n                      req.changeRequestType === 'update' ? 'error' : 'info',\r\n                    background: 'red',\r\n                    type: 'pill',\r\n                  },\r\n                ],\r\n              },\r\n              session2: [\r\n                {\r\n                  name: type,\r\n                  color: 'default',\r\n                  background: 'red',\r\n                  type: 'normal',\r\n                },\r\n                {\r\n                  name: req.status,\r\n                  color: 'default',\r\n                  background: 'red',\r\n                  type: 'normal',\r\n                },\r\n              ],\r\n              session3: [\r\n                {\r\n                  iconName: 'user',\r\n                  label: req.requestedBy,\r\n                },\r\n                {\r\n                  iconName: 'calendar-days',\r\n                  label: formatDate(req?.requestedAt, 'dd MMM yyyy', 'en-IN'),\r\n                },\r\n              ],\r\n              session4: {\r\n                status: statusTexts[statusKey],\r\n                iconName: statusIcons[statusKey],\r\n              },\r\n            };\r\n          },\r\n        );\r\n      });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    // The layout is now managed with fixed heights in CSS\r\n    // No need for recalculateLayout\r\n\r\n    this.initApiCalls();\r\n  }\r\n\r\n  // Navigate to a route\r\n  navigateTo(route: string): void {\r\n    this.router.navigate([route]);\r\n  }\r\n\r\n  // Approve a pending item\r\n  approveItem(id: string): void {\r\n    // In a real app, you would call a service to approve the item\r\n    // Then remove it from the pendingApprovals array or refresh the list\r\n  }\r\n\r\n  // Test method to demonstrate loader functionality\r\n  testLoader(): void {\r\n    this.apiService.getConfigLabels().subscribe({\r\n      next: (response) => {},\r\n      error: (error) => {},\r\n    });\r\n  }\r\n\r\n  uClick(index: any) {\r\n    this.router.navigate(['/approval']);\r\n  }\r\n\r\n  onSelectionChange(data: any) {\r\n    this.selectedActiveMonitoring = data.selectedValue;\r\n    this.setActiveMonitoringData();\r\n  }\r\n\r\n  onQuickActionClick(action: any) {\r\n    console.log('Quick action clicked:', action);\r\n    // Handle navigation or action based on action.id\r\n    switch (action.id) {\r\n      case 'build-agent':\r\n        this.router.navigate(['/build/agents/create']);\r\n        break;\r\n      case 'build-workflow':\r\n        this.router.navigate(['/build/workflows/create']);\r\n        break;\r\n      case 'create-prompt':\r\n        this.router.navigate(['/libraries/prompts/create']);\r\n        break;\r\n      case 'create-tool':\r\n        this.router.navigate(['/libraries/tools/create']);\r\n        break;\r\n      case 'create-guardrail':\r\n        this.router.navigate(['/libraries/guardrails/create']);\r\n        break;\r\n      case 'create-knowledge-base':\r\n        this.router.navigate(['/libraries/knowledge-base/create']);\r\n        break;\r\n      default:\r\n        console.log('Unknown action:', action.id);\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,YAAY,EAAEC,UAAU,QAAQ,iBAAiB;AAC1D,SAASC,YAAY,QAAQ,iBAAiB;AAG9C,SACEC,qBAAqB,EACrBC,eAAe,EACfC,eAAe,EACfC,iBAAiB,EAEjBC,aAAa,EACbC,aAAa,EACbC,iBAAiB,QACZ,wBAAwB;AAC/B,SAASC,qBAAqB,QAAQ,+DAA+D;AAMrG,SACEC,yBAAyB,EACzBC,iBAAiB,EACjBC,OAAO,EACPC,sBAAsB,QACjB,+BAA+B;AAEtC,OAAOC,YAAY,MAAM,qCAAqC;AA6CvD,IAAMC,kBAAkB,GAAxB,MAAMA,kBAAkB;EA+anBC,MAAA;EACAC,UAAA;EACAC,eAAA;EAhbVC,OAAO,GAAqBT,yBAAyB;EAC9CU,MAAM,GAAQN,YAAY,CAACM,MAAM;EAExCC,YAAY,GAAU,CACpB;IAAEC,EAAE,EAAE,aAAa;IAAEC,KAAK,EAAE,aAAa;IAAEC,IAAI,EAAE;EAAiB,CAAE,EACpE;IACEF,EAAE,EAAE,gBAAgB;IACpBC,KAAK,EAAE,gBAAgB;IACvBC,IAAI,EAAE;GACP,EACD;IAAEF,EAAE,EAAE,eAAe;IAAEC,KAAK,EAAE,eAAe;IAAEC,IAAI,EAAE;EAAkB,CAAE,EACzE;IAAEF,EAAE,EAAE,aAAa;IAAEC,KAAK,EAAE,aAAa;IAAEC,IAAI,EAAE;EAAgB,CAAE,EACnE;IACEF,EAAE,EAAE,kBAAkB;IACtBC,KAAK,EAAE,kBAAkB;IACzBC,IAAI,EAAE;GACP,EACD;IACEF,EAAE,EAAE,uBAAuB;IAC3BC,KAAK,EAAE,uBAAuB;IAC9BC,IAAI,EAAE;GACP,CACF;EAEDC,gBAAgB,GAAG;IACjBC,MAAM,EAAE;MACNC,QAAQ,EAAE,EAAE;MAAE;MACdC,KAAK,EAAE,yBAAyB;MAChCC,OAAO,EAAE;KACV;IACDC,QAAQ,EAAE,CACR;MACEC,QAAQ,EAAE;QACRH,KAAK,EAAE,+BAA+B;QACtCR,MAAM,EAAE,CACN;UACEY,IAAI,EAAE,OAAO;UACbC,KAAK,EAAE,MAAM;UACbC,UAAU,EAAE,SAAS;UACrBC,IAAI,EAAE;SACP,EACD;UACEH,IAAI,EAAE,MAAM;UACZC,KAAK,EAAE,OAAO;UACdC,UAAU,EAAE,SAAS;UACrBC,IAAI,EAAE;SACP;OAEJ;MACDC,QAAQ,EAAE,CACR;QACEJ,IAAI,EAAE,YAAY;QAClBC,KAAK,EAAE,SAAS;QAChBC,UAAU,EAAE,SAAS;QACrBC,IAAI,EAAE;OACP,EACD;QACEH,IAAI,EAAE,WAAW;QACjBC,KAAK,EAAE,SAAS;QAChBC,UAAU,EAAE,SAAS;QACrBC,IAAI,EAAE;OACP,EACD;QACEH,IAAI,EAAE,sBAAsB;QAC5BC,KAAK,EAAE,SAAS;QAChBC,UAAU,EAAE,SAAS;QACrBC,IAAI,EAAE;OACP,EACD;QACEH,IAAI,EAAE,kBAAkB;QACxBC,KAAK,EAAE,SAAS;QAChBC,UAAU,EAAE,SAAS;QACrBC,IAAI,EAAE;OACP,EACD;QACEH,IAAI,EAAE,aAAa;QACnBC,KAAK,EAAE,SAAS;QAChBC,UAAU,EAAE,SAAS;QACrBC,IAAI,EAAE;OACP,CACF;MACDE,QAAQ,EAAE,CACR;QAAEV,QAAQ,EAAE,MAAM;QAAEJ,KAAK,EAAE;MAA4B,CAAE,EACzD;QAAEI,QAAQ,EAAE,eAAe;QAAEJ,KAAK,EAAE;MAAa,CAAE,CACpD;MACDe,QAAQ,EAAE;QACRC,MAAM,EAAE,kBAAkB;QAC1BZ,QAAQ,EAAE;;KAEb;IACD;IACA;MACEI,QAAQ,EAAE;QACRH,KAAK,EAAE,+BAA+B;QACtCR,MAAM,EAAE,CACN;UACEY,IAAI,EAAE,UAAU;UAChBC,KAAK,EAAE,MAAM;UACbC,UAAU,EAAE,SAAS;UACrBC,IAAI,EAAE;SACP,EACD;UACEH,IAAI,EAAE,UAAU;UAChBC,KAAK,EAAE,OAAO;UACdC,UAAU,EAAE,SAAS;UACrBC,IAAI,EAAE;SACP;OAEJ;MACDC,QAAQ,EAAE,CACR;QACEJ,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,SAAS;QAChBC,UAAU,EAAE,SAAS;QACrBC,IAAI,EAAE;OACP,EACD;QACEH,IAAI,EAAE,WAAW;QACjBC,KAAK,EAAE,SAAS;QAChBC,UAAU,EAAE,SAAS;QACrBC,IAAI,EAAE;OACP,CACF;MACDE,QAAQ,EAAE,CACR;QAAEV,QAAQ,EAAE,MAAM;QAAEJ,KAAK,EAAE;MAAmB,CAAE,EAChD;QAAEI,QAAQ,EAAE,eAAe;QAAEJ,KAAK,EAAE;MAAa,CAAE,CACpD;MACDe,QAAQ,EAAE;QACRC,MAAM,EAAE,kBAAkB;QAC1BZ,QAAQ,EAAE;;KAEb,EACD;MACEI,QAAQ,EAAE;QACRH,KAAK,EAAE,0BAA0B;QACjCR,MAAM,EAAE,CACN;UACEY,IAAI,EAAE,OAAO;UACbC,KAAK,EAAE,MAAM;UACbC,UAAU,EAAE,SAAS;UACrBC,IAAI,EAAE;SACP,EACD;UACEH,IAAI,EAAE,QAAQ;UACdC,KAAK,EAAE,SAAS;UAChBC,UAAU,EAAE,SAAS;UACrBC,IAAI,EAAE;SACP;OAEJ;MACDC,QAAQ,EAAE,CACR;QACEJ,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,SAAS;QAChBC,UAAU,EAAE,SAAS;QACrBC,IAAI,EAAE;OACP,EACD;QACEH,IAAI,EAAE,WAAW;QACjBC,KAAK,EAAE,SAAS;QAChBC,UAAU,EAAE,SAAS;QACrBC,IAAI,EAAE;OACP,CACF;MACDE,QAAQ,EAAE,CACR;QAAEV,QAAQ,EAAE,MAAM;QAAEJ,KAAK,EAAE;MAAmB,CAAE,EAChD;QAAEI,QAAQ,EAAE,eAAe;QAAEJ,KAAK,EAAE;MAAa,CAAE,CACpD;MACDe,QAAQ,EAAE;QACRC,MAAM,EAAE,iBAAiB;QACzBZ,QAAQ,EAAE;;KAEb,EACD;MACEI,QAAQ,EAAE;QACRH,KAAK,EAAE,kCAAkC;QACzCR,MAAM,EAAE,CACN;UACEY,IAAI,EAAE,OAAO;UACbC,KAAK,EAAE,MAAM;UACbC,UAAU,EAAE,SAAS;UACrBC,IAAI,EAAE;SACP,EACD;UACEH,IAAI,EAAE,KAAK;UACXC,KAAK,EAAE,SAAS;UAChBC,UAAU,EAAE,SAAS;UACrBC,IAAI,EAAE;SACP;OAEJ;MACDC,QAAQ,EAAE,CACR;QACEJ,IAAI,EAAE,aAAa;QACnBC,KAAK,EAAE,SAAS;QAChBC,UAAU,EAAE,SAAS;QACrBC,IAAI,EAAE;OACP,EACD;QACEH,IAAI,EAAE,WAAW;QACjBC,KAAK,EAAE,SAAS;QAChBC,UAAU,EAAE,SAAS;QACrBC,IAAI,EAAE;OACP,CACF;MACDE,QAAQ,EAAE,CACR;QAAEV,QAAQ,EAAE,MAAM;QAAEJ,KAAK,EAAE;MAAsB,CAAE,EACnD;QAAEI,QAAQ,EAAE,eAAe;QAAEJ,KAAK,EAAE;MAAa,CAAE,CACpD;MACDe,QAAQ,EAAE;QACRC,MAAM,EAAE,WAAW;QACnBZ,QAAQ,EAAE;;KAEb,CACF;IACDa,MAAM,EAAE;GACT;EACDC,gBAAgB,GAAuB;IACrC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA,CACD;EACD;EACAC,WAAW,GAAW,CAAC;EACvBC,gBAAgB,GAAW,EAAE;EAE7BC,cAAc,GAAW,GAAG;EAC5BC,mBAAmB,GAAW,EAAE;EAEhCC,UAAU,GAAW,GAAG;EACxBC,aAAa,GAAW,EAAE;EAE1BC,wBAAwB,GAAGrC,iBAAiB,CAACsC,KAAK;EAElDC,uBAAuB,GAAG,CAAC;EAE3B;EACAC,WAAW,GAAqB;IAAEnB,IAAI,EAAE;EAAW,CAAE;EAErD;EACAoB,WAAW,GAAW,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;EAE9C;EACAC,QAAQ,GAAc,CACpB;IACEjC,EAAE,EAAE,GAAG;IACPkC,QAAQ,EAAE,eAAe;IACzBC,MAAM,EAAE,yCAAyC;IACjDC,aAAa,EAAE,YAAY;IAC3BnB,MAAM,EAAE;GACT,EACD;IACEjB,EAAE,EAAE,GAAG;IACPkC,QAAQ,EAAE,aAAa;IACvBC,MAAM,EAAE,uCAAuC;IAC/CC,aAAa,EAAE,YAAY;IAC3BnB,MAAM,EAAE;GACT,EACD;IACEjB,EAAE,EAAE,GAAG;IACPkC,QAAQ,EAAE,gBAAgB;IAC1BC,MAAM,EAAE,0CAA0C;IAClDC,aAAa,EAAE,YAAY;IAC3BnB,MAAM,EAAE;GACT,EACD;IACEjB,EAAE,EAAE,GAAG;IACPkC,QAAQ,EAAE,cAAc;IACxBC,MAAM,EAAE,wCAAwC;IAChDC,aAAa,EAAE,YAAY;IAC3BnB,MAAM,EAAE;GACT,CACF;EAED;EACAoB,UAAU,GAAiB,CACzB;IACErC,EAAE,EAAE,GAAG;IACPU,IAAI,EAAE,OAAO;IACb4B,SAAS,EAAE;MACT5B,IAAI,EAAE,SAAS;MACf6B,IAAI,EAAE;KACP;IACDC,WAAW,EAAE;GACd,EACD;IACExC,EAAE,EAAE,GAAG;IACPU,IAAI,EAAE,UAAU;IAChB4B,SAAS,EAAE;MACT5B,IAAI,EAAE,WAAW;MACjB6B,IAAI,EAAE;KACP;IACDC,WAAW,EAAE;GACd,EACD;IACExC,EAAE,EAAE,GAAG;IACPU,IAAI,EAAE,QAAQ;IACd4B,SAAS,EAAE;MACT5B,IAAI,EAAE,QAAQ;MACd6B,IAAI,EAAE;KACP;IACDC,WAAW,EAAE;GACd,EACD;IACExC,EAAE,EAAE,GAAG;IACPU,IAAI,EAAE,OAAO;IACb4B,SAAS,EAAE;MACT5B,IAAI,EAAE,SAAS;MACf6B,IAAI,EAAE;KACP;IACDC,WAAW,EAAE;GACd,CACF;EAED;EACAC,gBAAgB,GAAsB,CACpC;IACEzC,EAAE,EAAE,GAAG;IACPU,IAAI,EAAE,yBAAyB;IAC/BG,IAAI,EAAE;GACP,EACD;IACEb,EAAE,EAAE,GAAG;IACPU,IAAI,EAAE,0BAA0B;IAChCG,IAAI,EAAE;GACP,EACD;IACEb,EAAE,EAAE,GAAG;IACPU,IAAI,EAAE,+BAA+B;IACrCG,IAAI,EAAE;GACP,EACD;IACEb,EAAE,EAAE,GAAG;IACPU,IAAI,EAAE,+BAA+B;IACrCG,IAAI,EAAE;GACP,EACD;IACEb,EAAE,EAAE,GAAG;IACPU,IAAI,EAAE,kCAAkC;IACxCG,IAAI,EAAE;GACP,EACD;IACEb,EAAE,EAAE,GAAG;IACPU,IAAI,EAAE,kCAAkC;IACxCG,IAAI,EAAE;GACP,EACD;IACEb,EAAE,EAAE,GAAG;IACPU,IAAI,EAAE,kCAAkC;IACxCG,IAAI,EAAE;GACP,EACD;IACEb,EAAE,EAAE,GAAG;IACPU,IAAI,EAAE,kCAAkC;IACxCG,IAAI,EAAE;GACP,CACF;EAED6B,kBAAkB,GAA0B;IAC1C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA,CACD;EACDC,iBAAiB,GAAU,EAAE;EAE7BC,YACUlD,MAAc,EACdC,UAAmC,EACnCC,eAAgC;IAFhC,KAAAF,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,eAAe,GAAfA,eAAe;EACtB;EAEH;EACAiD,uBAAuB,GAA0B,EAAE;EACnDC,wBAAwB,GAA0B,EAAE;EAEpDC,uBAAuBA,CAAA;IACrB;IACA,IAAI,IAAI,CAACrB,wBAAwB,KAAKrC,iBAAiB,CAACsC,KAAK,EAAE;MAC7D,IAAI,CAACe,kBAAkB,GAAG,IAAI,CAACG,uBAAuB;IACxD,CAAC,MAAM;MACL,IAAI,CAACH,kBAAkB,GAAG,IAAI,CAACI,wBAAwB;IACzD;EACF;EAEAE,2BAA2B,GAAGA,CAC5BC,IAAS,EACTC,OAAgB,EAChBC,QAAiB,MACQ;IACzBC,SAAS,EAAEH,IAAI,CAACC,OAAO,CAAC,IAAI,EAAE;IAC9BG,SAAS,EAAEC,MAAM,CAACL,IAAI,CAACE,QAAQ,CAAC,CAAC,IAAI,CAAC;IACtClC,MAAM,EAAE,EAAE;IACVsC,IAAI,EAAE,EAAE;IACRC,IAAI,EAAE;GACP,CAAC;EAEKC,eAAeA,CAACC,KAAgC;IACrD,OAAOA,KAAK,KAAK,UAAU,IAAIA,KAAK,KAAK,UAAU,IAAIA,KAAK,KAAK,QAAQ,GACrEA,KAAK,GACL,QAAQ;EACd;EAEAC,YAAYA,CAAA;IACV,MAAMH,IAAI,GAAG,IAAIzB,IAAI,EAAE;IACvB,MAAM6B,OAAO,GAAG,IAAI,CAACjE,UAAU,CAACjB,UAAU,CAAC8E,IAAI,CAAC;IAChDA,IAAI,CAACK,OAAO,CAAC,CAAC,CAAC;IACf,MAAMC,SAAS,GAAG,IAAI,CAACnE,UAAU,CAACjB,UAAU,CAAC8E,IAAI,CAAC;IAClD,IAAI,CAAC7D,UAAU,CACZoE,6BAA6B,CAACD,SAAS,EAAEF,OAAO,CAAC,CACjDI,SAAS,CAAEC,QAA6B,IAAI;MAC3C;MACA,IAAI,CAAC9C,gBAAgB,GAAG5B,sBAAsB,CAAC2E,GAAG,CAAEC,UAAU,IAAI;QAChEA,UAAU,CAACT,KAAK,GACbO,QAAQ,CAACE,UAAU,CAACC,KAAK,CAAY,IAAI,IAAI,CAAChD,WAAW;QAC5D,OAAO+C,UAA8B;MACvC,CAAC,CAAC;MAEF;MACA;MACA,IAAI,CAACtB,uBAAuB,GAAIoB,QAAQ,CAAC3E,OAAO,CAAC+E,SAAS,CAAW,CAClEC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC1C,uBAAuB,CAAC,CACtCsC,GAAG,CAAEG,SAAS,IACb,IAAI,CAACrB,2BAA2B,CAC9BqB,SAAS,EACT/E,OAAO,CAACiF,QAAQ,EAChBjF,OAAO,CAACkF,UAAU,CACnB,CACF;MAEH,IAAI,CAAC1B,wBAAwB,GAC3BmB,QAAQ,CAAC3E,OAAO,CAACmF,YAAY,CAC9B,CACEH,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC1C,uBAAuB,CAAC,CACtCsC,GAAG,CAAEQ,WAAW,IACf,IAAI,CAAC1B,2BAA2B,CAC9B0B,WAAW,EACXpF,OAAO,CAAC8D,SAAS,EACjB9D,OAAO,CAACqF,aAAa,CACtB,CACF;MAEH,IAAI,CAAC5B,uBAAuB,EAAE;IAChC,CAAC,CAAC;IAEJ,IAAI,CAACnD,eAAe,CAACgF,kBAAkB,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAACZ,SAAS,CAAEa,GAAG,IAAI;MACvE,MAAMC,kBAAkB,GAAGD,GAAG,EAAEC,kBAAkB,IAAI,EAAE;MACxD;MACA,IAAI,CAAC1D,WAAW,GACd0D,kBAAkB,CAACC,MAAM,CACtBC,iBAAsB,IAAKA,iBAAiB,CAAC/D,MAAM,KAAK,UAAU,CACpE,EAAEgE,MAAM,IAAI,CAAC;MAEhB;MACA,MAAMC,cAAc,GAAG,IAAI,CAAC/D,gBAAgB,CAACgE,EAAE,CAAC,CAAC,CAAC,CAAC;MACnD,IAAID,cAAc,EAAE;QAClBA,cAAc,CAACxB,KAAK,GAAG,IAAI,CAACtC,WAAW;MACzC;IACF,CAAC,CAAC;IAEF,IAAI,CAACxB,eAAe,CACjBwF,qBAAqB,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAClCpB,SAAS,CAAEC,QAAQ,IAAI;MACtB,MAAMpD,IAAI,GAAG,UAAU;MAEvB,IAAI,CAAC8B,iBAAiB,GAAGsB,QAAQ,CAACoB,qBAAqB,EAAEnB,GAAG,CACzDoB,GAAQ,IAAI;QACX,MAAMC,WAAW,GAAkC;UACjDC,QAAQ,EAAE,kBAAkB;UAC5BC,QAAQ,EAAE,UAAU;UACpBC,MAAM,EAAE;SACT;QACD,MAAMC,WAAW,GAAkC;UACjDH,QAAQ,EAAE,IAAI,CAAC1F,MAAM,CAAC0F,QAAQ;UAC9BC,QAAQ,EAAE,IAAI,CAAC3F,MAAM,CAAC2F,QAAQ;UAC9BC,MAAM,EAAE,IAAI,CAAC5F,MAAM,CAAC4F;SACrB;QACD,MAAME,SAAS,GAAG,IAAI,CAACnC,eAAe,CAAC6B,GAAG,EAAErE,MAAM,CAAC;QACnD,IAAI4E,UAAU,GAAG,CAAC;QAClB,IAAIvF,KAAK,GAAG,EAAE;QAEduF,UAAU,GAAGP,GAAG,CAACQ,UAAU;QAC3BxF,KAAK,GAAGgF,GAAG,CAACS,YAAY;QAExB,OAAO;UACL/F,EAAE,EAAEsF,GAAG,CAACtF,EAAE;UACVgG,KAAK,EAAEH,UAAU;UACjBhF,IAAI,EAAEA,IAAI;UACVJ,QAAQ,EAAE;YACRH,KAAK,EAAEA,KAAK;YACZR,MAAM,EAAE,CACN;cACEY,IAAI,EAAEG,IAAI;cACVF,KAAK,EAAE,SAAS;cAChBC,UAAU,EAAE,KAAK;cACjBC,IAAI,EAAE;aACP,EACD;cACEH,IAAI,EAAE4E,GAAG,CAACW,iBAAiB;cAC3BtF,KAAK,EACH2E,GAAG,CAACW,iBAAiB,KAAK,QAAQ,GAAG,OAAO,GAAG,MAAM;cACvDrF,UAAU,EAAE,KAAK;cACjBC,IAAI,EAAE;aACP;WAEJ;UACDC,QAAQ,EAAE,CACR;YACEJ,IAAI,EAAEG,IAAI;YACVF,KAAK,EAAE,SAAS;YAChBC,UAAU,EAAE,KAAK;YACjBC,IAAI,EAAE;WACP,EACD;YACEH,IAAI,EAAE4E,GAAG,CAACrE,MAAM;YAChBN,KAAK,EAAE,SAAS;YAChBC,UAAU,EAAE,KAAK;YACjBC,IAAI,EAAE;WACP,CACF;UACDE,QAAQ,EAAE,CACR;YACEV,QAAQ,EAAE,MAAM;YAChBJ,KAAK,EAAEqF,GAAG,CAACY;WACZ,EACD;YACE7F,QAAQ,EAAE,eAAe;YACzBJ,KAAK,EAAEvB,UAAU,CAAC4G,GAAG,EAAEa,WAAW,EAAE,aAAa,EAAE,OAAO;WAC3D,CACF;UACDnF,QAAQ,EAAE;YACRC,MAAM,EAAE0E,WAAW,CAACC,SAAS,CAAC;YAC9BvF,QAAQ,EAAEkF,WAAW,CAACK,SAAS;;SAElC;MACH,CAAC,CACF;IACH,CAAC,CAAC;EACN;EAEAQ,QAAQA,CAAA;IACN;IACA;IAEA,IAAI,CAACzC,YAAY,EAAE;EACrB;EAEA;EACA0C,UAAUA,CAACC,KAAa;IACtB,IAAI,CAAC5G,MAAM,CAAC6G,QAAQ,CAAC,CAACD,KAAK,CAAC,CAAC;EAC/B;EAEA;EACAE,WAAWA,CAACxG,EAAU;IACpB;IACA;EAAA;EAGF;EACAyG,UAAUA,CAAA;IACR,IAAI,CAAC9G,UAAU,CAAC+G,eAAe,EAAE,CAAC1C,SAAS,CAAC;MAC1C2C,IAAI,EAAG1C,QAAQ,IAAI,CAAE,CAAC;MACtB2C,KAAK,EAAGA,KAAK,IAAI,CAAE;KACpB,CAAC;EACJ;EAEAC,MAAMA,CAACC,KAAU;IACf,IAAI,CAACpH,MAAM,CAAC6G,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;EACrC;EAEAQ,iBAAiBA,CAACC,IAAS;IACzB,IAAI,CAACtF,wBAAwB,GAAGsF,IAAI,CAACC,aAAa;IAClD,IAAI,CAAClE,uBAAuB,EAAE;EAChC;EAEAmE,kBAAkBA,CAACC,MAAW;IAC5BC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEF,MAAM,CAAC;IAC5C;IACA,QAAQA,MAAM,CAACnH,EAAE;MACf,KAAK,aAAa;QAChB,IAAI,CAACN,MAAM,CAAC6G,QAAQ,CAAC,CAAC,sBAAsB,CAAC,CAAC;QAC9C;MACF,KAAK,gBAAgB;QACnB,IAAI,CAAC7G,MAAM,CAAC6G,QAAQ,CAAC,CAAC,yBAAyB,CAAC,CAAC;QACjD;MACF,KAAK,eAAe;QAClB,IAAI,CAAC7G,MAAM,CAAC6G,QAAQ,CAAC,CAAC,2BAA2B,CAAC,CAAC;QACnD;MACF,KAAK,aAAa;QAChB,IAAI,CAAC7G,MAAM,CAAC6G,QAAQ,CAAC,CAAC,yBAAyB,CAAC,CAAC;QACjD;MACF,KAAK,kBAAkB;QACrB,IAAI,CAAC7G,MAAM,CAAC6G,QAAQ,CAAC,CAAC,8BAA8B,CAAC,CAAC;QACtD;MACF,KAAK,uBAAuB;QAC1B,IAAI,CAAC7G,MAAM,CAAC6G,QAAQ,CAAC,CAAC,kCAAkC,CAAC,CAAC;QAC1D;MACF;QACEa,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,MAAM,CAACnH,EAAE,CAAC;IAC7C;EACF;CACD;AAzpBYP,kBAAkB,GAAA6H,UAAA,EAlB9B9I,SAAS,CAAC;EACT+I,QAAQ,EAAE,eAAe;EACzBC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPhJ,YAAY,EACZE,YAAY,EACZO,iBAAiB,EACjBJ,eAAe,EACfF,qBAAqB,EACrBI,aAAa,EACbD,iBAAiB,EACjBI,qBAAqB,EACrBN,eAAe,EACfI,aAAa,CACd;EACDyI,WAAW,EAAE,4BAA4B;EACzCC,QAAQ,EAAE;CACX,CAAC,C,EACWlI,kBAAkB,CAypB9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}