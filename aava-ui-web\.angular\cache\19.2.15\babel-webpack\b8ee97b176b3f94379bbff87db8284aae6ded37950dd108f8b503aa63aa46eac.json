{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ButtonComponent } from '@ava/play-comp-library';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nconst _c0 = [\"editorContainer\"];\nconst _c1 = () => ({\n  background: \"linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)\",\n  \"--button-effect-color\": \"33, 90, 214\"\n});\nconst _c2 = () => ({\n  \"border\": \"2px solid transparent\",\n  \"background-image\": \"linear-gradient(#ffffff, #ffffff), linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)\",\n  \"background-origin\": \"border-box\",\n  \"background-clip\": \"padding-box, border-box\",\n  \"--button-effect-color\": \"33, 90, 214\"\n});\nfunction CodeEditorComponent_div_1_h3_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h3\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.title);\n  }\n}\nfunction CodeEditorComponent_div_1_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 14)(1, \"ava-button\", 15);\n    i0.ɵɵlistener(\"userClick\", function CodeEditorComponent_div_1_span_3_Template_ava_button_userClick_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onPrimaryButtonClick());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"customStyles\", i0.ɵɵpureFunction0(2, _c1))(\"disabled\", ctx_r0.state.loading || ctx_r0.isPrimaryButtonDisabled);\n  }\n}\nfunction CodeEditorComponent_div_1_div_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 18)(2, \"ava-button\", 19);\n    i0.ɵɵlistener(\"userClick\", function CodeEditorComponent_div_1_div_4_ng_container_1_Template_ava_button_userClick_2_listener() {\n      const i_r4 = i0.ɵɵrestoreView(_r3).index;\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.onActionButtonClick(i_r4));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const btn_r5 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", btn_r5.customClass);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", btn_r5.label)(\"iconName\", btn_r5.icon || \"\")(\"customStyles\", i0.ɵɵpureFunction0(6, _c2))(\"pill\", true)(\"disabled\", ctx_r0.state.loading);\n  }\n}\nfunction CodeEditorComponent_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵtemplate(1, CodeEditorComponent_div_1_div_4_ng_container_1_Template, 3, 7, \"ng-container\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.actionButtons);\n  }\n}\nfunction CodeEditorComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9);\n    i0.ɵɵtemplate(2, CodeEditorComponent_div_1_h3_2_Template, 2, 1, \"h3\", 10)(3, CodeEditorComponent_div_1_span_3_Template, 2, 3, \"span\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, CodeEditorComponent_div_1_div_4_Template, 2, 1, \"div\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.title);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.primaryButtonSelected.observed);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.actionButtons.length);\n  }\n}\nfunction CodeEditorComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵelement(1, \"div\", 21);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading Code Editor...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CodeEditorComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23);\n    i0.ɵɵtext(2, \"\\u26A0\\uFE0F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4, \"Failed to Load Editor\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"ava-button\", 24);\n    i0.ɵɵlistener(\"userClick\", function CodeEditorComponent_div_3_Template_ava_button_userClick_7_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.initializeEditor());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r0.state.errorMessage);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pill\", true)(\"customStyles\", i0.ɵɵpureFunction0(3, _c2));\n  }\n}\nfunction CodeEditorComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵelement(1, \"div\", 26);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CodeEditorComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"p\", 28)(2, \"strong\");\n    i0.ɵɵtext(3, \"Note:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.footerText, \" \");\n  }\n}\n/**\n * Monaco Code Editor Component\n *\n * @Input title - Editor title displayed in header\n * @Input value - Initial code value\n * @Input language - Programming language (python, javascript, etc.)\n * @Input theme - Editor theme (light/dark)\n * @Input height - Editor height (default: 400px)\n * @Input readonly - Make editor read-only\n * @Input Control - Angular FormControl for form integration\n * @Input actionButtons - Array of custom action buttons\n * @Input footerText - Footer note text\n *\n * @Output valueChange - Emits when code changes\n * @Output primaryButtonSelected - Emits when Run button clicked\n * @Output actionButtonClicked - Emits when action button clicked\n * @Output editorReady - Emits when editor is initialized\n *\n * Usage:\n * <app-code-editor\n *   title=\"Code Editor\"\n *   language=\"python\"\n *   [Control]=\"formControl\"\n *   (primaryButtonSelected)=\"runCode()\">\n * </app-code-editor>\n */\nexport let CodeEditorComponent = /*#__PURE__*/(() => {\n  class CodeEditorComponent {\n    cdr;\n    editorContainer;\n    title = '';\n    value = '';\n    language = 'python';\n    theme = 'light';\n    height = '400px';\n    readonly = false;\n    customCssClass = '';\n    placeholder = '';\n    Control = null;\n    actionButtons = [];\n    footerText = '';\n    isPrimaryButtonDisabled = false;\n    valueChange = new EventEmitter();\n    primaryButtonSelected = new EventEmitter();\n    actionButtonClicked = new EventEmitter();\n    editorReady = new EventEmitter();\n    state = {\n      loading: true,\n      error: false,\n      errorMessage: null,\n      processing: false\n    };\n    editor = null;\n    valueChangeTimeout = null;\n    monacoModule;\n    constructor(cdr) {\n      this.cdr = cdr;\n    }\n    ngOnChanges(changes) {\n      if (this.editor) {\n        if (changes['value'] && changes['value'].currentValue !== this.editor.getValue()) {\n          this.editor.setValue(changes['value'].currentValue || '');\n        }\n        if (changes['language'] && changes['language'].currentValue) {\n          this.monacoModule.editor.setModelLanguage(this.editor.getModel(), changes['language'].currentValue);\n        }\n        if (changes['theme'] && changes['theme'].currentValue) {\n          this.applyTheme();\n        }\n      }\n    }\n    ngOnInit() {\n      this.configureMonacoEnvironment();\n      if (this.Control?.value && !this.value) {\n        this.value = this.Control.value;\n      }\n    }\n    ngAfterViewInit() {\n      setTimeout(() => this.initializeEditor(), 100);\n    }\n    ngOnDestroy() {\n      this.editor?.dispose();\n    }\n    configureMonacoEnvironment() {\n      if (typeof window !== 'undefined' && !window.MonacoEnvironment) {\n        window.MonacoEnvironment = {\n          getWorkerUrl: () => './editor.worker.js'\n        };\n      }\n    }\n    initializeEditor() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        try {\n          if (_this.editor) return;\n          _this.state.loading = true;\n          _this.state.error = false;\n          _this.state.errorMessage = null;\n          _this.cdr.markForCheck();\n          // Temporarily disabled Monaco Editor for Docker build\n          const monacoModule = yield import('monaco-editor');\n          _this.monacoModule = monacoModule;\n          if (!_this.editorContainer?.nativeElement) {\n            throw new Error('Editor container not found');\n          }\n          _this.editorContainer.nativeElement.style.height = _this.height;\n          // Temporarily disabled Monaco Editor for Docker build\n          _this.defineCustomThemes(monacoModule);\n          // Temporarily disabled Monaco Editor initialization for Docker build\n          _this.editor = monacoModule.editor.create(_this.editorContainer.nativeElement, {\n            value: _this.value,\n            language: _this.language,\n            theme: _this.theme === 'dark' ? 'code-editor-dark' : 'code-editor-light',\n            automaticLayout: true,\n            minimap: {\n              enabled: false\n            },\n            scrollBeyondLastLine: false,\n            readOnly: _this.readonly,\n            placeholder: _this.placeholder,\n            fontSize: 14,\n            fontFamily: \"'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', 'Consolas', 'Courier New', monospace\",\n            tabSize: 4,\n            lineHeight: 22,\n            wordWrap: 'on',\n            lineNumbers: 'on',\n            folding: true,\n            autoIndent: 'full',\n            formatOnPaste: true,\n            formatOnType: true,\n            autoClosingBrackets: 'always',\n            autoClosingQuotes: 'always',\n            bracketPairColorization: {\n              enabled: true\n            },\n            guides: {\n              bracketPairs: true,\n              indentation: true\n            }\n          });\n          _this.setupEventListeners();\n          _this.state.loading = false;\n          _this.editorReady.emit(_this.editor);\n          _this.cdr.markForCheck();\n        } catch (error) {\n          _this.state.loading = false;\n          _this.state.error = true;\n          _this.state.errorMessage = `Failed to initialize code editor: ${error}`;\n          _this.cdr.markForCheck();\n        }\n      })();\n    }\n    defineCustomThemes(monaco) {\n      monaco.editor.defineTheme('code-editor-light', {\n        base: 'vs',\n        inherit: true,\n        rules: [{\n          token: 'comment',\n          foreground: '6c757d',\n          fontStyle: 'italic'\n        }, {\n          token: 'keyword',\n          foreground: '0066cc'\n        }, {\n          token: 'string',\n          foreground: '28a745'\n        }, {\n          token: 'number',\n          foreground: 'e83e8c'\n        }, {\n          token: 'function',\n          foreground: 'fd7e14'\n        }],\n        colors: {\n          'editor.background': '#ffffff',\n          'editor.foreground': '#2c3e50',\n          'editorLineNumber.foreground': '#6c757d',\n          'editor.selectionBackground': '#007bff26',\n          'editor.lineHighlightBackground': '#f8f9fa',\n          'editorCursor.foreground': '#007bff'\n        }\n      });\n      monaco.editor.defineTheme('code-editor-dark', {\n        base: 'vs-dark',\n        inherit: true,\n        rules: [{\n          token: 'comment',\n          foreground: '6c757d',\n          fontStyle: 'italic'\n        }, {\n          token: 'keyword',\n          foreground: '66d9ef'\n        }, {\n          token: 'string',\n          foreground: 'a6e22e'\n        }, {\n          token: 'number',\n          foreground: 'fd971f'\n        }, {\n          token: 'function',\n          foreground: 'f92672'\n        }],\n        colors: {\n          'editor.background': '#1e1e1e',\n          'editor.foreground': '#e9ecef',\n          'editorLineNumber.foreground': '#6c757d',\n          'editor.selectionBackground': '#66d9ef26',\n          'editor.lineHighlightBackground': '#2c2c2c',\n          'editorCursor.foreground': '#66d9ef'\n        }\n      });\n    }\n    applyTheme() {\n      if (this.editor) {\n        this.editor.updateOptions({\n          theme: this.theme === 'dark' ? 'code-editor-dark' : 'code-editor-light'\n        });\n      }\n    }\n    setupEventListeners() {\n      if (!this.editor) return;\n      this.editor.onDidChangeModelContent(() => {\n        const currentValue = this.editor.getValue();\n        if (this.valueChangeTimeout) clearTimeout(this.valueChangeTimeout);\n        this.valueChangeTimeout = setTimeout(() => {\n          this.valueChange.emit(currentValue);\n          if (this.Control) {\n            this.Control.setValue(currentValue);\n          }\n        }, 200);\n      });\n    }\n    selectAll() {\n      if (this.editor) {\n        this.editor.setSelection(this.editor.getModel().getFullModelRange());\n        this.editor.focus();\n      }\n    }\n    clear() {\n      if (this.editor) {\n        this.editor.setValue('');\n        this.editor.focus();\n      }\n    }\n    showProcessingLoader() {\n      this.state.processing = true;\n      this.cdr.markForCheck();\n    }\n    hideProcessingLoader() {\n      this.state.processing = false;\n      this.cdr.markForCheck();\n    }\n    onPrimaryButtonClick() {\n      this.showProcessingLoader();\n      this.primaryButtonSelected.emit();\n    }\n    getValue() {\n      return this.editor?.getValue() ?? '';\n    }\n    setValue(newValue) {\n      this.editor?.setValue(newValue);\n    }\n    setTheme(newTheme) {\n      this.theme = newTheme;\n      this.applyTheme();\n    }\n    focus() {\n      this.editor?.focus();\n    }\n    get isReady() {\n      return !!this.editor && !this.state.loading;\n    }\n    onActionButtonClick(idx) {\n      this.actionButtonClicked.emit(idx);\n    }\n    static ɵfac = function CodeEditorComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CodeEditorComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CodeEditorComponent,\n      selectors: [[\"app-code-editor\"]],\n      viewQuery: function CodeEditorComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.editorContainer = _t.first);\n        }\n      },\n      inputs: {\n        title: \"title\",\n        value: \"value\",\n        language: \"language\",\n        theme: \"theme\",\n        height: \"height\",\n        readonly: \"readonly\",\n        customCssClass: \"customCssClass\",\n        placeholder: \"placeholder\",\n        Control: \"Control\",\n        actionButtons: \"actionButtons\",\n        footerText: \"footerText\",\n        isPrimaryButtonDisabled: \"isPrimaryButtonDisabled\"\n      },\n      outputs: {\n        valueChange: \"valueChange\",\n        primaryButtonSelected: \"primaryButtonSelected\",\n        actionButtonClicked: \"actionButtonClicked\",\n        editorReady: \"editorReady\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 8,\n      vars: 8,\n      consts: [[\"editorContainer\", \"\"], [1, \"code-editor-container\", 3, \"ngClass\"], [\"class\", \"editor-header\", 4, \"ngIf\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [1, \"monaco-editor-container\"], [\"class\", \"editor-loader-overlay\", 4, \"ngIf\"], [\"class\", \"editor-footer\", 4, \"ngIf\"], [1, \"editor-header\"], [1, \"title-action-row\"], [\"class\", \"editor-title\", 4, \"ngIf\"], [\"class\", \"run-btn-wrapper\", 4, \"ngIf\"], [\"class\", \"editor-actions\", 4, \"ngIf\"], [1, \"editor-title\"], [1, \"run-btn-wrapper\"], [\"label\", \"Test\", \"variant\", \"primary\", \"size\", \"small\", 1, \"run-black-btn\", 3, \"userClick\", \"customStyles\", \"disabled\"], [1, \"editor-actions\"], [4, \"ngFor\", \"ngForOf\"], [3, \"ngClass\"], [\"variant\", \"secondary\", \"size\", \"small\", 1, \"action-btn\", 3, \"userClick\", \"label\", \"iconName\", \"customStyles\", \"pill\", \"disabled\"], [1, \"loading-container\"], [1, \"loading-spinner\"], [1, \"error-container\"], [1, \"error-icon\"], [\"label\", \"Retry\", \"variant\", \"secondary\", \"size\", \"small\", 3, \"userClick\", \"pill\", \"customStyles\"], [1, \"editor-loader-overlay\"], [1, \"editor-loader-spinner\"], [1, \"editor-footer\"], [1, \"footer-note\"]],\n      template: function CodeEditorComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1);\n          i0.ɵɵtemplate(1, CodeEditorComponent_div_1_Template, 5, 3, \"div\", 2)(2, CodeEditorComponent_div_2_Template, 4, 0, \"div\", 3)(3, CodeEditorComponent_div_3_Template, 8, 4, \"div\", 4);\n          i0.ɵɵelement(4, \"div\", 5, 0);\n          i0.ɵɵtemplate(6, CodeEditorComponent_div_6_Template, 2, 0, \"div\", 6)(7, CodeEditorComponent_div_7_Template, 5, 1, \"div\", 7);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngClass\", ctx.customCssClass);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.title || ctx.primaryButtonSelected.observed);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.state.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.state.error);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"hidden\", ctx.state.loading || ctx.state.error);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.state.processing);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.footerText);\n        }\n      },\n      dependencies: [CommonModule, i1.NgClass, i1.NgForOf, i1.NgIf, ButtonComponent],\n      styles: [\".code-editor-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n  background: #ffffff;\\n  font-family: -apple-system, BlinkMacSystemFont, \\\"Segoe UI\\\", Roboto, sans-serif;\\n  position: relative;\\n}\\n\\n.editor-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n  padding: 16px 20px;\\n  background: #ffffff;\\n}\\n\\n.title-action-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.editor-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 18px;\\n  font-weight: 700;\\n  color: #000000;\\n}\\n\\n.editor-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.run-btn-wrapper[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n  .run-black-btn .btn {\\n  background: #000 !important;\\n  color: #fff !important;\\n  border: none !important;\\n  border-radius: 6px;\\n  font-size: 14px;\\n  height: 32px;\\n  padding: 6px 12px;\\n}\\n  .run-black-btn .btn:hover:not(:disabled) {\\n  background: #222 !important;\\n}\\n\\n.loading-container[_ngcontent-%COMP%], \\n.error-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px 20px;\\n  text-align: center;\\n  min-height: 200px;\\n  background: #ffffff;\\n}\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  border: 2px solid #f3f3f3;\\n  border-top: 2px solid #0969da;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n  margin-bottom: 12px;\\n}\\n\\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #656d76;\\n  font-size: 14px;\\n}\\n\\n.error-container[_ngcontent-%COMP%] {\\n  color: #cf222e;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  margin-bottom: 8px;\\n}\\n.error-container[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 16px;\\n  color: #cf222e;\\n}\\n.error-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  font-size: 14px;\\n  color: #656d76;\\n}\\n\\n.monaco-editor-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-height: 300px;\\n  position: relative;\\n  background: #ffffff;\\n}\\n.monaco-editor-container.hidden[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n\\n.editor-footer[_ngcontent-%COMP%] {\\n  padding: 12px 20px;\\n  background: #ffffff;\\n}\\n.editor-footer[_ngcontent-%COMP%]   .footer-note[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 12px;\\n  color: #656d76;\\n  line-height: 1.4;\\n}\\n.editor-footer[_ngcontent-%COMP%]   .footer-note[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #24292f;\\n  font-weight: 600;\\n}\\n\\n.editor-loader-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: rgba(255, 255, 255, 0.5);\\n  -webkit-backdrop-filter: blur(4px);\\n          backdrop-filter: blur(4px);\\n  z-index: 10;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.editor-loader-overlay[_ngcontent-%COMP%]   .editor-loader-spinner[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border: 5px solid rgba(0, 0, 0, 0.1);\\n  border-top: 5px solid #222;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .editor-header[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n  }\\n  .title-action-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 8px;\\n  }\\n  .monaco-editor-container[_ngcontent-%COMP%] {\\n    min-height: 250px;\\n  }\\n  .editor-title[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      changeDetection: 0\n    });\n  }\n  return CodeEditorComponent;\n})();", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "ButtonComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "title", "ɵɵlistener", "CodeEditorComponent_div_1_span_3_Template_ava_button_userClick_1_listener", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "onPrimaryButtonClick", "ɵɵproperty", "ɵɵpureFunction0", "_c1", "state", "loading", "isPrimaryButtonDisabled", "ɵɵelementContainerStart", "CodeEditorComponent_div_1_div_4_ng_container_1_Template_ava_button_userClick_2_listener", "i_r4", "_r3", "index", "onActionButtonClick", "btn_r5", "customClass", "label", "icon", "_c2", "ɵɵtemplate", "CodeEditorComponent_div_1_div_4_ng_container_1_Template", "actionButtons", "CodeEditorComponent_div_1_h3_2_Template", "CodeEditorComponent_div_1_span_3_Template", "CodeEditorComponent_div_1_div_4_Template", "primaryButtonSelected", "observed", "length", "ɵɵelement", "CodeEditorComponent_div_3_Template_ava_button_userClick_7_listener", "_r6", "initializeEditor", "errorMessage", "ɵɵtextInterpolate1", "footerText", "CodeEditorComponent", "cdr", "<PERSON><PERSON><PERSON><PERSON>", "value", "language", "theme", "height", "readonly", "customCssClass", "placeholder", "Control", "valueChange", "actionButtonClicked", "<PERSON><PERSON><PERSON><PERSON>", "error", "processing", "editor", "valueChangeTimeout", "monacoModule", "constructor", "ngOnChanges", "changes", "currentValue", "getValue", "setValue", "setModelLanguage", "getModel", "applyTheme", "ngOnInit", "configureMonacoEnvironment", "ngAfterViewInit", "setTimeout", "ngOnDestroy", "dispose", "window", "MonacoEnvironment", "getWorkerUrl", "_this", "_asyncToGenerator", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nativeElement", "Error", "style", "defineCustomThemes", "create", "automaticLayout", "minimap", "enabled", "scrollBeyondLastLine", "readOnly", "fontSize", "fontFamily", "tabSize", "lineHeight", "wordWrap", "lineNumbers", "folding", "autoIndent", "formatOnPaste", "formatOnType", "autoClosingBrackets", "autoClosingQuotes", "bracketPairColorization", "guides", "bracketPairs", "indentation", "setupEventListeners", "emit", "monaco", "defineTheme", "base", "inherit", "rules", "token", "foreground", "fontStyle", "colors", "updateOptions", "onDidChangeModelContent", "clearTimeout", "selectAll", "setSelection", "getFullModelRange", "focus", "clear", "showProcessingLoader", "hideProcessingLoader", "newValue", "setTheme", "newTheme", "isReady", "idx", "ɵɵdirectiveInject", "ChangeDetectorRef", "selectors", "viewQuery", "CodeEditorComponent_Query", "rf", "ctx", "CodeEditorComponent_div_1_Template", "CodeEditorComponent_div_2_Template", "CodeEditorComponent_div_3_Template", "CodeEditorComponent_div_6_Template", "CodeEditorComponent_div_7_Template", "ɵɵclassProp", "i1", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles", "changeDetection"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\shared\\components\\code-editor\\code-editor.component.ts", "C:\\console\\aava-ui-web\\projects\\console\\src\\app\\shared\\components\\code-editor\\code-editor.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  ElementRef,\r\n  ViewChild,\r\n  Input,\r\n  Output,\r\n  EventEmitter,\r\n  OnInit,\r\n  OnDestroy,\r\n  AfterViewInit,\r\n  ChangeDetectionStrategy,\r\n  ChangeDetectorRef,\r\n  OnChanges,\r\n  SimpleChanges\r\n} from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormControl } from '@angular/forms';\r\nimport { ButtonComponent } from '@ava/play-comp-library';\r\n\r\ndeclare const monaco: any;\r\n\r\nexport type CodeLanguage = 'python' | 'javascript' | 'typescript' | 'json' | 'sql' | 'html' | 'css' | 'markdown' | 'yaml' | 'xml' | 'plaintext';\r\nexport type CodeEditorTheme = 'light' | 'dark';\r\n\r\nexport interface EditorActionButton {\r\n \r\n  label: string;\r\n  style?: 'primary' | 'secondary' | 'outline' | 'text' | 'danger';\r\n  customClass?: string;\r\n  icon?: string;\r\n}\r\n\r\n/**\r\n * Monaco Code Editor Component\r\n *\r\n * @Input title - Editor title displayed in header\r\n * @Input value - Initial code value\r\n * @Input language - Programming language (python, javascript, etc.)\r\n * @Input theme - Editor theme (light/dark)\r\n * @Input height - Editor height (default: 400px)\r\n * @Input readonly - Make editor read-only\r\n * @Input Control - Angular FormControl for form integration\r\n * @Input actionButtons - Array of custom action buttons\r\n * @Input footerText - Footer note text\r\n *\r\n * @Output valueChange - Emits when code changes\r\n * @Output primaryButtonSelected - Emits when Run button clicked\r\n * @Output actionButtonClicked - Emits when action button clicked\r\n * @Output editorReady - Emits when editor is initialized\r\n *\r\n * Usage:\r\n * <app-code-editor\r\n *   title=\"Code Editor\"\r\n *   language=\"python\"\r\n *   [Control]=\"formControl\"\r\n *   (primaryButtonSelected)=\"runCode()\">\r\n * </app-code-editor>\r\n */\r\n@Component({\r\n  selector: 'app-code-editor',\r\n  standalone: true,\r\n  imports: [CommonModule, ButtonComponent],\r\n  changeDetection: ChangeDetectionStrategy.OnPush,\r\n  templateUrl: './code-editor.component.html',\r\n  styleUrls: ['./code-editor.component.scss']\r\n})\r\nexport class CodeEditorComponent implements OnInit, AfterViewInit, OnDestroy, OnChanges {\r\n  @ViewChild('editorContainer', { static: true }) private readonly editorContainer!: ElementRef;\r\n\r\n  @Input() title = '';\r\n  @Input() value = '';\r\n  @Input() language: CodeLanguage = 'python';\r\n  @Input() theme: CodeEditorTheme = 'light';\r\n  @Input() height = '400px';\r\n  @Input() readonly = false;\r\n  @Input() customCssClass = '';\r\n  @Input() placeholder = '';\r\n  @Input() Control: FormControl | null = null;\r\n  @Input() actionButtons: EditorActionButton[] = [];\r\n  @Input() footerText = '';\r\n  @Input() isPrimaryButtonDisabled = false;\r\n\r\n\r\n  @Output() readonly valueChange = new EventEmitter<string>();\r\n  @Output() readonly primaryButtonSelected = new EventEmitter<void>();\r\n  @Output() readonly actionButtonClicked = new EventEmitter<number>();\r\n  @Output() readonly editorReady = new EventEmitter<any>();\r\n\r\n  readonly state = {\r\n    loading: true,\r\n    error: false,\r\n    errorMessage: null as string | null,\r\n    processing: false,\r\n  };\r\n\r\n  private editor: any = null;\r\n  private valueChangeTimeout: any = null;\r\n  private monacoModule:any\r\n\r\n  constructor(private readonly cdr: ChangeDetectorRef) {}\r\n\r\n  ngOnChanges(changes: SimpleChanges): void {\r\n    if (this.editor) {\r\n      if (changes['value'] && changes['value'].currentValue !== this.editor.getValue()) {\r\n        this.editor.setValue(changes['value'].currentValue || '');\r\n      }\r\n      if (changes['language'] && changes['language'].currentValue) {\r\n        this.monacoModule.editor.setModelLanguage(this.editor.getModel(), changes['language'].currentValue);\r\n      }\r\n      if (changes['theme'] && changes['theme'].currentValue) {\r\n        this.applyTheme();\r\n      }\r\n    }\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.configureMonacoEnvironment();\r\n    if (this.Control?.value && !this.value) {\r\n      this.value = this.Control.value;\r\n    }\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    setTimeout(() => this.initializeEditor(), 100);\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.editor?.dispose();\r\n  }\r\n\r\n  private configureMonacoEnvironment(): void {\r\n    if (typeof window !== 'undefined' && !(window as any).MonacoEnvironment) {\r\n      (window as any).MonacoEnvironment = {\r\n        getWorkerUrl: () => './editor.worker.js'\r\n      };\r\n    }\r\n  }\r\n\r\n  async initializeEditor(): Promise<void> {\r\n    try {\r\n      if (this.editor) return;\r\n      this.state.loading = true;\r\n      this.state.error = false;\r\n      this.state.errorMessage = null;\r\n      this.cdr.markForCheck();\r\n\r\n      // Temporarily disabled Monaco Editor for Docker build\r\n    const monacoModule = await import('monaco-editor');\r\n\r\n    this.monacoModule = monacoModule;\r\n\r\n      if (!this.editorContainer?.nativeElement) {\r\n        throw new Error('Editor container not found');\r\n      }\r\n\r\n      this.editorContainer.nativeElement.style.height = this.height;\r\n      // Temporarily disabled Monaco Editor for Docker build\r\n      this.defineCustomThemes(monacoModule);\r\n\r\n      // Temporarily disabled Monaco Editor initialization for Docker build\r\n      \r\n      this.editor = monacoModule.editor.create(this.editorContainer.nativeElement, {\r\n        value: this.value,\r\n        language: this.language,\r\n        theme: this.theme === 'dark' ? 'code-editor-dark' : 'code-editor-light',\r\n        automaticLayout: true,\r\n        minimap: { enabled: false },\r\n        scrollBeyondLastLine: false,\r\n        readOnly: this.readonly,\r\n        placeholder: this.placeholder,\r\n        fontSize: 14,\r\n        fontFamily: \"'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', 'Consolas', 'Courier New', monospace\",\r\n        tabSize: 4,\r\n        lineHeight: 22,\r\n        wordWrap: 'on',\r\n        lineNumbers: 'on',\r\n        folding: true,\r\n        autoIndent: 'full',\r\n        formatOnPaste: true,\r\n        formatOnType: true,\r\n        autoClosingBrackets: 'always',\r\n        autoClosingQuotes: 'always',\r\n        bracketPairColorization: { enabled: true },\r\n        guides: { bracketPairs: true, indentation: true }\r\n      });\r\n\r\n      this.setupEventListeners();\r\n      \r\n      this.state.loading = false;\r\n      this.editorReady.emit(this.editor);\r\n      this.cdr.markForCheck();\r\n\r\n    } catch (error) {\r\n      this.state.loading = false;\r\n      this.state.error = true;\r\n      this.state.errorMessage = `Failed to initialize code editor: ${error}`;\r\n      this.cdr.markForCheck();\r\n    }\r\n  }\r\n\r\n  private defineCustomThemes(monaco: any): void {\r\n    monaco.editor.defineTheme('code-editor-light', {\r\n      base: 'vs',\r\n      inherit: true,\r\n      rules: [\r\n        { token: 'comment', foreground: '6c757d', fontStyle: 'italic' },\r\n        { token: 'keyword', foreground: '0066cc' },\r\n        { token: 'string', foreground: '28a745' },\r\n        { token: 'number', foreground: 'e83e8c' },\r\n        { token: 'function', foreground: 'fd7e14' }\r\n      ],\r\n      colors: {\r\n        'editor.background': '#ffffff',\r\n        'editor.foreground': '#2c3e50',\r\n        'editorLineNumber.foreground': '#6c757d',\r\n        'editor.selectionBackground': '#007bff26',\r\n        'editor.lineHighlightBackground': '#f8f9fa',\r\n        'editorCursor.foreground': '#007bff'\r\n      }\r\n    });\r\n\r\n    monaco.editor.defineTheme('code-editor-dark', {\r\n      base: 'vs-dark',\r\n      inherit: true,\r\n      rules: [\r\n        { token: 'comment', foreground: '6c757d', fontStyle: 'italic' },\r\n        { token: 'keyword', foreground: '66d9ef' },\r\n        { token: 'string', foreground: 'a6e22e' },\r\n        { token: 'number', foreground: 'fd971f' },\r\n        { token: 'function', foreground: 'f92672' }\r\n      ],\r\n      colors: {\r\n        'editor.background': '#1e1e1e',\r\n        'editor.foreground': '#e9ecef',\r\n        'editorLineNumber.foreground': '#6c757d',\r\n        'editor.selectionBackground': '#66d9ef26',\r\n        'editor.lineHighlightBackground': '#2c2c2c',\r\n        'editorCursor.foreground': '#66d9ef'\r\n      }\r\n    });\r\n  }\r\n\r\n  private applyTheme(): void {\r\n    if (this.editor) {\r\n      this.editor.updateOptions({\r\n        theme: this.theme === 'dark' ? 'code-editor-dark' : 'code-editor-light'\r\n      });\r\n    }\r\n  }\r\n\r\n  private setupEventListeners(): void {\r\n    if (!this.editor) return;\r\n\r\n    this.editor.onDidChangeModelContent(() => {\r\n      const currentValue = this.editor.getValue();\r\n      if (this.valueChangeTimeout) clearTimeout(this.valueChangeTimeout);\r\n      this.valueChangeTimeout = setTimeout(() => {\r\n        this.valueChange.emit(currentValue);\r\n        if (this.Control) {\r\n          this.Control.setValue(currentValue);\r\n        }\r\n      }, 200);\r\n    });\r\n  }\r\n\r\n  selectAll(): void {\r\n    if (this.editor) {\r\n      this.editor.setSelection(this.editor.getModel()!.getFullModelRange());\r\n      this.editor.focus();\r\n    }\r\n  }\r\n\r\n  clear(): void {\r\n    if (this.editor) {\r\n      this.editor.setValue('');\r\n      this.editor.focus();\r\n    }\r\n  }\r\n\r\n  showProcessingLoader(): void {\r\n    this.state.processing = true;\r\n    this.cdr.markForCheck();\r\n  }\r\n\r\n  hideProcessingLoader(): void {\r\n    this.state.processing = false;\r\n    this.cdr.markForCheck();\r\n  }\r\n\r\n  onPrimaryButtonClick(): void {\r\n    this.showProcessingLoader();\r\n    this.primaryButtonSelected.emit();\r\n  }\r\n\r\n  getValue(): string {\r\n    return this.editor?.getValue() ?? '';\r\n  }\r\n\r\n  setValue(newValue: string): void {\r\n    this.editor?.setValue(newValue);\r\n  }\r\n\r\n  setTheme(newTheme: CodeEditorTheme): void {\r\n    this.theme = newTheme;\r\n    this.applyTheme();\r\n  }\r\n\r\n  focus(): void {\r\n    this.editor?.focus();\r\n  }\r\n\r\n  get isReady(): boolean {\r\n    return !!this.editor && !this.state.loading;\r\n  }\r\n\r\n  onActionButtonClick(idx: number): void {\r\n    this.actionButtonClicked.emit(idx);\r\n  }\r\n}\r\n", "<div class=\"code-editor-container\" [ngClass]=\"customCssClass\">\r\n  <div class=\"editor-header\" *ngIf=\"title || primaryButtonSelected.observed\">\r\n    <div class=\"title-action-row\">\r\n      <h3 class=\"editor-title\" *ngIf=\"title\">{{ title }}</h3>\r\n      <span class=\"run-btn-wrapper\" *ngIf=\"primaryButtonSelected.observed\">\r\n        <ava-button\r\n          label=\"Test\"\r\n          variant=\"primary\"\r\n          size=\"small\"\r\n          [customStyles]=\"{\r\n            background:\r\n              'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n            '--button-effect-color': '33, 90, 214',\r\n          }\"\r\n          [disabled]=\"state.loading || isPrimaryButtonDisabled\"\r\n          (userClick)=\"onPrimaryButtonClick()\"\r\n          class=\"run-black-btn\">\r\n        </ava-button>\r\n      </span>\r\n    </div>\r\n\r\n    <div class=\"editor-actions\" *ngIf=\"actionButtons.length\">\r\n      <ng-container *ngFor=\"let btn of actionButtons; let i = index\">\r\n        <span [ngClass]=\"btn.customClass\">\r\n          <ava-button\r\n            class=\"action-btn\"\r\n            [label]=\"btn.label\"\r\n            variant=\"secondary\"\r\n            [iconName]=\"btn.icon || ''\"\r\n            size=\"small\"\r\n            [customStyles]=\"{\r\n              'border': '2px solid transparent',\r\n              'background-image': 'linear-gradient(#ffffff, #ffffff), linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n              'background-origin': 'border-box',\r\n              'background-clip': 'padding-box, border-box',\r\n              '--button-effect-color': '33, 90, 214'\r\n            }\"\r\n            [pill]=\"true\"\r\n            [disabled]=\"state.loading\"\r\n            (userClick)=\"onActionButtonClick(i)\">\r\n          </ava-button>\r\n        </span>\r\n      </ng-container>\r\n    </div>\r\n  </div>\r\n\r\n  <div *ngIf=\"state.loading\" class=\"loading-container\">\r\n    <div class=\"loading-spinner\"></div>\r\n    <p>Loading Code Editor...</p>\r\n  </div>\r\n\r\n  <div *ngIf=\"state.error\" class=\"error-container\">\r\n    <div class=\"error-icon\">⚠️</div>\r\n    <h4>Failed to Load Editor</h4>\r\n    <p>{{ state.errorMessage }}</p>\r\n    <ava-button\r\n      label=\"Retry\"\r\n      variant=\"secondary\"\r\n      size=\"small\"\r\n      [pill]=\"true\"\r\n      [customStyles]=\"{\r\n        'border': '2px solid transparent',\r\n        'background-image': 'linear-gradient(#ffffff, #ffffff), linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n        'background-origin': 'border-box',\r\n        'background-clip': 'padding-box, border-box',\r\n        '--button-effect-color': '33, 90, 214'\r\n      }\"\r\n      (userClick)=\"initializeEditor()\">\r\n    </ava-button>\r\n  </div>\r\n\r\n  <div\r\n    #editorContainer\r\n    class=\"monaco-editor-container\"\r\n    [class.hidden]=\"state.loading || state.error\">\r\n  </div>\r\n\r\n  <div *ngIf=\"state.processing\" class=\"editor-loader-overlay\">\r\n    <div class=\"editor-loader-spinner\"></div>\r\n  </div>\r\n\r\n  <div class=\"editor-footer\" *ngIf=\"footerText\">\r\n    <p class=\"footer-note\">\r\n      <strong>Note:</strong> {{ footerText }}\r\n    </p>\r\n  </div>\r\n</div>\r\n"], "mappings": ";AAAA,SAMEA,YAAY,QAQP,eAAe;AACtB,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,eAAe,QAAQ,wBAAwB;;;;;;;;;;;;;;;;;ICdlDC,EAAA,CAAAC,cAAA,aAAuC;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAhBH,EAAA,CAAAI,SAAA,EAAW;IAAXJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAW;;;;;;IAEhDP,EADF,CAAAC,cAAA,eAAqE,qBAY3C;IADtBD,EAAA,CAAAQ,UAAA,uBAAAC,0EAAA;MAAAT,EAAA,CAAAU,aAAA,CAAAC,GAAA;MAAA,MAAAL,MAAA,GAAAN,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAAaP,MAAA,CAAAQ,oBAAA,EAAsB;IAAA,EAAC;IAGxCd,EADE,CAAAG,YAAA,EAAa,EACR;;;;IATHH,EAAA,CAAAI,SAAA,EAIE;IACFJ,EALA,CAAAe,UAAA,iBAAAf,EAAA,CAAAgB,eAAA,IAAAC,GAAA,EAIE,aAAAX,MAAA,CAAAY,KAAA,CAAAC,OAAA,IAAAb,MAAA,CAAAc,uBAAA,CACmD;;;;;;IAQzDpB,EAAA,CAAAqB,uBAAA,GAA+D;IAE3DrB,EADF,CAAAC,cAAA,eAAkC,qBAgBO;IAArCD,EAAA,CAAAQ,UAAA,uBAAAc,wFAAA;MAAA,MAAAC,IAAA,GAAAvB,EAAA,CAAAU,aAAA,CAAAc,GAAA,EAAAC,KAAA;MAAA,MAAAnB,MAAA,GAAAN,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAAaP,MAAA,CAAAoB,mBAAA,CAAAH,IAAA,CAAsB;IAAA,EAAC;IAExCvB,EADE,CAAAG,YAAA,EAAa,EACR;;;;;;IAlBDH,EAAA,CAAAI,SAAA,EAA2B;IAA3BJ,EAAA,CAAAe,UAAA,YAAAY,MAAA,CAAAC,WAAA,CAA2B;IAG7B5B,EAAA,CAAAI,SAAA,EAAmB;IAYnBJ,EAZA,CAAAe,UAAA,UAAAY,MAAA,CAAAE,KAAA,CAAmB,aAAAF,MAAA,CAAAG,IAAA,OAEQ,iBAAA9B,EAAA,CAAAgB,eAAA,IAAAe,GAAA,EAQzB,cACW,aAAAzB,MAAA,CAAAY,KAAA,CAAAC,OAAA,CACa;;;;;IAjBlCnB,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAgC,UAAA,IAAAC,uDAAA,2BAA+D;IAqBjEjC,EAAA,CAAAG,YAAA,EAAM;;;;IArB0BH,EAAA,CAAAI,SAAA,EAAkB;IAAlBJ,EAAA,CAAAe,UAAA,YAAAT,MAAA,CAAA4B,aAAA,CAAkB;;;;;IApBlDlC,EADF,CAAAC,cAAA,aAA2E,aAC3C;IAE5BD,EADA,CAAAgC,UAAA,IAAAG,uCAAA,iBAAuC,IAAAC,yCAAA,mBAC8B;IAevEpC,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAgC,UAAA,IAAAK,wCAAA,kBAAyD;IAuB3DrC,EAAA,CAAAG,YAAA,EAAM;;;;IAzCwBH,EAAA,CAAAI,SAAA,GAAW;IAAXJ,EAAA,CAAAe,UAAA,SAAAT,MAAA,CAAAC,KAAA,CAAW;IACNP,EAAA,CAAAI,SAAA,EAAoC;IAApCJ,EAAA,CAAAe,UAAA,SAAAT,MAAA,CAAAgC,qBAAA,CAAAC,QAAA,CAAoC;IAiBxCvC,EAAA,CAAAI,SAAA,EAA0B;IAA1BJ,EAAA,CAAAe,UAAA,SAAAT,MAAA,CAAA4B,aAAA,CAAAM,MAAA,CAA0B;;;;;IAyBzDxC,EAAA,CAAAC,cAAA,cAAqD;IACnDD,EAAA,CAAAyC,SAAA,cAAmC;IACnCzC,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAC3BF,EAD2B,CAAAG,YAAA,EAAI,EACzB;;;;;;IAGJH,EADF,CAAAC,cAAA,cAAiD,cACvB;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAChCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC/BH,EAAA,CAAAC,cAAA,qBAYmC;IAAjCD,EAAA,CAAAQ,UAAA,uBAAAkC,mEAAA;MAAA1C,EAAA,CAAAU,aAAA,CAAAiC,GAAA;MAAA,MAAArC,MAAA,GAAAN,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAAaP,MAAA,CAAAsC,gBAAA,EAAkB;IAAA,EAAC;IAEpC5C,EADE,CAAAG,YAAA,EAAa,EACT;;;;IAfDH,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAY,KAAA,CAAA2B,YAAA,CAAwB;IAKzB7C,EAAA,CAAAI,SAAA,EAAa;IACbJ,EADA,CAAAe,UAAA,cAAa,iBAAAf,EAAA,CAAAgB,eAAA,IAAAe,GAAA,EAOX;;;;;IAWN/B,EAAA,CAAAC,cAAA,cAA4D;IAC1DD,EAAA,CAAAyC,SAAA,cAAyC;IAC3CzC,EAAA,CAAAG,YAAA,EAAM;;;;;IAIFH,EAFJ,CAAAC,cAAA,cAA8C,YACrB,aACb;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,GACzB;IACFF,EADE,CAAAG,YAAA,EAAI,EACA;;;;IAFqBH,EAAA,CAAAI,SAAA,GACzB;IADyBJ,EAAA,CAAA8C,kBAAA,MAAAxC,MAAA,CAAAyC,UAAA,MACzB;;;ADpDJ;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA,WAAaC,mBAAmB;EAA1B,MAAOA,mBAAmB;IAiCDC,GAAA;IAhCoCC,eAAe;IAEvE3C,KAAK,GAAG,EAAE;IACV4C,KAAK,GAAG,EAAE;IACVC,QAAQ,GAAiB,QAAQ;IACjCC,KAAK,GAAoB,OAAO;IAChCC,MAAM,GAAG,OAAO;IAChBC,QAAQ,GAAG,KAAK;IAChBC,cAAc,GAAG,EAAE;IACnBC,WAAW,GAAG,EAAE;IAChBC,OAAO,GAAuB,IAAI;IAClCxB,aAAa,GAAyB,EAAE;IACxCa,UAAU,GAAG,EAAE;IACf3B,uBAAuB,GAAG,KAAK;IAGrBuC,WAAW,GAAG,IAAI9D,YAAY,EAAU;IACxCyC,qBAAqB,GAAG,IAAIzC,YAAY,EAAQ;IAChD+D,mBAAmB,GAAG,IAAI/D,YAAY,EAAU;IAChDgE,WAAW,GAAG,IAAIhE,YAAY,EAAO;IAE/CqB,KAAK,GAAG;MACfC,OAAO,EAAE,IAAI;MACb2C,KAAK,EAAE,KAAK;MACZjB,YAAY,EAAE,IAAqB;MACnCkB,UAAU,EAAE;KACb;IAEOC,MAAM,GAAQ,IAAI;IAClBC,kBAAkB,GAAQ,IAAI;IAC9BC,YAAY;IAEpBC,YAA6BlB,GAAsB;MAAtB,KAAAA,GAAG,GAAHA,GAAG;IAAsB;IAEtDmB,WAAWA,CAACC,OAAsB;MAChC,IAAI,IAAI,CAACL,MAAM,EAAE;QACf,IAAIK,OAAO,CAAC,OAAO,CAAC,IAAIA,OAAO,CAAC,OAAO,CAAC,CAACC,YAAY,KAAK,IAAI,CAACN,MAAM,CAACO,QAAQ,EAAE,EAAE;UAChF,IAAI,CAACP,MAAM,CAACQ,QAAQ,CAACH,OAAO,CAAC,OAAO,CAAC,CAACC,YAAY,IAAI,EAAE,CAAC;QAC3D;QACA,IAAID,OAAO,CAAC,UAAU,CAAC,IAAIA,OAAO,CAAC,UAAU,CAAC,CAACC,YAAY,EAAE;UAC3D,IAAI,CAACJ,YAAY,CAACF,MAAM,CAACS,gBAAgB,CAAC,IAAI,CAACT,MAAM,CAACU,QAAQ,EAAE,EAAEL,OAAO,CAAC,UAAU,CAAC,CAACC,YAAY,CAAC;QACrG;QACA,IAAID,OAAO,CAAC,OAAO,CAAC,IAAIA,OAAO,CAAC,OAAO,CAAC,CAACC,YAAY,EAAE;UACrD,IAAI,CAACK,UAAU,EAAE;QACnB;MACF;IACF;IAEAC,QAAQA,CAAA;MACN,IAAI,CAACC,0BAA0B,EAAE;MACjC,IAAI,IAAI,CAACnB,OAAO,EAAEP,KAAK,IAAI,CAAC,IAAI,CAACA,KAAK,EAAE;QACtC,IAAI,CAACA,KAAK,GAAG,IAAI,CAACO,OAAO,CAACP,KAAK;MACjC;IACF;IAEA2B,eAAeA,CAAA;MACbC,UAAU,CAAC,MAAM,IAAI,CAACnC,gBAAgB,EAAE,EAAE,GAAG,CAAC;IAChD;IAEAoC,WAAWA,CAAA;MACT,IAAI,CAAChB,MAAM,EAAEiB,OAAO,EAAE;IACxB;IAEQJ,0BAA0BA,CAAA;MAChC,IAAI,OAAOK,MAAM,KAAK,WAAW,IAAI,CAAEA,MAAc,CAACC,iBAAiB,EAAE;QACtED,MAAc,CAACC,iBAAiB,GAAG;UAClCC,YAAY,EAAEA,CAAA,KAAM;SACrB;MACH;IACF;IAEMxC,gBAAgBA,CAAA;MAAA,IAAAyC,KAAA;MAAA,OAAAC,iBAAA;QACpB,IAAI;UACF,IAAID,KAAI,CAACrB,MAAM,EAAE;UACjBqB,KAAI,CAACnE,KAAK,CAACC,OAAO,GAAG,IAAI;UACzBkE,KAAI,CAACnE,KAAK,CAAC4C,KAAK,GAAG,KAAK;UACxBuB,KAAI,CAACnE,KAAK,CAAC2B,YAAY,GAAG,IAAI;UAC9BwC,KAAI,CAACpC,GAAG,CAACsC,YAAY,EAAE;UAEvB;UACF,MAAMrB,YAAY,SAAS,MAAM,CAAC,eAAe,CAAC;UAElDmB,KAAI,CAACnB,YAAY,GAAGA,YAAY;UAE9B,IAAI,CAACmB,KAAI,CAACnC,eAAe,EAAEsC,aAAa,EAAE;YACxC,MAAM,IAAIC,KAAK,CAAC,4BAA4B,CAAC;UAC/C;UAEAJ,KAAI,CAACnC,eAAe,CAACsC,aAAa,CAACE,KAAK,CAACpC,MAAM,GAAG+B,KAAI,CAAC/B,MAAM;UAC7D;UACA+B,KAAI,CAACM,kBAAkB,CAACzB,YAAY,CAAC;UAErC;UAEAmB,KAAI,CAACrB,MAAM,GAAGE,YAAY,CAACF,MAAM,CAAC4B,MAAM,CAACP,KAAI,CAACnC,eAAe,CAACsC,aAAa,EAAE;YAC3ErC,KAAK,EAAEkC,KAAI,CAAClC,KAAK;YACjBC,QAAQ,EAAEiC,KAAI,CAACjC,QAAQ;YACvBC,KAAK,EAAEgC,KAAI,CAAChC,KAAK,KAAK,MAAM,GAAG,kBAAkB,GAAG,mBAAmB;YACvEwC,eAAe,EAAE,IAAI;YACrBC,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAK,CAAE;YAC3BC,oBAAoB,EAAE,KAAK;YAC3BC,QAAQ,EAAEZ,KAAI,CAAC9B,QAAQ;YACvBE,WAAW,EAAE4B,KAAI,CAAC5B,WAAW;YAC7ByC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAE,uFAAuF;YACnGC,OAAO,EAAE,CAAC;YACVC,UAAU,EAAE,EAAE;YACdC,QAAQ,EAAE,IAAI;YACdC,WAAW,EAAE,IAAI;YACjBC,OAAO,EAAE,IAAI;YACbC,UAAU,EAAE,MAAM;YAClBC,aAAa,EAAE,IAAI;YACnBC,YAAY,EAAE,IAAI;YAClBC,mBAAmB,EAAE,QAAQ;YAC7BC,iBAAiB,EAAE,QAAQ;YAC3BC,uBAAuB,EAAE;cAAEf,OAAO,EAAE;YAAI,CAAE;YAC1CgB,MAAM,EAAE;cAAEC,YAAY,EAAE,IAAI;cAAEC,WAAW,EAAE;YAAI;WAChD,CAAC;UAEF5B,KAAI,CAAC6B,mBAAmB,EAAE;UAE1B7B,KAAI,CAACnE,KAAK,CAACC,OAAO,GAAG,KAAK;UAC1BkE,KAAI,CAACxB,WAAW,CAACsD,IAAI,CAAC9B,KAAI,CAACrB,MAAM,CAAC;UAClCqB,KAAI,CAACpC,GAAG,CAACsC,YAAY,EAAE;QAEzB,CAAC,CAAC,OAAOzB,KAAK,EAAE;UACduB,KAAI,CAACnE,KAAK,CAACC,OAAO,GAAG,KAAK;UAC1BkE,KAAI,CAACnE,KAAK,CAAC4C,KAAK,GAAG,IAAI;UACvBuB,KAAI,CAACnE,KAAK,CAAC2B,YAAY,GAAG,qCAAqCiB,KAAK,EAAE;UACtEuB,KAAI,CAACpC,GAAG,CAACsC,YAAY,EAAE;QACzB;MAAC;IACH;IAEQI,kBAAkBA,CAACyB,MAAW;MACpCA,MAAM,CAACpD,MAAM,CAACqD,WAAW,CAAC,mBAAmB,EAAE;QAC7CC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,IAAI;QACbC,KAAK,EAAE,CACL;UAAEC,KAAK,EAAE,SAAS;UAAEC,UAAU,EAAE,QAAQ;UAAEC,SAAS,EAAE;QAAQ,CAAE,EAC/D;UAAEF,KAAK,EAAE,SAAS;UAAEC,UAAU,EAAE;QAAQ,CAAE,EAC1C;UAAED,KAAK,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAQ,CAAE,EACzC;UAAED,KAAK,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAQ,CAAE,EACzC;UAAED,KAAK,EAAE,UAAU;UAAEC,UAAU,EAAE;QAAQ,CAAE,CAC5C;QACDE,MAAM,EAAE;UACN,mBAAmB,EAAE,SAAS;UAC9B,mBAAmB,EAAE,SAAS;UAC9B,6BAA6B,EAAE,SAAS;UACxC,4BAA4B,EAAE,WAAW;UACzC,gCAAgC,EAAE,SAAS;UAC3C,yBAAyB,EAAE;;OAE9B,CAAC;MAEFR,MAAM,CAACpD,MAAM,CAACqD,WAAW,CAAC,kBAAkB,EAAE;QAC5CC,IAAI,EAAE,SAAS;QACfC,OAAO,EAAE,IAAI;QACbC,KAAK,EAAE,CACL;UAAEC,KAAK,EAAE,SAAS;UAAEC,UAAU,EAAE,QAAQ;UAAEC,SAAS,EAAE;QAAQ,CAAE,EAC/D;UAAEF,KAAK,EAAE,SAAS;UAAEC,UAAU,EAAE;QAAQ,CAAE,EAC1C;UAAED,KAAK,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAQ,CAAE,EACzC;UAAED,KAAK,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAQ,CAAE,EACzC;UAAED,KAAK,EAAE,UAAU;UAAEC,UAAU,EAAE;QAAQ,CAAE,CAC5C;QACDE,MAAM,EAAE;UACN,mBAAmB,EAAE,SAAS;UAC9B,mBAAmB,EAAE,SAAS;UAC9B,6BAA6B,EAAE,SAAS;UACxC,4BAA4B,EAAE,WAAW;UACzC,gCAAgC,EAAE,SAAS;UAC3C,yBAAyB,EAAE;;OAE9B,CAAC;IACJ;IAEQjD,UAAUA,CAAA;MAChB,IAAI,IAAI,CAACX,MAAM,EAAE;QACf,IAAI,CAACA,MAAM,CAAC6D,aAAa,CAAC;UACxBxE,KAAK,EAAE,IAAI,CAACA,KAAK,KAAK,MAAM,GAAG,kBAAkB,GAAG;SACrD,CAAC;MACJ;IACF;IAEQ6D,mBAAmBA,CAAA;MACzB,IAAI,CAAC,IAAI,CAAClD,MAAM,EAAE;MAElB,IAAI,CAACA,MAAM,CAAC8D,uBAAuB,CAAC,MAAK;QACvC,MAAMxD,YAAY,GAAG,IAAI,CAACN,MAAM,CAACO,QAAQ,EAAE;QAC3C,IAAI,IAAI,CAACN,kBAAkB,EAAE8D,YAAY,CAAC,IAAI,CAAC9D,kBAAkB,CAAC;QAClE,IAAI,CAACA,kBAAkB,GAAGc,UAAU,CAAC,MAAK;UACxC,IAAI,CAACpB,WAAW,CAACwD,IAAI,CAAC7C,YAAY,CAAC;UACnC,IAAI,IAAI,CAACZ,OAAO,EAAE;YAChB,IAAI,CAACA,OAAO,CAACc,QAAQ,CAACF,YAAY,CAAC;UACrC;QACF,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,CAAC;IACJ;IAEA0D,SAASA,CAAA;MACP,IAAI,IAAI,CAAChE,MAAM,EAAE;QACf,IAAI,CAACA,MAAM,CAACiE,YAAY,CAAC,IAAI,CAACjE,MAAM,CAACU,QAAQ,EAAG,CAACwD,iBAAiB,EAAE,CAAC;QACrE,IAAI,CAAClE,MAAM,CAACmE,KAAK,EAAE;MACrB;IACF;IAEAC,KAAKA,CAAA;MACH,IAAI,IAAI,CAACpE,MAAM,EAAE;QACf,IAAI,CAACA,MAAM,CAACQ,QAAQ,CAAC,EAAE,CAAC;QACxB,IAAI,CAACR,MAAM,CAACmE,KAAK,EAAE;MACrB;IACF;IAEAE,oBAAoBA,CAAA;MAClB,IAAI,CAACnH,KAAK,CAAC6C,UAAU,GAAG,IAAI;MAC5B,IAAI,CAACd,GAAG,CAACsC,YAAY,EAAE;IACzB;IAEA+C,oBAAoBA,CAAA;MAClB,IAAI,CAACpH,KAAK,CAAC6C,UAAU,GAAG,KAAK;MAC7B,IAAI,CAACd,GAAG,CAACsC,YAAY,EAAE;IACzB;IAEAzE,oBAAoBA,CAAA;MAClB,IAAI,CAACuH,oBAAoB,EAAE;MAC3B,IAAI,CAAC/F,qBAAqB,CAAC6E,IAAI,EAAE;IACnC;IAEA5C,QAAQA,CAAA;MACN,OAAO,IAAI,CAACP,MAAM,EAAEO,QAAQ,EAAE,IAAI,EAAE;IACtC;IAEAC,QAAQA,CAAC+D,QAAgB;MACvB,IAAI,CAACvE,MAAM,EAAEQ,QAAQ,CAAC+D,QAAQ,CAAC;IACjC;IAEAC,QAAQA,CAACC,QAAyB;MAChC,IAAI,CAACpF,KAAK,GAAGoF,QAAQ;MACrB,IAAI,CAAC9D,UAAU,EAAE;IACnB;IAEAwD,KAAKA,CAAA;MACH,IAAI,CAACnE,MAAM,EAAEmE,KAAK,EAAE;IACtB;IAEA,IAAIO,OAAOA,CAAA;MACT,OAAO,CAAC,CAAC,IAAI,CAAC1E,MAAM,IAAI,CAAC,IAAI,CAAC9C,KAAK,CAACC,OAAO;IAC7C;IAEAO,mBAAmBA,CAACiH,GAAW;MAC7B,IAAI,CAAC/E,mBAAmB,CAACuD,IAAI,CAACwB,GAAG,CAAC;IACpC;;uCA3PW3F,mBAAmB,EAAAhD,EAAA,CAAA4I,iBAAA,CAAA5I,EAAA,CAAA6I,iBAAA;IAAA;;YAAnB7F,mBAAmB;MAAA8F,SAAA;MAAAC,SAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UClEhCjJ,EAAA,CAAAC,cAAA,aAA8D;UAmD5DD,EAlDA,CAAAgC,UAAA,IAAAmH,kCAAA,iBAA2E,IAAAC,kCAAA,iBA6CtB,IAAAC,kCAAA,iBAKJ;UAoBjDrJ,EAAA,CAAAyC,SAAA,gBAIM;UAMNzC,EAJA,CAAAgC,UAAA,IAAAsH,kCAAA,iBAA4D,IAAAC,kCAAA,iBAId;UAKhDvJ,EAAA,CAAAG,YAAA,EAAM;;;UAtF6BH,EAAA,CAAAe,UAAA,YAAAmI,GAAA,CAAA1F,cAAA,CAA0B;UAC/BxD,EAAA,CAAAI,SAAA,EAA6C;UAA7CJ,EAAA,CAAAe,UAAA,SAAAmI,GAAA,CAAA3I,KAAA,IAAA2I,GAAA,CAAA5G,qBAAA,CAAAC,QAAA,CAA6C;UA6CnEvC,EAAA,CAAAI,SAAA,EAAmB;UAAnBJ,EAAA,CAAAe,UAAA,SAAAmI,GAAA,CAAAhI,KAAA,CAAAC,OAAA,CAAmB;UAKnBnB,EAAA,CAAAI,SAAA,EAAiB;UAAjBJ,EAAA,CAAAe,UAAA,SAAAmI,GAAA,CAAAhI,KAAA,CAAA4C,KAAA,CAAiB;UAuBrB9D,EAAA,CAAAI,SAAA,EAA6C;UAA7CJ,EAAA,CAAAwJ,WAAA,WAAAN,GAAA,CAAAhI,KAAA,CAAAC,OAAA,IAAA+H,GAAA,CAAAhI,KAAA,CAAA4C,KAAA,CAA6C;UAGzC9D,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAe,UAAA,SAAAmI,GAAA,CAAAhI,KAAA,CAAA6C,UAAA,CAAsB;UAIA/D,EAAA,CAAAI,SAAA,EAAgB;UAAhBJ,EAAA,CAAAe,UAAA,SAAAmI,GAAA,CAAAnG,UAAA,CAAgB;;;qBDpBlCjD,YAAY,EAAA2J,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAE7J,eAAe;MAAA8J,MAAA;MAAAC,eAAA;IAAA;;SAK5B9G,mBAAmB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}