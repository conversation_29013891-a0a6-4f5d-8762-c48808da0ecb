{"ast": null, "code": "export function basis(t1, v0, v1, v2, v3) {\n  var t2 = t1 * t1,\n    t3 = t2 * t1;\n  return ((1 - 3 * t1 + 3 * t2 - t3) * v0 + (4 - 6 * t2 + 3 * t3) * v1 + (1 + 3 * t1 + 3 * t2 - 3 * t3) * v2 + t3 * v3) / 6;\n}\nexport default function (values) {\n  var n = values.length - 1;\n  return function (t) {\n    var i = t <= 0 ? t = 0 : t >= 1 ? (t = 1, n - 1) : Math.floor(t * n),\n      v1 = values[i],\n      v2 = values[i + 1],\n      v0 = i > 0 ? values[i - 1] : 2 * v1 - v2,\n      v3 = i < n - 1 ? values[i + 2] : 2 * v2 - v1;\n    return basis((t - i / n) * n, v0, v1, v2, v3);\n  };\n}", "map": {"version": 3, "names": ["basis", "t1", "v0", "v1", "v2", "v3", "t2", "t3", "values", "n", "length", "t", "i", "Math", "floor"], "sources": ["C:/console/aava-ui-web/node_modules/d3-interpolate/src/basis.js"], "sourcesContent": ["export function basis(t1, v0, v1, v2, v3) {\n  var t2 = t1 * t1, t3 = t2 * t1;\n  return ((1 - 3 * t1 + 3 * t2 - t3) * v0\n      + (4 - 6 * t2 + 3 * t3) * v1\n      + (1 + 3 * t1 + 3 * t2 - 3 * t3) * v2\n      + t3 * v3) / 6;\n}\n\nexport default function(values) {\n  var n = values.length - 1;\n  return function(t) {\n    var i = t <= 0 ? (t = 0) : t >= 1 ? (t = 1, n - 1) : Math.floor(t * n),\n        v1 = values[i],\n        v2 = values[i + 1],\n        v0 = i > 0 ? values[i - 1] : 2 * v1 - v2,\n        v3 = i < n - 1 ? values[i + 2] : 2 * v2 - v1;\n    return basis((t - i / n) * n, v0, v1, v2, v3);\n  };\n}\n"], "mappings": "AAAA,OAAO,SAASA,KAAKA,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;EACxC,IAAIC,EAAE,GAAGL,EAAE,GAAGA,EAAE;IAAEM,EAAE,GAAGD,EAAE,GAAGL,EAAE;EAC9B,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGA,EAAE,GAAG,CAAC,GAAGK,EAAE,GAAGC,EAAE,IAAIL,EAAE,GACjC,CAAC,CAAC,GAAG,CAAC,GAAGI,EAAE,GAAG,CAAC,GAAGC,EAAE,IAAIJ,EAAE,GAC1B,CAAC,CAAC,GAAG,CAAC,GAAGF,EAAE,GAAG,CAAC,GAAGK,EAAE,GAAG,CAAC,GAAGC,EAAE,IAAIH,EAAE,GACnCG,EAAE,GAAGF,EAAE,IAAI,CAAC;AACpB;AAEA,eAAe,UAASG,MAAM,EAAE;EAC9B,IAAIC,CAAC,GAAGD,MAAM,CAACE,MAAM,GAAG,CAAC;EACzB,OAAO,UAASC,CAAC,EAAE;IACjB,IAAIC,CAAC,GAAGD,CAAC,IAAI,CAAC,GAAIA,CAAC,GAAG,CAAC,GAAIA,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAG,CAAC,EAAEF,CAAC,GAAG,CAAC,IAAII,IAAI,CAACC,KAAK,CAACH,CAAC,GAAGF,CAAC,CAAC;MAClEN,EAAE,GAAGK,MAAM,CAACI,CAAC,CAAC;MACdR,EAAE,GAAGI,MAAM,CAACI,CAAC,GAAG,CAAC,CAAC;MAClBV,EAAE,GAAGU,CAAC,GAAG,CAAC,GAAGJ,MAAM,CAACI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAGT,EAAE,GAAGC,EAAE;MACxCC,EAAE,GAAGO,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGD,MAAM,CAACI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAGR,EAAE,GAAGD,EAAE;IAChD,OAAOH,KAAK,CAAC,CAACW,CAAC,GAAGC,CAAC,GAAGH,CAAC,IAAIA,CAAC,EAAEP,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;EAC/C,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}