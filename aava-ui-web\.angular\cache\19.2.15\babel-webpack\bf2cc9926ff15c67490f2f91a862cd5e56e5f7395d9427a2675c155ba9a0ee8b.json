{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, HostListener } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { Subject, takeUntil, forkJoin } from 'rxjs';\nimport * as Highcharts from 'highcharts';\nimport { HighchartsChartModule } from 'highcharts-angular';\nimport NoDataToDisplay from 'highcharts/modules/no-data-to-display';\nimport HC_more from 'highcharts/highcharts-more';\nimport HC_Exporting from 'highcharts/modules/exporting';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatOptionModule } from '@angular/material/core';\nimport { MatInputModule } from '@angular/material/input';\nimport { ButtonComponent, CalendarComponent, TextCardComponent, IconComponent } from '@ava/play-comp-library';\nimport { environment } from 'projects/console/src/environments/environment';\nHC_more(Highcharts);\nNoDataToDisplay(Highcharts);\nHC_Exporting(Highcharts);\nlet AnalyticsComponent = class AnalyticsComponent {\n  analyticsService;\n  fb;\n  destroy$ = new Subject();\n  CHART_COLORS = {\n    primary: '#4F46E5',\n    // Modern Indigo\n    secondary: '#059669',\n    // Emerald Green\n    tertiary: '#DC2626',\n    // Modern Red\n    success: '#10B981',\n    // Success Green\n    warning: '#F59E0B',\n    // Amber Warning\n    danger: '#EF4444' // Error Red\n  };\n  // Professional color palettes for different chart types\n  PROFESSIONAL_COLORS = {\n    userConsumption: '#8B5CF6',\n    // Purple - User related\n    linesOfCode: '#3B82F6',\n    // Blue - Code related\n    topUseCases: '#10B981',\n    // Green - Success/Usage\n    numberOfRequests: '#F59E0B',\n    // Orange - Activity\n    topLanguages: '#EF4444',\n    // Red - Languages\n    userResponse: ['#8B5CF6', '#EC4899', '#F59E0B', '#10B981'],\n    // Multi-color for pie\n    userActivity: ['#10B981', '#EF4444'],\n    // Green/Red for active/inactive\n    responseTime: '#06B6D4',\n    // Cyan - Performance\n    studioUsage: ['#8B5CF6', '#EC4899', '#F59E0B', '#10B981', '#EF4444'],\n    // Multi-color\n    topAgents: '#EC4899',\n    // Pink - Agents\n    agentCreated: '#14B8A6',\n    // Teal - Creation\n    toolAnalytics: ['#F59E0B', '#8B5CF6'],\n    // Orange/Purple for tools\n    toolUsage: '#F97316',\n    // Orange - Tool usage\n    adoptionRate: '#8B5CF6',\n    // Purple - Adoption\n    collaborativeUserConsumption: '#EC4899' // Pink - Collaborative users\n  };\n  CHART_DEFAULTS = {\n    backgroundColor: 'transparent',\n    fontFamily: 'Mulish, sans-serif',\n    height: 320\n  };\n  Highcharts = Highcharts;\n  vmFG;\n  dateRange = {\n    fromDate: this.getDefaultFromDate(),\n    toDate: this.getDefaultToDate()\n  };\n  activeTab = 'usecase';\n  tabs = [{\n    id: 'usecase',\n    label: 'Individual'\n  }, {\n    id: 'agents',\n    label: 'Collaborative'\n  }];\n  tabListStyle = {\n    'background': 'var(--card-bg)',\n    'border': '1px solid var(--card-border)',\n    'border-radius': '8px',\n    'padding': '0.25rem'\n  };\n  isLoading = false;\n  usageLoader = false;\n  metricesLoader = false;\n  consumptionLoader = false;\n  agentMetricsLoader = false;\n  chartLoadingStates = {};\n  chartOptions = {};\n  chartRefs = {};\n  analyticsData = {};\n  useCaseMetrics = {};\n  agentMetrics = {};\n  userActivity = {};\n  userActivityStats = {};\n  userConsumption = [];\n  toolUsage = [];\n  sortedAnalytics = [];\n  agentMetricsTableData = [];\n  expandedRows = new Set();\n  noDataAvailable = false;\n  agentMetricsNoDataAvailable = false;\n  selectedUseCases = [];\n  useCaseList = [];\n  sortedUseCaseList = [];\n  searchText = '';\n  filteringEnabled = false;\n  showDownloadOptions = false;\n  langfuseUrl = environment.consoleLangfuseUrl;\n  ICLAnalyticsUrl = environment.consoleTruelensUrl;\n  constructor(analyticsService, fb) {\n    this.analyticsService = analyticsService;\n    this.fb = fb;\n    this.initializeForm();\n  }\n  ngOnInit() {\n    this.initializeAnalytics();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  initializeForm() {\n    this.vmFG = this.fb.group({\n      fromDate: [this.dateRange.fromDate],\n      toDate: [this.dateRange.toDate]\n    });\n    this.vmFG.valueChanges.pipe(takeUntil(this.destroy$)).subscribe(values => {\n      if (values.fromDate && values.toDate) {\n        this.dateRange.fromDate = values.fromDate;\n        this.dateRange.toDate = values.toDate;\n      }\n    });\n  }\n  initializeAnalytics() {\n    this.loadAnalyticsData();\n  }\n  onTabChange(tabItem) {\n    this.activeTab = tabItem.id;\n    this.loadTabSpecificData(tabItem.id);\n  }\n  getActiveTab() {\n    return this.tabs.find(tab => tab.id === this.activeTab) || this.tabs[0];\n  }\n  loadTabSpecificData(tabId) {\n    switch (tabId) {\n      case 'usecase':\n        this.loadUseCaseData();\n        break;\n      case 'agents':\n        this.loadAgentData();\n        break;\n    }\n  }\n  onDateRangeChange() {\n    const formValues = this.vmFG.value;\n    if (formValues.fromDate && formValues.toDate) {\n      this.dateRange.fromDate = formValues.fromDate;\n      this.dateRange.toDate = formValues.toDate;\n    }\n  }\n  applyFilter() {\n    this.onDateRangeChange();\n    if (!this.isValidDateRange()) {\n      console.error('Invalid date range selected');\n      return;\n    }\n    this.loadAnalyticsData();\n  }\n  isValidDateRange() {\n    const fromDate = new Date(this.dateRange.fromDate);\n    const toDate = new Date(this.dateRange.toDate);\n    return fromDate <= toDate && fromDate <= new Date();\n  }\n  getDefaultFromDate() {\n    const date = new Date();\n    // Set to the 1st day of the current month\n    date.setDate(1);\n    return date.toISOString().split('T')[0];\n  }\n  getDefaultToDate() {\n    return new Date().toISOString().split('T')[0];\n  }\n  formatDateForAPI(dateString) {\n    const [year, month, day] = dateString.split('-');\n    return `${day}-${month}-${year}`;\n  }\n  getFormattedDateRangeForAPI() {\n    return {\n      fromDate: this.formatDateForAPI(this.dateRange.fromDate),\n      toDate: this.formatDateForAPI(this.dateRange.toDate)\n    };\n  }\n  loadAnalyticsData() {\n    this.isLoading = true;\n    switch (this.activeTab) {\n      case 'usecase':\n        this.loadUseCaseData();\n        break;\n      case 'agents':\n        this.loadAgentData();\n        break;\n      default:\n        this.isLoading = false;\n    }\n  }\n  loadUseCaseData() {\n    this.isLoading = true;\n    this.usageLoader = true;\n    const formattedDateRange = this.getFormattedDateRangeForAPI();\n    forkJoin({\n      mainAnalytics: this.analyticsService.getAllAnalyticsData(formattedDateRange),\n      totalRequestMetrics: this.analyticsService.totalRequestCount(formattedDateRange),\n      userConsumption: this.analyticsService.getUserUsageAnalytics(formattedDateRange),\n      userActivity: this.analyticsService.getUserActivity(formattedDateRange),\n      responseTime: this.analyticsService.getResponseTime(formattedDateRange)\n    }).pipe(takeUntil(this.destroy$)).subscribe({\n      next: responses => {\n        this.parseAndCreateChartsWithSeparateAPIs(responses);\n        this.useCaseMetrics = responses.totalRequestMetrics;\n        this.isLoading = false;\n      },\n      error: error => {\n        console.error('Error loading analytics data:', error);\n        this.isLoading = false;\n      }\n    });\n  }\n  parseAndCreateChartsWithSeparateAPIs(responses) {\n    const mainData = responses.mainAnalytics;\n    const userConsumptionData = responses.userConsumption;\n    const userActivityData = responses.userActivity;\n    const responseTimeData = responses.responseTime;\n    Object.keys(this.chartLoadingStates).forEach(key => {\n      this.chartLoadingStates[key] = false;\n    });\n    let userAnalyticsArray = null;\n    if (userConsumptionData) {\n      if (userConsumptionData.userLevelAnalytics && userConsumptionData.userLevelAnalytics.length > 0) {\n        userAnalyticsArray = userConsumptionData.userLevelAnalytics;\n      } else if (userConsumptionData.userAnalytics && userConsumptionData.userAnalytics.length > 0) {\n        userAnalyticsArray = userConsumptionData.userAnalytics;\n      } else if (Array.isArray(userConsumptionData) && userConsumptionData.length > 0) {\n        userAnalyticsArray = userConsumptionData;\n      }\n    }\n    if (userAnalyticsArray && userAnalyticsArray.length > 0) {\n      this.chartOptions['userConsumption'] = this.createUserConsumptionChart(userAnalyticsArray);\n      this.sortedAnalytics = userAnalyticsArray;\n      this.noDataAvailable = false;\n      this.usageLoader = false;\n    } else {\n      this.chartOptions['userConsumption'] = this.createNoDataChart('User Consumption');\n      this.sortedAnalytics = [];\n      this.noDataAvailable = true;\n      this.usageLoader = false;\n    }\n    if (mainData.linesOfCodeAnalytics && mainData.linesOfCodeAnalytics.length > 0) {\n      const categories = mainData.linesOfCodeAnalytics.map(item => item.date);\n      const values = mainData.linesOfCodeAnalytics.map(item => item.count);\n      this.chartOptions['linesOfCodeProcessed'] = this.createLineChart('Lines of code Processed', categories, values, 'Lines of Code');\n    } else {\n      this.chartOptions['linesOfCodeProcessed'] = this.createNoDataChart('Lines of code Processed');\n    }\n    if (mainData.useCaseLevelAnalytics && mainData.useCaseLevelAnalytics.length > 0) {\n      const sortedUseCases = mainData.useCaseLevelAnalytics.sort((a, b) => b.count - a.count).slice(0, 5);\n      const categories = sortedUseCases.map(item => item.useCaseCode);\n      const values = sortedUseCases.map(item => item.count);\n      this.chartOptions['topUseCases'] = this.createColumnChart('Top 5 Individual Agents', categories, values, 'Usage Count', 'agents');\n    } else {\n      this.chartOptions['topUseCases'] = this.createNoDataChart('Top 5 Individual Agents');\n    }\n    let requestData = mainData.numberOfRequestsAnalytics || mainData.numberOfRequestAnalytics;\n    if (requestData && requestData.length > 0) {\n      const categories = requestData.map(item => item.date);\n      const values = requestData.map(item => item.requestCount);\n      this.chartOptions['numberOfRequests'] = this.createLineChart('Number of Requests', categories, values, 'Request Count');\n    } else {\n      this.chartOptions['numberOfRequests'] = this.createNoDataChart('Number of Requests');\n    }\n    if (mainData.programmingLanguageAnalytics && mainData.programmingLanguageAnalytics.length > 0) {\n      const categories = mainData.programmingLanguageAnalytics.map(item => item.programmingLanguage);\n      const values = mainData.programmingLanguageAnalytics.map(item => item.count);\n      this.chartOptions['topLanguages'] = this.createColumnChart('Top 5 Languages / Frameworks', categories, values, 'Usage Count', 'languages');\n    } else {\n      this.chartOptions['topLanguages'] = this.createNoDataChart('Top 5 Languages / Frameworks');\n    }\n    if (mainData.userResponseAnalytics && mainData.userResponseAnalytics.length > 0) {\n      const pieData = mainData.userResponseAnalytics.map(item => [item.response, item.percentage]);\n      this.chartOptions['userResponse'] = this.createPieChart('User Response', pieData, 'userResponse');\n    } else {\n      this.chartOptions['userResponse'] = this.createNoDataChart('User Response');\n    }\n    let activeUsers = 0;\n    let inactiveUsers = 0;\n    let totalUsers = 0;\n    if (userActivityData && userActivityData.userActivity) {\n      const activity = userActivityData.userActivity;\n      activeUsers = activity.activeUsers || 0;\n      inactiveUsers = activity.inactiveUsers || 0;\n      totalUsers = activity.totalUsers || 0;\n    } else if (userActivityData) {\n      activeUsers = userActivityData.activeUsers || 0;\n      inactiveUsers = userActivityData.inactiveUsers || 0;\n      totalUsers = userActivityData.totalUsers || 0;\n    }\n    const total = totalUsers || activeUsers + inactiveUsers;\n    console.log('Active Users:', activeUsers, 'Inactive Users:', inactiveUsers, 'Total:', total);\n    if (total > 0) {\n      const inactivePercentage = inactiveUsers / total * 100;\n      const activePercentage = activeUsers / total * 100;\n      const pieData = [['Active Users', activePercentage], ['Inactive Users', inactivePercentage]];\n      this.chartOptions['userActivity'] = this.createPieChart('% Dormant User', pieData, 'dormantUser');\n      this.userActivity = {\n        activeUsers,\n        dormantUsers: inactiveUsers,\n        totalUsers: total\n      };\n    } else {\n      this.chartOptions['userActivity'] = this.createNoDataChart('% Dormant User');\n    }\n    let categories = [];\n    let values = [];\n    if (responseTimeData) {\n      if (responseTimeData.responseTimes && Array.isArray(responseTimeData.responseTimes)) {\n        categories = responseTimeData.responseTimes.map(item => item.createdAt);\n        values = responseTimeData.responseTimes.map(item => item.responseTime);\n      } else if (responseTimeData.categories && responseTimeData.series) {\n        categories = responseTimeData.categories;\n        values = responseTimeData.series;\n      } else if (responseTimeData.categories && responseTimeData.ySeries) {\n        categories = responseTimeData.categories;\n        values = responseTimeData.ySeries;\n      } else if (Array.isArray(responseTimeData) && responseTimeData.length > 0) {\n        categories = responseTimeData.map(item => item.createdAt || item.date);\n        values = responseTimeData.map(item => item.responseTime || item.time);\n      }\n    }\n    if (categories.length > 0 && values.length > 0) {\n      this.chartOptions['responseTime'] = this.createLineChart('Response Time', categories, values, 'Response Time (ms)');\n    } else {\n      this.chartOptions['responseTime'] = this.createNoDataChart('Response Time');\n    }\n  }\n  loadAgentData() {\n    this.isLoading = true;\n    const formattedDateRange = this.getFormattedDateRangeForAPI();\n    this.analyticsService.getAgenticAIAnalytics(formattedDateRange).pipe(takeUntil(this.destroy$)).subscribe({\n      next: data => {\n        this.processAgentAnalyticsData(data);\n        this.loadAgentCharts(data);\n        this.isLoading = false;\n      },\n      error: error => {\n        console.error('Error loading agent data:', error);\n        this.isLoading = false;\n      }\n    });\n  }\n  processAgentAnalyticsData(data) {\n    this.agentMetrics = data;\n    this.userActivityStats = data.userActivityStats;\n    this.toolUsage = data.toolUsage || [];\n    this.userConsumption = data.userConsumption || [];\n  }\n  toggleRowExpansion(index) {\n    if (this.expandedRows.has(index)) {\n      this.expandedRows.delete(index);\n    } else {\n      this.expandedRows.add(index);\n    }\n  }\n  isRowExpanded(index) {\n    return this.expandedRows.has(index);\n  }\n  loadAgentMetricsTable(data) {\n    this.agentMetricsLoader = true;\n    if (data && data.agentMetrics && data.agentMetrics.length > 0) {\n      this.agentMetricsTableData = data.agentMetrics;\n      this.agentMetricsNoDataAvailable = false;\n    } else {\n      this.agentMetricsTableData = [];\n      this.agentMetricsNoDataAvailable = true;\n    }\n    this.agentMetricsLoader = false;\n  }\n  loadAgentCharts(data) {\n    this.agentMetrics = data;\n    if (data.studioUsage && data.studioUsage.length > 0) {\n      const studioUsageData = data.studioUsage.map(item => [item.domainName, item.percentage]);\n      this.chartOptions['studioUsage'] = this.createPieChart('Studio Usage', studioUsageData, 'studioUsage');\n    } else {\n      this.chartOptions['studioUsage'] = this.createNoDataChart('Studio Usage');\n    }\n    if (data.topAgents && data.topAgents.length > 0) {\n      const categories = data.topAgents.slice(0, 5).map(agent => agent.agentName);\n      const series = data.topAgents.slice(0, 5).map(agent => agent.usageCount);\n      this.chartOptions['topAgents'] = this.createColumnChart('Top 5 Collaborative Agents', categories, series, 'Usage Count', 'topAgents');\n    } else {\n      this.chartOptions['topAgents'] = this.createNoDataChart('Top 5 Collaborative Agents');\n    }\n    if (data.agentCreated && data.agentCreated.length > 0) {\n      const categories = data.agentCreated.map(item => item.teamName);\n      const series = data.agentCreated.map(item => item.usageCount);\n      this.chartOptions['agentCreated'] = this.createColumnChart('Collaborative Agent Created', categories, series, 'Usage Count', 'agentCreated');\n    } else {\n      this.chartOptions['agentCreated'] = this.createNoDataChart('Collaborative Agent Created');\n    }\n    this.loadAgentMetricsTable(data);\n    if (data.toolAnalytics && data.toolAnalytics.length > 0) {\n      const toolAnalyticsItem = data.toolAnalytics[0];\n      const toolAnalyticsData = [['User Defined Tools', toolAnalyticsItem.userDefinedPercentage], ['Built-in Tools', toolAnalyticsItem.builtInPercentage]];\n      this.chartOptions['toolAnalytics'] = this.createPieChart('Tool Analytics', toolAnalyticsData, 'default');\n    } else {\n      this.chartOptions['toolAnalytics'] = this.createNoDataChart('Tool Analytics');\n    }\n    if (data.toolUsage && data.toolUsage.length > 0) {\n      const categories = data.toolUsage.slice(0, 5).map(tool => tool.toolName);\n      const series = data.toolUsage.slice(0, 5).map(tool => tool.usageCount);\n      this.chartOptions['toolUsage'] = this.createHorizontalBarChart('Tool Usage', categories, series, 'Usage Count', 'toolUsage');\n    } else {\n      this.chartOptions['toolUsage'] = this.createNoDataChart('Tool Usage');\n    }\n    if (data.adoptionRate && data.adoptionRate.length > 0) {\n      const categories = data.adoptionRate.map(item => item.workflowName);\n      const series = data.adoptionRate.map(item => item.executionCount);\n      this.chartOptions['adoptionRate'] = this.createLineChart('Adoption Rate', categories, series, 'Execution Count');\n    } else {\n      this.chartOptions['adoptionRate'] = this.createNoDataChart('Adoption Rate');\n    }\n    if (data.userConsumption && data.userConsumption.length > 0) {\n      const transformedData = data.userConsumption.map(user => ({\n        userSignature: user.email,\n        requestCount: user.consumptionCount\n      }));\n      this.chartOptions['userConsumption'] = this.createUserConsumptionChart(transformedData);\n    } else {\n      this.chartOptions['userConsumption'] = this.createNoDataChart('User Consumption');\n    }\n    if (data.userActivityStats && Array.isArray(data.userActivityStats) && data.userActivityStats.length > 0) {\n      const userStats = data.userActivityStats[0];\n      const activePercentage = userStats.activeUserPercentage || 0;\n      const inactivePercentage = userStats.inactiveUserPercentage || 0;\n      console.log('User stats:', userStats);\n      console.log('Active percentage:', activePercentage);\n      console.log('Inactive percentage:', inactivePercentage);\n      if (activePercentage + inactivePercentage > 0) {\n        const dormantUserData = [['Active Users', Math.round(activePercentage * 100) / 100], ['Inactive Users', Math.round(inactivePercentage * 100) / 100]];\n        console.log('Dormant User Chart Data:', dormantUserData);\n        this.chartOptions['userActivity'] = this.createPieChart('% Dormant User', dormantUserData, 'dormantUser');\n      } else {\n        this.chartOptions['userActivity'] = this.createNoDataChart('% Dormant User');\n      }\n    } else {\n      console.log('No user activity stats found');\n      this.chartOptions['userActivity'] = this.createNoDataChart('% Dormant User');\n    }\n  }\n  // Chart creation methods\n  createLineChart(title, categories, data, seriesName) {\n    // Get professional color based on chart title\n    let chartColor = this.CHART_COLORS.primary;\n    if (title.includes('Lines of code')) {\n      chartColor = this.PROFESSIONAL_COLORS.linesOfCode;\n    } else if (title.includes('Number of Requests')) {\n      chartColor = this.PROFESSIONAL_COLORS.numberOfRequests;\n    } else if (title.includes('Response Time')) {\n      chartColor = this.PROFESSIONAL_COLORS.responseTime;\n    } else if (title.includes('Adoption Rate')) {\n      chartColor = this.PROFESSIONAL_COLORS.adoptionRate;\n    }\n    const commonConfig = this.getCommonChartConfig();\n    return {\n      ...commonConfig,\n      chart: {\n        ...commonConfig.chart,\n        type: 'line',\n        height: this.CHART_DEFAULTS.height,\n        zoomType: 'x',\n        panning: {\n          enabled: true,\n          type: 'x'\n        },\n        panKey: 'shift'\n      },\n      xAxis: {\n        categories: categories,\n        labels: {\n          rotation: -45,\n          style: {\n            color: '#6B7280',\n            fontSize: '10px',\n            fontFamily: 'Inter, sans-serif'\n          }\n        },\n        lineColor: '#E5E7EB',\n        tickColor: '#E5E7EB'\n      },\n      yAxis: {\n        title: {\n          text: '',\n          style: {\n            color: '#6B7280'\n          }\n        },\n        labels: {\n          style: {\n            color: '#6B7280',\n            fontSize: '10px',\n            fontFamily: 'Inter, sans-serif'\n          }\n        },\n        gridLineColor: '#F3F4F6'\n      },\n      series: [{\n        name: seriesName,\n        type: 'line',\n        data: data,\n        color: chartColor,\n        lineWidth: 3,\n        marker: {\n          fillColor: chartColor,\n          lineColor: '#FFFFFF',\n          lineWidth: 2,\n          radius: 5\n        },\n        shadow: {\n          color: chartColor,\n          opacity: 0.3,\n          width: 3\n        }\n      }],\n      plotOptions: {\n        line: {\n          dataLabels: {\n            enabled: false\n          },\n          marker: {\n            enabled: true\n          }\n        }\n      }\n    };\n  }\n  createColumnChart(title, categories, data, seriesName, chartType = 'default') {\n    // Get professional color based on chart type\n    let color = this.PROFESSIONAL_COLORS.topUseCases; // Default green\n    switch (chartType) {\n      case 'languages':\n        color = this.PROFESSIONAL_COLORS.topLanguages; // Red for languages\n        break;\n      case 'topAgents':\n        color = this.PROFESSIONAL_COLORS.topAgents; // Pink for top collaborative agents\n        break;\n      case 'agentCreated':\n        color = this.PROFESSIONAL_COLORS.agentCreated; // Teal for agent creation\n        break;\n      case 'agents':\n        color = this.PROFESSIONAL_COLORS.topUseCases; // Green for individual agents\n        break;\n      default:\n        color = this.PROFESSIONAL_COLORS.topUseCases;\n      // Default green\n    }\n    const commonConfig = this.getCommonChartConfig();\n    return {\n      ...commonConfig,\n      chart: {\n        ...commonConfig.chart,\n        type: 'column',\n        height: this.CHART_DEFAULTS.height,\n        zoomType: 'x',\n        panning: {\n          enabled: true,\n          type: 'x'\n        },\n        panKey: 'shift'\n      },\n      xAxis: {\n        categories: categories,\n        labels: {\n          style: {\n            color: '#6B7280',\n            fontSize: '10px',\n            fontFamily: 'Inter, sans-serif'\n          }\n        },\n        lineColor: '#E5E7EB',\n        tickColor: '#E5E7EB'\n      },\n      yAxis: {\n        title: {\n          text: '',\n          style: {\n            color: '#6B7280'\n          }\n        },\n        labels: {\n          style: {\n            color: '#6B7280',\n            fontSize: '10px',\n            fontFamily: 'Inter, sans-serif'\n          }\n        },\n        gridLineColor: '#F3F4F6'\n      },\n      series: [{\n        name: seriesName,\n        type: 'column',\n        data: data,\n        color: color,\n        borderWidth: 0,\n        borderRadius: 6,\n        shadow: {\n          color: color,\n          opacity: 0.2,\n          width: 2\n        }\n      }],\n      plotOptions: {\n        column: {\n          dataLabels: {\n            enabled: true,\n            style: {\n              color: '#333333',\n              // Dark text for better visibility\n              fontSize: '12px',\n              fontWeight: '600',\n              textOutline: '1px contrast' // Add text outline for better readability\n            }\n          },\n          borderRadius: 4,\n          pointPadding: 0.2,\n          // Uniform spacing between bars\n          groupPadding: 0.15,\n          // Uniform spacing between groups\n          maxPointWidth: 60 // Maximum bar width for consistency\n        }\n      }\n    };\n  }\n  createPieChart(title, data, chartType = 'default') {\n    // Professional color schemes for different pie chart types\n    let colors = [];\n    if (chartType === 'userResponse') {\n      colors = this.PROFESSIONAL_COLORS.userResponse; // Multi-color palette\n    } else if (chartType === 'dormantUser') {\n      colors = this.PROFESSIONAL_COLORS.userActivity; // Green/Red for active/inactive\n    } else if (chartType === 'studioUsage') {\n      colors = this.PROFESSIONAL_COLORS.studioUsage; // Multi-color for studio usage\n    } else if (chartType === 'default') {\n      colors = this.PROFESSIONAL_COLORS.toolAnalytics; // Orange/Purple for tool analytics\n    } else {\n      colors = [this.CHART_COLORS.primary, this.CHART_COLORS.secondary]; // Fallback\n    }\n    const commonConfig = this.getCommonChartConfig();\n    return {\n      ...commonConfig,\n      chart: {\n        ...commonConfig.chart,\n        type: 'pie',\n        height: this.CHART_DEFAULTS.height\n      },\n      series: [{\n        name: 'Percentage',\n        type: 'pie',\n        data: data,\n        innerSize: '60%',\n        colors: colors,\n        dataLabels: {\n          enabled: false\n        }\n      }],\n      legend: {\n        enabled: true,\n        align: 'right',\n        verticalAlign: 'middle',\n        layout: 'vertical',\n        itemStyle: {\n          color: '#374151',\n          fontSize: '11px',\n          fontFamily: 'Inter, sans-serif',\n          fontWeight: '500'\n        },\n        symbolRadius: 8,\n        itemMarginBottom: 8\n      },\n      plotOptions: {\n        pie: {\n          allowPointSelect: false,\n          cursor: 'pointer',\n          dataLabels: {\n            enabled: false\n          },\n          showInLegend: true,\n          borderWidth: 0\n        }\n      }\n    };\n  }\n  createUserConsumptionChart(data) {\n    const categories = data.map(item => item.userSignature || item.email || item.userName || 'Unknown User');\n    const values = data.map(item => item.requestCount || item.usageCount || item.count || 0);\n    // Professional height for better visual appearance\n    const chartHeight = this.CHART_DEFAULTS.height;\n    const showScrollbar = categories.length > 8; // Show scrollbar if more than 8 users\n    return {\n      chart: {\n        type: 'bar',\n        backgroundColor: this.CHART_DEFAULTS.backgroundColor,\n        height: chartHeight,\n        marginLeft: 200,\n        // Reduced margin for better space utilization\n        marginRight: 80,\n        // Add right margin for value labels\n        scrollablePlotArea: showScrollbar ? {\n          minHeight: categories.length * 30 + 80,\n          // Reduced spacing\n          scrollPositionY: 0\n        } : undefined,\n        style: {\n          fontFamily: this.CHART_DEFAULTS.fontFamily\n        }\n      },\n      title: {\n        text: '',\n        style: {\n          display: 'none'\n        }\n      },\n      xAxis: {\n        categories: categories,\n        labels: {\n          style: {\n            color: '#333333',\n            fontSize: '11px',\n            fontWeight: '500',\n            fontFamily: 'Mulish, sans-serif'\n          },\n          overflow: 'allow',\n          step: 1,\n          useHTML: true,\n          formatter: function () {\n            // Show full email at the start of each bar\n            const email = this.value;\n            return `<div style=\"width: 180px; text-align: left; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;\">${email}</div>`;\n          }\n        },\n        lineColor: 'transparent',\n        tickColor: 'transparent',\n        min: 0,\n        max: showScrollbar ? Math.min(10, categories.length - 1) : undefined // Show 11 items initially if scrollable\n      },\n      yAxis: {\n        title: {\n          text: '',\n          style: {\n            color: '#666666'\n          }\n        },\n        labels: {\n          style: {\n            color: '#666666',\n            fontSize: '10px',\n            fontFamily: 'Mulish, sans-serif'\n          }\n        },\n        gridLineColor: 'transparent',\n        lineColor: 'transparent'\n      },\n      series: [{\n        name: 'Usage',\n        type: 'bar',\n        data: values,\n        color: '#8B7EC8',\n        borderWidth: 0,\n        borderRadius: 2\n      }],\n      legend: {\n        enabled: false\n      },\n      plotOptions: {\n        bar: {\n          dataLabels: {\n            enabled: true,\n            inside: false,\n            align: 'right',\n            x: 5,\n            // Position labels slightly to the right of bars\n            style: {\n              color: '#333333',\n              fontSize: '12px',\n              fontWeight: '600',\n              fontFamily: 'Mulish, sans-serif',\n              textOutline: 'none'\n            },\n            formatter: function () {\n              return this.y;\n            }\n          },\n          color: '#8B7EC8',\n          borderRadius: 4,\n          pointPadding: 0.15,\n          // Reduced padding for better fit\n          groupPadding: 0.05,\n          // Reduced group padding\n          maxPointWidth: 25 // Reduced max width for better proportion\n        }\n      },\n      tooltip: {\n        enabled: true,\n        formatter: function () {\n          return '<b>' + this.x + '</b><br/>Requests: ' + this.y;\n        }\n      },\n      navigation: {\n        buttonOptions: {\n          enabled: true,\n          theme: {\n            stroke: '#8B7EC8',\n            r: 2,\n            states: {\n              hover: {\n                fill: '#8B7EC8',\n                stroke: '#8B7EC8'\n              },\n              select: {\n                fill: '#8B7EC8',\n                stroke: '#8B7EC8'\n              }\n            }\n          }\n        }\n      },\n      exporting: {\n        enabled: false\n      },\n      credits: {\n        enabled: false\n      }\n    };\n  }\n  createNoDataChart(title) {\n    return {\n      chart: {\n        backgroundColor: 'transparent'\n      },\n      title: {\n        text: title,\n        align: 'left',\n        style: {\n          color: 'var(--text-color)',\n          fontSize: '16px',\n          fontWeight: '600'\n        }\n      },\n      series: [],\n      lang: {\n        noData: 'No data available for the selected period'\n      },\n      noData: {\n        style: {\n          fontWeight: 'bold',\n          fontSize: '15px',\n          color: 'var(--text-secondary)'\n        }\n      },\n      exporting: {\n        enabled: false\n      },\n      credits: {\n        enabled: false\n      }\n    };\n  }\n  // Reusable method to create charts with no-data fallback\n  createChartWithFallback(data, chartCreator, title) {\n    if (data && data.length > 0) {\n      return chartCreator(data);\n    } else {\n      return this.createNoDataChart(title);\n    }\n  }\n  // Common chart styling configuration\n  getCommonChartConfig() {\n    return {\n      chart: {\n        backgroundColor: 'transparent'\n      },\n      title: {\n        text: '',\n        style: {\n          display: 'none'\n        }\n      },\n      exporting: {\n        enabled: false\n      },\n      credits: {\n        enabled: false\n      },\n      legend: {\n        enabled: false\n      }\n    };\n  }\n  // Chart callback functions\n  chartCallback = (chart, chartKey) => {\n    this.chartRefs[chartKey] = chart;\n  };\n  // Export methods\n  downloadExcel(analyticsName) {\n    const formattedDateRange = this.getFormattedDateRangeForAPI();\n    this.analyticsService.downloadChartExcel(formattedDateRange, analyticsName, 'excel');\n  }\n  downloadAgentExcel(analyticsName) {\n    const formattedDateRange = this.getFormattedDateRangeForAPI();\n    this.analyticsService.downloadAgenticAIExcel(formattedDateRange, analyticsName, 'excel');\n  }\n  downloadDump() {\n    const formattedDateRange = this.getFormattedDateRangeForAPI();\n    this.analyticsService.downloadDump(formattedDateRange);\n  }\n  // Filter and dropdown methods\n  onUseCaseSelectionChanged(event) {\n    this.selectedUseCases = event.value;\n    this.filteringEnabled = this.selectedUseCases.length > 0;\n  }\n  setPayload() {\n    // Apply filters and reload data\n    this.applyFilter();\n  }\n  toggleDownloadOptions(event) {\n    event.stopPropagation();\n    this.showDownloadOptions = !this.showDownloadOptions;\n  }\n  downloadChartsAsPDF() {\n    // Implementation for PDF download\n    console.log('Downloading charts as PDF...');\n    this.showDownloadOptions = false;\n  }\n  // Digital Ascender Style Horizontal Bar Chart\n  createHorizontalBarChart(title, categories, data, seriesName, chartType = 'default') {\n    // Professional colors for horizontal bar charts\n    let color = this.PROFESSIONAL_COLORS.toolUsage; // Default orange for tool usage\n    if (chartType === 'toolUsage') {\n      color = this.PROFESSIONAL_COLORS.toolUsage; // Professional orange for tool usage\n    } else if (chartType === 'userConsumption') {\n      color = this.PROFESSIONAL_COLORS.collaborativeUserConsumption; // Pink for collaborative user consumption\n    }\n    // Professional height for better visual appearance\n    const chartHeight = this.CHART_DEFAULTS.height;\n    const showScrollbar = categories.length > 8; // Show scrollbar if more than 8 items\n    const commonConfig = this.getCommonChartConfig();\n    return {\n      ...commonConfig,\n      chart: {\n        ...commonConfig.chart,\n        type: 'bar',\n        height: chartHeight,\n        marginLeft: 180,\n        // Reduced margin for better space utilization\n        marginRight: 80,\n        // Add right margin for value labels\n        scrollablePlotArea: showScrollbar ? {\n          minHeight: categories.length * 30 + 80,\n          // Reduced spacing\n          scrollPositionY: 0\n        } : undefined,\n        zoomType: 'x',\n        panning: {\n          enabled: true,\n          type: 'x'\n        },\n        panKey: 'shift'\n      },\n      xAxis: {\n        categories: categories,\n        title: {\n          text: null\n        },\n        labels: {\n          style: {\n            color: '#333333',\n            fontSize: '11px',\n            fontWeight: '500',\n            fontFamily: 'Mulish, sans-serif'\n          },\n          overflow: 'allow',\n          step: 1,\n          useHTML: true,\n          formatter: function () {\n            // Show full name at the start of each bar\n            const name = this.value;\n            return `<div style=\"width: 160px; text-align: left; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;\">${name}</div>`;\n          }\n        },\n        lineColor: 'transparent',\n        tickColor: 'transparent',\n        min: 0,\n        max: showScrollbar ? Math.min(10, categories.length - 1) : undefined // Show 11 items initially if scrollable\n      },\n      yAxis: {\n        min: 0,\n        title: {\n          text: '',\n          style: {\n            color: '#666666'\n          }\n        },\n        labels: {\n          style: {\n            color: '#666666',\n            fontSize: '10px',\n            fontFamily: 'Mulish, sans-serif'\n          }\n        },\n        gridLineColor: 'transparent',\n        lineColor: 'transparent'\n      },\n      plotOptions: {\n        bar: {\n          dataLabels: {\n            enabled: true,\n            inside: false,\n            align: 'right',\n            x: 5,\n            // Position labels slightly to the right of bars\n            style: {\n              color: '#333333',\n              fontSize: '12px',\n              fontWeight: '600',\n              fontFamily: 'Mulish, sans-serif',\n              textOutline: 'none'\n            },\n            formatter: function () {\n              return this.y;\n            }\n          },\n          color: color,\n          borderRadius: 4,\n          pointPadding: 0.15,\n          // Reduced padding for better fit\n          groupPadding: 0.05,\n          // Reduced group padding\n          maxPointWidth: 25,\n          // Reduced max width for better proportion\n          borderWidth: 0\n        }\n      },\n      series: [{\n        type: 'bar',\n        name: seriesName,\n        data: data,\n        color: color\n      }],\n      legend: {\n        enabled: false\n      },\n      tooltip: {\n        enabled: true,\n        formatter: function () {\n          return '<b>' + this.x + '</b><br/>' + seriesName + ': ' + this.y;\n        }\n      }\n    };\n  }\n  // Common chart configuration helper\n  getBaseChartConfig(type, height = this.CHART_DEFAULTS.height) {\n    return {\n      chart: {\n        type,\n        backgroundColor: this.CHART_DEFAULTS.backgroundColor,\n        height,\n        style: {\n          fontFamily: this.CHART_DEFAULTS.fontFamily\n        }\n      },\n      title: {\n        text: '',\n        style: {\n          display: 'none'\n        }\n      },\n      credits: {\n        enabled: false\n      },\n      legend: {\n        enabled: false\n      }\n    };\n  }\n  // Utility methods for bar charts\n  getBarWidth(value, isToolUsage = false) {\n    if (isToolUsage) {\n      const maxValue = Math.max(...this.toolUsage.map(tool => tool.usageCount));\n      const calculatedWidth = maxValue > 0 ? value / maxValue * 100 : 0;\n      // Ensure minimum width of 25% to show tool names\n      return Math.max(calculatedWidth, 25);\n    } else {\n      // Handle both individual and collaborative user consumption data\n      let maxValue = 0;\n      if (this.activeTab === 'usecase' && this.sortedAnalytics && this.sortedAnalytics.length > 0) {\n        // Individual tab - use sortedAnalytics\n        maxValue = Math.max(...this.sortedAnalytics.map(user => user.requestCount || 0));\n      } else if (this.activeTab === 'agents' && this.userConsumption && this.userConsumption.length > 0) {\n        // Collaborative tab - use userConsumption\n        maxValue = Math.max(...this.userConsumption.map(user => user.consumptionCount || user.requestCount || 0));\n      }\n      const calculatedWidth = maxValue > 0 ? value / maxValue * 100 : 0;\n      // Ensure minimum width of 30% to show email addresses\n      return Math.max(calculatedWidth, 30);\n    }\n  }\n  // Navigation methods\n  goToLangfuse() {\n    window.open(this.langfuseUrl, '_blank');\n  }\n  goToTrulens() {\n    window.open(this.ICLAnalyticsUrl, '_blank');\n  }\n  goToIclAnalytics() {\n    window.open('https://aava-trulens-dev.avateam.io/', '_blank');\n  }\n  onRangeSelected(event) {\n    if (event && event.startDate && event.endDate) {\n      // Convert dates to the format expected by the component\n      const startDate = new Date(event.startDate);\n      const endDate = new Date(event.endDate);\n      this.dateRange.fromDate = startDate.toISOString().split('T')[0];\n      this.dateRange.toDate = endDate.toISOString().split('T')[0];\n      // Update form controls\n      this.vmFG.patchValue({\n        fromDate: this.dateRange.fromDate,\n        toDate: this.dateRange.toDate\n      });\n      // Don't auto-apply filter - let user click the filter button\n      console.log('Date range selected:', this.dateRange);\n    }\n  }\n  getFormattedDateRange() {\n    if (!this.dateRange.fromDate || !this.dateRange.toDate) {\n      return 'No dates selected';\n    }\n    const fromDate = new Date(this.dateRange.fromDate);\n    const toDate = new Date(this.dateRange.toDate);\n    const options = {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    };\n    return `${fromDate.toLocaleDateString('en-US', options)} to ${toDate.toLocaleDateString('en-US', options)}`;\n  }\n  getCalendarDateRange() {\n    if (!this.dateRange.fromDate || !this.dateRange.toDate) {\n      return {\n        start: null,\n        end: null\n      };\n    }\n    return {\n      start: new Date(this.dateRange.fromDate),\n      end: new Date(this.dateRange.toDate)\n    };\n  }\n  onDocumentClick(event) {\n    const target = event.target;\n    const downloadButton = target.closest('.download-button');\n    const downloadDropdown = target.closest('.download-dropdown');\n    if (!downloadButton && !downloadDropdown) {\n      this.showDownloadOptions = false;\n    }\n  }\n  onDownloadToggle(event) {\n    event.stopPropagation();\n    this.showDownloadOptions = !this.showDownloadOptions;\n    console.log('Download toggle clicked, showDownloadOptions:', this.showDownloadOptions);\n  }\n  onPdfDownload() {\n    this.downloadChartsAsPDF();\n    this.showDownloadOptions = false;\n  }\n  onDataDownload() {\n    if (this.activeTab === 'usecase') {\n      this.downloadDump();\n    } else {\n      this.downloadAgentExcel('agentAnalytics');\n    }\n    this.showDownloadOptions = false;\n  }\n};\n__decorate([HostListener('document:click', ['$event'])], AnalyticsComponent.prototype, \"onDocumentClick\", null);\nAnalyticsComponent = __decorate([Component({\n  selector: 'app-analytics',\n  standalone: true,\n  imports: [CommonModule, FormsModule, ReactiveFormsModule, HighchartsChartModule, MatSelectModule, MatFormFieldModule, MatOptionModule, MatInputModule, ButtonComponent, CalendarComponent, TextCardComponent, IconComponent],\n  templateUrl: './analytics.component.html',\n  styleUrls: ['./analytics.component.scss']\n})], AnalyticsComponent);\nexport { AnalyticsComponent };", "map": {"version": 3, "names": ["Component", "HostListener", "CommonModule", "FormsModule", "ReactiveFormsModule", "Subject", "takeUntil", "fork<PERSON><PERSON>n", "Highcharts", "HighchartsChartModule", "NoDataToDisplay", "HC_more", "HC_Exporting", "MatSelectModule", "MatFormFieldModule", "MatOptionModule", "MatInputModule", "ButtonComponent", "CalendarComponent", "TextCardComponent", "IconComponent", "environment", "AnalyticsComponent", "analyticsService", "fb", "destroy$", "CHART_COLORS", "primary", "secondary", "tertiary", "success", "warning", "danger", "PROFESSIONAL_COLORS", "userConsumption", "linesOfCode", "topUseCases", "numberOfRequests", "topLanguages", "userResponse", "userActivity", "responseTime", "studioUsage", "topAgents", "agentCreated", "toolAnalytics", "toolUsage", "adoptionRate", "collaborativeUserConsumption", "CHART_DEFAULTS", "backgroundColor", "fontFamily", "height", "vmFG", "date<PERSON><PERSON><PERSON>", "fromDate", "getDefaultFromDate", "toDate", "getDefaultToDate", "activeTab", "tabs", "id", "label", "tabListStyle", "isLoading", "usageLoader", "metricesLoader", "consumptionLoader", "agentMetricsLoader", "chartLoadingStates", "chartOptions", "chartRefs", "analyticsData", "useCaseMetrics", "agentMetrics", "userActivityStats", "sortedAnalytics", "agentMetricsTableData", "expandedRows", "Set", "noDataAvailable", "agentMetricsNoDataAvailable", "selectedUseCases", "useCaseList", "sortedUseCaseList", "searchText", "filteringEnabled", "showDownloadOptions", "langfuseUrl", "consoleLangfuseUrl", "ICLAnalyticsUrl", "consoleTruelensUrl", "constructor", "initializeForm", "ngOnInit", "initializeAnalytics", "ngOnDestroy", "next", "complete", "group", "valueChanges", "pipe", "subscribe", "values", "loadAnalyticsData", "onTabChange", "tabItem", "loadTabSpecificData", "getActiveTab", "find", "tab", "tabId", "loadUseCaseData", "loadAgentData", "onDateRangeChange", "formValues", "value", "applyFilter", "isValidDateRange", "console", "error", "Date", "date", "setDate", "toISOString", "split", "formatDateForAPI", "dateString", "year", "month", "day", "getFormattedDateRangeForAPI", "formattedDateRange", "mainAnalytics", "getAllAnalyticsData", "totalRequestMetrics", "totalRequestCount", "getUserUsageAnalytics", "getUserActivity", "getResponseTime", "responses", "parseAndCreateChartsWithSeparateAPIs", "mainData", "userConsumptionData", "userActivityData", "responseTimeData", "Object", "keys", "for<PERSON>ach", "key", "userAnalyticsArray", "userLevelAnalytics", "length", "userAnalytics", "Array", "isArray", "createUserConsumptionChart", "createNoDataChart", "linesOfCodeAnalytics", "categories", "map", "item", "count", "createLineChart", "useCaseLevelAnalytics", "sortedUseCases", "sort", "a", "b", "slice", "useCaseCode", "createColumnChart", "requestData", "numberOfRequestsAnalytics", "numberOfRequestAnalytics", "requestCount", "programmingLanguageAnalytics", "programmingLanguage", "userResponseAnalytics", "pieData", "response", "percentage", "create<PERSON>ieChart", "activeUsers", "inactiveUsers", "totalUsers", "activity", "total", "log", "inactivePercentage", "activePercentage", "dormantUsers", "responseTimes", "createdAt", "series", "ySeries", "time", "getAgenticAIAnalytics", "data", "processAgentAnalyticsData", "loadAgentCharts", "toggleRowExpansion", "index", "has", "delete", "add", "isRowExpanded", "loadAgentMetricsTable", "studioUsageData", "domainName", "agent", "<PERSON><PERSON><PERSON>", "usageCount", "teamName", "toolAnalyticsItem", "toolAnalyticsData", "userDefinedPercentage", "builtInPercentage", "tool", "toolName", "createHorizontalBarChart", "workflowName", "executionCount", "transformedData", "user", "userSignature", "email", "consumptionCount", "userStats", "activeUserPercentage", "inactiveUserPercentage", "dormantUserData", "Math", "round", "title", "seriesName", "chartColor", "includes", "commonConfig", "getCommonChartConfig", "chart", "type", "zoomType", "panning", "enabled", "panKey", "xAxis", "labels", "rotation", "style", "color", "fontSize", "lineColor", "tickColor", "yAxis", "text", "gridLineColor", "name", "lineWidth", "marker", "fillColor", "radius", "shadow", "opacity", "width", "plotOptions", "line", "dataLabels", "chartType", "borderWidth", "borderRadius", "column", "fontWeight", "textOutline", "pointPadding", "groupPadding", "maxPointWidth", "colors", "innerSize", "legend", "align", "verticalAlign", "layout", "itemStyle", "symbolRadius", "itemMarginBottom", "pie", "allowPointSelect", "cursor", "showInLegend", "userName", "chartHeight", "showScrollbar", "marginLeft", "marginRight", "scrollablePlotArea", "minHeight", "scrollPositionY", "undefined", "display", "overflow", "step", "useHTML", "formatter", "min", "max", "bar", "inside", "x", "y", "tooltip", "navigation", "buttonOptions", "theme", "stroke", "r", "states", "hover", "fill", "select", "exporting", "credits", "lang", "noData", "createChartWithFallback", "chartCreator", "chartCallback", "chart<PERSON>ey", "downloadExcel", "analyticsName", "downloadChartExcel", "downloadAgentExcel", "downloadAgenticAIExcel", "downloadDump", "onUseCaseSelectionChanged", "event", "setPayload", "toggleDownloadOptions", "stopPropagation", "downloadChartsAsPDF", "getBaseChartConfig", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isToolUsage", "maxValue", "calculatedWidth", "goToLangfuse", "window", "open", "goToTrulens", "goToIclAnalytics", "onRangeSelected", "startDate", "endDate", "patchValue", "getFormattedDateRange", "options", "toLocaleDateString", "getCalendarDateRange", "start", "end", "onDocumentClick", "target", "downloadButton", "closest", "downloadDropdown", "onDownloadToggle", "onPdfDownload", "onDataDownload", "__decorate", "selector", "standalone", "imports", "templateUrl", "styleUrls"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\analytics\\analytics.component.ts"], "sourcesContent": ["import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, HostListener } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';\r\nimport { Subject, takeUntil, forkJoin } from 'rxjs';\r\nimport * as Highcharts from 'highcharts';\r\nimport { HighchartsChartModule } from 'highcharts-angular';\r\nimport NoDataToDisplay from 'highcharts/modules/no-data-to-display';\r\nimport HC_more from 'highcharts/highcharts-more';\r\nimport HC_Exporting from 'highcharts/modules/exporting';\r\nimport { MatSelectModule } from '@angular/material/select';\r\nimport { MatFormFieldModule } from '@angular/material/form-field';\r\nimport { MatOptionModule } from '@angular/material/core';\r\nimport { MatInputModule } from '@angular/material/input';\r\nimport { TabItem, ButtonComponent, CalendarComponent, TextCardComponent, IconComponent } from '@ava/play-comp-library';\r\nimport { AnalyticsService } from '../../shared/services/analytics.service';\r\nimport { environment } from 'projects/console/src/environments/environment';\r\n\r\nHC_more(Highcharts);\r\nNoDataToDisplay(Highcharts);\r\nHC_Exporting(Highcharts);\r\n\r\n// Remove AvaTab interface since we'll use TabItem from the library\r\n\r\ninterface DateRange {\r\n  fromDate: string;\r\n  toDate: string;\r\n}\r\n\r\ninterface ChartColors {\r\n  primary: string;\r\n  secondary: string;\r\n  tertiary: string;\r\n  success: string;\r\n  warning: string;\r\n  danger: string;\r\n}\r\n\r\ninterface ChartDefaults {\r\n  backgroundColor: string;\r\n  fontFamily: string;\r\n  height: number;\r\n}\r\n@Component({\r\n  selector: 'app-analytics',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    HighchartsChartModule,\r\n    MatSelectModule,\r\n    MatFormFieldModule,\r\n    MatOptionModule,\r\n    MatInputModule,\r\n    ButtonComponent,\r\n    CalendarComponent,\r\n    TextCardComponent,\r\n    IconComponent\r\n  ],\r\n  templateUrl: './analytics.component.html',\r\n  styleUrls: ['./analytics.component.scss']\r\n})\r\nexport class AnalyticsComponent implements OnInit, OnDestroy {\r\n  private readonly destroy$ = new Subject<void>();\r\n  private readonly CHART_COLORS: ChartColors = {\r\n    primary: '#4F46E5',      // Modern Indigo\r\n    secondary: '#059669',    // Emerald Green\r\n    tertiary: '#DC2626',     // Modern Red\r\n    success: '#10B981',      // Success Green\r\n    warning: '#F59E0B',      // Amber Warning\r\n    danger: '#EF4444'        // Error Red\r\n  };\r\n\r\n  // Professional color palettes for different chart types\r\n  private readonly PROFESSIONAL_COLORS = {\r\n    userConsumption: '#8B5CF6',     // Purple - User related\r\n    linesOfCode: '#3B82F6',         // Blue - Code related\r\n    topUseCases: '#10B981',         // Green - Success/Usage\r\n    numberOfRequests: '#F59E0B',    // Orange - Activity\r\n    topLanguages: '#EF4444',        // Red - Languages\r\n    userResponse: ['#8B5CF6', '#EC4899', '#F59E0B', '#10B981'], // Multi-color for pie\r\n    userActivity: ['#10B981', '#EF4444'], // Green/Red for active/inactive\r\n    responseTime: '#06B6D4',        // Cyan - Performance\r\n    studioUsage: ['#8B5CF6', '#EC4899', '#F59E0B', '#10B981', '#EF4444'], // Multi-color\r\n    topAgents: '#EC4899',           // Pink - Agents\r\n    agentCreated: '#14B8A6',        // Teal - Creation\r\n    toolAnalytics: ['#F59E0B', '#8B5CF6'], // Orange/Purple for tools\r\n    toolUsage: '#F97316',           // Orange - Tool usage\r\n    adoptionRate: '#8B5CF6',        // Purple - Adoption\r\n    collaborativeUserConsumption: '#EC4899' // Pink - Collaborative users\r\n  };\r\n  private readonly CHART_DEFAULTS: ChartDefaults = {\r\n    backgroundColor: 'transparent',\r\n    fontFamily: 'Mulish, sans-serif',\r\n    height: 320\r\n  };\r\n  Highcharts: typeof Highcharts = Highcharts;\r\n  vmFG!: FormGroup;\r\n  dateRange: DateRange = {\r\n    fromDate: this.getDefaultFromDate(),\r\n    toDate: this.getDefaultToDate()\r\n  };\r\n  activeTab: string = 'usecase';\r\n\r\n  tabs: TabItem[] = [\r\n    {\r\n      id: 'usecase',\r\n      label: 'Individual'\r\n    },\r\n    {\r\n      id: 'agents',\r\n      label: 'Collaborative'\r\n    }\r\n  ];\r\n\r\n  tabListStyle = {\r\n    'background': 'var(--card-bg)',\r\n    'border': '1px solid var(--card-border)',\r\n    'border-radius': '8px',\r\n    'padding': '0.25rem'\r\n  };\r\n  isLoading = false;\r\n  usageLoader = false;\r\n  metricesLoader = false;\r\n  consumptionLoader = false;\r\n  agentMetricsLoader = false;\r\n  chartLoadingStates: { [key: string]: boolean } = {};\r\n  chartOptions: { [key: string]: Highcharts.Options } = {};\r\n  chartRefs: { [key: string]: Highcharts.Chart } = {};\r\n  analyticsData: any = {};\r\n  useCaseMetrics: any = {};\r\n  agentMetrics: any = {};\r\n  userActivity: any = {};\r\n  userActivityStats: any = {};\r\n  userConsumption: any[] = [];\r\n  toolUsage: any[] = [];\r\n  sortedAnalytics: any[] = [];\r\n  agentMetricsTableData: any[] = [];\r\n  expandedRows: Set<number> = new Set();\r\n  noDataAvailable = false;\r\n  agentMetricsNoDataAvailable = false;\r\n  selectedUseCases: string[] = [];\r\n  useCaseList: any[] = [];\r\n  sortedUseCaseList: any[] = [];\r\n  searchText = '';\r\n  filteringEnabled = false;\r\n  showDownloadOptions = false;\r\n  public langfuseUrl = environment.consoleLangfuseUrl;\r\n  public ICLAnalyticsUrl = environment.consoleTruelensUrl;\r\n\r\n  constructor(\r\n    private analyticsService: AnalyticsService,\r\n    private fb: FormBuilder\r\n  ) {\r\n    this.initializeForm();\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.initializeAnalytics();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n  }\r\n\r\n  private initializeForm(): void {\r\n    this.vmFG = this.fb.group({\r\n      fromDate: [this.dateRange.fromDate],\r\n      toDate: [this.dateRange.toDate]\r\n    });\r\n    this.vmFG.valueChanges.pipe(takeUntil(this.destroy$)).subscribe(values => {\r\n      if (values.fromDate && values.toDate) {\r\n        this.dateRange.fromDate = values.fromDate;\r\n        this.dateRange.toDate = values.toDate;\r\n      }\r\n    });\r\n  }\r\n\r\n  private initializeAnalytics(): void {\r\n    this.loadAnalyticsData();\r\n  }\r\n\r\n  onTabChange(tabItem: TabItem): void {\r\n    this.activeTab = tabItem.id;\r\n    this.loadTabSpecificData(tabItem.id);\r\n  }\r\n\r\n  getActiveTab(): TabItem {\r\n    return this.tabs.find(tab => tab.id === this.activeTab) || this.tabs[0];\r\n  }\r\n\r\n  private loadTabSpecificData(tabId: string): void {\r\n    switch (tabId) {\r\n      case 'usecase':\r\n        this.loadUseCaseData();\r\n        break;\r\n      case 'agents':\r\n        this.loadAgentData();\r\n        break;\r\n    }\r\n  }\r\n\r\n  onDateRangeChange(): void {\r\n    const formValues = this.vmFG.value;\r\n    if (formValues.fromDate && formValues.toDate) {\r\n      this.dateRange.fromDate = formValues.fromDate;\r\n      this.dateRange.toDate = formValues.toDate;\r\n    }\r\n  }\r\n\r\n  applyFilter(): void {\r\n    this.onDateRangeChange();\r\n    if (!this.isValidDateRange()) {\r\n      console.error('Invalid date range selected');\r\n      return;\r\n    }\r\n    this.loadAnalyticsData();\r\n  }\r\n\r\n  private isValidDateRange(): boolean {\r\n    const fromDate = new Date(this.dateRange.fromDate);\r\n    const toDate = new Date(this.dateRange.toDate);\r\n    return fromDate <= toDate && fromDate <= new Date();\r\n  }\r\n\r\n  private getDefaultFromDate(): string {\r\n    const date = new Date();\r\n    // Set to the 1st day of the current month\r\n    date.setDate(1);\r\n    return date.toISOString().split('T')[0];\r\n  }\r\n\r\n  private getDefaultToDate(): string {\r\n    return new Date().toISOString().split('T')[0];\r\n  }\r\n\r\n  private formatDateForAPI(dateString: string): string {\r\n    const [year, month, day] = dateString.split('-');\r\n    return `${day}-${month}-${year}`;\r\n  }\r\n\r\n  private getFormattedDateRangeForAPI(): DateRange {\r\n    return {\r\n      fromDate: this.formatDateForAPI(this.dateRange.fromDate),\r\n      toDate: this.formatDateForAPI(this.dateRange.toDate)\r\n    };\r\n  }\r\n\r\n  private loadAnalyticsData(): void {\r\n    this.isLoading = true;\r\n    switch (this.activeTab) {\r\n      case 'usecase':\r\n        this.loadUseCaseData();\r\n        break;\r\n      case 'agents':\r\n        this.loadAgentData();\r\n        break;\r\n      default:\r\n        this.isLoading = false;\r\n    }\r\n  }\r\n\r\n  private loadUseCaseData(): void {\r\n    this.isLoading = true;\r\n    this.usageLoader = true;\r\n    const formattedDateRange = this.getFormattedDateRangeForAPI();\r\n    forkJoin({\r\n      mainAnalytics: this.analyticsService.getAllAnalyticsData(formattedDateRange),\r\n      totalRequestMetrics: this.analyticsService.totalRequestCount(formattedDateRange),\r\n      userConsumption: this.analyticsService.getUserUsageAnalytics(formattedDateRange),\r\n      userActivity: this.analyticsService.getUserActivity(formattedDateRange),\r\n      responseTime: this.analyticsService.getResponseTime(formattedDateRange)\r\n    }).pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (responses) => {\r\n          this.parseAndCreateChartsWithSeparateAPIs(responses);\r\n          this.useCaseMetrics = responses.totalRequestMetrics;\r\n          this.isLoading = false;\r\n        },\r\n        error: (error) => {\r\n          console.error('Error loading analytics data:', error);\r\n          this.isLoading = false;\r\n        }\r\n      });\r\n  }\r\n\r\n  private parseAndCreateChartsWithSeparateAPIs(responses: any): void {\r\n    const mainData = responses.mainAnalytics;\r\n    const userConsumptionData = responses.userConsumption;\r\n    const userActivityData = responses.userActivity;\r\n    const responseTimeData = responses.responseTime;\r\n\r\n    Object.keys(this.chartLoadingStates).forEach(key => {\r\n      this.chartLoadingStates[key] = false;\r\n    });\r\n\r\n    let userAnalyticsArray = null;\r\n    if (userConsumptionData) {\r\n      if (userConsumptionData.userLevelAnalytics && userConsumptionData.userLevelAnalytics.length > 0) {\r\n        userAnalyticsArray = userConsumptionData.userLevelAnalytics;\r\n      } else if (userConsumptionData.userAnalytics && userConsumptionData.userAnalytics.length > 0) {\r\n        userAnalyticsArray = userConsumptionData.userAnalytics;\r\n      } else if (Array.isArray(userConsumptionData) && userConsumptionData.length > 0) {\r\n        userAnalyticsArray = userConsumptionData;\r\n      }\r\n    }\r\n\r\n    if (userAnalyticsArray && userAnalyticsArray.length > 0) {\r\n      this.chartOptions['userConsumption'] = this.createUserConsumptionChart(userAnalyticsArray);\r\n      this.sortedAnalytics = userAnalyticsArray;\r\n      this.noDataAvailable = false;\r\n      this.usageLoader = false;\r\n    } else {\r\n      this.chartOptions['userConsumption'] = this.createNoDataChart('User Consumption');\r\n      this.sortedAnalytics = [];\r\n      this.noDataAvailable = true;\r\n      this.usageLoader = false;\r\n    }\r\n\r\n    if (mainData.linesOfCodeAnalytics && mainData.linesOfCodeAnalytics.length > 0) {\r\n      const categories = mainData.linesOfCodeAnalytics.map((item: any) => item.date);\r\n      const values = mainData.linesOfCodeAnalytics.map((item: any) => item.count);\r\n      this.chartOptions['linesOfCodeProcessed'] = this.createLineChart('Lines of code Processed', categories, values, 'Lines of Code');\r\n    } else {\r\n      this.chartOptions['linesOfCodeProcessed'] = this.createNoDataChart('Lines of code Processed');\r\n    }\r\n\r\n    if (mainData.useCaseLevelAnalytics && mainData.useCaseLevelAnalytics.length > 0) {\r\n      const sortedUseCases = mainData.useCaseLevelAnalytics\r\n        .sort((a: any, b: any) => b.count - a.count)\r\n        .slice(0, 5);\r\n      const categories = sortedUseCases.map((item: any) => item.useCaseCode);\r\n      const values = sortedUseCases.map((item: any) => item.count);\r\n      this.chartOptions['topUseCases'] = this.createColumnChart('Top 5 Individual Agents', categories, values, 'Usage Count', 'agents');\r\n    } else {\r\n      this.chartOptions['topUseCases'] = this.createNoDataChart('Top 5 Individual Agents');\r\n    }\r\n\r\n    let requestData = mainData.numberOfRequestsAnalytics || mainData.numberOfRequestAnalytics;\r\n    if (requestData && requestData.length > 0) {\r\n      const categories = requestData.map((item: any) => item.date);\r\n      const values = requestData.map((item: any) => item.requestCount);\r\n      this.chartOptions['numberOfRequests'] = this.createLineChart('Number of Requests', categories, values, 'Request Count');\r\n    } else {\r\n      this.chartOptions['numberOfRequests'] = this.createNoDataChart('Number of Requests');\r\n    }\r\n\r\n    if (mainData.programmingLanguageAnalytics && mainData.programmingLanguageAnalytics.length > 0) {\r\n      const categories = mainData.programmingLanguageAnalytics.map((item: any) => item.programmingLanguage);\r\n      const values = mainData.programmingLanguageAnalytics.map((item: any) => item.count);\r\n      this.chartOptions['topLanguages'] = this.createColumnChart('Top 5 Languages / Frameworks', categories, values, 'Usage Count', 'languages');\r\n    } else {\r\n      this.chartOptions['topLanguages'] = this.createNoDataChart('Top 5 Languages / Frameworks');\r\n    }\r\n\r\n    if (mainData.userResponseAnalytics && mainData.userResponseAnalytics.length > 0) {\r\n      const pieData = mainData.userResponseAnalytics.map((item: any) => [item.response, item.percentage]);\r\n      this.chartOptions['userResponse'] = this.createPieChart('User Response', pieData, 'userResponse');\r\n    } else {\r\n      this.chartOptions['userResponse'] = this.createNoDataChart('User Response');\r\n    }\r\n\r\n    let activeUsers = 0;\r\n    let inactiveUsers = 0;\r\n    let totalUsers = 0;\r\n\r\n    if (userActivityData && userActivityData.userActivity) {\r\n      const activity = userActivityData.userActivity;\r\n      activeUsers = activity.activeUsers || 0;\r\n      inactiveUsers = activity.inactiveUsers || 0;\r\n      totalUsers = activity.totalUsers || 0;\r\n    } else if (userActivityData) {\r\n      activeUsers = userActivityData.activeUsers || 0;\r\n      inactiveUsers = userActivityData.inactiveUsers || 0;\r\n      totalUsers = userActivityData.totalUsers || 0;\r\n    }\r\n\r\n    const total = totalUsers || (activeUsers + inactiveUsers);\r\n    console.log('Active Users:', activeUsers, 'Inactive Users:', inactiveUsers, 'Total:', total);\r\n\r\n    if (total > 0) {\r\n      const inactivePercentage = (inactiveUsers / total) * 100;\r\n      const activePercentage = (activeUsers / total) * 100;\r\n      const pieData = [\r\n        ['Active Users', activePercentage],\r\n        ['Inactive Users', inactivePercentage]\r\n      ];\r\n      this.chartOptions['userActivity'] = this.createPieChart('% Dormant User', pieData, 'dormantUser');\r\n      this.userActivity = { activeUsers, dormantUsers: inactiveUsers, totalUsers: total };\r\n    } else {\r\n      this.chartOptions['userActivity'] = this.createNoDataChart('% Dormant User');\r\n    }\r\n\r\n    let categories: string[] = [];\r\n    let values: number[] = [];\r\n\r\n    if (responseTimeData) {\r\n      if (responseTimeData.responseTimes && Array.isArray(responseTimeData.responseTimes)) {\r\n        categories = responseTimeData.responseTimes.map((item: any) => item.createdAt);\r\n        values = responseTimeData.responseTimes.map((item: any) => item.responseTime);\r\n      } else if (responseTimeData.categories && responseTimeData.series) {\r\n        categories = responseTimeData.categories;\r\n        values = responseTimeData.series;\r\n      } else if (responseTimeData.categories && responseTimeData.ySeries) {\r\n        categories = responseTimeData.categories;\r\n        values = responseTimeData.ySeries;\r\n      } else if (Array.isArray(responseTimeData) && responseTimeData.length > 0) {\r\n        categories = responseTimeData.map((item: any) => item.createdAt || item.date);\r\n        values = responseTimeData.map((item: any) => item.responseTime || item.time);\r\n      }\r\n    }\r\n\r\n    if (categories.length > 0 && values.length > 0) {\r\n      this.chartOptions['responseTime'] = this.createLineChart('Response Time', categories, values, 'Response Time (ms)');\r\n    } else {\r\n      this.chartOptions['responseTime'] = this.createNoDataChart('Response Time');\r\n    }\r\n  }\r\n\r\n  private loadAgentData(): void {\r\n    this.isLoading = true;\r\n    const formattedDateRange = this.getFormattedDateRangeForAPI();\r\n    this.analyticsService.getAgenticAIAnalytics(formattedDateRange)\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (data) => {\r\n          this.processAgentAnalyticsData(data);\r\n          this.loadAgentCharts(data);\r\n          this.isLoading = false;\r\n        },\r\n        error: (error) => {\r\n          console.error('Error loading agent data:', error);\r\n          this.isLoading = false;\r\n        }\r\n      });\r\n  }\r\n\r\n  private processAgentAnalyticsData(data: any): void {\r\n    this.agentMetrics = data;\r\n    this.userActivityStats = data.userActivityStats;\r\n    this.toolUsage = data.toolUsage || [];\r\n    this.userConsumption = data.userConsumption || [];\r\n  }\r\n\r\n\r\n\r\n  toggleRowExpansion(index: number): void {\r\n    if (this.expandedRows.has(index)) {\r\n      this.expandedRows.delete(index);\r\n    } else {\r\n      this.expandedRows.add(index);\r\n    }\r\n  }\r\n\r\n  isRowExpanded(index: number): boolean {\r\n    return this.expandedRows.has(index);\r\n  }\r\n\r\n  private loadAgentMetricsTable(data: any): void {\r\n    this.agentMetricsLoader = true;\r\n    if (data && data.agentMetrics && data.agentMetrics.length > 0) {\r\n      this.agentMetricsTableData = data.agentMetrics;\r\n      this.agentMetricsNoDataAvailable = false;\r\n    } else {\r\n      this.agentMetricsTableData = [];\r\n      this.agentMetricsNoDataAvailable = true;\r\n    }\r\n    this.agentMetricsLoader = false;\r\n  }\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n  private loadAgentCharts(data: any): void {\r\n\r\n    this.agentMetrics = data;\r\n\r\n    if (data.studioUsage && data.studioUsage.length > 0) {\r\n      const studioUsageData = data.studioUsage.map((item: any) => [item.domainName, item.percentage]);\r\n      this.chartOptions['studioUsage'] = this.createPieChart('Studio Usage', studioUsageData, 'studioUsage');\r\n    } else {\r\n      this.chartOptions['studioUsage'] = this.createNoDataChart('Studio Usage');\r\n    }\r\n\r\n    if (data.topAgents && data.topAgents.length > 0) {\r\n      const categories = data.topAgents.slice(0, 5).map((agent: any) => agent.agentName);\r\n      const series = data.topAgents.slice(0, 5).map((agent: any) => agent.usageCount);\r\n      this.chartOptions['topAgents'] = this.createColumnChart('Top 5 Collaborative Agents', categories, series, 'Usage Count', 'topAgents');\r\n    } else {\r\n      this.chartOptions['topAgents'] = this.createNoDataChart('Top 5 Collaborative Agents');\r\n    }\r\n\r\n    if (data.agentCreated && data.agentCreated.length > 0) {\r\n      const categories = data.agentCreated.map((item: any) => item.teamName);\r\n      const series = data.agentCreated.map((item: any) => item.usageCount);\r\n      this.chartOptions['agentCreated'] = this.createColumnChart('Collaborative Agent Created', categories, series, 'Usage Count', 'agentCreated');\r\n    } else {\r\n      this.chartOptions['agentCreated'] = this.createNoDataChart('Collaborative Agent Created');\r\n    }\r\n\r\n    this.loadAgentMetricsTable(data);\r\n\r\n    if (data.toolAnalytics && data.toolAnalytics.length > 0) {\r\n      const toolAnalyticsItem = data.toolAnalytics[0];\r\n      const toolAnalyticsData = [\r\n        ['User Defined Tools', toolAnalyticsItem.userDefinedPercentage],\r\n        ['Built-in Tools', toolAnalyticsItem.builtInPercentage]\r\n      ];\r\n      this.chartOptions['toolAnalytics'] = this.createPieChart('Tool Analytics', toolAnalyticsData, 'default');\r\n    } else {\r\n      this.chartOptions['toolAnalytics'] = this.createNoDataChart('Tool Analytics');\r\n    }\r\n\r\n    if (data.toolUsage && data.toolUsage.length > 0) {\r\n      const categories = data.toolUsage.slice(0, 5).map((tool: any) => tool.toolName);\r\n      const series = data.toolUsage.slice(0, 5).map((tool: any) => tool.usageCount);\r\n      this.chartOptions['toolUsage'] = this.createHorizontalBarChart('Tool Usage', categories, series, 'Usage Count', 'toolUsage');\r\n    } else {\r\n      this.chartOptions['toolUsage'] = this.createNoDataChart('Tool Usage');\r\n    }\r\n\r\n    if (data.adoptionRate && data.adoptionRate.length > 0) {\r\n      const categories = data.adoptionRate.map((item: any) => item.workflowName);\r\n      const series = data.adoptionRate.map((item: any) => item.executionCount);\r\n      this.chartOptions['adoptionRate'] = this.createLineChart('Adoption Rate', categories, series, 'Execution Count');\r\n    } else {\r\n      this.chartOptions['adoptionRate'] = this.createNoDataChart('Adoption Rate');\r\n    }\r\n\r\n    if (data.userConsumption && data.userConsumption.length > 0) {\r\n      const transformedData = data.userConsumption.map((user: any) => ({\r\n        userSignature: user.email,\r\n        requestCount: user.consumptionCount\r\n      }));\r\n      this.chartOptions['userConsumption'] = this.createUserConsumptionChart(transformedData);\r\n    } else {\r\n      this.chartOptions['userConsumption'] = this.createNoDataChart('User Consumption');\r\n    }\r\n\r\n    if (data.userActivityStats && Array.isArray(data.userActivityStats) && data.userActivityStats.length > 0) {\r\n      const userStats = data.userActivityStats[0];\r\n      const activePercentage = userStats.activeUserPercentage || 0;\r\n      const inactivePercentage = userStats.inactiveUserPercentage || 0;\r\n\r\n      console.log('User stats:', userStats);\r\n      console.log('Active percentage:', activePercentage);\r\n      console.log('Inactive percentage:', inactivePercentage);\r\n\r\n      if (activePercentage + inactivePercentage > 0) {\r\n        const dormantUserData = [\r\n          ['Active Users', Math.round(activePercentage * 100) / 100],\r\n          ['Inactive Users', Math.round(inactivePercentage * 100) / 100]\r\n        ];\r\n\r\n        console.log('Dormant User Chart Data:', dormantUserData);\r\n        this.chartOptions['userActivity'] = this.createPieChart('% Dormant User', dormantUserData, 'dormantUser');\r\n      } else {\r\n        this.chartOptions['userActivity'] = this.createNoDataChart('% Dormant User');\r\n      }\r\n    } else {\r\n      console.log('No user activity stats found');\r\n      this.chartOptions['userActivity'] = this.createNoDataChart('% Dormant User');\r\n    }\r\n  }\r\n\r\n  // Chart creation methods\r\n  private createLineChart(title: string, categories: string[], data: number[], seriesName: string): Highcharts.Options {\r\n    // Get professional color based on chart title\r\n    let chartColor = this.CHART_COLORS.primary;\r\n    if (title.includes('Lines of code')) {\r\n      chartColor = this.PROFESSIONAL_COLORS.linesOfCode;\r\n    } else if (title.includes('Number of Requests')) {\r\n      chartColor = this.PROFESSIONAL_COLORS.numberOfRequests;\r\n    } else if (title.includes('Response Time')) {\r\n      chartColor = this.PROFESSIONAL_COLORS.responseTime;\r\n    } else if (title.includes('Adoption Rate')) {\r\n      chartColor = this.PROFESSIONAL_COLORS.adoptionRate;\r\n    }\r\n\r\n    const commonConfig = this.getCommonChartConfig();\r\n\r\n    return {\r\n      ...commonConfig,\r\n      chart: {\r\n        ...commonConfig.chart,\r\n        type: 'line',\r\n        height: this.CHART_DEFAULTS.height,\r\n        zoomType: 'x',\r\n        panning: {\r\n          enabled: true,\r\n          type: 'x'\r\n        },\r\n        panKey: 'shift'\r\n      } as any,\r\n      xAxis: {\r\n        categories: categories,\r\n        labels: {\r\n          rotation: -45,\r\n          style: {\r\n            color: '#6B7280',\r\n            fontSize: '10px',\r\n            fontFamily: 'Inter, sans-serif'\r\n          }\r\n        },\r\n        lineColor: '#E5E7EB',\r\n        tickColor: '#E5E7EB'\r\n      },\r\n      yAxis: {\r\n        title: {\r\n          text: '',\r\n          style: {\r\n            color: '#6B7280'\r\n          }\r\n        },\r\n        labels: {\r\n          style: {\r\n            color: '#6B7280',\r\n            fontSize: '10px',\r\n            fontFamily: 'Inter, sans-serif'\r\n          }\r\n        },\r\n        gridLineColor: '#F3F4F6'\r\n      },\r\n      series: [{\r\n        name: seriesName,\r\n        type: 'line',\r\n        data: data,\r\n        color: chartColor,\r\n        lineWidth: 3,\r\n        marker: {\r\n          fillColor: chartColor,\r\n          lineColor: '#FFFFFF',\r\n          lineWidth: 2,\r\n          radius: 5\r\n        },\r\n        shadow: {\r\n          color: chartColor,\r\n          opacity: 0.3,\r\n          width: 3\r\n        }\r\n      }],\r\n      plotOptions: {\r\n        line: {\r\n          dataLabels: {\r\n            enabled: false\r\n          },\r\n          marker: {\r\n            enabled: true\r\n          }\r\n        }\r\n      }\r\n    };\r\n  }\r\n\r\n  private createColumnChart(title: string, categories: string[], data: number[], seriesName: string, chartType: string = 'default'): Highcharts.Options {\r\n    // Get professional color based on chart type\r\n    let color = this.PROFESSIONAL_COLORS.topUseCases; // Default green\r\n\r\n    switch (chartType) {\r\n      case 'languages':\r\n        color = this.PROFESSIONAL_COLORS.topLanguages; // Red for languages\r\n        break;\r\n      case 'topAgents':\r\n        color = this.PROFESSIONAL_COLORS.topAgents; // Pink for top collaborative agents\r\n        break;\r\n      case 'agentCreated':\r\n        color = this.PROFESSIONAL_COLORS.agentCreated; // Teal for agent creation\r\n        break;\r\n      case 'agents':\r\n        color = this.PROFESSIONAL_COLORS.topUseCases; // Green for individual agents\r\n        break;\r\n      default:\r\n        color = this.PROFESSIONAL_COLORS.topUseCases; // Default green\r\n    }\r\n\r\n    const commonConfig = this.getCommonChartConfig();\r\n\r\n    return {\r\n      ...commonConfig,\r\n      chart: {\r\n        ...commonConfig.chart,\r\n        type: 'column',\r\n        height: this.CHART_DEFAULTS.height,\r\n        zoomType: 'x',\r\n        panning: {\r\n          enabled: true,\r\n          type: 'x'\r\n        },\r\n        panKey: 'shift'\r\n      } as any,\r\n      xAxis: {\r\n        categories: categories,\r\n        labels: {\r\n          style: {\r\n            color: '#6B7280',\r\n            fontSize: '10px',\r\n            fontFamily: 'Inter, sans-serif'\r\n          }\r\n        },\r\n        lineColor: '#E5E7EB',\r\n        tickColor: '#E5E7EB'\r\n      },\r\n      yAxis: {\r\n        title: {\r\n          text: '',\r\n          style: {\r\n            color: '#6B7280'\r\n          }\r\n        },\r\n        labels: {\r\n          style: {\r\n            color: '#6B7280',\r\n            fontSize: '10px',\r\n            fontFamily: 'Inter, sans-serif'\r\n          }\r\n        },\r\n        gridLineColor: '#F3F4F6'\r\n      },\r\n      series: [{\r\n        name: seriesName,\r\n        type: 'column',\r\n        data: data,\r\n        color: color,\r\n        borderWidth: 0,\r\n        borderRadius: 6,\r\n        shadow: {\r\n          color: color,\r\n          opacity: 0.2,\r\n          width: 2\r\n        }\r\n      }],\r\n      plotOptions: {\r\n        column: {\r\n          dataLabels: {\r\n            enabled: true,\r\n            style: {\r\n              color: '#333333',      // Dark text for better visibility\r\n              fontSize: '12px',\r\n              fontWeight: '600',\r\n              textOutline: '1px contrast' // Add text outline for better readability\r\n            }\r\n          },\r\n          borderRadius: 4,\r\n          pointPadding: 0.2,    // Uniform spacing between bars\r\n          groupPadding: 0.15,   // Uniform spacing between groups\r\n          maxPointWidth: 60     // Maximum bar width for consistency\r\n        }\r\n      }\r\n    };\r\n  }\r\n\r\n  private createPieChart(title: string, data: any[], chartType: string = 'default'): Highcharts.Options {\r\n    // Professional color schemes for different pie chart types\r\n    let colors: string[] = [];\r\n\r\n    if (chartType === 'userResponse') {\r\n      colors = this.PROFESSIONAL_COLORS.userResponse; // Multi-color palette\r\n    } else if (chartType === 'dormantUser') {\r\n      colors = this.PROFESSIONAL_COLORS.userActivity; // Green/Red for active/inactive\r\n    } else if (chartType === 'studioUsage') {\r\n      colors = this.PROFESSIONAL_COLORS.studioUsage; // Multi-color for studio usage\r\n    } else if (chartType === 'default') {\r\n      colors = this.PROFESSIONAL_COLORS.toolAnalytics; // Orange/Purple for tool analytics\r\n    } else {\r\n      colors = [this.CHART_COLORS.primary, this.CHART_COLORS.secondary]; // Fallback\r\n    }\r\n\r\n    const commonConfig = this.getCommonChartConfig();\r\n\r\n    return {\r\n      ...commonConfig,\r\n      chart: {\r\n        ...commonConfig.chart,\r\n        type: 'pie',\r\n        height: this.CHART_DEFAULTS.height\r\n      },\r\n      series: [{\r\n        name: 'Percentage',\r\n        type: 'pie',\r\n        data: data,\r\n        innerSize: '60%',\r\n        colors: colors,\r\n        dataLabels: {\r\n          enabled: false\r\n        }\r\n      }],\r\n      legend: {\r\n        enabled: true,\r\n        align: 'right',\r\n        verticalAlign: 'middle',\r\n        layout: 'vertical',\r\n        itemStyle: {\r\n          color: '#374151',\r\n          fontSize: '11px',\r\n          fontFamily: 'Inter, sans-serif',\r\n          fontWeight: '500'\r\n        },\r\n        symbolRadius: 8,\r\n        itemMarginBottom: 8\r\n      },\r\n      plotOptions: {\r\n        pie: {\r\n          allowPointSelect: false,\r\n          cursor: 'pointer',\r\n          dataLabels: {\r\n            enabled: false\r\n          },\r\n          showInLegend: true,\r\n          borderWidth: 0\r\n        }\r\n      }\r\n    };\r\n  }\r\n\r\n  private createUserConsumptionChart(data: any[]): Highcharts.Options {\r\n    const categories = data.map(item => item.userSignature || item.email || item.userName || 'Unknown User');\r\n    const values = data.map(item => item.requestCount || item.usageCount || item.count || 0);\r\n\r\n    // Professional height for better visual appearance\r\n    const chartHeight = this.CHART_DEFAULTS.height;\r\n    const showScrollbar = categories.length > 8; // Show scrollbar if more than 8 users\r\n\r\n    return {\r\n      chart: {\r\n        type: 'bar',\r\n        backgroundColor: this.CHART_DEFAULTS.backgroundColor,\r\n        height: chartHeight,\r\n        marginLeft: 200, // Reduced margin for better space utilization\r\n        marginRight: 80, // Add right margin for value labels\r\n        scrollablePlotArea: showScrollbar ? {\r\n          minHeight: categories.length * 30 + 80, // Reduced spacing\r\n          scrollPositionY: 0\r\n        } : undefined,\r\n        style: {\r\n          fontFamily: this.CHART_DEFAULTS.fontFamily\r\n        }\r\n      } as any,\r\n      title: {\r\n        text: '',\r\n        style: {\r\n          display: 'none'\r\n        }\r\n      },\r\n      xAxis: {\r\n        categories: categories,\r\n        labels: {\r\n          style: {\r\n            color: '#333333',\r\n            fontSize: '11px',\r\n            fontWeight: '500',\r\n            fontFamily: 'Mulish, sans-serif'\r\n          },\r\n          overflow: 'allow',\r\n          step: 1,\r\n          useHTML: true,\r\n          formatter: function() {\r\n            // Show full email at the start of each bar\r\n            const email = this.value as string;\r\n            return `<div style=\"width: 180px; text-align: left; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;\">${email}</div>`;\r\n          }\r\n        },\r\n        lineColor: 'transparent',\r\n        tickColor: 'transparent',\r\n        min: 0,\r\n        max: showScrollbar ? Math.min(10, categories.length - 1) : undefined // Show 11 items initially if scrollable\r\n      },\r\n      yAxis: {\r\n        title: {\r\n          text: '',\r\n          style: {\r\n            color: '#666666'\r\n          }\r\n        },\r\n        labels: {\r\n          style: {\r\n            color: '#666666',\r\n            fontSize: '10px',\r\n            fontFamily: 'Mulish, sans-serif'\r\n          }\r\n        },\r\n        gridLineColor: 'transparent',\r\n        lineColor: 'transparent'\r\n      },\r\n      series: [{\r\n        name: 'Usage',\r\n        type: 'bar',\r\n        data: values,\r\n        color: '#8B7EC8',\r\n        borderWidth: 0,\r\n        borderRadius: 2\r\n      }],\r\n      legend: {\r\n        enabled: false\r\n      },\r\n      plotOptions: {\r\n        bar: {\r\n          dataLabels: {\r\n            enabled: true,\r\n            inside: false,\r\n            align: 'right',\r\n            x: 5, // Position labels slightly to the right of bars\r\n            style: {\r\n              color: '#333333',\r\n              fontSize: '12px',\r\n              fontWeight: '600',\r\n              fontFamily: 'Mulish, sans-serif',\r\n              textOutline: 'none'\r\n            },\r\n            formatter: function() {\r\n              return this.y;\r\n            }\r\n          },\r\n          color: '#8B7EC8',\r\n          borderRadius: 4,\r\n          pointPadding: 0.15, // Reduced padding for better fit\r\n          groupPadding: 0.05, // Reduced group padding\r\n          maxPointWidth: 25   // Reduced max width for better proportion\r\n        }\r\n      },\r\n      tooltip: {\r\n        enabled: true,\r\n        formatter: function() {\r\n          return '<b>' + this.x + '</b><br/>Requests: ' + this.y;\r\n        }\r\n      },\r\n      navigation: {\r\n        buttonOptions: {\r\n          enabled: true,\r\n          theme: {\r\n            stroke: '#8B7EC8',\r\n            r: 2,\r\n            states: {\r\n              hover: {\r\n                fill: '#8B7EC8',\r\n                stroke: '#8B7EC8'\r\n              },\r\n              select: {\r\n                fill: '#8B7EC8',\r\n                stroke: '#8B7EC8'\r\n              }\r\n            }\r\n          } as any\r\n        }\r\n      },\r\n      exporting: {\r\n        enabled: false\r\n      },\r\n      credits: {\r\n        enabled: false\r\n      }\r\n    };\r\n  }\r\n\r\n  private createNoDataChart(title: string): Highcharts.Options {\r\n    return {\r\n      chart: {\r\n        backgroundColor: 'transparent'\r\n      },\r\n      title: {\r\n        text: title,\r\n        align: 'left',\r\n        style: {\r\n          color: 'var(--text-color)',\r\n          fontSize: '16px',\r\n          fontWeight: '600'\r\n        }\r\n      },\r\n      series: [],\r\n      lang: {\r\n        noData: 'No data available for the selected period'\r\n      },\r\n      noData: {\r\n        style: {\r\n          fontWeight: 'bold',\r\n          fontSize: '15px',\r\n          color: 'var(--text-secondary)'\r\n        }\r\n      },\r\n      exporting: {\r\n        enabled: false\r\n      },\r\n      credits: {\r\n        enabled: false\r\n      }\r\n    };\r\n  }\r\n\r\n  // Reusable method to create charts with no-data fallback\r\n  private createChartWithFallback<T>(\r\n    data: T[] | null | undefined,\r\n    chartCreator: (data: T[]) => Highcharts.Options,\r\n    title: string\r\n  ): Highcharts.Options {\r\n    if (data && data.length > 0) {\r\n      return chartCreator(data);\r\n    } else {\r\n      return this.createNoDataChart(title);\r\n    }\r\n  }\r\n\r\n  // Common chart styling configuration\r\n  private getCommonChartConfig(): Partial<Highcharts.Options> {\r\n    return {\r\n      chart: {\r\n        backgroundColor: 'transparent'\r\n      },\r\n      title: {\r\n        text: '',\r\n        style: {\r\n          display: 'none'\r\n        }\r\n      },\r\n      exporting: {\r\n        enabled: false\r\n      },\r\n      credits: {\r\n        enabled: false\r\n      },\r\n      legend: {\r\n        enabled: false\r\n      }\r\n    };\r\n  }\r\n\r\n  // Chart callback functions\r\n  chartCallback = (chart: Highcharts.Chart, chartKey: string): void => {\r\n    this.chartRefs[chartKey] = chart;\r\n  };\r\n\r\n  // Export methods\r\n  downloadExcel(analyticsName: string): void {\r\n    const formattedDateRange = this.getFormattedDateRangeForAPI();\r\n    this.analyticsService.downloadChartExcel(formattedDateRange, analyticsName, 'excel');\r\n  }\r\n\r\n  downloadAgentExcel(analyticsName: string): void {\r\n    const formattedDateRange = this.getFormattedDateRangeForAPI();\r\n    this.analyticsService.downloadAgenticAIExcel(formattedDateRange, analyticsName, 'excel');\r\n  }\r\n\r\n  downloadDump(): void {\r\n    const formattedDateRange = this.getFormattedDateRangeForAPI();\r\n    this.analyticsService.downloadDump(formattedDateRange);\r\n  }\r\n\r\n  // Filter and dropdown methods\r\n  onUseCaseSelectionChanged(event: any): void {\r\n    this.selectedUseCases = event.value;\r\n    this.filteringEnabled = this.selectedUseCases.length > 0;\r\n  }\r\n\r\n  setPayload(): void {\r\n    // Apply filters and reload data\r\n    this.applyFilter();\r\n  }\r\n\r\n  toggleDownloadOptions(event: Event): void {\r\n    event.stopPropagation();\r\n    this.showDownloadOptions = !this.showDownloadOptions;\r\n  }\r\n\r\n  downloadChartsAsPDF(): void {\r\n    // Implementation for PDF download\r\n    console.log('Downloading charts as PDF...');\r\n    this.showDownloadOptions = false;\r\n  }\r\n\r\n  // Digital Ascender Style Horizontal Bar Chart\r\n  private createHorizontalBarChart(title: string, categories: string[], data: number[], seriesName: string, chartType: string = 'default'): Highcharts.Options {\r\n    // Professional colors for horizontal bar charts\r\n    let color = this.PROFESSIONAL_COLORS.toolUsage; // Default orange for tool usage\r\n\r\n    if (chartType === 'toolUsage') {\r\n      color = this.PROFESSIONAL_COLORS.toolUsage; // Professional orange for tool usage\r\n    } else if (chartType === 'userConsumption') {\r\n      color = this.PROFESSIONAL_COLORS.collaborativeUserConsumption; // Pink for collaborative user consumption\r\n    }\r\n\r\n    // Professional height for better visual appearance\r\n    const chartHeight = this.CHART_DEFAULTS.height;\r\n    const showScrollbar = categories.length > 8; // Show scrollbar if more than 8 items\r\n\r\n    const commonConfig = this.getCommonChartConfig();\r\n\r\n    return {\r\n      ...commonConfig,\r\n      chart: {\r\n        ...commonConfig.chart,\r\n        type: 'bar',\r\n        height: chartHeight,\r\n        marginLeft: 180, // Reduced margin for better space utilization\r\n        marginRight: 80, // Add right margin for value labels\r\n        scrollablePlotArea: showScrollbar ? {\r\n          minHeight: categories.length * 30 + 80, // Reduced spacing\r\n          scrollPositionY: 0\r\n        } : undefined,\r\n        zoomType: 'x',\r\n        panning: {\r\n          enabled: true,\r\n          type: 'x'\r\n        },\r\n        panKey: 'shift'\r\n      } as any,\r\n      xAxis: {\r\n        categories: categories,\r\n        title: {\r\n          text: null\r\n        },\r\n        labels: {\r\n          style: {\r\n            color: '#333333',\r\n            fontSize: '11px',\r\n            fontWeight: '500',\r\n            fontFamily: 'Mulish, sans-serif'\r\n          },\r\n          overflow: 'allow',\r\n          step: 1,\r\n          useHTML: true,\r\n          formatter: function() {\r\n            // Show full name at the start of each bar\r\n            const name = this.value as string;\r\n            return `<div style=\"width: 160px; text-align: left; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;\">${name}</div>`;\r\n          }\r\n        },\r\n        lineColor: 'transparent',\r\n        tickColor: 'transparent',\r\n        min: 0,\r\n        max: showScrollbar ? Math.min(10, categories.length - 1) : undefined // Show 11 items initially if scrollable\r\n      },\r\n      yAxis: {\r\n        min: 0,\r\n        title: {\r\n          text: '',\r\n          style: {\r\n            color: '#666666'\r\n          }\r\n        },\r\n        labels: {\r\n          style: {\r\n            color: '#666666',\r\n            fontSize: '10px',\r\n            fontFamily: 'Mulish, sans-serif'\r\n          }\r\n        },\r\n        gridLineColor: 'transparent',\r\n        lineColor: 'transparent'\r\n      },\r\n      plotOptions: {\r\n        bar: {\r\n          dataLabels: {\r\n            enabled: true,\r\n            inside: false,\r\n            align: 'right',\r\n            x: 5, // Position labels slightly to the right of bars\r\n            style: {\r\n              color: '#333333',\r\n              fontSize: '12px',\r\n              fontWeight: '600',\r\n              fontFamily: 'Mulish, sans-serif',\r\n              textOutline: 'none'\r\n            },\r\n            formatter: function() {\r\n              return this.y;\r\n            }\r\n          },\r\n          color: color,\r\n          borderRadius: 4,\r\n          pointPadding: 0.15, // Reduced padding for better fit\r\n          groupPadding: 0.05, // Reduced group padding\r\n          maxPointWidth: 25,   // Reduced max width for better proportion\r\n          borderWidth: 0\r\n        }\r\n      },\r\n      series: [{\r\n        type: 'bar',\r\n        name: seriesName,\r\n        data: data,\r\n        color: color\r\n      }] as any,\r\n      legend: {\r\n        enabled: false\r\n      },\r\n      tooltip: {\r\n        enabled: true,\r\n        formatter: function() {\r\n          return '<b>' + this.x + '</b><br/>' + seriesName + ': ' + this.y;\r\n        }\r\n      }\r\n    };\r\n  }\r\n\r\n  // Common chart configuration helper\r\n  private getBaseChartConfig(type: 'column' | 'bar' | 'pie' | 'line', height: number = this.CHART_DEFAULTS.height): any {\r\n    return {\r\n      chart: {\r\n        type,\r\n        backgroundColor: this.CHART_DEFAULTS.backgroundColor,\r\n        height,\r\n        style: {\r\n          fontFamily: this.CHART_DEFAULTS.fontFamily\r\n        }\r\n      },\r\n      title: {\r\n        text: '',\r\n        style: { display: 'none' }\r\n      },\r\n      credits: { enabled: false },\r\n      legend: { enabled: false }\r\n    };\r\n  }\r\n\r\n  // Utility methods for bar charts\r\n  getBarWidth(value: number, isToolUsage: boolean = false): number {\r\n    if (isToolUsage) {\r\n      const maxValue = Math.max(...this.toolUsage.map(tool => tool.usageCount));\r\n      const calculatedWidth = maxValue > 0 ? (value / maxValue) * 100 : 0;\r\n      // Ensure minimum width of 25% to show tool names\r\n      return Math.max(calculatedWidth, 25);\r\n    } else {\r\n      // Handle both individual and collaborative user consumption data\r\n      let maxValue = 0;\r\n\r\n      if (this.activeTab === 'usecase' && this.sortedAnalytics && this.sortedAnalytics.length > 0) {\r\n        // Individual tab - use sortedAnalytics\r\n        maxValue = Math.max(...this.sortedAnalytics.map(user => user.requestCount || 0));\r\n      } else if (this.activeTab === 'agents' && this.userConsumption && this.userConsumption.length > 0) {\r\n        // Collaborative tab - use userConsumption\r\n        maxValue = Math.max(...this.userConsumption.map(user => user.consumptionCount || user.requestCount || 0));\r\n      }\r\n\r\n      const calculatedWidth = maxValue > 0 ? (value / maxValue) * 100 : 0;\r\n      // Ensure minimum width of 30% to show email addresses\r\n      return Math.max(calculatedWidth, 30);\r\n    }\r\n  }\r\n\r\n\r\n\r\n  // Navigation methods\r\n  public goToLangfuse() {\r\n    window.open(this.langfuseUrl, '_blank');\r\n  }\r\n\r\n  public goToTrulens() {\r\n    window.open(this.ICLAnalyticsUrl, '_blank');\r\n  }\r\n\r\n  public goToIclAnalytics() {\r\n    window.open('https://aava-trulens-dev.avateam.io/', '_blank');\r\n  }\r\n\r\n  public onRangeSelected(event: any) {\r\n    if (event && event.startDate && event.endDate) {\r\n      // Convert dates to the format expected by the component\r\n      const startDate = new Date(event.startDate);\r\n      const endDate = new Date(event.endDate);\r\n\r\n      this.dateRange.fromDate = startDate.toISOString().split('T')[0];\r\n      this.dateRange.toDate = endDate.toISOString().split('T')[0];\r\n\r\n      // Update form controls\r\n      this.vmFG.patchValue({\r\n        fromDate: this.dateRange.fromDate,\r\n        toDate: this.dateRange.toDate\r\n      });\r\n\r\n      // Don't auto-apply filter - let user click the filter button\r\n      console.log('Date range selected:', this.dateRange);\r\n    }\r\n  }\r\n\r\n  getFormattedDateRange(): string {\r\n    if (!this.dateRange.fromDate || !this.dateRange.toDate) {\r\n      return 'No dates selected';\r\n    }\r\n\r\n    const fromDate = new Date(this.dateRange.fromDate);\r\n    const toDate = new Date(this.dateRange.toDate);\r\n\r\n    const options: Intl.DateTimeFormatOptions = {\r\n      year: 'numeric',\r\n      month: 'short',\r\n      day: 'numeric'\r\n    };\r\n\r\n    return `${fromDate.toLocaleDateString('en-US', options)} to ${toDate.toLocaleDateString('en-US', options)}`;\r\n  }\r\n\r\n  getCalendarDateRange(): { start: Date | null; end: Date | null } {\r\n    if (!this.dateRange.fromDate || !this.dateRange.toDate) {\r\n      return { start: null, end: null };\r\n    }\r\n\r\n    return {\r\n      start: new Date(this.dateRange.fromDate),\r\n      end: new Date(this.dateRange.toDate)\r\n    };\r\n  }\r\n\r\n  @HostListener('document:click', ['$event'])\r\n  onDocumentClick(event: Event) {\r\n    const target = event.target as HTMLElement;\r\n    const downloadButton = target.closest('.download-button');\r\n    const downloadDropdown = target.closest('.download-dropdown');\r\n\r\n    if (!downloadButton && !downloadDropdown) {\r\n      this.showDownloadOptions = false;\r\n    }\r\n  }\r\n\r\n  public onDownloadToggle(event: any) {\r\n    event.stopPropagation();\r\n    this.showDownloadOptions = !this.showDownloadOptions;\r\n    console.log('Download toggle clicked, showDownloadOptions:', this.showDownloadOptions);\r\n  }\r\n\r\n  public onPdfDownload() {\r\n    this.downloadChartsAsPDF();\r\n    this.showDownloadOptions = false;\r\n  }\r\n\r\n  public onDataDownload() {\r\n    if (this.activeTab === 'usecase') {\r\n      this.downloadDump();\r\n    } else {\r\n      this.downloadAgentExcel('agentAnalytics');\r\n    }\r\n    this.showDownloadOptions = false;\r\n  }\r\n\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAqBC,YAAY,QAAQ,eAAe;AAC1E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAgC,gBAAgB;AACzF,SAASC,OAAO,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,MAAM;AACnD,OAAO,KAAKC,UAAU,MAAM,YAAY;AACxC,SAASC,qBAAqB,QAAQ,oBAAoB;AAC1D,OAAOC,eAAe,MAAM,uCAAuC;AACnE,OAAOC,OAAO,MAAM,4BAA4B;AAChD,OAAOC,YAAY,MAAM,8BAA8B;AACvD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAAkBC,eAAe,EAAEC,iBAAiB,EAAEC,iBAAiB,EAAEC,aAAa,QAAQ,wBAAwB;AAEtH,SAASC,WAAW,QAAQ,+CAA+C;AAE3EV,OAAO,CAACH,UAAU,CAAC;AACnBE,eAAe,CAACF,UAAU,CAAC;AAC3BI,YAAY,CAACJ,UAAU,CAAC;AA2CjB,IAAMc,kBAAkB,GAAxB,MAAMA,kBAAkB;EAyFnBC,gBAAA;EACAC,EAAA;EAzFOC,QAAQ,GAAG,IAAIpB,OAAO,EAAQ;EAC9BqB,YAAY,GAAgB;IAC3CC,OAAO,EAAE,SAAS;IAAO;IACzBC,SAAS,EAAE,SAAS;IAAK;IACzBC,QAAQ,EAAE,SAAS;IAAM;IACzBC,OAAO,EAAE,SAAS;IAAO;IACzBC,OAAO,EAAE,SAAS;IAAO;IACzBC,MAAM,EAAE,SAAS,CAAQ;GAC1B;EAED;EACiBC,mBAAmB,GAAG;IACrCC,eAAe,EAAE,SAAS;IAAM;IAChCC,WAAW,EAAE,SAAS;IAAU;IAChCC,WAAW,EAAE,SAAS;IAAU;IAChCC,gBAAgB,EAAE,SAAS;IAAK;IAChCC,YAAY,EAAE,SAAS;IAAS;IAChCC,YAAY,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;IAAE;IAC5DC,YAAY,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;IAAE;IACtCC,YAAY,EAAE,SAAS;IAAS;IAChCC,WAAW,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;IAAE;IACtEC,SAAS,EAAE,SAAS;IAAY;IAChCC,YAAY,EAAE,SAAS;IAAS;IAChCC,aAAa,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;IAAE;IACvCC,SAAS,EAAE,SAAS;IAAY;IAChCC,YAAY,EAAE,SAAS;IAAS;IAChCC,4BAA4B,EAAE,SAAS,CAAC;GACzC;EACgBC,cAAc,GAAkB;IAC/CC,eAAe,EAAE,aAAa;IAC9BC,UAAU,EAAE,oBAAoB;IAChCC,MAAM,EAAE;GACT;EACD5C,UAAU,GAAsBA,UAAU;EAC1C6C,IAAI;EACJC,SAAS,GAAc;IACrBC,QAAQ,EAAE,IAAI,CAACC,kBAAkB,EAAE;IACnCC,MAAM,EAAE,IAAI,CAACC,gBAAgB;GAC9B;EACDC,SAAS,GAAW,SAAS;EAE7BC,IAAI,GAAc,CAChB;IACEC,EAAE,EAAE,SAAS;IACbC,KAAK,EAAE;GACR,EACD;IACED,EAAE,EAAE,QAAQ;IACZC,KAAK,EAAE;GACR,CACF;EAEDC,YAAY,GAAG;IACb,YAAY,EAAE,gBAAgB;IAC9B,QAAQ,EAAE,8BAA8B;IACxC,eAAe,EAAE,KAAK;IACtB,SAAS,EAAE;GACZ;EACDC,SAAS,GAAG,KAAK;EACjBC,WAAW,GAAG,KAAK;EACnBC,cAAc,GAAG,KAAK;EACtBC,iBAAiB,GAAG,KAAK;EACzBC,kBAAkB,GAAG,KAAK;EAC1BC,kBAAkB,GAA+B,EAAE;EACnDC,YAAY,GAA0C,EAAE;EACxDC,SAAS,GAAwC,EAAE;EACnDC,aAAa,GAAQ,EAAE;EACvBC,cAAc,GAAQ,EAAE;EACxBC,YAAY,GAAQ,EAAE;EACtBlC,YAAY,GAAQ,EAAE;EACtBmC,iBAAiB,GAAQ,EAAE;EAC3BzC,eAAe,GAAU,EAAE;EAC3BY,SAAS,GAAU,EAAE;EACrB8B,eAAe,GAAU,EAAE;EAC3BC,qBAAqB,GAAU,EAAE;EACjCC,YAAY,GAAgB,IAAIC,GAAG,EAAE;EACrCC,eAAe,GAAG,KAAK;EACvBC,2BAA2B,GAAG,KAAK;EACnCC,gBAAgB,GAAa,EAAE;EAC/BC,WAAW,GAAU,EAAE;EACvBC,iBAAiB,GAAU,EAAE;EAC7BC,UAAU,GAAG,EAAE;EACfC,gBAAgB,GAAG,KAAK;EACxBC,mBAAmB,GAAG,KAAK;EACpBC,WAAW,GAAGnE,WAAW,CAACoE,kBAAkB;EAC5CC,eAAe,GAAGrE,WAAW,CAACsE,kBAAkB;EAEvDC,YACUrE,gBAAkC,EAClCC,EAAe;IADf,KAAAD,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,EAAE,GAAFA,EAAE;IAEV,IAAI,CAACqE,cAAc,EAAE;EACvB;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACvE,QAAQ,CAACwE,IAAI,EAAE;IACpB,IAAI,CAACxE,QAAQ,CAACyE,QAAQ,EAAE;EAC1B;EAEQL,cAAcA,CAAA;IACpB,IAAI,CAACxC,IAAI,GAAG,IAAI,CAAC7B,EAAE,CAAC2E,KAAK,CAAC;MACxB5C,QAAQ,EAAE,CAAC,IAAI,CAACD,SAAS,CAACC,QAAQ,CAAC;MACnCE,MAAM,EAAE,CAAC,IAAI,CAACH,SAAS,CAACG,MAAM;KAC/B,CAAC;IACF,IAAI,CAACJ,IAAI,CAAC+C,YAAY,CAACC,IAAI,CAAC/F,SAAS,CAAC,IAAI,CAACmB,QAAQ,CAAC,CAAC,CAAC6E,SAAS,CAACC,MAAM,IAAG;MACvE,IAAIA,MAAM,CAAChD,QAAQ,IAAIgD,MAAM,CAAC9C,MAAM,EAAE;QACpC,IAAI,CAACH,SAAS,CAACC,QAAQ,GAAGgD,MAAM,CAAChD,QAAQ;QACzC,IAAI,CAACD,SAAS,CAACG,MAAM,GAAG8C,MAAM,CAAC9C,MAAM;MACvC;IACF,CAAC,CAAC;EACJ;EAEQsC,mBAAmBA,CAAA;IACzB,IAAI,CAACS,iBAAiB,EAAE;EAC1B;EAEAC,WAAWA,CAACC,OAAgB;IAC1B,IAAI,CAAC/C,SAAS,GAAG+C,OAAO,CAAC7C,EAAE;IAC3B,IAAI,CAAC8C,mBAAmB,CAACD,OAAO,CAAC7C,EAAE,CAAC;EACtC;EAEA+C,YAAYA,CAAA;IACV,OAAO,IAAI,CAAChD,IAAI,CAACiD,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACjD,EAAE,KAAK,IAAI,CAACF,SAAS,CAAC,IAAI,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;EACzE;EAEQ+C,mBAAmBA,CAACI,KAAa;IACvC,QAAQA,KAAK;MACX,KAAK,SAAS;QACZ,IAAI,CAACC,eAAe,EAAE;QACtB;MACF,KAAK,QAAQ;QACX,IAAI,CAACC,aAAa,EAAE;QACpB;IACJ;EACF;EAEAC,iBAAiBA,CAAA;IACf,MAAMC,UAAU,GAAG,IAAI,CAAC9D,IAAI,CAAC+D,KAAK;IAClC,IAAID,UAAU,CAAC5D,QAAQ,IAAI4D,UAAU,CAAC1D,MAAM,EAAE;MAC5C,IAAI,CAACH,SAAS,CAACC,QAAQ,GAAG4D,UAAU,CAAC5D,QAAQ;MAC7C,IAAI,CAACD,SAAS,CAACG,MAAM,GAAG0D,UAAU,CAAC1D,MAAM;IAC3C;EACF;EAEA4D,WAAWA,CAAA;IACT,IAAI,CAACH,iBAAiB,EAAE;IACxB,IAAI,CAAC,IAAI,CAACI,gBAAgB,EAAE,EAAE;MAC5BC,OAAO,CAACC,KAAK,CAAC,6BAA6B,CAAC;MAC5C;IACF;IACA,IAAI,CAAChB,iBAAiB,EAAE;EAC1B;EAEQc,gBAAgBA,CAAA;IACtB,MAAM/D,QAAQ,GAAG,IAAIkE,IAAI,CAAC,IAAI,CAACnE,SAAS,CAACC,QAAQ,CAAC;IAClD,MAAME,MAAM,GAAG,IAAIgE,IAAI,CAAC,IAAI,CAACnE,SAAS,CAACG,MAAM,CAAC;IAC9C,OAAOF,QAAQ,IAAIE,MAAM,IAAIF,QAAQ,IAAI,IAAIkE,IAAI,EAAE;EACrD;EAEQjE,kBAAkBA,CAAA;IACxB,MAAMkE,IAAI,GAAG,IAAID,IAAI,EAAE;IACvB;IACAC,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC;IACf,OAAOD,IAAI,CAACE,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACzC;EAEQnE,gBAAgBA,CAAA;IACtB,OAAO,IAAI+D,IAAI,EAAE,CAACG,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC/C;EAEQC,gBAAgBA,CAACC,UAAkB;IACzC,MAAM,CAACC,IAAI,EAAEC,KAAK,EAAEC,GAAG,CAAC,GAAGH,UAAU,CAACF,KAAK,CAAC,GAAG,CAAC;IAChD,OAAO,GAAGK,GAAG,IAAID,KAAK,IAAID,IAAI,EAAE;EAClC;EAEQG,2BAA2BA,CAAA;IACjC,OAAO;MACL5E,QAAQ,EAAE,IAAI,CAACuE,gBAAgB,CAAC,IAAI,CAACxE,SAAS,CAACC,QAAQ,CAAC;MACxDE,MAAM,EAAE,IAAI,CAACqE,gBAAgB,CAAC,IAAI,CAACxE,SAAS,CAACG,MAAM;KACpD;EACH;EAEQ+C,iBAAiBA,CAAA;IACvB,IAAI,CAACxC,SAAS,GAAG,IAAI;IACrB,QAAQ,IAAI,CAACL,SAAS;MACpB,KAAK,SAAS;QACZ,IAAI,CAACqD,eAAe,EAAE;QACtB;MACF,KAAK,QAAQ;QACX,IAAI,CAACC,aAAa,EAAE;QACpB;MACF;QACE,IAAI,CAACjD,SAAS,GAAG,KAAK;IAC1B;EACF;EAEQgD,eAAeA,CAAA;IACrB,IAAI,CAAChD,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,MAAMmE,kBAAkB,GAAG,IAAI,CAACD,2BAA2B,EAAE;IAC7D5H,QAAQ,CAAC;MACP8H,aAAa,EAAE,IAAI,CAAC9G,gBAAgB,CAAC+G,mBAAmB,CAACF,kBAAkB,CAAC;MAC5EG,mBAAmB,EAAE,IAAI,CAAChH,gBAAgB,CAACiH,iBAAiB,CAACJ,kBAAkB,CAAC;MAChFlG,eAAe,EAAE,IAAI,CAACX,gBAAgB,CAACkH,qBAAqB,CAACL,kBAAkB,CAAC;MAChF5F,YAAY,EAAE,IAAI,CAACjB,gBAAgB,CAACmH,eAAe,CAACN,kBAAkB,CAAC;MACvE3F,YAAY,EAAE,IAAI,CAAClB,gBAAgB,CAACoH,eAAe,CAACP,kBAAkB;KACvE,CAAC,CAAC/B,IAAI,CAAC/F,SAAS,CAAC,IAAI,CAACmB,QAAQ,CAAC,CAAC,CAC9B6E,SAAS,CAAC;MACTL,IAAI,EAAG2C,SAAS,IAAI;QAClB,IAAI,CAACC,oCAAoC,CAACD,SAAS,CAAC;QACpD,IAAI,CAACnE,cAAc,GAAGmE,SAAS,CAACL,mBAAmB;QACnD,IAAI,CAACvE,SAAS,GAAG,KAAK;MACxB,CAAC;MACDwD,KAAK,EAAGA,KAAK,IAAI;QACfD,OAAO,CAACC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD,IAAI,CAACxD,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACN;EAEQ6E,oCAAoCA,CAACD,SAAc;IACzD,MAAME,QAAQ,GAAGF,SAAS,CAACP,aAAa;IACxC,MAAMU,mBAAmB,GAAGH,SAAS,CAAC1G,eAAe;IACrD,MAAM8G,gBAAgB,GAAGJ,SAAS,CAACpG,YAAY;IAC/C,MAAMyG,gBAAgB,GAAGL,SAAS,CAACnG,YAAY;IAE/CyG,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC9E,kBAAkB,CAAC,CAAC+E,OAAO,CAACC,GAAG,IAAG;MACjD,IAAI,CAAChF,kBAAkB,CAACgF,GAAG,CAAC,GAAG,KAAK;IACtC,CAAC,CAAC;IAEF,IAAIC,kBAAkB,GAAG,IAAI;IAC7B,IAAIP,mBAAmB,EAAE;MACvB,IAAIA,mBAAmB,CAACQ,kBAAkB,IAAIR,mBAAmB,CAACQ,kBAAkB,CAACC,MAAM,GAAG,CAAC,EAAE;QAC/FF,kBAAkB,GAAGP,mBAAmB,CAACQ,kBAAkB;MAC7D,CAAC,MAAM,IAAIR,mBAAmB,CAACU,aAAa,IAAIV,mBAAmB,CAACU,aAAa,CAACD,MAAM,GAAG,CAAC,EAAE;QAC5FF,kBAAkB,GAAGP,mBAAmB,CAACU,aAAa;MACxD,CAAC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACZ,mBAAmB,CAAC,IAAIA,mBAAmB,CAACS,MAAM,GAAG,CAAC,EAAE;QAC/EF,kBAAkB,GAAGP,mBAAmB;MAC1C;IACF;IAEA,IAAIO,kBAAkB,IAAIA,kBAAkB,CAACE,MAAM,GAAG,CAAC,EAAE;MACvD,IAAI,CAAClF,YAAY,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAACsF,0BAA0B,CAACN,kBAAkB,CAAC;MAC1F,IAAI,CAAC1E,eAAe,GAAG0E,kBAAkB;MACzC,IAAI,CAACtE,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACf,WAAW,GAAG,KAAK;IAC1B,CAAC,MAAM;MACL,IAAI,CAACK,YAAY,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAACuF,iBAAiB,CAAC,kBAAkB,CAAC;MACjF,IAAI,CAACjF,eAAe,GAAG,EAAE;MACzB,IAAI,CAACI,eAAe,GAAG,IAAI;MAC3B,IAAI,CAACf,WAAW,GAAG,KAAK;IAC1B;IAEA,IAAI6E,QAAQ,CAACgB,oBAAoB,IAAIhB,QAAQ,CAACgB,oBAAoB,CAACN,MAAM,GAAG,CAAC,EAAE;MAC7E,MAAMO,UAAU,GAAGjB,QAAQ,CAACgB,oBAAoB,CAACE,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAACvC,IAAI,CAAC;MAC9E,MAAMnB,MAAM,GAAGuC,QAAQ,CAACgB,oBAAoB,CAACE,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAACC,KAAK,CAAC;MAC3E,IAAI,CAAC5F,YAAY,CAAC,sBAAsB,CAAC,GAAG,IAAI,CAAC6F,eAAe,CAAC,yBAAyB,EAAEJ,UAAU,EAAExD,MAAM,EAAE,eAAe,CAAC;IAClI,CAAC,MAAM;MACL,IAAI,CAACjC,YAAY,CAAC,sBAAsB,CAAC,GAAG,IAAI,CAACuF,iBAAiB,CAAC,yBAAyB,CAAC;IAC/F;IAEA,IAAIf,QAAQ,CAACsB,qBAAqB,IAAItB,QAAQ,CAACsB,qBAAqB,CAACZ,MAAM,GAAG,CAAC,EAAE;MAC/E,MAAMa,cAAc,GAAGvB,QAAQ,CAACsB,qBAAqB,CAClDE,IAAI,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAKA,CAAC,CAACN,KAAK,GAAGK,CAAC,CAACL,KAAK,CAAC,CAC3CO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;MACd,MAAMV,UAAU,GAAGM,cAAc,CAACL,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAACS,WAAW,CAAC;MACtE,MAAMnE,MAAM,GAAG8D,cAAc,CAACL,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAACC,KAAK,CAAC;MAC5D,IAAI,CAAC5F,YAAY,CAAC,aAAa,CAAC,GAAG,IAAI,CAACqG,iBAAiB,CAAC,yBAAyB,EAAEZ,UAAU,EAAExD,MAAM,EAAE,aAAa,EAAE,QAAQ,CAAC;IACnI,CAAC,MAAM;MACL,IAAI,CAACjC,YAAY,CAAC,aAAa,CAAC,GAAG,IAAI,CAACuF,iBAAiB,CAAC,yBAAyB,CAAC;IACtF;IAEA,IAAIe,WAAW,GAAG9B,QAAQ,CAAC+B,yBAAyB,IAAI/B,QAAQ,CAACgC,wBAAwB;IACzF,IAAIF,WAAW,IAAIA,WAAW,CAACpB,MAAM,GAAG,CAAC,EAAE;MACzC,MAAMO,UAAU,GAAGa,WAAW,CAACZ,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAACvC,IAAI,CAAC;MAC5D,MAAMnB,MAAM,GAAGqE,WAAW,CAACZ,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAACc,YAAY,CAAC;MAChE,IAAI,CAACzG,YAAY,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAAC6F,eAAe,CAAC,oBAAoB,EAAEJ,UAAU,EAAExD,MAAM,EAAE,eAAe,CAAC;IACzH,CAAC,MAAM;MACL,IAAI,CAACjC,YAAY,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAACuF,iBAAiB,CAAC,oBAAoB,CAAC;IACtF;IAEA,IAAIf,QAAQ,CAACkC,4BAA4B,IAAIlC,QAAQ,CAACkC,4BAA4B,CAACxB,MAAM,GAAG,CAAC,EAAE;MAC7F,MAAMO,UAAU,GAAGjB,QAAQ,CAACkC,4BAA4B,CAAChB,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAACgB,mBAAmB,CAAC;MACrG,MAAM1E,MAAM,GAAGuC,QAAQ,CAACkC,4BAA4B,CAAChB,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAACC,KAAK,CAAC;MACnF,IAAI,CAAC5F,YAAY,CAAC,cAAc,CAAC,GAAG,IAAI,CAACqG,iBAAiB,CAAC,8BAA8B,EAAEZ,UAAU,EAAExD,MAAM,EAAE,aAAa,EAAE,WAAW,CAAC;IAC5I,CAAC,MAAM;MACL,IAAI,CAACjC,YAAY,CAAC,cAAc,CAAC,GAAG,IAAI,CAACuF,iBAAiB,CAAC,8BAA8B,CAAC;IAC5F;IAEA,IAAIf,QAAQ,CAACoC,qBAAqB,IAAIpC,QAAQ,CAACoC,qBAAqB,CAAC1B,MAAM,GAAG,CAAC,EAAE;MAC/E,MAAM2B,OAAO,GAAGrC,QAAQ,CAACoC,qBAAqB,CAAClB,GAAG,CAAEC,IAAS,IAAK,CAACA,IAAI,CAACmB,QAAQ,EAAEnB,IAAI,CAACoB,UAAU,CAAC,CAAC;MACnG,IAAI,CAAC/G,YAAY,CAAC,cAAc,CAAC,GAAG,IAAI,CAACgH,cAAc,CAAC,eAAe,EAAEH,OAAO,EAAE,cAAc,CAAC;IACnG,CAAC,MAAM;MACL,IAAI,CAAC7G,YAAY,CAAC,cAAc,CAAC,GAAG,IAAI,CAACuF,iBAAiB,CAAC,eAAe,CAAC;IAC7E;IAEA,IAAI0B,WAAW,GAAG,CAAC;IACnB,IAAIC,aAAa,GAAG,CAAC;IACrB,IAAIC,UAAU,GAAG,CAAC;IAElB,IAAIzC,gBAAgB,IAAIA,gBAAgB,CAACxG,YAAY,EAAE;MACrD,MAAMkJ,QAAQ,GAAG1C,gBAAgB,CAACxG,YAAY;MAC9C+I,WAAW,GAAGG,QAAQ,CAACH,WAAW,IAAI,CAAC;MACvCC,aAAa,GAAGE,QAAQ,CAACF,aAAa,IAAI,CAAC;MAC3CC,UAAU,GAAGC,QAAQ,CAACD,UAAU,IAAI,CAAC;IACvC,CAAC,MAAM,IAAIzC,gBAAgB,EAAE;MAC3BuC,WAAW,GAAGvC,gBAAgB,CAACuC,WAAW,IAAI,CAAC;MAC/CC,aAAa,GAAGxC,gBAAgB,CAACwC,aAAa,IAAI,CAAC;MACnDC,UAAU,GAAGzC,gBAAgB,CAACyC,UAAU,IAAI,CAAC;IAC/C;IAEA,MAAME,KAAK,GAAGF,UAAU,IAAKF,WAAW,GAAGC,aAAc;IACzDjE,OAAO,CAACqE,GAAG,CAAC,eAAe,EAAEL,WAAW,EAAE,iBAAiB,EAAEC,aAAa,EAAE,QAAQ,EAAEG,KAAK,CAAC;IAE5F,IAAIA,KAAK,GAAG,CAAC,EAAE;MACb,MAAME,kBAAkB,GAAIL,aAAa,GAAGG,KAAK,GAAI,GAAG;MACxD,MAAMG,gBAAgB,GAAIP,WAAW,GAAGI,KAAK,GAAI,GAAG;MACpD,MAAMR,OAAO,GAAG,CACd,CAAC,cAAc,EAAEW,gBAAgB,CAAC,EAClC,CAAC,gBAAgB,EAAED,kBAAkB,CAAC,CACvC;MACD,IAAI,CAACvH,YAAY,CAAC,cAAc,CAAC,GAAG,IAAI,CAACgH,cAAc,CAAC,gBAAgB,EAAEH,OAAO,EAAE,aAAa,CAAC;MACjG,IAAI,CAAC3I,YAAY,GAAG;QAAE+I,WAAW;QAAEQ,YAAY,EAAEP,aAAa;QAAEC,UAAU,EAAEE;MAAK,CAAE;IACrF,CAAC,MAAM;MACL,IAAI,CAACrH,YAAY,CAAC,cAAc,CAAC,GAAG,IAAI,CAACuF,iBAAiB,CAAC,gBAAgB,CAAC;IAC9E;IAEA,IAAIE,UAAU,GAAa,EAAE;IAC7B,IAAIxD,MAAM,GAAa,EAAE;IAEzB,IAAI0C,gBAAgB,EAAE;MACpB,IAAIA,gBAAgB,CAAC+C,aAAa,IAAItC,KAAK,CAACC,OAAO,CAACV,gBAAgB,CAAC+C,aAAa,CAAC,EAAE;QACnFjC,UAAU,GAAGd,gBAAgB,CAAC+C,aAAa,CAAChC,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAACgC,SAAS,CAAC;QAC9E1F,MAAM,GAAG0C,gBAAgB,CAAC+C,aAAa,CAAChC,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAACxH,YAAY,CAAC;MAC/E,CAAC,MAAM,IAAIwG,gBAAgB,CAACc,UAAU,IAAId,gBAAgB,CAACiD,MAAM,EAAE;QACjEnC,UAAU,GAAGd,gBAAgB,CAACc,UAAU;QACxCxD,MAAM,GAAG0C,gBAAgB,CAACiD,MAAM;MAClC,CAAC,MAAM,IAAIjD,gBAAgB,CAACc,UAAU,IAAId,gBAAgB,CAACkD,OAAO,EAAE;QAClEpC,UAAU,GAAGd,gBAAgB,CAACc,UAAU;QACxCxD,MAAM,GAAG0C,gBAAgB,CAACkD,OAAO;MACnC,CAAC,MAAM,IAAIzC,KAAK,CAACC,OAAO,CAACV,gBAAgB,CAAC,IAAIA,gBAAgB,CAACO,MAAM,GAAG,CAAC,EAAE;QACzEO,UAAU,GAAGd,gBAAgB,CAACe,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAACgC,SAAS,IAAIhC,IAAI,CAACvC,IAAI,CAAC;QAC7EnB,MAAM,GAAG0C,gBAAgB,CAACe,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAACxH,YAAY,IAAIwH,IAAI,CAACmC,IAAI,CAAC;MAC9E;IACF;IAEA,IAAIrC,UAAU,CAACP,MAAM,GAAG,CAAC,IAAIjD,MAAM,CAACiD,MAAM,GAAG,CAAC,EAAE;MAC9C,IAAI,CAAClF,YAAY,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC6F,eAAe,CAAC,eAAe,EAAEJ,UAAU,EAAExD,MAAM,EAAE,oBAAoB,CAAC;IACrH,CAAC,MAAM;MACL,IAAI,CAACjC,YAAY,CAAC,cAAc,CAAC,GAAG,IAAI,CAACuF,iBAAiB,CAAC,eAAe,CAAC;IAC7E;EACF;EAEQ5C,aAAaA,CAAA;IACnB,IAAI,CAACjD,SAAS,GAAG,IAAI;IACrB,MAAMoE,kBAAkB,GAAG,IAAI,CAACD,2BAA2B,EAAE;IAC7D,IAAI,CAAC5G,gBAAgB,CAAC8K,qBAAqB,CAACjE,kBAAkB,CAAC,CAC5D/B,IAAI,CAAC/F,SAAS,CAAC,IAAI,CAACmB,QAAQ,CAAC,CAAC,CAC9B6E,SAAS,CAAC;MACTL,IAAI,EAAGqG,IAAI,IAAI;QACb,IAAI,CAACC,yBAAyB,CAACD,IAAI,CAAC;QACpC,IAAI,CAACE,eAAe,CAACF,IAAI,CAAC;QAC1B,IAAI,CAACtI,SAAS,GAAG,KAAK;MACxB,CAAC;MACDwD,KAAK,EAAGA,KAAK,IAAI;QACfD,OAAO,CAACC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,IAAI,CAACxD,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACN;EAEQuI,yBAAyBA,CAACD,IAAS;IACzC,IAAI,CAAC5H,YAAY,GAAG4H,IAAI;IACxB,IAAI,CAAC3H,iBAAiB,GAAG2H,IAAI,CAAC3H,iBAAiB;IAC/C,IAAI,CAAC7B,SAAS,GAAGwJ,IAAI,CAACxJ,SAAS,IAAI,EAAE;IACrC,IAAI,CAACZ,eAAe,GAAGoK,IAAI,CAACpK,eAAe,IAAI,EAAE;EACnD;EAIAuK,kBAAkBA,CAACC,KAAa;IAC9B,IAAI,IAAI,CAAC5H,YAAY,CAAC6H,GAAG,CAACD,KAAK,CAAC,EAAE;MAChC,IAAI,CAAC5H,YAAY,CAAC8H,MAAM,CAACF,KAAK,CAAC;IACjC,CAAC,MAAM;MACL,IAAI,CAAC5H,YAAY,CAAC+H,GAAG,CAACH,KAAK,CAAC;IAC9B;EACF;EAEAI,aAAaA,CAACJ,KAAa;IACzB,OAAO,IAAI,CAAC5H,YAAY,CAAC6H,GAAG,CAACD,KAAK,CAAC;EACrC;EAEQK,qBAAqBA,CAACT,IAAS;IACrC,IAAI,CAAClI,kBAAkB,GAAG,IAAI;IAC9B,IAAIkI,IAAI,IAAIA,IAAI,CAAC5H,YAAY,IAAI4H,IAAI,CAAC5H,YAAY,CAAC8E,MAAM,GAAG,CAAC,EAAE;MAC7D,IAAI,CAAC3E,qBAAqB,GAAGyH,IAAI,CAAC5H,YAAY;MAC9C,IAAI,CAACO,2BAA2B,GAAG,KAAK;IAC1C,CAAC,MAAM;MACL,IAAI,CAACJ,qBAAqB,GAAG,EAAE;MAC/B,IAAI,CAACI,2BAA2B,GAAG,IAAI;IACzC;IACA,IAAI,CAACb,kBAAkB,GAAG,KAAK;EACjC;EAQQoI,eAAeA,CAACF,IAAS;IAE/B,IAAI,CAAC5H,YAAY,GAAG4H,IAAI;IAExB,IAAIA,IAAI,CAAC5J,WAAW,IAAI4J,IAAI,CAAC5J,WAAW,CAAC8G,MAAM,GAAG,CAAC,EAAE;MACnD,MAAMwD,eAAe,GAAGV,IAAI,CAAC5J,WAAW,CAACsH,GAAG,CAAEC,IAAS,IAAK,CAACA,IAAI,CAACgD,UAAU,EAAEhD,IAAI,CAACoB,UAAU,CAAC,CAAC;MAC/F,IAAI,CAAC/G,YAAY,CAAC,aAAa,CAAC,GAAG,IAAI,CAACgH,cAAc,CAAC,cAAc,EAAE0B,eAAe,EAAE,aAAa,CAAC;IACxG,CAAC,MAAM;MACL,IAAI,CAAC1I,YAAY,CAAC,aAAa,CAAC,GAAG,IAAI,CAACuF,iBAAiB,CAAC,cAAc,CAAC;IAC3E;IAEA,IAAIyC,IAAI,CAAC3J,SAAS,IAAI2J,IAAI,CAAC3J,SAAS,CAAC6G,MAAM,GAAG,CAAC,EAAE;MAC/C,MAAMO,UAAU,GAAGuC,IAAI,CAAC3J,SAAS,CAAC8H,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACT,GAAG,CAAEkD,KAAU,IAAKA,KAAK,CAACC,SAAS,CAAC;MAClF,MAAMjB,MAAM,GAAGI,IAAI,CAAC3J,SAAS,CAAC8H,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACT,GAAG,CAAEkD,KAAU,IAAKA,KAAK,CAACE,UAAU,CAAC;MAC/E,IAAI,CAAC9I,YAAY,CAAC,WAAW,CAAC,GAAG,IAAI,CAACqG,iBAAiB,CAAC,4BAA4B,EAAEZ,UAAU,EAAEmC,MAAM,EAAE,aAAa,EAAE,WAAW,CAAC;IACvI,CAAC,MAAM;MACL,IAAI,CAAC5H,YAAY,CAAC,WAAW,CAAC,GAAG,IAAI,CAACuF,iBAAiB,CAAC,4BAA4B,CAAC;IACvF;IAEA,IAAIyC,IAAI,CAAC1J,YAAY,IAAI0J,IAAI,CAAC1J,YAAY,CAAC4G,MAAM,GAAG,CAAC,EAAE;MACrD,MAAMO,UAAU,GAAGuC,IAAI,CAAC1J,YAAY,CAACoH,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAACoD,QAAQ,CAAC;MACtE,MAAMnB,MAAM,GAAGI,IAAI,CAAC1J,YAAY,CAACoH,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAACmD,UAAU,CAAC;MACpE,IAAI,CAAC9I,YAAY,CAAC,cAAc,CAAC,GAAG,IAAI,CAACqG,iBAAiB,CAAC,6BAA6B,EAAEZ,UAAU,EAAEmC,MAAM,EAAE,aAAa,EAAE,cAAc,CAAC;IAC9I,CAAC,MAAM;MACL,IAAI,CAAC5H,YAAY,CAAC,cAAc,CAAC,GAAG,IAAI,CAACuF,iBAAiB,CAAC,6BAA6B,CAAC;IAC3F;IAEA,IAAI,CAACkD,qBAAqB,CAACT,IAAI,CAAC;IAEhC,IAAIA,IAAI,CAACzJ,aAAa,IAAIyJ,IAAI,CAACzJ,aAAa,CAAC2G,MAAM,GAAG,CAAC,EAAE;MACvD,MAAM8D,iBAAiB,GAAGhB,IAAI,CAACzJ,aAAa,CAAC,CAAC,CAAC;MAC/C,MAAM0K,iBAAiB,GAAG,CACxB,CAAC,oBAAoB,EAAED,iBAAiB,CAACE,qBAAqB,CAAC,EAC/D,CAAC,gBAAgB,EAAEF,iBAAiB,CAACG,iBAAiB,CAAC,CACxD;MACD,IAAI,CAACnJ,YAAY,CAAC,eAAe,CAAC,GAAG,IAAI,CAACgH,cAAc,CAAC,gBAAgB,EAAEiC,iBAAiB,EAAE,SAAS,CAAC;IAC1G,CAAC,MAAM;MACL,IAAI,CAACjJ,YAAY,CAAC,eAAe,CAAC,GAAG,IAAI,CAACuF,iBAAiB,CAAC,gBAAgB,CAAC;IAC/E;IAEA,IAAIyC,IAAI,CAACxJ,SAAS,IAAIwJ,IAAI,CAACxJ,SAAS,CAAC0G,MAAM,GAAG,CAAC,EAAE;MAC/C,MAAMO,UAAU,GAAGuC,IAAI,CAACxJ,SAAS,CAAC2H,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACT,GAAG,CAAE0D,IAAS,IAAKA,IAAI,CAACC,QAAQ,CAAC;MAC/E,MAAMzB,MAAM,GAAGI,IAAI,CAACxJ,SAAS,CAAC2H,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACT,GAAG,CAAE0D,IAAS,IAAKA,IAAI,CAACN,UAAU,CAAC;MAC7E,IAAI,CAAC9I,YAAY,CAAC,WAAW,CAAC,GAAG,IAAI,CAACsJ,wBAAwB,CAAC,YAAY,EAAE7D,UAAU,EAAEmC,MAAM,EAAE,aAAa,EAAE,WAAW,CAAC;IAC9H,CAAC,MAAM;MACL,IAAI,CAAC5H,YAAY,CAAC,WAAW,CAAC,GAAG,IAAI,CAACuF,iBAAiB,CAAC,YAAY,CAAC;IACvE;IAEA,IAAIyC,IAAI,CAACvJ,YAAY,IAAIuJ,IAAI,CAACvJ,YAAY,CAACyG,MAAM,GAAG,CAAC,EAAE;MACrD,MAAMO,UAAU,GAAGuC,IAAI,CAACvJ,YAAY,CAACiH,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAAC4D,YAAY,CAAC;MAC1E,MAAM3B,MAAM,GAAGI,IAAI,CAACvJ,YAAY,CAACiH,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAAC6D,cAAc,CAAC;MACxE,IAAI,CAACxJ,YAAY,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC6F,eAAe,CAAC,eAAe,EAAEJ,UAAU,EAAEmC,MAAM,EAAE,iBAAiB,CAAC;IAClH,CAAC,MAAM;MACL,IAAI,CAAC5H,YAAY,CAAC,cAAc,CAAC,GAAG,IAAI,CAACuF,iBAAiB,CAAC,eAAe,CAAC;IAC7E;IAEA,IAAIyC,IAAI,CAACpK,eAAe,IAAIoK,IAAI,CAACpK,eAAe,CAACsH,MAAM,GAAG,CAAC,EAAE;MAC3D,MAAMuE,eAAe,GAAGzB,IAAI,CAACpK,eAAe,CAAC8H,GAAG,CAAEgE,IAAS,KAAM;QAC/DC,aAAa,EAAED,IAAI,CAACE,KAAK;QACzBnD,YAAY,EAAEiD,IAAI,CAACG;OACpB,CAAC,CAAC;MACH,IAAI,CAAC7J,YAAY,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAACsF,0BAA0B,CAACmE,eAAe,CAAC;IACzF,CAAC,MAAM;MACL,IAAI,CAACzJ,YAAY,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAACuF,iBAAiB,CAAC,kBAAkB,CAAC;IACnF;IAEA,IAAIyC,IAAI,CAAC3H,iBAAiB,IAAI+E,KAAK,CAACC,OAAO,CAAC2C,IAAI,CAAC3H,iBAAiB,CAAC,IAAI2H,IAAI,CAAC3H,iBAAiB,CAAC6E,MAAM,GAAG,CAAC,EAAE;MACxG,MAAM4E,SAAS,GAAG9B,IAAI,CAAC3H,iBAAiB,CAAC,CAAC,CAAC;MAC3C,MAAMmH,gBAAgB,GAAGsC,SAAS,CAACC,oBAAoB,IAAI,CAAC;MAC5D,MAAMxC,kBAAkB,GAAGuC,SAAS,CAACE,sBAAsB,IAAI,CAAC;MAEhE/G,OAAO,CAACqE,GAAG,CAAC,aAAa,EAAEwC,SAAS,CAAC;MACrC7G,OAAO,CAACqE,GAAG,CAAC,oBAAoB,EAAEE,gBAAgB,CAAC;MACnDvE,OAAO,CAACqE,GAAG,CAAC,sBAAsB,EAAEC,kBAAkB,CAAC;MAEvD,IAAIC,gBAAgB,GAAGD,kBAAkB,GAAG,CAAC,EAAE;QAC7C,MAAM0C,eAAe,GAAG,CACtB,CAAC,cAAc,EAAEC,IAAI,CAACC,KAAK,CAAC3C,gBAAgB,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,EAC1D,CAAC,gBAAgB,EAAE0C,IAAI,CAACC,KAAK,CAAC5C,kBAAkB,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAC/D;QAEDtE,OAAO,CAACqE,GAAG,CAAC,0BAA0B,EAAE2C,eAAe,CAAC;QACxD,IAAI,CAACjK,YAAY,CAAC,cAAc,CAAC,GAAG,IAAI,CAACgH,cAAc,CAAC,gBAAgB,EAAEiD,eAAe,EAAE,aAAa,CAAC;MAC3G,CAAC,MAAM;QACL,IAAI,CAACjK,YAAY,CAAC,cAAc,CAAC,GAAG,IAAI,CAACuF,iBAAiB,CAAC,gBAAgB,CAAC;MAC9E;IACF,CAAC,MAAM;MACLtC,OAAO,CAACqE,GAAG,CAAC,8BAA8B,CAAC;MAC3C,IAAI,CAACtH,YAAY,CAAC,cAAc,CAAC,GAAG,IAAI,CAACuF,iBAAiB,CAAC,gBAAgB,CAAC;IAC9E;EACF;EAEA;EACQM,eAAeA,CAACuE,KAAa,EAAE3E,UAAoB,EAAEuC,IAAc,EAAEqC,UAAkB;IAC7F;IACA,IAAIC,UAAU,GAAG,IAAI,CAAClN,YAAY,CAACC,OAAO;IAC1C,IAAI+M,KAAK,CAACG,QAAQ,CAAC,eAAe,CAAC,EAAE;MACnCD,UAAU,GAAG,IAAI,CAAC3M,mBAAmB,CAACE,WAAW;IACnD,CAAC,MAAM,IAAIuM,KAAK,CAACG,QAAQ,CAAC,oBAAoB,CAAC,EAAE;MAC/CD,UAAU,GAAG,IAAI,CAAC3M,mBAAmB,CAACI,gBAAgB;IACxD,CAAC,MAAM,IAAIqM,KAAK,CAACG,QAAQ,CAAC,eAAe,CAAC,EAAE;MAC1CD,UAAU,GAAG,IAAI,CAAC3M,mBAAmB,CAACQ,YAAY;IACpD,CAAC,MAAM,IAAIiM,KAAK,CAACG,QAAQ,CAAC,eAAe,CAAC,EAAE;MAC1CD,UAAU,GAAG,IAAI,CAAC3M,mBAAmB,CAACc,YAAY;IACpD;IAEA,MAAM+L,YAAY,GAAG,IAAI,CAACC,oBAAoB,EAAE;IAEhD,OAAO;MACL,GAAGD,YAAY;MACfE,KAAK,EAAE;QACL,GAAGF,YAAY,CAACE,KAAK;QACrBC,IAAI,EAAE,MAAM;QACZ7L,MAAM,EAAE,IAAI,CAACH,cAAc,CAACG,MAAM;QAClC8L,QAAQ,EAAE,GAAG;QACbC,OAAO,EAAE;UACPC,OAAO,EAAE,IAAI;UACbH,IAAI,EAAE;SACP;QACDI,MAAM,EAAE;OACF;MACRC,KAAK,EAAE;QACLvF,UAAU,EAAEA,UAAU;QACtBwF,MAAM,EAAE;UACNC,QAAQ,EAAE,CAAC,EAAE;UACbC,KAAK,EAAE;YACLC,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE,MAAM;YAChBxM,UAAU,EAAE;;SAEf;QACDyM,SAAS,EAAE,SAAS;QACpBC,SAAS,EAAE;OACZ;MACDC,KAAK,EAAE;QACLpB,KAAK,EAAE;UACLqB,IAAI,EAAE,EAAE;UACRN,KAAK,EAAE;YACLC,KAAK,EAAE;;SAEV;QACDH,MAAM,EAAE;UACNE,KAAK,EAAE;YACLC,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE,MAAM;YAChBxM,UAAU,EAAE;;SAEf;QACD6M,aAAa,EAAE;OAChB;MACD9D,MAAM,EAAE,CAAC;QACP+D,IAAI,EAAEtB,UAAU;QAChBM,IAAI,EAAE,MAAM;QACZ3C,IAAI,EAAEA,IAAI;QACVoD,KAAK,EAAEd,UAAU;QACjBsB,SAAS,EAAE,CAAC;QACZC,MAAM,EAAE;UACNC,SAAS,EAAExB,UAAU;UACrBgB,SAAS,EAAE,SAAS;UACpBM,SAAS,EAAE,CAAC;UACZG,MAAM,EAAE;SACT;QACDC,MAAM,EAAE;UACNZ,KAAK,EAAEd,UAAU;UACjB2B,OAAO,EAAE,GAAG;UACZC,KAAK,EAAE;;OAEV,CAAC;MACFC,WAAW,EAAE;QACXC,IAAI,EAAE;UACJC,UAAU,EAAE;YACVvB,OAAO,EAAE;WACV;UACDe,MAAM,EAAE;YACNf,OAAO,EAAE;;;;KAIhB;EACH;EAEQzE,iBAAiBA,CAAC+D,KAAa,EAAE3E,UAAoB,EAAEuC,IAAc,EAAEqC,UAAkB,EAAEiC,SAAA,GAAoB,SAAS;IAC9H;IACA,IAAIlB,KAAK,GAAG,IAAI,CAACzN,mBAAmB,CAACG,WAAW,CAAC,CAAC;IAElD,QAAQwO,SAAS;MACf,KAAK,WAAW;QACdlB,KAAK,GAAG,IAAI,CAACzN,mBAAmB,CAACK,YAAY,CAAC,CAAC;QAC/C;MACF,KAAK,WAAW;QACdoN,KAAK,GAAG,IAAI,CAACzN,mBAAmB,CAACU,SAAS,CAAC,CAAC;QAC5C;MACF,KAAK,cAAc;QACjB+M,KAAK,GAAG,IAAI,CAACzN,mBAAmB,CAACW,YAAY,CAAC,CAAC;QAC/C;MACF,KAAK,QAAQ;QACX8M,KAAK,GAAG,IAAI,CAACzN,mBAAmB,CAACG,WAAW,CAAC,CAAC;QAC9C;MACF;QACEsN,KAAK,GAAG,IAAI,CAACzN,mBAAmB,CAACG,WAAW;MAAE;IAClD;IAEA,MAAM0M,YAAY,GAAG,IAAI,CAACC,oBAAoB,EAAE;IAEhD,OAAO;MACL,GAAGD,YAAY;MACfE,KAAK,EAAE;QACL,GAAGF,YAAY,CAACE,KAAK;QACrBC,IAAI,EAAE,QAAQ;QACd7L,MAAM,EAAE,IAAI,CAACH,cAAc,CAACG,MAAM;QAClC8L,QAAQ,EAAE,GAAG;QACbC,OAAO,EAAE;UACPC,OAAO,EAAE,IAAI;UACbH,IAAI,EAAE;SACP;QACDI,MAAM,EAAE;OACF;MACRC,KAAK,EAAE;QACLvF,UAAU,EAAEA,UAAU;QACtBwF,MAAM,EAAE;UACNE,KAAK,EAAE;YACLC,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE,MAAM;YAChBxM,UAAU,EAAE;;SAEf;QACDyM,SAAS,EAAE,SAAS;QACpBC,SAAS,EAAE;OACZ;MACDC,KAAK,EAAE;QACLpB,KAAK,EAAE;UACLqB,IAAI,EAAE,EAAE;UACRN,KAAK,EAAE;YACLC,KAAK,EAAE;;SAEV;QACDH,MAAM,EAAE;UACNE,KAAK,EAAE;YACLC,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE,MAAM;YAChBxM,UAAU,EAAE;;SAEf;QACD6M,aAAa,EAAE;OAChB;MACD9D,MAAM,EAAE,CAAC;QACP+D,IAAI,EAAEtB,UAAU;QAChBM,IAAI,EAAE,QAAQ;QACd3C,IAAI,EAAEA,IAAI;QACVoD,KAAK,EAAEA,KAAK;QACZmB,WAAW,EAAE,CAAC;QACdC,YAAY,EAAE,CAAC;QACfR,MAAM,EAAE;UACNZ,KAAK,EAAEA,KAAK;UACZa,OAAO,EAAE,GAAG;UACZC,KAAK,EAAE;;OAEV,CAAC;MACFC,WAAW,EAAE;QACXM,MAAM,EAAE;UACNJ,UAAU,EAAE;YACVvB,OAAO,EAAE,IAAI;YACbK,KAAK,EAAE;cACLC,KAAK,EAAE,SAAS;cAAO;cACvBC,QAAQ,EAAE,MAAM;cAChBqB,UAAU,EAAE,KAAK;cACjBC,WAAW,EAAE,cAAc,CAAC;;WAE/B;UACDH,YAAY,EAAE,CAAC;UACfI,YAAY,EAAE,GAAG;UAAK;UACtBC,YAAY,EAAE,IAAI;UAAI;UACtBC,aAAa,EAAE,EAAE,CAAK;;;KAG3B;EACH;EAEQ9F,cAAcA,CAACoD,KAAa,EAAEpC,IAAW,EAAEsE,SAAA,GAAoB,SAAS;IAC9E;IACA,IAAIS,MAAM,GAAa,EAAE;IAEzB,IAAIT,SAAS,KAAK,cAAc,EAAE;MAChCS,MAAM,GAAG,IAAI,CAACpP,mBAAmB,CAACM,YAAY,CAAC,CAAC;IAClD,CAAC,MAAM,IAAIqO,SAAS,KAAK,aAAa,EAAE;MACtCS,MAAM,GAAG,IAAI,CAACpP,mBAAmB,CAACO,YAAY,CAAC,CAAC;IAClD,CAAC,MAAM,IAAIoO,SAAS,KAAK,aAAa,EAAE;MACtCS,MAAM,GAAG,IAAI,CAACpP,mBAAmB,CAACS,WAAW,CAAC,CAAC;IACjD,CAAC,MAAM,IAAIkO,SAAS,KAAK,SAAS,EAAE;MAClCS,MAAM,GAAG,IAAI,CAACpP,mBAAmB,CAACY,aAAa,CAAC,CAAC;IACnD,CAAC,MAAM;MACLwO,MAAM,GAAG,CAAC,IAAI,CAAC3P,YAAY,CAACC,OAAO,EAAE,IAAI,CAACD,YAAY,CAACE,SAAS,CAAC,CAAC,CAAC;IACrE;IAEA,MAAMkN,YAAY,GAAG,IAAI,CAACC,oBAAoB,EAAE;IAEhD,OAAO;MACL,GAAGD,YAAY;MACfE,KAAK,EAAE;QACL,GAAGF,YAAY,CAACE,KAAK;QACrBC,IAAI,EAAE,KAAK;QACX7L,MAAM,EAAE,IAAI,CAACH,cAAc,CAACG;OAC7B;MACD8I,MAAM,EAAE,CAAC;QACP+D,IAAI,EAAE,YAAY;QAClBhB,IAAI,EAAE,KAAK;QACX3C,IAAI,EAAEA,IAAI;QACVgF,SAAS,EAAE,KAAK;QAChBD,MAAM,EAAEA,MAAM;QACdV,UAAU,EAAE;UACVvB,OAAO,EAAE;;OAEZ,CAAC;MACFmC,MAAM,EAAE;QACNnC,OAAO,EAAE,IAAI;QACboC,KAAK,EAAE,OAAO;QACdC,aAAa,EAAE,QAAQ;QACvBC,MAAM,EAAE,UAAU;QAClBC,SAAS,EAAE;UACTjC,KAAK,EAAE,SAAS;UAChBC,QAAQ,EAAE,MAAM;UAChBxM,UAAU,EAAE,mBAAmB;UAC/B6N,UAAU,EAAE;SACb;QACDY,YAAY,EAAE,CAAC;QACfC,gBAAgB,EAAE;OACnB;MACDpB,WAAW,EAAE;QACXqB,GAAG,EAAE;UACHC,gBAAgB,EAAE,KAAK;UACvBC,MAAM,EAAE,SAAS;UACjBrB,UAAU,EAAE;YACVvB,OAAO,EAAE;WACV;UACD6C,YAAY,EAAE,IAAI;UAClBpB,WAAW,EAAE;;;KAGlB;EACH;EAEQjH,0BAA0BA,CAAC0C,IAAW;IAC5C,MAAMvC,UAAU,GAAGuC,IAAI,CAACtC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACgE,aAAa,IAAIhE,IAAI,CAACiE,KAAK,IAAIjE,IAAI,CAACiI,QAAQ,IAAI,cAAc,CAAC;IACxG,MAAM3L,MAAM,GAAG+F,IAAI,CAACtC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACc,YAAY,IAAId,IAAI,CAACmD,UAAU,IAAInD,IAAI,CAACC,KAAK,IAAI,CAAC,CAAC;IAExF;IACA,MAAMiI,WAAW,GAAG,IAAI,CAAClP,cAAc,CAACG,MAAM;IAC9C,MAAMgP,aAAa,GAAGrI,UAAU,CAACP,MAAM,GAAG,CAAC,CAAC,CAAC;IAE7C,OAAO;MACLwF,KAAK,EAAE;QACLC,IAAI,EAAE,KAAK;QACX/L,eAAe,EAAE,IAAI,CAACD,cAAc,CAACC,eAAe;QACpDE,MAAM,EAAE+O,WAAW;QACnBE,UAAU,EAAE,GAAG;QAAE;QACjBC,WAAW,EAAE,EAAE;QAAE;QACjBC,kBAAkB,EAAEH,aAAa,GAAG;UAClCI,SAAS,EAAEzI,UAAU,CAACP,MAAM,GAAG,EAAE,GAAG,EAAE;UAAE;UACxCiJ,eAAe,EAAE;SAClB,GAAGC,SAAS;QACbjD,KAAK,EAAE;UACLtM,UAAU,EAAE,IAAI,CAACF,cAAc,CAACE;;OAE5B;MACRuL,KAAK,EAAE;QACLqB,IAAI,EAAE,EAAE;QACRN,KAAK,EAAE;UACLkD,OAAO,EAAE;;OAEZ;MACDrD,KAAK,EAAE;QACLvF,UAAU,EAAEA,UAAU;QACtBwF,MAAM,EAAE;UACNE,KAAK,EAAE;YACLC,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE,MAAM;YAChBqB,UAAU,EAAE,KAAK;YACjB7N,UAAU,EAAE;WACb;UACDyP,QAAQ,EAAE,OAAO;UACjBC,IAAI,EAAE,CAAC;UACPC,OAAO,EAAE,IAAI;UACbC,SAAS,EAAE,SAAAA,CAAA;YACT;YACA,MAAM7E,KAAK,GAAG,IAAI,CAAC9G,KAAe;YAClC,OAAO,gHAAgH8G,KAAK,QAAQ;UACtI;SACD;QACD0B,SAAS,EAAE,aAAa;QACxBC,SAAS,EAAE,aAAa;QACxBmD,GAAG,EAAE,CAAC;QACNC,GAAG,EAAEb,aAAa,GAAG5D,IAAI,CAACwE,GAAG,CAAC,EAAE,EAAEjJ,UAAU,CAACP,MAAM,GAAG,CAAC,CAAC,GAAGkJ,SAAS,CAAC;OACtE;MACD5C,KAAK,EAAE;QACLpB,KAAK,EAAE;UACLqB,IAAI,EAAE,EAAE;UACRN,KAAK,EAAE;YACLC,KAAK,EAAE;;SAEV;QACDH,MAAM,EAAE;UACNE,KAAK,EAAE;YACLC,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE,MAAM;YAChBxM,UAAU,EAAE;;SAEf;QACD6M,aAAa,EAAE,aAAa;QAC5BJ,SAAS,EAAE;OACZ;MACD1D,MAAM,EAAE,CAAC;QACP+D,IAAI,EAAE,OAAO;QACbhB,IAAI,EAAE,KAAK;QACX3C,IAAI,EAAE/F,MAAM;QACZmJ,KAAK,EAAE,SAAS;QAChBmB,WAAW,EAAE,CAAC;QACdC,YAAY,EAAE;OACf,CAAC;MACFS,MAAM,EAAE;QACNnC,OAAO,EAAE;OACV;MACDqB,WAAW,EAAE;QACXyC,GAAG,EAAE;UACHvC,UAAU,EAAE;YACVvB,OAAO,EAAE,IAAI;YACb+D,MAAM,EAAE,KAAK;YACb3B,KAAK,EAAE,OAAO;YACd4B,CAAC,EAAE,CAAC;YAAE;YACN3D,KAAK,EAAE;cACLC,KAAK,EAAE,SAAS;cAChBC,QAAQ,EAAE,MAAM;cAChBqB,UAAU,EAAE,KAAK;cACjB7N,UAAU,EAAE,oBAAoB;cAChC8N,WAAW,EAAE;aACd;YACD8B,SAAS,EAAE,SAAAA,CAAA;cACT,OAAO,IAAI,CAACM,CAAC;YACf;WACD;UACD3D,KAAK,EAAE,SAAS;UAChBoB,YAAY,EAAE,CAAC;UACfI,YAAY,EAAE,IAAI;UAAE;UACpBC,YAAY,EAAE,IAAI;UAAE;UACpBC,aAAa,EAAE,EAAE,CAAG;;OAEvB;MACDkC,OAAO,EAAE;QACPlE,OAAO,EAAE,IAAI;QACb2D,SAAS,EAAE,SAAAA,CAAA;UACT,OAAO,KAAK,GAAG,IAAI,CAACK,CAAC,GAAG,qBAAqB,GAAG,IAAI,CAACC,CAAC;QACxD;OACD;MACDE,UAAU,EAAE;QACVC,aAAa,EAAE;UACbpE,OAAO,EAAE,IAAI;UACbqE,KAAK,EAAE;YACLC,MAAM,EAAE,SAAS;YACjBC,CAAC,EAAE,CAAC;YACJC,MAAM,EAAE;cACNC,KAAK,EAAE;gBACLC,IAAI,EAAE,SAAS;gBACfJ,MAAM,EAAE;eACT;cACDK,MAAM,EAAE;gBACND,IAAI,EAAE,SAAS;gBACfJ,MAAM,EAAE;;;;;OAKjB;MACDM,SAAS,EAAE;QACT5E,OAAO,EAAE;OACV;MACD6E,OAAO,EAAE;QACP7E,OAAO,EAAE;;KAEZ;EACH;EAEQvF,iBAAiBA,CAAC6E,KAAa;IACrC,OAAO;MACLM,KAAK,EAAE;QACL9L,eAAe,EAAE;OAClB;MACDwL,KAAK,EAAE;QACLqB,IAAI,EAAErB,KAAK;QACX8C,KAAK,EAAE,MAAM;QACb/B,KAAK,EAAE;UACLC,KAAK,EAAE,mBAAmB;UAC1BC,QAAQ,EAAE,MAAM;UAChBqB,UAAU,EAAE;;OAEf;MACD9E,MAAM,EAAE,EAAE;MACVgI,IAAI,EAAE;QACJC,MAAM,EAAE;OACT;MACDA,MAAM,EAAE;QACN1E,KAAK,EAAE;UACLuB,UAAU,EAAE,MAAM;UAClBrB,QAAQ,EAAE,MAAM;UAChBD,KAAK,EAAE;;OAEV;MACDsE,SAAS,EAAE;QACT5E,OAAO,EAAE;OACV;MACD6E,OAAO,EAAE;QACP7E,OAAO,EAAE;;KAEZ;EACH;EAEA;EACQgF,uBAAuBA,CAC7B9H,IAA4B,EAC5B+H,YAA+C,EAC/C3F,KAAa;IAEb,IAAIpC,IAAI,IAAIA,IAAI,CAAC9C,MAAM,GAAG,CAAC,EAAE;MAC3B,OAAO6K,YAAY,CAAC/H,IAAI,CAAC;IAC3B,CAAC,MAAM;MACL,OAAO,IAAI,CAACzC,iBAAiB,CAAC6E,KAAK,CAAC;IACtC;EACF;EAEA;EACQK,oBAAoBA,CAAA;IAC1B,OAAO;MACLC,KAAK,EAAE;QACL9L,eAAe,EAAE;OAClB;MACDwL,KAAK,EAAE;QACLqB,IAAI,EAAE,EAAE;QACRN,KAAK,EAAE;UACLkD,OAAO,EAAE;;OAEZ;MACDqB,SAAS,EAAE;QACT5E,OAAO,EAAE;OACV;MACD6E,OAAO,EAAE;QACP7E,OAAO,EAAE;OACV;MACDmC,MAAM,EAAE;QACNnC,OAAO,EAAE;;KAEZ;EACH;EAEA;EACAkF,aAAa,GAAGA,CAACtF,KAAuB,EAAEuF,QAAgB,KAAU;IAClE,IAAI,CAAChQ,SAAS,CAACgQ,QAAQ,CAAC,GAAGvF,KAAK;EAClC,CAAC;EAED;EACAwF,aAAaA,CAACC,aAAqB;IACjC,MAAMrM,kBAAkB,GAAG,IAAI,CAACD,2BAA2B,EAAE;IAC7D,IAAI,CAAC5G,gBAAgB,CAACmT,kBAAkB,CAACtM,kBAAkB,EAAEqM,aAAa,EAAE,OAAO,CAAC;EACtF;EAEAE,kBAAkBA,CAACF,aAAqB;IACtC,MAAMrM,kBAAkB,GAAG,IAAI,CAACD,2BAA2B,EAAE;IAC7D,IAAI,CAAC5G,gBAAgB,CAACqT,sBAAsB,CAACxM,kBAAkB,EAAEqM,aAAa,EAAE,OAAO,CAAC;EAC1F;EAEAI,YAAYA,CAAA;IACV,MAAMzM,kBAAkB,GAAG,IAAI,CAACD,2BAA2B,EAAE;IAC7D,IAAI,CAAC5G,gBAAgB,CAACsT,YAAY,CAACzM,kBAAkB,CAAC;EACxD;EAEA;EACA0M,yBAAyBA,CAACC,KAAU;IAClC,IAAI,CAAC7P,gBAAgB,GAAG6P,KAAK,CAAC3N,KAAK;IACnC,IAAI,CAAC9B,gBAAgB,GAAG,IAAI,CAACJ,gBAAgB,CAACsE,MAAM,GAAG,CAAC;EAC1D;EAEAwL,UAAUA,CAAA;IACR;IACA,IAAI,CAAC3N,WAAW,EAAE;EACpB;EAEA4N,qBAAqBA,CAACF,KAAY;IAChCA,KAAK,CAACG,eAAe,EAAE;IACvB,IAAI,CAAC3P,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;EACtD;EAEA4P,mBAAmBA,CAAA;IACjB;IACA5N,OAAO,CAACqE,GAAG,CAAC,8BAA8B,CAAC;IAC3C,IAAI,CAACrG,mBAAmB,GAAG,KAAK;EAClC;EAEA;EACQqI,wBAAwBA,CAACc,KAAa,EAAE3E,UAAoB,EAAEuC,IAAc,EAAEqC,UAAkB,EAAEiC,SAAA,GAAoB,SAAS;IACrI;IACA,IAAIlB,KAAK,GAAG,IAAI,CAACzN,mBAAmB,CAACa,SAAS,CAAC,CAAC;IAEhD,IAAI8N,SAAS,KAAK,WAAW,EAAE;MAC7BlB,KAAK,GAAG,IAAI,CAACzN,mBAAmB,CAACa,SAAS,CAAC,CAAC;IAC9C,CAAC,MAAM,IAAI8N,SAAS,KAAK,iBAAiB,EAAE;MAC1ClB,KAAK,GAAG,IAAI,CAACzN,mBAAmB,CAACe,4BAA4B,CAAC,CAAC;IACjE;IAEA;IACA,MAAMmP,WAAW,GAAG,IAAI,CAAClP,cAAc,CAACG,MAAM;IAC9C,MAAMgP,aAAa,GAAGrI,UAAU,CAACP,MAAM,GAAG,CAAC,CAAC,CAAC;IAE7C,MAAMsF,YAAY,GAAG,IAAI,CAACC,oBAAoB,EAAE;IAEhD,OAAO;MACL,GAAGD,YAAY;MACfE,KAAK,EAAE;QACL,GAAGF,YAAY,CAACE,KAAK;QACrBC,IAAI,EAAE,KAAK;QACX7L,MAAM,EAAE+O,WAAW;QACnBE,UAAU,EAAE,GAAG;QAAE;QACjBC,WAAW,EAAE,EAAE;QAAE;QACjBC,kBAAkB,EAAEH,aAAa,GAAG;UAClCI,SAAS,EAAEzI,UAAU,CAACP,MAAM,GAAG,EAAE,GAAG,EAAE;UAAE;UACxCiJ,eAAe,EAAE;SAClB,GAAGC,SAAS;QACbxD,QAAQ,EAAE,GAAG;QACbC,OAAO,EAAE;UACPC,OAAO,EAAE,IAAI;UACbH,IAAI,EAAE;SACP;QACDI,MAAM,EAAE;OACF;MACRC,KAAK,EAAE;QACLvF,UAAU,EAAEA,UAAU;QACtB2E,KAAK,EAAE;UACLqB,IAAI,EAAE;SACP;QACDR,MAAM,EAAE;UACNE,KAAK,EAAE;YACLC,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE,MAAM;YAChBqB,UAAU,EAAE,KAAK;YACjB7N,UAAU,EAAE;WACb;UACDyP,QAAQ,EAAE,OAAO;UACjBC,IAAI,EAAE,CAAC;UACPC,OAAO,EAAE,IAAI;UACbC,SAAS,EAAE,SAAAA,CAAA;YACT;YACA,MAAM9C,IAAI,GAAG,IAAI,CAAC7I,KAAe;YACjC,OAAO,gHAAgH6I,IAAI,QAAQ;UACrI;SACD;QACDL,SAAS,EAAE,aAAa;QACxBC,SAAS,EAAE,aAAa;QACxBmD,GAAG,EAAE,CAAC;QACNC,GAAG,EAAEb,aAAa,GAAG5D,IAAI,CAACwE,GAAG,CAAC,EAAE,EAAEjJ,UAAU,CAACP,MAAM,GAAG,CAAC,CAAC,GAAGkJ,SAAS,CAAC;OACtE;MACD5C,KAAK,EAAE;QACLkD,GAAG,EAAE,CAAC;QACNtE,KAAK,EAAE;UACLqB,IAAI,EAAE,EAAE;UACRN,KAAK,EAAE;YACLC,KAAK,EAAE;;SAEV;QACDH,MAAM,EAAE;UACNE,KAAK,EAAE;YACLC,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE,MAAM;YAChBxM,UAAU,EAAE;;SAEf;QACD6M,aAAa,EAAE,aAAa;QAC5BJ,SAAS,EAAE;OACZ;MACDa,WAAW,EAAE;QACXyC,GAAG,EAAE;UACHvC,UAAU,EAAE;YACVvB,OAAO,EAAE,IAAI;YACb+D,MAAM,EAAE,KAAK;YACb3B,KAAK,EAAE,OAAO;YACd4B,CAAC,EAAE,CAAC;YAAE;YACN3D,KAAK,EAAE;cACLC,KAAK,EAAE,SAAS;cAChBC,QAAQ,EAAE,MAAM;cAChBqB,UAAU,EAAE,KAAK;cACjB7N,UAAU,EAAE,oBAAoB;cAChC8N,WAAW,EAAE;aACd;YACD8B,SAAS,EAAE,SAAAA,CAAA;cACT,OAAO,IAAI,CAACM,CAAC;YACf;WACD;UACD3D,KAAK,EAAEA,KAAK;UACZoB,YAAY,EAAE,CAAC;UACfI,YAAY,EAAE,IAAI;UAAE;UACpBC,YAAY,EAAE,IAAI;UAAE;UACpBC,aAAa,EAAE,EAAE;UAAI;UACrBP,WAAW,EAAE;;OAEhB;MACD3E,MAAM,EAAE,CAAC;QACP+C,IAAI,EAAE,KAAK;QACXgB,IAAI,EAAEtB,UAAU;QAChBrC,IAAI,EAAEA,IAAI;QACVoD,KAAK,EAAEA;OACR,CAAQ;MACT6B,MAAM,EAAE;QACNnC,OAAO,EAAE;OACV;MACDkE,OAAO,EAAE;QACPlE,OAAO,EAAE,IAAI;QACb2D,SAAS,EAAE,SAAAA,CAAA;UACT,OAAO,KAAK,GAAG,IAAI,CAACK,CAAC,GAAG,WAAW,GAAGzE,UAAU,GAAG,IAAI,GAAG,IAAI,CAAC0E,CAAC;QAClE;;KAEH;EACH;EAEA;EACQ+B,kBAAkBA,CAACnG,IAAuC,EAAE7L,MAAA,GAAiB,IAAI,CAACH,cAAc,CAACG,MAAM;IAC7G,OAAO;MACL4L,KAAK,EAAE;QACLC,IAAI;QACJ/L,eAAe,EAAE,IAAI,CAACD,cAAc,CAACC,eAAe;QACpDE,MAAM;QACNqM,KAAK,EAAE;UACLtM,UAAU,EAAE,IAAI,CAACF,cAAc,CAACE;;OAEnC;MACDuL,KAAK,EAAE;QACLqB,IAAI,EAAE,EAAE;QACRN,KAAK,EAAE;UAAEkD,OAAO,EAAE;QAAM;OACzB;MACDsB,OAAO,EAAE;QAAE7E,OAAO,EAAE;MAAK,CAAE;MAC3BmC,MAAM,EAAE;QAAEnC,OAAO,EAAE;MAAK;KACzB;EACH;EAEA;EACAiG,WAAWA,CAACjO,KAAa,EAAEkO,WAAA,GAAuB,KAAK;IACrD,IAAIA,WAAW,EAAE;MACf,MAAMC,QAAQ,GAAG/G,IAAI,CAACyE,GAAG,CAAC,GAAG,IAAI,CAACnQ,SAAS,CAACkH,GAAG,CAAC0D,IAAI,IAAIA,IAAI,CAACN,UAAU,CAAC,CAAC;MACzE,MAAMoI,eAAe,GAAGD,QAAQ,GAAG,CAAC,GAAInO,KAAK,GAAGmO,QAAQ,GAAI,GAAG,GAAG,CAAC;MACnE;MACA,OAAO/G,IAAI,CAACyE,GAAG,CAACuC,eAAe,EAAE,EAAE,CAAC;IACtC,CAAC,MAAM;MACL;MACA,IAAID,QAAQ,GAAG,CAAC;MAEhB,IAAI,IAAI,CAAC5R,SAAS,KAAK,SAAS,IAAI,IAAI,CAACiB,eAAe,IAAI,IAAI,CAACA,eAAe,CAAC4E,MAAM,GAAG,CAAC,EAAE;QAC3F;QACA+L,QAAQ,GAAG/G,IAAI,CAACyE,GAAG,CAAC,GAAG,IAAI,CAACrO,eAAe,CAACoF,GAAG,CAACgE,IAAI,IAAIA,IAAI,CAACjD,YAAY,IAAI,CAAC,CAAC,CAAC;MAClF,CAAC,MAAM,IAAI,IAAI,CAACpH,SAAS,KAAK,QAAQ,IAAI,IAAI,CAACzB,eAAe,IAAI,IAAI,CAACA,eAAe,CAACsH,MAAM,GAAG,CAAC,EAAE;QACjG;QACA+L,QAAQ,GAAG/G,IAAI,CAACyE,GAAG,CAAC,GAAG,IAAI,CAAC/Q,eAAe,CAAC8H,GAAG,CAACgE,IAAI,IAAIA,IAAI,CAACG,gBAAgB,IAAIH,IAAI,CAACjD,YAAY,IAAI,CAAC,CAAC,CAAC;MAC3G;MAEA,MAAMyK,eAAe,GAAGD,QAAQ,GAAG,CAAC,GAAInO,KAAK,GAAGmO,QAAQ,GAAI,GAAG,GAAG,CAAC;MACnE;MACA,OAAO/G,IAAI,CAACyE,GAAG,CAACuC,eAAe,EAAE,EAAE,CAAC;IACtC;EACF;EAIA;EACOC,YAAYA,CAAA;IACjBC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACnQ,WAAW,EAAE,QAAQ,CAAC;EACzC;EAEOoQ,WAAWA,CAAA;IAChBF,MAAM,CAACC,IAAI,CAAC,IAAI,CAACjQ,eAAe,EAAE,QAAQ,CAAC;EAC7C;EAEOmQ,gBAAgBA,CAAA;IACrBH,MAAM,CAACC,IAAI,CAAC,sCAAsC,EAAE,QAAQ,CAAC;EAC/D;EAEOG,eAAeA,CAACf,KAAU;IAC/B,IAAIA,KAAK,IAAIA,KAAK,CAACgB,SAAS,IAAIhB,KAAK,CAACiB,OAAO,EAAE;MAC7C;MACA,MAAMD,SAAS,GAAG,IAAItO,IAAI,CAACsN,KAAK,CAACgB,SAAS,CAAC;MAC3C,MAAMC,OAAO,GAAG,IAAIvO,IAAI,CAACsN,KAAK,CAACiB,OAAO,CAAC;MAEvC,IAAI,CAAC1S,SAAS,CAACC,QAAQ,GAAGwS,SAAS,CAACnO,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC/D,IAAI,CAACvE,SAAS,CAACG,MAAM,GAAGuS,OAAO,CAACpO,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAE3D;MACA,IAAI,CAACxE,IAAI,CAAC4S,UAAU,CAAC;QACnB1S,QAAQ,EAAE,IAAI,CAACD,SAAS,CAACC,QAAQ;QACjCE,MAAM,EAAE,IAAI,CAACH,SAAS,CAACG;OACxB,CAAC;MAEF;MACA8D,OAAO,CAACqE,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACtI,SAAS,CAAC;IACrD;EACF;EAEA4S,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAAC5S,SAAS,CAACC,QAAQ,IAAI,CAAC,IAAI,CAACD,SAAS,CAACG,MAAM,EAAE;MACtD,OAAO,mBAAmB;IAC5B;IAEA,MAAMF,QAAQ,GAAG,IAAIkE,IAAI,CAAC,IAAI,CAACnE,SAAS,CAACC,QAAQ,CAAC;IAClD,MAAME,MAAM,GAAG,IAAIgE,IAAI,CAAC,IAAI,CAACnE,SAAS,CAACG,MAAM,CAAC;IAE9C,MAAM0S,OAAO,GAA+B;MAC1CnO,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE;KACN;IAED,OAAO,GAAG3E,QAAQ,CAAC6S,kBAAkB,CAAC,OAAO,EAAED,OAAO,CAAC,OAAO1S,MAAM,CAAC2S,kBAAkB,CAAC,OAAO,EAAED,OAAO,CAAC,EAAE;EAC7G;EAEAE,oBAAoBA,CAAA;IAClB,IAAI,CAAC,IAAI,CAAC/S,SAAS,CAACC,QAAQ,IAAI,CAAC,IAAI,CAACD,SAAS,CAACG,MAAM,EAAE;MACtD,OAAO;QAAE6S,KAAK,EAAE,IAAI;QAAEC,GAAG,EAAE;MAAI,CAAE;IACnC;IAEA,OAAO;MACLD,KAAK,EAAE,IAAI7O,IAAI,CAAC,IAAI,CAACnE,SAAS,CAACC,QAAQ,CAAC;MACxCgT,GAAG,EAAE,IAAI9O,IAAI,CAAC,IAAI,CAACnE,SAAS,CAACG,MAAM;KACpC;EACH;EAGA+S,eAAeA,CAACzB,KAAY;IAC1B,MAAM0B,MAAM,GAAG1B,KAAK,CAAC0B,MAAqB;IAC1C,MAAMC,cAAc,GAAGD,MAAM,CAACE,OAAO,CAAC,kBAAkB,CAAC;IACzD,MAAMC,gBAAgB,GAAGH,MAAM,CAACE,OAAO,CAAC,oBAAoB,CAAC;IAE7D,IAAI,CAACD,cAAc,IAAI,CAACE,gBAAgB,EAAE;MACxC,IAAI,CAACrR,mBAAmB,GAAG,KAAK;IAClC;EACF;EAEOsR,gBAAgBA,CAAC9B,KAAU;IAChCA,KAAK,CAACG,eAAe,EAAE;IACvB,IAAI,CAAC3P,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;IACpDgC,OAAO,CAACqE,GAAG,CAAC,+CAA+C,EAAE,IAAI,CAACrG,mBAAmB,CAAC;EACxF;EAEOuR,aAAaA,CAAA;IAClB,IAAI,CAAC3B,mBAAmB,EAAE;IAC1B,IAAI,CAAC5P,mBAAmB,GAAG,KAAK;EAClC;EAEOwR,cAAcA,CAAA;IACnB,IAAI,IAAI,CAACpT,SAAS,KAAK,SAAS,EAAE;MAChC,IAAI,CAACkR,YAAY,EAAE;IACrB,CAAC,MAAM;MACL,IAAI,CAACF,kBAAkB,CAAC,gBAAgB,CAAC;IAC3C;IACA,IAAI,CAACpP,mBAAmB,GAAG,KAAK;EAClC;CAED;AA9BCyR,UAAA,EADC/W,YAAY,CAAC,gBAAgB,EAAE,CAAC,QAAQ,CAAC,CAAC,C,wDAS1C;AAluCUqB,kBAAkB,GAAA0V,UAAA,EApB9BhX,SAAS,CAAC;EACTiX,QAAQ,EAAE,eAAe;EACzBC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPjX,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBK,qBAAqB,EACrBI,eAAe,EACfC,kBAAkB,EAClBC,eAAe,EACfC,cAAc,EACdC,eAAe,EACfC,iBAAiB,EACjBC,iBAAiB,EACjBC,aAAa,CACd;EACDgW,WAAW,EAAE,4BAA4B;EACzCC,SAAS,EAAE,CAAC,4BAA4B;CACzC,CAAC,C,EACW/V,kBAAkB,CAwvC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}