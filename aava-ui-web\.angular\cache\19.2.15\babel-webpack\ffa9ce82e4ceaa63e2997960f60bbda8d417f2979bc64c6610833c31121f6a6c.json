{"ast": null, "code": "import cross from \"./cross.js\";\nfunction lexicographicOrder(a, b) {\n  return a[0] - b[0] || a[1] - b[1];\n}\n\n// Computes the upper convex hull per the monotone chain algorithm.\n// Assumes points.length >= 3, is sorted by x, unique in y.\n// Returns an array of indices into points in left-to-right order.\nfunction computeUpperHullIndexes(points) {\n  const n = points.length,\n    indexes = [0, 1];\n  let size = 2,\n    i;\n  for (i = 2; i < n; ++i) {\n    while (size > 1 && cross(points[indexes[size - 2]], points[indexes[size - 1]], points[i]) <= 0) --size;\n    indexes[size++] = i;\n  }\n  return indexes.slice(0, size); // remove popped points\n}\nexport default function (points) {\n  if ((n = points.length) < 3) return null;\n  var i,\n    n,\n    sortedPoints = new Array(n),\n    flippedPoints = new Array(n);\n  for (i = 0; i < n; ++i) sortedPoints[i] = [+points[i][0], +points[i][1], i];\n  sortedPoints.sort(lexicographicOrder);\n  for (i = 0; i < n; ++i) flippedPoints[i] = [sortedPoints[i][0], -sortedPoints[i][1]];\n  var upperIndexes = computeUpperHullIndexes(sortedPoints),\n    lowerIndexes = computeUpperHullIndexes(flippedPoints);\n\n  // Construct the hull polygon, removing possible duplicate endpoints.\n  var skipLeft = lowerIndexes[0] === upperIndexes[0],\n    skipRight = lowerIndexes[lowerIndexes.length - 1] === upperIndexes[upperIndexes.length - 1],\n    hull = [];\n\n  // Add upper hull in right-to-l order.\n  // Then add lower hull in left-to-right order.\n  for (i = upperIndexes.length - 1; i >= 0; --i) hull.push(points[sortedPoints[upperIndexes[i]][2]]);\n  for (i = +skipLeft; i < lowerIndexes.length - skipRight; ++i) hull.push(points[sortedPoints[lowerIndexes[i]][2]]);\n  return hull;\n}", "map": {"version": 3, "names": ["cross", "lexicographicOrder", "a", "b", "computeUpperHullIndexes", "points", "n", "length", "indexes", "size", "i", "slice", "sortedPoints", "Array", "flippedPoints", "sort", "upperIndexes", "lowerIndexes", "skip<PERSON><PERSON><PERSON>", "skipRight", "hull", "push"], "sources": ["C:/console/aava-ui-web/node_modules/d3-polygon/src/hull.js"], "sourcesContent": ["import cross from \"./cross.js\";\n\nfunction lexicographicOrder(a, b) {\n  return a[0] - b[0] || a[1] - b[1];\n}\n\n// Computes the upper convex hull per the monotone chain algorithm.\n// Assumes points.length >= 3, is sorted by x, unique in y.\n// Returns an array of indices into points in left-to-right order.\nfunction computeUpperHullIndexes(points) {\n  const n = points.length,\n      indexes = [0, 1];\n  let size = 2, i;\n\n  for (i = 2; i < n; ++i) {\n    while (size > 1 && cross(points[indexes[size - 2]], points[indexes[size - 1]], points[i]) <= 0) --size;\n    indexes[size++] = i;\n  }\n\n  return indexes.slice(0, size); // remove popped points\n}\n\nexport default function(points) {\n  if ((n = points.length) < 3) return null;\n\n  var i,\n      n,\n      sortedPoints = new Array(n),\n      flippedPoints = new Array(n);\n\n  for (i = 0; i < n; ++i) sortedPoints[i] = [+points[i][0], +points[i][1], i];\n  sortedPoints.sort(lexicographicOrder);\n  for (i = 0; i < n; ++i) flippedPoints[i] = [sortedPoints[i][0], -sortedPoints[i][1]];\n\n  var upperIndexes = computeUpperHullIndexes(sortedPoints),\n      lowerIndexes = computeUpperHullIndexes(flippedPoints);\n\n  // Construct the hull polygon, removing possible duplicate endpoints.\n  var skipLeft = lowerIndexes[0] === upperIndexes[0],\n      skipRight = lowerIndexes[lowerIndexes.length - 1] === upperIndexes[upperIndexes.length - 1],\n      hull = [];\n\n  // Add upper hull in right-to-l order.\n  // Then add lower hull in left-to-right order.\n  for (i = upperIndexes.length - 1; i >= 0; --i) hull.push(points[sortedPoints[upperIndexes[i]][2]]);\n  for (i = +skipLeft; i < lowerIndexes.length - skipRight; ++i) hull.push(points[sortedPoints[lowerIndexes[i]][2]]);\n\n  return hull;\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,YAAY;AAE9B,SAASC,kBAAkBA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAChC,OAAOD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,IAAID,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC;AACnC;;AAEA;AACA;AACA;AACA,SAASC,uBAAuBA,CAACC,MAAM,EAAE;EACvC,MAAMC,CAAC,GAAGD,MAAM,CAACE,MAAM;IACnBC,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;EACpB,IAAIC,IAAI,GAAG,CAAC;IAAEC,CAAC;EAEf,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,CAAC,EAAE,EAAEI,CAAC,EAAE;IACtB,OAAOD,IAAI,GAAG,CAAC,IAAIT,KAAK,CAACK,MAAM,CAACG,OAAO,CAACC,IAAI,GAAG,CAAC,CAAC,CAAC,EAAEJ,MAAM,CAACG,OAAO,CAACC,IAAI,GAAG,CAAC,CAAC,CAAC,EAAEJ,MAAM,CAACK,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,EAAED,IAAI;IACtGD,OAAO,CAACC,IAAI,EAAE,CAAC,GAAGC,CAAC;EACrB;EAEA,OAAOF,OAAO,CAACG,KAAK,CAAC,CAAC,EAAEF,IAAI,CAAC,CAAC,CAAC;AACjC;AAEA,eAAe,UAASJ,MAAM,EAAE;EAC9B,IAAI,CAACC,CAAC,GAAGD,MAAM,CAACE,MAAM,IAAI,CAAC,EAAE,OAAO,IAAI;EAExC,IAAIG,CAAC;IACDJ,CAAC;IACDM,YAAY,GAAG,IAAIC,KAAK,CAACP,CAAC,CAAC;IAC3BQ,aAAa,GAAG,IAAID,KAAK,CAACP,CAAC,CAAC;EAEhC,KAAKI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,CAAC,EAAE,EAAEI,CAAC,EAAEE,YAAY,CAACF,CAAC,CAAC,GAAG,CAAC,CAACL,MAAM,CAACK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAACL,MAAM,CAACK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC;EAC3EE,YAAY,CAACG,IAAI,CAACd,kBAAkB,CAAC;EACrC,KAAKS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,CAAC,EAAE,EAAEI,CAAC,EAAEI,aAAa,CAACJ,CAAC,CAAC,GAAG,CAACE,YAAY,CAACF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAACE,YAAY,CAACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAEpF,IAAIM,YAAY,GAAGZ,uBAAuB,CAACQ,YAAY,CAAC;IACpDK,YAAY,GAAGb,uBAAuB,CAACU,aAAa,CAAC;;EAEzD;EACA,IAAII,QAAQ,GAAGD,YAAY,CAAC,CAAC,CAAC,KAAKD,YAAY,CAAC,CAAC,CAAC;IAC9CG,SAAS,GAAGF,YAAY,CAACA,YAAY,CAACV,MAAM,GAAG,CAAC,CAAC,KAAKS,YAAY,CAACA,YAAY,CAACT,MAAM,GAAG,CAAC,CAAC;IAC3Fa,IAAI,GAAG,EAAE;;EAEb;EACA;EACA,KAAKV,CAAC,GAAGM,YAAY,CAACT,MAAM,GAAG,CAAC,EAAEG,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAEU,IAAI,CAACC,IAAI,CAAChB,MAAM,CAACO,YAAY,CAACI,YAAY,CAACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClG,KAAKA,CAAC,GAAG,CAACQ,QAAQ,EAAER,CAAC,GAAGO,YAAY,CAACV,MAAM,GAAGY,SAAS,EAAE,EAAET,CAAC,EAAEU,IAAI,CAACC,IAAI,CAAChB,MAAM,CAACO,YAAY,CAACK,YAAY,CAACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAEjH,OAAOU,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}