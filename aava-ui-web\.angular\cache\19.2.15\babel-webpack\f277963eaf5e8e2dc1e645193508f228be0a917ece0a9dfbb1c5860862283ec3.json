{"ast": null, "code": "import { MarketplaceAuthGuard } from './shared/guards/marketplace-auth.guard';\nimport { LoginComponent } from '@shared/auth/components/login/login.component';\nimport { CallbackComponent } from '@shared/auth/components/callback/callback.component';\nimport { LaunchpadHomeComponent } from './pages/launchpad-home/launchpad-home.component';\nimport { AgentsFilterComponent } from './pages/agents-filter/agents-filter.component';\nimport { MyAgentHomeComponent } from './shared/components/my-agent-home/my-agent-home.component';\nimport { MarketplaceComponent } from './pages/marketplace/marketplace.component';\nimport { AnalyticsComponent } from './shared/components/analytics/analytics.component';\nimport { DocumentsComponent } from './shared/components/documents/documents.component';\nexport const routes = [{\n  path: 'marketplace',\n  component: MarketplaceComponent\n}, {\n  path: 'login',\n  component: LoginComponent\n}, {\n  path: 'callback',\n  component: CallbackComponent\n}, {\n  path: 'dashboard',\n  canActivate: [MarketplaceAuthGuard],\n  component: LaunchpadHomeComponent\n}, {\n  path: 'agent-list',\n  canActivate: [MarketplaceAuthGuard],\n  component: AgentsFilterComponent\n}, {\n  path: 'my-agent-home',\n  canActivate: [MarketplaceAuthGuard],\n  component: MyAgentHomeComponent\n}, {\n  path: 'analytics',\n  canActivate: [MarketplaceAuthGuard],\n  component: AnalyticsComponent\n}, {\n  path: 'documents',\n  canActivate: [MarketplaceAuthGuard],\n  component: DocumentsComponent\n}, {\n  path: 'build',\n  canActivate: [MarketplaceAuthGuard],\n  children: [{\n    path: 'agents',\n    loadComponent: () => import('@shared/pages/agents/agents.component').then(m => m.AgentsComponent)\n  }, {\n    path: 'agents/:type',\n    loadComponent: () => import('@shared/pages/agents/build-agents/build-agents.component').then(m => m.BuildAgentsComponent)\n  }, {\n    path: 'agents/:type/execute',\n    loadComponent: () => import('@shared/pages/agents/agent-execution/agent-execution.component').then(m => m.AgentExecutionComponent)\n  }, {\n    path: 'workflows',\n    loadComponent: () => import('@shared/pages/workflows/workflows.component').then(m => m.WorkflowsComponent)\n  }, {\n    path: 'workflows/create',\n    loadComponent: () => import('@shared/pages/workflows/workflow-editor/workflow-editor.component').then(m => m.WorkflowEditorComponent)\n  }, {\n    path: 'workflows/edit/:id',\n    loadComponent: () => import('@shared/pages/workflows/workflow-editor/workflow-editor.component').then(m => m.WorkflowEditorComponent)\n  }, {\n    path: 'workflows/execute/:id',\n    loadComponent: () => import('@shared/pages/workflows/workflow-execution/workflow-execution.component').then(m => m.WorkflowExecutionComponent)\n  }]\n}, {\n  path: 'tools',\n  redirectTo: '/dashboard'\n}, {\n  path: 'team',\n  redirectTo: '/dashboard'\n}, {\n  path: '',\n  redirectTo: '/dashboard',\n  pathMatch: 'full'\n}, {\n  path: '**',\n  redirectTo: '/marketplace'\n}];", "map": {"version": 3, "names": ["MarketplaceAuthGuard", "LoginComponent", "CallbackComponent", "LaunchpadHomeComponent", "AgentsFilterComponent", "MyAgentHomeComponent", "MarketplaceComponent", "AnalyticsComponent", "DocumentsComponent", "routes", "path", "component", "canActivate", "children", "loadComponent", "then", "m", "AgentsComponent", "BuildAgentsComponent", "AgentExecutionComponent", "WorkflowsComponent", "WorkflowEditorComponent", "WorkflowExecutionComponent", "redirectTo", "pathMatch"], "sources": ["C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\app.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\r\nimport { MarketplaceAuthGuard } from './shared/guards/marketplace-auth.guard';\r\nimport { LoginComponent } from '@shared/auth/components/login/login.component';\r\nimport { CallbackComponent } from '@shared/auth/components/callback/callback.component';\r\nimport { LaunchpadHomeComponent } from './pages/launchpad-home/launchpad-home.component';\r\nimport { AgentsFilterComponent } from './pages/agents-filter/agents-filter.component';\r\nimport { MyAgentHomeComponent } from './shared/components/my-agent-home/my-agent-home.component';\r\nimport { MarketplaceComponent } from './pages/marketplace/marketplace.component';\r\nimport { RootRedirectComponent } from './shared/components/root-redirect/root-redirect.component';\r\nimport { AnalyticsComponent } from './shared/components/analytics/analytics.component';\r\nimport { DocumentsComponent } from './shared/components/documents/documents.component';\r\n\r\nexport const routes: Routes = [\r\n  { path: 'marketplace', component: MarketplaceComponent },\r\n  { path: 'login', component: LoginComponent },\r\n  { path: 'callback', component: CallbackComponent },\r\n\r\n  {\r\n    path: 'dashboard',\r\n    canActivate: [MarketplaceAuthGuard],\r\n    component: LaunchpadHomeComponent,\r\n  },\r\n  {\r\n    path: 'agent-list',\r\n    canActivate: [MarketplaceAuthGuard],\r\n    component: AgentsFilterComponent,\r\n  },\r\n  {\r\n    path: 'my-agent-home',\r\n    canActivate: [MarketplaceAuthGuard],\r\n    component: MyAgentHomeComponent,\r\n  },\r\n  {\r\n    path: 'analytics',\r\n    canActivate: [MarketplaceAuthGuard],\r\n    component: AnalyticsComponent,\r\n  },\r\n  {\r\n    path: 'documents',\r\n    canActivate: [MarketplaceAuthGuard],\r\n    component: DocumentsComponent,\r\n  },\r\n  {\r\n    path: 'build',\r\n    canActivate: [MarketplaceAuthGuard],\r\n    children: [\r\n      {\r\n        path: 'agents',\r\n        loadComponent: () =>\r\n          import('@shared/pages/agents/agents.component').then(\r\n            (m) => m.AgentsComponent,\r\n          ),\r\n      },\r\n      {\r\n        path: 'agents/:type',\r\n        loadComponent: () =>\r\n          import('@shared/pages/agents/build-agents/build-agents.component').then(\r\n            (m) => m.BuildAgentsComponent,\r\n          ),\r\n      },\r\n      {\r\n        path: 'agents/:type/execute',\r\n        loadComponent: () =>\r\n          import(\r\n            '@shared/pages/agents/agent-execution/agent-execution.component'\r\n          ).then((m) => m.AgentExecutionComponent),\r\n      },\r\n      {\r\n        path: 'workflows',\r\n        loadComponent: () =>\r\n          import('@shared/pages/workflows/workflows.component').then(\r\n            (m) => m.WorkflowsComponent,\r\n          ),\r\n      },\r\n      {\r\n        path: 'workflows/create',\r\n        loadComponent: () =>\r\n          import('@shared/pages/workflows/workflow-editor/workflow-editor.component').then(\r\n            (m) => m.WorkflowEditorComponent,\r\n          ),\r\n      },\r\n      {\r\n        path: 'workflows/edit/:id',\r\n        loadComponent: () =>\r\n          import('@shared/pages/workflows/workflow-editor/workflow-editor.component').then(\r\n            (m) => m.WorkflowEditorComponent,\r\n          ),\r\n      },\r\n      {\r\n        path: 'workflows/execute/:id',\r\n        loadComponent: () =>\r\n          import('@shared/pages/workflows/workflow-execution/workflow-execution.component').then(\r\n            (m) => m.WorkflowExecutionComponent,\r\n          ),\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    path: 'tools',\r\n    redirectTo: '/dashboard',\r\n  },\r\n  {\r\n    path: 'team',\r\n    redirectTo: '/dashboard',\r\n  },\r\n  {\r\n    path: '',\r\n    redirectTo: '/dashboard',\r\n    pathMatch: 'full',\r\n  },\r\n  {\r\n    path: '**',\r\n    redirectTo: '/marketplace',\r\n  },\r\n];\r\n"], "mappings": "AACA,SAASA,oBAAoB,QAAQ,wCAAwC;AAC7E,SAASC,cAAc,QAAQ,+CAA+C;AAC9E,SAASC,iBAAiB,QAAQ,qDAAqD;AACvF,SAASC,sBAAsB,QAAQ,iDAAiD;AACxF,SAASC,qBAAqB,QAAQ,+CAA+C;AACrF,SAASC,oBAAoB,QAAQ,2DAA2D;AAChG,SAASC,oBAAoB,QAAQ,2CAA2C;AAEhF,SAASC,kBAAkB,QAAQ,mDAAmD;AACtF,SAASC,kBAAkB,QAAQ,mDAAmD;AAEtF,OAAO,MAAMC,MAAM,GAAW,CAC5B;EAAEC,IAAI,EAAE,aAAa;EAAEC,SAAS,EAAEL;AAAoB,CAAE,EACxD;EAAEI,IAAI,EAAE,OAAO;EAAEC,SAAS,EAAEV;AAAc,CAAE,EAC5C;EAAES,IAAI,EAAE,UAAU;EAAEC,SAAS,EAAET;AAAiB,CAAE,EAElD;EACEQ,IAAI,EAAE,WAAW;EACjBE,WAAW,EAAE,CAACZ,oBAAoB,CAAC;EACnCW,SAAS,EAAER;CACZ,EACD;EACEO,IAAI,EAAE,YAAY;EAClBE,WAAW,EAAE,CAACZ,oBAAoB,CAAC;EACnCW,SAAS,EAAEP;CACZ,EACD;EACEM,IAAI,EAAE,eAAe;EACrBE,WAAW,EAAE,CAACZ,oBAAoB,CAAC;EACnCW,SAAS,EAAEN;CACZ,EACD;EACEK,IAAI,EAAE,WAAW;EACjBE,WAAW,EAAE,CAACZ,oBAAoB,CAAC;EACnCW,SAAS,EAAEJ;CACZ,EACD;EACEG,IAAI,EAAE,WAAW;EACjBE,WAAW,EAAE,CAACZ,oBAAoB,CAAC;EACnCW,SAAS,EAAEH;CACZ,EACD;EACEE,IAAI,EAAE,OAAO;EACbE,WAAW,EAAE,CAACZ,oBAAoB,CAAC;EACnCa,QAAQ,EAAE,CACR;IACEH,IAAI,EAAE,QAAQ;IACdI,aAAa,EAAEA,CAAA,KACb,MAAM,CAAC,uCAAuC,CAAC,CAACC,IAAI,CACjDC,CAAC,IAAKA,CAAC,CAACC,eAAe;GAE7B,EACD;IACEP,IAAI,EAAE,cAAc;IACpBI,aAAa,EAAEA,CAAA,KACb,MAAM,CAAC,0DAA0D,CAAC,CAACC,IAAI,CACpEC,CAAC,IAAKA,CAAC,CAACE,oBAAoB;GAElC,EACD;IACER,IAAI,EAAE,sBAAsB;IAC5BI,aAAa,EAAEA,CAAA,KACb,MAAM,CACJ,gEAAgE,CACjE,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACG,uBAAuB;GAC1C,EACD;IACET,IAAI,EAAE,WAAW;IACjBI,aAAa,EAAEA,CAAA,KACb,MAAM,CAAC,6CAA6C,CAAC,CAACC,IAAI,CACvDC,CAAC,IAAKA,CAAC,CAACI,kBAAkB;GAEhC,EACD;IACEV,IAAI,EAAE,kBAAkB;IACxBI,aAAa,EAAEA,CAAA,KACb,MAAM,CAAC,mEAAmE,CAAC,CAACC,IAAI,CAC7EC,CAAC,IAAKA,CAAC,CAACK,uBAAuB;GAErC,EACD;IACEX,IAAI,EAAE,oBAAoB;IAC1BI,aAAa,EAAEA,CAAA,KACb,MAAM,CAAC,mEAAmE,CAAC,CAACC,IAAI,CAC7EC,CAAC,IAAKA,CAAC,CAACK,uBAAuB;GAErC,EACD;IACEX,IAAI,EAAE,uBAAuB;IAC7BI,aAAa,EAAEA,CAAA,KACb,MAAM,CAAC,yEAAyE,CAAC,CAACC,IAAI,CACnFC,CAAC,IAAKA,CAAC,CAACM,0BAA0B;GAExC;CAEJ,EACD;EACEZ,IAAI,EAAE,OAAO;EACba,UAAU,EAAE;CACb,EACD;EACEb,IAAI,EAAE,MAAM;EACZa,UAAU,EAAE;CACb,EACD;EACEb,IAAI,EAAE,EAAE;EACRa,UAAU,EAAE,YAAY;EACxBC,SAAS,EAAE;CACZ,EACD;EACEd,IAAI,EAAE,IAAI;EACVa,UAAU,EAAE;CACb,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}