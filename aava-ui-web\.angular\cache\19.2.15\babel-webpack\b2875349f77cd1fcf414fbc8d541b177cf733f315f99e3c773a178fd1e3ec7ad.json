{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { FilterTabsComponent } from '../filter-tabs/filter-tabs.component';\nimport { IconComponent } from '@ava/play-comp-library';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../services/drawer.service\";\nimport * as i3 from \"../../services/entity.service\";\nimport * as i4 from \"@angular/common\";\nfunction AgentsComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4);\n    i0.ɵɵelement(2, \"ava-icon\", 5);\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.isSearchLoading ? \"Searching agents...\" : \"Loading agents...\");\n  }\n}\nfunction AgentsComponent_div_2_app_filter_tabs_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-filter-tabs\", 10);\n    i0.ɵɵlistener(\"tabChange\", function AgentsComponent_div_2_app_filter_tabs_1_Template_app_filter_tabs_tabChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onFilterChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"tabs\", ctx_r0.filterTabs)(\"activeTab\", ctx_r0.activeFilter);\n  }\n}\nfunction AgentsComponent_div_2_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵelement(1, \"ava-icon\", 12);\n    i0.ɵɵelementStart(2, \"h3\");\n    i0.ɵɵtext(3, \"No agent present\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"No agents found matching your search query \\\"\", ctx_r0.searchQuery, \"\\\"\");\n  }\n}\nfunction AgentsComponent_div_2_div_3_div_2_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26);\n    i0.ɵɵelement(2, \"ava-icon\", 27);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 28);\n    i0.ɵɵtext(5, \"3 days ago\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const agent_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", agent_r4.users, \" \");\n  }\n}\nfunction AgentsComponent_div_2_div_3_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 17);\n    i0.ɵɵlistener(\"click\", function AgentsComponent_div_2_div_3_div_2_Template_div_click_1_listener() {\n      const agent_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.showAgentDetails(agent_r4));\n    });\n    i0.ɵɵelementStart(2, \"div\", 18)(3, \"div\", 19)(4, \"h2\", 20);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 21);\n    i0.ɵɵelement(7, \"ava-icon\", 22);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"p\", 23);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"truncate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, AgentsComponent_div_2_div_3_div_2_div_12_Template, 6, 1, \"div\", 24);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const agent_r4 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.showTwoColumns ? \"col-12 col-md-6\" : \"col-12 col-md-6 col-lg-3\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"marketplace-card\", ctx_r0.isMarketplace);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(agent_r4.title);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", agent_r4.rating, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 7, agent_r4.description, 75));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isMarketplace);\n  }\n}\nfunction AgentsComponent_div_2_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14);\n    i0.ɵɵtemplate(2, AgentsComponent_div_2_div_3_div_2_Template, 13, 10, \"div\", 15);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"marketplace-scroll\", ctx_r0.isMarketplace)(\"launchpad-no-scroll\", !ctx_r0.isMarketplace);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.getDisplayedAgents());\n  }\n}\nfunction AgentsComponent_div_2_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵelement(1, \"ava-icon\", 12);\n    i0.ɵɵelementStart(2, \"h3\");\n    i0.ɵɵtext(3, \"No agents found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"Try adjusting your filters or search criteria\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AgentsComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵtemplate(1, AgentsComponent_div_2_app_filter_tabs_1_Template, 1, 2, \"app-filter-tabs\", 7)(2, AgentsComponent_div_2_div_2_Template, 6, 1, \"div\", 8)(3, AgentsComponent_div_2_div_3_Template, 3, 5, \"div\", 9)(4, AgentsComponent_div_2_div_4_Template, 6, 0, \"div\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"marketplace-content\", ctx_r0.isMarketplace)(\"launchpad-content\", !ctx_r0.isMarketplace);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.showExploreButton);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.loading && !ctx_r0.isSearchLoading && ctx_r0.searchQuery.trim() && (ctx_r0.searchResults.length === 0 || ctx_r0.getDisplayedAgents().length === 0));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !(!ctx_r0.loading && !ctx_r0.isSearchLoading && ctx_r0.searchQuery.trim() && (ctx_r0.searchResults.length === 0 || ctx_r0.getDisplayedAgents().length === 0)));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.loading && !ctx_r0.isSearchLoading && !ctx_r0.searchQuery.trim() && ctx_r0.agents.length === 0);\n  }\n}\nexport let TruncatePipe = /*#__PURE__*/(() => {\n  class TruncatePipe {\n    transform(value, limit = 75) {\n      if (!value) return '';\n      if (value.length <= limit) return value;\n      return value.substring(0, limit) + '...';\n    }\n    static ɵfac = function TruncatePipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TruncatePipe)();\n    };\n    static ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"truncate\",\n      type: TruncatePipe,\n      pure: true\n    });\n  }\n  return TruncatePipe;\n})();\nexport let AgentsComponent = /*#__PURE__*/(() => {\n  class AgentsComponent {\n    router;\n    drawerService;\n    entityService;\n    agents = [];\n    showExploreButton = true;\n    showTwoColumns = false;\n    isMarketplace = false; // New input to detect marketplace context\n    searchResults = []; // Search results from search bar\n    searchQuery = ''; // Current search query\n    isSearchLoading = false; // Search loading state\n    originalAgents = [];\n    entityAgents = [];\n    loading = false;\n    activeFilter = 'all';\n    filterTabs = [{\n      id: 'all',\n      label: 'All',\n      priority: 100,\n      disabled: false\n    }, {\n      id: 'experience',\n      label: 'Experience Studio',\n      icon: 'lightbulb',\n      priority: 90,\n      disabled: true\n    }, {\n      id: 'product',\n      label: 'Product Studio',\n      icon: 'box',\n      priority: 80,\n      disabled: true\n    }, {\n      id: 'data',\n      label: 'Data Studio',\n      icon: 'database',\n      priority: 70,\n      disabled: true\n    }, {\n      id: 'finops',\n      label: 'FinOps Studio',\n      icon: 'dollar-sign',\n      priority: 60,\n      disabled: true\n    }];\n    constructor(router, drawerService, entityService) {\n      this.router = router;\n      this.drawerService = drawerService;\n      this.entityService = entityService;\n    }\n    ngOnInit() {\n      this.originalAgents = [...this.agents];\n      this.loadEntityAgents();\n    }\n    /**\n     * Load agents from the entity API\n     */\n    loadEntityAgents() {\n      this.loading = true;\n      this.entityService.getAgents(0, 50).subscribe({\n        next: agents => {\n          this.entityAgents = agents;\n          this.loading = false;\n        },\n        error: error => {\n          console.error('Error loading agents:', error);\n          this.loading = false;\n        }\n      });\n    }\n    onFilterChange(filterId) {\n      this.activeFilter = filterId;\n      // Filter agents by studio type if not 'all'\n      if (filterId === 'all') {\n        this.agents = [...this.originalAgents];\n      } else {\n        const studioMap = {\n          experience: 'Experience Studio',\n          product: 'Product Studio',\n          data: 'Data Studio',\n          finops: 'FinOps Studio'\n        };\n        this.agents = this.originalAgents.filter(agent => agent.studio?.type === studioMap[filterId]);\n      }\n    }\n    navigate() {\n      this.router.navigateByUrl('/agent-list');\n    }\n    /**\n     * Shows the agent details panel for the selected agent\n     * @param agent The agent to display details for\n     */\n    showAgentDetails(agent) {\n      // Find the corresponding entity data\n      const entityAgent = this.entityAgents.find(ea => ea.id === agent.id);\n      console.log('Showing agent details for:', agent, entityAgent);\n      this.drawerService.open({\n        data: {\n          ...agent,\n          entityData: entityAgent\n        },\n        width: '650px',\n        position: 'right',\n        onGoToPlayground: selectedAgent => this.goToPlayground(selectedAgent)\n      });\n    }\n    /**\n     * Navigates to the playground for the selected agent\n     */\n    goToPlayground(agent) {\n      if (agent) {\n        // You can replace this with the actual navigation logic\n        this.router.navigate(['/playground', agent.id]);\n        // Close the drawer after navigation\n        this.drawerService.close();\n      }\n    }\n    /**\n     * Gets the agents to display based on the showTwoColumns setting and search results\n     * Returns only 6 agents (3 rows x 2 columns) when showTwoColumns is true\n     */\n    getDisplayedAgents() {\n      let agentsToDisplay = this.agents;\n      // If there's a search query, handle search results\n      if (this.searchQuery.trim()) {\n        // If no search results, return empty array\n        if (this.searchResults.length === 0) {\n          return [];\n        }\n        // If there are search results, filter agents to show only those that match\n        const searchResultIds = this.searchResults.map(result => result.id);\n        // Check if we have all the search result agents in our current list\n        const missingAgentIds = searchResultIds.filter(id => !this.agents.some(agent => agent.id.toString() === id));\n        if (missingAgentIds.length > 0) {\n          console.log('Missing agent IDs from search results:', missingAgentIds);\n          // Create agent objects directly from search results\n          const searchResultAgents = this.searchResults.map(revelioResult => ({\n            id: parseInt(revelioResult.id) || 0,\n            // Convert string id to number\n            title: revelioResult.metadata.name || 'Unknown Agent',\n            description: revelioResult.metadata.description || revelioResult.metadata.details || '',\n            rating: 4.5,\n            // Default rating\n            studio: {\n              name: 'Experience Studio',\n              // Default studio\n              type: 'Experience Studio',\n              backgroundColor: '#FFF4F9',\n              textColor: '#E91E63'\n            },\n            developer: revelioResult.metadata.createdBy || 'Unknown Developer',\n            users: Math.floor(Math.random() * 100) + 10 // Random users count\n          }));\n          // Filter by active tab if not 'all' and we're in marketplace\n          if (this.activeFilter !== 'all' && this.isMarketplace) {\n            const studioMap = {\n              experience: 'Experience Studio',\n              product: 'Product Studio',\n              data: 'Data Studio',\n              finops: 'FinOps Studio'\n            };\n            const targetStudioType = studioMap[this.activeFilter];\n            agentsToDisplay = searchResultAgents.filter(agent => agent.studio?.type === targetStudioType);\n          } else {\n            agentsToDisplay = searchResultAgents;\n          }\n        } else {\n          // All search results are in our current list, filter normally\n          let filteredAgents = this.agents.filter(agent => {\n            const isMatch = searchResultIds.includes(agent.id.toString());\n            console.log(`Agent ${agent.id} (${agent.title}) - Match: ${isMatch}`);\n            return isMatch;\n          });\n          // Further filter by active tab if not 'all' and we're in marketplace\n          if (this.activeFilter !== 'all' && this.isMarketplace) {\n            const studioMap = {\n              experience: 'Experience Studio',\n              product: 'Product Studio',\n              data: 'Data Studio',\n              finops: 'FinOps Studio'\n            };\n            const targetStudioType = studioMap[this.activeFilter];\n            agentsToDisplay = filteredAgents.filter(agent => agent.studio?.type === targetStudioType);\n          } else {\n            agentsToDisplay = filteredAgents;\n          }\n        }\n      }\n      if (this.showTwoColumns) {\n        return agentsToDisplay.slice(0, 6); // Show only 6 agents for 3 rows x 2 columns\n      }\n      return agentsToDisplay; // Show all agents in normal mode\n    }\n    /**\n     * Check if we should show the search results message (only in marketplace)\n     */\n    get shouldShowSearchResults() {\n      return this.searchResults.length > 0 && this.searchQuery.trim() !== '' && this.isMarketplace;\n    }\n    /**\n     * Get the search results count\n     */\n    get searchResultsCount() {\n      return this.searchResults.length;\n    }\n    static ɵfac = function AgentsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AgentsComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.DrawerService), i0.ɵɵdirectiveInject(i3.EntityService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AgentsComponent,\n      selectors: [[\"app-agents\"]],\n      inputs: {\n        agents: \"agents\",\n        showExploreButton: \"showExploreButton\",\n        showTwoColumns: \"showTwoColumns\",\n        isMarketplace: \"isMarketplace\",\n        searchResults: \"searchResults\",\n        searchQuery: \"searchQuery\",\n        isSearchLoading: \"isSearchLoading\"\n      },\n      decls: 3,\n      vars: 6,\n      consts: [[1, \"agents-container\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"agents-content\", 3, \"marketplace-content\", \"launchpad-content\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"loading-spinner\"], [\"iconName\", \"refresh-cw\", \"iconSize\", \"24px\", \"iconColor\", \"#616874\", 1, \"spinning\"], [1, \"agents-content\"], [3, \"tabs\", \"activeTab\", \"tabChange\", 4, \"ngIf\"], [\"class\", \"no-agents\", 4, \"ngIf\"], [\"class\", \"agents-grid-container\", 3, \"marketplace-scroll\", \"launchpad-no-scroll\", 4, \"ngIf\"], [3, \"tabChange\", \"tabs\", \"activeTab\"], [1, \"no-agents\"], [\"iconName\", \"search\", \"iconSize\", \"48px\", \"iconColor\", \"#858aad\"], [1, \"agents-grid-container\"], [1, \"agents-grid\", \"row\"], [3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [3, \"ngClass\"], [1, \"agent-card\", 3, \"click\"], [1, \"card-content\"], [1, \"card-header\", \"truncate-header\"], [1, \"agent-title-truncate\"], [1, \"rating\"], [\"iconName\", \"star\", \"iconSize\", \"18px\", \"iconColor\", \"#FFD700\", 1, \"agent_star-icon\"], [1, \"description\"], [\"class\", \"card-footer\", 4, \"ngIf\"], [1, \"card-footer\"], [1, \"users\"], [\"iconName\", \"user\", \"iconSize\", \"16px\", \"iconColor\", \"#858aad\", 1, \"profile-svg-icon\"], [1, \"agent-time-ago\"]],\n      template: function AgentsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, AgentsComponent_div_1_Template, 5, 1, \"div\", 1)(2, AgentsComponent_div_2_Template, 5, 8, \"div\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"marketplace-container\", ctx.isMarketplace)(\"launchpad-container\", !ctx.isMarketplace);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (ctx.loading || ctx.isSearchLoading) && ctx.isMarketplace);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.isSearchLoading || !ctx.isMarketplace);\n        }\n      },\n      dependencies: [CommonModule, i4.NgClass, i4.NgForOf, i4.NgIf, RouterModule, FilterTabsComponent, TruncatePipe, IconComponent],\n      styles: [\".electric-card, .agents-container .agent-card {\\n  background: rgba(255, 255, 255, 0.2);\\n  backdrop-filter: blur(10px);\\n  -webkit-backdrop-filter: blur(10px);\\n  border-radius: 12px;\\n  padding: 20px;\\n  margin-bottom: 30px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\\n  cursor: pointer;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.electric-card::before, .agents-container .agent-card::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, rgba(140, 101, 247, 0.1), rgba(232, 67, 147, 0.1));\\n  opacity: 0;\\n  z-index: 0;\\n}\\n.electric-card::after, .agents-container .agent-card::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  inset: 0;\\n  padding: 2px;\\n  border-radius: 12px;\\n  background: linear-gradient(90deg, rgba(255, 255, 255, 0.8) 0%, rgba(173, 216, 230, 0.8) 25%, rgba(255, 255, 255, 0.8) 50%, rgba(173, 216, 230, 0.8) 75%, rgba(255, 255, 255, 0.8) 100%);\\n  background-size: 400% 100%;\\n  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);\\n  mask-composite: exclude;\\n  opacity: 0;\\n  pointer-events: none;\\n  filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.7)) drop-shadow(0 0 4px rgba(173, 216, 230, 0.5));\\n}\\n.electric-card .card-content, .agents-container .agent-card .card-content {\\n  position: relative;\\n  z-index: 1;\\n}\\n.electric-card .truncate-header, .agents-container .agent-card .truncate-header {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  width: 100%;\\n  min-width: 0;\\n}\\n.electric-card .truncate-header .agent-title-truncate, .agents-container .agent-card .truncate-header .agent-title-truncate {\\n  flex: 1;\\n  min-width: 0;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  margin: 0;\\n  margin-right: 8px;\\n}\\n.electric-card .truncate-header .rating, .agents-container .agent-card .truncate-header .rating {\\n  flex-shrink: 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n\\n.agents-container {\\n  display: flex;\\n  flex-direction: column;\\n}\\n.agents-container.marketplace-container {\\n  height: 100%;\\n  overflow: hidden;\\n}\\n.agents-container.launchpad-container {\\n  height: auto;\\n  overflow: visible;\\n}\\n.agents-container .search-results-header {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 16px 0;\\n  margin-bottom: 16px;\\n  border-bottom: 1px solid #e5e7eb;\\n}\\n.agents-container .search-results-header .search-results-info {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.agents-container .search-results-header .search-results-info .search-results-text {\\n  color: #616874;\\n  font-family: Mulish;\\n  font-size: 16px;\\n  font-weight: 500;\\n}\\n.agents-container .search-results-header .clear-search-btn {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  background: none;\\n  border: none;\\n  color: #616874;\\n  font-family: Mulish;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  padding: 8px 12px;\\n  border-radius: 8px;\\n  transition: all 0.3s ease;\\n}\\n.agents-container .search-results-header .clear-search-btn:hover {\\n  background-color: rgba(97, 104, 116, 0.1);\\n}\\n.agents-container .search-results-header .clear-search-btn:active {\\n  transform: scale(0.95);\\n}\\n.agents-container .loading-container {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  min-height: 200px;\\n  width: 100%;\\n}\\n.agents-container .loading-container .loading-spinner {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 16px;\\n}\\n.agents-container .loading-container .loading-spinner .spinning {\\n  animation: spin 1s linear infinite;\\n}\\n.agents-container .loading-container .loading-spinner p {\\n  color: #616874;\\n  font-family: Mulish;\\n  font-size: 16px;\\n  font-weight: 500;\\n  margin: 0;\\n}\\n.agents-container .no-agents {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  min-height: 200px;\\n  width: 100%;\\n  gap: 16px;\\n}\\n.agents-container .no-agents h3 {\\n  color: #4C515B;\\n  font-family: Mulish;\\n  font-size: 20px;\\n  font-weight: 600;\\n  margin: 0;\\n}\\n.agents-container .no-agents p {\\n  color: #616874;\\n  font-family: Mulish;\\n  font-size: 16px;\\n  font-weight: 400;\\n  margin: 0;\\n  text-align: center;\\n}\\n.agents-container .agents-content {\\n  display: flex;\\n  flex-direction: column;\\n}\\n.agents-container .agents-content.marketplace-content {\\n  height: 100%;\\n  overflow: hidden;\\n}\\n.agents-container .agents-content.launchpad-content {\\n  height: auto;\\n  overflow: visible;\\n}\\n.agents-container.marketplace-container {\\n  height: 100%;\\n  overflow: hidden;\\n}\\n.agents-container.launchpad-container {\\n  height: auto;\\n  overflow: visible;\\n}\\n.agents-container .search-results-header {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 16px 0;\\n  margin-bottom: 16px;\\n  border-bottom: 1px solid #e5e7eb;\\n}\\n.agents-container .search-results-header .search-results-info {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.agents-container .search-results-header .search-results-info .search-results-text {\\n  color: #616874;\\n  font-family: Mulish;\\n  font-size: 16px;\\n  font-weight: 500;\\n}\\n.agents-container .search-results-header .clear-search-btn {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  background: none;\\n  border: none;\\n  color: #616874;\\n  font-family: Mulish;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  padding: 8px 12px;\\n  border-radius: 8px;\\n  transition: all 0.3s ease;\\n}\\n.agents-container .search-results-header .clear-search-btn:hover {\\n  background-color: rgba(97, 104, 116, 0.1);\\n}\\n.agents-container .search-results-header .clear-search-btn:active {\\n  transform: scale(0.95);\\n}\\n.agents-container .loading-container {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  min-height: 200px;\\n  width: 100%;\\n}\\n.agents-container .loading-container .loading-spinner {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 16px;\\n}\\n.agents-container .loading-container .loading-spinner .spinning {\\n  animation: spin 1s linear infinite;\\n}\\n.agents-container .loading-container .loading-spinner p {\\n  color: #616874;\\n  font-family: Mulish;\\n  font-size: 16px;\\n  font-weight: 500;\\n  margin: 0;\\n}\\n.agents-container .no-agents {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  min-height: 200px;\\n  width: 100%;\\n  gap: 16px;\\n}\\n.agents-container .no-agents h3 {\\n  color: #4C515B;\\n  font-family: Mulish;\\n  font-size: 20px;\\n  font-weight: 600;\\n  margin: 0;\\n}\\n.agents-container .no-agents p {\\n  color: #616874;\\n  font-family: Mulish;\\n  font-size: 16px;\\n  font-weight: 400;\\n  margin: 0;\\n  text-align: center;\\n}\\n.agents-container .agents-content {\\n  display: flex;\\n  flex-direction: column;\\n}\\n.agents-container .agents-content.marketplace-content {\\n  height: 100%;\\n  overflow: hidden;\\n}\\n.agents-container .agents-content.launchpad-content {\\n  height: auto;\\n  overflow: visible;\\n}\\n.agents-container app-filter-tabs {\\n  position: sticky;\\n  top: 0;\\n  z-index: 50;\\n  flex-shrink: 0;\\n  background: white;\\n}\\n.agents-container .agents-grid-container {\\n  flex: 1;\\n  overflow-x: hidden;\\n  padding-bottom: 20px;\\n}\\n.agents-container .agents-grid-container.marketplace-scroll {\\n  overflow-y: auto;\\n}\\n.agents-container .agents-grid-container.launchpad-no-scroll {\\n  overflow-y: visible;\\n}\\n.agents-container .agents-grid {\\n  padding: 0 15px;\\n}\\n.agents-container .agent-card {\\n  /* Remove border-color and transition for border color */\\n  margin: 0 12px 12px 0px;\\n}\\n.agents-container .agent-card .card-header {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  padding: 0px;\\n}\\n.agents-container .agent-card .card-header h2 {\\n  color: #4C515B;\\n  font-family: Mulish;\\n  font-size: 20px;\\n  font-weight: 600;\\n  letter-spacing: -0.456px;\\n  margin: 0;\\n}\\n.agents-container .agent-card .card-header .rating {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  font-weight: 500;\\n}\\n.agents-container .agent-card .description {\\n  color: #474C6B;\\n  text-align: left;\\n  font-family: Inter;\\n  font-size: 14px;\\n  font-weight: 400;\\n  margin: 0;\\n}\\n.agents-container .agent-card .card-footer {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 0px;\\n  width: 100%;\\n}\\n.agents-container .agent-card .card-footer .users {\\n  color: #858aad;\\n  font-family: Mulish;\\n  font-size: 16px;\\n  font-style: normal;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n.agents-container .agent-card .card-footer .agent-time-ago {\\n  color: var(--Neutral-N-800, #474C6B);\\n  font-family: Mulish;\\n  font-size: 16px;\\n  font-style: normal;\\n  font-weight: 500;\\n  line-height: normal;\\n}\\n.agents-container .agent-card.marketplace-card .card-content {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n\\n@keyframes spin {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3Byb2plY3RzL2VsZGVyLXdhbmQvc3JjL2FwcC9zaGFyZWQvY29tcG9uZW50cy9hZ2VudHMvYWdlbnRzLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUNBO0VBQ0Usb0NBQUE7RUFDQSwyQkFBQTtFQUNBLG1DQUFBO0VBQ0EsbUJBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSwwQ0FBQTtFQUNBLGVBQUE7RUFDQSxrQkFBQTtFQUNBLGdCQUFBO0FBQUY7QUFFRTtFQUNFLFdBQUE7RUFDQSxrQkFBQTtFQUNBLE1BQUE7RUFDQSxPQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7RUFDQSxzRkFBQTtFQUNBLFVBQUE7RUFDQSxVQUFBO0FBQUo7QUFHRTtFQUNFLFdBQUE7RUFDQSxrQkFBQTtFQUNBLFFBQUE7RUFDQSxZQUFBO0VBQ0EsbUJBQUE7RUFDQSx3TEFBQTtFQU1BLDBCQUFBO0VBQ0EsOEVBQ0U7RUFHRix1QkFBQTtFQUNBLFVBQUE7RUFDQSxvQkFBQTtFQUNBLG1HQUFBO0FBUko7QUFXRTtFQUNFLGtCQUFBO0VBQ0EsVUFBQTtBQVRKO0FBYUU7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSw4QkFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0FBWEo7QUFhSTtFQUNFLE9BQUE7RUFDQSxZQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLHVCQUFBO0VBQ0EsU0FBQTtFQUNBLGlCQUFBO0FBWE47QUFjSTtFQUNFLGNBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxRQUFBO0FBWk47O0FBa0JBO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0FBZkY7QUFrQkU7RUFDRSxZQUFBO0VBQ0EsZ0JBQUE7QUFoQko7QUFtQkU7RUFDRSxZQUFBO0VBQ0EsaUJBQUE7QUFqQko7QUFxQkU7RUFDRSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQkFBQTtFQUNBLGVBQUE7RUFDQSxtQkFBQTtFQUNBLGdDQUFBO0FBbkJKO0FBcUJJO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsUUFBQTtBQW5CTjtBQXFCTTtFQUNFLGNBQUE7RUFDQSxtQkFBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtBQW5CUjtBQXVCSTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFFBQUE7RUFDQSxnQkFBQTtFQUNBLFlBQUE7RUFDQSxjQUFBO0VBQ0EsbUJBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxlQUFBO0VBQ0EsaUJBQUE7RUFDQSxrQkFBQTtFQUNBLHlCQUFBO0FBckJOO0FBdUJNO0VBQ0UseUNBQUE7QUFyQlI7QUF3Qk07RUFDRSxzQkFBQTtBQXRCUjtBQTRCRTtFQUNFLGFBQUE7RUFDQSx1QkFBQTtFQUNBLG1CQUFBO0VBQ0EsaUJBQUE7RUFDQSxXQUFBO0FBMUJKO0FBNEJJO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsbUJBQUE7RUFDQSxTQUFBO0FBMUJOO0FBNEJNO0VBQ0Usa0NBQUE7QUExQlI7QUE2Qk07RUFDRSxjQUFBO0VBQ0EsbUJBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxTQUFBO0FBM0JSO0FBaUNFO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLGlCQUFBO0VBQ0EsV0FBQTtFQUNBLFNBQUE7QUEvQko7QUFpQ0k7RUFDRSxjQUFBO0VBQ0EsbUJBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxTQUFBO0FBL0JOO0FBa0NJO0VBQ0UsY0FBQTtFQUNBLG1CQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsU0FBQTtFQUNBLGtCQUFBO0FBaENOO0FBcUNFO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0FBbkNKO0FBc0NJO0VBQ0UsWUFBQTtFQUNBLGdCQUFBO0FBcENOO0FBdUNJO0VBQ0UsWUFBQTtFQUNBLGlCQUFBO0FBckNOO0FBMENFO0VBQ0UsWUFBQTtFQUNBLGdCQUFBO0FBeENKO0FBMkNFO0VBQ0UsWUFBQTtFQUNBLGlCQUFBO0FBekNKO0FBNkNFO0VBQ0UsYUFBQTtFQUNBLDhCQUFBO0VBQ0EsbUJBQUE7RUFDQSxlQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQ0FBQTtBQTNDSjtBQTZDSTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFFBQUE7QUEzQ047QUE2Q007RUFDRSxjQUFBO0VBQ0EsbUJBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7QUEzQ1I7QUErQ0k7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxRQUFBO0VBQ0EsZ0JBQUE7RUFDQSxZQUFBO0VBQ0EsY0FBQTtFQUNBLG1CQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsZUFBQTtFQUNBLGlCQUFBO0VBQ0Esa0JBQUE7RUFDQSx5QkFBQTtBQTdDTjtBQStDTTtFQUNFLHlDQUFBO0FBN0NSO0FBZ0RNO0VBQ0Usc0JBQUE7QUE5Q1I7QUFvREU7RUFDRSxhQUFBO0VBQ0EsdUJBQUE7RUFDQSxtQkFBQTtFQUNBLGlCQUFBO0VBQ0EsV0FBQTtBQWxESjtBQW9ESTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQUFBO0VBQ0EsU0FBQTtBQWxETjtBQW9ETTtFQUNFLGtDQUFBO0FBbERSO0FBcURNO0VBQ0UsY0FBQTtFQUNBLG1CQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsU0FBQTtBQW5EUjtBQXlERTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxpQkFBQTtFQUNBLFdBQUE7RUFDQSxTQUFBO0FBdkRKO0FBeURJO0VBQ0UsY0FBQTtFQUNBLG1CQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsU0FBQTtBQXZETjtBQTBESTtFQUNFLGNBQUE7RUFDQSxtQkFBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLFNBQUE7RUFDQSxrQkFBQTtBQXhETjtBQTZERTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtBQTNESjtBQThESTtFQUNFLFlBQUE7RUFDQSxnQkFBQTtBQTVETjtBQStESTtFQUNFLFlBQUE7RUFDQSxpQkFBQTtBQTdETjtBQWtFRTtFQUNFLGdCQUFBO0VBQ0EsTUFBQTtFQUNBLFdBQUE7RUFDQSxjQUFBO0VBQ0EsaUJBQUE7QUFoRUo7QUFvRUU7RUFDRSxPQUFBO0VBQ0Esa0JBQUE7RUFDQSxvQkFBQTtBQWxFSjtBQXFFSTtFQUNFLGdCQUFBO0FBbkVOO0FBdUVJO0VBQ0UsbUJBQUE7QUFyRU47QUEwRUU7RUFDRSxlQUFBO0FBeEVKO0FBMkVFO0VBQ0Usd0RBQUE7RUFFQSx1QkFBQTtBQTFFSjtBQTRFSTtFQUNFLGFBQUE7RUFDQSw4QkFBQTtFQUNBLHVCQUFBO0VBQ0EsWUFBQTtBQTFFTjtBQTRFTTtFQUNFLGNBQUE7RUFDQSxtQkFBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLHdCQUFBO0VBQ0EsU0FBQTtBQTFFUjtBQTZFTTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFFBQUE7RUFDQSxnQkFBQTtBQTNFUjtBQStFSTtFQUNFLGNBQUE7RUFDQSxnQkFBQTtFQUNBLGtCQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsU0FBQTtBQTdFTjtBQWdGSTtFQUNFLGFBQUE7RUFDQSw4QkFBQTtFQUNBLG1CQUFBO0VBQ0EsWUFBQTtFQUNBLFdBQUE7QUE5RU47QUFnRk07RUFDRSxjQUFBO0VBQ0EsbUJBQUE7RUFDQSxlQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFFBQUE7QUE5RVI7QUFpRk07RUFDRSxvQ0FBQTtFQUNBLG1CQUFBO0VBQ0EsZUFBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxtQkFBQTtBQS9FUjtBQXFGTTtFQUNFLGFBQUE7RUFDQSxzQkFBQTtFQUNBLFNBQUE7QUFuRlI7O0FBMEZBO0VBQ0U7SUFDRSx1QkFBQTtFQXZGRjtFQXlGQTtJQUNFLHlCQUFBO0VBdkZGO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyIvLyBHZW5lcmljIGVsZWN0cmljIGNhcmQgc3R5bGVzXHJcbi5lbGVjdHJpYy1jYXJkIHtcclxuICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMik7XHJcbiAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDEwcHgpO1xyXG4gIC13ZWJraXQtYmFja2Ryb3AtZmlsdGVyOiBibHVyKDEwcHgpO1xyXG4gIGJvcmRlci1yYWRpdXM6IDEycHg7XHJcbiAgcGFkZGluZzogMjBweDtcclxuICBtYXJnaW4tYm90dG9tOiAzMHB4O1xyXG4gIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgwLCAwLCAwLCAwLjA1KTtcclxuICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gIG92ZXJmbG93OiBoaWRkZW47XHJcblxyXG4gICY6OmJlZm9yZSB7XHJcbiAgICBjb250ZW50OiAnJztcclxuICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcclxuICAgIHRvcDogMDtcclxuICAgIGxlZnQ6IDA7XHJcbiAgICB3aWR0aDogMTAwJTtcclxuICAgIGhlaWdodDogMTAwJTtcclxuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHJnYmEoMTQwLCAxMDEsIDI0NywgMC4xKSwgcmdiYSgyMzIsIDY3LCAxNDcsIDAuMSkpO1xyXG4gICAgb3BhY2l0eTogMDtcclxuICAgIHotaW5kZXg6IDA7XHJcbiAgfVxyXG5cclxuICAmOjphZnRlciB7XHJcbiAgICBjb250ZW50OiAnJztcclxuICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcclxuICAgIGluc2V0OiAwO1xyXG4gICAgcGFkZGluZzogMnB4O1xyXG4gICAgYm9yZGVyLXJhZGl1czogMTJweDtcclxuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg5MGRlZyxcclxuICAgICAgICByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOCkgMCUsXHJcbiAgICAgICAgcmdiYSgxNzMsIDIxNiwgMjMwLCAwLjgpIDI1JSxcclxuICAgICAgICByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOCkgNTAlLFxyXG4gICAgICAgIHJnYmEoMTczLCAyMTYsIDIzMCwgMC44KSA3NSUsXHJcbiAgICAgICAgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjgpIDEwMCUpO1xyXG4gICAgYmFja2dyb3VuZC1zaXplOiA0MDAlIDEwMCU7XHJcbiAgICAtd2Via2l0LW1hc2s6XHJcbiAgICAgIGxpbmVhci1ncmFkaWVudCgjZmZmIDAgMCkgY29udGVudC1ib3gsXHJcbiAgICAgIGxpbmVhci1ncmFkaWVudCgjZmZmIDAgMCk7XHJcbiAgICAtd2Via2l0LW1hc2stY29tcG9zaXRlOiB4b3I7XHJcbiAgICBtYXNrLWNvbXBvc2l0ZTogZXhjbHVkZTtcclxuICAgIG9wYWNpdHk6IDA7XHJcbiAgICBwb2ludGVyLWV2ZW50czogbm9uZTtcclxuICAgIGZpbHRlcjogZHJvcC1zaGFkb3coMCAwIDJweCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuNykpIGRyb3Atc2hhZG93KDAgMCA0cHggcmdiYSgxNzMsIDIxNiwgMjMwLCAwLjUpKTtcclxuICB9XHJcblxyXG4gIC5jYXJkLWNvbnRlbnQge1xyXG4gICAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gICAgei1pbmRleDogMTtcclxuICB9XHJcblxyXG4gIC8vIFdpZHRoLWJhc2VkIHRydW5jYXRpb24gZm9yIGNhcmQgaGVhZGVyXHJcbiAgLnRydW5jYXRlLWhlYWRlciB7XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcclxuICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgbWluLXdpZHRoOiAwOyAvLyBBbGxvdyBmbGV4IGl0ZW1zIHRvIHNocmluayBiZWxvdyBjb250ZW50IHNpemVcclxuXHJcbiAgICAuYWdlbnQtdGl0bGUtdHJ1bmNhdGUge1xyXG4gICAgICBmbGV4OiAxO1xyXG4gICAgICBtaW4td2lkdGg6IDA7IC8vIEFsbG93IHRoZSB0aXRsZSB0byBzaHJpbmtcclxuICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDtcclxuICAgICAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuICAgICAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7XHJcbiAgICAgIG1hcmdpbjogMDtcclxuICAgICAgbWFyZ2luLXJpZ2h0OiA4cHg7IC8vIEFkZCBzb21lIHNwYWNlIGJldHdlZW4gdGl0bGUgYW5kIHJhdGluZ1xyXG4gICAgfVxyXG5cclxuICAgIC5yYXRpbmcge1xyXG4gICAgICBmbGV4LXNocmluazogMDsgLy8gUHJldmVudCByYXRpbmcgZnJvbSBzaHJpbmtpbmdcclxuICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgZ2FwOiA0cHg7XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4vLyBBZ2VudCBzcGVjaWZpYyBzdHlsZXNcclxuLmFnZW50cy1jb250YWluZXIge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuXHJcbiAgLy8gSGVpZ2h0IGFuZCBvdmVyZmxvdyBiZWhhdmlvciBiYXNlZCBvbiBjb250ZXh0XHJcbiAgJi5tYXJrZXRwbGFjZS1jb250YWluZXIge1xyXG4gICAgaGVpZ2h0OiAxMDAlO1xyXG4gICAgb3ZlcmZsb3c6IGhpZGRlbjsgLy8gUHJldmVudCBjb250YWluZXIgZnJvbSBzY3JvbGxpbmcgaW4gbWFya2V0cGxhY2VcclxuICB9XHJcblxyXG4gICYubGF1bmNocGFkLWNvbnRhaW5lciB7XHJcbiAgICBoZWlnaHQ6IGF1dG87XHJcbiAgICBvdmVyZmxvdzogdmlzaWJsZTsgLy8gQWxsb3cgbmF0dXJhbCBmbG93IGluIGxhdW5jaHBhZFxyXG4gIH1cclxuXHJcbiAgLy8gU2VhcmNoIHJlc3VsdHMgaGVhZGVyXHJcbiAgLnNlYXJjaC1yZXN1bHRzLWhlYWRlciB7XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xyXG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgIHBhZGRpbmc6IDE2cHggMDtcclxuICAgIG1hcmdpbi1ib3R0b206IDE2cHg7XHJcbiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U1ZTdlYjtcclxuXHJcbiAgICAuc2VhcmNoLXJlc3VsdHMtaW5mbyB7XHJcbiAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgIGdhcDogOHB4O1xyXG5cclxuICAgICAgLnNlYXJjaC1yZXN1bHRzLXRleHQge1xyXG4gICAgICAgIGNvbG9yOiAjNjE2ODc0O1xyXG4gICAgICAgIGZvbnQtZmFtaWx5OiBNdWxpc2g7XHJcbiAgICAgICAgZm9udC1zaXplOiAxNnB4O1xyXG4gICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAuY2xlYXItc2VhcmNoLWJ0biB7XHJcbiAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgIGdhcDogNHB4O1xyXG4gICAgICBiYWNrZ3JvdW5kOiBub25lO1xyXG4gICAgICBib3JkZXI6IG5vbmU7XHJcbiAgICAgIGNvbG9yOiAjNjE2ODc0O1xyXG4gICAgICBmb250LWZhbWlseTogTXVsaXNoO1xyXG4gICAgICBmb250LXNpemU6IDE0cHg7XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICAgIGN1cnNvcjogcG9pbnRlcjtcclxuICAgICAgcGFkZGluZzogOHB4IDEycHg7XHJcbiAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcclxuXHJcbiAgICAgICY6aG92ZXIge1xyXG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6IHJnYmEoOTcsIDEwNCwgMTE2LCAwLjEpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAmOmFjdGl2ZSB7XHJcbiAgICAgICAgdHJhbnNmb3JtOiBzY2FsZSgwLjk1KTtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLy8gTG9hZGluZyBzdGF0ZVxyXG4gIC5sb2FkaW5nLWNvbnRhaW5lciB7XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgbWluLWhlaWdodDogMjAwcHg7XHJcbiAgICB3aWR0aDogMTAwJTtcclxuXHJcbiAgICAubG9hZGluZy1zcGlubmVyIHtcclxuICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgZ2FwOiAxNnB4O1xyXG5cclxuICAgICAgLnNwaW5uaW5nIHtcclxuICAgICAgICBhbmltYXRpb246IHNwaW4gMXMgbGluZWFyIGluZmluaXRlO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBwIHtcclxuICAgICAgICBjb2xvcjogIzYxNjg3NDtcclxuICAgICAgICBmb250LWZhbWlseTogTXVsaXNoO1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMTZweDtcclxuICAgICAgICBmb250LXdlaWdodDogNTAwO1xyXG4gICAgICAgIG1hcmdpbjogMDtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLy8gTm8gYWdlbnRzIGZvdW5kIHN0YXRlXHJcbiAgLm5vLWFnZW50cyB7XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICAgIG1pbi1oZWlnaHQ6IDIwMHB4O1xyXG4gICAgd2lkdGg6IDEwMCU7XHJcbiAgICBnYXA6IDE2cHg7XHJcblxyXG4gICAgaDMge1xyXG4gICAgICBjb2xvcjogIzRDNTE1QjtcclxuICAgICAgZm9udC1mYW1pbHk6IE11bGlzaDtcclxuICAgICAgZm9udC1zaXplOiAyMHB4O1xyXG4gICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICBtYXJnaW46IDA7XHJcbiAgICB9XHJcblxyXG4gICAgcCB7XHJcbiAgICAgIGNvbG9yOiAjNjE2ODc0O1xyXG4gICAgICBmb250LWZhbWlseTogTXVsaXNoO1xyXG4gICAgICBmb250LXNpemU6IDE2cHg7XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA0MDA7XHJcbiAgICAgIG1hcmdpbjogMDtcclxuICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLy8gQ29udGVudCB3cmFwcGVyXHJcbiAgLmFnZW50cy1jb250ZW50IHtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG5cclxuICAgIC8vIEhlaWdodCBhbmQgb3ZlcmZsb3cgYmVoYXZpb3IgYmFzZWQgb24gY29udGV4dFxyXG4gICAgJi5tYXJrZXRwbGFjZS1jb250ZW50IHtcclxuICAgICAgaGVpZ2h0OiAxMDAlO1xyXG4gICAgICBvdmVyZmxvdzogaGlkZGVuO1xyXG4gICAgfVxyXG5cclxuICAgICYubGF1bmNocGFkLWNvbnRlbnQge1xyXG4gICAgICBoZWlnaHQ6IGF1dG87XHJcbiAgICAgIG92ZXJmbG93OiB2aXNpYmxlO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLy8gSGVpZ2h0IGFuZCBvdmVyZmxvdyBiZWhhdmlvciBiYXNlZCBvbiBjb250ZXh0XHJcbiAgJi5tYXJrZXRwbGFjZS1jb250YWluZXIge1xyXG4gICAgaGVpZ2h0OiAxMDAlO1xyXG4gICAgb3ZlcmZsb3c6IGhpZGRlbjsgLy8gUHJldmVudCBjb250YWluZXIgZnJvbSBzY3JvbGxpbmcgaW4gbWFya2V0cGxhY2VcclxuICB9XHJcblxyXG4gICYubGF1bmNocGFkLWNvbnRhaW5lciB7XHJcbiAgICBoZWlnaHQ6IGF1dG87XHJcbiAgICBvdmVyZmxvdzogdmlzaWJsZTsgLy8gQWxsb3cgbmF0dXJhbCBmbG93IGluIGxhdW5jaHBhZFxyXG4gIH1cclxuXHJcbiAgLy8gU2VhcmNoIHJlc3VsdHMgaGVhZGVyXHJcbiAgLnNlYXJjaC1yZXN1bHRzLWhlYWRlciB7XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xyXG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgIHBhZGRpbmc6IDE2cHggMDtcclxuICAgIG1hcmdpbi1ib3R0b206IDE2cHg7XHJcbiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U1ZTdlYjtcclxuXHJcbiAgICAuc2VhcmNoLXJlc3VsdHMtaW5mbyB7XHJcbiAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgIGdhcDogOHB4O1xyXG5cclxuICAgICAgLnNlYXJjaC1yZXN1bHRzLXRleHQge1xyXG4gICAgICAgIGNvbG9yOiAjNjE2ODc0O1xyXG4gICAgICAgIGZvbnQtZmFtaWx5OiBNdWxpc2g7XHJcbiAgICAgICAgZm9udC1zaXplOiAxNnB4O1xyXG4gICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAuY2xlYXItc2VhcmNoLWJ0biB7XHJcbiAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgIGdhcDogNHB4O1xyXG4gICAgICBiYWNrZ3JvdW5kOiBub25lO1xyXG4gICAgICBib3JkZXI6IG5vbmU7XHJcbiAgICAgIGNvbG9yOiAjNjE2ODc0O1xyXG4gICAgICBmb250LWZhbWlseTogTXVsaXNoO1xyXG4gICAgICBmb250LXNpemU6IDE0cHg7XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICAgIGN1cnNvcjogcG9pbnRlcjtcclxuICAgICAgcGFkZGluZzogOHB4IDEycHg7XHJcbiAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcclxuXHJcbiAgICAgICY6aG92ZXIge1xyXG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6IHJnYmEoOTcsIDEwNCwgMTE2LCAwLjEpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAmOmFjdGl2ZSB7XHJcbiAgICAgICAgdHJhbnNmb3JtOiBzY2FsZSgwLjk1KTtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLy8gTG9hZGluZyBzdGF0ZVxyXG4gIC5sb2FkaW5nLWNvbnRhaW5lciB7XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgbWluLWhlaWdodDogMjAwcHg7XHJcbiAgICB3aWR0aDogMTAwJTtcclxuXHJcbiAgICAubG9hZGluZy1zcGlubmVyIHtcclxuICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgZ2FwOiAxNnB4O1xyXG5cclxuICAgICAgLnNwaW5uaW5nIHtcclxuICAgICAgICBhbmltYXRpb246IHNwaW4gMXMgbGluZWFyIGluZmluaXRlO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBwIHtcclxuICAgICAgICBjb2xvcjogIzYxNjg3NDtcclxuICAgICAgICBmb250LWZhbWlseTogTXVsaXNoO1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMTZweDtcclxuICAgICAgICBmb250LXdlaWdodDogNTAwO1xyXG4gICAgICAgIG1hcmdpbjogMDtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLy8gTm8gYWdlbnRzIGZvdW5kIHN0YXRlXHJcbiAgLm5vLWFnZW50cyB7XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICAgIG1pbi1oZWlnaHQ6IDIwMHB4O1xyXG4gICAgd2lkdGg6IDEwMCU7XHJcbiAgICBnYXA6IDE2cHg7XHJcblxyXG4gICAgaDMge1xyXG4gICAgICBjb2xvcjogIzRDNTE1QjtcclxuICAgICAgZm9udC1mYW1pbHk6IE11bGlzaDtcclxuICAgICAgZm9udC1zaXplOiAyMHB4O1xyXG4gICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICBtYXJnaW46IDA7XHJcbiAgICB9XHJcblxyXG4gICAgcCB7XHJcbiAgICAgIGNvbG9yOiAjNjE2ODc0O1xyXG4gICAgICBmb250LWZhbWlseTogTXVsaXNoO1xyXG4gICAgICBmb250LXNpemU6IDE2cHg7XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA0MDA7XHJcbiAgICAgIG1hcmdpbjogMDtcclxuICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLy8gQ29udGVudCB3cmFwcGVyXHJcbiAgLmFnZW50cy1jb250ZW50IHtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG5cclxuICAgIC8vIEhlaWdodCBhbmQgb3ZlcmZsb3cgYmVoYXZpb3IgYmFzZWQgb24gY29udGV4dFxyXG4gICAgJi5tYXJrZXRwbGFjZS1jb250ZW50IHtcclxuICAgICAgaGVpZ2h0OiAxMDAlO1xyXG4gICAgICBvdmVyZmxvdzogaGlkZGVuO1xyXG4gICAgfVxyXG5cclxuICAgICYubGF1bmNocGFkLWNvbnRlbnQge1xyXG4gICAgICBoZWlnaHQ6IGF1dG87XHJcbiAgICAgIG92ZXJmbG93OiB2aXNpYmxlO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLy8gU3RpY2t5IGZpbHRlciB0YWJzXHJcbiAgYXBwLWZpbHRlci10YWJzIHtcclxuICAgIHBvc2l0aW9uOiBzdGlja3k7XHJcbiAgICB0b3A6IDA7XHJcbiAgICB6LWluZGV4OiA1MDtcclxuICAgIGZsZXgtc2hyaW5rOiAwO1xyXG4gICAgYmFja2dyb3VuZDogd2hpdGU7IC8vIEVuc3VyZSB0YWJzIGhhdmUgYmFja2dyb3VuZCB3aGVuIHN0aWNreVxyXG4gIH1cclxuXHJcbiAgLy8gU2Nyb2xsYWJsZSBjb250YWluZXIgZm9yIGFnZW50cyBncmlkIC0gb25seSBpbiBtYXJrZXRwbGFjZVxyXG4gIC5hZ2VudHMtZ3JpZC1jb250YWluZXIge1xyXG4gICAgZmxleDogMTtcclxuICAgIG92ZXJmbG93LXg6IGhpZGRlbjtcclxuICAgIHBhZGRpbmctYm90dG9tOiAyMHB4O1xyXG5cclxuICAgIC8vIFNjcm9sbGluZyBvbmx5IGluIG1hcmtldHBsYWNlXHJcbiAgICAmLm1hcmtldHBsYWNlLXNjcm9sbCB7XHJcbiAgICAgIG92ZXJmbG93LXk6IGF1dG87XHJcbiAgICB9XHJcblxyXG4gICAgLy8gTm8gc2Nyb2xsaW5nIGluIGxhdW5jaHBhZFxyXG4gICAgJi5sYXVuY2hwYWQtbm8tc2Nyb2xsIHtcclxuICAgICAgb3ZlcmZsb3cteTogdmlzaWJsZTtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8vIEFnZW50cyBncmlkXHJcbiAgLmFnZW50cy1ncmlkIHtcclxuICAgIHBhZGRpbmc6IDAgMTVweDsgLy8gQm9vdHN0cmFwIHJvdyBwYWRkaW5nXHJcbiAgfVxyXG5cclxuICAuYWdlbnQtY2FyZCB7XHJcbiAgICAvKiBSZW1vdmUgYm9yZGVyLWNvbG9yIGFuZCB0cmFuc2l0aW9uIGZvciBib3JkZXIgY29sb3IgKi9cclxuICAgIEBleHRlbmQgLmVsZWN0cmljLWNhcmQ7XHJcbiAgICBtYXJnaW46IDAgMTJweCAxMnB4IDBweDtcclxuXHJcbiAgICAuY2FyZC1oZWFkZXIge1xyXG4gICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XHJcbiAgICAgIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0O1xyXG4gICAgICBwYWRkaW5nOiAwcHg7XHJcblxyXG4gICAgICBoMiB7XHJcbiAgICAgICAgY29sb3I6ICM0QzUxNUI7XHJcbiAgICAgICAgZm9udC1mYW1pbHk6IE11bGlzaDtcclxuICAgICAgICBmb250LXNpemU6IDIwcHg7XHJcbiAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgICAgICBsZXR0ZXItc3BhY2luZzogLTAuNDU2cHg7XHJcbiAgICAgICAgbWFyZ2luOiAwO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAucmF0aW5nIHtcclxuICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgICAgZ2FwOiA0cHg7XHJcbiAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC5kZXNjcmlwdGlvbiB7XHJcbiAgICAgIGNvbG9yOiAjNDc0QzZCO1xyXG4gICAgICB0ZXh0LWFsaWduOiBsZWZ0O1xyXG4gICAgICBmb250LWZhbWlseTogSW50ZXI7XHJcbiAgICAgIGZvbnQtc2l6ZTogMTRweDtcclxuICAgICAgZm9udC13ZWlnaHQ6IDQwMDtcclxuICAgICAgbWFyZ2luOiAwO1xyXG4gICAgfVxyXG5cclxuICAgIC5jYXJkLWZvb3RlciB7XHJcbiAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcclxuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgcGFkZGluZzogMHB4O1xyXG4gICAgICB3aWR0aDogMTAwJTtcclxuXHJcbiAgICAgIC51c2VycyB7XHJcbiAgICAgICAgY29sb3I6ICM4NThhYWQ7XHJcbiAgICAgICAgZm9udC1mYW1pbHk6IE11bGlzaDtcclxuICAgICAgICBmb250LXNpemU6IDE2cHg7XHJcbiAgICAgICAgZm9udC1zdHlsZTogbm9ybWFsO1xyXG4gICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICAgIGdhcDo0cHg7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC5hZ2VudC10aW1lLWFnbyB7XHJcbiAgICAgICAgY29sb3I6IHZhcigtLU5ldXRyYWwtTi04MDAsICM0NzRDNkIpO1xyXG4gICAgICAgIGZvbnQtZmFtaWx5OiBNdWxpc2g7XHJcbiAgICAgICAgZm9udC1zaXplOiAxNnB4O1xyXG4gICAgICAgIGZvbnQtc3R5bGU6IG5vcm1hbDtcclxuICAgICAgICBmb250LXdlaWdodDogNTAwO1xyXG4gICAgICAgIGxpbmUtaGVpZ2h0OiBub3JtYWw7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAvLyBNYXJrZXRwbGFjZSBzcGVjaWZpYyBzcGFjaW5nIC0gMTZweCBnYXBzIGJldHdlZW4gc2VjdGlvbnNcclxuICAgICYubWFya2V0cGxhY2UtY2FyZCB7XHJcbiAgICAgIC5jYXJkLWNvbnRlbnQge1xyXG4gICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICAgICAgICBnYXA6IDE2cHg7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi8vIFNwaW5uaW5nIGFuaW1hdGlvblxyXG5Aa2V5ZnJhbWVzIHNwaW4ge1xyXG4gIGZyb20ge1xyXG4gICAgdHJhbnNmb3JtOiByb3RhdGUoMGRlZyk7XHJcbiAgfVxyXG4gIHRvIHtcclxuICAgIHRyYW5zZm9ybTogcm90YXRlKDM2MGRlZyk7XHJcbiAgfVxyXG59XHJcbiJdLCJzb3VyY2VSb290IjoiIn0= */\"],\n      encapsulation: 2\n    });\n  }\n  return AgentsComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "FilterTabsComponent", "IconComponent", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "isSearchLoading", "ɵɵlistener", "AgentsComponent_div_2_app_filter_tabs_1_Template_app_filter_tabs_tabChange_0_listener", "$event", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "onFilterChange", "ɵɵproperty", "filterTabs", "activeFilter", "ɵɵtextInterpolate1", "searchQuery", "agent_r4", "users", "AgentsComponent_div_2_div_3_div_2_Template_div_click_1_listener", "_r3", "$implicit", "showAgentDetails", "ɵɵtemplate", "AgentsComponent_div_2_div_3_div_2_div_12_Template", "showTwoColumns", "ɵɵclassProp", "isMarketplace", "title", "rating", "ɵɵpipeBind2", "description", "AgentsComponent_div_2_div_3_div_2_Template", "getDisplayedAgents", "AgentsComponent_div_2_app_filter_tabs_1_Template", "AgentsComponent_div_2_div_2_Template", "AgentsComponent_div_2_div_3_Template", "AgentsComponent_div_2_div_4_Template", "showExploreButton", "loading", "trim", "searchResults", "length", "agents", "TruncatePipe", "transform", "value", "limit", "substring", "pure", "AgentsComponent", "router", "drawerService", "entityService", "originalAgents", "entityAgents", "id", "label", "priority", "disabled", "icon", "constructor", "ngOnInit", "loadEntityAgents", "getAgents", "subscribe", "next", "error", "console", "filterId", "studioMap", "experience", "product", "data", "finops", "filter", "agent", "studio", "type", "navigate", "navigateByUrl", "entityAgent", "find", "ea", "log", "open", "entityData", "width", "position", "onGoToPlayground", "selectedAgent", "goToPlayground", "close", "agentsToDisplay", "searchResultIds", "map", "result", "missingAgentIds", "some", "toString", "searchResultAgents", "revelioResult", "parseInt", "metadata", "name", "details", "backgroundColor", "textColor", "developer", "created<PERSON>y", "Math", "floor", "random", "targetStudioType", "filteredAgents", "isMatch", "includes", "slice", "shouldShowSearchResults", "searchResultsCount", "ɵɵdirectiveInject", "i1", "Router", "i2", "DrawerService", "i3", "EntityService", "selectors", "inputs", "decls", "vars", "consts", "template", "AgentsComponent_Template", "rf", "ctx", "AgentsComponent_div_1_Template", "AgentsComponent_div_2_Template", "i4", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles", "encapsulation"], "sources": ["C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\shared\\components\\agents\\agents.component.ts", "C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\shared\\components\\agents\\agents.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  Input,\r\n  Pipe,\r\n  PipeTransform,\r\n  ViewEncapsulation,\r\n  OnInit,\r\n} from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Router, RouterModule } from '@angular/router';\r\n\r\nimport { Agent, EntityResult } from '../../interfaces/agent-list.interface';\r\nimport {\r\n  FilterTabsComponent,\r\n  FilterTab,\r\n} from '../filter-tabs/filter-tabs.component';\r\nimport { IconComponent } from '@ava/play-comp-library';\r\nimport { DrawerService } from '../../services/drawer.service';\r\nimport { EntityService } from '../../services/entity.service';\r\nimport { RevelioSearchResult } from '../../services/search.service';\r\n\r\n@Pipe({\r\n  name: 'truncate',\r\n  standalone: true,\r\n})\r\nexport class TruncatePipe implements PipeTransform {\r\n  transform(value: string, limit = 75): string {\r\n    if (!value) return '';\r\n    if (value.length <= limit) return value;\r\n    return value.substring(0, limit) + '...';\r\n  }\r\n}\r\n\r\n@Component({\r\n  selector: 'app-agents',\r\n  templateUrl: './agents.component.html',\r\n  styleUrls: ['./agents.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    RouterModule,\r\n    FilterTabsComponent,\r\n    TruncatePipe,\r\n    IconComponent,\r\n  ],\r\n  encapsulation: ViewEncapsulation.None,\r\n})\r\nexport class AgentsComponent implements OnInit {\r\n  @Input() agents: Agent[] = [];\r\n  @Input() showExploreButton = true;\r\n  @Input() showTwoColumns = false;\r\n  @Input() isMarketplace = false; // New input to detect marketplace context\r\n  @Input() searchResults: RevelioSearchResult[] = []; // Search results from search bar\r\n  @Input() searchQuery: string = ''; // Current search query\r\n  @Input() isSearchLoading: boolean = false; // Search loading state\r\n\r\n  private originalAgents: Agent[] = [];\r\n  entityAgents: EntityResult[] = [];\r\n  loading = false;\r\n\r\n  activeFilter = 'all';\r\n  filterTabs: FilterTab[] = [\r\n    { id: 'all', label: 'All', priority: 100, disabled: false },\r\n    {\r\n      id: 'experience',\r\n      label: 'Experience Studio',\r\n      icon: 'lightbulb',\r\n      priority: 90,\r\n      disabled: true,\r\n    },\r\n    {\r\n      id: 'product',\r\n      label: 'Product Studio',\r\n      icon: 'box',\r\n      priority: 80,\r\n      disabled: true,\r\n    },\r\n    {\r\n      id: 'data',\r\n      label: 'Data Studio',\r\n      icon: 'database',\r\n      priority: 70,\r\n      disabled: true,\r\n    },\r\n    {\r\n      id: 'finops',\r\n      label: 'FinOps Studio',\r\n      icon: 'dollar-sign',\r\n      priority: 60,\r\n      disabled: true,\r\n    },\r\n  ];\r\n  constructor(\r\n    private readonly router: Router,\r\n    private readonly drawerService: DrawerService,\r\n    private readonly entityService: EntityService,\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.originalAgents = [...this.agents];\r\n    this.loadEntityAgents();\r\n  }\r\n\r\n  /**\r\n   * Load agents from the entity API\r\n   */\r\n  private loadEntityAgents(): void {\r\n    this.loading = true;\r\n    this.entityService.getAgents(0, 50).subscribe({\r\n      next: (agents: EntityResult[]) => {\r\n        this.entityAgents = agents;\r\n        this.loading = false;\r\n      },\r\n      error: (error: any) => {\r\n        console.error('Error loading agents:', error);\r\n        this.loading = false;\r\n      },\r\n    });\r\n  }\r\n\r\n  onFilterChange(filterId: string) {\r\n    this.activeFilter = filterId;\r\n    // Filter agents by studio type if not 'all'\r\n    if (filterId === 'all') {\r\n      this.agents = [...this.originalAgents];\r\n    } else {\r\n      const studioMap: any = {\r\n        experience: 'Experience Studio',\r\n        product: 'Product Studio',\r\n        data: 'Data Studio',\r\n        finops: 'FinOps Studio',\r\n      };\r\n      this.agents = this.originalAgents.filter(\r\n        (agent: Agent) => agent.studio?.type === studioMap[filterId],\r\n      );\r\n    }\r\n  }\r\n\r\n  navigate() {\r\n    this.router.navigateByUrl('/agent-list');\r\n  }\r\n\r\n  /**\r\n   * Shows the agent details panel for the selected agent\r\n   * @param agent The agent to display details for\r\n   */\r\n  showAgentDetails(agent: Agent): void {\r\n    // Find the corresponding entity data\r\n    const entityAgent = this.entityAgents.find((ea) => ea.id === agent.id);\r\n\r\n    console.log('Showing agent details for:', agent, entityAgent);\r\n\r\n    this.drawerService.open({\r\n      data: { ...agent, entityData: entityAgent },\r\n      width: '650px',\r\n      position: 'right',\r\n      onGoToPlayground: (selectedAgent: Agent) =>\r\n        this.goToPlayground(selectedAgent),\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Navigates to the playground for the selected agent\r\n   */\r\n  goToPlayground(agent: Agent): void {\r\n    if (agent) {\r\n      // You can replace this with the actual navigation logic\r\n      this.router.navigate(['/playground', agent.id]);\r\n      // Close the drawer after navigation\r\n      this.drawerService.close();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Gets the agents to display based on the showTwoColumns setting and search results\r\n   * Returns only 6 agents (3 rows x 2 columns) when showTwoColumns is true\r\n   */\r\n  getDisplayedAgents(): Agent[] {\r\n    let agentsToDisplay = this.agents;\r\n\r\n    // If there's a search query, handle search results\r\n    if (this.searchQuery.trim()) {\r\n      // If no search results, return empty array\r\n      if (this.searchResults.length === 0) {\r\n        return [];\r\n      }\r\n\r\n      // If there are search results, filter agents to show only those that match\r\n      const searchResultIds = this.searchResults.map((result) => result.id);\r\n\r\n      // Check if we have all the search result agents in our current list\r\n      const missingAgentIds = searchResultIds.filter(\r\n        (id) => !this.agents.some((agent) => agent.id.toString() === id),\r\n      );\r\n\r\n      if (missingAgentIds.length > 0) {\r\n        console.log('Missing agent IDs from search results:', missingAgentIds);\r\n        // Create agent objects directly from search results\r\n        const searchResultAgents = this.searchResults.map((revelioResult) => ({\r\n          id: parseInt(revelioResult.id) || 0, // Convert string id to number\r\n          title: revelioResult.metadata.name || 'Unknown Agent',\r\n          description:\r\n            revelioResult.metadata.description ||\r\n            revelioResult.metadata.details ||\r\n            '',\r\n          rating: 4.5, // Default rating\r\n          studio: {\r\n            name: 'Experience Studio', // Default studio\r\n            type: 'Experience Studio',\r\n            backgroundColor: '#FFF4F9',\r\n            textColor: '#E91E63',\r\n          },\r\n          developer: revelioResult.metadata.createdBy || 'Unknown Developer',\r\n          users: Math.floor(Math.random() * 100) + 10, // Random users count\r\n        }));\r\n\r\n        // Filter by active tab if not 'all' and we're in marketplace\r\n        if (this.activeFilter !== 'all' && this.isMarketplace) {\r\n          const studioMap: any = {\r\n            experience: 'Experience Studio',\r\n            product: 'Product Studio',\r\n            data: 'Data Studio',\r\n            finops: 'FinOps Studio',\r\n          };\r\n          const targetStudioType = studioMap[this.activeFilter];\r\n          agentsToDisplay = searchResultAgents.filter(\r\n            (agent) => agent.studio?.type === targetStudioType,\r\n          );\r\n        } else {\r\n          agentsToDisplay = searchResultAgents;\r\n        }\r\n      } else {\r\n        // All search results are in our current list, filter normally\r\n        let filteredAgents = this.agents.filter((agent) => {\r\n          const isMatch = searchResultIds.includes(agent.id.toString());\r\n          console.log(`Agent ${agent.id} (${agent.title}) - Match: ${isMatch}`);\r\n          return isMatch;\r\n        });\r\n\r\n        // Further filter by active tab if not 'all' and we're in marketplace\r\n        if (this.activeFilter !== 'all' && this.isMarketplace) {\r\n          const studioMap: any = {\r\n            experience: 'Experience Studio',\r\n            product: 'Product Studio',\r\n            data: 'Data Studio',\r\n            finops: 'FinOps Studio',\r\n          };\r\n          const targetStudioType = studioMap[this.activeFilter];\r\n          agentsToDisplay = filteredAgents.filter(\r\n            (agent) => agent.studio?.type === targetStudioType,\r\n          );\r\n        } else {\r\n          agentsToDisplay = filteredAgents;\r\n        }\r\n      }\r\n    }\r\n\r\n    if (this.showTwoColumns) {\r\n      return agentsToDisplay.slice(0, 6); // Show only 6 agents for 3 rows x 2 columns\r\n    }\r\n    return agentsToDisplay; // Show all agents in normal mode\r\n  }\r\n\r\n  /**\r\n   * Check if we should show the search results message (only in marketplace)\r\n   */\r\n  get shouldShowSearchResults(): boolean {\r\n    return (\r\n      this.searchResults.length > 0 &&\r\n      this.searchQuery.trim() !== '' &&\r\n      this.isMarketplace\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get the search results count\r\n   */\r\n  get searchResultsCount(): number {\r\n    return this.searchResults.length;\r\n  }\r\n}\r\n", "<div\r\n  class=\"agents-container\"\r\n  [class.marketplace-container]=\"isMarketplace\"\r\n  [class.launchpad-container]=\"!isMarketplace\"\r\n>\r\n  <!-- Loading State - show in marketplace when loading agents or when search is running -->\r\n  <div\r\n    class=\"loading-container\"\r\n    *ngIf=\"(loading || isSearchLoading) && isMarketplace\"\r\n  >\r\n    <div class=\"loading-spinner\">\r\n      <ava-icon\r\n        iconName=\"refresh-cw\"\r\n        iconSize=\"24px\"\r\n        iconColor=\"#616874\"\r\n        class=\"spinning\"\r\n      ></ava-icon>\r\n      <p>{{ isSearchLoading ? \"Searching agents...\" : \"Loading agents...\" }}</p>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Content when not loading or when in launchpad (always show content) -->\r\n  <div\r\n    *ngIf=\"(!loading && !isSearchLoading) || !isMarketplace\"\r\n    class=\"agents-content\"\r\n    [class.marketplace-content]=\"isMarketplace\"\r\n    [class.launchpad-content]=\"!isMarketplace\"\r\n  >\r\n    <app-filter-tabs\r\n      *ngIf=\"!showExploreButton\"\r\n      [tabs]=\"filterTabs\"\r\n      [activeTab]=\"activeFilter\"\r\n      (tabChange)=\"onFilterChange($event)\"\r\n    >\r\n    </app-filter-tabs>\r\n    <!-- No agents found message for search results - moved to top -->\r\n    <div\r\n      class=\"no-agents\"\r\n      *ngIf=\"\r\n        !loading &&\r\n        !isSearchLoading &&\r\n        searchQuery.trim() &&\r\n        (searchResults.length === 0 || getDisplayedAgents().length === 0)\r\n      \"\r\n    >\r\n      <ava-icon\r\n        iconName=\"search\"\r\n        iconSize=\"48px\"\r\n        iconColor=\"#858aad\"\r\n      ></ava-icon>\r\n      <h3>No agent present</h3>\r\n      <p>No agents found matching your search query \"{{ searchQuery }}\"</p>\r\n    </div>\r\n\r\n    <div\r\n      class=\"agents-grid-container\"\r\n      [class.marketplace-scroll]=\"isMarketplace\"\r\n      [class.launchpad-no-scroll]=\"!isMarketplace\"\r\n      *ngIf=\"\r\n        !(\r\n          !loading &&\r\n          !isSearchLoading &&\r\n          searchQuery.trim() &&\r\n          (searchResults.length === 0 || getDisplayedAgents().length === 0)\r\n        )\r\n      \"\r\n    >\r\n      <div class=\"agents-grid row\">\r\n        <div\r\n          [ngClass]=\"\r\n            showTwoColumns ? 'col-12 col-md-6' : 'col-12 col-md-6 col-lg-3'\r\n          \"\r\n          *ngFor=\"let agent of getDisplayedAgents()\"\r\n        >\r\n          <div\r\n            class=\"agent-card\"\r\n            [class.marketplace-card]=\"isMarketplace\"\r\n            (click)=\"showAgentDetails(agent)\"\r\n          >\r\n            <div class=\"card-content\">\r\n              <div class=\"card-header truncate-header\">\r\n                <h2 class=\"agent-title-truncate\">{{ agent.title }}</h2>\r\n                <div class=\"rating\">\r\n                  <ava-icon\r\n                    iconName=\"star\"\r\n                    iconSize=\"18px\"\r\n                    iconColor=\"#FFD700\"\r\n                    class=\"agent_star-icon\"\r\n                  ></ava-icon>\r\n                  {{ agent.rating }}\r\n                </div>\r\n              </div>\r\n\r\n              <p class=\"description\">{{ agent.description | truncate: 75 }}</p>\r\n\r\n              <!-- Footer comments - only show in marketplace -->\r\n              <div class=\"card-footer\" *ngIf=\"isMarketplace\">\r\n                <div class=\"users\">\r\n                  <ava-icon\r\n                    iconName=\"user\"\r\n                    iconSize=\"16px\"\r\n                    iconColor=\"#858aad\"\r\n                    class=\"profile-svg-icon\"\r\n                  ></ava-icon>\r\n                  {{ agent.users }}\r\n                </div>\r\n                <div class=\"agent-time-ago\">3 days ago</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- No agents found message (general) -->\r\n    <div\r\n      class=\"no-agents\"\r\n      *ngIf=\"\r\n        !loading &&\r\n        !isSearchLoading &&\r\n        !searchQuery.trim() &&\r\n        agents.length === 0\r\n      \"\r\n    >\r\n      <ava-icon\r\n        iconName=\"search\"\r\n        iconSize=\"48px\"\r\n        iconColor=\"#858aad\"\r\n      ></ava-icon>\r\n      <h3>No agents found</h3>\r\n      <p>Try adjusting your filters or search criteria</p>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAQA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAiBC,YAAY,QAAQ,iBAAiB;AAGtD,SACEC,mBAAmB,QAEd,sCAAsC;AAC7C,SAASC,aAAa,QAAQ,wBAAwB;;;;;;;;ICNlDC,EAJF,CAAAC,cAAA,aAGC,aAC8B;IAC3BD,EAAA,CAAAE,SAAA,kBAKY;IACZF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAAmE;IAE1EH,EAF0E,CAAAI,YAAA,EAAI,EACtE,EACF;;;;IAFCJ,EAAA,CAAAK,SAAA,GAAmE;IAAnEL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,eAAA,+CAAmE;;;;;;IAWxER,EAAA,CAAAC,cAAA,0BAKC;IADCD,EAAA,CAAAS,UAAA,uBAAAC,sFAAAC,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAN,MAAA,GAAAP,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAaR,MAAA,CAAAS,cAAA,CAAAL,MAAA,CAAsB;IAAA,EAAC;IAEtCX,EAAA,CAAAI,YAAA,EAAkB;;;;IAHhBJ,EADA,CAAAiB,UAAA,SAAAV,MAAA,CAAAW,UAAA,CAAmB,cAAAX,MAAA,CAAAY,YAAA,CACO;;;;;IAK5BnB,EAAA,CAAAC,cAAA,cAQC;IACCD,EAAA,CAAAE,SAAA,mBAIY;IACZF,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,uBAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACzBJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAA8D;IACnEH,EADmE,CAAAI,YAAA,EAAI,EACjE;;;;IADDJ,EAAA,CAAAK,SAAA,GAA8D;IAA9DL,EAAA,CAAAoB,kBAAA,kDAAAb,MAAA,CAAAc,WAAA,OAA8D;;;;;IA8CvDrB,EADF,CAAAC,cAAA,cAA+C,cAC1B;IACjBD,EAAA,CAAAE,SAAA,mBAKY;IACZF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAG,MAAA,iBAAU;IACxCH,EADwC,CAAAI,YAAA,EAAM,EACxC;;;;IAHFJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAoB,kBAAA,MAAAE,QAAA,CAAAC,KAAA,MACF;;;;;;IA/BNvB,EANF,CAAAC,cAAA,cAKC,cAKE;IADCD,EAAA,CAAAS,UAAA,mBAAAe,gEAAA;MAAA,MAAAF,QAAA,GAAAtB,EAAA,CAAAY,aAAA,CAAAa,GAAA,EAAAC,SAAA;MAAA,MAAAnB,MAAA,GAAAP,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASR,MAAA,CAAAoB,gBAAA,CAAAL,QAAA,CAAuB;IAAA,EAAC;IAI7BtB,EAFJ,CAAAC,cAAA,cAA0B,cACiB,aACN;IAAAD,EAAA,CAAAG,MAAA,GAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACvDJ,EAAA,CAAAC,cAAA,cAAoB;IAClBD,EAAA,CAAAE,SAAA,mBAKY;IACZF,EAAA,CAAAG,MAAA,GACF;IACFH,EADE,CAAAI,YAAA,EAAM,EACF;IAENJ,EAAA,CAAAC,cAAA,YAAuB;IAAAD,EAAA,CAAAG,MAAA,IAAsC;;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAGjEJ,EAAA,CAAA4B,UAAA,KAAAC,iDAAA,kBAA+C;IAcrD7B,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;;;;;IAzCJJ,EAAA,CAAAiB,UAAA,YAAAV,MAAA,CAAAuB,cAAA,kDAEC;IAKC9B,EAAA,CAAAK,SAAA,EAAwC;IAAxCL,EAAA,CAAA+B,WAAA,qBAAAxB,MAAA,CAAAyB,aAAA,CAAwC;IAKHhC,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAM,iBAAA,CAAAgB,QAAA,CAAAW,KAAA,CAAiB;IAQhDjC,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAoB,kBAAA,MAAAE,QAAA,CAAAY,MAAA,MACF;IAGqBlC,EAAA,CAAAK,SAAA,GAAsC;IAAtCL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAAmC,WAAA,QAAAb,QAAA,CAAAc,WAAA,MAAsC;IAGnCpC,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAiB,UAAA,SAAAV,MAAA,CAAAyB,aAAA,CAAmB;;;;;IA7BrDhC,EAbF,CAAAC,cAAA,cAYC,cAC8B;IAC3BD,EAAA,CAAA4B,UAAA,IAAAS,0CAAA,oBAKC;IAuCLrC,EADE,CAAAI,YAAA,EAAM,EACF;;;;IAvDJJ,EADA,CAAA+B,WAAA,uBAAAxB,MAAA,CAAAyB,aAAA,CAA0C,yBAAAzB,MAAA,CAAAyB,aAAA,CACE;IAetBhC,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAiB,UAAA,YAAAV,MAAA,CAAA+B,kBAAA,GAAuB;;;;;IA2C/CtC,EAAA,CAAAC,cAAA,cAQC;IACCD,EAAA,CAAAE,SAAA,mBAIY;IACZF,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACxBJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,oDAA6C;IAClDH,EADkD,CAAAI,YAAA,EAAI,EAChD;;;;;IA7GRJ,EAAA,CAAAC,cAAA,aAKC;IAwFCD,EAvFA,CAAA4B,UAAA,IAAAW,gDAAA,6BAKC,IAAAC,oCAAA,iBAWA,IAAAC,oCAAA,iBAsBA,IAAAC,oCAAA,iBAyDA;IASH1C,EAAA,CAAAI,YAAA,EAAM;;;;IA1GJJ,EADA,CAAA+B,WAAA,wBAAAxB,MAAA,CAAAyB,aAAA,CAA2C,uBAAAzB,MAAA,CAAAyB,aAAA,CACD;IAGvChC,EAAA,CAAAK,SAAA,EAAwB;IAAxBL,EAAA,CAAAiB,UAAA,UAAAV,MAAA,CAAAoC,iBAAA,CAAwB;IASxB3C,EAAA,CAAAK,SAAA,EAKH;IALGL,EAAA,CAAAiB,UAAA,UAAAV,MAAA,CAAAqC,OAAA,KAAArC,MAAA,CAAAC,eAAA,IAAAD,MAAA,CAAAc,WAAA,CAAAwB,IAAA,OAAAtC,MAAA,CAAAuC,aAAA,CAAAC,MAAA,UAAAxC,MAAA,CAAA+B,kBAAA,GAAAS,MAAA,QAKH;IAeG/C,EAAA,CAAAK,SAAA,EAOL;IAPKL,EAAA,CAAAiB,UAAA,YAAAV,MAAA,CAAAqC,OAAA,KAAArC,MAAA,CAAAC,eAAA,IAAAD,MAAA,CAAAc,WAAA,CAAAwB,IAAA,OAAAtC,MAAA,CAAAuC,aAAA,CAAAC,MAAA,UAAAxC,MAAA,CAAA+B,kBAAA,GAAAS,MAAA,SAOL;IAoDK/C,EAAA,CAAAK,SAAA,EAKH;IALGL,EAAA,CAAAiB,UAAA,UAAAV,MAAA,CAAAqC,OAAA,KAAArC,MAAA,CAAAC,eAAA,KAAAD,MAAA,CAAAc,WAAA,CAAAwB,IAAA,MAAAtC,MAAA,CAAAyC,MAAA,CAAAD,MAAA,OAKH;;;ADjGJ,WAAaE,YAAY;EAAnB,MAAOA,YAAY;IACvBC,SAASA,CAACC,KAAa,EAAEC,KAAK,GAAG,EAAE;MACjC,IAAI,CAACD,KAAK,EAAE,OAAO,EAAE;MACrB,IAAIA,KAAK,CAACJ,MAAM,IAAIK,KAAK,EAAE,OAAOD,KAAK;MACvC,OAAOA,KAAK,CAACE,SAAS,CAAC,CAAC,EAAED,KAAK,CAAC,GAAG,KAAK;IAC1C;;uCALWH,YAAY;IAAA;;;YAAZA,YAAY;MAAAK,IAAA;IAAA;;SAAZL,YAAY;AAAA;AAsBzB,WAAaM,eAAe;EAAtB,MAAOA,eAAe;IA8CPC,MAAA;IACAC,aAAA;IACAC,aAAA;IA/CVV,MAAM,GAAY,EAAE;IACpBL,iBAAiB,GAAG,IAAI;IACxBb,cAAc,GAAG,KAAK;IACtBE,aAAa,GAAG,KAAK,CAAC,CAAC;IACvBc,aAAa,GAA0B,EAAE,CAAC,CAAC;IAC3CzB,WAAW,GAAW,EAAE,CAAC,CAAC;IAC1Bb,eAAe,GAAY,KAAK,CAAC,CAAC;IAEnCmD,cAAc,GAAY,EAAE;IACpCC,YAAY,GAAmB,EAAE;IACjChB,OAAO,GAAG,KAAK;IAEfzB,YAAY,GAAG,KAAK;IACpBD,UAAU,GAAgB,CACxB;MAAE2C,EAAE,EAAE,KAAK;MAAEC,KAAK,EAAE,KAAK;MAAEC,QAAQ,EAAE,GAAG;MAAEC,QAAQ,EAAE;IAAK,CAAE,EAC3D;MACEH,EAAE,EAAE,YAAY;MAChBC,KAAK,EAAE,mBAAmB;MAC1BG,IAAI,EAAE,WAAW;MACjBF,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE;KACX,EACD;MACEH,EAAE,EAAE,SAAS;MACbC,KAAK,EAAE,gBAAgB;MACvBG,IAAI,EAAE,KAAK;MACXF,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE;KACX,EACD;MACEH,EAAE,EAAE,MAAM;MACVC,KAAK,EAAE,aAAa;MACpBG,IAAI,EAAE,UAAU;MAChBF,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE;KACX,EACD;MACEH,EAAE,EAAE,QAAQ;MACZC,KAAK,EAAE,eAAe;MACtBG,IAAI,EAAE,aAAa;MACnBF,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE;KACX,CACF;IACDE,YACmBV,MAAc,EACdC,aAA4B,EAC5BC,aAA4B;MAF5B,KAAAF,MAAM,GAANA,MAAM;MACN,KAAAC,aAAa,GAAbA,aAAa;MACb,KAAAC,aAAa,GAAbA,aAAa;IAC7B;IAEHS,QAAQA,CAAA;MACN,IAAI,CAACR,cAAc,GAAG,CAAC,GAAG,IAAI,CAACX,MAAM,CAAC;MACtC,IAAI,CAACoB,gBAAgB,EAAE;IACzB;IAEA;;;IAGQA,gBAAgBA,CAAA;MACtB,IAAI,CAACxB,OAAO,GAAG,IAAI;MACnB,IAAI,CAACc,aAAa,CAACW,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAACC,SAAS,CAAC;QAC5CC,IAAI,EAAGvB,MAAsB,IAAI;UAC/B,IAAI,CAACY,YAAY,GAAGZ,MAAM;UAC1B,IAAI,CAACJ,OAAO,GAAG,KAAK;QACtB,CAAC;QACD4B,KAAK,EAAGA,KAAU,IAAI;UACpBC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;UAC7C,IAAI,CAAC5B,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;IACJ;IAEA5B,cAAcA,CAAC0D,QAAgB;MAC7B,IAAI,CAACvD,YAAY,GAAGuD,QAAQ;MAC5B;MACA,IAAIA,QAAQ,KAAK,KAAK,EAAE;QACtB,IAAI,CAAC1B,MAAM,GAAG,CAAC,GAAG,IAAI,CAACW,cAAc,CAAC;MACxC,CAAC,MAAM;QACL,MAAMgB,SAAS,GAAQ;UACrBC,UAAU,EAAE,mBAAmB;UAC/BC,OAAO,EAAE,gBAAgB;UACzBC,IAAI,EAAE,aAAa;UACnBC,MAAM,EAAE;SACT;QACD,IAAI,CAAC/B,MAAM,GAAG,IAAI,CAACW,cAAc,CAACqB,MAAM,CACrCC,KAAY,IAAKA,KAAK,CAACC,MAAM,EAAEC,IAAI,KAAKR,SAAS,CAACD,QAAQ,CAAC,CAC7D;MACH;IACF;IAEAU,QAAQA,CAAA;MACN,IAAI,CAAC5B,MAAM,CAAC6B,aAAa,CAAC,aAAa,CAAC;IAC1C;IAEA;;;;IAIA1D,gBAAgBA,CAACsD,KAAY;MAC3B;MACA,MAAMK,WAAW,GAAG,IAAI,CAAC1B,YAAY,CAAC2B,IAAI,CAAEC,EAAE,IAAKA,EAAE,CAAC3B,EAAE,KAAKoB,KAAK,CAACpB,EAAE,CAAC;MAEtEY,OAAO,CAACgB,GAAG,CAAC,4BAA4B,EAAER,KAAK,EAAEK,WAAW,CAAC;MAE7D,IAAI,CAAC7B,aAAa,CAACiC,IAAI,CAAC;QACtBZ,IAAI,EAAE;UAAE,GAAGG,KAAK;UAAEU,UAAU,EAAEL;QAAW,CAAE;QAC3CM,KAAK,EAAE,OAAO;QACdC,QAAQ,EAAE,OAAO;QACjBC,gBAAgB,EAAGC,aAAoB,IACrC,IAAI,CAACC,cAAc,CAACD,aAAa;OACpC,CAAC;IACJ;IAEA;;;IAGAC,cAAcA,CAACf,KAAY;MACzB,IAAIA,KAAK,EAAE;QACT;QACA,IAAI,CAACzB,MAAM,CAAC4B,QAAQ,CAAC,CAAC,aAAa,EAAEH,KAAK,CAACpB,EAAE,CAAC,CAAC;QAC/C;QACA,IAAI,CAACJ,aAAa,CAACwC,KAAK,EAAE;MAC5B;IACF;IAEA;;;;IAIA3D,kBAAkBA,CAAA;MAChB,IAAI4D,eAAe,GAAG,IAAI,CAAClD,MAAM;MAEjC;MACA,IAAI,IAAI,CAAC3B,WAAW,CAACwB,IAAI,EAAE,EAAE;QAC3B;QACA,IAAI,IAAI,CAACC,aAAa,CAACC,MAAM,KAAK,CAAC,EAAE;UACnC,OAAO,EAAE;QACX;QAEA;QACA,MAAMoD,eAAe,GAAG,IAAI,CAACrD,aAAa,CAACsD,GAAG,CAAEC,MAAM,IAAKA,MAAM,CAACxC,EAAE,CAAC;QAErE;QACA,MAAMyC,eAAe,GAAGH,eAAe,CAACnB,MAAM,CAC3CnB,EAAE,IAAK,CAAC,IAAI,CAACb,MAAM,CAACuD,IAAI,CAAEtB,KAAK,IAAKA,KAAK,CAACpB,EAAE,CAAC2C,QAAQ,EAAE,KAAK3C,EAAE,CAAC,CACjE;QAED,IAAIyC,eAAe,CAACvD,MAAM,GAAG,CAAC,EAAE;UAC9B0B,OAAO,CAACgB,GAAG,CAAC,wCAAwC,EAAEa,eAAe,CAAC;UACtE;UACA,MAAMG,kBAAkB,GAAG,IAAI,CAAC3D,aAAa,CAACsD,GAAG,CAAEM,aAAa,KAAM;YACpE7C,EAAE,EAAE8C,QAAQ,CAACD,aAAa,CAAC7C,EAAE,CAAC,IAAI,CAAC;YAAE;YACrC5B,KAAK,EAAEyE,aAAa,CAACE,QAAQ,CAACC,IAAI,IAAI,eAAe;YACrDzE,WAAW,EACTsE,aAAa,CAACE,QAAQ,CAACxE,WAAW,IAClCsE,aAAa,CAACE,QAAQ,CAACE,OAAO,IAC9B,EAAE;YACJ5E,MAAM,EAAE,GAAG;YAAE;YACbgD,MAAM,EAAE;cACN2B,IAAI,EAAE,mBAAmB;cAAE;cAC3B1B,IAAI,EAAE,mBAAmB;cACzB4B,eAAe,EAAE,SAAS;cAC1BC,SAAS,EAAE;aACZ;YACDC,SAAS,EAAEP,aAAa,CAACE,QAAQ,CAACM,SAAS,IAAI,mBAAmB;YAClE3F,KAAK,EAAE4F,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,EAAE,CAAE;WAC9C,CAAC,CAAC;UAEH;UACA,IAAI,IAAI,CAAClG,YAAY,KAAK,KAAK,IAAI,IAAI,CAACa,aAAa,EAAE;YACrD,MAAM2C,SAAS,GAAQ;cACrBC,UAAU,EAAE,mBAAmB;cAC/BC,OAAO,EAAE,gBAAgB;cACzBC,IAAI,EAAE,aAAa;cACnBC,MAAM,EAAE;aACT;YACD,MAAMuC,gBAAgB,GAAG3C,SAAS,CAAC,IAAI,CAACxD,YAAY,CAAC;YACrD+E,eAAe,GAAGO,kBAAkB,CAACzB,MAAM,CACxCC,KAAK,IAAKA,KAAK,CAACC,MAAM,EAAEC,IAAI,KAAKmC,gBAAgB,CACnD;UACH,CAAC,MAAM;YACLpB,eAAe,GAAGO,kBAAkB;UACtC;QACF,CAAC,MAAM;UACL;UACA,IAAIc,cAAc,GAAG,IAAI,CAACvE,MAAM,CAACgC,MAAM,CAAEC,KAAK,IAAI;YAChD,MAAMuC,OAAO,GAAGrB,eAAe,CAACsB,QAAQ,CAACxC,KAAK,CAACpB,EAAE,CAAC2C,QAAQ,EAAE,CAAC;YAC7D/B,OAAO,CAACgB,GAAG,CAAC,SAASR,KAAK,CAACpB,EAAE,KAAKoB,KAAK,CAAChD,KAAK,cAAcuF,OAAO,EAAE,CAAC;YACrE,OAAOA,OAAO;UAChB,CAAC,CAAC;UAEF;UACA,IAAI,IAAI,CAACrG,YAAY,KAAK,KAAK,IAAI,IAAI,CAACa,aAAa,EAAE;YACrD,MAAM2C,SAAS,GAAQ;cACrBC,UAAU,EAAE,mBAAmB;cAC/BC,OAAO,EAAE,gBAAgB;cACzBC,IAAI,EAAE,aAAa;cACnBC,MAAM,EAAE;aACT;YACD,MAAMuC,gBAAgB,GAAG3C,SAAS,CAAC,IAAI,CAACxD,YAAY,CAAC;YACrD+E,eAAe,GAAGqB,cAAc,CAACvC,MAAM,CACpCC,KAAK,IAAKA,KAAK,CAACC,MAAM,EAAEC,IAAI,KAAKmC,gBAAgB,CACnD;UACH,CAAC,MAAM;YACLpB,eAAe,GAAGqB,cAAc;UAClC;QACF;MACF;MAEA,IAAI,IAAI,CAACzF,cAAc,EAAE;QACvB,OAAOoE,eAAe,CAACwB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACtC;MACA,OAAOxB,eAAe,CAAC,CAAC;IAC1B;IAEA;;;IAGA,IAAIyB,uBAAuBA,CAAA;MACzB,OACE,IAAI,CAAC7E,aAAa,CAACC,MAAM,GAAG,CAAC,IAC7B,IAAI,CAAC1B,WAAW,CAACwB,IAAI,EAAE,KAAK,EAAE,IAC9B,IAAI,CAACb,aAAa;IAEtB;IAEA;;;IAGA,IAAI4F,kBAAkBA,CAAA;MACpB,OAAO,IAAI,CAAC9E,aAAa,CAACC,MAAM;IAClC;;uCAxOWQ,eAAe,EAAAvD,EAAA,CAAA6H,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAA/H,EAAA,CAAA6H,iBAAA,CAAAG,EAAA,CAAAC,aAAA,GAAAjI,EAAA,CAAA6H,iBAAA,CAAAK,EAAA,CAAAC,aAAA;IAAA;;YAAf5E,eAAe;MAAA6E,SAAA;MAAAC,MAAA;QAAArF,MAAA;QAAAL,iBAAA;QAAAb,cAAA;QAAAE,aAAA;QAAAc,aAAA;QAAAzB,WAAA;QAAAb,eAAA;MAAA;MAAA8H,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC/C5B3I,EAAA,CAAAC,cAAA,aAIC;UAkBCD,EAhBA,CAAA4B,UAAA,IAAAiH,8BAAA,iBAGC,IAAAC,8BAAA,iBAkBA;UA0GH9I,EAAA,CAAAI,YAAA,EAAM;;;UAlIJJ,EADA,CAAA+B,WAAA,0BAAA6G,GAAA,CAAA5G,aAAA,CAA6C,yBAAA4G,GAAA,CAAA5G,aAAA,CACD;UAKzChC,EAAA,CAAAK,SAAA,EAAmD;UAAnDL,EAAA,CAAAiB,UAAA,UAAA2H,GAAA,CAAAhG,OAAA,IAAAgG,GAAA,CAAApI,eAAA,KAAAoI,GAAA,CAAA5G,aAAA,CAAmD;UAenDhC,EAAA,CAAAK,SAAA,EAAsD;UAAtDL,EAAA,CAAAiB,UAAA,UAAA2H,GAAA,CAAAhG,OAAA,KAAAgG,GAAA,CAAApI,eAAA,KAAAoI,GAAA,CAAA5G,aAAA,CAAsD;;;qBDgBvDpC,YAAY,EAAAmJ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EACZrJ,YAAY,EACZC,mBAAmB,EAhBVmD,YAAY,EAkBrBlD,aAAa;MAAAoJ,MAAA;MAAAC,aAAA;IAAA;;SAIJ7F,eAAe;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}