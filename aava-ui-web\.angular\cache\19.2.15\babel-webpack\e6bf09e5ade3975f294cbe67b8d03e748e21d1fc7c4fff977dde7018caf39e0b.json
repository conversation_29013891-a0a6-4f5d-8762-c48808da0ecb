{"ast": null, "code": "export default function (callback) {\n  for (var groups = this._groups, j = 0, m = groups.length; j < m; ++j) {\n    for (var group = groups[j], i = 0, n = group.length, node; i < n; ++i) {\n      if (node = group[i]) callback.call(node, node.__data__, i, group);\n    }\n  }\n  return this;\n}", "map": {"version": 3, "names": ["callback", "groups", "_groups", "j", "m", "length", "group", "i", "n", "node", "call", "__data__"], "sources": ["C:/console/aava-ui-web/node_modules/d3-selection/src/selection/each.js"], "sourcesContent": ["export default function(callback) {\n\n  for (var groups = this._groups, j = 0, m = groups.length; j < m; ++j) {\n    for (var group = groups[j], i = 0, n = group.length, node; i < n; ++i) {\n      if (node = group[i]) callback.call(node, node.__data__, i, group);\n    }\n  }\n\n  return this;\n}\n"], "mappings": "AAAA,eAAe,UAASA,QAAQ,EAAE;EAEhC,KAAK,IAAIC,MAAM,GAAG,IAAI,CAACC,OAAO,EAAEC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGH,MAAM,CAACI,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;IACpE,KAAK,IAAIG,KAAK,GAAGL,MAAM,CAACE,CAAC,CAAC,EAAEI,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGF,KAAK,CAACD,MAAM,EAAEI,IAAI,EAAEF,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;MACrE,IAAIE,IAAI,GAAGH,KAAK,CAACC,CAAC,CAAC,EAAEP,QAAQ,CAACU,IAAI,CAACD,IAAI,EAAEA,IAAI,CAACE,QAAQ,EAAEJ,CAAC,EAAED,KAAK,CAAC;IACnE;EACF;EAEA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}