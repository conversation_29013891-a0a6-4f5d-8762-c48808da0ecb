{"ast": null, "code": "import { normalizeInterval } from \"./_lib/normalizeInterval.js\";\nimport { constructFrom } from \"./constructFrom.js\";\nimport { eachDayOfInterval } from \"./eachDayOfInterval.js\";\nimport { isWeekend } from \"./isWeekend.js\";\n\n/**\n * The {@link eachWeekendOfInterval} function options.\n */\n\n/**\n * The {@link eachWeekendOfInterval} function result type.\n */\n\n/**\n * @name eachWeekendOfInterval\n * @category Interval Helpers\n * @summary List all the Saturdays and Sundays in the given date interval.\n *\n * @description\n * Get all the Saturdays and Sundays in the given date interval.\n *\n * @typeParam IntervalType - Interval type.\n * @typeParam Options - Options type.\n *\n * @param interval - The given interval\n * @param options - An object with options\n *\n * @returns An array containing all the Saturdays and Sundays\n *\n * @example\n * // Lists all Saturdays and Sundays in the given date interval\n * const result = eachWeekendOfInterval({\n *   start: new Date(2018, 8, 17),\n *   end: new Date(2018, 8, 30)\n * })\n * //=> [\n * //   Sat Sep 22 2018 00:00:00,\n * //   Sun Sep 23 2018 00:00:00,\n * //   Sat Sep 29 2018 00:00:00,\n * //   Sun Sep 30 2018 00:00:00\n * // ]\n */\nexport function eachWeekendOfInterval(interval, options) {\n  const {\n    start,\n    end\n  } = normalizeInterval(options?.in, interval);\n  const dateInterval = eachDayOfInterval({\n    start,\n    end\n  }, options);\n  const weekends = [];\n  let index = 0;\n  while (index < dateInterval.length) {\n    const date = dateInterval[index++];\n    if (isWeekend(date)) weekends.push(constructFrom(start, date));\n  }\n  return weekends;\n}\n\n// Fallback for modularized imports:\nexport default eachWeekendOfInterval;", "map": {"version": 3, "names": ["normalizeInterval", "constructFrom", "eachDayOfInterval", "isWeekend", "eachWeekendOfInterval", "interval", "options", "start", "end", "in", "dateInterval", "weekends", "index", "length", "date", "push"], "sources": ["C:/console/aava-ui-web/node_modules/date-fns/eachWeekendOfInterval.js"], "sourcesContent": ["import { normalizeInterval } from \"./_lib/normalizeInterval.js\";\nimport { constructFrom } from \"./constructFrom.js\";\nimport { eachDayOfInterval } from \"./eachDayOfInterval.js\";\nimport { isWeekend } from \"./isWeekend.js\";\n\n/**\n * The {@link eachWeekendOfInterval} function options.\n */\n\n/**\n * The {@link eachWeekendOfInterval} function result type.\n */\n\n/**\n * @name eachWeekendOfInterval\n * @category Interval Helpers\n * @summary List all the Saturdays and Sundays in the given date interval.\n *\n * @description\n * Get all the Saturdays and Sundays in the given date interval.\n *\n * @typeParam IntervalType - Interval type.\n * @typeParam Options - Options type.\n *\n * @param interval - The given interval\n * @param options - An object with options\n *\n * @returns An array containing all the Saturdays and Sundays\n *\n * @example\n * // Lists all Saturdays and Sundays in the given date interval\n * const result = eachWeekendOfInterval({\n *   start: new Date(2018, 8, 17),\n *   end: new Date(2018, 8, 30)\n * })\n * //=> [\n * //   Sat Sep 22 2018 00:00:00,\n * //   Sun Sep 23 2018 00:00:00,\n * //   Sat Sep 29 2018 00:00:00,\n * //   Sun Sep 30 2018 00:00:00\n * // ]\n */\nexport function eachWeekendOfInterval(interval, options) {\n  const { start, end } = normalizeInterval(options?.in, interval);\n  const dateInterval = eachDayOfInterval({ start, end }, options);\n  const weekends = [];\n  let index = 0;\n  while (index < dateInterval.length) {\n    const date = dateInterval[index++];\n    if (isWeekend(date)) weekends.push(constructFrom(start, date));\n  }\n  return weekends;\n}\n\n// Fallback for modularized imports:\nexport default eachWeekendOfInterval;\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,SAAS,QAAQ,gBAAgB;;AAE1C;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,qBAAqBA,CAACC,QAAQ,EAAEC,OAAO,EAAE;EACvD,MAAM;IAAEC,KAAK;IAAEC;EAAI,CAAC,GAAGR,iBAAiB,CAACM,OAAO,EAAEG,EAAE,EAAEJ,QAAQ,CAAC;EAC/D,MAAMK,YAAY,GAAGR,iBAAiB,CAAC;IAAEK,KAAK;IAAEC;EAAI,CAAC,EAAEF,OAAO,CAAC;EAC/D,MAAMK,QAAQ,GAAG,EAAE;EACnB,IAAIC,KAAK,GAAG,CAAC;EACb,OAAOA,KAAK,GAAGF,YAAY,CAACG,MAAM,EAAE;IAClC,MAAMC,IAAI,GAAGJ,YAAY,CAACE,KAAK,EAAE,CAAC;IAClC,IAAIT,SAAS,CAACW,IAAI,CAAC,EAAEH,QAAQ,CAACI,IAAI,CAACd,aAAa,CAACM,KAAK,EAAEO,IAAI,CAAC,CAAC;EAChE;EACA,OAAOH,QAAQ;AACjB;;AAEA;AACA,eAAeP,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}