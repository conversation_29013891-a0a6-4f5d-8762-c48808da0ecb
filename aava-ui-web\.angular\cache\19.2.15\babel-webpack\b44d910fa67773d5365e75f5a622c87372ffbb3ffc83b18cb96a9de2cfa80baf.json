{"ast": null, "code": "import { AgentsComponent } from '../../shared/components/agents/agents.component';\nimport { StudiosComponent } from '../../shared/components/studios/studios.component';\nimport { SidebarComponent } from '../../shared/components/sidebar/sidebar.component';\nimport Hero from '../../shared/components/hero/hero.component';\nimport { AnalyticsCardComponent } from '../../shared/components/analytics-card/analytics-card.component';\nimport { Subscription } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../shared/services/entity.service\";\nimport * as i2 from \"../../shared/service/global-store.service\";\nexport let LaunchpadHomeComponent = /*#__PURE__*/(() => {\n  class LaunchpadHomeComponent {\n    entityService;\n    globalStoreService;\n    agentsData = [];\n    searchResults = [];\n    searchQuery = '';\n    isSearchLoading = false;\n    selectedStudioOption = 'EE';\n    studioSubscription = new Subscription();\n    constructor(entityService, globalStoreService) {\n      this.entityService = entityService;\n      this.globalStoreService = globalStoreService;\n    }\n    ngOnInit() {\n      this.loadAgents();\n      // Subscribe to studio option changes\n      this.studioSubscription = this.globalStoreService.selectedStudioOption.subscribe(option => {\n        this.selectedStudioOption = option;\n      });\n    }\n    ngOnDestroy() {\n      this.studioSubscription.unsubscribe();\n    }\n    /**\n     * Load agents and convert entity data to agent format\n     */\n    loadAgents() {\n      this.entityService.getAgents(0, 50).subscribe({\n        next: entityAgents => {\n          // Convert entity agents to the expected agent format\n          this.agentsData = entityAgents.map(entityAgent => ({\n            id: entityAgent.id,\n            title: entityAgent.name,\n            description: entityAgent.description,\n            rating: 4.5,\n            // Default rating since it's not in the API\n            studio: {\n              name: 'Experience Studio',\n              // Default studio\n              type: 'Experience Studio',\n              backgroundColor: '#FFF4F9',\n              textColor: '#DC047B'\n            },\n            users: Math.floor(Math.random() * 100) + 10 // Random users count for now\n          }));\n        },\n        error: error => {\n          console.error('Error loading agents:', error);\n          // Fallback to empty array\n          this.agentsData = [];\n        }\n      });\n    }\n    /**\n     * Handle search results from the search bar\n     */\n    onSearchResultsChange(results) {\n      this.searchResults = results;\n    }\n    /**\n     * Handle search query change from the search bar\n     */\n    onSearchQueryChange(query) {\n      this.searchQuery = query;\n    }\n    /**\n     * Handle search loading change from the search bar\n     */\n    onSearchLoadingChange(isLoading) {\n      this.isSearchLoading = isLoading;\n    }\n    /**\n     * Handle send clicked from the search bar\n     */\n    onSendClicked(query) {}\n    static ɵfac = function LaunchpadHomeComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LaunchpadHomeComponent)(i0.ɵɵdirectiveInject(i1.EntityService), i0.ɵɵdirectiveInject(i2.GlobalStoreService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LaunchpadHomeComponent,\n      selectors: [[\"app-launchpad-home\"]],\n      decls: 19,\n      vars: 6,\n      consts: [[1, \"launchpad-container\"], [1, \"sidebar-section\"], [1, \"main-content-section\"], [3, \"searchResultsChange\", \"searchQueryChange\", \"searchLoadingChange\", \"sendClicked\"], [1, \"content-section\"], [1, \"section-text\"], [1, \"two-column-layout\"], [1, \"left-column\"], [3, \"selectedStudioOption\"], [1, \"right-column\"], [3, \"agents\", \"showTwoColumns\", \"searchResults\", \"searchQuery\", \"isSearchLoading\"], [1, \"analytics-section\"], [1, \"analytics-header\"]],\n      template: function LaunchpadHomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"app-sidebar\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 2)(4, \"app-hero\", 3);\n          i0.ɵɵlistener(\"searchResultsChange\", function LaunchpadHomeComponent_Template_app_hero_searchResultsChange_4_listener($event) {\n            return ctx.onSearchResultsChange($event);\n          })(\"searchQueryChange\", function LaunchpadHomeComponent_Template_app_hero_searchQueryChange_4_listener($event) {\n            return ctx.onSearchQueryChange($event);\n          })(\"searchLoadingChange\", function LaunchpadHomeComponent_Template_app_hero_searchLoadingChange_4_listener($event) {\n            return ctx.onSearchLoadingChange($event);\n          })(\"sendClicked\", function LaunchpadHomeComponent_Template_app_hero_sendClicked_4_listener($event) {\n            return ctx.onSendClicked($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 4)(6, \"div\", 5)(7, \"p\");\n          i0.ɵɵtext(8, \" Here's a personalized overview of your day, organized to meet your current goals and responsibilities. \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 6)(10, \"div\", 7);\n          i0.ɵɵelement(11, \"app-studios\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"div\", 9);\n          i0.ɵɵelement(13, \"app-agents\", 10);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(14, \"div\", 11)(15, \"div\", 12)(16, \"h2\");\n          i0.ɵɵtext(17, \"Analytics\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(18, \"app-analytics-card\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"selectedStudioOption\", ctx.selectedStudioOption);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"agents\", ctx.agentsData)(\"showTwoColumns\", true)(\"searchResults\", ctx.searchResults)(\"searchQuery\", ctx.searchQuery)(\"isSearchLoading\", ctx.isSearchLoading);\n        }\n      },\n      dependencies: [AgentsComponent, StudiosComponent, SidebarComponent, Hero, AnalyticsCardComponent],\n      styles: [\".launchpad-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  margin: 0 20px;\\n  position: relative;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.sidebar-section[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 947px;\\n  flex-shrink: 0;\\n}\\n\\n.main-content-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  max-width: 1431px;\\n  height: 947px;\\n  display: flex;\\n  padding: 72px 10px 12px 10px;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  flex-shrink: 0;\\n  border-radius: 24px;\\n  border: 2px solid #FFF;\\n  background: linear-gradient(0deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.2) 100%), linear-gradient(114deg, rgba(240, 235, 248, 0.8) 1.5%, rgba(255, 255, 255, 0.8) 50.17%, rgba(245, 233, 247, 0.8) 98.86%);\\n  box-shadow: 0px 2px 2px -3px #F0F1F2, 0px 0px 6px -2px #D1D3D8;\\n  -webkit-backdrop-filter: blur(8px);\\n          backdrop-filter: blur(8px);\\n}\\n\\n.analytics-section[_ngcontent-%COMP%] {\\n  width: 288px;\\n  height: 947px;\\n  flex-shrink: 0;\\n  display: flex;\\n  padding: 20px 10px 12px 10px;\\n  flex-direction: column;\\n  justify-content: flex-start;\\n  align-items: center;\\n  gap: 24px;\\n  border-radius: 24px;\\n  border: 2px solid #FFF;\\n  background: linear-gradient(0deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.2) 100%), linear-gradient(114deg, rgba(240, 235, 248, 0.8) 1.5%, rgba(255, 255, 255, 0.8) 50.17%, rgba(245, 233, 247, 0.8) 98.86%);\\n  box-shadow: 0px 2px 2px -3px #F0F1F2, 0px 0px 6px -2px #D1D3D8;\\n  -webkit-backdrop-filter: blur(8px);\\n          backdrop-filter: blur(8px);\\n}\\n.analytics-section[_ngcontent-%COMP%]   .analytics-header[_ngcontent-%COMP%] {\\n  width: 100%;\\n  text-align: left;\\n  margin-bottom: 16px;\\n}\\n.analytics-section[_ngcontent-%COMP%]   .analytics-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  color: #000;\\n  font-family: Mulish;\\n  font-size: 20px;\\n  font-weight: 600;\\n  margin: 0;\\n  margin-bottom: 10px;\\n}\\n\\n.content-section[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 24px;\\n}\\n.content-section[_ngcontent-%COMP%]   .section-text[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.content-section[_ngcontent-%COMP%]   .section-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #616874;\\n  font-family: Mulish;\\n  font-size: 20px;\\n  font-weight: 600;\\n  line-height: 24px;\\n  margin: 0;\\n  margin-top: 18px;\\n  width: auto;\\n}\\n.content-section[_ngcontent-%COMP%]   .two-column-layout[_ngcontent-%COMP%] {\\n  display: flex;\\n  width: 100%;\\n  max-width: 1193px;\\n  height: 370px !important;\\n  gap: 8px;\\n}\\n.content-section[_ngcontent-%COMP%]   .two-column-layout[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n  height: 100%;\\n  overflow: hidden;\\n}\\n.content-section[_ngcontent-%COMP%]   .two-column-layout[_ngcontent-%COMP%]   .right-column[_ngcontent-%COMP%] {\\n  flex: 2;\\n  min-width: 0;\\n  height: 100%;\\n  overflow: hidden;\\n}\\n\\n@media (max-width: 1400px) {\\n  .launchpad-container[_ngcontent-%COMP%] {\\n    gap: 24px;\\n    margin: 0 10px;\\n  }\\n  .main-content-section[_ngcontent-%COMP%] {\\n    max-width: 1000px;\\n  }\\n  .analytics-section[_ngcontent-%COMP%] {\\n    width: 250px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .launchpad-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 16px;\\n    align-items: stretch;\\n  }\\n  .sidebar-section[_ngcontent-%COMP%] {\\n    width: 100%;\\n    height: auto;\\n  }\\n  .main-content-section[_ngcontent-%COMP%], \\n   .analytics-section[_ngcontent-%COMP%] {\\n    width: 100%;\\n    height: auto;\\n    min-height: 400px;\\n    padding: 20px 10px;\\n  }\\n  .content-section[_ngcontent-%COMP%]   .two-column-layout[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 24px;\\n    height: auto;\\n  }\\n  .content-section[_ngcontent-%COMP%]   .section-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .launchpad-container[_ngcontent-%COMP%] {\\n    margin: 0 5px;\\n    gap: 12px;\\n  }\\n  .main-content-section[_ngcontent-%COMP%], \\n   .analytics-section[_ngcontent-%COMP%] {\\n    padding: 15px 8px;\\n  }\\n  .content-section[_ngcontent-%COMP%]   .section-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return LaunchpadHomeComponent;\n})();", "map": {"version": 3, "names": ["AgentsComponent", "StudiosComponent", "SidebarComponent", "Hero", "AnalyticsCardComponent", "Subscription", "LaunchpadHomeComponent", "entityService", "globalStoreService", "agentsData", "searchResults", "searchQuery", "isSearchLoading", "selectedStudioOption", "studioSubscription", "constructor", "ngOnInit", "loadAgents", "subscribe", "option", "ngOnDestroy", "unsubscribe", "getAgents", "next", "entityAgents", "map", "entityAgent", "id", "title", "name", "description", "rating", "studio", "type", "backgroundColor", "textColor", "users", "Math", "floor", "random", "error", "console", "onSearchResultsChange", "results", "onSearchQueryChange", "query", "onSearchLoadingChange", "isLoading", "onSendClicked", "i0", "ɵɵdirectiveInject", "i1", "EntityService", "i2", "GlobalStoreService", "selectors", "decls", "vars", "consts", "template", "LaunchpadHomeComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵlistener", "LaunchpadHomeComponent_Template_app_hero_searchResultsChange_4_listener", "$event", "LaunchpadHomeComponent_Template_app_hero_searchQueryChange_4_listener", "LaunchpadHomeComponent_Template_app_hero_searchLoadingChange_4_listener", "LaunchpadHomeComponent_Template_app_hero_sendClicked_4_listener", "ɵɵtext", "ɵɵadvance", "ɵɵproperty", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\pages\\launchpad-home\\launchpad-home.component.ts", "C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\pages\\launchpad-home\\launchpad-home.component.html"], "sourcesContent": ["import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';\r\nimport { AgentsComponent } from '../../shared/components/agents/agents.component';\r\nimport { StudiosComponent } from '../../shared/components/studios/studios.component';\r\nimport { SidebarComponent } from '../../shared/components/sidebar/sidebar.component';\r\nimport Hero from '../../shared/components/hero/hero.component';\r\nimport { AnalyticsCardComponent } from '../../shared/components/analytics-card/analytics-card.component';\r\nimport {\r\n  Agent,\r\n  EntityResult,\r\n} from '../../shared/interfaces/agent-list.interface';\r\nimport { EntityService } from '../../shared/services/entity.service';\r\nimport { RevelioSearchResult } from '../../shared/services/search.service';\r\nimport { GlobalStoreService } from '../../shared/service/global-store.service';\r\nimport { Subscription } from 'rxjs';\r\n\r\n@Component({\r\n  selector: 'app-launchpad-home',\r\n  standalone: true,\r\n  imports: [\r\n    AgentsComponent,\r\n    StudiosComponent,\r\n    Sidebar<PERSON>omponent,\r\n    Hero,\r\n    AnalyticsCardComponent,\r\n  ],\r\n  templateUrl: './launchpad-home.component.html',\r\n  styleUrl: './launchpad-home.component.scss',\r\n})\r\nexport class LaunchpadHomeComponent implements OnInit, OnDestroy {\r\n  agentsData: Agent[] = [];\r\n  searchResults: RevelioSearchResult[] = [];\r\n  searchQuery: string = '';\r\n  isSearchLoading: boolean = false;\r\n  selectedStudioOption: string = 'EE';\r\n\r\n  private studioSubscription: Subscription = new Subscription();\r\n\r\n  constructor(\r\n    private entityService: EntityService,\r\n    private globalStoreService: GlobalStoreService,\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.loadAgents();\r\n\r\n    // Subscribe to studio option changes\r\n    this.studioSubscription =\r\n      this.globalStoreService.selectedStudioOption.subscribe(\r\n        (option: string) => {\r\n          this.selectedStudioOption = option;\r\n        },\r\n      );\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.studioSubscription.unsubscribe();\r\n  }\r\n\r\n  /**\r\n   * Load agents and convert entity data to agent format\r\n   */\r\n  private loadAgents(): void {\r\n    this.entityService.getAgents(0, 50).subscribe({\r\n      next: (entityAgents) => {\r\n        // Convert entity agents to the expected agent format\r\n        this.agentsData = entityAgents.map((entityAgent) => ({\r\n          id: entityAgent.id,\r\n          title: entityAgent.name,\r\n          description: entityAgent.description,\r\n          rating: 4.5, // Default rating since it's not in the API\r\n          studio: {\r\n            name: 'Experience Studio', // Default studio\r\n            type: 'Experience Studio',\r\n            backgroundColor: '#FFF4F9',\r\n            textColor: '#DC047B',\r\n          },\r\n          users: Math.floor(Math.random() * 100) + 10, // Random users count for now\r\n        }));\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading agents:', error);\r\n        // Fallback to empty array\r\n        this.agentsData = [];\r\n      },\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Handle search results from the search bar\r\n   */\r\n  onSearchResultsChange(results: RevelioSearchResult[]): void {\r\n    this.searchResults = results;\r\n  }\r\n\r\n  /**\r\n   * Handle search query change from the search bar\r\n   */\r\n  onSearchQueryChange(query: string): void {\r\n    this.searchQuery = query;\r\n  }\r\n\r\n  /**\r\n   * Handle search loading change from the search bar\r\n   */\r\n  onSearchLoadingChange(isLoading: boolean): void {\r\n    this.isSearchLoading = isLoading;\r\n  }\r\n\r\n  /**\r\n   * Handle send clicked from the search bar\r\n   */\r\n  onSendClicked(query: string): void {}\r\n}\r\n", "<div class=\"launchpad-container\">\r\n  <!-- Sidebar -->\r\n  <div class=\"sidebar-section\">\r\n    <app-sidebar></app-sidebar>\r\n  </div>\r\n\r\n  <!-- Main Content -->\r\n  <div class=\"main-content-section\">\r\n    <app-hero\r\n      (searchResultsChange)=\"onSearchResultsChange($event)\"\r\n      (searchQueryChange)=\"onSearchQueryChange($event)\"\r\n      (searchLoadingChange)=\"onSearchLoadingChange($event)\"\r\n      (sendClicked)=\"onSendClicked($event)\"\r\n    >\r\n    </app-hero>\r\n\r\n    <div class=\"content-section\">\r\n      <div class=\"section-text\">\r\n        <p>\r\n          Here's a personalized overview of your day, organized to meet your\r\n          current goals and responsibilities.\r\n        </p>\r\n      </div>\r\n\r\n      <div class=\"two-column-layout\">\r\n        <!-- Left column: Studios -->\r\n        <div class=\"left-column\">\r\n          <app-studios\r\n            [selectedStudioOption]=\"selectedStudioOption\"\r\n          ></app-studios>\r\n        </div>\r\n\r\n        <div class=\"right-column\">\r\n          <app-agents\r\n            [agents]=\"agentsData\"\r\n            [showTwoColumns]=\"true\"\r\n            [searchResults]=\"searchResults\"\r\n            [searchQuery]=\"searchQuery\"\r\n            [isSearchLoading]=\"isSearchLoading\"\r\n          >\r\n          </app-agents>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Analytics Section -->\r\n  <div class=\"analytics-section\">\r\n    <div class=\"analytics-header\">\r\n      <h2>Analytics</h2>\r\n      <app-analytics-card></app-analytics-card>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAASA,eAAe,QAAQ,iDAAiD;AACjF,SAASC,gBAAgB,QAAQ,mDAAmD;AACpF,SAASC,gBAAgB,QAAQ,mDAAmD;AACpF,OAAOC,IAAI,MAAM,6CAA6C;AAC9D,SAASC,sBAAsB,QAAQ,iEAAiE;AAQxG,SAASC,YAAY,QAAQ,MAAM;;;;AAenC,WAAaC,sBAAsB;EAA7B,MAAOA,sBAAsB;IAUvBC,aAAA;IACAC,kBAAA;IAVVC,UAAU,GAAY,EAAE;IACxBC,aAAa,GAA0B,EAAE;IACzCC,WAAW,GAAW,EAAE;IACxBC,eAAe,GAAY,KAAK;IAChCC,oBAAoB,GAAW,IAAI;IAE3BC,kBAAkB,GAAiB,IAAIT,YAAY,EAAE;IAE7DU,YACUR,aAA4B,EAC5BC,kBAAsC;MADtC,KAAAD,aAAa,GAAbA,aAAa;MACb,KAAAC,kBAAkB,GAAlBA,kBAAkB;IACzB;IAEHQ,QAAQA,CAAA;MACN,IAAI,CAACC,UAAU,EAAE;MAEjB;MACA,IAAI,CAACH,kBAAkB,GACrB,IAAI,CAACN,kBAAkB,CAACK,oBAAoB,CAACK,SAAS,CACnDC,MAAc,IAAI;QACjB,IAAI,CAACN,oBAAoB,GAAGM,MAAM;MACpC,CAAC,CACF;IACL;IAEAC,WAAWA,CAAA;MACT,IAAI,CAACN,kBAAkB,CAACO,WAAW,EAAE;IACvC;IAEA;;;IAGQJ,UAAUA,CAAA;MAChB,IAAI,CAACV,aAAa,CAACe,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAACJ,SAAS,CAAC;QAC5CK,IAAI,EAAGC,YAAY,IAAI;UACrB;UACA,IAAI,CAACf,UAAU,GAAGe,YAAY,CAACC,GAAG,CAAEC,WAAW,KAAM;YACnDC,EAAE,EAAED,WAAW,CAACC,EAAE;YAClBC,KAAK,EAAEF,WAAW,CAACG,IAAI;YACvBC,WAAW,EAAEJ,WAAW,CAACI,WAAW;YACpCC,MAAM,EAAE,GAAG;YAAE;YACbC,MAAM,EAAE;cACNH,IAAI,EAAE,mBAAmB;cAAE;cAC3BI,IAAI,EAAE,mBAAmB;cACzBC,eAAe,EAAE,SAAS;cAC1BC,SAAS,EAAE;aACZ;YACDC,KAAK,EAAEC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,EAAE,CAAE;WAC9C,CAAC,CAAC;QACL,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;UAC7C;UACA,IAAI,CAAC/B,UAAU,GAAG,EAAE;QACtB;OACD,CAAC;IACJ;IAEA;;;IAGAiC,qBAAqBA,CAACC,OAA8B;MAClD,IAAI,CAACjC,aAAa,GAAGiC,OAAO;IAC9B;IAEA;;;IAGAC,mBAAmBA,CAACC,KAAa;MAC/B,IAAI,CAAClC,WAAW,GAAGkC,KAAK;IAC1B;IAEA;;;IAGAC,qBAAqBA,CAACC,SAAkB;MACtC,IAAI,CAACnC,eAAe,GAAGmC,SAAS;IAClC;IAEA;;;IAGAC,aAAaA,CAACH,KAAa,GAAS;;uCAnFzBvC,sBAAsB,EAAA2C,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,kBAAA;IAAA;;YAAtBhD,sBAAsB;MAAAiD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC1BjCZ,EAFF,CAAAc,cAAA,aAAiC,aAEF;UAC3Bd,EAAA,CAAAe,SAAA,kBAA2B;UAC7Bf,EAAA,CAAAgB,YAAA,EAAM;UAIJhB,EADF,CAAAc,cAAA,aAAkC,kBAM/B;UADCd,EAHA,CAAAiB,UAAA,iCAAAC,wEAAAC,MAAA;YAAA,OAAuBN,GAAA,CAAApB,qBAAA,CAAA0B,MAAA,CAA6B;UAAA,EAAC,+BAAAC,sEAAAD,MAAA;YAAA,OAChCN,GAAA,CAAAlB,mBAAA,CAAAwB,MAAA,CAA2B;UAAA,EAAC,iCAAAE,wEAAAF,MAAA;YAAA,OAC1BN,GAAA,CAAAhB,qBAAA,CAAAsB,MAAA,CAA6B;UAAA,EAAC,yBAAAG,gEAAAH,MAAA;YAAA,OACtCN,GAAA,CAAAd,aAAA,CAAAoB,MAAA,CAAqB;UAAA,EAAC;UAEvCnB,EAAA,CAAAgB,YAAA,EAAW;UAIPhB,EAFJ,CAAAc,cAAA,aAA6B,aACD,QACrB;UACDd,EAAA,CAAAuB,MAAA,+GAEF;UACFvB,EADE,CAAAgB,YAAA,EAAI,EACA;UAIJhB,EAFF,CAAAc,cAAA,aAA+B,cAEJ;UACvBd,EAAA,CAAAe,SAAA,sBAEe;UACjBf,EAAA,CAAAgB,YAAA,EAAM;UAENhB,EAAA,CAAAc,cAAA,cAA0B;UACxBd,EAAA,CAAAe,SAAA,sBAOa;UAIrBf,EAHM,CAAAgB,YAAA,EAAM,EACF,EACF,EACF;UAKFhB,EAFJ,CAAAc,cAAA,eAA+B,eACC,UACxB;UAAAd,EAAA,CAAAuB,MAAA,iBAAS;UAAAvB,EAAA,CAAAgB,YAAA,EAAK;UAClBhB,EAAA,CAAAe,SAAA,0BAAyC;UAG/Cf,EAFI,CAAAgB,YAAA,EAAM,EACF,EACF;;;UAzBMhB,EAAA,CAAAwB,SAAA,IAA6C;UAA7CxB,EAAA,CAAAyB,UAAA,yBAAAZ,GAAA,CAAAjD,oBAAA,CAA6C;UAM7CoC,EAAA,CAAAwB,SAAA,GAAqB;UAIrBxB,EAJA,CAAAyB,UAAA,WAAAZ,GAAA,CAAArD,UAAA,CAAqB,wBACE,kBAAAqD,GAAA,CAAApD,aAAA,CACQ,gBAAAoD,GAAA,CAAAnD,WAAA,CACJ,oBAAAmD,GAAA,CAAAlD,eAAA,CACQ;;;qBDnB3CZ,eAAe,EACfC,gBAAgB,EAChBC,gBAAgB,EAChBC,IAAI,EACJC,sBAAsB;MAAAuE,MAAA;IAAA;;SAKbrE,sBAAsB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}