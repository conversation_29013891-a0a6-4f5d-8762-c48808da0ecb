{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { Subject, takeUntil, switchMap, finalize, catchError, of } from 'rxjs';\nimport { FormsModule } from '@angular/forms';\n// Import child components\nimport { AgentExecutionPlaygroundComponent } from './components/agent-execution-playground/agent-execution-playground.component';\nimport { IconComponent } from '@ava/play-comp-library';\nimport { environment } from '@shared/environments/environment';\nimport { ExecutionStatus } from '@shared/models/execution.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../services/agent-service.service\";\nimport * as i3 from \"../build-agents/services/agent-playground.service\";\nimport * as i4 from \"@shared/auth/services/token-storage.service\";\nimport * as i5 from \"@shared/services/loader/loader.service\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"@shared/services/tool-execution/tool-execution.service\";\nimport * as i8 from \"@angular/common\";\nfunction AgentExecutionComponent_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵtext(1, \" History \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"app-agent-execution-playground\", 22);\n    i0.ɵɵlistener(\"promptChange\", function AgentExecutionComponent_div_13_Template_app_agent_execution_playground_promptChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPromptChanged($event));\n    })(\"messageSent\", function AgentExecutionComponent_div_13_Template_app_agent_execution_playground_messageSent_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.handleChatMessage($event));\n    })(\"conversationalToggle\", function AgentExecutionComponent_div_13_Template_app_agent_execution_playground_conversationalToggle_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPlaygroundConversationalToggle($event));\n    })(\"templateToggle\", function AgentExecutionComponent_div_13_Template_app_agent_execution_playground_templateToggle_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPlaygroundTemplateToggle($event));\n    })(\"filesSelected\", function AgentExecutionComponent_div_13_Template_app_agent_execution_playground_filesSelected_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFilesSelected($event));\n    })(\"approvalRequested\", function AgentExecutionComponent_div_13_Template_app_agent_execution_playground_approvalRequested_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onApprovalRequested());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"messages\", ctx_r1.chatMessages)(\"isLoading\", ctx_r1.isProcessingChat)(\"agentType\", ctx_r1.agentType)(\"showChatInteractionToggles\", ctx_r1.agentType === \"individual\")(\"showAiPrincipleToggle\", true)(\"showApprovalButton\", false)(\"showDropdown\", false)(\"showAgentNameInput\", true)(\"showFileUploadButton\", true)(\"displayedAgentName\", ctx_r1.agentName)(\"agentNamePlaceholder\", \"Current Agent Name\")(\"acceptedFileType\", ctx_r1.fileType);\n  }\n}\nfunction AgentExecutionComponent_div_22_div_40_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77);\n    i0.ɵɵtext(1, \" No prompt configured \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_22_div_40_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"span\", 79);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(node_r4.name || \"Prompt\");\n  }\n}\nfunction AgentExecutionComponent_div_22_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_22_div_40_div_1_Template, 2, 0, \"div\", 74);\n    i0.ɵɵelementStart(2, \"div\", 75);\n    i0.ɵɵtemplate(3, AgentExecutionComponent_div_22_div_40_div_3_Template, 3, 1, \"div\", 76);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.blueprintPromptNodes.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.blueprintPromptNodes);\n  }\n}\nfunction AgentExecutionComponent_div_22_div_61_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77);\n    i0.ɵɵtext(1, \" No knowledge base configured \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_22_div_61_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"span\", 79);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(node_r5.name || \"Knowledge Base\");\n  }\n}\nfunction AgentExecutionComponent_div_22_div_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_22_div_61_div_1_Template, 2, 0, \"div\", 74);\n    i0.ɵɵelementStart(2, \"div\", 75);\n    i0.ɵɵtemplate(3, AgentExecutionComponent_div_22_div_61_div_3_Template, 3, 1, \"div\", 76);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.blueprintKnowledgeNodes.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.blueprintKnowledgeNodes);\n  }\n}\nfunction AgentExecutionComponent_div_22_div_79_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77);\n    i0.ɵɵtext(1, \" No model configured \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_22_div_79_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"span\", 79);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(node_r6.name || \"Model\");\n  }\n}\nfunction AgentExecutionComponent_div_22_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_22_div_79_div_1_Template, 2, 0, \"div\", 74);\n    i0.ɵɵelementStart(2, \"div\", 75);\n    i0.ɵɵtemplate(3, AgentExecutionComponent_div_22_div_79_div_3_Template, 3, 1, \"div\", 76);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.blueprintModelNodes.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.blueprintModelNodes);\n  }\n}\nfunction AgentExecutionComponent_div_22_div_80__svg_svg_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 47);\n    i0.ɵɵelement(1, \"rect\", 82)(2, \"path\", 83);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_22_div_80_div_13_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77);\n    i0.ɵɵtext(1, \" No tools configured \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_22_div_80_div_13_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"span\", 79);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r8 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(node_r8.name || \"Tool\");\n  }\n}\nfunction AgentExecutionComponent_div_22_div_80_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_22_div_80_div_13_div_1_Template, 2, 0, \"div\", 74);\n    i0.ɵɵelementStart(2, \"div\", 75);\n    i0.ɵɵtemplate(3, AgentExecutionComponent_div_22_div_80_div_13_div_3_Template, 3, 1, \"div\", 76);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.blueprintToolNodes.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.blueprintToolNodes);\n  }\n}\nfunction AgentExecutionComponent_div_22_div_80_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 80)(1, \"div\", 44);\n    i0.ɵɵlistener(\"click\", function AgentExecutionComponent_div_22_div_80_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleBlueprintZone(\"tool\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 45)(3, \"div\", 46);\n    i0.ɵɵtemplate(4, AgentExecutionComponent_div_22_div_80__svg_svg_4_Template, 3, 0, \"svg\", 81);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h3\", 50);\n    i0.ɵɵtext(6, \"Tools\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 51)(8, \"span\", 65);\n    i0.ɵɵtext(9, \"Optional\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 53);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(11, \"svg\", 54);\n    i0.ɵɵelement(12, \"path\", 55);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(13, AgentExecutionComponent_div_22_div_80_div_13_Template, 4, 2, \"div\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"has-nodes\", ctx_r1.blueprintToolNodes.length > 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.agentType === \"collaborative\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵstyleProp(\"transform\", ctx_r1.isBlueprintZoneExpanded(\"tool\") ? \"rotate(180deg)\" : \"rotate(0deg)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isBlueprintZoneExpanded(\"tool\"));\n  }\n}\nfunction AgentExecutionComponent_div_22_div_81_div_15_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77);\n    i0.ɵɵtext(1, \" No guardrails configured \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_22_div_81_div_15_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"span\", 79);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r10 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(node_r10.name || \"Guardrail\");\n  }\n}\nfunction AgentExecutionComponent_div_22_div_81_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_22_div_81_div_15_div_1_Template, 2, 0, \"div\", 74);\n    i0.ɵɵelementStart(2, \"div\", 75);\n    i0.ɵɵtemplate(3, AgentExecutionComponent_div_22_div_81_div_15_div_3_Template, 3, 1, \"div\", 76);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.blueprintGuardrailNodes.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.blueprintGuardrailNodes);\n  }\n}\nfunction AgentExecutionComponent_div_22_div_81_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 84)(1, \"div\", 44);\n    i0.ɵɵlistener(\"click\", function AgentExecutionComponent_div_22_div_81_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleBlueprintZone(\"guardrail\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 45)(3, \"div\", 46);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(4, \"svg\", 47);\n    i0.ɵɵelement(5, \"rect\", 85)(6, \"path\", 86);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(7, \"h3\", 50);\n    i0.ɵɵtext(8, \"Guardrails\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 51)(10, \"span\", 65);\n    i0.ɵɵtext(11, \"Optional\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 53);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(13, \"svg\", 54);\n    i0.ɵɵelement(14, \"path\", 55);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(15, AgentExecutionComponent_div_22_div_81_div_15_Template, 4, 2, \"div\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"has-nodes\", ctx_r1.blueprintGuardrailNodes.length > 0);\n    i0.ɵɵadvance(13);\n    i0.ɵɵstyleProp(\"transform\", ctx_r1.isBlueprintZoneExpanded(\"guardrail\") ? \"rotate(180deg)\" : \"rotate(0deg)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isBlueprintZoneExpanded(\"guardrail\"));\n  }\n}\nfunction AgentExecutionComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 24)(2, \"h3\");\n    i0.ɵɵtext(3, \"Agent Blueprint\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 25);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(5, \"svg\", 26);\n    i0.ɵɵelement(6, \"line\", 27)(7, \"line\", 28)(8, \"line\", 29)(9, \"line\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(10, \"div\", 31)(11, \"div\", 32);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(12, \"svg\", 33)(13, \"defs\")(14, \"linearGradient\", 34);\n    i0.ɵɵelement(15, \"stop\", 35)(16, \"stop\", 36);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(17, \"circle\", 37)(18, \"circle\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(19, \"div\", 39)(20, \"div\", 40);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 41);\n    i0.ɵɵtext(23, \"Complete\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(24, \"div\", 42)(25, \"div\", 43)(26, \"div\", 44);\n    i0.ɵɵlistener(\"click\", function AgentExecutionComponent_div_22_Template_div_click_26_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleBlueprintZone(\"prompt\"));\n    });\n    i0.ɵɵelementStart(27, \"div\", 45)(28, \"div\", 46);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(29, \"svg\", 47);\n    i0.ɵɵelement(30, \"rect\", 48)(31, \"path\", 49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(32, \"h3\", 50);\n    i0.ɵɵtext(33, \"System Prompt\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 51)(35, \"span\", 52);\n    i0.ɵɵtext(36, \"Required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"button\", 53);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(38, \"svg\", 54);\n    i0.ɵɵelement(39, \"path\", 55);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(40, AgentExecutionComponent_div_22_div_40_Template, 4, 2, \"div\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(41, \"div\", 57)(42, \"div\", 44);\n    i0.ɵɵlistener(\"click\", function AgentExecutionComponent_div_22_Template_div_click_42_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleBlueprintZone(\"knowledge\"));\n    });\n    i0.ɵɵelementStart(43, \"div\", 45)(44, \"div\", 46);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(45, \"svg\", 47);\n    i0.ɵɵelement(46, \"rect\", 58)(47, \"path\", 59)(48, \"path\", 60)(49, \"path\", 61)(50, \"path\", 62)(51, \"path\", 63)(52, \"path\", 64);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(53, \"h3\", 50);\n    i0.ɵɵtext(54, \"Knowledgebase\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(55, \"div\", 51)(56, \"span\", 65);\n    i0.ɵɵtext(57, \"Optional\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"button\", 53);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(59, \"svg\", 54);\n    i0.ɵɵelement(60, \"path\", 55);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(61, AgentExecutionComponent_div_22_div_61_Template, 4, 2, \"div\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(62, \"div\", 66)(63, \"div\", 44);\n    i0.ɵɵlistener(\"click\", function AgentExecutionComponent_div_22_Template_div_click_63_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleBlueprintZone(\"model\"));\n    });\n    i0.ɵɵelementStart(64, \"div\", 45)(65, \"div\", 46);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(66, \"svg\", 47);\n    i0.ɵɵelement(67, \"rect\", 67)(68, \"path\", 68)(69, \"path\", 69)(70, \"path\", 70);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(71, \"h3\", 50);\n    i0.ɵɵtext(72, \"AI Model\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(73, \"div\", 51)(74, \"span\", 52);\n    i0.ɵɵtext(75, \"Required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(76, \"button\", 53);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(77, \"svg\", 54);\n    i0.ɵɵelement(78, \"path\", 55);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(79, AgentExecutionComponent_div_22_div_79_Template, 4, 2, \"div\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(80, AgentExecutionComponent_div_22_div_80_Template, 14, 6, \"div\", 71)(81, AgentExecutionComponent_div_22_div_81_Template, 16, 5, \"div\", 72);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(18);\n    i0.ɵɵstyleProp(\"stroke-dasharray\", 314)(\"stroke-dashoffset\", 314 - 314 * ctx_r1.blueprintCompletionPercentage / 100);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.blueprintCompletionPercentage, \"% \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"has-nodes\", ctx_r1.blueprintPromptNodes.length > 0);\n    i0.ɵɵadvance(13);\n    i0.ɵɵstyleProp(\"transform\", ctx_r1.isBlueprintZoneExpanded(\"prompt\") ? \"rotate(180deg)\" : \"rotate(0deg)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isBlueprintZoneExpanded(\"prompt\"));\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"has-nodes\", ctx_r1.blueprintKnowledgeNodes.length > 0);\n    i0.ɵɵadvance(18);\n    i0.ɵɵstyleProp(\"transform\", ctx_r1.isBlueprintZoneExpanded(\"knowledge\") ? \"rotate(180deg)\" : \"rotate(0deg)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isBlueprintZoneExpanded(\"knowledge\"));\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"has-nodes\", ctx_r1.blueprintModelNodes.length > 0);\n    i0.ɵɵadvance(15);\n    i0.ɵɵstyleProp(\"transform\", ctx_r1.isBlueprintZoneExpanded(\"model\") ? \"rotate(180deg)\" : \"rotate(0deg)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isBlueprintZoneExpanded(\"model\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.agentType === \"collaborative\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.agentType === \"individual\");\n  }\n}\nfunction AgentExecutionComponent_div_23_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 92)(1, \"div\", 93)(2, \"h4\");\n    i0.ɵɵtext(3, \"Response\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 94);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.latestAgentResponse.response.choices[0].text, \" \");\n  }\n}\nfunction AgentExecutionComponent_div_23_div_1_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \" No agent output available yet. Send a message to see the response. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_23_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 90)(1, \"h3\");\n    i0.ɵɵtext(2, \"Agent Output\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, AgentExecutionComponent_div_23_div_1_div_3_Template, 6, 1, \"div\", 91)(4, AgentExecutionComponent_div_23_div_1_ng_template_4_Template, 2, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const noIndividualOutput_r11 = i0.ɵɵreference(5);\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.latestAgentResponse == null ? null : ctx_r1.latestAgentResponse.response == null ? null : ctx_r1.latestAgentResponse.response.choices == null ? null : ctx_r1.latestAgentResponse.response.choices.length) > 0)(\"ngIfElse\", noIndividualOutput_r11);\n  }\n}\nfunction AgentExecutionComponent_div_23_div_2_div_3_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 100)(1, \"strong\");\n    i0.ɵɵtext(2, \"Description:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const taskOutput_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", taskOutput_r12.description, \" \");\n  }\n}\nfunction AgentExecutionComponent_div_23_div_2_div_3_div_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 101)(1, \"strong\");\n    i0.ɵɵtext(2, \"Expected Output:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const taskOutput_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", taskOutput_r12.expected_output, \" \");\n  }\n}\nfunction AgentExecutionComponent_div_23_div_2_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 92)(1, \"div\", 93)(2, \"h4\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, AgentExecutionComponent_div_23_div_2_div_3_div_1_div_4_Template, 4, 1, \"div\", 98)(5, AgentExecutionComponent_div_23_div_2_div_3_div_1_div_5_Template, 4, 1, \"div\", 99);\n    i0.ɵɵelementStart(6, \"div\", 94);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const taskOutput_r12 = ctx.$implicit;\n    const i_r13 = ctx.index;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(taskOutput_r12.summary || \"Task \" + (i_r13 + 1));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", taskOutput_r12.description);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", taskOutput_r12.expected_output);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", taskOutput_r12.raw, \" \");\n  }\n}\nfunction AgentExecutionComponent_div_23_div_2_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_23_div_2_div_3_div_1_Template, 8, 4, \"div\", 97);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.latestAgentResponse.agentResponse.agent.tasksOutputs);\n  }\n}\nfunction AgentExecutionComponent_div_23_div_2_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \" No task outputs available yet. Send a message to see the results. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_23_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 95)(1, \"h3\");\n    i0.ɵɵtext(2, \"Task Outputs\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, AgentExecutionComponent_div_23_div_2_div_3_Template, 2, 1, \"div\", 96)(4, AgentExecutionComponent_div_23_div_2_ng_template_4_Template, 2, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const noCollaborativeOutput_r14 = i0.ɵɵreference(5);\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.latestAgentResponse == null ? null : ctx_r1.latestAgentResponse.agentResponse == null ? null : ctx_r1.latestAgentResponse.agentResponse.agent == null ? null : ctx_r1.latestAgentResponse.agentResponse.agent.tasksOutputs == null ? null : ctx_r1.latestAgentResponse.agentResponse.agent.tasksOutputs.length) > 0)(\"ngIfElse\", noCollaborativeOutput_r14);\n  }\n}\nfunction AgentExecutionComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 87);\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_23_div_1_Template, 6, 2, \"div\", 88)(2, AgentExecutionComponent_div_23_div_2_Template, 6, 2, \"div\", 89);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.agentType === \"individual\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.agentType === \"collaborative\");\n  }\n}\n// Remove duplicate definitions - they're now in shared models\nexport let AgentExecutionComponent = /*#__PURE__*/(() => {\n  class AgentExecutionComponent {\n    route;\n    router;\n    agentService;\n    agentPlaygroundService;\n    tokenStorage;\n    loaderService;\n    formBuilder;\n    toolExecutionService;\n    navigationTabs = [{\n      id: 'nav-home',\n      label: 'Agent Activity'\n    }, {\n      id: 'nav-products',\n      label: 'Agent Output'\n    }, {\n      id: 'nav-services',\n      label: 'Preview',\n      disabled: true\n    }];\n    // Agent details\n    agentId = null;\n    agentType = 'individual';\n    agentName = 'Agent';\n    agentDetail = '';\n    playgroundComp;\n    // Activity logs\n    activityLogs = [];\n    activityProgress = 0;\n    executionDetails;\n    isRunning = false;\n    status = ExecutionStatus.notStarted;\n    // Chat messages\n    isProcessingChat = false;\n    inputText = '';\n    // Enhanced chat interface properties\n    chatHistory = []; // Store all chat interactions with status\n    // Agent outputs\n    agentOutputs = [];\n    latestAgentResponse = null; // Store the latest agent response for display\n    agentForm;\n    fileType = '.pdf,.doc,.docx,.txt,.csv,.xlsx,.xls,.ppt,.pptx,.png,.jpg,.jpeg,.gif,.bmp,.svg';\n    // Execution state\n    executionStartTime = null;\n    executionCompleted = false;\n    executionId;\n    enableStreamingLog = environment.enableLogStreaming || 'all';\n    isExecutionComplete = false;\n    progressInterval;\n    destroy$ = new Subject();\n    selectedTab = 'Agent Activity';\n    demoTabs = [{\n      id: 'tab1',\n      label: 'History'\n    }, {\n      id: 'tab2',\n      label: 'Blueprint'\n    }, {\n      id: 'tab3',\n      label: 'Agent Output'\n    }];\n    errorMsg = false;\n    resMessage;\n    taskMessage = [];\n    isJsonValid = false;\n    disableChat = false;\n    selectedFiles = [];\n    agentNodes = [];\n    userInputList = [];\n    progress = 0;\n    isLoading = false;\n    loaderColor = '';\n    inputFieldOrder = [];\n    currentInputIndex = 0;\n    activeTabId = 'nav-home';\n    // Panel state properties\n    isLeftPanelCollapsed = false;\n    activeRightTab = 'blueprint';\n    // Agent-specific properties\n    currentAgentDetails = null;\n    buildAgentNodes = [];\n    canvasNodes = [];\n    canvasEdges = [];\n    selectedPrompt = '';\n    selectedAgentMode = '';\n    selectedUseCaseIdentifier = '';\n    agentFilesUploadedData = [];\n    agentAttachment = [];\n    isAgentPlaygroundLoading = false;\n    agentPlaygroundDestroy = new Subject();\n    agentChatPayload = [];\n    agentCode = '';\n    promptOptions = [];\n    // Custom Blueprint Display Properties\n    blueprintCompletionPercentage = 0;\n    blueprintPromptNodes = [];\n    blueprintModelNodes = [];\n    blueprintKnowledgeNodes = [];\n    blueprintGuardrailNodes = [];\n    blueprintToolNodes = [];\n    // Blueprint zone expansion state\n    blueprintZonesExpanded = {\n      prompt: true,\n      model: true,\n      knowledge: true,\n      guardrail: true,\n      tool: true\n    };\n    // Blueprint panel properties (using existing arrays above)\n    constructor(route, router, agentService, agentPlaygroundService, tokenStorage, loaderService, formBuilder, toolExecutionService) {\n      this.route = route;\n      this.router = router;\n      this.agentService = agentService;\n      this.agentPlaygroundService = agentPlaygroundService;\n      this.tokenStorage = tokenStorage;\n      this.loaderService = loaderService;\n      this.formBuilder = formBuilder;\n      this.toolExecutionService = toolExecutionService;\n      this.agentForm = this.formBuilder.group({\n        isConversational: [true],\n        isUseTemplate: [false]\n      });\n    }\n    ngOnInit() {\n      console.log(' SHARED COMPONENT INITIALIZED! ');\n      this.executionId = crypto.randomUUID();\n      this.route.params.subscribe(params => {\n        this.agentType = params['type'] || 'individual';\n        console.log(' SHARED: Agent type set to:', this.agentType);\n      });\n      this.route.queryParams.subscribe(params => {\n        if (params['id']) {\n          this.agentId = params['id'];\n          this.loadAgentData(params['id']);\n        }\n      });\n      // Initialize chat messages\n      this.chatHistory = [{\n        user: '',\n        ai: {\n          from: 'ai',\n          text: `Hi there! I am ${this.agentName || 'your agent'}. How can I help you today?`\n        }\n      }];\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n      if (this.progressInterval) {\n        clearInterval(this.progressInterval);\n      }\n    }\n    onTabChange(event) {\n      this.activeTabId = event.id;\n      this.selectedTab = event.label;\n    }\n    loadAgentData(agentId) {\n      this.isLoading = true;\n      // Load agent data based on type\n      if (this.agentType === 'collaborative') {\n        this.agentService.getCollaborativeAgentDetailsById(agentId).subscribe({\n          next: response => {\n            this.handleAgentDataResponse(response);\n          },\n          error: error => {\n            console.error('Error loading collaborative agent:', error);\n            this.isLoading = false;\n          }\n        });\n      } else {\n        this.agentService.getAgentById(agentId).subscribe({\n          next: response => {\n            this.handleAgentDataResponse(response);\n          },\n          error: error => {\n            console.error('Error loading individual agent:', error);\n            this.isLoading = false;\n          }\n        });\n      }\n    }\n    handleAgentDataResponse(response) {\n      this.isLoading = false;\n      // Extract agent details\n      let agentData;\n      if (response.agentDetails && Array.isArray(response.agentDetails) && response.agentDetails.length > 0) {\n        agentData = response.agentDetails[0];\n      } else if (response.agentDetail) {\n        agentData = response.agentDetail;\n      } else if (response.data) {\n        agentData = response.data;\n      } else {\n        agentData = response;\n      }\n      if (agentData) {\n        this.currentAgentDetails = agentData;\n        this.agentName = agentData.name || agentData.agentName || 'Agent';\n        this.agentDetail = agentData.description || agentData.agentDetail || '';\n        // For individual agents, set up the required properties for playground functionality\n        if (this.agentType === 'individual') {\n          // Set selectedPrompt to agent name or useCaseName to bypass dropdown requirement\n          this.selectedPrompt = agentData.useCaseName || agentData.name || 'loaded-agent';\n          // Set selectedAgentMode for API calls - use useCaseCode if available\n          this.selectedAgentMode = agentData.useCaseCode || agentData.useCaseName || agentData.name || '';\n          // Set useCaseIdentifier - use organizationPath if available\n          if (agentData.organizationPath) {\n            this.selectedUseCaseIdentifier = agentData.organizationPath;\n          } else if (agentData.useCaseCode) {\n            this.selectedUseCaseIdentifier = agentData.useCaseCode;\n          } else if (agentData.useCaseName) {\n            this.selectedUseCaseIdentifier = agentData.useCaseName;\n          }\n        }\n        // Update chat message with agent name\n        if (this.chatHistory.length > 0) {\n          this.chatHistory[0].ai.text = `Hi there! I am ${this.agentName}. How can I help you today?`;\n        }\n        // Load agent nodes and configuration\n        this.loadAgentNodes(agentData);\n      }\n    }\n    loadAgentNodes(agentData) {\n      // Map agent configuration to blueprint panel\n      this.mapAgentConfigurationToBlueprint(agentData);\n    }\n    handleChatMessage(message) {\n      if (!message.trim() || this.isProcessingChat) return;\n      this.isProcessingChat = true;\n      this.activeRightTab = 'output'; // Switch to output tab\n      // Add user message and AI loading state to chat history\n      this.chatHistory.push({\n        user: message,\n        ai: {\n          from: 'ai',\n          text: '...',\n          isLoading: true,\n          status: 'loading',\n          timestamp: new Date()\n        }\n      });\n      // Determine agent type and call the appropriate API\n      if (this.agentType === 'collaborative') {\n        this.executeCollaborativeAgent(message);\n      } else {\n        this.executeIndividualAgent(message);\n      }\n    }\n    executeIndividualAgent(message) {\n      if (!this.currentAgentDetails && (!this.selectedPrompt || this.selectedPrompt === 'default')) {\n        this.showAgentError('Agent details are not loaded. Please try refreshing the page.');\n        this.isProcessingChat = false;\n        return;\n      }\n      const isConversational = this.agentForm.get('isConversational')?.value || false;\n      const isUseTemplate = this.agentForm.get('isUseTemplate')?.value || false;\n      const agentMode = this.agentCode || this.selectedAgentMode || this.currentAgentDetails?.useCaseCode || this.currentAgentDetails?.useCaseName || this.currentAgentDetails?.name || this.selectedPrompt;\n      let useCaseIdentifier = this.selectedUseCaseIdentifier;\n      if (!useCaseIdentifier) {\n        if (this.currentAgentDetails?.organizationPath) {\n          useCaseIdentifier = this.currentAgentDetails.organizationPath;\n        } else {\n          const orgPath = this.buildOrganizationPath();\n          const agentIdentifier = this.currentAgentDetails?.useCaseCode || this.currentAgentDetails?.useCaseName || this.currentAgentDetails?.name || agentMode;\n          useCaseIdentifier = `${agentIdentifier}${orgPath}`;\n        }\n      }\n      if (this.agentFilesUploadedData.length > 0) {\n        this.processAgentFilesAndSendMessage(message, agentMode, useCaseIdentifier, isConversational, isUseTemplate);\n        return;\n      }\n      this.sendAgentMessageToAPI(message, agentMode, useCaseIdentifier, isConversational, isUseTemplate);\n    }\n    executeCollaborativeAgent(message) {\n      const payload = {\n        executionId: this.executionId,\n        agentId: Number(this.agentId),\n        user: this.tokenStorage.getDaUsername() || '<EMAIL>',\n        userInputs: {\n          question: message\n        }\n      };\n      if (this.agentFilesUploadedData.length > 0) {\n        const fileWrapper = this.agentFilesUploadedData[0];\n        this.agentPlaygroundService.submitAgentExecuteWithFile(payload, fileWrapper).pipe(finalize(() => {\n          this.isProcessingChat = false;\n        }), takeUntil(this.agentPlaygroundDestroy)).subscribe({\n          next: res => this.handleAgentExecuteResponse(res),\n          error: err => this.handleAgentExecuteError(err)\n        });\n      } else {\n        this.agentPlaygroundService.submitAgentExecute(payload).pipe(finalize(() => {\n          this.isProcessingChat = false;\n        }), takeUntil(this.agentPlaygroundDestroy)).subscribe({\n          next: res => this.handleAgentExecuteResponse(res),\n          error: err => this.handleAgentExecuteError(err)\n        });\n      }\n    }\n    handleAgentExecuteResponse(response) {\n      this.isProcessingChat = false;\n      this.latestAgentResponse = response;\n      const agentResponseText = response?.agentResponse?.agent?.output || response?.agentResponse?.agent?.response || response?.agentResponse?.detail || 'No response found.';\n      const lastInteraction = this.chatHistory[this.chatHistory.length - 1];\n      if (lastInteraction && lastInteraction.ai.isLoading) {\n        lastInteraction.ai.text = agentResponseText.replace(/\\\\n/g, '\\n');\n        lastInteraction.ai.isLoading = false;\n        lastInteraction.ai.status = 'success';\n        lastInteraction.ai.timestamp = new Date();\n      }\n    }\n    handleAgentExecuteError(err) {\n      this.isProcessingChat = false;\n      const errorResponse = err?.error?.message || err?.message || 'An unexpected error occurred.';\n      const lastInteraction = this.chatHistory[this.chatHistory.length - 1];\n      if (lastInteraction && lastInteraction.ai.isLoading) {\n        lastInteraction.ai.text = errorResponse;\n        lastInteraction.ai.isLoading = false;\n        lastInteraction.ai.status = 'failed';\n        lastInteraction.ai.timestamp = new Date();\n      }\n    }\n    onPromptChanged(prompt) {\n      this.inputText = prompt.name || String(prompt.value) || '';\n    }\n    onPlaygroundConversationalToggle(value) {\n      // Update the form control\n      this.agentForm.get('isConversational')?.setValue(value);\n      // When conversational mode is turned off, clear the conversation history\n      // This ensures that the next message will be treated as a fresh start\n      if (!value) {\n        this.agentChatPayload = [];\n        console.log('Conversational mode disabled - cleared chat payload history');\n      } else {\n        console.log('Conversational mode enabled - will maintain chat history');\n      }\n    }\n    onPlaygroundTemplateToggle(value) {\n      // Update the form control\n      this.agentForm.get('isUseTemplate')?.setValue(value);\n      console.log('Template mode toggled:', value);\n    }\n    onFilesSelected(files) {\n      this.selectedFiles = files;\n      // Update agentFilesUploadedData for agent execution\n      this.agentFilesUploadedData = files;\n    }\n    onApprovalRequested() {\n      // Handle approval request\n    }\n    saveLogs() {\n      // Save execution logs\n    }\n    exportResults(section) {\n      // Export results\n    }\n    handleControlAction(action) {\n      // Handle execution control actions\n    }\n    navigateBack() {\n      this.router.navigate(['/build/agents', this.agentType], {\n        queryParams: {\n          id: this.agentId,\n          mode: 'view'\n        }\n      });\n    }\n    editAgent() {\n      this.router.navigate(['/build/agents', this.agentType], {\n        queryParams: {\n          id: this.agentId,\n          mode: 'edit'\n        }\n      });\n    }\n    navigateToAgentsList() {\n      this.router.navigate(['/build/agents']);\n    }\n    toggleLeftPanel() {\n      this.isLeftPanelCollapsed = !this.isLeftPanelCollapsed;\n    }\n    setActiveRightTab(tab) {\n      this.activeRightTab = tab;\n    }\n    // Blueprint zone management methods\n    toggleBlueprintZone(zoneType) {\n      this.blueprintZonesExpanded[zoneType] = !this.blueprintZonesExpanded[zoneType];\n    }\n    isBlueprintZoneExpanded(zoneType) {\n      return this.blueprintZonesExpanded[zoneType] || false;\n    }\n    // API and helper methods from build-agents component\n    showAgentError(message) {\n      this.chatHistory.push({\n        user: '',\n        ai: {\n          from: 'ai',\n          text: message\n        }\n      });\n    }\n    buildOrganizationPath() {\n      // Simple implementation - in real scenario this would be from navbar/metadata\n      return '';\n    }\n    getMetadataFromNavbar() {\n      // Simple implementation - in real scenario this would get org level mapping\n      return {};\n    }\n    sendAgentMessageToAPI(message, mode, useCaseIdentifier, isConversational, isUseTemplate) {\n      if (isConversational) {\n        this.agentChatPayload.push({\n          content: message,\n          role: 'user'\n        });\n      }\n      const payload = isConversational ? this.agentChatPayload : message;\n      const {\n        levelId\n      } = this.getMetadataFromNavbar();\n      this.agentPlaygroundService.generatePrompt(payload, mode, isConversational, isUseTemplate, this.agentAttachment, useCaseIdentifier, '', levelId).pipe(finalize(() => {\n        this.isProcessingChat = false;\n      }), takeUntil(this.agentPlaygroundDestroy)).subscribe({\n        next: res => this.handleAgentExecuteResponse(res),\n        error: err => this.handleAgentExecuteError(err)\n      });\n    }\n    processAgentFilesAndSendMessage(message, mode, useCaseIdentifier, isConversational, isUseTemplate) {\n      const formData = new FormData();\n      this.agentFilesUploadedData.forEach(fileData => {\n        if (fileData.file) {\n          formData.append('files', fileData.file);\n        }\n      });\n      if (formData.has('files')) {\n        this.agentPlaygroundService.getFileToContent(formData).pipe(switchMap(fileResponse => {\n          const fileContent = fileResponse?.fileResponses?.map(response => response.fileContent)?.join('\\n') || '';\n          this.sendAgentMessageToAPIWithFiles(message, mode, useCaseIdentifier, isConversational, isUseTemplate, fileContent);\n          return of(null);\n        }), catchError(error => {\n          console.error('Error parsing files:', error);\n          this.sendAgentMessageToAPI(message, mode, useCaseIdentifier, isConversational, isUseTemplate);\n          return of(null);\n        })).subscribe();\n      } else {\n        this.sendAgentMessageToAPI(message, mode, useCaseIdentifier, isConversational, isUseTemplate);\n      }\n    }\n    sendAgentMessageToAPIWithFiles(message, mode, useCaseIdentifier, isConversational, isUseTemplate, fileContents) {\n      if (isConversational) {\n        this.agentChatPayload.push({\n          content: message,\n          role: 'user'\n        });\n      }\n      const payload = isConversational ? this.agentChatPayload : message;\n      const {\n        levelId\n      } = this.getMetadataFromNavbar();\n      this.agentPlaygroundService.generatePrompt(payload, mode, isConversational, isUseTemplate, this.agentAttachment, useCaseIdentifier, fileContents, levelId).pipe(finalize(() => {\n        this.isProcessingChat = false;\n        this.isAgentPlaygroundLoading = false;\n      }), takeUntil(this.agentPlaygroundDestroy)).subscribe({\n        next: generatedResponse => {\n          if (generatedResponse?.response && generatedResponse?.response?.choices) {\n            const aiResponseText = generatedResponse.response.choices[0].text;\n            this.chatMessages = [...this.chatMessages, {\n              from: 'ai',\n              text: aiResponseText\n            }];\n            if (isConversational) {\n              this.agentChatPayload.push({\n                content: aiResponseText,\n                role: 'assistant'\n              });\n            }\n          } else {\n            console.warn('Unexpected API response format:', generatedResponse);\n            this.showAgentError('Received unexpected response format from API.');\n          }\n        },\n        error: error => {\n          console.error('API Error:', error);\n          const errorMessage = error?.error?.message || 'An error occurred while processing your request.';\n          this.showAgentError(errorMessage);\n          if (isConversational && this.agentChatPayload.length > 0) {\n            this.agentChatPayload.pop();\n          }\n        }\n      });\n    }\n    // Blueprint panel methods\n    mapAgentConfigurationToBlueprint(agentData) {\n      if (!agentData) {\n        console.warn('No agent data provided for blueprint');\n        return;\n      }\n      console.log('🔍 DEBUG: Full agent data received:', agentData);\n      console.log('🔍 DEBUG: Agent type:', this.agentType);\n      console.log('🔍 DEBUG: Agent data keys:', Object.keys(agentData));\n      // Clear existing nodes\n      this.buildAgentNodes = [];\n      this.canvasNodes = [];\n      let nodeCounter = 1;\n      // Map agent configuration to nodes based on agent type\n      if (this.agentType === 'individual') {\n        this.mapIndividualAgentToBlueprint(agentData, nodeCounter);\n      } else if (this.agentType === 'collaborative') {\n        this.mapCollaborativeAgentToBlueprint(agentData, nodeCounter);\n      }\n      console.log('🎯 Blueprint nodes mapped:', {\n        buildAgentNodes: this.buildAgentNodes,\n        canvasNodes: this.canvasNodes,\n        totalNodes: this.buildAgentNodes.length\n      });\n    }\n    mapIndividualAgentToBlueprint(agentData, nodeCounter) {\n      console.log('🔍 Individual agent mapping - checking fields:', {\n        config: agentData.config,\n        configLength: agentData.config?.length,\n        useCaseName: agentData.useCaseName,\n        prompt: agentData.prompt,\n        useCaseDetails: agentData.useCaseDetails\n      });\n      // Clear existing blueprint nodes\n      this.blueprintPromptNodes = [];\n      this.blueprintModelNodes = [];\n      this.blueprintKnowledgeNodes = [];\n      this.blueprintGuardrailNodes = [];\n      // Add prompt node from \"prompt\" field\n      if (agentData.prompt) {\n        this.blueprintPromptNodes.push({\n          id: `prompt-${nodeCounter++}`,\n          name: agentData.prompt,\n          type: 'prompt'\n        });\n        console.log('✅ Added prompt node:', agentData.prompt);\n      }\n      // Process the config array to extract model, knowledge bases, and guardrails\n      if (agentData.config && Array.isArray(agentData.config)) {\n        console.log('🔍 Processing config array with length:', agentData.config.length);\n        agentData.config.forEach((category, categoryIndex) => {\n          console.log(`🔍 Category ${categoryIndex} (ID: ${category.categoryId}):`, category.categoryName);\n          if (category.config && Array.isArray(category.config)) {\n            console.log(`🔍 Category ${categoryIndex} has ${category.config.length} config items`);\n            category.config.forEach((configItem, itemIndex) => {\n              console.log(`🔍 Config item ${categoryIndex}-${itemIndex}:`, {\n                configKey: configItem.configKey,\n                configValue: configItem.configValue,\n                categoryId: configItem.categoryId\n              });\n              // Handle AI Model from categoryId 1\n              if (configItem.categoryId === 1 && configItem.configKey === 'MODEL' && configItem.configValue) {\n                console.log('✅ Adding AI model node from categoryId 1:', configItem.configValue);\n                this.blueprintModelNodes.push({\n                  id: `model-${nodeCounter++}`,\n                  name: `${configItem.configKey}`,\n                  type: 'model'\n                });\n              }\n              // Handle Knowledge Base from categoryId 2\n              if (configItem.categoryId === 2 && configItem.configKey === 'RAG_KNOWLEDGEBASE_NAME' && configItem.configValue) {\n                console.log('✅ Adding knowledge base nodes from categoryId 2:', configItem.configValue);\n                const kbValue = configItem.configValue.toString();\n                const kbIds = kbValue.split(',').map(id => id.trim()).filter(id => id);\n                kbIds.forEach(kbId => {\n                  this.blueprintKnowledgeNodes.push({\n                    id: `knowledge-${nodeCounter++}`,\n                    name: `Knowledge Base: ${kbId}`,\n                    type: 'knowledge'\n                  });\n                });\n              }\n              // Handle Guardrails from categoryId 3 where configValue is true\n              if (configItem.categoryId === 3 && configItem.configValue === 'true') {\n                console.log('✅ Found enabled guardrail from categoryId 3:', {\n                  key: configItem.configKey,\n                  value: configItem.configValue\n                });\n                if (configItem.configKey === 'ENABLE_GUARDRAILS') {\n                  // Only add one general guardrail node if not already added\n                  if (this.blueprintGuardrailNodes.length === 0) {\n                    this.blueprintGuardrailNodes.push({\n                      id: `guardrail-${nodeCounter++}`,\n                      name: 'Guardrails Enabled',\n                      type: 'guardrail'\n                    });\n                  }\n                } else if (configItem.configKey.startsWith('GUARDRAIL_')) {\n                  // Add specific guardrail nodes for enabled guardrails\n                  let guardrailName = configItem.configKey;\n                  if (guardrailName.startsWith('GUARDRAIL_')) {\n                    guardrailName = guardrailName.replace('GUARDRAIL_', '').replace(/_/g, ' ');\n                  }\n                  this.blueprintGuardrailNodes.push({\n                    id: `guardrail-${nodeCounter++}`,\n                    name: `${guardrailName}`,\n                    type: 'guardrail'\n                  });\n                }\n              }\n            });\n          }\n        });\n      }\n      console.log('🎯 Final blueprint nodes:', {\n        promptNodes: this.blueprintPromptNodes,\n        modelNodes: this.blueprintModelNodes,\n        knowledgeNodes: this.blueprintKnowledgeNodes,\n        guardrailNodes: this.blueprintGuardrailNodes\n      });\n      // Calculate completion percentage\n      const totalRequired = 2; // Prompt + Model are required\n      const currentRequired = this.blueprintPromptNodes.length + this.blueprintModelNodes.length;\n      this.blueprintCompletionPercentage = Math.round(currentRequired / totalRequired * 100);\n    }\n    mapCollaborativeAgentToBlueprint(agentData, nodeCounter) {\n      console.log('🚀 SHARED COMPONENT: mapCollaborativeAgentToBlueprint called!');\n      console.log('🔍 DEBUG: Collaborative agent data received:', agentData);\n      console.log('🔍 DEBUG: Collaborative agent data keys:', Object.keys(agentData));\n      console.log('🔍 DEBUG: Agent type in component:', this.agentType);\n      console.log('🔍 DEBUG: Current nodeCounter:', nodeCounter);\n      // Clear existing blueprint nodes\n      this.blueprintPromptNodes = [];\n      this.blueprintModelNodes = [];\n      this.blueprintKnowledgeNodes = [];\n      this.blueprintToolNodes = [];\n      this.blueprintGuardrailNodes = [];\n      console.log('🔍 DEBUG: Cleared all blueprint node arrays');\n      // Add prompt node - handle different prompt structures for collaborative agents\n      const shouldCreatePromptNode = agentData.goal || agentData.role || agentData.description;\n      console.log('🔍 DEBUG: Checking prompt node creation:', {\n        goal: agentData.goal,\n        role: agentData.role,\n        description: agentData.description,\n        shouldCreatePromptNode\n      });\n      if (shouldCreatePromptNode) {\n        let promptNodeName = agentData.goal || agentData.role || agentData.description || 'Collaborative Agent Prompt';\n        // Truncate prompt if too long for display\n        if (promptNodeName.length > 150) {\n          promptNodeName = promptNodeName.substring(0, 150) + '...';\n        }\n        this.blueprintPromptNodes.push({\n          id: `prompt-${nodeCounter++}`,\n          name: promptNodeName,\n          type: 'prompt'\n        });\n        console.log('✅ Added collaborative prompt node:', promptNodeName);\n      }\n      // Add model nodes - handle both old and new API formats like build-agents\n      let modelReferences = [];\n      console.log('🔍 DEBUG: Checking model data:', {\n        hasAgentConfigs: !!agentData.agentConfigs,\n        agentConfigs: agentData.agentConfigs,\n        model: agentData.model,\n        modelName: agentData.modelName,\n        modelDetails: agentData.modelDetails\n      });\n      // New API format: agentConfigs.modelRef (array of model IDs or objects)\n      if (agentData.agentConfigs && agentData.agentConfigs.modelRef) {\n        const modelRefs = Array.isArray(agentData.agentConfigs.modelRef) ? agentData.agentConfigs.modelRef : [agentData.agentConfigs.modelRef];\n        modelReferences = modelRefs.map(ref => {\n          if (typeof ref === 'number' || typeof ref === 'string') {\n            return {\n              modelId: ref\n            };\n          }\n          return ref;\n        });\n      }\n      // Old API format: modelDetails\n      else if (agentData.modelDetails) {\n        modelReferences = [agentData.modelDetails];\n      }\n      // Fallback: check for model or modelName directly\n      else if (agentData.model || agentData.modelName) {\n        modelReferences = [{\n          modelId: agentData.model || agentData.modelName\n        }];\n      }\n      modelReferences.forEach(modelRef => {\n        const modelId = modelRef.modelId || modelRef.id;\n        const modelName = modelRef.model || modelRef.modelDeploymentName || `Model ID: ${modelId}`;\n        this.blueprintModelNodes.push({\n          id: `model-${nodeCounter++}`,\n          name: modelName,\n          type: 'model'\n        });\n        console.log('✅ Added collaborative model node:', modelName);\n      });\n      // Add knowledge base nodes - handle both old and new API formats\n      let knowledgeReferences = [];\n      // New API format: agentConfigs.knowledgeBaseRef (array of KB IDs or objects)\n      if (agentData.agentConfigs && agentData.agentConfigs.knowledgeBaseRef) {\n        const kbRefs = Array.isArray(agentData.agentConfigs.knowledgeBaseRef) ? agentData.agentConfigs.knowledgeBaseRef : [agentData.agentConfigs.knowledgeBaseRef];\n        knowledgeReferences = kbRefs.map(ref => {\n          if (typeof ref === 'number' || typeof ref === 'string') {\n            return {\n              knowledgeBaseId: ref\n            };\n          }\n          return ref;\n        });\n      }\n      // Old API format: knowledgeBase\n      else if (agentData.knowledgeBase && Array.isArray(agentData.knowledgeBase)) {\n        knowledgeReferences = agentData.knowledgeBase;\n      }\n      knowledgeReferences.forEach(kbRef => {\n        const kbId = kbRef.knowledgeBaseId || kbRef.id;\n        const collectionName = kbRef.indexCollectionName || kbRef.name;\n        const kbName = collectionName || `Knowledge Base ID: ${kbId}`;\n        this.blueprintKnowledgeNodes.push({\n          id: `knowledge-${nodeCounter++}`,\n          name: kbName,\n          type: 'knowledge'\n        });\n        console.log('✅ Added collaborative knowledge node:', kbName);\n      });\n      // Add tool nodes - handle both old and new API formats like build-agents\n      let toolReferences = [];\n      let userToolReferences = [];\n      console.log('🔍 DEBUG: Checking tool data:', {\n        hasAgentConfigs: !!agentData.agentConfigs,\n        agentConfigsContent: agentData.agentConfigs,\n        hasTools: agentData.tools,\n        toolsContent: agentData.tools,\n        hasUserTools: agentData.userTools,\n        userToolsContent: agentData.userTools\n      });\n      // New API format: agentConfigs.toolRef and agentConfigs.userToolRef\n      if (agentData.agentConfigs) {\n        if (agentData.agentConfigs.toolRef) {\n          const toolRefs = Array.isArray(agentData.agentConfigs.toolRef) ? agentData.agentConfigs.toolRef : [agentData.agentConfigs.toolRef];\n          toolReferences = toolRefs.map(ref => {\n            if (typeof ref === 'number' || typeof ref === 'string') {\n              return {\n                toolId: ref\n              };\n            }\n            return ref;\n          });\n        }\n        if (agentData.agentConfigs.userToolRef) {\n          const userToolRefs = Array.isArray(agentData.agentConfigs.userToolRef) ? agentData.agentConfigs.userToolRef : [agentData.agentConfigs.userToolRef];\n          userToolReferences = userToolRefs.map(ref => {\n            if (typeof ref === 'number' || typeof ref === 'string') {\n              return {\n                toolId: ref\n              };\n            }\n            return ref;\n          });\n        }\n      }\n      // Old API format: tools and userTools\n      else {\n        if (agentData.tools && Array.isArray(agentData.tools)) {\n          toolReferences = agentData.tools;\n        }\n        if (agentData.userTools && Array.isArray(agentData.userTools)) {\n          userToolReferences = agentData.userTools;\n        }\n      }\n      // Process built-in tools\n      toolReferences.forEach(tool => {\n        const toolId = tool.toolId || tool.id;\n        const toolName = tool.toolName || tool.name || `Tool ID: ${toolId}`;\n        this.blueprintToolNodes.push({\n          id: `tool-${nodeCounter++}`,\n          name: toolName,\n          type: 'tool'\n        });\n        console.log('✅ Added collaborative builtin tool node:', toolName);\n      });\n      // Process user tools\n      userToolReferences.forEach(userTool => {\n        const userToolId = userTool.toolId || userTool.id;\n        const userToolName = userTool.toolName || userTool.name || `User Tool ID: ${userToolId}`;\n        this.blueprintToolNodes.push({\n          id: `tool-${nodeCounter++}`,\n          name: userToolName,\n          type: 'tool'\n        });\n        console.log('✅ Added collaborative user tool node:', userToolName);\n      });\n      console.log('🎯 Final collaborative blueprint nodes:', {\n        promptNodes: this.blueprintPromptNodes,\n        modelNodes: this.blueprintModelNodes,\n        knowledgeNodes: this.blueprintKnowledgeNodes,\n        toolNodes: this.blueprintToolNodes,\n        guardrailNodes: this.blueprintGuardrailNodes\n      });\n      // Debug: Check blueprint node arrays lengths\n      console.log('📊 Blueprint node counts:', {\n        promptCount: this.blueprintPromptNodes.length,\n        modelCount: this.blueprintModelNodes.length,\n        knowledgeCount: this.blueprintKnowledgeNodes.length,\n        toolCount: this.blueprintToolNodes.length,\n        guardrailCount: this.blueprintGuardrailNodes.length\n      });\n      // Debug: Check if tools zone will be visible\n      console.log('🔧 Tools zone debug:', {\n        agentType: this.agentType,\n        isCollaborative: this.agentType === 'collaborative',\n        hasToolNodes: this.blueprintToolNodes.length > 0,\n        toolNodeNames: this.blueprintToolNodes.map(t => t.name)\n      });\n      // Calculate completion percentage\n      const totalRequired = 2; // Prompt + Model are required\n      const currentRequired = this.blueprintPromptNodes.length + this.blueprintModelNodes.length;\n      this.blueprintCompletionPercentage = Math.round(currentRequired / totalRequired * 100);\n    }\n    static ɵfac = function AgentExecutionComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AgentExecutionComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AgentServiceService), i0.ɵɵdirectiveInject(i3.AgentPlaygroundService), i0.ɵɵdirectiveInject(i4.TokenStorageService), i0.ɵɵdirectiveInject(i5.LoaderService), i0.ɵɵdirectiveInject(i6.FormBuilder), i0.ɵɵdirectiveInject(i7.ToolExecutionService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AgentExecutionComponent,\n      selectors: [[\"app-agent-execution\"]],\n      viewQuery: function AgentExecutionComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(AgentExecutionPlaygroundComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.playgroundComp = _t.first);\n        }\n      },\n      decls: 24,\n      vars: 12,\n      consts: [[\"noIndividualOutput\", \"\"], [\"noCollaborativeOutput\", \"\"], [1, \"agent-execution-container\"], [1, \"top-nav-bar\"], [1, \"nav-left\"], [\"type\", \"button\", 1, \"back-button\", 3, \"click\"], [\"iconName\", \"ArrowLeft\", \"iconSize\", \"16\", \"iconColor\", \"#374151\"], [1, \"agent-name\"], [1, \"main-content\"], [1, \"left-panel\"], [1, \"panel-header\"], [\"type\", \"button\", 1, \"collapse-btn\", 3, \"click\"], [\"iconSize\", \"16\", \"iconColor\", \"#6B7280\", 3, \"iconName\"], [\"class\", \"history-btn\", \"type\", \"button\", \"disabled\", \"\", 4, \"ngIf\"], [\"class\", \"panel-content\", 4, \"ngIf\"], [1, \"right-panel\"], [1, \"tabs-container\"], [1, \"tab-btn\", 3, \"click\"], [1, \"panel-content\"], [\"class\", \"blueprint-content\", 4, \"ngIf\"], [\"class\", \"output-content\", 4, \"ngIf\"], [\"type\", \"button\", \"disabled\", \"\", 1, \"history-btn\"], [3, \"promptChange\", \"messageSent\", \"conversationalToggle\", \"templateToggle\", \"filesSelected\", \"approvalRequested\", \"messages\", \"isLoading\", \"agentType\", \"showChatInteractionToggles\", \"showAiPrincipleToggle\", \"showApprovalButton\", \"showDropdown\", \"showAgentNameInput\", \"showFileUploadButton\", \"displayedAgentName\", \"agentNamePlaceholder\", \"acceptedFileType\"], [1, \"blueprint-content\"], [1, \"blueprint-header\"], [1, \"custom-blueprint-container\"], [\"viewBox\", \"0 0 100 100\", \"preserveAspectRatio\", \"none\", 1, \"connection-lines\"], [\"x1\", \"25\", \"y1\", \"25\", \"x2\", \"50\", \"y2\", \"50\", \"stroke\", \"#BBBEC5\", \"stroke-width\", \"0.3\"], [\"x1\", \"75\", \"y1\", \"25\", \"x2\", \"50\", \"y2\", \"50\", \"stroke\", \"#BBBEC5\", \"stroke-width\", \"0.3\"], [\"x1\", \"25\", \"y1\", \"75\", \"x2\", \"50\", \"y2\", \"50\", \"stroke\", \"#BBBEC5\", \"stroke-width\", \"0.3\"], [\"x1\", \"75\", \"y1\", \"75\", \"x2\", \"50\", \"y2\", \"50\", \"stroke\", \"#BBBEC5\", \"stroke-width\", \"0.3\"], [1, \"central-progress\"], [1, \"progress-ring\"], [\"width\", \"120\", \"height\", \"120\", 1, \"progress-circle\"], [\"id\", \"progressGradient\", \"x1\", \"0%\", \"y1\", \"0%\", \"x2\", \"100%\", \"y2\", \"100%\"], [\"offset\", \"33.91%\", 2, \"stop-color\", \"#0084ff\"], [\"offset\", \"100%\", 2, \"stop-color\", \"#03bdd4\"], [\"cx\", \"60\", \"cy\", \"60\", \"r\", \"50\", \"fill\", \"none\", \"stroke\", \"#e5e7eb\", \"stroke-width\", \"8\", 1, \"progress-background\"], [\"cx\", \"60\", \"cy\", \"60\", \"r\", \"50\", \"fill\", \"none\", \"stroke\", \"url(#progressGradient)\", \"stroke-width\", \"8\", \"stroke-linecap\", \"round\", \"transform\", \"rotate(180 60 60)\", 1, \"progress-bar\"], [1, \"progress-content\"], [1, \"progress-percentage\"], [1, \"progress-label\"], [\"id\", \"parent-box\"], [1, \"blueprint-zone\", \"north-zone\", \"prompts-zone\"], [1, \"zone-header\", 3, \"click\"], [1, \"header-content\"], [1, \"header-icon\"], [\"width\", \"45\", \"height\", \"44\", \"viewBox\", \"0 0 45 44\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"x\", \"0.679688\", \"width\", \"44\", \"height\", \"44\", \"rx\", \"8\", \"fill\", \"#5082EF\"], [\"d\", \"M20.6166 25.5C20.5273 25.1539 20.347 24.8381 20.0942 24.5854C19.8415 24.3327 19.5257 24.1523 19.1796 24.063L13.0446 22.481C12.94 22.4513 12.8478 22.3883 12.7822 22.3014C12.7166 22.2146 12.6812 22.1088 12.6812 22C12.6812 21.8912 12.7166 21.7854 12.7822 21.6986C12.8478 21.6118 12.94 21.5487 13.0446 21.519L19.1796 19.936C19.5256 19.8468 19.8413 19.6666 20.094 19.414C20.3467 19.1615 20.5272 18.8459 20.6166 18.5L22.1986 12.365C22.228 12.2599 22.291 12.1673 22.3779 12.1014C22.4649 12.0355 22.571 11.9998 22.6801 11.9998C22.7892 11.9998 22.8954 12.0355 22.9823 12.1014C23.0692 12.1673 23.1322 12.2599 23.1616 12.365L24.7426 18.5C24.8319 18.8461 25.0123 19.1619 25.265 19.4146C25.5177 19.6673 25.8336 19.8477 26.1796 19.937L32.3146 21.518C32.4201 21.5471 32.5132 21.61 32.5795 21.6971C32.6458 21.7841 32.6817 21.8906 32.6817 22C32.6817 22.1094 32.6458 22.2159 32.5795 22.3029C32.5132 22.39 32.4201 22.4529 32.3146 22.482L26.1796 24.063C25.8336 24.1523 25.5177 24.3327 25.265 24.5854C25.0123 24.8381 24.8319 25.1539 24.7426 25.5L23.1606 31.635C23.1312 31.7401 23.0682 31.8327 22.9813 31.8986C22.8944 31.9646 22.7882 32.0003 22.6791 32.0003C22.57 32.0003 22.4639 31.9646 22.3769 31.8986C22.29 31.8327 22.227 31.7401 22.1976 31.635L20.6166 25.5Z\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"zone-title\"], [1, \"header-actions\"], [1, \"required-badge\"], [\"type\", \"button\", 1, \"accordion-toggle\"], [\"width\", \"16\", \"height\", \"16\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\"], [\"d\", \"M6 9L12 15L18 9\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"class\", \"zone-content\", 4, \"ngIf\"], [1, \"blueprint-zone\", \"west-zone\", \"knowledge-zone\"], [\"x\", \"0.679688\", \"width\", \"44\", \"height\", \"44\", \"rx\", \"8\", \"fill\", \"#308666\"], [\"d\", \"M22.6797 17V31\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M26.6797 22H28.6797\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M26.6797 18H28.6797\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M13.6797 28C13.4145 28 13.1601 27.8946 12.9726 27.7071C12.785 27.5196 12.6797 27.2652 12.6797 27V14C12.6797 13.7348 12.785 13.4804 12.9726 13.2929C13.1601 13.1054 13.4145 13 13.6797 13H18.6797C19.7406 13 20.758 13.4214 21.5081 14.1716C22.2583 14.9217 22.6797 15.9391 22.6797 17C22.6797 15.9391 23.1011 14.9217 23.8513 14.1716C24.6014 13.4214 25.6188 13 26.6797 13H31.6797C31.9449 13 32.1993 13.1054 32.3868 13.2929C32.5743 13.4804 32.6797 13.7348 32.6797 14V27C32.6797 27.2652 32.5743 27.5196 32.3868 27.7071C32.1993 27.8946 31.9449 28 31.6797 28H25.6797C24.884 28 24.121 28.3161 23.5584 28.8787C22.9958 29.4413 22.6797 30.2044 22.6797 31C22.6797 30.2044 22.3636 29.4413 21.801 28.8787C21.2384 28.3161 20.4753 28 19.6797 28H13.6797Z\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M16.6797 22H18.6797\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M16.6797 18H18.6797\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"optional-badge\"], [1, \"blueprint-zone\", \"east-zone\", \"models-zone\"], [\"x\", \"0.679688\", \"width\", \"44\", \"height\", \"44\", \"rx\", \"8\", \"fill\", \"#997BCF\"], [\"d\", \"M31.6797 18C31.6793 17.6493 31.5867 17.3048 31.4112 17.0012C31.2357 16.6975 30.9834 16.4454 30.6797 16.27L23.6797 12.27C23.3756 12.0945 23.0308 12.0021 22.6797 12.0021C22.3286 12.0021 21.9837 12.0945 21.6797 12.27L14.6797 16.27C14.376 16.4454 14.1237 16.6975 13.9481 17.0012C13.7726 17.3048 13.68 17.6493 13.6797 18V26C13.68 26.3508 13.7726 26.6952 13.9481 26.9989C14.1237 27.3025 14.376 27.5547 14.6797 27.73L21.6797 31.73C21.9837 31.9056 22.3286 31.998 22.6797 31.998C23.0308 31.998 23.3756 31.9056 23.6797 31.73L30.6797 27.73C30.9834 27.5547 31.2357 27.3025 31.4112 26.9989C31.5867 26.6952 31.6793 26.3508 31.6797 26V18Z\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M13.9795 17L22.6795 22L31.3795 17\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M22.6797 32V22\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"class\", \"blueprint-zone south-zone tools-zone\", 3, \"has-nodes\", 4, \"ngIf\"], [\"class\", \"blueprint-zone south-zone guardrails-zone\", 3, \"has-nodes\", 4, \"ngIf\"], [1, \"zone-content\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [1, \"nodes-list\"], [\"class\", \"kanban-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"empty-state\"], [1, \"kanban-card\"], [1, \"card-title\"], [1, \"blueprint-zone\", \"south-zone\", \"tools-zone\"], [\"width\", \"45\", \"height\", \"44\", \"viewBox\", \"0 0 45 44\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\", 4, \"ngIf\"], [\"x\", \"0.679688\", \"width\", \"44\", \"height\", \"44\", \"rx\", \"8\", \"fill\", \"#D97706\"], [\"d\", \"M25.3795 16.3C25.1963 16.4869 25.0937 16.7382 25.0937 17C25.0937 17.2617 25.1963 17.5131 25.3795 17.7L26.9795 19.3C27.1665 19.4832 27.4178 19.5858 27.6795 19.5858C27.9413 19.5858 28.1926 19.4832 28.3795 19.3L32.1495 15.53C32.6524 16.6412 32.8046 17.8792 32.586 19.0791C32.3674 20.279 31.7883 21.3838 30.9258 22.2463C30.0634 23.1087 28.9586 23.6878 27.7587 23.9064C26.5588 24.1251 25.3207 23.9728 24.2095 23.47L17.2995 30.38C16.9017 30.7778 16.3622 31.0013 15.7995 31.0013C15.2369 31.0013 14.6974 30.7778 14.2995 30.38C13.9017 29.9822 13.6782 29.4426 13.6782 28.88C13.6782 28.3174 13.9017 27.7778 14.2995 27.38L21.2095 20.47C20.7067 19.3588 20.5545 18.1207 20.7731 16.9208C20.9917 15.7209 21.5708 14.6161 22.4333 13.7537C23.2957 12.8913 24.4005 12.3121 25.6004 12.0935C26.8003 11.8749 28.0384 12.0271 29.1495 12.53L25.3895 16.29L25.3795 16.3Z\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"blueprint-zone\", \"south-zone\", \"guardrails-zone\"], [\"x\", \"0.679688\", \"width\", \"44\", \"height\", \"44\", \"rx\", \"8\", \"fill\", \"#DC2626\"], [\"d\", \"M30.6797 23C30.6797 28 27.1797 30.5 23.0197 31.95C22.8019 32.0238 22.5652 32.0202 22.3497 31.94C18.1797 30.5 14.6797 28 14.6797 23V16C14.6797 15.7347 14.785 15.4804 14.9726 15.2929C15.1601 15.1053 15.4145 15 15.6797 15C17.6797 15 20.1797 13.8 21.9197 12.28C22.1315 12.099 22.401 11.9995 22.6797 11.9995C22.9583 11.9995 23.2278 12.099 23.4397 12.28C25.1897 13.81 27.6797 15 29.6797 15C29.9449 15 30.1993 15.1053 30.3868 15.2929C30.5743 15.4804 30.6797 15.7347 30.6797 16V23Z\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"output-content\"], [\"class\", \"individual-output\", 4, \"ngIf\"], [\"class\", \"collaborative-output\", 4, \"ngIf\"], [1, \"individual-output\"], [\"class\", \"output-box\", 4, \"ngIf\", \"ngIfElse\"], [1, \"output-box\"], [1, \"output-section\"], [1, \"output-text\"], [1, \"collaborative-output\"], [4, \"ngIf\", \"ngIfElse\"], [\"class\", \"output-box\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"task-description\", 4, \"ngIf\"], [\"class\", \"task-expected\", 4, \"ngIf\"], [1, \"task-description\"], [1, \"task-expected\"]],\n      template: function AgentExecutionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4)(3, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function AgentExecutionComponent_Template_button_click_3_listener() {\n            return ctx.navigateBack();\n          });\n          i0.ɵɵelement(4, \"ava-icon\", 6);\n          i0.ɵɵelementStart(5, \"span\", 7);\n          i0.ɵɵtext(6);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(7, \"div\", 8)(8, \"div\", 9)(9, \"div\", 10)(10, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function AgentExecutionComponent_Template_button_click_10_listener() {\n            return ctx.toggleLeftPanel();\n          });\n          i0.ɵɵelement(11, \"ava-icon\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(12, AgentExecutionComponent_button_12_Template, 2, 0, \"button\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(13, AgentExecutionComponent_div_13_Template, 2, 12, \"div\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"div\", 15)(15, \"div\", 10)(16, \"div\", 16)(17, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function AgentExecutionComponent_Template_button_click_17_listener() {\n            return ctx.setActiveRightTab(\"blueprint\");\n          });\n          i0.ɵɵtext(18, \" Blueprint \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function AgentExecutionComponent_Template_button_click_19_listener() {\n            return ctx.setActiveRightTab(\"output\");\n          });\n          i0.ɵɵtext(20, \" Agent Output \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"div\", 18);\n          i0.ɵɵtemplate(22, AgentExecutionComponent_div_22_Template, 82, 22, \"div\", 19)(23, AgentExecutionComponent_div_23_Template, 3, 2, \"div\", 20);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.agentName);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"collapsed\", ctx.isLeftPanelCollapsed);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"iconName\", ctx.isLeftPanelCollapsed ? \"ChevronRight\" : \"PanelLeft\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLeftPanelCollapsed);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLeftPanelCollapsed);\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"active\", ctx.activeRightTab === \"blueprint\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"active\", ctx.activeRightTab === \"output\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.activeRightTab === \"blueprint\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.activeRightTab === \"output\");\n        }\n      },\n      dependencies: [CommonModule, i8.NgForOf, i8.NgIf, FormsModule, AgentExecutionPlaygroundComponent, IconComponent],\n      styles: [\".agent-execution-container[_ngcontent-%COMP%] {\\n  height: calc(100vh - 84px);\\n  display: flex;\\n  flex-direction: column;\\n  color: var(--color-text-primary);\\n  overflow: hidden;\\n}\\n\\n.top-nav-bar[_ngcontent-%COMP%] {\\n  padding: 16px 24px;\\n  padding-bottom: 0px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  min-height: 64px;\\n}\\n\\n.nav-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.back-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 8px 12px;\\n  border-radius: 6px;\\n  transition: background-color 0.2s ease;\\n}\\n.back-button[_ngcontent-%COMP%]:hover {\\n  background: var(--color-background-tertiary);\\n}\\n.back-button[_ngcontent-%COMP%]   .agent-name[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #000000;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  gap: 16px;\\n  padding: 16px;\\n  padding-top: 0px;\\n  height: calc(100vh - 96px);\\n  overflow: hidden;\\n}\\n\\n.left-panel[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 400px;\\n  max-width: 600px;\\n  background: white;\\n  border-radius: 12px;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n  transition: all 0.3s ease;\\n}\\n.left-panel.collapsed[_ngcontent-%COMP%] {\\n  flex: 0 0 48px;\\n  min-width: 48px;\\n  max-width: 48px;\\n}\\n\\n.right-panel[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 400px;\\n  background: white;\\n  border-radius: 12px;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n\\n.panel-header[_ngcontent-%COMP%] {\\n  padding: 16px 20px;\\n  background: #e9effd;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  max-height: 45px;\\n  border-radius: 12px 12px 0 0;\\n}\\n\\n.collapse-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 6px;\\n  border-radius: 4px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: background-color 0.2s ease;\\n  color: #1a46a7;\\n}\\n.collapse-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--color-background-quaternary);\\n}\\n\\n.history-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 8px 12px;\\n  border-radius: 6px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #1a46a7;\\n  transition: all 0.2s ease;\\n}\\n.history-btn[_ngcontent-%COMP%]:disabled {\\n  cursor: not-allowed;\\n}\\n.history-btn[_ngcontent-%COMP%]:hover {\\n  color: var(--color-text-primary);\\n  background: var(--color-background-quaternary);\\n}\\n\\n.tabs-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  padding: 4px;\\n}\\n\\n.tab-btn[_ngcontent-%COMP%] {\\n  padding: 8px 20px;\\n  border: none;\\n  background: transparent;\\n  color: var(--text-secondary);\\n  border-radius: 10px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  white-space: nowrap;\\n  color: #1a46a7;\\n}\\n.tab-btn.active[_ngcontent-%COMP%] {\\n  background: white;\\n  color: var(--nav-pill-selected-color);\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n  font-weight: 600;\\n}\\n\\n.panel-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.mock-content[_ngcontent-%COMP%] {\\n  padding: 24px;\\n  height: 100%;\\n  overflow-y: auto;\\n}\\n.mock-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: var(--color-text-primary);\\n}\\n.mock-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 24px 0;\\n  color: var(--color-text-secondary);\\n  font-size: 14px;\\n  line-height: 1.5;\\n}\\n\\n.blueprint-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n}\\n\\n.blueprint-header[_ngcontent-%COMP%] {\\n  padding: 10px;\\n}\\n.blueprint-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: black;\\n  margin: 0 0 5px 0;\\n  font-size: 18px;\\n  font-weight: 600;\\n  text-align: center;\\n}\\n.blueprint-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--color-text-secondary);\\n  margin: 0;\\n  font-size: 14px;\\n}\\n\\n.blueprint-canvas-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow: auto;\\n  padding: 10px;\\n  background: var(--color-background-primary);\\n}\\n\\n.custom-blueprint-container[_ngcontent-%COMP%] {\\n  height: 100%;\\n  width: 100%;\\n  min-height: 500px;\\n  background-color: #f8f9fa;\\n  background-image: radial-gradient(circle, #d1d5db 1px, transparent 1px);\\n  background-size: 20px 20px;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  border: 1px solid #d1d3d8;\\n  position: relative;\\n  padding: 5%;\\n  border-radius: 10px;\\n}\\n\\n.central-progress[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  z-index: 10;\\n  background-color: white;\\n  border-radius: 50%;\\n  padding: 8px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-ring[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-circle[_ngcontent-%COMP%] {\\n  transform: rotate(-90deg);\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-background[_ngcontent-%COMP%] {\\n  opacity: 0.15;\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  transition: stroke-dashoffset 0.3s ease;\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-content[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  text-align: center;\\n  color: #374151;\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-percentage[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  line-height: 1;\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-label[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #6b7280;\\n  margin-top: 2px;\\n}\\n\\n#parent-box[_ngcontent-%COMP%] {\\n  height: 100%;\\n  width: 100%;\\n  gap: 2rem;\\n  position: relative;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.blueprint-zones-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 80%;\\n  height: 80%;\\n  max-width: 800px;\\n  max-height: 600px;\\n}\\n\\n#box1[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: space-between;\\n  height: 300px;\\n  position: relative;\\n  border: 1px solid #bbbec5;\\n  position: fixed;\\n  top: 32%;\\n  left: 40%;\\n  border-left: none;\\n}\\n\\n#box2[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: space-between;\\n  height: 300px;\\n  position: relative;\\n  border: 1px solid #bbbec5;\\n  position: fixed;\\n  top: 32%;\\n  left: 60%;\\n  border-right: none;\\n}\\n\\n.blueprint-zone[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  padding: 16px;\\n  transition: all 0.3s ease;\\n  position: absolute;\\n  width: 280px;\\n  z-index: 5;\\n}\\n.blueprint-zone.has-nodes[_ngcontent-%COMP%] {\\n  border-style: solid;\\n}\\n.blueprint-zone.collapsed[_ngcontent-%COMP%] {\\n  min-height: 40px;\\n}\\n.blueprint-zone.collapsed[_ngcontent-%COMP%]   .zone-header[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n\\n.north-zone[_ngcontent-%COMP%] {\\n  background-color: #ecf0fa;\\n  color: #005eb5;\\n  border: 2px solid #9ab7f6;\\n  top: 0;\\n  left: 0;\\n  overflow: hidden;\\n}\\n\\n.east-zone[_ngcontent-%COMP%] {\\n  background-color: #f2ebfd;\\n  border: 2px solid #d6c2f9;\\n  color: #d6c2f9;\\n  top: 0;\\n  right: 0;\\n  overflow: hidden;\\n}\\n\\n.south-zone[_ngcontent-%COMP%] {\\n  background-color: #fbf6f7;\\n  border: 2px solid #fecaca;\\n  color: #dc2626 !important;\\n  bottom: 0;\\n  right: 0;\\n  overflow: hidden;\\n}\\n.south-zone.has-nodes[_ngcontent-%COMP%] {\\n  height: auto;\\n  min-height: 100px;\\n  max-height: 200px;\\n}\\n.south-zone.has-nodes[_ngcontent-%COMP%]:hover {\\n  overflow-y: auto;\\n  scrollbar-width: none !important;\\n}\\n\\n.west-zone[_ngcontent-%COMP%] {\\n  background-color: #ecf8f4;\\n  border: 2px solid #a9e1cc;\\n  color: #25684f;\\n  bottom: 0;\\n  left: 0;\\n  overflow: hidden;\\n}\\n.west-zone.has-nodes[_ngcontent-%COMP%] {\\n  height: auto;\\n  min-height: 100px;\\n  max-height: 200px;\\n}\\n.west-zone.has-nodes[_ngcontent-%COMP%]:hover {\\n  overflow-y: auto;\\n  scrollbar-width: none !important;\\n}\\n\\n.connection-lines[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  pointer-events: none;\\n  z-index: 1;\\n}\\n.connection-lines[_ngcontent-%COMP%]   line[_ngcontent-%COMP%] {\\n  opacity: 0.7;\\n  transition: opacity 0.3s ease;\\n}\\n.connection-lines[_ngcontent-%COMP%]   line[_ngcontent-%COMP%]:hover {\\n  opacity: 1;\\n}\\n\\n.central-progress[_ngcontent-%COMP%] {\\n  z-index: 10;\\n}\\n\\n.blueprint-zone[_ngcontent-%COMP%] {\\n  z-index: 5;\\n}\\n\\n.zone-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin-bottom: 16px;\\n  padding-bottom: 12px;\\n  cursor: pointer;\\n  -webkit-user-select: none;\\n          user-select: none;\\n  transition: all 0.2s ease;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 8px;\\n  background-color: rgba(59, 130, 246, 0.1);\\n  color: #3b82f6;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .zone-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #374151;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .accordion-toggle[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 4px;\\n  border-radius: 4px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.2s ease;\\n  color: #6b7280;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .accordion-toggle[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 0, 0, 0.05);\\n  color: #374151;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .accordion-toggle[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  transition: transform 0.3s ease;\\n}\\n\\n.required-badge[_ngcontent-%COMP%] {\\n  background-color: var(--status-error);\\n  color: white;\\n  font-size: 10px;\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n  font-weight: 500;\\n}\\n\\n.optional-badge[_ngcontent-%COMP%] {\\n  background-color: var(--status-warning);\\n  color: white;\\n  font-size: 10px;\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n  font-weight: 500;\\n}\\n\\n.zone-content[_ngcontent-%COMP%] {\\n  min-height: 60px;\\n  flex: 1;\\n  transition: all 0.3s ease-in-out;\\n  opacity: 1;\\n  overflow: hidden;\\n}\\n\\n.empty-state[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: var(--text-secondary);\\n  font-size: 12px;\\n  padding: 20px 0;\\n  font-style: italic;\\n}\\n\\n.nodes-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n}\\n\\n.kanban-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  padding: 12px 16px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n  transition: all 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n}\\n.kanban-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\\n  transform: translateY(-1px);\\n}\\n.kanban-card[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #374151;\\n  flex: 1;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n.blueprint-placeholder[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n\\n.blueprint-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 16px;\\n  background: var(--color-background-primary);\\n  border: 1px solid var(--color-border-primary);\\n  border-radius: 8px;\\n  transition: all 0.2s ease;\\n}\\n.blueprint-item[_ngcontent-%COMP%]:hover {\\n  border-color: var(--color-border-secondary);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.blueprint-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  width: 32px;\\n  height: 32px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.blueprint-text[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: var(--color-text-primary);\\n}\\n\\n.output-placeholder[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 24px;\\n}\\n\\n.output-section[_ngcontent-%COMP%] {\\n  background: var(--color-background-primary);\\n  border: 1px solid var(--color-border-primary);\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n.output-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 16px 16px 8px 16px;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: var(--color-text-primary);\\n}\\n.output-section[_ngcontent-%COMP%]   .output-meta[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 0 16px 16px 16px;\\n  font-size: 12px;\\n  color: var(--color-text-tertiary);\\n}\\n\\n.output-preview[_ngcontent-%COMP%] {\\n  border-top: 1px solid var(--color-border-primary);\\n}\\n.output-preview[_ngcontent-%COMP%]   .code-block[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  background: var(--color-background-secondary);\\n  font-family: \\\"Monaco\\\", \\\"Menlo\\\", \\\"Ubuntu Mono\\\", monospace;\\n  font-size: 12px;\\n  line-height: 1.6;\\n  color: var(--color-text-primary);\\n  border-bottom: 1px solid var(--color-border-primary);\\n}\\n.output-preview[_ngcontent-%COMP%]   .preview-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 12px 16px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: var(--color-brand-primary);\\n  transition: all 0.2s ease;\\n  width: 100%;\\n  text-align: left;\\n}\\n.output-preview[_ngcontent-%COMP%]   .preview-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--color-background-tertiary);\\n}\\n\\n.scrollable-chat-history[_ngcontent-%COMP%] {\\n  height: calc(100% - 40px);\\n  overflow-y: auto;\\n  padding: 1rem;\\n}\\n.scrollable-chat-history[_ngcontent-%COMP%]   .chat-interaction[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n.scrollable-chat-history[_ngcontent-%COMP%]   .chat-interaction[_ngcontent-%COMP%]   .user-prompt[_ngcontent-%COMP%] {\\n  margin-bottom: 0.5rem;\\n}\\n.scrollable-chat-history[_ngcontent-%COMP%]   .chat-interaction[_ngcontent-%COMP%]   .user-prompt[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: var(--color-text-primary);\\n}\\n.scrollable-chat-history[_ngcontent-%COMP%]   .chat-interaction[_ngcontent-%COMP%]   .user-prompt[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: var(--color-text-secondary);\\n}\\n.scrollable-chat-history[_ngcontent-%COMP%]   .chat-interaction[_ngcontent-%COMP%]   .ai-response[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  border-radius: 8px;\\n  background-color: var(--color-background-secondary);\\n  border-left: 4px solid var(--color-border-primary);\\n}\\n.scrollable-chat-history[_ngcontent-%COMP%]   .chat-interaction[_ngcontent-%COMP%]   .ai-response.loading[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--color-text-tertiary);\\n  font-style: italic;\\n}\\n.scrollable-chat-history[_ngcontent-%COMP%]   .chat-interaction[_ngcontent-%COMP%]   .ai-response.status-success[_ngcontent-%COMP%] {\\n  border-left-color: var(--status-success);\\n}\\n.scrollable-chat-history[_ngcontent-%COMP%]   .chat-interaction[_ngcontent-%COMP%]   .ai-response.status-failed[_ngcontent-%COMP%] {\\n  border-left-color: var(--status-danger);\\n}\\n.scrollable-chat-history[_ngcontent-%COMP%]   .chat-interaction[_ngcontent-%COMP%]   .ai-response[_ngcontent-%COMP%]   .response-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 0.5rem;\\n}\\n.scrollable-chat-history[_ngcontent-%COMP%]   .chat-interaction[_ngcontent-%COMP%]   .ai-response[_ngcontent-%COMP%]   .response-header[_ngcontent-%COMP%]   .agent-name-highlight[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: var(--color-text-primary);\\n}\\n.scrollable-chat-history[_ngcontent-%COMP%]   .chat-interaction[_ngcontent-%COMP%]   .ai-response[_ngcontent-%COMP%]   .response-header[_ngcontent-%COMP%]   .status-label[_ngcontent-%COMP%] {\\n  padding: 0.25rem 0.75rem;\\n  border-radius: 12px;\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n  color: white;\\n}\\n.scrollable-chat-history[_ngcontent-%COMP%]   .chat-interaction[_ngcontent-%COMP%]   .ai-response[_ngcontent-%COMP%]   .response-header[_ngcontent-%COMP%]   .status-label.status-success[_ngcontent-%COMP%] {\\n  background-color: var(--status-success);\\n}\\n.scrollable-chat-history[_ngcontent-%COMP%]   .chat-interaction[_ngcontent-%COMP%]   .ai-response[_ngcontent-%COMP%]   .response-header[_ngcontent-%COMP%]   .status-label.status-failed[_ngcontent-%COMP%] {\\n  background-color: var(--status-danger);\\n}\\n.scrollable-chat-history[_ngcontent-%COMP%]   .chat-interaction[_ngcontent-%COMP%]   .ai-response[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: var(--color-text-primary);\\n  white-space: pre-wrap;\\n  word-wrap: break-word;\\n}\\n\\n@media (max-width: 768px) {\\n  .execution-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .playground-column[_ngcontent-%COMP%], \\n   .output-column[_ngcontent-%COMP%] {\\n    flex: none;\\n    height: 50%;\\n  }\\n  .playground-column[_ngcontent-%COMP%] {\\n    border-right: none;\\n    border-bottom: 1px solid var(--color-border-primary);\\n  }\\n}\\n.row[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  margin: 0;\\n}\\n\\n.col-7[_ngcontent-%COMP%] {\\n  flex: 0 0 58.333333%;\\n  max-width: 58.333333%;\\n}\\n\\n.col-5[_ngcontent-%COMP%] {\\n  flex: 0 0 41.666667%;\\n  max-width: 41.666667%;\\n}\\n\\n*[_ngcontent-%COMP%] {\\n  box-sizing: border-box;\\n}\\n\\n[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n  height: 6px;\\n}\\n\\n[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: var(--color-background-secondary);\\n}\\n\\n[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: var(--color-border-primary);\\n  border-radius: 3px;\\n}\\n\\n[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: var(--color-border-secondary);\\n}\\n\\n.activity-placeholder[_ngcontent-%COMP%], \\n.output-placeholder[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n.activity-placeholder[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], \\n.output-placeholder[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: var(--color-text-primary);\\n}\\n.activity-placeholder[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], \\n.output-placeholder[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: var(--color-text-secondary);\\n  font-size: 14px;\\n}\\n\\n.activity-list[_ngcontent-%COMP%], \\n.output-list[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  border: 1px solid var(--color-border-primary);\\n  border-radius: 8px;\\n  padding: 16px;\\n  background: var(--color-background-secondary);\\n}\\n\\n.activity-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n  padding: 12px 0;\\n  border-bottom: 1px solid var(--color-border-primary);\\n}\\n.activity-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.activity-item[_ngcontent-%COMP%]   .timestamp[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: var(--color-text-tertiary);\\n  font-weight: 500;\\n}\\n.activity-item[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--color-text-primary);\\n}\\n\\n.output-item[_ngcontent-%COMP%] {\\n  padding: 12px 0;\\n  border-bottom: 1px solid var(--color-border-primary);\\n}\\n.output-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.output-item[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--color-text-primary);\\n  line-height: 1.5;\\n}\\n\\n.individual-output[_ngcontent-%COMP%], \\n.collaborative-output[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n}\\n\\n.scrollable-chat-history[_ngcontent-%COMP%] {\\n  height: 100%;\\n  overflow-y: auto;\\n  padding: 1rem;\\n}\\n.scrollable-chat-history[_ngcontent-%COMP%]   .chat-interaction[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n.scrollable-chat-history[_ngcontent-%COMP%]   .chat-interaction[_ngcontent-%COMP%]   .user-prompt[_ngcontent-%COMP%] {\\n  margin-bottom: 0.5rem;\\n}\\n.scrollable-chat-history[_ngcontent-%COMP%]   .chat-interaction[_ngcontent-%COMP%]   .ai-response[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  border-radius: 8px;\\n  background-color: #f3f4f6;\\n}\\n.scrollable-chat-history[_ngcontent-%COMP%]   .chat-interaction[_ngcontent-%COMP%]   .ai-response.status-success[_ngcontent-%COMP%] {\\n  border-left: 4px solid #10b981;\\n}\\n.scrollable-chat-history[_ngcontent-%COMP%]   .chat-interaction[_ngcontent-%COMP%]   .ai-response.status-failed[_ngcontent-%COMP%] {\\n  border-left: 4px solid #ef4444;\\n}\\n.scrollable-chat-history[_ngcontent-%COMP%]   .chat-interaction[_ngcontent-%COMP%]   .ai-response[_ngcontent-%COMP%]   .response-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 0.5rem;\\n}\\n.scrollable-chat-history[_ngcontent-%COMP%]   .chat-interaction[_ngcontent-%COMP%]   .ai-response[_ngcontent-%COMP%]   .response-header[_ngcontent-%COMP%]   .agent-name-highlight[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n  color: #374151;\\n}\\n.scrollable-chat-history[_ngcontent-%COMP%]   .chat-interaction[_ngcontent-%COMP%]   .ai-response[_ngcontent-%COMP%]   .response-header[_ngcontent-%COMP%]   .status-label[_ngcontent-%COMP%] {\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 12px;\\n  font-size: 0.875rem;\\n  color: white;\\n}\\n.scrollable-chat-history[_ngcontent-%COMP%]   .chat-interaction[_ngcontent-%COMP%]   .ai-response[_ngcontent-%COMP%]   .response-header[_ngcontent-%COMP%]   .status-label.status-success[_ngcontent-%COMP%] {\\n  background-color: #10b981;\\n}\\n.scrollable-chat-history[_ngcontent-%COMP%]   .chat-interaction[_ngcontent-%COMP%]   .ai-response[_ngcontent-%COMP%]   .response-header[_ngcontent-%COMP%]   .status-label.status-failed[_ngcontent-%COMP%] {\\n  background-color: #ef4444;\\n}\\n\\n.individual-output[_ngcontent-%COMP%], \\n.collaborative-output[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n.individual-output[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], \\n.collaborative-output[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: var(--color-text-primary);\\n}\\n.individual-output[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], \\n.collaborative-output[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--color-text-secondary);\\n  font-size: 14px;\\n  margin: 0;\\n}\\n\\n.output-box[_ngcontent-%COMP%] {\\n  background: var(--color-background-secondary);\\n  border: 1px solid var(--color-border-primary);\\n  border-radius: 8px;\\n  margin-bottom: 16px;\\n}\\n.output-box[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n\\n.output-section[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n.output-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 12px 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: var(--color-text-primary);\\n}\\n.output-section[_ngcontent-%COMP%]   .task-description[_ngcontent-%COMP%], \\n.output-section[_ngcontent-%COMP%]   .task-expected[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n  font-size: 14px;\\n  color: var(--color-text-secondary);\\n}\\n.output-section[_ngcontent-%COMP%]   .task-description[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%], \\n.output-section[_ngcontent-%COMP%]   .task-expected[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: var(--color-text-primary);\\n}\\n\\n.output-text[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  line-height: 1.6;\\n  color: var(--color-text-primary);\\n  white-space: pre-wrap;\\n  word-wrap: break-word;\\n  background: var(--color-background-primary);\\n  padding: 12px;\\n  border-radius: 6px;\\n  border: 1px solid var(--color-border-secondary);\\n  max-height: 400px;\\n  overflow-y: auto;\\n}\\n\\n.tools-zone[_ngcontent-%COMP%] {\\n  border-color: #ff8c00;\\n}\\n.tools-zone[_ngcontent-%COMP%]   .zone-header[_ngcontent-%COMP%] {\\n  border-bottom-color: rgba(255, 140, 0, 0.2);\\n}\\n\\n.guardrails-zone[_ngcontent-%COMP%] {\\n  border-color: #dc2626;\\n}\\n.guardrails-zone[_ngcontent-%COMP%]   .zone-header[_ngcontent-%COMP%] {\\n  border-bottom-color: rgba(220, 38, 38, 0.2);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return AgentExecutionComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "Subject", "takeUntil", "switchMap", "finalize", "catchError", "of", "FormsModule", "AgentExecutionPlaygroundComponent", "IconComponent", "environment", "ExecutionStatus", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "AgentExecutionComponent_div_13_Template_app_agent_execution_playground_promptChange_1_listener", "$event", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onPromptChanged", "AgentExecutionComponent_div_13_Template_app_agent_execution_playground_messageSent_1_listener", "handleChatMessage", "AgentExecutionComponent_div_13_Template_app_agent_execution_playground_conversationalToggle_1_listener", "onPlaygroundConversationalToggle", "AgentExecutionComponent_div_13_Template_app_agent_execution_playground_templateToggle_1_listener", "onPlaygroundTemplateToggle", "AgentExecutionComponent_div_13_Template_app_agent_execution_playground_filesSelected_1_listener", "onFilesSelected", "AgentExecutionComponent_div_13_Template_app_agent_execution_playground_approvalRequested_1_listener", "onApprovalRequested", "ɵɵadvance", "ɵɵproperty", "chatMessages", "isProcessingChat", "agentType", "<PERSON><PERSON><PERSON>", "fileType", "ɵɵtextInterpolate", "node_r4", "name", "ɵɵtemplate", "AgentExecutionComponent_div_22_div_40_div_1_Template", "AgentExecutionComponent_div_22_div_40_div_3_Template", "blueprintPromptNodes", "length", "node_r5", "AgentExecutionComponent_div_22_div_61_div_1_Template", "AgentExecutionComponent_div_22_div_61_div_3_Template", "blueprintKnowledgeNodes", "node_r6", "AgentExecutionComponent_div_22_div_79_div_1_Template", "AgentExecutionComponent_div_22_div_79_div_3_Template", "blueprintModelNodes", "ɵɵelement", "node_r8", "AgentExecutionComponent_div_22_div_80_div_13_div_1_Template", "AgentExecutionComponent_div_22_div_80_div_13_div_3_Template", "blueprintToolNodes", "AgentExecutionComponent_div_22_div_80_Template_div_click_1_listener", "_r7", "toggleBlueprintZone", "AgentExecutionComponent_div_22_div_80__svg_svg_4_Template", "AgentExecutionComponent_div_22_div_80_div_13_Template", "ɵɵclassProp", "ɵɵstyleProp", "isBlueprintZoneExpanded", "node_r10", "AgentExecutionComponent_div_22_div_81_div_15_div_1_Template", "AgentExecutionComponent_div_22_div_81_div_15_div_3_Template", "blueprintGuardrailNodes", "AgentExecutionComponent_div_22_div_81_Template_div_click_1_listener", "_r9", "AgentExecutionComponent_div_22_div_81_div_15_Template", "AgentExecutionComponent_div_22_Template_div_click_26_listener", "_r3", "AgentExecutionComponent_div_22_div_40_Template", "AgentExecutionComponent_div_22_Template_div_click_42_listener", "AgentExecutionComponent_div_22_div_61_Template", "AgentExecutionComponent_div_22_Template_div_click_63_listener", "AgentExecutionComponent_div_22_div_79_Template", "AgentExecutionComponent_div_22_div_80_Template", "AgentExecutionComponent_div_22_div_81_Template", "blueprintCompletionPercentage", "ɵɵtextInterpolate1", "latestAgentResponse", "response", "choices", "text", "AgentExecutionComponent_div_23_div_1_div_3_Template", "AgentExecutionComponent_div_23_div_1_ng_template_4_Template", "ɵɵtemplateRefExtractor", "noIndividualOutput_r11", "taskOutput_r12", "description", "expected_output", "AgentExecutionComponent_div_23_div_2_div_3_div_1_div_4_Template", "AgentExecutionComponent_div_23_div_2_div_3_div_1_div_5_Template", "summary", "i_r13", "raw", "AgentExecutionComponent_div_23_div_2_div_3_div_1_Template", "agentResponse", "agent", "tasksOutputs", "AgentExecutionComponent_div_23_div_2_div_3_Template", "AgentExecutionComponent_div_23_div_2_ng_template_4_Template", "noCollaborativeOutput_r14", "AgentExecutionComponent_div_23_div_1_Template", "AgentExecutionComponent_div_23_div_2_Template", "AgentExecutionComponent", "route", "router", "agentService", "agentPlaygroundService", "tokenStorage", "loaderService", "formBuilder", "toolExecutionService", "navigationTabs", "id", "label", "disabled", "agentId", "agentDetail", "playgroundComp", "activityLogs", "activityProgress", "executionDetails", "isRunning", "status", "notStarted", "inputText", "chatHistory", "agentOutputs", "agentForm", "executionStartTime", "executionCompleted", "executionId", "enableStreamingLog", "enableLogStreaming", "isExecutionComplete", "progressInterval", "destroy$", "selectedTab", "demoTabs", "errorMsg", "resMessage", "taskMessage", "isJsonValid", "disable<PERSON>hat", "selectedFiles", "agentNodes", "userInputList", "progress", "isLoading", "loaderColor", "inputFieldOrder", "currentInputIndex", "activeTabId", "isLeftPanelCollapsed", "activeRightTab", "currentAgentDetails", "buildAgentNodes", "canvasNodes", "canvasEdges", "selected<PERSON><PERSON><PERSON>", "selectedAgentMode", "selectedUseCaseIdentifier", "agentFilesUploadedData", "agentAttachment", "isAgentPlaygroundLoading", "agentPlaygroundDestroy", "agentChatPayload", "agentCode", "promptOptions", "blueprintZonesExpanded", "prompt", "model", "knowledge", "guardrail", "tool", "constructor", "group", "isConversational", "isUseTemplate", "ngOnInit", "console", "log", "crypto", "randomUUID", "params", "subscribe", "queryParams", "loadAgentData", "user", "ai", "from", "ngOnDestroy", "next", "complete", "clearInterval", "onTabChange", "event", "getCollaborativeAgentDetailsById", "handleAgentDataResponse", "error", "getAgentById", "agentData", "agentDetails", "Array", "isArray", "data", "useCaseName", "useCaseCode", "organizationPath", "loadAgentNodes", "mapAgentConfigurationToBlueprint", "message", "trim", "push", "timestamp", "Date", "executeCollaborativeAgent", "executeIndividualAgent", "showAgentError", "get", "value", "agentMode", "useCaseIdentifier", "orgPath", "buildOrganizationPath", "agentIdentifier", "processAgentFilesAndSendMessage", "sendAgentMessageToAPI", "payload", "Number", "getDaUsername", "userInputs", "question", "fileWrapper", "submitAgentExecuteWithFile", "pipe", "res", "handleAgentExecuteResponse", "err", "handleAgentExecuteError", "submitAgentExecute", "agentResponseText", "output", "detail", "lastInteraction", "replace", "errorResponse", "String", "setValue", "files", "saveLogs", "exportResults", "section", "handleControlAction", "action", "navigateBack", "navigate", "mode", "editAgent", "navigateToAgentsList", "toggleLeftPanel", "setActiveRightTab", "tab", "zoneType", "getMetadataFromNavbar", "content", "role", "levelId", "generatePrompt", "formData", "FormData", "for<PERSON>ach", "fileData", "file", "append", "has", "getFileToContent", "fileResponse", "fileContent", "fileResponses", "map", "join", "sendAgentMessageToAPIWithFiles", "fileContents", "generatedResponse", "aiResponseText", "warn", "errorMessage", "pop", "Object", "keys", "nodeCounter", "mapIndividualAgentToBlueprint", "mapCollaborativeAgentToBlueprint", "totalNodes", "config", "config<PERSON><PERSON><PERSON>", "useCaseDetails", "type", "category", "categoryIndex", "categoryId", "categoryName", "configItem", "itemIndex", "config<PERSON><PERSON>", "config<PERSON><PERSON><PERSON>", "kbValue", "toString", "kbIds", "split", "filter", "kbId", "key", "startsWith", "guardrailName", "promptNodes", "modelNodes", "knowledgeNodes", "guardrailNodes", "totalRequired", "currentRequired", "Math", "round", "shouldCreatePromptNode", "goal", "promptNodeName", "substring", "modelReferences", "hasAgentConfigs", "agentConfigs", "modelName", "modelDetails", "modelRef", "modelRefs", "ref", "modelId", "modelDeploymentName", "knowledgeReferences", "knowledgeBaseRef", "kbRefs", "knowledgeBaseId", "knowledgeBase", "kbRef", "collectionName", "indexCollectionName", "kbName", "toolReferences", "userToolReferences", "agentConfigsContent", "hasTools", "tools", "toolsContent", "hasUserTools", "userTools", "userToolsContent", "toolRef", "toolRefs", "toolId", "userToolRef", "userToolRefs", "toolName", "userTool", "userToolId", "userToolName", "toolNodes", "promptCount", "modelCount", "knowledgeCount", "toolCount", "guardrailCount", "isCollaborative", "hasToolNodes", "toolNodeNames", "t", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "AgentServiceService", "i3", "AgentPlaygroundService", "i4", "TokenStorageService", "i5", "LoaderService", "i6", "FormBuilder", "i7", "ToolExecutionService", "selectors", "viewQuery", "AgentExecutionComponent_Query", "rf", "ctx", "AgentExecutionComponent_Template_button_click_3_listener", "AgentExecutionComponent_Template_button_click_10_listener", "AgentExecutionComponent_button_12_Template", "AgentExecutionComponent_div_13_Template", "AgentExecutionComponent_Template_button_click_17_listener", "AgentExecutionComponent_Template_button_click_19_listener", "AgentExecutionComponent_div_22_Template", "AgentExecutionComponent_div_23_Template", "i8", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\agents\\agent-execution\\agent-execution.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\pages\\agents\\agent-execution\\agent-execution.component.html"], "sourcesContent": ["import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { CommonModule } from '@angular/common';\nimport { Subject, takeUntil, switchMap, finalize, catchError, of } from 'rxjs';\nimport { FormBuilder, FormGroup, FormsModule } from '@angular/forms';\n\n// Import child components\nimport { AgentExecutionPlaygroundComponent } from './components/agent-execution-playground/agent-execution-playground.component';\nimport { ChatMessage } from '@shared/components/chat-window/chat-window.component';\n\n// Extended interface for enhanced chat features\ninterface EnhancedChatMessage extends ChatMessage {\n  isLoading?: boolean;\n  status?: 'success' | 'failed' | 'loading';\n  timestamp?: Date;\n}\nimport { IconComponent, TabItem, DropdownOption } from '@ava/play-comp-library';\nimport { AgentServiceService } from '../services/agent-service.service';\nimport { AgentPlaygroundService } from '../build-agents/services/agent-playground.service';\nimport { environment } from '@shared/environments/environment';\nimport { TokenStorageService } from '@shared/auth/services/token-storage.service';\nimport { LoaderService } from '@shared/services/loader/loader.service';\nimport { ToolExecutionService } from '@shared/services/tool-execution/tool-execution.service';\nimport { AvaTab } from '@shared/models/tab.model';\nimport { ExecutionStatus, ActivityLog, ExecutionDetails, OutputItem } from '@shared/models/execution.model';\n\n// Remove duplicate definitions - they're now in shared models\n\n@Component({\n  selector: 'app-agent-execution',\n  standalone: true,\n  imports: [\n    CommonModule,\n    FormsModule,\n    AgentExecutionPlaygroundComponent,\n    IconComponent,\n  ],\n  templateUrl: './agent-execution.component.html',\n  styleUrls: ['./agent-execution.component.scss'],\n})\nexport class AgentExecutionComponent implements OnInit, OnDestroy {\n  navigationTabs: TabItem[] = [\n    { id: 'nav-home', label: 'Agent Activity' },\n    { id: 'nav-products', label: 'Agent Output' },\n    { id: 'nav-services', label: 'Preview', disabled: true },\n  ];\n\n  // Agent details\n  agentId: string | null = null;\n  agentType: string = 'individual';\n  agentName: string = 'Agent';\n  agentDetail: string = '';\n\n  @ViewChild(AgentExecutionPlaygroundComponent, { static: false })\n  playgroundComp!: AgentExecutionPlaygroundComponent;\n\n  // Activity logs\n  activityLogs: ActivityLog[] = [];\n  activityProgress: number = 0;\n  executionDetails?: ExecutionDetails;\n  isRunning: boolean = false;\n  status: ExecutionStatus = ExecutionStatus.notStarted;\n\n  // Chat messages\n  isProcessingChat: boolean = false;\n  inputText = '';\n\n  // Enhanced chat interface properties\n  chatHistory: { user: string; ai: EnhancedChatMessage }[] = []; // Store all chat interactions with status\n\n  // Agent outputs\n  agentOutputs: OutputItem[] = [];\n  latestAgentResponse: any = null; // Store the latest agent response for display\n  public agentForm!: FormGroup;\n  public fileType: string =\n    '.pdf,.doc,.docx,.txt,.csv,.xlsx,.xls,.ppt,.pptx,.png,.jpg,.jpeg,.gif,.bmp,.svg';\n\n  // Execution state\n  executionStartTime: Date | null = null;\n  executionCompleted: boolean = false;\n  executionId!: string;\n\n  enableStreamingLog = environment.enableLogStreaming || 'all';\n\n  public isExecutionComplete: boolean = false;\n  progressInterval: any;\n\n  private destroy$ = new Subject<void>();\n  selectedTab: string = 'Agent Activity';\n  demoTabs: AvaTab[] = [\n    { id: 'tab1', label: 'History' },\n    { id: 'tab2', label: 'Blueprint' },\n    { id: 'tab3', label: 'Agent Output' },\n  ];\n\n  errorMsg = false;\n  resMessage: any;\n  taskMessage: any[] = [];\n  isJsonValid = false;\n  disableChat: boolean = false;\n  selectedFiles: File[] = [];\n  agentNodes: any[] = [];\n  userInputList: any[] = [];\n  progress = 0;\n  isLoading = false;\n  loaderColor: string = '';\n\n  inputFieldOrder: string[] = [];\n  currentInputIndex: number = 0;\n  activeTabId: string = 'nav-home';\n\n  // Panel state properties\n  isLeftPanelCollapsed: boolean = false;\n  activeRightTab: string = 'blueprint';\n\n  // Agent-specific properties\n  currentAgentDetails: any = null;\n  buildAgentNodes: any[] = [];\n  canvasNodes: any[] = [];\n  canvasEdges: any[] = [];\n  selectedPrompt: string = '';\n  selectedAgentMode: string = '';\n  selectedUseCaseIdentifier: string = '';\n  agentFilesUploadedData: any[] = [];\n  agentAttachment: string[] = [];\n  isAgentPlaygroundLoading = false;\n  agentPlaygroundDestroy = new Subject<boolean>();\n  agentChatPayload: any[] = [];\n  agentCode: string = '';\n  promptOptions: DropdownOption[] = [];\n\n  // Custom Blueprint Display Properties\n  blueprintCompletionPercentage: number = 0;\n  blueprintPromptNodes: any[] = [];\n  blueprintModelNodes: any[] = [];\n  blueprintKnowledgeNodes: any[] = [];\n  blueprintGuardrailNodes: any[] = [];\n  blueprintToolNodes: any[] = [];\n\n  // Blueprint zone expansion state\n  private blueprintZonesExpanded: { [key: string]: boolean } = {\n    prompt: true,\n    model: true,\n    knowledge: true,\n    guardrail: true,\n    tool: true,\n  };\n\n  // Blueprint panel properties (using existing arrays above)\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private agentService: AgentServiceService,\n    private agentPlaygroundService: AgentPlaygroundService,\n    private tokenStorage: TokenStorageService,\n    private loaderService: LoaderService,\n    private formBuilder: FormBuilder,\n    private toolExecutionService: ToolExecutionService,\n  ) {\n    this.agentForm = this.formBuilder.group({\n      isConversational: [true],\n      isUseTemplate: [false],\n    });\n  }\n\n  ngOnInit(): void {\n    console.log(' SHARED COMPONENT INITIALIZED! ');\n    this.executionId = crypto.randomUUID();\n\n    this.route.params.subscribe((params) => {\n      this.agentType = params['type'] || 'individual';\n      console.log(' SHARED: Agent type set to:', this.agentType);\n    });\n\n    this.route.queryParams.subscribe((params) => {\n      if (params['id']) {\n        this.agentId = params['id'];\n        this.loadAgentData(params['id']);\n      }\n    });\n\n    // Initialize chat messages\n    this.chatHistory = [\n      {\n        user: '',\n        ai: {\n          from: 'ai',\n          text: `Hi there! I am ${this.agentName || 'your agent'}. How can I help you today?`,\n        },\n      },\n    ];\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n    if (this.progressInterval) {\n      clearInterval(this.progressInterval);\n    }\n  }\n\n  onTabChange(event: { id: string; label: string }) {\n    this.activeTabId = event.id;\n    this.selectedTab = event.label;\n  }\n\n  loadAgentData(agentId: string): void {\n    this.isLoading = true;\n\n    // Load agent data based on type\n    if (this.agentType === 'collaborative') {\n      this.agentService.getCollaborativeAgentDetailsById(agentId).subscribe({\n        next: (response: any) => {\n          this.handleAgentDataResponse(response);\n        },\n        error: (error: any) => {\n          console.error('Error loading collaborative agent:', error);\n          this.isLoading = false;\n        },\n      });\n    } else {\n      this.agentService.getAgentById(agentId).subscribe({\n        next: (response: any) => {\n          this.handleAgentDataResponse(response);\n        },\n        error: (error: any) => {\n          console.error('Error loading individual agent:', error);\n          this.isLoading = false;\n        },\n      });\n    }\n  }\n\n  private handleAgentDataResponse(response: any): void {\n    this.isLoading = false;\n\n    // Extract agent details\n    let agentData;\n    if (\n      response.agentDetails &&\n      Array.isArray(response.agentDetails) &&\n      response.agentDetails.length > 0\n    ) {\n      agentData = response.agentDetails[0];\n    } else if (response.agentDetail) {\n      agentData = response.agentDetail;\n    } else if (response.data) {\n      agentData = response.data;\n    } else {\n      agentData = response;\n    }\n\n    if (agentData) {\n      this.currentAgentDetails = agentData;\n      this.agentName = agentData.name || agentData.agentName || 'Agent';\n      this.agentDetail = agentData.description || agentData.agentDetail || '';\n\n      // For individual agents, set up the required properties for playground functionality\n      if (this.agentType === 'individual') {\n        // Set selectedPrompt to agent name or useCaseName to bypass dropdown requirement\n        this.selectedPrompt =\n          agentData.useCaseName || agentData.name || 'loaded-agent';\n\n        // Set selectedAgentMode for API calls - use useCaseCode if available\n        this.selectedAgentMode =\n          agentData.useCaseCode ||\n          agentData.useCaseName ||\n          agentData.name ||\n          '';\n\n        // Set useCaseIdentifier - use organizationPath if available\n        if (agentData.organizationPath) {\n          this.selectedUseCaseIdentifier = agentData.organizationPath;\n        } else if (agentData.useCaseCode) {\n          this.selectedUseCaseIdentifier = agentData.useCaseCode;\n        } else if (agentData.useCaseName) {\n          this.selectedUseCaseIdentifier = agentData.useCaseName;\n        }\n      }\n\n      // Update chat message with agent name\n      if (this.chatHistory.length > 0) {\n        this.chatHistory[0].ai.text = `Hi there! I am ${this.agentName}. How can I help you today?`;\n      }\n\n      // Load agent nodes and configuration\n      this.loadAgentNodes(agentData);\n    }\n  }\n\n  private loadAgentNodes(agentData: any): void {\n    // Map agent configuration to blueprint panel\n    this.mapAgentConfigurationToBlueprint(agentData);\n  }\n\n  handleChatMessage(message: string): void {\n    if (!message.trim() || this.isProcessingChat) return;\n\n    this.isProcessingChat = true;\n    this.activeRightTab = 'output'; // Switch to output tab\n\n    // Add user message and AI loading state to chat history\n    this.chatHistory.push({\n      user: message,\n      ai: {\n        from: 'ai',\n        text: '...',\n        isLoading: true,\n        status: 'loading',\n        timestamp: new Date(),\n      },\n    });\n\n    // Determine agent type and call the appropriate API\n    if (this.agentType === 'collaborative') {\n      this.executeCollaborativeAgent(message);\n    } else {\n      this.executeIndividualAgent(message);\n    }\n  }\n\n  private executeIndividualAgent(message: string): void {\n    if (!this.currentAgentDetails && (!this.selectedPrompt || this.selectedPrompt === 'default')) {\n      this.showAgentError('Agent details are not loaded. Please try refreshing the page.');\n      this.isProcessingChat = false;\n      return;\n    }\n\n    const isConversational = this.agentForm.get('isConversational')?.value || false;\n    const isUseTemplate = this.agentForm.get('isUseTemplate')?.value || false;\n\n    const agentMode = this.agentCode || this.selectedAgentMode || this.currentAgentDetails?.useCaseCode || this.currentAgentDetails?.useCaseName || this.currentAgentDetails?.name || this.selectedPrompt;\n\n    let useCaseIdentifier = this.selectedUseCaseIdentifier;\n    if (!useCaseIdentifier) {\n      if (this.currentAgentDetails?.organizationPath) {\n        useCaseIdentifier = this.currentAgentDetails.organizationPath;\n      } else {\n        const orgPath = this.buildOrganizationPath();\n        const agentIdentifier = this.currentAgentDetails?.useCaseCode || this.currentAgentDetails?.useCaseName || this.currentAgentDetails?.name || agentMode;\n        useCaseIdentifier = `${agentIdentifier}${orgPath}`;\n      }\n    }\n\n    if (this.agentFilesUploadedData.length > 0) {\n      this.processAgentFilesAndSendMessage(message, agentMode, useCaseIdentifier, isConversational, isUseTemplate);\n      return;\n    }\n\n    this.sendAgentMessageToAPI(message, agentMode, useCaseIdentifier, isConversational, isUseTemplate);\n  }\n\n  private executeCollaborativeAgent(message: string): void {\n    const payload = {\n      executionId: this.executionId,\n      agentId: Number(this.agentId),\n      user: this.tokenStorage.getDaUsername() || '<EMAIL>',\n      userInputs: { question: message },\n    };\n\n    if (this.agentFilesUploadedData.length > 0) {\n      const fileWrapper = this.agentFilesUploadedData[0];\n      this.agentPlaygroundService.submitAgentExecuteWithFile(payload, fileWrapper)\n        .pipe(\n          finalize(() => { this.isProcessingChat = false; }),\n          takeUntil(this.agentPlaygroundDestroy)\n        )\n        .subscribe({\n          next: (res) => this.handleAgentExecuteResponse(res),\n          error: (err: any) => this.handleAgentExecuteError(err),\n        });\n    } else {\n      this.agentPlaygroundService.submitAgentExecute(payload)\n        .pipe(\n          finalize(() => { this.isProcessingChat = false; }),\n          takeUntil(this.agentPlaygroundDestroy)\n        )\n        .subscribe({\n          next: (res) => this.handleAgentExecuteResponse(res),\n          error: (err: any) => this.handleAgentExecuteError(err),\n        });\n    }\n  }\n\n  private handleAgentExecuteResponse(response: any): void {\n    this.isProcessingChat = false;\n    this.latestAgentResponse = response;\n\n    const agentResponseText = \n      response?.agentResponse?.agent?.output || \n      response?.agentResponse?.agent?.response || \n      response?.agentResponse?.detail || \n      'No response found.';\n\n    const lastInteraction = this.chatHistory[this.chatHistory.length - 1];\n    if (lastInteraction && lastInteraction.ai.isLoading) {\n      lastInteraction.ai.text = agentResponseText.replace(/\\\\n/g, '\\n');\n      lastInteraction.ai.isLoading = false;\n      lastInteraction.ai.status = 'success';\n      lastInteraction.ai.timestamp = new Date();\n    }\n  }\n\n  private handleAgentExecuteError(err: any): void {\n    this.isProcessingChat = false;\n    const errorResponse = err?.error?.message || err?.message || 'An unexpected error occurred.';\n\n    const lastInteraction = this.chatHistory[this.chatHistory.length - 1];\n    if (lastInteraction && lastInteraction.ai.isLoading) {\n      lastInteraction.ai.text = errorResponse;\n      lastInteraction.ai.isLoading = false;\n      lastInteraction.ai.status = 'failed';\n      lastInteraction.ai.timestamp = new Date();\n    }\n  }\n\n  onPromptChanged(prompt: DropdownOption): void {\n    this.inputText = prompt.name || String(prompt.value) || '';\n  }\n\n  onPlaygroundConversationalToggle(value: boolean): void {\n    // Update the form control\n    this.agentForm.get('isConversational')?.setValue(value);\n\n    // When conversational mode is turned off, clear the conversation history\n    // This ensures that the next message will be treated as a fresh start\n    if (!value) {\n      this.agentChatPayload = [];\n      console.log(\n        'Conversational mode disabled - cleared chat payload history',\n      );\n    } else {\n      console.log('Conversational mode enabled - will maintain chat history');\n    }\n  }\n\n  onPlaygroundTemplateToggle(value: boolean): void {\n    // Update the form control\n    this.agentForm.get('isUseTemplate')?.setValue(value);\n    console.log('Template mode toggled:', value);\n  }\n\n  onFilesSelected(files: any[]): void {\n    this.selectedFiles = files;\n    // Update agentFilesUploadedData for agent execution\n    this.agentFilesUploadedData = files;\n  }\n\n  onApprovalRequested(): void {\n    // Handle approval request\n  }\n\n  saveLogs(): void {\n    // Save execution logs\n  }\n\n  exportResults(section: 'activity' | 'output'): void {\n    // Export results\n  }\n\n  handleControlAction(action: 'play' | 'pause' | 'stop'): void {\n    // Handle execution control actions\n  }\n\n  navigateBack(): void {\n    this.router.navigate(['/build/agents', this.agentType], {\n      queryParams: { id: this.agentId, mode: 'view' },\n    });\n  }\n\n  editAgent(): void {\n    this.router.navigate(['/build/agents', this.agentType], {\n      queryParams: { id: this.agentId, mode: 'edit' },\n    });\n  }\n\n  navigateToAgentsList(): void {\n    this.router.navigate(['/build/agents']);\n  }\n\n  toggleLeftPanel(): void {\n    this.isLeftPanelCollapsed = !this.isLeftPanelCollapsed;\n  }\n\n  setActiveRightTab(tab: string): void {\n    this.activeRightTab = tab;\n  }\n\n  // Blueprint zone management methods\n  toggleBlueprintZone(zoneType: string): void {\n    this.blueprintZonesExpanded[zoneType] =\n      !this.blueprintZonesExpanded[zoneType];\n  }\n\n  isBlueprintZoneExpanded(zoneType: string): boolean {\n    return this.blueprintZonesExpanded[zoneType] || false;\n  }\n\n  // API and helper methods from build-agents component\n  private showAgentError(message: string): void {\n    this.chatHistory.push({\n      user: '',\n      ai: {\n        from: 'ai',\n        text: message,\n      },\n    });\n  }\n\n  private buildOrganizationPath(): string {\n    // Simple implementation - in real scenario this would be from navbar/metadata\n    return '';\n  }\n\n  private getMetadataFromNavbar(): { levelId?: number } {\n    // Simple implementation - in real scenario this would get org level mapping\n    return {};\n  }\n\n  private sendAgentMessageToAPI(\n    message: string,\n    mode: string,\n    useCaseIdentifier: string,\n    isConversational: boolean,\n    isUseTemplate: boolean,\n  ): void {\n    if (isConversational) {\n      this.agentChatPayload.push({ content: message, role: 'user' });\n    }\n\n    const payload = isConversational ? this.agentChatPayload : message;\n    const { levelId } = this.getMetadataFromNavbar();\n\n    this.agentPlaygroundService\n      .generatePrompt(\n        payload,\n        mode,\n        isConversational,\n        isUseTemplate,\n        this.agentAttachment,\n        useCaseIdentifier,\n        '',\n        levelId,\n      )\n      .pipe(\n        finalize(() => {\n          this.isProcessingChat = false;\n        }),\n        takeUntil(this.agentPlaygroundDestroy),\n      )\n      .subscribe({\n        next: (res: any) => this.handleAgentExecuteResponse(res),\n        error: (err: any) => this.handleAgentExecuteError(err),\n      });\n  }\n\n  private processAgentFilesAndSendMessage(\n    message: string,\n    mode: string,\n    useCaseIdentifier: string,\n    isConversational: boolean,\n    isUseTemplate: boolean,\n  ): void {\n    const formData = new FormData();\n    this.agentFilesUploadedData.forEach((fileData) => {\n      if (fileData.file) {\n        formData.append('files', fileData.file);\n      }\n    });\n\n    if (formData.has('files')) {\n      this.agentPlaygroundService\n        .getFileToContent(formData)\n        .pipe(\n          switchMap((fileResponse) => {\n            const fileContent =\n              fileResponse?.fileResponses\n                ?.map((response: any) => response.fileContent)\n                ?.join('\\n') || '';\n            this.sendAgentMessageToAPIWithFiles(\n              message,\n              mode,\n              useCaseIdentifier,\n              isConversational,\n              isUseTemplate,\n              fileContent,\n            );\n            return of(null);\n          }),\n          catchError((error) => {\n            console.error('Error parsing files:', error);\n            this.sendAgentMessageToAPI(\n              message,\n              mode,\n              useCaseIdentifier,\n              isConversational,\n              isUseTemplate,\n            );\n            return of(null);\n          }),\n        )\n        .subscribe();\n    } else {\n      this.sendAgentMessageToAPI(\n        message,\n        mode,\n        useCaseIdentifier,\n        isConversational,\n        isUseTemplate,\n      );\n    }\n  }\n\n\n\n  private sendAgentMessageToAPIWithFiles(\n    message: string,\n    mode: string,\n    useCaseIdentifier: string,\n    isConversational: boolean,\n    isUseTemplate: boolean,\n    fileContents: string,\n  ): void {\n    if (isConversational) {\n      this.agentChatPayload.push({ content: message, role: 'user' });\n    }\n    const payload = isConversational ? this.agentChatPayload : message;\n    const { levelId } = this.getMetadataFromNavbar();\n\n    this.agentPlaygroundService\n      .generatePrompt(\n        payload,\n        mode,\n        isConversational,\n        isUseTemplate,\n        this.agentAttachment,\n        useCaseIdentifier,\n        fileContents,\n        levelId,\n      )\n      .pipe(\n        finalize(() => {\n          this.isProcessingChat = false;\n          this.isAgentPlaygroundLoading = false;\n        }),\n        takeUntil(this.agentPlaygroundDestroy),\n      )\n      .subscribe({\n        next: (generatedResponse: any) => {\n          if (\n            generatedResponse?.response &&\n            generatedResponse?.response?.choices\n          ) {\n            const aiResponseText = generatedResponse.response.choices[0].text;\n            this.chatMessages = [\n              ...this.chatMessages,\n              { from: 'ai', text: aiResponseText },\n            ];\n            if (isConversational) {\n              this.agentChatPayload.push({\n                content: aiResponseText,\n                role: 'assistant',\n              });\n            }\n          } else {\n            console.warn('Unexpected API response format:', generatedResponse);\n            this.showAgentError(\n              'Received unexpected response format from API.',\n            );\n          }\n        },\n        error: (error: any) => {\n          console.error('API Error:', error);\n          const errorMessage =\n            error?.error?.message ||\n            'An error occurred while processing your request.';\n          this.showAgentError(errorMessage);\n          if (isConversational && this.agentChatPayload.length > 0) {\n            this.agentChatPayload.pop();\n          }\n        },\n      });\n  }\n\n  // Blueprint panel methods\n  private mapAgentConfigurationToBlueprint(agentData: any): void {\n    if (!agentData) {\n      console.warn('No agent data provided for blueprint');\n      return;\n    }\n\n    console.log('🔍 DEBUG: Full agent data received:', agentData);\n    console.log('🔍 DEBUG: Agent type:', this.agentType);\n    console.log('🔍 DEBUG: Agent data keys:', Object.keys(agentData));\n\n    // Clear existing nodes\n    this.buildAgentNodes = [];\n    this.canvasNodes = [];\n\n    let nodeCounter = 1;\n\n    // Map agent configuration to nodes based on agent type\n    if (this.agentType === 'individual') {\n      this.mapIndividualAgentToBlueprint(agentData, nodeCounter);\n    } else if (this.agentType === 'collaborative') {\n      this.mapCollaborativeAgentToBlueprint(agentData, nodeCounter);\n    }\n\n    console.log('🎯 Blueprint nodes mapped:', {\n      buildAgentNodes: this.buildAgentNodes,\n      canvasNodes: this.canvasNodes,\n      totalNodes: this.buildAgentNodes.length,\n    });\n  }\n\n  private mapIndividualAgentToBlueprint(\n    agentData: any,\n    nodeCounter: number,\n  ): void {\n    console.log('🔍 Individual agent mapping - checking fields:', {\n      config: agentData.config,\n      configLength: agentData.config?.length,\n      useCaseName: agentData.useCaseName,\n      prompt: agentData.prompt,\n      useCaseDetails: agentData.useCaseDetails,\n    });\n\n    // Clear existing blueprint nodes\n    this.blueprintPromptNodes = [];\n    this.blueprintModelNodes = [];\n    this.blueprintKnowledgeNodes = [];\n    this.blueprintGuardrailNodes = [];\n\n    // Add prompt node from \"prompt\" field\n    if (agentData.prompt) {\n      this.blueprintPromptNodes.push({\n        id: `prompt-${nodeCounter++}`,\n        name: agentData.prompt,\n        type: 'prompt',\n      });\n      console.log('✅ Added prompt node:', agentData.prompt);\n    }\n\n    // Process the config array to extract model, knowledge bases, and guardrails\n    if (agentData.config && Array.isArray(agentData.config)) {\n      console.log(\n        '🔍 Processing config array with length:',\n        agentData.config.length,\n      );\n\n      agentData.config.forEach((category: any, categoryIndex: number) => {\n        console.log(\n          `🔍 Category ${categoryIndex} (ID: ${category.categoryId}):`,\n          category.categoryName,\n        );\n\n        if (category.config && Array.isArray(category.config)) {\n          console.log(\n            `🔍 Category ${categoryIndex} has ${category.config.length} config items`,\n          );\n\n          category.config.forEach((configItem: any, itemIndex: number) => {\n            console.log(`🔍 Config item ${categoryIndex}-${itemIndex}:`, {\n              configKey: configItem.configKey,\n              configValue: configItem.configValue,\n              categoryId: configItem.categoryId,\n            });\n\n            // Handle AI Model from categoryId 1\n            if (\n              configItem.categoryId === 1 &&\n              configItem.configKey === 'MODEL' &&\n              configItem.configValue\n            ) {\n              console.log(\n                '✅ Adding AI model node from categoryId 1:',\n                configItem.configValue,\n              );\n              this.blueprintModelNodes.push({\n                id: `model-${nodeCounter++}`,\n                name: `${configItem.configKey}`,\n                type: 'model',\n              });\n            }\n\n            // Handle Knowledge Base from categoryId 2\n            if (\n              configItem.categoryId === 2 &&\n              configItem.configKey === 'RAG_KNOWLEDGEBASE_NAME' &&\n              configItem.configValue\n            ) {\n              console.log(\n                '✅ Adding knowledge base nodes from categoryId 2:',\n                configItem.configValue,\n              );\n              const kbValue = configItem.configValue.toString();\n              const kbIds = kbValue\n                .split(',')\n                .map((id: string) => id.trim())\n                .filter((id: string) => id);\n\n              kbIds.forEach((kbId: string) => {\n                this.blueprintKnowledgeNodes.push({\n                  id: `knowledge-${nodeCounter++}`,\n                  name: `Knowledge Base: ${kbId}`,\n                  type: 'knowledge',\n                });\n              });\n            }\n\n            // Handle Guardrails from categoryId 3 where configValue is true\n            if (\n              configItem.categoryId === 3 &&\n              configItem.configValue === 'true'\n            ) {\n              console.log('✅ Found enabled guardrail from categoryId 3:', {\n                key: configItem.configKey,\n                value: configItem.configValue,\n              });\n\n              if (configItem.configKey === 'ENABLE_GUARDRAILS') {\n                // Only add one general guardrail node if not already added\n                if (this.blueprintGuardrailNodes.length === 0) {\n                  this.blueprintGuardrailNodes.push({\n                    id: `guardrail-${nodeCounter++}`,\n                    name: 'Guardrails Enabled',\n                    type: 'guardrail',\n                  });\n                }\n              } else if (configItem.configKey.startsWith('GUARDRAIL_')) {\n                // Add specific guardrail nodes for enabled guardrails\n                let guardrailName = configItem.configKey;\n                if (guardrailName.startsWith('GUARDRAIL_')) {\n                  guardrailName = guardrailName\n                    .replace('GUARDRAIL_', '')\n                    .replace(/_/g, ' ');\n                }\n\n                this.blueprintGuardrailNodes.push({\n                  id: `guardrail-${nodeCounter++}`,\n                  name: `${guardrailName}`,\n                  type: 'guardrail',\n                });\n              }\n            }\n          });\n        }\n      });\n    }\n\n    console.log('🎯 Final blueprint nodes:', {\n      promptNodes: this.blueprintPromptNodes,\n      modelNodes: this.blueprintModelNodes,\n      knowledgeNodes: this.blueprintKnowledgeNodes,\n      guardrailNodes: this.blueprintGuardrailNodes,\n    });\n\n    // Calculate completion percentage\n    const totalRequired = 2; // Prompt + Model are required\n    const currentRequired =\n      this.blueprintPromptNodes.length + this.blueprintModelNodes.length;\n    this.blueprintCompletionPercentage = Math.round(\n      (currentRequired / totalRequired) * 100,\n    );\n  }\n\n  private mapCollaborativeAgentToBlueprint(\n    agentData: any,\n    nodeCounter: number,\n  ): void {\n    console.log(\n      '🚀 SHARED COMPONENT: mapCollaborativeAgentToBlueprint called!',\n    );\n    console.log('🔍 DEBUG: Collaborative agent data received:', agentData);\n    console.log(\n      '🔍 DEBUG: Collaborative agent data keys:',\n      Object.keys(agentData),\n    );\n    console.log('🔍 DEBUG: Agent type in component:', this.agentType);\n    console.log('🔍 DEBUG: Current nodeCounter:', nodeCounter);\n\n    // Clear existing blueprint nodes\n    this.blueprintPromptNodes = [];\n    this.blueprintModelNodes = [];\n    this.blueprintKnowledgeNodes = [];\n    this.blueprintToolNodes = [];\n    this.blueprintGuardrailNodes = [];\n\n    console.log('🔍 DEBUG: Cleared all blueprint node arrays');\n\n    // Add prompt node - handle different prompt structures for collaborative agents\n    const shouldCreatePromptNode =\n      agentData.goal || agentData.role || agentData.description;\n\n    console.log('🔍 DEBUG: Checking prompt node creation:', {\n      goal: agentData.goal,\n      role: agentData.role,\n      description: agentData.description,\n      shouldCreatePromptNode,\n    });\n\n    if (shouldCreatePromptNode) {\n      let promptNodeName =\n        agentData.goal ||\n        agentData.role ||\n        agentData.description ||\n        'Collaborative Agent Prompt';\n\n      // Truncate prompt if too long for display\n      if (promptNodeName.length > 150) {\n        promptNodeName = promptNodeName.substring(0, 150) + '...';\n      }\n\n      this.blueprintPromptNodes.push({\n        id: `prompt-${nodeCounter++}`,\n        name: promptNodeName,\n        type: 'prompt',\n      });\n      console.log('✅ Added collaborative prompt node:', promptNodeName);\n    }\n\n    // Add model nodes - handle both old and new API formats like build-agents\n    let modelReferences = [];\n\n    console.log('🔍 DEBUG: Checking model data:', {\n      hasAgentConfigs: !!agentData.agentConfigs,\n      agentConfigs: agentData.agentConfigs,\n      model: agentData.model,\n      modelName: agentData.modelName,\n      modelDetails: agentData.modelDetails,\n    });\n\n    // New API format: agentConfigs.modelRef (array of model IDs or objects)\n    if (agentData.agentConfigs && agentData.agentConfigs.modelRef) {\n      const modelRefs = Array.isArray(agentData.agentConfigs.modelRef)\n        ? agentData.agentConfigs.modelRef\n        : [agentData.agentConfigs.modelRef];\n\n      modelReferences = modelRefs.map((ref: any) => {\n        if (typeof ref === 'number' || typeof ref === 'string') {\n          return { modelId: ref };\n        }\n        return ref;\n      });\n    }\n    // Old API format: modelDetails\n    else if (agentData.modelDetails) {\n      modelReferences = [agentData.modelDetails];\n    }\n    // Fallback: check for model or modelName directly\n    else if (agentData.model || agentData.modelName) {\n      modelReferences = [{ modelId: agentData.model || agentData.modelName }];\n    }\n\n    modelReferences.forEach((modelRef: any) => {\n      const modelId = modelRef.modelId || modelRef.id;\n      const modelName =\n        modelRef.model ||\n        modelRef.modelDeploymentName ||\n        `Model ID: ${modelId}`;\n\n      this.blueprintModelNodes.push({\n        id: `model-${nodeCounter++}`,\n        name: modelName,\n        type: 'model',\n      });\n      console.log('✅ Added collaborative model node:', modelName);\n    });\n\n    // Add knowledge base nodes - handle both old and new API formats\n    let knowledgeReferences = [];\n\n    // New API format: agentConfigs.knowledgeBaseRef (array of KB IDs or objects)\n    if (agentData.agentConfigs && agentData.agentConfigs.knowledgeBaseRef) {\n      const kbRefs = Array.isArray(agentData.agentConfigs.knowledgeBaseRef)\n        ? agentData.agentConfigs.knowledgeBaseRef\n        : [agentData.agentConfigs.knowledgeBaseRef];\n\n      knowledgeReferences = kbRefs.map((ref: any) => {\n        if (typeof ref === 'number' || typeof ref === 'string') {\n          return { knowledgeBaseId: ref };\n        }\n        return ref;\n      });\n    }\n    // Old API format: knowledgeBase\n    else if (\n      agentData.knowledgeBase &&\n      Array.isArray(agentData.knowledgeBase)\n    ) {\n      knowledgeReferences = agentData.knowledgeBase;\n    }\n\n    knowledgeReferences.forEach((kbRef: any) => {\n      const kbId = kbRef.knowledgeBaseId || kbRef.id;\n      const collectionName = kbRef.indexCollectionName || kbRef.name;\n      const kbName = collectionName || `Knowledge Base ID: ${kbId}`;\n\n      this.blueprintKnowledgeNodes.push({\n        id: `knowledge-${nodeCounter++}`,\n        name: kbName,\n        type: 'knowledge',\n      });\n      console.log('✅ Added collaborative knowledge node:', kbName);\n    });\n\n    // Add tool nodes - handle both old and new API formats like build-agents\n    let toolReferences = [];\n    let userToolReferences = [];\n\n    console.log('🔍 DEBUG: Checking tool data:', {\n      hasAgentConfigs: !!agentData.agentConfigs,\n      agentConfigsContent: agentData.agentConfigs,\n      hasTools: agentData.tools,\n      toolsContent: agentData.tools,\n      hasUserTools: agentData.userTools,\n      userToolsContent: agentData.userTools,\n    });\n\n    // New API format: agentConfigs.toolRef and agentConfigs.userToolRef\n    if (agentData.agentConfigs) {\n      if (agentData.agentConfigs.toolRef) {\n        const toolRefs = Array.isArray(agentData.agentConfigs.toolRef)\n          ? agentData.agentConfigs.toolRef\n          : [agentData.agentConfigs.toolRef];\n\n        toolReferences = toolRefs.map((ref: any) => {\n          if (typeof ref === 'number' || typeof ref === 'string') {\n            return { toolId: ref };\n          }\n          return ref;\n        });\n      }\n      if (agentData.agentConfigs.userToolRef) {\n        const userToolRefs = Array.isArray(agentData.agentConfigs.userToolRef)\n          ? agentData.agentConfigs.userToolRef\n          : [agentData.agentConfigs.userToolRef];\n\n        userToolReferences = userToolRefs.map((ref: any) => {\n          if (typeof ref === 'number' || typeof ref === 'string') {\n            return { toolId: ref };\n          }\n          return ref;\n        });\n      }\n    }\n    // Old API format: tools and userTools\n    else {\n      if (agentData.tools && Array.isArray(agentData.tools)) {\n        toolReferences = agentData.tools;\n      }\n      if (agentData.userTools && Array.isArray(agentData.userTools)) {\n        userToolReferences = agentData.userTools;\n      }\n    }\n\n    // Process built-in tools\n    toolReferences.forEach((tool: any) => {\n      const toolId = tool.toolId || tool.id;\n      const toolName = tool.toolName || tool.name || `Tool ID: ${toolId}`;\n\n      this.blueprintToolNodes.push({\n        id: `tool-${nodeCounter++}`,\n        name: toolName,\n        type: 'tool',\n      });\n      console.log('✅ Added collaborative builtin tool node:', toolName);\n    });\n\n    // Process user tools\n    userToolReferences.forEach((userTool: any) => {\n      const userToolId = userTool.toolId || userTool.id;\n      const userToolName =\n        userTool.toolName || userTool.name || `User Tool ID: ${userToolId}`;\n\n      this.blueprintToolNodes.push({\n        id: `tool-${nodeCounter++}`,\n        name: userToolName,\n        type: 'tool',\n      });\n      console.log('✅ Added collaborative user tool node:', userToolName);\n    });\n\n    console.log('🎯 Final collaborative blueprint nodes:', {\n      promptNodes: this.blueprintPromptNodes,\n      modelNodes: this.blueprintModelNodes,\n      knowledgeNodes: this.blueprintKnowledgeNodes,\n      toolNodes: this.blueprintToolNodes,\n      guardrailNodes: this.blueprintGuardrailNodes,\n    });\n\n    // Debug: Check blueprint node arrays lengths\n    console.log('📊 Blueprint node counts:', {\n      promptCount: this.blueprintPromptNodes.length,\n      modelCount: this.blueprintModelNodes.length,\n      knowledgeCount: this.blueprintKnowledgeNodes.length,\n      toolCount: this.blueprintToolNodes.length,\n      guardrailCount: this.blueprintGuardrailNodes.length,\n    });\n\n    // Debug: Check if tools zone will be visible\n    console.log('🔧 Tools zone debug:', {\n      agentType: this.agentType,\n      isCollaborative: this.agentType === 'collaborative',\n      hasToolNodes: this.blueprintToolNodes.length > 0,\n      toolNodeNames: this.blueprintToolNodes.map((t) => t.name),\n    });\n\n    // Calculate completion percentage\n    const totalRequired = 2; // Prompt + Model are required\n    const currentRequired =\n      this.blueprintPromptNodes.length + this.blueprintModelNodes.length;\n    this.blueprintCompletionPercentage = Math.round(\n      (currentRequired / totalRequired) * 100,\n    );\n  }\n}\n", "<div class=\"agent-execution-container\">\r\n  <!-- Top Navigation Bar -->\r\n  <div class=\"top-nav-bar\">\r\n    <div class=\"nav-left\">\r\n      <button class=\"back-button\" (click)=\"navigateBack()\" type=\"button\">\r\n        <ava-icon\r\n          iconName=\"ArrowLeft\"\r\n          iconSize=\"16\"\r\n          iconColor=\"#374151\"\r\n        ></ava-icon>\r\n        <span class=\"agent-name\">{{ agentName }}</span>\r\n      </button>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Main Content with Two Panels -->\r\n  <div class=\"main-content\">\r\n    <!-- Left Panel: Playground -->\r\n    <div class=\"left-panel\" [class.collapsed]=\"isLeftPanelCollapsed\">\r\n      <div class=\"panel-header\">\r\n        <button class=\"collapse-btn\" (click)=\"toggleLeftPanel()\" type=\"button\">\r\n          <ava-icon\r\n            [iconName]=\"isLeftPanelCollapsed ? 'ChevronRight' : 'PanelLeft'\"\r\n            iconSize=\"16\"\r\n            iconColor=\"#6B7280\"\r\n          >\r\n          </ava-icon>\r\n        </button>\r\n        <button\r\n          class=\"history-btn\"\r\n          *ngIf=\"!isLeftPanelCollapsed\"\r\n          type=\"button\"\r\n          disabled\r\n        >\r\n          History\r\n        </button>\r\n      </div>\r\n\r\n      <div class=\"panel-content\" *ngIf=\"!isLeftPanelCollapsed\">\r\n        <app-agent-execution-playground\r\n          [messages]=\"chatMessages\"\r\n          [isLoading]=\"isProcessingChat\"\r\n          [agentType]=\"agentType\"\r\n          [showChatInteractionToggles]=\"agentType === 'individual'\"\r\n          [showAiPrincipleToggle]=\"true\"\r\n          [showApprovalButton]=\"false\"\r\n          [showDropdown]=\"false\"\r\n          [showAgentNameInput]=\"true\"\r\n          [showFileUploadButton]=\"true\"\r\n          [displayedAgentName]=\"agentName\"\r\n          [agentNamePlaceholder]=\"'Current Agent Name'\"\r\n          [acceptedFileType]=\"fileType\"\r\n          (promptChange)=\"onPromptChanged($event)\"\r\n          (messageSent)=\"handleChatMessage($event)\"\r\n          (conversationalToggle)=\"onPlaygroundConversationalToggle($event)\"\r\n          (templateToggle)=\"onPlaygroundTemplateToggle($event)\"\r\n          (filesSelected)=\"onFilesSelected($event)\"\r\n          (approvalRequested)=\"onApprovalRequested()\"\r\n        >\r\n        </app-agent-execution-playground>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Right Panel: Blueprint/Output -->\r\n    <div class=\"right-panel\">\r\n      <!-- Right Panel Header -->\r\n      <div class=\"panel-header\">\r\n        <div class=\"tabs-container\">\r\n          <button\r\n            class=\"tab-btn\"\r\n            [class.active]=\"activeRightTab === 'blueprint'\"\r\n            (click)=\"setActiveRightTab('blueprint')\"\r\n          >\r\n            Blueprint\r\n          </button>\r\n          <button\r\n            class=\"tab-btn\"\r\n            [class.active]=\"activeRightTab === 'output'\"\r\n            (click)=\"setActiveRightTab('output')\"\r\n          >\r\n            Agent Output\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"panel-content\">\r\n        <!-- Blueprint Content -->\r\n        <div *ngIf=\"activeRightTab === 'blueprint'\" class=\"blueprint-content\">\r\n          <div class=\"blueprint-header\">\r\n            <h3>Agent Blueprint</h3>\r\n          </div>\r\n\r\n          <!-- Custom Blueprint Display -->\r\n          <div class=\"custom-blueprint-container\">\r\n            <!-- SVG Connecting Lines -->\r\n            <svg\r\n              class=\"connection-lines\"\r\n              viewBox=\"0 0 100 100\"\r\n              preserveAspectRatio=\"none\"\r\n            >\r\n              <!-- Line from System Prompt (top-left) to center -->\r\n              <line\r\n                x1=\"25\"\r\n                y1=\"25\"\r\n                x2=\"50\"\r\n                y2=\"50\"\r\n                stroke=\"#BBBEC5\"\r\n                stroke-width=\"0.3\"\r\n              />\r\n\r\n              <!-- Line from AI Model (top-right) to center -->\r\n              <line\r\n                x1=\"75\"\r\n                y1=\"25\"\r\n                x2=\"50\"\r\n                y2=\"50\"\r\n                stroke=\"#BBBEC5\"\r\n                stroke-width=\"0.3\"\r\n              />\r\n\r\n              <!-- Line from Knowledge Base (bottom-left) to center -->\r\n              <line\r\n                x1=\"25\"\r\n                y1=\"75\"\r\n                x2=\"50\"\r\n                y2=\"50\"\r\n                stroke=\"#BBBEC5\"\r\n                stroke-width=\"0.3\"\r\n              />\r\n\r\n              <!-- Line from Guardrails (bottom-right) to center -->\r\n              <line\r\n                x1=\"75\"\r\n                y1=\"75\"\r\n                x2=\"50\"\r\n                y2=\"50\"\r\n                stroke=\"#BBBEC5\"\r\n                stroke-width=\"0.3\"\r\n              />\r\n            </svg>\r\n\r\n            <!-- Central Progress Bar -->\r\n            <div class=\"central-progress\">\r\n              <div class=\"progress-ring\">\r\n                <svg class=\"progress-circle\" width=\"120\" height=\"120\">\r\n                  <defs>\r\n                    <linearGradient\r\n                      id=\"progressGradient\"\r\n                      x1=\"0%\"\r\n                      y1=\"0%\"\r\n                      x2=\"100%\"\r\n                      y2=\"100%\"\r\n                    >\r\n                      <stop offset=\"33.91%\" style=\"stop-color: #0084ff\" />\r\n                      <stop offset=\"100%\" style=\"stop-color: #03bdd4\" />\r\n                    </linearGradient>\r\n                  </defs>\r\n                  <circle\r\n                    class=\"progress-background\"\r\n                    cx=\"60\"\r\n                    cy=\"60\"\r\n                    r=\"50\"\r\n                    fill=\"none\"\r\n                    stroke=\"#e5e7eb\"\r\n                    stroke-width=\"8\"\r\n                  />\r\n                  <circle\r\n                    class=\"progress-bar\"\r\n                    cx=\"60\"\r\n                    cy=\"60\"\r\n                    r=\"50\"\r\n                    fill=\"none\"\r\n                    stroke=\"url(#progressGradient)\"\r\n                    stroke-width=\"8\"\r\n                    stroke-linecap=\"round\"\r\n                    [style.stroke-dasharray]=\"314\"\r\n                    [style.stroke-dashoffset]=\"\r\n                      314 - (314 * blueprintCompletionPercentage) / 100\r\n                    \"\r\n                    transform=\"rotate(180 60 60)\"\r\n                  />\r\n                </svg>\r\n                <div class=\"progress-content\">\r\n                  <div class=\"progress-percentage\">\r\n                    {{ blueprintCompletionPercentage }}%\r\n                  </div>\r\n                  <div class=\"progress-label\">Complete</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Blueprint Zones Layout -->\r\n            <div id=\"parent-box\">\r\n              <!-- <div id=\"box1\"> -->\r\n              <!-- System Prompt Zone -->\r\n              <div\r\n                class=\"blueprint-zone north-zone prompts-zone\"\r\n                [class.has-nodes]=\"blueprintPromptNodes.length > 0\"\r\n              >\r\n                <div\r\n                  class=\"zone-header\"\r\n                  (click)=\"toggleBlueprintZone('prompt')\"\r\n                >\r\n                  <div class=\"header-content\">\r\n                    <div class=\"header-icon\">\r\n                      <svg\r\n                        width=\"45\"\r\n                        height=\"44\"\r\n                        viewBox=\"0 0 45 44\"\r\n                        fill=\"none\"\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                      >\r\n                        <rect\r\n                          x=\"0.679688\"\r\n                          width=\"44\"\r\n                          height=\"44\"\r\n                          rx=\"8\"\r\n                          fill=\"#5082EF\"\r\n                        />\r\n                        <path\r\n                          d=\"M20.6166 25.5C20.5273 25.1539 20.347 24.8381 20.0942 24.5854C19.8415 24.3327 19.5257 24.1523 19.1796 24.063L13.0446 22.481C12.94 22.4513 12.8478 22.3883 12.7822 22.3014C12.7166 22.2146 12.6812 22.1088 12.6812 22C12.6812 21.8912 12.7166 21.7854 12.7822 21.6986C12.8478 21.6118 12.94 21.5487 13.0446 21.519L19.1796 19.936C19.5256 19.8468 19.8413 19.6666 20.094 19.414C20.3467 19.1615 20.5272 18.8459 20.6166 18.5L22.1986 12.365C22.228 12.2599 22.291 12.1673 22.3779 12.1014C22.4649 12.0355 22.571 11.9998 22.6801 11.9998C22.7892 11.9998 22.8954 12.0355 22.9823 12.1014C23.0692 12.1673 23.1322 12.2599 23.1616 12.365L24.7426 18.5C24.8319 18.8461 25.0123 19.1619 25.265 19.4146C25.5177 19.6673 25.8336 19.8477 26.1796 19.937L32.3146 21.518C32.4201 21.5471 32.5132 21.61 32.5795 21.6971C32.6458 21.7841 32.6817 21.8906 32.6817 22C32.6817 22.1094 32.6458 22.2159 32.5795 22.3029C32.5132 22.39 32.4201 22.4529 32.3146 22.482L26.1796 24.063C25.8336 24.1523 25.5177 24.3327 25.265 24.5854C25.0123 24.8381 24.8319 25.1539 24.7426 25.5L23.1606 31.635C23.1312 31.7401 23.0682 31.8327 22.9813 31.8986C22.8944 31.9646 22.7882 32.0003 22.6791 32.0003C22.57 32.0003 22.4639 31.9646 22.3769 31.8986C22.29 31.8327 22.227 31.7401 22.1976 31.635L20.6166 25.5Z\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                      </svg>\r\n                    </div>\r\n                    <h3 class=\"zone-title\">System Prompt</h3>\r\n                  </div>\r\n                  <div class=\"header-actions\">\r\n                    <span class=\"required-badge\">Required</span>\r\n                    <button class=\"accordion-toggle\" type=\"button\">\r\n                      <svg\r\n                        width=\"16\"\r\n                        height=\"16\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        fill=\"none\"\r\n                        [style.transform]=\"\r\n                          isBlueprintZoneExpanded('prompt')\r\n                            ? 'rotate(180deg)'\r\n                            : 'rotate(0deg)'\r\n                        \"\r\n                      >\r\n                        <path\r\n                          d=\"M6 9L12 15L18 9\"\r\n                          stroke=\"currentColor\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                      </svg>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n                <div\r\n                  class=\"zone-content\"\r\n                  *ngIf=\"isBlueprintZoneExpanded('prompt')\"\r\n                >\r\n                  <div\r\n                    *ngIf=\"blueprintPromptNodes.length === 0\"\r\n                    class=\"empty-state\"\r\n                  >\r\n                    No prompt configured\r\n                  </div>\r\n                  <div class=\"nodes-list\">\r\n                    <div\r\n                      *ngFor=\"let node of blueprintPromptNodes\"\r\n                      class=\"kanban-card\"\r\n                    >\r\n                      <span class=\"card-title\">{{\r\n                        node.name || \"Prompt\"\r\n                      }}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Knowledge Base Zone -->\r\n              <div\r\n                class=\"blueprint-zone west-zone knowledge-zone\"\r\n                [class.has-nodes]=\"blueprintKnowledgeNodes.length > 0\"\r\n              >\r\n                <div\r\n                  class=\"zone-header\"\r\n                  (click)=\"toggleBlueprintZone('knowledge')\"\r\n                >\r\n                  <div class=\"header-content\">\r\n                    <div class=\"header-icon\">\r\n                      <svg\r\n                        width=\"45\"\r\n                        height=\"44\"\r\n                        viewBox=\"0 0 45 44\"\r\n                        fill=\"none\"\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                      >\r\n                        <rect\r\n                          x=\"0.679688\"\r\n                          width=\"44\"\r\n                          height=\"44\"\r\n                          rx=\"8\"\r\n                          fill=\"#308666\"\r\n                        />\r\n                        <path\r\n                          d=\"M22.6797 17V31\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                        <path\r\n                          d=\"M26.6797 22H28.6797\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                        <path\r\n                          d=\"M26.6797 18H28.6797\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                        <path\r\n                          d=\"M13.6797 28C13.4145 28 13.1601 27.8946 12.9726 27.7071C12.785 27.5196 12.6797 27.2652 12.6797 27V14C12.6797 13.7348 12.785 13.4804 12.9726 13.2929C13.1601 13.1054 13.4145 13 13.6797 13H18.6797C19.7406 13 20.758 13.4214 21.5081 14.1716C22.2583 14.9217 22.6797 15.9391 22.6797 17C22.6797 15.9391 23.1011 14.9217 23.8513 14.1716C24.6014 13.4214 25.6188 13 26.6797 13H31.6797C31.9449 13 32.1993 13.1054 32.3868 13.2929C32.5743 13.4804 32.6797 13.7348 32.6797 14V27C32.6797 27.2652 32.5743 27.5196 32.3868 27.7071C32.1993 27.8946 31.9449 28 31.6797 28H25.6797C24.884 28 24.121 28.3161 23.5584 28.8787C22.9958 29.4413 22.6797 30.2044 22.6797 31C22.6797 30.2044 22.3636 29.4413 21.801 28.8787C21.2384 28.3161 20.4753 28 19.6797 28H13.6797Z\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                        <path\r\n                          d=\"M16.6797 22H18.6797\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                        <path\r\n                          d=\"M16.6797 18H18.6797\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                      </svg>\r\n                    </div>\r\n                    <h3 class=\"zone-title\">Knowledgebase</h3>\r\n                  </div>\r\n                  <div class=\"header-actions\">\r\n                    <span class=\"optional-badge\">Optional</span>\r\n                    <button class=\"accordion-toggle\" type=\"button\">\r\n                      <svg\r\n                        width=\"16\"\r\n                        height=\"16\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        fill=\"none\"\r\n                        [style.transform]=\"\r\n                          isBlueprintZoneExpanded('knowledge')\r\n                            ? 'rotate(180deg)'\r\n                            : 'rotate(0deg)'\r\n                        \"\r\n                      >\r\n                        <path\r\n                          d=\"M6 9L12 15L18 9\"\r\n                          stroke=\"currentColor\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                      </svg>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n                <div\r\n                  class=\"zone-content\"\r\n                  *ngIf=\"isBlueprintZoneExpanded('knowledge')\"\r\n                >\r\n                  <div\r\n                    *ngIf=\"blueprintKnowledgeNodes.length === 0\"\r\n                    class=\"empty-state\"\r\n                  >\r\n                    No knowledge base configured\r\n                  </div>\r\n                  <div class=\"nodes-list\">\r\n                    <div\r\n                      *ngFor=\"let node of blueprintKnowledgeNodes\"\r\n                      class=\"kanban-card\"\r\n                    >\r\n                      <span class=\"card-title\">{{\r\n                        node.name || \"Knowledge Base\"\r\n                      }}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <!-- </div> -->\r\n\r\n              <!-- <div id=\"box2\"> -->\r\n              <!-- AI Model Zone -->\r\n              <div\r\n                class=\"blueprint-zone east-zone models-zone\"\r\n                [class.has-nodes]=\"blueprintModelNodes.length > 0\"\r\n              >\r\n                <div class=\"zone-header\" (click)=\"toggleBlueprintZone('model')\">\r\n                  <div class=\"header-content\">\r\n                    <div class=\"header-icon\">\r\n                      <svg\r\n                        width=\"45\"\r\n                        height=\"44\"\r\n                        viewBox=\"0 0 45 44\"\r\n                        fill=\"none\"\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                      >\r\n                        <rect\r\n                          x=\"0.679688\"\r\n                          width=\"44\"\r\n                          height=\"44\"\r\n                          rx=\"8\"\r\n                          fill=\"#997BCF\"\r\n                        />\r\n                        <path\r\n                          d=\"M31.6797 18C31.6793 17.6493 31.5867 17.3048 31.4112 17.0012C31.2357 16.6975 30.9834 16.4454 30.6797 16.27L23.6797 12.27C23.3756 12.0945 23.0308 12.0021 22.6797 12.0021C22.3286 12.0021 21.9837 12.0945 21.6797 12.27L14.6797 16.27C14.376 16.4454 14.1237 16.6975 13.9481 17.0012C13.7726 17.3048 13.68 17.6493 13.6797 18V26C13.68 26.3508 13.7726 26.6952 13.9481 26.9989C14.1237 27.3025 14.376 27.5547 14.6797 27.73L21.6797 31.73C21.9837 31.9056 22.3286 31.998 22.6797 31.998C23.0308 31.998 23.3756 31.9056 23.6797 31.73L30.6797 27.73C30.9834 27.5547 31.2357 27.3025 31.4112 26.9989C31.5867 26.6952 31.6793 26.3508 31.6797 26V18Z\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                        <path\r\n                          d=\"M13.9795 17L22.6795 22L31.3795 17\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                        <path\r\n                          d=\"M22.6797 32V22\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                      </svg>\r\n                    </div>\r\n                    <h3 class=\"zone-title\">AI Model</h3>\r\n                  </div>\r\n                  <div class=\"header-actions\">\r\n                    <span class=\"required-badge\">Required</span>\r\n                    <button class=\"accordion-toggle\" type=\"button\">\r\n                      <svg\r\n                        width=\"16\"\r\n                        height=\"16\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        fill=\"none\"\r\n                        [style.transform]=\"\r\n                          isBlueprintZoneExpanded('model')\r\n                            ? 'rotate(180deg)'\r\n                            : 'rotate(0deg)'\r\n                        \"\r\n                      >\r\n                        <path\r\n                          d=\"M6 9L12 15L18 9\"\r\n                          stroke=\"currentColor\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                      </svg>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n                <div\r\n                  class=\"zone-content\"\r\n                  *ngIf=\"isBlueprintZoneExpanded('model')\"\r\n                >\r\n                  <div\r\n                    *ngIf=\"blueprintModelNodes.length === 0\"\r\n                    class=\"empty-state\"\r\n                  >\r\n                    No model configured\r\n                  </div>\r\n                  <div class=\"nodes-list\">\r\n                    <div\r\n                      *ngFor=\"let node of blueprintModelNodes\"\r\n                      class=\"kanban-card\"\r\n                    >\r\n                      <span class=\"card-title\">{{ node.name || \"Model\" }}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Tools Zone (for Collaborative Agents) -->\r\n              <div\r\n                *ngIf=\"agentType === 'collaborative'\"\r\n                class=\"blueprint-zone south-zone tools-zone\"\r\n                [class.has-nodes]=\"blueprintToolNodes.length > 0\"\r\n              >\r\n                <div class=\"zone-header\" (click)=\"toggleBlueprintZone('tool')\">\r\n                  <div class=\"header-content\">\r\n                    <div class=\"header-icon\">\r\n                      <svg\r\n                        width=\"45\"\r\n                        height=\"44\"\r\n                        viewBox=\"0 0 45 44\"\r\n                        fill=\"none\"\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                        *ngIf=\"agentType === 'collaborative'\"\r\n                      >\r\n                        <rect\r\n                          x=\"0.679688\"\r\n                          width=\"44\"\r\n                          height=\"44\"\r\n                          rx=\"8\"\r\n                          fill=\"#D97706\"\r\n                        />\r\n                        <path\r\n                          d=\"M25.3795 16.3C25.1963 16.4869 25.0937 16.7382 25.0937 17C25.0937 17.2617 25.1963 17.5131 25.3795 17.7L26.9795 19.3C27.1665 19.4832 27.4178 19.5858 27.6795 19.5858C27.9413 19.5858 28.1926 19.4832 28.3795 19.3L32.1495 15.53C32.6524 16.6412 32.8046 17.8792 32.586 19.0791C32.3674 20.279 31.7883 21.3838 30.9258 22.2463C30.0634 23.1087 28.9586 23.6878 27.7587 23.9064C26.5588 24.1251 25.3207 23.9728 24.2095 23.47L17.2995 30.38C16.9017 30.7778 16.3622 31.0013 15.7995 31.0013C15.2369 31.0013 14.6974 30.7778 14.2995 30.38C13.9017 29.9822 13.6782 29.4426 13.6782 28.88C13.6782 28.3174 13.9017 27.7778 14.2995 27.38L21.2095 20.47C20.7067 19.3588 20.5545 18.1207 20.7731 16.9208C20.9917 15.7209 21.5708 14.6161 22.4333 13.7537C23.2957 12.8913 24.4005 12.3121 25.6004 12.0935C26.8003 11.8749 28.0384 12.0271 29.1495 12.53L25.3895 16.29L25.3795 16.3Z\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                      </svg>\r\n                    </div>\r\n                    <h3 class=\"zone-title\">Tools</h3>\r\n                  </div>\r\n                  <div class=\"header-actions\">\r\n                    <span class=\"optional-badge\">Optional</span>\r\n                    <button class=\"accordion-toggle\" type=\"button\">\r\n                      <svg\r\n                        width=\"16\"\r\n                        height=\"16\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        fill=\"none\"\r\n                        [style.transform]=\"\r\n                          isBlueprintZoneExpanded('tool')\r\n                            ? 'rotate(180deg)'\r\n                            : 'rotate(0deg)'\r\n                        \"\r\n                      >\r\n                        <path\r\n                          d=\"M6 9L12 15L18 9\"\r\n                          stroke=\"currentColor\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                      </svg>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n                <div\r\n                  class=\"zone-content\"\r\n                  *ngIf=\"isBlueprintZoneExpanded('tool')\"\r\n                >\r\n                  <div\r\n                    *ngIf=\"blueprintToolNodes.length === 0\"\r\n                    class=\"empty-state\"\r\n                  >\r\n                    No tools configured\r\n                  </div>\r\n                  <div class=\"nodes-list\">\r\n                    <div\r\n                      *ngFor=\"let node of blueprintToolNodes\"\r\n                      class=\"kanban-card\"\r\n                    >\r\n                      <span class=\"card-title\">{{ node.name || \"Tool\" }}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Guardrails Zone (for Individual Agents) -->\r\n              <div\r\n                *ngIf=\"agentType === 'individual'\"\r\n                class=\"blueprint-zone south-zone guardrails-zone\"\r\n                [class.has-nodes]=\"blueprintGuardrailNodes.length > 0\"\r\n              >\r\n                <div\r\n                  class=\"zone-header\"\r\n                  (click)=\"toggleBlueprintZone('guardrail')\"\r\n                >\r\n                  <div class=\"header-content\">\r\n                    <div class=\"header-icon\">\r\n                      <svg\r\n                        width=\"45\"\r\n                        height=\"44\"\r\n                        viewBox=\"0 0 45 44\"\r\n                        fill=\"none\"\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                      >\r\n                        <rect\r\n                          x=\"0.679688\"\r\n                          width=\"44\"\r\n                          height=\"44\"\r\n                          rx=\"8\"\r\n                          fill=\"#DC2626\"\r\n                        />\r\n                        <path\r\n                          d=\"M30.6797 23C30.6797 28 27.1797 30.5 23.0197 31.95C22.8019 32.0238 22.5652 32.0202 22.3497 31.94C18.1797 30.5 14.6797 28 14.6797 23V16C14.6797 15.7347 14.785 15.4804 14.9726 15.2929C15.1601 15.1053 15.4145 15 15.6797 15C17.6797 15 20.1797 13.8 21.9197 12.28C22.1315 12.099 22.401 11.9995 22.6797 11.9995C22.9583 11.9995 23.2278 12.099 23.4397 12.28C25.1897 13.81 27.6797 15 29.6797 15C29.9449 15 30.1993 15.1053 30.3868 15.2929C30.5743 15.4804 30.6797 15.7347 30.6797 16V23Z\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                      </svg>\r\n                    </div>\r\n                    <h3 class=\"zone-title\">Guardrails</h3>\r\n                  </div>\r\n                  <div class=\"header-actions\">\r\n                    <span class=\"optional-badge\">Optional</span>\r\n                    <button class=\"accordion-toggle\" type=\"button\">\r\n                      <svg\r\n                        width=\"16\"\r\n                        height=\"16\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        fill=\"none\"\r\n                        [style.transform]=\"\r\n                          isBlueprintZoneExpanded('guardrail')\r\n                            ? 'rotate(180deg)'\r\n                            : 'rotate(0deg)'\r\n                        \"\r\n                      >\r\n                        <path\r\n                          d=\"M6 9L12 15L18 9\"\r\n                          stroke=\"currentColor\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                      </svg>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n                <div\r\n                  class=\"zone-content\"\r\n                  *ngIf=\"isBlueprintZoneExpanded('guardrail')\"\r\n                >\r\n                  <div\r\n                    *ngIf=\"blueprintGuardrailNodes.length === 0\"\r\n                    class=\"empty-state\"\r\n                  >\r\n                    No guardrails configured\r\n                  </div>\r\n                  <div class=\"nodes-list\">\r\n                    <div\r\n                      *ngFor=\"let node of blueprintGuardrailNodes\"\r\n                      class=\"kanban-card\"\r\n                    >\r\n                      <span class=\"card-title\">{{\r\n                        node.name || \"Guardrail\"\r\n                      }}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <!-- </div> -->\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Agent Output Content -->\r\n        <div *ngIf=\"activeRightTab === 'output'\" class=\"output-content\">\r\n          <!-- Individual Agent Output -->\r\n          <div *ngIf=\"agentType === 'individual'\" class=\"individual-output\">\r\n            <h3>Agent Output</h3>\r\n            <div\r\n              *ngIf=\"\r\n                latestAgentResponse?.response?.choices?.length > 0;\r\n                else noIndividualOutput\r\n              \"\r\n              class=\"output-box\"\r\n            >\r\n              <div class=\"output-section\">\r\n                <h4>Response</h4>\r\n                <div class=\"output-text\">\r\n                  {{ latestAgentResponse.response.choices[0].text }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <ng-template #noIndividualOutput>\r\n              <p>\r\n                No agent output available yet. Send a message to see the\r\n                response.\r\n              </p>\r\n            </ng-template>\r\n          </div>\r\n\r\n          <!-- Collaborative Agent Output -->\r\n          <div\r\n            *ngIf=\"agentType === 'collaborative'\"\r\n            class=\"collaborative-output\"\r\n          >\r\n            <h3>Task Outputs</h3>\r\n            <div\r\n              *ngIf=\"\r\n                latestAgentResponse?.agentResponse?.agent?.tasksOutputs\r\n                  ?.length > 0;\r\n                else noCollaborativeOutput\r\n              \"\r\n            >\r\n              <div\r\n                *ngFor=\"\r\n                  let taskOutput of latestAgentResponse.agentResponse.agent\r\n                    .tasksOutputs;\r\n                  let i = index\r\n                \"\r\n                class=\"output-box\"\r\n              >\r\n                <div class=\"output-section\">\r\n                  <h4>{{ taskOutput.summary || \"Task \" + (i + 1) }}</h4>\r\n                  <div *ngIf=\"taskOutput.description\" class=\"task-description\">\r\n                    <strong>Description:</strong> {{ taskOutput.description }}\r\n                  </div>\r\n                  <div *ngIf=\"taskOutput.expected_output\" class=\"task-expected\">\r\n                    <strong>Expected Output:</strong>\r\n                    {{ taskOutput.expected_output }}\r\n                  </div>\r\n                  <div class=\"output-text\">\r\n                    {{ taskOutput.raw }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <ng-template #noCollaborativeOutput>\r\n              <p>\r\n                No task outputs available yet. Send a message to see the\r\n                results.\r\n              </p>\r\n            </ng-template>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,OAAO,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,EAAE,QAAQ,MAAM;AAC9E,SAAiCC,WAAW,QAAQ,gBAAgB;AAEpE;AACA,SAASC,iCAAiC,QAAQ,8EAA8E;AAShI,SAASC,aAAa,QAAiC,wBAAwB;AAG/E,SAASC,WAAW,QAAQ,kCAAkC;AAK9D,SAASC,eAAe,QAAmD,gCAAgC;;;;;;;;;;;;ICInGC,EAAA,CAAAC,cAAA,iBAKC;IACCD,EAAA,CAAAE,MAAA,gBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAITH,EADF,CAAAC,cAAA,cAAyD,yCAoBtD;IADCD,EALA,CAAAI,UAAA,0BAAAC,+FAAAC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAgBF,MAAA,CAAAG,eAAA,CAAAN,MAAA,CAAuB;IAAA,EAAC,yBAAAO,8FAAAP,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CACzBF,MAAA,CAAAK,iBAAA,CAAAR,MAAA,CAAyB;IAAA,EAAC,kCAAAS,uGAAAT,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CACjBF,MAAA,CAAAO,gCAAA,CAAAV,MAAA,CAAwC;IAAA,EAAC,4BAAAW,iGAAAX,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAC/CF,MAAA,CAAAS,0BAAA,CAAAZ,MAAA,CAAkC;IAAA,EAAC,2BAAAa,gGAAAb,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CACpCF,MAAA,CAAAW,eAAA,CAAAd,MAAA,CAAuB;IAAA,EAAC,+BAAAe,oGAAA;MAAArB,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CACpBF,MAAA,CAAAa,mBAAA,EAAqB;IAAA,EAAC;IAG/CtB,EADE,CAAAG,YAAA,EAAiC,EAC7B;;;;IApBFH,EAAA,CAAAuB,SAAA,EAAyB;IAWzBvB,EAXA,CAAAwB,UAAA,aAAAf,MAAA,CAAAgB,YAAA,CAAyB,cAAAhB,MAAA,CAAAiB,gBAAA,CACK,cAAAjB,MAAA,CAAAkB,SAAA,CACP,+BAAAlB,MAAA,CAAAkB,SAAA,kBACkC,+BAC3B,6BACF,uBACN,4BACK,8BACE,uBAAAlB,MAAA,CAAAmB,SAAA,CACG,8CACa,qBAAAnB,MAAA,CAAAoB,QAAA,CAChB;;;;;IAgNrB7B,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAMFH,EAJF,CAAAC,cAAA,cAGC,eAC0B;IAAAD,EAAA,CAAAE,MAAA,GAEvB;IACJF,EADI,CAAAG,YAAA,EAAO,EACL;;;;IAHqBH,EAAA,CAAAuB,SAAA,GAEvB;IAFuBvB,EAAA,CAAA8B,iBAAA,CAAAC,OAAA,CAAAC,IAAA,aAEvB;;;;;IAjBRhC,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAiC,UAAA,IAAAC,oDAAA,kBAGC;IAGDlC,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAiC,UAAA,IAAAE,oDAAA,kBAGC;IAMLnC,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAfDH,EAAA,CAAAuB,SAAA,EAAuC;IAAvCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAA2B,oBAAA,CAAAC,MAAA,OAAuC;IAOrBrC,EAAA,CAAAuB,SAAA,GAAuB;IAAvBvB,EAAA,CAAAwB,UAAA,YAAAf,MAAA,CAAA2B,oBAAA,CAAuB;;;;;IA+G5CpC,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,MAAA,qCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAMFH,EAJF,CAAAC,cAAA,cAGC,eAC0B;IAAAD,EAAA,CAAAE,MAAA,GAEvB;IACJF,EADI,CAAAG,YAAA,EAAO,EACL;;;;IAHqBH,EAAA,CAAAuB,SAAA,GAEvB;IAFuBvB,EAAA,CAAA8B,iBAAA,CAAAQ,OAAA,CAAAN,IAAA,qBAEvB;;;;;IAjBRhC,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAiC,UAAA,IAAAM,oDAAA,kBAGC;IAGDvC,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAiC,UAAA,IAAAO,oDAAA,kBAGC;IAMLxC,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAfDH,EAAA,CAAAuB,SAAA,EAA0C;IAA1CvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAgC,uBAAA,CAAAJ,MAAA,OAA0C;IAOxBrC,EAAA,CAAAuB,SAAA,GAA0B;IAA1BvB,EAAA,CAAAwB,UAAA,YAAAf,MAAA,CAAAgC,uBAAA,CAA0B;;;;;IAyF/CzC,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAMFH,EAJF,CAAAC,cAAA,cAGC,eAC0B;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IACrDF,EADqD,CAAAG,YAAA,EAAO,EACtD;;;;IADqBH,EAAA,CAAAuB,SAAA,GAA0B;IAA1BvB,EAAA,CAAA8B,iBAAA,CAAAY,OAAA,CAAAV,IAAA,YAA0B;;;;;IAfzDhC,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAiC,UAAA,IAAAU,oDAAA,kBAGC;IAGD3C,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAiC,UAAA,IAAAW,oDAAA,kBAGC;IAIL5C,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAbDH,EAAA,CAAAuB,SAAA,EAAsC;IAAtCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAoC,mBAAA,CAAAR,MAAA,OAAsC;IAOpBrC,EAAA,CAAAuB,SAAA,GAAsB;IAAtBvB,EAAA,CAAAwB,UAAA,YAAAf,MAAA,CAAAoC,mBAAA,CAAsB;;;;;;IAkBvC7C,EAAA,CAAAC,cAAA,cAOC;IAQCD,EAPA,CAAA8C,SAAA,eAME,eAOA;IACJ9C,EAAA,CAAAG,YAAA,EAAM;;;;;IAiCVH,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAMFH,EAJF,CAAAC,cAAA,cAGC,eAC0B;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IACpDF,EADoD,CAAAG,YAAA,EAAO,EACrD;;;;IADqBH,EAAA,CAAAuB,SAAA,GAAyB;IAAzBvB,EAAA,CAAA8B,iBAAA,CAAAiB,OAAA,CAAAf,IAAA,WAAyB;;;;;IAfxDhC,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAiC,UAAA,IAAAe,2DAAA,kBAGC;IAGDhD,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAiC,UAAA,IAAAgB,2DAAA,kBAGC;IAILjD,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAbDH,EAAA,CAAAuB,SAAA,EAAqC;IAArCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAyC,kBAAA,CAAAb,MAAA,OAAqC;IAOnBrC,EAAA,CAAAuB,SAAA,GAAqB;IAArBvB,EAAA,CAAAwB,UAAA,YAAAf,MAAA,CAAAyC,kBAAA,CAAqB;;;;;;IAlE5ClD,EALF,CAAAC,cAAA,cAIC,cACgE;IAAtCD,EAAA,CAAAI,UAAA,mBAAA+C,oEAAA;MAAAnD,EAAA,CAAAO,aAAA,CAAA6C,GAAA;MAAA,MAAA3C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA4C,mBAAA,CAAoB,MAAM,CAAC;IAAA,EAAC;IAE1DrD,EADF,CAAAC,cAAA,cAA4B,cACD;IACvBD,EAAA,CAAAiC,UAAA,IAAAqB,yDAAA,kBAOC;IAgBHtD,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAC9BF,EAD8B,CAAAG,YAAA,EAAK,EAC7B;IAEJH,EADF,CAAAC,cAAA,cAA4B,eACG;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,kBAA+C;;IAC7CD,EAAA,CAAAC,cAAA,eAUC;IACCD,EAAA,CAAA8C,SAAA,gBAME;IAIV9C,EAHM,CAAAG,YAAA,EAAM,EACC,EACL,EACF;IACNH,EAAA,CAAAiC,UAAA,KAAAsB,qDAAA,kBAGC;IAgBHvD,EAAA,CAAAG,YAAA,EAAM;;;;IA3EJH,EAAA,CAAAwD,WAAA,cAAA/C,MAAA,CAAAyC,kBAAA,CAAAb,MAAA,KAAiD;IAWxCrC,EAAA,CAAAuB,SAAA,GAAmC;IAAnCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAkB,SAAA,qBAAmC;IA4BpC3B,EAAA,CAAAuB,SAAA,GAIC;IAJDvB,EAAA,CAAAyD,WAAA,cAAAhD,MAAA,CAAAiD,uBAAA,6CAIC;IAeN1D,EAAA,CAAAuB,SAAA,GAAqC;IAArCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAiD,uBAAA,SAAqC;;;;;IAqFtC1D,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAMFH,EAJF,CAAAC,cAAA,cAGC,eAC0B;IAAAD,EAAA,CAAAE,MAAA,GAEvB;IACJF,EADI,CAAAG,YAAA,EAAO,EACL;;;;IAHqBH,EAAA,CAAAuB,SAAA,GAEvB;IAFuBvB,EAAA,CAAA8B,iBAAA,CAAA6B,QAAA,CAAA3B,IAAA,gBAEvB;;;;;IAjBRhC,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAiC,UAAA,IAAA2B,2DAAA,kBAGC;IAGD5D,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAiC,UAAA,IAAA4B,2DAAA,kBAGC;IAML7D,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAfDH,EAAA,CAAAuB,SAAA,EAA0C;IAA1CvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAqD,uBAAA,CAAAzB,MAAA,OAA0C;IAOxBrC,EAAA,CAAAuB,SAAA,GAA0B;IAA1BvB,EAAA,CAAAwB,UAAA,YAAAf,MAAA,CAAAqD,uBAAA,CAA0B;;;;;;IApEjD9D,EALF,CAAAC,cAAA,cAIC,cAIE;IADCD,EAAA,CAAAI,UAAA,mBAAA2D,oEAAA;MAAA/D,EAAA,CAAAO,aAAA,CAAAyD,GAAA;MAAA,MAAAvD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA4C,mBAAA,CAAoB,WAAW,CAAC;IAAA,EAAC;IAGxCrD,EADF,CAAAC,cAAA,cAA4B,cACD;;IACvBD,EAAA,CAAAC,cAAA,cAMC;IAQCD,EAPA,CAAA8C,SAAA,eAME,eAOA;IAEN9C,EADE,CAAAG,YAAA,EAAM,EACF;;IACNH,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IACnCF,EADmC,CAAAG,YAAA,EAAK,EAClC;IAEJH,EADF,CAAAC,cAAA,cAA4B,gBACG;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,kBAA+C;;IAC7CD,EAAA,CAAAC,cAAA,eAUC;IACCD,EAAA,CAAA8C,SAAA,gBAME;IAIV9C,EAHM,CAAAG,YAAA,EAAM,EACC,EACL,EACF;IACNH,EAAA,CAAAiC,UAAA,KAAAgC,qDAAA,kBAGC;IAkBHjE,EAAA,CAAAG,YAAA,EAAM;;;;IA/EJH,EAAA,CAAAwD,WAAA,cAAA/C,MAAA,CAAAqD,uBAAA,CAAAzB,MAAA,KAAsD;IAyC9CrC,EAAA,CAAAuB,SAAA,IAIC;IAJDvB,EAAA,CAAAyD,WAAA,cAAAhD,MAAA,CAAAiD,uBAAA,kDAIC;IAeN1D,EAAA,CAAAuB,SAAA,GAA0C;IAA1CvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAiD,uBAAA,cAA0C;;;;;;IApiBjD1D,EAFJ,CAAAC,cAAA,cAAsE,cACtC,SACxB;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IACrBF,EADqB,CAAAG,YAAA,EAAK,EACpB;IAGNH,EAAA,CAAAC,cAAA,cAAwC;;IAEtCD,EAAA,CAAAC,cAAA,cAIC;IAgCCD,EA9BA,CAAA8C,SAAA,eAOE,eAUA,eAUA,eAUA;IACJ9C,EAAA,CAAAG,YAAA,EAAM;;IAIJH,EADF,CAAAC,cAAA,eAA8B,eACD;;IAGrBD,EAFJ,CAAAC,cAAA,eAAsD,YAC9C,0BAOH;IAECD,EADA,CAAA8C,SAAA,gBAAoD,gBACF;IAEtD9C,EADE,CAAAG,YAAA,EAAiB,EACZ;IAUPH,EATA,CAAA8C,SAAA,kBAQE,kBAeA;IACJ9C,EAAA,CAAAG,YAAA,EAAM;;IAEJH,EADF,CAAAC,cAAA,eAA8B,eACK;IAC/BD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAG1CF,EAH0C,CAAAG,YAAA,EAAM,EACtC,EACF,EACF;IAUFH,EAPJ,CAAAC,cAAA,eAAqB,eAMlB,eAIE;IADCD,EAAA,CAAAI,UAAA,mBAAA8D,8DAAA;MAAAlE,EAAA,CAAAO,aAAA,CAAA4D,GAAA;MAAA,MAAA1D,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA4C,mBAAA,CAAoB,QAAQ,CAAC;IAAA,EAAC;IAGrCrD,EADF,CAAAC,cAAA,eAA4B,eACD;;IACvBD,EAAA,CAAAC,cAAA,eAMC;IAQCD,EAPA,CAAA8C,SAAA,gBAME,gBAOA;IAEN9C,EADE,CAAAG,YAAA,EAAM,EACF;;IACNH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IACtCF,EADsC,CAAAG,YAAA,EAAK,EACrC;IAEJH,EADF,CAAAC,cAAA,eAA4B,gBACG;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,kBAA+C;;IAC7CD,EAAA,CAAAC,cAAA,eAUC;IACCD,EAAA,CAAA8C,SAAA,gBAME;IAIV9C,EAHM,CAAAG,YAAA,EAAM,EACC,EACL,EACF;IACNH,EAAA,CAAAiC,UAAA,KAAAmC,8CAAA,kBAGC;IAkBHpE,EAAA,CAAAG,YAAA,EAAM;;IAOJH,EAJF,CAAAC,cAAA,eAGC,eAIE;IADCD,EAAA,CAAAI,UAAA,mBAAAiE,8DAAA;MAAArE,EAAA,CAAAO,aAAA,CAAA4D,GAAA;MAAA,MAAA1D,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA4C,mBAAA,CAAoB,WAAW,CAAC;IAAA,EAAC;IAGxCrD,EADF,CAAAC,cAAA,eAA4B,eACD;;IACvBD,EAAA,CAAAC,cAAA,eAMC;IA2CCD,EA1CA,CAAA8C,SAAA,gBAME,gBAOA,gBAOA,gBAOA,gBAOA,gBAOA,gBAOA;IAEN9C,EADE,CAAAG,YAAA,EAAM,EACF;;IACNH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IACtCF,EADsC,CAAAG,YAAA,EAAK,EACrC;IAEJH,EADF,CAAAC,cAAA,eAA4B,gBACG;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,kBAA+C;;IAC7CD,EAAA,CAAAC,cAAA,eAUC;IACCD,EAAA,CAAA8C,SAAA,gBAME;IAIV9C,EAHM,CAAAG,YAAA,EAAM,EACC,EACL,EACF;IACNH,EAAA,CAAAiC,UAAA,KAAAqC,8CAAA,kBAGC;IAkBHtE,EAAA,CAAAG,YAAA,EAAM;;IASJH,EAJF,CAAAC,cAAA,eAGC,eACiE;IAAvCD,EAAA,CAAAI,UAAA,mBAAAmE,8DAAA;MAAAvE,EAAA,CAAAO,aAAA,CAAA4D,GAAA;MAAA,MAAA1D,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA4C,mBAAA,CAAoB,OAAO,CAAC;IAAA,EAAC;IAE3DrD,EADF,CAAAC,cAAA,eAA4B,eACD;;IACvBD,EAAA,CAAAC,cAAA,eAMC;IAsBCD,EArBA,CAAA8C,SAAA,gBAME,gBAOA,gBAOA,gBAOA;IAEN9C,EADE,CAAAG,YAAA,EAAM,EACF;;IACNH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IACjCF,EADiC,CAAAG,YAAA,EAAK,EAChC;IAEJH,EADF,CAAAC,cAAA,eAA4B,gBACG;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,kBAA+C;;IAC7CD,EAAA,CAAAC,cAAA,eAUC;IACCD,EAAA,CAAA8C,SAAA,gBAME;IAIV9C,EAHM,CAAAG,YAAA,EAAM,EACC,EACL,EACF;IACNH,EAAA,CAAAiC,UAAA,KAAAuC,8CAAA,kBAGC;IAgBHxE,EAAA,CAAAG,YAAA,EAAM;IAoFNH,EAjFA,CAAAiC,UAAA,KAAAwC,8CAAA,mBAIC,KAAAC,8CAAA,mBAiFA;IAkFP1E,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IAreMH,EAAA,CAAAuB,SAAA,IAA8B;IAC9BvB,EADA,CAAAyD,WAAA,yBAA8B,kCAAAhD,MAAA,CAAAkE,6BAAA,OAG7B;IAMD3E,EAAA,CAAAuB,SAAA,GACF;IADEvB,EAAA,CAAA4E,kBAAA,MAAAnE,MAAA,CAAAkE,6BAAA,OACF;IAYF3E,EAAA,CAAAuB,SAAA,GAAmD;IAAnDvB,EAAA,CAAAwD,WAAA,cAAA/C,MAAA,CAAA2B,oBAAA,CAAAC,MAAA,KAAmD;IAyC3CrC,EAAA,CAAAuB,SAAA,IAIC;IAJDvB,EAAA,CAAAyD,WAAA,cAAAhD,MAAA,CAAAiD,uBAAA,+CAIC;IAeN1D,EAAA,CAAAuB,SAAA,GAAuC;IAAvCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAiD,uBAAA,WAAuC;IAwB1C1D,EAAA,CAAAuB,SAAA,EAAsD;IAAtDvB,EAAA,CAAAwD,WAAA,cAAA/C,MAAA,CAAAgC,uBAAA,CAAAJ,MAAA,KAAsD;IA4E9CrC,EAAA,CAAAuB,SAAA,IAIC;IAJDvB,EAAA,CAAAyD,WAAA,cAAAhD,MAAA,CAAAiD,uBAAA,kDAIC;IAeN1D,EAAA,CAAAuB,SAAA,GAA0C;IAA1CvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAiD,uBAAA,cAA0C;IA0B7C1D,EAAA,CAAAuB,SAAA,EAAkD;IAAlDvB,EAAA,CAAAwD,WAAA,cAAA/C,MAAA,CAAAoC,mBAAA,CAAAR,MAAA,KAAkD;IAoD1CrC,EAAA,CAAAuB,SAAA,IAIC;IAJDvB,EAAA,CAAAyD,WAAA,cAAAhD,MAAA,CAAAiD,uBAAA,8CAIC;IAeN1D,EAAA,CAAAuB,SAAA,GAAsC;IAAtCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAiD,uBAAA,UAAsC;IAqBxC1D,EAAA,CAAAuB,SAAA,EAAmC;IAAnCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAkB,SAAA,qBAAmC;IAiFnC3B,EAAA,CAAAuB,SAAA,EAAgC;IAAhCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAkB,SAAA,kBAAgC;;;;;IAoGjC3B,EARJ,CAAAC,cAAA,cAMC,cAC6B,SACtB;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAE,MAAA,GACF;IAEJF,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IAHAH,EAAA,CAAAuB,SAAA,GACF;IADEvB,EAAA,CAAA4E,kBAAA,MAAAnE,MAAA,CAAAoE,mBAAA,CAAAC,QAAA,CAAAC,OAAA,IAAAC,IAAA,MACF;;;;;IAIFhF,EAAA,CAAAC,cAAA,QAAG;IACDD,EAAA,CAAAE,MAAA,2EAEF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAnBNH,EADF,CAAAC,cAAA,cAAkE,SAC5D;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAerBH,EAdA,CAAAiC,UAAA,IAAAgD,mDAAA,kBAMC,IAAAC,2DAAA,gCAAAlF,EAAA,CAAAmF,sBAAA,CAQgC;IAMnCnF,EAAA,CAAAG,YAAA,EAAM;;;;;IAnBDH,EAAA,CAAAuB,SAAA,GAEiB;IAAAvB,EAFjB,CAAAwB,UAAA,UAAAf,MAAA,CAAAoE,mBAAA,kBAAApE,MAAA,CAAAoE,mBAAA,CAAAC,QAAA,kBAAArE,MAAA,CAAAoE,mBAAA,CAAAC,QAAA,CAAAC,OAAA,kBAAAtE,MAAA,CAAAoE,mBAAA,CAAAC,QAAA,CAAAC,OAAA,CAAA1C,MAAA,MAEiB,aAAA+C,sBAAA,CAClB;;;;;IA0CMpF,EADF,CAAAC,cAAA,eAA6D,aACnD;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,GAChC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAD0BH,EAAA,CAAAuB,SAAA,GAChC;IADgCvB,EAAA,CAAA4E,kBAAA,MAAAS,cAAA,CAAAC,WAAA,MAChC;;;;;IAEEtF,EADF,CAAAC,cAAA,eAA8D,aACpD;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACjCH,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAuB,SAAA,GACF;IADEvB,EAAA,CAAA4E,kBAAA,MAAAS,cAAA,CAAAE,eAAA,MACF;;;;;IAPAvF,EATJ,CAAAC,cAAA,cAOC,cAC6B,SACtB;IAAAD,EAAA,CAAAE,MAAA,GAA6C;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAItDH,EAHA,CAAAiC,UAAA,IAAAuD,+DAAA,kBAA6D,IAAAC,+DAAA,kBAGC;IAI9DzF,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAE,MAAA,GACF;IAEJF,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;;IAZEH,EAAA,CAAAuB,SAAA,GAA6C;IAA7CvB,EAAA,CAAA8B,iBAAA,CAAAuD,cAAA,CAAAK,OAAA,eAAAC,KAAA,MAA6C;IAC3C3F,EAAA,CAAAuB,SAAA,EAA4B;IAA5BvB,EAAA,CAAAwB,UAAA,SAAA6D,cAAA,CAAAC,WAAA,CAA4B;IAG5BtF,EAAA,CAAAuB,SAAA,EAAgC;IAAhCvB,EAAA,CAAAwB,UAAA,SAAA6D,cAAA,CAAAE,eAAA,CAAgC;IAKpCvF,EAAA,CAAAuB,SAAA,GACF;IADEvB,EAAA,CAAA4E,kBAAA,MAAAS,cAAA,CAAAO,GAAA,MACF;;;;;IA1BN5F,EAAA,CAAAC,cAAA,UAMC;IACCD,EAAA,CAAAiC,UAAA,IAAA4D,yDAAA,kBAOC;IAeH7F,EAAA,CAAAG,YAAA,EAAM;;;;IApBkCH,EAAA,CAAAuB,SAAA,EAGlD;IAHkDvB,EAAA,CAAAwB,UAAA,YAAAf,MAAA,CAAAoE,mBAAA,CAAAiB,aAAA,CAAAC,KAAA,CAAAC,YAAA,CAGlD;;;;;IAmBYhG,EAAA,CAAAC,cAAA,QAAG;IACDD,EAAA,CAAAE,MAAA,0EAEF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAnCNH,EAJF,CAAAC,cAAA,cAGC,SACK;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAK;IA+BrBH,EA9BA,CAAAiC,UAAA,IAAAgE,mDAAA,kBAMC,IAAAC,2DAAA,gCAAAlG,EAAA,CAAAmF,sBAAA,CAwBmC;IAMtCnF,EAAA,CAAAG,YAAA,EAAM;;;;;IAnCDH,EAAA,CAAAuB,SAAA,GAGgB;IAAAvB,EAHhB,CAAAwB,UAAA,UAAAf,MAAA,CAAAoE,mBAAA,kBAAApE,MAAA,CAAAoE,mBAAA,CAAAiB,aAAA,kBAAArF,MAAA,CAAAoE,mBAAA,CAAAiB,aAAA,CAAAC,KAAA,kBAAAtF,MAAA,CAAAoE,mBAAA,CAAAiB,aAAA,CAAAC,KAAA,CAAAC,YAAA,kBAAAvF,MAAA,CAAAoE,mBAAA,CAAAiB,aAAA,CAAAC,KAAA,CAAAC,YAAA,CAAA3D,MAAA,MAGgB,aAAA8D,yBAAA,CAClB;;;;;IArCLnG,EAAA,CAAAC,cAAA,cAAgE;IA2B9DD,EAzBA,CAAAiC,UAAA,IAAAmE,6CAAA,kBAAkE,IAAAC,6CAAA,kBA4BjE;IAuCHrG,EAAA,CAAAG,YAAA,EAAM;;;;IAnEEH,EAAA,CAAAuB,SAAA,EAAgC;IAAhCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAkB,SAAA,kBAAgC;IA0BnC3B,EAAA,CAAAuB,SAAA,EAAmC;IAAnCvB,EAAA,CAAAwB,UAAA,SAAAf,MAAA,CAAAkB,SAAA,qBAAmC;;;ADzpBhD;AAcA,WAAa2E,uBAAuB;EAA9B,MAAOA,uBAAuB;IA+GxBC,KAAA;IACAC,MAAA;IACAC,YAAA;IACAC,sBAAA;IACAC,YAAA;IACAC,aAAA;IACAC,WAAA;IACAC,oBAAA;IArHVC,cAAc,GAAc,CAC1B;MAAEC,EAAE,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAgB,CAAE,EAC3C;MAAED,EAAE,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAc,CAAE,EAC7C;MAAED,EAAE,EAAE,cAAc;MAAEC,KAAK,EAAE,SAAS;MAAEC,QAAQ,EAAE;IAAI,CAAE,CACzD;IAED;IACAC,OAAO,GAAkB,IAAI;IAC7BxF,SAAS,GAAW,YAAY;IAChCC,SAAS,GAAW,OAAO;IAC3BwF,WAAW,GAAW,EAAE;IAGxBC,cAAc;IAEd;IACAC,YAAY,GAAkB,EAAE;IAChCC,gBAAgB,GAAW,CAAC;IAC5BC,gBAAgB;IAChBC,SAAS,GAAY,KAAK;IAC1BC,MAAM,GAAoB3H,eAAe,CAAC4H,UAAU;IAEpD;IACAjG,gBAAgB,GAAY,KAAK;IACjCkG,SAAS,GAAG,EAAE;IAEd;IACAC,WAAW,GAAgD,EAAE,CAAC,CAAC;IAE/D;IACAC,YAAY,GAAiB,EAAE;IAC/BjD,mBAAmB,GAAQ,IAAI,CAAC,CAAC;IAC1BkD,SAAS;IACTlG,QAAQ,GACb,gFAAgF;IAElF;IACAmG,kBAAkB,GAAgB,IAAI;IACtCC,kBAAkB,GAAY,KAAK;IACnCC,WAAW;IAEXC,kBAAkB,GAAGrI,WAAW,CAACsI,kBAAkB,IAAI,KAAK;IAErDC,mBAAmB,GAAY,KAAK;IAC3CC,gBAAgB;IAERC,QAAQ,GAAG,IAAIlJ,OAAO,EAAQ;IACtCmJ,WAAW,GAAW,gBAAgB;IACtCC,QAAQ,GAAa,CACnB;MAAEzB,EAAE,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAS,CAAE,EAChC;MAAED,EAAE,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAW,CAAE,EAClC;MAAED,EAAE,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAc,CAAE,CACtC;IAEDyB,QAAQ,GAAG,KAAK;IAChBC,UAAU;IACVC,WAAW,GAAU,EAAE;IACvBC,WAAW,GAAG,KAAK;IACnBC,WAAW,GAAY,KAAK;IAC5BC,aAAa,GAAW,EAAE;IAC1BC,UAAU,GAAU,EAAE;IACtBC,aAAa,GAAU,EAAE;IACzBC,QAAQ,GAAG,CAAC;IACZC,SAAS,GAAG,KAAK;IACjBC,WAAW,GAAW,EAAE;IAExBC,eAAe,GAAa,EAAE;IAC9BC,iBAAiB,GAAW,CAAC;IAC7BC,WAAW,GAAW,UAAU;IAEhC;IACAC,oBAAoB,GAAY,KAAK;IACrCC,cAAc,GAAW,WAAW;IAEpC;IACAC,mBAAmB,GAAQ,IAAI;IAC/BC,eAAe,GAAU,EAAE;IAC3BC,WAAW,GAAU,EAAE;IACvBC,WAAW,GAAU,EAAE;IACvBC,cAAc,GAAW,EAAE;IAC3BC,iBAAiB,GAAW,EAAE;IAC9BC,yBAAyB,GAAW,EAAE;IACtCC,sBAAsB,GAAU,EAAE;IAClCC,eAAe,GAAa,EAAE;IAC9BC,wBAAwB,GAAG,KAAK;IAChCC,sBAAsB,GAAG,IAAI/K,OAAO,EAAW;IAC/CgL,gBAAgB,GAAU,EAAE;IAC5BC,SAAS,GAAW,EAAE;IACtBC,aAAa,GAAqB,EAAE;IAEpC;IACA5F,6BAA6B,GAAW,CAAC;IACzCvC,oBAAoB,GAAU,EAAE;IAChCS,mBAAmB,GAAU,EAAE;IAC/BJ,uBAAuB,GAAU,EAAE;IACnCqB,uBAAuB,GAAU,EAAE;IACnCZ,kBAAkB,GAAU,EAAE;IAE9B;IACQsH,sBAAsB,GAA+B;MAC3DC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE,IAAI;MACXC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE,IAAI;MACfC,IAAI,EAAE;KACP;IAED;IAEAC,YACUvE,KAAqB,EACrBC,MAAc,EACdC,YAAiC,EACjCC,sBAA8C,EAC9CC,YAAiC,EACjCC,aAA4B,EAC5BC,WAAwB,EACxBC,oBAA0C;MAP1C,KAAAP,KAAK,GAALA,KAAK;MACL,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,YAAY,GAAZA,YAAY;MACZ,KAAAC,sBAAsB,GAAtBA,sBAAsB;MACtB,KAAAC,YAAY,GAAZA,YAAY;MACZ,KAAAC,aAAa,GAAbA,aAAa;MACb,KAAAC,WAAW,GAAXA,WAAW;MACX,KAAAC,oBAAoB,GAApBA,oBAAoB;MAE5B,IAAI,CAACiB,SAAS,GAAG,IAAI,CAAClB,WAAW,CAACkE,KAAK,CAAC;QACtCC,gBAAgB,EAAE,CAAC,IAAI,CAAC;QACxBC,aAAa,EAAE,CAAC,KAAK;OACtB,CAAC;IACJ;IAEAC,QAAQA,CAAA;MACNC,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;MAC9C,IAAI,CAAClD,WAAW,GAAGmD,MAAM,CAACC,UAAU,EAAE;MAEtC,IAAI,CAAC/E,KAAK,CAACgF,MAAM,CAACC,SAAS,CAAED,MAAM,IAAI;QACrC,IAAI,CAAC5J,SAAS,GAAG4J,MAAM,CAAC,MAAM,CAAC,IAAI,YAAY;QAC/CJ,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAACzJ,SAAS,CAAC;MAC5D,CAAC,CAAC;MAEF,IAAI,CAAC4E,KAAK,CAACkF,WAAW,CAACD,SAAS,CAAED,MAAM,IAAI;QAC1C,IAAIA,MAAM,CAAC,IAAI,CAAC,EAAE;UAChB,IAAI,CAACpE,OAAO,GAAGoE,MAAM,CAAC,IAAI,CAAC;UAC3B,IAAI,CAACG,aAAa,CAACH,MAAM,CAAC,IAAI,CAAC,CAAC;QAClC;MACF,CAAC,CAAC;MAEF;MACA,IAAI,CAAC1D,WAAW,GAAG,CACjB;QACE8D,IAAI,EAAE,EAAE;QACRC,EAAE,EAAE;UACFC,IAAI,EAAE,IAAI;UACV7G,IAAI,EAAE,kBAAkB,IAAI,CAACpD,SAAS,IAAI,YAAY;;OAEzD,CACF;IACH;IAEAkK,WAAWA,CAAA;MACT,IAAI,CAACvD,QAAQ,CAACwD,IAAI,EAAE;MACpB,IAAI,CAACxD,QAAQ,CAACyD,QAAQ,EAAE;MACxB,IAAI,IAAI,CAAC1D,gBAAgB,EAAE;QACzB2D,aAAa,CAAC,IAAI,CAAC3D,gBAAgB,CAAC;MACtC;IACF;IAEA4D,WAAWA,CAACC,KAAoC;MAC9C,IAAI,CAAC5C,WAAW,GAAG4C,KAAK,CAACnF,EAAE;MAC3B,IAAI,CAACwB,WAAW,GAAG2D,KAAK,CAAClF,KAAK;IAChC;IAEAyE,aAAaA,CAACvE,OAAe;MAC3B,IAAI,CAACgC,SAAS,GAAG,IAAI;MAErB;MACA,IAAI,IAAI,CAACxH,SAAS,KAAK,eAAe,EAAE;QACtC,IAAI,CAAC8E,YAAY,CAAC2F,gCAAgC,CAACjF,OAAO,CAAC,CAACqE,SAAS,CAAC;UACpEO,IAAI,EAAGjH,QAAa,IAAI;YACtB,IAAI,CAACuH,uBAAuB,CAACvH,QAAQ,CAAC;UACxC,CAAC;UACDwH,KAAK,EAAGA,KAAU,IAAI;YACpBnB,OAAO,CAACmB,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;YAC1D,IAAI,CAACnD,SAAS,GAAG,KAAK;UACxB;SACD,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAAC1C,YAAY,CAAC8F,YAAY,CAACpF,OAAO,CAAC,CAACqE,SAAS,CAAC;UAChDO,IAAI,EAAGjH,QAAa,IAAI;YACtB,IAAI,CAACuH,uBAAuB,CAACvH,QAAQ,CAAC;UACxC,CAAC;UACDwH,KAAK,EAAGA,KAAU,IAAI;YACpBnB,OAAO,CAACmB,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;YACvD,IAAI,CAACnD,SAAS,GAAG,KAAK;UACxB;SACD,CAAC;MACJ;IACF;IAEQkD,uBAAuBA,CAACvH,QAAa;MAC3C,IAAI,CAACqE,SAAS,GAAG,KAAK;MAEtB;MACA,IAAIqD,SAAS;MACb,IACE1H,QAAQ,CAAC2H,YAAY,IACrBC,KAAK,CAACC,OAAO,CAAC7H,QAAQ,CAAC2H,YAAY,CAAC,IACpC3H,QAAQ,CAAC2H,YAAY,CAACpK,MAAM,GAAG,CAAC,EAChC;QACAmK,SAAS,GAAG1H,QAAQ,CAAC2H,YAAY,CAAC,CAAC,CAAC;MACtC,CAAC,MAAM,IAAI3H,QAAQ,CAACsC,WAAW,EAAE;QAC/BoF,SAAS,GAAG1H,QAAQ,CAACsC,WAAW;MAClC,CAAC,MAAM,IAAItC,QAAQ,CAAC8H,IAAI,EAAE;QACxBJ,SAAS,GAAG1H,QAAQ,CAAC8H,IAAI;MAC3B,CAAC,MAAM;QACLJ,SAAS,GAAG1H,QAAQ;MACtB;MAEA,IAAI0H,SAAS,EAAE;QACb,IAAI,CAAC9C,mBAAmB,GAAG8C,SAAS;QACpC,IAAI,CAAC5K,SAAS,GAAG4K,SAAS,CAACxK,IAAI,IAAIwK,SAAS,CAAC5K,SAAS,IAAI,OAAO;QACjE,IAAI,CAACwF,WAAW,GAAGoF,SAAS,CAAClH,WAAW,IAAIkH,SAAS,CAACpF,WAAW,IAAI,EAAE;QAEvE;QACA,IAAI,IAAI,CAACzF,SAAS,KAAK,YAAY,EAAE;UACnC;UACA,IAAI,CAACmI,cAAc,GACjB0C,SAAS,CAACK,WAAW,IAAIL,SAAS,CAACxK,IAAI,IAAI,cAAc;UAE3D;UACA,IAAI,CAAC+H,iBAAiB,GACpByC,SAAS,CAACM,WAAW,IACrBN,SAAS,CAACK,WAAW,IACrBL,SAAS,CAACxK,IAAI,IACd,EAAE;UAEJ;UACA,IAAIwK,SAAS,CAACO,gBAAgB,EAAE;YAC9B,IAAI,CAAC/C,yBAAyB,GAAGwC,SAAS,CAACO,gBAAgB;UAC7D,CAAC,MAAM,IAAIP,SAAS,CAACM,WAAW,EAAE;YAChC,IAAI,CAAC9C,yBAAyB,GAAGwC,SAAS,CAACM,WAAW;UACxD,CAAC,MAAM,IAAIN,SAAS,CAACK,WAAW,EAAE;YAChC,IAAI,CAAC7C,yBAAyB,GAAGwC,SAAS,CAACK,WAAW;UACxD;QACF;QAEA;QACA,IAAI,IAAI,CAAChF,WAAW,CAACxF,MAAM,GAAG,CAAC,EAAE;UAC/B,IAAI,CAACwF,WAAW,CAAC,CAAC,CAAC,CAAC+D,EAAE,CAAC5G,IAAI,GAAG,kBAAkB,IAAI,CAACpD,SAAS,6BAA6B;QAC7F;QAEA;QACA,IAAI,CAACoL,cAAc,CAACR,SAAS,CAAC;MAChC;IACF;IAEQQ,cAAcA,CAACR,SAAc;MACnC;MACA,IAAI,CAACS,gCAAgC,CAACT,SAAS,CAAC;IAClD;IAEA1L,iBAAiBA,CAACoM,OAAe;MAC/B,IAAI,CAACA,OAAO,CAACC,IAAI,EAAE,IAAI,IAAI,CAACzL,gBAAgB,EAAE;MAE9C,IAAI,CAACA,gBAAgB,GAAG,IAAI;MAC5B,IAAI,CAAC+H,cAAc,GAAG,QAAQ,CAAC,CAAC;MAEhC;MACA,IAAI,CAAC5B,WAAW,CAACuF,IAAI,CAAC;QACpBzB,IAAI,EAAEuB,OAAO;QACbtB,EAAE,EAAE;UACFC,IAAI,EAAE,IAAI;UACV7G,IAAI,EAAE,KAAK;UACXmE,SAAS,EAAE,IAAI;UACfzB,MAAM,EAAE,SAAS;UACjB2F,SAAS,EAAE,IAAIC,IAAI;;OAEtB,CAAC;MAEF;MACA,IAAI,IAAI,CAAC3L,SAAS,KAAK,eAAe,EAAE;QACtC,IAAI,CAAC4L,yBAAyB,CAACL,OAAO,CAAC;MACzC,CAAC,MAAM;QACL,IAAI,CAACM,sBAAsB,CAACN,OAAO,CAAC;MACtC;IACF;IAEQM,sBAAsBA,CAACN,OAAe;MAC5C,IAAI,CAAC,IAAI,CAACxD,mBAAmB,KAAK,CAAC,IAAI,CAACI,cAAc,IAAI,IAAI,CAACA,cAAc,KAAK,SAAS,CAAC,EAAE;QAC5F,IAAI,CAAC2D,cAAc,CAAC,+DAA+D,CAAC;QACpF,IAAI,CAAC/L,gBAAgB,GAAG,KAAK;QAC7B;MACF;MAEA,MAAMsJ,gBAAgB,GAAG,IAAI,CAACjD,SAAS,CAAC2F,GAAG,CAAC,kBAAkB,CAAC,EAAEC,KAAK,IAAI,KAAK;MAC/E,MAAM1C,aAAa,GAAG,IAAI,CAAClD,SAAS,CAAC2F,GAAG,CAAC,eAAe,CAAC,EAAEC,KAAK,IAAI,KAAK;MAEzE,MAAMC,SAAS,GAAG,IAAI,CAACtD,SAAS,IAAI,IAAI,CAACP,iBAAiB,IAAI,IAAI,CAACL,mBAAmB,EAAEoD,WAAW,IAAI,IAAI,CAACpD,mBAAmB,EAAEmD,WAAW,IAAI,IAAI,CAACnD,mBAAmB,EAAE1H,IAAI,IAAI,IAAI,CAAC8H,cAAc;MAErM,IAAI+D,iBAAiB,GAAG,IAAI,CAAC7D,yBAAyB;MACtD,IAAI,CAAC6D,iBAAiB,EAAE;QACtB,IAAI,IAAI,CAACnE,mBAAmB,EAAEqD,gBAAgB,EAAE;UAC9Cc,iBAAiB,GAAG,IAAI,CAACnE,mBAAmB,CAACqD,gBAAgB;QAC/D,CAAC,MAAM;UACL,MAAMe,OAAO,GAAG,IAAI,CAACC,qBAAqB,EAAE;UAC5C,MAAMC,eAAe,GAAG,IAAI,CAACtE,mBAAmB,EAAEoD,WAAW,IAAI,IAAI,CAACpD,mBAAmB,EAAEmD,WAAW,IAAI,IAAI,CAACnD,mBAAmB,EAAE1H,IAAI,IAAI4L,SAAS;UACrJC,iBAAiB,GAAG,GAAGG,eAAe,GAAGF,OAAO,EAAE;QACpD;MACF;MAEA,IAAI,IAAI,CAAC7D,sBAAsB,CAAC5H,MAAM,GAAG,CAAC,EAAE;QAC1C,IAAI,CAAC4L,+BAA+B,CAACf,OAAO,EAAEU,SAAS,EAAEC,iBAAiB,EAAE7C,gBAAgB,EAAEC,aAAa,CAAC;QAC5G;MACF;MAEA,IAAI,CAACiD,qBAAqB,CAAChB,OAAO,EAAEU,SAAS,EAAEC,iBAAiB,EAAE7C,gBAAgB,EAAEC,aAAa,CAAC;IACpG;IAEQsC,yBAAyBA,CAACL,OAAe;MAC/C,MAAMiB,OAAO,GAAG;QACdjG,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7Bf,OAAO,EAAEiH,MAAM,CAAC,IAAI,CAACjH,OAAO,CAAC;QAC7BwE,IAAI,EAAE,IAAI,CAAChF,YAAY,CAAC0H,aAAa,EAAE,IAAI,uBAAuB;QAClEC,UAAU,EAAE;UAAEC,QAAQ,EAAErB;QAAO;OAChC;MAED,IAAI,IAAI,CAACjD,sBAAsB,CAAC5H,MAAM,GAAG,CAAC,EAAE;QAC1C,MAAMmM,WAAW,GAAG,IAAI,CAACvE,sBAAsB,CAAC,CAAC,CAAC;QAClD,IAAI,CAACvD,sBAAsB,CAAC+H,0BAA0B,CAACN,OAAO,EAAEK,WAAW,CAAC,CACzEE,IAAI,CACHlP,QAAQ,CAAC,MAAK;UAAG,IAAI,CAACkC,gBAAgB,GAAG,KAAK;QAAE,CAAC,CAAC,EAClDpC,SAAS,CAAC,IAAI,CAAC8K,sBAAsB,CAAC,CACvC,CACAoB,SAAS,CAAC;UACTO,IAAI,EAAG4C,GAAG,IAAK,IAAI,CAACC,0BAA0B,CAACD,GAAG,CAAC;UACnDrC,KAAK,EAAGuC,GAAQ,IAAK,IAAI,CAACC,uBAAuB,CAACD,GAAG;SACtD,CAAC;MACN,CAAC,MAAM;QACL,IAAI,CAACnI,sBAAsB,CAACqI,kBAAkB,CAACZ,OAAO,CAAC,CACpDO,IAAI,CACHlP,QAAQ,CAAC,MAAK;UAAG,IAAI,CAACkC,gBAAgB,GAAG,KAAK;QAAE,CAAC,CAAC,EAClDpC,SAAS,CAAC,IAAI,CAAC8K,sBAAsB,CAAC,CACvC,CACAoB,SAAS,CAAC;UACTO,IAAI,EAAG4C,GAAG,IAAK,IAAI,CAACC,0BAA0B,CAACD,GAAG,CAAC;UACnDrC,KAAK,EAAGuC,GAAQ,IAAK,IAAI,CAACC,uBAAuB,CAACD,GAAG;SACtD,CAAC;MACN;IACF;IAEQD,0BAA0BA,CAAC9J,QAAa;MAC9C,IAAI,CAACpD,gBAAgB,GAAG,KAAK;MAC7B,IAAI,CAACmD,mBAAmB,GAAGC,QAAQ;MAEnC,MAAMkK,iBAAiB,GACrBlK,QAAQ,EAAEgB,aAAa,EAAEC,KAAK,EAAEkJ,MAAM,IACtCnK,QAAQ,EAAEgB,aAAa,EAAEC,KAAK,EAAEjB,QAAQ,IACxCA,QAAQ,EAAEgB,aAAa,EAAEoJ,MAAM,IAC/B,oBAAoB;MAEtB,MAAMC,eAAe,GAAG,IAAI,CAACtH,WAAW,CAAC,IAAI,CAACA,WAAW,CAACxF,MAAM,GAAG,CAAC,CAAC;MACrE,IAAI8M,eAAe,IAAIA,eAAe,CAACvD,EAAE,CAACzC,SAAS,EAAE;QACnDgG,eAAe,CAACvD,EAAE,CAAC5G,IAAI,GAAGgK,iBAAiB,CAACI,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC;QACjED,eAAe,CAACvD,EAAE,CAACzC,SAAS,GAAG,KAAK;QACpCgG,eAAe,CAACvD,EAAE,CAAClE,MAAM,GAAG,SAAS;QACrCyH,eAAe,CAACvD,EAAE,CAACyB,SAAS,GAAG,IAAIC,IAAI,EAAE;MAC3C;IACF;IAEQwB,uBAAuBA,CAACD,GAAQ;MACtC,IAAI,CAACnN,gBAAgB,GAAG,KAAK;MAC7B,MAAM2N,aAAa,GAAGR,GAAG,EAAEvC,KAAK,EAAEY,OAAO,IAAI2B,GAAG,EAAE3B,OAAO,IAAI,+BAA+B;MAE5F,MAAMiC,eAAe,GAAG,IAAI,CAACtH,WAAW,CAAC,IAAI,CAACA,WAAW,CAACxF,MAAM,GAAG,CAAC,CAAC;MACrE,IAAI8M,eAAe,IAAIA,eAAe,CAACvD,EAAE,CAACzC,SAAS,EAAE;QACnDgG,eAAe,CAACvD,EAAE,CAAC5G,IAAI,GAAGqK,aAAa;QACvCF,eAAe,CAACvD,EAAE,CAACzC,SAAS,GAAG,KAAK;QACpCgG,eAAe,CAACvD,EAAE,CAAClE,MAAM,GAAG,QAAQ;QACpCyH,eAAe,CAACvD,EAAE,CAACyB,SAAS,GAAG,IAAIC,IAAI,EAAE;MAC3C;IACF;IAEA1M,eAAeA,CAAC6J,MAAsB;MACpC,IAAI,CAAC7C,SAAS,GAAG6C,MAAM,CAACzI,IAAI,IAAIsN,MAAM,CAAC7E,MAAM,CAACkD,KAAK,CAAC,IAAI,EAAE;IAC5D;IAEA3M,gCAAgCA,CAAC2M,KAAc;MAC7C;MACA,IAAI,CAAC5F,SAAS,CAAC2F,GAAG,CAAC,kBAAkB,CAAC,EAAE6B,QAAQ,CAAC5B,KAAK,CAAC;MAEvD;MACA;MACA,IAAI,CAACA,KAAK,EAAE;QACV,IAAI,CAACtD,gBAAgB,GAAG,EAAE;QAC1Bc,OAAO,CAACC,GAAG,CACT,6DAA6D,CAC9D;MACH,CAAC,MAAM;QACLD,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;MACzE;IACF;IAEAlK,0BAA0BA,CAACyM,KAAc;MACvC;MACA,IAAI,CAAC5F,SAAS,CAAC2F,GAAG,CAAC,eAAe,CAAC,EAAE6B,QAAQ,CAAC5B,KAAK,CAAC;MACpDxC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEuC,KAAK,CAAC;IAC9C;IAEAvM,eAAeA,CAACoO,KAAY;MAC1B,IAAI,CAACzG,aAAa,GAAGyG,KAAK;MAC1B;MACA,IAAI,CAACvF,sBAAsB,GAAGuF,KAAK;IACrC;IAEAlO,mBAAmBA,CAAA;MACjB;IAAA;IAGFmO,QAAQA,CAAA;MACN;IAAA;IAGFC,aAAaA,CAACC,OAA8B;MAC1C;IAAA;IAGFC,mBAAmBA,CAACC,MAAiC;MACnD;IAAA;IAGFC,YAAYA,CAAA;MACV,IAAI,CAACtJ,MAAM,CAACuJ,QAAQ,CAAC,CAAC,eAAe,EAAE,IAAI,CAACpO,SAAS,CAAC,EAAE;QACtD8J,WAAW,EAAE;UAAEzE,EAAE,EAAE,IAAI,CAACG,OAAO;UAAE6I,IAAI,EAAE;QAAM;OAC9C,CAAC;IACJ;IAEAC,SAASA,CAAA;MACP,IAAI,CAACzJ,MAAM,CAACuJ,QAAQ,CAAC,CAAC,eAAe,EAAE,IAAI,CAACpO,SAAS,CAAC,EAAE;QACtD8J,WAAW,EAAE;UAAEzE,EAAE,EAAE,IAAI,CAACG,OAAO;UAAE6I,IAAI,EAAE;QAAM;OAC9C,CAAC;IACJ;IAEAE,oBAAoBA,CAAA;MAClB,IAAI,CAAC1J,MAAM,CAACuJ,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;IACzC;IAEAI,eAAeA,CAAA;MACb,IAAI,CAAC3G,oBAAoB,GAAG,CAAC,IAAI,CAACA,oBAAoB;IACxD;IAEA4G,iBAAiBA,CAACC,GAAW;MAC3B,IAAI,CAAC5G,cAAc,GAAG4G,GAAG;IAC3B;IAEA;IACAhN,mBAAmBA,CAACiN,QAAgB;MAClC,IAAI,CAAC9F,sBAAsB,CAAC8F,QAAQ,CAAC,GACnC,CAAC,IAAI,CAAC9F,sBAAsB,CAAC8F,QAAQ,CAAC;IAC1C;IAEA5M,uBAAuBA,CAAC4M,QAAgB;MACtC,OAAO,IAAI,CAAC9F,sBAAsB,CAAC8F,QAAQ,CAAC,IAAI,KAAK;IACvD;IAEA;IACQ7C,cAAcA,CAACP,OAAe;MACpC,IAAI,CAACrF,WAAW,CAACuF,IAAI,CAAC;QACpBzB,IAAI,EAAE,EAAE;QACRC,EAAE,EAAE;UACFC,IAAI,EAAE,IAAI;UACV7G,IAAI,EAAEkI;;OAET,CAAC;IACJ;IAEQa,qBAAqBA,CAAA;MAC3B;MACA,OAAO,EAAE;IACX;IAEQwC,qBAAqBA,CAAA;MAC3B;MACA,OAAO,EAAE;IACX;IAEQrC,qBAAqBA,CAC3BhB,OAAe,EACf8C,IAAY,EACZnC,iBAAyB,EACzB7C,gBAAyB,EACzBC,aAAsB;MAEtB,IAAID,gBAAgB,EAAE;QACpB,IAAI,CAACX,gBAAgB,CAAC+C,IAAI,CAAC;UAAEoD,OAAO,EAAEtD,OAAO;UAAEuD,IAAI,EAAE;QAAM,CAAE,CAAC;MAChE;MAEA,MAAMtC,OAAO,GAAGnD,gBAAgB,GAAG,IAAI,CAACX,gBAAgB,GAAG6C,OAAO;MAClE,MAAM;QAAEwD;MAAO,CAAE,GAAG,IAAI,CAACH,qBAAqB,EAAE;MAEhD,IAAI,CAAC7J,sBAAsB,CACxBiK,cAAc,CACbxC,OAAO,EACP6B,IAAI,EACJhF,gBAAgB,EAChBC,aAAa,EACb,IAAI,CAACf,eAAe,EACpB2D,iBAAiB,EACjB,EAAE,EACF6C,OAAO,CACR,CACAhC,IAAI,CACHlP,QAAQ,CAAC,MAAK;QACZ,IAAI,CAACkC,gBAAgB,GAAG,KAAK;MAC/B,CAAC,CAAC,EACFpC,SAAS,CAAC,IAAI,CAAC8K,sBAAsB,CAAC,CACvC,CACAoB,SAAS,CAAC;QACTO,IAAI,EAAG4C,GAAQ,IAAK,IAAI,CAACC,0BAA0B,CAACD,GAAG,CAAC;QACxDrC,KAAK,EAAGuC,GAAQ,IAAK,IAAI,CAACC,uBAAuB,CAACD,GAAG;OACtD,CAAC;IACN;IAEQZ,+BAA+BA,CACrCf,OAAe,EACf8C,IAAY,EACZnC,iBAAyB,EACzB7C,gBAAyB,EACzBC,aAAsB;MAEtB,MAAM2F,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/B,IAAI,CAAC5G,sBAAsB,CAAC6G,OAAO,CAAEC,QAAQ,IAAI;QAC/C,IAAIA,QAAQ,CAACC,IAAI,EAAE;UACjBJ,QAAQ,CAACK,MAAM,CAAC,OAAO,EAAEF,QAAQ,CAACC,IAAI,CAAC;QACzC;MACF,CAAC,CAAC;MAEF,IAAIJ,QAAQ,CAACM,GAAG,CAAC,OAAO,CAAC,EAAE;QACzB,IAAI,CAACxK,sBAAsB,CACxByK,gBAAgB,CAACP,QAAQ,CAAC,CAC1BlC,IAAI,CACHnP,SAAS,CAAE6R,YAAY,IAAI;UACzB,MAAMC,WAAW,GACfD,YAAY,EAAEE,aAAa,EACvBC,GAAG,CAAEzM,QAAa,IAAKA,QAAQ,CAACuM,WAAW,CAAC,EAC5CG,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;UACtB,IAAI,CAACC,8BAA8B,CACjCvE,OAAO,EACP8C,IAAI,EACJnC,iBAAiB,EACjB7C,gBAAgB,EAChBC,aAAa,EACboG,WAAW,CACZ;UACD,OAAO3R,EAAE,CAAC,IAAI,CAAC;QACjB,CAAC,CAAC,EACFD,UAAU,CAAE6M,KAAK,IAAI;UACnBnB,OAAO,CAACmB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,IAAI,CAAC4B,qBAAqB,CACxBhB,OAAO,EACP8C,IAAI,EACJnC,iBAAiB,EACjB7C,gBAAgB,EAChBC,aAAa,CACd;UACD,OAAOvL,EAAE,CAAC,IAAI,CAAC;QACjB,CAAC,CAAC,CACH,CACA8L,SAAS,EAAE;MAChB,CAAC,MAAM;QACL,IAAI,CAAC0C,qBAAqB,CACxBhB,OAAO,EACP8C,IAAI,EACJnC,iBAAiB,EACjB7C,gBAAgB,EAChBC,aAAa,CACd;MACH;IACF;IAIQwG,8BAA8BA,CACpCvE,OAAe,EACf8C,IAAY,EACZnC,iBAAyB,EACzB7C,gBAAyB,EACzBC,aAAsB,EACtByG,YAAoB;MAEpB,IAAI1G,gBAAgB,EAAE;QACpB,IAAI,CAACX,gBAAgB,CAAC+C,IAAI,CAAC;UAAEoD,OAAO,EAAEtD,OAAO;UAAEuD,IAAI,EAAE;QAAM,CAAE,CAAC;MAChE;MACA,MAAMtC,OAAO,GAAGnD,gBAAgB,GAAG,IAAI,CAACX,gBAAgB,GAAG6C,OAAO;MAClE,MAAM;QAAEwD;MAAO,CAAE,GAAG,IAAI,CAACH,qBAAqB,EAAE;MAEhD,IAAI,CAAC7J,sBAAsB,CACxBiK,cAAc,CACbxC,OAAO,EACP6B,IAAI,EACJhF,gBAAgB,EAChBC,aAAa,EACb,IAAI,CAACf,eAAe,EACpB2D,iBAAiB,EACjB6D,YAAY,EACZhB,OAAO,CACR,CACAhC,IAAI,CACHlP,QAAQ,CAAC,MAAK;QACZ,IAAI,CAACkC,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACyI,wBAAwB,GAAG,KAAK;MACvC,CAAC,CAAC,EACF7K,SAAS,CAAC,IAAI,CAAC8K,sBAAsB,CAAC,CACvC,CACAoB,SAAS,CAAC;QACTO,IAAI,EAAG4F,iBAAsB,IAAI;UAC/B,IACEA,iBAAiB,EAAE7M,QAAQ,IAC3B6M,iBAAiB,EAAE7M,QAAQ,EAAEC,OAAO,EACpC;YACA,MAAM6M,cAAc,GAAGD,iBAAiB,CAAC7M,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,CAACC,IAAI;YACjE,IAAI,CAACvD,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;cAAEoK,IAAI,EAAE,IAAI;cAAE7G,IAAI,EAAE4M;YAAc,CAAE,CACrC;YACD,IAAI5G,gBAAgB,EAAE;cACpB,IAAI,CAACX,gBAAgB,CAAC+C,IAAI,CAAC;gBACzBoD,OAAO,EAAEoB,cAAc;gBACvBnB,IAAI,EAAE;eACP,CAAC;YACJ;UACF,CAAC,MAAM;YACLtF,OAAO,CAAC0G,IAAI,CAAC,iCAAiC,EAAEF,iBAAiB,CAAC;YAClE,IAAI,CAAClE,cAAc,CACjB,+CAA+C,CAChD;UACH;QACF,CAAC;QACDnB,KAAK,EAAGA,KAAU,IAAI;UACpBnB,OAAO,CAACmB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;UAClC,MAAMwF,YAAY,GAChBxF,KAAK,EAAEA,KAAK,EAAEY,OAAO,IACrB,kDAAkD;UACpD,IAAI,CAACO,cAAc,CAACqE,YAAY,CAAC;UACjC,IAAI9G,gBAAgB,IAAI,IAAI,CAACX,gBAAgB,CAAChI,MAAM,GAAG,CAAC,EAAE;YACxD,IAAI,CAACgI,gBAAgB,CAAC0H,GAAG,EAAE;UAC7B;QACF;OACD,CAAC;IACN;IAEA;IACQ9E,gCAAgCA,CAACT,SAAc;MACrD,IAAI,CAACA,SAAS,EAAE;QACdrB,OAAO,CAAC0G,IAAI,CAAC,sCAAsC,CAAC;QACpD;MACF;MAEA1G,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEoB,SAAS,CAAC;MAC7DrB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAACzJ,SAAS,CAAC;MACpDwJ,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE4G,MAAM,CAACC,IAAI,CAACzF,SAAS,CAAC,CAAC;MAEjE;MACA,IAAI,CAAC7C,eAAe,GAAG,EAAE;MACzB,IAAI,CAACC,WAAW,GAAG,EAAE;MAErB,IAAIsI,WAAW,GAAG,CAAC;MAEnB;MACA,IAAI,IAAI,CAACvQ,SAAS,KAAK,YAAY,EAAE;QACnC,IAAI,CAACwQ,6BAA6B,CAAC3F,SAAS,EAAE0F,WAAW,CAAC;MAC5D,CAAC,MAAM,IAAI,IAAI,CAACvQ,SAAS,KAAK,eAAe,EAAE;QAC7C,IAAI,CAACyQ,gCAAgC,CAAC5F,SAAS,EAAE0F,WAAW,CAAC;MAC/D;MAEA/G,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE;QACxCzB,eAAe,EAAE,IAAI,CAACA,eAAe;QACrCC,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7ByI,UAAU,EAAE,IAAI,CAAC1I,eAAe,CAACtH;OAClC,CAAC;IACJ;IAEQ8P,6BAA6BA,CACnC3F,SAAc,EACd0F,WAAmB;MAEnB/G,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAE;QAC5DkH,MAAM,EAAE9F,SAAS,CAAC8F,MAAM;QACxBC,YAAY,EAAE/F,SAAS,CAAC8F,MAAM,EAAEjQ,MAAM;QACtCwK,WAAW,EAAEL,SAAS,CAACK,WAAW;QAClCpC,MAAM,EAAE+B,SAAS,CAAC/B,MAAM;QACxB+H,cAAc,EAAEhG,SAAS,CAACgG;OAC3B,CAAC;MAEF;MACA,IAAI,CAACpQ,oBAAoB,GAAG,EAAE;MAC9B,IAAI,CAACS,mBAAmB,GAAG,EAAE;MAC7B,IAAI,CAACJ,uBAAuB,GAAG,EAAE;MACjC,IAAI,CAACqB,uBAAuB,GAAG,EAAE;MAEjC;MACA,IAAI0I,SAAS,CAAC/B,MAAM,EAAE;QACpB,IAAI,CAACrI,oBAAoB,CAACgL,IAAI,CAAC;UAC7BpG,EAAE,EAAE,UAAUkL,WAAW,EAAE,EAAE;UAC7BlQ,IAAI,EAAEwK,SAAS,CAAC/B,MAAM;UACtBgI,IAAI,EAAE;SACP,CAAC;QACFtH,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEoB,SAAS,CAAC/B,MAAM,CAAC;MACvD;MAEA;MACA,IAAI+B,SAAS,CAAC8F,MAAM,IAAI5F,KAAK,CAACC,OAAO,CAACH,SAAS,CAAC8F,MAAM,CAAC,EAAE;QACvDnH,OAAO,CAACC,GAAG,CACT,yCAAyC,EACzCoB,SAAS,CAAC8F,MAAM,CAACjQ,MAAM,CACxB;QAEDmK,SAAS,CAAC8F,MAAM,CAACxB,OAAO,CAAC,CAAC4B,QAAa,EAAEC,aAAqB,KAAI;UAChExH,OAAO,CAACC,GAAG,CACT,eAAeuH,aAAa,SAASD,QAAQ,CAACE,UAAU,IAAI,EAC5DF,QAAQ,CAACG,YAAY,CACtB;UAED,IAAIH,QAAQ,CAACJ,MAAM,IAAI5F,KAAK,CAACC,OAAO,CAAC+F,QAAQ,CAACJ,MAAM,CAAC,EAAE;YACrDnH,OAAO,CAACC,GAAG,CACT,eAAeuH,aAAa,QAAQD,QAAQ,CAACJ,MAAM,CAACjQ,MAAM,eAAe,CAC1E;YAEDqQ,QAAQ,CAACJ,MAAM,CAACxB,OAAO,CAAC,CAACgC,UAAe,EAAEC,SAAiB,KAAI;cAC7D5H,OAAO,CAACC,GAAG,CAAC,kBAAkBuH,aAAa,IAAII,SAAS,GAAG,EAAE;gBAC3DC,SAAS,EAAEF,UAAU,CAACE,SAAS;gBAC/BC,WAAW,EAAEH,UAAU,CAACG,WAAW;gBACnCL,UAAU,EAAEE,UAAU,CAACF;eACxB,CAAC;cAEF;cACA,IACEE,UAAU,CAACF,UAAU,KAAK,CAAC,IAC3BE,UAAU,CAACE,SAAS,KAAK,OAAO,IAChCF,UAAU,CAACG,WAAW,EACtB;gBACA9H,OAAO,CAACC,GAAG,CACT,2CAA2C,EAC3C0H,UAAU,CAACG,WAAW,CACvB;gBACD,IAAI,CAACpQ,mBAAmB,CAACuK,IAAI,CAAC;kBAC5BpG,EAAE,EAAE,SAASkL,WAAW,EAAE,EAAE;kBAC5BlQ,IAAI,EAAE,GAAG8Q,UAAU,CAACE,SAAS,EAAE;kBAC/BP,IAAI,EAAE;iBACP,CAAC;cACJ;cAEA;cACA,IACEK,UAAU,CAACF,UAAU,KAAK,CAAC,IAC3BE,UAAU,CAACE,SAAS,KAAK,wBAAwB,IACjDF,UAAU,CAACG,WAAW,EACtB;gBACA9H,OAAO,CAACC,GAAG,CACT,kDAAkD,EAClD0H,UAAU,CAACG,WAAW,CACvB;gBACD,MAAMC,OAAO,GAAGJ,UAAU,CAACG,WAAW,CAACE,QAAQ,EAAE;gBACjD,MAAMC,KAAK,GAAGF,OAAO,CAClBG,KAAK,CAAC,GAAG,CAAC,CACV9B,GAAG,CAAEvK,EAAU,IAAKA,EAAE,CAACmG,IAAI,EAAE,CAAC,CAC9BmG,MAAM,CAAEtM,EAAU,IAAKA,EAAE,CAAC;gBAE7BoM,KAAK,CAACtC,OAAO,CAAEyC,IAAY,IAAI;kBAC7B,IAAI,CAAC9Q,uBAAuB,CAAC2K,IAAI,CAAC;oBAChCpG,EAAE,EAAE,aAAakL,WAAW,EAAE,EAAE;oBAChClQ,IAAI,EAAE,mBAAmBuR,IAAI,EAAE;oBAC/Bd,IAAI,EAAE;mBACP,CAAC;gBACJ,CAAC,CAAC;cACJ;cAEA;cACA,IACEK,UAAU,CAACF,UAAU,KAAK,CAAC,IAC3BE,UAAU,CAACG,WAAW,KAAK,MAAM,EACjC;gBACA9H,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE;kBAC1DoI,GAAG,EAAEV,UAAU,CAACE,SAAS;kBACzBrF,KAAK,EAAEmF,UAAU,CAACG;iBACnB,CAAC;gBAEF,IAAIH,UAAU,CAACE,SAAS,KAAK,mBAAmB,EAAE;kBAChD;kBACA,IAAI,IAAI,CAAClP,uBAAuB,CAACzB,MAAM,KAAK,CAAC,EAAE;oBAC7C,IAAI,CAACyB,uBAAuB,CAACsJ,IAAI,CAAC;sBAChCpG,EAAE,EAAE,aAAakL,WAAW,EAAE,EAAE;sBAChClQ,IAAI,EAAE,oBAAoB;sBAC1ByQ,IAAI,EAAE;qBACP,CAAC;kBACJ;gBACF,CAAC,MAAM,IAAIK,UAAU,CAACE,SAAS,CAACS,UAAU,CAAC,YAAY,CAAC,EAAE;kBACxD;kBACA,IAAIC,aAAa,GAAGZ,UAAU,CAACE,SAAS;kBACxC,IAAIU,aAAa,CAACD,UAAU,CAAC,YAAY,CAAC,EAAE;oBAC1CC,aAAa,GAAGA,aAAa,CAC1BtE,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CACzBA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;kBACvB;kBAEA,IAAI,CAACtL,uBAAuB,CAACsJ,IAAI,CAAC;oBAChCpG,EAAE,EAAE,aAAakL,WAAW,EAAE,EAAE;oBAChClQ,IAAI,EAAE,GAAG0R,aAAa,EAAE;oBACxBjB,IAAI,EAAE;mBACP,CAAC;gBACJ;cACF;YACF,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ;MAEAtH,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;QACvCuI,WAAW,EAAE,IAAI,CAACvR,oBAAoB;QACtCwR,UAAU,EAAE,IAAI,CAAC/Q,mBAAmB;QACpCgR,cAAc,EAAE,IAAI,CAACpR,uBAAuB;QAC5CqR,cAAc,EAAE,IAAI,CAAChQ;OACtB,CAAC;MAEF;MACA,MAAMiQ,aAAa,GAAG,CAAC,CAAC,CAAC;MACzB,MAAMC,eAAe,GACnB,IAAI,CAAC5R,oBAAoB,CAACC,MAAM,GAAG,IAAI,CAACQ,mBAAmB,CAACR,MAAM;MACpE,IAAI,CAACsC,6BAA6B,GAAGsP,IAAI,CAACC,KAAK,CAC5CF,eAAe,GAAGD,aAAa,GAAI,GAAG,CACxC;IACH;IAEQ3B,gCAAgCA,CACtC5F,SAAc,EACd0F,WAAmB;MAEnB/G,OAAO,CAACC,GAAG,CACT,+DAA+D,CAChE;MACDD,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEoB,SAAS,CAAC;MACtErB,OAAO,CAACC,GAAG,CACT,0CAA0C,EAC1C4G,MAAM,CAACC,IAAI,CAACzF,SAAS,CAAC,CACvB;MACDrB,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE,IAAI,CAACzJ,SAAS,CAAC;MACjEwJ,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE8G,WAAW,CAAC;MAE1D;MACA,IAAI,CAAC9P,oBAAoB,GAAG,EAAE;MAC9B,IAAI,CAACS,mBAAmB,GAAG,EAAE;MAC7B,IAAI,CAACJ,uBAAuB,GAAG,EAAE;MACjC,IAAI,CAACS,kBAAkB,GAAG,EAAE;MAC5B,IAAI,CAACY,uBAAuB,GAAG,EAAE;MAEjCqH,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;MAE1D;MACA,MAAM+I,sBAAsB,GAC1B3H,SAAS,CAAC4H,IAAI,IAAI5H,SAAS,CAACiE,IAAI,IAAIjE,SAAS,CAAClH,WAAW;MAE3D6F,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE;QACtDgJ,IAAI,EAAE5H,SAAS,CAAC4H,IAAI;QACpB3D,IAAI,EAAEjE,SAAS,CAACiE,IAAI;QACpBnL,WAAW,EAAEkH,SAAS,CAAClH,WAAW;QAClC6O;OACD,CAAC;MAEF,IAAIA,sBAAsB,EAAE;QAC1B,IAAIE,cAAc,GAChB7H,SAAS,CAAC4H,IAAI,IACd5H,SAAS,CAACiE,IAAI,IACdjE,SAAS,CAAClH,WAAW,IACrB,4BAA4B;QAE9B;QACA,IAAI+O,cAAc,CAAChS,MAAM,GAAG,GAAG,EAAE;UAC/BgS,cAAc,GAAGA,cAAc,CAACC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK;QAC3D;QAEA,IAAI,CAAClS,oBAAoB,CAACgL,IAAI,CAAC;UAC7BpG,EAAE,EAAE,UAAUkL,WAAW,EAAE,EAAE;UAC7BlQ,IAAI,EAAEqS,cAAc;UACpB5B,IAAI,EAAE;SACP,CAAC;QACFtH,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEiJ,cAAc,CAAC;MACnE;MAEA;MACA,IAAIE,eAAe,GAAG,EAAE;MAExBpJ,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;QAC5CoJ,eAAe,EAAE,CAAC,CAAChI,SAAS,CAACiI,YAAY;QACzCA,YAAY,EAAEjI,SAAS,CAACiI,YAAY;QACpC/J,KAAK,EAAE8B,SAAS,CAAC9B,KAAK;QACtBgK,SAAS,EAAElI,SAAS,CAACkI,SAAS;QAC9BC,YAAY,EAAEnI,SAAS,CAACmI;OACzB,CAAC;MAEF;MACA,IAAInI,SAAS,CAACiI,YAAY,IAAIjI,SAAS,CAACiI,YAAY,CAACG,QAAQ,EAAE;QAC7D,MAAMC,SAAS,GAAGnI,KAAK,CAACC,OAAO,CAACH,SAAS,CAACiI,YAAY,CAACG,QAAQ,CAAC,GAC5DpI,SAAS,CAACiI,YAAY,CAACG,QAAQ,GAC/B,CAACpI,SAAS,CAACiI,YAAY,CAACG,QAAQ,CAAC;QAErCL,eAAe,GAAGM,SAAS,CAACtD,GAAG,CAAEuD,GAAQ,IAAI;UAC3C,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;YACtD,OAAO;cAAEC,OAAO,EAAED;YAAG,CAAE;UACzB;UACA,OAAOA,GAAG;QACZ,CAAC,CAAC;MACJ;MACA;MAAA,KACK,IAAItI,SAAS,CAACmI,YAAY,EAAE;QAC/BJ,eAAe,GAAG,CAAC/H,SAAS,CAACmI,YAAY,CAAC;MAC5C;MACA;MAAA,KACK,IAAInI,SAAS,CAAC9B,KAAK,IAAI8B,SAAS,CAACkI,SAAS,EAAE;QAC/CH,eAAe,GAAG,CAAC;UAAEQ,OAAO,EAAEvI,SAAS,CAAC9B,KAAK,IAAI8B,SAAS,CAACkI;QAAS,CAAE,CAAC;MACzE;MAEAH,eAAe,CAACzD,OAAO,CAAE8D,QAAa,IAAI;QACxC,MAAMG,OAAO,GAAGH,QAAQ,CAACG,OAAO,IAAIH,QAAQ,CAAC5N,EAAE;QAC/C,MAAM0N,SAAS,GACbE,QAAQ,CAAClK,KAAK,IACdkK,QAAQ,CAACI,mBAAmB,IAC5B,aAAaD,OAAO,EAAE;QAExB,IAAI,CAAClS,mBAAmB,CAACuK,IAAI,CAAC;UAC5BpG,EAAE,EAAE,SAASkL,WAAW,EAAE,EAAE;UAC5BlQ,IAAI,EAAE0S,SAAS;UACfjC,IAAI,EAAE;SACP,CAAC;QACFtH,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEsJ,SAAS,CAAC;MAC7D,CAAC,CAAC;MAEF;MACA,IAAIO,mBAAmB,GAAG,EAAE;MAE5B;MACA,IAAIzI,SAAS,CAACiI,YAAY,IAAIjI,SAAS,CAACiI,YAAY,CAACS,gBAAgB,EAAE;QACrE,MAAMC,MAAM,GAAGzI,KAAK,CAACC,OAAO,CAACH,SAAS,CAACiI,YAAY,CAACS,gBAAgB,CAAC,GACjE1I,SAAS,CAACiI,YAAY,CAACS,gBAAgB,GACvC,CAAC1I,SAAS,CAACiI,YAAY,CAACS,gBAAgB,CAAC;QAE7CD,mBAAmB,GAAGE,MAAM,CAAC5D,GAAG,CAAEuD,GAAQ,IAAI;UAC5C,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;YACtD,OAAO;cAAEM,eAAe,EAAEN;YAAG,CAAE;UACjC;UACA,OAAOA,GAAG;QACZ,CAAC,CAAC;MACJ;MACA;MAAA,KACK,IACHtI,SAAS,CAAC6I,aAAa,IACvB3I,KAAK,CAACC,OAAO,CAACH,SAAS,CAAC6I,aAAa,CAAC,EACtC;QACAJ,mBAAmB,GAAGzI,SAAS,CAAC6I,aAAa;MAC/C;MAEAJ,mBAAmB,CAACnE,OAAO,CAAEwE,KAAU,IAAI;QACzC,MAAM/B,IAAI,GAAG+B,KAAK,CAACF,eAAe,IAAIE,KAAK,CAACtO,EAAE;QAC9C,MAAMuO,cAAc,GAAGD,KAAK,CAACE,mBAAmB,IAAIF,KAAK,CAACtT,IAAI;QAC9D,MAAMyT,MAAM,GAAGF,cAAc,IAAI,sBAAsBhC,IAAI,EAAE;QAE7D,IAAI,CAAC9Q,uBAAuB,CAAC2K,IAAI,CAAC;UAChCpG,EAAE,EAAE,aAAakL,WAAW,EAAE,EAAE;UAChClQ,IAAI,EAAEyT,MAAM;UACZhD,IAAI,EAAE;SACP,CAAC;QACFtH,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEqK,MAAM,CAAC;MAC9D,CAAC,CAAC;MAEF;MACA,IAAIC,cAAc,GAAG,EAAE;MACvB,IAAIC,kBAAkB,GAAG,EAAE;MAE3BxK,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;QAC3CoJ,eAAe,EAAE,CAAC,CAAChI,SAAS,CAACiI,YAAY;QACzCmB,mBAAmB,EAAEpJ,SAAS,CAACiI,YAAY;QAC3CoB,QAAQ,EAAErJ,SAAS,CAACsJ,KAAK;QACzBC,YAAY,EAAEvJ,SAAS,CAACsJ,KAAK;QAC7BE,YAAY,EAAExJ,SAAS,CAACyJ,SAAS;QACjCC,gBAAgB,EAAE1J,SAAS,CAACyJ;OAC7B,CAAC;MAEF;MACA,IAAIzJ,SAAS,CAACiI,YAAY,EAAE;QAC1B,IAAIjI,SAAS,CAACiI,YAAY,CAAC0B,OAAO,EAAE;UAClC,MAAMC,QAAQ,GAAG1J,KAAK,CAACC,OAAO,CAACH,SAAS,CAACiI,YAAY,CAAC0B,OAAO,CAAC,GAC1D3J,SAAS,CAACiI,YAAY,CAAC0B,OAAO,GAC9B,CAAC3J,SAAS,CAACiI,YAAY,CAAC0B,OAAO,CAAC;UAEpCT,cAAc,GAAGU,QAAQ,CAAC7E,GAAG,CAAEuD,GAAQ,IAAI;YACzC,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;cACtD,OAAO;gBAAEuB,MAAM,EAAEvB;cAAG,CAAE;YACxB;YACA,OAAOA,GAAG;UACZ,CAAC,CAAC;QACJ;QACA,IAAItI,SAAS,CAACiI,YAAY,CAAC6B,WAAW,EAAE;UACtC,MAAMC,YAAY,GAAG7J,KAAK,CAACC,OAAO,CAACH,SAAS,CAACiI,YAAY,CAAC6B,WAAW,CAAC,GAClE9J,SAAS,CAACiI,YAAY,CAAC6B,WAAW,GAClC,CAAC9J,SAAS,CAACiI,YAAY,CAAC6B,WAAW,CAAC;UAExCX,kBAAkB,GAAGY,YAAY,CAAChF,GAAG,CAAEuD,GAAQ,IAAI;YACjD,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;cACtD,OAAO;gBAAEuB,MAAM,EAAEvB;cAAG,CAAE;YACxB;YACA,OAAOA,GAAG;UACZ,CAAC,CAAC;QACJ;MACF;MACA;MAAA,KACK;QACH,IAAItI,SAAS,CAACsJ,KAAK,IAAIpJ,KAAK,CAACC,OAAO,CAACH,SAAS,CAACsJ,KAAK,CAAC,EAAE;UACrDJ,cAAc,GAAGlJ,SAAS,CAACsJ,KAAK;QAClC;QACA,IAAItJ,SAAS,CAACyJ,SAAS,IAAIvJ,KAAK,CAACC,OAAO,CAACH,SAAS,CAACyJ,SAAS,CAAC,EAAE;UAC7DN,kBAAkB,GAAGnJ,SAAS,CAACyJ,SAAS;QAC1C;MACF;MAEA;MACAP,cAAc,CAAC5E,OAAO,CAAEjG,IAAS,IAAI;QACnC,MAAMwL,MAAM,GAAGxL,IAAI,CAACwL,MAAM,IAAIxL,IAAI,CAAC7D,EAAE;QACrC,MAAMwP,QAAQ,GAAG3L,IAAI,CAAC2L,QAAQ,IAAI3L,IAAI,CAAC7I,IAAI,IAAI,YAAYqU,MAAM,EAAE;QAEnE,IAAI,CAACnT,kBAAkB,CAACkK,IAAI,CAAC;UAC3BpG,EAAE,EAAE,QAAQkL,WAAW,EAAE,EAAE;UAC3BlQ,IAAI,EAAEwU,QAAQ;UACd/D,IAAI,EAAE;SACP,CAAC;QACFtH,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEoL,QAAQ,CAAC;MACnE,CAAC,CAAC;MAEF;MACAb,kBAAkB,CAAC7E,OAAO,CAAE2F,QAAa,IAAI;QAC3C,MAAMC,UAAU,GAAGD,QAAQ,CAACJ,MAAM,IAAII,QAAQ,CAACzP,EAAE;QACjD,MAAM2P,YAAY,GAChBF,QAAQ,CAACD,QAAQ,IAAIC,QAAQ,CAACzU,IAAI,IAAI,iBAAiB0U,UAAU,EAAE;QAErE,IAAI,CAACxT,kBAAkB,CAACkK,IAAI,CAAC;UAC3BpG,EAAE,EAAE,QAAQkL,WAAW,EAAE,EAAE;UAC3BlQ,IAAI,EAAE2U,YAAY;UAClBlE,IAAI,EAAE;SACP,CAAC;QACFtH,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEuL,YAAY,CAAC;MACpE,CAAC,CAAC;MAEFxL,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE;QACrDuI,WAAW,EAAE,IAAI,CAACvR,oBAAoB;QACtCwR,UAAU,EAAE,IAAI,CAAC/Q,mBAAmB;QACpCgR,cAAc,EAAE,IAAI,CAACpR,uBAAuB;QAC5CmU,SAAS,EAAE,IAAI,CAAC1T,kBAAkB;QAClC4Q,cAAc,EAAE,IAAI,CAAChQ;OACtB,CAAC;MAEF;MACAqH,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;QACvCyL,WAAW,EAAE,IAAI,CAACzU,oBAAoB,CAACC,MAAM;QAC7CyU,UAAU,EAAE,IAAI,CAACjU,mBAAmB,CAACR,MAAM;QAC3C0U,cAAc,EAAE,IAAI,CAACtU,uBAAuB,CAACJ,MAAM;QACnD2U,SAAS,EAAE,IAAI,CAAC9T,kBAAkB,CAACb,MAAM;QACzC4U,cAAc,EAAE,IAAI,CAACnT,uBAAuB,CAACzB;OAC9C,CAAC;MAEF;MACA8I,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;QAClCzJ,SAAS,EAAE,IAAI,CAACA,SAAS;QACzBuV,eAAe,EAAE,IAAI,CAACvV,SAAS,KAAK,eAAe;QACnDwV,YAAY,EAAE,IAAI,CAACjU,kBAAkB,CAACb,MAAM,GAAG,CAAC;QAChD+U,aAAa,EAAE,IAAI,CAAClU,kBAAkB,CAACqO,GAAG,CAAE8F,CAAC,IAAKA,CAAC,CAACrV,IAAI;OACzD,CAAC;MAEF;MACA,MAAM+R,aAAa,GAAG,CAAC,CAAC,CAAC;MACzB,MAAMC,eAAe,GACnB,IAAI,CAAC5R,oBAAoB,CAACC,MAAM,GAAG,IAAI,CAACQ,mBAAmB,CAACR,MAAM;MACpE,IAAI,CAACsC,6BAA6B,GAAGsP,IAAI,CAACC,KAAK,CAC5CF,eAAe,GAAGD,aAAa,GAAI,GAAG,CACxC;IACH;;uCApjCWzN,uBAAuB,EAAAtG,EAAA,CAAAsX,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAxX,EAAA,CAAAsX,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAzX,EAAA,CAAAsX,iBAAA,CAAAI,EAAA,CAAAC,mBAAA,GAAA3X,EAAA,CAAAsX,iBAAA,CAAAM,EAAA,CAAAC,sBAAA,GAAA7X,EAAA,CAAAsX,iBAAA,CAAAQ,EAAA,CAAAC,mBAAA,GAAA/X,EAAA,CAAAsX,iBAAA,CAAAU,EAAA,CAAAC,aAAA,GAAAjY,EAAA,CAAAsX,iBAAA,CAAAY,EAAA,CAAAC,WAAA,GAAAnY,EAAA,CAAAsX,iBAAA,CAAAc,EAAA,CAAAC,oBAAA;IAAA;;YAAvB/R,uBAAuB;MAAAgS,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBAavB7Y,iCAAiC;;;;;;;;;;;;UCjDxCI,EAJN,CAAAC,cAAA,aAAuC,aAEZ,aACD,gBAC+C;UAAvCD,EAAA,CAAAI,UAAA,mBAAAuY,yDAAA;YAAA,OAASD,GAAA,CAAA5I,YAAA,EAAc;UAAA,EAAC;UAClD9P,EAAA,CAAA8C,SAAA,kBAIY;UACZ9C,EAAA,CAAAC,cAAA,cAAyB;UAAAD,EAAA,CAAAE,MAAA,GAAe;UAG9CF,EAH8C,CAAAG,YAAA,EAAO,EACxC,EACL,EACF;UAOAH,EAJN,CAAAC,cAAA,aAA0B,aAEyC,cACrC,kBAC+C;UAA1CD,EAAA,CAAAI,UAAA,mBAAAwY,0DAAA;YAAA,OAASF,GAAA,CAAAvI,eAAA,EAAiB;UAAA,EAAC;UACtDnQ,EAAA,CAAA8C,SAAA,oBAKW;UACb9C,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAiC,UAAA,KAAA4W,0CAAA,qBAKC;UAGH7Y,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAiC,UAAA,KAAA6W,uCAAA,mBAAyD;UAuB3D9Y,EAAA,CAAAG,YAAA,EAAM;UAOAH,EAJN,CAAAC,cAAA,eAAyB,eAEG,eACI,kBAKzB;UADCD,EAAA,CAAAI,UAAA,mBAAA2Y,0DAAA;YAAA,OAASL,GAAA,CAAAtI,iBAAA,CAAkB,WAAW,CAAC;UAAA,EAAC;UAExCpQ,EAAA,CAAAE,MAAA,mBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAIC;UADCD,EAAA,CAAAI,UAAA,mBAAA4Y,0DAAA;YAAA,OAASN,GAAA,CAAAtI,iBAAA,CAAkB,QAAQ,CAAC;UAAA,EAAC;UAErCpQ,EAAA,CAAAE,MAAA,sBACF;UAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;UAENH,EAAA,CAAAC,cAAA,eAA2B;UAkkBzBD,EAhkBA,CAAAiC,UAAA,KAAAgX,uCAAA,oBAAsE,KAAAC,uCAAA,kBAgkBN;UAyExElZ,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACF;;;UAttB2BH,EAAA,CAAAuB,SAAA,GAAe;UAAfvB,EAAA,CAAA8B,iBAAA,CAAA4W,GAAA,CAAA9W,SAAA,CAAe;UAQpB5B,EAAA,CAAAuB,SAAA,GAAwC;UAAxCvB,EAAA,CAAAwD,WAAA,cAAAkV,GAAA,CAAAlP,oBAAA,CAAwC;UAIxDxJ,EAAA,CAAAuB,SAAA,GAAgE;UAAhEvB,EAAA,CAAAwB,UAAA,aAAAkX,GAAA,CAAAlP,oBAAA,gCAAgE;UAQjExJ,EAAA,CAAAuB,SAAA,EAA2B;UAA3BvB,EAAA,CAAAwB,UAAA,UAAAkX,GAAA,CAAAlP,oBAAA,CAA2B;UAQJxJ,EAAA,CAAAuB,SAAA,EAA2B;UAA3BvB,EAAA,CAAAwB,UAAA,UAAAkX,GAAA,CAAAlP,oBAAA,CAA2B;UAgCjDxJ,EAAA,CAAAuB,SAAA,GAA+C;UAA/CvB,EAAA,CAAAwD,WAAA,WAAAkV,GAAA,CAAAjP,cAAA,iBAA+C;UAO/CzJ,EAAA,CAAAuB,SAAA,GAA4C;UAA5CvB,EAAA,CAAAwD,WAAA,WAAAkV,GAAA,CAAAjP,cAAA,cAA4C;UAU1CzJ,EAAA,CAAAuB,SAAA,GAAoC;UAApCvB,EAAA,CAAAwB,UAAA,SAAAkX,GAAA,CAAAjP,cAAA,iBAAoC;UAgkBpCzJ,EAAA,CAAAuB,SAAA,EAAiC;UAAjCvB,EAAA,CAAAwB,UAAA,SAAAkX,GAAA,CAAAjP,cAAA,cAAiC;;;qBDvnB3CrK,YAAY,EAAA+Z,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZ1Z,WAAW,EACXC,iCAAiC,EACjCC,aAAa;MAAAyZ,MAAA;IAAA;;SAKJhT,uBAAuB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}