{"ast": null, "code": "import _asyncToGenerator from \"C:/console/aava-ui-web/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Component, ViewChild, ViewContainerRef } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { ButtonComponent, AvaTextboxComponent, AvaTextareaComponent, IconComponent, DialogService } from '@ava/play-comp-library';\nimport { CanvasBoardComponent } from '../../../components/canvas-board/canvas-board.component';\nimport { BuildAgentNodeComponent } from './components/build-agent-node';\nimport { Subject, takeUntil, switchMap, finalize, catchError, of } from 'rxjs';\nimport { CustomTabsComponent } from '../../../components/custom-tabs/custom-tabs.component';\nimport { PlaygroundComponent } from '../../../components/playground/playground.component';\nimport { AgentsPreviewPanelComponent } from './agents-preview-panel/agents-preview-panel.component';\nimport { DropZoneCanvasComponent } from '../../../components/drop-zone-canvas/drop-zone-canvas.component';\nlet BuildAgentsComponent = class BuildAgentsComponent {\n  router;\n  route;\n  toolExecutionService;\n  cdr;\n  toolsService;\n  agentService;\n  promptsService;\n  tokenStorage;\n  agentPlaygroundService;\n  formBuilder;\n  knowledgeBaseService;\n  drawerService;\n  guardrailsService;\n  modelService;\n  dialogService;\n  canvasBoardComponent;\n  drawerContainer;\n  destroy$ = new Subject();\n  activeTab = 'prompts';\n  searchQuery = '';\n  searchForm;\n  isSidebarCollapsed = false;\n  showPreview = false;\n  selectedItem = null;\n  previewData = null;\n  isLoadingPreview = false;\n  canvasNodes = [];\n  canvasEdges = [];\n  buildAgentNodes = [];\n  executeNodes = [];\n  selectedNodeId = null;\n  currentAgentType = 'individual';\n  currentMode = 'create';\n  currentAgentId = null;\n  isFieldsDisabled = false;\n  isDuplicateMode = false;\n  handleEditModeConnections = false;\n  showSuccessPopup = false;\n  showErrorPopup = false;\n  showWarningPopup = false;\n  isApprovalSuccess = false;\n  popupMessage = '';\n  popupTitle = '';\n  executionId;\n  get showApprovalButton() {\n    return false;\n  }\n  currentAgentDetails = null;\n  nodeLimits = {\n    individual: {\n      prompt: 1,\n      model: 1,\n      knowledge: -1,\n      guardrail: -1\n    },\n    collaborative: {\n      prompt: 1,\n      model: 1,\n      knowledge: -1,\n      tool: -1\n    }\n  };\n  // Track the latest node for simple vertical positioning\n  latestNodeId = null;\n  // Drop zone configuration\n  get dropZoneConfig() {\n    return {\n      agentType: this.currentAgentType,\n      allowedCategories: {\n        prompts: {\n          max: 1,\n          required: true\n        },\n        models: {\n          max: 1,\n          required: true\n        },\n        knowledge: {\n          max: -1,\n          required: true\n        },\n        // -1 means unlimited\n        guardrails: {\n          max: -1,\n          required: false\n        },\n        tools: {\n          max: -1,\n          required: false\n        }\n      }\n    };\n  }\n  tabs = [];\n  _customTabs = [];\n  get customTabs() {\n    return this._customTabs;\n  }\n  allToolItems = {\n    prompts: [],\n    models: [],\n    knowledge: [],\n    tools: [],\n    guardrails: []\n  };\n  navigationHints = ['Alt + Drag to pan canvas', 'Mouse wheel to zoom', 'Space to reset view'];\n  isExecuteMode = false;\n  showChatInterface = false;\n  chatMessages = [];\n  isProcessingChat = false;\n  promptOptions = [];\n  executionSubscription;\n  isEditMode = false;\n  isViewMode = false;\n  isNewAgent = true;\n  agentConfigIds = new Map();\n  // Get dynamic button text based on current mode\n  get primaryButtonText() {\n    if (this.isExecuteMode) {\n      return 'Run'; // When in execute mode, show \"Run\"\n    }\n    if (this.isViewMode) {\n      return 'Execute'; // When viewing existing agent, show \"Execute\"\n    }\n    if (this.isEditMode) {\n      // For collaborative agents in edit mode, use \"Update and Send Approval\"\n      if (this.currentAgentType === 'collaborative') {\n        return 'Update and Send Approval';\n      }\n      return 'Update'; // When editing existing individual agent, show \"Update\"\n    }\n    if (this.isDuplicateMode) {\n      return 'Save'; // When duplicating agent, show \"Save\"\n    }\n    // For collaborative agents in create mode, use \"Save and Send Approval\"\n    if (this.currentAgentType === 'collaborative') {\n      return 'Save and Send Approval';\n    }\n    return 'Save'; // When creating new individual agent, show \"Save\"\n  }\n  // Agent data properties for saving\n  agentName = '';\n  agentDetail = '';\n  agentCode = ''; // Store the agent code (e.g., BUGBUSTERCHATBOT) for API calls\n  // Agent Details Floater state\n  isAgentDetailsCollapsed = true;\n  agentMetadata = {\n    org: '',\n    domain: '',\n    project: '',\n    team: ''\n  };\n  buildOrganizationPath() {\n    const {\n      org,\n      domain,\n      project,\n      team\n    } = this.agentMetadata;\n    return org && domain && project && team ? `@${org}@${domain}@${project}@${team}` : '@ASCENDION@PLATFORM_ENGINEERING@AVA@DIGITAL_ASCENDER';\n  }\n  // Cache for labels API response to avoid multiple calls\n  labelsCache = null;\n  // Agent Playground Properties\n  agentPlaygroundForm;\n  selectedPrompt = 'default';\n  selectedAgentMode = '';\n  selectedUseCaseIdentifier = '';\n  agentChatPayload = [];\n  agentFilesUploadedData = [];\n  agentAttachment = [];\n  isAgentPlaygroundLoading = false;\n  agentPlaygroundDestroy = new Subject();\n  responseModalConfig = {\n    id: 'responseModal',\n    type: 'dialog',\n    title: 'Agent Response',\n    closeButton: false,\n    primaryButtonConfig: {\n      text: 'OK',\n      buttonType: 'primary',\n      buttonSize: 'medium',\n      imageUrl: '',\n      linkURL: ''\n    }\n  };\n  isResponseModalOpen = false;\n  modalMessage = '';\n  isModalError = false;\n  autoSelectedAgentFromCard = null;\n  testSpaceInput = '';\n  // Track guardrail toggle states\n  guardrailToggleStates = {};\n  constructor(router, route, toolExecutionService, cdr, toolsService, agentService, promptsService, tokenStorage, agentPlaygroundService, formBuilder, knowledgeBaseService, drawerService, guardrailsService, modelService, dialogService) {\n    this.router = router;\n    this.route = route;\n    this.toolExecutionService = toolExecutionService;\n    this.cdr = cdr;\n    this.toolsService = toolsService;\n    this.agentService = agentService;\n    this.promptsService = promptsService;\n    this.tokenStorage = tokenStorage;\n    this.agentPlaygroundService = agentPlaygroundService;\n    this.formBuilder = formBuilder;\n    this.knowledgeBaseService = knowledgeBaseService;\n    this.drawerService = drawerService;\n    this.guardrailsService = guardrailsService;\n    this.modelService = modelService;\n    this.dialogService = dialogService;\n    this.agentPlaygroundForm = this.formBuilder.group({\n      isConversational: [true],\n      isUseTemplate: [false]\n    });\n    this.searchForm = this.formBuilder.group({\n      search: ['']\n    });\n  }\n  ngOnInit() {\n    this.executionId = crypto.randomUUID();\n    this.route.params.subscribe(params => {\n      this.currentAgentType = params['type'] === 'individual' || params['type'] === 'collaborative' ? params['type'] : 'individual';\n      this.currentAgentDetails = null;\n      this.configureTabsForAgentType();\n      this.loadDataForAgentType();\n    });\n    this.route.queryParams.subscribe(params => {\n      if (params['id']) {\n        this.currentAgentId = params['id'];\n        const mode = params['mode'];\n        this.isViewMode = this.isEditMode = this.isExecuteMode = this.isDuplicateMode = this.isNewAgent = false;\n        this.isFieldsDisabled = false;\n        console.log('🔧 MODE DETECTION:', {\n          mode,\n          currentAgentId: this.currentAgentId,\n          url: this.router.url\n        });\n        if (mode === 'view') {\n          this.isViewMode = true;\n          this.isFieldsDisabled = true;\n        } else if (mode === 'edit') {\n          this.isEditMode = true;\n          this.handleEditModeConnections = true;\n        } else if (mode === 'execute') {\n          // Redirect to the new agent execution component\n          this.router.navigate(['/build/agents', this.currentAgentType, 'execute'], {\n            queryParams: {\n              id: params['id']\n            }\n          });\n          return;\n        } else if (mode === 'duplicate') {\n          this.isDuplicateMode = true;\n          this.agentConfigIds.clear();\n        } else {\n          this.isNewAgent = true;\n        }\n        console.log('🔧 MODE FLAGS AFTER DETECTION:', {\n          isViewMode: this.isViewMode,\n          isEditMode: this.isEditMode,\n          isExecuteMode: this.isExecuteMode,\n          isDuplicateMode: this.isDuplicateMode,\n          isNewAgent: this.isNewAgent\n        });\n        this.loadAgentData(params['id']);\n      } else {\n        this.isNewAgent = true;\n        this.isEditMode = this.isViewMode = false;\n        this.isFieldsDisabled = false;\n        this.currentAgentId = null;\n      }\n    });\n    this.activeTab = 'prompts';\n    this.searchForm.get('search')?.valueChanges.pipe(takeUntil(this.destroy$)).subscribe(value => {\n      this.searchQuery = value || '';\n      this.updateFilteredTools();\n    });\n    this.updateFilteredTools();\n  }\n  ngAfterViewInit() {\n    this.cdr.detectChanges();\n    setTimeout(() => {\n      if (this.canvasBoardComponent && this.buildAgentNodes.length > 0) {\n        this.canvasBoardComponent.updateNodeConnectionPoints();\n        if (this.canvasEdges.length === 0 && this.buildAgentNodes.length > 1) {\n          this.createAgentFlowConnections(this.buildAgentNodes);\n        }\n      }\n    }, 300);\n    this.drawerService.registerViewContainer(this.drawerContainer);\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n    if (this.executionSubscription) {\n      this.executionSubscription.unsubscribe();\n    }\n    this.agentPlaygroundDestroy.next(true);\n    this.agentPlaygroundDestroy.complete();\n  }\n  generateMockItems(type, name, icon, count) {\n    return Array.from({\n      length: count\n    }, (_, i) => ({\n      id: `${type}${i + 1}`,\n      name,\n      icon,\n      type: type,\n      description: 'AI agents are software programs that use artificial intelligence to perform tasks and achieve goals.'\n    }));\n  }\n  loadPrompts() {\n    this.promptsService.fetchAllPrompts().subscribe({\n      next: prompts => {\n        const filteredPrompts = this.filterPromptsByAgentType(prompts);\n        this.allToolItems['prompts'] = filteredPrompts.map(prompt => ({\n          id: prompt.id?.toString() || Math.random().toString(),\n          name: prompt.name,\n          description: prompt.description || prompt.descriptionConsolidated,\n          promptDescription: prompt.promptDescription,\n          icon: 'assets/images/prompt.png',\n          type: 'prompt',\n          promptType: prompt.type,\n          role: prompt.role,\n          goal: prompt.goal,\n          backstory: prompt.backstory,\n          expectedOutput: prompt.expectedOutput,\n          descriptionConsolidated: prompt.descriptionConsolidated,\n          expectedOutputConsolidated: prompt.expectedOutputConsolidated,\n          originalPromptData: prompt\n        }));\n        this.updateFilteredTools();\n      },\n      error: error => {\n        console.error('Error loading prompts:', error);\n      }\n    });\n  }\n  filterPromptsByAgentType(prompts) {\n    if (!Array.isArray(prompts) || prompts.length === 0) return [];\n    if (this.currentAgentType === 'collaborative') {\n      return prompts.filter(prompt => prompt.type?.trim() !== 'free form');\n    } else {\n      return prompts; // Return all prompts\n    }\n  }\n  loadModels() {\n    if (this.currentAgentType === 'collaborative') {\n      this.agentService.getCollaborativeModels().subscribe({\n        next: response => {\n          if (response && Array.isArray(response)) {\n            this.allToolItems['models'] = response.map(model => ({\n              id: model.id,\n              name: `${model.modelDeploymentName} (${model.model})`,\n              description: model.modelDescription || `${model.modelType} model via ${model.aiEngine}`,\n              icon: 'assets/images/Boundingbox.png',\n              type: 'model',\n              model: model.model,\n              aiEngine: model.aiEngine,\n              modelType: model.modelType\n            }));\n          } else if (response && response.models) {\n            this.allToolItems['models'] = response.models.map(model => ({\n              id: model.id,\n              name: model.model,\n              description: model.modelDescription,\n              icon: 'assets/images/Boundingbox.png',\n              type: 'model'\n            }));\n          }\n          this.updateFilteredTools();\n        },\n        error: error => {\n          console.error('Error loading models from direct API:', error);\n          this.loadModelsFromLabels();\n        }\n      });\n    } else {\n      this.loadModelsFromLabels();\n    }\n  }\n  loadModelsFromLabels() {\n    this.agentService.getModelsFromLabels().subscribe({\n      next: models => {\n        this.allToolItems['models'] = models.map(model => ({\n          id: model.id,\n          name: model.name,\n          description: model.description,\n          icon: model.icon,\n          type: 'model'\n        }));\n        this.updateFilteredTools();\n      },\n      error: error => {\n        console.error('Error loading models from labels API:', error);\n        this.allToolItems['models'] = this.generateMockItems('model', 'Model Name', 'assets/images/Boundingbox.png', 2);\n        this.updateFilteredTools();\n      }\n    });\n  }\n  loadModelsFromCache() {\n    if (!this.labelsCache) return;\n    const categoryLabels = this.labelsCache.categoryLabels || [];\n    const modelCategory = categoryLabels.find(category => category.categoryId === 1);\n    if (modelCategory) {\n      const modelLabel = modelCategory.labels.find(label => label.labelCode === 'MODEL');\n      if (modelLabel && modelLabel.labelValues) {\n        const parsedModels = this.agentService.parseLabelValues(modelLabel.labelValues);\n        this.allToolItems['models'] = parsedModels.map(option => ({\n          id: option.value,\n          name: option.name,\n          type: 'model',\n          icon: 'assets/images/model.png',\n          description: `Model: ${option.name}`\n        }));\n        this.updateFilteredTools();\n      }\n    }\n  }\n  loadKnowledgeBase() {\n    if (this.currentAgentType === 'collaborative') {\n      // Use knowledge base service directly for collaborative agents\n      this.knowledgeBaseService.fetchAllKnowledge().subscribe({\n        next: response => {\n          // Transform CardData format to the expected format for collaborative agents\n          this.allToolItems['knowledge'] = response.map(kb => ({\n            id: kb.id,\n            name: kb.collectionName || kb.title || kb.name || 'Unknown Knowledge Base',\n            description: kb.description || `Knowledge Base: ${kb.collectionName || kb.title || kb.name}`,\n            icon: 'assets/images/import_contacts.png',\n            type: 'knowledge',\n            // Add any additional properties if available\n            vectorDb: kb.vectorDb,\n            modelRef: kb.modelRef,\n            splitSize: kb.splitSize\n          }));\n          this.updateFilteredTools();\n        },\n        error: error => {\n          console.error('Error loading knowledge bases from knowledge base service:', error);\n          // For collaborative agents, don't fallback to labels API\n          // Just use empty array or mock data\n          this.allToolItems['knowledge'] = this.generateMockItems('knowledge', 'Knowledge Base Name', 'assets/images/import_contacts.png', 2);\n          this.updateFilteredTools();\n        }\n      });\n    } else {\n      this.loadKnowledgeBaseFromLabels();\n    }\n  }\n  loadKnowledgeBaseFromLabels() {\n    this.agentService.getKnowledgeBasesFromLabels().subscribe({\n      next: knowledgeItems => {\n        this.allToolItems['knowledge'] = knowledgeItems.map(item => ({\n          id: item.id,\n          name: item.name,\n          description: item.description,\n          icon: item.icon,\n          type: 'knowledge'\n        }));\n        this.updateFilteredTools();\n      },\n      error: error => {\n        console.error('Error loading knowledge bases from labels API:', error);\n        // Fallback to mock data if API fails\n        this.allToolItems['knowledge'] = this.generateMockItems('knowledge', 'Knowledge Base Name', 'assets/images/import_contacts.png', 2);\n        this.updateFilteredTools();\n      }\n    });\n  }\n  // Load knowledge bases from cached labels data\n  loadKnowledgeBaseFromCache() {\n    if (!this.labelsCache) return;\n    const categoryLabels = this.labelsCache.categoryLabels || [];\n    const iclCategory = categoryLabels.find(category => category.categoryId === 2);\n    if (iclCategory) {\n      const knowledgeLabel = iclCategory.labels.find(label => label.labelCode === 'RAG_KNOWLEDGEBASE_NAME');\n      if (knowledgeLabel && knowledgeLabel.labelValues) {\n        const parsedKnowledge = this.agentService.parseLabelValues(knowledgeLabel.labelValues);\n        this.allToolItems['knowledge'] = parsedKnowledge.map(option => ({\n          id: option.value,\n          // This is the actual knowledge base ID we need for payload\n          name: option.name,\n          type: 'knowledge',\n          icon: 'assets/images/knowledge.png',\n          description: `Knowledge Base: ${option.name}`\n        }));\n      }\n    }\n  }\n  loadTools() {\n    // Load both built-in and user-defined tools\n    const builtInTools$ = this.toolsService.getBuiltInToolsList();\n    // Load built-in tools first\n    builtInTools$.subscribe({\n      next: response => {\n        const builtInTools = response.tools || [];\n        const builtInToolItems = builtInTools.map(tool => ({\n          id: `builtin-${tool.toolId}`,\n          name: tool.toolName || 'Unknown Built-in Tool',\n          description: 'Built-in tool for AI agent tasks',\n          icon: 'assets/images/build.png',\n          type: 'tool'\n        }));\n        // Then load user-defined tools with dynamic records count\n        this.loadUserToolsWithPagination(builtInToolItems);\n      },\n      error: error => {\n        console.error('Error loading built-in tools:', error);\n        // Still try to load user tools even if built-in tools fail\n        this.loadUserToolsWithPagination([]);\n      }\n    });\n  }\n  loadUserToolsWithPagination(builtInToolItems) {\n    // First, get the total count by making an initial API call\n    this.toolsService.getUserToolsList(1, 11).subscribe({\n      next: userResponse => {\n        const totalRecords = userResponse.totalNoOfRecords || 51;\n        // Now load all user tools using the total count\n        this.toolsService.getUserToolsList(1, totalRecords).subscribe({\n          next: fullUserResponse => {\n            const userTools = fullUserResponse.userToolDetails || [];\n            const userToolItems = userTools.map(tool => ({\n              id: `user-${tool.id}`,\n              name: tool.name || 'Unknown User Tool',\n              description: tool.description || 'User-defined tool for AI agent tasks',\n              icon: 'assets/images/build.png',\n              type: 'tool'\n            }));\n            // Combine both built-in and user-defined tools\n            this.allToolItems['tools'] = [...builtInToolItems, ...userToolItems];\n            this.updateFilteredTools();\n          },\n          error: error => {\n            console.error('Error loading full user tools list:', error);\n            // Use only built-in tools if user tools fail\n            this.allToolItems['tools'] = builtInToolItems;\n            this.updateFilteredTools();\n          }\n        });\n      },\n      error: error => {\n        console.error('Error loading user tools:', error);\n        // Use only built-in tools if user tools fail\n        this.allToolItems['tools'] = builtInToolItems;\n        this.updateFilteredTools();\n      }\n    });\n  }\n  loadGuardrails() {\n    this.agentService.getGuardrailsFromLabels().subscribe({\n      next: guardrails => {\n        this.allToolItems['guardrails'] = guardrails.map(guardrail => ({\n          id: guardrail.id,\n          name: guardrail.name,\n          description: guardrail.description,\n          icon: guardrail.icon,\n          type: 'guardrail',\n          code: guardrail.code\n        }));\n        this.updateFilteredTools();\n        this.updateFilteredTools();\n      },\n      error: error => {\n        console.error('Error loading guardrails from labels API:', error);\n        // Fallback to mock data if API fails\n        this.allToolItems['guardrails'] = this.generateMockItems('guardrail', 'Guardrail Name', 'assets/images/swords.png', 2);\n        this.updateFilteredTools();\n      }\n    });\n  }\n  // Load guardrails from cached labels data\n  loadGuardrailsFromCache() {\n    if (!this.labelsCache) return;\n    const categoryLabels = this.labelsCache.categoryLabels || [];\n    const otherCategory = categoryLabels.find(category => category.categoryId === 3);\n    if (otherCategory) {\n      // Include ALL toggle labels from Other category, including \"Enable Guardrails\"\n      this.allToolItems['guardrails'] = otherCategory.labels.filter(label => label.labelType === 'Toggle') // Include all toggle types\n      .map(label => ({\n        id: label.labelId.toString(),\n        name: label.labelName,\n        code: label.labelCode,\n        type: 'guardrail',\n        icon: 'assets/images/guardrail.png',\n        description: label.labelInfo || `Guardrail: ${label.labelName}`\n      }));\n    }\n  }\n  _filteredTools = [];\n  get currentTabTools() {\n    return this.allToolItems[this.activeTab] || [];\n  }\n  get filteredTools() {\n    return this._filteredTools;\n  }\n  updateFilteredTools() {\n    const currentTools = this.currentTabTools;\n    const availableTools = currentTools.filter(tool => {\n      const isAlreadyAdded = this.buildAgentNodes.some(node => node.originalToolData && node.originalToolData.id === tool.id);\n      return !isAlreadyAdded;\n    });\n    if (!this.searchQuery) {\n      this._filteredTools = availableTools;\n      return;\n    }\n    const query = this.searchQuery.toLowerCase();\n    this._filteredTools = availableTools.filter(tool => tool.name && tool.name.toLowerCase().includes(query) || tool.description && tool.description.toLowerCase().includes(query));\n  }\n  onTabChange(tabValue) {\n    this.activeTab = tabValue.toString();\n    this.searchQuery = '';\n    this.searchForm.get('search')?.setValue('', {\n      emitEvent: false\n    });\n    this.updateFilteredTools();\n  }\n  onSearchChange(query) {\n    this.searchQuery = query;\n    this.updateFilteredTools();\n  }\n  onCustomTabChange(tabValue) {\n    this.activeTab = tabValue.toString();\n    this.searchQuery = '';\n    this.searchForm.get('search')?.setValue('', {\n      emitEvent: false\n    });\n    this.updateFilteredTools();\n  }\n  toggleSidebar() {\n    this.isSidebarCollapsed = !this.isSidebarCollapsed;\n  }\n  isActiveCustomTab(tabValue) {\n    return this.activeTab === tabValue.toString();\n  }\n  getTabIcon(tab) {\n    const iconMap = {\n      prompts: 'FileText',\n      models: 'Box',\n      knowledge: 'BookOpen',\n      guardrails: 'Swords',\n      tools: 'Wrench'\n    };\n    return iconMap[tab.value] || 'Circle';\n  }\n  handleAutoTabSwitch(nodeType) {\n    const currentLimits = this.nodeLimits[this.currentAgentType];\n    const currentNodeLimit = currentLimits[nodeType];\n    if (currentNodeLimit === 1) {\n      const nextTab = this.getNextRequiredTab(nodeType);\n      if (nextTab) {\n        this.activeTab = nextTab;\n        this.searchQuery = '';\n        this.searchForm.get('search')?.setValue('', {\n          emitEvent: false\n        });\n        this.updateFilteredTools();\n      }\n    }\n  }\n  getNextRequiredTab(currentNodeType) {\n    const currentLimits = this.nodeLimits[this.currentAgentType];\n    if (currentNodeType === 'prompt') {\n      const hasModel = this.buildAgentNodes.some(node => node.type === 'model');\n      if (!hasModel && currentLimits['model'] === 1) {\n        return 'models';\n      }\n    } else if (currentNodeType === 'model') {\n      return 'knowledge';\n    }\n    return null;\n  }\n  onItemPreview(item) {\n    this.selectedItem = item;\n    this.showPreview = true;\n    this.loadPreviewData(item);\n    this.drawerService.open(AgentsPreviewPanelComponent, {\n      previewData: this.previewData,\n      closePreview: () => this.drawerService.clear()\n    });\n  }\n  closePreview() {\n    this.showPreview = false;\n    this.selectedItem = null;\n    this.previewData = null;\n  }\n  loadPreviewData(item) {\n    this.previewData = {\n      type: item.type,\n      title: item.name,\n      data: {\n        ...item\n      },\n      loading: true,\n      error: null\n    };\n    // Fetch detailed data based on item type\n    switch (item.type) {\n      case 'prompt':\n        this.loadPromptDetails(item);\n        break;\n      case 'model':\n        this.loadModelDetails(item);\n        break;\n      case 'knowledge':\n        this.loadKnowledgeDetails(item);\n        break;\n      case 'guardrail':\n        this.loadGuardrailDetails(item);\n        break;\n      case 'tool':\n        this.loadToolDetails(item);\n        break;\n      default:\n        this.previewData.loading = false;\n    }\n  }\n  loadPromptDetails(item) {\n    if (item.id) {\n      this.promptsService.getPromptById(item.id.toString()).subscribe({\n        next: response => {\n          this.previewData.data = {\n            ...this.previewData.data,\n            ...response,\n            description: response.description || response.descriptionConsolidated || item.description,\n            promptTask: response.template || response.content || response.promptTemplate || response.prompt,\n            createdBy: response.createdBy || response.author || 'Unknown',\n            updatedAt: response.updatedAt || response.createdAt || new Date().toISOString(),\n            role: response.role,\n            goal: response.goal,\n            backstory: response.backstory,\n            expectedOutput: response.expectedOutput || response.expectedOutputConsolidated\n          };\n          this.previewData.loading = false;\n        },\n        error: error => {\n          console.error('Error loading prompt details:', error);\n          this.previewData.error = 'Failed to load prompt details';\n          this.previewData.loading = false;\n        }\n      });\n    } else {\n      this.previewData.loading = false;\n    }\n  }\n  loadModelDetails(item) {\n    if (item.id && this.modelService?.getOneModeById) {\n      this.modelService.getOneModeById(item.id.toString()).subscribe({\n        next: response => {\n          const modelData = response;\n          this.previewData.data = {\n            ...this.previewData.data,\n            modelDescription: modelData.modelDescription || item.description,\n            modelType: modelData.model || item.name,\n            temperature: modelData.temperature || 0.7,\n            maxToken: modelData.maxToken || 4000,\n            topP: modelData.topP || 0.95,\n            maxIteration: modelData.maxIteration || 1,\n            aiEngine: modelData.aiEngine || 'OpenAI',\n            baseUrl: modelData.serviceUrl || modelData.baseurl,\n            llmDeploymentName: modelData.modelDeploymentName || item.name,\n            apiKey: modelData.apiKeyEncoded || modelData.awsSecretKey,\n            apiVersion: modelData.apiVersion,\n            headerName: modelData.headerName || '',\n            model: modelData.model || '',\n            createdBy: modelData.createdBy || 'Unknown',\n            createdOn: modelData.date || new Date().toISOString()\n          };\n          this.previewData.loading = false;\n        },\n        error: error => {\n          console.error('Error loading model details:', error);\n          this.setDefaultModelData(item);\n        }\n      });\n    } else {\n      this.setDefaultModelData(item);\n    }\n  }\n  setDefaultModelData(item) {\n    this.previewData.data = {\n      ...this.previewData.data,\n      modelDescription: item.description || `Model: ${item.name}`,\n      modelType: item.name,\n      temperature: 0.7,\n      maxToken: 4000,\n      topP: 0.95,\n      maxIteration: 1,\n      aiEngine: 'OpenAI',\n      baseUrl: '',\n      llmDeploymentName: item.name,\n      apiKey: '***',\n      apiVersion: '2024-02-15-preview',\n      createdBy: 'System',\n      createdOn: new Date().toISOString()\n    };\n    this.previewData.loading = false;\n  }\n  loadKnowledgeDetails(item) {\n    if (item.id) {\n      this.knowledgeBaseService.getKnowledgeBaseById(item.id.toString()).subscribe({\n        next: response => {\n          this.previewData.data = {\n            ...this.previewData.data,\n            name: item.name,\n            description: item.description,\n            embeddingModel: response.files?.[0]?.modelDeploymentName || 'text-embedding-ada-002',\n            splitSize: 0.5,\n            uploadType: 'Vector Database',\n            retrieverType: response.retrieverType || 'normal',\n            files: response.files || [],\n            fileCount: response.files?.length || 0,\n            totalSize: response.files?.reduce((sum, file) => sum + (file.fileSizeBytes || 0), 0) || 0,\n            createdBy: response.files?.[0]?.uploadedBy || 'Unknown',\n            createdDate: response.files?.[0]?.uploadDate || new Date().toISOString()\n          };\n          this.previewData.loading = false;\n        },\n        error: error => {\n          console.error('Error loading knowledge base details:', error);\n          this.setDefaultKnowledgeData(item);\n        }\n      });\n    }\n  }\n  setDefaultKnowledgeData(item) {\n    this.previewData.data = {\n      ...this.previewData.data,\n      name: item.name,\n      description: item.description || `Knowledge Base: ${item.name}`,\n      embeddingModel: 'text-embedding-ada-002',\n      splitSize: 0.5,\n      uploadType: 'Vector Database',\n      files: [],\n      createdBy: 'System',\n      createdDate: new Date().toISOString()\n    };\n    this.previewData.loading = false;\n  }\n  loadGuardrailDetails(item) {\n    if (item.name && this.guardrailsService?.getGuardrailByName) {\n      this.guardrailsService.getGuardrailByName(item.name).subscribe({\n        next: guardrail => {\n          if (guardrail) {\n            this.previewData.data = {\n              ...this.previewData.data,\n              name: guardrail.name || item.name,\n              description: guardrail.description || item.description,\n              content: guardrail.content,\n              yamlContent: guardrail.yamlContent || '',\n              organization: guardrail.organization || 'Ascendion',\n              configKey: guardrail.configKey || '',\n              chatBot: guardrail.chatBot || false,\n              createdBy: guardrail.createdBy || 'Unknown',\n              createdDate: guardrail.createdDate || guardrail.createdOn || new Date().toISOString()\n            };\n          }\n          this.previewData.loading = false;\n        },\n        error: error => {\n          console.error('Error loading guardrail details:', error);\n        }\n      });\n    }\n  }\n  setDefaultGuardrailData(item) {\n    this.previewData.data = {\n      ...this.previewData.data,\n      name: item.name,\n      description: item.description || `Guardrail: ${item.name}`,\n      content: '# Guardrail configuration\\n# Add your Colang rules here',\n      createdBy: 'System',\n      createdDate: new Date().toISOString()\n    };\n    this.previewData.loading = false;\n  }\n  loadToolDetails(item) {\n    if (item.id && this.toolsService.getUserToolDetails) {\n      // Extract numeric ID from string like \"user-423\"\n      let toolId;\n      if (typeof item.id === 'string' && item.id.startsWith('user-')) {\n        // Extract number from \"user-423\" format\n        const numericPart = item.id.replace('user-', '');\n        toolId = Number(numericPart);\n      } else {\n        toolId = Number(item.id);\n      }\n      if (isNaN(toolId)) {\n        console.warn('Invalid tool ID:', item.id, 'Using fallback data');\n        this.previewData.loading = false;\n        return;\n      }\n      this.toolsService.getUserToolDetails(toolId).subscribe({\n        next: response => {\n          console.log('Tool details response:', response); // Debug log\n          // Handle different response structures\n          let toolDetail, toolConfigs;\n          if (response.userToolDetail) {\n            // New API format\n            toolDetail = response.userToolDetail;\n            toolConfigs = toolDetail.toolConfigs || {};\n          } else if (response.tools && response.tools[0]) {\n            // Old API format\n            const tool = response.tools[0];\n            toolDetail = {\n              id: tool.toolId,\n              name: tool.toolName,\n              description: tool.toolDescription,\n              createdBy: tool.createdBy,\n              createdAt: tool.createTimestamp,\n              modifiedAt: tool.updateTimestamp,\n              isDeleted: !tool.isApproved\n            };\n            toolConfigs = {\n              tool_class_name: [tool.toolClassName],\n              tool_class_def: [tool.toolClassDef]\n            };\n          } else {\n            // Fallback\n            toolDetail = response;\n            toolConfigs = {};\n          }\n          // Extract functionality/code from various possible fields\n          let functionality = 'Tool code not found';\n          if (toolConfigs.tool_class_def && toolConfigs.tool_class_def[0]) {\n            functionality = toolConfigs.tool_class_def[0];\n          } else if (toolDetail.toolClassDef) {\n            functionality = toolDetail.toolClassDef;\n          } else if (toolDetail.code) {\n            functionality = toolDetail.code;\n          } else if (toolDetail.definition) {\n            functionality = toolDetail.definition;\n          }\n          this.previewData.data = {\n            ...this.previewData.data,\n            id: toolDetail.id,\n            name: toolDetail.name || item.name,\n            description: toolDetail.description || item.description,\n            className: toolConfigs.tool_class_name?.[0] || toolDetail.toolClassName || 'Unknown',\n            functionality: functionality,\n            isApproved: !toolDetail.isDeleted,\n            createdBy: toolDetail.createdBy || 'Unknown',\n            createdOn: toolDetail.createdAt || toolDetail.createTimestamp || new Date().toISOString(),\n            modifiedBy: toolDetail.modifiedBy,\n            modifiedAt: toolDetail.modifiedAt || toolDetail.updateTimestamp,\n            isDeleted: toolDetail.isDeleted || false\n          };\n          this.previewData.loading = false;\n        },\n        error: error => {\n          console.error('Error loading tool details:', error);\n          this.previewData.error = 'Failed to load tool details';\n          this.previewData.loading = false;\n        }\n      });\n    } else {\n      this.previewData.loading = false;\n    }\n  }\n  getIconForType(type) {\n    const iconMap = {\n      prompt: 'FileText',\n      model: 'assets/images/deployed_code.png',\n      knowledge: 'assets/images/import_contacts.png',\n      tool: 'assets/images/build.png',\n      guardrail: 'assets/images/swords.png'\n    };\n    return iconMap[type] || 'assets/images/build.png'; // Default to tool icon\n  }\n  getAdditionalFields(data) {\n    if (!data) return [];\n    const excludeFields = ['id', 'name', 'description', 'labelCode', 'categoryName', 'categoryId', 'labelInfo'];\n    const additionalFields = [];\n    Object.keys(data).forEach(key => {\n      if (!excludeFields.includes(key) && data[key] !== null && data[key] !== undefined) {\n        additionalFields.push({\n          key: this.formatFieldName(key),\n          value: data[key]\n        });\n      }\n    });\n    return additionalFields;\n  }\n  formatFieldName(fieldName) {\n    return fieldName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()).trim();\n  }\n  configureTabsForAgentType() {\n    if (this.currentAgentType === 'individual') {\n      this.tabs = [{\n        id: 'prompts',\n        label: 'Prompts',\n        iconName: 'FileText'\n      }, {\n        id: 'models',\n        label: 'Models',\n        iconName: 'Box'\n      }, {\n        id: 'knowledge',\n        label: 'Knowledge Base',\n        iconName: 'BookOpen'\n      }, {\n        id: 'guardrails',\n        label: 'Guardrails',\n        iconName: 'Swords'\n      }];\n    } else {\n      this.tabs = [{\n        id: 'prompts',\n        label: 'Prompts',\n        iconName: 'FileText'\n      }, {\n        id: 'models',\n        label: 'Models',\n        iconName: 'Box'\n      }, {\n        id: 'knowledge',\n        label: 'Knowledge Base',\n        iconName: 'BookOpen'\n      }, {\n        id: 'tools',\n        label: 'Tools',\n        iconName: 'Wrench'\n      }];\n    }\n    this._customTabs = this.tabs.map(tab => ({\n      label: tab.label || '',\n      value: tab.id,\n      icon: tab.iconName || '',\n      disabled: false\n    }));\n    this.activeTab = 'models';\n  }\n  canAddNodeOfType(nodeType) {\n    const currentLimits = this.nodeLimits[this.currentAgentType];\n    const limit = currentLimits[nodeType];\n    if (limit === undefined || limit === -1) return true;\n    const currentCount = this.buildAgentNodes.filter(node => node.type === nodeType).length;\n    return currentCount < limit;\n  }\n  getNodeLimit(nodeType) {\n    const currentLimits = this.nodeLimits[this.currentAgentType];\n    return currentLimits[nodeType] || -1;\n  }\n  removeItemFromList(tool) {\n    let tabType;\n    if (tool.type === 'guardrail') {\n      tabType = 'guardrails';\n    } else if (tool.type === 'knowledge') {\n      tabType = 'knowledge';\n    } else {\n      tabType = `${tool.type}s`;\n    }\n    if (this.allToolItems[tabType]) {\n      this.allToolItems[tabType] = this.allToolItems[tabType].filter(item => item.id !== tool.id);\n    }\n  }\n  addItemBackToList(node) {\n    let tabType;\n    if (node.type === 'guardrail') {\n      tabType = 'guardrails';\n    } else if (node.type === 'knowledge') {\n      tabType = 'knowledge';\n    } else {\n      tabType = `${node.type}s`;\n    }\n    if (this.allToolItems[tabType]) {\n      const originalToolId = node.originalToolData?.id;\n      const existingItem = this.allToolItems[tabType].find(item => item.name === node.name || item.id === node.id || originalToolId && item.id === originalToolId);\n      if (!existingItem) {\n        let toolItem;\n        if (node.originalToolData) {\n          toolItem = {\n            ...node.originalToolData\n          };\n        } else {\n          toolItem = {\n            id: node.id,\n            name: node.name,\n            description: `${node.type} component: ${node.name}`,\n            icon: node.icon || this.getIconForType(node.type),\n            type: node.type\n          };\n        }\n        this.allToolItems[tabType].push(toolItem);\n      }\n    }\n  }\n  isTabRequired(tabValue) {\n    const nodeType = tabValue === 'guardrails' ? 'guardrail' : tabValue.slice(0, -1);\n    const currentLimits = this.nodeLimits[this.currentAgentType];\n    const limit = currentLimits[nodeType];\n    return limit === 1;\n  }\n  validateMandatoryComponents() {\n    const missingComponents = [];\n    const currentLimits = this.nodeLimits[this.currentAgentType];\n    Object.entries(currentLimits).forEach(([nodeType, limit]) => {\n      if (limit === 1) {\n        const hasComponent = this.buildAgentNodes.some(node => node.type === nodeType);\n        if (!hasComponent) {\n          missingComponents.push(nodeType);\n        }\n      }\n    });\n    return {\n      isValid: missingComponents.length === 0,\n      missingComponents\n    };\n  }\n  loadDataForAgentType() {\n    this.allToolItems = {\n      prompts: [],\n      models: [],\n      knowledge: [],\n      tools: [],\n      guardrails: []\n    };\n    this.loadLabelsAndData();\n  }\n  loadLabelsAndData() {\n    if (this.currentAgentType === 'collaborative') {\n      this.loadPrompts();\n      this.loadModels();\n      this.loadKnowledgeBase();\n      this.loadTools();\n    } else {\n      this.agentService.getLabels().subscribe({\n        next: response => {\n          this.labelsCache = response;\n          this.loadPrompts();\n          this.loadModelsFromCache();\n          this.loadKnowledgeBaseFromCache();\n          this.loadGuardrailsFromCache();\n        },\n        error: error => {\n          console.error('Error loading labels API:', error);\n          this.loadPrompts();\n          this.loadModels();\n          this.loadKnowledgeBase();\n          this.loadGuardrails();\n        }\n      });\n    }\n  }\n  getBuilderTitle() {\n    return this.currentAgentType === 'individual' ? 'Individual Agent Builder' : 'Collaborative Agent Builder';\n  }\n  onDragStart(event, tool) {\n    if (event.dataTransfer) {\n      event.dataTransfer.setData('application/reactflow', JSON.stringify(tool));\n      event.dataTransfer.effectAllowed = 'move';\n    }\n  }\n  onCanvasDropped(event) {\n    const toolData = event.event.dataTransfer?.getData('application/reactflow');\n    if (!toolData) return;\n    try {\n      const tool = JSON.parse(toolData);\n      if (!this.canAddNodeOfType(tool.type)) {\n        const limit = this.getNodeLimit(tool.type);\n        if (limit === 1) {\n          const existingNode = this.buildAgentNodes.find(node => node.type === tool.type);\n          if (existingNode) {\n            this.onDeleteNode(existingNode.id);\n          }\n        } else {\n          this.showErrorMessage('Node Limit Reached', `You can only add ${limit} ${tool.type} node(s) for ${this.currentAgentType} agents.`);\n          return;\n        }\n      }\n      console.log('🚀 onCanvasDropped - Starting node creation:', {\n        toolType: tool.type,\n        toolName: tool.name,\n        dropPosition: event.position,\n        currentNodesCount: this.buildAgentNodes.length\n      });\n      const autoPosition = this.calculateAutoPosition(tool.type);\n      console.log('🚀 Auto position calculated:', autoPosition);\n      let nodeName = tool.name;\n      if (tool.type === 'prompt') {\n        if (this.currentAgentType === 'collaborative') {\n          nodeName = tool.goal || tool.name;\n        } else {\n          nodeName = tool.prompt || tool.name;\n        }\n      }\n      const newNodeId = this.generateNodeId();\n      const buildAgentNode = {\n        id: newNodeId,\n        name: nodeName,\n        icon: tool.type === 'prompt' ? tool.icon : undefined,\n        type: tool.type,\n        position: autoPosition,\n        originalToolData: tool\n      };\n      console.log('🚀 Created buildAgentNode:', {\n        id: buildAgentNode.id,\n        type: buildAgentNode.type,\n        position: buildAgentNode.position\n      });\n      const nodeWidth = this.isExecuteMode ? 55 : 90;\n      const newCanvasNode = {\n        id: buildAgentNode.id,\n        type: 'build-agent',\n        data: {\n          ...buildAgentNode,\n          width: nodeWidth\n        },\n        position: autoPosition\n      };\n      console.log('🚀 Created newCanvasNode:', {\n        id: newCanvasNode.id,\n        position: newCanvasNode.position\n      });\n      this.buildAgentNodes = [...this.buildAgentNodes, buildAgentNode];\n      this.canvasNodes = [...this.canvasNodes, newCanvasNode];\n      console.log('🚀 Arrays updated:', {\n        buildAgentNodesLength: this.buildAgentNodes.length,\n        canvasNodesLength: this.canvasNodes.length,\n        lastBuildAgentNode: this.buildAgentNodes[this.buildAgentNodes.length - 1],\n        lastCanvasNode: this.canvasNodes[this.canvasNodes.length - 1]\n      });\n      // CRITICAL: Check if positions match\n      const lastBuildAgent = this.buildAgentNodes[this.buildAgentNodes.length - 1];\n      const lastCanvas = this.canvasNodes[this.canvasNodes.length - 1];\n      console.log('🔍 Position verification:', {\n        buildAgentPosition: lastBuildAgent?.position,\n        canvasNodePosition: lastCanvas?.position,\n        canvasNodeDataPosition: lastCanvas?.data?.position,\n        positionsMatch: JSON.stringify(lastBuildAgent?.position) === JSON.stringify(lastCanvas?.position)\n      });\n      // Track this as the latest node for next positioning\n      this.latestNodeId = buildAgentNode.id;\n      console.log('🚀 Latest node ID set to:', this.latestNodeId);\n      // Simple connections and cleanup\n      this.createAgentFlowConnections(this.buildAgentNodes);\n      this.removeItemFromList(tool);\n      this.updateFilteredTools();\n      this.cdr.detectChanges();\n      console.log('🚀 Setting up force positioning timeout for:', buildAgentNode.id);\n      // FORCE correct positioning after DOM update AND fix connections\n      setTimeout(() => {\n        console.log('🚀 Force positioning timeout triggered for:', buildAgentNode.id, autoPosition);\n        this.forceNodeToPosition(buildAgentNode.id, autoPosition);\n        // Update connections after position is forced\n        setTimeout(() => {\n          console.log('🚀 Updating connections after position force');\n          this.createAgentFlowConnections(this.buildAgentNodes);\n          if (this.canvasBoardComponent) {\n            // Force multiple connection updates for reliability\n            this.canvasBoardComponent.updateNodeConnectionPoints();\n            setTimeout(() => this.canvasBoardComponent.updateNodeConnectionPoints(), 50);\n            setTimeout(() => this.canvasBoardComponent.updateNodeConnectionPoints(), 100);\n          }\n          this.cdr.detectChanges();\n        }, 100);\n      }, 100);\n      console.log('🚀 onCanvasDropped completed successfully');\n      this.handleAutoTabSwitch(tool.type);\n    } catch (error) {\n      console.error('Error adding node:', error);\n    }\n  }\n  onNodeSelected(nodeId) {\n    this.selectedNodeId = nodeId;\n    // Single click only selects the node, no preview\n  }\n  onNodeDoubleClicked(nodeId) {\n    const selectedNode = this.buildAgentNodes.find(node => node.id === nodeId);\n    if (selectedNode) {\n      const toolItem = this.findToolItemForNode(selectedNode);\n      if (toolItem) {\n        this.onItemPreview(toolItem);\n      } else {\n        let actualToolItem = null;\n        if (this.allToolItems[selectedNode.type + 's']) {\n          actualToolItem = this.allToolItems[selectedNode.type + 's'].find(item => item.name === selectedNode.name) || null;\n        }\n        if (actualToolItem) {\n          this.onItemPreview(actualToolItem);\n        } else {\n          const basicToolItem = {\n            id: selectedNode.id,\n            name: selectedNode.name,\n            type: selectedNode.type,\n            description: `${selectedNode.type} component: ${selectedNode.name}`,\n            icon: selectedNode.icon || this.getIconForType(selectedNode.type)\n          };\n          this.onItemPreview(basicToolItem);\n        }\n      }\n    }\n  }\n  findToolItemForNode(node) {\n    const tabType = node.type === 'guardrail' ? 'guardrails' : node.type + 's';\n    return this.allToolItems[tabType]?.find(item => item.name === node.name) || null;\n  }\n  onNodeMoved(event) {\n    console.log('🎪 Node moved event received:', {\n      nodeId: event.nodeId,\n      newPosition: event.position,\n      currentBuildAgentPosition: this.buildAgentNodes.find(n => n.id === event.nodeId)?.position,\n      currentCanvasPosition: this.canvasNodes.find(n => n.id === event.nodeId)?.position\n    });\n    const buildAgentNodeIndex = this.buildAgentNodes.findIndex(node => node.id === event.nodeId);\n    if (buildAgentNodeIndex !== -1) {\n      this.buildAgentNodes = [...this.buildAgentNodes.slice(0, buildAgentNodeIndex), {\n        ...this.buildAgentNodes[buildAgentNodeIndex],\n        position: event.position\n      }, ...this.buildAgentNodes.slice(buildAgentNodeIndex + 1)];\n    }\n    const canvasNodeIndex = this.canvasNodes.findIndex(node => node.id === event.nodeId);\n    if (canvasNodeIndex !== -1) {\n      this.canvasNodes = [...this.canvasNodes.slice(0, canvasNodeIndex), {\n        ...this.canvasNodes[canvasNodeIndex],\n        position: event.position\n      }, ...this.canvasNodes.slice(canvasNodeIndex + 1)];\n    }\n    // Track this as the latest moved node for next positioning\n    // In edit mode, once user starts dragging nodes, subsequent new nodes will follow normal create mode positioning\n    this.latestNodeId = event.nodeId;\n    // Ensure position updates worked correctly\n    const updatedBuildNode = this.buildAgentNodes.find(n => n.id === event.nodeId);\n    if (updatedBuildNode && (updatedBuildNode.position.x !== event.position.x || updatedBuildNode.position.y !== event.position.y)) {\n      console.warn('⚠️ Position update failed! Forcing correct position:', {\n        expected: event.position,\n        actual: updatedBuildNode.position,\n        nodeId: event.nodeId\n      });\n      // Force correct position\n      updatedBuildNode.position = {\n        ...event.position\n      };\n    }\n    console.log('🎪 After node moved update:', {\n      nodeId: event.nodeId,\n      newPosition: event.position,\n      newBuildAgentPosition: this.buildAgentNodes.find(n => n.id === event.nodeId)?.position,\n      newCanvasPosition: this.canvasNodes.find(n => n.id === event.nodeId)?.position,\n      latestNodeId: this.latestNodeId,\n      positionsMatch: JSON.stringify(this.buildAgentNodes.find(n => n.id === event.nodeId)?.position) === JSON.stringify(event.position)\n    });\n    // CRITICAL: Force connection updates after node move\n    console.log('🔗 Setting up connection update timeout...');\n    setTimeout(() => {\n      console.log('🔗 Connection update timeout triggered');\n      // Removed forceCorrectPositions to prevent conflicts with user drag\n      console.log('🔗 Skipping position forcing to preserve user drag...');\n      // Recreate connections to ensure they follow moved nodes\n      console.log('🔗 Recreating connections...');\n      this.createAgentFlowConnections(this.buildAgentNodes);\n      if (this.canvasBoardComponent) {\n        console.log('🔗 Updating node connection points...');\n        this.canvasBoardComponent.updateNodeConnectionPoints();\n        // CRITICAL: Update edge paths after recreating connections\n        console.log('🔗 Updating edge paths after connection recreation...');\n        this.canvasBoardComponent.updateEdgePaths();\n        // Force immediate re-render of connections\n        this.cdr.detectChanges();\n        console.log('🔗 Connection update completed');\n      } else {\n        console.warn('⚠️ Canvas board component not available for connection update');\n      }\n    }, 0);\n    this.cdr.detectChanges();\n  }\n  onDeleteNode(nodeId) {\n    const nodeToDelete = this.buildAgentNodes.find(node => node.id === nodeId);\n    if (nodeToDelete) {\n      // Clean up guardrail toggle state if it's a guardrail\n      if (nodeToDelete.type === 'guardrail') {\n        delete this.guardrailToggleStates[nodeId];\n        console.log('🛡️ Cleaned up guardrail toggle state for:', nodeId);\n      }\n      this.addItemBackToList(nodeToDelete);\n    }\n    this.buildAgentNodes = this.buildAgentNodes.filter(node => node.id !== nodeId);\n    this.canvasNodes = this.canvasNodes.filter(node => node.id !== nodeId);\n    this.canvasEdges = this.canvasEdges.filter(edge => edge.source !== nodeId && edge.target !== nodeId);\n    this.recalculateNodePositionsAfterDeletion();\n    this.createAgentFlowConnections(this.buildAgentNodes);\n    setTimeout(() => {\n      if (this.canvasBoardComponent) {\n        this.canvasBoardComponent.updateNodeConnectionPoints();\n      }\n      this.updateFilteredTools();\n      this.cdr.detectChanges();\n    }, 100);\n  }\n  recalculateNodePositionsAfterDeletion() {\n    // For simple vertical stacking, we don't need to recalculate positions\n    // Just update the canvas nodes with current positions\n    this.canvasNodes = this.buildAgentNodes.map(node => ({\n      id: node.id,\n      type: 'build-agent',\n      data: {\n        ...node,\n        width: this.isExecuteMode ? 55 : 90\n      },\n      position: node.position\n    }));\n    // Update latest node ID to the last remaining node (if any)\n    if (this.buildAgentNodes.length > 0) {\n      this.latestNodeId = this.buildAgentNodes[this.buildAgentNodes.length - 1].id;\n    } else {\n      this.latestNodeId = null;\n    }\n  }\n  onConnectionCreated(edge) {\n    const newEdge = {\n      id: edge.id || `edge_${edge.source}_${edge.target}_${Math.floor(Math.random() * 1000)}`,\n      source: edge.source,\n      target: edge.target,\n      animated: edge.animated || true\n    };\n    this.canvasEdges = [...this.canvasEdges, newEdge];\n  }\n  onStartConnection(_event) {\n    // Canvas board handles connection logic\n  }\n  // Removed onNodePositionChanged - using onNodeMoved instead for simplicity\n  onUndo() {\n    /* Implement undo functionality */\n  }\n  onRedo() {\n    /* Implement redo functionality */\n  }\n  onReset() {\n    this.buildAgentNodes = [];\n    this.canvasNodes = [];\n    this.canvasEdges = [];\n    this.currentAgentDetails = null;\n  }\n  onRun() {\n    this.onExecute();\n  }\n  // Drop zone event handlers\n  onDropZoneNodeDropped(event) {\n    console.log('🎯 Node dropped in zone:', {\n      zone: event.zone,\n      nodeType: event.node.type,\n      position: event.position\n    });\n    // Check if we need to replace existing node for single-node zones\n    if ((event.zone === 'prompt' || event.zone === 'model') && this.buildAgentNodes.length > 0) {\n      const existingNodeIndex = this.buildAgentNodes.findIndex(node => node.type === event.zone);\n      if (existingNodeIndex !== -1) {\n        console.log(`🔄 Replacing existing ${event.zone} node`);\n        // Remove the existing node first\n        this.buildAgentNodes.splice(existingNodeIndex, 1);\n        this.canvasNodes = this.canvasNodes.filter(node => node.data?.type !== event.zone);\n      }\n    }\n    // Create new node directly\n    const tool = event.node;\n    // Create new node\n    const buildAgentNode = {\n      id: crypto.randomUUID(),\n      type: tool.type,\n      name: tool.name,\n      icon: tool.icon,\n      position: {\n        x: 0,\n        y: 0\n      },\n      // Position will be managed by drop zone\n      originalToolData: tool // Store all the original tool data\n    };\n    // Add to buildAgentNodes\n    this.buildAgentNodes.push(buildAgentNode);\n    // Create canvas node\n    const canvasNode = {\n      id: buildAgentNode.id,\n      type: 'build-agent',\n      data: {\n        ...buildAgentNode,\n        width: 90\n      },\n      position: buildAgentNode.position\n    };\n    // Add to canvas nodes\n    this.canvasNodes.push(canvasNode);\n    // Initialize guardrail toggle state if it's a guardrail\n    if (tool.type === 'guardrail') {\n      this.guardrailToggleStates[buildAgentNode.id] = true; // Default to enabled\n      console.log('🛡️ Initialized guardrail toggle state:', buildAgentNode.id, 'enabled');\n    }\n    // Remove from available tools\n    this.removeItemFromList(tool);\n    this.updateFilteredTools();\n    // Trigger change detection\n    this.cdr.detectChanges();\n    console.log('✅ Node added to drop zone:', {\n      nodeId: buildAgentNode.id,\n      zone: event.zone,\n      totalNodes: this.buildAgentNodes.length\n    });\n  }\n  onDropZoneNodeDeleted(nodeId) {\n    this.onDeleteNode(nodeId);\n  }\n  // Handle guardrail toggle events from drop-zone-canvas\n  onGuardrailToggled(event) {\n    console.log('🛡️ Guardrail toggle event received:', event);\n    console.log('🛡️ Current toggle states before update:', this.guardrailToggleStates);\n    this.guardrailToggleStates[event.nodeId] = event.enabled;\n    console.log('🛡️ Current toggle states after update:', this.guardrailToggleStates);\n    console.log('🛡️ Guardrail toggle updated:', event.nodeId, event.enabled ? 'enabled' : 'disabled');\n  }\n  // Get metadata from navbar cookies\n  getMetadataFromNavbar() {\n    const orgPath = this.tokenStorage.getCookie('org_path');\n    let organizationPath = '';\n    let levelId = 0; // Initialize to 0 instead of default value\n    let orgId, domainId, projectId, teamId;\n    let org, domain, project, team;\n    if (orgPath) {\n      const parts = orgPath.split('::');\n      const usecasePath = parts[0] || '';\n      const usecaseIdPath = parts[1] || '';\n      organizationPath = usecasePath;\n      const names = usecasePath.split('@');\n      if (names.length >= 4) {\n        [org, domain, project, team] = names;\n      }\n      const ids = usecaseIdPath.split('@').map(Number);\n      if (ids.length >= 4) {\n        [orgId, domainId, projectId, teamId] = ids;\n        levelId = teamId || ids[ids.length - 1] || 0; // Remove default fallback\n      } else if (ids.length > 0 && ids[0]) {\n        levelId = ids[0];\n      }\n    }\n    return {\n      orgPath: organizationPath,\n      levelId,\n      orgId,\n      domainId,\n      projectId,\n      teamId,\n      org,\n      domain,\n      project,\n      team\n    };\n  }\n  getUserSignature() {\n    return this.tokenStorage.getDaUsername() || '<EMAIL>';\n  }\n  buildConfigurationFromLabels() {\n    const configurations = [];\n    const hasModel = this.buildAgentNodes.some(node => node.type === 'model');\n    const hasKnowledge = this.buildAgentNodes.some(node => node.type === 'knowledge');\n    configurations.push({\n      categoryName: 'Model',\n      categoryId: 1,\n      configs: [{\n        configurationName: 'MODEL',\n        configurationValue: hasModel ? this.getModelConfigValue() : ''\n      }, {\n        configurationName: 'MAX_TOKEN',\n        configurationValue: ''\n      }, {\n        configurationName: 'TEMPERATURE',\n        configurationValue: ''\n      }, {\n        configurationName: 'TOP_P',\n        configurationValue: ''\n      }, {\n        configurationName: 'FREQUENCY_PENALTY',\n        configurationValue: ''\n      }, {\n        configurationName: 'PROMPT_PREFIX',\n        configurationValue: ''\n      }, {\n        configurationName: 'PROMPT_WRAPPER',\n        configurationValue: ''\n      }]\n    });\n    configurations.push({\n      categoryName: 'In Context Learning (ICL)',\n      categoryId: 2,\n      configs: [{\n        configurationName: 'RAG',\n        configurationValue: hasKnowledge ? 'true' : ''\n      }, {\n        configurationName: 'RAG_MODE',\n        configurationValue: ''\n      }, {\n        configurationName: 'RAG_KNOWLEDGEBASE_NAME',\n        configurationValue: hasKnowledge ? this.getKnowledgeBaseConfigValue() : ''\n      }, {\n        configurationName: 'RAG_KNOWLEDGEBASE_MAX_RECORD_COUNT',\n        configurationValue: ''\n      }, {\n        configurationName: 'TOKEN_COMPRESSION',\n        configurationValue: ''\n      }]\n    });\n    const availableGuardrails = this.allToolItems['guardrails'] || [];\n    if (availableGuardrails.length > 0) {\n      configurations.push({\n        categoryName: 'Other',\n        categoryId: 3,\n        configs: this.getGuardrailConfigs()\n      });\n    }\n    return configurations;\n  }\n  extractPromptId(nodeId) {\n    const match = nodeId.match(/prompt-(\\d+)/);\n    return match ? match[1] : nodeId;\n  }\n  getModelConfigValue() {\n    const modelNode = this.buildAgentNodes.find(node => node.type === 'model');\n    if (modelNode) {\n      if (modelNode.originalToolData && modelNode.originalToolData.id) {\n        return modelNode.originalToolData.id.toString();\n      }\n      const modelData = this.allToolItems['models'].find(model => model.name === modelNode.name);\n      if (modelData) return modelData.id.toString();\n      const modelDataById = this.allToolItems['models'].find(model => model.id === modelNode.name || model.id.toString() === modelNode.name);\n      if (modelDataById) return modelDataById.id.toString();\n    }\n    return '32';\n  }\n  getKnowledgeBaseConfigValue() {\n    const knowledgeNodes = this.buildAgentNodes.filter(node => node.type === 'knowledge');\n    if (knowledgeNodes.length > 0) {\n      const knowledgeIds = knowledgeNodes.map(node => {\n        if (node.originalToolData && node.originalToolData.id) {\n          return node.originalToolData.id.toString();\n        }\n        const knowledgeData = this.allToolItems['knowledge'].find(kb => kb.name === node.name);\n        return knowledgeData ? knowledgeData.id : null;\n      }).filter(id => id !== null);\n      return knowledgeIds.join(',');\n    }\n    return '';\n  }\n  extractGuardrailId(nodeId) {\n    return nodeId.match(/guardrail-(\\d+)/)?.[1] || nodeId;\n  }\n  getGuardrailConfigs() {\n    const guardrailNodes = this.buildAgentNodes.filter(node => node.type === 'guardrail');\n    const configs = [];\n    // Check if any guardrails are present AND enabled\n    const hasEnabledGuardrails = guardrailNodes.some(node => this.guardrailToggleStates[node.id] !== false);\n    configs.push({\n      configurationName: 'ENABLE_GUARDRAILS',\n      configurationValue: hasEnabledGuardrails ? 'true' : 'false'\n    });\n    const availableGuardrails = this.allToolItems['guardrails'] || [];\n    const enabledGuardrailNames = new Set();\n    // Only add guardrails that are both dropped AND enabled via toggle\n    guardrailNodes.forEach(node => {\n      const isEnabled = this.guardrailToggleStates[node.id] !== false; // Default to true if not set\n      console.log('🛡️ Processing guardrail node:', {\n        nodeId: node.id,\n        nodeName: node.name,\n        toggleState: this.guardrailToggleStates[node.id],\n        isEnabled: isEnabled\n      });\n      if (isEnabled) {\n        const guardrailId = this.extractGuardrailId(node.id);\n        const guardrailData = availableGuardrails.find(gr => gr.id === guardrailId || gr.id === node.id || gr.name === node.name);\n        const configName = guardrailData ? `GUARDRAIL_${guardrailData.name.toUpperCase().replace(/\\s+/g, '_').replace(/[^A-Z0-9_]/g, '')}` : `GUARDRAIL_${node.name.toUpperCase().replace(/\\s+/g, '_').replace(/[^A-Z0-9_]/g, '')}`;\n        enabledGuardrailNames.add(configName);\n        console.log('🛡️ Including enabled guardrail in config:', configName, 'for node:', node.name, 'guardrailData:', guardrailData);\n      } else {\n        console.log('🛡️ Excluding disabled guardrail from config:', node.name);\n      }\n    });\n    // First, add all enabled guardrails (from dropped nodes) to configs\n    enabledGuardrailNames.forEach(configName => {\n      configs.push({\n        configurationName: configName,\n        configurationValue: true\n      });\n      console.log('🛡️ Added enabled guardrail config:', configName, '= true');\n    });\n    // Then, set all other available guardrails to false (if not already added)\n    availableGuardrails.forEach(guardrail => {\n      if (guardrail.name === 'Enable Guardrails') return;\n      const configName = `GUARDRAIL_${guardrail.name.toUpperCase().replace(/\\s+/g, '_').replace(/[^A-Z0-9_]/g, '')}`;\n      // Only add if not already added from enabled guardrails\n      const alreadyAdded = configs.some(config => config.configurationName === configName);\n      if (!alreadyAdded) {\n        configs.push({\n          configurationName: configName,\n          configurationValue: false\n        });\n        console.log('🛡️ Added disabled guardrail config:', configName, '= false');\n      } else {\n        console.log('🛡️ Skipped already added guardrail config:', configName);\n      }\n    });\n    console.log('🛡️ Final guardrail configs:', configs);\n    // Debug: Show specific guardrail configs\n    configs.forEach(config => {\n      if (config.configurationName.startsWith('GUARDRAIL_')) {\n        console.log('🛡️ Config detail:', config.configurationName, '=', config.configurationValue);\n      }\n    });\n    return configs;\n  }\n  validateIndividualAgentData() {\n    const errors = [];\n    const warnings = [];\n    if (!this.agentName || this.agentName.trim() === '') errors.push('Agent name is required');\n    if (!this.agentDetail || this.agentDetail.trim() === '') errors.push('Agent details are required');\n    const promptNode = this.buildAgentNodes.find(node => node.type === 'prompt');\n    if (!promptNode) errors.push('Prompt selection is required');\n    const modelNode = this.buildAgentNodes.find(node => node.type === 'model');\n    if (!modelNode) warnings.push('Model selection is recommended for better performance');\n    const knowledgeNode = this.buildAgentNodes.find(node => node.type === 'knowledge');\n    if (!knowledgeNode) warnings.push('Knowledge base selection is recommended for enhanced responses');\n    const guardrailNode = this.buildAgentNodes.find(node => node.type === 'guardrail');\n    if (!guardrailNode) warnings.push('Guardrail selection is recommended for safe AI interactions');\n    return {\n      isValid: errors.length === 0,\n      errors,\n      warnings\n    };\n  }\n  buildIndividualAgentPayload() {\n    const {\n      orgPath,\n      levelId\n    } = this.getMetadataFromNavbar();\n    const promptNode = this.buildAgentNodes.find(node => node.type === 'prompt');\n    let promptDetails = '';\n    let promptTemplate = '';\n    if (promptNode) {\n      const promptId = this.extractPromptId(promptNode.id);\n      const promptData = this.allToolItems['prompts'].find(prompt => prompt.id === promptId || prompt.id === promptNode.id || prompt.name === promptNode.name);\n      if (promptData) {\n        promptDetails = promptData.description || promptData.name || '';\n        promptTemplate = promptData.template || promptData.content || promptData.promptTemplate || promptData.name || '';\n      } else {\n        promptDetails = promptNode.name;\n        promptTemplate = promptNode.name;\n      }\n    }\n    const useCaseCode = this.agentName.trim().replace(/\\s+/g, '_').toUpperCase();\n    const finalUseCaseDetails = this.agentDetail ? this.agentDetail.trim() : promptDetails;\n    const baseOrgPath = orgPath || 'ADD_NEW@SENIOR_BUSINESS_ANALYST@TEST_GOPAL@TEAM1';\n    const finalOrgPath = `${useCaseCode}@${baseOrgPath}`;\n    return {\n      levelId,\n      code: useCaseCode,\n      name: this.agentName.trim(),\n      useCaseDetails: finalUseCaseDetails,\n      promptTemplate,\n      type: null,\n      webPortalUseCase: null,\n      location: null,\n      sourceLanguage: null,\n      destinationLanguage: null,\n      configuration: this.buildConfigurationFromLabels(),\n      userSignature: this.getUserSignature(),\n      organizationPath: finalOrgPath\n    };\n  }\n  fetchPromptDetailsForSave() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const promptNode = _this.buildAgentNodes.find(node => node.type === 'prompt');\n      if (!promptNode) return;\n      let promptData = _this.allToolItems['prompts']?.find(p => p.name === promptNode.name);\n      if (!promptData && promptNode.originalToolData) {\n        promptData = promptNode.originalToolData;\n      }\n      if (!promptData) {\n        const extractedId = _this.extractPromptId(promptNode.id);\n        if (extractedId && extractedId !== promptNode.id) {\n          try {\n            const apiPromptData = yield new Promise((resolve, reject) => {\n              _this.promptsService.getPromptById(extractedId).subscribe({\n                next: data => resolve(data),\n                error: error => {\n                  console.error('Error fetching prompt from API:', error);\n                  reject(error);\n                }\n              });\n            });\n            if (apiPromptData) {\n              if (!_this.allToolItems['prompts']) _this.allToolItems['prompts'] = [];\n              _this.allToolItems['prompts'].push(apiPromptData);\n            }\n          } catch (error) {\n            console.error('Error fetching prompt details:', error);\n          }\n        }\n      }\n    })();\n  }\n  saveIndividualAgent() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (_this2.currentAgentType !== 'individual') return;\n      const validation = _this2.validateIndividualAgentData();\n      if (!validation.isValid) {\n        _this2.dialogService.warning({\n          title: 'Validation Failed',\n          message: 'Please fix the following errors:\\n' + validation.errors.join('\\n'),\n          showProceedButton: true,\n          proceedButtonText: 'OK'\n        });\n        return;\n      }\n      if (validation.warnings && validation.warnings.length > 0) {\n        _this2.dialogService.warning({\n          title: 'Configuration Recommendations',\n          message: 'The following configurations are recommended:\\n' + validation.warnings.join('\\n') + '\\n\\nDo you want to continue saving without these configurations?',\n          showProceedButton: true,\n          proceedButtonText: 'OK'\n        });\n      }\n      yield _this2.performIndividualAgentSave();\n    })();\n  }\n  saveCollaborativeAgent() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (_this3.currentAgentType !== 'collaborative') return;\n      // Validate level path is available from header\n      const {\n        levelId\n      } = _this3.getMetadataFromNavbar();\n      if (!levelId || levelId === 0) {\n        _this3.dialogService.warning({\n          title: 'Organization Path Required',\n          message: 'Please select a valid organization path from the header navigation before creating a collaborative agent.',\n          showProceedButton: true,\n          proceedButtonText: 'OK'\n        });\n        return;\n      }\n      const validation = _this3.validateCollaborativeAgentData();\n      if (!validation.isValid) {\n        _this3.dialogService.warning({\n          title: 'Validation Failed',\n          message: 'Please fix the following errors:\\n' + validation.errors.join('\\n'),\n          showProceedButton: true,\n          proceedButtonText: 'OK'\n        });\n        return;\n      }\n      if (validation.warnings && validation.warnings.length > 0) {\n        _this3.dialogService.warning({\n          title: 'Configuration Recommendations',\n          message: 'The following configurations are recommended:\\n' + validation.warnings.join('\\n') + '\\n\\nDo you want to continue saving without these configurations?',\n          showProceedButton: true,\n          proceedButtonText: 'OK'\n        });\n        return;\n      }\n      yield _this3.performCollaborativeAgentSave();\n    })();\n  }\n  performCollaborativeAgentSave() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      yield _this4.fetchPromptDetailsForSave();\n      const payload = _this4.buildCollaborativeAgentPayloadV2();\n      _this4.saveAgent(() => _this4.agentService.createCollaborativeAgentV2(payload), payload.name, true);\n    })();\n  }\n  performIndividualAgentSave() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      const payload = _this5.buildIndividualAgentPayload();\n      _this5.saveAgent(() => _this5.agentService.individualAgentSave(payload), payload.name, false);\n    })();\n  }\n  saveAgent(saveFn, agentName, isCollaborativeType) {\n    saveFn().subscribe({\n      next: response => {\n        const defaultSuccess = isCollaborativeType ? `Collaborative agent \"${agentName || 'Agent'}\" has been saved and sent for approval successfully!` : `Individual agent \"${agentName || 'Agent'}\" has been saved successfully!`;\n        const successMessage = response?.message || defaultSuccess;\n        // Store the agent ID for potential navigation from success popup\n        this.currentAgentId = response.useCaseId || response.agentId;\n        this.dialogService.confirmation({\n          title: 'Success',\n          message: successMessage,\n          confirmButtonText: 'OK'\n        });\n      },\n      error: error => {\n        const errorMessage = error?.error?.message || error?.message || `Error saving ${isCollaborativeType ? 'collaborative' : 'individual'} agent. Please try again.`;\n        this.dialogService.error({\n          title: 'Error',\n          message: errorMessage,\n          showRetryButton: true,\n          retryButtonText: 'Ok'\n        });\n      }\n    });\n  }\n  validateCollaborativeAgentData() {\n    const errors = [];\n    const warnings = [];\n    if (!this.agentName || this.agentName.trim().length === 0) errors.push('Agent name is required');\n    if (!this.agentDetail || this.agentDetail.trim().length === 0) errors.push('Agent details are required');\n    const promptNodes = this.buildAgentNodes.filter(node => node.type === 'prompt');\n    if (promptNodes.length === 0) errors.push('Prompt selection is required');\n    const modelNodes = this.buildAgentNodes.filter(node => node.type === 'model');\n    if (modelNodes.length === 0) errors.push('Model selection is required');\n    const knowledgeNodes = this.buildAgentNodes.filter(node => node.type === 'knowledge');\n    if (knowledgeNodes.length === 0) warnings.push('Knowledge base selection is recommended for enhanced responses');\n    const toolNodes = this.buildAgentNodes.filter(node => node.type === 'tool');\n    if (toolNodes.length === 0) warnings.push('Tool selection is recommended for collaborative agent capabilities');\n    return {\n      isValid: errors.length === 0,\n      errors,\n      warnings\n    };\n  }\n  buildCollaborativeAgentPayloadV2() {\n    const {\n      levelId\n    } = this.getMetadataFromNavbar();\n    const modelNodes = this.buildAgentNodes.filter(node => node.type === 'model');\n    const modelRefs = modelNodes.map(node => {\n      const modelData = this.allToolItems['models'].find(m => m.name === node.name);\n      return modelData?.id || 40;\n    });\n    const uniqueModelRefs = [...new Set(modelRefs)];\n    const knowledgeNodes = this.buildAgentNodes.filter(node => node.type === 'knowledge');\n    const knowledgeIds = knowledgeNodes.map(node => {\n      if (node.originalToolData && node.originalToolData.id) {\n        return node.originalToolData.id;\n      }\n      const knowledgeData = this.allToolItems['knowledge'].find(k => k.name === node.name);\n      return knowledgeData?.id;\n    }).filter(id => id);\n    const uniqueKnowledgeIds = [...new Set(knowledgeIds)];\n    const promptNode = this.buildAgentNodes.find(node => node.type === 'prompt');\n    let promptData = null;\n    if (promptNode) {\n      if (promptNode.originalToolData) {\n        promptData = promptNode.originalToolData;\n      } else {\n        promptData = this.allToolItems['prompts'].find(p => p.name === promptNode.name);\n        if (!promptData) {\n          const extractedId = this.extractPromptId(promptNode.id);\n          promptData = this.allToolItems['prompts'].find(p => String(p.id) === extractedId || String(p.id) === String(parseInt(extractedId)));\n        }\n      }\n    }\n    const originalPromptData = promptData?.originalPromptData || promptData;\n    const sourceData = originalPromptData || promptData;\n    const role = sourceData?.role || 'Python Developer';\n    const goal = sourceData?.goal || this.agentDetail || 'Analyze data and provide insights using Python';\n    const backstory = sourceData?.backstory || this.agentDetail || 'You are an experienced data analyst with strong Python skills.';\n    const description = sourceData?.description || sourceData?.descriptionConsolidated || this.agentDetail || '%1$s';\n    const expectedOutput = sourceData?.expectedOutput || sourceData?.expectedOutputConsolidated || 'Output is :';\n    const toolNodes = this.buildAgentNodes.filter(node => node.type === 'tool');\n    const builtInTools = [];\n    const userTools = [];\n    console.log('=== DEEP TOOL ANALYSIS ===');\n    console.log('Total buildAgentNodes:', this.buildAgentNodes.length);\n    console.log('Tool nodes found:', toolNodes.length);\n    console.log('Tool nodes details:', toolNodes.map((node, index) => ({\n      index,\n      id: node.id,\n      name: node.name,\n      type: node.type,\n      hasOriginalToolData: !!node.originalToolData,\n      originalToolDataId: node.originalToolData?.id,\n      originalToolDataName: node.originalToolData?.name\n    })));\n    toolNodes.forEach((node, index) => {\n      console.log(`\\n--- Processing tool node ${index + 1}/${toolNodes.length} ---`);\n      console.log('Node details:', {\n        id: node.id,\n        name: node.name,\n        originalToolData: node.originalToolData\n      });\n      let toolData = this.allToolItems['tools'].find(t => t.name === node.name);\n      if (!toolData && node.originalToolData) {\n        toolData = node.originalToolData;\n        console.log('Using originalToolData for node:', node.name, toolData);\n      }\n      if (!toolData && node.originalToolData?.id) {\n        toolData = this.allToolItems['tools'].find(t => t.id === node.originalToolData.id);\n        console.log('Found tool by ID:', toolData);\n      }\n      if (node.originalToolData && node.originalToolData.id) {\n        console.log('Preferring originalToolData over lookup result');\n        toolData = node.originalToolData;\n      }\n      if (!toolData) {\n        console.warn(`Tool data not found for node: ${node.name}`, {\n          nodeName: node.name,\n          nodeOriginalData: node.originalToolData,\n          availableTools: this.allToolItems['tools'].map(t => ({\n            id: t.id,\n            name: t.name\n          }))\n        });\n        return;\n      }\n      console.log('Found tool data for node:', node.name, toolData);\n      let numericToolId;\n      const toolId = toolData.id;\n      // Extract numeric ID from prefixed string IDs\n      if (typeof toolId === 'string') {\n        if (toolId.startsWith('builtin-')) {\n          // Extract numeric ID from \"builtin-123\" format\n          const numericPart = toolId.replace('builtin-', '');\n          numericToolId = parseInt(numericPart);\n        } else if (toolId.startsWith('user-')) {\n          // Extract numeric ID from \"user-456\" format\n          const numericPart = toolId.replace('user-', '');\n          numericToolId = parseInt(numericPart);\n        } else {\n          // Try to extract any numeric part from the string\n          const numericMatch = toolId.match(/\\d+/);\n          numericToolId = numericMatch ? parseInt(numericMatch[0]) : 0;\n        }\n      } else if (typeof toolId === 'number') {\n        numericToolId = toolId;\n      } else {\n        return;\n      }\n      const isUserTool = typeof toolData.id === 'string' && toolData.id.startsWith('user-');\n      if (isUserTool) {\n        userTools.push(numericToolId);\n      } else {\n        builtInTools.push(numericToolId);\n      }\n    });\n    const agentConfigs = {\n      temperature: 0.3,\n      topP: 0.95,\n      maxToken: '4000',\n      modelRef: uniqueModelRefs,\n      knowledgeBaseRef: uniqueKnowledgeIds,\n      maxIter: null,\n      maxRpm: 5,\n      maxExecutionTime: 5,\n      allowDelegation: true,\n      allowCodeExecution: true,\n      isSafeCodeExecution: true,\n      toolRef: builtInTools,\n      userToolRef: userTools\n    };\n    const userSignature = this.getUserSignature();\n    // Ensure levelId is properly retrieved from header without default fallback\n    if (!levelId || levelId === 0) {\n      throw new Error('Organization path is required from header navigation. Please select a valid organization path before creating a collaborative agent.');\n    }\n    return {\n      agentDetails: this.agentDetail.trim(),\n      name: this.agentName.trim(),\n      role,\n      goal,\n      backstory,\n      description,\n      expectedOutput,\n      agentConfigs,\n      createdBy: userSignature,\n      levelId: levelId,\n      modifiedBy: userSignature\n    };\n  }\n  buildCollaborativeAgentChangeRequestPayload() {\n    const basePayload = this.buildCollaborativeAgentPayloadV2();\n    return {\n      id: parseInt(this.currentAgentId),\n      ...basePayload,\n      isDeleted: false\n    };\n  }\n  buildIndividualAgentUpdatePayload() {\n    const basePayload = this.buildIndividualAgentPayload();\n    return {\n      ...basePayload,\n      useCaseId: parseInt(this.currentAgentId),\n      configuration: basePayload.configuration.map(category => ({\n        ...category,\n        configs: category.configs.map(config => ({\n          ...config,\n          configId: this.agentConfigIds.get(`${category.categoryId}-${config.configurationName}`) || undefined\n        }))\n      }))\n    };\n  }\n  updateIndividualAgent() {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      return new Promise((resolve, reject) => {\n        try {\n          const payload = _this6.buildIndividualAgentUpdatePayload();\n          _this6.agentService.individualAgentEdit(payload).subscribe({\n            next: response => {\n              const successMessage = response?.message || `Individual agent \"${payload.name || 'Agent'}\" has been updated successfully!`;\n              _this6.dialogService.confirmation({\n                title: 'Success',\n                message: successMessage,\n                confirmButtonText: 'OK'\n              });\n              // Update the current agent details with the new data\n              _this6.currentAgentDetails = {\n                ..._this6.currentAgentDetails,\n                ...payload,\n                code: payload.code,\n                useCaseCode: payload.code,\n                name: payload.name,\n                useCaseDetails: payload.useCaseDetails\n              };\n              // Update the agentCode with the new code\n              _this6.agentCode = payload.code;\n              // Store the agent ID for potential navigation from success popup\n              _this6.currentAgentId = _this6.currentAgentId || payload.id;\n              // Refresh playground with updated data\n              _this6.loadAgentDetailsForPlayground();\n              resolve();\n            },\n            error: error => {\n              console.error('Error updating individual agent:', error);\n              const errorMessage = error?.error?.message || error?.message || 'Error updating individual agent. Please try again.';\n              _this6.dialogService.error({\n                title: 'Error',\n                message: errorMessage,\n                showRetryButton: true,\n                retryButtonText: 'Ok'\n              });\n              reject(error);\n            }\n          });\n        } catch (error) {\n          console.error('Error in updateIndividualAgent:', error);\n          reject(error);\n        }\n      });\n    })();\n  }\n  submitCollaborativeAgentChangeRequest() {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      if (_this7.currentAgentType !== 'collaborative' || !_this7.currentAgentId) return;\n      // Validate level path is available from header\n      const {\n        levelId\n      } = _this7.getMetadataFromNavbar();\n      if (!levelId || levelId === 0) {\n        _this7.dialogService.confirmation({\n          title: 'Error',\n          message: 'Please select a valid organization path from the header navigation before updating a collaborative agent.',\n          confirmButtonText: 'OK'\n        });\n        return;\n      }\n      const validation = _this7.validateCollaborativeAgentData();\n      if (!validation.isValid) {\n        console.error('Validation failed:', validation.errors);\n        _this7.showErrorMessage('Validation Failed', 'Please fix the following errors:\\n' + validation.errors.join('\\n'));\n        return;\n      }\n      try {\n        const payload = _this7.buildCollaborativeAgentChangeRequestPayload();\n        _this7.agentService.submitCollaborativeAgentChangeRequest(payload).subscribe({\n          next: response => {\n            const successMessage = response?.message || `Change request for agent \"${payload.name || 'Agent'}\" has been submitted successfully!`;\n            // Store the agent ID for potential navigation from success popup\n            _this7.currentAgentId = _this7.currentAgentId || payload.id;\n            _this7.dialogService.confirmation({\n              title: 'Success',\n              message: successMessage,\n              confirmButtonText: 'OK'\n            });\n            _this7.loadAgentDetailsForPlayground();\n          },\n          error: error => {\n            console.error('Error submitting collaborative agent change request:', error);\n            const errorMessage = error?.error?.message || error?.message || 'Failed to submit change request. Please try again.';\n            _this7.dialogService.error({\n              title: 'Error',\n              message: errorMessage,\n              showRetryButton: true,\n              retryButtonText: 'Ok'\n            });\n          }\n        });\n      } catch (error) {\n        console.error('Error preparing change request:', error);\n        const errorMessage = error?.message || 'Failed to prepare change request. Please check the console for details.';\n        _this7.dialogService.warning({\n          title: 'Warning',\n          message: errorMessage,\n          showProceedButton: true,\n          proceedButtonText: 'Ok'\n        });\n      }\n    })();\n  }\n  onApprovalRequested() {\n    // This method is kept for compatibility but no longer used for collaborative agents\n  }\n  onPrimaryButtonClick() {\n    var _this8 = this;\n    return _asyncToGenerator(function* () {\n      console.log('🚀 PRIMARY BUTTON CLICKED:', {\n        isExecuteMode: _this8.isExecuteMode,\n        isViewMode: _this8.isViewMode,\n        isEditMode: _this8.isEditMode,\n        primaryButtonText: _this8.primaryButtonText\n      });\n      if (_this8.isExecuteMode) return;\n      if (_this8.isViewMode) {\n        console.log('🚀 View mode - calling onExecute...');\n        _this8.onExecute();\n        return;\n      }\n      // Check if button text is \"Execute\" - call onExecute regardless of mode\n      if (_this8.primaryButtonText === 'Execute') {\n        console.log('🚀 Execute button clicked - calling onExecute...');\n        _this8.onExecute();\n        return;\n      }\n      if (_this8.isEditMode) {\n        if (_this8.currentAgentType === 'individual') {\n          yield _this8.updateIndividualAgent();\n        } else if (_this8.currentAgentType === 'collaborative') {\n          yield _this8.submitCollaborativeAgentChangeRequest();\n        }\n        return;\n      }\n      const validation = _this8.currentAgentType === 'individual' ? _this8.validateIndividualAgentData() : _this8.validateCollaborativeAgentData();\n      if (!validation.isValid) {\n        _this8.showErrorMessage('Validation Failed', 'Please fix the following errors before saving:\\n' + validation.errors.join('\\n'));\n        return;\n      }\n      if (_this8.currentAgentType === 'individual') {\n        yield _this8.saveIndividualAgent();\n      } else if (_this8.currentAgentType === 'collaborative') {\n        yield _this8.saveCollaborativeAgent();\n      }\n    })();\n  }\n  onExecute() {\n    this.isExecuteMode = true;\n    this.showChatInterface = true;\n    // Recalculate all node positions for execute mode layout\n    this.repositionNodesForExecuteMode();\n    // Multiple force updates to ensure positioning sticks\n    setTimeout(() => {\n      this.cdr.detectChanges();\n      this.forceExecutePositions();\n      if (this.canvasBoardComponent) {\n        this.canvasBoardComponent.updateNodeConnectionPoints();\n      }\n    }, 100);\n    // Additional safety update\n    setTimeout(() => {\n      this.forceExecutePositions();\n    }, 300);\n    this.loadAgentDetailsForPlayground();\n    if (this.agentName) {\n      setTimeout(() => {\n        this.autoSelectCurrentAgent();\n      }, 1500);\n    }\n    this.chatMessages = [{\n      from: 'ai',\n      text: `Hi there! I am ${this.agentName || 'your build agent'}. How can I help you today?`\n    }];\n    setTimeout(() => {\n      const agentId = 'build-agent-' + Date.now();\n      this.toolExecutionService.startExecution(agentId, this.chatMessages);\n      this.executionSubscription = this.toolExecutionService.getExecutionState().subscribe(state => {\n        if (state.isExecuting && state.toolId === agentId) {\n          this.chatMessages = state.chatMessages;\n        }\n      });\n    }, 100);\n  }\n  handleChatMessage(message) {\n    if (this.currentAgentType === 'individual') {\n      if (!this.selectedPrompt || this.selectedPrompt === 'default') {\n        this.showAgentError('Please select an agent from the dropdown before testing.');\n        return;\n      }\n      let displayMessage = message;\n      if (this.agentFilesUploadedData.length > 0) {\n        const fileNames = this.agentFilesUploadedData.map(file => file.documentName).join(', ');\n        displayMessage = `${message}\\n\\n📎 Attached files: ${fileNames}`;\n      }\n      this.chatMessages = [...this.chatMessages, {\n        from: 'user',\n        text: displayMessage\n      }];\n      this.isProcessingChat = true;\n      const isConversational = this.agentPlaygroundForm.get('isConversational')?.value || false;\n      const isUseTemplate = this.agentPlaygroundForm.get('isUseTemplate')?.value || false;\n      const agentMode = this.agentCode || this.selectedAgentMode || this.selectedPrompt;\n      let useCaseIdentifier = this.selectedUseCaseIdentifier;\n      if (!useCaseIdentifier) {\n        const orgPath = this.buildOrganizationPath();\n        useCaseIdentifier = `${agentMode}${orgPath}`;\n      }\n      if (this.agentFilesUploadedData.length > 0) {\n        this.processAgentFilesAndSendMessage(message, agentMode, useCaseIdentifier, isConversational, isUseTemplate);\n        return;\n      }\n      this.sendAgentMessageToAPI(message, agentMode, useCaseIdentifier, isConversational, isUseTemplate);\n    } else if (this.currentAgentType === 'collaborative') {\n      this.isProcessingChat = true;\n      let payload = {\n        executionId: this.executionId,\n        agentId: Number(this.currentAgentId),\n        user: this.tokenStorage.getDaUsername() || '<EMAIL>',\n        userInputs: {\n          question: message\n        }\n      };\n      if (this.agentFilesUploadedData.length > 0) {\n        const fileWrapper = this.agentFilesUploadedData[0];\n        let displayMessage;\n        if (this.agentFilesUploadedData.length > 0) {\n          const fileNames = this.agentFilesUploadedData.map(file => file.documentName).join(', ');\n          displayMessage = `📎 Attached files: ${fileNames}`;\n          this.chatMessages = [{\n            from: 'user',\n            text: displayMessage\n          }];\n        }\n        this.agentPlaygroundService.submitAgentExecuteWithFile(payload, fileWrapper).pipe(finalize(() => {\n          this.isProcessingChat = false;\n          this.isAgentPlaygroundLoading = false;\n        }), takeUntil(this.agentPlaygroundDestroy)).subscribe({\n          next: res => this.handleAgentExecuteResponse(res, message),\n          error: err => {\n            this.chatMessages = [...this.chatMessages, {\n              from: 'user',\n              text: message\n            }, {\n              from: 'ai',\n              text: err?.error?.message || err?.message || 'Something went wrong.'\n            }];\n          }\n        });\n      } else {\n        this.agentPlaygroundService.submitAgentExecute(payload).pipe(finalize(() => {\n          this.isProcessingChat = false;\n          this.isAgentPlaygroundLoading = false;\n        }), takeUntil(this.agentPlaygroundDestroy)).subscribe({\n          next: res => this.handleAgentExecuteResponse(res, message),\n          error: err => {\n            this.chatMessages = [...this.chatMessages, {\n              from: 'user',\n              text: message\n            }, {\n              from: 'ai',\n              text: err?.error?.message || err?.message || 'Something went wrong.'\n            }];\n          }\n        });\n      }\n    }\n  }\n  handleAgentExecuteResponse(response, message) {\n    try {\n      const outputRaw = response?.agentResponse?.agent?.output;\n      let formattedOutput = '';\n      if (outputRaw) {\n        // Directly replace escaped \\n with real newlines\n        formattedOutput = outputRaw.replace(/\\\\n/g, '\\n');\n      } else {\n        formattedOutput = response?.agentResponse?.detail;\n      }\n      this.chatMessages = [...this.chatMessages, {\n        from: 'user',\n        text: message\n      }, {\n        from: 'ai',\n        text: formattedOutput || 'No response from agent.'\n      }];\n    } catch (err) {\n      this.chatMessages = [...this.chatMessages, {\n        from: 'ai',\n        text: err?.message || 'Agent response could not be processed.'\n      }];\n    }\n  }\n  processAgentFilesAndSendMessage(message, mode, useCaseIdentifier, isConversational, isUseTemplate) {\n    const formData = new FormData();\n    this.agentFilesUploadedData.forEach(fileData => {\n      if (fileData.file) {\n        formData.append('files', fileData.file);\n      }\n    });\n    if (formData.has('files')) {\n      this.agentPlaygroundService.getFileToContent(formData).pipe(switchMap(fileResponse => {\n        const fileContent = fileResponse?.fileResponses?.map(response => response.fileContent)?.join('\\n') || '';\n        this.sendAgentMessageToAPIWithFiles(message, mode, useCaseIdentifier, isConversational, isUseTemplate, fileContent);\n        return of(null);\n      }), catchError(error => {\n        console.error('Error parsing files:', error);\n        this.sendAgentMessageToAPI(message, mode, useCaseIdentifier, isConversational, isUseTemplate);\n        return of(null);\n      })).subscribe();\n    } else {\n      this.sendAgentMessageToAPI(message, mode, useCaseIdentifier, isConversational, isUseTemplate);\n    }\n  }\n  sendAgentMessageToAPI(message, mode, useCaseIdentifier, isConversational, isUseTemplate) {\n    if (isConversational) {\n      this.agentChatPayload.push({\n        content: message,\n        role: 'user'\n      });\n    }\n    const payload = isConversational ? this.agentChatPayload : message;\n    const {\n      levelId\n    } = this.getMetadataFromNavbar();\n    this.agentPlaygroundService.generatePrompt(payload, mode, isConversational, isUseTemplate, this.agentAttachment, useCaseIdentifier, '', levelId).pipe(finalize(() => {\n      this.isProcessingChat = false;\n      this.isAgentPlaygroundLoading = false;\n    }), takeUntil(this.agentPlaygroundDestroy)).subscribe({\n      next: generatedResponse => {\n        if (generatedResponse?.response && generatedResponse?.response?.choices) {\n          const aiResponseText = generatedResponse.response.choices[0].text;\n          this.chatMessages = [...this.chatMessages, {\n            from: 'ai',\n            text: aiResponseText\n          }];\n          if (isConversational) {\n            this.agentChatPayload.push({\n              content: aiResponseText,\n              role: 'assistant'\n            });\n          }\n        } else {\n          console.warn('Unexpected API response format:', generatedResponse);\n          this.showAgentError('Received unexpected response format from API.');\n        }\n      },\n      error: error => {\n        console.error('API Error:', error);\n        const errorMessage = error?.error?.message || 'An error occurred while processing your request.';\n        this.showAgentError(errorMessage);\n        if (isConversational && this.agentChatPayload.length > 0) {\n          this.agentChatPayload.pop();\n        }\n      }\n    });\n  }\n  sendAgentMessageToAPIWithFiles(message, mode, useCaseIdentifier, isConversational, isUseTemplate, fileContents) {\n    if (isConversational) {\n      this.agentChatPayload.push({\n        content: message,\n        role: 'user'\n      });\n    }\n    const payload = isConversational ? this.agentChatPayload : message;\n    const {\n      levelId\n    } = this.getMetadataFromNavbar();\n    this.agentPlaygroundService.generatePrompt(payload, mode, isConversational, isUseTemplate, this.agentAttachment, useCaseIdentifier, fileContents, levelId).pipe(finalize(() => {\n      this.isProcessingChat = false;\n      this.isAgentPlaygroundLoading = false;\n    }), takeUntil(this.agentPlaygroundDestroy)).subscribe({\n      next: generatedResponse => {\n        if (generatedResponse?.response && generatedResponse?.response?.choices) {\n          const aiResponseText = generatedResponse.response.choices[0].text;\n          this.chatMessages = [...this.chatMessages, {\n            from: 'ai',\n            text: aiResponseText\n          }];\n          if (isConversational) {\n            this.agentChatPayload.push({\n              content: aiResponseText,\n              role: 'assistant'\n            });\n          }\n        } else {\n          console.warn('Unexpected API response format:', generatedResponse);\n          this.showAgentError('Received unexpected response format from API.');\n        }\n      },\n      error: error => {\n        console.error('API Error:', error);\n        const errorMessage = error?.error?.message || 'An error occurred while processing your request.';\n        this.showAgentError(errorMessage);\n        if (isConversational && this.agentChatPayload.length > 0) {\n          this.agentChatPayload.pop();\n        }\n      }\n    });\n  }\n  showAgentError(message) {\n    this.chatMessages = [...this.chatMessages, {\n      from: 'ai',\n      text: message\n    }];\n  }\n  onPlaygroundConversationalToggle(event) {\n    this.agentPlaygroundForm.get('isConversational')?.setValue(event);\n  }\n  onPlaygroundTemplateToggle(event) {\n    this.agentPlaygroundForm.get('isUseTemplate')?.setValue(event);\n  }\n  onFilesSelected(files) {\n    this.agentFilesUploadedData = files;\n  }\n  onAgentConversationalToggle(event) {\n    const isConversational = event;\n    this.agentPlaygroundForm.get('isConversational')?.setValue(isConversational);\n    if (isConversational && this.agentPlaygroundForm.get('isUseTemplate')?.value) {\n      this.agentPlaygroundForm.get('isUseTemplate')?.setValue(false);\n    }\n  }\n  onAgentTemplateToggle(event) {\n    const isUseTemplate = event;\n    this.agentPlaygroundForm.get('isUseTemplate')?.setValue(isUseTemplate);\n    if (isUseTemplate && this.agentPlaygroundForm.get('isConversational')?.value) {\n      this.agentPlaygroundForm.get('isConversational')?.setValue(false);\n    }\n  }\n  onAgentFileSelected(event) {\n    const files = event.target.files;\n    if (files && files.length > 0) {\n      this.agentFilesUploadedData = [];\n      for (let i = 0; i < files.length; i++) {\n        const file = files[i];\n        this.agentFilesUploadedData.push({\n          id: `agent_file_${Date.now()}_${i}`,\n          documentName: file.name,\n          isImage: file.type.startsWith('image/'),\n          file: file\n        });\n      }\n    }\n  }\n  removeAgentFile(index) {\n    this.agentFilesUploadedData.splice(index, 1);\n  }\n  clearAgentChatData() {\n    this.chatMessages = [{\n      from: 'ai',\n      text: 'Hi there, how can I help you test your agent today?'\n    }];\n    this.agentChatPayload = [];\n    this.agentFilesUploadedData = [];\n    this.agentAttachment = [];\n  }\n  showResponseModal(message, isError = false) {\n    this.modalMessage = message;\n    this.isModalError = isError;\n    this.isResponseModalOpen = true;\n  }\n  closeResponseModal() {\n    this.isResponseModalOpen = false;\n    this.modalMessage = '';\n    this.isModalError = false;\n  }\n  loadAgentDetailsForPlayground() {\n    if (this.currentAgentDetails) {\n      const agentData = this.currentAgentDetails;\n      this.promptOptions = [{\n        value: agentData.useCaseCode || agentData.code || this.currentAgentId || 'DEFAULT_AGENT',\n        name: agentData.useCaseName || agentData.name || 'Current Agent',\n        agentData: agentData\n      }];\n      this.selectedPrompt = this.promptOptions[0].value.toString();\n      this.selectedAgentMode = agentData.useCaseCode || agentData.code || '';\n      if (agentData.organizationPath) {\n        this.selectedUseCaseIdentifier = agentData.organizationPath;\n      } else {\n        const orgPath = this.buildOrganizationPath();\n        this.selectedUseCaseIdentifier = `${this.selectedAgentMode}${orgPath}`;\n      }\n    }\n    this.promptOptions.unshift({\n      value: 'default',\n      name: 'Choose an Agent'\n    });\n    this.cdr.detectChanges();\n  }\n  autoSelectCurrentAgent() {\n    if (!this.promptOptions?.length || !this.agentName) return;\n    // Find by code/value first, then fallback to name\n    const currentAgentOption = this.promptOptions.find(option => {\n      const agentData = option.agentData;\n      return agentData?.code === this.agentCode || agentData?.useCaseCode === this.agentCode || option.value === this.agentCode || option.name === this.agentName;\n    });\n    if (!currentAgentOption) return;\n    this.selectedPrompt = currentAgentOption.value.toString(); // Use value, not name\n    this.onPromptChanged(currentAgentOption);\n    this.cdr.detectChanges();\n  }\n  onPromptChanged(selectedOption) {\n    const agentData = selectedOption.agentData;\n    const agentCode = agentData?.code || selectedOption.value.toString();\n    this.selectedPrompt = selectedOption.name;\n    this.agentCode = agentCode;\n    this.selectedAgentMode = agentCode;\n    if (agentData?.useCaseIdentifier) {\n      this.selectedUseCaseIdentifier = agentData.useCaseIdentifier;\n    } else {\n      this.selectedUseCaseIdentifier = `${agentCode}${this.buildOrganizationPath()}`;\n    }\n    this.cdr.detectChanges();\n  }\n  // Exit execute mode\n  onExitExecuteMode() {\n    this.isExecuteMode = false;\n    this.showChatInterface = false;\n    // Clear execute nodes and restore original canvas nodes\n    this.executeNodes = [];\n    this.canvasNodes = this.buildAgentNodes.map(node => ({\n      id: node.id,\n      type: 'build-agent',\n      data: {\n        ...node,\n        width: 90\n      },\n      position: node.position\n    }));\n    // Recreate connections based on hierarchy\n    this.createAgentFlowConnections(this.buildAgentNodes);\n    // Removed complex positioning - using simple vertical stacking\n    // Force connection points update after a delay to ensure DOM is updated\n    setTimeout(() => {\n      this.cdr.detectChanges();\n    }, 200);\n    // Clean up execution subscription\n    if (this.executionSubscription) {\n      this.executionSubscription.unsubscribe();\n      this.executionSubscription = undefined;\n    }\n    // Stop execution service\n    this.toolExecutionService.stopExecution();\n  }\n  onCanvasStateChanged(state) {\n    // Only update edges, don't override our controlled node positions\n    this.canvasEdges = state.edges;\n    // Ensure connection points are updated when state changes\n    setTimeout(() => {\n      if (this.canvasBoardComponent) {\n        this.canvasBoardComponent.updateNodeConnectionPoints();\n      }\n    }, 100);\n  }\n  // Handle agent name changes from canvas board\n  onAgentNameChanged(agentName) {\n    this.agentName = agentName;\n  }\n  // Handle metadata changes from canvas board\n  onMetadataChanged(metadata) {\n    this.agentMetadata = metadata;\n  }\n  // Handle agent details changes from canvas board\n  onAgentDetailsChanged(details) {\n    this.agentName = details.name;\n    this.agentDetail = details.useCaseDetails;\n  }\n  // Get active tab label for display\n  getActiveTabLabel() {\n    const activeTabObj = this.tabs.find(tab => tab.id === this.activeTab);\n    return activeTabObj ? activeTabObj.label || 'Item' : 'Item';\n  }\n  // Create new item action (dynamic based on active tab)\n  onCreateNewItem() {\n    // Navigate to appropriate creation page based on active tab\n    switch (this.activeTab) {\n      case 'prompts':\n        this.router.navigate(['/libraries/prompts/create']);\n        break;\n      case 'models':\n        this.router.navigate(['/libraries/models/create']);\n        break;\n      case 'knowledge':\n        this.router.navigate(['/libraries/knowledge-base/create']);\n        break;\n      case 'tools':\n        this.router.navigate(['/libraries/tools/create']);\n        break;\n      case 'guardrails':\n        this.router.navigate(['/libraries/guardrails/create']);\n        break;\n      default:\n        console.warn(`No creation route defined for tab: ${this.activeTab}`);\n    }\n  }\n  // Reposition all nodes for execute mode\n  repositionNodesForExecuteMode() {\n    // Use the original createExecuteNodes approach\n    this.createExecuteNodes();\n    // Force immediate update\n    this.cdr.detectChanges();\n  }\n  // Calculate automatic position - simple vertical stacking below latest node\n  calculateAutoPosition(nodeType) {\n    console.log('🎯 calculateAutoPosition called:', {\n      nodeType,\n      isExecuteMode: this.isExecuteMode,\n      buildAgentNodesLength: this.buildAgentNodes.length,\n      latestNodeId: this.latestNodeId,\n      existingNodePositions: this.buildAgentNodes.map(n => ({\n        id: n.id,\n        pos: n.position\n      }))\n    });\n    if (this.isExecuteMode) {\n      // Execute mode - vertical line of small agent icons (properly centered)\n      const centerX = 200; // Better center position for the canvas\n      const startY = 100; // Start position from top\n      const verticalSpacing = 80; // More spacing for better visual separation\n      // Calculate position based on current number of nodes\n      const nodeIndex = this.buildAgentNodes.length;\n      const executePosition = {\n        x: centerX,\n        y: startY + nodeIndex * verticalSpacing\n      };\n      console.log('🎯 Execute mode position calculated (vertical icons):', executePosition);\n      return executePosition;\n    }\n    // Build mode - Simple vertical positioning below latest node\n    const simplePosition = this.calculateSimpleVerticalPosition();\n    console.log('🎯 Build mode position calculated:', simplePosition);\n    return simplePosition;\n  }\n  // Simple vertical positioning - place new node below the latest node\n  calculateSimpleVerticalPosition() {\n    const defaultStartX = 100; // Default starting X position\n    const defaultStartY = 150; // Default starting Y position\n    const verticalSpacing = 120; // Space between nodes vertically\n    console.log('📍 calculateSimpleVerticalPosition - Constants:', {\n      defaultStartX,\n      defaultStartY,\n      verticalSpacing\n    });\n    // If no nodes exist or no latest node tracked, use default start position\n    if (this.buildAgentNodes.length === 0 || !this.latestNodeId) {\n      console.log('📍 Using default position (no nodes or no latest):', {\n        nodesLength: this.buildAgentNodes.length,\n        latestNodeId: this.latestNodeId,\n        defaultPosition: {\n          x: defaultStartX,\n          y: defaultStartY\n        }\n      });\n      return {\n        x: defaultStartX,\n        y: defaultStartY\n      };\n    }\n    // Find the latest node\n    const latestNode = this.buildAgentNodes.find(node => node.id === this.latestNodeId);\n    console.log('📍 Latest node search:', {\n      searchingForId: this.latestNodeId,\n      foundNode: latestNode,\n      allNodeIds: this.buildAgentNodes.map(n => n.id)\n    });\n    if (!latestNode) {\n      // Fallback: if latest node not found, use default position\n      console.log('📍 Latest node not found, using default position');\n      return {\n        x: defaultStartX,\n        y: defaultStartY\n      };\n    }\n    // Place new node directly below the latest node\n    const newPosition = {\n      x: latestNode.position.x,\n      // Same X coordinate (vertical alignment)\n      y: latestNode.position.y + verticalSpacing // Below with spacing\n    };\n    console.log('📍 Calculated new position below latest node:', {\n      latestNodePosition: latestNode.position,\n      newPosition,\n      spacing: verticalSpacing\n    });\n    return newPosition;\n  }\n  // Force our calculated positions to override any canvas interference\n  forceCorrectPositions() {\n    // Update canvas nodes to match our controlled build agent node positions\n    this.canvasNodes = this.buildAgentNodes.map(node => ({\n      id: node.id,\n      type: 'build-agent',\n      data: {\n        ...node,\n        width: this.isExecuteMode ? 55 : 90\n      },\n      position: node.position // Use our controlled position\n    }));\n    // Force change detection immediately\n    this.cdr.detectChanges();\n  }\n  // Force execute mode positions using DOM manipulation\n  forceExecutePositions() {\n    if (!this.isExecuteMode) return;\n    this.buildAgentNodes.forEach((node, index) => {\n      const executePosition = {\n        x: 40,\n        y: 100 + index * 80\n      };\n      // Force DOM position using CSS variables\n      const nodeElement = document.querySelector(`[data-node-id=\"${node.id}\"]`);\n      if (nodeElement) {\n        nodeElement.style.setProperty('--node-x', `${executePosition.x}px`);\n        nodeElement.style.setProperty('--node-y', `${executePosition.y}px`);\n      }\n      // Also update our data\n      node.position = {\n        ...executePosition\n      };\n    });\n    // Update canvas nodes to match\n    this.canvasNodes = this.buildAgentNodes.map((node, index) => ({\n      id: node.id,\n      type: 'build-agent',\n      data: {\n        ...node,\n        width: 55,\n        position: node.position\n      },\n      position: node.position\n    }));\n    this.cdr.detectChanges();\n  }\n  // Force a specific node to a specific position using DOM manipulation\n  forceNodeToPosition(nodeId, position) {\n    console.log('🎯 forceNodeToPosition called:', {\n      nodeId,\n      position\n    });\n    const nodeElement = document.querySelector(`[data-node-id=\"${nodeId}\"]`);\n    if (nodeElement) {\n      console.log('🎯 Found node element, forcing position via CSS');\n      // Force position using CSS transform (override CDK drag)\n      nodeElement.style.position = 'absolute';\n      nodeElement.style.left = `${position.x}px`;\n      nodeElement.style.top = `${position.y}px`;\n      nodeElement.style.transform = ''; // Clear any CDK transform\n      // Also update our internal data to match\n      const buildAgentIndex = this.buildAgentNodes.findIndex(n => n.id === nodeId);\n      if (buildAgentIndex !== -1) {\n        this.buildAgentNodes[buildAgentIndex] = {\n          ...this.buildAgentNodes[buildAgentIndex],\n          position: position\n        };\n      }\n      const canvasIndex = this.canvasNodes.findIndex(n => n.id === nodeId);\n      if (canvasIndex !== -1) {\n        this.canvasNodes[canvasIndex] = {\n          ...this.canvasNodes[canvasIndex],\n          position: position\n        };\n      }\n      console.log('🎯 Position forced successfully');\n    } else {\n      console.log('🎯 Node element not found, retrying...');\n      // Retry after a short delay if element not found\n      setTimeout(() => {\n        this.forceNodeToPosition(nodeId, position);\n      }, 100);\n    }\n  }\n  // Simple method to create a connection between two nodes\n  createConnection(sourceId, targetId) {\n    // Validate that both nodes exist\n    const sourceNode = this.buildAgentNodes.find(node => node.id === sourceId);\n    const targetNode = this.buildAgentNodes.find(node => node.id === targetId);\n    if (!sourceNode || !targetNode) {\n      console.warn(`Cannot create connection: source or target node not found. Source: ${sourceId}, Target: ${targetId}`);\n      return;\n    }\n    // Check if connection already exists\n    const existingConnection = this.canvasEdges.find(edge => edge.source === sourceId && edge.target === targetId);\n    if (existingConnection) {\n      return; // Connection already exists\n    }\n    const newEdge = {\n      id: `edge_${sourceId}_${targetId}`,\n      source: sourceId,\n      target: targetId,\n      animated: false // Simple static connections\n    };\n    this.canvasEdges = [...this.canvasEdges, newEdge];\n  }\n  // Create simple straight-line connections between nodes in order\n  createAgentFlowConnections(nodes) {\n    console.log('🔗 createAgentFlowConnections called:', {\n      nodesLength: nodes.length,\n      nodeIds: nodes.map(n => n.id),\n      nodePositions: nodes.map(n => ({\n        id: n.id,\n        position: n.position\n      }))\n    });\n    if (nodes.length < 2) {\n      console.log('🔗 Not enough nodes for connections, skipping');\n      return; // Need at least 2 nodes to create connections\n    }\n    // Clear existing edges first\n    this.canvasEdges = [];\n    console.log('🔗 Cleared existing edges');\n    // Dead simple: connect each node to the next one in sequence\n    console.log('🔗 Creating connections...');\n    for (let i = 0; i < nodes.length - 1; i++) {\n      const sourceId = nodes[i].id;\n      const targetId = nodes[i + 1].id;\n      console.log(`🔗 Creating connection: ${sourceId} -> ${targetId}`);\n      this.createConnection(sourceId, targetId);\n    }\n    console.log('🔗 Final edges:', this.canvasEdges);\n    // Force change detection to ensure edges are updated\n    this.cdr.detectChanges();\n    console.log('🔗 Connection creation completed');\n  }\n  // Create consolidated execute nodes from build agent nodes\n  createExecuteNodes() {\n    // No complex execute nodes - just use the simple positioning\n  }\n  // Get execute node data for a given canvas node\n  getExecuteNodeData(node) {\n    if (!this.isExecuteMode) return undefined;\n    // Create executeNodeData from the individual node data for icons to work\n    const nodeData = node.data;\n    if (!nodeData) return undefined;\n    return {\n      type: nodeData.type,\n      nodes: [nodeData],\n      // Single node array\n      position: nodeData.position || node.position\n    };\n  }\n  // Generate a unique node ID - similar to workflow editor\n  generateNodeId() {\n    return `node_${Date.now()}_${Math.floor(Math.random() * 1000)}`;\n  }\n  // Load agent data from catalogue\n  loadAgentData(agentId) {\n    // For collaborative agents, don't load labels API - proceed directly\n    if (this.currentAgentType === 'collaborative') {\n      this.loadAgentDataAfterLabels(agentId);\n      return;\n    }\n    // For individual agents, ensure labels data is loaded first\n    if (!this.labelsCache) {\n      this.agentService.getLabels().subscribe({\n        next: response => {\n          this.labelsCache = response;\n          // Load all data from cached labels\n          this.loadModelsFromCache();\n          this.loadKnowledgeBaseFromCache();\n          this.loadGuardrailsFromCache();\n          // Now load the agent data\n          this.loadAgentDataAfterLabels(agentId);\n        },\n        error: error => {\n          console.error('Error loading labels for agent mapping:', error);\n          // Proceed anyway with existing data\n          this.loadAgentDataAfterLabels(agentId);\n        }\n      });\n      return;\n    }\n    this.loadAgentDataAfterLabels(agentId);\n  }\n  // Load agent data after ensuring labels are loaded\n  loadAgentDataAfterLabels(agentId) {\n    if (this.currentAgentType === 'collaborative') {\n      this.agentService.getCollaborativeAgentDetailsById(agentId).subscribe({\n        next: response => {\n          // Handle the new API response format\n          let agentData = null;\n          if (response && response.agentDetail) {\n            // New API format: data is in agentDetail property\n            agentData = response.agentDetail;\n          } else if (response && (response.agentDetails || response.agentDetail)) {\n            // Fallback to old format\n            agentData = response.agentDetails || response.agentDetail;\n          } else if (response && response.data) {\n            // Another possible format\n            agentData = response.data;\n          }\n          if (agentData) {\n            // Store agent details for playground display\n            this.currentAgentDetails = {\n              name: agentData.name || 'Unnamed Agent',\n              description: agentData.description || agentData.agentDetails || '',\n              role: agentData.role || '',\n              goal: agentData.goal || '',\n              backstory: agentData.backstory || '',\n              expectedOutput: agentData.expectedOutput || ''\n            };\n            // Create a response object in the expected format for mapping\n            const mappedResponse = {\n              agentDetail: agentData,\n              agentDetails: agentData // Keep both for compatibility\n            };\n            this.mapCollaborativeAgentDataToCanvas(mappedResponse);\n          } else {\n            console.warn('No collaborative agent data found in response:', response);\n            this.showErrorMessage('Agent Not Found', 'No agent data found. The agent might not exist or you might not have permission to view it.');\n          }\n        },\n        error: error => {\n          console.error('Error loading collaborative agent data:', error);\n          this.showErrorMessage('Load Failed', 'Failed to load collaborative agent data. Please check the console for details.');\n        }\n      });\n    } else {\n      this.agentService.getAgentById(agentId).subscribe({\n        next: response => {\n          if (response && (response.useCaseName || response.name)) {\n            // Store agent details for playground display with all necessary data\n            this.currentAgentDetails = {\n              name: response.name || response.useCaseName || 'Unnamed Agent',\n              description: response.description || response.useCaseDescription || '',\n              role: response.role || '',\n              goal: response.goal || '',\n              backstory: response.backstory || '',\n              expectedOutput: response.expectedOutput || '',\n              // Add playground-specific data from the API response\n              useCaseCode: response.useCaseCode || response.code || '',\n              useCaseName: response.useCaseName || response.name || '',\n              useCaseId: response.useCaseId || response.id || '',\n              organizationPath: response.organizationPath || '',\n              // Extract config data for mode and useCaseIdentifier\n              config: response.config || []\n            };\n            this.mapAgentDataToCanvas(response);\n          } else {\n            console.warn('No individual agent data found in response:', response);\n            this.showErrorMessage('Agent Not Found', 'No agent data found. The agent might not exist or you might not have permission to view it.');\n          }\n        },\n        error: error => {\n          console.error('Error loading individual agent data:', error);\n          this.showErrorMessage('Load Failed', 'Failed to load individual agent data. Please check the console for details.');\n        }\n      });\n    }\n  }\n  // Map agent data to canvas board\n  mapAgentDataToCanvas(agentData) {\n    if (!agentData) {\n      console.error('No individual agent data provided');\n      return;\n    }\n    // Set basic agent information using the actual API response structure\n    // In duplicate mode, clear the name field so user can enter a new name\n    if (this.isDuplicateMode) {\n      this.agentName = ''; // Clear name for duplicate mode\n    } else {\n      if (agentData.useCaseName) {\n        this.agentName = agentData.useCaseName;\n      } else if (agentData.name) {\n        this.agentName = agentData.name;\n      }\n    }\n    // Extract agent code for API calls\n    if (agentData.useCaseCode) {\n      this.agentCode = agentData.useCaseCode;\n    } else if (agentData.code) {\n      this.agentCode = agentData.code;\n    } else {\n      // Fallback: generate code from agent name\n      this.agentCode = this.agentName.trim().replace(/\\s+/g, '_').toUpperCase();\n    }\n    if (agentData.useCaseDetails) {\n      this.agentDetail = agentData.useCaseDetails;\n    } else if (agentData.description) {\n      this.agentDetail = agentData.description;\n    } else if (agentData.agentDetail) {\n      this.agentDetail = agentData.agentDetail;\n    }\n    // Set metadata from the API response\n    if (agentData.organizationPath) {\n      const pathParts = agentData.organizationPath.split('@');\n      if (pathParts.length >= 5) {\n        this.agentMetadata = {\n          org: pathParts[1] || agentData.org || '',\n          domain: pathParts[2] || agentData.domain || '',\n          project: pathParts[3] || agentData.project || '',\n          team: pathParts[4] || agentData.team || ''\n        };\n      }\n    } else {\n      this.agentMetadata = {\n        org: agentData.org || '',\n        domain: agentData.domain || '',\n        project: agentData.project || '',\n        team: agentData.team || ''\n      };\n    }\n    // Map individual agent data to canvas nodes based on config array\n    // Use a temporary array to collect nodes in hierarchy order\n    const nodesByType = {\n      prompt: [],\n      model: [],\n      knowledge: [],\n      guardrail: [],\n      tool: []\n    };\n    let nodeCounter = 1;\n    if (agentData.prompt || agentData.useCaseDetails || agentData.useCaseName) {\n      let promptName = agentData.prompt || 'Default Prompt';\n      // Try to find the exact prompt by name from the loaded prompts\n      let promptData = this.allToolItems['prompts']?.find(p => p.name === promptName || p.name.toLowerCase() === promptName.toLowerCase());\n      // If not found by name, use the first available prompt as fallback\n      if (!promptData && this.allToolItems['prompts']?.length > 0) {\n        promptData = this.allToolItems['prompts'][0];\n        promptName = promptData.name; // Use the actual prompt name from the data\n      }\n      // Always create a prompt node (essential for individual agents)\n      const promptNode = {\n        id: `prompt-${nodeCounter++}`,\n        name: promptName,\n        type: 'prompt',\n        position: {\n          x: 0,\n          y: 0\n        } // Will be calculated later\n      };\n      nodesByType['prompt'].push(promptNode);\n    }\n    // Process the config array to extract model, tools, knowledge bases, etc.\n    if (agentData.config && Array.isArray(agentData.config)) {\n      agentData.config.forEach(category => {\n        if (category.config && Array.isArray(category.config)) {\n          category.config.forEach(configItem => {\n            // Store configId for update operations\n            if (configItem.configId) {\n              const configKey = `${category.categoryId}-${configItem.configKey}`;\n              this.agentConfigIds.set(configKey, configItem.configId);\n            }\n            // Handle MODEL configuration\n            if (configItem.configKey === 'MODEL' && configItem.configValue) {\n              const modelId = configItem.configValue;\n              const modelData = this.allToolItems['models']?.find(m => m.id === modelId || m.id === modelId.toString() || m.id.toString() === modelId);\n              if (modelData) {\n                nodesByType['model'].push({\n                  id: `model-${nodeCounter++}`,\n                  type: 'model',\n                  name: modelData.name,\n                  icon: undefined,\n                  // Let node component use Lucide icon\n                  position: {\n                    x: 0,\n                    y: 0\n                  },\n                  // Will be calculated later\n                  originalToolData: modelData // Store the original tool data for ID retrieval\n                });\n              } else {\n                // Create a placeholder model node if not found\n                nodesByType['model'].push({\n                  id: `model-${nodeCounter++}`,\n                  type: 'model',\n                  name: `Model ID: ${modelId}`,\n                  icon: undefined,\n                  // Let node component use Lucide icon\n                  position: {\n                    x: 0,\n                    y: 0\n                  },\n                  // Will be calculated later\n                  originalToolData: {\n                    id: modelId,\n                    name: `Model ID: ${modelId}`\n                  } // Store the ID for retrieval\n                });\n              }\n            }\n            // Handle RAG_KNOWLEDGEBASE_NAME configuration (can contain comma-separated IDs)\n            if (configItem.configKey === 'RAG_KNOWLEDGEBASE_NAME' && configItem.configValue) {\n              const kbValue = configItem.configValue.toString(); // Ensure it's a string\n              const kbIds = kbValue.split(',').map(id => id.trim()).filter(id => id); // Split by comma and clean\n              // Process each knowledge base ID separately to create individual nodes\n              kbIds.forEach(kbId => {\n                const knowledgeData = this.allToolItems['knowledge']?.find(k => {\n                  return k.id === kbId || k.id === kbId.toString() || k.id.toString() === kbId || k.name === kbId || k.name.toLowerCase() === kbId.toLowerCase();\n                });\n                if (knowledgeData) {\n                  nodesByType['knowledge'].push({\n                    id: `knowledge-${nodeCounter++}`,\n                    type: 'knowledge',\n                    name: knowledgeData.name,\n                    icon: undefined,\n                    // Let node component use Lucide icon\n                    position: {\n                      x: 0,\n                      y: 0\n                    },\n                    // Will be calculated later\n                    originalToolData: knowledgeData // Store the original tool data for ID retrieval\n                  });\n                } else {\n                  // Create a placeholder knowledge node if not found in labels\n                  // Try to get the name from labels API directly\n                  let kbName = `Knowledge Base ID: ${kbId}`;\n                  if (this.labelsCache && this.labelsCache.categoryLabels) {\n                    const iclCategory = this.labelsCache.categoryLabels.find(cat => cat.categoryId === 2);\n                    if (iclCategory) {\n                      const kbLabel = iclCategory.labels.find(label => label.labelCode === 'RAG_KNOWLEDGEBASE_NAME');\n                      if (kbLabel && kbLabel.labelValues) {\n                        const kbOptions = this.agentService.parseLabelValues(kbLabel.labelValues);\n                        const kbOption = kbOptions.find(opt => opt.value === kbId);\n                        if (kbOption) {\n                          kbName = kbOption.name;\n                        }\n                      }\n                    }\n                  }\n                  nodesByType['knowledge'].push({\n                    id: `knowledge-${nodeCounter++}`,\n                    type: 'knowledge',\n                    name: kbName,\n                    icon: undefined,\n                    // Let node component use Lucide icon\n                    position: {\n                      x: 0,\n                      y: 0\n                    },\n                    // Will be calculated later\n                    originalToolData: {\n                      id: kbId,\n                      name: kbName\n                    } // Store the ID for retrieval\n                  });\n                }\n              });\n            }\n            // Handle ENABLE_GUARDRAILS - create a general guardrail node when enabled\n            if (configItem.configKey === 'ENABLE_GUARDRAILS' && (configItem.configValue === 'true' || configItem.configValue === true)) {\n              // Only add one general guardrail node if not already added\n              if (nodesByType['guardrail'].length === 0) {\n                nodesByType['guardrail'].push({\n                  id: `guardrail-${nodeCounter++}`,\n                  type: 'guardrail',\n                  name: 'Guardrails',\n                  icon: undefined,\n                  // Let node component use Lucide icon\n                  position: {\n                    x: 0,\n                    y: 0\n                  } // Will be calculated later\n                });\n              }\n            }\n            // Handle specific GUARDRAIL configurations (only show enabled guardrails)\n            if (configItem.configKey && configItem.configKey.startsWith('GUARDRAIL_') && configItem.configKey !== 'ENABLE_GUARDRAILS' && (configItem.configValue === 'true' || configItem.configValue === true)) {\n              const guardrailData = this.allToolItems['guardrails']?.find(g => g.code === configItem.configKey);\n              if (guardrailData) {\n                nodesByType['guardrail'].push({\n                  id: `guardrail-${nodeCounter++}`,\n                  type: 'guardrail',\n                  name: guardrailData.name,\n                  icon: undefined,\n                  // Let node component use Lucide icon\n                  position: {\n                    x: 0,\n                    y: 0\n                  } // Will be calculated later\n                });\n              } else {\n                // Create placeholder guardrail node\n                const guardrailName = configItem.configKey.replace('GUARDRAIL_', '').replace(/_/g, ' ');\n                nodesByType['guardrail'].push({\n                  id: `guardrail-${nodeCounter++}`,\n                  type: 'guardrail',\n                  name: guardrailName,\n                  icon: undefined,\n                  // Let node component use Lucide icon\n                  position: {\n                    x: 0,\n                    y: 0\n                  } // Will be calculated later\n                });\n              }\n            }\n          });\n        }\n      });\n    }\n    // Assemble nodes in hierarchy order and calculate positions\n    const nodes = [];\n    const flowOrder = ['prompt', 'model', 'knowledge', 'guardrail', 'tool'];\n    // Clear and reset for clean positioning (individual agents)\n    this.buildAgentNodes = [];\n    this.latestNodeId = null;\n    console.log('🔧 EDIT MODE DETECTION IN INDIVIDUAL MAPPING:', {\n      isEditMode: this.isEditMode,\n      isViewMode: this.isViewMode,\n      isExecuteMode: this.isExecuteMode,\n      isDuplicateMode: this.isDuplicateMode,\n      totalNodesFound: Object.keys(nodesByType).reduce((total, type) => total + nodesByType[type].length, 0)\n    });\n    flowOrder.forEach(nodeType => {\n      nodesByType[nodeType].forEach(node => {\n        console.log(`🔧 Processing node ${nodes.length + 1}: ${node.name} (${node.type})`);\n        // Use different positioning logic based on mode\n        if (this.isEditMode) {\n          // In edit mode, use consistent initial positioning\n          const nodeIndex = nodes.length; // Current index in the final nodes array\n          console.log(`🔧 EDIT MODE - calculating position for index ${nodeIndex}`);\n          node.position = this.calculateEditModePosition(nodeIndex);\n        } else {\n          // Use simple vertical positioning for other modes (view, duplicate, etc.)\n          console.log(`🔧 NON-EDIT MODE - using calculateSimpleVerticalPosition`);\n          node.position = this.calculateSimpleVerticalPosition();\n        }\n        console.log(`🔧 Final node position: ${JSON.stringify(node.position)}`);\n        nodes.push(node);\n        // Add to buildAgentNodes and update latest node for next position calculation\n        this.buildAgentNodes.push(node);\n        this.latestNodeId = node.id;\n      });\n    });\n    // Update build agent nodes with final array\n    this.buildAgentNodes = nodes;\n    // Update canvas nodes for display\n    this.canvasNodes = nodes.map(node => ({\n      id: node.id,\n      type: 'build-agent',\n      data: {\n        ...node,\n        width: this.isExecuteMode ? 55 : 120\n      },\n      position: node.position\n    }));\n    // Update canvas board with the loaded data first\n    this.cdr.detectChanges();\n    // Wait for DOM to update, then create connections\n    setTimeout(() => {\n      // Clear existing edges when loading new agent data\n      this.canvasEdges = [];\n      // Create connections between nodes in the proper flow order\n      this.createAgentFlowConnections(nodes);\n      // Force update of connection points after nodes are positioned\n      if (this.canvasBoardComponent) {\n        this.canvasBoardComponent.updateNodeConnectionPoints();\n      }\n      // Trigger change detection again to ensure connections are rendered\n      this.cdr.detectChanges();\n    }, 200);\n    // In duplicate mode, clear the agent ID after loading data to ensure new agent creation\n    if (this.isDuplicateMode) {\n      this.currentAgentId = null;\n    }\n    // If in execute mode, automatically initialize playground\n    if (this.isExecuteMode) {\n      // Auto-select this agent in the dropdown when execute mode loads\n      this.autoSelectCurrentAgentInDropdown();\n      setTimeout(() => {\n        this.initializeExecuteMode();\n      }, 500);\n    }\n    // If in edit mode, ensure connections are properly established\n    if (this.isEditMode) {\n      setTimeout(() => {\n        this.ensureConnectionsInEditMode();\n      }, 500);\n    }\n  }\n  // Map collaborative agent data to canvas board\n  mapCollaborativeAgentDataToCanvas(response) {\n    // Handle different response structures\n    let agentData;\n    if (response.agentDetails && Array.isArray(response.agentDetails) && response.agentDetails.length > 0) {\n      agentData = response.agentDetails[0];\n    } else if (response.agentDetail) {\n      agentData = response.agentDetail;\n    } else if (response.data) {\n      agentData = response.data;\n    } else {\n      agentData = response;\n    }\n    if (!agentData) {\n      console.error('No agent data found in response');\n      return;\n    }\n    // Set basic agent information\n    // In duplicate mode, clear the name field so user can enter a new name\n    if (this.isDuplicateMode) {\n      this.agentName = ''; // Clear name for duplicate mode\n    } else {\n      if (agentData.name) {\n        this.agentName = agentData.name;\n      }\n    }\n    // Extract agent code for API calls\n    if (agentData.code) {\n      this.agentCode = agentData.code;\n    } else if (agentData.useCaseCode) {\n      this.agentCode = agentData.useCaseCode;\n    } else {\n      // Fallback: generate code from agent name\n      this.agentCode = this.agentName.trim().replace(/\\s+/g, '_').toUpperCase();\n    }\n    // Set agent details - check multiple possible field names\n    if (agentData.agentDetails) {\n      this.agentDetail = agentData.agentDetails;\n    } else if (agentData.agentDetail) {\n      this.agentDetail = agentData.agentDetail;\n    } else if (agentData.description) {\n      this.agentDetail = agentData.description;\n    }\n    // Set metadata from the API response\n    this.agentMetadata = {\n      org: agentData.org || '',\n      domain: agentData.domain || '',\n      project: agentData.project || '',\n      team: agentData.team || ''\n    };\n    // Map collaborative agent data to canvas nodes\n    // Use a temporary array to collect nodes in hierarchy order\n    const nodesByType = {\n      prompt: [],\n      model: [],\n      knowledge: [],\n      guardrail: [],\n      tool: []\n    };\n    let nodeCounter = 1;\n    // Add prompt node first - handle different prompt structures for different agent types\n    const shouldCreatePromptNode = this.currentAgentType === 'individual' && agentData.prompt || this.currentAgentType === 'collaborative' && (agentData.goal || agentData.role || agentData.description);\n    if (shouldCreatePromptNode) {\n      // Get a fallback prompt data for icon and structure\n      let promptData = this.allToolItems['prompts']?.find(p => p.promptType === 'zero shot') || this.allToolItems['prompts']?.[0];\n      // Create a default prompt data if none found\n      if (!promptData) {\n        promptData = {\n          id: 'default-prompt',\n          name: 'Default Prompt',\n          type: 'prompt',\n          icon: this.getIconForType('prompt'),\n          description: 'Default prompt'\n        };\n      }\n      // Determine prompt node name and data based on agent type\n      let promptNodeName;\n      let promptNodeData;\n      if (this.currentAgentType === 'collaborative') {\n        // For collaborative agents: use goal as primary display, store all prompt-related fields\n        promptNodeName = agentData.goal || agentData.role || agentData.description || 'Collaborative Agent Prompt';\n        // Truncate goal if too long for display\n        if (promptNodeName.length > 150) {\n          promptNodeName = promptNodeName.substring(0, 150) + '...';\n        }\n        promptNodeData = {\n          ...promptData,\n          // Store all collaborative agent prompt fields\n          goal: agentData.goal,\n          role: agentData.role,\n          description: agentData.description,\n          expectedOutput: agentData.expectedOutput,\n          backstory: agentData.backstory,\n          agentType: 'collaborative'\n        };\n      } else {\n        // For individual agents: use prompt field, store the full prompt text\n        promptNodeName = agentData.prompt || 'Individual Agent Prompt';\n        // Truncate prompt if too long for display\n        if (promptNodeName.length > 150) {\n          promptNodeName = promptNodeName.substring(0, 150) + '...';\n        }\n        promptNodeData = {\n          ...promptData,\n          // Store the full prompt text for individual agents\n          prompt: agentData.prompt,\n          fullPromptText: agentData.prompt,\n          agentType: 'individual'\n        };\n      }\n      const promptNode = {\n        id: `prompt-${nodeCounter++}`,\n        name: promptNodeName,\n        type: 'prompt',\n        icon: promptData.icon || this.getIconForType('prompt'),\n        position: {\n          x: 0,\n          y: 0\n        },\n        // Will be calculated later\n        originalToolData: promptNodeData\n      };\n      nodesByType['prompt'].push(promptNode);\n    } else if (agentData.agentDetail || agentData.name) {\n      // Fallback: Create a default prompt node if no prompt data found\n      const promptData = this.allToolItems['prompts']?.find(p => p.promptType === 'zero shot') || this.allToolItems['prompts']?.[0];\n      if (promptData) {\n        let promptNodeName;\n        let fallbackPromptData;\n        if (this.currentAgentType === 'collaborative') {\n          // For collaborative agents, try to use available fields or fallback to prompt name\n          promptNodeName = agentData.goal || agentData.role || agentData.description || promptData.name;\n          fallbackPromptData = {\n            ...promptData,\n            goal: agentData.goal,\n            role: agentData.role,\n            description: agentData.description,\n            expectedOutput: agentData.expectedOutput,\n            agentType: 'collaborative'\n          };\n        } else {\n          // For individual agents, use prompt field or fallback to prompt name\n          promptNodeName = agentData.prompt || promptData.name;\n          fallbackPromptData = {\n            ...promptData,\n            prompt: agentData.prompt,\n            fullPromptText: agentData.prompt,\n            agentType: 'individual'\n          };\n        }\n        const promptNode = {\n          id: `prompt-${nodeCounter++}`,\n          name: promptNodeName,\n          type: 'prompt',\n          icon: promptData.icon || this.getIconForType('prompt'),\n          position: {\n            x: 0,\n            y: 0\n          },\n          // Will be calculated later\n          originalToolData: fallbackPromptData\n        };\n        nodesByType['prompt'].push(promptNode);\n      }\n    }\n    // Add model nodes - handle both old and new API formats\n    let modelReferences = [];\n    // New API format: agentConfigs.modelRef (array of model IDs or objects)\n    if (agentData.agentConfigs && agentData.agentConfigs.modelRef) {\n      const modelRefs = Array.isArray(agentData.agentConfigs.modelRef) ? agentData.agentConfigs.modelRef : [agentData.agentConfigs.modelRef];\n      // Handle both ID arrays and object arrays\n      modelReferences = modelRefs.map(ref => {\n        if (typeof ref === 'number' || typeof ref === 'string') {\n          return {\n            modelId: ref\n          };\n        }\n        return ref;\n      });\n    }\n    // Old API format: modelDetails\n    else if (agentData.modelDetails) {\n      modelReferences = [agentData.modelDetails];\n    }\n    modelReferences.forEach(modelRef => {\n      const modelId = modelRef.modelId || modelRef.id;\n      const modelData = this.allToolItems['models']?.find(m => m.id === modelId || m.id === modelId.toString() || m.id.toString() === modelId);\n      if (modelData) {\n        nodesByType['model'].push({\n          id: `model-${nodeCounter++}`,\n          type: 'model',\n          name: modelData.name,\n          icon: undefined,\n          // Let node component use Lucide icon\n          position: {\n            x: 0,\n            y: 0\n          },\n          // Will be calculated later\n          originalToolData: modelData // Store original data for retrieval\n        });\n      } else {\n        // Create a placeholder model node with the model name from API\n        const modelName = modelRef.model || modelRef.modelDeploymentName || `Model ID: ${modelId}`;\n        nodesByType['model'].push({\n          id: `model-${nodeCounter++}`,\n          type: 'model',\n          name: modelName,\n          icon: undefined,\n          // Let node component use Lucide icon\n          position: {\n            x: 0,\n            y: 0\n          },\n          // Will be calculated later\n          originalToolData: {\n            id: modelId,\n            name: modelName\n          } // Store for retrieval\n        });\n      }\n    });\n    // Add knowledge base nodes - handle both old and new API formats\n    let knowledgeReferences = [];\n    // New API format: agentConfigs.knowledgeBaseRef (array of KB IDs or objects)\n    if (agentData.agentConfigs && agentData.agentConfigs.knowledgeBaseRef) {\n      const kbRefs = Array.isArray(agentData.agentConfigs.knowledgeBaseRef) ? agentData.agentConfigs.knowledgeBaseRef : [agentData.agentConfigs.knowledgeBaseRef];\n      // Handle both ID arrays and object arrays\n      knowledgeReferences = kbRefs.map(ref => {\n        if (typeof ref === 'number' || typeof ref === 'string') {\n          return {\n            knowledgeBaseId: ref\n          };\n        }\n        return ref;\n      });\n    }\n    // Old API format: indexCollectionName\n    else if (agentData.indexCollectionName && Array.isArray(agentData.indexCollectionName)) {\n      knowledgeReferences = agentData.indexCollectionName.map(name => ({\n        indexCollectionName: name\n      }));\n    }\n    knowledgeReferences.forEach(kbRef => {\n      const kbId = kbRef.knowledgeBaseId || kbRef.id;\n      const collectionName = kbRef.indexCollectionName || kbRef.name;\n      // Try to find by ID first, then by name\n      const knowledgeData = this.allToolItems['knowledge']?.find(k => kbId && (k.id === kbId || k.id === kbId.toString() || k.id.toString() === kbId) || collectionName && (k.name === collectionName || k.name.toLowerCase() === collectionName.toLowerCase()));\n      if (knowledgeData) {\n        nodesByType['knowledge'].push({\n          id: `knowledge-${nodeCounter++}`,\n          type: 'knowledge',\n          name: knowledgeData.name,\n          icon: undefined,\n          // Let node component use Lucide icon\n          position: {\n            x: 0,\n            y: 0\n          },\n          // Will be calculated later\n          originalToolData: knowledgeData // Store original data for retrieval\n        });\n      } else {\n        // Create a placeholder knowledge node\n        const kbName = collectionName || `Knowledge Base ID: ${kbId}`;\n        nodesByType['knowledge'].push({\n          id: `knowledge-${nodeCounter++}`,\n          type: 'knowledge',\n          name: kbName,\n          icon: undefined,\n          // Let node component use Lucide icon\n          position: {\n            x: 0,\n            y: 0\n          },\n          // Will be calculated later\n          originalToolData: {\n            id: kbId,\n            name: kbName\n          } // Store for retrieval\n        });\n      }\n    });\n    // Add tool nodes - handle both old and new API formats\n    let toolReferences = [];\n    let userToolReferences = [];\n    // New API format: agentConfigs.toolRef and agentConfigs.userToolRef (arrays of tool IDs or objects)\n    if (agentData.agentConfigs) {\n      if (agentData.agentConfigs.toolRef) {\n        const toolRefs = Array.isArray(agentData.agentConfigs.toolRef) ? agentData.agentConfigs.toolRef : [agentData.agentConfigs.toolRef];\n        // Handle both ID arrays and object arrays\n        toolReferences = toolRefs.map(ref => {\n          if (typeof ref === 'number' || typeof ref === 'string') {\n            return {\n              toolId: ref\n            };\n          }\n          return ref;\n        });\n      }\n      if (agentData.agentConfigs.userToolRef) {\n        const userToolRefs = Array.isArray(agentData.agentConfigs.userToolRef) ? agentData.agentConfigs.userToolRef : [agentData.agentConfigs.userToolRef];\n        // Handle both ID arrays and object arrays\n        userToolReferences = userToolRefs.map(ref => {\n          if (typeof ref === 'number' || typeof ref === 'string') {\n            return {\n              toolId: ref\n            };\n          }\n          return ref;\n        });\n      }\n    }\n    // Old API format: tools and userTools\n    else {\n      if (agentData.tools && Array.isArray(agentData.tools)) {\n        toolReferences = agentData.tools;\n      }\n      if (agentData.userTools && Array.isArray(agentData.userTools)) {\n        userToolReferences = agentData.userTools;\n      }\n    }\n    // Process built-in tools\n    toolReferences.forEach(tool => {\n      const toolId = tool.toolId || tool.id;\n      const toolData = this.allToolItems['tools']?.find(t => t.id === `builtin-${toolId}` || t.id === toolId || t.id === toolId.toString() || t.id.toString() === toolId);\n      if (toolData) {\n        nodesByType['tool'].push({\n          id: `tool-${nodeCounter++}`,\n          type: 'tool',\n          name: toolData.name,\n          icon: undefined,\n          // Let node component use Lucide icon\n          position: {\n            x: 0,\n            y: 0\n          },\n          // Will be calculated later\n          originalToolData: toolData // Store original data for retrieval\n        });\n      } else {\n        // Create a placeholder tool node with the tool name from API\n        const toolName = tool.toolName || `Tool ID: ${toolId}`;\n        nodesByType['tool'].push({\n          id: `tool-${nodeCounter++}`,\n          type: 'tool',\n          name: toolName,\n          icon: undefined,\n          // Let node component use Lucide icon\n          position: {\n            x: 0,\n            y: 0\n          },\n          // Will be calculated later\n          originalToolData: {\n            id: toolId,\n            name: toolName\n          } // Store for retrieval\n        });\n      }\n    });\n    // Process user tools\n    userToolReferences.forEach(userTool => {\n      const userToolId = userTool.toolId || userTool.id;\n      // Look for user tools (they have 'user-' prefix in our system)\n      const userToolData = this.allToolItems['tools']?.find(t => t.id === `user-${userToolId}` || t.id === userToolId || t.id === userToolId.toString() || t.id.toString() === userToolId);\n      if (userToolData) {\n        nodesByType['tool'].push({\n          id: `tool-${nodeCounter++}`,\n          type: 'tool',\n          name: userToolData.name,\n          icon: undefined,\n          // Let node component use Lucide icon\n          position: {\n            x: 0,\n            y: 0\n          },\n          // Will be calculated later\n          originalToolData: userToolData // Store original data for retrieval\n        });\n      } else {\n        // Create a placeholder user tool node\n        const toolName = userTool.toolName || `User Tool ID: ${userToolId}`;\n        nodesByType['tool'].push({\n          id: `tool-${nodeCounter++}`,\n          type: 'tool',\n          name: toolName,\n          icon: undefined,\n          // Let node component use Lucide icon\n          position: {\n            x: 0,\n            y: 0\n          },\n          // Will be calculated later\n          originalToolData: {\n            id: userToolId,\n            name: toolName\n          } // Store for retrieval\n        });\n      }\n    });\n    // Add a default prompt node if none exists (collaborative agents should have prompts)\n    if (nodesByType['prompt'].length === 0) {\n      const defaultPrompt = this.allToolItems['prompts']?.find(p => {\n        const promptType = p.type?.trim(); // Cast to any to access the prompt type property\n        return promptType === 'zero shot';\n      });\n      if (defaultPrompt) {\n        // For collaborative agents, use goal as the prompt node name, fallback to prompt data name\n        const promptNodeName = this.currentAgentType === 'collaborative' ? agentData.goal || defaultPrompt.name : defaultPrompt.name;\n        nodesByType['prompt'].push({\n          id: `prompt-${nodeCounter++}`,\n          type: 'prompt',\n          name: promptNodeName,\n          icon: defaultPrompt.icon || this.getIconForType('prompt'),\n          position: {\n            x: 0,\n            y: 0\n          },\n          // Will be calculated later\n          originalToolData: defaultPrompt // Store original data for retrieval\n        });\n      }\n    }\n    // Assemble nodes in hierarchy order and calculate positions\n    const nodes = [];\n    const flowOrder = ['prompt', 'model', 'knowledge', 'guardrail', 'tool'];\n    // Clear and reset for clean positioning (collaborative agents)\n    this.buildAgentNodes = [];\n    this.latestNodeId = null;\n    flowOrder.forEach(nodeType => {\n      nodesByType[nodeType].forEach(node => {\n        // Use different positioning logic based on mode\n        if (this.isEditMode) {\n          // In edit mode, use consistent initial positioning\n          const nodeIndex = nodes.length; // Current index in the final nodes array\n          node.position = this.calculateEditModePosition(nodeIndex);\n        } else {\n          // Use simple vertical positioning for other modes (view, duplicate, etc.)\n          node.position = this.calculateSimpleVerticalPosition();\n        }\n        nodes.push(node);\n        // Add to buildAgentNodes and update latest node for next position calculation\n        this.buildAgentNodes.push(node);\n        this.latestNodeId = node.id;\n      });\n    });\n    // Update build agent nodes with final array\n    this.buildAgentNodes = nodes;\n    // Update canvas nodes for display\n    this.canvasNodes = nodes.map(node => ({\n      id: node.id,\n      type: 'build-agent',\n      data: {\n        ...node,\n        width: this.isExecuteMode ? 55 : 120\n      },\n      position: node.position\n    }));\n    // Update canvas board with the loaded data first\n    this.cdr.detectChanges();\n    // Wait for DOM to update, then create connections\n    setTimeout(() => {\n      // Clear existing edges when loading new agent data\n      this.canvasEdges = [];\n      // Create connections between nodes in the proper flow order\n      this.createAgentFlowConnections(nodes);\n      // Force update of connection points after nodes are positioned\n      if (this.canvasBoardComponent) {\n        this.canvasBoardComponent.updateNodeConnectionPoints();\n      }\n      // Trigger change detection again to ensure connections are rendered\n      this.cdr.detectChanges();\n    }, 200);\n    // In duplicate mode, clear the agent ID after loading data to ensure new agent creation\n    if (this.isDuplicateMode) {\n      this.currentAgentId = null;\n    }\n    // If in execute mode, automatically initialize playground\n    if (this.isExecuteMode) {\n      setTimeout(() => {\n        this.initializeExecuteMode();\n      }, 500);\n    }\n    // If in edit mode, ensure connections are properly established\n    if (this.isEditMode) {\n      setTimeout(() => {\n        this.ensureConnectionsInEditMode();\n      }, 500);\n    }\n  }\n  // Simple method to auto-select current agent in dropdown\n  autoSelectCurrentAgentInDropdown() {\n    if (!this.agentName) {\n      return;\n    }\n    // Load agent details for playground\n    this.loadAgentDetailsForPlayground();\n    // Try to auto-select after a short delay\n    setTimeout(() => {\n      if (this.promptOptions && this.promptOptions.length > 0) {\n        // Find the current agent in the dropdown options\n        const currentAgentOption = this.promptOptions.find(option => option.name === this.agentName || option.name.toLowerCase() === this.agentName.toLowerCase());\n        if (currentAgentOption) {\n          this.selectedPrompt = currentAgentOption.name; // Use name for dropdown display\n          this.agentCode = currentAgentOption.value.toString(); // Store agent code\n          this.onPromptChanged(currentAgentOption);\n          this.cdr.detectChanges();\n        }\n      }\n    }, 1500); // Give more time for the dropdown to load\n  }\n  // Initialize execute mode - automatically open playground with agent selected\n  initializeExecuteMode() {\n    // Force execute mode positioning - override any existing positions\n    const executePositions = this.buildAgentNodes.map((node, index) => ({\n      x: 40,\n      y: 100 + index * 80\n    }));\n    // Update buildAgentNodes positions\n    this.buildAgentNodes.forEach((node, index) => {\n      node.position = {\n        ...executePositions[index]\n      };\n    });\n    // Recreate canvasNodes completely to avoid stale references\n    this.canvasNodes = this.buildAgentNodes.map((node, index) => ({\n      id: node.id,\n      type: 'build-agent',\n      data: {\n        ...node,\n        width: 55,\n        type: node.type,\n        name: node.name,\n        icon: node.icon,\n        position: {\n          ...executePositions[index]\n        }\n      },\n      position: {\n        ...executePositions[index]\n      }\n    }));\n    // Create simple connections\n    this.canvasEdges = [];\n    for (let i = 0; i < this.buildAgentNodes.length - 1; i++) {\n      this.canvasEdges.push({\n        id: `edge-${i}`,\n        source: this.buildAgentNodes[i].id,\n        target: this.buildAgentNodes[i + 1].id\n      });\n    }\n    // Force immediate update and wait for DOM\n    this.cdr.detectChanges();\n    // Force position update after DOM settles\n    setTimeout(() => {\n      this.forceExecutePositions();\n    }, 100);\n    // Initialize chat\n    this.chatMessages = [{\n      from: 'ai',\n      text: `Hi there! I am ${this.agentName || 'your build agent'}. How can I help you today?`\n    }];\n  }\n  // Method to handle agent selection from card click\n  selectAgentFromCard(agentData) {\n    // Store the selected agent data\n    this.autoSelectedAgentFromCard = agentData;\n    // Set agent properties\n    this.agentName = agentData.name;\n    this.agentCode = agentData.code;\n    this.selectedAgentMode = agentData.code;\n    this.selectedUseCaseIdentifier = agentData.useCaseIdentifier;\n    // Auto-select in dropdown (keep dropdown enabled)\n    this.selectedPrompt = agentData.name;\n    // Update the prompt options to include the selected agent if not already present\n    if (this.promptOptions && this.promptOptions.length > 0) {\n      const existingOption = this.promptOptions.find(option => option.value === agentData.code || option.name === agentData.name);\n      if (!existingOption) {\n        // Add the selected agent to the options\n        this.promptOptions.push({\n          value: agentData.code,\n          name: agentData.name,\n          agentData: agentData\n        });\n      }\n    }\n    // Force change detection\n    this.cdr.detectChanges();\n  }\n  // Navigation\n  goBack() {\n    this.router.navigate(['/agents']);\n  }\n  // Test method to check current agent details for playground\n  testCurrentAgentDetails() {\n    // Method kept for compatibility - details available in component properties\n  }\n  // Popup handling methods\n  showSuccessMessage(title, message) {\n    this.popupMessage = message;\n    this.showSuccessPopup = true;\n  }\n  // Show approval success message (redirects to agents page after confirmation)\n  showApprovalSuccessMessage(title, message) {\n    this.popupMessage = message;\n    this.isApprovalSuccess = true; // Set flag for approval success\n    this.showSuccessPopup = true;\n  }\n  showErrorMessage(title, message) {\n    this.popupMessage = message;\n    this.showErrorPopup = true;\n  }\n  showWarningMessage(title, message) {\n    this.popupMessage = message;\n    this.showWarningPopup = true;\n  }\n  closeSuccessPopup() {\n    this.showSuccessPopup = false;\n    this.isApprovalSuccess = false;\n    this.popupMessage = '';\n  }\n  // Ensure connections are properly established in edit mode\n  ensureConnectionsInEditMode() {\n    if (this.handleEditModeConnections && this.buildAgentNodes.length > 1) {\n      // Always recreate connections in edit mode to ensure they're properly established\n      this.createAgentFlowConnections(this.buildAgentNodes);\n      // Force update of connection points\n      if (this.canvasBoardComponent) {\n        this.canvasBoardComponent.updateNodeConnectionPoints();\n      }\n      // Trigger change detection\n      this.cdr.detectChanges();\n      // Reset the flag after handling\n      this.handleEditModeConnections = false;\n    }\n  }\n  // Calculate position for edit mode - consistent initial layout\n  calculateEditModePosition(index) {\n    const editModeStartX = 150;\n    const editModeStartY = 100;\n    const editModeVerticalSpacing = 120;\n    const calculatedPosition = {\n      x: editModeStartX,\n      y: editModeStartY + index * editModeVerticalSpacing\n    };\n    console.log('🔧 EDIT MODE POSITION CALCULATED:', {\n      index,\n      editModeStartX,\n      editModeStartY,\n      editModeVerticalSpacing,\n      calculatedPosition\n    });\n    return calculatedPosition;\n  }\n  // Top Bar Navigation Methods\n  navigateToAgentsList() {\n    this.router.navigate(['/build/agents']);\n  }\n  switchAgentType(newType) {\n    if (this.currentAgentType !== newType) {\n      // Clear all data before switching modes\n      this.clearAllAgentData();\n      // Navigate to the new agent type route\n      this.router.navigate(['/build/agents', newType]);\n    }\n  }\n  clearAllAgentData() {\n    // Clear agent basic information\n    this.agentName = '';\n    this.agentDetail = '';\n    this.agentCode = '';\n    // Clear agent metadata\n    this.agentMetadata = {\n      org: '',\n      domain: '',\n      project: '',\n      team: ''\n    };\n    // Clear all canvas-related data\n    this.buildAgentNodes = [];\n    this.canvasNodes = [];\n    this.canvasEdges = [];\n    // Clear current agent details and ID\n    this.currentAgentDetails = null;\n    this.currentAgentId = null;\n    // Reset latest node ID counter\n    this.latestNodeId = null;\n    // Clear any selection states\n    this.selectedNodeId = null;\n    // Clear preview data if any\n    this.showPreview = false;\n    this.previewData = null;\n    this.closePreview();\n    // Clear chat messages and test input\n    this.chatMessages = [];\n    this.testSpaceInput = '';\n    // Reset search form and tab selection\n    this.activeTab = 'prompts';\n    this.searchForm.get('search')?.setValue('', {\n      emitEvent: false\n    });\n    // Reset agent playground data\n    this.agentChatPayload = [];\n    this.agentFilesUploadedData = [];\n    this.agentAttachment = [];\n    // Reset execution ID for new session\n    this.executionId = crypto.randomUUID();\n    // Clear any loading states\n    this.isProcessingChat = false;\n    this.isAgentPlaygroundLoading = false;\n    // Reset mode flags to default\n    this.isViewMode = false;\n    this.isEditMode = false;\n    this.isExecuteMode = false;\n    this.isDuplicateMode = false;\n    this.isNewAgent = true;\n    this.isFieldsDisabled = false;\n    // Reset UI states\n    this.isAgentDetailsCollapsed = true;\n    this.isSidebarCollapsed = false;\n    this.showChatInterface = false;\n    this.autoSelectedAgentFromCard = null;\n    // Force change detection to update UI\n    this.cdr.detectChanges();\n    console.log('🔄 All agent data cleared for mode switch');\n  }\n  saveAgentFromTopBar() {\n    var _this9 = this;\n    return _asyncToGenerator(function* () {\n      // Use existing save logic\n      yield _this9.onPrimaryButtonClick();\n    })();\n  }\n  canSaveAgent() {\n    // Check if we have minimum required components\n    const hasPrompt = this.buildAgentNodes.some(node => node.type === 'prompt');\n    const hasModel = this.buildAgentNodes.some(node => node.type === 'model');\n    const hasValidName = !!(this.agentName && this.agentName.trim().length > 0);\n    const hasValidDescription = !!(this.agentDetail && this.agentDetail.trim().length > 0);\n    return hasPrompt && hasModel && hasValidName && hasValidDescription;\n  }\n  // Agent Details Floater Methods\n  toggleAgentDetailsFloater() {\n    this.isAgentDetailsCollapsed = !this.isAgentDetailsCollapsed;\n  }\n  // Handle Execute Agent action from success popup\n  onSuccessExecuteAgent() {\n    this.closeSuccessPopup();\n    // Navigate to the agent execution page\n    if (this.currentAgentId) {\n      this.router.navigate(['/build/agents', this.currentAgentType, 'execute'], {\n        queryParams: {\n          id: this.currentAgentId\n        }\n      });\n    }\n  }\n  // Handle Go to Agents Library action from success popup\n  onSuccessGoToLibrary() {\n    this.closeSuccessPopup();\n    // Navigate to the agents library\n    this.router.navigate(['/build/agents']);\n  }\n};\n__decorate([ViewChild(CanvasBoardComponent)], BuildAgentsComponent.prototype, \"canvasBoardComponent\", void 0);\n__decorate([ViewChild('drawerContainer', {\n  read: ViewContainerRef\n})], BuildAgentsComponent.prototype, \"drawerContainer\", void 0);\nBuildAgentsComponent = __decorate([Component({\n  selector: 'app-build-agents',\n  standalone: true,\n  imports: [CommonModule, FormsModule, ReactiveFormsModule, ButtonComponent, BuildAgentNodeComponent, PlaygroundComponent, AvaTextboxComponent, AvaTextareaComponent, IconComponent, PopupComponent, CustomTabsComponent, DropZoneCanvasComponent],\n  providers: [DialogService],\n  templateUrl: './build-agents.component.html',\n  styleUrls: ['./build-agents.component.scss']\n})], BuildAgentsComponent);\nexport { BuildAgentsComponent };", "map": {"version": 3, "names": ["Component", "ViewChild", "ViewContainerRef", "CommonModule", "FormsModule", "ReactiveFormsModule", "ButtonComponent", "AvaTextboxComponent", "AvaTextareaComponent", "IconComponent", "DialogService", "CanvasBoardComponent", "BuildAgentNodeComponent", "Subject", "takeUntil", "switchMap", "finalize", "catchError", "of", "CustomTabsComponent", "PlaygroundComponent", "AgentsPreviewPanelComponent", "DropZoneCanvasComponent", "BuildAgentsComponent", "router", "route", "toolExecutionService", "cdr", "toolsService", "agentService", "promptsService", "tokenStorage", "agentPlaygroundService", "formBuilder", "knowledgeBaseService", "drawerService", "guardrailsService", "modelService", "dialogService", "canvasBoardComponent", "drawerContainer", "destroy$", "activeTab", "searchQuery", "searchForm", "isSidebarCollapsed", "showPreview", "selectedItem", "previewData", "isLoadingPreview", "canvasNodes", "canvasEdges", "buildAgentNodes", "executeNodes", "selectedNodeId", "currentAgentType", "currentMode", "currentAgentId", "isFieldsDisabled", "isDuplicateMode", "handleEditModeConnections", "showSuccessPopup", "showErrorPopup", "showWarningPopup", "isApprovalSuccess", "popupMessage", "popupTitle", "executionId", "showApprovalButton", "currentAgentDetails", "nodeLimits", "individual", "prompt", "model", "knowledge", "guardrail", "collaborative", "tool", "latestNodeId", "dropZoneConfig", "agentType", "allowedCategories", "prompts", "max", "required", "models", "guardrails", "tools", "tabs", "_customTabs", "customTabs", "allToolItems", "navigationHints", "isExecuteMode", "showChatInterface", "chatMessages", "isProcessingChat", "promptOptions", "executionSubscription", "isEditMode", "isViewMode", "isNewAgent", "agentConfigIds", "Map", "primaryButtonText", "<PERSON><PERSON><PERSON>", "agentDetail", "agentCode", "isAgentDetailsCollapsed", "agentMetadata", "org", "domain", "project", "team", "buildOrganizationPath", "labelsCache", "agentPlaygroundForm", "selected<PERSON><PERSON><PERSON>", "selectedAgentMode", "selectedUseCaseIdentifier", "agentChatPayload", "agentFilesUploadedData", "agentAttachment", "isAgentPlaygroundLoading", "agentPlaygroundDestroy", "responseModalConfig", "id", "type", "title", "closeButton", "primaryButtonConfig", "text", "buttonType", "buttonSize", "imageUrl", "linkURL", "isResponseModalOpen", "modalMessage", "isModalError", "autoSelectedAgentFromCard", "testSpaceInput", "guardrailToggleStates", "constructor", "group", "isConversational", "isUseTemplate", "search", "ngOnInit", "crypto", "randomUUID", "params", "subscribe", "configureTabsForAgentType", "loadDataForAgentType", "queryParams", "mode", "console", "log", "url", "navigate", "clear", "loadAgentData", "get", "valueChanges", "pipe", "value", "updateFilteredTools", "ngAfterViewInit", "detectChanges", "setTimeout", "length", "updateNodeConnectionPoints", "createAgentFlowConnections", "registerViewContainer", "ngOnDestroy", "next", "complete", "unsubscribe", "generateMockItems", "name", "icon", "count", "Array", "from", "_", "i", "description", "loadPrompts", "fetchAllPrompts", "filteredPrompts", "filterPromptsByAgentType", "map", "toString", "Math", "random", "descriptionConsolidated", "promptDescription", "promptType", "role", "goal", "backstory", "expectedOutput", "expectedOutputConsolidated", "originalPromptData", "error", "isArray", "filter", "trim", "loadModels", "getCollaborativeModels", "response", "modelDeploymentName", "modelDescription", "modelType", "aiEngine", "loadModelsFromLabels", "getModelsFromLabels", "loadModelsFromCache", "categoryLabels", "modelCategory", "find", "category", "categoryId", "modelLabel", "labels", "label", "labelCode", "labelValues", "parsedModels", "parseLabel<PERSON><PERSON><PERSON>", "option", "loadKnowledgeBase", "fetchAllKnowledge", "kb", "collectionName", "vectorDb", "modelRef", "splitSize", "loadKnowledgeBaseFromLabels", "getKnowledgeBasesFromLabels", "knowledgeItems", "item", "loadKnowledgeBaseFromCache", "iclCategory", "knowledgeLabel", "parsedKnowledge", "loadTools", "builtInTools$", "getBuiltInToolsList", "builtInTools", "builtInToolItems", "toolId", "toolName", "loadUserToolsWithPagination", "getUserToolsList", "userResponse", "totalRecords", "totalNoOfRecords", "fullUserResponse", "userTools", "userToolDetails", "userToolItems", "loadGuardrails", "getGuardrailsFromLabels", "code", "loadGuardrailsFromCache", "otherCategory", "labelType", "labelId", "labelName", "labelInfo", "_filteredTools", "currentTabTools", "filteredTools", "currentTools", "availableTools", "isAlreadyAdded", "some", "node", "originalToolData", "query", "toLowerCase", "includes", "onTabChange", "tabValue", "setValue", "emitEvent", "onSearchChange", "onCustomTabChange", "toggleSidebar", "isActiveCustomTab", "getTabIcon", "tab", "iconMap", "handleAutoTabSwitch", "nodeType", "currentLimits", "currentNodeLimit", "nextTab", "getNextRequiredTab", "currentNodeType", "hasModel", "onItemPreview", "loadPreviewData", "open", "closePreview", "data", "loading", "loadPromptDetails", "loadModelDetails", "loadKnowledgeDetails", "loadGuardrailDetails", "loadToolDetails", "getPromptById", "promptTask", "template", "content", "promptTemplate", "created<PERSON>y", "author", "updatedAt", "createdAt", "Date", "toISOString", "getOneModeById", "modelData", "temperature", "maxToken", "topP", "maxIteration", "baseUrl", "serviceUrl", "baseurl", "llmDeploymentName", "<PERSON><PERSON><PERSON><PERSON>", "apiKeyEncoded", "awsSecretKey", "apiVersion", "headerName", "createdOn", "date", "setDefaultModelData", "getKnowledgeBaseById", "embeddingModel", "files", "uploadType", "retrieverType", "fileCount", "totalSize", "reduce", "sum", "file", "fileSizeBytes", "uploadedBy", "createdDate", "uploadDate", "setDefaultKnowledgeData", "getGuardrailByName", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "organization", "config<PERSON><PERSON>", "chatBot", "setDefaultGuardrailData", "getUserToolDetails", "startsWith", "numericPart", "replace", "Number", "isNaN", "warn", "toolDetail", "toolConfigs", "userToolDetail", "toolDescription", "createTimestamp", "modifiedAt", "updateTimestamp", "isDeleted", "isApproved", "tool_class_name", "toolClassName", "tool_class_def", "toolClassDef", "functionality", "definition", "className", "modifiedBy", "getIconForType", "getAdditionalFields", "excludeFields", "additionalFields", "Object", "keys", "for<PERSON>ach", "key", "undefined", "push", "formatFieldName", "fieldName", "str", "toUpperCase", "iconName", "disabled", "canAddNodeOfType", "limit", "currentCount", "getNodeLimit", "removeItemFromList", "tabType", "addItemBackToList", "originalToolId", "existingItem", "toolItem", "isTabRequired", "slice", "validateMandatoryComponents", "missingComponents", "entries", "hasComponent", "<PERSON><PERSON><PERSON><PERSON>", "loadLabelsAndData", "<PERSON><PERSON><PERSON><PERSON>", "getBuilderTitle", "onDragStart", "event", "dataTransfer", "setData", "JSON", "stringify", "effectAllowed", "onCanvasDropped", "toolData", "getData", "parse", "existingNode", "onDeleteNode", "showErrorMessage", "toolType", "dropPosition", "position", "currentNodesCount", "autoPosition", "calculateAutoPosition", "nodeName", "newNodeId", "generateNodeId", "buildAgentNode", "nodeWidth", "newCanvasNode", "width", "buildAgentNodesLength", "canvasNodesLength", "lastBuildAgentNode", "lastCanvasNode", "lastBuildAgent", "lastCanvas", "buildAgentPosition", "canvasNodePosition", "canvasNodeDataPosition", "positionsMatch", "forceNodeToPosition", "onNodeSelected", "nodeId", "onNodeDoubleClicked", "selectedNode", "findToolItemForNode", "actualToolItem", "basicToolItem", "onNodeMoved", "newPosition", "currentBuildAgentPosition", "n", "currentCanvasPosition", "buildAgentNodeIndex", "findIndex", "canvasNodeIndex", "updatedBuildNode", "x", "y", "expected", "actual", "newBuildAgentPosition", "newCanvasPosition", "updateEdgePaths", "nodeToDelete", "edge", "source", "target", "recalculateNodePositionsAfterDeletion", "onConnectionCreated", "newEdge", "floor", "animated", "onStartConnection", "_event", "onUndo", "onRedo", "onReset", "onRun", "onExecute", "onDropZoneNodeDropped", "zone", "existingNodeIndex", "splice", "canvasNode", "totalNodes", "onDropZoneNodeDeleted", "onGuardrailToggled", "enabled", "getMetadataFromNavbar", "orgPath", "<PERSON><PERSON><PERSON><PERSON>", "organizationPath", "levelId", "orgId", "domainId", "projectId", "teamId", "parts", "split", "usecasePath", "usecaseIdPath", "names", "ids", "getUserSignature", "getDaUsername", "buildConfigurationFromLabels", "configurations", "hasKnowledge", "categoryName", "configs", "configurationName", "configurationValue", "getModelConfigValue", "getKnowledgeBaseConfigValue", "availableGuardrails", "getGuardrailConfigs", "extractPromptId", "match", "modelNode", "modelDataById", "knowledgeNodes", "knowledgeIds", "knowledgeData", "join", "extractGuardrailId", "guardrailNodes", "hasEnabledGuardrails", "enabledGuardrailNames", "Set", "isEnabled", "toggleState", "guardrailId", "guardrailData", "gr", "config<PERSON><PERSON>", "add", "alreadyAdded", "config", "validateIndividualAgentData", "errors", "warnings", "promptNode", "knowledgeNode", "guardrailNode", "buildIndividualAgentPayload", "promptDetails", "promptId", "promptData", "useCaseCode", "finalUseCaseDetails", "baseOrgPath", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useCaseDetails", "webPortalUseCase", "location", "sourceLanguage", "destinationLanguage", "configuration", "userSignature", "fetchPromptDetailsForSave", "_this", "_asyncToGenerator", "p", "extractedId", "apiPromptData", "Promise", "resolve", "reject", "saveIndividualAgent", "_this2", "validation", "warning", "message", "showProceedButton", "proceedButtonText", "performIndividualAgentSave", "saveCollaborativeAgent", "_this3", "validateCollaborativeAgentData", "performCollaborativeAgentSave", "_this4", "payload", "buildCollaborativeAgentPayloadV2", "saveAgent", "createCollaborativeAgentV2", "_this5", "individualAgentSave", "saveFn", "isCollaborativeType", "defaultSuccess", "successMessage", "useCaseId", "agentId", "confirmation", "confirmButtonText", "errorMessage", "showRetryButton", "retryButtonText", "promptNodes", "modelNodes", "toolNodes", "modelRefs", "m", "uniqueModelRefs", "k", "uniqueKnowledgeIds", "String", "parseInt", "sourceData", "index", "hasOriginalToolData", "originalToolDataId", "originalToolDataName", "t", "nodeOriginalData", "numericToolId", "numericMatch", "isUserTool", "agentConfigs", "knowledgeBaseRef", "maxIter", "maxRpm", "maxExecutionTime", "allowDelegation", "allowCodeExecution", "isSafeCodeExecution", "toolRef", "userToolRef", "Error", "agentDetails", "buildCollaborativeAgentChangeRequestPayload", "basePayload", "buildIndividualAgentUpdatePayload", "configId", "updateIndividualAgent", "_this6", "individualAgentEdit", "loadAgentDetailsForPlayground", "submitCollaborativeAgentChangeRequest", "_this7", "onApprovalRequested", "onPrimaryButtonClick", "_this8", "repositionNodesForExecuteMode", "forceExecutePositions", "autoSelectCurrentAgent", "now", "startExecution", "getExecutionState", "state", "isExecuting", "handleChatMessage", "showAgentError", "displayMessage", "fileNames", "documentName", "agentMode", "useCaseIdentifier", "processAgentFilesAndSendMessage", "sendAgentMessageToAPI", "user", "userInputs", "question", "fileWrapper", "submitAgentExecuteWithFile", "res", "handleAgentExecuteResponse", "err", "submitAgentExecute", "outputRaw", "agentResponse", "agent", "output", "formattedOutput", "detail", "formData", "FormData", "fileData", "append", "has", "getFileToContent", "fileResponse", "fileContent", "fileResponses", "sendAgentMessageToAPIWithFiles", "generatePrompt", "generatedResponse", "choices", "aiResponseText", "pop", "fileContents", "onPlaygroundConversationalToggle", "onPlaygroundTemplateToggle", "onFilesSelected", "onAgentConversationalToggle", "onAgentTemplateToggle", "onAgentFileSelected", "isImage", "removeAgentFile", "clearAgentChatData", "showResponseModal", "isError", "closeResponseModal", "agentData", "useCaseName", "unshift", "currentAgentOption", "onPromptChanged", "selectedOption", "onExitExecuteMode", "stopExecution", "onCanvasStateChanged", "edges", "onAgentNameChanged", "onMetadataChanged", "metadata", "onAgentDetailsChanged", "details", "getActiveTabLabel", "activeTabObj", "onCreateNewItem", "createExecuteNodes", "existingNodePositions", "pos", "centerX", "startY", "verticalSpacing", "nodeIndex", "executePosition", "simplePosition", "calculateSimpleVerticalPosition", "defaultStartX", "defaultStartY", "<PERSON><PERSON><PERSON><PERSON>", "defaultPosition", "latestNode", "searchingForId", "foundNode", "allNodeIds", "latestNodePosition", "spacing", "forceCorrectPositions", "nodeElement", "document", "querySelector", "style", "setProperty", "left", "top", "transform", "buildAgentIndex", "canvasIndex", "createConnection", "sourceId", "targetId", "sourceNode", "targetNode", "existingConnection", "nodes", "nodeIds", "nodePositions", "getExecuteNodeData", "nodeData", "loadAgentDataAfterLabels", "getCollaborativeAgentDetailsById", "mappedResponse", "mapCollaborativeAgentDataToCanvas", "getAgentById", "useCaseDescription", "mapAgentDataToCanvas", "pathParts", "nodesByType", "nodeCounter", "promptName", "configItem", "set", "config<PERSON><PERSON><PERSON>", "modelId", "kbValue", "kbIds", "kbId", "kbName", "cat", "kbLabel", "kbOptions", "kbOption", "opt", "g", "guardrailName", "flowOrder", "totalNodesFound", "total", "calculateEditModePosition", "autoSelectCurrentAgentInDropdown", "initializeExecuteMode", "ensureConnectionsInEditMode", "shouldCreatePromptNode", "promptNodeName", "promptNodeData", "substring", "fullPromptText", "fallbackPromptData", "modelReferences", "ref", "modelDetails", "modelName", "knowledgeReferences", "kbRefs", "knowledgeBaseId", "indexCollectionName", "kbRef", "toolReferences", "userToolReferences", "toolRefs", "userToolRefs", "userTool", "userToolId", "userToolData", "defaultPrompt", "executePositions", "selectAgentFromCard", "existingOption", "goBack", "testCurrentAgentDetails", "showSuccessMessage", "showApprovalSuccessMessage", "showWarningMessage", "closeSuccessPopup", "editModeStartX", "editModeStartY", "editModeVerticalSpacing", "calculatedPosition", "navigateToAgentsList", "switchAgentType", "newType", "clearAllAgentData", "saveAgentFromTopBar", "_this9", "canSaveAgent", "has<PERSON>rompt", "hasValidName", "hasValidDescription", "toggleAgentDetailsFloater", "onSuccessExecuteAgent", "onSuccessGoToLibrary", "__decorate", "read", "selector", "standalone", "imports", "PopupComponent", "providers", "templateUrl", "styleUrls"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\agents\\build-agents\\build-agents.component.ts"], "sourcesContent": ["import {\r\n  Component,\r\n  <PERSON><PERSON>nit,\r\n  <PERSON><PERSON><PERSON><PERSON>,\r\n  AfterViewInit,\r\n  ChangeDetectorRef,\r\n  ViewChild,\r\n  ViewContainerRef,\r\n} from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport {\r\n  FormsModule,\r\n  ReactiveFormsModule,\r\n  FormBuilder,\r\n  FormGroup,\r\n} from '@angular/forms';\r\nimport { Router, ActivatedRoute } from '@angular/router';\r\nimport {\r\n  ButtonComponent,\r\n  DropdownOption,\r\n  AvaTextboxComponent,\r\n  AvaTextareaComponent,\r\n  IconComponent,\r\n  DialogService,\r\n} from '@ava/play-comp-library';\r\nimport {\r\n  CanvasBoardComponent,\r\n  CanvasNode,\r\n  CanvasEdge,\r\n} from '../../../components/canvas-board/canvas-board.component';\r\nimport {\r\n  BuildAgentNodeComponent,\r\n  BuildAgentNodeData,\r\n  ExecuteNodeData,\r\n} from './components/build-agent-node';\r\n\r\nimport { ChatMessage } from '../../../components/chat-window';\r\nimport { ToolExecutionService } from '../../../services/tool-execution/tool-execution.service';\r\nimport {\r\n  Subscription,\r\n  Subject,\r\n  takeUntil,\r\n  switchMap,\r\n  finalize,\r\n  catchError,\r\n  of,\r\n  Observable,\r\n} from 'rxjs';\r\nimport { PromptsService } from '../../../services/prompts.service';\r\nimport { ToolsService } from '../../../services/tools.service';\r\nimport { AgentServiceService } from '../services/agent-service.service';\r\nimport { TokenStorageService } from '../../../auth/services/token-storage.service';\r\nimport { AgentPlaygroundService } from './services/agent-playground.service';\r\nimport { KnowledgeBaseService } from '../../../services/knowledge-base.service';\r\n\r\nimport { AvaTab } from '../../../models/tab.model';\r\nimport {\r\n  CustomTab,\r\n  CustomTabsComponent,\r\n} from '../../../components/custom-tabs/custom-tabs.component';\r\nimport { PlaygroundComponent } from '../../../components/playground/playground.component';\r\nimport { DrawerService } from '../../../services/drawer/drawer.service';\r\nimport { AgentsPreviewPanelComponent } from './agents-preview-panel/agents-preview-panel.component';\r\nimport { GuardrailsService } from '../../../services/guardrails.service';\r\nimport { ModelService } from '../../../services/model.service';\r\nimport {\r\n  DropZoneCanvasComponent,\r\n  DropZoneConfig,\r\n} from '../../../components/drop-zone-canvas/drop-zone-canvas.component';\r\n\r\ninterface ToolItem {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  icon: string;\r\n  type: 'tool' | 'model' | 'knowledge' | 'prompt' | 'guardrail';\r\n  [key: string]: any;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-build-agents',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    ButtonComponent,\r\n    BuildAgentNodeComponent,\r\n    PlaygroundComponent,\r\n    AvaTextboxComponent,\r\n    AvaTextareaComponent,\r\n    IconComponent,\r\n    PopupComponent,\r\n    CustomTabsComponent,\r\n    DropZoneCanvasComponent,\r\n  ],\r\n  providers: [DialogService],\r\n  templateUrl: './build-agents.component.html',\r\n  styleUrls: ['./build-agents.component.scss'],\r\n})\r\nexport class BuildAgentsComponent implements OnInit, OnDestroy, AfterViewInit {\r\n  @ViewChild(CanvasBoardComponent) canvasBoardComponent!: CanvasBoardComponent;\r\n  @ViewChild('drawerContainer', { read: ViewContainerRef }) drawerContainer!: ViewContainerRef;\r\n\r\n  private destroy$ = new Subject<void>();\r\n  activeTab: string = 'prompts';\r\n  searchQuery: string = '';\r\n  searchForm!: FormGroup;\r\n\r\n  isSidebarCollapsed = false;\r\n  showPreview = false;\r\n  selectedItem: any = null;\r\n  previewData: any = null;\r\n  isLoadingPreview = false;\r\n  canvasNodes: CanvasNode[] = [];\r\n  canvasEdges: CanvasEdge[] = [];\r\n  buildAgentNodes: BuildAgentNodeData[] = [];\r\n  executeNodes: ExecuteNodeData[] = [];\r\n  selectedNodeId: string | null = null;\r\n  currentAgentType = 'individual';\r\n  currentMode: 'create' | 'view' | 'edit' | 'duplicate' = 'create';\r\n  currentAgentId: string | null = null;\r\n  isFieldsDisabled = false;\r\n  isDuplicateMode = false;\r\n  handleEditModeConnections = false;\r\n\r\n  showSuccessPopup = false;\r\n  showErrorPopup = false;\r\n  showWarningPopup = false;\r\n  isApprovalSuccess = false;\r\n  popupMessage = '';\r\n  popupTitle = '';\r\n  executionId!: string;\r\n\r\n  get showApprovalButton(): boolean {\r\n    return false;\r\n  }\r\n  currentAgentDetails: any = null;\r\n  private readonly nodeLimits = {\r\n    individual: { prompt: 1, model: 1, knowledge: -1, guardrail: -1 },\r\n    collaborative: { prompt: 1, model: 1, knowledge: -1, tool: -1 },\r\n  };\r\n\r\n  // Track the latest node for simple vertical positioning\r\n  private latestNodeId: string | null = null;\r\n\r\n  // Drop zone configuration\r\n  get dropZoneConfig(): DropZoneConfig {\r\n    return {\r\n      agentType: this.currentAgentType as 'individual' | 'collaborative',\r\n      allowedCategories: {\r\n        prompts: { max: 1, required: true },\r\n        models: { max: 1, required: true },\r\n        knowledge: { max: -1, required: true }, // -1 means unlimited\r\n        guardrails: { max: -1, required: false },\r\n        tools: { max: -1, required: false },\r\n      },\r\n    };\r\n  }\r\n  tabs: AvaTab[] = [];\r\n  private _customTabs: CustomTab[] = [];\r\n  get customTabs(): CustomTab[] {\r\n    return this._customTabs;\r\n  }\r\n\r\n  allToolItems: { [key: string]: ToolItem[] } = {\r\n    prompts: [],\r\n    models: [],\r\n    knowledge: [],\r\n    tools: [],\r\n    guardrails: [],\r\n  };\r\n\r\n  navigationHints = [\r\n    'Alt + Drag to pan canvas',\r\n    'Mouse wheel to zoom',\r\n    'Space to reset view',\r\n  ];\r\n  isExecuteMode = false;\r\n  showChatInterface = false;\r\n  chatMessages: ChatMessage[] = [];\r\n  isProcessingChat = false;\r\n  promptOptions: DropdownOption[] = [];\r\n  private executionSubscription?: Subscription;\r\n\r\n  isEditMode = false;\r\n  isViewMode = false;\r\n  isNewAgent = true;\r\n  agentConfigIds = new Map<string, number>();\r\n\r\n  // Get dynamic button text based on current mode\r\n  get primaryButtonText(): string {\r\n    if (this.isExecuteMode) {\r\n      return 'Run'; // When in execute mode, show \"Run\"\r\n    }\r\n\r\n    if (this.isViewMode) {\r\n      return 'Execute'; // When viewing existing agent, show \"Execute\"\r\n    }\r\n\r\n    if (this.isEditMode) {\r\n      // For collaborative agents in edit mode, use \"Update and Send Approval\"\r\n      if (this.currentAgentType === 'collaborative') {\r\n        return 'Update and Send Approval';\r\n      }\r\n      return 'Update'; // When editing existing individual agent, show \"Update\"\r\n    }\r\n\r\n    if (this.isDuplicateMode) {\r\n      return 'Save'; // When duplicating agent, show \"Save\"\r\n    }\r\n\r\n    // For collaborative agents in create mode, use \"Save and Send Approval\"\r\n    if (this.currentAgentType === 'collaborative') {\r\n      return 'Save and Send Approval';\r\n    }\r\n\r\n    return 'Save'; // When creating new individual agent, show \"Save\"\r\n  }\r\n\r\n  // Agent data properties for saving\r\n  agentName: string = '';\r\n  agentDetail: string = '';\r\n  agentCode: string = ''; // Store the agent code (e.g., BUGBUSTERCHATBOT) for API calls\r\n\r\n  // Agent Details Floater state\r\n  isAgentDetailsCollapsed: boolean = true;\r\n  agentMetadata: {\r\n    org: string;\r\n    domain: string;\r\n    project: string;\r\n    team: string;\r\n  } = { org: '', domain: '', project: '', team: '' };\r\n\r\n  private buildOrganizationPath(): string {\r\n    const { org, domain, project, team } = this.agentMetadata;\r\n    return org && domain && project && team\r\n      ? `@${org}@${domain}@${project}@${team}`\r\n      : '@ASCENDION@PLATFORM_ENGINEERING@AVA@DIGITAL_ASCENDER';\r\n  }\r\n\r\n  // Cache for labels API response to avoid multiple calls\r\n  private labelsCache: any = null;\r\n\r\n  // Agent Playground Properties\r\n  agentPlaygroundForm!: FormGroup;\r\n  selectedPrompt = 'default';\r\n  selectedAgentMode = '';\r\n  selectedUseCaseIdentifier = '';\r\n  agentChatPayload: any[] = [];\r\n  agentFilesUploadedData: any[] = [];\r\n  agentAttachment: string[] = [];\r\n  isAgentPlaygroundLoading = false;\r\n  agentPlaygroundDestroy = new Subject<boolean>();\r\n\r\n  responseModalConfig = {\r\n    id: 'responseModal',\r\n    type: 'dialog',\r\n    title: 'Agent Response',\r\n    closeButton: false,\r\n    primaryButtonConfig: {\r\n      text: 'OK',\r\n      buttonType: 'primary',\r\n      buttonSize: 'medium',\r\n      imageUrl: '',\r\n      linkURL: '',\r\n    },\r\n  };\r\n  isResponseModalOpen = false;\r\n  modalMessage = '';\r\n  isModalError = false;\r\n  autoSelectedAgentFromCard: any = null;\r\n  testSpaceInput = '';\r\n\r\n  // Track guardrail toggle states\r\n  guardrailToggleStates: { [nodeId: string]: boolean } = {};\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private toolExecutionService: ToolExecutionService,\r\n    private cdr: ChangeDetectorRef,\r\n    private toolsService: ToolsService,\r\n    private agentService: AgentServiceService,\r\n    private promptsService: PromptsService,\r\n    private tokenStorage: TokenStorageService,\r\n    private agentPlaygroundService: AgentPlaygroundService,\r\n    private formBuilder: FormBuilder,\r\n    private knowledgeBaseService: KnowledgeBaseService,\r\n    private drawerService: DrawerService,\r\n    private guardrailsService: GuardrailsService,\r\n    private modelService: ModelService,\r\n    private dialogService: DialogService,\r\n  ) {\r\n    this.agentPlaygroundForm = this.formBuilder.group({\r\n      isConversational: [true],\r\n      isUseTemplate: [false],\r\n    });\r\n    this.searchForm = this.formBuilder.group({ search: [''] });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.executionId = crypto.randomUUID();\r\n    this.route.params.subscribe((params) => {\r\n      this.currentAgentType =\r\n        params['type'] === 'individual' || params['type'] === 'collaborative'\r\n          ? params['type']\r\n          : 'individual';\r\n      this.currentAgentDetails = null;\r\n      this.configureTabsForAgentType();\r\n      this.loadDataForAgentType();\r\n    });\r\n    this.route.queryParams.subscribe((params) => {\r\n      if (params['id']) {\r\n        this.currentAgentId = params['id'];\r\n        const mode = params['mode'];\r\n        this.isViewMode =\r\n          this.isEditMode =\r\n          this.isExecuteMode =\r\n          this.isDuplicateMode =\r\n          this.isNewAgent =\r\n            false;\r\n        this.isFieldsDisabled = false;\r\n\r\n        console.log('🔧 MODE DETECTION:', {\r\n          mode,\r\n          currentAgentId: this.currentAgentId,\r\n          url: this.router.url,\r\n        });\r\n\r\n        if (mode === 'view') {\r\n          this.isViewMode = true;\r\n          this.isFieldsDisabled = true;\r\n        } else if (mode === 'edit') {\r\n          this.isEditMode = true;\r\n          this.handleEditModeConnections = true;\r\n        } else if (mode === 'execute') {\r\n          // Redirect to the new agent execution component\r\n          this.router.navigate(\r\n            ['/build/agents', this.currentAgentType, 'execute'],\r\n            {\r\n              queryParams: { id: params['id'] },\r\n            },\r\n          );\r\n          return;\r\n        } else if (mode === 'duplicate') {\r\n          this.isDuplicateMode = true;\r\n          this.agentConfigIds.clear();\r\n        } else {\r\n          this.isNewAgent = true;\r\n        }\r\n\r\n        console.log('🔧 MODE FLAGS AFTER DETECTION:', {\r\n          isViewMode: this.isViewMode,\r\n          isEditMode: this.isEditMode,\r\n          isExecuteMode: this.isExecuteMode,\r\n          isDuplicateMode: this.isDuplicateMode,\r\n          isNewAgent: this.isNewAgent,\r\n        });\r\n\r\n        this.loadAgentData(params['id']);\r\n      } else {\r\n        this.isNewAgent = true;\r\n        this.isEditMode = this.isViewMode = false;\r\n        this.isFieldsDisabled = false;\r\n        this.currentAgentId = null;\r\n      }\r\n    });\r\n\r\n    this.activeTab = 'prompts';\r\n    this.searchForm\r\n      .get('search')\r\n      ?.valueChanges.pipe(takeUntil(this.destroy$))\r\n      .subscribe((value) => {\r\n        this.searchQuery = value || '';\r\n        this.updateFilteredTools();\r\n      });\r\n    this.updateFilteredTools();\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    this.cdr.detectChanges();\r\n    setTimeout(() => {\r\n      if (this.canvasBoardComponent && this.buildAgentNodes.length > 0) {\r\n        this.canvasBoardComponent.updateNodeConnectionPoints();\r\n        if (this.canvasEdges.length === 0 && this.buildAgentNodes.length > 1) {\r\n          this.createAgentFlowConnections(this.buildAgentNodes);\r\n        }\r\n      }\r\n    }, 300);\r\n    this.drawerService.registerViewContainer(this.drawerContainer);\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n    if (this.executionSubscription) {\r\n      this.executionSubscription.unsubscribe();\r\n    }\r\n    this.agentPlaygroundDestroy.next(true);\r\n    this.agentPlaygroundDestroy.complete();\r\n  }\r\n\r\n  private generateMockItems(\r\n    type: string,\r\n    name: string,\r\n    icon: string,\r\n    count: number,\r\n  ): ToolItem[] {\r\n    return Array.from({ length: count }, (_, i) => ({\r\n      id: `${type}${i + 1}`,\r\n      name,\r\n      icon,\r\n      type: type as any,\r\n      description:\r\n        'AI agents are software programs that use artificial intelligence to perform tasks and achieve goals.',\r\n    }));\r\n  }\r\n\r\n  private loadPrompts(): void {\r\n    this.promptsService.fetchAllPrompts().subscribe({\r\n      next: (prompts: any[]) => {\r\n        const filteredPrompts = this.filterPromptsByAgentType(prompts);\r\n        this.allToolItems['prompts'] = filteredPrompts.map((prompt: any) => ({\r\n          id: prompt.id?.toString() || Math.random().toString(),\r\n          name: prompt.name,\r\n          description: prompt.description || prompt.descriptionConsolidated,\r\n          promptDescription: prompt.promptDescription,\r\n          icon: 'assets/images/prompt.png',\r\n          type: 'prompt',\r\n          promptType: prompt.type,\r\n          role: prompt.role,\r\n          goal: prompt.goal,\r\n          backstory: prompt.backstory,\r\n          expectedOutput: prompt.expectedOutput,\r\n          descriptionConsolidated: prompt.descriptionConsolidated,\r\n          expectedOutputConsolidated: prompt.expectedOutputConsolidated,\r\n          originalPromptData: prompt,\r\n        }));\r\n        this.updateFilteredTools();\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading prompts:', error);\r\n      },\r\n    });\r\n  }\r\n\r\n  private filterPromptsByAgentType(prompts: any[]): any[] {\r\n    if (!Array.isArray(prompts) || prompts.length === 0) return [];\r\n\r\n    if (this.currentAgentType === 'collaborative') {\r\n      return prompts.filter((prompt) => prompt.type?.trim() !== 'free form');\r\n    } else {\r\n      return prompts; // Return all prompts\r\n    }\r\n  }\r\n\r\n  private loadModels(): void {\r\n    if (this.currentAgentType === 'collaborative') {\r\n      this.agentService.getCollaborativeModels().subscribe({\r\n        next: (response: any) => {\r\n          if (response && Array.isArray(response)) {\r\n            this.allToolItems['models'] = response.map((model: any) => ({\r\n              id: model.id,\r\n              name: `${model.modelDeploymentName} (${model.model})`,\r\n              description:\r\n                model.modelDescription ||\r\n                `${model.modelType} model via ${model.aiEngine}`,\r\n              icon: 'assets/images/Boundingbox.png',\r\n              type: 'model' as const,\r\n              model: model.model,\r\n              aiEngine: model.aiEngine,\r\n              modelType: model.modelType,\r\n            }));\r\n          } else if (response && response.models) {\r\n            this.allToolItems['models'] = response.models.map((model: any) => ({\r\n              id: model.id,\r\n              name: model.model,\r\n              description: model.modelDescription,\r\n              icon: 'assets/images/Boundingbox.png',\r\n              type: 'model' as const,\r\n            }));\r\n          }\r\n          this.updateFilteredTools();\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error loading models from direct API:', error);\r\n          this.loadModelsFromLabels();\r\n        },\r\n      });\r\n    } else {\r\n      this.loadModelsFromLabels();\r\n    }\r\n  }\r\n\r\n  private loadModelsFromLabels(): void {\r\n    this.agentService.getModelsFromLabels().subscribe({\r\n      next: (models: any[]) => {\r\n        this.allToolItems['models'] = models.map((model: any) => ({\r\n          id: model.id,\r\n          name: model.name,\r\n          description: model.description,\r\n          icon: model.icon,\r\n          type: 'model' as const,\r\n        }));\r\n        this.updateFilteredTools();\r\n      },\r\n      error: (error: any) => {\r\n        console.error('Error loading models from labels API:', error);\r\n        this.allToolItems['models'] = this.generateMockItems(\r\n          'model',\r\n          'Model Name',\r\n          'assets/images/Boundingbox.png',\r\n          2,\r\n        );\r\n        this.updateFilteredTools();\r\n      },\r\n    });\r\n  }\r\n\r\n  private loadModelsFromCache(): void {\r\n    if (!this.labelsCache) return;\r\n\r\n    const categoryLabels = this.labelsCache.categoryLabels || [];\r\n    const modelCategory = categoryLabels.find(\r\n      (category: any) => category.categoryId === 1,\r\n    );\r\n\r\n    if (modelCategory) {\r\n      const modelLabel = modelCategory.labels.find(\r\n        (label: any) => label.labelCode === 'MODEL',\r\n      );\r\n      if (modelLabel && modelLabel.labelValues) {\r\n        const parsedModels = this.agentService.parseLabelValues(\r\n          modelLabel.labelValues,\r\n        );\r\n        this.allToolItems['models'] = parsedModels.map((option) => ({\r\n          id: option.value,\r\n          name: option.name,\r\n          type: 'model',\r\n          icon: 'assets/images/model.png',\r\n          description: `Model: ${option.name}`,\r\n        }));\r\n        this.updateFilteredTools();\r\n      }\r\n    }\r\n  }\r\n\r\n  private loadKnowledgeBase(): void {\r\n    if (this.currentAgentType === 'collaborative') {\r\n      // Use knowledge base service directly for collaborative agents\r\n      this.knowledgeBaseService.fetchAllKnowledge().subscribe({\r\n        next: (response: any[]) => {\r\n          // Transform CardData format to the expected format for collaborative agents\r\n          this.allToolItems['knowledge'] = response.map((kb: any) => ({\r\n            id: kb.id,\r\n            name:\r\n              kb.collectionName ||\r\n              kb.title ||\r\n              kb.name ||\r\n              'Unknown Knowledge Base',\r\n            description:\r\n              kb.description ||\r\n              `Knowledge Base: ${kb.collectionName || kb.title || kb.name}`,\r\n            icon: 'assets/images/import_contacts.png',\r\n            type: 'knowledge' as const,\r\n            // Add any additional properties if available\r\n            vectorDb: kb.vectorDb,\r\n            modelRef: kb.modelRef,\r\n            splitSize: kb.splitSize,\r\n          }));\r\n          this.updateFilteredTools();\r\n        },\r\n        error: (error) => {\r\n          console.error(\r\n            'Error loading knowledge bases from knowledge base service:',\r\n            error,\r\n          );\r\n          // For collaborative agents, don't fallback to labels API\r\n          // Just use empty array or mock data\r\n          this.allToolItems['knowledge'] = this.generateMockItems(\r\n            'knowledge',\r\n            'Knowledge Base Name',\r\n            'assets/images/import_contacts.png',\r\n            2,\r\n          );\r\n          this.updateFilteredTools();\r\n        },\r\n      });\r\n    } else {\r\n      this.loadKnowledgeBaseFromLabels();\r\n    }\r\n  }\r\n\r\n  private loadKnowledgeBaseFromLabels(): void {\r\n    this.agentService.getKnowledgeBasesFromLabels().subscribe({\r\n      next: (knowledgeItems) => {\r\n        this.allToolItems['knowledge'] = knowledgeItems.map((item: any) => ({\r\n          id: item.id,\r\n          name: item.name,\r\n          description: item.description,\r\n          icon: item.icon,\r\n          type: 'knowledge' as const,\r\n        }));\r\n        this.updateFilteredTools();\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading knowledge bases from labels API:', error);\r\n        // Fallback to mock data if API fails\r\n        this.allToolItems['knowledge'] = this.generateMockItems(\r\n          'knowledge',\r\n          'Knowledge Base Name',\r\n          'assets/images/import_contacts.png',\r\n          2,\r\n        );\r\n        this.updateFilteredTools();\r\n      },\r\n    });\r\n  }\r\n\r\n  // Load knowledge bases from cached labels data\r\n  private loadKnowledgeBaseFromCache(): void {\r\n    if (!this.labelsCache) return;\r\n\r\n    const categoryLabels = this.labelsCache.categoryLabels || [];\r\n    const iclCategory = categoryLabels.find(\r\n      (category: any) => category.categoryId === 2,\r\n    );\r\n\r\n    if (iclCategory) {\r\n      const knowledgeLabel = iclCategory.labels.find(\r\n        (label: any) => label.labelCode === 'RAG_KNOWLEDGEBASE_NAME',\r\n      );\r\n      if (knowledgeLabel && knowledgeLabel.labelValues) {\r\n        const parsedKnowledge = this.agentService.parseLabelValues(\r\n          knowledgeLabel.labelValues,\r\n        );\r\n        this.allToolItems['knowledge'] = parsedKnowledge.map((option) => ({\r\n          id: option.value, // This is the actual knowledge base ID we need for payload\r\n          name: option.name,\r\n          type: 'knowledge',\r\n          icon: 'assets/images/knowledge.png',\r\n          description: `Knowledge Base: ${option.name}`,\r\n        }));\r\n      }\r\n    }\r\n  }\r\n\r\n  private loadTools(): void {\r\n    // Load both built-in and user-defined tools\r\n    const builtInTools$ = this.toolsService.getBuiltInToolsList();\r\n\r\n    // Load built-in tools first\r\n    builtInTools$.subscribe({\r\n      next: (response: any) => {\r\n        const builtInTools = response.tools || [];\r\n        const builtInToolItems = builtInTools.map((tool: any) => ({\r\n          id: `builtin-${tool.toolId}`,\r\n          name: tool.toolName || 'Unknown Built-in Tool',\r\n          description: 'Built-in tool for AI agent tasks',\r\n          icon: 'assets/images/build.png',\r\n          type: 'tool',\r\n        }));\r\n\r\n        // Then load user-defined tools with dynamic records count\r\n        this.loadUserToolsWithPagination(builtInToolItems);\r\n      },\r\n      error: (error: any) => {\r\n        console.error('Error loading built-in tools:', error);\r\n        // Still try to load user tools even if built-in tools fail\r\n        this.loadUserToolsWithPagination([]);\r\n      },\r\n    });\r\n  }\r\n\r\n  private loadUserToolsWithPagination(builtInToolItems: any[]): void {\r\n    // First, get the total count by making an initial API call\r\n    this.toolsService.getUserToolsList(1, 11).subscribe({\r\n      next: (userResponse: any) => {\r\n        const totalRecords = userResponse.totalNoOfRecords || 51;\r\n\r\n        // Now load all user tools using the total count\r\n        this.toolsService.getUserToolsList(1, totalRecords).subscribe({\r\n          next: (fullUserResponse: any) => {\r\n            const userTools = fullUserResponse.userToolDetails || [];\r\n            const userToolItems = userTools.map((tool: any) => ({\r\n              id: `user-${tool.id}`,\r\n              name: tool.name || 'Unknown User Tool',\r\n              description:\r\n                tool.description || 'User-defined tool for AI agent tasks',\r\n              icon: 'assets/images/build.png',\r\n              type: 'tool',\r\n            }));\r\n\r\n            // Combine both built-in and user-defined tools\r\n            this.allToolItems['tools'] = [\r\n              ...builtInToolItems,\r\n              ...userToolItems,\r\n            ];\r\n            this.updateFilteredTools();\r\n          },\r\n          error: (error) => {\r\n            console.error('Error loading full user tools list:', error);\r\n            // Use only built-in tools if user tools fail\r\n            this.allToolItems['tools'] = builtInToolItems;\r\n            this.updateFilteredTools();\r\n          },\r\n        });\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading user tools:', error);\r\n        // Use only built-in tools if user tools fail\r\n        this.allToolItems['tools'] = builtInToolItems;\r\n        this.updateFilteredTools();\r\n      },\r\n    });\r\n  }\r\n\r\n  private loadGuardrails(): void {\r\n    this.agentService.getGuardrailsFromLabels().subscribe({\r\n      next: (guardrails: any[]) => {\r\n        this.allToolItems['guardrails'] = guardrails.map((guardrail: any) => ({\r\n          id: guardrail.id,\r\n          name: guardrail.name,\r\n          description: guardrail.description,\r\n          icon: guardrail.icon,\r\n          type: 'guardrail' as const,\r\n          code: guardrail.code,\r\n        }));\r\n        this.updateFilteredTools();\r\n        this.updateFilteredTools();\r\n      },\r\n      error: (error: any) => {\r\n        console.error('Error loading guardrails from labels API:', error);\r\n        // Fallback to mock data if API fails\r\n        this.allToolItems['guardrails'] = this.generateMockItems(\r\n          'guardrail',\r\n          'Guardrail Name',\r\n          'assets/images/swords.png',\r\n          2,\r\n        );\r\n        this.updateFilteredTools();\r\n      },\r\n    });\r\n  }\r\n\r\n  // Load guardrails from cached labels data\r\n  private loadGuardrailsFromCache(): void {\r\n    if (!this.labelsCache) return;\r\n\r\n    const categoryLabels = this.labelsCache.categoryLabels || [];\r\n    const otherCategory = categoryLabels.find(\r\n      (category: any) => category.categoryId === 3,\r\n    );\r\n\r\n    if (otherCategory) {\r\n      // Include ALL toggle labels from Other category, including \"Enable Guardrails\"\r\n      this.allToolItems['guardrails'] = otherCategory.labels\r\n        .filter((label: any) => label.labelType === 'Toggle') // Include all toggle types\r\n        .map((label: any) => ({\r\n          id: label.labelId.toString(),\r\n          name: label.labelName,\r\n          code: label.labelCode,\r\n          type: 'guardrail' as const,\r\n          icon: 'assets/images/guardrail.png',\r\n          description: label.labelInfo || `Guardrail: ${label.labelName}`,\r\n        }));\r\n    }\r\n  }\r\n\r\n  private _filteredTools: ToolItem[] = [];\r\n\r\n  get currentTabTools(): ToolItem[] {\r\n    return this.allToolItems[this.activeTab] || [];\r\n  }\r\n  get filteredTools(): ToolItem[] {\r\n    return this._filteredTools;\r\n  }\r\n\r\n  private updateFilteredTools(): void {\r\n    const currentTools = this.currentTabTools;\r\n    const availableTools = currentTools.filter((tool) => {\r\n      const isAlreadyAdded = this.buildAgentNodes.some(\r\n        (node) => node.originalToolData && node.originalToolData.id === tool.id,\r\n      );\r\n      return !isAlreadyAdded;\r\n    });\r\n\r\n    if (!this.searchQuery) {\r\n      this._filteredTools = availableTools;\r\n      return;\r\n    }\r\n\r\n    const query = this.searchQuery.toLowerCase();\r\n    this._filteredTools = availableTools.filter(\r\n      (tool) =>\r\n        (tool.name && tool.name.toLowerCase().includes(query)) ||\r\n        (tool.description && tool.description.toLowerCase().includes(query)),\r\n    );\r\n  }\r\n\r\n  onTabChange(tabValue: string | number): void {\r\n    this.activeTab = tabValue.toString();\r\n    this.searchQuery = '';\r\n    this.searchForm.get('search')?.setValue('', { emitEvent: false });\r\n    this.updateFilteredTools();\r\n  }\r\n\r\n  onSearchChange(query: string): void {\r\n    this.searchQuery = query;\r\n    this.updateFilteredTools();\r\n  }\r\n\r\n  onCustomTabChange(tabValue: string | number): void {\r\n    this.activeTab = tabValue.toString();\r\n    this.searchQuery = '';\r\n    this.searchForm.get('search')?.setValue('', { emitEvent: false });\r\n    this.updateFilteredTools();\r\n  }\r\n\r\n  toggleSidebar(): void {\r\n    this.isSidebarCollapsed = !this.isSidebarCollapsed;\r\n  }\r\n  isActiveCustomTab(tabValue: string | number): boolean {\r\n    return this.activeTab === tabValue.toString();\r\n  }\r\n\r\n  getTabIcon(tab: any): string {\r\n    const iconMap: { [key: string]: string } = {\r\n      prompts: 'FileText',\r\n      models: 'Box',\r\n      knowledge: 'BookOpen',\r\n      guardrails: 'Swords',\r\n      tools: 'Wrench',\r\n    };\r\n    return iconMap[tab.value] || 'Circle';\r\n  }\r\n\r\n  private handleAutoTabSwitch(nodeType: string): void {\r\n    const currentLimits =\r\n      this.nodeLimits[this.currentAgentType as keyof typeof this.nodeLimits];\r\n    const currentNodeLimit =\r\n      currentLimits[nodeType as keyof typeof currentLimits];\r\n\r\n    if (currentNodeLimit === 1) {\r\n      const nextTab = this.getNextRequiredTab(nodeType);\r\n      if (nextTab) {\r\n        this.activeTab = nextTab;\r\n        this.searchQuery = '';\r\n        this.searchForm.get('search')?.setValue('', { emitEvent: false });\r\n        this.updateFilteredTools();\r\n      }\r\n    }\r\n  }\r\n\r\n  private getNextRequiredTab(currentNodeType: string): string | null {\r\n    const currentLimits =\r\n      this.nodeLimits[this.currentAgentType as keyof typeof this.nodeLimits];\r\n\r\n    if (currentNodeType === 'prompt') {\r\n      const hasModel = this.buildAgentNodes.some(\r\n        (node) => node.type === 'model',\r\n      );\r\n      if (!hasModel && currentLimits['model'] === 1) {\r\n        return 'models';\r\n      }\r\n    } else if (currentNodeType === 'model') {\r\n      return 'knowledge';\r\n    }\r\n\r\n    return null;\r\n  }\r\n\r\n  onItemPreview(item: ToolItem): void {\r\n    this.selectedItem = item;\r\n    this.showPreview = true;\r\n    this.loadPreviewData(item);\r\n    this.drawerService.open(AgentsPreviewPanelComponent, {\r\n      previewData: this.previewData,\r\n      closePreview: () => this.drawerService.clear(),\r\n    });\r\n  }\r\n\r\n  closePreview(): void {\r\n    this.showPreview = false;\r\n    this.selectedItem = null;\r\n    this.previewData = null;\r\n  }\r\n\r\n  private loadPreviewData(item: ToolItem): void {\r\n    this.previewData = {\r\n      type: item.type,\r\n      title: item.name,\r\n      data: { ...item },\r\n      loading: true,\r\n      error: null,\r\n    };\r\n\r\n    // Fetch detailed data based on item type\r\n    switch (item.type) {\r\n      case 'prompt':\r\n        this.loadPromptDetails(item);\r\n        break;\r\n      case 'model':\r\n        this.loadModelDetails(item);\r\n        break;\r\n      case 'knowledge':\r\n        this.loadKnowledgeDetails(item);\r\n        break;\r\n      case 'guardrail':\r\n        this.loadGuardrailDetails(item);\r\n        break;\r\n      case 'tool':\r\n        this.loadToolDetails(item);\r\n        break;\r\n      default:\r\n        this.previewData.loading = false;\r\n    }\r\n  }\r\n\r\n  private loadPromptDetails(item: ToolItem): void {\r\n    if (item.id) {\r\n      this.promptsService.getPromptById(item.id.toString()).subscribe({\r\n        next: (response: any) => {\r\n          this.previewData.data = {\r\n            ...this.previewData.data,\r\n            ...response,\r\n            description:\r\n              response.description ||\r\n              response.descriptionConsolidated ||\r\n              item.description,\r\n            promptTask:\r\n              response.template ||\r\n              response.content ||\r\n              response.promptTemplate ||\r\n              response.prompt,\r\n            createdBy: response.createdBy || response.author || 'Unknown',\r\n            updatedAt:\r\n              response.updatedAt ||\r\n              response.createdAt ||\r\n              new Date().toISOString(),\r\n            role: response.role,\r\n            goal: response.goal,\r\n            backstory: response.backstory,\r\n            expectedOutput:\r\n              response.expectedOutput || response.expectedOutputConsolidated,\r\n          };\r\n          this.previewData.loading = false;\r\n        },\r\n        error: (error) => {\r\n          console.error('Error loading prompt details:', error);\r\n          this.previewData.error = 'Failed to load prompt details';\r\n          this.previewData.loading = false;\r\n        },\r\n      });\r\n    } else {\r\n      this.previewData.loading = false;\r\n    }\r\n  }\r\n\r\n  private loadModelDetails(item: ToolItem): void {\r\n    if (item.id && this.modelService?.getOneModeById) {\r\n      this.modelService.getOneModeById(item.id.toString()).subscribe({\r\n        next: (response: any) => {\r\n          const modelData = response;\r\n          this.previewData.data = {\r\n            ...this.previewData.data,\r\n            modelDescription: modelData.modelDescription || item.description,\r\n            modelType: modelData.model || item.name,\r\n            temperature: modelData.temperature || 0.7,\r\n            maxToken: modelData.maxToken || 4000,\r\n            topP: modelData.topP || 0.95,\r\n            maxIteration: modelData.maxIteration || 1,\r\n            aiEngine: modelData.aiEngine || 'OpenAI',\r\n            baseUrl: modelData.serviceUrl || modelData.baseurl,\r\n            llmDeploymentName: modelData.modelDeploymentName || item.name,\r\n            apiKey: modelData.apiKeyEncoded || modelData.awsSecretKey,\r\n            apiVersion: modelData.apiVersion,\r\n            headerName: modelData.headerName || '',\r\n            model: modelData.model || '',\r\n            createdBy: modelData.createdBy || 'Unknown',\r\n            createdOn: modelData.date || new Date().toISOString(),\r\n          };\r\n          this.previewData.loading = false;\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error loading model details:', error);\r\n          this.setDefaultModelData(item);\r\n        },\r\n      });\r\n    } else {\r\n      this.setDefaultModelData(item);\r\n    }\r\n  }\r\n\r\n  private setDefaultModelData(item: ToolItem): void {\r\n    this.previewData.data = {\r\n      ...this.previewData.data,\r\n      modelDescription: item.description || `Model: ${item.name}`,\r\n      modelType: item.name,\r\n      temperature: 0.7,\r\n      maxToken: 4000,\r\n      topP: 0.95,\r\n      maxIteration: 1,\r\n      aiEngine: 'OpenAI',\r\n      baseUrl: '',\r\n      llmDeploymentName: item.name,\r\n      apiKey: '***',\r\n      apiVersion: '2024-02-15-preview',\r\n      createdBy: 'System',\r\n      createdOn: new Date().toISOString(),\r\n    };\r\n    this.previewData.loading = false;\r\n  }\r\n\r\n  private loadKnowledgeDetails(item: ToolItem): void {\r\n    if (item.id) {\r\n      this.knowledgeBaseService\r\n        .getKnowledgeBaseById(item.id.toString())\r\n        .subscribe({\r\n          next: (response: any) => {\r\n            this.previewData.data = {\r\n              ...this.previewData.data,\r\n              name: item.name,\r\n              description: item.description,\r\n              embeddingModel:\r\n                response.files?.[0]?.modelDeploymentName ||\r\n                'text-embedding-ada-002',\r\n              splitSize: 0.5,\r\n              uploadType: 'Vector Database',\r\n              retrieverType: response.retrieverType || 'normal',\r\n              files: response.files || [],\r\n              fileCount: response.files?.length || 0,\r\n              totalSize:\r\n                response.files?.reduce(\r\n                  (sum: number, file: any) => sum + (file.fileSizeBytes || 0),\r\n                  0,\r\n                ) || 0,\r\n              createdBy: response.files?.[0]?.uploadedBy || 'Unknown',\r\n              createdDate:\r\n                response.files?.[0]?.uploadDate || new Date().toISOString(),\r\n            };\r\n            this.previewData.loading = false;\r\n          },\r\n          error: (error: any) => {\r\n            console.error('Error loading knowledge base details:', error);\r\n            this.setDefaultKnowledgeData(item);\r\n          },\r\n        });\r\n    }\r\n  }\r\n\r\n  private setDefaultKnowledgeData(item: ToolItem): void {\r\n    this.previewData.data = {\r\n      ...this.previewData.data,\r\n      name: item.name,\r\n      description: item.description || `Knowledge Base: ${item.name}`,\r\n      embeddingModel: 'text-embedding-ada-002',\r\n      splitSize: 0.5,\r\n      uploadType: 'Vector Database',\r\n      files: [],\r\n      createdBy: 'System',\r\n      createdDate: new Date().toISOString(),\r\n    };\r\n    this.previewData.loading = false;\r\n  }\r\n\r\n  private loadGuardrailDetails(item: ToolItem): void {\r\n    if (item.name && this.guardrailsService?.getGuardrailByName) {\r\n      this.guardrailsService.getGuardrailByName(item.name).subscribe({\r\n        next: (guardrail: any) => {\r\n          if (guardrail) {\r\n            this.previewData.data = {\r\n              ...this.previewData.data,\r\n              name: guardrail.name || item.name,\r\n              description: guardrail.description || item.description,\r\n              content: guardrail.content,\r\n              yamlContent: guardrail.yamlContent || '',\r\n              organization: guardrail.organization || 'Ascendion',\r\n              configKey: guardrail.configKey || '',\r\n              chatBot: guardrail.chatBot || false,\r\n              createdBy: guardrail.createdBy || 'Unknown',\r\n              createdDate:\r\n                guardrail.createdDate ||\r\n                guardrail.createdOn ||\r\n                new Date().toISOString(),\r\n            };\r\n          }\r\n          this.previewData.loading = false;\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error loading guardrail details:', error);\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  private setDefaultGuardrailData(item: ToolItem): void {\r\n    this.previewData.data = {\r\n      ...this.previewData.data,\r\n      name: item.name,\r\n      description: item.description || `Guardrail: ${item.name}`,\r\n      content: '# Guardrail configuration\\n# Add your Colang rules here',\r\n      createdBy: 'System',\r\n      createdDate: new Date().toISOString(),\r\n    };\r\n    this.previewData.loading = false;\r\n  }\r\n\r\n  private loadToolDetails(item: ToolItem): void {\r\n    if (item.id && this.toolsService.getUserToolDetails) {\r\n      // Extract numeric ID from string like \"user-423\"\r\n      let toolId: number;\r\n\r\n      if (typeof item.id === 'string' && item.id.startsWith('user-')) {\r\n        // Extract number from \"user-423\" format\r\n        const numericPart = item.id.replace('user-', '');\r\n        toolId = Number(numericPart);\r\n      } else {\r\n        toolId = Number(item.id);\r\n      }\r\n\r\n      if (isNaN(toolId)) {\r\n        console.warn('Invalid tool ID:', item.id, 'Using fallback data');\r\n        this.previewData.loading = false;\r\n        return;\r\n      }\r\n\r\n      this.toolsService.getUserToolDetails(toolId).subscribe({\r\n        next: (response: any) => {\r\n          console.log('Tool details response:', response); // Debug log\r\n\r\n          // Handle different response structures\r\n          let toolDetail, toolConfigs;\r\n\r\n          if (response.userToolDetail) {\r\n            // New API format\r\n            toolDetail = response.userToolDetail;\r\n            toolConfigs = toolDetail.toolConfigs || {};\r\n          } else if (response.tools && response.tools[0]) {\r\n            // Old API format\r\n            const tool = response.tools[0];\r\n            toolDetail = {\r\n              id: tool.toolId,\r\n              name: tool.toolName,\r\n              description: tool.toolDescription,\r\n              createdBy: tool.createdBy,\r\n              createdAt: tool.createTimestamp,\r\n              modifiedAt: tool.updateTimestamp,\r\n              isDeleted: !tool.isApproved,\r\n            };\r\n            toolConfigs = {\r\n              tool_class_name: [tool.toolClassName],\r\n              tool_class_def: [tool.toolClassDef],\r\n            };\r\n          } else {\r\n            // Fallback\r\n            toolDetail = response;\r\n            toolConfigs = {};\r\n          }\r\n\r\n          // Extract functionality/code from various possible fields\r\n          let functionality = 'Tool code not found';\r\n          if (toolConfigs.tool_class_def && toolConfigs.tool_class_def[0]) {\r\n            functionality = toolConfigs.tool_class_def[0];\r\n          } else if (toolDetail.toolClassDef) {\r\n            functionality = toolDetail.toolClassDef;\r\n          } else if (toolDetail.code) {\r\n            functionality = toolDetail.code;\r\n          } else if (toolDetail.definition) {\r\n            functionality = toolDetail.definition;\r\n          }\r\n\r\n          this.previewData.data = {\r\n            ...this.previewData.data,\r\n            id: toolDetail.id,\r\n            name: toolDetail.name || item.name,\r\n            description: toolDetail.description || item.description,\r\n            className:\r\n              toolConfigs.tool_class_name?.[0] ||\r\n              toolDetail.toolClassName ||\r\n              'Unknown',\r\n            functionality: functionality,\r\n            isApproved: !toolDetail.isDeleted,\r\n            createdBy: toolDetail.createdBy || 'Unknown',\r\n            createdOn:\r\n              toolDetail.createdAt ||\r\n              toolDetail.createTimestamp ||\r\n              new Date().toISOString(),\r\n            modifiedBy: toolDetail.modifiedBy,\r\n            modifiedAt: toolDetail.modifiedAt || toolDetail.updateTimestamp,\r\n            isDeleted: toolDetail.isDeleted || false,\r\n          };\r\n          this.previewData.loading = false;\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error loading tool details:', error);\r\n          this.previewData.error = 'Failed to load tool details';\r\n          this.previewData.loading = false;\r\n        },\r\n      });\r\n    } else {\r\n      this.previewData.loading = false;\r\n    }\r\n  }\r\n\r\n  getIconForType(type: string): string {\r\n    const iconMap: { [key: string]: string } = {\r\n      prompt: 'FileText',\r\n      model: 'assets/images/deployed_code.png',\r\n      knowledge: 'assets/images/import_contacts.png',\r\n      tool: 'assets/images/build.png',\r\n      guardrail: 'assets/images/swords.png',\r\n    };\r\n    return iconMap[type] || 'assets/images/build.png'; // Default to tool icon\r\n  }\r\n\r\n  getAdditionalFields(data: any): { key: string; value: any }[] {\r\n    if (!data) return [];\r\n\r\n    const excludeFields = [\r\n      'id',\r\n      'name',\r\n      'description',\r\n      'labelCode',\r\n      'categoryName',\r\n      'categoryId',\r\n      'labelInfo',\r\n    ];\r\n    const additionalFields: { key: string; value: any }[] = [];\r\n\r\n    Object.keys(data).forEach((key) => {\r\n      if (\r\n        !excludeFields.includes(key) &&\r\n        data[key] !== null &&\r\n        data[key] !== undefined\r\n      ) {\r\n        additionalFields.push({\r\n          key: this.formatFieldName(key),\r\n          value: data[key],\r\n        });\r\n      }\r\n    });\r\n\r\n    return additionalFields;\r\n  }\r\n\r\n  private formatFieldName(fieldName: string): string {\r\n    return fieldName\r\n      .replace(/([A-Z])/g, ' $1')\r\n      .replace(/^./, (str) => str.toUpperCase())\r\n      .trim();\r\n  }\r\n\r\n  private configureTabsForAgentType(): void {\r\n    if (this.currentAgentType === 'individual') {\r\n      this.tabs = [\r\n        { id: 'prompts', label: 'Prompts', iconName: 'FileText' },\r\n        { id: 'models', label: 'Models', iconName: 'Box' },\r\n        { id: 'knowledge', label: 'Knowledge Base', iconName: 'BookOpen' },\r\n        { id: 'guardrails', label: 'Guardrails', iconName: 'Swords' },\r\n      ];\r\n    } else {\r\n      this.tabs = [\r\n        { id: 'prompts', label: 'Prompts', iconName: 'FileText' },\r\n        { id: 'models', label: 'Models', iconName: 'Box' },\r\n        { id: 'knowledge', label: 'Knowledge Base', iconName: 'BookOpen' },\r\n        { id: 'tools', label: 'Tools', iconName: 'Wrench' },\r\n      ];\r\n    }\r\n\r\n    this._customTabs = this.tabs.map((tab) => ({\r\n      label: tab.label || '',\r\n      value: tab.id,\r\n      icon: tab.iconName || '',\r\n      disabled: false,\r\n    }));\r\n\r\n    this.activeTab = 'models';\r\n  }\r\n\r\n  private canAddNodeOfType(nodeType: string): boolean {\r\n    const currentLimits =\r\n      this.nodeLimits[this.currentAgentType as keyof typeof this.nodeLimits];\r\n    const limit = currentLimits[nodeType as keyof typeof currentLimits];\r\n\r\n    if (limit === undefined || limit === -1) return true;\r\n\r\n    const currentCount = this.buildAgentNodes.filter(\r\n      (node) => node.type === nodeType,\r\n    ).length;\r\n    return currentCount < limit;\r\n  }\r\n\r\n  private getNodeLimit(nodeType: string): number {\r\n    const currentLimits =\r\n      this.nodeLimits[this.currentAgentType as keyof typeof this.nodeLimits];\r\n    return currentLimits[nodeType as keyof typeof currentLimits] || -1;\r\n  }\r\n\r\n  private removeItemFromList(tool: any): void {\r\n    let tabType: string;\r\n    if (tool.type === 'guardrail') {\r\n      tabType = 'guardrails';\r\n    } else if (tool.type === 'knowledge') {\r\n      tabType = 'knowledge';\r\n    } else {\r\n      tabType = `${tool.type}s`;\r\n    }\r\n    if (this.allToolItems[tabType]) {\r\n      this.allToolItems[tabType] = this.allToolItems[tabType].filter(\r\n        (item) => item.id !== tool.id,\r\n      );\r\n    }\r\n  }\r\n\r\n  private addItemBackToList(node: BuildAgentNodeData): void {\r\n    let tabType: string;\r\n    if (node.type === 'guardrail') {\r\n      tabType = 'guardrails';\r\n    } else if (node.type === 'knowledge') {\r\n      tabType = 'knowledge';\r\n    } else {\r\n      tabType = `${node.type}s`;\r\n    }\r\n    if (this.allToolItems[tabType]) {\r\n      const originalToolId = node.originalToolData?.id;\r\n      const existingItem = this.allToolItems[tabType].find(\r\n        (item) =>\r\n          item.name === node.name ||\r\n          item.id === node.id ||\r\n          (originalToolId && item.id === originalToolId),\r\n      );\r\n\r\n      if (!existingItem) {\r\n        let toolItem: ToolItem;\r\n        if (node.originalToolData) {\r\n          toolItem = { ...node.originalToolData };\r\n        } else {\r\n          toolItem = {\r\n            id: node.id,\r\n            name: node.name,\r\n            description: `${node.type} component: ${node.name}`,\r\n            icon: node.icon || this.getIconForType(node.type),\r\n            type: node.type,\r\n          };\r\n        }\r\n        this.allToolItems[tabType].push(toolItem);\r\n      }\r\n    }\r\n  }\r\n\r\n  isTabRequired(tabValue: string): boolean {\r\n    const nodeType =\r\n      tabValue === 'guardrails' ? 'guardrail' : tabValue.slice(0, -1);\r\n    const currentLimits =\r\n      this.nodeLimits[this.currentAgentType as keyof typeof this.nodeLimits];\r\n    const limit = currentLimits[nodeType as keyof typeof currentLimits];\r\n    return limit === 1;\r\n  }\r\n\r\n  validateMandatoryComponents(): {\r\n    isValid: boolean;\r\n    missingComponents: string[];\r\n  } {\r\n    const missingComponents: string[] = [];\r\n    const currentLimits =\r\n      this.nodeLimits[this.currentAgentType as keyof typeof this.nodeLimits];\r\n\r\n    Object.entries(currentLimits).forEach(([nodeType, limit]) => {\r\n      if (limit === 1) {\r\n        const hasComponent = this.buildAgentNodes.some(\r\n          (node) => node.type === nodeType,\r\n        );\r\n        if (!hasComponent) {\r\n          missingComponents.push(nodeType);\r\n        }\r\n      }\r\n    });\r\n\r\n    return { isValid: missingComponents.length === 0, missingComponents };\r\n  }\r\n\r\n  private loadDataForAgentType(): void {\r\n    this.allToolItems = {\r\n      prompts: [],\r\n      models: [],\r\n      knowledge: [],\r\n      tools: [],\r\n      guardrails: [],\r\n    };\r\n    this.loadLabelsAndData();\r\n  }\r\n\r\n  private loadLabelsAndData(): void {\r\n    if (this.currentAgentType === 'collaborative') {\r\n      this.loadPrompts();\r\n      this.loadModels();\r\n      this.loadKnowledgeBase();\r\n      this.loadTools();\r\n    } else {\r\n      this.agentService.getLabels().subscribe({\r\n        next: (response: any) => {\r\n          this.labelsCache = response;\r\n          this.loadPrompts();\r\n          this.loadModelsFromCache();\r\n          this.loadKnowledgeBaseFromCache();\r\n          this.loadGuardrailsFromCache();\r\n        },\r\n        error: (error) => {\r\n          console.error('Error loading labels API:', error);\r\n          this.loadPrompts();\r\n          this.loadModels();\r\n          this.loadKnowledgeBase();\r\n          this.loadGuardrails();\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  getBuilderTitle(): string {\r\n    return this.currentAgentType === 'individual'\r\n      ? 'Individual Agent Builder'\r\n      : 'Collaborative Agent Builder';\r\n  }\r\n\r\n  onDragStart(event: DragEvent, tool: ToolItem): void {\r\n    if (event.dataTransfer) {\r\n      event.dataTransfer.setData('application/reactflow', JSON.stringify(tool));\r\n      event.dataTransfer.effectAllowed = 'move';\r\n    }\r\n  }\r\n\r\n  onCanvasDropped(event: {\r\n    event: DragEvent;\r\n    position: { x: number; y: number };\r\n  }): void {\r\n    const toolData = event.event.dataTransfer?.getData('application/reactflow');\r\n    if (!toolData) return;\r\n\r\n    try {\r\n      const tool = JSON.parse(toolData);\r\n\r\n      if (!this.canAddNodeOfType(tool.type)) {\r\n        const limit = this.getNodeLimit(tool.type);\r\n        if (limit === 1) {\r\n          const existingNode = this.buildAgentNodes.find(\r\n            (node) => node.type === tool.type,\r\n          );\r\n          if (existingNode) {\r\n            this.onDeleteNode(existingNode.id);\r\n          }\r\n        } else {\r\n          this.showErrorMessage(\r\n            'Node Limit Reached',\r\n            `You can only add ${limit} ${tool.type} node(s) for ${this.currentAgentType} agents.`,\r\n          );\r\n          return;\r\n        }\r\n      }\r\n\r\n      console.log('🚀 onCanvasDropped - Starting node creation:', {\r\n        toolType: tool.type,\r\n        toolName: tool.name,\r\n        dropPosition: event.position,\r\n        currentNodesCount: this.buildAgentNodes.length,\r\n      });\r\n\r\n      const autoPosition = this.calculateAutoPosition(tool.type);\r\n      console.log('🚀 Auto position calculated:', autoPosition);\r\n\r\n      let nodeName = tool.name;\r\n      if (tool.type === 'prompt') {\r\n        if (this.currentAgentType === 'collaborative') {\r\n          nodeName = tool.goal || tool.name;\r\n        } else {\r\n          nodeName = tool.prompt || tool.name;\r\n        }\r\n      }\r\n\r\n      const newNodeId = this.generateNodeId();\r\n      const buildAgentNode: BuildAgentNodeData = {\r\n        id: newNodeId,\r\n        name: nodeName,\r\n        icon: tool.type === 'prompt' ? tool.icon : undefined,\r\n        type: tool.type,\r\n        position: autoPosition,\r\n        originalToolData: tool,\r\n      };\r\n\r\n      console.log('🚀 Created buildAgentNode:', {\r\n        id: buildAgentNode.id,\r\n        type: buildAgentNode.type,\r\n        position: buildAgentNode.position,\r\n      });\r\n\r\n      const nodeWidth = this.isExecuteMode ? 55 : 90;\r\n      const newCanvasNode: CanvasNode = {\r\n        id: buildAgentNode.id,\r\n        type: 'build-agent',\r\n        data: { ...buildAgentNode, width: nodeWidth },\r\n        position: autoPosition,\r\n      };\r\n\r\n      console.log('🚀 Created newCanvasNode:', {\r\n        id: newCanvasNode.id,\r\n        position: newCanvasNode.position,\r\n      });\r\n\r\n      this.buildAgentNodes = [...this.buildAgentNodes, buildAgentNode];\r\n      this.canvasNodes = [...this.canvasNodes, newCanvasNode];\r\n\r\n      console.log('🚀 Arrays updated:', {\r\n        buildAgentNodesLength: this.buildAgentNodes.length,\r\n        canvasNodesLength: this.canvasNodes.length,\r\n        lastBuildAgentNode:\r\n          this.buildAgentNodes[this.buildAgentNodes.length - 1],\r\n        lastCanvasNode: this.canvasNodes[this.canvasNodes.length - 1],\r\n      });\r\n\r\n      // CRITICAL: Check if positions match\r\n      const lastBuildAgent =\r\n        this.buildAgentNodes[this.buildAgentNodes.length - 1];\r\n      const lastCanvas = this.canvasNodes[this.canvasNodes.length - 1];\r\n      console.log('🔍 Position verification:', {\r\n        buildAgentPosition: lastBuildAgent?.position,\r\n        canvasNodePosition: lastCanvas?.position,\r\n        canvasNodeDataPosition: lastCanvas?.data?.position,\r\n        positionsMatch:\r\n          JSON.stringify(lastBuildAgent?.position) ===\r\n          JSON.stringify(lastCanvas?.position),\r\n      });\r\n\r\n      // Track this as the latest node for next positioning\r\n      this.latestNodeId = buildAgentNode.id;\r\n      console.log('🚀 Latest node ID set to:', this.latestNodeId);\r\n\r\n      // Simple connections and cleanup\r\n      this.createAgentFlowConnections(this.buildAgentNodes);\r\n      this.removeItemFromList(tool);\r\n      this.updateFilteredTools();\r\n      this.cdr.detectChanges();\r\n\r\n      console.log(\r\n        '🚀 Setting up force positioning timeout for:',\r\n        buildAgentNode.id,\r\n      );\r\n\r\n      // FORCE correct positioning after DOM update AND fix connections\r\n      setTimeout(() => {\r\n        console.log(\r\n          '🚀 Force positioning timeout triggered for:',\r\n          buildAgentNode.id,\r\n          autoPosition,\r\n        );\r\n        this.forceNodeToPosition(buildAgentNode.id, autoPosition);\r\n\r\n        // Update connections after position is forced\r\n        setTimeout(() => {\r\n          console.log('🚀 Updating connections after position force');\r\n          this.createAgentFlowConnections(this.buildAgentNodes);\r\n          if (this.canvasBoardComponent) {\r\n            // Force multiple connection updates for reliability\r\n            this.canvasBoardComponent.updateNodeConnectionPoints();\r\n            setTimeout(\r\n              () => this.canvasBoardComponent.updateNodeConnectionPoints(),\r\n              50,\r\n            );\r\n            setTimeout(\r\n              () => this.canvasBoardComponent.updateNodeConnectionPoints(),\r\n              100,\r\n            );\r\n          }\r\n          this.cdr.detectChanges();\r\n        }, 100);\r\n      }, 100);\r\n\r\n      console.log('🚀 onCanvasDropped completed successfully');\r\n      this.handleAutoTabSwitch(tool.type);\r\n    } catch (error) {\r\n      console.error('Error adding node:', error);\r\n    }\r\n  }\r\n\r\n  onNodeSelected(nodeId: string): void {\r\n    this.selectedNodeId = nodeId;\r\n    // Single click only selects the node, no preview\r\n  }\r\n\r\n  onNodeDoubleClicked(nodeId: string): void {\r\n    const selectedNode = this.buildAgentNodes.find(\r\n      (node) => node.id === nodeId,\r\n    );\r\n    if (selectedNode) {\r\n      const toolItem = this.findToolItemForNode(selectedNode);\r\n      if (toolItem) {\r\n        this.onItemPreview(toolItem);\r\n      } else {\r\n        let actualToolItem: ToolItem | null = null;\r\n        if (this.allToolItems[selectedNode.type + 's']) {\r\n          actualToolItem =\r\n            this.allToolItems[selectedNode.type + 's'].find(\r\n              (item: any) => item.name === selectedNode.name,\r\n            ) || null;\r\n        }\r\n\r\n        if (actualToolItem) {\r\n          this.onItemPreview(actualToolItem);\r\n        } else {\r\n          const basicToolItem: ToolItem = {\r\n            id: selectedNode.id,\r\n            name: selectedNode.name,\r\n            type: selectedNode.type,\r\n            description: `${selectedNode.type} component: ${selectedNode.name}`,\r\n            icon: selectedNode.icon || this.getIconForType(selectedNode.type),\r\n          };\r\n          this.onItemPreview(basicToolItem);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  private findToolItemForNode(node: BuildAgentNodeData): ToolItem | null {\r\n    const tabType = node.type === 'guardrail' ? 'guardrails' : node.type + 's';\r\n    return (\r\n      this.allToolItems[tabType]?.find(\r\n        (item: any) => item.name === node.name,\r\n      ) || null\r\n    );\r\n  }\r\n\r\n  onNodeMoved(event: {\r\n    nodeId: string;\r\n    position: { x: number; y: number };\r\n  }): void {\r\n    console.log('🎪 Node moved event received:', {\r\n      nodeId: event.nodeId,\r\n      newPosition: event.position,\r\n      currentBuildAgentPosition: this.buildAgentNodes.find(\r\n        (n) => n.id === event.nodeId,\r\n      )?.position,\r\n      currentCanvasPosition: this.canvasNodes.find((n) => n.id === event.nodeId)\r\n        ?.position,\r\n    });\r\n\r\n    const buildAgentNodeIndex = this.buildAgentNodes.findIndex(\r\n      (node) => node.id === event.nodeId,\r\n    );\r\n    if (buildAgentNodeIndex !== -1) {\r\n      this.buildAgentNodes = [\r\n        ...this.buildAgentNodes.slice(0, buildAgentNodeIndex),\r\n        {\r\n          ...this.buildAgentNodes[buildAgentNodeIndex],\r\n          position: event.position,\r\n        },\r\n        ...this.buildAgentNodes.slice(buildAgentNodeIndex + 1),\r\n      ];\r\n    }\r\n\r\n    const canvasNodeIndex = this.canvasNodes.findIndex(\r\n      (node) => node.id === event.nodeId,\r\n    );\r\n    if (canvasNodeIndex !== -1) {\r\n      this.canvasNodes = [\r\n        ...this.canvasNodes.slice(0, canvasNodeIndex),\r\n        { ...this.canvasNodes[canvasNodeIndex], position: event.position },\r\n        ...this.canvasNodes.slice(canvasNodeIndex + 1),\r\n      ];\r\n    }\r\n\r\n    // Track this as the latest moved node for next positioning\r\n    // In edit mode, once user starts dragging nodes, subsequent new nodes will follow normal create mode positioning\r\n    this.latestNodeId = event.nodeId;\r\n\r\n    // Ensure position updates worked correctly\r\n    const updatedBuildNode = this.buildAgentNodes.find(\r\n      (n) => n.id === event.nodeId,\r\n    );\r\n    if (\r\n      updatedBuildNode &&\r\n      (updatedBuildNode.position.x !== event.position.x ||\r\n        updatedBuildNode.position.y !== event.position.y)\r\n    ) {\r\n      console.warn('⚠️ Position update failed! Forcing correct position:', {\r\n        expected: event.position,\r\n        actual: updatedBuildNode.position,\r\n        nodeId: event.nodeId,\r\n      });\r\n      // Force correct position\r\n      updatedBuildNode.position = { ...event.position };\r\n    }\r\n\r\n    console.log('🎪 After node moved update:', {\r\n      nodeId: event.nodeId,\r\n      newPosition: event.position,\r\n      newBuildAgentPosition: this.buildAgentNodes.find(\r\n        (n) => n.id === event.nodeId,\r\n      )?.position,\r\n      newCanvasPosition: this.canvasNodes.find((n) => n.id === event.nodeId)\r\n        ?.position,\r\n      latestNodeId: this.latestNodeId,\r\n      positionsMatch:\r\n        JSON.stringify(\r\n          this.buildAgentNodes.find((n) => n.id === event.nodeId)?.position,\r\n        ) === JSON.stringify(event.position),\r\n    });\r\n\r\n    // CRITICAL: Force connection updates after node move\r\n    console.log('🔗 Setting up connection update timeout...');\r\n    setTimeout(() => {\r\n      console.log('🔗 Connection update timeout triggered');\r\n\r\n      // Removed forceCorrectPositions to prevent conflicts with user drag\r\n      console.log('🔗 Skipping position forcing to preserve user drag...');\r\n\r\n      // Recreate connections to ensure they follow moved nodes\r\n      console.log('🔗 Recreating connections...');\r\n      this.createAgentFlowConnections(this.buildAgentNodes);\r\n\r\n      if (this.canvasBoardComponent) {\r\n        console.log('🔗 Updating node connection points...');\r\n        this.canvasBoardComponent.updateNodeConnectionPoints();\r\n\r\n        // CRITICAL: Update edge paths after recreating connections\r\n        console.log('🔗 Updating edge paths after connection recreation...');\r\n        this.canvasBoardComponent.updateEdgePaths();\r\n\r\n        // Force immediate re-render of connections\r\n        this.cdr.detectChanges();\r\n        console.log('🔗 Connection update completed');\r\n      } else {\r\n        console.warn(\r\n          '⚠️ Canvas board component not available for connection update',\r\n        );\r\n      }\r\n    }, 0);\r\n\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  onDeleteNode(nodeId: string): void {\r\n    const nodeToDelete = this.buildAgentNodes.find(\r\n      (node) => node.id === nodeId,\r\n    );\r\n    if (nodeToDelete) {\r\n      // Clean up guardrail toggle state if it's a guardrail\r\n      if (nodeToDelete.type === 'guardrail') {\r\n        delete this.guardrailToggleStates[nodeId];\r\n        console.log('🛡️ Cleaned up guardrail toggle state for:', nodeId);\r\n      }\r\n\r\n      this.addItemBackToList(nodeToDelete);\r\n    }\r\n\r\n    this.buildAgentNodes = this.buildAgentNodes.filter(\r\n      (node) => node.id !== nodeId,\r\n    );\r\n    this.canvasNodes = this.canvasNodes.filter((node) => node.id !== nodeId);\r\n    this.canvasEdges = this.canvasEdges.filter(\r\n      (edge) => edge.source !== nodeId && edge.target !== nodeId,\r\n    );\r\n\r\n    this.recalculateNodePositionsAfterDeletion();\r\n    this.createAgentFlowConnections(this.buildAgentNodes);\r\n\r\n    setTimeout(() => {\r\n      if (this.canvasBoardComponent) {\r\n        this.canvasBoardComponent.updateNodeConnectionPoints();\r\n      }\r\n      this.updateFilteredTools();\r\n      this.cdr.detectChanges();\r\n    }, 100);\r\n  }\r\n\r\n  private recalculateNodePositionsAfterDeletion(): void {\r\n    // For simple vertical stacking, we don't need to recalculate positions\r\n    // Just update the canvas nodes with current positions\r\n    this.canvasNodes = this.buildAgentNodes.map((node) => ({\r\n      id: node.id,\r\n      type: 'build-agent',\r\n      data: { ...node, width: this.isExecuteMode ? 55 : 90 },\r\n      position: node.position,\r\n    }));\r\n\r\n    // Update latest node ID to the last remaining node (if any)\r\n    if (this.buildAgentNodes.length > 0) {\r\n      this.latestNodeId =\r\n        this.buildAgentNodes[this.buildAgentNodes.length - 1].id;\r\n    } else {\r\n      this.latestNodeId = null;\r\n    }\r\n  }\r\n\r\n  onConnectionCreated(edge: CanvasEdge): void {\r\n    const newEdge: CanvasEdge = {\r\n      id:\r\n        edge.id ||\r\n        `edge_${edge.source}_${edge.target}_${Math.floor(Math.random() * 1000)}`,\r\n      source: edge.source,\r\n      target: edge.target,\r\n      animated: edge.animated || true,\r\n    };\r\n    this.canvasEdges = [...this.canvasEdges, newEdge];\r\n  }\r\n\r\n  onStartConnection(_event: {\r\n    nodeId: string;\r\n    handleType: 'source' | 'target';\r\n    event: MouseEvent;\r\n  }): void {\r\n    // Canvas board handles connection logic\r\n  }\r\n\r\n  // Removed onNodePositionChanged - using onNodeMoved instead for simplicity\r\n\r\n  onUndo(): void {\r\n    /* Implement undo functionality */\r\n  }\r\n  onRedo(): void {\r\n    /* Implement redo functionality */\r\n  }\r\n  onReset(): void {\r\n    this.buildAgentNodes = [];\r\n    this.canvasNodes = [];\r\n    this.canvasEdges = [];\r\n    this.currentAgentDetails = null;\r\n  }\r\n  onRun(): void {\r\n    this.onExecute();\r\n  }\r\n\r\n  // Drop zone event handlers\r\n  onDropZoneNodeDropped(event: {\r\n    node: any;\r\n    zone: string;\r\n    position: { x: number; y: number };\r\n  }): void {\r\n    console.log('🎯 Node dropped in zone:', {\r\n      zone: event.zone,\r\n      nodeType: event.node.type,\r\n      position: event.position,\r\n    });\r\n\r\n    // Check if we need to replace existing node for single-node zones\r\n    if (\r\n      (event.zone === 'prompt' || event.zone === 'model') &&\r\n      this.buildAgentNodes.length > 0\r\n    ) {\r\n      const existingNodeIndex = this.buildAgentNodes.findIndex(\r\n        (node) => node.type === event.zone,\r\n      );\r\n\r\n      if (existingNodeIndex !== -1) {\r\n        console.log(`🔄 Replacing existing ${event.zone} node`);\r\n        // Remove the existing node first\r\n        this.buildAgentNodes.splice(existingNodeIndex, 1);\r\n        this.canvasNodes = this.canvasNodes.filter(\r\n          (node) => node.data?.type !== event.zone,\r\n        );\r\n      }\r\n    }\r\n\r\n    // Create new node directly\r\n    const tool = event.node;\r\n\r\n    // Create new node\r\n    const buildAgentNode: BuildAgentNodeData = {\r\n      id: crypto.randomUUID(),\r\n      type: tool.type,\r\n      name: tool.name,\r\n      icon: tool.icon,\r\n      position: { x: 0, y: 0 }, // Position will be managed by drop zone\r\n      originalToolData: tool, // Store all the original tool data\r\n    };\r\n\r\n    // Add to buildAgentNodes\r\n    this.buildAgentNodes.push(buildAgentNode);\r\n\r\n    // Create canvas node\r\n    const canvasNode: CanvasNode = {\r\n      id: buildAgentNode.id,\r\n      type: 'build-agent',\r\n      data: { ...buildAgentNode, width: 90 },\r\n      position: buildAgentNode.position,\r\n    };\r\n\r\n    // Add to canvas nodes\r\n    this.canvasNodes.push(canvasNode);\r\n\r\n    // Initialize guardrail toggle state if it's a guardrail\r\n    if (tool.type === 'guardrail') {\r\n      this.guardrailToggleStates[buildAgentNode.id] = true; // Default to enabled\r\n      console.log(\r\n        '🛡️ Initialized guardrail toggle state:',\r\n        buildAgentNode.id,\r\n        'enabled',\r\n      );\r\n    }\r\n\r\n    // Remove from available tools\r\n    this.removeItemFromList(tool);\r\n    this.updateFilteredTools();\r\n\r\n    // Trigger change detection\r\n    this.cdr.detectChanges();\r\n\r\n    console.log('✅ Node added to drop zone:', {\r\n      nodeId: buildAgentNode.id,\r\n      zone: event.zone,\r\n      totalNodes: this.buildAgentNodes.length,\r\n    });\r\n  }\r\n\r\n  onDropZoneNodeDeleted(nodeId: string): void {\r\n    this.onDeleteNode(nodeId);\r\n  }\r\n\r\n  // Handle guardrail toggle events from drop-zone-canvas\r\n  onGuardrailToggled(event: { nodeId: string; enabled: boolean }): void {\r\n    console.log('🛡️ Guardrail toggle event received:', event);\r\n    console.log(\r\n      '🛡️ Current toggle states before update:',\r\n      this.guardrailToggleStates,\r\n    );\r\n\r\n    this.guardrailToggleStates[event.nodeId] = event.enabled;\r\n\r\n    console.log(\r\n      '🛡️ Current toggle states after update:',\r\n      this.guardrailToggleStates,\r\n    );\r\n    console.log(\r\n      '🛡️ Guardrail toggle updated:',\r\n      event.nodeId,\r\n      event.enabled ? 'enabled' : 'disabled',\r\n    );\r\n  }\r\n\r\n  // Get metadata from navbar cookies\r\n  private getMetadataFromNavbar(): {\r\n    orgPath: string;\r\n    levelId: number;\r\n    orgId?: number;\r\n    domainId?: number;\r\n    projectId?: number;\r\n    teamId?: number;\r\n    org?: string;\r\n    domain?: string;\r\n    project?: string;\r\n    team?: string;\r\n  } {\r\n    const orgPath = this.tokenStorage.getCookie('org_path');\r\n    let organizationPath = '';\r\n    let levelId = 0; // Initialize to 0 instead of default value\r\n    let orgId, domainId, projectId, teamId;\r\n    let org, domain, project, team;\r\n\r\n    if (orgPath) {\r\n      const parts = orgPath.split('::');\r\n      const usecasePath = parts[0] || '';\r\n      const usecaseIdPath = parts[1] || '';\r\n      organizationPath = usecasePath;\r\n\r\n      const names = usecasePath.split('@');\r\n      if (names.length >= 4) {\r\n        [org, domain, project, team] = names;\r\n      }\r\n\r\n      const ids = usecaseIdPath.split('@').map(Number);\r\n      if (ids.length >= 4) {\r\n        [orgId, domainId, projectId, teamId] = ids;\r\n        levelId = teamId || ids[ids.length - 1] || 0; // Remove default fallback\r\n      } else if (ids.length > 0 && ids[0]) {\r\n        levelId = ids[0];\r\n      }\r\n    }\r\n\r\n    return {\r\n      orgPath: organizationPath,\r\n      levelId,\r\n      orgId,\r\n      domainId,\r\n      projectId,\r\n      teamId,\r\n      org,\r\n      domain,\r\n      project,\r\n      team,\r\n    };\r\n  }\r\n\r\n  private getUserSignature(): string {\r\n    return this.tokenStorage.getDaUsername() || '<EMAIL>';\r\n  }\r\n  private buildConfigurationFromLabels(): any[] {\r\n    const configurations: any[] = [];\r\n    const hasModel = this.buildAgentNodes.some((node) => node.type === 'model');\r\n    const hasKnowledge = this.buildAgentNodes.some(\r\n      (node) => node.type === 'knowledge',\r\n    );\r\n\r\n    configurations.push({\r\n      categoryName: 'Model',\r\n      categoryId: 1,\r\n      configs: [\r\n        {\r\n          configurationName: 'MODEL',\r\n          configurationValue: hasModel ? this.getModelConfigValue() : '',\r\n        },\r\n        { configurationName: 'MAX_TOKEN', configurationValue: '' },\r\n        { configurationName: 'TEMPERATURE', configurationValue: '' },\r\n        { configurationName: 'TOP_P', configurationValue: '' },\r\n        { configurationName: 'FREQUENCY_PENALTY', configurationValue: '' },\r\n        { configurationName: 'PROMPT_PREFIX', configurationValue: '' },\r\n        { configurationName: 'PROMPT_WRAPPER', configurationValue: '' },\r\n      ],\r\n    });\r\n\r\n    configurations.push({\r\n      categoryName: 'In Context Learning (ICL)',\r\n      categoryId: 2,\r\n      configs: [\r\n        {\r\n          configurationName: 'RAG',\r\n          configurationValue: hasKnowledge ? 'true' : '',\r\n        },\r\n        { configurationName: 'RAG_MODE', configurationValue: '' },\r\n        {\r\n          configurationName: 'RAG_KNOWLEDGEBASE_NAME',\r\n          configurationValue: hasKnowledge\r\n            ? this.getKnowledgeBaseConfigValue()\r\n            : '',\r\n        },\r\n        {\r\n          configurationName: 'RAG_KNOWLEDGEBASE_MAX_RECORD_COUNT',\r\n          configurationValue: '',\r\n        },\r\n        { configurationName: 'TOKEN_COMPRESSION', configurationValue: '' },\r\n      ],\r\n    });\r\n\r\n    const availableGuardrails = this.allToolItems['guardrails'] || [];\r\n    if (availableGuardrails.length > 0) {\r\n      configurations.push({\r\n        categoryName: 'Other',\r\n        categoryId: 3,\r\n        configs: this.getGuardrailConfigs(),\r\n      });\r\n    }\r\n    return configurations;\r\n  }\r\n\r\n  private extractPromptId(nodeId: string): string {\r\n    const match = nodeId.match(/prompt-(\\d+)/);\r\n    return match ? match[1] : nodeId;\r\n  }\r\n\r\n  private getModelConfigValue(): string {\r\n    const modelNode = this.buildAgentNodes.find(\r\n      (node) => node.type === 'model',\r\n    );\r\n    if (modelNode) {\r\n      if (modelNode.originalToolData && modelNode.originalToolData.id) {\r\n        return modelNode.originalToolData.id.toString();\r\n      }\r\n      const modelData = this.allToolItems['models'].find(\r\n        (model) => model.name === modelNode.name,\r\n      );\r\n      if (modelData) return modelData.id.toString();\r\n\r\n      const modelDataById = this.allToolItems['models'].find(\r\n        (model) =>\r\n          model.id === modelNode.name || model.id.toString() === modelNode.name,\r\n      );\r\n      if (modelDataById) return modelDataById.id.toString();\r\n    }\r\n    return '32';\r\n  }\r\n\r\n  private getKnowledgeBaseConfigValue(): string {\r\n    const knowledgeNodes = this.buildAgentNodes.filter(\r\n      (node) => node.type === 'knowledge',\r\n    );\r\n    if (knowledgeNodes.length > 0) {\r\n      const knowledgeIds = knowledgeNodes\r\n        .map((node) => {\r\n          if (node.originalToolData && node.originalToolData.id) {\r\n            return node.originalToolData.id.toString();\r\n          }\r\n          const knowledgeData = this.allToolItems['knowledge'].find(\r\n            (kb) => kb.name === node.name,\r\n          );\r\n          return knowledgeData ? knowledgeData.id : null;\r\n        })\r\n        .filter((id) => id !== null);\r\n      return knowledgeIds.join(',');\r\n    }\r\n    return '';\r\n  }\r\n\r\n  private extractGuardrailId(nodeId: string): string {\r\n    return nodeId.match(/guardrail-(\\d+)/)?.[1] || nodeId;\r\n  }\r\n\r\n  private getGuardrailConfigs(): any[] {\r\n    const guardrailNodes = this.buildAgentNodes.filter(\r\n      (node) => node.type === 'guardrail',\r\n    );\r\n    const configs: any[] = [];\r\n\r\n    // Check if any guardrails are present AND enabled\r\n    const hasEnabledGuardrails = guardrailNodes.some(\r\n      (node) => this.guardrailToggleStates[node.id] !== false,\r\n    );\r\n\r\n    configs.push({\r\n      configurationName: 'ENABLE_GUARDRAILS',\r\n      configurationValue: hasEnabledGuardrails ? 'true' : 'false',\r\n    });\r\n\r\n    const availableGuardrails = this.allToolItems['guardrails'] || [];\r\n    const enabledGuardrailNames = new Set<string>();\r\n\r\n    // Only add guardrails that are both dropped AND enabled via toggle\r\n    guardrailNodes.forEach((node) => {\r\n      const isEnabled = this.guardrailToggleStates[node.id] !== false; // Default to true if not set\r\n\r\n      console.log('🛡️ Processing guardrail node:', {\r\n        nodeId: node.id,\r\n        nodeName: node.name,\r\n        toggleState: this.guardrailToggleStates[node.id],\r\n        isEnabled: isEnabled,\r\n      });\r\n\r\n      if (isEnabled) {\r\n        const guardrailId = this.extractGuardrailId(node.id);\r\n        const guardrailData = availableGuardrails.find(\r\n          (gr) =>\r\n            gr.id === guardrailId || gr.id === node.id || gr.name === node.name,\r\n        );\r\n\r\n        const configName = guardrailData\r\n          ? `GUARDRAIL_${guardrailData.name\r\n              .toUpperCase()\r\n              .replace(/\\s+/g, '_')\r\n              .replace(/[^A-Z0-9_]/g, '')}`\r\n          : `GUARDRAIL_${node.name\r\n              .toUpperCase()\r\n              .replace(/\\s+/g, '_')\r\n              .replace(/[^A-Z0-9_]/g, '')}`;\r\n        enabledGuardrailNames.add(configName);\r\n\r\n        console.log(\r\n          '🛡️ Including enabled guardrail in config:',\r\n          configName,\r\n          'for node:',\r\n          node.name,\r\n          'guardrailData:',\r\n          guardrailData,\r\n        );\r\n      } else {\r\n        console.log('🛡️ Excluding disabled guardrail from config:', node.name);\r\n      }\r\n    });\r\n\r\n    // First, add all enabled guardrails (from dropped nodes) to configs\r\n    enabledGuardrailNames.forEach((configName) => {\r\n      configs.push({\r\n        configurationName: configName,\r\n        configurationValue: true,\r\n      });\r\n      console.log('🛡️ Added enabled guardrail config:', configName, '= true');\r\n    });\r\n\r\n    // Then, set all other available guardrails to false (if not already added)\r\n    availableGuardrails.forEach((guardrail) => {\r\n      if (guardrail.name === 'Enable Guardrails') return;\r\n      const configName = `GUARDRAIL_${guardrail.name\r\n        .toUpperCase()\r\n        .replace(/\\s+/g, '_')\r\n        .replace(/[^A-Z0-9_]/g, '')}`;\r\n\r\n      // Only add if not already added from enabled guardrails\r\n      const alreadyAdded = configs.some(\r\n        (config) => config.configurationName === configName,\r\n      );\r\n      if (!alreadyAdded) {\r\n        configs.push({\r\n          configurationName: configName,\r\n          configurationValue: false,\r\n        });\r\n        console.log(\r\n          '🛡️ Added disabled guardrail config:',\r\n          configName,\r\n          '= false',\r\n        );\r\n      } else {\r\n        console.log('🛡️ Skipped already added guardrail config:', configName);\r\n      }\r\n    });\r\n\r\n    console.log('🛡️ Final guardrail configs:', configs);\r\n\r\n    // Debug: Show specific guardrail configs\r\n    configs.forEach((config) => {\r\n      if (config.configurationName.startsWith('GUARDRAIL_')) {\r\n        console.log(\r\n          '🛡️ Config detail:',\r\n          config.configurationName,\r\n          '=',\r\n          config.configurationValue,\r\n        );\r\n      }\r\n    });\r\n\r\n    return configs;\r\n  }\r\n\r\n  private validateIndividualAgentData(): {\r\n    isValid: boolean;\r\n    errors: string[];\r\n    warnings: string[];\r\n  } {\r\n    const errors: string[] = [];\r\n    const warnings: string[] = [];\r\n\r\n    if (!this.agentName || this.agentName.trim() === '')\r\n      errors.push('Agent name is required');\r\n    if (!this.agentDetail || this.agentDetail.trim() === '')\r\n      errors.push('Agent details are required');\r\n\r\n    const promptNode = this.buildAgentNodes.find(\r\n      (node) => node.type === 'prompt',\r\n    );\r\n    if (!promptNode) errors.push('Prompt selection is required');\r\n\r\n    const modelNode = this.buildAgentNodes.find(\r\n      (node) => node.type === 'model',\r\n    );\r\n    if (!modelNode)\r\n      warnings.push('Model selection is recommended for better performance');\r\n\r\n    const knowledgeNode = this.buildAgentNodes.find(\r\n      (node) => node.type === 'knowledge',\r\n    );\r\n    if (!knowledgeNode)\r\n      warnings.push(\r\n        'Knowledge base selection is recommended for enhanced responses',\r\n      );\r\n\r\n    const guardrailNode = this.buildAgentNodes.find(\r\n      (node) => node.type === 'guardrail',\r\n    );\r\n    if (!guardrailNode)\r\n      warnings.push(\r\n        'Guardrail selection is recommended for safe AI interactions',\r\n      );\r\n\r\n    return { isValid: errors.length === 0, errors, warnings };\r\n  }\r\n\r\n  private buildIndividualAgentPayload(): any {\r\n    const { orgPath, levelId } = this.getMetadataFromNavbar();\r\n    const promptNode = this.buildAgentNodes.find(\r\n      (node) => node.type === 'prompt',\r\n    );\r\n    let promptDetails = '';\r\n    let promptTemplate = '';\r\n\r\n    if (promptNode) {\r\n      const promptId = this.extractPromptId(promptNode.id);\r\n      const promptData = this.allToolItems['prompts'].find(\r\n        (prompt) =>\r\n          prompt.id === promptId ||\r\n          prompt.id === promptNode.id ||\r\n          prompt.name === promptNode.name,\r\n      );\r\n\r\n      if (promptData) {\r\n        promptDetails = promptData.description || promptData.name || '';\r\n        promptTemplate =\r\n          (promptData as any).template ||\r\n          (promptData as any).content ||\r\n          (promptData as any).promptTemplate ||\r\n          promptData.name ||\r\n          '';\r\n      } else {\r\n        promptDetails = promptNode.name;\r\n        promptTemplate = promptNode.name;\r\n      }\r\n    }\r\n\r\n    const useCaseCode = this.agentName\r\n      .trim()\r\n      .replace(/\\s+/g, '_')\r\n      .toUpperCase();\r\n    const finalUseCaseDetails = this.agentDetail\r\n      ? this.agentDetail.trim()\r\n      : promptDetails;\r\n    const baseOrgPath =\r\n      orgPath || 'ADD_NEW@SENIOR_BUSINESS_ANALYST@TEST_GOPAL@TEAM1';\r\n    const finalOrgPath = `${useCaseCode}@${baseOrgPath}`;\r\n\r\n    return {\r\n      levelId,\r\n      code: useCaseCode,\r\n      name: this.agentName.trim(),\r\n      useCaseDetails: finalUseCaseDetails,\r\n      promptTemplate,\r\n      type: null,\r\n      webPortalUseCase: null,\r\n      location: null,\r\n      sourceLanguage: null,\r\n      destinationLanguage: null,\r\n      configuration: this.buildConfigurationFromLabels(),\r\n      userSignature: this.getUserSignature(),\r\n      organizationPath: finalOrgPath,\r\n    };\r\n  }\r\n\r\n  private async fetchPromptDetailsForSave(): Promise<void> {\r\n    const promptNode = this.buildAgentNodes.find(\r\n      (node) => node.type === 'prompt',\r\n    );\r\n    if (!promptNode) return;\r\n\r\n    let promptData = this.allToolItems['prompts']?.find(\r\n      (p) => p.name === promptNode.name,\r\n    );\r\n    if (!promptData && promptNode.originalToolData) {\r\n      promptData = promptNode.originalToolData;\r\n    }\r\n\r\n    if (!promptData) {\r\n      const extractedId = this.extractPromptId(promptNode.id);\r\n      if (extractedId && extractedId !== promptNode.id) {\r\n        try {\r\n          const apiPromptData = await new Promise((resolve, reject) => {\r\n            this.promptsService.getPromptById(extractedId).subscribe({\r\n              next: (data) => resolve(data),\r\n              error: (error) => {\r\n                console.error('Error fetching prompt from API:', error);\r\n                reject(error);\r\n              },\r\n            });\r\n          });\r\n\r\n          if (apiPromptData) {\r\n            if (!this.allToolItems['prompts'])\r\n              this.allToolItems['prompts'] = [];\r\n            this.allToolItems['prompts'].push(apiPromptData as any);\r\n          }\r\n        } catch (error) {\r\n          console.error('Error fetching prompt details:', error);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  private async saveIndividualAgent(): Promise<void> {\r\n    if (this.currentAgentType !== 'individual') return;\r\n\r\n    const validation = this.validateIndividualAgentData();\r\n    if (!validation.isValid) {\r\n      this.dialogService.warning({\r\n        title: 'Validation Failed',\r\n        message: 'Please fix the following errors:\\n' + validation.errors.join('\\n'),\r\n        showProceedButton: true,\r\n        proceedButtonText: 'OK',\r\n      })\r\n      return;\r\n    }\r\n\r\n    if (validation.warnings && validation.warnings.length > 0) {\r\n      this.dialogService.warning({\r\n        title: 'Configuration Recommendations',\r\n        message: 'The following configurations are recommended:\\n' +\r\n        validation.warnings.join('\\n') +\r\n        '\\n\\nDo you want to continue saving without these configurations?',\r\n        showProceedButton: true,\r\n        proceedButtonText: 'OK',\r\n      })\r\n    }\r\n\r\n    await this.performIndividualAgentSave();\r\n  }\r\n\r\n  private async saveCollaborativeAgent(): Promise<void> {\r\n    if (this.currentAgentType !== 'collaborative') return;\r\n\r\n    // Validate level path is available from header\r\n    const { levelId } = this.getMetadataFromNavbar();\r\n    if (!levelId || levelId === 0) {\r\n      this.dialogService.warning({\r\n        title: 'Organization Path Required',\r\n        message: 'Please select a valid organization path from the header navigation before creating a collaborative agent.',\r\n        showProceedButton: true,\r\n        proceedButtonText: 'OK',\r\n      })\r\n      return;\r\n    }\r\n\r\n    const validation = this.validateCollaborativeAgentData();\r\n    if (!validation.isValid) {\r\n      this.dialogService.warning({\r\n        title: 'Validation Failed',\r\n        message: 'Please fix the following errors:\\n' + validation.errors.join('\\n'),\r\n        showProceedButton: true,\r\n        proceedButtonText: 'OK',\r\n      })\r\n      return;\r\n    }\r\n\r\n    if (validation.warnings && validation.warnings.length > 0) {\r\n      this.dialogService.warning({\r\n        title: 'Configuration Recommendations',\r\n        message: 'The following configurations are recommended:\\n' +\r\n        validation.warnings.join('\\n') +\r\n        '\\n\\nDo you want to continue saving without these configurations?',\r\n        showProceedButton: true,\r\n        proceedButtonText: 'OK',\r\n      })\r\n      return;\r\n    }\r\n\r\n    await this.performCollaborativeAgentSave();\r\n  }\r\n\r\n  private async performCollaborativeAgentSave(): Promise<void> {\r\n    await this.fetchPromptDetailsForSave();\r\n    const payload = this.buildCollaborativeAgentPayloadV2();\r\n    this.saveAgent(\r\n      () => this.agentService.createCollaborativeAgentV2(payload),\r\n      payload.name,\r\n      true,\r\n    );\r\n  }\r\n\r\n  private async performIndividualAgentSave(): Promise<void> {\r\n    const payload = this.buildIndividualAgentPayload();\r\n    this.saveAgent(\r\n      () => this.agentService.individualAgentSave(payload),\r\n      payload.name,\r\n      false,\r\n    );\r\n  }\r\n\r\n  private saveAgent(\r\n    saveFn: () => Observable<any>,\r\n    agentName: string,\r\n    isCollaborativeType: boolean,\r\n  ): void {\r\n    saveFn().subscribe({\r\n      next: (response: any) => {\r\n        const defaultSuccess = isCollaborativeType\r\n          ? `Collaborative agent \"${agentName || 'Agent'}\" has been saved and sent for approval successfully!`\r\n          : `Individual agent \"${agentName || 'Agent'}\" has been saved successfully!`;\r\n\r\n        const successMessage = response?.message || defaultSuccess;\r\n\r\n        // Store the agent ID for potential navigation from success popup\r\n        this.currentAgentId = response.useCaseId || response.agentId;\r\n\r\n        this.dialogService.confirmation({\r\n          title: 'Success',\r\n          message: successMessage,\r\n          confirmButtonText: 'OK',\r\n        });\r\n      },\r\n      error: (error: any) => {\r\n        const errorMessage =\r\n          error?.error?.message ||\r\n          error?.message ||\r\n          `Error saving ${\r\n            isCollaborativeType ? 'collaborative' : 'individual'\r\n          } agent. Please try again.`;\r\n\r\n          this.dialogService.error({\r\n            title: 'Error',\r\n            message: errorMessage,\r\n            showRetryButton: true,\r\n            retryButtonText: 'Ok'\r\n          });\r\n      },\r\n    });\r\n  }\r\n\r\n  private validateCollaborativeAgentData(): {\r\n    isValid: boolean;\r\n    errors: string[];\r\n    warnings: string[];\r\n  } {\r\n    const errors: string[] = [];\r\n    const warnings: string[] = [];\r\n\r\n    if (!this.agentName || this.agentName.trim().length === 0)\r\n      errors.push('Agent name is required');\r\n    if (!this.agentDetail || this.agentDetail.trim().length === 0)\r\n      errors.push('Agent details are required');\r\n\r\n    const promptNodes = this.buildAgentNodes.filter(\r\n      (node) => node.type === 'prompt',\r\n    );\r\n    if (promptNodes.length === 0) errors.push('Prompt selection is required');\r\n\r\n    const modelNodes = this.buildAgentNodes.filter(\r\n      (node) => node.type === 'model',\r\n    );\r\n    if (modelNodes.length === 0) errors.push('Model selection is required');\r\n\r\n    const knowledgeNodes = this.buildAgentNodes.filter(\r\n      (node) => node.type === 'knowledge',\r\n    );\r\n    if (knowledgeNodes.length === 0)\r\n      warnings.push(\r\n        'Knowledge base selection is recommended for enhanced responses',\r\n      );\r\n\r\n    const toolNodes = this.buildAgentNodes.filter(\r\n      (node) => node.type === 'tool',\r\n    );\r\n    if (toolNodes.length === 0)\r\n      warnings.push(\r\n        'Tool selection is recommended for collaborative agent capabilities',\r\n      );\r\n\r\n    return { isValid: errors.length === 0, errors, warnings };\r\n  }\r\n\r\n  private buildCollaborativeAgentPayloadV2(): any {\r\n    const { levelId } = this.getMetadataFromNavbar();\r\n\r\n    const modelNodes = this.buildAgentNodes.filter(\r\n      (node) => node.type === 'model',\r\n    );\r\n    const modelRefs = modelNodes.map((node) => {\r\n      const modelData = this.allToolItems['models'].find(\r\n        (m) => m.name === node.name,\r\n      );\r\n      return modelData?.id || 40;\r\n    });\r\n    const uniqueModelRefs = [...new Set(modelRefs)];\r\n\r\n    const knowledgeNodes = this.buildAgentNodes.filter(\r\n      (node) => node.type === 'knowledge',\r\n    );\r\n    const knowledgeIds = knowledgeNodes\r\n      .map((node) => {\r\n        if (node.originalToolData && node.originalToolData.id) {\r\n          return node.originalToolData.id;\r\n        }\r\n        const knowledgeData = this.allToolItems['knowledge'].find(\r\n          (k) => k.name === node.name,\r\n        );\r\n        return knowledgeData?.id;\r\n      })\r\n      .filter((id) => id);\r\n    const uniqueKnowledgeIds = [...new Set(knowledgeIds)];\r\n\r\n    const promptNode = this.buildAgentNodes.find(\r\n      (node) => node.type === 'prompt',\r\n    );\r\n    let promptData = null;\r\n\r\n    if (promptNode) {\r\n      if (promptNode.originalToolData) {\r\n        promptData = promptNode.originalToolData;\r\n      } else {\r\n        promptData = this.allToolItems['prompts'].find(\r\n          (p) => p.name === promptNode.name,\r\n        );\r\n        if (!promptData) {\r\n          const extractedId = this.extractPromptId(promptNode.id);\r\n          promptData = this.allToolItems['prompts'].find(\r\n            (p) =>\r\n              String(p.id) === extractedId ||\r\n              String(p.id) === String(parseInt(extractedId)),\r\n          );\r\n        }\r\n      }\r\n    }\r\n\r\n    const originalPromptData = promptData?.originalPromptData || promptData;\r\n    const sourceData = originalPromptData || promptData;\r\n    const role = sourceData?.role || 'Python Developer';\r\n    const goal =\r\n      sourceData?.goal ||\r\n      this.agentDetail ||\r\n      'Analyze data and provide insights using Python';\r\n    const backstory =\r\n      sourceData?.backstory ||\r\n      this.agentDetail ||\r\n      'You are an experienced data analyst with strong Python skills.';\r\n    const description =\r\n      sourceData?.description ||\r\n      sourceData?.descriptionConsolidated ||\r\n      this.agentDetail ||\r\n      '%1$s';\r\n    const expectedOutput =\r\n      sourceData?.expectedOutput ||\r\n      sourceData?.expectedOutputConsolidated ||\r\n      'Output is :';\r\n\r\n    const toolNodes = this.buildAgentNodes.filter(\r\n      (node) => node.type === 'tool',\r\n    );\r\n    const builtInTools: number[] = [];\r\n    const userTools: number[] = [];\r\n\r\n    console.log('=== DEEP TOOL ANALYSIS ===');\r\n    console.log('Total buildAgentNodes:', this.buildAgentNodes.length);\r\n    console.log('Tool nodes found:', toolNodes.length);\r\n    console.log(\r\n      'Tool nodes details:',\r\n      toolNodes.map((node, index) => ({\r\n        index,\r\n        id: node.id,\r\n        name: node.name,\r\n        type: node.type,\r\n        hasOriginalToolData: !!node.originalToolData,\r\n        originalToolDataId: node.originalToolData?.id,\r\n        originalToolDataName: node.originalToolData?.name,\r\n      })),\r\n    );\r\n\r\n    toolNodes.forEach((node, index) => {\r\n      console.log(\r\n        `\\n--- Processing tool node ${index + 1}/${toolNodes.length} ---`,\r\n      );\r\n      console.log('Node details:', {\r\n        id: node.id,\r\n        name: node.name,\r\n        originalToolData: node.originalToolData,\r\n      });\r\n      let toolData = this.allToolItems['tools'].find(\r\n        (t) => t.name === node.name,\r\n      );\r\n      if (!toolData && node.originalToolData) {\r\n        toolData = node.originalToolData;\r\n        console.log('Using originalToolData for node:', node.name, toolData);\r\n      }\r\n      if (!toolData && node.originalToolData?.id) {\r\n        toolData = this.allToolItems['tools'].find(\r\n          (t) => t.id === node.originalToolData.id,\r\n        );\r\n        console.log('Found tool by ID:', toolData);\r\n      }\r\n      if (node.originalToolData && node.originalToolData.id) {\r\n        console.log('Preferring originalToolData over lookup result');\r\n        toolData = node.originalToolData;\r\n      }\r\n\r\n      if (!toolData) {\r\n        console.warn(`Tool data not found for node: ${node.name}`, {\r\n          nodeName: node.name,\r\n          nodeOriginalData: node.originalToolData,\r\n          availableTools: this.allToolItems['tools'].map((t) => ({\r\n            id: t.id,\r\n            name: t.name,\r\n          })),\r\n        });\r\n        return;\r\n      }\r\n\r\n      console.log('Found tool data for node:', node.name, toolData);\r\n\r\n      let numericToolId: number;\r\n      const toolId = toolData.id;\r\n\r\n      // Extract numeric ID from prefixed string IDs\r\n      if (typeof toolId === 'string') {\r\n        if (toolId.startsWith('builtin-')) {\r\n          // Extract numeric ID from \"builtin-123\" format\r\n          const numericPart = toolId.replace('builtin-', '');\r\n          numericToolId = parseInt(numericPart);\r\n        } else if (toolId.startsWith('user-')) {\r\n          // Extract numeric ID from \"user-456\" format\r\n          const numericPart = toolId.replace('user-', '');\r\n          numericToolId = parseInt(numericPart);\r\n        } else {\r\n          // Try to extract any numeric part from the string\r\n          const numericMatch = toolId.match(/\\d+/);\r\n          numericToolId = numericMatch ? parseInt(numericMatch[0]) : 0;\r\n        }\r\n      } else if (typeof toolId === 'number') {\r\n        numericToolId = toolId;\r\n      } else {\r\n        return;\r\n      }\r\n\r\n      const isUserTool =\r\n        typeof toolData.id === 'string' && toolData.id.startsWith('user-');\r\n      if (isUserTool) {\r\n        userTools.push(numericToolId);\r\n      } else {\r\n        builtInTools.push(numericToolId);\r\n      }\r\n    });\r\n\r\n    const agentConfigs = {\r\n      temperature: 0.3,\r\n      topP: 0.95,\r\n      maxToken: '4000',\r\n      modelRef: uniqueModelRefs,\r\n      knowledgeBaseRef: uniqueKnowledgeIds,\r\n      maxIter: null,\r\n      maxRpm: 5,\r\n      maxExecutionTime: 5,\r\n      allowDelegation: true,\r\n      allowCodeExecution: true,\r\n      isSafeCodeExecution: true,\r\n      toolRef: builtInTools,\r\n      userToolRef: userTools,\r\n    };\r\n\r\n    const userSignature = this.getUserSignature();\r\n\r\n    // Ensure levelId is properly retrieved from header without default fallback\r\n    if (!levelId || levelId === 0) {\r\n      throw new Error(\r\n        'Organization path is required from header navigation. Please select a valid organization path before creating a collaborative agent.',\r\n      );\r\n    }\r\n\r\n    return {\r\n      agentDetails: this.agentDetail.trim(),\r\n      name: this.agentName.trim(),\r\n      role,\r\n      goal,\r\n      backstory,\r\n      description,\r\n      expectedOutput,\r\n      agentConfigs,\r\n      createdBy: userSignature,\r\n      levelId: levelId,\r\n      modifiedBy: userSignature,\r\n    };\r\n  }\r\n\r\n  private buildCollaborativeAgentChangeRequestPayload(): any {\r\n    const basePayload = this.buildCollaborativeAgentPayloadV2();\r\n    return {\r\n      id: parseInt(this.currentAgentId!),\r\n      ...basePayload,\r\n      isDeleted: false,\r\n    };\r\n  }\r\n\r\n  private buildIndividualAgentUpdatePayload(): any {\r\n    const basePayload = this.buildIndividualAgentPayload();\r\n    return {\r\n      ...basePayload,\r\n      useCaseId: parseInt(this.currentAgentId!),\r\n      configuration: basePayload.configuration.map((category: any) => ({\r\n        ...category,\r\n        configs: category.configs.map((config: any) => ({\r\n          ...config,\r\n          configId:\r\n            this.agentConfigIds.get(\r\n              `${category.categoryId}-${config.configurationName}`,\r\n            ) || undefined,\r\n        })),\r\n      })),\r\n    };\r\n  }\r\n\r\n  private async updateIndividualAgent(): Promise<void> {\r\n    return new Promise((resolve, reject) => {\r\n      try {\r\n        const payload = this.buildIndividualAgentUpdatePayload();\r\n        this.agentService.individualAgentEdit(payload).subscribe({\r\n          next: (response: any) => {\r\n            const successMessage =\r\n              response?.message ||\r\n              `Individual agent \"${payload.name || 'Agent'}\" has been updated successfully!`;\r\n              this.dialogService.confirmation({\r\n                title: 'Success',\r\n                message: successMessage,\r\n                confirmButtonText: 'OK',\r\n              });\r\n\r\n            // Update the current agent details with the new data\r\n            this.currentAgentDetails = {\r\n              ...this.currentAgentDetails,\r\n              ...payload,\r\n              code: payload.code,\r\n              useCaseCode: payload.code,\r\n              name: payload.name,\r\n              useCaseDetails: payload.useCaseDetails,\r\n            };\r\n\r\n            // Update the agentCode with the new code\r\n            this.agentCode = payload.code;\r\n\r\n            // Store the agent ID for potential navigation from success popup\r\n            this.currentAgentId = this.currentAgentId || payload.id;\r\n\r\n            // Refresh playground with updated data\r\n            this.loadAgentDetailsForPlayground();\r\n\r\n            resolve();\r\n          },\r\n          error: (error: any) => {\r\n            console.error('Error updating individual agent:', error);\r\n            const errorMessage =\r\n              error?.error?.message ||\r\n              error?.message ||\r\n              'Error updating individual agent. Please try again.';\r\n              this.dialogService.error({\r\n                title: 'Error',\r\n                message: errorMessage,\r\n                showRetryButton: true,\r\n                retryButtonText: 'Ok'\r\n              });\r\n            reject(error);\r\n          },\r\n        });\r\n      } catch (error) {\r\n        console.error('Error in updateIndividualAgent:', error);\r\n        reject(error);\r\n      }\r\n    });\r\n  }\r\n\r\n  private async submitCollaborativeAgentChangeRequest(): Promise<void> {\r\n    if (this.currentAgentType !== 'collaborative' || !this.currentAgentId)\r\n      return;\r\n\r\n    // Validate level path is available from header\r\n    const { levelId } = this.getMetadataFromNavbar();\r\n    if (!levelId || levelId === 0) {\r\n      this.dialogService.confirmation({\r\n        title: 'Error',\r\n        message: 'Please select a valid organization path from the header navigation before updating a collaborative agent.',\r\n        confirmButtonText: 'OK',\r\n      })\r\n      return;\r\n    }\r\n\r\n    const validation = this.validateCollaborativeAgentData();\r\n    if (!validation.isValid) {\r\n      console.error('Validation failed:', validation.errors);\r\n      this.showErrorMessage(\r\n        'Validation Failed',\r\n        'Please fix the following errors:\\n' + validation.errors.join('\\n'),\r\n      );\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const payload = this.buildCollaborativeAgentChangeRequestPayload();\r\n      this.agentService\r\n        .submitCollaborativeAgentChangeRequest(payload)\r\n        .subscribe({\r\n          next: (response: any) => {\r\n            const successMessage =\r\n              response?.message ||\r\n              `Change request for agent \"${payload.name || 'Agent'}\" has been submitted successfully!`;\r\n            // Store the agent ID for potential navigation from success popup\r\n            this.currentAgentId = this.currentAgentId || payload.id;\r\n            this.dialogService.confirmation({\r\n              title: 'Success',\r\n              message: successMessage,\r\n              confirmButtonText: 'OK',\r\n            })\r\n\r\n            this.loadAgentDetailsForPlayground();\r\n          },\r\n          error: (error: any) => {\r\n            console.error(\r\n              'Error submitting collaborative agent change request:',\r\n              error,\r\n            );\r\n            const errorMessage =\r\n              error?.error?.message ||\r\n              error?.message ||\r\n              'Failed to submit change request. Please try again.';\r\n              this.dialogService.error({\r\n                title: 'Error',\r\n                message: errorMessage,\r\n                showRetryButton: true,\r\n                retryButtonText: 'Ok'\r\n              })\r\n          },\r\n        });\r\n    } catch (error: any) {\r\n      console.error('Error preparing change request:', error);\r\n      const errorMessage =\r\n        error?.message ||\r\n        'Failed to prepare change request. Please check the console for details.';\r\n        this.dialogService.warning({\r\n          title: 'Warning',\r\n          message: errorMessage,\r\n          showProceedButton: true,\r\n          proceedButtonText: 'Ok'\r\n        })\r\n    }\r\n  }\r\n\r\n  public onApprovalRequested(): void {\r\n    // This method is kept for compatibility but no longer used for collaborative agents\r\n  }\r\n\r\n  async onPrimaryButtonClick(): Promise<void> {\r\n    console.log('🚀 PRIMARY BUTTON CLICKED:', {\r\n      isExecuteMode: this.isExecuteMode,\r\n      isViewMode: this.isViewMode,\r\n      isEditMode: this.isEditMode,\r\n      primaryButtonText: this.primaryButtonText,\r\n    });\r\n\r\n    if (this.isExecuteMode) return;\r\n\r\n    if (this.isViewMode) {\r\n      console.log('🚀 View mode - calling onExecute...');\r\n      this.onExecute();\r\n      return;\r\n    }\r\n\r\n    // Check if button text is \"Execute\" - call onExecute regardless of mode\r\n    if (this.primaryButtonText === 'Execute') {\r\n      console.log('🚀 Execute button clicked - calling onExecute...');\r\n      this.onExecute();\r\n      return;\r\n    }\r\n\r\n    if (this.isEditMode) {\r\n      if (this.currentAgentType === 'individual') {\r\n        await this.updateIndividualAgent();\r\n      } else if (this.currentAgentType === 'collaborative') {\r\n        await this.submitCollaborativeAgentChangeRequest();\r\n      }\r\n      return;\r\n    }\r\n\r\n    const validation =\r\n      this.currentAgentType === 'individual'\r\n        ? this.validateIndividualAgentData()\r\n        : this.validateCollaborativeAgentData();\r\n    if (!validation.isValid) {\r\n      this.showErrorMessage(\r\n        'Validation Failed',\r\n        'Please fix the following errors before saving:\\n' +\r\n          validation.errors.join('\\n'),\r\n      );\r\n      return;\r\n    }\r\n\r\n    if (this.currentAgentType === 'individual') {\r\n      await this.saveIndividualAgent();\r\n    } else if (this.currentAgentType === 'collaborative') {\r\n      await this.saveCollaborativeAgent();\r\n    }\r\n  }\r\n\r\n  onExecute(): void {\r\n    this.isExecuteMode = true;\r\n    this.showChatInterface = true;\r\n\r\n    // Recalculate all node positions for execute mode layout\r\n    this.repositionNodesForExecuteMode();\r\n\r\n    // Multiple force updates to ensure positioning sticks\r\n    setTimeout(() => {\r\n      this.cdr.detectChanges();\r\n      this.forceExecutePositions();\r\n      if (this.canvasBoardComponent) {\r\n        this.canvasBoardComponent.updateNodeConnectionPoints();\r\n      }\r\n    }, 100);\r\n\r\n    // Additional safety update\r\n    setTimeout(() => {\r\n      this.forceExecutePositions();\r\n    }, 300);\r\n\r\n    this.loadAgentDetailsForPlayground();\r\n    if (this.agentName) {\r\n      setTimeout(() => {\r\n        this.autoSelectCurrentAgent();\r\n      }, 1500);\r\n    }\r\n\r\n    this.chatMessages = [\r\n      {\r\n        from: 'ai',\r\n        text: `Hi there! I am ${this.agentName || 'your build agent'}. How can I help you today?`,\r\n      },\r\n    ];\r\n\r\n    setTimeout(() => {\r\n      const agentId = 'build-agent-' + Date.now();\r\n      this.toolExecutionService.startExecution(agentId, this.chatMessages);\r\n      this.executionSubscription = this.toolExecutionService\r\n        .getExecutionState()\r\n        .subscribe((state) => {\r\n          if (state.isExecuting && state.toolId === agentId) {\r\n            this.chatMessages = state.chatMessages;\r\n          }\r\n        });\r\n    }, 100);\r\n  }\r\n\r\n  handleChatMessage(message: string): void {\r\n    if (this.currentAgentType === 'individual') {\r\n      if (!this.selectedPrompt || this.selectedPrompt === 'default') {\r\n        this.showAgentError(\r\n          'Please select an agent from the dropdown before testing.',\r\n        );\r\n        return;\r\n      }\r\n\r\n      let displayMessage = message;\r\n      if (this.agentFilesUploadedData.length > 0) {\r\n        const fileNames = this.agentFilesUploadedData\r\n          .map((file) => file.documentName)\r\n          .join(', ');\r\n        displayMessage = `${message}\\n\\n📎 Attached files: ${fileNames}`;\r\n      }\r\n\r\n      this.chatMessages = [\r\n        ...this.chatMessages,\r\n        { from: 'user', text: displayMessage },\r\n      ];\r\n      this.isProcessingChat = true;\r\n\r\n      const isConversational =\r\n        this.agentPlaygroundForm.get('isConversational')?.value || false;\r\n      const isUseTemplate =\r\n        this.agentPlaygroundForm.get('isUseTemplate')?.value || false;\r\n      const agentMode =\r\n        this.agentCode || this.selectedAgentMode || this.selectedPrompt;\r\n\r\n      let useCaseIdentifier = this.selectedUseCaseIdentifier;\r\n      if (!useCaseIdentifier) {\r\n        const orgPath = this.buildOrganizationPath();\r\n        useCaseIdentifier = `${agentMode}${orgPath}`;\r\n      }\r\n\r\n      if (this.agentFilesUploadedData.length > 0) {\r\n        this.processAgentFilesAndSendMessage(\r\n          message,\r\n          agentMode,\r\n          useCaseIdentifier,\r\n          isConversational,\r\n          isUseTemplate,\r\n        );\r\n        return;\r\n      }\r\n\r\n      this.sendAgentMessageToAPI(\r\n        message,\r\n        agentMode,\r\n        useCaseIdentifier,\r\n        isConversational,\r\n        isUseTemplate,\r\n      );\r\n    } else if (this.currentAgentType === 'collaborative') {\r\n      this.isProcessingChat = true;\r\n      let payload = {\r\n        executionId: this.executionId,\r\n        agentId: Number(this.currentAgentId),\r\n        user: this.tokenStorage.getDaUsername() || '<EMAIL>',\r\n        userInputs: { question: message },\r\n      };\r\n\r\n      if (this.agentFilesUploadedData.length > 0) {\r\n        const fileWrapper = this.agentFilesUploadedData[0];\r\n        let displayMessage: string;\r\n        if (this.agentFilesUploadedData.length > 0) {\r\n          const fileNames = this.agentFilesUploadedData\r\n            .map((file) => file.documentName)\r\n            .join(', ');\r\n          displayMessage = `📎 Attached files: ${fileNames}`;\r\n\r\n          this.chatMessages = [{ from: 'user', text: displayMessage }];\r\n        }\r\n        this.agentPlaygroundService\r\n          .submitAgentExecuteWithFile(payload, fileWrapper)\r\n          .pipe(\r\n            finalize(() => {\r\n              this.isProcessingChat = false;\r\n              this.isAgentPlaygroundLoading = false;\r\n            }),\r\n            takeUntil(this.agentPlaygroundDestroy),\r\n          )\r\n          .subscribe({\r\n            next: (res) => this.handleAgentExecuteResponse(res, message),\r\n            error: (err: any) => {\r\n              this.chatMessages = [\r\n                ...this.chatMessages,\r\n                { from: 'user', text: message },\r\n                {\r\n                  from: 'ai',\r\n                  text:\r\n                    err?.error?.message ||\r\n                    err?.message ||\r\n                    'Something went wrong.',\r\n                },\r\n              ];\r\n            },\r\n          });\r\n      } else {\r\n        this.agentPlaygroundService\r\n          .submitAgentExecute(payload)\r\n          .pipe(\r\n            finalize(() => {\r\n              this.isProcessingChat = false;\r\n              this.isAgentPlaygroundLoading = false;\r\n            }),\r\n            takeUntil(this.agentPlaygroundDestroy),\r\n          )\r\n          .subscribe({\r\n            next: (res) => this.handleAgentExecuteResponse(res, message),\r\n            error: (err: any) => {\r\n              this.chatMessages = [\r\n                ...this.chatMessages,\r\n                { from: 'user', text: message },\r\n                {\r\n                  from: 'ai',\r\n                  text:\r\n                    err?.error?.message ||\r\n                    err?.message ||\r\n                    'Something went wrong.',\r\n                },\r\n              ];\r\n            },\r\n          });\r\n      }\r\n    }\r\n  }\r\n\r\n  handleAgentExecuteResponse(response: any, message: string): void {\r\n    try {\r\n      const outputRaw = response?.agentResponse?.agent?.output;\r\n      let formattedOutput = '';\r\n\r\n      if (outputRaw) {\r\n        // Directly replace escaped \\n with real newlines\r\n        formattedOutput = outputRaw.replace(/\\\\n/g, '\\n');\r\n      } else {\r\n        formattedOutput = response?.agentResponse?.detail;\r\n      }\r\n\r\n      this.chatMessages = [\r\n        ...this.chatMessages,\r\n        { from: 'user', text: message },\r\n        { from: 'ai', text: formattedOutput || 'No response from agent.' },\r\n      ];\r\n    } catch (err: any) {\r\n      this.chatMessages = [\r\n        ...this.chatMessages,\r\n        {\r\n          from: 'ai',\r\n          text: err?.message || 'Agent response could not be processed.',\r\n        },\r\n      ];\r\n    }\r\n  }\r\n\r\n  private processAgentFilesAndSendMessage(\r\n    message: string,\r\n    mode: string,\r\n    useCaseIdentifier: string,\r\n    isConversational: boolean,\r\n    isUseTemplate: boolean,\r\n  ): void {\r\n    const formData = new FormData();\r\n    this.agentFilesUploadedData.forEach((fileData) => {\r\n      if (fileData.file) {\r\n        formData.append('files', fileData.file);\r\n      }\r\n    });\r\n\r\n    if (formData.has('files')) {\r\n      this.agentPlaygroundService\r\n        .getFileToContent(formData)\r\n        .pipe(\r\n          switchMap((fileResponse) => {\r\n            const fileContent =\r\n              fileResponse?.fileResponses\r\n                ?.map((response: any) => response.fileContent)\r\n                ?.join('\\n') || '';\r\n            this.sendAgentMessageToAPIWithFiles(\r\n              message,\r\n              mode,\r\n              useCaseIdentifier,\r\n              isConversational,\r\n              isUseTemplate,\r\n              fileContent,\r\n            );\r\n            return of(null);\r\n          }),\r\n          catchError((error) => {\r\n            console.error('Error parsing files:', error);\r\n            this.sendAgentMessageToAPI(\r\n              message,\r\n              mode,\r\n              useCaseIdentifier,\r\n              isConversational,\r\n              isUseTemplate,\r\n            );\r\n            return of(null);\r\n          }),\r\n        )\r\n        .subscribe();\r\n    } else {\r\n      this.sendAgentMessageToAPI(\r\n        message,\r\n        mode,\r\n        useCaseIdentifier,\r\n        isConversational,\r\n        isUseTemplate,\r\n      );\r\n    }\r\n  }\r\n\r\n  private sendAgentMessageToAPI(\r\n    message: string,\r\n    mode: string,\r\n    useCaseIdentifier: string,\r\n    isConversational: boolean,\r\n    isUseTemplate: boolean,\r\n  ): void {\r\n    if (isConversational) {\r\n      this.agentChatPayload.push({ content: message, role: 'user' });\r\n    }\r\n\r\n    const payload = isConversational ? this.agentChatPayload : message;\r\n    const { levelId } = this.getMetadataFromNavbar();\r\n\r\n    this.agentPlaygroundService\r\n      .generatePrompt(\r\n        payload,\r\n        mode,\r\n        isConversational,\r\n        isUseTemplate,\r\n        this.agentAttachment,\r\n        useCaseIdentifier,\r\n        '',\r\n        levelId,\r\n      )\r\n      .pipe(\r\n        finalize(() => {\r\n          this.isProcessingChat = false;\r\n          this.isAgentPlaygroundLoading = false;\r\n        }),\r\n        takeUntil(this.agentPlaygroundDestroy),\r\n      )\r\n      .subscribe({\r\n        next: (generatedResponse: any) => {\r\n          if (\r\n            generatedResponse?.response &&\r\n            generatedResponse?.response?.choices\r\n          ) {\r\n            const aiResponseText = generatedResponse.response.choices[0].text;\r\n            this.chatMessages = [\r\n              ...this.chatMessages,\r\n              { from: 'ai', text: aiResponseText },\r\n            ];\r\n            if (isConversational) {\r\n              this.agentChatPayload.push({\r\n                content: aiResponseText,\r\n                role: 'assistant',\r\n              });\r\n            }\r\n          } else {\r\n            console.warn('Unexpected API response format:', generatedResponse);\r\n            this.showAgentError(\r\n              'Received unexpected response format from API.',\r\n            );\r\n          }\r\n        },\r\n        error: (error: any) => {\r\n          console.error('API Error:', error);\r\n          const errorMessage =\r\n            error?.error?.message ||\r\n            'An error occurred while processing your request.';\r\n          this.showAgentError(errorMessage);\r\n          if (isConversational && this.agentChatPayload.length > 0) {\r\n            this.agentChatPayload.pop();\r\n          }\r\n        },\r\n      });\r\n  }\r\n\r\n  private sendAgentMessageToAPIWithFiles(\r\n    message: string,\r\n    mode: string,\r\n    useCaseIdentifier: string,\r\n    isConversational: boolean,\r\n    isUseTemplate: boolean,\r\n    fileContents: string,\r\n  ): void {\r\n    if (isConversational) {\r\n      this.agentChatPayload.push({ content: message, role: 'user' });\r\n    }\r\n    const payload = isConversational ? this.agentChatPayload : message;\r\n    const { levelId } = this.getMetadataFromNavbar();\r\n\r\n    this.agentPlaygroundService\r\n      .generatePrompt(\r\n        payload,\r\n        mode,\r\n        isConversational,\r\n        isUseTemplate,\r\n        this.agentAttachment,\r\n        useCaseIdentifier,\r\n        fileContents,\r\n        levelId,\r\n      )\r\n      .pipe(\r\n        finalize(() => {\r\n          this.isProcessingChat = false;\r\n          this.isAgentPlaygroundLoading = false;\r\n        }),\r\n        takeUntil(this.agentPlaygroundDestroy),\r\n      )\r\n      .subscribe({\r\n        next: (generatedResponse: any) => {\r\n          if (\r\n            generatedResponse?.response &&\r\n            generatedResponse?.response?.choices\r\n          ) {\r\n            const aiResponseText = generatedResponse.response.choices[0].text;\r\n            this.chatMessages = [\r\n              ...this.chatMessages,\r\n              { from: 'ai', text: aiResponseText },\r\n            ];\r\n            if (isConversational) {\r\n              this.agentChatPayload.push({\r\n                content: aiResponseText,\r\n                role: 'assistant',\r\n              });\r\n            }\r\n          } else {\r\n            console.warn('Unexpected API response format:', generatedResponse);\r\n            this.showAgentError(\r\n              'Received unexpected response format from API.',\r\n            );\r\n          }\r\n        },\r\n        error: (error: any) => {\r\n          console.error('API Error:', error);\r\n          const errorMessage =\r\n            error?.error?.message ||\r\n            'An error occurred while processing your request.';\r\n          this.showAgentError(errorMessage);\r\n          if (isConversational && this.agentChatPayload.length > 0) {\r\n            this.agentChatPayload.pop();\r\n          }\r\n        },\r\n      });\r\n  }\r\n\r\n  private showAgentError(message: string): void {\r\n    this.chatMessages = [...this.chatMessages, { from: 'ai', text: message }];\r\n  }\r\n\r\n  onPlaygroundConversationalToggle(event: boolean): void {\r\n    this.agentPlaygroundForm.get('isConversational')?.setValue(event);\r\n  }\r\n\r\n  onPlaygroundTemplateToggle(event: boolean): void {\r\n    this.agentPlaygroundForm.get('isUseTemplate')?.setValue(event);\r\n  }\r\n\r\n  onFilesSelected(files: any[]): void {\r\n    this.agentFilesUploadedData = files;\r\n  }\r\n\r\n  onAgentConversationalToggle(event: any): void {\r\n    const isConversational = event;\r\n    this.agentPlaygroundForm\r\n      .get('isConversational')\r\n      ?.setValue(isConversational);\r\n    if (\r\n      isConversational &&\r\n      this.agentPlaygroundForm.get('isUseTemplate')?.value\r\n    ) {\r\n      this.agentPlaygroundForm.get('isUseTemplate')?.setValue(false);\r\n    }\r\n  }\r\n\r\n  onAgentTemplateToggle(event: any): void {\r\n    const isUseTemplate = event;\r\n    this.agentPlaygroundForm.get('isUseTemplate')?.setValue(isUseTemplate);\r\n    if (\r\n      isUseTemplate &&\r\n      this.agentPlaygroundForm.get('isConversational')?.value\r\n    ) {\r\n      this.agentPlaygroundForm.get('isConversational')?.setValue(false);\r\n    }\r\n  }\r\n\r\n  onAgentFileSelected(event: any): void {\r\n    const files = event.target.files;\r\n    if (files && files.length > 0) {\r\n      this.agentFilesUploadedData = [];\r\n      for (let i = 0; i < files.length; i++) {\r\n        const file = files[i];\r\n        this.agentFilesUploadedData.push({\r\n          id: `agent_file_${Date.now()}_${i}`,\r\n          documentName: file.name,\r\n          isImage: file.type.startsWith('image/'),\r\n          file: file,\r\n        });\r\n      }\r\n    }\r\n  }\r\n\r\n  removeAgentFile(index: number): void {\r\n    this.agentFilesUploadedData.splice(index, 1);\r\n  }\r\n\r\n  clearAgentChatData(): void {\r\n    this.chatMessages = [\r\n      {\r\n        from: 'ai',\r\n        text: 'Hi there, how can I help you test your agent today?',\r\n      },\r\n    ];\r\n    this.agentChatPayload = [];\r\n    this.agentFilesUploadedData = [];\r\n    this.agentAttachment = [];\r\n  }\r\n\r\n  showResponseModal(message: string, isError: boolean = false): void {\r\n    this.modalMessage = message;\r\n    this.isModalError = isError;\r\n    this.isResponseModalOpen = true;\r\n  }\r\n\r\n  closeResponseModal(): void {\r\n    this.isResponseModalOpen = false;\r\n    this.modalMessage = '';\r\n    this.isModalError = false;\r\n  }\r\n\r\n  public loadAgentDetailsForPlayground(): void {\r\n    if (this.currentAgentDetails) {\r\n      const agentData = this.currentAgentDetails;\r\n      this.promptOptions = [\r\n        {\r\n          value:\r\n            agentData.useCaseCode ||\r\n            agentData.code ||\r\n            this.currentAgentId ||\r\n            'DEFAULT_AGENT',\r\n          name: agentData.useCaseName || agentData.name || 'Current Agent',\r\n          agentData: agentData,\r\n        } as any,\r\n      ];\r\n\r\n      this.selectedPrompt = this.promptOptions[0].value.toString();\r\n      this.selectedAgentMode = agentData.useCaseCode || agentData.code || '';\r\n\r\n      if (agentData.organizationPath) {\r\n        this.selectedUseCaseIdentifier = agentData.organizationPath;\r\n      } else {\r\n        const orgPath = this.buildOrganizationPath();\r\n        this.selectedUseCaseIdentifier = `${this.selectedAgentMode}${orgPath}`;\r\n      }\r\n    }\r\n\r\n    this.promptOptions.unshift({ value: 'default', name: 'Choose an Agent' });\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  private autoSelectCurrentAgent(): void {\r\n    if (!this.promptOptions?.length || !this.agentName) return;\r\n\r\n    // Find by code/value first, then fallback to name\r\n    const currentAgentOption = this.promptOptions.find((option) => {\r\n      const agentData = (option as any).agentData;\r\n      return (\r\n        agentData?.code === this.agentCode ||\r\n        agentData?.useCaseCode === this.agentCode ||\r\n        option.value === this.agentCode ||\r\n        option.name === this.agentName\r\n      );\r\n    });\r\n\r\n    if (!currentAgentOption) return;\r\n\r\n    this.selectedPrompt = currentAgentOption.value.toString(); // Use value, not name\r\n    this.onPromptChanged(currentAgentOption);\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  onPromptChanged(selectedOption: DropdownOption): void {\r\n    const agentData = (selectedOption as any).agentData;\r\n    const agentCode = agentData?.code || selectedOption.value.toString();\r\n\r\n    this.selectedPrompt = selectedOption.name;\r\n    this.agentCode = agentCode;\r\n    this.selectedAgentMode = agentCode;\r\n\r\n    if (agentData?.useCaseIdentifier) {\r\n      this.selectedUseCaseIdentifier = agentData.useCaseIdentifier;\r\n    } else {\r\n      this.selectedUseCaseIdentifier = `${agentCode}${this.buildOrganizationPath()}`;\r\n    }\r\n\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  // Exit execute mode\r\n  onExitExecuteMode(): void {\r\n    this.isExecuteMode = false;\r\n    this.showChatInterface = false;\r\n\r\n    // Clear execute nodes and restore original canvas nodes\r\n    this.executeNodes = [];\r\n    this.canvasNodes = this.buildAgentNodes.map((node) => ({\r\n      id: node.id,\r\n      type: 'build-agent',\r\n      data: { ...node, width: 90 },\r\n      position: node.position,\r\n    }));\r\n\r\n    // Recreate connections based on hierarchy\r\n    this.createAgentFlowConnections(this.buildAgentNodes);\r\n\r\n    // Removed complex positioning - using simple vertical stacking\r\n\r\n    // Force connection points update after a delay to ensure DOM is updated\r\n    setTimeout(() => {\r\n      this.cdr.detectChanges();\r\n    }, 200);\r\n\r\n    // Clean up execution subscription\r\n    if (this.executionSubscription) {\r\n      this.executionSubscription.unsubscribe();\r\n      this.executionSubscription = undefined;\r\n    }\r\n\r\n    // Stop execution service\r\n    this.toolExecutionService.stopExecution();\r\n  }\r\n\r\n  onCanvasStateChanged(state: {\r\n    nodes: CanvasNode[];\r\n    edges: CanvasEdge[];\r\n  }): void {\r\n    // Only update edges, don't override our controlled node positions\r\n    this.canvasEdges = state.edges;\r\n\r\n    // Ensure connection points are updated when state changes\r\n    setTimeout(() => {\r\n      if (this.canvasBoardComponent) {\r\n        this.canvasBoardComponent.updateNodeConnectionPoints();\r\n      }\r\n    }, 100);\r\n  }\r\n\r\n  // Handle agent name changes from canvas board\r\n  onAgentNameChanged(agentName: string): void {\r\n    this.agentName = agentName;\r\n  }\r\n\r\n  // Handle metadata changes from canvas board\r\n  onMetadataChanged(metadata: {\r\n    org: string;\r\n    domain: string;\r\n    project: string;\r\n    team: string;\r\n  }): void {\r\n    this.agentMetadata = metadata;\r\n  }\r\n\r\n  // Handle agent details changes from canvas board\r\n  onAgentDetailsChanged(details: {\r\n    name: string;\r\n    useCaseDetails: string;\r\n  }): void {\r\n    this.agentName = details.name;\r\n    this.agentDetail = details.useCaseDetails;\r\n  }\r\n\r\n  // Get active tab label for display\r\n  getActiveTabLabel(): string {\r\n    const activeTabObj = this.tabs.find((tab) => tab.id === this.activeTab);\r\n    return activeTabObj ? activeTabObj.label || 'Item' : 'Item';\r\n  }\r\n\r\n  // Create new item action (dynamic based on active tab)\r\n  onCreateNewItem(): void {\r\n    // Navigate to appropriate creation page based on active tab\r\n    switch (this.activeTab) {\r\n      case 'prompts':\r\n        this.router.navigate(['/libraries/prompts/create']);\r\n        break;\r\n      case 'models':\r\n        this.router.navigate(['/libraries/models/create']);\r\n        break;\r\n      case 'knowledge':\r\n        this.router.navigate(['/libraries/knowledge-base/create']);\r\n        break;\r\n      case 'tools':\r\n        this.router.navigate(['/libraries/tools/create']);\r\n        break;\r\n      case 'guardrails':\r\n        this.router.navigate(['/libraries/guardrails/create']);\r\n        break;\r\n      default:\r\n        console.warn(`No creation route defined for tab: ${this.activeTab}`);\r\n    }\r\n  }\r\n\r\n  // Reposition all nodes for execute mode\r\n  private repositionNodesForExecuteMode(): void {\r\n    // Use the original createExecuteNodes approach\r\n    this.createExecuteNodes();\r\n\r\n    // Force immediate update\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  // Calculate automatic position - simple vertical stacking below latest node\r\n  private calculateAutoPosition(nodeType: string): { x: number; y: number } {\r\n    console.log('🎯 calculateAutoPosition called:', {\r\n      nodeType,\r\n      isExecuteMode: this.isExecuteMode,\r\n      buildAgentNodesLength: this.buildAgentNodes.length,\r\n      latestNodeId: this.latestNodeId,\r\n      existingNodePositions: this.buildAgentNodes.map((n) => ({\r\n        id: n.id,\r\n        pos: n.position,\r\n      })),\r\n    });\r\n\r\n    if (this.isExecuteMode) {\r\n      // Execute mode - vertical line of small agent icons (properly centered)\r\n      const centerX = 200; // Better center position for the canvas\r\n      const startY = 100; // Start position from top\r\n      const verticalSpacing = 80; // More spacing for better visual separation\r\n\r\n      // Calculate position based on current number of nodes\r\n      const nodeIndex = this.buildAgentNodes.length;\r\n      const executePosition = {\r\n        x: centerX,\r\n        y: startY + nodeIndex * verticalSpacing,\r\n      };\r\n      console.log(\r\n        '🎯 Execute mode position calculated (vertical icons):',\r\n        executePosition,\r\n      );\r\n      return executePosition;\r\n    }\r\n\r\n    // Build mode - Simple vertical positioning below latest node\r\n    const simplePosition = this.calculateSimpleVerticalPosition();\r\n    console.log('🎯 Build mode position calculated:', simplePosition);\r\n    return simplePosition;\r\n  }\r\n\r\n  // Simple vertical positioning - place new node below the latest node\r\n  private calculateSimpleVerticalPosition(): { x: number; y: number } {\r\n    const defaultStartX = 100; // Default starting X position\r\n    const defaultStartY = 150; // Default starting Y position\r\n    const verticalSpacing = 120; // Space between nodes vertically\r\n\r\n    console.log('📍 calculateSimpleVerticalPosition - Constants:', {\r\n      defaultStartX,\r\n      defaultStartY,\r\n      verticalSpacing,\r\n    });\r\n\r\n    // If no nodes exist or no latest node tracked, use default start position\r\n    if (this.buildAgentNodes.length === 0 || !this.latestNodeId) {\r\n      console.log('📍 Using default position (no nodes or no latest):', {\r\n        nodesLength: this.buildAgentNodes.length,\r\n        latestNodeId: this.latestNodeId,\r\n        defaultPosition: { x: defaultStartX, y: defaultStartY },\r\n      });\r\n      return { x: defaultStartX, y: defaultStartY };\r\n    }\r\n\r\n    // Find the latest node\r\n    const latestNode = this.buildAgentNodes.find(\r\n      (node) => node.id === this.latestNodeId,\r\n    );\r\n\r\n    console.log('📍 Latest node search:', {\r\n      searchingForId: this.latestNodeId,\r\n      foundNode: latestNode,\r\n      allNodeIds: this.buildAgentNodes.map((n) => n.id),\r\n    });\r\n\r\n    if (!latestNode) {\r\n      // Fallback: if latest node not found, use default position\r\n      console.log('📍 Latest node not found, using default position');\r\n      return { x: defaultStartX, y: defaultStartY };\r\n    }\r\n\r\n    // Place new node directly below the latest node\r\n    const newPosition = {\r\n      x: latestNode.position.x, // Same X coordinate (vertical alignment)\r\n      y: latestNode.position.y + verticalSpacing, // Below with spacing\r\n    };\r\n\r\n    console.log('📍 Calculated new position below latest node:', {\r\n      latestNodePosition: latestNode.position,\r\n      newPosition,\r\n      spacing: verticalSpacing,\r\n    });\r\n\r\n    return newPosition;\r\n  }\r\n\r\n  // Force our calculated positions to override any canvas interference\r\n  private forceCorrectPositions(): void {\r\n    // Update canvas nodes to match our controlled build agent node positions\r\n    this.canvasNodes = this.buildAgentNodes.map((node) => ({\r\n      id: node.id,\r\n      type: 'build-agent',\r\n      data: { ...node, width: this.isExecuteMode ? 55 : 90 },\r\n      position: node.position, // Use our controlled position\r\n    }));\r\n\r\n    // Force change detection immediately\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  // Force execute mode positions using DOM manipulation\r\n  private forceExecutePositions(): void {\r\n    if (!this.isExecuteMode) return;\r\n\r\n    this.buildAgentNodes.forEach((node, index) => {\r\n      const executePosition = {\r\n        x: 40,\r\n        y: 100 + index * 80,\r\n      };\r\n\r\n      // Force DOM position using CSS variables\r\n      const nodeElement = document.querySelector(\r\n        `[data-node-id=\"${node.id}\"]`,\r\n      ) as HTMLElement;\r\n      if (nodeElement) {\r\n        nodeElement.style.setProperty('--node-x', `${executePosition.x}px`);\r\n        nodeElement.style.setProperty('--node-y', `${executePosition.y}px`);\r\n      }\r\n\r\n      // Also update our data\r\n      node.position = { ...executePosition };\r\n    });\r\n\r\n    // Update canvas nodes to match\r\n    this.canvasNodes = this.buildAgentNodes.map((node, index) => ({\r\n      id: node.id,\r\n      type: 'build-agent',\r\n      data: {\r\n        ...node,\r\n        width: 55,\r\n        position: node.position,\r\n      },\r\n      position: node.position,\r\n    }));\r\n\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  // Force a specific node to a specific position using DOM manipulation\r\n  private forceNodeToPosition(\r\n    nodeId: string,\r\n    position: { x: number; y: number },\r\n  ): void {\r\n    console.log('🎯 forceNodeToPosition called:', { nodeId, position });\r\n\r\n    const nodeElement = document.querySelector(\r\n      `[data-node-id=\"${nodeId}\"]`,\r\n    ) as HTMLElement;\r\n    if (nodeElement) {\r\n      console.log('🎯 Found node element, forcing position via CSS');\r\n      // Force position using CSS transform (override CDK drag)\r\n      nodeElement.style.position = 'absolute';\r\n      nodeElement.style.left = `${position.x}px`;\r\n      nodeElement.style.top = `${position.y}px`;\r\n      nodeElement.style.transform = ''; // Clear any CDK transform\r\n\r\n      // Also update our internal data to match\r\n      const buildAgentIndex = this.buildAgentNodes.findIndex(\r\n        (n) => n.id === nodeId,\r\n      );\r\n      if (buildAgentIndex !== -1) {\r\n        this.buildAgentNodes[buildAgentIndex] = {\r\n          ...this.buildAgentNodes[buildAgentIndex],\r\n          position: position,\r\n        };\r\n      }\r\n\r\n      const canvasIndex = this.canvasNodes.findIndex((n) => n.id === nodeId);\r\n      if (canvasIndex !== -1) {\r\n        this.canvasNodes[canvasIndex] = {\r\n          ...this.canvasNodes[canvasIndex],\r\n          position: position,\r\n        };\r\n      }\r\n\r\n      console.log('🎯 Position forced successfully');\r\n    } else {\r\n      console.log('🎯 Node element not found, retrying...');\r\n      // Retry after a short delay if element not found\r\n      setTimeout(() => {\r\n        this.forceNodeToPosition(nodeId, position);\r\n      }, 100);\r\n    }\r\n  }\r\n\r\n  // Simple method to create a connection between two nodes\r\n  private createConnection(sourceId: string, targetId: string): void {\r\n    // Validate that both nodes exist\r\n    const sourceNode = this.buildAgentNodes.find(\r\n      (node) => node.id === sourceId,\r\n    );\r\n    const targetNode = this.buildAgentNodes.find(\r\n      (node) => node.id === targetId,\r\n    );\r\n\r\n    if (!sourceNode || !targetNode) {\r\n      console.warn(\r\n        `Cannot create connection: source or target node not found. Source: ${sourceId}, Target: ${targetId}`,\r\n      );\r\n      return;\r\n    }\r\n\r\n    // Check if connection already exists\r\n    const existingConnection = this.canvasEdges.find(\r\n      (edge) => edge.source === sourceId && edge.target === targetId,\r\n    );\r\n\r\n    if (existingConnection) {\r\n      return; // Connection already exists\r\n    }\r\n\r\n    const newEdge: CanvasEdge = {\r\n      id: `edge_${sourceId}_${targetId}`,\r\n      source: sourceId,\r\n      target: targetId,\r\n      animated: false, // Simple static connections\r\n    };\r\n\r\n    this.canvasEdges = [...this.canvasEdges, newEdge];\r\n  }\r\n\r\n  // Create simple straight-line connections between nodes in order\r\n  private createAgentFlowConnections(nodes: BuildAgentNodeData[]): void {\r\n    console.log('🔗 createAgentFlowConnections called:', {\r\n      nodesLength: nodes.length,\r\n      nodeIds: nodes.map((n) => n.id),\r\n      nodePositions: nodes.map((n) => ({ id: n.id, position: n.position })),\r\n    });\r\n\r\n    if (nodes.length < 2) {\r\n      console.log('🔗 Not enough nodes for connections, skipping');\r\n      return; // Need at least 2 nodes to create connections\r\n    }\r\n\r\n    // Clear existing edges first\r\n    this.canvasEdges = [];\r\n    console.log('🔗 Cleared existing edges');\r\n\r\n    // Dead simple: connect each node to the next one in sequence\r\n    console.log('🔗 Creating connections...');\r\n    for (let i = 0; i < nodes.length - 1; i++) {\r\n      const sourceId = nodes[i].id;\r\n      const targetId = nodes[i + 1].id;\r\n      console.log(`🔗 Creating connection: ${sourceId} -> ${targetId}`);\r\n      this.createConnection(sourceId, targetId);\r\n    }\r\n\r\n    console.log('🔗 Final edges:', this.canvasEdges);\r\n\r\n    // Force change detection to ensure edges are updated\r\n    this.cdr.detectChanges();\r\n    console.log('🔗 Connection creation completed');\r\n  }\r\n\r\n  // Create consolidated execute nodes from build agent nodes\r\n  private createExecuteNodes(): void {\r\n    // No complex execute nodes - just use the simple positioning\r\n  }\r\n\r\n  // Get execute node data for a given canvas node\r\n  getExecuteNodeData(node: CanvasNode): ExecuteNodeData | undefined {\r\n    if (!this.isExecuteMode) return undefined;\r\n\r\n    // Create executeNodeData from the individual node data for icons to work\r\n    const nodeData = node.data;\r\n    if (!nodeData) return undefined;\r\n\r\n    return {\r\n      type: nodeData.type,\r\n      nodes: [nodeData], // Single node array\r\n      position: nodeData.position || node.position,\r\n    };\r\n  }\r\n\r\n  // Generate a unique node ID - similar to workflow editor\r\n  generateNodeId(): string {\r\n    return `node_${Date.now()}_${Math.floor(Math.random() * 1000)}`;\r\n  }\r\n\r\n  // Load agent data from catalogue\r\n  private loadAgentData(agentId: string): void {\r\n    // For collaborative agents, don't load labels API - proceed directly\r\n    if (this.currentAgentType === 'collaborative') {\r\n      this.loadAgentDataAfterLabels(agentId);\r\n      return;\r\n    }\r\n\r\n    // For individual agents, ensure labels data is loaded first\r\n    if (!this.labelsCache) {\r\n      this.agentService.getLabels().subscribe({\r\n        next: (response: any) => {\r\n          this.labelsCache = response;\r\n\r\n          // Load all data from cached labels\r\n          this.loadModelsFromCache();\r\n          this.loadKnowledgeBaseFromCache();\r\n          this.loadGuardrailsFromCache();\r\n\r\n          // Now load the agent data\r\n          this.loadAgentDataAfterLabels(agentId);\r\n        },\r\n        error: (error) => {\r\n          console.error('Error loading labels for agent mapping:', error);\r\n          // Proceed anyway with existing data\r\n          this.loadAgentDataAfterLabels(agentId);\r\n        },\r\n      });\r\n      return;\r\n    }\r\n\r\n    this.loadAgentDataAfterLabels(agentId);\r\n  }\r\n\r\n  // Load agent data after ensuring labels are loaded\r\n  private loadAgentDataAfterLabels(agentId: string): void {\r\n    if (this.currentAgentType === 'collaborative') {\r\n      this.agentService.getCollaborativeAgentDetailsById(agentId).subscribe({\r\n        next: (response) => {\r\n          // Handle the new API response format\r\n          let agentData = null;\r\n          if (response && response.agentDetail) {\r\n            // New API format: data is in agentDetail property\r\n            agentData = response.agentDetail;\r\n          } else if (\r\n            response &&\r\n            (response.agentDetails || response.agentDetail)\r\n          ) {\r\n            // Fallback to old format\r\n            agentData = response.agentDetails || response.agentDetail;\r\n          } else if (response && response.data) {\r\n            // Another possible format\r\n            agentData = response.data;\r\n          }\r\n\r\n          if (agentData) {\r\n            // Store agent details for playground display\r\n            this.currentAgentDetails = {\r\n              name: agentData.name || 'Unnamed Agent',\r\n              description:\r\n                agentData.description || agentData.agentDetails || '',\r\n              role: agentData.role || '',\r\n              goal: agentData.goal || '',\r\n              backstory: agentData.backstory || '',\r\n              expectedOutput: agentData.expectedOutput || '',\r\n            };\r\n\r\n            // Create a response object in the expected format for mapping\r\n            const mappedResponse = {\r\n              agentDetail: agentData,\r\n              agentDetails: agentData, // Keep both for compatibility\r\n            };\r\n            this.mapCollaborativeAgentDataToCanvas(mappedResponse);\r\n          } else {\r\n            console.warn(\r\n              'No collaborative agent data found in response:',\r\n              response,\r\n            );\r\n            this.showErrorMessage(\r\n              'Agent Not Found',\r\n              'No agent data found. The agent might not exist or you might not have permission to view it.',\r\n            );\r\n          }\r\n        },\r\n        error: (error) => {\r\n          console.error('Error loading collaborative agent data:', error);\r\n          this.showErrorMessage(\r\n            'Load Failed',\r\n            'Failed to load collaborative agent data. Please check the console for details.',\r\n          );\r\n        },\r\n      });\r\n    } else {\r\n      this.agentService.getAgentById(agentId).subscribe({\r\n        next: (response) => {\r\n          if (response && (response.useCaseName || response.name)) {\r\n            // Store agent details for playground display with all necessary data\r\n            this.currentAgentDetails = {\r\n              name: response.name || response.useCaseName || 'Unnamed Agent',\r\n              description:\r\n                response.description || response.useCaseDescription || '',\r\n              role: response.role || '',\r\n              goal: response.goal || '',\r\n              backstory: response.backstory || '',\r\n              expectedOutput: response.expectedOutput || '',\r\n              // Add playground-specific data from the API response\r\n              useCaseCode: response.useCaseCode || response.code || '',\r\n              useCaseName: response.useCaseName || response.name || '',\r\n              useCaseId: response.useCaseId || response.id || '',\r\n              organizationPath: response.organizationPath || '',\r\n              // Extract config data for mode and useCaseIdentifier\r\n              config: response.config || [],\r\n            };\r\n\r\n            this.mapAgentDataToCanvas(response);\r\n          } else {\r\n            console.warn(\r\n              'No individual agent data found in response:',\r\n              response,\r\n            );\r\n            this.showErrorMessage(\r\n              'Agent Not Found',\r\n              'No agent data found. The agent might not exist or you might not have permission to view it.',\r\n            );\r\n          }\r\n        },\r\n        error: (error) => {\r\n          console.error('Error loading individual agent data:', error);\r\n          this.showErrorMessage(\r\n            'Load Failed',\r\n            'Failed to load individual agent data. Please check the console for details.',\r\n          );\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  // Map agent data to canvas board\r\n  private mapAgentDataToCanvas(agentData: any): void {\r\n    if (!agentData) {\r\n      console.error('No individual agent data provided');\r\n      return;\r\n    }\r\n\r\n    // Set basic agent information using the actual API response structure\r\n    // In duplicate mode, clear the name field so user can enter a new name\r\n    if (this.isDuplicateMode) {\r\n      this.agentName = ''; // Clear name for duplicate mode\r\n    } else {\r\n      if (agentData.useCaseName) {\r\n        this.agentName = agentData.useCaseName;\r\n      } else if (agentData.name) {\r\n        this.agentName = agentData.name;\r\n      }\r\n    }\r\n\r\n    // Extract agent code for API calls\r\n    if (agentData.useCaseCode) {\r\n      this.agentCode = agentData.useCaseCode;\r\n    } else if (agentData.code) {\r\n      this.agentCode = agentData.code;\r\n    } else {\r\n      // Fallback: generate code from agent name\r\n      this.agentCode = this.agentName.trim().replace(/\\s+/g, '_').toUpperCase();\r\n    }\r\n\r\n    if (agentData.useCaseDetails) {\r\n      this.agentDetail = agentData.useCaseDetails;\r\n    } else if (agentData.description) {\r\n      this.agentDetail = agentData.description;\r\n    } else if (agentData.agentDetail) {\r\n      this.agentDetail = agentData.agentDetail;\r\n    }\r\n\r\n    // Set metadata from the API response\r\n    if (agentData.organizationPath) {\r\n      const pathParts = agentData.organizationPath.split('@');\r\n      if (pathParts.length >= 5) {\r\n        this.agentMetadata = {\r\n          org: pathParts[1] || agentData.org || '',\r\n          domain: pathParts[2] || agentData.domain || '',\r\n          project: pathParts[3] || agentData.project || '',\r\n          team: pathParts[4] || agentData.team || '',\r\n        };\r\n      }\r\n    } else {\r\n      this.agentMetadata = {\r\n        org: agentData.org || '',\r\n        domain: agentData.domain || '',\r\n        project: agentData.project || '',\r\n        team: agentData.team || '',\r\n      };\r\n    }\r\n\r\n    // Map individual agent data to canvas nodes based on config array\r\n    // Use a temporary array to collect nodes in hierarchy order\r\n    const nodesByType: { [key: string]: BuildAgentNodeData[] } = {\r\n      prompt: [],\r\n      model: [],\r\n      knowledge: [],\r\n      guardrail: [],\r\n      tool: [],\r\n    };\r\n    let nodeCounter = 1;\r\n\r\n    if (agentData.prompt || agentData.useCaseDetails || agentData.useCaseName) {\r\n      let promptName = agentData.prompt || 'Default Prompt';\r\n\r\n      // Try to find the exact prompt by name from the loaded prompts\r\n      let promptData = this.allToolItems['prompts']?.find(\r\n        (p) =>\r\n          p.name === promptName ||\r\n          p.name.toLowerCase() === promptName.toLowerCase(),\r\n      );\r\n\r\n      // If not found by name, use the first available prompt as fallback\r\n      if (!promptData && this.allToolItems['prompts']?.length > 0) {\r\n        promptData = this.allToolItems['prompts'][0];\r\n        promptName = promptData.name; // Use the actual prompt name from the data\r\n      }\r\n\r\n      // Always create a prompt node (essential for individual agents)\r\n      const promptNode: BuildAgentNodeData = {\r\n        id: `prompt-${nodeCounter++}`,\r\n        name: promptName,\r\n        type: 'prompt',\r\n        position: { x: 0, y: 0 }, // Will be calculated later\r\n      };\r\n      nodesByType['prompt'].push(promptNode);\r\n    }\r\n\r\n    // Process the config array to extract model, tools, knowledge bases, etc.\r\n    if (agentData.config && Array.isArray(agentData.config)) {\r\n      agentData.config.forEach((category: any) => {\r\n        if (category.config && Array.isArray(category.config)) {\r\n          category.config.forEach((configItem: any) => {\r\n            // Store configId for update operations\r\n            if (configItem.configId) {\r\n              const configKey = `${category.categoryId}-${configItem.configKey}`;\r\n              this.agentConfigIds.set(configKey, configItem.configId);\r\n            }\r\n            // Handle MODEL configuration\r\n            if (configItem.configKey === 'MODEL' && configItem.configValue) {\r\n              const modelId = configItem.configValue;\r\n\r\n              const modelData = this.allToolItems['models']?.find(\r\n                (m) =>\r\n                  m.id === modelId ||\r\n                  m.id === modelId.toString() ||\r\n                  m.id.toString() === modelId,\r\n              );\r\n\r\n              if (modelData) {\r\n                nodesByType['model'].push({\r\n                  id: `model-${nodeCounter++}`,\r\n                  type: 'model',\r\n                  name: modelData.name,\r\n                  icon: undefined, // Let node component use Lucide icon\r\n                  position: { x: 0, y: 0 }, // Will be calculated later\r\n                  originalToolData: modelData, // Store the original tool data for ID retrieval\r\n                });\r\n              } else {\r\n                // Create a placeholder model node if not found\r\n                nodesByType['model'].push({\r\n                  id: `model-${nodeCounter++}`,\r\n                  type: 'model',\r\n                  name: `Model ID: ${modelId}`,\r\n                  icon: undefined, // Let node component use Lucide icon\r\n                  position: { x: 0, y: 0 }, // Will be calculated later\r\n                  originalToolData: {\r\n                    id: modelId,\r\n                    name: `Model ID: ${modelId}`,\r\n                  }, // Store the ID for retrieval\r\n                });\r\n              }\r\n            }\r\n\r\n            // Handle RAG_KNOWLEDGEBASE_NAME configuration (can contain comma-separated IDs)\r\n            if (\r\n              configItem.configKey === 'RAG_KNOWLEDGEBASE_NAME' &&\r\n              configItem.configValue\r\n            ) {\r\n              const kbValue = configItem.configValue.toString(); // Ensure it's a string\r\n              const kbIds = kbValue\r\n                .split(',')\r\n                .map((id: string) => id.trim())\r\n                .filter((id: string) => id); // Split by comma and clean\r\n\r\n              // Process each knowledge base ID separately to create individual nodes\r\n              kbIds.forEach((kbId: string) => {\r\n                const knowledgeData = this.allToolItems['knowledge']?.find(\r\n                  (k) => {\r\n                    return (\r\n                      k.id === kbId ||\r\n                      k.id === kbId.toString() ||\r\n                      k.id.toString() === kbId ||\r\n                      k.name === kbId ||\r\n                      k.name.toLowerCase() === kbId.toLowerCase()\r\n                    );\r\n                  },\r\n                );\r\n\r\n                if (knowledgeData) {\r\n                  nodesByType['knowledge'].push({\r\n                    id: `knowledge-${nodeCounter++}`,\r\n                    type: 'knowledge',\r\n                    name: knowledgeData.name,\r\n                    icon: undefined, // Let node component use Lucide icon\r\n                    position: { x: 0, y: 0 }, // Will be calculated later\r\n                    originalToolData: knowledgeData, // Store the original tool data for ID retrieval\r\n                  });\r\n                } else {\r\n                  // Create a placeholder knowledge node if not found in labels\r\n                  // Try to get the name from labels API directly\r\n                  let kbName = `Knowledge Base ID: ${kbId}`;\r\n                  if (this.labelsCache && this.labelsCache.categoryLabels) {\r\n                    const iclCategory = this.labelsCache.categoryLabels.find(\r\n                      (cat: any) => cat.categoryId === 2,\r\n                    );\r\n                    if (iclCategory) {\r\n                      const kbLabel = iclCategory.labels.find(\r\n                        (label: any) =>\r\n                          label.labelCode === 'RAG_KNOWLEDGEBASE_NAME',\r\n                      );\r\n                      if (kbLabel && kbLabel.labelValues) {\r\n                        const kbOptions = this.agentService.parseLabelValues(\r\n                          kbLabel.labelValues,\r\n                        );\r\n                        const kbOption = kbOptions.find(\r\n                          (opt) => opt.value === kbId,\r\n                        );\r\n                        if (kbOption) {\r\n                          kbName = kbOption.name;\r\n                        }\r\n                      }\r\n                    }\r\n                  }\r\n\r\n                  nodesByType['knowledge'].push({\r\n                    id: `knowledge-${nodeCounter++}`,\r\n                    type: 'knowledge',\r\n                    name: kbName,\r\n                    icon: undefined, // Let node component use Lucide icon\r\n                    position: { x: 0, y: 0 }, // Will be calculated later\r\n                    originalToolData: { id: kbId, name: kbName }, // Store the ID for retrieval\r\n                  });\r\n                }\r\n              });\r\n            }\r\n\r\n            // Handle ENABLE_GUARDRAILS - create a general guardrail node when enabled\r\n            if (\r\n              configItem.configKey === 'ENABLE_GUARDRAILS' &&\r\n              (configItem.configValue === 'true' ||\r\n                configItem.configValue === true)\r\n            ) {\r\n              // Only add one general guardrail node if not already added\r\n              if (nodesByType['guardrail'].length === 0) {\r\n                nodesByType['guardrail'].push({\r\n                  id: `guardrail-${nodeCounter++}`,\r\n                  type: 'guardrail',\r\n                  name: 'Guardrails',\r\n                  icon: undefined, // Let node component use Lucide icon\r\n                  position: { x: 0, y: 0 }, // Will be calculated later\r\n                });\r\n              }\r\n            }\r\n\r\n            // Handle specific GUARDRAIL configurations (only show enabled guardrails)\r\n            if (\r\n              configItem.configKey &&\r\n              configItem.configKey.startsWith('GUARDRAIL_') &&\r\n              configItem.configKey !== 'ENABLE_GUARDRAILS' &&\r\n              (configItem.configValue === 'true' ||\r\n                configItem.configValue === true)\r\n            ) {\r\n              const guardrailData = this.allToolItems['guardrails']?.find(\r\n                (g) => (g as any).code === configItem.configKey,\r\n              );\r\n\r\n              if (guardrailData) {\r\n                nodesByType['guardrail'].push({\r\n                  id: `guardrail-${nodeCounter++}`,\r\n                  type: 'guardrail',\r\n                  name: guardrailData.name,\r\n                  icon: undefined, // Let node component use Lucide icon\r\n                  position: { x: 0, y: 0 }, // Will be calculated later\r\n                });\r\n              } else {\r\n                // Create placeholder guardrail node\r\n                const guardrailName = configItem.configKey\r\n                  .replace('GUARDRAIL_', '')\r\n                  .replace(/_/g, ' ');\r\n                nodesByType['guardrail'].push({\r\n                  id: `guardrail-${nodeCounter++}`,\r\n                  type: 'guardrail',\r\n                  name: guardrailName,\r\n                  icon: undefined, // Let node component use Lucide icon\r\n                  position: { x: 0, y: 0 }, // Will be calculated later\r\n                });\r\n              }\r\n            }\r\n          });\r\n        }\r\n      });\r\n    }\r\n\r\n    // Assemble nodes in hierarchy order and calculate positions\r\n    const nodes: BuildAgentNodeData[] = [];\r\n    const flowOrder = ['prompt', 'model', 'knowledge', 'guardrail', 'tool'];\r\n\r\n    // Clear and reset for clean positioning (individual agents)\r\n    this.buildAgentNodes = [];\r\n    this.latestNodeId = null;\r\n\r\n    console.log('🔧 EDIT MODE DETECTION IN INDIVIDUAL MAPPING:', {\r\n      isEditMode: this.isEditMode,\r\n      isViewMode: this.isViewMode,\r\n      isExecuteMode: this.isExecuteMode,\r\n      isDuplicateMode: this.isDuplicateMode,\r\n      totalNodesFound: Object.keys(nodesByType).reduce(\r\n        (total, type) => total + nodesByType[type].length,\r\n        0,\r\n      ),\r\n    });\r\n\r\n    flowOrder.forEach((nodeType) => {\r\n      nodesByType[nodeType].forEach((node) => {\r\n        console.log(\r\n          `🔧 Processing node ${nodes.length + 1}: ${node.name} (${node.type})`,\r\n        );\r\n\r\n        // Use different positioning logic based on mode\r\n        if (this.isEditMode) {\r\n          // In edit mode, use consistent initial positioning\r\n          const nodeIndex = nodes.length; // Current index in the final nodes array\r\n          console.log(\r\n            `🔧 EDIT MODE - calculating position for index ${nodeIndex}`,\r\n          );\r\n          node.position = this.calculateEditModePosition(nodeIndex);\r\n        } else {\r\n          // Use simple vertical positioning for other modes (view, duplicate, etc.)\r\n          console.log(\r\n            `🔧 NON-EDIT MODE - using calculateSimpleVerticalPosition`,\r\n          );\r\n          node.position = this.calculateSimpleVerticalPosition();\r\n        }\r\n\r\n        console.log(`🔧 Final node position: ${JSON.stringify(node.position)}`);\r\n\r\n        nodes.push(node);\r\n        // Add to buildAgentNodes and update latest node for next position calculation\r\n        this.buildAgentNodes.push(node);\r\n        this.latestNodeId = node.id;\r\n      });\r\n    });\r\n\r\n    // Update build agent nodes with final array\r\n    this.buildAgentNodes = nodes;\r\n\r\n    // Update canvas nodes for display\r\n    this.canvasNodes = nodes.map((node) => ({\r\n      id: node.id,\r\n      type: 'build-agent',\r\n      data: { ...node, width: this.isExecuteMode ? 55 : 120 },\r\n      position: node.position,\r\n    }));\r\n\r\n    // Update canvas board with the loaded data first\r\n    this.cdr.detectChanges();\r\n\r\n    // Wait for DOM to update, then create connections\r\n    setTimeout(() => {\r\n      // Clear existing edges when loading new agent data\r\n      this.canvasEdges = [];\r\n\r\n      // Create connections between nodes in the proper flow order\r\n      this.createAgentFlowConnections(nodes);\r\n\r\n      // Force update of connection points after nodes are positioned\r\n      if (this.canvasBoardComponent) {\r\n        this.canvasBoardComponent.updateNodeConnectionPoints();\r\n      }\r\n\r\n      // Trigger change detection again to ensure connections are rendered\r\n      this.cdr.detectChanges();\r\n    }, 200);\r\n\r\n    // In duplicate mode, clear the agent ID after loading data to ensure new agent creation\r\n    if (this.isDuplicateMode) {\r\n      this.currentAgentId = null;\r\n    }\r\n\r\n    // If in execute mode, automatically initialize playground\r\n    if (this.isExecuteMode) {\r\n      // Auto-select this agent in the dropdown when execute mode loads\r\n      this.autoSelectCurrentAgentInDropdown();\r\n      setTimeout(() => {\r\n        this.initializeExecuteMode();\r\n      }, 500);\r\n    }\r\n\r\n    // If in edit mode, ensure connections are properly established\r\n    if (this.isEditMode) {\r\n      setTimeout(() => {\r\n        this.ensureConnectionsInEditMode();\r\n      }, 500);\r\n    }\r\n  }\r\n\r\n  // Map collaborative agent data to canvas board\r\n  private mapCollaborativeAgentDataToCanvas(response: any): void {\r\n    // Handle different response structures\r\n    let agentData;\r\n    if (\r\n      response.agentDetails &&\r\n      Array.isArray(response.agentDetails) &&\r\n      response.agentDetails.length > 0\r\n    ) {\r\n      agentData = response.agentDetails[0];\r\n    } else if (response.agentDetail) {\r\n      agentData = response.agentDetail;\r\n    } else if (response.data) {\r\n      agentData = response.data;\r\n    } else {\r\n      agentData = response;\r\n    }\r\n\r\n    if (!agentData) {\r\n      console.error('No agent data found in response');\r\n      return;\r\n    }\r\n\r\n    // Set basic agent information\r\n    // In duplicate mode, clear the name field so user can enter a new name\r\n    if (this.isDuplicateMode) {\r\n      this.agentName = ''; // Clear name for duplicate mode\r\n    } else {\r\n      if (agentData.name) {\r\n        this.agentName = agentData.name;\r\n      }\r\n    }\r\n\r\n    // Extract agent code for API calls\r\n    if (agentData.code) {\r\n      this.agentCode = agentData.code;\r\n    } else if (agentData.useCaseCode) {\r\n      this.agentCode = agentData.useCaseCode;\r\n    } else {\r\n      // Fallback: generate code from agent name\r\n      this.agentCode = this.agentName.trim().replace(/\\s+/g, '_').toUpperCase();\r\n    }\r\n\r\n    // Set agent details - check multiple possible field names\r\n    if (agentData.agentDetails) {\r\n      this.agentDetail = agentData.agentDetails;\r\n    } else if (agentData.agentDetail) {\r\n      this.agentDetail = agentData.agentDetail;\r\n    } else if (agentData.description) {\r\n      this.agentDetail = agentData.description;\r\n    }\r\n\r\n    // Set metadata from the API response\r\n    this.agentMetadata = {\r\n      org: agentData.org || '',\r\n      domain: agentData.domain || '',\r\n      project: agentData.project || '',\r\n      team: agentData.team || '',\r\n    };\r\n\r\n    // Map collaborative agent data to canvas nodes\r\n    // Use a temporary array to collect nodes in hierarchy order\r\n    const nodesByType: { [key: string]: BuildAgentNodeData[] } = {\r\n      prompt: [],\r\n      model: [],\r\n      knowledge: [],\r\n      guardrail: [],\r\n      tool: [],\r\n    };\r\n    let nodeCounter = 1;\r\n\r\n    // Add prompt node first - handle different prompt structures for different agent types\r\n    const shouldCreatePromptNode =\r\n      (this.currentAgentType === 'individual' && agentData.prompt) ||\r\n      (this.currentAgentType === 'collaborative' &&\r\n        (agentData.goal || agentData.role || agentData.description));\r\n\r\n    if (shouldCreatePromptNode) {\r\n      // Get a fallback prompt data for icon and structure\r\n      let promptData =\r\n        this.allToolItems['prompts']?.find(\r\n          (p) => (p as any).promptType === 'zero shot',\r\n        ) || this.allToolItems['prompts']?.[0];\r\n\r\n      // Create a default prompt data if none found\r\n      if (!promptData) {\r\n        promptData = {\r\n          id: 'default-prompt',\r\n          name: 'Default Prompt',\r\n          type: 'prompt',\r\n          icon: this.getIconForType('prompt'),\r\n          description: 'Default prompt',\r\n        };\r\n      }\r\n\r\n      // Determine prompt node name and data based on agent type\r\n      let promptNodeName;\r\n      let promptNodeData;\r\n\r\n      if (this.currentAgentType === 'collaborative') {\r\n        // For collaborative agents: use goal as primary display, store all prompt-related fields\r\n        promptNodeName =\r\n          agentData.goal ||\r\n          agentData.role ||\r\n          agentData.description ||\r\n          'Collaborative Agent Prompt';\r\n\r\n        // Truncate goal if too long for display\r\n        if (promptNodeName.length > 150) {\r\n          promptNodeName = promptNodeName.substring(0, 150) + '...';\r\n        }\r\n\r\n        promptNodeData = {\r\n          ...promptData,\r\n          // Store all collaborative agent prompt fields\r\n          goal: agentData.goal,\r\n          role: agentData.role,\r\n          description: agentData.description,\r\n          expectedOutput: agentData.expectedOutput,\r\n          backstory: agentData.backstory,\r\n          agentType: 'collaborative',\r\n        };\r\n      } else {\r\n        // For individual agents: use prompt field, store the full prompt text\r\n        promptNodeName = agentData.prompt || 'Individual Agent Prompt';\r\n\r\n        // Truncate prompt if too long for display\r\n        if (promptNodeName.length > 150) {\r\n          promptNodeName = promptNodeName.substring(0, 150) + '...';\r\n        }\r\n\r\n        promptNodeData = {\r\n          ...promptData,\r\n          // Store the full prompt text for individual agents\r\n          prompt: agentData.prompt,\r\n          fullPromptText: agentData.prompt,\r\n          agentType: 'individual',\r\n        };\r\n      }\r\n\r\n      const promptNode: BuildAgentNodeData = {\r\n        id: `prompt-${nodeCounter++}`,\r\n        name: promptNodeName,\r\n        type: 'prompt',\r\n        icon: promptData.icon || this.getIconForType('prompt'),\r\n        position: { x: 0, y: 0 }, // Will be calculated later\r\n        originalToolData: promptNodeData,\r\n      };\r\n\r\n      nodesByType['prompt'].push(promptNode);\r\n    } else if (agentData.agentDetail || agentData.name) {\r\n      // Fallback: Create a default prompt node if no prompt data found\r\n      const promptData =\r\n        this.allToolItems['prompts']?.find(\r\n          (p) => (p as any).promptType === 'zero shot',\r\n        ) || this.allToolItems['prompts']?.[0];\r\n\r\n      if (promptData) {\r\n        let promptNodeName;\r\n        let fallbackPromptData;\r\n\r\n        if (this.currentAgentType === 'collaborative') {\r\n          // For collaborative agents, try to use available fields or fallback to prompt name\r\n          promptNodeName =\r\n            agentData.goal ||\r\n            agentData.role ||\r\n            agentData.description ||\r\n            promptData.name;\r\n          fallbackPromptData = {\r\n            ...promptData,\r\n            goal: agentData.goal,\r\n            role: agentData.role,\r\n            description: agentData.description,\r\n            expectedOutput: agentData.expectedOutput,\r\n            agentType: 'collaborative',\r\n          };\r\n        } else {\r\n          // For individual agents, use prompt field or fallback to prompt name\r\n          promptNodeName = agentData.prompt || promptData.name;\r\n          fallbackPromptData = {\r\n            ...promptData,\r\n            prompt: agentData.prompt,\r\n            fullPromptText: agentData.prompt,\r\n            agentType: 'individual',\r\n          };\r\n        }\r\n\r\n        const promptNode: BuildAgentNodeData = {\r\n          id: `prompt-${nodeCounter++}`,\r\n          name: promptNodeName,\r\n          type: 'prompt',\r\n          icon: promptData.icon || this.getIconForType('prompt'),\r\n          position: { x: 0, y: 0 }, // Will be calculated later\r\n          originalToolData: fallbackPromptData,\r\n        };\r\n        nodesByType['prompt'].push(promptNode);\r\n      }\r\n    }\r\n\r\n    // Add model nodes - handle both old and new API formats\r\n    let modelReferences = [];\r\n\r\n    // New API format: agentConfigs.modelRef (array of model IDs or objects)\r\n    if (agentData.agentConfigs && agentData.agentConfigs.modelRef) {\r\n      const modelRefs = Array.isArray(agentData.agentConfigs.modelRef)\r\n        ? agentData.agentConfigs.modelRef\r\n        : [agentData.agentConfigs.modelRef];\r\n\r\n      // Handle both ID arrays and object arrays\r\n      modelReferences = modelRefs.map((ref: any) => {\r\n        if (typeof ref === 'number' || typeof ref === 'string') {\r\n          return { modelId: ref };\r\n        }\r\n        return ref;\r\n      });\r\n    }\r\n    // Old API format: modelDetails\r\n    else if (agentData.modelDetails) {\r\n      modelReferences = [agentData.modelDetails];\r\n    }\r\n\r\n    modelReferences.forEach((modelRef: any) => {\r\n      const modelId = modelRef.modelId || modelRef.id;\r\n\r\n      const modelData = this.allToolItems['models']?.find(\r\n        (m) =>\r\n          m.id === modelId ||\r\n          m.id === modelId.toString() ||\r\n          m.id.toString() === modelId,\r\n      );\r\n\r\n      if (modelData) {\r\n        nodesByType['model'].push({\r\n          id: `model-${nodeCounter++}`,\r\n          type: 'model',\r\n          name: modelData.name,\r\n          icon: undefined, // Let node component use Lucide icon\r\n          position: { x: 0, y: 0 }, // Will be calculated later\r\n          originalToolData: modelData, // Store original data for retrieval\r\n        });\r\n      } else {\r\n        // Create a placeholder model node with the model name from API\r\n        const modelName =\r\n          modelRef.model ||\r\n          modelRef.modelDeploymentName ||\r\n          `Model ID: ${modelId}`;\r\n        nodesByType['model'].push({\r\n          id: `model-${nodeCounter++}`,\r\n          type: 'model',\r\n          name: modelName,\r\n          icon: undefined, // Let node component use Lucide icon\r\n          position: { x: 0, y: 0 }, // Will be calculated later\r\n          originalToolData: { id: modelId, name: modelName }, // Store for retrieval\r\n        });\r\n      }\r\n    });\r\n\r\n    // Add knowledge base nodes - handle both old and new API formats\r\n    let knowledgeReferences = [];\r\n\r\n    // New API format: agentConfigs.knowledgeBaseRef (array of KB IDs or objects)\r\n    if (agentData.agentConfigs && agentData.agentConfigs.knowledgeBaseRef) {\r\n      const kbRefs = Array.isArray(agentData.agentConfigs.knowledgeBaseRef)\r\n        ? agentData.agentConfigs.knowledgeBaseRef\r\n        : [agentData.agentConfigs.knowledgeBaseRef];\r\n\r\n      // Handle both ID arrays and object arrays\r\n      knowledgeReferences = kbRefs.map((ref: any) => {\r\n        if (typeof ref === 'number' || typeof ref === 'string') {\r\n          return { knowledgeBaseId: ref };\r\n        }\r\n        return ref;\r\n      });\r\n    }\r\n    // Old API format: indexCollectionName\r\n    else if (\r\n      agentData.indexCollectionName &&\r\n      Array.isArray(agentData.indexCollectionName)\r\n    ) {\r\n      knowledgeReferences = agentData.indexCollectionName.map(\r\n        (name: string) => ({ indexCollectionName: name }),\r\n      );\r\n    }\r\n\r\n    knowledgeReferences.forEach((kbRef: any) => {\r\n      const kbId = kbRef.knowledgeBaseId || kbRef.id;\r\n      const collectionName = kbRef.indexCollectionName || kbRef.name;\r\n\r\n      // Try to find by ID first, then by name\r\n      const knowledgeData = this.allToolItems['knowledge']?.find(\r\n        (k) =>\r\n          (kbId &&\r\n            (k.id === kbId ||\r\n              k.id === kbId.toString() ||\r\n              k.id.toString() === kbId)) ||\r\n          (collectionName &&\r\n            (k.name === collectionName ||\r\n              k.name.toLowerCase() === collectionName.toLowerCase())),\r\n      );\r\n\r\n      if (knowledgeData) {\r\n        nodesByType['knowledge'].push({\r\n          id: `knowledge-${nodeCounter++}`,\r\n          type: 'knowledge',\r\n          name: knowledgeData.name,\r\n          icon: undefined, // Let node component use Lucide icon\r\n          position: { x: 0, y: 0 }, // Will be calculated later\r\n          originalToolData: knowledgeData, // Store original data for retrieval\r\n        });\r\n      } else {\r\n        // Create a placeholder knowledge node\r\n        const kbName = collectionName || `Knowledge Base ID: ${kbId}`;\r\n        nodesByType['knowledge'].push({\r\n          id: `knowledge-${nodeCounter++}`,\r\n          type: 'knowledge',\r\n          name: kbName,\r\n          icon: undefined, // Let node component use Lucide icon\r\n          position: { x: 0, y: 0 }, // Will be calculated later\r\n          originalToolData: { id: kbId, name: kbName }, // Store for retrieval\r\n        });\r\n      }\r\n    });\r\n\r\n    // Add tool nodes - handle both old and new API formats\r\n    let toolReferences = [];\r\n    let userToolReferences = [];\r\n\r\n    // New API format: agentConfigs.toolRef and agentConfigs.userToolRef (arrays of tool IDs or objects)\r\n    if (agentData.agentConfigs) {\r\n      if (agentData.agentConfigs.toolRef) {\r\n        const toolRefs = Array.isArray(agentData.agentConfigs.toolRef)\r\n          ? agentData.agentConfigs.toolRef\r\n          : [agentData.agentConfigs.toolRef];\r\n\r\n        // Handle both ID arrays and object arrays\r\n        toolReferences = toolRefs.map((ref: any) => {\r\n          if (typeof ref === 'number' || typeof ref === 'string') {\r\n            return { toolId: ref };\r\n          }\r\n          return ref;\r\n        });\r\n      }\r\n      if (agentData.agentConfigs.userToolRef) {\r\n        const userToolRefs = Array.isArray(agentData.agentConfigs.userToolRef)\r\n          ? agentData.agentConfigs.userToolRef\r\n          : [agentData.agentConfigs.userToolRef];\r\n\r\n        // Handle both ID arrays and object arrays\r\n        userToolReferences = userToolRefs.map((ref: any) => {\r\n          if (typeof ref === 'number' || typeof ref === 'string') {\r\n            return { toolId: ref };\r\n          }\r\n          return ref;\r\n        });\r\n      }\r\n    }\r\n    // Old API format: tools and userTools\r\n    else {\r\n      if (agentData.tools && Array.isArray(agentData.tools)) {\r\n        toolReferences = agentData.tools;\r\n      }\r\n      if (agentData.userTools && Array.isArray(agentData.userTools)) {\r\n        userToolReferences = agentData.userTools;\r\n      }\r\n    }\r\n\r\n    // Process built-in tools\r\n    toolReferences.forEach((tool: any) => {\r\n      const toolId = tool.toolId || tool.id;\r\n\r\n      const toolData = this.allToolItems['tools']?.find(\r\n        (t) =>\r\n          t.id === `builtin-${toolId}` ||\r\n          t.id === toolId ||\r\n          t.id === toolId.toString() ||\r\n          t.id.toString() === toolId,\r\n      );\r\n\r\n      if (toolData) {\r\n        nodesByType['tool'].push({\r\n          id: `tool-${nodeCounter++}`,\r\n          type: 'tool',\r\n          name: toolData.name,\r\n          icon: undefined, // Let node component use Lucide icon\r\n          position: { x: 0, y: 0 }, // Will be calculated later\r\n          originalToolData: toolData, // Store original data for retrieval\r\n        });\r\n      } else {\r\n        // Create a placeholder tool node with the tool name from API\r\n        const toolName = tool.toolName || `Tool ID: ${toolId}`;\r\n        nodesByType['tool'].push({\r\n          id: `tool-${nodeCounter++}`,\r\n          type: 'tool',\r\n          name: toolName,\r\n          icon: undefined, // Let node component use Lucide icon\r\n          position: { x: 0, y: 0 }, // Will be calculated later\r\n          originalToolData: { id: toolId, name: toolName }, // Store for retrieval\r\n        });\r\n      }\r\n    });\r\n\r\n    // Process user tools\r\n    userToolReferences.forEach((userTool: any) => {\r\n      const userToolId = userTool.toolId || userTool.id;\r\n\r\n      // Look for user tools (they have 'user-' prefix in our system)\r\n      const userToolData = this.allToolItems['tools']?.find(\r\n        (t) =>\r\n          t.id === `user-${userToolId}` ||\r\n          t.id === userToolId ||\r\n          t.id === userToolId.toString() ||\r\n          t.id.toString() === userToolId,\r\n      );\r\n\r\n      if (userToolData) {\r\n        nodesByType['tool'].push({\r\n          id: `tool-${nodeCounter++}`,\r\n          type: 'tool',\r\n          name: userToolData.name,\r\n          icon: undefined, // Let node component use Lucide icon\r\n          position: { x: 0, y: 0 }, // Will be calculated later\r\n          originalToolData: userToolData, // Store original data for retrieval\r\n        });\r\n      } else {\r\n        // Create a placeholder user tool node\r\n        const toolName = userTool.toolName || `User Tool ID: ${userToolId}`;\r\n        nodesByType['tool'].push({\r\n          id: `tool-${nodeCounter++}`,\r\n          type: 'tool',\r\n          name: toolName,\r\n          icon: undefined, // Let node component use Lucide icon\r\n          position: { x: 0, y: 0 }, // Will be calculated later\r\n          originalToolData: { id: userToolId, name: toolName }, // Store for retrieval\r\n        });\r\n      }\r\n    });\r\n\r\n    // Add a default prompt node if none exists (collaborative agents should have prompts)\r\n    if (nodesByType['prompt'].length === 0) {\r\n      const defaultPrompt = this.allToolItems['prompts']?.find((p) => {\r\n        const promptType = (p as any).type?.trim(); // Cast to any to access the prompt type property\r\n        return promptType === 'zero shot';\r\n      });\r\n      if (defaultPrompt) {\r\n        // For collaborative agents, use goal as the prompt node name, fallback to prompt data name\r\n        const promptNodeName =\r\n          this.currentAgentType === 'collaborative'\r\n            ? agentData.goal || defaultPrompt.name\r\n            : defaultPrompt.name;\r\n\r\n        nodesByType['prompt'].push({\r\n          id: `prompt-${nodeCounter++}`,\r\n          type: 'prompt',\r\n          name: promptNodeName,\r\n          icon: defaultPrompt.icon || this.getIconForType('prompt'),\r\n          position: { x: 0, y: 0 }, // Will be calculated later\r\n          originalToolData: defaultPrompt, // Store original data for retrieval\r\n        });\r\n      }\r\n    }\r\n\r\n    // Assemble nodes in hierarchy order and calculate positions\r\n    const nodes: BuildAgentNodeData[] = [];\r\n    const flowOrder = ['prompt', 'model', 'knowledge', 'guardrail', 'tool'];\r\n\r\n    // Clear and reset for clean positioning (collaborative agents)\r\n    this.buildAgentNodes = [];\r\n    this.latestNodeId = null;\r\n\r\n    flowOrder.forEach((nodeType) => {\r\n      nodesByType[nodeType].forEach((node) => {\r\n        // Use different positioning logic based on mode\r\n        if (this.isEditMode) {\r\n          // In edit mode, use consistent initial positioning\r\n          const nodeIndex = nodes.length; // Current index in the final nodes array\r\n          node.position = this.calculateEditModePosition(nodeIndex);\r\n        } else {\r\n          // Use simple vertical positioning for other modes (view, duplicate, etc.)\r\n          node.position = this.calculateSimpleVerticalPosition();\r\n        }\r\n        nodes.push(node);\r\n        // Add to buildAgentNodes and update latest node for next position calculation\r\n        this.buildAgentNodes.push(node);\r\n        this.latestNodeId = node.id;\r\n      });\r\n    });\r\n\r\n    // Update build agent nodes with final array\r\n    this.buildAgentNodes = nodes;\r\n\r\n    // Update canvas nodes for display\r\n    this.canvasNodes = nodes.map((node) => ({\r\n      id: node.id,\r\n      type: 'build-agent',\r\n      data: { ...node, width: this.isExecuteMode ? 55 : 120 },\r\n      position: node.position,\r\n    }));\r\n\r\n    // Update canvas board with the loaded data first\r\n    this.cdr.detectChanges();\r\n\r\n    // Wait for DOM to update, then create connections\r\n    setTimeout(() => {\r\n      // Clear existing edges when loading new agent data\r\n      this.canvasEdges = [];\r\n\r\n      // Create connections between nodes in the proper flow order\r\n      this.createAgentFlowConnections(nodes);\r\n\r\n      // Force update of connection points after nodes are positioned\r\n      if (this.canvasBoardComponent) {\r\n        this.canvasBoardComponent.updateNodeConnectionPoints();\r\n      }\r\n\r\n      // Trigger change detection again to ensure connections are rendered\r\n      this.cdr.detectChanges();\r\n    }, 200);\r\n\r\n    // In duplicate mode, clear the agent ID after loading data to ensure new agent creation\r\n    if (this.isDuplicateMode) {\r\n      this.currentAgentId = null;\r\n    }\r\n\r\n    // If in execute mode, automatically initialize playground\r\n    if (this.isExecuteMode) {\r\n      setTimeout(() => {\r\n        this.initializeExecuteMode();\r\n      }, 500);\r\n    }\r\n\r\n    // If in edit mode, ensure connections are properly established\r\n    if (this.isEditMode) {\r\n      setTimeout(() => {\r\n        this.ensureConnectionsInEditMode();\r\n      }, 500);\r\n    }\r\n  }\r\n\r\n  // Simple method to auto-select current agent in dropdown\r\n  private autoSelectCurrentAgentInDropdown(): void {\r\n    if (!this.agentName) {\r\n      return;\r\n    }\r\n\r\n    // Load agent details for playground\r\n    this.loadAgentDetailsForPlayground();\r\n\r\n    // Try to auto-select after a short delay\r\n    setTimeout(() => {\r\n      if (this.promptOptions && this.promptOptions.length > 0) {\r\n        // Find the current agent in the dropdown options\r\n        const currentAgentOption = this.promptOptions.find(\r\n          (option) =>\r\n            option.name === this.agentName ||\r\n            option.name.toLowerCase() === this.agentName.toLowerCase(),\r\n        );\r\n\r\n        if (currentAgentOption) {\r\n          this.selectedPrompt = currentAgentOption.name; // Use name for dropdown display\r\n          this.agentCode = currentAgentOption.value.toString(); // Store agent code\r\n          this.onPromptChanged(currentAgentOption);\r\n          this.cdr.detectChanges();\r\n        }\r\n      }\r\n    }, 1500); // Give more time for the dropdown to load\r\n  }\r\n\r\n  // Initialize execute mode - automatically open playground with agent selected\r\n  private initializeExecuteMode(): void {\r\n    // Force execute mode positioning - override any existing positions\r\n    const executePositions = this.buildAgentNodes.map((node, index) => ({\r\n      x: 40,\r\n      y: 100 + index * 80,\r\n    }));\r\n\r\n    // Update buildAgentNodes positions\r\n    this.buildAgentNodes.forEach((node, index) => {\r\n      node.position = { ...executePositions[index] };\r\n    });\r\n\r\n    // Recreate canvasNodes completely to avoid stale references\r\n    this.canvasNodes = this.buildAgentNodes.map((node, index) => ({\r\n      id: node.id,\r\n      type: 'build-agent',\r\n      data: {\r\n        ...node,\r\n        width: 55,\r\n        type: node.type,\r\n        name: node.name,\r\n        icon: node.icon,\r\n        position: { ...executePositions[index] },\r\n      },\r\n      position: { ...executePositions[index] },\r\n    }));\r\n\r\n    // Create simple connections\r\n    this.canvasEdges = [];\r\n    for (let i = 0; i < this.buildAgentNodes.length - 1; i++) {\r\n      this.canvasEdges.push({\r\n        id: `edge-${i}`,\r\n        source: this.buildAgentNodes[i].id,\r\n        target: this.buildAgentNodes[i + 1].id,\r\n      });\r\n    }\r\n\r\n    // Force immediate update and wait for DOM\r\n    this.cdr.detectChanges();\r\n\r\n    // Force position update after DOM settles\r\n    setTimeout(() => {\r\n      this.forceExecutePositions();\r\n    }, 100);\r\n\r\n    // Initialize chat\r\n    this.chatMessages = [\r\n      {\r\n        from: 'ai',\r\n        text: `Hi there! I am ${this.agentName || 'your build agent'}. How can I help you today?`,\r\n      },\r\n    ];\r\n  }\r\n\r\n  // Method to handle agent selection from card click\r\n  public selectAgentFromCard(agentData: any): void {\r\n    // Store the selected agent data\r\n    this.autoSelectedAgentFromCard = agentData;\r\n\r\n    // Set agent properties\r\n    this.agentName = agentData.name;\r\n    this.agentCode = agentData.code;\r\n    this.selectedAgentMode = agentData.code;\r\n    this.selectedUseCaseIdentifier = agentData.useCaseIdentifier;\r\n\r\n    // Auto-select in dropdown (keep dropdown enabled)\r\n    this.selectedPrompt = agentData.name;\r\n\r\n    // Update the prompt options to include the selected agent if not already present\r\n    if (this.promptOptions && this.promptOptions.length > 0) {\r\n      const existingOption = this.promptOptions.find(\r\n        (option) =>\r\n          option.value === agentData.code || option.name === agentData.name,\r\n      );\r\n\r\n      if (!existingOption) {\r\n        // Add the selected agent to the options\r\n        this.promptOptions.push({\r\n          value: agentData.code,\r\n          name: agentData.name,\r\n          agentData: agentData,\r\n        } as any);\r\n      }\r\n    }\r\n\r\n    // Force change detection\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  // Navigation\r\n  goBack(): void {\r\n    this.router.navigate(['/agents']);\r\n  }\r\n\r\n  // Test method to check current agent details for playground\r\n  public testCurrentAgentDetails(): void {\r\n    // Method kept for compatibility - details available in component properties\r\n  }\r\n\r\n  // Popup handling methods\r\n  showSuccessMessage(title: string, message: string): void {\r\n    this.popupMessage = message;\r\n    this.showSuccessPopup = true;\r\n  }\r\n\r\n  // Show approval success message (redirects to agents page after confirmation)\r\n  showApprovalSuccessMessage(title: string, message: string): void {\r\n    this.popupMessage = message;\r\n    this.isApprovalSuccess = true; // Set flag for approval success\r\n    this.showSuccessPopup = true;\r\n  }\r\n\r\n  showErrorMessage(title: string, message: string): void {\r\n    this.popupMessage = message;\r\n    this.showErrorPopup = true;\r\n  }\r\n\r\n  showWarningMessage(title: string, message: string): void {\r\n    this.popupMessage = message;\r\n    this.showWarningPopup = true;\r\n  }\r\n\r\n  closeSuccessPopup(): void {\r\n    this.showSuccessPopup = false;\r\n    this.isApprovalSuccess = false;\r\n    this.popupMessage = '';\r\n  }\r\n\r\n\r\n  // Ensure connections are properly established in edit mode\r\n  private ensureConnectionsInEditMode(): void {\r\n    if (this.handleEditModeConnections && this.buildAgentNodes.length > 1) {\r\n      // Always recreate connections in edit mode to ensure they're properly established\r\n      this.createAgentFlowConnections(this.buildAgentNodes);\r\n\r\n      // Force update of connection points\r\n      if (this.canvasBoardComponent) {\r\n        this.canvasBoardComponent.updateNodeConnectionPoints();\r\n      }\r\n\r\n      // Trigger change detection\r\n      this.cdr.detectChanges();\r\n\r\n      // Reset the flag after handling\r\n      this.handleEditModeConnections = false;\r\n    }\r\n  }\r\n\r\n  // Calculate position for edit mode - consistent initial layout\r\n  private calculateEditModePosition(index: number): { x: number; y: number } {\r\n    const editModeStartX = 150;\r\n    const editModeStartY = 100;\r\n    const editModeVerticalSpacing = 120;\r\n\r\n    const calculatedPosition = {\r\n      x: editModeStartX,\r\n      y: editModeStartY + index * editModeVerticalSpacing,\r\n    };\r\n\r\n    console.log('🔧 EDIT MODE POSITION CALCULATED:', {\r\n      index,\r\n      editModeStartX,\r\n      editModeStartY,\r\n      editModeVerticalSpacing,\r\n      calculatedPosition,\r\n    });\r\n\r\n    return calculatedPosition;\r\n  }\r\n\r\n  // Top Bar Navigation Methods\r\n  navigateToAgentsList(): void {\r\n    this.router.navigate(['/build/agents']);\r\n  }\r\n\r\n  switchAgentType(newType: 'individual' | 'collaborative'): void {\r\n    if (this.currentAgentType !== newType) {\r\n      // Clear all data before switching modes\r\n      this.clearAllAgentData();\r\n\r\n      // Navigate to the new agent type route\r\n      this.router.navigate(['/build/agents', newType]);\r\n    }\r\n  }\r\n\r\n  private clearAllAgentData(): void {\r\n    // Clear agent basic information\r\n    this.agentName = '';\r\n    this.agentDetail = '';\r\n    this.agentCode = '';\r\n\r\n    // Clear agent metadata\r\n    this.agentMetadata = {\r\n      org: '',\r\n      domain: '',\r\n      project: '',\r\n      team: '',\r\n    };\r\n\r\n    // Clear all canvas-related data\r\n    this.buildAgentNodes = [];\r\n    this.canvasNodes = [];\r\n    this.canvasEdges = [];\r\n\r\n    // Clear current agent details and ID\r\n    this.currentAgentDetails = null;\r\n    this.currentAgentId = null;\r\n\r\n    // Reset latest node ID counter\r\n    this.latestNodeId = null;\r\n\r\n    // Clear any selection states\r\n    this.selectedNodeId = null;\r\n\r\n    // Clear preview data if any\r\n    this.showPreview = false;\r\n    this.previewData = null;\r\n    this.closePreview();\r\n\r\n    // Clear chat messages and test input\r\n    this.chatMessages = [];\r\n    this.testSpaceInput = '';\r\n\r\n    // Reset search form and tab selection\r\n    this.activeTab = 'prompts';\r\n    this.searchForm.get('search')?.setValue('', { emitEvent: false });\r\n\r\n    // Reset agent playground data\r\n    this.agentChatPayload = [];\r\n    this.agentFilesUploadedData = [];\r\n    this.agentAttachment = [];\r\n\r\n    // Reset execution ID for new session\r\n    this.executionId = crypto.randomUUID();\r\n\r\n    // Clear any loading states\r\n    this.isProcessingChat = false;\r\n    this.isAgentPlaygroundLoading = false;\r\n\r\n    // Reset mode flags to default\r\n    this.isViewMode = false;\r\n    this.isEditMode = false;\r\n    this.isExecuteMode = false;\r\n    this.isDuplicateMode = false;\r\n    this.isNewAgent = true;\r\n    this.isFieldsDisabled = false;\r\n\r\n    // Reset UI states\r\n    this.isAgentDetailsCollapsed = true;\r\n    this.isSidebarCollapsed = false;\r\n    this.showChatInterface = false;\r\n    this.autoSelectedAgentFromCard = null;\r\n\r\n    // Force change detection to update UI\r\n    this.cdr.detectChanges();\r\n\r\n    console.log('🔄 All agent data cleared for mode switch');\r\n  }\r\n\r\n  async saveAgentFromTopBar(): Promise<void> {\r\n    // Use existing save logic\r\n    await this.onPrimaryButtonClick();\r\n  }\r\n\r\n  canSaveAgent(): boolean {\r\n    // Check if we have minimum required components\r\n    const hasPrompt = this.buildAgentNodes.some(\r\n      (node) => node.type === 'prompt',\r\n    );\r\n    const hasModel = this.buildAgentNodes.some((node) => node.type === 'model');\r\n    const hasValidName = !!(this.agentName && this.agentName.trim().length > 0);\r\n    const hasValidDescription = !!(\r\n      this.agentDetail && this.agentDetail.trim().length > 0\r\n    );\r\n\r\n    return hasPrompt && hasModel && hasValidName && hasValidDescription;\r\n  }\r\n\r\n  // Agent Details Floater Methods\r\n  toggleAgentDetailsFloater(): void {\r\n    this.isAgentDetailsCollapsed = !this.isAgentDetailsCollapsed;\r\n  }\r\n\r\n  // Handle Execute Agent action from success popup\r\n  onSuccessExecuteAgent(): void {\r\n    this.closeSuccessPopup();\r\n\r\n    // Navigate to the agent execution page\r\n    if (this.currentAgentId) {\r\n      this.router.navigate(\r\n        ['/build/agents', this.currentAgentType, 'execute'],\r\n        {\r\n          queryParams: { id: this.currentAgentId },\r\n        },\r\n      );\r\n    }\r\n  }\r\n\r\n  // Handle Go to Agents Library action from success popup\r\n  onSuccessGoToLibrary(): void {\r\n    this.closeSuccessPopup();\r\n\r\n    // Navigate to the agents library\r\n    this.router.navigate(['/build/agents']);\r\n  }\r\n}\r\n"], "mappings": ";;AAAA,SACEA,SAAS,EAKTC,SAAS,EACTC,gBAAgB,QACX,eAAe;AACtB,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SACEC,WAAW,EACXC,mBAAmB,QAGd,gBAAgB;AAEvB,SACEC,eAAe,EAEfC,mBAAmB,EACnBC,oBAAoB,EACpBC,aAAa,EACbC,aAAa,QACR,wBAAwB;AAC/B,SACEC,oBAAoB,QAGf,yDAAyD;AAChE,SACEC,uBAAuB,QAGlB,+BAA+B;AAItC,SAEEC,OAAO,EACPC,SAAS,EACTC,SAAS,EACTC,QAAQ,EACRC,UAAU,EACVC,EAAE,QAEG,MAAM;AASb,SAEEC,mBAAmB,QACd,uDAAuD;AAC9D,SAASC,mBAAmB,QAAQ,qDAAqD;AAEzF,SAASC,2BAA2B,QAAQ,uDAAuD;AAGnG,SACEC,uBAAuB,QAElB,iEAAiE;AAgCjE,IAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EAkLrBC,MAAA;EACAC,KAAA;EACAC,oBAAA;EACAC,GAAA;EACAC,YAAA;EACAC,YAAA;EACAC,cAAA;EACAC,YAAA;EACAC,sBAAA;EACAC,WAAA;EACAC,oBAAA;EACAC,aAAA;EACAC,iBAAA;EACAC,YAAA;EACAC,aAAA;EA/LuBC,oBAAoB;EACKC,eAAe;EAEjEC,QAAQ,GAAG,IAAI5B,OAAO,EAAQ;EACtC6B,SAAS,GAAW,SAAS;EAC7BC,WAAW,GAAW,EAAE;EACxBC,UAAU;EAEVC,kBAAkB,GAAG,KAAK;EAC1BC,WAAW,GAAG,KAAK;EACnBC,YAAY,GAAQ,IAAI;EACxBC,WAAW,GAAQ,IAAI;EACvBC,gBAAgB,GAAG,KAAK;EACxBC,WAAW,GAAiB,EAAE;EAC9BC,WAAW,GAAiB,EAAE;EAC9BC,eAAe,GAAyB,EAAE;EAC1CC,YAAY,GAAsB,EAAE;EACpCC,cAAc,GAAkB,IAAI;EACpCC,gBAAgB,GAAG,YAAY;EAC/BC,WAAW,GAA6C,QAAQ;EAChEC,cAAc,GAAkB,IAAI;EACpCC,gBAAgB,GAAG,KAAK;EACxBC,eAAe,GAAG,KAAK;EACvBC,yBAAyB,GAAG,KAAK;EAEjCC,gBAAgB,GAAG,KAAK;EACxBC,cAAc,GAAG,KAAK;EACtBC,gBAAgB,GAAG,KAAK;EACxBC,iBAAiB,GAAG,KAAK;EACzBC,YAAY,GAAG,EAAE;EACjBC,UAAU,GAAG,EAAE;EACfC,WAAW;EAEX,IAAIC,kBAAkBA,CAAA;IACpB,OAAO,KAAK;EACd;EACAC,mBAAmB,GAAQ,IAAI;EACdC,UAAU,GAAG;IAC5BC,UAAU,EAAE;MAAEC,MAAM,EAAE,CAAC;MAAEC,KAAK,EAAE,CAAC;MAAEC,SAAS,EAAE,CAAC,CAAC;MAAEC,SAAS,EAAE,CAAC;IAAC,CAAE;IACjEC,aAAa,EAAE;MAAEJ,MAAM,EAAE,CAAC;MAAEC,KAAK,EAAE,CAAC;MAAEC,SAAS,EAAE,CAAC,CAAC;MAAEG,IAAI,EAAE,CAAC;IAAC;GAC9D;EAED;EACQC,YAAY,GAAkB,IAAI;EAE1C;EACA,IAAIC,cAAcA,CAAA;IAChB,OAAO;MACLC,SAAS,EAAE,IAAI,CAACzB,gBAAkD;MAClE0B,iBAAiB,EAAE;QACjBC,OAAO,EAAE;UAAEC,GAAG,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAI,CAAE;QACnCC,MAAM,EAAE;UAAEF,GAAG,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAClCV,SAAS,EAAE;UAAES,GAAG,EAAE,CAAC,CAAC;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAAE;QACxCE,UAAU,EAAE;UAAEH,GAAG,EAAE,CAAC,CAAC;UAAEC,QAAQ,EAAE;QAAK,CAAE;QACxCG,KAAK,EAAE;UAAEJ,GAAG,EAAE,CAAC,CAAC;UAAEC,QAAQ,EAAE;QAAK;;KAEpC;EACH;EACAI,IAAI,GAAa,EAAE;EACXC,WAAW,GAAgB,EAAE;EACrC,IAAIC,UAAUA,CAAA;IACZ,OAAO,IAAI,CAACD,WAAW;EACzB;EAEAE,YAAY,GAAkC;IAC5CT,OAAO,EAAE,EAAE;IACXG,MAAM,EAAE,EAAE;IACVX,SAAS,EAAE,EAAE;IACba,KAAK,EAAE,EAAE;IACTD,UAAU,EAAE;GACb;EAEDM,eAAe,GAAG,CAChB,0BAA0B,EAC1B,qBAAqB,EACrB,qBAAqB,CACtB;EACDC,aAAa,GAAG,KAAK;EACrBC,iBAAiB,GAAG,KAAK;EACzBC,YAAY,GAAkB,EAAE;EAChCC,gBAAgB,GAAG,KAAK;EACxBC,aAAa,GAAqB,EAAE;EAC5BC,qBAAqB;EAE7BC,UAAU,GAAG,KAAK;EAClBC,UAAU,GAAG,KAAK;EAClBC,UAAU,GAAG,IAAI;EACjBC,cAAc,GAAG,IAAIC,GAAG,EAAkB;EAE1C;EACA,IAAIC,iBAAiBA,CAAA;IACnB,IAAI,IAAI,CAACX,aAAa,EAAE;MACtB,OAAO,KAAK,CAAC,CAAC;IAChB;IAEA,IAAI,IAAI,CAACO,UAAU,EAAE;MACnB,OAAO,SAAS,CAAC,CAAC;IACpB;IAEA,IAAI,IAAI,CAACD,UAAU,EAAE;MACnB;MACA,IAAI,IAAI,CAAC5C,gBAAgB,KAAK,eAAe,EAAE;QAC7C,OAAO,0BAA0B;MACnC;MACA,OAAO,QAAQ,CAAC,CAAC;IACnB;IAEA,IAAI,IAAI,CAACI,eAAe,EAAE;MACxB,OAAO,MAAM,CAAC,CAAC;IACjB;IAEA;IACA,IAAI,IAAI,CAACJ,gBAAgB,KAAK,eAAe,EAAE;MAC7C,OAAO,wBAAwB;IACjC;IAEA,OAAO,MAAM,CAAC,CAAC;EACjB;EAEA;EACAkD,SAAS,GAAW,EAAE;EACtBC,WAAW,GAAW,EAAE;EACxBC,SAAS,GAAW,EAAE,CAAC,CAAC;EAExB;EACAC,uBAAuB,GAAY,IAAI;EACvCC,aAAa,GAKT;IAAEC,GAAG,EAAE,EAAE;IAAEC,MAAM,EAAE,EAAE;IAAEC,OAAO,EAAE,EAAE;IAAEC,IAAI,EAAE;EAAE,CAAE;EAE1CC,qBAAqBA,CAAA;IAC3B,MAAM;MAAEJ,GAAG;MAAEC,MAAM;MAAEC,OAAO;MAAEC;IAAI,CAAE,GAAG,IAAI,CAACJ,aAAa;IACzD,OAAOC,GAAG,IAAIC,MAAM,IAAIC,OAAO,IAAIC,IAAI,GACnC,IAAIH,GAAG,IAAIC,MAAM,IAAIC,OAAO,IAAIC,IAAI,EAAE,GACtC,sDAAsD;EAC5D;EAEA;EACQE,WAAW,GAAQ,IAAI;EAE/B;EACAC,mBAAmB;EACnBC,cAAc,GAAG,SAAS;EAC1BC,iBAAiB,GAAG,EAAE;EACtBC,yBAAyB,GAAG,EAAE;EAC9BC,gBAAgB,GAAU,EAAE;EAC5BC,sBAAsB,GAAU,EAAE;EAClCC,eAAe,GAAa,EAAE;EAC9BC,wBAAwB,GAAG,KAAK;EAChCC,sBAAsB,GAAG,IAAI/G,OAAO,EAAW;EAE/CgH,mBAAmB,GAAG;IACpBC,EAAE,EAAE,eAAe;IACnBC,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE,KAAK;IAClBC,mBAAmB,EAAE;MACnBC,IAAI,EAAE,IAAI;MACVC,UAAU,EAAE,SAAS;MACrBC,UAAU,EAAE,QAAQ;MACpBC,QAAQ,EAAE,EAAE;MACZC,OAAO,EAAE;;GAEZ;EACDC,mBAAmB,GAAG,KAAK;EAC3BC,YAAY,GAAG,EAAE;EACjBC,YAAY,GAAG,KAAK;EACpBC,yBAAyB,GAAQ,IAAI;EACrCC,cAAc,GAAG,EAAE;EAEnB;EACAC,qBAAqB,GAAkC,EAAE;EAEzDC,YACUtH,MAAc,EACdC,KAAqB,EACrBC,oBAA0C,EAC1CC,GAAsB,EACtBC,YAA0B,EAC1BC,YAAiC,EACjCC,cAA8B,EAC9BC,YAAiC,EACjCC,sBAA8C,EAC9CC,WAAwB,EACxBC,oBAA0C,EAC1CC,aAA4B,EAC5BC,iBAAoC,EACpCC,YAA0B,EAC1BC,aAA4B;IAd5B,KAAAd,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,sBAAsB,GAAtBA,sBAAsB;IACtB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,aAAa,GAAbA,aAAa;IAErB,IAAI,CAAC8E,mBAAmB,GAAG,IAAI,CAACnF,WAAW,CAAC8G,KAAK,CAAC;MAChDC,gBAAgB,EAAE,CAAC,IAAI,CAAC;MACxBC,aAAa,EAAE,CAAC,KAAK;KACtB,CAAC;IACF,IAAI,CAACrG,UAAU,GAAG,IAAI,CAACX,WAAW,CAAC8G,KAAK,CAAC;MAAEG,MAAM,EAAE,CAAC,EAAE;IAAC,CAAE,CAAC;EAC5D;EAEAC,QAAQA,CAAA;IACN,IAAI,CAAChF,WAAW,GAAGiF,MAAM,CAACC,UAAU,EAAE;IACtC,IAAI,CAAC5H,KAAK,CAAC6H,MAAM,CAACC,SAAS,CAAED,MAAM,IAAI;MACrC,IAAI,CAAC/F,gBAAgB,GACnB+F,MAAM,CAAC,MAAM,CAAC,KAAK,YAAY,IAAIA,MAAM,CAAC,MAAM,CAAC,KAAK,eAAe,GACjEA,MAAM,CAAC,MAAM,CAAC,GACd,YAAY;MAClB,IAAI,CAACjF,mBAAmB,GAAG,IAAI;MAC/B,IAAI,CAACmF,yBAAyB,EAAE;MAChC,IAAI,CAACC,oBAAoB,EAAE;IAC7B,CAAC,CAAC;IACF,IAAI,CAAChI,KAAK,CAACiI,WAAW,CAACH,SAAS,CAAED,MAAM,IAAI;MAC1C,IAAIA,MAAM,CAAC,IAAI,CAAC,EAAE;QAChB,IAAI,CAAC7F,cAAc,GAAG6F,MAAM,CAAC,IAAI,CAAC;QAClC,MAAMK,IAAI,GAAGL,MAAM,CAAC,MAAM,CAAC;QAC3B,IAAI,CAAClD,UAAU,GACb,IAAI,CAACD,UAAU,GACf,IAAI,CAACN,aAAa,GAClB,IAAI,CAAClC,eAAe,GACpB,IAAI,CAAC0C,UAAU,GACb,KAAK;QACT,IAAI,CAAC3C,gBAAgB,GAAG,KAAK;QAE7BkG,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE;UAChCF,IAAI;UACJlG,cAAc,EAAE,IAAI,CAACA,cAAc;UACnCqG,GAAG,EAAE,IAAI,CAACtI,MAAM,CAACsI;SAClB,CAAC;QAEF,IAAIH,IAAI,KAAK,MAAM,EAAE;UACnB,IAAI,CAACvD,UAAU,GAAG,IAAI;UACtB,IAAI,CAAC1C,gBAAgB,GAAG,IAAI;QAC9B,CAAC,MAAM,IAAIiG,IAAI,KAAK,MAAM,EAAE;UAC1B,IAAI,CAACxD,UAAU,GAAG,IAAI;UACtB,IAAI,CAACvC,yBAAyB,GAAG,IAAI;QACvC,CAAC,MAAM,IAAI+F,IAAI,KAAK,SAAS,EAAE;UAC7B;UACA,IAAI,CAACnI,MAAM,CAACuI,QAAQ,CAClB,CAAC,eAAe,EAAE,IAAI,CAACxG,gBAAgB,EAAE,SAAS,CAAC,EACnD;YACEmG,WAAW,EAAE;cAAE5B,EAAE,EAAEwB,MAAM,CAAC,IAAI;YAAC;WAChC,CACF;UACD;QACF,CAAC,MAAM,IAAIK,IAAI,KAAK,WAAW,EAAE;UAC/B,IAAI,CAAChG,eAAe,GAAG,IAAI;UAC3B,IAAI,CAAC2C,cAAc,CAAC0D,KAAK,EAAE;QAC7B,CAAC,MAAM;UACL,IAAI,CAAC3D,UAAU,GAAG,IAAI;QACxB;QAEAuD,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;UAC5CzD,UAAU,EAAE,IAAI,CAACA,UAAU;UAC3BD,UAAU,EAAE,IAAI,CAACA,UAAU;UAC3BN,aAAa,EAAE,IAAI,CAACA,aAAa;UACjClC,eAAe,EAAE,IAAI,CAACA,eAAe;UACrC0C,UAAU,EAAE,IAAI,CAACA;SAClB,CAAC;QAEF,IAAI,CAAC4D,aAAa,CAACX,MAAM,CAAC,IAAI,CAAC,CAAC;MAClC,CAAC,MAAM;QACL,IAAI,CAACjD,UAAU,GAAG,IAAI;QACtB,IAAI,CAACF,UAAU,GAAG,IAAI,CAACC,UAAU,GAAG,KAAK;QACzC,IAAI,CAAC1C,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACD,cAAc,GAAG,IAAI;MAC5B;IACF,CAAC,CAAC;IAEF,IAAI,CAACf,SAAS,GAAG,SAAS;IAC1B,IAAI,CAACE,UAAU,CACZsH,GAAG,CAAC,QAAQ,CAAC,EACZC,YAAY,CAACC,IAAI,CAACtJ,SAAS,CAAC,IAAI,CAAC2B,QAAQ,CAAC,CAAC,CAC5C8G,SAAS,CAAEc,KAAK,IAAI;MACnB,IAAI,CAAC1H,WAAW,GAAG0H,KAAK,IAAI,EAAE;MAC9B,IAAI,CAACC,mBAAmB,EAAE;IAC5B,CAAC,CAAC;IACJ,IAAI,CAACA,mBAAmB,EAAE;EAC5B;EAEAC,eAAeA,CAAA;IACb,IAAI,CAAC5I,GAAG,CAAC6I,aAAa,EAAE;IACxBC,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAAClI,oBAAoB,IAAI,IAAI,CAACa,eAAe,CAACsH,MAAM,GAAG,CAAC,EAAE;QAChE,IAAI,CAACnI,oBAAoB,CAACoI,0BAA0B,EAAE;QACtD,IAAI,IAAI,CAACxH,WAAW,CAACuH,MAAM,KAAK,CAAC,IAAI,IAAI,CAACtH,eAAe,CAACsH,MAAM,GAAG,CAAC,EAAE;UACpE,IAAI,CAACE,0BAA0B,CAAC,IAAI,CAACxH,eAAe,CAAC;QACvD;MACF;IACF,CAAC,EAAE,GAAG,CAAC;IACP,IAAI,CAACjB,aAAa,CAAC0I,qBAAqB,CAAC,IAAI,CAACrI,eAAe,CAAC;EAChE;EAEAsI,WAAWA,CAAA;IACT,IAAI,CAACrI,QAAQ,CAACsI,IAAI,EAAE;IACpB,IAAI,CAACtI,QAAQ,CAACuI,QAAQ,EAAE;IACxB,IAAI,IAAI,CAAC9E,qBAAqB,EAAE;MAC9B,IAAI,CAACA,qBAAqB,CAAC+E,WAAW,EAAE;IAC1C;IACA,IAAI,CAACrD,sBAAsB,CAACmD,IAAI,CAAC,IAAI,CAAC;IACtC,IAAI,CAACnD,sBAAsB,CAACoD,QAAQ,EAAE;EACxC;EAEQE,iBAAiBA,CACvBnD,IAAY,EACZoD,IAAY,EACZC,IAAY,EACZC,KAAa;IAEb,OAAOC,KAAK,CAACC,IAAI,CAAC;MAAEb,MAAM,EAAEW;IAAK,CAAE,EAAE,CAACG,CAAC,EAAEC,CAAC,MAAM;MAC9C3D,EAAE,EAAE,GAAGC,IAAI,GAAG0D,CAAC,GAAG,CAAC,EAAE;MACrBN,IAAI;MACJC,IAAI;MACJrD,IAAI,EAAEA,IAAW;MACjB2D,WAAW,EACT;KACH,CAAC,CAAC;EACL;EAEQC,WAAWA,CAAA;IACjB,IAAI,CAAC7J,cAAc,CAAC8J,eAAe,EAAE,CAACrC,SAAS,CAAC;MAC9CwB,IAAI,EAAG7F,OAAc,IAAI;QACvB,MAAM2G,eAAe,GAAG,IAAI,CAACC,wBAAwB,CAAC5G,OAAO,CAAC;QAC9D,IAAI,CAACS,YAAY,CAAC,SAAS,CAAC,GAAGkG,eAAe,CAACE,GAAG,CAAEvH,MAAW,KAAM;UACnEsD,EAAE,EAAEtD,MAAM,CAACsD,EAAE,EAAEkE,QAAQ,EAAE,IAAIC,IAAI,CAACC,MAAM,EAAE,CAACF,QAAQ,EAAE;UACrDb,IAAI,EAAE3G,MAAM,CAAC2G,IAAI;UACjBO,WAAW,EAAElH,MAAM,CAACkH,WAAW,IAAIlH,MAAM,CAAC2H,uBAAuB;UACjEC,iBAAiB,EAAE5H,MAAM,CAAC4H,iBAAiB;UAC3ChB,IAAI,EAAE,0BAA0B;UAChCrD,IAAI,EAAE,QAAQ;UACdsE,UAAU,EAAE7H,MAAM,CAACuD,IAAI;UACvBuE,IAAI,EAAE9H,MAAM,CAAC8H,IAAI;UACjBC,IAAI,EAAE/H,MAAM,CAAC+H,IAAI;UACjBC,SAAS,EAAEhI,MAAM,CAACgI,SAAS;UAC3BC,cAAc,EAAEjI,MAAM,CAACiI,cAAc;UACrCN,uBAAuB,EAAE3H,MAAM,CAAC2H,uBAAuB;UACvDO,0BAA0B,EAAElI,MAAM,CAACkI,0BAA0B;UAC7DC,kBAAkB,EAAEnI;SACrB,CAAC,CAAC;QACH,IAAI,CAAC8F,mBAAmB,EAAE;MAC5B,CAAC;MACDsC,KAAK,EAAGA,KAAK,IAAI;QACfhD,OAAO,CAACgD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAChD;KACD,CAAC;EACJ;EAEQd,wBAAwBA,CAAC5G,OAAc;IAC7C,IAAI,CAACoG,KAAK,CAACuB,OAAO,CAAC3H,OAAO,CAAC,IAAIA,OAAO,CAACwF,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IAE9D,IAAI,IAAI,CAACnH,gBAAgB,KAAK,eAAe,EAAE;MAC7C,OAAO2B,OAAO,CAAC4H,MAAM,CAAEtI,MAAM,IAAKA,MAAM,CAACuD,IAAI,EAAEgF,IAAI,EAAE,KAAK,WAAW,CAAC;IACxE,CAAC,MAAM;MACL,OAAO7H,OAAO,CAAC,CAAC;IAClB;EACF;EAEQ8H,UAAUA,CAAA;IAChB,IAAI,IAAI,CAACzJ,gBAAgB,KAAK,eAAe,EAAE;MAC7C,IAAI,CAAC1B,YAAY,CAACoL,sBAAsB,EAAE,CAAC1D,SAAS,CAAC;QACnDwB,IAAI,EAAGmC,QAAa,IAAI;UACtB,IAAIA,QAAQ,IAAI5B,KAAK,CAACuB,OAAO,CAACK,QAAQ,CAAC,EAAE;YACvC,IAAI,CAACvH,YAAY,CAAC,QAAQ,CAAC,GAAGuH,QAAQ,CAACnB,GAAG,CAAEtH,KAAU,KAAM;cAC1DqD,EAAE,EAAErD,KAAK,CAACqD,EAAE;cACZqD,IAAI,EAAE,GAAG1G,KAAK,CAAC0I,mBAAmB,KAAK1I,KAAK,CAACA,KAAK,GAAG;cACrDiH,WAAW,EACTjH,KAAK,CAAC2I,gBAAgB,IACtB,GAAG3I,KAAK,CAAC4I,SAAS,cAAc5I,KAAK,CAAC6I,QAAQ,EAAE;cAClDlC,IAAI,EAAE,+BAA+B;cACrCrD,IAAI,EAAE,OAAgB;cACtBtD,KAAK,EAAEA,KAAK,CAACA,KAAK;cAClB6I,QAAQ,EAAE7I,KAAK,CAAC6I,QAAQ;cACxBD,SAAS,EAAE5I,KAAK,CAAC4I;aAClB,CAAC,CAAC;UACL,CAAC,MAAM,IAAIH,QAAQ,IAAIA,QAAQ,CAAC7H,MAAM,EAAE;YACtC,IAAI,CAACM,YAAY,CAAC,QAAQ,CAAC,GAAGuH,QAAQ,CAAC7H,MAAM,CAAC0G,GAAG,CAAEtH,KAAU,KAAM;cACjEqD,EAAE,EAAErD,KAAK,CAACqD,EAAE;cACZqD,IAAI,EAAE1G,KAAK,CAACA,KAAK;cACjBiH,WAAW,EAAEjH,KAAK,CAAC2I,gBAAgB;cACnChC,IAAI,EAAE,+BAA+B;cACrCrD,IAAI,EAAE;aACP,CAAC,CAAC;UACL;UACA,IAAI,CAACuC,mBAAmB,EAAE;QAC5B,CAAC;QACDsC,KAAK,EAAGA,KAAU,IAAI;UACpBhD,OAAO,CAACgD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;UAC7D,IAAI,CAACW,oBAAoB,EAAE;QAC7B;OACD,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACA,oBAAoB,EAAE;IAC7B;EACF;EAEQA,oBAAoBA,CAAA;IAC1B,IAAI,CAAC1L,YAAY,CAAC2L,mBAAmB,EAAE,CAACjE,SAAS,CAAC;MAChDwB,IAAI,EAAG1F,MAAa,IAAI;QACtB,IAAI,CAACM,YAAY,CAAC,QAAQ,CAAC,GAAGN,MAAM,CAAC0G,GAAG,CAAEtH,KAAU,KAAM;UACxDqD,EAAE,EAAErD,KAAK,CAACqD,EAAE;UACZqD,IAAI,EAAE1G,KAAK,CAAC0G,IAAI;UAChBO,WAAW,EAAEjH,KAAK,CAACiH,WAAW;UAC9BN,IAAI,EAAE3G,KAAK,CAAC2G,IAAI;UAChBrD,IAAI,EAAE;SACP,CAAC,CAAC;QACH,IAAI,CAACuC,mBAAmB,EAAE;MAC5B,CAAC;MACDsC,KAAK,EAAGA,KAAU,IAAI;QACpBhD,OAAO,CAACgD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC7D,IAAI,CAACjH,YAAY,CAAC,QAAQ,CAAC,GAAG,IAAI,CAACuF,iBAAiB,CAClD,OAAO,EACP,YAAY,EACZ,+BAA+B,EAC/B,CAAC,CACF;QACD,IAAI,CAACZ,mBAAmB,EAAE;MAC5B;KACD,CAAC;EACJ;EAEQmD,mBAAmBA,CAAA;IACzB,IAAI,CAAC,IAAI,CAACtG,WAAW,EAAE;IAEvB,MAAMuG,cAAc,GAAG,IAAI,CAACvG,WAAW,CAACuG,cAAc,IAAI,EAAE;IAC5D,MAAMC,aAAa,GAAGD,cAAc,CAACE,IAAI,CACtCC,QAAa,IAAKA,QAAQ,CAACC,UAAU,KAAK,CAAC,CAC7C;IAED,IAAIH,aAAa,EAAE;MACjB,MAAMI,UAAU,GAAGJ,aAAa,CAACK,MAAM,CAACJ,IAAI,CACzCK,KAAU,IAAKA,KAAK,CAACC,SAAS,KAAK,OAAO,CAC5C;MACD,IAAIH,UAAU,IAAIA,UAAU,CAACI,WAAW,EAAE;QACxC,MAAMC,YAAY,GAAG,IAAI,CAACvM,YAAY,CAACwM,gBAAgB,CACrDN,UAAU,CAACI,WAAW,CACvB;QACD,IAAI,CAACxI,YAAY,CAAC,QAAQ,CAAC,GAAGyI,YAAY,CAACrC,GAAG,CAAEuC,MAAM,KAAM;UAC1DxG,EAAE,EAAEwG,MAAM,CAACjE,KAAK;UAChBc,IAAI,EAAEmD,MAAM,CAACnD,IAAI;UACjBpD,IAAI,EAAE,OAAO;UACbqD,IAAI,EAAE,yBAAyB;UAC/BM,WAAW,EAAE,UAAU4C,MAAM,CAACnD,IAAI;SACnC,CAAC,CAAC;QACH,IAAI,CAACb,mBAAmB,EAAE;MAC5B;IACF;EACF;EAEQiE,iBAAiBA,CAAA;IACvB,IAAI,IAAI,CAAChL,gBAAgB,KAAK,eAAe,EAAE;MAC7C;MACA,IAAI,CAACrB,oBAAoB,CAACsM,iBAAiB,EAAE,CAACjF,SAAS,CAAC;QACtDwB,IAAI,EAAGmC,QAAe,IAAI;UACxB;UACA,IAAI,CAACvH,YAAY,CAAC,WAAW,CAAC,GAAGuH,QAAQ,CAACnB,GAAG,CAAE0C,EAAO,KAAM;YAC1D3G,EAAE,EAAE2G,EAAE,CAAC3G,EAAE;YACTqD,IAAI,EACFsD,EAAE,CAACC,cAAc,IACjBD,EAAE,CAACzG,KAAK,IACRyG,EAAE,CAACtD,IAAI,IACP,wBAAwB;YAC1BO,WAAW,EACT+C,EAAE,CAAC/C,WAAW,IACd,mBAAmB+C,EAAE,CAACC,cAAc,IAAID,EAAE,CAACzG,KAAK,IAAIyG,EAAE,CAACtD,IAAI,EAAE;YAC/DC,IAAI,EAAE,mCAAmC;YACzCrD,IAAI,EAAE,WAAoB;YAC1B;YACA4G,QAAQ,EAAEF,EAAE,CAACE,QAAQ;YACrBC,QAAQ,EAAEH,EAAE,CAACG,QAAQ;YACrBC,SAAS,EAAEJ,EAAE,CAACI;WACf,CAAC,CAAC;UACH,IAAI,CAACvE,mBAAmB,EAAE;QAC5B,CAAC;QACDsC,KAAK,EAAGA,KAAK,IAAI;UACfhD,OAAO,CAACgD,KAAK,CACX,4DAA4D,EAC5DA,KAAK,CACN;UACD;UACA;UACA,IAAI,CAACjH,YAAY,CAAC,WAAW,CAAC,GAAG,IAAI,CAACuF,iBAAiB,CACrD,WAAW,EACX,qBAAqB,EACrB,mCAAmC,EACnC,CAAC,CACF;UACD,IAAI,CAACZ,mBAAmB,EAAE;QAC5B;OACD,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACwE,2BAA2B,EAAE;IACpC;EACF;EAEQA,2BAA2BA,CAAA;IACjC,IAAI,CAACjN,YAAY,CAACkN,2BAA2B,EAAE,CAACxF,SAAS,CAAC;MACxDwB,IAAI,EAAGiE,cAAc,IAAI;QACvB,IAAI,CAACrJ,YAAY,CAAC,WAAW,CAAC,GAAGqJ,cAAc,CAACjD,GAAG,CAAEkD,IAAS,KAAM;UAClEnH,EAAE,EAAEmH,IAAI,CAACnH,EAAE;UACXqD,IAAI,EAAE8D,IAAI,CAAC9D,IAAI;UACfO,WAAW,EAAEuD,IAAI,CAACvD,WAAW;UAC7BN,IAAI,EAAE6D,IAAI,CAAC7D,IAAI;UACfrD,IAAI,EAAE;SACP,CAAC,CAAC;QACH,IAAI,CAACuC,mBAAmB,EAAE;MAC5B,CAAC;MACDsC,KAAK,EAAGA,KAAK,IAAI;QACfhD,OAAO,CAACgD,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;QACtE;QACA,IAAI,CAACjH,YAAY,CAAC,WAAW,CAAC,GAAG,IAAI,CAACuF,iBAAiB,CACrD,WAAW,EACX,qBAAqB,EACrB,mCAAmC,EACnC,CAAC,CACF;QACD,IAAI,CAACZ,mBAAmB,EAAE;MAC5B;KACD,CAAC;EACJ;EAEA;EACQ4E,0BAA0BA,CAAA;IAChC,IAAI,CAAC,IAAI,CAAC/H,WAAW,EAAE;IAEvB,MAAMuG,cAAc,GAAG,IAAI,CAACvG,WAAW,CAACuG,cAAc,IAAI,EAAE;IAC5D,MAAMyB,WAAW,GAAGzB,cAAc,CAACE,IAAI,CACpCC,QAAa,IAAKA,QAAQ,CAACC,UAAU,KAAK,CAAC,CAC7C;IAED,IAAIqB,WAAW,EAAE;MACf,MAAMC,cAAc,GAAGD,WAAW,CAACnB,MAAM,CAACJ,IAAI,CAC3CK,KAAU,IAAKA,KAAK,CAACC,SAAS,KAAK,wBAAwB,CAC7D;MACD,IAAIkB,cAAc,IAAIA,cAAc,CAACjB,WAAW,EAAE;QAChD,MAAMkB,eAAe,GAAG,IAAI,CAACxN,YAAY,CAACwM,gBAAgB,CACxDe,cAAc,CAACjB,WAAW,CAC3B;QACD,IAAI,CAACxI,YAAY,CAAC,WAAW,CAAC,GAAG0J,eAAe,CAACtD,GAAG,CAAEuC,MAAM,KAAM;UAChExG,EAAE,EAAEwG,MAAM,CAACjE,KAAK;UAAE;UAClBc,IAAI,EAAEmD,MAAM,CAACnD,IAAI;UACjBpD,IAAI,EAAE,WAAW;UACjBqD,IAAI,EAAE,6BAA6B;UACnCM,WAAW,EAAE,mBAAmB4C,MAAM,CAACnD,IAAI;SAC5C,CAAC,CAAC;MACL;IACF;EACF;EAEQmE,SAASA,CAAA;IACf;IACA,MAAMC,aAAa,GAAG,IAAI,CAAC3N,YAAY,CAAC4N,mBAAmB,EAAE;IAE7D;IACAD,aAAa,CAAChG,SAAS,CAAC;MACtBwB,IAAI,EAAGmC,QAAa,IAAI;QACtB,MAAMuC,YAAY,GAAGvC,QAAQ,CAAC3H,KAAK,IAAI,EAAE;QACzC,MAAMmK,gBAAgB,GAAGD,YAAY,CAAC1D,GAAG,CAAElH,IAAS,KAAM;UACxDiD,EAAE,EAAE,WAAWjD,IAAI,CAAC8K,MAAM,EAAE;UAC5BxE,IAAI,EAAEtG,IAAI,CAAC+K,QAAQ,IAAI,uBAAuB;UAC9ClE,WAAW,EAAE,kCAAkC;UAC/CN,IAAI,EAAE,yBAAyB;UAC/BrD,IAAI,EAAE;SACP,CAAC,CAAC;QAEH;QACA,IAAI,CAAC8H,2BAA2B,CAACH,gBAAgB,CAAC;MACpD,CAAC;MACD9C,KAAK,EAAGA,KAAU,IAAI;QACpBhD,OAAO,CAACgD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD;QACA,IAAI,CAACiD,2BAA2B,CAAC,EAAE,CAAC;MACtC;KACD,CAAC;EACJ;EAEQA,2BAA2BA,CAACH,gBAAuB;IACzD;IACA,IAAI,CAAC9N,YAAY,CAACkO,gBAAgB,CAAC,CAAC,EAAE,EAAE,CAAC,CAACvG,SAAS,CAAC;MAClDwB,IAAI,EAAGgF,YAAiB,IAAI;QAC1B,MAAMC,YAAY,GAAGD,YAAY,CAACE,gBAAgB,IAAI,EAAE;QAExD;QACA,IAAI,CAACrO,YAAY,CAACkO,gBAAgB,CAAC,CAAC,EAAEE,YAAY,CAAC,CAACzG,SAAS,CAAC;UAC5DwB,IAAI,EAAGmF,gBAAqB,IAAI;YAC9B,MAAMC,SAAS,GAAGD,gBAAgB,CAACE,eAAe,IAAI,EAAE;YACxD,MAAMC,aAAa,GAAGF,SAAS,CAACpE,GAAG,CAAElH,IAAS,KAAM;cAClDiD,EAAE,EAAE,QAAQjD,IAAI,CAACiD,EAAE,EAAE;cACrBqD,IAAI,EAAEtG,IAAI,CAACsG,IAAI,IAAI,mBAAmB;cACtCO,WAAW,EACT7G,IAAI,CAAC6G,WAAW,IAAI,sCAAsC;cAC5DN,IAAI,EAAE,yBAAyB;cAC/BrD,IAAI,EAAE;aACP,CAAC,CAAC;YAEH;YACA,IAAI,CAACpC,YAAY,CAAC,OAAO,CAAC,GAAG,CAC3B,GAAG+J,gBAAgB,EACnB,GAAGW,aAAa,CACjB;YACD,IAAI,CAAC/F,mBAAmB,EAAE;UAC5B,CAAC;UACDsC,KAAK,EAAGA,KAAK,IAAI;YACfhD,OAAO,CAACgD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;YAC3D;YACA,IAAI,CAACjH,YAAY,CAAC,OAAO,CAAC,GAAG+J,gBAAgB;YAC7C,IAAI,CAACpF,mBAAmB,EAAE;UAC5B;SACD,CAAC;MACJ,CAAC;MACDsC,KAAK,EAAGA,KAAK,IAAI;QACfhD,OAAO,CAACgD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD;QACA,IAAI,CAACjH,YAAY,CAAC,OAAO,CAAC,GAAG+J,gBAAgB;QAC7C,IAAI,CAACpF,mBAAmB,EAAE;MAC5B;KACD,CAAC;EACJ;EAEQgG,cAAcA,CAAA;IACpB,IAAI,CAACzO,YAAY,CAAC0O,uBAAuB,EAAE,CAAChH,SAAS,CAAC;MACpDwB,IAAI,EAAGzF,UAAiB,IAAI;QAC1B,IAAI,CAACK,YAAY,CAAC,YAAY,CAAC,GAAGL,UAAU,CAACyG,GAAG,CAAEpH,SAAc,KAAM;UACpEmD,EAAE,EAAEnD,SAAS,CAACmD,EAAE;UAChBqD,IAAI,EAAExG,SAAS,CAACwG,IAAI;UACpBO,WAAW,EAAE/G,SAAS,CAAC+G,WAAW;UAClCN,IAAI,EAAEzG,SAAS,CAACyG,IAAI;UACpBrD,IAAI,EAAE,WAAoB;UAC1ByI,IAAI,EAAE7L,SAAS,CAAC6L;SACjB,CAAC,CAAC;QACH,IAAI,CAAClG,mBAAmB,EAAE;QAC1B,IAAI,CAACA,mBAAmB,EAAE;MAC5B,CAAC;MACDsC,KAAK,EAAGA,KAAU,IAAI;QACpBhD,OAAO,CAACgD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;QACjE;QACA,IAAI,CAACjH,YAAY,CAAC,YAAY,CAAC,GAAG,IAAI,CAACuF,iBAAiB,CACtD,WAAW,EACX,gBAAgB,EAChB,0BAA0B,EAC1B,CAAC,CACF;QACD,IAAI,CAACZ,mBAAmB,EAAE;MAC5B;KACD,CAAC;EACJ;EAEA;EACQmG,uBAAuBA,CAAA;IAC7B,IAAI,CAAC,IAAI,CAACtJ,WAAW,EAAE;IAEvB,MAAMuG,cAAc,GAAG,IAAI,CAACvG,WAAW,CAACuG,cAAc,IAAI,EAAE;IAC5D,MAAMgD,aAAa,GAAGhD,cAAc,CAACE,IAAI,CACtCC,QAAa,IAAKA,QAAQ,CAACC,UAAU,KAAK,CAAC,CAC7C;IAED,IAAI4C,aAAa,EAAE;MACjB;MACA,IAAI,CAAC/K,YAAY,CAAC,YAAY,CAAC,GAAG+K,aAAa,CAAC1C,MAAM,CACnDlB,MAAM,CAAEmB,KAAU,IAAKA,KAAK,CAAC0C,SAAS,KAAK,QAAQ,CAAC,CAAC;MAAA,CACrD5E,GAAG,CAAEkC,KAAU,KAAM;QACpBnG,EAAE,EAAEmG,KAAK,CAAC2C,OAAO,CAAC5E,QAAQ,EAAE;QAC5Bb,IAAI,EAAE8C,KAAK,CAAC4C,SAAS;QACrBL,IAAI,EAAEvC,KAAK,CAACC,SAAS;QACrBnG,IAAI,EAAE,WAAoB;QAC1BqD,IAAI,EAAE,6BAA6B;QACnCM,WAAW,EAAEuC,KAAK,CAAC6C,SAAS,IAAI,cAAc7C,KAAK,CAAC4C,SAAS;OAC9D,CAAC,CAAC;IACP;EACF;EAEQE,cAAc,GAAe,EAAE;EAEvC,IAAIC,eAAeA,CAAA;IACjB,OAAO,IAAI,CAACrL,YAAY,CAAC,IAAI,CAACjD,SAAS,CAAC,IAAI,EAAE;EAChD;EACA,IAAIuO,aAAaA,CAAA;IACf,OAAO,IAAI,CAACF,cAAc;EAC5B;EAEQzG,mBAAmBA,CAAA;IACzB,MAAM4G,YAAY,GAAG,IAAI,CAACF,eAAe;IACzC,MAAMG,cAAc,GAAGD,YAAY,CAACpE,MAAM,CAAEjI,IAAI,IAAI;MAClD,MAAMuM,cAAc,GAAG,IAAI,CAAChO,eAAe,CAACiO,IAAI,CAC7CC,IAAI,IAAKA,IAAI,CAACC,gBAAgB,IAAID,IAAI,CAACC,gBAAgB,CAACzJ,EAAE,KAAKjD,IAAI,CAACiD,EAAE,CACxE;MACD,OAAO,CAACsJ,cAAc;IACxB,CAAC,CAAC;IAEF,IAAI,CAAC,IAAI,CAACzO,WAAW,EAAE;MACrB,IAAI,CAACoO,cAAc,GAAGI,cAAc;MACpC;IACF;IAEA,MAAMK,KAAK,GAAG,IAAI,CAAC7O,WAAW,CAAC8O,WAAW,EAAE;IAC5C,IAAI,CAACV,cAAc,GAAGI,cAAc,CAACrE,MAAM,CACxCjI,IAAI,IACFA,IAAI,CAACsG,IAAI,IAAItG,IAAI,CAACsG,IAAI,CAACsG,WAAW,EAAE,CAACC,QAAQ,CAACF,KAAK,CAAC,IACpD3M,IAAI,CAAC6G,WAAW,IAAI7G,IAAI,CAAC6G,WAAW,CAAC+F,WAAW,EAAE,CAACC,QAAQ,CAACF,KAAK,CAAE,CACvE;EACH;EAEAG,WAAWA,CAACC,QAAyB;IACnC,IAAI,CAAClP,SAAS,GAAGkP,QAAQ,CAAC5F,QAAQ,EAAE;IACpC,IAAI,CAACrJ,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,UAAU,CAACsH,GAAG,CAAC,QAAQ,CAAC,EAAE2H,QAAQ,CAAC,EAAE,EAAE;MAAEC,SAAS,EAAE;IAAK,CAAE,CAAC;IACjE,IAAI,CAACxH,mBAAmB,EAAE;EAC5B;EAEAyH,cAAcA,CAACP,KAAa;IAC1B,IAAI,CAAC7O,WAAW,GAAG6O,KAAK;IACxB,IAAI,CAAClH,mBAAmB,EAAE;EAC5B;EAEA0H,iBAAiBA,CAACJ,QAAyB;IACzC,IAAI,CAAClP,SAAS,GAAGkP,QAAQ,CAAC5F,QAAQ,EAAE;IACpC,IAAI,CAACrJ,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,UAAU,CAACsH,GAAG,CAAC,QAAQ,CAAC,EAAE2H,QAAQ,CAAC,EAAE,EAAE;MAAEC,SAAS,EAAE;IAAK,CAAE,CAAC;IACjE,IAAI,CAACxH,mBAAmB,EAAE;EAC5B;EAEA2H,aAAaA,CAAA;IACX,IAAI,CAACpP,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;EACpD;EACAqP,iBAAiBA,CAACN,QAAyB;IACzC,OAAO,IAAI,CAAClP,SAAS,KAAKkP,QAAQ,CAAC5F,QAAQ,EAAE;EAC/C;EAEAmG,UAAUA,CAACC,GAAQ;IACjB,MAAMC,OAAO,GAA8B;MACzCnN,OAAO,EAAE,UAAU;MACnBG,MAAM,EAAE,KAAK;MACbX,SAAS,EAAE,UAAU;MACrBY,UAAU,EAAE,QAAQ;MACpBC,KAAK,EAAE;KACR;IACD,OAAO8M,OAAO,CAACD,GAAG,CAAC/H,KAAK,CAAC,IAAI,QAAQ;EACvC;EAEQiI,mBAAmBA,CAACC,QAAgB;IAC1C,MAAMC,aAAa,GACjB,IAAI,CAAClO,UAAU,CAAC,IAAI,CAACf,gBAAgD,CAAC;IACxE,MAAMkP,gBAAgB,GACpBD,aAAa,CAACD,QAAsC,CAAC;IAEvD,IAAIE,gBAAgB,KAAK,CAAC,EAAE;MAC1B,MAAMC,OAAO,GAAG,IAAI,CAACC,kBAAkB,CAACJ,QAAQ,CAAC;MACjD,IAAIG,OAAO,EAAE;QACX,IAAI,CAAChQ,SAAS,GAAGgQ,OAAO;QACxB,IAAI,CAAC/P,WAAW,GAAG,EAAE;QACrB,IAAI,CAACC,UAAU,CAACsH,GAAG,CAAC,QAAQ,CAAC,EAAE2H,QAAQ,CAAC,EAAE,EAAE;UAAEC,SAAS,EAAE;QAAK,CAAE,CAAC;QACjE,IAAI,CAACxH,mBAAmB,EAAE;MAC5B;IACF;EACF;EAEQqI,kBAAkBA,CAACC,eAAuB;IAChD,MAAMJ,aAAa,GACjB,IAAI,CAAClO,UAAU,CAAC,IAAI,CAACf,gBAAgD,CAAC;IAExE,IAAIqP,eAAe,KAAK,QAAQ,EAAE;MAChC,MAAMC,QAAQ,GAAG,IAAI,CAACzP,eAAe,CAACiO,IAAI,CACvCC,IAAI,IAAKA,IAAI,CAACvJ,IAAI,KAAK,OAAO,CAChC;MACD,IAAI,CAAC8K,QAAQ,IAAIL,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;QAC7C,OAAO,QAAQ;MACjB;IACF,CAAC,MAAM,IAAII,eAAe,KAAK,OAAO,EAAE;MACtC,OAAO,WAAW;IACpB;IAEA,OAAO,IAAI;EACb;EAEAE,aAAaA,CAAC7D,IAAc;IAC1B,IAAI,CAAClM,YAAY,GAAGkM,IAAI;IACxB,IAAI,CAACnM,WAAW,GAAG,IAAI;IACvB,IAAI,CAACiQ,eAAe,CAAC9D,IAAI,CAAC;IAC1B,IAAI,CAAC9M,aAAa,CAAC6Q,IAAI,CAAC3R,2BAA2B,EAAE;MACnD2B,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BiQ,YAAY,EAAEA,CAAA,KAAM,IAAI,CAAC9Q,aAAa,CAAC6H,KAAK;KAC7C,CAAC;EACJ;EAEAiJ,YAAYA,CAAA;IACV,IAAI,CAACnQ,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,WAAW,GAAG,IAAI;EACzB;EAEQ+P,eAAeA,CAAC9D,IAAc;IACpC,IAAI,CAACjM,WAAW,GAAG;MACjB+E,IAAI,EAAEkH,IAAI,CAAClH,IAAI;MACfC,KAAK,EAAEiH,IAAI,CAAC9D,IAAI;MAChB+H,IAAI,EAAE;QAAE,GAAGjE;MAAI,CAAE;MACjBkE,OAAO,EAAE,IAAI;MACbvG,KAAK,EAAE;KACR;IAED;IACA,QAAQqC,IAAI,CAAClH,IAAI;MACf,KAAK,QAAQ;QACX,IAAI,CAACqL,iBAAiB,CAACnE,IAAI,CAAC;QAC5B;MACF,KAAK,OAAO;QACV,IAAI,CAACoE,gBAAgB,CAACpE,IAAI,CAAC;QAC3B;MACF,KAAK,WAAW;QACd,IAAI,CAACqE,oBAAoB,CAACrE,IAAI,CAAC;QAC/B;MACF,KAAK,WAAW;QACd,IAAI,CAACsE,oBAAoB,CAACtE,IAAI,CAAC;QAC/B;MACF,KAAK,MAAM;QACT,IAAI,CAACuE,eAAe,CAACvE,IAAI,CAAC;QAC1B;MACF;QACE,IAAI,CAACjM,WAAW,CAACmQ,OAAO,GAAG,KAAK;IACpC;EACF;EAEQC,iBAAiBA,CAACnE,IAAc;IACtC,IAAIA,IAAI,CAACnH,EAAE,EAAE;MACX,IAAI,CAAChG,cAAc,CAAC2R,aAAa,CAACxE,IAAI,CAACnH,EAAE,CAACkE,QAAQ,EAAE,CAAC,CAACzC,SAAS,CAAC;QAC9DwB,IAAI,EAAGmC,QAAa,IAAI;UACtB,IAAI,CAAClK,WAAW,CAACkQ,IAAI,GAAG;YACtB,GAAG,IAAI,CAAClQ,WAAW,CAACkQ,IAAI;YACxB,GAAGhG,QAAQ;YACXxB,WAAW,EACTwB,QAAQ,CAACxB,WAAW,IACpBwB,QAAQ,CAACf,uBAAuB,IAChC8C,IAAI,CAACvD,WAAW;YAClBgI,UAAU,EACRxG,QAAQ,CAACyG,QAAQ,IACjBzG,QAAQ,CAAC0G,OAAO,IAChB1G,QAAQ,CAAC2G,cAAc,IACvB3G,QAAQ,CAAC1I,MAAM;YACjBsP,SAAS,EAAE5G,QAAQ,CAAC4G,SAAS,IAAI5G,QAAQ,CAAC6G,MAAM,IAAI,SAAS;YAC7DC,SAAS,EACP9G,QAAQ,CAAC8G,SAAS,IAClB9G,QAAQ,CAAC+G,SAAS,IAClB,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;YAC1B7H,IAAI,EAAEY,QAAQ,CAACZ,IAAI;YACnBC,IAAI,EAAEW,QAAQ,CAACX,IAAI;YACnBC,SAAS,EAAEU,QAAQ,CAACV,SAAS;YAC7BC,cAAc,EACZS,QAAQ,CAACT,cAAc,IAAIS,QAAQ,CAACR;WACvC;UACD,IAAI,CAAC1J,WAAW,CAACmQ,OAAO,GAAG,KAAK;QAClC,CAAC;QACDvG,KAAK,EAAGA,KAAK,IAAI;UACfhD,OAAO,CAACgD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;UACrD,IAAI,CAAC5J,WAAW,CAAC4J,KAAK,GAAG,+BAA+B;UACxD,IAAI,CAAC5J,WAAW,CAACmQ,OAAO,GAAG,KAAK;QAClC;OACD,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACnQ,WAAW,CAACmQ,OAAO,GAAG,KAAK;IAClC;EACF;EAEQE,gBAAgBA,CAACpE,IAAc;IACrC,IAAIA,IAAI,CAACnH,EAAE,IAAI,IAAI,CAACzF,YAAY,EAAE+R,cAAc,EAAE;MAChD,IAAI,CAAC/R,YAAY,CAAC+R,cAAc,CAACnF,IAAI,CAACnH,EAAE,CAACkE,QAAQ,EAAE,CAAC,CAACzC,SAAS,CAAC;QAC7DwB,IAAI,EAAGmC,QAAa,IAAI;UACtB,MAAMmH,SAAS,GAAGnH,QAAQ;UAC1B,IAAI,CAAClK,WAAW,CAACkQ,IAAI,GAAG;YACtB,GAAG,IAAI,CAAClQ,WAAW,CAACkQ,IAAI;YACxB9F,gBAAgB,EAAEiH,SAAS,CAACjH,gBAAgB,IAAI6B,IAAI,CAACvD,WAAW;YAChE2B,SAAS,EAAEgH,SAAS,CAAC5P,KAAK,IAAIwK,IAAI,CAAC9D,IAAI;YACvCmJ,WAAW,EAAED,SAAS,CAACC,WAAW,IAAI,GAAG;YACzCC,QAAQ,EAAEF,SAAS,CAACE,QAAQ,IAAI,IAAI;YACpCC,IAAI,EAAEH,SAAS,CAACG,IAAI,IAAI,IAAI;YAC5BC,YAAY,EAAEJ,SAAS,CAACI,YAAY,IAAI,CAAC;YACzCnH,QAAQ,EAAE+G,SAAS,CAAC/G,QAAQ,IAAI,QAAQ;YACxCoH,OAAO,EAAEL,SAAS,CAACM,UAAU,IAAIN,SAAS,CAACO,OAAO;YAClDC,iBAAiB,EAAER,SAAS,CAAClH,mBAAmB,IAAI8B,IAAI,CAAC9D,IAAI;YAC7D2J,MAAM,EAAET,SAAS,CAACU,aAAa,IAAIV,SAAS,CAACW,YAAY;YACzDC,UAAU,EAAEZ,SAAS,CAACY,UAAU;YAChCC,UAAU,EAAEb,SAAS,CAACa,UAAU,IAAI,EAAE;YACtCzQ,KAAK,EAAE4P,SAAS,CAAC5P,KAAK,IAAI,EAAE;YAC5BqP,SAAS,EAAEO,SAAS,CAACP,SAAS,IAAI,SAAS;YAC3CqB,SAAS,EAAEd,SAAS,CAACe,IAAI,IAAI,IAAIlB,IAAI,EAAE,CAACC,WAAW;WACpD;UACD,IAAI,CAACnR,WAAW,CAACmQ,OAAO,GAAG,KAAK;QAClC,CAAC;QACDvG,KAAK,EAAGA,KAAU,IAAI;UACpBhD,OAAO,CAACgD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;UACpD,IAAI,CAACyI,mBAAmB,CAACpG,IAAI,CAAC;QAChC;OACD,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACoG,mBAAmB,CAACpG,IAAI,CAAC;IAChC;EACF;EAEQoG,mBAAmBA,CAACpG,IAAc;IACxC,IAAI,CAACjM,WAAW,CAACkQ,IAAI,GAAG;MACtB,GAAG,IAAI,CAAClQ,WAAW,CAACkQ,IAAI;MACxB9F,gBAAgB,EAAE6B,IAAI,CAACvD,WAAW,IAAI,UAAUuD,IAAI,CAAC9D,IAAI,EAAE;MAC3DkC,SAAS,EAAE4B,IAAI,CAAC9D,IAAI;MACpBmJ,WAAW,EAAE,GAAG;MAChBC,QAAQ,EAAE,IAAI;MACdC,IAAI,EAAE,IAAI;MACVC,YAAY,EAAE,CAAC;MACfnH,QAAQ,EAAE,QAAQ;MAClBoH,OAAO,EAAE,EAAE;MACXG,iBAAiB,EAAE5F,IAAI,CAAC9D,IAAI;MAC5B2J,MAAM,EAAE,KAAK;MACbG,UAAU,EAAE,oBAAoB;MAChCnB,SAAS,EAAE,QAAQ;MACnBqB,SAAS,EAAE,IAAIjB,IAAI,EAAE,CAACC,WAAW;KAClC;IACD,IAAI,CAACnR,WAAW,CAACmQ,OAAO,GAAG,KAAK;EAClC;EAEQG,oBAAoBA,CAACrE,IAAc;IACzC,IAAIA,IAAI,CAACnH,EAAE,EAAE;MACX,IAAI,CAAC5F,oBAAoB,CACtBoT,oBAAoB,CAACrG,IAAI,CAACnH,EAAE,CAACkE,QAAQ,EAAE,CAAC,CACxCzC,SAAS,CAAC;QACTwB,IAAI,EAAGmC,QAAa,IAAI;UACtB,IAAI,CAAClK,WAAW,CAACkQ,IAAI,GAAG;YACtB,GAAG,IAAI,CAAClQ,WAAW,CAACkQ,IAAI;YACxB/H,IAAI,EAAE8D,IAAI,CAAC9D,IAAI;YACfO,WAAW,EAAEuD,IAAI,CAACvD,WAAW;YAC7B6J,cAAc,EACZrI,QAAQ,CAACsI,KAAK,GAAG,CAAC,CAAC,EAAErI,mBAAmB,IACxC,wBAAwB;YAC1B0B,SAAS,EAAE,GAAG;YACd4G,UAAU,EAAE,iBAAiB;YAC7BC,aAAa,EAAExI,QAAQ,CAACwI,aAAa,IAAI,QAAQ;YACjDF,KAAK,EAAEtI,QAAQ,CAACsI,KAAK,IAAI,EAAE;YAC3BG,SAAS,EAAEzI,QAAQ,CAACsI,KAAK,EAAE9K,MAAM,IAAI,CAAC;YACtCkL,SAAS,EACP1I,QAAQ,CAACsI,KAAK,EAAEK,MAAM,CACpB,CAACC,GAAW,EAAEC,IAAS,KAAKD,GAAG,IAAIC,IAAI,CAACC,aAAa,IAAI,CAAC,CAAC,EAC3D,CAAC,CACF,IAAI,CAAC;YACRlC,SAAS,EAAE5G,QAAQ,CAACsI,KAAK,GAAG,CAAC,CAAC,EAAES,UAAU,IAAI,SAAS;YACvDC,WAAW,EACThJ,QAAQ,CAACsI,KAAK,GAAG,CAAC,CAAC,EAAEW,UAAU,IAAI,IAAIjC,IAAI,EAAE,CAACC,WAAW;WAC5D;UACD,IAAI,CAACnR,WAAW,CAACmQ,OAAO,GAAG,KAAK;QAClC,CAAC;QACDvG,KAAK,EAAGA,KAAU,IAAI;UACpBhD,OAAO,CAACgD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;UAC7D,IAAI,CAACwJ,uBAAuB,CAACnH,IAAI,CAAC;QACpC;OACD,CAAC;IACN;EACF;EAEQmH,uBAAuBA,CAACnH,IAAc;IAC5C,IAAI,CAACjM,WAAW,CAACkQ,IAAI,GAAG;MACtB,GAAG,IAAI,CAAClQ,WAAW,CAACkQ,IAAI;MACxB/H,IAAI,EAAE8D,IAAI,CAAC9D,IAAI;MACfO,WAAW,EAAEuD,IAAI,CAACvD,WAAW,IAAI,mBAAmBuD,IAAI,CAAC9D,IAAI,EAAE;MAC/DoK,cAAc,EAAE,wBAAwB;MACxC1G,SAAS,EAAE,GAAG;MACd4G,UAAU,EAAE,iBAAiB;MAC7BD,KAAK,EAAE,EAAE;MACT1B,SAAS,EAAE,QAAQ;MACnBoC,WAAW,EAAE,IAAIhC,IAAI,EAAE,CAACC,WAAW;KACpC;IACD,IAAI,CAACnR,WAAW,CAACmQ,OAAO,GAAG,KAAK;EAClC;EAEQI,oBAAoBA,CAACtE,IAAc;IACzC,IAAIA,IAAI,CAAC9D,IAAI,IAAI,IAAI,CAAC/I,iBAAiB,EAAEiU,kBAAkB,EAAE;MAC3D,IAAI,CAACjU,iBAAiB,CAACiU,kBAAkB,CAACpH,IAAI,CAAC9D,IAAI,CAAC,CAAC5B,SAAS,CAAC;QAC7DwB,IAAI,EAAGpG,SAAc,IAAI;UACvB,IAAIA,SAAS,EAAE;YACb,IAAI,CAAC3B,WAAW,CAACkQ,IAAI,GAAG;cACtB,GAAG,IAAI,CAAClQ,WAAW,CAACkQ,IAAI;cACxB/H,IAAI,EAAExG,SAAS,CAACwG,IAAI,IAAI8D,IAAI,CAAC9D,IAAI;cACjCO,WAAW,EAAE/G,SAAS,CAAC+G,WAAW,IAAIuD,IAAI,CAACvD,WAAW;cACtDkI,OAAO,EAAEjP,SAAS,CAACiP,OAAO;cAC1B0C,WAAW,EAAE3R,SAAS,CAAC2R,WAAW,IAAI,EAAE;cACxCC,YAAY,EAAE5R,SAAS,CAAC4R,YAAY,IAAI,WAAW;cACnDC,SAAS,EAAE7R,SAAS,CAAC6R,SAAS,IAAI,EAAE;cACpCC,OAAO,EAAE9R,SAAS,CAAC8R,OAAO,IAAI,KAAK;cACnC3C,SAAS,EAAEnP,SAAS,CAACmP,SAAS,IAAI,SAAS;cAC3CoC,WAAW,EACTvR,SAAS,CAACuR,WAAW,IACrBvR,SAAS,CAACwQ,SAAS,IACnB,IAAIjB,IAAI,EAAE,CAACC,WAAW;aACzB;UACH;UACA,IAAI,CAACnR,WAAW,CAACmQ,OAAO,GAAG,KAAK;QAClC,CAAC;QACDvG,KAAK,EAAGA,KAAU,IAAI;UACpBhD,OAAO,CAACgD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QAC1D;OACD,CAAC;IACJ;EACF;EAEQ8J,uBAAuBA,CAACzH,IAAc;IAC5C,IAAI,CAACjM,WAAW,CAACkQ,IAAI,GAAG;MACtB,GAAG,IAAI,CAAClQ,WAAW,CAACkQ,IAAI;MACxB/H,IAAI,EAAE8D,IAAI,CAAC9D,IAAI;MACfO,WAAW,EAAEuD,IAAI,CAACvD,WAAW,IAAI,cAAcuD,IAAI,CAAC9D,IAAI,EAAE;MAC1DyI,OAAO,EAAE,yDAAyD;MAClEE,SAAS,EAAE,QAAQ;MACnBoC,WAAW,EAAE,IAAIhC,IAAI,EAAE,CAACC,WAAW;KACpC;IACD,IAAI,CAACnR,WAAW,CAACmQ,OAAO,GAAG,KAAK;EAClC;EAEQK,eAAeA,CAACvE,IAAc;IACpC,IAAIA,IAAI,CAACnH,EAAE,IAAI,IAAI,CAAClG,YAAY,CAAC+U,kBAAkB,EAAE;MACnD;MACA,IAAIhH,MAAc;MAElB,IAAI,OAAOV,IAAI,CAACnH,EAAE,KAAK,QAAQ,IAAImH,IAAI,CAACnH,EAAE,CAAC8O,UAAU,CAAC,OAAO,CAAC,EAAE;QAC9D;QACA,MAAMC,WAAW,GAAG5H,IAAI,CAACnH,EAAE,CAACgP,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;QAChDnH,MAAM,GAAGoH,MAAM,CAACF,WAAW,CAAC;MAC9B,CAAC,MAAM;QACLlH,MAAM,GAAGoH,MAAM,CAAC9H,IAAI,CAACnH,EAAE,CAAC;MAC1B;MAEA,IAAIkP,KAAK,CAACrH,MAAM,CAAC,EAAE;QACjB/F,OAAO,CAACqN,IAAI,CAAC,kBAAkB,EAAEhI,IAAI,CAACnH,EAAE,EAAE,qBAAqB,CAAC;QAChE,IAAI,CAAC9E,WAAW,CAACmQ,OAAO,GAAG,KAAK;QAChC;MACF;MAEA,IAAI,CAACvR,YAAY,CAAC+U,kBAAkB,CAAChH,MAAM,CAAC,CAACpG,SAAS,CAAC;QACrDwB,IAAI,EAAGmC,QAAa,IAAI;UACtBtD,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEqD,QAAQ,CAAC,CAAC,CAAC;UAEjD;UACA,IAAIgK,UAAU,EAAEC,WAAW;UAE3B,IAAIjK,QAAQ,CAACkK,cAAc,EAAE;YAC3B;YACAF,UAAU,GAAGhK,QAAQ,CAACkK,cAAc;YACpCD,WAAW,GAAGD,UAAU,CAACC,WAAW,IAAI,EAAE;UAC5C,CAAC,MAAM,IAAIjK,QAAQ,CAAC3H,KAAK,IAAI2H,QAAQ,CAAC3H,KAAK,CAAC,CAAC,CAAC,EAAE;YAC9C;YACA,MAAMV,IAAI,GAAGqI,QAAQ,CAAC3H,KAAK,CAAC,CAAC,CAAC;YAC9B2R,UAAU,GAAG;cACXpP,EAAE,EAAEjD,IAAI,CAAC8K,MAAM;cACfxE,IAAI,EAAEtG,IAAI,CAAC+K,QAAQ;cACnBlE,WAAW,EAAE7G,IAAI,CAACwS,eAAe;cACjCvD,SAAS,EAAEjP,IAAI,CAACiP,SAAS;cACzBG,SAAS,EAAEpP,IAAI,CAACyS,eAAe;cAC/BC,UAAU,EAAE1S,IAAI,CAAC2S,eAAe;cAChCC,SAAS,EAAE,CAAC5S,IAAI,CAAC6S;aAClB;YACDP,WAAW,GAAG;cACZQ,eAAe,EAAE,CAAC9S,IAAI,CAAC+S,aAAa,CAAC;cACrCC,cAAc,EAAE,CAAChT,IAAI,CAACiT,YAAY;aACnC;UACH,CAAC,MAAM;YACL;YACAZ,UAAU,GAAGhK,QAAQ;YACrBiK,WAAW,GAAG,EAAE;UAClB;UAEA;UACA,IAAIY,aAAa,GAAG,qBAAqB;UACzC,IAAIZ,WAAW,CAACU,cAAc,IAAIV,WAAW,CAACU,cAAc,CAAC,CAAC,CAAC,EAAE;YAC/DE,aAAa,GAAGZ,WAAW,CAACU,cAAc,CAAC,CAAC,CAAC;UAC/C,CAAC,MAAM,IAAIX,UAAU,CAACY,YAAY,EAAE;YAClCC,aAAa,GAAGb,UAAU,CAACY,YAAY;UACzC,CAAC,MAAM,IAAIZ,UAAU,CAAC1G,IAAI,EAAE;YAC1BuH,aAAa,GAAGb,UAAU,CAAC1G,IAAI;UACjC,CAAC,MAAM,IAAI0G,UAAU,CAACc,UAAU,EAAE;YAChCD,aAAa,GAAGb,UAAU,CAACc,UAAU;UACvC;UAEA,IAAI,CAAChV,WAAW,CAACkQ,IAAI,GAAG;YACtB,GAAG,IAAI,CAAClQ,WAAW,CAACkQ,IAAI;YACxBpL,EAAE,EAAEoP,UAAU,CAACpP,EAAE;YACjBqD,IAAI,EAAE+L,UAAU,CAAC/L,IAAI,IAAI8D,IAAI,CAAC9D,IAAI;YAClCO,WAAW,EAAEwL,UAAU,CAACxL,WAAW,IAAIuD,IAAI,CAACvD,WAAW;YACvDuM,SAAS,EACPd,WAAW,CAACQ,eAAe,GAAG,CAAC,CAAC,IAChCT,UAAU,CAACU,aAAa,IACxB,SAAS;YACXG,aAAa,EAAEA,aAAa;YAC5BL,UAAU,EAAE,CAACR,UAAU,CAACO,SAAS;YACjC3D,SAAS,EAAEoD,UAAU,CAACpD,SAAS,IAAI,SAAS;YAC5CqB,SAAS,EACP+B,UAAU,CAACjD,SAAS,IACpBiD,UAAU,CAACI,eAAe,IAC1B,IAAIpD,IAAI,EAAE,CAACC,WAAW,EAAE;YAC1B+D,UAAU,EAAEhB,UAAU,CAACgB,UAAU;YACjCX,UAAU,EAAEL,UAAU,CAACK,UAAU,IAAIL,UAAU,CAACM,eAAe;YAC/DC,SAAS,EAAEP,UAAU,CAACO,SAAS,IAAI;WACpC;UACD,IAAI,CAACzU,WAAW,CAACmQ,OAAO,GAAG,KAAK;QAClC,CAAC;QACDvG,KAAK,EAAGA,KAAU,IAAI;UACpBhD,OAAO,CAACgD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;UACnD,IAAI,CAAC5J,WAAW,CAAC4J,KAAK,GAAG,6BAA6B;UACtD,IAAI,CAAC5J,WAAW,CAACmQ,OAAO,GAAG,KAAK;QAClC;OACD,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACnQ,WAAW,CAACmQ,OAAO,GAAG,KAAK;IAClC;EACF;EAEAgF,cAAcA,CAACpQ,IAAY;IACzB,MAAMsK,OAAO,GAA8B;MACzC7N,MAAM,EAAE,UAAU;MAClBC,KAAK,EAAE,iCAAiC;MACxCC,SAAS,EAAE,mCAAmC;MAC9CG,IAAI,EAAE,yBAAyB;MAC/BF,SAAS,EAAE;KACZ;IACD,OAAO0N,OAAO,CAACtK,IAAI,CAAC,IAAI,yBAAyB,CAAC,CAAC;EACrD;EAEAqQ,mBAAmBA,CAAClF,IAAS;IAC3B,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IAEpB,MAAMmF,aAAa,GAAG,CACpB,IAAI,EACJ,MAAM,EACN,aAAa,EACb,WAAW,EACX,cAAc,EACd,YAAY,EACZ,WAAW,CACZ;IACD,MAAMC,gBAAgB,GAAkC,EAAE;IAE1DC,MAAM,CAACC,IAAI,CAACtF,IAAI,CAAC,CAACuF,OAAO,CAAEC,GAAG,IAAI;MAChC,IACE,CAACL,aAAa,CAAC3G,QAAQ,CAACgH,GAAG,CAAC,IAC5BxF,IAAI,CAACwF,GAAG,CAAC,KAAK,IAAI,IAClBxF,IAAI,CAACwF,GAAG,CAAC,KAAKC,SAAS,EACvB;QACAL,gBAAgB,CAACM,IAAI,CAAC;UACpBF,GAAG,EAAE,IAAI,CAACG,eAAe,CAACH,GAAG,CAAC;UAC9BrO,KAAK,EAAE6I,IAAI,CAACwF,GAAG;SAChB,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,OAAOJ,gBAAgB;EACzB;EAEQO,eAAeA,CAACC,SAAiB;IACvC,OAAOA,SAAS,CACbhC,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAC1BA,OAAO,CAAC,IAAI,EAAGiC,GAAG,IAAKA,GAAG,CAACC,WAAW,EAAE,CAAC,CACzCjM,IAAI,EAAE;EACX;EAEQvD,yBAAyBA,CAAA;IAC/B,IAAI,IAAI,CAACjG,gBAAgB,KAAK,YAAY,EAAE;MAC1C,IAAI,CAACiC,IAAI,GAAG,CACV;QAAEsC,EAAE,EAAE,SAAS;QAAEmG,KAAK,EAAE,SAAS;QAAEgL,QAAQ,EAAE;MAAU,CAAE,EACzD;QAAEnR,EAAE,EAAE,QAAQ;QAAEmG,KAAK,EAAE,QAAQ;QAAEgL,QAAQ,EAAE;MAAK,CAAE,EAClD;QAAEnR,EAAE,EAAE,WAAW;QAAEmG,KAAK,EAAE,gBAAgB;QAAEgL,QAAQ,EAAE;MAAU,CAAE,EAClE;QAAEnR,EAAE,EAAE,YAAY;QAAEmG,KAAK,EAAE,YAAY;QAAEgL,QAAQ,EAAE;MAAQ,CAAE,CAC9D;IACH,CAAC,MAAM;MACL,IAAI,CAACzT,IAAI,GAAG,CACV;QAAEsC,EAAE,EAAE,SAAS;QAAEmG,KAAK,EAAE,SAAS;QAAEgL,QAAQ,EAAE;MAAU,CAAE,EACzD;QAAEnR,EAAE,EAAE,QAAQ;QAAEmG,KAAK,EAAE,QAAQ;QAAEgL,QAAQ,EAAE;MAAK,CAAE,EAClD;QAAEnR,EAAE,EAAE,WAAW;QAAEmG,KAAK,EAAE,gBAAgB;QAAEgL,QAAQ,EAAE;MAAU,CAAE,EAClE;QAAEnR,EAAE,EAAE,OAAO;QAAEmG,KAAK,EAAE,OAAO;QAAEgL,QAAQ,EAAE;MAAQ,CAAE,CACpD;IACH;IAEA,IAAI,CAACxT,WAAW,GAAG,IAAI,CAACD,IAAI,CAACuG,GAAG,CAAEqG,GAAG,KAAM;MACzCnE,KAAK,EAAEmE,GAAG,CAACnE,KAAK,IAAI,EAAE;MACtB5D,KAAK,EAAE+H,GAAG,CAACtK,EAAE;MACbsD,IAAI,EAAEgH,GAAG,CAAC6G,QAAQ,IAAI,EAAE;MACxBC,QAAQ,EAAE;KACX,CAAC,CAAC;IAEH,IAAI,CAACxW,SAAS,GAAG,QAAQ;EAC3B;EAEQyW,gBAAgBA,CAAC5G,QAAgB;IACvC,MAAMC,aAAa,GACjB,IAAI,CAAClO,UAAU,CAAC,IAAI,CAACf,gBAAgD,CAAC;IACxE,MAAM6V,KAAK,GAAG5G,aAAa,CAACD,QAAsC,CAAC;IAEnE,IAAI6G,KAAK,KAAKT,SAAS,IAAIS,KAAK,KAAK,CAAC,CAAC,EAAE,OAAO,IAAI;IAEpD,MAAMC,YAAY,GAAG,IAAI,CAACjW,eAAe,CAAC0J,MAAM,CAC7CwE,IAAI,IAAKA,IAAI,CAACvJ,IAAI,KAAKwK,QAAQ,CACjC,CAAC7H,MAAM;IACR,OAAO2O,YAAY,GAAGD,KAAK;EAC7B;EAEQE,YAAYA,CAAC/G,QAAgB;IACnC,MAAMC,aAAa,GACjB,IAAI,CAAClO,UAAU,CAAC,IAAI,CAACf,gBAAgD,CAAC;IACxE,OAAOiP,aAAa,CAACD,QAAsC,CAAC,IAAI,CAAC,CAAC;EACpE;EAEQgH,kBAAkBA,CAAC1U,IAAS;IAClC,IAAI2U,OAAe;IACnB,IAAI3U,IAAI,CAACkD,IAAI,KAAK,WAAW,EAAE;MAC7ByR,OAAO,GAAG,YAAY;IACxB,CAAC,MAAM,IAAI3U,IAAI,CAACkD,IAAI,KAAK,WAAW,EAAE;MACpCyR,OAAO,GAAG,WAAW;IACvB,CAAC,MAAM;MACLA,OAAO,GAAG,GAAG3U,IAAI,CAACkD,IAAI,GAAG;IAC3B;IACA,IAAI,IAAI,CAACpC,YAAY,CAAC6T,OAAO,CAAC,EAAE;MAC9B,IAAI,CAAC7T,YAAY,CAAC6T,OAAO,CAAC,GAAG,IAAI,CAAC7T,YAAY,CAAC6T,OAAO,CAAC,CAAC1M,MAAM,CAC3DmC,IAAI,IAAKA,IAAI,CAACnH,EAAE,KAAKjD,IAAI,CAACiD,EAAE,CAC9B;IACH;EACF;EAEQ2R,iBAAiBA,CAACnI,IAAwB;IAChD,IAAIkI,OAAe;IACnB,IAAIlI,IAAI,CAACvJ,IAAI,KAAK,WAAW,EAAE;MAC7ByR,OAAO,GAAG,YAAY;IACxB,CAAC,MAAM,IAAIlI,IAAI,CAACvJ,IAAI,KAAK,WAAW,EAAE;MACpCyR,OAAO,GAAG,WAAW;IACvB,CAAC,MAAM;MACLA,OAAO,GAAG,GAAGlI,IAAI,CAACvJ,IAAI,GAAG;IAC3B;IACA,IAAI,IAAI,CAACpC,YAAY,CAAC6T,OAAO,CAAC,EAAE;MAC9B,MAAME,cAAc,GAAGpI,IAAI,CAACC,gBAAgB,EAAEzJ,EAAE;MAChD,MAAM6R,YAAY,GAAG,IAAI,CAAChU,YAAY,CAAC6T,OAAO,CAAC,CAAC5L,IAAI,CACjDqB,IAAI,IACHA,IAAI,CAAC9D,IAAI,KAAKmG,IAAI,CAACnG,IAAI,IACvB8D,IAAI,CAACnH,EAAE,KAAKwJ,IAAI,CAACxJ,EAAE,IAClB4R,cAAc,IAAIzK,IAAI,CAACnH,EAAE,KAAK4R,cAAe,CACjD;MAED,IAAI,CAACC,YAAY,EAAE;QACjB,IAAIC,QAAkB;QACtB,IAAItI,IAAI,CAACC,gBAAgB,EAAE;UACzBqI,QAAQ,GAAG;YAAE,GAAGtI,IAAI,CAACC;UAAgB,CAAE;QACzC,CAAC,MAAM;UACLqI,QAAQ,GAAG;YACT9R,EAAE,EAAEwJ,IAAI,CAACxJ,EAAE;YACXqD,IAAI,EAAEmG,IAAI,CAACnG,IAAI;YACfO,WAAW,EAAE,GAAG4F,IAAI,CAACvJ,IAAI,eAAeuJ,IAAI,CAACnG,IAAI,EAAE;YACnDC,IAAI,EAAEkG,IAAI,CAAClG,IAAI,IAAI,IAAI,CAAC+M,cAAc,CAAC7G,IAAI,CAACvJ,IAAI,CAAC;YACjDA,IAAI,EAAEuJ,IAAI,CAACvJ;WACZ;QACH;QACA,IAAI,CAACpC,YAAY,CAAC6T,OAAO,CAAC,CAACZ,IAAI,CAACgB,QAAQ,CAAC;MAC3C;IACF;EACF;EAEAC,aAAaA,CAACjI,QAAgB;IAC5B,MAAMW,QAAQ,GACZX,QAAQ,KAAK,YAAY,GAAG,WAAW,GAAGA,QAAQ,CAACkI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACjE,MAAMtH,aAAa,GACjB,IAAI,CAAClO,UAAU,CAAC,IAAI,CAACf,gBAAgD,CAAC;IACxE,MAAM6V,KAAK,GAAG5G,aAAa,CAACD,QAAsC,CAAC;IACnE,OAAO6G,KAAK,KAAK,CAAC;EACpB;EAEAW,2BAA2BA,CAAA;IAIzB,MAAMC,iBAAiB,GAAa,EAAE;IACtC,MAAMxH,aAAa,GACjB,IAAI,CAAClO,UAAU,CAAC,IAAI,CAACf,gBAAgD,CAAC;IAExEgV,MAAM,CAAC0B,OAAO,CAACzH,aAAa,CAAC,CAACiG,OAAO,CAAC,CAAC,CAAClG,QAAQ,EAAE6G,KAAK,CAAC,KAAI;MAC1D,IAAIA,KAAK,KAAK,CAAC,EAAE;QACf,MAAMc,YAAY,GAAG,IAAI,CAAC9W,eAAe,CAACiO,IAAI,CAC3CC,IAAI,IAAKA,IAAI,CAACvJ,IAAI,KAAKwK,QAAQ,CACjC;QACD,IAAI,CAAC2H,YAAY,EAAE;UACjBF,iBAAiB,CAACpB,IAAI,CAACrG,QAAQ,CAAC;QAClC;MACF;IACF,CAAC,CAAC;IAEF,OAAO;MAAE4H,OAAO,EAAEH,iBAAiB,CAACtP,MAAM,KAAK,CAAC;MAAEsP;IAAiB,CAAE;EACvE;EAEQvQ,oBAAoBA,CAAA;IAC1B,IAAI,CAAC9D,YAAY,GAAG;MAClBT,OAAO,EAAE,EAAE;MACXG,MAAM,EAAE,EAAE;MACVX,SAAS,EAAE,EAAE;MACba,KAAK,EAAE,EAAE;MACTD,UAAU,EAAE;KACb;IACD,IAAI,CAAC8U,iBAAiB,EAAE;EAC1B;EAEQA,iBAAiBA,CAAA;IACvB,IAAI,IAAI,CAAC7W,gBAAgB,KAAK,eAAe,EAAE;MAC7C,IAAI,CAACoI,WAAW,EAAE;MAClB,IAAI,CAACqB,UAAU,EAAE;MACjB,IAAI,CAACuB,iBAAiB,EAAE;MACxB,IAAI,CAACe,SAAS,EAAE;IAClB,CAAC,MAAM;MACL,IAAI,CAACzN,YAAY,CAACwY,SAAS,EAAE,CAAC9Q,SAAS,CAAC;QACtCwB,IAAI,EAAGmC,QAAa,IAAI;UACtB,IAAI,CAAC/F,WAAW,GAAG+F,QAAQ;UAC3B,IAAI,CAACvB,WAAW,EAAE;UAClB,IAAI,CAAC8B,mBAAmB,EAAE;UAC1B,IAAI,CAACyB,0BAA0B,EAAE;UACjC,IAAI,CAACuB,uBAAuB,EAAE;QAChC,CAAC;QACD7D,KAAK,EAAGA,KAAK,IAAI;UACfhD,OAAO,CAACgD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;UACjD,IAAI,CAACjB,WAAW,EAAE;UAClB,IAAI,CAACqB,UAAU,EAAE;UACjB,IAAI,CAACuB,iBAAiB,EAAE;UACxB,IAAI,CAAC+B,cAAc,EAAE;QACvB;OACD,CAAC;IACJ;EACF;EAEAgK,eAAeA,CAAA;IACb,OAAO,IAAI,CAAC/W,gBAAgB,KAAK,YAAY,GACzC,0BAA0B,GAC1B,6BAA6B;EACnC;EAEAgX,WAAWA,CAACC,KAAgB,EAAE3V,IAAc;IAC1C,IAAI2V,KAAK,CAACC,YAAY,EAAE;MACtBD,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,uBAAuB,EAAEC,IAAI,CAACC,SAAS,CAAC/V,IAAI,CAAC,CAAC;MACzE2V,KAAK,CAACC,YAAY,CAACI,aAAa,GAAG,MAAM;IAC3C;EACF;EAEAC,eAAeA,CAACN,KAGf;IACC,MAAMO,QAAQ,GAAGP,KAAK,CAACA,KAAK,CAACC,YAAY,EAAEO,OAAO,CAAC,uBAAuB,CAAC;IAC3E,IAAI,CAACD,QAAQ,EAAE;IAEf,IAAI;MACF,MAAMlW,IAAI,GAAG8V,IAAI,CAACM,KAAK,CAACF,QAAQ,CAAC;MAEjC,IAAI,CAAC,IAAI,CAAC5B,gBAAgB,CAACtU,IAAI,CAACkD,IAAI,CAAC,EAAE;QACrC,MAAMqR,KAAK,GAAG,IAAI,CAACE,YAAY,CAACzU,IAAI,CAACkD,IAAI,CAAC;QAC1C,IAAIqR,KAAK,KAAK,CAAC,EAAE;UACf,MAAM8B,YAAY,GAAG,IAAI,CAAC9X,eAAe,CAACwK,IAAI,CAC3C0D,IAAI,IAAKA,IAAI,CAACvJ,IAAI,KAAKlD,IAAI,CAACkD,IAAI,CAClC;UACD,IAAImT,YAAY,EAAE;YAChB,IAAI,CAACC,YAAY,CAACD,YAAY,CAACpT,EAAE,CAAC;UACpC;QACF,CAAC,MAAM;UACL,IAAI,CAACsT,gBAAgB,CACnB,oBAAoB,EACpB,oBAAoBhC,KAAK,IAAIvU,IAAI,CAACkD,IAAI,gBAAgB,IAAI,CAACxE,gBAAgB,UAAU,CACtF;UACD;QACF;MACF;MAEAqG,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE;QAC1DwR,QAAQ,EAAExW,IAAI,CAACkD,IAAI;QACnB6H,QAAQ,EAAE/K,IAAI,CAACsG,IAAI;QACnBmQ,YAAY,EAAEd,KAAK,CAACe,QAAQ;QAC5BC,iBAAiB,EAAE,IAAI,CAACpY,eAAe,CAACsH;OACzC,CAAC;MAEF,MAAM+Q,YAAY,GAAG,IAAI,CAACC,qBAAqB,CAAC7W,IAAI,CAACkD,IAAI,CAAC;MAC1D6B,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE4R,YAAY,CAAC;MAEzD,IAAIE,QAAQ,GAAG9W,IAAI,CAACsG,IAAI;MACxB,IAAItG,IAAI,CAACkD,IAAI,KAAK,QAAQ,EAAE;QAC1B,IAAI,IAAI,CAACxE,gBAAgB,KAAK,eAAe,EAAE;UAC7CoY,QAAQ,GAAG9W,IAAI,CAAC0H,IAAI,IAAI1H,IAAI,CAACsG,IAAI;QACnC,CAAC,MAAM;UACLwQ,QAAQ,GAAG9W,IAAI,CAACL,MAAM,IAAIK,IAAI,CAACsG,IAAI;QACrC;MACF;MAEA,MAAMyQ,SAAS,GAAG,IAAI,CAACC,cAAc,EAAE;MACvC,MAAMC,cAAc,GAAuB;QACzChU,EAAE,EAAE8T,SAAS;QACbzQ,IAAI,EAAEwQ,QAAQ;QACdvQ,IAAI,EAAEvG,IAAI,CAACkD,IAAI,KAAK,QAAQ,GAAGlD,IAAI,CAACuG,IAAI,GAAGuN,SAAS;QACpD5Q,IAAI,EAAElD,IAAI,CAACkD,IAAI;QACfwT,QAAQ,EAAEE,YAAY;QACtBlK,gBAAgB,EAAE1M;OACnB;MAED+E,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE;QACxC/B,EAAE,EAAEgU,cAAc,CAAChU,EAAE;QACrBC,IAAI,EAAE+T,cAAc,CAAC/T,IAAI;QACzBwT,QAAQ,EAAEO,cAAc,CAACP;OAC1B,CAAC;MAEF,MAAMQ,SAAS,GAAG,IAAI,CAAClW,aAAa,GAAG,EAAE,GAAG,EAAE;MAC9C,MAAMmW,aAAa,GAAe;QAChClU,EAAE,EAAEgU,cAAc,CAAChU,EAAE;QACrBC,IAAI,EAAE,aAAa;QACnBmL,IAAI,EAAE;UAAE,GAAG4I,cAAc;UAAEG,KAAK,EAAEF;QAAS,CAAE;QAC7CR,QAAQ,EAAEE;OACX;MAED7R,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;QACvC/B,EAAE,EAAEkU,aAAa,CAAClU,EAAE;QACpByT,QAAQ,EAAES,aAAa,CAACT;OACzB,CAAC;MAEF,IAAI,CAACnY,eAAe,GAAG,CAAC,GAAG,IAAI,CAACA,eAAe,EAAE0Y,cAAc,CAAC;MAChE,IAAI,CAAC5Y,WAAW,GAAG,CAAC,GAAG,IAAI,CAACA,WAAW,EAAE8Y,aAAa,CAAC;MAEvDpS,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE;QAChCqS,qBAAqB,EAAE,IAAI,CAAC9Y,eAAe,CAACsH,MAAM;QAClDyR,iBAAiB,EAAE,IAAI,CAACjZ,WAAW,CAACwH,MAAM;QAC1C0R,kBAAkB,EAChB,IAAI,CAAChZ,eAAe,CAAC,IAAI,CAACA,eAAe,CAACsH,MAAM,GAAG,CAAC,CAAC;QACvD2R,cAAc,EAAE,IAAI,CAACnZ,WAAW,CAAC,IAAI,CAACA,WAAW,CAACwH,MAAM,GAAG,CAAC;OAC7D,CAAC;MAEF;MACA,MAAM4R,cAAc,GAClB,IAAI,CAAClZ,eAAe,CAAC,IAAI,CAACA,eAAe,CAACsH,MAAM,GAAG,CAAC,CAAC;MACvD,MAAM6R,UAAU,GAAG,IAAI,CAACrZ,WAAW,CAAC,IAAI,CAACA,WAAW,CAACwH,MAAM,GAAG,CAAC,CAAC;MAChEd,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;QACvC2S,kBAAkB,EAAEF,cAAc,EAAEf,QAAQ;QAC5CkB,kBAAkB,EAAEF,UAAU,EAAEhB,QAAQ;QACxCmB,sBAAsB,EAAEH,UAAU,EAAErJ,IAAI,EAAEqI,QAAQ;QAClDoB,cAAc,EACZhC,IAAI,CAACC,SAAS,CAAC0B,cAAc,EAAEf,QAAQ,CAAC,KACxCZ,IAAI,CAACC,SAAS,CAAC2B,UAAU,EAAEhB,QAAQ;OACtC,CAAC;MAEF;MACA,IAAI,CAACzW,YAAY,GAAGgX,cAAc,CAAChU,EAAE;MACrC8B,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAAC/E,YAAY,CAAC;MAE3D;MACA,IAAI,CAAC8F,0BAA0B,CAAC,IAAI,CAACxH,eAAe,CAAC;MACrD,IAAI,CAACmW,kBAAkB,CAAC1U,IAAI,CAAC;MAC7B,IAAI,CAACyF,mBAAmB,EAAE;MAC1B,IAAI,CAAC3I,GAAG,CAAC6I,aAAa,EAAE;MAExBZ,OAAO,CAACC,GAAG,CACT,8CAA8C,EAC9CiS,cAAc,CAAChU,EAAE,CAClB;MAED;MACA2C,UAAU,CAAC,MAAK;QACdb,OAAO,CAACC,GAAG,CACT,6CAA6C,EAC7CiS,cAAc,CAAChU,EAAE,EACjB2T,YAAY,CACb;QACD,IAAI,CAACmB,mBAAmB,CAACd,cAAc,CAAChU,EAAE,EAAE2T,YAAY,CAAC;QAEzD;QACAhR,UAAU,CAAC,MAAK;UACdb,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;UAC3D,IAAI,CAACe,0BAA0B,CAAC,IAAI,CAACxH,eAAe,CAAC;UACrD,IAAI,IAAI,CAACb,oBAAoB,EAAE;YAC7B;YACA,IAAI,CAACA,oBAAoB,CAACoI,0BAA0B,EAAE;YACtDF,UAAU,CACR,MAAM,IAAI,CAAClI,oBAAoB,CAACoI,0BAA0B,EAAE,EAC5D,EAAE,CACH;YACDF,UAAU,CACR,MAAM,IAAI,CAAClI,oBAAoB,CAACoI,0BAA0B,EAAE,EAC5D,GAAG,CACJ;UACH;UACA,IAAI,CAAChJ,GAAG,CAAC6I,aAAa,EAAE;QAC1B,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,EAAE,GAAG,CAAC;MAEPZ,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MACxD,IAAI,CAACyI,mBAAmB,CAACzN,IAAI,CAACkD,IAAI,CAAC;IACrC,CAAC,CAAC,OAAO6E,KAAK,EAAE;MACdhD,OAAO,CAACgD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;IAC5C;EACF;EAEAiQ,cAAcA,CAACC,MAAc;IAC3B,IAAI,CAACxZ,cAAc,GAAGwZ,MAAM;IAC5B;EACF;EAEAC,mBAAmBA,CAACD,MAAc;IAChC,MAAME,YAAY,GAAG,IAAI,CAAC5Z,eAAe,CAACwK,IAAI,CAC3C0D,IAAI,IAAKA,IAAI,CAACxJ,EAAE,KAAKgV,MAAM,CAC7B;IACD,IAAIE,YAAY,EAAE;MAChB,MAAMpD,QAAQ,GAAG,IAAI,CAACqD,mBAAmB,CAACD,YAAY,CAAC;MACvD,IAAIpD,QAAQ,EAAE;QACZ,IAAI,CAAC9G,aAAa,CAAC8G,QAAQ,CAAC;MAC9B,CAAC,MAAM;QACL,IAAIsD,cAAc,GAAoB,IAAI;QAC1C,IAAI,IAAI,CAACvX,YAAY,CAACqX,YAAY,CAACjV,IAAI,GAAG,GAAG,CAAC,EAAE;UAC9CmV,cAAc,GACZ,IAAI,CAACvX,YAAY,CAACqX,YAAY,CAACjV,IAAI,GAAG,GAAG,CAAC,CAAC6F,IAAI,CAC5CqB,IAAS,IAAKA,IAAI,CAAC9D,IAAI,KAAK6R,YAAY,CAAC7R,IAAI,CAC/C,IAAI,IAAI;QACb;QAEA,IAAI+R,cAAc,EAAE;UAClB,IAAI,CAACpK,aAAa,CAACoK,cAAc,CAAC;QACpC,CAAC,MAAM;UACL,MAAMC,aAAa,GAAa;YAC9BrV,EAAE,EAAEkV,YAAY,CAAClV,EAAE;YACnBqD,IAAI,EAAE6R,YAAY,CAAC7R,IAAI;YACvBpD,IAAI,EAAEiV,YAAY,CAACjV,IAAI;YACvB2D,WAAW,EAAE,GAAGsR,YAAY,CAACjV,IAAI,eAAeiV,YAAY,CAAC7R,IAAI,EAAE;YACnEC,IAAI,EAAE4R,YAAY,CAAC5R,IAAI,IAAI,IAAI,CAAC+M,cAAc,CAAC6E,YAAY,CAACjV,IAAI;WACjE;UACD,IAAI,CAAC+K,aAAa,CAACqK,aAAa,CAAC;QACnC;MACF;IACF;EACF;EAEQF,mBAAmBA,CAAC3L,IAAwB;IAClD,MAAMkI,OAAO,GAAGlI,IAAI,CAACvJ,IAAI,KAAK,WAAW,GAAG,YAAY,GAAGuJ,IAAI,CAACvJ,IAAI,GAAG,GAAG;IAC1E,OACE,IAAI,CAACpC,YAAY,CAAC6T,OAAO,CAAC,EAAE5L,IAAI,CAC7BqB,IAAS,IAAKA,IAAI,CAAC9D,IAAI,KAAKmG,IAAI,CAACnG,IAAI,CACvC,IAAI,IAAI;EAEb;EAEAiS,WAAWA,CAAC5C,KAGX;IACC5Q,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;MAC3CiT,MAAM,EAAEtC,KAAK,CAACsC,MAAM;MACpBO,WAAW,EAAE7C,KAAK,CAACe,QAAQ;MAC3B+B,yBAAyB,EAAE,IAAI,CAACla,eAAe,CAACwK,IAAI,CACjD2P,CAAC,IAAKA,CAAC,CAACzV,EAAE,KAAK0S,KAAK,CAACsC,MAAM,CAC7B,EAAEvB,QAAQ;MACXiC,qBAAqB,EAAE,IAAI,CAACta,WAAW,CAAC0K,IAAI,CAAE2P,CAAC,IAAKA,CAAC,CAACzV,EAAE,KAAK0S,KAAK,CAACsC,MAAM,CAAC,EACtEvB;KACL,CAAC;IAEF,MAAMkC,mBAAmB,GAAG,IAAI,CAACra,eAAe,CAACsa,SAAS,CACvDpM,IAAI,IAAKA,IAAI,CAACxJ,EAAE,KAAK0S,KAAK,CAACsC,MAAM,CACnC;IACD,IAAIW,mBAAmB,KAAK,CAAC,CAAC,EAAE;MAC9B,IAAI,CAACra,eAAe,GAAG,CACrB,GAAG,IAAI,CAACA,eAAe,CAAC0W,KAAK,CAAC,CAAC,EAAE2D,mBAAmB,CAAC,EACrD;QACE,GAAG,IAAI,CAACra,eAAe,CAACqa,mBAAmB,CAAC;QAC5ClC,QAAQ,EAAEf,KAAK,CAACe;OACjB,EACD,GAAG,IAAI,CAACnY,eAAe,CAAC0W,KAAK,CAAC2D,mBAAmB,GAAG,CAAC,CAAC,CACvD;IACH;IAEA,MAAME,eAAe,GAAG,IAAI,CAACza,WAAW,CAACwa,SAAS,CAC/CpM,IAAI,IAAKA,IAAI,CAACxJ,EAAE,KAAK0S,KAAK,CAACsC,MAAM,CACnC;IACD,IAAIa,eAAe,KAAK,CAAC,CAAC,EAAE;MAC1B,IAAI,CAACza,WAAW,GAAG,CACjB,GAAG,IAAI,CAACA,WAAW,CAAC4W,KAAK,CAAC,CAAC,EAAE6D,eAAe,CAAC,EAC7C;QAAE,GAAG,IAAI,CAACza,WAAW,CAACya,eAAe,CAAC;QAAEpC,QAAQ,EAAEf,KAAK,CAACe;MAAQ,CAAE,EAClE,GAAG,IAAI,CAACrY,WAAW,CAAC4W,KAAK,CAAC6D,eAAe,GAAG,CAAC,CAAC,CAC/C;IACH;IAEA;IACA;IACA,IAAI,CAAC7Y,YAAY,GAAG0V,KAAK,CAACsC,MAAM;IAEhC;IACA,MAAMc,gBAAgB,GAAG,IAAI,CAACxa,eAAe,CAACwK,IAAI,CAC/C2P,CAAC,IAAKA,CAAC,CAACzV,EAAE,KAAK0S,KAAK,CAACsC,MAAM,CAC7B;IACD,IACEc,gBAAgB,KACfA,gBAAgB,CAACrC,QAAQ,CAACsC,CAAC,KAAKrD,KAAK,CAACe,QAAQ,CAACsC,CAAC,IAC/CD,gBAAgB,CAACrC,QAAQ,CAACuC,CAAC,KAAKtD,KAAK,CAACe,QAAQ,CAACuC,CAAC,CAAC,EACnD;MACAlU,OAAO,CAACqN,IAAI,CAAC,sDAAsD,EAAE;QACnE8G,QAAQ,EAAEvD,KAAK,CAACe,QAAQ;QACxByC,MAAM,EAAEJ,gBAAgB,CAACrC,QAAQ;QACjCuB,MAAM,EAAEtC,KAAK,CAACsC;OACf,CAAC;MACF;MACAc,gBAAgB,CAACrC,QAAQ,GAAG;QAAE,GAAGf,KAAK,CAACe;MAAQ,CAAE;IACnD;IAEA3R,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE;MACzCiT,MAAM,EAAEtC,KAAK,CAACsC,MAAM;MACpBO,WAAW,EAAE7C,KAAK,CAACe,QAAQ;MAC3B0C,qBAAqB,EAAE,IAAI,CAAC7a,eAAe,CAACwK,IAAI,CAC7C2P,CAAC,IAAKA,CAAC,CAACzV,EAAE,KAAK0S,KAAK,CAACsC,MAAM,CAC7B,EAAEvB,QAAQ;MACX2C,iBAAiB,EAAE,IAAI,CAAChb,WAAW,CAAC0K,IAAI,CAAE2P,CAAC,IAAKA,CAAC,CAACzV,EAAE,KAAK0S,KAAK,CAACsC,MAAM,CAAC,EAClEvB,QAAQ;MACZzW,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/B6X,cAAc,EACZhC,IAAI,CAACC,SAAS,CACZ,IAAI,CAACxX,eAAe,CAACwK,IAAI,CAAE2P,CAAC,IAAKA,CAAC,CAACzV,EAAE,KAAK0S,KAAK,CAACsC,MAAM,CAAC,EAAEvB,QAAQ,CAClE,KAAKZ,IAAI,CAACC,SAAS,CAACJ,KAAK,CAACe,QAAQ;KACtC,CAAC;IAEF;IACA3R,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;IACzDY,UAAU,CAAC,MAAK;MACdb,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MAErD;MACAD,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;MAEpE;MACAD,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3C,IAAI,CAACe,0BAA0B,CAAC,IAAI,CAACxH,eAAe,CAAC;MAErD,IAAI,IAAI,CAACb,oBAAoB,EAAE;QAC7BqH,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;QACpD,IAAI,CAACtH,oBAAoB,CAACoI,0BAA0B,EAAE;QAEtD;QACAf,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QACpE,IAAI,CAACtH,oBAAoB,CAAC4b,eAAe,EAAE;QAE3C;QACA,IAAI,CAACxc,GAAG,CAAC6I,aAAa,EAAE;QACxBZ,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;MAC/C,CAAC,MAAM;QACLD,OAAO,CAACqN,IAAI,CACV,+DAA+D,CAChE;MACH;IACF,CAAC,EAAE,CAAC,CAAC;IAEL,IAAI,CAACtV,GAAG,CAAC6I,aAAa,EAAE;EAC1B;EAEA2Q,YAAYA,CAAC2B,MAAc;IACzB,MAAMsB,YAAY,GAAG,IAAI,CAAChb,eAAe,CAACwK,IAAI,CAC3C0D,IAAI,IAAKA,IAAI,CAACxJ,EAAE,KAAKgV,MAAM,CAC7B;IACD,IAAIsB,YAAY,EAAE;MAChB;MACA,IAAIA,YAAY,CAACrW,IAAI,KAAK,WAAW,EAAE;QACrC,OAAO,IAAI,CAACc,qBAAqB,CAACiU,MAAM,CAAC;QACzClT,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEiT,MAAM,CAAC;MACnE;MAEA,IAAI,CAACrD,iBAAiB,CAAC2E,YAAY,CAAC;IACtC;IAEA,IAAI,CAAChb,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC0J,MAAM,CAC/CwE,IAAI,IAAKA,IAAI,CAACxJ,EAAE,KAAKgV,MAAM,CAC7B;IACD,IAAI,CAAC5Z,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC4J,MAAM,CAAEwE,IAAI,IAAKA,IAAI,CAACxJ,EAAE,KAAKgV,MAAM,CAAC;IACxE,IAAI,CAAC3Z,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC2J,MAAM,CACvCuR,IAAI,IAAKA,IAAI,CAACC,MAAM,KAAKxB,MAAM,IAAIuB,IAAI,CAACE,MAAM,KAAKzB,MAAM,CAC3D;IAED,IAAI,CAAC0B,qCAAqC,EAAE;IAC5C,IAAI,CAAC5T,0BAA0B,CAAC,IAAI,CAACxH,eAAe,CAAC;IAErDqH,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAAClI,oBAAoB,EAAE;QAC7B,IAAI,CAACA,oBAAoB,CAACoI,0BAA0B,EAAE;MACxD;MACA,IAAI,CAACL,mBAAmB,EAAE;MAC1B,IAAI,CAAC3I,GAAG,CAAC6I,aAAa,EAAE;IAC1B,CAAC,EAAE,GAAG,CAAC;EACT;EAEQgU,qCAAqCA,CAAA;IAC3C;IACA;IACA,IAAI,CAACtb,WAAW,GAAG,IAAI,CAACE,eAAe,CAAC2I,GAAG,CAAEuF,IAAI,KAAM;MACrDxJ,EAAE,EAAEwJ,IAAI,CAACxJ,EAAE;MACXC,IAAI,EAAE,aAAa;MACnBmL,IAAI,EAAE;QAAE,GAAG5B,IAAI;QAAE2K,KAAK,EAAE,IAAI,CAACpW,aAAa,GAAG,EAAE,GAAG;MAAE,CAAE;MACtD0V,QAAQ,EAAEjK,IAAI,CAACiK;KAChB,CAAC,CAAC;IAEH;IACA,IAAI,IAAI,CAACnY,eAAe,CAACsH,MAAM,GAAG,CAAC,EAAE;MACnC,IAAI,CAAC5F,YAAY,GACf,IAAI,CAAC1B,eAAe,CAAC,IAAI,CAACA,eAAe,CAACsH,MAAM,GAAG,CAAC,CAAC,CAAC5C,EAAE;IAC5D,CAAC,MAAM;MACL,IAAI,CAAChD,YAAY,GAAG,IAAI;IAC1B;EACF;EAEA2Z,mBAAmBA,CAACJ,IAAgB;IAClC,MAAMK,OAAO,GAAe;MAC1B5W,EAAE,EACAuW,IAAI,CAACvW,EAAE,IACP,QAAQuW,IAAI,CAACC,MAAM,IAAID,IAAI,CAACE,MAAM,IAAItS,IAAI,CAAC0S,KAAK,CAAC1S,IAAI,CAACC,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE;MAC1EoS,MAAM,EAAED,IAAI,CAACC,MAAM;MACnBC,MAAM,EAAEF,IAAI,CAACE,MAAM;MACnBK,QAAQ,EAAEP,IAAI,CAACO,QAAQ,IAAI;KAC5B;IACD,IAAI,CAACzb,WAAW,GAAG,CAAC,GAAG,IAAI,CAACA,WAAW,EAAEub,OAAO,CAAC;EACnD;EAEAG,iBAAiBA,CAACC,MAIjB;IACC;EAAA;EAGF;EAEAC,MAAMA,CAAA;IACJ;EAAA;EAEFC,MAAMA,CAAA;IACJ;EAAA;EAEFC,OAAOA,CAAA;IACL,IAAI,CAAC7b,eAAe,GAAG,EAAE;IACzB,IAAI,CAACF,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB,IAAI,CAACkB,mBAAmB,GAAG,IAAI;EACjC;EACA6a,KAAKA,CAAA;IACH,IAAI,CAACC,SAAS,EAAE;EAClB;EAEA;EACAC,qBAAqBA,CAAC5E,KAIrB;IACC5Q,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE;MACtCwV,IAAI,EAAE7E,KAAK,CAAC6E,IAAI;MAChB9M,QAAQ,EAAEiI,KAAK,CAAClJ,IAAI,CAACvJ,IAAI;MACzBwT,QAAQ,EAAEf,KAAK,CAACe;KACjB,CAAC;IAEF;IACA,IACE,CAACf,KAAK,CAAC6E,IAAI,KAAK,QAAQ,IAAI7E,KAAK,CAAC6E,IAAI,KAAK,OAAO,KAClD,IAAI,CAACjc,eAAe,CAACsH,MAAM,GAAG,CAAC,EAC/B;MACA,MAAM4U,iBAAiB,GAAG,IAAI,CAAClc,eAAe,CAACsa,SAAS,CACrDpM,IAAI,IAAKA,IAAI,CAACvJ,IAAI,KAAKyS,KAAK,CAAC6E,IAAI,CACnC;MAED,IAAIC,iBAAiB,KAAK,CAAC,CAAC,EAAE;QAC5B1V,OAAO,CAACC,GAAG,CAAC,yBAAyB2Q,KAAK,CAAC6E,IAAI,OAAO,CAAC;QACvD;QACA,IAAI,CAACjc,eAAe,CAACmc,MAAM,CAACD,iBAAiB,EAAE,CAAC,CAAC;QACjD,IAAI,CAACpc,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC4J,MAAM,CACvCwE,IAAI,IAAKA,IAAI,CAAC4B,IAAI,EAAEnL,IAAI,KAAKyS,KAAK,CAAC6E,IAAI,CACzC;MACH;IACF;IAEA;IACA,MAAMxa,IAAI,GAAG2V,KAAK,CAAClJ,IAAI;IAEvB;IACA,MAAMwK,cAAc,GAAuB;MACzChU,EAAE,EAAEsB,MAAM,CAACC,UAAU,EAAE;MACvBtB,IAAI,EAAElD,IAAI,CAACkD,IAAI;MACfoD,IAAI,EAAEtG,IAAI,CAACsG,IAAI;MACfC,IAAI,EAAEvG,IAAI,CAACuG,IAAI;MACfmQ,QAAQ,EAAE;QAAEsC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAC,CAAE;MAAE;MAC1BvM,gBAAgB,EAAE1M,IAAI,CAAE;KACzB;IAED;IACA,IAAI,CAACzB,eAAe,CAACwV,IAAI,CAACkD,cAAc,CAAC;IAEzC;IACA,MAAM0D,UAAU,GAAe;MAC7B1X,EAAE,EAAEgU,cAAc,CAAChU,EAAE;MACrBC,IAAI,EAAE,aAAa;MACnBmL,IAAI,EAAE;QAAE,GAAG4I,cAAc;QAAEG,KAAK,EAAE;MAAE,CAAE;MACtCV,QAAQ,EAAEO,cAAc,CAACP;KAC1B;IAED;IACA,IAAI,CAACrY,WAAW,CAAC0V,IAAI,CAAC4G,UAAU,CAAC;IAEjC;IACA,IAAI3a,IAAI,CAACkD,IAAI,KAAK,WAAW,EAAE;MAC7B,IAAI,CAACc,qBAAqB,CAACiT,cAAc,CAAChU,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;MACtD8B,OAAO,CAACC,GAAG,CACT,yCAAyC,EACzCiS,cAAc,CAAChU,EAAE,EACjB,SAAS,CACV;IACH;IAEA;IACA,IAAI,CAACyR,kBAAkB,CAAC1U,IAAI,CAAC;IAC7B,IAAI,CAACyF,mBAAmB,EAAE;IAE1B;IACA,IAAI,CAAC3I,GAAG,CAAC6I,aAAa,EAAE;IAExBZ,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE;MACxCiT,MAAM,EAAEhB,cAAc,CAAChU,EAAE;MACzBuX,IAAI,EAAE7E,KAAK,CAAC6E,IAAI;MAChBI,UAAU,EAAE,IAAI,CAACrc,eAAe,CAACsH;KAClC,CAAC;EACJ;EAEAgV,qBAAqBA,CAAC5C,MAAc;IAClC,IAAI,CAAC3B,YAAY,CAAC2B,MAAM,CAAC;EAC3B;EAEA;EACA6C,kBAAkBA,CAACnF,KAA2C;IAC5D5Q,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE2Q,KAAK,CAAC;IAC1D5Q,OAAO,CAACC,GAAG,CACT,0CAA0C,EAC1C,IAAI,CAAChB,qBAAqB,CAC3B;IAED,IAAI,CAACA,qBAAqB,CAAC2R,KAAK,CAACsC,MAAM,CAAC,GAAGtC,KAAK,CAACoF,OAAO;IAExDhW,OAAO,CAACC,GAAG,CACT,yCAAyC,EACzC,IAAI,CAAChB,qBAAqB,CAC3B;IACDe,OAAO,CAACC,GAAG,CACT,+BAA+B,EAC/B2Q,KAAK,CAACsC,MAAM,EACZtC,KAAK,CAACoF,OAAO,GAAG,SAAS,GAAG,UAAU,CACvC;EACH;EAEA;EACQC,qBAAqBA,CAAA;IAY3B,MAAMC,OAAO,GAAG,IAAI,CAAC/d,YAAY,CAACge,SAAS,CAAC,UAAU,CAAC;IACvD,IAAIC,gBAAgB,GAAG,EAAE;IACzB,IAAIC,OAAO,GAAG,CAAC,CAAC,CAAC;IACjB,IAAIC,KAAK,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,MAAM;IACtC,IAAIvZ,GAAG,EAAEC,MAAM,EAAEC,OAAO,EAAEC,IAAI;IAE9B,IAAI6Y,OAAO,EAAE;MACX,MAAMQ,KAAK,GAAGR,OAAO,CAACS,KAAK,CAAC,IAAI,CAAC;MACjC,MAAMC,WAAW,GAAGF,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE;MAClC,MAAMG,aAAa,GAAGH,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE;MACpCN,gBAAgB,GAAGQ,WAAW;MAE9B,MAAME,KAAK,GAAGF,WAAW,CAACD,KAAK,CAAC,GAAG,CAAC;MACpC,IAAIG,KAAK,CAAChW,MAAM,IAAI,CAAC,EAAE;QACrB,CAAC5D,GAAG,EAAEC,MAAM,EAAEC,OAAO,EAAEC,IAAI,CAAC,GAAGyZ,KAAK;MACtC;MAEA,MAAMC,GAAG,GAAGF,aAAa,CAACF,KAAK,CAAC,GAAG,CAAC,CAACxU,GAAG,CAACgL,MAAM,CAAC;MAChD,IAAI4J,GAAG,CAACjW,MAAM,IAAI,CAAC,EAAE;QACnB,CAACwV,KAAK,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,CAAC,GAAGM,GAAG;QAC1CV,OAAO,GAAGI,MAAM,IAAIM,GAAG,CAACA,GAAG,CAACjW,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;MAChD,CAAC,MAAM,IAAIiW,GAAG,CAACjW,MAAM,GAAG,CAAC,IAAIiW,GAAG,CAAC,CAAC,CAAC,EAAE;QACnCV,OAAO,GAAGU,GAAG,CAAC,CAAC,CAAC;MAClB;IACF;IAEA,OAAO;MACLb,OAAO,EAAEE,gBAAgB;MACzBC,OAAO;MACPC,KAAK;MACLC,QAAQ;MACRC,SAAS;MACTC,MAAM;MACNvZ,GAAG;MACHC,MAAM;MACNC,OAAO;MACPC;KACD;EACH;EAEQ2Z,gBAAgBA,CAAA;IACtB,OAAO,IAAI,CAAC7e,YAAY,CAAC8e,aAAa,EAAE,IAAI,uBAAuB;EACrE;EACQC,4BAA4BA,CAAA;IAClC,MAAMC,cAAc,GAAU,EAAE;IAChC,MAAMlO,QAAQ,GAAG,IAAI,CAACzP,eAAe,CAACiO,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACvJ,IAAI,KAAK,OAAO,CAAC;IAC3E,MAAMiZ,YAAY,GAAG,IAAI,CAAC5d,eAAe,CAACiO,IAAI,CAC3CC,IAAI,IAAKA,IAAI,CAACvJ,IAAI,KAAK,WAAW,CACpC;IAEDgZ,cAAc,CAACnI,IAAI,CAAC;MAClBqI,YAAY,EAAE,OAAO;MACrBnT,UAAU,EAAE,CAAC;MACboT,OAAO,EAAE,CACP;QACEC,iBAAiB,EAAE,OAAO;QAC1BC,kBAAkB,EAAEvO,QAAQ,GAAG,IAAI,CAACwO,mBAAmB,EAAE,GAAG;OAC7D,EACD;QAAEF,iBAAiB,EAAE,WAAW;QAAEC,kBAAkB,EAAE;MAAE,CAAE,EAC1D;QAAED,iBAAiB,EAAE,aAAa;QAAEC,kBAAkB,EAAE;MAAE,CAAE,EAC5D;QAAED,iBAAiB,EAAE,OAAO;QAAEC,kBAAkB,EAAE;MAAE,CAAE,EACtD;QAAED,iBAAiB,EAAE,mBAAmB;QAAEC,kBAAkB,EAAE;MAAE,CAAE,EAClE;QAAED,iBAAiB,EAAE,eAAe;QAAEC,kBAAkB,EAAE;MAAE,CAAE,EAC9D;QAAED,iBAAiB,EAAE,gBAAgB;QAAEC,kBAAkB,EAAE;MAAE,CAAE;KAElE,CAAC;IAEFL,cAAc,CAACnI,IAAI,CAAC;MAClBqI,YAAY,EAAE,2BAA2B;MACzCnT,UAAU,EAAE,CAAC;MACboT,OAAO,EAAE,CACP;QACEC,iBAAiB,EAAE,KAAK;QACxBC,kBAAkB,EAAEJ,YAAY,GAAG,MAAM,GAAG;OAC7C,EACD;QAAEG,iBAAiB,EAAE,UAAU;QAAEC,kBAAkB,EAAE;MAAE,CAAE,EACzD;QACED,iBAAiB,EAAE,wBAAwB;QAC3CC,kBAAkB,EAAEJ,YAAY,GAC5B,IAAI,CAACM,2BAA2B,EAAE,GAClC;OACL,EACD;QACEH,iBAAiB,EAAE,oCAAoC;QACvDC,kBAAkB,EAAE;OACrB,EACD;QAAED,iBAAiB,EAAE,mBAAmB;QAAEC,kBAAkB,EAAE;MAAE,CAAE;KAErE,CAAC;IAEF,MAAMG,mBAAmB,GAAG,IAAI,CAAC5b,YAAY,CAAC,YAAY,CAAC,IAAI,EAAE;IACjE,IAAI4b,mBAAmB,CAAC7W,MAAM,GAAG,CAAC,EAAE;MAClCqW,cAAc,CAACnI,IAAI,CAAC;QAClBqI,YAAY,EAAE,OAAO;QACrBnT,UAAU,EAAE,CAAC;QACboT,OAAO,EAAE,IAAI,CAACM,mBAAmB;OAClC,CAAC;IACJ;IACA,OAAOT,cAAc;EACvB;EAEQU,eAAeA,CAAC3E,MAAc;IACpC,MAAM4E,KAAK,GAAG5E,MAAM,CAAC4E,KAAK,CAAC,cAAc,CAAC;IAC1C,OAAOA,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAG5E,MAAM;EAClC;EAEQuE,mBAAmBA,CAAA;IACzB,MAAMM,SAAS,GAAG,IAAI,CAACve,eAAe,CAACwK,IAAI,CACxC0D,IAAI,IAAKA,IAAI,CAACvJ,IAAI,KAAK,OAAO,CAChC;IACD,IAAI4Z,SAAS,EAAE;MACb,IAAIA,SAAS,CAACpQ,gBAAgB,IAAIoQ,SAAS,CAACpQ,gBAAgB,CAACzJ,EAAE,EAAE;QAC/D,OAAO6Z,SAAS,CAACpQ,gBAAgB,CAACzJ,EAAE,CAACkE,QAAQ,EAAE;MACjD;MACA,MAAMqI,SAAS,GAAG,IAAI,CAAC1O,YAAY,CAAC,QAAQ,CAAC,CAACiI,IAAI,CAC/CnJ,KAAK,IAAKA,KAAK,CAAC0G,IAAI,KAAKwW,SAAS,CAACxW,IAAI,CACzC;MACD,IAAIkJ,SAAS,EAAE,OAAOA,SAAS,CAACvM,EAAE,CAACkE,QAAQ,EAAE;MAE7C,MAAM4V,aAAa,GAAG,IAAI,CAACjc,YAAY,CAAC,QAAQ,CAAC,CAACiI,IAAI,CACnDnJ,KAAK,IACJA,KAAK,CAACqD,EAAE,KAAK6Z,SAAS,CAACxW,IAAI,IAAI1G,KAAK,CAACqD,EAAE,CAACkE,QAAQ,EAAE,KAAK2V,SAAS,CAACxW,IAAI,CACxE;MACD,IAAIyW,aAAa,EAAE,OAAOA,aAAa,CAAC9Z,EAAE,CAACkE,QAAQ,EAAE;IACvD;IACA,OAAO,IAAI;EACb;EAEQsV,2BAA2BA,CAAA;IACjC,MAAMO,cAAc,GAAG,IAAI,CAACze,eAAe,CAAC0J,MAAM,CAC/CwE,IAAI,IAAKA,IAAI,CAACvJ,IAAI,KAAK,WAAW,CACpC;IACD,IAAI8Z,cAAc,CAACnX,MAAM,GAAG,CAAC,EAAE;MAC7B,MAAMoX,YAAY,GAAGD,cAAc,CAChC9V,GAAG,CAAEuF,IAAI,IAAI;QACZ,IAAIA,IAAI,CAACC,gBAAgB,IAAID,IAAI,CAACC,gBAAgB,CAACzJ,EAAE,EAAE;UACrD,OAAOwJ,IAAI,CAACC,gBAAgB,CAACzJ,EAAE,CAACkE,QAAQ,EAAE;QAC5C;QACA,MAAM+V,aAAa,GAAG,IAAI,CAACpc,YAAY,CAAC,WAAW,CAAC,CAACiI,IAAI,CACtDa,EAAE,IAAKA,EAAE,CAACtD,IAAI,KAAKmG,IAAI,CAACnG,IAAI,CAC9B;QACD,OAAO4W,aAAa,GAAGA,aAAa,CAACja,EAAE,GAAG,IAAI;MAChD,CAAC,CAAC,CACDgF,MAAM,CAAEhF,EAAE,IAAKA,EAAE,KAAK,IAAI,CAAC;MAC9B,OAAOga,YAAY,CAACE,IAAI,CAAC,GAAG,CAAC;IAC/B;IACA,OAAO,EAAE;EACX;EAEQC,kBAAkBA,CAACnF,MAAc;IACvC,OAAOA,MAAM,CAAC4E,KAAK,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,IAAI5E,MAAM;EACvD;EAEQ0E,mBAAmBA,CAAA;IACzB,MAAMU,cAAc,GAAG,IAAI,CAAC9e,eAAe,CAAC0J,MAAM,CAC/CwE,IAAI,IAAKA,IAAI,CAACvJ,IAAI,KAAK,WAAW,CACpC;IACD,MAAMmZ,OAAO,GAAU,EAAE;IAEzB;IACA,MAAMiB,oBAAoB,GAAGD,cAAc,CAAC7Q,IAAI,CAC7CC,IAAI,IAAK,IAAI,CAACzI,qBAAqB,CAACyI,IAAI,CAACxJ,EAAE,CAAC,KAAK,KAAK,CACxD;IAEDoZ,OAAO,CAACtI,IAAI,CAAC;MACXuI,iBAAiB,EAAE,mBAAmB;MACtCC,kBAAkB,EAAEe,oBAAoB,GAAG,MAAM,GAAG;KACrD,CAAC;IAEF,MAAMZ,mBAAmB,GAAG,IAAI,CAAC5b,YAAY,CAAC,YAAY,CAAC,IAAI,EAAE;IACjE,MAAMyc,qBAAqB,GAAG,IAAIC,GAAG,EAAU;IAE/C;IACAH,cAAc,CAACzJ,OAAO,CAAEnH,IAAI,IAAI;MAC9B,MAAMgR,SAAS,GAAG,IAAI,CAACzZ,qBAAqB,CAACyI,IAAI,CAACxJ,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC;MAEjE8B,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;QAC5CiT,MAAM,EAAExL,IAAI,CAACxJ,EAAE;QACf6T,QAAQ,EAAErK,IAAI,CAACnG,IAAI;QACnBoX,WAAW,EAAE,IAAI,CAAC1Z,qBAAqB,CAACyI,IAAI,CAACxJ,EAAE,CAAC;QAChDwa,SAAS,EAAEA;OACZ,CAAC;MAEF,IAAIA,SAAS,EAAE;QACb,MAAME,WAAW,GAAG,IAAI,CAACP,kBAAkB,CAAC3Q,IAAI,CAACxJ,EAAE,CAAC;QACpD,MAAM2a,aAAa,GAAGlB,mBAAmB,CAAC3T,IAAI,CAC3C8U,EAAE,IACDA,EAAE,CAAC5a,EAAE,KAAK0a,WAAW,IAAIE,EAAE,CAAC5a,EAAE,KAAKwJ,IAAI,CAACxJ,EAAE,IAAI4a,EAAE,CAACvX,IAAI,KAAKmG,IAAI,CAACnG,IAAI,CACtE;QAED,MAAMwX,UAAU,GAAGF,aAAa,GAC5B,aAAaA,aAAa,CAACtX,IAAI,CAC5B6N,WAAW,EAAE,CACblC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CACpBA,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,EAAE,GAC/B,aAAaxF,IAAI,CAACnG,IAAI,CACnB6N,WAAW,EAAE,CACblC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CACpBA,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,EAAE;QACnCsL,qBAAqB,CAACQ,GAAG,CAACD,UAAU,CAAC;QAErC/Y,OAAO,CAACC,GAAG,CACT,4CAA4C,EAC5C8Y,UAAU,EACV,WAAW,EACXrR,IAAI,CAACnG,IAAI,EACT,gBAAgB,EAChBsX,aAAa,CACd;MACH,CAAC,MAAM;QACL7Y,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAEyH,IAAI,CAACnG,IAAI,CAAC;MACzE;IACF,CAAC,CAAC;IAEF;IACAiX,qBAAqB,CAAC3J,OAAO,CAAEkK,UAAU,IAAI;MAC3CzB,OAAO,CAACtI,IAAI,CAAC;QACXuI,iBAAiB,EAAEwB,UAAU;QAC7BvB,kBAAkB,EAAE;OACrB,CAAC;MACFxX,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE8Y,UAAU,EAAE,QAAQ,CAAC;IAC1E,CAAC,CAAC;IAEF;IACApB,mBAAmB,CAAC9I,OAAO,CAAE9T,SAAS,IAAI;MACxC,IAAIA,SAAS,CAACwG,IAAI,KAAK,mBAAmB,EAAE;MAC5C,MAAMwX,UAAU,GAAG,aAAahe,SAAS,CAACwG,IAAI,CAC3C6N,WAAW,EAAE,CACblC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CACpBA,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,EAAE;MAE/B;MACA,MAAM+L,YAAY,GAAG3B,OAAO,CAAC7P,IAAI,CAC9ByR,MAAM,IAAKA,MAAM,CAAC3B,iBAAiB,KAAKwB,UAAU,CACpD;MACD,IAAI,CAACE,YAAY,EAAE;QACjB3B,OAAO,CAACtI,IAAI,CAAC;UACXuI,iBAAiB,EAAEwB,UAAU;UAC7BvB,kBAAkB,EAAE;SACrB,CAAC;QACFxX,OAAO,CAACC,GAAG,CACT,sCAAsC,EACtC8Y,UAAU,EACV,SAAS,CACV;MACH,CAAC,MAAM;QACL/Y,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE8Y,UAAU,CAAC;MACxE;IACF,CAAC,CAAC;IAEF/Y,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEqX,OAAO,CAAC;IAEpD;IACAA,OAAO,CAACzI,OAAO,CAAEqK,MAAM,IAAI;MACzB,IAAIA,MAAM,CAAC3B,iBAAiB,CAACvK,UAAU,CAAC,YAAY,CAAC,EAAE;QACrDhN,OAAO,CAACC,GAAG,CACT,oBAAoB,EACpBiZ,MAAM,CAAC3B,iBAAiB,EACxB,GAAG,EACH2B,MAAM,CAAC1B,kBAAkB,CAC1B;MACH;IACF,CAAC,CAAC;IAEF,OAAOF,OAAO;EAChB;EAEQ6B,2BAA2BA,CAAA;IAKjC,MAAMC,MAAM,GAAa,EAAE;IAC3B,MAAMC,QAAQ,GAAa,EAAE;IAE7B,IAAI,CAAC,IAAI,CAACxc,SAAS,IAAI,IAAI,CAACA,SAAS,CAACsG,IAAI,EAAE,KAAK,EAAE,EACjDiW,MAAM,CAACpK,IAAI,CAAC,wBAAwB,CAAC;IACvC,IAAI,CAAC,IAAI,CAAClS,WAAW,IAAI,IAAI,CAACA,WAAW,CAACqG,IAAI,EAAE,KAAK,EAAE,EACrDiW,MAAM,CAACpK,IAAI,CAAC,4BAA4B,CAAC;IAE3C,MAAMsK,UAAU,GAAG,IAAI,CAAC9f,eAAe,CAACwK,IAAI,CACzC0D,IAAI,IAAKA,IAAI,CAACvJ,IAAI,KAAK,QAAQ,CACjC;IACD,IAAI,CAACmb,UAAU,EAAEF,MAAM,CAACpK,IAAI,CAAC,8BAA8B,CAAC;IAE5D,MAAM+I,SAAS,GAAG,IAAI,CAACve,eAAe,CAACwK,IAAI,CACxC0D,IAAI,IAAKA,IAAI,CAACvJ,IAAI,KAAK,OAAO,CAChC;IACD,IAAI,CAAC4Z,SAAS,EACZsB,QAAQ,CAACrK,IAAI,CAAC,uDAAuD,CAAC;IAExE,MAAMuK,aAAa,GAAG,IAAI,CAAC/f,eAAe,CAACwK,IAAI,CAC5C0D,IAAI,IAAKA,IAAI,CAACvJ,IAAI,KAAK,WAAW,CACpC;IACD,IAAI,CAACob,aAAa,EAChBF,QAAQ,CAACrK,IAAI,CACX,gEAAgE,CACjE;IAEH,MAAMwK,aAAa,GAAG,IAAI,CAAChgB,eAAe,CAACwK,IAAI,CAC5C0D,IAAI,IAAKA,IAAI,CAACvJ,IAAI,KAAK,WAAW,CACpC;IACD,IAAI,CAACqb,aAAa,EAChBH,QAAQ,CAACrK,IAAI,CACX,6DAA6D,CAC9D;IAEH,OAAO;MAAEuB,OAAO,EAAE6I,MAAM,CAACtY,MAAM,KAAK,CAAC;MAAEsY,MAAM;MAAEC;IAAQ,CAAE;EAC3D;EAEQI,2BAA2BA,CAAA;IACjC,MAAM;MAAEvD,OAAO;MAAEG;IAAO,CAAE,GAAG,IAAI,CAACJ,qBAAqB,EAAE;IACzD,MAAMqD,UAAU,GAAG,IAAI,CAAC9f,eAAe,CAACwK,IAAI,CACzC0D,IAAI,IAAKA,IAAI,CAACvJ,IAAI,KAAK,QAAQ,CACjC;IACD,IAAIub,aAAa,GAAG,EAAE;IACtB,IAAIzP,cAAc,GAAG,EAAE;IAEvB,IAAIqP,UAAU,EAAE;MACd,MAAMK,QAAQ,GAAG,IAAI,CAAC9B,eAAe,CAACyB,UAAU,CAACpb,EAAE,CAAC;MACpD,MAAM0b,UAAU,GAAG,IAAI,CAAC7d,YAAY,CAAC,SAAS,CAAC,CAACiI,IAAI,CACjDpJ,MAAM,IACLA,MAAM,CAACsD,EAAE,KAAKyb,QAAQ,IACtB/e,MAAM,CAACsD,EAAE,KAAKob,UAAU,CAACpb,EAAE,IAC3BtD,MAAM,CAAC2G,IAAI,KAAK+X,UAAU,CAAC/X,IAAI,CAClC;MAED,IAAIqY,UAAU,EAAE;QACdF,aAAa,GAAGE,UAAU,CAAC9X,WAAW,IAAI8X,UAAU,CAACrY,IAAI,IAAI,EAAE;QAC/D0I,cAAc,GACX2P,UAAkB,CAAC7P,QAAQ,IAC3B6P,UAAkB,CAAC5P,OAAO,IAC1B4P,UAAkB,CAAC3P,cAAc,IAClC2P,UAAU,CAACrY,IAAI,IACf,EAAE;MACN,CAAC,MAAM;QACLmY,aAAa,GAAGJ,UAAU,CAAC/X,IAAI;QAC/B0I,cAAc,GAAGqP,UAAU,CAAC/X,IAAI;MAClC;IACF;IAEA,MAAMsY,WAAW,GAAG,IAAI,CAAChd,SAAS,CAC/BsG,IAAI,EAAE,CACN+J,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CACpBkC,WAAW,EAAE;IAChB,MAAM0K,mBAAmB,GAAG,IAAI,CAAChd,WAAW,GACxC,IAAI,CAACA,WAAW,CAACqG,IAAI,EAAE,GACvBuW,aAAa;IACjB,MAAMK,WAAW,GACf7D,OAAO,IAAI,kDAAkD;IAC/D,MAAM8D,YAAY,GAAG,GAAGH,WAAW,IAAIE,WAAW,EAAE;IAEpD,OAAO;MACL1D,OAAO;MACPzP,IAAI,EAAEiT,WAAW;MACjBtY,IAAI,EAAE,IAAI,CAAC1E,SAAS,CAACsG,IAAI,EAAE;MAC3B8W,cAAc,EAAEH,mBAAmB;MACnC7P,cAAc;MACd9L,IAAI,EAAE,IAAI;MACV+b,gBAAgB,EAAE,IAAI;MACtBC,QAAQ,EAAE,IAAI;MACdC,cAAc,EAAE,IAAI;MACpBC,mBAAmB,EAAE,IAAI;MACzBC,aAAa,EAAE,IAAI,CAACpD,4BAA4B,EAAE;MAClDqD,aAAa,EAAE,IAAI,CAACvD,gBAAgB,EAAE;MACtCZ,gBAAgB,EAAE4D;KACnB;EACH;EAEcQ,yBAAyBA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACrC,MAAMpB,UAAU,GAAGmB,KAAI,CAACjhB,eAAe,CAACwK,IAAI,CACzC0D,IAAI,IAAKA,IAAI,CAACvJ,IAAI,KAAK,QAAQ,CACjC;MACD,IAAI,CAACmb,UAAU,EAAE;MAEjB,IAAIM,UAAU,GAAGa,KAAI,CAAC1e,YAAY,CAAC,SAAS,CAAC,EAAEiI,IAAI,CAChD2W,CAAC,IAAKA,CAAC,CAACpZ,IAAI,KAAK+X,UAAU,CAAC/X,IAAI,CAClC;MACD,IAAI,CAACqY,UAAU,IAAIN,UAAU,CAAC3R,gBAAgB,EAAE;QAC9CiS,UAAU,GAAGN,UAAU,CAAC3R,gBAAgB;MAC1C;MAEA,IAAI,CAACiS,UAAU,EAAE;QACf,MAAMgB,WAAW,GAAGH,KAAI,CAAC5C,eAAe,CAACyB,UAAU,CAACpb,EAAE,CAAC;QACvD,IAAI0c,WAAW,IAAIA,WAAW,KAAKtB,UAAU,CAACpb,EAAE,EAAE;UAChD,IAAI;YACF,MAAM2c,aAAa,SAAS,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;cAC1DP,KAAI,CAACviB,cAAc,CAAC2R,aAAa,CAAC+Q,WAAW,CAAC,CAACjb,SAAS,CAAC;gBACvDwB,IAAI,EAAGmI,IAAI,IAAKyR,OAAO,CAACzR,IAAI,CAAC;gBAC7BtG,KAAK,EAAGA,KAAK,IAAI;kBACfhD,OAAO,CAACgD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;kBACvDgY,MAAM,CAAChY,KAAK,CAAC;gBACf;eACD,CAAC;YACJ,CAAC,CAAC;YAEF,IAAI6X,aAAa,EAAE;cACjB,IAAI,CAACJ,KAAI,CAAC1e,YAAY,CAAC,SAAS,CAAC,EAC/B0e,KAAI,CAAC1e,YAAY,CAAC,SAAS,CAAC,GAAG,EAAE;cACnC0e,KAAI,CAAC1e,YAAY,CAAC,SAAS,CAAC,CAACiT,IAAI,CAAC6L,aAAoB,CAAC;YACzD;UACF,CAAC,CAAC,OAAO7X,KAAK,EAAE;YACdhD,OAAO,CAACgD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;UACxD;QACF;MACF;IAAC;EACH;EAEciY,mBAAmBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAR,iBAAA;MAC/B,IAAIQ,MAAI,CAACvhB,gBAAgB,KAAK,YAAY,EAAE;MAE5C,MAAMwhB,UAAU,GAAGD,MAAI,CAAC/B,2BAA2B,EAAE;MACrD,IAAI,CAACgC,UAAU,CAAC5K,OAAO,EAAE;QACvB2K,MAAI,CAACxiB,aAAa,CAAC0iB,OAAO,CAAC;UACzBhd,KAAK,EAAE,mBAAmB;UAC1Bid,OAAO,EAAE,oCAAoC,GAAGF,UAAU,CAAC/B,MAAM,CAAChB,IAAI,CAAC,IAAI,CAAC;UAC5EkD,iBAAiB,EAAE,IAAI;UACvBC,iBAAiB,EAAE;SACpB,CAAC;QACF;MACF;MAEA,IAAIJ,UAAU,CAAC9B,QAAQ,IAAI8B,UAAU,CAAC9B,QAAQ,CAACvY,MAAM,GAAG,CAAC,EAAE;QACzDoa,MAAI,CAACxiB,aAAa,CAAC0iB,OAAO,CAAC;UACzBhd,KAAK,EAAE,+BAA+B;UACtCid,OAAO,EAAE,iDAAiD,GAC1DF,UAAU,CAAC9B,QAAQ,CAACjB,IAAI,CAAC,IAAI,CAAC,GAC9B,kEAAkE;UAClEkD,iBAAiB,EAAE,IAAI;UACvBC,iBAAiB,EAAE;SACpB,CAAC;MACJ;MAEA,MAAML,MAAI,CAACM,0BAA0B,EAAE;IAAC;EAC1C;EAEcC,sBAAsBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAhB,iBAAA;MAClC,IAAIgB,MAAI,CAAC/hB,gBAAgB,KAAK,eAAe,EAAE;MAE/C;MACA,MAAM;QAAE0c;MAAO,CAAE,GAAGqF,MAAI,CAACzF,qBAAqB,EAAE;MAChD,IAAI,CAACI,OAAO,IAAIA,OAAO,KAAK,CAAC,EAAE;QAC7BqF,MAAI,CAAChjB,aAAa,CAAC0iB,OAAO,CAAC;UACzBhd,KAAK,EAAE,4BAA4B;UACnCid,OAAO,EAAE,2GAA2G;UACpHC,iBAAiB,EAAE,IAAI;UACvBC,iBAAiB,EAAE;SACpB,CAAC;QACF;MACF;MAEA,MAAMJ,UAAU,GAAGO,MAAI,CAACC,8BAA8B,EAAE;MACxD,IAAI,CAACR,UAAU,CAAC5K,OAAO,EAAE;QACvBmL,MAAI,CAAChjB,aAAa,CAAC0iB,OAAO,CAAC;UACzBhd,KAAK,EAAE,mBAAmB;UAC1Bid,OAAO,EAAE,oCAAoC,GAAGF,UAAU,CAAC/B,MAAM,CAAChB,IAAI,CAAC,IAAI,CAAC;UAC5EkD,iBAAiB,EAAE,IAAI;UACvBC,iBAAiB,EAAE;SACpB,CAAC;QACF;MACF;MAEA,IAAIJ,UAAU,CAAC9B,QAAQ,IAAI8B,UAAU,CAAC9B,QAAQ,CAACvY,MAAM,GAAG,CAAC,EAAE;QACzD4a,MAAI,CAAChjB,aAAa,CAAC0iB,OAAO,CAAC;UACzBhd,KAAK,EAAE,+BAA+B;UACtCid,OAAO,EAAE,iDAAiD,GAC1DF,UAAU,CAAC9B,QAAQ,CAACjB,IAAI,CAAC,IAAI,CAAC,GAC9B,kEAAkE;UAClEkD,iBAAiB,EAAE,IAAI;UACvBC,iBAAiB,EAAE;SACpB,CAAC;QACF;MACF;MAEA,MAAMG,MAAI,CAACE,6BAA6B,EAAE;IAAC;EAC7C;EAEcA,6BAA6BA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAnB,iBAAA;MACzC,MAAMmB,MAAI,CAACrB,yBAAyB,EAAE;MACtC,MAAMsB,OAAO,GAAGD,MAAI,CAACE,gCAAgC,EAAE;MACvDF,MAAI,CAACG,SAAS,CACZ,MAAMH,MAAI,CAAC5jB,YAAY,CAACgkB,0BAA0B,CAACH,OAAO,CAAC,EAC3DA,OAAO,CAACva,IAAI,EACZ,IAAI,CACL;IAAC;EACJ;EAEcia,0BAA0BA,CAAA;IAAA,IAAAU,MAAA;IAAA,OAAAxB,iBAAA;MACtC,MAAMoB,OAAO,GAAGI,MAAI,CAACzC,2BAA2B,EAAE;MAClDyC,MAAI,CAACF,SAAS,CACZ,MAAME,MAAI,CAACjkB,YAAY,CAACkkB,mBAAmB,CAACL,OAAO,CAAC,EACpDA,OAAO,CAACva,IAAI,EACZ,KAAK,CACN;IAAC;EACJ;EAEQya,SAASA,CACfI,MAA6B,EAC7Bvf,SAAiB,EACjBwf,mBAA4B;IAE5BD,MAAM,EAAE,CAACzc,SAAS,CAAC;MACjBwB,IAAI,EAAGmC,QAAa,IAAI;QACtB,MAAMgZ,cAAc,GAAGD,mBAAmB,GACtC,wBAAwBxf,SAAS,IAAI,OAAO,sDAAsD,GAClG,qBAAqBA,SAAS,IAAI,OAAO,gCAAgC;QAE7E,MAAM0f,cAAc,GAAGjZ,QAAQ,EAAE+X,OAAO,IAAIiB,cAAc;QAE1D;QACA,IAAI,CAACziB,cAAc,GAAGyJ,QAAQ,CAACkZ,SAAS,IAAIlZ,QAAQ,CAACmZ,OAAO;QAE5D,IAAI,CAAC/jB,aAAa,CAACgkB,YAAY,CAAC;UAC9Bte,KAAK,EAAE,SAAS;UAChBid,OAAO,EAAEkB,cAAc;UACvBI,iBAAiB,EAAE;SACpB,CAAC;MACJ,CAAC;MACD3Z,KAAK,EAAGA,KAAU,IAAI;QACpB,MAAM4Z,YAAY,GAChB5Z,KAAK,EAAEA,KAAK,EAAEqY,OAAO,IACrBrY,KAAK,EAAEqY,OAAO,IACd,gBACEgB,mBAAmB,GAAG,eAAe,GAAG,YAC1C,2BAA2B;QAE3B,IAAI,CAAC3jB,aAAa,CAACsK,KAAK,CAAC;UACvB5E,KAAK,EAAE,OAAO;UACdid,OAAO,EAAEuB,YAAY;UACrBC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE;SAClB,CAAC;MACN;KACD,CAAC;EACJ;EAEQnB,8BAA8BA,CAAA;IAKpC,MAAMvC,MAAM,GAAa,EAAE;IAC3B,MAAMC,QAAQ,GAAa,EAAE;IAE7B,IAAI,CAAC,IAAI,CAACxc,SAAS,IAAI,IAAI,CAACA,SAAS,CAACsG,IAAI,EAAE,CAACrC,MAAM,KAAK,CAAC,EACvDsY,MAAM,CAACpK,IAAI,CAAC,wBAAwB,CAAC;IACvC,IAAI,CAAC,IAAI,CAAClS,WAAW,IAAI,IAAI,CAACA,WAAW,CAACqG,IAAI,EAAE,CAACrC,MAAM,KAAK,CAAC,EAC3DsY,MAAM,CAACpK,IAAI,CAAC,4BAA4B,CAAC;IAE3C,MAAM+N,WAAW,GAAG,IAAI,CAACvjB,eAAe,CAAC0J,MAAM,CAC5CwE,IAAI,IAAKA,IAAI,CAACvJ,IAAI,KAAK,QAAQ,CACjC;IACD,IAAI4e,WAAW,CAACjc,MAAM,KAAK,CAAC,EAAEsY,MAAM,CAACpK,IAAI,CAAC,8BAA8B,CAAC;IAEzE,MAAMgO,UAAU,GAAG,IAAI,CAACxjB,eAAe,CAAC0J,MAAM,CAC3CwE,IAAI,IAAKA,IAAI,CAACvJ,IAAI,KAAK,OAAO,CAChC;IACD,IAAI6e,UAAU,CAAClc,MAAM,KAAK,CAAC,EAAEsY,MAAM,CAACpK,IAAI,CAAC,6BAA6B,CAAC;IAEvE,MAAMiJ,cAAc,GAAG,IAAI,CAACze,eAAe,CAAC0J,MAAM,CAC/CwE,IAAI,IAAKA,IAAI,CAACvJ,IAAI,KAAK,WAAW,CACpC;IACD,IAAI8Z,cAAc,CAACnX,MAAM,KAAK,CAAC,EAC7BuY,QAAQ,CAACrK,IAAI,CACX,gEAAgE,CACjE;IAEH,MAAMiO,SAAS,GAAG,IAAI,CAACzjB,eAAe,CAAC0J,MAAM,CAC1CwE,IAAI,IAAKA,IAAI,CAACvJ,IAAI,KAAK,MAAM,CAC/B;IACD,IAAI8e,SAAS,CAACnc,MAAM,KAAK,CAAC,EACxBuY,QAAQ,CAACrK,IAAI,CACX,oEAAoE,CACrE;IAEH,OAAO;MAAEuB,OAAO,EAAE6I,MAAM,CAACtY,MAAM,KAAK,CAAC;MAAEsY,MAAM;MAAEC;IAAQ,CAAE;EAC3D;EAEQ0C,gCAAgCA,CAAA;IACtC,MAAM;MAAE1F;IAAO,CAAE,GAAG,IAAI,CAACJ,qBAAqB,EAAE;IAEhD,MAAM+G,UAAU,GAAG,IAAI,CAACxjB,eAAe,CAAC0J,MAAM,CAC3CwE,IAAI,IAAKA,IAAI,CAACvJ,IAAI,KAAK,OAAO,CAChC;IACD,MAAM+e,SAAS,GAAGF,UAAU,CAAC7a,GAAG,CAAEuF,IAAI,IAAI;MACxC,MAAM+C,SAAS,GAAG,IAAI,CAAC1O,YAAY,CAAC,QAAQ,CAAC,CAACiI,IAAI,CAC/CmZ,CAAC,IAAKA,CAAC,CAAC5b,IAAI,KAAKmG,IAAI,CAACnG,IAAI,CAC5B;MACD,OAAOkJ,SAAS,EAAEvM,EAAE,IAAI,EAAE;IAC5B,CAAC,CAAC;IACF,MAAMkf,eAAe,GAAG,CAAC,GAAG,IAAI3E,GAAG,CAACyE,SAAS,CAAC,CAAC;IAE/C,MAAMjF,cAAc,GAAG,IAAI,CAACze,eAAe,CAAC0J,MAAM,CAC/CwE,IAAI,IAAKA,IAAI,CAACvJ,IAAI,KAAK,WAAW,CACpC;IACD,MAAM+Z,YAAY,GAAGD,cAAc,CAChC9V,GAAG,CAAEuF,IAAI,IAAI;MACZ,IAAIA,IAAI,CAACC,gBAAgB,IAAID,IAAI,CAACC,gBAAgB,CAACzJ,EAAE,EAAE;QACrD,OAAOwJ,IAAI,CAACC,gBAAgB,CAACzJ,EAAE;MACjC;MACA,MAAMia,aAAa,GAAG,IAAI,CAACpc,YAAY,CAAC,WAAW,CAAC,CAACiI,IAAI,CACtDqZ,CAAC,IAAKA,CAAC,CAAC9b,IAAI,KAAKmG,IAAI,CAACnG,IAAI,CAC5B;MACD,OAAO4W,aAAa,EAAEja,EAAE;IAC1B,CAAC,CAAC,CACDgF,MAAM,CAAEhF,EAAE,IAAKA,EAAE,CAAC;IACrB,MAAMof,kBAAkB,GAAG,CAAC,GAAG,IAAI7E,GAAG,CAACP,YAAY,CAAC,CAAC;IAErD,MAAMoB,UAAU,GAAG,IAAI,CAAC9f,eAAe,CAACwK,IAAI,CACzC0D,IAAI,IAAKA,IAAI,CAACvJ,IAAI,KAAK,QAAQ,CACjC;IACD,IAAIyb,UAAU,GAAG,IAAI;IAErB,IAAIN,UAAU,EAAE;MACd,IAAIA,UAAU,CAAC3R,gBAAgB,EAAE;QAC/BiS,UAAU,GAAGN,UAAU,CAAC3R,gBAAgB;MAC1C,CAAC,MAAM;QACLiS,UAAU,GAAG,IAAI,CAAC7d,YAAY,CAAC,SAAS,CAAC,CAACiI,IAAI,CAC3C2W,CAAC,IAAKA,CAAC,CAACpZ,IAAI,KAAK+X,UAAU,CAAC/X,IAAI,CAClC;QACD,IAAI,CAACqY,UAAU,EAAE;UACf,MAAMgB,WAAW,GAAG,IAAI,CAAC/C,eAAe,CAACyB,UAAU,CAACpb,EAAE,CAAC;UACvD0b,UAAU,GAAG,IAAI,CAAC7d,YAAY,CAAC,SAAS,CAAC,CAACiI,IAAI,CAC3C2W,CAAC,IACA4C,MAAM,CAAC5C,CAAC,CAACzc,EAAE,CAAC,KAAK0c,WAAW,IAC5B2C,MAAM,CAAC5C,CAAC,CAACzc,EAAE,CAAC,KAAKqf,MAAM,CAACC,QAAQ,CAAC5C,WAAW,CAAC,CAAC,CACjD;QACH;MACF;IACF;IAEA,MAAM7X,kBAAkB,GAAG6W,UAAU,EAAE7W,kBAAkB,IAAI6W,UAAU;IACvE,MAAM6D,UAAU,GAAG1a,kBAAkB,IAAI6W,UAAU;IACnD,MAAMlX,IAAI,GAAG+a,UAAU,EAAE/a,IAAI,IAAI,kBAAkB;IACnD,MAAMC,IAAI,GACR8a,UAAU,EAAE9a,IAAI,IAChB,IAAI,CAAC7F,WAAW,IAChB,gDAAgD;IAClD,MAAM8F,SAAS,GACb6a,UAAU,EAAE7a,SAAS,IACrB,IAAI,CAAC9F,WAAW,IAChB,gEAAgE;IAClE,MAAMgF,WAAW,GACf2b,UAAU,EAAE3b,WAAW,IACvB2b,UAAU,EAAElb,uBAAuB,IACnC,IAAI,CAACzF,WAAW,IAChB,MAAM;IACR,MAAM+F,cAAc,GAClB4a,UAAU,EAAE5a,cAAc,IAC1B4a,UAAU,EAAE3a,0BAA0B,IACtC,aAAa;IAEf,MAAMma,SAAS,GAAG,IAAI,CAACzjB,eAAe,CAAC0J,MAAM,CAC1CwE,IAAI,IAAKA,IAAI,CAACvJ,IAAI,KAAK,MAAM,CAC/B;IACD,MAAM0H,YAAY,GAAa,EAAE;IACjC,MAAMU,SAAS,GAAa,EAAE;IAE9BvG,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;IACzCD,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAACzG,eAAe,CAACsH,MAAM,CAAC;IAClEd,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEgd,SAAS,CAACnc,MAAM,CAAC;IAClDd,OAAO,CAACC,GAAG,CACT,qBAAqB,EACrBgd,SAAS,CAAC9a,GAAG,CAAC,CAACuF,IAAI,EAAEgW,KAAK,MAAM;MAC9BA,KAAK;MACLxf,EAAE,EAAEwJ,IAAI,CAACxJ,EAAE;MACXqD,IAAI,EAAEmG,IAAI,CAACnG,IAAI;MACfpD,IAAI,EAAEuJ,IAAI,CAACvJ,IAAI;MACfwf,mBAAmB,EAAE,CAAC,CAACjW,IAAI,CAACC,gBAAgB;MAC5CiW,kBAAkB,EAAElW,IAAI,CAACC,gBAAgB,EAAEzJ,EAAE;MAC7C2f,oBAAoB,EAAEnW,IAAI,CAACC,gBAAgB,EAAEpG;KAC9C,CAAC,CAAC,CACJ;IAED0b,SAAS,CAACpO,OAAO,CAAC,CAACnH,IAAI,EAAEgW,KAAK,KAAI;MAChC1d,OAAO,CAACC,GAAG,CACT,8BAA8Byd,KAAK,GAAG,CAAC,IAAIT,SAAS,CAACnc,MAAM,MAAM,CAClE;MACDd,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE;QAC3B/B,EAAE,EAAEwJ,IAAI,CAACxJ,EAAE;QACXqD,IAAI,EAAEmG,IAAI,CAACnG,IAAI;QACfoG,gBAAgB,EAAED,IAAI,CAACC;OACxB,CAAC;MACF,IAAIwJ,QAAQ,GAAG,IAAI,CAACpV,YAAY,CAAC,OAAO,CAAC,CAACiI,IAAI,CAC3C8Z,CAAC,IAAKA,CAAC,CAACvc,IAAI,KAAKmG,IAAI,CAACnG,IAAI,CAC5B;MACD,IAAI,CAAC4P,QAAQ,IAAIzJ,IAAI,CAACC,gBAAgB,EAAE;QACtCwJ,QAAQ,GAAGzJ,IAAI,CAACC,gBAAgB;QAChC3H,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEyH,IAAI,CAACnG,IAAI,EAAE4P,QAAQ,CAAC;MACtE;MACA,IAAI,CAACA,QAAQ,IAAIzJ,IAAI,CAACC,gBAAgB,EAAEzJ,EAAE,EAAE;QAC1CiT,QAAQ,GAAG,IAAI,CAACpV,YAAY,CAAC,OAAO,CAAC,CAACiI,IAAI,CACvC8Z,CAAC,IAAKA,CAAC,CAAC5f,EAAE,KAAKwJ,IAAI,CAACC,gBAAgB,CAACzJ,EAAE,CACzC;QACD8B,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEkR,QAAQ,CAAC;MAC5C;MACA,IAAIzJ,IAAI,CAACC,gBAAgB,IAAID,IAAI,CAACC,gBAAgB,CAACzJ,EAAE,EAAE;QACrD8B,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;QAC7DkR,QAAQ,GAAGzJ,IAAI,CAACC,gBAAgB;MAClC;MAEA,IAAI,CAACwJ,QAAQ,EAAE;QACbnR,OAAO,CAACqN,IAAI,CAAC,iCAAiC3F,IAAI,CAACnG,IAAI,EAAE,EAAE;UACzDwQ,QAAQ,EAAErK,IAAI,CAACnG,IAAI;UACnBwc,gBAAgB,EAAErW,IAAI,CAACC,gBAAgB;UACvCJ,cAAc,EAAE,IAAI,CAACxL,YAAY,CAAC,OAAO,CAAC,CAACoG,GAAG,CAAE2b,CAAC,KAAM;YACrD5f,EAAE,EAAE4f,CAAC,CAAC5f,EAAE;YACRqD,IAAI,EAAEuc,CAAC,CAACvc;WACT,CAAC;SACH,CAAC;QACF;MACF;MAEAvB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEyH,IAAI,CAACnG,IAAI,EAAE4P,QAAQ,CAAC;MAE7D,IAAI6M,aAAqB;MACzB,MAAMjY,MAAM,GAAGoL,QAAQ,CAACjT,EAAE;MAE1B;MACA,IAAI,OAAO6H,MAAM,KAAK,QAAQ,EAAE;QAC9B,IAAIA,MAAM,CAACiH,UAAU,CAAC,UAAU,CAAC,EAAE;UACjC;UACA,MAAMC,WAAW,GAAGlH,MAAM,CAACmH,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;UAClD8Q,aAAa,GAAGR,QAAQ,CAACvQ,WAAW,CAAC;QACvC,CAAC,MAAM,IAAIlH,MAAM,CAACiH,UAAU,CAAC,OAAO,CAAC,EAAE;UACrC;UACA,MAAMC,WAAW,GAAGlH,MAAM,CAACmH,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;UAC/C8Q,aAAa,GAAGR,QAAQ,CAACvQ,WAAW,CAAC;QACvC,CAAC,MAAM;UACL;UACA,MAAMgR,YAAY,GAAGlY,MAAM,CAAC+R,KAAK,CAAC,KAAK,CAAC;UACxCkG,aAAa,GAAGC,YAAY,GAAGT,QAAQ,CAACS,YAAY,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;QAC9D;MACF,CAAC,MAAM,IAAI,OAAOlY,MAAM,KAAK,QAAQ,EAAE;QACrCiY,aAAa,GAAGjY,MAAM;MACxB,CAAC,MAAM;QACL;MACF;MAEA,MAAMmY,UAAU,GACd,OAAO/M,QAAQ,CAACjT,EAAE,KAAK,QAAQ,IAAIiT,QAAQ,CAACjT,EAAE,CAAC8O,UAAU,CAAC,OAAO,CAAC;MACpE,IAAIkR,UAAU,EAAE;QACd3X,SAAS,CAACyI,IAAI,CAACgP,aAAa,CAAC;MAC/B,CAAC,MAAM;QACLnY,YAAY,CAACmJ,IAAI,CAACgP,aAAa,CAAC;MAClC;IACF,CAAC,CAAC;IAEF,MAAMG,YAAY,GAAG;MACnBzT,WAAW,EAAE,GAAG;MAChBE,IAAI,EAAE,IAAI;MACVD,QAAQ,EAAE,MAAM;MAChB3F,QAAQ,EAAEoY,eAAe;MACzBgB,gBAAgB,EAAEd,kBAAkB;MACpCe,OAAO,EAAE,IAAI;MACbC,MAAM,EAAE,CAAC;MACTC,gBAAgB,EAAE,CAAC;MACnBC,eAAe,EAAE,IAAI;MACrBC,kBAAkB,EAAE,IAAI;MACxBC,mBAAmB,EAAE,IAAI;MACzBC,OAAO,EAAE9Y,YAAY;MACrB+Y,WAAW,EAAErY;KACd;IAED,MAAMgU,aAAa,GAAG,IAAI,CAACvD,gBAAgB,EAAE;IAE7C;IACA,IAAI,CAACX,OAAO,IAAIA,OAAO,KAAK,CAAC,EAAE;MAC7B,MAAM,IAAIwI,KAAK,CACb,sIAAsI,CACvI;IACH;IAEA,OAAO;MACLC,YAAY,EAAE,IAAI,CAAChiB,WAAW,CAACqG,IAAI,EAAE;MACrC5B,IAAI,EAAE,IAAI,CAAC1E,SAAS,CAACsG,IAAI,EAAE;MAC3BT,IAAI;MACJC,IAAI;MACJC,SAAS;MACTd,WAAW;MACXe,cAAc;MACdsb,YAAY;MACZjU,SAAS,EAAEqQ,aAAa;MACxBlE,OAAO,EAAEA,OAAO;MAChB/H,UAAU,EAAEiM;KACb;EACH;EAEQwE,2CAA2CA,CAAA;IACjD,MAAMC,WAAW,GAAG,IAAI,CAACjD,gCAAgC,EAAE;IAC3D,OAAO;MACL7d,EAAE,EAAEsf,QAAQ,CAAC,IAAI,CAAC3jB,cAAe,CAAC;MAClC,GAAGmlB,WAAW;MACdnR,SAAS,EAAE;KACZ;EACH;EAEQoR,iCAAiCA,CAAA;IACvC,MAAMD,WAAW,GAAG,IAAI,CAACvF,2BAA2B,EAAE;IACtD,OAAO;MACL,GAAGuF,WAAW;MACdxC,SAAS,EAAEgB,QAAQ,CAAC,IAAI,CAAC3jB,cAAe,CAAC;MACzCygB,aAAa,EAAE0E,WAAW,CAAC1E,aAAa,CAACnY,GAAG,CAAE8B,QAAa,KAAM;QAC/D,GAAGA,QAAQ;QACXqT,OAAO,EAAErT,QAAQ,CAACqT,OAAO,CAACnV,GAAG,CAAE+W,MAAW,KAAM;UAC9C,GAAGA,MAAM;UACTgG,QAAQ,EACN,IAAI,CAACxiB,cAAc,CAAC4D,GAAG,CACrB,GAAG2D,QAAQ,CAACC,UAAU,IAAIgV,MAAM,CAAC3B,iBAAiB,EAAE,CACrD,IAAIxI;SACR,CAAC;OACH,CAAC;KACH;EACH;EAEcoQ,qBAAqBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA1E,iBAAA;MACjC,OAAO,IAAII,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;QACrC,IAAI;UACF,MAAMc,OAAO,GAAGsD,MAAI,CAACH,iCAAiC,EAAE;UACxDG,MAAI,CAACnnB,YAAY,CAAConB,mBAAmB,CAACvD,OAAO,CAAC,CAACnc,SAAS,CAAC;YACvDwB,IAAI,EAAGmC,QAAa,IAAI;cACtB,MAAMiZ,cAAc,GAClBjZ,QAAQ,EAAE+X,OAAO,IACjB,qBAAqBS,OAAO,CAACva,IAAI,IAAI,OAAO,kCAAkC;cAC9E6d,MAAI,CAAC1mB,aAAa,CAACgkB,YAAY,CAAC;gBAC9Bte,KAAK,EAAE,SAAS;gBAChBid,OAAO,EAAEkB,cAAc;gBACvBI,iBAAiB,EAAE;eACpB,CAAC;cAEJ;cACAyC,MAAI,CAAC3kB,mBAAmB,GAAG;gBACzB,GAAG2kB,MAAI,CAAC3kB,mBAAmB;gBAC3B,GAAGqhB,OAAO;gBACVlV,IAAI,EAAEkV,OAAO,CAAClV,IAAI;gBAClBiT,WAAW,EAAEiC,OAAO,CAAClV,IAAI;gBACzBrF,IAAI,EAAEua,OAAO,CAACva,IAAI;gBAClB0Y,cAAc,EAAE6B,OAAO,CAAC7B;eACzB;cAED;cACAmF,MAAI,CAACriB,SAAS,GAAG+e,OAAO,CAAClV,IAAI;cAE7B;cACAwY,MAAI,CAACvlB,cAAc,GAAGulB,MAAI,CAACvlB,cAAc,IAAIiiB,OAAO,CAAC5d,EAAE;cAEvD;cACAkhB,MAAI,CAACE,6BAA6B,EAAE;cAEpCvE,OAAO,EAAE;YACX,CAAC;YACD/X,KAAK,EAAGA,KAAU,IAAI;cACpBhD,OAAO,CAACgD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;cACxD,MAAM4Z,YAAY,GAChB5Z,KAAK,EAAEA,KAAK,EAAEqY,OAAO,IACrBrY,KAAK,EAAEqY,OAAO,IACd,oDAAoD;cACpD+D,MAAI,CAAC1mB,aAAa,CAACsK,KAAK,CAAC;gBACvB5E,KAAK,EAAE,OAAO;gBACdid,OAAO,EAAEuB,YAAY;gBACrBC,eAAe,EAAE,IAAI;gBACrBC,eAAe,EAAE;eAClB,CAAC;cACJ9B,MAAM,CAAChY,KAAK,CAAC;YACf;WACD,CAAC;QACJ,CAAC,CAAC,OAAOA,KAAK,EAAE;UACdhD,OAAO,CAACgD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;UACvDgY,MAAM,CAAChY,KAAK,CAAC;QACf;MACF,CAAC,CAAC;IAAC;EACL;EAEcuc,qCAAqCA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA9E,iBAAA;MACjD,IAAI8E,MAAI,CAAC7lB,gBAAgB,KAAK,eAAe,IAAI,CAAC6lB,MAAI,CAAC3lB,cAAc,EACnE;MAEF;MACA,MAAM;QAAEwc;MAAO,CAAE,GAAGmJ,MAAI,CAACvJ,qBAAqB,EAAE;MAChD,IAAI,CAACI,OAAO,IAAIA,OAAO,KAAK,CAAC,EAAE;QAC7BmJ,MAAI,CAAC9mB,aAAa,CAACgkB,YAAY,CAAC;UAC9Bte,KAAK,EAAE,OAAO;UACdid,OAAO,EAAE,2GAA2G;UACpHsB,iBAAiB,EAAE;SACpB,CAAC;QACF;MACF;MAEA,MAAMxB,UAAU,GAAGqE,MAAI,CAAC7D,8BAA8B,EAAE;MACxD,IAAI,CAACR,UAAU,CAAC5K,OAAO,EAAE;QACvBvQ,OAAO,CAACgD,KAAK,CAAC,oBAAoB,EAAEmY,UAAU,CAAC/B,MAAM,CAAC;QACtDoG,MAAI,CAAChO,gBAAgB,CACnB,mBAAmB,EACnB,oCAAoC,GAAG2J,UAAU,CAAC/B,MAAM,CAAChB,IAAI,CAAC,IAAI,CAAC,CACpE;QACD;MACF;MAEA,IAAI;QACF,MAAM0D,OAAO,GAAG0D,MAAI,CAACT,2CAA2C,EAAE;QAClES,MAAI,CAACvnB,YAAY,CACdsnB,qCAAqC,CAACzD,OAAO,CAAC,CAC9Cnc,SAAS,CAAC;UACTwB,IAAI,EAAGmC,QAAa,IAAI;YACtB,MAAMiZ,cAAc,GAClBjZ,QAAQ,EAAE+X,OAAO,IACjB,6BAA6BS,OAAO,CAACva,IAAI,IAAI,OAAO,oCAAoC;YAC1F;YACAie,MAAI,CAAC3lB,cAAc,GAAG2lB,MAAI,CAAC3lB,cAAc,IAAIiiB,OAAO,CAAC5d,EAAE;YACvDshB,MAAI,CAAC9mB,aAAa,CAACgkB,YAAY,CAAC;cAC9Bte,KAAK,EAAE,SAAS;cAChBid,OAAO,EAAEkB,cAAc;cACvBI,iBAAiB,EAAE;aACpB,CAAC;YAEF6C,MAAI,CAACF,6BAA6B,EAAE;UACtC,CAAC;UACDtc,KAAK,EAAGA,KAAU,IAAI;YACpBhD,OAAO,CAACgD,KAAK,CACX,sDAAsD,EACtDA,KAAK,CACN;YACD,MAAM4Z,YAAY,GAChB5Z,KAAK,EAAEA,KAAK,EAAEqY,OAAO,IACrBrY,KAAK,EAAEqY,OAAO,IACd,oDAAoD;YACpDmE,MAAI,CAAC9mB,aAAa,CAACsK,KAAK,CAAC;cACvB5E,KAAK,EAAE,OAAO;cACdid,OAAO,EAAEuB,YAAY;cACrBC,eAAe,EAAE,IAAI;cACrBC,eAAe,EAAE;aAClB,CAAC;UACN;SACD,CAAC;MACN,CAAC,CAAC,OAAO9Z,KAAU,EAAE;QACnBhD,OAAO,CAACgD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD,MAAM4Z,YAAY,GAChB5Z,KAAK,EAAEqY,OAAO,IACd,yEAAyE;QACzEmE,MAAI,CAAC9mB,aAAa,CAAC0iB,OAAO,CAAC;UACzBhd,KAAK,EAAE,SAAS;UAChBid,OAAO,EAAEuB,YAAY;UACrBtB,iBAAiB,EAAE,IAAI;UACvBC,iBAAiB,EAAE;SACpB,CAAC;MACN;IAAC;EACH;EAEOkE,mBAAmBA,CAAA;IACxB;EAAA;EAGIC,oBAAoBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAjF,iBAAA;MACxB1a,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE;QACxChE,aAAa,EAAE0jB,MAAI,CAAC1jB,aAAa;QACjCO,UAAU,EAAEmjB,MAAI,CAACnjB,UAAU;QAC3BD,UAAU,EAAEojB,MAAI,CAACpjB,UAAU;QAC3BK,iBAAiB,EAAE+iB,MAAI,CAAC/iB;OACzB,CAAC;MAEF,IAAI+iB,MAAI,CAAC1jB,aAAa,EAAE;MAExB,IAAI0jB,MAAI,CAACnjB,UAAU,EAAE;QACnBwD,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;QAClD0f,MAAI,CAACpK,SAAS,EAAE;QAChB;MACF;MAEA;MACA,IAAIoK,MAAI,CAAC/iB,iBAAiB,KAAK,SAAS,EAAE;QACxCoD,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;QAC/D0f,MAAI,CAACpK,SAAS,EAAE;QAChB;MACF;MAEA,IAAIoK,MAAI,CAACpjB,UAAU,EAAE;QACnB,IAAIojB,MAAI,CAAChmB,gBAAgB,KAAK,YAAY,EAAE;UAC1C,MAAMgmB,MAAI,CAACR,qBAAqB,EAAE;QACpC,CAAC,MAAM,IAAIQ,MAAI,CAAChmB,gBAAgB,KAAK,eAAe,EAAE;UACpD,MAAMgmB,MAAI,CAACJ,qCAAqC,EAAE;QACpD;QACA;MACF;MAEA,MAAMpE,UAAU,GACdwE,MAAI,CAAChmB,gBAAgB,KAAK,YAAY,GAClCgmB,MAAI,CAACxG,2BAA2B,EAAE,GAClCwG,MAAI,CAAChE,8BAA8B,EAAE;MAC3C,IAAI,CAACR,UAAU,CAAC5K,OAAO,EAAE;QACvBoP,MAAI,CAACnO,gBAAgB,CACnB,mBAAmB,EACnB,kDAAkD,GAChD2J,UAAU,CAAC/B,MAAM,CAAChB,IAAI,CAAC,IAAI,CAAC,CAC/B;QACD;MACF;MAEA,IAAIuH,MAAI,CAAChmB,gBAAgB,KAAK,YAAY,EAAE;QAC1C,MAAMgmB,MAAI,CAAC1E,mBAAmB,EAAE;MAClC,CAAC,MAAM,IAAI0E,MAAI,CAAChmB,gBAAgB,KAAK,eAAe,EAAE;QACpD,MAAMgmB,MAAI,CAAClE,sBAAsB,EAAE;MACrC;IAAC;EACH;EAEAlG,SAASA,CAAA;IACP,IAAI,CAACtZ,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,iBAAiB,GAAG,IAAI;IAE7B;IACA,IAAI,CAAC0jB,6BAA6B,EAAE;IAEpC;IACA/e,UAAU,CAAC,MAAK;MACd,IAAI,CAAC9I,GAAG,CAAC6I,aAAa,EAAE;MACxB,IAAI,CAACif,qBAAqB,EAAE;MAC5B,IAAI,IAAI,CAAClnB,oBAAoB,EAAE;QAC7B,IAAI,CAACA,oBAAoB,CAACoI,0BAA0B,EAAE;MACxD;IACF,CAAC,EAAE,GAAG,CAAC;IAEP;IACAF,UAAU,CAAC,MAAK;MACd,IAAI,CAACgf,qBAAqB,EAAE;IAC9B,CAAC,EAAE,GAAG,CAAC;IAEP,IAAI,CAACP,6BAA6B,EAAE;IACpC,IAAI,IAAI,CAACziB,SAAS,EAAE;MAClBgE,UAAU,CAAC,MAAK;QACd,IAAI,CAACif,sBAAsB,EAAE;MAC/B,CAAC,EAAE,IAAI,CAAC;IACV;IAEA,IAAI,CAAC3jB,YAAY,GAAG,CAClB;MACEwF,IAAI,EAAE,IAAI;MACVpD,IAAI,EAAE,kBAAkB,IAAI,CAAC1B,SAAS,IAAI,kBAAkB;KAC7D,CACF;IAEDgE,UAAU,CAAC,MAAK;MACd,MAAM4b,OAAO,GAAG,cAAc,GAAGnS,IAAI,CAACyV,GAAG,EAAE;MAC3C,IAAI,CAACjoB,oBAAoB,CAACkoB,cAAc,CAACvD,OAAO,EAAE,IAAI,CAACtgB,YAAY,CAAC;MACpE,IAAI,CAACG,qBAAqB,GAAG,IAAI,CAACxE,oBAAoB,CACnDmoB,iBAAiB,EAAE,CACnBtgB,SAAS,CAAEugB,KAAK,IAAI;QACnB,IAAIA,KAAK,CAACC,WAAW,IAAID,KAAK,CAACna,MAAM,KAAK0W,OAAO,EAAE;UACjD,IAAI,CAACtgB,YAAY,GAAG+jB,KAAK,CAAC/jB,YAAY;QACxC;MACF,CAAC,CAAC;IACN,CAAC,EAAE,GAAG,CAAC;EACT;EAEAikB,iBAAiBA,CAAC/E,OAAe;IAC/B,IAAI,IAAI,CAAC1hB,gBAAgB,KAAK,YAAY,EAAE;MAC1C,IAAI,CAAC,IAAI,CAAC8D,cAAc,IAAI,IAAI,CAACA,cAAc,KAAK,SAAS,EAAE;QAC7D,IAAI,CAAC4iB,cAAc,CACjB,0DAA0D,CAC3D;QACD;MACF;MAEA,IAAIC,cAAc,GAAGjF,OAAO;MAC5B,IAAI,IAAI,CAACxd,sBAAsB,CAACiD,MAAM,GAAG,CAAC,EAAE;QAC1C,MAAMyf,SAAS,GAAG,IAAI,CAAC1iB,sBAAsB,CAC1CsE,GAAG,CAAEgK,IAAI,IAAKA,IAAI,CAACqU,YAAY,CAAC,CAChCpI,IAAI,CAAC,IAAI,CAAC;QACbkI,cAAc,GAAG,GAAGjF,OAAO,0BAA0BkF,SAAS,EAAE;MAClE;MAEA,IAAI,CAACpkB,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;QAAEwF,IAAI,EAAE,MAAM;QAAEpD,IAAI,EAAE+hB;MAAc,CAAE,CACvC;MACD,IAAI,CAAClkB,gBAAgB,GAAG,IAAI;MAE5B,MAAMgD,gBAAgB,GACpB,IAAI,CAAC5B,mBAAmB,CAAC8C,GAAG,CAAC,kBAAkB,CAAC,EAAEG,KAAK,IAAI,KAAK;MAClE,MAAMpB,aAAa,GACjB,IAAI,CAAC7B,mBAAmB,CAAC8C,GAAG,CAAC,eAAe,CAAC,EAAEG,KAAK,IAAI,KAAK;MAC/D,MAAMggB,SAAS,GACb,IAAI,CAAC1jB,SAAS,IAAI,IAAI,CAACW,iBAAiB,IAAI,IAAI,CAACD,cAAc;MAEjE,IAAIijB,iBAAiB,GAAG,IAAI,CAAC/iB,yBAAyB;MACtD,IAAI,CAAC+iB,iBAAiB,EAAE;QACtB,MAAMxK,OAAO,GAAG,IAAI,CAAC5Y,qBAAqB,EAAE;QAC5CojB,iBAAiB,GAAG,GAAGD,SAAS,GAAGvK,OAAO,EAAE;MAC9C;MAEA,IAAI,IAAI,CAACrY,sBAAsB,CAACiD,MAAM,GAAG,CAAC,EAAE;QAC1C,IAAI,CAAC6f,+BAA+B,CAClCtF,OAAO,EACPoF,SAAS,EACTC,iBAAiB,EACjBthB,gBAAgB,EAChBC,aAAa,CACd;QACD;MACF;MAEA,IAAI,CAACuhB,qBAAqB,CACxBvF,OAAO,EACPoF,SAAS,EACTC,iBAAiB,EACjBthB,gBAAgB,EAChBC,aAAa,CACd;IACH,CAAC,MAAM,IAAI,IAAI,CAAC1F,gBAAgB,KAAK,eAAe,EAAE;MACpD,IAAI,CAACyC,gBAAgB,GAAG,IAAI;MAC5B,IAAI0f,OAAO,GAAG;QACZvhB,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7BkiB,OAAO,EAAEtP,MAAM,CAAC,IAAI,CAACtT,cAAc,CAAC;QACpCgnB,IAAI,EAAE,IAAI,CAAC1oB,YAAY,CAAC8e,aAAa,EAAE,IAAI,uBAAuB;QAClE6J,UAAU,EAAE;UAAEC,QAAQ,EAAE1F;QAAO;OAChC;MAED,IAAI,IAAI,CAACxd,sBAAsB,CAACiD,MAAM,GAAG,CAAC,EAAE;QAC1C,MAAMkgB,WAAW,GAAG,IAAI,CAACnjB,sBAAsB,CAAC,CAAC,CAAC;QAClD,IAAIyiB,cAAsB;QAC1B,IAAI,IAAI,CAACziB,sBAAsB,CAACiD,MAAM,GAAG,CAAC,EAAE;UAC1C,MAAMyf,SAAS,GAAG,IAAI,CAAC1iB,sBAAsB,CAC1CsE,GAAG,CAAEgK,IAAI,IAAKA,IAAI,CAACqU,YAAY,CAAC,CAChCpI,IAAI,CAAC,IAAI,CAAC;UACbkI,cAAc,GAAG,sBAAsBC,SAAS,EAAE;UAElD,IAAI,CAACpkB,YAAY,GAAG,CAAC;YAAEwF,IAAI,EAAE,MAAM;YAAEpD,IAAI,EAAE+hB;UAAc,CAAE,CAAC;QAC9D;QACA,IAAI,CAACloB,sBAAsB,CACxB6oB,0BAA0B,CAACnF,OAAO,EAAEkF,WAAW,CAAC,CAChDxgB,IAAI,CACHpJ,QAAQ,CAAC,MAAK;UACZ,IAAI,CAACgF,gBAAgB,GAAG,KAAK;UAC7B,IAAI,CAAC2B,wBAAwB,GAAG,KAAK;QACvC,CAAC,CAAC,EACF7G,SAAS,CAAC,IAAI,CAAC8G,sBAAsB,CAAC,CACvC,CACA2B,SAAS,CAAC;UACTwB,IAAI,EAAG+f,GAAG,IAAK,IAAI,CAACC,0BAA0B,CAACD,GAAG,EAAE7F,OAAO,CAAC;UAC5DrY,KAAK,EAAGoe,GAAQ,IAAI;YAClB,IAAI,CAACjlB,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;cAAEwF,IAAI,EAAE,MAAM;cAAEpD,IAAI,EAAE8c;YAAO,CAAE,EAC/B;cACE1Z,IAAI,EAAE,IAAI;cACVpD,IAAI,EACF6iB,GAAG,EAAEpe,KAAK,EAAEqY,OAAO,IACnB+F,GAAG,EAAE/F,OAAO,IACZ;aACH,CACF;UACH;SACD,CAAC;MACN,CAAC,MAAM;QACL,IAAI,CAACjjB,sBAAsB,CACxBipB,kBAAkB,CAACvF,OAAO,CAAC,CAC3Btb,IAAI,CACHpJ,QAAQ,CAAC,MAAK;UACZ,IAAI,CAACgF,gBAAgB,GAAG,KAAK;UAC7B,IAAI,CAAC2B,wBAAwB,GAAG,KAAK;QACvC,CAAC,CAAC,EACF7G,SAAS,CAAC,IAAI,CAAC8G,sBAAsB,CAAC,CACvC,CACA2B,SAAS,CAAC;UACTwB,IAAI,EAAG+f,GAAG,IAAK,IAAI,CAACC,0BAA0B,CAACD,GAAG,EAAE7F,OAAO,CAAC;UAC5DrY,KAAK,EAAGoe,GAAQ,IAAI;YAClB,IAAI,CAACjlB,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;cAAEwF,IAAI,EAAE,MAAM;cAAEpD,IAAI,EAAE8c;YAAO,CAAE,EAC/B;cACE1Z,IAAI,EAAE,IAAI;cACVpD,IAAI,EACF6iB,GAAG,EAAEpe,KAAK,EAAEqY,OAAO,IACnB+F,GAAG,EAAE/F,OAAO,IACZ;aACH,CACF;UACH;SACD,CAAC;MACN;IACF;EACF;EAEA8F,0BAA0BA,CAAC7d,QAAa,EAAE+X,OAAe;IACvD,IAAI;MACF,MAAMiG,SAAS,GAAGhe,QAAQ,EAAEie,aAAa,EAAEC,KAAK,EAAEC,MAAM;MACxD,IAAIC,eAAe,GAAG,EAAE;MAExB,IAAIJ,SAAS,EAAE;QACb;QACAI,eAAe,GAAGJ,SAAS,CAACpU,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC;MACnD,CAAC,MAAM;QACLwU,eAAe,GAAGpe,QAAQ,EAAEie,aAAa,EAAEI,MAAM;MACnD;MAEA,IAAI,CAACxlB,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;QAAEwF,IAAI,EAAE,MAAM;QAAEpD,IAAI,EAAE8c;MAAO,CAAE,EAC/B;QAAE1Z,IAAI,EAAE,IAAI;QAAEpD,IAAI,EAAEmjB,eAAe,IAAI;MAAyB,CAAE,CACnE;IACH,CAAC,CAAC,OAAON,GAAQ,EAAE;MACjB,IAAI,CAACjlB,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;QACEwF,IAAI,EAAE,IAAI;QACVpD,IAAI,EAAE6iB,GAAG,EAAE/F,OAAO,IAAI;OACvB,CACF;IACH;EACF;EAEQsF,+BAA+BA,CACrCtF,OAAe,EACftb,IAAY,EACZ2gB,iBAAyB,EACzBthB,gBAAyB,EACzBC,aAAsB;IAEtB,MAAMuiB,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/B,IAAI,CAAChkB,sBAAsB,CAACgR,OAAO,CAAEiT,QAAQ,IAAI;MAC/C,IAAIA,QAAQ,CAAC3V,IAAI,EAAE;QACjByV,QAAQ,CAACG,MAAM,CAAC,OAAO,EAAED,QAAQ,CAAC3V,IAAI,CAAC;MACzC;IACF,CAAC,CAAC;IAEF,IAAIyV,QAAQ,CAACI,GAAG,CAAC,OAAO,CAAC,EAAE;MACzB,IAAI,CAAC5pB,sBAAsB,CACxB6pB,gBAAgB,CAACL,QAAQ,CAAC,CAC1BphB,IAAI,CACHrJ,SAAS,CAAE+qB,YAAY,IAAI;QACzB,MAAMC,WAAW,GACfD,YAAY,EAAEE,aAAa,EACvBjgB,GAAG,CAAEmB,QAAa,IAAKA,QAAQ,CAAC6e,WAAW,CAAC,EAC5C/J,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;QACtB,IAAI,CAACiK,8BAA8B,CACjChH,OAAO,EACPtb,IAAI,EACJ2gB,iBAAiB,EACjBthB,gBAAgB,EAChBC,aAAa,EACb8iB,WAAW,CACZ;QACD,OAAO7qB,EAAE,CAAC,IAAI,CAAC;MACjB,CAAC,CAAC,EACFD,UAAU,CAAE2L,KAAK,IAAI;QACnBhD,OAAO,CAACgD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,IAAI,CAAC4d,qBAAqB,CACxBvF,OAAO,EACPtb,IAAI,EACJ2gB,iBAAiB,EACjBthB,gBAAgB,EAChBC,aAAa,CACd;QACD,OAAO/H,EAAE,CAAC,IAAI,CAAC;MACjB,CAAC,CAAC,CACH,CACAqI,SAAS,EAAE;IAChB,CAAC,MAAM;MACL,IAAI,CAACihB,qBAAqB,CACxBvF,OAAO,EACPtb,IAAI,EACJ2gB,iBAAiB,EACjBthB,gBAAgB,EAChBC,aAAa,CACd;IACH;EACF;EAEQuhB,qBAAqBA,CAC3BvF,OAAe,EACftb,IAAY,EACZ2gB,iBAAyB,EACzBthB,gBAAyB,EACzBC,aAAsB;IAEtB,IAAID,gBAAgB,EAAE;MACpB,IAAI,CAACxB,gBAAgB,CAACoR,IAAI,CAAC;QAAEhF,OAAO,EAAEqR,OAAO;QAAE3Y,IAAI,EAAE;MAAM,CAAE,CAAC;IAChE;IAEA,MAAMoZ,OAAO,GAAG1c,gBAAgB,GAAG,IAAI,CAACxB,gBAAgB,GAAGyd,OAAO;IAClE,MAAM;MAAEhF;IAAO,CAAE,GAAG,IAAI,CAACJ,qBAAqB,EAAE;IAEhD,IAAI,CAAC7d,sBAAsB,CACxBkqB,cAAc,CACbxG,OAAO,EACP/b,IAAI,EACJX,gBAAgB,EAChBC,aAAa,EACb,IAAI,CAACvB,eAAe,EACpB4iB,iBAAiB,EACjB,EAAE,EACFrK,OAAO,CACR,CACA7V,IAAI,CACHpJ,QAAQ,CAAC,MAAK;MACZ,IAAI,CAACgF,gBAAgB,GAAG,KAAK;MAC7B,IAAI,CAAC2B,wBAAwB,GAAG,KAAK;IACvC,CAAC,CAAC,EACF7G,SAAS,CAAC,IAAI,CAAC8G,sBAAsB,CAAC,CACvC,CACA2B,SAAS,CAAC;MACTwB,IAAI,EAAGohB,iBAAsB,IAAI;QAC/B,IACEA,iBAAiB,EAAEjf,QAAQ,IAC3Bif,iBAAiB,EAAEjf,QAAQ,EAAEkf,OAAO,EACpC;UACA,MAAMC,cAAc,GAAGF,iBAAiB,CAACjf,QAAQ,CAACkf,OAAO,CAAC,CAAC,CAAC,CAACjkB,IAAI;UACjE,IAAI,CAACpC,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;YAAEwF,IAAI,EAAE,IAAI;YAAEpD,IAAI,EAAEkkB;UAAc,CAAE,CACrC;UACD,IAAIrjB,gBAAgB,EAAE;YACpB,IAAI,CAACxB,gBAAgB,CAACoR,IAAI,CAAC;cACzBhF,OAAO,EAAEyY,cAAc;cACvB/f,IAAI,EAAE;aACP,CAAC;UACJ;QACF,CAAC,MAAM;UACL1C,OAAO,CAACqN,IAAI,CAAC,iCAAiC,EAAEkV,iBAAiB,CAAC;UAClE,IAAI,CAAClC,cAAc,CACjB,+CAA+C,CAChD;QACH;MACF,CAAC;MACDrd,KAAK,EAAGA,KAAU,IAAI;QACpBhD,OAAO,CAACgD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;QAClC,MAAM4Z,YAAY,GAChB5Z,KAAK,EAAEA,KAAK,EAAEqY,OAAO,IACrB,kDAAkD;QACpD,IAAI,CAACgF,cAAc,CAACzD,YAAY,CAAC;QACjC,IAAIxd,gBAAgB,IAAI,IAAI,CAACxB,gBAAgB,CAACkD,MAAM,GAAG,CAAC,EAAE;UACxD,IAAI,CAAClD,gBAAgB,CAAC8kB,GAAG,EAAE;QAC7B;MACF;KACD,CAAC;EACN;EAEQL,8BAA8BA,CACpChH,OAAe,EACftb,IAAY,EACZ2gB,iBAAyB,EACzBthB,gBAAyB,EACzBC,aAAsB,EACtBsjB,YAAoB;IAEpB,IAAIvjB,gBAAgB,EAAE;MACpB,IAAI,CAACxB,gBAAgB,CAACoR,IAAI,CAAC;QAAEhF,OAAO,EAAEqR,OAAO;QAAE3Y,IAAI,EAAE;MAAM,CAAE,CAAC;IAChE;IACA,MAAMoZ,OAAO,GAAG1c,gBAAgB,GAAG,IAAI,CAACxB,gBAAgB,GAAGyd,OAAO;IAClE,MAAM;MAAEhF;IAAO,CAAE,GAAG,IAAI,CAACJ,qBAAqB,EAAE;IAEhD,IAAI,CAAC7d,sBAAsB,CACxBkqB,cAAc,CACbxG,OAAO,EACP/b,IAAI,EACJX,gBAAgB,EAChBC,aAAa,EACb,IAAI,CAACvB,eAAe,EACpB4iB,iBAAiB,EACjBiC,YAAY,EACZtM,OAAO,CACR,CACA7V,IAAI,CACHpJ,QAAQ,CAAC,MAAK;MACZ,IAAI,CAACgF,gBAAgB,GAAG,KAAK;MAC7B,IAAI,CAAC2B,wBAAwB,GAAG,KAAK;IACvC,CAAC,CAAC,EACF7G,SAAS,CAAC,IAAI,CAAC8G,sBAAsB,CAAC,CACvC,CACA2B,SAAS,CAAC;MACTwB,IAAI,EAAGohB,iBAAsB,IAAI;QAC/B,IACEA,iBAAiB,EAAEjf,QAAQ,IAC3Bif,iBAAiB,EAAEjf,QAAQ,EAAEkf,OAAO,EACpC;UACA,MAAMC,cAAc,GAAGF,iBAAiB,CAACjf,QAAQ,CAACkf,OAAO,CAAC,CAAC,CAAC,CAACjkB,IAAI;UACjE,IAAI,CAACpC,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;YAAEwF,IAAI,EAAE,IAAI;YAAEpD,IAAI,EAAEkkB;UAAc,CAAE,CACrC;UACD,IAAIrjB,gBAAgB,EAAE;YACpB,IAAI,CAACxB,gBAAgB,CAACoR,IAAI,CAAC;cACzBhF,OAAO,EAAEyY,cAAc;cACvB/f,IAAI,EAAE;aACP,CAAC;UACJ;QACF,CAAC,MAAM;UACL1C,OAAO,CAACqN,IAAI,CAAC,iCAAiC,EAAEkV,iBAAiB,CAAC;UAClE,IAAI,CAAClC,cAAc,CACjB,+CAA+C,CAChD;QACH;MACF,CAAC;MACDrd,KAAK,EAAGA,KAAU,IAAI;QACpBhD,OAAO,CAACgD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;QAClC,MAAM4Z,YAAY,GAChB5Z,KAAK,EAAEA,KAAK,EAAEqY,OAAO,IACrB,kDAAkD;QACpD,IAAI,CAACgF,cAAc,CAACzD,YAAY,CAAC;QACjC,IAAIxd,gBAAgB,IAAI,IAAI,CAACxB,gBAAgB,CAACkD,MAAM,GAAG,CAAC,EAAE;UACxD,IAAI,CAAClD,gBAAgB,CAAC8kB,GAAG,EAAE;QAC7B;MACF;KACD,CAAC;EACN;EAEQrC,cAAcA,CAAChF,OAAe;IACpC,IAAI,CAAClf,YAAY,GAAG,CAAC,GAAG,IAAI,CAACA,YAAY,EAAE;MAAEwF,IAAI,EAAE,IAAI;MAAEpD,IAAI,EAAE8c;IAAO,CAAE,CAAC;EAC3E;EAEAuH,gCAAgCA,CAAChS,KAAc;IAC7C,IAAI,CAACpT,mBAAmB,CAAC8C,GAAG,CAAC,kBAAkB,CAAC,EAAE2H,QAAQ,CAAC2I,KAAK,CAAC;EACnE;EAEAiS,0BAA0BA,CAACjS,KAAc;IACvC,IAAI,CAACpT,mBAAmB,CAAC8C,GAAG,CAAC,eAAe,CAAC,EAAE2H,QAAQ,CAAC2I,KAAK,CAAC;EAChE;EAEAkS,eAAeA,CAAClX,KAAY;IAC1B,IAAI,CAAC/N,sBAAsB,GAAG+N,KAAK;EACrC;EAEAmX,2BAA2BA,CAACnS,KAAU;IACpC,MAAMxR,gBAAgB,GAAGwR,KAAK;IAC9B,IAAI,CAACpT,mBAAmB,CACrB8C,GAAG,CAAC,kBAAkB,CAAC,EACtB2H,QAAQ,CAAC7I,gBAAgB,CAAC;IAC9B,IACEA,gBAAgB,IAChB,IAAI,CAAC5B,mBAAmB,CAAC8C,GAAG,CAAC,eAAe,CAAC,EAAEG,KAAK,EACpD;MACA,IAAI,CAACjD,mBAAmB,CAAC8C,GAAG,CAAC,eAAe,CAAC,EAAE2H,QAAQ,CAAC,KAAK,CAAC;IAChE;EACF;EAEA+a,qBAAqBA,CAACpS,KAAU;IAC9B,MAAMvR,aAAa,GAAGuR,KAAK;IAC3B,IAAI,CAACpT,mBAAmB,CAAC8C,GAAG,CAAC,eAAe,CAAC,EAAE2H,QAAQ,CAAC5I,aAAa,CAAC;IACtE,IACEA,aAAa,IACb,IAAI,CAAC7B,mBAAmB,CAAC8C,GAAG,CAAC,kBAAkB,CAAC,EAAEG,KAAK,EACvD;MACA,IAAI,CAACjD,mBAAmB,CAAC8C,GAAG,CAAC,kBAAkB,CAAC,EAAE2H,QAAQ,CAAC,KAAK,CAAC;IACnE;EACF;EAEAgb,mBAAmBA,CAACrS,KAAU;IAC5B,MAAMhF,KAAK,GAAGgF,KAAK,CAAC+D,MAAM,CAAC/I,KAAK;IAChC,IAAIA,KAAK,IAAIA,KAAK,CAAC9K,MAAM,GAAG,CAAC,EAAE;MAC7B,IAAI,CAACjD,sBAAsB,GAAG,EAAE;MAChC,KAAK,IAAIgE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+J,KAAK,CAAC9K,MAAM,EAAEe,CAAC,EAAE,EAAE;QACrC,MAAMsK,IAAI,GAAGP,KAAK,CAAC/J,CAAC,CAAC;QACrB,IAAI,CAAChE,sBAAsB,CAACmR,IAAI,CAAC;UAC/B9Q,EAAE,EAAE,cAAcoM,IAAI,CAACyV,GAAG,EAAE,IAAIle,CAAC,EAAE;UACnC2e,YAAY,EAAErU,IAAI,CAAC5K,IAAI;UACvB2hB,OAAO,EAAE/W,IAAI,CAAChO,IAAI,CAAC6O,UAAU,CAAC,QAAQ,CAAC;UACvCb,IAAI,EAAEA;SACP,CAAC;MACJ;IACF;EACF;EAEAgX,eAAeA,CAACzF,KAAa;IAC3B,IAAI,CAAC7f,sBAAsB,CAAC8X,MAAM,CAAC+H,KAAK,EAAE,CAAC,CAAC;EAC9C;EAEA0F,kBAAkBA,CAAA;IAChB,IAAI,CAACjnB,YAAY,GAAG,CAClB;MACEwF,IAAI,EAAE,IAAI;MACVpD,IAAI,EAAE;KACP,CACF;IACD,IAAI,CAACX,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACC,sBAAsB,GAAG,EAAE;IAChC,IAAI,CAACC,eAAe,GAAG,EAAE;EAC3B;EAEAulB,iBAAiBA,CAAChI,OAAe,EAAEiI,OAAA,GAAmB,KAAK;IACzD,IAAI,CAACzkB,YAAY,GAAGwc,OAAO;IAC3B,IAAI,CAACvc,YAAY,GAAGwkB,OAAO;IAC3B,IAAI,CAAC1kB,mBAAmB,GAAG,IAAI;EACjC;EAEA2kB,kBAAkBA,CAAA;IAChB,IAAI,CAAC3kB,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,YAAY,GAAG,KAAK;EAC3B;EAEOwgB,6BAA6BA,CAAA;IAClC,IAAI,IAAI,CAAC7kB,mBAAmB,EAAE;MAC5B,MAAM+oB,SAAS,GAAG,IAAI,CAAC/oB,mBAAmB;MAC1C,IAAI,CAAC4B,aAAa,GAAG,CACnB;QACEoE,KAAK,EACH+iB,SAAS,CAAC3J,WAAW,IACrB2J,SAAS,CAAC5c,IAAI,IACd,IAAI,CAAC/M,cAAc,IACnB,eAAe;QACjB0H,IAAI,EAAEiiB,SAAS,CAACC,WAAW,IAAID,SAAS,CAACjiB,IAAI,IAAI,eAAe;QAChEiiB,SAAS,EAAEA;OACL,CACT;MAED,IAAI,CAAC/lB,cAAc,GAAG,IAAI,CAACpB,aAAa,CAAC,CAAC,CAAC,CAACoE,KAAK,CAAC2B,QAAQ,EAAE;MAC5D,IAAI,CAAC1E,iBAAiB,GAAG8lB,SAAS,CAAC3J,WAAW,IAAI2J,SAAS,CAAC5c,IAAI,IAAI,EAAE;MAEtE,IAAI4c,SAAS,CAACpN,gBAAgB,EAAE;QAC9B,IAAI,CAACzY,yBAAyB,GAAG6lB,SAAS,CAACpN,gBAAgB;MAC7D,CAAC,MAAM;QACL,MAAMF,OAAO,GAAG,IAAI,CAAC5Y,qBAAqB,EAAE;QAC5C,IAAI,CAACK,yBAAyB,GAAG,GAAG,IAAI,CAACD,iBAAiB,GAAGwY,OAAO,EAAE;MACxE;IACF;IAEA,IAAI,CAAC7Z,aAAa,CAACqnB,OAAO,CAAC;MAAEjjB,KAAK,EAAE,SAAS;MAAEc,IAAI,EAAE;IAAiB,CAAE,CAAC;IACzE,IAAI,CAACxJ,GAAG,CAAC6I,aAAa,EAAE;EAC1B;EAEQkf,sBAAsBA,CAAA;IAC5B,IAAI,CAAC,IAAI,CAACzjB,aAAa,EAAEyE,MAAM,IAAI,CAAC,IAAI,CAACjE,SAAS,EAAE;IAEpD;IACA,MAAM8mB,kBAAkB,GAAG,IAAI,CAACtnB,aAAa,CAAC2H,IAAI,CAAEU,MAAM,IAAI;MAC5D,MAAM8e,SAAS,GAAI9e,MAAc,CAAC8e,SAAS;MAC3C,OACEA,SAAS,EAAE5c,IAAI,KAAK,IAAI,CAAC7J,SAAS,IAClCymB,SAAS,EAAE3J,WAAW,KAAK,IAAI,CAAC9c,SAAS,IACzC2H,MAAM,CAACjE,KAAK,KAAK,IAAI,CAAC1D,SAAS,IAC/B2H,MAAM,CAACnD,IAAI,KAAK,IAAI,CAAC1E,SAAS;IAElC,CAAC,CAAC;IAEF,IAAI,CAAC8mB,kBAAkB,EAAE;IAEzB,IAAI,CAAClmB,cAAc,GAAGkmB,kBAAkB,CAACljB,KAAK,CAAC2B,QAAQ,EAAE,CAAC,CAAC;IAC3D,IAAI,CAACwhB,eAAe,CAACD,kBAAkB,CAAC;IACxC,IAAI,CAAC5rB,GAAG,CAAC6I,aAAa,EAAE;EAC1B;EAEAgjB,eAAeA,CAACC,cAA8B;IAC5C,MAAML,SAAS,GAAIK,cAAsB,CAACL,SAAS;IACnD,MAAMzmB,SAAS,GAAGymB,SAAS,EAAE5c,IAAI,IAAIid,cAAc,CAACpjB,KAAK,CAAC2B,QAAQ,EAAE;IAEpE,IAAI,CAAC3E,cAAc,GAAGomB,cAAc,CAACtiB,IAAI;IACzC,IAAI,CAACxE,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACW,iBAAiB,GAAGX,SAAS;IAElC,IAAIymB,SAAS,EAAE9C,iBAAiB,EAAE;MAChC,IAAI,CAAC/iB,yBAAyB,GAAG6lB,SAAS,CAAC9C,iBAAiB;IAC9D,CAAC,MAAM;MACL,IAAI,CAAC/iB,yBAAyB,GAAG,GAAGZ,SAAS,GAAG,IAAI,CAACO,qBAAqB,EAAE,EAAE;IAChF;IAEA,IAAI,CAACvF,GAAG,CAAC6I,aAAa,EAAE;EAC1B;EAEA;EACAkjB,iBAAiBA,CAAA;IACf,IAAI,CAAC7nB,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAE9B;IACA,IAAI,CAACzC,YAAY,GAAG,EAAE;IACtB,IAAI,CAACH,WAAW,GAAG,IAAI,CAACE,eAAe,CAAC2I,GAAG,CAAEuF,IAAI,KAAM;MACrDxJ,EAAE,EAAEwJ,IAAI,CAACxJ,EAAE;MACXC,IAAI,EAAE,aAAa;MACnBmL,IAAI,EAAE;QAAE,GAAG5B,IAAI;QAAE2K,KAAK,EAAE;MAAE,CAAE;MAC5BV,QAAQ,EAAEjK,IAAI,CAACiK;KAChB,CAAC,CAAC;IAEH;IACA,IAAI,CAAC3Q,0BAA0B,CAAC,IAAI,CAACxH,eAAe,CAAC;IAErD;IAEA;IACAqH,UAAU,CAAC,MAAK;MACd,IAAI,CAAC9I,GAAG,CAAC6I,aAAa,EAAE;IAC1B,CAAC,EAAE,GAAG,CAAC;IAEP;IACA,IAAI,IAAI,CAACtE,qBAAqB,EAAE;MAC9B,IAAI,CAACA,qBAAqB,CAAC+E,WAAW,EAAE;MACxC,IAAI,CAAC/E,qBAAqB,GAAGyS,SAAS;IACxC;IAEA;IACA,IAAI,CAACjX,oBAAoB,CAACisB,aAAa,EAAE;EAC3C;EAEAC,oBAAoBA,CAAC9D,KAGpB;IACC;IACA,IAAI,CAAC3mB,WAAW,GAAG2mB,KAAK,CAAC+D,KAAK;IAE9B;IACApjB,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAAClI,oBAAoB,EAAE;QAC7B,IAAI,CAACA,oBAAoB,CAACoI,0BAA0B,EAAE;MACxD;IACF,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACAmjB,kBAAkBA,CAACrnB,SAAiB;IAClC,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC5B;EAEA;EACAsnB,iBAAiBA,CAACC,QAKjB;IACC,IAAI,CAACnnB,aAAa,GAAGmnB,QAAQ;EAC/B;EAEA;EACAC,qBAAqBA,CAACC,OAGrB;IACC,IAAI,CAACznB,SAAS,GAAGynB,OAAO,CAAC/iB,IAAI;IAC7B,IAAI,CAACzE,WAAW,GAAGwnB,OAAO,CAACrK,cAAc;EAC3C;EAEA;EACAsK,iBAAiBA,CAAA;IACf,MAAMC,YAAY,GAAG,IAAI,CAAC5oB,IAAI,CAACoI,IAAI,CAAEwE,GAAG,IAAKA,GAAG,CAACtK,EAAE,KAAK,IAAI,CAACpF,SAAS,CAAC;IACvE,OAAO0rB,YAAY,GAAGA,YAAY,CAACngB,KAAK,IAAI,MAAM,GAAG,MAAM;EAC7D;EAEA;EACAogB,eAAeA,CAAA;IACb;IACA,QAAQ,IAAI,CAAC3rB,SAAS;MACpB,KAAK,SAAS;QACZ,IAAI,CAAClB,MAAM,CAACuI,QAAQ,CAAC,CAAC,2BAA2B,CAAC,CAAC;QACnD;MACF,KAAK,QAAQ;QACX,IAAI,CAACvI,MAAM,CAACuI,QAAQ,CAAC,CAAC,0BAA0B,CAAC,CAAC;QAClD;MACF,KAAK,WAAW;QACd,IAAI,CAACvI,MAAM,CAACuI,QAAQ,CAAC,CAAC,kCAAkC,CAAC,CAAC;QAC1D;MACF,KAAK,OAAO;QACV,IAAI,CAACvI,MAAM,CAACuI,QAAQ,CAAC,CAAC,yBAAyB,CAAC,CAAC;QACjD;MACF,KAAK,YAAY;QACf,IAAI,CAACvI,MAAM,CAACuI,QAAQ,CAAC,CAAC,8BAA8B,CAAC,CAAC;QACtD;MACF;QACEH,OAAO,CAACqN,IAAI,CAAC,sCAAsC,IAAI,CAACvU,SAAS,EAAE,CAAC;IACxE;EACF;EAEA;EACQ8mB,6BAA6BA,CAAA;IACnC;IACA,IAAI,CAAC8E,kBAAkB,EAAE;IAEzB;IACA,IAAI,CAAC3sB,GAAG,CAAC6I,aAAa,EAAE;EAC1B;EAEA;EACQkR,qBAAqBA,CAACnJ,QAAgB;IAC5C3I,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE;MAC9C0I,QAAQ;MACR1M,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCqW,qBAAqB,EAAE,IAAI,CAAC9Y,eAAe,CAACsH,MAAM;MAClD5F,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BypB,qBAAqB,EAAE,IAAI,CAACnrB,eAAe,CAAC2I,GAAG,CAAEwR,CAAC,KAAM;QACtDzV,EAAE,EAAEyV,CAAC,CAACzV,EAAE;QACR0mB,GAAG,EAAEjR,CAAC,CAAChC;OACR,CAAC;KACH,CAAC;IAEF,IAAI,IAAI,CAAC1V,aAAa,EAAE;MACtB;MACA,MAAM4oB,OAAO,GAAG,GAAG,CAAC,CAAC;MACrB,MAAMC,MAAM,GAAG,GAAG,CAAC,CAAC;MACpB,MAAMC,eAAe,GAAG,EAAE,CAAC,CAAC;MAE5B;MACA,MAAMC,SAAS,GAAG,IAAI,CAACxrB,eAAe,CAACsH,MAAM;MAC7C,MAAMmkB,eAAe,GAAG;QACtBhR,CAAC,EAAE4Q,OAAO;QACV3Q,CAAC,EAAE4Q,MAAM,GAAGE,SAAS,GAAGD;OACzB;MACD/kB,OAAO,CAACC,GAAG,CACT,uDAAuD,EACvDglB,eAAe,CAChB;MACD,OAAOA,eAAe;IACxB;IAEA;IACA,MAAMC,cAAc,GAAG,IAAI,CAACC,+BAA+B,EAAE;IAC7DnlB,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEilB,cAAc,CAAC;IACjE,OAAOA,cAAc;EACvB;EAEA;EACQC,+BAA+BA,CAAA;IACrC,MAAMC,aAAa,GAAG,GAAG,CAAC,CAAC;IAC3B,MAAMC,aAAa,GAAG,GAAG,CAAC,CAAC;IAC3B,MAAMN,eAAe,GAAG,GAAG,CAAC,CAAC;IAE7B/kB,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAE;MAC7DmlB,aAAa;MACbC,aAAa;MACbN;KACD,CAAC;IAEF;IACA,IAAI,IAAI,CAACvrB,eAAe,CAACsH,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC5F,YAAY,EAAE;MAC3D8E,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAE;QAChEqlB,WAAW,EAAE,IAAI,CAAC9rB,eAAe,CAACsH,MAAM;QACxC5F,YAAY,EAAE,IAAI,CAACA,YAAY;QAC/BqqB,eAAe,EAAE;UAAEtR,CAAC,EAAEmR,aAAa;UAAElR,CAAC,EAAEmR;QAAa;OACtD,CAAC;MACF,OAAO;QAAEpR,CAAC,EAAEmR,aAAa;QAAElR,CAAC,EAAEmR;MAAa,CAAE;IAC/C;IAEA;IACA,MAAMG,UAAU,GAAG,IAAI,CAAChsB,eAAe,CAACwK,IAAI,CACzC0D,IAAI,IAAKA,IAAI,CAACxJ,EAAE,KAAK,IAAI,CAAChD,YAAY,CACxC;IAED8E,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE;MACpCwlB,cAAc,EAAE,IAAI,CAACvqB,YAAY;MACjCwqB,SAAS,EAAEF,UAAU;MACrBG,UAAU,EAAE,IAAI,CAACnsB,eAAe,CAAC2I,GAAG,CAAEwR,CAAC,IAAKA,CAAC,CAACzV,EAAE;KACjD,CAAC;IAEF,IAAI,CAACsnB,UAAU,EAAE;MACf;MACAxlB,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;MAC/D,OAAO;QAAEgU,CAAC,EAAEmR,aAAa;QAAElR,CAAC,EAAEmR;MAAa,CAAE;IAC/C;IAEA;IACA,MAAM5R,WAAW,GAAG;MAClBQ,CAAC,EAAEuR,UAAU,CAAC7T,QAAQ,CAACsC,CAAC;MAAE;MAC1BC,CAAC,EAAEsR,UAAU,CAAC7T,QAAQ,CAACuC,CAAC,GAAG6Q,eAAe,CAAE;KAC7C;IAED/kB,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAE;MAC3D2lB,kBAAkB,EAAEJ,UAAU,CAAC7T,QAAQ;MACvC8B,WAAW;MACXoS,OAAO,EAAEd;KACV,CAAC;IAEF,OAAOtR,WAAW;EACpB;EAEA;EACQqS,qBAAqBA,CAAA;IAC3B;IACA,IAAI,CAACxsB,WAAW,GAAG,IAAI,CAACE,eAAe,CAAC2I,GAAG,CAAEuF,IAAI,KAAM;MACrDxJ,EAAE,EAAEwJ,IAAI,CAACxJ,EAAE;MACXC,IAAI,EAAE,aAAa;MACnBmL,IAAI,EAAE;QAAE,GAAG5B,IAAI;QAAE2K,KAAK,EAAE,IAAI,CAACpW,aAAa,GAAG,EAAE,GAAG;MAAE,CAAE;MACtD0V,QAAQ,EAAEjK,IAAI,CAACiK,QAAQ,CAAE;KAC1B,CAAC,CAAC;IAEH;IACA,IAAI,CAAC5Z,GAAG,CAAC6I,aAAa,EAAE;EAC1B;EAEA;EACQif,qBAAqBA,CAAA;IAC3B,IAAI,CAAC,IAAI,CAAC5jB,aAAa,EAAE;IAEzB,IAAI,CAACzC,eAAe,CAACqV,OAAO,CAAC,CAACnH,IAAI,EAAEgW,KAAK,KAAI;MAC3C,MAAMuH,eAAe,GAAG;QACtBhR,CAAC,EAAE,EAAE;QACLC,CAAC,EAAE,GAAG,GAAGwJ,KAAK,GAAG;OAClB;MAED;MACA,MAAMqI,WAAW,GAAGC,QAAQ,CAACC,aAAa,CACxC,kBAAkBve,IAAI,CAACxJ,EAAE,IAAI,CACf;MAChB,IAAI6nB,WAAW,EAAE;QACfA,WAAW,CAACG,KAAK,CAACC,WAAW,CAAC,UAAU,EAAE,GAAGlB,eAAe,CAAChR,CAAC,IAAI,CAAC;QACnE8R,WAAW,CAACG,KAAK,CAACC,WAAW,CAAC,UAAU,EAAE,GAAGlB,eAAe,CAAC/Q,CAAC,IAAI,CAAC;MACrE;MAEA;MACAxM,IAAI,CAACiK,QAAQ,GAAG;QAAE,GAAGsT;MAAe,CAAE;IACxC,CAAC,CAAC;IAEF;IACA,IAAI,CAAC3rB,WAAW,GAAG,IAAI,CAACE,eAAe,CAAC2I,GAAG,CAAC,CAACuF,IAAI,EAAEgW,KAAK,MAAM;MAC5Dxf,EAAE,EAAEwJ,IAAI,CAACxJ,EAAE;MACXC,IAAI,EAAE,aAAa;MACnBmL,IAAI,EAAE;QACJ,GAAG5B,IAAI;QACP2K,KAAK,EAAE,EAAE;QACTV,QAAQ,EAAEjK,IAAI,CAACiK;OAChB;MACDA,QAAQ,EAAEjK,IAAI,CAACiK;KAChB,CAAC,CAAC;IAEH,IAAI,CAAC5Z,GAAG,CAAC6I,aAAa,EAAE;EAC1B;EAEA;EACQoS,mBAAmBA,CACzBE,MAAc,EACdvB,QAAkC;IAElC3R,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;MAAEiT,MAAM;MAAEvB;IAAQ,CAAE,CAAC;IAEnE,MAAMoU,WAAW,GAAGC,QAAQ,CAACC,aAAa,CACxC,kBAAkB/S,MAAM,IAAI,CACd;IAChB,IAAI6S,WAAW,EAAE;MACf/lB,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;MAC9D;MACA8lB,WAAW,CAACG,KAAK,CAACvU,QAAQ,GAAG,UAAU;MACvCoU,WAAW,CAACG,KAAK,CAACE,IAAI,GAAG,GAAGzU,QAAQ,CAACsC,CAAC,IAAI;MAC1C8R,WAAW,CAACG,KAAK,CAACG,GAAG,GAAG,GAAG1U,QAAQ,CAACuC,CAAC,IAAI;MACzC6R,WAAW,CAACG,KAAK,CAACI,SAAS,GAAG,EAAE,CAAC,CAAC;MAElC;MACA,MAAMC,eAAe,GAAG,IAAI,CAAC/sB,eAAe,CAACsa,SAAS,CACnDH,CAAC,IAAKA,CAAC,CAACzV,EAAE,KAAKgV,MAAM,CACvB;MACD,IAAIqT,eAAe,KAAK,CAAC,CAAC,EAAE;QAC1B,IAAI,CAAC/sB,eAAe,CAAC+sB,eAAe,CAAC,GAAG;UACtC,GAAG,IAAI,CAAC/sB,eAAe,CAAC+sB,eAAe,CAAC;UACxC5U,QAAQ,EAAEA;SACX;MACH;MAEA,MAAM6U,WAAW,GAAG,IAAI,CAACltB,WAAW,CAACwa,SAAS,CAAEH,CAAC,IAAKA,CAAC,CAACzV,EAAE,KAAKgV,MAAM,CAAC;MACtE,IAAIsT,WAAW,KAAK,CAAC,CAAC,EAAE;QACtB,IAAI,CAACltB,WAAW,CAACktB,WAAW,CAAC,GAAG;UAC9B,GAAG,IAAI,CAACltB,WAAW,CAACktB,WAAW,CAAC;UAChC7U,QAAQ,EAAEA;SACX;MACH;MAEA3R,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAChD,CAAC,MAAM;MACLD,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrD;MACAY,UAAU,CAAC,MAAK;QACd,IAAI,CAACmS,mBAAmB,CAACE,MAAM,EAAEvB,QAAQ,CAAC;MAC5C,CAAC,EAAE,GAAG,CAAC;IACT;EACF;EAEA;EACQ8U,gBAAgBA,CAACC,QAAgB,EAAEC,QAAgB;IACzD;IACA,MAAMC,UAAU,GAAG,IAAI,CAACptB,eAAe,CAACwK,IAAI,CACzC0D,IAAI,IAAKA,IAAI,CAACxJ,EAAE,KAAKwoB,QAAQ,CAC/B;IACD,MAAMG,UAAU,GAAG,IAAI,CAACrtB,eAAe,CAACwK,IAAI,CACzC0D,IAAI,IAAKA,IAAI,CAACxJ,EAAE,KAAKyoB,QAAQ,CAC/B;IAED,IAAI,CAACC,UAAU,IAAI,CAACC,UAAU,EAAE;MAC9B7mB,OAAO,CAACqN,IAAI,CACV,sEAAsEqZ,QAAQ,aAAaC,QAAQ,EAAE,CACtG;MACD;IACF;IAEA;IACA,MAAMG,kBAAkB,GAAG,IAAI,CAACvtB,WAAW,CAACyK,IAAI,CAC7CyQ,IAAI,IAAKA,IAAI,CAACC,MAAM,KAAKgS,QAAQ,IAAIjS,IAAI,CAACE,MAAM,KAAKgS,QAAQ,CAC/D;IAED,IAAIG,kBAAkB,EAAE;MACtB,OAAO,CAAC;IACV;IAEA,MAAMhS,OAAO,GAAe;MAC1B5W,EAAE,EAAE,QAAQwoB,QAAQ,IAAIC,QAAQ,EAAE;MAClCjS,MAAM,EAAEgS,QAAQ;MAChB/R,MAAM,EAAEgS,QAAQ;MAChB3R,QAAQ,EAAE,KAAK,CAAE;KAClB;IAED,IAAI,CAACzb,WAAW,GAAG,CAAC,GAAG,IAAI,CAACA,WAAW,EAAEub,OAAO,CAAC;EACnD;EAEA;EACQ9T,0BAA0BA,CAAC+lB,KAA2B;IAC5D/mB,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE;MACnDqlB,WAAW,EAAEyB,KAAK,CAACjmB,MAAM;MACzBkmB,OAAO,EAAED,KAAK,CAAC5kB,GAAG,CAAEwR,CAAC,IAAKA,CAAC,CAACzV,EAAE,CAAC;MAC/B+oB,aAAa,EAAEF,KAAK,CAAC5kB,GAAG,CAAEwR,CAAC,KAAM;QAAEzV,EAAE,EAAEyV,CAAC,CAACzV,EAAE;QAAEyT,QAAQ,EAAEgC,CAAC,CAAChC;MAAQ,CAAE,CAAC;KACrE,CAAC;IAEF,IAAIoV,KAAK,CAACjmB,MAAM,GAAG,CAAC,EAAE;MACpBd,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC5D,OAAO,CAAC;IACV;IAEA;IACA,IAAI,CAAC1G,WAAW,GAAG,EAAE;IACrByG,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;IAExC;IACAD,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;IACzC,KAAK,IAAI4B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGklB,KAAK,CAACjmB,MAAM,GAAG,CAAC,EAAEe,CAAC,EAAE,EAAE;MACzC,MAAM6kB,QAAQ,GAAGK,KAAK,CAACllB,CAAC,CAAC,CAAC3D,EAAE;MAC5B,MAAMyoB,QAAQ,GAAGI,KAAK,CAACllB,CAAC,GAAG,CAAC,CAAC,CAAC3D,EAAE;MAChC8B,OAAO,CAACC,GAAG,CAAC,2BAA2BymB,QAAQ,OAAOC,QAAQ,EAAE,CAAC;MACjE,IAAI,CAACF,gBAAgB,CAACC,QAAQ,EAAEC,QAAQ,CAAC;IAC3C;IAEA3mB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC1G,WAAW,CAAC;IAEhD;IACA,IAAI,CAACxB,GAAG,CAAC6I,aAAa,EAAE;IACxBZ,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;EACjD;EAEA;EACQykB,kBAAkBA,CAAA;IACxB;EAAA;EAGF;EACAwC,kBAAkBA,CAACxf,IAAgB;IACjC,IAAI,CAAC,IAAI,CAACzL,aAAa,EAAE,OAAO8S,SAAS;IAEzC;IACA,MAAMoY,QAAQ,GAAGzf,IAAI,CAAC4B,IAAI;IAC1B,IAAI,CAAC6d,QAAQ,EAAE,OAAOpY,SAAS;IAE/B,OAAO;MACL5Q,IAAI,EAAEgpB,QAAQ,CAAChpB,IAAI;MACnB4oB,KAAK,EAAE,CAACI,QAAQ,CAAC;MAAE;MACnBxV,QAAQ,EAAEwV,QAAQ,CAACxV,QAAQ,IAAIjK,IAAI,CAACiK;KACrC;EACH;EAEA;EACAM,cAAcA,CAAA;IACZ,OAAO,QAAQ3H,IAAI,CAACyV,GAAG,EAAE,IAAI1d,IAAI,CAAC0S,KAAK,CAAC1S,IAAI,CAACC,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE;EACjE;EAEA;EACQjC,aAAaA,CAACoc,OAAe;IACnC;IACA,IAAI,IAAI,CAAC9iB,gBAAgB,KAAK,eAAe,EAAE;MAC7C,IAAI,CAACytB,wBAAwB,CAAC3K,OAAO,CAAC;MACtC;IACF;IAEA;IACA,IAAI,CAAC,IAAI,CAAClf,WAAW,EAAE;MACrB,IAAI,CAACtF,YAAY,CAACwY,SAAS,EAAE,CAAC9Q,SAAS,CAAC;QACtCwB,IAAI,EAAGmC,QAAa,IAAI;UACtB,IAAI,CAAC/F,WAAW,GAAG+F,QAAQ;UAE3B;UACA,IAAI,CAACO,mBAAmB,EAAE;UAC1B,IAAI,CAACyB,0BAA0B,EAAE;UACjC,IAAI,CAACuB,uBAAuB,EAAE;UAE9B;UACA,IAAI,CAACugB,wBAAwB,CAAC3K,OAAO,CAAC;QACxC,CAAC;QACDzZ,KAAK,EAAGA,KAAK,IAAI;UACfhD,OAAO,CAACgD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;UAC/D;UACA,IAAI,CAACokB,wBAAwB,CAAC3K,OAAO,CAAC;QACxC;OACD,CAAC;MACF;IACF;IAEA,IAAI,CAAC2K,wBAAwB,CAAC3K,OAAO,CAAC;EACxC;EAEA;EACQ2K,wBAAwBA,CAAC3K,OAAe;IAC9C,IAAI,IAAI,CAAC9iB,gBAAgB,KAAK,eAAe,EAAE;MAC7C,IAAI,CAAC1B,YAAY,CAACovB,gCAAgC,CAAC5K,OAAO,CAAC,CAAC9c,SAAS,CAAC;QACpEwB,IAAI,EAAGmC,QAAQ,IAAI;UACjB;UACA,IAAIkgB,SAAS,GAAG,IAAI;UACpB,IAAIlgB,QAAQ,IAAIA,QAAQ,CAACxG,WAAW,EAAE;YACpC;YACA0mB,SAAS,GAAGlgB,QAAQ,CAACxG,WAAW;UAClC,CAAC,MAAM,IACLwG,QAAQ,KACPA,QAAQ,CAACwb,YAAY,IAAIxb,QAAQ,CAACxG,WAAW,CAAC,EAC/C;YACA;YACA0mB,SAAS,GAAGlgB,QAAQ,CAACwb,YAAY,IAAIxb,QAAQ,CAACxG,WAAW;UAC3D,CAAC,MAAM,IAAIwG,QAAQ,IAAIA,QAAQ,CAACgG,IAAI,EAAE;YACpC;YACAka,SAAS,GAAGlgB,QAAQ,CAACgG,IAAI;UAC3B;UAEA,IAAIka,SAAS,EAAE;YACb;YACA,IAAI,CAAC/oB,mBAAmB,GAAG;cACzB8G,IAAI,EAAEiiB,SAAS,CAACjiB,IAAI,IAAI,eAAe;cACvCO,WAAW,EACT0hB,SAAS,CAAC1hB,WAAW,IAAI0hB,SAAS,CAAC1E,YAAY,IAAI,EAAE;cACvDpc,IAAI,EAAE8gB,SAAS,CAAC9gB,IAAI,IAAI,EAAE;cAC1BC,IAAI,EAAE6gB,SAAS,CAAC7gB,IAAI,IAAI,EAAE;cAC1BC,SAAS,EAAE4gB,SAAS,CAAC5gB,SAAS,IAAI,EAAE;cACpCC,cAAc,EAAE2gB,SAAS,CAAC3gB,cAAc,IAAI;aAC7C;YAED;YACA,MAAMykB,cAAc,GAAG;cACrBxqB,WAAW,EAAE0mB,SAAS;cACtB1E,YAAY,EAAE0E,SAAS,CAAE;aAC1B;YACD,IAAI,CAAC+D,iCAAiC,CAACD,cAAc,CAAC;UACxD,CAAC,MAAM;YACLtnB,OAAO,CAACqN,IAAI,CACV,gDAAgD,EAChD/J,QAAQ,CACT;YACD,IAAI,CAACkO,gBAAgB,CACnB,iBAAiB,EACjB,6FAA6F,CAC9F;UACH;QACF,CAAC;QACDxO,KAAK,EAAGA,KAAK,IAAI;UACfhD,OAAO,CAACgD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;UAC/D,IAAI,CAACwO,gBAAgB,CACnB,aAAa,EACb,gFAAgF,CACjF;QACH;OACD,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACvZ,YAAY,CAACuvB,YAAY,CAAC/K,OAAO,CAAC,CAAC9c,SAAS,CAAC;QAChDwB,IAAI,EAAGmC,QAAQ,IAAI;UACjB,IAAIA,QAAQ,KAAKA,QAAQ,CAACmgB,WAAW,IAAIngB,QAAQ,CAAC/B,IAAI,CAAC,EAAE;YACvD;YACA,IAAI,CAAC9G,mBAAmB,GAAG;cACzB8G,IAAI,EAAE+B,QAAQ,CAAC/B,IAAI,IAAI+B,QAAQ,CAACmgB,WAAW,IAAI,eAAe;cAC9D3hB,WAAW,EACTwB,QAAQ,CAACxB,WAAW,IAAIwB,QAAQ,CAACmkB,kBAAkB,IAAI,EAAE;cAC3D/kB,IAAI,EAAEY,QAAQ,CAACZ,IAAI,IAAI,EAAE;cACzBC,IAAI,EAAEW,QAAQ,CAACX,IAAI,IAAI,EAAE;cACzBC,SAAS,EAAEU,QAAQ,CAACV,SAAS,IAAI,EAAE;cACnCC,cAAc,EAAES,QAAQ,CAACT,cAAc,IAAI,EAAE;cAC7C;cACAgX,WAAW,EAAEvW,QAAQ,CAACuW,WAAW,IAAIvW,QAAQ,CAACsD,IAAI,IAAI,EAAE;cACxD6c,WAAW,EAAEngB,QAAQ,CAACmgB,WAAW,IAAIngB,QAAQ,CAAC/B,IAAI,IAAI,EAAE;cACxDib,SAAS,EAAElZ,QAAQ,CAACkZ,SAAS,IAAIlZ,QAAQ,CAACpF,EAAE,IAAI,EAAE;cAClDkY,gBAAgB,EAAE9S,QAAQ,CAAC8S,gBAAgB,IAAI,EAAE;cACjD;cACA8C,MAAM,EAAE5V,QAAQ,CAAC4V,MAAM,IAAI;aAC5B;YAED,IAAI,CAACwO,oBAAoB,CAACpkB,QAAQ,CAAC;UACrC,CAAC,MAAM;YACLtD,OAAO,CAACqN,IAAI,CACV,6CAA6C,EAC7C/J,QAAQ,CACT;YACD,IAAI,CAACkO,gBAAgB,CACnB,iBAAiB,EACjB,6FAA6F,CAC9F;UACH;QACF,CAAC;QACDxO,KAAK,EAAGA,KAAK,IAAI;UACfhD,OAAO,CAACgD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;UAC5D,IAAI,CAACwO,gBAAgB,CACnB,aAAa,EACb,6EAA6E,CAC9E;QACH;OACD,CAAC;IACJ;EACF;EAEA;EACQkW,oBAAoBA,CAAClE,SAAc;IACzC,IAAI,CAACA,SAAS,EAAE;MACdxjB,OAAO,CAACgD,KAAK,CAAC,mCAAmC,CAAC;MAClD;IACF;IAEA;IACA;IACA,IAAI,IAAI,CAACjJ,eAAe,EAAE;MACxB,IAAI,CAAC8C,SAAS,GAAG,EAAE,CAAC,CAAC;IACvB,CAAC,MAAM;MACL,IAAI2mB,SAAS,CAACC,WAAW,EAAE;QACzB,IAAI,CAAC5mB,SAAS,GAAG2mB,SAAS,CAACC,WAAW;MACxC,CAAC,MAAM,IAAID,SAAS,CAACjiB,IAAI,EAAE;QACzB,IAAI,CAAC1E,SAAS,GAAG2mB,SAAS,CAACjiB,IAAI;MACjC;IACF;IAEA;IACA,IAAIiiB,SAAS,CAAC3J,WAAW,EAAE;MACzB,IAAI,CAAC9c,SAAS,GAAGymB,SAAS,CAAC3J,WAAW;IACxC,CAAC,MAAM,IAAI2J,SAAS,CAAC5c,IAAI,EAAE;MACzB,IAAI,CAAC7J,SAAS,GAAGymB,SAAS,CAAC5c,IAAI;IACjC,CAAC,MAAM;MACL;MACA,IAAI,CAAC7J,SAAS,GAAG,IAAI,CAACF,SAAS,CAACsG,IAAI,EAAE,CAAC+J,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACkC,WAAW,EAAE;IAC3E;IAEA,IAAIoU,SAAS,CAACvJ,cAAc,EAAE;MAC5B,IAAI,CAACnd,WAAW,GAAG0mB,SAAS,CAACvJ,cAAc;IAC7C,CAAC,MAAM,IAAIuJ,SAAS,CAAC1hB,WAAW,EAAE;MAChC,IAAI,CAAChF,WAAW,GAAG0mB,SAAS,CAAC1hB,WAAW;IAC1C,CAAC,MAAM,IAAI0hB,SAAS,CAAC1mB,WAAW,EAAE;MAChC,IAAI,CAACA,WAAW,GAAG0mB,SAAS,CAAC1mB,WAAW;IAC1C;IAEA;IACA,IAAI0mB,SAAS,CAACpN,gBAAgB,EAAE;MAC9B,MAAMuR,SAAS,GAAGnE,SAAS,CAACpN,gBAAgB,CAACO,KAAK,CAAC,GAAG,CAAC;MACvD,IAAIgR,SAAS,CAAC7mB,MAAM,IAAI,CAAC,EAAE;QACzB,IAAI,CAAC7D,aAAa,GAAG;UACnBC,GAAG,EAAEyqB,SAAS,CAAC,CAAC,CAAC,IAAInE,SAAS,CAACtmB,GAAG,IAAI,EAAE;UACxCC,MAAM,EAAEwqB,SAAS,CAAC,CAAC,CAAC,IAAInE,SAAS,CAACrmB,MAAM,IAAI,EAAE;UAC9CC,OAAO,EAAEuqB,SAAS,CAAC,CAAC,CAAC,IAAInE,SAAS,CAACpmB,OAAO,IAAI,EAAE;UAChDC,IAAI,EAAEsqB,SAAS,CAAC,CAAC,CAAC,IAAInE,SAAS,CAACnmB,IAAI,IAAI;SACzC;MACH;IACF,CAAC,MAAM;MACL,IAAI,CAACJ,aAAa,GAAG;QACnBC,GAAG,EAAEsmB,SAAS,CAACtmB,GAAG,IAAI,EAAE;QACxBC,MAAM,EAAEqmB,SAAS,CAACrmB,MAAM,IAAI,EAAE;QAC9BC,OAAO,EAAEomB,SAAS,CAACpmB,OAAO,IAAI,EAAE;QAChCC,IAAI,EAAEmmB,SAAS,CAACnmB,IAAI,IAAI;OACzB;IACH;IAEA;IACA;IACA,MAAMuqB,WAAW,GAA4C;MAC3DhtB,MAAM,EAAE,EAAE;MACVC,KAAK,EAAE,EAAE;MACTC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE,EAAE;MACbE,IAAI,EAAE;KACP;IACD,IAAI4sB,WAAW,GAAG,CAAC;IAEnB,IAAIrE,SAAS,CAAC5oB,MAAM,IAAI4oB,SAAS,CAACvJ,cAAc,IAAIuJ,SAAS,CAACC,WAAW,EAAE;MACzE,IAAIqE,UAAU,GAAGtE,SAAS,CAAC5oB,MAAM,IAAI,gBAAgB;MAErD;MACA,IAAIgf,UAAU,GAAG,IAAI,CAAC7d,YAAY,CAAC,SAAS,CAAC,EAAEiI,IAAI,CAChD2W,CAAC,IACAA,CAAC,CAACpZ,IAAI,KAAKumB,UAAU,IACrBnN,CAAC,CAACpZ,IAAI,CAACsG,WAAW,EAAE,KAAKigB,UAAU,CAACjgB,WAAW,EAAE,CACpD;MAED;MACA,IAAI,CAAC+R,UAAU,IAAI,IAAI,CAAC7d,YAAY,CAAC,SAAS,CAAC,EAAE+E,MAAM,GAAG,CAAC,EAAE;QAC3D8Y,UAAU,GAAG,IAAI,CAAC7d,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAC5C+rB,UAAU,GAAGlO,UAAU,CAACrY,IAAI,CAAC,CAAC;MAChC;MAEA;MACA,MAAM+X,UAAU,GAAuB;QACrCpb,EAAE,EAAE,UAAU2pB,WAAW,EAAE,EAAE;QAC7BtmB,IAAI,EAAEumB,UAAU;QAChB3pB,IAAI,EAAE,QAAQ;QACdwT,QAAQ,EAAE;UAAEsC,CAAC,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAC,CAAE,CAAE;OAC3B;MACD0T,WAAW,CAAC,QAAQ,CAAC,CAAC5Y,IAAI,CAACsK,UAAU,CAAC;IACxC;IAEA;IACA,IAAIkK,SAAS,CAACtK,MAAM,IAAIxX,KAAK,CAACuB,OAAO,CAACugB,SAAS,CAACtK,MAAM,CAAC,EAAE;MACvDsK,SAAS,CAACtK,MAAM,CAACrK,OAAO,CAAE5K,QAAa,IAAI;QACzC,IAAIA,QAAQ,CAACiV,MAAM,IAAIxX,KAAK,CAACuB,OAAO,CAACgB,QAAQ,CAACiV,MAAM,CAAC,EAAE;UACrDjV,QAAQ,CAACiV,MAAM,CAACrK,OAAO,CAAEkZ,UAAe,IAAI;YAC1C;YACA,IAAIA,UAAU,CAAC7I,QAAQ,EAAE;cACvB,MAAMtS,SAAS,GAAG,GAAG3I,QAAQ,CAACC,UAAU,IAAI6jB,UAAU,CAACnb,SAAS,EAAE;cAClE,IAAI,CAAClQ,cAAc,CAACsrB,GAAG,CAACpb,SAAS,EAAEmb,UAAU,CAAC7I,QAAQ,CAAC;YACzD;YACA;YACA,IAAI6I,UAAU,CAACnb,SAAS,KAAK,OAAO,IAAImb,UAAU,CAACE,WAAW,EAAE;cAC9D,MAAMC,OAAO,GAAGH,UAAU,CAACE,WAAW;cAEtC,MAAMxd,SAAS,GAAG,IAAI,CAAC1O,YAAY,CAAC,QAAQ,CAAC,EAAEiI,IAAI,CAChDmZ,CAAC,IACAA,CAAC,CAACjf,EAAE,KAAKgqB,OAAO,IAChB/K,CAAC,CAACjf,EAAE,KAAKgqB,OAAO,CAAC9lB,QAAQ,EAAE,IAC3B+a,CAAC,CAACjf,EAAE,CAACkE,QAAQ,EAAE,KAAK8lB,OAAO,CAC9B;cAED,IAAIzd,SAAS,EAAE;gBACbmd,WAAW,CAAC,OAAO,CAAC,CAAC5Y,IAAI,CAAC;kBACxB9Q,EAAE,EAAE,SAAS2pB,WAAW,EAAE,EAAE;kBAC5B1pB,IAAI,EAAE,OAAO;kBACboD,IAAI,EAAEkJ,SAAS,CAAClJ,IAAI;kBACpBC,IAAI,EAAEuN,SAAS;kBAAE;kBACjB4C,QAAQ,EAAE;oBAAEsC,CAAC,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAC,CAAE;kBAAE;kBAC1BvM,gBAAgB,EAAE8C,SAAS,CAAE;iBAC9B,CAAC;cACJ,CAAC,MAAM;gBACL;gBACAmd,WAAW,CAAC,OAAO,CAAC,CAAC5Y,IAAI,CAAC;kBACxB9Q,EAAE,EAAE,SAAS2pB,WAAW,EAAE,EAAE;kBAC5B1pB,IAAI,EAAE,OAAO;kBACboD,IAAI,EAAE,aAAa2mB,OAAO,EAAE;kBAC5B1mB,IAAI,EAAEuN,SAAS;kBAAE;kBACjB4C,QAAQ,EAAE;oBAAEsC,CAAC,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAC,CAAE;kBAAE;kBAC1BvM,gBAAgB,EAAE;oBAChBzJ,EAAE,EAAEgqB,OAAO;oBACX3mB,IAAI,EAAE,aAAa2mB,OAAO;mBAC3B,CAAE;iBACJ,CAAC;cACJ;YACF;YAEA;YACA,IACEH,UAAU,CAACnb,SAAS,KAAK,wBAAwB,IACjDmb,UAAU,CAACE,WAAW,EACtB;cACA,MAAME,OAAO,GAAGJ,UAAU,CAACE,WAAW,CAAC7lB,QAAQ,EAAE,CAAC,CAAC;cACnD,MAAMgmB,KAAK,GAAGD,OAAO,CAClBxR,KAAK,CAAC,GAAG,CAAC,CACVxU,GAAG,CAAEjE,EAAU,IAAKA,EAAE,CAACiF,IAAI,EAAE,CAAC,CAC9BD,MAAM,CAAEhF,EAAU,IAAKA,EAAE,CAAC,CAAC,CAAC;cAE/B;cACAkqB,KAAK,CAACvZ,OAAO,CAAEwZ,IAAY,IAAI;gBAC7B,MAAMlQ,aAAa,GAAG,IAAI,CAACpc,YAAY,CAAC,WAAW,CAAC,EAAEiI,IAAI,CACvDqZ,CAAC,IAAI;kBACJ,OACEA,CAAC,CAACnf,EAAE,KAAKmqB,IAAI,IACbhL,CAAC,CAACnf,EAAE,KAAKmqB,IAAI,CAACjmB,QAAQ,EAAE,IACxBib,CAAC,CAACnf,EAAE,CAACkE,QAAQ,EAAE,KAAKimB,IAAI,IACxBhL,CAAC,CAAC9b,IAAI,KAAK8mB,IAAI,IACfhL,CAAC,CAAC9b,IAAI,CAACsG,WAAW,EAAE,KAAKwgB,IAAI,CAACxgB,WAAW,EAAE;gBAE/C,CAAC,CACF;gBAED,IAAIsQ,aAAa,EAAE;kBACjByP,WAAW,CAAC,WAAW,CAAC,CAAC5Y,IAAI,CAAC;oBAC5B9Q,EAAE,EAAE,aAAa2pB,WAAW,EAAE,EAAE;oBAChC1pB,IAAI,EAAE,WAAW;oBACjBoD,IAAI,EAAE4W,aAAa,CAAC5W,IAAI;oBACxBC,IAAI,EAAEuN,SAAS;oBAAE;oBACjB4C,QAAQ,EAAE;sBAAEsC,CAAC,EAAE,CAAC;sBAAEC,CAAC,EAAE;oBAAC,CAAE;oBAAE;oBAC1BvM,gBAAgB,EAAEwQ,aAAa,CAAE;mBAClC,CAAC;gBACJ,CAAC,MAAM;kBACL;kBACA;kBACA,IAAImQ,MAAM,GAAG,sBAAsBD,IAAI,EAAE;kBACzC,IAAI,IAAI,CAAC9qB,WAAW,IAAI,IAAI,CAACA,WAAW,CAACuG,cAAc,EAAE;oBACvD,MAAMyB,WAAW,GAAG,IAAI,CAAChI,WAAW,CAACuG,cAAc,CAACE,IAAI,CACrDukB,GAAQ,IAAKA,GAAG,CAACrkB,UAAU,KAAK,CAAC,CACnC;oBACD,IAAIqB,WAAW,EAAE;sBACf,MAAMijB,OAAO,GAAGjjB,WAAW,CAACnB,MAAM,CAACJ,IAAI,CACpCK,KAAU,IACTA,KAAK,CAACC,SAAS,KAAK,wBAAwB,CAC/C;sBACD,IAAIkkB,OAAO,IAAIA,OAAO,CAACjkB,WAAW,EAAE;wBAClC,MAAMkkB,SAAS,GAAG,IAAI,CAACxwB,YAAY,CAACwM,gBAAgB,CAClD+jB,OAAO,CAACjkB,WAAW,CACpB;wBACD,MAAMmkB,QAAQ,GAAGD,SAAS,CAACzkB,IAAI,CAC5B2kB,GAAG,IAAKA,GAAG,CAACloB,KAAK,KAAK4nB,IAAI,CAC5B;wBACD,IAAIK,QAAQ,EAAE;0BACZJ,MAAM,GAAGI,QAAQ,CAACnnB,IAAI;wBACxB;sBACF;oBACF;kBACF;kBAEAqmB,WAAW,CAAC,WAAW,CAAC,CAAC5Y,IAAI,CAAC;oBAC5B9Q,EAAE,EAAE,aAAa2pB,WAAW,EAAE,EAAE;oBAChC1pB,IAAI,EAAE,WAAW;oBACjBoD,IAAI,EAAE+mB,MAAM;oBACZ9mB,IAAI,EAAEuN,SAAS;oBAAE;oBACjB4C,QAAQ,EAAE;sBAAEsC,CAAC,EAAE,CAAC;sBAAEC,CAAC,EAAE;oBAAC,CAAE;oBAAE;oBAC1BvM,gBAAgB,EAAE;sBAAEzJ,EAAE,EAAEmqB,IAAI;sBAAE9mB,IAAI,EAAE+mB;oBAAM,CAAE,CAAE;mBAC/C,CAAC;gBACJ;cACF,CAAC,CAAC;YACJ;YAEA;YACA,IACEP,UAAU,CAACnb,SAAS,KAAK,mBAAmB,KAC3Cmb,UAAU,CAACE,WAAW,KAAK,MAAM,IAChCF,UAAU,CAACE,WAAW,KAAK,IAAI,CAAC,EAClC;cACA;cACA,IAAIL,WAAW,CAAC,WAAW,CAAC,CAAC9mB,MAAM,KAAK,CAAC,EAAE;gBACzC8mB,WAAW,CAAC,WAAW,CAAC,CAAC5Y,IAAI,CAAC;kBAC5B9Q,EAAE,EAAE,aAAa2pB,WAAW,EAAE,EAAE;kBAChC1pB,IAAI,EAAE,WAAW;kBACjBoD,IAAI,EAAE,YAAY;kBAClBC,IAAI,EAAEuN,SAAS;kBAAE;kBACjB4C,QAAQ,EAAE;oBAAEsC,CAAC,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAC,CAAE,CAAE;iBAC3B,CAAC;cACJ;YACF;YAEA;YACA,IACE6T,UAAU,CAACnb,SAAS,IACpBmb,UAAU,CAACnb,SAAS,CAACI,UAAU,CAAC,YAAY,CAAC,IAC7C+a,UAAU,CAACnb,SAAS,KAAK,mBAAmB,KAC3Cmb,UAAU,CAACE,WAAW,KAAK,MAAM,IAChCF,UAAU,CAACE,WAAW,KAAK,IAAI,CAAC,EAClC;cACA,MAAMpP,aAAa,GAAG,IAAI,CAAC9c,YAAY,CAAC,YAAY,CAAC,EAAEiI,IAAI,CACxD4kB,CAAC,IAAMA,CAAS,CAAChiB,IAAI,KAAKmhB,UAAU,CAACnb,SAAS,CAChD;cAED,IAAIiM,aAAa,EAAE;gBACjB+O,WAAW,CAAC,WAAW,CAAC,CAAC5Y,IAAI,CAAC;kBAC5B9Q,EAAE,EAAE,aAAa2pB,WAAW,EAAE,EAAE;kBAChC1pB,IAAI,EAAE,WAAW;kBACjBoD,IAAI,EAAEsX,aAAa,CAACtX,IAAI;kBACxBC,IAAI,EAAEuN,SAAS;kBAAE;kBACjB4C,QAAQ,EAAE;oBAAEsC,CAAC,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAC,CAAE,CAAE;iBAC3B,CAAC;cACJ,CAAC,MAAM;gBACL;gBACA,MAAM2U,aAAa,GAAGd,UAAU,CAACnb,SAAS,CACvCM,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CACzBA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;gBACrB0a,WAAW,CAAC,WAAW,CAAC,CAAC5Y,IAAI,CAAC;kBAC5B9Q,EAAE,EAAE,aAAa2pB,WAAW,EAAE,EAAE;kBAChC1pB,IAAI,EAAE,WAAW;kBACjBoD,IAAI,EAAEsnB,aAAa;kBACnBrnB,IAAI,EAAEuN,SAAS;kBAAE;kBACjB4C,QAAQ,EAAE;oBAAEsC,CAAC,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAC,CAAE,CAAE;iBAC3B,CAAC;cACJ;YACF;UACF,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ;IAEA;IACA,MAAM6S,KAAK,GAAyB,EAAE;IACtC,MAAM+B,SAAS,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,CAAC;IAEvE;IACA,IAAI,CAACtvB,eAAe,GAAG,EAAE;IACzB,IAAI,CAAC0B,YAAY,GAAG,IAAI;IAExB8E,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAE;MAC3D1D,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BC,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BP,aAAa,EAAE,IAAI,CAACA,aAAa;MACjClC,eAAe,EAAE,IAAI,CAACA,eAAe;MACrCgvB,eAAe,EAAEpa,MAAM,CAACC,IAAI,CAACgZ,WAAW,CAAC,CAAC3b,MAAM,CAC9C,CAAC+c,KAAK,EAAE7qB,IAAI,KAAK6qB,KAAK,GAAGpB,WAAW,CAACzpB,IAAI,CAAC,CAAC2C,MAAM,EACjD,CAAC;KAEJ,CAAC;IAEFgoB,SAAS,CAACja,OAAO,CAAElG,QAAQ,IAAI;MAC7Bif,WAAW,CAACjf,QAAQ,CAAC,CAACkG,OAAO,CAAEnH,IAAI,IAAI;QACrC1H,OAAO,CAACC,GAAG,CACT,sBAAsB8mB,KAAK,CAACjmB,MAAM,GAAG,CAAC,KAAK4G,IAAI,CAACnG,IAAI,KAAKmG,IAAI,CAACvJ,IAAI,GAAG,CACtE;QAED;QACA,IAAI,IAAI,CAAC5B,UAAU,EAAE;UACnB;UACA,MAAMyoB,SAAS,GAAG+B,KAAK,CAACjmB,MAAM,CAAC,CAAC;UAChCd,OAAO,CAACC,GAAG,CACT,iDAAiD+kB,SAAS,EAAE,CAC7D;UACDtd,IAAI,CAACiK,QAAQ,GAAG,IAAI,CAACsX,yBAAyB,CAACjE,SAAS,CAAC;QAC3D,CAAC,MAAM;UACL;UACAhlB,OAAO,CAACC,GAAG,CACT,0DAA0D,CAC3D;UACDyH,IAAI,CAACiK,QAAQ,GAAG,IAAI,CAACwT,+BAA+B,EAAE;QACxD;QAEAnlB,OAAO,CAACC,GAAG,CAAC,2BAA2B8Q,IAAI,CAACC,SAAS,CAACtJ,IAAI,CAACiK,QAAQ,CAAC,EAAE,CAAC;QAEvEoV,KAAK,CAAC/X,IAAI,CAACtH,IAAI,CAAC;QAChB;QACA,IAAI,CAAClO,eAAe,CAACwV,IAAI,CAACtH,IAAI,CAAC;QAC/B,IAAI,CAACxM,YAAY,GAAGwM,IAAI,CAACxJ,EAAE;MAC7B,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAAC1E,eAAe,GAAGutB,KAAK;IAE5B;IACA,IAAI,CAACztB,WAAW,GAAGytB,KAAK,CAAC5kB,GAAG,CAAEuF,IAAI,KAAM;MACtCxJ,EAAE,EAAEwJ,IAAI,CAACxJ,EAAE;MACXC,IAAI,EAAE,aAAa;MACnBmL,IAAI,EAAE;QAAE,GAAG5B,IAAI;QAAE2K,KAAK,EAAE,IAAI,CAACpW,aAAa,GAAG,EAAE,GAAG;MAAG,CAAE;MACvD0V,QAAQ,EAAEjK,IAAI,CAACiK;KAChB,CAAC,CAAC;IAEH;IACA,IAAI,CAAC5Z,GAAG,CAAC6I,aAAa,EAAE;IAExB;IACAC,UAAU,CAAC,MAAK;MACd;MACA,IAAI,CAACtH,WAAW,GAAG,EAAE;MAErB;MACA,IAAI,CAACyH,0BAA0B,CAAC+lB,KAAK,CAAC;MAEtC;MACA,IAAI,IAAI,CAACpuB,oBAAoB,EAAE;QAC7B,IAAI,CAACA,oBAAoB,CAACoI,0BAA0B,EAAE;MACxD;MAEA;MACA,IAAI,CAAChJ,GAAG,CAAC6I,aAAa,EAAE;IAC1B,CAAC,EAAE,GAAG,CAAC;IAEP;IACA,IAAI,IAAI,CAAC7G,eAAe,EAAE;MACxB,IAAI,CAACF,cAAc,GAAG,IAAI;IAC5B;IAEA;IACA,IAAI,IAAI,CAACoC,aAAa,EAAE;MACtB;MACA,IAAI,CAACitB,gCAAgC,EAAE;MACvCroB,UAAU,CAAC,MAAK;QACd,IAAI,CAACsoB,qBAAqB,EAAE;MAC9B,CAAC,EAAE,GAAG,CAAC;IACT;IAEA;IACA,IAAI,IAAI,CAAC5sB,UAAU,EAAE;MACnBsE,UAAU,CAAC,MAAK;QACd,IAAI,CAACuoB,2BAA2B,EAAE;MACpC,CAAC,EAAE,GAAG,CAAC;IACT;EACF;EAEA;EACQ7B,iCAAiCA,CAACjkB,QAAa;IACrD;IACA,IAAIkgB,SAAS;IACb,IACElgB,QAAQ,CAACwb,YAAY,IACrBpd,KAAK,CAACuB,OAAO,CAACK,QAAQ,CAACwb,YAAY,CAAC,IACpCxb,QAAQ,CAACwb,YAAY,CAAChe,MAAM,GAAG,CAAC,EAChC;MACA0iB,SAAS,GAAGlgB,QAAQ,CAACwb,YAAY,CAAC,CAAC,CAAC;IACtC,CAAC,MAAM,IAAIxb,QAAQ,CAACxG,WAAW,EAAE;MAC/B0mB,SAAS,GAAGlgB,QAAQ,CAACxG,WAAW;IAClC,CAAC,MAAM,IAAIwG,QAAQ,CAACgG,IAAI,EAAE;MACxBka,SAAS,GAAGlgB,QAAQ,CAACgG,IAAI;IAC3B,CAAC,MAAM;MACLka,SAAS,GAAGlgB,QAAQ;IACtB;IAEA,IAAI,CAACkgB,SAAS,EAAE;MACdxjB,OAAO,CAACgD,KAAK,CAAC,iCAAiC,CAAC;MAChD;IACF;IAEA;IACA;IACA,IAAI,IAAI,CAACjJ,eAAe,EAAE;MACxB,IAAI,CAAC8C,SAAS,GAAG,EAAE,CAAC,CAAC;IACvB,CAAC,MAAM;MACL,IAAI2mB,SAAS,CAACjiB,IAAI,EAAE;QAClB,IAAI,CAAC1E,SAAS,GAAG2mB,SAAS,CAACjiB,IAAI;MACjC;IACF;IAEA;IACA,IAAIiiB,SAAS,CAAC5c,IAAI,EAAE;MAClB,IAAI,CAAC7J,SAAS,GAAGymB,SAAS,CAAC5c,IAAI;IACjC,CAAC,MAAM,IAAI4c,SAAS,CAAC3J,WAAW,EAAE;MAChC,IAAI,CAAC9c,SAAS,GAAGymB,SAAS,CAAC3J,WAAW;IACxC,CAAC,MAAM;MACL;MACA,IAAI,CAAC9c,SAAS,GAAG,IAAI,CAACF,SAAS,CAACsG,IAAI,EAAE,CAAC+J,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACkC,WAAW,EAAE;IAC3E;IAEA;IACA,IAAIoU,SAAS,CAAC1E,YAAY,EAAE;MAC1B,IAAI,CAAChiB,WAAW,GAAG0mB,SAAS,CAAC1E,YAAY;IAC3C,CAAC,MAAM,IAAI0E,SAAS,CAAC1mB,WAAW,EAAE;MAChC,IAAI,CAACA,WAAW,GAAG0mB,SAAS,CAAC1mB,WAAW;IAC1C,CAAC,MAAM,IAAI0mB,SAAS,CAAC1hB,WAAW,EAAE;MAChC,IAAI,CAAChF,WAAW,GAAG0mB,SAAS,CAAC1hB,WAAW;IAC1C;IAEA;IACA,IAAI,CAAC7E,aAAa,GAAG;MACnBC,GAAG,EAAEsmB,SAAS,CAACtmB,GAAG,IAAI,EAAE;MACxBC,MAAM,EAAEqmB,SAAS,CAACrmB,MAAM,IAAI,EAAE;MAC9BC,OAAO,EAAEomB,SAAS,CAACpmB,OAAO,IAAI,EAAE;MAChCC,IAAI,EAAEmmB,SAAS,CAACnmB,IAAI,IAAI;KACzB;IAED;IACA;IACA,MAAMuqB,WAAW,GAA4C;MAC3DhtB,MAAM,EAAE,EAAE;MACVC,KAAK,EAAE,EAAE;MACTC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE,EAAE;MACbE,IAAI,EAAE;KACP;IACD,IAAI4sB,WAAW,GAAG,CAAC;IAEnB;IACA,MAAMwB,sBAAsB,GACzB,IAAI,CAAC1vB,gBAAgB,KAAK,YAAY,IAAI6pB,SAAS,CAAC5oB,MAAM,IAC1D,IAAI,CAACjB,gBAAgB,KAAK,eAAe,KACvC6pB,SAAS,CAAC7gB,IAAI,IAAI6gB,SAAS,CAAC9gB,IAAI,IAAI8gB,SAAS,CAAC1hB,WAAW,CAAE;IAEhE,IAAIunB,sBAAsB,EAAE;MAC1B;MACA,IAAIzP,UAAU,GACZ,IAAI,CAAC7d,YAAY,CAAC,SAAS,CAAC,EAAEiI,IAAI,CAC/B2W,CAAC,IAAMA,CAAS,CAAClY,UAAU,KAAK,WAAW,CAC7C,IAAI,IAAI,CAAC1G,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;MAExC;MACA,IAAI,CAAC6d,UAAU,EAAE;QACfA,UAAU,GAAG;UACX1b,EAAE,EAAE,gBAAgB;UACpBqD,IAAI,EAAE,gBAAgB;UACtBpD,IAAI,EAAE,QAAQ;UACdqD,IAAI,EAAE,IAAI,CAAC+M,cAAc,CAAC,QAAQ,CAAC;UACnCzM,WAAW,EAAE;SACd;MACH;MAEA;MACA,IAAIwnB,cAAc;MAClB,IAAIC,cAAc;MAElB,IAAI,IAAI,CAAC5vB,gBAAgB,KAAK,eAAe,EAAE;QAC7C;QACA2vB,cAAc,GACZ9F,SAAS,CAAC7gB,IAAI,IACd6gB,SAAS,CAAC9gB,IAAI,IACd8gB,SAAS,CAAC1hB,WAAW,IACrB,4BAA4B;QAE9B;QACA,IAAIwnB,cAAc,CAACxoB,MAAM,GAAG,GAAG,EAAE;UAC/BwoB,cAAc,GAAGA,cAAc,CAACE,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK;QAC3D;QAEAD,cAAc,GAAG;UACf,GAAG3P,UAAU;UACb;UACAjX,IAAI,EAAE6gB,SAAS,CAAC7gB,IAAI;UACpBD,IAAI,EAAE8gB,SAAS,CAAC9gB,IAAI;UACpBZ,WAAW,EAAE0hB,SAAS,CAAC1hB,WAAW;UAClCe,cAAc,EAAE2gB,SAAS,CAAC3gB,cAAc;UACxCD,SAAS,EAAE4gB,SAAS,CAAC5gB,SAAS;UAC9BxH,SAAS,EAAE;SACZ;MACH,CAAC,MAAM;QACL;QACAkuB,cAAc,GAAG9F,SAAS,CAAC5oB,MAAM,IAAI,yBAAyB;QAE9D;QACA,IAAI0uB,cAAc,CAACxoB,MAAM,GAAG,GAAG,EAAE;UAC/BwoB,cAAc,GAAGA,cAAc,CAACE,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK;QAC3D;QAEAD,cAAc,GAAG;UACf,GAAG3P,UAAU;UACb;UACAhf,MAAM,EAAE4oB,SAAS,CAAC5oB,MAAM;UACxB6uB,cAAc,EAAEjG,SAAS,CAAC5oB,MAAM;UAChCQ,SAAS,EAAE;SACZ;MACH;MAEA,MAAMke,UAAU,GAAuB;QACrCpb,EAAE,EAAE,UAAU2pB,WAAW,EAAE,EAAE;QAC7BtmB,IAAI,EAAE+nB,cAAc;QACpBnrB,IAAI,EAAE,QAAQ;QACdqD,IAAI,EAAEoY,UAAU,CAACpY,IAAI,IAAI,IAAI,CAAC+M,cAAc,CAAC,QAAQ,CAAC;QACtDoD,QAAQ,EAAE;UAAEsC,CAAC,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAC,CAAE;QAAE;QAC1BvM,gBAAgB,EAAE4hB;OACnB;MAED3B,WAAW,CAAC,QAAQ,CAAC,CAAC5Y,IAAI,CAACsK,UAAU,CAAC;IACxC,CAAC,MAAM,IAAIkK,SAAS,CAAC1mB,WAAW,IAAI0mB,SAAS,CAACjiB,IAAI,EAAE;MAClD;MACA,MAAMqY,UAAU,GACd,IAAI,CAAC7d,YAAY,CAAC,SAAS,CAAC,EAAEiI,IAAI,CAC/B2W,CAAC,IAAMA,CAAS,CAAClY,UAAU,KAAK,WAAW,CAC7C,IAAI,IAAI,CAAC1G,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;MAExC,IAAI6d,UAAU,EAAE;QACd,IAAI0P,cAAc;QAClB,IAAII,kBAAkB;QAEtB,IAAI,IAAI,CAAC/vB,gBAAgB,KAAK,eAAe,EAAE;UAC7C;UACA2vB,cAAc,GACZ9F,SAAS,CAAC7gB,IAAI,IACd6gB,SAAS,CAAC9gB,IAAI,IACd8gB,SAAS,CAAC1hB,WAAW,IACrB8X,UAAU,CAACrY,IAAI;UACjBmoB,kBAAkB,GAAG;YACnB,GAAG9P,UAAU;YACbjX,IAAI,EAAE6gB,SAAS,CAAC7gB,IAAI;YACpBD,IAAI,EAAE8gB,SAAS,CAAC9gB,IAAI;YACpBZ,WAAW,EAAE0hB,SAAS,CAAC1hB,WAAW;YAClCe,cAAc,EAAE2gB,SAAS,CAAC3gB,cAAc;YACxCzH,SAAS,EAAE;WACZ;QACH,CAAC,MAAM;UACL;UACAkuB,cAAc,GAAG9F,SAAS,CAAC5oB,MAAM,IAAIgf,UAAU,CAACrY,IAAI;UACpDmoB,kBAAkB,GAAG;YACnB,GAAG9P,UAAU;YACbhf,MAAM,EAAE4oB,SAAS,CAAC5oB,MAAM;YACxB6uB,cAAc,EAAEjG,SAAS,CAAC5oB,MAAM;YAChCQ,SAAS,EAAE;WACZ;QACH;QAEA,MAAMke,UAAU,GAAuB;UACrCpb,EAAE,EAAE,UAAU2pB,WAAW,EAAE,EAAE;UAC7BtmB,IAAI,EAAE+nB,cAAc;UACpBnrB,IAAI,EAAE,QAAQ;UACdqD,IAAI,EAAEoY,UAAU,CAACpY,IAAI,IAAI,IAAI,CAAC+M,cAAc,CAAC,QAAQ,CAAC;UACtDoD,QAAQ,EAAE;YAAEsC,CAAC,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAC,CAAE;UAAE;UAC1BvM,gBAAgB,EAAE+hB;SACnB;QACD9B,WAAW,CAAC,QAAQ,CAAC,CAAC5Y,IAAI,CAACsK,UAAU,CAAC;MACxC;IACF;IAEA;IACA,IAAIqQ,eAAe,GAAG,EAAE;IAExB;IACA,IAAInG,SAAS,CAACrF,YAAY,IAAIqF,SAAS,CAACrF,YAAY,CAACnZ,QAAQ,EAAE;MAC7D,MAAMkY,SAAS,GAAGxb,KAAK,CAACuB,OAAO,CAACugB,SAAS,CAACrF,YAAY,CAACnZ,QAAQ,CAAC,GAC5Dwe,SAAS,CAACrF,YAAY,CAACnZ,QAAQ,GAC/B,CAACwe,SAAS,CAACrF,YAAY,CAACnZ,QAAQ,CAAC;MAErC;MACA2kB,eAAe,GAAGzM,SAAS,CAAC/a,GAAG,CAAEynB,GAAQ,IAAI;QAC3C,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;UACtD,OAAO;YAAE1B,OAAO,EAAE0B;UAAG,CAAE;QACzB;QACA,OAAOA,GAAG;MACZ,CAAC,CAAC;IACJ;IACA;IAAA,KACK,IAAIpG,SAAS,CAACqG,YAAY,EAAE;MAC/BF,eAAe,GAAG,CAACnG,SAAS,CAACqG,YAAY,CAAC;IAC5C;IAEAF,eAAe,CAAC9a,OAAO,CAAE7J,QAAa,IAAI;MACxC,MAAMkjB,OAAO,GAAGljB,QAAQ,CAACkjB,OAAO,IAAIljB,QAAQ,CAAC9G,EAAE;MAE/C,MAAMuM,SAAS,GAAG,IAAI,CAAC1O,YAAY,CAAC,QAAQ,CAAC,EAAEiI,IAAI,CAChDmZ,CAAC,IACAA,CAAC,CAACjf,EAAE,KAAKgqB,OAAO,IAChB/K,CAAC,CAACjf,EAAE,KAAKgqB,OAAO,CAAC9lB,QAAQ,EAAE,IAC3B+a,CAAC,CAACjf,EAAE,CAACkE,QAAQ,EAAE,KAAK8lB,OAAO,CAC9B;MAED,IAAIzd,SAAS,EAAE;QACbmd,WAAW,CAAC,OAAO,CAAC,CAAC5Y,IAAI,CAAC;UACxB9Q,EAAE,EAAE,SAAS2pB,WAAW,EAAE,EAAE;UAC5B1pB,IAAI,EAAE,OAAO;UACboD,IAAI,EAAEkJ,SAAS,CAAClJ,IAAI;UACpBC,IAAI,EAAEuN,SAAS;UAAE;UACjB4C,QAAQ,EAAE;YAAEsC,CAAC,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAC,CAAE;UAAE;UAC1BvM,gBAAgB,EAAE8C,SAAS,CAAE;SAC9B,CAAC;MACJ,CAAC,MAAM;QACL;QACA,MAAMqf,SAAS,GACb9kB,QAAQ,CAACnK,KAAK,IACdmK,QAAQ,CAACzB,mBAAmB,IAC5B,aAAa2kB,OAAO,EAAE;QACxBN,WAAW,CAAC,OAAO,CAAC,CAAC5Y,IAAI,CAAC;UACxB9Q,EAAE,EAAE,SAAS2pB,WAAW,EAAE,EAAE;UAC5B1pB,IAAI,EAAE,OAAO;UACboD,IAAI,EAAEuoB,SAAS;UACftoB,IAAI,EAAEuN,SAAS;UAAE;UACjB4C,QAAQ,EAAE;YAAEsC,CAAC,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAC,CAAE;UAAE;UAC1BvM,gBAAgB,EAAE;YAAEzJ,EAAE,EAAEgqB,OAAO;YAAE3mB,IAAI,EAAEuoB;UAAS,CAAE,CAAE;SACrD,CAAC;MACJ;IACF,CAAC,CAAC;IAEF;IACA,IAAIC,mBAAmB,GAAG,EAAE;IAE5B;IACA,IAAIvG,SAAS,CAACrF,YAAY,IAAIqF,SAAS,CAACrF,YAAY,CAACC,gBAAgB,EAAE;MACrE,MAAM4L,MAAM,GAAGtoB,KAAK,CAACuB,OAAO,CAACugB,SAAS,CAACrF,YAAY,CAACC,gBAAgB,CAAC,GACjEoF,SAAS,CAACrF,YAAY,CAACC,gBAAgB,GACvC,CAACoF,SAAS,CAACrF,YAAY,CAACC,gBAAgB,CAAC;MAE7C;MACA2L,mBAAmB,GAAGC,MAAM,CAAC7nB,GAAG,CAAEynB,GAAQ,IAAI;QAC5C,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;UACtD,OAAO;YAAEK,eAAe,EAAEL;UAAG,CAAE;QACjC;QACA,OAAOA,GAAG;MACZ,CAAC,CAAC;IACJ;IACA;IAAA,KACK,IACHpG,SAAS,CAAC0G,mBAAmB,IAC7BxoB,KAAK,CAACuB,OAAO,CAACugB,SAAS,CAAC0G,mBAAmB,CAAC,EAC5C;MACAH,mBAAmB,GAAGvG,SAAS,CAAC0G,mBAAmB,CAAC/nB,GAAG,CACpDZ,IAAY,KAAM;QAAE2oB,mBAAmB,EAAE3oB;MAAI,CAAE,CAAC,CAClD;IACH;IAEAwoB,mBAAmB,CAAClb,OAAO,CAAEsb,KAAU,IAAI;MACzC,MAAM9B,IAAI,GAAG8B,KAAK,CAACF,eAAe,IAAIE,KAAK,CAACjsB,EAAE;MAC9C,MAAM4G,cAAc,GAAGqlB,KAAK,CAACD,mBAAmB,IAAIC,KAAK,CAAC5oB,IAAI;MAE9D;MACA,MAAM4W,aAAa,GAAG,IAAI,CAACpc,YAAY,CAAC,WAAW,CAAC,EAAEiI,IAAI,CACvDqZ,CAAC,IACCgL,IAAI,KACFhL,CAAC,CAACnf,EAAE,KAAKmqB,IAAI,IACZhL,CAAC,CAACnf,EAAE,KAAKmqB,IAAI,CAACjmB,QAAQ,EAAE,IACxBib,CAAC,CAACnf,EAAE,CAACkE,QAAQ,EAAE,KAAKimB,IAAI,CAAC,IAC5BvjB,cAAc,KACZuY,CAAC,CAAC9b,IAAI,KAAKuD,cAAc,IACxBuY,CAAC,CAAC9b,IAAI,CAACsG,WAAW,EAAE,KAAK/C,cAAc,CAAC+C,WAAW,EAAE,CAAE,CAC9D;MAED,IAAIsQ,aAAa,EAAE;QACjByP,WAAW,CAAC,WAAW,CAAC,CAAC5Y,IAAI,CAAC;UAC5B9Q,EAAE,EAAE,aAAa2pB,WAAW,EAAE,EAAE;UAChC1pB,IAAI,EAAE,WAAW;UACjBoD,IAAI,EAAE4W,aAAa,CAAC5W,IAAI;UACxBC,IAAI,EAAEuN,SAAS;UAAE;UACjB4C,QAAQ,EAAE;YAAEsC,CAAC,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAC,CAAE;UAAE;UAC1BvM,gBAAgB,EAAEwQ,aAAa,CAAE;SAClC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,MAAMmQ,MAAM,GAAGxjB,cAAc,IAAI,sBAAsBujB,IAAI,EAAE;QAC7DT,WAAW,CAAC,WAAW,CAAC,CAAC5Y,IAAI,CAAC;UAC5B9Q,EAAE,EAAE,aAAa2pB,WAAW,EAAE,EAAE;UAChC1pB,IAAI,EAAE,WAAW;UACjBoD,IAAI,EAAE+mB,MAAM;UACZ9mB,IAAI,EAAEuN,SAAS;UAAE;UACjB4C,QAAQ,EAAE;YAAEsC,CAAC,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAC,CAAE;UAAE;UAC1BvM,gBAAgB,EAAE;YAAEzJ,EAAE,EAAEmqB,IAAI;YAAE9mB,IAAI,EAAE+mB;UAAM,CAAE,CAAE;SAC/C,CAAC;MACJ;IACF,CAAC,CAAC;IAEF;IACA,IAAI8B,cAAc,GAAG,EAAE;IACvB,IAAIC,kBAAkB,GAAG,EAAE;IAE3B;IACA,IAAI7G,SAAS,CAACrF,YAAY,EAAE;MAC1B,IAAIqF,SAAS,CAACrF,YAAY,CAACQ,OAAO,EAAE;QAClC,MAAM2L,QAAQ,GAAG5oB,KAAK,CAACuB,OAAO,CAACugB,SAAS,CAACrF,YAAY,CAACQ,OAAO,CAAC,GAC1D6E,SAAS,CAACrF,YAAY,CAACQ,OAAO,GAC9B,CAAC6E,SAAS,CAACrF,YAAY,CAACQ,OAAO,CAAC;QAEpC;QACAyL,cAAc,GAAGE,QAAQ,CAACnoB,GAAG,CAAEynB,GAAQ,IAAI;UACzC,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;YACtD,OAAO;cAAE7jB,MAAM,EAAE6jB;YAAG,CAAE;UACxB;UACA,OAAOA,GAAG;QACZ,CAAC,CAAC;MACJ;MACA,IAAIpG,SAAS,CAACrF,YAAY,CAACS,WAAW,EAAE;QACtC,MAAM2L,YAAY,GAAG7oB,KAAK,CAACuB,OAAO,CAACugB,SAAS,CAACrF,YAAY,CAACS,WAAW,CAAC,GAClE4E,SAAS,CAACrF,YAAY,CAACS,WAAW,GAClC,CAAC4E,SAAS,CAACrF,YAAY,CAACS,WAAW,CAAC;QAExC;QACAyL,kBAAkB,GAAGE,YAAY,CAACpoB,GAAG,CAAEynB,GAAQ,IAAI;UACjD,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;YACtD,OAAO;cAAE7jB,MAAM,EAAE6jB;YAAG,CAAE;UACxB;UACA,OAAOA,GAAG;QACZ,CAAC,CAAC;MACJ;IACF;IACA;IAAA,KACK;MACH,IAAIpG,SAAS,CAAC7nB,KAAK,IAAI+F,KAAK,CAACuB,OAAO,CAACugB,SAAS,CAAC7nB,KAAK,CAAC,EAAE;QACrDyuB,cAAc,GAAG5G,SAAS,CAAC7nB,KAAK;MAClC;MACA,IAAI6nB,SAAS,CAACjd,SAAS,IAAI7E,KAAK,CAACuB,OAAO,CAACugB,SAAS,CAACjd,SAAS,CAAC,EAAE;QAC7D8jB,kBAAkB,GAAG7G,SAAS,CAACjd,SAAS;MAC1C;IACF;IAEA;IACA6jB,cAAc,CAACvb,OAAO,CAAE5T,IAAS,IAAI;MACnC,MAAM8K,MAAM,GAAG9K,IAAI,CAAC8K,MAAM,IAAI9K,IAAI,CAACiD,EAAE;MAErC,MAAMiT,QAAQ,GAAG,IAAI,CAACpV,YAAY,CAAC,OAAO,CAAC,EAAEiI,IAAI,CAC9C8Z,CAAC,IACAA,CAAC,CAAC5f,EAAE,KAAK,WAAW6H,MAAM,EAAE,IAC5B+X,CAAC,CAAC5f,EAAE,KAAK6H,MAAM,IACf+X,CAAC,CAAC5f,EAAE,KAAK6H,MAAM,CAAC3D,QAAQ,EAAE,IAC1B0b,CAAC,CAAC5f,EAAE,CAACkE,QAAQ,EAAE,KAAK2D,MAAM,CAC7B;MAED,IAAIoL,QAAQ,EAAE;QACZyW,WAAW,CAAC,MAAM,CAAC,CAAC5Y,IAAI,CAAC;UACvB9Q,EAAE,EAAE,QAAQ2pB,WAAW,EAAE,EAAE;UAC3B1pB,IAAI,EAAE,MAAM;UACZoD,IAAI,EAAE4P,QAAQ,CAAC5P,IAAI;UACnBC,IAAI,EAAEuN,SAAS;UAAE;UACjB4C,QAAQ,EAAE;YAAEsC,CAAC,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAC,CAAE;UAAE;UAC1BvM,gBAAgB,EAAEwJ,QAAQ,CAAE;SAC7B,CAAC;MACJ,CAAC,MAAM;QACL;QACA,MAAMnL,QAAQ,GAAG/K,IAAI,CAAC+K,QAAQ,IAAI,YAAYD,MAAM,EAAE;QACtD6hB,WAAW,CAAC,MAAM,CAAC,CAAC5Y,IAAI,CAAC;UACvB9Q,EAAE,EAAE,QAAQ2pB,WAAW,EAAE,EAAE;UAC3B1pB,IAAI,EAAE,MAAM;UACZoD,IAAI,EAAEyE,QAAQ;UACdxE,IAAI,EAAEuN,SAAS;UAAE;UACjB4C,QAAQ,EAAE;YAAEsC,CAAC,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAC,CAAE;UAAE;UAC1BvM,gBAAgB,EAAE;YAAEzJ,EAAE,EAAE6H,MAAM;YAAExE,IAAI,EAAEyE;UAAQ,CAAE,CAAE;SACnD,CAAC;MACJ;IACF,CAAC,CAAC;IAEF;IACAqkB,kBAAkB,CAACxb,OAAO,CAAE2b,QAAa,IAAI;MAC3C,MAAMC,UAAU,GAAGD,QAAQ,CAACzkB,MAAM,IAAIykB,QAAQ,CAACtsB,EAAE;MAEjD;MACA,MAAMwsB,YAAY,GAAG,IAAI,CAAC3uB,YAAY,CAAC,OAAO,CAAC,EAAEiI,IAAI,CAClD8Z,CAAC,IACAA,CAAC,CAAC5f,EAAE,KAAK,QAAQusB,UAAU,EAAE,IAC7B3M,CAAC,CAAC5f,EAAE,KAAKusB,UAAU,IACnB3M,CAAC,CAAC5f,EAAE,KAAKusB,UAAU,CAACroB,QAAQ,EAAE,IAC9B0b,CAAC,CAAC5f,EAAE,CAACkE,QAAQ,EAAE,KAAKqoB,UAAU,CACjC;MAED,IAAIC,YAAY,EAAE;QAChB9C,WAAW,CAAC,MAAM,CAAC,CAAC5Y,IAAI,CAAC;UACvB9Q,EAAE,EAAE,QAAQ2pB,WAAW,EAAE,EAAE;UAC3B1pB,IAAI,EAAE,MAAM;UACZoD,IAAI,EAAEmpB,YAAY,CAACnpB,IAAI;UACvBC,IAAI,EAAEuN,SAAS;UAAE;UACjB4C,QAAQ,EAAE;YAAEsC,CAAC,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAC,CAAE;UAAE;UAC1BvM,gBAAgB,EAAE+iB,YAAY,CAAE;SACjC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,MAAM1kB,QAAQ,GAAGwkB,QAAQ,CAACxkB,QAAQ,IAAI,iBAAiBykB,UAAU,EAAE;QACnE7C,WAAW,CAAC,MAAM,CAAC,CAAC5Y,IAAI,CAAC;UACvB9Q,EAAE,EAAE,QAAQ2pB,WAAW,EAAE,EAAE;UAC3B1pB,IAAI,EAAE,MAAM;UACZoD,IAAI,EAAEyE,QAAQ;UACdxE,IAAI,EAAEuN,SAAS;UAAE;UACjB4C,QAAQ,EAAE;YAAEsC,CAAC,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAC,CAAE;UAAE;UAC1BvM,gBAAgB,EAAE;YAAEzJ,EAAE,EAAEusB,UAAU;YAAElpB,IAAI,EAAEyE;UAAQ,CAAE,CAAE;SACvD,CAAC;MACJ;IACF,CAAC,CAAC;IAEF;IACA,IAAI4hB,WAAW,CAAC,QAAQ,CAAC,CAAC9mB,MAAM,KAAK,CAAC,EAAE;MACtC,MAAM6pB,aAAa,GAAG,IAAI,CAAC5uB,YAAY,CAAC,SAAS,CAAC,EAAEiI,IAAI,CAAE2W,CAAC,IAAI;QAC7D,MAAMlY,UAAU,GAAIkY,CAAS,CAACxc,IAAI,EAAEgF,IAAI,EAAE,CAAC,CAAC;QAC5C,OAAOV,UAAU,KAAK,WAAW;MACnC,CAAC,CAAC;MACF,IAAIkoB,aAAa,EAAE;QACjB;QACA,MAAMrB,cAAc,GAClB,IAAI,CAAC3vB,gBAAgB,KAAK,eAAe,GACrC6pB,SAAS,CAAC7gB,IAAI,IAAIgoB,aAAa,CAACppB,IAAI,GACpCopB,aAAa,CAACppB,IAAI;QAExBqmB,WAAW,CAAC,QAAQ,CAAC,CAAC5Y,IAAI,CAAC;UACzB9Q,EAAE,EAAE,UAAU2pB,WAAW,EAAE,EAAE;UAC7B1pB,IAAI,EAAE,QAAQ;UACdoD,IAAI,EAAE+nB,cAAc;UACpB9nB,IAAI,EAAEmpB,aAAa,CAACnpB,IAAI,IAAI,IAAI,CAAC+M,cAAc,CAAC,QAAQ,CAAC;UACzDoD,QAAQ,EAAE;YAAEsC,CAAC,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAC,CAAE;UAAE;UAC1BvM,gBAAgB,EAAEgjB,aAAa,CAAE;SAClC,CAAC;MACJ;IACF;IAEA;IACA,MAAM5D,KAAK,GAAyB,EAAE;IACtC,MAAM+B,SAAS,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,CAAC;IAEvE;IACA,IAAI,CAACtvB,eAAe,GAAG,EAAE;IACzB,IAAI,CAAC0B,YAAY,GAAG,IAAI;IAExB4tB,SAAS,CAACja,OAAO,CAAElG,QAAQ,IAAI;MAC7Bif,WAAW,CAACjf,QAAQ,CAAC,CAACkG,OAAO,CAAEnH,IAAI,IAAI;QACrC;QACA,IAAI,IAAI,CAACnL,UAAU,EAAE;UACnB;UACA,MAAMyoB,SAAS,GAAG+B,KAAK,CAACjmB,MAAM,CAAC,CAAC;UAChC4G,IAAI,CAACiK,QAAQ,GAAG,IAAI,CAACsX,yBAAyB,CAACjE,SAAS,CAAC;QAC3D,CAAC,MAAM;UACL;UACAtd,IAAI,CAACiK,QAAQ,GAAG,IAAI,CAACwT,+BAA+B,EAAE;QACxD;QACA4B,KAAK,CAAC/X,IAAI,CAACtH,IAAI,CAAC;QAChB;QACA,IAAI,CAAClO,eAAe,CAACwV,IAAI,CAACtH,IAAI,CAAC;QAC/B,IAAI,CAACxM,YAAY,GAAGwM,IAAI,CAACxJ,EAAE;MAC7B,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAAC1E,eAAe,GAAGutB,KAAK;IAE5B;IACA,IAAI,CAACztB,WAAW,GAAGytB,KAAK,CAAC5kB,GAAG,CAAEuF,IAAI,KAAM;MACtCxJ,EAAE,EAAEwJ,IAAI,CAACxJ,EAAE;MACXC,IAAI,EAAE,aAAa;MACnBmL,IAAI,EAAE;QAAE,GAAG5B,IAAI;QAAE2K,KAAK,EAAE,IAAI,CAACpW,aAAa,GAAG,EAAE,GAAG;MAAG,CAAE;MACvD0V,QAAQ,EAAEjK,IAAI,CAACiK;KAChB,CAAC,CAAC;IAEH;IACA,IAAI,CAAC5Z,GAAG,CAAC6I,aAAa,EAAE;IAExB;IACAC,UAAU,CAAC,MAAK;MACd;MACA,IAAI,CAACtH,WAAW,GAAG,EAAE;MAErB;MACA,IAAI,CAACyH,0BAA0B,CAAC+lB,KAAK,CAAC;MAEtC;MACA,IAAI,IAAI,CAACpuB,oBAAoB,EAAE;QAC7B,IAAI,CAACA,oBAAoB,CAACoI,0BAA0B,EAAE;MACxD;MAEA;MACA,IAAI,CAAChJ,GAAG,CAAC6I,aAAa,EAAE;IAC1B,CAAC,EAAE,GAAG,CAAC;IAEP;IACA,IAAI,IAAI,CAAC7G,eAAe,EAAE;MACxB,IAAI,CAACF,cAAc,GAAG,IAAI;IAC5B;IAEA;IACA,IAAI,IAAI,CAACoC,aAAa,EAAE;MACtB4E,UAAU,CAAC,MAAK;QACd,IAAI,CAACsoB,qBAAqB,EAAE;MAC9B,CAAC,EAAE,GAAG,CAAC;IACT;IAEA;IACA,IAAI,IAAI,CAAC5sB,UAAU,EAAE;MACnBsE,UAAU,CAAC,MAAK;QACd,IAAI,CAACuoB,2BAA2B,EAAE;MACpC,CAAC,EAAE,GAAG,CAAC;IACT;EACF;EAEA;EACQF,gCAAgCA,CAAA;IACtC,IAAI,CAAC,IAAI,CAACrsB,SAAS,EAAE;MACnB;IACF;IAEA;IACA,IAAI,CAACyiB,6BAA6B,EAAE;IAEpC;IACAze,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACxE,aAAa,IAAI,IAAI,CAACA,aAAa,CAACyE,MAAM,GAAG,CAAC,EAAE;QACvD;QACA,MAAM6iB,kBAAkB,GAAG,IAAI,CAACtnB,aAAa,CAAC2H,IAAI,CAC/CU,MAAM,IACLA,MAAM,CAACnD,IAAI,KAAK,IAAI,CAAC1E,SAAS,IAC9B6H,MAAM,CAACnD,IAAI,CAACsG,WAAW,EAAE,KAAK,IAAI,CAAChL,SAAS,CAACgL,WAAW,EAAE,CAC7D;QAED,IAAI8b,kBAAkB,EAAE;UACtB,IAAI,CAAClmB,cAAc,GAAGkmB,kBAAkB,CAACpiB,IAAI,CAAC,CAAC;UAC/C,IAAI,CAACxE,SAAS,GAAG4mB,kBAAkB,CAACljB,KAAK,CAAC2B,QAAQ,EAAE,CAAC,CAAC;UACtD,IAAI,CAACwhB,eAAe,CAACD,kBAAkB,CAAC;UACxC,IAAI,CAAC5rB,GAAG,CAAC6I,aAAa,EAAE;QAC1B;MACF;IACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACZ;EAEA;EACQuoB,qBAAqBA,CAAA;IAC3B;IACA,MAAMyB,gBAAgB,GAAG,IAAI,CAACpxB,eAAe,CAAC2I,GAAG,CAAC,CAACuF,IAAI,EAAEgW,KAAK,MAAM;MAClEzJ,CAAC,EAAE,EAAE;MACLC,CAAC,EAAE,GAAG,GAAGwJ,KAAK,GAAG;KAClB,CAAC,CAAC;IAEH;IACA,IAAI,CAAClkB,eAAe,CAACqV,OAAO,CAAC,CAACnH,IAAI,EAAEgW,KAAK,KAAI;MAC3ChW,IAAI,CAACiK,QAAQ,GAAG;QAAE,GAAGiZ,gBAAgB,CAAClN,KAAK;MAAC,CAAE;IAChD,CAAC,CAAC;IAEF;IACA,IAAI,CAACpkB,WAAW,GAAG,IAAI,CAACE,eAAe,CAAC2I,GAAG,CAAC,CAACuF,IAAI,EAAEgW,KAAK,MAAM;MAC5Dxf,EAAE,EAAEwJ,IAAI,CAACxJ,EAAE;MACXC,IAAI,EAAE,aAAa;MACnBmL,IAAI,EAAE;QACJ,GAAG5B,IAAI;QACP2K,KAAK,EAAE,EAAE;QACTlU,IAAI,EAAEuJ,IAAI,CAACvJ,IAAI;QACfoD,IAAI,EAAEmG,IAAI,CAACnG,IAAI;QACfC,IAAI,EAAEkG,IAAI,CAAClG,IAAI;QACfmQ,QAAQ,EAAE;UAAE,GAAGiZ,gBAAgB,CAAClN,KAAK;QAAC;OACvC;MACD/L,QAAQ,EAAE;QAAE,GAAGiZ,gBAAgB,CAAClN,KAAK;MAAC;KACvC,CAAC,CAAC;IAEH;IACA,IAAI,CAACnkB,WAAW,GAAG,EAAE;IACrB,KAAK,IAAIsI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACrI,eAAe,CAACsH,MAAM,GAAG,CAAC,EAAEe,CAAC,EAAE,EAAE;MACxD,IAAI,CAACtI,WAAW,CAACyV,IAAI,CAAC;QACpB9Q,EAAE,EAAE,QAAQ2D,CAAC,EAAE;QACf6S,MAAM,EAAE,IAAI,CAAClb,eAAe,CAACqI,CAAC,CAAC,CAAC3D,EAAE;QAClCyW,MAAM,EAAE,IAAI,CAACnb,eAAe,CAACqI,CAAC,GAAG,CAAC,CAAC,CAAC3D;OACrC,CAAC;IACJ;IAEA;IACA,IAAI,CAACnG,GAAG,CAAC6I,aAAa,EAAE;IAExB;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACgf,qBAAqB,EAAE;IAC9B,CAAC,EAAE,GAAG,CAAC;IAEP;IACA,IAAI,CAAC1jB,YAAY,GAAG,CAClB;MACEwF,IAAI,EAAE,IAAI;MACVpD,IAAI,EAAE,kBAAkB,IAAI,CAAC1B,SAAS,IAAI,kBAAkB;KAC7D,CACF;EACH;EAEA;EACOguB,mBAAmBA,CAACrH,SAAc;IACvC;IACA,IAAI,CAACzkB,yBAAyB,GAAGykB,SAAS;IAE1C;IACA,IAAI,CAAC3mB,SAAS,GAAG2mB,SAAS,CAACjiB,IAAI;IAC/B,IAAI,CAACxE,SAAS,GAAGymB,SAAS,CAAC5c,IAAI;IAC/B,IAAI,CAAClJ,iBAAiB,GAAG8lB,SAAS,CAAC5c,IAAI;IACvC,IAAI,CAACjJ,yBAAyB,GAAG6lB,SAAS,CAAC9C,iBAAiB;IAE5D;IACA,IAAI,CAACjjB,cAAc,GAAG+lB,SAAS,CAACjiB,IAAI;IAEpC;IACA,IAAI,IAAI,CAAClF,aAAa,IAAI,IAAI,CAACA,aAAa,CAACyE,MAAM,GAAG,CAAC,EAAE;MACvD,MAAMgqB,cAAc,GAAG,IAAI,CAACzuB,aAAa,CAAC2H,IAAI,CAC3CU,MAAM,IACLA,MAAM,CAACjE,KAAK,KAAK+iB,SAAS,CAAC5c,IAAI,IAAIlC,MAAM,CAACnD,IAAI,KAAKiiB,SAAS,CAACjiB,IAAI,CACpE;MAED,IAAI,CAACupB,cAAc,EAAE;QACnB;QACA,IAAI,CAACzuB,aAAa,CAAC2S,IAAI,CAAC;UACtBvO,KAAK,EAAE+iB,SAAS,CAAC5c,IAAI;UACrBrF,IAAI,EAAEiiB,SAAS,CAACjiB,IAAI;UACpBiiB,SAAS,EAAEA;SACL,CAAC;MACX;IACF;IAEA;IACA,IAAI,CAACzrB,GAAG,CAAC6I,aAAa,EAAE;EAC1B;EAEA;EACAmqB,MAAMA,CAAA;IACJ,IAAI,CAACnzB,MAAM,CAACuI,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;EACnC;EAEA;EACO6qB,uBAAuBA,CAAA;IAC5B;EAAA;EAGF;EACAC,kBAAkBA,CAAC7sB,KAAa,EAAEid,OAAe;IAC/C,IAAI,CAAChhB,YAAY,GAAGghB,OAAO;IAC3B,IAAI,CAACphB,gBAAgB,GAAG,IAAI;EAC9B;EAEA;EACAixB,0BAA0BA,CAAC9sB,KAAa,EAAEid,OAAe;IACvD,IAAI,CAAChhB,YAAY,GAAGghB,OAAO;IAC3B,IAAI,CAACjhB,iBAAiB,GAAG,IAAI,CAAC,CAAC;IAC/B,IAAI,CAACH,gBAAgB,GAAG,IAAI;EAC9B;EAEAuX,gBAAgBA,CAACpT,KAAa,EAAEid,OAAe;IAC7C,IAAI,CAAChhB,YAAY,GAAGghB,OAAO;IAC3B,IAAI,CAACnhB,cAAc,GAAG,IAAI;EAC5B;EAEAixB,kBAAkBA,CAAC/sB,KAAa,EAAEid,OAAe;IAC/C,IAAI,CAAChhB,YAAY,GAAGghB,OAAO;IAC3B,IAAI,CAAClhB,gBAAgB,GAAG,IAAI;EAC9B;EAEAixB,iBAAiBA,CAAA;IACf,IAAI,CAACnxB,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACG,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,YAAY,GAAG,EAAE;EACxB;EAGA;EACQ+uB,2BAA2BA,CAAA;IACjC,IAAI,IAAI,CAACpvB,yBAAyB,IAAI,IAAI,CAACR,eAAe,CAACsH,MAAM,GAAG,CAAC,EAAE;MACrE;MACA,IAAI,CAACE,0BAA0B,CAAC,IAAI,CAACxH,eAAe,CAAC;MAErD;MACA,IAAI,IAAI,CAACb,oBAAoB,EAAE;QAC7B,IAAI,CAACA,oBAAoB,CAACoI,0BAA0B,EAAE;MACxD;MAEA;MACA,IAAI,CAAChJ,GAAG,CAAC6I,aAAa,EAAE;MAExB;MACA,IAAI,CAAC5G,yBAAyB,GAAG,KAAK;IACxC;EACF;EAEA;EACQivB,yBAAyBA,CAACvL,KAAa;IAC7C,MAAM2N,cAAc,GAAG,GAAG;IAC1B,MAAMC,cAAc,GAAG,GAAG;IAC1B,MAAMC,uBAAuB,GAAG,GAAG;IAEnC,MAAMC,kBAAkB,GAAG;MACzBvX,CAAC,EAAEoX,cAAc;MACjBnX,CAAC,EAAEoX,cAAc,GAAG5N,KAAK,GAAG6N;KAC7B;IAEDvrB,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE;MAC/Cyd,KAAK;MACL2N,cAAc;MACdC,cAAc;MACdC,uBAAuB;MACvBC;KACD,CAAC;IAEF,OAAOA,kBAAkB;EAC3B;EAEA;EACAC,oBAAoBA,CAAA;IAClB,IAAI,CAAC7zB,MAAM,CAACuI,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;EACzC;EAEAurB,eAAeA,CAACC,OAAuC;IACrD,IAAI,IAAI,CAAChyB,gBAAgB,KAAKgyB,OAAO,EAAE;MACrC;MACA,IAAI,CAACC,iBAAiB,EAAE;MAExB;MACA,IAAI,CAACh0B,MAAM,CAACuI,QAAQ,CAAC,CAAC,eAAe,EAAEwrB,OAAO,CAAC,CAAC;IAClD;EACF;EAEQC,iBAAiBA,CAAA;IACvB;IACA,IAAI,CAAC/uB,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,SAAS,GAAG,EAAE;IAEnB;IACA,IAAI,CAACE,aAAa,GAAG;MACnBC,GAAG,EAAE,EAAE;MACPC,MAAM,EAAE,EAAE;MACVC,OAAO,EAAE,EAAE;MACXC,IAAI,EAAE;KACP;IAED;IACA,IAAI,CAAC7D,eAAe,GAAG,EAAE;IACzB,IAAI,CAACF,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,WAAW,GAAG,EAAE;IAErB;IACA,IAAI,CAACkB,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACZ,cAAc,GAAG,IAAI;IAE1B;IACA,IAAI,CAACqB,YAAY,GAAG,IAAI;IAExB;IACA,IAAI,CAACxB,cAAc,GAAG,IAAI;IAE1B;IACA,IAAI,CAACR,WAAW,GAAG,KAAK;IACxB,IAAI,CAACE,WAAW,GAAG,IAAI;IACvB,IAAI,CAACiQ,YAAY,EAAE;IAEnB;IACA,IAAI,CAAClN,YAAY,GAAG,EAAE;IACtB,IAAI,CAAC6C,cAAc,GAAG,EAAE;IAExB;IACA,IAAI,CAAClG,SAAS,GAAG,SAAS;IAC1B,IAAI,CAACE,UAAU,CAACsH,GAAG,CAAC,QAAQ,CAAC,EAAE2H,QAAQ,CAAC,EAAE,EAAE;MAAEC,SAAS,EAAE;IAAK,CAAE,CAAC;IAEjE;IACA,IAAI,CAACtK,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACC,sBAAsB,GAAG,EAAE;IAChC,IAAI,CAACC,eAAe,GAAG,EAAE;IAEzB;IACA,IAAI,CAACvD,WAAW,GAAGiF,MAAM,CAACC,UAAU,EAAE;IAEtC;IACA,IAAI,CAACrD,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAAC2B,wBAAwB,GAAG,KAAK;IAErC;IACA,IAAI,CAACvB,UAAU,GAAG,KAAK;IACvB,IAAI,CAACD,UAAU,GAAG,KAAK;IACvB,IAAI,CAACN,aAAa,GAAG,KAAK;IAC1B,IAAI,CAAClC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAAC0C,UAAU,GAAG,IAAI;IACtB,IAAI,CAAC3C,gBAAgB,GAAG,KAAK;IAE7B;IACA,IAAI,CAACkD,uBAAuB,GAAG,IAAI;IACnC,IAAI,CAAC/D,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACiD,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAAC6C,yBAAyB,GAAG,IAAI;IAErC;IACA,IAAI,CAAChH,GAAG,CAAC6I,aAAa,EAAE;IAExBZ,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;EAC1D;EAEM4rB,mBAAmBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAApR,iBAAA;MACvB;MACA,MAAMoR,MAAI,CAACpM,oBAAoB,EAAE;IAAC;EACpC;EAEAqM,YAAYA,CAAA;IACV;IACA,MAAMC,SAAS,GAAG,IAAI,CAACxyB,eAAe,CAACiO,IAAI,CACxCC,IAAI,IAAKA,IAAI,CAACvJ,IAAI,KAAK,QAAQ,CACjC;IACD,MAAM8K,QAAQ,GAAG,IAAI,CAACzP,eAAe,CAACiO,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACvJ,IAAI,KAAK,OAAO,CAAC;IAC3E,MAAM8tB,YAAY,GAAG,CAAC,EAAE,IAAI,CAACpvB,SAAS,IAAI,IAAI,CAACA,SAAS,CAACsG,IAAI,EAAE,CAACrC,MAAM,GAAG,CAAC,CAAC;IAC3E,MAAMorB,mBAAmB,GAAG,CAAC,EAC3B,IAAI,CAACpvB,WAAW,IAAI,IAAI,CAACA,WAAW,CAACqG,IAAI,EAAE,CAACrC,MAAM,GAAG,CAAC,CACvD;IAED,OAAOkrB,SAAS,IAAI/iB,QAAQ,IAAIgjB,YAAY,IAAIC,mBAAmB;EACrE;EAEA;EACAC,yBAAyBA,CAAA;IACvB,IAAI,CAACnvB,uBAAuB,GAAG,CAAC,IAAI,CAACA,uBAAuB;EAC9D;EAEA;EACAovB,qBAAqBA,CAAA;IACnB,IAAI,CAAChB,iBAAiB,EAAE;IAExB;IACA,IAAI,IAAI,CAACvxB,cAAc,EAAE;MACvB,IAAI,CAACjC,MAAM,CAACuI,QAAQ,CAClB,CAAC,eAAe,EAAE,IAAI,CAACxG,gBAAgB,EAAE,SAAS,CAAC,EACnD;QACEmG,WAAW,EAAE;UAAE5B,EAAE,EAAE,IAAI,CAACrE;QAAc;OACvC,CACF;IACH;EACF;EAEA;EACAwyB,oBAAoBA,CAAA;IAClB,IAAI,CAACjB,iBAAiB,EAAE;IAExB;IACA,IAAI,CAACxzB,MAAM,CAACuI,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;EACzC;CACD;AAlkKkCmsB,UAAA,EAAhCj2B,SAAS,CAACU,oBAAoB,CAAC,C,iEAA6C;AACnBu1B,UAAA,EAAzDj2B,SAAS,CAAC,iBAAiB,EAAE;EAAEk2B,IAAI,EAAEj2B;AAAgB,CAAE,CAAC,C,4DAAoC;AAFlFqB,oBAAoB,GAAA20B,UAAA,EArBhCl2B,SAAS,CAAC;EACTo2B,QAAQ,EAAE,kBAAkB;EAC5BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPn2B,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,eAAe,EACfM,uBAAuB,EACvBQ,mBAAmB,EACnBb,mBAAmB,EACnBC,oBAAoB,EACpBC,aAAa,EACb81B,cAAc,EACdp1B,mBAAmB,EACnBG,uBAAuB,CACxB;EACDk1B,SAAS,EAAE,CAAC91B,aAAa,CAAC;EAC1B+1B,WAAW,EAAE,+BAA+B;EAC5CC,SAAS,EAAE,CAAC,+BAA+B;CAC5C,CAAC,C,EACWn1B,oBAAoB,CAmkKhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}