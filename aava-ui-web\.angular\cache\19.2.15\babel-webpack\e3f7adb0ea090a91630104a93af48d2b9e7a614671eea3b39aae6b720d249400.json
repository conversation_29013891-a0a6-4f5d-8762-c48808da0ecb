{"ast": null, "code": "import { catchError, of } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@shared/auth/services/token-storage.service\";\nimport * as i3 from \"@shared/auth/services/auth.service\";\nexport let RootRedirectComponent = /*#__PURE__*/(() => {\n  class RootRedirectComponent {\n    router;\n    tokenStorage;\n    authService;\n    redirectMessage = 'Checking authentication...';\n    constructor(router, tokenStorage, authService) {\n      this.router = router;\n      this.tokenStorage = tokenStorage;\n      this.authService = authService;\n    }\n    ngOnInit() {\n      console.log('🔀 RootRedirectComponent: Determining redirect destination');\n      this.determineRedirect();\n    }\n    determineRedirect() {\n      const accessToken = this.tokenStorage.getAccessToken();\n      const refreshToken = this.tokenStorage.getRefreshToken();\n      // If we have an access token, go to dashboard\n      if (accessToken) {\n        console.log('✅ User authenticated, redirecting to dashboard');\n        this.redirectMessage = 'Welcome back! Redirecting to your dashboard...';\n        setTimeout(() => {\n          this.router.navigate(['/dashboard']);\n        }, 1000);\n        return;\n      }\n      // If we have a refresh token but no access token, try to refresh\n      if (!accessToken && refreshToken) {\n        console.log('🔄 Attempting token refresh');\n        this.redirectMessage = 'Refreshing your session...';\n        this.authService.refreshToken(refreshToken).pipe(catchError(error => {\n          console.log('❌ Token refresh failed, redirecting to marketplace');\n          this.redirectMessage = 'Session expired. Redirecting to marketplace...';\n          setTimeout(() => {\n            this.router.navigate(['/marketplace']);\n          }, 1500);\n          return of(null);\n        })).subscribe(success => {\n          if (success) {\n            console.log('✅ Token refreshed successfully, redirecting to dashboard');\n            this.redirectMessage = 'Session refreshed! Redirecting to your dashboard...';\n            setTimeout(() => {\n              this.router.navigate(['/dashboard']);\n            }, 1000);\n          }\n        });\n        return;\n      }\n      // If no tokens at all, redirect to marketplace\n      console.log('🏪 No authentication found, redirecting to marketplace');\n      this.redirectMessage = 'Welcome! Redirecting to marketplace...';\n      setTimeout(() => {\n        this.router.navigate(['/marketplace']);\n      }, 1000);\n    }\n    static ɵfac = function RootRedirectComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RootRedirectComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.TokenStorageService), i0.ɵɵdirectiveInject(i3.AuthService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RootRedirectComponent,\n      selectors: [[\"app-root-redirect\"]],\n      decls: 6,\n      vars: 1,\n      consts: [[2, \"display\", \"flex\", \"justify-content\", \"center\", \"align-items\", \"center\", \"height\", \"100vh\", \"background\", \"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\", \"color\", \"white\"], [2, \"text-align\", \"center\"], [2, \"font-size\", \"2rem\", \"margin-bottom\", \"1rem\"], [2, \"font-size\", \"1rem\", \"opacity\", \"0.8\"]],\n      template: function RootRedirectComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtext(3, \"\\uD83C\\uDF1F Elder Wand\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 3);\n          i0.ɵɵtext(5);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.redirectMessage);\n        }\n      },\n      encapsulation: 2\n    });\n  }\n  return RootRedirectComponent;\n})();", "map": {"version": 3, "names": ["catchError", "of", "RootRedirectComponent", "router", "tokenStorage", "authService", "redirectMessage", "constructor", "ngOnInit", "console", "log", "determineRedirect", "accessToken", "getAccessToken", "refreshToken", "getRefreshToken", "setTimeout", "navigate", "pipe", "error", "subscribe", "success", "i0", "ɵɵdirectiveInject", "i1", "Router", "i2", "TokenStorageService", "i3", "AuthService", "selectors", "decls", "vars", "consts", "template", "RootRedirectComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate"], "sources": ["C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\shared\\components\\root-redirect\\root-redirect.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { TokenStorageService } from '@shared/auth/services/token-storage.service';\r\nimport { AuthService } from '@shared/auth/services/auth.service';\r\nimport { catchError, of } from 'rxjs';\r\n\r\n@Component({\r\n  selector: 'app-root-redirect',\r\n  standalone: true,\r\n  template: `\r\n    <div\r\n      style=\"display: flex; justify-content: center; align-items: center; height: 100vh; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;\"\r\n    >\r\n      <div style=\"text-align: center;\">\r\n        <div style=\"font-size: 2rem; margin-bottom: 1rem;\">🌟 Elder Wand</div>\r\n        <div style=\"font-size: 1rem; opacity: 0.8;\">{{ redirectMessage }}</div>\r\n      </div>\r\n    </div>\r\n  `,\r\n})\r\nexport class RootRedirectComponent implements OnInit {\r\n  redirectMessage = 'Checking authentication...';\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private tokenStorage: TokenStorageService,\r\n    private authService: AuthService,\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    console.log('🔀 RootRedirectComponent: Determining redirect destination');\r\n    this.determineRedirect();\r\n  }\r\n\r\n  private determineRedirect(): void {\r\n    const accessToken = this.tokenStorage.getAccessToken();\r\n    const refreshToken = this.tokenStorage.getRefreshToken();\r\n\r\n    // If we have an access token, go to dashboard\r\n    if (accessToken) {\r\n      console.log('✅ User authenticated, redirecting to dashboard');\r\n      this.redirectMessage = 'Welcome back! Redirecting to your dashboard...';\r\n      setTimeout(() => {\r\n        this.router.navigate(['/dashboard']);\r\n      }, 1000);\r\n      return;\r\n    }\r\n\r\n    // If we have a refresh token but no access token, try to refresh\r\n    if (!accessToken && refreshToken) {\r\n      console.log('🔄 Attempting token refresh');\r\n      this.redirectMessage = 'Refreshing your session...';\r\n\r\n      this.authService\r\n        .refreshToken(refreshToken)\r\n        .pipe(\r\n          catchError((error) => {\r\n            console.log('❌ Token refresh failed, redirecting to marketplace');\r\n            this.redirectMessage =\r\n              'Session expired. Redirecting to marketplace...';\r\n            setTimeout(() => {\r\n              this.router.navigate(['/marketplace']);\r\n            }, 1500);\r\n            return of(null);\r\n          }),\r\n        )\r\n        .subscribe((success) => {\r\n          if (success) {\r\n            console.log(\r\n              '✅ Token refreshed successfully, redirecting to dashboard',\r\n            );\r\n            this.redirectMessage =\r\n              'Session refreshed! Redirecting to your dashboard...';\r\n            setTimeout(() => {\r\n              this.router.navigate(['/dashboard']);\r\n            }, 1000);\r\n          }\r\n        });\r\n      return;\r\n    }\r\n\r\n    // If no tokens at all, redirect to marketplace\r\n    console.log('🏪 No authentication found, redirecting to marketplace');\r\n    this.redirectMessage = 'Welcome! Redirecting to marketplace...';\r\n    setTimeout(() => {\r\n      this.router.navigate(['/marketplace']);\r\n    }, 1000);\r\n  }\r\n}\r\n"], "mappings": "AAIA,SAASA,UAAU,EAAEC,EAAE,QAAQ,MAAM;;;;;AAgBrC,WAAaC,qBAAqB;EAA5B,MAAOA,qBAAqB;IAItBC,MAAA;IACAC,YAAA;IACAC,WAAA;IALVC,eAAe,GAAG,4BAA4B;IAE9CC,YACUJ,MAAc,EACdC,YAAiC,EACjCC,WAAwB;MAFxB,KAAAF,MAAM,GAANA,MAAM;MACN,KAAAC,YAAY,GAAZA,YAAY;MACZ,KAAAC,WAAW,GAAXA,WAAW;IAClB;IAEHG,QAAQA,CAAA;MACNC,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;MACzE,IAAI,CAACC,iBAAiB,EAAE;IAC1B;IAEQA,iBAAiBA,CAAA;MACvB,MAAMC,WAAW,GAAG,IAAI,CAACR,YAAY,CAACS,cAAc,EAAE;MACtD,MAAMC,YAAY,GAAG,IAAI,CAACV,YAAY,CAACW,eAAe,EAAE;MAExD;MACA,IAAIH,WAAW,EAAE;QACfH,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;QAC7D,IAAI,CAACJ,eAAe,GAAG,gDAAgD;QACvEU,UAAU,CAAC,MAAK;UACd,IAAI,CAACb,MAAM,CAACc,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;QACtC,CAAC,EAAE,IAAI,CAAC;QACR;MACF;MAEA;MACA,IAAI,CAACL,WAAW,IAAIE,YAAY,EAAE;QAChCL,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC1C,IAAI,CAACJ,eAAe,GAAG,4BAA4B;QAEnD,IAAI,CAACD,WAAW,CACbS,YAAY,CAACA,YAAY,CAAC,CAC1BI,IAAI,CACHlB,UAAU,CAAEmB,KAAK,IAAI;UACnBV,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;UACjE,IAAI,CAACJ,eAAe,GAClB,gDAAgD;UAClDU,UAAU,CAAC,MAAK;YACd,IAAI,CAACb,MAAM,CAACc,QAAQ,CAAC,CAAC,cAAc,CAAC,CAAC;UACxC,CAAC,EAAE,IAAI,CAAC;UACR,OAAOhB,EAAE,CAAC,IAAI,CAAC;QACjB,CAAC,CAAC,CACH,CACAmB,SAAS,CAAEC,OAAO,IAAI;UACrB,IAAIA,OAAO,EAAE;YACXZ,OAAO,CAACC,GAAG,CACT,0DAA0D,CAC3D;YACD,IAAI,CAACJ,eAAe,GAClB,qDAAqD;YACvDU,UAAU,CAAC,MAAK;cACd,IAAI,CAACb,MAAM,CAACc,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;YACtC,CAAC,EAAE,IAAI,CAAC;UACV;QACF,CAAC,CAAC;QACJ;MACF;MAEA;MACAR,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;MACrE,IAAI,CAACJ,eAAe,GAAG,wCAAwC;MAC/DU,UAAU,CAAC,MAAK;QACd,IAAI,CAACb,MAAM,CAACc,QAAQ,CAAC,CAAC,cAAc,CAAC,CAAC;MACxC,CAAC,EAAE,IAAI,CAAC;IACV;;uCAnEWf,qBAAqB,EAAAoB,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,mBAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;;YAArB3B,qBAAqB;MAAA4B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAN1Bd,EAJJ,CAAAgB,cAAA,aAEC,aACkC,aACoB;UAAAhB,EAAA,CAAAiB,MAAA,8BAAa;UAAAjB,EAAA,CAAAkB,YAAA,EAAM;UACtElB,EAAA,CAAAgB,cAAA,aAA4C;UAAAhB,EAAA,CAAAiB,MAAA,GAAqB;UAErEjB,EAFqE,CAAAkB,YAAA,EAAM,EACnE,EACF;;;UAF0ClB,EAAA,CAAAmB,SAAA,GAAqB;UAArBnB,EAAA,CAAAoB,iBAAA,CAAAL,GAAA,CAAA/B,eAAA,CAAqB;;;;;;SAK5DJ,qBAAqB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}