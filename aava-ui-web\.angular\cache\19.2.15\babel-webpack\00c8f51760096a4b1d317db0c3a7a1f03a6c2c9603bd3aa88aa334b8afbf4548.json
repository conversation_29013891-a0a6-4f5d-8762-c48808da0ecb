{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormControl, Validators } from '@angular/forms';\nimport { Subscription } from 'rxjs';\nimport { PlaygroundComponent } from '@shared/components/playground/playground.component';\nimport { AvaTextareaComponent, AvaTextboxComponent, ButtonComponent, IconComponent, PopupComponent } from '@ava/play-comp-library';\nimport { CodeEditorComponent } from '@shared/components/code-editor/code-editor.component';\nimport guardrailsLabels from '../constants/guardrails-base.json';\nimport { LucideAngularModule } from 'lucide-angular';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@shared/services/tool-execution/tool-execution.service\";\nimport * as i4 from \"@shared/services/prompt-enhance.service\";\nimport * as i5 from \"@shared/services/guardrails.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"lucide-angular\";\nconst _c0 = [\"colangEditor\"];\nconst _c1 = [\"ymlEditor\"];\nconst _c2 = a0 => ({\n  \"three-column-layout\": a0\n});\nfunction CreateGuardrailsComponent_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 26);\n    i0.ɵɵtext(1, \"Guardrail Description\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreateGuardrailsComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵelement(1, \"ava-textbox\", 28)(2, \"ava-textarea\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formControl\", ctx_r1.getControl(\"name\"))(\"label\", ctx_r1.labels.guardrailName)(\"placeholder\", ctx_r1.labels.gnPlacholder)(\"fullWidth\", true)(\"required\", true)(\"error\", ctx_r1.getFieldError(\"name\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", ctx_r1.labels.description)(\"formControl\", ctx_r1.getControl(\"description\"))(\"placeholder\", ctx_r1.labels.gdPlaceholder)(\"rows\", 4)(\"fullWidth\", true)(\"required\", true)(\"error\", ctx_r1.getFieldError(\"description\"));\n  }\n}\nfunction CreateGuardrailsComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵelement(1, \"ava-icon\", 31);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.codeEditorState.errorMessage, \" \");\n  }\n}\nexport let CreateGuardrailsComponent = /*#__PURE__*/(() => {\n  class CreateGuardrailsComponent {\n    fb;\n    router;\n    route;\n    toolExecutionService;\n    promptGenerateService;\n    guardrailsService;\n    colangEditor;\n    ymlEditor;\n    // Labels from constants file\n    grLabels = guardrailsLabels.labels;\n    // Mode flags\n    guardrailId = null;\n    isEditMode = false;\n    isExecuteMode = false;\n    showChatInterface = false;\n    isLeftCollapsed = false;\n    toggleLeftPanel() {\n      this.isLeftCollapsed = !this.isLeftCollapsed;\n    }\n    guardrailForm;\n    // Code format options\n    codeFormats = ['Colang', 'YML'];\n    selectedCodeFormat = 'Colang';\n    // Chat interface properties\n    chatMessages = [];\n    isProcessingChat = false;\n    // Subscription\n    executionSubscription = new Subscription();\n    validationOutput = '';\n    showValidationOutput = false;\n    validationOutputEditorConfig = {\n      title: 'Tool Compiler',\n      language: 'json',\n      theme: 'light',\n      readOnly: true,\n      height: '250px'\n    };\n    codeFormatTabs = [{\n      label: 'Colang',\n      value: 'colang'\n    }, {\n      label: 'YML',\n      value: 'yml'\n    }];\n    selectedTab = 'colang';\n    rawColangContent = '';\n    rawYamlContent = '';\n    // For shared save/update popup\n    showConfirmPopup = false;\n    confirmPopupTitle = '';\n    confirmPopupMessage = '';\n    confirmButtonLabel = '';\n    confirmButtonColor = '';\n    actionType = null;\n    // Success Popup for Save/Update\n    showInfoPopup = false;\n    infoMessage = '';\n    labels = guardrailsLabels.labels;\n    originalFormValue;\n    codeEditorState = {\n      loading: false,\n      error: false,\n      errorMessage: null,\n      processing: false\n    };\n    constructor(fb, router, route, toolExecutionService, promptGenerateService, guardrailsService) {\n      this.fb = fb;\n      this.router = router;\n      this.route = route;\n      this.toolExecutionService = toolExecutionService;\n      this.promptGenerateService = promptGenerateService;\n      this.guardrailsService = guardrailsService;\n      this.guardrailForm = this.fb.group({\n        // Guardrail details\n        name: ['', Validators.required],\n        description: ['', Validators.required],\n        // Filters\n        organization: [''],\n        domain: [''],\n        project: [''],\n        team: [''],\n        // Code content\n        codeContent: new FormControl('', Validators.required),\n        yamlContent: ['']\n        // codeContent: ['<!DOCTYPE html>\\n<html lang=\"en\">\\n<head>\\n  <meta charset=\"UTF-8\">\\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\\n  <title>Agentic Activity Log</title>\\n  <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css\" rel=\"stylesheet\">\\n</head>\\n<body>\\n\\n<div class=\"container mt-5\">\\n  <h2 class=\"text-center mb-4\">Agentic Activity Log</h2>\\n  \\n  <div class=\"card shadow-lg\">\\n    <div class=\"card-body\">\\n      <h5 class=\"card-title\">Lead Qualification Agent</h5>\\n      <p><strong>Execution ID:</strong> LQ-20250323-001</p>\\n      <p><strong>Status:</strong> <span class=\"badge bg-success\">Completed</span></p>\\n      \\n      <table class=\"table table-bordered mt-3\">\\n        <thead class=\"table-dark\">\\n          <tr>\\n            <th>Step</th>\\n            <th>Details</th>\\n            <th>Time</th>']\n      });\n    }\n    // Define language mapping (Colang and YML can be treated as plaintext or yaml depending on real syntax support)\n    codeLanguagesMap = {\n      Colang: 'plaintext',\n      YML: 'yaml'\n    };\n    get currentCodeControl() {\n      return this.selectedCodeFormat === 'YML' ? this.guardrailForm.get('yamlContent') : this.guardrailForm.get('codeContent');\n    }\n    getColangControl() {\n      return this.guardrailForm.get('codeContent');\n    }\n    getYmlControl() {\n      return this.guardrailForm.get('yamlContent');\n    }\n    ngOnInit() {\n      // Check if we're in edit mode\n      this.guardrailId = this.route.snapshot.paramMap.get('id');\n      const executeParam = this.route.snapshot.queryParamMap.get('execute');\n      this.isEditMode = !!this.guardrailId;\n      this.isExecuteMode = executeParam === 'true';\n      this.showChatInterface = this.isExecuteMode;\n      if (this.isEditMode && this.guardrailId) {\n        console.log('iseditmode', this.isEditMode);\n        // Load guardrail data for editing\n        console.log(`Editing guardrail with ID: ${this.guardrailId}`);\n        this.loadGuardrailData(this.guardrailId);\n        // If in execute mode, start the execution\n        if (this.isExecuteMode) {\n          // Initialize messages\n          this.chatMessages = [{\n            from: 'ai',\n            text: 'Hi Akash, this is the guardrail testing'\n          }, {\n            from: 'user',\n            text: 'Test this input'\n          }, {\n            from: 'ai',\n            text: 'Here is the output'\n          }];\n          // Start execution (after a small delay to ensure UI is ready)\n          setTimeout(() => {\n            this.toolExecutionService.startExecution(this.guardrailId, this.chatMessages);\n            // Subscribe to execution state changes\n            this.executionSubscription = this.toolExecutionService.getExecutionState().subscribe(state => {\n              if (state.isExecuting && state.toolId === this.guardrailId) {\n                this.chatMessages = state.chatMessages;\n              }\n            });\n          }, 100);\n        }\n      }\n    }\n    ngOnDestroy() {\n      // Clean up subscription\n      if (this.executionSubscription) {\n        this.executionSubscription.unsubscribe();\n      }\n    }\n    onSave() {\n      const name = this.guardrailForm.get('name')?.value;\n      const description = this.guardrailForm.get('description')?.value;\n      const content = this.guardrailForm.get('codeContent')?.value;\n      const organization = this.guardrailForm.get('organization')?.value || 'Ascendion';\n      const configKey = this.guardrailForm.get('configKey')?.value;\n      const yamlContent = this.guardrailForm.get('yamlContent')?.value;\n      const chatBot = this.guardrailForm.get('chatBot')?.value;\n      const payload = {\n        name,\n        description,\n        content,\n        organization,\n        configKey,\n        yamlContent,\n        chatBot\n      };\n      if (this.guardrailForm.invalid) {\n        this.guardrailForm.markAllAsTouched();\n        return;\n      }\n      if (this.isEditMode && this.guardrailId) {\n        this.guardrailsService.updateGuardrail({\n          id: Number(this.guardrailId),\n          ...payload\n        }).subscribe({\n          next: () => {\n            this.infoMessage = `Guardrail \"${payload.name}\" has been successfully updated.`;\n            this.showInfoPopup = true;\n          },\n          error: err => console.error('Update failed', err)\n        });\n      } else {\n        this.guardrailsService.addGuardrail(payload).subscribe({\n          next: () => {\n            this.infoMessage = `Guardrail \"${payload.name}\" has been successfully created.`;\n            this.showInfoPopup = true;\n          },\n          error: err => console.error('Create failed', err)\n        });\n      }\n    }\n    handleInfoPopup() {\n      this.showInfoPopup = false;\n      this.router.navigate(['/libraries/guardrails']);\n    }\n    onExecute() {\n      console.log('Executing guardrail:', this.guardrailForm.value);\n      if (this.guardrailId) {\n        // If we're already in execute mode with chat interface showing\n        if (this.isExecuteMode && this.showChatInterface) {\n          // Process the execution\n          console.log('Processing execution');\n        } else {\n          console.log('Entering execute mode, showing chat interface');\n          // Set flags to show chat interface\n          this.isExecuteMode = true;\n          this.showChatInterface = true;\n          // Set the initial messages\n          this.chatMessages = [{\n            from: 'ai',\n            text: 'Hi Akash, this is the guardrail testing'\n          }, {\n            from: 'user',\n            text: 'Test this input'\n          }, {\n            from: 'ai',\n            text: 'Here is the output'\n          }];\n          // Delay starting the execution service slightly to allow UI to update\n          setTimeout(() => {\n            console.log('Starting execution service for guardrail ID:', this.guardrailId);\n            this.toolExecutionService.startExecution(this.guardrailId, this.chatMessages);\n          }, 100);\n        }\n      }\n    }\n    onExit() {\n      // If we're in execute mode with chat interface showing\n      if (this.isExecuteMode && this.isEditMode) {\n        // Return to edit mode without chat interface\n        this.isExecuteMode = false;\n        this.showChatInterface = false;\n        this.toolExecutionService.stopExecution();\n        console.log('Exited execution mode, returning to edit mode');\n      } else {\n        // Get the return page if available\n        const returnPage = this.route.snapshot.queryParamMap.get('returnPage');\n        const pageNumber = returnPage ? parseInt(returnPage) : 1;\n        // Exit to guardrails list at correct page\n        this.router.navigate(['/libraries/guardrails'], {\n          queryParams: {\n            page: pageNumber\n          }\n        });\n      }\n    }\n    // Helper method to get form controls easily from the template\n    getControl(name) {\n      return this.guardrailForm.get(name);\n    }\n    // Method to change the selected code format\n    setCodeFormat(format) {\n      // Save the current editor content into the appropriate form control\n      const currentEditorContent = this.selectedCodeFormat === 'Colang' ? this.colangEditor?.getValue() || '' : this.ymlEditor?.getValue() || '';\n      if (this.selectedCodeFormat === 'Colang') {\n        this.rawColangContent = currentEditorContent;\n        this.guardrailForm.get('codeContent')?.setValue(currentEditorContent);\n      } else {\n        this.rawYamlContent = currentEditorContent;\n        this.guardrailForm.get('yamlContent')?.setValue(currentEditorContent);\n      }\n      // Change the format\n      this.selectedCodeFormat = format;\n      // Get the new content for the selected format\n      const newContent = format === 'Colang' ? this.rawColangContent : this.rawYamlContent;\n      // Update the editor value\n      setTimeout(() => {\n        this.colangEditor?.setValue(newContent || '');\n      });\n    }\n    // Load guardrail data from mock data\n    loadGuardrailData(guardrailId) {\n      this.guardrailsService.getGuardrailById(Number(guardrailId)).subscribe({\n        next: guardrail => {\n          if (!guardrail) {\n            console.error('No guardrail data found for ID:', guardrailId);\n            return;\n          }\n          // Set raw contents\n          this.rawColangContent = guardrail.content || '';\n          this.rawYamlContent = guardrail.yamlContent || '';\n          // Patch form values\n          this.guardrailForm.patchValue({\n            name: guardrail.name || '',\n            description: guardrail.description || '',\n            organization: guardrail.organization || 'Ascendion',\n            configKey: guardrail.configKey || '',\n            chatBot: guardrail.chatBot || false,\n            codeContent: this.rawColangContent,\n            yamlContent: this.rawYamlContent\n          });\n          // Save original form value for change tracking\n          this.originalFormValue = this.guardrailForm.getRawValue();\n          // Set content for both editors\n          setTimeout(() => {\n            if (this.colangEditor) {\n              this.colangEditor.setValue(this.rawColangContent || '');\n            }\n            if (this.ymlEditor) {\n              this.ymlEditor.setValue(this.rawYamlContent || '');\n            }\n          }, 100);\n        },\n        error: err => {\n          console.error('Failed to load guardrail data:', err);\n        }\n      });\n    }\n    tryParseJSONString(str) {\n      try {\n        const parsed = JSON.parse(str);\n        return typeof parsed === 'string' ? parsed : str;\n      } catch {\n        return str;\n      }\n    }\n    addChatMessage(from, text) {\n      this.chatMessages = [...this.chatMessages, {\n        from,\n        text\n      }];\n    }\n    // Handle chat messages\n    handleChatMessage(message) {\n      if (this.isPlaygroundDisabled) {\n        return;\n      }\n      this.addChatMessage('user', message);\n      this.isProcessingChat = true;\n      this.executeGuradrails(message);\n    }\n    executeGuradrails(message) {\n      // Get current content from both editors\n      const colangContent = this.colangEditor?.getValue() || this.guardrailForm.get('codeContent')?.value;\n      const yamlContent = this.ymlEditor?.getValue() || this.guardrailForm.get('yamlContent')?.value;\n      const payload = {\n        prompt: message,\n        mode: \"DEFAULT\",\n        promptOverride: true,\n        colangContent: colangContent,\n        yamlContent: yamlContent\n      };\n      this.promptGenerateService.modelGuardrailApi(payload.prompt, payload.mode, payload.promptOverride, payload.yamlContent, payload.colangContent).subscribe({\n        next: response => {\n          if (response && response.response && response.response.choices && response.response.choices.length > 0) {\n            const aiText = response.response.choices[0].text;\n            this.addChatMessage('ai', aiText);\n            // Keep loader active for a brief moment to ensure message is displayed\n            setTimeout(() => {\n              this.isProcessingChat = false;\n            }, 500);\n          } else {\n            this.addChatMessage('ai', 'No response received');\n            setTimeout(() => {\n              this.isProcessingChat = false;\n            }, 500);\n          }\n        },\n        error: error => {\n          console.error('Error executing guardrail:', error);\n          this.addChatMessage('ai', 'Error processing your request');\n          setTimeout(() => {\n            this.isProcessingChat = false;\n          }, 500);\n        }\n      });\n    }\n    //Drop Down\n    promptOptions = [{\n      value: 'default',\n      name: 'Choose Prompt'\n    }, {\n      value: 'ruby-developer',\n      name: 'Senior Ruby Developer'\n    }, {\n      value: 'python-developer',\n      name: 'Python Developer'\n    }, {\n      value: 'data-scientist',\n      name: 'Data Scientist'\n    }, {\n      value: 'frontend-developer',\n      name: 'Frontend Developer'\n    }];\n    onPromptChanged(option) {\n      console.log('Prompt changed in parent:', option);\n      // your logic to handle selected prompt\n    }\n    showInterface = () => {\n      setTimeout(() => {\n        this.isProcessingChat = false;\n        this.isExecuteMode = true;\n        this.showChatInterface = true;\n        setTimeout(() => {\n          if (this.colangEditor?.isReady) {\n            this.colangEditor.focus();\n            this.colangEditor['editor']?.layout();\n            this.colangEditor.hideProcessingLoader();\n          }\n          if (this.ymlEditor?.isReady) {\n            this.ymlEditor['editor']?.layout();\n          }\n        }, 100);\n      });\n    };\n    // onTabSelected(tabValue: string): void { ... } method removed\n    isSubmitDisabled() {\n      const isFormInvalid = this.guardrailForm.invalid;\n      const isUnchanged = JSON.stringify(this.guardrailForm.getRawValue()) === JSON.stringify(this.originalFormValue);\n      return isFormInvalid || isUnchanged;\n    }\n    getFieldError(fieldName) {\n      const field = this.guardrailForm.get(fieldName);\n      // Capitalize only if first letter is not already uppercase\n      const formattedFieldName = /^[A-Z]/.test(fieldName) ? fieldName : fieldName.charAt(0).toUpperCase() + fieldName.slice(1);\n      if (field && field.invalid && (field.touched || field.dirty)) {\n        if (field.errors?.['required']) {\n          return `${formattedFieldName} is required`;\n        }\n      }\n      return '';\n    }\n    // Trigger Save Confirmation\n    confirmSave() {\n      this.actionType = 'save';\n      this.confirmPopupTitle = 'Save Guardrail?';\n      this.confirmPopupMessage = 'Are you sure you want to save this guardrail?';\n      this.confirmButtonLabel = 'Save';\n      this.confirmButtonColor = '#007bff';\n      this.showConfirmPopup = true;\n    }\n    // Trigger Update Confirmation\n    confirmUpdate() {\n      this.actionType = 'update';\n      this.confirmPopupTitle = 'Update Guardrail?';\n      this.confirmPopupMessage = 'Are you sure you want to update this guardrail?';\n      this.confirmButtonLabel = 'Update';\n      this.confirmButtonColor = '#28a745';\n      this.showConfirmPopup = true;\n    }\n    // On confirm button clicked\n    onConfirmAction() {\n      this.showConfirmPopup = false;\n      this.onSave(); // reuse existing logic\n    }\n    // Close/cancel\n    closePopup() {\n      this.showConfirmPopup = false;\n    }\n    get isColangTestDisabled() {\n      const isColang = this.selectedCodeFormat === 'Colang';\n      const colangCode = this.guardrailForm.get('codeContent')?.value?.trim();\n      return isColang && (!colangCode || colangCode.length === 0);\n    }\n    onColangContentChanged(value) {\n      const control = this.guardrailForm.get('codeContent');\n      control?.setValue(value);\n      control?.markAsDirty();\n      control?.markAsTouched(); // Add this line to mark as touched\n    }\n    showCodeEditorError() {\n      const ctrl = this.guardrailForm.get('codeContent');\n      const editorValue = this.colangEditor?.getValue() || '';\n      const formValue = ctrl?.value || '';\n      // Check if control is touched and either form value or editor value is empty\n      const isEmpty = !editorValue.trim() && !formValue.trim();\n      const hasError = !!(ctrl?.touched && isEmpty);\n      this.codeEditorState.error = hasError;\n      this.codeEditorState.errorMessage = hasError ? 'Colang Code is required.' : null;\n      return hasError;\n    }\n    get isPlaygroundDisabled() {\n      const colangContent = this.colangEditor?.getValue() || this.guardrailForm.get('codeContent')?.value;\n      return !colangContent || colangContent.trim().length === 0;\n    }\n    static ɵfac = function CreateGuardrailsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CreateGuardrailsComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.ToolExecutionService), i0.ɵɵdirectiveInject(i4.PromptEnhanceService), i0.ɵɵdirectiveInject(i5.GuardrailsService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CreateGuardrailsComponent,\n      selectors: [[\"app-create-guardrails\"]],\n      viewQuery: function CreateGuardrailsComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.colangEditor = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.ymlEditor = _t.first);\n        }\n      },\n      decls: 29,\n      vars: 46,\n      consts: [[\"colangEditor\", \"\"], [\"ymlEditor\", \"\"], [1, \"page-title\"], [1, \"create-guardrails-container\"], [3, \"formGroup\"], [1, \"form-layout\", 3, \"ngClass\"], [1, \"left-column\"], [1, \"left-header\"], [\"class\", \"left-title\", 4, \"ngIf\"], [\"name\", \"panel-left\", 1, \"collapse-icon\", 3, \"click\"], [\"class\", \"card-content\", 4, \"ngIf\"], [1, \"middle-column\"], [1, \"split-editors\"], [1, \"editors-header-row\"], [1, \"editors-title\"], [\"size\", \"medium\", \"state\", \"active\", \"variant\", \"primary\", 3, \"userClick\", \"label\", \"disabled\"], [1, \"colang-panel\"], [1, \"code-editor-container\"], [3, \"valueChange\", \"title\", \"titleRequired\", \"language\", \"Control\", \"customCssClass\", \"placeholder\"], [\"class\", \"error-text\", 4, \"ngIf\"], [1, \"yml-panel\"], [3, \"title\", \"language\", \"Control\", \"customCssClass\", \"placeholder\"], [1, \"rightEnd-column\"], [3, \"promptChange\", \"messageSent\", \"promptOptions\", \"messages\", \"isLoading\", \"showChatInteractionToggles\", \"showAiPrincipleToggle\", \"showApprovalButton\", \"isMinimalView\", \"isDisabled\"], [3, \"confirm\", \"cancel\", \"closed\", \"show\", \"title\", \"message\", \"showHeaderIcon\", \"headerIconName\", \"iconColor\", \"showClose\", \"showCancel\", \"showConfirm\", \"confirmButtonLabel\", \"confirmButtonVariant\", \"confirmButtonBackground\"], [\"messageAlignment\", \"center\", \"title\", \"SUCCESS!\", \"headerIconName\", \"circle-check\", \"iconColor\", \"green\", 3, \"closed\", \"show\", \"message\", \"showHeaderIcon\", \"showClose\"], [1, \"left-title\"], [1, \"card-content\"], [\"id\", \"guardrailName\", \"name\", \"GuardrailName\", \"variant\", \"primary\", \"size\", \"md\", 3, \"formControl\", \"label\", \"placeholder\", \"fullWidth\", \"required\", \"error\"], [\"id\", \"description\", \"name\", \"description\", \"size\", \"md\", 3, \"label\", \"formControl\", \"placeholder\", \"rows\", \"fullWidth\", \"required\", \"error\"], [1, \"error-text\"], [\"iconName\", \"info\", \"iconColor\", \"red\", \"iconSize\", \"14\"]],\n      template: function CreateGuardrailsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"p\", 2);\n          i0.ɵɵtext(1, \"Guardrail Configuration\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"div\", 3)(3, \"form\", 4)(4, \"div\", 5)(5, \"div\", 6)(6, \"div\", 7);\n          i0.ɵɵtemplate(7, CreateGuardrailsComponent_span_7_Template, 2, 0, \"span\", 8);\n          i0.ɵɵelementStart(8, \"lucide-icon\", 9);\n          i0.ɵɵlistener(\"click\", function CreateGuardrailsComponent_Template_lucide_icon_click_8_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.toggleLeftPanel());\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(9, CreateGuardrailsComponent_div_9_Template, 3, 13, \"div\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 11)(11, \"div\", 12)(12, \"div\", 13)(13, \"div\", 14);\n          i0.ɵɵtext(14, \"Guardrail Configuration\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"ava-button\", 15);\n          i0.ɵɵlistener(\"userClick\", function CreateGuardrailsComponent_Template_ava_button_userClick_15_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.isEditMode ? ctx.confirmUpdate() : ctx.confirmSave());\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 16)(17, \"div\", 17)(18, \"app-code-editor\", 18, 0);\n          i0.ɵɵlistener(\"valueChange\", function CreateGuardrailsComponent_Template_app_code_editor_valueChange_18_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onColangContentChanged($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(20, CreateGuardrailsComponent_div_20_Template, 3, 1, \"div\", 19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(21, \"div\", 20)(22, \"div\", 17);\n          i0.ɵɵelement(23, \"app-code-editor\", 21, 1);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(25, \"div\", 22)(26, \"app-playground\", 23);\n          i0.ɵɵlistener(\"promptChange\", function CreateGuardrailsComponent_Template_app_playground_promptChange_26_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onPromptChanged($event));\n          })(\"messageSent\", function CreateGuardrailsComponent_Template_app_playground_messageSent_26_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.handleChatMessage($event));\n          });\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(27, \"ava-popup\", 24);\n          i0.ɵɵlistener(\"confirm\", function CreateGuardrailsComponent_Template_ava_popup_confirm_27_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onConfirmAction());\n          })(\"cancel\", function CreateGuardrailsComponent_Template_ava_popup_cancel_27_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.closePopup());\n          })(\"closed\", function CreateGuardrailsComponent_Template_ava_popup_closed_27_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.closePopup());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"ava-popup\", 25);\n          i0.ɵɵlistener(\"closed\", function CreateGuardrailsComponent_Template_ava_popup_closed_28_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.handleInfoPopup());\n          });\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"formGroup\", ctx.guardrailForm);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(44, _c2, ctx.isExecuteMode && ctx.showChatInterface));\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"collapsed\", ctx.isLeftCollapsed);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLeftCollapsed);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLeftCollapsed);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"label\", ctx.isEditMode ? \"Update\" : \"Save\")(\"disabled\", ctx.isSubmitDisabled());\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"title\", \"Colang Editor\")(\"titleRequired\", true)(\"language\", ctx.codeLanguagesMap[\"Colang\"])(\"Control\", ctx.getColangControl())(\"customCssClass\", \"tools-monaco-editor\")(\"placeholder\", \"Write your Colang code here...\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.showCodeEditorError());\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"title\", \"YML Editor\")(\"language\", ctx.codeLanguagesMap[\"YML\"])(\"Control\", ctx.getYmlControl())(\"customCssClass\", \"tools-monaco-editor\")(\"placeholder\", \"Write your YML code here...\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"promptOptions\", ctx.promptOptions)(\"messages\", ctx.chatMessages)(\"isLoading\", ctx.isProcessingChat)(\"showChatInteractionToggles\", false)(\"showAiPrincipleToggle\", true)(\"showApprovalButton\", false)(\"isMinimalView\", true)(\"isDisabled\", ctx.isPlaygroundDisabled);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"show\", ctx.showConfirmPopup)(\"title\", ctx.confirmPopupTitle)(\"message\", ctx.confirmPopupMessage)(\"showHeaderIcon\", true)(\"headerIconName\", ctx.actionType === \"save\" ? \"save\" : \"edit\")(\"iconColor\", ctx.confirmButtonColor)(\"showClose\", true)(\"showCancel\", true)(\"showConfirm\", true)(\"confirmButtonLabel\", ctx.confirmButtonLabel)(\"confirmButtonVariant\", \"primary\")(\"confirmButtonBackground\", ctx.confirmButtonColor);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"show\", ctx.showInfoPopup)(\"message\", ctx.infoMessage)(\"showHeaderIcon\", true)(\"showClose\", true);\n        }\n      },\n      dependencies: [CommonModule, i6.NgClass, i6.NgIf, ReactiveFormsModule, i1.ɵNgNoValidate, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.FormControlDirective, i1.FormGroupDirective, PlaygroundComponent, AvaTextboxComponent, ButtonComponent, AvaTextareaComponent, CodeEditorComponent, PopupComponent, LucideAngularModule, i7.LucideAngularComponent, IconComponent],\n      styles: [\".create-guardrails-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n  background-color: transparent;\\n}\\n\\n.page-title[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 20px;\\n}\\n\\n.yml-panel[_ngcontent-%COMP%] {\\n  border-top: 2px solid #e1e4e8;\\n}\\n\\n.chat-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  min-height: 400px;\\n  border: 1px solid #e1e4e8;\\n  border-radius: 4px;\\n  overflow: hidden;\\n  position: relative;\\n  background: #ffffff;\\n}\\n\\n.playground-title[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: var(--dashboard-primary);\\n  margin: 0 0 16px;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\napp-chat-interface[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  flex: 1;\\n}\\n\\nform[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: calc(100vh - 260px);\\n  overflow: hidden;\\n}\\n\\n.form-layout[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  height: 100%;\\n  flex: 1 1 0;\\n  min-width: 0;\\n  min-height: 0;\\n  gap: 0;\\n  padding: 0;\\n  border: 1px solid #e1e4e8;\\n  background: #ffffff;\\n}\\n.form-layout.three-column-layout[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%] {\\n  width: 25%;\\n  transition: width 0.3s ease;\\n}\\n@media (max-width: 1400px) {\\n  .form-layout.three-column-layout[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%] {\\n    width: 25%;\\n  }\\n}\\n@media (max-width: 1200px) {\\n  .form-layout.three-column-layout[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n.form-layout.three-column-layout[_ngcontent-%COMP%]   .middle-column[_ngcontent-%COMP%] {\\n  width: 40%;\\n  transition: width 0.3s ease;\\n}\\n@media (max-width: 1400px) {\\n  .form-layout.three-column-layout[_ngcontent-%COMP%]   .middle-column[_ngcontent-%COMP%] {\\n    width: 35%;\\n  }\\n}\\n@media (max-width: 1200px) {\\n  .form-layout.three-column-layout[_ngcontent-%COMP%]   .middle-column[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n.form-layout.three-column-layout[_ngcontent-%COMP%]   .rightEnd-column[_ngcontent-%COMP%] {\\n  width: 30%;\\n  flex-shrink: 0;\\n  display: flex;\\n  flex-direction: column;\\n  overflow-y: auto;\\n}\\n@media (max-width: 1400px) {\\n  .form-layout.three-column-layout[_ngcontent-%COMP%]   .rightEnd-column[_ngcontent-%COMP%] {\\n    width: 60%;\\n  }\\n}\\n@media (max-width: 1200px) {\\n  .form-layout.three-column-layout[_ngcontent-%COMP%]   .rightEnd-column[_ngcontent-%COMP%] {\\n    width: 60%;\\n  }\\n}\\n@media (max-width: 1400px) {\\n  .form-layout[_ngcontent-%COMP%] {\\n    gap: 16px;\\n    padding: 16px;\\n  }\\n}\\n@media (max-width: 1200px) {\\n  .form-layout[_ngcontent-%COMP%] {\\n    flex-wrap: wrap;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .form-layout[_ngcontent-%COMP%] {\\n    gap: 12px;\\n    padding: 12px;\\n    flex-direction: column;\\n  }\\n}\\n.form-layout[_ngcontent-%COMP%]   .middle-column[_ngcontent-%COMP%] {\\n  padding: 0px !important;\\n}\\n.form-layout[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%], .form-layout[_ngcontent-%COMP%]   .middle-column[_ngcontent-%COMP%], .form-layout[_ngcontent-%COMP%]   .chat-column[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n  height: 100%;\\n  background: #ffffff;\\n  border-right: 1px solid #e1e4e8;\\n  overflow-y: auto;\\n}\\n.form-layout[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%]:last-child, .form-layout[_ngcontent-%COMP%]   .middle-column[_ngcontent-%COMP%]:last-child, .form-layout[_ngcontent-%COMP%]   .chat-column[_ngcontent-%COMP%]:last-child {\\n  border-right: none;\\n}\\n@media (max-width: 1400px) {\\n  .form-layout[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%], .form-layout[_ngcontent-%COMP%]   .middle-column[_ngcontent-%COMP%], .form-layout[_ngcontent-%COMP%]   .chat-column[_ngcontent-%COMP%] {\\n    gap: 16px;\\n    padding: 16px;\\n  }\\n}\\n@media (max-width: 1200px) {\\n  .form-layout[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%], .form-layout[_ngcontent-%COMP%]   .middle-column[_ngcontent-%COMP%], .form-layout[_ngcontent-%COMP%]   .chat-column[_ngcontent-%COMP%] {\\n    height: auto;\\n    padding: 16px;\\n    border-right: none;\\n    border-bottom: 1px solid #e1e4e8;\\n    overflow-y: visible;\\n  }\\n  .form-layout[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%]:last-child, .form-layout[_ngcontent-%COMP%]   .middle-column[_ngcontent-%COMP%]:last-child, .form-layout[_ngcontent-%COMP%]   .chat-column[_ngcontent-%COMP%]:last-child {\\n    border-bottom: none;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .form-layout[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%], .form-layout[_ngcontent-%COMP%]   .middle-column[_ngcontent-%COMP%], .form-layout[_ngcontent-%COMP%]   .chat-column[_ngcontent-%COMP%] {\\n    gap: 12px;\\n    width: 100% !important;\\n    padding: 12px;\\n  }\\n}\\n.form-layout[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%] {\\n  width: 40%;\\n  flex-shrink: 0;\\n  transition: width 0.3s ease;\\n}\\n@media (max-width: 1400px) {\\n  .form-layout[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%] {\\n    width: 40%;\\n  }\\n}\\n@media (max-width: 1200px) {\\n  .form-layout[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n.form-layout[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%]   app-card[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n}\\n.form-layout[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%]   app-card[_ngcontent-%COMP%]:first-of-type {\\n  flex: 0 0 auto;\\n}\\n.form-layout[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%]   app-card[_ngcontent-%COMP%]:last-of-type {\\n  flex: 1;\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.form-layout[_ngcontent-%COMP%]   .left-column[_ngcontent-%COMP%]   app-card[_ngcontent-%COMP%]:last-of-type   .card-content[_ngcontent-%COMP%] {\\n  overflow-y: auto;\\n}\\n.form-layout[_ngcontent-%COMP%]   .middle-column[_ngcontent-%COMP%] {\\n  flex: 1 1 0;\\n  min-width: 0;\\n  display: flex;\\n  flex-direction: column;\\n  padding: 0 !important;\\n}\\n.form-layout[_ngcontent-%COMP%]   .middle-column[_ngcontent-%COMP%]   .middle-column-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n}\\n.form-layout[_ngcontent-%COMP%]   .middle-column[_ngcontent-%COMP%]   app-card[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.form-layout[_ngcontent-%COMP%]   .rightEnd-column[_ngcontent-%COMP%] {\\n  width: 30%;\\n  flex-shrink: 0;\\n  display: flex;\\n  flex-direction: column;\\n  overflow-y: auto;\\n}\\n@media (max-width: 1400px) {\\n  .form-layout[_ngcontent-%COMP%]   .rightEnd-column[_ngcontent-%COMP%] {\\n    width: 60%;\\n  }\\n}\\n@media (max-width: 1200px) {\\n  .form-layout[_ngcontent-%COMP%]   .rightEnd-column[_ngcontent-%COMP%] {\\n    width: 60%;\\n  }\\n}\\n.form-layout[_ngcontent-%COMP%]   .chat-column[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  display: flex;\\n  flex-direction: column;\\n  background-color: var(--agent-chat-column-bg);\\n  border-left: 1px solid var(--agent-chat-column-border);\\n  box-shadow: -2px 0 10px var(--agent-chat-column-shadow);\\n  min-width: 250px;\\n  height: 100%;\\n  width: 30%;\\n}\\n.form-layout[_ngcontent-%COMP%]   .chat-column[_ngcontent-%COMP%]   app-card[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n}\\n@media (max-width: 1200px) {\\n  .form-layout[_ngcontent-%COMP%]   .chat-column[_ngcontent-%COMP%] {\\n    width: 100%;\\n    min-width: unset;\\n    border-left: none;\\n    border-top: 1px solid var(--agent-chat-column-border);\\n    box-shadow: 0 -2px 10px var(--agent-chat-column-shadow);\\n  }\\n}\\n.form-layout[_ngcontent-%COMP%]   .chat-column[_ngcontent-%COMP%]   .chat-column-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n}\\n.form-layout[_ngcontent-%COMP%]   .chat-column[_ngcontent-%COMP%]   app-card[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.card-content[_ngcontent-%COMP%] {\\n  padding: 0 1rem;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n@media (max-width: 576px) {\\n  .card-content[_ngcontent-%COMP%] {\\n    padding: 12px;\\n    gap: 12px;\\n  }\\n}\\n\\n.middle-column[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%], \\n.chat-column[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.chat-column[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%] {\\n  padding-bottom: 0;\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  flex: 1;\\n}\\n.chat-column[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .chat-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  flex: 1;\\n  height: 100%;\\n  overflow: hidden;\\n}\\n\\n.head-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n}\\n.head-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin: 0 0 8px;\\n  color: var(--text-color);\\n}\\n@media (max-width: 576px) {\\n  .head-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n    margin-bottom: 6px;\\n  }\\n}\\n\\n.code-format-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n  margin-bottom: 16px;\\n  flex-shrink: 0;\\n}\\n.code-format-buttons[_ngcontent-%COMP%]   .format-button[_ngcontent-%COMP%] {\\n  padding: 8px 16px;\\n  border-radius: 10px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  background-color: var(--form-input-bg);\\n  border: none;\\n  color: var(--text-secondary);\\n  transition: all 0.2s ease;\\n}\\n.code-format-buttons[_ngcontent-%COMP%]   .format-button[_ngcontent-%COMP%]:hover {\\n  background-color: var(--dropdown-hover-bg);\\n}\\n.code-format-buttons[_ngcontent-%COMP%]   .format-button.active[_ngcontent-%COMP%] {\\n  background-color: var(--dropdown-selected-bg);\\n  color: var(--dropdown-selected-text);\\n  font-weight: 600;\\n}\\n\\n.code-editor-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  flex: 1;\\n}\\n.code-editor-container[_ngcontent-%COMP%]   .code-editor-field[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  flex: 1;\\n}\\n.code-editor-container[_ngcontent-%COMP%]   app-form-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n}\\n.code-editor-container[_ngcontent-%COMP%]     .form-field {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  flex: 1;\\n}\\n.code-editor-container[_ngcontent-%COMP%]     textarea {\\n  font-family: \\\"Courier New\\\", monospace;\\n  line-height: 1.5;\\n  padding: 16px;\\n  background-color: var(--form-input-bg);\\n  border: 1px solid var(--form-input-border);\\n  flex: 1;\\n  min-height: 350px;\\n  resize: none;\\n}\\n\\n.validation-output-section[_ngcontent-%COMP%] {\\n  border-top: 1px solid #e1e4e8;\\n  margin-top: 16px;\\n  padding-top: 16px;\\n}\\n\\n.middle-column-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  gap: 16px;\\n  padding: 33px 13px 4px;\\n  margin-top: auto;\\n  margin-bottom: 0;\\n  flex-shrink: 0;\\n}\\n@media (max-width: 576px) {\\n  .middle-column-buttons[_ngcontent-%COMP%] {\\n    gap: 12px;\\n    padding: 12px 0 0;\\n    margin-top: auto;\\n    margin-bottom: 0;\\n  }\\n}\\n.middle-column-buttons[_ngcontent-%COMP%]   .exit-button[_ngcontent-%COMP%], .middle-column-buttons[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%], .middle-column-buttons[_ngcontent-%COMP%]   .execute-button[_ngcontent-%COMP%] {\\n  padding: 10px 24px;\\n  border-radius: 6px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n@media (max-width: 576px) {\\n  .middle-column-buttons[_ngcontent-%COMP%]   .exit-button[_ngcontent-%COMP%], .middle-column-buttons[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%], .middle-column-buttons[_ngcontent-%COMP%]   .execute-button[_ngcontent-%COMP%] {\\n    padding: 8px 16px;\\n    font-size: 13px;\\n  }\\n}\\n.middle-column-buttons[_ngcontent-%COMP%]   .exit-button[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  border: 1px solid var(--button-secondary-border);\\n  color: var(--button-secondary-text);\\n}\\n.middle-column-buttons[_ngcontent-%COMP%]   .exit-button[_ngcontent-%COMP%]:hover {\\n  background-color: var(--button-secondary-hover-bg);\\n}\\n.middle-column-buttons[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%], .middle-column-buttons[_ngcontent-%COMP%]   .execute-button[_ngcontent-%COMP%] {\\n  background: var(--button-gradient);\\n  border: none;\\n  color: var(--button-primary-text);\\n}\\n.middle-column-buttons[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%]:hover, .middle-column-buttons[_ngcontent-%COMP%]   .execute-button[_ngcontent-%COMP%]:hover {\\n  opacity: var(--button-hover-opacity);\\n}\\n.middle-column-buttons[_ngcontent-%COMP%]   .execute-button[_ngcontent-%COMP%] {\\n  background: var(--button-gradient);\\n  transition: all 0.2s ease;\\n}\\n.middle-column-buttons[_ngcontent-%COMP%]   .execute-button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 8px var(--dashboard-shadow-hover);\\n}\\n\\n.note[_ngcontent-%COMP%] {\\n  font-weight: 400;\\n  color: #A3A7C2;\\n  font-size: 12px;\\n}\\n\\n  .card-container {\\n  padding: 0px !important;\\n  box-shadow: none !important;\\n  height: 100% !important;\\n  display: flex !important;\\n  flex-direction: column !important;\\n}\\n  .card-container.clickable:hover:not(.no-hover) {\\n  box-shadow: none !important;\\n}\\n\\n.pill-tabs-container[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  overflow: hidden;\\n  padding: 4px;\\n  gap: 4px;\\n}\\n\\n.pill-tab-button[_ngcontent-%COMP%] {\\n  margin: 0;\\n  border: none;\\n  font-weight: 500;\\n  padding: 10px 20px;\\n  background-color: transparent;\\n  color: #6c757d;\\n  transition: all 0.2s ease;\\n  cursor: pointer;\\n  font-size: 14px;\\n  min-width: 80px;\\n  text-align: center;\\n}\\n\\n.pill-tab-button[_ngcontent-%COMP%]:hover:not(.active) {\\n  background-color: rgba(101, 102, 205, 0.05);\\n  color: #6566CD;\\n}\\n\\n.pill-tab-button.active[_ngcontent-%COMP%] {\\n  background-color: #6566CD;\\n  color: white;\\n  font-weight: 600;\\n  box-shadow: 0 2px 4px rgba(101, 102, 205, 0.3);\\n}\\n\\n.item-icon[_ngcontent-%COMP%] {\\n  display: none !important;\\n}\\n\\n[_nghost-%COMP%]     .nav-item {\\n  border-radius: 6px !important;\\n}\\n\\n.left-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  height: 48px;\\n  padding: 0 16px;\\n  background: #fff;\\n  border-bottom: 1px solid #e1e4e8;\\n  z-index: 2;\\n}\\n\\n.collapse-icon[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: #fff;\\n  border-radius: 50%;\\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\\n  cursor: pointer;\\n  z-index: 2;\\n  font-size: 18px;\\n  border: 1px solid #e1e4e8;\\n  margin-right: 0;\\n}\\n\\n.left-title[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 16px;\\n  color: #23272E;\\n  margin-left: 0;\\n}\\n\\n.left-column[_ngcontent-%COMP%] {\\n  width: 340px;\\n  min-width: 60px;\\n  max-width: 340px;\\n  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  height: 100vh;\\n  overflow: hidden;\\n  position: relative;\\n  background: #f8f9fa;\\n  border-right: 1px solid #e1e4e8;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.left-column.collapsed[_ngcontent-%COMP%] {\\n  width: 48px;\\n  min-width: 48px;\\n  max-width: 48px;\\n}\\n\\n.collapse-icon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 16px;\\n  right: 8px;\\n  width: 24px;\\n  height: 24px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: #fff;\\n  border-radius: 50%;\\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\\n  cursor: pointer;\\n  z-index: 2;\\n  font-size: 18px;\\n  border: 1px solid #e1e4e8;\\n}\\n\\n.left-column.collapsed[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n\\n.editors-header-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  height: 48px;\\n  background: #fff;\\n  border-bottom: 1px solid #e1e4e8;\\n  padding: 0 16px;\\n  margin-bottom: 0;\\n}\\n\\n.editors-title[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #23272E;\\n  background: #fff;\\n  border-bottom: 1px solid #e1e4e8;\\n  height: 48px;\\n  display: flex;\\n  align-items: center;\\n  position: sticky;\\n  top: 0;\\n  z-index: 2;\\n}\\n\\n.rightEnd-column[_ngcontent-%COMP%] {\\n  height: 100vh;\\n  border-left: 1px solid #e1e4e8;\\n  display: flex;\\n  flex-direction: column;\\n  flex-shrink: 0;\\n  flex-grow: 0;\\n  padding: 0;\\n  margin: 0;\\n}\\n\\n.rightEnd-column[_ngcontent-%COMP%]    > app-playground[_ngcontent-%COMP%] {\\n  flex: 1 1 0;\\n  min-height: 0;\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n.rightEnd-column[_ngcontent-%COMP%]    > app-playground[_ngcontent-%COMP%], \\n.rightEnd-column[_ngcontent-%COMP%]   .playground-container[_ngcontent-%COMP%] {\\n  flex: 1 1 0;\\n  min-height: 0;\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.error-text[_ngcontent-%COMP%] {\\n  color: #dc3545; \\n\\n  font-size: 0.875rem; \\n\\n  margin-top: 4px;\\n  margin-left: 4px;\\n  display: block;\\n  font-weight: 500;\\n}\\n\\n  ava-icon {\\n  margin-bottom: 4px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return CreateGuardrailsComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "FormControl", "Validators", "Subscription", "PlaygroundComponent", "AvaTextareaComponent", "AvaTextboxComponent", "ButtonComponent", "IconComponent", "PopupComponent", "CodeEditorComponent", "guardrailsLabels", "LucideAngularModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "ctx_r1", "getControl", "labels", "guardrailName", "gnPlacholder", "getFieldError", "description", "gdPlaceholder", "ɵɵtextInterpolate1", "codeEditorState", "errorMessage", "CreateGuardrailsComponent", "fb", "router", "route", "toolExecutionService", "promptGenerateService", "guardrailsService", "colangEditor", "ymlEditor", "g<PERSON><PERSON><PERSON><PERSON>", "guardrailId", "isEditMode", "isExecuteMode", "showChatInterface", "isLeftCollapsed", "toggleLeftPanel", "guardrailForm", "codeFormats", "selectedCodeFormat", "chatMessages", "isProcessingChat", "executionSubscription", "validationOutput", "showValidationOutput", "validationOutputEditorConfig", "title", "language", "theme", "readOnly", "height", "codeFormatTabs", "label", "value", "selectedTab", "rawColangContent", "rawYamlContent", "showConfirmPopup", "confirmPopupTitle", "confirmPopupMessage", "confirmButtonLabel", "confirmButtonColor", "actionType", "showInfoPopup", "infoMessage", "originalFormValue", "loading", "error", "processing", "constructor", "group", "name", "required", "organization", "domain", "project", "team", "codeContent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "codeLanguagesMap", "Colang", "YML", "currentCodeControl", "get", "getColangControl", "getYmlControl", "ngOnInit", "snapshot", "paramMap", "executeParam", "queryParamMap", "console", "log", "loadGuardrailData", "from", "text", "setTimeout", "startExecution", "getExecutionState", "subscribe", "state", "isExecuting", "toolId", "ngOnDestroy", "unsubscribe", "onSave", "content", "config<PERSON><PERSON>", "chatBot", "payload", "invalid", "mark<PERSON>llAsTouched", "updateGuardrail", "id", "Number", "next", "err", "addGuardrail", "handleInfoPopup", "navigate", "onExecute", "onExit", "stopExecution", "returnPage", "pageNumber", "parseInt", "queryParams", "page", "setCodeFormat", "format", "currentEditorContent", "getValue", "setValue", "newContent", "getGuardrailById", "guardrail", "patchValue", "getRawValue", "tryParseJSONString", "str", "parsed", "JSON", "parse", "addChatMessage", "handleChatMessage", "message", "isPlaygroundDisabled", "executeGuradrails", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "prompt", "mode", "promptOverride", "modelGuardrailApi", "response", "choices", "length", "aiText", "promptOptions", "onPromptChanged", "option", "showInterface", "isReady", "focus", "layout", "hideProcessingLoader", "isSubmitDisabled", "isFormInvalid", "isUnchanged", "stringify", "fieldName", "field", "formattedFieldName", "test", "char<PERSON>t", "toUpperCase", "slice", "touched", "dirty", "errors", "confirmSave", "confirmUpdate", "onConfirmAction", "closePopup", "isColangTestDisabled", "isColang", "colangCode", "trim", "onColangContentChanged", "control", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>ched", "showCodeEditorError", "ctrl", "<PERSON><PERSON><PERSON><PERSON>", "formValue", "isEmpty", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "Router", "ActivatedRoute", "i3", "ToolExecutionService", "i4", "PromptEnhanceService", "i5", "GuardrailsService", "selectors", "viewQuery", "CreateGuardrailsComponent_Query", "rf", "ctx", "ɵɵtemplate", "CreateGuardrailsComponent_span_7_Template", "ɵɵlistener", "CreateGuardrailsComponent_Template_lucide_icon_click_8_listener", "ɵɵrestoreView", "_r1", "ɵɵresetView", "CreateGuardrailsComponent_div_9_Template", "CreateGuardrailsComponent_Template_ava_button_userClick_15_listener", "CreateGuardrailsComponent_Template_app_code_editor_valueChange_18_listener", "$event", "CreateGuardrailsComponent_div_20_Template", "CreateGuardrailsComponent_Template_app_playground_promptChange_26_listener", "CreateGuardrailsComponent_Template_app_playground_messageSent_26_listener", "CreateGuardrailsComponent_Template_ava_popup_confirm_27_listener", "CreateGuardrailsComponent_Template_ava_popup_cancel_27_listener", "CreateGuardrailsComponent_Template_ava_popup_closed_27_listener", "CreateGuardrailsComponent_Template_ava_popup_closed_28_listener", "ɵɵpureFunction1", "_c2", "ɵɵclassProp", "i6", "Ng<PERSON><PERSON>", "NgIf", "ɵNgNoValidate", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "FormControlDirective", "FormGroupDirective", "i7", "LucideAngularComponent", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\libraries\\guardrails\\create-guardrails\\create-guardrails.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\pages\\libraries\\guardrails\\create-guardrails\\create-guardrails.component.html"], "sourcesContent": ["import { Component, <PERSON><PERSON>ni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport {\r\n  FormBuilder,\r\n  FormGroup,\r\n  ReactiveFormsModule,\r\n  FormControl,\r\n  Validators,\r\n} from '@angular/forms';\r\nimport { Router, ActivatedRoute } from '@angular/router';\r\nimport { ChatMessage } from '@shared/components/chat-window/chat-window.component';\r\nimport { ToolExecutionService } from '@shared/services/tool-execution/tool-execution.service';\r\nimport { Subscription } from 'rxjs';\r\nimport { PlaygroundComponent } from '@shared/components/playground/playground.component';\r\nimport {\r\n  AvaTextareaComponent,\r\n  AvaTextboxComponent,\r\n  ButtonComponent,\r\n  DropdownOption,\r\n  IconComponent,\r\n  PopupComponent,\r\n} from '@ava/play-comp-library';\r\nimport {\r\n  CodeEditorComponent,\r\n  CodeLanguage,\r\n  CodeEditorTheme,\r\n} from '@shared/components/code-editor/code-editor.component';\r\nimport { PromptEnhanceService } from '@shared/services/prompt-enhance.service';\r\nimport { GuardrailsService } from '@shared/services/guardrails.service';\r\nimport { Guardrail } from '@shared/models/card.model';\r\nimport guardrailsLabels from '../constants/guardrails-base.json';\r\nimport { LucideAngularModule } from 'lucide-angular';\r\n\r\n@Component({\r\n  selector: 'app-create-guardrails',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    ReactiveFormsModule,\r\n    PlaygroundComponent,\r\n    AvaTextboxComponent,\r\n    ButtonComponent,\r\n    AvaTextareaComponent,\r\n    CodeEditorComponent,\r\n    PopupComponent,\r\n    LucideAngularModule,\r\n    IconComponent,\r\n  ],\r\n  templateUrl: './create-guardrails.component.html',\r\n  styleUrls: ['./create-guardrails.component.scss'],\r\n})\r\nexport class CreateGuardrailsComponent implements OnInit, OnDestroy {\r\n  @ViewChild('colangEditor') colangEditor!: CodeEditorComponent;\r\n  @ViewChild('ymlEditor') ymlEditor!: CodeEditorComponent;\r\n\r\n  // Labels from constants file\r\n  grLabels = guardrailsLabels.labels;\r\n\r\n  // Mode flags\r\n  guardrailId: string | null = null;\r\n  isEditMode: boolean = false;\r\n  isExecuteMode: boolean = false;\r\n  showChatInterface: boolean = false;\r\n  isLeftCollapsed = false;\r\n\r\n  toggleLeftPanel() {\r\n    this.isLeftCollapsed = !this.isLeftCollapsed;\r\n  }\r\n\r\n  guardrailForm: FormGroup;\r\n\r\n  // Code format options\r\n  codeFormats: string[] = ['Colang', 'YML'];\r\n  selectedCodeFormat: string = 'Colang';\r\n\r\n  // Chat interface properties\r\n  chatMessages: ChatMessage[] = [];\r\n  isProcessingChat: boolean = false;\r\n\r\n  // Subscription\r\n  private executionSubscription: Subscription = new Subscription();\r\n  public validationOutput: string = '';\r\n  public showValidationOutput: boolean = false;\r\n  public validationOutputEditorConfig = {\r\n    title: 'Tool Compiler',\r\n    language: 'json' as CodeLanguage,\r\n    theme: 'light' as CodeEditorTheme,\r\n    readOnly: true,\r\n    height: '250px',\r\n  };\r\n\r\n  codeFormatTabs = [\r\n    { label: 'Colang', value: 'colang' },\r\n    { label: 'YML', value: 'yml' },\r\n  ];\r\n  selectedTab: string = 'colang';\r\n\r\n  private rawColangContent: string = '';\r\n  private rawYamlContent: string = '';\r\n\r\n  // For shared save/update popup\r\n  showConfirmPopup: boolean = false;\r\n  confirmPopupTitle: string = '';\r\n  confirmPopupMessage: string = '';\r\n  confirmButtonLabel: string = '';\r\n  confirmButtonColor: string = '';\r\n  actionType: 'save' | 'update' | null = null;\r\n  // Success Popup for Save/Update\r\n  showInfoPopup: boolean = false;\r\n  infoMessage: string = '';\r\n  public labels: any = guardrailsLabels.labels;\r\n  originalFormValue: any;\r\n  codeEditorState = {\r\n  loading: false,\r\n  error: false,\r\n  errorMessage: null as string | null,\r\n  processing: false,\r\n};\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private toolExecutionService: ToolExecutionService,\r\n    private promptGenerateService: PromptEnhanceService,\r\n    private guardrailsService: GuardrailsService,\r\n  ) {\r\n    this.guardrailForm = this.fb.group({\r\n      // Guardrail details\r\n     name: ['', Validators.required],\r\n     description: ['', Validators.required],\r\n\r\n      // Filters\r\n      organization: [''],\r\n      domain: [''],\r\n      project: [''],\r\n      team: [''],\r\n      // Code content\r\n       codeContent: new FormControl('', Validators.required),\r\n      yamlContent: [''],\r\n\r\n      // codeContent: ['<!DOCTYPE html>\\n<html lang=\"en\">\\n<head>\\n  <meta charset=\"UTF-8\">\\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\\n  <title>Agentic Activity Log</title>\\n  <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css\" rel=\"stylesheet\">\\n</head>\\n<body>\\n\\n<div class=\"container mt-5\">\\n  <h2 class=\"text-center mb-4\">Agentic Activity Log</h2>\\n  \\n  <div class=\"card shadow-lg\">\\n    <div class=\"card-body\">\\n      <h5 class=\"card-title\">Lead Qualification Agent</h5>\\n      <p><strong>Execution ID:</strong> LQ-20250323-001</p>\\n      <p><strong>Status:</strong> <span class=\"badge bg-success\">Completed</span></p>\\n      \\n      <table class=\"table table-bordered mt-3\">\\n        <thead class=\"table-dark\">\\n          <tr>\\n            <th>Step</th>\\n            <th>Details</th>\\n            <th>Time</th>']\r\n    });\r\n  }\r\n  // Define language mapping (Colang and YML can be treated as plaintext or yaml depending on real syntax support)\r\n  codeLanguagesMap: Record<string, CodeLanguage> = {\r\n    Colang: 'plaintext',\r\n    YML: 'yaml',\r\n  };\r\n  get currentCodeControl(): FormControl {\r\n    return this.selectedCodeFormat === 'YML'\r\n      ? (this.guardrailForm.get('yamlContent') as FormControl)\r\n      : (this.guardrailForm.get('codeContent') as FormControl);\r\n  }\r\n\r\n  getColangControl(): FormControl {\r\n    return this.guardrailForm.get('codeContent') as FormControl;\r\n  }\r\n  getYmlControl(): FormControl {\r\n    return this.guardrailForm.get('yamlContent') as FormControl;\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    // Check if we're in edit mode\r\n    this.guardrailId = this.route.snapshot.paramMap.get('id');\r\n    const executeParam = this.route.snapshot.queryParamMap.get('execute');\r\n\r\n    this.isEditMode = !!this.guardrailId;\r\n    this.isExecuteMode = executeParam === 'true';\r\n    this.showChatInterface = this.isExecuteMode;\r\n\r\n    if (this.isEditMode && this.guardrailId) {\r\n      console.log('iseditmode', this.isEditMode);\r\n      // Load guardrail data for editing\r\n      console.log(`Editing guardrail with ID: ${this.guardrailId}`);\r\n      this.loadGuardrailData(this.guardrailId);\r\n\r\n      // If in execute mode, start the execution\r\n      if (this.isExecuteMode) {\r\n        // Initialize messages\r\n        this.chatMessages = [\r\n          {\r\n            from: 'ai',\r\n            text: 'Hi Akash, this is the guardrail testing',\r\n          },\r\n          {\r\n            from: 'user',\r\n            text: 'Test this input',\r\n          },\r\n          {\r\n            from: 'ai',\r\n            text: 'Here is the output',\r\n          },\r\n        ];\r\n\r\n        // Start execution (after a small delay to ensure UI is ready)\r\n        setTimeout(() => {\r\n          this.toolExecutionService.startExecution(\r\n            this.guardrailId!,\r\n            this.chatMessages,\r\n          );\r\n\r\n          // Subscribe to execution state changes\r\n          this.executionSubscription = this.toolExecutionService\r\n            .getExecutionState()\r\n            .subscribe((state) => {\r\n              if (state.isExecuting && state.toolId === this.guardrailId) {\r\n                this.chatMessages = state.chatMessages;\r\n              }\r\n            });\r\n        }, 100);\r\n      }\r\n    }\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // Clean up subscription\r\n    if (this.executionSubscription) {\r\n      this.executionSubscription.unsubscribe();\r\n    }\r\n  }\r\n\r\n  onSave(): void {\r\n    const name = this.guardrailForm.get('name')?.value;\r\n    const description = this.guardrailForm.get('description')?.value;\r\n    const content = this.guardrailForm.get('codeContent')?.value;\r\n    const organization =\r\n      this.guardrailForm.get('organization')?.value || 'Ascendion';\r\n    const configKey = this.guardrailForm.get('configKey')?.value;\r\n    const yamlContent = this.guardrailForm.get('yamlContent')?.value;\r\n    const chatBot = this.guardrailForm.get('chatBot')?.value;\r\n\r\n    const payload = {\r\n      name,\r\n      description,\r\n      content,\r\n      organization,\r\n      configKey,\r\n      yamlContent,\r\n      chatBot,\r\n    };\r\n    if (this.guardrailForm.invalid) {\r\n      this.guardrailForm.markAllAsTouched();\r\n      return;\r\n    }\r\n    if (this.isEditMode && this.guardrailId) {\r\n      this.guardrailsService\r\n        .updateGuardrail({ id: Number(this.guardrailId), ...payload })\r\n        .subscribe({\r\n          next: () =>{\r\n          this.infoMessage = `Guardrail \"${payload.name}\" has been successfully updated.`;\r\n          this.showInfoPopup = true;\r\n          },\r\n          error: (err) => console.error('Update failed', err),\r\n        });\r\n    } else {\r\n      this.guardrailsService.addGuardrail(payload).subscribe({\r\n        next: () => {\r\n        this.infoMessage = `Guardrail \"${payload.name}\" has been successfully created.`;\r\n        this.showInfoPopup = true;\r\n      },\r\n        error: (err) => console.error('Create failed', err),\r\n      });\r\n    }\r\n  }\r\n  handleInfoPopup(): void {\r\n  this.showInfoPopup = false;\r\n  this.router.navigate(['/libraries/guardrails']);\r\n}\r\n\r\n  onExecute(): void {\r\n    console.log('Executing guardrail:', this.guardrailForm.value);\r\n    if (this.guardrailId) {\r\n      // If we're already in execute mode with chat interface showing\r\n      if (this.isExecuteMode && this.showChatInterface) {\r\n        // Process the execution\r\n        console.log('Processing execution');\r\n      } else {\r\n        console.log('Entering execute mode, showing chat interface');\r\n\r\n        // Set flags to show chat interface\r\n        this.isExecuteMode = true;\r\n        this.showChatInterface = true;\r\n\r\n        // Set the initial messages\r\n        this.chatMessages = [\r\n          {\r\n            from: 'ai',\r\n            text: 'Hi Akash, this is the guardrail testing',\r\n          },\r\n          {\r\n            from: 'user',\r\n            text: 'Test this input',\r\n          },\r\n          {\r\n            from: 'ai',\r\n            text: 'Here is the output',\r\n          },\r\n        ];\r\n\r\n        // Delay starting the execution service slightly to allow UI to update\r\n        setTimeout(() => {\r\n          console.log(\r\n            'Starting execution service for guardrail ID:',\r\n            this.guardrailId,\r\n          );\r\n          this.toolExecutionService.startExecution(\r\n            this.guardrailId!,\r\n            this.chatMessages,\r\n          );\r\n        }, 100);\r\n      }\r\n    }\r\n  }\r\n\r\n  onExit(): void {\r\n    // If we're in execute mode with chat interface showing\r\n    if (this.isExecuteMode && this.isEditMode) {\r\n      // Return to edit mode without chat interface\r\n      this.isExecuteMode = false;\r\n      this.showChatInterface = false;\r\n      this.toolExecutionService.stopExecution();\r\n      console.log('Exited execution mode, returning to edit mode');\r\n    } else {\r\n      // Get the return page if available\r\n      const returnPage = this.route.snapshot.queryParamMap.get('returnPage');\r\n      const pageNumber = returnPage ? parseInt(returnPage) : 1;\r\n\r\n      // Exit to guardrails list at correct page\r\n      this.router.navigate(['/libraries/guardrails'], {\r\n        queryParams: { page: pageNumber },\r\n      });\r\n    }\r\n  }\r\n\r\n  // Helper method to get form controls easily from the template\r\n  getControl(name: string): FormControl {\r\n    return this.guardrailForm.get(name) as FormControl;\r\n  }\r\n\r\n  // Method to change the selected code format\r\n  setCodeFormat(format: string): void {\r\n    // Save the current editor content into the appropriate form control\r\n    const currentEditorContent = this.selectedCodeFormat === 'Colang' \r\n      ? this.colangEditor?.getValue() || ''\r\n      : this.ymlEditor?.getValue() || '';\r\n    if (this.selectedCodeFormat === 'Colang') {\r\n      this.rawColangContent = currentEditorContent;\r\n      this.guardrailForm.get('codeContent')?.setValue(currentEditorContent);\r\n    } else {\r\n      this.rawYamlContent = currentEditorContent;\r\n      this.guardrailForm.get('yamlContent')?.setValue(currentEditorContent);\r\n    }\r\n\r\n    // Change the format\r\n    this.selectedCodeFormat = format;\r\n\r\n    // Get the new content for the selected format\r\n    const newContent =\r\n      format === 'Colang' ? this.rawColangContent : this.rawYamlContent;\r\n\r\n    // Update the editor value\r\n    setTimeout(() => {\r\n      this.colangEditor?.setValue(newContent || '');\r\n    });\r\n  }\r\n  // Load guardrail data from mock data\r\n  loadGuardrailData(guardrailId: string): void {\r\n    this.guardrailsService.getGuardrailById(Number(guardrailId)).subscribe({\r\n      next: (guardrail: Guardrail) => {\r\n        if (!guardrail) {\r\n          console.error('No guardrail data found for ID:', guardrailId);\r\n          return;\r\n        }\r\n\r\n        // Set raw contents\r\n        this.rawColangContent = guardrail.content || '';\r\n        this.rawYamlContent = guardrail.yamlContent || '';\r\n\r\n        // Patch form values\r\n        this.guardrailForm.patchValue({\r\n          name: guardrail.name || '',\r\n          description: guardrail.description || '',\r\n          organization: guardrail.organization || 'Ascendion',\r\n          configKey: guardrail.configKey || '',\r\n          chatBot: guardrail.chatBot || false,\r\n          codeContent: this.rawColangContent,\r\n          yamlContent: this.rawYamlContent,\r\n        });\r\n        // Save original form value for change tracking\r\n      this.originalFormValue = this.guardrailForm.getRawValue();\r\n        // Set content for both editors\r\n        setTimeout(() => {\r\n          if (this.colangEditor) {\r\n            this.colangEditor.setValue(this.rawColangContent || '');\r\n          }\r\n          if (this.ymlEditor) {\r\n            this.ymlEditor.setValue(this.rawYamlContent || '');\r\n          }\r\n        }, 100);\r\n      },\r\n      error: (err) => {\r\n        console.error('Failed to load guardrail data:', err);\r\n      },\r\n    });\r\n  }\r\n\r\n  private tryParseJSONString(str: string): string {\r\n    try {\r\n      const parsed = JSON.parse(str);\r\n      return typeof parsed === 'string' ? parsed : str;\r\n    } catch {\r\n      return str;\r\n    }\r\n  }\r\n\r\n  private addChatMessage(from: 'ai' | 'user', text: string): void {\r\n    this.chatMessages = [...this.chatMessages, { from, text }];\r\n  }\r\n\r\n  // Handle chat messages\r\n  handleChatMessage(message: string): void {\r\n    if(this.isPlaygroundDisabled){\r\n      return;\r\n    }\r\n    this.addChatMessage('user', message);\r\n    this.isProcessingChat = true;\r\n    this.executeGuradrails(message);\r\n  }\r\n\r\nexecuteGuradrails(message: string) {\r\n  // Get current content from both editors\r\n  const colangContent = this.colangEditor?.getValue() || this.guardrailForm.get('codeContent')?.value;\r\n  const yamlContent = this.ymlEditor?.getValue() || this.guardrailForm.get('yamlContent')?.value;\r\n\r\n  const payload = {\r\n    prompt: message,\r\n    mode: \"DEFAULT\",\r\n    promptOverride: true,\r\n    colangContent: colangContent,\r\n    yamlContent: yamlContent\r\n  };\r\n\r\n  this.promptGenerateService.modelGuardrailApi(\r\n    payload.prompt,\r\n    payload.mode,\r\n    payload.promptOverride,\r\n    payload.yamlContent,\r\n    payload.colangContent\r\n  ).subscribe({\r\n    next: (response: any) => {\r\n      if (response && response.response && response.response.choices && response.response.choices.length > 0) {\r\n        const aiText = response.response.choices[0].text;\r\n        this.addChatMessage('ai', aiText);\r\n        \r\n        // Keep loader active for a brief moment to ensure message is displayed\r\n        setTimeout(() => {\r\n          this.isProcessingChat = false;\r\n        }, 500);\r\n      } else {\r\n        this.addChatMessage('ai', 'No response received');\r\n        setTimeout(() => {\r\n          this.isProcessingChat = false;\r\n        }, 500);\r\n      }\r\n    },\r\n    error: (error) => {\r\n      console.error('Error executing guardrail:', error);\r\n      this.addChatMessage('ai', 'Error processing your request');\r\n      setTimeout(() => {\r\n        this.isProcessingChat = false;\r\n      }, 500);\r\n    }\r\n  });\r\n}\r\n  //Drop Down\r\n  promptOptions: DropdownOption[] = [\r\n    { value: 'default', name: 'Choose Prompt' },\r\n    { value: 'ruby-developer', name: 'Senior Ruby Developer' },\r\n    { value: 'python-developer', name: 'Python Developer' },\r\n    { value: 'data-scientist', name: 'Data Scientist' },\r\n    { value: 'frontend-developer', name: 'Frontend Developer' },\r\n  ];\r\n  onPromptChanged(option: DropdownOption) {\r\n    console.log('Prompt changed in parent:', option);\r\n    // your logic to handle selected prompt\r\n  }\r\n  showInterface = (): void => {\r\n    setTimeout(() => {\r\n      this.isProcessingChat = false;\r\n      this.isExecuteMode = true;\r\n      this.showChatInterface = true;\r\n      \r\n      setTimeout(() => {\r\n        if (this.colangEditor?.isReady) {\r\n          this.colangEditor.focus();\r\n          this.colangEditor['editor']?.layout();\r\n          this.colangEditor.hideProcessingLoader();\r\n        }\r\n        if (this.ymlEditor?.isReady) {\r\n          this.ymlEditor['editor']?.layout();\r\n        }\r\n      }, 100);\r\n    });\r\n  };\r\n\r\n  // onTabSelected(tabValue: string): void { ... } method removed\r\n\r\n  isSubmitDisabled(): boolean {\r\n   const isFormInvalid = this.guardrailForm.invalid;\r\n  const isUnchanged = JSON.stringify(this.guardrailForm.getRawValue()) === JSON.stringify(this.originalFormValue);\r\n  return isFormInvalid || isUnchanged;\r\n}\r\n  getFieldError(fieldName: string): string {\r\n    const field = this.guardrailForm.get(fieldName);\r\n    // Capitalize only if first letter is not already uppercase\r\n    const formattedFieldName = /^[A-Z]/.test(fieldName)\r\n      ? fieldName\r\n      : fieldName.charAt(0).toUpperCase() + fieldName.slice(1);\r\n    if (field && field.invalid && (field.touched || field.dirty)) {\r\n      if (field.errors?.['required']) {\r\n        return `${formattedFieldName} is required`;\r\n      }\r\n    }\r\n    return '';\r\n  }\r\n\r\n  // Trigger Save Confirmation\r\nconfirmSave(): void {\r\n  this.actionType = 'save';\r\n  this.confirmPopupTitle = 'Save Guardrail?';\r\n  this.confirmPopupMessage = 'Are you sure you want to save this guardrail?';\r\n  this.confirmButtonLabel = 'Save';\r\n  this.confirmButtonColor = '#007bff';\r\n  this.showConfirmPopup = true;\r\n}\r\n\r\n// Trigger Update Confirmation\r\nconfirmUpdate(): void {\r\n  this.actionType = 'update';\r\n  this.confirmPopupTitle = 'Update Guardrail?';\r\n  this.confirmPopupMessage = 'Are you sure you want to update this guardrail?';\r\n  this.confirmButtonLabel = 'Update';\r\n  this.confirmButtonColor = '#28a745';\r\n  this.showConfirmPopup = true;\r\n}\r\n\r\n// On confirm button clicked\r\n  onConfirmAction(): void {\r\n    this.showConfirmPopup = false;\r\n    this.onSave(); // reuse existing logic\r\n  }\r\n\r\n  // Close/cancel\r\n  closePopup(): void {\r\n    this.showConfirmPopup = false;\r\n  }\r\n  get isColangTestDisabled(): boolean {\r\n  const isColang = this.selectedCodeFormat === 'Colang';\r\n  const colangCode = this.guardrailForm.get('codeContent')?.value?.trim();\r\n  return isColang && (!colangCode || colangCode.length === 0);\r\n}\r\nonColangContentChanged(value: string): void {\r\n  const control = this.guardrailForm.get('codeContent');\r\n  control?.setValue(value);\r\n  control?.markAsDirty();\r\n  control?.markAsTouched(); // Add this line to mark as touched\r\n}\r\nshowCodeEditorError(): boolean {\r\n  const ctrl = this.guardrailForm.get('codeContent');\r\n  const editorValue = this.colangEditor?.getValue() || '';\r\n  const formValue = ctrl?.value || '';\r\n  \r\n  // Check if control is touched and either form value or editor value is empty\r\n  const isEmpty = !editorValue.trim() && !formValue.trim();\r\n  const hasError = !!(ctrl?.touched && isEmpty);\r\n\r\n  this.codeEditorState.error = hasError;\r\n  this.codeEditorState.errorMessage = hasError ? 'Colang Code is required.' : null;\r\n\r\n  return hasError;\r\n}\r\n  get isPlaygroundDisabled(): boolean {\r\n  const colangContent = this.colangEditor?.getValue() || this.guardrailForm.get('codeContent')?.value;\r\n  return !colangContent || colangContent.trim().length === 0;\r\n}\r\n\r\n}\r\n", "<p class=\"page-title\">Guardrail Configuration</p>\r\n<div class=\"create-guardrails-container\">\r\n  <form [formGroup]=\"guardrailForm\">\r\n    <div\r\n      class=\"form-layout\"\r\n      [ngClass]=\"{ 'three-column-layout': isExecuteMode && showChatInterface }\"\r\n    >\r\n      <!-- Left Column -->\r\n      <div class=\"left-column\" [class.collapsed]=\"isLeftCollapsed\">\r\n        <div class=\"left-header\">\r\n          <span class=\"left-title\" *ngIf=\"!isLeftCollapsed\">Guardrail Description</span>\r\n          <lucide-icon name=\"panel-left\" class=\"collapse-icon\" (click)=\"toggleLeftPanel()\"></lucide-icon>\r\n        </div>\r\n        <div class=\"card-content\" *ngIf=\"!isLeftCollapsed\">\r\n          <ava-textbox\r\n            [formControl]=\"getControl('name')\"\r\n            [label]=\"labels.guardrailName\"\r\n            id=\"guardrailName\"\r\n            name=\"GuardrailName\"\r\n            [placeholder]=\"labels.gnPlacholder\"\r\n            variant=\"primary\"\r\n            size=\"md\"\r\n            [fullWidth]=\"true\"\r\n            [required]=\"true\"\r\n            [error]=\"getFieldError('name')\"\r\n          ></ava-textbox>\r\n          <ava-textarea\r\n            id=\"description\"\r\n            name=\"description\"\r\n            [label]=\"labels.description\"\r\n            [formControl]=\"getControl('description')\"\r\n            [placeholder]=\"labels.gdPlaceholder\"\r\n            [rows]=\"4\"\r\n            size=\"md\"\r\n            [fullWidth]=\"true\"\r\n            [required]=\"true\"\r\n            [error]=\"getFieldError('description')\"\r\n          >\r\n          </ava-textarea>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Middle Column (split into Colang and YML panels) -->\r\n      <div class=\"middle-column\">\r\n        <div class=\"split-editors\">\r\n          \r\n          <div class=\"editors-header-row\">\r\n            <div class=\"editors-title\">Guardrail Configuration</div>\r\n            <ava-button\r\n              [label]=\"isEditMode ? 'Update' : 'Save'\"\r\n              size=\"medium\"\r\n              state=\"active\"\r\n              variant=\"primary\"\r\n              (userClick)=\"isEditMode ? confirmUpdate() : confirmSave()\"\r\n              [disabled]=\"isSubmitDisabled()\"\r\n            ></ava-button>\r\n          </div>\r\n          <div class=\"colang-panel\">\r\n            <div class=\"code-editor-container\">\r\n              <app-code-editor\r\n                #colangEditor\r\n                [title]=\"'Colang Editor'\"\r\n                [titleRequired]=\"true\"\r\n                [language]=\"codeLanguagesMap['Colang']\"\r\n                [Control]=\"getColangControl()\"\r\n                [customCssClass]=\"'tools-monaco-editor'\"\r\n                [placeholder]=\"'Write your Colang code here...'\"\r\n                (valueChange)=\"onColangContentChanged($event)\" \r\n              ></app-code-editor>\r\n               <!-- Show error if touched and empty -->\r\n          <div *ngIf=\"showCodeEditorError()\" class=\"error-text\">\r\n            <ava-icon iconName=\"info\" iconColor=\"red\" iconSize=\"14\"></ava-icon>\r\n            {{ codeEditorState.errorMessage }}\r\n          </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"yml-panel\">\r\n            <div class=\"code-editor-container\">\r\n              <app-code-editor\r\n                #ymlEditor\r\n                [title]=\"'YML Editor'\"\r\n                [language]=\"codeLanguagesMap['YML']\"\r\n                [Control]=\"getYmlControl()\"\r\n                [customCssClass]=\"'tools-monaco-editor'\"\r\n                [placeholder]=\"'Write your YML code here...'\"\r\n              ></app-code-editor>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Rightmost Playground Panel (always visible) -->\r\n      <div class=\"rightEnd-column\">\r\n        <!-- <div class=\"playground-title\">Guardrail Playground</div> -->\r\n        <app-playground\r\n          [promptOptions]=\"promptOptions\"\r\n          [messages]=\"chatMessages\"\r\n          [isLoading]=\"isProcessingChat\"\r\n          [showChatInteractionToggles]=\"false\"\r\n          [showAiPrincipleToggle]=\"true\"\r\n          (promptChange)=\"onPromptChanged($event)\"\r\n          (messageSent)=\"handleChatMessage($event)\"\r\n          [showApprovalButton]=\"false\"\r\n          [isMinimalView]=\"true\"\r\n          [isDisabled]=\"isPlaygroundDisabled\"\r\n        >\r\n        </app-playground>\r\n      </div>\r\n    </div>\r\n  </form>\r\n</div>\r\n<!-- Shared Save/Update Popup -->\r\n<ava-popup\r\n  [show]=\"showConfirmPopup\"\r\n  [title]=\"confirmPopupTitle\"\r\n  [message]=\"confirmPopupMessage\"\r\n  [showHeaderIcon]=\"true\"\r\n  [headerIconName]=\"actionType === 'save' ? 'save' : 'edit'\"\r\n  [iconColor]=\"confirmButtonColor\"\r\n  [showClose]=\"true\"\r\n  [showCancel]=\"true\"\r\n  [showConfirm]=\"true\"\r\n  [confirmButtonLabel]=\"confirmButtonLabel\"\r\n  [confirmButtonVariant]=\"'primary'\"\r\n  [confirmButtonBackground]=\"confirmButtonColor\"\r\n  (confirm)=\"onConfirmAction()\"\r\n  (cancel)=\"closePopup()\"\r\n  (closed)=\"closePopup()\"\r\n></ava-popup>\r\n<!-- Success Popup -->\r\n<ava-popup\r\n  messageAlignment=\"center\"\r\n  [show]=\"showInfoPopup\"\r\n  title=\"SUCCESS!\"\r\n  [message]=\"infoMessage\"\r\n  [showHeaderIcon]=\"true\"\r\n  headerIconName=\"circle-check\"\r\n  iconColor=\"green\"\r\n  [showClose]=\"true\"\r\n  (closed)=\"handleInfoPopup()\"\r\n></ava-popup>\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAGEC,mBAAmB,EACnBC,WAAW,EACXC,UAAU,QACL,gBAAgB;AAIvB,SAASC,YAAY,QAAQ,MAAM;AACnC,SAASC,mBAAmB,QAAQ,oDAAoD;AACxF,SACEC,oBAAoB,EACpBC,mBAAmB,EACnBC,eAAe,EAEfC,aAAa,EACbC,cAAc,QACT,wBAAwB;AAC/B,SACEC,mBAAmB,QAGd,sDAAsD;AAI7D,OAAOC,gBAAgB,MAAM,mCAAmC;AAChE,SAASC,mBAAmB,QAAQ,gBAAgB;;;;;;;;;;;;;;;;ICrB1CC,EAAA,CAAAC,cAAA,eAAkD;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAGhFH,EAAA,CAAAC,cAAA,cAAmD;IAajDD,EAZA,CAAAI,SAAA,sBAWe,uBAaA;IACjBJ,EAAA,CAAAG,YAAA,EAAM;;;;IAxBFH,EAAA,CAAAK,SAAA,EAAkC;IASlCL,EATA,CAAAM,UAAA,gBAAAC,MAAA,CAAAC,UAAA,SAAkC,UAAAD,MAAA,CAAAE,MAAA,CAAAC,aAAA,CACJ,gBAAAH,MAAA,CAAAE,MAAA,CAAAE,YAAA,CAGK,mBAGjB,kBACD,UAAAJ,MAAA,CAAAK,aAAA,SACc;IAK/BZ,EAAA,CAAAK,SAAA,EAA4B;IAO5BL,EAPA,CAAAM,UAAA,UAAAC,MAAA,CAAAE,MAAA,CAAAI,WAAA,CAA4B,gBAAAN,MAAA,CAAAC,UAAA,gBACa,gBAAAD,MAAA,CAAAE,MAAA,CAAAK,aAAA,CACL,WAC1B,mBAEQ,kBACD,UAAAP,MAAA,CAAAK,aAAA,gBACqB;;;;;IAkCxCZ,EAAA,CAAAC,cAAA,cAAsD;IACpDD,EAAA,CAAAI,SAAA,mBAAmE;IACnEJ,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAe,kBAAA,MAAAR,MAAA,CAAAS,eAAA,CAAAC,YAAA,MACF;;;ADtBV,WAAaC,yBAAyB;EAAhC,MAAOA,yBAAyB;IAqE1BC,EAAA;IACAC,MAAA;IACAC,KAAA;IACAC,oBAAA;IACAC,qBAAA;IACAC,iBAAA;IAzEiBC,YAAY;IACfC,SAAS;IAEjC;IACAC,QAAQ,GAAG7B,gBAAgB,CAACW,MAAM;IAElC;IACAmB,WAAW,GAAkB,IAAI;IACjCC,UAAU,GAAY,KAAK;IAC3BC,aAAa,GAAY,KAAK;IAC9BC,iBAAiB,GAAY,KAAK;IAClCC,eAAe,GAAG,KAAK;IAEvBC,eAAeA,CAAA;MACb,IAAI,CAACD,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;IAC9C;IAEAE,aAAa;IAEb;IACAC,WAAW,GAAa,CAAC,QAAQ,EAAE,KAAK,CAAC;IACzCC,kBAAkB,GAAW,QAAQ;IAErC;IACAC,YAAY,GAAkB,EAAE;IAChCC,gBAAgB,GAAY,KAAK;IAEjC;IACQC,qBAAqB,GAAiB,IAAIjD,YAAY,EAAE;IACzDkD,gBAAgB,GAAW,EAAE;IAC7BC,oBAAoB,GAAY,KAAK;IACrCC,4BAA4B,GAAG;MACpCC,KAAK,EAAE,eAAe;MACtBC,QAAQ,EAAE,MAAsB;MAChCC,KAAK,EAAE,OAA0B;MACjCC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE;KACT;IAEDC,cAAc,GAAG,CACf;MAAEC,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAQ,CAAE,EACpC;MAAED,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAK,CAAE,CAC/B;IACDC,WAAW,GAAW,QAAQ;IAEtBC,gBAAgB,GAAW,EAAE;IAC7BC,cAAc,GAAW,EAAE;IAEnC;IACAC,gBAAgB,GAAY,KAAK;IACjCC,iBAAiB,GAAW,EAAE;IAC9BC,mBAAmB,GAAW,EAAE;IAChCC,kBAAkB,GAAW,EAAE;IAC/BC,kBAAkB,GAAW,EAAE;IAC/BC,UAAU,GAA6B,IAAI;IAC3C;IACAC,aAAa,GAAY,KAAK;IAC9BC,WAAW,GAAW,EAAE;IACjBpD,MAAM,GAAQX,gBAAgB,CAACW,MAAM;IAC5CqD,iBAAiB;IACjB9C,eAAe,GAAG;MAClB+C,OAAO,EAAE,KAAK;MACdC,KAAK,EAAE,KAAK;MACZ/C,YAAY,EAAE,IAAqB;MACnCgD,UAAU,EAAE;KACb;IAECC,YACU/C,EAAe,EACfC,MAAc,EACdC,KAAqB,EACrBC,oBAA0C,EAC1CC,qBAA2C,EAC3CC,iBAAoC;MALpC,KAAAL,EAAE,GAAFA,EAAE;MACF,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,KAAK,GAALA,KAAK;MACL,KAAAC,oBAAoB,GAApBA,oBAAoB;MACpB,KAAAC,qBAAqB,GAArBA,qBAAqB;MACrB,KAAAC,iBAAiB,GAAjBA,iBAAiB;MAEzB,IAAI,CAACU,aAAa,GAAG,IAAI,CAACf,EAAE,CAACgD,KAAK,CAAC;QACjC;QACDC,IAAI,EAAE,CAAC,EAAE,EAAE/E,UAAU,CAACgF,QAAQ,CAAC;QAC/BxD,WAAW,EAAE,CAAC,EAAE,EAAExB,UAAU,CAACgF,QAAQ,CAAC;QAErC;QACAC,YAAY,EAAE,CAAC,EAAE,CAAC;QAClBC,MAAM,EAAE,CAAC,EAAE,CAAC;QACZC,OAAO,EAAE,CAAC,EAAE,CAAC;QACbC,IAAI,EAAE,CAAC,EAAE,CAAC;QACV;QACCC,WAAW,EAAE,IAAItF,WAAW,CAAC,EAAE,EAAEC,UAAU,CAACgF,QAAQ,CAAC;QACtDM,WAAW,EAAE,CAAC,EAAE;QAEhB;OACD,CAAC;IACJ;IACA;IACAC,gBAAgB,GAAiC;MAC/CC,MAAM,EAAE,WAAW;MACnBC,GAAG,EAAE;KACN;IACD,IAAIC,kBAAkBA,CAAA;MACpB,OAAO,IAAI,CAAC3C,kBAAkB,KAAK,KAAK,GACnC,IAAI,CAACF,aAAa,CAAC8C,GAAG,CAAC,aAAa,CAAiB,GACrD,IAAI,CAAC9C,aAAa,CAAC8C,GAAG,CAAC,aAAa,CAAiB;IAC5D;IAEAC,gBAAgBA,CAAA;MACd,OAAO,IAAI,CAAC/C,aAAa,CAAC8C,GAAG,CAAC,aAAa,CAAgB;IAC7D;IACAE,aAAaA,CAAA;MACX,OAAO,IAAI,CAAChD,aAAa,CAAC8C,GAAG,CAAC,aAAa,CAAgB;IAC7D;IAEAG,QAAQA,CAAA;MACN;MACA,IAAI,CAACvD,WAAW,GAAG,IAAI,CAACP,KAAK,CAAC+D,QAAQ,CAACC,QAAQ,CAACL,GAAG,CAAC,IAAI,CAAC;MACzD,MAAMM,YAAY,GAAG,IAAI,CAACjE,KAAK,CAAC+D,QAAQ,CAACG,aAAa,CAACP,GAAG,CAAC,SAAS,CAAC;MAErE,IAAI,CAACnD,UAAU,GAAG,CAAC,CAAC,IAAI,CAACD,WAAW;MACpC,IAAI,CAACE,aAAa,GAAGwD,YAAY,KAAK,MAAM;MAC5C,IAAI,CAACvD,iBAAiB,GAAG,IAAI,CAACD,aAAa;MAE3C,IAAI,IAAI,CAACD,UAAU,IAAI,IAAI,CAACD,WAAW,EAAE;QACvC4D,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC5D,UAAU,CAAC;QAC1C;QACA2D,OAAO,CAACC,GAAG,CAAC,8BAA8B,IAAI,CAAC7D,WAAW,EAAE,CAAC;QAC7D,IAAI,CAAC8D,iBAAiB,CAAC,IAAI,CAAC9D,WAAW,CAAC;QAExC;QACA,IAAI,IAAI,CAACE,aAAa,EAAE;UACtB;UACA,IAAI,CAACO,YAAY,GAAG,CAClB;YACEsD,IAAI,EAAE,IAAI;YACVC,IAAI,EAAE;WACP,EACD;YACED,IAAI,EAAE,MAAM;YACZC,IAAI,EAAE;WACP,EACD;YACED,IAAI,EAAE,IAAI;YACVC,IAAI,EAAE;WACP,CACF;UAED;UACAC,UAAU,CAAC,MAAK;YACd,IAAI,CAACvE,oBAAoB,CAACwE,cAAc,CACtC,IAAI,CAAClE,WAAY,EACjB,IAAI,CAACS,YAAY,CAClB;YAED;YACA,IAAI,CAACE,qBAAqB,GAAG,IAAI,CAACjB,oBAAoB,CACnDyE,iBAAiB,EAAE,CACnBC,SAAS,CAAEC,KAAK,IAAI;cACnB,IAAIA,KAAK,CAACC,WAAW,IAAID,KAAK,CAACE,MAAM,KAAK,IAAI,CAACvE,WAAW,EAAE;gBAC1D,IAAI,CAACS,YAAY,GAAG4D,KAAK,CAAC5D,YAAY;cACxC;YACF,CAAC,CAAC;UACN,CAAC,EAAE,GAAG,CAAC;QACT;MACF;IACF;IAEA+D,WAAWA,CAAA;MACT;MACA,IAAI,IAAI,CAAC7D,qBAAqB,EAAE;QAC9B,IAAI,CAACA,qBAAqB,CAAC8D,WAAW,EAAE;MAC1C;IACF;IAEAC,MAAMA,CAAA;MACJ,MAAMlC,IAAI,GAAG,IAAI,CAAClC,aAAa,CAAC8C,GAAG,CAAC,MAAM,CAAC,EAAE9B,KAAK;MAClD,MAAMrC,WAAW,GAAG,IAAI,CAACqB,aAAa,CAAC8C,GAAG,CAAC,aAAa,CAAC,EAAE9B,KAAK;MAChE,MAAMqD,OAAO,GAAG,IAAI,CAACrE,aAAa,CAAC8C,GAAG,CAAC,aAAa,CAAC,EAAE9B,KAAK;MAC5D,MAAMoB,YAAY,GAChB,IAAI,CAACpC,aAAa,CAAC8C,GAAG,CAAC,cAAc,CAAC,EAAE9B,KAAK,IAAI,WAAW;MAC9D,MAAMsD,SAAS,GAAG,IAAI,CAACtE,aAAa,CAAC8C,GAAG,CAAC,WAAW,CAAC,EAAE9B,KAAK;MAC5D,MAAMyB,WAAW,GAAG,IAAI,CAACzC,aAAa,CAAC8C,GAAG,CAAC,aAAa,CAAC,EAAE9B,KAAK;MAChE,MAAMuD,OAAO,GAAG,IAAI,CAACvE,aAAa,CAAC8C,GAAG,CAAC,SAAS,CAAC,EAAE9B,KAAK;MAExD,MAAMwD,OAAO,GAAG;QACdtC,IAAI;QACJvD,WAAW;QACX0F,OAAO;QACPjC,YAAY;QACZkC,SAAS;QACT7B,WAAW;QACX8B;OACD;MACD,IAAI,IAAI,CAACvE,aAAa,CAACyE,OAAO,EAAE;QAC9B,IAAI,CAACzE,aAAa,CAAC0E,gBAAgB,EAAE;QACrC;MACF;MACA,IAAI,IAAI,CAAC/E,UAAU,IAAI,IAAI,CAACD,WAAW,EAAE;QACvC,IAAI,CAACJ,iBAAiB,CACnBqF,eAAe,CAAC;UAAEC,EAAE,EAAEC,MAAM,CAAC,IAAI,CAACnF,WAAW,CAAC;UAAE,GAAG8E;QAAO,CAAE,CAAC,CAC7DV,SAAS,CAAC;UACTgB,IAAI,EAAEA,CAAA,KAAK;YACX,IAAI,CAACnD,WAAW,GAAG,cAAc6C,OAAO,CAACtC,IAAI,kCAAkC;YAC/E,IAAI,CAACR,aAAa,GAAG,IAAI;UACzB,CAAC;UACDI,KAAK,EAAGiD,GAAG,IAAKzB,OAAO,CAACxB,KAAK,CAAC,eAAe,EAAEiD,GAAG;SACnD,CAAC;MACN,CAAC,MAAM;QACL,IAAI,CAACzF,iBAAiB,CAAC0F,YAAY,CAACR,OAAO,CAAC,CAACV,SAAS,CAAC;UACrDgB,IAAI,EAAEA,CAAA,KAAK;YACX,IAAI,CAACnD,WAAW,GAAG,cAAc6C,OAAO,CAACtC,IAAI,kCAAkC;YAC/E,IAAI,CAACR,aAAa,GAAG,IAAI;UAC3B,CAAC;UACCI,KAAK,EAAGiD,GAAG,IAAKzB,OAAO,CAACxB,KAAK,CAAC,eAAe,EAAEiD,GAAG;SACnD,CAAC;MACJ;IACF;IACAE,eAAeA,CAAA;MACf,IAAI,CAACvD,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACxC,MAAM,CAACgG,QAAQ,CAAC,CAAC,uBAAuB,CAAC,CAAC;IACjD;IAEEC,SAASA,CAAA;MACP7B,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACvD,aAAa,CAACgB,KAAK,CAAC;MAC7D,IAAI,IAAI,CAACtB,WAAW,EAAE;QACpB;QACA,IAAI,IAAI,CAACE,aAAa,IAAI,IAAI,CAACC,iBAAiB,EAAE;UAChD;UACAyD,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;QACrC,CAAC,MAAM;UACLD,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;UAE5D;UACA,IAAI,CAAC3D,aAAa,GAAG,IAAI;UACzB,IAAI,CAACC,iBAAiB,GAAG,IAAI;UAE7B;UACA,IAAI,CAACM,YAAY,GAAG,CAClB;YACEsD,IAAI,EAAE,IAAI;YACVC,IAAI,EAAE;WACP,EACD;YACED,IAAI,EAAE,MAAM;YACZC,IAAI,EAAE;WACP,EACD;YACED,IAAI,EAAE,IAAI;YACVC,IAAI,EAAE;WACP,CACF;UAED;UACAC,UAAU,CAAC,MAAK;YACdL,OAAO,CAACC,GAAG,CACT,8CAA8C,EAC9C,IAAI,CAAC7D,WAAW,CACjB;YACD,IAAI,CAACN,oBAAoB,CAACwE,cAAc,CACtC,IAAI,CAAClE,WAAY,EACjB,IAAI,CAACS,YAAY,CAClB;UACH,CAAC,EAAE,GAAG,CAAC;QACT;MACF;IACF;IAEAiF,MAAMA,CAAA;MACJ;MACA,IAAI,IAAI,CAACxF,aAAa,IAAI,IAAI,CAACD,UAAU,EAAE;QACzC;QACA,IAAI,CAACC,aAAa,GAAG,KAAK;QAC1B,IAAI,CAACC,iBAAiB,GAAG,KAAK;QAC9B,IAAI,CAACT,oBAAoB,CAACiG,aAAa,EAAE;QACzC/B,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC9D,CAAC,MAAM;QACL;QACA,MAAM+B,UAAU,GAAG,IAAI,CAACnG,KAAK,CAAC+D,QAAQ,CAACG,aAAa,CAACP,GAAG,CAAC,YAAY,CAAC;QACtE,MAAMyC,UAAU,GAAGD,UAAU,GAAGE,QAAQ,CAACF,UAAU,CAAC,GAAG,CAAC;QAExD;QACA,IAAI,CAACpG,MAAM,CAACgG,QAAQ,CAAC,CAAC,uBAAuB,CAAC,EAAE;UAC9CO,WAAW,EAAE;YAAEC,IAAI,EAAEH;UAAU;SAChC,CAAC;MACJ;IACF;IAEA;IACAjH,UAAUA,CAAC4D,IAAY;MACrB,OAAO,IAAI,CAAClC,aAAa,CAAC8C,GAAG,CAACZ,IAAI,CAAgB;IACpD;IAEA;IACAyD,aAAaA,CAACC,MAAc;MAC1B;MACA,MAAMC,oBAAoB,GAAG,IAAI,CAAC3F,kBAAkB,KAAK,QAAQ,GAC7D,IAAI,CAACX,YAAY,EAAEuG,QAAQ,EAAE,IAAI,EAAE,GACnC,IAAI,CAACtG,SAAS,EAAEsG,QAAQ,EAAE,IAAI,EAAE;MACpC,IAAI,IAAI,CAAC5F,kBAAkB,KAAK,QAAQ,EAAE;QACxC,IAAI,CAACgB,gBAAgB,GAAG2E,oBAAoB;QAC5C,IAAI,CAAC7F,aAAa,CAAC8C,GAAG,CAAC,aAAa,CAAC,EAAEiD,QAAQ,CAACF,oBAAoB,CAAC;MACvE,CAAC,MAAM;QACL,IAAI,CAAC1E,cAAc,GAAG0E,oBAAoB;QAC1C,IAAI,CAAC7F,aAAa,CAAC8C,GAAG,CAAC,aAAa,CAAC,EAAEiD,QAAQ,CAACF,oBAAoB,CAAC;MACvE;MAEA;MACA,IAAI,CAAC3F,kBAAkB,GAAG0F,MAAM;MAEhC;MACA,MAAMI,UAAU,GACdJ,MAAM,KAAK,QAAQ,GAAG,IAAI,CAAC1E,gBAAgB,GAAG,IAAI,CAACC,cAAc;MAEnE;MACAwC,UAAU,CAAC,MAAK;QACd,IAAI,CAACpE,YAAY,EAAEwG,QAAQ,CAACC,UAAU,IAAI,EAAE,CAAC;MAC/C,CAAC,CAAC;IACJ;IACA;IACAxC,iBAAiBA,CAAC9D,WAAmB;MACnC,IAAI,CAACJ,iBAAiB,CAAC2G,gBAAgB,CAACpB,MAAM,CAACnF,WAAW,CAAC,CAAC,CAACoE,SAAS,CAAC;QACrEgB,IAAI,EAAGoB,SAAoB,IAAI;UAC7B,IAAI,CAACA,SAAS,EAAE;YACd5C,OAAO,CAACxB,KAAK,CAAC,iCAAiC,EAAEpC,WAAW,CAAC;YAC7D;UACF;UAEA;UACA,IAAI,CAACwB,gBAAgB,GAAGgF,SAAS,CAAC7B,OAAO,IAAI,EAAE;UAC/C,IAAI,CAAClD,cAAc,GAAG+E,SAAS,CAACzD,WAAW,IAAI,EAAE;UAEjD;UACA,IAAI,CAACzC,aAAa,CAACmG,UAAU,CAAC;YAC5BjE,IAAI,EAAEgE,SAAS,CAAChE,IAAI,IAAI,EAAE;YAC1BvD,WAAW,EAAEuH,SAAS,CAACvH,WAAW,IAAI,EAAE;YACxCyD,YAAY,EAAE8D,SAAS,CAAC9D,YAAY,IAAI,WAAW;YACnDkC,SAAS,EAAE4B,SAAS,CAAC5B,SAAS,IAAI,EAAE;YACpCC,OAAO,EAAE2B,SAAS,CAAC3B,OAAO,IAAI,KAAK;YACnC/B,WAAW,EAAE,IAAI,CAACtB,gBAAgB;YAClCuB,WAAW,EAAE,IAAI,CAACtB;WACnB,CAAC;UACF;UACF,IAAI,CAACS,iBAAiB,GAAG,IAAI,CAAC5B,aAAa,CAACoG,WAAW,EAAE;UACvD;UACAzC,UAAU,CAAC,MAAK;YACd,IAAI,IAAI,CAACpE,YAAY,EAAE;cACrB,IAAI,CAACA,YAAY,CAACwG,QAAQ,CAAC,IAAI,CAAC7E,gBAAgB,IAAI,EAAE,CAAC;YACzD;YACA,IAAI,IAAI,CAAC1B,SAAS,EAAE;cAClB,IAAI,CAACA,SAAS,CAACuG,QAAQ,CAAC,IAAI,CAAC5E,cAAc,IAAI,EAAE,CAAC;YACpD;UACF,CAAC,EAAE,GAAG,CAAC;QACT,CAAC;QACDW,KAAK,EAAGiD,GAAG,IAAI;UACbzB,OAAO,CAACxB,KAAK,CAAC,gCAAgC,EAAEiD,GAAG,CAAC;QACtD;OACD,CAAC;IACJ;IAEQsB,kBAAkBA,CAACC,GAAW;MACpC,IAAI;QACF,MAAMC,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACH,GAAG,CAAC;QAC9B,OAAO,OAAOC,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAGD,GAAG;MAClD,CAAC,CAAC,MAAM;QACN,OAAOA,GAAG;MACZ;IACF;IAEQI,cAAcA,CAACjD,IAAmB,EAAEC,IAAY;MACtD,IAAI,CAACvD,YAAY,GAAG,CAAC,GAAG,IAAI,CAACA,YAAY,EAAE;QAAEsD,IAAI;QAAEC;MAAI,CAAE,CAAC;IAC5D;IAEA;IACAiD,iBAAiBA,CAACC,OAAe;MAC/B,IAAG,IAAI,CAACC,oBAAoB,EAAC;QAC3B;MACF;MACA,IAAI,CAACH,cAAc,CAAC,MAAM,EAAEE,OAAO,CAAC;MACpC,IAAI,CAACxG,gBAAgB,GAAG,IAAI;MAC5B,IAAI,CAAC0G,iBAAiB,CAACF,OAAO,CAAC;IACjC;IAEFE,iBAAiBA,CAACF,OAAe;MAC/B;MACA,MAAMG,aAAa,GAAG,IAAI,CAACxH,YAAY,EAAEuG,QAAQ,EAAE,IAAI,IAAI,CAAC9F,aAAa,CAAC8C,GAAG,CAAC,aAAa,CAAC,EAAE9B,KAAK;MACnG,MAAMyB,WAAW,GAAG,IAAI,CAACjD,SAAS,EAAEsG,QAAQ,EAAE,IAAI,IAAI,CAAC9F,aAAa,CAAC8C,GAAG,CAAC,aAAa,CAAC,EAAE9B,KAAK;MAE9F,MAAMwD,OAAO,GAAG;QACdwC,MAAM,EAAEJ,OAAO;QACfK,IAAI,EAAE,SAAS;QACfC,cAAc,EAAE,IAAI;QACpBH,aAAa,EAAEA,aAAa;QAC5BtE,WAAW,EAAEA;OACd;MAED,IAAI,CAACpD,qBAAqB,CAAC8H,iBAAiB,CAC1C3C,OAAO,CAACwC,MAAM,EACdxC,OAAO,CAACyC,IAAI,EACZzC,OAAO,CAAC0C,cAAc,EACtB1C,OAAO,CAAC/B,WAAW,EACnB+B,OAAO,CAACuC,aAAa,CACtB,CAACjD,SAAS,CAAC;QACVgB,IAAI,EAAGsC,QAAa,IAAI;UACtB,IAAIA,QAAQ,IAAIA,QAAQ,CAACA,QAAQ,IAAIA,QAAQ,CAACA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACA,QAAQ,CAACC,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;YACtG,MAAMC,MAAM,GAAGH,QAAQ,CAACA,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC3D,IAAI;YAChD,IAAI,CAACgD,cAAc,CAAC,IAAI,EAAEa,MAAM,CAAC;YAEjC;YACA5D,UAAU,CAAC,MAAK;cACd,IAAI,CAACvD,gBAAgB,GAAG,KAAK;YAC/B,CAAC,EAAE,GAAG,CAAC;UACT,CAAC,MAAM;YACL,IAAI,CAACsG,cAAc,CAAC,IAAI,EAAE,sBAAsB,CAAC;YACjD/C,UAAU,CAAC,MAAK;cACd,IAAI,CAACvD,gBAAgB,GAAG,KAAK;YAC/B,CAAC,EAAE,GAAG,CAAC;UACT;QACF,CAAC;QACD0B,KAAK,EAAGA,KAAK,IAAI;UACfwB,OAAO,CAACxB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;UAClD,IAAI,CAAC4E,cAAc,CAAC,IAAI,EAAE,+BAA+B,CAAC;UAC1D/C,UAAU,CAAC,MAAK;YACd,IAAI,CAACvD,gBAAgB,GAAG,KAAK;UAC/B,CAAC,EAAE,GAAG,CAAC;QACT;OACD,CAAC;IACJ;IACE;IACAoH,aAAa,GAAqB,CAChC;MAAExG,KAAK,EAAE,SAAS;MAAEkB,IAAI,EAAE;IAAe,CAAE,EAC3C;MAAElB,KAAK,EAAE,gBAAgB;MAAEkB,IAAI,EAAE;IAAuB,CAAE,EAC1D;MAAElB,KAAK,EAAE,kBAAkB;MAAEkB,IAAI,EAAE;IAAkB,CAAE,EACvD;MAAElB,KAAK,EAAE,gBAAgB;MAAEkB,IAAI,EAAE;IAAgB,CAAE,EACnD;MAAElB,KAAK,EAAE,oBAAoB;MAAEkB,IAAI,EAAE;IAAoB,CAAE,CAC5D;IACDuF,eAAeA,CAACC,MAAsB;MACpCpE,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEmE,MAAM,CAAC;MAChD;IACF;IACAC,aAAa,GAAGA,CAAA,KAAW;MACzBhE,UAAU,CAAC,MAAK;QACd,IAAI,CAACvD,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACR,aAAa,GAAG,IAAI;QACzB,IAAI,CAACC,iBAAiB,GAAG,IAAI;QAE7B8D,UAAU,CAAC,MAAK;UACd,IAAI,IAAI,CAACpE,YAAY,EAAEqI,OAAO,EAAE;YAC9B,IAAI,CAACrI,YAAY,CAACsI,KAAK,EAAE;YACzB,IAAI,CAACtI,YAAY,CAAC,QAAQ,CAAC,EAAEuI,MAAM,EAAE;YACrC,IAAI,CAACvI,YAAY,CAACwI,oBAAoB,EAAE;UAC1C;UACA,IAAI,IAAI,CAACvI,SAAS,EAAEoI,OAAO,EAAE;YAC3B,IAAI,CAACpI,SAAS,CAAC,QAAQ,CAAC,EAAEsI,MAAM,EAAE;UACpC;QACF,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,CAAC;IACJ,CAAC;IAED;IAEAE,gBAAgBA,CAAA;MACf,MAAMC,aAAa,GAAG,IAAI,CAACjI,aAAa,CAACyE,OAAO;MACjD,MAAMyD,WAAW,GAAG1B,IAAI,CAAC2B,SAAS,CAAC,IAAI,CAACnI,aAAa,CAACoG,WAAW,EAAE,CAAC,KAAKI,IAAI,CAAC2B,SAAS,CAAC,IAAI,CAACvG,iBAAiB,CAAC;MAC/G,OAAOqG,aAAa,IAAIC,WAAW;IACrC;IACExJ,aAAaA,CAAC0J,SAAiB;MAC7B,MAAMC,KAAK,GAAG,IAAI,CAACrI,aAAa,CAAC8C,GAAG,CAACsF,SAAS,CAAC;MAC/C;MACA,MAAME,kBAAkB,GAAG,QAAQ,CAACC,IAAI,CAACH,SAAS,CAAC,GAC/CA,SAAS,GACTA,SAAS,CAACI,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGL,SAAS,CAACM,KAAK,CAAC,CAAC,CAAC;MAC1D,IAAIL,KAAK,IAAIA,KAAK,CAAC5D,OAAO,KAAK4D,KAAK,CAACM,OAAO,IAAIN,KAAK,CAACO,KAAK,CAAC,EAAE;QAC5D,IAAIP,KAAK,CAACQ,MAAM,GAAG,UAAU,CAAC,EAAE;UAC9B,OAAO,GAAGP,kBAAkB,cAAc;QAC5C;MACF;MACA,OAAO,EAAE;IACX;IAEA;IACFQ,WAAWA,CAAA;MACT,IAAI,CAACrH,UAAU,GAAG,MAAM;MACxB,IAAI,CAACJ,iBAAiB,GAAG,iBAAiB;MAC1C,IAAI,CAACC,mBAAmB,GAAG,+CAA+C;MAC1E,IAAI,CAACC,kBAAkB,GAAG,MAAM;MAChC,IAAI,CAACC,kBAAkB,GAAG,SAAS;MACnC,IAAI,CAACJ,gBAAgB,GAAG,IAAI;IAC9B;IAEA;IACA2H,aAAaA,CAAA;MACX,IAAI,CAACtH,UAAU,GAAG,QAAQ;MAC1B,IAAI,CAACJ,iBAAiB,GAAG,mBAAmB;MAC5C,IAAI,CAACC,mBAAmB,GAAG,iDAAiD;MAC5E,IAAI,CAACC,kBAAkB,GAAG,QAAQ;MAClC,IAAI,CAACC,kBAAkB,GAAG,SAAS;MACnC,IAAI,CAACJ,gBAAgB,GAAG,IAAI;IAC9B;IAEA;IACE4H,eAAeA,CAAA;MACb,IAAI,CAAC5H,gBAAgB,GAAG,KAAK;MAC7B,IAAI,CAACgD,MAAM,EAAE,CAAC,CAAC;IACjB;IAEA;IACA6E,UAAUA,CAAA;MACR,IAAI,CAAC7H,gBAAgB,GAAG,KAAK;IAC/B;IACA,IAAI8H,oBAAoBA,CAAA;MACxB,MAAMC,QAAQ,GAAG,IAAI,CAACjJ,kBAAkB,KAAK,QAAQ;MACrD,MAAMkJ,UAAU,GAAG,IAAI,CAACpJ,aAAa,CAAC8C,GAAG,CAAC,aAAa,CAAC,EAAE9B,KAAK,EAAEqI,IAAI,EAAE;MACvE,OAAOF,QAAQ,KAAK,CAACC,UAAU,IAAIA,UAAU,CAAC9B,MAAM,KAAK,CAAC,CAAC;IAC7D;IACAgC,sBAAsBA,CAACtI,KAAa;MAClC,MAAMuI,OAAO,GAAG,IAAI,CAACvJ,aAAa,CAAC8C,GAAG,CAAC,aAAa,CAAC;MACrDyG,OAAO,EAAExD,QAAQ,CAAC/E,KAAK,CAAC;MACxBuI,OAAO,EAAEC,WAAW,EAAE;MACtBD,OAAO,EAAEE,aAAa,EAAE,CAAC,CAAC;IAC5B;IACAC,mBAAmBA,CAAA;MACjB,MAAMC,IAAI,GAAG,IAAI,CAAC3J,aAAa,CAAC8C,GAAG,CAAC,aAAa,CAAC;MAClD,MAAM8G,WAAW,GAAG,IAAI,CAACrK,YAAY,EAAEuG,QAAQ,EAAE,IAAI,EAAE;MACvD,MAAM+D,SAAS,GAAGF,IAAI,EAAE3I,KAAK,IAAI,EAAE;MAEnC;MACA,MAAM8I,OAAO,GAAG,CAACF,WAAW,CAACP,IAAI,EAAE,IAAI,CAACQ,SAAS,CAACR,IAAI,EAAE;MACxD,MAAMU,QAAQ,GAAG,CAAC,EAAEJ,IAAI,EAAEhB,OAAO,IAAImB,OAAO,CAAC;MAE7C,IAAI,CAAChL,eAAe,CAACgD,KAAK,GAAGiI,QAAQ;MACrC,IAAI,CAACjL,eAAe,CAACC,YAAY,GAAGgL,QAAQ,GAAG,0BAA0B,GAAG,IAAI;MAEhF,OAAOA,QAAQ;IACjB;IACE,IAAIlD,oBAAoBA,CAAA;MACxB,MAAME,aAAa,GAAG,IAAI,CAACxH,YAAY,EAAEuG,QAAQ,EAAE,IAAI,IAAI,CAAC9F,aAAa,CAAC8C,GAAG,CAAC,aAAa,CAAC,EAAE9B,KAAK;MACnG,OAAO,CAAC+F,aAAa,IAAIA,aAAa,CAACsC,IAAI,EAAE,CAAC/B,MAAM,KAAK,CAAC;IAC5D;;uCAthBatI,yBAAyB,EAAAlB,EAAA,CAAAkM,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAApM,EAAA,CAAAkM,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAtM,EAAA,CAAAkM,iBAAA,CAAAG,EAAA,CAAAE,cAAA,GAAAvM,EAAA,CAAAkM,iBAAA,CAAAM,EAAA,CAAAC,oBAAA,GAAAzM,EAAA,CAAAkM,iBAAA,CAAAQ,EAAA,CAAAC,oBAAA,GAAA3M,EAAA,CAAAkM,iBAAA,CAAAU,EAAA,CAAAC,iBAAA;IAAA;;YAAzB3L,yBAAyB;MAAA4L,SAAA;MAAAC,SAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;UCnDtCjN,EAAA,CAAAC,cAAA,WAAsB;UAAAD,EAAA,CAAAE,MAAA,8BAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UASzCH,EARR,CAAAC,cAAA,aAAyC,cACL,aAI/B,aAE8D,aAClC;UACvBD,EAAA,CAAAmN,UAAA,IAAAC,yCAAA,kBAAkD;UAClDpN,EAAA,CAAAC,cAAA,qBAAiF;UAA5BD,EAAA,CAAAqN,UAAA,mBAAAC,gEAAA;YAAAtN,EAAA,CAAAuN,aAAA,CAAAC,GAAA;YAAA,OAAAxN,EAAA,CAAAyN,WAAA,CAASP,GAAA,CAAAjL,eAAA,EAAiB;UAAA,EAAC;UAClFjC,EADmF,CAAAG,YAAA,EAAc,EAC3F;UACNH,EAAA,CAAAmN,UAAA,IAAAO,wCAAA,mBAAmD;UA2BrD1N,EAAA,CAAAG,YAAA,EAAM;UAOAH,EAJN,CAAAC,cAAA,eAA2B,eACE,eAEO,eACH;UAAAD,EAAA,CAAAE,MAAA,+BAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACxDH,EAAA,CAAAC,cAAA,sBAOC;UAFCD,EAAA,CAAAqN,UAAA,uBAAAM,oEAAA;YAAA3N,EAAA,CAAAuN,aAAA,CAAAC,GAAA;YAAA,OAAAxN,EAAA,CAAAyN,WAAA,CAAAP,GAAA,CAAArL,UAAA,GAA0BqL,GAAA,CAAAjC,aAAA,EAAe,GAAGiC,GAAA,CAAAlC,WAAA,EAAa;UAAA,EAAC;UAG9DhL,EADG,CAAAG,YAAA,EAAa,EACV;UAGFH,EAFJ,CAAAC,cAAA,eAA0B,eACW,8BAUhC;UADCD,EAAA,CAAAqN,UAAA,yBAAAO,2EAAAC,MAAA;YAAA7N,EAAA,CAAAuN,aAAA,CAAAC,GAAA;YAAA,OAAAxN,EAAA,CAAAyN,WAAA,CAAeP,GAAA,CAAA1B,sBAAA,CAAAqC,MAAA,CAA8B;UAAA,EAAC;UAC/C7N,EAAA,CAAAG,YAAA,EAAkB;UAEvBH,EAAA,CAAAmN,UAAA,KAAAW,yCAAA,kBAAsD;UAKtD9N,EADE,CAAAG,YAAA,EAAM,EACF;UAEJH,EADF,CAAAC,cAAA,eAAuB,eACc;UACjCD,EAAA,CAAAI,SAAA,8BAOmB;UAI3BJ,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACF;UAKJH,EAFF,CAAAC,cAAA,eAA6B,0BAa1B;UAJCD,EADA,CAAAqN,UAAA,0BAAAU,2EAAAF,MAAA;YAAA7N,EAAA,CAAAuN,aAAA,CAAAC,GAAA;YAAA,OAAAxN,EAAA,CAAAyN,WAAA,CAAgBP,GAAA,CAAAvD,eAAA,CAAAkE,MAAA,CAAuB;UAAA,EAAC,yBAAAG,0EAAAH,MAAA;YAAA7N,EAAA,CAAAuN,aAAA,CAAAC,GAAA;YAAA,OAAAxN,EAAA,CAAAyN,WAAA,CACzBP,GAAA,CAAArE,iBAAA,CAAAgF,MAAA,CAAyB;UAAA,EAAC;UASnD7N,EAJQ,CAAAG,YAAA,EAAiB,EACb,EACF,EACD,EACH;UAENH,EAAA,CAAAC,cAAA,qBAgBC;UADCD,EAFA,CAAAqN,UAAA,qBAAAY,iEAAA;YAAAjO,EAAA,CAAAuN,aAAA,CAAAC,GAAA;YAAA,OAAAxN,EAAA,CAAAyN,WAAA,CAAWP,GAAA,CAAAhC,eAAA,EAAiB;UAAA,EAAC,oBAAAgD,gEAAA;YAAAlO,EAAA,CAAAuN,aAAA,CAAAC,GAAA;YAAA,OAAAxN,EAAA,CAAAyN,WAAA,CACnBP,GAAA,CAAA/B,UAAA,EAAY;UAAA,EAAC,oBAAAgD,gEAAA;YAAAnO,EAAA,CAAAuN,aAAA,CAAAC,GAAA;YAAA,OAAAxN,EAAA,CAAAyN,WAAA,CACbP,GAAA,CAAA/B,UAAA,EAAY;UAAA,EAAC;UACxBnL,EAAA,CAAAG,YAAA,EAAY;UAEbH,EAAA,CAAAC,cAAA,qBAUC;UADCD,EAAA,CAAAqN,UAAA,oBAAAe,gEAAA;YAAApO,EAAA,CAAAuN,aAAA,CAAAC,GAAA;YAAA,OAAAxN,EAAA,CAAAyN,WAAA,CAAUP,GAAA,CAAA/F,eAAA,EAAiB;UAAA,EAAC;UAC7BnH,EAAA,CAAAG,YAAA,EAAY;;;UA1ILH,EAAA,CAAAK,SAAA,GAA2B;UAA3BL,EAAA,CAAAM,UAAA,cAAA4M,GAAA,CAAAhL,aAAA,CAA2B;UAG7BlC,EAAA,CAAAK,SAAA,EAAyE;UAAzEL,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAAqO,eAAA,KAAAC,GAAA,EAAApB,GAAA,CAAApL,aAAA,IAAAoL,GAAA,CAAAnL,iBAAA,EAAyE;UAGhD/B,EAAA,CAAAK,SAAA,EAAmC;UAAnCL,EAAA,CAAAuO,WAAA,cAAArB,GAAA,CAAAlL,eAAA,CAAmC;UAE9BhC,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAM,UAAA,UAAA4M,GAAA,CAAAlL,eAAA,CAAsB;UAGvBhC,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAM,UAAA,UAAA4M,GAAA,CAAAlL,eAAA,CAAsB;UAoC3ChC,EAAA,CAAAK,SAAA,GAAwC;UAKxCL,EALA,CAAAM,UAAA,UAAA4M,GAAA,CAAArL,UAAA,qBAAwC,aAAAqL,GAAA,CAAAhD,gBAAA,GAKT;UAO7BlK,EAAA,CAAAK,SAAA,GAAyB;UAKzBL,EALA,CAAAM,UAAA,0BAAyB,uBACH,aAAA4M,GAAA,CAAAtI,gBAAA,WACiB,YAAAsI,GAAA,CAAAjI,gBAAA,GACT,yCACU,iDACQ;UAIhDjF,EAAA,CAAAK,SAAA,GAA2B;UAA3BL,EAAA,CAAAM,UAAA,SAAA4M,GAAA,CAAAtB,mBAAA,GAA2B;UAU3B5L,EAAA,CAAAK,SAAA,GAAsB;UAItBL,EAJA,CAAAM,UAAA,uBAAsB,aAAA4M,GAAA,CAAAtI,gBAAA,QACc,YAAAsI,GAAA,CAAAhI,aAAA,GACT,yCACa,8CACK;UAWnDlF,EAAA,CAAAK,SAAA,GAA+B;UAS/BL,EATA,CAAAM,UAAA,kBAAA4M,GAAA,CAAAxD,aAAA,CAA+B,aAAAwD,GAAA,CAAA7K,YAAA,CACN,cAAA6K,GAAA,CAAA5K,gBAAA,CACK,qCACM,+BACN,6BAGF,uBACN,eAAA4K,GAAA,CAAAnE,oBAAA,CACa;UAS3C/I,EAAA,CAAAK,SAAA,EAAyB;UAWzBL,EAXA,CAAAM,UAAA,SAAA4M,GAAA,CAAA5J,gBAAA,CAAyB,UAAA4J,GAAA,CAAA3J,iBAAA,CACE,YAAA2J,GAAA,CAAA1J,mBAAA,CACI,wBACR,mBAAA0J,GAAA,CAAAvJ,UAAA,8BACmC,cAAAuJ,GAAA,CAAAxJ,kBAAA,CAC1B,mBACd,oBACC,qBACC,uBAAAwJ,GAAA,CAAAzJ,kBAAA,CACqB,mCACP,4BAAAyJ,GAAA,CAAAxJ,kBAAA,CACY;UAQ9C1D,EAAA,CAAAK,SAAA,EAAsB;UAMtBL,EANA,CAAAM,UAAA,SAAA4M,GAAA,CAAAtJ,aAAA,CAAsB,YAAAsJ,GAAA,CAAArJ,WAAA,CAEC,wBACA,mBAGL;;;qBDrGhB3E,YAAY,EAAAsP,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZvP,mBAAmB,EAAAgN,EAAA,CAAAwC,aAAA,EAAAxC,EAAA,CAAAyC,eAAA,EAAAzC,EAAA,CAAA0C,oBAAA,EAAA1C,EAAA,CAAA2C,iBAAA,EAAA3C,EAAA,CAAA4C,oBAAA,EAAA5C,EAAA,CAAA6C,kBAAA,EACnBzP,mBAAmB,EACnBE,mBAAmB,EACnBC,eAAe,EACfF,oBAAoB,EACpBK,mBAAmB,EACnBD,cAAc,EACdG,mBAAmB,EAAAkP,EAAA,CAAAC,sBAAA,EACnBvP,aAAa;MAAAwP,MAAA;IAAA;;SAKJjO,yBAAyB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}