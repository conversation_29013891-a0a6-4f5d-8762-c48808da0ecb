{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { Subject, takeUntil } from 'rxjs';\nimport { FormsModule, Validators } from '@angular/forms';\n// Import child components\nimport { ChatInterfaceComponent } from '@shared/components/chat-interface/chat-interface.component';\nimport { AgentActivityComponent } from './components/agent-activity/agent-activity.component';\nimport { AgentOutputComponent } from './components/agent-output/agent-output.component';\nimport { ButtonComponent, IconComponent, TabsComponent } from '@ava/play-comp-library';\nimport { environment } from '@shared/environments/environment';\nimport workflowConstants from './../constants/workflows.json';\nimport { ExecutionStatus } from '@shared/models/execution.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@shared/services/workflow.service\";\nimport * as i3 from \"@shared/index\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/common\";\nconst _c0 = () => ({\n  background: \"linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)\",\n  \"--button-effect-color\": \"33, 90, 214\",\n  \"border-radius\": \"8px\",\n  \"box-shadow\": \"none\"\n});\nfunction WorkflowExecutionComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"app-agent-activity\", 23);\n    i0.ɵɵlistener(\"saveLogs\", function WorkflowExecutionComponent_div_22_Template_app_agent_activity_saveLogs_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.saveLogs());\n    })(\"controlAction\", function WorkflowExecutionComponent_div_22_Template_app_agent_activity_controlAction_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.handleControlAction($event));\n    })(\"onOutPutBtnClick\", function WorkflowExecutionComponent_div_22_Template_app_agent_activity_onOutPutBtnClick_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onTabChange({\n        id: \"nav-products\",\n        label: \"Agent Output\"\n      }));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"activityLogs\", ctx_r2.workflowLogs)(\"executionDetails\", ctx_r2.executionDetails)(\"progress\", ctx_r2.progress)(\"isRunning\", ctx_r2.isRunning)(\"status\", ctx_r2.status);\n  }\n}\nfunction WorkflowExecutionComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"app-agent-output\", 24);\n    i0.ɵɵlistener(\"export\", function WorkflowExecutionComponent_div_23_Template_app_agent_output_export_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.exportResults(\"output\"));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"outputs\", ctx_r2.taskMessage);\n  }\n}\nexport let WorkflowExecutionComponent = /*#__PURE__*/(() => {\n  class WorkflowExecutionComponent {\n    route;\n    router;\n    workflowService;\n    tokenStorage;\n    loaderService;\n    formBuilder;\n    navigationTabs = [{\n      id: 'nav-home',\n      label: 'Agent Activity'\n    }, {\n      id: 'nav-products',\n      label: 'Agent Output'\n    }, {\n      id: 'nav-services',\n      label: 'Preview',\n      disabled: true\n    }];\n    // Workflow details\n    workflowId = null;\n    workflowName = 'Workflow';\n    constants = workflowConstants;\n    chatInterfaceComp;\n    // Activity logs\n    activityLogs = [];\n    activityProgress = 0;\n    executionDetails;\n    isRunning = false;\n    status = ExecutionStatus.notStarted;\n    // Chat messages\n    chatMessages = [];\n    isProcessingChat = false;\n    inputText = '';\n    // Agent outputs\n    agentOutputs = [];\n    workflowForm;\n    fileType = '.zip';\n    // Execution state\n    executionStartTime = null;\n    executionCompleted = false;\n    executionId;\n    workflowLogs = [];\n    enableStreamingLog = environment.enableLogStreaming || 'all';\n    isExecutionComplete = false;\n    progressInterval;\n    // Component lifecycle\n    destroy$ = new Subject();\n    selectedTab = 'Agent Activity';\n    demoTabs = [{\n      id: 'activity',\n      label: 'Agent Activity'\n    }, {\n      id: 'agents',\n      label: 'Agent Output'\n    }, {\n      id: 'preview',\n      label: 'Preview',\n      disabled: true\n    }];\n    errorMsg = false;\n    resMessage;\n    taskMessage = [];\n    isJsonValid = false;\n    disableChat = false;\n    selectedFiles = [];\n    workflowAgents = [];\n    userInputList = [];\n    progress = 0;\n    isLoading = false;\n    loaderColor = '';\n    inputFieldOrder = [];\n    currentInputIndex = 0;\n    activeTabId = 'nav-home';\n    constructor(route, router, workflowService, tokenStorage, loaderService, formBuilder) {\n      this.route = route;\n      this.router = router;\n      this.workflowService = workflowService;\n      this.tokenStorage = tokenStorage;\n      this.loaderService = loaderService;\n      this.formBuilder = formBuilder;\n    }\n    ngOnInit() {\n      this.loaderService.disableLoader();\n      this.selectedTab = 'Agent Activity';\n      this.executionId = crypto.randomUUID();\n      // Get workflow ID from route params\n      this.route.paramMap.pipe(takeUntil(this.destroy$)).subscribe(params => {\n        this.workflowId = params.get('id');\n        if (this.workflowId) {\n          this.loadWorkflow(this.workflowId);\n        } else {\n          // No workflow ID, redirect back to workflows page\n          this.router.navigate(['/build/workflows']);\n        }\n      });\n      // this.executeWorkflow()\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n      this.loaderService.enableLoader();\n    }\n    onTabChange(event) {\n      this.selectedTab = event.label;\n      this.activeTabId = event.id;\n      console.log('Tab changed:', event);\n    }\n    // Load workflow data\n    loadWorkflow(id) {\n      // In a real app, this would fetch the workflow from a service\n      console.log(`Loading workflow with ID: ${id}`);\n      this.chatMessages = [{\n        from: 'ai',\n        text: 'I am your workflow assistant. I will help you in executing this workflow.'\n      }];\n      this.workflowForm = this.formBuilder.group({});\n      this.workflowService.getWorkflowById(id).subscribe({\n        next: res => {\n          this.workflowAgents = res.workflowAgents;\n          this.userInputList = this.extractInputField(this.workflowAgents);\n          if (this.userInputList.length === 0) {\n            this.disableChat = true;\n          }\n          this.workflowName = res.name;\n          this.initializeForm();\n          this.startInputCollection();\n        },\n        error: err => {\n          this.disableChat = true;\n          this.chatInterfaceComp.addAiResponse('No input is required for this workflow. Click on send button to execute the workflow.');\n          console.log(err);\n        }\n      });\n    }\n    extractInputField(pipeLineAgents) {\n      const PLACEHOLDER_PATTERNS = /(%\\d+\\$s)|\\{\\{([a-zA-Z0-9-_]+)\\}\\}/g;\n      const placeholderMap = {};\n      pipeLineAgents.forEach(agent => {\n        const agentName = agent?.agentDetails?.name;\n        const agentDescription = agent?.agentDetails?.description;\n        const matches = agentDescription.matchAll(PLACEHOLDER_PATTERNS) || [];\n        for (const match of matches) {\n          const placeholder = match[1] || match[2];\n          const placeholderInput = match[0];\n          if (!placeholderMap[placeholder]) {\n            placeholderMap[placeholder] = {\n              agents: new Set(),\n              inputs: new Set()\n            };\n            ;\n          }\n          placeholderMap[placeholder].agents.add(agentName);\n          placeholderMap[placeholder].inputs.add(placeholderInput);\n        }\n      });\n      return Object.entries(placeholderMap).map(([placeholder, {\n        agents,\n        inputs\n      }]) => ({\n        name: [...agents].length > 2 ? `${[...agents].slice(0, -1).join(\", \")} and ${[...agents].at(-1)}` : [...agents].join(\" and \"),\n        placeholder,\n        input: [...inputs][0]\n      }));\n    }\n    isImageInput(input) {\n      const match = input.match(/{{(.*?)}}/);\n      if (match && match[1]) {\n        const variableName = match[1].trim();\n        return variableName.startsWith('image') || variableName.startsWith('Image');\n      }\n      return false;\n    }\n    initializeForm() {\n      this.userInputList.forEach(label => {\n        this.workflowForm.addControl(label.input, this.formBuilder.control('', Validators.required));\n      });\n    }\n    isInputValid() {\n      return this.workflowForm.valid && this.workflowId;\n    }\n    startFakeProgress() {\n      this.progress = 0;\n      this.progressInterval = setInterval(() => {\n        if (this.progress < 90) {\n          this.progress += 5; // Increase slowly\n        }\n      }, 200); // Adjust speed\n    }\n    stopFakeProgress() {\n      clearInterval(this.progressInterval);\n      this.progress = 100;\n      setTimeout(() => {\n        this.isLoading = false;\n      }, 500); // Small delay to let user see 100%\n    }\n    // Handle new chat message from user\n    handleChatMessage(message) {\n      // console.log('message ', message, 'is blank', message.trim() === '');\n      this.isProcessingChat = true;\n      if (message.trim() === '') {\n        if (this.inputFieldOrder.length === 0) {\n          this.chatInterfaceComp.addAiResponse('Executing the workflow...');\n          this.executeWorkflow();\n        }\n        return;\n      }\n      if (this.isExecutionComplete || this.currentInputIndex === this.inputFieldOrder.length) {\n        this.chatInterfaceComp.addAiResponse('Executing the workflow...');\n        this.executeWorkflow();\n        return;\n      }\n      const field = this.inputFieldOrder[this.currentInputIndex];\n      if (this.isImageInput(field)) {\n        // Ignore text input, wait for file input\n        this.chatInterfaceComp.addAiResponse(`Please upload an image file for ${field}`);\n        return;\n      }\n      this.workflowForm.get(field)?.setValue(message);\n      this.currentInputIndex++;\n      if (this.currentInputIndex < this.inputFieldOrder.length) {\n        this.promptForCurrentField();\n      } else {\n        this.chatInterfaceComp.addAiResponse('Thank you for the input! Executing the workflow...');\n        this.executeWorkflow();\n      }\n    }\n    // Save execution logs\n    saveLogs() {\n      console.log('Saving execution logs...');\n      // This would typically save to a service\n    }\n    // Export results\n    exportResults(section) {\n      console.log(`Exporting ${section} data...`);\n      if (section === 'activity') {\n        const data = this.activityLogs.map(log => `[${log.timestamp}] ${log.message}`).join('\\n');\n        this.downloadAsFile(data, 'workflow-activity-logs.txt', 'text/plain');\n      } else {\n        const data = JSON.stringify(this.agentOutputs, null, 2);\n        this.downloadAsFile(data, 'workflow-outputs.json', 'application/json');\n      }\n    }\n    // Helper method to download data as a file\n    downloadAsFile(data, filename, type) {\n      const blob = new Blob([data], {\n        type\n      });\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = filename;\n      link.click();\n      URL.revokeObjectURL(url);\n    }\n    // Handle controls for execution\n    handleControlAction(action) {\n      console.log(`Control action: ${action}`);\n      // In a real app, this would control the workflow execution\n      if (action === 'play') {\n        this.isRunning = true;\n      } else if (action === 'pause' || action === 'stop') {\n        this.isRunning = false;\n      }\n    }\n    // Navigate back to workflow listing\n    navigateBack() {\n      this.router.navigate(['/build/workflows']);\n    }\n    // Navigate to edit workflow\n    editWorkflow() {\n      if (this.workflowId) {\n        this.router.navigate(['/build/workflows/edit', this.workflowId]);\n      }\n    }\n    logExecutionStatus(delay = 2000) {\n      setTimeout(() => {\n        if (!this.isExecutionComplete) {\n          console.log(this.constants);\n          console.log(this.constants['labels'].workflowExecProcessing);\n          this.workflowLogs.push({\n            content: this.constants['labels'].workflowExecProcessing,\n            color: '#F9DB24'\n          });\n        }\n      }, delay);\n    }\n    // public parseAnsiString(ansiString: string) {\n    //   const regex = ansiRegex();\n    //   const parts = ansiString.split(regex);\n    //   const matches = [...ansiString.matchAll(regex)];\n    //   parts.forEach((part, index) => {\n    //     if (part.trim() !== '') {\n    //       let colorCode = matches[index - 1][0];\n    //       if (index - 2 >= 0 && matches[index - 2]?.includes('\\u001b[1m')) {\n    //         colorCode = `\\u001b[1m${colorCode}`;\n    //       }\n    //       this.workflowLogs.push({\n    //         content: part,\n    //         color: this.colorMap[colorCode] || 'white',\n    //       });\n    //     }\n    //   });\n    // }\n    getWorkflowLogs(executionId) {\n      this.workflowService.workflowLogConnect(executionId).pipe(takeUntil(this.destroy$)).subscribe({\n        next: message => {\n          console.log('message: ', message);\n          const {\n            content,\n            color\n          } = message;\n          if (color) {\n            this.workflowLogs.push({\n              content,\n              color\n            });\n          } else if (this.enableStreamingLog === 'all') {\n            // this.parseAnsiString(content);\n          }\n        },\n        error: err => {\n          this.workflowLogs.push({\n            content: this.constants['workflowLog'],\n            color: 'red'\n          });\n          console.error('WebSocket error:', err);\n        },\n        complete: () => {\n          this.logExecutionStatus();\n          console.log('WebSocket connection closed');\n        }\n      });\n    }\n    // public parseAnsiString(ansiString: string) {\n    //   const regex = ansiRegex();\n    //   const parts = ansiString.split(regex);\n    //   const matches = [...ansiString.matchAll(regex)];\n    //   parts.forEach((part, index) => {\n    //     if (part.trim() !== '') {\n    //       let colorCode = matches[index-1][0];\n    //       if(index - 2 >= 0 && matches[index-2]?.includes('\\u001b[1m')) {\n    //         colorCode = `\\u001b[1m${colorCode}`;\n    //       }\n    //       this.workflowLogs.push({\n    //         content: part, \n    //         color: this.colorMap[colorCode] || 'white', \n    //       });\n    //     }\n    //   });\n    // }\n    validateJson(output) {\n      this.isJsonValid = false;\n      try {\n        const parsedOutput = JSON.parse(output);\n        this.isJsonValid = true;\n        return parsedOutput;\n      } catch (e) {\n        return null;\n      }\n    }\n    executeWorkflow() {\n      let payload = new FormData();\n      let queryString = '';\n      this.status = ExecutionStatus.running;\n      if (this.selectedFiles.length) {\n        this.selectedFiles.forEach(file => {\n          payload.append('files', file);\n        });\n        payload.append('workflowId', this.workflowId);\n        payload.append('userInputs', JSON.stringify(this.workflowForm.value));\n        payload.append('user', this.tokenStorage.getDaUsername());\n        payload.append('executionId', this.executionId);\n        queryString = '/files';\n      } else {\n        payload = {\n          pipeLineId: this.workflowId,\n          userInputs: this.workflowForm.value,\n          executionId: this.executionId,\n          user: this.tokenStorage.getDaUsername()\n        };\n      }\n      this.getWorkflowLogs(this.executionId);\n      this.startFakeProgress();\n      this.workflowService.executeWorkflow(payload, queryString).pipe(takeUntil(this.destroy$)).subscribe({\n        next: res => {\n          this.isProcessingChat = false;\n          this.isRunning = false;\n          this.chatInterfaceComp.addAiResponse(res?.message || \"Workflow execution completed successfully!\");\n          if (res?.workflowResponse?.pipeline?.output) {\n            this.isExecutionComplete = true;\n            // console.log(this.constants['labels'].workflowExecComplete);\n            this.workflowLogs.push({\n              content: this.constants['labels'].workflowExecComplete,\n              color: '#0F8251'\n            });\n            this.errorMsg = false;\n            this.resMessage = res?.workflowResponse?.pipeline?.output;\n            this.agentOutputs = res?.workflowResponse?.pipeline?.tasksOutputs.map(task => {\n              return {\n                id: task?.id || '',\n                title: task?.title || '',\n                content: task?.content || '',\n                agentName: task?.agentName || '',\n                timestamp: task?.timestamp || '',\n                type: task?.type || '',\n                description: task?.description || '',\n                expected_output: task?.expected_output || '',\n                summary: task?.summary || '',\n                raw: task?.raw || ''\n              };\n            });\n            this.taskMessage = res?.workflowResponse?.pipeline?.tasksOutputs.map(task => {\n              return {\n                description: task.description,\n                summary: task.summary,\n                raw: task.raw,\n                expected_output: task.expected_output\n              };\n            });\n            // if(\"file_download_url\" in res?.pipeline){\n            //   this.isFileWriter = true;\n            //   this.fileDownloadLink = res?.pipeline?.file_download_url;\n            //   if(!this.fileDownloadLink){\n            //     this.fileDownloadUrlError = [];\n            //     this.fileDownloadUrlError.push(\"Output file is not generated yet!\")\n            //   }\n            // }\n            // this.isAccordian = true\n          }\n          this.validateJson(this.resMessage);\n          this.status = ExecutionStatus.completed;\n          this.stopFakeProgress();\n          this.selectedFiles = [];\n        },\n        error: error => {\n          this.isExecutionComplete = true;\n          this.isProcessingChat = false;\n          this.errorMsg = true;\n          this.resMessage = error?.error?.detail;\n          this.workflowService.workflowLogDisconnect();\n          this.workflowLogs.push({\n            content: this.constants['labels'].workflowLogFailed,\n            color: 'red'\n          });\n          this.chatInterfaceComp.addAiResponse('Something went wrong, Workflow execution has failed.');\n          this.selectedFiles = [];\n          this.stopFakeProgress();\n          console.log('error is', error.message);\n        }\n      });\n    }\n    // public asyncExecutePipeline() {\n    //   const payload: FormData = new FormData();\n    //   if (this.selectedFiles?.length) {\n    //     for (const element of this.selectedFiles) {\n    //       payload.append('files', element)\n    //     }\n    //   }\n    //   payload.append('pipeLineId', String(this.workflowId));\n    //   payload.append('userInputs', JSON.stringify(this.workflowForm.value));\n    //   payload.append('user', this.tokenStorage.getDaUsername() || '');\n    //   payload.append('executionId', this.executionId);\n    //   this.workflowService.asyncExecutePipeline(payload).pipe().subscribe({\n    //     next: (res: any) => {\n    //       if(res) {\n    //         // res handling\n    //         console.log(res);\n    //       }\n    //     },\n    //     error: e => {\n    //       // error handling\n    //       console.log(e);\n    //     }\n    //   })\n    // }\n    handleAttachment() {\n      console.log('handleAttachment');\n    }\n    onAttachmentsSelected(files) {\n      if (this.currentInputIndex === this.inputFieldOrder.length || this.inputFieldOrder.length === 0) {\n        this.selectedFiles = files;\n        return;\n      }\n      const field = this.inputFieldOrder[this.currentInputIndex];\n      if (this.isImageInput(field)) {\n        if (files && files.length > 0) {\n          this.onImageSelected(files[0]);\n        }\n      } else {\n        this.selectedFiles = files;\n      }\n    }\n    startInputCollection() {\n      this.inputFieldOrder = Object.keys(this.workflowForm.controls);\n      this.currentInputIndex = 0;\n      if (this.inputFieldOrder.length > 0) {\n        this.promptForCurrentField();\n      } else {\n        this.disableChat = true;\n        this.chatInterfaceComp.addAiResponse('No input is required for this workflow. Click on send button to execute the workflow.');\n      }\n    }\n    promptForCurrentField() {\n      const field = this.inputFieldOrder[this.currentInputIndex];\n      if (this.isImageInput(field)) {\n        this.fileType = '.jpeg,.png,.jpg,.svg';\n        this.chatInterfaceComp.addAiResponse(`Please upload an image for ${field}`);\n        // UI should now show a file input for the user\n      } else {\n        this.fileType = '.zip'; // or whatever default you want for non-image\n        this.chatInterfaceComp.addAiResponse(`Please enter the value of ${field}`);\n      }\n    }\n    onImageSelected(file) {\n      const field = this.inputFieldOrder[this.currentInputIndex];\n      if (!this.isImageInput(field)) return;\n      const reader = new FileReader();\n      reader.onload = () => {\n        const base64String = reader.result;\n        this.workflowForm.get(field)?.setValue(base64String);\n        this.currentInputIndex++;\n        if (this.currentInputIndex < this.inputFieldOrder.length) {\n          this.promptForCurrentField();\n        } else {\n          this.chatInterfaceComp.addAiResponse('Thank you! Running the workflow...');\n          this.executeWorkflow();\n        }\n      };\n      reader.readAsDataURL(file);\n    }\n    static ɵfac = function WorkflowExecutionComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || WorkflowExecutionComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.WorkflowService), i0.ɵɵdirectiveInject(i3.TokenStorageService), i0.ɵɵdirectiveInject(i3.LoaderService), i0.ɵɵdirectiveInject(i4.FormBuilder));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: WorkflowExecutionComponent,\n      selectors: [[\"app-workflow-execution\"]],\n      viewQuery: function WorkflowExecutionComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(ChatInterfaceComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.chatInterfaceComp = _t.first);\n        }\n      },\n      decls: 24,\n      vars: 15,\n      consts: [[\"chatInterfaceComp\", \"\"], [1, \"workflow-execution-container\"], [\"width\", \"0\", \"height\", \"0\", 2, \"position\", \"absolute\"], [\"id\", \"gradient\", \"x1\", \"0%\", \"y1\", \"0%\", \"x2\", \"100%\", \"y2\", \"0%\"], [\"offset\", \"0%\", \"stop-color\", \"#6566CD\"], [\"offset\", \"100%\", \"stop-color\", \"#F96CAB\"], [\"role\", \"main\", 1, \"execution-content\"], [\"role\", \"region\", \"aria-label\", \"Workflow Playground\", 1, \"column\", \"playground-column\"], [1, \"column-content\", \"playground-content\"], [1, \"column-header\"], [\"id\", \"playground-title\"], [\"iconName\", \"panelLeft\", \"iconColor\", \"#1A46A7\"], [\"aria-labelledby\", \"playground-title\", 1, \"chat-container\"], [3, \"messageSent\", \"attachmentClicked\", \"attachmentSelected\", \"messages\", \"isLoading\", \"isDisabled\", \"inputText\", \"isOptionalInput\", \"handleFileUpload\", \"fileType\", \"maxFileSize\"], [\"role\", \"region\", \"aria-label\", \"Agent Output\", 1, \"column\", \"output-column\"], [1, \"column-content\"], [1, \"column-header\", \"row\"], [1, \"col-7\"], [\"variant\", \"button\", \"buttonShape\", \"pill\", \"ariaLabel\", \"Pill navigation tabs\", 3, \"tabChange\", \"tabs\", \"activeTabId\", \"showContentPanels\"], [1, \"col-5\", \"right-section-header\"], [\"label\", \"Send for Approval\", \"variant\", \"primary\", \"size\", \"medium\", 3, \"customStyles\"], [\"style\", \"height: 100%\", 4, \"ngIf\"], [2, \"height\", \"100%\"], [3, \"saveLogs\", \"controlAction\", \"onOutPutBtnClick\", \"activityLogs\", \"executionDetails\", \"progress\", \"isRunning\", \"status\"], [3, \"export\", \"outputs\"]],\n      template: function WorkflowExecutionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(1, \"svg\", 2)(2, \"defs\")(3, \"linearGradient\", 3);\n          i0.ɵɵelement(4, \"stop\", 4)(5, \"stop\", 5);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 7)(8, \"div\", 8)(9, \"div\", 9)(10, \"h2\", 10);\n          i0.ɵɵelement(11, \"ava-icon\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"div\", 12)(13, \"app-chat-interface\", 13, 0);\n          i0.ɵɵlistener(\"messageSent\", function WorkflowExecutionComponent_Template_app_chat_interface_messageSent_13_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.handleChatMessage($event));\n          })(\"attachmentClicked\", function WorkflowExecutionComponent_Template_app_chat_interface_attachmentClicked_13_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.handleAttachment());\n          })(\"attachmentSelected\", function WorkflowExecutionComponent_Template_app_chat_interface_attachmentSelected_13_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onAttachmentsSelected($event));\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(15, \"div\", 14)(16, \"div\", 15)(17, \"div\", 16)(18, \"div\", 17)(19, \"ava-tabs\", 18);\n          i0.ɵɵlistener(\"tabChange\", function WorkflowExecutionComponent_Template_ava_tabs_tabChange_19_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onTabChange($event));\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 19);\n          i0.ɵɵelement(21, \"ava-button\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(22, WorkflowExecutionComponent_div_22_Template, 2, 5, \"div\", 21)(23, WorkflowExecutionComponent_div_23_Template, 2, 1, \"div\", 21);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(13);\n          i0.ɵɵpropertyInterpolate(\"fileType\", ctx.fileType);\n          i0.ɵɵproperty(\"messages\", ctx.chatMessages)(\"isLoading\", ctx.isProcessingChat)(\"isDisabled\", ctx.disableChat)(\"inputText\", ctx.inputText)(\"isOptionalInput\", true)(\"handleFileUpload\", true)(\"maxFileSize\", 10485760);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"tabs\", ctx.navigationTabs)(\"activeTabId\", ctx.activeTabId)(\"showContentPanels\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"customStyles\", i0.ɵɵpureFunction0(14, _c0));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTab === \"Agent Activity\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTab === \"Agent Output\");\n        }\n      },\n      dependencies: [CommonModule, i5.NgIf, FormsModule, ChatInterfaceComponent, AgentActivityComponent, AgentOutputComponent, TabsComponent, ButtonComponent, IconComponent],\n      styles: [\".ava-tabs {\\n  background: none !important;\\n}\\n\\n  .ava-tabs__container {\\n  border-radius: none !important;\\n  border: none !important;\\n  padding: 0 !important;\\n  box-shadow: none !important;\\n  background: none !important;\\n}\\n\\n  .ava-tabs__list {\\n  padding: 0 !important;\\n}\\n\\n.workflow-execution-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 93%;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 20px 24px;\\n  border-bottom: 1px solid var(--border-color, #e0e0e0);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-title[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 24px;\\n  font-weight: 600;\\n  color: var(--text-color, #333);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-title[_ngcontent-%COMP%]   .header-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%], \\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 8px 16px;\\n  border-radius: 5px;\\n  font-weight: 500;\\n  font-size: 14px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%], \\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  margin-right: 6px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%] {\\n  background-color: var(--bg-muted, #f5f5f5);\\n  border: 1px solid var(--border-color, #e0e0e0);\\n  color: var(--text-color, #333);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]:hover {\\n  background-color: var(--bg-muted-hover, #e9e9e9);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%] {\\n  background-color: var(--card-bg, #fff);\\n  border: 1px solid var(--border-color, #e0e0e0);\\n  color: var(--text-color, #333);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]:hover {\\n  background-color: var(--card-bg-hover, #f9f9f9);\\n  border-color: var(--border-color-dark, #d0d0d0);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-grow: 1;\\n  height: 95vh;\\n  overflow: hidden;\\n  gap: 20px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 8px 16px;\\n  background-color: #e9effd;\\n  color: #fff;\\n  height: 64px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: #1a46a7;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  padding: 8px 16px;\\n  background-color: var(--card-bg, white);\\n  position: relative;\\n  border: 1px solid transparent;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: transparent;\\n  background-image: linear-gradient(90deg, #6566cd 0%, #f96cab 100%);\\n  -webkit-background-clip: text;\\n  background-clip: text;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  border-radius: 8px;\\n  padding: 1px;\\n  background: linear-gradient(90deg, #6566cd 0%, #f96cab 100%);\\n  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);\\n  mask-composite: exclude;\\n  pointer-events: none;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%]:hover {\\n  background-color: var(--bg-muted-hover, #f9f5ff);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 18px;\\n  height: 18px;\\n  fill: none;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]   path[_ngcontent-%COMP%] {\\n  stroke-width: 2;\\n  stroke: url(#gradient);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 0;\\n  display: flex;\\n  flex-direction: column;\\n  scrollbar-width: none;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 8px;\\n  height: 8px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: var(--scrollbar-track, #f1f1f1);\\n  border-radius: 4px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: var(--scrollbar-thumb, #d1d1d1);\\n  border-radius: 4px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: var(--scrollbar-thumb-hover, #b1b1b1);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%]   .nav-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  gap: 16px;\\n  background-color: #e9effd;\\n  padding: 0 15px 0 0;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%]   .nav-wrapper[_ngcontent-%COMP%]   .right-inner-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%]   .nav-wrapper[_ngcontent-%COMP%]   .right-inner-wrapper[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #fff;\\n  padding: 0 10px;\\n  margin: 0px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .activity-column[_ngcontent-%COMP%] {\\n  background: transparent;\\n  box-shadow: none;\\n  border-radius: 8px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .activity-column[_ngcontent-%COMP%]   app-agent-activity[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .playground-column[_ngcontent-%COMP%] {\\n  flex: 3.5;\\n  background: var(--card-bg);\\n  border-radius: 8px;\\n  box-shadow: 0 4px 15px var(--card-shadow, rgba(0, 0, 0, 0.05));\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .playground-column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .header-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  gap: 12px;\\n  align-items: center;\\n  color: #1a46a7;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .playground-column[_ngcontent-%COMP%]   .playground-content[_ngcontent-%COMP%] {\\n  padding: 0;\\n  height: 100%;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .playground-column[_ngcontent-%COMP%]   .playground-content[_ngcontent-%COMP%]   .chat-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  min-height: 400px;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  position: relative;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .playground-column[_ngcontent-%COMP%]   .playground-content[_ngcontent-%COMP%]   .chat-container[_ngcontent-%COMP%]     .prompt-container {\\n  margin-top: 30px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .playground-column[_ngcontent-%COMP%]   .playground-content[_ngcontent-%COMP%]   .chat-container[_ngcontent-%COMP%]   .playground-title[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: var(--dashboard-primary, #6566cd);\\n  margin: 16px 16px;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .playground-column[_ngcontent-%COMP%]   .playground-content[_ngcontent-%COMP%]   .chat-container[_ngcontent-%COMP%]   app-chat-interface[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  flex: 1;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .output-column[_ngcontent-%COMP%] {\\n  flex: 5.5;\\n  background-color: var(--card-bg, white);\\n  border-radius: 8px;\\n  box-shadow: 0 4px 15px var(--card-shadow, rgba(0, 0, 0, 0.05));\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .output-column[_ngcontent-%COMP%]   app-agent-output[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n  nav.ava-tabs__list {\\n  background: #e9effd;\\n  padding: 4px;\\n}\\n\\n  button.ava-button.primary.active {\\n  background: #616161;\\n  color: #fff;\\n}\\n\\n  .column-header .ava-tabs[data-variant=button] .ava-tabs__tab--pill {\\n  border-radius: 8px !important;\\n  padding: 12px 16px !important;\\n  font-family: \\\"Mulish\\\";\\n}\\n\\n  .ava-tabs[data-variant=button] .ava-tabs__tab--active .ava-tabs__tab-text {\\n  color: white;\\n}\\n\\n  .ava-tabs__tab-text {\\n  color: #4c515b;\\n  font-family: \\\"Mulish\\\";\\n  font-weight: 600;\\n}\\n\\n  .right-section-header .ava-button.secondary {\\n  color: #1a46a7;\\n  border: none;\\n}\\n\\n  .right-section-header .ava-button.secondary:hover {\\n  color: #1a46a7;\\n  border: none;\\n}\\n\\n.right-section-header[_ngcontent-%COMP%] {\\n  text-align: end;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return WorkflowExecutionComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "Subject", "takeUntil", "FormsModule", "Validators", "ChatInterfaceComponent", "AgentActivityComponent", "AgentOutputComponent", "ButtonComponent", "IconComponent", "TabsComponent", "environment", "workflowConstants", "ExecutionStatus", "i0", "ɵɵelementStart", "ɵɵlistener", "WorkflowExecutionComponent_div_22_Template_app_agent_activity_saveLogs_1_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "saveLogs", "WorkflowExecutionComponent_div_22_Template_app_agent_activity_controlAction_1_listener", "$event", "handleControlAction", "WorkflowExecutionComponent_div_22_Template_app_agent_activity_onOutPutBtnClick_1_listener", "onTabChange", "id", "label", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "workflowLogs", "executionDetails", "progress", "isRunning", "status", "WorkflowExecutionComponent_div_23_Template_app_agent_output_export_1_listener", "_r4", "exportResults", "taskMessage", "WorkflowExecutionComponent", "route", "router", "workflowService", "tokenStorage", "loaderService", "formBuilder", "navigationTabs", "disabled", "workflowId", "workflowName", "constants", "chatInterfaceComp", "activityLogs", "activityProgress", "notStarted", "chatMessages", "isProcessingChat", "inputText", "agentOutputs", "workflowForm", "fileType", "executionStartTime", "executionCompleted", "executionId", "enableStreamingLog", "enableLogStreaming", "isExecutionComplete", "progressInterval", "destroy$", "selectedTab", "demoTabs", "errorMsg", "resMessage", "isJsonValid", "disable<PERSON>hat", "selectedFiles", "workflowAgents", "userInputList", "isLoading", "loaderColor", "inputFieldOrder", "currentInputIndex", "activeTabId", "constructor", "ngOnInit", "disable<PERSON><PERSON><PERSON>", "crypto", "randomUUID", "paramMap", "pipe", "subscribe", "params", "get", "loadWorkflow", "navigate", "ngOnDestroy", "next", "complete", "<PERSON><PERSON><PERSON><PERSON>", "event", "console", "log", "from", "text", "group", "getWorkflowById", "res", "extractInputField", "length", "name", "initializeForm", "startInputCollection", "error", "err", "addAiResponse", "pipeLineAgents", "PLACEHOLDER_PATTERNS", "placeholderM<PERSON>", "for<PERSON>ach", "agent", "<PERSON><PERSON><PERSON>", "agentDetails", "agentDescription", "description", "matches", "matchAll", "match", "placeholder", "placeholderInput", "agents", "Set", "inputs", "add", "Object", "entries", "map", "slice", "join", "at", "input", "isImageInput", "variableName", "trim", "startsWith", "addControl", "control", "required", "isInputValid", "valid", "startFakeProgress", "setInterval", "stopFakeProgress", "clearInterval", "setTimeout", "handleChatMessage", "message", "executeWorkflow", "field", "setValue", "promptForCurrentField", "section", "data", "timestamp", "downloadAsFile", "JSON", "stringify", "filename", "type", "blob", "Blob", "url", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "click", "revokeObjectURL", "action", "navigateBack", "editWorkflow", "logExecutionStatus", "delay", "workflowExecProcessing", "push", "content", "color", "getWorkflowLogs", "workflowLogConnect", "validate<PERSON><PERSON>", "output", "parsedOutput", "parse", "e", "payload", "FormData", "queryString", "running", "file", "append", "value", "getDaUsername", "pipeLineId", "userInputs", "user", "workflowResponse", "pipeline", "workflowExecComplete", "tasksOutputs", "task", "title", "expected_output", "summary", "raw", "completed", "detail", "workflowLogDisconnect", "workflowLogFailed", "handleAttachment", "onAttachmentsSelected", "files", "onImageSelected", "keys", "controls", "reader", "FileReader", "onload", "base64String", "result", "readAsDataURL", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "WorkflowService", "i3", "TokenStorageService", "LoaderService", "i4", "FormBuilder", "selectors", "viewQuery", "WorkflowExecutionComponent_Query", "rf", "ctx", "ɵɵelement", "WorkflowExecutionComponent_Template_app_chat_interface_messageSent_13_listener", "_r1", "WorkflowExecutionComponent_Template_app_chat_interface_attachmentClicked_13_listener", "WorkflowExecutionComponent_Template_app_chat_interface_attachmentSelected_13_listener", "WorkflowExecutionComponent_Template_ava_tabs_tabChange_19_listener", "ɵɵtemplate", "WorkflowExecutionComponent_div_22_Template", "WorkflowExecutionComponent_div_23_Template", "ɵɵpropertyInterpolate", "ɵɵpureFunction0", "_c0", "i5", "NgIf", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\workflows\\workflow-execution\\workflow-execution.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\pages\\workflows\\workflow-execution\\workflow-execution.component.html"], "sourcesContent": ["import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { FormBuilder, FormGroup, FormsModule, Validators } from '@angular/forms';\r\n\r\n// Import child components\r\nimport { ChatInterfaceComponent } from '@shared/components/chat-interface/chat-interface.component';\r\nimport { ChatMessage } from '@shared/components/chat-window/chat-window.component';\r\nimport {\r\n  AgentActivityComponent,\r\n} from './components/agent-activity/agent-activity.component';\r\nimport {\r\n  AgentOutputComponent,\r\n  AgentOutput as OutputItem,\r\n} from './components/agent-output/agent-output.component';\r\nimport {\r\n  ButtonComponent,\r\n  IconComponent,\r\n  TabItem,\r\n  TabsComponent,\r\n} from '@ava/play-comp-library';\r\nimport { WorkflowService } from '@shared/services/workflow.service';\r\nimport { environment } from '@shared/environments/environment';\r\nimport workflowConstants from './../constants/workflows.json';\r\nimport { TokenStorageService, LoaderService } from '@shared/index';\r\nimport { AvaTab } from '@shared/models/tab.model';\r\nimport { ExecutionStatus, ActivityLog } from '@shared/models/execution.model';\r\nimport { AgentActivityExecutionDetails } from './components/agent-activity/agent-activity.component';\r\n\r\n\r\n@Component({\r\n  selector: 'app-workflow-execution',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    ChatInterfaceComponent,\r\n    AgentActivityComponent,\r\n    AgentOutputComponent,\r\n    TabsComponent,\r\n    ButtonComponent,\r\n    IconComponent,\r\n  ],\r\n  templateUrl: './workflow-execution.component.html',\r\n  styleUrls: ['./workflow-execution.component.scss'],\r\n})\r\nexport class WorkflowExecutionComponent implements OnInit, OnDestroy {\r\n  navigationTabs: TabItem[] = [\r\n    { id: 'nav-home', label: 'Agent Activity' },\r\n    { id: 'nav-products', label: 'Agent Output' },\r\n    { id: 'nav-services', label: 'Preview', disabled: true },\r\n  ];\r\n  // Workflow details\r\n  workflowId: string | null = null;\r\n  workflowName: string = 'Workflow';\r\n\r\n  constants = workflowConstants as Record<string, any>;\r\n\r\n  @ViewChild(ChatInterfaceComponent, { static: false })\r\n  chatInterfaceComp!: ChatInterfaceComponent;\r\n\r\n  // Activity logs\r\n  activityLogs: ActivityLog[] = [];\r\n  activityProgress: number = 0;\r\n  executionDetails?: AgentActivityExecutionDetails;\r\n  isRunning: boolean = false;\r\n  status: ExecutionStatus = ExecutionStatus.notStarted;\r\n\r\n  // Chat messages\r\n  chatMessages: ChatMessage[] = [];\r\n  isProcessingChat: boolean = false;\r\n  inputText = '';\r\n\r\n  // Agent outputs\r\n  agentOutputs: OutputItem[] = [];\r\n  public workflowForm!: FormGroup;\r\n  public fileType : string = '.zip';\r\n\r\n  // Execution state\r\n  executionStartTime: Date | null = null;\r\n  executionCompleted: boolean = false;\r\n  executionId!: string;\r\n\r\n  public workflowLogs: any[] = [];\r\n  enableStreamingLog = environment.enableLogStreaming || 'all';\r\n\r\n  public isExecutionComplete: boolean = false;\r\n  progressInterval: any;\r\n\r\n  // Component lifecycle\r\n  private destroy$ = new Subject<void>();\r\n  selectedTab: string = 'Agent Activity';\r\n  demoTabs: AvaTab[] = [\r\n    { id: 'activity', label: 'Agent Activity' },\r\n    { id: 'agents', label: 'Agent Output' },\r\n    { id: 'preview', label: 'Preview', disabled: true },\r\n  ];\r\n  errorMsg = false;\r\n  resMessage: any;\r\n  taskMessage = [];\r\n  isJsonValid = false;\r\n  disableChat : boolean = false;\r\n  selectedFiles: File[] = [];\r\n  workflowAgents: any[] = [];\r\n  userInputList: any[] = [];\r\n  progress = 0;\r\n  isLoading = false;\r\n  loaderColor: string = '';\r\n\r\n  inputFieldOrder: string[] = [];\r\n  currentInputIndex: number = 0;\r\n  activeTabId: string = 'nav-home';\r\n  \r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private workflowService: WorkflowService,\r\n    private tokenStorage: TokenStorageService,\r\n    private loaderService: LoaderService,\r\n    private formBuilder: FormBuilder,\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loaderService.disableLoader();\r\n    this.selectedTab = 'Agent Activity';\r\n    this.executionId = crypto.randomUUID();\r\n    // Get workflow ID from route params\r\n    this.route.paramMap.pipe(takeUntil(this.destroy$)).subscribe((params) => {\r\n      this.workflowId = params.get('id');\r\n      if (this.workflowId) {\r\n        this.loadWorkflow(this.workflowId);\r\n      } else {\r\n        // No workflow ID, redirect back to workflows page\r\n        this.router.navigate(['/build/workflows']);\r\n      }\r\n    });\r\n    // this.executeWorkflow()\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n    this.loaderService.enableLoader();\r\n  }\r\n  onTabChange(event: { id: string, label: string }) {\r\n    this.selectedTab = event.label;\r\n    this.activeTabId = event.id;\r\n    console.log('Tab changed:', event);\r\n  }\r\n\r\n  // Load workflow data\r\n  loadWorkflow(id: string): void {\r\n    // In a real app, this would fetch the workflow from a service\r\n    console.log(`Loading workflow with ID: ${id}`);\r\n    this.chatMessages = [\r\n      {\r\n        from: 'ai',\r\n        text: 'I am your workflow assistant. I will help you in executing this workflow.',\r\n      } as ChatMessage,\r\n    ]\r\n    this.workflowForm = this.formBuilder.group({});\r\n    \r\n    this.workflowService.getWorkflowById(id).subscribe({\r\n        next: (res) => {\r\n        this.workflowAgents = res.workflowAgents;\r\n        this.userInputList = this.extractInputField(this.workflowAgents);\r\n        if(this.userInputList.length === 0){\r\n          this.disableChat = true;\r\n        }\r\n        this.workflowName = res.name;        \r\n        this.initializeForm();\r\n        this.startInputCollection();\r\n      },\r\n        error: (err) => {\r\n          this.disableChat = true;\r\n          this.chatInterfaceComp.addAiResponse('No input is required for this workflow. Click on send button to execute the workflow.');\r\n          console.log(err);\r\n        }\r\n    });\r\n\r\n  }\r\n\r\n  public extractInputField(pipeLineAgents: any) {\r\n    const PLACEHOLDER_PATTERNS =  /(%\\d+\\$s)|\\{\\{([a-zA-Z0-9-_]+)\\}\\}/g;\r\n    const placeholderMap: { [key: string]: { agents: Set<string>; inputs: Set<string> } } = {};\r\n\r\n    pipeLineAgents.forEach((agent: any) => {\r\n      const agentName = agent?.agentDetails?.name;\r\n      const agentDescription = agent?.agentDetails?.description;\r\n      const matches = agentDescription.matchAll(PLACEHOLDER_PATTERNS) || [];\r\n\r\n      for (const match of matches) { \r\n        const placeholder = match[1] || match[2];\r\n        const placeholderInput = match[0];\r\n        if (!placeholderMap[placeholder]) {\r\n          placeholderMap[placeholder] = { agents: new Set(), inputs: new Set() };;\r\n        }\r\n        placeholderMap[placeholder].agents.add(agentName);\r\n        placeholderMap[placeholder].inputs.add(placeholderInput);\r\n      }\r\n    })\r\n\r\n    return Object.entries(placeholderMap).map(([placeholder, { agents, inputs }]) => ({\r\n      name: [...agents].length > 2\r\n        ? `${[...agents].slice(0, -1).join(\", \")} and ${[...agents].at(-1)}`\r\n        : [...agents].join(\" and \"),\r\n      placeholder,\r\n      input: [...inputs][0],\r\n    }));\r\n  }\r\n\r\n  public isImageInput(input: string): boolean {\r\n    const match = input.match(/{{(.*?)}}/);\r\n    if (match && match[1]) {\r\n      const variableName = match[1].trim();\r\n      return variableName.startsWith('image') || variableName.startsWith('Image');\r\n    }\r\n    return false;\r\n  }\r\n\r\n  public initializeForm() {   \r\n    this.userInputList.forEach((label: any) => {\r\n      this.workflowForm.addControl(label.input, this.formBuilder.control('', Validators.required));\r\n    })\r\n  }\r\n\r\n  public isInputValid() {\r\n    return this.workflowForm.valid && this.workflowId;\r\n  }\r\n\r\n  startFakeProgress() {\r\n    this.progress = 0;\r\n    this.progressInterval = setInterval(() => {\r\n      if (this.progress < 90) {\r\n        this.progress += 5; // Increase slowly\r\n      }\r\n    }, 200); // Adjust speed\r\n  }\r\n\r\n  stopFakeProgress() {\r\n    clearInterval(this.progressInterval);\r\n    this.progress = 100;\r\n\r\n    setTimeout(() => {\r\n      this.isLoading = false;\r\n    }, 500); // Small delay to let user see 100%\r\n  }\r\n\r\n  // Handle new chat message from user\r\n  handleChatMessage(message: string): void {\r\n    // console.log('message ', message, 'is blank', message.trim() === '');\r\n    this.isProcessingChat = true;\r\n    if(message.trim() === ''){\r\n      if(this.inputFieldOrder.length === 0){\r\n        this.chatInterfaceComp.addAiResponse('Executing the workflow...');\r\n        this.executeWorkflow();\r\n      }\r\n      return;\r\n    }\r\n\r\n    if(this.isExecutionComplete || this.currentInputIndex===this.inputFieldOrder.length){\r\n        this.chatInterfaceComp.addAiResponse('Executing the workflow...');\r\n        this.executeWorkflow();\r\n      return;\r\n    }\r\n\r\n    const field = this.inputFieldOrder[this.currentInputIndex];\r\n    if (this.isImageInput(field)) {\r\n      // Ignore text input, wait for file input\r\n      this.chatInterfaceComp.addAiResponse(`Please upload an image file for ${field}`);\r\n      return;\r\n    }\r\n\r\n    this.workflowForm.get(field)?.setValue(message);\r\n    this.currentInputIndex++;\r\n\r\n    if (this.currentInputIndex < this.inputFieldOrder.length) {\r\n      this.promptForCurrentField();\r\n    } else {\r\n      this.chatInterfaceComp.addAiResponse('Thank you for the input! Executing the workflow...');\r\n      this.executeWorkflow();\r\n    }\r\n  }\r\n\r\n  // Save execution logs\r\n  saveLogs(): void {\r\n    console.log('Saving execution logs...');\r\n    // This would typically save to a service\r\n  }\r\n\r\n  // Export results\r\n  exportResults(section: 'activity' | 'output'): void {\r\n    console.log(`Exporting ${section} data...`);\r\n\r\n    if (section === 'activity') {\r\n      const data = this.activityLogs\r\n        .map((log) => `[${log.timestamp}] ${log.message}`)\r\n        .join('\\n');\r\n      this.downloadAsFile(data, 'workflow-activity-logs.txt', 'text/plain');\r\n    } else {\r\n      const data = JSON.stringify(this.agentOutputs, null, 2);\r\n      this.downloadAsFile(data, 'workflow-outputs.json', 'application/json');\r\n    }\r\n  }\r\n\r\n  // Helper method to download data as a file\r\n  private downloadAsFile(data: string, filename: string, type: string): void {\r\n    const blob = new Blob([data], { type });\r\n    const url = URL.createObjectURL(blob);\r\n    const link = document.createElement('a');\r\n    link.href = url;\r\n    link.download = filename;\r\n    link.click();\r\n    URL.revokeObjectURL(url);\r\n  }\r\n\r\n  // Handle controls for execution\r\n  handleControlAction(action: 'play' | 'pause' | 'stop'): void {\r\n    console.log(`Control action: ${action}`);\r\n    // In a real app, this would control the workflow execution\r\n\r\n    if (action === 'play') {\r\n      this.isRunning = true;\r\n    } else if (action === 'pause' || action === 'stop') {\r\n      this.isRunning = false;\r\n    }\r\n  }\r\n\r\n  // Navigate back to workflow listing\r\n  navigateBack(): void {\r\n    this.router.navigate(['/build/workflows']);\r\n  }\r\n\r\n  // Navigate to edit workflow\r\n  editWorkflow(): void {\r\n    if (this.workflowId) {\r\n      this.router.navigate(['/build/workflows/edit', this.workflowId]);\r\n    }\r\n  }\r\n\r\n  public logExecutionStatus(delay: number = 2000) {\r\n    setTimeout(() => {\r\n      if (!this.isExecutionComplete) {\r\n        console.log(this.constants);\r\n        console.log(this.constants['labels'].workflowExecProcessing);\r\n        this.workflowLogs.push({\r\n          content: this.constants['labels'].workflowExecProcessing,\r\n          color: '#F9DB24',\r\n        });\r\n      }\r\n    }, delay);\r\n  }\r\n\r\n  // public parseAnsiString(ansiString: string) {\r\n  //   const regex = ansiRegex();\r\n  //   const parts = ansiString.split(regex);\r\n  //   const matches = [...ansiString.matchAll(regex)];\r\n  //   parts.forEach((part, index) => {\r\n  //     if (part.trim() !== '') {\r\n  //       let colorCode = matches[index - 1][0];\r\n  //       if (index - 2 >= 0 && matches[index - 2]?.includes('\\u001b[1m')) {\r\n  //         colorCode = `\\u001b[1m${colorCode}`;\r\n  //       }\r\n  //       this.workflowLogs.push({\r\n  //         content: part,\r\n  //         color: this.colorMap[colorCode] || 'white',\r\n  //       });\r\n  //     }\r\n  //   });\r\n  // }\r\n\r\n  public getWorkflowLogs(executionId: string) {\r\n    this.workflowService\r\n      .workflowLogConnect(executionId)\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (message) => {\r\n          console.log('message: ', message);\r\n          const { content, color } = message;\r\n          if (color) {\r\n            this.workflowLogs.push({ content, color });\r\n          } else if (this.enableStreamingLog === 'all') {\r\n            // this.parseAnsiString(content);\r\n          }\r\n        },\r\n        error: (err) => {\r\n          this.workflowLogs.push({\r\n            content: this.constants['workflowLog'],\r\n            color: 'red',\r\n          });\r\n          console.error('WebSocket error:', err);\r\n        },\r\n        complete: () => {\r\n          this.logExecutionStatus();\r\n          console.log('WebSocket connection closed');\r\n        },\r\n      });\r\n  }\r\n\r\n  // public parseAnsiString(ansiString: string) {\r\n  //   const regex = ansiRegex();\r\n  //   const parts = ansiString.split(regex);\r\n  //   const matches = [...ansiString.matchAll(regex)];\r\n  //   parts.forEach((part, index) => {\r\n  //     if (part.trim() !== '') {\r\n  //       let colorCode = matches[index-1][0];\r\n  //       if(index - 2 >= 0 && matches[index-2]?.includes('\\u001b[1m')) {\r\n  //         colorCode = `\\u001b[1m${colorCode}`;\r\n  //       }\r\n  //       this.workflowLogs.push({\r\n  //         content: part, \r\n  //         color: this.colorMap[colorCode] || 'white', \r\n  //       });\r\n  //     }\r\n  //   });\r\n  // }\r\n\r\n  public validateJson(output: string): any | null {\r\n    this.isJsonValid = false;\r\n    try {\r\n      const parsedOutput = JSON.parse(output);\r\n      this.isJsonValid = true;\r\n      return parsedOutput;\r\n    } catch (e) {\r\n      return null;\r\n    }\r\n  }\r\n\r\n  public executeWorkflow() {\r\n    let payload: FormData | Record<string, any> = new FormData();\r\n    let queryString = '';\r\n\r\n\r\n    this.status = ExecutionStatus.running;\r\n    if (this.selectedFiles.length) {\r\n      this.selectedFiles.forEach((file) => {\r\n        payload.append('files', file);\r\n      });\r\n      payload.append('workflowId', this.workflowId);\r\n      payload.append('userInputs', JSON.stringify(this.workflowForm.value));\r\n      payload.append('user', this.tokenStorage.getDaUsername());\r\n      payload.append('executionId', this.executionId);\r\n      queryString = '/files';\r\n    } else {\r\n      payload = {\r\n        pipeLineId: this.workflowId,\r\n        userInputs: this.workflowForm.value,\r\n        executionId: this.executionId,\r\n        user: this.tokenStorage.getDaUsername(),\r\n      };\r\n    }\r\n\r\n    this.getWorkflowLogs(this.executionId);\r\n    this.startFakeProgress();\r\n\r\n    this.workflowService\r\n      .executeWorkflow(payload, queryString)\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (res) => {\r\n          this.isProcessingChat = false;\r\n          this.isRunning = false;\r\n          this.chatInterfaceComp.addAiResponse(res?.message || \"Workflow execution completed successfully!\");\r\n\r\n          if (res?.workflowResponse?.pipeline?.output) {\r\n            this.isExecutionComplete = true;\r\n            // console.log(this.constants['labels'].workflowExecComplete);\r\n            this.workflowLogs.push({\r\n              content: this.constants['labels'].workflowExecComplete,\r\n              color: '#0F8251',\r\n            });\r\n            this.errorMsg = false;\r\n            this.resMessage = res?.workflowResponse?.pipeline?.output;\r\n            this.agentOutputs = res?.workflowResponse?.pipeline?.tasksOutputs.map((task: any) => {\r\n              return {\r\n                id: task?.id || '',\r\n                title: task?.title || '',\r\n                content: task?.content || '',\r\n                agentName: task?.agentName || '',\r\n                timestamp: task?.timestamp || '',\r\n                type: task?.type || '',\r\n                description: task?.description || '',\r\n                expected_output: task?.expected_output || '',\r\n                summary: task?.summary || '',\r\n                raw: task?.raw || '',\r\n              };\r\n            })\r\n\r\n            this.taskMessage = res?.workflowResponse?.pipeline?.tasksOutputs.map(\r\n              (task: {\r\n                description: any;\r\n                summary: any;\r\n                raw: any;\r\n                expected_output: any;\r\n              }) => {\r\n                return {\r\n                  description: task.description,\r\n                  summary: task.summary,\r\n                  raw: task.raw,\r\n                  expected_output: task.expected_output,\r\n                };\r\n              },\r\n            );\r\n\r\n            // if(\"file_download_url\" in res?.pipeline){\r\n            //   this.isFileWriter = true;\r\n            //   this.fileDownloadLink = res?.pipeline?.file_download_url;\r\n\r\n            //   if(!this.fileDownloadLink){\r\n            //     this.fileDownloadUrlError = [];\r\n            //     this.fileDownloadUrlError.push(\"Output file is not generated yet!\")\r\n            //   }\r\n            // }\r\n            // this.isAccordian = true\r\n          }\r\n          this.validateJson(this.resMessage);\r\n          this.status = ExecutionStatus.completed;\r\n          this.stopFakeProgress();\r\n          this.selectedFiles = [];\r\n        },\r\n        error: (error) => {\r\n          this.isExecutionComplete = true;\r\n          this.isProcessingChat = false;\r\n          this.errorMsg = true;\r\n          this.resMessage = error?.error?.detail;\r\n          this.workflowService.workflowLogDisconnect();\r\n          this.workflowLogs.push({\r\n            content: this.constants['labels'].workflowLogFailed,\r\n            color: 'red',\r\n          });\r\n          this.chatInterfaceComp.addAiResponse(\r\n            'Something went wrong, Workflow execution has failed.',\r\n          );\r\n          this.selectedFiles = [];\r\n          this.stopFakeProgress();\r\n          console.log('error is', error.message);\r\n        },\r\n      });\r\n  }\r\n\r\n  // public asyncExecutePipeline() {\r\n  //   const payload: FormData = new FormData();\r\n  //   if (this.selectedFiles?.length) {\r\n  //     for (const element of this.selectedFiles) {\r\n  //       payload.append('files', element)\r\n  //     }\r\n  //   }\r\n  //   payload.append('pipeLineId', String(this.workflowId));\r\n  //   payload.append('userInputs', JSON.stringify(this.workflowForm.value));\r\n  //   payload.append('user', this.tokenStorage.getDaUsername() || '');\r\n  //   payload.append('executionId', this.executionId);\r\n\r\n  //   this.workflowService.asyncExecutePipeline(payload).pipe().subscribe({\r\n  //     next: (res: any) => {\r\n  //       if(res) {\r\n  //         // res handling\r\n  //         console.log(res);\r\n  //       }\r\n  //     },\r\n  //     error: e => {\r\n  //       // error handling\r\n  //       console.log(e);\r\n  //     }\r\n  //   })\r\n  // }\r\n\r\n  handleAttachment() {\r\n    console.log('handleAttachment');\r\n  }\r\n\r\n  onAttachmentsSelected(files: File[]) {\r\n    if(this.currentInputIndex===this.inputFieldOrder.length || this.inputFieldOrder.length===0){\r\n      this.selectedFiles = files;\r\n      return;\r\n    }\r\n\r\n    const field = this.inputFieldOrder[this.currentInputIndex];\r\n    if(this.isImageInput(field)){\r\n      if (files && files.length > 0) {\r\n        this.onImageSelected(files[0]);\r\n      }\r\n    } else {\r\n      this.selectedFiles = files;\r\n    }\r\n  }\r\n\r\n  startInputCollection(){\r\n    this.inputFieldOrder = Object.keys(this.workflowForm.controls);\r\n    this.currentInputIndex = 0;\r\n    if (this.inputFieldOrder.length > 0) {\r\n      this.promptForCurrentField();\r\n    }\r\n    else{\r\n      this.disableChat = true;\r\n      this.chatInterfaceComp.addAiResponse('No input is required for this workflow. Click on send button to execute the workflow.');\r\n    }\r\n  }\r\n\r\n  promptForCurrentField() {\r\n    const field = this.inputFieldOrder[this.currentInputIndex];\r\n    if (this.isImageInput(field)) {\r\n      this.fileType = '.jpeg,.png,.jpg,.svg';\r\n      this.chatInterfaceComp.addAiResponse(`Please upload an image for ${field}`);\r\n      // UI should now show a file input for the user\r\n    } else {\r\n      this.fileType = '.zip'; // or whatever default you want for non-image\r\n      this.chatInterfaceComp.addAiResponse(`Please enter the value of ${field}`);\r\n    }\r\n  }\r\n\r\n  onImageSelected(file: File) {\r\n    const field = this.inputFieldOrder[this.currentInputIndex];\r\n    if (!this.isImageInput(field)) return;\r\n\r\n    const reader = new FileReader();\r\n    reader.onload = () => {\r\n      const base64String = (reader.result as string); \r\n      this.workflowForm.get(field)?.setValue(base64String);\r\n      this.currentInputIndex++;\r\n      if (this.currentInputIndex < this.inputFieldOrder.length) {\r\n        this.promptForCurrentField();\r\n      } else {\r\n        this.chatInterfaceComp.addAiResponse('Thank you! Running the workflow...');\r\n        this.executeWorkflow();\r\n      }\r\n    };\r\n    reader.readAsDataURL(file);\r\n  }\r\n}\r\n", "<div class=\"workflow-execution-container\">\r\n  <!-- SVG Gradient Definitions for Icons -->\r\n  <svg width=\"0\" height=\"0\" style=\"position: absolute\">\r\n    <defs>\r\n      <linearGradient id=\"gradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\r\n        <stop offset=\"0%\" stop-color=\"#6566CD\" />\r\n        <stop offset=\"100%\" stop-color=\"#F96CAB\" />\r\n      </linearGradient>\r\n    </defs>\r\n  </svg>\r\n  <div class=\"execution-content\" role=\"main\">\r\n    <div\r\n      class=\"column playground-column\"\r\n      role=\"region\"\r\n      aria-label=\"Workflow Playground\"\r\n    >\r\n      <div class=\"column-content playground-content\">\r\n        <div class=\"column-header\">\r\n          <h2 id=\"playground-title\">\r\n            <ava-icon iconName=\"panelLeft\" iconColor=\"#1A46A7\"></ava-icon>\r\n          </h2>\r\n        </div>\r\n        <div class=\"chat-container\" aria-labelledby=\"playground-title\">\r\n          <!-- Chat Interface Component -->\r\n          <app-chat-interface\r\n            #chatInterfaceComp\r\n            [messages]=\"chatMessages\"\r\n            [isLoading]=\"isProcessingChat\"\r\n            [isDisabled]=\"disableChat\"\r\n            (messageSent)=\"handleChatMessage($event)\"\r\n            (attachmentClicked)=\"handleAttachment()\"\r\n            [inputText]=\"inputText\"\r\n            [isOptionalInput]=\"true\"\r\n            [handleFileUpload]=\"true\"\r\n            fileType=\"{{fileType}}\"\r\n            [maxFileSize]=\"10485760\"\r\n            (attachmentSelected)=\"onAttachmentsSelected($event)\"\r\n          ></app-chat-interface>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Right Column: Agent Output -->\r\n    <div class=\"column output-column\" role=\"region\" aria-label=\"Agent Output\">\r\n      <div class=\"column-content\">\r\n        <div class=\"column-header row\">\r\n          <div class=\"col-7\">\r\n            <ava-tabs\r\n              [tabs]=\"navigationTabs\"\r\n              [activeTabId]=\"activeTabId\"\r\n              variant=\"button\"\r\n              buttonShape=\"pill\"\r\n              [showContentPanels]=\"false\"\r\n              (tabChange)=\"onTabChange($event)\"\r\n              ariaLabel=\"Pill navigation tabs\"\r\n            ></ava-tabs>\r\n          </div>\r\n          <div class=\"col-5 right-section-header\">\r\n            <ava-button\r\n              label=\"Send for Approval\"\r\n              variant=\"primary\"\r\n              [customStyles]=\"{\r\n                background:\r\n                  'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n                '--button-effect-color': '33, 90, 214',\r\n                'border-radius': '8px',\r\n                'box-shadow': 'none',\r\n              }\"\r\n              size=\"medium\"\r\n            >\r\n            </ava-button>\r\n            <!-- <ava-button\r\n              label=\"Export\"\r\n              variant=\"secondary\"\r\n              size=\"medium\"\r\n              class=\"ms-2\"\r\n            >\r\n            </ava-button> -->\r\n          </div>\r\n        </div>\r\n        <!-- activity content -->\r\n        <div *ngIf=\"selectedTab === 'Agent Activity'\" style=\"height: 100%\">\r\n          <app-agent-activity\r\n            [activityLogs]=\"workflowLogs\"\r\n            [executionDetails]=\"executionDetails\"\r\n            [progress]=\"progress\"\r\n            [isRunning]=\"isRunning\"\r\n            (saveLogs)=\"saveLogs()\"\r\n            (controlAction)=\"handleControlAction($event)\"\r\n            [status]=\"status\"\r\n            (onOutPutBtnClick)=\"onTabChange({id: 'nav-products', label: 'Agent Output'})\"\r\n          ></app-agent-activity>\r\n        </div>\r\n        <!-- Agent Output Component -->\r\n        <div *ngIf=\"selectedTab === 'Agent Output'\" style=\"height: 100%\">\r\n          <app-agent-output\r\n            [outputs]=\"taskMessage\"\r\n            (export)=\"exportResults('output')\"\r\n          ></app-agent-output>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;AACzC,SAAiCC,WAAW,EAAEC,UAAU,QAAQ,gBAAgB;AAEhF;AACA,SAASC,sBAAsB,QAAQ,4DAA4D;AAEnG,SACEC,sBAAsB,QACjB,sDAAsD;AAC7D,SACEC,oBAAoB,QAEf,kDAAkD;AACzD,SACEC,eAAe,EACfC,aAAa,EAEbC,aAAa,QACR,wBAAwB;AAE/B,SAASC,WAAW,QAAQ,kCAAkC;AAC9D,OAAOC,iBAAiB,MAAM,+BAA+B;AAG7D,SAASC,eAAe,QAAqB,gCAAgC;;;;;;;;;;;;;;;;ICuDnEC,EADF,CAAAC,cAAA,cAAmE,6BAUhE;IADCD,EAHA,CAAAE,UAAA,sBAAAC,kFAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAYF,MAAA,CAAAG,QAAA,EAAU;IAAA,EAAC,2BAAAC,uFAAAC,MAAA;MAAAX,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CACNF,MAAA,CAAAM,mBAAA,CAAAD,MAAA,CAA2B;IAAA,EAAC,8BAAAE,0FAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAEzBF,MAAA,CAAAQ,WAAA,CAAY;QAAAC,EAAA,EAAK,cAAc;QAAAC,KAAA,EAAS;MAAc,CAAC,CAAC;IAAA,EAAC;IAEjFhB,EADG,CAAAiB,YAAA,EAAqB,EAClB;;;;IATFjB,EAAA,CAAAkB,SAAA,EAA6B;IAM7BlB,EANA,CAAAmB,UAAA,iBAAAb,MAAA,CAAAc,YAAA,CAA6B,qBAAAd,MAAA,CAAAe,gBAAA,CACQ,aAAAf,MAAA,CAAAgB,QAAA,CAChB,cAAAhB,MAAA,CAAAiB,SAAA,CACE,WAAAjB,MAAA,CAAAkB,MAAA,CAGN;;;;;;IAMnBxB,EADF,CAAAC,cAAA,cAAiE,2BAI9D;IADCD,EAAA,CAAAE,UAAA,oBAAAuB,8EAAA;MAAAzB,EAAA,CAAAI,aAAA,CAAAsB,GAAA;MAAA,MAAApB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAUF,MAAA,CAAAqB,aAAA,CAAc,QAAQ,CAAC;IAAA,EAAC;IAEtC3B,EADG,CAAAiB,YAAA,EAAmB,EAChB;;;;IAHFjB,EAAA,CAAAkB,SAAA,EAAuB;IAAvBlB,EAAA,CAAAmB,UAAA,YAAAb,MAAA,CAAAsB,WAAA,CAAuB;;;ADjDnC,WAAaC,0BAA0B;EAAjC,MAAOA,0BAA0B;IAqE3BC,KAAA;IACAC,MAAA;IACAC,eAAA;IACAC,YAAA;IACAC,aAAA;IACAC,WAAA;IAzEVC,cAAc,GAAc,CAC1B;MAAErB,EAAE,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAgB,CAAE,EAC3C;MAAED,EAAE,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAc,CAAE,EAC7C;MAAED,EAAE,EAAE,cAAc;MAAEC,KAAK,EAAE,SAAS;MAAEqB,QAAQ,EAAE;IAAI,CAAE,CACzD;IACD;IACAC,UAAU,GAAkB,IAAI;IAChCC,YAAY,GAAW,UAAU;IAEjCC,SAAS,GAAG1C,iBAAwC;IAGpD2C,iBAAiB;IAEjB;IACAC,YAAY,GAAkB,EAAE;IAChCC,gBAAgB,GAAW,CAAC;IAC5BtB,gBAAgB;IAChBE,SAAS,GAAY,KAAK;IAC1BC,MAAM,GAAoBzB,eAAe,CAAC6C,UAAU;IAEpD;IACAC,YAAY,GAAkB,EAAE;IAChCC,gBAAgB,GAAY,KAAK;IACjCC,SAAS,GAAG,EAAE;IAEd;IACAC,YAAY,GAAiB,EAAE;IACxBC,YAAY;IACZC,QAAQ,GAAY,MAAM;IAEjC;IACAC,kBAAkB,GAAgB,IAAI;IACtCC,kBAAkB,GAAY,KAAK;IACnCC,WAAW;IAEJjC,YAAY,GAAU,EAAE;IAC/BkC,kBAAkB,GAAGzD,WAAW,CAAC0D,kBAAkB,IAAI,KAAK;IAErDC,mBAAmB,GAAY,KAAK;IAC3CC,gBAAgB;IAEhB;IACQC,QAAQ,GAAG,IAAIvE,OAAO,EAAQ;IACtCwE,WAAW,GAAW,gBAAgB;IACtCC,QAAQ,GAAa,CACnB;MAAE7C,EAAE,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAgB,CAAE,EAC3C;MAAED,EAAE,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAc,CAAE,EACvC;MAAED,EAAE,EAAE,SAAS;MAAEC,KAAK,EAAE,SAAS;MAAEqB,QAAQ,EAAE;IAAI,CAAE,CACpD;IACDwB,QAAQ,GAAG,KAAK;IAChBC,UAAU;IACVlC,WAAW,GAAG,EAAE;IAChBmC,WAAW,GAAG,KAAK;IACnBC,WAAW,GAAa,KAAK;IAC7BC,aAAa,GAAW,EAAE;IAC1BC,cAAc,GAAU,EAAE;IAC1BC,aAAa,GAAU,EAAE;IACzB7C,QAAQ,GAAG,CAAC;IACZ8C,SAAS,GAAG,KAAK;IACjBC,WAAW,GAAW,EAAE;IAExBC,eAAe,GAAa,EAAE;IAC9BC,iBAAiB,GAAW,CAAC;IAC7BC,WAAW,GAAW,UAAU;IAGhCC,YACU3C,KAAqB,EACrBC,MAAc,EACdC,eAAgC,EAChCC,YAAiC,EACjCC,aAA4B,EAC5BC,WAAwB;MALxB,KAAAL,KAAK,GAALA,KAAK;MACL,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,eAAe,GAAfA,eAAe;MACf,KAAAC,YAAY,GAAZA,YAAY;MACZ,KAAAC,aAAa,GAAbA,aAAa;MACb,KAAAC,WAAW,GAAXA,WAAW;IAClB;IAEHuC,QAAQA,CAAA;MACN,IAAI,CAACxC,aAAa,CAACyC,aAAa,EAAE;MAClC,IAAI,CAAChB,WAAW,GAAG,gBAAgB;MACnC,IAAI,CAACN,WAAW,GAAGuB,MAAM,CAACC,UAAU,EAAE;MACtC;MACA,IAAI,CAAC/C,KAAK,CAACgD,QAAQ,CAACC,IAAI,CAAC3F,SAAS,CAAC,IAAI,CAACsE,QAAQ,CAAC,CAAC,CAACsB,SAAS,CAAEC,MAAM,IAAI;QACtE,IAAI,CAAC3C,UAAU,GAAG2C,MAAM,CAACC,GAAG,CAAC,IAAI,CAAC;QAClC,IAAI,IAAI,CAAC5C,UAAU,EAAE;UACnB,IAAI,CAAC6C,YAAY,CAAC,IAAI,CAAC7C,UAAU,CAAC;QACpC,CAAC,MAAM;UACL;UACA,IAAI,CAACP,MAAM,CAACqD,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;QAC5C;MACF,CAAC,CAAC;MACF;IACF;IAEAC,WAAWA,CAAA;MACT,IAAI,CAAC3B,QAAQ,CAAC4B,IAAI,EAAE;MACpB,IAAI,CAAC5B,QAAQ,CAAC6B,QAAQ,EAAE;MACxB,IAAI,CAACrD,aAAa,CAACsD,YAAY,EAAE;IACnC;IACA1E,WAAWA,CAAC2E,KAAoC;MAC9C,IAAI,CAAC9B,WAAW,GAAG8B,KAAK,CAACzE,KAAK;MAC9B,IAAI,CAACwD,WAAW,GAAGiB,KAAK,CAAC1E,EAAE;MAC3B2E,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEF,KAAK,CAAC;IACpC;IAEA;IACAN,YAAYA,CAACpE,EAAU;MACrB;MACA2E,OAAO,CAACC,GAAG,CAAC,6BAA6B5E,EAAE,EAAE,CAAC;MAC9C,IAAI,CAAC8B,YAAY,GAAG,CAClB;QACE+C,IAAI,EAAE,IAAI;QACVC,IAAI,EAAE;OACQ,CACjB;MACD,IAAI,CAAC5C,YAAY,GAAG,IAAI,CAACd,WAAW,CAAC2D,KAAK,CAAC,EAAE,CAAC;MAE9C,IAAI,CAAC9D,eAAe,CAAC+D,eAAe,CAAChF,EAAE,CAAC,CAACiE,SAAS,CAAC;QAC/CM,IAAI,EAAGU,GAAG,IAAI;UACd,IAAI,CAAC9B,cAAc,GAAG8B,GAAG,CAAC9B,cAAc;UACxC,IAAI,CAACC,aAAa,GAAG,IAAI,CAAC8B,iBAAiB,CAAC,IAAI,CAAC/B,cAAc,CAAC;UAChE,IAAG,IAAI,CAACC,aAAa,CAAC+B,MAAM,KAAK,CAAC,EAAC;YACjC,IAAI,CAAClC,WAAW,GAAG,IAAI;UACzB;UACA,IAAI,CAACzB,YAAY,GAAGyD,GAAG,CAACG,IAAI;UAC5B,IAAI,CAACC,cAAc,EAAE;UACrB,IAAI,CAACC,oBAAoB,EAAE;QAC7B,CAAC;QACCC,KAAK,EAAGC,GAAG,IAAI;UACb,IAAI,CAACvC,WAAW,GAAG,IAAI;UACvB,IAAI,CAACvB,iBAAiB,CAAC+D,aAAa,CAAC,uFAAuF,CAAC;UAC7Hd,OAAO,CAACC,GAAG,CAACY,GAAG,CAAC;QAClB;OACH,CAAC;IAEJ;IAEON,iBAAiBA,CAACQ,cAAmB;MAC1C,MAAMC,oBAAoB,GAAI,qCAAqC;MACnE,MAAMC,cAAc,GAAoE,EAAE;MAE1FF,cAAc,CAACG,OAAO,CAAEC,KAAU,IAAI;QACpC,MAAMC,SAAS,GAAGD,KAAK,EAAEE,YAAY,EAAEZ,IAAI;QAC3C,MAAMa,gBAAgB,GAAGH,KAAK,EAAEE,YAAY,EAAEE,WAAW;QACzD,MAAMC,OAAO,GAAGF,gBAAgB,CAACG,QAAQ,CAACT,oBAAoB,CAAC,IAAI,EAAE;QAErE,KAAK,MAAMU,KAAK,IAAIF,OAAO,EAAE;UAC3B,MAAMG,WAAW,GAAGD,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC;UACxC,MAAME,gBAAgB,GAAGF,KAAK,CAAC,CAAC,CAAC;UACjC,IAAI,CAACT,cAAc,CAACU,WAAW,CAAC,EAAE;YAChCV,cAAc,CAACU,WAAW,CAAC,GAAG;cAAEE,MAAM,EAAE,IAAIC,GAAG,EAAE;cAAEC,MAAM,EAAE,IAAID,GAAG;YAAE,CAAE;YAAC;UACzE;UACAb,cAAc,CAACU,WAAW,CAAC,CAACE,MAAM,CAACG,GAAG,CAACZ,SAAS,CAAC;UACjDH,cAAc,CAACU,WAAW,CAAC,CAACI,MAAM,CAACC,GAAG,CAACJ,gBAAgB,CAAC;QAC1D;MACF,CAAC,CAAC;MAEF,OAAOK,MAAM,CAACC,OAAO,CAACjB,cAAc,CAAC,CAACkB,GAAG,CAAC,CAAC,CAACR,WAAW,EAAE;QAAEE,MAAM;QAAEE;MAAM,CAAE,CAAC,MAAM;QAChFtB,IAAI,EAAE,CAAC,GAAGoB,MAAM,CAAC,CAACrB,MAAM,GAAG,CAAC,GACxB,GAAG,CAAC,GAAGqB,MAAM,CAAC,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAGR,MAAM,CAAC,CAACS,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,GAClE,CAAC,GAAGT,MAAM,CAAC,CAACQ,IAAI,CAAC,OAAO,CAAC;QAC7BV,WAAW;QACXY,KAAK,EAAE,CAAC,GAAGR,MAAM,CAAC,CAAC,CAAC;OACrB,CAAC,CAAC;IACL;IAEOS,YAAYA,CAACD,KAAa;MAC/B,MAAMb,KAAK,GAAGa,KAAK,CAACb,KAAK,CAAC,WAAW,CAAC;MACtC,IAAIA,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,EAAE;QACrB,MAAMe,YAAY,GAAGf,KAAK,CAAC,CAAC,CAAC,CAACgB,IAAI,EAAE;QACpC,OAAOD,YAAY,CAACE,UAAU,CAAC,OAAO,CAAC,IAAIF,YAAY,CAACE,UAAU,CAAC,OAAO,CAAC;MAC7E;MACA,OAAO,KAAK;IACd;IAEOjC,cAAcA,CAAA;MACnB,IAAI,CAACjC,aAAa,CAACyC,OAAO,CAAE5F,KAAU,IAAI;QACxC,IAAI,CAACiC,YAAY,CAACqF,UAAU,CAACtH,KAAK,CAACiH,KAAK,EAAE,IAAI,CAAC9F,WAAW,CAACoG,OAAO,CAAC,EAAE,EAAEjJ,UAAU,CAACkJ,QAAQ,CAAC,CAAC;MAC9F,CAAC,CAAC;IACJ;IAEOC,YAAYA,CAAA;MACjB,OAAO,IAAI,CAACxF,YAAY,CAACyF,KAAK,IAAI,IAAI,CAACpG,UAAU;IACnD;IAEAqG,iBAAiBA,CAAA;MACf,IAAI,CAACrH,QAAQ,GAAG,CAAC;MACjB,IAAI,CAACmC,gBAAgB,GAAGmF,WAAW,CAAC,MAAK;QACvC,IAAI,IAAI,CAACtH,QAAQ,GAAG,EAAE,EAAE;UACtB,IAAI,CAACA,QAAQ,IAAI,CAAC,CAAC,CAAC;QACtB;MACF,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACX;IAEAuH,gBAAgBA,CAAA;MACdC,aAAa,CAAC,IAAI,CAACrF,gBAAgB,CAAC;MACpC,IAAI,CAACnC,QAAQ,GAAG,GAAG;MAEnByH,UAAU,CAAC,MAAK;QACd,IAAI,CAAC3E,SAAS,GAAG,KAAK;MACxB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACX;IAEA;IACA4E,iBAAiBA,CAACC,OAAe;MAC/B;MACA,IAAI,CAACnG,gBAAgB,GAAG,IAAI;MAC5B,IAAGmG,OAAO,CAACb,IAAI,EAAE,KAAK,EAAE,EAAC;QACvB,IAAG,IAAI,CAAC9D,eAAe,CAAC4B,MAAM,KAAK,CAAC,EAAC;UACnC,IAAI,CAACzD,iBAAiB,CAAC+D,aAAa,CAAC,2BAA2B,CAAC;UACjE,IAAI,CAAC0C,eAAe,EAAE;QACxB;QACA;MACF;MAEA,IAAG,IAAI,CAAC1F,mBAAmB,IAAI,IAAI,CAACe,iBAAiB,KAAG,IAAI,CAACD,eAAe,CAAC4B,MAAM,EAAC;QAChF,IAAI,CAACzD,iBAAiB,CAAC+D,aAAa,CAAC,2BAA2B,CAAC;QACjE,IAAI,CAAC0C,eAAe,EAAE;QACxB;MACF;MAEA,MAAMC,KAAK,GAAG,IAAI,CAAC7E,eAAe,CAAC,IAAI,CAACC,iBAAiB,CAAC;MAC1D,IAAI,IAAI,CAAC2D,YAAY,CAACiB,KAAK,CAAC,EAAE;QAC5B;QACA,IAAI,CAAC1G,iBAAiB,CAAC+D,aAAa,CAAC,mCAAmC2C,KAAK,EAAE,CAAC;QAChF;MACF;MAEA,IAAI,CAAClG,YAAY,CAACiC,GAAG,CAACiE,KAAK,CAAC,EAAEC,QAAQ,CAACH,OAAO,CAAC;MAC/C,IAAI,CAAC1E,iBAAiB,EAAE;MAExB,IAAI,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAACD,eAAe,CAAC4B,MAAM,EAAE;QACxD,IAAI,CAACmD,qBAAqB,EAAE;MAC9B,CAAC,MAAM;QACL,IAAI,CAAC5G,iBAAiB,CAAC+D,aAAa,CAAC,oDAAoD,CAAC;QAC1F,IAAI,CAAC0C,eAAe,EAAE;MACxB;IACF;IAEA;IACAzI,QAAQA,CAAA;MACNiF,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACvC;IACF;IAEA;IACAhE,aAAaA,CAAC2H,OAA8B;MAC1C5D,OAAO,CAACC,GAAG,CAAC,aAAa2D,OAAO,UAAU,CAAC;MAE3C,IAAIA,OAAO,KAAK,UAAU,EAAE;QAC1B,MAAMC,IAAI,GAAG,IAAI,CAAC7G,YAAY,CAC3BmF,GAAG,CAAElC,GAAG,IAAK,IAAIA,GAAG,CAAC6D,SAAS,KAAK7D,GAAG,CAACsD,OAAO,EAAE,CAAC,CACjDlB,IAAI,CAAC,IAAI,CAAC;QACb,IAAI,CAAC0B,cAAc,CAACF,IAAI,EAAE,4BAA4B,EAAE,YAAY,CAAC;MACvE,CAAC,MAAM;QACL,MAAMA,IAAI,GAAGG,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC3G,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC;QACvD,IAAI,CAACyG,cAAc,CAACF,IAAI,EAAE,uBAAuB,EAAE,kBAAkB,CAAC;MACxE;IACF;IAEA;IACQE,cAAcA,CAACF,IAAY,EAAEK,QAAgB,EAAEC,IAAY;MACjE,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACR,IAAI,CAAC,EAAE;QAAEM;MAAI,CAAE,CAAC;MACvC,MAAMG,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;MACrC,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGN,GAAG;MACfG,IAAI,CAACI,QAAQ,GAAGX,QAAQ;MACxBO,IAAI,CAACK,KAAK,EAAE;MACZP,GAAG,CAACQ,eAAe,CAACT,GAAG,CAAC;IAC1B;IAEA;IACApJ,mBAAmBA,CAAC8J,MAAiC;MACnDhF,OAAO,CAACC,GAAG,CAAC,mBAAmB+E,MAAM,EAAE,CAAC;MACxC;MAEA,IAAIA,MAAM,KAAK,MAAM,EAAE;QACrB,IAAI,CAACnJ,SAAS,GAAG,IAAI;MACvB,CAAC,MAAM,IAAImJ,MAAM,KAAK,OAAO,IAAIA,MAAM,KAAK,MAAM,EAAE;QAClD,IAAI,CAACnJ,SAAS,GAAG,KAAK;MACxB;IACF;IAEA;IACAoJ,YAAYA,CAAA;MACV,IAAI,CAAC5I,MAAM,CAACqD,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;IAC5C;IAEA;IACAwF,YAAYA,CAAA;MACV,IAAI,IAAI,CAACtI,UAAU,EAAE;QACnB,IAAI,CAACP,MAAM,CAACqD,QAAQ,CAAC,CAAC,uBAAuB,EAAE,IAAI,CAAC9C,UAAU,CAAC,CAAC;MAClE;IACF;IAEOuI,kBAAkBA,CAACC,KAAA,GAAgB,IAAI;MAC5C/B,UAAU,CAAC,MAAK;QACd,IAAI,CAAC,IAAI,CAACvF,mBAAmB,EAAE;UAC7BkC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACnD,SAAS,CAAC;UAC3BkD,OAAO,CAACC,GAAG,CAAC,IAAI,CAACnD,SAAS,CAAC,QAAQ,CAAC,CAACuI,sBAAsB,CAAC;UAC5D,IAAI,CAAC3J,YAAY,CAAC4J,IAAI,CAAC;YACrBC,OAAO,EAAE,IAAI,CAACzI,SAAS,CAAC,QAAQ,CAAC,CAACuI,sBAAsB;YACxDG,KAAK,EAAE;WACR,CAAC;QACJ;MACF,CAAC,EAAEJ,KAAK,CAAC;IACX;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEOK,eAAeA,CAAC9H,WAAmB;MACxC,IAAI,CAACrB,eAAe,CACjBoJ,kBAAkB,CAAC/H,WAAW,CAAC,CAC/B0B,IAAI,CAAC3F,SAAS,CAAC,IAAI,CAACsE,QAAQ,CAAC,CAAC,CAC9BsB,SAAS,CAAC;QACTM,IAAI,EAAG2D,OAAO,IAAI;UAChBvD,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEsD,OAAO,CAAC;UACjC,MAAM;YAAEgC,OAAO;YAAEC;UAAK,CAAE,GAAGjC,OAAO;UAClC,IAAIiC,KAAK,EAAE;YACT,IAAI,CAAC9J,YAAY,CAAC4J,IAAI,CAAC;cAAEC,OAAO;cAAEC;YAAK,CAAE,CAAC;UAC5C,CAAC,MAAM,IAAI,IAAI,CAAC5H,kBAAkB,KAAK,KAAK,EAAE;YAC5C;UAAA;QAEJ,CAAC;QACDgD,KAAK,EAAGC,GAAG,IAAI;UACb,IAAI,CAACnF,YAAY,CAAC4J,IAAI,CAAC;YACrBC,OAAO,EAAE,IAAI,CAACzI,SAAS,CAAC,aAAa,CAAC;YACtC0I,KAAK,EAAE;WACR,CAAC;UACFxF,OAAO,CAACY,KAAK,CAAC,kBAAkB,EAAEC,GAAG,CAAC;QACxC,CAAC;QACDhB,QAAQ,EAAEA,CAAA,KAAK;UACb,IAAI,CAACsF,kBAAkB,EAAE;UACzBnF,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC5C;OACD,CAAC;IACN;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEO0F,YAAYA,CAACC,MAAc;MAChC,IAAI,CAACvH,WAAW,GAAG,KAAK;MACxB,IAAI;QACF,MAAMwH,YAAY,GAAG7B,IAAI,CAAC8B,KAAK,CAACF,MAAM,CAAC;QACvC,IAAI,CAACvH,WAAW,GAAG,IAAI;QACvB,OAAOwH,YAAY;MACrB,CAAC,CAAC,OAAOE,CAAC,EAAE;QACV,OAAO,IAAI;MACb;IACF;IAEOvC,eAAeA,CAAA;MACpB,IAAIwC,OAAO,GAAmC,IAAIC,QAAQ,EAAE;MAC5D,IAAIC,WAAW,GAAG,EAAE;MAGpB,IAAI,CAACpK,MAAM,GAAGzB,eAAe,CAAC8L,OAAO;MACrC,IAAI,IAAI,CAAC5H,aAAa,CAACiC,MAAM,EAAE;QAC7B,IAAI,CAACjC,aAAa,CAAC2C,OAAO,CAAEkF,IAAI,IAAI;UAClCJ,OAAO,CAACK,MAAM,CAAC,OAAO,EAAED,IAAI,CAAC;QAC/B,CAAC,CAAC;QACFJ,OAAO,CAACK,MAAM,CAAC,YAAY,EAAE,IAAI,CAACzJ,UAAU,CAAC;QAC7CoJ,OAAO,CAACK,MAAM,CAAC,YAAY,EAAErC,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC1G,YAAY,CAAC+I,KAAK,CAAC,CAAC;QACrEN,OAAO,CAACK,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC9J,YAAY,CAACgK,aAAa,EAAE,CAAC;QACzDP,OAAO,CAACK,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC1I,WAAW,CAAC;QAC/CuI,WAAW,GAAG,QAAQ;MACxB,CAAC,MAAM;QACLF,OAAO,GAAG;UACRQ,UAAU,EAAE,IAAI,CAAC5J,UAAU;UAC3B6J,UAAU,EAAE,IAAI,CAAClJ,YAAY,CAAC+I,KAAK;UACnC3I,WAAW,EAAE,IAAI,CAACA,WAAW;UAC7B+I,IAAI,EAAE,IAAI,CAACnK,YAAY,CAACgK,aAAa;SACtC;MACH;MAEA,IAAI,CAACd,eAAe,CAAC,IAAI,CAAC9H,WAAW,CAAC;MACtC,IAAI,CAACsF,iBAAiB,EAAE;MAExB,IAAI,CAAC3G,eAAe,CACjBkH,eAAe,CAACwC,OAAO,EAAEE,WAAW,CAAC,CACrC7G,IAAI,CAAC3F,SAAS,CAAC,IAAI,CAACsE,QAAQ,CAAC,CAAC,CAC9BsB,SAAS,CAAC;QACTM,IAAI,EAAGU,GAAG,IAAI;UACZ,IAAI,CAAClD,gBAAgB,GAAG,KAAK;UAC7B,IAAI,CAACvB,SAAS,GAAG,KAAK;UACtB,IAAI,CAACkB,iBAAiB,CAAC+D,aAAa,CAACR,GAAG,EAAEiD,OAAO,IAAI,4CAA4C,CAAC;UAElG,IAAIjD,GAAG,EAAEqG,gBAAgB,EAAEC,QAAQ,EAAEhB,MAAM,EAAE;YAC3C,IAAI,CAAC9H,mBAAmB,GAAG,IAAI;YAC/B;YACA,IAAI,CAACpC,YAAY,CAAC4J,IAAI,CAAC;cACrBC,OAAO,EAAE,IAAI,CAACzI,SAAS,CAAC,QAAQ,CAAC,CAAC+J,oBAAoB;cACtDrB,KAAK,EAAE;aACR,CAAC;YACF,IAAI,CAACrH,QAAQ,GAAG,KAAK;YACrB,IAAI,CAACC,UAAU,GAAGkC,GAAG,EAAEqG,gBAAgB,EAAEC,QAAQ,EAAEhB,MAAM;YACzD,IAAI,CAACtI,YAAY,GAAGgD,GAAG,EAAEqG,gBAAgB,EAAEC,QAAQ,EAAEE,YAAY,CAAC3E,GAAG,CAAE4E,IAAS,IAAI;cAClF,OAAO;gBACL1L,EAAE,EAAE0L,IAAI,EAAE1L,EAAE,IAAI,EAAE;gBAClB2L,KAAK,EAAED,IAAI,EAAEC,KAAK,IAAI,EAAE;gBACxBzB,OAAO,EAAEwB,IAAI,EAAExB,OAAO,IAAI,EAAE;gBAC5BnE,SAAS,EAAE2F,IAAI,EAAE3F,SAAS,IAAI,EAAE;gBAChC0C,SAAS,EAAEiD,IAAI,EAAEjD,SAAS,IAAI,EAAE;gBAChCK,IAAI,EAAE4C,IAAI,EAAE5C,IAAI,IAAI,EAAE;gBACtB5C,WAAW,EAAEwF,IAAI,EAAExF,WAAW,IAAI,EAAE;gBACpC0F,eAAe,EAAEF,IAAI,EAAEE,eAAe,IAAI,EAAE;gBAC5CC,OAAO,EAAEH,IAAI,EAAEG,OAAO,IAAI,EAAE;gBAC5BC,GAAG,EAAEJ,IAAI,EAAEI,GAAG,IAAI;eACnB;YACH,CAAC,CAAC;YAEF,IAAI,CAACjL,WAAW,GAAGoE,GAAG,EAAEqG,gBAAgB,EAAEC,QAAQ,EAAEE,YAAY,CAAC3E,GAAG,CACjE4E,IAKA,IAAI;cACH,OAAO;gBACLxF,WAAW,EAAEwF,IAAI,CAACxF,WAAW;gBAC7B2F,OAAO,EAAEH,IAAI,CAACG,OAAO;gBACrBC,GAAG,EAAEJ,IAAI,CAACI,GAAG;gBACbF,eAAe,EAAEF,IAAI,CAACE;eACvB;YACH,CAAC,CACF;YAED;YACA;YACA;YAEA;YACA;YACA;YACA;YACA;YACA;UACF;UACA,IAAI,CAACtB,YAAY,CAAC,IAAI,CAACvH,UAAU,CAAC;UAClC,IAAI,CAACtC,MAAM,GAAGzB,eAAe,CAAC+M,SAAS;UACvC,IAAI,CAACjE,gBAAgB,EAAE;UACvB,IAAI,CAAC5E,aAAa,GAAG,EAAE;QACzB,CAAC;QACDqC,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAAC9C,mBAAmB,GAAG,IAAI;UAC/B,IAAI,CAACV,gBAAgB,GAAG,KAAK;UAC7B,IAAI,CAACe,QAAQ,GAAG,IAAI;UACpB,IAAI,CAACC,UAAU,GAAGwC,KAAK,EAAEA,KAAK,EAAEyG,MAAM;UACtC,IAAI,CAAC/K,eAAe,CAACgL,qBAAqB,EAAE;UAC5C,IAAI,CAAC5L,YAAY,CAAC4J,IAAI,CAAC;YACrBC,OAAO,EAAE,IAAI,CAACzI,SAAS,CAAC,QAAQ,CAAC,CAACyK,iBAAiB;YACnD/B,KAAK,EAAE;WACR,CAAC;UACF,IAAI,CAACzI,iBAAiB,CAAC+D,aAAa,CAClC,sDAAsD,CACvD;UACD,IAAI,CAACvC,aAAa,GAAG,EAAE;UACvB,IAAI,CAAC4E,gBAAgB,EAAE;UACvBnD,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEW,KAAK,CAAC2C,OAAO,CAAC;QACxC;OACD,CAAC;IACN;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEAiE,gBAAgBA,CAAA;MACdxH,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;IACjC;IAEAwH,qBAAqBA,CAACC,KAAa;MACjC,IAAG,IAAI,CAAC7I,iBAAiB,KAAG,IAAI,CAACD,eAAe,CAAC4B,MAAM,IAAI,IAAI,CAAC5B,eAAe,CAAC4B,MAAM,KAAG,CAAC,EAAC;QACzF,IAAI,CAACjC,aAAa,GAAGmJ,KAAK;QAC1B;MACF;MAEA,MAAMjE,KAAK,GAAG,IAAI,CAAC7E,eAAe,CAAC,IAAI,CAACC,iBAAiB,CAAC;MAC1D,IAAG,IAAI,CAAC2D,YAAY,CAACiB,KAAK,CAAC,EAAC;QAC1B,IAAIiE,KAAK,IAAIA,KAAK,CAAClH,MAAM,GAAG,CAAC,EAAE;UAC7B,IAAI,CAACmH,eAAe,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC;QAChC;MACF,CAAC,MAAM;QACL,IAAI,CAACnJ,aAAa,GAAGmJ,KAAK;MAC5B;IACF;IAEA/G,oBAAoBA,CAAA;MAClB,IAAI,CAAC/B,eAAe,GAAGqD,MAAM,CAAC2F,IAAI,CAAC,IAAI,CAACrK,YAAY,CAACsK,QAAQ,CAAC;MAC9D,IAAI,CAAChJ,iBAAiB,GAAG,CAAC;MAC1B,IAAI,IAAI,CAACD,eAAe,CAAC4B,MAAM,GAAG,CAAC,EAAE;QACnC,IAAI,CAACmD,qBAAqB,EAAE;MAC9B,CAAC,MACG;QACF,IAAI,CAACrF,WAAW,GAAG,IAAI;QACvB,IAAI,CAACvB,iBAAiB,CAAC+D,aAAa,CAAC,uFAAuF,CAAC;MAC/H;IACF;IAEA6C,qBAAqBA,CAAA;MACnB,MAAMF,KAAK,GAAG,IAAI,CAAC7E,eAAe,CAAC,IAAI,CAACC,iBAAiB,CAAC;MAC1D,IAAI,IAAI,CAAC2D,YAAY,CAACiB,KAAK,CAAC,EAAE;QAC5B,IAAI,CAACjG,QAAQ,GAAG,sBAAsB;QACtC,IAAI,CAACT,iBAAiB,CAAC+D,aAAa,CAAC,8BAA8B2C,KAAK,EAAE,CAAC;QAC3E;MACF,CAAC,MAAM;QACL,IAAI,CAACjG,QAAQ,GAAG,MAAM,CAAC,CAAC;QACxB,IAAI,CAACT,iBAAiB,CAAC+D,aAAa,CAAC,6BAA6B2C,KAAK,EAAE,CAAC;MAC5E;IACF;IAEAkE,eAAeA,CAACvB,IAAU;MACxB,MAAM3C,KAAK,GAAG,IAAI,CAAC7E,eAAe,CAAC,IAAI,CAACC,iBAAiB,CAAC;MAC1D,IAAI,CAAC,IAAI,CAAC2D,YAAY,CAACiB,KAAK,CAAC,EAAE;MAE/B,MAAMqE,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,MAAM,GAAG,MAAK;QACnB,MAAMC,YAAY,GAAIH,MAAM,CAACI,MAAiB;QAC9C,IAAI,CAAC3K,YAAY,CAACiC,GAAG,CAACiE,KAAK,CAAC,EAAEC,QAAQ,CAACuE,YAAY,CAAC;QACpD,IAAI,CAACpJ,iBAAiB,EAAE;QACxB,IAAI,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAACD,eAAe,CAAC4B,MAAM,EAAE;UACxD,IAAI,CAACmD,qBAAqB,EAAE;QAC9B,CAAC,MAAM;UACL,IAAI,CAAC5G,iBAAiB,CAAC+D,aAAa,CAAC,oCAAoC,CAAC;UAC1E,IAAI,CAAC0C,eAAe,EAAE;QACxB;MACF,CAAC;MACDsE,MAAM,CAACK,aAAa,CAAC/B,IAAI,CAAC;IAC5B;;uCAtkBWjK,0BAA0B,EAAA7B,EAAA,CAAA8N,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAhO,EAAA,CAAA8N,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAjO,EAAA,CAAA8N,iBAAA,CAAAI,EAAA,CAAAC,eAAA,GAAAnO,EAAA,CAAA8N,iBAAA,CAAAM,EAAA,CAAAC,mBAAA,GAAArO,EAAA,CAAA8N,iBAAA,CAAAM,EAAA,CAAAE,aAAA,GAAAtO,EAAA,CAAA8N,iBAAA,CAAAS,EAAA,CAAAC,WAAA;IAAA;;YAA1B3M,0BAA0B;MAAA4M,SAAA;MAAAC,SAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBAY1BrP,sBAAsB;;;;;;;;;;;;;UC3DnCS,EAAA,CAAAC,cAAA,aAA0C;;UAIpCD,EAFJ,CAAAC,cAAA,aAAqD,WAC7C,wBAC4D;UAE9DD,EADA,CAAA8O,SAAA,cAAyC,cACE;UAGjD9O,EAFI,CAAAiB,YAAA,EAAiB,EACZ,EACH;;UASEjB,EARR,CAAAC,cAAA,aAA2C,aAKxC,aACgD,aAClB,cACC;UACxBD,EAAA,CAAA8O,SAAA,oBAA8D;UAElE9O,EADE,CAAAiB,YAAA,EAAK,EACD;UAGJjB,EAFF,CAAAC,cAAA,eAA+D,iCAe5D;UADCD,EAPA,CAAAE,UAAA,yBAAA6O,+EAAApO,MAAA;YAAAX,EAAA,CAAAI,aAAA,CAAA4O,GAAA;YAAA,OAAAhP,EAAA,CAAAQ,WAAA,CAAeqO,GAAA,CAAA7F,iBAAA,CAAArI,MAAA,CAAyB;UAAA,EAAC,+BAAAsO,qFAAA;YAAAjP,EAAA,CAAAI,aAAA,CAAA4O,GAAA;YAAA,OAAAhP,EAAA,CAAAQ,WAAA,CACpBqO,GAAA,CAAA3B,gBAAA,EAAkB;UAAA,EAAC,gCAAAgC,sFAAAvO,MAAA;YAAAX,EAAA,CAAAI,aAAA,CAAA4O,GAAA;YAAA,OAAAhP,EAAA,CAAAQ,WAAA,CAMlBqO,GAAA,CAAA1B,qBAAA,CAAAxM,MAAA,CAA6B;UAAA,EAAC;UAI5DX,EAHO,CAAAiB,YAAA,EAAqB,EAClB,EACF,EACF;UAOEjB,EAJR,CAAAC,cAAA,eAA0E,eAC5C,eACK,eACV,oBAShB;UAFCD,EAAA,CAAAE,UAAA,uBAAAiP,mEAAAxO,MAAA;YAAAX,EAAA,CAAAI,aAAA,CAAA4O,GAAA;YAAA,OAAAhP,EAAA,CAAAQ,WAAA,CAAaqO,GAAA,CAAA/N,WAAA,CAAAH,MAAA,CAAmB;UAAA,EAAC;UAGrCX,EADG,CAAAiB,YAAA,EAAW,EACR;UACNjB,EAAA,CAAAC,cAAA,eAAwC;UACtCD,EAAA,CAAA8O,SAAA,sBAYa;UASjB9O,EADE,CAAAiB,YAAA,EAAM,EACF;UAeNjB,EAbA,CAAAoP,UAAA,KAAAC,0CAAA,kBAAmE,KAAAC,0CAAA,kBAaF;UASzEtP,EAHM,CAAAiB,YAAA,EAAM,EACF,EACF,EACF;;;UArEMjB,EAAA,CAAAkB,SAAA,IAAuB;UAAvBlB,EAAA,CAAAuP,qBAAA,aAAAV,GAAA,CAAA3L,QAAA,CAAuB;UACvBlD,EATA,CAAAmB,UAAA,aAAA0N,GAAA,CAAAhM,YAAA,CAAyB,cAAAgM,GAAA,CAAA/L,gBAAA,CACK,eAAA+L,GAAA,CAAA7K,WAAA,CACJ,cAAA6K,GAAA,CAAA9L,SAAA,CAGH,yBACC,0BACC,yBAED;UAatB/C,EAAA,CAAAkB,SAAA,GAAuB;UAIvBlB,EAJA,CAAAmB,UAAA,SAAA0N,GAAA,CAAAzM,cAAA,CAAuB,gBAAAyM,GAAA,CAAArK,WAAA,CACI,4BAGA;UAS3BxE,EAAA,CAAAkB,SAAA,GAME;UANFlB,EAAA,CAAAmB,UAAA,iBAAAnB,EAAA,CAAAwP,eAAA,KAAAC,GAAA,EAME;UAcFzP,EAAA,CAAAkB,SAAA,EAAsC;UAAtClB,EAAA,CAAAmB,UAAA,SAAA0N,GAAA,CAAAlL,WAAA,sBAAsC;UAatC3D,EAAA,CAAAkB,SAAA,EAAoC;UAApClB,EAAA,CAAAmB,UAAA,SAAA0N,GAAA,CAAAlL,WAAA,oBAAoC;;;qBD3D9CzE,YAAY,EAAAwQ,EAAA,CAAAC,IAAA,EACZtQ,WAAW,EACXE,sBAAsB,EACtBC,sBAAsB,EACtBC,oBAAoB,EACpBG,aAAa,EACbF,eAAe,EACfC,aAAa;MAAAiQ,MAAA;IAAA;;SAKJ/N,0BAA0B;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}