{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { Subject, takeUntil, switchMap, finalize, catchError, of } from 'rxjs';\nimport { FormsModule } from '@angular/forms';\n// Import child components\nimport { AgentExecutionPlaygroundComponent } from './components/agent-execution-playground/agent-execution-playground.component';\nimport { IconComponent, CubicalLoadingComponent } from '@ava/play-comp-library';\nimport { environment } from '@shared/environments/environment';\nimport { ExecutionStatus } from '@shared/models/execution.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../services/agent-service.service\";\nimport * as i3 from \"../build-agents/services/agent-playground.service\";\nimport * as i4 from \"@shared/auth/services/token-storage.service\";\nimport * as i5 from \"@shared/services/loader/loader.service\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"@shared/services/tool-execution/tool-execution.service\";\nimport * as i8 from \"@angular/common\";\nfunction AgentExecutionComponent_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 19);\n    i0.ɵɵtext(1, \" History \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_23_div_40_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74);\n    i0.ɵɵtext(1, \" No prompt configured \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_23_div_40_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"span\", 76);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r3 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(node_r3.name || \"Prompt\");\n  }\n}\nfunction AgentExecutionComponent_div_23_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_23_div_40_div_1_Template, 2, 0, \"div\", 71);\n    i0.ɵɵelementStart(2, \"div\", 72);\n    i0.ɵɵtemplate(3, AgentExecutionComponent_div_23_div_40_div_3_Template, 3, 1, \"div\", 73);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.blueprintPromptNodes.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.blueprintPromptNodes);\n  }\n}\nfunction AgentExecutionComponent_div_23_div_61_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74);\n    i0.ɵɵtext(1, \" No knowledge base configured \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_23_div_61_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"span\", 76);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(node_r4.name || \"Knowledge Base\");\n  }\n}\nfunction AgentExecutionComponent_div_23_div_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_23_div_61_div_1_Template, 2, 0, \"div\", 71);\n    i0.ɵɵelementStart(2, \"div\", 72);\n    i0.ɵɵtemplate(3, AgentExecutionComponent_div_23_div_61_div_3_Template, 3, 1, \"div\", 73);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.blueprintKnowledgeNodes.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.blueprintKnowledgeNodes);\n  }\n}\nfunction AgentExecutionComponent_div_23_div_79_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74);\n    i0.ɵɵtext(1, \" No model configured \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_23_div_79_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"span\", 76);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(node_r5.name || \"Model\");\n  }\n}\nfunction AgentExecutionComponent_div_23_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_23_div_79_div_1_Template, 2, 0, \"div\", 71);\n    i0.ɵɵelementStart(2, \"div\", 72);\n    i0.ɵɵtemplate(3, AgentExecutionComponent_div_23_div_79_div_3_Template, 3, 1, \"div\", 73);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.blueprintModelNodes.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.blueprintModelNodes);\n  }\n}\nfunction AgentExecutionComponent_div_23_div_80__svg_svg_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 44);\n    i0.ɵɵelement(1, \"rect\", 79)(2, \"path\", 80);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_23_div_80_div_13_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74);\n    i0.ɵɵtext(1, \" No tools configured \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_23_div_80_div_13_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"span\", 76);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(node_r7.name || \"Tool\");\n  }\n}\nfunction AgentExecutionComponent_div_23_div_80_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_23_div_80_div_13_div_1_Template, 2, 0, \"div\", 71);\n    i0.ɵɵelementStart(2, \"div\", 72);\n    i0.ɵɵtemplate(3, AgentExecutionComponent_div_23_div_80_div_13_div_3_Template, 3, 1, \"div\", 73);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.blueprintToolNodes.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.blueprintToolNodes);\n  }\n}\nfunction AgentExecutionComponent_div_23_div_80_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 77)(1, \"div\", 41);\n    i0.ɵɵlistener(\"click\", function AgentExecutionComponent_div_23_div_80_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleBlueprintZone(\"tool\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 42)(3, \"div\", 43);\n    i0.ɵɵtemplate(4, AgentExecutionComponent_div_23_div_80__svg_svg_4_Template, 3, 0, \"svg\", 78);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h3\", 47);\n    i0.ɵɵtext(6, \"Tools\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 48)(8, \"span\", 62);\n    i0.ɵɵtext(9, \"Optional\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 50);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(11, \"svg\", 51);\n    i0.ɵɵelement(12, \"path\", 52);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(13, AgentExecutionComponent_div_23_div_80_div_13_Template, 4, 2, \"div\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"has-nodes\", ctx_r1.blueprintToolNodes.length > 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.agentType === \"collaborative\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵstyleProp(\"transform\", ctx_r1.isBlueprintZoneExpanded(\"tool\") ? \"rotate(180deg)\" : \"rotate(0deg)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isBlueprintZoneExpanded(\"tool\"));\n  }\n}\nfunction AgentExecutionComponent_div_23_div_81_div_15_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74);\n    i0.ɵɵtext(1, \" No guardrails configured \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_23_div_81_div_15_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"span\", 76);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r9 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(node_r9.name || \"Guardrail\");\n  }\n}\nfunction AgentExecutionComponent_div_23_div_81_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_23_div_81_div_15_div_1_Template, 2, 0, \"div\", 71);\n    i0.ɵɵelementStart(2, \"div\", 72);\n    i0.ɵɵtemplate(3, AgentExecutionComponent_div_23_div_81_div_15_div_3_Template, 3, 1, \"div\", 73);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.blueprintGuardrailNodes.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.blueprintGuardrailNodes);\n  }\n}\nfunction AgentExecutionComponent_div_23_div_81_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 81)(1, \"div\", 41);\n    i0.ɵɵlistener(\"click\", function AgentExecutionComponent_div_23_div_81_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleBlueprintZone(\"guardrail\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 42)(3, \"div\", 43);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(4, \"svg\", 44);\n    i0.ɵɵelement(5, \"rect\", 82)(6, \"path\", 83);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(7, \"h3\", 47);\n    i0.ɵɵtext(8, \"Guardrails\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 48)(10, \"span\", 62);\n    i0.ɵɵtext(11, \"Optional\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 50);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(13, \"svg\", 51);\n    i0.ɵɵelement(14, \"path\", 52);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(15, AgentExecutionComponent_div_23_div_81_div_15_Template, 4, 2, \"div\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"has-nodes\", ctx_r1.blueprintGuardrailNodes.length > 0);\n    i0.ɵɵadvance(13);\n    i0.ɵɵstyleProp(\"transform\", ctx_r1.isBlueprintZoneExpanded(\"guardrail\") ? \"rotate(180deg)\" : \"rotate(0deg)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isBlueprintZoneExpanded(\"guardrail\"));\n  }\n}\nfunction AgentExecutionComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"div\", 21)(2, \"h3\");\n    i0.ɵɵtext(3, \"Agent Blueprint\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 22);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(5, \"svg\", 23);\n    i0.ɵɵelement(6, \"line\", 24)(7, \"line\", 25)(8, \"line\", 26)(9, \"line\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(10, \"div\", 28)(11, \"div\", 29);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(12, \"svg\", 30)(13, \"defs\")(14, \"linearGradient\", 31);\n    i0.ɵɵelement(15, \"stop\", 32)(16, \"stop\", 33);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(17, \"circle\", 34)(18, \"circle\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(19, \"div\", 36)(20, \"div\", 37);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 38);\n    i0.ɵɵtext(23, \"Complete\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(24, \"div\", 39)(25, \"div\", 40)(26, \"div\", 41);\n    i0.ɵɵlistener(\"click\", function AgentExecutionComponent_div_23_Template_div_click_26_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleBlueprintZone(\"prompt\"));\n    });\n    i0.ɵɵelementStart(27, \"div\", 42)(28, \"div\", 43);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(29, \"svg\", 44);\n    i0.ɵɵelement(30, \"rect\", 45)(31, \"path\", 46);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(32, \"h3\", 47);\n    i0.ɵɵtext(33, \"System Prompt\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 48)(35, \"span\", 49);\n    i0.ɵɵtext(36, \"Required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"button\", 50);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(38, \"svg\", 51);\n    i0.ɵɵelement(39, \"path\", 52);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(40, AgentExecutionComponent_div_23_div_40_Template, 4, 2, \"div\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(41, \"div\", 54)(42, \"div\", 41);\n    i0.ɵɵlistener(\"click\", function AgentExecutionComponent_div_23_Template_div_click_42_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleBlueprintZone(\"knowledge\"));\n    });\n    i0.ɵɵelementStart(43, \"div\", 42)(44, \"div\", 43);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(45, \"svg\", 44);\n    i0.ɵɵelement(46, \"rect\", 55)(47, \"path\", 56)(48, \"path\", 57)(49, \"path\", 58)(50, \"path\", 59)(51, \"path\", 60)(52, \"path\", 61);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(53, \"h3\", 47);\n    i0.ɵɵtext(54, \"Knowledgebase\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(55, \"div\", 48)(56, \"span\", 62);\n    i0.ɵɵtext(57, \"Optional\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"button\", 50);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(59, \"svg\", 51);\n    i0.ɵɵelement(60, \"path\", 52);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(61, AgentExecutionComponent_div_23_div_61_Template, 4, 2, \"div\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(62, \"div\", 63)(63, \"div\", 41);\n    i0.ɵɵlistener(\"click\", function AgentExecutionComponent_div_23_Template_div_click_63_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleBlueprintZone(\"model\"));\n    });\n    i0.ɵɵelementStart(64, \"div\", 42)(65, \"div\", 43);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(66, \"svg\", 44);\n    i0.ɵɵelement(67, \"rect\", 64)(68, \"path\", 65)(69, \"path\", 66)(70, \"path\", 67);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(71, \"h3\", 47);\n    i0.ɵɵtext(72, \"AI Model\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(73, \"div\", 48)(74, \"span\", 49);\n    i0.ɵɵtext(75, \"Required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(76, \"button\", 50);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(77, \"svg\", 51);\n    i0.ɵɵelement(78, \"path\", 52);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(79, AgentExecutionComponent_div_23_div_79_Template, 4, 2, \"div\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(80, AgentExecutionComponent_div_23_div_80_Template, 14, 6, \"div\", 68)(81, AgentExecutionComponent_div_23_div_81_Template, 16, 5, \"div\", 69);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(18);\n    i0.ɵɵstyleProp(\"stroke-dasharray\", 314)(\"stroke-dashoffset\", 314 - 314 * ctx_r1.blueprintCompletionPercentage / 100);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.blueprintCompletionPercentage, \"% \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"has-nodes\", ctx_r1.blueprintPromptNodes.length > 0);\n    i0.ɵɵadvance(13);\n    i0.ɵɵstyleProp(\"transform\", ctx_r1.isBlueprintZoneExpanded(\"prompt\") ? \"rotate(180deg)\" : \"rotate(0deg)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isBlueprintZoneExpanded(\"prompt\"));\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"has-nodes\", ctx_r1.blueprintKnowledgeNodes.length > 0);\n    i0.ɵɵadvance(18);\n    i0.ɵɵstyleProp(\"transform\", ctx_r1.isBlueprintZoneExpanded(\"knowledge\") ? \"rotate(180deg)\" : \"rotate(0deg)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isBlueprintZoneExpanded(\"knowledge\"));\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"has-nodes\", ctx_r1.blueprintModelNodes.length > 0);\n    i0.ɵɵadvance(15);\n    i0.ɵɵstyleProp(\"transform\", ctx_r1.isBlueprintZoneExpanded(\"model\") ? \"rotate(180deg)\" : \"rotate(0deg)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isBlueprintZoneExpanded(\"model\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.agentType === \"collaborative\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.agentType === \"individual\");\n  }\n}\nfunction AgentExecutionComponent_div_24_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 89);\n    i0.ɵɵelement(1, \"ava-cubical-loading\", 90);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AgentExecutionComponent_div_24_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 91)(1, \"p\");\n    i0.ɵɵtext(2, \"No agent outputs available yet. Send a message to see the response.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AgentExecutionComponent_div_24_div_6_div_1_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 102)(1, \"div\", 103)(2, \"h4\");\n    i0.ɵɵtext(3, \"Response\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 104);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const execution_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (execution_r10.response == null ? null : execution_r10.response.response == null ? null : execution_r10.response.response.choices == null ? null : execution_r10.response.response.choices[0] == null ? null : execution_r10.response.response.choices[0].text) || (execution_r10.response == null ? null : execution_r10.response.agentResponse == null ? null : execution_r10.response.agentResponse.detail) || \"No response content available.\", \" \");\n  }\n}\nfunction AgentExecutionComponent_div_24_div_6_div_1_div_15_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 111)(1, \"strong\");\n    i0.ɵɵtext(2, \"Description:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const taskOutput_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", taskOutput_r11.description, \" \");\n  }\n}\nfunction AgentExecutionComponent_div_24_div_6_div_1_div_15_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 112)(1, \"strong\");\n    i0.ɵɵtext(2, \"Expected Output:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const taskOutput_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", taskOutput_r11.expected_output, \" \");\n  }\n}\nfunction AgentExecutionComponent_div_24_div_6_div_1_div_15_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 106);\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_24_div_6_div_1_div_15_div_1_div_1_Template, 4, 1, \"div\", 107)(2, AgentExecutionComponent_div_24_div_6_div_1_div_15_div_1_div_2_Template, 4, 1, \"div\", 108);\n    i0.ɵɵelementStart(3, \"div\", 109);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 110)(6, \"h4\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const taskOutput_r11 = ctx.$implicit;\n    const j_r12 = ctx.index;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", taskOutput_r11.description);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", taskOutput_r11.expected_output);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", taskOutput_r11.raw, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Summary: \", taskOutput_r11.summary || \"Task \" + (j_r12 + 1), \"\");\n  }\n}\nfunction AgentExecutionComponent_div_24_div_6_div_1_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 102);\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_24_div_6_div_1_div_15_div_1_Template, 8, 4, \"div\", 105);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const execution_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", execution_r10.response == null ? null : execution_r10.response.agentResponse == null ? null : execution_r10.response.agentResponse.agent == null ? null : execution_r10.response.agentResponse.agent.tasksOutputs);\n  }\n}\nfunction AgentExecutionComponent_div_24_div_6_div_1_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 113)(1, \"div\", 114)(2, \"strong\");\n    i0.ɵɵtext(3, \"Error:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" The agent execution failed. Please try again. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AgentExecutionComponent_div_24_div_6_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 94)(1, \"div\", 95)(2, \"div\", 5);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 96);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 97)(7, \"div\", 98)(8, \"strong\");\n    i0.ɵɵtext(9, \"Query:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 99);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(14, AgentExecutionComponent_div_24_div_6_div_1_div_14_Template, 6, 1, \"div\", 100)(15, AgentExecutionComponent_div_24_div_6_div_1_div_15_Template, 2, 1, \"div\", 100)(16, AgentExecutionComponent_div_24_div_6_div_1_div_16_Template, 5, 0, \"div\", 101);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const execution_r10 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"success\", execution_r10.status === \"success\")(\"failed\", execution_r10.status === \"failed\")(\"pending\", execution_r10.status === \"pending\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(execution_r10.agentName);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(execution_r10.status);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", execution_r10.status === \"success\" ? \"Success\" : execution_r10.status === \"failed\" ? \"Failed\" : \"Pending\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", execution_r10.userMessage, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(13, 15, execution_r10.timestamp, \"medium\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", execution_r10.status === \"success\" && execution_r10.response && ctx_r1.agentType === \"individual\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", execution_r10.status === \"success\" && execution_r10.response && ctx_r1.agentType === \"collaborative\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", execution_r10.status === \"failed\");\n  }\n}\nfunction AgentExecutionComponent_div_24_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 92);\n    i0.ɵɵtemplate(1, AgentExecutionComponent_div_24_div_6_div_1_Template, 17, 18, \"div\", 93);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.executionHistory);\n  }\n}\nfunction AgentExecutionComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 84)(1, \"h3\");\n    i0.ɵɵtext(2, \"Agent Output\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, AgentExecutionComponent_div_24_div_3_Template, 2, 0, \"div\", 85);\n    i0.ɵɵelementStart(4, \"div\", 86);\n    i0.ɵɵtemplate(5, AgentExecutionComponent_div_24_div_5_Template, 3, 0, \"div\", 87)(6, AgentExecutionComponent_div_24_div_6_Template, 2, 1, \"div\", 88);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoadingOutput);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.executionHistory.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.executionHistory.length > 0);\n  }\n}\n// Remove duplicate definitions - they're now in shared models\nexport let AgentExecutionComponent = /*#__PURE__*/(() => {\n  class AgentExecutionComponent {\n    route;\n    router;\n    agentService;\n    agentPlaygroundService;\n    tokenStorage;\n    loaderService;\n    formBuilder;\n    toolExecutionService;\n    navigationTabs = [{\n      id: 'nav-home',\n      label: 'Agent Activity'\n    }, {\n      id: 'nav-products',\n      label: 'Agent Output'\n    }, {\n      id: 'nav-services',\n      label: 'Preview',\n      disabled: true\n    }];\n    // Agent details\n    agentId = null;\n    agentType = 'individual';\n    agentName = 'Agent';\n    agentDetail = '';\n    playgroundComp;\n    // Activity logs\n    activityLogs = [];\n    activityProgress = 0;\n    executionDetails;\n    isRunning = false;\n    status = ExecutionStatus.notStarted;\n    // Chat messages\n    chatMessages = [];\n    isProcessingChat = false;\n    inputText = '';\n    isLoadingOutput = false;\n    // Agent outputs\n    agentOutputs = [];\n    latestAgentResponse = null; // Store the latest agent response for display\n    // New properties for execution history\n    executionHistory = [];\n    agentForm;\n    // Execution state\n    executionStartTime = null;\n    executionCompleted = false;\n    executionId;\n    enableStreamingLog = environment.enableLogStreaming || 'all';\n    isExecutionComplete = false;\n    progressInterval;\n    destroy$ = new Subject();\n    selectedTab = 'Agent Activity';\n    demoTabs = [{\n      id: 'tab1',\n      label: 'History'\n    }, {\n      id: 'tab2',\n      label: 'Blueprint'\n    }, {\n      id: 'tab3',\n      label: 'Agent Output'\n    }];\n    errorMsg = false;\n    resMessage;\n    taskMessage = [];\n    isJsonValid = false;\n    disableChat = false;\n    selectedFiles = [];\n    agentNodes = [];\n    userInputList = [];\n    progress = 0;\n    isLoading = false;\n    loaderColor = '';\n    inputFieldOrder = [];\n    currentInputIndex = 0;\n    activeTabId = 'nav-home';\n    // Panel state properties\n    isLeftPanelCollapsed = false;\n    activeRightTab = 'blueprint';\n    // Agent-specific properties\n    currentAgentDetails = null;\n    buildAgentNodes = [];\n    canvasNodes = [];\n    canvasEdges = [];\n    selectedPrompt = '';\n    selectedAgentMode = '';\n    selectedUseCaseIdentifier = '';\n    agentFilesUploadedData = [];\n    agentAttachment = [];\n    isAgentPlaygroundLoading = false;\n    agentPlaygroundDestroy = new Subject();\n    agentChatPayload = [];\n    agentCode = '';\n    promptOptions = [];\n    // Custom Blueprint Display Properties\n    blueprintCompletionPercentage = 0;\n    blueprintPromptNodes = [];\n    blueprintModelNodes = [];\n    blueprintKnowledgeNodes = [];\n    blueprintGuardrailNodes = [];\n    blueprintToolNodes = [];\n    // Blueprint zone expansion state\n    blueprintZonesExpanded = {\n      prompt: true,\n      model: true,\n      knowledge: true,\n      guardrail: true,\n      tool: true\n    };\n    // Blueprint panel properties (using existing arrays above)\n    constructor(route, router, agentService, agentPlaygroundService, tokenStorage, loaderService, formBuilder, toolExecutionService) {\n      this.route = route;\n      this.router = router;\n      this.agentService = agentService;\n      this.agentPlaygroundService = agentPlaygroundService;\n      this.tokenStorage = tokenStorage;\n      this.loaderService = loaderService;\n      this.formBuilder = formBuilder;\n      this.toolExecutionService = toolExecutionService;\n      this.agentForm = this.formBuilder.group({\n        isConversational: [true],\n        isUseTemplate: [false]\n      });\n    }\n    ngOnInit() {\n      console.log('🌟 SHARED COMPONENT INITIALIZED! 🌟');\n      this.executionId = crypto.randomUUID();\n      this.route.params.subscribe(params => {\n        this.agentType = params['type'] || 'individual';\n        console.log('🌟 SHARED: Agent type set to:', this.agentType);\n      });\n      this.route.queryParams.subscribe(params => {\n        if (params['id']) {\n          this.agentId = params['id'];\n          this.loadAgentData(params['id']);\n        }\n      });\n      // Initialize chat messages\n      this.chatMessages = [{\n        from: 'ai',\n        text: `Hi there! I am ${this.agentName || 'your agent'}. How can I help you today?`\n      }];\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n      if (this.progressInterval) {\n        clearInterval(this.progressInterval);\n      }\n    }\n    onTabChange(event) {\n      this.activeTabId = event.id;\n      this.selectedTab = event.label;\n    }\n    loadAgentData(agentId) {\n      this.isLoading = true;\n      // Load agent data based on type\n      if (this.agentType === 'collaborative') {\n        this.agentService.getCollaborativeAgentDetailsById(agentId).subscribe({\n          next: response => {\n            this.handleAgentDataResponse(response);\n          },\n          error: error => {\n            console.error('Error loading collaborative agent:', error);\n            this.isLoading = false;\n          }\n        });\n      } else {\n        this.agentService.getAgentById(agentId).subscribe({\n          next: response => {\n            this.handleAgentDataResponse(response);\n          },\n          error: error => {\n            console.error('Error loading individual agent:', error);\n            this.isLoading = false;\n          }\n        });\n      }\n    }\n    handleAgentDataResponse(response) {\n      this.isLoading = false;\n      // Extract agent details\n      let agentData;\n      if (response.agentDetails && Array.isArray(response.agentDetails) && response.agentDetails.length > 0) {\n        agentData = response.agentDetails[0];\n      } else if (response.agentDetail) {\n        agentData = response.agentDetail;\n      } else if (response.data) {\n        agentData = response.data;\n      } else {\n        agentData = response;\n      }\n      if (agentData) {\n        this.currentAgentDetails = agentData;\n        // For individual agents, use useCaseName as the agent name\n        if (this.agentType === 'individual') {\n          this.agentName = agentData.useCaseName || agentData.name || agentData.agentName || 'Agent';\n        } else {\n          this.agentName = agentData.name || agentData.agentName || 'Agent';\n        }\n        this.agentDetail = agentData.description || agentData.agentDetail || '';\n        // For individual agents, set up the required properties for playground functionality\n        if (this.agentType === 'individual') {\n          // Set selectedPrompt to agent name or useCaseName to bypass dropdown requirement\n          this.selectedPrompt = agentData.useCaseName || agentData.name || 'loaded-agent';\n          // Set selectedAgentMode for API calls - use useCaseCode if available\n          this.selectedAgentMode = agentData.useCaseCode || agentData.useCaseName || agentData.name || '';\n          // Set useCaseIdentifier - use organizationPath if available\n          if (agentData.organizationPath) {\n            this.selectedUseCaseIdentifier = agentData.organizationPath;\n          } else if (agentData.useCaseCode) {\n            this.selectedUseCaseIdentifier = agentData.useCaseCode;\n          } else if (agentData.useCaseName) {\n            this.selectedUseCaseIdentifier = agentData.useCaseName;\n          }\n        }\n        // Update chat message with agent name\n        if (this.chatMessages.length > 0) {\n          this.chatMessages[0].text = `Hi there! I am ${this.agentName}. How can I help you today?`;\n        }\n        // Load agent nodes and configuration\n        this.loadAgentNodes(agentData);\n      }\n    }\n    loadAgentNodes(agentData) {\n      // Map agent configuration to blueprint panel\n      this.mapAgentConfigurationToBlueprint(agentData);\n    }\n    handleChatMessage(message) {\n      if (this.agentType === 'individual') {\n        // For individual agents, use the loaded agent details instead of requiring dropdown selection\n        if (!this.currentAgentDetails && (!this.selectedPrompt || this.selectedPrompt === 'default')) {\n          this.showAgentError('Agent details are not loaded. Please try refreshing the page.');\n          return;\n        }\n        let displayMessage = message;\n        if (this.agentFilesUploadedData.length > 0) {\n          const fileNames = this.agentFilesUploadedData.map(file => file.documentName).join(', ');\n          displayMessage = `${message}\\n\\n📎 Attached files: ${fileNames}`;\n        }\n        // Add to execution history\n        const executionId = crypto.randomUUID();\n        this.executionHistory.push({\n          id: executionId,\n          agentName: this.agentName,\n          userMessage: message,\n          status: 'pending',\n          // Will be updated based on API response\n          timestamp: new Date()\n        });\n        console.log('Added new execution to history:', this.executionHistory);\n        this.chatMessages = [...this.chatMessages, {\n          from: 'user',\n          text: displayMessage\n        }];\n        this.isProcessingChat = true;\n        const isConversational = this.agentForm.get('isConversational')?.value || false;\n        const isUseTemplate = this.agentForm.get('isUseTemplate')?.value || false;\n        console.log('Chat message handling - isConversational:', isConversational, 'isUseTemplate:', isUseTemplate);\n        // Use agent details from the loaded agent data instead of dropdown selection\n        // Mode should be the useCaseCode, not useCaseName\n        const agentMode = this.agentCode || this.selectedAgentMode || this.currentAgentDetails?.useCaseCode || this.currentAgentDetails?.useCaseName || this.currentAgentDetails?.name || this.selectedPrompt;\n        let useCaseIdentifier = this.selectedUseCaseIdentifier;\n        if (!useCaseIdentifier) {\n          // Use organizationPath if available, otherwise build from agent details\n          if (this.currentAgentDetails?.organizationPath) {\n            useCaseIdentifier = this.currentAgentDetails.organizationPath;\n          } else {\n            const orgPath = this.buildOrganizationPath();\n            const agentIdentifier = this.currentAgentDetails?.useCaseCode || this.currentAgentDetails?.useCaseName || this.currentAgentDetails?.name || agentMode;\n            useCaseIdentifier = `${agentIdentifier}${orgPath}`;\n          }\n        }\n        if (this.agentFilesUploadedData.length > 0) {\n          this.processAgentFilesAndSendMessage(message, agentMode, useCaseIdentifier, isConversational, isUseTemplate);\n          return;\n        }\n        this.sendAgentMessageToAPI(message, agentMode, useCaseIdentifier, isConversational, isUseTemplate);\n      } else if (this.agentType === 'collaborative') {\n        // Add to execution history for collaborative agents too\n        const executionId = crypto.randomUUID();\n        this.executionHistory.push({\n          id: executionId,\n          agentName: this.agentName,\n          userMessage: message,\n          status: 'pending',\n          // Will be updated based on API response\n          timestamp: new Date()\n        });\n        console.log('Added new collaborative execution to history:', this.executionHistory);\n        this.isProcessingChat = true;\n        // Set loading state for output section\n        this.isLoadingOutput = true;\n        this.setActiveRightTab('output');\n        let payload = {\n          executionId: this.executionId,\n          agentId: Number(this.agentId),\n          user: this.tokenStorage.getDaUsername() || '<EMAIL>',\n          userInputs: {\n            question: message\n          }\n        };\n        // Add user message to chat for collaborative agents\n        this.chatMessages = [...this.chatMessages, {\n          from: 'user',\n          text: message\n        }];\n        if (this.agentFilesUploadedData.length > 0) {\n          const fileWrapper = this.agentFilesUploadedData[0];\n          let displayMessage;\n          if (this.agentFilesUploadedData.length > 0) {\n            const fileNames = this.agentFilesUploadedData.map(file => file.documentName).join(', ');\n            displayMessage = `📎 Attached files: ${fileNames}`;\n            this.chatMessages = [{\n              from: 'user',\n              text: displayMessage\n            }];\n          }\n          this.agentPlaygroundService.submitAgentExecuteWithFile(payload, fileWrapper).pipe(finalize(() => {\n            this.isProcessingChat = false;\n            this.isAgentPlaygroundLoading = false;\n            this.isLoadingOutput = false;\n          }), takeUntil(this.agentPlaygroundDestroy)).subscribe({\n            next: res => this.handleAgentExecuteResponse(res, message),\n            error: err => {\n              // Update execution history with failed status\n              if (this.executionHistory.length > 0) {\n                this.executionHistory[this.executionHistory.length - 1].status = 'failed';\n              }\n              const errorMessage = err?.error?.message || err?.message || 'An error occurred while processing your request.';\n              // Update the last chat message with the final status\n              if (this.chatMessages.length > 0) {\n                const lastMessage = this.chatMessages[this.chatMessages.length - 1];\n                if (lastMessage.from === 'ai' && lastMessage.text.includes('Status: Pending')) {\n                  lastMessage.text = `${this.agentName} - Status: Failed`;\n                }\n              }\n              // Add error message to chat\n              this.chatMessages = [...this.chatMessages, {\n                from: 'ai',\n                text: `${this.agentName} - Status: Failed`\n              }];\n              // Switch to output tab even on failure\n              this.setActiveRightTab('output');\n            }\n          });\n        } else {\n          this.agentPlaygroundService.submitAgentExecute(payload).pipe(finalize(() => {\n            this.isProcessingChat = false;\n            this.isAgentPlaygroundLoading = false;\n            this.isLoadingOutput = false;\n          }), takeUntil(this.agentPlaygroundDestroy)).subscribe({\n            next: res => this.handleAgentExecuteResponse(res, message),\n            error: err => {\n              // Update execution history with failed status\n              if (this.executionHistory.length > 0) {\n                this.executionHistory[this.executionHistory.length - 1].status = 'failed';\n              }\n              const errorMessage = err?.error?.message || err?.message || 'An error occurred while processing your request.';\n              // Update the last chat message with the final status\n              if (this.chatMessages.length > 0) {\n                const lastMessage = this.chatMessages[this.chatMessages.length - 1];\n                if (lastMessage.from === 'ai' && lastMessage.text.includes('Status: Pending')) {\n                  lastMessage.text = `${this.agentName} - Status: Failed`;\n                }\n              }\n              // Add error message to chat\n              this.chatMessages = [...this.chatMessages, {\n                from: 'ai',\n                text: `${this.agentName} - Status: Failed`\n              }];\n              // Switch to output tab even on failure\n              this.setActiveRightTab('output');\n            }\n          });\n        }\n      }\n    }\n    onPromptChanged(prompt) {\n      this.inputText = prompt.name || String(prompt.value) || '';\n    }\n    onPlaygroundConversationalToggle(value) {\n      // Update the form control\n      this.agentForm.get('isConversational')?.setValue(value);\n      // When conversational mode is turned off, clear the conversation history\n      // This ensures that the next message will be treated as a fresh start\n      if (!value) {\n        this.agentChatPayload = [];\n        console.log('Conversational mode disabled - cleared chat payload history');\n      } else {\n        console.log('Conversational mode enabled - will maintain chat history');\n      }\n    }\n    onPlaygroundTemplateToggle(value) {\n      // Update the form control\n      this.agentForm.get('isUseTemplate')?.setValue(value);\n      console.log('Template mode toggled:', value);\n    }\n    onFilesSelected(files) {\n      this.selectedFiles = files;\n      // Update agentFilesUploadedData for agent execution\n      this.agentFilesUploadedData = files;\n    }\n    onApprovalRequested() {\n      // Handle approval request\n    }\n    saveLogs() {\n      // Save execution logs\n    }\n    exportResults(section) {\n      // Export results\n    }\n    handleControlAction(action) {\n      // Handle execution control actions\n    }\n    navigateBack() {\n      this.router.navigate(['/build/agents', this.agentType], {\n        queryParams: {\n          id: this.agentId,\n          mode: 'view'\n        }\n      });\n    }\n    editAgent() {\n      this.router.navigate(['/build/agents', this.agentType], {\n        queryParams: {\n          id: this.agentId,\n          mode: 'edit'\n        }\n      });\n    }\n    navigateToAgentsList() {\n      this.router.navigate(['/build/agents']);\n    }\n    toggleLeftPanel() {\n      this.isLeftPanelCollapsed = !this.isLeftPanelCollapsed;\n    }\n    setActiveRightTab(tab) {\n      this.activeRightTab = tab;\n    }\n    // Blueprint zone management methods\n    toggleBlueprintZone(zoneType) {\n      this.blueprintZonesExpanded[zoneType] = !this.blueprintZonesExpanded[zoneType];\n    }\n    isBlueprintZoneExpanded(zoneType) {\n      return this.blueprintZonesExpanded[zoneType] || false;\n    }\n    // API and helper methods from build-agents component\n    showAgentError(message) {\n      this.chatMessages = [...this.chatMessages, {\n        from: 'ai',\n        text: message\n      }];\n    }\n    buildOrganizationPath() {\n      // Simple implementation - in real scenario this would be from navbar/metadata\n      return '';\n    }\n    getMetadataFromNavbar() {\n      // Simple implementation - in real scenario this would get org level mapping\n      return {};\n    }\n    handleAgentExecuteResponse(response, message) {\n      try {\n        // Update execution history with success status\n        if (this.executionHistory.length > 0) {\n          this.executionHistory[this.executionHistory.length - 1].status = 'success';\n          this.executionHistory[this.executionHistory.length - 1].response = response;\n          console.log('Updated execution history:', this.executionHistory);\n        }\n        const outputRaw = response?.agentResponse?.agent?.output;\n        let formattedOutput = '';\n        if (outputRaw) {\n          // Directly replace escaped \\n with real newlines\n          formattedOutput = outputRaw.replace(/\\\\n/g, '\\n');\n        } else {\n          formattedOutput = response?.agentResponse?.detail;\n        }\n        // Add success status message to chat\n        this.chatMessages = [...this.chatMessages, {\n          from: 'ai',\n          text: `${this.agentName} - Status: Success`\n        }];\n        // Switch to output tab\n        this.setActiveRightTab('output');\n      } catch (err) {\n        // Update execution history with failed status\n        if (this.executionHistory.length > 0) {\n          this.executionHistory[this.executionHistory.length - 1].status = 'failed';\n          console.log('Updated execution history (failed):', this.executionHistory);\n        }\n        // Add failure status message to chat\n        this.chatMessages = [...this.chatMessages, {\n          from: 'ai',\n          text: `${this.agentName} - Status: Failed`\n        }];\n        // Switch to output tab even on failure\n        this.setActiveRightTab('output');\n      }\n    }\n    processAgentFilesAndSendMessage(message, mode, useCaseIdentifier, isConversational, isUseTemplate) {\n      const formData = new FormData();\n      this.agentFilesUploadedData.forEach(fileData => {\n        if (fileData.file) {\n          formData.append('files', fileData.file);\n        }\n      });\n      if (formData.has('files')) {\n        this.agentPlaygroundService.getFileToContent(formData).pipe(switchMap(fileResponse => {\n          const fileContent = fileResponse?.fileResponses?.map(response => response.fileContent)?.join('\\n') || '';\n          this.sendAgentMessageToAPIWithFiles(message, mode, useCaseIdentifier, isConversational, isUseTemplate, fileContent);\n          return of(null);\n        }), catchError(error => {\n          console.error('Error parsing files:', error);\n          this.sendAgentMessageToAPI(message, mode, useCaseIdentifier, isConversational, isUseTemplate);\n          return of(null);\n        })).subscribe();\n      } else {\n        this.sendAgentMessageToAPI(message, mode, useCaseIdentifier, isConversational, isUseTemplate);\n      }\n    }\n    sendAgentMessageToAPI(message, mode, useCaseIdentifier, isConversational, isUseTemplate) {\n      console.log('API Call Parameters:', {\n        message,\n        mode,\n        useCaseIdentifier,\n        isConversational,\n        isUseTemplate,\n        currentChatPayloadLength: this.agentChatPayload.length\n      });\n      if (isConversational) {\n        this.agentChatPayload.push({\n          content: message,\n          role: 'user'\n        });\n      }\n      const payload = isConversational ? this.agentChatPayload : message;\n      const {\n        levelId\n      } = this.getMetadataFromNavbar();\n      console.log('Final payload being sent:', payload);\n      // Set loading state for output section\n      this.isLoadingOutput = true;\n      this.setActiveRightTab('output');\n      this.agentPlaygroundService.generatePrompt(payload, mode, isConversational, isUseTemplate, this.agentAttachment, useCaseIdentifier, '', levelId).pipe(finalize(() => {\n        this.isProcessingChat = false;\n        this.isAgentPlaygroundLoading = false;\n        this.isLoadingOutput = false;\n      }), takeUntil(this.agentPlaygroundDestroy)).subscribe({\n        next: generatedResponse => {\n          // Call handleAgentExecuteResponse to properly handle the response\n          this.handleAgentExecuteResponse(generatedResponse, message);\n        },\n        error: error => {\n          console.error('API Error:', error);\n          // Update execution history with failed status\n          if (this.executionHistory.length > 0) {\n            this.executionHistory[this.executionHistory.length - 1].status = 'failed';\n          }\n          const errorMessage = error?.error?.message || error?.message || 'An error occurred while processing your request.';\n          // Add failure status message to chat\n          this.chatMessages = [...this.chatMessages, {\n            from: 'ai',\n            text: `${this.agentName} - Status: Failed`\n          }, {\n            from: 'ai',\n            text: errorMessage\n          }];\n          // Switch to output tab even on failure\n          this.setActiveRightTab('output');\n          if (isConversational && this.agentChatPayload.length > 0) {\n            this.agentChatPayload.pop();\n          }\n        }\n      });\n    }\n    sendAgentMessageToAPIWithFiles(message, mode, useCaseIdentifier, isConversational, isUseTemplate, fileContents) {\n      if (isConversational) {\n        this.agentChatPayload.push({\n          content: message,\n          role: 'user'\n        });\n      }\n      const payload = isConversational ? this.agentChatPayload : message;\n      const {\n        levelId\n      } = this.getMetadataFromNavbar();\n      // Set loading state for output section\n      this.isLoadingOutput = true;\n      this.setActiveRightTab('output');\n      this.agentPlaygroundService.generatePrompt(payload, mode, isConversational, isUseTemplate, this.agentAttachment, useCaseIdentifier, fileContents, levelId).pipe(finalize(() => {\n        this.isProcessingChat = false;\n        this.isAgentPlaygroundLoading = false;\n        this.isLoadingOutput = false;\n      }), takeUntil(this.agentPlaygroundDestroy)).subscribe({\n        next: generatedResponse => {\n          // Call handleAgentExecuteResponse to properly handle the response\n          this.handleAgentExecuteResponse(generatedResponse, message);\n        },\n        error: error => {\n          console.error('API Error:', error);\n          const errorMessage = error?.error?.message || error?.message || 'An error occurred while processing your request.';\n          // Add failure status message to chat\n          this.chatMessages = [...this.chatMessages, {\n            from: 'ai',\n            text: `${this.agentName} - Status: Failed`\n          }, {\n            from: 'ai',\n            text: errorMessage\n          }];\n          if (isConversational && this.agentChatPayload.length > 0) {\n            this.agentChatPayload.pop();\n          }\n        }\n      });\n    }\n    // Blueprint panel methods\n    mapAgentConfigurationToBlueprint(agentData) {\n      if (!agentData) {\n        console.warn('No agent data provided for blueprint');\n        return;\n      }\n      console.log('🔍 DEBUG: Full agent data received:', agentData);\n      console.log('🔍 DEBUG: Agent type:', this.agentType);\n      console.log('🔍 DEBUG: Agent data keys:', Object.keys(agentData));\n      // Clear existing nodes\n      this.buildAgentNodes = [];\n      this.canvasNodes = [];\n      let nodeCounter = 1;\n      // Map agent configuration to nodes based on agent type\n      if (this.agentType === 'individual') {\n        this.mapIndividualAgentToBlueprint(agentData, nodeCounter);\n      } else if (this.agentType === 'collaborative') {\n        this.mapCollaborativeAgentToBlueprint(agentData, nodeCounter);\n      }\n      console.log('🎯 Blueprint nodes mapped:', {\n        buildAgentNodes: this.buildAgentNodes,\n        canvasNodes: this.canvasNodes,\n        totalNodes: this.buildAgentNodes.length\n      });\n    }\n    mapIndividualAgentToBlueprint(agentData, nodeCounter) {\n      console.log('🔍 Individual agent mapping - checking fields:', {\n        config: agentData.config,\n        configLength: agentData.config?.length,\n        useCaseName: agentData.useCaseName,\n        prompt: agentData.prompt,\n        useCaseDetails: agentData.useCaseDetails\n      });\n      // Clear existing blueprint nodes\n      this.blueprintPromptNodes = [];\n      this.blueprintModelNodes = [];\n      this.blueprintKnowledgeNodes = [];\n      this.blueprintGuardrailNodes = [];\n      // Add prompt node from \"prompt\" field\n      if (agentData.prompt) {\n        this.blueprintPromptNodes.push({\n          id: `prompt-${nodeCounter++}`,\n          name: agentData.prompt,\n          type: 'prompt'\n        });\n        console.log('✅ Added prompt node:', agentData.prompt);\n      }\n      // Process the config array to extract model, knowledge bases, and guardrails\n      if (agentData.config && Array.isArray(agentData.config)) {\n        console.log('🔍 Processing config array with length:', agentData.config.length);\n        agentData.config.forEach((category, categoryIndex) => {\n          console.log(`🔍 Category ${categoryIndex} (ID: ${category.categoryId}):`, category.categoryName);\n          if (category.config && Array.isArray(category.config)) {\n            console.log(`🔍 Category ${categoryIndex} has ${category.config.length} config items`);\n            category.config.forEach((configItem, itemIndex) => {\n              console.log(`🔍 Config item ${categoryIndex}-${itemIndex}:`, {\n                configKey: configItem.configKey,\n                configValue: configItem.configValue,\n                categoryId: configItem.categoryId\n              });\n              // Handle AI Model from categoryId 1\n              if (configItem.categoryId === 1 && configItem.configKey === 'MODEL' && configItem.configValue) {\n                console.log('✅ Adding AI model node from categoryId 1:', configItem.configValue);\n                this.blueprintModelNodes.push({\n                  id: `model-${nodeCounter++}`,\n                  name: `${configItem.configKey}`,\n                  type: 'model'\n                });\n              }\n              // Handle Knowledge Base from categoryId 2\n              if (configItem.categoryId === 2 && configItem.configKey === 'RAG_KNOWLEDGEBASE_NAME' && configItem.configValue) {\n                console.log('✅ Adding knowledge base nodes from categoryId 2:', configItem.configValue);\n                const kbValue = configItem.configValue.toString();\n                const kbIds = kbValue.split(',').map(id => id.trim()).filter(id => id);\n                kbIds.forEach(kbId => {\n                  this.blueprintKnowledgeNodes.push({\n                    id: `knowledge-${nodeCounter++}`,\n                    name: `Knowledge Base: ${kbId}`,\n                    type: 'knowledge'\n                  });\n                });\n              }\n              // Handle Guardrails from categoryId 3 where configValue is true\n              if (configItem.categoryId === 3 && configItem.configValue === 'true') {\n                console.log('✅ Found enabled guardrail from categoryId 3:', {\n                  key: configItem.configKey,\n                  value: configItem.configValue\n                });\n                if (configItem.configKey === 'ENABLE_GUARDRAILS') {\n                  // Only add one general guardrail node if not already added\n                  if (this.blueprintGuardrailNodes.length === 0) {\n                    this.blueprintGuardrailNodes.push({\n                      id: `guardrail-${nodeCounter++}`,\n                      name: 'Guardrails Enabled',\n                      type: 'guardrail'\n                    });\n                  }\n                } else if (configItem.configKey.startsWith('GUARDRAIL_')) {\n                  // Add specific guardrail nodes for enabled guardrails\n                  let guardrailName = configItem.configKey;\n                  if (guardrailName.startsWith('GUARDRAIL_')) {\n                    guardrailName = guardrailName.replace('GUARDRAIL_', '').replace(/_/g, ' ');\n                  }\n                  this.blueprintGuardrailNodes.push({\n                    id: `guardrail-${nodeCounter++}`,\n                    name: `${guardrailName}`,\n                    type: 'guardrail'\n                  });\n                }\n              }\n            });\n          }\n        });\n      }\n      console.log('🎯 Final blueprint nodes:', {\n        promptNodes: this.blueprintPromptNodes,\n        modelNodes: this.blueprintModelNodes,\n        knowledgeNodes: this.blueprintKnowledgeNodes,\n        guardrailNodes: this.blueprintGuardrailNodes\n      });\n      // Calculate completion percentage\n      const totalRequired = 2; // Prompt + Model are required\n      const currentRequired = this.blueprintPromptNodes.length + this.blueprintModelNodes.length;\n      this.blueprintCompletionPercentage = Math.round(currentRequired / totalRequired * 100);\n    }\n    mapCollaborativeAgentToBlueprint(agentData, nodeCounter) {\n      console.log('🚀 SHARED COMPONENT: mapCollaborativeAgentToBlueprint called!');\n      console.log('🔍 DEBUG: Collaborative agent data received:', agentData);\n      console.log('🔍 DEBUG: Collaborative agent data keys:', Object.keys(agentData));\n      console.log('🔍 DEBUG: Agent type in component:', this.agentType);\n      console.log('🔍 DEBUG: Current nodeCounter:', nodeCounter);\n      // Clear existing blueprint nodes\n      this.blueprintPromptNodes = [];\n      this.blueprintModelNodes = [];\n      this.blueprintKnowledgeNodes = [];\n      this.blueprintToolNodes = [];\n      this.blueprintGuardrailNodes = [];\n      console.log('🔍 DEBUG: Cleared all blueprint node arrays');\n      // Add prompt node - handle different prompt structures for collaborative agents\n      const shouldCreatePromptNode = agentData.goal || agentData.role || agentData.description;\n      console.log('🔍 DEBUG: Checking prompt node creation:', {\n        goal: agentData.goal,\n        role: agentData.role,\n        description: agentData.description,\n        shouldCreatePromptNode\n      });\n      if (shouldCreatePromptNode) {\n        let promptNodeName = agentData.goal || agentData.role || agentData.description || 'Collaborative Agent Prompt';\n        // Truncate prompt if too long for display\n        if (promptNodeName.length > 150) {\n          promptNodeName = promptNodeName.substring(0, 150) + '...';\n        }\n        this.blueprintPromptNodes.push({\n          id: `prompt-${nodeCounter++}`,\n          name: promptNodeName,\n          type: 'prompt'\n        });\n        console.log('✅ Added collaborative prompt node:', promptNodeName);\n      }\n      // Add model nodes - handle both old and new API formats like build-agents\n      let modelReferences = [];\n      console.log('🔍 DEBUG: Checking model data:', {\n        hasAgentConfigs: !!agentData.agentConfigs,\n        agentConfigsContent: agentData.agentConfigs,\n        hasTools: agentData.tools,\n        toolsContent: agentData.tools,\n        hasUserTools: agentData.userTools,\n        userToolsContent: agentData.userTools\n      });\n      // New API format: agentConfigs.modelRef (array of model IDs or objects)\n      if (agentData.agentConfigs && agentData.agentConfigs.modelRef) {\n        const modelRefs = Array.isArray(agentData.agentConfigs.modelRef) ? agentData.agentConfigs.modelRef : [agentData.agentConfigs.modelRef];\n        modelReferences = modelRefs.map(ref => {\n          if (typeof ref === 'number' || typeof ref === 'string') {\n            return {\n              modelId: ref\n            };\n          }\n          return ref;\n        });\n      }\n      // Old API format: modelDetails\n      else if (agentData.modelDetails) {\n        modelReferences = [agentData.modelDetails];\n      }\n      // Fallback: check for model or modelName directly\n      else if (agentData.model || agentData.modelName) {\n        modelReferences = [{\n          modelId: agentData.model || agentData.modelName\n        }];\n      }\n      modelReferences.forEach(modelRef => {\n        const modelId = modelRef.modelId || modelRef.id;\n        const modelName = modelRef.model || modelRef.modelDeploymentName || `Model ID: ${modelId}`;\n        this.blueprintModelNodes.push({\n          id: `model-${nodeCounter++}`,\n          name: modelName,\n          type: 'model'\n        });\n        console.log('✅ Added collaborative model node:', modelName);\n      });\n      // Add knowledge base nodes - handle both old and new API formats\n      let knowledgeReferences = [];\n      // New API format: agentConfigs.knowledgeBaseRef (array of KB IDs or objects)\n      if (agentData.agentConfigs && agentData.agentConfigs.knowledgeBaseRef) {\n        const kbRefs = Array.isArray(agentData.agentConfigs.knowledgeBaseRef) ? agentData.agentConfigs.knowledgeBaseRef : [agentData.agentConfigs.knowledgeBaseRef];\n        knowledgeReferences = kbRefs.map(ref => {\n          if (typeof ref === 'number' || typeof ref === 'string') {\n            return {\n              knowledgeBaseId: ref\n            };\n          }\n          return ref;\n        });\n      }\n      // Old API format: knowledgeBase\n      else if (agentData.knowledgeBase && Array.isArray(agentData.knowledgeBase)) {\n        knowledgeReferences = agentData.knowledgeBase;\n      }\n      knowledgeReferences.forEach(kbRef => {\n        const kbId = kbRef.knowledgeBaseId || kbRef.id;\n        const collectionName = kbRef.indexCollectionName || kbRef.name;\n        const kbName = collectionName || `Knowledge Base ID: ${kbId}`;\n        this.blueprintKnowledgeNodes.push({\n          id: `knowledge-${nodeCounter++}`,\n          name: kbName,\n          type: 'knowledge'\n        });\n        console.log('✅ Added collaborative knowledge node:', kbName);\n      });\n      // Add tool nodes - handle both old and new API formats like build-agents\n      let toolReferences = [];\n      let userToolReferences = [];\n      console.log('🔍 DEBUG: Checking tool data:', {\n        hasAgentConfigs: !!agentData.agentConfigs,\n        agentConfigsContent: agentData.agentConfigs,\n        hasTools: agentData.tools,\n        toolsContent: agentData.tools,\n        hasUserTools: agentData.userTools,\n        userToolsContent: agentData.userTools\n      });\n      // New API format: agentConfigs.toolRef and agentConfigs.userToolRef\n      if (agentData.agentConfigs) {\n        if (agentData.agentConfigs.toolRef) {\n          const toolRefs = Array.isArray(agentData.agentConfigs.toolRef) ? agentData.agentConfigs.toolRef : [agentData.agentConfigs.toolRef];\n          toolReferences = toolRefs.map(ref => {\n            if (typeof ref === 'number' || typeof ref === 'string') {\n              return {\n                toolId: ref\n              };\n            }\n            return ref;\n          });\n        }\n        if (agentData.agentConfigs.userToolRef) {\n          const userToolRefs = Array.isArray(agentData.agentConfigs.userToolRef) ? agentData.agentConfigs.userToolRef : [agentData.agentConfigs.userToolRef];\n          userToolReferences = userToolRefs.map(ref => {\n            if (typeof ref === 'number' || typeof ref === 'string') {\n              return {\n                toolId: ref\n              };\n            }\n            return ref;\n          });\n        }\n      }\n      // Old API format: tools and userTools\n      else {\n        if (agentData.tools && Array.isArray(agentData.tools)) {\n          toolReferences = agentData.tools;\n        }\n        if (agentData.userTools && Array.isArray(agentData.userTools)) {\n          userToolReferences = agentData.userTools;\n        }\n      }\n      // Process built-in tools\n      toolReferences.forEach(tool => {\n        const toolId = tool.toolId || tool.id;\n        const toolName = tool.toolName || tool.name || `Tool ID: ${toolId}`;\n        this.blueprintToolNodes.push({\n          id: `tool-${nodeCounter++}`,\n          name: toolName,\n          type: 'tool'\n        });\n        console.log('✅ Added collaborative builtin tool node:', toolName);\n      });\n      // Process user tools\n      userToolReferences.forEach(userTool => {\n        const userToolId = userTool.toolId || userTool.id;\n        const userToolName = userTool.toolName || userTool.name || `User Tool ID: ${userToolId}`;\n        this.blueprintToolNodes.push({\n          id: `tool-${nodeCounter++}`,\n          name: userToolName,\n          type: 'tool'\n        });\n        console.log('✅ Added collaborative user tool node:', userToolName);\n      });\n      console.log('🎯 Final collaborative blueprint nodes:', {\n        promptNodes: this.blueprintPromptNodes,\n        modelNodes: this.blueprintModelNodes,\n        knowledgeNodes: this.blueprintKnowledgeNodes,\n        toolNodes: this.blueprintToolNodes,\n        guardrailNodes: this.blueprintGuardrailNodes\n      });\n      // Debug: Check blueprint node arrays lengths\n      console.log('📊 Blueprint node counts:', {\n        promptCount: this.blueprintPromptNodes.length,\n        modelCount: this.blueprintModelNodes.length,\n        knowledgeCount: this.blueprintKnowledgeNodes.length,\n        toolCount: this.blueprintToolNodes.length,\n        guardrailCount: this.blueprintGuardrailNodes.length\n      });\n      // Debug: Check if tools zone will be visible\n      console.log('🔧 Tools zone debug:', {\n        agentType: this.agentType,\n        isCollaborative: this.agentType === 'collaborative',\n        hasToolNodes: this.blueprintToolNodes.length > 0,\n        toolNodeNames: this.blueprintToolNodes.map(t => t.name)\n      });\n      // Calculate completion percentage\n      const totalRequired = 2; // Prompt + Model are required\n      const currentRequired = this.blueprintPromptNodes.length + this.blueprintModelNodes.length;\n      this.blueprintCompletionPercentage = Math.round(currentRequired / totalRequired * 100);\n    }\n    static ɵfac = function AgentExecutionComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AgentExecutionComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AgentServiceService), i0.ɵɵdirectiveInject(i3.AgentPlaygroundService), i0.ɵɵdirectiveInject(i4.TokenStorageService), i0.ɵɵdirectiveInject(i5.LoaderService), i0.ɵɵdirectiveInject(i6.FormBuilder), i0.ɵɵdirectiveInject(i7.ToolExecutionService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AgentExecutionComponent,\n      selectors: [[\"app-agent-execution\"]],\n      viewQuery: function AgentExecutionComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(AgentExecutionPlaygroundComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.playgroundComp = _t.first);\n        }\n      },\n      decls: 25,\n      vars: 25,\n      consts: [[1, \"agent-execution-container\"], [1, \"top-nav-bar\"], [1, \"nav-left\"], [\"type\", \"button\", 1, \"back-button\", 3, \"click\"], [\"iconName\", \"ArrowLeft\", \"iconSize\", \"16\", \"iconColor\", \"#374151\"], [1, \"agent-name\"], [1, \"main-content\"], [1, \"left-panel\"], [1, \"panel-header\"], [\"type\", \"button\", 1, \"collapse-btn\", 3, \"click\"], [\"iconSize\", \"16\", \"iconColor\", \"#6B7280\", 3, \"iconName\"], [\"class\", \"history-btn\", \"type\", \"button\", \"disabled\", \"\", 4, \"ngIf\"], [1, \"panel-content\"], [3, \"promptChange\", \"messageSent\", \"conversationalToggle\", \"templateToggle\", \"filesSelected\", \"approvalRequested\", \"messages\", \"isLoading\", \"agentType\", \"showChatInteractionToggles\", \"showAiPrincipleToggle\", \"showApprovalButton\", \"showDropdown\", \"showAgentNameInput\", \"showFileUploadButton\", \"showStatusOnly\", \"displayedAgentName\", \"agentNamePlaceholder\"], [1, \"right-panel\"], [1, \"tabs-container\"], [1, \"tab-btn\", 3, \"click\"], [\"class\", \"blueprint-content\", 4, \"ngIf\"], [\"class\", \"output-content\", 4, \"ngIf\"], [\"type\", \"button\", \"disabled\", \"\", 1, \"history-btn\"], [1, \"blueprint-content\"], [1, \"blueprint-header\"], [1, \"custom-blueprint-container\"], [\"viewBox\", \"0 0 100 100\", \"preserveAspectRatio\", \"none\", 1, \"connection-lines\"], [\"x1\", \"25\", \"y1\", \"25\", \"x2\", \"50\", \"y2\", \"50\", \"stroke\", \"#BBBEC5\", \"stroke-width\", \"0.3\"], [\"x1\", \"75\", \"y1\", \"25\", \"x2\", \"50\", \"y2\", \"50\", \"stroke\", \"#BBBEC5\", \"stroke-width\", \"0.3\"], [\"x1\", \"25\", \"y1\", \"75\", \"x2\", \"50\", \"y2\", \"50\", \"stroke\", \"#BBBEC5\", \"stroke-width\", \"0.3\"], [\"x1\", \"75\", \"y1\", \"75\", \"x2\", \"50\", \"y2\", \"50\", \"stroke\", \"#BBBEC5\", \"stroke-width\", \"0.3\"], [1, \"central-progress\"], [1, \"progress-ring\"], [\"width\", \"120\", \"height\", \"120\", 1, \"progress-circle\"], [\"id\", \"progressGradient\", \"x1\", \"0%\", \"y1\", \"0%\", \"x2\", \"100%\", \"y2\", \"100%\"], [\"offset\", \"33.91%\", 2, \"stop-color\", \"#0084ff\"], [\"offset\", \"100%\", 2, \"stop-color\", \"#03bdd4\"], [\"cx\", \"60\", \"cy\", \"60\", \"r\", \"50\", \"fill\", \"none\", \"stroke\", \"#e5e7eb\", \"stroke-width\", \"8\", 1, \"progress-background\"], [\"cx\", \"60\", \"cy\", \"60\", \"r\", \"50\", \"fill\", \"none\", \"stroke\", \"url(#progressGradient)\", \"stroke-width\", \"8\", \"stroke-linecap\", \"round\", \"transform\", \"rotate(180 60 60)\", 1, \"progress-bar\"], [1, \"progress-content\"], [1, \"progress-percentage\"], [1, \"progress-label\"], [\"id\", \"parent-box\"], [1, \"blueprint-zone\", \"north-zone\", \"prompts-zone\"], [1, \"zone-header\", 3, \"click\"], [1, \"header-content\"], [1, \"header-icon\"], [\"width\", \"45\", \"height\", \"44\", \"viewBox\", \"0 0 45 44\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"x\", \"0.679688\", \"width\", \"44\", \"height\", \"44\", \"rx\", \"8\", \"fill\", \"#5082EF\"], [\"d\", \"M20.6166 25.5C20.5273 25.1539 20.347 24.8381 20.0942 24.5854C19.8415 24.3327 19.5257 24.1523 19.1796 24.063L13.0446 22.481C12.94 22.4513 12.8478 22.3883 12.7822 22.3014C12.7166 22.2146 12.6812 22.1088 12.6812 22C12.6812 21.8912 12.7166 21.7854 12.7822 21.6986C12.8478 21.6118 12.94 21.5487 13.0446 21.519L19.1796 19.936C19.5256 19.8468 19.8413 19.6666 20.094 19.414C20.3467 19.1615 20.5272 18.8459 20.6166 18.5L22.1986 12.365C22.228 12.2599 22.291 12.1673 22.3779 12.1014C22.4649 12.0355 22.571 11.9998 22.6801 11.9998C22.7892 11.9998 22.8954 12.0355 22.9823 12.1014C23.0692 12.1673 23.1322 12.2599 23.1616 12.365L24.7426 18.5C24.8319 18.8461 25.0123 19.1619 25.265 19.4146C25.5177 19.6673 25.8336 19.8477 26.1796 19.937L32.3146 21.518C32.4201 21.5471 32.5132 21.61 32.5795 21.6971C32.6458 21.7841 32.6817 21.8906 32.6817 22C32.6817 22.1094 32.6458 22.2159 32.5795 22.3029C32.5132 22.39 32.4201 22.4529 32.3146 22.482L26.1796 24.063C25.8336 24.1523 25.5177 24.3327 25.265 24.5854C25.0123 24.8381 24.8319 25.1539 24.7426 25.5L23.1606 31.635C23.1312 31.7401 23.0682 31.8327 22.9813 31.8986C22.8944 31.9646 22.7882 32.0003 22.6791 32.0003C22.57 32.0003 22.4639 31.9646 22.3769 31.8986C22.29 31.8327 22.227 31.7401 22.1976 31.635L20.6166 25.5Z\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"zone-title\"], [1, \"header-actions\"], [1, \"required-badge\"], [\"type\", \"button\", 1, \"accordion-toggle\"], [\"width\", \"16\", \"height\", \"16\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\"], [\"d\", \"M6 9L12 15L18 9\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"class\", \"zone-content\", 4, \"ngIf\"], [1, \"blueprint-zone\", \"west-zone\", \"knowledge-zone\"], [\"x\", \"0.679688\", \"width\", \"44\", \"height\", \"44\", \"rx\", \"8\", \"fill\", \"#308666\"], [\"d\", \"M22.6797 17V31\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M26.6797 22H28.6797\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M26.6797 18H28.6797\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M13.6797 28C13.4145 28 13.1601 27.8946 12.9726 27.7071C12.785 27.5196 12.6797 27.2652 12.6797 27V14C12.6797 13.7348 12.785 13.4804 12.9726 13.2929C13.1601 13.1054 13.4145 13 13.6797 13H18.6797C19.7406 13 20.758 13.4214 21.5081 14.1716C22.2583 14.9217 22.6797 15.9391 22.6797 17C22.6797 15.9391 23.1011 14.9217 23.8513 14.1716C24.6014 13.4214 25.6188 13 26.6797 13H31.6797C31.9449 13 32.1993 13.1054 32.3868 13.2929C32.5743 13.4804 32.6797 13.7348 32.6797 14V27C32.6797 27.2652 32.5743 27.5196 32.3868 27.7071C32.1993 27.8946 31.9449 28 31.6797 28H25.6797C24.884 28 24.121 28.3161 23.5584 28.8787C22.9958 29.4413 22.6797 30.2044 22.6797 31C22.6797 30.2044 22.3636 29.4413 21.801 28.8787C21.2384 28.3161 20.4753 28 19.6797 28H13.6797Z\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M16.6797 22H18.6797\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M16.6797 18H18.6797\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"optional-badge\"], [1, \"blueprint-zone\", \"east-zone\", \"models-zone\"], [\"x\", \"0.679688\", \"width\", \"44\", \"height\", \"44\", \"rx\", \"8\", \"fill\", \"#997BCF\"], [\"d\", \"M31.6797 18C31.6793 17.6493 31.5867 17.3048 31.4112 17.0012C31.2357 16.6975 30.9834 16.4454 30.6797 16.27L23.6797 12.27C23.3756 12.0945 23.0308 12.0021 22.6797 12.0021C22.3286 12.0021 21.9837 12.0945 21.6797 12.27L14.6797 16.27C14.376 16.4454 14.1237 16.6975 13.9481 17.0012C13.7726 17.3048 13.68 17.6493 13.6797 18V26C13.68 26.3508 13.7726 26.6952 13.9481 26.9989C14.1237 27.3025 14.376 27.5547 14.6797 27.73L21.6797 31.73C21.9837 31.9056 22.3286 31.998 22.6797 31.998C23.0308 31.998 23.3756 31.9056 23.6797 31.73L30.6797 27.73C30.9834 27.5547 31.2357 27.3025 31.4112 26.9989C31.5867 26.6952 31.6793 26.3508 31.6797 26V18Z\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M13.9795 17L22.6795 22L31.3795 17\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M22.6797 32V22\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"class\", \"blueprint-zone south-zone tools-zone\", 3, \"has-nodes\", 4, \"ngIf\"], [\"class\", \"blueprint-zone south-zone guardrails-zone\", 3, \"has-nodes\", 4, \"ngIf\"], [1, \"zone-content\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [1, \"nodes-list\"], [\"class\", \"kanban-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"empty-state\"], [1, \"kanban-card\"], [1, \"card-title\"], [1, \"blueprint-zone\", \"south-zone\", \"tools-zone\"], [\"width\", \"45\", \"height\", \"44\", \"viewBox\", \"0 0 45 44\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\", 4, \"ngIf\"], [\"x\", \"0.679688\", \"width\", \"44\", \"height\", \"44\", \"rx\", \"8\", \"fill\", \"#D97706\"], [\"d\", \"M25.3795 16.3C25.1963 16.4869 25.0937 16.7382 25.0937 17C25.0937 17.2617 25.1963 17.5131 25.3795 17.7L26.9795 19.3C27.1665 19.4832 27.4178 19.5858 27.6795 19.5858C27.9413 19.5858 28.1926 19.4832 28.3795 19.3L32.1495 15.53C32.6524 16.6412 32.8046 17.8792 32.586 19.0791C32.3674 20.279 31.7883 21.3838 30.9258 22.2463C30.0634 23.1087 28.9586 23.6878 27.7587 23.9064C26.5588 24.1251 25.3207 23.9728 24.2095 23.47L17.2995 30.38C16.9017 30.7778 16.3622 31.0013 15.7995 31.0013C15.2369 31.0013 14.6974 30.7778 14.2995 30.38C13.9017 29.9822 13.6782 29.4426 13.6782 28.88C13.6782 28.3174 13.9017 27.7778 14.2995 27.38L21.2095 20.47C20.7067 19.3588 20.5545 18.1207 20.7731 16.9208C20.9917 15.7209 21.5708 14.6161 22.4333 13.7537C23.2957 12.8913 24.4005 12.3121 25.6004 12.0935C26.8003 11.8749 28.0384 12.0271 29.1495 12.53L25.3895 16.29L25.3795 16.3Z\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"blueprint-zone\", \"south-zone\", \"guardrails-zone\"], [\"x\", \"0.679688\", \"width\", \"44\", \"height\", \"44\", \"rx\", \"8\", \"fill\", \"#DC2626\"], [\"d\", \"M30.6797 23C30.6797 28 27.1797 30.5 23.0197 31.95C22.8019 32.0238 22.5652 32.0202 22.3497 31.94C18.1797 30.5 14.6797 28 14.6797 23V16C14.6797 15.7347 14.785 15.4804 14.9726 15.2929C15.1601 15.1053 15.4145 15 15.6797 15C17.6797 15 20.1797 13.8 21.9197 12.28C22.1315 12.099 22.401 11.9995 22.6797 11.9995C22.9583 11.9995 23.2278 12.099 23.4397 12.28C25.1897 13.81 27.6797 15 29.6797 15C29.9449 15 30.1993 15.1053 30.3868 15.2929C30.5743 15.4804 30.6797 15.7347 30.6797 16V23Z\", \"stroke\", \"white\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"output-content\"], [\"class\", \"loader-overlay\", 4, \"ngIf\"], [1, \"execution-history-container\"], [\"class\", \"no-output-message\", 4, \"ngIf\"], [\"class\", \"execution-history-list\", 4, \"ngIf\"], [1, \"loader-overlay\"], [\"background\", \"linear-gradient(45deg, #4facfe 0%, #00f2fe 100%)\"], [1, \"no-output-message\"], [1, \"execution-history-list\"], [\"class\", \"execution-item\", 3, \"success\", \"failed\", \"pending\", 4, \"ngFor\", \"ngForOf\"], [1, \"execution-item\"], [1, \"execution-header\"], [1, \"execution-status\"], [1, \"execution-details\"], [1, \"user-message\"], [1, \"execution-timestamp\"], [\"class\", \"execution-response\", 4, \"ngIf\"], [\"class\", \"execution-error\", 4, \"ngIf\"], [1, \"execution-response\"], [1, \"response-section\"], [1, \"response-text\"], [\"class\", \"task-output\", 4, \"ngFor\", \"ngForOf\"], [1, \"task-output\"], [\"class\", \"task-description\", 4, \"ngIf\"], [\"class\", \"task-expected\", 4, \"ngIf\"], [1, \"task-content\"], [1, \"task-header\"], [1, \"task-description\"], [1, \"task-expected\"], [1, \"execution-error\"], [1, \"error-message\"]],\n      template: function AgentExecutionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function AgentExecutionComponent_Template_button_click_3_listener() {\n            return ctx.navigateBack();\n          });\n          i0.ɵɵelement(4, \"ava-icon\", 4);\n          i0.ɵɵelementStart(5, \"span\", 5);\n          i0.ɵɵtext(6);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"div\", 7)(9, \"div\", 8)(10, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function AgentExecutionComponent_Template_button_click_10_listener() {\n            return ctx.toggleLeftPanel();\n          });\n          i0.ɵɵelement(11, \"ava-icon\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(12, AgentExecutionComponent_button_12_Template, 2, 0, \"button\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 12)(14, \"app-agent-execution-playground\", 13);\n          i0.ɵɵlistener(\"promptChange\", function AgentExecutionComponent_Template_app_agent_execution_playground_promptChange_14_listener($event) {\n            return ctx.onPromptChanged($event);\n          })(\"messageSent\", function AgentExecutionComponent_Template_app_agent_execution_playground_messageSent_14_listener($event) {\n            return ctx.handleChatMessage($event);\n          })(\"conversationalToggle\", function AgentExecutionComponent_Template_app_agent_execution_playground_conversationalToggle_14_listener($event) {\n            return ctx.onPlaygroundConversationalToggle($event);\n          })(\"templateToggle\", function AgentExecutionComponent_Template_app_agent_execution_playground_templateToggle_14_listener($event) {\n            return ctx.onPlaygroundTemplateToggle($event);\n          })(\"filesSelected\", function AgentExecutionComponent_Template_app_agent_execution_playground_filesSelected_14_listener($event) {\n            return ctx.onFilesSelected($event);\n          })(\"approvalRequested\", function AgentExecutionComponent_Template_app_agent_execution_playground_approvalRequested_14_listener() {\n            return ctx.onApprovalRequested();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"div\", 14)(16, \"div\", 8)(17, \"div\", 15)(18, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function AgentExecutionComponent_Template_button_click_18_listener() {\n            return ctx.setActiveRightTab(\"blueprint\");\n          });\n          i0.ɵɵtext(19, \" Blueprint \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function AgentExecutionComponent_Template_button_click_20_listener() {\n            return ctx.setActiveRightTab(\"output\");\n          });\n          i0.ɵɵtext(21, \" Agent Output \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(22, \"div\", 12);\n          i0.ɵɵtemplate(23, AgentExecutionComponent_div_23_Template, 82, 22, \"div\", 17)(24, AgentExecutionComponent_div_24_Template, 7, 3, \"div\", 18);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.agentName);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"collapsed\", ctx.isLeftPanelCollapsed);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"iconName\", ctx.isLeftPanelCollapsed ? \"ChevronRight\" : \"PanelLeft\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLeftPanelCollapsed);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"hidden\", ctx.isLeftPanelCollapsed);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"messages\", ctx.chatMessages)(\"isLoading\", ctx.isProcessingChat)(\"agentType\", ctx.agentType)(\"showChatInteractionToggles\", ctx.agentType === \"individual\")(\"showAiPrincipleToggle\", false)(\"showApprovalButton\", false)(\"showDropdown\", false)(\"showAgentNameInput\", true)(\"showFileUploadButton\", true)(\"showStatusOnly\", true)(\"displayedAgentName\", ctx.agentName)(\"agentNamePlaceholder\", \"Current Agent Name\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"active\", ctx.activeRightTab === \"blueprint\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"active\", ctx.activeRightTab === \"output\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.activeRightTab === \"blueprint\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.activeRightTab === \"output\");\n        }\n      },\n      dependencies: [CommonModule, i8.NgForOf, i8.NgIf, i8.DatePipe, FormsModule, AgentExecutionPlaygroundComponent, IconComponent, CubicalLoadingComponent],\n      styles: [\".agent-execution-container[_ngcontent-%COMP%] {\\n  height: calc(100vh - 84px);\\n  display: flex;\\n  flex-direction: column;\\n  color: var(--color-text-primary);\\n  overflow: hidden;\\n}\\n\\n.top-nav-bar[_ngcontent-%COMP%] {\\n  padding: 16px 24px;\\n  padding-bottom: 0px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  min-height: 64px;\\n}\\n\\n.nav-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.back-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 8px 12px;\\n  border-radius: 6px;\\n  transition: background-color 0.2s ease;\\n}\\n.back-button[_ngcontent-%COMP%]:hover {\\n  background: var(--color-background-tertiary);\\n}\\n.back-button[_ngcontent-%COMP%]   .agent-name[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #000000;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  gap: 16px;\\n  padding: 16px;\\n  padding-top: 0px;\\n  height: calc(100vh - 96px);\\n  overflow: hidden;\\n}\\n\\n.left-panel[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 400px;\\n  max-width: 600px;\\n  background: white;\\n  border-radius: 12px;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n  transition: all 0.3s ease;\\n}\\n.left-panel.collapsed[_ngcontent-%COMP%] {\\n  flex: 0 0 48px;\\n  min-width: 48px;\\n  max-width: 48px;\\n}\\n\\n.right-panel[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 400px;\\n  background: white;\\n  border-radius: 12px;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n\\n.panel-header[_ngcontent-%COMP%] {\\n  padding: 16px 20px;\\n  background: #e9effd;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  max-height: 45px;\\n  border-radius: 12px 12px 0 0;\\n}\\n\\n.collapse-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 6px;\\n  border-radius: 4px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: background-color 0.2s ease;\\n  color: #1a46a7;\\n}\\n.collapse-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--color-background-quaternary);\\n}\\n\\n.history-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 8px 12px;\\n  border-radius: 6px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #1a46a7;\\n  transition: all 0.2s ease;\\n}\\n.history-btn[_ngcontent-%COMP%]:disabled {\\n  cursor: not-allowed;\\n}\\n.history-btn[_ngcontent-%COMP%]:hover {\\n  color: var(--color-text-primary);\\n  background: var(--color-background-quaternary);\\n}\\n\\n.tabs-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  padding: 4px;\\n}\\n\\n.tab-btn[_ngcontent-%COMP%] {\\n  padding: 8px 20px;\\n  border: none;\\n  background: transparent;\\n  color: var(--text-secondary);\\n  border-radius: 10px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  white-space: nowrap;\\n  color: #1a46a7;\\n}\\n.tab-btn.active[_ngcontent-%COMP%] {\\n  background: white;\\n  color: var(--nav-pill-selected-color);\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n  font-weight: 600;\\n}\\n\\n.panel-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.panel-content.hidden[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n\\n.mock-content[_ngcontent-%COMP%] {\\n  padding: 24px;\\n  height: 100%;\\n  overflow-y: auto;\\n}\\n.mock-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: var(--color-text-primary);\\n}\\n.mock-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 24px 0;\\n  color: var(--color-text-secondary);\\n  font-size: 14px;\\n  line-height: 1.5;\\n}\\n\\n.blueprint-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n}\\n\\n.blueprint-header[_ngcontent-%COMP%] {\\n  padding: 10px;\\n}\\n.blueprint-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: black;\\n  margin: 0 0 5px 0;\\n  font-size: 18px;\\n  font-weight: 600;\\n  text-align: center;\\n}\\n.blueprint-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--color-text-secondary);\\n  margin: 0;\\n  font-size: 14px;\\n}\\n\\n.blueprint-canvas-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow: auto;\\n  padding: 10px;\\n  background: var(--color-background-primary);\\n}\\n\\n.custom-blueprint-container[_ngcontent-%COMP%] {\\n  height: 100%;\\n  width: 100%;\\n  min-height: 500px;\\n  background-color: #f8f9fa;\\n  background-image: radial-gradient(circle, #d1d5db 1px, transparent 1px);\\n  background-size: 20px 20px;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  border: 1px solid #d1d3d8;\\n  position: relative;\\n  padding: 5%;\\n  border-radius: 10px;\\n}\\n\\n.central-progress[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  z-index: 10;\\n  background-color: white;\\n  border-radius: 50%;\\n  padding: 8px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-ring[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-circle[_ngcontent-%COMP%] {\\n  transform: rotate(-90deg);\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-background[_ngcontent-%COMP%] {\\n  opacity: 0.15;\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  transition: stroke-dashoffset 0.3s ease;\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-content[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  text-align: center;\\n  color: #374151;\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-percentage[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  line-height: 1;\\n}\\n.central-progress[_ngcontent-%COMP%]   .progress-label[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #6b7280;\\n  margin-top: 2px;\\n}\\n\\n#parent-box[_ngcontent-%COMP%] {\\n  height: 100%;\\n  width: 100%;\\n  gap: 2rem;\\n  position: relative;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.blueprint-zones-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 80%;\\n  height: 80%;\\n  max-width: 800px;\\n  max-height: 600px;\\n}\\n\\n#box1[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: space-between;\\n  height: 300px;\\n  position: relative;\\n  border: 1px solid #bbbec5;\\n  position: fixed;\\n  top: 32%;\\n  left: 40%;\\n  border-left: none;\\n}\\n\\n#box2[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: space-between;\\n  height: 300px;\\n  position: relative;\\n  border: 1px solid #bbbec5;\\n  position: fixed;\\n  top: 32%;\\n  left: 60%;\\n  border-right: none;\\n}\\n\\n.blueprint-zone[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  padding: 16px;\\n  transition: all 0.3s ease;\\n  position: absolute;\\n  width: 280px;\\n  z-index: 5;\\n}\\n.blueprint-zone.has-nodes[_ngcontent-%COMP%] {\\n  border-style: solid;\\n}\\n.blueprint-zone.collapsed[_ngcontent-%COMP%] {\\n  min-height: 40px;\\n}\\n.blueprint-zone.collapsed[_ngcontent-%COMP%]   .zone-header[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n\\n.north-zone[_ngcontent-%COMP%] {\\n  background-color: #ecf0fa;\\n  color: #005eb5;\\n  border: 2px solid #9ab7f6;\\n  top: 0;\\n  left: 0;\\n  overflow: hidden;\\n}\\n\\n.east-zone[_ngcontent-%COMP%] {\\n  background-color: #f2ebfd;\\n  border: 2px solid #d6c2f9;\\n  color: #d6c2f9;\\n  top: 0;\\n  right: 0;\\n  overflow: hidden;\\n}\\n\\n.south-zone[_ngcontent-%COMP%] {\\n  background-color: #fbf6f7;\\n  border: 2px solid #fecaca;\\n  color: #dc2626 !important;\\n  bottom: 0;\\n  right: 0;\\n  overflow: hidden;\\n}\\n.south-zone.has-nodes[_ngcontent-%COMP%] {\\n  height: auto;\\n  min-height: 100px;\\n  max-height: 200px;\\n}\\n.south-zone.has-nodes[_ngcontent-%COMP%]:hover {\\n  overflow-y: auto;\\n  scrollbar-width: none !important;\\n}\\n\\n.west-zone[_ngcontent-%COMP%] {\\n  background-color: #ecf8f4;\\n  border: 2px solid #a9e1cc;\\n  color: #25684f;\\n  bottom: 0;\\n  left: 0;\\n  overflow: hidden;\\n}\\n.west-zone.has-nodes[_ngcontent-%COMP%] {\\n  height: auto;\\n  min-height: 100px;\\n  max-height: 200px;\\n}\\n.west-zone.has-nodes[_ngcontent-%COMP%]:hover {\\n  overflow-y: auto;\\n  scrollbar-width: none !important;\\n}\\n\\n.connection-lines[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  pointer-events: none;\\n  z-index: 1;\\n}\\n.connection-lines[_ngcontent-%COMP%]   line[_ngcontent-%COMP%] {\\n  opacity: 0.7;\\n  transition: opacity 0.3s ease;\\n}\\n.connection-lines[_ngcontent-%COMP%]   line[_ngcontent-%COMP%]:hover {\\n  opacity: 1;\\n}\\n\\n.central-progress[_ngcontent-%COMP%] {\\n  z-index: 10;\\n}\\n\\n.blueprint-zone[_ngcontent-%COMP%] {\\n  z-index: 5;\\n}\\n\\n.zone-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin-bottom: 16px;\\n  padding-bottom: 12px;\\n  cursor: pointer;\\n  -webkit-user-select: none;\\n          user-select: none;\\n  transition: all 0.2s ease;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 8px;\\n  background-color: rgba(59, 130, 246, 0.1);\\n  color: #3b82f6;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .zone-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #374151;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .accordion-toggle[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 4px;\\n  border-radius: 4px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.2s ease;\\n  color: #6b7280;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .accordion-toggle[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 0, 0, 0.05);\\n  color: #374151;\\n}\\n.zone-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .accordion-toggle[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  transition: transform 0.3s ease;\\n}\\n\\n.required-badge[_ngcontent-%COMP%] {\\n  background-color: var(--status-error);\\n  color: white;\\n  font-size: 10px;\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n  font-weight: 500;\\n}\\n\\n.optional-badge[_ngcontent-%COMP%] {\\n  background-color: var(--status-warning);\\n  color: white;\\n  font-size: 10px;\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n  font-weight: 500;\\n}\\n\\n.zone-content[_ngcontent-%COMP%] {\\n  min-height: 60px;\\n  flex: 1;\\n  transition: all 0.3s ease-in-out;\\n  opacity: 1;\\n  overflow: hidden;\\n}\\n\\n.empty-state[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: var(--text-secondary);\\n  font-size: 12px;\\n  padding: 20px 0;\\n  font-style: italic;\\n}\\n\\n.nodes-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n}\\n\\n.kanban-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  padding: 12px 16px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n  transition: all 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n}\\n.kanban-card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\\n  transform: translateY(-1px);\\n}\\n.kanban-card[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #374151;\\n  flex: 1;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n.blueprint-placeholder[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n\\n.blueprint-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 16px;\\n  background: var(--color-background-primary);\\n  border: 1px solid var(--color-border-primary);\\n  border-radius: 8px;\\n  transition: all 0.2s ease;\\n}\\n.blueprint-item[_ngcontent-%COMP%]:hover {\\n  border-color: var(--color-border-secondary);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.blueprint-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  width: 32px;\\n  height: 32px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.blueprint-text[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: var(--color-text-primary);\\n}\\n\\n.output-placeholder[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 24px;\\n}\\n\\n.output-section[_ngcontent-%COMP%] {\\n  background: var(--color-background-primary);\\n  border: 1px solid var(--color-border-primary);\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n.output-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 16px 16px 8px 16px;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: var(--color-text-primary);\\n}\\n.output-section[_ngcontent-%COMP%]   .output-meta[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 0 16px 16px 16px;\\n  font-size: 12px;\\n  color: var(--color-text-tertiary);\\n}\\n\\n.output-preview[_ngcontent-%COMP%] {\\n  border-top: 1px solid var(--color-border-primary);\\n}\\n.output-preview[_ngcontent-%COMP%]   .code-block[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  background: var(--color-background-secondary);\\n  font-family: \\\"Monaco\\\", \\\"Menlo\\\", \\\"Ubuntu Mono\\\", monospace;\\n  font-size: 12px;\\n  line-height: 1.6;\\n  color: var(--color-text-primary);\\n  border-bottom: 1px solid var(--color-border-primary);\\n}\\n.output-preview[_ngcontent-%COMP%]   .preview-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 12px 16px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: var(--color-brand-primary);\\n  transition: all 0.2s ease;\\n  width: 100%;\\n  text-align: left;\\n}\\n.output-preview[_ngcontent-%COMP%]   .preview-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--color-background-tertiary);\\n}\\n\\n@media (max-width: 768px) {\\n  .execution-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .playground-column[_ngcontent-%COMP%], \\n   .output-column[_ngcontent-%COMP%] {\\n    flex: none;\\n    height: 50%;\\n  }\\n  .playground-column[_ngcontent-%COMP%] {\\n    border-right: none;\\n    border-bottom: 1px solid var(--color-border-primary);\\n  }\\n}\\n.row[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  margin: 0;\\n}\\n\\n.col-7[_ngcontent-%COMP%] {\\n  flex: 0 0 58.333333%;\\n  max-width: 58.333333%;\\n}\\n\\n.col-5[_ngcontent-%COMP%] {\\n  flex: 0 0 41.666667%;\\n  max-width: 41.666667%;\\n}\\n\\n*[_ngcontent-%COMP%] {\\n  box-sizing: border-box;\\n}\\n\\n[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n  height: 6px;\\n}\\n\\n[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: var(--color-background-secondary);\\n}\\n\\n[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: var(--color-border-primary);\\n  border-radius: 3px;\\n}\\n\\n[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: var(--color-border-secondary);\\n}\\n\\n.activity-placeholder[_ngcontent-%COMP%], \\n.output-placeholder[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n.activity-placeholder[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], \\n.output-placeholder[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: var(--color-text-primary);\\n}\\n.activity-placeholder[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], \\n.output-placeholder[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: var(--color-text-secondary);\\n  font-size: 14px;\\n}\\n\\n.activity-list[_ngcontent-%COMP%], \\n.output-list[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  border: 1px solid var(--color-border-primary);\\n  border-radius: 8px;\\n  padding: 16px;\\n  background: var(--color-background-secondary);\\n}\\n\\n.activity-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n  padding: 12px 0;\\n  border-bottom: 1px solid var(--color-border-primary);\\n}\\n.activity-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.activity-item[_ngcontent-%COMP%]   .timestamp[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: var(--color-text-tertiary);\\n  font-weight: 500;\\n}\\n.activity-item[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--color-text-primary);\\n}\\n\\n.output-item[_ngcontent-%COMP%] {\\n  padding: 12px 0;\\n  border-bottom: 1px solid var(--color-border-primary);\\n}\\n.output-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.output-item[_ngcontent-%COMP%]   .output-content[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--color-text-primary);\\n  line-height: 1.5;\\n}\\n\\n.individual-output[_ngcontent-%COMP%], \\n.collaborative-output[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n.individual-output[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], \\n.collaborative-output[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: var(--color-text-primary);\\n}\\n.individual-output[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], \\n.collaborative-output[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--color-text-secondary);\\n  font-size: 14px;\\n  margin: 0;\\n}\\n\\n.output-content[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.output-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: var(--color-text-primary);\\n}\\n\\n.execution-history-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.no-output-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  height: 100%;\\n}\\n.no-output-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--color-text-secondary);\\n  font-size: 14px;\\n  text-align: center;\\n}\\n\\n.execution-history-list[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding-right: 8px;\\n}\\n.execution-history-list[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.execution-history-list[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: var(--color-background-secondary);\\n  border-radius: 3px;\\n}\\n.execution-history-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: var(--color-border-primary);\\n  border-radius: 3px;\\n}\\n.execution-history-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: var(--color-border-secondary);\\n}\\n\\n.execution-item[_ngcontent-%COMP%] {\\n  background: var(--color-background-secondary);\\n  border: 1px solid var(--color-border-primary);\\n  border-radius: 8px;\\n  margin-bottom: 16px;\\n  overflow: hidden;\\n  transition: all 0.2s ease;\\n}\\n.execution-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.execution-item.success[_ngcontent-%COMP%] {\\n  border-left: 4px solid #10b981;\\n}\\n.execution-item.failed[_ngcontent-%COMP%] {\\n  border-left: 4px solid #ef4444;\\n}\\n.execution-item.pending[_ngcontent-%COMP%] {\\n  border-left: 4px solid #f59e0b;\\n}\\n\\n.execution-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 12px 16px;\\n  background: var(--color-background-primary);\\n  border-bottom: 1px solid var(--color-border-primary);\\n}\\n\\n.agent-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 14px;\\n  color: var(--color-text-primary);\\n}\\n\\n.execution-status[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 600;\\n  padding: 4px 8px;\\n  border-radius: 4px;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.execution-status.success[_ngcontent-%COMP%] {\\n  background: rgba(16, 185, 129, 0.1);\\n  color: #10b981;\\n}\\n.execution-status.failed[_ngcontent-%COMP%] {\\n  background: rgba(239, 68, 68, 0.1);\\n  color: #ef4444;\\n}\\n.execution-status.pending[_ngcontent-%COMP%] {\\n  background: rgba(245, 158, 11, 0.1);\\n  color: #f59e0b;\\n}\\n\\n.execution-details[_ngcontent-%COMP%] {\\n  padding: 12px 16px;\\n  border-bottom: 1px solid var(--color-border-primary);\\n}\\n\\n.user-message[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--color-text-primary);\\n  margin-bottom: 8px;\\n  line-height: 1.4;\\n}\\n.user-message[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: var(--color-text-primary);\\n}\\n\\n.execution-timestamp[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: var(--color-text-tertiary);\\n}\\n\\n.execution-response[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n\\n.response-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 12px 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: var(--color-text-primary);\\n}\\n\\n.response-text[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  line-height: 1.6;\\n  color: var(--color-text-primary);\\n  white-space: pre-wrap;\\n  word-wrap: break-word;\\n  background: var(--color-background-primary);\\n  padding: 12px;\\n  border-radius: 6px;\\n  border: 1px solid var(--color-border-secondary);\\n  max-height: 300px;\\n  overflow-y: auto;\\n}\\n\\n.task-output[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.task-output[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n\\n.task-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 12px 0 12px 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: var(--color-text-primary);\\n}\\n\\n.task-description[_ngcontent-%COMP%], \\n.task-expected[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n  font-size: 14px;\\n  color: var(--color-text-secondary);\\n}\\n.task-description[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%], \\n.task-expected[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: var(--color-text-primary);\\n}\\n\\n.task-content[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  line-height: 1.6;\\n  color: var(--color-text-primary);\\n  white-space: pre-wrap;\\n  word-wrap: break-word;\\n  background: var(--color-background-primary);\\n  padding: 12px;\\n  border-radius: 6px;\\n  border: 1px solid var(--color-border-secondary);\\n  max-height: 300px;\\n  overflow-y: auto;\\n}\\n\\n.execution-error[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #ef4444;\\n  background: rgba(239, 68, 68, 0.1);\\n  padding: 12px;\\n  border-radius: 6px;\\n  border: 1px solid rgba(239, 68, 68, 0.2);\\n}\\n.error-message[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #ef4444;\\n}\\n\\n.output-box[_ngcontent-%COMP%] {\\n  background: var(--color-background-secondary);\\n  border: 1px solid var(--color-border-primary);\\n  border-radius: 8px;\\n  margin-bottom: 16px;\\n}\\n.output-box[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n\\n.output-section[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n.output-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 12px 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: var(--color-text-primary);\\n}\\n.output-section[_ngcontent-%COMP%]   .task-description[_ngcontent-%COMP%], \\n.output-section[_ngcontent-%COMP%]   .task-expected[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n  font-size: 14px;\\n  color: var(--color-text-secondary);\\n}\\n.output-section[_ngcontent-%COMP%]   .task-description[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%], \\n.output-section[_ngcontent-%COMP%]   .task-expected[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: var(--color-text-primary);\\n}\\n\\n.output-text[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  line-height: 1.6;\\n  color: var(--color-text-primary);\\n  white-space: pre-wrap;\\n  word-wrap: break-word;\\n  background: var(--color-background-primary);\\n  padding: 12px;\\n  border-radius: 6px;\\n  border: 1px solid var(--color-border-secondary);\\n  max-height: 400px;\\n  overflow-y: auto;\\n}\\n\\n.tools-zone[_ngcontent-%COMP%] {\\n  border-color: #ff8c00;\\n}\\n.tools-zone[_ngcontent-%COMP%]   .zone-header[_ngcontent-%COMP%] {\\n  border-bottom-color: rgba(255, 140, 0, 0.2);\\n}\\n\\n.guardrails-zone[_ngcontent-%COMP%] {\\n  border-color: #dc2626;\\n}\\n.guardrails-zone[_ngcontent-%COMP%]   .zone-header[_ngcontent-%COMP%] {\\n  border-bottom-color: rgba(220, 38, 38, 0.2);\\n}\\n\\n.output-content[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.loader-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background-color: rgba(255, 255, 255, 0.7);\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  z-index: 1000;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return AgentExecutionComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "Subject", "takeUntil", "switchMap", "finalize", "catchError", "of", "FormsModule", "AgentExecutionPlaygroundComponent", "IconComponent", "CubicalLoadingComponent", "environment", "ExecutionStatus", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "node_r3", "name", "ɵɵtemplate", "AgentExecutionComponent_div_23_div_40_div_1_Template", "AgentExecutionComponent_div_23_div_40_div_3_Template", "ɵɵproperty", "ctx_r1", "blueprintPromptNodes", "length", "node_r4", "AgentExecutionComponent_div_23_div_61_div_1_Template", "AgentExecutionComponent_div_23_div_61_div_3_Template", "blueprintKnowledgeNodes", "node_r5", "AgentExecutionComponent_div_23_div_79_div_1_Template", "AgentExecutionComponent_div_23_div_79_div_3_Template", "blueprintModelNodes", "ɵɵelement", "node_r7", "AgentExecutionComponent_div_23_div_80_div_13_div_1_Template", "AgentExecutionComponent_div_23_div_80_div_13_div_3_Template", "blueprintToolNodes", "ɵɵlistener", "AgentExecutionComponent_div_23_div_80_Template_div_click_1_listener", "ɵɵrestoreView", "_r6", "ɵɵnextContext", "ɵɵresetView", "toggleBlueprintZone", "AgentExecutionComponent_div_23_div_80__svg_svg_4_Template", "AgentExecutionComponent_div_23_div_80_div_13_Template", "ɵɵclassProp", "agentType", "ɵɵstyleProp", "isBlueprintZoneExpanded", "node_r9", "AgentExecutionComponent_div_23_div_81_div_15_div_1_Template", "AgentExecutionComponent_div_23_div_81_div_15_div_3_Template", "blueprintGuardrailNodes", "AgentExecutionComponent_div_23_div_81_Template_div_click_1_listener", "_r8", "AgentExecutionComponent_div_23_div_81_div_15_Template", "AgentExecutionComponent_div_23_Template_div_click_26_listener", "_r1", "AgentExecutionComponent_div_23_div_40_Template", "AgentExecutionComponent_div_23_Template_div_click_42_listener", "AgentExecutionComponent_div_23_div_61_Template", "AgentExecutionComponent_div_23_Template_div_click_63_listener", "AgentExecutionComponent_div_23_div_79_Template", "AgentExecutionComponent_div_23_div_80_Template", "AgentExecutionComponent_div_23_div_81_Template", "blueprintCompletionPercentage", "ɵɵtextInterpolate1", "execution_r10", "response", "choices", "text", "agentResponse", "detail", "taskOutput_r11", "description", "expected_output", "AgentExecutionComponent_div_24_div_6_div_1_div_15_div_1_div_1_Template", "AgentExecutionComponent_div_24_div_6_div_1_div_15_div_1_div_2_Template", "raw", "summary", "j_r12", "AgentExecutionComponent_div_24_div_6_div_1_div_15_div_1_Template", "agent", "tasksOutputs", "AgentExecutionComponent_div_24_div_6_div_1_div_14_Template", "AgentExecutionComponent_div_24_div_6_div_1_div_15_Template", "AgentExecutionComponent_div_24_div_6_div_1_div_16_Template", "status", "<PERSON><PERSON><PERSON>", "ɵɵclassMap", "userMessage", "ɵɵpipeBind2", "timestamp", "AgentExecutionComponent_div_24_div_6_div_1_Template", "executionHistory", "AgentExecutionComponent_div_24_div_3_Template", "AgentExecutionComponent_div_24_div_5_Template", "AgentExecutionComponent_div_24_div_6_Template", "isLoadingOutput", "AgentExecutionComponent", "route", "router", "agentService", "agentPlaygroundService", "tokenStorage", "loaderService", "formBuilder", "toolExecutionService", "navigationTabs", "id", "label", "disabled", "agentId", "agentDetail", "playgroundComp", "activityLogs", "activityProgress", "executionDetails", "isRunning", "notStarted", "chatMessages", "isProcessingChat", "inputText", "agentOutputs", "latestAgentResponse", "agentForm", "executionStartTime", "executionCompleted", "executionId", "enableStreamingLog", "enableLogStreaming", "isExecutionComplete", "progressInterval", "destroy$", "selectedTab", "demoTabs", "errorMsg", "resMessage", "taskMessage", "isJsonValid", "disable<PERSON>hat", "selectedFiles", "agentNodes", "userInputList", "progress", "isLoading", "loaderColor", "inputFieldOrder", "currentInputIndex", "activeTabId", "isLeftPanelCollapsed", "activeRightTab", "currentAgentDetails", "buildAgentNodes", "canvasNodes", "canvasEdges", "selected<PERSON><PERSON><PERSON>", "selectedAgentMode", "selectedUseCaseIdentifier", "agentFilesUploadedData", "agentAttachment", "isAgentPlaygroundLoading", "agentPlaygroundDestroy", "agentChatPayload", "agentCode", "promptOptions", "blueprintZonesExpanded", "prompt", "model", "knowledge", "guardrail", "tool", "constructor", "group", "isConversational", "isUseTemplate", "ngOnInit", "console", "log", "crypto", "randomUUID", "params", "subscribe", "queryParams", "loadAgentData", "from", "ngOnDestroy", "next", "complete", "clearInterval", "onTabChange", "event", "getCollaborativeAgentDetailsById", "handleAgentDataResponse", "error", "getAgentById", "agentData", "agentDetails", "Array", "isArray", "data", "useCaseName", "useCaseCode", "organizationPath", "loadAgentNodes", "mapAgentConfigurationToBlueprint", "handleChatMessage", "message", "showAgentError", "displayMessage", "fileNames", "map", "file", "documentName", "join", "push", "Date", "get", "value", "agentMode", "useCaseIdentifier", "orgPath", "buildOrganizationPath", "agentIdentifier", "processAgentFilesAndSendMessage", "sendAgentMessageToAPI", "setActiveRightTab", "payload", "Number", "user", "getDaUsername", "userInputs", "question", "fileWrapper", "submitAgentExecuteWithFile", "pipe", "res", "handleAgentExecuteResponse", "err", "errorMessage", "lastMessage", "includes", "submitAgentExecute", "onPromptChanged", "String", "onPlaygroundConversationalToggle", "setValue", "onPlaygroundTemplateToggle", "onFilesSelected", "files", "onApprovalRequested", "saveLogs", "exportResults", "section", "handleControlAction", "action", "navigateBack", "navigate", "mode", "editAgent", "navigateToAgentsList", "toggleLeftPanel", "tab", "zoneType", "getMetadataFromNavbar", "outputRaw", "output", "formattedOutput", "replace", "formData", "FormData", "for<PERSON>ach", "fileData", "append", "has", "getFileToContent", "fileResponse", "fileContent", "fileResponses", "sendAgentMessageToAPIWithFiles", "currentChatPayloadLength", "content", "role", "levelId", "generatePrompt", "generatedResponse", "pop", "fileContents", "warn", "Object", "keys", "nodeCounter", "mapIndividualAgentToBlueprint", "mapCollaborativeAgentToBlueprint", "totalNodes", "config", "config<PERSON><PERSON><PERSON>", "useCaseDetails", "type", "category", "categoryIndex", "categoryId", "categoryName", "configItem", "itemIndex", "config<PERSON><PERSON>", "config<PERSON><PERSON><PERSON>", "kbValue", "toString", "kbIds", "split", "trim", "filter", "kbId", "key", "startsWith", "guardrailName", "promptNodes", "modelNodes", "knowledgeNodes", "guardrailNodes", "totalRequired", "currentRequired", "Math", "round", "shouldCreatePromptNode", "goal", "promptNodeName", "substring", "modelReferences", "hasAgentConfigs", "agentConfigs", "agentConfigsContent", "hasTools", "tools", "toolsContent", "hasUserTools", "userTools", "userToolsContent", "modelRef", "modelRefs", "ref", "modelId", "modelDetails", "modelName", "modelDeploymentName", "knowledgeReferences", "knowledgeBaseRef", "kbRefs", "knowledgeBaseId", "knowledgeBase", "kbRef", "collectionName", "indexCollectionName", "kbName", "toolReferences", "userToolReferences", "toolRef", "toolRefs", "toolId", "userToolRef", "userToolRefs", "toolName", "userTool", "userToolId", "userToolName", "toolNodes", "promptCount", "modelCount", "knowledgeCount", "toolCount", "guardrailCount", "isCollaborative", "hasToolNodes", "toolNodeNames", "t", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "AgentServiceService", "i3", "AgentPlaygroundService", "i4", "TokenStorageService", "i5", "LoaderService", "i6", "FormBuilder", "i7", "ToolExecutionService", "selectors", "viewQuery", "AgentExecutionComponent_Query", "rf", "ctx", "AgentExecutionComponent_Template_button_click_3_listener", "AgentExecutionComponent_Template_button_click_10_listener", "AgentExecutionComponent_button_12_Template", "AgentExecutionComponent_Template_app_agent_execution_playground_promptChange_14_listener", "$event", "AgentExecutionComponent_Template_app_agent_execution_playground_messageSent_14_listener", "AgentExecutionComponent_Template_app_agent_execution_playground_conversationalToggle_14_listener", "AgentExecutionComponent_Template_app_agent_execution_playground_templateToggle_14_listener", "AgentExecutionComponent_Template_app_agent_execution_playground_filesSelected_14_listener", "AgentExecutionComponent_Template_app_agent_execution_playground_approvalRequested_14_listener", "AgentExecutionComponent_Template_button_click_18_listener", "AgentExecutionComponent_Template_button_click_20_listener", "AgentExecutionComponent_div_23_Template", "AgentExecutionComponent_div_24_Template", "i8", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\agents\\agent-execution\\agent-execution.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\pages\\agents\\agent-execution\\agent-execution.component.html"], "sourcesContent": ["import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild, OnChanges } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Subject, takeUntil, switchMap, finalize, catchError, of } from 'rxjs';\r\nimport { FormBuilder, FormGroup, FormsModule } from '@angular/forms';\r\n\r\n// Import child components\r\nimport { AgentExecutionPlaygroundComponent } from './components/agent-execution-playground/agent-execution-playground.component';\r\nimport { ChatMessage } from '@shared/components/chat-window/chat-window.component';\r\nimport { IconComponent, TabItem, DropdownOption, CubicalLoadingComponent } from '@ava/play-comp-library';\r\nimport { AgentServiceService } from '../services/agent-service.service';\r\nimport { AgentPlaygroundService } from '../build-agents/services/agent-playground.service';\r\nimport { environment } from '@shared/environments/environment';\r\nimport { TokenStorageService } from '@shared/auth/services/token-storage.service';\r\nimport { LoaderService } from '@shared/services/loader/loader.service';\r\nimport { ToolExecutionService } from '@shared/services/tool-execution/tool-execution.service';\r\nimport { AvaTab } from '@shared/models/tab.model';\r\nimport { ExecutionStatus, ActivityLog, ExecutionDetails, OutputItem } from '@shared/models/execution.model';\r\n\r\n// Remove duplicate definitions - they're now in shared models\r\n\r\n@Component({\r\n  selector: 'app-agent-execution',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    AgentExecutionPlaygroundComponent,\r\n    IconComponent,\r\n    CubicalLoadingComponent,\r\n  ],\r\n  templateUrl: './agent-execution.component.html',\r\n  styleUrls: ['./agent-execution.component.scss'],\r\n})\r\nexport class AgentExecutionComponent implements OnInit, OnDestroy {\r\n  navigationTabs: TabItem[] = [\r\n    { id: 'nav-home', label: 'Agent Activity' },\r\n    { id: 'nav-products', label: 'Agent Output' },\r\n    { id: 'nav-services', label: 'Preview', disabled: true },\r\n  ];\r\n\r\n  // Agent details\r\n  agentId: string | null = null;\r\n  agentType: string = 'individual';\r\n  agentName: string = 'Agent';\r\n  agentDetail: string = '';\r\n\r\n  @ViewChild(AgentExecutionPlaygroundComponent, { static: false })\r\n  playgroundComp!: AgentExecutionPlaygroundComponent;\r\n\r\n  // Activity logs\r\n  activityLogs: ActivityLog[] = [];\r\n  activityProgress: number = 0;\r\n  executionDetails?: ExecutionDetails;\r\n  isRunning: boolean = false;\r\n  status: ExecutionStatus = ExecutionStatus.notStarted;\r\n\r\n  // Chat messages\r\n  chatMessages: ChatMessage[] = [];\r\n  isProcessingChat: boolean = false;\r\n\r\n  inputText = '';\r\n  isLoadingOutput: boolean = false;\r\n\r\n  // Agent outputs\r\n  agentOutputs: OutputItem[] = [];\r\n  latestAgentResponse: any = null; // Store the latest agent response for display\r\n  \r\n  // New properties for execution history\r\n  executionHistory: Array<{\r\n    id: string;\r\n    agentName: string;\r\n    userMessage: string;\r\n    status: 'pending' | 'success' | 'failed';\r\n    response?: any;\r\n    timestamp: Date;\r\n  }> = [];\r\n\r\n  public agentForm!: FormGroup;\r\n\r\n  // Execution state\r\n  executionStartTime: Date | null = null;\r\n  executionCompleted: boolean = false;\r\n  executionId!: string;\r\n\r\n  enableStreamingLog = environment.enableLogStreaming || 'all';\r\n\r\n  public isExecutionComplete: boolean = false;\r\n  progressInterval: any;\r\n\r\n  private destroy$ = new Subject<void>();\r\n  selectedTab: string = 'Agent Activity';\r\n  demoTabs: AvaTab[] = [\r\n    { id: 'tab1', label: 'History' },\r\n    { id: 'tab2', label: 'Blueprint' },\r\n    { id: 'tab3', label: 'Agent Output' },\r\n  ];\r\n\r\n  errorMsg = false;\r\n  resMessage: any;\r\n  taskMessage: any[] = [];\r\n  isJsonValid = false;\r\n  disableChat: boolean = false;\r\n  selectedFiles: File[] = [];\r\n  agentNodes: any[] = [];\r\n  userInputList: any[] = [];\r\n  progress = 0;\r\n  isLoading = false;\r\n  loaderColor: string = '';\r\n\r\n  inputFieldOrder: string[] = [];\r\n  currentInputIndex: number = 0;\r\n  activeTabId: string = 'nav-home';\r\n\r\n  // Panel state properties\r\n  isLeftPanelCollapsed: boolean = false;\r\n  activeRightTab: string = 'blueprint';\r\n\r\n  // Agent-specific properties\r\n  currentAgentDetails: any = null;\r\n  buildAgentNodes: any[] = [];\r\n  canvasNodes: any[] = [];\r\n  canvasEdges: any[] = [];\r\n  selectedPrompt: string = '';\r\n  selectedAgentMode: string = '';\r\n  selectedUseCaseIdentifier: string = '';\r\n  agentFilesUploadedData: any[] = [];\r\n  agentAttachment: string[] = [];\r\n  isAgentPlaygroundLoading = false;\r\n  agentPlaygroundDestroy = new Subject<boolean>();\r\n  agentChatPayload: any[] = [];\r\n  agentCode: string = '';\r\n  promptOptions: DropdownOption[] = [];\r\n\r\n  // Custom Blueprint Display Properties\r\n  blueprintCompletionPercentage: number = 0;\r\n  blueprintPromptNodes: any[] = [];\r\n  blueprintModelNodes: any[] = [];\r\n  blueprintKnowledgeNodes: any[] = [];\r\n  blueprintGuardrailNodes: any[] = [];\r\n  blueprintToolNodes: any[] = [];\r\n\r\n  // Blueprint zone expansion state\r\n  private blueprintZonesExpanded: { [key: string]: boolean } = {\r\n    prompt: true,\r\n    model: true,\r\n    knowledge: true,\r\n    guardrail: true,\r\n    tool: true,\r\n  };\r\n\r\n  // Blueprint panel properties (using existing arrays above)\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private agentService: AgentServiceService,\r\n    private agentPlaygroundService: AgentPlaygroundService,\r\n    private tokenStorage: TokenStorageService,\r\n    private loaderService: LoaderService,\r\n    private formBuilder: FormBuilder,\r\n    private toolExecutionService: ToolExecutionService,\r\n  ) {\r\n    this.agentForm = this.formBuilder.group({\r\n      isConversational: [true],\r\n      isUseTemplate: [false],\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    console.log('🌟 SHARED COMPONENT INITIALIZED! 🌟');\r\n    this.executionId = crypto.randomUUID();\r\n\r\n    this.route.params.subscribe((params) => {\r\n      this.agentType = params['type'] || 'individual';\r\n      console.log('🌟 SHARED: Agent type set to:', this.agentType);\r\n    });\r\n\r\n    this.route.queryParams.subscribe((params) => {\r\n      if (params['id']) {\r\n        this.agentId = params['id'];\r\n        this.loadAgentData(params['id']);\r\n      }\r\n    });\r\n\r\n    // Initialize chat messages\r\n    this.chatMessages = [\r\n      {\r\n        from: 'ai',\r\n        text: `Hi there! I am ${this.agentName || 'your agent'}. How can I help you today?`,\r\n      },\r\n    ];\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n    if (this.progressInterval) {\r\n      clearInterval(this.progressInterval);\r\n    }\r\n  }\r\n\r\n  onTabChange(event: { id: string; label: string }) {\r\n    this.activeTabId = event.id;\r\n    this.selectedTab = event.label;\r\n  }\r\n\r\n  loadAgentData(agentId: string): void {\r\n    this.isLoading = true;\r\n\r\n    // Load agent data based on type\r\n    if (this.agentType === 'collaborative') {\r\n      this.agentService.getCollaborativeAgentDetailsById(agentId).subscribe({\r\n        next: (response: any) => {\r\n          this.handleAgentDataResponse(response);\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error loading collaborative agent:', error);\r\n          this.isLoading = false;\r\n        },\r\n      });\r\n    } else {\r\n      this.agentService.getAgentById(agentId).subscribe({\r\n        next: (response: any) => {\r\n          this.handleAgentDataResponse(response);\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error loading individual agent:', error);\r\n          this.isLoading = false;\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  private handleAgentDataResponse(response: any): void {\r\n    this.isLoading = false;\r\n\r\n    // Extract agent details\r\n    let agentData;\r\n    if (\r\n      response.agentDetails &&\r\n      Array.isArray(response.agentDetails) &&\r\n      response.agentDetails.length > 0\r\n    ) {\r\n      agentData = response.agentDetails[0];\r\n    } else if (response.agentDetail) {\r\n      agentData = response.agentDetail;\r\n    } else if (response.data) {\r\n      agentData = response.data;\r\n    } else {\r\n      agentData = response;\r\n    }\r\n\r\n    if (agentData) {\r\n      this.currentAgentDetails = agentData;\r\n      // For individual agents, use useCaseName as the agent name\r\n      if (this.agentType === 'individual') {\r\n        this.agentName = agentData.useCaseName || agentData.name || agentData.agentName || 'Agent';\r\n      } else {\r\n        this.agentName = agentData.name || agentData.agentName || 'Agent';\r\n      }\r\n      this.agentDetail = agentData.description || agentData.agentDetail || '';\r\n\r\n      // For individual agents, set up the required properties for playground functionality\r\n      if (this.agentType === 'individual') {\r\n        // Set selectedPrompt to agent name or useCaseName to bypass dropdown requirement\r\n        this.selectedPrompt =\r\n          agentData.useCaseName || agentData.name || 'loaded-agent';\r\n\r\n        // Set selectedAgentMode for API calls - use useCaseCode if available\r\n        this.selectedAgentMode =\r\n          agentData.useCaseCode ||\r\n          agentData.useCaseName ||\r\n          agentData.name ||\r\n          '';\r\n\r\n        // Set useCaseIdentifier - use organizationPath if available\r\n        if (agentData.organizationPath) {\r\n          this.selectedUseCaseIdentifier = agentData.organizationPath;\r\n        } else if (agentData.useCaseCode) {\r\n          this.selectedUseCaseIdentifier = agentData.useCaseCode;\r\n        } else if (agentData.useCaseName) {\r\n          this.selectedUseCaseIdentifier = agentData.useCaseName;\r\n        }\r\n      }\r\n\r\n      // Update chat message with agent name\r\n      if (this.chatMessages.length > 0) {\r\n        this.chatMessages[0].text = `Hi there! I am ${this.agentName}. How can I help you today?`;\r\n      }\r\n\r\n      // Load agent nodes and configuration\r\n      this.loadAgentNodes(agentData);\r\n    }\r\n  }\r\n\r\n  private loadAgentNodes(agentData: any): void {\r\n    // Map agent configuration to blueprint panel\r\n    this.mapAgentConfigurationToBlueprint(agentData);\r\n  }\r\n\r\n  handleChatMessage(message: string): void {\r\n    if (this.agentType === 'individual') {\r\n      // For individual agents, use the loaded agent details instead of requiring dropdown selection\r\n      if (\r\n        !this.currentAgentDetails &&\r\n        (!this.selectedPrompt || this.selectedPrompt === 'default')\r\n      ) {\r\n        this.showAgentError(\r\n          'Agent details are not loaded. Please try refreshing the page.',\r\n        );\r\n        return;\r\n      }\r\n\r\n      let displayMessage = message;\r\n      if (this.agentFilesUploadedData.length > 0) {\r\n        const fileNames = this.agentFilesUploadedData\r\n          .map((file) => file.documentName)\r\n          .join(', ');\r\n        displayMessage = `${message}\\n\\n📎 Attached files: ${fileNames}`;\r\n      }\r\n\r\n      // Add to execution history\r\n      const executionId = crypto.randomUUID();\r\n      this.executionHistory.push({\r\n        id: executionId,\r\n        agentName: this.agentName,\r\n        userMessage: message,\r\n        status: 'pending', // Will be updated based on API response\r\n        timestamp: new Date()\r\n      });\r\n      console.log('Added new execution to history:', this.executionHistory);\r\n\r\n      this.chatMessages = [\r\n        ...this.chatMessages,\r\n        { from: 'user', text: displayMessage },\r\n      ];\r\n      this.isProcessingChat = true;\r\n\r\n      const isConversational =\r\n        this.agentForm.get('isConversational')?.value || false;\r\n      const isUseTemplate = this.agentForm.get('isUseTemplate')?.value || false;\r\n\r\n      console.log(\r\n        'Chat message handling - isConversational:',\r\n        isConversational,\r\n        'isUseTemplate:',\r\n        isUseTemplate,\r\n      );\r\n\r\n      // Use agent details from the loaded agent data instead of dropdown selection\r\n      // Mode should be the useCaseCode, not useCaseName\r\n      const agentMode =\r\n        this.agentCode ||\r\n        this.selectedAgentMode ||\r\n        this.currentAgentDetails?.useCaseCode ||\r\n        this.currentAgentDetails?.useCaseName ||\r\n        this.currentAgentDetails?.name ||\r\n        this.selectedPrompt;\r\n\r\n      let useCaseIdentifier = this.selectedUseCaseIdentifier;\r\n      if (!useCaseIdentifier) {\r\n        // Use organizationPath if available, otherwise build from agent details\r\n        if (this.currentAgentDetails?.organizationPath) {\r\n          useCaseIdentifier = this.currentAgentDetails.organizationPath;\r\n        } else {\r\n          const orgPath = this.buildOrganizationPath();\r\n          const agentIdentifier =\r\n            this.currentAgentDetails?.useCaseCode ||\r\n            this.currentAgentDetails?.useCaseName ||\r\n            this.currentAgentDetails?.name ||\r\n            agentMode;\r\n          useCaseIdentifier = `${agentIdentifier}${orgPath}`;\r\n        }\r\n      }\r\n\r\n      if (this.agentFilesUploadedData.length > 0) {\r\n        this.processAgentFilesAndSendMessage(\r\n          message,\r\n          agentMode,\r\n          useCaseIdentifier,\r\n          isConversational,\r\n          isUseTemplate,\r\n        );\r\n        return;\r\n      }\r\n\r\n      this.sendAgentMessageToAPI(\r\n        message,\r\n        agentMode,\r\n        useCaseIdentifier,\r\n        isConversational,\r\n        isUseTemplate,\r\n      );\r\n    } else if (this.agentType === 'collaborative') {\r\n      // Add to execution history for collaborative agents too\r\n      const executionId = crypto.randomUUID();\r\n      this.executionHistory.push({\r\n        id: executionId,\r\n        agentName: this.agentName,\r\n        userMessage: message,\r\n        status: 'pending', // Will be updated based on API response\r\n        timestamp: new Date()\r\n      });\r\n      console.log('Added new collaborative execution to history:', this.executionHistory);\r\n\r\n      this.isProcessingChat = true;\r\n      // Set loading state for output section\r\n      this.isLoadingOutput = true;\r\n      this.setActiveRightTab('output');\r\n      \r\n      let payload = {\r\n        executionId: this.executionId,\r\n        agentId: Number(this.agentId),\r\n        user: this.tokenStorage.getDaUsername() || '<EMAIL>',\r\n        userInputs: { question: message },\r\n      };\r\n\r\n      // Add user message to chat for collaborative agents\r\n      this.chatMessages = [\r\n        ...this.chatMessages,\r\n        { from: 'user', text: message },\r\n      ];\r\n\r\n      if (this.agentFilesUploadedData.length > 0) {\r\n        const fileWrapper = this.agentFilesUploadedData[0];\r\n        let displayMessage: string;\r\n        if (this.agentFilesUploadedData.length > 0) {\r\n          const fileNames = this.agentFilesUploadedData\r\n            .map((file) => file.documentName)\r\n            .join(', ');\r\n          displayMessage = `📎 Attached files: ${fileNames}`;\r\n\r\n          this.chatMessages = [{ from: 'user', text: displayMessage }];\r\n        }\r\n        this.agentPlaygroundService\r\n          .submitAgentExecuteWithFile(payload, fileWrapper)\r\n          .pipe(\r\n            finalize(() => {\r\n              this.isProcessingChat = false;\r\n              this.isAgentPlaygroundLoading = false;\r\n              this.isLoadingOutput = false;\r\n            }),\r\n            takeUntil(this.agentPlaygroundDestroy),\r\n          )\r\n          .subscribe({\r\n            next: (res) => this.handleAgentExecuteResponse(res, message),\r\n            error: (err: any) => {\r\n              // Update execution history with failed status\r\n              if (this.executionHistory.length > 0) {\r\n                this.executionHistory[this.executionHistory.length - 1].status = 'failed';\r\n              }\r\n              \r\n              const errorMessage =\r\n                err?.error?.message ||\r\n                err?.message ||\r\n                'An error occurred while processing your request.';\r\n              \r\n              // Update the last chat message with the final status\r\n              if (this.chatMessages.length > 0) {\r\n                const lastMessage = this.chatMessages[this.chatMessages.length - 1];\r\n                if (lastMessage.from === 'ai' && lastMessage.text.includes('Status: Pending')) {\r\n                  lastMessage.text = `${this.agentName} - Status: Failed`;\r\n                }\r\n              }\r\n              \r\n              // Add error message to chat\r\n              this.chatMessages = [\r\n                ...this.chatMessages,\r\n                { from: 'ai', text: `${this.agentName} - Status: Failed` },\r\n              ];\r\n              \r\n              // Switch to output tab even on failure\r\n              this.setActiveRightTab('output');\r\n            },\r\n          });\r\n      } else {\r\n        this.agentPlaygroundService\r\n          .submitAgentExecute(payload)\r\n          .pipe(\r\n            finalize(() => {\r\n              this.isProcessingChat = false;\r\n              this.isAgentPlaygroundLoading = false;\r\n              this.isLoadingOutput = false;\r\n            }),\r\n            takeUntil(this.agentPlaygroundDestroy),\r\n          )\r\n          .subscribe({\r\n            next: (res) => this.handleAgentExecuteResponse(res, message),\r\n            error: (err: any) => {\r\n              // Update execution history with failed status\r\n              if (this.executionHistory.length > 0) {\r\n                this.executionHistory[this.executionHistory.length - 1].status = 'failed';\r\n              }\r\n              \r\n              const errorMessage =\r\n                err?.error?.message ||\r\n                err?.message ||\r\n                'An error occurred while processing your request.';\r\n              \r\n              // Update the last chat message with the final status\r\n              if (this.chatMessages.length > 0) {\r\n                const lastMessage = this.chatMessages[this.chatMessages.length - 1];\r\n                if (lastMessage.from === 'ai' && lastMessage.text.includes('Status: Pending')) {\r\n                  lastMessage.text = `${this.agentName} - Status: Failed`;\r\n                }\r\n              }\r\n              \r\n              // Add error message to chat\r\n              this.chatMessages = [\r\n                ...this.chatMessages,\r\n                { from: 'ai', text: `${this.agentName} - Status: Failed` },\r\n              ];\r\n              \r\n              // Switch to output tab even on failure\r\n              this.setActiveRightTab('output');\r\n            },\r\n          });\r\n      }\r\n    }\r\n  }\r\n\r\n  onPromptChanged(prompt: DropdownOption): void {\r\n    this.inputText = prompt.name || String(prompt.value) || '';\r\n  }\r\n\r\n  onPlaygroundConversationalToggle(value: boolean): void {\r\n    // Update the form control\r\n    this.agentForm.get('isConversational')?.setValue(value);\r\n\r\n    // When conversational mode is turned off, clear the conversation history\r\n    // This ensures that the next message will be treated as a fresh start\r\n    if (!value) {\r\n      this.agentChatPayload = [];\r\n      console.log(\r\n        'Conversational mode disabled - cleared chat payload history',\r\n      );\r\n    } else {\r\n      console.log('Conversational mode enabled - will maintain chat history');\r\n    }\r\n  }\r\n\r\n  onPlaygroundTemplateToggle(value: boolean): void {\r\n    // Update the form control\r\n    this.agentForm.get('isUseTemplate')?.setValue(value);\r\n    console.log('Template mode toggled:', value);\r\n  }\r\n\r\n  onFilesSelected(files: any[]): void {\r\n    this.selectedFiles = files;\r\n    // Update agentFilesUploadedData for agent execution\r\n    this.agentFilesUploadedData = files;\r\n  }\r\n\r\n  onApprovalRequested(): void {\r\n    // Handle approval request\r\n  }\r\n\r\n  saveLogs(): void {\r\n    // Save execution logs\r\n  }\r\n\r\n  exportResults(section: 'activity' | 'output'): void {\r\n    // Export results\r\n  }\r\n\r\n  handleControlAction(action: 'play' | 'pause' | 'stop'): void {\r\n    // Handle execution control actions\r\n  }\r\n\r\n  navigateBack(): void {\r\n    this.router.navigate(['/build/agents', this.agentType], {\r\n      queryParams: { id: this.agentId, mode: 'view' },\r\n    });\r\n  }\r\n\r\n  editAgent(): void {\r\n    this.router.navigate(['/build/agents', this.agentType], {\r\n      queryParams: { id: this.agentId, mode: 'edit' },\r\n    });\r\n  }\r\n\r\n  navigateToAgentsList(): void {\r\n    this.router.navigate(['/build/agents']);\r\n  }\r\n\r\n  toggleLeftPanel(): void {\r\n    this.isLeftPanelCollapsed = !this.isLeftPanelCollapsed;\r\n  }\r\n\r\n  setActiveRightTab(tab: string): void {\r\n    this.activeRightTab = tab;\r\n  }\r\n\r\n  // Blueprint zone management methods\r\n  toggleBlueprintZone(zoneType: string): void {\r\n    this.blueprintZonesExpanded[zoneType] =\r\n      !this.blueprintZonesExpanded[zoneType];\r\n  }\r\n\r\n  isBlueprintZoneExpanded(zoneType: string): boolean {\r\n    return this.blueprintZonesExpanded[zoneType] || false;\r\n  }\r\n\r\n  // API and helper methods from build-agents component\r\n  private showAgentError(message: string): void {\r\n    this.chatMessages = [...this.chatMessages, { from: 'ai', text: message }];\r\n  }\r\n\r\n  private buildOrganizationPath(): string {\r\n    // Simple implementation - in real scenario this would be from navbar/metadata\r\n    return '';\r\n  }\r\n\r\n  private getMetadataFromNavbar(): { levelId?: number } {\r\n    // Simple implementation - in real scenario this would get org level mapping\r\n    return {};\r\n  }\r\n\r\n  handleAgentExecuteResponse(response: any, message: string): void {\r\n    try {\r\n      // Update execution history with success status\r\n      if (this.executionHistory.length > 0) {\r\n        this.executionHistory[this.executionHistory.length - 1].status = 'success';\r\n        this.executionHistory[this.executionHistory.length - 1].response = response;\r\n        console.log('Updated execution history:', this.executionHistory);\r\n      }\r\n\r\n      const outputRaw = response?.agentResponse?.agent?.output;\r\n      let formattedOutput = '';\r\n\r\n      if (outputRaw) {\r\n        // Directly replace escaped \\n with real newlines\r\n        formattedOutput = outputRaw.replace(/\\\\n/g, '\\n');\r\n      } else {\r\n        formattedOutput = response?.agentResponse?.detail;\r\n      }\r\n\r\n      // Add success status message to chat\r\n      this.chatMessages = [\r\n        ...this.chatMessages,\r\n        { from: 'ai', text: `${this.agentName} - Status: Success` }\r\n      ];\r\n      \r\n      // Switch to output tab\r\n      this.setActiveRightTab('output');\r\n    } catch (err: any) {\r\n      // Update execution history with failed status\r\n      if (this.executionHistory.length > 0) {\r\n        this.executionHistory[this.executionHistory.length - 1].status = 'failed';\r\n        console.log('Updated execution history (failed):', this.executionHistory);\r\n      }\r\n\r\n      // Add failure status message to chat\r\n      this.chatMessages = [\r\n        ...this.chatMessages,\r\n        { from: 'ai', text: `${this.agentName} - Status: Failed` }\r\n      ];\r\n      \r\n      // Switch to output tab even on failure\r\n      this.setActiveRightTab('output');\r\n    }\r\n  }\r\n\r\n  private processAgentFilesAndSendMessage(\r\n    message: string,\r\n    mode: string,\r\n    useCaseIdentifier: string,\r\n    isConversational: boolean,\r\n    isUseTemplate: boolean,\r\n  ): void {\r\n    const formData = new FormData();\r\n    this.agentFilesUploadedData.forEach((fileData) => {\r\n      if (fileData.file) {\r\n        formData.append('files', fileData.file);\r\n      }\r\n    });\r\n\r\n    if (formData.has('files')) {\r\n      this.agentPlaygroundService\r\n        .getFileToContent(formData)\r\n        .pipe(\r\n          switchMap((fileResponse) => {\r\n            const fileContent =\r\n              fileResponse?.fileResponses\r\n                ?.map((response: any) => response.fileContent)\r\n                ?.join('\\n') || '';\r\n            this.sendAgentMessageToAPIWithFiles(\r\n              message,\r\n              mode,\r\n              useCaseIdentifier,\r\n              isConversational,\r\n              isUseTemplate,\r\n              fileContent,\r\n            );\r\n            return of(null);\r\n          }),\r\n          catchError((error) => {\r\n            console.error('Error parsing files:', error);\r\n            this.sendAgentMessageToAPI(\r\n              message,\r\n              mode,\r\n              useCaseIdentifier,\r\n              isConversational,\r\n              isUseTemplate,\r\n            );\r\n            return of(null);\r\n          }),\r\n        )\r\n        .subscribe();\r\n    } else {\r\n      this.sendAgentMessageToAPI(\r\n        message,\r\n        mode,\r\n        useCaseIdentifier,\r\n        isConversational,\r\n        isUseTemplate,\r\n      );\r\n    }\r\n  }\r\n\r\n  private sendAgentMessageToAPI(\r\n    message: string,\r\n    mode: string,\r\n    useCaseIdentifier: string,\r\n    isConversational: boolean,\r\n    isUseTemplate: boolean,\r\n  ): void {\r\n    console.log('API Call Parameters:', {\r\n      message,\r\n      mode,\r\n      useCaseIdentifier,\r\n      isConversational,\r\n      isUseTemplate,\r\n      currentChatPayloadLength: this.agentChatPayload.length,\r\n    });\r\n\r\n    if (isConversational) {\r\n      this.agentChatPayload.push({ content: message, role: 'user' });\r\n    }\r\n\r\n    const payload = isConversational ? this.agentChatPayload : message;\r\n    const { levelId } = this.getMetadataFromNavbar();\r\n\r\n    console.log('Final payload being sent:', payload);\r\n\r\n    // Set loading state for output section\r\n    this.isLoadingOutput = true;\r\n    this.setActiveRightTab('output');\r\n\r\n    this.agentPlaygroundService\r\n      .generatePrompt(\r\n        payload,\r\n        mode,\r\n        isConversational,\r\n        isUseTemplate,\r\n        this.agentAttachment,\r\n        useCaseIdentifier,\r\n        '',\r\n        levelId,\r\n      )\r\n      .pipe(\r\n        finalize(() => {\r\n          this.isProcessingChat = false;\r\n          this.isAgentPlaygroundLoading = false;\r\n          this.isLoadingOutput = false;\r\n        }),\r\n        takeUntil(this.agentPlaygroundDestroy),\r\n      )\r\n      .subscribe({\r\n        next: (generatedResponse: any) => {\r\n          // Call handleAgentExecuteResponse to properly handle the response\r\n          this.handleAgentExecuteResponse(generatedResponse, message);\r\n        },\r\n        error: (error: any) => {\r\n          console.error('API Error:', error);\r\n          \r\n          // Update execution history with failed status\r\n          if (this.executionHistory.length > 0) {\r\n            this.executionHistory[this.executionHistory.length - 1].status = 'failed';\r\n          }\r\n          \r\n          const errorMessage =\r\n            error?.error?.message ||\r\n            error?.message ||\r\n            'An error occurred while processing your request.';\r\n          \r\n          // Add failure status message to chat\r\n          this.chatMessages = [\r\n            ...this.chatMessages,\r\n            { from: 'ai', text: `${this.agentName} - Status: Failed` },\r\n            { from: 'ai', text: errorMessage }\r\n          ];\r\n          \r\n          // Switch to output tab even on failure\r\n          this.setActiveRightTab('output');\r\n          \r\n          if (isConversational && this.agentChatPayload.length > 0) {\r\n            this.agentChatPayload.pop();\r\n          }\r\n        },\r\n      });\r\n  }\r\n\r\n  private sendAgentMessageToAPIWithFiles(\r\n    message: string,\r\n    mode: string,\r\n    useCaseIdentifier: string,\r\n    isConversational: boolean,\r\n    isUseTemplate: boolean,\r\n    fileContents: string,\r\n  ): void {\r\n    if (isConversational) {\r\n      this.agentChatPayload.push({ content: message, role: 'user' });\r\n    }\r\n    const payload = isConversational ? this.agentChatPayload : message;\r\n    const { levelId } = this.getMetadataFromNavbar();\r\n\r\n    // Set loading state for output section\r\n    this.isLoadingOutput = true;\r\n    this.setActiveRightTab('output');\r\n\r\n    this.agentPlaygroundService\r\n      .generatePrompt(\r\n        payload,\r\n        mode,\r\n        isConversational,\r\n        isUseTemplate,\r\n        this.agentAttachment,\r\n        useCaseIdentifier,\r\n        fileContents,\r\n        levelId,\r\n      )\r\n      .pipe(\r\n        finalize(() => {\r\n          this.isProcessingChat = false;\r\n          this.isAgentPlaygroundLoading = false;\r\n          this.isLoadingOutput = false;\r\n        }),\r\n        takeUntil(this.agentPlaygroundDestroy),\r\n      )\r\n      .subscribe({\r\n        next: (generatedResponse: any) => {\r\n          // Call handleAgentExecuteResponse to properly handle the response\r\n          this.handleAgentExecuteResponse(generatedResponse, message);\r\n        },\r\n        error: (error: any) => {\r\n          console.error('API Error:', error);\r\n          const errorMessage =\r\n            error?.error?.message ||\r\n            error?.message ||\r\n            'An error occurred while processing your request.';\r\n          \r\n          // Add failure status message to chat\r\n          this.chatMessages = [\r\n            ...this.chatMessages,\r\n            { from: 'ai', text: `${this.agentName} - Status: Failed` },\r\n            { from: 'ai', text: errorMessage }\r\n          ];\r\n          \r\n          if (isConversational && this.agentChatPayload.length > 0) {\r\n            this.agentChatPayload.pop();\r\n          }\r\n        },\r\n      });\r\n  }\r\n\r\n  // Blueprint panel methods\r\n  private mapAgentConfigurationToBlueprint(agentData: any): void {\r\n    if (!agentData) {\r\n      console.warn('No agent data provided for blueprint');\r\n      return;\r\n    }\r\n\r\n    console.log('🔍 DEBUG: Full agent data received:', agentData);\r\n    console.log('🔍 DEBUG: Agent type:', this.agentType);\r\n    console.log('🔍 DEBUG: Agent data keys:', Object.keys(agentData));\r\n\r\n    // Clear existing nodes\r\n    this.buildAgentNodes = [];\r\n    this.canvasNodes = [];\r\n\r\n    let nodeCounter = 1;\r\n\r\n    // Map agent configuration to nodes based on agent type\r\n    if (this.agentType === 'individual') {\r\n      this.mapIndividualAgentToBlueprint(agentData, nodeCounter);\r\n    } else if (this.agentType === 'collaborative') {\r\n      this.mapCollaborativeAgentToBlueprint(agentData, nodeCounter);\r\n    }\r\n\r\n    console.log('🎯 Blueprint nodes mapped:', {\r\n      buildAgentNodes: this.buildAgentNodes,\r\n      canvasNodes: this.canvasNodes,\r\n      totalNodes: this.buildAgentNodes.length,\r\n    });\r\n  }\r\n\r\n  private mapIndividualAgentToBlueprint(\r\n    agentData: any,\r\n    nodeCounter: number,\r\n  ): void {\r\n    console.log('🔍 Individual agent mapping - checking fields:', {\r\n      config: agentData.config,\r\n      configLength: agentData.config?.length,\r\n      useCaseName: agentData.useCaseName,\r\n      prompt: agentData.prompt,\r\n      useCaseDetails: agentData.useCaseDetails,\r\n    });\r\n\r\n    // Clear existing blueprint nodes\r\n    this.blueprintPromptNodes = [];\r\n    this.blueprintModelNodes = [];\r\n    this.blueprintKnowledgeNodes = [];\r\n    this.blueprintGuardrailNodes = [];\r\n\r\n    // Add prompt node from \"prompt\" field\r\n    if (agentData.prompt) {\r\n      this.blueprintPromptNodes.push({\r\n        id: `prompt-${nodeCounter++}`,\r\n        name: agentData.prompt,\r\n        type: 'prompt',\r\n      });\r\n      console.log('✅ Added prompt node:', agentData.prompt);\r\n    }\r\n\r\n    // Process the config array to extract model, knowledge bases, and guardrails\r\n    if (agentData.config && Array.isArray(agentData.config)) {\r\n      console.log(\r\n        '🔍 Processing config array with length:',\r\n        agentData.config.length,\r\n      );\r\n\r\n      agentData.config.forEach((category: any, categoryIndex: number) => {\r\n        console.log(\r\n          `🔍 Category ${categoryIndex} (ID: ${category.categoryId}):`,\r\n          category.categoryName,\r\n        );\r\n\r\n        if (category.config && Array.isArray(category.config)) {\r\n          console.log(\r\n            `🔍 Category ${categoryIndex} has ${category.config.length} config items`,\r\n          );\r\n\r\n          category.config.forEach((configItem: any, itemIndex: number) => {\r\n            console.log(`🔍 Config item ${categoryIndex}-${itemIndex}:`, {\r\n              configKey: configItem.configKey,\r\n              configValue: configItem.configValue,\r\n              categoryId: configItem.categoryId,\r\n            });\r\n\r\n            // Handle AI Model from categoryId 1\r\n            if (\r\n              configItem.categoryId === 1 &&\r\n              configItem.configKey === 'MODEL' &&\r\n              configItem.configValue\r\n            ) {\r\n              console.log(\r\n                '✅ Adding AI model node from categoryId 1:',\r\n                configItem.configValue,\r\n              );\r\n              this.blueprintModelNodes.push({\r\n                id: `model-${nodeCounter++}`,\r\n                name: `${configItem.configKey}`,\r\n                type: 'model',\r\n              });\r\n            }\r\n\r\n            // Handle Knowledge Base from categoryId 2\r\n            if (\r\n              configItem.categoryId === 2 &&\r\n              configItem.configKey === 'RAG_KNOWLEDGEBASE_NAME' &&\r\n              configItem.configValue\r\n            ) {\r\n              console.log(\r\n                '✅ Adding knowledge base nodes from categoryId 2:',\r\n                configItem.configValue,\r\n              );\r\n              const kbValue = configItem.configValue.toString();\r\n              const kbIds = kbValue\r\n                .split(',')\r\n                .map((id: string) => id.trim())\r\n                .filter((id: string) => id);\r\n\r\n              kbIds.forEach((kbId: string) => {\r\n                this.blueprintKnowledgeNodes.push({\r\n                  id: `knowledge-${nodeCounter++}`,\r\n                  name: `Knowledge Base: ${kbId}`,\r\n                  type: 'knowledge',\r\n                });\r\n              });\r\n            }\r\n\r\n            // Handle Guardrails from categoryId 3 where configValue is true\r\n            if (\r\n              configItem.categoryId === 3 &&\r\n              configItem.configValue === 'true'\r\n            ) {\r\n              console.log('✅ Found enabled guardrail from categoryId 3:', {\r\n                key: configItem.configKey,\r\n                value: configItem.configValue,\r\n              });\r\n\r\n              if (configItem.configKey === 'ENABLE_GUARDRAILS') {\r\n                // Only add one general guardrail node if not already added\r\n                if (this.blueprintGuardrailNodes.length === 0) {\r\n                  this.blueprintGuardrailNodes.push({\r\n                    id: `guardrail-${nodeCounter++}`,\r\n                    name: 'Guardrails Enabled',\r\n                    type: 'guardrail',\r\n                  });\r\n                }\r\n              } else if (configItem.configKey.startsWith('GUARDRAIL_')) {\r\n                // Add specific guardrail nodes for enabled guardrails\r\n                let guardrailName = configItem.configKey;\r\n                if (guardrailName.startsWith('GUARDRAIL_')) {\r\n                  guardrailName = guardrailName\r\n                    .replace('GUARDRAIL_', '')\r\n                    .replace(/_/g, ' ');\r\n                }\r\n\r\n                this.blueprintGuardrailNodes.push({\r\n                  id: `guardrail-${nodeCounter++}`,\r\n                  name: `${guardrailName}`,\r\n                  type: 'guardrail',\r\n                });\r\n              }\r\n            }\r\n          });\r\n        }\r\n      });\r\n    }\r\n\r\n    console.log('🎯 Final blueprint nodes:', {\r\n      promptNodes: this.blueprintPromptNodes,\r\n      modelNodes: this.blueprintModelNodes,\r\n      knowledgeNodes: this.blueprintKnowledgeNodes,\r\n      guardrailNodes: this.blueprintGuardrailNodes,\r\n    });\r\n\r\n    // Calculate completion percentage\r\n    const totalRequired = 2; // Prompt + Model are required\r\n    const currentRequired =\r\n      this.blueprintPromptNodes.length + this.blueprintModelNodes.length;\r\n    this.blueprintCompletionPercentage = Math.round(\r\n      (currentRequired / totalRequired) * 100,\r\n    );\r\n  }\r\n\r\n  private mapCollaborativeAgentToBlueprint(\r\n    agentData: any,\r\n    nodeCounter: number,\r\n  ): void {\r\n    console.log(\r\n      '🚀 SHARED COMPONENT: mapCollaborativeAgentToBlueprint called!',\r\n    );\r\n    console.log('🔍 DEBUG: Collaborative agent data received:', agentData);\r\n    console.log(\r\n      '🔍 DEBUG: Collaborative agent data keys:',\r\n      Object.keys(agentData),\r\n    );\r\n    console.log('🔍 DEBUG: Agent type in component:', this.agentType);\r\n    console.log('🔍 DEBUG: Current nodeCounter:', nodeCounter);\r\n\r\n    // Clear existing blueprint nodes\r\n    this.blueprintPromptNodes = [];\r\n    this.blueprintModelNodes = [];\r\n    this.blueprintKnowledgeNodes = [];\r\n    this.blueprintToolNodes = [];\r\n    this.blueprintGuardrailNodes = [];\r\n\r\n    console.log('🔍 DEBUG: Cleared all blueprint node arrays');\r\n\r\n    // Add prompt node - handle different prompt structures for collaborative agents\r\n    const shouldCreatePromptNode =\r\n      agentData.goal || agentData.role || agentData.description;\r\n\r\n    console.log('🔍 DEBUG: Checking prompt node creation:', {\r\n      goal: agentData.goal,\r\n      role: agentData.role,\r\n      description: agentData.description,\r\n      shouldCreatePromptNode,\r\n    });\r\n\r\n    if (shouldCreatePromptNode) {\r\n      let promptNodeName =\r\n        agentData.goal ||\r\n        agentData.role ||\r\n        agentData.description ||\r\n        'Collaborative Agent Prompt';\r\n\r\n      // Truncate prompt if too long for display\r\n      if (promptNodeName.length > 150) {\r\n        promptNodeName = promptNodeName.substring(0, 150) + '...';\r\n      }\r\n\r\n      this.blueprintPromptNodes.push({\r\n        id: `prompt-${nodeCounter++}`,\r\n        name: promptNodeName,\r\n        type: 'prompt',\r\n      });\r\n      console.log('✅ Added collaborative prompt node:', promptNodeName);\r\n    }\r\n\r\n    // Add model nodes - handle both old and new API formats like build-agents\r\n    let modelReferences = [];\r\n\r\n    console.log('🔍 DEBUG: Checking model data:', {\r\n      hasAgentConfigs: !!agentData.agentConfigs,\r\n      agentConfigsContent: agentData.agentConfigs,\r\n      hasTools: agentData.tools,\r\n      toolsContent: agentData.tools,\r\n      hasUserTools: agentData.userTools,\r\n      userToolsContent: agentData.userTools,\r\n    });\r\n\r\n    // New API format: agentConfigs.modelRef (array of model IDs or objects)\r\n    if (agentData.agentConfigs && agentData.agentConfigs.modelRef) {\r\n      const modelRefs = Array.isArray(agentData.agentConfigs.modelRef)\r\n        ? agentData.agentConfigs.modelRef\r\n        : [agentData.agentConfigs.modelRef];\r\n\r\n      modelReferences = modelRefs.map((ref: any) => {\r\n        if (typeof ref === 'number' || typeof ref === 'string') {\r\n          return { modelId: ref };\r\n        }\r\n        return ref;\r\n      });\r\n    }\r\n    // Old API format: modelDetails\r\n    else if (agentData.modelDetails) {\r\n      modelReferences = [agentData.modelDetails];\r\n    }\r\n    // Fallback: check for model or modelName directly\r\n    else if (agentData.model || agentData.modelName) {\r\n      modelReferences = [{ modelId: agentData.model || agentData.modelName }];\r\n    }\r\n\r\n    modelReferences.forEach((modelRef: any) => {\r\n      const modelId = modelRef.modelId || modelRef.id;\r\n      const modelName =\r\n        modelRef.model ||\r\n        modelRef.modelDeploymentName ||\r\n        `Model ID: ${modelId}`;\r\n\r\n      this.blueprintModelNodes.push({\r\n        id: `model-${nodeCounter++}`,\r\n        name: modelName,\r\n        type: 'model',\r\n      });\r\n      console.log('✅ Added collaborative model node:', modelName);\r\n    });\r\n\r\n    // Add knowledge base nodes - handle both old and new API formats\r\n    let knowledgeReferences = [];\r\n\r\n    // New API format: agentConfigs.knowledgeBaseRef (array of KB IDs or objects)\r\n    if (agentData.agentConfigs && agentData.agentConfigs.knowledgeBaseRef) {\r\n      const kbRefs = Array.isArray(agentData.agentConfigs.knowledgeBaseRef)\r\n        ? agentData.agentConfigs.knowledgeBaseRef\r\n        : [agentData.agentConfigs.knowledgeBaseRef];\r\n\r\n      knowledgeReferences = kbRefs.map((ref: any) => {\r\n        if (typeof ref === 'number' || typeof ref === 'string') {\r\n          return { knowledgeBaseId: ref };\r\n        }\r\n        return ref;\r\n      });\r\n    }\r\n    // Old API format: knowledgeBase\r\n    else if (\r\n      agentData.knowledgeBase &&\r\n      Array.isArray(agentData.knowledgeBase)\r\n    ) {\r\n      knowledgeReferences = agentData.knowledgeBase;\r\n    }\r\n\r\n    knowledgeReferences.forEach((kbRef: any) => {\r\n      const kbId = kbRef.knowledgeBaseId || kbRef.id;\r\n      const collectionName = kbRef.indexCollectionName || kbRef.name;\r\n      const kbName = collectionName || `Knowledge Base ID: ${kbId}`;\r\n\r\n      this.blueprintKnowledgeNodes.push({\r\n        id: `knowledge-${nodeCounter++}`,\r\n        name: kbName,\r\n        type: 'knowledge',\r\n      });\r\n      console.log('✅ Added collaborative knowledge node:', kbName);\r\n    });\r\n\r\n    // Add tool nodes - handle both old and new API formats like build-agents\r\n    let toolReferences = [];\r\n    let userToolReferences = [];\r\n\r\n    console.log('🔍 DEBUG: Checking tool data:', {\r\n      hasAgentConfigs: !!agentData.agentConfigs,\r\n      agentConfigsContent: agentData.agentConfigs,\r\n      hasTools: agentData.tools,\r\n      toolsContent: agentData.tools,\r\n      hasUserTools: agentData.userTools,\r\n      userToolsContent: agentData.userTools,\r\n    });\r\n\r\n    // New API format: agentConfigs.toolRef and agentConfigs.userToolRef\r\n    if (agentData.agentConfigs) {\r\n      if (agentData.agentConfigs.toolRef) {\r\n        const toolRefs = Array.isArray(agentData.agentConfigs.toolRef)\r\n          ? agentData.agentConfigs.toolRef\r\n          : [agentData.agentConfigs.toolRef];\r\n\r\n        toolReferences = toolRefs.map((ref: any) => {\r\n          if (typeof ref === 'number' || typeof ref === 'string') {\r\n            return { toolId: ref };\r\n          }\r\n          return ref;\r\n        });\r\n      }\r\n      if (agentData.agentConfigs.userToolRef) {\r\n        const userToolRefs = Array.isArray(agentData.agentConfigs.userToolRef)\r\n          ? agentData.agentConfigs.userToolRef\r\n          : [agentData.agentConfigs.userToolRef];\r\n\r\n        userToolReferences = userToolRefs.map((ref: any) => {\r\n          if (typeof ref === 'number' || typeof ref === 'string') {\r\n            return { toolId: ref };\r\n          }\r\n          return ref;\r\n        });\r\n      }\r\n    }\r\n    // Old API format: tools and userTools\r\n    else {\r\n      if (agentData.tools && Array.isArray(agentData.tools)) {\r\n        toolReferences = agentData.tools;\r\n      }\r\n      if (agentData.userTools && Array.isArray(agentData.userTools)) {\r\n        userToolReferences = agentData.userTools;\r\n      }\r\n    }\r\n\r\n    // Process built-in tools\r\n    toolReferences.forEach((tool: any) => {\r\n      const toolId = tool.toolId || tool.id;\r\n      const toolName = tool.toolName || tool.name || `Tool ID: ${toolId}`;\r\n\r\n      this.blueprintToolNodes.push({\r\n        id: `tool-${nodeCounter++}`,\r\n        name: toolName,\r\n        type: 'tool',\r\n      });\r\n      console.log('✅ Added collaborative builtin tool node:', toolName);\r\n    });\r\n\r\n    // Process user tools\r\n    userToolReferences.forEach((userTool: any) => {\r\n      const userToolId = userTool.toolId || userTool.id;\r\n      const userToolName =\r\n        userTool.toolName || userTool.name || `User Tool ID: ${userToolId}`;\r\n\r\n      this.blueprintToolNodes.push({\r\n        id: `tool-${nodeCounter++}`,\r\n        name: userToolName,\r\n        type: 'tool',\r\n      });\r\n      console.log('✅ Added collaborative user tool node:', userToolName);\r\n    });\r\n\r\n    console.log('🎯 Final collaborative blueprint nodes:', {\r\n      promptNodes: this.blueprintPromptNodes,\r\n      modelNodes: this.blueprintModelNodes,\r\n      knowledgeNodes: this.blueprintKnowledgeNodes,\r\n      toolNodes: this.blueprintToolNodes,\r\n      guardrailNodes: this.blueprintGuardrailNodes,\r\n    });\r\n\r\n    // Debug: Check blueprint node arrays lengths\r\n    console.log('📊 Blueprint node counts:', {\r\n      promptCount: this.blueprintPromptNodes.length,\r\n      modelCount: this.blueprintModelNodes.length,\r\n      knowledgeCount: this.blueprintKnowledgeNodes.length,\r\n      toolCount: this.blueprintToolNodes.length,\r\n      guardrailCount: this.blueprintGuardrailNodes.length,\r\n    });\r\n\r\n    // Debug: Check if tools zone will be visible\r\n    console.log('🔧 Tools zone debug:', {\r\n      agentType: this.agentType,\r\n      isCollaborative: this.agentType === 'collaborative',\r\n      hasToolNodes: this.blueprintToolNodes.length > 0,\r\n      toolNodeNames: this.blueprintToolNodes.map((t) => t.name),\r\n    });\r\n\r\n    // Calculate completion percentage\r\n    const totalRequired = 2; // Prompt + Model are required\r\n    const currentRequired =\r\n      this.blueprintPromptNodes.length + this.blueprintModelNodes.length;\r\n    this.blueprintCompletionPercentage = Math.round(\r\n      (currentRequired / totalRequired) * 100,\r\n    );\r\n  }\r\n}\r\n", "<div class=\"agent-execution-container\">\r\n  <!-- Top Navigation Bar -->\r\n  <div class=\"top-nav-bar\">\r\n    <div class=\"nav-left\">\r\n      <button class=\"back-button\" (click)=\"navigateBack()\" type=\"button\">\r\n        <ava-icon\r\n          iconName=\"ArrowLeft\"\r\n          iconSize=\"16\"\r\n          iconColor=\"#374151\"\r\n        ></ava-icon>\r\n        <span class=\"agent-name\">{{ agentName }}</span>\r\n      </button>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Main Content with Two Panels -->\r\n  <div class=\"main-content\">\r\n    <!-- Left Panel: Playground -->\r\n    <div class=\"left-panel\" [class.collapsed]=\"isLeftPanelCollapsed\">\r\n      <div class=\"panel-header\">\r\n        <button class=\"collapse-btn\" (click)=\"toggleLeftPanel()\" type=\"button\">\r\n          <ava-icon\r\n            [iconName]=\"isLeftPanelCollapsed ? 'ChevronRight' : 'PanelLeft'\"\r\n            iconSize=\"16\"\r\n            iconColor=\"#6B7280\"\r\n          >\r\n          </ava-icon>\r\n        </button>\r\n        <button\r\n          class=\"history-btn\"\r\n          *ngIf=\"!isLeftPanelCollapsed\"\r\n          type=\"button\"\r\n          disabled\r\n        >\r\n          History\r\n        </button>\r\n      </div>\r\n\r\n      <div class=\"panel-content\" [class.hidden]=\"isLeftPanelCollapsed\">\r\n        <app-agent-execution-playground\r\n          [messages]=\"chatMessages\"\r\n          [isLoading]=\"isProcessingChat\"\r\n          [agentType]=\"agentType\"\r\n          [showChatInteractionToggles]=\"agentType === 'individual'\"\r\n          [showAiPrincipleToggle]=\"false\"\r\n          [showApprovalButton]=\"false\"\r\n          [showDropdown]=\"false\"\r\n          [showAgentNameInput]=\"true\"\r\n          [showFileUploadButton]=\"true\"\r\n          [showStatusOnly]=\"true\"\r\n          [displayedAgentName]=\"agentName\"\r\n          [agentNamePlaceholder]=\"'Current Agent Name'\"\r\n          (promptChange)=\"onPromptChanged($event)\"\r\n          (messageSent)=\"handleChatMessage($event)\"\r\n          (conversationalToggle)=\"onPlaygroundConversationalToggle($event)\"\r\n          (templateToggle)=\"onPlaygroundTemplateToggle($event)\"\r\n          (filesSelected)=\"onFilesSelected($event)\"\r\n          (approvalRequested)=\"onApprovalRequested()\"\r\n        >\r\n        </app-agent-execution-playground>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Right Panel: Blueprint/Output -->\r\n    <div class=\"right-panel\">\r\n      <!-- Right Panel Header -->\r\n      <div class=\"panel-header\">\r\n        <div class=\"tabs-container\">\r\n          <button\r\n            class=\"tab-btn\"\r\n            [class.active]=\"activeRightTab === 'blueprint'\"\r\n            (click)=\"setActiveRightTab('blueprint')\"\r\n          >\r\n            Blueprint\r\n          </button>\r\n          <button\r\n            class=\"tab-btn\"\r\n            [class.active]=\"activeRightTab === 'output'\"\r\n            (click)=\"setActiveRightTab('output')\"\r\n          >\r\n            Agent Output\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"panel-content\">\r\n        <!-- Blueprint Content -->\r\n        <div *ngIf=\"activeRightTab === 'blueprint'\" class=\"blueprint-content\">\r\n          <div class=\"blueprint-header\">\r\n            <h3>Agent Blueprint</h3>\r\n          </div>\r\n\r\n          <!-- Custom Blueprint Display -->\r\n          <div class=\"custom-blueprint-container\">\r\n            <!-- SVG Connecting Lines -->\r\n            <svg\r\n              class=\"connection-lines\"\r\n              viewBox=\"0 0 100 100\"\r\n              preserveAspectRatio=\"none\"\r\n            >\r\n              <!-- Line from System Prompt (top-left) to center -->\r\n              <line\r\n                x1=\"25\"\r\n                y1=\"25\"\r\n                x2=\"50\"\r\n                y2=\"50\"\r\n                stroke=\"#BBBEC5\"\r\n                stroke-width=\"0.3\"\r\n              />\r\n\r\n              <!-- Line from AI Model (top-right) to center -->\r\n              <line\r\n                x1=\"75\"\r\n                y1=\"25\"\r\n                x2=\"50\"\r\n                y2=\"50\"\r\n                stroke=\"#BBBEC5\"\r\n                stroke-width=\"0.3\"\r\n              />\r\n\r\n              <!-- Line from Knowledge Base (bottom-left) to center -->\r\n              <line\r\n                x1=\"25\"\r\n                y1=\"75\"\r\n                x2=\"50\"\r\n                y2=\"50\"\r\n                stroke=\"#BBBEC5\"\r\n                stroke-width=\"0.3\"\r\n              />\r\n\r\n              <!-- Line from Guardrails (bottom-right) to center -->\r\n              <line\r\n                x1=\"75\"\r\n                y1=\"75\"\r\n                x2=\"50\"\r\n                y2=\"50\"\r\n                stroke=\"#BBBEC5\"\r\n                stroke-width=\"0.3\"\r\n              />\r\n            </svg>\r\n\r\n            <!-- Central Progress Bar -->\r\n            <div class=\"central-progress\">\r\n              <div class=\"progress-ring\">\r\n                <svg class=\"progress-circle\" width=\"120\" height=\"120\">\r\n                  <defs>\r\n                    <linearGradient\r\n                      id=\"progressGradient\"\r\n                      x1=\"0%\"\r\n                      y1=\"0%\"\r\n                      x2=\"100%\"\r\n                      y2=\"100%\"\r\n                    >\r\n                      <stop offset=\"33.91%\" style=\"stop-color: #0084ff\" />\r\n                      <stop offset=\"100%\" style=\"stop-color: #03bdd4\" />\r\n                    </linearGradient>\r\n                  </defs>\r\n                  <circle\r\n                    class=\"progress-background\"\r\n                    cx=\"60\"\r\n                    cy=\"60\"\r\n                    r=\"50\"\r\n                    fill=\"none\"\r\n                    stroke=\"#e5e7eb\"\r\n                    stroke-width=\"8\"\r\n                  />\r\n                  <circle\r\n                    class=\"progress-bar\"\r\n                    cx=\"60\"\r\n                    cy=\"60\"\r\n                    r=\"50\"\r\n                    fill=\"none\"\r\n                    stroke=\"url(#progressGradient)\"\r\n                    stroke-width=\"8\"\r\n                    stroke-linecap=\"round\"\r\n                    [style.stroke-dasharray]=\"314\"\r\n                    [style.stroke-dashoffset]=\"\r\n                      314 - (314 * blueprintCompletionPercentage) / 100\r\n                    \"\r\n                    transform=\"rotate(180 60 60)\"\r\n                  />\r\n                </svg>\r\n                <div class=\"progress-content\">\r\n                  <div class=\"progress-percentage\">\r\n                    {{ blueprintCompletionPercentage }}%\r\n                  </div>\r\n                  <div class=\"progress-label\">Complete</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Blueprint Zones Layout -->\r\n            <div id=\"parent-box\">\r\n              <!-- <div id=\"box1\"> -->\r\n              <!-- System Prompt Zone -->\r\n              <div\r\n                class=\"blueprint-zone north-zone prompts-zone\"\r\n                [class.has-nodes]=\"blueprintPromptNodes.length > 0\"\r\n              >\r\n                <div\r\n                  class=\"zone-header\"\r\n                  (click)=\"toggleBlueprintZone('prompt')\"\r\n                >\r\n                  <div class=\"header-content\">\r\n                    <div class=\"header-icon\">\r\n                      <svg\r\n                        width=\"45\"\r\n                        height=\"44\"\r\n                        viewBox=\"0 0 45 44\"\r\n                        fill=\"none\"\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                      >\r\n                        <rect\r\n                          x=\"0.679688\"\r\n                          width=\"44\"\r\n                          height=\"44\"\r\n                          rx=\"8\"\r\n                          fill=\"#5082EF\"\r\n                        />\r\n                        <path\r\n                          d=\"M20.6166 25.5C20.5273 25.1539 20.347 24.8381 20.0942 24.5854C19.8415 24.3327 19.5257 24.1523 19.1796 24.063L13.0446 22.481C12.94 22.4513 12.8478 22.3883 12.7822 22.3014C12.7166 22.2146 12.6812 22.1088 12.6812 22C12.6812 21.8912 12.7166 21.7854 12.7822 21.6986C12.8478 21.6118 12.94 21.5487 13.0446 21.519L19.1796 19.936C19.5256 19.8468 19.8413 19.6666 20.094 19.414C20.3467 19.1615 20.5272 18.8459 20.6166 18.5L22.1986 12.365C22.228 12.2599 22.291 12.1673 22.3779 12.1014C22.4649 12.0355 22.571 11.9998 22.6801 11.9998C22.7892 11.9998 22.8954 12.0355 22.9823 12.1014C23.0692 12.1673 23.1322 12.2599 23.1616 12.365L24.7426 18.5C24.8319 18.8461 25.0123 19.1619 25.265 19.4146C25.5177 19.6673 25.8336 19.8477 26.1796 19.937L32.3146 21.518C32.4201 21.5471 32.5132 21.61 32.5795 21.6971C32.6458 21.7841 32.6817 21.8906 32.6817 22C32.6817 22.1094 32.6458 22.2159 32.5795 22.3029C32.5132 22.39 32.4201 22.4529 32.3146 22.482L26.1796 24.063C25.8336 24.1523 25.5177 24.3327 25.265 24.5854C25.0123 24.8381 24.8319 25.1539 24.7426 25.5L23.1606 31.635C23.1312 31.7401 23.0682 31.8327 22.9813 31.8986C22.8944 31.9646 22.7882 32.0003 22.6791 32.0003C22.57 32.0003 22.4639 31.9646 22.3769 31.8986C22.29 31.8327 22.227 31.7401 22.1976 31.635L20.6166 25.5Z\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                      </svg>\r\n                    </div>\r\n                    <h3 class=\"zone-title\">System Prompt</h3>\r\n                  </div>\r\n                  <div class=\"header-actions\">\r\n                    <span class=\"required-badge\">Required</span>\r\n                    <button class=\"accordion-toggle\" type=\"button\">\r\n                      <svg\r\n                        width=\"16\"\r\n                        height=\"16\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        fill=\"none\"\r\n                        [style.transform]=\"\r\n                          isBlueprintZoneExpanded('prompt')\r\n                            ? 'rotate(180deg)'\r\n                            : 'rotate(0deg)'\r\n                        \"\r\n                      >\r\n                        <path\r\n                          d=\"M6 9L12 15L18 9\"\r\n                          stroke=\"currentColor\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                      </svg>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n                <div\r\n                  class=\"zone-content\"\r\n                  *ngIf=\"isBlueprintZoneExpanded('prompt')\"\r\n                >\r\n                  <div\r\n                    *ngIf=\"blueprintPromptNodes.length === 0\"\r\n                    class=\"empty-state\"\r\n                  >\r\n                    No prompt configured\r\n                  </div>\r\n                  <div class=\"nodes-list\">\r\n                    <div\r\n                      *ngFor=\"let node of blueprintPromptNodes\"\r\n                      class=\"kanban-card\"\r\n                    >\r\n                      <span class=\"card-title\">{{\r\n                        node.name || \"Prompt\"\r\n                      }}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Knowledge Base Zone -->\r\n              <div\r\n                class=\"blueprint-zone west-zone knowledge-zone\"\r\n                [class.has-nodes]=\"blueprintKnowledgeNodes.length > 0\"\r\n              >\r\n                <div\r\n                  class=\"zone-header\"\r\n                  (click)=\"toggleBlueprintZone('knowledge')\"\r\n                >\r\n                  <div class=\"header-content\">\r\n                    <div class=\"header-icon\">\r\n                      <svg\r\n                        width=\"45\"\r\n                        height=\"44\"\r\n                        viewBox=\"0 0 45 44\"\r\n                        fill=\"none\"\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                      >\r\n                        <rect\r\n                          x=\"0.679688\"\r\n                          width=\"44\"\r\n                          height=\"44\"\r\n                          rx=\"8\"\r\n                          fill=\"#308666\"\r\n                        />\r\n                        <path\r\n                          d=\"M22.6797 17V31\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                        <path\r\n                          d=\"M26.6797 22H28.6797\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                        <path\r\n                          d=\"M26.6797 18H28.6797\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                        <path\r\n                          d=\"M13.6797 28C13.4145 28 13.1601 27.8946 12.9726 27.7071C12.785 27.5196 12.6797 27.2652 12.6797 27V14C12.6797 13.7348 12.785 13.4804 12.9726 13.2929C13.1601 13.1054 13.4145 13 13.6797 13H18.6797C19.7406 13 20.758 13.4214 21.5081 14.1716C22.2583 14.9217 22.6797 15.9391 22.6797 17C22.6797 15.9391 23.1011 14.9217 23.8513 14.1716C24.6014 13.4214 25.6188 13 26.6797 13H31.6797C31.9449 13 32.1993 13.1054 32.3868 13.2929C32.5743 13.4804 32.6797 13.7348 32.6797 14V27C32.6797 27.2652 32.5743 27.5196 32.3868 27.7071C32.1993 27.8946 31.9449 28 31.6797 28H25.6797C24.884 28 24.121 28.3161 23.5584 28.8787C22.9958 29.4413 22.6797 30.2044 22.6797 31C22.6797 30.2044 22.3636 29.4413 21.801 28.8787C21.2384 28.3161 20.4753 28 19.6797 28H13.6797Z\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                        <path\r\n                          d=\"M16.6797 22H18.6797\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                        <path\r\n                          d=\"M16.6797 18H18.6797\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                      </svg>\r\n                    </div>\r\n                    <h3 class=\"zone-title\">Knowledgebase</h3>\r\n                  </div>\r\n                  <div class=\"header-actions\">\r\n                    <span class=\"optional-badge\">Optional</span>\r\n                    <button class=\"accordion-toggle\" type=\"button\">\r\n                      <svg\r\n                        width=\"16\"\r\n                        height=\"16\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        fill=\"none\"\r\n                        [style.transform]=\"\r\n                          isBlueprintZoneExpanded('knowledge')\r\n                            ? 'rotate(180deg)'\r\n                            : 'rotate(0deg)'\r\n                        \"\r\n                      >\r\n                        <path\r\n                          d=\"M6 9L12 15L18 9\"\r\n                          stroke=\"currentColor\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                      </svg>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n                <div\r\n                  class=\"zone-content\"\r\n                  *ngIf=\"isBlueprintZoneExpanded('knowledge')\"\r\n                >\r\n                  <div\r\n                    *ngIf=\"blueprintKnowledgeNodes.length === 0\"\r\n                    class=\"empty-state\"\r\n                  >\r\n                    No knowledge base configured\r\n                  </div>\r\n                  <div class=\"nodes-list\">\r\n                    <div\r\n                      *ngFor=\"let node of blueprintKnowledgeNodes\"\r\n                      class=\"kanban-card\"\r\n                    >\r\n                      <span class=\"card-title\">{{\r\n                        node.name || \"Knowledge Base\"\r\n                      }}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <!-- </div> -->\r\n\r\n              <!-- <div id=\"box2\"> -->\r\n              <!-- AI Model Zone -->\r\n              <div\r\n                class=\"blueprint-zone east-zone models-zone\"\r\n                [class.has-nodes]=\"blueprintModelNodes.length > 0\"\r\n              >\r\n                <div class=\"zone-header\" (click)=\"toggleBlueprintZone('model')\">\r\n                  <div class=\"header-content\">\r\n                    <div class=\"header-icon\">\r\n                      <svg\r\n                        width=\"45\"\r\n                        height=\"44\"\r\n                        viewBox=\"0 0 45 44\"\r\n                        fill=\"none\"\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                      >\r\n                        <rect\r\n                          x=\"0.679688\"\r\n                          width=\"44\"\r\n                          height=\"44\"\r\n                          rx=\"8\"\r\n                          fill=\"#997BCF\"\r\n                        />\r\n                        <path\r\n                          d=\"M31.6797 18C31.6793 17.6493 31.5867 17.3048 31.4112 17.0012C31.2357 16.6975 30.9834 16.4454 30.6797 16.27L23.6797 12.27C23.3756 12.0945 23.0308 12.0021 22.6797 12.0021C22.3286 12.0021 21.9837 12.0945 21.6797 12.27L14.6797 16.27C14.376 16.4454 14.1237 16.6975 13.9481 17.0012C13.7726 17.3048 13.68 17.6493 13.6797 18V26C13.68 26.3508 13.7726 26.6952 13.9481 26.9989C14.1237 27.3025 14.376 27.5547 14.6797 27.73L21.6797 31.73C21.9837 31.9056 22.3286 31.998 22.6797 31.998C23.0308 31.998 23.3756 31.9056 23.6797 31.73L30.6797 27.73C30.9834 27.5547 31.2357 27.3025 31.4112 26.9989C31.5867 26.6952 31.6793 26.3508 31.6797 26V18Z\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                        <path\r\n                          d=\"M13.9795 17L22.6795 22L31.3795 17\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                        <path\r\n                          d=\"M22.6797 32V22\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                      </svg>\r\n                    </div>\r\n                    <h3 class=\"zone-title\">AI Model</h3>\r\n                  </div>\r\n                  <div class=\"header-actions\">\r\n                    <span class=\"required-badge\">Required</span>\r\n                    <button class=\"accordion-toggle\" type=\"button\">\r\n                      <svg\r\n                        width=\"16\"\r\n                        height=\"16\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        fill=\"none\"\r\n                        [style.transform]=\"\r\n                          isBlueprintZoneExpanded('model')\r\n                            ? 'rotate(180deg)'\r\n                            : 'rotate(0deg)'\r\n                        \"\r\n                      >\r\n                        <path\r\n                          d=\"M6 9L12 15L18 9\"\r\n                          stroke=\"currentColor\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                      </svg>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n                <div\r\n                  class=\"zone-content\"\r\n                  *ngIf=\"isBlueprintZoneExpanded('model')\"\r\n                >\r\n                  <div\r\n                    *ngIf=\"blueprintModelNodes.length === 0\"\r\n                    class=\"empty-state\"\r\n                  >\r\n                    No model configured\r\n                  </div>\r\n                  <div class=\"nodes-list\">\r\n                    <div\r\n                      *ngFor=\"let node of blueprintModelNodes\"\r\n                      class=\"kanban-card\"\r\n                    >\r\n                      <span class=\"card-title\">{{ node.name || \"Model\" }}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Tools Zone (for Collaborative Agents) -->\r\n              <div\r\n                *ngIf=\"agentType === 'collaborative'\"\r\n                class=\"blueprint-zone south-zone tools-zone\"\r\n                [class.has-nodes]=\"blueprintToolNodes.length > 0\"\r\n              >\r\n                <div class=\"zone-header\" (click)=\"toggleBlueprintZone('tool')\">\r\n                  <div class=\"header-content\">\r\n                    <div class=\"header-icon\">\r\n                      <svg\r\n                        width=\"45\"\r\n                        height=\"44\"\r\n                        viewBox=\"0 0 45 44\"\r\n                        fill=\"none\"\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                        *ngIf=\"agentType === 'collaborative'\"\r\n                      >\r\n                        <rect\r\n                          x=\"0.679688\"\r\n                          width=\"44\"\r\n                          height=\"44\"\r\n                          rx=\"8\"\r\n                          fill=\"#D97706\"\r\n                        />\r\n                        <path\r\n                          d=\"M25.3795 16.3C25.1963 16.4869 25.0937 16.7382 25.0937 17C25.0937 17.2617 25.1963 17.5131 25.3795 17.7L26.9795 19.3C27.1665 19.4832 27.4178 19.5858 27.6795 19.5858C27.9413 19.5858 28.1926 19.4832 28.3795 19.3L32.1495 15.53C32.6524 16.6412 32.8046 17.8792 32.586 19.0791C32.3674 20.279 31.7883 21.3838 30.9258 22.2463C30.0634 23.1087 28.9586 23.6878 27.7587 23.9064C26.5588 24.1251 25.3207 23.9728 24.2095 23.47L17.2995 30.38C16.9017 30.7778 16.3622 31.0013 15.7995 31.0013C15.2369 31.0013 14.6974 30.7778 14.2995 30.38C13.9017 29.9822 13.6782 29.4426 13.6782 28.88C13.6782 28.3174 13.9017 27.7778 14.2995 27.38L21.2095 20.47C20.7067 19.3588 20.5545 18.1207 20.7731 16.9208C20.9917 15.7209 21.5708 14.6161 22.4333 13.7537C23.2957 12.8913 24.4005 12.3121 25.6004 12.0935C26.8003 11.8749 28.0384 12.0271 29.1495 12.53L25.3895 16.29L25.3795 16.3Z\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                      </svg>\r\n                    </div>\r\n                    <h3 class=\"zone-title\">Tools</h3>\r\n                  </div>\r\n                  <div class=\"header-actions\">\r\n                    <span class=\"optional-badge\">Optional</span>\r\n                    <button class=\"accordion-toggle\" type=\"button\">\r\n                      <svg\r\n                        width=\"16\"\r\n                        height=\"16\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        fill=\"none\"\r\n                        [style.transform]=\"\r\n                          isBlueprintZoneExpanded('tool')\r\n                            ? 'rotate(180deg)'\r\n                            : 'rotate(0deg)'\r\n                        \"\r\n                      >\r\n                        <path\r\n                          d=\"M6 9L12 15L18 9\"\r\n                          stroke=\"currentColor\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                      </svg>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n                <div\r\n                  class=\"zone-content\"\r\n                  *ngIf=\"isBlueprintZoneExpanded('tool')\"\r\n                >\r\n                  <div\r\n                    *ngIf=\"blueprintToolNodes.length === 0\"\r\n                    class=\"empty-state\"\r\n                  >\r\n                    No tools configured\r\n                  </div>\r\n                  <div class=\"nodes-list\">\r\n                    <div\r\n                      *ngFor=\"let node of blueprintToolNodes\"\r\n                      class=\"kanban-card\"\r\n                    >\r\n                      <span class=\"card-title\">{{ node.name || \"Tool\" }}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Guardrails Zone (for Individual Agents) -->\r\n              <div\r\n                *ngIf=\"agentType === 'individual'\"\r\n                class=\"blueprint-zone south-zone guardrails-zone\"\r\n                [class.has-nodes]=\"blueprintGuardrailNodes.length > 0\"\r\n              >\r\n                <div\r\n                  class=\"zone-header\"\r\n                  (click)=\"toggleBlueprintZone('guardrail')\"\r\n                >\r\n                  <div class=\"header-content\">\r\n                    <div class=\"header-icon\">\r\n                      <svg\r\n                        width=\"45\"\r\n                        height=\"44\"\r\n                        viewBox=\"0 0 45 44\"\r\n                        fill=\"none\"\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                      >\r\n                        <rect\r\n                          x=\"0.679688\"\r\n                          width=\"44\"\r\n                          height=\"44\"\r\n                          rx=\"8\"\r\n                          fill=\"#DC2626\"\r\n                        />\r\n                        <path\r\n                          d=\"M30.6797 23C30.6797 28 27.1797 30.5 23.0197 31.95C22.8019 32.0238 22.5652 32.0202 22.3497 31.94C18.1797 30.5 14.6797 28 14.6797 23V16C14.6797 15.7347 14.785 15.4804 14.9726 15.2929C15.1601 15.1053 15.4145 15 15.6797 15C17.6797 15 20.1797 13.8 21.9197 12.28C22.1315 12.099 22.401 11.9995 22.6797 11.9995C22.9583 11.9995 23.2278 12.099 23.4397 12.28C25.1897 13.81 27.6797 15 29.6797 15C29.9449 15 30.1993 15.1053 30.3868 15.2929C30.5743 15.4804 30.6797 15.7347 30.6797 16V23Z\"\r\n                          stroke=\"white\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                      </svg>\r\n                    </div>\r\n                    <h3 class=\"zone-title\">Guardrails</h3>\r\n                  </div>\r\n                  <div class=\"header-actions\">\r\n                    <span class=\"optional-badge\">Optional</span>\r\n                    <button class=\"accordion-toggle\" type=\"button\">\r\n                      <svg\r\n                        width=\"16\"\r\n                        height=\"16\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        fill=\"none\"\r\n                        [style.transform]=\"\r\n                          isBlueprintZoneExpanded('guardrail')\r\n                            ? 'rotate(180deg)'\r\n                            : 'rotate(0deg)'\r\n                        \"\r\n                      >\r\n                        <path\r\n                          d=\"M6 9L12 15L18 9\"\r\n                          stroke=\"currentColor\"\r\n                          stroke-width=\"2\"\r\n                          stroke-linecap=\"round\"\r\n                          stroke-linejoin=\"round\"\r\n                        />\r\n                      </svg>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n                <div\r\n                  class=\"zone-content\"\r\n                  *ngIf=\"isBlueprintZoneExpanded('guardrail')\"\r\n                >\r\n                  <div\r\n                    *ngIf=\"blueprintGuardrailNodes.length === 0\"\r\n                    class=\"empty-state\"\r\n                  >\r\n                    No guardrails configured\r\n                  </div>\r\n                  <div class=\"nodes-list\">\r\n                    <div\r\n                      *ngFor=\"let node of blueprintGuardrailNodes\"\r\n                      class=\"kanban-card\"\r\n                    >\r\n                      <span class=\"card-title\">{{\r\n                        node.name || \"Guardrail\"\r\n                      }}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <!-- </div> -->\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Agent Output Content -->\r\n        <div *ngIf=\"activeRightTab === 'output'\" class=\"output-content\">\r\n          <h3>Agent Output</h3>\r\n          \r\n          <!-- Cubical Loader Overlay -->\r\n          <div *ngIf=\"isLoadingOutput\" class=\"loader-overlay\">\r\n            <ava-cubical-loading background=\"linear-gradient(45deg, #4facfe 0%, #00f2fe 100%)\"></ava-cubical-loading>\r\n          </div>\r\n          \r\n          <!-- Execution History with Scroll -->\r\n          <div class=\"execution-history-container\">\r\n            <div *ngIf=\"executionHistory.length === 0\" class=\"no-output-message\">\r\n              <p>No agent outputs available yet. Send a message to see the response.</p>\r\n            </div>\r\n            \r\n            <div *ngIf=\"executionHistory.length > 0\" class=\"execution-history-list\">\r\n              <div \r\n                *ngFor=\"let execution of executionHistory; let i = index\" \r\n                class=\"execution-item\"\r\n                [class.success]=\"execution.status === 'success'\"\r\n                [class.failed]=\"execution.status === 'failed'\"\r\n                [class.pending]=\"execution.status === 'pending'\"\r\n              >\r\n                <div class=\"execution-header\">\r\n                  <div class=\"agent-name\">{{ execution.agentName }}</div>\r\n                  <div class=\"execution-status\" [class]=\"execution.status\">\r\n                    {{ execution.status === 'success' ? 'Success' : execution.status === 'failed' ? 'Failed' : 'Pending' }}\r\n                  </div>\r\n                </div>\r\n                \r\n                <div class=\"execution-details\">\r\n                  <div class=\"user-message\">\r\n                    <strong>Query:</strong> {{ execution.userMessage }}\r\n                  </div>\r\n                  \r\n                  <div class=\"execution-timestamp\">\r\n                    {{ execution.timestamp | date:'medium' }}\r\n                  </div>\r\n                </div>\r\n                \r\n                <!-- Individual Agent Response -->\r\n                <div *ngIf=\"execution.status === 'success' && execution.response && agentType === 'individual'\" class=\"execution-response\">\r\n                  <div class=\"response-section\">\r\n                    <h4>Response</h4>\r\n                    <div class=\"response-text\">\r\n                      {{ execution.response?.response?.choices?.[0]?.text || execution.response?.agentResponse?.detail || 'No response content available.' }}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                \r\n                <!-- Collaborative Agent Response -->\r\n                <div *ngIf=\"execution.status === 'success' && execution.response && agentType === 'collaborative'\" class=\"execution-response\">\r\n                  <div \r\n                    *ngFor=\"let taskOutput of execution.response?.agentResponse?.agent?.tasksOutputs; let j = index\"\r\n                    class=\"task-output\"\r\n                  >\r\n                    <div *ngIf=\"taskOutput.description\" class=\"task-description\">\r\n                      <strong>Description:</strong> {{ taskOutput.description }}\r\n                    </div>\r\n                    <div *ngIf=\"taskOutput.expected_output\" class=\"task-expected\">\r\n                      <strong>Expected Output:</strong> {{ taskOutput.expected_output }}\r\n                    </div>\r\n                    <div class=\"task-content\">\r\n                      {{ taskOutput.raw }}\r\n                    </div>\r\n                    <div class=\"task-header\">\r\n                      <h4>Summary: {{ taskOutput.summary || 'Task ' + (j + 1) }}</h4>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                \r\n                <!-- Failed Response -->\r\n                <div *ngIf=\"execution.status === 'failed'\" class=\"execution-error\">\r\n                  <div class=\"error-message\">\r\n                    <strong>Error:</strong> The agent execution failed. Please try again.\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,OAAO,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,EAAE,QAAQ,MAAM;AAC9E,SAAiCC,WAAW,QAAQ,gBAAgB;AAEpE;AACA,SAASC,iCAAiC,QAAQ,8EAA8E;AAEhI,SAASC,aAAa,EAA2BC,uBAAuB,QAAQ,wBAAwB;AAGxG,SAASC,WAAW,QAAQ,kCAAkC;AAK9D,SAASC,eAAe,QAAmD,gCAAgC;;;;;;;;;;;;ICWnGC,EAAA,CAAAC,cAAA,iBAKC;IACCD,EAAA,CAAAE,MAAA,gBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAgOCH,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAMFH,EAJF,CAAAC,cAAA,cAGC,eAC0B;IAAAD,EAAA,CAAAE,MAAA,GAEvB;IACJF,EADI,CAAAG,YAAA,EAAO,EACL;;;;IAHqBH,EAAA,CAAAI,SAAA,GAEvB;IAFuBJ,EAAA,CAAAK,iBAAA,CAAAC,OAAA,CAAAC,IAAA,aAEvB;;;;;IAjBRP,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAQ,UAAA,IAAAC,oDAAA,kBAGC;IAGDT,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAQ,UAAA,IAAAE,oDAAA,kBAGC;IAMLV,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAfDH,EAAA,CAAAI,SAAA,EAAuC;IAAvCJ,EAAA,CAAAW,UAAA,SAAAC,MAAA,CAAAC,oBAAA,CAAAC,MAAA,OAAuC;IAOrBd,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAW,UAAA,YAAAC,MAAA,CAAAC,oBAAA,CAAuB;;;;;IA+G5Cb,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,MAAA,qCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAMFH,EAJF,CAAAC,cAAA,cAGC,eAC0B;IAAAD,EAAA,CAAAE,MAAA,GAEvB;IACJF,EADI,CAAAG,YAAA,EAAO,EACL;;;;IAHqBH,EAAA,CAAAI,SAAA,GAEvB;IAFuBJ,EAAA,CAAAK,iBAAA,CAAAU,OAAA,CAAAR,IAAA,qBAEvB;;;;;IAjBRP,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAQ,UAAA,IAAAQ,oDAAA,kBAGC;IAGDhB,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAQ,UAAA,IAAAS,oDAAA,kBAGC;IAMLjB,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAfDH,EAAA,CAAAI,SAAA,EAA0C;IAA1CJ,EAAA,CAAAW,UAAA,SAAAC,MAAA,CAAAM,uBAAA,CAAAJ,MAAA,OAA0C;IAOxBd,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAW,UAAA,YAAAC,MAAA,CAAAM,uBAAA,CAA0B;;;;;IAyF/ClB,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAMFH,EAJF,CAAAC,cAAA,cAGC,eAC0B;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IACrDF,EADqD,CAAAG,YAAA,EAAO,EACtD;;;;IADqBH,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAK,iBAAA,CAAAc,OAAA,CAAAZ,IAAA,YAA0B;;;;;IAfzDP,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAQ,UAAA,IAAAY,oDAAA,kBAGC;IAGDpB,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAQ,UAAA,IAAAa,oDAAA,kBAGC;IAILrB,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAbDH,EAAA,CAAAI,SAAA,EAAsC;IAAtCJ,EAAA,CAAAW,UAAA,SAAAC,MAAA,CAAAU,mBAAA,CAAAR,MAAA,OAAsC;IAOpBd,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAW,UAAA,YAAAC,MAAA,CAAAU,mBAAA,CAAsB;;;;;;IAkBvCtB,EAAA,CAAAC,cAAA,cAOC;IAQCD,EAPA,CAAAuB,SAAA,eAME,eAOA;IACJvB,EAAA,CAAAG,YAAA,EAAM;;;;;IAiCVH,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAMFH,EAJF,CAAAC,cAAA,cAGC,eAC0B;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IACpDF,EADoD,CAAAG,YAAA,EAAO,EACrD;;;;IADqBH,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAK,iBAAA,CAAAmB,OAAA,CAAAjB,IAAA,WAAyB;;;;;IAfxDP,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAQ,UAAA,IAAAiB,2DAAA,kBAGC;IAGDzB,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAQ,UAAA,IAAAkB,2DAAA,kBAGC;IAIL1B,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAbDH,EAAA,CAAAI,SAAA,EAAqC;IAArCJ,EAAA,CAAAW,UAAA,SAAAC,MAAA,CAAAe,kBAAA,CAAAb,MAAA,OAAqC;IAOnBd,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAW,UAAA,YAAAC,MAAA,CAAAe,kBAAA,CAAqB;;;;;;IAlE5C3B,EALF,CAAAC,cAAA,cAIC,cACgE;IAAtCD,EAAA,CAAA4B,UAAA,mBAAAC,oEAAA;MAAA7B,EAAA,CAAA8B,aAAA,CAAAC,GAAA;MAAA,MAAAnB,MAAA,GAAAZ,EAAA,CAAAgC,aAAA;MAAA,OAAAhC,EAAA,CAAAiC,WAAA,CAASrB,MAAA,CAAAsB,mBAAA,CAAoB,MAAM,CAAC;IAAA,EAAC;IAE1DlC,EADF,CAAAC,cAAA,cAA4B,cACD;IACvBD,EAAA,CAAAQ,UAAA,IAAA2B,yDAAA,kBAOC;IAgBHnC,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAC9BF,EAD8B,CAAAG,YAAA,EAAK,EAC7B;IAEJH,EADF,CAAAC,cAAA,cAA4B,eACG;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,kBAA+C;;IAC7CD,EAAA,CAAAC,cAAA,eAUC;IACCD,EAAA,CAAAuB,SAAA,gBAME;IAIVvB,EAHM,CAAAG,YAAA,EAAM,EACC,EACL,EACF;IACNH,EAAA,CAAAQ,UAAA,KAAA4B,qDAAA,kBAGC;IAgBHpC,EAAA,CAAAG,YAAA,EAAM;;;;IA3EJH,EAAA,CAAAqC,WAAA,cAAAzB,MAAA,CAAAe,kBAAA,CAAAb,MAAA,KAAiD;IAWxCd,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAW,UAAA,SAAAC,MAAA,CAAA0B,SAAA,qBAAmC;IA4BpCtC,EAAA,CAAAI,SAAA,GAIC;IAJDJ,EAAA,CAAAuC,WAAA,cAAA3B,MAAA,CAAA4B,uBAAA,6CAIC;IAeNxC,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAW,UAAA,SAAAC,MAAA,CAAA4B,uBAAA,SAAqC;;;;;IAqFtCxC,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAMFH,EAJF,CAAAC,cAAA,cAGC,eAC0B;IAAAD,EAAA,CAAAE,MAAA,GAEvB;IACJF,EADI,CAAAG,YAAA,EAAO,EACL;;;;IAHqBH,EAAA,CAAAI,SAAA,GAEvB;IAFuBJ,EAAA,CAAAK,iBAAA,CAAAoC,OAAA,CAAAlC,IAAA,gBAEvB;;;;;IAjBRP,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAQ,UAAA,IAAAkC,2DAAA,kBAGC;IAGD1C,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAQ,UAAA,IAAAmC,2DAAA,kBAGC;IAML3C,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAfDH,EAAA,CAAAI,SAAA,EAA0C;IAA1CJ,EAAA,CAAAW,UAAA,SAAAC,MAAA,CAAAgC,uBAAA,CAAA9B,MAAA,OAA0C;IAOxBd,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAW,UAAA,YAAAC,MAAA,CAAAgC,uBAAA,CAA0B;;;;;;IApEjD5C,EALF,CAAAC,cAAA,cAIC,cAIE;IADCD,EAAA,CAAA4B,UAAA,mBAAAiB,oEAAA;MAAA7C,EAAA,CAAA8B,aAAA,CAAAgB,GAAA;MAAA,MAAAlC,MAAA,GAAAZ,EAAA,CAAAgC,aAAA;MAAA,OAAAhC,EAAA,CAAAiC,WAAA,CAASrB,MAAA,CAAAsB,mBAAA,CAAoB,WAAW,CAAC;IAAA,EAAC;IAGxClC,EADF,CAAAC,cAAA,cAA4B,cACD;;IACvBD,EAAA,CAAAC,cAAA,cAMC;IAQCD,EAPA,CAAAuB,SAAA,eAME,eAOA;IAENvB,EADE,CAAAG,YAAA,EAAM,EACF;;IACNH,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IACnCF,EADmC,CAAAG,YAAA,EAAK,EAClC;IAEJH,EADF,CAAAC,cAAA,cAA4B,gBACG;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,kBAA+C;;IAC7CD,EAAA,CAAAC,cAAA,eAUC;IACCD,EAAA,CAAAuB,SAAA,gBAME;IAIVvB,EAHM,CAAAG,YAAA,EAAM,EACC,EACL,EACF;IACNH,EAAA,CAAAQ,UAAA,KAAAuC,qDAAA,kBAGC;IAkBH/C,EAAA,CAAAG,YAAA,EAAM;;;;IA/EJH,EAAA,CAAAqC,WAAA,cAAAzB,MAAA,CAAAgC,uBAAA,CAAA9B,MAAA,KAAsD;IAyC9Cd,EAAA,CAAAI,SAAA,IAIC;IAJDJ,EAAA,CAAAuC,WAAA,cAAA3B,MAAA,CAAA4B,uBAAA,kDAIC;IAeNxC,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAW,UAAA,SAAAC,MAAA,CAAA4B,uBAAA,cAA0C;;;;;;IApiBjDxC,EAFJ,CAAAC,cAAA,cAAsE,cACtC,SACxB;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IACrBF,EADqB,CAAAG,YAAA,EAAK,EACpB;IAGNH,EAAA,CAAAC,cAAA,cAAwC;;IAEtCD,EAAA,CAAAC,cAAA,cAIC;IAgCCD,EA9BA,CAAAuB,SAAA,eAOE,eAUA,eAUA,eAUA;IACJvB,EAAA,CAAAG,YAAA,EAAM;;IAIJH,EADF,CAAAC,cAAA,eAA8B,eACD;;IAGrBD,EAFJ,CAAAC,cAAA,eAAsD,YAC9C,0BAOH;IAECD,EADA,CAAAuB,SAAA,gBAAoD,gBACF;IAEtDvB,EADE,CAAAG,YAAA,EAAiB,EACZ;IAUPH,EATA,CAAAuB,SAAA,kBAQE,kBAeA;IACJvB,EAAA,CAAAG,YAAA,EAAM;;IAEJH,EADF,CAAAC,cAAA,eAA8B,eACK;IAC/BD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAG1CF,EAH0C,CAAAG,YAAA,EAAM,EACtC,EACF,EACF;IAUFH,EAPJ,CAAAC,cAAA,eAAqB,eAMlB,eAIE;IADCD,EAAA,CAAA4B,UAAA,mBAAAoB,8DAAA;MAAAhD,EAAA,CAAA8B,aAAA,CAAAmB,GAAA;MAAA,MAAArC,MAAA,GAAAZ,EAAA,CAAAgC,aAAA;MAAA,OAAAhC,EAAA,CAAAiC,WAAA,CAASrB,MAAA,CAAAsB,mBAAA,CAAoB,QAAQ,CAAC;IAAA,EAAC;IAGrClC,EADF,CAAAC,cAAA,eAA4B,eACD;;IACvBD,EAAA,CAAAC,cAAA,eAMC;IAQCD,EAPA,CAAAuB,SAAA,gBAME,gBAOA;IAENvB,EADE,CAAAG,YAAA,EAAM,EACF;;IACNH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IACtCF,EADsC,CAAAG,YAAA,EAAK,EACrC;IAEJH,EADF,CAAAC,cAAA,eAA4B,gBACG;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,kBAA+C;;IAC7CD,EAAA,CAAAC,cAAA,eAUC;IACCD,EAAA,CAAAuB,SAAA,gBAME;IAIVvB,EAHM,CAAAG,YAAA,EAAM,EACC,EACL,EACF;IACNH,EAAA,CAAAQ,UAAA,KAAA0C,8CAAA,kBAGC;IAkBHlD,EAAA,CAAAG,YAAA,EAAM;;IAOJH,EAJF,CAAAC,cAAA,eAGC,eAIE;IADCD,EAAA,CAAA4B,UAAA,mBAAAuB,8DAAA;MAAAnD,EAAA,CAAA8B,aAAA,CAAAmB,GAAA;MAAA,MAAArC,MAAA,GAAAZ,EAAA,CAAAgC,aAAA;MAAA,OAAAhC,EAAA,CAAAiC,WAAA,CAASrB,MAAA,CAAAsB,mBAAA,CAAoB,WAAW,CAAC;IAAA,EAAC;IAGxClC,EADF,CAAAC,cAAA,eAA4B,eACD;;IACvBD,EAAA,CAAAC,cAAA,eAMC;IA2CCD,EA1CA,CAAAuB,SAAA,gBAME,gBAOA,gBAOA,gBAOA,gBAOA,gBAOA,gBAOA;IAENvB,EADE,CAAAG,YAAA,EAAM,EACF;;IACNH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IACtCF,EADsC,CAAAG,YAAA,EAAK,EACrC;IAEJH,EADF,CAAAC,cAAA,eAA4B,gBACG;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,kBAA+C;;IAC7CD,EAAA,CAAAC,cAAA,eAUC;IACCD,EAAA,CAAAuB,SAAA,gBAME;IAIVvB,EAHM,CAAAG,YAAA,EAAM,EACC,EACL,EACF;IACNH,EAAA,CAAAQ,UAAA,KAAA4C,8CAAA,kBAGC;IAkBHpD,EAAA,CAAAG,YAAA,EAAM;;IASJH,EAJF,CAAAC,cAAA,eAGC,eACiE;IAAvCD,EAAA,CAAA4B,UAAA,mBAAAyB,8DAAA;MAAArD,EAAA,CAAA8B,aAAA,CAAAmB,GAAA;MAAA,MAAArC,MAAA,GAAAZ,EAAA,CAAAgC,aAAA;MAAA,OAAAhC,EAAA,CAAAiC,WAAA,CAASrB,MAAA,CAAAsB,mBAAA,CAAoB,OAAO,CAAC;IAAA,EAAC;IAE3DlC,EADF,CAAAC,cAAA,eAA4B,eACD;;IACvBD,EAAA,CAAAC,cAAA,eAMC;IAsBCD,EArBA,CAAAuB,SAAA,gBAME,gBAOA,gBAOA,gBAOA;IAENvB,EADE,CAAAG,YAAA,EAAM,EACF;;IACNH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IACjCF,EADiC,CAAAG,YAAA,EAAK,EAChC;IAEJH,EADF,CAAAC,cAAA,eAA4B,gBACG;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,kBAA+C;;IAC7CD,EAAA,CAAAC,cAAA,eAUC;IACCD,EAAA,CAAAuB,SAAA,gBAME;IAIVvB,EAHM,CAAAG,YAAA,EAAM,EACC,EACL,EACF;IACNH,EAAA,CAAAQ,UAAA,KAAA8C,8CAAA,kBAGC;IAgBHtD,EAAA,CAAAG,YAAA,EAAM;IAoFNH,EAjFA,CAAAQ,UAAA,KAAA+C,8CAAA,mBAIC,KAAAC,8CAAA,mBAiFA;IAkFPxD,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IAreMH,EAAA,CAAAI,SAAA,IAA8B;IAC9BJ,EADA,CAAAuC,WAAA,yBAA8B,kCAAA3B,MAAA,CAAA6C,6BAAA,OAG7B;IAMDzD,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAA0D,kBAAA,MAAA9C,MAAA,CAAA6C,6BAAA,OACF;IAYFzD,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAqC,WAAA,cAAAzB,MAAA,CAAAC,oBAAA,CAAAC,MAAA,KAAmD;IAyC3Cd,EAAA,CAAAI,SAAA,IAIC;IAJDJ,EAAA,CAAAuC,WAAA,cAAA3B,MAAA,CAAA4B,uBAAA,+CAIC;IAeNxC,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAW,UAAA,SAAAC,MAAA,CAAA4B,uBAAA,WAAuC;IAwB1CxC,EAAA,CAAAI,SAAA,EAAsD;IAAtDJ,EAAA,CAAAqC,WAAA,cAAAzB,MAAA,CAAAM,uBAAA,CAAAJ,MAAA,KAAsD;IA4E9Cd,EAAA,CAAAI,SAAA,IAIC;IAJDJ,EAAA,CAAAuC,WAAA,cAAA3B,MAAA,CAAA4B,uBAAA,kDAIC;IAeNxC,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAW,UAAA,SAAAC,MAAA,CAAA4B,uBAAA,cAA0C;IA0B7CxC,EAAA,CAAAI,SAAA,EAAkD;IAAlDJ,EAAA,CAAAqC,WAAA,cAAAzB,MAAA,CAAAU,mBAAA,CAAAR,MAAA,KAAkD;IAoD1Cd,EAAA,CAAAI,SAAA,IAIC;IAJDJ,EAAA,CAAAuC,WAAA,cAAA3B,MAAA,CAAA4B,uBAAA,8CAIC;IAeNxC,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAW,UAAA,SAAAC,MAAA,CAAA4B,uBAAA,UAAsC;IAqBxCxC,EAAA,CAAAI,SAAA,EAAmC;IAAnCJ,EAAA,CAAAW,UAAA,SAAAC,MAAA,CAAA0B,SAAA,qBAAmC;IAiFnCtC,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAW,UAAA,SAAAC,MAAA,CAAA0B,SAAA,kBAAgC;;;;;IA4FvCtC,EAAA,CAAAC,cAAA,cAAoD;IAClDD,EAAA,CAAAuB,SAAA,8BAAyG;IAC3GvB,EAAA,CAAAG,YAAA,EAAM;;;;;IAKFH,EADF,CAAAC,cAAA,cAAqE,QAChE;IAAAD,EAAA,CAAAE,MAAA,0EAAmE;IACxEF,EADwE,CAAAG,YAAA,EAAI,EACtE;;;;;IA8BEH,EAFJ,CAAAC,cAAA,eAA2H,eAC3F,SACxB;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAAE,MAAA,GACF;IAEJF,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IAHAH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAA0D,kBAAA,OAAAC,aAAA,CAAAC,QAAA,kBAAAD,aAAA,CAAAC,QAAA,CAAAA,QAAA,kBAAAD,aAAA,CAAAC,QAAA,CAAAA,QAAA,CAAAC,OAAA,kBAAAF,aAAA,CAAAC,QAAA,CAAAA,QAAA,CAAAC,OAAA,qBAAAF,aAAA,CAAAC,QAAA,CAAAA,QAAA,CAAAC,OAAA,IAAAC,IAAA,MAAAH,aAAA,CAAAC,QAAA,kBAAAD,aAAA,CAAAC,QAAA,CAAAG,aAAA,kBAAAJ,aAAA,CAAAC,QAAA,CAAAG,aAAA,CAAAC,MAAA,2CACF;;;;;IAWEhE,EADF,CAAAC,cAAA,eAA6D,aACnD;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,GAChC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAD0BH,EAAA,CAAAI,SAAA,GAChC;IADgCJ,EAAA,CAAA0D,kBAAA,MAAAO,cAAA,CAAAC,WAAA,MAChC;;;;;IAEElE,EADF,CAAAC,cAAA,eAA8D,aACpD;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,GACpC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAD8BH,EAAA,CAAAI,SAAA,GACpC;IADoCJ,EAAA,CAAA0D,kBAAA,MAAAO,cAAA,CAAAE,eAAA,MACpC;;;;;IATFnE,EAAA,CAAAC,cAAA,eAGC;IAICD,EAHA,CAAAQ,UAAA,IAAA4D,sEAAA,mBAA6D,IAAAC,sEAAA,mBAGC;IAG9DrE,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAyB,SACnB;IAAAD,EAAA,CAAAE,MAAA,GAAsD;IAE9DF,EAF8D,CAAAG,YAAA,EAAK,EAC3D,EACF;;;;;IAZEH,EAAA,CAAAI,SAAA,EAA4B;IAA5BJ,EAAA,CAAAW,UAAA,SAAAsD,cAAA,CAAAC,WAAA,CAA4B;IAG5BlE,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAW,UAAA,SAAAsD,cAAA,CAAAE,eAAA,CAAgC;IAIpCnE,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAA0D,kBAAA,MAAAO,cAAA,CAAAK,GAAA,MACF;IAEMtE,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAA0D,kBAAA,cAAAO,cAAA,CAAAM,OAAA,eAAAC,KAAA,UAAsD;;;;;IAfhExE,EAAA,CAAAC,cAAA,eAA8H;IAC5HD,EAAA,CAAAQ,UAAA,IAAAiE,gEAAA,mBAGC;IAcHzE,EAAA,CAAAG,YAAA,EAAM;;;;IAhBqBH,EAAA,CAAAI,SAAA,EAA2D;IAA3DJ,EAAA,CAAAW,UAAA,YAAAgD,aAAA,CAAAC,QAAA,kBAAAD,aAAA,CAAAC,QAAA,CAAAG,aAAA,kBAAAJ,aAAA,CAAAC,QAAA,CAAAG,aAAA,CAAAW,KAAA,kBAAAf,aAAA,CAAAC,QAAA,CAAAG,aAAA,CAAAW,KAAA,CAAAC,YAAA,CAA2D;;;;;IAqBlF3E,EAFJ,CAAAC,cAAA,eAAmE,eACtC,aACjB;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,sDAC1B;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IApDJH,EARJ,CAAAC,cAAA,cAMC,cAC+B,aACJ;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACvDH,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAIFH,EAFJ,CAAAC,cAAA,cAA+B,cACH,aAChB;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IAC1B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAAiC;IAC/BD,EAAA,CAAAE,MAAA,IACF;;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAkCNH,EA/BA,CAAAQ,UAAA,KAAAoE,0DAAA,mBAA2H,KAAAC,0DAAA,mBAUG,KAAAC,0DAAA,mBAqB3D;IAKrE9E,EAAA,CAAAG,YAAA,EAAM;;;;;IAxDJH,EAFA,CAAAqC,WAAA,YAAAsB,aAAA,CAAAoB,MAAA,eAAgD,WAAApB,aAAA,CAAAoB,MAAA,cACF,YAAApB,aAAA,CAAAoB,MAAA,eACE;IAGtB/E,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAK,iBAAA,CAAAsD,aAAA,CAAAqB,SAAA,CAAyB;IACnBhF,EAAA,CAAAI,SAAA,EAA0B;IAA1BJ,EAAA,CAAAiF,UAAA,CAAAtB,aAAA,CAAAoB,MAAA,CAA0B;IACtD/E,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAA0D,kBAAA,MAAAC,aAAA,CAAAoB,MAAA,6BAAApB,aAAA,CAAAoB,MAAA,0CACF;IAK0B/E,EAAA,CAAAI,SAAA,GAC1B;IAD0BJ,EAAA,CAAA0D,kBAAA,MAAAC,aAAA,CAAAuB,WAAA,MAC1B;IAGElF,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAA0D,kBAAA,MAAA1D,EAAA,CAAAmF,WAAA,SAAAxB,aAAA,CAAAyB,SAAA,iBACF;IAIIpF,EAAA,CAAAI,SAAA,GAAwF;IAAxFJ,EAAA,CAAAW,UAAA,SAAAgD,aAAA,CAAAoB,MAAA,kBAAApB,aAAA,CAAAC,QAAA,IAAAhD,MAAA,CAAA0B,SAAA,kBAAwF;IAUxFtC,EAAA,CAAAI,SAAA,EAA2F;IAA3FJ,EAAA,CAAAW,UAAA,SAAAgD,aAAA,CAAAoB,MAAA,kBAAApB,aAAA,CAAAC,QAAA,IAAAhD,MAAA,CAAA0B,SAAA,qBAA2F;IAqB3FtC,EAAA,CAAAI,SAAA,EAAmC;IAAnCJ,EAAA,CAAAW,UAAA,SAAAgD,aAAA,CAAAoB,MAAA,cAAmC;;;;;IAzD7C/E,EAAA,CAAAC,cAAA,cAAwE;IACtED,EAAA,CAAAQ,UAAA,IAAA6E,mDAAA,oBAMC;IAwDHrF,EAAA,CAAAG,YAAA,EAAM;;;;IA7DoBH,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAW,UAAA,YAAAC,MAAA,CAAA0E,gBAAA,CAAqB;;;;;IAfjDtF,EADF,CAAAC,cAAA,cAAgE,SAC1D;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGrBH,EAAA,CAAAQ,UAAA,IAAA+E,6CAAA,kBAAoD;IAKpDvF,EAAA,CAAAC,cAAA,cAAyC;IAKvCD,EAJA,CAAAQ,UAAA,IAAAgF,6CAAA,kBAAqE,IAAAC,6CAAA,kBAIG;IAiE5EzF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IA3EEH,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAW,UAAA,SAAAC,MAAA,CAAA8E,eAAA,CAAqB;IAMnB1F,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAW,UAAA,SAAAC,MAAA,CAAA0E,gBAAA,CAAAxE,MAAA,OAAmC;IAInCd,EAAA,CAAAI,SAAA,EAAiC;IAAjCJ,EAAA,CAAAW,UAAA,SAAAC,MAAA,CAAA0E,gBAAA,CAAAxE,MAAA,KAAiC;;;ADlpBnD;AAeA,WAAa6E,uBAAuB;EAA9B,MAAOA,uBAAuB;IAwHxBC,KAAA;IACAC,MAAA;IACAC,YAAA;IACAC,sBAAA;IACAC,YAAA;IACAC,aAAA;IACAC,WAAA;IACAC,oBAAA;IA9HVC,cAAc,GAAc,CAC1B;MAAEC,EAAE,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAgB,CAAE,EAC3C;MAAED,EAAE,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAc,CAAE,EAC7C;MAAED,EAAE,EAAE,cAAc;MAAEC,KAAK,EAAE,SAAS;MAAEC,QAAQ,EAAE;IAAI,CAAE,CACzD;IAED;IACAC,OAAO,GAAkB,IAAI;IAC7BlE,SAAS,GAAW,YAAY;IAChC0C,SAAS,GAAW,OAAO;IAC3ByB,WAAW,GAAW,EAAE;IAGxBC,cAAc;IAEd;IACAC,YAAY,GAAkB,EAAE;IAChCC,gBAAgB,GAAW,CAAC;IAC5BC,gBAAgB;IAChBC,SAAS,GAAY,KAAK;IAC1B/B,MAAM,GAAoBhF,eAAe,CAACgH,UAAU;IAEpD;IACAC,YAAY,GAAkB,EAAE;IAChCC,gBAAgB,GAAY,KAAK;IAEjCC,SAAS,GAAG,EAAE;IACdxB,eAAe,GAAY,KAAK;IAEhC;IACAyB,YAAY,GAAiB,EAAE;IAC/BC,mBAAmB,GAAQ,IAAI,CAAC,CAAC;IAEjC;IACA9B,gBAAgB,GAOX,EAAE;IAEA+B,SAAS;IAEhB;IACAC,kBAAkB,GAAgB,IAAI;IACtCC,kBAAkB,GAAY,KAAK;IACnCC,WAAW;IAEXC,kBAAkB,GAAG3H,WAAW,CAAC4H,kBAAkB,IAAI,KAAK;IAErDC,mBAAmB,GAAY,KAAK;IAC3CC,gBAAgB;IAERC,QAAQ,GAAG,IAAIzI,OAAO,EAAQ;IACtC0I,WAAW,GAAW,gBAAgB;IACtCC,QAAQ,GAAa,CACnB;MAAE1B,EAAE,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAS,CAAE,EAChC;MAAED,EAAE,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAW,CAAE,EAClC;MAAED,EAAE,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAc,CAAE,CACtC;IAED0B,QAAQ,GAAG,KAAK;IAChBC,UAAU;IACVC,WAAW,GAAU,EAAE;IACvBC,WAAW,GAAG,KAAK;IACnBC,WAAW,GAAY,KAAK;IAC5BC,aAAa,GAAW,EAAE;IAC1BC,UAAU,GAAU,EAAE;IACtBC,aAAa,GAAU,EAAE;IACzBC,QAAQ,GAAG,CAAC;IACZC,SAAS,GAAG,KAAK;IACjBC,WAAW,GAAW,EAAE;IAExBC,eAAe,GAAa,EAAE;IAC9BC,iBAAiB,GAAW,CAAC;IAC7BC,WAAW,GAAW,UAAU;IAEhC;IACAC,oBAAoB,GAAY,KAAK;IACrCC,cAAc,GAAW,WAAW;IAEpC;IACAC,mBAAmB,GAAQ,IAAI;IAC/BC,eAAe,GAAU,EAAE;IAC3BC,WAAW,GAAU,EAAE;IACvBC,WAAW,GAAU,EAAE;IACvBC,cAAc,GAAW,EAAE;IAC3BC,iBAAiB,GAAW,EAAE;IAC9BC,yBAAyB,GAAW,EAAE;IACtCC,sBAAsB,GAAU,EAAE;IAClCC,eAAe,GAAa,EAAE;IAC9BC,wBAAwB,GAAG,KAAK;IAChCC,sBAAsB,GAAG,IAAItK,OAAO,EAAW;IAC/CuK,gBAAgB,GAAU,EAAE;IAC5BC,SAAS,GAAW,EAAE;IACtBC,aAAa,GAAqB,EAAE;IAEpC;IACApG,6BAA6B,GAAW,CAAC;IACzC5C,oBAAoB,GAAU,EAAE;IAChCS,mBAAmB,GAAU,EAAE;IAC/BJ,uBAAuB,GAAU,EAAE;IACnC0B,uBAAuB,GAAU,EAAE;IACnCjB,kBAAkB,GAAU,EAAE;IAE9B;IACQmI,sBAAsB,GAA+B;MAC3DC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE,IAAI;MACXC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE,IAAI;MACfC,IAAI,EAAE;KACP;IAED;IAEAC,YACUxE,KAAqB,EACrBC,MAAc,EACdC,YAAiC,EACjCC,sBAA8C,EAC9CC,YAAiC,EACjCC,aAA4B,EAC5BC,WAAwB,EACxBC,oBAA0C;MAP1C,KAAAP,KAAK,GAALA,KAAK;MACL,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,YAAY,GAAZA,YAAY;MACZ,KAAAC,sBAAsB,GAAtBA,sBAAsB;MACtB,KAAAC,YAAY,GAAZA,YAAY;MACZ,KAAAC,aAAa,GAAbA,aAAa;MACb,KAAAC,WAAW,GAAXA,WAAW;MACX,KAAAC,oBAAoB,GAApBA,oBAAoB;MAE5B,IAAI,CAACkB,SAAS,GAAG,IAAI,CAACnB,WAAW,CAACmE,KAAK,CAAC;QACtCC,gBAAgB,EAAE,CAAC,IAAI,CAAC;QACxBC,aAAa,EAAE,CAAC,KAAK;OACtB,CAAC;IACJ;IAEAC,QAAQA,CAAA;MACNC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MAClD,IAAI,CAAClD,WAAW,GAAGmD,MAAM,CAACC,UAAU,EAAE;MAEtC,IAAI,CAAChF,KAAK,CAACiF,MAAM,CAACC,SAAS,CAAED,MAAM,IAAI;QACrC,IAAI,CAACvI,SAAS,GAAGuI,MAAM,CAAC,MAAM,CAAC,IAAI,YAAY;QAC/CJ,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAACpI,SAAS,CAAC;MAC9D,CAAC,CAAC;MAEF,IAAI,CAACsD,KAAK,CAACmF,WAAW,CAACD,SAAS,CAAED,MAAM,IAAI;QAC1C,IAAIA,MAAM,CAAC,IAAI,CAAC,EAAE;UAChB,IAAI,CAACrE,OAAO,GAAGqE,MAAM,CAAC,IAAI,CAAC;UAC3B,IAAI,CAACG,aAAa,CAACH,MAAM,CAAC,IAAI,CAAC,CAAC;QAClC;MACF,CAAC,CAAC;MAEF;MACA,IAAI,CAAC7D,YAAY,GAAG,CAClB;QACEiE,IAAI,EAAE,IAAI;QACVnH,IAAI,EAAE,kBAAkB,IAAI,CAACkB,SAAS,IAAI,YAAY;OACvD,CACF;IACH;IAEAkG,WAAWA,CAAA;MACT,IAAI,CAACrD,QAAQ,CAACsD,IAAI,EAAE;MACpB,IAAI,CAACtD,QAAQ,CAACuD,QAAQ,EAAE;MACxB,IAAI,IAAI,CAACxD,gBAAgB,EAAE;QACzByD,aAAa,CAAC,IAAI,CAACzD,gBAAgB,CAAC;MACtC;IACF;IAEA0D,WAAWA,CAACC,KAAoC;MAC9C,IAAI,CAAC1C,WAAW,GAAG0C,KAAK,CAAClF,EAAE;MAC3B,IAAI,CAACyB,WAAW,GAAGyD,KAAK,CAACjF,KAAK;IAChC;IAEA0E,aAAaA,CAACxE,OAAe;MAC3B,IAAI,CAACiC,SAAS,GAAG,IAAI;MAErB;MACA,IAAI,IAAI,CAACnG,SAAS,KAAK,eAAe,EAAE;QACtC,IAAI,CAACwD,YAAY,CAAC0F,gCAAgC,CAAChF,OAAO,CAAC,CAACsE,SAAS,CAAC;UACpEK,IAAI,EAAGvH,QAAa,IAAI;YACtB,IAAI,CAAC6H,uBAAuB,CAAC7H,QAAQ,CAAC;UACxC,CAAC;UACD8H,KAAK,EAAGA,KAAU,IAAI;YACpBjB,OAAO,CAACiB,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;YAC1D,IAAI,CAACjD,SAAS,GAAG,KAAK;UACxB;SACD,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAAC3C,YAAY,CAAC6F,YAAY,CAACnF,OAAO,CAAC,CAACsE,SAAS,CAAC;UAChDK,IAAI,EAAGvH,QAAa,IAAI;YACtB,IAAI,CAAC6H,uBAAuB,CAAC7H,QAAQ,CAAC;UACxC,CAAC;UACD8H,KAAK,EAAGA,KAAU,IAAI;YACpBjB,OAAO,CAACiB,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;YACvD,IAAI,CAACjD,SAAS,GAAG,KAAK;UACxB;SACD,CAAC;MACJ;IACF;IAEQgD,uBAAuBA,CAAC7H,QAAa;MAC3C,IAAI,CAAC6E,SAAS,GAAG,KAAK;MAEtB;MACA,IAAImD,SAAS;MACb,IACEhI,QAAQ,CAACiI,YAAY,IACrBC,KAAK,CAACC,OAAO,CAACnI,QAAQ,CAACiI,YAAY,CAAC,IACpCjI,QAAQ,CAACiI,YAAY,CAAC/K,MAAM,GAAG,CAAC,EAChC;QACA8K,SAAS,GAAGhI,QAAQ,CAACiI,YAAY,CAAC,CAAC,CAAC;MACtC,CAAC,MAAM,IAAIjI,QAAQ,CAAC6C,WAAW,EAAE;QAC/BmF,SAAS,GAAGhI,QAAQ,CAAC6C,WAAW;MAClC,CAAC,MAAM,IAAI7C,QAAQ,CAACoI,IAAI,EAAE;QACxBJ,SAAS,GAAGhI,QAAQ,CAACoI,IAAI;MAC3B,CAAC,MAAM;QACLJ,SAAS,GAAGhI,QAAQ;MACtB;MAEA,IAAIgI,SAAS,EAAE;QACb,IAAI,CAAC5C,mBAAmB,GAAG4C,SAAS;QACpC;QACA,IAAI,IAAI,CAACtJ,SAAS,KAAK,YAAY,EAAE;UACnC,IAAI,CAAC0C,SAAS,GAAG4G,SAAS,CAACK,WAAW,IAAIL,SAAS,CAACrL,IAAI,IAAIqL,SAAS,CAAC5G,SAAS,IAAI,OAAO;QAC5F,CAAC,MAAM;UACL,IAAI,CAACA,SAAS,GAAG4G,SAAS,CAACrL,IAAI,IAAIqL,SAAS,CAAC5G,SAAS,IAAI,OAAO;QACnE;QACA,IAAI,CAACyB,WAAW,GAAGmF,SAAS,CAAC1H,WAAW,IAAI0H,SAAS,CAACnF,WAAW,IAAI,EAAE;QAEvE;QACA,IAAI,IAAI,CAACnE,SAAS,KAAK,YAAY,EAAE;UACnC;UACA,IAAI,CAAC8G,cAAc,GACjBwC,SAAS,CAACK,WAAW,IAAIL,SAAS,CAACrL,IAAI,IAAI,cAAc;UAE3D;UACA,IAAI,CAAC8I,iBAAiB,GACpBuC,SAAS,CAACM,WAAW,IACrBN,SAAS,CAACK,WAAW,IACrBL,SAAS,CAACrL,IAAI,IACd,EAAE;UAEJ;UACA,IAAIqL,SAAS,CAACO,gBAAgB,EAAE;YAC9B,IAAI,CAAC7C,yBAAyB,GAAGsC,SAAS,CAACO,gBAAgB;UAC7D,CAAC,MAAM,IAAIP,SAAS,CAACM,WAAW,EAAE;YAChC,IAAI,CAAC5C,yBAAyB,GAAGsC,SAAS,CAACM,WAAW;UACxD,CAAC,MAAM,IAAIN,SAAS,CAACK,WAAW,EAAE;YAChC,IAAI,CAAC3C,yBAAyB,GAAGsC,SAAS,CAACK,WAAW;UACxD;QACF;QAEA;QACA,IAAI,IAAI,CAACjF,YAAY,CAAClG,MAAM,GAAG,CAAC,EAAE;UAChC,IAAI,CAACkG,YAAY,CAAC,CAAC,CAAC,CAAClD,IAAI,GAAG,kBAAkB,IAAI,CAACkB,SAAS,6BAA6B;QAC3F;QAEA;QACA,IAAI,CAACoH,cAAc,CAACR,SAAS,CAAC;MAChC;IACF;IAEQQ,cAAcA,CAACR,SAAc;MACnC;MACA,IAAI,CAACS,gCAAgC,CAACT,SAAS,CAAC;IAClD;IAEAU,iBAAiBA,CAACC,OAAe;MAC/B,IAAI,IAAI,CAACjK,SAAS,KAAK,YAAY,EAAE;QACnC;QACA,IACE,CAAC,IAAI,CAAC0G,mBAAmB,KACxB,CAAC,IAAI,CAACI,cAAc,IAAI,IAAI,CAACA,cAAc,KAAK,SAAS,CAAC,EAC3D;UACA,IAAI,CAACoD,cAAc,CACjB,+DAA+D,CAChE;UACD;QACF;QAEA,IAAIC,cAAc,GAAGF,OAAO;QAC5B,IAAI,IAAI,CAAChD,sBAAsB,CAACzI,MAAM,GAAG,CAAC,EAAE;UAC1C,MAAM4L,SAAS,GAAG,IAAI,CAACnD,sBAAsB,CAC1CoD,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACC,YAAY,CAAC,CAChCC,IAAI,CAAC,IAAI,CAAC;UACbL,cAAc,GAAG,GAAGF,OAAO,0BAA0BG,SAAS,EAAE;QAClE;QAEA;QACA,MAAMlF,WAAW,GAAGmD,MAAM,CAACC,UAAU,EAAE;QACvC,IAAI,CAACtF,gBAAgB,CAACyH,IAAI,CAAC;UACzB1G,EAAE,EAAEmB,WAAW;UACfxC,SAAS,EAAE,IAAI,CAACA,SAAS;UACzBE,WAAW,EAAEqH,OAAO;UACpBxH,MAAM,EAAE,SAAS;UAAE;UACnBK,SAAS,EAAE,IAAI4H,IAAI;SACpB,CAAC;QACFvC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,IAAI,CAACpF,gBAAgB,CAAC;QAErE,IAAI,CAAC0B,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;UAAEiE,IAAI,EAAE,MAAM;UAAEnH,IAAI,EAAE2I;QAAc,CAAE,CACvC;QACD,IAAI,CAACxF,gBAAgB,GAAG,IAAI;QAE5B,MAAMqD,gBAAgB,GACpB,IAAI,CAACjD,SAAS,CAAC4F,GAAG,CAAC,kBAAkB,CAAC,EAAEC,KAAK,IAAI,KAAK;QACxD,MAAM3C,aAAa,GAAG,IAAI,CAAClD,SAAS,CAAC4F,GAAG,CAAC,eAAe,CAAC,EAAEC,KAAK,IAAI,KAAK;QAEzEzC,OAAO,CAACC,GAAG,CACT,2CAA2C,EAC3CJ,gBAAgB,EAChB,gBAAgB,EAChBC,aAAa,CACd;QAED;QACA;QACA,MAAM4C,SAAS,GACb,IAAI,CAACvD,SAAS,IACd,IAAI,CAACP,iBAAiB,IACtB,IAAI,CAACL,mBAAmB,EAAEkD,WAAW,IACrC,IAAI,CAAClD,mBAAmB,EAAEiD,WAAW,IACrC,IAAI,CAACjD,mBAAmB,EAAEzI,IAAI,IAC9B,IAAI,CAAC6I,cAAc;QAErB,IAAIgE,iBAAiB,GAAG,IAAI,CAAC9D,yBAAyB;QACtD,IAAI,CAAC8D,iBAAiB,EAAE;UACtB;UACA,IAAI,IAAI,CAACpE,mBAAmB,EAAEmD,gBAAgB,EAAE;YAC9CiB,iBAAiB,GAAG,IAAI,CAACpE,mBAAmB,CAACmD,gBAAgB;UAC/D,CAAC,MAAM;YACL,MAAMkB,OAAO,GAAG,IAAI,CAACC,qBAAqB,EAAE;YAC5C,MAAMC,eAAe,GACnB,IAAI,CAACvE,mBAAmB,EAAEkD,WAAW,IACrC,IAAI,CAAClD,mBAAmB,EAAEiD,WAAW,IACrC,IAAI,CAACjD,mBAAmB,EAAEzI,IAAI,IAC9B4M,SAAS;YACXC,iBAAiB,GAAG,GAAGG,eAAe,GAAGF,OAAO,EAAE;UACpD;QACF;QAEA,IAAI,IAAI,CAAC9D,sBAAsB,CAACzI,MAAM,GAAG,CAAC,EAAE;UAC1C,IAAI,CAAC0M,+BAA+B,CAClCjB,OAAO,EACPY,SAAS,EACTC,iBAAiB,EACjB9C,gBAAgB,EAChBC,aAAa,CACd;UACD;QACF;QAEA,IAAI,CAACkD,qBAAqB,CACxBlB,OAAO,EACPY,SAAS,EACTC,iBAAiB,EACjB9C,gBAAgB,EAChBC,aAAa,CACd;MACH,CAAC,MAAM,IAAI,IAAI,CAACjI,SAAS,KAAK,eAAe,EAAE;QAC7C;QACA,MAAMkF,WAAW,GAAGmD,MAAM,CAACC,UAAU,EAAE;QACvC,IAAI,CAACtF,gBAAgB,CAACyH,IAAI,CAAC;UACzB1G,EAAE,EAAEmB,WAAW;UACfxC,SAAS,EAAE,IAAI,CAACA,SAAS;UACzBE,WAAW,EAAEqH,OAAO;UACpBxH,MAAM,EAAE,SAAS;UAAE;UACnBK,SAAS,EAAE,IAAI4H,IAAI;SACpB,CAAC;QACFvC,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAE,IAAI,CAACpF,gBAAgB,CAAC;QAEnF,IAAI,CAAC2B,gBAAgB,GAAG,IAAI;QAC5B;QACA,IAAI,CAACvB,eAAe,GAAG,IAAI;QAC3B,IAAI,CAACgI,iBAAiB,CAAC,QAAQ,CAAC;QAEhC,IAAIC,OAAO,GAAG;UACZnG,WAAW,EAAE,IAAI,CAACA,WAAW;UAC7BhB,OAAO,EAAEoH,MAAM,CAAC,IAAI,CAACpH,OAAO,CAAC;UAC7BqH,IAAI,EAAE,IAAI,CAAC7H,YAAY,CAAC8H,aAAa,EAAE,IAAI,uBAAuB;UAClEC,UAAU,EAAE;YAAEC,QAAQ,EAAEzB;UAAO;SAChC;QAED;QACA,IAAI,CAACvF,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;UAAEiE,IAAI,EAAE,MAAM;UAAEnH,IAAI,EAAEyI;QAAO,CAAE,CAChC;QAED,IAAI,IAAI,CAAChD,sBAAsB,CAACzI,MAAM,GAAG,CAAC,EAAE;UAC1C,MAAMmN,WAAW,GAAG,IAAI,CAAC1E,sBAAsB,CAAC,CAAC,CAAC;UAClD,IAAIkD,cAAsB;UAC1B,IAAI,IAAI,CAAClD,sBAAsB,CAACzI,MAAM,GAAG,CAAC,EAAE;YAC1C,MAAM4L,SAAS,GAAG,IAAI,CAACnD,sBAAsB,CAC1CoD,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACC,YAAY,CAAC,CAChCC,IAAI,CAAC,IAAI,CAAC;YACbL,cAAc,GAAG,sBAAsBC,SAAS,EAAE;YAElD,IAAI,CAAC1F,YAAY,GAAG,CAAC;cAAEiE,IAAI,EAAE,MAAM;cAAEnH,IAAI,EAAE2I;YAAc,CAAE,CAAC;UAC9D;UACA,IAAI,CAAC1G,sBAAsB,CACxBmI,0BAA0B,CAACP,OAAO,EAAEM,WAAW,CAAC,CAChDE,IAAI,CACH5O,QAAQ,CAAC,MAAK;YACZ,IAAI,CAAC0H,gBAAgB,GAAG,KAAK;YAC7B,IAAI,CAACwC,wBAAwB,GAAG,KAAK;YACrC,IAAI,CAAC/D,eAAe,GAAG,KAAK;UAC9B,CAAC,CAAC,EACFrG,SAAS,CAAC,IAAI,CAACqK,sBAAsB,CAAC,CACvC,CACAoB,SAAS,CAAC;YACTK,IAAI,EAAGiD,GAAG,IAAK,IAAI,CAACC,0BAA0B,CAACD,GAAG,EAAE7B,OAAO,CAAC;YAC5Db,KAAK,EAAG4C,GAAQ,IAAI;cAClB;cACA,IAAI,IAAI,CAAChJ,gBAAgB,CAACxE,MAAM,GAAG,CAAC,EAAE;gBACpC,IAAI,CAACwE,gBAAgB,CAAC,IAAI,CAACA,gBAAgB,CAACxE,MAAM,GAAG,CAAC,CAAC,CAACiE,MAAM,GAAG,QAAQ;cAC3E;cAEA,MAAMwJ,YAAY,GAChBD,GAAG,EAAE5C,KAAK,EAAEa,OAAO,IACnB+B,GAAG,EAAE/B,OAAO,IACZ,kDAAkD;cAEpD;cACA,IAAI,IAAI,CAACvF,YAAY,CAAClG,MAAM,GAAG,CAAC,EAAE;gBAChC,MAAM0N,WAAW,GAAG,IAAI,CAACxH,YAAY,CAAC,IAAI,CAACA,YAAY,CAAClG,MAAM,GAAG,CAAC,CAAC;gBACnE,IAAI0N,WAAW,CAACvD,IAAI,KAAK,IAAI,IAAIuD,WAAW,CAAC1K,IAAI,CAAC2K,QAAQ,CAAC,iBAAiB,CAAC,EAAE;kBAC7ED,WAAW,CAAC1K,IAAI,GAAG,GAAG,IAAI,CAACkB,SAAS,mBAAmB;gBACzD;cACF;cAEA;cACA,IAAI,CAACgC,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;gBAAEiE,IAAI,EAAE,IAAI;gBAAEnH,IAAI,EAAE,GAAG,IAAI,CAACkB,SAAS;cAAmB,CAAE,CAC3D;cAED;cACA,IAAI,CAAC0I,iBAAiB,CAAC,QAAQ,CAAC;YAClC;WACD,CAAC;QACN,CAAC,MAAM;UACL,IAAI,CAAC3H,sBAAsB,CACxB2I,kBAAkB,CAACf,OAAO,CAAC,CAC3BQ,IAAI,CACH5O,QAAQ,CAAC,MAAK;YACZ,IAAI,CAAC0H,gBAAgB,GAAG,KAAK;YAC7B,IAAI,CAACwC,wBAAwB,GAAG,KAAK;YACrC,IAAI,CAAC/D,eAAe,GAAG,KAAK;UAC9B,CAAC,CAAC,EACFrG,SAAS,CAAC,IAAI,CAACqK,sBAAsB,CAAC,CACvC,CACAoB,SAAS,CAAC;YACTK,IAAI,EAAGiD,GAAG,IAAK,IAAI,CAACC,0BAA0B,CAACD,GAAG,EAAE7B,OAAO,CAAC;YAC5Db,KAAK,EAAG4C,GAAQ,IAAI;cAClB;cACA,IAAI,IAAI,CAAChJ,gBAAgB,CAACxE,MAAM,GAAG,CAAC,EAAE;gBACpC,IAAI,CAACwE,gBAAgB,CAAC,IAAI,CAACA,gBAAgB,CAACxE,MAAM,GAAG,CAAC,CAAC,CAACiE,MAAM,GAAG,QAAQ;cAC3E;cAEA,MAAMwJ,YAAY,GAChBD,GAAG,EAAE5C,KAAK,EAAEa,OAAO,IACnB+B,GAAG,EAAE/B,OAAO,IACZ,kDAAkD;cAEpD;cACA,IAAI,IAAI,CAACvF,YAAY,CAAClG,MAAM,GAAG,CAAC,EAAE;gBAChC,MAAM0N,WAAW,GAAG,IAAI,CAACxH,YAAY,CAAC,IAAI,CAACA,YAAY,CAAClG,MAAM,GAAG,CAAC,CAAC;gBACnE,IAAI0N,WAAW,CAACvD,IAAI,KAAK,IAAI,IAAIuD,WAAW,CAAC1K,IAAI,CAAC2K,QAAQ,CAAC,iBAAiB,CAAC,EAAE;kBAC7ED,WAAW,CAAC1K,IAAI,GAAG,GAAG,IAAI,CAACkB,SAAS,mBAAmB;gBACzD;cACF;cAEA;cACA,IAAI,CAACgC,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;gBAAEiE,IAAI,EAAE,IAAI;gBAAEnH,IAAI,EAAE,GAAG,IAAI,CAACkB,SAAS;cAAmB,CAAE,CAC3D;cAED;cACA,IAAI,CAAC0I,iBAAiB,CAAC,QAAQ,CAAC;YAClC;WACD,CAAC;QACN;MACF;IACF;IAEAiB,eAAeA,CAAC5E,MAAsB;MACpC,IAAI,CAAC7C,SAAS,GAAG6C,MAAM,CAACxJ,IAAI,IAAIqO,MAAM,CAAC7E,MAAM,CAACmD,KAAK,CAAC,IAAI,EAAE;IAC5D;IAEA2B,gCAAgCA,CAAC3B,KAAc;MAC7C;MACA,IAAI,CAAC7F,SAAS,CAAC4F,GAAG,CAAC,kBAAkB,CAAC,EAAE6B,QAAQ,CAAC5B,KAAK,CAAC;MAEvD;MACA;MACA,IAAI,CAACA,KAAK,EAAE;QACV,IAAI,CAACvD,gBAAgB,GAAG,EAAE;QAC1Bc,OAAO,CAACC,GAAG,CACT,6DAA6D,CAC9D;MACH,CAAC,MAAM;QACLD,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;MACzE;IACF;IAEAqE,0BAA0BA,CAAC7B,KAAc;MACvC;MACA,IAAI,CAAC7F,SAAS,CAAC4F,GAAG,CAAC,eAAe,CAAC,EAAE6B,QAAQ,CAAC5B,KAAK,CAAC;MACpDzC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEwC,KAAK,CAAC;IAC9C;IAEA8B,eAAeA,CAACC,KAAY;MAC1B,IAAI,CAAC5G,aAAa,GAAG4G,KAAK;MAC1B;MACA,IAAI,CAAC1F,sBAAsB,GAAG0F,KAAK;IACrC;IAEAC,mBAAmBA,CAAA;MACjB;IAAA;IAGFC,QAAQA,CAAA;MACN;IAAA;IAGFC,aAAaA,CAACC,OAA8B;MAC1C;IAAA;IAGFC,mBAAmBA,CAACC,MAAiC;MACnD;IAAA;IAGFC,YAAYA,CAAA;MACV,IAAI,CAAC3J,MAAM,CAAC4J,QAAQ,CAAC,CAAC,eAAe,EAAE,IAAI,CAACnN,SAAS,CAAC,EAAE;QACtDyI,WAAW,EAAE;UAAE1E,EAAE,EAAE,IAAI,CAACG,OAAO;UAAEkJ,IAAI,EAAE;QAAM;OAC9C,CAAC;IACJ;IAEAC,SAASA,CAAA;MACP,IAAI,CAAC9J,MAAM,CAAC4J,QAAQ,CAAC,CAAC,eAAe,EAAE,IAAI,CAACnN,SAAS,CAAC,EAAE;QACtDyI,WAAW,EAAE;UAAE1E,EAAE,EAAE,IAAI,CAACG,OAAO;UAAEkJ,IAAI,EAAE;QAAM;OAC9C,CAAC;IACJ;IAEAE,oBAAoBA,CAAA;MAClB,IAAI,CAAC/J,MAAM,CAAC4J,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;IACzC;IAEAI,eAAeA,CAAA;MACb,IAAI,CAAC/G,oBAAoB,GAAG,CAAC,IAAI,CAACA,oBAAoB;IACxD;IAEA4E,iBAAiBA,CAACoC,GAAW;MAC3B,IAAI,CAAC/G,cAAc,GAAG+G,GAAG;IAC3B;IAEA;IACA5N,mBAAmBA,CAAC6N,QAAgB;MAClC,IAAI,CAACjG,sBAAsB,CAACiG,QAAQ,CAAC,GACnC,CAAC,IAAI,CAACjG,sBAAsB,CAACiG,QAAQ,CAAC;IAC1C;IAEAvN,uBAAuBA,CAACuN,QAAgB;MACtC,OAAO,IAAI,CAACjG,sBAAsB,CAACiG,QAAQ,CAAC,IAAI,KAAK;IACvD;IAEA;IACQvD,cAAcA,CAACD,OAAe;MACpC,IAAI,CAACvF,YAAY,GAAG,CAAC,GAAG,IAAI,CAACA,YAAY,EAAE;QAAEiE,IAAI,EAAE,IAAI;QAAEnH,IAAI,EAAEyI;MAAO,CAAE,CAAC;IAC3E;IAEQe,qBAAqBA,CAAA;MAC3B;MACA,OAAO,EAAE;IACX;IAEQ0C,qBAAqBA,CAAA;MAC3B;MACA,OAAO,EAAE;IACX;IAEA3B,0BAA0BA,CAACzK,QAAa,EAAE2I,OAAe;MACvD,IAAI;QACF;QACA,IAAI,IAAI,CAACjH,gBAAgB,CAACxE,MAAM,GAAG,CAAC,EAAE;UACpC,IAAI,CAACwE,gBAAgB,CAAC,IAAI,CAACA,gBAAgB,CAACxE,MAAM,GAAG,CAAC,CAAC,CAACiE,MAAM,GAAG,SAAS;UAC1E,IAAI,CAACO,gBAAgB,CAAC,IAAI,CAACA,gBAAgB,CAACxE,MAAM,GAAG,CAAC,CAAC,CAAC8C,QAAQ,GAAGA,QAAQ;UAC3E6G,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACpF,gBAAgB,CAAC;QAClE;QAEA,MAAM2K,SAAS,GAAGrM,QAAQ,EAAEG,aAAa,EAAEW,KAAK,EAAEwL,MAAM;QACxD,IAAIC,eAAe,GAAG,EAAE;QAExB,IAAIF,SAAS,EAAE;UACb;UACAE,eAAe,GAAGF,SAAS,CAACG,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC;QACnD,CAAC,MAAM;UACLD,eAAe,GAAGvM,QAAQ,EAAEG,aAAa,EAAEC,MAAM;QACnD;QAEA;QACA,IAAI,CAACgD,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;UAAEiE,IAAI,EAAE,IAAI;UAAEnH,IAAI,EAAE,GAAG,IAAI,CAACkB,SAAS;QAAoB,CAAE,CAC5D;QAED;QACA,IAAI,CAAC0I,iBAAiB,CAAC,QAAQ,CAAC;MAClC,CAAC,CAAC,OAAOY,GAAQ,EAAE;QACjB;QACA,IAAI,IAAI,CAAChJ,gBAAgB,CAACxE,MAAM,GAAG,CAAC,EAAE;UACpC,IAAI,CAACwE,gBAAgB,CAAC,IAAI,CAACA,gBAAgB,CAACxE,MAAM,GAAG,CAAC,CAAC,CAACiE,MAAM,GAAG,QAAQ;UACzE0F,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE,IAAI,CAACpF,gBAAgB,CAAC;QAC3E;QAEA;QACA,IAAI,CAAC0B,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;UAAEiE,IAAI,EAAE,IAAI;UAAEnH,IAAI,EAAE,GAAG,IAAI,CAACkB,SAAS;QAAmB,CAAE,CAC3D;QAED;QACA,IAAI,CAAC0I,iBAAiB,CAAC,QAAQ,CAAC;MAClC;IACF;IAEQF,+BAA+BA,CACrCjB,OAAe,EACfmD,IAAY,EACZtC,iBAAyB,EACzB9C,gBAAyB,EACzBC,aAAsB;MAEtB,MAAM8F,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/B,IAAI,CAAC/G,sBAAsB,CAACgH,OAAO,CAAEC,QAAQ,IAAI;QAC/C,IAAIA,QAAQ,CAAC5D,IAAI,EAAE;UACjByD,QAAQ,CAACI,MAAM,CAAC,OAAO,EAAED,QAAQ,CAAC5D,IAAI,CAAC;QACzC;MACF,CAAC,CAAC;MAEF,IAAIyD,QAAQ,CAACK,GAAG,CAAC,OAAO,CAAC,EAAE;QACzB,IAAI,CAAC3K,sBAAsB,CACxB4K,gBAAgB,CAACN,QAAQ,CAAC,CAC1BlC,IAAI,CACH7O,SAAS,CAAEsR,YAAY,IAAI;UACzB,MAAMC,WAAW,GACfD,YAAY,EAAEE,aAAa,EACvBnE,GAAG,CAAE/I,QAAa,IAAKA,QAAQ,CAACiN,WAAW,CAAC,EAC5C/D,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;UACtB,IAAI,CAACiE,8BAA8B,CACjCxE,OAAO,EACPmD,IAAI,EACJtC,iBAAiB,EACjB9C,gBAAgB,EAChBC,aAAa,EACbsG,WAAW,CACZ;UACD,OAAOpR,EAAE,CAAC,IAAI,CAAC;QACjB,CAAC,CAAC,EACFD,UAAU,CAAEkM,KAAK,IAAI;UACnBjB,OAAO,CAACiB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,IAAI,CAAC+B,qBAAqB,CACxBlB,OAAO,EACPmD,IAAI,EACJtC,iBAAiB,EACjB9C,gBAAgB,EAChBC,aAAa,CACd;UACD,OAAO9K,EAAE,CAAC,IAAI,CAAC;QACjB,CAAC,CAAC,CACH,CACAqL,SAAS,EAAE;MAChB,CAAC,MAAM;QACL,IAAI,CAAC2C,qBAAqB,CACxBlB,OAAO,EACPmD,IAAI,EACJtC,iBAAiB,EACjB9C,gBAAgB,EAChBC,aAAa,CACd;MACH;IACF;IAEQkD,qBAAqBA,CAC3BlB,OAAe,EACfmD,IAAY,EACZtC,iBAAyB,EACzB9C,gBAAyB,EACzBC,aAAsB;MAEtBE,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;QAClC6B,OAAO;QACPmD,IAAI;QACJtC,iBAAiB;QACjB9C,gBAAgB;QAChBC,aAAa;QACbyG,wBAAwB,EAAE,IAAI,CAACrH,gBAAgB,CAAC7I;OACjD,CAAC;MAEF,IAAIwJ,gBAAgB,EAAE;QACpB,IAAI,CAACX,gBAAgB,CAACoD,IAAI,CAAC;UAAEkE,OAAO,EAAE1E,OAAO;UAAE2E,IAAI,EAAE;QAAM,CAAE,CAAC;MAChE;MAEA,MAAMvD,OAAO,GAAGrD,gBAAgB,GAAG,IAAI,CAACX,gBAAgB,GAAG4C,OAAO;MAClE,MAAM;QAAE4E;MAAO,CAAE,GAAG,IAAI,CAACnB,qBAAqB,EAAE;MAEhDvF,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEiD,OAAO,CAAC;MAEjD;MACA,IAAI,CAACjI,eAAe,GAAG,IAAI;MAC3B,IAAI,CAACgI,iBAAiB,CAAC,QAAQ,CAAC;MAEhC,IAAI,CAAC3H,sBAAsB,CACxBqL,cAAc,CACbzD,OAAO,EACP+B,IAAI,EACJpF,gBAAgB,EAChBC,aAAa,EACb,IAAI,CAACf,eAAe,EACpB4D,iBAAiB,EACjB,EAAE,EACF+D,OAAO,CACR,CACAhD,IAAI,CACH5O,QAAQ,CAAC,MAAK;QACZ,IAAI,CAAC0H,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACwC,wBAAwB,GAAG,KAAK;QACrC,IAAI,CAAC/D,eAAe,GAAG,KAAK;MAC9B,CAAC,CAAC,EACFrG,SAAS,CAAC,IAAI,CAACqK,sBAAsB,CAAC,CACvC,CACAoB,SAAS,CAAC;QACTK,IAAI,EAAGkG,iBAAsB,IAAI;UAC/B;UACA,IAAI,CAAChD,0BAA0B,CAACgD,iBAAiB,EAAE9E,OAAO,CAAC;QAC7D,CAAC;QACDb,KAAK,EAAGA,KAAU,IAAI;UACpBjB,OAAO,CAACiB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;UAElC;UACA,IAAI,IAAI,CAACpG,gBAAgB,CAACxE,MAAM,GAAG,CAAC,EAAE;YACpC,IAAI,CAACwE,gBAAgB,CAAC,IAAI,CAACA,gBAAgB,CAACxE,MAAM,GAAG,CAAC,CAAC,CAACiE,MAAM,GAAG,QAAQ;UAC3E;UAEA,MAAMwJ,YAAY,GAChB7C,KAAK,EAAEA,KAAK,EAAEa,OAAO,IACrBb,KAAK,EAAEa,OAAO,IACd,kDAAkD;UAEpD;UACA,IAAI,CAACvF,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;YAAEiE,IAAI,EAAE,IAAI;YAAEnH,IAAI,EAAE,GAAG,IAAI,CAACkB,SAAS;UAAmB,CAAE,EAC1D;YAAEiG,IAAI,EAAE,IAAI;YAAEnH,IAAI,EAAEyK;UAAY,CAAE,CACnC;UAED;UACA,IAAI,CAACb,iBAAiB,CAAC,QAAQ,CAAC;UAEhC,IAAIpD,gBAAgB,IAAI,IAAI,CAACX,gBAAgB,CAAC7I,MAAM,GAAG,CAAC,EAAE;YACxD,IAAI,CAAC6I,gBAAgB,CAAC2H,GAAG,EAAE;UAC7B;QACF;OACD,CAAC;IACN;IAEQP,8BAA8BA,CACpCxE,OAAe,EACfmD,IAAY,EACZtC,iBAAyB,EACzB9C,gBAAyB,EACzBC,aAAsB,EACtBgH,YAAoB;MAEpB,IAAIjH,gBAAgB,EAAE;QACpB,IAAI,CAACX,gBAAgB,CAACoD,IAAI,CAAC;UAAEkE,OAAO,EAAE1E,OAAO;UAAE2E,IAAI,EAAE;QAAM,CAAE,CAAC;MAChE;MACA,MAAMvD,OAAO,GAAGrD,gBAAgB,GAAG,IAAI,CAACX,gBAAgB,GAAG4C,OAAO;MAClE,MAAM;QAAE4E;MAAO,CAAE,GAAG,IAAI,CAACnB,qBAAqB,EAAE;MAEhD;MACA,IAAI,CAACtK,eAAe,GAAG,IAAI;MAC3B,IAAI,CAACgI,iBAAiB,CAAC,QAAQ,CAAC;MAEhC,IAAI,CAAC3H,sBAAsB,CACxBqL,cAAc,CACbzD,OAAO,EACP+B,IAAI,EACJpF,gBAAgB,EAChBC,aAAa,EACb,IAAI,CAACf,eAAe,EACpB4D,iBAAiB,EACjBmE,YAAY,EACZJ,OAAO,CACR,CACAhD,IAAI,CACH5O,QAAQ,CAAC,MAAK;QACZ,IAAI,CAAC0H,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACwC,wBAAwB,GAAG,KAAK;QACrC,IAAI,CAAC/D,eAAe,GAAG,KAAK;MAC9B,CAAC,CAAC,EACFrG,SAAS,CAAC,IAAI,CAACqK,sBAAsB,CAAC,CACvC,CACAoB,SAAS,CAAC;QACTK,IAAI,EAAGkG,iBAAsB,IAAI;UAC/B;UACA,IAAI,CAAChD,0BAA0B,CAACgD,iBAAiB,EAAE9E,OAAO,CAAC;QAC7D,CAAC;QACDb,KAAK,EAAGA,KAAU,IAAI;UACpBjB,OAAO,CAACiB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;UAClC,MAAM6C,YAAY,GAChB7C,KAAK,EAAEA,KAAK,EAAEa,OAAO,IACrBb,KAAK,EAAEa,OAAO,IACd,kDAAkD;UAEpD;UACA,IAAI,CAACvF,YAAY,GAAG,CAClB,GAAG,IAAI,CAACA,YAAY,EACpB;YAAEiE,IAAI,EAAE,IAAI;YAAEnH,IAAI,EAAE,GAAG,IAAI,CAACkB,SAAS;UAAmB,CAAE,EAC1D;YAAEiG,IAAI,EAAE,IAAI;YAAEnH,IAAI,EAAEyK;UAAY,CAAE,CACnC;UAED,IAAIjE,gBAAgB,IAAI,IAAI,CAACX,gBAAgB,CAAC7I,MAAM,GAAG,CAAC,EAAE;YACxD,IAAI,CAAC6I,gBAAgB,CAAC2H,GAAG,EAAE;UAC7B;QACF;OACD,CAAC;IACN;IAEA;IACQjF,gCAAgCA,CAACT,SAAc;MACrD,IAAI,CAACA,SAAS,EAAE;QACdnB,OAAO,CAAC+G,IAAI,CAAC,sCAAsC,CAAC;QACpD;MACF;MAEA/G,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEkB,SAAS,CAAC;MAC7DnB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAACpI,SAAS,CAAC;MACpDmI,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE+G,MAAM,CAACC,IAAI,CAAC9F,SAAS,CAAC,CAAC;MAEjE;MACA,IAAI,CAAC3C,eAAe,GAAG,EAAE;MACzB,IAAI,CAACC,WAAW,GAAG,EAAE;MAErB,IAAIyI,WAAW,GAAG,CAAC;MAEnB;MACA,IAAI,IAAI,CAACrP,SAAS,KAAK,YAAY,EAAE;QACnC,IAAI,CAACsP,6BAA6B,CAAChG,SAAS,EAAE+F,WAAW,CAAC;MAC5D,CAAC,MAAM,IAAI,IAAI,CAACrP,SAAS,KAAK,eAAe,EAAE;QAC7C,IAAI,CAACuP,gCAAgC,CAACjG,SAAS,EAAE+F,WAAW,CAAC;MAC/D;MAEAlH,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE;QACxCzB,eAAe,EAAE,IAAI,CAACA,eAAe;QACrCC,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7B4I,UAAU,EAAE,IAAI,CAAC7I,eAAe,CAACnI;OAClC,CAAC;IACJ;IAEQ8Q,6BAA6BA,CACnChG,SAAc,EACd+F,WAAmB;MAEnBlH,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAE;QAC5DqH,MAAM,EAAEnG,SAAS,CAACmG,MAAM;QACxBC,YAAY,EAAEpG,SAAS,CAACmG,MAAM,EAAEjR,MAAM;QACtCmL,WAAW,EAAEL,SAAS,CAACK,WAAW;QAClClC,MAAM,EAAE6B,SAAS,CAAC7B,MAAM;QACxBkI,cAAc,EAAErG,SAAS,CAACqG;OAC3B,CAAC;MAEF;MACA,IAAI,CAACpR,oBAAoB,GAAG,EAAE;MAC9B,IAAI,CAACS,mBAAmB,GAAG,EAAE;MAC7B,IAAI,CAACJ,uBAAuB,GAAG,EAAE;MACjC,IAAI,CAAC0B,uBAAuB,GAAG,EAAE;MAEjC;MACA,IAAIgJ,SAAS,CAAC7B,MAAM,EAAE;QACpB,IAAI,CAAClJ,oBAAoB,CAACkM,IAAI,CAAC;UAC7B1G,EAAE,EAAE,UAAUsL,WAAW,EAAE,EAAE;UAC7BpR,IAAI,EAAEqL,SAAS,CAAC7B,MAAM;UACtBmI,IAAI,EAAE;SACP,CAAC;QACFzH,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEkB,SAAS,CAAC7B,MAAM,CAAC;MACvD;MAEA;MACA,IAAI6B,SAAS,CAACmG,MAAM,IAAIjG,KAAK,CAACC,OAAO,CAACH,SAAS,CAACmG,MAAM,CAAC,EAAE;QACvDtH,OAAO,CAACC,GAAG,CACT,yCAAyC,EACzCkB,SAAS,CAACmG,MAAM,CAACjR,MAAM,CACxB;QAED8K,SAAS,CAACmG,MAAM,CAACxB,OAAO,CAAC,CAAC4B,QAAa,EAAEC,aAAqB,KAAI;UAChE3H,OAAO,CAACC,GAAG,CACT,eAAe0H,aAAa,SAASD,QAAQ,CAACE,UAAU,IAAI,EAC5DF,QAAQ,CAACG,YAAY,CACtB;UAED,IAAIH,QAAQ,CAACJ,MAAM,IAAIjG,KAAK,CAACC,OAAO,CAACoG,QAAQ,CAACJ,MAAM,CAAC,EAAE;YACrDtH,OAAO,CAACC,GAAG,CACT,eAAe0H,aAAa,QAAQD,QAAQ,CAACJ,MAAM,CAACjR,MAAM,eAAe,CAC1E;YAEDqR,QAAQ,CAACJ,MAAM,CAACxB,OAAO,CAAC,CAACgC,UAAe,EAAEC,SAAiB,KAAI;cAC7D/H,OAAO,CAACC,GAAG,CAAC,kBAAkB0H,aAAa,IAAII,SAAS,GAAG,EAAE;gBAC3DC,SAAS,EAAEF,UAAU,CAACE,SAAS;gBAC/BC,WAAW,EAAEH,UAAU,CAACG,WAAW;gBACnCL,UAAU,EAAEE,UAAU,CAACF;eACxB,CAAC;cAEF;cACA,IACEE,UAAU,CAACF,UAAU,KAAK,CAAC,IAC3BE,UAAU,CAACE,SAAS,KAAK,OAAO,IAChCF,UAAU,CAACG,WAAW,EACtB;gBACAjI,OAAO,CAACC,GAAG,CACT,2CAA2C,EAC3C6H,UAAU,CAACG,WAAW,CACvB;gBACD,IAAI,CAACpR,mBAAmB,CAACyL,IAAI,CAAC;kBAC5B1G,EAAE,EAAE,SAASsL,WAAW,EAAE,EAAE;kBAC5BpR,IAAI,EAAE,GAAGgS,UAAU,CAACE,SAAS,EAAE;kBAC/BP,IAAI,EAAE;iBACP,CAAC;cACJ;cAEA;cACA,IACEK,UAAU,CAACF,UAAU,KAAK,CAAC,IAC3BE,UAAU,CAACE,SAAS,KAAK,wBAAwB,IACjDF,UAAU,CAACG,WAAW,EACtB;gBACAjI,OAAO,CAACC,GAAG,CACT,kDAAkD,EAClD6H,UAAU,CAACG,WAAW,CACvB;gBACD,MAAMC,OAAO,GAAGJ,UAAU,CAACG,WAAW,CAACE,QAAQ,EAAE;gBACjD,MAAMC,KAAK,GAAGF,OAAO,CAClBG,KAAK,CAAC,GAAG,CAAC,CACVnG,GAAG,CAAEtG,EAAU,IAAKA,EAAE,CAAC0M,IAAI,EAAE,CAAC,CAC9BC,MAAM,CAAE3M,EAAU,IAAKA,EAAE,CAAC;gBAE7BwM,KAAK,CAACtC,OAAO,CAAE0C,IAAY,IAAI;kBAC7B,IAAI,CAAC/R,uBAAuB,CAAC6L,IAAI,CAAC;oBAChC1G,EAAE,EAAE,aAAasL,WAAW,EAAE,EAAE;oBAChCpR,IAAI,EAAE,mBAAmB0S,IAAI,EAAE;oBAC/Bf,IAAI,EAAE;mBACP,CAAC;gBACJ,CAAC,CAAC;cACJ;cAEA;cACA,IACEK,UAAU,CAACF,UAAU,KAAK,CAAC,IAC3BE,UAAU,CAACG,WAAW,KAAK,MAAM,EACjC;gBACAjI,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE;kBAC1DwI,GAAG,EAAEX,UAAU,CAACE,SAAS;kBACzBvF,KAAK,EAAEqF,UAAU,CAACG;iBACnB,CAAC;gBAEF,IAAIH,UAAU,CAACE,SAAS,KAAK,mBAAmB,EAAE;kBAChD;kBACA,IAAI,IAAI,CAAC7P,uBAAuB,CAAC9B,MAAM,KAAK,CAAC,EAAE;oBAC7C,IAAI,CAAC8B,uBAAuB,CAACmK,IAAI,CAAC;sBAChC1G,EAAE,EAAE,aAAasL,WAAW,EAAE,EAAE;sBAChCpR,IAAI,EAAE,oBAAoB;sBAC1B2R,IAAI,EAAE;qBACP,CAAC;kBACJ;gBACF,CAAC,MAAM,IAAIK,UAAU,CAACE,SAAS,CAACU,UAAU,CAAC,YAAY,CAAC,EAAE;kBACxD;kBACA,IAAIC,aAAa,GAAGb,UAAU,CAACE,SAAS;kBACxC,IAAIW,aAAa,CAACD,UAAU,CAAC,YAAY,CAAC,EAAE;oBAC1CC,aAAa,GAAGA,aAAa,CAC1BhD,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CACzBA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;kBACvB;kBAEA,IAAI,CAACxN,uBAAuB,CAACmK,IAAI,CAAC;oBAChC1G,EAAE,EAAE,aAAasL,WAAW,EAAE,EAAE;oBAChCpR,IAAI,EAAE,GAAG6S,aAAa,EAAE;oBACxBlB,IAAI,EAAE;mBACP,CAAC;gBACJ;cACF;YACF,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ;MAEAzH,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;QACvC2I,WAAW,EAAE,IAAI,CAACxS,oBAAoB;QACtCyS,UAAU,EAAE,IAAI,CAAChS,mBAAmB;QACpCiS,cAAc,EAAE,IAAI,CAACrS,uBAAuB;QAC5CsS,cAAc,EAAE,IAAI,CAAC5Q;OACtB,CAAC;MAEF;MACA,MAAM6Q,aAAa,GAAG,CAAC,CAAC,CAAC;MACzB,MAAMC,eAAe,GACnB,IAAI,CAAC7S,oBAAoB,CAACC,MAAM,GAAG,IAAI,CAACQ,mBAAmB,CAACR,MAAM;MACpE,IAAI,CAAC2C,6BAA6B,GAAGkQ,IAAI,CAACC,KAAK,CAC5CF,eAAe,GAAGD,aAAa,GAAI,GAAG,CACxC;IACH;IAEQ5B,gCAAgCA,CACtCjG,SAAc,EACd+F,WAAmB;MAEnBlH,OAAO,CAACC,GAAG,CACT,+DAA+D,CAChE;MACDD,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEkB,SAAS,CAAC;MACtEnB,OAAO,CAACC,GAAG,CACT,0CAA0C,EAC1C+G,MAAM,CAACC,IAAI,CAAC9F,SAAS,CAAC,CACvB;MACDnB,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE,IAAI,CAACpI,SAAS,CAAC;MACjEmI,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEiH,WAAW,CAAC;MAE1D;MACA,IAAI,CAAC9Q,oBAAoB,GAAG,EAAE;MAC9B,IAAI,CAACS,mBAAmB,GAAG,EAAE;MAC7B,IAAI,CAACJ,uBAAuB,GAAG,EAAE;MACjC,IAAI,CAACS,kBAAkB,GAAG,EAAE;MAC5B,IAAI,CAACiB,uBAAuB,GAAG,EAAE;MAEjC6H,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;MAE1D;MACA,MAAMmJ,sBAAsB,GAC1BjI,SAAS,CAACkI,IAAI,IAAIlI,SAAS,CAACsF,IAAI,IAAItF,SAAS,CAAC1H,WAAW;MAE3DuG,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE;QACtDoJ,IAAI,EAAElI,SAAS,CAACkI,IAAI;QACpB5C,IAAI,EAAEtF,SAAS,CAACsF,IAAI;QACpBhN,WAAW,EAAE0H,SAAS,CAAC1H,WAAW;QAClC2P;OACD,CAAC;MAEF,IAAIA,sBAAsB,EAAE;QAC1B,IAAIE,cAAc,GAChBnI,SAAS,CAACkI,IAAI,IACdlI,SAAS,CAACsF,IAAI,IACdtF,SAAS,CAAC1H,WAAW,IACrB,4BAA4B;QAE9B;QACA,IAAI6P,cAAc,CAACjT,MAAM,GAAG,GAAG,EAAE;UAC/BiT,cAAc,GAAGA,cAAc,CAACC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK;QAC3D;QAEA,IAAI,CAACnT,oBAAoB,CAACkM,IAAI,CAAC;UAC7B1G,EAAE,EAAE,UAAUsL,WAAW,EAAE,EAAE;UAC7BpR,IAAI,EAAEwT,cAAc;UACpB7B,IAAI,EAAE;SACP,CAAC;QACFzH,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEqJ,cAAc,CAAC;MACnE;MAEA;MACA,IAAIE,eAAe,GAAG,EAAE;MAExBxJ,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;QAC5CwJ,eAAe,EAAE,CAAC,CAACtI,SAAS,CAACuI,YAAY;QACzCC,mBAAmB,EAAExI,SAAS,CAACuI,YAAY;QAC3CE,QAAQ,EAAEzI,SAAS,CAAC0I,KAAK;QACzBC,YAAY,EAAE3I,SAAS,CAAC0I,KAAK;QAC7BE,YAAY,EAAE5I,SAAS,CAAC6I,SAAS;QACjCC,gBAAgB,EAAE9I,SAAS,CAAC6I;OAC7B,CAAC;MAEF;MACA,IAAI7I,SAAS,CAACuI,YAAY,IAAIvI,SAAS,CAACuI,YAAY,CAACQ,QAAQ,EAAE;QAC7D,MAAMC,SAAS,GAAG9I,KAAK,CAACC,OAAO,CAACH,SAAS,CAACuI,YAAY,CAACQ,QAAQ,CAAC,GAC5D/I,SAAS,CAACuI,YAAY,CAACQ,QAAQ,GAC/B,CAAC/I,SAAS,CAACuI,YAAY,CAACQ,QAAQ,CAAC;QAErCV,eAAe,GAAGW,SAAS,CAACjI,GAAG,CAAEkI,GAAQ,IAAI;UAC3C,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;YACtD,OAAO;cAAEC,OAAO,EAAED;YAAG,CAAE;UACzB;UACA,OAAOA,GAAG;QACZ,CAAC,CAAC;MACJ;MACA;MAAA,KACK,IAAIjJ,SAAS,CAACmJ,YAAY,EAAE;QAC/Bd,eAAe,GAAG,CAACrI,SAAS,CAACmJ,YAAY,CAAC;MAC5C;MACA;MAAA,KACK,IAAInJ,SAAS,CAAC5B,KAAK,IAAI4B,SAAS,CAACoJ,SAAS,EAAE;QAC/Cf,eAAe,GAAG,CAAC;UAAEa,OAAO,EAAElJ,SAAS,CAAC5B,KAAK,IAAI4B,SAAS,CAACoJ;QAAS,CAAE,CAAC;MACzE;MAEAf,eAAe,CAAC1D,OAAO,CAAEoE,QAAa,IAAI;QACxC,MAAMG,OAAO,GAAGH,QAAQ,CAACG,OAAO,IAAIH,QAAQ,CAACtO,EAAE;QAC/C,MAAM2O,SAAS,GACbL,QAAQ,CAAC3K,KAAK,IACd2K,QAAQ,CAACM,mBAAmB,IAC5B,aAAaH,OAAO,EAAE;QAExB,IAAI,CAACxT,mBAAmB,CAACyL,IAAI,CAAC;UAC5B1G,EAAE,EAAE,SAASsL,WAAW,EAAE,EAAE;UAC5BpR,IAAI,EAAEyU,SAAS;UACf9C,IAAI,EAAE;SACP,CAAC;QACFzH,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEsK,SAAS,CAAC;MAC7D,CAAC,CAAC;MAEF;MACA,IAAIE,mBAAmB,GAAG,EAAE;MAE5B;MACA,IAAItJ,SAAS,CAACuI,YAAY,IAAIvI,SAAS,CAACuI,YAAY,CAACgB,gBAAgB,EAAE;QACrE,MAAMC,MAAM,GAAGtJ,KAAK,CAACC,OAAO,CAACH,SAAS,CAACuI,YAAY,CAACgB,gBAAgB,CAAC,GACjEvJ,SAAS,CAACuI,YAAY,CAACgB,gBAAgB,GACvC,CAACvJ,SAAS,CAACuI,YAAY,CAACgB,gBAAgB,CAAC;QAE7CD,mBAAmB,GAAGE,MAAM,CAACzI,GAAG,CAAEkI,GAAQ,IAAI;UAC5C,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;YACtD,OAAO;cAAEQ,eAAe,EAAER;YAAG,CAAE;UACjC;UACA,OAAOA,GAAG;QACZ,CAAC,CAAC;MACJ;MACA;MAAA,KACK,IACHjJ,SAAS,CAAC0J,aAAa,IACvBxJ,KAAK,CAACC,OAAO,CAACH,SAAS,CAAC0J,aAAa,CAAC,EACtC;QACAJ,mBAAmB,GAAGtJ,SAAS,CAAC0J,aAAa;MAC/C;MAEAJ,mBAAmB,CAAC3E,OAAO,CAAEgF,KAAU,IAAI;QACzC,MAAMtC,IAAI,GAAGsC,KAAK,CAACF,eAAe,IAAIE,KAAK,CAAClP,EAAE;QAC9C,MAAMmP,cAAc,GAAGD,KAAK,CAACE,mBAAmB,IAAIF,KAAK,CAAChV,IAAI;QAC9D,MAAMmV,MAAM,GAAGF,cAAc,IAAI,sBAAsBvC,IAAI,EAAE;QAE7D,IAAI,CAAC/R,uBAAuB,CAAC6L,IAAI,CAAC;UAChC1G,EAAE,EAAE,aAAasL,WAAW,EAAE,EAAE;UAChCpR,IAAI,EAAEmV,MAAM;UACZxD,IAAI,EAAE;SACP,CAAC;QACFzH,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEgL,MAAM,CAAC;MAC9D,CAAC,CAAC;MAEF;MACA,IAAIC,cAAc,GAAG,EAAE;MACvB,IAAIC,kBAAkB,GAAG,EAAE;MAE3BnL,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;QAC3CwJ,eAAe,EAAE,CAAC,CAACtI,SAAS,CAACuI,YAAY;QACzCC,mBAAmB,EAAExI,SAAS,CAACuI,YAAY;QAC3CE,QAAQ,EAAEzI,SAAS,CAAC0I,KAAK;QACzBC,YAAY,EAAE3I,SAAS,CAAC0I,KAAK;QAC7BE,YAAY,EAAE5I,SAAS,CAAC6I,SAAS;QACjCC,gBAAgB,EAAE9I,SAAS,CAAC6I;OAC7B,CAAC;MAEF;MACA,IAAI7I,SAAS,CAACuI,YAAY,EAAE;QAC1B,IAAIvI,SAAS,CAACuI,YAAY,CAAC0B,OAAO,EAAE;UAClC,MAAMC,QAAQ,GAAGhK,KAAK,CAACC,OAAO,CAACH,SAAS,CAACuI,YAAY,CAAC0B,OAAO,CAAC,GAC1DjK,SAAS,CAACuI,YAAY,CAAC0B,OAAO,GAC9B,CAACjK,SAAS,CAACuI,YAAY,CAAC0B,OAAO,CAAC;UAEpCF,cAAc,GAAGG,QAAQ,CAACnJ,GAAG,CAAEkI,GAAQ,IAAI;YACzC,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;cACtD,OAAO;gBAAEkB,MAAM,EAAElB;cAAG,CAAE;YACxB;YACA,OAAOA,GAAG;UACZ,CAAC,CAAC;QACJ;QACA,IAAIjJ,SAAS,CAACuI,YAAY,CAAC6B,WAAW,EAAE;UACtC,MAAMC,YAAY,GAAGnK,KAAK,CAACC,OAAO,CAACH,SAAS,CAACuI,YAAY,CAAC6B,WAAW,CAAC,GAClEpK,SAAS,CAACuI,YAAY,CAAC6B,WAAW,GAClC,CAACpK,SAAS,CAACuI,YAAY,CAAC6B,WAAW,CAAC;UAExCJ,kBAAkB,GAAGK,YAAY,CAACtJ,GAAG,CAAEkI,GAAQ,IAAI;YACjD,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;cACtD,OAAO;gBAAEkB,MAAM,EAAElB;cAAG,CAAE;YACxB;YACA,OAAOA,GAAG;UACZ,CAAC,CAAC;QACJ;MACF;MACA;MAAA,KACK;QACH,IAAIjJ,SAAS,CAAC0I,KAAK,IAAIxI,KAAK,CAACC,OAAO,CAACH,SAAS,CAAC0I,KAAK,CAAC,EAAE;UACrDqB,cAAc,GAAG/J,SAAS,CAAC0I,KAAK;QAClC;QACA,IAAI1I,SAAS,CAAC6I,SAAS,IAAI3I,KAAK,CAACC,OAAO,CAACH,SAAS,CAAC6I,SAAS,CAAC,EAAE;UAC7DmB,kBAAkB,GAAGhK,SAAS,CAAC6I,SAAS;QAC1C;MACF;MAEA;MACAkB,cAAc,CAACpF,OAAO,CAAEpG,IAAS,IAAI;QACnC,MAAM4L,MAAM,GAAG5L,IAAI,CAAC4L,MAAM,IAAI5L,IAAI,CAAC9D,EAAE;QACrC,MAAM6P,QAAQ,GAAG/L,IAAI,CAAC+L,QAAQ,IAAI/L,IAAI,CAAC5J,IAAI,IAAI,YAAYwV,MAAM,EAAE;QAEnE,IAAI,CAACpU,kBAAkB,CAACoL,IAAI,CAAC;UAC3B1G,EAAE,EAAE,QAAQsL,WAAW,EAAE,EAAE;UAC3BpR,IAAI,EAAE2V,QAAQ;UACdhE,IAAI,EAAE;SACP,CAAC;QACFzH,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEwL,QAAQ,CAAC;MACnE,CAAC,CAAC;MAEF;MACAN,kBAAkB,CAACrF,OAAO,CAAE4F,QAAa,IAAI;QAC3C,MAAMC,UAAU,GAAGD,QAAQ,CAACJ,MAAM,IAAII,QAAQ,CAAC9P,EAAE;QACjD,MAAMgQ,YAAY,GAChBF,QAAQ,CAACD,QAAQ,IAAIC,QAAQ,CAAC5V,IAAI,IAAI,iBAAiB6V,UAAU,EAAE;QAErE,IAAI,CAACzU,kBAAkB,CAACoL,IAAI,CAAC;UAC3B1G,EAAE,EAAE,QAAQsL,WAAW,EAAE,EAAE;UAC3BpR,IAAI,EAAE8V,YAAY;UAClBnE,IAAI,EAAE;SACP,CAAC;QACFzH,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE2L,YAAY,CAAC;MACpE,CAAC,CAAC;MAEF5L,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE;QACrD2I,WAAW,EAAE,IAAI,CAACxS,oBAAoB;QACtCyS,UAAU,EAAE,IAAI,CAAChS,mBAAmB;QACpCiS,cAAc,EAAE,IAAI,CAACrS,uBAAuB;QAC5CoV,SAAS,EAAE,IAAI,CAAC3U,kBAAkB;QAClC6R,cAAc,EAAE,IAAI,CAAC5Q;OACtB,CAAC;MAEF;MACA6H,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;QACvC6L,WAAW,EAAE,IAAI,CAAC1V,oBAAoB,CAACC,MAAM;QAC7C0V,UAAU,EAAE,IAAI,CAAClV,mBAAmB,CAACR,MAAM;QAC3C2V,cAAc,EAAE,IAAI,CAACvV,uBAAuB,CAACJ,MAAM;QACnD4V,SAAS,EAAE,IAAI,CAAC/U,kBAAkB,CAACb,MAAM;QACzC6V,cAAc,EAAE,IAAI,CAAC/T,uBAAuB,CAAC9B;OAC9C,CAAC;MAEF;MACA2J,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;QAClCpI,SAAS,EAAE,IAAI,CAACA,SAAS;QACzBsU,eAAe,EAAE,IAAI,CAACtU,SAAS,KAAK,eAAe;QACnDuU,YAAY,EAAE,IAAI,CAAClV,kBAAkB,CAACb,MAAM,GAAG,CAAC;QAChDgW,aAAa,EAAE,IAAI,CAACnV,kBAAkB,CAACgL,GAAG,CAAEoK,CAAC,IAAKA,CAAC,CAACxW,IAAI;OACzD,CAAC;MAEF;MACA,MAAMkT,aAAa,GAAG,CAAC,CAAC,CAAC;MACzB,MAAMC,eAAe,GACnB,IAAI,CAAC7S,oBAAoB,CAACC,MAAM,GAAG,IAAI,CAACQ,mBAAmB,CAACR,MAAM;MACpE,IAAI,CAAC2C,6BAA6B,GAAGkQ,IAAI,CAACC,KAAK,CAC5CF,eAAe,GAAGD,aAAa,GAAI,GAAG,CACxC;IACH;;uCAjvCW9N,uBAAuB,EAAA3F,EAAA,CAAAgX,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAlX,EAAA,CAAAgX,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAnX,EAAA,CAAAgX,iBAAA,CAAAI,EAAA,CAAAC,mBAAA,GAAArX,EAAA,CAAAgX,iBAAA,CAAAM,EAAA,CAAAC,sBAAA,GAAAvX,EAAA,CAAAgX,iBAAA,CAAAQ,EAAA,CAAAC,mBAAA,GAAAzX,EAAA,CAAAgX,iBAAA,CAAAU,EAAA,CAAAC,aAAA,GAAA3X,EAAA,CAAAgX,iBAAA,CAAAY,EAAA,CAAAC,WAAA,GAAA7X,EAAA,CAAAgX,iBAAA,CAAAc,EAAA,CAAAC,oBAAA;IAAA;;YAAvBpS,uBAAuB;MAAAqS,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBAavBxY,iCAAiC;;;;;;;;;;;;UC3CxCK,EAJN,CAAAC,cAAA,aAAuC,aAEZ,aACD,gBAC+C;UAAvCD,EAAA,CAAA4B,UAAA,mBAAAyW,yDAAA;YAAA,OAASD,GAAA,CAAA5I,YAAA,EAAc;UAAA,EAAC;UAClDxP,EAAA,CAAAuB,SAAA,kBAIY;UACZvB,EAAA,CAAAC,cAAA,cAAyB;UAAAD,EAAA,CAAAE,MAAA,GAAe;UAG9CF,EAH8C,CAAAG,YAAA,EAAO,EACxC,EACL,EACF;UAOAH,EAJN,CAAAC,cAAA,aAA0B,aAEyC,aACrC,iBAC+C;UAA1CD,EAAA,CAAA4B,UAAA,mBAAA0W,0DAAA;YAAA,OAASF,GAAA,CAAAvI,eAAA,EAAiB;UAAA,EAAC;UACtD7P,EAAA,CAAAuB,SAAA,oBAKW;UACbvB,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAQ,UAAA,KAAA+X,0CAAA,qBAKC;UAGHvY,EAAA,CAAAG,YAAA,EAAM;UAGJH,EADF,CAAAC,cAAA,eAAiE,0CAoB9D;UADCD,EALA,CAAA4B,UAAA,0BAAA4W,yFAAAC,MAAA;YAAA,OAAgBL,GAAA,CAAAzJ,eAAA,CAAA8J,MAAA,CAAuB;UAAA,EAAC,yBAAAC,wFAAAD,MAAA;YAAA,OACzBL,GAAA,CAAA9L,iBAAA,CAAAmM,MAAA,CAAyB;UAAA,EAAC,kCAAAE,iGAAAF,MAAA;YAAA,OACjBL,GAAA,CAAAvJ,gCAAA,CAAA4J,MAAA,CAAwC;UAAA,EAAC,4BAAAG,2FAAAH,MAAA;YAAA,OAC/CL,GAAA,CAAArJ,0BAAA,CAAA0J,MAAA,CAAkC;UAAA,EAAC,2BAAAI,0FAAAJ,MAAA;YAAA,OACpCL,GAAA,CAAApJ,eAAA,CAAAyJ,MAAA,CAAuB;UAAA,EAAC,+BAAAK,8FAAA;YAAA,OACpBV,GAAA,CAAAlJ,mBAAA,EAAqB;UAAA,EAAC;UAIjDlP,EAFI,CAAAG,YAAA,EAAiC,EAC7B,EACF;UAOAH,EAJN,CAAAC,cAAA,eAAyB,cAEG,eACI,kBAKzB;UADCD,EAAA,CAAA4B,UAAA,mBAAAmX,0DAAA;YAAA,OAASX,GAAA,CAAA1K,iBAAA,CAAkB,WAAW,CAAC;UAAA,EAAC;UAExC1N,EAAA,CAAAE,MAAA,mBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAIC;UADCD,EAAA,CAAA4B,UAAA,mBAAAoX,0DAAA;YAAA,OAASZ,GAAA,CAAA1K,iBAAA,CAAkB,QAAQ,CAAC;UAAA,EAAC;UAErC1N,EAAA,CAAAE,MAAA,sBACF;UAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;UAENH,EAAA,CAAAC,cAAA,eAA2B;UAkkBzBD,EAhkBA,CAAAQ,UAAA,KAAAyY,uCAAA,oBAAsE,KAAAC,uCAAA,kBAgkBN;UAmFxElZ,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACF;;;UAhuB2BH,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAK,iBAAA,CAAA+X,GAAA,CAAApT,SAAA,CAAe;UAQpBhF,EAAA,CAAAI,SAAA,GAAwC;UAAxCJ,EAAA,CAAAqC,WAAA,cAAA+V,GAAA,CAAAtP,oBAAA,CAAwC;UAIxD9I,EAAA,CAAAI,SAAA,GAAgE;UAAhEJ,EAAA,CAAAW,UAAA,aAAAyX,GAAA,CAAAtP,oBAAA,gCAAgE;UAQjE9I,EAAA,CAAAI,SAAA,EAA2B;UAA3BJ,EAAA,CAAAW,UAAA,UAAAyX,GAAA,CAAAtP,oBAAA,CAA2B;UAQL9I,EAAA,CAAAI,SAAA,EAAqC;UAArCJ,EAAA,CAAAqC,WAAA,WAAA+V,GAAA,CAAAtP,oBAAA,CAAqC;UAE5D9I,EAAA,CAAAI,SAAA,EAAyB;UAWzBJ,EAXA,CAAAW,UAAA,aAAAyX,GAAA,CAAApR,YAAA,CAAyB,cAAAoR,GAAA,CAAAnR,gBAAA,CACK,cAAAmR,GAAA,CAAA9V,SAAA,CACP,+BAAA8V,GAAA,CAAA9V,SAAA,kBACkC,gCAC1B,6BACH,uBACN,4BACK,8BACE,wBACN,uBAAA8V,GAAA,CAAApT,SAAA,CACS,8CACa;UAmB3ChF,EAAA,CAAAI,SAAA,GAA+C;UAA/CJ,EAAA,CAAAqC,WAAA,WAAA+V,GAAA,CAAArP,cAAA,iBAA+C;UAO/C/I,EAAA,CAAAI,SAAA,GAA4C;UAA5CJ,EAAA,CAAAqC,WAAA,WAAA+V,GAAA,CAAArP,cAAA,cAA4C;UAU1C/I,EAAA,CAAAI,SAAA,GAAoC;UAApCJ,EAAA,CAAAW,UAAA,SAAAyX,GAAA,CAAArP,cAAA,iBAAoC;UAgkBpC/I,EAAA,CAAAI,SAAA,EAAiC;UAAjCJ,EAAA,CAAAW,UAAA,SAAAyX,GAAA,CAAArP,cAAA,cAAiC;;;qBD9nB3C5J,YAAY,EAAAga,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,QAAA,EACZ5Z,WAAW,EACXC,iCAAiC,EACjCC,aAAa,EACbC,uBAAuB;MAAA0Z,MAAA;IAAA;;SAKd5T,uBAAuB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}