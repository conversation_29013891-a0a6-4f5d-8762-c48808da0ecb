{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { DecimalPipe } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule, Validators } from '@angular/forms';\nimport knowledgeBaseLabels from '../constants/knowledge-base.json';\nimport { SliderComponent, DropdownComponent, AvaTextboxComponent, AvaTextareaComponent, FileUploadComponent, ButtonComponent, TableComponent } from '@ava/play-comp-library';\nimport { PopupComponent } from '@ava/play-comp-library';\nimport { LucideAngularModule } from 'lucide-angular';\nimport { map } from 'rxjs/internal/operators/map';\nlet CreateKnowledgeBaseComponent = class CreateKnowledgeBaseComponent {\n  fb;\n  router;\n  _decimalPipe;\n  knowledgeBaseService;\n  route;\n  // Form group for the knowledge base creation form\n  knowledgeBaseForm;\n  // Stores route param for edit mode\n  knowledgeBaseId = null;\n  // Popup & submission state\n  submissionMessage = null;\n  submissionSuccess = false;\n  showInfoPopup = false;\n  fileUploadRequired = false;\n  // UI mode tracking\n  isEditMode = false;\n  //maxFile size is 15MB \n  maxFileSize = 15 * 1024 * 1024;\n  // Allowed file formats for upload\n  allowedFormats = knowledgeBaseLabels.allowedFormats;\n  // UI text constants\n  componentTitle = 'Upload File Here';\n  showUploadButton = true;\n  showDialogCloseIcon = false;\n  // Selected options\n  selectedRetriever = knowledgeBaseLabels.labels.default;\n  selectedUploadType = 'upload-files';\n  uploadPlaceholder = 'Upload Files';\n  iconName = 'circle-check';\n  iconColor = 'red';\n  // Model search input text (currently unused)\n  searchModelText = '';\n  // Uploaded files\n  uploadedFiles = [];\n  // Split size fields\n  splitSize = 5000;\n  parentSplitSize = 5000;\n  childSplitSize = 2000;\n  // Dropdown data\n  embeddingModelOptions = [];\n  schemeOptions = [];\n  uploadOptions = [];\n  // Label map\n  kbLabels = knowledgeBaseLabels.labels;\n  // Retriever options for toggle\n  retrieverOptions = [this.kbLabels.default, this.kbLabels.parentDoc];\n  // Submit button label (Save / Update)\n  saveOrUpdateLabel = this.kbLabels.save;\n  // Controls required per upload type\n  selectedControls = {\n    'upload-files': [],\n    'Azure Blob': knowledgeBaseLabels.azureBlob,\n    'GitHub': knowledgeBaseLabels.github,\n    'Share Point': knowledgeBaseLabels.sharePoint,\n    'Confluence Wiki': knowledgeBaseLabels.confluenceWiki,\n    'Database': knowledgeBaseLabels.database\n  };\n  // API path suffix map based on upload source\n  pathSuffix = {\n    'Azure Blob': '/blob',\n    'GitHub': '/github',\n    'Share Point': '/sharepoint',\n    'Confluence Wiki': '/confluence',\n    'Database': '/database'\n  };\n  tableColumns = [{\n    key: 'fileName',\n    label: 'File Name',\n    type: 'text'\n  }, {\n    key: 'fileSizeFormatted',\n    label: 'File Size',\n    type: 'text'\n  }, {\n    key: 'uploadDateFormatted',\n    label: 'Upload Date',\n    type: 'text'\n  }, {\n    key: 'retrieverType',\n    label: 'Retriever Type',\n    type: 'text'\n  }];\n  tableData = [];\n  isLeftCollapsed = false;\n  toggleLeftPanel() {\n    this.isLeftCollapsed = !this.isLeftCollapsed;\n  }\n  // Constructor injecting services and initializing form group\n  constructor(fb, router, _decimalPipe, knowledgeBaseService, route) {\n    this.fb = fb;\n    this.router = router;\n    this._decimalPipe = _decimalPipe;\n    this.knowledgeBaseService = knowledgeBaseService;\n    this.route = route;\n    // Define form controls and validators\n    this.knowledgeBaseForm = this.fb.group({\n      name: ['', [Validators.required, Validators.minLength(3)]],\n      description: ['', Validators.required],\n      retriever: [this.kbLabels.default],\n      splitSize: [5000],\n      parentSplitSize: [5000],\n      childSplitSize: [2000],\n      embeddingModel: [null, Validators.required],\n      uploadType: ['', Validators.required],\n      containerName: [''],\n      accountName: [''],\n      accountKey: [''],\n      githubKey: [''],\n      githubAccount: [''],\n      githubRepo: [''],\n      githubBranch: [''],\n      tenantId: [''],\n      sharepointSiteName: [''],\n      sharepointFolderPath: [''],\n      email: [''],\n      clientId: [''],\n      clientSecret: [''],\n      apiToken: [''],\n      baseUrl: [''],\n      spaceKey: [''],\n      overlap: [''],\n      scheme: [''],\n      host: [''],\n      port: [''],\n      user: [''],\n      password: [''],\n      dbname: [''],\n      query: ['']\n    });\n  }\n  // Lifecycle hook: on component init\n  ngOnInit() {\n    this.loadEmbeddingModelOptions();\n    this.loadUploadOptionsAndSetDefault();\n    this.loadSchemeDropdownOptions();\n    // Check if editing an existing knowledge base\n    this.knowledgeBaseId = this.route.snapshot.paramMap.get('id');\n    this.isEditMode = !!this.knowledgeBaseId;\n    if (this.isEditMode) {\n      this.loadEditModeData();\n    }\n    this.saveOrUpdateLabel = this.isEditMode ? this.kbLabels.update : this.kbLabels.save;\n  }\n  // Fetch available embedding models\n  loadEmbeddingModelOptions() {\n    this.knowledgeBaseService.getEmbeddingModelOptions().subscribe(options => {\n      this.embeddingModelOptions = options.map(opt => ({\n        name: opt.modelDeploymentName,\n        value: String(opt.id)\n      }));\n    });\n  }\n  // Scheme Dropdown values\n  loadSchemeDropdownOptions() {\n    this.knowledgeBaseService.getSchemeDropdowns().subscribe(response => {\n      if (response && response.value) {\n        try {\n          const parsedValue = JSON.parse(response.value); // parse string to object\n          this.schemeOptions = Object.keys(parsedValue).map(key => ({\n            name: key,\n            value: parsedValue[key]\n          }));\n        } catch (error) {\n          console.error('Failed to parse scheme options:', error);\n          this.schemeOptions = [];\n        }\n      } else {\n        this.schemeOptions = [];\n      }\n    });\n  }\n  // Triggered when upload type changes\n  onUploadTypeChange(event) {\n    const selected = event?.selectedOptions?.[0]?.value;\n    this.selectedUploadType = selected || '';\n    this.uploadPlaceholder = '';\n    if (this.selectedUploadType !== 'upload-files') {\n      this.uploadedFiles = [];\n    }\n    // Clear all old validators AND values\n    Object.values(this.selectedControls).flat().forEach(ctrlName => {\n      const ctrl = this.getControl(ctrlName);\n      if (ctrl) {\n        ctrl.clearValidators();\n        ctrl.setValue(null); // or ''\n        ctrl.markAsPristine();\n        ctrl.markAsUntouched();\n      }\n    });\n    // Apply required validators to new controls\n    this.selectedControls[this.selectedUploadType]?.forEach(ctrl => {\n      this.getControl(ctrl)?.setValidators(Validators.required);\n    });\n    // Reapply validator to uploadType itself\n    this.getControl('uploadType')?.setValidators(Validators.required);\n    // Mark uploadType as interacted\n    this.getControl('uploadType')?.markAsTouched();\n    this.getControl('uploadType')?.markAsDirty();\n    // Recalculate form status\n    this.knowledgeBaseForm.updateValueAndValidity();\n  }\n  // Submit form handler\n  onSave() {\n    this.submissionSuccess = false;\n    this.fileUploadRequired = false;\n    // Basic form validation\n    if (this.knowledgeBaseForm.invalid && this.uploadedFiles.length === 0) {\n      this.knowledgeBaseForm.markAllAsTouched();\n      this.fileUploadRequired = true;\n      return;\n    }\n    const type = this.knowledgeBaseForm.value.uploadType;\n    const suffix = this.pathSuffix[type] || '';\n    const retrieverType = this.selectedRetriever === this.kbLabels.parentDoc ? 'parent_doc_retriever' : 'normal';\n    const knowledgeBase = this.getControl('name').value;\n    const description = this.getControl('description').value;\n    const splitSize = this.getControl('splitSize').value;\n    const parentSplitSize = this.getControl('parentSplitSize')?.value;\n    const childSplitSize = this.getControl('childSplitSize')?.value;\n    // Get selected model ID\n    const selectedModelValue = this.knowledgeBaseForm.value['embeddingModel'];\n    const selectedModel = this.embeddingModelOptions.find(opt => opt.name === selectedModelValue || opt.value === selectedModelValue);\n    const selectedModelId = selectedModel ? selectedModel.value : null;\n    // Construct base payload\n    const payload = {};\n    if (this.isEditMode && this.knowledgeBaseId) {\n      // Only send masterId during edit\n      payload['masterId'] = this.knowledgeBaseId;\n    } else {\n      // Full payload during create\n      payload['knowledgeBase'] = knowledgeBase;\n      payload['description'] = description;\n      payload['model-ref'] = selectedModelId;\n      payload['type'] = retrieverType;\n      if (retrieverType === 'parent_doc_retriever') {\n        payload['parentSplitSize'] = parentSplitSize;\n        payload['splitSize'] = childSplitSize;\n      } else {\n        payload['splitSize'] = splitSize;\n      }\n    }\n    // Handle file upload case\n    if (type === 'upload-files' || type == 'Upload Files') {\n      const file = this.uploadedFiles;\n      if (!file) return;\n      const formData = new FormData();\n      this.uploadedFiles.forEach(file => {\n        formData.append('files', file);\n      });\n      // Submit form with files\n      this.knowledgeBaseService.submitUpload(formData, payload, suffix).subscribe({\n        next: info => {\n          this.iconName = 'circle-check';\n          this.submissionMessage = info?.info?.message || info.message;\n          this.submissionSuccess = true;\n          this.showInfoPopup = true;\n          this.iconColor = 'green';\n        },\n        error: err => {\n          this.iconName = 'alert-circle';\n          this.submissionMessage = err?.error?.message || err.message || 'Knowledge Base creation or update failed. Please try again.';\n          this.submissionSuccess = false;\n          this.showInfoPopup = true;\n          this.iconColor = 'red';\n        }\n      });\n      // Handle non-file (external source) upload case\n    } else {\n      const data = {};\n      // Add selected controls to both body and query params\n      this.selectedControls[type]?.forEach(key => {\n        const value = this.getControl(key)?.value;\n        data[key] = value;\n        // If edit mode, also include these as query params (i.e., in payload)\n        if (this.isEditMode) {\n          payload[key] = value;\n        }\n      });\n      // Also add masterId for edit mode\n      this.knowledgeBaseService.submitUpload(data, payload, suffix).subscribe({\n        next: info => {\n          this.iconName = 'circle-check';\n          this.submissionMessage = info?.info?.message || info.message;\n          this.submissionSuccess = true;\n          this.showInfoPopup = true;\n          this.iconColor = 'green';\n        },\n        error: err => {\n          this.iconName = 'alert-circle';\n          this.submissionMessage = err?.error?.message || err.message || 'Knowledge Base creation or update failed. Please try again.';\n          this.submissionSuccess = false;\n          this.showInfoPopup = true;\n          this.iconColor = 'red';\n        }\n      });\n    }\n  }\n  // Get a typed form control\n  getControl(name) {\n    return this.knowledgeBaseForm.get(name);\n  }\n  // Utility to return readable field validation error\n  getFieldError(fieldName) {\n    const field = this.knowledgeBaseForm.get(fieldName);\n    // Capitalize only if first letter is not already uppercase\n    const formattedFieldName = /^[A-Z]/.test(fieldName) ? fieldName : fieldName.charAt(0).toUpperCase() + fieldName.slice(1);\n    if (field && field.invalid && (field.touched || field.dirty)) {\n      if (field.errors?.['required']) {\n        return `${formattedFieldName} is required`;\n      }\n      if (field.errors?.['email']) {\n        return 'Please enter a valid email address';\n      }\n      if (field.errors?.['minlength']) {\n        return `${formattedFieldName} must be at least ${field.errors['minlength'].requiredLength} characters long`;\n      }\n    } else if (fieldName == 'scheme' || fieldName == 'embeddingModel') {\n      if (field && field.errors?.['required']) {\n        return `${formattedFieldName} is required`;\n      }\n    }\n    return '';\n  }\n  // Navigate back to listing\n  onExit() {\n    this.router.navigate(['/libraries/knowledge-base']);\n  }\n  // Load upload options and set first one as default\n  loadUploadOptionsAndSetDefault() {\n    this.knowledgeBaseService.getUploadDropdowns().subscribe({\n      next: res => {\n        if (res?.value) {\n          this.uploadOptions = this.getUploadTypeOptions(res.value);\n          // Safely assign default value and apply\n          const firstOption = this.uploadOptions[0];\n          if (firstOption && firstOption.value) {\n            const defaultValue = firstOption.value;\n            // Set form control\n            this.knowledgeBaseForm.get('uploadType')?.setValue(defaultValue);\n            // Set component state\n            this.selectedUploadType = defaultValue;\n            // Trigger validators and placeholder update\n            this.onUploadTypeChange({\n              selectedOptions: [{\n                value: defaultValue\n              }]\n            });\n          }\n        }\n      },\n      error: () => {}\n    });\n  }\n  // Converts string labels into slug format\n  getUploadTypeOptions(rawValue) {\n    const parsed = JSON.parse(rawValue);\n    return Object.keys(parsed).map(label => ({\n      name: label,\n      value: this.toSlug(label)\n    }));\n  }\n  // Utility for slug formatting\n  toSlug(label) {\n    return label.toLowerCase().replace(/\\s+/g, '-');\n  }\n  // Handle retriever mode toggle\n  selectRetriever(retriever) {\n    this.selectedRetriever = retriever;\n    this.knowledgeBaseForm.get('retriever')?.setValue(retriever);\n  }\n  onParentSplitSizeChange(event) {\n    this.parentSplitSize = event;\n    this.knowledgeBaseForm.get('parentSplitSize')?.setValue(this.parentSplitSize);\n  }\n  onChildSplitSizeChange(event) {\n    this.childSplitSize = event;\n    this.knowledgeBaseForm.get('childSplitSize')?.setValue(this.childSplitSize);\n  }\n  // splitSize input setter\n  splitSizeChange(event) {\n    this.splitSize = event;\n    this.knowledgeBaseForm.get('splitSize')?.setValue(this.splitSize);\n  }\n  // Update file list after change\n  filesListChanged(file) {\n    this.uploadedFiles = [...file];\n  }\n  // Disable submit based on form or file state\n  isSubmitDisabled() {\n    const isFormInvalid = this.knowledgeBaseForm.invalid;\n    const isUploadTypeFile = this.selectedUploadType === 'upload-files';\n    const isFileEmpty = this.uploadedFiles.length === 0;\n    const isParentDoc = this.selectedRetriever === this.kbLabels.parentDoc;\n    const splitSize = Number(this.knowledgeBaseForm.get('splitSize')?.value);\n    const parentSplitSize = Number(this.knowledgeBaseForm.get('parentSplitSize')?.value);\n    const childSplitSize = Number(this.knowledgeBaseForm.get('childSplitSize')?.value);\n    //  Normal condition: splitSize must be at least 100\n    const isNormalSplitSizeInvalid = !isParentDoc && splitSize < 100;\n    //  Parent-child condition:\n    // 1. parent must be > child\n    // 2. both must be >= 100\n    const isParentChildInvalid = isParentDoc && (parentSplitSize <= childSplitSize || parentSplitSize < 100 || childSplitSize < 100);\n    return isFormInvalid || isUploadTypeFile && isFileEmpty || isParentChildInvalid || isNormalSplitSizeInvalid;\n  }\n  // Close popup and navigate on success\n  handlePopupClose() {\n    this.showInfoPopup = false;\n    if (this.submissionSuccess) {\n      this.router.navigate(['/libraries/knowledge-base']);\n    }\n  }\n  // Format file size from bytes to MB string\n  formatDocumentSize(size) {\n    if (size < 102400) {\n      return this._decimalPipe.transform(size / 1024, '1.0-2') + ' KB';\n    } else {\n      return this._decimalPipe.transform(size / 1048576, '1.2-2') + ' MB';\n    }\n  }\n  // Format rows\n  formatRows(row) {\n    return row > 1 ? `${row} rows` : `${row} row`;\n  }\n  // Format date to dd/mm/yy, hh:mm format\n  formatUploadDate(dateStr) {\n    const date = new Date(dateStr);\n    const day = String(date.getDate()).padStart(2, '0');\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const year = String(date.getFullYear()).slice(-2);\n    const hours = String(date.getHours()).padStart(2, '0');\n    const minutes = String(date.getMinutes()).padStart(2, '0');\n    return `${day}/${month}/${year}, ${hours}:${minutes}`;\n  }\n  // Returns a user-friendly label for the given retriever type\n  getRetrieverLabel(type) {\n    const map = {\n      normal: 'Default',\n      parent_doc_retriever: 'Parent Doc'\n    };\n    this.selectedRetriever = map[type];\n    return map[type] || 'Unknown';\n  }\n  loadEditModeData() {\n    const kbId = Number(this.knowledgeBaseId);\n    // Fetch files and other details\n    this.knowledgeBaseService.getKnowledgeBaseById(kbId).subscribe({\n      next: response => {\n        if (response?.files?.length > 0) {\n          const retrieverType = response.retrieverType || 'normal';\n          this.tableData = response.files.map(file => ({\n            fileName: file.fileName,\n            fileSizeFormatted: file.source === 'database' ? this.formatRows(file.fileSizeBytes) : this.formatDocumentSize(file.fileSizeBytes),\n            uploadDateFormatted: this.formatUploadDate(file.uploadDate),\n            retrieverType: this.getRetrieverLabel(retrieverType)\n          }));\n          // Disable fields in edit mode\n          ['name', 'description', 'splitSize', 'embeddingModel'].forEach(field => this.knowledgeBaseForm.get(field)?.disable());\n        } else {\n          this.tableData = [];\n        }\n      },\n      error: err => {\n        console.error('Error fetching knowledge base data', err);\n        this.tableData = [];\n      }\n    });\n    // Fetch and patch name/description\n    this.knowledgeBaseService.fetchAllKnowledge().pipe(map(response => {\n      const match = response.find(item => item.id === kbId);\n      if (match) {\n        this.knowledgeBaseForm.patchValue({\n          name: match.collectionName,\n          description: match.description\n        });\n      }\n      return response;\n    })).subscribe({\n      next: () => {\n        // Successfully patched the form (already done in map)\n      },\n      error: () => {}\n    });\n  }\n  onSliderInputChange(event, controlName) {\n    let inputValue;\n    // Handle both native event and direct value\n    if (event?.target) {\n      inputValue = event.target.valueAsNumber;\n    } else if (typeof event === 'number') {\n      inputValue = event;\n    } else {\n      inputValue = Number(event);\n    }\n    // Cap to max value (you can also parametrize this if needed)\n    const cappedValue = Math.min(inputValue, 20000);\n    // Update local variable based on controlName\n    if (controlName === 'parentSplitSize') {\n      this.parentSplitSize = cappedValue;\n    } else if (controlName === 'childSplitSize') {\n      this.childSplitSize = cappedValue;\n    } else if (controlName === 'splitSize') {\n      this.splitSize = cappedValue;\n    }\n    // Update the corresponding form control\n    this.knowledgeBaseForm.get(controlName)?.setValue(cappedValue);\n  }\n};\nCreateKnowledgeBaseComponent = __decorate([Component({\n  selector: 'app-create-knowledge-base',\n  standalone: true,\n  imports: [CommonModule, ReactiveFormsModule, FormsModule, LucideAngularModule, DropdownComponent, AvaTextboxComponent, AvaTextareaComponent, SliderComponent, FileUploadComponent, PopupComponent, ButtonComponent, TableComponent],\n  providers: [DecimalPipe],\n  templateUrl: './create-knowledge-base.component.html',\n  styleUrls: ['./create-knowledge-base.component.scss'],\n  schemas: [CUSTOM_ELEMENTS_SCHEMA]\n})], CreateKnowledgeBaseComponent);\nexport { CreateKnowledgeBaseComponent };", "map": {"version": 3, "names": ["Component", "CUSTOM_ELEMENTS_SCHEMA", "CommonModule", "DecimalPipe", "ReactiveFormsModule", "FormsModule", "Validators", "knowledgeBaseLabels", "SliderComponent", "DropdownComponent", "AvaTextboxComponent", "AvaTextareaComponent", "FileUploadComponent", "ButtonComponent", "TableComponent", "PopupComponent", "LucideAngularModule", "map", "CreateKnowledgeBaseComponent", "fb", "router", "_decimalPipe", "knowledgeBaseService", "route", "knowledgeBaseForm", "knowledgeBaseId", "submissionMessage", "submissionSuccess", "showInfoPopup", "fileUploadRequired", "isEditMode", "maxFileSize", "allowedFormats", "componentTitle", "showUploadButton", "showDialogCloseIcon", "selected<PERSON>et<PERSON>r", "labels", "default", "selectedUploadType", "uploadPlaceholder", "iconName", "iconColor", "searchModelText", "uploadedFiles", "splitSize", "parentSplitSize", "childSplitSize", "embeddingModelOptions", "schemeOptions", "uploadOptions", "kbLabels", "retrieverOptions", "parentDoc", "saveOrUpdateLabel", "save", "selectedControls", "azureBlob", "github", "sharePoint", "confluenceWiki", "database", "pathSuffix", "tableColumns", "key", "label", "type", "tableData", "isLeftCollapsed", "toggleLeftPanel", "constructor", "group", "name", "required", "<PERSON><PERSON><PERSON><PERSON>", "description", "retriever", "embeddingModel", "uploadType", "containerName", "accountName", "accountKey", "gith<PERSON><PERSON><PERSON>", "gith<PERSON><PERSON><PERSON>unt", "githubRepo", "githubBranch", "tenantId", "sharepointSiteName", "sharepointFolderPath", "email", "clientId", "clientSecret", "apiToken", "baseUrl", "spaceKey", "overlap", "scheme", "host", "port", "user", "password", "dbname", "query", "ngOnInit", "loadEmbeddingModelOptions", "loadUploadOptionsAndSetDefault", "loadSchemeDropdownOptions", "snapshot", "paramMap", "get", "loadEditModeData", "update", "getEmbeddingModelOptions", "subscribe", "options", "opt", "modelDeploymentName", "value", "String", "id", "getSchemeDropdowns", "response", "parsedValue", "JSON", "parse", "Object", "keys", "error", "console", "onUploadTypeChange", "event", "selected", "selectedOptions", "values", "flat", "for<PERSON>ach", "ctrlName", "ctrl", "getControl", "clearValidators", "setValue", "mark<PERSON><PERSON>ristine", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setValidators", "<PERSON><PERSON><PERSON><PERSON>ched", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updateValueAndValidity", "onSave", "invalid", "length", "mark<PERSON>llAsTouched", "suffix", "retrieverType", "knowledgeBase", "selectedModelValue", "selected<PERSON><PERSON>l", "find", "selectedModelId", "payload", "file", "formData", "FormData", "append", "submitUpload", "next", "info", "message", "err", "data", "getFieldError", "fieldName", "field", "formattedFieldName", "test", "char<PERSON>t", "toUpperCase", "slice", "touched", "dirty", "errors", "<PERSON><PERSON><PERSON><PERSON>", "onExit", "navigate", "getUploadDropdowns", "res", "getUploadTypeOptions", "firstOption", "defaultValue", "rawValue", "parsed", "to<PERSON><PERSON>", "toLowerCase", "replace", "selectRetriever", "onParentSplitSizeChange", "onChildSplitSizeChange", "splitSizeChange", "filesListChanged", "isSubmitDisabled", "isFormInvalid", "isUploadTypeFile", "isFileEmpty", "isParentDoc", "Number", "isNormalSplitSizeInvalid", "isParentChildInvalid", "handlePopupClose", "formatDocumentSize", "size", "transform", "formatRows", "row", "formatUploadDate", "dateStr", "date", "Date", "day", "getDate", "padStart", "month", "getMonth", "year", "getFullYear", "hours", "getHours", "minutes", "getMinutes", "getRetrieverLabel", "normal", "parent_doc_retriever", "kbId", "getKnowledgeBaseById", "files", "fileName", "fileSizeFormatted", "source", "fileSizeBytes", "uploadDateFormatted", "uploadDate", "disable", "fetchAllKnowledge", "pipe", "match", "item", "patchValue", "collectionName", "onSliderInputChange", "controlName", "inputValue", "target", "valueAsNumber", "cappedValue", "Math", "min", "__decorate", "selector", "standalone", "imports", "providers", "templateUrl", "styleUrls", "schemas"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\libraries\\knowledge-base\\create-knowledge-base\\create-knowledge-base.component.ts"], "sourcesContent": ["import { Component, CUSTOM_ELEMENTS_SCHEMA, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { DecimalPipe } from '@angular/common';\r\nimport {\r\n  FormBuilder,\r\n  FormGroup,\r\n  ReactiveFormsModule,\r\n  FormControl,\r\n  FormsModule,\r\n  Validators,\r\n} from '@angular/forms';\r\nimport { Router, ActivatedRoute } from '@angular/router';\r\nimport knowledgeBaseLabels from '../constants/knowledge-base.json';\r\nimport {\r\n  SliderComponent,\r\n  DropdownComponent,\r\n  AvaTextboxComponent,\r\n  AvaTextareaComponent,\r\n  FileUploadComponent,\r\n  ButtonComponent,\r\n  TableComponent,\r\n} from '@ava/play-comp-library';\r\nimport { KnowledgeBaseService } from 'projects/console/src/app/shared/services/knowledge-base.service';\r\nimport { PopupComponent } from '@ava/play-comp-library';\r\nimport { LucideAngularModule } from 'lucide-angular';\r\nimport { map } from 'rxjs/internal/operators/map';\r\n\r\n@Component({\r\n  selector: 'app-create-knowledge-base',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    ReactiveFormsModule,\r\n    FormsModule,\r\n    LucideAngularModule,\r\n    DropdownComponent,\r\n    AvaTextboxComponent,\r\n    AvaTextareaComponent,\r\n    SliderComponent,\r\n    FileUploadComponent,\r\n    PopupComponent,\r\n    ButtonComponent,\r\n    TableComponent,\r\n  ],\r\n  providers: [DecimalPipe],\r\n  templateUrl: './create-knowledge-base.component.html',\r\n  styleUrls: ['./create-knowledge-base.component.scss'],\r\n  schemas: [CUSTOM_ELEMENTS_SCHEMA],\r\n})\r\nexport class CreateKnowledgeBaseComponent implements OnInit {\r\n  // Form group for the knowledge base creation form\r\n  knowledgeBaseForm: FormGroup;\r\n\r\n  // Stores route param for edit mode\r\n  knowledgeBaseId: string | null = null;\r\n\r\n  // Popup & submission state\r\n  submissionMessage: string | null = null;\r\n  submissionSuccess = false;\r\n  showInfoPopup = false;\r\n  fileUploadRequired = false;\r\n\r\n  // UI mode tracking\r\n  isEditMode = false;\r\n\r\n  //maxFile size is 15MB \r\n  maxFileSize = 15 * 1024 * 1024;\r\n\r\n  // Allowed file formats for upload\r\n  allowedFormats: string[] = knowledgeBaseLabels.allowedFormats;\r\n\r\n  // UI text constants\r\n  componentTitle: string = 'Upload File Here';\r\n  showUploadButton = true;\r\n  showDialogCloseIcon = false;\r\n\r\n  // Selected options\r\n  selectedRetriever = knowledgeBaseLabels.labels.default;\r\n  selectedUploadType = 'upload-files';\r\n  uploadPlaceholder = 'Upload Files';\r\n  iconName = 'circle-check';\r\n  iconColor = 'red';\r\n\r\n  // Model search input text (currently unused)\r\n  searchModelText = '';\r\n\r\n  // Uploaded files\r\n  uploadedFiles: File[] = [];\r\n\r\n  // Split size fields\r\n  splitSize: number = 5000;\r\n  parentSplitSize: number = 5000;\r\n  childSplitSize: number = 2000;\r\n\r\n  // Dropdown data\r\n  embeddingModelOptions: { name: string; value: string }[] = [];\r\n  schemeOptions: { name: string; value: string }[] = [];\r\n\r\n  uploadOptions: any[] = [];\r\n\r\n  // Label map\r\n  kbLabels = knowledgeBaseLabels.labels;\r\n\r\n  // Retriever options for toggle\r\n  retrieverOptions: string[] = [this.kbLabels.default, this.kbLabels.parentDoc];\r\n\r\n  // Submit button label (Save / Update)\r\n  saveOrUpdateLabel: string = this.kbLabels.save;\r\n\r\n  // Controls required per upload type\r\n  private selectedControls: Record<string, string[]> = {\r\n    'upload-files': [],\r\n    'Azure Blob': knowledgeBaseLabels.azureBlob,\r\n    'GitHub': knowledgeBaseLabels.github,\r\n    'Share Point': knowledgeBaseLabels.sharePoint,\r\n    'Confluence Wiki': knowledgeBaseLabels.confluenceWiki,\r\n    'Database': knowledgeBaseLabels.database\r\n  };\r\n\r\n  // API path suffix map based on upload source\r\n  private pathSuffix: Record<string, string> = {\r\n    'Azure Blob': '/blob',\r\n    'GitHub': '/github',\r\n    'Share Point': '/sharepoint',\r\n    'Confluence Wiki': '/confluence',\r\n    'Database': '/database',\r\n  };\r\n\r\n  tableColumns = [\r\n    { key: 'fileName', label: 'File Name', type: 'text' },\r\n    { key: 'fileSizeFormatted', label: 'File Size', type: 'text' },\r\n    { key: 'uploadDateFormatted', label: 'Upload Date', type: 'text' },\r\n    { key: 'retrieverType', label: 'Retriever Type', type: 'text' },\r\n  ];\r\n\r\n  tableData: any[] = [];\r\n\r\n  isLeftCollapsed = false;\r\n\r\n  toggleLeftPanel() {\r\n    this.isLeftCollapsed = !this.isLeftCollapsed;\r\n  }\r\n\r\n  // Constructor injecting services and initializing form group\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private router: Router,\r\n    public _decimalPipe: DecimalPipe,\r\n    private knowledgeBaseService: KnowledgeBaseService,\r\n    private route: ActivatedRoute,\r\n  ) {\r\n    // Define form controls and validators\r\n    this.knowledgeBaseForm = this.fb.group({\r\n      name: ['', [Validators.required, Validators.minLength(3)]],\r\n      description: ['', Validators.required],\r\n      retriever: [this.kbLabels.default],\r\n      splitSize: [5000],\r\n      parentSplitSize: [5000],\r\n      childSplitSize: [2000],\r\n      embeddingModel: [null, Validators.required],\r\n      uploadType: ['', Validators.required],\r\n      containerName: [''],\r\n      accountName: [''],\r\n      accountKey: [''],\r\n      githubKey: [''],\r\n      githubAccount: [''],\r\n      githubRepo: [''],\r\n      githubBranch: [''],\r\n      tenantId: [''],\r\n      sharepointSiteName: [''],\r\n      sharepointFolderPath: [''],\r\n      email: [''],\r\n      clientId: [''],\r\n      clientSecret: [''],\r\n      apiToken: [''],\r\n      baseUrl: [''],\r\n      spaceKey: [''],\r\n      overlap: [''],\r\n      scheme: [''],\r\n      host: [''],\r\n      port: [''],\r\n      user: [''],\r\n      password: [''],\r\n      dbname: [''],\r\n      query: [''],\r\n    });\r\n  }\r\n\r\n  // Lifecycle hook: on component init\r\n  ngOnInit(): void {\r\n    this.loadEmbeddingModelOptions();\r\n    this.loadUploadOptionsAndSetDefault();\r\n    this.loadSchemeDropdownOptions();\r\n    // Check if editing an existing knowledge base\r\n    this.knowledgeBaseId = this.route.snapshot.paramMap.get('id');\r\n    this.isEditMode = !!this.knowledgeBaseId;\r\n    if (this.isEditMode) {\r\n      this.loadEditModeData();\r\n    }\r\n\r\n    this.saveOrUpdateLabel = this.isEditMode\r\n      ? this.kbLabels.update\r\n      : this.kbLabels.save;\r\n  }\r\n\r\n  // Fetch available embedding models\r\n  loadEmbeddingModelOptions(): void {\r\n    this.knowledgeBaseService\r\n      .getEmbeddingModelOptions()\r\n      .subscribe((options) => {\r\n        this.embeddingModelOptions = options.map((opt: any) => ({\r\n          name: opt.modelDeploymentName,\r\n          value: String(opt.id),\r\n        }));\r\n      });\r\n  }\r\n\r\n\r\n  // Scheme Dropdown values\r\n  loadSchemeDropdownOptions(): void {\r\n    this.knowledgeBaseService.getSchemeDropdowns().subscribe((response) => {\r\n      if (response && response.value) {\r\n        try {\r\n          const parsedValue = JSON.parse(response.value); // parse string to object\r\n          this.schemeOptions = Object.keys(parsedValue).map((key) => ({\r\n            name: key,\r\n            value: parsedValue[key]\r\n          }));\r\n        } catch (error) {\r\n          console.error('Failed to parse scheme options:', error);\r\n          this.schemeOptions = [];\r\n        }\r\n      } else {\r\n        this.schemeOptions = [];\r\n      }\r\n    });\r\n  }\r\n\r\n  // Triggered when upload type changes\r\n  onUploadTypeChange(event: any): void {\r\n    const selected = event?.selectedOptions?.[0]?.value;\r\n    this.selectedUploadType = selected || '';\r\n    this.uploadPlaceholder = '';\r\n\r\n    if (this.selectedUploadType !== 'upload-files') {\r\n      this.uploadedFiles = [];\r\n    }\r\n    // Clear all old validators AND values\r\n    Object.values(this.selectedControls)\r\n      .flat()\r\n      .forEach((ctrlName) => {\r\n        const ctrl = this.getControl(ctrlName);\r\n        if (ctrl) {\r\n          ctrl.clearValidators();\r\n          ctrl.setValue(null); // or ''\r\n          ctrl.markAsPristine();\r\n          ctrl.markAsUntouched();\r\n        }\r\n      });\r\n\r\n\r\n    // Apply required validators to new controls\r\n    this.selectedControls[this.selectedUploadType]?.forEach((ctrl) => {\r\n      this.getControl(ctrl)?.setValidators(Validators.required);\r\n    });\r\n\r\n    // Reapply validator to uploadType itself\r\n    this.getControl('uploadType')?.setValidators(Validators.required);\r\n\r\n    // Mark uploadType as interacted\r\n    this.getControl('uploadType')?.markAsTouched();\r\n    this.getControl('uploadType')?.markAsDirty();\r\n\r\n    // Recalculate form status\r\n    this.knowledgeBaseForm.updateValueAndValidity();\r\n  }\r\n\r\n  // Submit form handler\r\n  onSave(): void {\r\n    this.submissionSuccess = false;\r\n    this.fileUploadRequired = false;\r\n\r\n    // Basic form validation\r\n    if (this.knowledgeBaseForm.invalid && this.uploadedFiles.length === 0) {\r\n      this.knowledgeBaseForm.markAllAsTouched();\r\n      this.fileUploadRequired = true;\r\n      return;\r\n    }\r\n\r\n    const type = this.knowledgeBaseForm.value.uploadType;\r\n    const suffix = this.pathSuffix[type] || '';\r\n    const retrieverType =\r\n      this.selectedRetriever === this.kbLabels.parentDoc\r\n        ? 'parent_doc_retriever'\r\n        : 'normal';\r\n\r\n    const knowledgeBase = this.getControl('name').value;\r\n    const description = this.getControl('description').value;\r\n    const splitSize = this.getControl('splitSize').value;\r\n    const parentSplitSize = this.getControl('parentSplitSize')?.value;\r\n    const childSplitSize = this.getControl('childSplitSize')?.value;\r\n\r\n    // Get selected model ID\r\n    const selectedModelValue = this.knowledgeBaseForm.value['embeddingModel'];\r\n    const selectedModel = this.embeddingModelOptions.find(\r\n      (opt) =>\r\n        opt.name === selectedModelValue || opt.value === selectedModelValue,\r\n    );\r\n    const selectedModelId = selectedModel ? selectedModel.value : null;\r\n\r\n    // Construct base payload\r\n    const payload: Record<string, any> = {};\r\n\r\n    if (this.isEditMode && this.knowledgeBaseId) {\r\n      // Only send masterId during edit\r\n      payload['masterId'] = this.knowledgeBaseId;\r\n    } else {\r\n      // Full payload during create\r\n      payload['knowledgeBase'] = knowledgeBase;\r\n      payload['description'] = description;\r\n      payload['model-ref'] = selectedModelId;\r\n      payload['type'] = retrieverType;\r\n\r\n      if (retrieverType === 'parent_doc_retriever') {\r\n        payload['parentSplitSize'] = parentSplitSize;\r\n        payload['splitSize'] = childSplitSize;\r\n      } else {\r\n        payload['splitSize'] = splitSize;\r\n      }\r\n    }\r\n\r\n    // Handle file upload case\r\n    if (type === 'upload-files' || type == 'Upload Files') {\r\n      const file = this.uploadedFiles;\r\n      if (!file) return;\r\n\r\n      const formData = new FormData();\r\n      this.uploadedFiles.forEach((file: File) => {\r\n        formData.append('files', file);\r\n      });\r\n\r\n      // Submit form with files\r\n      this.knowledgeBaseService\r\n        .submitUpload(formData, payload, suffix)\r\n        .subscribe({\r\n          next: (info) => {\r\n            this.iconName = 'circle-check';\r\n            this.submissionMessage = info?.info?.message || info.message;\r\n            this.submissionSuccess = true;\r\n            this.showInfoPopup = true;\r\n            this.iconColor = 'green';\r\n          },\r\n          error: (err) => {\r\n            this.iconName = 'alert-circle';\r\n            this.submissionMessage =\r\n              err?.error?.message || err.message || 'Knowledge Base creation or update failed. Please try again.';\r\n            this.submissionSuccess = false;\r\n            this.showInfoPopup = true;\r\n            this.iconColor = 'red';\r\n          },\r\n        });\r\n\r\n      // Handle non-file (external source) upload case\r\n    } else {\r\n      const data: Record<string, any> = {};\r\n\r\n      // Add selected controls to both body and query params\r\n\r\n      this.selectedControls[type]?.forEach((key) => {\r\n        const value = this.getControl(key)?.value;\r\n        data[key] = value;\r\n\r\n        // If edit mode, also include these as query params (i.e., in payload)\r\n        if (this.isEditMode) {\r\n          payload[key] = value;\r\n        }\r\n      });\r\n\r\n      // Also add masterId for edit mode\r\n\r\n      this.knowledgeBaseService.submitUpload(data, payload, suffix).subscribe({\r\n        next: (info) => {\r\n          this.iconName = 'circle-check';\r\n          this.submissionMessage = info?.info?.message || info.message;\r\n          this.submissionSuccess = true;\r\n          this.showInfoPopup = true;\r\n          this.iconColor = 'green';\r\n        },\r\n        error: (err) => {\r\n          this.iconName = 'alert-circle';\r\n          this.submissionMessage =\r\n            err?.error?.message || err.message || 'Knowledge Base creation or update failed. Please try again.';\r\n          this.submissionSuccess = false;\r\n          this.showInfoPopup = true;\r\n          this.iconColor = 'red';\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  // Get a typed form control\r\n  getControl(name: string): FormControl {\r\n    return this.knowledgeBaseForm.get(name) as FormControl;\r\n  }\r\n\r\n  // Utility to return readable field validation error\r\n  getFieldError(fieldName: string): string {\r\n    const field = this.knowledgeBaseForm.get(fieldName);\r\n    // Capitalize only if first letter is not already uppercase\r\n    const formattedFieldName = /^[A-Z]/.test(fieldName)\r\n      ? fieldName\r\n      : fieldName.charAt(0).toUpperCase() + fieldName.slice(1);\r\n    if (field && field.invalid && (field.touched || field.dirty)) {\r\n      if (field.errors?.['required']) {\r\n        return `${formattedFieldName} is required`;\r\n      }\r\n      if (field.errors?.['email']) {\r\n        return 'Please enter a valid email address';\r\n      }\r\n      if (field.errors?.['minlength']) {\r\n        return `${formattedFieldName} must be at least ${field.errors['minlength'].requiredLength} characters long`;\r\n      }\r\n    } else if (fieldName == 'scheme' || fieldName == 'embeddingModel') {\r\n      if (field && field.errors?.['required']) {\r\n        return `${formattedFieldName} is required`;\r\n      }\r\n    }\r\n    return '';\r\n  }\r\n\r\n  // Navigate back to listing\r\n  onExit(): void {\r\n    this.router.navigate(['/libraries/knowledge-base']);\r\n  }\r\n\r\n  // Load upload options and set first one as default\r\n  loadUploadOptionsAndSetDefault(): void {\r\n    this.knowledgeBaseService.getUploadDropdowns().subscribe({\r\n      next: (res) => {\r\n        if (res?.value) {\r\n          this.uploadOptions = this.getUploadTypeOptions(res.value);\r\n\r\n          // Safely assign default value and apply\r\n          const firstOption = this.uploadOptions[0];\r\n          if (firstOption && firstOption.value) {\r\n            const defaultValue = firstOption.value;\r\n\r\n            // Set form control\r\n            this.knowledgeBaseForm.get('uploadType')?.setValue(defaultValue);\r\n\r\n            // Set component state\r\n            this.selectedUploadType = defaultValue;\r\n\r\n            // Trigger validators and placeholder update\r\n            this.onUploadTypeChange({\r\n              selectedOptions: [{ value: defaultValue }],\r\n            });\r\n          }\r\n        }\r\n      },\r\n      error: () => { },\r\n    });\r\n  }\r\n\r\n  // Converts string labels into slug format\r\n  getUploadTypeOptions(rawValue: string): { name: string; value: string }[] {\r\n    const parsed = JSON.parse(rawValue);\r\n    return Object.keys(parsed).map((label) => ({\r\n      name: label,\r\n      value: this.toSlug(label),\r\n    }));\r\n  }\r\n\r\n  // Utility for slug formatting\r\n  toSlug(label: string): string {\r\n    return label.toLowerCase().replace(/\\s+/g, '-');\r\n  }\r\n\r\n  // Handle retriever mode toggle\r\n  selectRetriever(retriever: string): void {\r\n    this.selectedRetriever = retriever;\r\n    this.knowledgeBaseForm.get('retriever')?.setValue(retriever);\r\n  }\r\n\r\n  onParentSplitSizeChange(event: any): void {\r\n    this.parentSplitSize = event;\r\n    this.knowledgeBaseForm\r\n      .get('parentSplitSize')\r\n      ?.setValue(this.parentSplitSize);\r\n  }\r\n\r\n  onChildSplitSizeChange(event: any): void {\r\n    this.childSplitSize = event;\r\n    this.knowledgeBaseForm.get('childSplitSize')?.setValue(this.childSplitSize);\r\n  }\r\n\r\n  // splitSize input setter\r\n  splitSizeChange(event: any) {\r\n    this.splitSize = event;\r\n    this.knowledgeBaseForm\r\n      .get('splitSize')\r\n      ?.setValue(this.splitSize);\r\n  }\r\n\r\n  \r\n  // Update file list after change\r\n  filesListChanged(file: File[]) {\r\n    this.uploadedFiles = [...file];\r\n  }\r\n\r\n  // Disable submit based on form or file state\r\n  isSubmitDisabled(): boolean {\r\n    const isFormInvalid = this.knowledgeBaseForm.invalid;\r\n    const isUploadTypeFile = this.selectedUploadType === 'upload-files';\r\n    const isFileEmpty = this.uploadedFiles.length === 0;\r\n    const isParentDoc = this.selectedRetriever === this.kbLabels.parentDoc;\r\n\r\n    const splitSize = Number(this.knowledgeBaseForm.get('splitSize')?.value);\r\n    const parentSplitSize = Number(this.knowledgeBaseForm.get('parentSplitSize')?.value);\r\n    const childSplitSize = Number(this.knowledgeBaseForm.get('childSplitSize')?.value);\r\n\r\n    //  Normal condition: splitSize must be at least 100\r\n    const isNormalSplitSizeInvalid =\r\n      !isParentDoc && splitSize < 100;\r\n\r\n    //  Parent-child condition:\r\n    // 1. parent must be > child\r\n    // 2. both must be >= 100\r\n    const isParentChildInvalid =\r\n      isParentDoc &&\r\n      (parentSplitSize <= childSplitSize ||\r\n        parentSplitSize < 100 ||\r\n        childSplitSize < 100);\r\n\r\n    return (\r\n      isFormInvalid ||\r\n      (isUploadTypeFile && isFileEmpty) ||\r\n      isParentChildInvalid ||\r\n      isNormalSplitSizeInvalid\r\n    );\r\n  }\r\n\r\n\r\n  // Close popup and navigate on success\r\n  handlePopupClose(): void {\r\n    this.showInfoPopup = false;\r\n    if (this.submissionSuccess) {\r\n      this.router.navigate(['/libraries/knowledge-base']);\r\n    }\r\n  }\r\n\r\n  // Format file size from bytes to MB string\r\n  formatDocumentSize(size: number): string {\r\n    if (size < 102400) {\r\n      return this._decimalPipe.transform(size / 1024, '1.0-2') + ' KB';\r\n    } else {\r\n      return this._decimalPipe.transform(size / 1048576, '1.2-2') + ' MB';\r\n    }\r\n  }\r\n\r\n  // Format rows\r\n  formatRows(row: number) {\r\n    return row > 1 ? `${row} rows` : `${row} row`;\r\n  }\r\n  \r\n  // Format date to dd/mm/yy, hh:mm format\r\n  formatUploadDate(dateStr: string): string {\r\n    const date = new Date(dateStr);\r\n    const day = String(date.getDate()).padStart(2, '0');\r\n    const month = String(date.getMonth() + 1).padStart(2, '0');\r\n    const year = String(date.getFullYear()).slice(-2);\r\n    const hours = String(date.getHours()).padStart(2, '0');\r\n    const minutes = String(date.getMinutes()).padStart(2, '0');\r\n    return `${day}/${month}/${year}, ${hours}:${minutes}`;\r\n  }\r\n\r\n  // Returns a user-friendly label for the given retriever type\r\n  getRetrieverLabel(type: string): string {\r\n    const map: Record<string, string> = {\r\n      normal: 'Default',\r\n      parent_doc_retriever: 'Parent Doc',\r\n    };\r\n    this.selectedRetriever = map[type];\r\n    return map[type] || 'Unknown';\r\n  }\r\n\r\n  private loadEditModeData(): void {\r\n    const kbId = Number(this.knowledgeBaseId);\r\n\r\n    // Fetch files and other details\r\n    this.knowledgeBaseService.getKnowledgeBaseById(kbId).subscribe({\r\n      next: (response) => {\r\n        if (response?.files?.length > 0) {\r\n          const retrieverType = response.retrieverType || 'normal';\r\n          this.tableData = response.files.map((file: any) => ({\r\n            fileName: file.fileName,\r\n            fileSizeFormatted: file.source === 'database'\r\n              ? this.formatRows(file.fileSizeBytes)\r\n              : this.formatDocumentSize(file.fileSizeBytes),\r\n            uploadDateFormatted: this.formatUploadDate(file.uploadDate),\r\n            retrieverType: this.getRetrieverLabel(retrieverType)\r\n          }));\r\n\r\n          // Disable fields in edit mode\r\n          ['name', 'description', 'splitSize', 'embeddingModel'].forEach(field =>\r\n            this.knowledgeBaseForm.get(field)?.disable()\r\n          );\r\n        } else {\r\n          this.tableData = [];\r\n        }\r\n      },\r\n      error: (err) => {\r\n        console.error('Error fetching knowledge base data', err);\r\n        this.tableData = [];\r\n      }\r\n    });\r\n\r\n    // Fetch and patch name/description\r\n    this.knowledgeBaseService.fetchAllKnowledge()\r\n      .pipe(\r\n        map((response: any[]) => {\r\n          const match = response.find(item => item.id === kbId);\r\n          if (match) {\r\n            this.knowledgeBaseForm.patchValue({\r\n              name: match.collectionName,\r\n              description: match.description,\r\n            });\r\n          }\r\n          return response;\r\n        })\r\n      )\r\n      .subscribe({\r\n        next: () => {\r\n          // Successfully patched the form (already done in map)\r\n        },\r\n        error: () => {\r\n        }\r\n      });\r\n  }\r\n\r\n  onSliderInputChange(event: any, controlName: string): void {\r\n    let inputValue: number;\r\n\r\n    // Handle both native event and direct value\r\n    if (event?.target) {\r\n      inputValue = (event.target as HTMLInputElement).valueAsNumber;\r\n    } else if (typeof event === 'number') {\r\n      inputValue = event;\r\n    } else {\r\n      inputValue = Number(event);\r\n    }\r\n\r\n    // Cap to max value (you can also parametrize this if needed)\r\n    const cappedValue = Math.min(inputValue, 20000);\r\n\r\n    // Update local variable based on controlName\r\n    if (controlName === 'parentSplitSize') {\r\n      this.parentSplitSize = cappedValue;\r\n    } else if (controlName === 'childSplitSize') {\r\n      this.childSplitSize = cappedValue;\r\n    } else if (controlName === 'splitSize') {\r\n      this.splitSize = cappedValue;\r\n    }\r\n    // Update the corresponding form control\r\n    this.knowledgeBaseForm.get(controlName)?.setValue(cappedValue);\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,sBAAsB,QAAgB,eAAe;AACzE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,SAGEC,mBAAmB,EAEnBC,WAAW,EACXC,UAAU,QACL,gBAAgB;AAEvB,OAAOC,mBAAmB,MAAM,kCAAkC;AAClE,SACEC,eAAe,EACfC,iBAAiB,EACjBC,mBAAmB,EACnBC,oBAAoB,EACpBC,mBAAmB,EACnBC,eAAe,EACfC,cAAc,QACT,wBAAwB;AAE/B,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,GAAG,QAAQ,6BAA6B;AAwB1C,IAAMC,4BAA4B,GAAlC,MAAMA,4BAA4B;EAgG7BC,EAAA;EACAC,MAAA;EACDC,YAAA;EACCC,oBAAA;EACAC,KAAA;EAnGV;EACAC,iBAAiB;EAEjB;EACAC,eAAe,GAAkB,IAAI;EAErC;EACAC,iBAAiB,GAAkB,IAAI;EACvCC,iBAAiB,GAAG,KAAK;EACzBC,aAAa,GAAG,KAAK;EACrBC,kBAAkB,GAAG,KAAK;EAE1B;EACAC,UAAU,GAAG,KAAK;EAElB;EACAC,WAAW,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI;EAE9B;EACAC,cAAc,GAAazB,mBAAmB,CAACyB,cAAc;EAE7D;EACAC,cAAc,GAAW,kBAAkB;EAC3CC,gBAAgB,GAAG,IAAI;EACvBC,mBAAmB,GAAG,KAAK;EAE3B;EACAC,iBAAiB,GAAG7B,mBAAmB,CAAC8B,MAAM,CAACC,OAAO;EACtDC,kBAAkB,GAAG,cAAc;EACnCC,iBAAiB,GAAG,cAAc;EAClCC,QAAQ,GAAG,cAAc;EACzBC,SAAS,GAAG,KAAK;EAEjB;EACAC,eAAe,GAAG,EAAE;EAEpB;EACAC,aAAa,GAAW,EAAE;EAE1B;EACAC,SAAS,GAAW,IAAI;EACxBC,eAAe,GAAW,IAAI;EAC9BC,cAAc,GAAW,IAAI;EAE7B;EACAC,qBAAqB,GAAsC,EAAE;EAC7DC,aAAa,GAAsC,EAAE;EAErDC,aAAa,GAAU,EAAE;EAEzB;EACAC,QAAQ,GAAG5C,mBAAmB,CAAC8B,MAAM;EAErC;EACAe,gBAAgB,GAAa,CAAC,IAAI,CAACD,QAAQ,CAACb,OAAO,EAAE,IAAI,CAACa,QAAQ,CAACE,SAAS,CAAC;EAE7E;EACAC,iBAAiB,GAAW,IAAI,CAACH,QAAQ,CAACI,IAAI;EAE9C;EACQC,gBAAgB,GAA6B;IACnD,cAAc,EAAE,EAAE;IAClB,YAAY,EAAEjD,mBAAmB,CAACkD,SAAS;IAC3C,QAAQ,EAAElD,mBAAmB,CAACmD,MAAM;IACpC,aAAa,EAAEnD,mBAAmB,CAACoD,UAAU;IAC7C,iBAAiB,EAAEpD,mBAAmB,CAACqD,cAAc;IACrD,UAAU,EAAErD,mBAAmB,CAACsD;GACjC;EAED;EACQC,UAAU,GAA2B;IAC3C,YAAY,EAAE,OAAO;IACrB,QAAQ,EAAE,SAAS;IACnB,aAAa,EAAE,aAAa;IAC5B,iBAAiB,EAAE,aAAa;IAChC,UAAU,EAAE;GACb;EAEDC,YAAY,GAAG,CACb;IAAEC,GAAG,EAAE,UAAU;IAAEC,KAAK,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAM,CAAE,EACrD;IAAEF,GAAG,EAAE,mBAAmB;IAAEC,KAAK,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAM,CAAE,EAC9D;IAAEF,GAAG,EAAE,qBAAqB;IAAEC,KAAK,EAAE,aAAa;IAAEC,IAAI,EAAE;EAAM,CAAE,EAClE;IAAEF,GAAG,EAAE,eAAe;IAAEC,KAAK,EAAE,gBAAgB;IAAEC,IAAI,EAAE;EAAM,CAAE,CAChE;EAEDC,SAAS,GAAU,EAAE;EAErBC,eAAe,GAAG,KAAK;EAEvBC,eAAeA,CAAA;IACb,IAAI,CAACD,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEA;EACAE,YACUnD,EAAe,EACfC,MAAc,EACfC,YAAyB,EACxBC,oBAA0C,EAC1CC,KAAqB;IAJrB,KAAAJ,EAAE,GAAFA,EAAE;IACF,KAAAC,MAAM,GAANA,MAAM;IACP,KAAAC,YAAY,GAAZA,YAAY;IACX,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,KAAK,GAALA,KAAK;IAEb;IACA,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACL,EAAE,CAACoD,KAAK,CAAC;MACrCC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAClE,UAAU,CAACmE,QAAQ,EAAEnE,UAAU,CAACoE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1DC,WAAW,EAAE,CAAC,EAAE,EAAErE,UAAU,CAACmE,QAAQ,CAAC;MACtCG,SAAS,EAAE,CAAC,IAAI,CAACzB,QAAQ,CAACb,OAAO,CAAC;MAClCO,SAAS,EAAE,CAAC,IAAI,CAAC;MACjBC,eAAe,EAAE,CAAC,IAAI,CAAC;MACvBC,cAAc,EAAE,CAAC,IAAI,CAAC;MACtB8B,cAAc,EAAE,CAAC,IAAI,EAAEvE,UAAU,CAACmE,QAAQ,CAAC;MAC3CK,UAAU,EAAE,CAAC,EAAE,EAAExE,UAAU,CAACmE,QAAQ,CAAC;MACrCM,aAAa,EAAE,CAAC,EAAE,CAAC;MACnBC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBC,SAAS,EAAE,CAAC,EAAE,CAAC;MACfC,aAAa,EAAE,CAAC,EAAE,CAAC;MACnBC,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBC,YAAY,EAAE,CAAC,EAAE,CAAC;MAClBC,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,kBAAkB,EAAE,CAAC,EAAE,CAAC;MACxBC,oBAAoB,EAAE,CAAC,EAAE,CAAC;MAC1BC,KAAK,EAAE,CAAC,EAAE,CAAC;MACXC,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,YAAY,EAAE,CAAC,EAAE,CAAC;MAClBC,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,OAAO,EAAE,CAAC,EAAE,CAAC;MACbC,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,OAAO,EAAE,CAAC,EAAE,CAAC;MACbC,MAAM,EAAE,CAAC,EAAE,CAAC;MACZC,IAAI,EAAE,CAAC,EAAE,CAAC;MACVC,IAAI,EAAE,CAAC,EAAE,CAAC;MACVC,IAAI,EAAE,CAAC,EAAE,CAAC;MACVC,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,MAAM,EAAE,CAAC,EAAE,CAAC;MACZC,KAAK,EAAE,CAAC,EAAE;KACX,CAAC;EACJ;EAEA;EACAC,QAAQA,CAAA;IACN,IAAI,CAACC,yBAAyB,EAAE;IAChC,IAAI,CAACC,8BAA8B,EAAE;IACrC,IAAI,CAACC,yBAAyB,EAAE;IAChC;IACA,IAAI,CAACjF,eAAe,GAAG,IAAI,CAACF,KAAK,CAACoF,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IAC7D,IAAI,CAAC/E,UAAU,GAAG,CAAC,CAAC,IAAI,CAACL,eAAe;IACxC,IAAI,IAAI,CAACK,UAAU,EAAE;MACnB,IAAI,CAACgF,gBAAgB,EAAE;IACzB;IAEA,IAAI,CAACxD,iBAAiB,GAAG,IAAI,CAACxB,UAAU,GACpC,IAAI,CAACqB,QAAQ,CAAC4D,MAAM,GACpB,IAAI,CAAC5D,QAAQ,CAACI,IAAI;EACxB;EAEA;EACAiD,yBAAyBA,CAAA;IACvB,IAAI,CAAClF,oBAAoB,CACtB0F,wBAAwB,EAAE,CAC1BC,SAAS,CAAEC,OAAO,IAAI;MACrB,IAAI,CAAClE,qBAAqB,GAAGkE,OAAO,CAACjG,GAAG,CAAEkG,GAAQ,KAAM;QACtD3C,IAAI,EAAE2C,GAAG,CAACC,mBAAmB;QAC7BC,KAAK,EAAEC,MAAM,CAACH,GAAG,CAACI,EAAE;OACrB,CAAC,CAAC;IACL,CAAC,CAAC;EACN;EAGA;EACAb,yBAAyBA,CAAA;IACvB,IAAI,CAACpF,oBAAoB,CAACkG,kBAAkB,EAAE,CAACP,SAAS,CAAEQ,QAAQ,IAAI;MACpE,IAAIA,QAAQ,IAAIA,QAAQ,CAACJ,KAAK,EAAE;QAC9B,IAAI;UACF,MAAMK,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACH,QAAQ,CAACJ,KAAK,CAAC,CAAC,CAAC;UAChD,IAAI,CAACpE,aAAa,GAAG4E,MAAM,CAACC,IAAI,CAACJ,WAAW,CAAC,CAACzG,GAAG,CAAE+C,GAAG,KAAM;YAC1DQ,IAAI,EAAER,GAAG;YACTqD,KAAK,EAAEK,WAAW,CAAC1D,GAAG;WACvB,CAAC,CAAC;QACL,CAAC,CAAC,OAAO+D,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;UACvD,IAAI,CAAC9E,aAAa,GAAG,EAAE;QACzB;MACF,CAAC,MAAM;QACL,IAAI,CAACA,aAAa,GAAG,EAAE;MACzB;IACF,CAAC,CAAC;EACJ;EAEA;EACAgF,kBAAkBA,CAACC,KAAU;IAC3B,MAAMC,QAAQ,GAAGD,KAAK,EAAEE,eAAe,GAAG,CAAC,CAAC,EAAEf,KAAK;IACnD,IAAI,CAAC9E,kBAAkB,GAAG4F,QAAQ,IAAI,EAAE;IACxC,IAAI,CAAC3F,iBAAiB,GAAG,EAAE;IAE3B,IAAI,IAAI,CAACD,kBAAkB,KAAK,cAAc,EAAE;MAC9C,IAAI,CAACK,aAAa,GAAG,EAAE;IACzB;IACA;IACAiF,MAAM,CAACQ,MAAM,CAAC,IAAI,CAAC7E,gBAAgB,CAAC,CACjC8E,IAAI,EAAE,CACNC,OAAO,CAAEC,QAAQ,IAAI;MACpB,MAAMC,IAAI,GAAG,IAAI,CAACC,UAAU,CAACF,QAAQ,CAAC;MACtC,IAAIC,IAAI,EAAE;QACRA,IAAI,CAACE,eAAe,EAAE;QACtBF,IAAI,CAACG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;QACrBH,IAAI,CAACI,cAAc,EAAE;QACrBJ,IAAI,CAACK,eAAe,EAAE;MACxB;IACF,CAAC,CAAC;IAGJ;IACA,IAAI,CAACtF,gBAAgB,CAAC,IAAI,CAACjB,kBAAkB,CAAC,EAAEgG,OAAO,CAAEE,IAAI,IAAI;MAC/D,IAAI,CAACC,UAAU,CAACD,IAAI,CAAC,EAAEM,aAAa,CAACzI,UAAU,CAACmE,QAAQ,CAAC;IAC3D,CAAC,CAAC;IAEF;IACA,IAAI,CAACiE,UAAU,CAAC,YAAY,CAAC,EAAEK,aAAa,CAACzI,UAAU,CAACmE,QAAQ,CAAC;IAEjE;IACA,IAAI,CAACiE,UAAU,CAAC,YAAY,CAAC,EAAEM,aAAa,EAAE;IAC9C,IAAI,CAACN,UAAU,CAAC,YAAY,CAAC,EAAEO,WAAW,EAAE;IAE5C;IACA,IAAI,CAACzH,iBAAiB,CAAC0H,sBAAsB,EAAE;EACjD;EAEA;EACAC,MAAMA,CAAA;IACJ,IAAI,CAACxH,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACE,kBAAkB,GAAG,KAAK;IAE/B;IACA,IAAI,IAAI,CAACL,iBAAiB,CAAC4H,OAAO,IAAI,IAAI,CAACxG,aAAa,CAACyG,MAAM,KAAK,CAAC,EAAE;MACrE,IAAI,CAAC7H,iBAAiB,CAAC8H,gBAAgB,EAAE;MACzC,IAAI,CAACzH,kBAAkB,GAAG,IAAI;MAC9B;IACF;IAEA,MAAMqC,IAAI,GAAG,IAAI,CAAC1C,iBAAiB,CAAC6F,KAAK,CAACvC,UAAU;IACpD,MAAMyE,MAAM,GAAG,IAAI,CAACzF,UAAU,CAACI,IAAI,CAAC,IAAI,EAAE;IAC1C,MAAMsF,aAAa,GACjB,IAAI,CAACpH,iBAAiB,KAAK,IAAI,CAACe,QAAQ,CAACE,SAAS,GAC9C,sBAAsB,GACtB,QAAQ;IAEd,MAAMoG,aAAa,GAAG,IAAI,CAACf,UAAU,CAAC,MAAM,CAAC,CAACrB,KAAK;IACnD,MAAM1C,WAAW,GAAG,IAAI,CAAC+D,UAAU,CAAC,aAAa,CAAC,CAACrB,KAAK;IACxD,MAAMxE,SAAS,GAAG,IAAI,CAAC6F,UAAU,CAAC,WAAW,CAAC,CAACrB,KAAK;IACpD,MAAMvE,eAAe,GAAG,IAAI,CAAC4F,UAAU,CAAC,iBAAiB,CAAC,EAAErB,KAAK;IACjE,MAAMtE,cAAc,GAAG,IAAI,CAAC2F,UAAU,CAAC,gBAAgB,CAAC,EAAErB,KAAK;IAE/D;IACA,MAAMqC,kBAAkB,GAAG,IAAI,CAAClI,iBAAiB,CAAC6F,KAAK,CAAC,gBAAgB,CAAC;IACzE,MAAMsC,aAAa,GAAG,IAAI,CAAC3G,qBAAqB,CAAC4G,IAAI,CAClDzC,GAAG,IACFA,GAAG,CAAC3C,IAAI,KAAKkF,kBAAkB,IAAIvC,GAAG,CAACE,KAAK,KAAKqC,kBAAkB,CACtE;IACD,MAAMG,eAAe,GAAGF,aAAa,GAAGA,aAAa,CAACtC,KAAK,GAAG,IAAI;IAElE;IACA,MAAMyC,OAAO,GAAwB,EAAE;IAEvC,IAAI,IAAI,CAAChI,UAAU,IAAI,IAAI,CAACL,eAAe,EAAE;MAC3C;MACAqI,OAAO,CAAC,UAAU,CAAC,GAAG,IAAI,CAACrI,eAAe;IAC5C,CAAC,MAAM;MACL;MACAqI,OAAO,CAAC,eAAe,CAAC,GAAGL,aAAa;MACxCK,OAAO,CAAC,aAAa,CAAC,GAAGnF,WAAW;MACpCmF,OAAO,CAAC,WAAW,CAAC,GAAGD,eAAe;MACtCC,OAAO,CAAC,MAAM,CAAC,GAAGN,aAAa;MAE/B,IAAIA,aAAa,KAAK,sBAAsB,EAAE;QAC5CM,OAAO,CAAC,iBAAiB,CAAC,GAAGhH,eAAe;QAC5CgH,OAAO,CAAC,WAAW,CAAC,GAAG/G,cAAc;MACvC,CAAC,MAAM;QACL+G,OAAO,CAAC,WAAW,CAAC,GAAGjH,SAAS;MAClC;IACF;IAEA;IACA,IAAIqB,IAAI,KAAK,cAAc,IAAIA,IAAI,IAAI,cAAc,EAAE;MACrD,MAAM6F,IAAI,GAAG,IAAI,CAACnH,aAAa;MAC/B,IAAI,CAACmH,IAAI,EAAE;MAEX,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/B,IAAI,CAACrH,aAAa,CAAC2F,OAAO,CAAEwB,IAAU,IAAI;QACxCC,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEH,IAAI,CAAC;MAChC,CAAC,CAAC;MAEF;MACA,IAAI,CAACzI,oBAAoB,CACtB6I,YAAY,CAACH,QAAQ,EAAEF,OAAO,EAAEP,MAAM,CAAC,CACvCtC,SAAS,CAAC;QACTmD,IAAI,EAAGC,IAAI,IAAI;UACb,IAAI,CAAC5H,QAAQ,GAAG,cAAc;UAC9B,IAAI,CAACf,iBAAiB,GAAG2I,IAAI,EAAEA,IAAI,EAAEC,OAAO,IAAID,IAAI,CAACC,OAAO;UAC5D,IAAI,CAAC3I,iBAAiB,GAAG,IAAI;UAC7B,IAAI,CAACC,aAAa,GAAG,IAAI;UACzB,IAAI,CAACc,SAAS,GAAG,OAAO;QAC1B,CAAC;QACDqF,KAAK,EAAGwC,GAAG,IAAI;UACb,IAAI,CAAC9H,QAAQ,GAAG,cAAc;UAC9B,IAAI,CAACf,iBAAiB,GACpB6I,GAAG,EAAExC,KAAK,EAAEuC,OAAO,IAAIC,GAAG,CAACD,OAAO,IAAI,6DAA6D;UACrG,IAAI,CAAC3I,iBAAiB,GAAG,KAAK;UAC9B,IAAI,CAACC,aAAa,GAAG,IAAI;UACzB,IAAI,CAACc,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;MAEJ;IACF,CAAC,MAAM;MACL,MAAM8H,IAAI,GAAwB,EAAE;MAEpC;MAEA,IAAI,CAAChH,gBAAgB,CAACU,IAAI,CAAC,EAAEqE,OAAO,CAAEvE,GAAG,IAAI;QAC3C,MAAMqD,KAAK,GAAG,IAAI,CAACqB,UAAU,CAAC1E,GAAG,CAAC,EAAEqD,KAAK;QACzCmD,IAAI,CAACxG,GAAG,CAAC,GAAGqD,KAAK;QAEjB;QACA,IAAI,IAAI,CAACvF,UAAU,EAAE;UACnBgI,OAAO,CAAC9F,GAAG,CAAC,GAAGqD,KAAK;QACtB;MACF,CAAC,CAAC;MAEF;MAEA,IAAI,CAAC/F,oBAAoB,CAAC6I,YAAY,CAACK,IAAI,EAAEV,OAAO,EAAEP,MAAM,CAAC,CAACtC,SAAS,CAAC;QACtEmD,IAAI,EAAGC,IAAI,IAAI;UACb,IAAI,CAAC5H,QAAQ,GAAG,cAAc;UAC9B,IAAI,CAACf,iBAAiB,GAAG2I,IAAI,EAAEA,IAAI,EAAEC,OAAO,IAAID,IAAI,CAACC,OAAO;UAC5D,IAAI,CAAC3I,iBAAiB,GAAG,IAAI;UAC7B,IAAI,CAACC,aAAa,GAAG,IAAI;UACzB,IAAI,CAACc,SAAS,GAAG,OAAO;QAC1B,CAAC;QACDqF,KAAK,EAAGwC,GAAG,IAAI;UACb,IAAI,CAAC9H,QAAQ,GAAG,cAAc;UAC9B,IAAI,CAACf,iBAAiB,GACpB6I,GAAG,EAAExC,KAAK,EAAEuC,OAAO,IAAIC,GAAG,CAACD,OAAO,IAAI,6DAA6D;UACrG,IAAI,CAAC3I,iBAAiB,GAAG,KAAK;UAC9B,IAAI,CAACC,aAAa,GAAG,IAAI;UACzB,IAAI,CAACc,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;IACJ;EACF;EAEA;EACAgG,UAAUA,CAAClE,IAAY;IACrB,OAAO,IAAI,CAAChD,iBAAiB,CAACqF,GAAG,CAACrC,IAAI,CAAgB;EACxD;EAEA;EACAiG,aAAaA,CAACC,SAAiB;IAC7B,MAAMC,KAAK,GAAG,IAAI,CAACnJ,iBAAiB,CAACqF,GAAG,CAAC6D,SAAS,CAAC;IACnD;IACA,MAAME,kBAAkB,GAAG,QAAQ,CAACC,IAAI,CAACH,SAAS,CAAC,GAC/CA,SAAS,GACTA,SAAS,CAACI,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGL,SAAS,CAACM,KAAK,CAAC,CAAC,CAAC;IAC1D,IAAIL,KAAK,IAAIA,KAAK,CAACvB,OAAO,KAAKuB,KAAK,CAACM,OAAO,IAAIN,KAAK,CAACO,KAAK,CAAC,EAAE;MAC5D,IAAIP,KAAK,CAACQ,MAAM,GAAG,UAAU,CAAC,EAAE;QAC9B,OAAO,GAAGP,kBAAkB,cAAc;MAC5C;MACA,IAAID,KAAK,CAACQ,MAAM,GAAG,OAAO,CAAC,EAAE;QAC3B,OAAO,oCAAoC;MAC7C;MACA,IAAIR,KAAK,CAACQ,MAAM,GAAG,WAAW,CAAC,EAAE;QAC/B,OAAO,GAAGP,kBAAkB,qBAAqBD,KAAK,CAACQ,MAAM,CAAC,WAAW,CAAC,CAACC,cAAc,kBAAkB;MAC7G;IACF,CAAC,MAAM,IAAIV,SAAS,IAAI,QAAQ,IAAIA,SAAS,IAAI,gBAAgB,EAAE;MACjE,IAAIC,KAAK,IAAIA,KAAK,CAACQ,MAAM,GAAG,UAAU,CAAC,EAAE;QACvC,OAAO,GAAGP,kBAAkB,cAAc;MAC5C;IACF;IACA,OAAO,EAAE;EACX;EAEA;EACAS,MAAMA,CAAA;IACJ,IAAI,CAACjK,MAAM,CAACkK,QAAQ,CAAC,CAAC,2BAA2B,CAAC,CAAC;EACrD;EAEA;EACA7E,8BAA8BA,CAAA;IAC5B,IAAI,CAACnF,oBAAoB,CAACiK,kBAAkB,EAAE,CAACtE,SAAS,CAAC;MACvDmD,IAAI,EAAGoB,GAAG,IAAI;QACZ,IAAIA,GAAG,EAAEnE,KAAK,EAAE;UACd,IAAI,CAACnE,aAAa,GAAG,IAAI,CAACuI,oBAAoB,CAACD,GAAG,CAACnE,KAAK,CAAC;UAEzD;UACA,MAAMqE,WAAW,GAAG,IAAI,CAACxI,aAAa,CAAC,CAAC,CAAC;UACzC,IAAIwI,WAAW,IAAIA,WAAW,CAACrE,KAAK,EAAE;YACpC,MAAMsE,YAAY,GAAGD,WAAW,CAACrE,KAAK;YAEtC;YACA,IAAI,CAAC7F,iBAAiB,CAACqF,GAAG,CAAC,YAAY,CAAC,EAAE+B,QAAQ,CAAC+C,YAAY,CAAC;YAEhE;YACA,IAAI,CAACpJ,kBAAkB,GAAGoJ,YAAY;YAEtC;YACA,IAAI,CAAC1D,kBAAkB,CAAC;cACtBG,eAAe,EAAE,CAAC;gBAAEf,KAAK,EAAEsE;cAAY,CAAE;aAC1C,CAAC;UACJ;QACF;MACF,CAAC;MACD5D,KAAK,EAAEA,CAAA,KAAK,CAAG;KAChB,CAAC;EACJ;EAEA;EACA0D,oBAAoBA,CAACG,QAAgB;IACnC,MAAMC,MAAM,GAAGlE,IAAI,CAACC,KAAK,CAACgE,QAAQ,CAAC;IACnC,OAAO/D,MAAM,CAACC,IAAI,CAAC+D,MAAM,CAAC,CAAC5K,GAAG,CAAEgD,KAAK,KAAM;MACzCO,IAAI,EAAEP,KAAK;MACXoD,KAAK,EAAE,IAAI,CAACyE,MAAM,CAAC7H,KAAK;KACzB,CAAC,CAAC;EACL;EAEA;EACA6H,MAAMA,CAAC7H,KAAa;IAClB,OAAOA,KAAK,CAAC8H,WAAW,EAAE,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;EACjD;EAEA;EACAC,eAAeA,CAACrH,SAAiB;IAC/B,IAAI,CAACxC,iBAAiB,GAAGwC,SAAS;IAClC,IAAI,CAACpD,iBAAiB,CAACqF,GAAG,CAAC,WAAW,CAAC,EAAE+B,QAAQ,CAAChE,SAAS,CAAC;EAC9D;EAEAsH,uBAAuBA,CAAChE,KAAU;IAChC,IAAI,CAACpF,eAAe,GAAGoF,KAAK;IAC5B,IAAI,CAAC1G,iBAAiB,CACnBqF,GAAG,CAAC,iBAAiB,CAAC,EACrB+B,QAAQ,CAAC,IAAI,CAAC9F,eAAe,CAAC;EACpC;EAEAqJ,sBAAsBA,CAACjE,KAAU;IAC/B,IAAI,CAACnF,cAAc,GAAGmF,KAAK;IAC3B,IAAI,CAAC1G,iBAAiB,CAACqF,GAAG,CAAC,gBAAgB,CAAC,EAAE+B,QAAQ,CAAC,IAAI,CAAC7F,cAAc,CAAC;EAC7E;EAEA;EACAqJ,eAAeA,CAAClE,KAAU;IACxB,IAAI,CAACrF,SAAS,GAAGqF,KAAK;IACtB,IAAI,CAAC1G,iBAAiB,CACnBqF,GAAG,CAAC,WAAW,CAAC,EACf+B,QAAQ,CAAC,IAAI,CAAC/F,SAAS,CAAC;EAC9B;EAGA;EACAwJ,gBAAgBA,CAACtC,IAAY;IAC3B,IAAI,CAACnH,aAAa,GAAG,CAAC,GAAGmH,IAAI,CAAC;EAChC;EAEA;EACAuC,gBAAgBA,CAAA;IACd,MAAMC,aAAa,GAAG,IAAI,CAAC/K,iBAAiB,CAAC4H,OAAO;IACpD,MAAMoD,gBAAgB,GAAG,IAAI,CAACjK,kBAAkB,KAAK,cAAc;IACnE,MAAMkK,WAAW,GAAG,IAAI,CAAC7J,aAAa,CAACyG,MAAM,KAAK,CAAC;IACnD,MAAMqD,WAAW,GAAG,IAAI,CAACtK,iBAAiB,KAAK,IAAI,CAACe,QAAQ,CAACE,SAAS;IAEtE,MAAMR,SAAS,GAAG8J,MAAM,CAAC,IAAI,CAACnL,iBAAiB,CAACqF,GAAG,CAAC,WAAW,CAAC,EAAEQ,KAAK,CAAC;IACxE,MAAMvE,eAAe,GAAG6J,MAAM,CAAC,IAAI,CAACnL,iBAAiB,CAACqF,GAAG,CAAC,iBAAiB,CAAC,EAAEQ,KAAK,CAAC;IACpF,MAAMtE,cAAc,GAAG4J,MAAM,CAAC,IAAI,CAACnL,iBAAiB,CAACqF,GAAG,CAAC,gBAAgB,CAAC,EAAEQ,KAAK,CAAC;IAElF;IACA,MAAMuF,wBAAwB,GAC5B,CAACF,WAAW,IAAI7J,SAAS,GAAG,GAAG;IAEjC;IACA;IACA;IACA,MAAMgK,oBAAoB,GACxBH,WAAW,KACV5J,eAAe,IAAIC,cAAc,IAChCD,eAAe,GAAG,GAAG,IACrBC,cAAc,GAAG,GAAG,CAAC;IAEzB,OACEwJ,aAAa,IACZC,gBAAgB,IAAIC,WAAY,IACjCI,oBAAoB,IACpBD,wBAAwB;EAE5B;EAGA;EACAE,gBAAgBA,CAAA;IACd,IAAI,CAAClL,aAAa,GAAG,KAAK;IAC1B,IAAI,IAAI,CAACD,iBAAiB,EAAE;MAC1B,IAAI,CAACP,MAAM,CAACkK,QAAQ,CAAC,CAAC,2BAA2B,CAAC,CAAC;IACrD;EACF;EAEA;EACAyB,kBAAkBA,CAACC,IAAY;IAC7B,IAAIA,IAAI,GAAG,MAAM,EAAE;MACjB,OAAO,IAAI,CAAC3L,YAAY,CAAC4L,SAAS,CAACD,IAAI,GAAG,IAAI,EAAE,OAAO,CAAC,GAAG,KAAK;IAClE,CAAC,MAAM;MACL,OAAO,IAAI,CAAC3L,YAAY,CAAC4L,SAAS,CAACD,IAAI,GAAG,OAAO,EAAE,OAAO,CAAC,GAAG,KAAK;IACrE;EACF;EAEA;EACAE,UAAUA,CAACC,GAAW;IACpB,OAAOA,GAAG,GAAG,CAAC,GAAG,GAAGA,GAAG,OAAO,GAAG,GAAGA,GAAG,MAAM;EAC/C;EAEA;EACAC,gBAAgBA,CAACC,OAAe;IAC9B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,OAAO,CAAC;IAC9B,MAAMG,GAAG,GAAGlG,MAAM,CAACgG,IAAI,CAACG,OAAO,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACnD,MAAMC,KAAK,GAAGrG,MAAM,CAACgG,IAAI,CAACM,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAMG,IAAI,GAAGvG,MAAM,CAACgG,IAAI,CAACQ,WAAW,EAAE,CAAC,CAAC9C,KAAK,CAAC,CAAC,CAAC,CAAC;IACjD,MAAM+C,KAAK,GAAGzG,MAAM,CAACgG,IAAI,CAACU,QAAQ,EAAE,CAAC,CAACN,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACtD,MAAMO,OAAO,GAAG3G,MAAM,CAACgG,IAAI,CAACY,UAAU,EAAE,CAAC,CAACR,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,OAAO,GAAGF,GAAG,IAAIG,KAAK,IAAIE,IAAI,KAAKE,KAAK,IAAIE,OAAO,EAAE;EACvD;EAEA;EACAE,iBAAiBA,CAACjK,IAAY;IAC5B,MAAMjD,GAAG,GAA2B;MAClCmN,MAAM,EAAE,SAAS;MACjBC,oBAAoB,EAAE;KACvB;IACD,IAAI,CAACjM,iBAAiB,GAAGnB,GAAG,CAACiD,IAAI,CAAC;IAClC,OAAOjD,GAAG,CAACiD,IAAI,CAAC,IAAI,SAAS;EAC/B;EAEQ4C,gBAAgBA,CAAA;IACtB,MAAMwH,IAAI,GAAG3B,MAAM,CAAC,IAAI,CAAClL,eAAe,CAAC;IAEzC;IACA,IAAI,CAACH,oBAAoB,CAACiN,oBAAoB,CAACD,IAAI,CAAC,CAACrH,SAAS,CAAC;MAC7DmD,IAAI,EAAG3C,QAAQ,IAAI;QACjB,IAAIA,QAAQ,EAAE+G,KAAK,EAAEnF,MAAM,GAAG,CAAC,EAAE;UAC/B,MAAMG,aAAa,GAAG/B,QAAQ,CAAC+B,aAAa,IAAI,QAAQ;UACxD,IAAI,CAACrF,SAAS,GAAGsD,QAAQ,CAAC+G,KAAK,CAACvN,GAAG,CAAE8I,IAAS,KAAM;YAClD0E,QAAQ,EAAE1E,IAAI,CAAC0E,QAAQ;YACvBC,iBAAiB,EAAE3E,IAAI,CAAC4E,MAAM,KAAK,UAAU,GACzC,IAAI,CAACzB,UAAU,CAACnD,IAAI,CAAC6E,aAAa,CAAC,GACnC,IAAI,CAAC7B,kBAAkB,CAAChD,IAAI,CAAC6E,aAAa,CAAC;YAC/CC,mBAAmB,EAAE,IAAI,CAACzB,gBAAgB,CAACrD,IAAI,CAAC+E,UAAU,CAAC;YAC3DtF,aAAa,EAAE,IAAI,CAAC2E,iBAAiB,CAAC3E,aAAa;WACpD,CAAC,CAAC;UAEH;UACA,CAAC,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,gBAAgB,CAAC,CAACjB,OAAO,CAACoC,KAAK,IAClE,IAAI,CAACnJ,iBAAiB,CAACqF,GAAG,CAAC8D,KAAK,CAAC,EAAEoE,OAAO,EAAE,CAC7C;QACH,CAAC,MAAM;UACL,IAAI,CAAC5K,SAAS,GAAG,EAAE;QACrB;MACF,CAAC;MACD4D,KAAK,EAAGwC,GAAG,IAAI;QACbvC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEwC,GAAG,CAAC;QACxD,IAAI,CAACpG,SAAS,GAAG,EAAE;MACrB;KACD,CAAC;IAEF;IACA,IAAI,CAAC7C,oBAAoB,CAAC0N,iBAAiB,EAAE,CAC1CC,IAAI,CACHhO,GAAG,CAAEwG,QAAe,IAAI;MACtB,MAAMyH,KAAK,GAAGzH,QAAQ,CAACmC,IAAI,CAACuF,IAAI,IAAIA,IAAI,CAAC5H,EAAE,KAAK+G,IAAI,CAAC;MACrD,IAAIY,KAAK,EAAE;QACT,IAAI,CAAC1N,iBAAiB,CAAC4N,UAAU,CAAC;UAChC5K,IAAI,EAAE0K,KAAK,CAACG,cAAc;UAC1B1K,WAAW,EAAEuK,KAAK,CAACvK;SACpB,CAAC;MACJ;MACA,OAAO8C,QAAQ;IACjB,CAAC,CAAC,CACH,CACAR,SAAS,CAAC;MACTmD,IAAI,EAAEA,CAAA,KAAK;QACT;MAAA,CACD;MACDrC,KAAK,EAAEA,CAAA,KAAK,CACZ;KACD,CAAC;EACN;EAEAuH,mBAAmBA,CAACpH,KAAU,EAAEqH,WAAmB;IACjD,IAAIC,UAAkB;IAEtB;IACA,IAAItH,KAAK,EAAEuH,MAAM,EAAE;MACjBD,UAAU,GAAItH,KAAK,CAACuH,MAA2B,CAACC,aAAa;IAC/D,CAAC,MAAM,IAAI,OAAOxH,KAAK,KAAK,QAAQ,EAAE;MACpCsH,UAAU,GAAGtH,KAAK;IACpB,CAAC,MAAM;MACLsH,UAAU,GAAG7C,MAAM,CAACzE,KAAK,CAAC;IAC5B;IAEA;IACA,MAAMyH,WAAW,GAAGC,IAAI,CAACC,GAAG,CAACL,UAAU,EAAE,KAAK,CAAC;IAE/C;IACA,IAAID,WAAW,KAAK,iBAAiB,EAAE;MACrC,IAAI,CAACzM,eAAe,GAAG6M,WAAW;IACpC,CAAC,MAAM,IAAIJ,WAAW,KAAK,gBAAgB,EAAE;MAC3C,IAAI,CAACxM,cAAc,GAAG4M,WAAW;IACnC,CAAC,MAAM,IAAIJ,WAAW,KAAK,WAAW,EAAE;MACtC,IAAI,CAAC1M,SAAS,GAAG8M,WAAW;IAC9B;IACA;IACA,IAAI,CAACnO,iBAAiB,CAACqF,GAAG,CAAC0I,WAAW,CAAC,EAAE3G,QAAQ,CAAC+G,WAAW,CAAC;EAChE;CACD;AAzmBYzO,4BAA4B,GAAA4O,UAAA,EAtBxC9P,SAAS,CAAC;EACT+P,QAAQ,EAAE,2BAA2B;EACrCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACP/P,YAAY,EACZE,mBAAmB,EACnBC,WAAW,EACXW,mBAAmB,EACnBP,iBAAiB,EACjBC,mBAAmB,EACnBC,oBAAoB,EACpBH,eAAe,EACfI,mBAAmB,EACnBG,cAAc,EACdF,eAAe,EACfC,cAAc,CACf;EACDoP,SAAS,EAAE,CAAC/P,WAAW,CAAC;EACxBgQ,WAAW,EAAE,wCAAwC;EACrDC,SAAS,EAAE,CAAC,wCAAwC,CAAC;EACrDC,OAAO,EAAE,CAACpQ,sBAAsB;CACjC,CAAC,C,EACWiB,4BAA4B,CAymBxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}