{"ast": null, "code": "import { inject } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { of } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport { AuthService } from '../services/auth.service';\nimport { TokenStorageService } from '../services/token-storage.service';\nimport * as i0 from \"@angular/core\";\nexport let AuthGuard = /*#__PURE__*/(() => {\n  class AuthGuard {\n    authService = inject(AuthService);\n    router = inject(Router);\n    tokenStorage = inject(TokenStorageService);\n    canActivate(route, state) {\n      const accessToken = this.tokenStorage.getAccessToken();\n      const refreshToken = this.tokenStorage.getRefreshToken();\n      const loginType = this.tokenStorage.getLoginType();\n      const url = state.url;\n      // Allow access to login and callback pages without authentication\n      if (url.includes('/login') || url.includes('/callback') || url.includes('/marketplace')) {\n        return of(true);\n      }\n      // If we have a valid access token, allow access\n      if (accessToken) {\n        return of(true);\n      }\n      // If we have a refresh token but no access token, try to refresh\n      if (!accessToken && refreshToken) {\n        return this.authService.refreshToken(refreshToken).pipe(map(() => true), catchError(() => {\n          // If refresh fails, clear tokens and redirect to login\n          this.tokenStorage.clearTokens();\n          return of(this.router.createUrlTree(['/login']));\n        }));\n      }\n      // If no tokens at all, redirect to login\n      if (!accessToken && !refreshToken) {\n        return of(this.router.createUrlTree(['/login']));\n      }\n      return of(true);\n    }\n    handleSSOAuth() {\n      const hasAccessToken = !!this.tokenStorage.getAccessToken();\n      if (!hasAccessToken) {\n        console.info('No access token found, redirecting to login');\n        return of(this.router.createUrlTree(['/login']));\n      }\n      return of(true);\n    }\n    handleBasicLoginAuth() {\n      const hasAccessToken = !!this.tokenStorage.getAccessToken();\n      if (!hasAccessToken) {\n        console.info('No access token found, redirecting to login');\n        return of(this.router.createUrlTree(['/login']));\n      }\n      return of(true);\n    }\n    static ɵfac = function AuthGuard_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AuthGuard)();\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthGuard,\n      factory: AuthGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return AuthGuard;\n})();", "map": {"version": 3, "names": ["inject", "Router", "of", "map", "catchError", "AuthService", "TokenStorageService", "<PERSON><PERSON><PERSON><PERSON>", "authService", "router", "tokenStorage", "canActivate", "route", "state", "accessToken", "getAccessToken", "refreshToken", "getRefreshToken", "loginType", "getLoginType", "url", "includes", "pipe", "clearTokens", "createUrlTree", "handleSSOAuth", "hasAccessToken", "console", "info", "handleBasicLoginAuth", "factory", "ɵfac", "providedIn"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\auth\\guards\\auth.guard.ts"], "sourcesContent": ["import { Injectable, inject } from '@angular/core';\r\nimport {\r\n  CanActivate,\r\n  Router,\r\n  ActivatedRouteSnapshot,\r\n  RouterStateSnapshot,\r\n  UrlTree,\r\n} from '@angular/router';\r\nimport { Observable, of } from 'rxjs';\r\nimport { map, take, catchError } from 'rxjs/operators';\r\nimport { AuthService } from '../services/auth.service';\r\nimport { TokenStorageService } from '../services/token-storage.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class AuthGuard implements CanActivate {\r\n  private readonly authService = inject(AuthService);\r\n  private readonly router = inject(Router);\r\n  private readonly tokenStorage = inject(TokenStorageService);\r\n\r\n  canActivate(\r\n    route: ActivatedRouteSnapshot,\r\n    state: RouterStateSnapshot,\r\n  ): Observable<boolean | UrlTree> {\r\n    const accessToken = this.tokenStorage.getAccessToken();\r\n    const refreshToken = this.tokenStorage.getRefreshToken();\r\n    const loginType = this.tokenStorage.getLoginType();\r\n    const url = state.url;\r\n\r\n    // Allow access to login and callback pages without authentication\r\n    if (url.includes('/login') || url.includes('/callback') || url.includes('/marketplace')) {\r\n      return of(true);\r\n    }\r\n\r\n    // If we have a valid access token, allow access\r\n    if (accessToken) {\r\n      return of(true);\r\n    }\r\n\r\n    // If we have a refresh token but no access token, try to refresh\r\n    if (!accessToken && refreshToken) {\r\n      return this.authService.refreshToken(refreshToken).pipe(\r\n        map(() => true),\r\n        catchError(() => {\r\n          // If refresh fails, clear tokens and redirect to login\r\n          this.tokenStorage.clearTokens();\r\n          return of(this.router.createUrlTree(['/login']));\r\n        })\r\n      );\r\n    }\r\n\r\n    // If no tokens at all, redirect to login\r\n    if (!accessToken && !refreshToken) {\r\n      return of(this.router.createUrlTree(['/login']));\r\n    }\r\n\r\n    return of(true);\r\n  }\r\n\r\n  private handleSSOAuth(): Observable<boolean | UrlTree> {\r\n    const hasAccessToken = !!this.tokenStorage.getAccessToken();\r\n    if (!hasAccessToken) {\r\n      console.info('No access token found, redirecting to login');\r\n      return of(this.router.createUrlTree(['/login']));\r\n    }\r\n    return of(true);\r\n  }\r\n\r\n  private handleBasicLoginAuth(): Observable<boolean | UrlTree> {\r\n    const hasAccessToken = !!this.tokenStorage.getAccessToken();\r\n    if (!hasAccessToken) {\r\n      console.info('No access token found, redirecting to login');\r\n      return of(this.router.createUrlTree(['/login']));\r\n    }\r\n    return of(true);\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,MAAM,QAAQ,eAAe;AAClD,SAEEC,MAAM,QAID,iBAAiB;AACxB,SAAqBC,EAAE,QAAQ,MAAM;AACrC,SAASC,GAAG,EAAQC,UAAU,QAAQ,gBAAgB;AACtD,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,mBAAmB,QAAQ,mCAAmC;;AAKvE,WAAaC,SAAS;EAAhB,MAAOA,SAAS;IACHC,WAAW,GAAGR,MAAM,CAACK,WAAW,CAAC;IACjCI,MAAM,GAAGT,MAAM,CAACC,MAAM,CAAC;IACvBS,YAAY,GAAGV,MAAM,CAACM,mBAAmB,CAAC;IAE3DK,WAAWA,CACTC,KAA6B,EAC7BC,KAA0B;MAE1B,MAAMC,WAAW,GAAG,IAAI,CAACJ,YAAY,CAACK,cAAc,EAAE;MACtD,MAAMC,YAAY,GAAG,IAAI,CAACN,YAAY,CAACO,eAAe,EAAE;MACxD,MAAMC,SAAS,GAAG,IAAI,CAACR,YAAY,CAACS,YAAY,EAAE;MAClD,MAAMC,GAAG,GAAGP,KAAK,CAACO,GAAG;MAErB;MACA,IAAIA,GAAG,CAACC,QAAQ,CAAC,QAAQ,CAAC,IAAID,GAAG,CAACC,QAAQ,CAAC,WAAW,CAAC,IAAID,GAAG,CAACC,QAAQ,CAAC,cAAc,CAAC,EAAE;QACvF,OAAOnB,EAAE,CAAC,IAAI,CAAC;MACjB;MAEA;MACA,IAAIY,WAAW,EAAE;QACf,OAAOZ,EAAE,CAAC,IAAI,CAAC;MACjB;MAEA;MACA,IAAI,CAACY,WAAW,IAAIE,YAAY,EAAE;QAChC,OAAO,IAAI,CAACR,WAAW,CAACQ,YAAY,CAACA,YAAY,CAAC,CAACM,IAAI,CACrDnB,GAAG,CAAC,MAAM,IAAI,CAAC,EACfC,UAAU,CAAC,MAAK;UACd;UACA,IAAI,CAACM,YAAY,CAACa,WAAW,EAAE;UAC/B,OAAOrB,EAAE,CAAC,IAAI,CAACO,MAAM,CAACe,aAAa,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;QAClD,CAAC,CAAC,CACH;MACH;MAEA;MACA,IAAI,CAACV,WAAW,IAAI,CAACE,YAAY,EAAE;QACjC,OAAOd,EAAE,CAAC,IAAI,CAACO,MAAM,CAACe,aAAa,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;MAClD;MAEA,OAAOtB,EAAE,CAAC,IAAI,CAAC;IACjB;IAEQuB,aAAaA,CAAA;MACnB,MAAMC,cAAc,GAAG,CAAC,CAAC,IAAI,CAAChB,YAAY,CAACK,cAAc,EAAE;MAC3D,IAAI,CAACW,cAAc,EAAE;QACnBC,OAAO,CAACC,IAAI,CAAC,6CAA6C,CAAC;QAC3D,OAAO1B,EAAE,CAAC,IAAI,CAACO,MAAM,CAACe,aAAa,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;MAClD;MACA,OAAOtB,EAAE,CAAC,IAAI,CAAC;IACjB;IAEQ2B,oBAAoBA,CAAA;MAC1B,MAAMH,cAAc,GAAG,CAAC,CAAC,IAAI,CAAChB,YAAY,CAACK,cAAc,EAAE;MAC3D,IAAI,CAACW,cAAc,EAAE;QACnBC,OAAO,CAACC,IAAI,CAAC,6CAA6C,CAAC;QAC3D,OAAO1B,EAAE,CAAC,IAAI,CAACO,MAAM,CAACe,aAAa,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;MAClD;MACA,OAAOtB,EAAE,CAAC,IAAI,CAAC;IACjB;;uCA5DWK,SAAS;IAAA;;aAATA,SAAS;MAAAuB,OAAA,EAATvB,SAAS,CAAAwB,IAAA;MAAAC,UAAA,EAFR;IAAM;;SAEPzB,SAAS;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}