{"ast": null, "code": "import { map } from 'rxjs';\nimport { HttpHeaders, HttpParams } from '@angular/common/http';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./environment.service\";\nexport let ModelService = /*#__PURE__*/(() => {\n  class ModelService {\n    http;\n    environmentService;\n    HttpOptions = {\n      headers: new HttpHeaders({\n        'Content-Type': 'application/json'\n      })\n    };\n    constructor(http, environmentService) {\n      this.http = http;\n      this.environmentService = environmentService;\n    }\n    get apiServiceUrl() {\n      return this.environmentService.consoleApi;\n    }\n    getAllModelList() {\n      const url = `${this.apiServiceUrl}/ava/force/model`;\n      return this.http.get(url, this.HttpOptions).pipe(map(response => {\n        return response.models;\n      }));\n    }\n    getGenerativeModels() {\n      const url = `${this.apiServiceUrl}/ava/force/model?modelType=Generative`;\n      return this.http.get(url, this.HttpOptions).pipe(map(response => {\n        return response.models;\n      }));\n    }\n    /**\n     * Fetch models by model type and AI engine, expecting the new array-of-objects format.\n     * @param modelType The type of the model (e.g., 'Generative', 'Embedding')\n     * @param aiEngine The AI engine (e.g., 'AzureOpenAI', 'AmazonBedrock')\n     */\n    getModelsByTypeAndEngine(modelType, aiEngine) {\n      const url = `${this.apiServiceUrl}/ava/force/model/list?modelType=${encodeURIComponent(modelType)}&aiEngine=${encodeURIComponent(aiEngine)}`;\n      return this.http.get(url, this.HttpOptions);\n    }\n    getDropdownOptions(refKey, reverseLabel = false, rawArray = false) {\n      const url = `${this.apiServiceUrl}/ava/force/refdata?ref_key=${encodeURIComponent(refKey)}`;\n      return this.http.get(url, this.HttpOptions).pipe(map(res => {\n        const parsed = JSON.parse(res.value);\n        if (rawArray && Array.isArray(parsed)) {\n          return parsed;\n        }\n        const optionsArray = Object.keys(parsed).map(key => {\n          return reverseLabel ? {\n            value: parsed[key],\n            label: key\n          } : {\n            value: key,\n            label: parsed[key]\n          };\n        });\n        return optionsArray;\n      }));\n    }\n    saveModel(payload) {\n      const url = `${this.apiServiceUrl}/ava/force/model`;\n      return this.http.post(url, payload, this.HttpOptions);\n    }\n    getOneModeById(id) {\n      const url = `${this.apiServiceUrl}/ava/force/model`;\n      let param = new HttpParams();\n      param = param.append('modelId', id);\n      return this.http.get(url, {\n        params: param,\n        headers: this.HttpOptions.headers\n      }).pipe(map(response => {\n        return response.model;\n      }));\n    }\n    static ɵfac = function ModelService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ModelService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.EnvironmentService));\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ModelService,\n      factory: ModelService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return ModelService;\n})();", "map": {"version": 3, "names": ["map", "HttpHeaders", "HttpParams", "ModelService", "http", "environmentService", "HttpOptions", "headers", "constructor", "apiServiceUrl", "consoleApi", "getAllModelList", "url", "get", "pipe", "response", "models", "getGenerativeModels", "getModelsByTypeAndEngine", "modelType", "aiEngine", "encodeURIComponent", "getDropdownOptions", "refKey", "reverseLabel", "rawArray", "res", "parsed", "JSON", "parse", "value", "Array", "isArray", "optionsArray", "Object", "keys", "key", "label", "saveModel", "payload", "post", "getOneModeById", "id", "param", "append", "params", "model", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "EnvironmentService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\services\\model.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Observable, map } from 'rxjs';\r\nimport { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';\r\nimport { EnvironmentService } from './environment.service';\r\nimport { Model, GetAllModelResponse, GetModelResponse } from '../models/card.model';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class ModelService {\r\n  private readonly HttpOptions = {\r\n    headers: new HttpHeaders({\r\n      'Content-Type': 'application/json',\r\n    }),\r\n  };\r\n\r\n  constructor(\r\n    private http: HttpClient,\r\n    private environmentService: EnvironmentService\r\n  ) {}\r\n\r\n  private get apiServiceUrl(): string {\r\n    return this.environmentService.consoleApi;\r\n  }\r\n\r\n  getAllModelList(): Observable<Model[]> {\r\n    const url = `${this.apiServiceUrl }/ava/force/model`\r\n    return this.http.get<GetAllModelResponse>(url, this.HttpOptions).pipe(\r\n      map((response) => {\r\n        return response.models;\r\n      })\r\n    );\r\n  }\r\n\r\n  getGenerativeModels(): Observable<Model[]> {\r\n    const url = `${this.apiServiceUrl}/ava/force/model?modelType=Generative`;\r\n    return this.http.get<GetAllModelResponse>(url, this.HttpOptions).pipe(\r\n      map((response) => {\r\n        return response.models;\r\n      })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Fetch models by model type and AI engine, expecting the new array-of-objects format.\r\n   * @param modelType The type of the model (e.g., 'Generative', 'Embedding')\r\n   * @param aiEngine The AI engine (e.g., 'AzureOpenAI', 'AmazonBedrock')\r\n   */\r\n  getModelsByTypeAndEngine(modelType: string, aiEngine: string): Observable<{ type: string; models: { id: string; name: string }[] }[]> {\r\n    const url = `${this.apiServiceUrl}/ava/force/model/list?modelType=${encodeURIComponent(modelType)}&aiEngine=${encodeURIComponent(aiEngine)}`;\r\n    return this.http.get<{ type: string; models: { id: string; name: string }[] }[]>(url, this.HttpOptions);\r\n  }\r\n\r\n  public getDropdownOptions(refKey: string, reverseLabel: boolean = false, rawArray: boolean = false) {\r\n    const url = `${this.apiServiceUrl }/ava/force/refdata?ref_key=${encodeURIComponent(refKey)}`;\r\n    return this.http.get(url, this.HttpOptions).pipe(\r\n      map((res: any) => {\r\n        const parsed = JSON.parse(res.value);\r\n        if (rawArray && Array.isArray(parsed)) {\r\n          return parsed;\r\n        }\r\n        const optionsArray = Object.keys(parsed).map(key => {\r\n          return reverseLabel\r\n            ? { value: parsed[key], label: key }\r\n            : { value: key, label: parsed[key] };\r\n        });\r\n        return optionsArray;\r\n      })\r\n    );\r\n  }\r\n\r\n  saveModel(payload: Model): Observable<any> {\r\n    const url = `${this.apiServiceUrl }/ava/force/model`\r\n    return this.http.post(url, payload, this.HttpOptions);\r\n  }\r\n\r\n  getOneModeById(id: string | number) {\r\n    const url = `${this.apiServiceUrl }/ava/force/model`\r\n    let param = new HttpParams();\r\n    param = param.append('modelId', id);\r\n    return this.http.get<GetModelResponse>(url, { params: param, headers: this.HttpOptions.headers }).pipe(\r\n      map((response) => {\r\n        return response.model;\r\n      })\r\n    );\r\n  }\r\n}\r\n"], "mappings": "AACA,SAAqBA,GAAG,QAAQ,MAAM;AACtC,SAAqBC,WAAW,EAAEC,UAAU,QAAQ,sBAAsB;;;;AAO1E,WAAaC,YAAY;EAAnB,MAAOA,YAAY;IAQbC,IAAA;IACAC,kBAAA;IAROC,WAAW,GAAG;MAC7BC,OAAO,EAAE,IAAIN,WAAW,CAAC;QACvB,cAAc,EAAE;OACjB;KACF;IAEDO,YACUJ,IAAgB,EAChBC,kBAAsC;MADtC,KAAAD,IAAI,GAAJA,IAAI;MACJ,KAAAC,kBAAkB,GAAlBA,kBAAkB;IACzB;IAEH,IAAYI,aAAaA,CAAA;MACvB,OAAO,IAAI,CAACJ,kBAAkB,CAACK,UAAU;IAC3C;IAEAC,eAAeA,CAAA;MACb,MAAMC,GAAG,GAAG,GAAG,IAAI,CAACH,aAAc,kBAAkB;MACpD,OAAO,IAAI,CAACL,IAAI,CAACS,GAAG,CAAsBD,GAAG,EAAE,IAAI,CAACN,WAAW,CAAC,CAACQ,IAAI,CACnEd,GAAG,CAAEe,QAAQ,IAAI;QACf,OAAOA,QAAQ,CAACC,MAAM;MACxB,CAAC,CAAC,CACH;IACH;IAEAC,mBAAmBA,CAAA;MACjB,MAAML,GAAG,GAAG,GAAG,IAAI,CAACH,aAAa,uCAAuC;MACxE,OAAO,IAAI,CAACL,IAAI,CAACS,GAAG,CAAsBD,GAAG,EAAE,IAAI,CAACN,WAAW,CAAC,CAACQ,IAAI,CACnEd,GAAG,CAAEe,QAAQ,IAAI;QACf,OAAOA,QAAQ,CAACC,MAAM;MACxB,CAAC,CAAC,CACH;IACH;IAEA;;;;;IAKAE,wBAAwBA,CAACC,SAAiB,EAAEC,QAAgB;MAC1D,MAAMR,GAAG,GAAG,GAAG,IAAI,CAACH,aAAa,mCAAmCY,kBAAkB,CAACF,SAAS,CAAC,aAAaE,kBAAkB,CAACD,QAAQ,CAAC,EAAE;MAC5I,OAAO,IAAI,CAAChB,IAAI,CAACS,GAAG,CAA6DD,GAAG,EAAE,IAAI,CAACN,WAAW,CAAC;IACzG;IAEOgB,kBAAkBA,CAACC,MAAc,EAAEC,YAAA,GAAwB,KAAK,EAAEC,QAAA,GAAoB,KAAK;MAChG,MAAMb,GAAG,GAAG,GAAG,IAAI,CAACH,aAAc,8BAA8BY,kBAAkB,CAACE,MAAM,CAAC,EAAE;MAC5F,OAAO,IAAI,CAACnB,IAAI,CAACS,GAAG,CAACD,GAAG,EAAE,IAAI,CAACN,WAAW,CAAC,CAACQ,IAAI,CAC9Cd,GAAG,CAAE0B,GAAQ,IAAI;QACf,MAAMC,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACH,GAAG,CAACI,KAAK,CAAC;QACpC,IAAIL,QAAQ,IAAIM,KAAK,CAACC,OAAO,CAACL,MAAM,CAAC,EAAE;UACrC,OAAOA,MAAM;QACf;QACA,MAAMM,YAAY,GAAGC,MAAM,CAACC,IAAI,CAACR,MAAM,CAAC,CAAC3B,GAAG,CAACoC,GAAG,IAAG;UACjD,OAAOZ,YAAY,GACf;YAAEM,KAAK,EAAEH,MAAM,CAACS,GAAG,CAAC;YAAEC,KAAK,EAAED;UAAG,CAAE,GAClC;YAAEN,KAAK,EAAEM,GAAG;YAAEC,KAAK,EAAEV,MAAM,CAACS,GAAG;UAAC,CAAE;QACxC,CAAC,CAAC;QACF,OAAOH,YAAY;MACrB,CAAC,CAAC,CACH;IACH;IAEAK,SAASA,CAACC,OAAc;MACtB,MAAM3B,GAAG,GAAG,GAAG,IAAI,CAACH,aAAc,kBAAkB;MACpD,OAAO,IAAI,CAACL,IAAI,CAACoC,IAAI,CAAC5B,GAAG,EAAE2B,OAAO,EAAE,IAAI,CAACjC,WAAW,CAAC;IACvD;IAEAmC,cAAcA,CAACC,EAAmB;MAChC,MAAM9B,GAAG,GAAG,GAAG,IAAI,CAACH,aAAc,kBAAkB;MACpD,IAAIkC,KAAK,GAAG,IAAIzC,UAAU,EAAE;MAC5ByC,KAAK,GAAGA,KAAK,CAACC,MAAM,CAAC,SAAS,EAAEF,EAAE,CAAC;MACnC,OAAO,IAAI,CAACtC,IAAI,CAACS,GAAG,CAAmBD,GAAG,EAAE;QAAEiC,MAAM,EAAEF,KAAK;QAAEpC,OAAO,EAAE,IAAI,CAACD,WAAW,CAACC;MAAO,CAAE,CAAC,CAACO,IAAI,CACpGd,GAAG,CAAEe,QAAQ,IAAI;QACf,OAAOA,QAAQ,CAAC+B,KAAK;MACvB,CAAC,CAAC,CACH;IACH;;uCA5EW3C,YAAY,EAAA4C,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,kBAAA;IAAA;;aAAZjD,YAAY;MAAAkD,OAAA,EAAZlD,YAAY,CAAAmD,IAAA;MAAAC,UAAA,EAFX;IAAM;;SAEPpD,YAAY;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}