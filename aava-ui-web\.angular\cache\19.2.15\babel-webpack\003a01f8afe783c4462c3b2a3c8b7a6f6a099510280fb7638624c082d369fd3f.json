{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { Subject, takeUntil } from 'rxjs';\nimport { FormsModule, Validators } from '@angular/forms';\n// Import child components\nimport { ChatInterfaceComponent } from '@shared/components/chat-interface/chat-interface.component';\nimport { AgentActivityComponent } from './components/agent-activity/agent-activity.component';\nimport { AgentOutputComponent } from './components/agent-output/agent-output.component';\nimport { ButtonComponent, IconComponent, TabsComponent } from '@ava/play-comp-library';\nimport { environment } from '@shared/environments/environment';\nimport workflowConstants from './../constants/workflows.json';\nimport { ExecutionStatus } from '@shared/models/execution.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@shared/services/workflow.service\";\nimport * as i3 from \"@shared/index\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/common\";\nconst _c0 = () => ({\n  \"background\": \"#1A46A7\",\n  \"width\": \"100%\",\n  \"border-radius\": \"8px\"\n});\nconst _c1 = () => ({\n  background: \"linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)\",\n  \"--button-effect-color\": \"33, 90, 214\",\n  \"border-radius\": \"8px\",\n  \"box-shadow\": \"none\"\n});\nfunction WorkflowExecutionComponent_ng_container_15_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"span\", 38);\n    i0.ɵɵtext(2, \"Model:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 39);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const agent_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(agent_r4.model);\n  }\n}\nfunction WorkflowExecutionComponent_ng_container_15_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"span\", 38);\n    i0.ɵɵtext(2, \"Tools:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 39);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const agent_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(agent_r4.tools.join(\", \"));\n  }\n}\nfunction WorkflowExecutionComponent_ng_container_15_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 40);\n  }\n}\nfunction WorkflowExecutionComponent_ng_container_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 27)(2, \"div\", 28)(3, \"div\", 29);\n    i0.ɵɵelement(4, \"ava-icon\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 31)(6, \"h3\", 32);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"ava-icon\", 33);\n    i0.ɵɵlistener(\"click\", function WorkflowExecutionComponent_ng_container_15_Template_ava_icon_click_8_listener() {\n      const i_r2 = i0.ɵɵrestoreView(_r1).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleAgentExpansion(i_r2));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 34);\n    i0.ɵɵtemplate(10, WorkflowExecutionComponent_ng_container_15_div_10_Template, 5, 1, \"div\", 35)(11, WorkflowExecutionComponent_ng_container_15_div_11_Template, 5, 1, \"div\", 35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(12, WorkflowExecutionComponent_ng_container_15_div_12_Template, 1, 0, \"div\", 36);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const agent_r4 = ctx.$implicit;\n    const i_r2 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"completed\", ctx_r2.agentStatuses[i_r2] == null ? null : ctx_r2.agentStatuses[i_r2].completed);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"iconName\", (ctx_r2.agentStatuses[i_r2] == null ? null : ctx_r2.agentStatuses[i_r2].completed) ? \"check\" : \"folder\")(\"iconColor\", (ctx_r2.agentStatuses[i_r2] == null ? null : ctx_r2.agentStatuses[i_r2].completed) ? \"#22C55E\" : \"#1A46A7\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((agent_r4 == null ? null : agent_r4.name) || \"Agent \" + (i_r2 + 1));\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"expanded\", ctx_r2.expandedAgents[i_r2]);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"expanded\", ctx_r2.expandedAgents[i_r2]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", agent_r4 == null ? null : agent_r4.model);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", agent_r4 == null ? null : agent_r4.tools == null ? null : agent_r4.tools.length);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r2 < ctx_r2.workflowAgents.length - 1);\n  }\n}\nfunction WorkflowExecutionComponent_div_16_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 46);\n    i0.ɵɵelement(2, \"textarea\", 47);\n    i0.ɵɵelementStart(3, \"div\", 48);\n    i0.ɵɵelement(4, \"ava-icon\", 49)(5, \"ava-icon\", 50)(6, \"ava-icon\", 51);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const input_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"formControlName\", input_r5.input)(\"placeholder\", \"Input Entered\");\n  }\n}\nfunction WorkflowExecutionComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"div\", 42)(2, \"div\", 29);\n    i0.ɵɵelement(3, \"ava-icon\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\", 44);\n    i0.ɵɵtext(5, \"Enter the input\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 45);\n    i0.ɵɵtemplate(7, WorkflowExecutionComponent_div_16_ng_container_7_Template, 7, 2, \"ng-container\", 14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.workflowForm);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.userInputList);\n  }\n}\nfunction WorkflowExecutionComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"app-agent-activity\", 53);\n    i0.ɵɵlistener(\"saveLogs\", function WorkflowExecutionComponent_div_29_Template_app_agent_activity_saveLogs_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.saveLogs());\n    })(\"controlAction\", function WorkflowExecutionComponent_div_29_Template_app_agent_activity_controlAction_1_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.handleControlAction($event));\n    })(\"onOutPutBtnClick\", function WorkflowExecutionComponent_div_29_Template_app_agent_activity_onOutPutBtnClick_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onTabChange({\n        id: \"nav-products\",\n        label: \"Agent Output\"\n      }));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"activityLogs\", ctx_r2.workflowLogs)(\"executionDetails\", ctx_r2.executionDetails)(\"progress\", ctx_r2.progress)(\"isRunning\", ctx_r2.isRunning)(\"status\", ctx_r2.status);\n  }\n}\nfunction WorkflowExecutionComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"app-agent-output\", 54);\n    i0.ɵɵlistener(\"export\", function WorkflowExecutionComponent_div_30_Template_app_agent_output_export_1_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.exportResults(\"output\"));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"outputs\", ctx_r2.taskMessage);\n  }\n}\nexport let WorkflowExecutionComponent = /*#__PURE__*/(() => {\n  class WorkflowExecutionComponent {\n    route;\n    router;\n    workflowService;\n    tokenStorage;\n    loaderService;\n    formBuilder;\n    navigationTabs = [{\n      id: 'nav-home',\n      label: 'Agent Activity'\n    }, {\n      id: 'nav-products',\n      label: 'Agent Output'\n    }, {\n      id: 'nav-services',\n      label: 'Preview',\n      disabled: true\n    }];\n    // Workflow details\n    workflowId = null;\n    workflowName = 'Workflow';\n    constants = workflowConstants;\n    chatInterfaceComp;\n    // Activity logs\n    activityLogs = [];\n    activityProgress = 0;\n    executionDetails;\n    isRunning = false;\n    status = ExecutionStatus.notStarted;\n    // Chat messages\n    chatMessages = [];\n    isProcessingChat = false;\n    inputText = '';\n    // Agent outputs\n    agentOutputs = [];\n    workflowForm;\n    fileType = '.zip';\n    // Execution state\n    executionStartTime = null;\n    executionCompleted = false;\n    executionId;\n    workflowLogs = [];\n    enableStreamingLog = environment.enableLogStreaming || 'all';\n    isExecutionComplete = false;\n    progressInterval;\n    // Component lifecycle\n    destroy$ = new Subject();\n    selectedTab = 'Agent Activity';\n    demoTabs = [{\n      id: 'activity',\n      label: 'Agent Activity'\n    }, {\n      id: 'agents',\n      label: 'Agent Output'\n    }, {\n      id: 'preview',\n      label: 'Preview',\n      disabled: true\n    }];\n    errorMsg = false;\n    resMessage;\n    taskMessage = [];\n    isJsonValid = false;\n    disableChat = false;\n    selectedFiles = [];\n    workflowAgents = [];\n    userInputList = [];\n    progress = 0;\n    isLoading = false;\n    loaderColor = '';\n    inputFieldOrder = [];\n    currentInputIndex = 0;\n    activeTabId = 'nav-home';\n    // Panel state\n    isLeftPanelCollapsed = false;\n    expandedAgents = [];\n    agentStatuses = [];\n    constructor(route, router, workflowService, tokenStorage, loaderService, formBuilder) {\n      this.route = route;\n      this.router = router;\n      this.workflowService = workflowService;\n      this.tokenStorage = tokenStorage;\n      this.loaderService = loaderService;\n      this.formBuilder = formBuilder;\n    }\n    ngOnInit() {\n      this.loaderService.disableLoader();\n      this.selectedTab = 'Agent Activity';\n      this.executionId = crypto.randomUUID();\n      // Get workflow ID from route params\n      this.route.paramMap.pipe(takeUntil(this.destroy$)).subscribe(params => {\n        this.workflowId = params.get('id');\n        if (this.workflowId) {\n          this.loadWorkflow(this.workflowId);\n        } else {\n          // No workflow ID, redirect back to workflows page\n          this.router.navigate(['/build/workflows']);\n        }\n      });\n      // this.executeWorkflow()\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n      this.loaderService.enableLoader();\n    }\n    onTabChange(event) {\n      this.selectedTab = event.label;\n      this.activeTabId = event.id;\n      console.log('Tab changed:', event);\n    }\n    // Load workflow data\n    loadWorkflow(id) {\n      // In a real app, this would fetch the workflow from a service\n      console.log(`Loading workflow with ID: ${id}`);\n      this.chatMessages = [{\n        from: 'ai',\n        text: 'I am your workflow assistant. I will help you in executing this workflow.'\n      }];\n      this.workflowForm = this.formBuilder.group({});\n      this.workflowService.getWorkflowById(id).subscribe({\n        next: res => {\n          this.workflowAgents = res.workflowAgents;\n          this.userInputList = this.extractInputField(this.workflowAgents);\n          if (this.userInputList.length === 0) {\n            this.disableChat = true;\n          }\n          this.workflowName = res.name;\n          this.initializeForm();\n          this.initializeAgentStates();\n          this.startInputCollection();\n        },\n        error: err => {\n          this.disableChat = true;\n          this.chatInterfaceComp.addAiResponse('No input is required for this workflow. Click on send button to execute the workflow.');\n          console.log(err);\n        }\n      });\n    }\n    extractInputField(pipeLineAgents) {\n      const PLACEHOLDER_PATTERNS = /(%\\d+\\$s)|\\{\\{([a-zA-Z0-9-_]+)\\}\\}/g;\n      const placeholderMap = {};\n      pipeLineAgents.forEach(agent => {\n        const agentName = agent?.agentDetails?.name;\n        const agentDescription = agent?.agentDetails?.description;\n        const matches = agentDescription.matchAll(PLACEHOLDER_PATTERNS) || [];\n        for (const match of matches) {\n          const placeholder = match[1] || match[2];\n          const placeholderInput = match[0];\n          if (!placeholderMap[placeholder]) {\n            placeholderMap[placeholder] = {\n              agents: new Set(),\n              inputs: new Set()\n            };\n            ;\n          }\n          placeholderMap[placeholder].agents.add(agentName);\n          placeholderMap[placeholder].inputs.add(placeholderInput);\n        }\n      });\n      return Object.entries(placeholderMap).map(([placeholder, {\n        agents,\n        inputs\n      }]) => ({\n        name: [...agents].length > 2 ? `${[...agents].slice(0, -1).join(\", \")} and ${[...agents].at(-1)}` : [...agents].join(\" and \"),\n        placeholder,\n        input: [...inputs][0]\n      }));\n    }\n    isImageInput(input) {\n      const match = input.match(/{{(.*?)}}/);\n      if (match && match[1]) {\n        const variableName = match[1].trim();\n        return variableName.startsWith('image') || variableName.startsWith('Image');\n      }\n      return false;\n    }\n    initializeForm() {\n      this.userInputList.forEach(label => {\n        this.workflowForm.addControl(label.input, this.formBuilder.control('', Validators.required));\n      });\n    }\n    isInputValid() {\n      return this.workflowForm.valid && this.workflowId;\n    }\n    startFakeProgress() {\n      this.progress = 0;\n      this.progressInterval = setInterval(() => {\n        if (this.progress < 90) {\n          this.progress += 5; // Increase slowly\n        }\n      }, 200); // Adjust speed\n    }\n    stopFakeProgress() {\n      clearInterval(this.progressInterval);\n      this.progress = 100;\n      setTimeout(() => {\n        this.isLoading = false;\n      }, 500); // Small delay to let user see 100%\n    }\n    // Handle new chat message from user\n    handleChatMessage(message) {\n      // console.log('message ', message, 'is blank', message.trim() === '');\n      this.isProcessingChat = true;\n      if (message.trim() === '') {\n        if (this.inputFieldOrder.length === 0) {\n          this.chatInterfaceComp.addAiResponse('Executing the workflow...');\n          this.executeWorkflow();\n        }\n        return;\n      }\n      if (this.isExecutionComplete || this.currentInputIndex === this.inputFieldOrder.length) {\n        this.chatInterfaceComp.addAiResponse('Executing the workflow...');\n        this.executeWorkflow();\n        return;\n      }\n      const field = this.inputFieldOrder[this.currentInputIndex];\n      if (this.isImageInput(field)) {\n        // Ignore text input, wait for file input\n        this.chatInterfaceComp.addAiResponse(`Please upload an image file for ${field}`);\n        return;\n      }\n      this.workflowForm.get(field)?.setValue(message);\n      this.currentInputIndex++;\n      if (this.currentInputIndex < this.inputFieldOrder.length) {\n        this.promptForCurrentField();\n      } else {\n        this.chatInterfaceComp.addAiResponse('Thank you for the input! Executing the workflow...');\n        this.executeWorkflow();\n      }\n    }\n    // Save execution logs\n    saveLogs() {\n      console.log('Saving execution logs...');\n      // This would typically save to a service\n    }\n    // Export results\n    exportResults(section) {\n      console.log(`Exporting ${section} data...`);\n      if (section === 'activity') {\n        const data = this.activityLogs.map(log => `[${log.timestamp}] ${log.message}`).join('\\n');\n        this.downloadAsFile(data, 'workflow-activity-logs.txt', 'text/plain');\n      } else {\n        const data = JSON.stringify(this.agentOutputs, null, 2);\n        this.downloadAsFile(data, 'workflow-outputs.json', 'application/json');\n      }\n    }\n    // Helper method to download data as a file\n    downloadAsFile(data, filename, type) {\n      const blob = new Blob([data], {\n        type\n      });\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = filename;\n      link.click();\n      URL.revokeObjectURL(url);\n    }\n    // Handle controls for execution\n    handleControlAction(action) {\n      console.log(`Control action: ${action}`);\n      // In a real app, this would control the workflow execution\n      if (action === 'play') {\n        this.isRunning = true;\n      } else if (action === 'pause' || action === 'stop') {\n        this.isRunning = false;\n      }\n    }\n    // Navigate back to workflow listing\n    navigateBack() {\n      this.router.navigate(['/build/workflows']);\n    }\n    // Navigate to edit workflow\n    editWorkflow() {\n      if (this.workflowId) {\n        this.router.navigate(['/build/workflows/edit', this.workflowId]);\n      }\n    }\n    logExecutionStatus(delay = 2000) {\n      setTimeout(() => {\n        if (!this.isExecutionComplete) {\n          console.log(this.constants);\n          console.log(this.constants['labels'].workflowExecProcessing);\n          this.workflowLogs.push({\n            content: this.constants['labels'].workflowExecProcessing,\n            color: '#F9DB24'\n          });\n        }\n      }, delay);\n    }\n    // public parseAnsiString(ansiString: string) {\n    //   const regex = ansiRegex();\n    //   const parts = ansiString.split(regex);\n    //   const matches = [...ansiString.matchAll(regex)];\n    //   parts.forEach((part, index) => {\n    //     if (part.trim() !== '') {\n    //       let colorCode = matches[index - 1][0];\n    //       if (index - 2 >= 0 && matches[index - 2]?.includes('\\u001b[1m')) {\n    //         colorCode = `\\u001b[1m${colorCode}`;\n    //       }\n    //       this.workflowLogs.push({\n    //         content: part,\n    //         color: this.colorMap[colorCode] || 'white',\n    //       });\n    //     }\n    //   });\n    // }\n    getWorkflowLogs(executionId) {\n      this.workflowService.workflowLogConnect(executionId).pipe(takeUntil(this.destroy$)).subscribe({\n        next: message => {\n          console.log('message: ', message);\n          const {\n            content,\n            color\n          } = message;\n          if (color) {\n            this.workflowLogs.push({\n              content,\n              color\n            });\n          } else if (this.enableStreamingLog === 'all') {\n            // this.parseAnsiString(content);\n          }\n        },\n        error: err => {\n          this.workflowLogs.push({\n            content: this.constants['workflowLog'],\n            color: 'red'\n          });\n          console.error('WebSocket error:', err);\n        },\n        complete: () => {\n          this.logExecutionStatus();\n          console.log('WebSocket connection closed');\n        }\n      });\n    }\n    // public parseAnsiString(ansiString: string) {\n    //   const regex = ansiRegex();\n    //   const parts = ansiString.split(regex);\n    //   const matches = [...ansiString.matchAll(regex)];\n    //   parts.forEach((part, index) => {\n    //     if (part.trim() !== '') {\n    //       let colorCode = matches[index-1][0];\n    //       if(index - 2 >= 0 && matches[index-2]?.includes('\\u001b[1m')) {\n    //         colorCode = `\\u001b[1m${colorCode}`;\n    //       }\n    //       this.workflowLogs.push({\n    //         content: part, \n    //         color: this.colorMap[colorCode] || 'white', \n    //       });\n    //     }\n    //   });\n    // }\n    validateJson(output) {\n      this.isJsonValid = false;\n      try {\n        const parsedOutput = JSON.parse(output);\n        this.isJsonValid = true;\n        return parsedOutput;\n      } catch (e) {\n        return null;\n      }\n    }\n    executeWorkflow() {\n      let payload = new FormData();\n      let queryString = '';\n      this.status = ExecutionStatus.running;\n      if (this.selectedFiles.length) {\n        this.selectedFiles.forEach(file => {\n          payload.append('files', file);\n        });\n        payload.append('workflowId', this.workflowId);\n        payload.append('userInputs', JSON.stringify(this.workflowForm.value));\n        payload.append('user', this.tokenStorage.getDaUsername());\n        payload.append('executionId', this.executionId);\n        queryString = '/files';\n      } else {\n        payload = {\n          pipeLineId: this.workflowId,\n          userInputs: this.workflowForm.value,\n          executionId: this.executionId,\n          user: this.tokenStorage.getDaUsername()\n        };\n      }\n      this.getWorkflowLogs(this.executionId);\n      this.startFakeProgress();\n      this.workflowService.executeWorkflow(payload, queryString).pipe(takeUntil(this.destroy$)).subscribe({\n        next: res => {\n          this.isProcessingChat = false;\n          this.isRunning = false;\n          this.chatInterfaceComp.addAiResponse(res?.message || \"Workflow execution completed successfully!\");\n          if (res?.workflowResponse?.pipeline?.output) {\n            this.isExecutionComplete = true;\n            // console.log(this.constants['labels'].workflowExecComplete);\n            this.workflowLogs.push({\n              content: this.constants['labels'].workflowExecComplete,\n              color: '#0F8251'\n            });\n            this.errorMsg = false;\n            this.resMessage = res?.workflowResponse?.pipeline?.output;\n            this.agentOutputs = res?.workflowResponse?.pipeline?.tasksOutputs.map(task => {\n              return {\n                id: task?.id || '',\n                title: task?.title || '',\n                content: task?.content || '',\n                agentName: task?.agentName || '',\n                timestamp: task?.timestamp || '',\n                type: task?.type || '',\n                description: task?.description || '',\n                expected_output: task?.expected_output || '',\n                summary: task?.summary || '',\n                raw: task?.raw || ''\n              };\n            });\n            this.taskMessage = res?.workflowResponse?.pipeline?.tasksOutputs.map(task => {\n              return {\n                description: task.description,\n                summary: task.summary,\n                raw: task.raw,\n                expected_output: task.expected_output\n              };\n            });\n            // if(\"file_download_url\" in res?.pipeline){\n            //   this.isFileWriter = true;\n            //   this.fileDownloadLink = res?.pipeline?.file_download_url;\n            //   if(!this.fileDownloadLink){\n            //     this.fileDownloadUrlError = [];\n            //     this.fileDownloadUrlError.push(\"Output file is not generated yet!\")\n            //   }\n            // }\n            // this.isAccordian = true\n          }\n          this.validateJson(this.resMessage);\n          this.status = ExecutionStatus.completed;\n          this.stopFakeProgress();\n          this.selectedFiles = [];\n        },\n        error: error => {\n          this.isExecutionComplete = true;\n          this.isProcessingChat = false;\n          this.errorMsg = true;\n          this.resMessage = error?.error?.detail;\n          this.workflowService.workflowLogDisconnect();\n          this.workflowLogs.push({\n            content: this.constants['labels'].workflowLogFailed,\n            color: 'red'\n          });\n          this.chatInterfaceComp.addAiResponse('Something went wrong, Workflow execution has failed.');\n          this.selectedFiles = [];\n          this.stopFakeProgress();\n          console.log('error is', error.message);\n        }\n      });\n    }\n    // public asyncExecutePipeline() {\n    //   const payload: FormData = new FormData();\n    //   if (this.selectedFiles?.length) {\n    //     for (const element of this.selectedFiles) {\n    //       payload.append('files', element)\n    //     }\n    //   }\n    //   payload.append('pipeLineId', String(this.workflowId));\n    //   payload.append('userInputs', JSON.stringify(this.workflowForm.value));\n    //   payload.append('user', this.tokenStorage.getDaUsername() || '');\n    //   payload.append('executionId', this.executionId);\n    //   this.workflowService.asyncExecutePipeline(payload).pipe().subscribe({\n    //     next: (res: any) => {\n    //       if(res) {\n    //         // res handling\n    //         console.log(res);\n    //       }\n    //     },\n    //     error: e => {\n    //       // error handling\n    //       console.log(e);\n    //     }\n    //   })\n    // }\n    handleAttachment() {\n      console.log('handleAttachment');\n    }\n    onAttachmentsSelected(files) {\n      if (this.currentInputIndex === this.inputFieldOrder.length || this.inputFieldOrder.length === 0) {\n        this.selectedFiles = files;\n        return;\n      }\n      const field = this.inputFieldOrder[this.currentInputIndex];\n      if (this.isImageInput(field)) {\n        if (files && files.length > 0) {\n          this.onImageSelected(files[0]);\n        }\n      } else {\n        this.selectedFiles = files;\n      }\n    }\n    startInputCollection() {\n      this.inputFieldOrder = Object.keys(this.workflowForm.controls);\n      this.currentInputIndex = 0;\n      if (this.inputFieldOrder.length > 0) {\n        this.promptForCurrentField();\n      } else {\n        this.disableChat = true;\n        this.chatInterfaceComp.addAiResponse('No input is required for this workflow. Click on send button to execute the workflow.');\n      }\n    }\n    promptForCurrentField() {\n      const field = this.inputFieldOrder[this.currentInputIndex];\n      if (this.isImageInput(field)) {\n        this.fileType = '.jpeg,.png,.jpg,.svg';\n        this.chatInterfaceComp.addAiResponse(`Please upload an image for ${field}`);\n        // UI should now show a file input for the user\n      } else {\n        this.fileType = '.zip'; // or whatever default you want for non-image\n        this.chatInterfaceComp.addAiResponse(`Please enter the value of ${field}`);\n      }\n    }\n    onImageSelected(file) {\n      const field = this.inputFieldOrder[this.currentInputIndex];\n      if (!this.isImageInput(field)) return;\n      const reader = new FileReader();\n      reader.onload = () => {\n        const base64String = reader.result;\n        this.workflowForm.get(field)?.setValue(base64String);\n        this.currentInputIndex++;\n        if (this.currentInputIndex < this.inputFieldOrder.length) {\n          this.promptForCurrentField();\n        } else {\n          this.chatInterfaceComp.addAiResponse('Thank you! Running the workflow...');\n          this.executeWorkflow();\n        }\n      };\n      reader.readAsDataURL(file);\n    }\n    // Panel management methods\n    toggleLeftPanel() {\n      this.isLeftPanelCollapsed = !this.isLeftPanelCollapsed;\n    }\n    toggleAgentExpansion(index) {\n      this.expandedAgents[index] = !this.expandedAgents[index];\n    }\n    initializeAgentStates() {\n      this.expandedAgents = new Array(this.workflowAgents.length).fill(false);\n      this.agentStatuses = this.workflowAgents.map(() => ({\n        completed: false\n      }));\n    }\n    // Extract input fields method (similar to pipeline component)\n    extractInputField(workflowAgents) {\n      const PLACEHOLDER_PATTERNS = /(%\\d+\\$s)|\\{\\{([a-zA-Z0-9-_]+)\\}\\}/g;\n      const placeholderMap = {};\n      workflowAgents.forEach(agent => {\n        const agentName = agent?.name || `Agent ${workflowAgents.indexOf(agent) + 1}`;\n        const agentDescription = agent?.description || agent?.task?.description || '';\n        const matches = agentDescription.matchAll(PLACEHOLDER_PATTERNS) || [];\n        for (const match of matches) {\n          const placeholder = match[1] || match[2];\n          const placeholderInput = match[0];\n          if (!placeholderMap[placeholder]) {\n            placeholderMap[placeholder] = {\n              agents: new Set(),\n              inputs: new Set()\n            };\n          }\n          placeholderMap[placeholder].agents.add(agentName);\n          placeholderMap[placeholder].inputs.add(placeholderInput);\n        }\n      });\n      return Object.entries(placeholderMap).map(([placeholder, {\n        agents,\n        inputs\n      }]) => ({\n        name: [...agents].length > 2 ? `${[...agents].slice(0, -1).join(\", \")} and ${[...agents].at(-1)}` : [...agents].join(\" and \"),\n        placeholder,\n        input: [...inputs][0]\n      }));\n    }\n    // Input validation\n    isInputValid() {\n      return this.workflowForm.valid && this.workflowId;\n    }\n    static ɵfac = function WorkflowExecutionComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || WorkflowExecutionComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.WorkflowService), i0.ɵɵdirectiveInject(i3.TokenStorageService), i0.ɵɵdirectiveInject(i3.LoaderService), i0.ɵɵdirectiveInject(i4.FormBuilder));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: WorkflowExecutionComponent,\n      selectors: [[\"app-workflow-execution\"]],\n      viewQuery: function WorkflowExecutionComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(ChatInterfaceComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.chatInterfaceComp = _t.first);\n        }\n      },\n      decls: 31,\n      vars: 16,\n      consts: [[1, \"workflow-execution-container\"], [\"width\", \"0\", \"height\", \"0\", 2, \"position\", \"absolute\"], [\"id\", \"gradient\", \"x1\", \"0%\", \"y1\", \"0%\", \"x2\", \"100%\", \"y2\", \"0%\"], [\"offset\", \"0%\", \"stop-color\", \"#6566CD\"], [\"offset\", \"100%\", \"stop-color\", \"#F96CAB\"], [\"role\", \"main\", 1, \"execution-content\"], [\"role\", \"region\", \"aria-label\", \"Workflow Execution Panel\", 1, \"column\", \"execution-panel-column\"], [1, \"column-content\", \"execution-panel-content\"], [1, \"column-header\"], [1, \"header-left\"], [\"iconName\", \"arrowLeft\", \"iconColor\", \"#1A46A7\", 1, \"back-icon\", 3, \"click\"], [\"iconName\", \"panelLeft\", \"iconColor\", \"#1A46A7\", 1, \"panel-icon\", 3, \"click\"], [1, \"panel-content\"], [1, \"agents-section\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"input-section\", 4, \"ngIf\"], [1, \"info-message\"], [1, \"execute-section\"], [\"label\", \"\\u25B6 Execute Agent\", \"variant\", \"primary\", \"size\", \"large\", 3, \"click\", \"disabled\", \"customStyles\"], [\"role\", \"region\", \"aria-label\", \"Agent Output\", 1, \"column\", \"output-column\"], [1, \"column-content\"], [1, \"column-header\", \"row\"], [1, \"col-7\"], [\"variant\", \"button\", \"buttonShape\", \"pill\", \"ariaLabel\", \"Pill navigation tabs\", 3, \"tabChange\", \"tabs\", \"activeTabId\", \"showContentPanels\"], [1, \"col-5\", \"right-section-header\"], [\"label\", \"Send for Approval\", \"variant\", \"primary\", \"size\", \"medium\", 3, \"customStyles\"], [\"style\", \"height: 100%\", 4, \"ngIf\"], [1, \"agent-card\"], [1, \"agent-header\"], [1, \"status-icon\"], [3, \"iconName\", \"iconColor\"], [1, \"agent-info\"], [1, \"agent-name\"], [\"iconName\", \"chevronDown\", \"iconColor\", \"#6B7280\", 1, \"expand-icon\", 3, \"click\"], [1, \"agent-details\"], [\"class\", \"agent-detail-item\", 4, \"ngIf\"], [\"class\", \"connection-line\", 4, \"ngIf\"], [1, \"agent-detail-item\"], [1, \"detail-label\"], [1, \"detail-value\"], [1, \"connection-line\"], [1, \"input-section\"], [1, \"input-header\"], [\"iconName\", \"check\", \"iconColor\", \"#22C55E\"], [1, \"input-title\"], [1, \"input-container\", 3, \"formGroup\"], [1, \"input-field\"], [1, \"input-textarea\", 3, \"formControlName\", \"placeholder\"], [1, \"input-actions\"], [\"iconName\", \"paperclip\", \"iconColor\", \"#6B7280\", 1, \"attach-icon\"], [\"iconName\", \"edit\", \"iconColor\", \"#6B7280\", 1, \"edit-icon\"], [\"iconName\", \"send\", \"iconColor\", \"#1A46A7\", 1, \"send-icon\"], [2, \"height\", \"100%\"], [3, \"saveLogs\", \"controlAction\", \"onOutPutBtnClick\", \"activityLogs\", \"executionDetails\", \"progress\", \"isRunning\", \"status\"], [3, \"export\", \"outputs\"]],\n      template: function WorkflowExecutionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(1, \"svg\", 1)(2, \"defs\")(3, \"linearGradient\", 2);\n          i0.ɵɵelement(4, \"stop\", 3)(5, \"stop\", 4);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6)(8, \"div\", 7)(9, \"div\", 8)(10, \"div\", 9)(11, \"ava-icon\", 10);\n          i0.ɵɵlistener(\"click\", function WorkflowExecutionComponent_Template_ava_icon_click_11_listener() {\n            return ctx.navigateBack();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"ava-icon\", 11);\n          i0.ɵɵlistener(\"click\", function WorkflowExecutionComponent_Template_ava_icon_click_12_listener() {\n            return ctx.toggleLeftPanel();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 12)(14, \"div\", 13);\n          i0.ɵɵtemplate(15, WorkflowExecutionComponent_ng_container_15_Template, 13, 12, \"ng-container\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(16, WorkflowExecutionComponent_div_16_Template, 8, 2, \"div\", 15);\n          i0.ɵɵelementStart(17, \"div\", 16)(18, \"p\");\n          i0.ɵɵtext(19, \"Agent input needed in order to execute Workflow\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 17)(21, \"ava-button\", 18);\n          i0.ɵɵlistener(\"click\", function WorkflowExecutionComponent_Template_ava_button_click_21_listener() {\n            return ctx.executeWorkflow();\n          });\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(22, \"div\", 19)(23, \"div\", 20)(24, \"div\", 21)(25, \"div\", 22)(26, \"ava-tabs\", 23);\n          i0.ɵɵlistener(\"tabChange\", function WorkflowExecutionComponent_Template_ava_tabs_tabChange_26_listener($event) {\n            return ctx.onTabChange($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"div\", 24);\n          i0.ɵɵelement(28, \"ava-button\", 25);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(29, WorkflowExecutionComponent_div_29_Template, 2, 5, \"div\", 26)(30, WorkflowExecutionComponent_div_30_Template, 2, 1, \"div\", 26);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵclassProp(\"collapsed\", ctx.isLeftPanelCollapsed);\n          i0.ɵɵadvance(6);\n          i0.ɵɵclassProp(\"hidden\", ctx.isLeftPanelCollapsed);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.workflowAgents);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.userInputList.length > 0);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"disabled\", !ctx.isInputValid())(\"customStyles\", i0.ɵɵpureFunction0(14, _c0));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"tabs\", ctx.navigationTabs)(\"activeTabId\", ctx.activeTabId)(\"showContentPanels\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"customStyles\", i0.ɵɵpureFunction0(15, _c1));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTab === \"Agent Activity\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTab === \"Agent Output\");\n        }\n      },\n      dependencies: [CommonModule, i5.NgForOf, i5.NgIf, FormsModule, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, AgentActivityComponent, AgentOutputComponent, TabsComponent, ButtonComponent, IconComponent],\n      styles: [\".ava-tabs {\\n  background: none !important;\\n}\\n\\n  .ava-tabs__container {\\n  border-radius: none !important;\\n  border: none !important;\\n  padding: 0 !important;\\n  box-shadow: none !important;\\n  background: none !important;\\n}\\n\\n  .ava-tabs__list {\\n  padding: 0 !important;\\n}\\n\\n.workflow-execution-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 93%;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 20px 24px;\\n  border-bottom: 1px solid var(--border-color, #e0e0e0);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-title[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 24px;\\n  font-weight: 600;\\n  color: var(--text-color, #333);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-title[_ngcontent-%COMP%]   .header-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%], \\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 8px 16px;\\n  border-radius: 5px;\\n  font-weight: 500;\\n  font-size: 14px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%], \\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  margin-right: 6px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%] {\\n  background-color: var(--bg-muted, #f5f5f5);\\n  border: 1px solid var(--border-color, #e0e0e0);\\n  color: var(--text-color, #333);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]:hover {\\n  background-color: var(--bg-muted-hover, #e9e9e9);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%] {\\n  background-color: var(--card-bg, #fff);\\n  border: 1px solid var(--border-color, #e0e0e0);\\n  color: var(--text-color, #333);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]:hover {\\n  background-color: var(--card-bg-hover, #f9f9f9);\\n  border-color: var(--border-color-dark, #d0d0d0);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-grow: 1;\\n  height: 95vh;\\n  overflow: hidden;\\n  gap: 20px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 8px 16px;\\n  background-color: #e9effd;\\n  color: #fff;\\n  height: 64px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: #1a46a7;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  padding: 8px 16px;\\n  background-color: var(--card-bg, white);\\n  position: relative;\\n  border: 1px solid transparent;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: transparent;\\n  background-image: linear-gradient(90deg, #6566cd 0%, #f96cab 100%);\\n  -webkit-background-clip: text;\\n  background-clip: text;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  border-radius: 8px;\\n  padding: 1px;\\n  background: linear-gradient(90deg, #6566cd 0%, #f96cab 100%);\\n  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);\\n  mask-composite: exclude;\\n  pointer-events: none;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%]:hover {\\n  background-color: var(--bg-muted-hover, #f9f5ff);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 18px;\\n  height: 18px;\\n  fill: none;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]   path[_ngcontent-%COMP%] {\\n  stroke-width: 2;\\n  stroke: url(#gradient);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 0;\\n  display: flex;\\n  flex-direction: column;\\n  scrollbar-width: none;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 8px;\\n  height: 8px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: var(--scrollbar-track, #f1f1f1);\\n  border-radius: 4px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: var(--scrollbar-thumb, #d1d1d1);\\n  border-radius: 4px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: var(--scrollbar-thumb-hover, #b1b1b1);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%]   .nav-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  gap: 16px;\\n  background-color: #e9effd;\\n  padding: 0 15px 0 0;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%]   .nav-wrapper[_ngcontent-%COMP%]   .right-inner-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%]   .nav-wrapper[_ngcontent-%COMP%]   .right-inner-wrapper[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #fff;\\n  padding: 0 10px;\\n  margin: 0px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .activity-column[_ngcontent-%COMP%] {\\n  background: transparent;\\n  box-shadow: none;\\n  border-radius: 8px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .activity-column[_ngcontent-%COMP%]   app-agent-activity[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%] {\\n  flex: 0 0 400px;\\n  background: #F0F4FF;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 15px var(--card-shadow, rgba(0, 0, 0, 0.05));\\n  transition: all 0.3s ease;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column.collapsed[_ngcontent-%COMP%] {\\n  flex: 0 0 60px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column.collapsed[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%] {\\n  background: #F0F4FF;\\n  border-bottom: 1px solid #E5E7EB;\\n  padding: 16px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  align-items: center;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .back-icon[_ngcontent-%COMP%], \\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .panel-icon[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transition: transform 0.2s ease;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .back-icon[_ngcontent-%COMP%]:hover, \\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .panel-icon[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%]   .execution-panel-content[_ngcontent-%COMP%] {\\n  padding: 0;\\n  height: 100%;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%]   .execution-panel-content[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  height: calc(100% - 80px);\\n  overflow-y: auto;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%]   .execution-panel-content[_ngcontent-%COMP%]   .panel-content.hidden[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .playground-column[_ngcontent-%COMP%] {\\n  flex: 3.5;\\n  background: var(--card-bg);\\n  border-radius: 8px;\\n  box-shadow: 0 4px 15px var(--card-shadow, rgba(0, 0, 0, 0.05));\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .playground-column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .header-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  gap: 12px;\\n  align-items: center;\\n  color: #1a46a7;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .playground-column[_ngcontent-%COMP%]   .playground-content[_ngcontent-%COMP%] {\\n  padding: 0;\\n  height: 100%;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .playground-column[_ngcontent-%COMP%]   .playground-content[_ngcontent-%COMP%]   .chat-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  min-height: 400px;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  position: relative;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .playground-column[_ngcontent-%COMP%]   .playground-content[_ngcontent-%COMP%]   .chat-container[_ngcontent-%COMP%]     .prompt-container {\\n  margin-top: 30px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .playground-column[_ngcontent-%COMP%]   .playground-content[_ngcontent-%COMP%]   .chat-container[_ngcontent-%COMP%]   .playground-title[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: var(--dashboard-primary, #6566cd);\\n  margin: 16px 16px;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .playground-column[_ngcontent-%COMP%]   .playground-content[_ngcontent-%COMP%]   .chat-container[_ngcontent-%COMP%]   app-chat-interface[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  flex: 1;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .output-column[_ngcontent-%COMP%] {\\n  flex: 5.5;\\n  background-color: var(--card-bg, white);\\n  border-radius: 8px;\\n  box-shadow: 0 4px 15px var(--card-shadow, rgba(0, 0, 0, 0.05));\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .output-column[_ngcontent-%COMP%]   app-agent-output[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n  nav.ava-tabs__list {\\n  background: #e9effd;\\n  padding: 4px;\\n}\\n\\n  button.ava-button.primary.active {\\n  background: #616161;\\n  color: #fff;\\n}\\n\\n  .column-header .ava-tabs[data-variant=button] .ava-tabs__tab--pill {\\n  border-radius: 8px !important;\\n  padding: 12px 16px !important;\\n  font-family: \\\"Mulish\\\";\\n}\\n\\n  .ava-tabs[data-variant=button] .ava-tabs__tab--active .ava-tabs__tab-text {\\n  color: white;\\n}\\n\\n  .ava-tabs__tab-text {\\n  color: #4c515b;\\n  font-family: \\\"Mulish\\\";\\n  font-weight: 600;\\n}\\n\\n  .right-section-header .ava-button.secondary {\\n  color: #1a46a7;\\n  border: none;\\n}\\n\\n  .right-section-header .ava-button.secondary:hover {\\n  color: #1a46a7;\\n  border: none;\\n}\\n\\n.right-section-header[_ngcontent-%COMP%] {\\n  text-align: end;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return WorkflowExecutionComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "Subject", "takeUntil", "FormsModule", "Validators", "ChatInterfaceComponent", "AgentActivityComponent", "AgentOutputComponent", "ButtonComponent", "IconComponent", "TabsComponent", "environment", "workflowConstants", "ExecutionStatus", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "agent_r4", "model", "tools", "join", "ɵɵelement", "ɵɵelementContainerStart", "ɵɵlistener", "WorkflowExecutionComponent_ng_container_15_Template_ava_icon_click_8_listener", "i_r2", "ɵɵrestoreView", "_r1", "index", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "toggleAgentExpansion", "ɵɵtemplate", "WorkflowExecutionComponent_ng_container_15_div_10_Template", "WorkflowExecutionComponent_ng_container_15_div_11_Template", "WorkflowExecutionComponent_ng_container_15_div_12_Template", "ɵɵclassProp", "agentStatuses", "completed", "ɵɵproperty", "name", "expandedAgents", "length", "workflowAgents", "input_r5", "input", "WorkflowExecutionComponent_div_16_ng_container_7_Template", "workflowForm", "userInputList", "WorkflowExecutionComponent_div_29_Template_app_agent_activity_saveLogs_1_listener", "_r6", "saveLogs", "WorkflowExecutionComponent_div_29_Template_app_agent_activity_controlAction_1_listener", "$event", "handleControlAction", "WorkflowExecutionComponent_div_29_Template_app_agent_activity_onOutPutBtnClick_1_listener", "onTabChange", "id", "label", "workflowLogs", "executionDetails", "progress", "isRunning", "status", "WorkflowExecutionComponent_div_30_Template_app_agent_output_export_1_listener", "_r7", "exportResults", "taskMessage", "WorkflowExecutionComponent", "route", "router", "workflowService", "tokenStorage", "loaderService", "formBuilder", "navigationTabs", "disabled", "workflowId", "workflowName", "constants", "chatInterfaceComp", "activityLogs", "activityProgress", "notStarted", "chatMessages", "isProcessingChat", "inputText", "agentOutputs", "fileType", "executionStartTime", "executionCompleted", "executionId", "enableStreamingLog", "enableLogStreaming", "isExecutionComplete", "progressInterval", "destroy$", "selectedTab", "demoTabs", "errorMsg", "resMessage", "isJsonValid", "disable<PERSON>hat", "selectedFiles", "isLoading", "loaderColor", "inputFieldOrder", "currentInputIndex", "activeTabId", "isLeftPanelCollapsed", "constructor", "ngOnInit", "disable<PERSON><PERSON><PERSON>", "crypto", "randomUUID", "paramMap", "pipe", "subscribe", "params", "get", "loadWorkflow", "navigate", "ngOnDestroy", "next", "complete", "<PERSON><PERSON><PERSON><PERSON>", "event", "console", "log", "from", "text", "group", "getWorkflowById", "res", "extractInputField", "initializeForm", "initializeAgentStates", "startInputCollection", "error", "err", "addAiResponse", "pipeLineAgents", "PLACEHOLDER_PATTERNS", "placeholderM<PERSON>", "for<PERSON>ach", "agent", "<PERSON><PERSON><PERSON>", "agentDetails", "agentDescription", "description", "matches", "matchAll", "match", "placeholder", "placeholderInput", "agents", "Set", "inputs", "add", "Object", "entries", "map", "slice", "at", "isImageInput", "variableName", "trim", "startsWith", "addControl", "control", "required", "isInputValid", "valid", "startFakeProgress", "setInterval", "stopFakeProgress", "clearInterval", "setTimeout", "handleChatMessage", "message", "executeWorkflow", "field", "setValue", "promptForCurrentField", "section", "data", "timestamp", "downloadAsFile", "JSON", "stringify", "filename", "type", "blob", "Blob", "url", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "click", "revokeObjectURL", "action", "navigateBack", "editWorkflow", "logExecutionStatus", "delay", "workflowExecProcessing", "push", "content", "color", "getWorkflowLogs", "workflowLogConnect", "validate<PERSON><PERSON>", "output", "parsedOutput", "parse", "e", "payload", "FormData", "queryString", "running", "file", "append", "value", "getDaUsername", "pipeLineId", "userInputs", "user", "workflowResponse", "pipeline", "workflowExecComplete", "tasksOutputs", "task", "title", "expected_output", "summary", "raw", "detail", "workflowLogDisconnect", "workflowLogFailed", "handleAttachment", "onAttachmentsSelected", "files", "onImageSelected", "keys", "controls", "reader", "FileReader", "onload", "base64String", "result", "readAsDataURL", "toggleLeftPanel", "Array", "fill", "indexOf", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "WorkflowService", "i3", "TokenStorageService", "LoaderService", "i4", "FormBuilder", "selectors", "viewQuery", "WorkflowExecutionComponent_Query", "rf", "ctx", "WorkflowExecutionComponent_Template_ava_icon_click_11_listener", "WorkflowExecutionComponent_Template_ava_icon_click_12_listener", "WorkflowExecutionComponent_ng_container_15_Template", "WorkflowExecutionComponent_div_16_Template", "WorkflowExecutionComponent_Template_ava_button_click_21_listener", "WorkflowExecutionComponent_Template_ava_tabs_tabChange_26_listener", "WorkflowExecutionComponent_div_29_Template", "WorkflowExecutionComponent_div_30_Template", "ɵɵpureFunction0", "_c0", "_c1", "i5", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\workflows\\workflow-execution\\workflow-execution.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\pages\\workflows\\workflow-execution\\workflow-execution.component.html"], "sourcesContent": ["import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { FormBuilder, FormGroup, FormsModule, Validators } from '@angular/forms';\r\n\r\n// Import child components\r\nimport { ChatInterfaceComponent } from '@shared/components/chat-interface/chat-interface.component';\r\nimport { ChatMessage } from '@shared/components/chat-window/chat-window.component';\r\nimport {\r\n  AgentActivityComponent,\r\n} from './components/agent-activity/agent-activity.component';\r\nimport {\r\n  AgentOutputComponent,\r\n  AgentOutput as OutputItem,\r\n} from './components/agent-output/agent-output.component';\r\nimport {\r\n  ButtonComponent,\r\n  IconComponent,\r\n  TabItem,\r\n  TabsComponent,\r\n} from '@ava/play-comp-library';\r\nimport { WorkflowService } from '@shared/services/workflow.service';\r\nimport { environment } from '@shared/environments/environment';\r\nimport workflowConstants from './../constants/workflows.json';\r\nimport { TokenStorageService, LoaderService } from '@shared/index';\r\nimport { AvaTab } from '@shared/models/tab.model';\r\nimport { ExecutionStatus, ActivityLog } from '@shared/models/execution.model';\r\nimport { AgentActivityExecutionDetails } from './components/agent-activity/agent-activity.component';\r\n\r\n\r\n@Component({\r\n  selector: 'app-workflow-execution',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    ChatInterfaceComponent,\r\n    AgentActivityComponent,\r\n    AgentOutputComponent,\r\n    TabsComponent,\r\n    ButtonComponent,\r\n    IconComponent,\r\n  ],\r\n  templateUrl: './workflow-execution.component.html',\r\n  styleUrls: ['./workflow-execution.component.scss'],\r\n})\r\nexport class WorkflowExecutionComponent implements OnInit, OnDestroy {\r\n  navigationTabs: TabItem[] = [\r\n    { id: 'nav-home', label: 'Agent Activity' },\r\n    { id: 'nav-products', label: 'Agent Output' },\r\n    { id: 'nav-services', label: 'Preview', disabled: true },\r\n  ];\r\n  // Workflow details\r\n  workflowId: string | null = null;\r\n  workflowName: string = 'Workflow';\r\n\r\n  constants = workflowConstants as Record<string, any>;\r\n\r\n  @ViewChild(ChatInterfaceComponent, { static: false })\r\n  chatInterfaceComp!: ChatInterfaceComponent;\r\n\r\n  // Activity logs\r\n  activityLogs: ActivityLog[] = [];\r\n  activityProgress: number = 0;\r\n  executionDetails?: AgentActivityExecutionDetails;\r\n  isRunning: boolean = false;\r\n  status: ExecutionStatus = ExecutionStatus.notStarted;\r\n\r\n  // Chat messages\r\n  chatMessages: ChatMessage[] = [];\r\n  isProcessingChat: boolean = false;\r\n  inputText = '';\r\n\r\n  // Agent outputs\r\n  agentOutputs: OutputItem[] = [];\r\n  public workflowForm!: FormGroup;\r\n  public fileType : string = '.zip';\r\n\r\n  // Execution state\r\n  executionStartTime: Date | null = null;\r\n  executionCompleted: boolean = false;\r\n  executionId!: string;\r\n\r\n  public workflowLogs: any[] = [];\r\n  enableStreamingLog = environment.enableLogStreaming || 'all';\r\n\r\n  public isExecutionComplete: boolean = false;\r\n  progressInterval: any;\r\n\r\n  // Component lifecycle\r\n  private destroy$ = new Subject<void>();\r\n  selectedTab: string = 'Agent Activity';\r\n  demoTabs: AvaTab[] = [\r\n    { id: 'activity', label: 'Agent Activity' },\r\n    { id: 'agents', label: 'Agent Output' },\r\n    { id: 'preview', label: 'Preview', disabled: true },\r\n  ];\r\n  errorMsg = false;\r\n  resMessage: any;\r\n  taskMessage = [];\r\n  isJsonValid = false;\r\n  disableChat : boolean = false;\r\n  selectedFiles: File[] = [];\r\n  workflowAgents: any[] = [];\r\n  userInputList: any[] = [];\r\n  progress = 0;\r\n  isLoading = false;\r\n  loaderColor: string = '';\r\n\r\n  inputFieldOrder: string[] = [];\r\n  currentInputIndex: number = 0;\r\n  activeTabId: string = 'nav-home';\r\n\r\n  // Panel state\r\n  isLeftPanelCollapsed = false;\r\n  expandedAgents: boolean[] = [];\r\n  agentStatuses: { completed: boolean }[] = [];\r\n  \r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private workflowService: WorkflowService,\r\n    private tokenStorage: TokenStorageService,\r\n    private loaderService: LoaderService,\r\n    private formBuilder: FormBuilder,\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loaderService.disableLoader();\r\n    this.selectedTab = 'Agent Activity';\r\n    this.executionId = crypto.randomUUID();\r\n    // Get workflow ID from route params\r\n    this.route.paramMap.pipe(takeUntil(this.destroy$)).subscribe((params) => {\r\n      this.workflowId = params.get('id');\r\n      if (this.workflowId) {\r\n        this.loadWorkflow(this.workflowId);\r\n      } else {\r\n        // No workflow ID, redirect back to workflows page\r\n        this.router.navigate(['/build/workflows']);\r\n      }\r\n    });\r\n    // this.executeWorkflow()\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n    this.loaderService.enableLoader();\r\n  }\r\n  onTabChange(event: { id: string, label: string }) {\r\n    this.selectedTab = event.label;\r\n    this.activeTabId = event.id;\r\n    console.log('Tab changed:', event);\r\n  }\r\n\r\n  // Load workflow data\r\n  loadWorkflow(id: string): void {\r\n    // In a real app, this would fetch the workflow from a service\r\n    console.log(`Loading workflow with ID: ${id}`);\r\n    this.chatMessages = [\r\n      {\r\n        from: 'ai',\r\n        text: 'I am your workflow assistant. I will help you in executing this workflow.',\r\n      } as ChatMessage,\r\n    ]\r\n    this.workflowForm = this.formBuilder.group({});\r\n    \r\n    this.workflowService.getWorkflowById(id).subscribe({\r\n        next: (res) => {\r\n        this.workflowAgents = res.workflowAgents;\r\n        this.userInputList = this.extractInputField(this.workflowAgents);\r\n        if(this.userInputList.length === 0){\r\n          this.disableChat = true;\r\n        }\r\n        this.workflowName = res.name;\r\n        this.initializeForm();\r\n        this.initializeAgentStates();\r\n        this.startInputCollection();\r\n      },\r\n        error: (err) => {\r\n          this.disableChat = true;\r\n          this.chatInterfaceComp.addAiResponse('No input is required for this workflow. Click on send button to execute the workflow.');\r\n          console.log(err);\r\n        }\r\n    });\r\n\r\n  }\r\n\r\n  public extractInputField(pipeLineAgents: any) {\r\n    const PLACEHOLDER_PATTERNS =  /(%\\d+\\$s)|\\{\\{([a-zA-Z0-9-_]+)\\}\\}/g;\r\n    const placeholderMap: { [key: string]: { agents: Set<string>; inputs: Set<string> } } = {};\r\n\r\n    pipeLineAgents.forEach((agent: any) => {\r\n      const agentName = agent?.agentDetails?.name;\r\n      const agentDescription = agent?.agentDetails?.description;\r\n      const matches = agentDescription.matchAll(PLACEHOLDER_PATTERNS) || [];\r\n\r\n      for (const match of matches) { \r\n        const placeholder = match[1] || match[2];\r\n        const placeholderInput = match[0];\r\n        if (!placeholderMap[placeholder]) {\r\n          placeholderMap[placeholder] = { agents: new Set(), inputs: new Set() };;\r\n        }\r\n        placeholderMap[placeholder].agents.add(agentName);\r\n        placeholderMap[placeholder].inputs.add(placeholderInput);\r\n      }\r\n    })\r\n\r\n    return Object.entries(placeholderMap).map(([placeholder, { agents, inputs }]) => ({\r\n      name: [...agents].length > 2\r\n        ? `${[...agents].slice(0, -1).join(\", \")} and ${[...agents].at(-1)}`\r\n        : [...agents].join(\" and \"),\r\n      placeholder,\r\n      input: [...inputs][0],\r\n    }));\r\n  }\r\n\r\n  public isImageInput(input: string): boolean {\r\n    const match = input.match(/{{(.*?)}}/);\r\n    if (match && match[1]) {\r\n      const variableName = match[1].trim();\r\n      return variableName.startsWith('image') || variableName.startsWith('Image');\r\n    }\r\n    return false;\r\n  }\r\n\r\n  public initializeForm() {   \r\n    this.userInputList.forEach((label: any) => {\r\n      this.workflowForm.addControl(label.input, this.formBuilder.control('', Validators.required));\r\n    })\r\n  }\r\n\r\n  public isInputValid() {\r\n    return this.workflowForm.valid && this.workflowId;\r\n  }\r\n\r\n  startFakeProgress() {\r\n    this.progress = 0;\r\n    this.progressInterval = setInterval(() => {\r\n      if (this.progress < 90) {\r\n        this.progress += 5; // Increase slowly\r\n      }\r\n    }, 200); // Adjust speed\r\n  }\r\n\r\n  stopFakeProgress() {\r\n    clearInterval(this.progressInterval);\r\n    this.progress = 100;\r\n\r\n    setTimeout(() => {\r\n      this.isLoading = false;\r\n    }, 500); // Small delay to let user see 100%\r\n  }\r\n\r\n  // Handle new chat message from user\r\n  handleChatMessage(message: string): void {\r\n    // console.log('message ', message, 'is blank', message.trim() === '');\r\n    this.isProcessingChat = true;\r\n    if(message.trim() === ''){\r\n      if(this.inputFieldOrder.length === 0){\r\n        this.chatInterfaceComp.addAiResponse('Executing the workflow...');\r\n        this.executeWorkflow();\r\n      }\r\n      return;\r\n    }\r\n\r\n    if(this.isExecutionComplete || this.currentInputIndex===this.inputFieldOrder.length){\r\n        this.chatInterfaceComp.addAiResponse('Executing the workflow...');\r\n        this.executeWorkflow();\r\n      return;\r\n    }\r\n\r\n    const field = this.inputFieldOrder[this.currentInputIndex];\r\n    if (this.isImageInput(field)) {\r\n      // Ignore text input, wait for file input\r\n      this.chatInterfaceComp.addAiResponse(`Please upload an image file for ${field}`);\r\n      return;\r\n    }\r\n\r\n    this.workflowForm.get(field)?.setValue(message);\r\n    this.currentInputIndex++;\r\n\r\n    if (this.currentInputIndex < this.inputFieldOrder.length) {\r\n      this.promptForCurrentField();\r\n    } else {\r\n      this.chatInterfaceComp.addAiResponse('Thank you for the input! Executing the workflow...');\r\n      this.executeWorkflow();\r\n    }\r\n  }\r\n\r\n  // Save execution logs\r\n  saveLogs(): void {\r\n    console.log('Saving execution logs...');\r\n    // This would typically save to a service\r\n  }\r\n\r\n  // Export results\r\n  exportResults(section: 'activity' | 'output'): void {\r\n    console.log(`Exporting ${section} data...`);\r\n\r\n    if (section === 'activity') {\r\n      const data = this.activityLogs\r\n        .map((log) => `[${log.timestamp}] ${log.message}`)\r\n        .join('\\n');\r\n      this.downloadAsFile(data, 'workflow-activity-logs.txt', 'text/plain');\r\n    } else {\r\n      const data = JSON.stringify(this.agentOutputs, null, 2);\r\n      this.downloadAsFile(data, 'workflow-outputs.json', 'application/json');\r\n    }\r\n  }\r\n\r\n  // Helper method to download data as a file\r\n  private downloadAsFile(data: string, filename: string, type: string): void {\r\n    const blob = new Blob([data], { type });\r\n    const url = URL.createObjectURL(blob);\r\n    const link = document.createElement('a');\r\n    link.href = url;\r\n    link.download = filename;\r\n    link.click();\r\n    URL.revokeObjectURL(url);\r\n  }\r\n\r\n  // Handle controls for execution\r\n  handleControlAction(action: 'play' | 'pause' | 'stop'): void {\r\n    console.log(`Control action: ${action}`);\r\n    // In a real app, this would control the workflow execution\r\n\r\n    if (action === 'play') {\r\n      this.isRunning = true;\r\n    } else if (action === 'pause' || action === 'stop') {\r\n      this.isRunning = false;\r\n    }\r\n  }\r\n\r\n  // Navigate back to workflow listing\r\n  navigateBack(): void {\r\n    this.router.navigate(['/build/workflows']);\r\n  }\r\n\r\n  // Navigate to edit workflow\r\n  editWorkflow(): void {\r\n    if (this.workflowId) {\r\n      this.router.navigate(['/build/workflows/edit', this.workflowId]);\r\n    }\r\n  }\r\n\r\n  public logExecutionStatus(delay: number = 2000) {\r\n    setTimeout(() => {\r\n      if (!this.isExecutionComplete) {\r\n        console.log(this.constants);\r\n        console.log(this.constants['labels'].workflowExecProcessing);\r\n        this.workflowLogs.push({\r\n          content: this.constants['labels'].workflowExecProcessing,\r\n          color: '#F9DB24',\r\n        });\r\n      }\r\n    }, delay);\r\n  }\r\n\r\n  // public parseAnsiString(ansiString: string) {\r\n  //   const regex = ansiRegex();\r\n  //   const parts = ansiString.split(regex);\r\n  //   const matches = [...ansiString.matchAll(regex)];\r\n  //   parts.forEach((part, index) => {\r\n  //     if (part.trim() !== '') {\r\n  //       let colorCode = matches[index - 1][0];\r\n  //       if (index - 2 >= 0 && matches[index - 2]?.includes('\\u001b[1m')) {\r\n  //         colorCode = `\\u001b[1m${colorCode}`;\r\n  //       }\r\n  //       this.workflowLogs.push({\r\n  //         content: part,\r\n  //         color: this.colorMap[colorCode] || 'white',\r\n  //       });\r\n  //     }\r\n  //   });\r\n  // }\r\n\r\n  public getWorkflowLogs(executionId: string) {\r\n    this.workflowService\r\n      .workflowLogConnect(executionId)\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (message) => {\r\n          console.log('message: ', message);\r\n          const { content, color } = message;\r\n          if (color) {\r\n            this.workflowLogs.push({ content, color });\r\n          } else if (this.enableStreamingLog === 'all') {\r\n            // this.parseAnsiString(content);\r\n          }\r\n        },\r\n        error: (err) => {\r\n          this.workflowLogs.push({\r\n            content: this.constants['workflowLog'],\r\n            color: 'red',\r\n          });\r\n          console.error('WebSocket error:', err);\r\n        },\r\n        complete: () => {\r\n          this.logExecutionStatus();\r\n          console.log('WebSocket connection closed');\r\n        },\r\n      });\r\n  }\r\n\r\n  // public parseAnsiString(ansiString: string) {\r\n  //   const regex = ansiRegex();\r\n  //   const parts = ansiString.split(regex);\r\n  //   const matches = [...ansiString.matchAll(regex)];\r\n  //   parts.forEach((part, index) => {\r\n  //     if (part.trim() !== '') {\r\n  //       let colorCode = matches[index-1][0];\r\n  //       if(index - 2 >= 0 && matches[index-2]?.includes('\\u001b[1m')) {\r\n  //         colorCode = `\\u001b[1m${colorCode}`;\r\n  //       }\r\n  //       this.workflowLogs.push({\r\n  //         content: part, \r\n  //         color: this.colorMap[colorCode] || 'white', \r\n  //       });\r\n  //     }\r\n  //   });\r\n  // }\r\n\r\n  public validateJson(output: string): any | null {\r\n    this.isJsonValid = false;\r\n    try {\r\n      const parsedOutput = JSON.parse(output);\r\n      this.isJsonValid = true;\r\n      return parsedOutput;\r\n    } catch (e) {\r\n      return null;\r\n    }\r\n  }\r\n\r\n  public executeWorkflow() {\r\n    let payload: FormData | Record<string, any> = new FormData();\r\n    let queryString = '';\r\n\r\n\r\n    this.status = ExecutionStatus.running;\r\n    if (this.selectedFiles.length) {\r\n      this.selectedFiles.forEach((file) => {\r\n        payload.append('files', file);\r\n      });\r\n      payload.append('workflowId', this.workflowId);\r\n      payload.append('userInputs', JSON.stringify(this.workflowForm.value));\r\n      payload.append('user', this.tokenStorage.getDaUsername());\r\n      payload.append('executionId', this.executionId);\r\n      queryString = '/files';\r\n    } else {\r\n      payload = {\r\n        pipeLineId: this.workflowId,\r\n        userInputs: this.workflowForm.value,\r\n        executionId: this.executionId,\r\n        user: this.tokenStorage.getDaUsername(),\r\n      };\r\n    }\r\n\r\n    this.getWorkflowLogs(this.executionId);\r\n    this.startFakeProgress();\r\n\r\n    this.workflowService\r\n      .executeWorkflow(payload, queryString)\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (res) => {\r\n          this.isProcessingChat = false;\r\n          this.isRunning = false;\r\n          this.chatInterfaceComp.addAiResponse(res?.message || \"Workflow execution completed successfully!\");\r\n\r\n          if (res?.workflowResponse?.pipeline?.output) {\r\n            this.isExecutionComplete = true;\r\n            // console.log(this.constants['labels'].workflowExecComplete);\r\n            this.workflowLogs.push({\r\n              content: this.constants['labels'].workflowExecComplete,\r\n              color: '#0F8251',\r\n            });\r\n            this.errorMsg = false;\r\n            this.resMessage = res?.workflowResponse?.pipeline?.output;\r\n            this.agentOutputs = res?.workflowResponse?.pipeline?.tasksOutputs.map((task: any) => {\r\n              return {\r\n                id: task?.id || '',\r\n                title: task?.title || '',\r\n                content: task?.content || '',\r\n                agentName: task?.agentName || '',\r\n                timestamp: task?.timestamp || '',\r\n                type: task?.type || '',\r\n                description: task?.description || '',\r\n                expected_output: task?.expected_output || '',\r\n                summary: task?.summary || '',\r\n                raw: task?.raw || '',\r\n              };\r\n            })\r\n\r\n            this.taskMessage = res?.workflowResponse?.pipeline?.tasksOutputs.map(\r\n              (task: {\r\n                description: any;\r\n                summary: any;\r\n                raw: any;\r\n                expected_output: any;\r\n              }) => {\r\n                return {\r\n                  description: task.description,\r\n                  summary: task.summary,\r\n                  raw: task.raw,\r\n                  expected_output: task.expected_output,\r\n                };\r\n              },\r\n            );\r\n\r\n            // if(\"file_download_url\" in res?.pipeline){\r\n            //   this.isFileWriter = true;\r\n            //   this.fileDownloadLink = res?.pipeline?.file_download_url;\r\n\r\n            //   if(!this.fileDownloadLink){\r\n            //     this.fileDownloadUrlError = [];\r\n            //     this.fileDownloadUrlError.push(\"Output file is not generated yet!\")\r\n            //   }\r\n            // }\r\n            // this.isAccordian = true\r\n          }\r\n          this.validateJson(this.resMessage);\r\n          this.status = ExecutionStatus.completed;\r\n          this.stopFakeProgress();\r\n          this.selectedFiles = [];\r\n        },\r\n        error: (error) => {\r\n          this.isExecutionComplete = true;\r\n          this.isProcessingChat = false;\r\n          this.errorMsg = true;\r\n          this.resMessage = error?.error?.detail;\r\n          this.workflowService.workflowLogDisconnect();\r\n          this.workflowLogs.push({\r\n            content: this.constants['labels'].workflowLogFailed,\r\n            color: 'red',\r\n          });\r\n          this.chatInterfaceComp.addAiResponse(\r\n            'Something went wrong, Workflow execution has failed.',\r\n          );\r\n          this.selectedFiles = [];\r\n          this.stopFakeProgress();\r\n          console.log('error is', error.message);\r\n        },\r\n      });\r\n  }\r\n\r\n  // public asyncExecutePipeline() {\r\n  //   const payload: FormData = new FormData();\r\n  //   if (this.selectedFiles?.length) {\r\n  //     for (const element of this.selectedFiles) {\r\n  //       payload.append('files', element)\r\n  //     }\r\n  //   }\r\n  //   payload.append('pipeLineId', String(this.workflowId));\r\n  //   payload.append('userInputs', JSON.stringify(this.workflowForm.value));\r\n  //   payload.append('user', this.tokenStorage.getDaUsername() || '');\r\n  //   payload.append('executionId', this.executionId);\r\n\r\n  //   this.workflowService.asyncExecutePipeline(payload).pipe().subscribe({\r\n  //     next: (res: any) => {\r\n  //       if(res) {\r\n  //         // res handling\r\n  //         console.log(res);\r\n  //       }\r\n  //     },\r\n  //     error: e => {\r\n  //       // error handling\r\n  //       console.log(e);\r\n  //     }\r\n  //   })\r\n  // }\r\n\r\n  handleAttachment() {\r\n    console.log('handleAttachment');\r\n  }\r\n\r\n  onAttachmentsSelected(files: File[]) {\r\n    if(this.currentInputIndex===this.inputFieldOrder.length || this.inputFieldOrder.length===0){\r\n      this.selectedFiles = files;\r\n      return;\r\n    }\r\n\r\n    const field = this.inputFieldOrder[this.currentInputIndex];\r\n    if(this.isImageInput(field)){\r\n      if (files && files.length > 0) {\r\n        this.onImageSelected(files[0]);\r\n      }\r\n    } else {\r\n      this.selectedFiles = files;\r\n    }\r\n  }\r\n\r\n  startInputCollection(){\r\n    this.inputFieldOrder = Object.keys(this.workflowForm.controls);\r\n    this.currentInputIndex = 0;\r\n    if (this.inputFieldOrder.length > 0) {\r\n      this.promptForCurrentField();\r\n    }\r\n    else{\r\n      this.disableChat = true;\r\n      this.chatInterfaceComp.addAiResponse('No input is required for this workflow. Click on send button to execute the workflow.');\r\n    }\r\n  }\r\n\r\n  promptForCurrentField() {\r\n    const field = this.inputFieldOrder[this.currentInputIndex];\r\n    if (this.isImageInput(field)) {\r\n      this.fileType = '.jpeg,.png,.jpg,.svg';\r\n      this.chatInterfaceComp.addAiResponse(`Please upload an image for ${field}`);\r\n      // UI should now show a file input for the user\r\n    } else {\r\n      this.fileType = '.zip'; // or whatever default you want for non-image\r\n      this.chatInterfaceComp.addAiResponse(`Please enter the value of ${field}`);\r\n    }\r\n  }\r\n\r\n  onImageSelected(file: File) {\r\n    const field = this.inputFieldOrder[this.currentInputIndex];\r\n    if (!this.isImageInput(field)) return;\r\n\r\n    const reader = new FileReader();\r\n    reader.onload = () => {\r\n      const base64String = (reader.result as string); \r\n      this.workflowForm.get(field)?.setValue(base64String);\r\n      this.currentInputIndex++;\r\n      if (this.currentInputIndex < this.inputFieldOrder.length) {\r\n        this.promptForCurrentField();\r\n      } else {\r\n        this.chatInterfaceComp.addAiResponse('Thank you! Running the workflow...');\r\n        this.executeWorkflow();\r\n      }\r\n    };\r\n    reader.readAsDataURL(file);\r\n  }\r\n\r\n  // Panel management methods\r\n  toggleLeftPanel(): void {\r\n    this.isLeftPanelCollapsed = !this.isLeftPanelCollapsed;\r\n  }\r\n\r\n  toggleAgentExpansion(index: number): void {\r\n    this.expandedAgents[index] = !this.expandedAgents[index];\r\n  }\r\n\r\n  initializeAgentStates(): void {\r\n    this.expandedAgents = new Array(this.workflowAgents.length).fill(false);\r\n    this.agentStatuses = this.workflowAgents.map(() => ({ completed: false }));\r\n  }\r\n\r\n  // Extract input fields method (similar to pipeline component)\r\n  public extractInputField(workflowAgents: any[]) {\r\n    const PLACEHOLDER_PATTERNS = /(%\\d+\\$s)|\\{\\{([a-zA-Z0-9-_]+)\\}\\}/g;\r\n    const placeholderMap: { [key: string]: { agents: Set<string>; inputs: Set<string> } } = {};\r\n\r\n    workflowAgents.forEach((agent: any) => {\r\n      const agentName = agent?.name || `Agent ${workflowAgents.indexOf(agent) + 1}`;\r\n      const agentDescription = agent?.description || agent?.task?.description || '';\r\n      const matches = agentDescription.matchAll(PLACEHOLDER_PATTERNS) || [];\r\n\r\n      for (const match of matches) {\r\n        const placeholder = match[1] || match[2];\r\n        const placeholderInput = match[0];\r\n        if (!placeholderMap[placeholder]) {\r\n          placeholderMap[placeholder] = { agents: new Set(), inputs: new Set() };\r\n        }\r\n        placeholderMap[placeholder].agents.add(agentName);\r\n        placeholderMap[placeholder].inputs.add(placeholderInput);\r\n      }\r\n    });\r\n\r\n    return Object.entries(placeholderMap).map(([placeholder, { agents, inputs }]) => ({\r\n      name: [...agents].length > 2\r\n        ? `${[...agents].slice(0, -1).join(\", \")} and ${[...agents].at(-1)}`\r\n        : [...agents].join(\" and \"),\r\n      placeholder,\r\n      input: [...inputs][0],\r\n    }));\r\n  }\r\n\r\n  // Input validation\r\n  isInputValid(): boolean {\r\n    return this.workflowForm.valid && this.workflowId;\r\n  }\r\n}\r\n", "<div class=\"workflow-execution-container\">\r\n  <!-- SVG Gradient Definitions for Icons -->\r\n  <svg width=\"0\" height=\"0\" style=\"position: absolute\">\r\n    <defs>\r\n      <linearGradient id=\"gradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\r\n        <stop offset=\"0%\" stop-color=\"#6566CD\" />\r\n        <stop offset=\"100%\" stop-color=\"#F96CAB\" />\r\n      </linearGradient>\r\n    </defs>\r\n  </svg>\r\n  <div class=\"execution-content\" role=\"main\">\r\n    <!-- Left Panel: Workflow Execution Panel -->\r\n    <div\r\n      class=\"column execution-panel-column\"\r\n      [class.collapsed]=\"isLeftPanelCollapsed\"\r\n      role=\"region\"\r\n      aria-label=\"Workflow Execution Panel\"\r\n    >\r\n      <div class=\"column-content execution-panel-content\">\r\n        <div class=\"column-header\">\r\n          <div class=\"header-left\">\r\n            <ava-icon\r\n              iconName=\"arrowLeft\"\r\n              iconColor=\"#1A46A7\"\r\n              class=\"back-icon\"\r\n              (click)=\"navigateBack()\"\r\n            ></ava-icon>\r\n            <ava-icon\r\n              iconName=\"panelLeft\"\r\n              iconColor=\"#1A46A7\"\r\n              class=\"panel-icon\"\r\n              (click)=\"toggleLeftPanel()\"\r\n            ></ava-icon>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"panel-content\" [class.hidden]=\"isLeftPanelCollapsed\">\r\n          <!-- Agent Cards -->\r\n          <div class=\"agents-section\">\r\n            <ng-container *ngFor=\"let agent of workflowAgents; let i = index\">\r\n              <div class=\"agent-card\" [class.completed]=\"agentStatuses[i]?.completed\">\r\n                <div class=\"agent-header\">\r\n                  <div class=\"status-icon\">\r\n                    <ava-icon\r\n                      [iconName]=\"agentStatuses[i]?.completed ? 'check' : 'folder'\"\r\n                      [iconColor]=\"agentStatuses[i]?.completed ? '#22C55E' : '#1A46A7'\"\r\n                    ></ava-icon>\r\n                  </div>\r\n                  <div class=\"agent-info\">\r\n                    <h3 class=\"agent-name\">{{ agent?.name || 'Agent ' + (i + 1) }}</h3>\r\n                    <ava-icon\r\n                      iconName=\"chevronDown\"\r\n                      iconColor=\"#6B7280\"\r\n                      class=\"expand-icon\"\r\n                      [class.expanded]=\"expandedAgents[i]\"\r\n                      (click)=\"toggleAgentExpansion(i)\"\r\n                    ></ava-icon>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Agent Details (Expandable) -->\r\n                <div class=\"agent-details\" [class.expanded]=\"expandedAgents[i]\">\r\n                  <div class=\"agent-detail-item\" *ngIf=\"agent?.model\">\r\n                    <span class=\"detail-label\">Model:</span>\r\n                    <span class=\"detail-value\">{{ agent.model }}</span>\r\n                  </div>\r\n                  <div class=\"agent-detail-item\" *ngIf=\"agent?.tools?.length\">\r\n                    <span class=\"detail-label\">Tools:</span>\r\n                    <span class=\"detail-value\">{{ agent.tools.join(', ') }}</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Connection Line -->\r\n              <div class=\"connection-line\" *ngIf=\"i < workflowAgents.length - 1\"></div>\r\n            </ng-container>\r\n          </div>\r\n\r\n          <!-- Input Section -->\r\n          <div class=\"input-section\" *ngIf=\"userInputList.length > 0\">\r\n            <div class=\"input-header\">\r\n              <div class=\"status-icon\">\r\n                <ava-icon\r\n                  iconName=\"check\"\r\n                  iconColor=\"#22C55E\"\r\n                ></ava-icon>\r\n              </div>\r\n              <h3 class=\"input-title\">Enter the input</h3>\r\n            </div>\r\n\r\n            <div class=\"input-container\" [formGroup]=\"workflowForm\">\r\n              <ng-container *ngFor=\"let input of userInputList\">\r\n                <div class=\"input-field\">\r\n                  <textarea\r\n                    [formControlName]=\"input.input\"\r\n                    [placeholder]=\"'Input Entered'\"\r\n                    class=\"input-textarea\"\r\n                  ></textarea>\r\n                  <div class=\"input-actions\">\r\n                    <ava-icon iconName=\"paperclip\" iconColor=\"#6B7280\" class=\"attach-icon\"></ava-icon>\r\n                    <ava-icon iconName=\"edit\" iconColor=\"#6B7280\" class=\"edit-icon\"></ava-icon>\r\n                    <ava-icon iconName=\"send\" iconColor=\"#1A46A7\" class=\"send-icon\"></ava-icon>\r\n                  </div>\r\n                </div>\r\n              </ng-container>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Info Message -->\r\n          <div class=\"info-message\">\r\n            <p>Agent input needed in order to execute Workflow</p>\r\n          </div>\r\n\r\n          <!-- Execute Button -->\r\n          <div class=\"execute-section\">\r\n            <ava-button\r\n              label=\"▶ Execute Agent\"\r\n              variant=\"primary\"\r\n              size=\"large\"\r\n              [disabled]=\"!isInputValid()\"\r\n              (click)=\"executeWorkflow()\"\r\n              [customStyles]=\"{\r\n                'background': '#1A46A7',\r\n                'width': '100%',\r\n                'border-radius': '8px'\r\n              }\"\r\n            ></ava-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Right Column: Agent Output -->\r\n    <div class=\"column output-column\" role=\"region\" aria-label=\"Agent Output\">\r\n      <div class=\"column-content\">\r\n        <div class=\"column-header row\">\r\n          <div class=\"col-7\">\r\n            <ava-tabs\r\n              [tabs]=\"navigationTabs\"\r\n              [activeTabId]=\"activeTabId\"\r\n              variant=\"button\"\r\n              buttonShape=\"pill\"\r\n              [showContentPanels]=\"false\"\r\n              (tabChange)=\"onTabChange($event)\"\r\n              ariaLabel=\"Pill navigation tabs\"\r\n            ></ava-tabs>\r\n          </div>\r\n          <div class=\"col-5 right-section-header\">\r\n            <ava-button\r\n              label=\"Send for Approval\"\r\n              variant=\"primary\"\r\n              [customStyles]=\"{\r\n                background:\r\n                  'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n                '--button-effect-color': '33, 90, 214',\r\n                'border-radius': '8px',\r\n                'box-shadow': 'none',\r\n              }\"\r\n              size=\"medium\"\r\n            >\r\n            </ava-button>\r\n            <!-- <ava-button\r\n              label=\"Export\"\r\n              variant=\"secondary\"\r\n              size=\"medium\"\r\n              class=\"ms-2\"\r\n            >\r\n            </ava-button> -->\r\n          </div>\r\n        </div>\r\n        <!-- activity content -->\r\n        <div *ngIf=\"selectedTab === 'Agent Activity'\" style=\"height: 100%\">\r\n          <app-agent-activity\r\n            [activityLogs]=\"workflowLogs\"\r\n            [executionDetails]=\"executionDetails\"\r\n            [progress]=\"progress\"\r\n            [isRunning]=\"isRunning\"\r\n            (saveLogs)=\"saveLogs()\"\r\n            (controlAction)=\"handleControlAction($event)\"\r\n            [status]=\"status\"\r\n            (onOutPutBtnClick)=\"onTabChange({id: 'nav-products', label: 'Agent Output'})\"\r\n          ></app-agent-activity>\r\n        </div>\r\n        <!-- Agent Output Component -->\r\n        <div *ngIf=\"selectedTab === 'Agent Output'\" style=\"height: 100%\">\r\n          <app-agent-output\r\n            [outputs]=\"taskMessage\"\r\n            (export)=\"exportResults('output')\"\r\n          ></app-agent-output>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;AACzC,SAAiCC,WAAW,EAAEC,UAAU,QAAQ,gBAAgB;AAEhF;AACA,SAASC,sBAAsB,QAAQ,4DAA4D;AAEnG,SACEC,sBAAsB,QACjB,sDAAsD;AAC7D,SACEC,oBAAoB,QAEf,kDAAkD;AACzD,SACEC,eAAe,EACfC,aAAa,EAEbC,aAAa,QACR,wBAAwB;AAE/B,SAASC,WAAW,QAAQ,kCAAkC;AAC9D,OAAOC,iBAAiB,MAAM,+BAA+B;AAG7D,SAASC,eAAe,QAAqB,gCAAgC;;;;;;;;;;;;;;;;;;;;ICoCzDC,EADF,CAAAC,cAAA,cAAoD,eACvB;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxCH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAC9CF,EAD8C,CAAAG,YAAA,EAAO,EAC/C;;;;IADuBH,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAK,iBAAA,CAAAC,QAAA,CAAAC,KAAA,CAAiB;;;;;IAG5CP,EADF,CAAAC,cAAA,cAA4D,eAC/B;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxCH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,GAA4B;IACzDF,EADyD,CAAAG,YAAA,EAAO,EAC1D;;;;IADuBH,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAK,iBAAA,CAAAC,QAAA,CAAAE,KAAA,CAAAC,IAAA,OAA4B;;;;;IAM7DT,EAAA,CAAAU,SAAA,cAAyE;;;;;;IAnC3EV,EAAA,CAAAW,uBAAA,GAAkE;IAG5DX,EAFJ,CAAAC,cAAA,cAAwE,cAC5C,cACC;IACvBD,EAAA,CAAAU,SAAA,mBAGY;IACdV,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAAwB,aACC;IAAAD,EAAA,CAAAE,MAAA,GAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnEH,EAAA,CAAAC,cAAA,mBAMC;IADCD,EAAA,CAAAY,UAAA,mBAAAC,8EAAA;MAAA,MAAAC,IAAA,GAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA,EAAAC,KAAA;MAAA,MAAAC,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASF,MAAA,CAAAG,oBAAA,CAAAP,IAAA,CAAuB;IAAA,EAAC;IAGvCd,EAFK,CAAAG,YAAA,EAAW,EACR,EACF;IAGNH,EAAA,CAAAC,cAAA,cAAgE;IAK9DD,EAJA,CAAAsB,UAAA,KAAAC,0DAAA,kBAAoD,KAAAC,0DAAA,kBAIQ;IAKhExB,EADE,CAAAG,YAAA,EAAM,EACF;IAGNH,EAAA,CAAAsB,UAAA,KAAAG,0DAAA,kBAAmE;;;;;;;IAlC3CzB,EAAA,CAAAI,SAAA,EAA+C;IAA/CJ,EAAA,CAAA0B,WAAA,cAAAR,MAAA,CAAAS,aAAA,CAAAb,IAAA,mBAAAI,MAAA,CAAAS,aAAA,CAAAb,IAAA,EAAAc,SAAA,CAA+C;IAI/D5B,EAAA,CAAAI,SAAA,GAA6D;IAC7DJ,EADA,CAAA6B,UAAA,cAAAX,MAAA,CAAAS,aAAA,CAAAb,IAAA,mBAAAI,MAAA,CAAAS,aAAA,CAAAb,IAAA,EAAAc,SAAA,uBAA6D,eAAAV,MAAA,CAAAS,aAAA,CAAAb,IAAA,mBAAAI,MAAA,CAAAS,aAAA,CAAAb,IAAA,EAAAc,SAAA,0BACI;IAI5C5B,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAK,iBAAA,EAAAC,QAAA,kBAAAA,QAAA,CAAAwB,IAAA,iBAAAhB,IAAA,MAAuC;IAK5Dd,EAAA,CAAAI,SAAA,EAAoC;IAApCJ,EAAA,CAAA0B,WAAA,aAAAR,MAAA,CAAAa,cAAA,CAAAjB,IAAA,EAAoC;IAOfd,EAAA,CAAAI,SAAA,EAAoC;IAApCJ,EAAA,CAAA0B,WAAA,aAAAR,MAAA,CAAAa,cAAA,CAAAjB,IAAA,EAAoC;IAC7Bd,EAAA,CAAAI,SAAA,EAAkB;IAAlBJ,EAAA,CAAA6B,UAAA,SAAAvB,QAAA,kBAAAA,QAAA,CAAAC,KAAA,CAAkB;IAIlBP,EAAA,CAAAI,SAAA,EAA0B;IAA1BJ,EAAA,CAAA6B,UAAA,SAAAvB,QAAA,kBAAAA,QAAA,CAAAE,KAAA,kBAAAF,QAAA,CAAAE,KAAA,CAAAwB,MAAA,CAA0B;IAQhChC,EAAA,CAAAI,SAAA,EAAmC;IAAnCJ,EAAA,CAAA6B,UAAA,SAAAf,IAAA,GAAAI,MAAA,CAAAe,cAAA,CAAAD,MAAA,KAAmC;;;;;IAiBjEhC,EAAA,CAAAW,uBAAA,GAAkD;IAChDX,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAU,SAAA,mBAIY;IACZV,EAAA,CAAAC,cAAA,cAA2B;IAGzBD,EAFA,CAAAU,SAAA,mBAAkF,mBACP,mBACA;IAE/EV,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IATFH,EAAA,CAAAI,SAAA,GAA+B;IAC/BJ,EADA,CAAA6B,UAAA,oBAAAK,QAAA,CAAAC,KAAA,CAA+B,gCACA;;;;;IAdrCnC,EAFJ,CAAAC,cAAA,cAA4D,cAChC,cACC;IACvBD,EAAA,CAAAU,SAAA,mBAGY;IACdV,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IACzCF,EADyC,CAAAG,YAAA,EAAK,EACxC;IAENH,EAAA,CAAAC,cAAA,cAAwD;IACtDD,EAAA,CAAAsB,UAAA,IAAAc,yDAAA,2BAAkD;IAetDpC,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAhByBH,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAA6B,UAAA,cAAAX,MAAA,CAAAmB,YAAA,CAA0B;IACrBrC,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAA6B,UAAA,YAAAX,MAAA,CAAAoB,aAAA,CAAgB;;;;;;IAiFpDtC,EADF,CAAAC,cAAA,cAAmE,6BAUhE;IADCD,EAHA,CAAAY,UAAA,sBAAA2B,kFAAA;MAAAvC,EAAA,CAAAe,aAAA,CAAAyB,GAAA;MAAA,MAAAtB,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAYF,MAAA,CAAAuB,QAAA,EAAU;IAAA,EAAC,2BAAAC,uFAAAC,MAAA;MAAA3C,EAAA,CAAAe,aAAA,CAAAyB,GAAA;MAAA,MAAAtB,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CACNF,MAAA,CAAA0B,mBAAA,CAAAD,MAAA,CAA2B;IAAA,EAAC,8BAAAE,0FAAA;MAAA7C,EAAA,CAAAe,aAAA,CAAAyB,GAAA;MAAA,MAAAtB,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAEzBF,MAAA,CAAA4B,WAAA,CAAY;QAAAC,EAAA,EAAK,cAAc;QAAAC,KAAA,EAAS;MAAc,CAAC,CAAC;IAAA,EAAC;IAEjFhD,EADG,CAAAG,YAAA,EAAqB,EAClB;;;;IATFH,EAAA,CAAAI,SAAA,EAA6B;IAM7BJ,EANA,CAAA6B,UAAA,iBAAAX,MAAA,CAAA+B,YAAA,CAA6B,qBAAA/B,MAAA,CAAAgC,gBAAA,CACQ,aAAAhC,MAAA,CAAAiC,QAAA,CAChB,cAAAjC,MAAA,CAAAkC,SAAA,CACE,WAAAlC,MAAA,CAAAmC,MAAA,CAGN;;;;;;IAMnBrD,EADF,CAAAC,cAAA,cAAiE,2BAI9D;IADCD,EAAA,CAAAY,UAAA,oBAAA0C,8EAAA;MAAAtD,EAAA,CAAAe,aAAA,CAAAwC,GAAA;MAAA,MAAArC,MAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAUF,MAAA,CAAAsC,aAAA,CAAc,QAAQ,CAAC;IAAA,EAAC;IAEtCxD,EADG,CAAAG,YAAA,EAAmB,EAChB;;;;IAHFH,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAA6B,UAAA,YAAAX,MAAA,CAAAuC,WAAA,CAAuB;;;AD3InC,WAAaC,0BAA0B;EAAjC,MAAOA,0BAA0B;IA0E3BC,KAAA;IACAC,MAAA;IACAC,eAAA;IACAC,YAAA;IACAC,aAAA;IACAC,WAAA;IA9EVC,cAAc,GAAc,CAC1B;MAAElB,EAAE,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAgB,CAAE,EAC3C;MAAED,EAAE,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAc,CAAE,EAC7C;MAAED,EAAE,EAAE,cAAc;MAAEC,KAAK,EAAE,SAAS;MAAEkB,QAAQ,EAAE;IAAI,CAAE,CACzD;IACD;IACAC,UAAU,GAAkB,IAAI;IAChCC,YAAY,GAAW,UAAU;IAEjCC,SAAS,GAAGvE,iBAAwC;IAGpDwE,iBAAiB;IAEjB;IACAC,YAAY,GAAkB,EAAE;IAChCC,gBAAgB,GAAW,CAAC;IAC5BtB,gBAAgB;IAChBE,SAAS,GAAY,KAAK;IAC1BC,MAAM,GAAoBtD,eAAe,CAAC0E,UAAU;IAEpD;IACAC,YAAY,GAAkB,EAAE;IAChCC,gBAAgB,GAAY,KAAK;IACjCC,SAAS,GAAG,EAAE;IAEd;IACAC,YAAY,GAAiB,EAAE;IACxBxC,YAAY;IACZyC,QAAQ,GAAY,MAAM;IAEjC;IACAC,kBAAkB,GAAgB,IAAI;IACtCC,kBAAkB,GAAY,KAAK;IACnCC,WAAW;IAEJhC,YAAY,GAAU,EAAE;IAC/BiC,kBAAkB,GAAGrF,WAAW,CAACsF,kBAAkB,IAAI,KAAK;IAErDC,mBAAmB,GAAY,KAAK;IAC3CC,gBAAgB;IAEhB;IACQC,QAAQ,GAAG,IAAInG,OAAO,EAAQ;IACtCoG,WAAW,GAAW,gBAAgB;IACtCC,QAAQ,GAAa,CACnB;MAAEzC,EAAE,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAgB,CAAE,EAC3C;MAAED,EAAE,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAc,CAAE,EACvC;MAAED,EAAE,EAAE,SAAS;MAAEC,KAAK,EAAE,SAAS;MAAEkB,QAAQ,EAAE;IAAI,CAAE,CACpD;IACDuB,QAAQ,GAAG,KAAK;IAChBC,UAAU;IACVjC,WAAW,GAAG,EAAE;IAChBkC,WAAW,GAAG,KAAK;IACnBC,WAAW,GAAa,KAAK;IAC7BC,aAAa,GAAW,EAAE;IAC1B5D,cAAc,GAAU,EAAE;IAC1BK,aAAa,GAAU,EAAE;IACzBa,QAAQ,GAAG,CAAC;IACZ2C,SAAS,GAAG,KAAK;IACjBC,WAAW,GAAW,EAAE;IAExBC,eAAe,GAAa,EAAE;IAC9BC,iBAAiB,GAAW,CAAC;IAC7BC,WAAW,GAAW,UAAU;IAEhC;IACAC,oBAAoB,GAAG,KAAK;IAC5BpE,cAAc,GAAc,EAAE;IAC9BJ,aAAa,GAA6B,EAAE;IAG5CyE,YACUzC,KAAqB,EACrBC,MAAc,EACdC,eAAgC,EAChCC,YAAiC,EACjCC,aAA4B,EAC5BC,WAAwB;MALxB,KAAAL,KAAK,GAALA,KAAK;MACL,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,eAAe,GAAfA,eAAe;MACf,KAAAC,YAAY,GAAZA,YAAY;MACZ,KAAAC,aAAa,GAAbA,aAAa;MACb,KAAAC,WAAW,GAAXA,WAAW;IAClB;IAEHqC,QAAQA,CAAA;MACN,IAAI,CAACtC,aAAa,CAACuC,aAAa,EAAE;MAClC,IAAI,CAACf,WAAW,GAAG,gBAAgB;MACnC,IAAI,CAACN,WAAW,GAAGsB,MAAM,CAACC,UAAU,EAAE;MACtC;MACA,IAAI,CAAC7C,KAAK,CAAC8C,QAAQ,CAACC,IAAI,CAACtH,SAAS,CAAC,IAAI,CAACkG,QAAQ,CAAC,CAAC,CAACqB,SAAS,CAAEC,MAAM,IAAI;QACtE,IAAI,CAACzC,UAAU,GAAGyC,MAAM,CAACC,GAAG,CAAC,IAAI,CAAC;QAClC,IAAI,IAAI,CAAC1C,UAAU,EAAE;UACnB,IAAI,CAAC2C,YAAY,CAAC,IAAI,CAAC3C,UAAU,CAAC;QACpC,CAAC,MAAM;UACL;UACA,IAAI,CAACP,MAAM,CAACmD,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;QAC5C;MACF,CAAC,CAAC;MACF;IACF;IAEAC,WAAWA,CAAA;MACT,IAAI,CAAC1B,QAAQ,CAAC2B,IAAI,EAAE;MACpB,IAAI,CAAC3B,QAAQ,CAAC4B,QAAQ,EAAE;MACxB,IAAI,CAACnD,aAAa,CAACoD,YAAY,EAAE;IACnC;IACArE,WAAWA,CAACsE,KAAoC;MAC9C,IAAI,CAAC7B,WAAW,GAAG6B,KAAK,CAACpE,KAAK;MAC9B,IAAI,CAACkD,WAAW,GAAGkB,KAAK,CAACrE,EAAE;MAC3BsE,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEF,KAAK,CAAC;IACpC;IAEA;IACAN,YAAYA,CAAC/D,EAAU;MACrB;MACAsE,OAAO,CAACC,GAAG,CAAC,6BAA6BvE,EAAE,EAAE,CAAC;MAC9C,IAAI,CAAC2B,YAAY,GAAG,CAClB;QACE6C,IAAI,EAAE,IAAI;QACVC,IAAI,EAAE;OACQ,CACjB;MACD,IAAI,CAACnF,YAAY,GAAG,IAAI,CAAC2B,WAAW,CAACyD,KAAK,CAAC,EAAE,CAAC;MAE9C,IAAI,CAAC5D,eAAe,CAAC6D,eAAe,CAAC3E,EAAE,CAAC,CAAC4D,SAAS,CAAC;QAC/CM,IAAI,EAAGU,GAAG,IAAI;UACd,IAAI,CAAC1F,cAAc,GAAG0F,GAAG,CAAC1F,cAAc;UACxC,IAAI,CAACK,aAAa,GAAG,IAAI,CAACsF,iBAAiB,CAAC,IAAI,CAAC3F,cAAc,CAAC;UAChE,IAAG,IAAI,CAACK,aAAa,CAACN,MAAM,KAAK,CAAC,EAAC;YACjC,IAAI,CAAC4D,WAAW,GAAG,IAAI;UACzB;UACA,IAAI,CAACxB,YAAY,GAAGuD,GAAG,CAAC7F,IAAI;UAC5B,IAAI,CAAC+F,cAAc,EAAE;UACrB,IAAI,CAACC,qBAAqB,EAAE;UAC5B,IAAI,CAACC,oBAAoB,EAAE;QAC7B,CAAC;QACCC,KAAK,EAAGC,GAAG,IAAI;UACb,IAAI,CAACrC,WAAW,GAAG,IAAI;UACvB,IAAI,CAACtB,iBAAiB,CAAC4D,aAAa,CAAC,uFAAuF,CAAC;UAC7Hb,OAAO,CAACC,GAAG,CAACW,GAAG,CAAC;QAClB;OACH,CAAC;IAEJ;IAEOL,iBAAiBA,CAACO,cAAmB;MAC1C,MAAMC,oBAAoB,GAAI,qCAAqC;MACnE,MAAMC,cAAc,GAAoE,EAAE;MAE1FF,cAAc,CAACG,OAAO,CAAEC,KAAU,IAAI;QACpC,MAAMC,SAAS,GAAGD,KAAK,EAAEE,YAAY,EAAE3G,IAAI;QAC3C,MAAM4G,gBAAgB,GAAGH,KAAK,EAAEE,YAAY,EAAEE,WAAW;QACzD,MAAMC,OAAO,GAAGF,gBAAgB,CAACG,QAAQ,CAACT,oBAAoB,CAAC,IAAI,EAAE;QAErE,KAAK,MAAMU,KAAK,IAAIF,OAAO,EAAE;UAC3B,MAAMG,WAAW,GAAGD,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC;UACxC,MAAME,gBAAgB,GAAGF,KAAK,CAAC,CAAC,CAAC;UACjC,IAAI,CAACT,cAAc,CAACU,WAAW,CAAC,EAAE;YAChCV,cAAc,CAACU,WAAW,CAAC,GAAG;cAAEE,MAAM,EAAE,IAAIC,GAAG,EAAE;cAAEC,MAAM,EAAE,IAAID,GAAG;YAAE,CAAE;YAAC;UACzE;UACAb,cAAc,CAACU,WAAW,CAAC,CAACE,MAAM,CAACG,GAAG,CAACZ,SAAS,CAAC;UACjDH,cAAc,CAACU,WAAW,CAAC,CAACI,MAAM,CAACC,GAAG,CAACJ,gBAAgB,CAAC;QAC1D;MACF,CAAC,CAAC;MAEF,OAAOK,MAAM,CAACC,OAAO,CAACjB,cAAc,CAAC,CAACkB,GAAG,CAAC,CAAC,CAACR,WAAW,EAAE;QAAEE,MAAM;QAAEE;MAAM,CAAE,CAAC,MAAM;QAChFrH,IAAI,EAAE,CAAC,GAAGmH,MAAM,CAAC,CAACjH,MAAM,GAAG,CAAC,GACxB,GAAG,CAAC,GAAGiH,MAAM,CAAC,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC/I,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAGwI,MAAM,CAAC,CAACQ,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,GAClE,CAAC,GAAGR,MAAM,CAAC,CAACxI,IAAI,CAAC,OAAO,CAAC;QAC7BsI,WAAW;QACX5G,KAAK,EAAE,CAAC,GAAGgH,MAAM,CAAC,CAAC,CAAC;OACrB,CAAC,CAAC;IACL;IAEOO,YAAYA,CAACvH,KAAa;MAC/B,MAAM2G,KAAK,GAAG3G,KAAK,CAAC2G,KAAK,CAAC,WAAW,CAAC;MACtC,IAAIA,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,EAAE;QACrB,MAAMa,YAAY,GAAGb,KAAK,CAAC,CAAC,CAAC,CAACc,IAAI,EAAE;QACpC,OAAOD,YAAY,CAACE,UAAU,CAAC,OAAO,CAAC,IAAIF,YAAY,CAACE,UAAU,CAAC,OAAO,CAAC;MAC7E;MACA,OAAO,KAAK;IACd;IAEOhC,cAAcA,CAAA;MACnB,IAAI,CAACvF,aAAa,CAACgG,OAAO,CAAEtF,KAAU,IAAI;QACxC,IAAI,CAACX,YAAY,CAACyH,UAAU,CAAC9G,KAAK,CAACb,KAAK,EAAE,IAAI,CAAC6B,WAAW,CAAC+F,OAAO,CAAC,EAAE,EAAEzK,UAAU,CAAC0K,QAAQ,CAAC,CAAC;MAC9F,CAAC,CAAC;IACJ;IAEOC,YAAYA,CAAA;MACjB,OAAO,IAAI,CAAC5H,YAAY,CAAC6H,KAAK,IAAI,IAAI,CAAC/F,UAAU;IACnD;IAEAgG,iBAAiBA,CAAA;MACf,IAAI,CAAChH,QAAQ,GAAG,CAAC;MACjB,IAAI,CAACkC,gBAAgB,GAAG+E,WAAW,CAAC,MAAK;QACvC,IAAI,IAAI,CAACjH,QAAQ,GAAG,EAAE,EAAE;UACtB,IAAI,CAACA,QAAQ,IAAI,CAAC,CAAC,CAAC;QACtB;MACF,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACX;IAEAkH,gBAAgBA,CAAA;MACdC,aAAa,CAAC,IAAI,CAACjF,gBAAgB,CAAC;MACpC,IAAI,CAAClC,QAAQ,GAAG,GAAG;MAEnBoH,UAAU,CAAC,MAAK;QACd,IAAI,CAACzE,SAAS,GAAG,KAAK;MACxB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACX;IAEA;IACA0E,iBAAiBA,CAACC,OAAe;MAC/B;MACA,IAAI,CAAC9F,gBAAgB,GAAG,IAAI;MAC5B,IAAG8F,OAAO,CAACb,IAAI,EAAE,KAAK,EAAE,EAAC;QACvB,IAAG,IAAI,CAAC5D,eAAe,CAAChE,MAAM,KAAK,CAAC,EAAC;UACnC,IAAI,CAACsC,iBAAiB,CAAC4D,aAAa,CAAC,2BAA2B,CAAC;UACjE,IAAI,CAACwC,eAAe,EAAE;QACxB;QACA;MACF;MAEA,IAAG,IAAI,CAACtF,mBAAmB,IAAI,IAAI,CAACa,iBAAiB,KAAG,IAAI,CAACD,eAAe,CAAChE,MAAM,EAAC;QAChF,IAAI,CAACsC,iBAAiB,CAAC4D,aAAa,CAAC,2BAA2B,CAAC;QACjE,IAAI,CAACwC,eAAe,EAAE;QACxB;MACF;MAEA,MAAMC,KAAK,GAAG,IAAI,CAAC3E,eAAe,CAAC,IAAI,CAACC,iBAAiB,CAAC;MAC1D,IAAI,IAAI,CAACyD,YAAY,CAACiB,KAAK,CAAC,EAAE;QAC5B;QACA,IAAI,CAACrG,iBAAiB,CAAC4D,aAAa,CAAC,mCAAmCyC,KAAK,EAAE,CAAC;QAChF;MACF;MAEA,IAAI,CAACtI,YAAY,CAACwE,GAAG,CAAC8D,KAAK,CAAC,EAAEC,QAAQ,CAACH,OAAO,CAAC;MAC/C,IAAI,CAACxE,iBAAiB,EAAE;MAExB,IAAI,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAACD,eAAe,CAAChE,MAAM,EAAE;QACxD,IAAI,CAAC6I,qBAAqB,EAAE;MAC9B,CAAC,MAAM;QACL,IAAI,CAACvG,iBAAiB,CAAC4D,aAAa,CAAC,oDAAoD,CAAC;QAC1F,IAAI,CAACwC,eAAe,EAAE;MACxB;IACF;IAEA;IACAjI,QAAQA,CAAA;MACN4E,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACvC;IACF;IAEA;IACA9D,aAAaA,CAACsH,OAA8B;MAC1CzD,OAAO,CAACC,GAAG,CAAC,aAAawD,OAAO,UAAU,CAAC;MAE3C,IAAIA,OAAO,KAAK,UAAU,EAAE;QAC1B,MAAMC,IAAI,GAAG,IAAI,CAACxG,YAAY,CAC3BgF,GAAG,CAAEjC,GAAG,IAAK,IAAIA,GAAG,CAAC0D,SAAS,KAAK1D,GAAG,CAACmD,OAAO,EAAE,CAAC,CACjDhK,IAAI,CAAC,IAAI,CAAC;QACb,IAAI,CAACwK,cAAc,CAACF,IAAI,EAAE,4BAA4B,EAAE,YAAY,CAAC;MACvE,CAAC,MAAM;QACL,MAAMA,IAAI,GAAGG,IAAI,CAACC,SAAS,CAAC,IAAI,CAACtG,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC;QACvD,IAAI,CAACoG,cAAc,CAACF,IAAI,EAAE,uBAAuB,EAAE,kBAAkB,CAAC;MACxE;IACF;IAEA;IACQE,cAAcA,CAACF,IAAY,EAAEK,QAAgB,EAAEC,IAAY;MACjE,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACR,IAAI,CAAC,EAAE;QAAEM;MAAI,CAAE,CAAC;MACvC,MAAMG,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;MACrC,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGN,GAAG;MACfG,IAAI,CAACI,QAAQ,GAAGX,QAAQ;MACxBO,IAAI,CAACK,KAAK,EAAE;MACZP,GAAG,CAACQ,eAAe,CAACT,GAAG,CAAC;IAC1B;IAEA;IACA5I,mBAAmBA,CAACsJ,MAAiC;MACnD7E,OAAO,CAACC,GAAG,CAAC,mBAAmB4E,MAAM,EAAE,CAAC;MACxC;MAEA,IAAIA,MAAM,KAAK,MAAM,EAAE;QACrB,IAAI,CAAC9I,SAAS,GAAG,IAAI;MACvB,CAAC,MAAM,IAAI8I,MAAM,KAAK,OAAO,IAAIA,MAAM,KAAK,MAAM,EAAE;QAClD,IAAI,CAAC9I,SAAS,GAAG,KAAK;MACxB;IACF;IAEA;IACA+I,YAAYA,CAAA;MACV,IAAI,CAACvI,MAAM,CAACmD,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;IAC5C;IAEA;IACAqF,YAAYA,CAAA;MACV,IAAI,IAAI,CAACjI,UAAU,EAAE;QACnB,IAAI,CAACP,MAAM,CAACmD,QAAQ,CAAC,CAAC,uBAAuB,EAAE,IAAI,CAAC5C,UAAU,CAAC,CAAC;MAClE;IACF;IAEOkI,kBAAkBA,CAACC,KAAA,GAAgB,IAAI;MAC5C/B,UAAU,CAAC,MAAK;QACd,IAAI,CAAC,IAAI,CAACnF,mBAAmB,EAAE;UAC7BiC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACjD,SAAS,CAAC;UAC3BgD,OAAO,CAACC,GAAG,CAAC,IAAI,CAACjD,SAAS,CAAC,QAAQ,CAAC,CAACkI,sBAAsB,CAAC;UAC5D,IAAI,CAACtJ,YAAY,CAACuJ,IAAI,CAAC;YACrBC,OAAO,EAAE,IAAI,CAACpI,SAAS,CAAC,QAAQ,CAAC,CAACkI,sBAAsB;YACxDG,KAAK,EAAE;WACR,CAAC;QACJ;MACF,CAAC,EAAEJ,KAAK,CAAC;IACX;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEOK,eAAeA,CAAC1H,WAAmB;MACxC,IAAI,CAACpB,eAAe,CACjB+I,kBAAkB,CAAC3H,WAAW,CAAC,CAC/ByB,IAAI,CAACtH,SAAS,CAAC,IAAI,CAACkG,QAAQ,CAAC,CAAC,CAC9BqB,SAAS,CAAC;QACTM,IAAI,EAAGwD,OAAO,IAAI;UAChBpD,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEmD,OAAO,CAAC;UACjC,MAAM;YAAEgC,OAAO;YAAEC;UAAK,CAAE,GAAGjC,OAAO;UAClC,IAAIiC,KAAK,EAAE;YACT,IAAI,CAACzJ,YAAY,CAACuJ,IAAI,CAAC;cAAEC,OAAO;cAAEC;YAAK,CAAE,CAAC;UAC5C,CAAC,MAAM,IAAI,IAAI,CAACxH,kBAAkB,KAAK,KAAK,EAAE;YAC5C;UAAA;QAEJ,CAAC;QACD8C,KAAK,EAAGC,GAAG,IAAI;UACb,IAAI,CAAChF,YAAY,CAACuJ,IAAI,CAAC;YACrBC,OAAO,EAAE,IAAI,CAACpI,SAAS,CAAC,aAAa,CAAC;YACtCqI,KAAK,EAAE;WACR,CAAC;UACFrF,OAAO,CAACW,KAAK,CAAC,kBAAkB,EAAEC,GAAG,CAAC;QACxC,CAAC;QACDf,QAAQ,EAAEA,CAAA,KAAK;UACb,IAAI,CAACmF,kBAAkB,EAAE;UACzBhF,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC5C;OACD,CAAC;IACN;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEOuF,YAAYA,CAACC,MAAc;MAChC,IAAI,CAACnH,WAAW,GAAG,KAAK;MACxB,IAAI;QACF,MAAMoH,YAAY,GAAG7B,IAAI,CAAC8B,KAAK,CAACF,MAAM,CAAC;QACvC,IAAI,CAACnH,WAAW,GAAG,IAAI;QACvB,OAAOoH,YAAY;MACrB,CAAC,CAAC,OAAOE,CAAC,EAAE;QACV,OAAO,IAAI;MACb;IACF;IAEOvC,eAAeA,CAAA;MACpB,IAAIwC,OAAO,GAAmC,IAAIC,QAAQ,EAAE;MAC5D,IAAIC,WAAW,GAAG,EAAE;MAGpB,IAAI,CAAC/J,MAAM,GAAGtD,eAAe,CAACsN,OAAO;MACrC,IAAI,IAAI,CAACxH,aAAa,CAAC7D,MAAM,EAAE;QAC7B,IAAI,CAAC6D,aAAa,CAACyC,OAAO,CAAEgF,IAAI,IAAI;UAClCJ,OAAO,CAACK,MAAM,CAAC,OAAO,EAAED,IAAI,CAAC;QAC/B,CAAC,CAAC;QACFJ,OAAO,CAACK,MAAM,CAAC,YAAY,EAAE,IAAI,CAACpJ,UAAU,CAAC;QAC7C+I,OAAO,CAACK,MAAM,CAAC,YAAY,EAAErC,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC9I,YAAY,CAACmL,KAAK,CAAC,CAAC;QACrEN,OAAO,CAACK,MAAM,CAAC,MAAM,EAAE,IAAI,CAACzJ,YAAY,CAAC2J,aAAa,EAAE,CAAC;QACzDP,OAAO,CAACK,MAAM,CAAC,aAAa,EAAE,IAAI,CAACtI,WAAW,CAAC;QAC/CmI,WAAW,GAAG,QAAQ;MACxB,CAAC,MAAM;QACLF,OAAO,GAAG;UACRQ,UAAU,EAAE,IAAI,CAACvJ,UAAU;UAC3BwJ,UAAU,EAAE,IAAI,CAACtL,YAAY,CAACmL,KAAK;UACnCvI,WAAW,EAAE,IAAI,CAACA,WAAW;UAC7B2I,IAAI,EAAE,IAAI,CAAC9J,YAAY,CAAC2J,aAAa;SACtC;MACH;MAEA,IAAI,CAACd,eAAe,CAAC,IAAI,CAAC1H,WAAW,CAAC;MACtC,IAAI,CAACkF,iBAAiB,EAAE;MAExB,IAAI,CAACtG,eAAe,CACjB6G,eAAe,CAACwC,OAAO,EAAEE,WAAW,CAAC,CACrC1G,IAAI,CAACtH,SAAS,CAAC,IAAI,CAACkG,QAAQ,CAAC,CAAC,CAC9BqB,SAAS,CAAC;QACTM,IAAI,EAAGU,GAAG,IAAI;UACZ,IAAI,CAAChD,gBAAgB,GAAG,KAAK;UAC7B,IAAI,CAACvB,SAAS,GAAG,KAAK;UACtB,IAAI,CAACkB,iBAAiB,CAAC4D,aAAa,CAACP,GAAG,EAAE8C,OAAO,IAAI,4CAA4C,CAAC;UAElG,IAAI9C,GAAG,EAAEkG,gBAAgB,EAAEC,QAAQ,EAAEhB,MAAM,EAAE;YAC3C,IAAI,CAAC1H,mBAAmB,GAAG,IAAI;YAC/B;YACA,IAAI,CAACnC,YAAY,CAACuJ,IAAI,CAAC;cACrBC,OAAO,EAAE,IAAI,CAACpI,SAAS,CAAC,QAAQ,CAAC,CAAC0J,oBAAoB;cACtDrB,KAAK,EAAE;aACR,CAAC;YACF,IAAI,CAACjH,QAAQ,GAAG,KAAK;YACrB,IAAI,CAACC,UAAU,GAAGiC,GAAG,EAAEkG,gBAAgB,EAAEC,QAAQ,EAAEhB,MAAM;YACzD,IAAI,CAACjI,YAAY,GAAG8C,GAAG,EAAEkG,gBAAgB,EAAEC,QAAQ,EAAEE,YAAY,CAACzE,GAAG,CAAE0E,IAAS,IAAI;cAClF,OAAO;gBACLlL,EAAE,EAAEkL,IAAI,EAAElL,EAAE,IAAI,EAAE;gBAClBmL,KAAK,EAAED,IAAI,EAAEC,KAAK,IAAI,EAAE;gBACxBzB,OAAO,EAAEwB,IAAI,EAAExB,OAAO,IAAI,EAAE;gBAC5BjE,SAAS,EAAEyF,IAAI,EAAEzF,SAAS,IAAI,EAAE;gBAChCwC,SAAS,EAAEiD,IAAI,EAAEjD,SAAS,IAAI,EAAE;gBAChCK,IAAI,EAAE4C,IAAI,EAAE5C,IAAI,IAAI,EAAE;gBACtB1C,WAAW,EAAEsF,IAAI,EAAEtF,WAAW,IAAI,EAAE;gBACpCwF,eAAe,EAAEF,IAAI,EAAEE,eAAe,IAAI,EAAE;gBAC5CC,OAAO,EAAEH,IAAI,EAAEG,OAAO,IAAI,EAAE;gBAC5BC,GAAG,EAAEJ,IAAI,EAAEI,GAAG,IAAI;eACnB;YACH,CAAC,CAAC;YAEF,IAAI,CAAC5K,WAAW,GAAGkE,GAAG,EAAEkG,gBAAgB,EAAEC,QAAQ,EAAEE,YAAY,CAACzE,GAAG,CACjE0E,IAKA,IAAI;cACH,OAAO;gBACLtF,WAAW,EAAEsF,IAAI,CAACtF,WAAW;gBAC7ByF,OAAO,EAAEH,IAAI,CAACG,OAAO;gBACrBC,GAAG,EAAEJ,IAAI,CAACI,GAAG;gBACbF,eAAe,EAAEF,IAAI,CAACE;eACvB;YACH,CAAC,CACF;YAED;YACA;YACA;YAEA;YACA;YACA;YACA;YACA;YACA;UACF;UACA,IAAI,CAACtB,YAAY,CAAC,IAAI,CAACnH,UAAU,CAAC;UAClC,IAAI,CAACrC,MAAM,GAAGtD,eAAe,CAAC6B,SAAS;UACvC,IAAI,CAACyI,gBAAgB,EAAE;UACvB,IAAI,CAACxE,aAAa,GAAG,EAAE;QACzB,CAAC;QACDmC,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAAC5C,mBAAmB,GAAG,IAAI;UAC/B,IAAI,CAACT,gBAAgB,GAAG,KAAK;UAC7B,IAAI,CAACc,QAAQ,GAAG,IAAI;UACpB,IAAI,CAACC,UAAU,GAAGsC,KAAK,EAAEA,KAAK,EAAEsG,MAAM;UACtC,IAAI,CAACzK,eAAe,CAAC0K,qBAAqB,EAAE;UAC5C,IAAI,CAACtL,YAAY,CAACuJ,IAAI,CAAC;YACrBC,OAAO,EAAE,IAAI,CAACpI,SAAS,CAAC,QAAQ,CAAC,CAACmK,iBAAiB;YACnD9B,KAAK,EAAE;WACR,CAAC;UACF,IAAI,CAACpI,iBAAiB,CAAC4D,aAAa,CAClC,sDAAsD,CACvD;UACD,IAAI,CAACrC,aAAa,GAAG,EAAE;UACvB,IAAI,CAACwE,gBAAgB,EAAE;UACvBhD,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEU,KAAK,CAACyC,OAAO,CAAC;QACxC;OACD,CAAC;IACN;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEAgE,gBAAgBA,CAAA;MACdpH,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;IACjC;IAEAoH,qBAAqBA,CAACC,KAAa;MACjC,IAAG,IAAI,CAAC1I,iBAAiB,KAAG,IAAI,CAACD,eAAe,CAAChE,MAAM,IAAI,IAAI,CAACgE,eAAe,CAAChE,MAAM,KAAG,CAAC,EAAC;QACzF,IAAI,CAAC6D,aAAa,GAAG8I,KAAK;QAC1B;MACF;MAEA,MAAMhE,KAAK,GAAG,IAAI,CAAC3E,eAAe,CAAC,IAAI,CAACC,iBAAiB,CAAC;MAC1D,IAAG,IAAI,CAACyD,YAAY,CAACiB,KAAK,CAAC,EAAC;QAC1B,IAAIgE,KAAK,IAAIA,KAAK,CAAC3M,MAAM,GAAG,CAAC,EAAE;UAC7B,IAAI,CAAC4M,eAAe,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC;QAChC;MACF,CAAC,MAAM;QACL,IAAI,CAAC9I,aAAa,GAAG8I,KAAK;MAC5B;IACF;IAEA5G,oBAAoBA,CAAA;MAClB,IAAI,CAAC/B,eAAe,GAAGqD,MAAM,CAACwF,IAAI,CAAC,IAAI,CAACxM,YAAY,CAACyM,QAAQ,CAAC;MAC9D,IAAI,CAAC7I,iBAAiB,GAAG,CAAC;MAC1B,IAAI,IAAI,CAACD,eAAe,CAAChE,MAAM,GAAG,CAAC,EAAE;QACnC,IAAI,CAAC6I,qBAAqB,EAAE;MAC9B,CAAC,MACG;QACF,IAAI,CAACjF,WAAW,GAAG,IAAI;QACvB,IAAI,CAACtB,iBAAiB,CAAC4D,aAAa,CAAC,uFAAuF,CAAC;MAC/H;IACF;IAEA2C,qBAAqBA,CAAA;MACnB,MAAMF,KAAK,GAAG,IAAI,CAAC3E,eAAe,CAAC,IAAI,CAACC,iBAAiB,CAAC;MAC1D,IAAI,IAAI,CAACyD,YAAY,CAACiB,KAAK,CAAC,EAAE;QAC5B,IAAI,CAAC7F,QAAQ,GAAG,sBAAsB;QACtC,IAAI,CAACR,iBAAiB,CAAC4D,aAAa,CAAC,8BAA8ByC,KAAK,EAAE,CAAC;QAC3E;MACF,CAAC,MAAM;QACL,IAAI,CAAC7F,QAAQ,GAAG,MAAM,CAAC,CAAC;QACxB,IAAI,CAACR,iBAAiB,CAAC4D,aAAa,CAAC,6BAA6ByC,KAAK,EAAE,CAAC;MAC5E;IACF;IAEAiE,eAAeA,CAACtB,IAAU;MACxB,MAAM3C,KAAK,GAAG,IAAI,CAAC3E,eAAe,CAAC,IAAI,CAACC,iBAAiB,CAAC;MAC1D,IAAI,CAAC,IAAI,CAACyD,YAAY,CAACiB,KAAK,CAAC,EAAE;MAE/B,MAAMoE,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,MAAM,GAAG,MAAK;QACnB,MAAMC,YAAY,GAAIH,MAAM,CAACI,MAAiB;QAC9C,IAAI,CAAC9M,YAAY,CAACwE,GAAG,CAAC8D,KAAK,CAAC,EAAEC,QAAQ,CAACsE,YAAY,CAAC;QACpD,IAAI,CAACjJ,iBAAiB,EAAE;QACxB,IAAI,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAACD,eAAe,CAAChE,MAAM,EAAE;UACxD,IAAI,CAAC6I,qBAAqB,EAAE;QAC9B,CAAC,MAAM;UACL,IAAI,CAACvG,iBAAiB,CAAC4D,aAAa,CAAC,oCAAoC,CAAC;UAC1E,IAAI,CAACwC,eAAe,EAAE;QACxB;MACF,CAAC;MACDqE,MAAM,CAACK,aAAa,CAAC9B,IAAI,CAAC;IAC5B;IAEA;IACA+B,eAAeA,CAAA;MACb,IAAI,CAAClJ,oBAAoB,GAAG,CAAC,IAAI,CAACA,oBAAoB;IACxD;IAEA9E,oBAAoBA,CAACJ,KAAa;MAChC,IAAI,CAACc,cAAc,CAACd,KAAK,CAAC,GAAG,CAAC,IAAI,CAACc,cAAc,CAACd,KAAK,CAAC;IAC1D;IAEA6G,qBAAqBA,CAAA;MACnB,IAAI,CAAC/F,cAAc,GAAG,IAAIuN,KAAK,CAAC,IAAI,CAACrN,cAAc,CAACD,MAAM,CAAC,CAACuN,IAAI,CAAC,KAAK,CAAC;MACvE,IAAI,CAAC5N,aAAa,GAAG,IAAI,CAACM,cAAc,CAACsH,GAAG,CAAC,OAAO;QAAE3H,SAAS,EAAE;MAAK,CAAE,CAAC,CAAC;IAC5E;IAEA;IACOgG,iBAAiBA,CAAC3F,cAAqB;MAC5C,MAAMmG,oBAAoB,GAAG,qCAAqC;MAClE,MAAMC,cAAc,GAAoE,EAAE;MAE1FpG,cAAc,CAACqG,OAAO,CAAEC,KAAU,IAAI;QACpC,MAAMC,SAAS,GAAGD,KAAK,EAAEzG,IAAI,IAAI,SAASG,cAAc,CAACuN,OAAO,CAACjH,KAAK,CAAC,GAAG,CAAC,EAAE;QAC7E,MAAMG,gBAAgB,GAAGH,KAAK,EAAEI,WAAW,IAAIJ,KAAK,EAAE0F,IAAI,EAAEtF,WAAW,IAAI,EAAE;QAC7E,MAAMC,OAAO,GAAGF,gBAAgB,CAACG,QAAQ,CAACT,oBAAoB,CAAC,IAAI,EAAE;QAErE,KAAK,MAAMU,KAAK,IAAIF,OAAO,EAAE;UAC3B,MAAMG,WAAW,GAAGD,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC;UACxC,MAAME,gBAAgB,GAAGF,KAAK,CAAC,CAAC,CAAC;UACjC,IAAI,CAACT,cAAc,CAACU,WAAW,CAAC,EAAE;YAChCV,cAAc,CAACU,WAAW,CAAC,GAAG;cAAEE,MAAM,EAAE,IAAIC,GAAG,EAAE;cAAEC,MAAM,EAAE,IAAID,GAAG;YAAE,CAAE;UACxE;UACAb,cAAc,CAACU,WAAW,CAAC,CAACE,MAAM,CAACG,GAAG,CAACZ,SAAS,CAAC;UACjDH,cAAc,CAACU,WAAW,CAAC,CAACI,MAAM,CAACC,GAAG,CAACJ,gBAAgB,CAAC;QAC1D;MACF,CAAC,CAAC;MAEF,OAAOK,MAAM,CAACC,OAAO,CAACjB,cAAc,CAAC,CAACkB,GAAG,CAAC,CAAC,CAACR,WAAW,EAAE;QAAEE,MAAM;QAAEE;MAAM,CAAE,CAAC,MAAM;QAChFrH,IAAI,EAAE,CAAC,GAAGmH,MAAM,CAAC,CAACjH,MAAM,GAAG,CAAC,GACxB,GAAG,CAAC,GAAGiH,MAAM,CAAC,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC/I,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAGwI,MAAM,CAAC,CAACQ,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,GAClE,CAAC,GAAGR,MAAM,CAAC,CAACxI,IAAI,CAAC,OAAO,CAAC;QAC7BsI,WAAW;QACX5G,KAAK,EAAE,CAAC,GAAGgH,MAAM,CAAC,CAAC,CAAC;OACrB,CAAC,CAAC;IACL;IAEA;IACAc,YAAYA,CAAA;MACV,OAAO,IAAI,CAAC5H,YAAY,CAAC6H,KAAK,IAAI,IAAI,CAAC/F,UAAU;IACnD;;uCA7nBWT,0BAA0B,EAAA1D,EAAA,CAAAyP,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA3P,EAAA,CAAAyP,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA5P,EAAA,CAAAyP,iBAAA,CAAAI,EAAA,CAAAC,eAAA,GAAA9P,EAAA,CAAAyP,iBAAA,CAAAM,EAAA,CAAAC,mBAAA,GAAAhQ,EAAA,CAAAyP,iBAAA,CAAAM,EAAA,CAAAE,aAAA,GAAAjQ,EAAA,CAAAyP,iBAAA,CAAAS,EAAA,CAAAC,WAAA;IAAA;;YAA1BzM,0BAA0B;MAAA0M,SAAA;MAAAC,SAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBAY1BhR,sBAAsB;;;;;;;;;;;;UC3DnCS,EAAA,CAAAC,cAAA,aAA0C;;UAIpCD,EAFJ,CAAAC,cAAA,aAAqD,WAC7C,wBAC4D;UAE9DD,EADA,CAAAU,SAAA,cAAyC,cACE;UAGjDV,EAFI,CAAAG,YAAA,EAAiB,EACZ,EACH;;UAYIH,EAXV,CAAAC,cAAA,aAA2C,aAOxC,aACqD,aACvB,cACA,oBAMtB;UADCD,EAAA,CAAAY,UAAA,mBAAA6P,+DAAA;YAAA,OAASD,GAAA,CAAArE,YAAA,EAAc;UAAA,EAAC;UACzBnM,EAAA,CAAAG,YAAA,EAAW;UACZH,EAAA,CAAAC,cAAA,oBAKC;UADCD,EAAA,CAAAY,UAAA,mBAAA8P,+DAAA;YAAA,OAASF,GAAA,CAAAnB,eAAA,EAAiB;UAAA,EAAC;UAGjCrP,EAFK,CAAAG,YAAA,EAAW,EACR,EACF;UAIJH,EAFF,CAAAC,cAAA,eAAiE,eAEnC;UAC1BD,EAAA,CAAAsB,UAAA,KAAAqP,mDAAA,6BAAkE;UAqCpE3Q,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAsB,UAAA,KAAAsP,0CAAA,kBAA4D;UA+B1D5Q,EADF,CAAAC,cAAA,eAA0B,SACrB;UAAAD,EAAA,CAAAE,MAAA,uDAA+C;UACpDF,EADoD,CAAAG,YAAA,EAAI,EAClD;UAIJH,EADF,CAAAC,cAAA,eAA6B,sBAY1B;UANCD,EAAA,CAAAY,UAAA,mBAAAiQ,iEAAA;YAAA,OAASL,GAAA,CAAA9F,eAAA,EAAiB;UAAA,EAAC;UAUrC1K,EAJS,CAAAG,YAAA,EAAa,EACV,EACF,EACF,EACF;UAOEH,EAJR,CAAAC,cAAA,eAA0E,eAC5C,eACK,eACV,oBAShB;UAFCD,EAAA,CAAAY,UAAA,uBAAAkQ,mEAAAnO,MAAA;YAAA,OAAa6N,GAAA,CAAA1N,WAAA,CAAAH,MAAA,CAAmB;UAAA,EAAC;UAGrC3C,EADG,CAAAG,YAAA,EAAW,EACR;UACNH,EAAA,CAAAC,cAAA,eAAwC;UACtCD,EAAA,CAAAU,SAAA,sBAYa;UASjBV,EADE,CAAAG,YAAA,EAAM,EACF;UAeNH,EAbA,CAAAsB,UAAA,KAAAyP,0CAAA,kBAAmE,KAAAC,0CAAA,kBAaF;UASzEhR,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACF;;;UAnLAH,EAAA,CAAAI,SAAA,GAAwC;UAAxCJ,EAAA,CAAA0B,WAAA,cAAA8O,GAAA,CAAArK,oBAAA,CAAwC;UAsBXnG,EAAA,CAAAI,SAAA,GAAqC;UAArCJ,EAAA,CAAA0B,WAAA,WAAA8O,GAAA,CAAArK,oBAAA,CAAqC;UAG5BnG,EAAA,CAAAI,SAAA,GAAmB;UAAnBJ,EAAA,CAAA6B,UAAA,YAAA2O,GAAA,CAAAvO,cAAA,CAAmB;UAwCzBjC,EAAA,CAAAI,SAAA,EAA8B;UAA9BJ,EAAA,CAAA6B,UAAA,SAAA2O,GAAA,CAAAlO,aAAA,CAAAN,MAAA,KAA8B;UAwCtDhC,EAAA,CAAAI,SAAA,GAA4B;UAE5BJ,EAFA,CAAA6B,UAAA,cAAA2O,GAAA,CAAAvG,YAAA,GAA4B,iBAAAjK,EAAA,CAAAiR,eAAA,KAAAC,GAAA,EAM1B;UAaFlR,EAAA,CAAAI,SAAA,GAAuB;UAIvBJ,EAJA,CAAA6B,UAAA,SAAA2O,GAAA,CAAAvM,cAAA,CAAuB,gBAAAuM,GAAA,CAAAtK,WAAA,CACI,4BAGA;UAS3BlG,EAAA,CAAAI,SAAA,GAME;UANFJ,EAAA,CAAA6B,UAAA,iBAAA7B,EAAA,CAAAiR,eAAA,KAAAE,GAAA,EAME;UAcFnR,EAAA,CAAAI,SAAA,EAAsC;UAAtCJ,EAAA,CAAA6B,UAAA,SAAA2O,GAAA,CAAAjL,WAAA,sBAAsC;UAatCvF,EAAA,CAAAI,SAAA,EAAoC;UAApCJ,EAAA,CAAA6B,UAAA,SAAA2O,GAAA,CAAAjL,WAAA,oBAAoC;;;qBDrJ9CrG,YAAY,EAAAkS,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZjS,WAAW,EAAA6Q,EAAA,CAAAqB,oBAAA,EAAArB,EAAA,CAAAsB,eAAA,EAAAtB,EAAA,CAAAuB,oBAAA,EAEXjS,sBAAsB,EACtBC,oBAAoB,EACpBG,aAAa,EACbF,eAAe,EACfC,aAAa;MAAA+R,MAAA;IAAA;;SAKJhO,0BAA0B;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}