{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { CommonModule } from '@angular/common';\nimport { Component, EventEmitter, HostListener, Input, Output, ViewChild } from '@angular/core';\nimport { FormsModule, FormControl, ReactiveFormsModule } from '@angular/forms';\nimport { ButtonComponent, DropdownComponent, IconComponent, ToggleComponent, AvaTextboxComponent } from '@ava/play-comp-library';\nlet PlaygroundComponent = class PlaygroundComponent {\n  isMenuOpen = false;\n  isToolMenuOpen = false;\n  promptChange = new EventEmitter();\n  promptOptions = [];\n  selectedValue = 'default'; // Input for pre-selected value\n  agentType = 'individual'; // Input for agent type ('individual' or 'collaborative')\n  showChatInteractionToggles = false; // Input to show conversational and template toggles\n  showAiPrincipleToggle = false; // Input to show AI principle toggle\n  showDropdown = true; // Input to control dropdown visibility\n  showAgentNameInput = false; // Input to show disabled agent name input field\n  agentNamePlaceholder = 'Agent Name'; // Placeholder for agent name input\n  displayedAgentName = ''; // Agent name to display in disabled input\n  showFileUploadButton = false; // Controls visibility of attach file button\n  selectedPrompt = 'default';\n  // Form control for agent name display\n  agentNameDisplayControl = new FormControl({\n    value: '',\n    disabled: true\n  });\n  // Chat data\n  showCopiedToast = false;\n  inputText = '';\n  previousMessagesLength = 0;\n  shouldScrollToBottom = false;\n  messages = [];\n  isLoading = false;\n  isDisabled = false;\n  showLoader = true;\n  messageSent = new EventEmitter();\n  conversationalToggle = new EventEmitter();\n  templateToggle = new EventEmitter();\n  filesSelected = new EventEmitter();\n  messagesContainer;\n  fileInput;\n  showApprovalButton = true;\n  approvalRequested = new EventEmitter();\n  isMinimalView = false;\n  // Simple toggle properties for display only\n  isConvChecked = true;\n  isUseTemplate = false;\n  // File upload properties\n  filesUploadedData = [];\n  acceptedFileType = '.pdf,.doc,.docx,.txt,.csv,.xlsx,.xls,.ppt,.pptx,.png,.jpg,.jpeg,.gif,.bmp,.svg';\n  ngOnInit() {\n    this.messages = [{\n      from: 'ai',\n      text: 'Hi there, how can I help you today?'\n    }];\n    this.shouldScrollToBottom = true;\n    // Set selected prompt from input\n    if (this.selectedValue) {\n      this.selectedPrompt = this.selectedValue;\n      // Update displayed agent name if showing agent name input\n      if (this.showAgentNameInput && !this.displayedAgentName) {\n        this.displayedAgentName = this.selectedValue;\n        this.agentNameDisplayControl.setValue(this.selectedValue);\n      }\n    }\n    // Initialize agent name display control\n    if (this.displayedAgentName) {\n      this.agentNameDisplayControl.setValue(this.displayedAgentName);\n    }\n  }\n  ngOnChanges(changes) {\n    // Update selectedPrompt when selectedValue input changes\n    if (changes['selectedValue'] && changes['selectedValue'].currentValue) {\n      // The selectedValue from parent should be the name (for dropdown display)\n      this.selectedPrompt = changes['selectedValue'].currentValue;\n    }\n    // Update agent name display control when displayedAgentName input changes\n    if (changes['displayedAgentName'] && changes['displayedAgentName'].currentValue !== undefined) {\n      this.agentNameDisplayControl.setValue(changes['displayedAgentName'].currentValue);\n    }\n  }\n  ngAfterViewChecked() {\n    if (this.shouldScrollToBottom) {\n      this.scrollToBottom();\n      this.shouldScrollToBottom = false;\n    }\n  }\n  scrollToBottom() {\n    try {\n      if (this.messagesContainer && this.messagesContainer.nativeElement) {\n        // Scroll to bottom to show latest messages\n        this.messagesContainer.nativeElement.scrollTop = this.messagesContainer.nativeElement.scrollHeight;\n      }\n    } catch (err) {\n      console.error('Error scrolling to bottom:', err);\n    }\n  }\n  handleSendMessage() {\n    if (!this.inputText.trim() || this.isDisabled) {\n      return;\n    }\n    // Add user message to the chat\n    this.messages = [...this.messages, {\n      from: 'user',\n      text: this.inputText\n    }];\n    this.shouldScrollToBottom = true;\n    // Emit the message to parent component\n    const messageText = this.inputText;\n    this.inputText = '';\n    this.messageSent.emit(messageText);\n    // Clear uploaded files after sending message\n    this.clearFiles();\n  }\n  clearFiles() {\n    this.filesUploadedData = [];\n    this.filesSelected.emit(this.filesUploadedData);\n  }\n  toggleMenu() {\n    this.isMenuOpen = !this.isMenuOpen;\n  }\n  onAiPrincipleToggle(event) {\n    console.log('AI Principles toggle:', event);\n  }\n  onConversationalToggle(event) {\n    this.isConvChecked = event;\n    // If conversational is enabled, disable template\n    if (event && this.isUseTemplate) {\n      this.isUseTemplate = false;\n      this.templateToggle.emit(false);\n    }\n    console.log('Conversational mode:', event);\n    this.conversationalToggle.emit(event);\n  }\n  onTemplateToggle(event) {\n    this.isUseTemplate = event;\n    // If template is enabled, disable conversational\n    if (event && this.isConvChecked) {\n      this.isConvChecked = false;\n      this.conversationalToggle.emit(false);\n    }\n    console.log('Use template:', event);\n    this.templateToggle.emit(event);\n  }\n  onPromptChange(selectionData) {\n    // The dropdown component emits an object with selectedOptions and selectedValue\n    // selectedValue contains the name of the selected option\n    let selectedName;\n    if (typeof selectionData === 'string') {\n      selectedName = selectionData;\n    } else if (selectionData && selectionData.selectedValue) {\n      selectedName = selectionData.selectedValue;\n    } else if (selectionData && selectionData.selectedOptions && selectionData.selectedOptions.length > 0) {\n      selectedName = selectionData.selectedOptions[0].name;\n    } else {\n      return;\n    }\n    this.selectedPrompt = selectedName;\n    // Update displayed agent name if showing agent name input\n    if (this.showAgentNameInput) {\n      this.displayedAgentName = selectedName;\n      this.agentNameDisplayControl.setValue(selectedName);\n    }\n    // Find the option by name\n    const selectedOption = this.promptOptions.find(opt => opt.name === selectedName);\n    if (selectedOption) {\n      this.promptChange.emit(selectedOption);\n    }\n  }\n  copyToClipboard(text) {\n    navigator.clipboard.writeText(text).then(() => {\n      this.showCopiedToast = true;\n      setTimeout(() => {\n        this.showCopiedToast = false;\n      }, 2000);\n    });\n  }\n  save() {\n    this.isMenuOpen = false;\n    console.log('Save clicked');\n    // your save logic here\n  }\n  export() {\n    this.isMenuOpen = false;\n    console.log('Export clicked');\n    // your export logic here\n  }\n  // Hide menu when clicking outside\n  onClickOutside(event) {\n    const target = event.target;\n    if (!target.closest('.btn-menu')) {\n      this.isMenuOpen = false;\n    }\n  }\n  onEnterKeydown(event) {\n    // Only prevent default and send if Shift key is not pressed\n    if (!event.shiftKey) {\n      event.preventDefault();\n      this.handleSendMessage();\n    }\n  }\n  // File upload methods\n  onFileSelected(event) {\n    const files = event.target.files;\n    if (files && files.length > 0) {\n      this.filesUploadedData = [];\n      for (let i = 0; i < files.length; i++) {\n        const file = files[i];\n        this.filesUploadedData.push({\n          id: `file_${Date.now()}_${i}`,\n          documentName: file.name,\n          isImage: file.type.startsWith('image/'),\n          file: file\n        });\n      }\n      console.log('Files selected:', this.filesUploadedData);\n      this.filesSelected.emit(this.filesUploadedData);\n    }\n  }\n  removeFile(index) {\n    this.filesUploadedData.splice(index, 1);\n    this.filesSelected.emit(this.filesUploadedData);\n  }\n  // Track by function for ngFor performance\n  trackByIndex(index, _item) {\n    return index;\n  }\n  onApprovalClick() {\n    this.approvalRequested.emit();\n  }\n};\n__decorate([Output()], PlaygroundComponent.prototype, \"promptChange\", void 0);\n__decorate([Input()], PlaygroundComponent.prototype, \"promptOptions\", void 0);\n__decorate([Input()], PlaygroundComponent.prototype, \"selectedValue\", void 0);\n__decorate([Input()], PlaygroundComponent.prototype, \"agentType\", void 0);\n__decorate([Input()], PlaygroundComponent.prototype, \"showChatInteractionToggles\", void 0);\n__decorate([Input()], PlaygroundComponent.prototype, \"showAiPrincipleToggle\", void 0);\n__decorate([Input()], PlaygroundComponent.prototype, \"showDropdown\", void 0);\n__decorate([Input()], PlaygroundComponent.prototype, \"showAgentNameInput\", void 0);\n__decorate([Input()], PlaygroundComponent.prototype, \"agentNamePlaceholder\", void 0);\n__decorate([Input()], PlaygroundComponent.prototype, \"displayedAgentName\", void 0);\n__decorate([Input()], PlaygroundComponent.prototype, \"showFileUploadButton\", void 0);\n__decorate([Input()], PlaygroundComponent.prototype, \"messages\", void 0);\n__decorate([Input()], PlaygroundComponent.prototype, \"isLoading\", void 0);\n__decorate([Input()], PlaygroundComponent.prototype, \"isDisabled\", void 0);\n__decorate([Input()], PlaygroundComponent.prototype, \"showLoader\", void 0);\n__decorate([Output()], PlaygroundComponent.prototype, \"messageSent\", void 0);\n__decorate([Output()], PlaygroundComponent.prototype, \"conversationalToggle\", void 0);\n__decorate([Output()], PlaygroundComponent.prototype, \"templateToggle\", void 0);\n__decorate([Output()], PlaygroundComponent.prototype, \"filesSelected\", void 0);\n__decorate([ViewChild('messagesContainer')], PlaygroundComponent.prototype, \"messagesContainer\", void 0);\n__decorate([ViewChild('fileInput')], PlaygroundComponent.prototype, \"fileInput\", void 0);\n__decorate([Input()], PlaygroundComponent.prototype, \"showApprovalButton\", void 0);\n__decorate([Output()], PlaygroundComponent.prototype, \"approvalRequested\", void 0);\n__decorate([Input()], PlaygroundComponent.prototype, \"isMinimalView\", void 0);\n__decorate([Input()], PlaygroundComponent.prototype, \"acceptedFileType\", void 0);\n__decorate([HostListener('document:click', ['$event'])], PlaygroundComponent.prototype, \"onClickOutside\", null);\n__decorate([HostListener('keydown.enter', ['$event'])], PlaygroundComponent.prototype, \"onEnterKeydown\", null);\nPlaygroundComponent = __decorate([Component({\n  selector: 'app-playground',\n  standalone: true,\n  imports: [CommonModule, DropdownComponent, FormsModule, ReactiveFormsModule, ButtonComponent, ToggleComponent, IconComponent, DropdownComponent, AvaTextboxComponent],\n  templateUrl: './playground.component.html',\n  styleUrl: './playground.component.scss'\n})], PlaygroundComponent);\nexport { PlaygroundComponent };", "map": {"version": 3, "names": ["CommonModule", "Component", "EventEmitter", "HostListener", "Input", "Output", "ViewChild", "FormsModule", "FormControl", "ReactiveFormsModule", "ButtonComponent", "DropdownComponent", "IconComponent", "ToggleComponent", "AvaTextboxComponent", "PlaygroundComponent", "isMenuOpen", "isToolMenuOpen", "prompt<PERSON><PERSON>e", "promptOptions", "selected<PERSON><PERSON><PERSON>", "agentType", "showChatInteractionToggles", "showAiPrincipleToggle", "showDropdown", "showAgentNameInput", "agentNamePlaceholder", "displayedAgentName", "showFileUploadButton", "selected<PERSON><PERSON><PERSON>", "agentNameDisplayControl", "value", "disabled", "showCopiedToast", "inputText", "previousMessages<PERSON><PERSON><PERSON>", "shouldScrollToBottom", "messages", "isLoading", "isDisabled", "<PERSON><PERSON><PERSON><PERSON>", "messageSent", "conversationalToggle", "templateToggle", "filesSelected", "messagesContainer", "fileInput", "showApprovalButton", "approvalRequested", "isMinimalView", "isConvChecked", "isUseTemplate", "filesUploadedData", "acceptedFileType", "ngOnInit", "from", "text", "setValue", "ngOnChanges", "changes", "currentValue", "undefined", "ngAfterViewChecked", "scrollToBottom", "nativeElement", "scrollTop", "scrollHeight", "err", "console", "error", "handleSendMessage", "trim", "messageText", "emit", "clearFiles", "toggleMenu", "onAiPrincipleToggle", "event", "log", "onConversationalToggle", "onTemplateToggle", "onPromptChange", "selectionData", "<PERSON><PERSON><PERSON>", "selectedOptions", "length", "name", "selectedOption", "find", "opt", "copyToClipboard", "navigator", "clipboard", "writeText", "then", "setTimeout", "save", "export", "onClickOutside", "target", "closest", "onEnterKeydown", "shift<PERSON>ey", "preventDefault", "onFileSelected", "files", "i", "file", "push", "id", "Date", "now", "documentName", "isImage", "type", "startsWith", "removeFile", "index", "splice", "trackByIndex", "_item", "onApprovalClick", "__decorate", "selector", "standalone", "imports", "templateUrl", "styleUrl"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\components\\playground\\playground.component.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\r\nimport {\r\n  AfterViewChecked,\r\n  Component,\r\n  ElementRef,\r\n  EventEmitter,\r\n  HostListener,\r\n  Input,\r\n  OnChanges,\r\n  OnInit,\r\n  Output,\r\n  ViewChild\r\n} from '@angular/core';\r\nimport { ChatMessage } from '../chat-window';\r\nimport { FormsModule, FormControl, ReactiveFormsModule } from '@angular/forms';\r\nimport {\r\n  ButtonComponent,\r\n  DropdownComponent,\r\n  IconComponent,\r\n  ToggleComponent,\r\n  DropdownOption,\r\n  AvaTextboxComponent,\r\n} from '@ava/play-comp-library';\r\n\r\ninterface Tool {\r\n  id: number;\r\n  name: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-playground',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    DropdownComponent,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    ButtonComponent,\r\n    ToggleComponent,\r\n    IconComponent,\r\n    DropdownComponent,\r\n    AvaTextboxComponent,\r\n  ],\r\n  templateUrl: './playground.component.html',\r\n  styleUrl: './playground.component.scss',\r\n})\r\nexport class PlaygroundComponent implements OnInit, OnChanges, AfterViewChecked {\r\n  isMenuOpen = false;\r\n  isToolMenuOpen = false;\r\n  @Output() promptChange = new EventEmitter<DropdownOption>();\r\n  @Input() promptOptions: DropdownOption[] = [];\r\n  @Input() selectedValue: string = 'default'; // Input for pre-selected value\r\n  @Input() agentType: string = 'individual'; // Input for agent type ('individual' or 'collaborative')\r\n  @Input() showChatInteractionToggles: boolean = false; // Input to show conversational and template toggles\r\n  @Input() showAiPrincipleToggle: boolean = false; // Input to show AI principle toggle\r\n  @Input() showDropdown: boolean = true; // Input to control dropdown visibility\r\n  @Input() showAgentNameInput: boolean = false; // Input to show disabled agent name input field\r\n  @Input() agentNamePlaceholder: string = 'Agent Name'; // Placeholder for agent name input\r\n  @Input() displayedAgentName: string = ''; // Agent name to display in disabled input\r\n  @Input() showFileUploadButton: boolean = false; // Controls visibility of attach file button\r\n  selectedPrompt: string = 'default';\r\n\r\n  // Form control for agent name display\r\n  agentNameDisplayControl = new FormControl({ value: '', disabled: true });\r\n\r\n  // Chat data\r\n  showCopiedToast = false;\r\n  inputText: string = '';\r\n  previousMessagesLength = 0;\r\n  shouldScrollToBottom = false;\r\n  @Input() messages: ChatMessage[] = [];\r\n  @Input() isLoading: boolean = false;\r\n  @Input() isDisabled: boolean = false;\r\n  @Input() showLoader: boolean = true;\r\n  @Output() messageSent = new EventEmitter<string>();\r\n  @Output() conversationalToggle = new EventEmitter<boolean>();\r\n  @Output() templateToggle = new EventEmitter<boolean>();\r\n  @Output() filesSelected = new EventEmitter<any[]>();\r\n  @ViewChild('messagesContainer') messagesContainer!: ElementRef;\r\n  @ViewChild('fileInput') fileInput!: ElementRef;\r\n  @Input() showApprovalButton: boolean = true;\r\n  @Output() approvalRequested = new EventEmitter<void>();\r\n  @Input() isMinimalView: boolean = false;\r\n\r\n  // Simple toggle properties for display only\r\n  public isConvChecked: boolean = true;\r\n  public isUseTemplate: boolean = false;\r\n\r\n  // File upload properties\r\n  public filesUploadedData: any[] = [];\r\n  @Input() acceptedFileType: string = '.pdf,.doc,.docx,.txt,.csv,.xlsx,.xls,.ppt,.pptx,.png,.jpg,.jpeg,.gif,.bmp,.svg';\r\n\r\n  ngOnInit(): void {\r\n    this.messages = [\r\n      {\r\n        from: 'ai',\r\n        text: 'Hi there, how can I help you today?',\r\n      },\r\n    ];\r\n    this.shouldScrollToBottom = true;\r\n\r\n    // Set selected prompt from input\r\n    if (this.selectedValue) {\r\n      this.selectedPrompt = this.selectedValue;\r\n      // Update displayed agent name if showing agent name input\r\n      if (this.showAgentNameInput && !this.displayedAgentName) {\r\n        this.displayedAgentName = this.selectedValue;\r\n        this.agentNameDisplayControl.setValue(this.selectedValue);\r\n      }\r\n    }\r\n\r\n    // Initialize agent name display control\r\n    if (this.displayedAgentName) {\r\n      this.agentNameDisplayControl.setValue(this.displayedAgentName);\r\n    }\r\n  }\r\n\r\n  ngOnChanges(changes: any): void {\r\n    // Update selectedPrompt when selectedValue input changes\r\n    if (changes['selectedValue'] && changes['selectedValue'].currentValue) {\r\n      // The selectedValue from parent should be the name (for dropdown display)\r\n      this.selectedPrompt = changes['selectedValue'].currentValue;\r\n    }\r\n\r\n    // Update agent name display control when displayedAgentName input changes\r\n    if (changes['displayedAgentName'] && changes['displayedAgentName'].currentValue !== undefined) {\r\n      this.agentNameDisplayControl.setValue(changes['displayedAgentName'].currentValue);\r\n    }\r\n  }\r\n\r\n  ngAfterViewChecked() {\r\n    if (this.shouldScrollToBottom) {\r\n      this.scrollToBottom();\r\n      this.shouldScrollToBottom = false;\r\n    }\r\n  }\r\n\r\n  scrollToBottom(): void {\r\n    try {\r\n      if (this.messagesContainer && this.messagesContainer.nativeElement) {\r\n        // Scroll to bottom to show latest messages\r\n        this.messagesContainer.nativeElement.scrollTop =\r\n          this.messagesContainer.nativeElement.scrollHeight;\r\n      }\r\n    } catch (err) {\r\n      console.error('Error scrolling to bottom:', err);\r\n    }\r\n  }\r\n\r\n  handleSendMessage(): void {\r\n    if (!this.inputText.trim() || this.isDisabled) {\r\n      return;\r\n    }\r\n\r\n    // Add user message to the chat\r\n    this.messages = [\r\n      ...this.messages,\r\n      {\r\n        from: 'user',\r\n        text: this.inputText,\r\n      },\r\n    ];\r\n    this.shouldScrollToBottom = true;\r\n\r\n    // Emit the message to parent component\r\n    const messageText = this.inputText;\r\n    this.inputText = '';\r\n    this.messageSent.emit(messageText);\r\n\r\n    // Clear uploaded files after sending message\r\n    this.clearFiles();\r\n  }\r\n\r\n  private clearFiles(): void {\r\n    this.filesUploadedData = [];\r\n    this.filesSelected.emit(this.filesUploadedData);\r\n  }\r\n\r\n  toggleMenu() {\r\n    this.isMenuOpen = !this.isMenuOpen;\r\n  }\r\n\r\n  onAiPrincipleToggle(event: any) {\r\n    console.log('AI Principles toggle:', event);\r\n  }\r\n\r\n  onConversationalToggle(event: any) {\r\n    this.isConvChecked = event;\r\n\r\n    // If conversational is enabled, disable template\r\n    if (event && this.isUseTemplate) {\r\n      this.isUseTemplate = false;\r\n      this.templateToggle.emit(false);\r\n    }\r\n\r\n    console.log('Conversational mode:', event);\r\n    this.conversationalToggle.emit(event);\r\n  }\r\n\r\n  onTemplateToggle(event: any) {\r\n    this.isUseTemplate = event;\r\n\r\n    // If template is enabled, disable conversational\r\n    if (event && this.isConvChecked) {\r\n      this.isConvChecked = false;\r\n      this.conversationalToggle.emit(false);\r\n    }\r\n\r\n    console.log('Use template:', event);\r\n    this.templateToggle.emit(event);\r\n  }\r\n  \r\n  onPromptChange(selectionData: any): void {\r\n    // The dropdown component emits an object with selectedOptions and selectedValue\r\n    // selectedValue contains the name of the selected option\r\n    let selectedName: string;\r\n\r\n    if (typeof selectionData === 'string') {\r\n      selectedName = selectionData;\r\n    } else if (selectionData && selectionData.selectedValue) {\r\n      selectedName = selectionData.selectedValue;\r\n    } else if (selectionData && selectionData.selectedOptions && selectionData.selectedOptions.length > 0) {\r\n      selectedName = selectionData.selectedOptions[0].name;\r\n    } else {\r\n      return;\r\n    }\r\n\r\n    this.selectedPrompt = selectedName;\r\n\r\n    // Update displayed agent name if showing agent name input\r\n    if (this.showAgentNameInput) {\r\n      this.displayedAgentName = selectedName;\r\n      this.agentNameDisplayControl.setValue(selectedName);\r\n    }\r\n\r\n    // Find the option by name\r\n    const selectedOption = this.promptOptions.find(\r\n      (opt) => opt.name === selectedName,\r\n    );\r\n\r\n    if (selectedOption) {\r\n      this.promptChange.emit(selectedOption);\r\n    }\r\n  }\r\n\r\n  copyToClipboard(text: string): void {\r\n  navigator.clipboard.writeText(text).then(() => {\r\n    this.showCopiedToast = true;\r\n    setTimeout(() => {\r\n      this.showCopiedToast = false;\r\n    }, 2000);\r\n  });\r\n}\r\n  save() {\r\n    this.isMenuOpen = false;\r\n    console.log('Save clicked');\r\n    // your save logic here\r\n  }\r\n\r\n  export() {\r\n    this.isMenuOpen = false;\r\n    console.log('Export clicked');\r\n    // your export logic here\r\n  }\r\n\r\n  // Hide menu when clicking outside\r\n  @HostListener('document:click', ['$event'])\r\n  onClickOutside(event: Event) {\r\n    const target = event.target as HTMLElement;\r\n    if (!target.closest('.btn-menu')) {\r\n      this.isMenuOpen = false;\r\n    }\r\n  }\r\n\r\n  @HostListener('keydown.enter', ['$event'])\r\n  onEnterKeydown(event: KeyboardEvent): void {\r\n    // Only prevent default and send if Shift key is not pressed\r\n    if (!event.shiftKey) {\r\n      event.preventDefault();\r\n      this.handleSendMessage();\r\n    }\r\n  }\r\n\r\n  // File upload methods\r\n  onFileSelected(event: any): void {\r\n    const files = event.target.files;\r\n    if (files && files.length > 0) {\r\n      this.filesUploadedData = [];\r\n\r\n      for (let i = 0; i < files.length; i++) {\r\n        const file = files[i];\r\n        this.filesUploadedData.push({\r\n          id: `file_${Date.now()}_${i}`,\r\n          documentName: file.name,\r\n          isImage: file.type.startsWith('image/'),\r\n          file: file\r\n        });\r\n      }\r\n\r\n      console.log('Files selected:', this.filesUploadedData);\r\n      this.filesSelected.emit(this.filesUploadedData);\r\n    }\r\n  }\r\n\r\n  removeFile(index: number): void {\r\n    this.filesUploadedData.splice(index, 1);\r\n    this.filesSelected.emit(this.filesUploadedData);\r\n  }\r\n\r\n  // Track by function for ngFor performance\r\n  trackByIndex(index: number, _item: any): number {\r\n    return index;\r\n  }\r\n\r\n  onApprovalClick() {\r\n    this.approvalRequested.emit();\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAEEC,SAAS,EAETC,YAAY,EACZC,YAAY,EACZC,KAAK,EAGLC,MAAM,EACNC,SAAS,QACJ,eAAe;AAEtB,SAASC,WAAW,EAAEC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AAC9E,SACEC,eAAe,EACfC,iBAAiB,EACjBC,aAAa,EACbC,eAAe,EAEfC,mBAAmB,QACd,wBAAwB;AAwBxB,IAAMC,mBAAmB,GAAzB,MAAMA,mBAAmB;EAC9BC,UAAU,GAAG,KAAK;EAClBC,cAAc,GAAG,KAAK;EACZC,YAAY,GAAG,IAAIhB,YAAY,EAAkB;EAClDiB,aAAa,GAAqB,EAAE;EACpCC,aAAa,GAAW,SAAS,CAAC,CAAC;EACnCC,SAAS,GAAW,YAAY,CAAC,CAAC;EAClCC,0BAA0B,GAAY,KAAK,CAAC,CAAC;EAC7CC,qBAAqB,GAAY,KAAK,CAAC,CAAC;EACxCC,YAAY,GAAY,IAAI,CAAC,CAAC;EAC9BC,kBAAkB,GAAY,KAAK,CAAC,CAAC;EACrCC,oBAAoB,GAAW,YAAY,CAAC,CAAC;EAC7CC,kBAAkB,GAAW,EAAE,CAAC,CAAC;EACjCC,oBAAoB,GAAY,KAAK,CAAC,CAAC;EAChDC,cAAc,GAAW,SAAS;EAElC;EACAC,uBAAuB,GAAG,IAAItB,WAAW,CAAC;IAAEuB,KAAK,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAI,CAAE,CAAC;EAExE;EACAC,eAAe,GAAG,KAAK;EACvBC,SAAS,GAAW,EAAE;EACtBC,sBAAsB,GAAG,CAAC;EAC1BC,oBAAoB,GAAG,KAAK;EACnBC,QAAQ,GAAkB,EAAE;EAC5BC,SAAS,GAAY,KAAK;EAC1BC,UAAU,GAAY,KAAK;EAC3BC,UAAU,GAAY,IAAI;EACzBC,WAAW,GAAG,IAAIvC,YAAY,EAAU;EACxCwC,oBAAoB,GAAG,IAAIxC,YAAY,EAAW;EAClDyC,cAAc,GAAG,IAAIzC,YAAY,EAAW;EAC5C0C,aAAa,GAAG,IAAI1C,YAAY,EAAS;EACnB2C,iBAAiB;EACzBC,SAAS;EACxBC,kBAAkB,GAAY,IAAI;EACjCC,iBAAiB,GAAG,IAAI9C,YAAY,EAAQ;EAC7C+C,aAAa,GAAY,KAAK;EAEvC;EACOC,aAAa,GAAY,IAAI;EAC7BC,aAAa,GAAY,KAAK;EAErC;EACOC,iBAAiB,GAAU,EAAE;EAC3BC,gBAAgB,GAAW,gFAAgF;EAEpHC,QAAQA,CAAA;IACN,IAAI,CAACjB,QAAQ,GAAG,CACd;MACEkB,IAAI,EAAE,IAAI;MACVC,IAAI,EAAE;KACP,CACF;IACD,IAAI,CAACpB,oBAAoB,GAAG,IAAI;IAEhC;IACA,IAAI,IAAI,CAAChB,aAAa,EAAE;MACtB,IAAI,CAACS,cAAc,GAAG,IAAI,CAACT,aAAa;MACxC;MACA,IAAI,IAAI,CAACK,kBAAkB,IAAI,CAAC,IAAI,CAACE,kBAAkB,EAAE;QACvD,IAAI,CAACA,kBAAkB,GAAG,IAAI,CAACP,aAAa;QAC5C,IAAI,CAACU,uBAAuB,CAAC2B,QAAQ,CAAC,IAAI,CAACrC,aAAa,CAAC;MAC3D;IACF;IAEA;IACA,IAAI,IAAI,CAACO,kBAAkB,EAAE;MAC3B,IAAI,CAACG,uBAAuB,CAAC2B,QAAQ,CAAC,IAAI,CAAC9B,kBAAkB,CAAC;IAChE;EACF;EAEA+B,WAAWA,CAACC,OAAY;IACtB;IACA,IAAIA,OAAO,CAAC,eAAe,CAAC,IAAIA,OAAO,CAAC,eAAe,CAAC,CAACC,YAAY,EAAE;MACrE;MACA,IAAI,CAAC/B,cAAc,GAAG8B,OAAO,CAAC,eAAe,CAAC,CAACC,YAAY;IAC7D;IAEA;IACA,IAAID,OAAO,CAAC,oBAAoB,CAAC,IAAIA,OAAO,CAAC,oBAAoB,CAAC,CAACC,YAAY,KAAKC,SAAS,EAAE;MAC7F,IAAI,CAAC/B,uBAAuB,CAAC2B,QAAQ,CAACE,OAAO,CAAC,oBAAoB,CAAC,CAACC,YAAY,CAAC;IACnF;EACF;EAEAE,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAAC1B,oBAAoB,EAAE;MAC7B,IAAI,CAAC2B,cAAc,EAAE;MACrB,IAAI,CAAC3B,oBAAoB,GAAG,KAAK;IACnC;EACF;EAEA2B,cAAcA,CAAA;IACZ,IAAI;MACF,IAAI,IAAI,CAAClB,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,CAACmB,aAAa,EAAE;QAClE;QACA,IAAI,CAACnB,iBAAiB,CAACmB,aAAa,CAACC,SAAS,GAC5C,IAAI,CAACpB,iBAAiB,CAACmB,aAAa,CAACE,YAAY;MACrD;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAACC,KAAK,CAAC,4BAA4B,EAAEF,GAAG,CAAC;IAClD;EACF;EAEAG,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACpC,SAAS,CAACqC,IAAI,EAAE,IAAI,IAAI,CAAChC,UAAU,EAAE;MAC7C;IACF;IAEA;IACA,IAAI,CAACF,QAAQ,GAAG,CACd,GAAG,IAAI,CAACA,QAAQ,EAChB;MACEkB,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE,IAAI,CAACtB;KACZ,CACF;IACD,IAAI,CAACE,oBAAoB,GAAG,IAAI;IAEhC;IACA,MAAMoC,WAAW,GAAG,IAAI,CAACtC,SAAS;IAClC,IAAI,CAACA,SAAS,GAAG,EAAE;IACnB,IAAI,CAACO,WAAW,CAACgC,IAAI,CAACD,WAAW,CAAC;IAElC;IACA,IAAI,CAACE,UAAU,EAAE;EACnB;EAEQA,UAAUA,CAAA;IAChB,IAAI,CAACtB,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACR,aAAa,CAAC6B,IAAI,CAAC,IAAI,CAACrB,iBAAiB,CAAC;EACjD;EAEAuB,UAAUA,CAAA;IACR,IAAI,CAAC3D,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;EACpC;EAEA4D,mBAAmBA,CAACC,KAAU;IAC5BT,OAAO,CAACU,GAAG,CAAC,uBAAuB,EAAED,KAAK,CAAC;EAC7C;EAEAE,sBAAsBA,CAACF,KAAU;IAC/B,IAAI,CAAC3B,aAAa,GAAG2B,KAAK;IAE1B;IACA,IAAIA,KAAK,IAAI,IAAI,CAAC1B,aAAa,EAAE;MAC/B,IAAI,CAACA,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACR,cAAc,CAAC8B,IAAI,CAAC,KAAK,CAAC;IACjC;IAEAL,OAAO,CAACU,GAAG,CAAC,sBAAsB,EAAED,KAAK,CAAC;IAC1C,IAAI,CAACnC,oBAAoB,CAAC+B,IAAI,CAACI,KAAK,CAAC;EACvC;EAEAG,gBAAgBA,CAACH,KAAU;IACzB,IAAI,CAAC1B,aAAa,GAAG0B,KAAK;IAE1B;IACA,IAAIA,KAAK,IAAI,IAAI,CAAC3B,aAAa,EAAE;MAC/B,IAAI,CAACA,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACR,oBAAoB,CAAC+B,IAAI,CAAC,KAAK,CAAC;IACvC;IAEAL,OAAO,CAACU,GAAG,CAAC,eAAe,EAAED,KAAK,CAAC;IACnC,IAAI,CAAClC,cAAc,CAAC8B,IAAI,CAACI,KAAK,CAAC;EACjC;EAEAI,cAAcA,CAACC,aAAkB;IAC/B;IACA;IACA,IAAIC,YAAoB;IAExB,IAAI,OAAOD,aAAa,KAAK,QAAQ,EAAE;MACrCC,YAAY,GAAGD,aAAa;IAC9B,CAAC,MAAM,IAAIA,aAAa,IAAIA,aAAa,CAAC9D,aAAa,EAAE;MACvD+D,YAAY,GAAGD,aAAa,CAAC9D,aAAa;IAC5C,CAAC,MAAM,IAAI8D,aAAa,IAAIA,aAAa,CAACE,eAAe,IAAIF,aAAa,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;MACrGF,YAAY,GAAGD,aAAa,CAACE,eAAe,CAAC,CAAC,CAAC,CAACE,IAAI;IACtD,CAAC,MAAM;MACL;IACF;IAEA,IAAI,CAACzD,cAAc,GAAGsD,YAAY;IAElC;IACA,IAAI,IAAI,CAAC1D,kBAAkB,EAAE;MAC3B,IAAI,CAACE,kBAAkB,GAAGwD,YAAY;MACtC,IAAI,CAACrD,uBAAuB,CAAC2B,QAAQ,CAAC0B,YAAY,CAAC;IACrD;IAEA;IACA,MAAMI,cAAc,GAAG,IAAI,CAACpE,aAAa,CAACqE,IAAI,CAC3CC,GAAG,IAAKA,GAAG,CAACH,IAAI,KAAKH,YAAY,CACnC;IAED,IAAII,cAAc,EAAE;MAClB,IAAI,CAACrE,YAAY,CAACuD,IAAI,CAACc,cAAc,CAAC;IACxC;EACF;EAEAG,eAAeA,CAAClC,IAAY;IAC5BmC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACrC,IAAI,CAAC,CAACsC,IAAI,CAAC,MAAK;MAC5C,IAAI,CAAC7D,eAAe,GAAG,IAAI;MAC3B8D,UAAU,CAAC,MAAK;QACd,IAAI,CAAC9D,eAAe,GAAG,KAAK;MAC9B,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC;EACJ;EACE+D,IAAIA,CAAA;IACF,IAAI,CAAChF,UAAU,GAAG,KAAK;IACvBoD,OAAO,CAACU,GAAG,CAAC,cAAc,CAAC;IAC3B;EACF;EAEAmB,MAAMA,CAAA;IACJ,IAAI,CAACjF,UAAU,GAAG,KAAK;IACvBoD,OAAO,CAACU,GAAG,CAAC,gBAAgB,CAAC;IAC7B;EACF;EAEA;EAEAoB,cAAcA,CAACrB,KAAY;IACzB,MAAMsB,MAAM,GAAGtB,KAAK,CAACsB,MAAqB;IAC1C,IAAI,CAACA,MAAM,CAACC,OAAO,CAAC,WAAW,CAAC,EAAE;MAChC,IAAI,CAACpF,UAAU,GAAG,KAAK;IACzB;EACF;EAGAqF,cAAcA,CAACxB,KAAoB;IACjC;IACA,IAAI,CAACA,KAAK,CAACyB,QAAQ,EAAE;MACnBzB,KAAK,CAAC0B,cAAc,EAAE;MACtB,IAAI,CAACjC,iBAAiB,EAAE;IAC1B;EACF;EAEA;EACAkC,cAAcA,CAAC3B,KAAU;IACvB,MAAM4B,KAAK,GAAG5B,KAAK,CAACsB,MAAM,CAACM,KAAK;IAChC,IAAIA,KAAK,IAAIA,KAAK,CAACpB,MAAM,GAAG,CAAC,EAAE;MAC7B,IAAI,CAACjC,iBAAiB,GAAG,EAAE;MAE3B,KAAK,IAAIsD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACpB,MAAM,EAAEqB,CAAC,EAAE,EAAE;QACrC,MAAMC,IAAI,GAAGF,KAAK,CAACC,CAAC,CAAC;QACrB,IAAI,CAACtD,iBAAiB,CAACwD,IAAI,CAAC;UAC1BC,EAAE,EAAE,QAAQC,IAAI,CAACC,GAAG,EAAE,IAAIL,CAAC,EAAE;UAC7BM,YAAY,EAAEL,IAAI,CAACrB,IAAI;UACvB2B,OAAO,EAAEN,IAAI,CAACO,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC;UACvCR,IAAI,EAAEA;SACP,CAAC;MACJ;MAEAvC,OAAO,CAACU,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC1B,iBAAiB,CAAC;MACtD,IAAI,CAACR,aAAa,CAAC6B,IAAI,CAAC,IAAI,CAACrB,iBAAiB,CAAC;IACjD;EACF;EAEAgE,UAAUA,CAACC,KAAa;IACtB,IAAI,CAACjE,iBAAiB,CAACkE,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;IACvC,IAAI,CAACzE,aAAa,CAAC6B,IAAI,CAAC,IAAI,CAACrB,iBAAiB,CAAC;EACjD;EAEA;EACAmE,YAAYA,CAACF,KAAa,EAAEG,KAAU;IACpC,OAAOH,KAAK;EACd;EAEAI,eAAeA,CAAA;IACb,IAAI,CAACzE,iBAAiB,CAACyB,IAAI,EAAE;EAC/B;CACD;AA5QWiD,UAAA,EAATrH,MAAM,EAAE,C,wDAAmD;AACnDqH,UAAA,EAARtH,KAAK,EAAE,C,yDAAsC;AACrCsH,UAAA,EAARtH,KAAK,EAAE,C,yDAAmC;AAClCsH,UAAA,EAARtH,KAAK,EAAE,C,qDAAkC;AACjCsH,UAAA,EAARtH,KAAK,EAAE,C,sEAA6C;AAC5CsH,UAAA,EAARtH,KAAK,EAAE,C,iEAAwC;AACvCsH,UAAA,EAARtH,KAAK,EAAE,C,wDAA8B;AAC7BsH,UAAA,EAARtH,KAAK,EAAE,C,8DAAqC;AACpCsH,UAAA,EAARtH,KAAK,EAAE,C,gEAA6C;AAC5CsH,UAAA,EAARtH,KAAK,EAAE,C,8DAAiC;AAChCsH,UAAA,EAARtH,KAAK,EAAE,C,gEAAuC;AAWtCsH,UAAA,EAARtH,KAAK,EAAE,C,oDAA8B;AAC7BsH,UAAA,EAARtH,KAAK,EAAE,C,qDAA4B;AAC3BsH,UAAA,EAARtH,KAAK,EAAE,C,sDAA6B;AAC5BsH,UAAA,EAARtH,KAAK,EAAE,C,sDAA4B;AAC1BsH,UAAA,EAATrH,MAAM,EAAE,C,uDAA0C;AACzCqH,UAAA,EAATrH,MAAM,EAAE,C,gEAAoD;AACnDqH,UAAA,EAATrH,MAAM,EAAE,C,0DAA8C;AAC7CqH,UAAA,EAATrH,MAAM,EAAE,C,yDAA2C;AACpBqH,UAAA,EAA/BpH,SAAS,CAAC,mBAAmB,CAAC,C,6DAAgC;AACvCoH,UAAA,EAAvBpH,SAAS,CAAC,WAAW,CAAC,C,qDAAwB;AACtCoH,UAAA,EAARtH,KAAK,EAAE,C,8DAAoC;AAClCsH,UAAA,EAATrH,MAAM,EAAE,C,6DAA8C;AAC9CqH,UAAA,EAARtH,KAAK,EAAE,C,yDAAgC;AAQ/BsH,UAAA,EAARtH,KAAK,EAAE,C,4DAA6G;AAiLrHsH,UAAA,EADCvH,YAAY,CAAC,gBAAgB,EAAE,CAAC,QAAQ,CAAC,CAAC,C,wDAM1C;AAGDuH,UAAA,EADCvH,YAAY,CAAC,eAAe,EAAE,CAAC,QAAQ,CAAC,CAAC,C,wDAOzC;AA3OUY,mBAAmB,GAAA2G,UAAA,EAjB/BzH,SAAS,CAAC;EACT0H,QAAQ,EAAE,gBAAgB;EAC1BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACP7H,YAAY,EACZW,iBAAiB,EACjBJ,WAAW,EACXE,mBAAmB,EACnBC,eAAe,EACfG,eAAe,EACfD,aAAa,EACbD,iBAAiB,EACjBG,mBAAmB,CACpB;EACDgH,WAAW,EAAE,6BAA6B;EAC1CC,QAAQ,EAAE;CACX,CAAC,C,EACWhH,mBAAmB,CA+Q/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}