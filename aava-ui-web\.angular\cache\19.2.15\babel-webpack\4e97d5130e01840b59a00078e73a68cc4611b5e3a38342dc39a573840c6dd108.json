{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { Subject, takeUntil } from 'rxjs';\nimport { FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';\n// Import child components\nimport { ChatInterfaceComponent } from '@shared/components/chat-interface/chat-interface.component';\nimport { AgentActivityComponent } from './components/agent-activity/agent-activity.component';\nimport { AgentOutputComponent } from './components/agent-output/agent-output.component';\nimport { AvaStepperComponent, ButtonComponent, IconComponent, TabsComponent } from '@ava/play-comp-library';\nimport { environment } from '@shared/environments/environment';\nimport workflowConstants from './../constants/workflows.json';\nimport { ExecutionStatus } from '@shared/models/execution.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@shared/services/workflow.service\";\nimport * as i3 from \"@shared/index\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/common\";\nconst _c0 = () => ({\n  \"background\": \"#1A46A7\",\n  \"width\": \"100%\",\n  \"border-radius\": \"8px\"\n});\nconst _c1 = () => ({\n  background: \"linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)\",\n  \"--button-effect-color\": \"33, 90, 214\",\n  \"border-radius\": \"8px\",\n  \"box-shadow\": \"none\"\n});\nfunction WorkflowExecutionComponent_ng_container_17_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"span\", 40);\n    i0.ɵɵtext(2, \"Model:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 41);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const agent_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(agent_r4.agent.llm.modelDeploymentName);\n  }\n}\nfunction WorkflowExecutionComponent_ng_container_17_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"span\", 40);\n    i0.ɵɵtext(2, \"Tools:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 41);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const agent_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.getCombinedTools(agent_r4.agent));\n  }\n}\nfunction WorkflowExecutionComponent_ng_container_17_div_12_ng_container_2_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵelement(1, \"textarea\", 50);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const input_r5 = i0.ɵɵnextContext().$implicit;\n    const i_r2 = i0.ɵɵnextContext(2).index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formControlName\", input_r5.input)(\"placeholder\", \"Enter \" + input_r5.label)(\"disabled\", ctx_r2.currentStepperStep !== i_r2);\n  }\n}\nfunction WorkflowExecutionComponent_ng_container_17_div_12_ng_container_2_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"div\", 52);\n    i0.ɵɵlistener(\"click\", function WorkflowExecutionComponent_ng_container_17_div_12_ng_container_2_div_5_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const input_r5 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.triggerFileInput(input_r5.input));\n    });\n    i0.ɵɵelement(2, \"ava-icon\", 53);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Upload Image\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"input\", 54);\n    i0.ɵɵlistener(\"change\", function WorkflowExecutionComponent_ng_container_17_div_12_ng_container_2_div_5_Template_input_change_5_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const input_r5 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onImageSelected($event, input_r5.input));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const input_r5 = i0.ɵɵnextContext().$implicit;\n    const i_r2 = i0.ɵɵnextContext(2).index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", ctx_r2.currentStepperStep !== i_r2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"id\", \"file-\" + input_r5.input);\n  }\n}\nfunction WorkflowExecutionComponent_ng_container_17_div_12_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 45)(2, \"label\", 46);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, WorkflowExecutionComponent_ng_container_17_div_12_ng_container_2_div_4_Template, 2, 3, \"div\", 47)(5, WorkflowExecutionComponent_ng_container_17_div_12_ng_container_2_div_5_Template, 6, 3, \"div\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const input_r5 = ctx.$implicit;\n    const inputIndex_r7 = ctx.index;\n    const i_r2 = i0.ɵɵnextContext(2).index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"hidden\", inputIndex_r7 > 1 && !ctx_r2.showAllInputs[i_r2]);\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.workflowForm);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(input_r5.label);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isImageInput(input_r5.input));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isImageInput(input_r5.input));\n  }\n}\nfunction WorkflowExecutionComponent_ng_container_17_div_12_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function WorkflowExecutionComponent_ng_container_17_div_12_div_3_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const i_r2 = i0.ɵɵnextContext(2).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleShowAllInputs(i_r2));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const i_r2 = i0.ɵɵnextContext(2).index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.showAllInputs[i_r2] ? \"Show Less\" : \"Show More (\" + (ctx_r2.getAgentInputs(i_r2).length - 2) + \" more)\", \" \");\n  }\n}\nfunction WorkflowExecutionComponent_ng_container_17_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"div\", 43);\n    i0.ɵɵtemplate(2, WorkflowExecutionComponent_ng_container_17_div_12_ng_container_2_Template, 6, 6, \"ng-container\", 16)(3, WorkflowExecutionComponent_ng_container_17_div_12_div_3_Template, 3, 1, \"div\", 44);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const i_r2 = i0.ɵɵnextContext().index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"scrollable\", ctx_r2.getAgentInputs(i_r2).length > 2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getAgentInputs(i_r2));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getAgentInputs(i_r2).length > 2);\n  }\n}\nfunction WorkflowExecutionComponent_ng_container_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 29)(2, \"div\", 30);\n    i0.ɵɵlistener(\"click\", function WorkflowExecutionComponent_ng_container_17_Template_div_click_2_listener() {\n      const i_r2 = i0.ɵɵrestoreView(_r1).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleAgentExpansion(i_r2));\n    });\n    i0.ɵɵelementStart(3, \"div\", 31);\n    i0.ɵɵelement(4, \"ava-icon\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 33)(6, \"h3\", 34);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"ava-icon\", 35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 36);\n    i0.ɵɵtemplate(10, WorkflowExecutionComponent_ng_container_17_div_10_Template, 5, 1, \"div\", 37)(11, WorkflowExecutionComponent_ng_container_17_div_11_Template, 5, 1, \"div\", 37)(12, WorkflowExecutionComponent_ng_container_17_div_12_Template, 4, 4, \"div\", 38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const agent_r4 = ctx.$implicit;\n    const i_r2 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"completed\", ctx_r2.agentStatuses[i_r2] == null ? null : ctx_r2.agentStatuses[i_r2].completed)(\"active\", ctx_r2.currentStepperStep === i_r2)(\"expanded\", ctx_r2.expandedAgents[i_r2]);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"iconName\", (ctx_r2.agentStatuses[i_r2] == null ? null : ctx_r2.agentStatuses[i_r2].completed) ? \"check\" : \"folder\")(\"iconColor\", (ctx_r2.agentStatuses[i_r2] == null ? null : ctx_r2.agentStatuses[i_r2].completed) ? \"#22C55E\" : \"#1A46A7\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((agent_r4 == null ? null : agent_r4.agent == null ? null : agent_r4.agent.name) || \"Agent \" + (i_r2 + 1));\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"expanded\", ctx_r2.expandedAgents[i_r2]);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"expanded\", ctx_r2.expandedAgents[i_r2]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", agent_r4 == null ? null : agent_r4.agent == null ? null : agent_r4.agent.llm == null ? null : agent_r4.agent.llm.modelDeploymentName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (agent_r4 == null ? null : agent_r4.agent == null ? null : agent_r4.agent.tools == null ? null : agent_r4.agent.tools.length) || (agent_r4 == null ? null : agent_r4.agent == null ? null : agent_r4.agent.userTools == null ? null : agent_r4.agent.userTools.length));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getAgentInputs(i_r2).length > 0);\n  }\n}\nfunction WorkflowExecutionComponent_div_18_ng_container_7_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"textarea\", 65);\n    i0.ɵɵlistener(\"keydown.enter\", function WorkflowExecutionComponent_div_18_ng_container_7_div_4_Template_textarea_keydown_enter_1_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onInputSubmit($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 66)(3, \"ava-icon\", 67);\n    i0.ɵɵlistener(\"click\", function WorkflowExecutionComponent_div_18_ng_container_7_div_4_Template_ava_icon_click_3_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onInputSubmit());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const input_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formControlName\", input_r10.input)(\"placeholder\", \"Enter \" + input_r10.label);\n  }\n}\nfunction WorkflowExecutionComponent_div_18_ng_container_7_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"div\", 52);\n    i0.ɵɵlistener(\"click\", function WorkflowExecutionComponent_div_18_ng_container_7_div_5_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const input_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.triggerFileInput(input_r10.input));\n    });\n    i0.ɵɵelement(2, \"ava-icon\", 53);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"input\", 54);\n    i0.ɵɵlistener(\"change\", function WorkflowExecutionComponent_div_18_ng_container_7_div_5_Template_input_change_5_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const input_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onImageSelected($event, input_r10.input));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const input_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Upload Image for \", input_r10.label, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"current-file-\" + input_r10.input);\n  }\n}\nfunction WorkflowExecutionComponent_div_18_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 62)(2, \"label\", 46);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, WorkflowExecutionComponent_div_18_ng_container_7_div_4_Template, 4, 2, \"div\", 63)(5, WorkflowExecutionComponent_div_18_ng_container_7_div_5_Template, 6, 2, \"div\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const input_r10 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(input_r10.label);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isImageInput(input_r10.input));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isImageInput(input_r10.input));\n  }\n}\nfunction WorkflowExecutionComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"div\", 58)(2, \"div\", 31);\n    i0.ɵɵelement(3, \"ava-icon\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\", 60);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 61);\n    i0.ɵɵtemplate(7, WorkflowExecutionComponent_div_18_ng_container_7_Template, 6, 3, \"ng-container\", 16);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"Enter input for \", ctx_r2.getCurrentAgentName(), \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.workflowForm);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getCurrentStepInputs());\n  }\n}\nfunction WorkflowExecutionComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 68)(1, \"app-agent-activity\", 69);\n    i0.ɵɵlistener(\"saveLogs\", function WorkflowExecutionComponent_div_31_Template_app_agent_activity_saveLogs_1_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.saveLogs());\n    })(\"controlAction\", function WorkflowExecutionComponent_div_31_Template_app_agent_activity_controlAction_1_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.handleControlAction($event));\n    })(\"onOutPutBtnClick\", function WorkflowExecutionComponent_div_31_Template_app_agent_activity_onOutPutBtnClick_1_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onTabChange({\n        id: \"nav-products\",\n        label: \"Agent Output\"\n      }));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"activityLogs\", ctx_r2.workflowLogs)(\"executionDetails\", ctx_r2.executionDetails)(\"progress\", ctx_r2.progress)(\"isRunning\", ctx_r2.isRunning)(\"status\", ctx_r2.status);\n  }\n}\nfunction WorkflowExecutionComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 68)(1, \"app-agent-output\", 70);\n    i0.ɵɵlistener(\"export\", function WorkflowExecutionComponent_div_32_Template_app_agent_output_export_1_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.exportResults(\"output\"));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"outputs\", ctx_r2.taskMessage);\n  }\n}\nexport let WorkflowExecutionComponent = /*#__PURE__*/(() => {\n  class WorkflowExecutionComponent {\n    route;\n    router;\n    workflowService;\n    tokenStorage;\n    loaderService;\n    formBuilder;\n    navigationTabs = [{\n      id: 'nav-home',\n      label: 'Agent Activity'\n    }, {\n      id: 'nav-products',\n      label: 'Agent Output'\n    }, {\n      id: 'nav-services',\n      label: 'Preview',\n      disabled: true\n    }];\n    // Workflow details\n    workflowId = null;\n    workflowName = 'Workflow';\n    constants = workflowConstants;\n    chatInterfaceComp;\n    // Activity logs\n    activityLogs = [];\n    activityProgress = 0;\n    executionDetails;\n    isRunning = false;\n    status = ExecutionStatus.notStarted;\n    // Chat messages\n    chatMessages = [];\n    isProcessingChat = false;\n    inputText = '';\n    // Agent outputs\n    agentOutputs = [];\n    workflowForm;\n    fileType = '.zip';\n    // Execution state\n    executionStartTime = null;\n    executionCompleted = false;\n    executionId;\n    workflowLogs = [];\n    enableStreamingLog = environment.enableLogStreaming || 'all';\n    isExecutionComplete = false;\n    progressInterval;\n    // Component lifecycle\n    destroy$ = new Subject();\n    selectedTab = 'Agent Activity';\n    demoTabs = [{\n      id: 'activity',\n      label: 'Agent Activity'\n    }, {\n      id: 'agents',\n      label: 'Agent Output'\n    }, {\n      id: 'preview',\n      label: 'Preview',\n      disabled: true\n    }];\n    errorMsg = false;\n    resMessage;\n    taskMessage = [];\n    isJsonValid = false;\n    disableChat = false;\n    selectedFiles = [];\n    workflowAgents = [];\n    userInputList = [];\n    progress = 0;\n    isLoading = false;\n    loaderColor = '';\n    inputFieldOrder = [];\n    currentInputIndex = 0;\n    activeTabId = 'nav-home';\n    // Panel state\n    isLeftPanelCollapsed = false;\n    expandedAgents = [];\n    agentStatuses = [];\n    showAllInputs = [];\n    // Stepper state\n    stepperSteps = [];\n    currentStepperStep = 0;\n    // Pipeline data\n    pipelineAgents = [];\n    agentInputsMap = {};\n    constructor(route, router, workflowService, tokenStorage, loaderService, formBuilder) {\n      this.route = route;\n      this.router = router;\n      this.workflowService = workflowService;\n      this.tokenStorage = tokenStorage;\n      this.loaderService = loaderService;\n      this.formBuilder = formBuilder;\n    }\n    ngOnInit() {\n      this.loaderService.disableLoader();\n      this.selectedTab = 'Agent Activity';\n      this.executionId = crypto.randomUUID();\n      // Get workflow ID from route params\n      this.route.paramMap.pipe(takeUntil(this.destroy$)).subscribe(params => {\n        this.workflowId = params.get('id');\n        if (this.workflowId) {\n          this.loadWorkflow(this.workflowId);\n        } else {\n          // No workflow ID, redirect back to workflows page\n          this.router.navigate(['/build/workflows']);\n        }\n      });\n      // this.executeWorkflow()\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n      this.loaderService.enableLoader();\n    }\n    onTabChange(event) {\n      this.selectedTab = event.label;\n      this.activeTabId = event.id;\n      console.log('Tab changed:', event);\n    }\n    // Load workflow data\n    loadWorkflow(id) {\n      // In a real app, this would fetch the workflow from a service\n      console.log(`Loading workflow with ID: ${id}`);\n      this.chatMessages = [{\n        from: 'ai',\n        text: 'I am your workflow assistant. I will help you in executing this workflow.'\n      }];\n      this.workflowForm = this.formBuilder.group({});\n      this.workflowService.getWorkflowById(id).subscribe({\n        next: res => {\n          // Handle pipeline response structure\n          if (res.pipeline && res.pipeline.pipeLineAgents) {\n            this.pipelineAgents = res.pipeline.pipeLineAgents;\n            this.workflowName = res.pipeline.name;\n            this.processPipelineAgents();\n          } else {\n            // Handle workflow response structure\n            this.workflowAgents = res.workflowAgents;\n            this.workflowName = res.name;\n            this.userInputList = this.extractInputField(this.workflowAgents);\n          }\n          if (this.userInputList.length === 0) {\n            this.disableChat = true;\n          }\n          this.initializeForm();\n          this.initializeAgentStates();\n          this.initializeStepper();\n          this.startInputCollection();\n        },\n        error: err => {\n          // Fallback to demo data for testing\n          this.loadDemoWorkflow();\n          console.log('Loading demo workflow due to error:', err);\n        }\n      });\n    }\n    isImageInput(input) {\n      const match = input.match(/{{(.*?)}}/);\n      if (match && match[1]) {\n        const variableName = match[1].trim();\n        return variableName.startsWith('image') || variableName.startsWith('Image');\n      }\n      return false;\n    }\n    initializeForm() {\n      this.userInputList.forEach(label => {\n        this.workflowForm.addControl(label.input, this.formBuilder.control('', Validators.required));\n      });\n    }\n    startFakeProgress() {\n      this.progress = 0;\n      this.progressInterval = setInterval(() => {\n        if (this.progress < 90) {\n          this.progress += 5; // Increase slowly\n        }\n      }, 200); // Adjust speed\n    }\n    stopFakeProgress() {\n      clearInterval(this.progressInterval);\n      this.progress = 100;\n      setTimeout(() => {\n        this.isLoading = false;\n      }, 500); // Small delay to let user see 100%\n    }\n    // Handle new chat message from user\n    handleChatMessage(message) {\n      // console.log('message ', message, 'is blank', message.trim() === '');\n      this.isProcessingChat = true;\n      if (message.trim() === '') {\n        if (this.inputFieldOrder.length === 0) {\n          this.chatInterfaceComp.addAiResponse('Executing the workflow...');\n          this.executeWorkflow();\n        }\n        return;\n      }\n      if (this.isExecutionComplete || this.currentInputIndex === this.inputFieldOrder.length) {\n        this.chatInterfaceComp.addAiResponse('Executing the workflow...');\n        this.executeWorkflow();\n        return;\n      }\n      const field = this.inputFieldOrder[this.currentInputIndex];\n      if (this.isImageInput(field)) {\n        // Ignore text input, wait for file input\n        this.chatInterfaceComp.addAiResponse(`Please upload an image file for ${field}`);\n        return;\n      }\n      this.workflowForm.get(field)?.setValue(message);\n      this.currentInputIndex++;\n      if (this.currentInputIndex < this.inputFieldOrder.length) {\n        this.promptForCurrentField();\n      } else {\n        this.chatInterfaceComp.addAiResponse('Thank you for the input! Executing the workflow...');\n        this.executeWorkflow();\n      }\n    }\n    // Save execution logs\n    saveLogs() {\n      console.log('Saving execution logs...');\n      // This would typically save to a service\n    }\n    // Export results\n    exportResults(section) {\n      console.log(`Exporting ${section} data...`);\n      if (section === 'activity') {\n        const data = this.activityLogs.map(log => `[${log.timestamp}] ${log.message}`).join('\\n');\n        this.downloadAsFile(data, 'workflow-activity-logs.txt', 'text/plain');\n      } else {\n        const data = JSON.stringify(this.agentOutputs, null, 2);\n        this.downloadAsFile(data, 'workflow-outputs.json', 'application/json');\n      }\n    }\n    // Helper method to download data as a file\n    downloadAsFile(data, filename, type) {\n      const blob = new Blob([data], {\n        type\n      });\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = filename;\n      link.click();\n      URL.revokeObjectURL(url);\n    }\n    // Handle controls for execution\n    handleControlAction(action) {\n      console.log(`Control action: ${action}`);\n      // In a real app, this would control the workflow execution\n      if (action === 'play') {\n        this.isRunning = true;\n      } else if (action === 'pause' || action === 'stop') {\n        this.isRunning = false;\n      }\n    }\n    // Navigate back to workflow listing\n    navigateBack() {\n      this.router.navigate(['/build/workflows']);\n    }\n    // Navigate to edit workflow\n    editWorkflow() {\n      if (this.workflowId) {\n        this.router.navigate(['/build/workflows/edit', this.workflowId]);\n      }\n    }\n    logExecutionStatus(delay = 2000) {\n      setTimeout(() => {\n        if (!this.isExecutionComplete) {\n          console.log(this.constants);\n          console.log(this.constants['labels'].workflowExecProcessing);\n          this.workflowLogs.push({\n            content: this.constants['labels'].workflowExecProcessing,\n            color: '#F9DB24'\n          });\n        }\n      }, delay);\n    }\n    // public parseAnsiString(ansiString: string) {\n    //   const regex = ansiRegex();\n    //   const parts = ansiString.split(regex);\n    //   const matches = [...ansiString.matchAll(regex)];\n    //   parts.forEach((part, index) => {\n    //     if (part.trim() !== '') {\n    //       let colorCode = matches[index - 1][0];\n    //       if (index - 2 >= 0 && matches[index - 2]?.includes('\\u001b[1m')) {\n    //         colorCode = `\\u001b[1m${colorCode}`;\n    //       }\n    //       this.workflowLogs.push({\n    //         content: part,\n    //         color: this.colorMap[colorCode] || 'white',\n    //       });\n    //     }\n    //   });\n    // }\n    getWorkflowLogs(executionId) {\n      this.workflowService.workflowLogConnect(executionId).pipe(takeUntil(this.destroy$)).subscribe({\n        next: message => {\n          console.log('message: ', message);\n          const {\n            content,\n            color\n          } = message;\n          if (color) {\n            this.workflowLogs.push({\n              content,\n              color\n            });\n          } else if (this.enableStreamingLog === 'all') {\n            // this.parseAnsiString(content);\n          }\n        },\n        error: err => {\n          this.workflowLogs.push({\n            content: this.constants['workflowLog'],\n            color: 'red'\n          });\n          console.error('WebSocket error:', err);\n        },\n        complete: () => {\n          this.logExecutionStatus();\n          console.log('WebSocket connection closed');\n        }\n      });\n    }\n    // public parseAnsiString(ansiString: string) {\n    //   const regex = ansiRegex();\n    //   const parts = ansiString.split(regex);\n    //   const matches = [...ansiString.matchAll(regex)];\n    //   parts.forEach((part, index) => {\n    //     if (part.trim() !== '') {\n    //       let colorCode = matches[index-1][0];\n    //       if(index - 2 >= 0 && matches[index-2]?.includes('\\u001b[1m')) {\n    //         colorCode = `\\u001b[1m${colorCode}`;\n    //       }\n    //       this.workflowLogs.push({\n    //         content: part, \n    //         color: this.colorMap[colorCode] || 'white', \n    //       });\n    //     }\n    //   });\n    // }\n    validateJson(output) {\n      this.isJsonValid = false;\n      try {\n        const parsedOutput = JSON.parse(output);\n        this.isJsonValid = true;\n        return parsedOutput;\n      } catch (e) {\n        return null;\n      }\n    }\n    executeWorkflow() {\n      let payload = new FormData();\n      let queryString = '';\n      this.status = ExecutionStatus.running;\n      if (this.selectedFiles.length) {\n        this.selectedFiles.forEach(file => {\n          payload.append('files', file);\n        });\n        payload.append('workflowId', this.workflowId);\n        payload.append('userInputs', JSON.stringify(this.workflowForm.value));\n        payload.append('user', this.tokenStorage.getDaUsername());\n        payload.append('executionId', this.executionId);\n        queryString = '/files';\n      } else {\n        payload = {\n          pipeLineId: this.workflowId,\n          userInputs: this.workflowForm.value,\n          executionId: this.executionId,\n          user: this.tokenStorage.getDaUsername()\n        };\n      }\n      this.getWorkflowLogs(this.executionId);\n      this.startFakeProgress();\n      this.workflowService.executeWorkflow(payload, queryString).pipe(takeUntil(this.destroy$)).subscribe({\n        next: res => {\n          this.isProcessingChat = false;\n          this.isRunning = false;\n          this.chatInterfaceComp.addAiResponse(res?.message || \"Workflow execution completed successfully!\");\n          if (res?.workflowResponse?.pipeline?.output) {\n            this.isExecutionComplete = true;\n            // console.log(this.constants['labels'].workflowExecComplete);\n            this.workflowLogs.push({\n              content: this.constants['labels'].workflowExecComplete,\n              color: '#0F8251'\n            });\n            this.errorMsg = false;\n            this.resMessage = res?.workflowResponse?.pipeline?.output;\n            this.agentOutputs = res?.workflowResponse?.pipeline?.tasksOutputs.map(task => {\n              return {\n                id: task?.id || '',\n                title: task?.title || '',\n                content: task?.content || '',\n                agentName: task?.agentName || '',\n                timestamp: task?.timestamp || '',\n                type: task?.type || '',\n                description: task?.description || '',\n                expected_output: task?.expected_output || '',\n                summary: task?.summary || '',\n                raw: task?.raw || ''\n              };\n            });\n            this.taskMessage = res?.workflowResponse?.pipeline?.tasksOutputs.map(task => {\n              return {\n                description: task.description,\n                summary: task.summary,\n                raw: task.raw,\n                expected_output: task.expected_output\n              };\n            });\n            // if(\"file_download_url\" in res?.pipeline){\n            //   this.isFileWriter = true;\n            //   this.fileDownloadLink = res?.pipeline?.file_download_url;\n            //   if(!this.fileDownloadLink){\n            //     this.fileDownloadUrlError = [];\n            //     this.fileDownloadUrlError.push(\"Output file is not generated yet!\")\n            //   }\n            // }\n            // this.isAccordian = true\n          }\n          this.validateJson(this.resMessage);\n          this.status = ExecutionStatus.completed;\n          this.stopFakeProgress();\n          this.selectedFiles = [];\n        },\n        error: error => {\n          this.isExecutionComplete = true;\n          this.isProcessingChat = false;\n          this.errorMsg = true;\n          this.resMessage = error?.error?.detail;\n          this.workflowService.workflowLogDisconnect();\n          this.workflowLogs.push({\n            content: this.constants['labels'].workflowLogFailed,\n            color: 'red'\n          });\n          this.chatInterfaceComp.addAiResponse('Something went wrong, Workflow execution has failed.');\n          this.selectedFiles = [];\n          this.stopFakeProgress();\n          console.log('error is', error.message);\n        }\n      });\n    }\n    // public asyncExecutePipeline() {\n    //   const payload: FormData = new FormData();\n    //   if (this.selectedFiles?.length) {\n    //     for (const element of this.selectedFiles) {\n    //       payload.append('files', element)\n    //     }\n    //   }\n    //   payload.append('pipeLineId', String(this.workflowId));\n    //   payload.append('userInputs', JSON.stringify(this.workflowForm.value));\n    //   payload.append('user', this.tokenStorage.getDaUsername() || '');\n    //   payload.append('executionId', this.executionId);\n    //   this.workflowService.asyncExecutePipeline(payload).pipe().subscribe({\n    //     next: (res: any) => {\n    //       if(res) {\n    //         // res handling\n    //         console.log(res);\n    //       }\n    //     },\n    //     error: e => {\n    //       // error handling\n    //       console.log(e);\n    //     }\n    //   })\n    // }\n    handleAttachment() {\n      console.log('handleAttachment');\n    }\n    onAttachmentsSelected(files) {\n      if (this.currentInputIndex === this.inputFieldOrder.length || this.inputFieldOrder.length === 0) {\n        this.selectedFiles = files;\n        return;\n      }\n      const field = this.inputFieldOrder[this.currentInputIndex];\n      if (this.isImageInput(field)) {\n        if (files && files.length > 0) {\n          // Create a mock event object for the new method signature\n          const mockEvent = {\n            target: {\n              files: [files[0]]\n            }\n          };\n          this.onImageSelected(mockEvent, field);\n        }\n      } else {\n        this.selectedFiles = files;\n      }\n    }\n    startInputCollection() {\n      this.inputFieldOrder = Object.keys(this.workflowForm.controls);\n      this.currentInputIndex = 0;\n      if (this.inputFieldOrder.length > 0) {\n        this.promptForCurrentField();\n      } else {\n        this.disableChat = true;\n        this.chatInterfaceComp.addAiResponse('No input is required for this workflow. Click on send button to execute the workflow.');\n      }\n    }\n    promptForCurrentField() {\n      const field = this.inputFieldOrder[this.currentInputIndex];\n      if (this.isImageInput(field)) {\n        this.fileType = '.jpeg,.png,.jpg,.svg';\n        this.chatInterfaceComp.addAiResponse(`Please upload an image for ${field}`);\n        // UI should now show a file input for the user\n      } else {\n        this.fileType = '.zip'; // or whatever default you want for non-image\n        this.chatInterfaceComp.addAiResponse(`Please enter the value of ${field}`);\n      }\n    }\n    // Panel management methods\n    toggleLeftPanel() {\n      this.isLeftPanelCollapsed = !this.isLeftPanelCollapsed;\n    }\n    toggleAgentExpansion(index) {\n      this.expandedAgents[index] = !this.expandedAgents[index];\n    }\n    initializeAgentStates() {\n      const agentCount = this.pipelineAgents.length || this.workflowAgents.length;\n      this.expandedAgents = new Array(agentCount).fill(false);\n      this.agentStatuses = new Array(agentCount).fill(0).map(() => ({\n        completed: false\n      }));\n      this.showAllInputs = new Array(agentCount).fill(false);\n    }\n    // Process pipeline agents and extract inputs\n    processPipelineAgents() {\n      this.pipelineAgents.forEach((pipelineAgent, index) => {\n        const agent = pipelineAgent.agent;\n        const agentInputs = this.extractAgentInputs(agent);\n        this.agentInputsMap[index] = agentInputs;\n      });\n      // Create combined user input list for form initialization\n      this.userInputList = [];\n      Object.values(this.agentInputsMap).forEach(inputs => {\n        this.userInputList.push(...inputs);\n      });\n    }\n    // Extract inputs for a specific agent\n    extractAgentInputs(agent) {\n      const PLACEHOLDER_PATTERNS = /\\{\\{([a-zA-Z0-9-_]+)\\}\\}/g;\n      const description = agent.task?.description || '';\n      const matches = [...description.matchAll(PLACEHOLDER_PATTERNS)];\n      return matches.map(match => ({\n        input: match[0],\n        // Full placeholder like {{image_1}}\n        placeholder: match[1],\n        // Just the variable name like image_1\n        label: this.formatInputLabel(match[1]),\n        agentName: agent.name\n      }));\n    }\n    // Format input label for display\n    formatInputLabel(placeholder) {\n      return placeholder.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase());\n    }\n    // Initialize stepper\n    initializeStepper() {\n      this.stepperSteps = this.pipelineAgents.map((pipelineAgent, index) => ({\n        id: index,\n        label: pipelineAgent.agent.name,\n        completed: false,\n        active: index === 0\n      }));\n      this.currentStepperStep = 0;\n    }\n    // Extract input fields method (similar to pipeline component)\n    extractInputField(workflowAgents) {\n      const PLACEHOLDER_PATTERNS = /(%\\d+\\$s)|\\{\\{([a-zA-Z0-9-_]+)\\}\\}/g;\n      const placeholderMap = {};\n      workflowAgents.forEach(agent => {\n        const agentName = agent?.name || `Agent ${workflowAgents.indexOf(agent) + 1}`;\n        const agentDescription = agent?.description || agent?.task?.description || '';\n        const matches = agentDescription.matchAll(PLACEHOLDER_PATTERNS) || [];\n        for (const match of matches) {\n          const placeholder = match[1] || match[2];\n          const placeholderInput = match[0];\n          if (!placeholderMap[placeholder]) {\n            placeholderMap[placeholder] = {\n              agents: new Set(),\n              inputs: new Set()\n            };\n          }\n          placeholderMap[placeholder].agents.add(agentName);\n          placeholderMap[placeholder].inputs.add(placeholderInput);\n        }\n      });\n      return Object.entries(placeholderMap).map(([placeholder, {\n        agents,\n        inputs\n      }]) => ({\n        name: [...agents].length > 2 ? `${[...agents].slice(0, -1).join(\", \")} and ${[...agents].at(-1)}` : [...agents].join(\" and \"),\n        placeholder,\n        input: [...inputs][0]\n      }));\n    }\n    // Input validation\n    isInputValid() {\n      return this.workflowForm.valid && !!this.workflowId;\n    }\n    // Stepper event handlers\n    onStepperChange(step) {\n      this.currentStepperStep = step.id;\n      this.expandedAgents[step.id] = true; // Auto-expand current agent\n    }\n    // Get inputs for current step\n    getCurrentStepInputs() {\n      return this.agentInputsMap[this.currentStepperStep] || [];\n    }\n    // Get current agent name\n    getCurrentAgentName() {\n      if (this.pipelineAgents[this.currentStepperStep]) {\n        return this.pipelineAgents[this.currentStepperStep].agent.name;\n      }\n      return `Agent ${this.currentStepperStep + 1}`;\n    }\n    // Get inputs for specific agent\n    getAgentInputs(agentIndex) {\n      return this.agentInputsMap[agentIndex] || [];\n    }\n    // Get combined tools for agent\n    getCombinedTools(agent) {\n      const tools = [...(agent.tools || []), ...(agent.userTools || [])];\n      return tools.map(tool => tool.toolName || tool).join(', ') || 'No tools';\n    }\n    // Toggle show all inputs for agent\n    toggleShowAllInputs(agentIndex) {\n      this.showAllInputs[agentIndex] = !this.showAllInputs[agentIndex];\n    }\n    // Handle input submission\n    onInputSubmit(event) {\n      const keyboardEvent = event;\n      if (keyboardEvent && keyboardEvent.shiftKey) {\n        return; // Allow shift+enter for new line\n      }\n      if (event) {\n        event.preventDefault();\n      }\n      // Check if current step inputs are valid\n      if (this.isCurrentStepValid()) {\n        this.moveToNextStep();\n      }\n    }\n    // Move to next step\n    moveToNextStep() {\n      if (this.currentStepperStep < this.pipelineAgents.length - 1) {\n        // Mark current step as completed\n        this.agentStatuses[this.currentStepperStep].completed = true;\n        this.stepperSteps[this.currentStepperStep].completed = true;\n        // Move to next step\n        this.currentStepperStep++;\n        this.stepperSteps[this.currentStepperStep].active = true;\n        // Auto-expand next agent\n        this.expandedAgents[this.currentStepperStep] = true;\n        // Add AI response for next step\n        const nextAgent = this.pipelineAgents[this.currentStepperStep];\n        this.chatInterfaceComp.addAiResponse(`Moving to ${nextAgent.agent.name}. Please provide the required inputs.`);\n      } else {\n        // All steps completed, ready to execute\n        this.agentStatuses[this.currentStepperStep].completed = true;\n        this.stepperSteps[this.currentStepperStep].completed = true;\n        this.chatInterfaceComp.addAiResponse('All inputs collected! Ready to execute the workflow.');\n      }\n    }\n    // Check if current step is valid\n    isCurrentStepValid() {\n      const currentInputs = this.getCurrentStepInputs();\n      return currentInputs.every(input => {\n        const control = this.workflowForm.get(input.input);\n        return control && control.valid && control.value;\n      });\n    }\n    // Execute current step\n    executeCurrentStep() {\n      if (this.isCurrentStepValid()) {\n        this.moveToNextStep();\n      }\n    }\n    // Handle image selection\n    onImageSelected(event, inputName) {\n      const file = event.target.files[0];\n      if (file) {\n        const reader = new FileReader();\n        reader.onload = () => {\n          const base64String = reader.result;\n          this.workflowForm.get(inputName)?.setValue(base64String);\n          // Auto-advance if this was the last required input for current step\n          setTimeout(() => {\n            if (this.isCurrentStepValid()) {\n              this.moveToNextStep();\n            }\n          }, 100);\n        };\n        reader.readAsDataURL(file);\n      }\n    }\n    // Trigger file input\n    triggerFileInput(inputName) {\n      const fileInput = document.getElementById('file-' + inputName) || document.getElementById('current-file-' + inputName);\n      if (fileInput) {\n        fileInput.click();\n      }\n    }\n    // Demo workflow for testing\n    loadDemoWorkflow() {\n      this.pipelineAgents = [{\n        serial: 1,\n        agent: {\n          name: 'OCR Agent',\n          task: {\n            description: 'Extract text from {{image_1}} and {{image_2}}'\n          },\n          llm: {\n            modelDeploymentName: 'GPT-4'\n          },\n          tools: [],\n          userTools: []\n        }\n      }, {\n        serial: 2,\n        agent: {\n          name: 'Analysis Agent',\n          task: {\n            description: 'Analyze the extracted text and generate {{report_type}} report'\n          },\n          llm: {\n            modelDeploymentName: 'Claude-3'\n          },\n          tools: [],\n          userTools: []\n        }\n      }];\n      this.workflowName = 'Demo OCR Workflow';\n      this.processPipelineAgents();\n      this.initializeForm();\n      this.initializeAgentStates();\n      this.initializeStepper();\n      if (this.userInputList.length === 0) {\n        this.disableChat = true;\n      }\n    }\n    static ɵfac = function WorkflowExecutionComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || WorkflowExecutionComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.WorkflowService), i0.ɵɵdirectiveInject(i3.TokenStorageService), i0.ɵɵdirectiveInject(i3.LoaderService), i0.ɵɵdirectiveInject(i4.FormBuilder));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: WorkflowExecutionComponent,\n      selectors: [[\"app-workflow-execution\"]],\n      viewQuery: function WorkflowExecutionComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(ChatInterfaceComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.chatInterfaceComp = _t.first);\n        }\n      },\n      decls: 33,\n      vars: 21,\n      consts: [[1, \"workflow-execution-container\"], [\"width\", \"0\", \"height\", \"0\", 2, \"position\", \"absolute\"], [\"id\", \"gradient\", \"x1\", \"0%\", \"y1\", \"0%\", \"x2\", \"100%\", \"y2\", \"0%\"], [\"offset\", \"0%\", \"stop-color\", \"#6566CD\"], [\"offset\", \"100%\", \"stop-color\", \"#F96CAB\"], [\"role\", \"main\", 1, \"execution-content\"], [\"role\", \"region\", \"aria-label\", \"Workflow Execution Panel\", 1, \"column\", \"execution-panel-column\"], [1, \"column-content\", \"execution-panel-content\"], [1, \"column-header\"], [1, \"header-left\"], [\"iconName\", \"arrowLeft\", \"iconColor\", \"#1A46A7\", 1, \"back-icon\", 3, \"click\"], [\"iconName\", \"panelLeft\", \"iconColor\", \"#1A46A7\", 1, \"panel-icon\", 3, \"click\"], [1, \"panel-content\"], [1, \"stepper-section\"], [\"orientation\", \"vertical\", \"size\", \"small\", 3, \"stepChange\", \"steps\", \"currentStep\", \"iconColor\", \"iconSize\", \"showNavigation\"], [1, \"agents-section\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"current-input-section\", 4, \"ngIf\"], [1, \"info-message\"], [1, \"execute-section\"], [\"label\", \"\\u25B6 Execute Agent\", \"variant\", \"primary\", \"size\", \"large\", 3, \"click\", \"disabled\", \"customStyles\"], [\"role\", \"region\", \"aria-label\", \"Agent Output\", 1, \"column\", \"output-column\"], [1, \"column-content\"], [1, \"column-header\", \"row\"], [1, \"col-7\"], [\"variant\", \"button\", \"buttonShape\", \"pill\", \"ariaLabel\", \"Pill navigation tabs\", 3, \"tabChange\", \"tabs\", \"activeTabId\", \"showContentPanels\"], [1, \"col-5\", \"right-section-header\"], [\"label\", \"Send for Approval\", \"variant\", \"primary\", \"size\", \"medium\", 3, \"customStyles\"], [\"style\", \"height: 100%\", 4, \"ngIf\"], [1, \"agent-card\"], [1, \"agent-header\", 3, \"click\"], [1, \"status-icon\"], [3, \"iconName\", \"iconColor\"], [1, \"agent-info\"], [1, \"agent-name\"], [\"iconName\", \"chevronDown\", \"iconColor\", \"#6B7280\", 1, \"expand-icon\"], [1, \"agent-details\"], [\"class\", \"agent-detail-item\", 4, \"ngIf\"], [\"class\", \"agent-inputs\", 4, \"ngIf\"], [1, \"agent-detail-item\"], [1, \"detail-label\"], [1, \"detail-value\"], [1, \"agent-inputs\"], [1, \"inputs-container\"], [\"class\", \"show-more-toggle\", 4, \"ngIf\"], [1, \"input-field\", 3, \"formGroup\"], [1, \"input-label\"], [\"class\", \"text-input-container\", 4, \"ngIf\"], [\"class\", \"image-input-container\", 4, \"ngIf\"], [1, \"text-input-container\"], [1, \"input-textarea\", 3, \"formControlName\", \"placeholder\", \"disabled\"], [1, \"image-input-container\"], [1, \"file-upload-area\", 3, \"click\"], [\"iconName\", \"upload\", \"iconColor\", \"#6B7280\"], [\"type\", \"file\", \"accept\", \"image/*\", 2, \"display\", \"none\", 3, \"change\", \"id\"], [1, \"show-more-toggle\"], [\"type\", \"button\", 1, \"toggle-btn\", 3, \"click\"], [1, \"current-input-section\"], [1, \"input-header\"], [\"iconName\", \"edit\", \"iconColor\", \"#1A46A7\"], [1, \"input-title\"], [1, \"current-input-container\", 3, \"formGroup\"], [1, \"input-field\"], [\"class\", \"input-container\", 4, \"ngIf\"], [1, \"input-container\"], [1, \"input-textarea\", 3, \"keydown.enter\", \"formControlName\", \"placeholder\"], [1, \"input-actions\"], [\"iconName\", \"send\", \"iconColor\", \"#1A46A7\", 1, \"send-icon\", 3, \"click\"], [2, \"height\", \"100%\"], [3, \"saveLogs\", \"controlAction\", \"onOutPutBtnClick\", \"activityLogs\", \"executionDetails\", \"progress\", \"isRunning\", \"status\"], [3, \"export\", \"outputs\"]],\n      template: function WorkflowExecutionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(1, \"svg\", 1)(2, \"defs\")(3, \"linearGradient\", 2);\n          i0.ɵɵelement(4, \"stop\", 3)(5, \"stop\", 4);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6)(8, \"div\", 7)(9, \"div\", 8)(10, \"div\", 9)(11, \"ava-icon\", 10);\n          i0.ɵɵlistener(\"click\", function WorkflowExecutionComponent_Template_ava_icon_click_11_listener() {\n            return ctx.navigateBack();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"ava-icon\", 11);\n          i0.ɵɵlistener(\"click\", function WorkflowExecutionComponent_Template_ava_icon_click_12_listener() {\n            return ctx.toggleLeftPanel();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 12)(14, \"div\", 13)(15, \"ava-stepper\", 14);\n          i0.ɵɵlistener(\"stepChange\", function WorkflowExecutionComponent_Template_ava_stepper_stepChange_15_listener($event) {\n            return ctx.onStepperChange($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 15);\n          i0.ɵɵtemplate(17, WorkflowExecutionComponent_ng_container_17_Template, 13, 16, \"ng-container\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(18, WorkflowExecutionComponent_div_18_Template, 8, 3, \"div\", 17);\n          i0.ɵɵelementStart(19, \"div\", 18)(20, \"p\");\n          i0.ɵɵtext(21, \"Agent input needed in order to execute Workflow\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"div\", 19)(23, \"ava-button\", 20);\n          i0.ɵɵlistener(\"click\", function WorkflowExecutionComponent_Template_ava_button_click_23_listener() {\n            return ctx.executeCurrentStep();\n          });\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(24, \"div\", 21)(25, \"div\", 22)(26, \"div\", 23)(27, \"div\", 24)(28, \"ava-tabs\", 25);\n          i0.ɵɵlistener(\"tabChange\", function WorkflowExecutionComponent_Template_ava_tabs_tabChange_28_listener($event) {\n            return ctx.onTabChange($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(29, \"div\", 26);\n          i0.ɵɵelement(30, \"ava-button\", 27);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(31, WorkflowExecutionComponent_div_31_Template, 2, 5, \"div\", 28)(32, WorkflowExecutionComponent_div_32_Template, 2, 1, \"div\", 28);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵclassProp(\"collapsed\", ctx.isLeftPanelCollapsed);\n          i0.ɵɵadvance(6);\n          i0.ɵɵclassProp(\"hidden\", ctx.isLeftPanelCollapsed);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"steps\", ctx.stepperSteps)(\"currentStep\", ctx.currentStepperStep)(\"iconColor\", \"#ffff\")(\"iconSize\", \"16\")(\"showNavigation\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.pipelineAgents);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.getCurrentStepInputs().length > 0);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"disabled\", !ctx.isCurrentStepValid())(\"customStyles\", i0.ɵɵpureFunction0(19, _c0));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"tabs\", ctx.navigationTabs)(\"activeTabId\", ctx.activeTabId)(\"showContentPanels\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"customStyles\", i0.ɵɵpureFunction0(20, _c1));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTab === \"Agent Activity\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTab === \"Agent Output\");\n        }\n      },\n      dependencies: [CommonModule, i5.NgForOf, i5.NgIf, FormsModule, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, ReactiveFormsModule, i4.FormGroupDirective, i4.FormControlName, AgentActivityComponent, AgentOutputComponent, TabsComponent, ButtonComponent, IconComponent, AvaStepperComponent],\n      styles: [\".ava-tabs {\\n  background: none !important;\\n}\\n\\n  .ava-tabs__container {\\n  border-radius: none !important;\\n  border: none !important;\\n  padding: 0 !important;\\n  box-shadow: none !important;\\n  background: none !important;\\n}\\n\\n  .ava-tabs__list {\\n  padding: 0 !important;\\n}\\n\\n.workflow-execution-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 93%;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 20px 24px;\\n  border-bottom: 1px solid var(--border-color, #e0e0e0);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-title[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 24px;\\n  font-weight: 600;\\n  color: var(--text-color, #333);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-title[_ngcontent-%COMP%]   .header-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%], \\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 8px 16px;\\n  border-radius: 5px;\\n  font-weight: 500;\\n  font-size: 14px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%], \\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  margin-right: 6px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%] {\\n  background-color: var(--bg-muted, #f5f5f5);\\n  border: 1px solid var(--border-color, #e0e0e0);\\n  color: var(--text-color, #333);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]:hover {\\n  background-color: var(--bg-muted-hover, #e9e9e9);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%] {\\n  background-color: var(--card-bg, #fff);\\n  border: 1px solid var(--border-color, #e0e0e0);\\n  color: var(--text-color, #333);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-header[_ngcontent-%COMP%]   .execution-actions[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]:hover {\\n  background-color: var(--card-bg-hover, #f9f9f9);\\n  border-color: var(--border-color-dark, #d0d0d0);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-grow: 1;\\n  height: 95vh;\\n  overflow: hidden;\\n  gap: 20px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 8px 16px;\\n  background-color: #e9effd;\\n  color: #fff;\\n  height: 64px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: #1a46a7;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  padding: 8px 16px;\\n  background-color: var(--card-bg, white);\\n  position: relative;\\n  border: 1px solid transparent;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: transparent;\\n  background-image: linear-gradient(90deg, #6566cd 0%, #f96cab 100%);\\n  -webkit-background-clip: text;\\n  background-clip: text;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  border-radius: 8px;\\n  padding: 1px;\\n  background: linear-gradient(90deg, #6566cd 0%, #f96cab 100%);\\n  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);\\n  mask-composite: exclude;\\n  pointer-events: none;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%]:hover {\\n  background-color: var(--bg-muted-hover, #f9f5ff);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 18px;\\n  height: 18px;\\n  fill: none;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]   path[_ngcontent-%COMP%] {\\n  stroke-width: 2;\\n  stroke: url(#gradient);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: 0;\\n  display: flex;\\n  flex-direction: column;\\n  scrollbar-width: none;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 8px;\\n  height: 8px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: var(--scrollbar-track, #f1f1f1);\\n  border-radius: 4px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: var(--scrollbar-thumb, #d1d1d1);\\n  border-radius: 4px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: var(--scrollbar-thumb-hover, #b1b1b1);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%]   .nav-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  gap: 16px;\\n  background-color: #e9effd;\\n  padding: 0 15px 0 0;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%]   .nav-wrapper[_ngcontent-%COMP%]   .right-inner-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .column[_ngcontent-%COMP%]   .column-content[_ngcontent-%COMP%]   .nav-wrapper[_ngcontent-%COMP%]   .right-inner-wrapper[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #fff;\\n  padding: 0 10px;\\n  margin: 0px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .activity-column[_ngcontent-%COMP%] {\\n  background: transparent;\\n  box-shadow: none;\\n  border-radius: 8px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .activity-column[_ngcontent-%COMP%]   app-agent-activity[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%] {\\n  flex: 0 0 400px;\\n  background: #F0F4FF;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 15px var(--card-shadow, rgba(0, 0, 0, 0.05));\\n  transition: all 0.3s ease;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column.collapsed[_ngcontent-%COMP%] {\\n  flex: 0 0 60px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column.collapsed[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%] {\\n  background: #F0F4FF;\\n  border-bottom: 1px solid #E5E7EB;\\n  padding: 16px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  align-items: center;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .back-icon[_ngcontent-%COMP%], \\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .panel-icon[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transition: transform 0.2s ease;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .back-icon[_ngcontent-%COMP%]:hover, \\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .panel-icon[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%]   .execution-panel-content[_ngcontent-%COMP%] {\\n  padding: 0;\\n  height: 100%;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%]   .execution-panel-content[_ngcontent-%COMP%]   .panel-content[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  height: calc(100% - 80px);\\n  overflow-y: auto;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .execution-panel-column[_ngcontent-%COMP%]   .execution-panel-content[_ngcontent-%COMP%]   .panel-content.hidden[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .playground-column[_ngcontent-%COMP%] {\\n  flex: 3.5;\\n  background: var(--card-bg);\\n  border-radius: 8px;\\n  box-shadow: 0 4px 15px var(--card-shadow, rgba(0, 0, 0, 0.05));\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .playground-column[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]   .header-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  gap: 12px;\\n  align-items: center;\\n  color: #1a46a7;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .playground-column[_ngcontent-%COMP%]   .playground-content[_ngcontent-%COMP%] {\\n  padding: 0;\\n  height: 100%;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .playground-column[_ngcontent-%COMP%]   .playground-content[_ngcontent-%COMP%]   .chat-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  min-height: 400px;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  position: relative;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .playground-column[_ngcontent-%COMP%]   .playground-content[_ngcontent-%COMP%]   .chat-container[_ngcontent-%COMP%]     .prompt-container {\\n  margin-top: 30px;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .playground-column[_ngcontent-%COMP%]   .playground-content[_ngcontent-%COMP%]   .chat-container[_ngcontent-%COMP%]   .playground-title[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: var(--dashboard-primary, #6566cd);\\n  margin: 16px 16px;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .playground-column[_ngcontent-%COMP%]   .playground-content[_ngcontent-%COMP%]   .chat-container[_ngcontent-%COMP%]   app-chat-interface[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  flex: 1;\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .output-column[_ngcontent-%COMP%] {\\n  flex: 5.5;\\n  background-color: var(--card-bg, white);\\n  border-radius: 8px;\\n  box-shadow: 0 4px 15px var(--card-shadow, rgba(0, 0, 0, 0.05));\\n}\\n.workflow-execution-container[_ngcontent-%COMP%]   .execution-content[_ngcontent-%COMP%]   .output-column[_ngcontent-%COMP%]   app-agent-output[_ngcontent-%COMP%] {\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n  nav.ava-tabs__list {\\n  background: #e9effd;\\n  padding: 4px;\\n}\\n\\n  button.ava-button.primary.active {\\n  background: #616161;\\n  color: #fff;\\n}\\n\\n  .column-header .ava-tabs[data-variant=button] .ava-tabs__tab--pill {\\n  border-radius: 8px !important;\\n  padding: 12px 16px !important;\\n  font-family: \\\"Mulish\\\";\\n}\\n\\n  .ava-tabs[data-variant=button] .ava-tabs__tab--active .ava-tabs__tab-text {\\n  color: white;\\n}\\n\\n  .ava-tabs__tab-text {\\n  color: #4c515b;\\n  font-family: \\\"Mulish\\\";\\n  font-weight: 600;\\n}\\n\\n  .right-section-header .ava-button.secondary {\\n  color: #1a46a7;\\n  border: none;\\n}\\n\\n  .right-section-header .ava-button.secondary:hover {\\n  color: #1a46a7;\\n  border: none;\\n}\\n\\n.right-section-header[_ngcontent-%COMP%] {\\n  text-align: end;\\n}\\n\\n.stepper-section[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  padding: 16px;\\n  background: white;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.agents-section[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%] {\\n  background: #E8F0FE;\\n  border-radius: 12px;\\n  padding: 16px;\\n  margin-bottom: 12px;\\n  transition: all 0.3s ease;\\n  border: 2px solid transparent;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card.completed[_ngcontent-%COMP%] {\\n  background: #F0F9FF;\\n  border-left: 4px solid #22C55E;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card.active[_ngcontent-%COMP%] {\\n  border-color: #1A46A7;\\n  background: #F0F4FF;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card.expanded[_ngcontent-%COMP%]   .agent-details[_ngcontent-%COMP%] {\\n  max-height: 500px;\\n  overflow-y: auto;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  cursor: pointer;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-header[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  background: white;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-header[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-header[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%]   .agent-name[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #1F2937;\\n  margin: 0;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-header[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%]   .expand-icon[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transition: transform 0.2s ease;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-header[_ngcontent-%COMP%]   .agent-info[_ngcontent-%COMP%]   .expand-icon.expanded[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-details[_ngcontent-%COMP%] {\\n  margin-top: 12px;\\n  max-height: 0;\\n  overflow: hidden;\\n  transition: max-height 0.3s ease;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-details[_ngcontent-%COMP%]   .agent-detail-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  padding: 4px 0;\\n  font-size: 14px;\\n  margin-bottom: 8px;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-details[_ngcontent-%COMP%]   .agent-detail-item[_ngcontent-%COMP%]   .detail-label[_ngcontent-%COMP%] {\\n  color: #6B7280;\\n  font-weight: 500;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-details[_ngcontent-%COMP%]   .agent-detail-item[_ngcontent-%COMP%]   .detail-value[_ngcontent-%COMP%] {\\n  color: #1F2937;\\n  text-align: right;\\n  max-width: 200px;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-details[_ngcontent-%COMP%]   .agent-inputs[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-details[_ngcontent-%COMP%]   .agent-inputs[_ngcontent-%COMP%]   .inputs-container.scrollable[_ngcontent-%COMP%] {\\n  max-height: 300px;\\n  overflow-y: auto;\\n  padding-right: 8px;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-details[_ngcontent-%COMP%]   .agent-inputs[_ngcontent-%COMP%]   .inputs-container.scrollable[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 4px;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-details[_ngcontent-%COMP%]   .agent-inputs[_ngcontent-%COMP%]   .inputs-container.scrollable[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 2px;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-details[_ngcontent-%COMP%]   .agent-inputs[_ngcontent-%COMP%]   .inputs-container.scrollable[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 2px;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-details[_ngcontent-%COMP%]   .agent-inputs[_ngcontent-%COMP%]   .inputs-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-details[_ngcontent-%COMP%]   .agent-inputs[_ngcontent-%COMP%]   .inputs-container[_ngcontent-%COMP%]   .input-field.hidden[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-details[_ngcontent-%COMP%]   .agent-inputs[_ngcontent-%COMP%]   .inputs-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .input-label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 12px;\\n  font-weight: 500;\\n  color: #374151;\\n  margin-bottom: 6px;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-details[_ngcontent-%COMP%]   .agent-inputs[_ngcontent-%COMP%]   .inputs-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .text-input-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-details[_ngcontent-%COMP%]   .agent-inputs[_ngcontent-%COMP%]   .inputs-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .text-input-container[_ngcontent-%COMP%]   .input-textarea[_ngcontent-%COMP%] {\\n  width: 100%;\\n  min-height: 60px;\\n  padding: 8px 12px;\\n  border: 1px solid #D1D5DB;\\n  border-radius: 6px;\\n  font-size: 13px;\\n  resize: vertical;\\n  background: white;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-details[_ngcontent-%COMP%]   .agent-inputs[_ngcontent-%COMP%]   .inputs-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .text-input-container[_ngcontent-%COMP%]   .input-textarea[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #1A46A7;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-details[_ngcontent-%COMP%]   .agent-inputs[_ngcontent-%COMP%]   .inputs-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .text-input-container[_ngcontent-%COMP%]   .input-textarea[_ngcontent-%COMP%]:disabled {\\n  background: #F9FAFB;\\n  color: #6B7280;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-details[_ngcontent-%COMP%]   .agent-inputs[_ngcontent-%COMP%]   .inputs-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .image-input-container[_ngcontent-%COMP%]   .file-upload-area[_ngcontent-%COMP%] {\\n  border: 2px dashed #D1D5DB;\\n  border-radius: 6px;\\n  padding: 16px;\\n  text-align: center;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-details[_ngcontent-%COMP%]   .agent-inputs[_ngcontent-%COMP%]   .inputs-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .image-input-container[_ngcontent-%COMP%]   .file-upload-area[_ngcontent-%COMP%]:hover:not(.disabled) {\\n  border-color: #1A46A7;\\n  background: #F9FAFB;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-details[_ngcontent-%COMP%]   .agent-inputs[_ngcontent-%COMP%]   .inputs-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .image-input-container[_ngcontent-%COMP%]   .file-upload-area.disabled[_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-details[_ngcontent-%COMP%]   .agent-inputs[_ngcontent-%COMP%]   .inputs-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .image-input-container[_ngcontent-%COMP%]   .file-upload-area[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-top: 8px;\\n  font-size: 13px;\\n  color: #6B7280;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-details[_ngcontent-%COMP%]   .agent-inputs[_ngcontent-%COMP%]   .inputs-container[_ngcontent-%COMP%]   .show-more-toggle[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 8px;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-details[_ngcontent-%COMP%]   .agent-inputs[_ngcontent-%COMP%]   .inputs-container[_ngcontent-%COMP%]   .show-more-toggle[_ngcontent-%COMP%]   .toggle-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #1A46A7;\\n  font-size: 12px;\\n  cursor: pointer;\\n  text-decoration: underline;\\n}\\n.agents-section[_ngcontent-%COMP%]   .agent-card[_ngcontent-%COMP%]   .agent-details[_ngcontent-%COMP%]   .agent-inputs[_ngcontent-%COMP%]   .inputs-container[_ngcontent-%COMP%]   .show-more-toggle[_ngcontent-%COMP%]   .toggle-btn[_ngcontent-%COMP%]:hover {\\n  color: #1E40AF;\\n}\\n\\n.current-input-section[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n  background: white;\\n  border-radius: 12px;\\n  padding: 16px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.current-input-section[_ngcontent-%COMP%]   .input-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  margin-bottom: 16px;\\n}\\n.current-input-section[_ngcontent-%COMP%]   .input-header[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  background: #1A46A7;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.current-input-section[_ngcontent-%COMP%]   .input-header[_ngcontent-%COMP%]   .input-title[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #1F2937;\\n  margin: 0;\\n}\\n.current-input-section[_ngcontent-%COMP%]   .current-input-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.current-input-section[_ngcontent-%COMP%]   .current-input-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .input-label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #374151;\\n  margin-bottom: 8px;\\n}\\n.current-input-section[_ngcontent-%COMP%]   .current-input-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.current-input-section[_ngcontent-%COMP%]   .current-input-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-textarea[_ngcontent-%COMP%] {\\n  width: 100%;\\n  min-height: 80px;\\n  padding: 12px 50px 12px 16px;\\n  border: 2px solid #E5E7EB;\\n  border-radius: 12px;\\n  font-size: 14px;\\n  resize: vertical;\\n  background: white;\\n}\\n.current-input-section[_ngcontent-%COMP%]   .current-input-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-textarea[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #1A46A7;\\n}\\n.current-input-section[_ngcontent-%COMP%]   .current-input-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-textarea[_ngcontent-%COMP%]::placeholder {\\n  color: #9CA3AF;\\n}\\n.current-input-section[_ngcontent-%COMP%]   .current-input-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-actions[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 12px;\\n  bottom: 12px;\\n  display: flex;\\n  gap: 8px;\\n}\\n.current-input-section[_ngcontent-%COMP%]   .current-input-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-actions[_ngcontent-%COMP%]   .send-icon[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transition: transform 0.2s ease;\\n}\\n.current-input-section[_ngcontent-%COMP%]   .current-input-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-actions[_ngcontent-%COMP%]   .send-icon[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n}\\n.current-input-section[_ngcontent-%COMP%]   .current-input-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .image-input-container[_ngcontent-%COMP%]   .file-upload-area[_ngcontent-%COMP%] {\\n  border: 2px dashed #D1D5DB;\\n  border-radius: 12px;\\n  padding: 24px;\\n  text-align: center;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  background: #F9FAFB;\\n}\\n.current-input-section[_ngcontent-%COMP%]   .current-input-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .image-input-container[_ngcontent-%COMP%]   .file-upload-area[_ngcontent-%COMP%]:hover {\\n  border-color: #1A46A7;\\n  background: #F0F4FF;\\n}\\n.current-input-section[_ngcontent-%COMP%]   .current-input-container[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   .image-input-container[_ngcontent-%COMP%]   .file-upload-area[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-top: 8px;\\n  font-size: 14px;\\n  color: #6B7280;\\n  font-weight: 500;\\n}\\n\\n.info-message[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 24px;\\n}\\n.info-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #1A46A7;\\n  font-size: 14px;\\n  margin: 0;\\n}\\n\\n.execute-section[_ngcontent-%COMP%] {\\n  margin-top: auto;\\n  padding-top: 24px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3Byb2plY3RzL3NoYXJlZC9wYWdlcy93b3JrZmxvd3Mvd29ya2Zsb3ctZXhlY3V0aW9uL3dvcmtmbG93LWV4ZWN1dGlvbi5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFHQTtFQUNFLDJCQUFBO0FBRkY7O0FBS0E7RUFDRSw4QkFBQTtFQUNBLHVCQUFBO0VBQ0EscUJBQUE7RUFDQSwyQkFBQTtFQUNBLDJCQUFBO0FBRkY7O0FBS0E7RUFDRSxxQkFBQTtBQUZGOztBQUtBO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsV0FBQTtBQUZGO0FBR0U7RUFDRSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQkFBQTtFQUNBLGtCQUFBO0VBQ0EscURBQUE7QUFESjtBQUlNO0VBQ0UsU0FBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLDhCQUFBO0FBRlI7QUFLTTtFQUNFLGFBQUE7RUFDQSxTQUFBO0FBSFI7QUFPSTtFQUNFLGFBQUE7RUFDQSxTQUFBO0FBTE47QUFPTTs7RUFFRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxpQkFBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxlQUFBO0VBQ0EsZUFBQTtFQUNBLHlCQUFBO0FBTFI7QUFPUTs7RUFDRSxpQkFBQTtBQUpWO0FBUU07RUFDRSwwQ0FBQTtFQUNBLDhDQUFBO0VBQ0EsOEJBQUE7QUFOUjtBQVFRO0VBQ0UsZ0RBQUE7QUFOVjtBQVVNO0VBQ0Usc0NBQUE7RUFDQSw4Q0FBQTtFQUNBLDhCQUFBO0FBUlI7QUFVUTtFQUNFLCtDQUFBO0VBQ0EsK0NBQUE7QUFSVjtBQWVFO0VBQ0UsYUFBQTtFQUNBLFlBQUE7RUFDQSxZQUFBO0VBQ0EsZ0JBQUE7RUFDQSxTQUFBO0FBYko7QUFnQkk7RUFDRSxPQUFBO0VBQ0EsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsZ0JBQUE7QUFkTjtBQWdCTTtFQUNFLGFBQUE7RUFDQSw4QkFBQTtFQUNBLG1CQUFBO0VBQ0EsaUJBQUE7RUFDQSx5QkEzR0k7RUE0R0osV0FBQTtFQUNBLFlBQUE7QUFkUjtBQWdCUTtFQUNFLFNBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFFQSxjQW5IRztBQW9HYjtBQWtCUTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFFBQUE7RUFDQSxpQkFBQTtFQUNBLHVDQUFBO0VBQ0Esa0JBQUE7RUFDQSw2QkFBQTtFQUNBLGtCQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0Esa0JBQUE7RUFDQSxrRUFBQTtFQUNBLDZCQUFBO0VBQ0EscUJBQUE7RUFDQSxlQUFBO0VBQ0EseUJBQUE7QUFoQlY7QUFrQlU7RUFDRSxXQUFBO0VBQ0Esa0JBQUE7RUFDQSxNQUFBO0VBQ0EsT0FBQTtFQUNBLFFBQUE7RUFDQSxTQUFBO0VBQ0Esa0JBQUE7RUFDQSxZQUFBO0VBQ0EsNERBQUE7RUFDQSw4RUFDRTtFQUdGLHVCQUFBO0VBQ0Esb0JBQUE7QUFsQlo7QUFxQlU7RUFDRSxnREFBQTtBQW5CWjtBQXNCVTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0EsVUFBQTtBQXBCWjtBQXNCWTtFQUNFLGVBQUE7RUFDQSxzQkFBQTtBQXBCZDtBQTBCTTtFQUNFLE9BQUE7RUFDQSxnQkFBQTtFQUNBLFVBQUE7RUFDQSxhQUFBO0VBQ0Esc0JBQUE7RUFFQSxxQkFBQTtBQXpCUjtBQTJCUTtFQUNFLFVBQUE7RUFDQSxXQUFBO0FBekJWO0FBNEJRO0VBQ0UsMkNBQUE7RUFDQSxrQkFBQTtBQTFCVjtBQTZCUTtFQUNFLDJDQUFBO0VBQ0Esa0JBQUE7QUEzQlY7QUE4QlE7RUFDRSxpREFBQTtBQTVCVjtBQThCUTtFQUNFLGFBQUE7RUFDQSw4QkFBQTtFQUNBLG1CQUFBO0VBQ0EsU0FBQTtFQUNBLHlCQTVNRTtFQTZNRixtQkFBQTtBQTVCVjtBQTZCVTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFFBQUE7QUEzQlo7QUE2Qlk7RUFDRSxXQUFBO0VBQ0EsZUFBQTtFQUNBLFdBQUE7QUEzQmQ7QUFtQ0k7RUFDRSx1QkFBQTtFQUNBLGdCQUFBO0VBQ0Esa0JBQUE7QUFqQ047QUFtQ007RUFDRSxZQUFBO0VBQ0EsYUFBQTtFQUNBLHNCQUFBO0FBakNSO0FBcUNJO0VBQ0UsZUFBQTtFQUNBLG1CQUFBO0VBQ0Esa0JBQUE7RUFDQSw4REFBQTtFQUNBLHlCQUFBO0FBbkNOO0FBcUNNO0VBQ0UsY0FBQTtBQW5DUjtBQXFDUTtFQUNFLGFBQUE7QUFuQ1Y7QUF1Q007RUFDRSxtQkFBQTtFQUNBLGdDQUFBO0VBQ0EsYUFBQTtBQXJDUjtBQXVDUTtFQUNFLGFBQUE7RUFDQSxTQUFBO0VBQ0EsbUJBQUE7QUFyQ1Y7QUF1Q1U7O0VBRUUsZUFBQTtFQUNBLCtCQUFBO0FBckNaO0FBdUNZOztFQUNFLHFCQUFBO0FBcENkO0FBMENNO0VBQ0UsVUFBQTtFQUNBLFlBQUE7QUF4Q1I7QUEwQ1E7RUFDRSxhQUFBO0VBQ0EseUJBQUE7RUFDQSxnQkFBQTtBQXhDVjtBQTBDVTtFQUNFLGFBQUE7QUF4Q1o7QUE4Q0k7RUFDRSxTQUFBO0VBQ0EsMEJBQUE7RUFDQSxrQkFBQTtFQUNBLDhEQUFBO0FBNUNOO0FBK0NRO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsU0FBQTtFQUNBLG1CQUFBO0VBQ0EsY0ExU0c7QUE2UGI7QUFpRE07RUFDRSxVQUFBO0VBQ0EsWUFBQTtBQS9DUjtBQWlEUTtFQUNFLGFBQUE7RUFDQSxPQUFBO0VBQ0EsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsWUFBQTtFQUNBLGlCQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLGtCQUFBO0FBL0NWO0FBaURVO0VBQ0UsZ0JBQUE7QUEvQ1o7QUFrRFU7RUFDRSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSx3Q0FBQTtFQUNBLGlCQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLHVCQUFBO0FBaERaO0FBbURVO0VBQ0UsWUFBQTtFQUNBLGFBQUE7RUFDQSxzQkFBQTtFQUNBLE9BQUE7QUFqRFo7QUF1REk7RUFDRSxTQUFBO0VBQ0EsdUNBQUE7RUFDQSxrQkFBQTtFQUNBLDhEQUFBO0FBckROO0FBdURNO0VBQ0UsWUFBQTtFQUNBLGFBQUE7RUFDQSxzQkFBQTtBQXJEUjs7QUEwREE7RUFDRSxtQkFyV1U7RUFzV1YsWUFBQTtBQXZERjs7QUEwREE7RUFDRSxtQkFBQTtFQUNBLFdBQUE7QUF2REY7O0FBMERBO0VBQ0UsNkJBQUE7RUFDQSw2QkFBQTtFQUNBLHFCQUFBO0FBdkRGOztBQTBEQTtFQUlFLFlBQUE7QUExREY7O0FBNkRBO0VBQ0UsY0FBQTtFQUNBLHFCQUFBO0VBQ0EsZ0JBQUE7QUExREY7O0FBNkRBO0VBQ0UsY0FBQTtFQUNBLFlBQUE7QUExREY7O0FBNkRBO0VBQ0UsY0FBQTtFQUNBLFlBQUE7QUExREY7O0FBNkRBO0VBQ0UsZUFBQTtBQTFERjs7QUE4REE7RUFDRSxtQkFBQTtFQUNBLGFBQUE7RUFDQSxpQkFBQTtFQUNBLGtCQUFBO0VBQ0Esd0NBQUE7QUEzREY7O0FBOERBO0VBQ0UsbUJBQUE7QUEzREY7QUE2REU7RUFDRSxtQkFBQTtFQUNBLG1CQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EseUJBQUE7RUFDQSw2QkFBQTtBQTNESjtBQTZESTtFQUNFLG1CQUFBO0VBQ0EsOEJBQUE7QUEzRE47QUE4REk7RUFDRSxxQkFBQTtFQUNBLG1CQUFBO0FBNUROO0FBZ0VNO0VBQ0UsaUJBQUE7RUFDQSxnQkFBQTtBQTlEUjtBQWtFSTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFNBQUE7RUFDQSxlQUFBO0FBaEVOO0FBa0VNO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxpQkFBQTtFQUNBLGtCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSx3Q0FBQTtBQWhFUjtBQW1FTTtFQUNFLE9BQUE7RUFDQSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQkFBQTtBQWpFUjtBQW1FUTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxTQUFBO0FBakVWO0FBb0VRO0VBQ0UsZUFBQTtFQUNBLCtCQUFBO0FBbEVWO0FBb0VVO0VBQ0UseUJBQUE7QUFsRVo7QUF3RUk7RUFDRSxnQkFBQTtFQUNBLGFBQUE7RUFDQSxnQkFBQTtFQUNBLGdDQUFBO0FBdEVOO0FBd0VNO0VBQ0UsYUFBQTtFQUNBLDhCQUFBO0VBQ0EsY0FBQTtFQUNBLGVBQUE7RUFDQSxrQkFBQTtBQXRFUjtBQXdFUTtFQUNFLGNBQUE7RUFDQSxnQkFBQTtBQXRFVjtBQXlFUTtFQUNFLGNBQUE7RUFDQSxpQkFBQTtFQUNBLGdCQUFBO0VBQ0EsZ0JBQUE7RUFDQSx1QkFBQTtFQUNBLG1CQUFBO0FBdkVWO0FBMkVNO0VBQ0UsZ0JBQUE7QUF6RVI7QUE0RVU7RUFDRSxpQkFBQTtFQUNBLGdCQUFBO0VBQ0Esa0JBQUE7QUExRVo7QUE0RVk7RUFDRSxVQUFBO0FBMUVkO0FBNkVZO0VBQ0UsbUJBQUE7RUFDQSxrQkFBQTtBQTNFZDtBQThFWTtFQUNFLG1CQUFBO0VBQ0Esa0JBQUE7QUE1RWQ7QUFnRlU7RUFDRSxtQkFBQTtBQTlFWjtBQWdGWTtFQUNFLGFBQUE7QUE5RWQ7QUFpRlk7RUFDRSxjQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0FBL0VkO0FBa0ZZO0VBQ0Usa0JBQUE7QUFoRmQ7QUFrRmM7RUFDRSxXQUFBO0VBQ0EsZ0JBQUE7RUFDQSxpQkFBQTtFQUNBLHlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxpQkFBQTtBQWhGaEI7QUFrRmdCO0VBQ0UsYUFBQTtFQUNBLHFCQUFBO0FBaEZsQjtBQW1GZ0I7RUFDRSxtQkFBQTtFQUNBLGNBQUE7QUFqRmxCO0FBdUZjO0VBQ0UsMEJBQUE7RUFDQSxrQkFBQTtFQUNBLGFBQUE7RUFDQSxrQkFBQTtFQUNBLGVBQUE7RUFDQSx5QkFBQTtBQXJGaEI7QUF1RmdCO0VBQ0UscUJBQUE7RUFDQSxtQkFBQTtBQXJGbEI7QUF3RmdCO0VBQ0UsWUFBQTtFQUNBLG1CQUFBO0FBdEZsQjtBQXlGZ0I7RUFDRSxjQUFBO0VBQ0EsZUFBQTtFQUNBLGVBQUE7RUFDQSxjQUFBO0FBdkZsQjtBQTZGVTtFQUNFLGtCQUFBO0VBQ0EsZUFBQTtBQTNGWjtBQTZGWTtFQUNFLGdCQUFBO0VBQ0EsWUFBQTtFQUNBLGNBQUE7RUFDQSxlQUFBO0VBQ0EsZUFBQTtFQUNBLDBCQUFBO0FBM0ZkO0FBNkZjO0VBQ0UsY0FBQTtBQTNGaEI7O0FBcUdBO0VBQ0UsbUJBQUE7RUFDQSxpQkFBQTtFQUNBLG1CQUFBO0VBQ0EsYUFBQTtFQUNBLHdDQUFBO0FBbEdGO0FBb0dFO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsU0FBQTtFQUNBLG1CQUFBO0FBbEdKO0FBb0dJO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxtQkFBQTtFQUNBLGtCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSx3Q0FBQTtBQWxHTjtBQXFHSTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxTQUFBO0FBbkdOO0FBd0dJO0VBQ0UsbUJBQUE7QUF0R047QUF3R007RUFDRSxjQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0FBdEdSO0FBeUdNO0VBQ0Usa0JBQUE7QUF2R1I7QUF5R1E7RUFDRSxXQUFBO0VBQ0EsZ0JBQUE7RUFDQSw0QkFBQTtFQUNBLHlCQUFBO0VBQ0EsbUJBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxpQkFBQTtBQXZHVjtBQXlHVTtFQUNFLGFBQUE7RUFDQSxxQkFBQTtBQXZHWjtBQTBHVTtFQUNFLGNBQUE7QUF4R1o7QUE0R1E7RUFDRSxrQkFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0EsYUFBQTtFQUNBLFFBQUE7QUExR1Y7QUE0R1U7RUFDRSxlQUFBO0VBQ0EsK0JBQUE7QUExR1o7QUE0R1k7RUFDRSxxQkFBQTtBQTFHZDtBQWlIUTtFQUNFLDBCQUFBO0VBQ0EsbUJBQUE7RUFDQSxhQUFBO0VBQ0Esa0JBQUE7RUFDQSxlQUFBO0VBQ0EseUJBQUE7RUFDQSxtQkFBQTtBQS9HVjtBQWlIVTtFQUNFLHFCQUFBO0VBQ0EsbUJBQUE7QUEvR1o7QUFrSFU7RUFDRSxjQUFBO0VBQ0EsZUFBQTtFQUNBLGVBQUE7RUFDQSxjQUFBO0VBQ0EsZ0JBQUE7QUFoSFo7O0FBd0hBO0VBQ0Usa0JBQUE7RUFDQSxtQkFBQTtBQXJIRjtBQXVIRTtFQUNFLGNBQUE7RUFDQSxlQUFBO0VBQ0EsU0FBQTtBQXJISjs7QUF5SEE7RUFDRSxnQkFBQTtFQUNBLGlCQUFBO0FBdEhGIiwic291cmNlc0NvbnRlbnQiOlsiJGhlYWRlci1iZzogI2U5ZWZmZDtcclxuJG1haW4tY29sb3I6ICMxYTQ2YTc7XHJcblxyXG46Om5nLWRlZXAgLmF2YS10YWJzIHtcclxuICBiYWNrZ3JvdW5kOiBub25lICFpbXBvcnRhbnQ7XHJcbn1cclxuXHJcbjo6bmctZGVlcCAuYXZhLXRhYnNfX2NvbnRhaW5lciB7XHJcbiAgYm9yZGVyLXJhZGl1czogbm9uZSAhaW1wb3J0YW50O1xyXG4gIGJvcmRlcjogbm9uZSAhaW1wb3J0YW50O1xyXG4gIHBhZGRpbmc6IDAgIWltcG9ydGFudDtcclxuICBib3gtc2hhZG93OiBub25lICFpbXBvcnRhbnQ7XHJcbiAgYmFja2dyb3VuZDogbm9uZSAhaW1wb3J0YW50O1xyXG59XHJcblxyXG46Om5nLWRlZXAgLmF2YS10YWJzX19saXN0IHtcclxuICBwYWRkaW5nOiAwICFpbXBvcnRhbnQ7XHJcbn1cclxuXHJcbi53b3JrZmxvdy1leGVjdXRpb24tY29udGFpbmVyIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgaGVpZ2h0OiA5MyU7XHJcbiAgLmV4ZWN1dGlvbi1oZWFkZXIge1xyXG4gICAgZGlzcGxheTogZmxleDtcclxuICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcclxuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICBwYWRkaW5nOiAyMHB4IDI0cHg7XHJcbiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgdmFyKC0tYm9yZGVyLWNvbG9yLCAjZTBlMGUwKTtcclxuXHJcbiAgICAuZXhlY3V0aW9uLXRpdGxlIHtcclxuICAgICAgaDEge1xyXG4gICAgICAgIG1hcmdpbjogMDtcclxuICAgICAgICBmb250LXNpemU6IDI0cHg7XHJcbiAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgICAgICBjb2xvcjogdmFyKC0tdGV4dC1jb2xvciwgIzMzMyk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC5oZWFkZXItYnV0dG9ucyB7XHJcbiAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICBnYXA6IDEycHg7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAuZXhlY3V0aW9uLWFjdGlvbnMge1xyXG4gICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICBnYXA6IDEycHg7XHJcblxyXG4gICAgICAuYmFjay1idXR0b24sXHJcbiAgICAgIC5lZGl0LWJ1dHRvbiB7XHJcbiAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICAgIHBhZGRpbmc6IDhweCAxNnB4O1xyXG4gICAgICAgIGJvcmRlci1yYWRpdXM6IDVweDtcclxuICAgICAgICBmb250LXdlaWdodDogNTAwO1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMTRweDtcclxuICAgICAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZTtcclxuXHJcbiAgICAgICAgc3ZnIHtcclxuICAgICAgICAgIG1hcmdpbi1yaWdodDogNnB4O1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgLmJhY2stYnV0dG9uIHtcclxuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS1iZy1tdXRlZCwgI2Y1ZjVmNSk7XHJcbiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgdmFyKC0tYm9yZGVyLWNvbG9yLCAjZTBlMGUwKTtcclxuICAgICAgICBjb2xvcjogdmFyKC0tdGV4dC1jb2xvciwgIzMzMyk7XHJcblxyXG4gICAgICAgICY6aG92ZXIge1xyXG4gICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0tYmctbXV0ZWQtaG92ZXIsICNlOWU5ZTkpO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgLmVkaXQtYnV0dG9uIHtcclxuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS1jYXJkLWJnLCAjZmZmKTtcclxuICAgICAgICBib3JkZXI6IDFweCBzb2xpZCB2YXIoLS1ib3JkZXItY29sb3IsICNlMGUwZTApO1xyXG4gICAgICAgIGNvbG9yOiB2YXIoLS10ZXh0LWNvbG9yLCAjMzMzKTtcclxuXHJcbiAgICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS1jYXJkLWJnLWhvdmVyLCAjZjlmOWY5KTtcclxuICAgICAgICAgIGJvcmRlci1jb2xvcjogdmFyKC0tYm9yZGVyLWNvbG9yLWRhcmssICNkMGQwZDApO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLy8gTWFpbiBjb250ZW50IHdpdGggMyBjb2x1bW5zXHJcbiAgLmV4ZWN1dGlvbi1jb250ZW50IHtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBmbGV4LWdyb3c6IDE7XHJcbiAgICBoZWlnaHQ6IDk1dmg7XHJcbiAgICBvdmVyZmxvdzogaGlkZGVuO1xyXG4gICAgZ2FwOiAyMHB4O1xyXG5cclxuICAgIC8vIENvbW1vbiBjb2x1bW4gc3R5bGluZ1xyXG4gICAgLmNvbHVtbiB7XHJcbiAgICAgIGZsZXg6IDE7XHJcbiAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgICAgIG92ZXJmbG93OiBoaWRkZW47XHJcblxyXG4gICAgICAuY29sdW1uLWhlYWRlciB7XHJcbiAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XHJcbiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgICBwYWRkaW5nOiA4cHggMTZweDtcclxuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAkaGVhZGVyLWJnO1xyXG4gICAgICAgIGNvbG9yOiAjZmZmO1xyXG4gICAgICAgIGhlaWdodDogNjRweDtcclxuXHJcbiAgICAgICAgaDIge1xyXG4gICAgICAgICAgbWFyZ2luOiAwO1xyXG4gICAgICAgICAgZm9udC1zaXplOiAyMHB4O1xyXG4gICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgICAgICAgIC8vIGNvbG9yOiB2YXIoLS10ZXh0LWNvbG9yLCAjMzMzKTtcclxuICAgICAgICAgIGNvbG9yOiAkbWFpbi1jb2xvcjtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC5pY29uLWJ1dHRvbiB7XHJcbiAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgICAgIGdhcDogNnB4O1xyXG4gICAgICAgICAgcGFkZGluZzogOHB4IDE2cHg7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS1jYXJkLWJnLCB3aGl0ZSk7XHJcbiAgICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcbiAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCB0cmFuc3BhcmVudDtcclxuICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICAgICAgICAgIGZvbnQtc2l6ZTogMTRweDtcclxuICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICAgICAgICBjb2xvcjogdHJhbnNwYXJlbnQ7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kLWltYWdlOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsICM2NTY2Y2QgMCUsICNmOTZjYWIgMTAwJSk7XHJcbiAgICAgICAgICAtd2Via2l0LWJhY2tncm91bmQtY2xpcDogdGV4dDtcclxuICAgICAgICAgIGJhY2tncm91bmQtY2xpcDogdGV4dDtcclxuICAgICAgICAgIGN1cnNvcjogcG9pbnRlcjtcclxuICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7XHJcblxyXG4gICAgICAgICAgJjo6YmVmb3JlIHtcclxuICAgICAgICAgICAgY29udGVudDogXCJcIjtcclxuICAgICAgICAgICAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gICAgICAgICAgICB0b3A6IDA7XHJcbiAgICAgICAgICAgIGxlZnQ6IDA7XHJcbiAgICAgICAgICAgIHJpZ2h0OiAwO1xyXG4gICAgICAgICAgICBib3R0b206IDA7XHJcbiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICAgICAgICAgICAgcGFkZGluZzogMXB4O1xyXG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsICM2NTY2Y2QgMCUsICNmOTZjYWIgMTAwJSk7XHJcbiAgICAgICAgICAgIC13ZWJraXQtbWFzazpcclxuICAgICAgICAgICAgICBsaW5lYXItZ3JhZGllbnQoI2ZmZiAwIDApIGNvbnRlbnQtYm94LFxyXG4gICAgICAgICAgICAgIGxpbmVhci1ncmFkaWVudCgjZmZmIDAgMCk7XHJcbiAgICAgICAgICAgIC13ZWJraXQtbWFzay1jb21wb3NpdGU6IHhvcjtcclxuICAgICAgICAgICAgbWFzay1jb21wb3NpdGU6IGV4Y2x1ZGU7XHJcbiAgICAgICAgICAgIHBvaW50ZXItZXZlbnRzOiBub25lO1xyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICY6aG92ZXIge1xyXG4gICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS1iZy1tdXRlZC1ob3ZlciwgI2Y5ZjVmZik7XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgc3ZnIHtcclxuICAgICAgICAgICAgd2lkdGg6IDE4cHg7XHJcbiAgICAgICAgICAgIGhlaWdodDogMThweDtcclxuICAgICAgICAgICAgZmlsbDogbm9uZTtcclxuXHJcbiAgICAgICAgICAgIHBhdGgge1xyXG4gICAgICAgICAgICAgIHN0cm9rZS13aWR0aDogMjtcclxuICAgICAgICAgICAgICBzdHJva2U6IHVybCgjZ3JhZGllbnQpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICAuY29sdW1uLWNvbnRlbnQge1xyXG4gICAgICAgIGZsZXg6IDE7XHJcbiAgICAgICAgb3ZlcmZsb3cteTogYXV0bztcclxuICAgICAgICBwYWRkaW5nOiAwO1xyXG4gICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICAgICAgICAvLyBoZWlnaHQ6IDEwMCU7XHJcbiAgICAgICAgc2Nyb2xsYmFyLXdpZHRoOiBub25lO1xyXG5cclxuICAgICAgICAmOjotd2Via2l0LXNjcm9sbGJhciB7XHJcbiAgICAgICAgICB3aWR0aDogOHB4O1xyXG4gICAgICAgICAgaGVpZ2h0OiA4cHg7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAmOjotd2Via2l0LXNjcm9sbGJhci10cmFjayB7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1zY3JvbGxiYXItdHJhY2ssICNmMWYxZjEpO1xyXG4gICAgICAgICAgYm9yZGVyLXJhZGl1czogNHB4O1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgJjo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWIge1xyXG4gICAgICAgICAgYmFja2dyb3VuZDogdmFyKC0tc2Nyb2xsYmFyLXRodW1iLCAjZDFkMWQxKTtcclxuICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDRweDtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgICY6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iOmhvdmVyIHtcclxuICAgICAgICAgIGJhY2tncm91bmQ6IHZhcigtLXNjcm9sbGJhci10aHVtYi1ob3ZlciwgI2IxYjFiMSk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIC5uYXYtd3JhcHBlciB7XHJcbiAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xyXG4gICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgICAgIGdhcDogMTZweDtcclxuICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICRoZWFkZXItYmc7XHJcbiAgICAgICAgICBwYWRkaW5nOiAwIDE1cHggMCAwO1xyXG4gICAgICAgICAgLnJpZ2h0LWlubmVyLXdyYXBwZXIge1xyXG4gICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICAgICAgICBnYXA6IDhweDtcclxuXHJcbiAgICAgICAgICAgIHAge1xyXG4gICAgICAgICAgICAgIGNvbG9yOiAjZmZmO1xyXG4gICAgICAgICAgICAgIHBhZGRpbmc6IDAgMTBweDtcclxuICAgICAgICAgICAgICBtYXJnaW46IDBweDtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC8vIFNwZWNpZmljIGNvbHVtbiBzdHlsZXMgZm9yIGNoaWxkIGNvbXBvbmVudHNcclxuICAgIC5hY3Rpdml0eS1jb2x1bW4ge1xyXG4gICAgICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDtcclxuICAgICAgYm94LXNoYWRvdzogbm9uZTtcclxuICAgICAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG5cclxuICAgICAgYXBwLWFnZW50LWFjdGl2aXR5IHtcclxuICAgICAgICBoZWlnaHQ6IDEwMCU7XHJcbiAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLmV4ZWN1dGlvbi1wYW5lbC1jb2x1bW4ge1xyXG4gICAgICBmbGV4OiAwIDAgNDAwcHg7XHJcbiAgICAgIGJhY2tncm91bmQ6ICNGMEY0RkY7XHJcbiAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICAgICAgYm94LXNoYWRvdzogMCA0cHggMTVweCB2YXIoLS1jYXJkLXNoYWRvdywgcmdiYSgwLCAwLCAwLCAwLjA1KSk7XHJcbiAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XHJcblxyXG4gICAgICAmLmNvbGxhcHNlZCB7XHJcbiAgICAgICAgZmxleDogMCAwIDYwcHg7XHJcblxyXG4gICAgICAgIC5wYW5lbC1jb250ZW50IHtcclxuICAgICAgICAgIGRpc3BsYXk6IG5vbmU7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICAuY29sdW1uLWhlYWRlciB7XHJcbiAgICAgICAgYmFja2dyb3VuZDogI0YwRjRGRjtcclxuICAgICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI0U1RTdFQjtcclxuICAgICAgICBwYWRkaW5nOiAxNnB4O1xyXG5cclxuICAgICAgICAuaGVhZGVyLWxlZnQge1xyXG4gICAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICAgIGdhcDogMTJweDtcclxuICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcblxyXG4gICAgICAgICAgLmJhY2staWNvbixcclxuICAgICAgICAgIC5wYW5lbC1pY29uIHtcclxuICAgICAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICAgICAgICAgICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4ycyBlYXNlO1xyXG5cclxuICAgICAgICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxLjEpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICAuZXhlY3V0aW9uLXBhbmVsLWNvbnRlbnQge1xyXG4gICAgICAgIHBhZGRpbmc6IDA7XHJcbiAgICAgICAgaGVpZ2h0OiAxMDAlO1xyXG5cclxuICAgICAgICAucGFuZWwtY29udGVudCB7XHJcbiAgICAgICAgICBwYWRkaW5nOiAyMHB4O1xyXG4gICAgICAgICAgaGVpZ2h0OiBjYWxjKDEwMCUgLSA4MHB4KTtcclxuICAgICAgICAgIG92ZXJmbG93LXk6IGF1dG87XHJcblxyXG4gICAgICAgICAgJi5oaWRkZW4ge1xyXG4gICAgICAgICAgICBkaXNwbGF5OiBub25lO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC5wbGF5Z3JvdW5kLWNvbHVtbiB7XHJcbiAgICAgIGZsZXg6IDMuNTtcclxuICAgICAgYmFja2dyb3VuZDogdmFyKC0tY2FyZC1iZyk7XHJcbiAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICAgICAgYm94LXNoYWRvdzogMCA0cHggMTVweCB2YXIoLS1jYXJkLXNoYWRvdywgcmdiYSgwLCAwLCAwLCAwLjA1KSk7XHJcblxyXG4gICAgICAuY29sdW1uLWhlYWRlciB7XHJcbiAgICAgICAgLmhlYWRlci1idXR0b25zIHtcclxuICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICBmbGV4LWRpcmVjdGlvbjogcm93O1xyXG4gICAgICAgICAgZ2FwOiAxMnB4O1xyXG4gICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgICAgIGNvbG9yOiAkbWFpbi1jb2xvcjtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC5wbGF5Z3JvdW5kLWNvbnRlbnQge1xyXG4gICAgICAgIHBhZGRpbmc6IDA7XHJcbiAgICAgICAgaGVpZ2h0OiAxMDAlO1xyXG5cclxuICAgICAgICAuY2hhdC1jb250YWluZXIge1xyXG4gICAgICAgICAgcGFkZGluZzogMjBweDtcclxuICAgICAgICAgIGZsZXg6IDE7XHJcbiAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICAgICAgICAgIGhlaWdodDogMTAwJTtcclxuICAgICAgICAgIG1pbi1oZWlnaHQ6IDQwMHB4O1xyXG4gICAgICAgICAgYm9yZGVyLXJhZGl1czogMTJweDtcclxuICAgICAgICAgIG92ZXJmbG93OiBoaWRkZW47XHJcbiAgICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcblxyXG4gICAgICAgICAgOjpuZy1kZWVwIC5wcm9tcHQtY29udGFpbmVyIHtcclxuICAgICAgICAgICAgbWFyZ2luLXRvcDogMzBweDtcclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAucGxheWdyb3VuZC10aXRsZSB7XHJcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTRweDtcclxuICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgICAgICAgICAgY29sb3I6IHZhcigtLWRhc2hib2FyZC1wcmltYXJ5LCAjNjU2NmNkKTtcclxuICAgICAgICAgICAgbWFyZ2luOiAxNnB4IDE2cHg7XHJcbiAgICAgICAgICAgIHdoaXRlLXNwYWNlOiBub3dyYXA7XHJcbiAgICAgICAgICAgIG92ZXJmbG93OiBoaWRkZW47XHJcbiAgICAgICAgICAgIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzO1xyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIGFwcC1jaGF0LWludGVyZmFjZSB7XHJcbiAgICAgICAgICAgIGhlaWdodDogMTAwJTtcclxuICAgICAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICAgICAgICAgICAgZmxleDogMTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAub3V0cHV0LWNvbHVtbiB7XHJcbiAgICAgIGZsZXg6IDUuNTtcclxuICAgICAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0tY2FyZC1iZywgd2hpdGUpO1xyXG4gICAgICBib3JkZXItcmFkaXVzOiA4cHg7XHJcbiAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDE1cHggdmFyKC0tY2FyZC1zaGFkb3csIHJnYmEoMCwgMCwgMCwgMC4wNSkpO1xyXG5cclxuICAgICAgYXBwLWFnZW50LW91dHB1dCB7XHJcbiAgICAgICAgaGVpZ2h0OiAxMDAlO1xyXG4gICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxufVxyXG46Om5nLWRlZXAgbmF2LmF2YS10YWJzX19saXN0IHtcclxuICBiYWNrZ3JvdW5kOiAkaGVhZGVyLWJnO1xyXG4gIHBhZGRpbmc6IDRweDtcclxufVxyXG5cclxuOjpuZy1kZWVwIGJ1dHRvbi5hdmEtYnV0dG9uLnByaW1hcnkuYWN0aXZlIHtcclxuICBiYWNrZ3JvdW5kOiAjNjE2MTYxO1xyXG4gIGNvbG9yOiAjZmZmO1xyXG59XHJcblxyXG46Om5nLWRlZXAgLmNvbHVtbi1oZWFkZXIgLmF2YS10YWJzW2RhdGEtdmFyaWFudD1cImJ1dHRvblwiXSAuYXZhLXRhYnNfX3RhYi0tcGlsbCB7XHJcbiAgYm9yZGVyLXJhZGl1czogOHB4ICFpbXBvcnRhbnQ7XHJcbiAgcGFkZGluZzogMTJweCAxNnB4ICFpbXBvcnRhbnQ7XHJcbiAgZm9udC1mYW1pbHk6IFwiTXVsaXNoXCI7XHJcbn1cclxuXHJcbjo6bmctZGVlcFxyXG4gIC5hdmEtdGFic1tkYXRhLXZhcmlhbnQ9XCJidXR0b25cIl1cclxuICAuYXZhLXRhYnNfX3RhYi0tYWN0aXZlXHJcbiAgLmF2YS10YWJzX190YWItdGV4dCB7XHJcbiAgY29sb3I6IHdoaXRlO1xyXG59XHJcblxyXG46Om5nLWRlZXAgLmF2YS10YWJzX190YWItdGV4dCB7XHJcbiAgY29sb3I6ICM0YzUxNWI7XHJcbiAgZm9udC1mYW1pbHk6IFwiTXVsaXNoXCI7XHJcbiAgZm9udC13ZWlnaHQ6IDYwMDtcclxufVxyXG5cclxuOjpuZy1kZWVwIC5yaWdodC1zZWN0aW9uLWhlYWRlciAuYXZhLWJ1dHRvbi5zZWNvbmRhcnkge1xyXG4gIGNvbG9yOiAjMWE0NmE3O1xyXG4gIGJvcmRlcjogbm9uZTtcclxufVxyXG5cclxuOjpuZy1kZWVwIC5yaWdodC1zZWN0aW9uLWhlYWRlciAuYXZhLWJ1dHRvbi5zZWNvbmRhcnk6aG92ZXIge1xyXG4gIGNvbG9yOiAjMWE0NmE3O1xyXG4gIGJvcmRlcjogbm9uZTtcclxufVxyXG5cclxuLnJpZ2h0LXNlY3Rpb24taGVhZGVyIHtcclxuICB0ZXh0LWFsaWduOiBlbmQ7XHJcbn1cclxuXHJcbi8vIEV4ZWN1dGlvbiBQYW5lbCBTdHlsZXNcclxuLnN0ZXBwZXItc2VjdGlvbiB7XHJcbiAgbWFyZ2luLWJvdHRvbTogMjBweDtcclxuICBwYWRkaW5nOiAxNnB4O1xyXG4gIGJhY2tncm91bmQ6IHdoaXRlO1xyXG4gIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICBib3gtc2hhZG93OiAwIDJweCA0cHggcmdiYSgwLCAwLCAwLCAwLjEpO1xyXG59XHJcblxyXG4uYWdlbnRzLXNlY3Rpb24ge1xyXG4gIG1hcmdpbi1ib3R0b206IDI0cHg7XHJcblxyXG4gIC5hZ2VudC1jYXJkIHtcclxuICAgIGJhY2tncm91bmQ6ICNFOEYwRkU7XHJcbiAgICBib3JkZXItcmFkaXVzOiAxMnB4O1xyXG4gICAgcGFkZGluZzogMTZweDtcclxuICAgIG1hcmdpbi1ib3R0b206IDEycHg7XHJcbiAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xyXG4gICAgYm9yZGVyOiAycHggc29saWQgdHJhbnNwYXJlbnQ7XHJcblxyXG4gICAgJi5jb21wbGV0ZWQge1xyXG4gICAgICBiYWNrZ3JvdW5kOiAjRjBGOUZGO1xyXG4gICAgICBib3JkZXItbGVmdDogNHB4IHNvbGlkICMyMkM1NUU7XHJcbiAgICB9XHJcblxyXG4gICAgJi5hY3RpdmUge1xyXG4gICAgICBib3JkZXItY29sb3I6ICMxQTQ2QTc7XHJcbiAgICAgIGJhY2tncm91bmQ6ICNGMEY0RkY7XHJcbiAgICB9XHJcblxyXG4gICAgJi5leHBhbmRlZCB7XHJcbiAgICAgIC5hZ2VudC1kZXRhaWxzIHtcclxuICAgICAgICBtYXgtaGVpZ2h0OiA1MDBweDtcclxuICAgICAgICBvdmVyZmxvdy15OiBhdXRvO1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLmFnZW50LWhlYWRlciB7XHJcbiAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgIGdhcDogMTJweDtcclxuICAgICAgY3Vyc29yOiBwb2ludGVyO1xyXG5cclxuICAgICAgLnN0YXR1cy1pY29uIHtcclxuICAgICAgICB3aWR0aDogMzJweDtcclxuICAgICAgICBoZWlnaHQ6IDMycHg7XHJcbiAgICAgICAgYmFja2dyb3VuZDogd2hpdGU7XHJcbiAgICAgICAgYm9yZGVyLXJhZGl1czogNTAlO1xyXG4gICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICAgICAgICBib3gtc2hhZG93OiAwIDJweCA0cHggcmdiYSgwLCAwLCAwLCAwLjEpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAuYWdlbnQtaW5mbyB7XHJcbiAgICAgICAgZmxleDogMTtcclxuICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcclxuICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG5cclxuICAgICAgICAuYWdlbnQtbmFtZSB7XHJcbiAgICAgICAgICBmb250LXNpemU6IDE2cHg7XHJcbiAgICAgICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICAgICAgY29sb3I6ICMxRjI5Mzc7XHJcbiAgICAgICAgICBtYXJnaW46IDA7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAuZXhwYW5kLWljb24ge1xyXG4gICAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICAgICAgICAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuMnMgZWFzZTtcclxuXHJcbiAgICAgICAgICAmLmV4cGFuZGVkIHtcclxuICAgICAgICAgICAgdHJhbnNmb3JtOiByb3RhdGUoMTgwZGVnKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAuYWdlbnQtZGV0YWlscyB7XHJcbiAgICAgIG1hcmdpbi10b3A6IDEycHg7XHJcbiAgICAgIG1heC1oZWlnaHQ6IDA7XHJcbiAgICAgIG92ZXJmbG93OiBoaWRkZW47XHJcbiAgICAgIHRyYW5zaXRpb246IG1heC1oZWlnaHQgMC4zcyBlYXNlO1xyXG5cclxuICAgICAgLmFnZW50LWRldGFpbC1pdGVtIHtcclxuICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcclxuICAgICAgICBwYWRkaW5nOiA0cHggMDtcclxuICAgICAgICBmb250LXNpemU6IDE0cHg7XHJcbiAgICAgICAgbWFyZ2luLWJvdHRvbTogOHB4O1xyXG5cclxuICAgICAgICAuZGV0YWlsLWxhYmVsIHtcclxuICAgICAgICAgIGNvbG9yOiAjNkI3MjgwO1xyXG4gICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC5kZXRhaWwtdmFsdWUge1xyXG4gICAgICAgICAgY29sb3I6ICMxRjI5Mzc7XHJcbiAgICAgICAgICB0ZXh0LWFsaWduOiByaWdodDtcclxuICAgICAgICAgIG1heC13aWR0aDogMjAwcHg7XHJcbiAgICAgICAgICBvdmVyZmxvdzogaGlkZGVuO1xyXG4gICAgICAgICAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7XHJcbiAgICAgICAgICB3aGl0ZS1zcGFjZTogbm93cmFwO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgLmFnZW50LWlucHV0cyB7XHJcbiAgICAgICAgbWFyZ2luLXRvcDogMTZweDtcclxuXHJcbiAgICAgICAgLmlucHV0cy1jb250YWluZXIge1xyXG4gICAgICAgICAgJi5zY3JvbGxhYmxlIHtcclxuICAgICAgICAgICAgbWF4LWhlaWdodDogMzAwcHg7XHJcbiAgICAgICAgICAgIG92ZXJmbG93LXk6IGF1dG87XHJcbiAgICAgICAgICAgIHBhZGRpbmctcmlnaHQ6IDhweDtcclxuXHJcbiAgICAgICAgICAgICY6Oi13ZWJraXQtc2Nyb2xsYmFyIHtcclxuICAgICAgICAgICAgICB3aWR0aDogNHB4O1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAmOjotd2Via2l0LXNjcm9sbGJhci10cmFjayB7XHJcbiAgICAgICAgICAgICAgYmFja2dyb3VuZDogI2YxZjFmMTtcclxuICAgICAgICAgICAgICBib3JkZXItcmFkaXVzOiAycHg7XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICY6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHtcclxuICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAjYzFjMWMxO1xyXG4gICAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDJweDtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIC5pbnB1dC1maWVsZCB7XHJcbiAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDEycHg7XHJcblxyXG4gICAgICAgICAgICAmLmhpZGRlbiB7XHJcbiAgICAgICAgICAgICAgZGlzcGxheTogbm9uZTtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgLmlucHV0LWxhYmVsIHtcclxuICAgICAgICAgICAgICBkaXNwbGF5OiBibG9jaztcclxuICAgICAgICAgICAgICBmb250LXNpemU6IDEycHg7XHJcbiAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgICAgICAgICAgICBjb2xvcjogIzM3NDE1MTtcclxuICAgICAgICAgICAgICBtYXJnaW4tYm90dG9tOiA2cHg7XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIC50ZXh0LWlucHV0LWNvbnRhaW5lciB7XHJcbiAgICAgICAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG5cclxuICAgICAgICAgICAgICAuaW5wdXQtdGV4dGFyZWEge1xyXG4gICAgICAgICAgICAgICAgd2lkdGg6IDEwMCU7XHJcbiAgICAgICAgICAgICAgICBtaW4taGVpZ2h0OiA2MHB4O1xyXG4gICAgICAgICAgICAgICAgcGFkZGluZzogOHB4IDEycHg7XHJcbiAgICAgICAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjRDFENURCO1xyXG4gICAgICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogNnB4O1xyXG4gICAgICAgICAgICAgICAgZm9udC1zaXplOiAxM3B4O1xyXG4gICAgICAgICAgICAgICAgcmVzaXplOiB2ZXJ0aWNhbDtcclxuICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IHdoaXRlO1xyXG5cclxuICAgICAgICAgICAgICAgICY6Zm9jdXMge1xyXG4gICAgICAgICAgICAgICAgICBvdXRsaW5lOiBub25lO1xyXG4gICAgICAgICAgICAgICAgICBib3JkZXItY29sb3I6ICMxQTQ2QTc7XHJcbiAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgJjpkaXNhYmxlZCB7XHJcbiAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICNGOUZBRkI7XHJcbiAgICAgICAgICAgICAgICAgIGNvbG9yOiAjNkI3MjgwO1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgLmltYWdlLWlucHV0LWNvbnRhaW5lciB7XHJcbiAgICAgICAgICAgICAgLmZpbGUtdXBsb2FkLWFyZWEge1xyXG4gICAgICAgICAgICAgICAgYm9yZGVyOiAycHggZGFzaGVkICNEMUQ1REI7XHJcbiAgICAgICAgICAgICAgICBib3JkZXItcmFkaXVzOiA2cHg7XHJcbiAgICAgICAgICAgICAgICBwYWRkaW5nOiAxNnB4O1xyXG4gICAgICAgICAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG4gICAgICAgICAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICAgICAgICAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZTtcclxuXHJcbiAgICAgICAgICAgICAgICAmOmhvdmVyOm5vdCguZGlzYWJsZWQpIHtcclxuICAgICAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAjMUE0NkE3O1xyXG4gICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAjRjlGQUZCO1xyXG4gICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICYuZGlzYWJsZWQge1xyXG4gICAgICAgICAgICAgICAgICBvcGFjaXR5OiAwLjU7XHJcbiAgICAgICAgICAgICAgICAgIGN1cnNvcjogbm90LWFsbG93ZWQ7XHJcbiAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgc3BhbiB7XHJcbiAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IGJsb2NrO1xyXG4gICAgICAgICAgICAgICAgICBtYXJnaW4tdG9wOiA4cHg7XHJcbiAgICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMTNweDtcclxuICAgICAgICAgICAgICAgICAgY29sb3I6ICM2QjcyODA7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgLnNob3ctbW9yZS10b2dnbGUge1xyXG4gICAgICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcbiAgICAgICAgICAgIG1hcmdpbi10b3A6IDhweDtcclxuXHJcbiAgICAgICAgICAgIC50b2dnbGUtYnRuIHtcclxuICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBub25lO1xyXG4gICAgICAgICAgICAgIGJvcmRlcjogbm9uZTtcclxuICAgICAgICAgICAgICBjb2xvcjogIzFBNDZBNztcclxuICAgICAgICAgICAgICBmb250LXNpemU6IDEycHg7XHJcbiAgICAgICAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICAgICAgICAgICAgIHRleHQtZGVjb3JhdGlvbjogdW5kZXJsaW5lO1xyXG5cclxuICAgICAgICAgICAgICAmOmhvdmVyIHtcclxuICAgICAgICAgICAgICAgIGNvbG9yOiAjMUU0MEFGO1xyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLmN1cnJlbnQtaW5wdXQtc2VjdGlvbiB7XHJcbiAgbWFyZ2luLWJvdHRvbTogMjRweDtcclxuICBiYWNrZ3JvdW5kOiB3aGl0ZTtcclxuICBib3JkZXItcmFkaXVzOiAxMnB4O1xyXG4gIHBhZGRpbmc6IDE2cHg7XHJcbiAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcclxuXHJcbiAgLmlucHV0LWhlYWRlciB7XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgIGdhcDogMTJweDtcclxuICAgIG1hcmdpbi1ib3R0b206IDE2cHg7XHJcblxyXG4gICAgLnN0YXR1cy1pY29uIHtcclxuICAgICAgd2lkdGg6IDMycHg7XHJcbiAgICAgIGhlaWdodDogMzJweDtcclxuICAgICAgYmFja2dyb3VuZDogIzFBNDZBNztcclxuICAgICAgYm9yZGVyLXJhZGl1czogNTAlO1xyXG4gICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICAgICAgYm94LXNoYWRvdzogMCAycHggNHB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcclxuICAgIH1cclxuXHJcbiAgICAuaW5wdXQtdGl0bGUge1xyXG4gICAgICBmb250LXNpemU6IDE2cHg7XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgIGNvbG9yOiAjMUYyOTM3O1xyXG4gICAgICBtYXJnaW46IDA7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAuY3VycmVudC1pbnB1dC1jb250YWluZXIge1xyXG4gICAgLmlucHV0LWZpZWxkIHtcclxuICAgICAgbWFyZ2luLWJvdHRvbTogMTZweDtcclxuXHJcbiAgICAgIC5pbnB1dC1sYWJlbCB7XHJcbiAgICAgICAgZGlzcGxheTogYmxvY2s7XHJcbiAgICAgICAgZm9udC1zaXplOiAxNHB4O1xyXG4gICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICAgICAgY29sb3I6ICMzNzQxNTE7XHJcbiAgICAgICAgbWFyZ2luLWJvdHRvbTogOHB4O1xyXG4gICAgICB9XHJcblxyXG4gICAgICAuaW5wdXQtY29udGFpbmVyIHtcclxuICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcblxyXG4gICAgICAgIC5pbnB1dC10ZXh0YXJlYSB7XHJcbiAgICAgICAgICB3aWR0aDogMTAwJTtcclxuICAgICAgICAgIG1pbi1oZWlnaHQ6IDgwcHg7XHJcbiAgICAgICAgICBwYWRkaW5nOiAxMnB4IDUwcHggMTJweCAxNnB4O1xyXG4gICAgICAgICAgYm9yZGVyOiAycHggc29saWQgI0U1RTdFQjtcclxuICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDEycHg7XHJcbiAgICAgICAgICBmb250LXNpemU6IDE0cHg7XHJcbiAgICAgICAgICByZXNpemU6IHZlcnRpY2FsO1xyXG4gICAgICAgICAgYmFja2dyb3VuZDogd2hpdGU7XHJcblxyXG4gICAgICAgICAgJjpmb2N1cyB7XHJcbiAgICAgICAgICAgIG91dGxpbmU6IG5vbmU7XHJcbiAgICAgICAgICAgIGJvcmRlci1jb2xvcjogIzFBNDZBNztcclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAmOjpwbGFjZWhvbGRlciB7XHJcbiAgICAgICAgICAgIGNvbG9yOiAjOUNBM0FGO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLmlucHV0LWFjdGlvbnMge1xyXG4gICAgICAgICAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gICAgICAgICAgcmlnaHQ6IDEycHg7XHJcbiAgICAgICAgICBib3R0b206IDEycHg7XHJcbiAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgICAgZ2FwOiA4cHg7XHJcblxyXG4gICAgICAgICAgLnNlbmQtaWNvbiB7XHJcbiAgICAgICAgICAgIGN1cnNvcjogcG9pbnRlcjtcclxuICAgICAgICAgICAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuMnMgZWFzZTtcclxuXHJcbiAgICAgICAgICAgICY6aG92ZXIge1xyXG4gICAgICAgICAgICAgIHRyYW5zZm9ybTogc2NhbGUoMS4xKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgLmltYWdlLWlucHV0LWNvbnRhaW5lciB7XHJcbiAgICAgICAgLmZpbGUtdXBsb2FkLWFyZWEge1xyXG4gICAgICAgICAgYm9yZGVyOiAycHggZGFzaGVkICNEMUQ1REI7XHJcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiAxMnB4O1xyXG4gICAgICAgICAgcGFkZGluZzogMjRweDtcclxuICAgICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICAgICAgICAgIGN1cnNvcjogcG9pbnRlcjtcclxuICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kOiAjRjlGQUZCO1xyXG5cclxuICAgICAgICAgICY6aG92ZXIge1xyXG4gICAgICAgICAgICBib3JkZXItY29sb3I6ICMxQTQ2QTc7XHJcbiAgICAgICAgICAgIGJhY2tncm91bmQ6ICNGMEY0RkY7XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgc3BhbiB7XHJcbiAgICAgICAgICAgIGRpc3BsYXk6IGJsb2NrO1xyXG4gICAgICAgICAgICBtYXJnaW4tdG9wOiA4cHg7XHJcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTRweDtcclxuICAgICAgICAgICAgY29sb3I6ICM2QjcyODA7XHJcbiAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4uaW5mby1tZXNzYWdlIHtcclxuICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcbiAgbWFyZ2luLWJvdHRvbTogMjRweDtcclxuXHJcbiAgcCB7XHJcbiAgICBjb2xvcjogIzFBNDZBNztcclxuICAgIGZvbnQtc2l6ZTogMTRweDtcclxuICAgIG1hcmdpbjogMDtcclxuICB9XHJcbn1cclxuXHJcbi5leGVjdXRlLXNlY3Rpb24ge1xyXG4gIG1hcmdpbi10b3A6IGF1dG87XHJcbiAgcGFkZGluZy10b3A6IDI0cHg7XHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n  return WorkflowExecutionComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "Subject", "takeUntil", "FormsModule", "ReactiveFormsModule", "Validators", "ChatInterfaceComponent", "AgentActivityComponent", "AgentOutputComponent", "AvaStepperComponent", "ButtonComponent", "IconComponent", "TabsComponent", "environment", "workflowConstants", "ExecutionStatus", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "agent_r4", "agent", "llm", "modelDeploymentName", "ctx_r2", "getCombinedTools", "ɵɵelement", "ɵɵproperty", "input_r5", "input", "label", "currentStepperStep", "i_r2", "ɵɵlistener", "WorkflowExecutionComponent_ng_container_17_div_12_ng_container_2_div_5_Template_div_click_1_listener", "ɵɵrestoreView", "_r6", "ɵɵnextContext", "$implicit", "ɵɵresetView", "triggerFileInput", "WorkflowExecutionComponent_ng_container_17_div_12_ng_container_2_div_5_Template_input_change_5_listener", "$event", "onImageSelected", "ɵɵclassProp", "ɵɵelementContainerStart", "ɵɵtemplate", "WorkflowExecutionComponent_ng_container_17_div_12_ng_container_2_div_4_Template", "WorkflowExecutionComponent_ng_container_17_div_12_ng_container_2_div_5_Template", "inputIndex_r7", "showAllInputs", "workflowForm", "isImageInput", "WorkflowExecutionComponent_ng_container_17_div_12_div_3_Template_button_click_1_listener", "_r8", "index", "toggleShowAllInputs", "ɵɵtextInterpolate1", "getAgentInputs", "length", "WorkflowExecutionComponent_ng_container_17_div_12_ng_container_2_Template", "WorkflowExecutionComponent_ng_container_17_div_12_div_3_Template", "WorkflowExecutionComponent_ng_container_17_Template_div_click_2_listener", "_r1", "toggleAgentExpansion", "WorkflowExecutionComponent_ng_container_17_div_10_Template", "WorkflowExecutionComponent_ng_container_17_div_11_Template", "WorkflowExecutionComponent_ng_container_17_div_12_Template", "agentStatuses", "completed", "expandedAgents", "name", "tools", "userTools", "WorkflowExecutionComponent_div_18_ng_container_7_div_4_Template_textarea_keydown_enter_1_listener", "_r9", "onInputSubmit", "WorkflowExecutionComponent_div_18_ng_container_7_div_4_Template_ava_icon_click_3_listener", "input_r10", "WorkflowExecutionComponent_div_18_ng_container_7_div_5_Template_div_click_1_listener", "_r11", "WorkflowExecutionComponent_div_18_ng_container_7_div_5_Template_input_change_5_listener", "WorkflowExecutionComponent_div_18_ng_container_7_div_4_Template", "WorkflowExecutionComponent_div_18_ng_container_7_div_5_Template", "WorkflowExecutionComponent_div_18_ng_container_7_Template", "getCurrentAgentName", "getCurrentStepInputs", "WorkflowExecutionComponent_div_31_Template_app_agent_activity_saveLogs_1_listener", "_r12", "saveLogs", "WorkflowExecutionComponent_div_31_Template_app_agent_activity_controlAction_1_listener", "handleControlAction", "WorkflowExecutionComponent_div_31_Template_app_agent_activity_onOutPutBtnClick_1_listener", "onTabChange", "id", "workflowLogs", "executionDetails", "progress", "isRunning", "status", "WorkflowExecutionComponent_div_32_Template_app_agent_output_export_1_listener", "_r13", "exportResults", "taskMessage", "WorkflowExecutionComponent", "route", "router", "workflowService", "tokenStorage", "loaderService", "formBuilder", "navigationTabs", "disabled", "workflowId", "workflowName", "constants", "chatInterfaceComp", "activityLogs", "activityProgress", "notStarted", "chatMessages", "isProcessingChat", "inputText", "agentOutputs", "fileType", "executionStartTime", "executionCompleted", "executionId", "enableStreamingLog", "enableLogStreaming", "isExecutionComplete", "progressInterval", "destroy$", "selectedTab", "demoTabs", "errorMsg", "resMessage", "isJsonValid", "disable<PERSON>hat", "selectedFiles", "workflowAgents", "userInputList", "isLoading", "loaderColor", "inputFieldOrder", "currentInputIndex", "activeTabId", "isLeftPanelCollapsed", "stepperSteps", "pipelineAgents", "agentInputsMap", "constructor", "ngOnInit", "disable<PERSON><PERSON><PERSON>", "crypto", "randomUUID", "paramMap", "pipe", "subscribe", "params", "get", "loadWorkflow", "navigate", "ngOnDestroy", "next", "complete", "<PERSON><PERSON><PERSON><PERSON>", "event", "console", "log", "from", "text", "group", "getWorkflowById", "res", "pipeline", "pipeLineAgents", "processPipelineAgents", "extractInputField", "initializeForm", "initializeAgentStates", "initializeStepper", "startInputCollection", "error", "err", "loadDemoWorkflow", "match", "variableName", "trim", "startsWith", "for<PERSON>ach", "addControl", "control", "required", "startFakeProgress", "setInterval", "stopFakeProgress", "clearInterval", "setTimeout", "handleChatMessage", "message", "addAiResponse", "executeWorkflow", "field", "setValue", "promptForCurrentField", "section", "data", "map", "timestamp", "join", "downloadAsFile", "JSON", "stringify", "filename", "type", "blob", "Blob", "url", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "click", "revokeObjectURL", "action", "navigateBack", "editWorkflow", "logExecutionStatus", "delay", "workflowExecProcessing", "push", "content", "color", "getWorkflowLogs", "workflowLogConnect", "validate<PERSON><PERSON>", "output", "parsedOutput", "parse", "e", "payload", "FormData", "queryString", "running", "file", "append", "value", "getDaUsername", "pipeLineId", "userInputs", "user", "workflowResponse", "workflowExecComplete", "tasksOutputs", "task", "title", "<PERSON><PERSON><PERSON>", "description", "expected_output", "summary", "raw", "detail", "workflowLogDisconnect", "workflowLogFailed", "handleAttachment", "onAttachmentsSelected", "files", "mockEvent", "target", "Object", "keys", "controls", "toggleLeftPanel", "agentCount", "Array", "fill", "pipelineAgent", "agentInputs", "extractAgentInputs", "values", "inputs", "PLACEHOLDER_PATTERNS", "matches", "matchAll", "placeholder", "formatInputLabel", "replace", "l", "toUpperCase", "active", "placeholderM<PERSON>", "indexOf", "agentDescription", "placeholderInput", "agents", "Set", "add", "entries", "slice", "at", "isInputValid", "valid", "onStepperChange", "step", "agentIndex", "tool", "toolName", "keyboardEvent", "shift<PERSON>ey", "preventDefault", "isCurrentStepValid", "moveToNextStep", "nextAgent", "currentInputs", "every", "executeCurrentStep", "inputName", "reader", "FileReader", "onload", "base64String", "result", "readAsDataURL", "fileInput", "getElementById", "serial", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "WorkflowService", "i3", "TokenStorageService", "LoaderService", "i4", "FormBuilder", "selectors", "viewQuery", "WorkflowExecutionComponent_Query", "rf", "ctx", "WorkflowExecutionComponent_Template_ava_icon_click_11_listener", "WorkflowExecutionComponent_Template_ava_icon_click_12_listener", "WorkflowExecutionComponent_Template_ava_stepper_stepChange_15_listener", "WorkflowExecutionComponent_ng_container_17_Template", "WorkflowExecutionComponent_div_18_Template", "WorkflowExecutionComponent_Template_ava_button_click_23_listener", "WorkflowExecutionComponent_Template_ava_tabs_tabChange_28_listener", "WorkflowExecutionComponent_div_31_Template", "WorkflowExecutionComponent_div_32_Template", "ɵɵpureFunction0", "_c0", "_c1", "i5", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\workflows\\workflow-execution\\workflow-execution.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\pages\\workflows\\workflow-execution\\workflow-execution.component.html"], "sourcesContent": ["import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Subject, takeUntil } from 'rxjs';\r\nimport { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';\r\n\r\n// Import child components\r\nimport { ChatInterfaceComponent } from '@shared/components/chat-interface/chat-interface.component';\r\nimport { ChatMessage } from '@shared/components/chat-window/chat-window.component';\r\nimport {\r\n  AgentActivityComponent,\r\n} from './components/agent-activity/agent-activity.component';\r\nimport {\r\n  AgentOutputComponent,\r\n  AgentOutput as OutputItem,\r\n} from './components/agent-output/agent-output.component';\r\nimport {\r\n  AvaStepperComponent,\r\n  ButtonComponent,\r\n  IconComponent,\r\n  TabItem,\r\n  TabsComponent,\r\n} from '@ava/play-comp-library';\r\nimport { WorkflowService } from '@shared/services/workflow.service';\r\nimport { environment } from '@shared/environments/environment';\r\nimport workflowConstants from './../constants/workflows.json';\r\nimport { TokenStorageService, LoaderService } from '@shared/index';\r\nimport { AvaTab } from '@shared/models/tab.model';\r\nimport { ExecutionStatus, ActivityLog } from '@shared/models/execution.model';\r\nimport { AgentActivityExecutionDetails } from './components/agent-activity/agent-activity.component';\r\n\r\n\r\n@Component({\r\n  selector: 'app-workflow-execution',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    ChatInterfaceComponent,\r\n    AgentActivityComponent,\r\n    AgentOutputComponent,\r\n    TabsComponent,\r\n    ButtonComponent,\r\n    IconComponent,\r\n    AvaStepperComponent,\r\n  ],\r\n  templateUrl: './workflow-execution.component.html',\r\n  styleUrls: ['./workflow-execution.component.scss'],\r\n})\r\nexport class WorkflowExecutionComponent implements OnInit, OnDestroy {\r\n  navigationTabs: TabItem[] = [\r\n    { id: 'nav-home', label: 'Agent Activity' },\r\n    { id: 'nav-products', label: 'Agent Output' },\r\n    { id: 'nav-services', label: 'Preview', disabled: true },\r\n  ];\r\n  // Workflow details\r\n  workflowId: string | null = null;\r\n  workflowName: string = 'Workflow';\r\n\r\n  constants = workflowConstants as Record<string, any>;\r\n\r\n  @ViewChild(ChatInterfaceComponent, { static: false })\r\n  chatInterfaceComp!: ChatInterfaceComponent;\r\n\r\n  // Activity logs\r\n  activityLogs: ActivityLog[] = [];\r\n  activityProgress: number = 0;\r\n  executionDetails?: AgentActivityExecutionDetails;\r\n  isRunning: boolean = false;\r\n  status: ExecutionStatus = ExecutionStatus.notStarted;\r\n\r\n  // Chat messages\r\n  chatMessages: ChatMessage[] = [];\r\n  isProcessingChat: boolean = false;\r\n  inputText = '';\r\n\r\n  // Agent outputs\r\n  agentOutputs: OutputItem[] = [];\r\n  public workflowForm!: FormGroup;\r\n  public fileType : string = '.zip';\r\n\r\n  // Execution state\r\n  executionStartTime: Date | null = null;\r\n  executionCompleted: boolean = false;\r\n  executionId!: string;\r\n\r\n  public workflowLogs: any[] = [];\r\n  enableStreamingLog = environment.enableLogStreaming || 'all';\r\n\r\n  public isExecutionComplete: boolean = false;\r\n  progressInterval: any;\r\n\r\n  // Component lifecycle\r\n  private destroy$ = new Subject<void>();\r\n  selectedTab: string = 'Agent Activity';\r\n  demoTabs: AvaTab[] = [\r\n    { id: 'activity', label: 'Agent Activity' },\r\n    { id: 'agents', label: 'Agent Output' },\r\n    { id: 'preview', label: 'Preview', disabled: true },\r\n  ];\r\n  errorMsg = false;\r\n  resMessage: any;\r\n  taskMessage = [];\r\n  isJsonValid = false;\r\n  disableChat : boolean = false;\r\n  selectedFiles: File[] = [];\r\n  workflowAgents: any[] = [];\r\n  userInputList: any[] = [];\r\n  progress = 0;\r\n  isLoading = false;\r\n  loaderColor: string = '';\r\n\r\n  inputFieldOrder: string[] = [];\r\n  currentInputIndex: number = 0;\r\n  activeTabId: string = 'nav-home';\r\n\r\n  // Panel state\r\n  isLeftPanelCollapsed = false;\r\n  expandedAgents: boolean[] = [];\r\n  agentStatuses: { completed: boolean }[] = [];\r\n  showAllInputs: boolean[] = [];\r\n\r\n  // Stepper state\r\n  stepperSteps: any[] = [];\r\n  currentStepperStep = 0;\r\n\r\n  // Pipeline data\r\n  pipelineAgents: any[] = [];\r\n  agentInputsMap: { [agentIndex: number]: any[] } = {};\r\n  \r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private workflowService: WorkflowService,\r\n    private tokenStorage: TokenStorageService,\r\n    private loaderService: LoaderService,\r\n    private formBuilder: FormBuilder,\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loaderService.disableLoader();\r\n    this.selectedTab = 'Agent Activity';\r\n    this.executionId = crypto.randomUUID();\r\n    // Get workflow ID from route params\r\n    this.route.paramMap.pipe(takeUntil(this.destroy$)).subscribe((params) => {\r\n      this.workflowId = params.get('id');\r\n      if (this.workflowId) {\r\n        this.loadWorkflow(this.workflowId);\r\n      } else {\r\n        // No workflow ID, redirect back to workflows page\r\n        this.router.navigate(['/build/workflows']);\r\n      }\r\n    });\r\n    // this.executeWorkflow()\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n    this.loaderService.enableLoader();\r\n  }\r\n  onTabChange(event: { id: string, label: string }) {\r\n    this.selectedTab = event.label;\r\n    this.activeTabId = event.id;\r\n    console.log('Tab changed:', event);\r\n  }\r\n\r\n  // Load workflow data\r\n  loadWorkflow(id: string): void {\r\n    // In a real app, this would fetch the workflow from a service\r\n    console.log(`Loading workflow with ID: ${id}`);\r\n    this.chatMessages = [\r\n      {\r\n        from: 'ai',\r\n        text: 'I am your workflow assistant. I will help you in executing this workflow.',\r\n      } as ChatMessage,\r\n    ]\r\n    this.workflowForm = this.formBuilder.group({});\r\n\r\n    this.workflowService.getWorkflowById(id).subscribe({\r\n        next: (res) => {\r\n        // Handle pipeline response structure\r\n        if (res.pipeline && res.pipeline.pipeLineAgents) {\r\n          this.pipelineAgents = res.pipeline.pipeLineAgents;\r\n          this.workflowName = res.pipeline.name;\r\n          this.processPipelineAgents();\r\n        } else {\r\n          // Handle workflow response structure\r\n          this.workflowAgents = res.workflowAgents;\r\n          this.workflowName = res.name;\r\n          this.userInputList = this.extractInputField(this.workflowAgents);\r\n        }\r\n\r\n        if(this.userInputList.length === 0){\r\n          this.disableChat = true;\r\n        }\r\n\r\n        this.initializeForm();\r\n        this.initializeAgentStates();\r\n        this.initializeStepper();\r\n        this.startInputCollection();\r\n      },\r\n        error: (err) => {\r\n          // Fallback to demo data for testing\r\n          this.loadDemoWorkflow();\r\n          console.log('Loading demo workflow due to error:', err);\r\n        }\r\n    });\r\n\r\n  }\r\n\r\n  public isImageInput(input: string): boolean {\r\n    const match = input.match(/{{(.*?)}}/);\r\n    if (match && match[1]) {\r\n      const variableName = match[1].trim();\r\n      return variableName.startsWith('image') || variableName.startsWith('Image');\r\n    }\r\n    return false;\r\n  }\r\n\r\n  public initializeForm() {   \r\n    this.userInputList.forEach((label: any) => {\r\n      this.workflowForm.addControl(label.input, this.formBuilder.control('', Validators.required));\r\n    })\r\n  }\r\n\r\n  startFakeProgress() {\r\n    this.progress = 0;\r\n    this.progressInterval = setInterval(() => {\r\n      if (this.progress < 90) {\r\n        this.progress += 5; // Increase slowly\r\n      }\r\n    }, 200); // Adjust speed\r\n  }\r\n\r\n  stopFakeProgress() {\r\n    clearInterval(this.progressInterval);\r\n    this.progress = 100;\r\n\r\n    setTimeout(() => {\r\n      this.isLoading = false;\r\n    }, 500); // Small delay to let user see 100%\r\n  }\r\n\r\n  // Handle new chat message from user\r\n  handleChatMessage(message: string): void {\r\n    // console.log('message ', message, 'is blank', message.trim() === '');\r\n    this.isProcessingChat = true;\r\n    if(message.trim() === ''){\r\n      if(this.inputFieldOrder.length === 0){\r\n        this.chatInterfaceComp.addAiResponse('Executing the workflow...');\r\n        this.executeWorkflow();\r\n      }\r\n      return;\r\n    }\r\n\r\n    if(this.isExecutionComplete || this.currentInputIndex===this.inputFieldOrder.length){\r\n        this.chatInterfaceComp.addAiResponse('Executing the workflow...');\r\n        this.executeWorkflow();\r\n      return;\r\n    }\r\n\r\n    const field = this.inputFieldOrder[this.currentInputIndex];\r\n    if (this.isImageInput(field)) {\r\n      // Ignore text input, wait for file input\r\n      this.chatInterfaceComp.addAiResponse(`Please upload an image file for ${field}`);\r\n      return;\r\n    }\r\n\r\n    this.workflowForm.get(field)?.setValue(message);\r\n    this.currentInputIndex++;\r\n\r\n    if (this.currentInputIndex < this.inputFieldOrder.length) {\r\n      this.promptForCurrentField();\r\n    } else {\r\n      this.chatInterfaceComp.addAiResponse('Thank you for the input! Executing the workflow...');\r\n      this.executeWorkflow();\r\n    }\r\n  }\r\n\r\n  // Save execution logs\r\n  saveLogs(): void {\r\n    console.log('Saving execution logs...');\r\n    // This would typically save to a service\r\n  }\r\n\r\n  // Export results\r\n  exportResults(section: 'activity' | 'output'): void {\r\n    console.log(`Exporting ${section} data...`);\r\n\r\n    if (section === 'activity') {\r\n      const data = this.activityLogs\r\n        .map((log) => `[${log.timestamp}] ${log.message}`)\r\n        .join('\\n');\r\n      this.downloadAsFile(data, 'workflow-activity-logs.txt', 'text/plain');\r\n    } else {\r\n      const data = JSON.stringify(this.agentOutputs, null, 2);\r\n      this.downloadAsFile(data, 'workflow-outputs.json', 'application/json');\r\n    }\r\n  }\r\n\r\n  // Helper method to download data as a file\r\n  private downloadAsFile(data: string, filename: string, type: string): void {\r\n    const blob = new Blob([data], { type });\r\n    const url = URL.createObjectURL(blob);\r\n    const link = document.createElement('a');\r\n    link.href = url;\r\n    link.download = filename;\r\n    link.click();\r\n    URL.revokeObjectURL(url);\r\n  }\r\n\r\n  // Handle controls for execution\r\n  handleControlAction(action: 'play' | 'pause' | 'stop'): void {\r\n    console.log(`Control action: ${action}`);\r\n    // In a real app, this would control the workflow execution\r\n\r\n    if (action === 'play') {\r\n      this.isRunning = true;\r\n    } else if (action === 'pause' || action === 'stop') {\r\n      this.isRunning = false;\r\n    }\r\n  }\r\n\r\n  // Navigate back to workflow listing\r\n  navigateBack(): void {\r\n    this.router.navigate(['/build/workflows']);\r\n  }\r\n\r\n  // Navigate to edit workflow\r\n  editWorkflow(): void {\r\n    if (this.workflowId) {\r\n      this.router.navigate(['/build/workflows/edit', this.workflowId]);\r\n    }\r\n  }\r\n\r\n  public logExecutionStatus(delay: number = 2000) {\r\n    setTimeout(() => {\r\n      if (!this.isExecutionComplete) {\r\n        console.log(this.constants);\r\n        console.log(this.constants['labels'].workflowExecProcessing);\r\n        this.workflowLogs.push({\r\n          content: this.constants['labels'].workflowExecProcessing,\r\n          color: '#F9DB24',\r\n        });\r\n      }\r\n    }, delay);\r\n  }\r\n\r\n  // public parseAnsiString(ansiString: string) {\r\n  //   const regex = ansiRegex();\r\n  //   const parts = ansiString.split(regex);\r\n  //   const matches = [...ansiString.matchAll(regex)];\r\n  //   parts.forEach((part, index) => {\r\n  //     if (part.trim() !== '') {\r\n  //       let colorCode = matches[index - 1][0];\r\n  //       if (index - 2 >= 0 && matches[index - 2]?.includes('\\u001b[1m')) {\r\n  //         colorCode = `\\u001b[1m${colorCode}`;\r\n  //       }\r\n  //       this.workflowLogs.push({\r\n  //         content: part,\r\n  //         color: this.colorMap[colorCode] || 'white',\r\n  //       });\r\n  //     }\r\n  //   });\r\n  // }\r\n\r\n  public getWorkflowLogs(executionId: string) {\r\n    this.workflowService\r\n      .workflowLogConnect(executionId)\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (message) => {\r\n          console.log('message: ', message);\r\n          const { content, color } = message;\r\n          if (color) {\r\n            this.workflowLogs.push({ content, color });\r\n          } else if (this.enableStreamingLog === 'all') {\r\n            // this.parseAnsiString(content);\r\n          }\r\n        },\r\n        error: (err) => {\r\n          this.workflowLogs.push({\r\n            content: this.constants['workflowLog'],\r\n            color: 'red',\r\n          });\r\n          console.error('WebSocket error:', err);\r\n        },\r\n        complete: () => {\r\n          this.logExecutionStatus();\r\n          console.log('WebSocket connection closed');\r\n        },\r\n      });\r\n  }\r\n\r\n  // public parseAnsiString(ansiString: string) {\r\n  //   const regex = ansiRegex();\r\n  //   const parts = ansiString.split(regex);\r\n  //   const matches = [...ansiString.matchAll(regex)];\r\n  //   parts.forEach((part, index) => {\r\n  //     if (part.trim() !== '') {\r\n  //       let colorCode = matches[index-1][0];\r\n  //       if(index - 2 >= 0 && matches[index-2]?.includes('\\u001b[1m')) {\r\n  //         colorCode = `\\u001b[1m${colorCode}`;\r\n  //       }\r\n  //       this.workflowLogs.push({\r\n  //         content: part, \r\n  //         color: this.colorMap[colorCode] || 'white', \r\n  //       });\r\n  //     }\r\n  //   });\r\n  // }\r\n\r\n  public validateJson(output: string): any | null {\r\n    this.isJsonValid = false;\r\n    try {\r\n      const parsedOutput = JSON.parse(output);\r\n      this.isJsonValid = true;\r\n      return parsedOutput;\r\n    } catch (e) {\r\n      return null;\r\n    }\r\n  }\r\n\r\n  public executeWorkflow() {\r\n    let payload: FormData | Record<string, any> = new FormData();\r\n    let queryString = '';\r\n\r\n\r\n    this.status = ExecutionStatus.running;\r\n    if (this.selectedFiles.length) {\r\n      this.selectedFiles.forEach((file) => {\r\n        payload.append('files', file);\r\n      });\r\n      payload.append('workflowId', this.workflowId);\r\n      payload.append('userInputs', JSON.stringify(this.workflowForm.value));\r\n      payload.append('user', this.tokenStorage.getDaUsername());\r\n      payload.append('executionId', this.executionId);\r\n      queryString = '/files';\r\n    } else {\r\n      payload = {\r\n        pipeLineId: this.workflowId,\r\n        userInputs: this.workflowForm.value,\r\n        executionId: this.executionId,\r\n        user: this.tokenStorage.getDaUsername(),\r\n      };\r\n    }\r\n\r\n    this.getWorkflowLogs(this.executionId);\r\n    this.startFakeProgress();\r\n\r\n    this.workflowService\r\n      .executeWorkflow(payload, queryString)\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (res) => {\r\n          this.isProcessingChat = false;\r\n          this.isRunning = false;\r\n          this.chatInterfaceComp.addAiResponse(res?.message || \"Workflow execution completed successfully!\");\r\n\r\n          if (res?.workflowResponse?.pipeline?.output) {\r\n            this.isExecutionComplete = true;\r\n            // console.log(this.constants['labels'].workflowExecComplete);\r\n            this.workflowLogs.push({\r\n              content: this.constants['labels'].workflowExecComplete,\r\n              color: '#0F8251',\r\n            });\r\n            this.errorMsg = false;\r\n            this.resMessage = res?.workflowResponse?.pipeline?.output;\r\n            this.agentOutputs = res?.workflowResponse?.pipeline?.tasksOutputs.map((task: any) => {\r\n              return {\r\n                id: task?.id || '',\r\n                title: task?.title || '',\r\n                content: task?.content || '',\r\n                agentName: task?.agentName || '',\r\n                timestamp: task?.timestamp || '',\r\n                type: task?.type || '',\r\n                description: task?.description || '',\r\n                expected_output: task?.expected_output || '',\r\n                summary: task?.summary || '',\r\n                raw: task?.raw || '',\r\n              };\r\n            })\r\n\r\n            this.taskMessage = res?.workflowResponse?.pipeline?.tasksOutputs.map(\r\n              (task: {\r\n                description: any;\r\n                summary: any;\r\n                raw: any;\r\n                expected_output: any;\r\n              }) => {\r\n                return {\r\n                  description: task.description,\r\n                  summary: task.summary,\r\n                  raw: task.raw,\r\n                  expected_output: task.expected_output,\r\n                };\r\n              },\r\n            );\r\n\r\n            // if(\"file_download_url\" in res?.pipeline){\r\n            //   this.isFileWriter = true;\r\n            //   this.fileDownloadLink = res?.pipeline?.file_download_url;\r\n\r\n            //   if(!this.fileDownloadLink){\r\n            //     this.fileDownloadUrlError = [];\r\n            //     this.fileDownloadUrlError.push(\"Output file is not generated yet!\")\r\n            //   }\r\n            // }\r\n            // this.isAccordian = true\r\n          }\r\n          this.validateJson(this.resMessage);\r\n          this.status = ExecutionStatus.completed;\r\n          this.stopFakeProgress();\r\n          this.selectedFiles = [];\r\n        },\r\n        error: (error) => {\r\n          this.isExecutionComplete = true;\r\n          this.isProcessingChat = false;\r\n          this.errorMsg = true;\r\n          this.resMessage = error?.error?.detail;\r\n          this.workflowService.workflowLogDisconnect();\r\n          this.workflowLogs.push({\r\n            content: this.constants['labels'].workflowLogFailed,\r\n            color: 'red',\r\n          });\r\n          this.chatInterfaceComp.addAiResponse(\r\n            'Something went wrong, Workflow execution has failed.',\r\n          );\r\n          this.selectedFiles = [];\r\n          this.stopFakeProgress();\r\n          console.log('error is', error.message);\r\n        },\r\n      });\r\n  }\r\n\r\n  // public asyncExecutePipeline() {\r\n  //   const payload: FormData = new FormData();\r\n  //   if (this.selectedFiles?.length) {\r\n  //     for (const element of this.selectedFiles) {\r\n  //       payload.append('files', element)\r\n  //     }\r\n  //   }\r\n  //   payload.append('pipeLineId', String(this.workflowId));\r\n  //   payload.append('userInputs', JSON.stringify(this.workflowForm.value));\r\n  //   payload.append('user', this.tokenStorage.getDaUsername() || '');\r\n  //   payload.append('executionId', this.executionId);\r\n\r\n  //   this.workflowService.asyncExecutePipeline(payload).pipe().subscribe({\r\n  //     next: (res: any) => {\r\n  //       if(res) {\r\n  //         // res handling\r\n  //         console.log(res);\r\n  //       }\r\n  //     },\r\n  //     error: e => {\r\n  //       // error handling\r\n  //       console.log(e);\r\n  //     }\r\n  //   })\r\n  // }\r\n\r\n  handleAttachment() {\r\n    console.log('handleAttachment');\r\n  }\r\n\r\n  onAttachmentsSelected(files: File[]) {\r\n    if(this.currentInputIndex===this.inputFieldOrder.length || this.inputFieldOrder.length===0){\r\n      this.selectedFiles = files;\r\n      return;\r\n    }\r\n\r\n    const field = this.inputFieldOrder[this.currentInputIndex];\r\n    if(this.isImageInput(field)){\r\n      if (files && files.length > 0) {\r\n        // Create a mock event object for the new method signature\r\n        const mockEvent = { target: { files: [files[0]] } };\r\n        this.onImageSelected(mockEvent, field);\r\n      }\r\n    } else {\r\n      this.selectedFiles = files;\r\n    }\r\n  }\r\n\r\n  startInputCollection(){\r\n    this.inputFieldOrder = Object.keys(this.workflowForm.controls);\r\n    this.currentInputIndex = 0;\r\n    if (this.inputFieldOrder.length > 0) {\r\n      this.promptForCurrentField();\r\n    }\r\n    else{\r\n      this.disableChat = true;\r\n      this.chatInterfaceComp.addAiResponse('No input is required for this workflow. Click on send button to execute the workflow.');\r\n    }\r\n  }\r\n\r\n  promptForCurrentField() {\r\n    const field = this.inputFieldOrder[this.currentInputIndex];\r\n    if (this.isImageInput(field)) {\r\n      this.fileType = '.jpeg,.png,.jpg,.svg';\r\n      this.chatInterfaceComp.addAiResponse(`Please upload an image for ${field}`);\r\n      // UI should now show a file input for the user\r\n    } else {\r\n      this.fileType = '.zip'; // or whatever default you want for non-image\r\n      this.chatInterfaceComp.addAiResponse(`Please enter the value of ${field}`);\r\n    }\r\n  }\r\n\r\n\r\n\r\n  // Panel management methods\r\n  toggleLeftPanel(): void {\r\n    this.isLeftPanelCollapsed = !this.isLeftPanelCollapsed;\r\n  }\r\n\r\n  toggleAgentExpansion(index: number): void {\r\n    this.expandedAgents[index] = !this.expandedAgents[index];\r\n  }\r\n\r\n  initializeAgentStates(): void {\r\n    const agentCount = this.pipelineAgents.length || this.workflowAgents.length;\r\n    this.expandedAgents = new Array(agentCount).fill(false);\r\n    this.agentStatuses = new Array(agentCount).fill(0).map(() => ({ completed: false }));\r\n    this.showAllInputs = new Array(agentCount).fill(false);\r\n  }\r\n\r\n  // Process pipeline agents and extract inputs\r\n  processPipelineAgents(): void {\r\n    this.pipelineAgents.forEach((pipelineAgent, index) => {\r\n      const agent = pipelineAgent.agent;\r\n      const agentInputs = this.extractAgentInputs(agent);\r\n      this.agentInputsMap[index] = agentInputs;\r\n    });\r\n\r\n    // Create combined user input list for form initialization\r\n    this.userInputList = [];\r\n    Object.values(this.agentInputsMap).forEach(inputs => {\r\n      this.userInputList.push(...inputs);\r\n    });\r\n  }\r\n\r\n  // Extract inputs for a specific agent\r\n  extractAgentInputs(agent: any): any[] {\r\n    const PLACEHOLDER_PATTERNS = /\\{\\{([a-zA-Z0-9-_]+)\\}\\}/g;\r\n    const description = agent.task?.description || '';\r\n    const matches = [...description.matchAll(PLACEHOLDER_PATTERNS)];\r\n\r\n    return matches.map(match => ({\r\n      input: match[0], // Full placeholder like {{image_1}}\r\n      placeholder: match[1], // Just the variable name like image_1\r\n      label: this.formatInputLabel(match[1]),\r\n      agentName: agent.name\r\n    }));\r\n  }\r\n\r\n  // Format input label for display\r\n  formatInputLabel(placeholder: string): string {\r\n    return placeholder\r\n      .replace(/_/g, ' ')\r\n      .replace(/\\b\\w/g, l => l.toUpperCase());\r\n  }\r\n\r\n  // Initialize stepper\r\n  initializeStepper(): void {\r\n    this.stepperSteps = this.pipelineAgents.map((pipelineAgent, index) => ({\r\n      id: index,\r\n      label: pipelineAgent.agent.name,\r\n      completed: false,\r\n      active: index === 0\r\n    }));\r\n    this.currentStepperStep = 0;\r\n  }\r\n\r\n  // Extract input fields method (similar to pipeline component)\r\n  public extractInputField(workflowAgents: any[]) {\r\n    const PLACEHOLDER_PATTERNS = /(%\\d+\\$s)|\\{\\{([a-zA-Z0-9-_]+)\\}\\}/g;\r\n    const placeholderMap: { [key: string]: { agents: Set<string>; inputs: Set<string> } } = {};\r\n\r\n    workflowAgents.forEach((agent: any) => {\r\n      const agentName = agent?.name || `Agent ${workflowAgents.indexOf(agent) + 1}`;\r\n      const agentDescription = agent?.description || agent?.task?.description || '';\r\n      const matches = agentDescription.matchAll(PLACEHOLDER_PATTERNS) || [];\r\n\r\n      for (const match of matches) {\r\n        const placeholder = match[1] || match[2];\r\n        const placeholderInput = match[0];\r\n        if (!placeholderMap[placeholder]) {\r\n          placeholderMap[placeholder] = { agents: new Set(), inputs: new Set() };\r\n        }\r\n        placeholderMap[placeholder].agents.add(agentName);\r\n        placeholderMap[placeholder].inputs.add(placeholderInput);\r\n      }\r\n    });\r\n\r\n    return Object.entries(placeholderMap).map(([placeholder, { agents, inputs }]) => ({\r\n      name: [...agents].length > 2\r\n        ? `${[...agents].slice(0, -1).join(\", \")} and ${[...agents].at(-1)}`\r\n        : [...agents].join(\" and \"),\r\n      placeholder,\r\n      input: [...inputs][0],\r\n    }));\r\n  }\r\n\r\n  // Input validation\r\n  isInputValid(): boolean {\r\n    return this.workflowForm.valid && !!this.workflowId;\r\n  }\r\n\r\n  // Stepper event handlers\r\n  onStepperChange(step: any): void {\r\n    this.currentStepperStep = step.id;\r\n    this.expandedAgents[step.id] = true; // Auto-expand current agent\r\n  }\r\n\r\n  // Get inputs for current step\r\n  getCurrentStepInputs(): any[] {\r\n    return this.agentInputsMap[this.currentStepperStep] || [];\r\n  }\r\n\r\n  // Get current agent name\r\n  getCurrentAgentName(): string {\r\n    if (this.pipelineAgents[this.currentStepperStep]) {\r\n      return this.pipelineAgents[this.currentStepperStep].agent.name;\r\n    }\r\n    return `Agent ${this.currentStepperStep + 1}`;\r\n  }\r\n\r\n  // Get inputs for specific agent\r\n  getAgentInputs(agentIndex: number): any[] {\r\n    return this.agentInputsMap[agentIndex] || [];\r\n  }\r\n\r\n  // Get combined tools for agent\r\n  getCombinedTools(agent: any): string {\r\n    const tools = [...(agent.tools || []), ...(agent.userTools || [])];\r\n    return tools.map(tool => tool.toolName || tool).join(', ') || 'No tools';\r\n  }\r\n\r\n  // Toggle show all inputs for agent\r\n  toggleShowAllInputs(agentIndex: number): void {\r\n    this.showAllInputs[agentIndex] = !this.showAllInputs[agentIndex];\r\n  }\r\n\r\n  // Handle input submission\r\n  onInputSubmit(event?: KeyboardEvent | Event): void {\r\n    const keyboardEvent = event as KeyboardEvent;\r\n    if (keyboardEvent && keyboardEvent.shiftKey) {\r\n      return; // Allow shift+enter for new line\r\n    }\r\n\r\n    if (event) {\r\n      event.preventDefault();\r\n    }\r\n\r\n    // Check if current step inputs are valid\r\n    if (this.isCurrentStepValid()) {\r\n      this.moveToNextStep();\r\n    }\r\n  }\r\n\r\n  // Move to next step\r\n  moveToNextStep(): void {\r\n    if (this.currentStepperStep < this.pipelineAgents.length - 1) {\r\n      // Mark current step as completed\r\n      this.agentStatuses[this.currentStepperStep].completed = true;\r\n      this.stepperSteps[this.currentStepperStep].completed = true;\r\n\r\n      // Move to next step\r\n      this.currentStepperStep++;\r\n      this.stepperSteps[this.currentStepperStep].active = true;\r\n\r\n      // Auto-expand next agent\r\n      this.expandedAgents[this.currentStepperStep] = true;\r\n\r\n      // Add AI response for next step\r\n      const nextAgent = this.pipelineAgents[this.currentStepperStep];\r\n      this.chatInterfaceComp.addAiResponse(`Moving to ${nextAgent.agent.name}. Please provide the required inputs.`);\r\n    } else {\r\n      // All steps completed, ready to execute\r\n      this.agentStatuses[this.currentStepperStep].completed = true;\r\n      this.stepperSteps[this.currentStepperStep].completed = true;\r\n      this.chatInterfaceComp.addAiResponse('All inputs collected! Ready to execute the workflow.');\r\n    }\r\n  }\r\n\r\n  // Check if current step is valid\r\n  isCurrentStepValid(): boolean {\r\n    const currentInputs = this.getCurrentStepInputs();\r\n    return currentInputs.every(input => {\r\n      const control = this.workflowForm.get(input.input);\r\n      return control && control.valid && control.value;\r\n    });\r\n  }\r\n\r\n  // Execute current step\r\n  executeCurrentStep(): void {\r\n    if (this.isCurrentStepValid()) {\r\n      this.moveToNextStep();\r\n    }\r\n  }\r\n\r\n  // Handle image selection\r\n  onImageSelected(event: any, inputName: string): void {\r\n    const file = event.target.files[0];\r\n    if (file) {\r\n      const reader = new FileReader();\r\n      reader.onload = () => {\r\n        const base64String = reader.result as string;\r\n        this.workflowForm.get(inputName)?.setValue(base64String);\r\n\r\n        // Auto-advance if this was the last required input for current step\r\n        setTimeout(() => {\r\n          if (this.isCurrentStepValid()) {\r\n            this.moveToNextStep();\r\n          }\r\n        }, 100);\r\n      };\r\n      reader.readAsDataURL(file);\r\n    }\r\n  }\r\n\r\n  // Trigger file input\r\n  triggerFileInput(inputName: string): void {\r\n    const fileInput = document.getElementById('file-' + inputName) ||\r\n                     document.getElementById('current-file-' + inputName);\r\n    if (fileInput) {\r\n      fileInput.click();\r\n    }\r\n  }\r\n\r\n  // Demo workflow for testing\r\n  loadDemoWorkflow(): void {\r\n    this.pipelineAgents = [\r\n      {\r\n        serial: 1,\r\n        agent: {\r\n          name: 'OCR Agent',\r\n          task: {\r\n            description: 'Extract text from {{image_1}} and {{image_2}}'\r\n          },\r\n          llm: { modelDeploymentName: 'GPT-4' },\r\n          tools: [],\r\n          userTools: []\r\n        }\r\n      },\r\n      {\r\n        serial: 2,\r\n        agent: {\r\n          name: 'Analysis Agent',\r\n          task: {\r\n            description: 'Analyze the extracted text and generate {{report_type}} report'\r\n          },\r\n          llm: { modelDeploymentName: 'Claude-3' },\r\n          tools: [],\r\n          userTools: []\r\n        }\r\n      }\r\n    ];\r\n\r\n    this.workflowName = 'Demo OCR Workflow';\r\n    this.processPipelineAgents();\r\n    this.initializeForm();\r\n    this.initializeAgentStates();\r\n    this.initializeStepper();\r\n\r\n    if (this.userInputList.length === 0) {\r\n      this.disableChat = true;\r\n    }\r\n  }\r\n}\r\n", "<div class=\"workflow-execution-container\">\r\n  <!-- SVG Gradient Definitions for Icons -->\r\n  <svg width=\"0\" height=\"0\" style=\"position: absolute\">\r\n    <defs>\r\n      <linearGradient id=\"gradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\r\n        <stop offset=\"0%\" stop-color=\"#6566CD\" />\r\n        <stop offset=\"100%\" stop-color=\"#F96CAB\" />\r\n      </linearGradient>\r\n    </defs>\r\n  </svg>\r\n  <div class=\"execution-content\" role=\"main\">\r\n    <!-- Left Panel: Workflow Execution Panel -->\r\n    <div\r\n      class=\"column execution-panel-column\"\r\n      [class.collapsed]=\"isLeftPanelCollapsed\"\r\n      role=\"region\"\r\n      aria-label=\"Workflow Execution Panel\"\r\n    >\r\n      <div class=\"column-content execution-panel-content\">\r\n        <div class=\"column-header\">\r\n          <div class=\"header-left\">\r\n            <ava-icon\r\n              iconName=\"arrowLeft\"\r\n              iconColor=\"#1A46A7\"\r\n              class=\"back-icon\"\r\n              (click)=\"navigateBack()\"\r\n            ></ava-icon>\r\n            <ava-icon\r\n              iconName=\"panelLeft\"\r\n              iconColor=\"#1A46A7\"\r\n              class=\"panel-icon\"\r\n              (click)=\"toggleLeftPanel()\"\r\n            ></ava-icon>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"panel-content\" [class.hidden]=\"isLeftPanelCollapsed\">\r\n          <!-- Stepper Section -->\r\n          <div class=\"stepper-section\">\r\n            <ava-stepper\r\n              [steps]=\"stepperSteps\"\r\n              [currentStep]=\"currentStepperStep\"\r\n              [iconColor]=\"'#ffff'\"\r\n              [iconSize]=\"'16'\"\r\n              orientation=\"vertical\"\r\n              size=\"small\"\r\n              [showNavigation]=\"false\"\r\n              (stepChange)=\"onStepperChange($event)\">\r\n            </ava-stepper>\r\n          </div>\r\n\r\n          <!-- Agent Cards -->\r\n          <div class=\"agents-section\">\r\n            <ng-container *ngFor=\"let agent of pipelineAgents; let i = index\">\r\n              <div class=\"agent-card\"\r\n                   [class.completed]=\"agentStatuses[i]?.completed\"\r\n                   [class.active]=\"currentStepperStep === i\"\r\n                   [class.expanded]=\"expandedAgents[i]\">\r\n                <div class=\"agent-header\" (click)=\"toggleAgentExpansion(i)\">\r\n                  <div class=\"status-icon\">\r\n                    <ava-icon\r\n                      [iconName]=\"agentStatuses[i]?.completed ? 'check' : 'folder'\"\r\n                      [iconColor]=\"agentStatuses[i]?.completed ? '#22C55E' : '#1A46A7'\"\r\n                    ></ava-icon>\r\n                  </div>\r\n                  <div class=\"agent-info\">\r\n                    <h3 class=\"agent-name\">{{ agent?.agent?.name || 'Agent ' + (i + 1) }}</h3>\r\n                    <ava-icon\r\n                      iconName=\"chevronDown\"\r\n                      iconColor=\"#6B7280\"\r\n                      class=\"expand-icon\"\r\n                      [class.expanded]=\"expandedAgents[i]\"\r\n                    ></ava-icon>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Agent Details (Expandable) -->\r\n                <div class=\"agent-details\" [class.expanded]=\"expandedAgents[i]\">\r\n                  <!-- Agent Model and Tools Info -->\r\n                  <div class=\"agent-detail-item\" *ngIf=\"agent?.agent?.llm?.modelDeploymentName\">\r\n                    <span class=\"detail-label\">Model:</span>\r\n                    <span class=\"detail-value\">{{ agent.agent.llm.modelDeploymentName }}</span>\r\n                  </div>\r\n                  <div class=\"agent-detail-item\" *ngIf=\"agent?.agent?.tools?.length || agent?.agent?.userTools?.length\">\r\n                    <span class=\"detail-label\">Tools:</span>\r\n                    <span class=\"detail-value\">{{ getCombinedTools(agent.agent) }}</span>\r\n                  </div>\r\n\r\n                  <!-- Agent Input Fields -->\r\n                  <div class=\"agent-inputs\" *ngIf=\"getAgentInputs(i).length > 0\">\r\n                    <div class=\"inputs-container\" [class.scrollable]=\"getAgentInputs(i).length > 2\">\r\n                      <ng-container *ngFor=\"let input of getAgentInputs(i); let inputIndex = index\">\r\n                        <div class=\"input-field\"\r\n                             [class.hidden]=\"inputIndex > 1 && !showAllInputs[i]\"\r\n                             [formGroup]=\"workflowForm\">\r\n                          <label class=\"input-label\">{{ input.label }}</label>\r\n\r\n                          <!-- Text Input -->\r\n                          <div *ngIf=\"!isImageInput(input.input)\" class=\"text-input-container\">\r\n                            <textarea\r\n                              [formControlName]=\"input.input\"\r\n                              [placeholder]=\"'Enter ' + input.label\"\r\n                              class=\"input-textarea\"\r\n                              [disabled]=\"currentStepperStep !== i\"\r\n                            ></textarea>\r\n                          </div>\r\n\r\n                          <!-- Image Input -->\r\n                          <div *ngIf=\"isImageInput(input.input)\" class=\"image-input-container\">\r\n                            <div class=\"file-upload-area\"\r\n                                 [class.disabled]=\"currentStepperStep !== i\"\r\n                                 (click)=\"triggerFileInput(input.input)\">\r\n                              <ava-icon iconName=\"upload\" iconColor=\"#6B7280\"></ava-icon>\r\n                              <span>Upload Image</span>\r\n                            </div>\r\n                            <input\r\n                              type=\"file\"\r\n                              [id]=\"'file-' + input.input\"\r\n                              accept=\"image/*\"\r\n                              (change)=\"onImageSelected($event, input.input)\"\r\n                              style=\"display: none;\">\r\n                          </div>\r\n                        </div>\r\n                      </ng-container>\r\n\r\n                      <!-- Show More/Less Toggle -->\r\n                      <div class=\"show-more-toggle\" *ngIf=\"getAgentInputs(i).length > 2\">\r\n                        <button type=\"button\"\r\n                                class=\"toggle-btn\"\r\n                                (click)=\"toggleShowAllInputs(i)\">\r\n                          {{ showAllInputs[i] ? 'Show Less' : 'Show More (' + (getAgentInputs(i).length - 2) + ' more)' }}\r\n                        </button>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </ng-container>\r\n          </div>\r\n\r\n          <!-- Current Input Section -->\r\n          <div class=\"current-input-section\" *ngIf=\"getCurrentStepInputs().length > 0\">\r\n            <div class=\"input-header\">\r\n              <div class=\"status-icon\">\r\n                <ava-icon\r\n                  iconName=\"edit\"\r\n                  iconColor=\"#1A46A7\"\r\n                ></ava-icon>\r\n              </div>\r\n              <h3 class=\"input-title\">Enter input for {{ getCurrentAgentName() }}</h3>\r\n            </div>\r\n\r\n            <div class=\"current-input-container\" [formGroup]=\"workflowForm\">\r\n              <ng-container *ngFor=\"let input of getCurrentStepInputs()\">\r\n                <div class=\"input-field\">\r\n                  <label class=\"input-label\">{{ input.label }}</label>\r\n\r\n                  <!-- Text Input -->\r\n                  <div *ngIf=\"!isImageInput(input.input)\" class=\"input-container\">\r\n                    <textarea\r\n                      [formControlName]=\"input.input\"\r\n                      [placeholder]=\"'Enter ' + input.label\"\r\n                      class=\"input-textarea\"\r\n                      (keydown.enter)=\"onInputSubmit($event)\"\r\n                    ></textarea>\r\n                    <div class=\"input-actions\">\r\n                      <ava-icon iconName=\"send\" iconColor=\"#1A46A7\" class=\"send-icon\" (click)=\"onInputSubmit()\"></ava-icon>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <!-- Image Input -->\r\n                  <div *ngIf=\"isImageInput(input.input)\" class=\"image-input-container\">\r\n                    <div class=\"file-upload-area\" (click)=\"triggerFileInput(input.input)\">\r\n                      <ava-icon iconName=\"upload\" iconColor=\"#6B7280\"></ava-icon>\r\n                      <span>Upload Image for {{ input.label }}</span>\r\n                    </div>\r\n                    <input\r\n                      type=\"file\"\r\n                      [id]=\"'current-file-' + input.input\"\r\n                      accept=\"image/*\"\r\n                      (change)=\"onImageSelected($event, input.input)\"\r\n                      style=\"display: none;\">\r\n                  </div>\r\n                </div>\r\n              </ng-container>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Info Message -->\r\n          <div class=\"info-message\">\r\n            <p>Agent input needed in order to execute Workflow</p>\r\n          </div>\r\n\r\n          <!-- Execute Button -->\r\n          <div class=\"execute-section\">\r\n            <ava-button\r\n              label=\"▶ Execute Agent\"\r\n              variant=\"primary\"\r\n              size=\"large\"\r\n              [disabled]=\"!isCurrentStepValid()\"\r\n              (click)=\"executeCurrentStep()\"\r\n              [customStyles]=\"{\r\n                'background': '#1A46A7',\r\n                'width': '100%',\r\n                'border-radius': '8px'\r\n              }\"\r\n            ></ava-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Right Column: Agent Output -->\r\n    <div class=\"column output-column\" role=\"region\" aria-label=\"Agent Output\">\r\n      <div class=\"column-content\">\r\n        <div class=\"column-header row\">\r\n          <div class=\"col-7\">\r\n            <ava-tabs\r\n              [tabs]=\"navigationTabs\"\r\n              [activeTabId]=\"activeTabId\"\r\n              variant=\"button\"\r\n              buttonShape=\"pill\"\r\n              [showContentPanels]=\"false\"\r\n              (tabChange)=\"onTabChange($event)\"\r\n              ariaLabel=\"Pill navigation tabs\"\r\n            ></ava-tabs>\r\n          </div>\r\n          <div class=\"col-5 right-section-header\">\r\n            <ava-button\r\n              label=\"Send for Approval\"\r\n              variant=\"primary\"\r\n              [customStyles]=\"{\r\n                background:\r\n                  'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',\r\n                '--button-effect-color': '33, 90, 214',\r\n                'border-radius': '8px',\r\n                'box-shadow': 'none',\r\n              }\"\r\n              size=\"medium\"\r\n            >\r\n            </ava-button>\r\n            <!-- <ava-button\r\n              label=\"Export\"\r\n              variant=\"secondary\"\r\n              size=\"medium\"\r\n              class=\"ms-2\"\r\n            >\r\n            </ava-button> -->\r\n          </div>\r\n        </div>\r\n        <!-- activity content -->\r\n        <div *ngIf=\"selectedTab === 'Agent Activity'\" style=\"height: 100%\">\r\n          <app-agent-activity\r\n            [activityLogs]=\"workflowLogs\"\r\n            [executionDetails]=\"executionDetails\"\r\n            [progress]=\"progress\"\r\n            [isRunning]=\"isRunning\"\r\n            (saveLogs)=\"saveLogs()\"\r\n            (controlAction)=\"handleControlAction($event)\"\r\n            [status]=\"status\"\r\n            (onOutPutBtnClick)=\"onTabChange({id: 'nav-products', label: 'Agent Output'})\"\r\n          ></app-agent-activity>\r\n        </div>\r\n        <!-- Agent Output Component -->\r\n        <div *ngIf=\"selectedTab === 'Agent Output'\" style=\"height: 100%\">\r\n          <app-agent-output\r\n            [outputs]=\"taskMessage\"\r\n            (export)=\"exportResults('output')\"\r\n          ></app-agent-output>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;AACzC,SAAiCC,WAAW,EAAEC,mBAAmB,EAAEC,UAAU,QAAQ,gBAAgB;AAErG;AACA,SAASC,sBAAsB,QAAQ,4DAA4D;AAEnG,SACEC,sBAAsB,QACjB,sDAAsD;AAC7D,SACEC,oBAAoB,QAEf,kDAAkD;AACzD,SACEC,mBAAmB,EACnBC,eAAe,EACfC,aAAa,EAEbC,aAAa,QACR,wBAAwB;AAE/B,SAASC,WAAW,QAAQ,kCAAkC;AAC9D,OAAOC,iBAAiB,MAAM,+BAA+B;AAG7D,SAASC,eAAe,QAAqB,gCAAgC;;;;;;;;;;;;;;;;;;;;ICoDzDC,EADF,CAAAC,cAAA,cAA8E,eACjD;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxCH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IACtEF,EADsE,CAAAG,YAAA,EAAO,EACvE;;;;IADuBH,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAK,iBAAA,CAAAC,QAAA,CAAAC,KAAA,CAAAC,GAAA,CAAAC,mBAAA,CAAyC;;;;;IAGpET,EADF,CAAAC,cAAA,cAAsG,eACzE;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxCH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,GAAmC;IAChEF,EADgE,CAAAG,YAAA,EAAO,EACjE;;;;;IADuBH,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAK,iBAAA,CAAAK,MAAA,CAAAC,gBAAA,CAAAL,QAAA,CAAAC,KAAA,EAAmC;;;;;IAaxDP,EAAA,CAAAC,cAAA,cAAqE;IACnED,EAAA,CAAAY,SAAA,mBAKY;IACdZ,EAAA,CAAAG,YAAA,EAAM;;;;;;IALFH,EAAA,CAAAI,SAAA,EAA+B;IAG/BJ,EAHA,CAAAa,UAAA,oBAAAC,QAAA,CAAAC,KAAA,CAA+B,2BAAAD,QAAA,CAAAE,KAAA,CACO,aAAAN,MAAA,CAAAO,kBAAA,KAAAC,IAAA,CAED;;;;;;IAMvClB,EADF,CAAAC,cAAA,cAAqE,cAGtB;IAAxCD,EAAA,CAAAmB,UAAA,mBAAAC,qGAAA;MAAApB,EAAA,CAAAqB,aAAA,CAAAC,GAAA;MAAA,MAAAR,QAAA,GAAAd,EAAA,CAAAuB,aAAA,GAAAC,SAAA;MAAA,MAAAd,MAAA,GAAAV,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAyB,WAAA,CAASf,MAAA,CAAAgB,gBAAA,CAAAZ,QAAA,CAAAC,KAAA,CAA6B;IAAA,EAAC;IAC1Cf,EAAA,CAAAY,SAAA,mBAA2D;IAC3DZ,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IACpBF,EADoB,CAAAG,YAAA,EAAO,EACrB;IACNH,EAAA,CAAAC,cAAA,gBAKyB;IADvBD,EAAA,CAAAmB,UAAA,oBAAAQ,wGAAAC,MAAA;MAAA5B,EAAA,CAAAqB,aAAA,CAAAC,GAAA;MAAA,MAAAR,QAAA,GAAAd,EAAA,CAAAuB,aAAA,GAAAC,SAAA;MAAA,MAAAd,MAAA,GAAAV,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAyB,WAAA,CAAUf,MAAA,CAAAmB,eAAA,CAAAD,MAAA,EAAAd,QAAA,CAAAC,KAAA,CAAoC;IAAA,EAAC;IAEnDf,EANE,CAAAG,YAAA,EAKyB,EACrB;;;;;;IAXCH,EAAA,CAAAI,SAAA,EAA2C;IAA3CJ,EAAA,CAAA8B,WAAA,aAAApB,MAAA,CAAAO,kBAAA,KAAAC,IAAA,CAA2C;IAO9ClB,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAa,UAAA,iBAAAC,QAAA,CAAAC,KAAA,CAA4B;;;;;IA1BpCf,EAAA,CAAA+B,uBAAA,GAA8E;IAI1E/B,EAHF,CAAAC,cAAA,cAEgC,gBACH;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAapDH,EAVA,CAAAgC,UAAA,IAAAC,+EAAA,kBAAqE,IAAAC,+EAAA,kBAUA;IAcvElC,EAAA,CAAAG,YAAA,EAAM;;;;;;;;IA7BDH,EAAA,CAAAI,SAAA,EAAoD;IAApDJ,EAAA,CAAA8B,WAAA,WAAAK,aAAA,SAAAzB,MAAA,CAAA0B,aAAA,CAAAlB,IAAA,EAAoD;IACpDlB,EAAA,CAAAa,UAAA,cAAAH,MAAA,CAAA2B,YAAA,CAA0B;IACFrC,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAK,iBAAA,CAAAS,QAAA,CAAAE,KAAA,CAAiB;IAGtChB,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAa,UAAA,UAAAH,MAAA,CAAA4B,YAAA,CAAAxB,QAAA,CAAAC,KAAA,EAAgC;IAUhCf,EAAA,CAAAI,SAAA,EAA+B;IAA/BJ,EAAA,CAAAa,UAAA,SAAAH,MAAA,CAAA4B,YAAA,CAAAxB,QAAA,CAAAC,KAAA,EAA+B;;;;;;IAmBvCf,EADF,CAAAC,cAAA,cAAmE,iBAGxB;IAAjCD,EAAA,CAAAmB,UAAA,mBAAAoB,yFAAA;MAAAvC,EAAA,CAAAqB,aAAA,CAAAmB,GAAA;MAAA,MAAAtB,IAAA,GAAAlB,EAAA,CAAAuB,aAAA,IAAAkB,KAAA;MAAA,MAAA/B,MAAA,GAAAV,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAyB,WAAA,CAASf,MAAA,CAAAgC,mBAAA,CAAAxB,IAAA,CAAsB;IAAA,EAAC;IACtClB,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;;IAFFH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAA2C,kBAAA,MAAAjC,MAAA,CAAA0B,aAAA,CAAAlB,IAAA,mCAAAR,MAAA,CAAAkC,cAAA,CAAA1B,IAAA,EAAA2B,MAAA,sBACF;;;;;IAzCJ7C,EADF,CAAAC,cAAA,cAA+D,cACmB;IAoC9ED,EAnCA,CAAAgC,UAAA,IAAAc,yEAAA,2BAA8E,IAAAC,gEAAA,kBAmCX;IAQvE/C,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IA5C0BH,EAAA,CAAAI,SAAA,EAAiD;IAAjDJ,EAAA,CAAA8B,WAAA,eAAApB,MAAA,CAAAkC,cAAA,CAAA1B,IAAA,EAAA2B,MAAA,KAAiD;IAC7C7C,EAAA,CAAAI,SAAA,EAAsB;IAAtBJ,EAAA,CAAAa,UAAA,YAAAH,MAAA,CAAAkC,cAAA,CAAA1B,IAAA,EAAsB;IAmCvBlB,EAAA,CAAAI,SAAA,EAAkC;IAAlCJ,EAAA,CAAAa,UAAA,SAAAH,MAAA,CAAAkC,cAAA,CAAA1B,IAAA,EAAA2B,MAAA,KAAkC;;;;;;IAzE3E7C,EAAA,CAAA+B,uBAAA,GAAkE;IAK9D/B,EAJF,CAAAC,cAAA,cAG0C,cACoB;IAAlCD,EAAA,CAAAmB,UAAA,mBAAA6B,yEAAA;MAAA,MAAA9B,IAAA,GAAAlB,EAAA,CAAAqB,aAAA,CAAA4B,GAAA,EAAAR,KAAA;MAAA,MAAA/B,MAAA,GAAAV,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAyB,WAAA,CAASf,MAAA,CAAAwC,oBAAA,CAAAhC,IAAA,CAAuB;IAAA,EAAC;IACzDlB,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAY,SAAA,mBAGY;IACdZ,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAAwB,aACC;IAAAD,EAAA,CAAAE,MAAA,GAA8C;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1EH,EAAA,CAAAY,SAAA,mBAKY;IAEhBZ,EADE,CAAAG,YAAA,EAAM,EACF;IAGNH,EAAA,CAAAC,cAAA,cAAgE;IAY9DD,EAVA,CAAAgC,UAAA,KAAAmB,0DAAA,kBAA8E,KAAAC,0DAAA,kBAIwB,KAAAC,0DAAA,kBAMvC;IA+CnErD,EADE,CAAAG,YAAA,EAAM,EACF;;;;;;;IAjFDH,EAAA,CAAAI,SAAA,EAA+C;IAE/CJ,EAFA,CAAA8B,WAAA,cAAApB,MAAA,CAAA4C,aAAA,CAAApC,IAAA,mBAAAR,MAAA,CAAA4C,aAAA,CAAApC,IAAA,EAAAqC,SAAA,CAA+C,WAAA7C,MAAA,CAAAO,kBAAA,KAAAC,IAAA,CACN,aAAAR,MAAA,CAAA8C,cAAA,CAAAtC,IAAA,EACL;IAIjClB,EAAA,CAAAI,SAAA,GAA6D;IAC7DJ,EADA,CAAAa,UAAA,cAAAH,MAAA,CAAA4C,aAAA,CAAApC,IAAA,mBAAAR,MAAA,CAAA4C,aAAA,CAAApC,IAAA,EAAAqC,SAAA,uBAA6D,eAAA7C,MAAA,CAAA4C,aAAA,CAAApC,IAAA,mBAAAR,MAAA,CAAA4C,aAAA,CAAApC,IAAA,EAAAqC,SAAA,0BACI;IAI5CvD,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAK,iBAAA,EAAAC,QAAA,kBAAAA,QAAA,CAAAC,KAAA,kBAAAD,QAAA,CAAAC,KAAA,CAAAkD,IAAA,iBAAAvC,IAAA,MAA8C;IAKnElB,EAAA,CAAAI,SAAA,EAAoC;IAApCJ,EAAA,CAAA8B,WAAA,aAAApB,MAAA,CAAA8C,cAAA,CAAAtC,IAAA,EAAoC;IAMflB,EAAA,CAAAI,SAAA,EAAoC;IAApCJ,EAAA,CAAA8B,WAAA,aAAApB,MAAA,CAAA8C,cAAA,CAAAtC,IAAA,EAAoC;IAE7BlB,EAAA,CAAAI,SAAA,EAA4C;IAA5CJ,EAAA,CAAAa,UAAA,SAAAP,QAAA,kBAAAA,QAAA,CAAAC,KAAA,kBAAAD,QAAA,CAAAC,KAAA,CAAAC,GAAA,kBAAAF,QAAA,CAAAC,KAAA,CAAAC,GAAA,CAAAC,mBAAA,CAA4C;IAI5CT,EAAA,CAAAI,SAAA,EAAoE;IAApEJ,EAAA,CAAAa,UAAA,UAAAP,QAAA,kBAAAA,QAAA,CAAAC,KAAA,kBAAAD,QAAA,CAAAC,KAAA,CAAAmD,KAAA,kBAAApD,QAAA,CAAAC,KAAA,CAAAmD,KAAA,CAAAb,MAAA,MAAAvC,QAAA,kBAAAA,QAAA,CAAAC,KAAA,kBAAAD,QAAA,CAAAC,KAAA,CAAAoD,SAAA,kBAAArD,QAAA,CAAAC,KAAA,CAAAoD,SAAA,CAAAd,MAAA,EAAoE;IAMzE7C,EAAA,CAAAI,SAAA,EAAkC;IAAlCJ,EAAA,CAAAa,UAAA,SAAAH,MAAA,CAAAkC,cAAA,CAAA1B,IAAA,EAAA2B,MAAA,KAAkC;;;;;;IAsE3D7C,EADF,CAAAC,cAAA,cAAgE,mBAM7D;IADCD,EAAA,CAAAmB,UAAA,2BAAAyC,kGAAAhC,MAAA;MAAA5B,EAAA,CAAAqB,aAAA,CAAAwC,GAAA;MAAA,MAAAnD,MAAA,GAAAV,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAyB,WAAA,CAAiBf,MAAA,CAAAoD,aAAA,CAAAlC,MAAA,CAAqB;IAAA,EAAC;IACxC5B,EAAA,CAAAG,YAAA,EAAW;IAEVH,EADF,CAAAC,cAAA,cAA2B,mBACiE;IAA1BD,EAAA,CAAAmB,UAAA,mBAAA4C,0FAAA;MAAA/D,EAAA,CAAAqB,aAAA,CAAAwC,GAAA;MAAA,MAAAnD,MAAA,GAAAV,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAyB,WAAA,CAASf,MAAA,CAAAoD,aAAA,EAAe;IAAA,EAAC;IAE7F9D,EAF8F,CAAAG,YAAA,EAAW,EACjG,EACF;;;;IARFH,EAAA,CAAAI,SAAA,EAA+B;IAC/BJ,EADA,CAAAa,UAAA,oBAAAmD,SAAA,CAAAjD,KAAA,CAA+B,2BAAAiD,SAAA,CAAAhD,KAAA,CACO;;;;;;IAWxChB,EADF,CAAAC,cAAA,cAAqE,cACG;IAAxCD,EAAA,CAAAmB,UAAA,mBAAA8C,qFAAA;MAAAjE,EAAA,CAAAqB,aAAA,CAAA6C,IAAA;MAAA,MAAAF,SAAA,GAAAhE,EAAA,CAAAuB,aAAA,GAAAC,SAAA;MAAA,MAAAd,MAAA,GAAAV,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAyB,WAAA,CAASf,MAAA,CAAAgB,gBAAA,CAAAsC,SAAA,CAAAjD,KAAA,CAA6B;IAAA,EAAC;IACnEf,EAAA,CAAAY,SAAA,mBAA2D;IAC3DZ,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAkC;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IACNH,EAAA,CAAAC,cAAA,gBAKyB;IADvBD,EAAA,CAAAmB,UAAA,oBAAAgD,wFAAAvC,MAAA;MAAA5B,EAAA,CAAAqB,aAAA,CAAA6C,IAAA;MAAA,MAAAF,SAAA,GAAAhE,EAAA,CAAAuB,aAAA,GAAAC,SAAA;MAAA,MAAAd,MAAA,GAAAV,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAyB,WAAA,CAAUf,MAAA,CAAAmB,eAAA,CAAAD,MAAA,EAAAoC,SAAA,CAAAjD,KAAA,CAAoC;IAAA,EAAC;IAEnDf,EANE,CAAAG,YAAA,EAKyB,EACrB;;;;IARIH,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAA2C,kBAAA,sBAAAqB,SAAA,CAAAhD,KAAA,KAAkC;IAIxChB,EAAA,CAAAI,SAAA,EAAoC;IAApCJ,EAAA,CAAAa,UAAA,yBAAAmD,SAAA,CAAAjD,KAAA,CAAoC;;;;;IAzB5Cf,EAAA,CAAA+B,uBAAA,GAA2D;IAEvD/B,EADF,CAAAC,cAAA,cAAyB,gBACI;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAgBpDH,EAbA,CAAAgC,UAAA,IAAAoC,+DAAA,kBAAgE,IAAAC,+DAAA,kBAaK;IAYvErE,EAAA,CAAAG,YAAA,EAAM;;;;;;IA5BuBH,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAK,iBAAA,CAAA2D,SAAA,CAAAhD,KAAA,CAAiB;IAGtChB,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAa,UAAA,UAAAH,MAAA,CAAA4B,YAAA,CAAA0B,SAAA,CAAAjD,KAAA,EAAgC;IAahCf,EAAA,CAAAI,SAAA,EAA+B;IAA/BJ,EAAA,CAAAa,UAAA,SAAAH,MAAA,CAAA4B,YAAA,CAAA0B,SAAA,CAAAjD,KAAA,EAA+B;;;;;IA5BzCf,EAFJ,CAAAC,cAAA,cAA6E,cACjD,cACC;IACvBD,EAAA,CAAAY,SAAA,mBAGY;IACdZ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAE,MAAA,GAA2C;IACrEF,EADqE,CAAAG,YAAA,EAAK,EACpE;IAENH,EAAA,CAAAC,cAAA,cAAgE;IAC9DD,EAAA,CAAAgC,UAAA,IAAAsC,yDAAA,2BAA2D;IAiC/DtE,EADE,CAAAG,YAAA,EAAM,EACF;;;;IArCsBH,EAAA,CAAAI,SAAA,GAA2C;IAA3CJ,EAAA,CAAA2C,kBAAA,qBAAAjC,MAAA,CAAA6D,mBAAA,OAA2C;IAGhCvE,EAAA,CAAAI,SAAA,EAA0B;IAA1BJ,EAAA,CAAAa,UAAA,cAAAH,MAAA,CAAA2B,YAAA,CAA0B;IAC7BrC,EAAA,CAAAI,SAAA,EAAyB;IAAzBJ,EAAA,CAAAa,UAAA,YAAAH,MAAA,CAAA8D,oBAAA,GAAyB;;;;;;IAmG7DxE,EADF,CAAAC,cAAA,cAAmE,6BAUhE;IADCD,EAHA,CAAAmB,UAAA,sBAAAsD,kFAAA;MAAAzE,EAAA,CAAAqB,aAAA,CAAAqD,IAAA;MAAA,MAAAhE,MAAA,GAAAV,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAyB,WAAA,CAAYf,MAAA,CAAAiE,QAAA,EAAU;IAAA,EAAC,2BAAAC,uFAAAhD,MAAA;MAAA5B,EAAA,CAAAqB,aAAA,CAAAqD,IAAA;MAAA,MAAAhE,MAAA,GAAAV,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAyB,WAAA,CACNf,MAAA,CAAAmE,mBAAA,CAAAjD,MAAA,CAA2B;IAAA,EAAC,8BAAAkD,0FAAA;MAAA9E,EAAA,CAAAqB,aAAA,CAAAqD,IAAA;MAAA,MAAAhE,MAAA,GAAAV,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAyB,WAAA,CAEzBf,MAAA,CAAAqE,WAAA,CAAY;QAAAC,EAAA,EAAK,cAAc;QAAAhE,KAAA,EAAS;MAAc,CAAC,CAAC;IAAA,EAAC;IAEjFhB,EADG,CAAAG,YAAA,EAAqB,EAClB;;;;IATFH,EAAA,CAAAI,SAAA,EAA6B;IAM7BJ,EANA,CAAAa,UAAA,iBAAAH,MAAA,CAAAuE,YAAA,CAA6B,qBAAAvE,MAAA,CAAAwE,gBAAA,CACQ,aAAAxE,MAAA,CAAAyE,QAAA,CAChB,cAAAzE,MAAA,CAAA0E,SAAA,CACE,WAAA1E,MAAA,CAAA2E,MAAA,CAGN;;;;;;IAMnBrF,EADF,CAAAC,cAAA,cAAiE,2BAI9D;IADCD,EAAA,CAAAmB,UAAA,oBAAAmE,8EAAA;MAAAtF,EAAA,CAAAqB,aAAA,CAAAkE,IAAA;MAAA,MAAA7E,MAAA,GAAAV,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAyB,WAAA,CAAUf,MAAA,CAAA8E,aAAA,CAAc,QAAQ,CAAC;IAAA,EAAC;IAEtCxF,EADG,CAAAG,YAAA,EAAmB,EAChB;;;;IAHFH,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAa,UAAA,YAAAH,MAAA,CAAA+E,WAAA,CAAuB;;;ADxNnC,WAAaC,0BAA0B;EAAjC,MAAOA,0BAA0B;IAmF3BC,KAAA;IACAC,MAAA;IACAC,eAAA;IACAC,YAAA;IACAC,aAAA;IACAC,WAAA;IAvFVC,cAAc,GAAc,CAC1B;MAAEjB,EAAE,EAAE,UAAU;MAAEhE,KAAK,EAAE;IAAgB,CAAE,EAC3C;MAAEgE,EAAE,EAAE,cAAc;MAAEhE,KAAK,EAAE;IAAc,CAAE,EAC7C;MAAEgE,EAAE,EAAE,cAAc;MAAEhE,KAAK,EAAE,SAAS;MAAEkF,QAAQ,EAAE;IAAI,CAAE,CACzD;IACD;IACAC,UAAU,GAAkB,IAAI;IAChCC,YAAY,GAAW,UAAU;IAEjCC,SAAS,GAAGvG,iBAAwC;IAGpDwG,iBAAiB;IAEjB;IACAC,YAAY,GAAkB,EAAE;IAChCC,gBAAgB,GAAW,CAAC;IAC5BtB,gBAAgB;IAChBE,SAAS,GAAY,KAAK;IAC1BC,MAAM,GAAoBtF,eAAe,CAAC0G,UAAU;IAEpD;IACAC,YAAY,GAAkB,EAAE;IAChCC,gBAAgB,GAAY,KAAK;IACjCC,SAAS,GAAG,EAAE;IAEd;IACAC,YAAY,GAAiB,EAAE;IACxBxE,YAAY;IACZyE,QAAQ,GAAY,MAAM;IAEjC;IACAC,kBAAkB,GAAgB,IAAI;IACtCC,kBAAkB,GAAY,KAAK;IACnCC,WAAW;IAEJhC,YAAY,GAAU,EAAE;IAC/BiC,kBAAkB,GAAGrH,WAAW,CAACsH,kBAAkB,IAAI,KAAK;IAErDC,mBAAmB,GAAY,KAAK;IAC3CC,gBAAgB;IAEhB;IACQC,QAAQ,GAAG,IAAIrI,OAAO,EAAQ;IACtCsI,WAAW,GAAW,gBAAgB;IACtCC,QAAQ,GAAa,CACnB;MAAExC,EAAE,EAAE,UAAU;MAAEhE,KAAK,EAAE;IAAgB,CAAE,EAC3C;MAAEgE,EAAE,EAAE,QAAQ;MAAEhE,KAAK,EAAE;IAAc,CAAE,EACvC;MAAEgE,EAAE,EAAE,SAAS;MAAEhE,KAAK,EAAE,SAAS;MAAEkF,QAAQ,EAAE;IAAI,CAAE,CACpD;IACDuB,QAAQ,GAAG,KAAK;IAChBC,UAAU;IACVjC,WAAW,GAAG,EAAE;IAChBkC,WAAW,GAAG,KAAK;IACnBC,WAAW,GAAa,KAAK;IAC7BC,aAAa,GAAW,EAAE;IAC1BC,cAAc,GAAU,EAAE;IAC1BC,aAAa,GAAU,EAAE;IACzB5C,QAAQ,GAAG,CAAC;IACZ6C,SAAS,GAAG,KAAK;IACjBC,WAAW,GAAW,EAAE;IAExBC,eAAe,GAAa,EAAE;IAC9BC,iBAAiB,GAAW,CAAC;IAC7BC,WAAW,GAAW,UAAU;IAEhC;IACAC,oBAAoB,GAAG,KAAK;IAC5B7E,cAAc,GAAc,EAAE;IAC9BF,aAAa,GAA6B,EAAE;IAC5ClB,aAAa,GAAc,EAAE;IAE7B;IACAkG,YAAY,GAAU,EAAE;IACxBrH,kBAAkB,GAAG,CAAC;IAEtB;IACAsH,cAAc,GAAU,EAAE;IAC1BC,cAAc,GAAoC,EAAE;IAGpDC,YACU9C,KAAqB,EACrBC,MAAc,EACdC,eAAgC,EAChCC,YAAiC,EACjCC,aAA4B,EAC5BC,WAAwB;MALxB,KAAAL,KAAK,GAALA,KAAK;MACL,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,eAAe,GAAfA,eAAe;MACf,KAAAC,YAAY,GAAZA,YAAY;MACZ,KAAAC,aAAa,GAAbA,aAAa;MACb,KAAAC,WAAW,GAAXA,WAAW;IAClB;IAEH0C,QAAQA,CAAA;MACN,IAAI,CAAC3C,aAAa,CAAC4C,aAAa,EAAE;MAClC,IAAI,CAACpB,WAAW,GAAG,gBAAgB;MACnC,IAAI,CAACN,WAAW,GAAG2B,MAAM,CAACC,UAAU,EAAE;MACtC;MACA,IAAI,CAAClD,KAAK,CAACmD,QAAQ,CAACC,IAAI,CAAC7J,SAAS,CAAC,IAAI,CAACoI,QAAQ,CAAC,CAAC,CAAC0B,SAAS,CAAEC,MAAM,IAAI;QACtE,IAAI,CAAC9C,UAAU,GAAG8C,MAAM,CAACC,GAAG,CAAC,IAAI,CAAC;QAClC,IAAI,IAAI,CAAC/C,UAAU,EAAE;UACnB,IAAI,CAACgD,YAAY,CAAC,IAAI,CAAChD,UAAU,CAAC;QACpC,CAAC,MAAM;UACL;UACA,IAAI,CAACP,MAAM,CAACwD,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;QAC5C;MACF,CAAC,CAAC;MACF;IACF;IAEAC,WAAWA,CAAA;MACT,IAAI,CAAC/B,QAAQ,CAACgC,IAAI,EAAE;MACpB,IAAI,CAAChC,QAAQ,CAACiC,QAAQ,EAAE;MACxB,IAAI,CAACxD,aAAa,CAACyD,YAAY,EAAE;IACnC;IACAzE,WAAWA,CAAC0E,KAAoC;MAC9C,IAAI,CAAClC,WAAW,GAAGkC,KAAK,CAACzI,KAAK;MAC9B,IAAI,CAACoH,WAAW,GAAGqB,KAAK,CAACzE,EAAE;MAC3B0E,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEF,KAAK,CAAC;IACpC;IAEA;IACAN,YAAYA,CAACnE,EAAU;MACrB;MACA0E,OAAO,CAACC,GAAG,CAAC,6BAA6B3E,EAAE,EAAE,CAAC;MAC9C,IAAI,CAAC0B,YAAY,GAAG,CAClB;QACEkD,IAAI,EAAE,IAAI;QACVC,IAAI,EAAE;OACQ,CACjB;MACD,IAAI,CAACxH,YAAY,GAAG,IAAI,CAAC2D,WAAW,CAAC8D,KAAK,CAAC,EAAE,CAAC;MAE9C,IAAI,CAACjE,eAAe,CAACkE,eAAe,CAAC/E,EAAE,CAAC,CAACgE,SAAS,CAAC;QAC/CM,IAAI,EAAGU,GAAG,IAAI;UACd;UACA,IAAIA,GAAG,CAACC,QAAQ,IAAID,GAAG,CAACC,QAAQ,CAACC,cAAc,EAAE;YAC/C,IAAI,CAAC3B,cAAc,GAAGyB,GAAG,CAACC,QAAQ,CAACC,cAAc;YACjD,IAAI,CAAC9D,YAAY,GAAG4D,GAAG,CAACC,QAAQ,CAACxG,IAAI;YACrC,IAAI,CAAC0G,qBAAqB,EAAE;UAC9B,CAAC,MAAM;YACL;YACA,IAAI,CAACrC,cAAc,GAAGkC,GAAG,CAAClC,cAAc;YACxC,IAAI,CAAC1B,YAAY,GAAG4D,GAAG,CAACvG,IAAI;YAC5B,IAAI,CAACsE,aAAa,GAAG,IAAI,CAACqC,iBAAiB,CAAC,IAAI,CAACtC,cAAc,CAAC;UAClE;UAEA,IAAG,IAAI,CAACC,aAAa,CAAClF,MAAM,KAAK,CAAC,EAAC;YACjC,IAAI,CAAC+E,WAAW,GAAG,IAAI;UACzB;UAEA,IAAI,CAACyC,cAAc,EAAE;UACrB,IAAI,CAACC,qBAAqB,EAAE;UAC5B,IAAI,CAACC,iBAAiB,EAAE;UACxB,IAAI,CAACC,oBAAoB,EAAE;QAC7B,CAAC;QACCC,KAAK,EAAGC,GAAG,IAAI;UACb;UACA,IAAI,CAACC,gBAAgB,EAAE;UACvBjB,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEe,GAAG,CAAC;QACzD;OACH,CAAC;IAEJ;IAEOpI,YAAYA,CAACvB,KAAa;MAC/B,MAAM6J,KAAK,GAAG7J,KAAK,CAAC6J,KAAK,CAAC,WAAW,CAAC;MACtC,IAAIA,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,EAAE;QACrB,MAAMC,YAAY,GAAGD,KAAK,CAAC,CAAC,CAAC,CAACE,IAAI,EAAE;QACpC,OAAOD,YAAY,CAACE,UAAU,CAAC,OAAO,CAAC,IAAIF,YAAY,CAACE,UAAU,CAAC,OAAO,CAAC;MAC7E;MACA,OAAO,KAAK;IACd;IAEOV,cAAcA,CAAA;MACnB,IAAI,CAACtC,aAAa,CAACiD,OAAO,CAAEhK,KAAU,IAAI;QACxC,IAAI,CAACqB,YAAY,CAAC4I,UAAU,CAACjK,KAAK,CAACD,KAAK,EAAE,IAAI,CAACiF,WAAW,CAACkF,OAAO,CAAC,EAAE,EAAE7L,UAAU,CAAC8L,QAAQ,CAAC,CAAC;MAC9F,CAAC,CAAC;IACJ;IAEAC,iBAAiBA,CAAA;MACf,IAAI,CAACjG,QAAQ,GAAG,CAAC;MACjB,IAAI,CAACkC,gBAAgB,GAAGgE,WAAW,CAAC,MAAK;QACvC,IAAI,IAAI,CAAClG,QAAQ,GAAG,EAAE,EAAE;UACtB,IAAI,CAACA,QAAQ,IAAI,CAAC,CAAC,CAAC;QACtB;MACF,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACX;IAEAmG,gBAAgBA,CAAA;MACdC,aAAa,CAAC,IAAI,CAAClE,gBAAgB,CAAC;MACpC,IAAI,CAAClC,QAAQ,GAAG,GAAG;MAEnBqG,UAAU,CAAC,MAAK;QACd,IAAI,CAACxD,SAAS,GAAG,KAAK;MACxB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACX;IAEA;IACAyD,iBAAiBA,CAACC,OAAe;MAC/B;MACA,IAAI,CAAC/E,gBAAgB,GAAG,IAAI;MAC5B,IAAG+E,OAAO,CAACZ,IAAI,EAAE,KAAK,EAAE,EAAC;QACvB,IAAG,IAAI,CAAC5C,eAAe,CAACrF,MAAM,KAAK,CAAC,EAAC;UACnC,IAAI,CAACyD,iBAAiB,CAACqF,aAAa,CAAC,2BAA2B,CAAC;UACjE,IAAI,CAACC,eAAe,EAAE;QACxB;QACA;MACF;MAEA,IAAG,IAAI,CAACxE,mBAAmB,IAAI,IAAI,CAACe,iBAAiB,KAAG,IAAI,CAACD,eAAe,CAACrF,MAAM,EAAC;QAChF,IAAI,CAACyD,iBAAiB,CAACqF,aAAa,CAAC,2BAA2B,CAAC;QACjE,IAAI,CAACC,eAAe,EAAE;QACxB;MACF;MAEA,MAAMC,KAAK,GAAG,IAAI,CAAC3D,eAAe,CAAC,IAAI,CAACC,iBAAiB,CAAC;MAC1D,IAAI,IAAI,CAAC7F,YAAY,CAACuJ,KAAK,CAAC,EAAE;QAC5B;QACA,IAAI,CAACvF,iBAAiB,CAACqF,aAAa,CAAC,mCAAmCE,KAAK,EAAE,CAAC;QAChF;MACF;MAEA,IAAI,CAACxJ,YAAY,CAAC6G,GAAG,CAAC2C,KAAK,CAAC,EAAEC,QAAQ,CAACJ,OAAO,CAAC;MAC/C,IAAI,CAACvD,iBAAiB,EAAE;MAExB,IAAI,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAACD,eAAe,CAACrF,MAAM,EAAE;QACxD,IAAI,CAACkJ,qBAAqB,EAAE;MAC9B,CAAC,MAAM;QACL,IAAI,CAACzF,iBAAiB,CAACqF,aAAa,CAAC,oDAAoD,CAAC;QAC1F,IAAI,CAACC,eAAe,EAAE;MACxB;IACF;IAEA;IACAjH,QAAQA,CAAA;MACN+E,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACvC;IACF;IAEA;IACAnE,aAAaA,CAACwG,OAA8B;MAC1CtC,OAAO,CAACC,GAAG,CAAC,aAAaqC,OAAO,UAAU,CAAC;MAE3C,IAAIA,OAAO,KAAK,UAAU,EAAE;QAC1B,MAAMC,IAAI,GAAG,IAAI,CAAC1F,YAAY,CAC3B2F,GAAG,CAAEvC,GAAG,IAAK,IAAIA,GAAG,CAACwC,SAAS,KAAKxC,GAAG,CAAC+B,OAAO,EAAE,CAAC,CACjDU,IAAI,CAAC,IAAI,CAAC;QACb,IAAI,CAACC,cAAc,CAACJ,IAAI,EAAE,4BAA4B,EAAE,YAAY,CAAC;MACvE,CAAC,MAAM;QACL,MAAMA,IAAI,GAAGK,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC1F,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC;QACvD,IAAI,CAACwF,cAAc,CAACJ,IAAI,EAAE,uBAAuB,EAAE,kBAAkB,CAAC;MACxE;IACF;IAEA;IACQI,cAAcA,CAACJ,IAAY,EAAEO,QAAgB,EAAEC,IAAY;MACjE,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACV,IAAI,CAAC,EAAE;QAAEQ;MAAI,CAAE,CAAC;MACvC,MAAMG,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;MACrC,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGN,GAAG;MACfG,IAAI,CAACI,QAAQ,GAAGX,QAAQ;MACxBO,IAAI,CAACK,KAAK,EAAE;MACZP,GAAG,CAACQ,eAAe,CAACT,GAAG,CAAC;IAC1B;IAEA;IACA/H,mBAAmBA,CAACyI,MAAiC;MACnD5D,OAAO,CAACC,GAAG,CAAC,mBAAmB2D,MAAM,EAAE,CAAC;MACxC;MAEA,IAAIA,MAAM,KAAK,MAAM,EAAE;QACrB,IAAI,CAAClI,SAAS,GAAG,IAAI;MACvB,CAAC,MAAM,IAAIkI,MAAM,KAAK,OAAO,IAAIA,MAAM,KAAK,MAAM,EAAE;QAClD,IAAI,CAAClI,SAAS,GAAG,KAAK;MACxB;IACF;IAEA;IACAmI,YAAYA,CAAA;MACV,IAAI,CAAC3H,MAAM,CAACwD,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;IAC5C;IAEA;IACAoE,YAAYA,CAAA;MACV,IAAI,IAAI,CAACrH,UAAU,EAAE;QACnB,IAAI,CAACP,MAAM,CAACwD,QAAQ,CAAC,CAAC,uBAAuB,EAAE,IAAI,CAACjD,UAAU,CAAC,CAAC;MAClE;IACF;IAEOsH,kBAAkBA,CAACC,KAAA,GAAgB,IAAI;MAC5ClC,UAAU,CAAC,MAAK;QACd,IAAI,CAAC,IAAI,CAACpE,mBAAmB,EAAE;UAC7BsC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACtD,SAAS,CAAC;UAC3BqD,OAAO,CAACC,GAAG,CAAC,IAAI,CAACtD,SAAS,CAAC,QAAQ,CAAC,CAACsH,sBAAsB,CAAC;UAC5D,IAAI,CAAC1I,YAAY,CAAC2I,IAAI,CAAC;YACrBC,OAAO,EAAE,IAAI,CAACxH,SAAS,CAAC,QAAQ,CAAC,CAACsH,sBAAsB;YACxDG,KAAK,EAAE;WACR,CAAC;QACJ;MACF,CAAC,EAAEJ,KAAK,CAAC;IACX;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEOK,eAAeA,CAAC9G,WAAmB;MACxC,IAAI,CAACpB,eAAe,CACjBmI,kBAAkB,CAAC/G,WAAW,CAAC,CAC/B8B,IAAI,CAAC7J,SAAS,CAAC,IAAI,CAACoI,QAAQ,CAAC,CAAC,CAC9B0B,SAAS,CAAC;QACTM,IAAI,EAAGoC,OAAO,IAAI;UAChBhC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE+B,OAAO,CAAC;UACjC,MAAM;YAAEmC,OAAO;YAAEC;UAAK,CAAE,GAAGpC,OAAO;UAClC,IAAIoC,KAAK,EAAE;YACT,IAAI,CAAC7I,YAAY,CAAC2I,IAAI,CAAC;cAAEC,OAAO;cAAEC;YAAK,CAAE,CAAC;UAC5C,CAAC,MAAM,IAAI,IAAI,CAAC5G,kBAAkB,KAAK,KAAK,EAAE;YAC5C;UAAA;QAEJ,CAAC;QACDuD,KAAK,EAAGC,GAAG,IAAI;UACb,IAAI,CAACzF,YAAY,CAAC2I,IAAI,CAAC;YACrBC,OAAO,EAAE,IAAI,CAACxH,SAAS,CAAC,aAAa,CAAC;YACtCyH,KAAK,EAAE;WACR,CAAC;UACFpE,OAAO,CAACe,KAAK,CAAC,kBAAkB,EAAEC,GAAG,CAAC;QACxC,CAAC;QACDnB,QAAQ,EAAEA,CAAA,KAAK;UACb,IAAI,CAACkE,kBAAkB,EAAE;UACzB/D,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC5C;OACD,CAAC;IACN;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEOsE,YAAYA,CAACC,MAAc;MAChC,IAAI,CAACvG,WAAW,GAAG,KAAK;MACxB,IAAI;QACF,MAAMwG,YAAY,GAAG7B,IAAI,CAAC8B,KAAK,CAACF,MAAM,CAAC;QACvC,IAAI,CAACvG,WAAW,GAAG,IAAI;QACvB,OAAOwG,YAAY;MACrB,CAAC,CAAC,OAAOE,CAAC,EAAE;QACV,OAAO,IAAI;MACb;IACF;IAEOzC,eAAeA,CAAA;MACpB,IAAI0C,OAAO,GAAmC,IAAIC,QAAQ,EAAE;MAC5D,IAAIC,WAAW,GAAG,EAAE;MAGpB,IAAI,CAACnJ,MAAM,GAAGtF,eAAe,CAAC0O,OAAO;MACrC,IAAI,IAAI,CAAC5G,aAAa,CAAChF,MAAM,EAAE;QAC7B,IAAI,CAACgF,aAAa,CAACmD,OAAO,CAAE0D,IAAI,IAAI;UAClCJ,OAAO,CAACK,MAAM,CAAC,OAAO,EAAED,IAAI,CAAC;QAC/B,CAAC,CAAC;QACFJ,OAAO,CAACK,MAAM,CAAC,YAAY,EAAE,IAAI,CAACxI,UAAU,CAAC;QAC7CmI,OAAO,CAACK,MAAM,CAAC,YAAY,EAAErC,IAAI,CAACC,SAAS,CAAC,IAAI,CAAClK,YAAY,CAACuM,KAAK,CAAC,CAAC;QACrEN,OAAO,CAACK,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC7I,YAAY,CAAC+I,aAAa,EAAE,CAAC;QACzDP,OAAO,CAACK,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC1H,WAAW,CAAC;QAC/CuH,WAAW,GAAG,QAAQ;MACxB,CAAC,MAAM;QACLF,OAAO,GAAG;UACRQ,UAAU,EAAE,IAAI,CAAC3I,UAAU;UAC3B4I,UAAU,EAAE,IAAI,CAAC1M,YAAY,CAACuM,KAAK;UACnC3H,WAAW,EAAE,IAAI,CAACA,WAAW;UAC7B+H,IAAI,EAAE,IAAI,CAAClJ,YAAY,CAAC+I,aAAa;SACtC;MACH;MAEA,IAAI,CAACd,eAAe,CAAC,IAAI,CAAC9G,WAAW,CAAC;MACtC,IAAI,CAACmE,iBAAiB,EAAE;MAExB,IAAI,CAACvF,eAAe,CACjB+F,eAAe,CAAC0C,OAAO,EAAEE,WAAW,CAAC,CACrCzF,IAAI,CAAC7J,SAAS,CAAC,IAAI,CAACoI,QAAQ,CAAC,CAAC,CAC9B0B,SAAS,CAAC;QACTM,IAAI,EAAGU,GAAG,IAAI;UACZ,IAAI,CAACrD,gBAAgB,GAAG,KAAK;UAC7B,IAAI,CAACvB,SAAS,GAAG,KAAK;UACtB,IAAI,CAACkB,iBAAiB,CAACqF,aAAa,CAAC3B,GAAG,EAAE0B,OAAO,IAAI,4CAA4C,CAAC;UAElG,IAAI1B,GAAG,EAAEiF,gBAAgB,EAAEhF,QAAQ,EAAEiE,MAAM,EAAE;YAC3C,IAAI,CAAC9G,mBAAmB,GAAG,IAAI;YAC/B;YACA,IAAI,CAACnC,YAAY,CAAC2I,IAAI,CAAC;cACrBC,OAAO,EAAE,IAAI,CAACxH,SAAS,CAAC,QAAQ,CAAC,CAAC6I,oBAAoB;cACtDpB,KAAK,EAAE;aACR,CAAC;YACF,IAAI,CAACrG,QAAQ,GAAG,KAAK;YACrB,IAAI,CAACC,UAAU,GAAGsC,GAAG,EAAEiF,gBAAgB,EAAEhF,QAAQ,EAAEiE,MAAM;YACzD,IAAI,CAACrH,YAAY,GAAGmD,GAAG,EAAEiF,gBAAgB,EAAEhF,QAAQ,EAAEkF,YAAY,CAACjD,GAAG,CAAEkD,IAAS,IAAI;cAClF,OAAO;gBACLpK,EAAE,EAAEoK,IAAI,EAAEpK,EAAE,IAAI,EAAE;gBAClBqK,KAAK,EAAED,IAAI,EAAEC,KAAK,IAAI,EAAE;gBACxBxB,OAAO,EAAEuB,IAAI,EAAEvB,OAAO,IAAI,EAAE;gBAC5ByB,SAAS,EAAEF,IAAI,EAAEE,SAAS,IAAI,EAAE;gBAChCnD,SAAS,EAAEiD,IAAI,EAAEjD,SAAS,IAAI,EAAE;gBAChCM,IAAI,EAAE2C,IAAI,EAAE3C,IAAI,IAAI,EAAE;gBACtB8C,WAAW,EAAEH,IAAI,EAAEG,WAAW,IAAI,EAAE;gBACpCC,eAAe,EAAEJ,IAAI,EAAEI,eAAe,IAAI,EAAE;gBAC5CC,OAAO,EAAEL,IAAI,EAAEK,OAAO,IAAI,EAAE;gBAC5BC,GAAG,EAAEN,IAAI,EAAEM,GAAG,IAAI;eACnB;YACH,CAAC,CAAC;YAEF,IAAI,CAACjK,WAAW,GAAGuE,GAAG,EAAEiF,gBAAgB,EAAEhF,QAAQ,EAAEkF,YAAY,CAACjD,GAAG,CACjEkD,IAKA,IAAI;cACH,OAAO;gBACLG,WAAW,EAAEH,IAAI,CAACG,WAAW;gBAC7BE,OAAO,EAAEL,IAAI,CAACK,OAAO;gBACrBC,GAAG,EAAEN,IAAI,CAACM,GAAG;gBACbF,eAAe,EAAEJ,IAAI,CAACI;eACvB;YACH,CAAC,CACF;YAED;YACA;YACA;YAEA;YACA;YACA;YACA;YACA;YACA;UACF;UACA,IAAI,CAACvB,YAAY,CAAC,IAAI,CAACvG,UAAU,CAAC;UAClC,IAAI,CAACrC,MAAM,GAAGtF,eAAe,CAACwD,SAAS;UACvC,IAAI,CAAC+H,gBAAgB,EAAE;UACvB,IAAI,CAACzD,aAAa,GAAG,EAAE;QACzB,CAAC;QACD4C,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACrD,mBAAmB,GAAG,IAAI;UAC/B,IAAI,CAACT,gBAAgB,GAAG,KAAK;UAC7B,IAAI,CAACc,QAAQ,GAAG,IAAI;UACpB,IAAI,CAACC,UAAU,GAAG+C,KAAK,EAAEA,KAAK,EAAEkF,MAAM;UACtC,IAAI,CAAC9J,eAAe,CAAC+J,qBAAqB,EAAE;UAC5C,IAAI,CAAC3K,YAAY,CAAC2I,IAAI,CAAC;YACrBC,OAAO,EAAE,IAAI,CAACxH,SAAS,CAAC,QAAQ,CAAC,CAACwJ,iBAAiB;YACnD/B,KAAK,EAAE;WACR,CAAC;UACF,IAAI,CAACxH,iBAAiB,CAACqF,aAAa,CAClC,sDAAsD,CACvD;UACD,IAAI,CAAC9D,aAAa,GAAG,EAAE;UACvB,IAAI,CAACyD,gBAAgB,EAAE;UACvB5B,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEc,KAAK,CAACiB,OAAO,CAAC;QACxC;OACD,CAAC;IACN;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEAoE,gBAAgBA,CAAA;MACdpG,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;IACjC;IAEAoG,qBAAqBA,CAACC,KAAa;MACjC,IAAG,IAAI,CAAC7H,iBAAiB,KAAG,IAAI,CAACD,eAAe,CAACrF,MAAM,IAAI,IAAI,CAACqF,eAAe,CAACrF,MAAM,KAAG,CAAC,EAAC;QACzF,IAAI,CAACgF,aAAa,GAAGmI,KAAK;QAC1B;MACF;MAEA,MAAMnE,KAAK,GAAG,IAAI,CAAC3D,eAAe,CAAC,IAAI,CAACC,iBAAiB,CAAC;MAC1D,IAAG,IAAI,CAAC7F,YAAY,CAACuJ,KAAK,CAAC,EAAC;QAC1B,IAAImE,KAAK,IAAIA,KAAK,CAACnN,MAAM,GAAG,CAAC,EAAE;UAC7B;UACA,MAAMoN,SAAS,GAAG;YAAEC,MAAM,EAAE;cAAEF,KAAK,EAAE,CAACA,KAAK,CAAC,CAAC,CAAC;YAAC;UAAE,CAAE;UACnD,IAAI,CAACnO,eAAe,CAACoO,SAAS,EAAEpE,KAAK,CAAC;QACxC;MACF,CAAC,MAAM;QACL,IAAI,CAAChE,aAAa,GAAGmI,KAAK;MAC5B;IACF;IAEAxF,oBAAoBA,CAAA;MAClB,IAAI,CAACtC,eAAe,GAAGiI,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC/N,YAAY,CAACgO,QAAQ,CAAC;MAC9D,IAAI,CAAClI,iBAAiB,GAAG,CAAC;MAC1B,IAAI,IAAI,CAACD,eAAe,CAACrF,MAAM,GAAG,CAAC,EAAE;QACnC,IAAI,CAACkJ,qBAAqB,EAAE;MAC9B,CAAC,MACG;QACF,IAAI,CAACnE,WAAW,GAAG,IAAI;QACvB,IAAI,CAACtB,iBAAiB,CAACqF,aAAa,CAAC,uFAAuF,CAAC;MAC/H;IACF;IAEAI,qBAAqBA,CAAA;MACnB,MAAMF,KAAK,GAAG,IAAI,CAAC3D,eAAe,CAAC,IAAI,CAACC,iBAAiB,CAAC;MAC1D,IAAI,IAAI,CAAC7F,YAAY,CAACuJ,KAAK,CAAC,EAAE;QAC5B,IAAI,CAAC/E,QAAQ,GAAG,sBAAsB;QACtC,IAAI,CAACR,iBAAiB,CAACqF,aAAa,CAAC,8BAA8BE,KAAK,EAAE,CAAC;QAC3E;MACF,CAAC,MAAM;QACL,IAAI,CAAC/E,QAAQ,GAAG,MAAM,CAAC,CAAC;QACxB,IAAI,CAACR,iBAAiB,CAACqF,aAAa,CAAC,6BAA6BE,KAAK,EAAE,CAAC;MAC5E;IACF;IAIA;IACAyE,eAAeA,CAAA;MACb,IAAI,CAACjI,oBAAoB,GAAG,CAAC,IAAI,CAACA,oBAAoB;IACxD;IAEAnF,oBAAoBA,CAACT,KAAa;MAChC,IAAI,CAACe,cAAc,CAACf,KAAK,CAAC,GAAG,CAAC,IAAI,CAACe,cAAc,CAACf,KAAK,CAAC;IAC1D;IAEA6H,qBAAqBA,CAAA;MACnB,MAAMiG,UAAU,GAAG,IAAI,CAAChI,cAAc,CAAC1F,MAAM,IAAI,IAAI,CAACiF,cAAc,CAACjF,MAAM;MAC3E,IAAI,CAACW,cAAc,GAAG,IAAIgN,KAAK,CAACD,UAAU,CAAC,CAACE,IAAI,CAAC,KAAK,CAAC;MACvD,IAAI,CAACnN,aAAa,GAAG,IAAIkN,KAAK,CAACD,UAAU,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC,CAACvE,GAAG,CAAC,OAAO;QAAE3I,SAAS,EAAE;MAAK,CAAE,CAAC,CAAC;MACpF,IAAI,CAACnB,aAAa,GAAG,IAAIoO,KAAK,CAACD,UAAU,CAAC,CAACE,IAAI,CAAC,KAAK,CAAC;IACxD;IAEA;IACAtG,qBAAqBA,CAAA;MACnB,IAAI,CAAC5B,cAAc,CAACyC,OAAO,CAAC,CAAC0F,aAAa,EAAEjO,KAAK,KAAI;QACnD,MAAMlC,KAAK,GAAGmQ,aAAa,CAACnQ,KAAK;QACjC,MAAMoQ,WAAW,GAAG,IAAI,CAACC,kBAAkB,CAACrQ,KAAK,CAAC;QAClD,IAAI,CAACiI,cAAc,CAAC/F,KAAK,CAAC,GAAGkO,WAAW;MAC1C,CAAC,CAAC;MAEF;MACA,IAAI,CAAC5I,aAAa,GAAG,EAAE;MACvBoI,MAAM,CAACU,MAAM,CAAC,IAAI,CAACrI,cAAc,CAAC,CAACwC,OAAO,CAAC8F,MAAM,IAAG;QAClD,IAAI,CAAC/I,aAAa,CAAC6F,IAAI,CAAC,GAAGkD,MAAM,CAAC;MACpC,CAAC,CAAC;IACJ;IAEA;IACAF,kBAAkBA,CAACrQ,KAAU;MAC3B,MAAMwQ,oBAAoB,GAAG,2BAA2B;MACxD,MAAMxB,WAAW,GAAGhP,KAAK,CAAC6O,IAAI,EAAEG,WAAW,IAAI,EAAE;MACjD,MAAMyB,OAAO,GAAG,CAAC,GAAGzB,WAAW,CAAC0B,QAAQ,CAACF,oBAAoB,CAAC,CAAC;MAE/D,OAAOC,OAAO,CAAC9E,GAAG,CAACtB,KAAK,KAAK;QAC3B7J,KAAK,EAAE6J,KAAK,CAAC,CAAC,CAAC;QAAE;QACjBsG,WAAW,EAAEtG,KAAK,CAAC,CAAC,CAAC;QAAE;QACvB5J,KAAK,EAAE,IAAI,CAACmQ,gBAAgB,CAACvG,KAAK,CAAC,CAAC,CAAC,CAAC;QACtC0E,SAAS,EAAE/O,KAAK,CAACkD;OAClB,CAAC,CAAC;IACL;IAEA;IACA0N,gBAAgBA,CAACD,WAAmB;MAClC,OAAOA,WAAW,CACfE,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAClBA,OAAO,CAAC,OAAO,EAAEC,CAAC,IAAIA,CAAC,CAACC,WAAW,EAAE,CAAC;IAC3C;IAEA;IACA/G,iBAAiBA,CAAA;MACf,IAAI,CAACjC,YAAY,GAAG,IAAI,CAACC,cAAc,CAAC2D,GAAG,CAAC,CAACwE,aAAa,EAAEjO,KAAK,MAAM;QACrEuC,EAAE,EAAEvC,KAAK;QACTzB,KAAK,EAAE0P,aAAa,CAACnQ,KAAK,CAACkD,IAAI;QAC/BF,SAAS,EAAE,KAAK;QAChBgO,MAAM,EAAE9O,KAAK,KAAK;OACnB,CAAC,CAAC;MACH,IAAI,CAACxB,kBAAkB,GAAG,CAAC;IAC7B;IAEA;IACOmJ,iBAAiBA,CAACtC,cAAqB;MAC5C,MAAMiJ,oBAAoB,GAAG,qCAAqC;MAClE,MAAMS,cAAc,GAAoE,EAAE;MAE1F1J,cAAc,CAACkD,OAAO,CAAEzK,KAAU,IAAI;QACpC,MAAM+O,SAAS,GAAG/O,KAAK,EAAEkD,IAAI,IAAI,SAASqE,cAAc,CAAC2J,OAAO,CAAClR,KAAK,CAAC,GAAG,CAAC,EAAE;QAC7E,MAAMmR,gBAAgB,GAAGnR,KAAK,EAAEgP,WAAW,IAAIhP,KAAK,EAAE6O,IAAI,EAAEG,WAAW,IAAI,EAAE;QAC7E,MAAMyB,OAAO,GAAGU,gBAAgB,CAACT,QAAQ,CAACF,oBAAoB,CAAC,IAAI,EAAE;QAErE,KAAK,MAAMnG,KAAK,IAAIoG,OAAO,EAAE;UAC3B,MAAME,WAAW,GAAGtG,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC;UACxC,MAAM+G,gBAAgB,GAAG/G,KAAK,CAAC,CAAC,CAAC;UACjC,IAAI,CAAC4G,cAAc,CAACN,WAAW,CAAC,EAAE;YAChCM,cAAc,CAACN,WAAW,CAAC,GAAG;cAAEU,MAAM,EAAE,IAAIC,GAAG,EAAE;cAAEf,MAAM,EAAE,IAAIe,GAAG;YAAE,CAAE;UACxE;UACAL,cAAc,CAACN,WAAW,CAAC,CAACU,MAAM,CAACE,GAAG,CAACxC,SAAS,CAAC;UACjDkC,cAAc,CAACN,WAAW,CAAC,CAACJ,MAAM,CAACgB,GAAG,CAACH,gBAAgB,CAAC;QAC1D;MACF,CAAC,CAAC;MAEF,OAAOxB,MAAM,CAAC4B,OAAO,CAACP,cAAc,CAAC,CAACtF,GAAG,CAAC,CAAC,CAACgF,WAAW,EAAE;QAAEU,MAAM;QAAEd;MAAM,CAAE,CAAC,MAAM;QAChFrN,IAAI,EAAE,CAAC,GAAGmO,MAAM,CAAC,CAAC/O,MAAM,GAAG,CAAC,GACxB,GAAG,CAAC,GAAG+O,MAAM,CAAC,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC5F,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAGwF,MAAM,CAAC,CAACK,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,GAClE,CAAC,GAAGL,MAAM,CAAC,CAACxF,IAAI,CAAC,OAAO,CAAC;QAC7B8E,WAAW;QACXnQ,KAAK,EAAE,CAAC,GAAG+P,MAAM,CAAC,CAAC,CAAC;OACrB,CAAC,CAAC;IACL;IAEA;IACAoB,YAAYA,CAAA;MACV,OAAO,IAAI,CAAC7P,YAAY,CAAC8P,KAAK,IAAI,CAAC,CAAC,IAAI,CAAChM,UAAU;IACrD;IAEA;IACAiM,eAAeA,CAACC,IAAS;MACvB,IAAI,CAACpR,kBAAkB,GAAGoR,IAAI,CAACrN,EAAE;MACjC,IAAI,CAACxB,cAAc,CAAC6O,IAAI,CAACrN,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;IACvC;IAEA;IACAR,oBAAoBA,CAAA;MAClB,OAAO,IAAI,CAACgE,cAAc,CAAC,IAAI,CAACvH,kBAAkB,CAAC,IAAI,EAAE;IAC3D;IAEA;IACAsD,mBAAmBA,CAAA;MACjB,IAAI,IAAI,CAACgE,cAAc,CAAC,IAAI,CAACtH,kBAAkB,CAAC,EAAE;QAChD,OAAO,IAAI,CAACsH,cAAc,CAAC,IAAI,CAACtH,kBAAkB,CAAC,CAACV,KAAK,CAACkD,IAAI;MAChE;MACA,OAAO,SAAS,IAAI,CAACxC,kBAAkB,GAAG,CAAC,EAAE;IAC/C;IAEA;IACA2B,cAAcA,CAAC0P,UAAkB;MAC/B,OAAO,IAAI,CAAC9J,cAAc,CAAC8J,UAAU,CAAC,IAAI,EAAE;IAC9C;IAEA;IACA3R,gBAAgBA,CAACJ,KAAU;MACzB,MAAMmD,KAAK,GAAG,CAAC,IAAInD,KAAK,CAACmD,KAAK,IAAI,EAAE,CAAC,EAAE,IAAInD,KAAK,CAACoD,SAAS,IAAI,EAAE,CAAC,CAAC;MAClE,OAAOD,KAAK,CAACwI,GAAG,CAACqG,IAAI,IAAIA,IAAI,CAACC,QAAQ,IAAID,IAAI,CAAC,CAACnG,IAAI,CAAC,IAAI,CAAC,IAAI,UAAU;IAC1E;IAEA;IACA1J,mBAAmBA,CAAC4P,UAAkB;MACpC,IAAI,CAAClQ,aAAa,CAACkQ,UAAU,CAAC,GAAG,CAAC,IAAI,CAAClQ,aAAa,CAACkQ,UAAU,CAAC;IAClE;IAEA;IACAxO,aAAaA,CAAC2F,KAA6B;MACzC,MAAMgJ,aAAa,GAAGhJ,KAAsB;MAC5C,IAAIgJ,aAAa,IAAIA,aAAa,CAACC,QAAQ,EAAE;QAC3C,OAAO,CAAC;MACV;MAEA,IAAIjJ,KAAK,EAAE;QACTA,KAAK,CAACkJ,cAAc,EAAE;MACxB;MAEA;MACA,IAAI,IAAI,CAACC,kBAAkB,EAAE,EAAE;QAC7B,IAAI,CAACC,cAAc,EAAE;MACvB;IACF;IAEA;IACAA,cAAcA,CAAA;MACZ,IAAI,IAAI,CAAC5R,kBAAkB,GAAG,IAAI,CAACsH,cAAc,CAAC1F,MAAM,GAAG,CAAC,EAAE;QAC5D;QACA,IAAI,CAACS,aAAa,CAAC,IAAI,CAACrC,kBAAkB,CAAC,CAACsC,SAAS,GAAG,IAAI;QAC5D,IAAI,CAAC+E,YAAY,CAAC,IAAI,CAACrH,kBAAkB,CAAC,CAACsC,SAAS,GAAG,IAAI;QAE3D;QACA,IAAI,CAACtC,kBAAkB,EAAE;QACzB,IAAI,CAACqH,YAAY,CAAC,IAAI,CAACrH,kBAAkB,CAAC,CAACsQ,MAAM,GAAG,IAAI;QAExD;QACA,IAAI,CAAC/N,cAAc,CAAC,IAAI,CAACvC,kBAAkB,CAAC,GAAG,IAAI;QAEnD;QACA,MAAM6R,SAAS,GAAG,IAAI,CAACvK,cAAc,CAAC,IAAI,CAACtH,kBAAkB,CAAC;QAC9D,IAAI,CAACqF,iBAAiB,CAACqF,aAAa,CAAC,aAAamH,SAAS,CAACvS,KAAK,CAACkD,IAAI,uCAAuC,CAAC;MAChH,CAAC,MAAM;QACL;QACA,IAAI,CAACH,aAAa,CAAC,IAAI,CAACrC,kBAAkB,CAAC,CAACsC,SAAS,GAAG,IAAI;QAC5D,IAAI,CAAC+E,YAAY,CAAC,IAAI,CAACrH,kBAAkB,CAAC,CAACsC,SAAS,GAAG,IAAI;QAC3D,IAAI,CAAC+C,iBAAiB,CAACqF,aAAa,CAAC,sDAAsD,CAAC;MAC9F;IACF;IAEA;IACAiH,kBAAkBA,CAAA;MAChB,MAAMG,aAAa,GAAG,IAAI,CAACvO,oBAAoB,EAAE;MACjD,OAAOuO,aAAa,CAACC,KAAK,CAACjS,KAAK,IAAG;QACjC,MAAMmK,OAAO,GAAG,IAAI,CAAC7I,YAAY,CAAC6G,GAAG,CAACnI,KAAK,CAACA,KAAK,CAAC;QAClD,OAAOmK,OAAO,IAAIA,OAAO,CAACiH,KAAK,IAAIjH,OAAO,CAAC0D,KAAK;MAClD,CAAC,CAAC;IACJ;IAEA;IACAqE,kBAAkBA,CAAA;MAChB,IAAI,IAAI,CAACL,kBAAkB,EAAE,EAAE;QAC7B,IAAI,CAACC,cAAc,EAAE;MACvB;IACF;IAEA;IACAhR,eAAeA,CAAC4H,KAAU,EAAEyJ,SAAiB;MAC3C,MAAMxE,IAAI,GAAGjF,KAAK,CAACyG,MAAM,CAACF,KAAK,CAAC,CAAC,CAAC;MAClC,IAAItB,IAAI,EAAE;QACR,MAAMyE,MAAM,GAAG,IAAIC,UAAU,EAAE;QAC/BD,MAAM,CAACE,MAAM,GAAG,MAAK;UACnB,MAAMC,YAAY,GAAGH,MAAM,CAACI,MAAgB;UAC5C,IAAI,CAAClR,YAAY,CAAC6G,GAAG,CAACgK,SAAS,CAAC,EAAEpH,QAAQ,CAACwH,YAAY,CAAC;UAExD;UACA9H,UAAU,CAAC,MAAK;YACd,IAAI,IAAI,CAACoH,kBAAkB,EAAE,EAAE;cAC7B,IAAI,CAACC,cAAc,EAAE;YACvB;UACF,CAAC,EAAE,GAAG,CAAC;QACT,CAAC;QACDM,MAAM,CAACK,aAAa,CAAC9E,IAAI,CAAC;MAC5B;IACF;IAEA;IACAhN,gBAAgBA,CAACwR,SAAiB;MAChC,MAAMO,SAAS,GAAGzG,QAAQ,CAAC0G,cAAc,CAAC,OAAO,GAAGR,SAAS,CAAC,IAC7ClG,QAAQ,CAAC0G,cAAc,CAAC,eAAe,GAAGR,SAAS,CAAC;MACrE,IAAIO,SAAS,EAAE;QACbA,SAAS,CAACrG,KAAK,EAAE;MACnB;IACF;IAEA;IACAzC,gBAAgBA,CAAA;MACd,IAAI,CAACpC,cAAc,GAAG,CACpB;QACEoL,MAAM,EAAE,CAAC;QACTpT,KAAK,EAAE;UACLkD,IAAI,EAAE,WAAW;UACjB2L,IAAI,EAAE;YACJG,WAAW,EAAE;WACd;UACD/O,GAAG,EAAE;YAAEC,mBAAmB,EAAE;UAAO,CAAE;UACrCiD,KAAK,EAAE,EAAE;UACTC,SAAS,EAAE;;OAEd,EACD;QACEgQ,MAAM,EAAE,CAAC;QACTpT,KAAK,EAAE;UACLkD,IAAI,EAAE,gBAAgB;UACtB2L,IAAI,EAAE;YACJG,WAAW,EAAE;WACd;UACD/O,GAAG,EAAE;YAAEC,mBAAmB,EAAE;UAAU,CAAE;UACxCiD,KAAK,EAAE,EAAE;UACTC,SAAS,EAAE;;OAEd,CACF;MAED,IAAI,CAACyC,YAAY,GAAG,mBAAmB;MACvC,IAAI,CAAC+D,qBAAqB,EAAE;MAC5B,IAAI,CAACE,cAAc,EAAE;MACrB,IAAI,CAACC,qBAAqB,EAAE;MAC5B,IAAI,CAACC,iBAAiB,EAAE;MAExB,IAAI,IAAI,CAACxC,aAAa,CAAClF,MAAM,KAAK,CAAC,EAAE;QACnC,IAAI,CAAC+E,WAAW,GAAG,IAAI;MACzB;IACF;;uCApzBWlC,0BAA0B,EAAA1F,EAAA,CAAA4T,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA9T,EAAA,CAAA4T,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA/T,EAAA,CAAA4T,iBAAA,CAAAI,EAAA,CAAAC,eAAA,GAAAjU,EAAA,CAAA4T,iBAAA,CAAAM,EAAA,CAAAC,mBAAA,GAAAnU,EAAA,CAAA4T,iBAAA,CAAAM,EAAA,CAAAE,aAAA,GAAApU,EAAA,CAAA4T,iBAAA,CAAAS,EAAA,CAAAC,WAAA;IAAA;;YAA1B5O,0BAA0B;MAAA6O,SAAA;MAAAC,SAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBAY1BpV,sBAAsB;;;;;;;;;;;;UC9DnCU,EAAA,CAAAC,cAAA,aAA0C;;UAIpCD,EAFJ,CAAAC,cAAA,aAAqD,WAC7C,wBAC4D;UAE9DD,EADA,CAAAY,SAAA,cAAyC,cACE;UAGjDZ,EAFI,CAAAG,YAAA,EAAiB,EACZ,EACH;;UAYIH,EAXV,CAAAC,cAAA,aAA2C,aAOxC,aACqD,aACvB,cACA,oBAMtB;UADCD,EAAA,CAAAmB,UAAA,mBAAAyT,+DAAA;YAAA,OAASD,GAAA,CAAApH,YAAA,EAAc;UAAA,EAAC;UACzBvN,EAAA,CAAAG,YAAA,EAAW;UACZH,EAAA,CAAAC,cAAA,oBAKC;UADCD,EAAA,CAAAmB,UAAA,mBAAA0T,+DAAA;YAAA,OAASF,GAAA,CAAArE,eAAA,EAAiB;UAAA,EAAC;UAGjCtQ,EAFK,CAAAG,YAAA,EAAW,EACR,EACF;UAKFH,EAHJ,CAAAC,cAAA,eAAiE,eAElC,uBASc;UAAvCD,EAAA,CAAAmB,UAAA,wBAAA2T,uEAAAlT,MAAA;YAAA,OAAc+S,GAAA,CAAAvC,eAAA,CAAAxQ,MAAA,CAAuB;UAAA,EAAC;UAE1C5B,EADE,CAAAG,YAAA,EAAc,EACV;UAGNH,EAAA,CAAAC,cAAA,eAA4B;UAC1BD,EAAA,CAAAgC,UAAA,KAAA+S,mDAAA,6BAAkE;UAqFpE/U,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAgC,UAAA,KAAAgT,0CAAA,kBAA6E;UAiD3EhV,EADF,CAAAC,cAAA,eAA0B,SACrB;UAAAD,EAAA,CAAAE,MAAA,uDAA+C;UACpDF,EADoD,CAAAG,YAAA,EAAI,EAClD;UAIJH,EADF,CAAAC,cAAA,eAA6B,sBAY1B;UANCD,EAAA,CAAAmB,UAAA,mBAAA8T,iEAAA;YAAA,OAASN,GAAA,CAAA1B,kBAAA,EAAoB;UAAA,EAAC;UAUxCjT,EAJS,CAAAG,YAAA,EAAa,EACV,EACF,EACF,EACF;UAOEH,EAJR,CAAAC,cAAA,eAA0E,eAC5C,eACK,eACV,oBAShB;UAFCD,EAAA,CAAAmB,UAAA,uBAAA+T,mEAAAtT,MAAA;YAAA,OAAa+S,GAAA,CAAA5P,WAAA,CAAAnD,MAAA,CAAmB;UAAA,EAAC;UAGrC5B,EADG,CAAAG,YAAA,EAAW,EACR;UACNH,EAAA,CAAAC,cAAA,eAAwC;UACtCD,EAAA,CAAAY,SAAA,sBAYa;UASjBZ,EADE,CAAAG,YAAA,EAAM,EACF;UAeNH,EAbA,CAAAgC,UAAA,KAAAmT,0CAAA,kBAAmE,KAAAC,0CAAA,kBAaF;UASzEpV,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACF;;;UAnQAH,EAAA,CAAAI,SAAA,GAAwC;UAAxCJ,EAAA,CAAA8B,WAAA,cAAA6S,GAAA,CAAAtM,oBAAA,CAAwC;UAsBXrI,EAAA,CAAAI,SAAA,GAAqC;UAArCJ,EAAA,CAAA8B,WAAA,WAAA6S,GAAA,CAAAtM,oBAAA,CAAqC;UAI1DrI,EAAA,CAAAI,SAAA,GAAsB;UAMtBJ,EANA,CAAAa,UAAA,UAAA8T,GAAA,CAAArM,YAAA,CAAsB,gBAAAqM,GAAA,CAAA1T,kBAAA,CACY,sBACb,kBACJ,yBAGO;UAOMjB,EAAA,CAAAI,SAAA,GAAmB;UAAnBJ,EAAA,CAAAa,UAAA,YAAA8T,GAAA,CAAApM,cAAA,CAAmB;UAwFjBvI,EAAA,CAAAI,SAAA,EAAuC;UAAvCJ,EAAA,CAAAa,UAAA,SAAA8T,GAAA,CAAAnQ,oBAAA,GAAA3B,MAAA,KAAuC;UA0DvE7C,EAAA,CAAAI,SAAA,GAAkC;UAElCJ,EAFA,CAAAa,UAAA,cAAA8T,GAAA,CAAA/B,kBAAA,GAAkC,iBAAA5S,EAAA,CAAAqV,eAAA,KAAAC,GAAA,EAMhC;UAaFtV,EAAA,CAAAI,SAAA,GAAuB;UAIvBJ,EAJA,CAAAa,UAAA,SAAA8T,GAAA,CAAA1O,cAAA,CAAuB,gBAAA0O,GAAA,CAAAvM,WAAA,CACI,4BAGA;UAS3BpI,EAAA,CAAAI,SAAA,GAME;UANFJ,EAAA,CAAAa,UAAA,iBAAAb,EAAA,CAAAqV,eAAA,KAAAE,GAAA,EAME;UAcFvV,EAAA,CAAAI,SAAA,EAAsC;UAAtCJ,EAAA,CAAAa,UAAA,SAAA8T,GAAA,CAAApN,WAAA,sBAAsC;UAatCvH,EAAA,CAAAI,SAAA,EAAoC;UAApCJ,EAAA,CAAAa,UAAA,SAAA8T,GAAA,CAAApN,WAAA,oBAAoC;;;qBDpO9CvI,YAAY,EAAAwW,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZvW,WAAW,EAAAkV,EAAA,CAAAsB,oBAAA,EAAAtB,EAAA,CAAAuB,eAAA,EAAAvB,EAAA,CAAAwB,oBAAA,EACXzW,mBAAmB,EAAAiV,EAAA,CAAAyB,kBAAA,EAAAzB,EAAA,CAAA0B,eAAA,EAEnBxW,sBAAsB,EACtBC,oBAAoB,EACpBI,aAAa,EACbF,eAAe,EACfC,aAAa,EACbF,mBAAmB;MAAAuW,MAAA;IAAA;;SAKVtQ,0BAA0B;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}