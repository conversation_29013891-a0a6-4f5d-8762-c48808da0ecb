{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { PageFooterComponent } from '../../components/page-footer/page-footer.component';\nimport agentsConfigData from './constants/agents.json';\nimport { combineLatest, debounceTime, distinctUntilChanged, startWith, Subject, takeUntil } from 'rxjs';\nimport { AvaTextboxComponent, DropdownComponent, IconComponent, TextCardComponent, PopupComponent, ButtonComponent, DialogService } from '@ava/play-comp-library';\nimport { LucideAngularModule } from 'lucide-angular';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { ConsoleCardComponent } from '../../components/console-card/console-card.component';\nimport { TimeAgoPipe } from '../../pipes/time-ago.pipe';\nlet AgentsComponent = class AgentsComponent {\n  agentService;\n  debouncedSearchService;\n  router;\n  fb;\n  dialogService;\n  defaultActions = [{\n    id: 'duplicate',\n    icon: 'copy',\n    label: 'Duplicate',\n    tooltip: 'Duplicate'\n  }, {\n    id: 'edit',\n    icon: 'edit',\n    label: 'Edit item',\n    tooltip: 'Edit'\n  }, {\n    id: 'delete',\n    icon: 'trash',\n    label: 'Delete item',\n    tooltip: 'Delete'\n  }, {\n    id: 'run',\n    icon: 'play',\n    label: 'Run application',\n    tooltip: 'Run',\n    isPrimary: true\n  }];\n  allAgents = [];\n  displayedAgents = [];\n  totalRecords = [];\n  allAgentsData = [];\n  individualAgentsData = [];\n  collaborativeAgentsData = [];\n  originalAllAgentsData = [];\n  originalIndividualAgentsData = [];\n  originalCollaborativeAgentsData = [];\n  agentsConfig = agentsConfigData;\n  isLoading = false;\n  error = null;\n  currentPage = 1;\n  itemsPerPage = agentsConfigData.pagination.itemsPerPage;\n  totalPages = 1;\n  collaborativeTotalPages = 1;\n  totalNoOfRecords = 0;\n  destroy$ = new Subject();\n  selectedFilterChange = new Subject();\n  agentsOptions = [{\n    name: 'All',\n    value: 'all'\n  }, {\n    name: 'Owned by me',\n    value: 'owned'\n  }, {\n    name: 'Experience',\n    value: 'experience'\n  }, {\n    name: 'Product',\n    value: 'product'\n  }, {\n    name: 'Data',\n    value: 'data'\n  }, {\n    name: 'Finops',\n    value: 'finops'\n  }, {\n    name: 'Quality Engineering',\n    value: 'quality'\n  }, {\n    name: 'Platform',\n    value: 'platform'\n  }];\n  selectedFilter = 'individual';\n  selectedData = null;\n  searchForm;\n  cardSkeletonPlaceholders = Array(11);\n  agentToDelete = null;\n  constructor(agentService, debouncedSearchService, router, fb, dialogService) {\n    this.agentService = agentService;\n    this.debouncedSearchService = debouncedSearchService;\n    this.router = router;\n    this.fb = fb;\n    this.dialogService = dialogService;\n    this.searchForm = this.fb.group({\n      search: ['']\n    });\n  }\n  ngOnInit() {\n    if (this.selectedFilter === 'collaborative') {\n      this.fetchCollaborativeAgents();\n    } else {\n      this.fetchIndividualAgents();\n    }\n    combineLatest([this.searchForm.get('search').valueChanges.pipe(debounceTime(600), distinctUntilChanged(), startWith('')), this.selectedFilterChange.pipe(startWith(this.selectedFilter))]).pipe(takeUntil(this.destroy$)).subscribe(([searchTermRaw, filterType]) => {\n      const searchTerm = searchTermRaw?.trim().toLowerCase();\n      if (filterType === 'individual' || filterType === 'collaborative') {\n        if (!searchTerm) {\n          this.fetchDefaultDataFor(filterType);\n        } else {\n          this.isLoading = true;\n          this.debouncedSearchService.triggerSearch(searchTerm, 'agents', filterType);\n        }\n      }\n    });\n    this.debouncedSearchService.searchResults$.pipe(takeUntil(this.destroy$)).subscribe({\n      next: response => {\n        const results = this.extractAgentArray(response);\n        const formatted = results.map((agent, index) => ({\n          ...agent,\n          id: agent.useCaseId?.toString() || agent.id?.toString() || index.toString(),\n          title: agent.useCaseName || agent.name || 'Unnamed',\n          name: agent.useCaseName || agent.name || 'Unnamed',\n          description: agent.useCaseDetails || agent.description || agent.goal || 'No description',\n          createdDate: this.formatDate(agent.created_at || agent.createdAt || agent.modifiedAt),\n          userCount: agent.users || 0,\n          type: this.selectedFilter\n        }));\n        if (this.selectedFilter === 'individual') {\n          this.individualAgentsData = formatted;\n        } else {\n          this.collaborativeAgentsData = formatted;\n        }\n        this.totalRecords = formatted;\n        this.updateDisplayedAgents();\n        this.isLoading = false;\n      },\n      error: () => {\n        this.error = 'Something went wrong during search.';\n        this.isLoading = false;\n      }\n    });\n  }\n  fetchDefaultDataFor(filter) {\n    switch (filter) {\n      case 'individual':\n        this.fetchIndividualAgents();\n        break;\n      case 'collaborative':\n        this.fetchCollaborativeAgents();\n        break;\n    }\n  }\n  get shouldShowPagination() {\n    if (this.selectedFilter === 'collaborative') {\n      return this.totalNoOfRecords > 0;\n    }\n    return this.filteredAgents.length > 0;\n  }\n  get totalItemsForPagination() {\n    if (this.selectedFilter === 'collaborative') {\n      return this.totalNoOfRecords + 1;\n    }\n    return this.filteredAgents.length + 1;\n  }\n  get filteredAgents() {\n    switch (this.selectedFilter) {\n      case 'individual':\n        return this.individualAgentsData;\n      case 'collaborative':\n        return this.collaborativeAgentsData;\n      case 'all':\n        return this.allAgentsData;\n      default:\n        return this.totalRecords;\n    }\n  }\n  onActionClick(event, agentId) {\n    switch (event.actionId) {\n      case 'delete':\n        this.confirmDeleteAgent(agentId);\n        break;\n      case 'edit':\n        this.editAgent(agentId);\n        break;\n      case 'duplicate':\n        this.duplicateAgent(agentId);\n        break;\n      case 'run':\n        this.executeAgent(agentId);\n        break;\n      default:\n        break;\n    }\n  }\n  onFilterChange(filter) {\n    this.selectedFilter = filter;\n    this.selectedFilterChange.next(filter);\n    this.searchForm.get('search')?.setValue('');\n    this.currentPage = 1;\n    if (filter !== 'collaborative') {\n      this.totalNoOfRecords = 0;\n    }\n    this.selectedFilter === 'individual' ? this.fetchIndividualAgents() : this.fetchCollaborativeAgents();\n  }\n  onSelectionChange(data) {\n    this.selectedData = data;\n  }\n  onPageChange(page) {\n    this.currentPage = page;\n    this.selectedFilter === 'collaborative' ? this.fetchCollaborativeAgents() : this.updateDisplayedAgents();\n  }\n  onCreateAgent() {\n    this.router.navigate([agentsConfigData.navigation.createRoute]);\n  }\n  fetchCollaborativeAgents() {\n    this.isLoading = true;\n    this.agentService.getCollaborativeAgentsPaginated(this.currentPage, this.itemsPerPage).subscribe({\n      next: response => {\n        this.totalNoOfRecords = response?.totalNoOfRecords ?? 0;\n        const agentsList = this.extractAgentArray(response);\n        const mappedAgents = agentsList.map((agent, index) => ({\n          ...agent,\n          id: agent.id?.toString() || `collab-${index}`,\n          title: agent.name || agent.agentDetails || 'Unnamed Agent',\n          name: agent.name || agent.agentDetails || 'Unnamed Agent',\n          description: agent.description || agent.agentDetails || agent.goal || 'No description',\n          createdDate: this.formatDate(agent.modifiedAt || agent.createdAt || new Date()),\n          userCount: agent.users || 0,\n          type: 'collaborative',\n          createdBy: agent.createdBy || '',\n          role: agent.role || '',\n          backstory: agent.backstory || '',\n          expectedOutput: agent.expectedOutput || ''\n        }));\n        this.collaborativeTotalPages = Math.ceil(this.totalNoOfRecords / this.itemsPerPage);\n        this.totalPages = this.collaborativeTotalPages;\n        this.collaborativeAgentsData = mappedAgents;\n        this.displayedAgents = mappedAgents;\n        this.totalRecords = mappedAgents;\n        this.isLoading = false;\n      },\n      error: error => {\n        console.error('Error fetching collaborative agents:', error);\n        this.error = error.message || 'Failed to load collaborative agents';\n        this.isLoading = false;\n      }\n    });\n  }\n  fetchIndividualAgents() {\n    this.isLoading = true;\n    this.agentService.getAllIndividualAgents().subscribe({\n      next: response => {\n        const agentsList = response?.individualAgents ?? response ?? [];\n        const mappedAgents = agentsList.map(agent => ({\n          ...agent,\n          id: agent.useCaseId?.toString() || agent.id?.toString() || '0',\n          title: agent.useCaseName || agent.name || 'Unnamed Agent',\n          name: agent.useCaseName || agent.name || 'Unnamed Agent',\n          description: agent.useCaseDetails || agent.description || 'No description',\n          createdDate: this.formatDate(agent.created_at || agent.createdAt),\n          userCount: agent.users || 0,\n          type: 'individual'\n        }));\n        this.originalIndividualAgentsData = mappedAgents;\n        this.individualAgentsData = mappedAgents;\n        this.totalRecords = mappedAgents;\n        this.updateDisplayedAgents();\n        this.isLoading = false;\n      },\n      error: error => {\n        console.error('Error fetching individual agents:', error);\n        this.error = error.message || 'Failed to load individual agents';\n        this.isLoading = false;\n      }\n    });\n  }\n  formatDate(dateInput) {\n    const date = new Date(dateInput);\n    return `${date.getMonth() + 1}/${date.getDate()}/${date.getFullYear()}`;\n  }\n  extractAgentArray(response) {\n    if (!response) return [];\n    const possibleArrays = [response.agentDetails, response.data, response.agents, response.collaborativeAgents];\n    for (const arr of possibleArrays) {\n      if (Array.isArray(arr)) return arr;\n    }\n    if (Array.isArray(response)) return response;\n    const firstArrayKey = Object.keys(response).find(key => Array.isArray(response[key]));\n    return firstArrayKey ? response[firstArrayKey] : [];\n  }\n  confirmDeleteAgent(agentId) {\n    const dataMap = {\n      all: this.allAgentsData,\n      individual: this.individualAgentsData,\n      collaborative: this.collaborativeAgentsData\n    };\n    const filteredAgents = dataMap[this.selectedFilter] ?? [];\n    const agentObj = filteredAgents.find(agent => agent.id === agentId) || this.allAgents.find(agent => agent.id === agentId);\n    if (!agentObj) return;\n    this.dialogService.confirmation({\n      title: 'Delete Agent?',\n      message: `Are you sure you want to delete ${agentObj.name || 'this agent'}?`,\n      confirmButtonText: 'Delete',\n      confirmButtonVariant: 'danger',\n      onConfirm: () => this.deleteAgent(agentId)\n    });\n  }\n  editAgent(agentId) {\n    const dataMap = {\n      all: this.allAgentsData,\n      individual: this.individualAgentsData,\n      collaborative: this.collaborativeAgentsData\n    };\n    const filteredAgents = dataMap[this.selectedFilter] ?? [];\n    let agent = filteredAgents.find(a => a.id === agentId) || this.allAgents.find(a => a.id === agentId);\n    if (!agent) {\n      return;\n    }\n    let agentType = agent.type;\n    const isCollaborative = this.selectedFilter === 'collaborative' || agent.role || agent.goal || agent.backstory || agent.tools;\n    agentType = agentType ?? (isCollaborative ? 'collaborative' : 'individual');\n    this.router.navigate(['/build/agents', agentType], {\n      queryParams: {\n        id: agentId,\n        mode: 'edit'\n      }\n    });\n  }\n  duplicateAgent(agentId) {\n    const dataMap = {\n      all: this.allAgentsData,\n      individual: this.individualAgentsData,\n      collaborative: this.collaborativeAgentsData\n    };\n    const filteredAgents = dataMap[this.selectedFilter] ?? [];\n    let agent = filteredAgents.find(a => a.id === agentId);\n    if (!agent && this.selectedFilter !== 'all') {\n      agent = this.allAgentsData.find(a => a.id === agentId);\n    }\n    if (!agent) {\n      return;\n    }\n    const isCollaborative = this.selectedFilter === 'collaborative' || agent.role || agent.goal || agent.backstory || agent.tools;\n    const agentType = isCollaborative ? 'collaborative' : 'individual';\n    this.router.navigate(['/build/agents', agentType], {\n      queryParams: {\n        id: agentId,\n        mode: 'duplicate'\n      }\n    });\n  }\n  executeAgent(agentId) {\n    const dataMap = {\n      all: this.allAgentsData,\n      individual: this.individualAgentsData,\n      collaborative: this.collaborativeAgentsData\n    };\n    const agent = dataMap[this.selectedFilter]?.find(a => a.id === agentId) || this.allAgents.find(a => a.id === agentId);\n    if (!agent) {\n      return;\n    }\n    const isCollaborative = this.isCollaborativeAgent(agent);\n    const agentType = isCollaborative ? 'collaborative' : 'individual';\n    this.router.navigate(['/build/agents', agentType, 'execute'], {\n      queryParams: {\n        id: agentId\n      }\n    });\n  }\n  isCollaborativeAgent(agent) {\n    return agent?.type === 'collaborative' || this.selectedFilter === 'collaborative' || agent?.tags?.some(tag => tag.label.toLowerCase() === 'collaborative') || agent?.userType === 'collaborative' || agent?.agentType === 'collaborative' || Boolean(agent?.role || agent?.goal || agent?.backstory || agent?.tools);\n  }\n  deleteAgent(agentId) {\n    const agent = this.getAgentById(agentId);\n    if (!agent) return;\n    const {\n      name: agentName,\n      type: agentType\n    } = agent;\n    const deleteApi$ = agentType === 'collaborative' ? this.agentService.deleteCollaborativeAgent(agentId) : this.agentService.deleteAgent(agentId);\n    deleteApi$.subscribe({\n      next: response => {\n        this.removeAgentFromData(agentId);\n        this.updateDisplayedAgents();\n        // Show success dialog\n        this.dialogService.success({\n          title: 'Success',\n          message: response?.message || `Agent \"${agentName}\" deleted successfully`,\n          confirmText: 'OK'\n        });\n      },\n      error: error => {\n        console.error('Error deleting agent:', error);\n        this.dialogService.error({\n          title: 'Error',\n          message: 'Failed to delete agent. Please try again.',\n          confirmText: 'OK'\n        });\n      }\n    });\n  }\n  updateDisplayedAgents() {\n    const startIndex = (this.currentPage - 1) * 11;\n    const endIndex = startIndex + 11;\n    this.displayedAgents = this.filteredAgents.slice(startIndex, endIndex);\n    this.totalPages = Math.ceil(this.filteredAgents.length / 11);\n  }\n  getAgentById(agentId) {\n    const dataMap = {\n      all: this.allAgentsData,\n      individual: this.individualAgentsData,\n      collaborative: this.collaborativeAgentsData\n    };\n    return dataMap[this.selectedFilter]?.find(a => a.id === agentId) || null;\n  }\n  removeAgentFromData(agentId) {\n    const removeFrom = list => list.filter(agent => agent.id !== agentId);\n    switch (this.selectedFilter) {\n      case 'all':\n        this.allAgentsData = removeFrom(this.allAgentsData);\n        this.originalAllAgentsData = removeFrom(this.originalAllAgentsData);\n        break;\n      case 'individual':\n        this.individualAgentsData = removeFrom(this.individualAgentsData);\n        this.originalIndividualAgentsData = removeFrom(this.originalIndividualAgentsData);\n        break;\n      case 'collaborative':\n        this.collaborativeAgentsData = removeFrom(this.collaborativeAgentsData);\n        this.originalCollaborativeAgentsData = removeFrom(this.originalCollaborativeAgentsData);\n        break;\n    }\n    this.allAgents = removeFrom(this.allAgents);\n    this.totalRecords = removeFrom(this.totalRecords);\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n};\nAgentsComponent = __decorate([Component({\n  selector: 'shared-agents',\n  standalone: true,\n  imports: [CommonModule, PageFooterComponent, TextCardComponent, AvaTextboxComponent, DropdownComponent, LucideAngularModule, IconComponent, ReactiveFormsModule, PopupComponent, ButtonComponent, ConsoleCardComponent, TimeAgoPipe, DialogService],\n  templateUrl: './agents.component.html',\n  styleUrl: './agents.component.scss'\n})], AgentsComponent);\nexport { AgentsComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "PageFooterComponent", "agentsConfigData", "combineLatest", "debounceTime", "distinctUntilChanged", "startWith", "Subject", "takeUntil", "AvaTextboxComponent", "DropdownComponent", "IconComponent", "TextCardComponent", "PopupComponent", "ButtonComponent", "DialogService", "LucideAngularModule", "ReactiveFormsModule", "ConsoleCardComponent", "TimeAgoPipe", "AgentsComponent", "agentService", "debouncedSearchService", "router", "fb", "dialogService", "defaultActions", "id", "icon", "label", "tooltip", "isPrimary", "allAgents", "displayedAgents", "totalRecords", "allAgentsData", "individualAgentsData", "collaborativeAgentsData", "originalAllAgentsData", "originalIndividualAgentsData", "originalCollaborativeAgentsData", "agentsConfig", "isLoading", "error", "currentPage", "itemsPerPage", "pagination", "totalPages", "collaborativeTotalPages", "totalNoOfRecords", "destroy$", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "agentsOptions", "name", "value", "<PERSON><PERSON><PERSON><PERSON>", "selectedData", "searchForm", "cardSkeletonPlaceholders", "Array", "agentToDelete", "constructor", "group", "search", "ngOnInit", "fetchCollaborativeAgents", "fetchIndividualAgents", "get", "valueChanges", "pipe", "subscribe", "searchTermRaw", "filterType", "searchTerm", "trim", "toLowerCase", "fetchDefaultDataFor", "triggerSearch", "searchResults$", "next", "response", "results", "extractAgentArray", "formatted", "map", "agent", "index", "useCaseId", "toString", "title", "useCaseName", "description", "useCaseDetails", "goal", "createdDate", "formatDate", "created_at", "createdAt", "modifiedAt", "userCount", "users", "type", "updateDisplayedAgents", "filter", "shouldShowPagination", "filteredAgents", "length", "totalItemsForPagination", "onActionClick", "event", "agentId", "actionId", "confirmDeleteAgent", "editAgent", "duplicateAgent", "executeAgent", "onFilterChange", "setValue", "onSelectionChange", "data", "onPageChange", "page", "onCreateAgent", "navigate", "navigation", "createRoute", "getCollaborativeAgentsPaginated", "agentsList", "mappedAgents", "agentDetails", "Date", "created<PERSON>y", "role", "backstory", "expectedOutput", "Math", "ceil", "console", "message", "getAllIndividualAgents", "individualAgents", "dateInput", "date", "getMonth", "getDate", "getFullYear", "<PERSON><PERSON><PERSON><PERSON>", "agents", "collaborativeAgents", "arr", "isArray", "firstArrayKey", "Object", "keys", "find", "key", "dataMap", "all", "individual", "collaborative", "agent<PERSON><PERSON><PERSON>", "confirmation", "confirmButtonText", "confirmButtonVariant", "onConfirm", "deleteAgent", "a", "agentType", "isCollaborative", "tools", "queryParams", "mode", "isCollaborativeAgent", "tags", "some", "tag", "userType", "Boolean", "getAgentById", "<PERSON><PERSON><PERSON>", "deleteApi$", "deleteCollaborativeAgent", "removeAgentFromData", "success", "confirmText", "startIndex", "endIndex", "slice", "removeFrom", "list", "ngOnDestroy", "complete", "__decorate", "selector", "standalone", "imports", "templateUrl", "styleUrl"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\pages\\agents\\agents.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Router } from '@angular/router';\r\nimport { PageFooterComponent } from '../../components/page-footer/page-footer.component';\r\nimport { PaginationService } from '../../services/pagination.service';\r\nimport { AgentServiceService } from './services/agent-service.service';\r\nimport agentsConfigData from './constants/agents.json';\r\nimport { combineLatest, debounceTime, distinctUntilChanged, startWith, Subject, takeUntil } from 'rxjs';\r\n\r\nimport {\r\n  AvaTextboxComponent,\r\n  DropdownComponent,\r\n  DropdownOption,\r\n  IconComponent,\r\n  TextCardComponent,\r\n  PopupComponent,\r\n  ButtonComponent,\r\n  DialogService  \r\n} from '@ava/play-comp-library';\r\nimport { LucideAngularModule } from 'lucide-angular';\r\nimport { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';\r\nimport {\r\n  ConsoleCardAction,\r\n  ConsoleCardComponent,\r\n} from '../../components/console-card/console-card.component';\r\nimport { TimeAgoPipe } from '../../pipes/time-ago.pipe';\r\nimport { DebouncedSearchService } from '../../services/debounced-search.service';\r\n\r\n@Component({\r\n  selector: 'shared-agents',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    PageFooterComponent,\r\n    TextCardComponent,\r\n    AvaTextboxComponent,\r\n    DropdownComponent,\r\n    LucideAngularModule,\r\n    IconComponent,\r\n    ReactiveFormsModule,\r\n    PopupComponent,\r\n    ButtonComponent,\r\n    ConsoleCardComponent,\r\n    TimeAgoPipe,\r\n    DialogService  \r\n  ],\r\n  templateUrl: './agents.component.html',\r\n  styleUrl: './agents.component.scss',\r\n})\r\nexport class AgentsComponent implements OnInit {\r\n  defaultActions: ConsoleCardAction[] = [\r\n    {\r\n      id: 'duplicate',\r\n      icon: 'copy',\r\n      label: 'Duplicate',\r\n      tooltip: 'Duplicate',\r\n    },\r\n    {\r\n      id: 'edit',\r\n      icon: 'edit',\r\n      label: 'Edit item',\r\n      tooltip: 'Edit',\r\n    },\r\n    {\r\n      id: 'delete',\r\n      icon: 'trash',\r\n      label: 'Delete item',\r\n      tooltip: 'Delete',\r\n    },\r\n    {\r\n      id: 'run',\r\n      icon: 'play',\r\n      label: 'Run application',\r\n      tooltip: 'Run',\r\n      isPrimary: true,\r\n    },\r\n  ];\r\n  allAgents: any[] = [];\r\n  displayedAgents: any[] = [];\r\n  totalRecords: any[] = [];\r\n  allAgentsData: any[] = [];\r\n  individualAgentsData: any[] = [];\r\n  collaborativeAgentsData: any[] = [];\r\n  originalAllAgentsData: any[] = [];\r\n  originalIndividualAgentsData: any[] = [];\r\n  originalCollaborativeAgentsData: any[] = [];\r\n  agentsConfig = agentsConfigData;\r\n  isLoading: boolean = false;\r\n  error: string | null = null;\r\n  currentPage: number = 1;\r\n  itemsPerPage: number = agentsConfigData.pagination.itemsPerPage;\r\n  totalPages: number = 1;\r\n  collaborativeTotalPages: number = 1;\r\n  totalNoOfRecords: number = 0;\r\n  destroy$ = new Subject<void>();\r\n  selectedFilterChange = new Subject<'all' | 'individual' | 'collaborative'>();\r\n  agentsOptions: DropdownOption[] = [\r\n    { name: 'All', value: 'all' },\r\n    { name: 'Owned by me', value: 'owned' },\r\n    { name: 'Experience', value: 'experience' },\r\n    { name: 'Product', value: 'product' },\r\n    { name: 'Data', value: 'data' },\r\n    { name: 'Finops', value: 'finops' },\r\n    { name: 'Quality Engineering', value: 'quality' },\r\n    { name: 'Platform', value: 'platform' },\r\n  ];\r\n  selectedFilter: 'all' | 'individual' | 'collaborative' = 'individual';\r\n  selectedData: any = null;\r\n  searchForm!: FormGroup;\r\n  cardSkeletonPlaceholders = Array(11);\r\n  agentToDelete: any = null; \r\n  constructor(\r\n    private agentService: AgentServiceService,\r\n    private debouncedSearchService: DebouncedSearchService,\r\n    private router: Router,\r\n    private fb: FormBuilder,\r\n    private dialogService: DialogService \r\n  ) {\r\n    this.searchForm = this.fb.group({\r\n      search: [''],\r\n    });\r\n  }\r\n\r\n    ngOnInit(): void {\r\n    if (this.selectedFilter === 'collaborative') {\r\n      this.fetchCollaborativeAgents();\r\n    } else {\r\n      this.fetchIndividualAgents();\r\n    }\r\n\r\n    combineLatest([\r\n      this.searchForm.get('search')!.valueChanges.pipe(\r\n        debounceTime(600),\r\n        distinctUntilChanged(),\r\n        startWith('')\r\n      ),\r\n      this.selectedFilterChange.pipe(startWith(this.selectedFilter)),\r\n    ])\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe(([searchTermRaw, filterType]) => {\r\n        const searchTerm = searchTermRaw?.trim().toLowerCase();\r\n\r\n        if (filterType === 'individual' || filterType === 'collaborative') {\r\n          if (!searchTerm) {\r\n            this.fetchDefaultDataFor(filterType);\r\n          } else {\r\n            this.isLoading = true;\r\n            this.debouncedSearchService.triggerSearch(\r\n              searchTerm,\r\n              'agents',\r\n              filterType\r\n            );\r\n          }\r\n        }\r\n      });\r\n\r\n    this.debouncedSearchService.searchResults$\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: (response) => {\r\n          const results = this.extractAgentArray(response);\r\n          const formatted = results.map((agent: any, index: number) => ({\r\n            ...agent,\r\n            id: agent.useCaseId?.toString() || agent.id?.toString() || index.toString(),\r\n            title: agent.useCaseName || agent.name || 'Unnamed',\r\n            name: agent.useCaseName || agent.name || 'Unnamed',\r\n            description: agent.useCaseDetails || agent.description || agent.goal || 'No description',\r\n            createdDate: this.formatDate(agent.created_at || agent.createdAt || agent.modifiedAt),\r\n            userCount: agent.users || 0,\r\n            type: this.selectedFilter,\r\n          }));\r\n\r\n          if (this.selectedFilter === 'individual') {\r\n            this.individualAgentsData = formatted;\r\n          } else {\r\n            this.collaborativeAgentsData = formatted;\r\n          }\r\n\r\n          this.totalRecords = formatted;\r\n          this.updateDisplayedAgents();\r\n          this.isLoading = false;\r\n        },\r\n        error: () => {\r\n          this.error = 'Something went wrong during search.';\r\n          this.isLoading = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  fetchDefaultDataFor(filter: string): void {\r\n    switch (filter) {\r\n      case 'individual':\r\n        this.fetchIndividualAgents();\r\n        break;\r\n      case 'collaborative':\r\n        this.fetchCollaborativeAgents();\r\n        break;\r\n    }\r\n  }\r\n\r\n  get shouldShowPagination(): boolean {\r\n    if (this.selectedFilter === 'collaborative') {\r\n      return this.totalNoOfRecords > 0;\r\n    }\r\n    return this.filteredAgents.length > 0;\r\n  }\r\n\r\n  get totalItemsForPagination(): number {\r\n    if (this.selectedFilter === 'collaborative') {\r\n      return this.totalNoOfRecords + 1;\r\n    }\r\n    return this.filteredAgents.length + 1;\r\n  }\r\n\r\n  get filteredAgents(): any[] {\r\n    switch (this.selectedFilter) {\r\n      case 'individual':\r\n        return this.individualAgentsData;\r\n      case 'collaborative':\r\n        return this.collaborativeAgentsData;\r\n      case 'all':\r\n        return this.allAgentsData;\r\n      default:\r\n        return this.totalRecords;\r\n    }\r\n  }\r\n\r\n  onActionClick(\r\n    event: { actionId: string; action: ConsoleCardAction },\r\n    agentId: string,\r\n  ): void {\r\n    switch (event.actionId) {\r\n      case 'delete':\r\n        this.confirmDeleteAgent(agentId);\r\n        break;\r\n      case 'edit':\r\n        this.editAgent(agentId);\r\n        break;\r\n      case 'duplicate':\r\n        this.duplicateAgent(agentId);\r\n        break;\r\n      case 'run':\r\n        this.executeAgent(agentId);\r\n        break;\r\n      default:\r\n        break;\r\n    }\r\n  }\r\n\r\n  onFilterChange(filter: 'all' | 'individual' | 'collaborative'): void {\r\n    this.selectedFilter = filter;\r\n        this.selectedFilterChange.next(filter);\r\n    this.searchForm.get('search')?.setValue('');\r\n\r\n    this.currentPage = 1;\r\n    if (filter !== 'collaborative') {\r\n      this.totalNoOfRecords = 0;\r\n    }\r\n\r\n    this.selectedFilter === 'individual'\r\n      ? this.fetchIndividualAgents()\r\n      : this.fetchCollaborativeAgents();\r\n  }\r\n\r\n  onSelectionChange(data: any) {\r\n    this.selectedData = data;\r\n  }\r\n\r\n  onPageChange(page: number): void {\r\n    this.currentPage = page;\r\n    this.selectedFilter === 'collaborative'\r\n      ? this.fetchCollaborativeAgents()\r\n      : this.updateDisplayedAgents();\r\n  }\r\n\r\n  onCreateAgent(): void {\r\n    this.router.navigate([agentsConfigData.navigation.createRoute]);\r\n  }\r\n\r\n  private fetchCollaborativeAgents(): void {\r\n    this.isLoading = true;\r\n    this.agentService\r\n      .getCollaborativeAgentsPaginated(this.currentPage, this.itemsPerPage)\r\n      .subscribe({\r\n        next: (response) => {\r\n          this.totalNoOfRecords = response?.totalNoOfRecords ?? 0;\r\n          const agentsList = this.extractAgentArray(response);\r\n          const mappedAgents = agentsList.map((agent: any, index: number) => ({\r\n            ...agent,\r\n            id: agent.id?.toString() || `collab-${index}`,\r\n            title: agent.name || agent.agentDetails || 'Unnamed Agent',\r\n            name: agent.name || agent.agentDetails || 'Unnamed Agent',\r\n            description:\r\n              agent.description ||\r\n              agent.agentDetails ||\r\n              agent.goal ||\r\n              'No description',\r\n            createdDate: this.formatDate(\r\n              agent.modifiedAt || agent.createdAt || new Date(),\r\n            ),\r\n            userCount: agent.users || 0,\r\n            type: 'collaborative',\r\n            createdBy: agent.createdBy || '',\r\n            role: agent.role || '',\r\n            backstory: agent.backstory || '',\r\n            expectedOutput: agent.expectedOutput || '',\r\n          }));\r\n          this.collaborativeTotalPages = Math.ceil(\r\n            this.totalNoOfRecords / this.itemsPerPage,\r\n          );\r\n          this.totalPages = this.collaborativeTotalPages;\r\n          this.collaborativeAgentsData = mappedAgents;\r\n          this.displayedAgents = mappedAgents;\r\n          this.totalRecords = mappedAgents;\r\n          this.isLoading = false;\r\n        },\r\n        error: (error) => {\r\n          console.error('Error fetching collaborative agents:', error);\r\n          this.error = error.message || 'Failed to load collaborative agents';\r\n          this.isLoading = false;\r\n        },\r\n      });\r\n  }\r\n\r\n  private fetchIndividualAgents(): void {\r\n    this.isLoading = true;\r\n    this.agentService.getAllIndividualAgents().subscribe({\r\n      next: (response) => {\r\n        const agentsList = response?.individualAgents ?? response ?? [];\r\n        const mappedAgents = agentsList.map((agent: any) => ({\r\n          ...agent,\r\n          id: agent.useCaseId?.toString() || agent.id?.toString() || '0',\r\n          title: agent.useCaseName || agent.name || 'Unnamed Agent',\r\n          name: agent.useCaseName || agent.name || 'Unnamed Agent',\r\n          description:\r\n            agent.useCaseDetails || agent.description || 'No description',\r\n          createdDate: this.formatDate(agent.created_at || agent.createdAt),\r\n          userCount: agent.users || 0,\r\n          type: 'individual',\r\n        }));\r\n        this.originalIndividualAgentsData = mappedAgents;\r\n        this.individualAgentsData = mappedAgents;\r\n        this.totalRecords = mappedAgents;\r\n        this.updateDisplayedAgents();\r\n        this.isLoading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error fetching individual agents:', error);\r\n        this.error = error.message || 'Failed to load individual agents';\r\n        this.isLoading = false;\r\n      },\r\n    });\r\n  }\r\n\r\n  private formatDate(dateInput: string | Date): string {\r\n    const date = new Date(dateInput);\r\n    return `${date.getMonth() + 1}/${date.getDate()}/${date.getFullYear()}`;\r\n  }\r\n\r\n  private extractAgentArray(response: any): any[] {\r\n    if (!response) return [];\r\n    const possibleArrays = [\r\n      response.agentDetails,\r\n      response.data,\r\n      response.agents,\r\n      response.collaborativeAgents,\r\n    ];\r\n    for (const arr of possibleArrays) {\r\n      if (Array.isArray(arr)) return arr;\r\n    }\r\n    if (Array.isArray(response)) return response;\r\n    const firstArrayKey = Object.keys(response).find((key) =>\r\n      Array.isArray(response[key]),\r\n    );\r\n    return firstArrayKey ? response[firstArrayKey] : [];\r\n  }\r\n\r\n  private confirmDeleteAgent(agentId: string): void {\r\n    const dataMap: Record<string, any[]> = {\r\n      all: this.allAgentsData,\r\n      individual: this.individualAgentsData,\r\n      collaborative: this.collaborativeAgentsData,\r\n    };\r\n    const filteredAgents = dataMap[this.selectedFilter] ?? [];\r\n    const agentObj = filteredAgents.find((agent) => agent.id === agentId) ||\r\n                   this.allAgents.find((agent: any) => agent.id === agentId);\r\n    \r\n    if (!agentObj) return;\r\n\r\n    this.dialogService.confirmation({\r\n      title: 'Delete Agent?',\r\n      message: `Are you sure you want to delete ${agentObj.name || 'this agent'}?`,\r\n      confirmButtonText: 'Delete',\r\n      confirmButtonVariant: 'danger',\r\n      onConfirm: () => this.deleteAgent(agentId)\r\n    });\r\n  }\r\n\r\n  private editAgent(agentId: string): void {\r\n    const dataMap: Record<string, any[]> = {\r\n      all: this.allAgentsData,\r\n      individual: this.individualAgentsData,\r\n      collaborative: this.collaborativeAgentsData,\r\n    };\r\n    const filteredAgents = dataMap[this.selectedFilter] ?? [];\r\n    let agent =\r\n      filteredAgents.find((a) => a.id === agentId) ||\r\n      this.allAgents.find((a) => a.id === agentId);\r\n    if (!agent) {\r\n      return;\r\n    }\r\n    let agentType = agent.type;\r\n    const isCollaborative =\r\n      this.selectedFilter === 'collaborative' ||\r\n      agent.role ||\r\n      agent.goal ||\r\n      agent.backstory ||\r\n      agent.tools;\r\n    agentType = agentType ?? (isCollaborative ? 'collaborative' : 'individual');\r\n    this.router.navigate(['/build/agents', agentType], {\r\n      queryParams: {\r\n        id: agentId,\r\n        mode: 'edit',\r\n      },\r\n    });\r\n  }\r\n\r\n  private duplicateAgent(agentId: string): void {\r\n    const dataMap: Record<string, any[]> = {\r\n      all: this.allAgentsData,\r\n      individual: this.individualAgentsData,\r\n      collaborative: this.collaborativeAgentsData,\r\n    };\r\n    const filteredAgents = dataMap[this.selectedFilter] ?? [];\r\n    let agent = filteredAgents.find((a) => a.id === agentId);\r\n    if (!agent && this.selectedFilter !== 'all') {\r\n      agent = this.allAgentsData.find((a) => a.id === agentId);\r\n    }\r\n    if (!agent) {\r\n      return;\r\n    }\r\n    const isCollaborative =\r\n      this.selectedFilter === 'collaborative' ||\r\n      agent.role ||\r\n      agent.goal ||\r\n      agent.backstory ||\r\n      agent.tools;\r\n    const agentType = isCollaborative ? 'collaborative' : 'individual';\r\n    this.router.navigate(['/build/agents', agentType], {\r\n      queryParams: {\r\n        id: agentId,\r\n        mode: 'duplicate',\r\n      },\r\n    });\r\n  }\r\n\r\n  private executeAgent(agentId: string): void {\r\n    const dataMap: Record<string, any[]> = {\r\n      all: this.allAgentsData,\r\n      individual: this.individualAgentsData,\r\n      collaborative: this.collaborativeAgentsData,\r\n    };\r\n    const agent =\r\n      dataMap[this.selectedFilter]?.find((a) => a.id === agentId) ||\r\n      this.allAgents.find((a) => a.id === agentId);\r\n    if (!agent) {\r\n      return;\r\n    }\r\n    const isCollaborative = this.isCollaborativeAgent(agent);\r\n    const agentType = isCollaborative ? 'collaborative' : 'individual';\r\n\r\n    this.router.navigate(['/build/agents', agentType, 'execute'], {\r\n      queryParams: {\r\n        id: agentId,\r\n      },\r\n    });\r\n  }\r\n\r\n  private isCollaborativeAgent(agent: any): boolean {\r\n    return (\r\n      agent?.type === 'collaborative' ||\r\n      this.selectedFilter === 'collaborative' ||\r\n      agent?.tags?.some(\r\n        (tag: { label: string }) => tag.label.toLowerCase() === 'collaborative',\r\n      ) ||\r\n      agent?.userType === 'collaborative' ||\r\n      agent?.agentType === 'collaborative' ||\r\n      Boolean(agent?.role || agent?.goal || agent?.backstory || agent?.tools)\r\n    );\r\n  }\r\n\r\n  private deleteAgent(agentId: string): void {\r\n    const agent = this.getAgentById(agentId);\r\n    if (!agent) return;\r\n\r\n    const { name: agentName, type: agentType } = agent;\r\n    const deleteApi$ = agentType === 'collaborative'\r\n      ? this.agentService.deleteCollaborativeAgent(agentId)\r\n      : this.agentService.deleteAgent(agentId);\r\n\r\n    deleteApi$.subscribe({\r\n      next: (response: any) => {\r\n        this.removeAgentFromData(agentId);\r\n        this.updateDisplayedAgents();\r\n        \r\n        // Show success dialog\r\n        this.dialogService.success({\r\n          title: 'Success',\r\n          message: response?.message || `Agent \"${agentName}\" deleted successfully`,\r\n          confirmText: 'OK'\r\n        });\r\n      },\r\n      error: (error: any) => {\r\n        console.error('Error deleting agent:', error);\r\n        this.dialogService.error({\r\n          title: 'Error',\r\n          message: 'Failed to delete agent. Please try again.',\r\n          confirmText: 'OK'\r\n        });\r\n      },\r\n    });\r\n  }\r\n  private updateDisplayedAgents(): void {\r\n    const startIndex = (this.currentPage - 1) * 11;\r\n    const endIndex = startIndex + 11;\r\n    this.displayedAgents = this.filteredAgents.slice(startIndex, endIndex);\r\n    this.totalPages = Math.ceil(this.filteredAgents.length / 11);\r\n  }\r\n\r\n  private getAgentById(agentId: string): any {\r\n    const dataMap: Record<string, any[]> = {\r\n      all: this.allAgentsData,\r\n      individual: this.individualAgentsData,\r\n      collaborative: this.collaborativeAgentsData,\r\n    };\r\n    return dataMap[this.selectedFilter]?.find((a) => a.id === agentId) || null;\r\n  }\r\n\r\n  private removeAgentFromData(agentId: string): void {\r\n    const removeFrom = (list: any[]) =>\r\n      list.filter((agent) => agent.id !== agentId);\r\n    switch (this.selectedFilter) {\r\n      case 'all':\r\n        this.allAgentsData = removeFrom(this.allAgentsData);\r\n        this.originalAllAgentsData = removeFrom(this.originalAllAgentsData);\r\n        break;\r\n      case 'individual':\r\n        this.individualAgentsData = removeFrom(this.individualAgentsData);\r\n        this.originalIndividualAgentsData = removeFrom(\r\n          this.originalIndividualAgentsData,\r\n        );\r\n        break;\r\n      case 'collaborative':\r\n        this.collaborativeAgentsData = removeFrom(this.collaborativeAgentsData);\r\n        this.originalCollaborativeAgentsData = removeFrom(\r\n          this.originalCollaborativeAgentsData,\r\n        );\r\n        break;\r\n    }\r\n    this.allAgents = removeFrom(this.allAgents);\r\n    this.totalRecords = removeFrom(this.totalRecords);\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n  }\r\n\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,mBAAmB,QAAQ,oDAAoD;AAGxF,OAAOC,gBAAgB,MAAM,yBAAyB;AACtD,SAASC,aAAa,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,SAAS,EAAEC,OAAO,EAAEC,SAAS,QAAQ,MAAM;AAEvG,SACEC,mBAAmB,EACnBC,iBAAiB,EAEjBC,aAAa,EACbC,iBAAiB,EACjBC,cAAc,EACdC,eAAe,EACfC,aAAa,QACR,wBAAwB;AAC/B,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAAiCC,mBAAmB,QAAQ,gBAAgB;AAC5E,SAEEC,oBAAoB,QACf,sDAAsD;AAC7D,SAASC,WAAW,QAAQ,2BAA2B;AAwBhD,IAAMC,eAAe,GAArB,MAAMA,eAAe;EA+DhBC,YAAA;EACAC,sBAAA;EACAC,MAAA;EACAC,EAAA;EACAC,aAAA;EAlEVC,cAAc,GAAwB,CACpC;IACEC,EAAE,EAAE,WAAW;IACfC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,WAAW;IAClBC,OAAO,EAAE;GACV,EACD;IACEH,EAAE,EAAE,MAAM;IACVC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,WAAW;IAClBC,OAAO,EAAE;GACV,EACD;IACEH,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,OAAO;IACbC,KAAK,EAAE,aAAa;IACpBC,OAAO,EAAE;GACV,EACD;IACEH,EAAE,EAAE,KAAK;IACTC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,iBAAiB;IACxBC,OAAO,EAAE,KAAK;IACdC,SAAS,EAAE;GACZ,CACF;EACDC,SAAS,GAAU,EAAE;EACrBC,eAAe,GAAU,EAAE;EAC3BC,YAAY,GAAU,EAAE;EACxBC,aAAa,GAAU,EAAE;EACzBC,oBAAoB,GAAU,EAAE;EAChCC,uBAAuB,GAAU,EAAE;EACnCC,qBAAqB,GAAU,EAAE;EACjCC,4BAA4B,GAAU,EAAE;EACxCC,+BAA+B,GAAU,EAAE;EAC3CC,YAAY,GAAGvC,gBAAgB;EAC/BwC,SAAS,GAAY,KAAK;EAC1BC,KAAK,GAAkB,IAAI;EAC3BC,WAAW,GAAW,CAAC;EACvBC,YAAY,GAAW3C,gBAAgB,CAAC4C,UAAU,CAACD,YAAY;EAC/DE,UAAU,GAAW,CAAC;EACtBC,uBAAuB,GAAW,CAAC;EACnCC,gBAAgB,GAAW,CAAC;EAC5BC,QAAQ,GAAG,IAAI3C,OAAO,EAAQ;EAC9B4C,oBAAoB,GAAG,IAAI5C,OAAO,EAA0C;EAC5E6C,aAAa,GAAqB,CAChC;IAAEC,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAK,CAAE,EAC7B;IAAED,IAAI,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAO,CAAE,EACvC;IAAED,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAY,CAAE,EAC3C;IAAED,IAAI,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAS,CAAE,EACrC;IAAED,IAAI,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAM,CAAE,EAC/B;IAAED,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAQ,CAAE,EACnC;IAAED,IAAI,EAAE,qBAAqB;IAAEC,KAAK,EAAE;EAAS,CAAE,EACjD;IAAED,IAAI,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAU,CAAE,CACxC;EACDC,cAAc,GAA2C,YAAY;EACrEC,YAAY,GAAQ,IAAI;EACxBC,UAAU;EACVC,wBAAwB,GAAGC,KAAK,CAAC,EAAE,CAAC;EACpCC,aAAa,GAAQ,IAAI;EACzBC,YACUxC,YAAiC,EACjCC,sBAA8C,EAC9CC,MAAc,EACdC,EAAe,EACfC,aAA4B;IAJ5B,KAAAJ,YAAY,GAAZA,YAAY;IACZ,KAAAC,sBAAsB,GAAtBA,sBAAsB;IACtB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,aAAa,GAAbA,aAAa;IAErB,IAAI,CAACgC,UAAU,GAAG,IAAI,CAACjC,EAAE,CAACsC,KAAK,CAAC;MAC9BC,MAAM,EAAE,CAAC,EAAE;KACZ,CAAC;EACJ;EAEEC,QAAQA,CAAA;IACR,IAAI,IAAI,CAACT,cAAc,KAAK,eAAe,EAAE;MAC3C,IAAI,CAACU,wBAAwB,EAAE;IACjC,CAAC,MAAM;MACL,IAAI,CAACC,qBAAqB,EAAE;IAC9B;IAEA/D,aAAa,CAAC,CACZ,IAAI,CAACsD,UAAU,CAACU,GAAG,CAAC,QAAQ,CAAE,CAACC,YAAY,CAACC,IAAI,CAC9CjE,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,SAAS,CAAC,EAAE,CAAC,CACd,EACD,IAAI,CAAC6C,oBAAoB,CAACkB,IAAI,CAAC/D,SAAS,CAAC,IAAI,CAACiD,cAAc,CAAC,CAAC,CAC/D,CAAC,CACCc,IAAI,CAAC7D,SAAS,CAAC,IAAI,CAAC0C,QAAQ,CAAC,CAAC,CAC9BoB,SAAS,CAAC,CAAC,CAACC,aAAa,EAAEC,UAAU,CAAC,KAAI;MACzC,MAAMC,UAAU,GAAGF,aAAa,EAAEG,IAAI,EAAE,CAACC,WAAW,EAAE;MAEtD,IAAIH,UAAU,KAAK,YAAY,IAAIA,UAAU,KAAK,eAAe,EAAE;QACjE,IAAI,CAACC,UAAU,EAAE;UACf,IAAI,CAACG,mBAAmB,CAACJ,UAAU,CAAC;QACtC,CAAC,MAAM;UACL,IAAI,CAAC9B,SAAS,GAAG,IAAI;UACrB,IAAI,CAACpB,sBAAsB,CAACuD,aAAa,CACvCJ,UAAU,EACV,QAAQ,EACRD,UAAU,CACX;QACH;MACF;IACF,CAAC,CAAC;IAEJ,IAAI,CAAClD,sBAAsB,CAACwD,cAAc,CACvCT,IAAI,CAAC7D,SAAS,CAAC,IAAI,CAAC0C,QAAQ,CAAC,CAAC,CAC9BoB,SAAS,CAAC;MACTS,IAAI,EAAGC,QAAQ,IAAI;QACjB,MAAMC,OAAO,GAAG,IAAI,CAACC,iBAAiB,CAACF,QAAQ,CAAC;QAChD,MAAMG,SAAS,GAAGF,OAAO,CAACG,GAAG,CAAC,CAACC,KAAU,EAAEC,KAAa,MAAM;UAC5D,GAAGD,KAAK;UACR1D,EAAE,EAAE0D,KAAK,CAACE,SAAS,EAAEC,QAAQ,EAAE,IAAIH,KAAK,CAAC1D,EAAE,EAAE6D,QAAQ,EAAE,IAAIF,KAAK,CAACE,QAAQ,EAAE;UAC3EC,KAAK,EAAEJ,KAAK,CAACK,WAAW,IAAIL,KAAK,CAAChC,IAAI,IAAI,SAAS;UACnDA,IAAI,EAAEgC,KAAK,CAACK,WAAW,IAAIL,KAAK,CAAChC,IAAI,IAAI,SAAS;UAClDsC,WAAW,EAAEN,KAAK,CAACO,cAAc,IAAIP,KAAK,CAACM,WAAW,IAAIN,KAAK,CAACQ,IAAI,IAAI,gBAAgB;UACxFC,WAAW,EAAE,IAAI,CAACC,UAAU,CAACV,KAAK,CAACW,UAAU,IAAIX,KAAK,CAACY,SAAS,IAAIZ,KAAK,CAACa,UAAU,CAAC;UACrFC,SAAS,EAAEd,KAAK,CAACe,KAAK,IAAI,CAAC;UAC3BC,IAAI,EAAE,IAAI,CAAC9C;SACZ,CAAC,CAAC;QAEH,IAAI,IAAI,CAACA,cAAc,KAAK,YAAY,EAAE;UACxC,IAAI,CAACnB,oBAAoB,GAAG+C,SAAS;QACvC,CAAC,MAAM;UACL,IAAI,CAAC9C,uBAAuB,GAAG8C,SAAS;QAC1C;QAEA,IAAI,CAACjD,YAAY,GAAGiD,SAAS;QAC7B,IAAI,CAACmB,qBAAqB,EAAE;QAC5B,IAAI,CAAC5D,SAAS,GAAG,KAAK;MACxB,CAAC;MACDC,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACA,KAAK,GAAG,qCAAqC;QAClD,IAAI,CAACD,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACN;EAEAkC,mBAAmBA,CAAC2B,MAAc;IAChC,QAAQA,MAAM;MACZ,KAAK,YAAY;QACf,IAAI,CAACrC,qBAAqB,EAAE;QAC5B;MACF,KAAK,eAAe;QAClB,IAAI,CAACD,wBAAwB,EAAE;QAC/B;IACJ;EACF;EAEA,IAAIuC,oBAAoBA,CAAA;IACtB,IAAI,IAAI,CAACjD,cAAc,KAAK,eAAe,EAAE;MAC3C,OAAO,IAAI,CAACN,gBAAgB,GAAG,CAAC;IAClC;IACA,OAAO,IAAI,CAACwD,cAAc,CAACC,MAAM,GAAG,CAAC;EACvC;EAEA,IAAIC,uBAAuBA,CAAA;IACzB,IAAI,IAAI,CAACpD,cAAc,KAAK,eAAe,EAAE;MAC3C,OAAO,IAAI,CAACN,gBAAgB,GAAG,CAAC;IAClC;IACA,OAAO,IAAI,CAACwD,cAAc,CAACC,MAAM,GAAG,CAAC;EACvC;EAEA,IAAID,cAAcA,CAAA;IAChB,QAAQ,IAAI,CAAClD,cAAc;MACzB,KAAK,YAAY;QACf,OAAO,IAAI,CAACnB,oBAAoB;MAClC,KAAK,eAAe;QAClB,OAAO,IAAI,CAACC,uBAAuB;MACrC,KAAK,KAAK;QACR,OAAO,IAAI,CAACF,aAAa;MAC3B;QACE,OAAO,IAAI,CAACD,YAAY;IAC5B;EACF;EAEA0E,aAAaA,CACXC,KAAsD,EACtDC,OAAe;IAEf,QAAQD,KAAK,CAACE,QAAQ;MACpB,KAAK,QAAQ;QACX,IAAI,CAACC,kBAAkB,CAACF,OAAO,CAAC;QAChC;MACF,KAAK,MAAM;QACT,IAAI,CAACG,SAAS,CAACH,OAAO,CAAC;QACvB;MACF,KAAK,WAAW;QACd,IAAI,CAACI,cAAc,CAACJ,OAAO,CAAC;QAC5B;MACF,KAAK,KAAK;QACR,IAAI,CAACK,YAAY,CAACL,OAAO,CAAC;QAC1B;MACF;QACE;IACJ;EACF;EAEAM,cAAcA,CAACb,MAA8C;IAC3D,IAAI,CAAChD,cAAc,GAAGgD,MAAM;IACxB,IAAI,CAACpD,oBAAoB,CAAC4B,IAAI,CAACwB,MAAM,CAAC;IAC1C,IAAI,CAAC9C,UAAU,CAACU,GAAG,CAAC,QAAQ,CAAC,EAAEkD,QAAQ,CAAC,EAAE,CAAC;IAE3C,IAAI,CAACzE,WAAW,GAAG,CAAC;IACpB,IAAI2D,MAAM,KAAK,eAAe,EAAE;MAC9B,IAAI,CAACtD,gBAAgB,GAAG,CAAC;IAC3B;IAEA,IAAI,CAACM,cAAc,KAAK,YAAY,GAChC,IAAI,CAACW,qBAAqB,EAAE,GAC5B,IAAI,CAACD,wBAAwB,EAAE;EACrC;EAEAqD,iBAAiBA,CAACC,IAAS;IACzB,IAAI,CAAC/D,YAAY,GAAG+D,IAAI;EAC1B;EAEAC,YAAYA,CAACC,IAAY;IACvB,IAAI,CAAC7E,WAAW,GAAG6E,IAAI;IACvB,IAAI,CAAClE,cAAc,KAAK,eAAe,GACnC,IAAI,CAACU,wBAAwB,EAAE,GAC/B,IAAI,CAACqC,qBAAqB,EAAE;EAClC;EAEAoB,aAAaA,CAAA;IACX,IAAI,CAACnG,MAAM,CAACoG,QAAQ,CAAC,CAACzH,gBAAgB,CAAC0H,UAAU,CAACC,WAAW,CAAC,CAAC;EACjE;EAEQ5D,wBAAwBA,CAAA;IAC9B,IAAI,CAACvB,SAAS,GAAG,IAAI;IACrB,IAAI,CAACrB,YAAY,CACdyG,+BAA+B,CAAC,IAAI,CAAClF,WAAW,EAAE,IAAI,CAACC,YAAY,CAAC,CACpEyB,SAAS,CAAC;MACTS,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAAC/B,gBAAgB,GAAG+B,QAAQ,EAAE/B,gBAAgB,IAAI,CAAC;QACvD,MAAM8E,UAAU,GAAG,IAAI,CAAC7C,iBAAiB,CAACF,QAAQ,CAAC;QACnD,MAAMgD,YAAY,GAAGD,UAAU,CAAC3C,GAAG,CAAC,CAACC,KAAU,EAAEC,KAAa,MAAM;UAClE,GAAGD,KAAK;UACR1D,EAAE,EAAE0D,KAAK,CAAC1D,EAAE,EAAE6D,QAAQ,EAAE,IAAI,UAAUF,KAAK,EAAE;UAC7CG,KAAK,EAAEJ,KAAK,CAAChC,IAAI,IAAIgC,KAAK,CAAC4C,YAAY,IAAI,eAAe;UAC1D5E,IAAI,EAAEgC,KAAK,CAAChC,IAAI,IAAIgC,KAAK,CAAC4C,YAAY,IAAI,eAAe;UACzDtC,WAAW,EACTN,KAAK,CAACM,WAAW,IACjBN,KAAK,CAAC4C,YAAY,IAClB5C,KAAK,CAACQ,IAAI,IACV,gBAAgB;UAClBC,WAAW,EAAE,IAAI,CAACC,UAAU,CAC1BV,KAAK,CAACa,UAAU,IAAIb,KAAK,CAACY,SAAS,IAAI,IAAIiC,IAAI,EAAE,CAClD;UACD/B,SAAS,EAAEd,KAAK,CAACe,KAAK,IAAI,CAAC;UAC3BC,IAAI,EAAE,eAAe;UACrB8B,SAAS,EAAE9C,KAAK,CAAC8C,SAAS,IAAI,EAAE;UAChCC,IAAI,EAAE/C,KAAK,CAAC+C,IAAI,IAAI,EAAE;UACtBC,SAAS,EAAEhD,KAAK,CAACgD,SAAS,IAAI,EAAE;UAChCC,cAAc,EAAEjD,KAAK,CAACiD,cAAc,IAAI;SACzC,CAAC,CAAC;QACH,IAAI,CAACtF,uBAAuB,GAAGuF,IAAI,CAACC,IAAI,CACtC,IAAI,CAACvF,gBAAgB,GAAG,IAAI,CAACJ,YAAY,CAC1C;QACD,IAAI,CAACE,UAAU,GAAG,IAAI,CAACC,uBAAuB;QAC9C,IAAI,CAACX,uBAAuB,GAAG2F,YAAY;QAC3C,IAAI,CAAC/F,eAAe,GAAG+F,YAAY;QACnC,IAAI,CAAC9F,YAAY,GAAG8F,YAAY;QAChC,IAAI,CAACtF,SAAS,GAAG,KAAK;MACxB,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACf8F,OAAO,CAAC9F,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;QAC5D,IAAI,CAACA,KAAK,GAAGA,KAAK,CAAC+F,OAAO,IAAI,qCAAqC;QACnE,IAAI,CAAChG,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACN;EAEQwB,qBAAqBA,CAAA;IAC3B,IAAI,CAACxB,SAAS,GAAG,IAAI;IACrB,IAAI,CAACrB,YAAY,CAACsH,sBAAsB,EAAE,CAACrE,SAAS,CAAC;MACnDS,IAAI,EAAGC,QAAQ,IAAI;QACjB,MAAM+C,UAAU,GAAG/C,QAAQ,EAAE4D,gBAAgB,IAAI5D,QAAQ,IAAI,EAAE;QAC/D,MAAMgD,YAAY,GAAGD,UAAU,CAAC3C,GAAG,CAAEC,KAAU,KAAM;UACnD,GAAGA,KAAK;UACR1D,EAAE,EAAE0D,KAAK,CAACE,SAAS,EAAEC,QAAQ,EAAE,IAAIH,KAAK,CAAC1D,EAAE,EAAE6D,QAAQ,EAAE,IAAI,GAAG;UAC9DC,KAAK,EAAEJ,KAAK,CAACK,WAAW,IAAIL,KAAK,CAAChC,IAAI,IAAI,eAAe;UACzDA,IAAI,EAAEgC,KAAK,CAACK,WAAW,IAAIL,KAAK,CAAChC,IAAI,IAAI,eAAe;UACxDsC,WAAW,EACTN,KAAK,CAACO,cAAc,IAAIP,KAAK,CAACM,WAAW,IAAI,gBAAgB;UAC/DG,WAAW,EAAE,IAAI,CAACC,UAAU,CAACV,KAAK,CAACW,UAAU,IAAIX,KAAK,CAACY,SAAS,CAAC;UACjEE,SAAS,EAAEd,KAAK,CAACe,KAAK,IAAI,CAAC;UAC3BC,IAAI,EAAE;SACP,CAAC,CAAC;QACH,IAAI,CAAC9D,4BAA4B,GAAGyF,YAAY;QAChD,IAAI,CAAC5F,oBAAoB,GAAG4F,YAAY;QACxC,IAAI,CAAC9F,YAAY,GAAG8F,YAAY;QAChC,IAAI,CAAC1B,qBAAqB,EAAE;QAC5B,IAAI,CAAC5D,SAAS,GAAG,KAAK;MACxB,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACf8F,OAAO,CAAC9F,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzD,IAAI,CAACA,KAAK,GAAGA,KAAK,CAAC+F,OAAO,IAAI,kCAAkC;QAChE,IAAI,CAAChG,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEQqD,UAAUA,CAAC8C,SAAwB;IACzC,MAAMC,IAAI,GAAG,IAAIZ,IAAI,CAACW,SAAS,CAAC;IAChC,OAAO,GAAGC,IAAI,CAACC,QAAQ,EAAE,GAAG,CAAC,IAAID,IAAI,CAACE,OAAO,EAAE,IAAIF,IAAI,CAACG,WAAW,EAAE,EAAE;EACzE;EAEQ/D,iBAAiBA,CAACF,QAAa;IACrC,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;IACxB,MAAMkE,cAAc,GAAG,CACrBlE,QAAQ,CAACiD,YAAY,EACrBjD,QAAQ,CAACuC,IAAI,EACbvC,QAAQ,CAACmE,MAAM,EACfnE,QAAQ,CAACoE,mBAAmB,CAC7B;IACD,KAAK,MAAMC,GAAG,IAAIH,cAAc,EAAE;MAChC,IAAIvF,KAAK,CAAC2F,OAAO,CAACD,GAAG,CAAC,EAAE,OAAOA,GAAG;IACpC;IACA,IAAI1F,KAAK,CAAC2F,OAAO,CAACtE,QAAQ,CAAC,EAAE,OAAOA,QAAQ;IAC5C,MAAMuE,aAAa,GAAGC,MAAM,CAACC,IAAI,CAACzE,QAAQ,CAAC,CAAC0E,IAAI,CAAEC,GAAG,IACnDhG,KAAK,CAAC2F,OAAO,CAACtE,QAAQ,CAAC2E,GAAG,CAAC,CAAC,CAC7B;IACD,OAAOJ,aAAa,GAAGvE,QAAQ,CAACuE,aAAa,CAAC,GAAG,EAAE;EACrD;EAEQvC,kBAAkBA,CAACF,OAAe;IACxC,MAAM8C,OAAO,GAA0B;MACrCC,GAAG,EAAE,IAAI,CAAC1H,aAAa;MACvB2H,UAAU,EAAE,IAAI,CAAC1H,oBAAoB;MACrC2H,aAAa,EAAE,IAAI,CAAC1H;KACrB;IACD,MAAMoE,cAAc,GAAGmD,OAAO,CAAC,IAAI,CAACrG,cAAc,CAAC,IAAI,EAAE;IACzD,MAAMyG,QAAQ,GAAGvD,cAAc,CAACiD,IAAI,CAAErE,KAAK,IAAKA,KAAK,CAAC1D,EAAE,KAAKmF,OAAO,CAAC,IACtD,IAAI,CAAC9E,SAAS,CAAC0H,IAAI,CAAErE,KAAU,IAAKA,KAAK,CAAC1D,EAAE,KAAKmF,OAAO,CAAC;IAExE,IAAI,CAACkD,QAAQ,EAAE;IAEf,IAAI,CAACvI,aAAa,CAACwI,YAAY,CAAC;MAC9BxE,KAAK,EAAE,eAAe;MACtBiD,OAAO,EAAE,mCAAmCsB,QAAQ,CAAC3G,IAAI,IAAI,YAAY,GAAG;MAC5E6G,iBAAiB,EAAE,QAAQ;MAC3BC,oBAAoB,EAAE,QAAQ;MAC9BC,SAAS,EAAEA,CAAA,KAAM,IAAI,CAACC,WAAW,CAACvD,OAAO;KAC1C,CAAC;EACJ;EAEQG,SAASA,CAACH,OAAe;IAC/B,MAAM8C,OAAO,GAA0B;MACrCC,GAAG,EAAE,IAAI,CAAC1H,aAAa;MACvB2H,UAAU,EAAE,IAAI,CAAC1H,oBAAoB;MACrC2H,aAAa,EAAE,IAAI,CAAC1H;KACrB;IACD,MAAMoE,cAAc,GAAGmD,OAAO,CAAC,IAAI,CAACrG,cAAc,CAAC,IAAI,EAAE;IACzD,IAAI8B,KAAK,GACPoB,cAAc,CAACiD,IAAI,CAAEY,CAAC,IAAKA,CAAC,CAAC3I,EAAE,KAAKmF,OAAO,CAAC,IAC5C,IAAI,CAAC9E,SAAS,CAAC0H,IAAI,CAAEY,CAAC,IAAKA,CAAC,CAAC3I,EAAE,KAAKmF,OAAO,CAAC;IAC9C,IAAI,CAACzB,KAAK,EAAE;MACV;IACF;IACA,IAAIkF,SAAS,GAAGlF,KAAK,CAACgB,IAAI;IAC1B,MAAMmE,eAAe,GACnB,IAAI,CAACjH,cAAc,KAAK,eAAe,IACvC8B,KAAK,CAAC+C,IAAI,IACV/C,KAAK,CAACQ,IAAI,IACVR,KAAK,CAACgD,SAAS,IACfhD,KAAK,CAACoF,KAAK;IACbF,SAAS,GAAGA,SAAS,KAAKC,eAAe,GAAG,eAAe,GAAG,YAAY,CAAC;IAC3E,IAAI,CAACjJ,MAAM,CAACoG,QAAQ,CAAC,CAAC,eAAe,EAAE4C,SAAS,CAAC,EAAE;MACjDG,WAAW,EAAE;QACX/I,EAAE,EAAEmF,OAAO;QACX6D,IAAI,EAAE;;KAET,CAAC;EACJ;EAEQzD,cAAcA,CAACJ,OAAe;IACpC,MAAM8C,OAAO,GAA0B;MACrCC,GAAG,EAAE,IAAI,CAAC1H,aAAa;MACvB2H,UAAU,EAAE,IAAI,CAAC1H,oBAAoB;MACrC2H,aAAa,EAAE,IAAI,CAAC1H;KACrB;IACD,MAAMoE,cAAc,GAAGmD,OAAO,CAAC,IAAI,CAACrG,cAAc,CAAC,IAAI,EAAE;IACzD,IAAI8B,KAAK,GAAGoB,cAAc,CAACiD,IAAI,CAAEY,CAAC,IAAKA,CAAC,CAAC3I,EAAE,KAAKmF,OAAO,CAAC;IACxD,IAAI,CAACzB,KAAK,IAAI,IAAI,CAAC9B,cAAc,KAAK,KAAK,EAAE;MAC3C8B,KAAK,GAAG,IAAI,CAAClD,aAAa,CAACuH,IAAI,CAAEY,CAAC,IAAKA,CAAC,CAAC3I,EAAE,KAAKmF,OAAO,CAAC;IAC1D;IACA,IAAI,CAACzB,KAAK,EAAE;MACV;IACF;IACA,MAAMmF,eAAe,GACnB,IAAI,CAACjH,cAAc,KAAK,eAAe,IACvC8B,KAAK,CAAC+C,IAAI,IACV/C,KAAK,CAACQ,IAAI,IACVR,KAAK,CAACgD,SAAS,IACfhD,KAAK,CAACoF,KAAK;IACb,MAAMF,SAAS,GAAGC,eAAe,GAAG,eAAe,GAAG,YAAY;IAClE,IAAI,CAACjJ,MAAM,CAACoG,QAAQ,CAAC,CAAC,eAAe,EAAE4C,SAAS,CAAC,EAAE;MACjDG,WAAW,EAAE;QACX/I,EAAE,EAAEmF,OAAO;QACX6D,IAAI,EAAE;;KAET,CAAC;EACJ;EAEQxD,YAAYA,CAACL,OAAe;IAClC,MAAM8C,OAAO,GAA0B;MACrCC,GAAG,EAAE,IAAI,CAAC1H,aAAa;MACvB2H,UAAU,EAAE,IAAI,CAAC1H,oBAAoB;MACrC2H,aAAa,EAAE,IAAI,CAAC1H;KACrB;IACD,MAAMgD,KAAK,GACTuE,OAAO,CAAC,IAAI,CAACrG,cAAc,CAAC,EAAEmG,IAAI,CAAEY,CAAC,IAAKA,CAAC,CAAC3I,EAAE,KAAKmF,OAAO,CAAC,IAC3D,IAAI,CAAC9E,SAAS,CAAC0H,IAAI,CAAEY,CAAC,IAAKA,CAAC,CAAC3I,EAAE,KAAKmF,OAAO,CAAC;IAC9C,IAAI,CAACzB,KAAK,EAAE;MACV;IACF;IACA,MAAMmF,eAAe,GAAG,IAAI,CAACI,oBAAoB,CAACvF,KAAK,CAAC;IACxD,MAAMkF,SAAS,GAAGC,eAAe,GAAG,eAAe,GAAG,YAAY;IAElE,IAAI,CAACjJ,MAAM,CAACoG,QAAQ,CAAC,CAAC,eAAe,EAAE4C,SAAS,EAAE,SAAS,CAAC,EAAE;MAC5DG,WAAW,EAAE;QACX/I,EAAE,EAAEmF;;KAEP,CAAC;EACJ;EAEQ8D,oBAAoBA,CAACvF,KAAU;IACrC,OACEA,KAAK,EAAEgB,IAAI,KAAK,eAAe,IAC/B,IAAI,CAAC9C,cAAc,KAAK,eAAe,IACvC8B,KAAK,EAAEwF,IAAI,EAAEC,IAAI,CACdC,GAAsB,IAAKA,GAAG,CAAClJ,KAAK,CAAC8C,WAAW,EAAE,KAAK,eAAe,CACxE,IACDU,KAAK,EAAE2F,QAAQ,KAAK,eAAe,IACnC3F,KAAK,EAAEkF,SAAS,KAAK,eAAe,IACpCU,OAAO,CAAC5F,KAAK,EAAE+C,IAAI,IAAI/C,KAAK,EAAEQ,IAAI,IAAIR,KAAK,EAAEgD,SAAS,IAAIhD,KAAK,EAAEoF,KAAK,CAAC;EAE3E;EAEQJ,WAAWA,CAACvD,OAAe;IACjC,MAAMzB,KAAK,GAAG,IAAI,CAAC6F,YAAY,CAACpE,OAAO,CAAC;IACxC,IAAI,CAACzB,KAAK,EAAE;IAEZ,MAAM;MAAEhC,IAAI,EAAE8H,SAAS;MAAE9E,IAAI,EAAEkE;IAAS,CAAE,GAAGlF,KAAK;IAClD,MAAM+F,UAAU,GAAGb,SAAS,KAAK,eAAe,GAC5C,IAAI,CAAClJ,YAAY,CAACgK,wBAAwB,CAACvE,OAAO,CAAC,GACnD,IAAI,CAACzF,YAAY,CAACgJ,WAAW,CAACvD,OAAO,CAAC;IAE1CsE,UAAU,CAAC9G,SAAS,CAAC;MACnBS,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAACsG,mBAAmB,CAACxE,OAAO,CAAC;QACjC,IAAI,CAACR,qBAAqB,EAAE;QAE5B;QACA,IAAI,CAAC7E,aAAa,CAAC8J,OAAO,CAAC;UACzB9F,KAAK,EAAE,SAAS;UAChBiD,OAAO,EAAE1D,QAAQ,EAAE0D,OAAO,IAAI,UAAUyC,SAAS,wBAAwB;UACzEK,WAAW,EAAE;SACd,CAAC;MACJ,CAAC;MACD7I,KAAK,EAAGA,KAAU,IAAI;QACpB8F,OAAO,CAAC9F,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,IAAI,CAAClB,aAAa,CAACkB,KAAK,CAAC;UACvB8C,KAAK,EAAE,OAAO;UACdiD,OAAO,EAAE,2CAA2C;UACpD8C,WAAW,EAAE;SACd,CAAC;MACJ;KACD,CAAC;EACJ;EACQlF,qBAAqBA,CAAA;IAC3B,MAAMmF,UAAU,GAAG,CAAC,IAAI,CAAC7I,WAAW,GAAG,CAAC,IAAI,EAAE;IAC9C,MAAM8I,QAAQ,GAAGD,UAAU,GAAG,EAAE;IAChC,IAAI,CAACxJ,eAAe,GAAG,IAAI,CAACwE,cAAc,CAACkF,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;IACtE,IAAI,CAAC3I,UAAU,GAAGwF,IAAI,CAACC,IAAI,CAAC,IAAI,CAAC/B,cAAc,CAACC,MAAM,GAAG,EAAE,CAAC;EAC9D;EAEQwE,YAAYA,CAACpE,OAAe;IAClC,MAAM8C,OAAO,GAA0B;MACrCC,GAAG,EAAE,IAAI,CAAC1H,aAAa;MACvB2H,UAAU,EAAE,IAAI,CAAC1H,oBAAoB;MACrC2H,aAAa,EAAE,IAAI,CAAC1H;KACrB;IACD,OAAOuH,OAAO,CAAC,IAAI,CAACrG,cAAc,CAAC,EAAEmG,IAAI,CAAEY,CAAC,IAAKA,CAAC,CAAC3I,EAAE,KAAKmF,OAAO,CAAC,IAAI,IAAI;EAC5E;EAEQwE,mBAAmBA,CAACxE,OAAe;IACzC,MAAM8E,UAAU,GAAIC,IAAW,IAC7BA,IAAI,CAACtF,MAAM,CAAElB,KAAK,IAAKA,KAAK,CAAC1D,EAAE,KAAKmF,OAAO,CAAC;IAC9C,QAAQ,IAAI,CAACvD,cAAc;MACzB,KAAK,KAAK;QACR,IAAI,CAACpB,aAAa,GAAGyJ,UAAU,CAAC,IAAI,CAACzJ,aAAa,CAAC;QACnD,IAAI,CAACG,qBAAqB,GAAGsJ,UAAU,CAAC,IAAI,CAACtJ,qBAAqB,CAAC;QACnE;MACF,KAAK,YAAY;QACf,IAAI,CAACF,oBAAoB,GAAGwJ,UAAU,CAAC,IAAI,CAACxJ,oBAAoB,CAAC;QACjE,IAAI,CAACG,4BAA4B,GAAGqJ,UAAU,CAC5C,IAAI,CAACrJ,4BAA4B,CAClC;QACD;MACF,KAAK,eAAe;QAClB,IAAI,CAACF,uBAAuB,GAAGuJ,UAAU,CAAC,IAAI,CAACvJ,uBAAuB,CAAC;QACvE,IAAI,CAACG,+BAA+B,GAAGoJ,UAAU,CAC/C,IAAI,CAACpJ,+BAA+B,CACrC;QACD;IACJ;IACA,IAAI,CAACR,SAAS,GAAG4J,UAAU,CAAC,IAAI,CAAC5J,SAAS,CAAC;IAC3C,IAAI,CAACE,YAAY,GAAG0J,UAAU,CAAC,IAAI,CAAC1J,YAAY,CAAC;EACnD;EAEA4J,WAAWA,CAAA;IACT,IAAI,CAAC5I,QAAQ,CAAC6B,IAAI,EAAE;IACpB,IAAI,CAAC7B,QAAQ,CAAC6I,QAAQ,EAAE;EAC1B;CAED;AAvgBY3K,eAAe,GAAA4K,UAAA,EArB3BjM,SAAS,CAAC;EACTkM,QAAQ,EAAE,eAAe;EACzBC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPnM,YAAY,EACZC,mBAAmB,EACnBW,iBAAiB,EACjBH,mBAAmB,EACnBC,iBAAiB,EACjBM,mBAAmB,EACnBL,aAAa,EACbM,mBAAmB,EACnBJ,cAAc,EACdC,eAAe,EACfI,oBAAoB,EACpBC,WAAW,EACXJ,aAAa,CACd;EACDqL,WAAW,EAAE,yBAAyB;EACtCC,QAAQ,EAAE;CACX,CAAC,C,EACWjL,eAAe,CAugB3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}