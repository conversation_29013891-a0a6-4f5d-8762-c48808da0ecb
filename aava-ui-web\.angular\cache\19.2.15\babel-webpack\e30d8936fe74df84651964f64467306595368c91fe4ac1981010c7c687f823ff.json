{"ast": null, "code": "import { ViewContainerRef } from '@angular/core';\nimport { RouterOutlet } from '@angular/router';\nimport { CommonModule } from '@angular/common';\nimport { NavHeaderComponent } from './shared/components/nav-header/nav-header.component';\nimport { LoaderComponent } from './shared/components/loader/loader.component';\nimport { environment } from '../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@shared/auth/services/auth-token.service\";\nimport * as i2 from \"@shared/auth/services/token-storage.service\";\nimport * as i3 from \"./shared/services/drawer/drawer.service\";\nimport * as i4 from \"./shared/services/theme/theme.service\";\nimport * as i5 from \"@shared/auth/services/auth.service\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"./shared/services/loader/loader.service\";\nimport * as i8 from \"@angular/common\";\nconst _c0 = [\"dialogHost\"];\nfunction AppComponent_app_nav_header_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-nav-header\", 6);\n  }\n}\nexport let AppComponent = /*#__PURE__*/(() => {\n  class AppComponent {\n    authTokenService;\n    tokenStorage;\n    drawerService;\n    themeService;\n    authService;\n    router;\n    loaderService;\n    dialogHost;\n    showHeaderAndNav = true;\n    // Retractable header properties\n    // isHeaderVisible: boolean = true;\n    // private headerTimeout: any;\n    // private readonly HEADER_SHOW_DURATION = 1000; // 1 second delay before hiding\n    // private readonly HEADER_ACTIVATION_ZONE = 100; // Top 100px area to activate header\n    // private isMouseOnHeader: boolean = false;\n    constructor(authTokenService, tokenStorage, drawerService, themeService, authService, router, loaderService) {\n      this.authTokenService = authTokenService;\n      this.tokenStorage = tokenStorage;\n      this.drawerService = drawerService;\n      this.themeService = themeService;\n      this.authService = authService;\n      this.router = router;\n      this.loaderService = loaderService;\n    }\n    // Mouse event handlers for retractable header\n    // @HostListener('mousemove', ['$event'])\n    // onMouseMove(event: MouseEvent) {\n    //   if (this.showHeaderAndNav) {\n    //     const mouseY = event.clientY;\n    //     // Check if mouse is in the top activation zone\n    //     if (mouseY <= this.HEADER_ACTIVATION_ZONE) {\n    //       this.showHeader();\n    //       return;\n    //     }\n    //     // Check if mouse is on the header area (when header is visible)\n    //     if (this.isHeaderVisible) {\n    //       // Get header element to check if mouse is on it\n    //       const headerElement = document.querySelector(\n    //         '.retractable-header',\n    //       ) as HTMLElement;\n    //       const breadcrumbElement = document.querySelector(\n    //         '.retractable-breadcrumb',\n    //       ) as HTMLElement;\n    //       if (headerElement || breadcrumbElement) {\n    //         const headerRect = headerElement?.getBoundingClientRect();\n    //         const breadcrumbRect = breadcrumbElement?.getBoundingClientRect();\n    //         // Check if mouse is on header or breadcrumb\n    //         const isOnHeader =\n    //           headerRect &&\n    //           mouseY >= headerRect.top &&\n    //           mouseY <= headerRect.bottom &&\n    //           event.clientX >= headerRect.left &&\n    //           event.clientX <= headerRect.right;\n    //         const isOnBreadcrumb =\n    //           breadcrumbRect &&\n    //           mouseY >= breadcrumbRect.top &&\n    //           mouseY <= breadcrumbRect.bottom &&\n    //           event.clientX >= breadcrumbRect.left &&\n    //           event.clientX <= breadcrumbRect.right;\n    //         if (isOnHeader || isOnBreadcrumb) {\n    //           // Mouse is on header - keep it visible and clear any hide timeout\n    //           this.isMouseOnHeader = true;\n    //           clearTimeout(this.headerTimeout);\n    //           return;\n    //         } else {\n    //           // Mouse left header area - start hide timer\n    //           this.isMouseOnHeader = false;\n    //           this.startHideTimer();\n    //         }\n    //       }\n    //     }\n    //   }\n    // }\n    // @HostListener('mouseleave', ['$event'])\n    // onMouseLeave(event: MouseEvent) {\n    //   if (this.showHeaderAndNav && !this.isMouseOnHeader) {\n    //     this.startHideTimer();\n    //   }\n    // }\n    // private showHeader() {\n    //   clearTimeout(this.headerTimeout);\n    //   this.isHeaderVisible = true;\n    //   this.isMouseOnHeader = false; // Reset mouse on header state\n    // }\n    // private startHideTimer() {\n    //   clearTimeout(this.headerTimeout);\n    //   this.headerTimeout = setTimeout(() => {\n    //     if (!this.isMouseOnHeader) {\n    //       this.isHeaderVisible = false;\n    //     }\n    //   }, this.HEADER_SHOW_DURATION);\n    // }\n    ngAfterViewInit() {\n      this.drawerService.registerViewContainer(this.dialogHost);\n    }\n    ngOnInit() {\n      const savedTheme = this.themeService.getCurrentTheme();\n      this.themeService.setTheme(savedTheme);\n      const authConfig = {\n        apiAuthUrl: environment.consoleApiAuthUrl,\n        redirectUrl: environment.consoleRedirectUrl,\n        postLoginRedirectUrl: '/dashboard',\n        appName: 'console'\n      };\n      this.authService.setAuthConfig(authConfig);\n      this.authTokenService.handleAuthCodeAndToken();\n      this.authTokenService.startTokenCheck();\n      if (!this.tokenStorage.getCookie('org_path')) {\n        this.tokenStorage.setCookie('org_path', 'ascendion@core@genai@aava::201@202@203@204');\n      }\n      this.router.events.subscribe(event => {\n        if (this.router.url === '/login') {\n          this.showHeaderAndNav = false;\n          //this.isHeaderVisible = false; // Ensure header is hidden on login page\n        } else {\n          this.showHeaderAndNav = true;\n          //this.isHeaderVisible = true; // Show header on other pages\n        }\n        // Reset loader on navigation start\n        if (event.constructor.name === 'NavigationStart') {\n          this.loaderService.resetOnNavigation();\n        }\n      });\n    }\n    ngOnDestroy() {\n      this.authTokenService.stopTokenCheck();\n      // Clean up header timeout\n      // if (this.headerTimeout) {\n      //   clearTimeout(this.headerTimeout);\n      // }\n    }\n    static ɵfac = function AppComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AppComponent)(i0.ɵɵdirectiveInject(i1.AuthTokenService), i0.ɵɵdirectiveInject(i2.TokenStorageService), i0.ɵɵdirectiveInject(i3.DrawerService), i0.ɵɵdirectiveInject(i4.ThemeService), i0.ɵɵdirectiveInject(i5.AuthService), i0.ɵɵdirectiveInject(i6.Router), i0.ɵɵdirectiveInject(i7.LoaderService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      viewQuery: function AppComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5, ViewContainerRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dialogHost = _t.first);\n        }\n      },\n      decls: 9,\n      vars: 5,\n      consts: [[\"dialogHost\", \"\"], [\"id\", \"app-container\"], [1, \"panel\"], [1, \"glass-effect\"], [\"class\", \"retractable-header\", 4, \"ngIf\"], [1, \"content-wrapper\"], [1, \"retractable-header\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2);\n          i0.ɵɵelement(2, \"div\", null, 0);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"div\", 3);\n          i0.ɵɵtemplate(5, AppComponent_app_nav_header_5_Template, 1, 0, \"app-nav-header\", 4);\n          i0.ɵɵelementStart(6, \"div\", 5);\n          i0.ɵɵelement(7, \"app-loader\")(8, \"router-outlet\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"login-active\", true);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.showHeaderAndNav);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"post-login\", ctx.showHeaderAndNav);\n        }\n      },\n      dependencies: [RouterOutlet, NavHeaderComponent, LoaderComponent, CommonModule, i8.NgIf],\n      styles: [\"#app-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100vh;\\n  padding: 0;\\n  overflow: hidden;\\n  display: flex;\\n  box-sizing: border-box;\\n  flex-direction: column;\\n  position: relative;\\n}\\n#app-container[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 100px;\\n  pointer-events: none;\\n  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.02), transparent);\\n  opacity: 0;\\n  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n#app-container[_ngcontent-%COMP%]:hover::before {\\n  opacity: 1;\\n}\\n\\n.app-container.login-active[_ngcontent-%COMP%] {\\n  padding: 0 !important;\\n}\\n\\n  app-nav-header {\\n  position: relative !important;\\n}\\n\\n.retractable-header[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 0px 0px 0px 0px;\\n}\\n.retractable-header.hidden[_ngcontent-%COMP%] {\\n  max-height: 0;\\n  opacity: 0;\\n  transform: translateY(-10px);\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.02), 0 1px 3px rgba(0, 0, 0, 0.01);\\n  margin-bottom: 0;\\n}\\n\\n.retractable-breadcrumb[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 0 2rem;\\n}\\n.retractable-breadcrumb.hidden[_ngcontent-%COMP%] {\\n  max-height: 0;\\n  opacity: 0;\\n  transform: translateY(-5px);\\n  margin-bottom: 0;\\n}\\n\\n.post-login[_ngcontent-%COMP%] {\\n  padding: 0 1rem;\\n}\\n\\n.content-wrapper[_ngcontent-%COMP%] {\\n  width: 100%;\\n  position: relative;\\n  display: flex;\\n  flex-direction: column;\\n  flex: 1;\\n  overflow: auto;\\n  scrollbar-width: none;\\n  -ms-overflow-style: none;\\n  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\n  transform: translateY(0);\\n}\\n\\n.content-wrapper[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none;\\n}\\n\\n  router-outlet + * {\\n  position: relative;\\n}\\n\\nfooter[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  text-align: center;\\n  bottom: 0;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3Byb2plY3RzL2NvbnNvbGUvc3JjL2FwcC9hcHAuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxXQUFBO0VBQ0EsYUFBQTtFQUNBLFVBQUE7RUFDQSxnQkFBQTtFQUNBLGFBQUE7RUFDQSxzQkFBQTtFQUNBLHNCQUFBO0VBQ0Esa0JBQUE7QUFDRjtBQUFFO0VBQ0UsV0FBQTtFQUNBLGVBQUE7RUFDQSxNQUFBO0VBQ0EsT0FBQTtFQUNBLFFBQUE7RUFDQSxhQUFBO0VBQ0Esb0JBQUE7RUFDQSx3RUFBQTtFQUNBLFVBQUE7RUFDQSxxREFBQTtBQUVKO0FBQ0U7RUFDRSxVQUFBO0FBQ0o7O0FBR0E7RUFDRSxxQkFBQTtBQUFGOztBQUdBO0VBQ0UsNkJBQUE7QUFBRjs7QUFHQTtFQUNFLFdBQUE7RUFDQSx3QkFBQTtBQUFGO0FBQ0U7RUFDRSxhQUFBO0VBQ0EsVUFBQTtFQUNBLDRCQUFBO0VBQ0EseUVBQ0U7RUFFRixnQkFBQTtBQURKOztBQUtBO0VBQ0UsV0FBQTtFQUNBLGVBQUE7QUFGRjtBQUdFO0VBQ0UsYUFBQTtFQUNBLFVBQUE7RUFDQSwyQkFBQTtFQUNBLGdCQUFBO0FBREo7O0FBS0E7RUFDRSxlQUFBO0FBRkY7O0FBTUE7RUFDRSxXQUFBO0VBQ0Esa0JBQUE7RUFDQSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxPQUFBO0VBQ0EsY0FBQTtFQUNBLHFCQUFBO0VBQ0Esd0JBQUE7RUFDQSx5REFBQTtFQUVBLHdCQUFBO0FBSkY7O0FBT0E7RUFDRSxhQUFBO0FBSkY7O0FBT0E7RUFDRSxrQkFBQTtBQUpGOztBQU9BO0VBQ0UsYUFBQTtFQUNBLGtCQUFBO0VBQ0EsU0FBQTtBQUpGIiwic291cmNlc0NvbnRlbnQiOlsiI2FwcC1jb250YWluZXIge1xyXG4gIHdpZHRoOiAxMDAlO1xyXG4gIGhlaWdodDogMTAwdmg7XHJcbiAgcGFkZGluZzogMDtcclxuICBvdmVyZmxvdzogaGlkZGVuO1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgYm94LXNpemluZzogYm9yZGVyLWJveDtcclxuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuICAmOjpiZWZvcmUge1xyXG4gICAgY29udGVudDogXCJcIjtcclxuICAgIHBvc2l0aW9uOiBmaXhlZDtcclxuICAgIHRvcDogMDtcclxuICAgIGxlZnQ6IDA7XHJcbiAgICByaWdodDogMDtcclxuICAgIGhlaWdodDogMTAwcHg7XHJcbiAgICBwb2ludGVyLWV2ZW50czogbm9uZTtcclxuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCh0byBib3R0b20sIHJnYmEoMCwgMCwgMCwgMC4wMiksIHRyYW5zcGFyZW50KTtcclxuICAgIG9wYWNpdHk6IDA7XHJcbiAgICB0cmFuc2l0aW9uOiBvcGFjaXR5IDAuM3MgY3ViaWMtYmV6aWVyKDAuNCwgMCwgMC4yLCAxKTtcclxuICB9XHJcblxyXG4gICY6aG92ZXI6OmJlZm9yZSB7XHJcbiAgICBvcGFjaXR5OiAxO1xyXG4gIH1cclxufVxyXG5cclxuLmFwcC1jb250YWluZXIubG9naW4tYWN0aXZlIHtcclxuICBwYWRkaW5nOiAwICFpbXBvcnRhbnQ7XHJcbn1cclxuXHJcbjo6bmctZGVlcCBhcHAtbmF2LWhlYWRlciB7XHJcbiAgcG9zaXRpb246IHJlbGF0aXZlICFpbXBvcnRhbnQ7XHJcbn1cclxuXHJcbi5yZXRyYWN0YWJsZS1oZWFkZXIge1xyXG4gIHdpZHRoOiAxMDAlO1xyXG4gIHBhZGRpbmc6IDBweCAwcHggMHB4IDBweDtcclxuICAmLmhpZGRlbiB7XHJcbiAgICBtYXgtaGVpZ2h0OiAwO1xyXG4gICAgb3BhY2l0eTogMDtcclxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMTBweCk7XHJcbiAgICBib3gtc2hhZG93OlxyXG4gICAgICAwIDRweCAyMHB4IHJnYmEoMCwgMCwgMCwgMC4wMiksXHJcbiAgICAgIDAgMXB4IDNweCByZ2JhKDAsIDAsIDAsIDAuMDEpO1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMDtcclxuICB9XHJcbn1cclxuXHJcbi5yZXRyYWN0YWJsZS1icmVhZGNydW1iIHtcclxuICB3aWR0aDogMTAwJTtcclxuICBwYWRkaW5nOiAwIDJyZW07XHJcbiAgJi5oaWRkZW4ge1xyXG4gICAgbWF4LWhlaWdodDogMDtcclxuICAgIG9wYWNpdHk6IDA7XHJcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTVweCk7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAwO1xyXG4gIH1cclxufVxyXG5cclxuLnBvc3QtbG9naW4ge1xyXG4gIHBhZGRpbmc6IDAgMXJlbTtcclxuICAvL21hcmdpbi10b3A6IDFyZW07XHJcbn1cclxuXHJcbi5jb250ZW50LXdyYXBwZXIge1xyXG4gIHdpZHRoOiAxMDAlO1xyXG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgZmxleDogMTtcclxuICBvdmVyZmxvdzogYXV0bztcclxuICBzY3JvbGxiYXItd2lkdGg6IG5vbmU7XHJcbiAgLW1zLW92ZXJmbG93LXN0eWxlOiBub25lO1xyXG4gIHRyYW5zaXRpb246IGFsbCAwLjVzIGN1YmljLWJlemllcigwLjI1LCAwLjQ2LCAwLjQ1LCAwLjk0KTtcclxuXHJcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApO1xyXG59XHJcblxyXG4uY29udGVudC13cmFwcGVyOjotd2Via2l0LXNjcm9sbGJhciB7XHJcbiAgZGlzcGxheTogbm9uZTtcclxufVxyXG5cclxuOjpuZy1kZWVwIHJvdXRlci1vdXRsZXQgKyAqIHtcclxuICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcbn1cclxuXHJcbmZvb3RlciB7XHJcbiAgcGFkZGluZzoxcmVtO1xyXG4gIHRleHQtYWxpZ246Y2VudGVyO1xyXG4gIGJvdHRvbTowO1xyXG59XHJcbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n  return AppComponent;\n})();", "map": {"version": 3, "names": ["ViewContainerRef", "RouterOutlet", "CommonModule", "NavHeaderComponent", "LoaderComponent", "environment", "i0", "ɵɵelement", "AppComponent", "authTokenService", "tokenStorage", "drawerService", "themeService", "authService", "router", "loaderService", "dialogHost", "showHeaderAndNav", "constructor", "ngAfterViewInit", "registerViewContainer", "ngOnInit", "savedTheme", "getCurrentTheme", "setTheme", "authConfig", "apiAuthUrl", "consoleApiAuthUrl", "redirectUrl", "consoleRedirectUrl", "postLoginRedirectUrl", "appName", "setAuthConfig", "handleAuthCodeAndToken", "startTokenCheck", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "events", "subscribe", "event", "url", "name", "resetOnNavigation", "ngOnDestroy", "stopTokenCheck", "ɵɵdirectiveInject", "i1", "AuthTokenService", "i2", "TokenStorageService", "i3", "DrawerService", "i4", "ThemeService", "i5", "AuthService", "i6", "Router", "i7", "LoaderService", "selectors", "viewQuery", "AppComponent_Query", "rf", "ctx", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵtemplate", "AppComponent_app_nav_header_5_Template", "ɵɵclassProp", "ɵɵadvance", "ɵɵproperty", "i8", "NgIf", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\app.component.ts", "C:\\console\\aava-ui-web\\projects\\console\\src\\app\\app.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  OnInit,\r\n  ViewChild,\r\n  ViewContainerRef,\r\n  HostListener,\r\n} from '@angular/core';\r\nimport { Router, RouterOutlet } from '@angular/router';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NavHeaderComponent } from './shared/components/nav-header/nav-header.component';\r\nimport { LoaderComponent } from './shared/components/loader/loader.component';\r\nimport { DrawerService } from './shared/services/drawer/drawer.service';\r\nimport { ThemeService } from './shared/services/theme/theme.service';\r\nimport { LoaderService } from './shared/services/loader/loader.service';\r\nimport { TokenStorageService } from '@shared/auth/services/token-storage.service';\r\nimport { AuthTokenService } from '@shared/auth/services/auth-token.service';\r\nimport { AuthConfig } from '@shared/auth/interfaces/auth-config.interface';\r\nimport { AuthService } from '@shared/auth/services/auth.service';\r\nimport { environment } from '../environments/environment';\r\n\r\n@Component({\r\n  selector: 'app-root',\r\n  standalone: true,\r\n  imports: [\r\n    RouterOutlet,\r\n    NavHeaderComponent,\r\n    LoaderComponent,\r\n    CommonModule,\r\n  ],\r\n  templateUrl: './app.component.html',\r\n  styleUrl: './app.component.scss',\r\n})\r\nexport class AppComponent implements OnInit {\r\n  @ViewChild('dialogHost', { read: ViewContainerRef })\r\n  dialogHost!: ViewContainerRef;\r\n  showHeaderAndNav: boolean = true;\r\n\r\n  // Retractable header properties\r\n  // isHeaderVisible: boolean = true;\r\n  // private headerTimeout: any;\r\n  // private readonly HEADER_SHOW_DURATION = 1000; // 1 second delay before hiding\r\n  // private readonly HEADER_ACTIVATION_ZONE = 100; // Top 100px area to activate header\r\n  // private isMouseOnHeader: boolean = false;\r\n\r\n  constructor(\r\n    private authTokenService: AuthTokenService,\r\n    private tokenStorage: TokenStorageService,\r\n    private drawerService: DrawerService,\r\n    private themeService: ThemeService,\r\n    private authService: AuthService,\r\n    private router: Router,\r\n    private loaderService: LoaderService, // Inject LoaderService\r\n  ) {}\r\n\r\n  // Mouse event handlers for retractable header\r\n  // @HostListener('mousemove', ['$event'])\r\n  // onMouseMove(event: MouseEvent) {\r\n  //   if (this.showHeaderAndNav) {\r\n  //     const mouseY = event.clientY;\r\n\r\n  //     // Check if mouse is in the top activation zone\r\n  //     if (mouseY <= this.HEADER_ACTIVATION_ZONE) {\r\n  //       this.showHeader();\r\n  //       return;\r\n  //     }\r\n\r\n  //     // Check if mouse is on the header area (when header is visible)\r\n  //     if (this.isHeaderVisible) {\r\n  //       // Get header element to check if mouse is on it\r\n  //       const headerElement = document.querySelector(\r\n  //         '.retractable-header',\r\n  //       ) as HTMLElement;\r\n  //       const breadcrumbElement = document.querySelector(\r\n  //         '.retractable-breadcrumb',\r\n  //       ) as HTMLElement;\r\n\r\n  //       if (headerElement || breadcrumbElement) {\r\n  //         const headerRect = headerElement?.getBoundingClientRect();\r\n  //         const breadcrumbRect = breadcrumbElement?.getBoundingClientRect();\r\n\r\n  //         // Check if mouse is on header or breadcrumb\r\n  //         const isOnHeader =\r\n  //           headerRect &&\r\n  //           mouseY >= headerRect.top &&\r\n  //           mouseY <= headerRect.bottom &&\r\n  //           event.clientX >= headerRect.left &&\r\n  //           event.clientX <= headerRect.right;\r\n\r\n  //         const isOnBreadcrumb =\r\n  //           breadcrumbRect &&\r\n  //           mouseY >= breadcrumbRect.top &&\r\n  //           mouseY <= breadcrumbRect.bottom &&\r\n  //           event.clientX >= breadcrumbRect.left &&\r\n  //           event.clientX <= breadcrumbRect.right;\r\n\r\n  //         if (isOnHeader || isOnBreadcrumb) {\r\n  //           // Mouse is on header - keep it visible and clear any hide timeout\r\n  //           this.isMouseOnHeader = true;\r\n  //           clearTimeout(this.headerTimeout);\r\n  //           return;\r\n  //         } else {\r\n  //           // Mouse left header area - start hide timer\r\n  //           this.isMouseOnHeader = false;\r\n  //           this.startHideTimer();\r\n  //         }\r\n  //       }\r\n  //     }\r\n  //   }\r\n  // }\r\n\r\n  // @HostListener('mouseleave', ['$event'])\r\n  // onMouseLeave(event: MouseEvent) {\r\n  //   if (this.showHeaderAndNav && !this.isMouseOnHeader) {\r\n  //     this.startHideTimer();\r\n  //   }\r\n  // }\r\n\r\n  // private showHeader() {\r\n  //   clearTimeout(this.headerTimeout);\r\n  //   this.isHeaderVisible = true;\r\n  //   this.isMouseOnHeader = false; // Reset mouse on header state\r\n  // }\r\n\r\n  // private startHideTimer() {\r\n  //   clearTimeout(this.headerTimeout);\r\n  //   this.headerTimeout = setTimeout(() => {\r\n  //     if (!this.isMouseOnHeader) {\r\n  //       this.isHeaderVisible = false;\r\n  //     }\r\n  //   }, this.HEADER_SHOW_DURATION);\r\n  // }\r\n\r\n  ngAfterViewInit() {\r\n    this.drawerService.registerViewContainer(this.dialogHost);\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    const savedTheme = this.themeService.getCurrentTheme();\r\n    this.themeService.setTheme(savedTheme);\r\n    const authConfig: AuthConfig = {\r\n      apiAuthUrl: environment.consoleApiAuthUrl,\r\n      redirectUrl: environment.consoleRedirectUrl,\r\n      postLoginRedirectUrl: '/dashboard',\r\n      appName: 'console',\r\n    };\r\n    this.authService.setAuthConfig(authConfig);\r\n    this.authTokenService.handleAuthCodeAndToken();\r\n    this.authTokenService.startTokenCheck();\r\n    if (!this.tokenStorage.getCookie('org_path')) {\r\n      this.tokenStorage.setCookie(\r\n        'org_path',\r\n        'ascendion@core@genai@aava::201@202@203@204',\r\n      );\r\n    }\r\n    this.router.events.subscribe((event) => {\r\n      if (this.router.url === '/login') {\r\n        this.showHeaderAndNav = false;\r\n        //this.isHeaderVisible = false; // Ensure header is hidden on login page\r\n      } else {\r\n        this.showHeaderAndNav = true;\r\n        //this.isHeaderVisible = true; // Show header on other pages\r\n      }\r\n      // Reset loader on navigation start\r\n      if (event.constructor.name === 'NavigationStart') {\r\n        this.loaderService.resetOnNavigation();\r\n      }\r\n    });\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.authTokenService.stopTokenCheck();\r\n    // Clean up header timeout\r\n    // if (this.headerTimeout) {\r\n    //   clearTimeout(this.headerTimeout);\r\n    // }\r\n  }\r\n}\r\n", "<div id=\"app-container\" [class.login-active]=\"true\">\r\n  <div class=\"panel\">\r\n    <div #dialogHost></div>\r\n  </div>\r\n  <div class=\"glass-effect\"></div>\r\n\r\n  <!-- Retractable Header - Now part of main flow -->\r\n  <app-nav-header *ngIf=\"showHeaderAndNav\" class=\"retractable-header\">\r\n  </app-nav-header>\r\n\r\n  <div class=\"content-wrapper\" [class.post-login]=\"showHeaderAndNav\">\r\n    <app-loader></app-loader>\r\n    <router-outlet></router-outlet>\r\n  </div>\r\n</div>\r\n<!-- <footer>© 2025 Ascendion. All Rights Reserved</footer> -->\r\n"], "mappings": "AAAA,SAIEA,gBAAgB,QAEX,eAAe;AACtB,SAAiBC,YAAY,QAAQ,iBAAiB;AACtD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,kBAAkB,QAAQ,qDAAqD;AACxF,SAASC,eAAe,QAAQ,6CAA6C;AAQ7E,SAASC,WAAW,QAAQ,6BAA6B;;;;;;;;;;;;;ICXvDC,EAAA,CAAAC,SAAA,wBACiB;;;ADwBnB,WAAaC,YAAY;EAAnB,MAAOA,YAAY;IAabC,gBAAA;IACAC,YAAA;IACAC,aAAA;IACAC,YAAA;IACAC,WAAA;IACAC,MAAA;IACAC,aAAA;IAjBVC,UAAU;IACVC,gBAAgB,GAAY,IAAI;IAEhC;IACA;IACA;IACA;IACA;IACA;IAEAC,YACUT,gBAAkC,EAClCC,YAAiC,EACjCC,aAA4B,EAC5BC,YAA0B,EAC1BC,WAAwB,EACxBC,MAAc,EACdC,aAA4B;MAN5B,KAAAN,gBAAgB,GAAhBA,gBAAgB;MAChB,KAAAC,YAAY,GAAZA,YAAY;MACZ,KAAAC,aAAa,GAAbA,aAAa;MACb,KAAAC,YAAY,GAAZA,YAAY;MACZ,KAAAC,WAAW,GAAXA,WAAW;MACX,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,aAAa,GAAbA,aAAa;IACpB;IAEH;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEAI,eAAeA,CAAA;MACb,IAAI,CAACR,aAAa,CAACS,qBAAqB,CAAC,IAAI,CAACJ,UAAU,CAAC;IAC3D;IAEAK,QAAQA,CAAA;MACN,MAAMC,UAAU,GAAG,IAAI,CAACV,YAAY,CAACW,eAAe,EAAE;MACtD,IAAI,CAACX,YAAY,CAACY,QAAQ,CAACF,UAAU,CAAC;MACtC,MAAMG,UAAU,GAAe;QAC7BC,UAAU,EAAErB,WAAW,CAACsB,iBAAiB;QACzCC,WAAW,EAAEvB,WAAW,CAACwB,kBAAkB;QAC3CC,oBAAoB,EAAE,YAAY;QAClCC,OAAO,EAAE;OACV;MACD,IAAI,CAAClB,WAAW,CAACmB,aAAa,CAACP,UAAU,CAAC;MAC1C,IAAI,CAAChB,gBAAgB,CAACwB,sBAAsB,EAAE;MAC9C,IAAI,CAACxB,gBAAgB,CAACyB,eAAe,EAAE;MACvC,IAAI,CAAC,IAAI,CAACxB,YAAY,CAACyB,SAAS,CAAC,UAAU,CAAC,EAAE;QAC5C,IAAI,CAACzB,YAAY,CAAC0B,SAAS,CACzB,UAAU,EACV,4CAA4C,CAC7C;MACH;MACA,IAAI,CAACtB,MAAM,CAACuB,MAAM,CAACC,SAAS,CAAEC,KAAK,IAAI;QACrC,IAAI,IAAI,CAACzB,MAAM,CAAC0B,GAAG,KAAK,QAAQ,EAAE;UAChC,IAAI,CAACvB,gBAAgB,GAAG,KAAK;UAC7B;QACF,CAAC,MAAM;UACL,IAAI,CAACA,gBAAgB,GAAG,IAAI;UAC5B;QACF;QACA;QACA,IAAIsB,KAAK,CAACrB,WAAW,CAACuB,IAAI,KAAK,iBAAiB,EAAE;UAChD,IAAI,CAAC1B,aAAa,CAAC2B,iBAAiB,EAAE;QACxC;MACF,CAAC,CAAC;IACJ;IAEAC,WAAWA,CAAA;MACT,IAAI,CAAClC,gBAAgB,CAACmC,cAAc,EAAE;MACtC;MACA;MACA;MACA;IACF;;uCA/IWpC,YAAY,EAAAF,EAAA,CAAAuC,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAzC,EAAA,CAAAuC,iBAAA,CAAAG,EAAA,CAAAC,mBAAA,GAAA3C,EAAA,CAAAuC,iBAAA,CAAAK,EAAA,CAAAC,aAAA,GAAA7C,EAAA,CAAAuC,iBAAA,CAAAO,EAAA,CAAAC,YAAA,GAAA/C,EAAA,CAAAuC,iBAAA,CAAAS,EAAA,CAAAC,WAAA,GAAAjD,EAAA,CAAAuC,iBAAA,CAAAW,EAAA,CAAAC,MAAA,GAAAnD,EAAA,CAAAuC,iBAAA,CAAAa,EAAA,CAAAC,aAAA;IAAA;;YAAZnD,YAAY;MAAAoD,SAAA;MAAAC,SAAA,WAAAC,mBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;iCACU/D,gBAAgB;;;;;;;;;;;;UChCjDM,EADF,CAAA2D,cAAA,aAAoD,aAC/B;UACjB3D,EAAA,CAAAC,SAAA,mBAAuB;UACzBD,EAAA,CAAA4D,YAAA,EAAM;UACN5D,EAAA,CAAAC,SAAA,aAAgC;UAGhCD,EAAA,CAAA6D,UAAA,IAAAC,sCAAA,4BAAoE;UAGpE9D,EAAA,CAAA2D,cAAA,aAAmE;UAEjE3D,EADA,CAAAC,SAAA,iBAAyB,oBACM;UAEnCD,EADE,CAAA4D,YAAA,EAAM,EACF;;;UAdkB5D,EAAA,CAAA+D,WAAA,sBAA2B;UAOhC/D,EAAA,CAAAgE,SAAA,GAAsB;UAAtBhE,EAAA,CAAAiE,UAAA,SAAAP,GAAA,CAAA/C,gBAAA,CAAsB;UAGVX,EAAA,CAAAgE,SAAA,EAAqC;UAArChE,EAAA,CAAA+D,WAAA,eAAAL,GAAA,CAAA/C,gBAAA,CAAqC;;;qBDchEhB,YAAY,EACZE,kBAAkB,EAClBC,eAAe,EACfF,YAAY,EAAAsE,EAAA,CAAAC,IAAA;MAAAC,MAAA;IAAA;;SAKHlE,YAAY;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}