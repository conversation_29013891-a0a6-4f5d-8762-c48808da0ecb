{"ast": null, "code": "import { input } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nconst _c0 = [[[\"\", \"panel-header\", \"\"]], [[\"\", \"panel-content\", \"\"]], [[\"\", \"panel-footer\", \"\"]]];\nconst _c1 = [\"[panel-header]\", \"[panel-content]\", \"[panel-footer]\"];\nfunction PreviewPanelComponent_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 2);\n  }\n}\nfunction PreviewPanelComponent_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 2);\n  }\n}\nexport let PreviewPanelComponent = /*#__PURE__*/(() => {\n  class PreviewPanelComponent {\n    divider = input(true);\n    showFooter = true;\n    static ɵfac = function PreviewPanelComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PreviewPanelComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PreviewPanelComponent,\n      selectors: [[\"app-preview-panel\"]],\n      inputs: {\n        divider: [1, \"divider\"],\n        showFooter: \"showFooter\"\n      },\n      ngContentSelectors: _c1,\n      decls: 9,\n      vars: 2,\n      consts: [[1, \"preview-panel\"], [1, \"preview-header\"], [1, \"divider\"], [1, \"preview-content\"], [1, \"preview-footer\"]],\n      template: function PreviewPanelComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c0);\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵprojection(2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(3, PreviewPanelComponent_Conditional_3_Template, 1, 0, \"div\", 2);\n          i0.ɵɵelementStart(4, \"div\", 3);\n          i0.ɵɵprojection(5, 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(6, PreviewPanelComponent_Conditional_6_Template, 1, 0, \"div\", 2);\n          i0.ɵɵelementStart(7, \"div\", 4);\n          i0.ɵɵprojection(8, 2);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵconditional(ctx.divider() ? 3 : -1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵconditional(ctx.divider() ? 6 : -1);\n        }\n      },\n      styles: [\".preview-panel[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  right: 0;\\n  width: 480px;\\n  height: 100vh;\\n  background: #fff;\\n  box-shadow: -2px 0 12px rgba(0, 0, 0, 0.08);\\n  display: flex;\\n  z-index: 200;\\n  flex-direction: column;\\n  padding: 0;\\n  border-left: 1px solid #eee;\\n}\\n\\n.preview-header[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  width: 100%;\\n  min-height: 56px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 20px 24px 0 24px;\\n}\\n\\n.divider[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 2px;\\n  background: #b4b4b4;\\n  margin: 20px 0;\\n}\\n\\n.preview-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  width: 100%;\\n  overflow-y: auto;\\n  padding: 0 32px;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.preview-footer[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  width: 100%;\\n  padding: 0 24px 20px 24px;\\n  box-sizing: border-box;\\n}\\n\\n@media (max-width: 600px) {\\n  .preview-panel[_ngcontent-%COMP%] {\\n    width: 100vw;\\n    left: 0;\\n    right: 0;\\n    padding: 16px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return PreviewPanelComponent;\n})();", "map": {"version": 3, "names": ["input", "i0", "ɵɵelement", "PreviewPanelComponent", "divider", "showFooter", "selectors", "inputs", "ngContentSelectors", "_c1", "decls", "vars", "consts", "template", "PreviewPanelComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵprojection", "ɵɵelementEnd", "ɵɵtemplate", "PreviewPanelComponent_Conditional_3_Template", "PreviewPanelComponent_Conditional_6_Template", "ɵɵadvance", "ɵɵconditional"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\components\\preview-panel\\preview-panel.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\components\\preview-panel\\preview-panel.component.html"], "sourcesContent": ["import { Component, input, Input } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-preview-panel',\r\n  imports: [],\r\n  templateUrl: './preview-panel.component.html',\r\n  styleUrl: './preview-panel.component.scss'\r\n})\r\nexport class PreviewPanelComponent {\r\n  divider = input<boolean>(true);\r\n@Input() showFooter: boolean = true;\r\n}\r\n", "<div class=\"preview-panel\">\r\n  <div class=\"preview-header\">\r\n    <ng-content select=\"[panel-header]\"></ng-content>\r\n  </div>\r\n  @if(divider()) {\r\n    <div class=\"divider\"></div>\r\n  }\r\n  <div class=\"preview-content\">\r\n    <ng-content select=\"[panel-content]\"></ng-content>\r\n  </div>\r\n  @if(divider()) {\r\n    <div class=\"divider\"></div>\r\n  }\r\n  <div class=\"preview-footer\">\r\n    <ng-content select=\"[panel-footer]\"></ng-content>\r\n  </div>\r\n</div>"], "mappings": "AAAA,SAAoBA,KAAK,QAAe,eAAe;;;;;;ICKnDC,EAAA,CAAAC,SAAA,aAA2B;;;;;IAM3BD,EAAA,CAAAC,SAAA,aAA2B;;;ADH/B,WAAaC,qBAAqB;EAA5B,MAAOA,qBAAqB;IAChCC,OAAO,GAAGJ,KAAK,CAAU,IAAI,CAAC;IACvBK,UAAU,GAAY,IAAI;;uCAFtBF,qBAAqB;IAAA;;YAArBA,qBAAqB;MAAAG,SAAA;MAAAC,MAAA;QAAAH,OAAA;QAAAC,UAAA;MAAA;MAAAG,kBAAA,EAAAC,GAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCPhCd,EADF,CAAAgB,cAAA,aAA2B,aACG;UAC1BhB,EAAA,CAAAiB,YAAA,GAAiD;UACnDjB,EAAA,CAAAkB,YAAA,EAAM;UACNlB,EAAA,CAAAmB,UAAA,IAAAC,4CAAA,iBAAgB;UAGhBpB,EAAA,CAAAgB,cAAA,aAA6B;UAC3BhB,EAAA,CAAAiB,YAAA,MAAkD;UACpDjB,EAAA,CAAAkB,YAAA,EAAM;UACNlB,EAAA,CAAAmB,UAAA,IAAAE,4CAAA,iBAAgB;UAGhBrB,EAAA,CAAAgB,cAAA,aAA4B;UAC1BhB,EAAA,CAAAiB,YAAA,MAAiD;UAErDjB,EADE,CAAAkB,YAAA,EAAM,EACF;;;UAZJlB,EAAA,CAAAsB,SAAA,GAEC;UAFDtB,EAAA,CAAAuB,aAAA,CAAAR,GAAA,CAAAZ,OAAA,YAEC;UAIDH,EAAA,CAAAsB,SAAA,GAEC;UAFDtB,EAAA,CAAAuB,aAAA,CAAAR,GAAA,CAAAZ,OAAA,YAEC;;;;;;SDJUD,qBAAqB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}