{"ast": null, "code": "import { selector } from \"d3-selection\";\nimport { Transition } from \"./index.js\";\nimport schedule, { get } from \"./schedule.js\";\nexport default function (select) {\n  var name = this._name,\n    id = this._id;\n  if (typeof select !== \"function\") select = selector(select);\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = new Array(n), node, subnode, i = 0; i < n; ++i) {\n      if ((node = group[i]) && (subnode = select.call(node, node.__data__, i, group))) {\n        if (\"__data__\" in node) subnode.__data__ = node.__data__;\n        subgroup[i] = subnode;\n        schedule(subgroup[i], name, id, i, subgroup, get(node, id));\n      }\n    }\n  }\n  return new Transition(subgroups, this._parents, name, id);\n}", "map": {"version": 3, "names": ["selector", "Transition", "schedule", "get", "select", "name", "_name", "id", "_id", "groups", "_groups", "m", "length", "subgroups", "Array", "j", "group", "n", "subgroup", "node", "subnode", "i", "call", "__data__", "_parents"], "sources": ["C:/console/aava-ui-web/node_modules/d3-transition/src/transition/select.js"], "sourcesContent": ["import {selector} from \"d3-selection\";\nimport {Transition} from \"./index.js\";\nimport schedule, {get} from \"./schedule.js\";\n\nexport default function(select) {\n  var name = this._name,\n      id = this._id;\n\n  if (typeof select !== \"function\") select = selector(select);\n\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = new Array(n), node, subnode, i = 0; i < n; ++i) {\n      if ((node = group[i]) && (subnode = select.call(node, node.__data__, i, group))) {\n        if (\"__data__\" in node) subnode.__data__ = node.__data__;\n        subgroup[i] = subnode;\n        schedule(subgroup[i], name, id, i, subgroup, get(node, id));\n      }\n    }\n  }\n\n  return new Transition(subgroups, this._parents, name, id);\n}\n"], "mappings": "AAAA,SAAQA,QAAQ,QAAO,cAAc;AACrC,SAAQC,UAAU,QAAO,YAAY;AACrC,OAAOC,QAAQ,IAAGC,GAAG,QAAO,eAAe;AAE3C,eAAe,UAASC,MAAM,EAAE;EAC9B,IAAIC,IAAI,GAAG,IAAI,CAACC,KAAK;IACjBC,EAAE,GAAG,IAAI,CAACC,GAAG;EAEjB,IAAI,OAAOJ,MAAM,KAAK,UAAU,EAAEA,MAAM,GAAGJ,QAAQ,CAACI,MAAM,CAAC;EAE3D,KAAK,IAAIK,MAAM,GAAG,IAAI,CAACC,OAAO,EAAEC,CAAC,GAAGF,MAAM,CAACG,MAAM,EAAEC,SAAS,GAAG,IAAIC,KAAK,CAACH,CAAC,CAAC,EAAEI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,CAAC,EAAE,EAAEI,CAAC,EAAE;IAC9F,KAAK,IAAIC,KAAK,GAAGP,MAAM,CAACM,CAAC,CAAC,EAAEE,CAAC,GAAGD,KAAK,CAACJ,MAAM,EAAEM,QAAQ,GAAGL,SAAS,CAACE,CAAC,CAAC,GAAG,IAAID,KAAK,CAACG,CAAC,CAAC,EAAEE,IAAI,EAAEC,OAAO,EAAEC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,CAAC,EAAE,EAAEI,CAAC,EAAE;MACtH,IAAI,CAACF,IAAI,GAAGH,KAAK,CAACK,CAAC,CAAC,MAAMD,OAAO,GAAGhB,MAAM,CAACkB,IAAI,CAACH,IAAI,EAAEA,IAAI,CAACI,QAAQ,EAAEF,CAAC,EAAEL,KAAK,CAAC,CAAC,EAAE;QAC/E,IAAI,UAAU,IAAIG,IAAI,EAAEC,OAAO,CAACG,QAAQ,GAAGJ,IAAI,CAACI,QAAQ;QACxDL,QAAQ,CAACG,CAAC,CAAC,GAAGD,OAAO;QACrBlB,QAAQ,CAACgB,QAAQ,CAACG,CAAC,CAAC,EAAEhB,IAAI,EAAEE,EAAE,EAAEc,CAAC,EAAEH,QAAQ,EAAEf,GAAG,CAACgB,IAAI,EAAEZ,EAAE,CAAC,CAAC;MAC7D;IACF;EACF;EAEA,OAAO,IAAIN,UAAU,CAACY,SAAS,EAAE,IAAI,CAACW,QAAQ,EAAEnB,IAAI,EAAEE,EAAE,CAAC;AAC3D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}