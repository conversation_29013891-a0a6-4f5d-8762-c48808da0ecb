{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { IconComponent } from '@ava/play-comp-library';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nconst _c0 = [\"*\"];\nconst _c1 = (a0, a1) => [a0, a1];\nfunction ConsoleCardComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"div\", 5);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ConsoleCardComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7)(2, \"div\", 8);\n    i0.ɵɵelement(3, \"div\", 9)(4, \"div\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 11);\n    i0.ɵɵelement(6, \"div\", 12)(7, \"div\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 14);\n    i0.ɵɵelement(9, \"div\", 15)(10, \"div\", 16)(11, \"div\", 17)(12, \"div\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 19)(14, \"div\", 20)(15, \"div\", 21);\n    i0.ɵɵelement(16, \"div\", 12)(17, \"div\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 21);\n    i0.ɵɵelement(19, \"div\", 12)(20, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 24);\n    i0.ɵɵelement(22, \"div\", 25)(23, \"div\", 25)(24, \"div\", 25)(25, \"div\", 26);\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ConsoleCardComponent_div_3_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵelement(1, \"ava-icon\", 34);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"iconSize\", 12);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.author);\n  }\n}\nfunction ConsoleCardComponent_div_3_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵelement(1, \"ava-icon\", 46);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"iconSize\", 12);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.date);\n  }\n}\nfunction ConsoleCardComponent_div_3_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function ConsoleCardComponent_div_3_button_22_Template_button_click_0_listener() {\n      const action_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onActionClick(action_r3));\n    });\n    i0.ɵɵelement(1, \"ava-icon\", 31);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const action_r3 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"primary\", action_r3.isPrimary);\n    i0.ɵɵproperty(\"disabled\", action_r3.disabled || ctx_r0.disabled || ctx_r0.loading || ctx_r0.skeleton)(\"title\", action_r3.tooltip);\n    i0.ɵɵattribute(\"aria-label\", action_r3.label)(\"data-tooltip\", action_r3.tooltip);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"iconName\", action_r3.icon)(\"iconSize\", 14);\n  }\n}\nfunction ConsoleCardComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 28)(2, \"div\", 29)(3, \"div\", 30);\n    i0.ɵɵelement(4, \"ava-icon\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 32);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 33);\n    i0.ɵɵelement(8, \"ava-icon\", 34);\n    i0.ɵɵelementStart(9, \"span\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 35)(12, \"h3\", 36);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"p\", 37);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵprojection(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 38)(18, \"div\", 39);\n    i0.ɵɵtemplate(19, ConsoleCardComponent_div_3_div_19_Template, 4, 2, \"div\", 40)(20, ConsoleCardComponent_div_3_div_20_Template, 4, 2, \"div\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 42);\n    i0.ɵɵtemplate(22, ConsoleCardComponent_div_3_button_22_Template, 2, 8, \"button\", 43);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"iconName\", ctx_r0.categoryIcon)(\"iconSize\", 18);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.categoryTitle);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"iconSize\", 14);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.categoryValue);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"title\", ctx_r0.title);\n    i0.ɵɵattribute(\"aria-label\", ctx_r0.title);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.title, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"truncated\", ctx_r0.isDescriptionTruncated);\n    i0.ɵɵproperty(\"title\", ctx_r0.isDescriptionTruncated ? ctx_r0.description : \"\");\n    i0.ɵɵattribute(\"aria-label\", ctx_r0.description);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.truncatedDescription, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.author);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.date);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.actions);\n  }\n}\nexport let ConsoleCardComponent = /*#__PURE__*/(() => {\n  class ConsoleCardComponent {\n    title = '';\n    description = '';\n    categoryIcon = 'bot';\n    categoryTitle = 'Agents';\n    categoryValue = '75';\n    author = '';\n    date = '';\n    variant = 'primary';\n    size = 'medium';\n    disabled = false;\n    loading = false;\n    skeleton = false;\n    // Dynamic action buttons\n    actions = [{\n      id: 'view',\n      icon: 'calendar-days',\n      label: 'View details',\n      tooltip: 'View Details'\n    }, {\n      id: 'delete',\n      icon: 'trash',\n      label: 'Delete item',\n      tooltip: 'Delete'\n    }, {\n      id: 'copy',\n      icon: 'copy',\n      label: 'Copy to clipboard',\n      tooltip: 'Copy'\n    }, {\n      id: 'play',\n      icon: 'play',\n      label: 'Execute or play',\n      tooltip: 'Play',\n      isPrimary: true\n    }];\n    // Event emitters for action clicks\n    actionClick = new EventEmitter();\n    constructor() {\n      // console.log('🟡 ConsoleCardComponent constructor called');\n    }\n    ngOnInit() {\n      // console.log('🟢 ConsoleCard ngOnInit - inputs:', {\n      //   title: this.title,\n      //   description: this.description,\n      //   categoryTitle: this.categoryTitle,\n      //   categoryValue: this.categoryValue,\n      //   author: this.author,\n      //   date: this.date,\n      //   variant: this.variant,\n      //   skeleton: this.skeleton,\n      //   actions: this.actions,\n      // });\n    }\n    ngAfterViewInit() {\n      // console.log(\n      //   '🔵 ConsoleCard ngAfterViewInit - all inputs should be set now:',\n      //   {\n      //     title: this.title,\n      //     description: this.description,\n      //     categoryTitle: this.categoryTitle,\n      //     categoryValue: this.categoryValue,\n      //     author: this.author,\n      //     date: this.date,\n      //     skeleton: this.skeleton,\n      //     actions: this.actions,\n      //   },\n      // );\n    }\n    onActionClick(action) {\n      if (!action.disabled && !this.disabled && !this.loading && !this.skeleton) {\n        console.log('🔄 Action clicked:', action);\n        this.actionClick.emit({\n          actionId: action.id,\n          action\n        });\n      }\n    }\n    /**\n     * Get truncated description (200 characters max)\n     */\n    get truncatedDescription() {\n      if (!this.description) return '';\n      if (this.description.length <= 200) return this.description;\n      return this.description.substring(0, 200) + '...';\n    }\n    /**\n     * Check if description is truncated\n     */\n    get isDescriptionTruncated() {\n      return !!(this.description && this.description.length > 200);\n    }\n    static ɵfac = function ConsoleCardComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ConsoleCardComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ConsoleCardComponent,\n      selectors: [[\"ava-console-card\"]],\n      inputs: {\n        title: \"title\",\n        description: \"description\",\n        categoryIcon: \"categoryIcon\",\n        categoryTitle: \"categoryTitle\",\n        categoryValue: \"categoryValue\",\n        author: \"author\",\n        date: \"date\",\n        variant: \"variant\",\n        size: \"size\",\n        disabled: \"disabled\",\n        loading: \"loading\",\n        skeleton: \"skeleton\",\n        actions: \"actions\"\n      },\n      outputs: {\n        actionClick: \"actionClick\"\n      },\n      ngContentSelectors: _c0,\n      decls: 4,\n      vars: 15,\n      consts: [[1, \"ava-console-card\", 3, \"ngClass\"], [\"class\", \"loading-overlay\", 4, \"ngIf\"], [\"class\", \"skeleton-loader\", 4, \"ngIf\"], [\"class\", \"card-content\", 4, \"ngIf\"], [1, \"loading-overlay\"], [1, \"loading-spinner\"], [1, \"skeleton-loader\"], [1, \"skeleton-header\"], [1, \"skeleton-category\"], [1, \"skeleton-icon\"], [1, \"skeleton-text\", \"skeleton-category-title\"], [1, \"skeleton-value\"], [1, \"skeleton-icon-small\"], [1, \"skeleton-text\", \"skeleton-number\"], [1, \"skeleton-body\"], [1, \"skeleton-text\", \"skeleton-title\"], [1, \"skeleton-text\", \"skeleton-description-1\"], [1, \"skeleton-text\", \"skeleton-description-2\"], [1, \"skeleton-text\", \"skeleton-description-3\"], [1, \"skeleton-footer\"], [1, \"skeleton-metadata\"], [1, \"skeleton-meta-item\"], [1, \"skeleton-text\", \"skeleton-author\"], [1, \"skeleton-text\", \"skeleton-date\"], [1, \"skeleton-actions\"], [1, \"skeleton-button\"], [1, \"skeleton-button\", \"skeleton-button-primary\"], [1, \"card-content\"], [1, \"card-header\"], [1, \"category-section\"], [1, \"category-icon\"], [3, \"iconName\", \"iconSize\"], [1, \"category-title\"], [1, \"category-value\"], [\"iconName\", \"user\", 3, \"iconSize\"], [1, \"card-body\"], [1, \"card-title\", 3, \"title\"], [1, \"card-description\", 3, \"title\"], [1, \"card-footer\"], [1, \"metadata\"], [\"class\", \"author\", 4, \"ngIf\"], [\"class\", \"date\", 4, \"ngIf\"], [1, \"actions\"], [\"class\", \"action-btn\", \"type\", \"button\", 3, \"primary\", \"disabled\", \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"author\"], [1, \"date\"], [\"iconName\", \"calendar-days\", 3, \"iconSize\"], [\"type\", \"button\", 1, \"action-btn\", 3, \"click\", \"disabled\", \"title\"]],\n      template: function ConsoleCardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, ConsoleCardComponent_div_1_Template, 2, 0, \"div\", 1)(2, ConsoleCardComponent_div_2_Template, 26, 0, \"div\", 2)(3, ConsoleCardComponent_div_3_Template, 23, 16, \"div\", 3);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"disabled\", ctx.disabled)(\"loading\", ctx.loading)(\"skeleton\", ctx.skeleton);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(12, _c1, ctx.variant, ctx.size));\n          i0.ɵɵattribute(\"role\", \"listitem\")(\"aria-disabled\", ctx.disabled);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.skeleton);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.skeleton);\n        }\n      },\n      dependencies: [CommonModule, i1.NgClass, i1.NgForOf, i1.NgIf, IconComponent],\n      styles: [\"svg {\\n  stroke: #2f5a8e !important;\\n}\\n\\n.ava-console-card[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  flex-direction: column;\\n  width: 100%;\\n  min-width: 350px;\\n  height: 326px;\\n  background: #ffffff;\\n  border-radius: 16px;\\n  box-shadow: 0px 4px 12px 0px rgba(135, 161, 151, 0.1215686275);\\n  -webkit-backdrop-filter: blur(6px);\\n          backdrop-filter: blur(6px);\\n  transition: all 0.3s ease;\\n}\\n.ava-console-card[_ngcontent-%COMP%]:hover:not(.disabled) {\\n  transform: translateY(-8px);\\n  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);\\n}\\n.ava-console-card[_ngcontent-%COMP%]:focus-within {\\n  outline-offset: 4px;\\n}\\n.ava-console-card.disabled[_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n  pointer-events: none;\\n}\\n.ava-console-card.loading[_ngcontent-%COMP%] {\\n  pointer-events: none;\\n}\\n.ava-console-card.skeleton[_ngcontent-%COMP%] {\\n  pointer-events: none;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .loading-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(255, 255, 255, 0.95);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 10;\\n  border-radius: inherit;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .loading-overlay[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border: 3px solid #e0e0e0;\\n  border-top: 3px solid #e91e63;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 24px;\\n  padding-bottom: 16px;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .category-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .category-section[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: #e91e63;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .category-section[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%]   ava-icon[_ngcontent-%COMP%] {\\n  color: inherit !important;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .category-section[_ngcontent-%COMP%]   .category-title[_ngcontent-%COMP%] {\\n  font-weight: 400;\\n  font-size: 12px;\\n  color: #2f5a8e;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .category-value[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  font-weight: 600;\\n  font-size: 12px;\\n  color: #2f5a8e;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .category-value[_ngcontent-%COMP%]   ava-icon[_ngcontent-%COMP%] {\\n  color: inherit !important;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .category-value[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: inherit !important;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%] {\\n  min-height: 200px;\\n  padding: 0 24px 24px;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n  font-size: 24px;\\n  color: #1a46a7;\\n  margin: 0;\\n  margin-bottom: 16px;\\n  line-height: 1.3;\\n  width: 100%;\\n  max-width: 100%;\\n  display: block;\\n  word-wrap: break-word;\\n  overflow-wrap: break-word;\\n  hyphens: auto;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  cursor: pointer;\\n  position: relative;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]   .card-description[_ngcontent-%COMP%] {\\n  font-weight: 400;\\n  font-size: 14px;\\n  color: #2f5a8e;\\n  margin: 0;\\n  margin-top: 1.2rem;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]   .card-description.truncated[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  position: relative;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-end;\\n  justify-content: space-between;\\n  padding: 0 24px 24px;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%]   .metadata[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 6px;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%]   .metadata[_ngcontent-%COMP%]   .author[_ngcontent-%COMP%], \\n.ava-console-card[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%]   .metadata[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  font-weight: 400;\\n  font-size: 12px;\\n  color: #2f5a8e;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%]   .metadata[_ngcontent-%COMP%]   .author[_ngcontent-%COMP%]   ava-icon[_ngcontent-%COMP%], \\n.ava-console-card[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%]   .metadata[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%]   ava-icon[_ngcontent-%COMP%] {\\n  color: inherit !important;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%]   .metadata[_ngcontent-%COMP%]   .author[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], \\n.ava-console-card[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%]   .metadata[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: inherit !important;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%]   .actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%]   .actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 32px;\\n  height: 32px;\\n  border: none;\\n  border-radius: 8px;\\n  background: #ffffff;\\n  color: #6b7280;\\n  cursor: pointer;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  overflow: visible;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%]   .actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ava-icon[_ngcontent-%COMP%] {\\n  color: inherit !important;\\n  transition: transform 0.2s ease;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%]   .actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover {\\n  background: #f8fafc;\\n  border-color: #2f5a8e;\\n  color: #2f5a8e;\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(47, 90, 142, 0.15);\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%]   .actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover   ava-icon[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%]   .actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover::after {\\n  opacity: 1;\\n  visibility: visible;\\n  transform: translateX(-50%) translateY(0px);\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%]   .actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid #2f5a8e;\\n  outline-offset: 2px;\\n  background: #f8fafc;\\n  border-color: #2f5a8e;\\n  color: #2f5a8e;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%]   .actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(0px);\\n  box-shadow: 0 2px 6px rgba(47, 90, 142, 0.2);\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%]   .actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]::after {\\n  content: attr(data-tooltip);\\n  position: absolute;\\n  bottom: 100%;\\n  left: 50%;\\n  transform: translateX(-50%) translateY(-8px);\\n  background: #1f2937;\\n  color: white;\\n  padding: 8px 12px;\\n  border-radius: 8px;\\n  font-size: 11px;\\n  font-weight: 500;\\n  white-space: nowrap;\\n  opacity: 0;\\n  visibility: hidden;\\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\\n  z-index: 1000;\\n  margin-bottom: 6px;\\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\\n  pointer-events: none;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%]   .actions[_ngcontent-%COMP%]   .action-btn.primary[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 16px rgba(26, 70, 167, 0.25);\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%]   .actions[_ngcontent-%COMP%]   .action-btn.primary[_ngcontent-%COMP%]:active {\\n  transform: translateY(0px);\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%]   .actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n  pointer-events: none;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%]   .actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:disabled:hover {\\n  transform: none;\\n  box-shadow: none;\\n}\\n.ava-console-card.small[_ngcontent-%COMP%] {\\n  width: 240px;\\n  height: 320px;\\n}\\n.ava-console-card.small[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n}\\n.ava-console-card.small[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%]   .actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  width: 28px;\\n  height: 28px;\\n}\\n.ava-console-card.large[_ngcontent-%COMP%] {\\n  width: 320px;\\n  height: 440px;\\n}\\n.ava-console-card.large[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\\n  font-size: 22px;\\n}\\n.ava-console-card.large[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%]   .actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .skeleton-loader[_ngcontent-%COMP%] {\\n  padding: 24px;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .skeleton-loader[_ngcontent-%COMP%]   .skeleton-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin-bottom: 20px;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .skeleton-loader[_ngcontent-%COMP%]   .skeleton-header[_ngcontent-%COMP%]   .skeleton-category[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .skeleton-loader[_ngcontent-%COMP%]   .skeleton-header[_ngcontent-%COMP%]   .skeleton-value[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .skeleton-loader[_ngcontent-%COMP%]   .skeleton-body[_ngcontent-%COMP%] {\\n  margin-bottom: 60px;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .skeleton-loader[_ngcontent-%COMP%]   .skeleton-body[_ngcontent-%COMP%]   .skeleton-title[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .skeleton-loader[_ngcontent-%COMP%]   .skeleton-footer[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-end;\\n  justify-content: space-between;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .skeleton-loader[_ngcontent-%COMP%]   .skeleton-footer[_ngcontent-%COMP%]   .skeleton-metadata[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .skeleton-loader[_ngcontent-%COMP%]   .skeleton-footer[_ngcontent-%COMP%]   .skeleton-meta-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .skeleton-loader[_ngcontent-%COMP%]   .skeleton-footer[_ngcontent-%COMP%]   .skeleton-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .skeleton-text[_ngcontent-%COMP%], \\n.ava-console-card[_ngcontent-%COMP%]   .skeleton-icon[_ngcontent-%COMP%], \\n.ava-console-card[_ngcontent-%COMP%]   .skeleton-icon-small[_ngcontent-%COMP%], \\n.ava-console-card[_ngcontent-%COMP%]   .skeleton-button[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_shimmer 1.5s infinite;\\n  border-radius: 4px;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .skeleton-text[_ngcontent-%COMP%] {\\n  height: 16px;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .skeleton-text.skeleton-category-title[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 12px;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .skeleton-text.skeleton-number[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 12px;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .skeleton-text.skeleton-title[_ngcontent-%COMP%] {\\n  width: 85%;\\n  height: 24px;\\n  border-radius: 6px;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .skeleton-text.skeleton-description-1[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 14px;\\n  margin-bottom: 8px;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .skeleton-text.skeleton-description-2[_ngcontent-%COMP%] {\\n  width: 90%;\\n  height: 14px;\\n  margin-bottom: 8px;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .skeleton-text.skeleton-description-3[_ngcontent-%COMP%] {\\n  width: 75%;\\n  height: 14px;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .skeleton-text.skeleton-author[_ngcontent-%COMP%] {\\n  width: 70px;\\n  height: 12px;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .skeleton-text.skeleton-date[_ngcontent-%COMP%] {\\n  width: 55px;\\n  height: 12px;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .skeleton-icon[_ngcontent-%COMP%] {\\n  width: 18px;\\n  height: 18px;\\n  border-radius: 50%;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .skeleton-icon-small[_ngcontent-%COMP%] {\\n  width: 12px;\\n  height: 12px;\\n  border-radius: 50%;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .skeleton-button[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 8px;\\n}\\n.ava-console-card[_ngcontent-%COMP%]   .skeleton-button.skeleton-button-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, rgba(47, 90, 142, 0.2509803922) 25%, rgba(26, 70, 167, 0.2509803922) 50%, rgba(47, 90, 142, 0.2509803922) 75%);\\n  background-size: 200% 100%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_shimmer {\\n  0% {\\n    background-position: -200% 0;\\n  }\\n  100% {\\n    background-position: 200% 0;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_tooltipFadeIn {\\n  0% {\\n    opacity: 0;\\n    visibility: hidden;\\n    transform: translateY(-8px);\\n  }\\n  100% {\\n    opacity: 1;\\n    visibility: visible;\\n    transform: translateY(0);\\n  }\\n}\\n.console-cards-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\\n  gap: 24px;\\n  width: 100%;\\n  justify-items: center;\\n  padding: 24px;\\n}\\n@media (min-width: 1400px) {\\n  .console-cards-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(4, 1fr);\\n    max-width: 1200px;\\n    margin: 0 auto;\\n  }\\n}\\n@media (min-width: 1024px) and (max-width: 1399px) {\\n  .console-cards-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(3, 1fr);\\n    max-width: 900px;\\n    margin: 0 auto;\\n  }\\n}\\n@media (min-width: 768px) and (max-width: 1023px) {\\n  .console-cards-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n    max-width: 600px;\\n    margin: 0 auto;\\n  }\\n}\\n@media (max-width: 767px) {\\n  .console-cards-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    max-width: 320px;\\n    margin: 0 auto;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return ConsoleCardComponent;\n})();", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "IconComponent", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵproperty", "ɵɵtextInterpolate", "ctx_r0", "author", "date", "ɵɵlistener", "ConsoleCardComponent_div_3_button_22_Template_button_click_0_listener", "action_r3", "ɵɵrestoreView", "_r2", "$implicit", "ɵɵnextContext", "ɵɵresetView", "onActionClick", "ɵɵclassProp", "isPrimary", "disabled", "loading", "skeleton", "tooltip", "icon", "ɵɵprojection", "ɵɵtemplate", "ConsoleCardComponent_div_3_div_19_Template", "ConsoleCardComponent_div_3_div_20_Template", "ConsoleCardComponent_div_3_button_22_Template", "categoryIcon", "categoryTitle", "categoryValue", "title", "ɵɵtextInterpolate1", "isDescriptionTruncated", "description", "truncatedDescription", "actions", "ConsoleCardComponent", "variant", "size", "id", "label", "actionClick", "constructor", "ngOnInit", "ngAfterViewInit", "action", "console", "log", "emit", "actionId", "length", "substring", "selectors", "inputs", "outputs", "ngContentSelectors", "_c0", "decls", "vars", "consts", "template", "ConsoleCardComponent_Template", "rf", "ctx", "ConsoleCardComponent_div_1_Template", "ConsoleCardComponent_div_2_Template", "ConsoleCardComponent_div_3_Template", "ɵɵpureFunction2", "_c1", "i1", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\shared\\components\\console-card\\console-card.component.ts", "C:\\console\\aava-ui-web\\projects\\console\\src\\app\\shared\\components\\console-card\\console-card.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  Input,\r\n  OnInit,\r\n  AfterViewInit,\r\n  Output,\r\n  EventEmitter,\r\n} from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { IconComponent } from '@ava/play-comp-library';\r\n\r\nexport interface ConsoleCardAction {\r\n  id: string;\r\n  icon: string;\r\n  label: string;\r\n  tooltip: string;\r\n  isPrimary?: boolean;\r\n  disabled?: boolean;\r\n}\r\n\r\n@Component({\r\n  selector: 'ava-console-card',\r\n  standalone: true,\r\n  imports: [CommonModule, IconComponent],\r\n  templateUrl: './console-card.component.html',\r\n  styleUrl: './console-card.component.scss',\r\n})\r\nexport class ConsoleCardComponent implements OnInit, AfterViewInit {\r\n  @Input() title = '';\r\n  @Input() description = '';\r\n  @Input() categoryIcon = 'bot';\r\n  @Input() categoryTitle = 'Agents';\r\n  @Input() categoryValue = '75';\r\n  @Input() author = '';\r\n  @Input() date = '';\r\n  @Input() variant:\r\n    | 'primary'\r\n    | 'secondary'\r\n    | 'tertiary'\r\n    | 'quaternary'\r\n    | 'quinary'\r\n    | 'senary' = 'primary';\r\n  @Input() size: 'small' | 'medium' | 'large' = 'medium';\r\n  @Input() disabled = false;\r\n  @Input() loading = false;\r\n  @Input() skeleton = false;\r\n\r\n  // Dynamic action buttons\r\n  @Input() actions: ConsoleCardAction[] = [\r\n    {\r\n      id: 'view',\r\n      icon: 'calendar-days',\r\n      label: 'View details',\r\n      tooltip: 'View Details',\r\n    },\r\n    {\r\n      id: 'delete',\r\n      icon: 'trash',\r\n      label: 'Delete item',\r\n      tooltip: 'Delete',\r\n    },\r\n    {\r\n      id: 'copy',\r\n      icon: 'copy',\r\n      label: 'Copy to clipboard',\r\n      tooltip: 'Copy',\r\n    },\r\n    {\r\n      id: 'play',\r\n      icon: 'play',\r\n      label: 'Execute or play',\r\n      tooltip: 'Play',\r\n      isPrimary: true,\r\n    },\r\n  ];\r\n\r\n  // Event emitters for action clicks\r\n  @Output() actionClick = new EventEmitter<{\r\n    actionId: string;\r\n    action: ConsoleCardAction;\r\n  }>();\r\n\r\n  constructor() {\r\n    // console.log('🟡 ConsoleCardComponent constructor called');\r\n  }\r\n\r\n  ngOnInit() {\r\n    // console.log('🟢 ConsoleCard ngOnInit - inputs:', {\r\n    //   title: this.title,\r\n    //   description: this.description,\r\n    //   categoryTitle: this.categoryTitle,\r\n    //   categoryValue: this.categoryValue,\r\n    //   author: this.author,\r\n    //   date: this.date,\r\n    //   variant: this.variant,\r\n    //   skeleton: this.skeleton,\r\n    //   actions: this.actions,\r\n    // });\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n    // console.log(\r\n    //   '🔵 ConsoleCard ngAfterViewInit - all inputs should be set now:',\r\n    //   {\r\n    //     title: this.title,\r\n    //     description: this.description,\r\n    //     categoryTitle: this.categoryTitle,\r\n    //     categoryValue: this.categoryValue,\r\n    //     author: this.author,\r\n    //     date: this.date,\r\n    //     skeleton: this.skeleton,\r\n    //     actions: this.actions,\r\n    //   },\r\n    // );\r\n  }\r\n\r\n  onActionClick(action: ConsoleCardAction): void {\r\n    if (!action.disabled && !this.disabled && !this.loading && !this.skeleton) {\r\n      console.log('🔄 Action clicked:', action);\r\n      this.actionClick.emit({ actionId: action.id, action });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get truncated description (200 characters max)\r\n   */\r\n  get truncatedDescription(): string {\r\n    if (!this.description) return '';\r\n    if (this.description.length <= 200) return this.description;\r\n    return this.description.substring(0, 200) + '...';\r\n  }\r\n\r\n  /**\r\n   * Check if description is truncated\r\n   */\r\n  get isDescriptionTruncated(): boolean {\r\n    return !!(this.description && this.description.length > 200);\r\n  }\r\n}\r\n", "<div\r\n  class=\"ava-console-card\"\r\n  [ngClass]=\"[variant, size]\"\r\n  [class.disabled]=\"disabled\"\r\n  [class.loading]=\"loading\"\r\n  [class.skeleton]=\"skeleton\"\r\n  [attr.role]=\"'listitem'\"\r\n  [attr.aria-disabled]=\"disabled\"\r\n>\r\n  <!-- Loading Overlay -->\r\n  <div class=\"loading-overlay\" *ngIf=\"loading\">\r\n    <div class=\"loading-spinner\"></div>\r\n  </div>\r\n\r\n  <!-- Skeleton Loader -->\r\n  <div class=\"skeleton-loader\" *ngIf=\"skeleton\">\r\n    <!-- Skeleton Header -->\r\n    <div class=\"skeleton-header\">\r\n      <div class=\"skeleton-category\">\r\n        <div class=\"skeleton-icon\"></div>\r\n        <div class=\"skeleton-text skeleton-category-title\"></div>\r\n      </div>\r\n      <div class=\"skeleton-value\">\r\n        <div class=\"skeleton-icon-small\"></div>\r\n        <div class=\"skeleton-text skeleton-number\"></div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Skeleton Content -->\r\n    <div class=\"skeleton-body\">\r\n      <div class=\"skeleton-text skeleton-title\"></div>\r\n      <div class=\"skeleton-text skeleton-description-1\"></div>\r\n      <div class=\"skeleton-text skeleton-description-2\"></div>\r\n      <div class=\"skeleton-text skeleton-description-3\"></div>\r\n    </div>\r\n\r\n    <!-- Skeleton Footer -->\r\n    <div class=\"skeleton-footer\">\r\n      <div class=\"skeleton-metadata\">\r\n        <div class=\"skeleton-meta-item\">\r\n          <div class=\"skeleton-icon-small\"></div>\r\n          <div class=\"skeleton-text skeleton-author\"></div>\r\n        </div>\r\n        <div class=\"skeleton-meta-item\">\r\n          <div class=\"skeleton-icon-small\"></div>\r\n          <div class=\"skeleton-text skeleton-date\"></div>\r\n        </div>\r\n      </div>\r\n      <div class=\"skeleton-actions\">\r\n        <div class=\"skeleton-button\"></div>\r\n        <div class=\"skeleton-button\"></div>\r\n        <div class=\"skeleton-button\"></div>\r\n        <div class=\"skeleton-button skeleton-button-primary\"></div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Regular Content (hidden when skeleton is active) -->\r\n  <div class=\"card-content\" *ngIf=\"!skeleton\">\r\n    <!-- Header Section -->\r\n    <div class=\"card-header\">\r\n      <div class=\"category-section\">\r\n        <div class=\"category-icon\">\r\n          <ava-icon [iconName]=\"categoryIcon\" [iconSize]=\"18\"></ava-icon>\r\n        </div>\r\n        <span class=\"category-title\">{{ categoryTitle }}</span>\r\n      </div>\r\n      <div class=\"category-value\">\r\n        <ava-icon iconName=\"user\" [iconSize]=\"14\"></ava-icon>\r\n        <span>{{ categoryValue }}</span>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Main Content Area -->\r\n    <div class=\"card-body\">\r\n      <h3 class=\"card-title\" [title]=\"title\" [attr.aria-label]=\"title\">\r\n        {{ title }}\r\n      </h3>\r\n      <p\r\n        class=\"card-description\"\r\n        [class.truncated]=\"isDescriptionTruncated\"\r\n        [title]=\"isDescriptionTruncated ? description : ''\"\r\n        [attr.aria-label]=\"description\"\r\n      >\r\n        {{ truncatedDescription }}\r\n      </p>\r\n      <ng-content></ng-content>\r\n    </div>\r\n\r\n    <!-- Footer Section -->\r\n    <div class=\"card-footer\">\r\n      <div class=\"metadata\">\r\n        <div class=\"author\" *ngIf=\"author\">\r\n          <ava-icon iconName=\"user\" [iconSize]=\"12\"></ava-icon>\r\n          <span>{{ author }}</span>\r\n        </div>\r\n        <div class=\"date\" *ngIf=\"date\">\r\n          <ava-icon iconName=\"calendar-days\" [iconSize]=\"12\"></ava-icon>\r\n          <span>{{ date }}</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"actions\">\r\n        <button\r\n          *ngFor=\"let action of actions\"\r\n          class=\"action-btn\"\r\n          [class.primary]=\"action.isPrimary\"\r\n          [disabled]=\"action.disabled || disabled || loading || skeleton\"\r\n          type=\"button\"\r\n          [attr.aria-label]=\"action.label\"\r\n          [title]=\"action.tooltip\"\r\n          [attr.data-tooltip]=\"action.tooltip\"\r\n          (click)=\"onActionClick(action)\"\r\n        >\r\n          <ava-icon [iconName]=\"action.icon\" [iconSize]=\"14\"></ava-icon>\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAAA,SAMEA,YAAY,QACP,eAAe;AACtB,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;;;;;;;ICCpDC,EAAA,CAAAC,cAAA,aAA6C;IAC3CD,EAAA,CAAAE,SAAA,aAAmC;IACrCF,EAAA,CAAAG,YAAA,EAAM;;;;;IAMFH,EAHJ,CAAAC,cAAA,aAA8C,aAEf,aACI;IAE7BD,EADA,CAAAE,SAAA,aAAiC,cACwB;IAC3DF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA4B;IAE1BD,EADA,CAAAE,SAAA,cAAuC,cACU;IAErDF,EADE,CAAAG,YAAA,EAAM,EACF;IAGNH,EAAA,CAAAC,cAAA,cAA2B;IAIzBD,EAHA,CAAAE,SAAA,cAAgD,eACQ,eACA,eACA;IAC1DF,EAAA,CAAAG,YAAA,EAAM;IAKFH,EAFJ,CAAAC,cAAA,eAA6B,eACI,eACG;IAE9BD,EADA,CAAAE,SAAA,eAAuC,eACU;IACnDF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAgC;IAE9BD,EADA,CAAAE,SAAA,eAAuC,eACQ;IAEnDF,EADE,CAAAG,YAAA,EAAM,EACF;IACNH,EAAA,CAAAC,cAAA,eAA8B;IAI5BD,EAHA,CAAAE,SAAA,eAAmC,eACA,eACA,eACwB;IAGjEF,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;;IAqCAH,EAAA,CAAAC,cAAA,cAAmC;IACjCD,EAAA,CAAAE,SAAA,mBAAqD;IACrDF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,GAAY;IACpBJ,EADoB,CAAAG,YAAA,EAAO,EACrB;;;;IAFsBH,EAAA,CAAAK,SAAA,EAAe;IAAfL,EAAA,CAAAM,UAAA,gBAAe;IACnCN,EAAA,CAAAK,SAAA,GAAY;IAAZL,EAAA,CAAAO,iBAAA,CAAAC,MAAA,CAAAC,MAAA,CAAY;;;;;IAEpBT,EAAA,CAAAC,cAAA,cAA+B;IAC7BD,EAAA,CAAAE,SAAA,mBAA8D;IAC9DF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,GAAU;IAClBJ,EADkB,CAAAG,YAAA,EAAO,EACnB;;;;IAF+BH,EAAA,CAAAK,SAAA,EAAe;IAAfL,EAAA,CAAAM,UAAA,gBAAe;IAC5CN,EAAA,CAAAK,SAAA,GAAU;IAAVL,EAAA,CAAAO,iBAAA,CAAAC,MAAA,CAAAE,IAAA,CAAU;;;;;;IAIlBV,EAAA,CAAAC,cAAA,iBAUC;IADCD,EAAA,CAAAW,UAAA,mBAAAC,sEAAA;MAAA,MAAAC,SAAA,GAAAb,EAAA,CAAAc,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAR,MAAA,GAAAR,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASV,MAAA,CAAAW,aAAA,CAAAN,SAAA,CAAqB;IAAA,EAAC;IAE/Bb,EAAA,CAAAE,SAAA,mBAA8D;IAChEF,EAAA,CAAAG,YAAA,EAAS;;;;;IATPH,EAAA,CAAAoB,WAAA,YAAAP,SAAA,CAAAQ,SAAA,CAAkC;IAIlCrB,EAHA,CAAAM,UAAA,aAAAO,SAAA,CAAAS,QAAA,IAAAd,MAAA,CAAAc,QAAA,IAAAd,MAAA,CAAAe,OAAA,IAAAf,MAAA,CAAAgB,QAAA,CAA+D,UAAAX,SAAA,CAAAY,OAAA,CAGvC;;IAIdzB,EAAA,CAAAK,SAAA,EAAwB;IAACL,EAAzB,CAAAM,UAAA,aAAAO,SAAA,CAAAa,IAAA,CAAwB,gBAAgB;;;;;IAnDpD1B,EAJN,CAAAC,cAAA,cAA4C,cAEjB,cACO,cACD;IACzBD,EAAA,CAAAE,SAAA,mBAA+D;IACjEF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAI,MAAA,GAAmB;IAClDJ,EADkD,CAAAG,YAAA,EAAO,EACnD;IACNH,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAAE,SAAA,mBAAqD;IACrDF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,IAAmB;IAE7BJ,EAF6B,CAAAG,YAAA,EAAO,EAC5B,EACF;IAIJH,EADF,CAAAC,cAAA,eAAuB,cAC4C;IAC/DD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAKC;IACCD,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAA2B,YAAA,IAAyB;IAC3B3B,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,eAAyB,eACD;IAKpBD,EAJA,CAAA4B,UAAA,KAAAC,0CAAA,kBAAmC,KAAAC,0CAAA,kBAIJ;IAIjC9B,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAqB;IACnBD,EAAA,CAAA4B,UAAA,KAAAG,6CAAA,qBAUC;IAKP/B,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IAtDYH,EAAA,CAAAK,SAAA,GAAyB;IAACL,EAA1B,CAAAM,UAAA,aAAAE,MAAA,CAAAwB,YAAA,CAAyB,gBAAgB;IAExBhC,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAO,iBAAA,CAAAC,MAAA,CAAAyB,aAAA,CAAmB;IAGtBjC,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAM,UAAA,gBAAe;IACnCN,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAO,iBAAA,CAAAC,MAAA,CAAA0B,aAAA,CAAmB;IAMJlC,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAM,UAAA,UAAAE,MAAA,CAAA2B,KAAA,CAAe;;IACpCnC,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAoC,kBAAA,MAAA5B,MAAA,CAAA2B,KAAA,MACF;IAGEnC,EAAA,CAAAK,SAAA,EAA0C;IAA1CL,EAAA,CAAAoB,WAAA,cAAAZ,MAAA,CAAA6B,sBAAA,CAA0C;IAC1CrC,EAAA,CAAAM,UAAA,UAAAE,MAAA,CAAA6B,sBAAA,GAAA7B,MAAA,CAAA8B,WAAA,MAAmD;;IAGnDtC,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAoC,kBAAA,MAAA5B,MAAA,CAAA+B,oBAAA,MACF;IAOuBvC,EAAA,CAAAK,SAAA,GAAY;IAAZL,EAAA,CAAAM,UAAA,SAAAE,MAAA,CAAAC,MAAA,CAAY;IAIdT,EAAA,CAAAK,SAAA,EAAU;IAAVL,EAAA,CAAAM,UAAA,SAAAE,MAAA,CAAAE,IAAA,CAAU;IAORV,EAAA,CAAAK,SAAA,GAAU;IAAVL,EAAA,CAAAM,UAAA,YAAAE,MAAA,CAAAgC,OAAA,CAAU;;;AD5EvC,WAAaC,oBAAoB;EAA3B,MAAOA,oBAAoB;IACtBN,KAAK,GAAG,EAAE;IACVG,WAAW,GAAG,EAAE;IAChBN,YAAY,GAAG,KAAK;IACpBC,aAAa,GAAG,QAAQ;IACxBC,aAAa,GAAG,IAAI;IACpBzB,MAAM,GAAG,EAAE;IACXC,IAAI,GAAG,EAAE;IACTgC,OAAO,GAMD,SAAS;IACfC,IAAI,GAAiC,QAAQ;IAC7CrB,QAAQ,GAAG,KAAK;IAChBC,OAAO,GAAG,KAAK;IACfC,QAAQ,GAAG,KAAK;IAEzB;IACSgB,OAAO,GAAwB,CACtC;MACEI,EAAE,EAAE,MAAM;MACVlB,IAAI,EAAE,eAAe;MACrBmB,KAAK,EAAE,cAAc;MACrBpB,OAAO,EAAE;KACV,EACD;MACEmB,EAAE,EAAE,QAAQ;MACZlB,IAAI,EAAE,OAAO;MACbmB,KAAK,EAAE,aAAa;MACpBpB,OAAO,EAAE;KACV,EACD;MACEmB,EAAE,EAAE,MAAM;MACVlB,IAAI,EAAE,MAAM;MACZmB,KAAK,EAAE,mBAAmB;MAC1BpB,OAAO,EAAE;KACV,EACD;MACEmB,EAAE,EAAE,MAAM;MACVlB,IAAI,EAAE,MAAM;MACZmB,KAAK,EAAE,iBAAiB;MACxBpB,OAAO,EAAE,MAAM;MACfJ,SAAS,EAAE;KACZ,CACF;IAED;IACUyB,WAAW,GAAG,IAAIjD,YAAY,EAGpC;IAEJkD,YAAA;MACE;IAAA;IAGFC,QAAQA,CAAA;MACN;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IAAA;IAGFC,eAAeA,CAAA;MACb;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IAAA;IAGF9B,aAAaA,CAAC+B,MAAyB;MACrC,IAAI,CAACA,MAAM,CAAC5B,QAAQ,IAAI,CAAC,IAAI,CAACA,QAAQ,IAAI,CAAC,IAAI,CAACC,OAAO,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE;QACzE2B,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEF,MAAM,CAAC;QACzC,IAAI,CAACJ,WAAW,CAACO,IAAI,CAAC;UAAEC,QAAQ,EAAEJ,MAAM,CAACN,EAAE;UAAEM;QAAM,CAAE,CAAC;MACxD;IACF;IAEA;;;IAGA,IAAIX,oBAAoBA,CAAA;MACtB,IAAI,CAAC,IAAI,CAACD,WAAW,EAAE,OAAO,EAAE;MAChC,IAAI,IAAI,CAACA,WAAW,CAACiB,MAAM,IAAI,GAAG,EAAE,OAAO,IAAI,CAACjB,WAAW;MAC3D,OAAO,IAAI,CAACA,WAAW,CAACkB,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK;IACnD;IAEA;;;IAGA,IAAInB,sBAAsBA,CAAA;MACxB,OAAO,CAAC,EAAE,IAAI,CAACC,WAAW,IAAI,IAAI,CAACA,WAAW,CAACiB,MAAM,GAAG,GAAG,CAAC;IAC9D;;uCA9GWd,oBAAoB;IAAA;;YAApBA,oBAAoB;MAAAgB,SAAA;MAAAC,MAAA;QAAAvB,KAAA;QAAAG,WAAA;QAAAN,YAAA;QAAAC,aAAA;QAAAC,aAAA;QAAAzB,MAAA;QAAAC,IAAA;QAAAgC,OAAA;QAAAC,IAAA;QAAArB,QAAA;QAAAC,OAAA;QAAAC,QAAA;QAAAgB,OAAA;MAAA;MAAAmB,OAAA;QAAAb,WAAA;MAAA;MAAAc,kBAAA,EAAAC,GAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UC3BjCnE,EAAA,CAAAC,cAAA,aAQC;UAkDCD,EAhDA,CAAA4B,UAAA,IAAAyC,mCAAA,iBAA6C,IAAAC,mCAAA,kBAKC,IAAAC,mCAAA,mBA2CF;UA4D9CvE,EAAA,CAAAG,YAAA,EAAM;;;UAjHJH,EAFA,CAAAoB,WAAA,aAAAgD,GAAA,CAAA9C,QAAA,CAA2B,YAAA8C,GAAA,CAAA7C,OAAA,CACF,aAAA6C,GAAA,CAAA5C,QAAA,CACE;UAH3BxB,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAAwE,eAAA,KAAAC,GAAA,EAAAL,GAAA,CAAA1B,OAAA,EAAA0B,GAAA,CAAAzB,IAAA,EAA2B;;UAQG3C,EAAA,CAAAK,SAAA,EAAa;UAAbL,EAAA,CAAAM,UAAA,SAAA8D,GAAA,CAAA7C,OAAA,CAAa;UAKbvB,EAAA,CAAAK,SAAA,EAAc;UAAdL,EAAA,CAAAM,UAAA,SAAA8D,GAAA,CAAA5C,QAAA,CAAc;UA2CjBxB,EAAA,CAAAK,SAAA,EAAe;UAAfL,EAAA,CAAAM,UAAA,UAAA8D,GAAA,CAAA5C,QAAA,CAAe;;;qBDnChC1B,YAAY,EAAA4E,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAE9E,aAAa;MAAA+E,MAAA;IAAA;;SAI1BrC,oBAAoB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}