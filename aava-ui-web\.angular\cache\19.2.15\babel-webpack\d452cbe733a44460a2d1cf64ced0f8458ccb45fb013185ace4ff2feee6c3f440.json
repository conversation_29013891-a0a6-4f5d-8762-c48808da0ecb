{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { IconComponent } from \"@ava/play-comp-library\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction NewsBlogsComponent_article_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"article\", 6)(1, \"div\", 7);\n    i0.ɵɵelement(2, \"img\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 9)(4, \"h3\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 10);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"a\", 11);\n    i0.ɵɵlistener(\"click\", function NewsBlogsComponent_article_8_Template_a_click_8_listener($event) {\n      const post_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      ctx_r2.readMore(post_r2.id);\n      return i0.ɵɵresetView($event.preventDefault());\n    });\n    i0.ɵɵtext(9, \"Read More\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 12)(11, \"div\", 13);\n    i0.ɵɵelement(12, \"img\", 14);\n    i0.ɵɵelementStart(13, \"span\", 15);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 16);\n    i0.ɵɵelement(16, \"ava-icon\", 17);\n    i0.ɵɵelementStart(17, \"span\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const post_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", post_r2.image, i0.ɵɵsanitizeUrl)(\"alt\", post_r2.title);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(post_r2.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", post_r2.description, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"src\", post_r2.author.avatar, i0.ɵɵsanitizeUrl)(\"alt\", post_r2.author.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(post_r2.author.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(post_r2.views);\n  }\n}\nexport let NewsBlogsComponent = /*#__PURE__*/(() => {\n  class NewsBlogsComponent {\n    blogPosts = [{\n      id: 101,\n      title: 'Gen AI 101 Learning Path',\n      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus vulputate, odio non blandit suscipit, arcu diam semper sapien,',\n      image: 'assets/icons/genai-101.svg',\n      author: {\n        name: 'Jhone Doe',\n        avatar: 'assets/icons/ellipse-avatar.svg'\n      },\n      views: 440\n    }, {\n      id: 102,\n      title: 'Ascendion uses AI to Elevate work',\n      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus vulputate, odio non blandit suscipit, arcu diam semper sapien,',\n      image: 'assets/icons/genai-102.svg',\n      author: {\n        name: 'Jhone Doe',\n        avatar: 'assets/icons/ellipse-avatar.svg'\n      },\n      views: 440\n    }, {\n      id: 103,\n      title: 'Gen AI 101 Learning Path',\n      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus vulputate, odio non blandit suscipit, arcu diam semper sapien,',\n      image: 'assets/icons/genai-103.svg',\n      author: {\n        name: 'Jhone Doe',\n        avatar: 'assets/icons/ellipse-avatar.svg'\n      },\n      views: 440\n    }, {\n      id: 104,\n      title: 'Gen AI 101 Learning Path',\n      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus vulputate, odio non blandit suscipit, arcu diam semper sapien,',\n      image: 'assets/icons/genai-104.svg',\n      author: {\n        name: 'Jhone Doe',\n        avatar: 'assets/icons/ellipse-avatar.svg'\n      },\n      views: 440\n    }, {\n      id: 105,\n      title: 'Gen AI 101 Learning Path',\n      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus vulputate, odio non blandit suscipit, arcu diam semper sapien,',\n      image: 'assets/icons/genai-105.svg',\n      author: {\n        name: 'Jhone Doe',\n        avatar: 'assets/icons/ellipse-avatar.svg'\n      },\n      views: 440\n    }, {\n      id: 106,\n      title: 'Gen AI 101 Learning Path',\n      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus vulputate, odio non blandit suscipit, arcu diam semper sapien,',\n      image: 'assets/icons/genai-106.svg',\n      author: {\n        name: 'Jhone Doe',\n        avatar: 'assets/icons/ellipse-avatar.svg'\n      },\n      views: 440\n    }];\n    readMore(id) {\n      // Implement your logic here for what happens when \"Read More\" is clicked.\n      // For example, navigate to a detailed blog post page:\n      // this.router.navigate(['/blog', id]);\n      console.log('Read More clicked for blog post ID:', id);\n      // alert(`Navigating to blog post ${id}`); // For demonstration\n    }\n    static ɵfac = function NewsBlogsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NewsBlogsComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NewsBlogsComponent,\n      selectors: [[\"app-news-blogs\"]],\n      decls: 9,\n      vars: 1,\n      consts: [[1, \"news-blogs\"], [1, \"container\"], [1, \"header\"], [1, \"gradient-text\"], [1, \"blog-grid\"], [\"class\", \"blog-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"blog-card\"], [1, \"blog-image\"], [3, \"src\", \"alt\"], [1, \"blog-content\"], [1, \"description\"], [\"href\", \"#\", 1, \"read-more\", 3, \"click\"], [1, \"blog-footer\"], [1, \"author\"], [1, \"author-avatar\", 3, \"src\", \"alt\"], [1, \"author-name\"], [1, \"views\"], [\"iconName\", \"eye\", \"iconColor\", \"#666D99\", \"iconSize\", \"14\"]],\n      template: function NewsBlogsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h2\");\n          i0.ɵɵtext(4, \" What's \");\n          i0.ɵɵelementStart(5, \"span\", 3);\n          i0.ɵɵtext(6, \"New?\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(7, \"div\", 4);\n          i0.ɵɵtemplate(8, NewsBlogsComponent_article_8_Template, 19, 8, \"article\", 5);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngForOf\", ctx.blogPosts);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, RouterModule, IconComponent],\n      styles: [\".news-blogs[_ngcontent-%COMP%] {\\n  padding: 80px 0;\\n  background: #ffffff;\\n}\\n.news-blogs[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 0 32px;\\n}\\n.news-blogs[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 48px;\\n}\\n.news-blogs[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  color: #14161f;\\n  font-family: \\\"Mulish\\\", sans-serif;\\n  font-size: 48px;\\n  font-weight: 700;\\n  line-height: 1.2;\\n  letter-spacing: -0.5px;\\n  margin: 0;\\n}\\n.news-blogs[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]   .gradient-text[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #6566CD 36.04%, #F96CAB 118.04%);\\n  background-clip: text;\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  color: transparent;\\n}\\n.news-blogs[_ngcontent-%COMP%]   .blog-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(3, 1fr);\\n  grid-template-rows: repeat(2, 1fr);\\n  gap: 24px;\\n  width: 100%;\\n}\\n.news-blogs[_ngcontent-%COMP%]   .blog-card[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: stretch;\\n  background: #ffffff;\\n  border-radius: 16px;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n  overflow: hidden;\\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\\n  min-height: 200px;\\n}\\n.news-blogs[_ngcontent-%COMP%]   .blog-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\\n}\\n.news-blogs[_ngcontent-%COMP%]   .blog-image[_ngcontent-%COMP%] {\\n  flex: 0 0 200px;\\n  width: 228px;\\n  height: 228%;\\n  overflow: hidden;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: #f8f9fa;\\n  padding: 10px;\\n}\\n.news-blogs[_ngcontent-%COMP%]   .blog-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n.news-blogs[_ngcontent-%COMP%]   .blog-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 24px;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: space-between;\\n}\\n.news-blogs[_ngcontent-%COMP%]   .blog-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #292c3d;\\n  font-family: \\\"Mulish\\\", sans-serif;\\n  font-size: 20px;\\n  font-weight: 700;\\n  line-height: 1.3;\\n  margin: 0 0 12px 0;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  display: -webkit-box;\\n  -webkit-line-clamp: 2;\\n  -webkit-box-orient: vertical;\\n}\\n.news-blogs[_ngcontent-%COMP%]   .blog-content[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%] {\\n  color: #666d99;\\n  font-family: \\\"Mulish\\\", sans-serif;\\n  font-size: 14px;\\n  font-weight: 400;\\n  line-height: 1.5;\\n  margin: 0 0 16px 0;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  display: -webkit-box;\\n  -webkit-line-clamp: 3;\\n  -webkit-box-orient: vertical;\\n}\\n.news-blogs[_ngcontent-%COMP%]   .read-more[_ngcontent-%COMP%] {\\n  color: #8b5cf6;\\n  font-family: \\\"Mulish\\\", sans-serif;\\n  font-size: 14px;\\n  font-weight: 600;\\n  text-decoration: none;\\n  margin-bottom: 16px;\\n  display: inline-block;\\n  transition: color 0.3s ease;\\n}\\n.news-blogs[_ngcontent-%COMP%]   .read-more[_ngcontent-%COMP%]:hover {\\n  color: #7c3aed;\\n  text-decoration: underline;\\n}\\n.news-blogs[_ngcontent-%COMP%]   .blog-footer[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin-top: auto;\\n}\\n.news-blogs[_ngcontent-%COMP%]   .author[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.news-blogs[_ngcontent-%COMP%]   .author-avatar[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n.news-blogs[_ngcontent-%COMP%]   .author-name[_ngcontent-%COMP%] {\\n  color: #292c3d;\\n  font-family: \\\"Mulish\\\", sans-serif;\\n  font-size: 12px;\\n  font-weight: 500;\\n  line-height: 1.4;\\n}\\n.news-blogs[_ngcontent-%COMP%]   .views[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  color: #666d99;\\n  font-family: \\\"Mulish\\\", sans-serif;\\n  font-size: 12px;\\n  font-weight: 500;\\n}\\n\\n@media (max-width: 1024px) {\\n  .news-blogs[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%] {\\n    padding: 0 24px;\\n  }\\n  .news-blogs[_ngcontent-%COMP%]   .blog-grid[_ngcontent-%COMP%] {\\n    gap: 20px;\\n  }\\n  .news-blogs[_ngcontent-%COMP%]   .blog-card[_ngcontent-%COMP%] {\\n    min-height: 180px;\\n  }\\n  .news-blogs[_ngcontent-%COMP%]   .blog-image[_ngcontent-%COMP%] {\\n    flex: 0 0 160px;\\n    width: 160px;\\n  }\\n  .news-blogs[_ngcontent-%COMP%]   .blog-content[_ngcontent-%COMP%] {\\n    padding: 20px;\\n  }\\n  .news-blogs[_ngcontent-%COMP%]   .blog-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .news-blogs[_ngcontent-%COMP%] {\\n    padding: 60px 0;\\n  }\\n  .news-blogs[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%] {\\n    padding: 0 20px;\\n  }\\n  .news-blogs[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n    font-size: 36px;\\n  }\\n  .news-blogs[_ngcontent-%COMP%]   .blog-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    grid-template-rows: auto;\\n    gap: 16px;\\n  }\\n  .news-blogs[_ngcontent-%COMP%]   .blog-card[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    min-height: auto;\\n  }\\n  .news-blogs[_ngcontent-%COMP%]   .blog-image[_ngcontent-%COMP%] {\\n    flex: none;\\n    width: 100%;\\n    height: 200px;\\n  }\\n  .news-blogs[_ngcontent-%COMP%]   .blog-content[_ngcontent-%COMP%] {\\n    padding: 20px;\\n  }\\n  .news-blogs[_ngcontent-%COMP%]   .blog-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n    margin-bottom: 8px;\\n  }\\n  .news-blogs[_ngcontent-%COMP%]   .blog-content[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n    margin-bottom: 12px;\\n    -webkit-line-clamp: 2;\\n  }\\n  .news-blogs[_ngcontent-%COMP%]   .read-more[_ngcontent-%COMP%] {\\n    margin-bottom: 12px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .news-blogs[_ngcontent-%COMP%] {\\n    padding: 40px 0;\\n  }\\n  .news-blogs[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%] {\\n    padding: 0 16px;\\n  }\\n  .news-blogs[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n    font-size: 28px;\\n  }\\n  .news-blogs[_ngcontent-%COMP%]   .blog-grid[_ngcontent-%COMP%] {\\n    gap: 12px;\\n  }\\n  .news-blogs[_ngcontent-%COMP%]   .blog-image[_ngcontent-%COMP%] {\\n    height: 160px;\\n  }\\n  .news-blogs[_ngcontent-%COMP%]   .blog-content[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .news-blogs[_ngcontent-%COMP%]   .blog-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .news-blogs[_ngcontent-%COMP%]   .blog-content[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%] {\\n    font-size: 13px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return NewsBlogsComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "IconComponent", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "NewsBlogsComponent_article_8_Template_a_click_8_listener", "$event", "post_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "readMore", "id", "ɵɵresetView", "preventDefault", "ɵɵadvance", "ɵɵproperty", "image", "ɵɵsanitizeUrl", "title", "ɵɵtextInterpolate", "ɵɵtextInterpolate1", "description", "author", "avatar", "name", "views", "NewsBlogsComponent", "blogPosts", "console", "log", "selectors", "decls", "vars", "consts", "template", "NewsBlogsComponent_Template", "rf", "ctx", "ɵɵtemplate", "NewsBlogsComponent_article_8_Template", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\shared\\components\\news-blogs\\news-blogs.component.ts", "C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\shared\\components\\news-blogs\\news-blogs.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { RouterModule } from '@angular/router';\r\nimport { IconsComponent } from '@awe/play-comp-library';\r\nimport { IconComponent } from \"@ava/play-comp-library\";\r\n\r\ninterface BlogPost {\r\n  id: number;\r\n  title: string;\r\n  description: string;\r\n  image: string;\r\n  author: {\r\n    name: string;\r\n    avatar: string;\r\n  };\r\n  views: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-news-blogs',\r\n  templateUrl: './news-blogs.component.html',\r\n  styleUrls: ['./news-blogs.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, RouterModule, IconsComponent, IconComponent],\r\n})\r\nexport class NewsBlogsComponent {\r\n  blogPosts: BlogPost[] = [\r\n    {\r\n      id: 101,\r\n      title: 'Gen AI 101 Learning Path',\r\n      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus vulputate, odio non blandit suscipit, arcu diam semper sapien,',\r\n      image: 'assets/icons/genai-101.svg',\r\n      author: {\r\n        name: '<PERSON><PERSON> Doe',\r\n        avatar: 'assets/icons/ellipse-avatar.svg',\r\n      },\r\n      views: 440,\r\n    },\r\n    {\r\n      id: 102,\r\n      title: 'Ascendion uses AI to Elevate work',\r\n      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus vulputate, odio non blandit suscipit, arcu diam semper sapien,',\r\n      image: 'assets/icons/genai-102.svg',\r\n      author: {\r\n        name: 'Jhone Doe',\r\n        avatar: 'assets/icons/ellipse-avatar.svg',\r\n      },\r\n      views: 440,\r\n    },\r\n    {\r\n      id: 103,\r\n      title: 'Gen AI 101 Learning Path',\r\n      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus vulputate, odio non blandit suscipit, arcu diam semper sapien,',\r\n      image: 'assets/icons/genai-103.svg',\r\n      author: {\r\n        name: 'Jhone Doe',\r\n        avatar: 'assets/icons/ellipse-avatar.svg',\r\n      },\r\n      views: 440,\r\n    },\r\n    {\r\n      id: 104,\r\n      title: 'Gen AI 101 Learning Path',\r\n      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus vulputate, odio non blandit suscipit, arcu diam semper sapien,',\r\n      image: 'assets/icons/genai-104.svg',\r\n      author: {\r\n        name: 'Jhone Doe',\r\n        avatar: 'assets/icons/ellipse-avatar.svg',\r\n      },\r\n      views: 440,\r\n    },\r\n    {\r\n      id: 105,\r\n      title: 'Gen AI 101 Learning Path',\r\n      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus vulputate, odio non blandit suscipit, arcu diam semper sapien,',\r\n      image: 'assets/icons/genai-105.svg',\r\n      author: {\r\n        name: 'Jhone Doe',\r\n        avatar: 'assets/icons/ellipse-avatar.svg',\r\n      },\r\n      views: 440,\r\n    },\r\n    {\r\n      id: 106,\r\n      title: 'Gen AI 101 Learning Path',\r\n      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Phasellus vulputate, odio non blandit suscipit, arcu diam semper sapien,',\r\n      image: 'assets/icons/genai-106.svg',\r\n      author: {\r\n        name: 'Jhone Doe',\r\n        avatar: 'assets/icons/ellipse-avatar.svg',\r\n      },\r\n      views: 440,\r\n    },\r\n  ];\r\n\r\n  readMore(id: number): void {\r\n    // Implement your logic here for what happens when \"Read More\" is clicked.\r\n    // For example, navigate to a detailed blog post page:\r\n    // this.router.navigate(['/blog', id]);\r\n    console.log('Read More clicked for blog post ID:', id);\r\n    // alert(`Navigating to blog post ${id}`); // For demonstration\r\n  }\r\n}", "<section class=\"news-blogs\">\r\n  <div class=\"container\">\r\n    <div class=\"header\">\r\n      <h2>\r\n        What's <span class=\"gradient-text\">New?</span>\r\n      </h2>\r\n    </div>\r\n    <div class=\"blog-grid\">\r\n      <article class=\"blog-card\" *ngFor=\"let post of blogPosts\">\r\n        <div class=\"blog-image\">\r\n          <img [src]=\"post.image\" [alt]=\"post.title\" />\r\n        </div>\r\n        <div class=\"blog-content\">\r\n          <h3>{{ post.title }}</h3>\r\n          <p class=\"description\">\r\n            {{ post.description }}\r\n          </p>\r\n          <a href=\"#\" class=\"read-more\" (click)=\"readMore(post.id); $event.preventDefault()\">Read More</a>\r\n          <div class=\"blog-footer\">\r\n            <div class=\"author\">\r\n              <img [src]=\"post.author.avatar\" [alt]=\"post.author.name\" class=\"author-avatar\" />\r\n              <span class=\"author-name\">{{ post.author.name }}</span>\r\n            </div>\r\n            <div class=\"views\">\r\n              <ava-icon iconName=\"eye\" iconColor=\"#666D99\" iconSize=\"14\"></ava-icon>\r\n              <span>{{ post.views }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </article>\r\n    </div>\r\n  </div>\r\n</section>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,aAAa,QAAQ,wBAAwB;;;;;;ICK9CC,EADF,CAAAC,cAAA,iBAA0D,aAChC;IACtBD,EAAA,CAAAE,SAAA,aAA6C;IAC/CF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,aAA0B,SACpB;IAAAD,EAAA,CAAAI,MAAA,GAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,YAAuB;IACrBD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,YAAmF;IAArDD,EAAA,CAAAK,UAAA,mBAAAC,yDAAAC,MAAA;MAAA,MAAAC,OAAA,GAAAR,EAAA,CAAAS,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAZ,EAAA,CAAAa,aAAA;MAASD,MAAA,CAAAE,QAAA,CAAAN,OAAA,CAAAO,EAAA,CAAiB;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAET,MAAA,CAAAU,cAAA,EAAuB;IAAA,EAAC;IAACjB,EAAA,CAAAI,MAAA,gBAAS;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAE9FH,EADF,CAAAC,cAAA,eAAyB,eACH;IAClBD,EAAA,CAAAE,SAAA,eAAiF;IACjFF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAI,MAAA,IAAsB;IAClDJ,EADkD,CAAAG,YAAA,EAAO,EACnD;IACNH,EAAA,CAAAC,cAAA,eAAmB;IACjBD,EAAA,CAAAE,SAAA,oBAAsE;IACtEF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAI,MAAA,IAAgB;IAI9BJ,EAJ8B,CAAAG,YAAA,EAAO,EACzB,EACF,EACF,EACE;;;;IAnBDH,EAAA,CAAAkB,SAAA,GAAkB;IAAClB,EAAnB,CAAAmB,UAAA,QAAAX,OAAA,CAAAY,KAAA,EAAApB,EAAA,CAAAqB,aAAA,CAAkB,QAAAb,OAAA,CAAAc,KAAA,CAAmB;IAGtCtB,EAAA,CAAAkB,SAAA,GAAgB;IAAhBlB,EAAA,CAAAuB,iBAAA,CAAAf,OAAA,CAAAc,KAAA,CAAgB;IAElBtB,EAAA,CAAAkB,SAAA,GACF;IADElB,EAAA,CAAAwB,kBAAA,MAAAhB,OAAA,CAAAiB,WAAA,MACF;IAISzB,EAAA,CAAAkB,SAAA,GAA0B;IAAClB,EAA3B,CAAAmB,UAAA,QAAAX,OAAA,CAAAkB,MAAA,CAAAC,MAAA,EAAA3B,EAAA,CAAAqB,aAAA,CAA0B,QAAAb,OAAA,CAAAkB,MAAA,CAAAE,IAAA,CAAyB;IAC9B5B,EAAA,CAAAkB,SAAA,GAAsB;IAAtBlB,EAAA,CAAAuB,iBAAA,CAAAf,OAAA,CAAAkB,MAAA,CAAAE,IAAA,CAAsB;IAI1C5B,EAAA,CAAAkB,SAAA,GAAgB;IAAhBlB,EAAA,CAAAuB,iBAAA,CAAAf,OAAA,CAAAqB,KAAA,CAAgB;;;ADApC,WAAaC,kBAAkB;EAAzB,MAAOA,kBAAkB;IAC7BC,SAAS,GAAe,CACtB;MACEhB,EAAE,EAAE,GAAG;MACPO,KAAK,EAAE,0BAA0B;MACjCG,WAAW,EAAE,mIAAmI;MAChJL,KAAK,EAAE,4BAA4B;MACnCM,MAAM,EAAE;QACNE,IAAI,EAAE,WAAW;QACjBD,MAAM,EAAE;OACT;MACDE,KAAK,EAAE;KACR,EACD;MACEd,EAAE,EAAE,GAAG;MACPO,KAAK,EAAE,mCAAmC;MAC1CG,WAAW,EAAE,mIAAmI;MAChJL,KAAK,EAAE,4BAA4B;MACnCM,MAAM,EAAE;QACNE,IAAI,EAAE,WAAW;QACjBD,MAAM,EAAE;OACT;MACDE,KAAK,EAAE;KACR,EACD;MACEd,EAAE,EAAE,GAAG;MACPO,KAAK,EAAE,0BAA0B;MACjCG,WAAW,EAAE,mIAAmI;MAChJL,KAAK,EAAE,4BAA4B;MACnCM,MAAM,EAAE;QACNE,IAAI,EAAE,WAAW;QACjBD,MAAM,EAAE;OACT;MACDE,KAAK,EAAE;KACR,EACD;MACEd,EAAE,EAAE,GAAG;MACPO,KAAK,EAAE,0BAA0B;MACjCG,WAAW,EAAE,mIAAmI;MAChJL,KAAK,EAAE,4BAA4B;MACnCM,MAAM,EAAE;QACNE,IAAI,EAAE,WAAW;QACjBD,MAAM,EAAE;OACT;MACDE,KAAK,EAAE;KACR,EACD;MACEd,EAAE,EAAE,GAAG;MACPO,KAAK,EAAE,0BAA0B;MACjCG,WAAW,EAAE,mIAAmI;MAChJL,KAAK,EAAE,4BAA4B;MACnCM,MAAM,EAAE;QACNE,IAAI,EAAE,WAAW;QACjBD,MAAM,EAAE;OACT;MACDE,KAAK,EAAE;KACR,EACD;MACEd,EAAE,EAAE,GAAG;MACPO,KAAK,EAAE,0BAA0B;MACjCG,WAAW,EAAE,mIAAmI;MAChJL,KAAK,EAAE,4BAA4B;MACnCM,MAAM,EAAE;QACNE,IAAI,EAAE,WAAW;QACjBD,MAAM,EAAE;OACT;MACDE,KAAK,EAAE;KACR,CACF;IAEDf,QAAQA,CAACC,EAAU;MACjB;MACA;MACA;MACAiB,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAElB,EAAE,CAAC;MACtD;IACF;;uCA5EWe,kBAAkB;IAAA;;YAAlBA,kBAAkB;MAAAI,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCtBzBxC,EAHN,CAAAC,cAAA,iBAA4B,aACH,aACD,SACd;UACFD,EAAA,CAAAI,MAAA,eAAO;UAAAJ,EAAA,CAAAC,cAAA,cAA4B;UAAAD,EAAA,CAAAI,MAAA,WAAI;UAE3CJ,EAF2C,CAAAG,YAAA,EAAO,EAC3C,EACD;UACNH,EAAA,CAAAC,cAAA,aAAuB;UACrBD,EAAA,CAAA0C,UAAA,IAAAC,qCAAA,sBAA0D;UAwBhE3C,EAFI,CAAAG,YAAA,EAAM,EACF,EACE;;;UAxBwCH,EAAA,CAAAkB,SAAA,GAAY;UAAZlB,EAAA,CAAAmB,UAAA,YAAAsB,GAAA,CAAAV,SAAA,CAAY;;;qBDelDlC,YAAY,EAAA+C,EAAA,CAAAC,OAAA,EAAE/C,YAAY,EAAkBC,aAAa;MAAA+C,MAAA;IAAA;;SAExDhB,kBAAkB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}