{"ast": null, "code": "import { asin, atan2, cos, degrees, haversin, radians, sin, sqrt } from \"./math.js\";\nexport default function (a, b) {\n  var x0 = a[0] * radians,\n    y0 = a[1] * radians,\n    x1 = b[0] * radians,\n    y1 = b[1] * radians,\n    cy0 = cos(y0),\n    sy0 = sin(y0),\n    cy1 = cos(y1),\n    sy1 = sin(y1),\n    kx0 = cy0 * cos(x0),\n    ky0 = cy0 * sin(x0),\n    kx1 = cy1 * cos(x1),\n    ky1 = cy1 * sin(x1),\n    d = 2 * asin(sqrt(haversin(y1 - y0) + cy0 * cy1 * haversin(x1 - x0))),\n    k = sin(d);\n  var interpolate = d ? function (t) {\n    var B = sin(t *= d) / k,\n      A = sin(d - t) / k,\n      x = A * kx0 + B * kx1,\n      y = A * ky0 + B * ky1,\n      z = A * sy0 + B * sy1;\n    return [atan2(y, x) * degrees, atan2(z, sqrt(x * x + y * y)) * degrees];\n  } : function () {\n    return [x0 * degrees, y0 * degrees];\n  };\n  interpolate.distance = d;\n  return interpolate;\n}", "map": {"version": 3, "names": ["asin", "atan2", "cos", "degrees", "haversin", "radians", "sin", "sqrt", "a", "b", "x0", "y0", "x1", "y1", "cy0", "sy0", "cy1", "sy1", "kx0", "ky0", "kx1", "ky1", "d", "k", "interpolate", "t", "B", "A", "x", "y", "z", "distance"], "sources": ["C:/console/aava-ui-web/node_modules/d3-geo/src/interpolate.js"], "sourcesContent": ["import {asin, atan2, cos, degrees, haversin, radians, sin, sqrt} from \"./math.js\";\n\nexport default function(a, b) {\n  var x0 = a[0] * radians,\n      y0 = a[1] * radians,\n      x1 = b[0] * radians,\n      y1 = b[1] * radians,\n      cy0 = cos(y0),\n      sy0 = sin(y0),\n      cy1 = cos(y1),\n      sy1 = sin(y1),\n      kx0 = cy0 * cos(x0),\n      ky0 = cy0 * sin(x0),\n      kx1 = cy1 * cos(x1),\n      ky1 = cy1 * sin(x1),\n      d = 2 * asin(sqrt(haversin(y1 - y0) + cy0 * cy1 * haversin(x1 - x0))),\n      k = sin(d);\n\n  var interpolate = d ? function(t) {\n    var B = sin(t *= d) / k,\n        A = sin(d - t) / k,\n        x = A * kx0 + B * kx1,\n        y = A * ky0 + B * ky1,\n        z = A * sy0 + B * sy1;\n    return [\n      atan2(y, x) * degrees,\n      atan2(z, sqrt(x * x + y * y)) * degrees\n    ];\n  } : function() {\n    return [x0 * degrees, y0 * degrees];\n  };\n\n  interpolate.distance = d;\n\n  return interpolate;\n}\n"], "mappings": "AAAA,SAAQA,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,GAAG,EAAEC,IAAI,QAAO,WAAW;AAEjF,eAAe,UAASC,CAAC,EAAEC,CAAC,EAAE;EAC5B,IAAIC,EAAE,GAAGF,CAAC,CAAC,CAAC,CAAC,GAAGH,OAAO;IACnBM,EAAE,GAAGH,CAAC,CAAC,CAAC,CAAC,GAAGH,OAAO;IACnBO,EAAE,GAAGH,CAAC,CAAC,CAAC,CAAC,GAAGJ,OAAO;IACnBQ,EAAE,GAAGJ,CAAC,CAAC,CAAC,CAAC,GAAGJ,OAAO;IACnBS,GAAG,GAAGZ,GAAG,CAACS,EAAE,CAAC;IACbI,GAAG,GAAGT,GAAG,CAACK,EAAE,CAAC;IACbK,GAAG,GAAGd,GAAG,CAACW,EAAE,CAAC;IACbI,GAAG,GAAGX,GAAG,CAACO,EAAE,CAAC;IACbK,GAAG,GAAGJ,GAAG,GAAGZ,GAAG,CAACQ,EAAE,CAAC;IACnBS,GAAG,GAAGL,GAAG,GAAGR,GAAG,CAACI,EAAE,CAAC;IACnBU,GAAG,GAAGJ,GAAG,GAAGd,GAAG,CAACU,EAAE,CAAC;IACnBS,GAAG,GAAGL,GAAG,GAAGV,GAAG,CAACM,EAAE,CAAC;IACnBU,CAAC,GAAG,CAAC,GAAGtB,IAAI,CAACO,IAAI,CAACH,QAAQ,CAACS,EAAE,GAAGF,EAAE,CAAC,GAAGG,GAAG,GAAGE,GAAG,GAAGZ,QAAQ,CAACQ,EAAE,GAAGF,EAAE,CAAC,CAAC,CAAC;IACrEa,CAAC,GAAGjB,GAAG,CAACgB,CAAC,CAAC;EAEd,IAAIE,WAAW,GAAGF,CAAC,GAAG,UAASG,CAAC,EAAE;IAChC,IAAIC,CAAC,GAAGpB,GAAG,CAACmB,CAAC,IAAIH,CAAC,CAAC,GAAGC,CAAC;MACnBI,CAAC,GAAGrB,GAAG,CAACgB,CAAC,GAAGG,CAAC,CAAC,GAAGF,CAAC;MAClBK,CAAC,GAAGD,CAAC,GAAGT,GAAG,GAAGQ,CAAC,GAAGN,GAAG;MACrBS,CAAC,GAAGF,CAAC,GAAGR,GAAG,GAAGO,CAAC,GAAGL,GAAG;MACrBS,CAAC,GAAGH,CAAC,GAAGZ,GAAG,GAAGW,CAAC,GAAGT,GAAG;IACzB,OAAO,CACLhB,KAAK,CAAC4B,CAAC,EAAED,CAAC,CAAC,GAAGzB,OAAO,EACrBF,KAAK,CAAC6B,CAAC,EAAEvB,IAAI,CAACqB,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGA,CAAC,CAAC,CAAC,GAAG1B,OAAO,CACxC;EACH,CAAC,GAAG,YAAW;IACb,OAAO,CAACO,EAAE,GAAGP,OAAO,EAAEQ,EAAE,GAAGR,OAAO,CAAC;EACrC,CAAC;EAEDqB,WAAW,CAACO,QAAQ,GAAGT,CAAC;EAExB,OAAOE,WAAW;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}