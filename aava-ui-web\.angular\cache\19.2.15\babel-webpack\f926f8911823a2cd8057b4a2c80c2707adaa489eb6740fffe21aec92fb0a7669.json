{"ast": null, "code": "import clipAntimeridian from \"../clip/antimeridian.js\";\nimport clipCircle from \"../clip/circle.js\";\nimport clipRectangle from \"../clip/rectangle.js\";\nimport compose from \"../compose.js\";\nimport identity from \"../identity.js\";\nimport { cos, degrees, radians, sin, sqrt } from \"../math.js\";\nimport { rotateRadians } from \"../rotation.js\";\nimport { transformer } from \"../transform.js\";\nimport { fitExtent, fitSize, fitWidth, fitHeight } from \"./fit.js\";\nimport resample from \"./resample.js\";\nvar transformRadians = transformer({\n  point: function (x, y) {\n    this.stream.point(x * radians, y * radians);\n  }\n});\nfunction transformRotate(rotate) {\n  return transformer({\n    point: function (x, y) {\n      var r = rotate(x, y);\n      return this.stream.point(r[0], r[1]);\n    }\n  });\n}\nfunction scaleTranslate(k, dx, dy, sx, sy) {\n  function transform(x, y) {\n    x *= sx;\n    y *= sy;\n    return [dx + k * x, dy - k * y];\n  }\n  transform.invert = function (x, y) {\n    return [(x - dx) / k * sx, (dy - y) / k * sy];\n  };\n  return transform;\n}\nfunction scaleTranslateRotate(k, dx, dy, sx, sy, alpha) {\n  if (!alpha) return scaleTranslate(k, dx, dy, sx, sy);\n  var cosAlpha = cos(alpha),\n    sinAlpha = sin(alpha),\n    a = cosAlpha * k,\n    b = sinAlpha * k,\n    ai = cosAlpha / k,\n    bi = sinAlpha / k,\n    ci = (sinAlpha * dy - cosAlpha * dx) / k,\n    fi = (sinAlpha * dx + cosAlpha * dy) / k;\n  function transform(x, y) {\n    x *= sx;\n    y *= sy;\n    return [a * x - b * y + dx, dy - b * x - a * y];\n  }\n  transform.invert = function (x, y) {\n    return [sx * (ai * x - bi * y + ci), sy * (fi - bi * x - ai * y)];\n  };\n  return transform;\n}\nexport default function projection(project) {\n  return projectionMutator(function () {\n    return project;\n  })();\n}\nexport function projectionMutator(projectAt) {\n  var project,\n    k = 150,\n    // scale\n    x = 480,\n    y = 250,\n    // translate\n    lambda = 0,\n    phi = 0,\n    // center\n    deltaLambda = 0,\n    deltaPhi = 0,\n    deltaGamma = 0,\n    rotate,\n    // pre-rotate\n    alpha = 0,\n    // post-rotate angle\n    sx = 1,\n    // reflectX\n    sy = 1,\n    // reflectX\n    theta = null,\n    preclip = clipAntimeridian,\n    // pre-clip angle\n    x0 = null,\n    y0,\n    x1,\n    y1,\n    postclip = identity,\n    // post-clip extent\n    delta2 = 0.5,\n    // precision\n    projectResample,\n    projectTransform,\n    projectRotateTransform,\n    cache,\n    cacheStream;\n  function projection(point) {\n    return projectRotateTransform(point[0] * radians, point[1] * radians);\n  }\n  function invert(point) {\n    point = projectRotateTransform.invert(point[0], point[1]);\n    return point && [point[0] * degrees, point[1] * degrees];\n  }\n  projection.stream = function (stream) {\n    return cache && cacheStream === stream ? cache : cache = transformRadians(transformRotate(rotate)(preclip(projectResample(postclip(cacheStream = stream)))));\n  };\n  projection.preclip = function (_) {\n    return arguments.length ? (preclip = _, theta = undefined, reset()) : preclip;\n  };\n  projection.postclip = function (_) {\n    return arguments.length ? (postclip = _, x0 = y0 = x1 = y1 = null, reset()) : postclip;\n  };\n  projection.clipAngle = function (_) {\n    return arguments.length ? (preclip = +_ ? clipCircle(theta = _ * radians) : (theta = null, clipAntimeridian), reset()) : theta * degrees;\n  };\n  projection.clipExtent = function (_) {\n    return arguments.length ? (postclip = _ == null ? (x0 = y0 = x1 = y1 = null, identity) : clipRectangle(x0 = +_[0][0], y0 = +_[0][1], x1 = +_[1][0], y1 = +_[1][1]), reset()) : x0 == null ? null : [[x0, y0], [x1, y1]];\n  };\n  projection.scale = function (_) {\n    return arguments.length ? (k = +_, recenter()) : k;\n  };\n  projection.translate = function (_) {\n    return arguments.length ? (x = +_[0], y = +_[1], recenter()) : [x, y];\n  };\n  projection.center = function (_) {\n    return arguments.length ? (lambda = _[0] % 360 * radians, phi = _[1] % 360 * radians, recenter()) : [lambda * degrees, phi * degrees];\n  };\n  projection.rotate = function (_) {\n    return arguments.length ? (deltaLambda = _[0] % 360 * radians, deltaPhi = _[1] % 360 * radians, deltaGamma = _.length > 2 ? _[2] % 360 * radians : 0, recenter()) : [deltaLambda * degrees, deltaPhi * degrees, deltaGamma * degrees];\n  };\n  projection.angle = function (_) {\n    return arguments.length ? (alpha = _ % 360 * radians, recenter()) : alpha * degrees;\n  };\n  projection.reflectX = function (_) {\n    return arguments.length ? (sx = _ ? -1 : 1, recenter()) : sx < 0;\n  };\n  projection.reflectY = function (_) {\n    return arguments.length ? (sy = _ ? -1 : 1, recenter()) : sy < 0;\n  };\n  projection.precision = function (_) {\n    return arguments.length ? (projectResample = resample(projectTransform, delta2 = _ * _), reset()) : sqrt(delta2);\n  };\n  projection.fitExtent = function (extent, object) {\n    return fitExtent(projection, extent, object);\n  };\n  projection.fitSize = function (size, object) {\n    return fitSize(projection, size, object);\n  };\n  projection.fitWidth = function (width, object) {\n    return fitWidth(projection, width, object);\n  };\n  projection.fitHeight = function (height, object) {\n    return fitHeight(projection, height, object);\n  };\n  function recenter() {\n    var center = scaleTranslateRotate(k, 0, 0, sx, sy, alpha).apply(null, project(lambda, phi)),\n      transform = scaleTranslateRotate(k, x - center[0], y - center[1], sx, sy, alpha);\n    rotate = rotateRadians(deltaLambda, deltaPhi, deltaGamma);\n    projectTransform = compose(project, transform);\n    projectRotateTransform = compose(rotate, projectTransform);\n    projectResample = resample(projectTransform, delta2);\n    return reset();\n  }\n  function reset() {\n    cache = cacheStream = null;\n    return projection;\n  }\n  return function () {\n    project = projectAt.apply(this, arguments);\n    projection.invert = project.invert && invert;\n    return recenter();\n  };\n}", "map": {"version": 3, "names": ["clipAntimeridian", "clipCircle", "clipRectangle", "compose", "identity", "cos", "degrees", "radians", "sin", "sqrt", "rotateRadians", "transformer", "fitExtent", "fitSize", "fit<PERSON><PERSON><PERSON>", "fitHeight", "resample", "transformRadians", "point", "x", "y", "stream", "transformRotate", "rotate", "r", "scaleTranslate", "k", "dx", "dy", "sx", "sy", "transform", "invert", "scaleTranslateRotate", "alpha", "cosAlpha", "sinAlpha", "a", "b", "ai", "bi", "ci", "fi", "projection", "project", "projectionMutator", "projectAt", "lambda", "phi", "deltaLambda", "deltaPhi", "deltaGamma", "theta", "preclip", "x0", "y0", "x1", "y1", "postclip", "delta2", "projectResample", "projectTransform", "projectRotateTransform", "cache", "cacheStream", "_", "arguments", "length", "undefined", "reset", "clipAngle", "clipExtent", "scale", "recenter", "translate", "center", "angle", "reflectX", "reflectY", "precision", "extent", "object", "size", "width", "height", "apply"], "sources": ["C:/console/aava-ui-web/node_modules/d3-geo/src/projection/index.js"], "sourcesContent": ["import clipAntimeridian from \"../clip/antimeridian.js\";\nimport clipCircle from \"../clip/circle.js\";\nimport clipRectangle from \"../clip/rectangle.js\";\nimport compose from \"../compose.js\";\nimport identity from \"../identity.js\";\nimport {cos, degrees, radians, sin, sqrt} from \"../math.js\";\nimport {rotateRadians} from \"../rotation.js\";\nimport {transformer} from \"../transform.js\";\nimport {fitExtent, fitSize, fitWidth, fitHeight} from \"./fit.js\";\nimport resample from \"./resample.js\";\n\nvar transformRadians = transformer({\n  point: function(x, y) {\n    this.stream.point(x * radians, y * radians);\n  }\n});\n\nfunction transformRotate(rotate) {\n  return transformer({\n    point: function(x, y) {\n      var r = rotate(x, y);\n      return this.stream.point(r[0], r[1]);\n    }\n  });\n}\n\nfunction scaleTranslate(k, dx, dy, sx, sy) {\n  function transform(x, y) {\n    x *= sx; y *= sy;\n    return [dx + k * x, dy - k * y];\n  }\n  transform.invert = function(x, y) {\n    return [(x - dx) / k * sx, (dy - y) / k * sy];\n  };\n  return transform;\n}\n\nfunction scaleTranslateRotate(k, dx, dy, sx, sy, alpha) {\n  if (!alpha) return scaleTranslate(k, dx, dy, sx, sy);\n  var cosAlpha = cos(alpha),\n      sinAlpha = sin(alpha),\n      a = cosAlpha * k,\n      b = sinAlpha * k,\n      ai = cosAlpha / k,\n      bi = sinAlpha / k,\n      ci = (sinAlpha * dy - cosAlpha * dx) / k,\n      fi = (sinAlpha * dx + cosAlpha * dy) / k;\n  function transform(x, y) {\n    x *= sx; y *= sy;\n    return [a * x - b * y + dx, dy - b * x - a * y];\n  }\n  transform.invert = function(x, y) {\n    return [sx * (ai * x - bi * y + ci), sy * (fi - bi * x - ai * y)];\n  };\n  return transform;\n}\n\nexport default function projection(project) {\n  return projectionMutator(function() { return project; })();\n}\n\nexport function projectionMutator(projectAt) {\n  var project,\n      k = 150, // scale\n      x = 480, y = 250, // translate\n      lambda = 0, phi = 0, // center\n      deltaLambda = 0, deltaPhi = 0, deltaGamma = 0, rotate, // pre-rotate\n      alpha = 0, // post-rotate angle\n      sx = 1, // reflectX\n      sy = 1, // reflectX\n      theta = null, preclip = clipAntimeridian, // pre-clip angle\n      x0 = null, y0, x1, y1, postclip = identity, // post-clip extent\n      delta2 = 0.5, // precision\n      projectResample,\n      projectTransform,\n      projectRotateTransform,\n      cache,\n      cacheStream;\n\n  function projection(point) {\n    return projectRotateTransform(point[0] * radians, point[1] * radians);\n  }\n\n  function invert(point) {\n    point = projectRotateTransform.invert(point[0], point[1]);\n    return point && [point[0] * degrees, point[1] * degrees];\n  }\n\n  projection.stream = function(stream) {\n    return cache && cacheStream === stream ? cache : cache = transformRadians(transformRotate(rotate)(preclip(projectResample(postclip(cacheStream = stream)))));\n  };\n\n  projection.preclip = function(_) {\n    return arguments.length ? (preclip = _, theta = undefined, reset()) : preclip;\n  };\n\n  projection.postclip = function(_) {\n    return arguments.length ? (postclip = _, x0 = y0 = x1 = y1 = null, reset()) : postclip;\n  };\n\n  projection.clipAngle = function(_) {\n    return arguments.length ? (preclip = +_ ? clipCircle(theta = _ * radians) : (theta = null, clipAntimeridian), reset()) : theta * degrees;\n  };\n\n  projection.clipExtent = function(_) {\n    return arguments.length ? (postclip = _ == null ? (x0 = y0 = x1 = y1 = null, identity) : clipRectangle(x0 = +_[0][0], y0 = +_[0][1], x1 = +_[1][0], y1 = +_[1][1]), reset()) : x0 == null ? null : [[x0, y0], [x1, y1]];\n  };\n\n  projection.scale = function(_) {\n    return arguments.length ? (k = +_, recenter()) : k;\n  };\n\n  projection.translate = function(_) {\n    return arguments.length ? (x = +_[0], y = +_[1], recenter()) : [x, y];\n  };\n\n  projection.center = function(_) {\n    return arguments.length ? (lambda = _[0] % 360 * radians, phi = _[1] % 360 * radians, recenter()) : [lambda * degrees, phi * degrees];\n  };\n\n  projection.rotate = function(_) {\n    return arguments.length ? (deltaLambda = _[0] % 360 * radians, deltaPhi = _[1] % 360 * radians, deltaGamma = _.length > 2 ? _[2] % 360 * radians : 0, recenter()) : [deltaLambda * degrees, deltaPhi * degrees, deltaGamma * degrees];\n  };\n\n  projection.angle = function(_) {\n    return arguments.length ? (alpha = _ % 360 * radians, recenter()) : alpha * degrees;\n  };\n\n  projection.reflectX = function(_) {\n    return arguments.length ? (sx = _ ? -1 : 1, recenter()) : sx < 0;\n  };\n\n  projection.reflectY = function(_) {\n    return arguments.length ? (sy = _ ? -1 : 1, recenter()) : sy < 0;\n  };\n\n  projection.precision = function(_) {\n    return arguments.length ? (projectResample = resample(projectTransform, delta2 = _ * _), reset()) : sqrt(delta2);\n  };\n\n  projection.fitExtent = function(extent, object) {\n    return fitExtent(projection, extent, object);\n  };\n\n  projection.fitSize = function(size, object) {\n    return fitSize(projection, size, object);\n  };\n\n  projection.fitWidth = function(width, object) {\n    return fitWidth(projection, width, object);\n  };\n\n  projection.fitHeight = function(height, object) {\n    return fitHeight(projection, height, object);\n  };\n\n  function recenter() {\n    var center = scaleTranslateRotate(k, 0, 0, sx, sy, alpha).apply(null, project(lambda, phi)),\n        transform = scaleTranslateRotate(k, x - center[0], y - center[1], sx, sy, alpha);\n    rotate = rotateRadians(deltaLambda, deltaPhi, deltaGamma);\n    projectTransform = compose(project, transform);\n    projectRotateTransform = compose(rotate, projectTransform);\n    projectResample = resample(projectTransform, delta2);\n    return reset();\n  }\n\n  function reset() {\n    cache = cacheStream = null;\n    return projection;\n  }\n\n  return function() {\n    project = projectAt.apply(this, arguments);\n    projection.invert = project.invert && invert;\n    return recenter();\n  };\n}\n"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,yBAAyB;AACtD,OAAOC,UAAU,MAAM,mBAAmB;AAC1C,OAAOC,aAAa,MAAM,sBAAsB;AAChD,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,SAAQC,GAAG,EAAEC,OAAO,EAAEC,OAAO,EAAEC,GAAG,EAAEC,IAAI,QAAO,YAAY;AAC3D,SAAQC,aAAa,QAAO,gBAAgB;AAC5C,SAAQC,WAAW,QAAO,iBAAiB;AAC3C,SAAQC,SAAS,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,SAAS,QAAO,UAAU;AAChE,OAAOC,QAAQ,MAAM,eAAe;AAEpC,IAAIC,gBAAgB,GAAGN,WAAW,CAAC;EACjCO,KAAK,EAAE,SAAAA,CAASC,CAAC,EAAEC,CAAC,EAAE;IACpB,IAAI,CAACC,MAAM,CAACH,KAAK,CAACC,CAAC,GAAGZ,OAAO,EAAEa,CAAC,GAAGb,OAAO,CAAC;EAC7C;AACF,CAAC,CAAC;AAEF,SAASe,eAAeA,CAACC,MAAM,EAAE;EAC/B,OAAOZ,WAAW,CAAC;IACjBO,KAAK,EAAE,SAAAA,CAASC,CAAC,EAAEC,CAAC,EAAE;MACpB,IAAII,CAAC,GAAGD,MAAM,CAACJ,CAAC,EAAEC,CAAC,CAAC;MACpB,OAAO,IAAI,CAACC,MAAM,CAACH,KAAK,CAACM,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC;EACF,CAAC,CAAC;AACJ;AAEA,SAASC,cAAcA,CAACC,CAAC,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;EACzC,SAASC,SAASA,CAACZ,CAAC,EAAEC,CAAC,EAAE;IACvBD,CAAC,IAAIU,EAAE;IAAET,CAAC,IAAIU,EAAE;IAChB,OAAO,CAACH,EAAE,GAAGD,CAAC,GAAGP,CAAC,EAAES,EAAE,GAAGF,CAAC,GAAGN,CAAC,CAAC;EACjC;EACAW,SAAS,CAACC,MAAM,GAAG,UAASb,CAAC,EAAEC,CAAC,EAAE;IAChC,OAAO,CAAC,CAACD,CAAC,GAAGQ,EAAE,IAAID,CAAC,GAAGG,EAAE,EAAE,CAACD,EAAE,GAAGR,CAAC,IAAIM,CAAC,GAAGI,EAAE,CAAC;EAC/C,CAAC;EACD,OAAOC,SAAS;AAClB;AAEA,SAASE,oBAAoBA,CAACP,CAAC,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEI,KAAK,EAAE;EACtD,IAAI,CAACA,KAAK,EAAE,OAAOT,cAAc,CAACC,CAAC,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;EACpD,IAAIK,QAAQ,GAAG9B,GAAG,CAAC6B,KAAK,CAAC;IACrBE,QAAQ,GAAG5B,GAAG,CAAC0B,KAAK,CAAC;IACrBG,CAAC,GAAGF,QAAQ,GAAGT,CAAC;IAChBY,CAAC,GAAGF,QAAQ,GAAGV,CAAC;IAChBa,EAAE,GAAGJ,QAAQ,GAAGT,CAAC;IACjBc,EAAE,GAAGJ,QAAQ,GAAGV,CAAC;IACjBe,EAAE,GAAG,CAACL,QAAQ,GAAGR,EAAE,GAAGO,QAAQ,GAAGR,EAAE,IAAID,CAAC;IACxCgB,EAAE,GAAG,CAACN,QAAQ,GAAGT,EAAE,GAAGQ,QAAQ,GAAGP,EAAE,IAAIF,CAAC;EAC5C,SAASK,SAASA,CAACZ,CAAC,EAAEC,CAAC,EAAE;IACvBD,CAAC,IAAIU,EAAE;IAAET,CAAC,IAAIU,EAAE;IAChB,OAAO,CAACO,CAAC,GAAGlB,CAAC,GAAGmB,CAAC,GAAGlB,CAAC,GAAGO,EAAE,EAAEC,EAAE,GAAGU,CAAC,GAAGnB,CAAC,GAAGkB,CAAC,GAAGjB,CAAC,CAAC;EACjD;EACAW,SAAS,CAACC,MAAM,GAAG,UAASb,CAAC,EAAEC,CAAC,EAAE;IAChC,OAAO,CAACS,EAAE,IAAIU,EAAE,GAAGpB,CAAC,GAAGqB,EAAE,GAAGpB,CAAC,GAAGqB,EAAE,CAAC,EAAEX,EAAE,IAAIY,EAAE,GAAGF,EAAE,GAAGrB,CAAC,GAAGoB,EAAE,GAAGnB,CAAC,CAAC,CAAC;EACnE,CAAC;EACD,OAAOW,SAAS;AAClB;AAEA,eAAe,SAASY,UAAUA,CAACC,OAAO,EAAE;EAC1C,OAAOC,iBAAiB,CAAC,YAAW;IAAE,OAAOD,OAAO;EAAE,CAAC,CAAC,CAAC,CAAC;AAC5D;AAEA,OAAO,SAASC,iBAAiBA,CAACC,SAAS,EAAE;EAC3C,IAAIF,OAAO;IACPlB,CAAC,GAAG,GAAG;IAAE;IACTP,CAAC,GAAG,GAAG;IAAEC,CAAC,GAAG,GAAG;IAAE;IAClB2B,MAAM,GAAG,CAAC;IAAEC,GAAG,GAAG,CAAC;IAAE;IACrBC,WAAW,GAAG,CAAC;IAAEC,QAAQ,GAAG,CAAC;IAAEC,UAAU,GAAG,CAAC;IAAE5B,MAAM;IAAE;IACvDW,KAAK,GAAG,CAAC;IAAE;IACXL,EAAE,GAAG,CAAC;IAAE;IACRC,EAAE,GAAG,CAAC;IAAE;IACRsB,KAAK,GAAG,IAAI;IAAEC,OAAO,GAAGrD,gBAAgB;IAAE;IAC1CsD,EAAE,GAAG,IAAI;IAAEC,EAAE;IAAEC,EAAE;IAAEC,EAAE;IAAEC,QAAQ,GAAGtD,QAAQ;IAAE;IAC5CuD,MAAM,GAAG,GAAG;IAAE;IACdC,eAAe;IACfC,gBAAgB;IAChBC,sBAAsB;IACtBC,KAAK;IACLC,WAAW;EAEf,SAASrB,UAAUA,CAACzB,KAAK,EAAE;IACzB,OAAO4C,sBAAsB,CAAC5C,KAAK,CAAC,CAAC,CAAC,GAAGX,OAAO,EAAEW,KAAK,CAAC,CAAC,CAAC,GAAGX,OAAO,CAAC;EACvE;EAEA,SAASyB,MAAMA,CAACd,KAAK,EAAE;IACrBA,KAAK,GAAG4C,sBAAsB,CAAC9B,MAAM,CAACd,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC;IACzD,OAAOA,KAAK,IAAI,CAACA,KAAK,CAAC,CAAC,CAAC,GAAGZ,OAAO,EAAEY,KAAK,CAAC,CAAC,CAAC,GAAGZ,OAAO,CAAC;EAC1D;EAEAqC,UAAU,CAACtB,MAAM,GAAG,UAASA,MAAM,EAAE;IACnC,OAAO0C,KAAK,IAAIC,WAAW,KAAK3C,MAAM,GAAG0C,KAAK,GAAGA,KAAK,GAAG9C,gBAAgB,CAACK,eAAe,CAACC,MAAM,CAAC,CAAC8B,OAAO,CAACO,eAAe,CAACF,QAAQ,CAACM,WAAW,GAAG3C,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9J,CAAC;EAEDsB,UAAU,CAACU,OAAO,GAAG,UAASY,CAAC,EAAE;IAC/B,OAAOC,SAAS,CAACC,MAAM,IAAId,OAAO,GAAGY,CAAC,EAAEb,KAAK,GAAGgB,SAAS,EAAEC,KAAK,CAAC,CAAC,IAAIhB,OAAO;EAC/E,CAAC;EAEDV,UAAU,CAACe,QAAQ,GAAG,UAASO,CAAC,EAAE;IAChC,OAAOC,SAAS,CAACC,MAAM,IAAIT,QAAQ,GAAGO,CAAC,EAAEX,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAG,IAAI,EAAEY,KAAK,CAAC,CAAC,IAAIX,QAAQ;EACxF,CAAC;EAEDf,UAAU,CAAC2B,SAAS,GAAG,UAASL,CAAC,EAAE;IACjC,OAAOC,SAAS,CAACC,MAAM,IAAId,OAAO,GAAG,CAACY,CAAC,GAAGhE,UAAU,CAACmD,KAAK,GAAGa,CAAC,GAAG1D,OAAO,CAAC,IAAI6C,KAAK,GAAG,IAAI,EAAEpD,gBAAgB,CAAC,EAAEqE,KAAK,CAAC,CAAC,IAAIjB,KAAK,GAAG9C,OAAO;EAC1I,CAAC;EAEDqC,UAAU,CAAC4B,UAAU,GAAG,UAASN,CAAC,EAAE;IAClC,OAAOC,SAAS,CAACC,MAAM,IAAIT,QAAQ,GAAGO,CAAC,IAAI,IAAI,IAAIX,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAG,IAAI,EAAErD,QAAQ,IAAIF,aAAa,CAACoD,EAAE,GAAG,CAACW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEV,EAAE,GAAG,CAACU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAET,EAAE,GAAG,CAACS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAER,EAAE,GAAG,CAACQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEI,KAAK,CAAC,CAAC,IAAIf,EAAE,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,CAACA,EAAE,EAAEC,EAAE,CAAC,EAAE,CAACC,EAAE,EAAEC,EAAE,CAAC,CAAC;EACzN,CAAC;EAEDd,UAAU,CAAC6B,KAAK,GAAG,UAASP,CAAC,EAAE;IAC7B,OAAOC,SAAS,CAACC,MAAM,IAAIzC,CAAC,GAAG,CAACuC,CAAC,EAAEQ,QAAQ,CAAC,CAAC,IAAI/C,CAAC;EACpD,CAAC;EAEDiB,UAAU,CAAC+B,SAAS,GAAG,UAAST,CAAC,EAAE;IACjC,OAAOC,SAAS,CAACC,MAAM,IAAIhD,CAAC,GAAG,CAAC8C,CAAC,CAAC,CAAC,CAAC,EAAE7C,CAAC,GAAG,CAAC6C,CAAC,CAAC,CAAC,CAAC,EAAEQ,QAAQ,CAAC,CAAC,IAAI,CAACtD,CAAC,EAAEC,CAAC,CAAC;EACvE,CAAC;EAEDuB,UAAU,CAACgC,MAAM,GAAG,UAASV,CAAC,EAAE;IAC9B,OAAOC,SAAS,CAACC,MAAM,IAAIpB,MAAM,GAAGkB,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG1D,OAAO,EAAEyC,GAAG,GAAGiB,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG1D,OAAO,EAAEkE,QAAQ,CAAC,CAAC,IAAI,CAAC1B,MAAM,GAAGzC,OAAO,EAAE0C,GAAG,GAAG1C,OAAO,CAAC;EACvI,CAAC;EAEDqC,UAAU,CAACpB,MAAM,GAAG,UAAS0C,CAAC,EAAE;IAC9B,OAAOC,SAAS,CAACC,MAAM,IAAIlB,WAAW,GAAGgB,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG1D,OAAO,EAAE2C,QAAQ,GAAGe,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG1D,OAAO,EAAE4C,UAAU,GAAGc,CAAC,CAACE,MAAM,GAAG,CAAC,GAAGF,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG1D,OAAO,GAAG,CAAC,EAAEkE,QAAQ,CAAC,CAAC,IAAI,CAACxB,WAAW,GAAG3C,OAAO,EAAE4C,QAAQ,GAAG5C,OAAO,EAAE6C,UAAU,GAAG7C,OAAO,CAAC;EACvO,CAAC;EAEDqC,UAAU,CAACiC,KAAK,GAAG,UAASX,CAAC,EAAE;IAC7B,OAAOC,SAAS,CAACC,MAAM,IAAIjC,KAAK,GAAG+B,CAAC,GAAG,GAAG,GAAG1D,OAAO,EAAEkE,QAAQ,CAAC,CAAC,IAAIvC,KAAK,GAAG5B,OAAO;EACrF,CAAC;EAEDqC,UAAU,CAACkC,QAAQ,GAAG,UAASZ,CAAC,EAAE;IAChC,OAAOC,SAAS,CAACC,MAAM,IAAItC,EAAE,GAAGoC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAEQ,QAAQ,CAAC,CAAC,IAAI5C,EAAE,GAAG,CAAC;EAClE,CAAC;EAEDc,UAAU,CAACmC,QAAQ,GAAG,UAASb,CAAC,EAAE;IAChC,OAAOC,SAAS,CAACC,MAAM,IAAIrC,EAAE,GAAGmC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAEQ,QAAQ,CAAC,CAAC,IAAI3C,EAAE,GAAG,CAAC;EAClE,CAAC;EAEDa,UAAU,CAACoC,SAAS,GAAG,UAASd,CAAC,EAAE;IACjC,OAAOC,SAAS,CAACC,MAAM,IAAIP,eAAe,GAAG5C,QAAQ,CAAC6C,gBAAgB,EAAEF,MAAM,GAAGM,CAAC,GAAGA,CAAC,CAAC,EAAEI,KAAK,CAAC,CAAC,IAAI5D,IAAI,CAACkD,MAAM,CAAC;EAClH,CAAC;EAEDhB,UAAU,CAAC/B,SAAS,GAAG,UAASoE,MAAM,EAAEC,MAAM,EAAE;IAC9C,OAAOrE,SAAS,CAAC+B,UAAU,EAAEqC,MAAM,EAAEC,MAAM,CAAC;EAC9C,CAAC;EAEDtC,UAAU,CAAC9B,OAAO,GAAG,UAASqE,IAAI,EAAED,MAAM,EAAE;IAC1C,OAAOpE,OAAO,CAAC8B,UAAU,EAAEuC,IAAI,EAAED,MAAM,CAAC;EAC1C,CAAC;EAEDtC,UAAU,CAAC7B,QAAQ,GAAG,UAASqE,KAAK,EAAEF,MAAM,EAAE;IAC5C,OAAOnE,QAAQ,CAAC6B,UAAU,EAAEwC,KAAK,EAAEF,MAAM,CAAC;EAC5C,CAAC;EAEDtC,UAAU,CAAC5B,SAAS,GAAG,UAASqE,MAAM,EAAEH,MAAM,EAAE;IAC9C,OAAOlE,SAAS,CAAC4B,UAAU,EAAEyC,MAAM,EAAEH,MAAM,CAAC;EAC9C,CAAC;EAED,SAASR,QAAQA,CAAA,EAAG;IAClB,IAAIE,MAAM,GAAG1C,oBAAoB,CAACP,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEG,EAAE,EAAEC,EAAE,EAAEI,KAAK,CAAC,CAACmD,KAAK,CAAC,IAAI,EAAEzC,OAAO,CAACG,MAAM,EAAEC,GAAG,CAAC,CAAC;MACvFjB,SAAS,GAAGE,oBAAoB,CAACP,CAAC,EAAEP,CAAC,GAAGwD,MAAM,CAAC,CAAC,CAAC,EAAEvD,CAAC,GAAGuD,MAAM,CAAC,CAAC,CAAC,EAAE9C,EAAE,EAAEC,EAAE,EAAEI,KAAK,CAAC;IACpFX,MAAM,GAAGb,aAAa,CAACuC,WAAW,EAAEC,QAAQ,EAAEC,UAAU,CAAC;IACzDU,gBAAgB,GAAG1D,OAAO,CAACyC,OAAO,EAAEb,SAAS,CAAC;IAC9C+B,sBAAsB,GAAG3D,OAAO,CAACoB,MAAM,EAAEsC,gBAAgB,CAAC;IAC1DD,eAAe,GAAG5C,QAAQ,CAAC6C,gBAAgB,EAAEF,MAAM,CAAC;IACpD,OAAOU,KAAK,CAAC,CAAC;EAChB;EAEA,SAASA,KAAKA,CAAA,EAAG;IACfN,KAAK,GAAGC,WAAW,GAAG,IAAI;IAC1B,OAAOrB,UAAU;EACnB;EAEA,OAAO,YAAW;IAChBC,OAAO,GAAGE,SAAS,CAACuC,KAAK,CAAC,IAAI,EAAEnB,SAAS,CAAC;IAC1CvB,UAAU,CAACX,MAAM,GAAGY,OAAO,CAACZ,MAAM,IAAIA,MAAM;IAC5C,OAAOyC,QAAQ,CAAC,CAAC;EACnB,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}