{"ast": null, "code": "export default function (series) {\n  var n = series.length,\n    o = new Array(n);\n  while (--n >= 0) o[n] = n;\n  return o;\n}", "map": {"version": 3, "names": ["series", "n", "length", "o", "Array"], "sources": ["C:/console/aava-ui-web/node_modules/d3-shape/src/order/none.js"], "sourcesContent": ["export default function(series) {\n  var n = series.length, o = new Array(n);\n  while (--n >= 0) o[n] = n;\n  return o;\n}\n"], "mappings": "AAAA,eAAe,UAASA,MAAM,EAAE;EAC9B,IAAIC,CAAC,GAAGD,MAAM,CAACE,MAAM;IAAEC,CAAC,GAAG,IAAIC,KAAK,CAACH,CAAC,CAAC;EACvC,OAAO,EAAEA,CAAC,IAAI,CAAC,EAAEE,CAAC,CAACF,CAAC,CAAC,GAAGA,CAAC;EACzB,OAAOE,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}