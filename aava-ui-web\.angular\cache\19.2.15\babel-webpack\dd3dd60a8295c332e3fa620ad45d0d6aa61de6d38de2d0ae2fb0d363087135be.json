{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { CommonModule, formatDate } from '@angular/common';\nimport { Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { ApprovalCardComponent, IconComponent, AvaTextboxComponent, PopupComponent, ConfirmationPopupComponent, AvaTagComponent, ButtonComponent } from '@ava/play-comp-library';\nimport approvalText from '../constants/approval.json';\nimport { debounceTime, distinctUntilChanged, map, startWith } from 'rxjs';\nimport { AgentsPreviewPanelComponent } from './agents-preview-panel/agents-preview-panel.component';\nimport { ApprovalTxtCardComponent } from '../approval-text-card/approval-text-card.component';\nlet ApprovalAgentsComponent = class ApprovalAgentsComponent {\n  router;\n  apiService;\n  approvalService;\n  fb;\n  drawerService;\n  agentService;\n  dialogService;\n  appLabels = approvalText.labels;\n  searchValue = '';\n  totalApprovedApprovals = 20;\n  totalPendingApprovals = 15;\n  totalApprovals = 60;\n  isBasicCollapsed = false;\n  quickActionsExpanded = true;\n  consoleApproval = {};\n  options = [];\n  basicSidebarItems = [];\n  quickActions = [];\n  toolReviews = [];\n  workflowReviews = [];\n  filteredAgentsReviews = [];\n  agentsReviews = [];\n  currentToolsPage = 1;\n  currentAgentsPage = 1;\n  currentWorkflowsPage = 1;\n  pageSize = 50;\n  totalRecords = 0;\n  isDeleted = false;\n  currentTab = 'Agents';\n  showToolApprovalPopup = false;\n  showInfoPopup = false;\n  showErrorPopup = false;\n  infoMessage = '';\n  selectedIndex = 0;\n  showFeedbackPopup = false;\n  searchForm;\n  labels = approvalText.labels;\n  approvedAgentId = null;\n  previewData = null;\n  selectedAgentId = 0;\n  constructor(router, apiService, approvalService, fb, drawerService, agentService, dialogService) {\n    this.router = router;\n    this.apiService = apiService;\n    this.approvalService = approvalService;\n    this.fb = fb;\n    this.drawerService = drawerService;\n    this.agentService = agentService;\n    this.dialogService = dialogService;\n    this.labels = approvalText.labels;\n    this.options = [{\n      name: this.labels.electronics,\n      value: 'electronics'\n    }, {\n      name: this.labels.clothing,\n      value: 'clothing'\n    }, {\n      name: this.labels.books,\n      value: 'books'\n    }];\n    this.basicSidebarItems = [{\n      id: '1',\n      icon: 'hammer',\n      text: this.labels.agents,\n      route: '',\n      active: true\n    }, {\n      id: '2',\n      icon: 'circle-check',\n      text: this.labels.workflows,\n      route: ''\n    }, {\n      id: '3',\n      icon: 'bot',\n      text: this.labels.tools,\n      route: ''\n    }];\n    this.quickActions = [{\n      icon: 'awe_agents',\n      label: this.labels.agents,\n      route: ''\n    }, {\n      icon: 'awe_workflows',\n      label: this.labels.workflows,\n      route: ''\n    }, {\n      icon: 'awe_tools',\n      label: this.labels.tools,\n      route: ''\n    }];\n    this.searchForm = this.fb.group({\n      search: ['']\n    });\n  }\n  ngOnInit() {\n    this.searchList();\n    this.totalApprovals = 60;\n    this.loadAgentsReviews();\n  }\n  searchList() {\n    console.log(this.searchForm.get('search')?.value);\n    this.searchForm.get('search').valueChanges.pipe(startWith(''), debounceTime(300), distinctUntilChanged(), map(value => value?.toLowerCase() ?? '')).subscribe(searchText => {\n      this.applyFilter(searchText);\n    });\n  }\n  applyFilter(text) {\n    const lower = text;\n    if (!text) {\n      this.updateConsoleApproval(this.agentsReviews, 'agent');\n      return;\n    }\n    this.filteredAgentsReviews = this.agentsReviews.filter(item => item.agentName?.toLowerCase().includes(lower));\n    this.updateConsoleApproval(this.filteredAgentsReviews, 'agent');\n  }\n  onSelectionChange(data) {\n    console.log('Selection changed:', data);\n  }\n  uClick(i) {\n    console.log('log' + i);\n  }\n  toggleQuickActions() {\n    this.quickActionsExpanded = !this.quickActionsExpanded;\n  }\n  onBasicCollapseToggle(isCollapsed) {\n    this.isBasicCollapsed = isCollapsed;\n    console.log('Basic sidebar collapsed:', isCollapsed);\n  }\n  onBasicItemClick(item) {\n    this.basicSidebarItems.forEach(i => i.active = false);\n    item.active = true;\n    console.log(item);\n  }\n  toRequestStatus(value) {\n    return value === 'approved' || value === 'rejected' || value === 'review' ? value : 'review';\n  }\n  loadAgentsReviews() {\n    this.approvalService.getAllReviewAgents(this.currentAgentsPage, this.pageSize, this.isDeleted).subscribe(response => {\n      if (this.currentAgentsPage > 1) {\n        this.agentsReviews = [...this.agentsReviews, ...response.agentReviewDetails];\n      } else {\n        this.agentsReviews = response?.agentReviewDetails;\n      }\n      this.filteredAgentsReviews = this.agentsReviews;\n      this.agentsReviews = this.agentsReviews.filter(r => r.status !== 'approved');\n      // console.log('agents reviews ', this.agentsReviews);\n      this.totalRecords = this.agentsReviews.length;\n      this.updateConsoleApproval(this.agentsReviews, 'agent');\n    });\n  }\n  loadMoreAgents(page) {\n    this.currentAgentsPage = page;\n    this.loadAgentsReviews();\n  }\n  loadReviews(name) {\n    this.loadAgentsReviews();\n  }\n  rejectApproval(idx) {\n    console.log(idx);\n    this.selectedIndex = idx;\n    // this.showFeedbackPopup = true;\n  }\n  approveApproval(idx) {\n    console.log(idx);\n    this.selectedIndex = idx;\n    console.log(this.filteredAgentsReviews[this.selectedIndex]);\n    // this.showToolApprovalPopup = true;\n  }\n  handleApproval() {\n    this.handleAgentApproval();\n  }\n  handleRejection(feedback) {\n    console.log(\"Clicked on confirmation popup\");\n    this.handleAgentRejection(feedback);\n  }\n  onCardClick(index) {\n    console.log('Selected card index:', index);\n    this.selectedIndex = index;\n    const selectedAgent = this.filteredAgentsReviews[this.selectedIndex];\n    this.selectedAgentId = selectedAgent.agentId;\n    this.loadPreviewData(selectedAgent);\n    this.drawerService.open(AgentsPreviewPanelComponent, {\n      previewData: this.previewData,\n      closePreview: () => this.drawerService.clear(),\n      editAgent: () => this.handleEditAgent(selectedAgent.agentId),\n      rejectApproval: () => this.handeMetaDataSendback(),\n      approveApproval: () => this.handleMetaDataApproval(),\n      testApproval: () => this.redirectToAgentsPlayground()\n    });\n    console.log(selectedAgent);\n  }\n  handleMetaDataApproval() {\n    this.showToolApprovalPopup = true;\n  }\n  handeMetaDataSendback() {\n    // this.showFeedbackPopup = true;\n    this.dialogService.confirmation({\n      title: 'Confirm Send Back?',\n      message: 'Are you sure you want to send back this agent for corrections and modification?',\n      confirmButtonText: 'Send Back',\n      cancelButtonText: 'Cancel',\n      confirmButtonVariant: 'danger',\n      cancelButtonVariant: 'primary',\n      destructive: true\n    }).then(result => {\n      console.log('Confirmation dialog closed:', result);\n      if (result.confirmed === true) {\n        this.handleAgentRejection(\"\");\n      } else if (result.confirmed === false) {\n        console.log('User cancelled deletion! Account is safe.');\n      }\n    });\n  }\n  handleEditAgent(agentId) {\n    console.log('Edit Agent', agentId);\n    this.drawerService.clear();\n    this.router.navigate(['/build/agents/collaborative'], {\n      queryParams: {\n        id: agentId,\n        mode: 'edit'\n      }\n    });\n  }\n  handleAgentApproval() {\n    const agentDetails = this.filteredAgentsReviews[this.selectedIndex];\n    const id = agentDetails.id;\n    const agentId = agentDetails.agentId;\n    const status = 'approved';\n    const reviewedBy = agentDetails.reviewedBy;\n    this.approvalService.approveAgent(id, agentId, status, reviewedBy).subscribe({\n      next: response => {\n        this.infoMessage = response?.message || this.labels.agentSuccessApproveMessage;\n        this.showInfoPopup = true;\n        // Store agent ID for navigation after popup confirmation\n        this.approvedAgentId = agentId;\n      },\n      error: error => {\n        this.showErrorPopup = true;\n        this.infoMessage = error?.error?.message || this.labels.defaultErrorMessage;\n        console.error('Error:', error);\n      }\n    });\n  }\n  loadPreviewData(selectedAgent) {\n    this.previewData = {\n      type: 'agent',\n      title: selectedAgent.agentName,\n      data: selectedAgent,\n      loading: true,\n      error: null\n    };\n    console.log('Load preview data', selectedAgent.agentId);\n    this.agentService.getCollaborativeAgentDetailsById(selectedAgent.agentId).subscribe({\n      next: response => {\n        // console.log('Collaborative agent details', response);\n        this.previewData.data = response.agentDetail;\n        this.previewData.loading = false;\n        this.previewData.error = null;\n      },\n      error: error => {\n        console.error('Error:', error);\n      }\n    });\n  }\n  handleAgentRejection(feedback) {\n    const agentDetails = this.filteredAgentsReviews[this.selectedIndex];\n    const id = agentDetails.id;\n    const agentId = agentDetails.agentId;\n    const status = 'rejected';\n    const reviewedBy = agentDetails.reviewedBy;\n    const message = feedback;\n    this.approvalService.rejectAgent(id, agentId, status, reviewedBy, message).subscribe({\n      next: response => {\n        this.infoMessage = response?.message || this.labels.agentSuccessRejectMessage;\n        this.showInfoPopup = true;\n      },\n      error: error => {\n        this.showErrorPopup = true;\n        this.infoMessage = error?.error?.message || this.labels.defaultErrorMessage;\n        console.error('Error:', error);\n      }\n    });\n  }\n  handleInfoPopup() {\n    this.showInfoPopup = false;\n    this.loadAgentsReviews();\n  }\n  handleTesting(index) {\n    this.selectedAgentId = this.filteredAgentsReviews[index].agentId;\n    console.log('Selected agent id', this.selectedAgentId);\n  }\n  redirectToAgentsPlayground() {\n    this.router.navigate([`/build/agents/collaborative/execute`], {\n      queryParams: {\n        id: this.selectedAgentId\n      }\n    });\n  }\n  updateConsoleApproval(data, type) {\n    this.consoleApproval = {\n      contents: data?.map(req => {\n        const statusIcons = {\n          approved: 'circle-check-big',\n          rejected: 'circle-x',\n          review: 'clock'\n        };\n        const statusTexts = {\n          approved: this.labels.approved,\n          rejected: this.labels.rejected,\n          review: this.labels.review\n        };\n        const statusKey = this.toRequestStatus(req?.status);\n        const specificId = req.agentId;\n        const title = req.agentName;\n        return {\n          id: req.id,\n          refId: specificId,\n          type: type,\n          session1: {\n            title: title,\n            labels: [{\n              name: type,\n              color: 'success',\n              background: 'red',\n              type: 'normal'\n            }, {\n              name: req.changeRequestType,\n              color: req.changeRequestType === 'update' ? 'error' : 'info',\n              background: 'red',\n              type: 'pill'\n            }]\n          },\n          session2: [{\n            name: type,\n            color: 'default',\n            background: 'red',\n            type: 'normal'\n          }, {\n            name: req.status,\n            color: 'default',\n            background: 'red',\n            type: 'normal'\n          }],\n          session3: [{\n            iconName: 'user',\n            label: req.requestedBy\n          }, {\n            iconName: 'calendar-days',\n            label: formatDate(req?.requestedAt, 'dd MMM yyyy', 'en-IN')\n          }],\n          session4: {\n            status: statusTexts[statusKey],\n            iconName: statusIcons[statusKey]\n          }\n        };\n      }),\n      footer: {}\n    };\n  }\n};\nApprovalAgentsComponent = __decorate([Component({\n  selector: 'app-approval-agents',\n  imports: [CommonModule, RouterModule, ApprovalCardComponent, IconComponent, AvaTextboxComponent, ReactiveFormsModule, AvaTagComponent, ButtonComponent, PopupComponent, ConfirmationPopupComponent, ApprovalTxtCardComponent],\n  schemas: [CUSTOM_ELEMENTS_SCHEMA],\n  templateUrl: './approval-agents.component.html',\n  styleUrls: ['./approval-agents.component.scss']\n})], ApprovalAgentsComponent);\nexport { ApprovalAgentsComponent };", "map": {"version": 3, "names": ["CommonModule", "formatDate", "Component", "CUSTOM_ELEMENTS_SCHEMA", "ReactiveFormsModule", "RouterModule", "ApprovalCardComponent", "IconComponent", "AvaTextboxComponent", "PopupComponent", "ConfirmationPopupComponent", "AvaTagComponent", "ButtonComponent", "approvalText", "debounceTime", "distinctUntilChanged", "map", "startWith", "AgentsPreviewPanelComponent", "ApprovalTxtCardComponent", "ApprovalAgentsComponent", "router", "apiService", "approvalService", "fb", "drawerService", "agentService", "dialogService", "appLabels", "labels", "searchValue", "totalApprovedApprovals", "totalPendingApprovals", "totalApprovals", "isBasicCollapsed", "quickActionsExpanded", "consoleApproval", "options", "basicSidebarItems", "quickActions", "toolReviews", "workflowReviews", "filteredAgentsReviews", "agentsReviews", "currentToolsPage", "currentAgentsPage", "currentWorkflowsPage", "pageSize", "totalRecords", "isDeleted", "currentTab", "showToolApprovalPopup", "showInfoPopup", "showErrorPopup", "infoMessage", "selectedIndex", "showFeedbackPopup", "searchForm", "approvedAgentId", "previewData", "selectedAgentId", "constructor", "name", "electronics", "value", "clothing", "books", "id", "icon", "text", "agents", "route", "active", "workflows", "tools", "label", "group", "search", "ngOnInit", "searchList", "loadAgentsReviews", "console", "log", "get", "valueChanges", "pipe", "toLowerCase", "subscribe", "searchText", "applyFilter", "lower", "updateConsoleApproval", "filter", "item", "<PERSON><PERSON><PERSON>", "includes", "onSelectionChange", "data", "uClick", "i", "toggleQuickActions", "onBasicCollapseToggle", "isCollapsed", "onBasicItemClick", "for<PERSON>ach", "toRequestStatus", "getAllReviewAgents", "response", "agentReviewDetails", "r", "status", "length", "loadMoreAgents", "page", "loadReviews", "rejectApproval", "idx", "approveApproval", "handleApproval", "handleAgentApproval", "handleRejection", "feedback", "handleAgentRejection", "onCardClick", "index", "selectedAgent", "agentId", "loadPreviewData", "open", "closePreview", "clear", "editAgent", "handleEditAgent", "handeMetaDataSendback", "handleMetaDataApproval", "testApproval", "redirectToAgentsPlayground", "confirmation", "title", "message", "confirmButtonText", "cancelButtonText", "confirmButtonVariant", "cancelButtonVariant", "destructive", "then", "result", "confirmed", "navigate", "queryParams", "mode", "agentDetails", "reviewedBy", "approveAgent", "next", "agentSuccessApproveMessage", "error", "defaultErrorMessage", "type", "loading", "getCollaborativeAgentDetailsById", "agentDetail", "rejectAgent", "agentSuccessRejectMessage", "handleInfoPopup", "handleTesting", "contents", "req", "statusIcons", "approved", "rejected", "review", "statusTexts", "statusKey", "specificId", "refId", "session1", "color", "background", "changeRequestType", "session2", "session3", "iconName", "requestedBy", "requestedAt", "session4", "footer", "__decorate", "selector", "imports", "schemas", "templateUrl", "styleUrls"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\approval\\approval-agents\\approval-agents.component.ts"], "sourcesContent": ["import { CommonModule, formatDate } from '@angular/common';\r\nimport { Component, CUSTOM_ELEMENTS_SCHEMA, OnInit } from '@angular/core';\r\nimport { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';\r\nimport { Router, RouterModule } from '@angular/router';\r\nimport {\r\n  ApprovalCardComponent,\r\n  IconComponent,\r\n  AvaTextboxComponent,\r\n  TextCardComponent,\r\n  PopupComponent,\r\n  ConfirmationPopupComponent,\r\n  AvaTagComponent,\r\n  DropdownOption,\r\n  ButtonComponent,\r\n  DialogService,\r\n} from '@ava/play-comp-library';\r\nimport approvalText from '../constants/approval.json';\r\nimport { SharedApiServiceService } from '../../../shared/services/shared-api-service.service';\r\nimport { ApprovalService } from '../../../shared/services/approval.service';\r\nimport { debounceTime, distinctUntilChanged, map, startWith } from 'rxjs';\r\nimport { DrawerService } from '../../../shared/services/drawer/drawer.service';\r\nimport { AgentsPreviewPanelComponent } from './agents-preview-panel/agents-preview-panel.component';\r\nimport { AgentServiceService } from '@shared/index';\r\nimport { ApprovalTxtCardComponent } from '../approval-text-card/approval-text-card.component';\r\n\r\ntype RequestStatus = 'approved' | 'rejected' | 'review';\r\n\r\n@Component({\r\n  selector: 'app-approval-agents',\r\n  imports: [\r\n    CommonModule,\r\n    RouterModule,\r\n    ApprovalCardComponent,\r\n    IconComponent,\r\n    AvaTextboxComponent,\r\n    ReactiveFormsModule,\r\n    AvaTagComponent,\r\n    ButtonComponent,\r\n    PopupComponent,\r\n    ConfirmationPopupComponent,\r\n    ApprovalTxtCardComponent\r\n  ],\r\n  schemas: [CUSTOM_ELEMENTS_SCHEMA],\r\n  templateUrl: './approval-agents.component.html',\r\n  styleUrls: ['./approval-agents.component.scss'],\r\n})\r\nexport class ApprovalAgentsComponent implements OnInit {\r\n  appLabels = approvalText.labels;\r\n\r\n  public searchValue: string = '';\r\n  public totalApprovedApprovals: number = 20;\r\n  public totalPendingApprovals: number = 15;\r\n  public totalApprovals: number = 60;\r\n  public isBasicCollapsed: boolean = false;\r\n  public quickActionsExpanded: boolean = true;\r\n  public consoleApproval: any = {};\r\n  public options: DropdownOption[] = [];\r\n  public basicSidebarItems: any[] = [];\r\n  public quickActions: any[] = [];\r\n  public toolReviews: any[] = [];\r\n  public workflowReviews: any[] = [];\r\n  public filteredAgentsReviews: any[] = [];\r\n  public agentsReviews: any[] = [];\r\n  public currentToolsPage = 1;\r\n  public currentAgentsPage = 1;\r\n  public currentWorkflowsPage = 1;\r\n  public pageSize = 50;\r\n  public totalRecords = 0;\r\n  public isDeleted = false;\r\n  public currentTab = 'Agents';\r\n  public showToolApprovalPopup = false;\r\n  public showInfoPopup = false;\r\n  public showErrorPopup = false;\r\n  public infoMessage = '';\r\n  public selectedIndex = 0;\r\n  public showFeedbackPopup = false;\r\n  public searchForm!: FormGroup;\r\n  public labels: any = approvalText.labels;\r\n  public approvedAgentId: number | null = null;\r\n  public previewData: any = null;\r\n  public selectedAgentId : number = 0;\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private apiService: SharedApiServiceService,\r\n    private approvalService: ApprovalService,\r\n    private fb: FormBuilder,\r\n    private drawerService: DrawerService,\r\n    private agentService: AgentServiceService,\r\n    private dialogService: DialogService\r\n  ) {\r\n    this.labels = approvalText.labels;\r\n    this.options = [\r\n      { name: this.labels.electronics, value: 'electronics' },\r\n      { name: this.labels.clothing, value: 'clothing' },\r\n      { name: this.labels.books, value: 'books' },\r\n    ];\r\n    this.basicSidebarItems = [\r\n      {\r\n        id: '1',\r\n        icon: 'hammer',\r\n        text: this.labels.agents,\r\n        route: '',\r\n        active: true,\r\n      },\r\n      { id: '2', icon: 'circle-check', text: this.labels.workflows, route: '' },\r\n      { id: '3', icon: 'bot', text: this.labels.tools, route: '' },\r\n    ];\r\n    this.quickActions = [\r\n      {\r\n        icon: 'awe_agents',\r\n        label: this.labels.agents,\r\n        route: '',\r\n      },\r\n      {\r\n        icon: 'awe_workflows',\r\n        label: this.labels.workflows,\r\n        route: '',\r\n      },\r\n      {\r\n        icon: 'awe_tools',\r\n        label: this.labels.tools,\r\n        route: '',\r\n      },\r\n    ];\r\n    this.searchForm = this.fb.group({\r\n      search: [''],\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.searchList();\r\n    this.totalApprovals = 60;\r\n    this.loadAgentsReviews();\r\n  }\r\n\r\n  public searchList() {\r\n    console.log(this.searchForm.get('search')?.value);\r\n    this.searchForm\r\n      .get('search')!\r\n      .valueChanges.pipe(\r\n        startWith(''),\r\n        debounceTime(300),\r\n        distinctUntilChanged(),\r\n        map((value) => value?.toLowerCase() ?? ''),\r\n      )\r\n      .subscribe((searchText) => {\r\n        this.applyFilter(searchText);\r\n      });\r\n  }\r\n\r\n  public applyFilter(text: string) {\r\n    const lower = text;\r\n\r\n    if (!text) {\r\n      this.updateConsoleApproval(this.agentsReviews, 'agent');\r\n      return;\r\n    }\r\n\r\n    this.filteredAgentsReviews = this.agentsReviews.filter((item) =>\r\n      item.agentName?.toLowerCase().includes(lower),\r\n    );\r\n    this.updateConsoleApproval(this.filteredAgentsReviews, 'agent');\r\n  }\r\n\r\n  public onSelectionChange(data: any) {\r\n    console.log('Selection changed:', data);\r\n  }\r\n\r\n  public uClick(i: any) {\r\n    console.log('log' + i);\r\n  }\r\n\r\n  public toggleQuickActions(): void {\r\n    this.quickActionsExpanded = !this.quickActionsExpanded;\r\n  }\r\n\r\n  public onBasicCollapseToggle(isCollapsed: boolean): void {\r\n    this.isBasicCollapsed = isCollapsed;\r\n    console.log('Basic sidebar collapsed:', isCollapsed);\r\n  }\r\n\r\n  public onBasicItemClick(item: any): void {\r\n    this.basicSidebarItems.forEach((i) => (i.active = false));\r\n    item.active = true;\r\n    console.log(item);\r\n  }\r\n\r\n  public toRequestStatus(value: string | null | undefined): RequestStatus {\r\n    return value === 'approved' || value === 'rejected' || value === 'review'\r\n      ? value\r\n      : 'review';\r\n  }\r\n\r\n  public loadAgentsReviews() {\r\n    this.approvalService\r\n      .getAllReviewAgents(this.currentAgentsPage, this.pageSize, this.isDeleted)\r\n      .subscribe((response) => {\r\n        if (this.currentAgentsPage > 1) {\r\n          this.agentsReviews = [\r\n            ...this.agentsReviews,\r\n            ...response.agentReviewDetails,\r\n          ];\r\n        } else {\r\n          this.agentsReviews = response?.agentReviewDetails;\r\n        }\r\n        this.filteredAgentsReviews = this.agentsReviews;\r\n        this.agentsReviews = this.agentsReviews.filter(\r\n          (r) => r.status !== 'approved',\r\n        );\r\n        // console.log('agents reviews ', this.agentsReviews);\r\n        this.totalRecords = this.agentsReviews.length;\r\n        this.updateConsoleApproval(this.agentsReviews, 'agent');\r\n      });\r\n  }\r\n\r\n  public loadMoreAgents(page: number) {\r\n    this.currentAgentsPage = page;\r\n    this.loadAgentsReviews();\r\n  }\r\n\r\n  public loadReviews(name: string) {\r\n    this.loadAgentsReviews();\r\n  }\r\n\r\n  public rejectApproval(idx: any) {\r\n    console.log(idx);\r\n    this.selectedIndex = idx;\r\n    // this.showFeedbackPopup = true;\r\n  }\r\n\r\n  public approveApproval(idx: any){\r\n    console.log(idx);\r\n    this.selectedIndex = idx;\r\n    console.log(this.filteredAgentsReviews[this.selectedIndex]);\r\n    // this.showToolApprovalPopup = true;\r\n  }\r\n\r\n  public handleApproval() {\r\n    this.handleAgentApproval();\r\n  }\r\n\r\n  public handleRejection(feedback: any) {\r\n    console.log(\"Clicked on confirmation popup\");\r\n    this.handleAgentRejection(feedback);\r\n  }\r\n\r\n  public onCardClick(index: number): void {\r\n    console.log('Selected card index:', index);\r\n    this.selectedIndex = index;\r\n    const selectedAgent = this.filteredAgentsReviews[this.selectedIndex];\r\n    this.selectedAgentId = selectedAgent.agentId;\r\n    this.loadPreviewData(selectedAgent);\r\n\r\n    this.drawerService.open(AgentsPreviewPanelComponent, {\r\n      previewData: this.previewData,\r\n      closePreview: () => this.drawerService.clear(),\r\n      editAgent: () => this.handleEditAgent(selectedAgent.agentId),\r\n      rejectApproval: () => this.handeMetaDataSendback(),\r\n      approveApproval: () => this.handleMetaDataApproval(),\r\n      testApproval: () => this.redirectToAgentsPlayground(),\r\n    });\r\n    console.log(selectedAgent);\r\n  }\r\n\r\n  public handleMetaDataApproval(){\r\n    this.showToolApprovalPopup = true;\r\n  }\r\n\r\n  public handeMetaDataSendback(){\r\n    // this.showFeedbackPopup = true;\r\n    this.dialogService.confirmation({\r\n      title: 'Confirm Send Back?',\r\n      message: 'Are you sure you want to send back this agent for corrections and modification?',\r\n      confirmButtonText: 'Send Back',\r\n      cancelButtonText: 'Cancel',\r\n      confirmButtonVariant: 'danger',\r\n      cancelButtonVariant: 'primary',\r\n      destructive: true,\r\n    }).then(result => {\r\n      console.log('Confirmation dialog closed:', result);\r\n      if (result.confirmed === true) {\r\n        this.handleAgentRejection(\"\");\r\n      } else if (result.confirmed === false) {\r\n        console.log('User cancelled deletion! Account is safe.');\r\n      }\r\n    });\r\n  }\r\n\r\n  public handleEditAgent(agentId: string) {\r\n    console.log('Edit Agent', agentId);\r\n    this.drawerService.clear();\r\n    this.router.navigate(\r\n      ['/build/agents/collaborative'],\r\n      { queryParams: { id: agentId, mode: 'edit' } }\r\n    );\r\n  }\r\n\r\n  public handleAgentApproval() {\r\n    const agentDetails = this.filteredAgentsReviews[this.selectedIndex];\r\n    const id = agentDetails.id;\r\n    const agentId = agentDetails.agentId;\r\n    const status = 'approved';\r\n    const reviewedBy = agentDetails.reviewedBy;\r\n\r\n    this.approvalService\r\n      .approveAgent(id, agentId, status, reviewedBy)\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.infoMessage =\r\n            response?.message || this.labels.agentSuccessApproveMessage;\r\n          this.showInfoPopup = true;\r\n\r\n          // Store agent ID for navigation after popup confirmation\r\n          this.approvedAgentId = agentId;\r\n        },\r\n        error: (error) => {\r\n          this.showErrorPopup = true;\r\n          this.infoMessage =\r\n            error?.error?.message || this.labels.defaultErrorMessage;\r\n          console.error('Error:', error);\r\n        },\r\n      });\r\n  }\r\n\r\n  public loadPreviewData(selectedAgent : any){\r\n    this.previewData = {\r\n      type: 'agent',\r\n      title: selectedAgent.agentName,\r\n      data: selectedAgent,\r\n      loading: true,\r\n      error: null,\r\n    };\r\n\r\n    console.log('Load preview data', selectedAgent.agentId);\r\n    this.agentService.getCollaborativeAgentDetailsById(selectedAgent.agentId).subscribe({\r\n      next: (response) => {\r\n        // console.log('Collaborative agent details', response);\r\n        this.previewData.data = response.agentDetail;\r\n        this.previewData.loading = false;\r\n        this.previewData.error = null;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error:', error);\r\n      },\r\n    });\r\n  }\r\n\r\n  public handleAgentRejection(feedback: any) {\r\n    const agentDetails = this.filteredAgentsReviews[this.selectedIndex];\r\n    const id = agentDetails.id;\r\n    const agentId = agentDetails.agentId;\r\n    const status = 'rejected';\r\n    const reviewedBy = agentDetails.reviewedBy ;\r\n    const message = feedback;\r\n\r\n    this.approvalService\r\n      .rejectAgent(id, agentId, status, reviewedBy, message)\r\n      .subscribe({\r\n        next: (response: any) => {\r\n          this.infoMessage =\r\n            response?.message || this.labels.agentSuccessRejectMessage;\r\n          this.showInfoPopup = true;\r\n        },\r\n        error: (error) => {\r\n          this.showErrorPopup = true;\r\n          this.infoMessage =\r\n            error?.error?.message || this.labels.defaultErrorMessage;\r\n          console.error('Error:', error);\r\n        },\r\n      });\r\n  }\r\n\r\n  public handleInfoPopup() {\r\n    this.showInfoPopup = false;\r\n    this.loadAgentsReviews();\r\n  }\r\n\r\n  public handleTesting(index: any) {\r\n    this.selectedAgentId = this.filteredAgentsReviews[index].agentId; \r\n    console.log('Selected agent id', this.selectedAgentId);\r\n  }\r\n\r\n  public redirectToAgentsPlayground(): void {\r\n    this.router.navigate([`/build/agents/collaborative/execute`], {\r\n      queryParams: { id: this.selectedAgentId },\r\n    });\r\n  }\r\n\r\n  public updateConsoleApproval(data: any[], type: string) {\r\n    this.consoleApproval = {\r\n      contents: data?.map((req: any) => {\r\n        const statusIcons: Record<RequestStatus, string> = {\r\n          approved: 'circle-check-big',\r\n          rejected: 'circle-x',\r\n          review: 'clock',\r\n        };\r\n        const statusTexts: Record<RequestStatus, string> = {\r\n          approved: this.labels.approved,\r\n          rejected: this.labels.rejected,\r\n          review: this.labels.review,\r\n        };\r\n        const statusKey = this.toRequestStatus(req?.status);\r\n        const specificId = req.agentId;\r\n        const title = req.agentName;\r\n\r\n        return {\r\n          id: req.id,\r\n          refId: specificId,\r\n          type: type,\r\n          session1: {\r\n            title: title,\r\n            labels: [\r\n              {\r\n                name: type,\r\n                color: 'success',\r\n                background: 'red',\r\n                type: 'normal',\r\n              },\r\n              {\r\n                name: req.changeRequestType,\r\n                color: req.changeRequestType === 'update' ? 'error' : 'info',\r\n                background: 'red',\r\n                type: 'pill',\r\n              },\r\n            ],\r\n          },\r\n          session2: [\r\n            {\r\n              name: type,\r\n              color: 'default',\r\n              background: 'red',\r\n              type: 'normal',\r\n            },\r\n            {\r\n              name: req.status,\r\n              color: 'default',\r\n              background: 'red',\r\n              type: 'normal',\r\n            },\r\n          ],\r\n          session3: [\r\n            {\r\n              iconName: 'user',\r\n              label: req.requestedBy,\r\n            },\r\n            {\r\n              iconName: 'calendar-days',\r\n              label: formatDate(req?.requestedAt, 'dd MMM yyyy', 'en-IN'),\r\n            },\r\n          ],\r\n          session4: {\r\n            status: statusTexts[statusKey],\r\n            iconName: statusIcons[statusKey],\r\n          },\r\n        };\r\n      }),\r\n      footer: {},\r\n    };\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,YAAY,EAAEC,UAAU,QAAQ,iBAAiB;AAC1D,SAASC,SAAS,EAAEC,sBAAsB,QAAgB,eAAe;AACzE,SAAiCC,mBAAmB,QAAQ,gBAAgB;AAC5E,SAAiBC,YAAY,QAAQ,iBAAiB;AACtD,SACEC,qBAAqB,EACrBC,aAAa,EACbC,mBAAmB,EAEnBC,cAAc,EACdC,0BAA0B,EAC1BC,eAAe,EAEfC,eAAe,QAEV,wBAAwB;AAC/B,OAAOC,YAAY,MAAM,4BAA4B;AAGrD,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,GAAG,EAAEC,SAAS,QAAQ,MAAM;AAEzE,SAASC,2BAA2B,QAAQ,uDAAuD;AAEnG,SAASC,wBAAwB,QAAQ,oDAAoD;AAuBtF,IAAMC,uBAAuB,GAA7B,MAAMA,uBAAuB;EAqCxBC,MAAA;EACAC,UAAA;EACAC,eAAA;EACAC,EAAA;EACAC,aAAA;EACAC,YAAA;EACAC,aAAA;EA1CVC,SAAS,GAAGf,YAAY,CAACgB,MAAM;EAExBC,WAAW,GAAW,EAAE;EACxBC,sBAAsB,GAAW,EAAE;EACnCC,qBAAqB,GAAW,EAAE;EAClCC,cAAc,GAAW,EAAE;EAC3BC,gBAAgB,GAAY,KAAK;EACjCC,oBAAoB,GAAY,IAAI;EACpCC,eAAe,GAAQ,EAAE;EACzBC,OAAO,GAAqB,EAAE;EAC9BC,iBAAiB,GAAU,EAAE;EAC7BC,YAAY,GAAU,EAAE;EACxBC,WAAW,GAAU,EAAE;EACvBC,eAAe,GAAU,EAAE;EAC3BC,qBAAqB,GAAU,EAAE;EACjCC,aAAa,GAAU,EAAE;EACzBC,gBAAgB,GAAG,CAAC;EACpBC,iBAAiB,GAAG,CAAC;EACrBC,oBAAoB,GAAG,CAAC;EACxBC,QAAQ,GAAG,EAAE;EACbC,YAAY,GAAG,CAAC;EAChBC,SAAS,GAAG,KAAK;EACjBC,UAAU,GAAG,QAAQ;EACrBC,qBAAqB,GAAG,KAAK;EAC7BC,aAAa,GAAG,KAAK;EACrBC,cAAc,GAAG,KAAK;EACtBC,WAAW,GAAG,EAAE;EAChBC,aAAa,GAAG,CAAC;EACjBC,iBAAiB,GAAG,KAAK;EACzBC,UAAU;EACV5B,MAAM,GAAQhB,YAAY,CAACgB,MAAM;EACjC6B,eAAe,GAAkB,IAAI;EACrCC,WAAW,GAAQ,IAAI;EACvBC,eAAe,GAAY,CAAC;EAEnCC,YACUxC,MAAc,EACdC,UAAmC,EACnCC,eAAgC,EAChCC,EAAe,EACfC,aAA4B,EAC5BC,YAAiC,EACjCC,aAA4B;IAN5B,KAAAN,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,aAAa,GAAbA,aAAa;IAErB,IAAI,CAACE,MAAM,GAAGhB,YAAY,CAACgB,MAAM;IACjC,IAAI,CAACQ,OAAO,GAAG,CACb;MAAEyB,IAAI,EAAE,IAAI,CAACjC,MAAM,CAACkC,WAAW;MAAEC,KAAK,EAAE;IAAa,CAAE,EACvD;MAAEF,IAAI,EAAE,IAAI,CAACjC,MAAM,CAACoC,QAAQ;MAAED,KAAK,EAAE;IAAU,CAAE,EACjD;MAAEF,IAAI,EAAE,IAAI,CAACjC,MAAM,CAACqC,KAAK;MAAEF,KAAK,EAAE;IAAO,CAAE,CAC5C;IACD,IAAI,CAAC1B,iBAAiB,GAAG,CACvB;MACE6B,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,IAAI,CAACxC,MAAM,CAACyC,MAAM;MACxBC,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE;KACT,EACD;MAAEL,EAAE,EAAE,GAAG;MAAEC,IAAI,EAAE,cAAc;MAAEC,IAAI,EAAE,IAAI,CAACxC,MAAM,CAAC4C,SAAS;MAAEF,KAAK,EAAE;IAAE,CAAE,EACzE;MAAEJ,EAAE,EAAE,GAAG;MAAEC,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE,IAAI,CAACxC,MAAM,CAAC6C,KAAK;MAAEH,KAAK,EAAE;IAAE,CAAE,CAC7D;IACD,IAAI,CAAChC,YAAY,GAAG,CAClB;MACE6B,IAAI,EAAE,YAAY;MAClBO,KAAK,EAAE,IAAI,CAAC9C,MAAM,CAACyC,MAAM;MACzBC,KAAK,EAAE;KACR,EACD;MACEH,IAAI,EAAE,eAAe;MACrBO,KAAK,EAAE,IAAI,CAAC9C,MAAM,CAAC4C,SAAS;MAC5BF,KAAK,EAAE;KACR,EACD;MACEH,IAAI,EAAE,WAAW;MACjBO,KAAK,EAAE,IAAI,CAAC9C,MAAM,CAAC6C,KAAK;MACxBH,KAAK,EAAE;KACR,CACF;IACD,IAAI,CAACd,UAAU,GAAG,IAAI,CAACjC,EAAE,CAACoD,KAAK,CAAC;MAC9BC,MAAM,EAAE,CAAC,EAAE;KACZ,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,UAAU,EAAE;IACjB,IAAI,CAAC9C,cAAc,GAAG,EAAE;IACxB,IAAI,CAAC+C,iBAAiB,EAAE;EAC1B;EAEOD,UAAUA,CAAA;IACfE,OAAO,CAACC,GAAG,CAAC,IAAI,CAACzB,UAAU,CAAC0B,GAAG,CAAC,QAAQ,CAAC,EAAEnB,KAAK,CAAC;IACjD,IAAI,CAACP,UAAU,CACZ0B,GAAG,CAAC,QAAQ,CAAE,CACdC,YAAY,CAACC,IAAI,CAChBpE,SAAS,CAAC,EAAE,CAAC,EACbH,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,GAAG,CAAEgD,KAAK,IAAKA,KAAK,EAAEsB,WAAW,EAAE,IAAI,EAAE,CAAC,CAC3C,CACAC,SAAS,CAAEC,UAAU,IAAI;MACxB,IAAI,CAACC,WAAW,CAACD,UAAU,CAAC;IAC9B,CAAC,CAAC;EACN;EAEOC,WAAWA,CAACpB,IAAY;IAC7B,MAAMqB,KAAK,GAAGrB,IAAI;IAElB,IAAI,CAACA,IAAI,EAAE;MACT,IAAI,CAACsB,qBAAqB,CAAC,IAAI,CAAChD,aAAa,EAAE,OAAO,CAAC;MACvD;IACF;IAEA,IAAI,CAACD,qBAAqB,GAAG,IAAI,CAACC,aAAa,CAACiD,MAAM,CAAEC,IAAI,IAC1DA,IAAI,CAACC,SAAS,EAAER,WAAW,EAAE,CAACS,QAAQ,CAACL,KAAK,CAAC,CAC9C;IACD,IAAI,CAACC,qBAAqB,CAAC,IAAI,CAACjD,qBAAqB,EAAE,OAAO,CAAC;EACjE;EAEOsD,iBAAiBA,CAACC,IAAS;IAChChB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEe,IAAI,CAAC;EACzC;EAEOC,MAAMA,CAACC,CAAM;IAClBlB,OAAO,CAACC,GAAG,CAAC,KAAK,GAAGiB,CAAC,CAAC;EACxB;EAEOC,kBAAkBA,CAAA;IACvB,IAAI,CAACjE,oBAAoB,GAAG,CAAC,IAAI,CAACA,oBAAoB;EACxD;EAEOkE,qBAAqBA,CAACC,WAAoB;IAC/C,IAAI,CAACpE,gBAAgB,GAAGoE,WAAW;IACnCrB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEoB,WAAW,CAAC;EACtD;EAEOC,gBAAgBA,CAACV,IAAS;IAC/B,IAAI,CAACvD,iBAAiB,CAACkE,OAAO,CAAEL,CAAC,IAAMA,CAAC,CAAC3B,MAAM,GAAG,KAAM,CAAC;IACzDqB,IAAI,CAACrB,MAAM,GAAG,IAAI;IAClBS,OAAO,CAACC,GAAG,CAACW,IAAI,CAAC;EACnB;EAEOY,eAAeA,CAACzC,KAAgC;IACrD,OAAOA,KAAK,KAAK,UAAU,IAAIA,KAAK,KAAK,UAAU,IAAIA,KAAK,KAAK,QAAQ,GACrEA,KAAK,GACL,QAAQ;EACd;EAEOgB,iBAAiBA,CAAA;IACtB,IAAI,CAACzD,eAAe,CACjBmF,kBAAkB,CAAC,IAAI,CAAC7D,iBAAiB,EAAE,IAAI,CAACE,QAAQ,EAAE,IAAI,CAACE,SAAS,CAAC,CACzEsC,SAAS,CAAEoB,QAAQ,IAAI;MACtB,IAAI,IAAI,CAAC9D,iBAAiB,GAAG,CAAC,EAAE;QAC9B,IAAI,CAACF,aAAa,GAAG,CACnB,GAAG,IAAI,CAACA,aAAa,EACrB,GAAGgE,QAAQ,CAACC,kBAAkB,CAC/B;MACH,CAAC,MAAM;QACL,IAAI,CAACjE,aAAa,GAAGgE,QAAQ,EAAEC,kBAAkB;MACnD;MACA,IAAI,CAAClE,qBAAqB,GAAG,IAAI,CAACC,aAAa;MAC/C,IAAI,CAACA,aAAa,GAAG,IAAI,CAACA,aAAa,CAACiD,MAAM,CAC3CiB,CAAC,IAAKA,CAAC,CAACC,MAAM,KAAK,UAAU,CAC/B;MACD;MACA,IAAI,CAAC9D,YAAY,GAAG,IAAI,CAACL,aAAa,CAACoE,MAAM;MAC7C,IAAI,CAACpB,qBAAqB,CAAC,IAAI,CAAChD,aAAa,EAAE,OAAO,CAAC;IACzD,CAAC,CAAC;EACN;EAEOqE,cAAcA,CAACC,IAAY;IAChC,IAAI,CAACpE,iBAAiB,GAAGoE,IAAI;IAC7B,IAAI,CAACjC,iBAAiB,EAAE;EAC1B;EAEOkC,WAAWA,CAACpD,IAAY;IAC7B,IAAI,CAACkB,iBAAiB,EAAE;EAC1B;EAEOmC,cAAcA,CAACC,GAAQ;IAC5BnC,OAAO,CAACC,GAAG,CAACkC,GAAG,CAAC;IAChB,IAAI,CAAC7D,aAAa,GAAG6D,GAAG;IACxB;EACF;EAEOC,eAAeA,CAACD,GAAQ;IAC7BnC,OAAO,CAACC,GAAG,CAACkC,GAAG,CAAC;IAChB,IAAI,CAAC7D,aAAa,GAAG6D,GAAG;IACxBnC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACxC,qBAAqB,CAAC,IAAI,CAACa,aAAa,CAAC,CAAC;IAC3D;EACF;EAEO+D,cAAcA,CAAA;IACnB,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEOC,eAAeA,CAACC,QAAa;IAClCxC,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC5C,IAAI,CAACwC,oBAAoB,CAACD,QAAQ,CAAC;EACrC;EAEOE,WAAWA,CAACC,KAAa;IAC9B3C,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE0C,KAAK,CAAC;IAC1C,IAAI,CAACrE,aAAa,GAAGqE,KAAK;IAC1B,MAAMC,aAAa,GAAG,IAAI,CAACnF,qBAAqB,CAAC,IAAI,CAACa,aAAa,CAAC;IACpE,IAAI,CAACK,eAAe,GAAGiE,aAAa,CAACC,OAAO;IAC5C,IAAI,CAACC,eAAe,CAACF,aAAa,CAAC;IAEnC,IAAI,CAACpG,aAAa,CAACuG,IAAI,CAAC9G,2BAA2B,EAAE;MACnDyC,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BsE,YAAY,EAAEA,CAAA,KAAM,IAAI,CAACxG,aAAa,CAACyG,KAAK,EAAE;MAC9CC,SAAS,EAAEA,CAAA,KAAM,IAAI,CAACC,eAAe,CAACP,aAAa,CAACC,OAAO,CAAC;MAC5DX,cAAc,EAAEA,CAAA,KAAM,IAAI,CAACkB,qBAAqB,EAAE;MAClDhB,eAAe,EAAEA,CAAA,KAAM,IAAI,CAACiB,sBAAsB,EAAE;MACpDC,YAAY,EAAEA,CAAA,KAAM,IAAI,CAACC,0BAA0B;KACpD,CAAC;IACFvD,OAAO,CAACC,GAAG,CAAC2C,aAAa,CAAC;EAC5B;EAEOS,sBAAsBA,CAAA;IAC3B,IAAI,CAACnF,qBAAqB,GAAG,IAAI;EACnC;EAEOkF,qBAAqBA,CAAA;IAC1B;IACA,IAAI,CAAC1G,aAAa,CAAC8G,YAAY,CAAC;MAC9BC,KAAK,EAAE,oBAAoB;MAC3BC,OAAO,EAAE,iFAAiF;MAC1FC,iBAAiB,EAAE,WAAW;MAC9BC,gBAAgB,EAAE,QAAQ;MAC1BC,oBAAoB,EAAE,QAAQ;MAC9BC,mBAAmB,EAAE,SAAS;MAC9BC,WAAW,EAAE;KACd,CAAC,CAACC,IAAI,CAACC,MAAM,IAAG;MACfjE,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEgE,MAAM,CAAC;MAClD,IAAIA,MAAM,CAACC,SAAS,KAAK,IAAI,EAAE;QAC7B,IAAI,CAACzB,oBAAoB,CAAC,EAAE,CAAC;MAC/B,CAAC,MAAM,IAAIwB,MAAM,CAACC,SAAS,KAAK,KAAK,EAAE;QACrClE,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MAC1D;IACF,CAAC,CAAC;EACJ;EAEOkD,eAAeA,CAACN,OAAe;IACpC7C,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE4C,OAAO,CAAC;IAClC,IAAI,CAACrG,aAAa,CAACyG,KAAK,EAAE;IAC1B,IAAI,CAAC7G,MAAM,CAAC+H,QAAQ,CAClB,CAAC,6BAA6B,CAAC,EAC/B;MAAEC,WAAW,EAAE;QAAElF,EAAE,EAAE2D,OAAO;QAAEwB,IAAI,EAAE;MAAM;IAAE,CAAE,CAC/C;EACH;EAEO/B,mBAAmBA,CAAA;IACxB,MAAMgC,YAAY,GAAG,IAAI,CAAC7G,qBAAqB,CAAC,IAAI,CAACa,aAAa,CAAC;IACnE,MAAMY,EAAE,GAAGoF,YAAY,CAACpF,EAAE;IAC1B,MAAM2D,OAAO,GAAGyB,YAAY,CAACzB,OAAO;IACpC,MAAMhB,MAAM,GAAG,UAAU;IACzB,MAAM0C,UAAU,GAAGD,YAAY,CAACC,UAAU;IAE1C,IAAI,CAACjI,eAAe,CACjBkI,YAAY,CAACtF,EAAE,EAAE2D,OAAO,EAAEhB,MAAM,EAAE0C,UAAU,CAAC,CAC7CjE,SAAS,CAAC;MACTmE,IAAI,EAAG/C,QAAa,IAAI;QACtB,IAAI,CAACrD,WAAW,GACdqD,QAAQ,EAAEgC,OAAO,IAAI,IAAI,CAAC9G,MAAM,CAAC8H,0BAA0B;QAC7D,IAAI,CAACvG,aAAa,GAAG,IAAI;QAEzB;QACA,IAAI,CAACM,eAAe,GAAGoE,OAAO;MAChC,CAAC;MACD8B,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACvG,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACC,WAAW,GACdsG,KAAK,EAAEA,KAAK,EAAEjB,OAAO,IAAI,IAAI,CAAC9G,MAAM,CAACgI,mBAAmB;QAC1D5E,OAAO,CAAC2E,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;MAChC;KACD,CAAC;EACN;EAEO7B,eAAeA,CAACF,aAAmB;IACxC,IAAI,CAAClE,WAAW,GAAG;MACjBmG,IAAI,EAAE,OAAO;MACbpB,KAAK,EAAEb,aAAa,CAAC/B,SAAS;MAC9BG,IAAI,EAAE4B,aAAa;MACnBkC,OAAO,EAAE,IAAI;MACbH,KAAK,EAAE;KACR;IAED3E,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE2C,aAAa,CAACC,OAAO,CAAC;IACvD,IAAI,CAACpG,YAAY,CAACsI,gCAAgC,CAACnC,aAAa,CAACC,OAAO,CAAC,CAACvC,SAAS,CAAC;MAClFmE,IAAI,EAAG/C,QAAQ,IAAI;QACjB;QACA,IAAI,CAAChD,WAAW,CAACsC,IAAI,GAAGU,QAAQ,CAACsD,WAAW;QAC5C,IAAI,CAACtG,WAAW,CAACoG,OAAO,GAAG,KAAK;QAChC,IAAI,CAACpG,WAAW,CAACiG,KAAK,GAAG,IAAI;MAC/B,CAAC;MACDA,KAAK,EAAGA,KAAK,IAAI;QACf3E,OAAO,CAAC2E,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;MAChC;KACD,CAAC;EACJ;EAEOlC,oBAAoBA,CAACD,QAAa;IACvC,MAAM8B,YAAY,GAAG,IAAI,CAAC7G,qBAAqB,CAAC,IAAI,CAACa,aAAa,CAAC;IACnE,MAAMY,EAAE,GAAGoF,YAAY,CAACpF,EAAE;IAC1B,MAAM2D,OAAO,GAAGyB,YAAY,CAACzB,OAAO;IACpC,MAAMhB,MAAM,GAAG,UAAU;IACzB,MAAM0C,UAAU,GAAGD,YAAY,CAACC,UAAU;IAC1C,MAAMb,OAAO,GAAGlB,QAAQ;IAExB,IAAI,CAAClG,eAAe,CACjB2I,WAAW,CAAC/F,EAAE,EAAE2D,OAAO,EAAEhB,MAAM,EAAE0C,UAAU,EAAEb,OAAO,CAAC,CACrDpD,SAAS,CAAC;MACTmE,IAAI,EAAG/C,QAAa,IAAI;QACtB,IAAI,CAACrD,WAAW,GACdqD,QAAQ,EAAEgC,OAAO,IAAI,IAAI,CAAC9G,MAAM,CAACsI,yBAAyB;QAC5D,IAAI,CAAC/G,aAAa,GAAG,IAAI;MAC3B,CAAC;MACDwG,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACvG,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACC,WAAW,GACdsG,KAAK,EAAEA,KAAK,EAAEjB,OAAO,IAAI,IAAI,CAAC9G,MAAM,CAACgI,mBAAmB;QAC1D5E,OAAO,CAAC2E,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;MAChC;KACD,CAAC;EACN;EAEOQ,eAAeA,CAAA;IACpB,IAAI,CAAChH,aAAa,GAAG,KAAK;IAC1B,IAAI,CAAC4B,iBAAiB,EAAE;EAC1B;EAEOqF,aAAaA,CAACzC,KAAU;IAC7B,IAAI,CAAChE,eAAe,GAAG,IAAI,CAAClB,qBAAqB,CAACkF,KAAK,CAAC,CAACE,OAAO;IAChE7C,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAACtB,eAAe,CAAC;EACxD;EAEO4E,0BAA0BA,CAAA;IAC/B,IAAI,CAACnH,MAAM,CAAC+H,QAAQ,CAAC,CAAC,qCAAqC,CAAC,EAAE;MAC5DC,WAAW,EAAE;QAAElF,EAAE,EAAE,IAAI,CAACP;MAAe;KACxC,CAAC;EACJ;EAEO+B,qBAAqBA,CAACM,IAAW,EAAE6D,IAAY;IACpD,IAAI,CAAC1H,eAAe,GAAG;MACrBkI,QAAQ,EAAErE,IAAI,EAAEjF,GAAG,CAAEuJ,GAAQ,IAAI;QAC/B,MAAMC,WAAW,GAAkC;UACjDC,QAAQ,EAAE,kBAAkB;UAC5BC,QAAQ,EAAE,UAAU;UACpBC,MAAM,EAAE;SACT;QACD,MAAMC,WAAW,GAAkC;UACjDH,QAAQ,EAAE,IAAI,CAAC5I,MAAM,CAAC4I,QAAQ;UAC9BC,QAAQ,EAAE,IAAI,CAAC7I,MAAM,CAAC6I,QAAQ;UAC9BC,MAAM,EAAE,IAAI,CAAC9I,MAAM,CAAC8I;SACrB;QACD,MAAME,SAAS,GAAG,IAAI,CAACpE,eAAe,CAAC8D,GAAG,EAAEzD,MAAM,CAAC;QACnD,MAAMgE,UAAU,GAAGP,GAAG,CAACzC,OAAO;QAC9B,MAAMY,KAAK,GAAG6B,GAAG,CAACzE,SAAS;QAE3B,OAAO;UACL3B,EAAE,EAAEoG,GAAG,CAACpG,EAAE;UACV4G,KAAK,EAAED,UAAU;UACjBhB,IAAI,EAAEA,IAAI;UACVkB,QAAQ,EAAE;YACRtC,KAAK,EAAEA,KAAK;YACZ7G,MAAM,EAAE,CACN;cACEiC,IAAI,EAAEgG,IAAI;cACVmB,KAAK,EAAE,SAAS;cAChBC,UAAU,EAAE,KAAK;cACjBpB,IAAI,EAAE;aACP,EACD;cACEhG,IAAI,EAAEyG,GAAG,CAACY,iBAAiB;cAC3BF,KAAK,EAAEV,GAAG,CAACY,iBAAiB,KAAK,QAAQ,GAAG,OAAO,GAAG,MAAM;cAC5DD,UAAU,EAAE,KAAK;cACjBpB,IAAI,EAAE;aACP;WAEJ;UACDsB,QAAQ,EAAE,CACR;YACEtH,IAAI,EAAEgG,IAAI;YACVmB,KAAK,EAAE,SAAS;YAChBC,UAAU,EAAE,KAAK;YACjBpB,IAAI,EAAE;WACP,EACD;YACEhG,IAAI,EAAEyG,GAAG,CAACzD,MAAM;YAChBmE,KAAK,EAAE,SAAS;YAChBC,UAAU,EAAE,KAAK;YACjBpB,IAAI,EAAE;WACP,CACF;UACDuB,QAAQ,EAAE,CACR;YACEC,QAAQ,EAAE,MAAM;YAChB3G,KAAK,EAAE4F,GAAG,CAACgB;WACZ,EACD;YACED,QAAQ,EAAE,eAAe;YACzB3G,KAAK,EAAE1E,UAAU,CAACsK,GAAG,EAAEiB,WAAW,EAAE,aAAa,EAAE,OAAO;WAC3D,CACF;UACDC,QAAQ,EAAE;YACR3E,MAAM,EAAE8D,WAAW,CAACC,SAAS,CAAC;YAC9BS,QAAQ,EAAEd,WAAW,CAACK,SAAS;;SAElC;MACH,CAAC,CAAC;MACFa,MAAM,EAAE;KACT;EACH;CACD;AA9ZYtK,uBAAuB,GAAAuK,UAAA,EAnBnCzL,SAAS,CAAC;EACT0L,QAAQ,EAAE,qBAAqB;EAC/BC,OAAO,EAAE,CACP7L,YAAY,EACZK,YAAY,EACZC,qBAAqB,EACrBC,aAAa,EACbC,mBAAmB,EACnBJ,mBAAmB,EACnBO,eAAe,EACfC,eAAe,EACfH,cAAc,EACdC,0BAA0B,EAC1BS,wBAAwB,CACzB;EACD2K,OAAO,EAAE,CAAC3L,sBAAsB,CAAC;EACjC4L,WAAW,EAAE,kCAAkC;EAC/CC,SAAS,EAAE,CAAC,kCAAkC;CAC/C,CAAC,C,EACW5K,uBAAuB,CA8ZnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}