{"ast": null, "code": "import { abs, epsilon } from \"./math.js\";\nexport default function (a, b) {\n  return abs(a[0] - b[0]) < epsilon && abs(a[1] - b[1]) < epsilon;\n}", "map": {"version": 3, "names": ["abs", "epsilon", "a", "b"], "sources": ["C:/console/aava-ui-web/node_modules/d3-geo/src/pointEqual.js"], "sourcesContent": ["import {abs, epsilon} from \"./math.js\";\n\nexport default function(a, b) {\n  return abs(a[0] - b[0]) < epsilon && abs(a[1] - b[1]) < epsilon;\n}\n"], "mappings": "AAAA,SAAQA,GAAG,EAAEC,OAAO,QAAO,WAAW;AAEtC,eAAe,UAASC,CAAC,EAAEC,CAAC,EAAE;EAC5B,OAAOH,GAAG,CAACE,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGF,OAAO,IAAID,GAAG,CAACE,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGF,OAAO;AACjE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}