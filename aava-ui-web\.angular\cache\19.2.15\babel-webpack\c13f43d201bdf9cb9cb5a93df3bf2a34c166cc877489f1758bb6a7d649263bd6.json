{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { DragDropModule } from '@angular/cdk/drag-drop';\nimport { IconComponent } from '@ava/play-comp-library';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/cdk/drag-drop\";\nconst _c0 = [\"nodeElement\"];\nconst _c1 = [\"dragRef\"];\nfunction BuildAgentNodeComponent_div_3_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 8);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r1.executeTooltipText, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction BuildAgentNodeComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵelement(1, \"ava-icon\", 6);\n    i0.ɵɵtemplate(2, BuildAgentNodeComponent_div_3_div_2_Template, 1, 1, \"div\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"iconName\", ctx_r1.iconName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showTooltip && ctx_r1.executeTooltipText);\n  }\n}\nfunction BuildAgentNodeComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10);\n    i0.ɵɵelement(2, \"ava-icon\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 12)(4, \"span\", 13);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"iconName\", ctx_r1.iconName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.currentNodeData.name);\n  }\n}\nfunction BuildAgentNodeComponent_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function BuildAgentNodeComponent_button_5_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onDeleteClick($event));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 15);\n    i0.ɵɵelement(2, \"line\", 16)(3, \"line\", 17);\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let BuildAgentNodeComponent = /*#__PURE__*/(() => {\n  class BuildAgentNodeComponent {\n    nodeElement;\n    cdkDrag;\n    node;\n    selected = false;\n    mouseInteractionsEnabled = true;\n    canvasMode = 'build'; // 'build' or 'execute'\n    executeNodeData; // For execute mode consolidated nodes\n    forceAbsolutePositioning = false; // Force absolute positioning (for edit/view modes)\n    hasBeenDragged = false; // Track if node has been dragged by user\n    dragStartPosition = {\n      x: 0,\n      y: 0\n    }; // Store drag start position\n    deleteNode = new EventEmitter();\n    moveNode = new EventEmitter();\n    nodeSelected = new EventEmitter();\n    nodeDoubleClicked = new EventEmitter();\n    // Removed nodePositionChanged to avoid conflicts with moveNode\n    startConnection = new EventEmitter();\n    isDragging = false;\n    showTooltip = false;\n    nodeConfig = {\n      prompt: {\n        icon: 'FileText',\n        useAsset: false\n      },\n      model: {\n        icon: 'Box',\n        useAsset: false\n      },\n      knowledge: {\n        icon: 'BookOpen',\n        useAsset: false\n      },\n      tool: {\n        icon: 'Wrench',\n        useAsset: false\n      },\n      guardrail: {\n        icon: 'Swords',\n        useAsset: false\n      }\n    };\n    ngOnChanges(changes) {\n      if (changes['node']?.currentValue) {\n        const currentNode = changes['node'].currentValue;\n        const previousNode = changes['node'].previousValue;\n        if (this.hasPositionChanged(currentNode, previousNode)) {\n          // Only update position if we're not currently dragging\n          if (!this.isDragging) {\n            // Use CSS variables for positioning in execute mode, otherwise use CDK Drag\n            if (this.isExecuteMode) {\n              this.setCSSPosition(currentNode.data.position);\n            } else {\n              // Always update CDK drag position for non-execute modes\n              this.updateCdkDragPosition(currentNode.data.position);\n            }\n          }\n        }\n      }\n    }\n    hasPositionChanged(currentNode, previousNode) {\n      if (!currentNode?.data?.position) return false;\n      if (!previousNode?.data?.position) return true;\n      const current = currentNode.data.position;\n      const previous = previousNode.data.position;\n      return current.x !== previous.x || current.y !== previous.y;\n    }\n    ngAfterViewInit() {\n      const nodeData = this.currentNodeData;\n      if (nodeData) {\n        if (this.isExecuteMode) {\n          // Set CSS position immediately for execute mode\n          this.setCSSPosition(nodeData.position);\n        } else {\n          // Set CDK drag position for other modes\n          setTimeout(() => {\n            this.updateCdkDragPosition(nodeData.position);\n          }, 0);\n        }\n      }\n    }\n    updateCdkDragPosition(position) {\n      if (this.isDragging) return;\n      console.log('🔧 updateCdkDragPosition called:', {\n        nodeId: this.currentNodeData.id,\n        position,\n        hasCdkDrag: !!this.cdkDrag\n      });\n      // Set absolute position only - let CDK handle its own drag state\n      try {\n        if (this.nodeElement?.nativeElement) {\n          const element = this.nodeElement.nativeElement;\n          // Set absolute positioning\n          element.style.position = 'absolute';\n          element.style.left = `${position.x}px`;\n          element.style.top = `${position.y}px`;\n          element.style.transform = 'none';\n          console.log('🔧 Absolute positioning set:', {\n            nodeId: this.currentNodeData.id,\n            position: position,\n            left: element.style.left,\n            top: element.style.top\n          });\n        }\n      } catch (error) {\n        console.warn('Error updating position:', error);\n      }\n    }\n    setCSSPosition(position) {\n      console.log('🔧 setCSSPosition called:', {\n        nodeId: this.currentNodeData.id,\n        nodeName: this.currentNodeData.name,\n        position,\n        hasElement: !!this.nodeElement?.nativeElement,\n        canvasMode: this.canvasMode,\n        shouldUseAbsolute: this.shouldUseAbsolutePositioning\n      });\n      if (this.nodeElement?.nativeElement) {\n        const element = this.nodeElement.nativeElement;\n        element.style.setProperty('--node-x', `${position.x}px`);\n        element.style.setProperty('--node-y', `${position.y}px`);\n        console.log('🔧 CSS variables set:', {\n          nodeX: element.style.getPropertyValue('--node-x'),\n          nodeY: element.style.getPropertyValue('--node-y'),\n          computedStyle: window.getComputedStyle(element).position\n        });\n      } else {\n        console.warn('🔧 setCSSPosition failed - no element available');\n      }\n    }\n    get currentNodeData() {\n      return this.node?.data || {};\n    }\n    get isExecuteMode() {\n      return this.canvasMode === 'execute';\n    }\n    get shouldUseAbsolutePositioning() {\n      // Only use absolute positioning for execute mode or when CDK drag is disabled\n      const result = this.isExecuteMode || !this.mouseInteractionsEnabled;\n      console.log('🔧 shouldUseAbsolutePositioning check:', {\n        nodeId: this.currentNodeData.id,\n        isExecuteMode: this.isExecuteMode,\n        mouseInteractionsEnabled: this.mouseInteractionsEnabled,\n        forceAbsolutePositioning: this.forceAbsolutePositioning,\n        hasBeenDragged: this.hasBeenDragged,\n        canvasMode: this.canvasMode,\n        result\n      });\n      return result;\n    }\n    get executeTooltipText() {\n      if (!this.isExecuteMode || !this.executeNodeData) return '';\n      const nodeNames = this.executeNodeData.nodes.map(node => node.name);\n      if (nodeNames.length === 1) {\n        return nodeNames[0];\n      } else if (nodeNames.length > 1) {\n        const typeLabel = this.executeNodeData.type === 'knowledge' ? 'knowledge bases' : `${this.executeNodeData.type}s`;\n        return `${nodeNames.length} ${typeLabel}:\\n${nodeNames.join('\\n')}`;\n      }\n      return '';\n    }\n    get isTooltipMultiLine() {\n      return this.executeTooltipText.includes('\\n') || this.executeTooltipText.length > 50;\n    }\n    get nodePosition() {\n      if (this.isExecuteMode && this.executeNodeData) {\n        return this.executeNodeData.position || {\n          x: 0,\n          y: 0\n        };\n      }\n      return this.currentNodeData.position || {\n        x: 0,\n        y: 0\n      };\n    }\n    get isCurrentlySelected() {\n      return this.selected;\n    }\n    get currentConfig() {\n      if (this.isExecuteMode && this.executeNodeData) {\n        return this.nodeConfig[this.executeNodeData.type] || this.nodeConfig.prompt;\n      }\n      const nodeData = this.currentNodeData;\n      return this.nodeConfig[nodeData.type] || this.nodeConfig.prompt;\n    }\n    get shouldUseAssetIcon() {\n      const config = this.currentConfig;\n      return config.useAsset || false;\n    }\n    get iconName() {\n      const config = this.currentConfig;\n      if (this.isExecuteMode && this.executeNodeData) {\n        // In execute mode, always use Lucide icons\n        return config.icon;\n      }\n      const nodeData = this.currentNodeData;\n      // For asset icons (prompts), use the node's icon or config icon\n      if (config.useAsset) {\n        return nodeData.icon || config.icon;\n      }\n      // For Lucide icons, always use the config icon (which contains the correct Lucide icon name)\n      // Ignore any asset paths that might be in nodeData.icon for non-prompt types\n      return config.icon;\n    }\n    onImageError(event) {\n      // Fallback to the default icon if the image fails to load\n      const fallbackIcon = 'assets/images/build.png';\n      event.target.src = fallbackIcon;\n      console.warn(`Failed to load icon for ${this.currentNodeData?.type} node, using fallback:`, fallbackIcon);\n    }\n    onNodeClick() {\n      if (this.isExecuteMode) {\n        // In execute mode, don't emit selection events\n        return;\n      }\n      if (this.mouseInteractionsEnabled && this.currentNodeData.id) {\n        this.nodeSelected.emit(this.currentNodeData.id);\n      }\n    }\n    onNodeDoubleClick() {\n      if (this.isExecuteMode) {\n        // In execute mode, don't emit double-click events\n        return;\n      }\n      if (this.mouseInteractionsEnabled && this.currentNodeData.id) {\n        this.nodeDoubleClicked.emit(this.currentNodeData.id);\n      }\n    }\n    onMouseEnter() {\n      if (this.isExecuteMode && this.executeTooltipText) {\n        this.showTooltip = true;\n      }\n    }\n    onMouseLeave() {\n      if (this.isExecuteMode) {\n        this.showTooltip = false;\n      }\n    }\n    onDeleteClick(event) {\n      event.stopPropagation();\n      if (this.currentNodeData.id) {\n        this.deleteNode.emit(this.currentNodeData.id);\n      }\n    }\n    onDragStarted(event) {\n      this.isDragging = true;\n      this.hasBeenDragged = true; // Mark that user has dragged this node\n      // Store the starting position for manual drag calculation\n      const element = this.nodeElement?.nativeElement;\n      if (element) {\n        this.dragStartPosition = {\n          x: parseInt(element.style.left) || 0,\n          y: parseInt(element.style.top) || 0\n        };\n      }\n      console.log('🔧 Drag started:', {\n        nodeId: this.currentNodeData.id,\n        hasBeenDragged: this.hasBeenDragged,\n        dragStartPosition: this.dragStartPosition\n      });\n    }\n    onDragEnded(event) {\n      if (!this.mouseInteractionsEnabled) return;\n      this.isDragging = false;\n      const nodeData = this.currentNodeData;\n      if (!nodeData?.id) return;\n      // Calculate new position from drag start + CDK transform\n      const element = this.nodeElement?.nativeElement;\n      if (element) {\n        const transform = event.source.getFreeDragPosition();\n        const absolutePosition = {\n          x: Math.max(0, Math.round(this.dragStartPosition.x + transform.x)),\n          y: Math.max(0, Math.round(this.dragStartPosition.y + transform.y))\n        };\n        console.log('🔧 Drag ended - calculated position:', {\n          nodeId: nodeData.id,\n          dragStartPosition: this.dragStartPosition,\n          transform: transform,\n          newAbsolutePosition: absolutePosition\n        });\n        // Emit the new position\n        this.moveNode.emit({\n          nodeId: nodeData.id,\n          position: absolutePosition\n        });\n      }\n    }\n    onNodeMouseDown(event) {\n      if (this.mouseInteractionsEnabled) {\n        // Check for connection starting gesture (Ctrl+click or right-click)\n        if (event.ctrlKey || event.button === 2) {\n          // Start connection from this node\n          event.preventDefault();\n          event.stopPropagation();\n          // Make sure position is up to date before starting connection (like workflow editor)\n          // Position changes handled by moveNode instead\n          this.startConnection.emit({\n            nodeId: this.currentNodeData.id,\n            handleType: 'source',\n            event: event\n          });\n          return;\n        }\n        this.isDragging = true;\n        this.onNodeClick();\n      }\n    }\n    static ɵfac = function BuildAgentNodeComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BuildAgentNodeComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: BuildAgentNodeComponent,\n      selectors: [[\"app-build-agent-node\"]],\n      viewQuery: function BuildAgentNodeComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.nodeElement = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.cdkDrag = _t.first);\n        }\n      },\n      inputs: {\n        node: \"node\",\n        selected: \"selected\",\n        mouseInteractionsEnabled: \"mouseInteractionsEnabled\",\n        canvasMode: \"canvasMode\",\n        executeNodeData: \"executeNodeData\",\n        forceAbsolutePositioning: \"forceAbsolutePositioning\"\n      },\n      outputs: {\n        deleteNode: \"deleteNode\",\n        moveNode: \"moveNode\",\n        nodeSelected: \"nodeSelected\",\n        nodeDoubleClicked: \"nodeDoubleClicked\",\n        startConnection: \"startConnection\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 6,\n      vars: 14,\n      consts: [[\"nodeElement\", \"\", \"dragRef\", \"cdkDrag\"], [\"cdkDrag\", \"\", \"cdkDragBoundary\", \".canvas-container\", 1, \"build-agent-node\", 3, \"cdkDragEnded\", \"cdkDragStarted\", \"mousedown\", \"click\", \"dblclick\", \"mouseenter\", \"mouseleave\", \"cdkDragDisabled\"], [\"class\", \"node-content-execute\", 4, \"ngIf\"], [\"class\", \"node-content-build\", 4, \"ngIf\"], [\"class\", \"delete-btn\", \"title\", \"Delete node\", 3, \"click\", 4, \"ngIf\"], [1, \"node-content-execute\"], [\"iconSize\", \"20\", \"iconColor\", \"white\", 3, \"iconName\"], [\"class\", \"execute-tooltip\", 3, \"innerHTML\", 4, \"ngIf\"], [1, \"execute-tooltip\", 3, \"innerHTML\"], [1, \"node-content-build\"], [1, \"node-icon-section\"], [\"iconSize\", \"24\", \"iconColor\", \"white\", 3, \"iconName\"], [1, \"node-label-section\"], [1, \"node-label\"], [\"title\", \"Delete node\", 1, \"delete-btn\", 3, \"click\"], [\"width\", \"12\", \"height\", \"12\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\"], [\"x1\", \"18\", \"y1\", \"6\", \"x2\", \"6\", \"y2\", \"18\"], [\"x1\", \"6\", \"y1\", \"6\", \"x2\", \"18\", \"y2\", \"18\"]],\n      template: function BuildAgentNodeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1, 0);\n          i0.ɵɵlistener(\"cdkDragEnded\", function BuildAgentNodeComponent_Template_div_cdkDragEnded_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onDragEnded($event));\n          })(\"cdkDragStarted\", function BuildAgentNodeComponent_Template_div_cdkDragStarted_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onDragStarted($event));\n          })(\"mousedown\", function BuildAgentNodeComponent_Template_div_mousedown_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onNodeMouseDown($event));\n          })(\"click\", function BuildAgentNodeComponent_Template_div_click_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onNodeClick());\n          })(\"dblclick\", function BuildAgentNodeComponent_Template_div_dblclick_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onNodeDoubleClick());\n          })(\"mouseenter\", function BuildAgentNodeComponent_Template_div_mouseenter_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onMouseEnter());\n          })(\"mouseleave\", function BuildAgentNodeComponent_Template_div_mouseleave_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onMouseLeave());\n          });\n          i0.ɵɵtemplate(3, BuildAgentNodeComponent_div_3_Template, 3, 2, \"div\", 2)(4, BuildAgentNodeComponent_div_4_Template, 6, 2, \"div\", 3)(5, BuildAgentNodeComponent_button_5_Template, 4, 0, \"button\", 4);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"selected\", ctx.isCurrentlySelected)(\"dragging\", ctx.isDragging)(\"execute-mode\", ctx.canvasMode === \"execute\")(\"force-absolute\", false);\n          i0.ɵɵproperty(\"cdkDragDisabled\", !ctx.mouseInteractionsEnabled || ctx.canvasMode === \"execute\");\n          i0.ɵɵattribute(\"data-node-id\", ctx.isExecuteMode ? \"execute-\" + (ctx.executeNodeData == null ? null : ctx.executeNodeData.type) : ctx.currentNodeData.id)(\"data-node-type\", ctx.isExecuteMode ? ctx.executeNodeData == null ? null : ctx.executeNodeData.type : ctx.currentNodeData.type);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.canvasMode === \"execute\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.canvasMode !== \"execute\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.canvasMode !== \"execute\");\n        }\n      },\n      dependencies: [CommonModule, i1.NgIf, DragDropModule, i2.CdkDrag, IconComponent],\n      styles: [\".build-agent-node[_ngcontent-%COMP%] {\\n  position: relative;\\n  cursor: pointer;\\n  -webkit-user-select: none;\\n          user-select: none;\\n  transition: opacity 0.1s ease;\\n  display: inline-block;\\n  margin: 0;\\n  padding: 0;\\n  box-sizing: border-box;\\n  width: auto;\\n  height: 48px;\\n}\\n.build-agent-node.selected[_ngcontent-%COMP%] {\\n  z-index: 10;\\n}\\n.build-agent-node.dragging[_ngcontent-%COMP%] {\\n  z-index: 20;\\n  opacity: 0.9;\\n  transition: none;\\n}\\n.build-agent-node.disabled[_ngcontent-%COMP%] {\\n  pointer-events: none;\\n  opacity: 0.5;\\n}\\n.build-agent-node.execute-mode[_ngcontent-%COMP%] {\\n  width: 55px;\\n  height: 55px;\\n  background: white;\\n  border-radius: 40px;\\n  border: 7px solid white;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  margin: 0 auto;\\n  position: absolute !important;\\n  transform: none !important;\\n  left: var(--node-x, 0px) !important;\\n  top: var(--node-y, 0px) !important;\\n}\\n.build-agent-node.force-absolute[_ngcontent-%COMP%] {\\n  position: absolute !important;\\n  transform: none !important;\\n  left: var(--node-x, 0px) !important;\\n  top: var(--node-y, 0px) !important;\\n}\\n.build-agent-node[_ngcontent-%COMP%]   .node-content-build[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background: white;\\n  border-radius: 40px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  height: auto;\\n  padding: 4px;\\n  padding-right: 16px;\\n  position: relative;\\n  transition: none;\\n  min-width: fit-content;\\n}\\n.build-agent-node[_ngcontent-%COMP%]   .node-content-build[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  left: 34px;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 8px;\\n  height: 8px;\\n  background: #9ca3af;\\n  border-radius: 50%;\\n  z-index: 0;\\n  opacity: 0;\\n  transition: opacity 0.2s ease;\\n}\\n.build-agent-node[_ngcontent-%COMP%]   .node-content-build[_ngcontent-%COMP%]   .node-icon-section[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n  z-index: 2;\\n  position: relative;\\n}\\n.build-agent-node[_ngcontent-%COMP%]   .node-content-build[_ngcontent-%COMP%]   .node-icon-section[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  object-fit: contain;\\n  filter: brightness(0) invert(1);\\n}\\n.build-agent-node[_ngcontent-%COMP%]   .node-content-build[_ngcontent-%COMP%]   .node-label-section[_ngcontent-%COMP%] {\\n  padding: 0 0 0 12px;\\n  display: flex;\\n  align-items: center;\\n  height: 100%;\\n}\\n.build-agent-node[_ngcontent-%COMP%]   .node-content-build[_ngcontent-%COMP%]   .node-label-section[_ngcontent-%COMP%]   .node-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #1f2937;\\n  white-space: nowrap;\\n  line-height: 1;\\n  max-width: 140px;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n.build-agent-node[_ngcontent-%COMP%]   .node-content-build[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n  transition: box-shadow 0.1s ease;\\n}\\n.build-agent-node[_ngcontent-%COMP%]   .node-content-build[_ngcontent-%COMP%]:hover::before {\\n  opacity: 1;\\n  transition: opacity 0.1s ease;\\n}\\n.build-agent-node[_ngcontent-%COMP%]   .node-content-execute[_ngcontent-%COMP%] {\\n  width: 55px;\\n  height: 55px;\\n  border-radius: 50%;\\n  border: 7px solid white;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n  position: relative;\\n  margin: 0 auto;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  transition: none;\\n}\\n.build-agent-node[_ngcontent-%COMP%]   .node-content-execute[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  object-fit: contain;\\n  filter: brightness(0) invert(1);\\n}\\n.build-agent-node[_ngcontent-%COMP%]   .node-content-execute[_ngcontent-%COMP%]   lucide-icon[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n.build-agent-node[data-node-type=prompt][_ngcontent-%COMP%]   .node-content-build[_ngcontent-%COMP%]   .node-icon-section[_ngcontent-%COMP%] {\\n  background: linear-gradient(180deg, #8be1eb 0%, #028697 100%);\\n}\\n.build-agent-node[data-node-type=model][_ngcontent-%COMP%]   .node-content-build[_ngcontent-%COMP%]   .node-icon-section[_ngcontent-%COMP%] {\\n  background: linear-gradient(180deg, #fec47e 0%, #db770c 100%);\\n}\\n.build-agent-node[data-node-type=knowledge][_ngcontent-%COMP%]   .node-content-build[_ngcontent-%COMP%]   .node-icon-section[_ngcontent-%COMP%] {\\n  background: linear-gradient(180deg, #f598b7 0%, #d41b5a 100%);\\n}\\n.build-agent-node[data-node-type=tool][_ngcontent-%COMP%]   .node-content-build[_ngcontent-%COMP%]   .node-icon-section[_ngcontent-%COMP%] {\\n  background: linear-gradient(180deg, #81d39f 0%, #179ead 100%);\\n}\\n.build-agent-node[data-node-type=guardrail][_ngcontent-%COMP%]   .node-content-build[_ngcontent-%COMP%]   .node-icon-section[_ngcontent-%COMP%] {\\n  background: linear-gradient(180deg, #b9a4de 0%, #5e35a7 100%);\\n}\\n.build-agent-node[data-node-type=prompt][_ngcontent-%COMP%]   .node-content-execute[_ngcontent-%COMP%] {\\n  background: linear-gradient(180deg, #8be1eb 0%, #028697 100%);\\n}\\n.build-agent-node[data-node-type=model][_ngcontent-%COMP%]   .node-content-execute[_ngcontent-%COMP%] {\\n  background: linear-gradient(180deg, #fec47e 0%, #db770c 100%);\\n}\\n.build-agent-node[data-node-type=knowledge][_ngcontent-%COMP%]   .node-content-execute[_ngcontent-%COMP%] {\\n  background: linear-gradient(180deg, #f598b7 0%, #d41b5a 100%);\\n}\\n.build-agent-node[data-node-type=tool][_ngcontent-%COMP%]   .node-content-execute[_ngcontent-%COMP%] {\\n  background: linear-gradient(180deg, #81d39f 0%, #179ead 100%);\\n}\\n.build-agent-node[data-node-type=guardrail][_ngcontent-%COMP%]   .node-content-execute[_ngcontent-%COMP%] {\\n  background: linear-gradient(180deg, #b9a4de 0%, #5e35a7 100%);\\n}\\n.build-agent-node.selected[_ngcontent-%COMP%]   .node-content-build[_ngcontent-%COMP%] {\\n  box-shadow: 0 0 0 2px #3b82f6;\\n}\\n.build-agent-node.selected[_ngcontent-%COMP%]   .node-content-execute[_ngcontent-%COMP%] {\\n  box-shadow: 0 0 0 2px #3b82f6;\\n}\\n.build-agent-node[_ngcontent-%COMP%]   .delete-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -8px;\\n  right: -8px;\\n  width: 20px;\\n  height: 20px;\\n  background: #ef4444;\\n  border: none;\\n  border-radius: 50%;\\n  color: white;\\n  cursor: pointer;\\n  display: none;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 10;\\n  transition: all 0.2s ease;\\n}\\n.build-agent-node[_ngcontent-%COMP%]   .delete-btn[_ngcontent-%COMP%]:hover {\\n  background: #dc2626;\\n  transform: scale(1.1);\\n}\\n.build-agent-node[_ngcontent-%COMP%]   .delete-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 10px;\\n  height: 10px;\\n}\\n.build-agent-node[_ngcontent-%COMP%]:hover   .delete-btn[_ngcontent-%COMP%], .build-agent-node.selected[_ngcontent-%COMP%]   .delete-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n.build-agent-node[_ngcontent-%COMP%]   .connection-point[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n.build-agent-node[_ngcontent-%COMP%]   .execute-tooltip[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 100%;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  background: rgba(0, 0, 0, 0.85);\\n  backdrop-filter: blur(8px);\\n  -webkit-backdrop-filter: blur(8px);\\n  color: white;\\n  padding: 10px 14px;\\n  border-radius: 8px;\\n  font-size: 12px;\\n  font-weight: 500;\\n  z-index: 9999;\\n  margin-right: 10px;\\n  text-align: left;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2), 0 2px 8px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);\\n  border: 1px solid rgba(255, 255, 255, 0.1);\\n  min-width: 150px;\\n  max-width: 400px;\\n  width: auto;\\n  height: auto;\\n  max-height: 300px !important;\\n  white-space: pre-line !important;\\n  overflow-y: auto !important;\\n  overflow-x: hidden !important;\\n  word-wrap: break-word !important;\\n  text-overflow: clip !important;\\n  -webkit-line-clamp: unset !important;\\n  line-clamp: unset !important;\\n  -webkit-box-orient: unset !important;\\n  display: block !important;\\n  line-height: 1.4;\\n  scrollbar-width: thin;\\n  scrollbar-color: rgba(255, 255, 255, 0.3) rgba(255, 255, 255, 0.1);\\n}\\n.build-agent-node[_ngcontent-%COMP%]   .execute-tooltip[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px !important;\\n  height: 6px !important;\\n}\\n.build-agent-node[_ngcontent-%COMP%]   .execute-tooltip[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: rgba(255, 255, 255, 0.1) !important;\\n  border-radius: 3px;\\n}\\n.build-agent-node[_ngcontent-%COMP%]   .execute-tooltip[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(255, 255, 255, 0.4) !important;\\n  border-radius: 3px;\\n}\\n.build-agent-node[_ngcontent-%COMP%]   .execute-tooltip[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: rgba(255, 255, 255, 0.6) !important;\\n}\\n.build-agent-node[_ngcontent-%COMP%]   .execute-tooltip[_ngcontent-%COMP%]::-webkit-scrollbar-corner {\\n  background: rgba(255, 255, 255, 0.1);\\n}\\n.build-agent-node[_ngcontent-%COMP%]   .execute-tooltip[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  left: 100%;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 0;\\n  height: 0;\\n  border-style: solid;\\n  border-width: 8px 0 8px 10px;\\n  border-color: transparent transparent transparent rgba(0, 0, 0, 0.85);\\n  filter: drop-shadow(1px 0 2px rgba(0, 0, 0, 0.2));\\n  margin-left: -1px;\\n}\\n.build-agent-node[_ngcontent-%COMP%]   .execute-tooltip[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  left: 100%;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 0;\\n  height: 0;\\n  border-style: solid;\\n  border-width: 9px 0 9px 11px;\\n  border-color: transparent transparent transparent rgba(0, 0, 0, 0.3);\\n  z-index: -1;\\n  margin-left: -2px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return BuildAgentNodeComponent;\n})();", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "DragDropModule", "IconComponent", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r1", "executeTooltipText", "ɵɵsanitizeHtml", "ɵɵelementStart", "ɵɵtemplate", "BuildAgentNodeComponent_div_3_div_2_Template", "ɵɵelementEnd", "ɵɵadvance", "iconName", "showTooltip", "ɵɵtext", "ɵɵtextInterpolate", "currentNodeData", "name", "ɵɵlistener", "BuildAgentNodeComponent_button_5_Template_button_click_0_listener", "$event", "ɵɵrestoreView", "_r3", "ɵɵnextContext", "ɵɵresetView", "onDeleteClick", "BuildAgentNodeComponent", "nodeElement", "cdkDrag", "node", "selected", "mouseInteractionsEnabled", "canvasMode", "executeNodeData", "forceAbsolutePositioning", "hasBeenDragged", "dragStartPosition", "x", "y", "deleteNode", "moveNode", "nodeSelected", "nodeDoubleClicked", "startConnection", "isDragging", "nodeConfig", "prompt", "icon", "useAsset", "model", "knowledge", "tool", "guardrail", "ngOnChanges", "changes", "currentValue", "currentNode", "previousNode", "previousValue", "hasPositionChanged", "isExecuteMode", "setCSSPosition", "data", "position", "updateCdkDragPosition", "current", "previous", "ngAfterViewInit", "nodeData", "setTimeout", "console", "log", "nodeId", "id", "hasCdkDrag", "nativeElement", "element", "style", "left", "top", "transform", "error", "warn", "nodeName", "hasElement", "shouldUseAbsolute", "shouldUseAbsolutePositioning", "setProperty", "nodeX", "getPropertyValue", "nodeY", "computedStyle", "window", "getComputedStyle", "result", "nodeNames", "nodes", "map", "length", "typeLabel", "type", "join", "isTooltipMultiLine", "includes", "nodePosition", "isCurrentlySelected", "currentConfig", "shouldUseAssetIcon", "config", "onImageError", "event", "fallbackIcon", "target", "src", "onNodeClick", "emit", "onNodeDoubleClick", "onMouseEnter", "onMouseLeave", "stopPropagation", "onDragStarted", "parseInt", "onDragEnded", "source", "getFreeDragPosition", "absolutePosition", "Math", "max", "round", "newAbsolutePosition", "onNodeMouseDown", "ctrl<PERSON>ey", "button", "preventDefault", "handleType", "selectors", "viewQuery", "BuildAgentNodeComponent_Query", "rf", "ctx", "BuildAgentNodeComponent_Template_div_cdkDragEnded_0_listener", "_r1", "BuildAgentNodeComponent_Template_div_cdkDragStarted_0_listener", "BuildAgentNodeComponent_Template_div_mousedown_0_listener", "BuildAgentNodeComponent_Template_div_click_0_listener", "BuildAgentNodeComponent_Template_div_dblclick_0_listener", "BuildAgentNodeComponent_Template_div_mouseenter_0_listener", "BuildAgentNodeComponent_Template_div_mouseleave_0_listener", "BuildAgentNodeComponent_div_3_Template", "BuildAgentNodeComponent_div_4_Template", "BuildAgentNodeComponent_button_5_Template", "ɵɵclassProp", "i1", "NgIf", "i2", "CdkDrag", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\agents\\build-agents\\components\\build-agent-node\\build-agent-node.component.ts", "C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\agents\\build-agents\\components\\build-agent-node\\build-agent-node.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  Input,\r\n  Output,\r\n  EventEmitter,\r\n  ElementRef,\r\n  ViewChild,\r\n  AfterViewInit,\r\n  OnChanges,\r\n  SimpleChanges,\r\n} from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { DragDropModule, CdkDragEnd, CdkDrag } from '@angular/cdk/drag-drop';\r\nimport { IconComponent } from '@ava/play-comp-library';\r\n\r\nexport interface BuildAgentNodeData {\r\n  id: string;\r\n  type: 'prompt' | 'model' | 'knowledge' | 'tool' | 'guardrail';\r\n  name: string;\r\n  icon?: string;\r\n  position: { x: number; y: number };\r\n  originalToolData?: any; // Store the original tool data for configuration purposes\r\n}\r\n\r\nexport interface ExecuteNodeData {\r\n  type: 'prompt' | 'model' | 'knowledge' | 'tool' | 'guardrail';\r\n  nodes: BuildAgentNodeData[]; // All nodes of this type\r\n  position: { x: number; y: number };\r\n}\r\n\r\n@Component({\r\n  selector: 'app-build-agent-node',\r\n  standalone: true,\r\n  imports: [CommonModule, DragDropModule, IconComponent],\r\n  templateUrl: './build-agent-node.component.html',\r\n  styleUrls: ['./build-agent-node.component.scss'],\r\n})\r\nexport class BuildAgentNodeComponent implements AfterViewInit, OnChanges {\r\n  @ViewChild('nodeElement') nodeElement!: ElementRef;\r\n  @ViewChild('dragRef') cdkDrag!: CdkDrag;\r\n\r\n  @Input() node: any;\r\n  @Input() selected: boolean = false;\r\n  @Input() mouseInteractionsEnabled: boolean = true;\r\n  @Input() canvasMode: string = 'build'; // 'build' or 'execute'\r\n  @Input() executeNodeData?: ExecuteNodeData; // For execute mode consolidated nodes\r\n  @Input() forceAbsolutePositioning: boolean = false; // Force absolute positioning (for edit/view modes)\r\n\r\n  private hasBeenDragged: boolean = false; // Track if node has been dragged by user\r\n  private dragStartPosition: { x: number; y: number } = { x: 0, y: 0 }; // Store drag start position\r\n\r\n  @Output() deleteNode = new EventEmitter<string>();\r\n  @Output() moveNode = new EventEmitter<{\r\n    nodeId: string;\r\n    position: { x: number; y: number };\r\n  }>();\r\n  @Output() nodeSelected = new EventEmitter<string>();\r\n  @Output() nodeDoubleClicked = new EventEmitter<string>();\r\n  // Removed nodePositionChanged to avoid conflicts with moveNode\r\n  @Output() startConnection = new EventEmitter<{\r\n    nodeId: string;\r\n    handleType: 'source' | 'target';\r\n    event: MouseEvent;\r\n  }>();\r\n\r\n  isDragging: boolean = false;\r\n  showTooltip: boolean = false;\r\n\r\n  private readonly nodeConfig = {\r\n    prompt: { icon: 'FileText', useAsset: false },\r\n    model: { icon: 'Box', useAsset: false },\r\n    knowledge: { icon: 'BookOpen', useAsset: false },\r\n    tool: { icon: 'Wrench', useAsset: false },\r\n    guardrail: { icon: 'Swords', useAsset: false },\r\n  };\r\n\r\n  ngOnChanges(changes: SimpleChanges): void {\r\n    if (changes['node']?.currentValue) {\r\n      const currentNode = changes['node'].currentValue;\r\n      const previousNode = changes['node'].previousValue;\r\n\r\n      if (this.hasPositionChanged(currentNode, previousNode)) {\r\n        // Only update position if we're not currently dragging\r\n        if (!this.isDragging) {\r\n          // Use CSS variables for positioning in execute mode, otherwise use CDK Drag\r\n          if (this.isExecuteMode) {\r\n            this.setCSSPosition(currentNode.data.position);\r\n          } else {\r\n            // Always update CDK drag position for non-execute modes\r\n            this.updateCdkDragPosition(currentNode.data.position);\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  private hasPositionChanged(currentNode: any, previousNode: any): boolean {\r\n    if (!currentNode?.data?.position) return false;\r\n    if (!previousNode?.data?.position) return true;\r\n\r\n    const current = currentNode.data.position;\r\n    const previous = previousNode.data.position;\r\n    return current.x !== previous.x || current.y !== previous.y;\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    const nodeData = this.currentNodeData;\r\n    if (nodeData) {\r\n      if (this.isExecuteMode) {\r\n        // Set CSS position immediately for execute mode\r\n        this.setCSSPosition(nodeData.position);\r\n      } else {\r\n        // Set CDK drag position for other modes\r\n        setTimeout(() => {\r\n          this.updateCdkDragPosition(nodeData.position);\r\n        }, 0);\r\n      }\r\n    }\r\n  }\r\n\r\n  private updateCdkDragPosition(position: { x: number; y: number }): void {\r\n    if (this.isDragging) return;\r\n\r\n    console.log('🔧 updateCdkDragPosition called:', {\r\n      nodeId: this.currentNodeData.id,\r\n      position,\r\n      hasCdkDrag: !!this.cdkDrag,\r\n    });\r\n\r\n    // Set absolute position only - let CDK handle its own drag state\r\n    try {\r\n      if (this.nodeElement?.nativeElement) {\r\n        const element = this.nodeElement.nativeElement;\r\n\r\n        // Set absolute positioning\r\n        element.style.position = 'absolute';\r\n        element.style.left = `${position.x}px`;\r\n        element.style.top = `${position.y}px`;\r\n        element.style.transform = 'none';\r\n\r\n        console.log('🔧 Absolute positioning set:', {\r\n          nodeId: this.currentNodeData.id,\r\n          position: position,\r\n          left: element.style.left,\r\n          top: element.style.top,\r\n        });\r\n      }\r\n    } catch (error) {\r\n      console.warn('Error updating position:', error);\r\n    }\r\n  }\r\n\r\n  private setCSSPosition(position: { x: number; y: number }): void {\r\n    console.log('🔧 setCSSPosition called:', {\r\n      nodeId: this.currentNodeData.id,\r\n      nodeName: this.currentNodeData.name,\r\n      position,\r\n      hasElement: !!this.nodeElement?.nativeElement,\r\n      canvasMode: this.canvasMode,\r\n      shouldUseAbsolute: this.shouldUseAbsolutePositioning,\r\n    });\r\n\r\n    if (this.nodeElement?.nativeElement) {\r\n      const element = this.nodeElement.nativeElement;\r\n      element.style.setProperty('--node-x', `${position.x}px`);\r\n      element.style.setProperty('--node-y', `${position.y}px`);\r\n\r\n      console.log('🔧 CSS variables set:', {\r\n        nodeX: element.style.getPropertyValue('--node-x'),\r\n        nodeY: element.style.getPropertyValue('--node-y'),\r\n        computedStyle: window.getComputedStyle(element).position,\r\n      });\r\n    } else {\r\n      console.warn('🔧 setCSSPosition failed - no element available');\r\n    }\r\n  }\r\n\r\n  get currentNodeData(): BuildAgentNodeData {\r\n    return this.node?.data || {};\r\n  }\r\n\r\n  get isExecuteMode(): boolean {\r\n    return this.canvasMode === 'execute';\r\n  }\r\n\r\n  get shouldUseAbsolutePositioning(): boolean {\r\n    // Only use absolute positioning for execute mode or when CDK drag is disabled\r\n    const result = this.isExecuteMode || !this.mouseInteractionsEnabled;\r\n    console.log('🔧 shouldUseAbsolutePositioning check:', {\r\n      nodeId: this.currentNodeData.id,\r\n      isExecuteMode: this.isExecuteMode,\r\n      mouseInteractionsEnabled: this.mouseInteractionsEnabled,\r\n      forceAbsolutePositioning: this.forceAbsolutePositioning,\r\n      hasBeenDragged: this.hasBeenDragged,\r\n      canvasMode: this.canvasMode,\r\n      result,\r\n    });\r\n    return result;\r\n  }\r\n\r\n  get executeTooltipText(): string {\r\n    if (!this.isExecuteMode || !this.executeNodeData) return '';\r\n\r\n    const nodeNames = this.executeNodeData.nodes.map((node) => node.name);\r\n    if (nodeNames.length === 1) {\r\n      return nodeNames[0];\r\n    } else if (nodeNames.length > 1) {\r\n      const typeLabel =\r\n        this.executeNodeData.type === 'knowledge'\r\n          ? 'knowledge bases'\r\n          : `${this.executeNodeData.type}s`;\r\n      return `${nodeNames.length} ${typeLabel}:\\n${nodeNames.join('\\n')}`;\r\n    }\r\n    return '';\r\n  }\r\n\r\n  get isTooltipMultiLine(): boolean {\r\n    return (\r\n      this.executeTooltipText.includes('\\n') ||\r\n      this.executeTooltipText.length > 50\r\n    );\r\n  }\r\n\r\n  get nodePosition(): { x: number; y: number } {\r\n    if (this.isExecuteMode && this.executeNodeData) {\r\n      return this.executeNodeData.position || { x: 0, y: 0 };\r\n    }\r\n    return this.currentNodeData.position || { x: 0, y: 0 };\r\n  }\r\n\r\n  get isCurrentlySelected(): boolean {\r\n    return this.selected;\r\n  }\r\n\r\n  get currentConfig() {\r\n    if (this.isExecuteMode && this.executeNodeData) {\r\n      return (\r\n        this.nodeConfig[this.executeNodeData.type] || this.nodeConfig.prompt\r\n      );\r\n    }\r\n    const nodeData = this.currentNodeData;\r\n    return this.nodeConfig[nodeData.type] || this.nodeConfig.prompt;\r\n  }\r\n\r\n  get shouldUseAssetIcon(): boolean {\r\n    const config = this.currentConfig;\r\n    return config.useAsset || false;\r\n  }\r\n\r\n  get iconName(): string {\r\n    const config = this.currentConfig;\r\n\r\n    if (this.isExecuteMode && this.executeNodeData) {\r\n      // In execute mode, always use Lucide icons\r\n      return config.icon;\r\n    }\r\n\r\n    const nodeData = this.currentNodeData;\r\n\r\n    // For asset icons (prompts), use the node's icon or config icon\r\n    if (config.useAsset) {\r\n      return nodeData.icon || config.icon;\r\n    }\r\n\r\n    // For Lucide icons, always use the config icon (which contains the correct Lucide icon name)\r\n    // Ignore any asset paths that might be in nodeData.icon for non-prompt types\r\n    return config.icon;\r\n  }\r\n\r\n  onImageError(event: any): void {\r\n    // Fallback to the default icon if the image fails to load\r\n    const fallbackIcon = 'assets/images/build.png';\r\n    event.target.src = fallbackIcon;\r\n    console.warn(\r\n      `Failed to load icon for ${this.currentNodeData?.type} node, using fallback:`,\r\n      fallbackIcon,\r\n    );\r\n  }\r\n\r\n  onNodeClick(): void {\r\n    if (this.isExecuteMode) {\r\n      // In execute mode, don't emit selection events\r\n      return;\r\n    }\r\n    if (this.mouseInteractionsEnabled && this.currentNodeData.id) {\r\n      this.nodeSelected.emit(this.currentNodeData.id);\r\n    }\r\n  }\r\n\r\n  onNodeDoubleClick(): void {\r\n    if (this.isExecuteMode) {\r\n      // In execute mode, don't emit double-click events\r\n      return;\r\n    }\r\n    if (this.mouseInteractionsEnabled && this.currentNodeData.id) {\r\n      this.nodeDoubleClicked.emit(this.currentNodeData.id);\r\n    }\r\n  }\r\n\r\n  onMouseEnter(): void {\r\n    if (this.isExecuteMode && this.executeTooltipText) {\r\n      this.showTooltip = true;\r\n    }\r\n  }\r\n\r\n  onMouseLeave(): void {\r\n    if (this.isExecuteMode) {\r\n      this.showTooltip = false;\r\n    }\r\n  }\r\n\r\n  onDeleteClick(event: Event): void {\r\n    event.stopPropagation();\r\n    if (this.currentNodeData.id) {\r\n      this.deleteNode.emit(this.currentNodeData.id);\r\n    }\r\n  }\r\n\r\n  onDragStarted(event: any): void {\r\n    this.isDragging = true;\r\n    this.hasBeenDragged = true; // Mark that user has dragged this node\r\n\r\n    // Store the starting position for manual drag calculation\r\n    const element = this.nodeElement?.nativeElement;\r\n    if (element) {\r\n      this.dragStartPosition = {\r\n        x: parseInt(element.style.left) || 0,\r\n        y: parseInt(element.style.top) || 0,\r\n      };\r\n    }\r\n\r\n    console.log('🔧 Drag started:', {\r\n      nodeId: this.currentNodeData.id,\r\n      hasBeenDragged: this.hasBeenDragged,\r\n      dragStartPosition: this.dragStartPosition,\r\n    });\r\n  }\r\n\r\n  onDragEnded(event: CdkDragEnd): void {\r\n    if (!this.mouseInteractionsEnabled) return;\r\n\r\n    this.isDragging = false;\r\n    const nodeData = this.currentNodeData;\r\n    if (!nodeData?.id) return;\r\n\r\n    // Calculate new position from drag start + CDK transform\r\n    const element = this.nodeElement?.nativeElement;\r\n    if (element) {\r\n      const transform = event.source.getFreeDragPosition();\r\n\r\n      const absolutePosition = {\r\n        x: Math.max(0, Math.round(this.dragStartPosition.x + transform.x)),\r\n        y: Math.max(0, Math.round(this.dragStartPosition.y + transform.y)),\r\n      };\r\n\r\n      console.log('🔧 Drag ended - calculated position:', {\r\n        nodeId: nodeData.id,\r\n        dragStartPosition: this.dragStartPosition,\r\n        transform: transform,\r\n        newAbsolutePosition: absolutePosition,\r\n      });\r\n\r\n      // Emit the new position\r\n      this.moveNode.emit({ nodeId: nodeData.id, position: absolutePosition });\r\n    }\r\n  }\r\n\r\n  onNodeMouseDown(event: MouseEvent): void {\r\n    if (this.mouseInteractionsEnabled) {\r\n      // Check for connection starting gesture (Ctrl+click or right-click)\r\n      if (event.ctrlKey || event.button === 2) {\r\n        // Start connection from this node\r\n        event.preventDefault();\r\n        event.stopPropagation();\r\n\r\n        // Make sure position is up to date before starting connection (like workflow editor)\r\n        // Position changes handled by moveNode instead\r\n\r\n        this.startConnection.emit({\r\n          nodeId: this.currentNodeData.id,\r\n          handleType: 'source',\r\n          event: event,\r\n        });\r\n        return;\r\n      }\r\n\r\n      this.isDragging = true;\r\n      this.onNodeClick();\r\n    }\r\n  }\r\n}\r\n", "<div\r\n  #nodeElement\r\n  class=\"build-agent-node\"\r\n  [class.selected]=\"isCurrentlySelected\"\r\n  [class.dragging]=\"isDragging\"\r\n  [class.execute-mode]=\"canvasMode === 'execute'\"\r\n  [class.force-absolute]=\"false\"\r\n  [attr.data-node-id]=\"\r\n    isExecuteMode ? 'execute-' + executeNodeData?.type : currentNodeData.id\r\n  \"\r\n  [attr.data-node-type]=\"\r\n    isExecuteMode ? executeNodeData?.type : currentNodeData.type\r\n  \"\r\n  cdkDrag\r\n  #dragRef=\"cdkDrag\"\r\n  [cdkDragDisabled]=\"!mouseInteractionsEnabled || canvasMode === 'execute'\"\r\n  cdkDragBoundary=\".canvas-container\"\r\n  (cdkDragEnded)=\"onDragEnded($event)\"\r\n  (cdkDragStarted)=\"onDragStarted($event)\"\r\n  (mousedown)=\"onNodeMouseDown($event)\"\r\n  (click)=\"onNodeClick()\"\r\n  (dblclick)=\"onNodeDoubleClick()\"\r\n  (mouseenter)=\"onMouseEnter()\"\r\n  (mouseleave)=\"onMouseLeave()\"\r\n>\r\n  <div class=\"node-content-execute\" *ngIf=\"canvasMode === 'execute'\">\r\n    <ava-icon [iconName]=\"iconName\" iconSize=\"20\" iconColor=\"white\"> </ava-icon>\r\n    <div\r\n      class=\"execute-tooltip\"\r\n      *ngIf=\"showTooltip && executeTooltipText\"\r\n      [innerHTML]=\"executeTooltipText\"\r\n    ></div>\r\n  </div>\r\n  <div class=\"node-content-build\" *ngIf=\"canvasMode !== 'execute'\">\r\n    <div class=\"node-icon-section\">\r\n      <ava-icon [iconName]=\"iconName\" iconSize=\"24\" iconColor=\"white\">\r\n      </ava-icon>\r\n    </div>\r\n    <div class=\"node-label-section\">\r\n      <span class=\"node-label\">{{ currentNodeData.name }}</span>\r\n    </div>\r\n  </div>\r\n  <button\r\n    class=\"delete-btn\"\r\n    *ngIf=\"canvasMode !== 'execute'\"\r\n    (click)=\"onDeleteClick($event)\"\r\n    title=\"Delete node\"\r\n  >\r\n    <svg\r\n      width=\"12\"\r\n      height=\"12\"\r\n      viewBox=\"0 0 24 24\"\r\n      fill=\"none\"\r\n      stroke=\"currentColor\"\r\n      stroke-width=\"2\"\r\n    >\r\n      <line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\"></line>\r\n      <line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\"></line>\r\n    </svg>\r\n  </button>\r\n</div>\r\n"], "mappings": "AAAA,SAIEA,YAAY,QAMP,eAAe;AACtB,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAA6B,wBAAwB;AAC5E,SAASC,aAAa,QAAQ,wBAAwB;;;;;;;;ICclDC,EAAA,CAAAC,SAAA,aAIO;;;;IADLD,EAAA,CAAAE,UAAA,cAAAC,MAAA,CAAAC,kBAAA,EAAAJ,EAAA,CAAAK,cAAA,CAAgC;;;;;IALpCL,EAAA,CAAAM,cAAA,aAAmE;IACjEN,EAAA,CAAAC,SAAA,kBAA4E;IAC5ED,EAAA,CAAAO,UAAA,IAAAC,4CAAA,iBAIC;IACHR,EAAA,CAAAS,YAAA,EAAM;;;;IANMT,EAAA,CAAAU,SAAA,EAAqB;IAArBV,EAAA,CAAAE,UAAA,aAAAC,MAAA,CAAAQ,QAAA,CAAqB;IAG5BX,EAAA,CAAAU,SAAA,EAAuC;IAAvCV,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAS,WAAA,IAAAT,MAAA,CAAAC,kBAAA,CAAuC;;;;;IAK1CJ,EADF,CAAAM,cAAA,aAAiE,cAChC;IAC7BN,EAAA,CAAAC,SAAA,mBACW;IACbD,EAAA,CAAAS,YAAA,EAAM;IAEJT,EADF,CAAAM,cAAA,cAAgC,eACL;IAAAN,EAAA,CAAAa,MAAA,GAA0B;IAEvDb,EAFuD,CAAAS,YAAA,EAAO,EACtD,EACF;;;;IANQT,EAAA,CAAAU,SAAA,GAAqB;IAArBV,EAAA,CAAAE,UAAA,aAAAC,MAAA,CAAAQ,QAAA,CAAqB;IAINX,EAAA,CAAAU,SAAA,GAA0B;IAA1BV,EAAA,CAAAc,iBAAA,CAAAX,MAAA,CAAAY,eAAA,CAAAC,IAAA,CAA0B;;;;;;IAGvDhB,EAAA,CAAAM,cAAA,iBAKC;IAFCN,EAAA,CAAAiB,UAAA,mBAAAC,kEAAAC,MAAA;MAAAnB,EAAA,CAAAoB,aAAA,CAAAC,GAAA;MAAA,MAAAlB,MAAA,GAAAH,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASpB,MAAA,CAAAqB,aAAA,CAAAL,MAAA,CAAqB;IAAA,EAAC;;IAG/BnB,EAAA,CAAAM,cAAA,cAOC;IAECN,EADA,CAAAC,SAAA,eAA2C,eACA;IAE/CD,EADE,CAAAS,YAAA,EAAM,EACC;;;ADtBX,WAAagB,uBAAuB;EAA9B,MAAOA,uBAAuB;IACRC,WAAW;IACfC,OAAO;IAEpBC,IAAI;IACJC,QAAQ,GAAY,KAAK;IACzBC,wBAAwB,GAAY,IAAI;IACxCC,UAAU,GAAW,OAAO,CAAC,CAAC;IAC9BC,eAAe,CAAmB,CAAC;IACnCC,wBAAwB,GAAY,KAAK,CAAC,CAAC;IAE5CC,cAAc,GAAY,KAAK,CAAC,CAAC;IACjCC,iBAAiB,GAA6B;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAC,CAAE,CAAC,CAAC;IAE5DC,UAAU,GAAG,IAAI1C,YAAY,EAAU;IACvC2C,QAAQ,GAAG,IAAI3C,YAAY,EAGjC;IACM4C,YAAY,GAAG,IAAI5C,YAAY,EAAU;IACzC6C,iBAAiB,GAAG,IAAI7C,YAAY,EAAU;IACxD;IACU8C,eAAe,GAAG,IAAI9C,YAAY,EAIxC;IAEJ+C,UAAU,GAAY,KAAK;IAC3B/B,WAAW,GAAY,KAAK;IAEXgC,UAAU,GAAG;MAC5BC,MAAM,EAAE;QAAEC,IAAI,EAAE,UAAU;QAAEC,QAAQ,EAAE;MAAK,CAAE;MAC7CC,KAAK,EAAE;QAAEF,IAAI,EAAE,KAAK;QAAEC,QAAQ,EAAE;MAAK,CAAE;MACvCE,SAAS,EAAE;QAAEH,IAAI,EAAE,UAAU;QAAEC,QAAQ,EAAE;MAAK,CAAE;MAChDG,IAAI,EAAE;QAAEJ,IAAI,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAK,CAAE;MACzCI,SAAS,EAAE;QAAEL,IAAI,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAK;KAC7C;IAEDK,WAAWA,CAACC,OAAsB;MAChC,IAAIA,OAAO,CAAC,MAAM,CAAC,EAAEC,YAAY,EAAE;QACjC,MAAMC,WAAW,GAAGF,OAAO,CAAC,MAAM,CAAC,CAACC,YAAY;QAChD,MAAME,YAAY,GAAGH,OAAO,CAAC,MAAM,CAAC,CAACI,aAAa;QAElD,IAAI,IAAI,CAACC,kBAAkB,CAACH,WAAW,EAAEC,YAAY,CAAC,EAAE;UACtD;UACA,IAAI,CAAC,IAAI,CAACb,UAAU,EAAE;YACpB;YACA,IAAI,IAAI,CAACgB,aAAa,EAAE;cACtB,IAAI,CAACC,cAAc,CAACL,WAAW,CAACM,IAAI,CAACC,QAAQ,CAAC;YAChD,CAAC,MAAM;cACL;cACA,IAAI,CAACC,qBAAqB,CAACR,WAAW,CAACM,IAAI,CAACC,QAAQ,CAAC;YACvD;UACF;QACF;MACF;IACF;IAEQJ,kBAAkBA,CAACH,WAAgB,EAAEC,YAAiB;MAC5D,IAAI,CAACD,WAAW,EAAEM,IAAI,EAAEC,QAAQ,EAAE,OAAO,KAAK;MAC9C,IAAI,CAACN,YAAY,EAAEK,IAAI,EAAEC,QAAQ,EAAE,OAAO,IAAI;MAE9C,MAAME,OAAO,GAAGT,WAAW,CAACM,IAAI,CAACC,QAAQ;MACzC,MAAMG,QAAQ,GAAGT,YAAY,CAACK,IAAI,CAACC,QAAQ;MAC3C,OAAOE,OAAO,CAAC5B,CAAC,KAAK6B,QAAQ,CAAC7B,CAAC,IAAI4B,OAAO,CAAC3B,CAAC,KAAK4B,QAAQ,CAAC5B,CAAC;IAC7D;IAEA6B,eAAeA,CAAA;MACb,MAAMC,QAAQ,GAAG,IAAI,CAACpD,eAAe;MACrC,IAAIoD,QAAQ,EAAE;QACZ,IAAI,IAAI,CAACR,aAAa,EAAE;UACtB;UACA,IAAI,CAACC,cAAc,CAACO,QAAQ,CAACL,QAAQ,CAAC;QACxC,CAAC,MAAM;UACL;UACAM,UAAU,CAAC,MAAK;YACd,IAAI,CAACL,qBAAqB,CAACI,QAAQ,CAACL,QAAQ,CAAC;UAC/C,CAAC,EAAE,CAAC,CAAC;QACP;MACF;IACF;IAEQC,qBAAqBA,CAACD,QAAkC;MAC9D,IAAI,IAAI,CAACnB,UAAU,EAAE;MAErB0B,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE;QAC9CC,MAAM,EAAE,IAAI,CAACxD,eAAe,CAACyD,EAAE;QAC/BV,QAAQ;QACRW,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC9C;OACpB,CAAC;MAEF;MACA,IAAI;QACF,IAAI,IAAI,CAACD,WAAW,EAAEgD,aAAa,EAAE;UACnC,MAAMC,OAAO,GAAG,IAAI,CAACjD,WAAW,CAACgD,aAAa;UAE9C;UACAC,OAAO,CAACC,KAAK,CAACd,QAAQ,GAAG,UAAU;UACnCa,OAAO,CAACC,KAAK,CAACC,IAAI,GAAG,GAAGf,QAAQ,CAAC1B,CAAC,IAAI;UACtCuC,OAAO,CAACC,KAAK,CAACE,GAAG,GAAG,GAAGhB,QAAQ,CAACzB,CAAC,IAAI;UACrCsC,OAAO,CAACC,KAAK,CAACG,SAAS,GAAG,MAAM;UAEhCV,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE;YAC1CC,MAAM,EAAE,IAAI,CAACxD,eAAe,CAACyD,EAAE;YAC/BV,QAAQ,EAAEA,QAAQ;YAClBe,IAAI,EAAEF,OAAO,CAACC,KAAK,CAACC,IAAI;YACxBC,GAAG,EAAEH,OAAO,CAACC,KAAK,CAACE;WACpB,CAAC;QACJ;MACF,CAAC,CAAC,OAAOE,KAAK,EAAE;QACdX,OAAO,CAACY,IAAI,CAAC,0BAA0B,EAAED,KAAK,CAAC;MACjD;IACF;IAEQpB,cAAcA,CAACE,QAAkC;MACvDO,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;QACvCC,MAAM,EAAE,IAAI,CAACxD,eAAe,CAACyD,EAAE;QAC/BU,QAAQ,EAAE,IAAI,CAACnE,eAAe,CAACC,IAAI;QACnC8C,QAAQ;QACRqB,UAAU,EAAE,CAAC,CAAC,IAAI,CAACzD,WAAW,EAAEgD,aAAa;QAC7C3C,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3BqD,iBAAiB,EAAE,IAAI,CAACC;OACzB,CAAC;MAEF,IAAI,IAAI,CAAC3D,WAAW,EAAEgD,aAAa,EAAE;QACnC,MAAMC,OAAO,GAAG,IAAI,CAACjD,WAAW,CAACgD,aAAa;QAC9CC,OAAO,CAACC,KAAK,CAACU,WAAW,CAAC,UAAU,EAAE,GAAGxB,QAAQ,CAAC1B,CAAC,IAAI,CAAC;QACxDuC,OAAO,CAACC,KAAK,CAACU,WAAW,CAAC,UAAU,EAAE,GAAGxB,QAAQ,CAACzB,CAAC,IAAI,CAAC;QAExDgC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;UACnCiB,KAAK,EAAEZ,OAAO,CAACC,KAAK,CAACY,gBAAgB,CAAC,UAAU,CAAC;UACjDC,KAAK,EAAEd,OAAO,CAACC,KAAK,CAACY,gBAAgB,CAAC,UAAU,CAAC;UACjDE,aAAa,EAAEC,MAAM,CAACC,gBAAgB,CAACjB,OAAO,CAAC,CAACb;SACjD,CAAC;MACJ,CAAC,MAAM;QACLO,OAAO,CAACY,IAAI,CAAC,iDAAiD,CAAC;MACjE;IACF;IAEA,IAAIlE,eAAeA,CAAA;MACjB,OAAO,IAAI,CAACa,IAAI,EAAEiC,IAAI,IAAI,EAAE;IAC9B;IAEA,IAAIF,aAAaA,CAAA;MACf,OAAO,IAAI,CAAC5B,UAAU,KAAK,SAAS;IACtC;IAEA,IAAIsD,4BAA4BA,CAAA;MAC9B;MACA,MAAMQ,MAAM,GAAG,IAAI,CAAClC,aAAa,IAAI,CAAC,IAAI,CAAC7B,wBAAwB;MACnEuC,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE;QACpDC,MAAM,EAAE,IAAI,CAACxD,eAAe,CAACyD,EAAE;QAC/Bb,aAAa,EAAE,IAAI,CAACA,aAAa;QACjC7B,wBAAwB,EAAE,IAAI,CAACA,wBAAwB;QACvDG,wBAAwB,EAAE,IAAI,CAACA,wBAAwB;QACvDC,cAAc,EAAE,IAAI,CAACA,cAAc;QACnCH,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3B8D;OACD,CAAC;MACF,OAAOA,MAAM;IACf;IAEA,IAAIzF,kBAAkBA,CAAA;MACpB,IAAI,CAAC,IAAI,CAACuD,aAAa,IAAI,CAAC,IAAI,CAAC3B,eAAe,EAAE,OAAO,EAAE;MAE3D,MAAM8D,SAAS,GAAG,IAAI,CAAC9D,eAAe,CAAC+D,KAAK,CAACC,GAAG,CAAEpE,IAAI,IAAKA,IAAI,CAACZ,IAAI,CAAC;MACrE,IAAI8E,SAAS,CAACG,MAAM,KAAK,CAAC,EAAE;QAC1B,OAAOH,SAAS,CAAC,CAAC,CAAC;MACrB,CAAC,MAAM,IAAIA,SAAS,CAACG,MAAM,GAAG,CAAC,EAAE;QAC/B,MAAMC,SAAS,GACb,IAAI,CAAClE,eAAe,CAACmE,IAAI,KAAK,WAAW,GACrC,iBAAiB,GACjB,GAAG,IAAI,CAACnE,eAAe,CAACmE,IAAI,GAAG;QACrC,OAAO,GAAGL,SAAS,CAACG,MAAM,IAAIC,SAAS,MAAMJ,SAAS,CAACM,IAAI,CAAC,IAAI,CAAC,EAAE;MACrE;MACA,OAAO,EAAE;IACX;IAEA,IAAIC,kBAAkBA,CAAA;MACpB,OACE,IAAI,CAACjG,kBAAkB,CAACkG,QAAQ,CAAC,IAAI,CAAC,IACtC,IAAI,CAAClG,kBAAkB,CAAC6F,MAAM,GAAG,EAAE;IAEvC;IAEA,IAAIM,YAAYA,CAAA;MACd,IAAI,IAAI,CAAC5C,aAAa,IAAI,IAAI,CAAC3B,eAAe,EAAE;QAC9C,OAAO,IAAI,CAACA,eAAe,CAAC8B,QAAQ,IAAI;UAAE1B,CAAC,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAC,CAAE;MACxD;MACA,OAAO,IAAI,CAACtB,eAAe,CAAC+C,QAAQ,IAAI;QAAE1B,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAC,CAAE;IACxD;IAEA,IAAImE,mBAAmBA,CAAA;MACrB,OAAO,IAAI,CAAC3E,QAAQ;IACtB;IAEA,IAAI4E,aAAaA,CAAA;MACf,IAAI,IAAI,CAAC9C,aAAa,IAAI,IAAI,CAAC3B,eAAe,EAAE;QAC9C,OACE,IAAI,CAACY,UAAU,CAAC,IAAI,CAACZ,eAAe,CAACmE,IAAI,CAAC,IAAI,IAAI,CAACvD,UAAU,CAACC,MAAM;MAExE;MACA,MAAMsB,QAAQ,GAAG,IAAI,CAACpD,eAAe;MACrC,OAAO,IAAI,CAAC6B,UAAU,CAACuB,QAAQ,CAACgC,IAAI,CAAC,IAAI,IAAI,CAACvD,UAAU,CAACC,MAAM;IACjE;IAEA,IAAI6D,kBAAkBA,CAAA;MACpB,MAAMC,MAAM,GAAG,IAAI,CAACF,aAAa;MACjC,OAAOE,MAAM,CAAC5D,QAAQ,IAAI,KAAK;IACjC;IAEA,IAAIpC,QAAQA,CAAA;MACV,MAAMgG,MAAM,GAAG,IAAI,CAACF,aAAa;MAEjC,IAAI,IAAI,CAAC9C,aAAa,IAAI,IAAI,CAAC3B,eAAe,EAAE;QAC9C;QACA,OAAO2E,MAAM,CAAC7D,IAAI;MACpB;MAEA,MAAMqB,QAAQ,GAAG,IAAI,CAACpD,eAAe;MAErC;MACA,IAAI4F,MAAM,CAAC5D,QAAQ,EAAE;QACnB,OAAOoB,QAAQ,CAACrB,IAAI,IAAI6D,MAAM,CAAC7D,IAAI;MACrC;MAEA;MACA;MACA,OAAO6D,MAAM,CAAC7D,IAAI;IACpB;IAEA8D,YAAYA,CAACC,KAAU;MACrB;MACA,MAAMC,YAAY,GAAG,yBAAyB;MAC9CD,KAAK,CAACE,MAAM,CAACC,GAAG,GAAGF,YAAY;MAC/BzC,OAAO,CAACY,IAAI,CACV,2BAA2B,IAAI,CAAClE,eAAe,EAAEoF,IAAI,wBAAwB,EAC7EW,YAAY,CACb;IACH;IAEAG,WAAWA,CAAA;MACT,IAAI,IAAI,CAACtD,aAAa,EAAE;QACtB;QACA;MACF;MACA,IAAI,IAAI,CAAC7B,wBAAwB,IAAI,IAAI,CAACf,eAAe,CAACyD,EAAE,EAAE;QAC5D,IAAI,CAAChC,YAAY,CAAC0E,IAAI,CAAC,IAAI,CAACnG,eAAe,CAACyD,EAAE,CAAC;MACjD;IACF;IAEA2C,iBAAiBA,CAAA;MACf,IAAI,IAAI,CAACxD,aAAa,EAAE;QACtB;QACA;MACF;MACA,IAAI,IAAI,CAAC7B,wBAAwB,IAAI,IAAI,CAACf,eAAe,CAACyD,EAAE,EAAE;QAC5D,IAAI,CAAC/B,iBAAiB,CAACyE,IAAI,CAAC,IAAI,CAACnG,eAAe,CAACyD,EAAE,CAAC;MACtD;IACF;IAEA4C,YAAYA,CAAA;MACV,IAAI,IAAI,CAACzD,aAAa,IAAI,IAAI,CAACvD,kBAAkB,EAAE;QACjD,IAAI,CAACQ,WAAW,GAAG,IAAI;MACzB;IACF;IAEAyG,YAAYA,CAAA;MACV,IAAI,IAAI,CAAC1D,aAAa,EAAE;QACtB,IAAI,CAAC/C,WAAW,GAAG,KAAK;MAC1B;IACF;IAEAY,aAAaA,CAACqF,KAAY;MACxBA,KAAK,CAACS,eAAe,EAAE;MACvB,IAAI,IAAI,CAACvG,eAAe,CAACyD,EAAE,EAAE;QAC3B,IAAI,CAAClC,UAAU,CAAC4E,IAAI,CAAC,IAAI,CAACnG,eAAe,CAACyD,EAAE,CAAC;MAC/C;IACF;IAEA+C,aAAaA,CAACV,KAAU;MACtB,IAAI,CAAClE,UAAU,GAAG,IAAI;MACtB,IAAI,CAACT,cAAc,GAAG,IAAI,CAAC,CAAC;MAE5B;MACA,MAAMyC,OAAO,GAAG,IAAI,CAACjD,WAAW,EAAEgD,aAAa;MAC/C,IAAIC,OAAO,EAAE;QACX,IAAI,CAACxC,iBAAiB,GAAG;UACvBC,CAAC,EAAEoF,QAAQ,CAAC7C,OAAO,CAACC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC;UACpCxC,CAAC,EAAEmF,QAAQ,CAAC7C,OAAO,CAACC,KAAK,CAACE,GAAG,CAAC,IAAI;SACnC;MACH;MAEAT,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE;QAC9BC,MAAM,EAAE,IAAI,CAACxD,eAAe,CAACyD,EAAE;QAC/BtC,cAAc,EAAE,IAAI,CAACA,cAAc;QACnCC,iBAAiB,EAAE,IAAI,CAACA;OACzB,CAAC;IACJ;IAEAsF,WAAWA,CAACZ,KAAiB;MAC3B,IAAI,CAAC,IAAI,CAAC/E,wBAAwB,EAAE;MAEpC,IAAI,CAACa,UAAU,GAAG,KAAK;MACvB,MAAMwB,QAAQ,GAAG,IAAI,CAACpD,eAAe;MACrC,IAAI,CAACoD,QAAQ,EAAEK,EAAE,EAAE;MAEnB;MACA,MAAMG,OAAO,GAAG,IAAI,CAACjD,WAAW,EAAEgD,aAAa;MAC/C,IAAIC,OAAO,EAAE;QACX,MAAMI,SAAS,GAAG8B,KAAK,CAACa,MAAM,CAACC,mBAAmB,EAAE;QAEpD,MAAMC,gBAAgB,GAAG;UACvBxF,CAAC,EAAEyF,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,KAAK,CAAC,IAAI,CAAC5F,iBAAiB,CAACC,CAAC,GAAG2C,SAAS,CAAC3C,CAAC,CAAC,CAAC;UAClEC,CAAC,EAAEwF,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,KAAK,CAAC,IAAI,CAAC5F,iBAAiB,CAACE,CAAC,GAAG0C,SAAS,CAAC1C,CAAC,CAAC;SAClE;QAEDgC,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE;UAClDC,MAAM,EAAEJ,QAAQ,CAACK,EAAE;UACnBrC,iBAAiB,EAAE,IAAI,CAACA,iBAAiB;UACzC4C,SAAS,EAAEA,SAAS;UACpBiD,mBAAmB,EAAEJ;SACtB,CAAC;QAEF;QACA,IAAI,CAACrF,QAAQ,CAAC2E,IAAI,CAAC;UAAE3C,MAAM,EAAEJ,QAAQ,CAACK,EAAE;UAAEV,QAAQ,EAAE8D;QAAgB,CAAE,CAAC;MACzE;IACF;IAEAK,eAAeA,CAACpB,KAAiB;MAC/B,IAAI,IAAI,CAAC/E,wBAAwB,EAAE;QACjC;QACA,IAAI+E,KAAK,CAACqB,OAAO,IAAIrB,KAAK,CAACsB,MAAM,KAAK,CAAC,EAAE;UACvC;UACAtB,KAAK,CAACuB,cAAc,EAAE;UACtBvB,KAAK,CAACS,eAAe,EAAE;UAEvB;UACA;UAEA,IAAI,CAAC5E,eAAe,CAACwE,IAAI,CAAC;YACxB3C,MAAM,EAAE,IAAI,CAACxD,eAAe,CAACyD,EAAE;YAC/B6D,UAAU,EAAE,QAAQ;YACpBxB,KAAK,EAAEA;WACR,CAAC;UACF;QACF;QAEA,IAAI,CAAClE,UAAU,GAAG,IAAI;QACtB,IAAI,CAACsE,WAAW,EAAE;MACpB;IACF;;uCAhWWxF,uBAAuB;IAAA;;YAAvBA,uBAAuB;MAAA6G,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UCrCpCzI,EAAA,CAAAM,cAAA,gBAwBC;UADCN,EANA,CAAAiB,UAAA,0BAAA0H,6DAAAxH,MAAA;YAAAnB,EAAA,CAAAoB,aAAA,CAAAwH,GAAA;YAAA,OAAA5I,EAAA,CAAAuB,WAAA,CAAgBmH,GAAA,CAAAjB,WAAA,CAAAtG,MAAA,CAAmB;UAAA,EAAC,4BAAA0H,+DAAA1H,MAAA;YAAAnB,EAAA,CAAAoB,aAAA,CAAAwH,GAAA;YAAA,OAAA5I,EAAA,CAAAuB,WAAA,CAClBmH,GAAA,CAAAnB,aAAA,CAAApG,MAAA,CAAqB;UAAA,EAAC,uBAAA2H,0DAAA3H,MAAA;YAAAnB,EAAA,CAAAoB,aAAA,CAAAwH,GAAA;YAAA,OAAA5I,EAAA,CAAAuB,WAAA,CAC3BmH,GAAA,CAAAT,eAAA,CAAA9G,MAAA,CAAuB;UAAA,EAAC,mBAAA4H,sDAAA;YAAA/I,EAAA,CAAAoB,aAAA,CAAAwH,GAAA;YAAA,OAAA5I,EAAA,CAAAuB,WAAA,CAC5BmH,GAAA,CAAAzB,WAAA,EAAa;UAAA,EAAC,sBAAA+B,yDAAA;YAAAhJ,EAAA,CAAAoB,aAAA,CAAAwH,GAAA;YAAA,OAAA5I,EAAA,CAAAuB,WAAA,CACXmH,GAAA,CAAAvB,iBAAA,EAAmB;UAAA,EAAC,wBAAA8B,2DAAA;YAAAjJ,EAAA,CAAAoB,aAAA,CAAAwH,GAAA;YAAA,OAAA5I,EAAA,CAAAuB,WAAA,CAClBmH,GAAA,CAAAtB,YAAA,EAAc;UAAA,EAAC,wBAAA8B,2DAAA;YAAAlJ,EAAA,CAAAoB,aAAA,CAAAwH,GAAA;YAAA,OAAA5I,EAAA,CAAAuB,WAAA,CACfmH,GAAA,CAAArB,YAAA,EAAc;UAAA,EAAC;UAmB7BrH,EAjBA,CAAAO,UAAA,IAAA4I,sCAAA,iBAAmE,IAAAC,sCAAA,iBAQF,IAAAC,yCAAA,oBAchE;UAaHrJ,EAAA,CAAAS,YAAA,EAAM;;;UAtDJT,EAHA,CAAAsJ,WAAA,aAAAZ,GAAA,CAAAlC,mBAAA,CAAsC,aAAAkC,GAAA,CAAA/F,UAAA,CACT,iBAAA+F,GAAA,CAAA3G,UAAA,eACkB,yBACjB;UAS9B/B,EAAA,CAAAE,UAAA,qBAAAwI,GAAA,CAAA5G,wBAAA,IAAA4G,GAAA,CAAA3G,UAAA,eAAyE;;UAUtC/B,EAAA,CAAAU,SAAA,GAA8B;UAA9BV,EAAA,CAAAE,UAAA,SAAAwI,GAAA,CAAA3G,UAAA,eAA8B;UAQhC/B,EAAA,CAAAU,SAAA,EAA8B;UAA9BV,EAAA,CAAAE,UAAA,SAAAwI,GAAA,CAAA3G,UAAA,eAA8B;UAW5D/B,EAAA,CAAAU,SAAA,EAA8B;UAA9BV,EAAA,CAAAE,UAAA,SAAAwI,GAAA,CAAA3G,UAAA,eAA8B;;;qBDXvBlC,YAAY,EAAA0J,EAAA,CAAAC,IAAA,EAAE1J,cAAc,EAAA2J,EAAA,CAAAC,OAAA,EAAE3J,aAAa;MAAA4J,MAAA;IAAA;;SAI1ClI,uBAAuB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}