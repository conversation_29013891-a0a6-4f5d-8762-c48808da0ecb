{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { HeaderComponent } from '@awe/play-comp-library';\nimport { IconComponent, ButtonComponent } from '@ava/play-comp-library';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@shared/index\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nfunction NavHeaderComponent_ava_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ava-button\", 21);\n    i0.ɵɵlistener(\"userClick\", function NavHeaderComponent_ava_button_6_Template_ava_button_userClick_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navigate());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"pill\", true);\n  }\n}\nfunction NavHeaderComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"ava-button\", 23);\n    i0.ɵɵlistener(\"userClick\", function NavHeaderComponent_div_7_Template_ava_button_userClick_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleAscendionDropdown());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 3)(3, \"div\", 24)(4, \"div\", 25)(5, \"div\", 26);\n    i0.ɵɵlistener(\"click\", function NavHeaderComponent_div_7_Template_div_click_5_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.selectAscendionOption(\"EE\"));\n    });\n    i0.ɵɵelementStart(6, \"span\", 20);\n    i0.ɵɵtext(7, \"Experience Engineering\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 26);\n    i0.ɵɵlistener(\"click\", function NavHeaderComponent_div_7_Template_div_click_8_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.selectAscendionOption(\"PE\"));\n    });\n    i0.ɵɵelementStart(9, \"span\", 20);\n    i0.ɵɵtext(10, \"Product Engineering\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 26);\n    i0.ɵɵlistener(\"click\", function NavHeaderComponent_div_7_Template_div_click_11_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.selectAscendionOption(\"QE\"));\n    });\n    i0.ɵɵelementStart(12, \"span\", 20);\n    i0.ɵɵtext(13, \"Quality Engineering\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", ctx_r1.selectedAscendionOption)(\"pill\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"visible\", ctx_r1.ascendionDropdownOpen);\n  }\n}\nfunction NavHeaderComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.userEmail);\n  }\n}\nexport let NavHeaderComponent = /*#__PURE__*/(() => {\n  class NavHeaderComponent {\n    authService;\n    tokenStorageService;\n    router;\n    location;\n    isLaunchpadRoute = false;\n    redirectUrl = '';\n    profileDropdownOpen = false;\n    ascendionDropdownOpen = false;\n    selectedAscendionOption = 'Experience Engineering'; // Default label\n    userName = '';\n    userEmail = '';\n    userSelected = new EventEmitter();\n    ascendionOptionSelected = new EventEmitter();\n    users = [{\n      name: 'Akshay',\n      role: 'Project Team',\n      type: 'Project Team'\n    }];\n    constructor(authService, tokenStorageService, router, location) {\n      this.authService = authService;\n      this.tokenStorageService = tokenStorageService;\n      this.router = router;\n      this.location = location;\n    }\n    ngOnInit() {\n      const authConfig = this.authService.getAuthConfig();\n      this.redirectUrl = authConfig?.redirectUrl || window.location.origin;\n      this.loadUserData();\n      this.selectUser(this.users[0]);\n    }\n    // Handle logout\n    onLogout() {\n      if (this.tokenStorageService.getLoginType() === 'basic') {\n        this.authService.basicLogout().subscribe({\n          next: () => {\n            // Use Angular router instead of window.location\n            this.router.navigate(['/login']);\n            this.tokenStorageService.deleteCookie('org_path');\n          },\n          error: error => {\n            console.error('Basic logout failed:', error);\n            // Still try to navigate to login even if logout fails\n            this.router.navigate(['/login']);\n          }\n        });\n      } else {\n        this.authService.logout(this.redirectUrl).subscribe({\n          next: () => {\n            this.tokenStorageService.deleteCookie('org_path');\n          },\n          error: error => {\n            console.error('SSO logout failed:', error);\n          }\n        });\n      }\n    }\n    onLogin() {\n      try {\n        // this.logger.info('Triggering login...');\n        const currentUrl = window.location.origin;\n        this.authService.loginSSO(currentUrl).subscribe({\n          // next: () => this.logger.debug('Login successful'),\n          // error: (error) => this.logger.error('Login failed:', error),\n        });\n      } catch (error) {\n        // this.logger.error('Error during login:', error);\n        // this.toastService.error('Login failed');\n      }\n    }\n    // Load user data from cookies\n    loadUserData() {\n      this.userName = this.tokenStorageService.getDaName() || 'User';\n      this.userEmail = this.tokenStorageService.getDaUsername() || '<EMAIL>';\n    }\n    getInitials(name) {\n      if (!name) return '';\n      const parts = name.split(' ');\n      if (parts.length === 1) return parts[0].charAt(0).toUpperCase();\n      return `${parts[0].charAt(0)}${parts[parts.length - 1].charAt(0)}`.toUpperCase();\n    }\n    // Toggle profile dropdown\n    toggleProfileDropdown() {\n      this.profileDropdownOpen = !this.profileDropdownOpen;\n      // Close nav dropdowns when profile dropdown opens\n      // if (this.profileDropdownOpen) {\n      //   this.closeAllDropdowns();\n      // }\n    }\n    // Toggle Ascendion dropdown\n    toggleAscendionDropdown() {\n      this.ascendionDropdownOpen = !this.ascendionDropdownOpen;\n    }\n    selectUser(user) {\n      this.userSelected.emit(user);\n    }\n    selectAscendionOption(option) {\n      const selectedLabel = option === 'EE' ? 'Experience Engineering' : option === 'PE' ? 'Product Engineering' : option === 'QE' ? 'Quality Engineering' : option;\n      // Update the button label\n      this.selectedAscendionOption = selectedLabel;\n      console.log('Selected label:', selectedLabel);\n      // Emit the selected option to parent components\n      this.ascendionOptionSelected.emit(option);\n      this.ascendionDropdownOpen = false; // Close dropdown after selection\n    }\n    onDocumentClick(event) {\n      const target = event.target;\n      const clickedInsideProfile = !!target.closest('.profile-container') || !!target.closest('.profile-dropdown');\n      const clickedInsideAscendion = !!target.closest('.ascendion-container') || !!target.closest('.ascendion-dropdown');\n      if (!clickedInsideProfile) {\n        this.profileDropdownOpen = false;\n      }\n      if (!clickedInsideAscendion) {\n        this.ascendionDropdownOpen = false;\n      }\n    }\n    navigate() {\n      this.router.navigateByUrl('/agent-list');\n    }\n    // Navigate back to previously opened page\n    onLogoClick() {\n      this.location.back();\n    }\n    static ɵfac = function NavHeaderComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NavHeaderComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i1.TokenStorageService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.Location));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NavHeaderComponent,\n      selectors: [[\"app-nav-header\"]],\n      hostBindings: function NavHeaderComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function NavHeaderComponent_click_HostBindingHandler($event) {\n            return ctx.onDocumentClick($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      inputs: {\n        isLaunchpadRoute: \"isLaunchpadRoute\"\n      },\n      outputs: {\n        userSelected: \"userSelected\",\n        ascendionOptionSelected: \"ascendionOptionSelected\"\n      },\n      decls: 24,\n      vars: 8,\n      consts: [[\"theme\", \"light\", 1, \"custom-header-container\"], [\"left-content\", \"\", 1, \"header-left-logo\"], [1, \"logo-text\", \"cursor-pointer\", 3, \"click\"], [1, \"ascendion-dropdown\"], [\"right-content\", \"\", 1, \"header-right-content\"], [\"class\", \"button\", \"label\", \"Go to Marketplace\", \"variant\", \"primary\", \"width\", \"auto\", \"height\", \"40px\", 3, \"pill\", \"userClick\", 4, \"ngIf\"], [\"class\", \"ascendion-container\", 4, \"ngIf\"], [\"iconName\", \"languages\", \"iconColor\", \"#666D99\", \"iconSize\", \"24px\", 1, \"nav-icon\"], [\"iconName\", \"sun\", \"iconColor\", \"#666D99\", \"iconSize\", \"24px\", 1, \"nav-icon\"], [1, \"profile-container\", \"header-right-content\"], [1, \"cursor-pointer\", \"d-flex\", \"justify-content-center\", \"align-items-center\", 3, \"click\"], [\"src\", \"assets/icons/user-avatar.svg\", \"alt\", \"User avatar\", 1, \"custom-avatar\"], [1, \"profile-dropdown\"], [1, \"profile-dropdown-content\"], [1, \"profile-info\"], [1, \"profile-name\"], [\"class\", \"profile-email\", 4, \"ngIf\"], [1, \"profile-divider\"], [1, \"profile-actions\"], [1, \"profile-action-item\", 3, \"click\"], [1, \"action-label\"], [\"label\", \"Go to Marketplace\", \"variant\", \"primary\", \"width\", \"auto\", \"height\", \"40px\", 1, \"button\", 3, \"userClick\", \"pill\"], [1, \"ascendion-container\"], [\"variant\", \"secondary\", \"iconName\", \"chevron-down\", \"iconPosition\", \"right\", \"iconColor\", \"#E91E63\", \"width\", \"auto\", \"height\", \"40px\", 3, \"userClick\", \"label\", \"pill\"], [1, \"ascendion-dropdown-content\"], [1, \"ascendion-actions\"], [1, \"ascendion-action-item\", 3, \"click\"], [1, \"profile-email\"]],\n      template: function NavHeaderComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"awe-header\", 0)(1, \"div\", 1)(2, \"p\", 2);\n          i0.ɵɵlistener(\"click\", function NavHeaderComponent_Template_p_click_2_listener() {\n            return ctx.onLogoClick();\n          });\n          i0.ɵɵtext(3, \" Ascendion AAVA \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"div\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 4);\n          i0.ɵɵtemplate(6, NavHeaderComponent_ava_button_6_Template, 1, 1, \"ava-button\", 5)(7, NavHeaderComponent_div_7_Template, 14, 4, \"div\", 6);\n          i0.ɵɵelement(8, \"ava-icon\", 7)(9, \"ava-icon\", 8);\n          i0.ɵɵelementStart(10, \"div\", 9)(11, \"div\", 10);\n          i0.ɵɵlistener(\"click\", function NavHeaderComponent_Template_div_click_11_listener() {\n            return ctx.toggleProfileDropdown();\n          });\n          i0.ɵɵelement(12, \"img\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 12)(14, \"div\", 13)(15, \"div\", 14)(16, \"div\", 15);\n          i0.ɵɵtext(17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(18, NavHeaderComponent_div_18_Template, 2, 1, \"div\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(19, \"div\", 17);\n          i0.ɵɵelementStart(20, \"div\", 18)(21, \"div\", 19);\n          i0.ɵɵlistener(\"click\", function NavHeaderComponent_Template_div_click_21_listener() {\n            return ctx.onLogout();\n          });\n          i0.ɵɵelementStart(22, \"span\", 20);\n          i0.ɵɵtext(23, \"Logout\");\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLaunchpadRoute);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLaunchpadRoute);\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"active\", ctx.profileDropdownOpen);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"visible\", ctx.profileDropdownOpen);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.userName);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.userEmail);\n        }\n      },\n      dependencies: [CommonModule, i3.NgIf, HeaderComponent, IconComponent, ButtonComponent],\n      styles: [\".outer-box.light {\\n  background-color: transparent !important;\\n  box-shadow: none !important;\\n}\\n\\n.container {\\n  background-color: transparent !important;\\n}\\n\\n.custom-header-container {\\n  width: 100%;\\n  box-sizing: border-box;\\n}\\n\\n.header-left-logo {\\n  display: flex;\\n  align-items: center;\\n  gap: 24px;\\n  width: auto;\\n  height: 36px;\\n  flex: 1;\\n}\\n.header-left-logo .logo-text {\\n  color: #000;\\n  font-family: Mulish;\\n  font-size: 32px;\\n  font-weight: 700;\\n  margin: 0;\\n}\\n\\n.header-right-content {\\n  display: flex;\\n  align-items: center;\\n  gap: 32px;\\n}\\n\\n.button .ava-button .button-label {\\n  color: #fff;\\n}\\n\\n.custom-avatar {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  box-shadow: 0 2px 8px rgba(20, 70, 146, 0.08);\\n}\\n\\n.nav-icon {\\n  width: 40px !important;\\n  height: 40px !important;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin: 0 !important;\\n  padding: 0 !important;\\n  box-sizing: border-box;\\n}\\n\\n.profile-container {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n}\\n.profile-container .cursor-pointer {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 8px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.profile-container .cursor-pointer:hover {\\n  background-color: var(--nav-hover);\\n  transform: scale(1.05);\\n}\\n.profile-container .cursor-pointer:hover:after {\\n  opacity: 0.1;\\n}\\n.profile-container .cursor-pointer.active {\\n  background-color: var(--nav-item-active-bg, #e6e4fb);\\n  box-shadow: 0 2px 4px var(--card-shadow, rgba(0, 0, 0, 0.1));\\n}\\n.profile-container .cursor-pointer.active:after {\\n  opacity: 0.1;\\n}\\n.profile-container .cursor-pointer:after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  inset: 0;\\n  background: var(--gradient-primary);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n  z-index: -1;\\n}\\n.profile-container .cursor-pointer img {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n}\\n\\n.ascendion-container {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.profile-dropdown {\\n  position: absolute;\\n  top: 110px;\\n  right: 50px;\\n  min-width: 200px;\\n  background-color: var(--dropdown-bg);\\n  border-radius: 8px;\\n  box-shadow: 0 4px 16px var(--dropdown-shadow);\\n  opacity: 0;\\n  visibility: hidden;\\n  transform: translateY(10px);\\n  transition: all 0.3s ease;\\n  z-index: 15;\\n  margin-top: 8px;\\n  padding: 8px;\\n}\\n.profile-dropdown.visible {\\n  opacity: 1;\\n  visibility: visible;\\n  transform: translateY(0);\\n}\\n\\n.profile-dropdown-content {\\n  display: flex;\\n  flex-direction: column;\\n  background-color: #fff;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\\n}\\n\\n.profile-info {\\n  padding: 12px;\\n  border-radius: 6px;\\n}\\n\\n.profile-name {\\n  font-weight: 500;\\n  font-size: 16px;\\n  color: var(--dropdown-text);\\n  margin-bottom: 4px;\\n}\\n\\n.profile-email {\\n  font-size: 14px;\\n  color: var(--text-secondary);\\n}\\n\\n.profile-divider {\\n  height: 1px;\\n  background-color: var(--border-color, #e5e7eb);\\n  margin: 8px 0;\\n}\\n\\n.profile-actions {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.profile-action-item {\\n  color: var(--nav-text, #666d99);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 12px;\\n  padding: 12px;\\n  border-radius: 6px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.profile-action-item:hover {\\n  background-color: var(--nav-hover);\\n  color: var(--nav-item-active-color);\\n}\\n.profile-action-item:hover:after {\\n  opacity: 0.1;\\n}\\n.profile-action-item:after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  inset: 0;\\n  background: var(--gradient-primary);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n  z-index: -1;\\n}\\n.profile-action-item .action-label {\\n  font-weight: 500;\\n  font-size: 1rem;\\n  color: var(--dropdown-text);\\n}\\n\\n.ascendion-dropdown {\\n  position: absolute;\\n  top: 48px;\\n  right: 0;\\n  min-width: 160px;\\n  background-color: var(--dropdown-bg);\\n  border-radius: 8px;\\n  box-shadow: 0 4px 16px var(--dropdown-shadow);\\n  opacity: 0;\\n  visibility: hidden;\\n  transform: translateY(10px);\\n  transition: all 0.3s ease;\\n  z-index: 15;\\n  padding: 8px;\\n}\\n.ascendion-dropdown.visible {\\n  opacity: 1;\\n  visibility: visible;\\n  transform: translateY(0);\\n}\\n\\n.ascendion-dropdown-content {\\n  display: flex;\\n  flex-direction: column;\\n  background-color: #fff;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\\n}\\n\\n.ascendion-actions {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.ascendion-action-item {\\n  color: var(--nav-text, #666d99);\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-start;\\n  gap: 12px;\\n  padding: 12px;\\n  border-radius: 6px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.ascendion-action-item:hover {\\n  background-color: var(--nav-hover);\\n  color: var(--nav-item-active-color);\\n}\\n.ascendion-action-item:hover:after {\\n  opacity: 0.1;\\n}\\n.ascendion-action-item:after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  inset: 0;\\n  background: var(--gradient-primary);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n  z-index: -1;\\n}\\n.ascendion-action-item .action-label {\\n  font-weight: 500;\\n  font-size: 1rem;\\n  color: var(--dropdown-text);\\n  text-align: left;\\n  width: 100%;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      encapsulation: 2\n    });\n  }\n  return NavHeaderComponent;\n})();", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "HeaderComponent", "IconComponent", "ButtonComponent", "i0", "ɵɵelementStart", "ɵɵlistener", "NavHeaderComponent_ava_button_6_Template_ava_button_userClick_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "navigate", "ɵɵelementEnd", "ɵɵproperty", "NavHeaderComponent_div_7_Template_ava_button_userClick_1_listener", "_r3", "toggleAscendionDropdown", "NavHeaderComponent_div_7_Template_div_click_5_listener", "selectAscendionOption", "ɵɵtext", "NavHeaderComponent_div_7_Template_div_click_8_listener", "NavHeaderComponent_div_7_Template_div_click_11_listener", "ɵɵadvance", "selectedAscendionOption", "ɵɵclassProp", "ascendionDropdownOpen", "ɵɵtextInterpolate", "userEmail", "NavHeaderComponent", "authService", "tokenStorageService", "router", "location", "isLaunchpadRoute", "redirectUrl", "profileDropdownOpen", "userName", "userSelected", "ascendionOptionSelected", "users", "name", "role", "type", "constructor", "ngOnInit", "authConfig", "getAuthConfig", "window", "origin", "loadUserData", "selectUser", "onLogout", "getLoginType", "basicLogout", "subscribe", "next", "deleteC<PERSON>ie", "error", "console", "logout", "onLogin", "currentUrl", "loginSSO", "getDaName", "getDaUsername", "getInitials", "parts", "split", "length", "char<PERSON>t", "toUpperCase", "toggleProfileDropdown", "user", "emit", "option", "<PERSON><PERSON><PERSON><PERSON>", "log", "onDocumentClick", "event", "target", "clickedInsideProfile", "closest", "clickedInsideAscendion", "navigateByUrl", "onLogoClick", "back", "ɵɵdirectiveInject", "i1", "AuthService", "TokenStorageService", "i2", "Router", "i3", "Location", "selectors", "hostBindings", "NavHeaderComponent_HostBindings", "rf", "ctx", "NavHeaderComponent_click_HostBindingHandler", "$event", "ɵɵresolveDocument", "NavHeaderComponent_Template_p_click_2_listener", "ɵɵelement", "ɵɵtemplate", "NavHeaderComponent_ava_button_6_Template", "NavHeaderComponent_div_7_Template", "NavHeaderComponent_Template_div_click_11_listener", "NavHeaderComponent_div_18_Template", "NavHeaderComponent_Template_div_click_21_listener", "NgIf", "styles", "encapsulation"], "sources": ["C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\shared\\components\\nav-header\\nav-header.component.ts", "C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\shared\\components\\nav-header\\nav-header.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  OnInit,\r\n  HostListener,\r\n  Output,\r\n  EventEmitter,\r\n  Input,\r\n  ViewEncapsulation,\r\n} from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { HeaderComponent } from '@awe/play-comp-library';\r\n\r\nimport { IconComponent, ButtonComponent } from '@ava/play-comp-library';\r\nimport { AuthService, TokenStorageService } from '@shared/index';\r\nimport { Router } from '@angular/router';\r\nimport { Location } from '@angular/common';\r\n\r\ninterface User {\r\n  name: string;\r\n  role: string;\r\n  type: string;\r\n  projects?: string[];\r\n}\r\n\r\n@Component({\r\n  selector: 'app-nav-header',\r\n  standalone: true,\r\n  imports: [CommonModule, HeaderComponent, IconComponent, ButtonComponent],\r\n  templateUrl: './nav-header.component.html',\r\n  styleUrl: './nav-header.component.scss',\r\n  encapsulation: ViewEncapsulation.None,\r\n})\r\nexport class NavHeaderComponent implements OnInit {\r\n  @Input() isLaunchpadRoute: boolean = false;\r\n  public redirectUrl = '';\r\n  profileDropdownOpen: boolean = false;\r\n  ascendionDropdownOpen: boolean = false;\r\n  selectedAscendionOption: string = 'Experience Engineering'; // Default label\r\n\r\n  userName: string = '';\r\n  userEmail: string = '';\r\n\r\n  @Output() userSelected = new EventEmitter<User>();\r\n  @Output() ascendionOptionSelected = new EventEmitter<string>();\r\n\r\n  users: User[] = [\r\n    {\r\n      name: 'Akshay',\r\n      role: 'Project Team',\r\n      type: 'Project Team',\r\n    },\r\n  ];\r\n  constructor(\r\n    private authService: AuthService,\r\n    private tokenStorageService: TokenStorageService,\r\n    private router: Router,\r\n    private location: Location,\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    const authConfig = this.authService.getAuthConfig();\r\n    this.redirectUrl = authConfig?.redirectUrl || window.location.origin;\r\n    this.loadUserData();\r\n    this.selectUser(this.users[0]);\r\n  }\r\n\r\n  // Handle logout\r\n  onLogout() {\r\n    if (this.tokenStorageService.getLoginType() === 'basic') {\r\n      this.authService.basicLogout().subscribe({\r\n        next: () => {\r\n          // Use Angular router instead of window.location\r\n          this.router.navigate(['/login']);\r\n          this.tokenStorageService.deleteCookie('org_path');\r\n        },\r\n        error: (error) => {\r\n          console.error('Basic logout failed:', error);\r\n          // Still try to navigate to login even if logout fails\r\n          this.router.navigate(['/login']);\r\n        },\r\n      });\r\n    } else {\r\n      this.authService.logout(this.redirectUrl).subscribe({\r\n        next: () => {\r\n          this.tokenStorageService.deleteCookie('org_path');\r\n        },\r\n        error: (error) => {\r\n          console.error('SSO logout failed:', error);\r\n        },\r\n      });\r\n    }\r\n  }\r\n  onLogin(): void {\r\n    try {\r\n      // this.logger.info('Triggering login...');\r\n      const currentUrl = window.location.origin;\r\n      this.authService.loginSSO(currentUrl).subscribe({\r\n        // next: () => this.logger.debug('Login successful'),\r\n        // error: (error) => this.logger.error('Login failed:', error),\r\n      });\r\n    } catch (error) {\r\n      // this.logger.error('Error during login:', error);\r\n      // this.toastService.error('Login failed');\r\n    }\r\n  }\r\n\r\n  // Load user data from cookies\r\n  loadUserData(): void {\r\n    this.userName = this.tokenStorageService.getDaName() || 'User';\r\n    this.userEmail =\r\n      this.tokenStorageService.getDaUsername() || '<EMAIL>';\r\n  }\r\n\r\n  getInitials(name: string): string {\r\n    if (!name) return '';\r\n    const parts = name.split(' ');\r\n    if (parts.length === 1) return parts[0].charAt(0).toUpperCase();\r\n    return `${parts[0].charAt(0)}${parts[parts.length - 1].charAt(0)}`.toUpperCase();\r\n  }\r\n\r\n  // Toggle profile dropdown\r\n  toggleProfileDropdown(): void {\r\n    this.profileDropdownOpen = !this.profileDropdownOpen;\r\n    // Close nav dropdowns when profile dropdown opens\r\n    // if (this.profileDropdownOpen) {\r\n    //   this.closeAllDropdowns();\r\n    // }\r\n  }\r\n\r\n  // Toggle Ascendion dropdown\r\n  toggleAscendionDropdown(): void {\r\n    this.ascendionDropdownOpen = !this.ascendionDropdownOpen;\r\n  }\r\n\r\n  selectUser(user: User): void {\r\n    this.userSelected.emit(user);\r\n  }\r\n\r\n  selectAscendionOption(option: string): void {\r\n    const selectedLabel =\r\n      option === 'EE'\r\n        ? 'Experience Engineering'\r\n        : option === 'PE'\r\n          ? 'Product Engineering'\r\n          : option === 'QE'\r\n            ? 'Quality Engineering'\r\n            : option;\r\n\r\n    // Update the button label\r\n    this.selectedAscendionOption = selectedLabel;\r\n\r\n    console.log('Selected label:', selectedLabel);\r\n\r\n    // Emit the selected option to parent components\r\n    this.ascendionOptionSelected.emit(option);\r\n\r\n    this.ascendionDropdownOpen = false; // Close dropdown after selection\r\n  }\r\n\r\n  @HostListener('document:click', ['$event'])\r\n  onDocumentClick(event: MouseEvent): void {\r\n    const target = event.target as HTMLElement;\r\n    const clickedInsideProfile =\r\n      !!target.closest('.profile-container') ||\r\n      !!target.closest('.profile-dropdown');\r\n    const clickedInsideAscendion =\r\n      !!target.closest('.ascendion-container') ||\r\n      !!target.closest('.ascendion-dropdown');\r\n\r\n    if (!clickedInsideProfile) {\r\n      this.profileDropdownOpen = false;\r\n    }\r\n\r\n    if (!clickedInsideAscendion) {\r\n      this.ascendionDropdownOpen = false;\r\n    }\r\n  }\r\n\r\n  navigate() {\r\n    this.router.navigateByUrl('/agent-list');\r\n  }\r\n\r\n  // Navigate back to previously opened page\r\n  onLogoClick(): void {\r\n    this.location.back();\r\n  }\r\n}\r\n", "<awe-header theme=\"light\" class=\"custom-header-container\">\r\n  <div left-content class=\"header-left-logo\">\r\n    <p class=\"logo-text cursor-pointer\" (click)=\"onLogoClick()\">\r\n      Ascendion AAVA\r\n    </p>\r\n    <div class=\"ascendion-dropdown\"></div>\r\n  </div>\r\n  <div right-content class=\"header-right-content\">\r\n    <!-- Show marketplace and Ascendion buttons only on launchpad route -->\r\n    <ava-button\r\n      *ngIf=\"isLaunchpadRoute\"\r\n      class=\"button\"\r\n      label=\"Go to Marketplace\"\r\n      variant=\"primary\"\r\n      width=\"auto\"\r\n      height=\"40px\"\r\n      (userClick)=\"navigate()\"\r\n      [pill]=\"true\"\r\n    ></ava-button>\r\n    <div class=\"ascendion-container\" *ngIf=\"isLaunchpadRoute\">\r\n      <ava-button\r\n        [label]=\"selectedAscendionOption\"\r\n        variant=\"secondary\"\r\n        iconName=\"chevron-down\"\r\n        iconPosition=\"right\"\r\n        [pill]=\"true\"\r\n        iconColor=\"#E91E63\"\r\n        width=\"auto\"\r\n        height=\"40px\"\r\n        (userClick)=\"toggleAscendionDropdown()\"\r\n      ></ava-button>\r\n\r\n      <!-- Ascendion Dropdown -->\r\n      <div class=\"ascendion-dropdown\" [class.visible]=\"ascendionDropdownOpen\">\r\n        <div class=\"ascendion-dropdown-content\">\r\n          <div class=\"ascendion-actions\">\r\n            <div\r\n              class=\"ascendion-action-item\"\r\n              (click)=\"selectAscendionOption('EE')\"\r\n            >\r\n              <span class=\"action-label\">Experience Engineering</span>\r\n            </div>\r\n            <div\r\n              class=\"ascendion-action-item\"\r\n              (click)=\"selectAscendionOption('PE')\"\r\n            >\r\n              <span class=\"action-label\">Product Engineering</span>\r\n            </div>\r\n            <div\r\n              class=\"ascendion-action-item\"\r\n              (click)=\"selectAscendionOption('QE')\"\r\n            >\r\n              <span class=\"action-label\">Quality Engineering</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <ava-icon\r\n      iconName=\"languages\"\r\n      iconColor=\"#666D99\"\r\n      iconSize=\"24px\"\r\n      class=\"nav-icon\"\r\n    ></ava-icon>\r\n    <ava-icon\r\n      iconName=\"sun\"\r\n      iconColor=\"#666D99\"\r\n      iconSize=\"24px\"\r\n      class=\"nav-icon\"\r\n    ></ava-icon>\r\n    <div class=\"profile-container header-right-content\">\r\n      <div\r\n        class=\"cursor-pointer d-flex justify-content-center align-items-center\"\r\n        [class.active]=\"profileDropdownOpen\"\r\n        (click)=\"toggleProfileDropdown()\"\r\n      >\r\n        <img\r\n          src=\"assets/icons/user-avatar.svg\"\r\n          class=\"custom-avatar\"\r\n          alt=\"User avatar\"\r\n        />\r\n      </div>\r\n\r\n      <!-- Profile Dropdown -->\r\n      <div class=\"profile-dropdown\" [class.visible]=\"profileDropdownOpen\">\r\n        <div class=\"profile-dropdown-content\">\r\n          <div class=\"profile-info\">\r\n            <div class=\"profile-name\">{{ userName }}</div>\r\n            <div class=\"profile-email\" *ngIf=\"userEmail\">{{ userEmail }}</div>\r\n          </div>\r\n          <div class=\"profile-divider\"></div>\r\n          <div class=\"profile-actions\">\r\n            <div class=\"profile-action-item\" (click)=\"onLogout()\">\r\n              <span class=\"action-label\">Logout</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</awe-header>\r\n"], "mappings": "AAAA,SAKEA,YAAY,QAGP,eAAe;AACtB,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,wBAAwB;AAExD,SAASC,aAAa,EAAEC,eAAe,QAAQ,wBAAwB;;;;;;;;ICHnEC,EAAA,CAAAC,cAAA,qBASC;IAFCD,EAAA,CAAAE,UAAA,uBAAAC,yEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAaF,MAAA,CAAAG,QAAA,EAAU;IAAA,EAAC;IAEzBT,EAAA,CAAAU,YAAA,EAAa;;;IADZV,EAAA,CAAAW,UAAA,cAAa;;;;;;IAGbX,EADF,CAAAC,cAAA,cAA0D,qBAWvD;IADCD,EAAA,CAAAE,UAAA,uBAAAU,kEAAA;MAAAZ,EAAA,CAAAI,aAAA,CAAAS,GAAA;MAAA,MAAAP,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAaF,MAAA,CAAAQ,uBAAA,EAAyB;IAAA,EAAC;IACxCd,EAAA,CAAAU,YAAA,EAAa;IAMRV,EAHN,CAAAC,cAAA,aAAwE,cAC9B,cACP,cAI5B;IADCD,EAAA,CAAAE,UAAA,mBAAAa,uDAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAS,GAAA;MAAA,MAAAP,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAU,qBAAA,CAAsB,IAAI,CAAC;IAAA,EAAC;IAErChB,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAiB,MAAA,6BAAsB;IACnDjB,EADmD,CAAAU,YAAA,EAAO,EACpD;IACNV,EAAA,CAAAC,cAAA,cAGC;IADCD,EAAA,CAAAE,UAAA,mBAAAgB,uDAAA;MAAAlB,EAAA,CAAAI,aAAA,CAAAS,GAAA;MAAA,MAAAP,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAU,qBAAA,CAAsB,IAAI,CAAC;IAAA,EAAC;IAErChB,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAiB,MAAA,2BAAmB;IAChDjB,EADgD,CAAAU,YAAA,EAAO,EACjD;IACNV,EAAA,CAAAC,cAAA,eAGC;IADCD,EAAA,CAAAE,UAAA,mBAAAiB,wDAAA;MAAAnB,EAAA,CAAAI,aAAA,CAAAS,GAAA;MAAA,MAAAP,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAU,qBAAA,CAAsB,IAAI,CAAC;IAAA,EAAC;IAErChB,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAiB,MAAA,2BAAmB;IAKxDjB,EALwD,CAAAU,YAAA,EAAO,EACjD,EACF,EACF,EACF,EACF;;;;IApCFV,EAAA,CAAAoB,SAAA,EAAiC;IAIjCpB,EAJA,CAAAW,UAAA,UAAAL,MAAA,CAAAe,uBAAA,CAAiC,cAIpB;IAQiBrB,EAAA,CAAAoB,SAAA,EAAuC;IAAvCpB,EAAA,CAAAsB,WAAA,YAAAhB,MAAA,CAAAiB,qBAAA,CAAuC;;;;;IAuDjEvB,EAAA,CAAAC,cAAA,cAA6C;IAAAD,EAAA,CAAAiB,MAAA,GAAe;IAAAjB,EAAA,CAAAU,YAAA,EAAM;;;;IAArBV,EAAA,CAAAoB,SAAA,EAAe;IAAfpB,EAAA,CAAAwB,iBAAA,CAAAlB,MAAA,CAAAmB,SAAA,CAAe;;;ADxDxE,WAAaC,kBAAkB;EAAzB,MAAOA,kBAAkB;IAqBnBC,WAAA;IACAC,mBAAA;IACAC,MAAA;IACAC,QAAA;IAvBDC,gBAAgB,GAAY,KAAK;IACnCC,WAAW,GAAG,EAAE;IACvBC,mBAAmB,GAAY,KAAK;IACpCV,qBAAqB,GAAY,KAAK;IACtCF,uBAAuB,GAAW,wBAAwB,CAAC,CAAC;IAE5Da,QAAQ,GAAW,EAAE;IACrBT,SAAS,GAAW,EAAE;IAEZU,YAAY,GAAG,IAAIxC,YAAY,EAAQ;IACvCyC,uBAAuB,GAAG,IAAIzC,YAAY,EAAU;IAE9D0C,KAAK,GAAW,CACd;MACEC,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,cAAc;MACpBC,IAAI,EAAE;KACP,CACF;IACDC,YACUd,WAAwB,EACxBC,mBAAwC,EACxCC,MAAc,EACdC,QAAkB;MAHlB,KAAAH,WAAW,GAAXA,WAAW;MACX,KAAAC,mBAAmB,GAAnBA,mBAAmB;MACnB,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,QAAQ,GAARA,QAAQ;IACf;IAEHY,QAAQA,CAAA;MACN,MAAMC,UAAU,GAAG,IAAI,CAAChB,WAAW,CAACiB,aAAa,EAAE;MACnD,IAAI,CAACZ,WAAW,GAAGW,UAAU,EAAEX,WAAW,IAAIa,MAAM,CAACf,QAAQ,CAACgB,MAAM;MACpE,IAAI,CAACC,YAAY,EAAE;MACnB,IAAI,CAACC,UAAU,CAAC,IAAI,CAACX,KAAK,CAAC,CAAC,CAAC,CAAC;IAChC;IAEA;IACAY,QAAQA,CAAA;MACN,IAAI,IAAI,CAACrB,mBAAmB,CAACsB,YAAY,EAAE,KAAK,OAAO,EAAE;QACvD,IAAI,CAACvB,WAAW,CAACwB,WAAW,EAAE,CAACC,SAAS,CAAC;UACvCC,IAAI,EAAEA,CAAA,KAAK;YACT;YACA,IAAI,CAACxB,MAAM,CAACpB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;YAChC,IAAI,CAACmB,mBAAmB,CAAC0B,YAAY,CAAC,UAAU,CAAC;UACnD,CAAC;UACDC,KAAK,EAAGA,KAAK,IAAI;YACfC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;YAC5C;YACA,IAAI,CAAC1B,MAAM,CAACpB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;UAClC;SACD,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAACkB,WAAW,CAAC8B,MAAM,CAAC,IAAI,CAACzB,WAAW,CAAC,CAACoB,SAAS,CAAC;UAClDC,IAAI,EAAEA,CAAA,KAAK;YACT,IAAI,CAACzB,mBAAmB,CAAC0B,YAAY,CAAC,UAAU,CAAC;UACnD,CAAC;UACDC,KAAK,EAAGA,KAAK,IAAI;YACfC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;UAC5C;SACD,CAAC;MACJ;IACF;IACAG,OAAOA,CAAA;MACL,IAAI;QACF;QACA,MAAMC,UAAU,GAAGd,MAAM,CAACf,QAAQ,CAACgB,MAAM;QACzC,IAAI,CAACnB,WAAW,CAACiC,QAAQ,CAACD,UAAU,CAAC,CAACP,SAAS,CAAC;UAC9C;UACA;QAAA,CACD,CAAC;MACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;QACd;QACA;MAAA;IAEJ;IAEA;IACAR,YAAYA,CAAA;MACV,IAAI,CAACb,QAAQ,GAAG,IAAI,CAACN,mBAAmB,CAACiC,SAAS,EAAE,IAAI,MAAM;MAC9D,IAAI,CAACpC,SAAS,GACZ,IAAI,CAACG,mBAAmB,CAACkC,aAAa,EAAE,IAAI,4BAA4B;IAC5E;IAEAC,WAAWA,CAACzB,IAAY;MACtB,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;MACpB,MAAM0B,KAAK,GAAG1B,IAAI,CAAC2B,KAAK,CAAC,GAAG,CAAC;MAC7B,IAAID,KAAK,CAACE,MAAM,KAAK,CAAC,EAAE,OAAOF,KAAK,CAAC,CAAC,CAAC,CAACG,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE;MAC/D,OAAO,GAAGJ,KAAK,CAAC,CAAC,CAAC,CAACG,MAAM,CAAC,CAAC,CAAC,GAAGH,KAAK,CAACA,KAAK,CAACE,MAAM,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,CAACC,WAAW,EAAE;IAClF;IAEA;IACAC,qBAAqBA,CAAA;MACnB,IAAI,CAACpC,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;MACpD;MACA;MACA;MACA;IACF;IAEA;IACAnB,uBAAuBA,CAAA;MACrB,IAAI,CAACS,qBAAqB,GAAG,CAAC,IAAI,CAACA,qBAAqB;IAC1D;IAEAyB,UAAUA,CAACsB,IAAU;MACnB,IAAI,CAACnC,YAAY,CAACoC,IAAI,CAACD,IAAI,CAAC;IAC9B;IAEAtD,qBAAqBA,CAACwD,MAAc;MAClC,MAAMC,aAAa,GACjBD,MAAM,KAAK,IAAI,GACX,wBAAwB,GACxBA,MAAM,KAAK,IAAI,GACb,qBAAqB,GACrBA,MAAM,KAAK,IAAI,GACb,qBAAqB,GACrBA,MAAM;MAEhB;MACA,IAAI,CAACnD,uBAAuB,GAAGoD,aAAa;MAE5CjB,OAAO,CAACkB,GAAG,CAAC,iBAAiB,EAAED,aAAa,CAAC;MAE7C;MACA,IAAI,CAACrC,uBAAuB,CAACmC,IAAI,CAACC,MAAM,CAAC;MAEzC,IAAI,CAACjD,qBAAqB,GAAG,KAAK,CAAC,CAAC;IACtC;IAGAoD,eAAeA,CAACC,KAAiB;MAC/B,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAAqB;MAC1C,MAAMC,oBAAoB,GACxB,CAAC,CAACD,MAAM,CAACE,OAAO,CAAC,oBAAoB,CAAC,IACtC,CAAC,CAACF,MAAM,CAACE,OAAO,CAAC,mBAAmB,CAAC;MACvC,MAAMC,sBAAsB,GAC1B,CAAC,CAACH,MAAM,CAACE,OAAO,CAAC,sBAAsB,CAAC,IACxC,CAAC,CAACF,MAAM,CAACE,OAAO,CAAC,qBAAqB,CAAC;MAEzC,IAAI,CAACD,oBAAoB,EAAE;QACzB,IAAI,CAAC7C,mBAAmB,GAAG,KAAK;MAClC;MAEA,IAAI,CAAC+C,sBAAsB,EAAE;QAC3B,IAAI,CAACzD,qBAAqB,GAAG,KAAK;MACpC;IACF;IAEAd,QAAQA,CAAA;MACN,IAAI,CAACoB,MAAM,CAACoD,aAAa,CAAC,aAAa,CAAC;IAC1C;IAEA;IACAC,WAAWA,CAAA;MACT,IAAI,CAACpD,QAAQ,CAACqD,IAAI,EAAE;IACtB;;uCAzJWzD,kBAAkB,EAAA1B,EAAA,CAAAoF,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAtF,EAAA,CAAAoF,iBAAA,CAAAC,EAAA,CAAAE,mBAAA,GAAAvF,EAAA,CAAAoF,iBAAA,CAAAI,EAAA,CAAAC,MAAA,GAAAzF,EAAA,CAAAoF,iBAAA,CAAAM,EAAA,CAAAC,QAAA;IAAA;;YAAlBjE,kBAAkB;MAAAkE,SAAA;MAAAC,YAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAlB/F,EAAA,CAAAE,UAAA,mBAAA+F,4CAAAC,MAAA;YAAA,OAAAF,GAAA,CAAArB,eAAA,CAAAuB,MAAA,CAAuB;UAAA,UAAAlG,EAAA,CAAAmG,iBAAA,CAAL;;;;;;;;;;;;;;;UC9B3BnG,EAFJ,CAAAC,cAAA,oBAA0D,aACb,WACmB;UAAxBD,EAAA,CAAAE,UAAA,mBAAAkG,+CAAA;YAAA,OAASJ,GAAA,CAAAd,WAAA,EAAa;UAAA,EAAC;UACzDlF,EAAA,CAAAiB,MAAA,uBACF;UAAAjB,EAAA,CAAAU,YAAA,EAAI;UACJV,EAAA,CAAAqG,SAAA,aAAsC;UACxCrG,EAAA,CAAAU,YAAA,EAAM;UACNV,EAAA,CAAAC,cAAA,aAAgD;UAY9CD,EAVA,CAAAsG,UAAA,IAAAC,wCAAA,wBASC,IAAAC,iCAAA,kBACyD;UA6C1DxG,EANA,CAAAqG,SAAA,kBAKY,kBAMA;UAEVrG,EADF,CAAAC,cAAA,cAAoD,eAKjD;UADCD,EAAA,CAAAE,UAAA,mBAAAuG,kDAAA;YAAA,OAAST,GAAA,CAAA3B,qBAAA,EAAuB;UAAA,EAAC;UAEjCrE,EAAA,CAAAqG,SAAA,eAIE;UACJrG,EAAA,CAAAU,YAAA,EAAM;UAMAV,EAHN,CAAAC,cAAA,eAAoE,eAC5B,eACV,eACE;UAAAD,EAAA,CAAAiB,MAAA,IAAc;UAAAjB,EAAA,CAAAU,YAAA,EAAM;UAC9CV,EAAA,CAAAsG,UAAA,KAAAI,kCAAA,kBAA6C;UAC/C1G,EAAA,CAAAU,YAAA,EAAM;UACNV,EAAA,CAAAqG,SAAA,eAAmC;UAEjCrG,EADF,CAAAC,cAAA,eAA6B,eAC2B;UAArBD,EAAA,CAAAE,UAAA,mBAAAyG,kDAAA;YAAA,OAASX,GAAA,CAAA/C,QAAA,EAAU;UAAA,EAAC;UACnDjD,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAiB,MAAA,cAAM;UAO/CjB,EAP+C,CAAAU,YAAA,EAAO,EACpC,EACF,EACF,EACF,EACF,EACF,EACK;;;UA1FNV,EAAA,CAAAoB,SAAA,GAAsB;UAAtBpB,EAAA,CAAAW,UAAA,SAAAqF,GAAA,CAAAjE,gBAAA,CAAsB;UASS/B,EAAA,CAAAoB,SAAA,EAAsB;UAAtBpB,EAAA,CAAAW,UAAA,SAAAqF,GAAA,CAAAjE,gBAAA,CAAsB;UAsDpD/B,EAAA,CAAAoB,SAAA,GAAoC;UAApCpB,EAAA,CAAAsB,WAAA,WAAA0E,GAAA,CAAA/D,mBAAA,CAAoC;UAWRjC,EAAA,CAAAoB,SAAA,GAAqC;UAArCpB,EAAA,CAAAsB,WAAA,YAAA0E,GAAA,CAAA/D,mBAAA,CAAqC;UAGnCjC,EAAA,CAAAoB,SAAA,GAAc;UAAdpB,EAAA,CAAAwB,iBAAA,CAAAwE,GAAA,CAAA9D,QAAA,CAAc;UACZlC,EAAA,CAAAoB,SAAA,EAAe;UAAfpB,EAAA,CAAAW,UAAA,SAAAqF,GAAA,CAAAvE,SAAA,CAAe;;;qBD7D3C7B,YAAY,EAAA8F,EAAA,CAAAkB,IAAA,EAAE/G,eAAe,EAAEC,aAAa,EAAEC,eAAe;MAAA8G,MAAA;MAAAC,aAAA;IAAA;;SAK5DpF,kBAAkB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}