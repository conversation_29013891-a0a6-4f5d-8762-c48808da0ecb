{"ast": null, "code": "import conicEqualArea from \"./conicEqualArea.js\";\nexport default function () {\n  return conicEqualArea().parallels([29.5, 45.5]).scale(1070).translate([480, 250]).rotate([96, 0]).center([-0.6, 38.7]);\n}", "map": {"version": 3, "names": ["conicEqualArea", "parallels", "scale", "translate", "rotate", "center"], "sources": ["C:/console/aava-ui-web/node_modules/d3-geo/src/projection/albers.js"], "sourcesContent": ["import conicEqualArea from \"./conicEqualArea.js\";\n\nexport default function() {\n  return conicEqualArea()\n      .parallels([29.5, 45.5])\n      .scale(1070)\n      .translate([480, 250])\n      .rotate([96, 0])\n      .center([-0.6, 38.7]);\n}\n"], "mappings": "AAAA,OAAOA,cAAc,MAAM,qBAAqB;AAEhD,eAAe,YAAW;EACxB,OAAOA,cAAc,CAAC,CAAC,CAClBC,SAAS,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CACvBC,KAAK,CAAC,IAAI,CAAC,CACXC,SAAS,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CACrBC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CACfC,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}