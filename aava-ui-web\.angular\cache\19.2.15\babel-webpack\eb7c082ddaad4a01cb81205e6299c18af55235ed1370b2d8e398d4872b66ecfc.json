{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { CommonModule } from '@angular/common';\nimport { Component } from '@angular/core';\nimport { IconComponent } from '@ava/play-comp-library';\nlet AdminManagementCellComponent = class AdminManagementCellComponent {\n  params;\n  userAvatar = '';\n  aweInit(params) {\n    this.params = params;\n    console.log(params);\n    this.generateUserAvatar();\n  }\n  deleteUser() {\n    this.params.context.componentParent.deleteUser(this.params.rowData.userId);\n    console.log(this.params.rowData.userId);\n  }\n  onCellClick() {\n    this.params.context.componentParent.openPreviewPanel(this.params.rowData);\n  }\n  generateUserAvatar() {\n    // Generate avatar from user initials if no profile picture is available\n    if (this.params.value) {\n      const nameParts = this.params.value.trim().split(' ');\n      let initials = '';\n      if (nameParts.length >= 2) {\n        // First letter of first name and first letter of last name\n        initials = nameParts[0][0] + nameParts[nameParts.length - 1][0];\n      } else if (nameParts.length === 1) {\n        // Just first letter if only one name\n        initials = nameParts[0][0];\n      } else {\n        initials = 'U'; // Default to 'U' for User\n      }\n      initials = initials.toUpperCase();\n      // Generate a colored avatar with initials\n      const colors = ['#8B5CF6', '#06B6D4', '#10B981', '#F59E0B', '#EF4444', '#8B5A2B', '#6366F1', '#EC4899'];\n      const colorIndex = this.params.value.length % colors.length;\n      const backgroundColor = colors[colorIndex];\n      this.userAvatar = `data:image/svg+xml;base64,${btoa(`\n        <svg width=\"40\" height=\"40\" xmlns=\"http://www.w3.org/2000/svg\">\n          <circle cx=\"20\" cy=\"20\" r=\"20\" fill=\"${backgroundColor}\"/>\n          <text x=\"20\" y=\"26\" font-family=\"Inter, Arial, sans-serif\" font-size=\"14\" font-weight=\"600\" fill=\"white\" text-anchor=\"middle\">${initials}</text>\n        </svg>\n      `)}`;\n    }\n  }\n};\nAdminManagementCellComponent = __decorate([Component({\n  selector: 'app-admin-management-cell',\n  standalone: true,\n  imports: [CommonModule, IconComponent],\n  template: `\n    @if (params.colDef.header === 'Name') {\n      <div class=\"user__cell flex\" (click)=\"onCellClick()\">\n        <div class=\"profile-trigger\">\n          <img [src]=\"userAvatar\" alt=\"User Profile\" class=\"profile-avatar\" />\n        </div>\n        <div class=\"name__container flex\">\n          <div class=\"name-title\" >{{ params.value }}</div>\n          <div class=\"sub-title\">{{ params.rowData.email }}</div>\n        </div>\n      </div>\n    } @else {\n      <ava-icon\n        [cursor]=\"true\"\n        (click)=\"deleteUser()\"\n        [iconSize]=\"16\"\n        iconName=\"trash-2\"\n        iconColor=\"#1C1B1F\"\n      ></ava-icon>\n    }\n  `,\n  styles: [`\n    .flex {\n      display: flex;\n    }\n    .user__cell {\n      cursor: pointer;\n      gap: 16px;\n    }\n    .name-title {\n      font-weight: 700;\n    }\n    .name__container {\n      flex-direction: column;\n      gap: 4px;\n    }\n    .sub-title {\n      font-size: 14px;\n      color: #616874;\n    }\n    .profile-trigger {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      cursor: pointer;\n      border-radius: 50%;\n      transition: all 0.2s ease;\n      padding: 2px;\n\n      &:hover {\n        background: rgba(0, 0, 0, 0.05);\n      }\n\n      &.active {\n        background: rgba(0, 0, 0, 0.1);\n      }\n\n      .profile-avatar {\n        width: 40px;\n        height: 40px;\n        border-radius: 50%;\n        object-fit: cover;\n        border: 2px solid rgba(255, 255, 255, 0.2);\n      }\n    }\n    `]\n})], AdminManagementCellComponent);\nexport { AdminManagementCellComponent };", "map": {"version": 3, "names": ["CommonModule", "Component", "IconComponent", "AdminManagementCellComponent", "params", "userAvatar", "aweInit", "console", "log", "generateUserAvatar", "deleteUser", "context", "componentParent", "rowData", "userId", "onCellClick", "openPreviewPanel", "value", "nameParts", "trim", "split", "initials", "length", "toUpperCase", "colors", "colorIndex", "backgroundColor", "btoa", "__decorate", "selector", "standalone", "imports", "template", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\pages\\manage\\admin-management\\components\\view-user-management\\renderer\\action-management-renderer.component.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\r\nimport { Component } from '@angular/core';\r\nimport { IconComponent } from '@ava/play-comp-library';\r\nimport { CellRenderer, CellRendererParams } from 'projects/console/src/app/shared/components/table-grid/model/table-grid.model';\r\n\r\n@Component({\r\n  selector: 'app-admin-management-cell',\r\n  standalone: true,\r\n  imports: [CommonModule, IconComponent],\r\n  template: `\r\n    @if (params.colDef.header === 'Name') {\r\n      <div class=\"user__cell flex\" (click)=\"onCellClick()\">\r\n        <div class=\"profile-trigger\">\r\n          <img [src]=\"userAvatar\" alt=\"User Profile\" class=\"profile-avatar\" />\r\n        </div>\r\n        <div class=\"name__container flex\">\r\n          <div class=\"name-title\" >{{ params.value }}</div>\r\n          <div class=\"sub-title\">{{ params.rowData.email }}</div>\r\n        </div>\r\n      </div>\r\n    } @else {\r\n      <ava-icon\r\n        [cursor]=\"true\"\r\n        (click)=\"deleteUser()\"\r\n        [iconSize]=\"16\"\r\n        iconName=\"trash-2\"\r\n        iconColor=\"#1C1B1F\"\r\n      ></ava-icon>\r\n    }\r\n  `,\r\n  styles: [\r\n    `\r\n    .flex {\r\n      display: flex;\r\n    }\r\n    .user__cell {\r\n      cursor: pointer;\r\n      gap: 16px;\r\n    }\r\n    .name-title {\r\n      font-weight: 700;\r\n    }\r\n    .name__container {\r\n      flex-direction: column;\r\n      gap: 4px;\r\n    }\r\n    .sub-title {\r\n      font-size: 14px;\r\n      color: #616874;\r\n    }\r\n    .profile-trigger {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      cursor: pointer;\r\n      border-radius: 50%;\r\n      transition: all 0.2s ease;\r\n      padding: 2px;\r\n\r\n      &:hover {\r\n        background: rgba(0, 0, 0, 0.05);\r\n      }\r\n\r\n      &.active {\r\n        background: rgba(0, 0, 0, 0.1);\r\n      }\r\n\r\n      .profile-avatar {\r\n        width: 40px;\r\n        height: 40px;\r\n        border-radius: 50%;\r\n        object-fit: cover;\r\n        border: 2px solid rgba(255, 255, 255, 0.2);\r\n      }\r\n    }\r\n    `,\r\n  ],\r\n})\r\nexport class AdminManagementCellComponent implements CellRenderer {\r\n  params!: any;\r\n  userAvatar: string = '';\r\n\r\n  aweInit(params: CellRendererParams) {\r\n    this.params = params;\r\n    console.log(params)\r\n    this.generateUserAvatar()\r\n  }\r\n\r\n  deleteUser() {\r\n    this.params.context.componentParent.deleteUser(this.params.rowData.userId);\r\n    console.log(this.params.rowData.userId)\r\n  }\r\n\r\n  onCellClick() {\r\n    this.params.context.componentParent.openPreviewPanel(this.params.rowData);\r\n  }\r\n\r\n  private generateUserAvatar(): void {\r\n    // Generate avatar from user initials if no profile picture is available\r\n    if (this.params.value) {\r\n      const nameParts = this.params.value.trim().split(' ');\r\n      let initials = '';\r\n\r\n      if (nameParts.length >= 2) {\r\n        // First letter of first name and first letter of last name\r\n        initials = nameParts[0][0] + nameParts[nameParts.length - 1][0];\r\n      } else if (nameParts.length === 1) {\r\n        // Just first letter if only one name\r\n        initials = nameParts[0][0];\r\n      } else {\r\n        initials = 'U'; // Default to 'U' for User\r\n      }\r\n\r\n      initials = initials.toUpperCase();\r\n\r\n      // Generate a colored avatar with initials\r\n      const colors = [\r\n        '#8B5CF6',\r\n        '#06B6D4',\r\n        '#10B981',\r\n        '#F59E0B',\r\n        '#EF4444',\r\n        '#8B5A2B',\r\n        '#6366F1',\r\n        '#EC4899',\r\n      ];\r\n      const colorIndex = this.params.value.length % colors.length;\r\n      const backgroundColor = colors[colorIndex];\r\n\r\n      this.userAvatar = `data:image/svg+xml;base64,${btoa(`\r\n        <svg width=\"40\" height=\"40\" xmlns=\"http://www.w3.org/2000/svg\">\r\n          <circle cx=\"20\" cy=\"20\" r=\"20\" fill=\"${backgroundColor}\"/>\r\n          <text x=\"20\" y=\"26\" font-family=\"Inter, Arial, sans-serif\" font-size=\"14\" font-weight=\"600\" fill=\"white\" text-anchor=\"middle\">${initials}</text>\r\n        </svg>\r\n      `)}`;\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,SAAS,QAAQ,eAAe;AACzC,SAASC,aAAa,QAAQ,wBAAwB;AA4E/C,IAAMC,4BAA4B,GAAlC,MAAMA,4BAA4B;EACvCC,MAAM;EACNC,UAAU,GAAW,EAAE;EAEvBC,OAAOA,CAACF,MAA0B;IAChC,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpBG,OAAO,CAACC,GAAG,CAACJ,MAAM,CAAC;IACnB,IAAI,CAACK,kBAAkB,EAAE;EAC3B;EAEAC,UAAUA,CAAA;IACR,IAAI,CAACN,MAAM,CAACO,OAAO,CAACC,eAAe,CAACF,UAAU,CAAC,IAAI,CAACN,MAAM,CAACS,OAAO,CAACC,MAAM,CAAC;IAC1EP,OAAO,CAACC,GAAG,CAAC,IAAI,CAACJ,MAAM,CAACS,OAAO,CAACC,MAAM,CAAC;EACzC;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACX,MAAM,CAACO,OAAO,CAACC,eAAe,CAACI,gBAAgB,CAAC,IAAI,CAACZ,MAAM,CAACS,OAAO,CAAC;EAC3E;EAEQJ,kBAAkBA,CAAA;IACxB;IACA,IAAI,IAAI,CAACL,MAAM,CAACa,KAAK,EAAE;MACrB,MAAMC,SAAS,GAAG,IAAI,CAACd,MAAM,CAACa,KAAK,CAACE,IAAI,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;MACrD,IAAIC,QAAQ,GAAG,EAAE;MAEjB,IAAIH,SAAS,CAACI,MAAM,IAAI,CAAC,EAAE;QACzB;QACAD,QAAQ,GAAGH,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,SAAS,CAACA,SAAS,CAACI,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACjE,CAAC,MAAM,IAAIJ,SAAS,CAACI,MAAM,KAAK,CAAC,EAAE;QACjC;QACAD,QAAQ,GAAGH,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,MAAM;QACLG,QAAQ,GAAG,GAAG,CAAC,CAAC;MAClB;MAEAA,QAAQ,GAAGA,QAAQ,CAACE,WAAW,EAAE;MAEjC;MACA,MAAMC,MAAM,GAAG,CACb,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;MACD,MAAMC,UAAU,GAAG,IAAI,CAACrB,MAAM,CAACa,KAAK,CAACK,MAAM,GAAGE,MAAM,CAACF,MAAM;MAC3D,MAAMI,eAAe,GAAGF,MAAM,CAACC,UAAU,CAAC;MAE1C,IAAI,CAACpB,UAAU,GAAG,6BAA6BsB,IAAI,CAAC;;iDAETD,eAAe;0IAC0EL,QAAQ;;OAE3I,CAAC,EAAE;IACN;EACF;CACD;AA3DYlB,4BAA4B,GAAAyB,UAAA,EAzExC3B,SAAS,CAAC;EACT4B,QAAQ,EAAE,2BAA2B;EACrCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAC/B,YAAY,EAAEE,aAAa,CAAC;EACtC8B,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;GAoBT;EACDC,MAAM,EAAE,CACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA4CC;CAEJ,CAAC,C,EACW9B,4BAA4B,CA2DxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}