{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { FilterTabsComponent } from '../filter-tabs/filter-tabs.component';\nimport { IconComponent } from '@ava/play-comp-library';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../services/drawer.service\";\nimport * as i3 from \"../../services/entity.service\";\nimport * as i4 from \"@angular/common\";\nfunction AgentsComponent_app_filter_tabs_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-filter-tabs\", 4);\n    i0.ɵɵlistener(\"tabChange\", function AgentsComponent_app_filter_tabs_1_Template_app_filter_tabs_tabChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFilterChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"tabs\", ctx_r1.filterTabs)(\"activeTab\", ctx_r1.activeFilter);\n  }\n}\nfunction AgentsComponent_div_3_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14);\n    i0.ɵɵelement(2, \"ava-icon\", 15);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 16);\n    i0.ɵɵtext(5, \" 3 days ago \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const agent_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", agent_r4.users, \" \");\n  }\n}\nfunction AgentsComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6);\n    i0.ɵɵlistener(\"click\", function AgentsComponent_div_3_Template_div_click_1_listener() {\n      const agent_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showAgentDetails(agent_r4));\n    });\n    i0.ɵɵelementStart(2, \"div\", 7)(3, \"div\", 8)(4, \"h2\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 9);\n    i0.ɵɵelement(7, \"ava-icon\", 10);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"p\", 11);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"truncate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, AgentsComponent_div_3_div_12_Template, 6, 1, \"div\", 12);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const agent_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.showTwoColumns ? \"col-12 col-md-6\" : \"col-12 col-md-6 col-lg-3\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"marketplace-card\", ctx_r1.isMarketplace);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(agent_r4.title);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", agent_r4.rating, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 7, agent_r4.description, 75));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isMarketplace);\n  }\n}\nexport let TruncatePipe = /*#__PURE__*/(() => {\n  class TruncatePipe {\n    transform(value, limit = 75) {\n      if (!value) return '';\n      if (value.length <= limit) return value;\n      return value.substring(0, limit) + '...';\n    }\n    static ɵfac = function TruncatePipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TruncatePipe)();\n    };\n    static ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"truncate\",\n      type: TruncatePipe,\n      pure: true\n    });\n  }\n  return TruncatePipe;\n})();\nexport let AgentsComponent = /*#__PURE__*/(() => {\n  class AgentsComponent {\n    router;\n    drawerService;\n    entityService;\n    agents = [];\n    showExploreButton = true;\n    showTwoColumns = false;\n    isMarketplace = false; // New input to detect marketplace context\n    searchResults = []; // Search results from search bar\n    searchQuery = ''; // Current search query\n    isSearchLoading = false; // Search loading state\n    originalAgents = [];\n    entityAgents = [];\n    loading = false;\n    activeFilter = 'all';\n    filterTabs = [{\n      id: 'all',\n      label: 'All',\n      priority: 100\n    }, {\n      id: 'experience',\n      label: 'Experience Studio',\n      icon: 'lightbulb',\n      priority: 90\n    }, {\n      id: 'product',\n      label: 'Product Studio',\n      icon: 'box',\n      priority: 80\n    }, {\n      id: 'data',\n      label: 'Data Studio',\n      icon: 'database',\n      priority: 70\n    }, {\n      id: 'finops',\n      label: 'FinOps Studio',\n      icon: 'dollar-sign',\n      priority: 60\n    }];\n    constructor(router, drawerService, entityService) {\n      this.router = router;\n      this.drawerService = drawerService;\n      this.entityService = entityService;\n    }\n    ngOnInit() {\n      this.originalAgents = [...this.agents];\n      this.loadEntityAgents();\n    }\n    /**\n     * Load agents from the entity API\n     */\n    loadEntityAgents() {\n      this.loading = true;\n      this.entityService.getAgents(0, 50).subscribe({\n        next: agents => {\n          this.entityAgents = agents;\n          this.loading = false;\n        },\n        error: error => {\n          console.error('Error loading agents:', error);\n          this.loading = false;\n        }\n      });\n    }\n    onFilterChange(filterId) {\n      this.activeFilter = filterId;\n      // Filter agents by studio type if not 'all'\n      if (filterId === 'all') {\n        this.agents = [...this.originalAgents];\n      } else {\n        const studioMap = {\n          experience: 'Experience Studio',\n          product: 'Product Studio',\n          data: 'Data Studio',\n          finops: 'FinOps Studio'\n        };\n        this.agents = this.originalAgents.filter(agent => agent.studio?.type === studioMap[filterId]);\n      }\n    }\n    navigate() {\n      this.router.navigateByUrl('/agent-list');\n    }\n    /**\n     * Shows the agent details panel for the selected agent\n     * @param agent The agent to display details for\n     */\n    showAgentDetails(agent) {\n      // Find the corresponding entity data\n      const entityAgent = this.entityAgents.find(ea => ea.id === agent.id);\n      console.log('Showing agent details for:', agent, entityAgent);\n      this.drawerService.open({\n        data: {\n          ...agent,\n          entityData: entityAgent\n        },\n        width: '650px',\n        position: 'right',\n        onGoToPlayground: selectedAgent => this.goToPlayground(selectedAgent)\n      });\n    }\n    /**\n     * Navigates to the playground for the selected agent\n     */\n    goToPlayground(agent) {\n      if (agent) {\n        // You can replace this with the actual navigation logic\n        this.router.navigate(['/playground', agent.id]);\n        // Close the drawer after navigation\n        this.drawerService.close();\n      }\n    }\n    /**\n     * Gets the agents to display based on the showTwoColumns setting and search results\n     * Returns only 6 agents (3 rows x 2 columns) when showTwoColumns is true\n     */\n    getDisplayedAgents() {\n      let agentsToDisplay = this.agents;\n      // If there are search results, filter agents to show only those that match\n      if (this.searchResults.length > 0 && this.searchQuery.trim()) {\n        const searchResultIds = this.searchResults.map(result => result.id);\n        // Check if we have all the search result agents in our current list\n        const missingAgentIds = searchResultIds.filter(id => !this.agents.some(agent => agent.id.toString() === id));\n        if (missingAgentIds.length > 0) {\n          console.log('Missing agent IDs from search results:', missingAgentIds);\n          // Create agent objects directly from search results\n          const searchResultAgents = this.searchResults.map(revelioResult => ({\n            id: parseInt(revelioResult.id) || 0,\n            // Convert string id to number\n            title: revelioResult.metadata.name || 'Unknown Agent',\n            description: revelioResult.metadata.description || revelioResult.metadata.details || '',\n            rating: 4.5,\n            // Default rating\n            studio: {\n              name: 'Experience Studio',\n              // Default studio\n              type: 'Experience Studio',\n              backgroundColor: '#FFF4F9',\n              textColor: '#E91E63'\n            },\n            developer: revelioResult.metadata.createdBy || 'Unknown Developer',\n            users: Math.floor(Math.random() * 100) + 10 // Random users count\n          }));\n          // Filter by active tab if not 'all' and we're in marketplace\n          if (this.activeFilter !== 'all' && this.isMarketplace) {\n            const studioMap = {\n              experience: 'Experience Studio',\n              product: 'Product Studio',\n              data: 'Data Studio',\n              finops: 'FinOps Studio'\n            };\n            const targetStudioType = studioMap[this.activeFilter];\n            agentsToDisplay = searchResultAgents.filter(agent => agent.studio?.type === targetStudioType);\n          } else {\n            agentsToDisplay = searchResultAgents;\n          }\n        } else {\n          // All search results are in our current list, filter normally\n          let filteredAgents = this.agents.filter(agent => {\n            const isMatch = searchResultIds.includes(agent.id.toString());\n            console.log(`Agent ${agent.id} (${agent.title}) - Match: ${isMatch}`);\n            return isMatch;\n          });\n          // Further filter by active tab if not 'all' and we're in marketplace\n          if (this.activeFilter !== 'all' && this.isMarketplace) {\n            const studioMap = {\n              experience: 'Experience Studio',\n              product: 'Product Studio',\n              data: 'Data Studio',\n              finops: 'FinOps Studio'\n            };\n            const targetStudioType = studioMap[this.activeFilter];\n            agentsToDisplay = filteredAgents.filter(agent => agent.studio?.type === targetStudioType);\n          } else {\n            agentsToDisplay = filteredAgents;\n          }\n        }\n      }\n      if (this.showTwoColumns) {\n        return agentsToDisplay.slice(0, 6); // Show only 6 agents for 3 rows x 2 columns\n      }\n      return agentsToDisplay; // Show all agents in normal mode\n    }\n    /**\n     * Check if we should show the search results message (only in marketplace)\n     */\n    get shouldShowSearchResults() {\n      return this.searchResults.length > 0 && this.searchQuery.trim() !== '' && this.isMarketplace;\n    }\n    /**\n     * Get the search results count\n     */\n    get searchResultsCount() {\n      return this.searchResults.length;\n    }\n    static ɵfac = function AgentsComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AgentsComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.DrawerService), i0.ɵɵdirectiveInject(i3.EntityService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AgentsComponent,\n      selectors: [[\"app-agents\"]],\n      inputs: {\n        agents: \"agents\",\n        showExploreButton: \"showExploreButton\",\n        showTwoColumns: \"showTwoColumns\",\n        isMarketplace: \"isMarketplace\",\n        searchResults: \"searchResults\",\n        searchQuery: \"searchQuery\",\n        isSearchLoading: \"isSearchLoading\"\n      },\n      decls: 4,\n      vars: 2,\n      consts: [[1, \"agents-container\"], [3, \"tabs\", \"activeTab\", \"tabChange\", 4, \"ngIf\"], [1, \"agents-grid\", \"row\"], [3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [3, \"tabChange\", \"tabs\", \"activeTab\"], [3, \"ngClass\"], [1, \"agent-card\", 3, \"click\"], [1, \"card-content\"], [1, \"card-header\"], [1, \"rating\"], [\"iconName\", \"star\", \"iconSize\", \"18px\", \"iconColor\", \"#FFD700\", 1, \"agent_star-icon\"], [1, \"description\"], [\"class\", \"card-footer\", 4, \"ngIf\"], [1, \"card-footer\"], [1, \"users\"], [\"iconName\", \"user\", \"iconSize\", \"16px\", \"iconColor\", \"#858aad\", 1, \"profile-svg-icon\"], [1, \"agent-time-ago\"]],\n      template: function AgentsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, AgentsComponent_app_filter_tabs_1_Template, 1, 2, \"app-filter-tabs\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2);\n          i0.ɵɵtemplate(3, AgentsComponent_div_3_Template, 13, 10, \"div\", 3);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.showExploreButton);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.getDisplayedAgents());\n        }\n      },\n      dependencies: [CommonModule, i4.NgClass, i4.NgForOf, i4.NgIf, RouterModule, FilterTabsComponent, TruncatePipe, IconComponent],\n      styles: [\".electric-card, .agents-container .agent-card {\\n  background: rgba(255, 255, 255, 0.2);\\n  backdrop-filter: blur(10px);\\n  -webkit-backdrop-filter: blur(10px);\\n  border-radius: 12px;\\n  padding: 20px;\\n  margin-bottom: 30px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\\n  cursor: pointer;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.electric-card::before, .agents-container .agent-card::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(135deg, rgba(140, 101, 247, 0.1), rgba(232, 67, 147, 0.1));\\n  opacity: 0;\\n  z-index: 0;\\n}\\n.electric-card::after, .agents-container .agent-card::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  inset: 0;\\n  padding: 2px;\\n  border-radius: 12px;\\n  background: linear-gradient(90deg, rgba(255, 255, 255, 0.8) 0%, rgba(173, 216, 230, 0.8) 25%, rgba(255, 255, 255, 0.8) 50%, rgba(173, 216, 230, 0.8) 75%, rgba(255, 255, 255, 0.8) 100%);\\n  background-size: 400% 100%;\\n  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);\\n  mask-composite: exclude;\\n  opacity: 0;\\n  pointer-events: none;\\n  filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.7)) drop-shadow(0 0 4px rgba(173, 216, 230, 0.5));\\n}\\n.electric-card .card-content, .agents-container .agent-card .card-content {\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.agents-container {\\n  display: flex;\\n  flex-direction: column;\\n}\\n.agents-container.marketplace-container {\\n  height: 100%;\\n  overflow: hidden;\\n}\\n.agents-container.launchpad-container {\\n  height: auto;\\n  overflow: visible;\\n}\\n.agents-container .search-results-header {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 16px 0;\\n  margin-bottom: 16px;\\n  border-bottom: 1px solid #e5e7eb;\\n}\\n.agents-container .search-results-header .search-results-info {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.agents-container .search-results-header .search-results-info .search-results-text {\\n  color: #616874;\\n  font-family: Mulish;\\n  font-size: 16px;\\n  font-weight: 500;\\n}\\n.agents-container .search-results-header .clear-search-btn {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  background: none;\\n  border: none;\\n  color: #616874;\\n  font-family: Mulish;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  padding: 8px 12px;\\n  border-radius: 8px;\\n  transition: all 0.3s ease;\\n}\\n.agents-container .search-results-header .clear-search-btn:hover {\\n  background-color: rgba(97, 104, 116, 0.1);\\n}\\n.agents-container .search-results-header .clear-search-btn:active {\\n  transform: scale(0.95);\\n}\\n.agents-container .loading-container {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  min-height: 200px;\\n  width: 100%;\\n}\\n.agents-container .loading-container .loading-spinner {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 16px;\\n}\\n.agents-container .loading-container .loading-spinner .spinning {\\n  animation: spin 1s linear infinite;\\n}\\n.agents-container .loading-container .loading-spinner p {\\n  color: #616874;\\n  font-family: Mulish;\\n  font-size: 16px;\\n  font-weight: 500;\\n  margin: 0;\\n}\\n.agents-container .no-agents {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  min-height: 200px;\\n  width: 100%;\\n  gap: 16px;\\n}\\n.agents-container .no-agents h3 {\\n  color: #4C515B;\\n  font-family: Mulish;\\n  font-size: 20px;\\n  font-weight: 600;\\n  margin: 0;\\n}\\n.agents-container .no-agents p {\\n  color: #616874;\\n  font-family: Mulish;\\n  font-size: 16px;\\n  font-weight: 400;\\n  margin: 0;\\n  text-align: center;\\n}\\n.agents-container .agents-content {\\n  display: flex;\\n  flex-direction: column;\\n}\\n.agents-container .agents-content.marketplace-content {\\n  height: 100%;\\n  overflow: hidden;\\n}\\n.agents-container .agents-content.launchpad-content {\\n  height: auto;\\n  overflow: visible;\\n}\\n.agents-container app-filter-tabs {\\n  position: sticky;\\n  top: 0;\\n  z-index: 50;\\n  flex-shrink: 0;\\n  background: white;\\n}\\n.agents-container .agents-grid-container {\\n  flex: 1;\\n  overflow-x: hidden;\\n  padding-bottom: 20px;\\n}\\n.agents-container .agents-grid-container.marketplace-scroll {\\n  overflow-y: auto;\\n}\\n.agents-container .agents-grid-container.launchpad-no-scroll {\\n  overflow-y: visible;\\n}\\n.agents-container .agents-grid {\\n  padding: 0 15px;\\n}\\n.agents-container .agent-card {\\n  /* Remove border-color and transition for border color */\\n  margin: 0 12px 12px 0px;\\n}\\n.agents-container .agent-card .card-header {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  padding: 0px;\\n}\\n.agents-container .agent-card .card-header h2 {\\n  color: #4C515B;\\n  font-family: Mulish;\\n  font-size: 20px;\\n  font-weight: 600;\\n  letter-spacing: -0.456px;\\n  margin: 0;\\n}\\n.agents-container .agent-card .card-header .rating {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  font-weight: 500;\\n}\\n.agents-container .agent-card .description {\\n  color: #474C6B;\\n  text-align: left;\\n  font-family: Inter;\\n  font-size: 14px;\\n  font-weight: 400;\\n  margin: 0;\\n}\\n.agents-container .agent-card .card-footer {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 0px;\\n  width: 100%;\\n}\\n.agents-container .agent-card .card-footer .users {\\n  color: #858aad;\\n  font-family: Mulish;\\n  font-size: 16px;\\n  font-style: normal;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n.agents-container .agent-card .card-footer .agent-time-ago {\\n  color: var(--Neutral-N-800, #474C6B);\\n  font-family: Mulish;\\n  font-size: 16px;\\n  font-style: normal;\\n  font-weight: 500;\\n  line-height: normal;\\n}\\n.agents-container .agent-card.marketplace-card .card-content {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n\\n@keyframes spin {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      encapsulation: 2\n    });\n  }\n  return AgentsComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "FilterTabsComponent", "IconComponent", "i0", "ɵɵelementStart", "ɵɵlistener", "AgentsComponent_app_filter_tabs_1_Template_app_filter_tabs_tabChange_0_listener", "$event", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onFilterChange", "ɵɵelementEnd", "ɵɵproperty", "filterTabs", "activeFilter", "ɵɵelement", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "agent_r4", "users", "AgentsComponent_div_3_Template_div_click_1_listener", "_r3", "$implicit", "showAgentDetails", "ɵɵtemplate", "AgentsComponent_div_3_div_12_Template", "showTwoColumns", "ɵɵclassProp", "isMarketplace", "ɵɵtextInterpolate", "title", "rating", "ɵɵpipeBind2", "description", "TruncatePipe", "transform", "value", "limit", "length", "substring", "pure", "AgentsComponent", "router", "drawerService", "entityService", "agents", "showExploreButton", "searchResults", "searchQuery", "isSearchLoading", "originalAgents", "entityAgents", "loading", "id", "label", "priority", "icon", "constructor", "ngOnInit", "loadEntityAgents", "getAgents", "subscribe", "next", "error", "console", "filterId", "studioMap", "experience", "product", "data", "finops", "filter", "agent", "studio", "type", "navigate", "navigateByUrl", "entityAgent", "find", "ea", "log", "open", "entityData", "width", "position", "onGoToPlayground", "selectedAgent", "goToPlayground", "close", "getDisplayedAgents", "agentsToDisplay", "trim", "searchResultIds", "map", "result", "missingAgentIds", "some", "toString", "searchResultAgents", "revelioResult", "parseInt", "metadata", "name", "details", "backgroundColor", "textColor", "developer", "created<PERSON>y", "Math", "floor", "random", "targetStudioType", "filteredAgents", "isMatch", "includes", "slice", "shouldShowSearchResults", "searchResultsCount", "ɵɵdirectiveInject", "i1", "Router", "i2", "DrawerService", "i3", "EntityService", "selectors", "inputs", "decls", "vars", "consts", "template", "AgentsComponent_Template", "rf", "ctx", "AgentsComponent_app_filter_tabs_1_Template", "AgentsComponent_div_3_Template", "i4", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles", "encapsulation"], "sources": ["C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\shared\\components\\agents\\agents.component.ts", "C:\\console\\aava-ui-web\\projects\\elder-wand\\src\\app\\shared\\components\\agents\\agents.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  Input,\r\n  Pipe,\r\n  PipeTransform,\r\n  ViewEncapsulation,\r\n  OnInit,\r\n} from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Router, RouterModule } from '@angular/router';\r\n\r\nimport { Agent, EntityResult } from '../../interfaces/agent-list.interface';\r\nimport {\r\n  FilterTabsComponent,\r\n  FilterTab,\r\n} from '../filter-tabs/filter-tabs.component';\r\nimport { IconComponent } from '@ava/play-comp-library';\r\nimport { DrawerService } from '../../services/drawer.service';\r\nimport { EntityService } from '../../services/entity.service';\r\nimport { RevelioSearchResult } from '../../services/search.service';\r\n\r\n@Pipe({\r\n  name: 'truncate',\r\n  standalone: true,\r\n})\r\nexport class TruncatePipe implements PipeTransform {\r\n  transform(value: string, limit = 75): string {\r\n    if (!value) return '';\r\n    if (value.length <= limit) return value;\r\n    return value.substring(0, limit) + '...';\r\n  }\r\n}\r\n\r\n@Component({\r\n  selector: 'app-agents',\r\n  templateUrl: './agents.component.html',\r\n  styleUrls: ['./agents.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    RouterModule,\r\n    FilterTabsComponent,\r\n    TruncatePipe,\r\n    IconComponent,\r\n  ],\r\n  encapsulation: ViewEncapsulation.None,\r\n})\r\nexport class AgentsComponent implements OnInit {\r\n  @Input() agents: Agent[] = [];\r\n  @Input() showExploreButton = true;\r\n  @Input() showTwoColumns = false;\r\n  @Input() isMarketplace = false; // New input to detect marketplace context\r\n  @Input() searchResults: RevelioSearchResult[] = []; // Search results from search bar\r\n  @Input() searchQuery: string = ''; // Current search query\r\n  @Input() isSearchLoading: boolean = false; // Search loading state\r\n\r\n  private originalAgents: Agent[] = [];\r\n  entityAgents: EntityResult[] = [];\r\n  loading = false;\r\n\r\n  activeFilter = 'all';\r\n  filterTabs: FilterTab[] = [\r\n    { id: 'all', label: 'All', priority: 100 },\r\n    {\r\n      id: 'experience',\r\n      label: 'Experience Studio',\r\n      icon: 'lightbulb',\r\n      priority: 90,\r\n    },\r\n    { id: 'product', label: 'Product Studio', icon: 'box', priority: 80 },\r\n    { id: 'data', label: 'Data Studio', icon: 'database', priority: 70 },\r\n    { id: 'finops', label: 'FinOps Studio', icon: 'dollar-sign', priority: 60 },\r\n  ];\r\n\r\n  constructor(\r\n    private readonly router: Router,\r\n    private readonly drawerService: DrawerService,\r\n    private readonly entityService: EntityService,\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.originalAgents = [...this.agents];\r\n    this.loadEntityAgents();\r\n  }\r\n\r\n  /**\r\n   * Load agents from the entity API\r\n   */\r\n  private loadEntityAgents(): void {\r\n    this.loading = true;\r\n    this.entityService.getAgents(0, 50).subscribe({\r\n      next: (agents: EntityResult[]) => {\r\n        this.entityAgents = agents;\r\n        this.loading = false;\r\n      },\r\n      error: (error: any) => {\r\n        console.error('Error loading agents:', error);\r\n        this.loading = false;\r\n      },\r\n    });\r\n  }\r\n\r\n  onFilterChange(filterId: string) {\r\n    this.activeFilter = filterId;\r\n    // Filter agents by studio type if not 'all'\r\n    if (filterId === 'all') {\r\n      this.agents = [...this.originalAgents];\r\n    } else {\r\n      const studioMap: any = {\r\n        experience: 'Experience Studio',\r\n        product: 'Product Studio',\r\n        data: 'Data Studio',\r\n        finops: 'FinOps Studio',\r\n      };\r\n      this.agents = this.originalAgents.filter(\r\n        (agent: Agent) => agent.studio?.type === studioMap[filterId],\r\n      );\r\n    }\r\n  }\r\n\r\n  navigate() {\r\n    this.router.navigateByUrl('/agent-list');\r\n  }\r\n\r\n  /**\r\n   * Shows the agent details panel for the selected agent\r\n   * @param agent The agent to display details for\r\n   */\r\n  showAgentDetails(agent: Agent): void {\r\n    // Find the corresponding entity data\r\n    const entityAgent = this.entityAgents.find((ea) => ea.id === agent.id);\r\n\r\n    console.log('Showing agent details for:', agent, entityAgent);\r\n\r\n    this.drawerService.open({\r\n      data: { ...agent, entityData: entityAgent },\r\n      width: '650px',\r\n      position: 'right',\r\n      onGoToPlayground: (selectedAgent: Agent) =>\r\n        this.goToPlayground(selectedAgent),\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Navigates to the playground for the selected agent\r\n   */\r\n  goToPlayground(agent: Agent): void {\r\n    if (agent) {\r\n      // You can replace this with the actual navigation logic\r\n      this.router.navigate(['/playground', agent.id]);\r\n      // Close the drawer after navigation\r\n      this.drawerService.close();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Gets the agents to display based on the showTwoColumns setting and search results\r\n   * Returns only 6 agents (3 rows x 2 columns) when showTwoColumns is true\r\n   */\r\n  getDisplayedAgents(): Agent[] {\r\n    let agentsToDisplay = this.agents;\r\n\r\n    // If there are search results, filter agents to show only those that match\r\n    if (this.searchResults.length > 0 && this.searchQuery.trim()) {\r\n      const searchResultIds = this.searchResults.map((result) => result.id);\r\n\r\n      // Check if we have all the search result agents in our current list\r\n      const missingAgentIds = searchResultIds.filter(\r\n        (id) => !this.agents.some((agent) => agent.id.toString() === id),\r\n      );\r\n\r\n      if (missingAgentIds.length > 0) {\r\n        console.log('Missing agent IDs from search results:', missingAgentIds);\r\n        // Create agent objects directly from search results\r\n        const searchResultAgents = this.searchResults.map((revelioResult) => ({\r\n          id: parseInt(revelioResult.id) || 0, // Convert string id to number\r\n          title: revelioResult.metadata.name || 'Unknown Agent',\r\n          description:\r\n            revelioResult.metadata.description ||\r\n            revelioResult.metadata.details ||\r\n            '',\r\n          rating: 4.5, // Default rating\r\n          studio: {\r\n            name: 'Experience Studio', // Default studio\r\n            type: 'Experience Studio',\r\n            backgroundColor: '#FFF4F9',\r\n            textColor: '#E91E63',\r\n          },\r\n          developer: revelioResult.metadata.createdBy || 'Unknown Developer',\r\n          users: Math.floor(Math.random() * 100) + 10, // Random users count\r\n        }));\r\n\r\n        // Filter by active tab if not 'all' and we're in marketplace\r\n        if (this.activeFilter !== 'all' && this.isMarketplace) {\r\n          const studioMap: any = {\r\n            experience: 'Experience Studio',\r\n            product: 'Product Studio',\r\n            data: 'Data Studio',\r\n            finops: 'FinOps Studio',\r\n          };\r\n          const targetStudioType = studioMap[this.activeFilter];\r\n          agentsToDisplay = searchResultAgents.filter(\r\n            (agent) => agent.studio?.type === targetStudioType,\r\n          );\r\n        } else {\r\n          agentsToDisplay = searchResultAgents;\r\n        }\r\n      } else {\r\n        // All search results are in our current list, filter normally\r\n        let filteredAgents = this.agents.filter((agent) => {\r\n          const isMatch = searchResultIds.includes(agent.id.toString());\r\n          console.log(`Agent ${agent.id} (${agent.title}) - Match: ${isMatch}`);\r\n          return isMatch;\r\n        });\r\n\r\n        // Further filter by active tab if not 'all' and we're in marketplace\r\n        if (this.activeFilter !== 'all' && this.isMarketplace) {\r\n          const studioMap: any = {\r\n            experience: 'Experience Studio',\r\n            product: 'Product Studio',\r\n            data: 'Data Studio',\r\n            finops: 'FinOps Studio',\r\n          };\r\n          const targetStudioType = studioMap[this.activeFilter];\r\n          agentsToDisplay = filteredAgents.filter(\r\n            (agent) => agent.studio?.type === targetStudioType,\r\n          );\r\n        } else {\r\n          agentsToDisplay = filteredAgents;\r\n        }\r\n      }\r\n    }\r\n\r\n    if (this.showTwoColumns) {\r\n      return agentsToDisplay.slice(0, 6); // Show only 6 agents for 3 rows x 2 columns\r\n    }\r\n    return agentsToDisplay; // Show all agents in normal mode\r\n  }\r\n\r\n  /**\r\n   * Check if we should show the search results message (only in marketplace)\r\n   */\r\n  get shouldShowSearchResults(): boolean {\r\n    return (\r\n      this.searchResults.length > 0 &&\r\n      this.searchQuery.trim() !== '' &&\r\n      this.isMarketplace\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get the search results count\r\n   */\r\n  get searchResultsCount(): number {\r\n    return this.searchResults.length;\r\n  }\r\n}\r\n", "<div class=\"agents-container\">\r\n  <app-filter-tabs *ngIf=\"!showExploreButton\" [tabs]=\"filterTabs\" [activeTab]=\"activeFilter\"\r\n    (tabChange)=\"onFilterChange($event)\">\r\n  </app-filter-tabs>\r\n\r\n  <div class=\"agents-grid row\">\r\n    <div [ngClass]=\"showTwoColumns ? 'col-12 col-md-6' : 'col-12 col-md-6 col-lg-3'\" *ngFor=\"let agent of getDisplayedAgents(); let i = index\">\r\n      <div class=\"agent-card\" [class.marketplace-card]=\"isMarketplace\" (click)=\"showAgentDetails(agent)\"\r\n        >\r\n        <div class=\"card-content\">\r\n          <div class=\"card-header\">\r\n            <h2>{{ agent.title }}</h2>\r\n            <div class=\"rating\">\r\n              <ava-icon iconName=\"star\" iconSize=\"18px\" iconColor=\"#FFD700\" class=\"agent_star-icon\"></ava-icon>\r\n              {{ agent.rating }}\r\n            </div>\r\n          </div>\r\n\r\n          <p class=\"description\">{{ agent.description | truncate: 75 }}</p>\r\n\r\n          <!-- Footer comments - only show in marketplace -->\r\n          <div class=\"card-footer\" *ngIf=\"isMarketplace\">\r\n            <div class=\"users\">\r\n              <ava-icon iconName=\"user\" iconSize=\"16px\" iconColor=\"#858aad\" class=\"profile-svg-icon\"></ava-icon>\r\n              {{ agent.users }}\r\n            </div>\r\n            <div class=\"agent-time-ago\">\r\n              3 days ago\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- <div class=\"explore-more\" *ngIf=\"showExploreButton\">\r\n    <awe-button label=\"Explore More\" variant=\"primary\" class=\"w-100\" width=\"245px\"\r\n      gradient=\"linear-gradient(90deg, #FAA74A 0%, #4383E6 100%)\" (click)=\"navigate()\"></awe-button>\r\n  </div> -->\r\n\r\n\r\n</div>\r\n"], "mappings": "AAQA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAiBC,YAAY,QAAQ,iBAAiB;AAGtD,SACEC,mBAAmB,QAEd,sCAAsC;AAC7C,SAASC,aAAa,QAAQ,wBAAwB;;;;;;;;;ICfpDC,EAAA,CAAAC,cAAA,yBACuC;IAArCD,EAAA,CAAAE,UAAA,uBAAAC,gFAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAaF,MAAA,CAAAG,cAAA,CAAAN,MAAA,CAAsB;IAAA,EAAC;IACtCJ,EAAA,CAAAW,YAAA,EAAkB;;;;IAF8CX,EAApB,CAAAY,UAAA,SAAAL,MAAA,CAAAM,UAAA,CAAmB,cAAAN,MAAA,CAAAO,YAAA,CAA2B;;;;;IAqBhFd,EADF,CAAAC,cAAA,cAA+C,cAC1B;IACjBD,EAAA,CAAAe,SAAA,mBAAkG;IAClGf,EAAA,CAAAgB,MAAA,GACF;IAAAhB,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAAgB,MAAA,mBACF;IACFhB,EADE,CAAAW,YAAA,EAAM,EACF;;;;IALFX,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAkB,kBAAA,MAAAC,QAAA,CAAAC,KAAA,MACF;;;;;;IAlBNpB,EADF,CAAAC,cAAA,aAA2I,aAEtI;IAD8DD,EAAA,CAAAE,UAAA,mBAAAmB,oDAAA;MAAA,MAAAF,QAAA,GAAAnB,EAAA,CAAAK,aAAA,CAAAiB,GAAA,EAAAC,SAAA;MAAA,MAAAhB,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAiB,gBAAA,CAAAL,QAAA,CAAuB;IAAA,EAAC;IAI5FnB,EAFJ,CAAAC,cAAA,aAA0B,aACC,SACnB;IAAAD,EAAA,CAAAgB,MAAA,GAAiB;IAAAhB,EAAA,CAAAW,YAAA,EAAK;IAC1BX,EAAA,CAAAC,cAAA,aAAoB;IAClBD,EAAA,CAAAe,SAAA,mBAAiG;IACjGf,EAAA,CAAAgB,MAAA,GACF;IACFhB,EADE,CAAAW,YAAA,EAAM,EACF;IAENX,EAAA,CAAAC,cAAA,YAAuB;IAAAD,EAAA,CAAAgB,MAAA,IAAsC;;IAAAhB,EAAA,CAAAW,YAAA,EAAI;IAGjEX,EAAA,CAAAyB,UAAA,KAAAC,qCAAA,kBAA+C;IAWrD1B,EAFI,CAAAW,YAAA,EAAM,EACF,EACF;;;;;IA1BDX,EAAA,CAAAY,UAAA,YAAAL,MAAA,CAAAoB,cAAA,kDAA2E;IACtD3B,EAAA,CAAAiB,SAAA,EAAwC;IAAxCjB,EAAA,CAAA4B,WAAA,qBAAArB,MAAA,CAAAsB,aAAA,CAAwC;IAItD7B,EAAA,CAAAiB,SAAA,GAAiB;IAAjBjB,EAAA,CAAA8B,iBAAA,CAAAX,QAAA,CAAAY,KAAA,CAAiB;IAGnB/B,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAAkB,kBAAA,MAAAC,QAAA,CAAAa,MAAA,MACF;IAGqBhC,EAAA,CAAAiB,SAAA,GAAsC;IAAtCjB,EAAA,CAAA8B,iBAAA,CAAA9B,EAAA,CAAAiC,WAAA,QAAAd,QAAA,CAAAe,WAAA,MAAsC;IAGnClC,EAAA,CAAAiB,SAAA,GAAmB;IAAnBjB,EAAA,CAAAY,UAAA,SAAAL,MAAA,CAAAsB,aAAA,CAAmB;;;ADIvD,WAAaM,YAAY;EAAnB,MAAOA,YAAY;IACvBC,SAASA,CAACC,KAAa,EAAEC,KAAK,GAAG,EAAE;MACjC,IAAI,CAACD,KAAK,EAAE,OAAO,EAAE;MACrB,IAAIA,KAAK,CAACE,MAAM,IAAID,KAAK,EAAE,OAAOD,KAAK;MACvC,OAAOA,KAAK,CAACG,SAAS,CAAC,CAAC,EAAEF,KAAK,CAAC,GAAG,KAAK;IAC1C;;uCALWH,YAAY;IAAA;;;YAAZA,YAAY;MAAAM,IAAA;IAAA;;SAAZN,YAAY;AAAA;AAsBzB,WAAaO,eAAe;EAAtB,MAAOA,eAAe;IA4BPC,MAAA;IACAC,aAAA;IACAC,aAAA;IA7BVC,MAAM,GAAY,EAAE;IACpBC,iBAAiB,GAAG,IAAI;IACxBpB,cAAc,GAAG,KAAK;IACtBE,aAAa,GAAG,KAAK,CAAC,CAAC;IACvBmB,aAAa,GAA0B,EAAE,CAAC,CAAC;IAC3CC,WAAW,GAAW,EAAE,CAAC,CAAC;IAC1BC,eAAe,GAAY,KAAK,CAAC,CAAC;IAEnCC,cAAc,GAAY,EAAE;IACpCC,YAAY,GAAmB,EAAE;IACjCC,OAAO,GAAG,KAAK;IAEfvC,YAAY,GAAG,KAAK;IACpBD,UAAU,GAAgB,CACxB;MAAEyC,EAAE,EAAE,KAAK;MAAEC,KAAK,EAAE,KAAK;MAAEC,QAAQ,EAAE;IAAG,CAAE,EAC1C;MACEF,EAAE,EAAE,YAAY;MAChBC,KAAK,EAAE,mBAAmB;MAC1BE,IAAI,EAAE,WAAW;MACjBD,QAAQ,EAAE;KACX,EACD;MAAEF,EAAE,EAAE,SAAS;MAAEC,KAAK,EAAE,gBAAgB;MAAEE,IAAI,EAAE,KAAK;MAAED,QAAQ,EAAE;IAAE,CAAE,EACrE;MAAEF,EAAE,EAAE,MAAM;MAAEC,KAAK,EAAE,aAAa;MAAEE,IAAI,EAAE,UAAU;MAAED,QAAQ,EAAE;IAAE,CAAE,EACpE;MAAEF,EAAE,EAAE,QAAQ;MAAEC,KAAK,EAAE,eAAe;MAAEE,IAAI,EAAE,aAAa;MAAED,QAAQ,EAAE;IAAE,CAAE,CAC5E;IAEDE,YACmBf,MAAc,EACdC,aAA4B,EAC5BC,aAA4B;MAF5B,KAAAF,MAAM,GAANA,MAAM;MACN,KAAAC,aAAa,GAAbA,aAAa;MACb,KAAAC,aAAa,GAAbA,aAAa;IAC7B;IAEHc,QAAQA,CAAA;MACN,IAAI,CAACR,cAAc,GAAG,CAAC,GAAG,IAAI,CAACL,MAAM,CAAC;MACtC,IAAI,CAACc,gBAAgB,EAAE;IACzB;IAEA;;;IAGQA,gBAAgBA,CAAA;MACtB,IAAI,CAACP,OAAO,GAAG,IAAI;MACnB,IAAI,CAACR,aAAa,CAACgB,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAACC,SAAS,CAAC;QAC5CC,IAAI,EAAGjB,MAAsB,IAAI;UAC/B,IAAI,CAACM,YAAY,GAAGN,MAAM;UAC1B,IAAI,CAACO,OAAO,GAAG,KAAK;QACtB,CAAC;QACDW,KAAK,EAAGA,KAAU,IAAI;UACpBC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;UAC7C,IAAI,CAACX,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;IACJ;IAEA3C,cAAcA,CAACwD,QAAgB;MAC7B,IAAI,CAACpD,YAAY,GAAGoD,QAAQ;MAC5B;MACA,IAAIA,QAAQ,KAAK,KAAK,EAAE;QACtB,IAAI,CAACpB,MAAM,GAAG,CAAC,GAAG,IAAI,CAACK,cAAc,CAAC;MACxC,CAAC,MAAM;QACL,MAAMgB,SAAS,GAAQ;UACrBC,UAAU,EAAE,mBAAmB;UAC/BC,OAAO,EAAE,gBAAgB;UACzBC,IAAI,EAAE,aAAa;UACnBC,MAAM,EAAE;SACT;QACD,IAAI,CAACzB,MAAM,GAAG,IAAI,CAACK,cAAc,CAACqB,MAAM,CACrCC,KAAY,IAAKA,KAAK,CAACC,MAAM,EAAEC,IAAI,KAAKR,SAAS,CAACD,QAAQ,CAAC,CAC7D;MACH;IACF;IAEAU,QAAQA,CAAA;MACN,IAAI,CAACjC,MAAM,CAACkC,aAAa,CAAC,aAAa,CAAC;IAC1C;IAEA;;;;IAIArD,gBAAgBA,CAACiD,KAAY;MAC3B;MACA,MAAMK,WAAW,GAAG,IAAI,CAAC1B,YAAY,CAAC2B,IAAI,CAAEC,EAAE,IAAKA,EAAE,CAAC1B,EAAE,KAAKmB,KAAK,CAACnB,EAAE,CAAC;MAEtEW,OAAO,CAACgB,GAAG,CAAC,4BAA4B,EAAER,KAAK,EAAEK,WAAW,CAAC;MAE7D,IAAI,CAAClC,aAAa,CAACsC,IAAI,CAAC;QACtBZ,IAAI,EAAE;UAAE,GAAGG,KAAK;UAAEU,UAAU,EAAEL;QAAW,CAAE;QAC3CM,KAAK,EAAE,OAAO;QACdC,QAAQ,EAAE,OAAO;QACjBC,gBAAgB,EAAGC,aAAoB,IACrC,IAAI,CAACC,cAAc,CAACD,aAAa;OACpC,CAAC;IACJ;IAEA;;;IAGAC,cAAcA,CAACf,KAAY;MACzB,IAAIA,KAAK,EAAE;QACT;QACA,IAAI,CAAC9B,MAAM,CAACiC,QAAQ,CAAC,CAAC,aAAa,EAAEH,KAAK,CAACnB,EAAE,CAAC,CAAC;QAC/C;QACA,IAAI,CAACV,aAAa,CAAC6C,KAAK,EAAE;MAC5B;IACF;IAEA;;;;IAIAC,kBAAkBA,CAAA;MAChB,IAAIC,eAAe,GAAG,IAAI,CAAC7C,MAAM;MAEjC;MACA,IAAI,IAAI,CAACE,aAAa,CAACT,MAAM,GAAG,CAAC,IAAI,IAAI,CAACU,WAAW,CAAC2C,IAAI,EAAE,EAAE;QAC5D,MAAMC,eAAe,GAAG,IAAI,CAAC7C,aAAa,CAAC8C,GAAG,CAAEC,MAAM,IAAKA,MAAM,CAACzC,EAAE,CAAC;QAErE;QACA,MAAM0C,eAAe,GAAGH,eAAe,CAACrB,MAAM,CAC3ClB,EAAE,IAAK,CAAC,IAAI,CAACR,MAAM,CAACmD,IAAI,CAAExB,KAAK,IAAKA,KAAK,CAACnB,EAAE,CAAC4C,QAAQ,EAAE,KAAK5C,EAAE,CAAC,CACjE;QAED,IAAI0C,eAAe,CAACzD,MAAM,GAAG,CAAC,EAAE;UAC9B0B,OAAO,CAACgB,GAAG,CAAC,wCAAwC,EAAEe,eAAe,CAAC;UACtE;UACA,MAAMG,kBAAkB,GAAG,IAAI,CAACnD,aAAa,CAAC8C,GAAG,CAAEM,aAAa,KAAM;YACpE9C,EAAE,EAAE+C,QAAQ,CAACD,aAAa,CAAC9C,EAAE,CAAC,IAAI,CAAC;YAAE;YACrCvB,KAAK,EAAEqE,aAAa,CAACE,QAAQ,CAACC,IAAI,IAAI,eAAe;YACrDrE,WAAW,EACTkE,aAAa,CAACE,QAAQ,CAACpE,WAAW,IAClCkE,aAAa,CAACE,QAAQ,CAACE,OAAO,IAC9B,EAAE;YACJxE,MAAM,EAAE,GAAG;YAAE;YACb0C,MAAM,EAAE;cACN6B,IAAI,EAAE,mBAAmB;cAAE;cAC3B5B,IAAI,EAAE,mBAAmB;cACzB8B,eAAe,EAAE,SAAS;cAC1BC,SAAS,EAAE;aACZ;YACDC,SAAS,EAAEP,aAAa,CAACE,QAAQ,CAACM,SAAS,IAAI,mBAAmB;YAClExF,KAAK,EAAEyF,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,EAAE,CAAE;WAC9C,CAAC,CAAC;UAEH;UACA,IAAI,IAAI,CAACjG,YAAY,KAAK,KAAK,IAAI,IAAI,CAACe,aAAa,EAAE;YACrD,MAAMsC,SAAS,GAAQ;cACrBC,UAAU,EAAE,mBAAmB;cAC/BC,OAAO,EAAE,gBAAgB;cACzBC,IAAI,EAAE,aAAa;cACnBC,MAAM,EAAE;aACT;YACD,MAAMyC,gBAAgB,GAAG7C,SAAS,CAAC,IAAI,CAACrD,YAAY,CAAC;YACrD6E,eAAe,GAAGQ,kBAAkB,CAAC3B,MAAM,CACxCC,KAAK,IAAKA,KAAK,CAACC,MAAM,EAAEC,IAAI,KAAKqC,gBAAgB,CACnD;UACH,CAAC,MAAM;YACLrB,eAAe,GAAGQ,kBAAkB;UACtC;QACF,CAAC,MAAM;UACL;UACA,IAAIc,cAAc,GAAG,IAAI,CAACnE,MAAM,CAAC0B,MAAM,CAAEC,KAAK,IAAI;YAChD,MAAMyC,OAAO,GAAGrB,eAAe,CAACsB,QAAQ,CAAC1C,KAAK,CAACnB,EAAE,CAAC4C,QAAQ,EAAE,CAAC;YAC7DjC,OAAO,CAACgB,GAAG,CAAC,SAASR,KAAK,CAACnB,EAAE,KAAKmB,KAAK,CAAC1C,KAAK,cAAcmF,OAAO,EAAE,CAAC;YACrE,OAAOA,OAAO;UAChB,CAAC,CAAC;UAEF;UACA,IAAI,IAAI,CAACpG,YAAY,KAAK,KAAK,IAAI,IAAI,CAACe,aAAa,EAAE;YACrD,MAAMsC,SAAS,GAAQ;cACrBC,UAAU,EAAE,mBAAmB;cAC/BC,OAAO,EAAE,gBAAgB;cACzBC,IAAI,EAAE,aAAa;cACnBC,MAAM,EAAE;aACT;YACD,MAAMyC,gBAAgB,GAAG7C,SAAS,CAAC,IAAI,CAACrD,YAAY,CAAC;YACrD6E,eAAe,GAAGsB,cAAc,CAACzC,MAAM,CACpCC,KAAK,IAAKA,KAAK,CAACC,MAAM,EAAEC,IAAI,KAAKqC,gBAAgB,CACnD;UACH,CAAC,MAAM;YACLrB,eAAe,GAAGsB,cAAc;UAClC;QACF;MACF;MAEA,IAAI,IAAI,CAACtF,cAAc,EAAE;QACvB,OAAOgE,eAAe,CAACyB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACtC;MACA,OAAOzB,eAAe,CAAC,CAAC;IAC1B;IAEA;;;IAGA,IAAI0B,uBAAuBA,CAAA;MACzB,OACE,IAAI,CAACrE,aAAa,CAACT,MAAM,GAAG,CAAC,IAC7B,IAAI,CAACU,WAAW,CAAC2C,IAAI,EAAE,KAAK,EAAE,IAC9B,IAAI,CAAC/D,aAAa;IAEtB;IAEA;;;IAGA,IAAIyF,kBAAkBA,CAAA;MACpB,OAAO,IAAI,CAACtE,aAAa,CAACT,MAAM;IAClC;;uCAhNWG,eAAe,EAAA1C,EAAA,CAAAuH,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAzH,EAAA,CAAAuH,iBAAA,CAAAG,EAAA,CAAAC,aAAA,GAAA3H,EAAA,CAAAuH,iBAAA,CAAAK,EAAA,CAAAC,aAAA;IAAA;;YAAfnF,eAAe;MAAAoF,SAAA;MAAAC,MAAA;QAAAjF,MAAA;QAAAC,iBAAA;QAAApB,cAAA;QAAAE,aAAA;QAAAmB,aAAA;QAAAC,WAAA;QAAAC,eAAA;MAAA;MAAA8E,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC/C5BrI,EAAA,CAAAC,cAAA,aAA8B;UAC5BD,EAAA,CAAAyB,UAAA,IAAA8G,0CAAA,6BACuC;UAGvCvI,EAAA,CAAAC,cAAA,aAA6B;UAC3BD,EAAA,CAAAyB,UAAA,IAAA+G,8BAAA,mBAA2I;UAmC/IxI,EARE,CAAAW,YAAA,EAAM,EAQF;;;UAxCcX,EAAA,CAAAiB,SAAA,EAAwB;UAAxBjB,EAAA,CAAAY,UAAA,UAAA0H,GAAA,CAAAvF,iBAAA,CAAwB;UAK2D/C,EAAA,CAAAiB,SAAA,GAAyB;UAAzBjB,EAAA,CAAAY,UAAA,YAAA0H,GAAA,CAAA5C,kBAAA,GAAyB;;;qBDiC5H9F,YAAY,EAAA6I,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EACZ/I,YAAY,EACZC,mBAAmB,EAhBVqC,YAAY,EAkBrBpC,aAAa;MAAA8I,MAAA;MAAAC,aAAA;IAAA;;SAIJpG,eAAe;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}