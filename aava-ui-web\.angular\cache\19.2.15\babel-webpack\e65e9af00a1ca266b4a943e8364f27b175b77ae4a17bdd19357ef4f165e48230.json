{"ast": null, "code": "import clipRectangle from \"./rectangle.js\";\nexport default function () {\n  var x0 = 0,\n    y0 = 0,\n    x1 = 960,\n    y1 = 500,\n    cache,\n    cacheStream,\n    clip;\n  return clip = {\n    stream: function (stream) {\n      return cache && cacheStream === stream ? cache : cache = clipRectangle(x0, y0, x1, y1)(cacheStream = stream);\n    },\n    extent: function (_) {\n      return arguments.length ? (x0 = +_[0][0], y0 = +_[0][1], x1 = +_[1][0], y1 = +_[1][1], cache = cacheStream = null, clip) : [[x0, y0], [x1, y1]];\n    }\n  };\n}", "map": {"version": 3, "names": ["clipRectangle", "x0", "y0", "x1", "y1", "cache", "cacheStream", "clip", "stream", "extent", "_", "arguments", "length"], "sources": ["C:/console/aava-ui-web/node_modules/d3-geo/src/clip/extent.js"], "sourcesContent": ["import clipRectangle from \"./rectangle.js\";\n\nexport default function() {\n  var x0 = 0,\n      y0 = 0,\n      x1 = 960,\n      y1 = 500,\n      cache,\n      cacheStream,\n      clip;\n\n  return clip = {\n    stream: function(stream) {\n      return cache && cacheStream === stream ? cache : cache = clipRectangle(x0, y0, x1, y1)(cacheStream = stream);\n    },\n    extent: function(_) {\n      return arguments.length ? (x0 = +_[0][0], y0 = +_[0][1], x1 = +_[1][0], y1 = +_[1][1], cache = cacheStream = null, clip) : [[x0, y0], [x1, y1]];\n    }\n  };\n}\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,gBAAgB;AAE1C,eAAe,YAAW;EACxB,IAAIC,EAAE,GAAG,CAAC;IACNC,EAAE,GAAG,CAAC;IACNC,EAAE,GAAG,GAAG;IACRC,EAAE,GAAG,GAAG;IACRC,KAAK;IACLC,WAAW;IACXC,IAAI;EAER,OAAOA,IAAI,GAAG;IACZC,MAAM,EAAE,SAAAA,CAASA,MAAM,EAAE;MACvB,OAAOH,KAAK,IAAIC,WAAW,KAAKE,MAAM,GAAGH,KAAK,GAAGA,KAAK,GAAGL,aAAa,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,CAACE,WAAW,GAAGE,MAAM,CAAC;IAC9G,CAAC;IACDC,MAAM,EAAE,SAAAA,CAASC,CAAC,EAAE;MAClB,OAAOC,SAAS,CAACC,MAAM,IAAIX,EAAE,GAAG,CAACS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAER,EAAE,GAAG,CAACQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEP,EAAE,GAAG,CAACO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEN,EAAE,GAAG,CAACM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEL,KAAK,GAAGC,WAAW,GAAG,IAAI,EAAEC,IAAI,IAAI,CAAC,CAACN,EAAE,EAAEC,EAAE,CAAC,EAAE,CAACC,EAAE,EAAEC,EAAE,CAAC,CAAC;IACjJ;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}