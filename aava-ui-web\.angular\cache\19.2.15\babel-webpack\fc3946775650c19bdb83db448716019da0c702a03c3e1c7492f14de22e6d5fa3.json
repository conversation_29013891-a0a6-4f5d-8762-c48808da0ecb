{"ast": null, "code": "import { signal, inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, Validators } from '@angular/forms';\nimport logintext from './login.json';\nimport { AvaTextboxComponent, IconComponent, ButtonComponent, PopupComponent } from '@ava/play-comp-library';\nimport { AuthService } from '@shared/auth/services/auth.service';\nimport { TokenStorageService } from '../../../auth/services/token-storage.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nexport let LoginComponent = /*#__PURE__*/(() => {\n  class LoginComponent {\n    fb;\n    router;\n    loginMode = signal('sso');\n    isLoading = signal(false);\n    showPassword = signal(false);\n    loginForm;\n    errorMessage = signal(null);\n    showErrorPopup = signal(false);\n    popupMessage = signal('');\n    labels = logintext;\n    authService = inject(AuthService);\n    tokenStorage = inject(TokenStorageService);\n    constructor(fb, router) {\n      this.fb = fb;\n      this.router = router;\n      this.loginForm = this.fb.group({\n        username: ['', [Validators.required]],\n        password: ['', Validators.required],\n        keepSignedIn: [false]\n      });\n    }\n    ngOnInit() {\n      const storedLoginType = this.tokenStorage.getLoginType();\n      if (storedLoginType === 'sso' || storedLoginType === 'basic') {\n        this.loginMode.set(storedLoginType === 'basic' ? 'form' : 'sso');\n      } else {\n        this.loginMode.set('sso');\n      }\n    }\n    getControl(name) {\n      return this.loginForm.get(name);\n    }\n    onBasicLogin() {\n      if (this.loginForm.valid) {\n        this.isLoading.set(true);\n        this.errorMessage.set(null);\n        const {\n          username,\n          password\n        } = this.loginForm.value;\n        this.authService.basicLoginWithCredentials(username, password).subscribe({\n          next: () => {\n            this.tokenStorage.storeLoginType('basic');\n            const redirectUrl = this.authService.getPostLoginRedirectUrl();\n            this.router.navigate([redirectUrl]);\n          },\n          error: error => {\n            this.isLoading.set(false);\n            let errorMessage = '';\n            if (error.error) {\n              // errorMessage = error.error;\n            } else if (error.message) {\n              errorMessage = 'Invalid username or password. Please try again.';\n            }\n            this.popupMessage.set(errorMessage);\n            this.showErrorPopup.set(true);\n            console.error('Login failed:', error);\n          }\n        });\n      } else {\n        this.markFormGroupTouched();\n      }\n    }\n    onCompanyLogin() {\n      this.isLoading.set(true);\n      this.errorMessage.set(null);\n      this.authService.loginSSO().subscribe({\n        next: () => {\n          this.isLoading.set(false);\n        },\n        error: error => {\n          console.error('Login failed:', error);\n          this.errorMessage.set('Failed to initiate login with company account.');\n          this.isLoading.set(false);\n        }\n      });\n    }\n    togglePasswordVisibility() {\n      this.showPassword.set(!this.showPassword());\n    }\n    clearInput(fieldName) {\n      this.loginForm.get(fieldName)?.setValue('');\n      this.loginForm.get(fieldName)?.markAsTouched();\n    }\n    clearUsername() {\n      this.clearInput('username');\n    }\n    markFormGroupTouched() {\n      Object.keys(this.loginForm.controls).forEach(key => {\n        const control = this.loginForm.get(key);\n        control?.markAsTouched();\n      });\n    }\n    getFieldError(fieldName) {\n      const field = this.loginForm.get(fieldName);\n      if (field?.touched && field?.errors) {\n        if (field.errors['required']) {\n          return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`;\n        }\n        if (field.errors['email']) {\n          return 'Please enter a valid email address';\n        }\n        if (field.errors['minlength']) {\n          return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} must be at least ${field.errors['minlength'].requiredLength} characters`;\n        }\n      }\n      return '';\n    }\n    // Check if both username and password fields are filled\n    areFieldsFilled() {\n      const username = this.loginForm.get('username')?.value;\n      const password = this.loginForm.get('password')?.value;\n      return !!username && !!password && username.trim() !== '' && password.trim() !== '';\n    }\n    onForgotPassword() {\n      this.router.navigate(['/forgot-password']);\n    }\n    onTroubleSigningIn() {\n      this.router.navigate(['/help']);\n    }\n    static ɵfac = function LoginComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 25,\n      vars: 39,\n      consts: [[\"id\", \"login-container\", 1, \"row\"], [1, \"col-5\", \"p-0\"], [\"src\", \"login.png\", \"alt\", \"\", 1, \"login-image\"], [1, \"col-7\", \"p-0\", \"login-section\"], [1, \"sign-in-container\"], [1, \"heading\"], [1, \"mb-2\", \"main-heading\"], [1, \"sub-heading\"], [1, \"new-login-form\", 3, \"formGroup\"], [1, \"form-field-wrapper\"], [\"formControlName\", \"username\", 3, \"label\", \"placeholder\", \"required\", \"error\", \"iconSeparator\", \"iconSpacing\"], [\"slot\", \"icon-end\", \"iconName\", \"x\", 3, \"click\", \"iconSize\", \"cursor\", \"disabled\"], [1, \"form-field-wrapper\", \"mt-4\", \"mb-4\"], [\"formControlName\", \"password\", 3, \"label\", \"type\", \"placeholder\", \"required\", \"error\", \"iconSeparator\", \"iconSpacing\"], [\"slot\", \"icon-end\", 3, \"click\", \"iconName\", \"iconSize\", \"cursor\", \"disabled\"], [1, \"new-buttons-container\"], [1, \"sign-in-button\", \"mb-5\"], [\"variant\", \"primary\", \"size\", \"large\", 1, \"mb-4\", 3, \"userClick\", \"label\", \"processing\", \"width\", \"disabled\"], [1, \"new-separator\"], [1, \"login-with-company\", \"mt-5\"], [\"variant\", \"secondary\", \"size\", \"large\", 3, \"userClick\", \"label\", \"width\"], [\"title\", \"Login Failed\", \"headerIconName\", \"alert-circle\", \"iconColor\", \"#dc3545\", 3, \"confirm\", \"closed\", \"show\", \"message\", \"showHeaderIcon\", \"showClose\", \"showCancel\", \"showConfirm\", \"confirmButtonLabel\", \"confirmButtonVariant\", \"confirmButtonBackground\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"img\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"h3\", 6);\n          i0.ɵɵtext(7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"p\", 7);\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"form\", 8)(11, \"div\", 9)(12, \"ava-textbox\", 10)(13, \"ava-icon\", 11);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_ava_icon_click_13_listener() {\n            return ctx.clearUsername();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(14, \"div\", 12)(15, \"ava-textbox\", 13)(16, \"ava-icon\", 14);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_ava_icon_click_16_listener() {\n            return ctx.togglePasswordVisibility();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 15)(18, \"div\", 16)(19, \"ava-button\", 17);\n          i0.ɵɵlistener(\"userClick\", function LoginComponent_Template_ava_button_userClick_19_listener() {\n            return ctx.onBasicLogin();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 18);\n          i0.ɵɵtext(21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"div\", 19)(23, \"ava-button\", 20);\n          i0.ɵɵlistener(\"userClick\", function LoginComponent_Template_ava_button_userClick_23_listener() {\n            return ctx.onCompanyLogin();\n          });\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelementStart(24, \"ava-popup\", 21);\n          i0.ɵɵlistener(\"confirm\", function LoginComponent_Template_ava_popup_confirm_24_listener() {\n            return ctx.showErrorPopup.set(false);\n          })(\"closed\", function LoginComponent_Template_ava_popup_closed_24_listener() {\n            return ctx.showErrorPopup.set(false);\n          });\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate(ctx.labels.labels.main_heading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.labels.labels.sub_heading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"label\", ctx.labels.labels.username)(\"placeholder\", ctx.labels.placeholders.username)(\"required\", true)(\"error\", ctx.getFieldError(\"username\"))(\"iconSeparator\", false)(\"iconSpacing\", \"normal\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"iconSize\", 16)(\"cursor\", true)(\"disabled\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"label\", ctx.labels.labels.password)(\"type\", ctx.showPassword() ? \"text\" : \"password\")(\"placeholder\", ctx.labels.placeholders.password)(\"required\", true)(\"error\", ctx.getFieldError(\"password\"))(\"iconSeparator\", false)(\"iconSpacing\", \"normal\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"iconName\", ctx.showPassword() ? \"eye-off\" : \"eye\")(\"iconSize\", 18)(\"cursor\", true)(\"disabled\", false);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"label\", ctx.labels.labels.sign_in + \" \" + ctx.labels.labels.arrow)(\"processing\", ctx.isLoading())(\"width\", \"100%\")(\"disabled\", !ctx.areFieldsFilled() || ctx.isLoading());\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.labels.labels.seperator, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"label\", ctx.labels.labels.login_with_company)(\"width\", \"100%\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"show\", ctx.showErrorPopup())(\"message\", ctx.popupMessage())(\"showHeaderIcon\", true)(\"showClose\", true)(\"showCancel\", false)(\"showConfirm\", true)(\"confirmButtonLabel\", \"OK\")(\"confirmButtonVariant\", \"primary\")(\"confirmButtonBackground\", \"#dc3545\");\n        }\n      },\n      dependencies: [CommonModule, ReactiveFormsModule, i1.ɵNgNoValidate, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.FormGroupDirective, i1.FormControlName, AvaTextboxComponent, IconComponent, ButtonComponent, PopupComponent],\n      styles: [\"#login-container[_ngcontent-%COMP%]   .login-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100vh;\\n}\\n#login-container[_ngcontent-%COMP%]   .login-section[_ngcontent-%COMP%] {\\n  background-color: white;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n#login-container[_ngcontent-%COMP%]   .login-section[_ngcontent-%COMP%]   .sign-in-container[_ngcontent-%COMP%] {\\n  min-width: 510px;\\n  width: 50%;\\n  margin: 0 auto;\\n  padding: 24px;\\n  background: var(--Brand-Neutral-n-50, #f0f1f2);\\n  border: 1px solid var(--Brand-Neutral-n-50, #f0f1f2);\\n  border-radius: 16px;\\n}\\n\\n.p-0[_ngcontent-%COMP%] {\\n  padding: 0;\\n}\\n\\n.mb-2[_ngcontent-%COMP%] {\\n  margin-bottom: 0.5rem;\\n}\\n\\n.mt-4[_ngcontent-%COMP%] {\\n  margin-top: 1.5rem;\\n}\\n\\n.mb-4[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n\\n.mb-5[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n}\\n\\n.mt-5[_ngcontent-%COMP%] {\\n  margin-top: 2rem;\\n}\\n\\n.heading[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 64px;\\n}\\n\\n.main-heading[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  font-weight: 700;\\n  color: #1a1a1a;\\n  margin-bottom: 8px;\\n}\\n\\n.sub-heading[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #666;\\n  font-weight: 400;\\n}\\n\\n.new-separator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  text-align: center;\\n  color: #898e99;\\n  font-size: 18px;\\n  font-weight: 600;\\n}\\n.new-separator[_ngcontent-%COMP%]::before, .new-separator[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  flex: 1;\\n  height: 1px;\\n  background: #898e99;\\n}\\n.new-separator[_ngcontent-%COMP%]::before {\\n  margin-right: 16px;\\n}\\n.new-separator[_ngcontent-%COMP%]::after {\\n  margin-left: 16px;\\n}\\n.new-separator[_ngcontent-%COMP%]   .sign-in-button[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n  return LoginComponent;\n})();", "map": {"version": 3, "names": ["signal", "inject", "CommonModule", "ReactiveFormsModule", "Validators", "logintext", "AvaTextboxComponent", "IconComponent", "ButtonComponent", "PopupComponent", "AuthService", "TokenStorageService", "LoginComponent", "fb", "router", "loginMode", "isLoading", "showPassword", "loginForm", "errorMessage", "showErrorPopup", "popupMessage", "labels", "authService", "tokenStorage", "constructor", "group", "username", "required", "password", "keepSignedIn", "ngOnInit", "storedLoginType", "getLoginType", "set", "getControl", "name", "get", "onBasicLogin", "valid", "value", "basicLoginWithCredentials", "subscribe", "next", "storeLoginType", "redirectUrl", "getPostLoginRedirectUrl", "navigate", "error", "message", "console", "markFormGroupTouched", "onCompanyLogin", "loginSSO", "togglePasswordVisibility", "clearInput", "fieldName", "setValue", "<PERSON><PERSON><PERSON><PERSON>ched", "clearUsername", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "getFieldError", "field", "touched", "errors", "char<PERSON>t", "toUpperCase", "slice", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>Filled", "trim", "onForgotPassword", "onTroubleSigningIn", "i0", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "LoginComponent_Template_ava_icon_click_13_listener", "LoginComponent_Template_ava_icon_click_16_listener", "LoginComponent_Template_ava_button_userClick_19_listener", "LoginComponent_Template_ava_button_userClick_23_listener", "LoginComponent_Template_ava_popup_confirm_24_listener", "LoginComponent_Template_ava_popup_closed_24_listener", "ɵɵadvance", "ɵɵtextInterpolate", "main_heading", "sub_heading", "ɵɵproperty", "placeholders", "sign_in", "arrow", "ɵɵtextInterpolate1", "seperator", "login_with_company", "ɵNgNoValidate", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "FormGroupDirective", "FormControlName", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\shared\\auth\\components\\login\\login.component.ts", "C:\\console\\aava-ui-web\\projects\\shared\\auth\\components\\login\\login.component.html"], "sourcesContent": ["import { Component, OnInit, signal, inject } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport {\r\n  ReactiveFormsModule,\r\n  FormBuilder,\r\n  FormGroup,\r\n  Validators,\r\n  FormControl,\r\n} from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\n\r\nimport { HttpErrorResponse } from '@angular/common/http';\r\nimport logintext from './login.json';\r\nimport {\r\n  AvaTextboxComponent,\r\n  IconComponent,\r\n  ButtonComponent,\r\n  PopupComponent,\r\n} from '@ava/play-comp-library';\r\nimport { AuthService } from '@shared/auth/services/auth.service';\r\nimport { TokenStorageService } from '../../../auth/services/token-storage.service';\r\n\r\nexport interface SavedAccount {\r\n  email: string;\r\n  profilePic?: string;\r\n  isSelected?: boolean;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-login',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    ReactiveFormsModule,\r\n    AvaTextboxComponent,\r\n    IconComponent,\r\n    ButtonComponent,\r\n    PopupComponent,\r\n  ],\r\n  templateUrl: './login.component.html',\r\n  styleUrls: ['./login.component.scss'],\r\n})\r\nexport class LoginComponent implements OnInit {\r\n  loginMode = signal<'sso' | 'form'>('sso');\r\n  isLoading = signal(false);\r\n  showPassword = signal(false);\r\n  loginForm: FormGroup;\r\n  errorMessage = signal<string | null>(null);\r\n  showErrorPopup = signal(false);\r\n  popupMessage = signal('');\r\n  public labels: any = logintext;\r\n\r\n  private authService = inject(AuthService);\r\n  private tokenStorage = inject(TokenStorageService);\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private router: Router,\r\n  ) {\r\n    this.loginForm = this.fb.group({\r\n      username: ['', [Validators.required]],\r\n      password: ['', Validators.required],\r\n      keepSignedIn: [false],\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    const storedLoginType = this.tokenStorage.getLoginType();\r\n    if (storedLoginType === 'sso' || storedLoginType === 'basic') {\r\n      this.loginMode.set(storedLoginType === 'basic' ? 'form' : 'sso');\r\n    } else {\r\n      this.loginMode.set('sso');\r\n    }\r\n  }\r\n\r\n  getControl(name: string): FormControl {\r\n    return this.loginForm.get(name) as FormControl;\r\n  }\r\n\r\n  onBasicLogin(): void {\r\n    if (this.loginForm.valid) {\r\n      this.isLoading.set(true);\r\n      this.errorMessage.set(null);\r\n\r\n      const { username, password } = this.loginForm.value;\r\n      this.authService.basicLoginWithCredentials(username, password).subscribe({\r\n        next: () => {\r\n          this.tokenStorage.storeLoginType('basic');\r\n          const redirectUrl = this.authService.getPostLoginRedirectUrl();\r\n          this.router.navigate([redirectUrl]);\r\n        },\r\n        error: (error: HttpErrorResponse) => {\r\n          this.isLoading.set(false);\r\n          let errorMessage = '';\r\n          \r\n          if (error.error) {\r\n            // errorMessage = error.error;\r\n          }\r\n          else if (error.message) {\r\n            errorMessage = 'Invalid username or password. Please try again.';\r\n          }\r\n          this.popupMessage.set(errorMessage);\r\n          this.showErrorPopup.set(true);\r\n          console.error('Login failed:', error);\r\n        },\r\n      });\r\n    } else {\r\n      this.markFormGroupTouched();\r\n    }\r\n  }\r\n\r\n  onCompanyLogin(): void {\r\n    this.isLoading.set(true);\r\n    this.errorMessage.set(null);\r\n\r\n    this.authService.loginSSO().subscribe({\r\n      next: () => {\r\n        this.isLoading.set(false);\r\n      },\r\n      error: (error) => {\r\n        console.error('Login failed:', error);\r\n        this.errorMessage.set('Failed to initiate login with company account.');\r\n        this.isLoading.set(false);\r\n      },\r\n    });\r\n  }\r\n\r\n  togglePasswordVisibility(): void {\r\n    this.showPassword.set(!this.showPassword());\r\n  }\r\n\r\n  clearInput(fieldName: string): void {\r\n    this.loginForm.get(fieldName)?.setValue('');\r\n    this.loginForm.get(fieldName)?.markAsTouched();\r\n  }\r\n\r\n  clearUsername(): void {\r\n    this.clearInput('username');\r\n  }\r\n\r\n  private markFormGroupTouched(): void {\r\n    Object.keys(this.loginForm.controls).forEach((key) => {\r\n      const control = this.loginForm.get(key);\r\n      control?.markAsTouched();\r\n    });\r\n  }\r\n\r\n  getFieldError(fieldName: string): string {\r\n    const field = this.loginForm.get(fieldName);\r\n    if (field?.touched && field?.errors) {\r\n      if (field.errors['required']) {\r\n        return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`;\r\n      }\r\n      if (field.errors['email']) {\r\n        return 'Please enter a valid email address';\r\n      }\r\n      if (field.errors['minlength']) {\r\n        return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} must be at least ${field.errors['minlength'].requiredLength} characters`;\r\n      }\r\n    }\r\n    return '';\r\n  }\r\n\r\n  // Check if both username and password fields are filled\r\n  areFieldsFilled(): boolean {\r\n    const username = this.loginForm.get('username')?.value;\r\n    const password = this.loginForm.get('password')?.value;\r\n    return !!username && !!password && username.trim() !== '' && password.trim() !== '';\r\n  }\r\n\r\n  onForgotPassword(): void {\r\n    this.router.navigate(['/forgot-password']);\r\n  }\r\n\r\n  onTroubleSigningIn(): void {\r\n    this.router.navigate(['/help']);\r\n  }\r\n}\r\n", "<div id=\"login-container\" class=\"row\">\r\n  <div class=\"col-5 p-0\">\r\n    <img class=\"login-image\" src=\"login.png\" alt=\"\" />\r\n  </div>\r\n  <div class=\"col-7 p-0 login-section\">\r\n    <div class=\"sign-in-container\">\r\n      <div class=\"heading\">\r\n        <h3 class=\"mb-2 main-heading\">{{ labels.labels.main_heading }}</h3>\r\n        <p class=\"sub-heading\">{{ labels.labels.sub_heading }}</p>\r\n      </div>\r\n      <form [formGroup]=\"loginForm\" class=\"new-login-form\">\r\n        <div class=\"form-field-wrapper\">\r\n          <ava-textbox\r\n            [label]=\"labels.labels.username\"\r\n            [placeholder]=\"labels.placeholders.username\"\r\n            [required]=\"true\"\r\n            formControlName=\"username\"\r\n            [error]=\"getFieldError('username')\"\r\n            [iconSeparator]=\"false\"\r\n            [iconSpacing]=\"'normal'\"\r\n          >\r\n            <ava-icon\r\n              slot=\"icon-end\"\r\n              iconName=\"x\"\r\n              [iconSize]=\"16\"\r\n              [cursor]=\"true\"\r\n              (click)=\"clearUsername()\"\r\n              [disabled]=\"false\"\r\n            >\r\n            </ava-icon>\r\n          </ava-textbox>\r\n        </div>\r\n        <div class=\"form-field-wrapper mt-4 mb-4\">\r\n          <ava-textbox\r\n            [label]=\"labels.labels.password\"\r\n            [type]=\"showPassword() ? 'text' : 'password'\"\r\n            [placeholder]=\"labels.placeholders.password\"\r\n            [required]=\"true\"\r\n            formControlName=\"password\"\r\n            [error]=\"getFieldError('password')\"\r\n            [iconSeparator]=\"false\"\r\n            [iconSpacing]=\"'normal'\"\r\n          >\r\n            <ava-icon\r\n              slot=\"icon-end\"\r\n              [iconName]=\"showPassword() ? 'eye-off' : 'eye'\"\r\n              [iconSize]=\"18\"\r\n              [cursor]=\"true\"\r\n              (click)=\"togglePasswordVisibility()\"\r\n              [disabled]=\"false\"\r\n            >\r\n            </ava-icon>\r\n          </ava-textbox>\r\n        </div>\r\n        <div class=\"new-buttons-container\">\r\n          <div class=\"sign-in-button mb-5\">\r\n            <ava-button\r\n              class=\"mb-4\"\r\n              [label]=\"labels.labels.sign_in + ' ' + labels.labels.arrow\"\r\n              variant=\"primary\"\r\n              size=\"large\"\r\n              [processing]=\"isLoading()\"\r\n              [width]=\"'100%'\"\r\n              (userClick)=\"onBasicLogin()\"\r\n              [disabled]=\"!areFieldsFilled() || isLoading()\"\r\n            >\r\n            </ava-button>\r\n          </div>\r\n\r\n          <div class=\"new-separator\">\r\n            {{ labels.labels.seperator }}\r\n          </div>\r\n          <div class=\"login-with-company mt-5\">\r\n            <ava-button\r\n              [label]=\"labels.labels.login_with_company\"\r\n              variant=\"secondary\"\r\n              size=\"large\"\r\n              [width]=\"'100%'\"\r\n              (userClick)=\"onCompanyLogin()\"\r\n            >\r\n            </ava-button>\r\n          </div>\r\n        </div>\r\n      </form>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<ava-popup\r\n  [show]=\"showErrorPopup()\"\r\n  title=\"Login Failed\"\r\n  [message]=\"popupMessage()\"\r\n  [showHeaderIcon]=\"true\"\r\n  headerIconName=\"alert-circle\"\r\n  iconColor=\"#dc3545\"\r\n  [showClose]=\"true\"\r\n  [showCancel]=\"false\"\r\n  [showConfirm]=\"true\"\r\n  [confirmButtonLabel]=\"'OK'\"\r\n  [confirmButtonVariant]=\"'primary'\"\r\n  [confirmButtonBackground]=\"'#dc3545'\"\r\n  (confirm)=\"showErrorPopup.set(false)\"\r\n  (closed)=\"showErrorPopup.set(false)\"\r\n>\r\n</ava-popup>\r\n"], "mappings": "AAAA,SAA4BA,MAAM,EAAEC,MAAM,QAAQ,eAAe;AACjE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SACEC,mBAAmB,EAGnBC,UAAU,QAEL,gBAAgB;AAIvB,OAAOC,SAAS,MAAM,cAAc;AACpC,SACEC,mBAAmB,EACnBC,aAAa,EACbC,eAAe,EACfC,cAAc,QACT,wBAAwB;AAC/B,SAASC,WAAW,QAAQ,oCAAoC;AAChE,SAASC,mBAAmB,QAAQ,8CAA8C;;;;AAsBlF,WAAaC,cAAc;EAArB,MAAOA,cAAc;IAcfC,EAAA;IACAC,MAAA;IAdVC,SAAS,GAAGf,MAAM,CAAiB,KAAK,CAAC;IACzCgB,SAAS,GAAGhB,MAAM,CAAC,KAAK,CAAC;IACzBiB,YAAY,GAAGjB,MAAM,CAAC,KAAK,CAAC;IAC5BkB,SAAS;IACTC,YAAY,GAAGnB,MAAM,CAAgB,IAAI,CAAC;IAC1CoB,cAAc,GAAGpB,MAAM,CAAC,KAAK,CAAC;IAC9BqB,YAAY,GAAGrB,MAAM,CAAC,EAAE,CAAC;IAClBsB,MAAM,GAAQjB,SAAS;IAEtBkB,WAAW,GAAGtB,MAAM,CAACS,WAAW,CAAC;IACjCc,YAAY,GAAGvB,MAAM,CAACU,mBAAmB,CAAC;IAElDc,YACUZ,EAAe,EACfC,MAAc;MADd,KAAAD,EAAE,GAAFA,EAAE;MACF,KAAAC,MAAM,GAANA,MAAM;MAEd,IAAI,CAACI,SAAS,GAAG,IAAI,CAACL,EAAE,CAACa,KAAK,CAAC;QAC7BC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACvB,UAAU,CAACwB,QAAQ,CAAC,CAAC;QACrCC,QAAQ,EAAE,CAAC,EAAE,EAAEzB,UAAU,CAACwB,QAAQ,CAAC;QACnCE,YAAY,EAAE,CAAC,KAAK;OACrB,CAAC;IACJ;IAEAC,QAAQA,CAAA;MACN,MAAMC,eAAe,GAAG,IAAI,CAACR,YAAY,CAACS,YAAY,EAAE;MACxD,IAAID,eAAe,KAAK,KAAK,IAAIA,eAAe,KAAK,OAAO,EAAE;QAC5D,IAAI,CAACjB,SAAS,CAACmB,GAAG,CAACF,eAAe,KAAK,OAAO,GAAG,MAAM,GAAG,KAAK,CAAC;MAClE,CAAC,MAAM;QACL,IAAI,CAACjB,SAAS,CAACmB,GAAG,CAAC,KAAK,CAAC;MAC3B;IACF;IAEAC,UAAUA,CAACC,IAAY;MACrB,OAAO,IAAI,CAAClB,SAAS,CAACmB,GAAG,CAACD,IAAI,CAAgB;IAChD;IAEAE,YAAYA,CAAA;MACV,IAAI,IAAI,CAACpB,SAAS,CAACqB,KAAK,EAAE;QACxB,IAAI,CAACvB,SAAS,CAACkB,GAAG,CAAC,IAAI,CAAC;QACxB,IAAI,CAACf,YAAY,CAACe,GAAG,CAAC,IAAI,CAAC;QAE3B,MAAM;UAAEP,QAAQ;UAAEE;QAAQ,CAAE,GAAG,IAAI,CAACX,SAAS,CAACsB,KAAK;QACnD,IAAI,CAACjB,WAAW,CAACkB,yBAAyB,CAACd,QAAQ,EAAEE,QAAQ,CAAC,CAACa,SAAS,CAAC;UACvEC,IAAI,EAAEA,CAAA,KAAK;YACT,IAAI,CAACnB,YAAY,CAACoB,cAAc,CAAC,OAAO,CAAC;YACzC,MAAMC,WAAW,GAAG,IAAI,CAACtB,WAAW,CAACuB,uBAAuB,EAAE;YAC9D,IAAI,CAAChC,MAAM,CAACiC,QAAQ,CAAC,CAACF,WAAW,CAAC,CAAC;UACrC,CAAC;UACDG,KAAK,EAAGA,KAAwB,IAAI;YAClC,IAAI,CAAChC,SAAS,CAACkB,GAAG,CAAC,KAAK,CAAC;YACzB,IAAIf,YAAY,GAAG,EAAE;YAErB,IAAI6B,KAAK,CAACA,KAAK,EAAE;cACf;YAAA,CACD,MACI,IAAIA,KAAK,CAACC,OAAO,EAAE;cACtB9B,YAAY,GAAG,iDAAiD;YAClE;YACA,IAAI,CAACE,YAAY,CAACa,GAAG,CAACf,YAAY,CAAC;YACnC,IAAI,CAACC,cAAc,CAACc,GAAG,CAAC,IAAI,CAAC;YAC7BgB,OAAO,CAACF,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;UACvC;SACD,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAACG,oBAAoB,EAAE;MAC7B;IACF;IAEAC,cAAcA,CAAA;MACZ,IAAI,CAACpC,SAAS,CAACkB,GAAG,CAAC,IAAI,CAAC;MACxB,IAAI,CAACf,YAAY,CAACe,GAAG,CAAC,IAAI,CAAC;MAE3B,IAAI,CAACX,WAAW,CAAC8B,QAAQ,EAAE,CAACX,SAAS,CAAC;QACpCC,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAAC3B,SAAS,CAACkB,GAAG,CAAC,KAAK,CAAC;QAC3B,CAAC;QACDc,KAAK,EAAGA,KAAK,IAAI;UACfE,OAAO,CAACF,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;UACrC,IAAI,CAAC7B,YAAY,CAACe,GAAG,CAAC,gDAAgD,CAAC;UACvE,IAAI,CAAClB,SAAS,CAACkB,GAAG,CAAC,KAAK,CAAC;QAC3B;OACD,CAAC;IACJ;IAEAoB,wBAAwBA,CAAA;MACtB,IAAI,CAACrC,YAAY,CAACiB,GAAG,CAAC,CAAC,IAAI,CAACjB,YAAY,EAAE,CAAC;IAC7C;IAEAsC,UAAUA,CAACC,SAAiB;MAC1B,IAAI,CAACtC,SAAS,CAACmB,GAAG,CAACmB,SAAS,CAAC,EAAEC,QAAQ,CAAC,EAAE,CAAC;MAC3C,IAAI,CAACvC,SAAS,CAACmB,GAAG,CAACmB,SAAS,CAAC,EAAEE,aAAa,EAAE;IAChD;IAEAC,aAAaA,CAAA;MACX,IAAI,CAACJ,UAAU,CAAC,UAAU,CAAC;IAC7B;IAEQJ,oBAAoBA,CAAA;MAC1BS,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC3C,SAAS,CAAC4C,QAAQ,CAAC,CAACC,OAAO,CAAEC,GAAG,IAAI;QACnD,MAAMC,OAAO,GAAG,IAAI,CAAC/C,SAAS,CAACmB,GAAG,CAAC2B,GAAG,CAAC;QACvCC,OAAO,EAAEP,aAAa,EAAE;MAC1B,CAAC,CAAC;IACJ;IAEAQ,aAAaA,CAACV,SAAiB;MAC7B,MAAMW,KAAK,GAAG,IAAI,CAACjD,SAAS,CAACmB,GAAG,CAACmB,SAAS,CAAC;MAC3C,IAAIW,KAAK,EAAEC,OAAO,IAAID,KAAK,EAAEE,MAAM,EAAE;QACnC,IAAIF,KAAK,CAACE,MAAM,CAAC,UAAU,CAAC,EAAE;UAC5B,OAAO,GAAGb,SAAS,CAACc,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGf,SAAS,CAACgB,KAAK,CAAC,CAAC,CAAC,cAAc;QAChF;QACA,IAAIL,KAAK,CAACE,MAAM,CAAC,OAAO,CAAC,EAAE;UACzB,OAAO,oCAAoC;QAC7C;QACA,IAAIF,KAAK,CAACE,MAAM,CAAC,WAAW,CAAC,EAAE;UAC7B,OAAO,GAAGb,SAAS,CAACc,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGf,SAAS,CAACgB,KAAK,CAAC,CAAC,CAAC,qBAAqBL,KAAK,CAACE,MAAM,CAAC,WAAW,CAAC,CAACI,cAAc,aAAa;QAC5I;MACF;MACA,OAAO,EAAE;IACX;IAEA;IACAC,eAAeA,CAAA;MACb,MAAM/C,QAAQ,GAAG,IAAI,CAACT,SAAS,CAACmB,GAAG,CAAC,UAAU,CAAC,EAAEG,KAAK;MACtD,MAAMX,QAAQ,GAAG,IAAI,CAACX,SAAS,CAACmB,GAAG,CAAC,UAAU,CAAC,EAAEG,KAAK;MACtD,OAAO,CAAC,CAACb,QAAQ,IAAI,CAAC,CAACE,QAAQ,IAAIF,QAAQ,CAACgD,IAAI,EAAE,KAAK,EAAE,IAAI9C,QAAQ,CAAC8C,IAAI,EAAE,KAAK,EAAE;IACrF;IAEAC,gBAAgBA,CAAA;MACd,IAAI,CAAC9D,MAAM,CAACiC,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;IAC5C;IAEA8B,kBAAkBA,CAAA;MAChB,IAAI,CAAC/D,MAAM,CAACiC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;IACjC;;uCAtIWnC,cAAc,EAAAkE,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;;YAAdvE,cAAc;MAAAwE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCzCzBZ,EADF,CAAAc,cAAA,aAAsC,aACb;UACrBd,EAAA,CAAAe,SAAA,aAAkD;UACpDf,EAAA,CAAAgB,YAAA,EAAM;UAIAhB,EAHN,CAAAc,cAAA,aAAqC,aACJ,aACR,YACW;UAAAd,EAAA,CAAAiB,MAAA,GAAgC;UAAAjB,EAAA,CAAAgB,YAAA,EAAK;UACnEhB,EAAA,CAAAc,cAAA,WAAuB;UAAAd,EAAA,CAAAiB,MAAA,GAA+B;UACxDjB,EADwD,CAAAgB,YAAA,EAAI,EACtD;UAYAhB,EAXN,CAAAc,cAAA,eAAqD,cACnB,uBAS7B,oBAQE;UAFCd,EAAA,CAAAkB,UAAA,mBAAAC,mDAAA;YAAA,OAASN,GAAA,CAAAhC,aAAA,EAAe;UAAA,EAAC;UAK/BmB,EAFI,CAAAgB,YAAA,EAAW,EACC,EACV;UAYFhB,EAXJ,CAAAc,cAAA,eAA0C,uBAUvC,oBAQE;UAFCd,EAAA,CAAAkB,UAAA,mBAAAE,mDAAA;YAAA,OAASP,GAAA,CAAArC,wBAAA,EAA0B;UAAA,EAAC;UAK1CwB,EAFI,CAAAgB,YAAA,EAAW,EACC,EACV;UAGFhB,EAFJ,CAAAc,cAAA,eAAmC,eACA,sBAU9B;UAFCd,EAAA,CAAAkB,UAAA,uBAAAG,yDAAA;YAAA,OAAaR,GAAA,CAAArD,YAAA,EAAc;UAAA,EAAC;UAIhCwC,EADE,CAAAgB,YAAA,EAAa,EACT;UAENhB,EAAA,CAAAc,cAAA,eAA2B;UACzBd,EAAA,CAAAiB,MAAA,IACF;UAAAjB,EAAA,CAAAgB,YAAA,EAAM;UAEJhB,EADF,CAAAc,cAAA,eAAqC,sBAOlC;UADCd,EAAA,CAAAkB,UAAA,uBAAAI,yDAAA;YAAA,OAAaT,GAAA,CAAAvC,cAAA,EAAgB;UAAA,EAAC;UAQ5C0B,EANY,CAAAgB,YAAA,EAAa,EACT,EACF,EACD,EACH,EACF,EACF;UAENhB,EAAA,CAAAc,cAAA,qBAeC;UADCd,EADA,CAAAkB,UAAA,qBAAAK,sDAAA;YAAA,OAAWV,GAAA,CAAAvE,cAAA,CAAAc,GAAA,CAAmB,KAAK,CAAC;UAAA,EAAC,oBAAAoE,qDAAA;YAAA,OAC3BX,GAAA,CAAAvE,cAAA,CAAAc,GAAA,CAAmB,KAAK,CAAC;UAAA,EAAC;UAEtC4C,EAAA,CAAAgB,YAAA,EAAY;;;UAjG0BhB,EAAA,CAAAyB,SAAA,GAAgC;UAAhCzB,EAAA,CAAA0B,iBAAA,CAAAb,GAAA,CAAArE,MAAA,CAAAA,MAAA,CAAAmF,YAAA,CAAgC;UACvC3B,EAAA,CAAAyB,SAAA,GAA+B;UAA/BzB,EAAA,CAAA0B,iBAAA,CAAAb,GAAA,CAAArE,MAAA,CAAAA,MAAA,CAAAoF,WAAA,CAA+B;UAElD5B,EAAA,CAAAyB,SAAA,EAAuB;UAAvBzB,EAAA,CAAA6B,UAAA,cAAAhB,GAAA,CAAAzE,SAAA,CAAuB;UAGvB4D,EAAA,CAAAyB,SAAA,GAAgC;UAMhCzB,EANA,CAAA6B,UAAA,UAAAhB,GAAA,CAAArE,MAAA,CAAAA,MAAA,CAAAK,QAAA,CAAgC,gBAAAgE,GAAA,CAAArE,MAAA,CAAAsF,YAAA,CAAAjF,QAAA,CACY,kBAC3B,UAAAgE,GAAA,CAAAzB,aAAA,aAEkB,wBACZ,yBACC;UAKtBY,EAAA,CAAAyB,SAAA,EAAe;UAGfzB,EAHA,CAAA6B,UAAA,gBAAe,gBACA,mBAEG;UAOpB7B,EAAA,CAAAyB,SAAA,GAAgC;UAOhCzB,EAPA,CAAA6B,UAAA,UAAAhB,GAAA,CAAArE,MAAA,CAAAA,MAAA,CAAAO,QAAA,CAAgC,SAAA8D,GAAA,CAAA1E,YAAA,yBACa,gBAAA0E,GAAA,CAAArE,MAAA,CAAAsF,YAAA,CAAA/E,QAAA,CACD,kBAC3B,UAAA8D,GAAA,CAAAzB,aAAA,aAEkB,wBACZ,yBACC;UAItBY,EAAA,CAAAyB,SAAA,EAA+C;UAI/CzB,EAJA,CAAA6B,UAAA,aAAAhB,GAAA,CAAA1E,YAAA,uBAA+C,gBAChC,gBACA,mBAEG;UASlB6D,EAAA,CAAAyB,SAAA,GAA2D;UAM3DzB,EANA,CAAA6B,UAAA,UAAAhB,GAAA,CAAArE,MAAA,CAAAA,MAAA,CAAAuF,OAAA,SAAAlB,GAAA,CAAArE,MAAA,CAAAA,MAAA,CAAAwF,KAAA,CAA2D,eAAAnB,GAAA,CAAA3E,SAAA,GAGjC,iBACV,cAAA2E,GAAA,CAAAjB,eAAA,MAAAiB,GAAA,CAAA3E,SAAA,GAE8B;UAMhD8D,EAAA,CAAAyB,SAAA,GACF;UADEzB,EAAA,CAAAiC,kBAAA,MAAApB,GAAA,CAAArE,MAAA,CAAAA,MAAA,CAAA0F,SAAA,MACF;UAGIlC,EAAA,CAAAyB,SAAA,GAA0C;UAG1CzB,EAHA,CAAA6B,UAAA,UAAAhB,GAAA,CAAArE,MAAA,CAAAA,MAAA,CAAA2F,kBAAA,CAA0C,iBAG1B;UAY5BnC,EAAA,CAAAyB,SAAA,EAAyB;UAWzBzB,EAXA,CAAA6B,UAAA,SAAAhB,GAAA,CAAAvE,cAAA,GAAyB,YAAAuE,GAAA,CAAAtE,YAAA,GAEC,wBACH,mBAGL,qBACE,qBACA,4BACO,mCACO,sCACG;;;qBDpEnCnB,YAAY,EACZC,mBAAmB,EAAA6E,EAAA,CAAAkC,aAAA,EAAAlC,EAAA,CAAAmC,eAAA,EAAAnC,EAAA,CAAAoC,oBAAA,EAAApC,EAAA,CAAAqC,iBAAA,EAAArC,EAAA,CAAAsC,kBAAA,EAAAtC,EAAA,CAAAuC,eAAA,EACnBjH,mBAAmB,EACnBC,aAAa,EACbC,eAAe,EACfC,cAAc;MAAA+G,MAAA;IAAA;;SAKL5G,cAAc;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}