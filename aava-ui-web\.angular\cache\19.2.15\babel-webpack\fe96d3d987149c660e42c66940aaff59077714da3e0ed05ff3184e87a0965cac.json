{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { PaginationComponent } from '../pagination/pagination.component';\nimport * as i0 from \"@angular/core\";\nexport let PageFooterComponent = /*#__PURE__*/(() => {\n  class PageFooterComponent {\n    totalItems = 0;\n    currentPage = 1;\n    itemsPerPage = 12;\n    visiblePageCount = 5;\n    pageChange = new EventEmitter();\n    onPageChange(page) {\n      this.pageChange.emit(page);\n    }\n    static ɵfac = function PageFooterComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PageFooterComponent)();\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PageFooterComponent,\n      selectors: [[\"app-page-footer\"]],\n      inputs: {\n        totalItems: \"totalItems\",\n        currentPage: \"currentPage\",\n        itemsPerPage: \"itemsPerPage\",\n        visiblePageCount: \"visiblePageCount\"\n      },\n      outputs: {\n        pageChange: \"pageChange\"\n      },\n      decls: 3,\n      vars: 4,\n      consts: [[1, \"page-footer\"], [1, \"footer-content\"], [3, \"pageChange\", \"totalItems\", \"currentPage\", \"itemsPerPage\", \"visiblePageCount\"]],\n      template: function PageFooterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"app-pagination\", 2);\n          i0.ɵɵlistener(\"pageChange\", function PageFooterComponent_Template_app_pagination_pageChange_2_listener($event) {\n            return ctx.onPageChange($event);\n          });\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"totalItems\", ctx.totalItems)(\"currentPage\", ctx.currentPage)(\"itemsPerPage\", ctx.itemsPerPage)(\"visiblePageCount\", ctx.visiblePageCount);\n        }\n      },\n      dependencies: [CommonModule, PaginationComponent],\n      styles: [\".page-footer[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 12px 0;\\n  margin-top: 20px;\\n  \\n\\n  border-radius: 0;\\n  position: relative;\\n  z-index: 200;\\n}\\n\\n.footer-content[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 1400px;\\n  margin: 0 auto;\\n  padding: 0 16px;\\n  display: flex;\\n  justify-content: center;\\n}\\n\\n@media (max-width: 768px) {\\n  .page-footer[_ngcontent-%COMP%] {\\n    padding: 8px 0;\\n  }\\n  .footer-content[_ngcontent-%COMP%] {\\n    padding: 0 12px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3Byb2plY3RzL2NvbnNvbGUvc3JjL2FwcC9zaGFyZWQvY29tcG9uZW50cy9wYWdlLWZvb3Rlci9wYWdlLWZvb3Rlci5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLFdBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFRQSwwQ0FBQTtFQUNBLGdCQUFBO0VBQ0Esa0JBQUE7RUFDQSxZQUFBO0FBTkY7O0FBU0E7RUFDRSxXQUFBO0VBQ0EsaUJBQUE7RUFDQSxjQUFBO0VBQ0EsZUFBQTtFQUNBLGFBQUE7RUFDQSx1QkFBQTtBQU5GOztBQVNBO0VBQ0U7SUFDRSxjQUFBO0VBTkY7RUFTQTtJQUNFLGVBQUE7RUFQRjtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiLnBhZ2UtZm9vdGVyIHtcclxuICB3aWR0aDogMTAwJTtcclxuICBwYWRkaW5nOiAxMnB4IDA7XHJcbiAgbWFyZ2luLXRvcDogMjBweDtcclxuICAvLyBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTAyLjE0ZGVnLCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOCkgMS4wNyUsIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC45KSA5OC4wMSUpO1xyXG4gIC8vIGJhY2tkcm9wLWZpbHRlcjogYmx1cig4cHgpO1xyXG4gIC8vIC13ZWJraXQtYmFja2Ryb3AtZmlsdGVyOiBibHVyKDhweCk7XHJcbiAgLy8gdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcclxuICAvLyBib3JkZXItdG9wOiAxcHggc29saWQgcmdiYSgyMzAsIDIzMCwgMjMwLCAwLjUpO1xyXG4gIC8vIGJveC1zaGFkb3c6IDAgLTRweCAxMHB4IHJnYmEoMCwgMCwgMCwgMC4wMyk7XHJcbiAgXHJcbiAgLyogRml4IGZvciBib3JkZXItcmFkaXVzIGNhdXNpbmcgY3V0LW9mZiAqL1xyXG4gIGJvcmRlci1yYWRpdXM6IDA7XHJcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gIHotaW5kZXg6IDIwMDtcclxufVxyXG5cclxuLmZvb3Rlci1jb250ZW50IHtcclxuICB3aWR0aDogMTAwJTtcclxuICBtYXgtd2lkdGg6IDE0MDBweDtcclxuICBtYXJnaW46IDAgYXV0bztcclxuICBwYWRkaW5nOiAwIDE2cHg7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxufVxyXG5cclxuQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XHJcbiAgLnBhZ2UtZm9vdGVyIHtcclxuICAgIHBhZGRpbmc6IDhweCAwO1xyXG4gIH1cclxuICBcclxuICAuZm9vdGVyLWNvbnRlbnQge1xyXG4gICAgcGFkZGluZzogMCAxMnB4O1xyXG4gIH1cclxufSAiXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n  return PageFooterComponent;\n})();", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "PaginationComponent", "PageFooterComponent", "totalItems", "currentPage", "itemsPerPage", "visiblePageCount", "pageChange", "onPageChange", "page", "emit", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "PageFooterComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵlistener", "PageFooterComponent_Template_app_pagination_pageChange_2_listener", "$event", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "styles"], "sources": ["C:\\console\\aava-ui-web\\projects\\console\\src\\app\\shared\\components\\page-footer\\page-footer.component.ts", "C:\\console\\aava-ui-web\\projects\\console\\src\\app\\shared\\components\\page-footer\\page-footer.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { PaginationComponent } from '../pagination/pagination.component';\r\n\r\n@Component({\r\n  selector: 'app-page-footer',\r\n  standalone: true,\r\n  imports: [CommonModule, PaginationComponent],\r\n  templateUrl: './page-footer.component.html',\r\n  styleUrls: ['./page-footer.component.scss']\r\n})\r\nexport class PageFooterComponent {\r\n  @Input() totalItems: number = 0;\r\n  @Input() currentPage: number = 1;\r\n  @Input() itemsPerPage: number = 12;\r\n  @Input() visiblePageCount: number = 5;\r\n  \r\n  @Output() pageChange = new EventEmitter<number>();\r\n  \r\n  onPageChange(page: number): void {\r\n    this.pageChange.emit(page);\r\n  }\r\n} ", "<div class=\"page-footer\">\r\n  <div class=\"footer-content\">\r\n    <app-pagination\r\n      [totalItems]=\"totalItems\"\r\n      [currentPage]=\"currentPage\"\r\n      [itemsPerPage]=\"itemsPerPage\"\r\n      [visiblePageCount]=\"visiblePageCount\"\r\n      (pageChange)=\"onPageChange($event)\"\r\n    ></app-pagination>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAAA,SAAoBA,YAAY,QAAuB,eAAe;AACtE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,oCAAoC;;AASxE,WAAaC,mBAAmB;EAA1B,MAAOA,mBAAmB;IACrBC,UAAU,GAAW,CAAC;IACtBC,WAAW,GAAW,CAAC;IACvBC,YAAY,GAAW,EAAE;IACzBC,gBAAgB,GAAW,CAAC;IAE3BC,UAAU,GAAG,IAAIR,YAAY,EAAU;IAEjDS,YAAYA,CAACC,IAAY;MACvB,IAAI,CAACF,UAAU,CAACG,IAAI,CAACD,IAAI,CAAC;IAC5B;;uCAVWP,mBAAmB;IAAA;;YAAnBA,mBAAmB;MAAAS,SAAA;MAAAC,MAAA;QAAAT,UAAA;QAAAC,WAAA;QAAAC,YAAA;QAAAC,gBAAA;MAAA;MAAAO,OAAA;QAAAN,UAAA;MAAA;MAAAO,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCT5BE,EAFJ,CAAAC,cAAA,aAAyB,aACK,wBAOzB;UADCD,EAAA,CAAAE,UAAA,wBAAAC,kEAAAC,MAAA;YAAA,OAAcL,GAAA,CAAAZ,YAAA,CAAAiB,MAAA,CAAoB;UAAA,EAAC;UAGzCJ,EAFK,CAAAK,YAAA,EAAiB,EACd,EACF;;;UAPAL,EAAA,CAAAM,SAAA,GAAyB;UAGzBN,EAHA,CAAAO,UAAA,eAAAR,GAAA,CAAAjB,UAAA,CAAyB,gBAAAiB,GAAA,CAAAhB,WAAA,CACE,iBAAAgB,GAAA,CAAAf,YAAA,CACE,qBAAAe,GAAA,CAAAd,gBAAA,CACQ;;;qBDC/BN,YAAY,EAAEC,mBAAmB;MAAA4B,MAAA;IAAA;;SAIhC3B,mBAAmB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}